package com.tzx.framework.common.file;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang.ArrayUtils;

import net.sf.json.JSONObject;

public class MqFileDownloadTest {
	public static void main(String[] args) {
//		String str = download("http://localhost:8080/examples/log.txt");
		//http://test.e7e6.net:8989/group1/M00/00/01/ChIJBVj19O-AaMkmAE8vmKE7ri8302.avi
//		String str = download("http://test.e7e6.net:8989/group1/M00/00/01/ChIJBVkBaR6AH3zRAAAJs-Cdr8o4683.mq",true);
//		System.out.println(str);
		
//		byte[] b1 = "hello,".getBytes();
//		byte[] b2 = "world!".getBytes(); 
//		byte[] b3 = "sjltest".getBytes();
//		List<byte[]> list = new ArrayList<>();
//		list.add(b1);
//		list.add(b2);
//		list.add(b3);
//		
//		int len = 0;
//		for(byte[] b : list){
//			len += b.length;
//		}
//		byte[] b4 = new byte[0];
//		for(byte[] b : list){
//			b4 = ArrayUtils.addAll(b4,b);
//		}
//		
//		System.out.println(new String(b4));
		
		try{
			Date start = new Date();
			String str = MqFileDownload.downloadMqStrByBlock("http://www.e7e6.mobi/group1/M00/01/82/CggVAlsEz-aAZ1o6AFfXBKfE3Ks9869.mq",0,true);
			JSONObject getJson = JSONObject.fromObject(str);
			System.out.println(str.length());	
			System.out.println("总共耗时:"+(new Date().getTime() - start.getTime())+" ms");
		}catch(Exception e){
			System.out.println(e.getMessage());
		}		
		
//		int fileSize = 2483;
//		byte[] bs = downloadBytesBlock("http://test.e7e6.net:8989/group1/M00/00/01/ChIJBVkBaR6AH3zRAAAJs-Cdr8o4683.mq", fileSize);
////		int fileSize = 3404;
////		byte[] bs = downloadBytesBlock("http://localhost:8080/examples/log.txt", fileSize);
////		DataOutputStream dos = new DataOutputStream(new FileOutputStream(new File("d:\\1.txt")));
//		DataOutputStream dos = new DataOutputStream(new FileOutputStream(new File("d:\\1.mq")));
//		dos.write(bs);
//		
//		dos.flush();
//		dos.close();
	}
}
