package com.tzx.pos.bo.imp;

import net.sf.json.JSONObject;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.tzx.base.bo.PosCodeService;
import com.tzx.framework.common.entity.Data;
import com.tzx.pos.bo.PosPrintNewService;
import com.tzx.pos.bo.PosPrintService;
import com.tzx.pos.bo.TakeOrderingDishService;
import com.tzx.pos.bo.TakeOrderingOpenTableService;
import com.tzx.pos.bo.TakeOrderingPaymentService;
import com.tzx.pos.bo.TakeOrderingService;
import com.tzx.pos.bo.dto.TakeOrderingParam;
import common.Logger;

/**
 * 接到入口类  (D5厨房订单核销(接单)接口 )
 * <AUTHOR>
 *
 */
@Service(TakeOrderingService.NAME)
public class TakeOrderingServiceImp extends PosBaseServiceImp implements TakeOrderingService {
	
	@Autowired 
	PosCodeService codeService;
	@Autowired
	private TakeOrderingOpenTableService takeOrderingOpenTableService;
	@Autowired
	private TakeOrderingDishService takeOrderingDishService;
	@Autowired
	private TakeOrderingPaymentService takeOrderingPaymentService;
	@Autowired
	private PosPrintService posPrintservice;
	
	@Autowired
	private PosPrintNewService posPrintNewService;
	private static final Logger logger = Logger.getLogger(TakeOrderingServiceImp.class);

	/**
	 * 接单入口
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void takeOrderingEntry(Data param, Data result, JSONObject json) throws Exception {
		
		logger.info(Thread.currentThread().getId() + "@-----------订单转账单流程开始--------");
		TakeOrderingParam params = new TakeOrderingParam();
		//调用开台
		params= takeOrderingOpenTableService.insertTakeOpenTable(param, result, json);
		//下单
		params=takeOrderingDishService.insertOrderDish(params, result);
		//组织打印json
		json.put("print_json", params.getPrintJson()); 
	    //支付
		takeOrderingPaymentService.posBillPayment(params,result);
		logger.info(Thread.currentThread().getId() + "@-----------订单转整单所以流程全部结束-------->>>>>>>>>>>>>>>>>>>>>>>>");
	}
}
