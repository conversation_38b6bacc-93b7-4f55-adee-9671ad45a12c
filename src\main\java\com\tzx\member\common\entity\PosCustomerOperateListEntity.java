package com.tzx.member.common.entity;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

import com.tzx.pos.base.constant.SysDictionary;

public class PosCustomerOperateListEntity implements Serializable
{

	/**
	 * 
	 */
	private static final long	serialVersionUID	= -5846262295615662112L;

	private String				tenancy_id;
	private Integer				store_id;
	private Integer				id;
	private Date				business_date;
	private Integer				operator_id;
	private String				pos_num;
	private Integer				shift_id;
	private Timestamp			operate_time;
	private String				chanel;
	private String				service_type;
	private String				operat_type;
	private Integer				customer_id;
	private String				customer_code;
	private String				mobil;
	private String				customer_name;
	private Integer				card_class_id;
	private Integer				card_id;
	private String				card_code;
	private Integer				incorporation_id;
	private String				incorporation_name;
	private String				third_bill_code;
	private String				third_bill_code_timestamp;
	private String				batch_num;
	private String				bill_code_original;
	private String				bill_code;
	private Double				trade_amount;
	private Double				trade_credit;
	private Double				deposit;
	private Double				sales_price;
	private String				sales_person;
	private Integer				payment_id;
	private String				payment_class;
	private Integer				coupons_type_id;
	private String				coupons_code;
	private String				is_invoice;
	private Timestamp			finish_time;
	private String				payment_state;
	private String				operate_state;
	private String				cancel_state		= SysDictionary.CUSTOMER_OPERATE_CANCEL_STATE_N;
	private String				request_status;
	private String				request_code;
	private String				request_msg;
	private Timestamp			last_query_time;
	private Integer				query_count;
	private String				action_type;
	private String				third_code;
	private String				extend_param;

	public String getTenancy_id()
	{
		return tenancy_id;
	}

	public void setTenancy_id(String tenancy_id)
	{
		this.tenancy_id = tenancy_id;
	}

	public Integer getStore_id()
	{
		return store_id;
	}

	public void setStore_id(Integer store_id)
	{
		this.store_id = store_id;
	}

	public Integer getId()
	{
		return id;
	}

	public void setId(Integer id)
	{
		this.id = id;
	}

	public Date getBusiness_date()
	{
		return business_date;
	}

	public void setBusiness_date(Date business_date)
	{
		this.business_date = business_date;
	}

	public Integer getOperator_id()
	{
		return operator_id;
	}

	public void setOperator_id(Integer operator_id)
	{
		this.operator_id = operator_id;
	}

	public String getPos_num()
	{
		return pos_num;
	}

	public void setPos_num(String pos_num)
	{
		this.pos_num = pos_num;
	}

	public Integer getShift_id()
	{
		return shift_id;
	}

	public void setShift_id(Integer shift_id)
	{
		this.shift_id = shift_id;
	}

	public Timestamp getOperate_time()
	{
		return operate_time;
	}

	public void setOperate_time(Timestamp operate_time)
	{
		this.operate_time = operate_time;
	}

	public String getChanel()
	{
		return chanel;
	}

	public void setChanel(String chanel)
	{
		this.chanel = chanel;
	}

	public String getService_type()
	{
		return service_type;
	}

	public void setService_type(String service_type)
	{
		this.service_type = service_type;
	}

	public String getOperat_type()
	{
		return operat_type;
	}

	public void setOperat_type(String operat_type)
	{
		this.operat_type = operat_type;
	}

	public Integer getCustomer_id()
	{
		return customer_id;
	}

	public void setCustomer_id(Integer customer_id)
	{
		this.customer_id = customer_id;
	}

	public String getCustomer_code()
	{
		return customer_code;
	}

	public void setCustomer_code(String customer_code)
	{
		this.customer_code = customer_code;
	}

	public String getMobil()
	{
		return mobil;
	}

	public void setMobil(String mobil)
	{
		this.mobil = mobil;
	}

	public String getCustomer_name()
	{
		return customer_name;
	}

	public void setCustomer_name(String customer_name)
	{
		this.customer_name = customer_name;
	}

	public Integer getCard_class_id()
	{
		return card_class_id;
	}

	public void setCard_class_id(Integer card_class_id)
	{
		this.card_class_id = card_class_id;
	}

	public Integer getCard_id()
	{
		return card_id;
	}

	public void setCard_id(Integer card_id)
	{
		this.card_id = card_id;
	}

	public String getCard_code()
	{
		return card_code;
	}

	public void setCard_code(String card_code)
	{
		this.card_code = card_code;
	}

	public Integer getIncorporation_id()
	{
		return incorporation_id;
	}

	public void setIncorporation_id(Integer incorporation_id)
	{
		this.incorporation_id = incorporation_id;
	}

	public String getIncorporation_name()
	{
		return incorporation_name;
	}

	public void setIncorporation_name(String incorporation_name)
	{
		this.incorporation_name = incorporation_name;
	}

	public String getThird_bill_code()
	{
		return third_bill_code;
	}

	public void setThird_bill_code(String third_bill_code)
	{
		this.third_bill_code = third_bill_code;
	}

	public String getThird_bill_code_timestamp()
	{
		return third_bill_code_timestamp;
	}

	public void setThird_bill_code_timestamp(String third_bill_code_timestamp)
	{
		this.third_bill_code_timestamp = third_bill_code_timestamp;
	}

	public String getBatch_num()
	{
		return batch_num;
	}

	public void setBatch_num(String batch_num)
	{
		this.batch_num = batch_num;
	}

	public String getBill_code_original()
	{
		return bill_code_original;
	}

	public void setBill_code_original(String bill_code_original)
	{
		this.bill_code_original = bill_code_original;
	}

	public String getBill_code()
	{
		return bill_code;
	}

	public void setBill_code(String bill_code)
	{
		this.bill_code = bill_code;
	}

	public Double getTrade_amount()
	{
		return trade_amount;
	}

	public void setTrade_amount(Double trade_amount)
	{
		this.trade_amount = trade_amount;
	}

	public Double getTrade_credit()
	{
		return trade_credit;
	}

	public void setTrade_credit(Double trade_credit)
	{
		this.trade_credit = trade_credit;
	}

	public Double getDeposit()
	{
		return deposit;
	}

	public void setDeposit(Double deposit)
	{
		this.deposit = deposit;
	}

	public Double getSales_price()
	{
		return sales_price;
	}

	public void setSales_price(Double sales_price)
	{
		this.sales_price = sales_price;
	}

	public String getSales_person()
	{
		return sales_person;
	}

	public void setSales_person(String sales_person)
	{
		this.sales_person = sales_person;
	}

	public Integer getPayment_id()
	{
		return payment_id;
	}

	public void setPayment_id(Integer payment_id)
	{
		this.payment_id = payment_id;
	}

	public String getPayment_class()
	{
		return payment_class;
	}

	public void setPayment_class(String payment_class)
	{
		this.payment_class = payment_class;
	}

	public Integer getCoupons_type_id()
	{
		return coupons_type_id;
	}

	public void setCoupons_type_id(Integer coupons_type_id)
	{
		this.coupons_type_id = coupons_type_id;
	}

	public String getCoupons_code()
	{
		return coupons_code;
	}

	public void setCoupons_code(String coupons_code)
	{
		this.coupons_code = coupons_code;
	}

	public String getIs_invoice()
	{
		return is_invoice;
	}

	public void setIs_invoice(String is_invoice)
	{
		this.is_invoice = is_invoice;
	}

	public Timestamp getFinish_time()
	{
		return finish_time;
	}

	public void setFinish_time(Timestamp finish_time)
	{
		this.finish_time = finish_time;
	}

	public String getPayment_state()
	{
		return payment_state;
	}

	public void setPayment_state(String payment_state)
	{
		this.payment_state = payment_state;
	}

	public String getOperate_state()
	{
		return operate_state;
	}

	public void setOperate_state(String operate_state)
	{
		this.operate_state = operate_state;
	}

	public String getCancel_state()
	{
		return cancel_state;
	}

	public void setCancel_state(String cancel_state)
	{
		this.cancel_state = cancel_state;
	}

	public String getRequest_status()
	{
		return request_status;
	}

	public void setRequest_status(String request_status)
	{
		this.request_status = request_status;
	}

	public String getRequest_code()
	{
		return request_code;
	}

	public void setRequest_code(String request_code)
	{
		this.request_code = request_code;
	}

	public String getRequest_msg()
	{
		return request_msg;
	}

	public void setRequest_msg(String request_msg)
	{
		this.request_msg = request_msg;
	}

	public Timestamp getLast_query_time()
	{
		return last_query_time;
	}

	public void setLast_query_time(Timestamp last_query_time)
	{
		this.last_query_time = last_query_time;
	}

	public Integer getQuery_count()
	{
		return query_count;
	}

	public void setQuery_count(Integer query_count)
	{
		this.query_count = query_count;
	}

	public String getAction_type()
	{
		return action_type;
	}

	public void setAction_type(String action_type)
	{
		this.action_type = action_type;
	}

	public String getThird_code()
	{
		return third_code;
	}

	public void setThird_code(String third_code)
	{
		this.third_code = third_code;
	}

	public String getExtend_param()
	{
		return extend_param;
	}

	public void setExtend_param(String extend_param)
	{
		this.extend_param = extend_param;
	}

}
