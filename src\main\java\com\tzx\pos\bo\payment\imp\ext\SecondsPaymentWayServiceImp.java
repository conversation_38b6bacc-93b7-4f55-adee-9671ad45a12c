package com.tzx.pos.bo.payment.imp.ext;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Service;

import com.tzx.base.common.util.PosPropertyUtil;
import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.util.HttpUtil;
import com.tzx.framework.common.util.Tools;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.JsonUtil;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.bo.payment.PaymentWayService;
import com.tzx.pos.bo.payment.imp.PaymentWayServiceImp;

/**
 * Created by Administrator on 2017-04-17.
 * 秒付
 */
@Service("com.tzx.pos.bo.payment.imp.ext.SecondsPaymentWayServiceImp")
public class SecondsPaymentWayServiceImp extends PaymentWayServiceImp implements PaymentWayService{
//    private  final  String url = "/tzxsaas/payment/news/post";
    private  final  String url = "/payment/news/post";

//    @Resource(name = PosDishDao.NAME)
//    private PosDishDao			posDishDao;
    
//    @Resource(name = PosSelfDao.NAME)
//    private PosSelfDao			posSelfDao;
    
    @Override
    public void payment(String tenantId, int storeId, String billNum, String batchNum, Date reportDate, int shiftId, String posNum, String optNum, String tableCode, JSONObject payment, Timestamp currentTime, String isOnlinePay) throws Exception {
        logger.info("进入付款方法，传入的数据为"+payment.toString());
        double amount = payment.optDouble("amount");
        Double  count= ParamUtil.getDoubleValueByObject(payment,"count",false,null);
        double currencyAmount = payment.optDouble("currency_amount");
        String number = payment.optString("number");
        String phone = payment.optString("mobile");
        String billCode = payment.optString("bill_code");
        int customerId = payment.optInt("customer_id");

        int jzid = payment.optInt("jzid");
        String paymentClass = payment.optString("payment_class");
        String paymentName = payment.optString("payment_name");
        String nameEnglish = payment.optString("payment_english_name");
        double rate = payment.optDouble("rate");
        String remark = payment.optString("remark");
        String isYsk = payment.optString("is_ysk");
        if (Tools.isNullOrEmpty(isYsk))
        {
            isYsk = "N";
        }
        if(Double.isNaN(count)||count<1)
        {
            count = 1d;
        }
        paymentDao.insertPosBillPayment(tenantId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, nameEnglish, rate, amount, count, currencyAmount, number, phone, isYsk, customerId, billCode, remark, currentTime,
                SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);

//        paymentDao.insertPosBillPaymentLog(tenantId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, nameEnglish, rate, amount, count, currencyAmount, number, phone, isYsk, customerId, billCode, remark, currentTime,
//                SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);
        logger.info("付款完成，准备关闭账单");
//        // 关闭账单
//        posDishDao.closeBill(tenantId, storeId, billNum);
//        logger.info("付款完成，准备更新桌位状态tenantId:"+tenantId+"storeId:"+storeId+"tableCode:"+tableCode);
//        // 更新桌位状态表
//        posDishDao.updateTableState(tenantId, storeId, tableCode);
//        logger.info("更新桌位状态完成，开始写日 志");
//         posSelfDao.billPrint("0", tenantId, storeId, billNum, posNum, optNum, reportDate, shiftId, "1002", "0", "", "", "0", "", "","");
//        logger.info("写日志传入参数posNum："+posNum+">>optNum:"+optNum+">>shiftId"+shiftId+">>reportDate"+reportDate+"com.tzx.pos.base.Constant.TITLE>>"+com.tzx.pos.base.Constant.TITLE+">>tableCode"+tableCode+">>billNum>>"+billNum);
//        posSelfDao.savePosLog(tenantId, storeId, posNum, optNum, optNum, shiftId, reportDate, com.tzx.pos.base.Constant.TITLE, "关闭桌位", "桌位编号:" + tableCode, "账单编号:" + billNum);
//        logger.info("操作完成");
    }

    @Override
    public void paymentDebit(String tenancyId, int storeId, String billNum, String orderNum, String chanel, Double paymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData) throws Exception {

    }

    @Override
    public void findPaymentDebit(String tenancyId, int storeId, String billNum, String orderNum, String chanel, Double paymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData) throws Exception {

    }

    @Override
    public void paymentRefund(String tenancyId, int storeId, String billNum, String oldBillNum, String orderNum, String chanel, Double billPaymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData,String operType) throws Exception {
       logger.info("进入退单方法，传入的数据为"+payment.toString());
//        String bill_num = payment.optString("bill_num");
        String payOrderId = payment.optString("bill_code");
//        String serviceType = payment.optString("serviceType");
        String pos_num = payment.optString("pos_num");
        String opt_num = payment.optString("cashier_num");
        Double amount = payment.optDouble("amount");
        String payType = payment.optString("payment_class");
        String currency = payment.optString("currency_amount");
        String reportDate = payment.optString("report_Date");
        String shift = payment.optString("shift_id");
//        String channel = payment.optString("channel");
        String season = getReason(billNum,storeId);
        
        JSONObject obj = new JSONObject();
        obj.put("order_no",oldBillNum);
        obj.put("payOrderId","".equals(payOrderId)?orderNum:payOrderId);
        obj.put("pos_num",pos_num);
        obj.put("season",season);
        obj.put("service_type",SysDictionary.SERVICE_TYPE_CONSUME);
        obj.put("opt_num",opt_num);
        obj.put("amount",amount<1?billPaymentAmount:amount);
        obj.put("pay_type","".equals(payType)?SysDictionary.PAYMENT_CLASS_SECONDS_PAY:payType);
        obj.put("currency",currency);
        obj.put("report_date","".equals(reportDate)? currentTime.toString():reportDate);
        obj.put("shift",shift);
        obj.put("channel",chanel);
        
        List<JSONObject> datas = new ArrayList<JSONObject>();
        datas.add(obj);
        Data reqData = Data.get(tenancyId,Integer.valueOf(storeId),0);
        reqData.setType(Type.REFUND_PAY_ORDER);
        reqData.setOper(Oper.delete);
        reqData.setSecret("0");
        reqData.setData(datas);
        reqData.setStore_id(storeId);
        reqData.setTenancy_id(tenancyId);
        
//        List<JSONObject> dataList = new ArrayList<JSONObject>();
//        logger.info("秒付退款请求参数："+JSONObject.fromObject(reqData).toString());
//        String  QUICK_PASS_URL = PosPropertyUtil.getMsg(com.tzx.pos.base.Constant.REFUND_URL);
//        String response = HttpClientPostUtil.sendPostRequest(QUICK_PASS_URL+url,JSONObject.fromObject(reqData).toString());
//        logger.info("秒付退款返回结果:"+response);
//
//        JSONObject resObj = JSONObject.fromObject(response);
//        JSONObject datajson = JSONObject.fromObject(JSONArray.fromObject(resObj.get("data")).get(0));
//        String paymentState = datajson.optString("payment_state");
//        //修改退款状态
//        String sql = "update pos_bill_payment set payment_state = ? where bill_num =?";
//        paymentDao.update(sql,new  Object[]{paymentState,bill_num});
//
//        resultJson.put("code",resObj.optString("code"));
//        resultJson.put("msg",resObj.optString("msg"));
//        resultJson.put("payment_state",paymentState);
//        if (datajson.containsKey("failure_msg")){
//            resultJson.put("failure_msg",datajson.optString("failure_msg"));
//        }
//        if (datajson.containsKey("failure_code")){
//            resultJson.put("failure_code",datajson.optString("failure_code"));
//        }
//
//        JSONObject Json = JSONObject.fromObject(response);
//        dataList.add(Json);
//        resultData.setData(dataList);
//        resultData.setTenancy_id(tenancyId);
//        resultData.setStore_id(storeId);
//        resultData.setCode(Integer.valueOf(resObj.optString("code")));
//        resultData.setMsg(resObj.optString("msg"));
        
        this.commonPost(reqData, resultData);
//        payment_state:3//"网络请求错误"
//        payment_state:1//付款支付
//        payment_state:0//付款失败
//        payment_state:4//已退款
//        payment_state:7//退款失败
        
		if (Constant.CODE_SUCCESS == resultData.getCode())
		{
			JSONObject paymentJson = new JSONObject();
			paymentJson.put("number", payment.optString("number"));
			paymentJson.put("bill_code", payment.optString("bill_code"));
			paymentJson.put("payment_state", SysDictionary.PAYMENT_STATE_REFUND_COMPLETE);
			paymentJson.put("id", payment.optInt("id"));
			paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", paymentJson);
		}
		else if (Constant.CODE_CONN_EXCEPTION == resultData.getCode())
		{
			resultData.setCode(Constant.CODE_CONN_EXCEPTION);
			resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
		}
		else
		{
			JSONObject paymentJson = new JSONObject();
			paymentJson.put("payment_state", SysDictionary.PAYMENT_STATE_REFUND_FAILURE);
			paymentJson.put("id", payment.optInt("id"));
			paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", paymentJson);
		}
    }

    /**
     * 获取退款原因Id
     * @return
     */
    public String getReason(String billNum,Integer sorteId) throws Exception {
        logger.info("查询退单原因>>billNum:"+billNum+">>sorteId:"+sorteId);
        String sql="select b.reason_name from pos_bill_item a ,hq_unusual_reason b where a.returngive_reason_id=b.id and a.bill_num=? and a.store_id=?";
        List <JSONObject>list=paymentDao.queryString4Json(null,sql,new  Object[]{billNum,sorteId});
        String reason_name="";
        logger.info("查询退单原因结果 >>list:"+list.toString());
        if(null!=list&&list.size()>0){
            JSONObject jsonObject= list.get(0);
            reason_name= jsonObject.getString("reason_name");
        }
        return reason_name;
    }

	@Override
	public void paymentCancel(String tenancyId, int storeId, String billNum, String orderNum, String chanel, Double billPaymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData, String operType) throws Exception
	{
	       logger.info("进入退单方法，传入的数据为"+payment.toString());
	        String bill_num = payment.optString("bill_num");
	        String payOrderId = payment.optString("bill_code");
	        String serviceType = payment.optString("serviceType");
	        String pos_num = payment.optString("pos_num");
	        String opt_num = payment.optString("cashier_num");
	        Double amount = payment.optDouble("amount");
	        String payType = payment.optString("payment_class");
	        String currency = payment.optString("currency_amount");
	        String reportDate = payment.optString("report_Date");
	        String shift = payment.optString("shift_id");
//	        String channel = payment.optString("channel");
	        String season = getReason(billNum,storeId);
	        JSONObject obj = new JSONObject();
	        obj.put("order_no",bill_num);
	        obj.put("payOrderId","".equals(payOrderId)?orderNum:payOrderId);
	        obj.put("pos_num",pos_num);
	        obj.put("season",season);
	        obj.put("service_type","".equals(serviceType)?SysDictionary.SERVICE_TYPE_CONSUME:serviceType);
	        obj.put("opt_num",opt_num);
	        obj.put("amount",amount<1?billPaymentAmount:amount);
	        obj.put("pay_type","".equals(payType)?SysDictionary.PAYMENT_CLASS_SECONDS_PAY:payType);
	        obj.put("currency",currency);
	        obj.put("report_date","".equals(reportDate)? currentTime.toString():reportDate);
	        obj.put("shift",shift);
	        obj.put("channel",chanel);
	        List<JSONObject> datas = new ArrayList<JSONObject>();
	        datas.add(obj);
	        Data reqData = Data.get(tenancyId,Integer.valueOf(storeId),0);
	        reqData.setType(Type.REFUND_PAY_ORDER);
	        reqData.setOper(Oper.delete);
	        reqData.setSecret("0");
	        reqData.setData(datas);
	        reqData.setStore_id(storeId);
	        reqData.setTenancy_id(tenancyId);
	        
//	        List<JSONObject> dataList = new ArrayList<JSONObject>();
//	        logger.info("秒付退款请求参数："+JSONObject.fromObject(reqData).toString());
//	        String  QUICK_PASS_URL = PosPropertyUtil.getMsg(com.tzx.pos.base.Constant.REFUND_URL);
//	        String response = HttpClientPostUtil.sendPostRequest(QUICK_PASS_URL+url,JSONObject.fromObject(reqData).toString());
//	        logger.info("秒付退款返回结果:"+response);
//
//	        JSONObject resObj = JSONObject.fromObject(response);
//	        JSONObject datajson = JSONObject.fromObject(JSONArray.fromObject(resObj.get("data")).get(0));
//	        String paymentState = datajson.optString("payment_state");
//	        //修改退款状态
//	        String sql = "update pos_bill_payment set payment_state = ? where bill_num =?";
//	        paymentDao.update(sql,new  Object[]{paymentState,bill_num});
//
//	        resultJson.put("code",resObj.optString("code"));
//	        resultJson.put("msg",resObj.optString("msg"));
//	        resultJson.put("payment_state",paymentState);
//	        if (datajson.containsKey("failure_msg")){
//	            resultJson.put("failure_msg",datajson.optString("failure_msg"));
//	        }
//	        if (datajson.containsKey("failure_code")){
//	            resultJson.put("failure_code",datajson.optString("failure_code"));
//	        }
//
//	        JSONObject Json = JSONObject.fromObject(response);
//	        dataList.add(Json);
//	        resultData.setData(dataList);
//	        resultData.setTenancy_id(tenancyId);
//	        resultData.setStore_id(storeId);
//	        resultData.setCode(Integer.valueOf(resObj.optString("code")));
//	        resultData.setMsg(resObj.optString("msg"));
	        
	        this.commonPost(reqData, resultData);
	    }

	@Override
	public void paymentRegain(String tenancyId, int storeId, String billNum, String orderNum, String chanel, Double billPaymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData, String operType) throws Exception
	{
	       logger.info("进入退单方法，传入的数据为"+payment.toString());
	        String bill_num = payment.optString("bill_num");
	        String payOrderId = payment.optString("bill_code");
	        String serviceType = payment.optString("serviceType");
	        String pos_num = payment.optString("pos_num");
	        String opt_num = payment.optString("cashier_num");
	        Double amount = payment.optDouble("amount");
	        String payType = payment.optString("payment_class");
	        String currency = payment.optString("currency_amount");
	        String reportDate = payment.optString("report_Date");
	        String shift = payment.optString("shift_id");
//	        String channel = payment.optString("channel");
	        String season = getReason(billNum,storeId);
	        JSONObject obj = new JSONObject();
	        obj.put("order_no",bill_num);
	        obj.put("payOrderId","".equals(payOrderId)?orderNum:payOrderId);
	        obj.put("pos_num",pos_num);
	        obj.put("season",season);
	        obj.put("service_type","".equals(serviceType)?SysDictionary.SERVICE_TYPE_CONSUME:serviceType);
	        obj.put("opt_num",opt_num);
	        obj.put("amount",amount<1?billPaymentAmount:amount);
	        obj.put("pay_type","".equals(payType)?SysDictionary.PAYMENT_CLASS_SECONDS_PAY:payType);
	        obj.put("currency",currency);
	        obj.put("report_date","".equals(reportDate)? currentTime.toString():reportDate);
	        obj.put("shift",shift);
	        obj.put("channel",chanel);
	        List<JSONObject> datas = new ArrayList<JSONObject>();
	        datas.add(obj);
	        Data reqData = Data.get(tenancyId,Integer.valueOf(storeId),0);
	        reqData.setType(Type.REFUND_PAY_ORDER);
	        reqData.setOper(Oper.delete);
	        reqData.setSecret("0");
	        reqData.setData(datas);
	        reqData.setStore_id(storeId);
	        reqData.setTenancy_id(tenancyId);
	        
//	        List<JSONObject> dataList = new ArrayList<JSONObject>();
//	        logger.info("秒付退款请求参数："+JSONObject.fromObject(reqData).toString());
//	        String  QUICK_PASS_URL = PosPropertyUtil.getMsg(com.tzx.pos.base.Constant.REFUND_URL);
//	        String response = HttpClientPostUtil.sendPostRequest(QUICK_PASS_URL+url,JSONObject.fromObject(reqData).toString());
//	        logger.info("秒付退款返回结果:"+response);
//
//	        JSONObject resObj = JSONObject.fromObject(response);
//	        JSONObject datajson = JSONObject.fromObject(JSONArray.fromObject(resObj.get("data")).get(0));
//	        String paymentState = datajson.optString("payment_state");
//	        
//	        //修改退款状态
//	        String sql = "update pos_bill_payment set payment_state = ? where bill_num =?";
//	        paymentDao.update(sql,new  Object[]{paymentState,bill_num});
//
//	        resultJson.put("code",resObj.optString("code"));
//	        resultJson.put("msg",resObj.optString("msg"));
//	        resultJson.put("payment_state",paymentState);
//	        if (datajson.containsKey("failure_msg")){
//	            resultJson.put("failure_msg",datajson.optString("failure_msg"));
//	        }
//	        if (datajson.containsKey("failure_code")){
//	            resultJson.put("failure_code",datajson.optString("failure_code"));
//	        }
//
//	        JSONObject Json = JSONObject.fromObject(response);
//	        dataList.add(Json);
//	        resultData.setData(dataList);
//	        resultData.setTenancy_id(tenancyId);
//	        resultData.setStore_id(storeId);
//	        resultData.setCode(Integer.valueOf(resObj.optString("code")));
//	        resultData.setMsg(resObj.optString("msg"));
	        
	        this.commonPost(reqData, resultData);
	    }
	
	public void commonPost(Data paramData, Data resData)
	{
		String reqURL = PosPropertyUtil.getMsg(com.tzx.pos.base.Constant.REFUND_URL) + url;
		JSONObject paramJson = JSONObject.fromObject(paramData);
		try
		{
			long t = System.currentTimeMillis();
			logger.info(String.valueOf(t) + "<秒付接口请求参数==>type: " + paramData.getType().name());
			logger.info(String.valueOf(t) + "<秒付接口请求参数==>oper: " + paramData.getOper().name());
			logger.info(String.valueOf(t) + "<秒付接口请求参数==>" + paramJson.toString());
			String result = HttpUtil.sendPostRequest(reqURL, paramJson.toString());
			logger.info(String.valueOf(t) + "<秒付接口返回结果==>" + result);
			if (Tools.isNullOrEmpty(result))
			{
				resData.setCode(Constant.CODE_CONN_EXCEPTION);
				resData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
				resData.setSuccess(false);
				logger.info("连接超时，请检查网络,请求体为：" + paramJson.toString());
			}
			else
			{
				try
				{
					Data data = JsonUtil.JsonToData(JSONObject.fromObject(result));
					if (1 == data.getCode())
					{
						resData.setCode(Constant.CODE_CONN_EXCEPTION);
						resData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
						logger.info("连接异常,请求体为：" + paramJson.toString());
					}
					else
					{
						resData.setCode(data.getCode());
						resData.setMsg(data.getMsg());
					}
					resData.setData(data.getData());
					resData.setSuccess(data.isSuccess());
				}
				catch (Exception se)
				{
					logger.info("转查会员信息错误：" + ExceptionMessage.getExceptionMessage(se));
					se.printStackTrace();
				}
			}
		}
		catch (Exception se)
		{
			logger.info("连接超时，请检查网络,请求体为：" + paramJson.toString());
			resData.setCode(5);
			resData.setMsg("连接超时，请检查网络");
			resData.setSuccess(false);
		}
	}

	@Override
	public void paymentDebitResult(String tenancyId, int storeId, String billNum, JSONObject paymentJson) throws Exception
	{
		// TODO 自动生成的方法存根
		JSONObject updatePaymentJson = new JSONObject();
		updatePaymentJson.put("bill_code", paymentJson.optString("bill_code"));
		updatePaymentJson.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
		updatePaymentJson.put("id", paymentJson.optInt("id"));
		paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", updatePaymentJson);
	}
}
