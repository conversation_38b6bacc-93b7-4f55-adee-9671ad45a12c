package com.tzx.pos.asyn.thread;

import com.tzx.pos.base.constant.FunctionCode;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.SpringContextUtils;
import com.tzx.pos.bo.PosPrintNewService;
import com.tzx.pos.po.springjdbc.dao.PosPaymentDao;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.jdbc.support.rowset.SqlRowSet;

import java.util.List;

/**
 * 打印结账单和会员小票 --- 线程
 */
public class BillAndMemberPrintThread implements Runnable{

    private static final Logger logger = Logger.getLogger(BillAndMemberPrintThread.class);

    private PosPaymentDao paymentDao = null;

    private PosPrintNewService posPrintNewService = null;

    private JSONObject printJson = null;

    public BillAndMemberPrintThread(JSONObject printJson)
    {
        this.printJson = printJson;
    }

    @Override
    public void run() {

        try{
            posPrintNewService = (PosPrintNewService) SpringContextUtils.getBean("com.tzx.pos.bo.imp.PosPrintNewServiceImp");

            String tenancyId = this.printJson.optString("tenancy_id");
            Integer storeId = this.printJson.optInt("store_id");
            String billNum = this.printJson.optString("bill_num");
            if(StringUtils.isEmpty(tenancyId) || storeId == null || StringUtils.isEmpty(billNum))
            {
                logger.info("账单参数为空：tenancyId="+tenancyId+" ,storeId="+storeId+" ,billNum="+billNum);
                return;
            }
            logger.info("云POS同步到门店后的打印参数："+this.printJson.toString());

            String posNum = this.printJson.optString("pos_num");
            String optNum = this.printJson.optString("opt_num");

            // 判断是否是移动端设备下的单
            String movePosNum = posPrintNewService.getPosNumByMoveDevicesNum(tenancyId, storeId, posNum);
            if(StringUtils.isNotEmpty(movePosNum)){
                printJson.put("pos_num", movePosNum);
            }

            boolean isNewPrint = posPrintNewService.isNONewPrint(tenancyId, storeId);
            if(isNewPrint)
            {
                try
                {
                    // 会员小票打印
                    StringBuilder memberSql = new StringBuilder();
                    memberSql.append("select m.last_updatetime, m.bill_code, m.card_code, m.customer_name as name, m.amount as consume_cardmoney, m.remark, coalesce(m.consume_after_main_balance, 0) as main_balance, coalesce(m.consume_after_reward_balance, 0) as reward_balance, coalesce(m.consume_after_credit, 0) as useful_credit ");
                    memberSql.append(" from pos_bill_member m where m.bill_num = ? and m.type = ? and m.store_id = ? and m.tenancy_id = ?");

                    paymentDao = (PosPaymentDao) SpringContextUtils.getBean("com.tzx.pos.po.springjdbc.dao.imp.PosPaymentDaoImp");
                    List<JSONObject> memberList = paymentDao.query4Json(tenancyId, memberSql.toString(), new Object[]{billNum, SysDictionary.BILL_MEMBERCARD_CZXF03, storeId, tenancyId});
                    if(memberList != null && memberList.size() > 0)
                    {
                        String operatorName = "";
                        String sqlName = "select e.name from employee e where cast(e.id as varchar) = ? and e.store_id = ? and e.tenancy_id = ? ";
                        SqlRowSet rowSet = paymentDao.query4SqlRowSet(sqlName, new Object[]{optNum, storeId, tenancyId});
                        if(rowSet.next())
                        {
                            operatorName = rowSet.getString("name");
                        }

                        JSONObject member = memberList.get(0);

                        JSONObject newPrintJson = new JSONObject();

                        String remark = member.optString("remark");
                        if(StringUtils.isNotEmpty(remark))
                        {
                            JSONObject remarkJson = JSONObject.fromObject(remark);
                            if(remarkJson != null)
                            {
                                newPrintJson.put("level_name", remarkJson.optString("level_name")); // 会员等级
                                newPrintJson.put("card_class_name", remarkJson.optString("card_class_name")); // 会员卡类型
                            }
                        }

                        newPrintJson.putAll(printJson);
                        newPrintJson.putAll(member);

                        newPrintJson.put("operator", operatorName);
                        newPrintJson.put("updatetime", member.optString("last_updatetime"));
                        newPrintJson.remove("function_id");

                        // 会员小票打印
                        posPrintNewService.posPrintByMode(tenancyId, storeId, SysDictionary.PRINT_CODE_1010, newPrintJson);
                    }
                }
                catch (Exception e1)
                {
                    logger.error("打印会员小票异常：", e1);
                }

                // 结账单打印
                posPrintNewService.posPrintByFunction(tenancyId, storeId, FunctionCode.BILL_PAYMENT, printJson);
            }
            else
            {
                logger.info("tenancyId="+tenancyId+",storeId="+storeId+"设置的是旧打印，咱不支持");
            }
        }
        catch (Exception e)
        {
            e.printStackTrace();
            logger.error("云端账单同步到门店时打印异常：", e);
        }

    }
}
