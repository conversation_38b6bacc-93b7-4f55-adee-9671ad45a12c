package com.tzx.pos.bo.imp;

import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;

import org.apache.commons.lang.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.tzx.pos.base.Constant;
import com.tzx.pos.bo.PosDebitImageService;
@Service
public class PosDebitImageServiceImp implements PosDebitImageService{
	@Cacheable(value="debitUserImageCache",key="#imageUrl")
	public String downloadDebitImage(String imageUrl) throws Exception {
		
		String itemImagePath = com.tzx.framework.common.constant.Constant.CONTEXT_PATH + Constant.DEBIT_USER_IMAGE;
		
		if (!StringUtils.isEmpty(imageUrl)){
			URL url = new URL(imageUrl);
			String filePath = itemImagePath + url.getPath();
			
		    File photFile = new File(filePath);
			if (!photFile.getParentFile().exists())
			{
				photFile.getParentFile().mkdirs();
			}
			
			DataInputStream in = null;
			DataOutputStream out = null;
			try
			{
				HttpURLConnection conn = (HttpURLConnection) url.openConnection();
				conn.connect();
				if (conn.getResponseCode() == 200)
				{
					in = new DataInputStream(conn.getInputStream());
					out = new DataOutputStream(new FileOutputStream(filePath));

					byte[] buffer = new byte[4096];
					int count = 0;
					while ((count = in.read(buffer)) > 0)
					{
						out.write(buffer, 0, count);
					}
					return "/"+Constant.DEBIT_USER_IMAGE+url.getPath().substring(url.getPath().lastIndexOf("/webapps")+1,url.getPath().length());
				}
			}
			finally
			{
				try
				{
					if (in != null)
					{
						in.close();
					}
					if (out != null)
					{
						out.close();
					}
				}
				catch (IOException e)
				{
					e.printStackTrace();
				}
			}
				
		}
		return null;
	}
}
