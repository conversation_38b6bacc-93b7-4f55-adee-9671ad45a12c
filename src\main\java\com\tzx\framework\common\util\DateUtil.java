package com.tzx.framework.common.util;

import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLConnection;
import java.sql.Time;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.Locale;

import net.sf.json.JSONNull;
import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.LogManager;
import org.apache.log4j.Logger;

public class DateUtil {
    private static final Logger logger = LogManager.getLogger(DateUtil.class);
//	private static SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	
//	private static SimpleDateFormat simpleDateFormat2222222 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//    private static SimpleDateFormat simpleTimeFormat = new SimpleDateFormat("HH:mm:ss");
//    public static SimpleDateFormat simpleyyyyMMddFormat = new SimpleDateFormat("yyyy-MM-dd");
//    public static SimpleDateFormat simpleyyyyMMddFormat2 = new SimpleDateFormat("yyyy年MM月dd日");
//    private static SimpleDateFormat simpleHHmmTimeFormat = new SimpleDateFormat("HH:mm");
//    private static SimpleDateFormat simpleDateFormatYM = new SimpleDateFormat("yyyy-MM");
//    private static SimpleDateFormat simpleDateFormatYMZH = new SimpleDateFormat("yyyy年MM月");
//    private static SimpleDateFormat simpleDateFormatY = new SimpleDateFormat("yyyy");
//    private static SimpleDateFormat simpleDateFormatYYYYMMDD = new SimpleDateFormat("yyyyMM-dd");
//    private static SimpleDateFormat simpleDateFormatHHMMSS = new SimpleDateFormat("HHmmss");
    private static ThreadLocal<SimpleDateFormat> local_simpleDateFormat = new ThreadLocal<SimpleDateFormat>();  
    private static ThreadLocal<SimpleDateFormat> local_simpleTimeFormat = new ThreadLocal<SimpleDateFormat>();  
    private static ThreadLocal<SimpleDateFormat> local_simpleyyyyMMddFormat = new ThreadLocal<SimpleDateFormat>();  
    private static ThreadLocal<SimpleDateFormat> local_simpleyyyyMMddFormat2 = new ThreadLocal<SimpleDateFormat>();  
    private static ThreadLocal<SimpleDateFormat> local_simpleHHmmTimeFormat = new ThreadLocal<SimpleDateFormat>();  
    private static ThreadLocal<SimpleDateFormat> local_simpleDateFormatYM = new ThreadLocal<SimpleDateFormat>();  
    private static ThreadLocal<SimpleDateFormat> local_simpleDateFormatYMZH = new ThreadLocal<SimpleDateFormat>();  
    private static ThreadLocal<SimpleDateFormat> local_simpleDateFormatY = new ThreadLocal<SimpleDateFormat>();  
    private static ThreadLocal<SimpleDateFormat> local_simpleDateFormatYYYYMMDD = new ThreadLocal<SimpleDateFormat>();  
    private static ThreadLocal<SimpleDateFormat> local_simpleDateFormatHHMMSS = new ThreadLocal<SimpleDateFormat>();  
    private static ThreadLocal<SimpleDateFormat> local_simpleDateFormaYMDH = new ThreadLocal<SimpleDateFormat>();

    /**
     * 毫秒
     */
    public static final String TIME_TYPE_MILLISECOND = "millisecond";
    /**
     * 秒
     */
    public static final String TIME_TYPE_SECOND = "second";
    /**
     * 分钟
     */
    public static final String TIME_TYPE_MINUTE = "minute";
    /**
     * 小时
     */
    public static final String TIME_TYPE_HOUR = "hour";

    public static final int DEFAULT_CONNTIMEOUT = 60000;    // 默认连接超时时间
    public static final int DEFAULT_READTIMEOUT   = 60000;   // 默认读取数据超时

    /**
     * 网络北京时间
     */
    public static final String  BEIJING_TIME="http://www.ntsc.ac.cn/";
    public static final String  BEIJING_TIME2="http://time.tianqi.com/";

    private static SimpleDateFormat get(String type)
    {
    	switch (type)
		{
			case "simpleDateFormat":
				SimpleDateFormat simpleDateFormat = local_simpleDateFormat.get();
				if(simpleDateFormat==null)
				{
					simpleDateFormat =  new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
					local_simpleDateFormat.set(simpleDateFormat);
				}
				return simpleDateFormat;

			case "simpleTimeFormat":
				SimpleDateFormat simpleTimeFormat = local_simpleTimeFormat.get();
				
				if(simpleTimeFormat==null)
				{
					simpleTimeFormat =  new SimpleDateFormat("HH:mm:ss");
					local_simpleTimeFormat.set(simpleTimeFormat);
				}
				return simpleTimeFormat;

			case "simpleyyyyMMddFormat":
				SimpleDateFormat simpleyyyyMMddFormat = local_simpleyyyyMMddFormat.get();
				
				if(simpleyyyyMMddFormat==null)
				{
					simpleyyyyMMddFormat = new SimpleDateFormat("yyyy-MM-dd");
					local_simpleyyyyMMddFormat.set(simpleyyyyMMddFormat);
				}
				return simpleyyyyMMddFormat;
	
			case "simpleyyyyMMddFormat2":
				SimpleDateFormat simpleyyyyMMddFormat2 = local_simpleyyyyMMddFormat2.get();

				if(simpleyyyyMMddFormat2==null)
				{
					simpleyyyyMMddFormat2 = new SimpleDateFormat("yyyy年MM月dd日");
					local_simpleyyyyMMddFormat2.set(simpleyyyyMMddFormat2);
				}
				return simpleyyyyMMddFormat2;

			case "simpleHHmmTimeFormat":
				SimpleDateFormat simpleHHmmTimeFormat = local_simpleHHmmTimeFormat.get();
				
				if(simpleHHmmTimeFormat==null)
				{
					simpleHHmmTimeFormat = new SimpleDateFormat("HH:mm");
					local_simpleHHmmTimeFormat.set(simpleHHmmTimeFormat);
				}
				return simpleHHmmTimeFormat;

			case "simpleDateFormatYM":
				SimpleDateFormat simpleDateFormatYM = local_simpleDateFormatYM.get();
				
				if(simpleDateFormatYM==null)
				{
					simpleDateFormatYM = new SimpleDateFormat("yyyy-MM");
					local_simpleDateFormatYM.set(simpleDateFormatYM);
				}
				return simpleDateFormatYM;

			case "simpleDateFormatYMZH":
				SimpleDateFormat simpleDateFormatYMZH = local_simpleDateFormatYMZH.get();
				
				if(simpleDateFormatYMZH==null)
				{
					simpleDateFormatYMZH = new SimpleDateFormat("yyyy年MM月");
					local_simpleDateFormatYMZH.set(simpleDateFormatYMZH);
				}
				return simpleDateFormatYMZH;

			case "simpleDateFormatY":
				SimpleDateFormat simpleDateFormatY = local_simpleDateFormatY.get();
				
				if(simpleDateFormatY==null)
				{
					simpleDateFormatY = new SimpleDateFormat("yyyy");
					local_simpleDateFormatY.set(simpleDateFormatY);
				}
				return simpleDateFormatY;

			case "simpleDateFormatYYYYMMDD":
				SimpleDateFormat simpleDateFormatYYYYMMDD = local_simpleDateFormatYYYYMMDD.get();
				
				if(simpleDateFormatYYYYMMDD==null)
				{
					simpleDateFormatYYYYMMDD = new SimpleDateFormat("yyyyMM-dd");
					local_simpleDateFormatYYYYMMDD.set(simpleDateFormatYYYYMMDD);
				}
				return simpleDateFormatYYYYMMDD;

			case "simpleDateFormatHHMMSS":
				SimpleDateFormat simpleDateFormatHHMMSS = local_simpleDateFormatHHMMSS.get();
				
				if(simpleDateFormatHHMMSS==null)
				{
					simpleDateFormatHHMMSS = new SimpleDateFormat("HHmmss");	
					local_simpleDateFormatHHMMSS.set(simpleDateFormatHHMMSS);
				}
				return simpleDateFormatHHMMSS;
				
			case "simpleDateFormaYMDH":
				SimpleDateFormat simpleDateFormaYMDH = local_simpleDateFormaYMDH.get();
				
				if(simpleDateFormaYMDH==null)
				{
					simpleDateFormaYMDH = new SimpleDateFormat("yyyyMMddHH");	
					local_simpleDateFormaYMDH.set(simpleDateFormaYMDH);
				}
				
				return simpleDateFormaYMDH;
			default:
				break;
		}
    	return null;
    }
   
    /**
     * format date just only for MQ generate insert SQL sentences。
     *
     * @param
     * @return
     */
    public static String formatForMQ(Object date) {
        if (null == date) {
            return "null";
        } else {
            return String.format("'%s'", format(date));
        }
    }

    public static String format(Object d) {
        if (d == null || d instanceof JSONNull) {
            return "";
        }
        SimpleDateFormat simpleDateFormat = get("simpleDateFormat");
        SimpleDateFormat simpleTimeFormat = get("simpleTimeFormat");
        SimpleDateFormat simpleyyyyMMddFormat = get("simpleyyyyMMddFormat");
        if (d instanceof Timestamp) {
            return simpleDateFormat.format((Timestamp) d);
        } else if (d instanceof Time) {
            return simpleTimeFormat.format((Time) d);
        } else if (d instanceof Date) {
            return simpleyyyyMMddFormat.format((Date) d);
        }

        return d.toString();
    }

    public static String format(Object d, String partten) {
        if (d == null || d instanceof JSONNull) {
            return "";
        }

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(partten);

        return simpleDateFormat.format(d);
    }

    /**
     * 返回 HH:mm:ss
     *
     * @param d
     * @return
     */
    public static String formatTime(Object d) {
        if (d == null || d instanceof JSONNull) {
            return "";
        }
        SimpleDateFormat simpleTimeFormat = get("simpleTimeFormat");
        return simpleTimeFormat.format(d);
    }

    public static Date parseDate(String s) {
        if (StringUtils.isEmpty(s)) {
            return null;
        }
        SimpleDateFormat simpleyyyyMMddFormat = get("simpleyyyyMMddFormat");
        try {
            return simpleyyyyMMddFormat.parse(s);
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 获取当前日期 yyyy-MM-dd HH:mm:ss
     *
     * @return Timestamp
     */
    public static Timestamp currentTimestamp() {
    	SimpleDateFormat simpleDateFormat = get("simpleDateFormat");
        String str = simpleDateFormat.format(new Date());
        try {
            return new Timestamp(simpleDateFormat.parse(str).getTime());
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 根据长度格式化日期 yyyy-MM-dd
     *
     * @return Timestamp
     */
    public static Timestamp limit6Timestamp() {
    	SimpleDateFormat simpleyyyyMMddFormat = get("simpleyyyyMMddFormat");
        String str = simpleyyyyMMddFormat.format(new Date());
        try {
            return new Timestamp(simpleyyyyMMddFormat.parse(str).getTime());
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 根据格式返回Timestamp,如果格式为空返回yyyy-MM-dd HH:mm:ss
     *
     * @param s
     * @param format
     * @return Timestamp
     */
    public static Timestamp formatTimestamp(String s, String format) {
        if (s == null) {
            return null;
        }
        SimpleDateFormat simpleDateFormat = get("simpleDateFormat");
        SimpleDateFormat simpleyyyyMMddFormat = get("simpleyyyyMMddFormat");
        try {
            if (StringUtils.isEmpty(format)) {
                return new Timestamp(simpleDateFormat.parse(s).getTime());
            } else if ("yyyy-MM-dd HH:mm:ss".equalsIgnoreCase(format)) {
                return new Timestamp(simpleDateFormat.parse(s).getTime());
            } else if ("yyyy-MM-dd".equalsIgnoreCase(format)) {
                return new Timestamp(simpleyyyyMMddFormat.parse(s).getTime());
            } else {
                return new Timestamp(System.currentTimeMillis());
            }
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 返回带格式的Timestamp，格式为：yyyy-MM-dd HH:mm:ss
     *
     * @param s
     * @return Timestamp
     */
    public static Timestamp formatTimestamp(String s) {
        if (s == null) {
            return null;
        }
        SimpleDateFormat simpleDateFormat = get("simpleDateFormat");
        try {
            return new Timestamp(simpleDateFormat.parse(s).getTime());
        } catch (ParseException e) {
            return null;
        }
    }

    public static Timestamp parseTimestamp(String s) {
        if (s == null) {
            return null;
        }
        SimpleDateFormat simpleDateFormat = get("simpleDateFormat");
        try {
            return new Timestamp(simpleDateFormat.parse(s).getTime());
        } catch (ParseException e) {
            return null;
        }
    }

    public static Time parseTime(String s) {
        if (s == null) {
            return null;
        }
        SimpleDateFormat simpleTimeFormat = get("simpleTimeFormat");
        try {
            return new Time(simpleTimeFormat.parse(s).getTime());
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 传入yyyy-MM-dd HH:mm:ss，返回date
     *
     * @param s
     * @return
     */
    public static Date parseDateAll(String s) {
        if (s == null) {
            return null;
        }
        SimpleDateFormat simpleDateFormat = get("simpleDateFormat");
        try {
            return simpleDateFormat.parse(s);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 比较两个时间点的大小
     *
     * @param t1
     * @param t2
     * @return
     */
    public static int timeCompare(String t1, String t2) {
        SimpleDateFormat formatter = new SimpleDateFormat("HH:mm:ss");
        Calendar c1 = Calendar.getInstance();
        Calendar c2 = Calendar.getInstance();
        try {
            c1.setTime(formatter.parse(t1));
            c2.setTime(formatter.parse(t2));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        int result = c1.compareTo(c2);
        return result;
    }

    /**
     * 比较两个时间的时间差(打印)
     *
     * @param date1
     * @param date2
     * @param notes 注释
     * @return 打印时间差(n天n小时n分n秒n毫秒), 返回大的时间Date格式
     * <p>
     * <br>
     * 主要用于测试方法执行时间,会分别输出每个代码段的执行时间;<br>
     * 例:<br>
     * <br>
     * 方法开始<br>
     * Date startTime = new Date();<br>
     * 代码段一<br>
     * startTime = DateUtil.timeCompare(new Date(), startTime, "代码段一");<br>
     * 代码段二<br>
     * startTime = DateUtil.timeCompare(new Date(), startTime, "代码段二");<br>
     * 代码段三<br>
     * startTime = DateUtil.timeCompare(new Date(), startTime, "代码段三");<br>
     * <p>
     */
    public static Date timeCompare(Date date1, Date date2, String notes) {
        Date returnTime;
        long timeDifference = 0;
        if (date2.getTime() > date1.getTime()) {
            timeDifference = date2.getTime() - date1.getTime();
            returnTime = date2;
        } else {
            timeDifference = date1.getTime() - date2.getTime();
            returnTime = date1;
        }
        long day = timeDifference / (24 * 60 * 60 * 1000);
        long hour = (timeDifference / (60 * 60 * 1000) - day * 24);
        long min = ((timeDifference / (60 * 1000)) - day * 24 * 60 - hour * 60);
        long s = (timeDifference / 1000 - day * 24 * 60 * 60 - hour * 60 * 60 - min * 60);
        long ms = (timeDifference % 1000 - day * 24 * 60 * 60 - hour * 60 * 60 - min * 60);
        System.out.println("【" + notes + "】:" + day + "天" + hour + "小时" + min + "分" + s + "秒" + ms + "毫秒");
        return returnTime;
    }

    /**
     * 返回本周的开始和结束时间
     *
     * @return
     */
    public static Date[] getCurrentWeek() {
        Calendar ca = Calendar.getInstance();
        int dayOfWeek = ca.get(Calendar.DAY_OF_WEEK);
        // 中国习惯：周一是一周的开始
        if (dayOfWeek == 1) {
            dayOfWeek = 7;
        } else {
            dayOfWeek--;
        }
        Calendar cal = (Calendar) ca.clone();

        cal.add(Calendar.DATE, 1 - dayOfWeek);
        Date date1 = cal.getTime();
        cal = (Calendar) ca.clone();
        cal.add(Calendar.DATE, 7 - dayOfWeek);
        Date date2 = cal.getTime();
        return new Date[]
                {date1, date2};
    }

    /**
     * 判断当前日期是星期几<br>
     * <br>
     *
     * @param pTime 修要判断的时间<br>
     * @return dayForWeek 判断结果<br>
     * @Exception 发生异常<br>
     */
    public static int dayForWeek(String pTime) throws Exception {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Calendar c = Calendar.getInstance();
        c.setTime(format.parse(pTime));
        int dayForWeek = 0;
        if (c.get(Calendar.DAY_OF_WEEK) == 1) {
            dayForWeek = 7;
        } else {
            dayForWeek = c.get(Calendar.DAY_OF_WEEK) - 1;
        }
        return dayForWeek;
    }

    /**
     * 得到当前日期 YYYY-MM-DD
     */
    public static String getNowDateYYDDMM() {
    	SimpleDateFormat simpleyyyyMMddFormat = get("simpleyyyyMMddFormat");
        return simpleyyyyMMddFormat.format(new Date());

    }
    
    /**
     * 得到日期的下一天
     * @param date
     * @return
     */
    public static String getNextDayYYYYMMDD(Date date){
    	Calendar c = Calendar.getInstance();
    	c.setTime(date);
    	c.set(Calendar.DATE, c.get(Calendar.DATE) + 1);
    	SimpleDateFormat simpleyyyyMMddFormat = get("simpleyyyyMMddFormat");
    	return simpleyyyyMMddFormat.format(c.getTime());
    }
    /**
     * 得到日期的下一天
     * @param date
     * @return
     */
    public static String getNextDayYYYYMMDD(String date){
    	return getNextDayYYYYMMDD(parseDate(date));
    }
    
    public static String getNowDateYYDDMMTomorrow() {
    	Date date1 = new Date();
    	@SuppressWarnings("deprecation")
    	Date date2 = new Date(date1.getYear(), date1.getMonth(), date1.getDate()+1);
    	SimpleDateFormat simpleyyyyMMddFormat = get("simpleyyyyMMddFormat");
        return simpleyyyyMMddFormat.format(date2);

    }

    
    /**
     * 得到当前年 YYYY
     */
    public static String getNowDateY() {
    	SimpleDateFormat simpleDateFormatY = get("simpleDateFormatY");
        return simpleDateFormatY.format(new Date());

    }

    /**
     * 得到下一年 YYYY
     */
    public static int getNextY() {
        Calendar cal = Calendar.getInstance();
        int year = cal.get(Calendar.YEAR);//获取年份
        return year + 1;
    }

    /**
     * 得到当前日期 YYYY-MM-DD HH:MM:SS
     */
    public static String getNowDateYYDDMMHHMMSS() {
    	SimpleDateFormat simpleDateFormat = get("simpleDateFormat");
        return simpleDateFormat.format(new Date());
    }

    /**
     * 得到当前HH:mm
     */
    public static String getNowDateHHMM() {
    	SimpleDateFormat simpleHHmmTimeFormat = get("simpleHHmmTimeFormat");
        return simpleHHmmTimeFormat.format(new Date());
    }

    /**
     * 得到传入的date得到HH:mm
     */
    public static String getDateHHMM(Date date) {
    	SimpleDateFormat simpleHHmmTimeFormat = get("simpleHHmmTimeFormat");
        return simpleHHmmTimeFormat.format(date);
    }

    /**
     * 转换HHmm字符串
     */
    public static Date parseDateHHMM(String hhmm) throws Exception {
    	SimpleDateFormat simpleHHmmTimeFormat = get("simpleHHmmTimeFormat");
        return simpleHHmmTimeFormat.parse(hhmm);
    }

    public static String getNowHHMMSS() throws Exception {
    	SimpleDateFormat simpleTimeFormat = get("simpleTimeFormat");
        return simpleTimeFormat.format(new Date());
    }

    public static Time getNowHHMMSSDate() throws Exception {
    	SimpleDateFormat simpleTimeFormat = get("simpleTimeFormat");
        return new Time(simpleTimeFormat.parse(simpleTimeFormat.format(new Date())).getTime());
    }

    public static Date parseHHMMSSDate(String hhmmss) throws Exception {
    	SimpleDateFormat simpleTimeFormat = get("simpleTimeFormat");
        return simpleTimeFormat.parse(hhmmss);
    }

    public static String formatDateHHMM(Time time) throws Exception {
    	SimpleDateFormat simpleHHmmTimeFormat = get("simpleHHmmTimeFormat");
        return simpleHHmmTimeFormat.format(time);
    }

    public static Time addTime(Time time, int minutes) throws Exception {
        return null;
    }

    /**
     * 判断字符串是否是日期格式
     *
     * @param date
     * @return
     */
    public static boolean isDate(String date) {
        try {
            return java.text.DateFormat.getDateInstance().parse(date) != null;
        } catch (java.text.ParseException e) {
            return false;
        }
    }

    /**
     * 返回当前日期0点的毫秒数
     *
     * @return Long
     */
    public static Long getTodayTime() {
        Date date1 = new Date();

        @SuppressWarnings("deprecation")
        Date date2 = new Date(date1.getYear(), date1.getMonth(), date1.getDate());

        return date2.getTime();
    }

    /**
     * 返回昨天日期0点的毫秒数
     *
     * @return Long
     */
    public static Long getYesterdayTime() {
        Date date1 = new Date();

        @SuppressWarnings("deprecation")
        Date date2 = new Date(date1.getYear(), date1.getMonth(), date1.getDate());

        return date2.getTime() - (24 * 60 * 60 * 1000);
    }

    public static Time parseTimeBystr(String param) {
        if (param == null) {
            return null;
        }
        SimpleDateFormat simpleHHmmTimeFormat = get("simpleHHmmTimeFormat");

        try {
            return new Time(simpleHHmmTimeFormat.parse(param).getTime());
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 判断两个日期是否是不同年份的同一天
     *
     * @param date1 date1
     * @param date2 date2
     * @return
     */
    public static boolean isSameDate(Date date1, Date date2) {
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date1);

        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(date2);
        boolean isSameMonth = cal1.get(Calendar.MONTH) == cal2.get(Calendar.MONTH);
        boolean isSameDate = isSameMonth && cal1.get(Calendar.DAY_OF_MONTH) == cal2.get(Calendar.DAY_OF_MONTH);

        return isSameDate;
    }

    /**
     * 将分钟时间格式化为**天**小时**分钟
     *
     * @return
     */
    public static String formatTime(int paramMinute) {
        long days = paramMinute / (60 * 24);
        long hours = (paramMinute % (60 * 24)) / 60;
        long minutes = paramMinute % 60;
        String result = "";
        if (days > 0) {
            result += days + "天";
        }
        if (hours > 0) {
            result += hours + "小时";
        }
        if (minutes > 0) {
            result += minutes + "分";
        }
        return result;
    }

    /**
     * 格式化日期类型的参数
     *
     * @param time
     * @return
     */
    public static String formatTime(String time) {
        try {
            Date date = DateUtil.parseDateAll(time);
            SimpleDateFormat simpleDateFormat = get("simpleDateFormat");
            return simpleDateFormat.format(date);
        } catch (Exception e) {
        }
        return null;
    }

 /*   public static void main(String[] args) throws Exception {
        // Time t = new Time(System.currentTimeMillis());
        //
        // System.out.println(format(t));
        // int a = timeCampare("","");
        System.out.println(DateUtil.getCurrentDateByDiff(600));
    }*/

    /**
     * 格式化日期类型的参数
     *
     * @return month年月 month_in传入 无则返回本月 month_in_next下月
     */
    @SuppressWarnings("deprecation")
    public static JSONObject getNextMonth(String yyyyMM) {
        JSONObject result = new JSONObject();
        Date now1 = new Date();
        SimpleDateFormat simpleDateFormatYM = get("simpleDateFormatYM");
        SimpleDateFormat simpleDateFormatYMZH = get("simpleDateFormatYMZH");
        SimpleDateFormat simpleDateFormat = get("simpleDateFormat");
        try {
            Date now = new Date();
            String nowds = simpleDateFormatYM.format(now);
            now1 = simpleDateFormatYM.parse(nowds);
            if (yyyyMM != null && yyyyMM.length() > 0) {
                Date indate = simpleDateFormatYM.parse(yyyyMM);

                result.put("month", simpleDateFormatYMZH.format(indate));
                result.put("month_in", simpleDateFormat.format(indate));
                indate.setMonth(indate.getMonth() + 1);
                result.put("month_in_next", simpleDateFormat.format(indate));
                return result;

            }
        } catch (ParseException e) {

            result.put("month", simpleDateFormatYMZH.format(now1));
            result.put("month_in", simpleDateFormat.format(now1));
            now1.setMonth(now1.getMonth() + 1);
            result.put("month_in_next", simpleDateFormat.format(now1));
            return result;
        }

        result.put("month", simpleDateFormatYMZH.format(now1));
        result.put("month_in", simpleDateFormat.format(now1));
        now1.setMonth(now1.getMonth() + 1);
        result.put("month_in_next", simpleDateFormat.format(now1));
        return result;
    }

    @SuppressWarnings("deprecation")
    public static JSONObject getThisMonth(String yyyyMM) {
        JSONObject result = new JSONObject();
        Date now1 = new Date();
        SimpleDateFormat simpleDateFormatYM = get("simpleDateFormatYM");
        SimpleDateFormat simpleDateFormatYMZH = get("simpleDateFormatYMZH");
        try {
            Date now = new Date();
            String nowds = simpleDateFormatYM.format(now);
            now1 = simpleDateFormatYM.parse(nowds);
            if (yyyyMM != null && yyyyMM.length() > 0) {
                Date indate = simpleDateFormatYM.parse(yyyyMM);

                result.put("month", simpleDateFormatYMZH.format(indate));
                result.put("month_c", yyyyMM);
                result.put("month_first", yyyyMM + "-01");
                Calendar a = Calendar.getInstance();
                a.set(Calendar.YEAR, indate.getYear());
                a.set(Calendar.MONTH, indate.getMonth());
                a.set(Calendar.DATE, 1);// 把日期设置为当月第一天
                a.roll(Calendar.DATE, -1);// 日期回滚一天，也就是最后一天
                result.put("month_last", yyyyMM + "-" + a.get(Calendar.DATE));
                result.put("days", a.get(Calendar.DATE));
                return result;

            }
        } catch (ParseException e) {
            String nd = simpleDateFormatYM.format(now1);
            result.put("month", simpleDateFormatYMZH.format(now1));
            result.put("month_c", nd);
            result.put("month_first", nd + "-01");
            Calendar a = Calendar.getInstance();
            a.set(Calendar.YEAR, now1.getYear());
            a.set(Calendar.MONTH, now1.getMonth());
            a.set(Calendar.DATE, 1);// 把日期设置为当月第一天
            a.roll(Calendar.DATE, -1);// 日期回滚一天，也就是最后一天
            result.put("month_last", nd + "-" + a.get(Calendar.DATE));
            result.put("days", a.get(Calendar.DATE));
            return result;
        }

        String nd = simpleDateFormatYM.format(now1);
        result.put("month", simpleDateFormatYMZH.format(now1));
        result.put("month_c", nd);
        result.put("month_first", nd + "-01");
        Calendar a = Calendar.getInstance();
        a.set(Calendar.YEAR, now1.getYear());
        a.set(Calendar.MONTH, now1.getMonth());
        a.set(Calendar.DATE, 1);// 把日期设置为当月第一天
        a.roll(Calendar.DATE, -1);// 日期回滚一天，也就是最后一天
        result.put("month_last", nd + "-" + a.get(Calendar.DATE));
        result.put("days", a.get(Calendar.DATE));
        return result;
    }

    /**
     * 得到这个月的第一天和今天
     *
     * @return month年月 month_in传入 无则返回本月 month_in_next下月
     */
    public static JSONObject getMonthDate() {
        JSONObject result = new JSONObject();
        Date now1 = new Date();
        SimpleDateFormat simpleDateFormatYM = get("simpleDateFormatYM");
        SimpleDateFormat simpleyyyyMMddFormat = get("simpleyyyyMMddFormat");
        String from = simpleDateFormatYM.format(now1) + "-01";
        String to = simpleyyyyMMddFormat.format(now1);
        result.put("month", from + " -- " + to);
        result.put("from", from);
        result.put("to", to);
        return result;
    }
    
    public static JSONObject getDateBeforeMonth(int num){
    	 JSONObject result = new JSONObject();
         Date b = new Date();
         Calendar c = Calendar.getInstance();
         c.add(Calendar.MONTH, -num);
         SimpleDateFormat simpleyyyyMMddFormat = get("simpleyyyyMMddFormat");
   	 	 String from = simpleyyyyMMddFormat.format(c.getTime());
   	 	 String to = simpleyyyyMMddFormat.format(b);
   	 	 result.put("from", from);
   	 	 result.put("to", to);
         return result;
    }
    /**
     * 计算两个日期相差天数
     *
     * @param date
     * @param date2
     * @return
     */
    public static int daysBetween(Date date, Date date2) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        long time1 = cal.getTimeInMillis();
        cal.setTime(date2);
        long time2 = cal.getTimeInMillis();
        long between_days = (time2 - time1) / (1000 * 3600 * 24);
        return Integer.parseInt(String.valueOf(between_days));
    }

    public static String getCurrentDateByDiff(int seconds) {
        Date now = new Date();
        long times = now.getTime() - seconds * 1000;
        Date result = new Date(times);
        return DateUtil.format(result, "yyyy-MM-dd HH:mm:ss");
    }
    
    public static String getyyyymmdd()
    {
    	SimpleDateFormat simpleDateFormatYYYYMMDD = get("simpleDateFormatYYYYMMDD");
    	 return simpleDateFormatYYYYMMDD.format(new Date());
    }
    
    public static String gethhmmss()
    {
    	SimpleDateFormat simpleDateFormatHHMMSS = get("simpleDateFormatHHMMSS");
    	 return simpleDateFormatHHMMSS.format(new Date());
    }
    
    public static String getSubSecYYYYMMDDHHMMSS(long sbS)
    {
    	SimpleDateFormat simpleDateFormat = get("simpleDateFormat");
    	return simpleDateFormat.format(new Date(System.currentTimeMillis()-(sbS*1000)));
    }
    
    public static String getYYYYMMDDHHMMSS(Date date)
    {
    	 return (new SimpleDateFormat("yyyyMMddHHmmss")).format(date);
    }
    /** 
     * 获取当前日期 yyyy-MM-dd HH:mm:ss
     *
     * @return Timestamp
     */
    public static Timestamp getNowTimestamp() {
    	
    	SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String str = dateFormat.format(new Date());
        try {
            return new Timestamp(dateFormat.parse(str).getTime());
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }
    
    public static String getDate(int days) {
        Date date1 = new Date();

        @SuppressWarnings("deprecation")
        Date date2 = new Date(date1.getYear(), date1.getMonth(), date1.getDate()+days);
        SimpleDateFormat simpleyyyyMMddFormat= get("simpleyyyyMMddFormat");
        return simpleyyyyMMddFormat.format(date2);
    }
    
    public static String getDate2(int days) {
        Date date1 = new Date();

        @SuppressWarnings("deprecation")
        Date date2 = new Date(date1.getYear(), date1.getMonth(), date1.getDate()+days);
        SimpleDateFormat simpleyyyyMMddFormat2 = get("simpleyyyyMMddFormat2");
        return simpleyyyyMMddFormat2.format(date2);
    }
    
    
    /**
     * @user yanZhen
     * 获取当前月份最后一天和第一天
     * 入参格式 "2017-04"  入参为空字符串时返回当前月的第一和最后一天
     */
    public static JSONObject getFirstAndLastDate(String str){
    	String yyyy_MM = "";
    	Calendar gc = new GregorianCalendar(); 
    	SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM"); 
    	
    
		try {
			if(StringUtils.isNotBlank(str) && !"null".equals(str)){
	    	    Date date = sdf.parse(str);  
	    		yyyy_MM = sdf.format(date);
	    	}else{
	    		yyyy_MM =new SimpleDateFormat("yyyy-MM").format(Calendar.getInstance().getTime());
	    		
	    	}
			Date date1 = sdf.parse(yyyy_MM);
			gc.setTime(date1); //放入你的日期 
		} catch (ParseException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} 
		
		int lastDay = gc.getActualMaximum(Calendar.DAY_OF_MONTH);
		JSONObject json = new JSONObject();
		json.put("start_day", yyyy_MM+"-01");
		json.put("end_day", yyyy_MM+"-"+lastDay);
		return json; 
    }
    
    
    /**
     * 比较日期大小
     * @user yanZhen
     * @param firstdate 
     * @param seconddate 
     * @return 1 表示 firstdate 大
     * 			2表示 seconddate 大
     * 			3表示 相等
     * 			0 表示比较失败
     */
    public static int compare4Date(String firstdate,String seconddate){
    	
    	SimpleDateFormat sdf = get("simpleyyyyMMddFormat");
    	int result = 0;
		try {
			Date first = sdf.parse(firstdate);
			Date second = sdf.parse(seconddate); 
			long first_time = first.getTime();
			long second_time = second.getTime();
			if(first_time > second_time){
				result =  1;
			}else if(second_time > first_time){
				result = 2;
			}else{
				result = 3;
			}
		} catch (ParseException e) {
			e.printStackTrace();
		} 
    	return result;
    }
    
    
    public static String getNewYMDH(){
    	
    	SimpleDateFormat sdf = get("simpleDateFormaYMDH");
    	String result= "";
    	Date now1 = new Date();
		try {
			result = sdf.format(now1);
			
		} catch (Exception e) {
			e.printStackTrace();
		} 
		
		
    	return result;
    }
    
     /** 
      * 时间戳转换成日期格式字符串 
     * @param seconds 精确到秒的字符串 
      * @param format
      * @return 
      */  
    public static String timeStamp2Date(String seconds,String format) {  
	  if(seconds == null || seconds.isEmpty() || seconds.equals("null")){  
	     return "";  
	  }  
	  if(format == null || format.isEmpty()){
	     format = "yyyy-MM-dd HH:mm:ss";
	  }   
	  SimpleDateFormat sdf = new SimpleDateFormat(format);  
	  return sdf.format(new Date(Long.valueOf(seconds+"000")));  
	}  
    /** 
     * 日期格式字符串转换成时间戳 
     * @param date_str 字符串日期
     * @param format 如：yyyy-MM-dd HH:mm:ss 
     * @return 
     */  
    public static String date2TimeStamp(String date_str,String format){  
        try {  
            SimpleDateFormat sdf = new SimpleDateFormat(format);  
            return String.valueOf(sdf.parse(date_str).getTime()/1000);  
        } catch (Exception e) {  
            e.printStackTrace();  
        }  
        return "";
    }

    /**
     * 获取2个时间点相差值（类型）
     * @param startTime
     * @param endTime
     * @param returnType
     * @return
     */
    public static long timestampBetween(Timestamp endTime, Timestamp startTime, String returnType){
        long timeLong = 0l;
        long differMsec = endTime.getTime() - startTime.getTime();

        switch (returnType)
        {
            case TIME_TYPE_MILLISECOND:
                timeLong = differMsec;
                break;
            case TIME_TYPE_SECOND:
                timeLong = differMsec/1000;
                break;
            case TIME_TYPE_MINUTE:
                timeLong = differMsec/(60*1000);
                break;
            case TIME_TYPE_HOUR:
                timeLong = differMsec/(60*60*1000);
                break;
            default:
                break;
        }

        return timeLong;
    }

    /**
     * 获取现在北京时间
     */
    public static String getWebsiteDatetime(){
        try {
            logger.info("进入获取网络时间getWebsiteDatetime");
            URL url = new URL(BEIJING_TIME);// 取得资源对象
            URLConnection uc = url.openConnection();// 生成连接对象
            uc.setConnectTimeout(DEFAULT_CONNTIMEOUT);
            uc.setReadTimeout(DEFAULT_READTIMEOUT);
            uc.connect();// 发出连接
            long ld = uc.getDate();// 读取网站日期时间
            logger.debug("获取网络时间1=="+ld);
            Date date;
            if(ld==0){
                url = new URL(BEIJING_TIME2);// 取得资源对象
                uc = url.openConnection();// 生成连接对象
                uc.setConnectTimeout(DEFAULT_CONNTIMEOUT);
                uc.setReadTimeout(DEFAULT_READTIMEOUT);
                uc.connect();// 发出连接
                long time2 = uc.getDate();// 读取网站日期时间
                logger.debug("获取网络时间2=="+time2);
                if(time2==0){
                    date = new Date();
                }else{
                    date  = new Date(time2);
                }
            }else{
                date= new Date(ld);// 转换为标准时间对象
            }
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.CHINA);// 输出北京时间
            logger.info("输出时间=="+sdf.format(date));
            return sdf.format(date);
        }  catch (Exception e) {
            e.printStackTrace();
            Date date= new Date();// 转换为标准时间对象e
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.CHINA);// 输出北京时间
            logger.info("e输出时间=="+sdf.format(date));
            return sdf.format(date);
        }
    }

    /**
     * 获取现在北京时间毫秒数
     */
    public static long getWebsitCurrentTimeMillis(){
        long timeLong = 0l;
        logger.info("进入获取网络时间getWebsitCurrentTimeMillis");
        try {
            URL url = new URL(BEIJING_TIME);// 取得资源对象
            URLConnection uc = url.openConnection();// 生成连接对象
            uc.setConnectTimeout(DEFAULT_CONNTIMEOUT);
            uc.setReadTimeout(DEFAULT_READTIMEOUT);
            uc.connect();// 发出连接
            timeLong= uc.getDate();// 读取网站日期时间
            logger.debug("获取网络时间1=="+timeLong);
            if(timeLong==0){
                url = new URL(BEIJING_TIME2);// 取得资源对象
                uc = url.openConnection();// 生成连接对象
                uc.setConnectTimeout(DEFAULT_CONNTIMEOUT);
                uc.setReadTimeout(DEFAULT_READTIMEOUT);
                uc.connect();// 发出连接
                timeLong = uc.getDate();// 读取网站日期时间
                logger.debug("获取网络时间2=="+timeLong);
                if(timeLong==0){
                    timeLong = System.currentTimeMillis();
                }
            }
        }  catch (Exception e) {
            timeLong = System.currentTimeMillis();
            e.printStackTrace();
        }
        logger.info("当前时间=="+timeLong);
        return timeLong;
    }

    /**
     * 日期加或减num天
     * @param sDate
     * @param num
     * @return
     */
    public static Date getDateByDay(Date sDate, int num) {
        Calendar cal = Calendar.getInstance();
        // 设置开始时间
        cal.setTime(sDate);
        //增加或减少num天
        cal.add(Calendar.DATE, num);
        return cal.getTime();
    }

    /**
     * 日期加或减num天
     * @param dateStr
     * @param num
     * @return
     * @throws Exception
     */
    public static String getDateByDay(String dateStr, int num) throws Exception {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date date = simpleDateFormat.parse(dateStr);
        Date resultDate = getDateByDay(date, num);
        return simpleDateFormat.format(resultDate);
    }
}
