package com.tzx.pos.po.springjdbc.dao;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import com.tzx.base.entity.PosBillPayment;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.pos.base.dao.BaseDao;

import net.sf.json.JSONObject;

public interface PosSelfDao extends BaseDao
{
	String	NAME	= "com.tzx.pos.po.springjdbc.dao.imp.PosSelfImp";

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param billBatch
	 * @throws Exception
	 */
	public void updatePosBill(String tenancyId, int storeId, JSONObject bill) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param condition
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> queryPosBillByBillNum(String tenancyId, int storeId, String billNum) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param tableCode
	 * @return
	 * @throws Exception
	 */
	public JSONObject queryPosBillForOpenByTablecode(String tenancyId, int storeId, String tableCode) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param billBatch
	 * @throws Exception
	 */
	public Integer insertBillBatch(String tenancyId, int storeId, JSONObject billBatch) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param batchNum
	 * @return
	 * @throws Exception
	 */
	public JSONObject queryBillBatchByBillNum(String tenancyId, int storeId, String billNum, String batchNum) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param batchNum
	 * @param freeAmount
	 * @param reasonId
	 * @throws Exception
	 */
	public void updateBillBatchForFreeAmountByBatchNum(String tenancyId, int storeId, String billNum, String batchNum, double freeAmount, int reasonId) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param batchNum
	 * @param itemRemark
	 * @throws Exception
	 */
	public void updateBillItemForItemRemarkByBatchNum(String tenancyId, int storeId, String billNum, String batchNum, String itemRemark) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param payId
	 * @throws Exception
	 */
	public void deletePosBillPaymentById(String tenancyId, int storeId, int payId) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param bill
	 * @throws Exception
	 */
	public void updatePosBillPayment(String tenancyId, int storeId, JSONObject payment) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param batchNum
	 * @throws Exception
	 */
	public int queryPosBillPaymentForCountByBatchNum(String tenancyId, int storeId, String billNum, String batchNum) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param payId
	 * @return
	 * @throws Exception
	 */
	public JSONObject queryPosBillPaymentById(String tenancyId, int storeId, int payId) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param payId
	 * @return
	 * @throws Exception
	 */
	public JSONObject queryPosBillPaymentByJzid(String tenancyId, int storeId, String billNum, String batch_num, int jzid) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param batch_num
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> queryPosBillPaymentForPayingByBatch(String tenancyId, int storeId, String billNum, String batch_num) throws Exception;

	/** 查询付款状态
	 * @param tenancyId
	 * @param storeId
	 * @param batchNum
	 * @return
	 * @throws SystemException
	 */
	public List<JSONObject> getPosBillBatchListByBillnum(String tenancyId, Integer storeId, String batchNum) throws SystemException;

	/** 查询账单批次表
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param batchNum
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> findBatch(String tenancyId, int storeId, String billNum, String batchNum) throws Exception;

	/** 修改账单批次退款金额
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param batchNum
	 * @throws Exception
	 */
	public void updateBatchReturnAmt(String tenancyId, int storeId, String billNum, String batchNum) throws Exception;
	
	/** 取得账单批次下单时间
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param batchNum
	 * @throws Exception
	 */
	public JSONObject getItemTime(String tenancyId, int storeId, String billNum, String batchNum) throws Exception;

	/** 插入付款历史记录
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param bill
	 * @throws Exception
	 */
	public void insertPosBillPaymentLog(String tenancyId, int storeId, String billNum, Date reportDate, int shiftId, String posNum, String optNum, String tableCode, String type, int jzid, String name, String nameEnglish, double rate, double amount, double count, double currencyAmount,
			String number, String phone, String isYsk, Integer customerId, String billCode, String remark, Timestamp lastUpdatetime, String paymentState, String batchNum, String paramCach, double more_coupon) throws Exception;
	
	/** 关闭账单
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param billno
	 * @throws SystemException
	 */
	public void closeBill(String tenancyId, Integer storeId, String billno) throws SystemException;
	
	/** 更新桌位状态表
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param tableCode
	 * @throws SystemException
	 */
	public void updateTableState(String tenancyId, Integer storeId, String tableCode) throws SystemException;
	
	/** 查询账单表
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> findBill(String tenancyId, int storeId, String billNum) throws Exception;
	
	/** 查询付款方式表
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param batchNum
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> findBillPayment(String tenancyId, int storeId, String billNum, String batchNum) throws Exception;
	
	/** 查询账单明细表
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param batchNum
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> findBillItem(String tenancyId, int storeId, String billNum, String batchNum) throws Exception;
	
	/** 下单、点菜
	 * 
	 * @param tenancyid
	 * @param billno
	 * @param organId
	 * @param mode
	 * @return
	 * @throws SystemException
	 */
	public Data newOrderDish(String tenancyid, String billno, Integer organId, String mode) throws SystemException;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param reportDate
	 * @param shiftId
	 * @param posNum
	 * @param optNum
	 * @param tableCode
	 * @param type
	 * @param jzid
	 * @param name
	 * @param nameEnglish
	 * @param rate
	 * @param amount
	 * @param count
	 * @param currencyAmount
	 * @param number
	 * @param phone
	 * @param isYsk
	 * @param customerId
	 * @param billCode
	 * @param remark
	 * @param lastUpdatetime
	 * @param paymentState
	 * @param batchNum
	 * @param paramCach
	 * @param moreCoupon
	 * @return
	 * @throws Exception
	 */
	public int insertPosBillPayment(String tenancyId, int storeId, String billNum, Date reportDate, int shiftId, String posNum, String optNum, String tableCode, String type, int jzid, String name, String nameEnglish, double rate, double amount, double count, double currencyAmount, String number,
			String phone, String isYsk, Integer customerId, String billCode, String remark, Timestamp lastUpdatetime, String paymentState, String batchNum, String paramCach, double moreCoupon) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param paymentEntity
	 * @return
	 * @throws Exception
	 */
	public int insertPosBillPayment(String tenancyId, int storeId, PosBillPayment paymentEntity) throws Exception;
}
