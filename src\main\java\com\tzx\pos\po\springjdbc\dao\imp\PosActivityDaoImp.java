package com.tzx.pos.po.springjdbc.dao.imp;

import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.Tools;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.dao.imp.BaseDaoImp;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.base.util.ReqDataUtil;
import com.tzx.pos.bo.dto.PosBill;
import com.tzx.pos.po.springjdbc.dao.PosActivityDao;
import com.tzx.pos.po.springjdbc.dao.PosDishDao;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Repository(PosActivityDao.NAME)
public class PosActivityDaoImp extends BaseDaoImp implements PosActivityDao
{
	private static final Logger logger = Logger.getLogger(PosActivityDaoImp.class);

	@Resource(name =  PosDishDao.NAME)
	private PosDishDao posDishDao;

	@Override
	public List<JSONObject> getValidActivity(String tenancyId, Integer storeId,String chanel)
			throws Exception {
		StringBuffer sb = new StringBuffer();// 组织sql
		sb.append("SELECT ca.id,ca.subject,ca.introduction,ca.start_time,ca.end_time,ca.activity_type,cas.bill_limit_times,cas.if_auto,cas.activity_area,"
				+ "cas.third_bill_code,cas.if_alipay_compresence,cas.apply_date,cas.target_group,cas.member_group_id,cas.organ_type,cas.if_all_day,cas.if_no_limit,cas.if_all_item,cas.if_loop,cas.if_parallel,"
				+ "(SELECT	array_to_string(ARRAY (	SELECT concat (	car.reach_amount,'_',car.discount_amount,'_',COALESCE(car.item_id,0),'_',COALESCE(car.item_num,0),'_',car.id,'_', COALESCE(car.item_unit_id,0))"
				+ "FROM crm_activity_rule car	WHERE	car.activity_id = ca.id order by  car.reach_amount),	',')) AS details,"
				+ "(CASE cas.if_all_day WHEN 1 THEN (SELECT	array_to_string(ARRAY (	SELECT concat (	cadc.begin_date,'_',cadc.end_date)"
				+ "FROM crm_activity_date_configure cadc	WHERE	cadc.activity_id = ca. id	and cadc.activity_type = 'if_all_day'),	','))"
				+ "ELSE	'' END) AS alldaytimes,"
				+ "(CASE cas.if_no_limit	WHEN 1 THEN(SELECT	array_to_string(ARRAY (	SELECT concat (	cadc.begin_date,'_',cadc.end_date)"
				+ "FROM crm_activity_date_configure cadc	WHERE	cadc.activity_id = ca. id	and cadc.activity_type = 'if_no_limit'),','))"
				+ "ELSE  '' END) AS nolimittimes "
				+ "FROM	crm_activity ca,crm_activity_sub_t cas,crm_activity_org cao,crm_activity_chanel cach "
				+ "WHERE cas.activity_id = ca.id and cao.store_id = "+storeId+" and ca.tenancy_id = '"+tenancyId+"' and cach.activity_id = ca.id and cao.activity_id = ca.id and cach.chanel = '"+chanel+"'"
				+ "and ca.valid_state ='1' and ca.running_state ='1' ORDER BY ca.activity_type DESC");// 1为有效的
		List<JSONObject> activityList = this.query4Json(tenancyId,
				sb.toString());// //查出的活动list集合
		logger.info("getValidActivity:success：activityList.size()："+activityList.size());
		return activityList;
	}

	@Override
	public void insertItemDiscountList(String tenancyId,
			Integer storeId, String billNum) throws Exception {
		Timestamp currentTime = DateUtil.currentTimestamp();
		// 查询pos_bill_item表
		String sql = "select rwid,item_id,activity_id,(item_amount - real_amount) as discount_amount,report_date,activity_batch_num,activity_rule_id from pos_bill_item where tenancy_id='"+tenancyId+"' and store_id="+storeId+" and bill_num='"+billNum+"' and activity_id is not null and activity_id <> 0 and item_property<>'SETMEAL'";
		List<JSONObject> itemList = this.query4Json(tenancyId,sql);
		List<Object[]> itemDiscountList = new ArrayList<Object[]>();
		for(JSONObject item : itemList){
			Object[] objs = new Object[]
			{ tenancyId,storeId, DateUtil.parseDate(item.optString("report_date")), billNum, "OPEN",item.optInt("item_id"),item.optInt("rwid"),item.optDouble("discount_amount"),item.optInt("activity_id"),currentTime,item.optString("activity_batch_num"),item.optInt("activity_rule_id"),0};
			itemDiscountList.add(objs);
		}
		String delDiscountSql = "delete from pos_item_discount_list where tenancy_id=? and store_id=? and bill_num=?";
		this.update(delDiscountSql, new Object[]{ tenancyId, storeId, billNum});
		String itemDiscountSql = "insert into pos_item_discount_list(tenancy_id,store_id,report_date,bill_num,bill_status,item_id,rwid,discount_amount,active_id,last_update_time,batch_num,rule_id,upload_tag) " +
				" values (?,?,?,?,?,?,?,?,?,?,?,?,?)";
		this.batchUpdate(itemDiscountSql, itemDiscountList);
		logger.info("insertItemDiscountList:success");
	}

	@Override
	public Double getSumDiscountAmount(String tenancyId,
			Integer storeId, String billNum) throws Exception {
		String sql = "select sum(discount_amount) from pos_item_discount_list where tenancy_id='"+tenancyId+"' and store_id="+storeId+" and bill_num='"+billNum+"'";
		Double sumDiscountamount = this.getDouble(tenancyId, sql);
		logger.info("getSumDiscountAmount:success：sumDiscountamount："+sumDiscountamount);
		return sumDiscountamount;
	}

	@Override
	public void deleteItemDiscountInfo(List<Object[]> objs) throws Exception {
		String sql = "delete from pos_item_discount_list where tenancy_id=? and store_id=? and bill_num=? and active_id=? and batch_num=?";
		this.batchUpdate(sql.toString(), objs);
		logger.info("deleteItemDiscountInfo:success");
	}

	@Override
	public List<JSONObject> getUsedActivityInfo(String tenancyId,
			Integer storeId, String billNum) throws Exception {
		StringBuffer sql = new StringBuffer();
		sql.append("SELECT activity_id,SUM (activity_count) num,actuse.if_parallel FROM	(SELECT ca. ID AS activity_id,ca.activity_type,	pid.activity_count,pid.activity_batch_num,cas.if_parallel FROM crm_activity ca"+
					" RIGHT JOIN (SELECT	activity_id,activity_batch_num,activity_count FROM	pos_bill_item WHERE	tenancy_id = '"+tenancyId+"'	AND store_id = "+storeId+" AND bill_num = '"+billNum+"' AND activity_id IS NOT NULL and activity_id <> 0"+
					" GROUP BY activity_id,activity_batch_num,activity_count) pid ON pid.activity_id = ca. ID "+
					" LEFT JOIN (SELECT activity_id, if_parallel FROM crm_activity_sub_t) cas ON cas.activity_id = ca. ID) actuse GROUP BY actuse.activity_id,if_parallel ");
		List<JSONObject> activityList = this.query4Json(tenancyId,sql.toString());// //查出的使用活动list集合
		logger.info("getUsedActivityInfo:success:activityList "+activityList.toString());
		return activityList;
	}

	@Override
	public void deleteActivityGift(String tenancyId,
			Integer storeId, String billNum, String batchNum) throws Exception {
		String sql = "delete from pos_bill_item WHERE iTem_remark = 'HDZC08' and tenancy_id = ? AND store_id = ? AND bill_num = ? and activity_batch_num = ?";
		this.update(sql, new Object[]
				{ tenancyId, storeId, billNum, batchNum });
		logger.info("deleteActivityGift:success");
	}

	@Override
	public List<?> getcancelActivityItemInfo(String tenancyId,
			Integer storeId, String billNum) throws Exception {
		String sql = "SELECT pbi.item_id,pbi.item_price,pbi.item_serial,pbi.item_count,pbi.item_class_id,hic.father_id as item_fater_id FROM pos_bill_item pbi,hq_item_class hic where pbi.item_class_id = hic.id and pbi.activity_id is null and pbi.tenancy_id='"+tenancyId+"' and pbi.store_id='"+storeId+"' and pbi.bill_num='"+billNum+"'";
		List<Map<String, Object>> listMap = this.jdbcTemplate.queryForList(sql);
		//List<JSONObject> itemList = this.query4Json(tenancyId,sql);// //查出的菜品list集合
		logger.info("getNoActivityItem:success：itemList："+listMap.toString());
		return listMap;
	}

	@Override
	public List<JSONObject> getActivityItemRules(String tenancyId,Integer activityId
			) throws Exception {
		String itemSql ="select DISTINCT(cai.rule_code),cai.item_type,cai.activity_id,cai.item_num,cai.item_unit_id,(SELECT "
				+"array_to_string(ARRAY (SELECT concat(car.item_type,'_',car.item_id,'_',car.item_unit_id,'_',car.rule_code) FROM crm_activity_item car "
				+"WHERE car.rule_code = cai.rule_code and car.activity_id = cai.activity_id),',')) AS itemrules from crm_activity_item cai where cai.activity_id ='"+activityId+"'";
		List<JSONObject> activityItemList = this.query4Json(tenancyId,itemSql);// //查出的商品限制list集合
		logger.info("getActivityItemRules:success:activityItemList"+activityItemList.toString());
		return activityItemList;
	}

	@Override
	public List<JSONObject> getusedActivityItemRules(String tenancyId,Integer storeId,
			String billNum) throws Exception {
		String sql = "SELECT car.reach_amount,car.discount_amount,COALESCE(car.item_id,0) as item_id,COALESCE(car.item_id,0) as item_num,pbic.activity_id,pbic.activity_batch_num,pbic.activity_rule_id,"
				     +"COALESCE(pbic.activity_count,1) as activity_count,pbic.item_all_amount,cas.activity_type,COALESCE(pobi.item_amount,0) as hdzc_item_amount,COALESCE(pobi1.item_amount,0) as hdbj_item_amount,COALESCE (pobi2.item_amount, 0) AS hdtyj_item_amount "
				     +" FROM crm_activity_rule car RIGHT JOIN (SELECT pbi.activity_id,pbi.activity_batch_num,pbi.activity_rule_id,"
				     +"pbi.activity_count,sum((pbi.item_price + coalesce (zi.method_amount, 0) + coalesce (gd.makeup_money, 0)) * pbi.item_count) as item_all_amount FROM pos_bill_item pbi "
                     +" left join (select gd.*, cd. id as details_id from hq_item_group_details gd left join hq_item_combo_details cd on gd.item_group_id = cd.details_id and cd.is_itemgroup = 'Y') gd on pbi.assist_item_id = gd.details_id and gd.item_id = pbi.item_id and gd.item_unit_id = pbi.item_unit_id left join (select bill_num, rwid, coalesce (sum(amount), 0) as method_amount from pos_zfkw_item group by bill_num, rwid ) zi on pbi.bill_num = zi.bill_num and pbi.rwid = zi.rwid "
                     +"WHERE pbi.tenancy_id = '"+tenancyId+"' AND pbi.store_id = "+storeId+"	AND pbi.bill_num = '"+billNum+"' and pbi.activity_id is not null and pbi.activity_id <> 0   and pbi.item_property <>'MEALLIST' GROUP BY "
				     +"pbi.activity_batch_num,pbi.activity_id,pbi.activity_rule_id,pbi.activity_count) pbic ON car.activity_id = pbic.activity_id AND car. ID = pbic.activity_rule_id "
				     +"LEFT JOIN (SELECT ca. ID,ca.activity_type,ca.subject	FROM crm_activity ca) cas ON cas. ID = car.activity_id "
					 +"LEFT JOIN(SELECT pob.activity_id,sum(pob.item_price*pob.item_count) as item_amount,pob.activity_batch_num from pos_bill_item pob where pob.item_remark='HDZC08'	and pob.tenancy_id = '"+tenancyId+"' AND pob.store_id = "+storeId
					 +" AND pob.bill_num = '"+billNum+"' GROUP BY pob.activity_id,pob.activity_batch_num) pobi on pobi.activity_id = cas. ID and pobi.activity_batch_num = pbic.activity_batch_num "
					 +"LEFT JOIN(SELECT pob.activity_id,sum(pob.item_price) as item_amount,pob.activity_batch_num from pos_bill_item pob where pob.item_remark='HDBJ09'	and pob.tenancy_id = '"+tenancyId+"' AND pob.store_id = "+storeId
					 +" AND pob.bill_num = '"+billNum+"' GROUP BY pob.activity_id, pob.activity_batch_num) pobi1 on pobi1.activity_id = cas. ID and pobi1.activity_batch_num = pbic.activity_batch_num "
					 +"LEFT JOIN (SELECT pob.activity_id,sum (pob.item_price) as item_amount,pob.activity_batch_num	FROM pos_bill_item pob WHERE pob.item_remark = 'HDTYJ10' AND pob.tenancy_id = '"+tenancyId+"' AND pob.store_id = "+storeId
					 +" AND pob.bill_num = '"+billNum+"' GROUP BY pob.activity_id,pob.activity_batch_num) pobi2 ON pobi2.activity_id = cas. ID AND pobi2.activity_batch_num = pbic.activity_batch_num";
		List<JSONObject> activityItemList = this.query4Json(tenancyId,sql);// //查出参加活动的相关信息集合
		return activityItemList;
	}

	/**
	 * 账单查询
	 *
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public List<PosBill> newFindPosBill(Data param, Data result) throws Exception {

		String tenantId = param.getTenancy_id();
		Integer organId = param.getStore_id();
		Data data = new Data();
		Map<String, Object> map = ReqDataUtil.getDataMap(param);

		Date reportDate = ParamUtil.getDateValue(map, "report_date", false, null);

		String pos_num = ParamUtil.getStringValue(map, "pos_num", false, null);
		// String opt_num = ParamUtil.getStringValue(map, "opt_num", false,
		// null);

		String tableCode = ParamUtil.getStringValue(map, "table_code", false, null);

		String billno = ParamUtil.getStringValue(map, "bill_num", false, null);

		String isChanel = ParamUtil.getStringValue(map, "is_check_chanel", false, null);

		String mode = ParamUtil.getStringValue(map, "mode", true, PosErrorCode.NOT_NULL_MODE);

		//
		String isDinner = ParamUtil.getStringValue(map, "isdinner", false, null);

		//操作员
		String operator = ParamUtil.getStringValue(map, "operator", false, null);

		//取餐号
		String take_meal_num = ParamUtil.getStringValue(map, "take_meal_num", false, null);

		// 是否查询账单明细
		String isBillList = ParamUtil.getStringValue(map, "isbilllist", false, null);

		String table_property_id = ParamUtil.getStringValue(map, "table_property_id", false, null);
		//
		String isAll = "";
		if (Tools.isNullOrEmpty(map.get("getalldata")) == false) {
			isAll = map.get("getalldata").toString();
		} else {
			isAll = "N";
		}

//		Pagination pagination = param.getPagination();
//
//		int limit = 10;
//		int pageNo = 1;
//		String orderBy = null;
//		String ascDesc = "asc";
//		int totalCount = 0;
//		if (pagination != null && pagination.getPagesize() > 0) {
//			limit = pagination.getPagesize();
//			if ((pagination.getPageno() - 1) > 0) {
//				pageNo = pagination.getPageno();
//			}
//
//			orderBy = pagination.getOrderby();
//
//			if (pagination.isAsc() == false) {
//				ascDesc = "desc";
//			}
//		}

		StringBuffer sql = new StringBuffer();
		sql.append(" select pb.bill_taste,pb.order_num,pb.fictitious_table, pb.table_code,(select user_name from user_authority where id = to_number(open_opt,'99G999D9S')) as user_name,pb.bill_num,pb.batch_num,pb.serial_num,pb.report_date,pb.shift_id,pb.opentable_time,pb.open_pos_num,pb.open_opt,pb.payment_time,pb.pos_num,pb.cashier_num,pb.waiter_num,round(pb.difference, 2) as difference,");
		sql.append(" pb.service_id,hsft.name as service_name,round(pb.service_amount, 2) as service_amount,round(pb.subtotal, 2) as subtotal,round(pb.payment_amount, 2) as payment_amount,round(pb.discountk_amount, 2) as discountk_amount,");
		sql.append(" round(pb.discountr_amount, 2) as discountr_amount,round(pb.discount_amount, 2) as discount_amount,round(pb.maling_amount, 2) as maling_amount,round(pb.average_amount, 2) as average_amount,round(pb.givi_amount, 2) as givi_amount,");
		sql.append(" pb.discount_rate,pb.discount_case_id,hdc.discount_case_name, pb.discount_mode_id,pb.guest,pb.guest_msg,pb.print_count,pb.bill_state,pb.bill_property,pb.remark,pb.payment_state,pb.copy_bill_num,pb.source as chanel");
		sql.append(" ,(select round(coalesce(sum(bi.item_amount),0),2) from pos_bill_item bi where bi.tenancy_id=pb.tenancy_id and bi.store_id=pb.store_id and bi.bill_num=pb.bill_num and (bi.item_remark<>'" + SysDictionary.ITEM_REMARK_FS02 + "' or bi.item_remark is null) and bi.item_property<>'" + SysDictionary.ITEM_PROPERTY_MEALLIST + "') as subtotal2");
		sql.append(" ,(select round(coalesce(sum(bi.item_amount),0),2) from pos_bill_item bi where bi.tenancy_id=pb.tenancy_id and bi.store_id=pb.store_id and bi.bill_num=pb.bill_num and bi.discount_state='N' and bi.item_property<>'" + SysDictionary.ITEM_PROPERTY_MEALLIST + "') as none_discount_amount");
		sql.append(" from pos_bill as pb left join hq_service_fee_type as hsft on hsft.id = pb.service_id left join hq_discount_case as hdc on pb.discount_case_id = hdc.id");
		sql.append(" left join tables_info ti on pb.table_code=ti.table_code");
		sql.append(" where pb.store_id=?");

		if ("Y".equals(isChanel)) {
			try {
				String checkChanel = posDishDao.getSysParameter(tenantId, organId, "BYXCZZDLYQD");

				StringBuilder chanel = new StringBuilder();
				if (null != checkChanel && checkChanel.length() > 0) {
					String[] chanels = checkChanel.split(",");
					for (String str : chanels) {

						if (chanel.length() > 0) {
							chanel.append(",");
						}
						chanel.append("'").append(str).append("'");
					}
				}
				if (chanel.length() > 0) {
					sql.append(" and pb.source not in (").append(chanel.toString()).append(")");
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}

		String billType = null;
		switch (mode) {
			case "0":
				billType = "'OPEN'";
				break;
			case "1":
				billType = "'CLOSED'";
				break;
			case "2":
				billType = "'SORT','OPEN'";
				break;
			case "3":
				break;
			default:
				logger.info("mode的值不为0或1或2或3，传入的mode值为：" + mode);
				throw new SystemException(PosErrorCode.MODE_NOT_MATCH_ERROR);
		}

		List<PosBill> list = new ArrayList<PosBill>();

		if (isDinner.equalsIgnoreCase("Y")) {
			if (isAll.equalsIgnoreCase("Y")) {
				sql.append(" and pb.report_date='").append(DateUtil.format(reportDate)).append("' and pb.bill_property in (").append(billType).append(")");
			} else {
				if ("3".equals(mode)) {
					String batchNum = "";
					if (StringUtils.isNotEmpty(tableCode)) {
						StringBuffer sqlState = new StringBuffer("select bill_batch_num from pos_tablestate where table_code = ? and store_id = ?");
						SqlRowSet tableInfoRs = posDishDao.query4SqlRowSet(sqlState.toString(), new Object[]
								{tableCode, organId});

						if (tableInfoRs.next()) {
							batchNum = tableInfoRs.getString("bill_batch_num");
						}
					}

					sql.append(" and pb.fictitious_table='").append(tableCode).append("' and pb.batch_num='").append(batchNum).append("' ");
				} else {
					sql.append(" and pb.bill_property in (").append(billType).append(")");

					if (StringUtils.isNotEmpty(billno)) {
						sql.append(" and pb.bill_num='").append(billno).append("' ");
					}
					if (StringUtils.isNotEmpty(operator)) {//操作员
						sql.append(" and pb.open_opt = '").append(operator).append("' ");
					}
					if (StringUtils.isNotEmpty(take_meal_num)) {//餐牌号
						sql.append(" and upper(pb.table_code) like '%").append(take_meal_num.toUpperCase()).append("%' ");
					} else if (StringUtils.isNotEmpty(tableCode)) {
						sql.append(" and pb.fictitious_table='").append(tableCode).append("' ");
						if ("2".equals(mode)) {
							sql.append(" and (open_pos_num = '").append(pos_num).append("'").append(" or pos_num = '").append(pos_num).append("')");
						}
					} else {
						throw new SystemException(PosErrorCode.PARAM_ERROR);
					}
				}
			}
		} else {
			sql.append(" and pb.bill_property in (").append(billType).append(")");
			sql.append(" and (open_pos_num = '").append(pos_num).append("' or open_pos_num in (select devices_code from hq_devices where show_type in('YD','PAD') and valid_state='1')");
			sql.append(" or pos_num = '").append(pos_num).append("' or pos_num in (select devices_code from hq_devices where show_type in('YD','PAD') and valid_state='1'))");

			if (StringUtils.isNotEmpty(billno)) {
				sql.append(" and pb.bill_num='").append(billno).append("' ");
			}
			if (StringUtils.isNotEmpty(operator)) {//操作员
				sql.append(" and pb.open_opt = '").append(operator).append("' ");
			}
			if (StringUtils.isNotEmpty(take_meal_num)) {//餐牌号
				sql.append(" and upper(pb.table_code) like '%").append(take_meal_num.toUpperCase()).append("%' ");
			} else if (StringUtils.isNotEmpty(tableCode)) {
				sql.append(" and pb.fictitious_table='").append(tableCode).append("' ");
			}
		}

		if (Tools.hv(table_property_id) && !"0".equals(table_property_id)) {
			sql.append(" and ti.table_property_id='").append(table_property_id).append("' ");
		}

//		totalCount = posDishDao.queryForInt("select count(1) as count from (" + sql.toString() + ") t", new Object[]
//				{organId});

//		if (pagination != null && pagination.getPagesize() > 0) {
//			if (StringUtils.isEmpty(pagination.getOrderby())) {
//				sql.append(" order by pb.bill_num desc ");
//			} else {
//				sql.append(" order by " + orderBy + " " + ascDesc);
//			}
//
//			sql.append(" limit " + limit + " offset " + (pageNo - 1) * pagination.getPagesize());
//		}

		try {
			list = (List<PosBill>) posDishDao.query(sql.toString(), new Object[]
					{organId}, BeanPropertyRowMapper.newInstance(PosBill.class));
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("账单查询：" + ExceptionMessage.getExceptionMessage(e));
			throw SystemException.getInstance(PosErrorCode.OPER_ERROR);
		}

		List<PosBill> billList = new ArrayList<PosBill>();
		if ("N".equals(isBillList)) {
			billList.addAll(list);
		} else {
			billList = posDishDao.findPosBillItem(tenantId, organId, list);
		}

//		pagination.setPageno(pagination.getPageno() > 0 ? pagination.getPageno() : 1);
//		pagination.setTotalcount(totalCount);
//
//		if (pagination.getPagesize() == 0) {
//			pagination.setPagesize(billList.size());
//		}
//		result.setPagination(pagination);

//		if (billList != null && billList.size() != 0) {
//			data.setData(billList);
//		}
		return billList;
	}

	/**
	 * 根据菜品折扣明细表pos_item_discount_list更新pos_bill_item数据
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param activityBatchNum
	 * @throws Exception
	 */
	public void updatePosBillItem(String tenancyId, Integer storeId, String billNum, String activityBatchNum) throws Exception {
		StringBuffer stringBuffer = new StringBuffer();
		//查询pos_item_discount_list
		stringBuffer.append("select item_id,rwid,active_id,discount_amount,batch_num,rule_id from pos_item_discount_list")
					.append(" where tenancy_id = '" + tenancyId + "' and store_id = " + storeId + " and bill_num = '" + billNum + "' ")
					.append(" and batch_num = '" + activityBatchNum + "'");
		List<JSONObject> itemList = this.query4Json(tenancyId,stringBuffer.toString());
		List<Object[]> itemDiscountList = new ArrayList<Object[]>();
		for(JSONObject item : itemList){
				Object[] objs = new Object[]
						{item.optDouble("discount_amount"), item.optInt("active_id"), item.optString("batch_num"),
								item.optInt("rule_id"), tenancyId, storeId, billNum};
				itemDiscountList.add(objs);
		}
		StringBuffer updateItemSql = new StringBuffer();
		updateItemSql.append("update pos_bill_item set discountr_amount = ?, activity_id = ?, activity_batch_num = ?,")
					.append(" activity_rule_id = ?, activity_count = 1 where tenancy_id = ? and store_id = ? and bill_num = ? ")
					.append(" and item_remark = '" + SysDictionary.ITEM_REMARK_HDZC08 + "' and activity_id = 0 ");
		this.batchUpdate(updateItemSql.toString(), itemDiscountList);
		logger.info("updatePosBillItem:success");
	}

}