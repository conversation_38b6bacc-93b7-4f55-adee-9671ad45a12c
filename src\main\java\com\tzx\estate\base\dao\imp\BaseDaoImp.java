package com.tzx.estate.base.dao.imp;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import net.sf.json.JSONObject;

import org.apache.log4j.Logger;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.support.rowset.SqlRowSet;

import com.tzx.estate.base.dao.BaseDao;
import com.tzx.framework.common.util.JsonUtils;
import com.tzx.framework.common.util.dao.impl.GenericDaoImpl;

/**
 * 
 * <AUTHOR> 
 * @日期：2015年5月26日-下午5:34:43
 */

public class BaseDaoImp extends GenericDaoImpl implements BaseDao
{
	private static final Logger logger = Logger.getLogger(BaseDaoImp.class);
	
	public SqlRowSet query4SqlRowSet(String sql, Object[] obj)
	{
		try
		{
			SqlRowSet rs = this.jdbcTemplate.queryForRowSet(sql,obj);
			return rs;
		}
		catch (DataAccessException e)
		{
			logger.error("查询错误",e);
			e.printStackTrace();
			return null;
		}
	}
	
	public int queryForInt(String sql, Object[] obj)
	{
		try
		{
			return this.jdbcTemplate.queryForObject(sql, Integer.class, obj);
		}
		catch (DataAccessException e)
		{
			logger.error("查询错误",e);
			e.printStackTrace();
			return 0;
		}
	}
	
	public int update(String sql, Object[] obj)
	{
		try
		{
			return this.jdbcTemplate.update(sql,obj);
		}
		catch (DataAccessException e)
		{
			logger.error("执行Sql语句错误",e);
			e.printStackTrace();
			return 0;
		}
	}
	
	public int[] batchUpdate(String sql,List<Object[]> batchArgs) throws Exception
	{
		return this.jdbcTemplate.batchUpdate(sql, batchArgs);
	}
	
	/**
	 * 加这个方法的原是：两次转JSONObject，后台没有错，返回给andriod或delphi那边是404
	 * @param tenantId
	 * @param sql
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> queryString4Json(String tenantId, String sql) throws Exception
	{
		List<JSONObject> list = new ArrayList<JSONObject>();

		List<Map<String, Object>> listMap = this.jdbcTemplate.queryForList(sql);
		
		List<Map<String,String>> listStrMap = new ArrayList<Map<String,String>>();
		
		for (Map<String, Object> map : listMap)
		{
			Map<String,String> strMap = new HashMap<String,String>();
			for (Map.Entry<String, Object> entry : map.entrySet()) {
				if (entry.getValue() == null || entry.getValue() == "" ) 
				{
					strMap.put(entry.getKey(), "");
				}
				else 
				{
					strMap.put(entry.getKey(),"" + entry.getValue());
				}
				
	        }  
			
			listStrMap.add(strMap);
		}
		for (Map<String, String> map : listStrMap) 
		{
			JSONObject json = JSONObject.fromObject(map, JsonUtils.gxhkJsonConfig);
			list.add(json);
		}
		
		return list;
	}
	
	/**
	 * 加这个方法的原是：两次转JSONObject，后台没有错，返回给andriod或delphi那边是404
	 * @param tenantId
	 * @param sql
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> queryString4Json(String tenantId, String sql, Object[] objs) throws Exception
	{
		List<JSONObject> list = new ArrayList<JSONObject>();

		List<Map<String, Object>> listMap = this.jdbcTemplate.queryForList(sql,objs);
		
		List<Map<String,String>> listStrMap = new ArrayList<Map<String,String>>();
		
		for (Map<String, Object> map : listMap)
		{
			Map<String,String> strMap = new HashMap<String,String>();
			for (Map.Entry<String, Object> entry : map.entrySet()) {
				if (entry.getValue() == null || entry.getValue() == "" ) 
				{
					strMap.put(entry.getKey(), "");
				}
				else 
				{
					strMap.put(entry.getKey(),"" + entry.getValue());
				}
	        }  
			
			listStrMap.add(strMap);
		}
		for (Map<String, String> map : listStrMap) 
		{
			JSONObject json = JSONObject.fromObject(map, JsonUtils.gxhkJsonConfig);
			list.add(json);
		}
		
		return list;
	}
	

	
	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public List<?> query(String sql, Object[] obj, BeanPropertyRowMapper className)
	{
		try
		{
			return this.jdbcTemplate.query(sql, obj, className);
		}
		catch (DataAccessException e)
		{
			logger.error("执行SQL语句错误",e);
			e.printStackTrace();
			return null;
		}
	}

	@Override
	public SqlRowSet query4SqlRowSet(String sql)
	{
		try
		{
			SqlRowSet rs = this.jdbcTemplate.queryForRowSet(sql);
			return rs;
		}
		catch (DataAccessException e)
		{
			logger.info("查询错误",e);
			e.printStackTrace();
			return null;
		}
	}
}