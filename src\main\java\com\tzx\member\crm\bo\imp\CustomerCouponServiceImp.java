package com.tzx.member.crm.bo.imp;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.springframework.stereotype.Service;

import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.DoubleHelper;
import com.tzx.framework.common.util.Tools;
import com.tzx.member.crm.bo.CustomerCouponService;
import com.tzx.member.crm.po.springjdbc.dao.CustomerDao;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.base.util.ReqDataUtil;
import com.tzx.pos.po.springjdbc.dao.PosPaymentDao;

@Service(CustomerCouponService.NAME)
public class CustomerCouponServiceImp extends CustomerServiceImp implements CustomerCouponService
{
	
	private final int	COUPONS_CODE_NOTEXIST_ERROR	= 5070;
	
	@Override
	public void customerCouponConsume(String tenancyId, int storeId, JSONObject param, Data resData, String operType) throws Exception
	{
		List<JSONObject> requestList = new ArrayList<JSONObject>();
		requestList.add(param);

		Data requestData = Data.get(tenancyId, storeId, 0);
		requestData.setType(Type.NEW_COUPONS);
		requestData.setOper(Oper.update);
		requestData.setData(requestList);

		this.commonPost(JSONObject.fromObject(requestData).toString(), resData);
	}

	@Override
	public Data queryCustomerCouponConsume(String tenancyId, int storeId, JSONObject paramJson) throws Exception
	{
		List<JSONObject> requestList = new ArrayList<JSONObject>();
		requestList.add(paramJson);

		Data requestData = Data.get(tenancyId, storeId, 0);
		requestData.setType(Type.COUPONS);
		requestData.setOper(Oper.check);
		requestData.setData(requestList);

		Data responseData = Data.get(requestData);
		this.commonPost(JSONObject.fromObject(requestData).toString(), responseData);
		return responseData;
	}

	@Override
	public void customerCouponCancelConsume(String tenancyId, int storeId, JSONObject param, Data resData, String operType) throws Exception
	{
		List<JSONObject> dataList = new ArrayList<JSONObject>();
		dataList.add(param);

		Data paramData = Data.get(tenancyId,storeId,Constant.CODE_SUCCESS);
		paramData.setType(Type.COUPONS);
		paramData.setOper(Oper.init);
		paramData.setData(dataList);

		this.commonPost(JSONObject.fromObject(paramData).toString(), resData);
		
		if(COUPONS_CODE_NOTEXIST_ERROR == resData.getCode())
		{
			resData.setCode(Constant.CODE_SUCCESS);
			resData.setSuccess(true);
		}
	}

	@Override
	public Data queryCustomerCouponCancelConsume(String tenancyId, int storeId, JSONObject paramJson) throws Exception
	{
		// TODO Auto-generated method stub
		return null;
	}

	@Resource(name = PosPaymentDao.NAME)
	private PosPaymentDao				paymentDao;

	@Resource(name = CustomerDao.NAME)
	protected CustomerDao				customerDao;
	
	@SuppressWarnings("unchecked")
	@Override
	public void checkCustomerCoupon(Data param, Data resData) throws Exception
	{
		String tenantId = param.getTenancy_id();
		int storeId = param.getStore_id();
		final int scale = 2;
		try
		{
			JSONObject dataJson = JSONObject.fromObject(param.getData().get(0));
			String reportDate = ParamUtil.getDateStringValue(dataJson, "report_date");
			String optNum = ParamUtil.getStringValueByObject(dataJson, "opt_num");
			String chanel = ParamUtil.getStringValueByObject(dataJson, "chanel");
			String billNum = ParamUtil.getStringValueByObject(dataJson, "bill_code", true, PosErrorCode.NOT_NULL_BILL_NUM);

			String optName = customerDao.getEmpNameById(optNum, tenantId, storeId);

			JSONObject billJson = customerDao.getPosBillByBillnum(tenantId, storeId, billNum);
			String batchNum = null;
			Double discountAmount = 0d;
			Double paymentAmount = 0d;
			if (null != billJson && false == billJson.isEmpty())
			{
				batchNum = ParamUtil.getStringValueByObject(billJson, "batch_num");
				discountAmount = ParamUtil.getDoubleValueByObject(billJson, "discountk_amount");
				paymentAmount = ParamUtil.getDoubleValueByObject(billJson, "payment_amount");
			}

			JSONArray coupons = dataJson.getJSONArray("couponslist");
			List<String> couponCodeList = new ArrayList<String>();
			for (Object coupon : coupons)
			{
				JSONObject jo = JSONObject.fromObject(coupon);
				couponCodeList.add(jo.optString("coupons_code"));
			}

			// 获取已支付优惠券信息
			List<JSONObject> paymentCouponList = paymentDao.getBillPaymentForCoupon(tenantId, storeId, billNum, batchNum);
			if (couponCodeList.size() > 0)
			{
				for (JSONObject couponCode : paymentCouponList)
				{
					String couponsCode = couponCode.optString("number");
					if (Tools.hv(couponsCode))
					{
						if (couponCodeList.contains(couponsCode))
						{
							throw SystemException.getInstance(PosErrorCode.COUPON_DISABLED_ERROR).set("{0}", couponsCode);
						}

//						JSONObject jo = new JSONObject();
//						jo.put("coupons_code", couponsCode);
//						coupons.add(jo);
					}
					else
					{
						throw SystemException.getInstance(PosErrorCode.COUPON_AND_NOCODE_NOT_PERMIT_SHARE_ERROR);
					}
				}
			}

			// 获取账单明细
			List<JSONObject> billDetailsList = paymentDao.getBillItemForCoupon(tenantId, storeId, billNum, batchNum);

			// 组织验券请求体
			JSONObject reqJson = new JSONObject();
			reqJson.put("bill_code", billNum);
			reqJson.put("batch_num", batchNum);
			reqJson.put("chanel", chanel);
			reqJson.put("bill_money", paymentAmount);
			reqJson.put("discount_amount", discountAmount);
			reqJson.put("couponslist", coupons);
			reqJson.put("billdetails", billDetailsList);
			reqJson.put("business_date", reportDate);
			reqJson.put("operator", optName);
			reqJson.put("operator_id", optNum);
			reqJson.put("updatetime", DateUtil.getNowDateYYDDMMHHMMSS());

			List<JSONObject> dataList = new ArrayList<JSONObject>();
			dataList.add(reqJson);
			param.setData(dataList);

			// 券验证
			Data cData = new Data();
			String reqParam = JSONObject.fromObject(param).toString();
			this.commonPost(reqParam, cData);

			if (Constant.CODE_SUCCESS != cData.getCode())
			{
				resData.setCode(cData.getCode());
				resData.setMsg(cData.getMsg());
				return;
			}

			Map<String, Object> rsMap = ReqDataUtil.getDataMap(cData);

			List<Map<String, Object>> couponsdetails = (List<Map<String, Object>>) rsMap.get("couponsdetails");
			if (couponsdetails.size() == 0)
			{
				throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
			}

			Double dbFaceVal = 0d;
			List<JSONObject> couponsList = new ArrayList<JSONObject>();
			for (int i = 0; i < couponsdetails.size(); i++)
			{
				Map<String, Object> cp = couponsdetails.get(i);
				String couponsCode = ParamUtil.getStringValue(cp, "coupons_code", false, null);

				if (couponCodeList.contains(couponsCode))
				{
					String couponsPro = ParamUtil.getStringValue(cp, "coupons_pro", false, null);
					Double faceValue = ParamUtil.getDoubleValue(cp, "face_value", false, null);
					Integer itemUnitId = ParamUtil.getIntegerValue(cp, "item_unit_id", false, null);
					Double discount = ParamUtil.getDoubleValue(cp, "discount", false, null);
					Double incomeAmount = ParamUtil.getDoubleValue(cp, "income_amount", false, null);
					String classId = ParamUtil.getStringValue(cp, "class_id", false, null);
					String className = ParamUtil.getStringValue(cp, "class_name", false, null);
					String typeId = ParamUtil.getStringValue(cp, "type_id", false, null);
					String isTotal = ParamUtil.getStringValue(cp, "is_total", false, null);

					String itemRwid = null;
					Double couponsAmount = faceValue;
					// 判断菜品券
					if (SysDictionary.COUPONS_PRO_DISH.equals(couponsPro) && null != itemUnitId)
					{
						for (JSONObject item : billDetailsList)
						{
							// 根据菜品规格ID,和抵扣金额获取抵扣菜品的RWID
							Double realAmount = DoubleHelper.div(item.optDouble("real_amount"), item.optDouble("item_count"), scale);
							if (itemUnitId.equals(item.optInt("item_unit_id")) && discount.equals(realAmount))
							{
								itemRwid = item.optString("rwid");
								// 计算抵扣面值
								couponsAmount = DoubleHelper.div(item.optDouble("item_amount"), item.optDouble("item_count"), scale);
								if (SysDictionary.COUPON_TOTAL_MODE_PART.equals(isTotal) && faceValue < couponsAmount)
								{
									couponsAmount = faceValue;
								}
								break;
							}
						}
					}

					dbFaceVal = DoubleHelper.add(dbFaceVal, discount, 4);

					JSONObject couponsJson = new JSONObject();
					couponsJson.put("number", couponsCode);
					couponsJson.put("count", 1d);
					couponsJson.put("amount", discount);
					couponsJson.put("face_value", couponsAmount);
					couponsJson.put("coupons_pro", couponsPro);
					couponsJson.put("item_rwid", (null != itemRwid ? itemRwid : "0"));
					couponsJson.put("income_amount", incomeAmount);
					couponsJson.put("class_id", (null != classId ? classId : "0"));
					couponsJson.put("class_name", (null != className ? className : ""));
					couponsJson.put("type_id", (null != typeId ? typeId : "0"));
					couponsJson.put("currency_amount", discount);
					couponsJson.put("remark", couponsPro);
					couponsList.add(couponsJson);
				}
			}

			Map<String, Object> rstMap = new HashMap<String, Object>();
			rstMap.put("face_value", dbFaceVal);
			rstMap.put("couponslist", couponsList);

			List<Object> rstList = new ArrayList<Object>();
			rstList.add(rstMap);

			resData.setData(rstList);
			resData.setCode(Constant.CODE_SUCCESS);
			resData.setMsg(Constant.QUERY_ITEM_DETAILS_SUCCESS);
		}
		catch (SystemException se)
		{
			throw se;
		}
		catch (Exception e)
		{
			e.printStackTrace();
			resData.setCode(Constant.CODE_NULL_DATASET);
			resData.setMsg(Constant.QUERY_ITEM_DETAILS_FAILURE);
		}
	}

}
