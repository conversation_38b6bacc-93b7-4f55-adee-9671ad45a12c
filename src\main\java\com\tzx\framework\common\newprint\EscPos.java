package com.tzx.framework.common.newprint;

import java.awt.image.BufferedImage;
import java.io.BufferedInputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.net.Socket;
import java.util.HashMap;
import java.util.Map;

import sun.misc.BASE64Encoder;

/**
 * 控制打印机工具类
 */
public class EscPos  extends Thread {
	public static final byte HT = 0x9;  
    public static final byte LF = 0x0A;  
    public static final byte CR = 0x0D;  
    public static final byte ESC = 0x1B;  
    public static final byte DLE = 0x10;  
    public static final byte GS = 0x1D;  
    public static final byte FS = 0x1C;  
    public static final byte STX = 0x02;  
    public static final byte US = 0x1F;  
    public static final byte CAN = 0x18;  
    public static final byte CLR = 0x0C;  
    public static final byte EOT = 0x04; 
    public static final byte[] ESC_INIT = new byte[] {ESC, '@'}; 
	public static final byte[] PRINT_STATE_DLE_EOT = new byte[] {DLE, EOT,0x01};
	public static final byte[] POS_CUT_MOVE = new byte[]{GS, 'V', 0x42}; 
    public static final byte[] POS_CUT_MODE_FULL = new byte[]{GS, 'V', 0x00};  
	public static final byte[] POS_CUT_MODE_PARTIAL = new byte[]{GS, 'V', 0x01}; 

	public static String encoding = null;
    
    byte[] byte_src = new byte[4];
    // 通过socket流进行读写
    private OutputStream socketOut = null;
    private OutputStreamWriter writer = null;
    private BufferedInputStream bis;
    // 以ip作为key，EscPos实例作为value的Map
    private static Map<String, EscPos> posMap = new HashMap<String, EscPos>();
    private static EscPos escPos = null;

    /**
     * 根据ip、端口、字符编码构造工具类实例
     *
     * @param ip          打印机ip
     * @param port        打印机端口，默认9100
     * @param encoding    打印机支持的编码格式(主要针对中文)
     * @throws IOException
     */
    @SuppressWarnings("static-access")
	public EscPos(String ip, int port, String encoding) throws IOException {
        @SuppressWarnings("resource")
		Socket socket = new Socket(ip, port);
        socketOut = socket.getOutputStream();
        socket.isClosed();
        this.encoding = encoding;
        writer = new OutputStreamWriter(socketOut, encoding);
        bis = new BufferedInputStream(socket.getInputStream()); 
    }

    public synchronized static EscPos getInstance(String ip, Integer port, String encoding) throws IOException {
        escPos = posMap.get(ip);
        if (escPos == null) {
            escPos = new EscPos(ip, port, encoding);
        }
        return escPos;
    }

    public synchronized static EscPos getInstance(String ip, Integer port) throws IOException {
        return getInstance(ip, port, Constant.DEFAULT_ENCODING);
    }

    public static synchronized EscPos getInstance(String ip) throws IOException {
        return getInstance(ip, Constant.DEFAULT_PORT, Constant.DEFAULT_ENCODING);
    }
    
    /**
     * 换行
     *
     * @param lineNum 换行数，0为不换行
     * @return
     * @throws IOException
     */
    private EscPos line(int lineNum) throws IOException {
        for (int i=0; i<lineNum; i++) {
            writer.write("\n");
            writer.flush();
        }
        return this;
    }
    
    public void nextLine(int lineNum) throws IOException {
        for (int i=0; i<lineNum; i++) {
            writer.write("\n");
            writer.flush();
        }
       // return this;
    }


    /**
     * 初始化打印机
     *
     * @return
     * @throws IOException
     */
    @SuppressWarnings("unused")
	private EscPos init() throws IOException {
        writer.write(0x1B);
        writer.write(0x40);
        return this;
    }

    /**
     * 进纸并全部切割
     *
     * @return
     * @throws IOException
     */
    private EscPos feedAndCut() throws IOException {
        writer.write(0x1D);
        writer.write(86);
        writer.write(65);
        writer.write(0);
        writer.flush();
        return this;
    }

    public EscPos printImage(BufferedImage bi) throws IOException {
    	//int i2 = 0;
    	write(ESC_INIT);  
    	check();
 
    	BufferedImage image= bi;  
        byte[] data = new byte[] { 0x1B, 0x33, 0x00 };
        write(data);
        data[0] = (byte)0x00;
        data[1] = (byte)0x00;
        data[2] = (byte)0x00;    //重置参数

        int pixelColor;

        // ESC * m nL nH 点阵图
        byte[] escBmp = new byte[] { 0x1B, 0x2A, 0x00, 0x00, 0x00 };

        escBmp[2] = (byte)0x21;

        //nL, nH
        escBmp[3] = (byte)(image.getWidth() % 256);
        escBmp[4] = (byte)(image.getWidth() / 256);

        // 每行进行打印
        for (int i = 0; i < image.getHeight()  / 24 + 1; i++){
            write(escBmp);

            for (int j = 0; j < image.getWidth(); j++){
                for (int k = 0; k < 24; k++){
                    if (((i * 24) + k) < image.getHeight()){
                        pixelColor = image.getRGB(j, (i * 24) + k);
                        if (pixelColor != -1){
                            data[k / 8] += (byte)(128 >> (k % 8));
                        }
                    }
                }

                write(data);
                // 重置参数
                data[0] = (byte)0x00;
                data[1] = (byte)0x00;
                data[2] = (byte)0x00;
            }
            //换行
            byte[] byte_send1 = new byte[2];
            byte_send1[0] = 0x0d;
            byte_send1[1] = 0x0a;
            write(byte_send1);

        	try
			{
				sleep(30);
			}
			catch (InterruptedException e)
			{
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
        }
        escPos.line(2);

        escPos.feedAndCut();
        return this;
    }
    

	private void write(byte ...data) throws IOException {
        socketOut.write(data);
        socketOut.flush();
    }
   
    public void cut() throws IOException 
    {
    	socketOut.write(POS_CUT_MOVE);
    	socketOut.write(POS_CUT_MODE_FULL);  
    }
    
    public void check() throws IOException 
    {
    	socketOut.flush();
    	socketOut.write(PRINT_STATE_DLE_EOT);
    	socketOut.flush();
    	System.out.println("打印机状态："+bis.read(byte_src));
    	
    }
    
    public static String getImageStr(String imgFile) {// 将图片文件转化为字节数组字符串，并对其进行Base64编码处理

    	InputStream in = null;
    	byte[] data = null;
    	// 读取图片字节数组
    	try {
    	in = new FileInputStream(imgFile);
    	data = new byte[in.available()];
    	in.read(data);
    	in.close();
    	} catch (IOException e) {
    	e.printStackTrace();
    	}
    	// 对字节数组Base64编码
    	BASE64Encoder encoder = new BASE64Encoder();
    	return encoder.encode(data);// 返回Base64编码过的字节数组字符串
    }
    
    public static void print(String Ip,int port,int copy,String html,int printer_id) throws Exception
    {
    	int ydycs = Constant.getTimes(printer_id);
    	
    	int newport = port==0 ? 9100 : port;
    	EscPos ep = EscPos.getInstance(Ip,newport);
		for(int i=0;i<(copy-ydycs);i++)
		{
			String htmln = html.replaceAll(":page_now:", (i+1+ydycs)+"");
			htmln = htmln.replaceAll(":page_total:",copy+"");
			BufferedImage bi = HtmlImageGenerators.html2bi(htmln);
			ep.printImage(bi);
		}
    }
}
