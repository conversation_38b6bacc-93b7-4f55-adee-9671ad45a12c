package com.tzx.framework.common.util;

import java.io.IOException;
import java.text.SimpleDateFormat;

import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.fasterxml.jackson.module.jaxb.JaxbAnnotationIntrospector;

/**
 * TODO
 * <AUTHOR> 
 * @日期：2015年5月19日-下午1:04:12
 */

public class ObjectMapper extends com.fasterxml.jackson.databind.ObjectMapper
{
	/**
	 * 
	 */
	private static final long	serialVersionUID	= 1L;

	public ObjectMapper()
	{
		super();
		// 设置输出时包含属性的风格
		this.setSerializationInclusion(Include.ALWAYS);
		// 设置输入时忽略在JSON字符串中存在但Java对象实际没有的属性
		this.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
		this.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));

		this.setAnnotationIntrospector(new JaxbAnnotationIntrospector(TypeFactory.defaultInstance()));

		this.getSerializerProvider().setNullValueSerializer(new JsonSerializer<Object>()
		{
			@Override
			public void serialize(Object value, JsonGenerator jgen, SerializerProvider provider) throws IOException, JsonProcessingException
			{
				jgen.writeString("");
			}
		});

	}
}
