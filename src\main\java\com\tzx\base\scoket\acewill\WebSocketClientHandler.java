/** 
 * @(#)CodeFormat.java    1.0   2017-10-06
 * 
 * Copyright 北京普照天星科技有限公司. All rights reserved. 
 * PROPRIETARY/CONFIDENTIAL. Use is subject to license terms. 
 */
package com.tzx.base.scoket.acewill;

import org.java_websocket.client.WebSocketClient;
import org.java_websocket.drafts.Draft;
import org.java_websocket.handshake.ServerHandshake;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import com.tzx.base.common.util.ConsumerThreadPoolUtil;
import com.tzx.base.listener.AcewillWebSocketConsumerThreadPoolTask;

import net.sf.json.JSONObject;

import java.net.URI;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 微生活webSocket客户端
 * 
 * <AUTHOR> email:<EMAIL>
 * @version 1.0 2018-03-10
 * @see
 * @since JDK7.0
 * @update
 */
public class WebSocketClientHandler extends WebSocketClient {
	
	private static final Logger LOGGER = LoggerFactory.getLogger(WebSocketClientHandler.class);

	public WebSocketClientHandler(URI serverUri, Draft draft) {
		super(serverUri, draft);
	}

	public WebSocketClientHandler(URI serverURI) {
		super(serverURI);
	}

	@Override
	public void onOpen(ServerHandshake handshakedata) {
		LOGGER.info("opened connection");
	}

	@Override
	public void onMessage(String message) {
		WebSocketConnectionThread.setLastMessageTime(new AtomicLong(System.currentTimeMillis()));
		if (message == null || "".equals(message)) {
			LOGGER.info("消息为空");
			return;
		} 
		
		JSONObject getJson = JSONObject.fromObject(message);
		Integer ok = (Integer) getJson.get("ok");
		if(ok!=null) {
			if (0 == ok)
			{
				LOGGER.info("收到消息回复{}", message);
			}
			return;
		}
		if(!StringUtils.isEmpty(getJson.get("id"))) {
			LOGGER.info("received: " + message);
			JSONObject sendJson = new JSONObject();
			sendJson.put("id", getJson.get("id"));
			sendJson.put("msgType", getJson.get("msgType"));
			sendJson.put("action", getJson.get("action"));
			sendJson.put("from", getJson.get("to"));
			sendJson.put("to", getJson.get("from"));
			sendJson.put("type", "ack");
			this.send(sendJson.toString());
			LOGGER.info("send: " + sendJson);
			ConsumerThreadPoolUtil.getConsumerThreadPool().execute(new AcewillWebSocketConsumerThreadPoolTask(message));
		}

	}

	@Override
	public void onClose(int code, String reason, boolean remote) {
		LOGGER.info("Connection closed by " + (remote ? "remote peer" : "us") +"; code:"+code+", reason:"+reason);
	}

	@Override
	public void onError(Exception ex) {
		LOGGER.info("Connection Error： " + ex.toString());
	}

	
}
