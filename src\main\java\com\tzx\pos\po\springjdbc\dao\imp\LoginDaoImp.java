package com.tzx.pos.po.springjdbc.dao.imp;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Repository;

import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.dao.imp.BaseDaoImp;
import com.tzx.pos.po.springjdbc.dao.LoginDao;

/**
 * <AUTHOR> 
 * @日期：2015年5月13日-下午1:32:17
 */
@Repository(LoginDao.NAME)
public class LoginDaoImp extends BaseDaoImp implements LoginDao
{
	private static final Logger logger = Logger.getLogger(LoginDaoImp.class);
	
	@Override
	public void updatePasword(String npassword,String optNum,Integer organId,Data result) throws Exception
	{
		StringBuilder sqlupdate = new StringBuilder("update user_authority set password = ?");
		sqlupdate.append(" where pos_user_name = ? ");
		logger.info("更新密码sql：" + sqlupdate.toString() + ",参数：" + npassword+","+optNum);
		
		int up = this.jdbcTemplate.update(sqlupdate.toString(), new Object[] {
				npassword, optNum });
		if (up == 0) 
		{
			throw new SystemException(PosErrorCode.MODIFY_PASSWORD_FAILURE);
		}else
		{
			result.setCode(Constant.CODE_SUCCESS);
			result.setMsg(Constant.MODIFY_PASSWORD_SUCCESS);
		}
	}
}
