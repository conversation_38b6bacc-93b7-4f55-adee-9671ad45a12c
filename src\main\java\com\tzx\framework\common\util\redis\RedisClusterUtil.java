package com.tzx.framework.common.util.redis;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.thoughtworks.xstream.XStream;

import redis.clients.jedis.JedisCluster;

/**
 * Created by jzq1999 on 2017/4/6.
 */
public class RedisClusterUtil extends BaseRedisUtil {

    private static final Logger log = LoggerFactory.getLogger(RedisClusterUtil.class);
    private static XStream xstream = new XStream();
    private JedisCluster jedisCluster;

    @Override
    public void rpush(String key, String... val) {
        try {
            jedisCluster.rpush(key, val);
            return;
        } catch (Exception var8) {
            log.error("redis操作发生异常", var8);
        }
    }

    public void lpush(String key, String... val) {
        
        try {
            jedisCluster.lpush(key, val);
            return;
        } catch (Exception var8) {
            log.error("redis操作发生异常", var8);
        }
    }

    public Long llen(String key) {
        if(!this.useCache) {
            return null;
        } else {
            Long var4;
            try {
                Long e = jedisCluster.llen(key);
                var4 = e;
                return var4;
            } catch (Exception var8) {
                log.error("redis操作发生异常", var8);
                var4 = null;
            }

            return var4;
        }
    }

    public List lrange(String key, int start, int end) {
        if(!this.useCache) {
            return null;
        } else {
            List var6;
            try {
                List e = jedisCluster.lrange(key, (long)start, (long)end);
                var6 = e;
                return var6;
            } catch (Exception var10) {
                log.error("redis操作发生异常", var10);
                var6 = null;
            }

            return var6;
        }
    }

    public void lset(String key, int index, String value) {
        try {
            jedisCluster.lset(key, (long)index, value);
            return;
        } catch (Exception var9) {
            log.error("redis操作发生异常", var9);
        }

    }

    public String lpop(String key) {
        if(!this.useCache) {
            return null;
        } else {
            String var4;
            try {
                String e = jedisCluster.lpop(key);
                var4 = e;
                return var4;
            } catch (Exception var8) {
                log.error("redis操作发生异常", var8);
                var4 = null;
            }

            return var4;
        }
    }

    public String rpop(String key) {
        if(!this.useCache) {
            return null;
        } else {
            String var4;
            try {
                String e = jedisCluster.rpop(key);
                var4 = e;
                return var4;
            } catch (Exception var8) {
                log.error("redis操作发生异常", var8);
                var4 = null;
            }

            return var4;
        }
    }

    public boolean exists(String key) {
        boolean var4;
        try {
            Boolean e = jedisCluster.exists(key);
            var4 = e.booleanValue();
            return var4;
        } catch (Exception var8) {
            log.error("redis操作发生异常", var8);
            var4 = false;
        }

        return var4;
    }

    public Long del(String key) {
        Long var4;
        try {
            Long e = jedisCluster.del(key);
            return e;
        } catch (Exception var8) {
            log.error("redis操作发生异常", var8);
            var4 = Long.valueOf(0L);
        }

        return var4;
    }

    public void set(String key, String val) {
        try {
            jedisCluster.setex(key, this.defaultExpireTime, val);
            return;
        } catch (Exception var8) {
            log.error("redis操作发生异常", var8);
        }

    }

    public void set(String key, String val, boolean expire) {
        try {
            if(expire) {
                jedisCluster.setex(key, this.defaultExpireTime, val);
            } else {
                jedisCluster.set(key, val);
            }

            return;
        } catch (Exception var9) {
            log.error("redis操作发生异常", var9);
        }
    }

    public String get(String key) {
        if(!this.useCache) {
            return null;
        } else {
            String var4;
            try {
                String e = jedisCluster.get(key);
                if(StringUtils.isNotBlank(e) && e.startsWith("<") && e.endsWith(">")) {
                    e = null;
                    this.del(key);
                }

                var4 = e;
                return var4;
            } catch (Exception var8) {
                log.error("redis操作发生异常", var8);
                var4 = null;
            }

            return var4;
        }
    }

    public String getSet(String key, String value) {
        if(!this.useCache) {
            return null;
        } else {
            String var5;
            try {
                String e = jedisCluster.getSet(key, value);
                var5 = e;
                return var5;
            } catch (Exception var9) {
                log.error("redis操作发生异常", var9);
                var5 = null;
            }

            return var5;
        }
    }

    public void lrem(String key, Integer count, String val) {
        try {
            jedisCluster.lrem(key, (long)count.intValue(), val);
            return;
        } catch (Exception var9) {
            log.error("redis操作发生异常", var9);
        }

    }

    public void sadd(String key, String val) {
        try {
            jedisCluster.sadd(key, new String[]{val});
            return;
        } catch (Exception var8) {
            log.error("redis操作发生异常", var8);
        }

    }

    public String spop(String key) {
        if(!this.useCache) {
            return null;
        } else {
            String var4;
            try {
                String e = jedisCluster.spop(key);
                var4 = e;
                return var4;
            } catch (Exception var8) {
                log.error("redis操作发生异常", var8);
                var4 = null;
            }

            return var4;
        }
    }

    public Set<String> smembers(String key) {
        if(!this.useCache) {
            return null;
        } else {
            Set var4;
            try {
                Set e = jedisCluster.smembers(key);
                var4 = e;
                return var4;
            } catch (Exception var8) {
                log.error("redis操作发生异常", var8);
                var4 = null;
            }

            return var4;
        }
    }

    public int scard(String key) {
        if(!this.useCache) {
            return 0;
        } else {
            byte var4;
            try {
                Long e = jedisCluster.scard(key);
                int var10 = e.intValue();
                return var10;
            } catch (Exception var8) {
                log.error("redis操作发生异常", var8);
                var4 = 0;
            }

            return var4;
        }
    }

    public void expire(String key, int num) {
        try {
            jedisCluster.expire(key, num);
            return;
        } catch (Exception var8) {
            log.error("redis操作发生异常", var8);
        }
    }

    public void setex(String key, int seconds, String value) {
        try {
            jedisCluster.setex(key, seconds, value);
            return;
        } catch (Exception var9) {
            log.error("redis操作发生异常", var9);
        }
    }

    public void srem(String key, String val) {
        try {
            jedisCluster.srem(key, new String[]{val});
            return;
        } catch (Exception var8) {
            log.error("redis操作发生异常", var8);
        }

    }

    public boolean sismember(String key, String member) {
        if(!this.useCache) {
            return false;
        } else {

            boolean var5;
            try {
                boolean e = jedisCluster.sismember(key, member).booleanValue();
                return e;
            } catch (Exception var9) {
                log.error("redis操作发生异常", var9);
                var5 = false;
            }

            return var5;
        }
    }

    public Long incr(String key) {
        if(!this.useCache) {
            return null;
        } else {
            Object var4;
            try {
                Long e = jedisCluster.incr(key);
                return e;
            } catch (Exception var8) {
                log.error("redis操作发生异常", var8);
                var4 = null;
            }

            return (Long)var4;
        }
    }

    public Long decr(String key) {
        if(!this.useCache) {
            return null;
        } else {

            Object var4;
            try {
                Long e = jedisCluster.decr(key);
                return e;
            } catch (Exception var8) {
                log.error("redis操作发生异常", var8);
                var4 = null;
            }

            return (Long)var4;
        }
    }

    public Long hincrBy(String key, String field, Long value) {
        if(!this.useCache) {
            return null;
        } else {
            Object var7;
            try {
                Long e = jedisCluster.hincrBy(key, field, value);
                return e;
            } catch (Exception var11) {
                log.error("redis操作发生异常", var11);
                var7 = null;
            }

            return (Long)var7;
        }
    }

    public boolean hmexite(String key, String field) {
        if(!this.useCache) {
            return false;
        } else {
            boolean var5;
            try {
                boolean e = jedisCluster.hexists(key, field).booleanValue();
                return e;
            } catch (Exception var9) {
                log.error("redis操作发生异常", var9);
                var5 = false;
            }

            return var5;
        }
    }

    public Long hgetNum(String key, String field) {
        if(!this.useCache) {
            return null;
        } else {
            Long var5;
            try {
                String e = jedisCluster.hget(key, field);
                if(StringUtils.isNotBlank(e)) {
                    var5 = Long.valueOf(e);
                    return var5;
                }

                var5 = Long.valueOf(0L);
            } catch (Exception var9) {
                log.error("redis操作发生异常", var9);
                var5 = null;
                return var5;
            } 

            return var5;
        }
    }

    public Long hsetnx(String key, String field, String value) {
        if(!this.useCache) {
            return null;
        } else {
            Object var6;
            try {
                Long e = jedisCluster.hsetnx(key, field, value);
                return e;
            } catch (Exception var10) {
                log.error("redis操作发生异常", var10);
                var6 = null;
            }

            return (Long)var6;
        }
    }

    public Map<String, String> hgetAll(String key) {
        if(!this.useCache) {
            return null;
        } else {
            Object var4;
            try {
                Map e = jedisCluster.hgetAll(key);
                return e;
            } catch (Exception var8) {
                log.error("redis操作发生异常", var8);
                var4 = null;
            }

            return (Map)var4;
        }
    }

    public Object getBySerialize(String key) {
        if(!this.useCache) {
            return null;
        } else {
            Object var4;
            try {
                String e = jedisCluster.get(key);
                if(e == null) {
                    var4 = null;
                    return var4;
                }

                var4 = xstream.fromXML(e);
                return var4;
            } catch (Exception var8) {
                log.error("redis操作发生异常", var8);
                var4 = null;
            }

            return var4;
        }
    }

    public void setBySerialize(String key, Object val) {
        try {
            if(val == null) {
                return;
            }

            jedisCluster.setex(key, this.defaultExpireTime, xstream.toXML(val));
            return;
        } catch (Exception var8) {
            log.error("redis操作发生异常", var8);
        }

    }

    public void setexBySerialize(String key, int seconds, Object val) {
        try {
            jedisCluster.setex(key, seconds, val == null?null:xstream.toXML(val));
            return;
        } catch (Exception var9) {
            log.error("redis操作发生异常", var9);
        }

    }

    public void zadd(String key, double score, String member) {
        if(this.useCache) {
            if(!StringUtils.isBlank(key)) {
                try {
                    jedisCluster.zadd(key, score, member);
                    return;
                } catch (Exception var10) {
                    log.error("redis操作发生异常", var10);
                }
            }
        }
    }

    public void zrem(String key, String member) {
        if(this.useCache) {
            if(!StringUtils.isBlank(key)) {
                try {
                    jedisCluster.zrem(key, new String[]{member});
                    return;
                } catch (Exception var8) {
                    log.error("redis操作发生异常", var8);
                }
            }
        }
    }

    public Double zscore(String key, String member) {
        Double result = null;
        if(!this.useCache) {
            return null;
        } else if(StringUtils.isBlank(key)) {
            return null;
        } else {
            Object var6;
            try {
                result = jedisCluster.zscore(key, member);
                return result;
            } catch (Exception var10) {
                log.error("redis操作发生异常", var10);
                var6 = null;
            }

            return (Double)var6;
        }
    }

    public List<String> zquery(String key, Long start, Long end, boolean isAsc) {
        if(!this.useCache) {
            return null;
        } else if(StringUtils.isBlank(key)) {
            return null;
        } else {
            ArrayList list;
            try {
                Set e = null;
                if(isAsc) {
                    e = jedisCluster.zrange(key, start, end);
                } else {
                    e = jedisCluster.zrevrange(key, start, end);
                }

                if(null != e && e.size() > 0) {
                    list = new ArrayList();
                    Iterator var10 = e.iterator();

                    while(var10.hasNext()) {
                        String s = (String)var10.next();
                        list.add(s);
                    }

                    ArrayList var17 = list;
                    return var17;
                }

                list = null;
            } catch (Exception var15) {
                log.error("redis操作发生异常", var15);
                list = null;
                return list;
            }

            return list;
        }
    }

    public Long zcard(String key) {
        if(!this.useCache) {
            return 0L;
        } else if(StringUtils.isBlank(key)) {
            return 0L;
        } else {
            long var4;
            try {
                 ;
                long e = jedisCluster.zcard(key).longValue();
                return e;
            } catch (Exception var9) {
                log.error("redis操作发生异常", var9);
                var4 = 0L;
            }

            return var4;
        }
    }

    public String hget(String key, String field) {
        if(!this.useCache) {
            return null;
        } else if(!StringUtils.isBlank(key) && !StringUtils.isBlank(field)) {
            Object var5;
            try {
                String e = jedisCluster.hget(key, field);
                return e;
            } catch (Exception var9) {
                log.error("redis操作发生异常", var9);
                var5 = null;
            }

            return (String)var5;
        } else {
            return null;
        }
    }

    public Long hdel(String key, String... fields) {
        if(!this.useCache) {
            return null;
        } else if(!StringUtils.isBlank(key) && fields != null && fields.length > 0) {
            Object var5;
            try {
                Long e = jedisCluster.hdel(key, fields);
                return e;
            } catch (Exception var9) {
                log.error("redis操作发生异常", var9);
                var5 = null;
            }

            return (Long)var5;
        } else {
            return null;
        }
    }

    public Long hset(String key, String field, String value) {
        if(!this.useCache) {
            return Long.valueOf(0L);
        } else if(!StringUtils.isBlank(key) && !StringUtils.isBlank(field) && !StringUtils.isBlank(value)) {
            Long result = Long.valueOf(0L);

            Long var7;
            try {
                result = jedisCluster.hset(key, field, value);
                jedisCluster.expire(key, this.defaultExpireTime);
                return result;
            } catch (Exception var11) {
                log.error("redis操作发生异常", var11);
                var7 = Long.valueOf(0L);
            }

            return var7;
        } else {
            return Long.valueOf(0L);
        }
    }

    public void sadd(String key, String... member) {
        try {
            jedisCluster.sadd(key, member);
            return;
        } catch (Exception var8) {
            log.error("redis操作发生异常", var8);
        }
    }


    public JedisCluster getJedisCluster() {
        return jedisCluster;
    }

    public void setJedisCluster(JedisCluster jedisCluster) {
        this.jedisCluster = jedisCluster;
    }

}
