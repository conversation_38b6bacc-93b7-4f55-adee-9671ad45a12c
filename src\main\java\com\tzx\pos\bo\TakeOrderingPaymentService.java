package com.tzx.pos.bo;

import com.tzx.framework.common.entity.Data;
import com.tzx.pos.bo.dto.TakeOrderingParam;
/**
 * 订单转账单支付
 * <AUTHOR>
 *
 */
public interface TakeOrderingPaymentService {
     String	NAME	= "com.tzx.pos.bo.imp.TakeOrderingPaymentServiceImp";
     
	 /**
	  * 支付
	  * @param param
	  * @param result
	  */
	 public void posBillPayment(TakeOrderingParam params,Data result) throws Exception;
}
