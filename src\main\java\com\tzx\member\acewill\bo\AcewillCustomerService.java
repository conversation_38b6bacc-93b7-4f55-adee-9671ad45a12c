package com.tzx.member.acewill.bo;

import java.util.List;

import com.tzx.base.entity.CustomerOperationLogs;
import net.sf.json.JSONObject;

import com.tzx.framework.common.entity.Data;
import com.tzx.member.common.entity.PosCustomerOperateListEntity;

public interface AcewillCustomerService
{
	String	NAME	= "com.tzx.member.acewill.bo.imp.AcewillCustomerServiceImp";

	/**
	 * 检查参数是否是使用微生活会员
	 * 
	 * @param tenantId
	 * @param storeId
	 * @return
	 * @throws Exception
	 */
	public boolean checkIsAcewillCustomer(String tenantId, int storeId) throws Exception;
	
	/**
	 * @param requestData
	 *            请求参数
	 * @param requestUrl
	 *            请求地址
	 * @return
	 * @throws Exception
	 */
	public Data commonPost(Data requestData, String requestUrl) throws Exception;

	/**
	 * 获取用户账户信息
	 * 
	 * @param requestData
	 * @param card_code
	 * @return
	 * @throws Exception
	 */
	public Data getAcewillCustomerUserInfo(Data requestData) throws Exception;

	/** 获取用户账户信息
	 * @param tenancyId
	 * @param storeId
	 * @param cardCode
	 * @return
	 * @throws Exception
	 */
	public Data getAcewillCustomerUserInfo(String tenancyId, Integer storeId, String cardCode) throws Exception;
	
	/**
	 * 交易预览 新增pos_customer_operate_lis
	 * 
	 * @param requestData
	 * @return
	 * @throws Exception
	 */
	public Data previewAcewillCustomerDeal(Data requestData) throws Exception;

	/**
	 * 交易提交 修改pos_customer_operate_lis状态
	 * 
	 * @param requestData
	 * @return
	 * @throws Exception
	 */
	public Data commitAcewillCustomerDeal(Data requestData) throws Exception;

//	/**
//	 * 交易冲正
//	 * 
//	 * @param tenancyId
//	 * @param storeId
//	 * @param paramJson
//	 * @return
//	 * @throws Exception
//	 */
//	public Data rollbackAcewillCustomerDeal(String tenancyId,Integer storeId,JSONObject paramJson) throws Exception;

	/**查询交易
	 * @param tenancyId
	 * @param storeId
	 * @param paramJson{"third_bill_code":"1220171107000003","bill_code":"12312312312312312"}
	 * @return
	 * @throws Exception
	 */
	public JSONObject queryAcewillCustomerDeal(String tenancyId, Integer storeId, JSONObject paramJson) throws Exception;
	
	/**
	 * 交易撤销 新增pos_customer_operate_lis
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param paramJson
	 * @return
	 * @throws Exception
	 */
	public Data cancelAcewillCustomerDeal(String tenancyId, Integer storeId, JSONObject paramJson, String actionType) throws Exception;
	
	/**
	 * 查询撤销状态
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param paramJson{"third_bill_code":"1220171107000003"}
	 * @return {"operate_state","01"}
	 * @throws Exception
	 */
	public JSONObject queryCancelAcewillCustomerDeal(String tenancyId, Integer storeId, JSONObject paramJson) throws Exception;

	/**
	 * 发送验证码
	 * 
	 * @param requestData
	 * @return
	 * @throws Exception
	 */
	public Data sendCodeAcewillCustomer(Data requestData) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param printCode
	 * @param paramJson
	 * @throws Exception
	 */
	public void printForCustomer(String tenancyId, Integer storeId, String printCode, JSONObject paramJson) throws Exception;

	/**
	 * 消费记录详情
	 * @param tenancyId
	 * @param storeId
	 * @param data
	 * @return
	 * @throws Exception
	 */
	public Data getAcewillCustomerView(String tenancyId, Integer storeId, JSONObject data) throws Exception ;
	
	/** 查看等级设置
	 * @param tenancyId
	 * @param storeId
	 * @return
	 * @throws Exception
	 */
	public Data getAcewillGradeRule(String tenancyId, Integer storeId) throws Exception;

	/** 保存日志
	 * @param tenancyId
	 * @param operationLogs
	 * @throws Exception
	 */
	public void  saveCustomerOperationLog(String tenancyId,CustomerOperationLogs operationLogs) throws Exception;
	
	/** 查询消费记录
	 * @param tenancyId
	 * @param storeId
	 * @param cardCode
	 * @param thirdBillCode
	 * @param outTradeNo
	 * @return
	 * @throws Exception
	 */
	public PosCustomerOperateListEntity getCustomerOperateListByAcewillXF(String tenancyId, Integer storeId, String cardCode,String thirdBillCode, String outTradeNo) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param cardCode
	 * @param lockTradeNo
	 * @param outTradeNo
	 * @param subBalance
	 * @param subCredit
	 * @param couponsList
	 * @throws Exception
	 */
	public Data lockAcewillCustomerDeal(String tenancyId, Integer storeId, String optNum, String cardCode, String lockTradeNo, String outTradeNo, Double subBalance, Double subCredit, List<String> couponsList) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param cardCode
	 * @param lockTradeNo
	 * @param outTradeNo
	 * @throws Exception
	 */
	public Data unlockAcewillCustomerDeal(String tenancyId, Integer storeId, String optNum, String cardCode, String lockTradeNo, String outTradeNo) throws Exception;
}
