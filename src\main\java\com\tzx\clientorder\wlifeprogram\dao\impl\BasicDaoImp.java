package com.tzx.clientorder.wlifeprogram.dao.impl;

import com.tzx.clientorder.wlifeprogram.dao.BasicDao;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.base.util.SqlUtil;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by qingui on 2018-06-27.
 */
@Repository(BasicDao.NAME)
public class BasicDaoImp extends WlifeCommonDaoImpl implements BasicDao{

    @Override
    public List<JSONObject> getSoldOutInfo(String tenancyId, int storeId) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" select i.id as dishid,i.item_code as dish_sno,u.id as dnid,0 as meanid,1 as type");
        sql.append(" ,case when s.num = 0 then 2 else 1 end as status, s.num as count");
        sql.append(" from pos_soldout s ");
        sql.append(" left join hq_item_info i on s.item_id = i.id and i.valid_state = '1' ");
        sql.append(" join hq_item_unit u on i.id = u.item_id and u.valid_state = '1' ");
        sql.append(" where s.store_id = " + storeId);
        return this.query4Json(tenancyId, sql.toString());
    }

    @Override
    public List<JSONObject> getShops(String tenancy_id, String store_id) throws Exception {
        StringBuilder sql = new StringBuilder();
        StringBuilder subSql = new StringBuilder();
        subSql.append(" (SELECT MAX(store_id) FROM  sys_parameter WHERE store_id in (")
                .append("0,").append(store_id).append(")").append(" and para_code='wlife_ordermode')");
        sql.append(" select o.id as sid, '' as bid, o.org_full_name as shopname, COALESCE(o.address, '') as shopadd ");
        sql.append(" , '' as shoplocation, COALESCE(o.longitude, '') as lng, COALESCE(o.latitude, '') as lat ");
        sql.append(",(SELECT para_value FROM sys_parameter WHERE para_code='wlife_ordermode' ")
                .append(" AND tenancy_id='").append(tenancy_id).append("' AND store_id= ").append(subSql).append(") as ordermode,");
        sql.append(" 0 as is_bind_user ");
        sql.append(" from organ o ");
        sql.append(" where o.tenancy_id='" + tenancy_id + "' ");
        sql.append(" and o.id='" + store_id + "' ");
        sql.append(" and o.org_type='3' ");
        sql.append(" and o.valid_state='1'  ");
        return this.query4Json(tenancy_id, sql.toString());
    }

    @Override
    public List<JSONObject> getTables(String tenancy_id, String store_id) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" select ti.id as tid, ti.table_code as sno, 0 as mealfee, ti.table_name as tablename  ");
        sql.append(" from tables_info ti ");
        sql.append(" where ti.tenancy_id='" + tenancy_id + "' ");
        sql.append(" and ti.valid_state='1' ");
        sql.append(" and ti.organ_id='" + store_id + "' ");
        return this.query4Json(tenancy_id, sql.toString());
    }

    @Override
    public List<JSONObject> getMemos(String tenancy_id, String store_id) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" select it.id as omid, COALESCE(it.father_id, 0) as omkid, it.name as ordermemo  ");
        sql.append(" from item_taste it ");
        sql.append(" left join item_taste_org ito on it.tenancy_id=ito.tenancy_id and it.id=ito.teste_id");
        sql.append(" where ito.tenancy_id='" + tenancy_id + "' and ito.store_id='"+store_id+"'");
        sql.append(" and it.valid_state='1' and (it.father_id is not null and it.father_id<>'0') ");
        sql.append(" order by it.father_id ");
        return this.query4Json(tenancy_id, sql.toString());
    }

    @Override
    public List<JSONObject> getDishKinds(String tenancyId, Integer storeId, String chanel) throws Exception
    {
        StringBuilder sql = new StringBuilder();
        sql.append("with recursive item_class as (");
        sql.append(" select hic.id,hic.itemclass_name,himco.classorder,hic.itemclass_code,hic.father_id,him.item_menu_id,count(*) item_count from (");
        sql.append(" select distinct himo.tenancy_id,himo.store_id,him.id as item_menu_id,himd.item_id,himc.class as item_class_id,himc.chanel");
        sql.append(" from hq_item_menu_class himc");
        sql.append(" left join hq_item_menu_details himd on himc.tenancy_id=himd.tenancy_id and himc.details_id=himd.id");
        sql.append(" left join hq_item_menu him on himd.tenancy_id=him.tenancy_id  and himd.item_menu_id=him.id");
        sql.append(" left join hq_item_menu_organ himo on him.tenancy_id=himo.tenancy_id and him.id=himo.item_menu_id");
        sql.append(" where himo.tenancy_id=? and himo.store_id=? and him.valid_state='1' and himd.valid_state='1' and himc.chanel=?) him");
        sql.append(" left join hq_item_class hic on him.tenancy_id=hic.tenancy_id and hic.id=him.item_class_id  ");
        sql.append(" left join hq_item_menu_classorder himco on hic.id=himco.class_id and him.item_menu_id=himco.menu_id");
        sql.append(" group by hic.id,hic.itemclass_name,himco.classorder,hic.itemclass_code,hic.father_id,him.item_menu_id");
        sql.append(" union select hic.id,hic.itemclass_name,himo.classorder,hic.itemclass_code,hic.father_id,item_class.item_menu_id,'0' item_count from hq_item_class hic");
        sql.append(" inner join item_class on hic.id=item_class.father_id ");
        sql.append(" left join hq_item_menu_classorder himo on hic.id=himo.class_id and item_class.item_menu_id=himo.menu_id");
        sql.append(") select item_class.id as id,item_class.itemclass_name as name,(coalesce(item_class.classorder,0)+1) seq,item_class.itemclass_code dishkindsno,item_class.father_id pdkid,'' as pkid ");
        sql.append(" ,0 as must,0 as must_seq,0 as suggest, '' as border, '' as icon, item_class.item_count dish_count from item_class order by item_class.classorder ");
        List<JSONObject> query4Json = this.query4Json(tenancyId, sql.toString(), new Object[]
                { tenancyId, storeId, chanel });
        return query4Json;
    }

    @Override
    public List<JSONObject> getItemInfos(String tenancy_id, String store_id) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append("  select distinct hii.id as id, COALESCE(himc.item_name,hii.item_name) as name, hii.item_code as dishsno, '' as describ  ");
        sql.append("  , COALESCE(hii.item_description, '') as info, (case when hii.is_combo='Y' then 2 else 1 end) as type  ");
        sql.append("  , (case when hii.is_assemble_combo='1' then false else true end) as wxDishs,(case when hii.is_assemble_combo='1' or pfi.id is not null then 2 else ((case when himd.is_show='1' then 1 else 2 end)) end) as dHide, himc.class as pkid ");
        sql.append("  , hic.father_id as dishkind, COALESCE(hii.photo1, '') as icon, COALESCE(hii.photo1, '') as image  ");
        sql.append("  , COALESCE(hii.photo1, '') as dishimg, 1 as min_unit, 1 as min_count, 1 as min_reduce ");//1 as soldouttype
//        sql.append("  ,(CASE WHEN (select coalesce(num,0) from pos_soldout WHERE item_id = hii.ID and tenancy_id = hii.tenancy_id) <= 0 THEN 1 ELSE 0 END) AS soldout ");
//        sql.append("  ,(CASE WHEN (select num from pos_soldout WHERE item_id = hii.ID and tenancy_id = hii.tenancy_id) is not null THEN (SELECT COALESCE (num, 0) AS num FROM pos_soldout WHERE item_id = hii. ID AND tenancy_id = hii.tenancy_id) ELSE -1 END) AS leftamount ");
        sql.append("  ,(CASE WHEN COALESCE(sl.num,0) <= 0 THEN 1 ELSE 0 END) AS soldout");
        sql.append("  ,(CASE WHEN sl.num is not null THEN COALESCE(sl.num, 0) ELSE -1 END) AS leftamount");
        sql.append("  , (case when hii.is_runningprice='Y' then hiu.unit_name else '' end) as priceName ");
        sql.append("  , (case when hii.is_runningprice='Y' then 1 else 0 end) as isWeigh   ");
       // sql.append("  , himcc.classorder,CAST (himd.menu_item_rank AS int4)");
        sql.append("  , himcc.classorder,(cast(himd.menu_item_rank as int4)+1) as dOrder, himc.phonetic_code ");
        sql.append("  from hq_item_menu_organ himo  ");
        sql.append("  join hq_item_menu him on (him.id=himo.item_menu_id and him.valid_state='1')   ");
        sql.append("  join hq_item_menu_details himd on (himd.item_menu_id=him.id and himd.valid_state='1')   ");
        sql.append("  join hq_item_menu_class himc on himc.details_id=himd.id and himc.chanel='WX02'  ");
        sql.append("  join hq_item_menu_classorder himcc on him.id=himcc.menu_id and himc.class=himcc.class_id  ");
        sql.append("  join hq_item_class hic on hic.id=himc.class and hic.chanel=himc.chanel and hic.valid_state='1'   ");
        sql.append("  join hq_item_info hii on (hii.id=himd.item_id and hii.valid_state='1')  ");
        sql.append("  join hq_item_unit hiu on (hiu.item_id=hii.id and hiu.valid_state='1' and hiu.is_default='Y') ");
        sql.append("  left join pos_soldout sl on sl.item_id = hii.ID and sl.tenancy_id = hii.tenancy_id and sl.store_id = himo.store_id");
        sql.append("  left join pos_forbidden_item pfi on pfi.item_id=hii.ID  and hii.tenancy_id = pfi.tenancy_id ");
        sql.append("  where himo.store_id=" + store_id + " and himo.tenancy_id='" + tenancy_id + "'");
        //sql.append("  order by himcc.classorder,himc.phonetic_code,cast(himd.menu_item_rank as int4) ");
        sql.append("  order by himcc.classorder,(cast(himd.menu_item_rank as int4)+1) ");
        return this.query4Json(tenancy_id, sql.toString());
    }

    @Override
	public List<JSONObject> getItemInfosForCombo(String tenancyId, String storeId, int[] comboIds, int[] itemIds) throws Exception
	{
		if (null == comboIds || 0 == comboIds.length)
		{
			return null;
		}

		StringBuilder sql = new StringBuilder();
		
		sql.append("with combo as( ");
		sql.append("select cd.iitem_id,(case when cd.is_itemgroup='Y' then gd.item_id else cd.details_id end) item_id from hq_item_combo_details cd ");
		sql.append("left join hq_item_group_details gd on cd.details_id = gd.item_group_id and cd.is_itemgroup='Y' ");
		sql.append("where cd.iitem_id in (").append(SqlUtil.getInnerStr(comboIds)).append(")), ");
		sql.append("menu as ( ");
		sql.append("select himd.item_id,himc.class as item_class,hic.father_id as father_class,himcc.classorder,cast(himd.menu_item_rank AS int4) as menu_item_rank ");
		sql.append("from hq_item_menu_organ himo ");
		sql.append("join hq_item_menu him on (him.id=himo.item_menu_id and him.valid_state='1') ");
		sql.append("join hq_item_menu_details himd on (himd.item_menu_id=him.id and himd.valid_state='1') ");
		sql.append("join hq_item_menu_class himc on himc.details_id=himd.id and himc.chanel='").append(SysDictionary.CHANEL_WX02).append("' ");
		sql.append("join hq_item_menu_classorder himcc on him.id=himcc.menu_id and himc.class=himcc.class_id ");
		sql.append("join hq_item_class hic on hic.id=himc.class and hic.chanel=himc.chanel and hic.valid_state='1' ");
		sql.append("where himo.store_id=").append(storeId).append(" and himo.tenancy_id='").append(tenancyId).append("' ) ");
		
		sql.append("select distinct hii.id as id,hii.item_name as name,hii.item_code as dishsno,'' as describ,menu.item_class as pkid,menu.father_class as dishkind,menu.classorder,menu.menu_item_rank as dOrder,");
		sql.append("coalesce(hii.item_description, '') as info,(case when hii.is_combo='Y' then 2 else 1 end) as type,");
		sql.append("coalesce(hii.photo1, '') as icon,coalesce(hii.photo1, '') as image,coalesce(hii.photo1, '') as dishimg,");
		sql.append("(case when hii.is_runningprice='Y' then 1 else 0 end) as isWeigh,(case when hii.is_runningprice='Y' then hiu.unit_name else '' end) as priceName,");
		sql.append("false as wxDishs,2 as dHide,1 as min_unit,1 as min_count,1 as min_reduce,0 AS soldout,-1 AS leftamount ");
		sql.append("from combo ");
		sql.append("join hq_item_info hii on (hii.id=combo.item_id and hii.valid_state='1') ");
		sql.append("left join hq_item_unit hiu on (hiu.item_id=hii.id and hiu.valid_state='1' and hiu.is_default='Y') ");
		sql.append("left join menu on menu.item_id=combo.iitem_id ");
		sql.append("where hii.tenancy_id='" ).append( tenancyId ).append( "' ");
		
		if (null != itemIds && 0 < itemIds.length)
		{
			sql.append(" and hii.id not in (").append(SqlUtil.getInnerStr(itemIds)).append(")");
		}
		sql.append(" order by menu.classorder,menu.menu_item_rank");

		return this.query4Json(tenancyId, sql.toString());
	}
    
    @Override
    public List<JSONObject> getUnitInfos(String tenancy_id, int[] item_ids, String price_system) throws Exception {
        if(item_ids == null || item_ids.length == 0){
            return new ArrayList<JSONObject>();
        }
        StringBuilder sql = new StringBuilder();
        sql.append(" select hiu.is_default,hiu.id as duid, hiu.unit_name as name, COALESCE(hip.price, hiu.standard_price) as price, COALESCE(hip.price, hiu.standard_price) as orgprice, 0 as bargainprice ");
        sql.append(" , 0 as limitCount, COALESCE(COALESCE(civs.vip_price, hip.price), hiu.standard_price) as memberprice, 1 as min_unit ");
        sql.append(" , hiu.item_id as item_id ");
        sql.append(" from hq_item_unit hiu  ");
        sql.append(" left join hq_item_pricesystem hip on (hip.item_unit_id=hiu.id and hip.chanel='"+SysDictionary.CHANEL_WX02+"' and hip.price_system='" + price_system + "') ");
        sql.append(" left join crm_item_vip_sysprice civs on (civs.unit_id=hiu.id and civs.chanel='"+SysDictionary.CHANEL_WX02+"' and civs.price_system='" + price_system + "') ");
        sql.append(" where hiu.item_id in (" + SqlUtil.getInnerStr(item_ids) + ") ");
        sql.append(" and hiu.valid_state='1' ");
        sql.append(" and hiu.tenancy_id='" + tenancy_id + "' ");
        return this.query4Json(tenancy_id, sql.toString());
    }
    
    @Override
	public List<JSONObject> getUnitInfos(String tenancyId, int[] itemIds, int[] timePriceIds, String priceSystem) throws Exception
	{
		// 如果时段特价方案为空,
		if (timePriceIds == null || timePriceIds.length == 0)
		{
			return this.getUnitInfos(tenancyId, itemIds, priceSystem);
		}

		if (itemIds == null || itemIds.length == 0)
		{
			return new ArrayList<JSONObject>();
		}

		StringBuilder sql = new StringBuilder();
		sql.append(" select hiu.is_default,hiu.item_id as item_id,hiu.id as duid, hiu.unit_name as name,");
		sql.append(" coalesce(hip.price, hiu.standard_price) as orgprice,");
		sql.append(" (case when tp.afterprice is not null then tp.afterprice when hip.price is not null then hip.price else hiu.standard_price end) as price,");
		sql.append(" (case when civs.vip_price is not null then civs.vip_price when hip.price is not null then hip.price else hiu.standard_price end) as memberprice,");
		sql.append(" (case when tp.afterprice is not null then 1 else 0 end) as bargainprice,");
		sql.append(" 0 as limitCount,  1 as min_unit ");
		sql.append(" from hq_item_unit hiu  ");
		sql.append(" left join hq_item_pricesystem hip on (hip.item_unit_id=hiu.id and hip.chanel='" + SysDictionary.CHANEL_WX02 + "' and hip.price_system='" + priceSystem + "') ");
		sql.append(" left join crm_item_vip_sysprice civs on (civs.unit_id=hiu.id and civs.chanel='" + SysDictionary.CHANEL_WX02 + "' and civs.price_system='" + priceSystem + "') ");
		sql.append(" left join (select htpd.item_id,htpd.item_unit_id,COALESCE(htpp.price, htpd.afterprice) as afterprice");
		sql.append(" from hq_time_price_details as htpd");
		sql.append(" left join hq_time_price_pricesystem htpp on htpp.time_price_details_iid=htpd.id and htpp.chanel='" + SysDictionary.CHANEL_WX02 + "' and htpp.price_system='" + priceSystem + "'");
		sql.append(" where htpd.valid_state='1' and htpd.time_price_id in (" + SqlUtil.getInnerStr(timePriceIds) + ") ) tp on  hiu.id =tp.item_unit_id and hiu.item_id = tp.item_id");
		sql.append(" where hiu.item_id in (" + SqlUtil.getInnerStr(itemIds) + ") ");
		sql.append(" and hiu.valid_state='1' ");
		sql.append(" and hiu.tenancy_id='" + tenancyId + "' ");
		return this.query4Json(tenancyId, sql.toString());
	}

    @Override
    public List<JSONObject> getMethodInfos(String tenancy_id, int[] item_ids, String storeId) throws Exception {
        if(item_ids == null || item_ids.length == 0){
            return new ArrayList<JSONObject>();
        }
        StringBuilder sql = new StringBuilder();
        sql.append(" select DISTINCT him.id as id, sd.class_item as name,");
        sql.append(" (case when him.makeup_way = 'ADD' then him.proportion_money when him.makeup_way = 'MULTI' then him.proportion_money * itp.price ELSE 0 end) as aprice,");
        sql.append(" (CASE WHEN (select coalesce(num,0) from pos_soldout WHERE item_id = him.item_id and tenancy_id = him.tenancy_id order by report_date desc LIMIT 1) <= 0 THEN 1 ELSE 0 END) AS soldout,");
        sql.append(" '' as icon ,him.item_id as item_id ");
        sql.append(" from hq_item_method him ");
        sql.append(" left join sys_dictionary sd on (sd.id=him.method_name_id and sd.class_identifier_code='method') ");
//        sql.append(" left join hq_item_info hii on him.item_id = hii.id");
        sql.append(" left join hq_item_unit hiu on hiu.item_id = him.item_id and hiu.is_default='Y'");
        sql.append(" left join hq_item_pricesystem itp on itp.item_unit_id = hiu.id and itp.chanel = '" + SysDictionary.CHANEL_WX02 +"'");
        sql.append(" left join organ o on itp.price_system::varchar = o.price_system ");
        sql.append(" where him.item_id in (" + SqlUtil.getInnerStr(item_ids) + ") ");
        sql.append(" and him.valid_state='1' ");
        sql.append(" and him.tenancy_id='" + tenancy_id + "' and o.id = " + storeId +" order by him.id asc ");
        return this.query4Json(tenancy_id, sql.toString());
    }

    @Override
    public List<JSONObject> getComboBaseInfo(String tenancy_id, int[] combo_item_ids, String price_system) throws Exception {
        if(combo_item_ids == null || combo_item_ids.length == 0){
            return new ArrayList<JSONObject>();
        }
        StringBuilder sql = new StringBuilder();
        sql.append(" select hiu.item_id as item_id,i.item_name as name, COALESCE(hip.price, hiu.standard_price) as price ");
        sql.append(" , COALESCE(hip.price, hiu.standard_price) as orgprice, 0 as bargainprice");
        sql.append(" , 0 as limitCount, COALESCE(COALESCE(civs.vip_price, hip.price), hiu.standard_price) as memberprice ");
        sql.append(" from hq_item_unit hiu ");
//		sql.append(" join hq_item_combo_details hicd on hicd.iitem_id=hiu.item_id and hicd.valid_state='1' ");
        sql.append(" left join hq_item_pricesystem hip on (hip.item_unit_id=hiu.id and hip.chanel='" + SysDictionary.CHANEL_WX02 +"' and hip.price_system ='" + price_system + "') ");
        sql.append(" left join crm_item_vip_sysprice civs on (civs.unit_id=hiu.id and civs.chanel='" + SysDictionary.CHANEL_WX02 +"' and civs.price_system='" + price_system + "' ) ");
        sql.append(" left join hq_item_info i on hiu.item_id = i.id ");
        sql.append(" where hiu.item_id in (" + SqlUtil.getInnerStr(combo_item_ids) + ") ");
        sql.append(" and hiu.is_default='Y' ");
        sql.append(" and hiu.valid_state='1' ");
        sql.append(" and hiu.tenancy_id='" + tenancy_id + "' ");
        return this.query4Json(tenancy_id, sql.toString());
    }
    
    @Override
	public List<JSONObject> getComboBaseInfo(String tenancyId, int[] comboItemIds, int[] timePriceIds, String priceSystem) throws Exception
	{
		// 如果时段特价方案为空,
		if (timePriceIds == null || timePriceIds.length == 0)
		{
			return this.getComboBaseInfo(tenancyId, comboItemIds, priceSystem);
		}

		if (comboItemIds == null || comboItemIds.length == 0)
		{
			return new ArrayList<JSONObject>();
		}
		StringBuilder sql = new StringBuilder();
		sql.append(" select hiu.item_id as item_id,hiu.id as duid, hiu.unit_name as name,");
		sql.append(" coalesce(hip.price, hiu.standard_price) as orgprice,");
		sql.append(" (case when tp.afterprice is not null then tp.afterprice when hip.price is not null then hip.price else hiu.standard_price end) as price,");
		sql.append(" (case when civs.vip_price is not null then civs.vip_price when hip.price is not null then hip.price else hiu.standard_price end) as memberprice,");
		sql.append(" (case when tp.afterprice is not null then 1 else 0 end) as bargainprice,");
		sql.append(" 0 as limitCount,  1 as min_unit ");
		sql.append(" from hq_item_unit hiu  ");
		sql.append(" left join hq_item_pricesystem hip on (hip.item_unit_id=hiu.id and hip.chanel='" + SysDictionary.CHANEL_WX02 + "' and hip.price_system='" + priceSystem + "') ");
		sql.append(" left join crm_item_vip_sysprice civs on (civs.unit_id=hiu.id and civs.chanel='" + SysDictionary.CHANEL_WX02 + "' and civs.price_system='" + priceSystem + "') ");
		sql.append(" left join (select htpd.item_id,htpd.item_unit_id,COALESCE(htpp.price, htpd.afterprice) as afterprice");
		sql.append(" from hq_time_price_details as htpd");
		sql.append(" left join hq_time_price_pricesystem htpp on htpp.time_price_details_iid=htpd.id and htpp.chanel='" + SysDictionary.CHANEL_WX02 + "' and htpp.price_system='" + priceSystem + "'");
		sql.append(" where htpd.valid_state='1' and htpd.time_price_id in (" + SqlUtil.getInnerStr(timePriceIds) + ") ) tp on  hiu.id =tp.item_unit_id and hiu.item_id = tp.item_id");
		sql.append(" where hiu.item_id in (" + SqlUtil.getInnerStr(comboItemIds) + ") ");
		sql.append(" and hiu.is_default='Y' ");
		sql.append(" and hiu.valid_state='1' ");
		sql.append(" and hiu.tenancy_id='" + tenancyId + "' ");
		return this.query4Json(tenancyId, sql.toString());
	}

    @Override
    public List<JSONObject> getMaindishDetails(String tenancy_id, int[] maindishCombIds) throws Exception {
        if(maindishCombIds == null || maindishCombIds.length == 0){
            return new ArrayList<JSONObject>();
        }
        StringBuilder sql = new StringBuilder();
        sql.append(" select hicd.id as hicd_id, hii.item_name as name, hicd.details_id as id, hicd.item_unit_id, hii.item_code as dishsno ");
        sql.append(" , hicd.combo_num as number ");
        sql.append(" from hq_item_combo_details hicd ");
        sql.append(" join hq_item_info hii on hii.id=hicd.details_id and hii.valid_state='1' ");
        sql.append(" where hicd.id in (" + SqlUtil.getInnerStr(maindishCombIds) + ") ");
        sql.append(" and hicd.valid_state='1' ");
        sql.append(" and hicd.tenancy_id='" + tenancy_id + "' ");
        return this.query4Json(tenancy_id, sql.toString());
    }

    @Override
    public List<JSONObject> getMandatoryBaseInfo(String tenancy_id, int[] mandatoryCombIds) throws Exception {
        if(mandatoryCombIds == null || mandatoryCombIds.length == 0){
            return new ArrayList<JSONObject>();
        }
        StringBuilder sql = new StringBuilder();
        sql.append(" select hicd.id as hicd_id,hig.id as mid, hig.item_group_name as title ");
        sql.append(" , hicd.combo_num as selnum");
        sql.append(" from hq_item_combo_details hicd ");
        sql.append(" join hq_item_group hig on hig.id=hicd.details_id and hig.valid_state='1' ");
        sql.append(" where hicd.id in (" + SqlUtil.getInnerStr(mandatoryCombIds) + ") ");
        sql.append(" and hicd.valid_state='1' ");
        sql.append(" and hicd.tenancy_id='" + tenancy_id + "' order by  hicd.id asc");
        return this.query4Json(tenancy_id, sql.toString());
    }

    @Override
    public List<JSONObject> getGroupDetails(String tenancy_id, int[] groupIds) throws Exception {
        if(groupIds == null || groupIds.length == 0){
            return new ArrayList<JSONObject>();
        }
        StringBuilder sql = new StringBuilder();
        sql.append(" select DISTINCT higd.item_id as id, hii.item_code as dishsno, hii.item_name as name ");
        sql.append(" , higd.item_unit_id as duid, higd.quantity_limit as maxnum, higd.makeup_money as aprice ");
        sql.append(" , higd.item_group_id as rpdid,higd.details_order ");
        sql.append(" from hq_item_group_details higd ");
        sql.append(" join hq_item_group hig on hig.id=higd.item_group_id and hig.valid_state='1' ");
        sql.append(" join hq_item_combo_details hicd on hicd.details_id=hig.id and hicd.is_itemgroup='Y' ");
        sql.append(" join hq_item_info hii on hii.id=higd.item_id and hii.valid_state='1'  ");
        sql.append(" where higd.item_group_id in (" + SqlUtil.getInnerStr(groupIds) + ") ");
        sql.append(" and higd.tenancy_id='" + tenancy_id + "' order by higd.details_order ");
        return this.query4Json(tenancy_id, sql.toString());
    }

	@Override
	public List<JSONObject> getTimePrice(String tenancyId, int storeId) throws Exception
	{
		Timestamp currentTime = DateUtil.currentTimestamp();
		int week = DateUtil.dayForWeek(DateUtil.formatDate(currentTime));
		String time = DateUtil.getDateHHMM(currentTime);
		String date = DateUtil.formatDate(currentTime);
		
		StringBuilder sql = new StringBuilder("select tp.id,tp.startdate,tp.enddate,tp.sarttime,tp.endtime,tp.running_cycle from hq_time_price as tp left join hq_time_price_org po on tp.id=po.time_price_id and tp.tenancy_id=po.tenancy_id where po.tenancy_id=? and po.store_id=? and tp.valid_state='1' ");
		sql.append("and tp.startdate <='"+date+"' and tp.enddate>='"+date+"' and tp.sarttime<='"+time+"' and tp.endtime>='"+time+"' and tp.running_cycle like '%0"+week+"%'");
		return this.query4Json(tenancyId, sql.toString(), new Object[]
		{ tenancyId, storeId });
	}

	@Override
	public JSONObject getTableInfoByTableCode(String tenancyId, int storeId, String tableCode) throws Exception
	{
		StringBuilder sql = new StringBuilder("select * from tables_info ti where ti.tenancy_id=? and ti.organ_id=? and ti.table_code=? and ti.valid_state='1'");
		List<JSONObject> list = this.query4Json(tenancyId, sql.toString(), new Object[]
		{ tenancyId, storeId, tableCode });

		if (null != list && 0 < list.size())
		{
			return list.get(0);
		}
		return null;
	}

	@Override
	public List<JSONObject> getTableItemForPropertyId(String tenancyId, int storeId, String propertyId) throws Exception
	{
		StringBuilder sql = new StringBuilder("select * from hq_table_property_item ti where ti.tenancy_id=? and ti.organ_id=? and ti.table_property_id=? and ti.dishes_sign='0'");
		return this.query4Json(tenancyId, sql.toString(), new Object[]
		{ tenancyId, storeId, Integer.parseInt(propertyId) });
	}
	
	@Override
	public List<JSONObject> selectDetailsInfo(String tenancy_id, int[] combo_item_ids) throws Exception
	{
		if (combo_item_ids == null || combo_item_ids.length == 0)
		{
			return new ArrayList<JSONObject>();
		}
		StringBuilder sql = new StringBuilder();
		sql.append(" select hii.item_name as name, hicd.combo_num as number, hicd.is_itemgroup ");
		sql.append(" , hicd.details_id as id, hicd.item_unit_id as duid, hii.item_code as dishsno ");
		sql.append(" , hicd.iitem_id as item_id, hicd.id as hicd_id ");
		sql.append(" from hq_item_combo_details hicd ");
		sql.append(" left join hq_item_combo_pricesystem hicp on (hicp.combo_details_id=hicd.id and hicp.price_system='1' and hicp.chanel='WX02') ");
		sql.append(" left join hq_item_info hii on hii.id=hicd.details_id and hii.valid_state='1' ");
		sql.append(" where hicd.iitem_id in (" + SqlUtil.getInnerStr(combo_item_ids) + ") ");
		sql.append(" and hicd.valid_state='1' ");
		sql.append(" and hicd.tenancy_id='" + tenancy_id + "' order by hicd.iitem_id,hicd.combo_order,hicd.id");
		return this.query4Json(tenancy_id, sql.toString());
	}

	@Override
	public List<JSONObject> getDishMemos(String tenancyId, Integer storeId) throws Exception
	{
		StringBuilder sql = new StringBuilder();
		sql.append("select td.item_id,it.father_id,tf.name as father_name,it.id,it.name ");
		sql.append("from hq_item_taste_details td  ");
		sql.append("left join item_taste it on it.id=td.taste_id ");
		sql.append("left join item_taste tf on tf.tenancy_id=it.tenancy_id and tf.id=it.father_id ");
		sql.append("left join item_taste_org tg on tg.tenancy_id=it.tenancy_id and tg.teste_id=it.id ");
		sql.append("where tg.tenancy_id=? and tg.store_id=? and it.valid_state='1' ");
		sql.append("and EXISTS(select hd.item_id from hq_item_menu_details hd left join hq_item_menu_organ hg on hg.tenancy_id=hd.tenancy_id and hg.item_menu_id=hd.item_menu_id and hg.store_id=tg.store_id where hd.item_id=td.item_id)");

		return this.query4Json(tenancyId, sql.toString(), new Object[]
		{ tenancyId, storeId });
	}
}
