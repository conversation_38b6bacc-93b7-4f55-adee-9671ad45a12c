package com.tzx.framework.common.newprint;

import java.awt.BasicStroke;
import java.awt.Color;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.RenderingHints;
import java.awt.geom.AffineTransform;
import java.awt.image.BufferedImage;
import java.awt.image.ConvolveOp;
import java.awt.image.Kernel;
import java.awt.print.PageFormat;
import java.awt.print.Printable;
import java.awt.print.PrinterException;


public class NewPrintable implements Printable
{
	private BufferedImage bi;
	public NewPrintable(BufferedImage bi)
	{
		this.bi = bi;
		
	}
	public void sharpen() {  
        if (bi == null)  
            return;  
        float[] data = {   
                -1.0f, -1.0f, -1.0f,  
                -1.0f, 9.0f, -1.0f,  
                -1.0f, -1.0f, -1.0f   
        };  
        applyFilter(data);  
    } 
	public void applyFilter(float[] data) {  
        if (bi == null)  
            return; //如果bufImage为空则直接返回  
        Kernel kernel = new Kernel(3, 3, data);    
        ConvolveOp imageOp=new ConvolveOp(kernel,ConvolveOp.EDGE_NO_OP, null);  //创建卷积变换操作对象  
        BufferedImage filteredBufImage = new BufferedImage(bi.getWidth(),bi.getHeight(),BufferedImage.TYPE_INT_ARGB); //过滤后的缓冲区图像  
        imageOp.filter(bi, filteredBufImage);//过滤图像，目标图像在filteredBufImage  
        bi = filteredBufImage; //让用于显示的缓冲区图像指向过滤后的图像  
      
    }  
	

	@Override
	public int print(Graphics graphics, PageFormat pageFormat, int pageIndex) throws PrinterException
	{
		Graphics2D g2d = (Graphics2D)graphics;
//		float[]   dash1   =   {0.1f};
//		g2d.setStroke(new   BasicStroke(0.1f,   BasicStroke.CAP_BUTT,   BasicStroke.JOIN_MITER,   1f, dash1,   1f));    
		//g2d.setStroke(new BasicStroke(0.1f));
		g2d.setStroke(new BasicStroke(0f, BasicStroke.CAP_BUTT, BasicStroke.JOIN_MITER)); 
		//g2d.setRenderingHint(hintKey, hintValue);
		g2d.setBackground(Color.WHITE);
		g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING,RenderingHints.VALUE_ANTIALIAS_ON);    
	    g2d.setRenderingHint(RenderingHints.KEY_STROKE_CONTROL,RenderingHints.VALUE_STROKE_DEFAULT);    
	    AffineTransform at = new AffineTransform();
	    at.scale(0.37, 0.35); 
	    g2d.drawImage(bi, at, null);
	   // g2d.drawImage(bi , 0, 0,bi.getWidth(),bi.getHeight(), null);
	    
	    //g2d.drawBytes(data, offset, length, x, y);
		return 0;
	}
	
}
