package com.tzx.pos.bo.douyin;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.HttpURLConnection;
import java.net.URL;

/**
 * <AUTHOR> at 2021-12-16
 */
public class DouyinOpenUtil {

    private static final Logger logger = LoggerFactory.getLogger(DouyinOpenUtil.class);

    public static String getEncryptedDataByQRCode(String shortUrl) {
        try {
            URL url=new URL(shortUrl);
            HttpURLConnection urlConnection= (HttpURLConnection) url.openConnection();
            urlConnection.setInstanceFollowRedirects(false);
            String rawUrl = urlConnection.getHeaderField("location");
            String[] params = rawUrl.split("\\?")[1].split("&");
            for (String param : params) {
                String[] keyValue = param.split("=");
                if ("object_id".equals(keyValue[0])) {
                    return keyValue[1];
                }
            }
        } catch (Exception e) {
            logger.error("抖音短连接解析错误", e);
        }
        return null;
    }
}
