package com.tzx.base.listener;

import com.tzx.clientorder.wxorder.base.util.WxOrderUtil;
import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.pos.bo.PosService;
import com.tzx.pos.bo.imp.PosServiceImp;
import org.apache.log4j.Logger;

import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;
import java.util.Map;

/**
 * 2017-10-18.
 */
public class ApplicationListener implements ServletContextListener {

    private static final Logger logger = Logger.getLogger(ApplicationListener.class);
    private Logger tip = Logger.getLogger("tip");

    @Override
    public void contextInitialized(ServletContextEvent servletContextEvent) {
        try {
            Map<String, String> systemMap = Constant.getSystemMap();
            String tenentId = systemMap.get("tenent_id");
            String storeId = systemMap.get("store_id");
            //通知微信端，该版本支持微信后付
            WxOrderUtil.updateWechatAfterSet(tenentId, storeId);
            tip.info("LocalServer服务启动成功。");    // spring容器初始化成功
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    @Override
    public void contextDestroyed(ServletContextEvent servletContextEvent) {
        logger.info("---容器关闭！---");
        tip.info("LocalServer服务启动失败。");
        try{
            Map<String, String> systemMap = Constant.getSystemMap();

            if (!systemMap.containsKey("tenent_id") || !systemMap.containsKey("store_id") || "".equals(systemMap.get("tenent_id")) || "".equals(systemMap.get("store_id")))
            {
                logger.info("参数为空");
                return;
            }

            String tenentId = systemMap.get("tenent_id");
            String storeId = systemMap.get("store_id");

            PosService baseService = (PosServiceImp) SpringConext.getApplicationContext().getBean(PosService.NAME);
            baseService.updatePrintSerialByMap(tenentId, Integer.parseInt(storeId));

        }catch (Exception e){
            e.printStackTrace();
        }
    }

}
