package com.tzx.pos.register.thread;

import java.util.concurrent.LinkedBlockingQueue;

import org.springframework.stereotype.Controller;

import net.sf.json.JSONObject;

@Controller("UploadQueen")
public class UploadQueen extends LinkedBlockingQueue<JSONObject> {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	static int size = 5;

	public UploadQueen() {

		super(size);

	}

	public UploadQueen(int size) {

		super(size);

	}

}
