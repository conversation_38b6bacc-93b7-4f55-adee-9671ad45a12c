package com.tzx.pos.base.controller;

import com.tzx.base.cache.Cache;
import com.tzx.base.cache.CacheManager;
import com.tzx.base.common.util.PosPropertyUtil;
import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.util.HttpUtil;
import com.tzx.framework.common.util.ObjectMapper;
import com.tzx.framework.common.util.Tools;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.pos.base.dao.ThirdCouponsDao;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.base.util.SpringContextUtils;
import com.tzx.pos.po.springjdbc.dao.PosDishDao;
import net.sf.json.JSONObject;
import org.apache.log4j.Logger;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: wj
 * @Description:
 * @Date: Created in 2018/1/15 下午12:04
 */

public class ThirdCouponsRunnable implements Runnable{

    private final String postUrl = "/orderRest/post";
    private static final Logger logger	= Logger.getLogger(ThirdCouponsRunnable.class);

    @Override
    public void run() {

        String tenancyId = null;
        if(Constant.systemMap.containsKey("tenent_id"))
        {
            tenancyId = Constant.systemMap.get("tenent_id");
        }

        Integer storeId = Integer.valueOf(Constant.systemMap.get("store_id"));
        if(Constant.systemMap.containsKey("store_id"))
        {
            storeId = Integer.valueOf(Constant.systemMap.get("store_id"));
        }

        if (Tools.isNullOrEmpty(tenancyId)||Tools.isNullOrEmpty(storeId))
        {
            logger.info("参数为空");
            return;
        }

        DBContextHolder.setTenancyid(tenancyId);
        try {
            List<JSONObject> list = null;
            if ((list = searchAllCoupon(tenancyId, storeId)) != null && !list.isEmpty() && monitorNetWork(list.remove(0), tenancyId, storeId)) {
                for (JSONObject object : list) {
                    try {

                        boolean isSuccess = pushResult(object, tenancyId, storeId);
                        if (!isSuccess) {
                            break;
                        }
                    } catch (Exception e) {
                        break;
                    }
                }
            }
        }catch (Exception exception){
            logger.error(exception.getMessage());
        }

    }

    /**
     * 监控网络情况
     */
    private boolean monitorNetWork(JSONObject object, String tenancyId, Integer storeId) {
        return pushResult(object, tenancyId, storeId);
    }

    private List<JSONObject> searchAllCoupon(String tenancyId, Integer storeId) throws Exception{

        ThirdCouponsDao dao = (ThirdCouponsDao) SpringContextUtils.getBean(ThirdCouponsDao.NAME);
        PosDishDao dishDao = (PosDishDao) SpringContextUtils.getBean(PosDishDao.NAME);

        String reportDate = null;
        reportDate = DateUtil.formatDate(dishDao.getReportDate(tenancyId, storeId));
        return dao.selectThirdCoupons(tenancyId, storeId, reportDate);
    }

    private Data stringToData(String  str) throws Exception
    {
        ObjectMapper objectMapper = new ObjectMapper();
        return  objectMapper.readValue(str, Data.class);
    }

    private boolean pushResult(JSONObject object, String tenancyId, Integer storeId){

        ThirdCouponsDao dao = (ThirdCouponsDao) SpringContextUtils.getBean(ThirdCouponsDao.NAME);

        String url= PosPropertyUtil.getMsg("saas.url") + postUrl;
        String billNum = object.optString("bill_num");
        String payment_id = object.optString("payment_id");

        JSONObject obj = new JSONObject();
        String code = object.optString("coupons_code");
        obj.put("couponCode",code);
        List<JSONObject> jsonList = new ArrayList<JSONObject>();
        jsonList.add(obj);
        Data data = Data.get();
        data.setType(Type.THIRD_COUPONS);
        data.setOper(Oper.trade_detail);
        data.setTenancy_id(tenancyId);
        data.setStore_id(storeId);
        data.setData(jsonList);

        logger.info("请求地址: "+url+"      请求参数: "+JSONObject.fromObject(data).toString());
        String result= HttpUtil.sendPostRequest(url, JSONObject.fromObject(data).toString());
        logger.info("返回参数" + result);

        Data responseData = null;
        try {
            responseData = this.stringToData(result);
        } catch (Exception e) {
            logger.error("解析出错");
            e.printStackTrace();
            return false;
        }

        if (com.tzx.pos.base.Constant.CODE_SUCCESS == responseData.getCode()) {
            JSONObject responseJson = JSONObject.fromObject(responseData.getData().get(0));
            // 响应数据存到表中

            double coupon_buy_price = responseJson.optDouble("userPay");    // 用户实付
            double due = responseJson.optDouble("netRevenue");      // 账单净收
            double tenancy_assume = responseJson.optDouble("cost");   //商家承担优惠(促销金额)
            double third_assume = responseJson.optDouble("thirdCost");  //第三方优惠
            double third_fee = responseJson.optDouble("serviceFee");  //服务费

            String couponCode = responseJson.optString("couponCode");

            try {
                dao.updateThirdCoupons(coupon_buy_price, due, tenancy_assume, third_assume, third_fee, billNum, couponCode, tenancyId, storeId,payment_id, 1);
            } catch (Exception e) {
                logger.error(e.getMessage());
                e.printStackTrace();
                return false;
            }

        }else {
            logger.error(responseData.getMsg());
            // 缓存数据
            if (CacheManager.hasCache(code)){
                Cache cache = CacheManager.getCacheInfo(code);
                int count = (int)cache.getValue();

                if (count >= 2){

                    try {
                        dao.updateThirdCoupons(0, 0, 0, 0, 0, billNum, code, tenancyId, storeId, payment_id,2);
                        CacheManager.clearOnly(code);
                    } catch (Exception e) {
                        logger.error(e.getMessage());
                        e.printStackTrace();
                        return false;
                    }
                }else {
                    cache.setValue(count + 1);
                    CacheManager.clearOnly(code);
                    CacheManager.putCache(code, cache);
                }
            }else {
                Cache cache = new Cache();
                cache.setKey(code);
                cache.setValue(1);
                cache.setTimeOut(System.currentTimeMillis() + 60*1000*60);
                CacheManager.putCache(code, cache);
            }

            return false;
        }
        return true;
    }
}
