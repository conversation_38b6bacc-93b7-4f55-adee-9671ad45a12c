package com.tzx.framework.common.util;


import java.util.Random;
import java.util.concurrent.ConcurrentHashMap;


public class JavaDynamicCode {

	private static Random	random	= new Random();
	private static String 	reg     = "[^0-9]";
	private static ConcurrentHashMap<String, Integer> sssmap = new ConcurrentHashMap<String, Integer>();
	
	static{
		sssmap.put("a00",2);sssmap.put("a01",9);sssmap.put("a02",1);sssmap.put("a03",4);sssmap.put("a04",8);sssmap.put("a05",0);sssmap.put("a06",7);sssmap.put("a07",5);sssmap.put("a08",6);sssmap.put("a09",3);
		sssmap.put("a10",5);sssmap.put("a11",0);sssmap.put("a12",8);sssmap.put("a13",1);sssmap.put("a14",7);sssmap.put("a15",4);sssmap.put("a16",9);sssmap.put("a17",3);sssmap.put("a18",2);sssmap.put("a19",6);
		sssmap.put("a20",7);sssmap.put("a21",3);sssmap.put("a22",6);sssmap.put("a23",1);sssmap.put("a24",8);sssmap.put("a25",4);sssmap.put("a26",2);sssmap.put("a27",5);sssmap.put("a28",0);sssmap.put("a29",9);
		sssmap.put("a30",9);sssmap.put("a31",5);sssmap.put("a32",4);sssmap.put("a33",2);sssmap.put("a34",0);sssmap.put("a35",3);sssmap.put("a36",6);sssmap.put("a37",1);sssmap.put("a38",7);sssmap.put("a39",8);
		sssmap.put("a40",8);sssmap.put("a41",4);sssmap.put("a42",6);sssmap.put("a43",1);sssmap.put("a44",9);sssmap.put("a45",7);sssmap.put("a46",0);sssmap.put("a47",3);sssmap.put("a48",2);sssmap.put("a49",5);
		sssmap.put("a50",7);sssmap.put("a51",1);sssmap.put("a52",9);sssmap.put("a53",4);sssmap.put("a54",3);sssmap.put("a55",2);sssmap.put("a56",8);sssmap.put("a57",5);sssmap.put("a58",0);sssmap.put("a59",6);
		sssmap.put("a60",2);sssmap.put("a61",8);sssmap.put("a62",6);sssmap.put("a63",5);sssmap.put("a64",7);sssmap.put("a65",3);sssmap.put("a66",9);sssmap.put("a67",0);sssmap.put("a68",1);sssmap.put("a69",4);
		sssmap.put("a70",8);sssmap.put("a71",5);sssmap.put("a72",0);sssmap.put("a73",2);sssmap.put("a74",9);sssmap.put("a75",1);sssmap.put("a76",7);sssmap.put("a77",6);sssmap.put("a78",4);sssmap.put("a79",3);
		sssmap.put("a80",5);sssmap.put("a81",4);sssmap.put("a82",3);sssmap.put("a83",6);sssmap.put("a84",0);sssmap.put("a85",8);sssmap.put("a86",2);sssmap.put("a87",1);sssmap.put("a88",9);sssmap.put("a89",7);
		sssmap.put("a90",3);sssmap.put("a91",8);sssmap.put("a92",0);sssmap.put("a93",2);sssmap.put("a94",9);sssmap.put("a95",1);sssmap.put("a96",7);sssmap.put("a97",4);sssmap.put("a98",5);sssmap.put("a99",6);
		
		
		//sssmap.put("b00",2);sssmap.put("b01",9);sssmap.put("b02",1);sssmap.put("b03",4);sssmap.put("b04",8);sssmap.put("b05",0);sssmap.put("b06",7);sssmap.put("b07",5);sssmap.put("b08",6);sssmap.put("a09",3);
		sssmap.put("b02",0);sssmap.put("b09",1);sssmap.put("b01",2);sssmap.put("b04",3);sssmap.put("b08",4);sssmap.put("b00",5);sssmap.put("b07",6);sssmap.put("b05",7);sssmap.put("b06",8);sssmap.put("b03",9);
		
		//sssmap.put("b10",5);sssmap.put("b11",0);sssmap.put("b12",8);sssmap.put("b13",1);sssmap.put("b14",7);sssmap.put("b15",4);sssmap.put("b16",9);sssmap.put("b17",3);sssmap.put("b18",2);sssmap.put("a19",6);
		sssmap.put("b15",0);sssmap.put("b10",1);sssmap.put("b18",2);sssmap.put("b11",3);sssmap.put("b17",4);sssmap.put("b14",5);sssmap.put("b19",6);sssmap.put("b13",7);sssmap.put("b12",8);sssmap.put("b16",9);
		
		//sssmap.put("b20",7);sssmap.put("b21",3);sssmap.put("b22",6);sssmap.put("b23",1);sssmap.put("b24",8);sssmap.put("b25",4);sssmap.put("b26",2);sssmap.put("b27",5);sssmap.put("b28",0);sssmap.put("a29",9);
		sssmap.put("b27",0);sssmap.put("b23",1);sssmap.put("b26",2);sssmap.put("b21",3);sssmap.put("b28",4);sssmap.put("b24",5);sssmap.put("b22",6);sssmap.put("b25",7);sssmap.put("b20",8);sssmap.put("b29",9);
		
		//sssmap.put("b30",9);sssmap.put("b31",5);sssmap.put("b32",4);sssmap.put("b33",2);sssmap.put("b34",0);sssmap.put("b35",3);sssmap.put("b36",6);sssmap.put("b37",1);sssmap.put("b38",7);sssmap.put("a39",8);
		sssmap.put("b39",0);sssmap.put("b35",1);sssmap.put("b34",2);sssmap.put("b32",3);sssmap.put("b30",4);sssmap.put("b33",5);sssmap.put("b36",6);sssmap.put("b31",7);sssmap.put("b37",8);sssmap.put("b38",9);
		
		//sssmap.put("b40",8);sssmap.put("b41",4);sssmap.put("b42",6);sssmap.put("b43",1);sssmap.put("b44",9);sssmap.put("b45",7);sssmap.put("b46",0);sssmap.put("b47",3);sssmap.put("b48",2);sssmap.put("a49",5);
		sssmap.put("b48",0);sssmap.put("b44",1);sssmap.put("b46",2);sssmap.put("b41",3);sssmap.put("b49",4);sssmap.put("b47",5);sssmap.put("b40",6);sssmap.put("b43",7);sssmap.put("b42",8);sssmap.put("b45",9);
		
		//sssmap.put("b50",7);sssmap.put("b51",1);sssmap.put("b52",9);sssmap.put("b53",4);sssmap.put("b54",3);sssmap.put("b55",2);sssmap.put("b56",8);sssmap.put("b57",5);sssmap.put("b58",0);sssmap.put("a59",6);
		sssmap.put("b57",0);sssmap.put("b51",1);sssmap.put("b59",2);sssmap.put("b54",3);sssmap.put("b53",4);sssmap.put("b52",5);sssmap.put("b58",6);sssmap.put("b55",7);sssmap.put("b50",8);sssmap.put("b56",9);
		
		//sssmap.put("b60",2);sssmap.put("b61",8);sssmap.put("b62",6);sssmap.put("b63",5);sssmap.put("b64",7);sssmap.put("b65",3);sssmap.put("b66",9);sssmap.put("b67",0);sssmap.put("b68",1);sssmap.put("a69",4);
		sssmap.put("b62",0);sssmap.put("b68",1);sssmap.put("b66",2);sssmap.put("b65",3);sssmap.put("b67",4);sssmap.put("b63",5);sssmap.put("b69",6);sssmap.put("b60",7);sssmap.put("b61",8);sssmap.put("b64",9);
		
		//sssmap.put("b70",8);sssmap.put("b71",5);sssmap.put("b72",0);sssmap.put("b73",2);sssmap.put("b74",9);sssmap.put("b75",1);sssmap.put("b76",7);sssmap.put("b77",6);sssmap.put("b78",4);sssmap.put("a79",3);
		sssmap.put("b78",0);sssmap.put("b75",1);sssmap.put("b70",2);sssmap.put("b72",3);sssmap.put("b79",4);sssmap.put("b71",5);sssmap.put("b77",6);sssmap.put("b76",7);sssmap.put("b74",8);sssmap.put("b73",9);
		
		//sssmap.put("b80",5);sssmap.put("b81",4);sssmap.put("b82",3);sssmap.put("b83",6);sssmap.put("b84",0);sssmap.put("b85",8);sssmap.put("b86",2);sssmap.put("b87",1);sssmap.put("b88",9);sssmap.put("a89",7);
		sssmap.put("b85",0);sssmap.put("b84",1);sssmap.put("b83",2);sssmap.put("b86",3);sssmap.put("b80",4);sssmap.put("b88",5);sssmap.put("b82",6);sssmap.put("b81",7);sssmap.put("b89",8);sssmap.put("b87",9);
		
		//sssmap.put("b90",3);sssmap.put("b91",8);sssmap.put("b92",0);sssmap.put("b93",2);sssmap.put("b94",9);sssmap.put("b95",1);sssmap.put("b96",7);sssmap.put("b97",4);sssmap.put("b98",5);sssmap.put("a99",6);
		sssmap.put("b93",0);sssmap.put("b98",1);sssmap.put("b90",2);sssmap.put("b92",3);sssmap.put("b99",4);sssmap.put("b91",5);sssmap.put("b97",6);sssmap.put("b94",7);sssmap.put("b95",8);sssmap.put("b96",9);
	}

	
	public static String encryptDynamicCode(String id,String card_code){
		Long begin = System.currentTimeMillis();
		StringBuilder sbcode = new StringBuilder();
		int id_length = id.length();
		int order_type = random.nextInt(9);
		int count = order_type+id_length;
		if(id_length<=10)
		{
			sbcode.append(id);
			for(int i=0;i<(10-id_length);i++)
			{
				//int rad = random.nextInt(9);
				//count += rad;
				sbcode.append(random.nextInt(9));
			}
			String new_code = sbcode.toString();
			sbcode.setLength(0);
			sbcode.append(order_type);
			String orderkey = "a"+ order_type;
			for(int j=0;j<10;j++)
			{
				String key = orderkey+j;
				int nownum = Integer.parseInt((String.valueOf(new_code.charAt(sssmap.get(key)))));
				count += nownum;
				sbcode.append(nownum);
			}
			sbcode.append(id_length);
			sbcode.append(count%10);
		}
		else
		{
			sbcode.append("0000000000000");
		}
		Long xh = System.currentTimeMillis() - begin;
		System.out.println("加密耗时："+xh);
		return sbcode.toString();
	}
	
	public static String unEncryptDynamicCode(String dynamicCode){
		Long begin = System.currentTimeMillis();
		
		StringBuilder id = new StringBuilder();
		dynamicCode = dynamicCode.replaceAll(reg,"");
		if(dynamicCode.length()==13)
		{
			int count = Integer.parseInt((String.valueOf(dynamicCode.charAt(0))));
			String type = "b"+count;
			int ws  = Integer.parseInt((String.valueOf(dynamicCode.charAt(11))));
			count += ws;
			int yzm = Integer.parseInt((String.valueOf(dynamicCode.charAt(12))));
			if(ws==0)
			{
				//报错  位数为0
			}
			for(int i=0;i<ws;i++)
			{
				String key = type+i;
				//System.out.println(key);
				int nownum =  Integer.parseInt((String.valueOf(dynamicCode.charAt(sssmap.get(key)+1))));
				id.append(nownum);
			}
			for(int i=1;i<11;i++)
			{
				int nownum = Integer.parseInt((String.valueOf(dynamicCode.charAt(i))));
				count += nownum;
			}
			
			if(count%10!=yzm)
			{
				throw new RuntimeException("验证错误"); 
				//System.out.println("验证错误");
			}
		}
		else
		{
			throw new RuntimeException("验证错误"); 
			// 报错  该验证码有误,请重试 
		}
		
		Long xh = System.currentTimeMillis() - begin;
		System.out.println("jie密耗时："+xh);
		return id.toString();
	}
	
	public static void main(String[] args) {
		int cs = 0;
		int cwsc = 0;
		for(int i= 0 ;i<200000;i++)
		{
			int ws = random.nextInt(10);
			if(ws>1)
			{
				cs ++;
				String id = "";
				for(int j=0;j<ws;j++)
				{
					id += random.nextInt(9);
				}
				String jm = encryptDynamicCode(id,"4324324234");
				//System.out.println("加密码："+jm);
				String jjm = unEncryptDynamicCode(jm);
				//System.out.println("解密码："+jjm);
				if(!id.equals(jjm))
				{
					cwsc ++;
					System.out.println("解密码错误：id:"+id+";解密码："+jjm);
				}
				
			}
		}
		System.out.println("次数："+cs+";错误次数："+cwsc);
		
	}

	
	
	
	
}
