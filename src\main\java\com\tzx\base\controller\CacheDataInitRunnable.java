package com.tzx.base.controller;

import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.base.bo.CacheManagerService;
import com.tzx.base.bo.imp.CacheManagerServiceImpl;

import org.apache.log4j.Logger;

import java.util.Map;

/**
 * Created by kevin on 2017-06-26.
 * 缓存业务数据初始化线程
 */
public class CacheDataInitRunnable implements Runnable  {

    private static final Logger logger = Logger.getLogger(CacheDataInitRunnable.class);

    @Override
    public void run() {
        // TODO Auto-generated method stub
        try{
            Map<String, String> systemMap = Constant.getSystemMap();

            if (!systemMap.containsKey("tenent_id") || !systemMap.containsKey("store_id") || "".equals(systemMap.get("tenent_id")) || "".equals(systemMap.get("store_id")))
            {
                logger.info("参数为空");
                return;
            }

            while(SpringConext.getApplicationContext() == null){
                Thread.sleep(1000);
            }

            String tenentId = systemMap.get("tenent_id");
            String storeId = systemMap.get("store_id");
            DBContextHolder.setTenancyid(tenentId);

            CacheManagerService baseService = (CacheManagerServiceImpl) SpringConext.getApplicationContext().getBean(CacheManagerService.NAME);
            baseService.loadBusinessData(tenentId, Integer.parseInt(storeId));
            logger.info("缓存业务数据初始化成功！");

        }catch(Exception e){
            e.printStackTrace();
            logger.info("初始化缓存业务数据失败...");
        }
    }
}
