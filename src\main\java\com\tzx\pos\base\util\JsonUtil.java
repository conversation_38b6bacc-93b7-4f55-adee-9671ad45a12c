/** 
 * @(#)CodeFormat.java    1.0   2017-06-24
 * 
 * Copyright 北京普照天星科技有限公司. All rights reserved. 
 * PROPRIETARY/CONFIDENTIAL. Use is subject to license terms. 
 */
package com.tzx.pos.base.util;

import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.entity.Pagination;
import com.tzx.framework.common.util.CommonUtil;
import com.tzx.framework.common.util.Tools;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.bo.dto.PosBill;
import com.tzx.pos.bo.dto.PosBillItem;
import com.tzx.pos.bo.dto.PosTableFind;

import common.Logger;

/**
 * JSON工具类.
 *
 * <AUTHOR> email:<EMAIL>
 * @version 1.0 2017-06-24
 * @see
 * @since JDK7.0
 * @update
 */
public class JsonUtil {

	private static final Logger logger = Logger.getLogger(JsonUtil.class);

	/**
	 * @Description JsonObject对象转Data对象
	 * @param jsonObj
	 *            JSONObject对象
	 * @return
	 * @exception Exception
	 *                系统异常
	 * <AUTHOR> email:<EMAIL>
	 * @version 1.0 2017-06-24
	 * @see
	 */
	public static Data JsonToData(JSONObject jsonObj) {
		Data data = new Data();
		if (jsonObj.has("t")) {
			String t = jsonObj.optString("t");
			if(Tools.hv(t)&& !"null".equals(t))
			{
				data.setT(jsonObj.getLong("t"));
			}
			else
			{
				data.setT(0L);;
			}
		}
		if (jsonObj.has("secret")) {
			data.setSecret(jsonObj.getString("secret"));
		}
		if (jsonObj.has("msg")) {
			data.setMsg(jsonObj.getString("msg"));
		}
		if (jsonObj.has("data")) {
			net.sf.json.JSONArray jsonArray = jsonObj.optJSONArray("data");
			List<Map<String, Object>> dataList = new ArrayList<Map<String, Object>>();
			if (jsonArray.size() > 0) {
				for (int i = 0; i < jsonArray.size(); i++) {
					Map map = new HashMap<String, Object>();
					JSONObject jsonObject = jsonArray.getJSONObject(i);
					Iterator it = jsonObject.keys();
					while (it.hasNext()) {
						String key = (String) it.next();
						map.put(key, jsonObject.get(key));
					}
					dataList.add(map);
				}
			}
			data.setData(dataList);
		}
		if (jsonObj.has("code")) {
			data.setCode(jsonObj.getInt("code"));
		}
		if (jsonObj.has("oper")) {
			data.setOper(Oper.valueOf(jsonObj.getString("oper")));
		}
		if (jsonObj.has("type")) {
			data.setType(Type.valueOf(jsonObj.getString("type")));
		}
		if (jsonObj.has("source")) {
			data.setSource(jsonObj.getString("source"));
		}
		if (jsonObj.has("success")) {
			data.setSuccess(jsonObj.getBoolean("success"));
		}
		if (jsonObj.has("pagination"))
		{
			Pagination pagination = new Pagination();
			String paginationStr = jsonObj.optString("pagination");
			if (Tools.hv(paginationStr) && !"null".equals(paginationStr))
			{
				JSONObject jsonObject = JSONObject.fromObject(paginationStr);
				if (jsonObject.has("pageno")) pagination.setPageno(jsonObject.getInt("pageno"));
				if (jsonObject.has("pagesize")) pagination.setPagesize(jsonObject.getInt("pagesize"));
				if (jsonObject.has("totalcount")) pagination.setTotalcount(jsonObject.getInt("totalcount"));
				if (jsonObject.has("orderby")) pagination.setOrderby(jsonObject.getString("orderby"));
				if (jsonObject.has("asc")) pagination.setAsc(jsonObject.getBoolean("asc"));
			}
			data.setPagination(pagination);
		}
		if (jsonObj.has("tenancy_id")) {
			data.setTenancy_id(jsonObj.getString("tenancy_id"));
		}
		if (jsonObj.has("store_id")) {
			data.setStore_id(jsonObj.optInt("store_id"));
		}
		return data;
	}
	
	public static Data JsonToData(JSONObject jsonObj, String userAgent)
	{
		Data data = JsonToData(jsonObj);
		if (Tools.isNullOrEmpty(data.getSource()) && "APP".equals(data.getSecret()) && "android".equals(userAgent.toLowerCase()))
		{
			data.setSource(SysDictionary.SOURCE_ANDROID_PAD);
		}
		return data;
	}

	/**
	 * @Description Data对象装JSON格式字符串
	 * @param data
	 *            Data对象
	 * @return
	 * @exception Exception
	 *                系统异常
	 * <AUTHOR> email:<EMAIL>
	 * @version 1.0 2017-06-26
	 * @see
	 */
	public static String DataToJson(Data data) {
		boolean tmpBo = false;
		StringBuilder sb = new StringBuilder();
		try {
			// 获取Data元素数据
			StringBuilder sb_data = new StringBuilder();
			sb_data.append("[");
			if (data.getData() != null && data.getData().size() > 0) {
				List<Map<String, Object>> maps = (List<Map<String, Object>>) data.getData();
				for (int i = 0; i < maps.size(); i++) {
					if (maps.get(i) instanceof JSONObject) {
						sb_data.append("{");
						Map<String, Object> map = maps.get(i);
						// 遍历Map对象
						tmpBo = false;
						for (Map.Entry<String, Object> entry : map.entrySet()) {
							if (entry.getValue() instanceof String) {
								sb_data.append("\"");
								sb_data.append(entry.getKey());
								sb_data.append("\":\"");
								sb_data.append(entry.getValue());
								sb_data.append("\",");
							} else {
								sb_data.append("\"");
								sb_data.append(entry.getKey());
								sb_data.append("\":");
								sb_data.append(entry.getValue());
								sb_data.append(",");
							}
							tmpBo = true;
						}
						// 删除最后一个逗号
						if (tmpBo == true)
							sb_data.deleteCharAt(sb_data.length() - 1);
						if (i == maps.size() - 1) {
							sb_data.append("}");
						} else {
							sb_data.append("},");
						}
					} else {
						// 桌台数据（Table_State）
						if (maps.get(i) instanceof com.tzx.pos.bo.dto.PosTableFind) {
							PosTableFind posTableFind = (PosTableFind) maps.get(i);
							sb_data.append("{\"service_id\":\"");
							sb_data.append(posTableFind.getService_id());
							sb_data.append("\",\"table_code\":\"");
							sb_data.append(posTableFind.getTable_code());
							sb_data.append("\",\"lock_pos_num\":\"");
							sb_data.append(posTableFind.getLock_pos_num());
							sb_data.append("\",\"printtimes\":\"");
							sb_data.append(posTableFind.getPrinttimes());
							sb_data.append("\",\"state\":\"");
							sb_data.append(posTableFind.getState());
							sb_data.append("\",\"business_area_id\":\"");
							sb_data.append(posTableFind.getBusiness_area_id());
							sb_data.append("\",\"stable\":\"\",\"");
							sb_data.append(posTableFind.getStable());
							sb_data.append("\":\"\",\"lock_opt_num\":\"");
							sb_data.append(posTableFind.getLock_opt_num());
							sb_data.append("\",\"payment_state\":\"");
							sb_data.append(posTableFind.getPayment_state());
							sb_data.append("\",\"table_property_id\":\"");
							sb_data.append(posTableFind.getTable_property_id());
							sb_data.append("\",\"table_name\":\"");
							sb_data.append(posTableFind.getTable_name());
							sb_data.append("\",\"guest\":\"");
							sb_data.append(posTableFind.getGuest());
							sb_data.append("\",\"seat_counts\":\"");
							sb_data.append(posTableFind.getSeat_counts());
							sb_data.append("\",\"table_fw\":\"");
							sb_data.append(posTableFind.getTable_fw());
							sb_data.append("\",\"billno\":\"");
							sb_data.append(posTableFind.getBillno());
							sb_data.append("\",\"opentabletime\":\"");
							sb_data.append(posTableFind.getOpentabletime());
							sb_data.append("\"}");
						} else if (maps.get(i) instanceof com.tzx.pos.bo.dto.PosBill) {
							// 下单返回数据
							PosBill posBill = (PosBill) maps.get(i);
							sb_data.append("{\"detaillist\":");
							sb_data.append("[");
							if (posBill.getDetaillist() != null && posBill.getDetaillist().size() > 0) {
								for (int m = 0; m < posBill.getDetaillist().size(); m++) {
									sb_data.append(JSONObject.fromObject(posBill.getDetaillist().get(m)).toString());
									if (m != posBill.getDetaillist().size() - 1) {
										sb_data.append(",");
									}
								}
							}
							sb_data.append("],");
							sb_data.append("\"service_id\":\"");
							sb_data.append(posBill.getService_id() == null ? "" : posBill.getService_id());
							sb_data.append("\",\"fictitious_table\":\"");
							sb_data.append(posBill.getFictitious_table());
							sb_data.append("\",\"remark\":\"");
							sb_data.append(posBill.getRemark() == null ? "" : posBill.getRemark());
							sb_data.append("\",\"opentable_time\":\"");
							sb_data.append(posBill.getOpentable_time() == null ? "" : posBill.getOpentable_time());
							sb_data.append("\",\"service_name\":\"");
							sb_data.append(posBill.getService_name() == null ? "" : posBill.getService_name());
							sb_data.append("\",\"payment_time\":\"");
							sb_data.append(posBill.getPayment_time() == null ? "" : posBill.getPayment_time());
							sb_data.append("\",\"invoicelist\":");
							sb_data.append("[");
							if (posBill.getInvoicelist() != null && posBill.getInvoicelist().size() > 0) {
								for (int m = 0; m < posBill.getInvoicelist().size(); m++) {
									sb_data.append(JSONObject.fromObject(posBill.getInvoicelist().get(m)).toString());
									if (m != posBill.getInvoicelist().size() - 1) {
										sb_data.append(",");
									}
								}
							}
							sb_data.append("]");
							sb_data.append(",\"order_num\":\"");
							sb_data.append(posBill.getOrder_num() == null ? "" : posBill.getOrder_num());
							sb_data.append("\",\"discountk_amount\":\"");
							sb_data.append(posBill.getDiscount_amount() == null ? "" : posBill.getDiscount_amount());
							sb_data.append("\",\"serial_num\":\"");
							sb_data.append(posBill.getSerial_num() == null ? "" : posBill.getSerial_num());
							sb_data.append("\",\"batch_num\":\"");
							sb_data.append(posBill.getBatch_num() == null ? "" : posBill.getBatch_num());
							sb_data.append("\",\"discount_rate\":\"");
							sb_data.append(posBill.getDiscount_rate() == null ? "" : posBill.getDiscount_rate());
							sb_data.append("\",\"waiter_num\":\"");
							sb_data.append(posBill.getWaiter_num() == null ? "" : posBill.getWaiter_num());
							sb_data.append("\",\"table_code\":\"");
							sb_data.append(posBill.getTable_code() == null ? "" : posBill.getTable_code());
							sb_data.append("\",\"discount_case_id\":\"");
							sb_data.append(posBill.getDiscount_case_id() == null ? "" : posBill.getDiscount_case_id());
							sb_data.append("\",\"shift_id\":\"");
							sb_data.append(posBill.getShift_id() == null ? "" : posBill.getShift_id());
							sb_data.append("\",\"subtotal\":\"");
							sb_data.append(posBill.getSubtotal() == null ? "" : posBill.getSubtotal());
							sb_data.append("\",\"discount_amount\":\"");
							sb_data.append(posBill.getDiscount_amount() == null ? "" : posBill.getDiscount_amount());
							sb_data.append("\",\"difference\":\"");
							sb_data.append(posBill.getDifference() == null ? "" : posBill.getDifference());
							sb_data.append("\",\"discount_mode_id\":\"");
							sb_data.append(posBill.getDiscount_mode_id() == null ? "" : posBill.getDiscount_mode_id());
							sb_data.append("\",\"bill_state\":\"");
							sb_data.append(posBill.getBill_state() == null ? "" : posBill.getBill_state());
							sb_data.append("\",\"pos_num\":\"");
							sb_data.append(posBill.getPos_num() == null ? "" : posBill.getPos_num());
							sb_data.append("\",\"discount_case_name\":\"");
							sb_data.append(
									posBill.getDiscount_case_name() == null ? "" : posBill.getDiscount_case_name());
							sb_data.append("\",\"service_amount\":\"");
							sb_data.append(posBill.getService_amount() == null ? "" : posBill.getService_amount());
							sb_data.append("\",\"chanel\":\"");
							sb_data.append(posBill.getChanel() == null ? "" : posBill.getChanel());
							sb_data.append("\",\"bill_taste\":\"");
							sb_data.append(posBill.getBill_taste() == null ? "" : posBill.getBill_taste());
							sb_data.append("\",\"none_discount_amount\":\"");
							sb_data.append(
									posBill.getNone_discount_amount() == null ? "" : posBill.getNone_discount_amount());
							sb_data.append("\",\"bill_num\":\"");
							sb_data.append(posBill.getBill_num() == null ? "" : posBill.getBill_num());
							sb_data.append("\",\"subtotal2\":\"");
							sb_data.append(posBill.getSubtotal2() == null ? "" : posBill.getSubtotal2());
							sb_data.append("\",\"id\":\"");
							sb_data.append(posBill.getId() == null ? "" : posBill.getId());
							sb_data.append("\",\"batchList\":");
							sb_data.append("[");
							if (posBill.getBatchList() != null && posBill.getBatchList().size() > 0) {
								for (int m = 0; m < posBill.getBatchList().size(); m++) {
									sb_data.append(JSONObject.fromObject(posBill.getBatchList().get(m)).toString());
									if (m != posBill.getBatchList().size() - 1) {
										sb_data.append(",");
									}
								}
							}
							sb_data.append("]");
							sb_data.append(",\"copy_bill_num\":\"");
							sb_data.append(posBill.getCopy_bill_num() == null ? "" : posBill.getCopy_bill_num());
							sb_data.append("\",\"average_amount\":\"");
							sb_data.append(posBill.getAverage_amount() == null ? "" : posBill.getAverage_amount());
							sb_data.append("\",\"servicelist\":");
							sb_data.append("[");
							if (posBill.getServicelist() != null && posBill.getServicelist().size() > 0) {
								for (int m = 0; m < posBill.getServicelist().size(); m++) {
									sb_data.append(JSONObject.fromObject(posBill.getServicelist().get(m)).toString());
									if (m != posBill.getServicelist().size() - 1) {
										sb_data.append(",");
									}
								}
							}
							sb_data.append("]");
							sb_data.append(",\"print_count\":\"");
							sb_data.append(posBill.getPrint_count() == null ? "" : posBill.getPrint_count());
							sb_data.append("\",\"paymentlist\":");
							sb_data.append("[");
							if (posBill.getPaymentlist() != null && posBill.getPaymentlist().size() > 0) {
								for (int m = 0; m < posBill.getPaymentlist().size(); m++) {
									sb_data.append(JSONObject.fromObject(posBill.getPaymentlist().get(m)).toString());
									if (m != posBill.getPaymentlist().size() - 1) {
										sb_data.append(",");
									}
								}
							}
							sb_data.append("]");
							sb_data.append(",\"givi_amount\":\"");
							sb_data.append(posBill.getGivi_amount() == null ? "" : posBill.getGivi_amount());
							sb_data.append("\",\"maling_amount\":\"");
							sb_data.append(posBill.getMaling_amount() == null ? "" : posBill.getMaling_amount());
							sb_data.append("\",\"report_date\":\"");
							sb_data.append(posBill.getReport_date() == null ? "" : posBill.getReport_date());
							sb_data.append("\",\"guest_msg\":\"");
							sb_data.append(posBill.getGuest_msg() == null ? "" : posBill.getGuest_msg());
							sb_data.append("\",\"open_opt\":\"");
							sb_data.append(posBill.getOpen_opt() == null ? "" : posBill.getOpen_opt());
							sb_data.append("\",\"payment_state\":\"");
							sb_data.append(posBill.getPayment_state() == null ? "" : posBill.getPayment_state());
							sb_data.append("\",\"guest\":\"");
							sb_data.append(posBill.getGuest() == null ? "" : posBill.getGuest());
							sb_data.append("\",\"cashier_num\":\"");
							sb_data.append(posBill.getCashier_num() == null ? "" : posBill.getCashier_num());
							sb_data.append("\",\"open_pos_num\":\"");
							sb_data.append(posBill.getOpen_pos_num() == null ? "" : posBill.getOpen_pos_num());
							sb_data.append("\",\"memberlist\":");
							sb_data.append("[");
							if (posBill.getMemberlist() != null && posBill.getMemberlist().size() > 0) {
								for (int m = 0; m < posBill.getMemberlist().size(); m++) {
									sb_data.append(JSONObject.fromObject(posBill.getMemberlist().get(m)).toString());
									if (m != posBill.getMemberlist().size() - 1) {
										sb_data.append(",");
									}
								}
							}
							sb_data.append("]");
							sb_data.append(",\"payment_amount\":\"");
							sb_data.append(posBill.getPayment_amount() == null ? "" : posBill.getPayment_amount());
							sb_data.append("\",\"discountr_amount\":\"");
							sb_data.append(posBill.getDiscountr_amount() == null ? "" : posBill.getDiscountr_amount());
							sb_data.append("\",\"bill_property\":\"");
							sb_data.append(posBill.getBill_property() == null ? "" : posBill.getBill_property());
							sb_data.append("\"}");
						} else {
							// 默认处理
							// sb_data.append(JSON.toJSONString(JSONObject.fromObject(maps.get(i)).toString()));
							sb_data.append(JSONObject.fromObject(maps.get(i)).toString());
						}
						if (i != maps.size() - 1) {
							sb_data.append(",");
						}
					}
				} // for
			}
			sb_data.append("]");
			sb.append("{\"data\":");
			sb.append(sb_data.toString()); // data数据
			sb.append(",\"store_id\":");
			sb.append(data.getStore_id()); // 门店号
			sb.append(",\"code\":");
			sb.append(data.getCode()); // 编码
			sb.append(",\"type\":\"");
			sb.append(data.getType()); // 类型
			sb.append("\",\"msg\":\"");
			if (data.getMsg() == null) {
				sb.append(""); // 消息
			} else {
				sb.append(data.getMsg()); // 消息
			}
			sb.append("\",\"tenancy_id\":\"");
			sb.append(data.getTenancy_id()); // 商户号
			sb.append("\",\"t\":");
			if (data.getT() == null) {
				sb.append(0); // t
			} else {
				sb.append(data.getT()); // t
			}
			sb.append(",\"source\":\"");
			if (data.getSource() == null) {
				sb.append(""); // 来源
			} else {
				sb.append(data.getSource()); // 来源
			}
			sb.append("\",\"oper\":\"");
			sb.append(data.getOper()); // 操作
			sb.append("\",\"secret\":\"");
			sb.append(data.getSecret()); // 秘钥
			sb.append("\",\"pagination\":");
			Pagination pagination = data.getPagination();
			if (pagination == null) {
				sb.append("{\"totalcount\":0,\"pagesize\":0,\"asc\":false,\"orderby\":\"\",\"pageno\":0}");
			} else {
				String orderBy = "";
				if (pagination.getOrderby() != null)
					orderBy = pagination.getOrderby();
				sb.append("{\"totalcount\":");
				sb.append(pagination.getTotalcount());
				sb.append(",\"pagesize\":");
				sb.append(pagination.getPagesize());
				sb.append(",\"asc\":");
				sb.append(pagination.isAsc());
				sb.append(",\"orderby\":\"");
				sb.append(orderBy);
				sb.append("\",\"pageno\":");
				sb.append(pagination.getPageno());
				sb.append("}");
			}
			sb.append(",\"success\":");
			sb.append(data.isSuccess()); // 成功标识
			sb.append("}");
		} catch (Exception e) {
			logger.error("DataToJson方法出现异常，异常信息：" + e.toString());
		}
		return sb.toString();
	}

	/**
	 * JSONObject排序
	 * 
	 * @param obj
	 * @return
	 */
	@SuppressWarnings("all")
	public static JSONObject sortJsonObject(JSONObject obj)
	{
		Map map = new TreeMap();
		Iterator<String> it = obj.keys();
		while (it.hasNext())
		{
			String key = it.next();
			Object value = obj.get(key);
			if (value instanceof JSONObject)
			{

				map.put(key, sortJsonObject(JSONObject.fromObject(value)));
			}
			else if (value instanceof JSONArray)
			{

				map.put(key, sortJsonArray(JSONArray.fromObject(value)));
			}
			else
			{
				map.put(key, value);
			}
		}
		return JSONObject.fromObject(map);
	}

	/**
	 * JSONArray排序
	 * 
	 * @param array
	 * @return
	 */
	@SuppressWarnings("all")
	public static JSONArray sortJsonArray(JSONArray array)
	{
		List list = new ArrayList();
		int size = array.size();
		for (int i = 0; i < size; i++)
		{
			Object obj = array.get(i);
			if (obj instanceof JSONObject)
			{
				list.add(sortJsonObject(JSONObject.fromObject(obj)));
			}
			else if (obj instanceof JSONArray)
			{
				list.add(sortJsonArray(JSONArray.fromObject(obj)));
			}
			else
			{
				list.add(obj);
			}
		}

//		Collections.sort(list, new Comparator<Map.Entry<String, String>>()
//		{
//			@Override
//			public int compare(Map.Entry<String, String> o1, Map.Entry<String, String> o2)
//			{
//				return (o1.getKey()).toString().compareTo(o2.getKey());
//			}
//		});

		return JSONArray.fromObject(list);
	}
	
	/**
	 * @param paraJson
	 * @param superKey 上级的键
	 * @param urlEncode 是否需要键值对urlencode
	 * @param keyToLower 键是否小写
	 * @return
	 */
	public static String formatJsonObjectToUrl(JSONObject paraJson,String superKey, boolean urlEncode, boolean keyToLower)
	{
		StringBuilder params = new StringBuilder();
		try
		{
			@SuppressWarnings("unchecked")
			Set<String> set = paraJson.keySet();
			for (String key : set)
			{
				String keyName = null;
				if (CommonUtil.hv(superKey))
				{
					keyName = superKey + "[" + key + "]";
				}
				else
				{
					keyName = key;
				}
				
				Object obj = paraJson.get(key);
				if (obj instanceof JSONObject)
				{
					String childParams = formatJsonObjectToUrl(JSONObject.fromObject(obj), keyName, urlEncode, keyToLower);
					params.append(childParams);
				}
				else if (obj instanceof JSONArray)
				{
					String childParams = formatJsonArrayToUrl(JSONArray.fromObject(obj), keyName, urlEncode, keyToLower);
					params.append(childParams);
				}
				else if (obj instanceof Double)
				{
					if (keyToLower)
					{
						keyName = keyName.toLowerCase();
					}
					if (urlEncode)
					{
						keyName = URLEncoder.encode(keyName, "utf-8");
					}

					String val = obj.toString();
					Double dol = Double.valueOf(val);
					if (0d == dol % 1.0)
					{
						val = String.valueOf(dol.intValue());
					}
					if (urlEncode)
					{
						val = URLEncoder.encode(val, "utf-8");
					}
					params.append(keyName).append("=").append(val).append("&");
				}
				else if (obj instanceof Boolean)
				{
					if (keyToLower)
					{
						keyName = keyName.toLowerCase();
					}
					if (urlEncode)
					{
						keyName = URLEncoder.encode(keyName, "utf-8");
					}
					
					String val = "0";
					if ((boolean) obj)
					{
						val = "1";
					}
					params.append(keyName).append("=").append(val).append("&");
				}
				else
				{
					if (keyToLower)
					{
						keyName = keyName.toLowerCase();
					}
					if (urlEncode)
					{
						keyName = URLEncoder.encode(keyName, "utf-8");
					}

					String val = obj.toString();
					if (urlEncode)
					{
						val = URLEncoder.encode(val, "utf-8");
					}
					params.append(keyName).append("=").append(val).append("&");
				}
	
			}
		}
		catch (UnsupportedEncodingException e)
		{
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return params.toString().replaceAll("\\*", "%2A");
	
	}
	
	/**
	 * @param paraArray
	 * @param superKey 上级的键
	 * @param urlEncode 是否需要键值对urlencode
	 * @param keyToLower 键是否小写
	 * @return
	 */
	public static String formatJsonArrayToUrl(JSONArray paraArray,String superKey, boolean urlEncode, boolean keyToLower)
	{
		StringBuilder params = new StringBuilder();
		try
		{
			for (int i = 0; i < paraArray.size(); i++)
			{
				Object obj = paraArray.get(i);
				if (obj instanceof JSONObject)
				{
					String childParams = formatJsonObjectToUrl(JSONObject.fromObject(obj), superKey + "[" + i + "]", urlEncode, keyToLower);
					params.append(childParams);
				}
				else if (obj instanceof JSONArray)
				{
					String childParams = formatJsonArrayToUrl(JSONArray.fromObject(obj), superKey + "[" + i + "]", urlEncode, keyToLower);
					params.append(childParams);
				}
				else
				{
					String key = superKey + "[" + i + "]";
					String val = obj.toString();
					if (keyToLower)
					{
						key = key.toLowerCase();
					}
					if (urlEncode)
					{
						key = URLEncoder.encode(key, "utf-8");
						val = URLEncoder.encode(val, "utf-8");
					}
					params.append(key).append("=").append(val).append("&");
				}
			}
		}
		catch (UnsupportedEncodingException e)
		{
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return params.toString();
	}
	
	public static void renameKey(JSONObject json, String[] origName, String[] newName){
		if(json == null || origName == null || newName == null){
			return;
		}
		for(int i = 0; i < origName.length; i++){
			if(i >= newName.length){
				return;
			}
			String oldKey = origName[i];
			String newKey = newName[i];
			if(oldKey == null || newKey == null){
				continue;
			}
			if(json.containsKey(oldKey)){
				Object object = json.get(oldKey);
				json.remove(oldKey);
				json.put(newKey, object);
			}
		}
	}
	
	public static void attrCopy(JSONObject source, JSONObject target, String[] attrNames){
		if(source == null || target == null || attrNames == null){
			return;
		}
		String key = "";
		for(int i = 0; i < attrNames.length; i++){
			key = attrNames[i];
			if(key != null && source.containsKey(key)){
				target.put(key, source.get(key));
			}
		}
	}
	
	public static Data JsonToD5Data(JSONObject jsonObj) {
		Data data = new Data();
		if (jsonObj.has("t")) {
			String t = jsonObj.optString("t");
			if(Tools.hv(t)&& !"null".equals(t))
			{
				data.setT(jsonObj.getLong("t"));
			}
			else
			{
				data.setT(0L);;
			}
		}
		if (jsonObj.has("secret")) {
			data.setSecret(jsonObj.getString("secret"));
		}
		if (jsonObj.has("message")) {
			data.setMsg(jsonObj.getString("message"));
		}
		if (jsonObj.has("data")) {
			net.sf.json.JSONArray jsonArray = jsonObj.getJSONArray("data");
			List<Map<String, Object>> dataList = new ArrayList<Map<String, Object>>();
			if (jsonArray.size() > 0) {
				for (int i = 0; i < jsonArray.size(); i++) {
					Map map = new HashMap<String, Object>();
					JSONObject jsonObject = jsonArray.getJSONObject(i);
					Iterator it = jsonObject.keys();
					while (it.hasNext()) {
						String key = (String) it.next();
						map.put(key, jsonObject.get(key));
					}
					dataList.add(map);
				}
			}
			data.setData(dataList);
		}
		if (jsonObj.has("code")) {
			data.setCode(jsonObj.getInt("code"));
		}
		if (jsonObj.has("oper")) {
			data.setOper(Oper.valueOf(jsonObj.getString("oper")));
		}
		if (jsonObj.has("type")) {
			data.setType(Type.valueOf(jsonObj.getString("type")));
		}
		if (jsonObj.has("source")) {
			data.setSource(jsonObj.getString("source"));
		}
		if (jsonObj.has("success")) {
			data.setSuccess(jsonObj.getBoolean("success"));
		}
		if (jsonObj.has("pagination"))
		{
			Pagination pagination = new Pagination();
			String paginationStr = jsonObj.optString("pagination");
			if (Tools.hv(paginationStr) && !"null".equals(paginationStr))
			{
				JSONObject jsonObject = JSONObject.fromObject(paginationStr);
				if (jsonObject.has("pageno")) pagination.setPageno(jsonObject.getInt("pageno"));
				if (jsonObject.has("pagesize")) pagination.setPagesize(jsonObject.getInt("pagesize"));
				if (jsonObject.has("totalcount")) pagination.setTotalcount(jsonObject.getInt("totalcount"));
				if (jsonObject.has("orderby")) pagination.setOrderby(jsonObject.getString("orderby"));
				if (jsonObject.has("asc")) pagination.setAsc(jsonObject.getBoolean("asc"));
			}
			data.setPagination(pagination);
		}
		if (jsonObj.has("tenancy_id")) {
			data.setTenancy_id(jsonObj.getString("tenancy_id"));
		}
		if (jsonObj.has("store_id")) {
			data.setStore_id(jsonObj.getInt("store_id"));
			if (jsonObj.has("store_id") && Tools.hv(jsonObj.opt("store_id"))
					&& jsonObj.optString("store_id").length() > 0) {
				data.setStore_id(jsonObj.getInt("store_id"));
			}
		}
		return data;
	}
	
	public static <T> T jsonToBean(JSONObject paramJson ,Class<T> beanClass) throws Exception
	{
		T bean = beanClass.newInstance();
		Field[] fields = beanClass.getDeclaredFields();
		for (Field f : fields)
		{
			if (Modifier.isFinal(f.getModifiers()) || false == paramJson.containsKey(f.getName()))
			{
				continue;
			}

			String methodName = "set" + f.getName().substring(0, 1).toUpperCase() + f.getName().substring(1);
			Method setMethod = beanClass.getMethod(methodName, f.getType());

			String type = f.getType().toString();
			if (type.endsWith("String"))
			{
				setMethod.invoke(bean, ParamUtil.getStringValueByObject(paramJson, f.getName()));
			}
			else if (type.endsWith("int") || type.endsWith("Integer"))
			{
				setMethod.invoke(bean, ParamUtil.getIntegerValueByObject(paramJson, f.getName()));
			}
			else if (type.endsWith("double") || type.endsWith("Double"))
			{
				setMethod.invoke(bean, ParamUtil.getDoubleValueByObject(paramJson, f.getName()));
			}
			else if (type.endsWith("Date"))
			{
				setMethod.invoke(bean, ParamUtil.getDateValueByObject(paramJson, f.getName()));
			}
			else if (type.endsWith("Timestamp"))
			{
				setMethod.invoke(bean, ParamUtil.getTimestampValueByObject(paramJson, f.getName()));
			}
			else
			{
				setMethod.invoke(bean, paramJson.opt(f.getName()));
			}
		}
		return bean;
	}
	
	public static JSONObject jsonFormat(JSONObject paramJson ,Class<?> beanClass) throws Exception
	{
		JSONObject result = new JSONObject();
		for (Field f : beanClass.getDeclaredFields())
		{
			if (Modifier.isFinal(f.getModifiers()) || false == paramJson.containsKey(f.getName()))
			{
				continue;
			}

			result.put(f.getName(), paramJson.opt(f.getName()));
		}
		return result;
	}
	
	public static JSONObject fromObject(Object obj) throws Exception
	{
		JSONObject result = new JSONObject();
		for (Field f : obj.getClass().getDeclaredFields())
		{
			if (Modifier.isFinal(f.getModifiers()))
			{
				continue;
			}

			f.setAccessible(true);
			Object val = f.get(obj);

			String type = f.getType().toString();
			if (type.endsWith("String"))
			{
				result.put(f.getName(), Tools.hv(val) ? val : null);
			}
			else if (type.endsWith("int") || type.endsWith("Integer"))
			{
				result.put(f.getName(), Tools.hv(val) ? val : 0);
			}
			else if (type.endsWith("double") || type.endsWith("Double"))
			{
				result.put(f.getName(), Tools.hv(val) ? val : 0d);
			}
			else if (type.endsWith("Date"))
			{
				result.put(f.getName(), Tools.hv(val) ? DateUtil.formatDate(val) : null);
			}
			else if (type.endsWith("Timestamp"))
			{
				result.put(f.getName(), Tools.hv(val) ? DateUtil.format(val) : null);
			}
			else
			{
				result.put(f.getName(), Tools.hv(val) ? val : null);
			}
		}
		return result;
	}
	
}
