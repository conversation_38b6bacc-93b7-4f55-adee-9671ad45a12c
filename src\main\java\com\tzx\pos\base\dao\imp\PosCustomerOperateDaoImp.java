package com.tzx.pos.base.dao.imp;

import java.sql.Timestamp;
import java.util.List;

import net.sf.json.JSONObject;

import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import com.tzx.member.common.entity.PosCustomerOperateListEntity;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.dao.PosCustomerOperateDao;

@Repository(PosCustomerOperateDao.NAME)
public class PosCustomerOperateDaoImp extends BaseDaoImp implements PosCustomerOperateDao
{
	@SuppressWarnings("unchecked")
	@Override
	public List<PosCustomerOperateListEntity> getPosCustomerOperateListNotComplete(String tenancyId, int storeId, Timestamp currentTime, Double queryCountNum) throws Exception
	{
		// TODO Auto-generated method stub
		StringBuffer querySql = new StringBuffer("select * from pos_customer_operate_list where tenancy_id=? and store_id=? and cancel_state=? and (operate_state=? or operate_state=?) and (query_count is null or query_count <=?)");
		return (List<PosCustomerOperateListEntity>) this.query(querySql.toString(), new Object[]
		{ tenancyId, storeId, SysDictionary.CUSTOMER_OPERATE_CANCEL_STATE_N, SysDictionary.CUSTOMER_OPERATE_STATE_WAIT,SysDictionary.CUSTOMER_OPERATE_STATE_PROCESS, queryCountNum }, BeanPropertyRowMapper.newInstance(PosCustomerOperateListEntity.class));
	}

	@Override
	public void updatePosCustomerOperateListByQueryResult(String tenancyId, int storeId, PosCustomerOperateListEntity customerOperate) throws Exception
	{
		// TODO Auto-generated method stub
		StringBuffer updateSql = new StringBuffer("update pos_customer_operate_list set last_query_time=?,query_count=? where tenancy_id=? and store_id=? and id=?");
		this.update(updateSql.toString(), new Object[]
		{ customerOperate.getLast_query_time(), customerOperate.getQuery_count(), tenancyId, storeId, customerOperate.getId() });
	}
}
