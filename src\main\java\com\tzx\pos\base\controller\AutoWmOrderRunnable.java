package com.tzx.pos.base.controller;

import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;

import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.pos.base.util.SpringContextUtils;
import com.tzx.pos.bo.OrderDeliveryService;
import com.tzx.pos.bo.PosService;
import com.tzx.pos.bo.imp.OrderDeliveryServiceImpl;
import com.tzx.pos.bo.imp.PosServiceImp;

import net.sf.json.JSONObject;

public class AutoWmOrderRunnable implements Runnable{

	private static final Logger	logger	= Logger.getLogger(AutoWmOrderRunnable.class);

	@Override
	public void run() {
		try {
			Map<String, String> systemMap = Constant.getSystemMap();
            
			String tenancyId = systemMap.get("tenent_id");
			int storeId = Integer.parseInt(systemMap.get("store_id"));
			
			DBContextHolder.setTenancyid(tenancyId);
			
			OrderDeliveryService orderDeliveryService = (OrderDeliveryServiceImpl) SpringConext.getApplicationContext().getBean(OrderDeliveryService.NAME);
			List<JSONObject> orderJo = orderDeliveryService.findWmOrderException(tenancyId,storeId);
			
			
			FindExceptionOrderRunnble fe = new FindExceptionOrderRunnble(orderJo);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	

	


}
