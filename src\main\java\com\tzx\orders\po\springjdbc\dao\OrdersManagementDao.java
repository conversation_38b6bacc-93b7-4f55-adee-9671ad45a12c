package com.tzx.orders.po.springjdbc.dao;

import java.util.List;

import net.sf.json.JSONObject;

import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.orders.bo.dto.OrderState;
import com.tzx.orders.bo.dto.PosBill;
import com.tzx.orders.bo.dto.PosBillItem;
import com.tzx.orders.bo.dto.PosBillPayment;

public interface OrdersManagementDao extends GenericDao
{
	String	NAME	= "com.tzx.orders.po.springjdbc.dao.imp.OrdersManagementDaoImp";

	/**
	 * 获取当前班次
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @return
	 * @throws Exception
	 */
	JSONObject getCurrentDutyOrder(String tenancyId, int storeId) throws Exception;

	/**
	 * 获取当前报表日期
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @return
	 * @throws Exception
	 */
	JSONObject getCurrentReportDate(String tenancyId, int storeId) throws Exception;

	/**
	 * 验证报表日期是否有效
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param reportDate
	 * @return
	 * @throws Exception
	 */
	boolean checkReportDate(String tenancyId, int storeId, String reportDate) throws Exception;

	/**
	 * 获取支付方式
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> getPaymentWay(String tenancyId, int storeId) throws Exception;

	/**
	 * 获取订餐异常原因
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param para
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> getUnusualReason(String tenancyId, int storeId, JSONObject para) throws Exception;

	/**
	 * 获取机构信息
	 * 
	 * @param tenentid
	 * @param storeid
	 * @return
	 * @throws Exception
	 */
	JSONObject getOrganInfo(String tenentid, int storeid) throws Exception;

	/**
	 * 根据订单号获取订单信息
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param para
	 * @return
	 * @throws Exception
	 */
	PosBill getOrderByOrdercode(String tenancyId, int storeId, String orderCode) throws Exception;

	/**
	 * 根据订单号获取订单状态
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param billCode
	 * @return
	 * @throws Exception
	 */
	OrderState getOrderStateByOrderCode(String tenancyId, int storeId, String orderCode) throws Exception;

	/**
	 * 查询上传失败的订单状态
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param orderCode
	 * @return
	 * @throws Exception
	 */
	List<OrderState> getOrderStateForNotUpload(String tenancyId, int storeId) throws Exception;

	/**
	 * 根据订单号获取订单明细
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param orderCode
	 * @return
	 * @throws Exception
	 */
	List<PosBillItem> getOrderItemByOrdercode(String tenancyId, int storeId, String orderCode, String saleMode) throws Exception;

	/**
	 * 根据订单号获取订单支付明细
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param orderCode
	 * @return
	 * @throws Exception
	 */
	List<PosBillPayment> getOrderPaymentByOrdercode(String tenancyId, int storeId, String orderCode) throws Exception;

	/**
	 * 修改订单
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param para
	 * @return
	 * @throws Exception
	 */
	boolean updateOrderInfo(String tenancyId, int storeId, JSONObject para) throws Exception;

	/**
	 * 插入订单取消原因/投诉
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param param
	 * @param type
	 * @return
	 * @throws Exception
	 */
	public boolean addOrderReasonDetail(String tenancyId, int storeId, String orderCode, List<JSONObject> param) throws Exception;

	/**
	 * 根据订单号删除订单
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param billCode
	 * @return
	 * @throws Exception
	 */
	public boolean deleteOrderByOrderCode(String tenancyId, int storeId, String orderCode) throws Exception;

	/**
	 * 新增订单
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public boolean addOrderInfo(String tenancyId, int storeId, JSONObject param) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getPosBillItemByBillNum(String tenancyId, int storeId, String billNum) throws Exception;

	/** 添加订单支付方式
	 * @param tenancyId
	 * @param storeId
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public boolean addOrderRepayment(String tenancyId, int storeId, List<JSONObject> param) throws Exception;

    /** 获取订单积分信息
     * @param tenancyId
     * @param storeId
     * @param orderCode
     * @return
     * @throws Exception
     */
    JSONObject getOrderCredit(String tenancyId, int storeId, String orderCode) throws Exception;
    
    /**
     * 
      * @Description: 根据orderCode 查询posBill
      * @Title:getBillByOrderCode
      * @param:@param tenancyId
      * @param:@param storeId
      * @param:@param orderCode
      * @param:@return
      * @return: JSONObject
      * @author: shenzhanyu
      * @version: 1.0
     * @throws Exception 
      * @Date: 2018年4月13日
     */
    JSONObject getBillByOrderCode(String tenancyId, int storeId, String orderCode) throws Exception;
    
    /**
      * @Description: 根据billNum 删除posBill
      * @Title:deletePosBillByBillNum
      * @param:@param tenancyId
      * @param:@param storeId
      * @param:@param bill
      * @param:@return
      * @param:@throws Exception
      * @return: boolean
      * @author: shenzhanyu
      * @version: 1.0
      * @Date: 2018年4月13日
     */
    boolean deletePosBillByBillNum(String tenancyId, int storeId,JSONObject bill) throws Exception;
    
    List<JSONObject>  getIdByOrderCode(String tenancyId, int storeId, String tableName,String orderCode) throws Exception;
    
    /**
      * 根据订单号查询申请退款订单
      * @Title:getRefundOrderStateByOrderCode
      * @param:@param tenentid
      * @param:@param storeid
      * @param:@param optString
      * @param:@return
      * @param:@throws Exception
      * @return: OrderState
      * @author: shenzhanyu
      * @version: 1.0
      * @Date: 2018年8月30日
     */
    public OrderState getRefundOrderStateByOrderCode(String tenentid, int storeid, String optString) throws Exception;
    
    
    /**
     * 
      * @Description: 根据orderCode 查询posBill
      * @Title:getBillByOrderCode
      * @param:@param tenancyId
      * @param:@param storeId
      * @param:@param orderCode
      * @param:@return
      * @return: JSONObject
      * @author: shenzhanyu
      * @version: 1.0
     * @throws Exception 
      * @Date: 2018年4月13日
     */
    JSONObject getBillByOrderCode(String tenancyId, int storeId, String orderCode,String bill_state) throws Exception;
    
    
	/**
	  * @Description: 根据支付方式查询是否存在
	  * @Title:existPaymentWay
	  * @param:@param tenancyId
	  * @param:@param storeId
	  * @param:@param payment_id
	  * @param:@return
	  * @param:@throws Exception
	  * @return: boolean
	  * @author: shenzhanyu
	  * @version: 1.0
	  * @Date: 2018年8月24日
	 */
	public boolean existPaymentWay(String tenancyId, int storeId,String order_code) throws Exception;
	
	
	/**
     * @Description: 根据订单号查询订单
     * @Title:getBillByBillNum
     * @param:@param tenancyId
     * @param:@param storeId
     * @param:@param bill_num
     * @param:@return
     * @param:@throws Exception
     * @return: JSONObject
     * @author: shenzhanyu
     * @version: 1.0
     * @Date: 2018年6月4日
    */
   public JSONObject getBillByBillNum(String tenancyId, int storeId, String bill_num)throws Exception;

    /**
     * 配送异常订单改为自配送
     * @param tenancyId
     * @param storeId
     * @param orderCode
     */
	public boolean udateOrderDelivery(String tenancyId, int storeId, String orderCode);

   
   
	/**
	  * @Description: 查詢本地菜品渠道类型
	  * @Title:getLocalItemChanel
	  * @param:@param tenancyId
	  * @param:@param storeId
	  * @param:@param para
	  * @param:@return
	  * @param:@throws Exception
	  * @return: List<JSONObject>
	  * @author: shenzhanyu
	  * @version: 1.0
	  * @Date: 2018年7月24日
	 */
	public List<JSONObject> getLocalItemChanel(String tenancyId, int storeId, JSONObject para) throws Exception;
	
	/**
	  * @Description: 根据渠道查询本地菜品类别
	  * @Title:getLocalItemCate
	  * @param:@param tenancyId
	  * @param:@param storeId
	  * @param:@param para
	  * @param:@return
	  * @param:@throws Exception
	  * @return: List<JSONObject>
	  * @author: shenzhanyu
	  * @version: 1.0
	  * @Date: 2018年7月24日
	 */
	public List<JSONObject> getLocalItemCate(String tenancyId, int storeId, JSONObject para) throws Exception;

	/**
	  * 修復菜品 菜品绑定
	  * @Title:addBindingItemInfo
	  * @param:@param tenancyId
	  * @param:@param storeId
	  * @param:@param param
	  * @param:@return
	  * @param:@throws Exception
	  * @return: boolean
	  * @author: shenzhanyu
	  * @version: 1.0
	  * @Date: 2018年7月22日
	 */
	public Object addBindingItemInfo(String tenancyId, int storeId, JSONObject param) throws Exception;
	
	/**
	  * @Description: 解除菜品绑定
	  * @Title:deleteBindingItemInfo
	  * @param:@param tenancyId
	  * @param:@param storeId
	  * @param:@param param
	  * @param:@return
	  * @param:@throws Exception
	  * @return: boolean
	  * @author: shenzhanyu
	  * @version: 1.0
	  * @Date: 2018年7月22日
	 */
	public boolean deleteBindingItemInfo(String tenancyId, int storeId,JSONObject param) throws Exception;
	
	/**
	  * @Description: 验证异常菜品是否绑定
	  * @Title:validateBindingItem
	  * @param:@param tenancyId
	  * @param:@param storeId
	  * @param:@param param
	  * @param:@return
	  * @param:@throws Exception
	  * @return: JSONObject
	  * @author: shenzhanyu
	  * @version: 1.0
	  * @Date: 2018年7月22日
	 */
	public JSONObject validateBindingItem(String tenancyId, int storeId, JSONObject param) throws Exception;

	/**
	 * 更新外卖订单提醒标识
	 * @param tenancyId
	 * @param storeId
	 * @param orderCode
	 * @return
	 * @throws Exception
	 */
	public boolean updateOrderPrintRemind(String tenancyId, int storeId, String orderCode) throws  Exception;
	
	/** 根据人员id查询人员姓名
	 * @param tenancyId
	 * @param storeId
	 * @param optNum
	 * @return
	 * @throws Exception
	 */
	public String getEmpNameById(String tenancyId, Integer storeId,String optNum) throws Exception;


	/**
	 * 修改外卖账单菜品类别
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @return
	 * @throws Exception
	 */
	public boolean updateBillItem(String tenancyId, String billNum) throws  Exception;



	/**
	 * 正在取单的订单标识置空
	 * @param tenancyId
	 * @param storeId
	 * @param orderCode
	 * @return
	 * @throws Exception
	 */
	public boolean settingTakeOrderFlag(String tenancyId, int storeId, String orderCode) throws  Exception;


	/**
	 * 获取订单是否正在取单
	 * @param tenancyId
	 * @param storeId
	 * @param orderCode
	 * @return
	 * @throws Exception
	 */
	public boolean getTakeOrderFlag(String tenancyId, int storeId, String orderCode) throws  Exception;

	/**
	 * 根据渠道获取支付方式信息
	 * @param tenancyId
	 * @param storeId
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> findPaymentWayByChanel(String tenancyId, int storeId,String chanel) throws Exception;

	/**
	 * 根据账单号查询账单扩展信息
	 * @param tenancyId
	 * @param storeId
	 * @param bill_num
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> findPosBillExtendsByBillNum(String tenancyId, int storeId,String bill_num) throws Exception;

	/**
	 * 根据订单号获取账单信息
	 *
	 * @param tenancyId
	 * @param storeId
	 * @return
	 * @throws Exception
	 */
	PosBill getBillByOrderNum(String tenancyId, int storeId, String orderNum) throws Exception;
}
