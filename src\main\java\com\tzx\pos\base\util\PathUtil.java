package com.tzx.pos.base.util;

import java.io.File;
import java.net.URISyntaxException;

import javax.servlet.http.HttpServletRequest;

import org.apache.log4j.Logger;

import com.tzx.base.common.util.PosPropertyUtil;
import com.tzx.pos.base.Constant;

public class PathUtil
{
	private static final Logger	logger	= Logger.getLogger(PathUtil.class);
	/**
     * 获取web项目根路径
     * @return
     */
    public static String getWebRoot(HttpServletRequest request) {
        // 得到协议如：http
        String scheme = request.getScheme();
        //得到服务器名称如：127.0.0.1
        String serverName = request.getServerName(); 
        //得到端口号如8080
        int serverPort = request.getServerPort();
        //得到当前上下文路径，也就是安装后的文件夹位置。
        String contextPath = request.getContextPath();
        //连起来拼成完整的url
        String webRoot = scheme+"://"+serverName+":"+serverPort+contextPath+"/";
        return webRoot;
    }
    
    /**   
     * 删除单个文件   
     * @param   fileName    被删除文件的文件名   
     * @return 单个文件删除成功返回true,否则返回false   
     */    
    public static boolean deleteFile(String fileName){     
        File file = new File(fileName);     
        if(file.isFile() && file.exists()){     
            file.delete();     
            return true;     
        }else{     
            return false;     
        }     
    }
    
    public static String getClassesPath2(){
        String path = "";
        try
        {
            File file = new File(PathUtil.class.getResource("/").toURI());
            path = file.getAbsolutePath();
        }catch (URISyntaxException e){
            e.printStackTrace();
        }
        return path;
    }
    /**此方法只能对web项目在服务器开启的情况下，在页面上调用时才有用。
     *因为只有这样getClassesPath2才能得到工程发布目录下的类编译路径（在WEB-INF\classes下）
     *如果是普通的java工程，则只会返回类文件的编译路径*/
    public static String getWebRootPath(){
        String path = "";
        path = getClassesPath2().split("WEB-INF")[0];
        if(path.endsWith("/") || path.endsWith("\\")){
            //使返回的路径不包含最后的"/"或"\"
            path = path.substring(0, path.length()-1);
        }
        return path;
    }
    
    /*
	 * 删除saas日志方法
	 * days:几天之前
	 * */
	public static void delSaasLog(){
		try {
			int days = Integer.parseInt(PosPropertyUtil.getMsg(Constant.DEL_BOH_LOG_DAYS));
			String IS_DEL_BOH_LOG = PosPropertyUtil.getMsg(Constant.IS_DEL_BOH_LOG);
			if(IS_DEL_BOH_LOG.equals("0")){//删除日志
				logger.info("com.tzx.pos.base.util.PathUtil====删除"+days+"天前saas日志");
				File file = new File(System.getenv().get("CATALINA_HOME")+"/logs");
			    File[] filename = file.listFiles();
			    if(null!=filename && filename.length>0)
			    {
				    for (int i = 0; i < filename.length; i++){
				    	if(filename[i].isFile()){
				    		long currentTime = System.currentTimeMillis();
				    		long timefile = filename[i].lastModified();
				    		long time = currentTime - timefile;
				    		if((time/(24*60*60*1000)) >= days){
				    			boolean b = filename[i].delete();
				    			if(b){
				    				System.out.println("delete file:" + filename[i].getName());
				    			}
				    		}
				
				    	}
				    }
			    }
			}else{
				logger.info("com.tzx.pos.base.util.PathUtil====不删除saas日志");
			}
		} catch (NumberFormatException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			logger.info("com.tzx.pos.base.util.PathUtil====删除saas日志失败:"+e.getMessage());
		}
	}
}
