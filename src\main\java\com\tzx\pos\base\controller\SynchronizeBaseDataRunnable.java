package com.tzx.pos.base.controller;

import com.tzx.pos.base.service.SynchronizeBaseDataService;
import com.tzx.pos.base.service.impl.SynchronizeBaseDataServiceImp;
import org.apache.commons.io.FileUtils;
import org.apache.log4j.Logger;

import java.io.File;

/**
 * <AUTHOR> on 2017-11-23
 */
public class SynchronizeBaseDataRunnable implements Runnable {

    Logger tip= Logger.getLogger("tip");
    @Override
    public void run() {
        String filePath = System.getProperty("catalina.home") + "/sync.lock";
        //判断是否需要下发数据
        File file = FileUtils.getFile(filePath);
        if(file.exists()){
            SynchronizeBaseDataService synchronizeBaseDataService=new SynchronizeBaseDataServiceImp();
            boolean b = synchronizeBaseDataService.syncData(SynchronizeBaseDataService.Type.organ);
            if(b){
                file.delete();
                tip.info("请求下发资料成功");
            }
        }
    }
}
