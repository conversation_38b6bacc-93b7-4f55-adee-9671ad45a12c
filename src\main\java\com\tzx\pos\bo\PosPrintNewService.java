package com.tzx.pos.bo;

import com.tzx.framework.common.exception.SystemException;

import net.sf.json.JSONObject;

public interface PosPrintNewService extends PosBaseService

{
	public String NAME="com.tzx.pos.bo.imp.PosPrintNewServiceImp";
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param para
	 * @param printMode
	 * @throws Exception
	 */
	public void posPrintByFunction(String tenancyId, int storeId, String functionId, JSONObject para) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param printMode
	 * @param para
	 * @throws Exception
	 */
	public void posPrintByMode(String tenancyId, int storeId, String printMode, JSONObject para) throws Exception;
	
	/**
	 * 是否开启新的打印模式
	 * @return
	 * @throws SystemException
	 */
	public  boolean isNONewPrint(String tenantId,Integer storeId)throws SystemException;

    /**
     * 根据移动设备编号获取pos_num
     * @param tenancyId
     * @param storeId
     * @param devicesNum
     * @return
     * @throws Exception
     */
	String getPosNumByMoveDevicesNum(String tenancyId, int storeId, String devicesNum) throws Exception;
}
