package com.tzx.framework.common.taglib;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import javax.servlet.jsp.JspException;
import javax.servlet.jsp.JspWriter;
import javax.servlet.jsp.tagext.BodyContent;
import javax.servlet.jsp.tagext.BodyTagSupport;


public class AuthCheckTaglib extends BodyTagSupport
{

//  执行顺序  
//  
//  doStartTag()->setBodyContent()->doInitBody()->doAfterTag()->doEndTag()  
//  
//  如果doStartTag()返回的是EVAL_BODY_INCLUDE执行doAfterTag()方法,  
//  
//  如果它返回SKIP_BODY就执行doEndTag()方法。  
//  
//  setBodyContent()方法用于设置标签体内容，如果在计算BodyContent时需要进行一些初始化工作，  
//  
//  则在doInitBody()方法中完成。标签体内容执行完后，会调用doAfterBody()方法  
//  
//  在doAfterTag()方法中返回EVAL_BODY_AGAIN来重复执行doAfterTag()方法  
//  
//  返回SKIP_BODY值则执行doEndTag()方法。  
//  
//  在doEndTag()方法中返回EVAL_PAGE值,则执行此标签的后的其它代码,  
//  
//  返回SKIP_PAGE则不执行此页面的其它代码。  
      
    private int mid;  
  
    private HttpServletRequest request;
    
    private HttpSession session;  
    
    private JspWriter out;  
  
    public void init() {  
        request = (HttpServletRequest) pageContext.getRequest();  
        session = request.getSession();
        out = pageContext.getOut();  
    }  
  
    @Override  
    public int doStartTag() throws JspException {  
        init();
        Map<Integer,Integer> map=(Map<Integer, Integer>)this.session.getAttribute("authMap");
        if(map.containsKey(0)){
        	return this.EVAL_BODY_INCLUDE; 
        }
        if(map.containsKey(mid)){
        	return this.EVAL_BODY_INCLUDE; 
        }
        else{
        	return this.SKIP_BODY;  
        }
    }
  
    // 设置当前标签体  
    @Override  
    public void setBodyContent(BodyContent bodyContent) {  
        this.bodyContent = bodyContent;  
     
    }  
  
    // 需要初始化bodyContent  
    @Override  
    public void doInitBody() throws JspException {  
      
    }  
  
    @Override  
    public int doAfterBody() throws JspException {        
           
    	return this.SKIP_BODY;  
    }  
  
    @Override  
    public int doEndTag() throws JspException {  
   
        return this.EVAL_PAGE;  
    }  
  
    // 必须实现setXX()方法  
    public void setMid(int mid) {  
        this.mid = mid;  
    }  
	
	
	
	
	
}
