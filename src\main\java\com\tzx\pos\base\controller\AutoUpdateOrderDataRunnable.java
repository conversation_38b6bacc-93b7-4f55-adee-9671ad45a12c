package com.tzx.pos.base.controller;

import java.util.Map;

import org.apache.log4j.Logger;

import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;

public class AutoUpdateOrderDataRunnable  implements Runnable
{

	Logger logger=Logger.getLogger(this.getClass());
	Map<String, String> systemMap = Constant.getSystemMap();
	String tenantId = systemMap.get("tenent_id");
//	int storeId = Integer.parseInt(systemMap.get("store_id"));
	

	@Override
	public void run()
	{
		GenericDao dao = (GenericDao) SpringConext.getBean("genericDaoImpl");
		DBContextHolder.setTenancyid(tenantId);
		
		StringBuilder sql=new StringBuilder();
//		sql.append("SELECT\n" +
//				"		A .order_num\n" +
//				"	FROM\n" +
//				"		pos_bill A,\n" +
//				"		cc_order_list b\n" +
//				"	WHERE\n" +
//				"		A .order_num = b.order_code\n" +
//				"	AND A .bill_state = 'ZDQX02'\n" +
//				"	AND A .bill_property = 'CLOSED'");
//		try
//		{
//			sql.delete(0, sql.length());
//			sql.append("UPDATE cc_order_item\n" +
//					"SET tag = 'ZDQX02',\n" +
//					" upload_tag = '2'\n" +
//					"WHERE\n" +
//					"	upload_tag <> '2'\n" +
//					"AND order_code IN (");
//			List<JSONObject> result=dao.query4Json(tenantId, sql.toString());
//			if(null!=result&&result.size()>0){
//				for(int i=0;i<result.size();i++){
//					if(i==0){
//						sql.append("'"+result.get(i).optString("order_num")+"'");
//					}else{
//						sql.append(",'"+result.get(i).optString("order_num")+"'");
//					}
//				}
//				sql.append(")");
//				dao.execute(tenantId, sql.toString());
//			}
//			
//		}
//		catch (Exception e)
//		{
//			e.printStackTrace();
//		}

		//--单品退菜订单更新
//		updateSql.append("UPDATE cc_order_list SET total_money=t.total_money,actual_pay=t.actual_pay ,upload_tag ='2' from (SELECT b.total_money + SUM (A .bill_amount) total_money, b.actual_pay + SUM (A .payment_amount) actual_pay, a.order_num FROM pos_bill A, cc_order_list b WHERE A .order_num = b.order_code AND A .bill_state = 'CJ01' AND A .bill_property = 'CLOSED' GROUP BY b.total_money, b.actual_pay, a.order_num ) t where order_code=t.order_num and upload_tag <> '2';");
		//--单品退菜订单明细更新 TODO
//		updateSql.append("");
		
		//--整单退菜订单更新
		sql.append("UPDATE cc_order_list\n" +
				"SET upload_tag = '2',\n" +
				" order_state = '08',\n" +
				" payment_state = '04'\n" +
				"WHERE\n" +
				"	upload_tag <> '2'\n" +
				"AND order_code IN (\n" +
				"	SELECT\n" +
				"		A .order_num\n" +
				"	FROM\n" +
				"		pos_bill A,\n" +
				"		cc_order_list b\n" +
				"	WHERE\n" +
				"	A .order_num = b.order_code\n" +
				"	AND A .bill_state = 'ZDQX02'\n" +
				"	AND A .bill_property = 'CLOSED'\n" +
				");");
		//--整单退菜订单明细更新
		sql.append("UPDATE cc_order_item\n" +
				"SET tag = 'TC01',\n" +
				" upload_tag = '2'\n" +
				"WHERE\n" +
				"	upload_tag <> '2'\n" +
				"AND order_code IN (\n" +
				"	SELECT\n" +
				"		A .order_num\n" +
				"	FROM\n" +
				"		pos_bill A,\n" +
				"		cc_order_list b\n" +
				"	WHERE\n" +
				"		A .order_num = b.order_code\n" +
				"	AND A .bill_state = 'ZDQX02'\n" +
				"	AND A .bill_property = 'CLOSED'\n" +
				");");
		
		try
		{
			dao.execute(tenantId, sql.toString());
			logger.info("=================已成功更新本地订单信息!=================");
		}
		catch (Exception e)
		{
			logger.error("=================更新本地订单信息失败!=================\n"+e.getStackTrace());
			e.printStackTrace();
		}
	}

}
