package com.tzx.orders.base.controller;

import static com.tzx.orders.base.constant.Constant.THIRD_CHANNEL;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.lang.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.BoundListOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.jdbc.core.BeanPropertyRowMapper;

import com.tzx.base.bo.ProcessMessageService;
import com.tzx.base.common.util.PosPropertyUtil;
import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.util.HttpUtil;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.Tools;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.orders.base.util.OrderUtil;
import com.tzx.orders.bo.dto.LostOrder;

/**
 * <AUTHOR> on 2017-12-23
 */
public class OrderAssistRunnable implements Runnable {

    private Logger logger = LoggerFactory.getLogger(OrderAssistRunnable.class);
    private Logger tip = LoggerFactory.getLogger("tip");

    private ProcessMessageService processMessageService;

    private StringRedisTemplate ordercenterRedisTemplate;

    private GenericDao dao;

    private String tenentId;
    private int storeId;
    private String qcometoboh;

    private String reqURL;

    private int redisQueryInterval;
    private int lostQueryInterval;


    @Override
    public void run() {

        setAssistConfig();

        processMessageService = (ProcessMessageService) SpringConext.getApplicationContext().getBean(ProcessMessageService.NAME);
        ordercenterRedisTemplate = (StringRedisTemplate) SpringConext.getApplicationContext().getBean("ordercenterRedisTemplate");
        dao = (GenericDao) SpringConext.getBean("genericDaoImpl");

        if (redisQueryInterval > 0) {
            Executors.newSingleThreadScheduledExecutor().scheduleWithFixedDelay(new OrderRedisPullRunnable(), redisQueryInterval,redisQueryInterval, TimeUnit.SECONDS);
        }
        if (lostQueryInterval > 0) {
            Executors.newSingleThreadScheduledExecutor().scheduleWithFixedDelay(new OrderLostPullRunnable(), lostQueryInterval, lostQueryInterval,TimeUnit.SECONDS);
        }
    }

    private void setAssistConfig() {

        Map<String, String> systemMap = Constant.getSystemMap();
        try {
            tenentId = systemMap.get("tenent_id");
            storeId = Integer.parseInt(systemMap.get("store_id"));
            qcometoboh = systemMap.get("qcometoboh");
            //参数格式: redisPull:30,lostPull:60 (单位为秒，值为0时禁用)
            String para = OrderUtil.getSysPara(tenentId, storeId, "ORDER_ASSIST_CONFIG");
			if (Tools.hv(para))
			{
				String[] paras = para.split(",");
				redisQueryInterval = Integer.parseInt(paras[0].split(":")[1]);
				lostQueryInterval = Integer.parseInt(paras[1].split(":")[1]);

				logger.info("OrderAssist配置为：" + para);
				if (0 == redisQueryInterval || 0 == lostQueryInterval)
				{
					tip.warn("门店接单辅助功能已禁用,可能会有订单丢失!");
				}
			}else
			{
				logger.error("OrderAssist必要参数未设置或设置错误，OrderAssist启用失败!");
	            tip.error("门店接单辅助参数设置错误，请检查!");
			}

        } catch (Exception e) {
            logger.error("OrderAssist必要参数未设置或设置错误，OrderAssist启用失败!", e);
            tip.error("门店接单辅助参数设置错误，请检查!");
        }
    }


    private class OrderRedisPullRunnable implements Runnable {

        BoundListOperations ops= ordercenterRedisTemplate.boundListOps(qcometoboh);

        @Override
        public void run() {
            try {
                if(0==ops.size()){
                    logger.info("redis["+qcometoboh+"]无订单数据");
                   return;
                }
                String orderStr = ops.rightPop().toString();
                if (orderStr != null && !"".equals(orderStr)) {
                    JSONObject getJson = JSONObject.fromObject(orderStr);
                    if (getJson != null && !getJson.isEmpty()) {
//                        String data = getJson.toString();
                        String type = getJson.optString("type");
                        if (Type.ORDER.name().equals(type.toUpperCase())) {
//                            if (Oper.add.name().equals(getJson.getString("oper"))) {
//                                logger.info("接收订单:" + data);
//                                try {
//                                    DBContextHolder.setTenancyid(tenentId);
//                                    ProcessMessageService processMessageService = (ProcessMessageService) SpringConext
//                                            .getBean(ProcessMessageService.NAME);
//                                    processMessageService.addOrders(tenentId, storeId, getJson);
//                                } catch (Exception e) {
//                                    logger.error("==========接收订单失败!\n", e);
//                                }
//                            } else if (Oper.updatedish.name().equals(getJson.getString("oper"))) {
//                                logger.info("接收订单[加菜]:" + data);
//                                try {
//                                    DBContextHolder.setTenancyid(tenentId);
//                                    ProcessMessageService processMessageService = (ProcessMessageService) SpringConext
//                                            .getBean(ProcessMessageService.NAME);
//                                    processMessageService.updateDish(tenentId, storeId, getJson);
//                                } catch (Exception e) {
//                                    logger.error("==========接收订单失败!\n", e);
//                                }
//                            } else if (Oper.cancle.name().equals(getJson.getString("oper"))) {
//                                try {
//                                    logger.info("订单取消得到的消息是：" + data);
//
//                                    DBContextHolder.setTenancyid(tenentId);
//                                    ProcessMessageService processMessageService = (ProcessMessageService) SpringConext
//                                            .getBean(ProcessMessageService.NAME);
//                                    processMessageService.cancleOrders(tenentId, storeId, getJson);
//                                } catch (Exception e) {
//                                    logger.info("得到的消息是：" + data);
//                                    logger.error("取消订单失败", e);
//                                }
//                            } else if (Oper.opinion.name().equals(getJson.getString("oper"))) {
//                                try {
//                                    DBContextHolder.setTenancyid(tenentId);
//                                    ProcessMessageService processMessageService = (ProcessMessageService) SpringConext
//                                            .getBean(ProcessMessageService.NAME);
//                                    processMessageService.opinionOrders(tenentId, storeId, getJson);
//                                } catch (Exception e) {
//                                    logger.info("得到的消息是：" + data);
//                                    logger.error("投诉订单失败", e);
//                                }
//                            } else if (Oper.complete.name().equals(getJson.getString("oper"))) {
//                                try {
//                                    DBContextHolder.setTenancyid(tenentId);
//                                    ProcessMessageService processMessageService = (ProcessMessageService) SpringConext
//                                            .getBean(ProcessMessageService.NAME);
//                                    processMessageService.completeOrders(tenentId, storeId, getJson);
//                                } catch (Exception e) {
//                                    logger.info("得到的消息是：" + data);
//                                    logger.error("完成订单失败", e);
//                                }
//                            }

                        	DBContextHolder.setTenancyid(tenentId);
                        	ProcessMessageService processMessageService = (ProcessMessageService) SpringConext
                                  .getBean(ProcessMessageService.NAME);
                        	processMessageService.ordersManagement(tenentId, storeId, getJson);
                        }
                    }
                }
            } catch (Throwable t) {
                logger.error("redis拉单线程异常", t);
            }

        }
    }

    private class OrderLostPullRunnable implements Runnable {
        @Override
        public void run() {
            logger.debug("执行拉取订单############");
            JSONObject jsonObject = null;
            try {
                jsonObject = queryLostOrders();
                if (null == jsonObject) {
                    logger.debug("本地不存在丢单数据");
                    return;
                }
                if (jsonObject.containsKey("code") && jsonObject.getInt("code") == 1) {
                    JSONArray orderArray = null;
                    if(jsonObject.containsKey("data"))
                    {
                        orderArray = jsonObject.getJSONArray("data");
                        if(orderArray.size()>0){
                            logger.info("订单断号拉取参数："+orderArray);
                            for (int i = 0; i < orderArray.size(); i++) {
                                try {
                                    JSONObject orderData = JSONObject.fromObject(jsonObject.getJSONArray("data").get(i));
//                                    processMessageService.addOrders(tenentId, storeId, orderData);
                                    DBContextHolder.setTenancyid(tenentId);
                                    ProcessMessageService processMessageService = (ProcessMessageService) SpringConext .getBean(ProcessMessageService.NAME);
                                    processMessageService.ordersManagement(tenentId, storeId, orderData);

                                } catch (Exception e) {
                                    logger.error("拉取到的订单处理错误！", e);
                                }
                            }
                        }else{
                            return;
                        }
                    }
                }
                if (jsonObject.containsKey("code") && jsonObject.getInt("code") == 0) {
                    String failReason = jsonObject.getString("reason");
                    logger.error("拉取丢失订单总部返回失败!原因: " + failReason);
                }
            }

            catch(Throwable e){
                logger.error("拉取订单失败！", e);
            }
        }


        private JSONObject queryLostOrders() throws Exception {

            JSONObject json = new JSONObject();
            reqURL = PosPropertyUtil.getMsg("saas.url") + "/orderRest/getMissOrderInfo";
            String date = System.getProperty("reportDate") == null ? DateFormatUtils.format(new Date(), "yyyy-MM-dd") : System.getProperty("reportDate");
            String startDate = date + " 00:00:00";
            String endDate = date + " 23:59:59";
            String sql = "SELECT T .tenancy_id, T .store_id, T .single_date,T .total, T .loss, T .channel,T.serial_number_agg FROM (SELECT tenancy_id,store_id,to_date(single_time :: VARCHAR, 'YYYY-MM-DD') AS single_date, chanel channel, max (CAST (chanel_serial_number AS INTEGER ) ) AS total, max (CAST (chanel_serial_number AS INTEGER ) ) - COUNT (1) AS loss,string_agg(chanel_serial_number,',') as serial_number_agg FROM cc_order_list  WHERE tenancy_id=? AND store_id=? and single_time BETWEEN  ? AND ? GROUP BY tenancy_id,store_id, single_date, chanel ORDER BY single_date, chanel ) T ";
            List<LostOrder> lostOrders = dao.getJdbcTemplate(tenentId).query(sql,
                    new Object[]{tenentId, storeId, Timestamp.valueOf(startDate), Timestamp.valueOf(endDate)},
                    BeanPropertyRowMapper.newInstance(LostOrder.class));
            json.put("tenancy_id", tenentId);
            json.put("shop_id", storeId);
            json.put("date_start", startDate);
            json.put("date_end", endDate);
            JSONArray jsonArray = new JSONArray();
            if (lostOrders != null && lostOrders.size() > 0) {
                List<String> orderChannel = new ArrayList<String>();
                orderChannel.addAll(THIRD_CHANNEL);
                for (int j = 0; j < lostOrders.size(); j++) {
                    LostOrder order = lostOrders.get(j);
                    JSONObject detailJson = new JSONObject();
                    String[] serial = null;
                    orderChannel.remove(order.getChannel());
                    if(order.getTotal()!=null && !"".equals(order.getTotal())){
                        if (order.getSerial_number_agg() != null) {
                            serial = order.getSerial_number_agg().split(",");
                        }
                        List<String> serials = Arrays.asList(serial);
                        List<Integer> sortList = new ArrayList<Integer>();

                        int i = 1;
                        if(order.getChannel().equals("TH01")){//沙绿自营外卖序列号从1000开始
                            i=1000;
                        }
                        while (i <= Integer.parseInt(order.getTotal())) {
                            if (!serials.contains(i + "")) {
                                sortList.add(i);
                            }
                            i++;
                        }

                        detailJson.put("channel", order.getChannel());
                        //若有断号,使用断号拉取；若无断号,最大号加1进行拉取。
                        if(sortList.isEmpty() || sortList.size()==0){//不存在断号
                            sortList.add(Integer.parseInt(order.getTotal())+1); //最大号+1
                        }
                        detailJson.put("sort_num", JSONArray.fromObject(sortList));
                        jsonArray.add(j, detailJson);
                    }
                }

                for(int j= 0;j<orderChannel.size();j++){//不存在的丢失数据时 拉取第一号订单
                    JSONObject detailJson = new JSONObject();
                    detailJson.put("channel", orderChannel.get(j));
                    List<Integer> sortList = new ArrayList<Integer>();
                    int  serial_number  =1;
                    if(orderChannel.get(j).equals("TH01")){
                        serial_number=1000;
                    }
                    sortList.add(serial_number);
                    detailJson.put("sort_num", JSONArray.fromObject(sortList));
                    jsonArray.add(j, detailJson);
                }
                json.put("detail", jsonArray);
                logger.info("断号拉取请求总部参数：："+json);
                return HttpUtil.post(reqURL, json, 3);
            }else{
                for(int j= 0;j<THIRD_CHANNEL.size();j++){
                    JSONObject detailJson = new JSONObject();
                    detailJson.put("channel", THIRD_CHANNEL.get(j));
                    List<Integer> sortList = new ArrayList<Integer>();
                    int  serial_number  =1;
                    if(THIRD_CHANNEL.get(j).equals("TH01")){
                        serial_number=1000;
                    }
                    sortList.add(serial_number);
                    detailJson.put("sort_num", JSONArray.fromObject(sortList));
                    jsonArray.add(j, detailJson);
                }
                json.put("detail", jsonArray);
                logger.info("断号拉取请求总部参数：："+json);
                return HttpUtil.post(reqURL, json, 3);
            }
        }
    }
}


