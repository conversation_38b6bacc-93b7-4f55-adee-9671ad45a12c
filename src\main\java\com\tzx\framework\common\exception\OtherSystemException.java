package com.tzx.framework.common.exception;

import java.util.List;

/**
 * 其它系统异常信息
 */
public class OtherSystemException extends RuntimeException{
    private int code;
    private String msg;
    private List<?> data;

    public OtherSystemException(int code, String msg, List<?> data){
        super(msg);
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public List<?> getData() {
        return data;
    }

    public void setData(List<?> data) {
        this.data = data;
    }
}
