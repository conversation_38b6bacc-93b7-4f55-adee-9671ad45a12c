package com.tzx.pos.bo.imp.print;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.util.Comet4jUtil;
import com.tzx.framework.common.util.Tools;
import com.tzx.pos.base.constant.FunctionCode;
import com.tzx.pos.base.constant.PrinterSerialNumber;
import com.tzx.pos.bo.PosPrintModeService;
import com.tzx.pos.bo.imp.PosBaseServiceImp;
import com.tzx.pos.po.springjdbc.dao.PosDao;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

@Service("allRetreatFoodPrintServiceImp")
public class AllRetreatFoodPrintServiceImp extends PosBaseServiceImp implements PosPrintModeService{

	@Resource(name = PosDao.NAME)
	private PosDao				posDao;
	private static final Logger	logger	= Logger.getLogger(AllRetreatFoodPrintServiceImp.class);
			

	@Override
	public void posPrint(String tenancyId, int storeId, JSONObject para, String printMode) throws Exception
	{
		try {
			// 得到打印机id，是否区域，站点ID(新加)，打印机名称，IP地址，备打名称，备打地址，模板ID(hq_printer_model)
			List<JSONObject> printerList = posDao.getPrinter(tenancyId, storeId, printMode);

			if (printerList.size() > 0) {
				logger.debug(" 模板【" + printMode + "】绑定的打印机：" + printerList.toString());
				for (int i = 0; i < printerList.size(); i++) {
					JSONObject printerJo = printerList.get(i);
					int printerId = printerJo.optInt("printer_id");
					if (printerId == 0) {
						continue;
					}
					para.put("printer_id", printerId);

					// IP地址
					String ipCom = printerJo.optString("ip_com");
					int isArea = printerJo.optInt("is_area");
					// 区域打印
					if (isArea == 1) {
						// 判断桌位是否允许区域打印
						JSONObject printerTableJo = posDao.getPrinterTable(tenancyId, storeId, para);
						String areaId = printerTableJo.optString("area_id");
						String tableCode = printerTableJo.optString("table_code");
						if (Tools.isNullOrEmpty(areaId)) {
							logger.info(" 分单失败：打印机【" + printerId + "】没有绑定桌位【" + tableCode + "】");
							continue;
						} else {
							printerJo.put("area_id", areaId);
						}
					}

					List<JSONObject> printParamList = new ArrayList<JSONObject>();
					try {
						printParamList = posDao.getPrintParam(tenancyId, storeId, printMode, printerJo);
					} catch (Exception e) {
						logger.error("分单失败：打印机【" + printerId + "】获取打印参数失败 ", e);
					}

					if (printParamList.size() > 0) {
						for (JSONObject printParam : printParamList) {
							if (printParam.optString("param_pairs").isEmpty()) {
								logger.info(" 分单失败：打印机【" + printerId + "】的打印模板【" + printMode + "】未设定打印参数");
								continue;
							}
							JSONObject params = new JSONObject();
							JSONObject paramPairs = JSONObject.fromObject(printParam.opt("param_pairs"));

							// 获取打印参数
							JSONArray rows = paramPairs.optJSONArray("rows");
							Object value = null;
							String paramName = null;
							for (Object row : rows) {
								JSONObject rowJo = JSONObject.fromObject(row);

								paramName = rowJo.optString("paramName");
								value = para.get(paramName);
								if (Tools.isNullOrEmpty(value)) {
									value = rowJo.opt("value");
								}
								params.put(paramName, value);
							}

							params.put("rwid", para.optString("rwids"));

							// 套餐主项rwid
							String tczx_rwid = para.optString("tczx_rwid");
							tczx_rwid = "null".equals(tczx_rwid) || null == tczx_rwid ? "" : tczx_rwid;
							params.put("tczx_rwid", tczx_rwid);

							try {
								if (!"1".equals(para.optString("isPrintSerialNumber"))) {
									printerJo.put("serial_number", 0);
									params.put("serial_number", 0);
									// 生成打印任务
									posDao.insertPrintTask(tenancyId, storeId, printerJo, params, printMode);

								} else {
									synchronized (PrinterSerialNumber.class) { // 序号
										int serialNumber = PrinterSerialNumber
												.getSerialNumber(printerJo.optString("ip_com"));
										serialNumber += 1;
										printerJo.put("serial_number", serialNumber);
										params.put("serial_number", serialNumber);
										// 生成打印任务
										posDao.insertPrintTask(tenancyId, storeId, printerJo, params, printMode);
										PrinterSerialNumber.addSerialNumber(printerJo.optString("ip_com"),
												serialNumber);
									}
								}
							} catch (Exception e) {
								logger.error("分单失败：打印机【" + printerId + "】菜品【" + para.optString("rwid") + "】生成打印任务失败",
										e);
							}

							try {
								// 发出打印通知
								Data cjData = new Data();
								cjData.setType(Type.PRINT);
								cjData.setOper(Oper.print);
								JSONObject printJson = new JSONObject();
								printJson.put("print_address", ipCom);
								List<JSONObject> list = new ArrayList<>();
								list.add(printJson);
								cjData.setData(list);

								Comet4jUtil.sendMessage2All(Comet4jUtil.POS_PRINTER,
										JSONObject.fromObject(cjData).toString());
								logger.debug("打印分单成功：打印机【" + printerId + "】" + "，打印模板【" + printMode + "】" + "，菜品rwid【"
										+ para.optInt("rwid") + "】");
							} catch (Exception e) {
								logger.error("分单失败：打印机【" + printerId + "】服务器连接异常", e);
							}
						}
					} else {
						logger.info("分单失败：没有打印机【" + printerId + "】可以打印的菜品");
						continue;
					}
				}
			}

		} catch (Exception e) {
			logger.error("分单失败：其他原因导致分单失败", e);
		}
	}

}
