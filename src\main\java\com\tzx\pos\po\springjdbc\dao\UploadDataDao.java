package com.tzx.pos.po.springjdbc.dao;

import java.util.Date;
import java.util.List;

import net.sf.json.JSONObject;

import com.tzx.pos.base.dao.BaseDao;

public interface UploadDataDao extends BaseDao
{
	String	NAME	= "com.tzx.pos.po.springjdbc.dao.imp.UploadDataDaoImp";

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param tableName
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getPosUploadDataItem(String tenancyId, int storeId, String tableName, JSONObject param) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param tableName
	 * @param param
	 * @param isUploadOpenBill
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getPosAutoUploadDataItem(String tenancyId, int storeId, String tableName, JSONObject param, String isUploadOpenBill) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param tableName
	 * @param param
	 * @param cond
	 * @return
	 * @throws Exception
	 */
	public int[] updatePosItemUploadTag(String tenancyId, int storeId, String tableName, List<JSONObject> param, String cond) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @return
	 * @throws Exception
	 */
	public boolean updatePosUploadDate(String tenancyId, int storeId) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param reportDate
	 * @param isOpenBill
	 * @return
	 * @throws Exception
	 */
	public Integer getPosBillDataForCountByReportDate(String tenancyId, int storeId, Date reportDate, String isOpenBill) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param reportDate
	 * @param isOpenBill
	 * @param limitSize
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getPosBillDataByReportDate(String tenancyId, int storeId, Date reportDate, String isOpenBill, int limitSize) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param reportDate
	 * @param isOpenBill
	 * @param limitSize
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getPosBill2DataByReportDate(String tenancyId, int storeId, Date reportDate, String isOpenBill, int limitSize) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param tableName
	 * @param billList
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getPosBillLinkDataByBillList(String tenancyId, int storeId, String tableName, List<JSONObject> billList,Date reportDate) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param tableName
	 * @param unionTableName
	 * @param billList
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getPosBillLinkDataByBillList(String tenancyId, int storeId, String tableName, String unionTableName, List<JSONObject> billList,Date reportDate) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param reportDate
	 * @param tableName
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getPosUploadDataByReportDate(String tenancyId, int storeId, Date reportDate, String tableName) throws Exception;

    /** 重置数据上传标志位
     * @param tenancyId
     * @param storeId
     * @param reportDate
     * @param tableName
     * @return
     * @throws Exception
     */
    boolean resetUploadTagByReportDate(String tenancyId, int storeId, Date reportDate, String[] tableName) throws Exception;
    
    /** 设置数据上传标志位
     * @param tenancyId
     * @param storeId
     * @param reportDate
     * @param tableName
     * @return
     * @throws Exception
     */
    boolean setUploadTagByReportDate(String tenancyId, int storeId, Date reportDate, String tableName) throws Exception;

}
