package com.tzx.clientorder.wlifeprogram.bo.impl;

import com.alibaba.fastjson.JSON;
import com.tzx.base.common.util.IdempotentUtil;
import com.tzx.base.common.util.StringUtil;
import com.tzx.clientorder.wlifeprogram.bo.AfterPaymentService;
import com.tzx.clientorder.wlifeprogram.common.entity.*;
import com.tzx.clientorder.wlifeprogram.common.util.WlifeProgramUtil;
import com.tzx.clientorder.wlifeprogram.dao.AfterPaymentDao;
import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.SysParameterCode;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.OtherSystemException;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.CommonUtil;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.DoubleHelper;
import com.tzx.framework.common.util.Md5Utils;
import com.tzx.framework.common.util.Tools;
import com.tzx.member.common.entity.CrmCardTradingListEntity;
import com.tzx.member.crm.bo.CustomerCardConsumeService;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.constant.WLifeConstant;
import com.tzx.pos.base.util.CacheTableUtil;
import com.tzx.pos.base.util.JsonUtil;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.bo.*;
import com.tzx.pos.bo.payment.PosPaymentService;
import com.tzx.member.acewill.bo.AcewillCustomerService;
import com.tzx.clientorder.acewillwechat.bo.HoldBillService;
import com.tzx.clientorder.common.entity.*;
import com.tzx.clientorder.acewillwechat.po.springjdbc.dao.*;
import com.tzx.pos.po.springjdbc.dao.PosDishDao;
import com.tzx.pos.service.servlet.PosDishServlet;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import net.sf.json.JsonConfig;
import net.sf.json.util.PropertyFilter;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.beans.PropertyDescriptor;
import java.sql.Timestamp;
import java.util.*;

@Service(AfterPaymentService.NAME)
public class AfterPaymentServiceImpl implements AfterPaymentService {

    private static final Logger LOG = LoggerFactory.getLogger(AfterPaymentServiceImpl.class);


    private static final String SUCCESS = "success";
    private static final String MSG = "msg";
    private static final String DATA = "data";

//    private static final String SET_MEAL_TYPE = "2";

    public static final String LOCK_POS_NUM_WXWLIFE = "WXWlife";
    public static final String BILL_SOURCE = "MD01";

    @Resource(name = AfterPaymentDao.NAME)
    private AfterPaymentDao afterPaymentDao;

    @Resource(name = PosDishServlet.NAME)
    private PosDishServlet posDishServlet;

//    @Resource(name = PosBaseService.NAME)
//    private PosBaseService posBaseService;

    @Resource(name = PosDishService.NAME)
    private PosDishService posDishService;

    @Resource(name = PosPaymentService.NAME)
    private PosPaymentService posPaymentService;

    @Resource(name = AcewillInsertOrderDao.NAME)
    AcewillInsertOrderDao insertOrderDao;

    @Resource(name = PosDiscountService.NAME)
    private PosDiscountService posDiscountService;
    @Resource(name = HoldBillService.NAME)
    private HoldBillService holdBillService;
    @Resource(name = AcewillCustomerService.NAME)
    private AcewillCustomerService acewillCustomerService;
//    @Resource(name = TableStateDao.NAME)
//    private TableStateDao tableStateDao;
    @Resource(name = PreorderDao.NAME)
    private PreorderDao preorderDao;
    @Resource(name = HoldBillDao.NAME)
    private HoldBillDao holdBillDao;

    @Resource(name = AcewillPaySucessDao.NAME)
    private AcewillPaySucessDao paySucessDao;
    @Resource(name = PosPrintNewService.NAME)
    private PosPrintNewService posPrintNewService;

    @Resource(name = PosPrintService.NAME)
    private PosPrintService posPrintService;

    @Resource(name = CustomerCardConsumeService.NAME)
    private CustomerCardConsumeService customerService;

//    @Resource
//    private PosDao posDao;
//    @Resource(name = OrderDao.NAME)
//    private OrderDao orderDao;
    @Resource(name = PosDishDao.NAME)
    private PosDishDao			posDishDao;

    @Override
    @Deprecated
    public void orderDish(JSONObject param, JSONObject printJson, JSONObject responseJson) throws Exception {
        String tenancyId = param.optString("business_id");
        int storeId = param.optInt("shop_id");
        String orderNum = param.optString("order_id"); // 平台订单号
        JSONObject orderInfo = param.getJSONObject("order_info"); // 订单信息

        if (StringUtils.isEmpty(tenancyId) || storeId == 0
                || orderInfo.isEmpty()
                || !orderInfo.containsKey("items")){
            throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
        }

        Object dishObj = orderInfo.get("items"); // 菜品明细
        if (dishObj instanceof JSONArray){
            throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
        }
        JSONArray dishArray = JSONArray.fromObject(dishObj);
        if (dishArray.isEmpty()){
            throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
        }
        JSONArray newItemArray = new JSONArray(); // 菜品明细
        for (int i = 0; i < dishArray.size(); i++){
            JSONObject dish = dishArray.getJSONObject(i);
            if (dish.isEmpty()){
                throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
            }
            JSONObject newDish = new JSONObject();
            newDish.put("item_id", dish.optString("did")); // 菜品id
            newDish.put("item_num", dish.optString("dishsno")); // 菜品速记码
            newDish.put("item_name", dish.optString("name")); // 菜品名称
            newDish.put("unit_id", dish.optString("duid")); // 菜品单位id
            newDish.put("item_count", dish.optDouble("number")); // 数量
//            newDish.put("", dish.optString("price")); // 现价
            newDish.put("item_price", dish.optDouble("orgprice")); // 原价
            newDish.put("third_price", dish.optDouble("memberprice")); // 会员价

            JSONArray methodArray = new JSONArray();
            String cooks = dish.optString("cooks"); // 做法id 数组
            if (StringUtils.isNotEmpty(cooks)){
                String[] cookIds = cooks.split(",");
                for (String cookId : cookIds) {
                    if (StringUtils.isNotEmpty(cookId)) {
                        JSONObject method = new JSONObject();
                        method.put("method_id", cookId);
                        methodArray.add(method);
                    }
                }
            }
            newDish.put("method", methodArray);

            String itemProperty = dish.optString("type"); // 菜品类型, 1:非套餐 2:套餐
            if ("1".equals(itemProperty)){
                newDish.put("item_property", SysDictionary.ITEM_PROPERTY_SINGLE);
            } else {
                newDish.put("item_property", SysDictionary.ITEM_PROPERTY_SETMEAL);
            }
            newDish.put("setmeal_id", newDish.optString("item_id")); // 套餐或套餐明细setmeal_id = 套餐item_id
            // 始终=1，下单接口会计算递增 item_serial 值，setmeal_rwid（仅针对套餐或套餐明细） 值
            newDish.put("setmeal_rwid", 1);
            newDish.put("item_serial", 1);
            int isGift = dish.optInt("is_gift"); // 是否赠菜, 0:否 1:是
            if (1 == isGift){
                newDish.put("item_remark", SysDictionary.ITEM_REMARK_FS02);
            }
            int isMemberprice = dish.optInt("is_memberprice"); // 是否使用会员价(0:否 1:是)
            if (1 == isMemberprice){
                newDish.put("discount_mode_id", SysDictionary.DISCOUNT_MODE_6);
            }

//            newDish.put("", dish.optInt("is_bargainprice")); // 是否使用特价, 0:否 1:是
            newDish.put("item_class_id", dish.optString("pkid")); // 菜品所属末端分类id
            newDish.put("item_amount", dish.optDouble("realprice")); // 总价(包括加价)
            newDish.put("item_taste", dish.optString("remark")); // 单品备注

            newItemArray.add(newDish);
        }

        // 账单备注
        String billTaste = orderInfo.optString("ordermemo");
        // 是否打印，默认值=Y 打印
        String isPrint = orderInfo.optString("is_print", "Y");
        // 顾客人数，默认值=1
        int guestNum = orderInfo.optInt("people", 1);
        // 桌位号
        String tableCode = ParamUtil.getStringValueByObject(orderInfo, "tableno", true, PosErrorCode.NOT_NULL_TABLE_CODE);
        // 桌台状态
        String tableState = afterPaymentDao.getTableState(tenancyId, storeId, tableCode);
        if (StringUtils.isEmpty(tableState)){
            throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_TABLE_CODE);
        }

        if (SysDictionary.TABLE_STATE_FREE.equals(tableState)){ // 空闲，需开台
            // 报表日期
            Date reportDate = afterPaymentDao.getReportDate(tenancyId, storeId);
            String reportDateStr = DateUtil.format(reportDate);

            // 查询最后一个签到机台
            List<JSONObject> optStateInfo = posDishService.getOptStateInfo(tenancyId, storeId, reportDateStr);
            if (optStateInfo == null || optStateInfo.size() == 0){
                throw new OtherSystemException(Constant.CODE_NULL_DATASET, Constant.CHECK_POS_LOGIN_FAILURE, null);
            }
            JSONObject optStateJson = optStateInfo.get(0);
            String posNum = optStateJson.optString("pos_num");
            String shiftId = optStateJson.optString("shift_id");
            String optNum = optStateJson.optString("opt_num");
           //开台
            JSONObject openJson = new JSONObject();
            openJson.put("report_date", reportDateStr);
            openJson.put("table_code", tableCode);
            openJson.put("chanel", SysDictionary.CHANEL_WX02);
            openJson.put("mode", 0);
            openJson.put("guest", guestNum);
            openJson.put("sale_mode", SysDictionary.SALE_MODE_TS01);
            openJson.put("pos_num", posNum);
            openJson.put("shift_id", shiftId);
            openJson.put("opt_num", optNum);
            openJson.put("waiter_num", optNum); // waiter_num 和 opt_num 取值一样
            openJson.put("split_flag", "N"); // 是否拆台

            List<JSONObject> openTableList = new ArrayList<>();
            openTableList.add(openJson);

            Data openTableData = new Data();
            openTableData.setTenancy_id(tenancyId);
            openTableData.setStore_id(storeId);
            openTableData.setType(Type.NEW_OPEN_TABLE);
            openTableData.setData(openTableList);

            List<JSONObject> openInfoList = posDishServlet.newestOpenTable(openTableData, printJson);
            if (openInfoList == null || openInfoList.size() == 0){
                throw new OtherSystemException(Constant.CODE_NULL_DATASET, Constant.OPEN_TABLE_FAILURE, null);
            }
            JSONObject openInfo = openInfoList.get(0);

            String billNum = openInfo.optString("bill_num");
            if (StringUtils.isEmpty(billNum)){
                throw SystemException.getInstance(PosErrorCode.GET_BILLNUM_SERVICE_ERROR);
            }

           //下单
            JSONObject orderDishJson = new JSONObject();
            orderDishJson.put("mode", "0");
            orderDishJson.put("isprint", isPrint);
            orderDishJson.put("report_date", reportDateStr);
            orderDishJson.put("shift_id", shiftId);
            orderDishJson.put("pos_num", posNum);
            orderDishJson.put("opt_num", optNum);
            orderDishJson.put("bill_num", billNum);
            orderDishJson.put("sale_mode", SysDictionary.SALE_MODE_TS01);
            orderDishJson.put("table_code", tableCode);
            orderDishJson.put("waiter_num", posNum);
            orderDishJson.put("chanel", SysDictionary.CHANEL_WX02);
            orderDishJson.put("bill_taste", billTaste); //整单备注

            orderDishJson.put("item", newItemArray); // 菜品明细

            List<JSONObject> orderDishList = new ArrayList<>();
            orderDishList.add(orderDishJson);

            Data orderDishData = Data.get();
            orderDishData.setTenancy_id(tenancyId);
            orderDishData.setStore_id(storeId);
            orderDishData.setType(Type.ORDERINGNEW);
            orderDishData.setData(orderDishList);

            printJson.clear(); // 清空 printJson
            posDishService.newestOrderDish(orderDishData, printJson);


        } else if (SysDictionary.TABLE_STATE_BUSY.equals(tableState)){ // 占用，则判断是否可以加菜
            List<JSONObject> billList = afterPaymentDao.getBillInfoByOrderNum(tenancyId, storeId, orderNum);
            if (billList.size() == 0){
                throw new OtherSystemException(Constant.CODE_NULL_DATASET, Constant.PAYMENT_ALIPAY_QUERY, null);
            }

        } else {
            throw SystemException.getInstance(PosErrorCode.TABLE_STATE_EXCEPTION);
        }

    }

    @Override
    public void saveOrUpdateOrder(JSONObject param, JSONObject printJson, JSONObject responseJson) throws Exception {
        String tenancyId = param.optString("business_id");
        int storeId = param.optInt("shop_id");
        JSONObject order = param.getJSONObject("order_info"); // 订单信息

        if (StringUtils.isEmpty(tenancyId) || storeId == 0
                || order.isEmpty()) {
            throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
        }

        Object setmeal = order.get("setmeal"); // 菜品明细
        Object normalitems = order.get("normalitems"); // 菜品明细

        JSONArray dishArray = JSONArray.fromObject(setmeal);
        JSONArray normalArray = JSONArray.fromObject(normalitems);
        if (dishArray.isEmpty() && normalArray.isEmpty()){
            throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
        }
        MicroLifeOrder microLifeOrder = (MicroLifeOrder) JSONObject.toBean(param,getJsonConfig());
        microLifeOrder.setShop_id(storeId);
        microLifeOrder.setBusiness_id(tenancyId);
        microLifeOrder.setBrand_id(null);
        // 获得订单信息
        MicroLifeOrderInfo microLifeOrderInfo = microLifeOrder.getOrder_info();

        // 查询桌台状态
        JSONObject tableStatus = afterPaymentDao.getTableStatus(tenancyId, storeId, microLifeOrderInfo.getTableno());
        String errorMsg = null;
        if (tableStatus != null) {

            boolean isLock = isLockByOther(microLifeOrder, tableStatus);
            if (!isLock) {
                // 未锁台
                AcewillOrderAssist orderAssist;
                String state = tableStatus.getString("state");
                if (SysDictionary.TABLE_STATE_FREE.equals(state)) {

                    JSONObject posBill = insertOrderDao.getPosBillByOrderNum(tenancyId, microLifeOrder.getOut_order_id());
                    if (posBill != null) {
                        throw new OtherSystemException(PosErrorCode.OPER_ERROR.getNumber(), "单号重复" + microLifeOrder.getOut_order_id(), null);
                    }
                    // 未占用，开台
                    orderAssist = createOrderAssist(microLifeOrder);
                    Data createOpenTableData = createOpenTableData(microLifeOrder, orderAssist);
                    List<JSONObject> openTableList = posDishServlet.newestOpenTable(createOpenTableData,
                            new JSONObject());
                    if (openTableList == null || openTableList.size() == 0) {
                        throw new OtherSystemException(Constant.CODE_NULL_DATASET, Constant.OPEN_TABLE_FAILURE, null);
                    }

                    JSONObject posBillJson = JSONObject.fromObject(openTableList.get(0));
                    String billNum = posBillJson.optString("bill_num");
                    if (StringUtils.isEmpty(billNum)) {
                        throw SystemException.getInstance(PosErrorCode.GET_BILLNUM_SERVICE_ERROR);
                    }
                    orderAssist.setBill_num(billNum);
                    insertOrderDao.insertOpenId(tenancyId, storeId, orderAssist.getBill_num(), microLifeOrder.getOpenid());
                }else  if (SysDictionary.TABLE_STATE_BUSY.equals(state)) {
                    // 已占用，订单号
                    JSONObject posBill = insertOrderDao.getPosBill(microLifeOrder.getBusiness_id(),
                            microLifeOrder.getShop_id(), microLifeOrderInfo.getTableno());

                    orderAssist = createOrderAssist(posBill);
                    insertOrderDao.updateOpenId(orderAssist.getBill_num(), microLifeOrder.getOpenid());
                }else {
                    LOG.error("storeId={},tablecode={} status error",storeId,microLifeOrderInfo.getTableno());
                    throw SystemException.getInstance(PosErrorCode.TABLE_STATE_EXCEPTION);
                }
                //是否并台之后加菜
                posDishDao.checkCombineTable(tenancyId, storeId, orderAssist.getBill_num());

                //查询账单 pos是否合并账单
                JSONObject bill = posDishService.isCombineTableOfBillNum(tenancyId,storeId, orderAssist.getBill_num());
                if(null!=bill){ //存在并台
                    String bill_num = bill.optString("bill_num");
                    String table_code = bill.optString("table_code");
                    orderAssist.setBill_num(bill_num);
                    microLifeOrder.setTableno(table_code);
                    microLifeOrderInfo.setTableno(table_code);
                }



                // 拼装下单所需参数
                Data orderParam = createOrderDishData(microLifeOrder, orderAssist);

                // 下单
                Data newestOrderDish = posDishService.newestOrderDish(orderParam, printJson);

                //更新OpenId
                if (newestOrderDish.getCode() == 0) {
                    // 会员信息
                    String sysParameter = CacheTableUtil.getSysParameter("CustomerType");
                    if (SysDictionary.CUSTOMER_TYPE_ACEWILL.equals(sysParameter) && null != order.optJSONObject("member")) {
                        savePosBillMember(microLifeOrder, orderAssist);
                        //处理折扣
                        //查询账单pos_bill，判断账单有没折扣
                        JSONObject member = order.optJSONObject("member");
                        JSONObject billObject = preorderDao.getBillDiscount(tenancyId, storeId, microLifeOrderInfo.getTableno());
                        if(null == billObject ){
                            LOG.error("进行折扣的门店为{}，桌号为:{}的账单不存在", storeId,microLifeOrderInfo.getTableno());
                        }
                        if(null != member){
                            setMemberDiscount(member, tenancyId,microLifeOrder.getShop_id(), microLifeOrderInfo.getTableno(), billObject);
                        }

                    }
                    //更新posbill
                    /*if(SysDictionary.TABLE_STATE_FREE.equals(state)){
                        if(null != orderAssist.getBill_num()){
                            afterPaymentDao.updatePosBillSource(orderAssist.getBill_num(),SysDictionary.CHANEL_WSP17,true);
                        }
                    }*/

                    if (null != orderAssist.getBill_num()) {
                        afterPaymentDao.updatePosBillOrderNum(microLifeOrder.getOut_order_id(), orderAssist.getBill_num());
                    }

                    String opType = SysDictionary.TABLE_STATE_FREE.equals(state) ? WLifeConstant.OPT_TYPE_NEW : WLifeConstant.OPT_TYPE_ADD;
                    JSONObject orderData = getOrderDetail(microLifeOrder.getBusiness_id(), microLifeOrder.getShop_id(), microLifeOrderInfo.getTableno(), opType);
                    LOG.info(" getOrderDetail data:" + orderData.toString());

                    buildSuccessData(orderData, responseJson, orderAssist.getBill_num(),true);
                } else {
                    if (Constant.CODE_PARAM_FAILURE == newestOrderDish.getCode()) {
                        throw new OtherSystemException(PosErrorCode.GREAT_THAN_SOLD_OUT_COUNT.getNumber(), newestOrderDish.getMsg(), null);
                    } else {
                        throw new OtherSystemException(newestOrderDish.getCode(), newestOrderDish.getMsg(), null);
                    }

                }
            } else {
                errorMsg = "桌台已锁单";
                LOG.error(errorMsg);
                String name = "";
                String lockOptNum = tableStatus.getString("lock_opt_num");
                String lockPosNum = tableStatus.optString("lock_pos_num");
                if(LOCK_POS_NUM_WXWLIFE.equals(lockPosNum)){
                    name = tableStatus.optString("opt_name");
                }else {
                    name = afterPaymentDao.getEmpNameById(lockOptNum, tenancyId, storeId);
                }

                throw SystemException.getInstance(PosErrorCode.TABLE_ALREADY_LOCK_ERROR).set("{0}", name).set("{1}", lockPosNum);
            }
        } else {
            errorMsg = "落单失败:当前桌台不存在";
            LOG.error(errorMsg);
            throw SystemException.getInstance(errorMsg, PosErrorCode.NOT_EXISTS_TABLE_CODE);
        }
    }


    private  boolean isLockByOther(MicroLifeOrder microLifeOrder,JSONObject tableStatus){
        boolean flag = false;
        MicroLifeOrderInfo orderInfo = microLifeOrder.getOrder_info();
        AcewillOrderMember member = orderInfo.getMember();
        // 查询桌台状态
        try {
            String state = tableStatus.optString("state");
            String lockOptNum = tableStatus.optString("lock_opt_num");
            String mobile = member.getMobile();
            if(SysDictionary.TABLE_STATE_BUSY.equals(state)&& StringUtil.hasText(lockOptNum)){
                if (StringUtil.hasText(mobile) && !mobile.equals(lockOptNum)) {
                    flag = true;
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return  flag;
    }


    private  void buildSuccessData(JSONObject data, JSONObject responseData, String oid, boolean isOrdering){
        if(null == data){
            data = new JSONObject();
        }
        if(null != oid){
            data.put("oid", oid);
        }
        if(isOrdering){
            data.put("status", 1);
        }

        responseData.put("success",1);
        responseData.put(MSG,"SUCCESS");
        responseData.put(DATA, data);
    }

    private AcewillPosBillMember savePosBillMember(MicroLifeOrder microLifeOrder, AcewillOrderAssist orderAssist)
            throws Exception {
        MicroLifeOrderInfo orderInfo = microLifeOrder.getOrder_info();
        AcewillOrderMember member = orderInfo.getMember();
        member.setBalance(DoubleHelper.div(member.getBalance(), 100d, DEFAULT_SCALE));
        
        insertOrderDao.deletePosBillMember(microLifeOrder.getBusiness_id(), microLifeOrder.getShop_id(), orderAssist.getBill_num(), SysDictionary.BILL_MEMBERCARD_JFZS06);
        
        AcewillPosBillMember posBillMember = new AcewillPosBillMember();
        posBillMember.setTenancy_id(microLifeOrder.getBusiness_id());
        posBillMember.setStore_id(microLifeOrder.getShop_id());
        posBillMember.setBill_num(orderAssist.getBill_num());
        posBillMember.setBill_code(microLifeOrder.getOut_order_id());
        posBillMember.setReport_date(com.tzx.pos.base.util.DateUtil.parseDate(orderAssist.getReport_date()));
        posBillMember.setType(SysDictionary.BILL_MEMBERCARD_JFZS06);
        posBillMember.setAmount(null);

        posBillMember.setCredit(0.0);
        posBillMember.setCard_code(member.getCno());
        posBillMember.setMobil(member.getMobile());
        posBillMember.setLast_updatetime(new Date());
        posBillMember.setUpload_tag(0);
        posBillMember.setRemark(null);
        posBillMember.setBill_code(null);
        posBillMember.setRequest_state(null);
        posBillMember.setCustomer_code(member.getCno());
        posBillMember.setCustomer_name(member.getName());
        posBillMember.setConsume_before_credit(member.getCredit());
        posBillMember.setConsume_after_credit(member.getCredit());
        posBillMember.setConsume_before_main_balance(member.getBalance());
        //交易前赠送账户余额
        posBillMember.setConsume_before_reward_balance(0d);
        //交易后主账户余额
        posBillMember.setConsume_after_main_balance(member.getBalance());
        //交易后赠送账户余额
        posBillMember.setConsume_after_reward_balance(0d);
        insertOrderDao.savePosBillMember(posBillMember);
        return posBillMember;
    }

    /** 组织下单参数
     * @param microLifeOrder
     * @param orderAssist
     * @return
     * @throws Exception
     */
    private Data createOrderDishData(MicroLifeOrder microLifeOrder, AcewillOrderAssist orderAssist) throws Exception {
    	AcewillOrder order = createOrder(microLifeOrder, orderAssist);
        JSONObject orderJsonObject = JSONObject.fromObject(order, getAcewillOrderJsonConfig());
    	
        List<JSONObject> orderList = new ArrayList<>();
        orderList.add(orderJsonObject);
        
    	Data data = new Data();
        data.setTenancy_id(microLifeOrder.getBusiness_id());
        data.setStore_id(microLifeOrder.getShop_id());
        data.setType(Type.ORDERING);
        data.setOper(Oper.add);
        data.setSource("cc_order");
        data.setData(orderList);
        return data;
    }
    private JsonConfig getAcewillOrderJsonConfig() {
        Map<String, Class<?>> classMap = new HashMap<>();
        classMap.put("item", AcewillOrderItem.class);
        classMap.put("method", AcewillOrderItemMethod.class);
        JsonConfig jsonConfig = new JsonConfig();
        jsonConfig.setClassMap(classMap);
        jsonConfig.setRootClass(AcewillOrder.class);
        return jsonConfig;
    }
    private AcewillOrder createOrder(MicroLifeOrder microLifeOrder, AcewillOrderAssist orderAssist) throws Exception {

        MicroLifeOrderInfo microLifeOrderInfo = microLifeOrder.getOrder_info();

        AcewillOrder acewillOrder = new AcewillOrder();
        // 账单号
        acewillOrder.setBill_num(orderAssist.getBill_num());
        // 整单备注
        //String billTaste = insertOrderDao.getBillTaste(insertOrder);
        AcewillOrderMemo ordermemo = microLifeOrderInfo.getOrdermemo();

//        acewillOrder.setBill_taste(ordermemo.getText());

        // 是否厨打
        acewillOrder.setIsprint("Y");
        // 0:下单 1:
        acewillOrder.setMode(0);
        // 操作员编号
        acewillOrder.setOpt_num(orderAssist.getOpt_num());
        // 收款机编号
        acewillOrder.setPos_num(orderAssist.getPos_num());
        // 备注
        if(StringUtil.hasText(ordermemo.getText())){
            acewillOrder.setRemark(ordermemo.getText());
        }else {
            acewillOrder.setRemark("");
        }
        // 报表日期
        acewillOrder.setReport_date(orderAssist.getReport_date());
        // 销售模式
        // acewillOrder.setSale_mode(sale_mode);
        // 班次id
        acewillOrder.setShift_id(orderAssist.getShift_id());
        //桌号
        acewillOrder.setTable_code(microLifeOrderInfo.getTableno());
        // 服务员号
        acewillOrder.setWaiter_num(null);
        //菜品明细
        List<AcewillOrderItem> orderItems = new ArrayList<>();
        acewillOrder.setItem(orderItems);
        // 点餐序号
        Integer item_serial = insertOrderDao.getLastItemSerial(microLifeOrder.getBusiness_id(), orderAssist.getBill_num());
        
        //获取套餐档案
        Map<String, Map<String, JSONObject>> itemComboMap = this.getComboDetailMap(microLifeOrder.getBusiness_id(), microLifeOrderInfo.getSetmeal());        
        //套餐主项默认规格id不需要设置
        getSetmealUnitMap(microLifeOrder);
        
        //查询规格
        Map<String, String> unitNameMap = this.getUnitNameMap(microLifeOrder);
        
        // 套餐明细
        List<AcewillOrderSetmeal> setmealList = microLifeOrderInfo.getSetmeal();
        if(setmealList!=null) {
            for (AcewillOrderSetmeal setmeal : setmealList) {
				// 点餐序号
				item_serial++;

				// 组织套餐主项数据
				String duName = unitNameMap.get(setmeal.getDuid());
				setmeal.setDuName(duName);
				
				orderItems.add(this.createOrderItem(setmeal, item_serial));


				//合并套餐主菜,辅菜
				List<AcewillOrderSetmealItem> setmealItemList = new ArrayList<>();
				if (null != setmeal.getMaindish()){
					setmealItemList.addAll(setmeal.getMaindish());
				}
				if (null != setmeal.getMandatory()){
					setmealItemList.addAll(setmeal.getMandatory());
				}
				if (null != setmeal.getOptional()){
					setmealItemList.addAll(setmeal.getOptional());
				}
				//获取套餐明细信息
				Map<String, JSONObject> itemComboDetailMap = itemComboMap.get(setmeal.getDid());
				// 组织套餐明细数据
				List<AcewillOrderItem> setmealOrderItemList = new ArrayList<AcewillOrderItem>();
				for (AcewillOrderSetmealItem item : setmealItemList){
					item.setNumber(setmeal.getNumber() * item.getNumber());
					item.setMallListName();// 设置套餐明细菜品名称
					String duItemName = unitNameMap.get(item.getDuid());
					item.setDuName(duItemName);
					//获取套餐明细信息
					JSONObject itemComboDetail = this.getComboDetailByDetailsId(itemComboDetailMap, item);
					
					AcewillOrderItem setmealOrderItem = this.createOrderItem(setmeal, item, item_serial, itemComboDetail);
					setmealOrderItemList.add(setmealOrderItem);
				}
				// 套餐明细排序
				orderItems.addAll(this.sortOrderSetmealItem(setmealOrderItemList));
            }
        }
        
        //单品
        List<AcewillOrderNormalitem> normalitemList = microLifeOrderInfo.getNormalitems();
        if(normalitemList!=null) {
            for (AcewillOrderNormalitem item : normalitemList) {
                item_serial++;
                String duName = unitNameMap.get(item.getDuid());
                item.setDuName(duName);
                
                AcewillOrderItem setmealOrderItem = this.createOrderItem(item, item_serial);
                orderItems.add(setmealOrderItem);
            }
        }

        return acewillOrder;
    }
    
    /** 查询套餐主项默认规格
     * @param microLifeOrder
     * @return
     * @throws Exception
     */
    private Map<String, String> getSetmealUnitMap(MicroLifeOrder microLifeOrder) throws Exception {
        
    	String tenentId = microLifeOrder.getBusiness_id();
        List<AcewillOrderSetmeal> setmealList = microLifeOrder.getOrder_info().getSetmeal();
        List<String> itemIdList = new ArrayList<>();
        if(setmealList!=null) {
            for (AcewillOrderSetmeal setmeal : setmealList) {
                itemIdList.add(setmeal.getDid());
            }
        }
        //查询默认规格
        Map<String,String> setmealUnitMap = new HashMap<String,String>();
        List<JSONObject> findItemUnit = insertOrderDao.findItemUnit(tenentId, itemIdList);
        if(findItemUnit!=null) {
            for (JSONObject jsonObject : findItemUnit) {
                setmealUnitMap.put(jsonObject.getString("item_id"), jsonObject.getString("id"));
            }
        }
        
      //设置套餐主项菜品规格
        if(setmealList!=null) {
            for (AcewillOrderSetmeal setmeal : setmealList) {
                String setmealUnitId = setmealUnitMap.get(setmeal.getDid());
                if(!StringUtils.isEmpty(setmealUnitId)) {
                    setmeal.setDuid(setmealUnitId);
                }
            }
        }
        return setmealUnitMap;
    }
    
	/**查询规格
	 * @param microLifeOrder
	 * @return
	 * @throws Exception
	 */
	private Map<String, String> getUnitNameMap(MicroLifeOrder microLifeOrder) throws Exception
	{
		String tenancyId = microLifeOrder.getBusiness_id();
		Integer storeId = microLifeOrder.getShop_id();
		
		List<String> duids = new ArrayList<>();
		List<AcewillOrderSetmeal> setmealList = microLifeOrder.getOrder_info().getSetmeal();
		if (setmealList != null)
		{
			for (AcewillOrderSetmeal setmeal : setmealList)
			{
				List<AcewillOrderSetmealItem> setmealItemList = new ArrayList<AcewillOrderSetmealItem>();
				if (null != setmeal.getMaindish())
				{
					setmealItemList.addAll(setmeal.getMaindish());
				}
				if (null != setmeal.getMandatory())
				{
					setmealItemList.addAll(setmeal.getMandatory());
				}

				if (null != setmeal.getOptional())
				{
					setmealItemList.addAll(setmeal.getOptional());
				}
				
				for (AcewillOrderSetmealItem setmealItem : setmealItemList)
				{
					duids.add(setmealItem.getDuid());
				}
				duids.add(setmeal.getDuid());
			}
		}
		// 单品中的规格id
		List<AcewillOrderNormalitem> normalList = microLifeOrder.getOrder_info().getNormalitems();
		if (normalList != null)
		{
			for (AcewillOrderNormalitem normal : normalList)
			{
				duids.add(normal.getDuid());
			}
		}

		List<JSONObject> list = afterPaymentDao.getUnitNameList(tenancyId, storeId, duids);
		Map<String, String> unitNameMap = new HashMap<>();
		if (null != list)
		{
			for (JSONObject jsonObject : list)
			{
				unitNameMap.put(jsonObject.getString("id"), jsonObject.getString("unit_name"));
			}
		}
		return unitNameMap;
	}

    //套餐菜品id
    private Map<String, JSONObject> itemComboDetailMap(MicroLifeOrder microLifeOrder)
            throws Exception {
        String TenentId = microLifeOrder.getBusiness_id();
        MicroLifeOrderInfo microLifeOrderInfo = microLifeOrder.getOrder_info();
        List<Integer> itemIdList = new ArrayList<>();
        List<AcewillOrderSetmeal> setmealList = microLifeOrderInfo.getSetmeal();
        if (setmealList!=null) {
            for (AcewillOrderSetmeal setmeal : setmealList) {
                List<AcewillOrderSetmealItem> setmealItemList = new ArrayList<>();
                if(null != setmeal.getMaindish()){
                    setmealItemList.addAll(setmeal.getMaindish());
                }
                if(null != setmeal.getMandatory()){
                    setmealItemList.addAll(setmeal.getMandatory());
                }
               if(null !=setmeal.getOptional()){
                   setmealItemList.addAll(setmeal.getOptional());
               }

                for (AcewillOrderSetmealItem item : setmealItemList) {
                    itemIdList.add(Integer.parseInt(item.getId()));
                }
                itemIdList.add(Integer.parseInt(setmeal.getDid()));
            }
        }
        List<AcewillOrderNormalitem> normalitemList = microLifeOrderInfo.getNormalitems();
        if(normalitemList!=null) {
            for (AcewillOrderNormalitem item : normalitemList) {
                itemIdList.add(Integer.parseInt(item.getDid()));
            }
        }
        List<JSONObject> itemComboDetails = insertOrderDao.getItemComboDetails(TenentId, itemIdList);
        Map<String,JSONObject> itemComboDetailMap = new HashMap<>();
        if(itemComboDetails!=null) {
            for (JSONObject jsonObject : itemComboDetails) {
                //itemComboDetailMap.put(jsonObject.getString("details_id"), jsonObject);
                itemComboDetailMap.put(jsonObject.getString("iitem_id"), jsonObject);
            }
        }
        return itemComboDetailMap;
    }
    
	/** 查询套餐明细
	 * @param tenantId
	 * @param setmealList
	 * @return
	 * @throws Exception
	 */
	private Map<String, Map<String, JSONObject>> getComboDetailMap(String tenantId,List<AcewillOrderSetmeal> setmealList) throws Exception
	{
		List<Integer> itemIdList = new ArrayList<>();
		if (setmealList != null)
		{
			for (AcewillOrderSetmeal setmeal : setmealList)
			{
				itemIdList.add(Integer.parseInt(setmeal.getDid()));
			}
		}

		//查询套餐明细
		List<JSONObject> omboDetailsList = afterPaymentDao.getItemComboDetails(tenantId, itemIdList);

		Map<String, Map<String, JSONObject>> comboMap = new HashMap<String, Map<String, JSONObject>>();
		if (null != omboDetailsList)
		{
			for (JSONObject comboDetail : omboDetailsList)
			{
				String iItemId = comboDetail.optString("iitem_id");//套餐主项ID
				String isItemGroup = comboDetail.optString("is_itemgroup");
				String detailsId = comboDetail.optString("details_id");
				String itemUnitId = comboDetail.optString("item_unit_id");

				Map<String, JSONObject> comboDetailMap = null;
				if (comboMap.containsKey(iItemId))
				{
					comboDetailMap = comboMap.get(iItemId);
				}
				else
				{
					comboDetailMap = new HashMap<String, JSONObject>();
				}

				if ("Y".equals(isItemGroup))
				{
					comboDetailMap.put(detailsId, comboDetail);
				}
				else
				{
					comboDetailMap.put(detailsId + "_" + itemUnitId, comboDetail);
				}

				comboMap.put(iItemId, comboDetailMap);
			}
		}
		return comboMap;
	}
	
	private JSONObject getComboDetailByDetailsId(Map<String, JSONObject> itemComboDetailMap,AcewillOrderSetmealItem item) throws Exception
	{
		JSONObject itemComboDetail = null;
		if (null != itemComboDetailMap)
		{
			if (Tools.hv(item.getRpdid()))
			{
				itemComboDetail = itemComboDetailMap.get(item.getRpdid());
			}
			if (null == itemComboDetail)
			{
				if (itemComboDetailMap.containsKey(item.getId() + "_" + item.getDuid()))
				{
					itemComboDetail = itemComboDetailMap.get(item.getId() + "_" + item.getDuid());
				}
				else
				{
					itemComboDetail = itemComboDetailMap.get(item.getId());
				}
			}
		}
		return itemComboDetail;
	}

	/** 单品
	 * @param item
	 * @param item_serial
	 * @param itemComboDetail
	 * @return
	 */
	private AcewillOrderItem createOrderItem(AcewillOrderNormalitem item, Integer item_serial) {
        AcewillOrderItem orderItem = new AcewillOrderItem();
        // 辅助Assist
//        if(itemComboDetail!=null) {
//            orderItem.setAssist_item_id(itemComboDetail.getString("id"));
//            orderItem.setAssist_num(itemComboDetail.getString("combo_num"));
//        }else {
            orderItem.setAssist_item_id("0");
            orderItem.setAssist_num("0");
//        }
        // orderItem.setAssist_money(assist_money);
        // 餐谱明细id orderItem.setDetails_id(details_id);

        orderItem.setItem_count(item.getNumber());

        orderItem.setItem_id(item.getDid());
        orderItem.setItem_name(item.getName());
        orderItem.setItem_num(item.getDishsno());
        orderItem.setItem_price(item.getPrice());
        orderItem.setItem_property(SysDictionary.ITEM_PROPERTY_SINGLE);
        orderItem.setItem_remark(null);
        // 点菜序号
        orderItem.setItem_serial(item_serial);
        orderItem.setItem_taste(item.getRemark());
        // 规格名称
        orderItem.setItem_unit_name(item.getDuName());

        // orderItem.setSale_mode(sale_mode);

        orderItem.setMethod(createItemMethod(item.getCooks()));
        // 座位号
        orderItem.setSeat_num(null);
        // 套菜点菜号
        orderItem.setSetmeal_id(null);
        orderItem.setSetmeal_rwid(null);
        // 规格id
        orderItem.setUnit_id(item.getDuid());
        // 等叫标记
        // orderItem.setWaitcall_tag(waitcall_tag);
        return orderItem;
    }
    /** 套餐主项
     * @param setmeal
     * @param item_serial
     * @param itemComboDetail
     * @return
     */
	private AcewillOrderItem createOrderItem(AcewillOrderSetmeal setmeal, Integer item_serial) {
        AcewillOrderItem orderItem = new AcewillOrderItem();
        // 辅助Assist
//        if(itemComboDetail!=null) {
//            orderItem.setAssist_item_id(itemComboDetail.getString("id"));
//            orderItem.setAssist_num(itemComboDetail.getString("combo_num"));
//        }else {
            orderItem.setAssist_item_id("0");
            orderItem.setAssist_num("0");
//        }
        // orderItem.setAssist_money(assist_money);
        // 餐谱明细id orderItem.setDetails_id(details_id);
        orderItem.setItem_count(setmeal.getNumber());

        orderItem.setItem_id(setmeal.getDid());
        orderItem.setItem_name(setmeal.getName());
        orderItem.setItem_num(setmeal.getDishsno());
        orderItem.setItem_price(setmeal.getPrice());
        orderItem.setItem_property(SysDictionary.ITEM_PROPERTY_SETMEAL);

        //orderItem.setItem_remark(setmeal.getRemark());
        // 点菜序号
        orderItem.setItem_serial(item_serial);
        orderItem.setItem_taste(setmeal.getRemark());

        // 规格名称
        orderItem.setItem_unit_name(setmeal.getDuName());
        // orderItem.setSale_mode(sale_mode);
        orderItem.setMethod(createItemMethod(setmeal.getCooks()));
        // 座位号
        orderItem.setSeat_num(null);
        orderItem.setSetmeal_id(setmeal.getDid());
        // 套菜点菜号
        orderItem.setSetmeal_rwid(item_serial);
        // 规格id
        orderItem.setUnit_id(setmeal.getDuid());
        // 等叫标记
        // orderItem.setWaitcall_tag(waitcall_tag);
        return orderItem;
    }
//    private AcewillOrderItem createOrderItem(AcewillOrderInfo orderInfo, AcewillOrderNormalitem item,
//                                             Integer item_serial, JSONObject itemComboDetail) {
//        AcewillOrderItem orderItem = new AcewillOrderItem();
//        // 辅助Assist
//        if(itemComboDetail!=null) {
//            orderItem.setAssist_item_id(itemComboDetail.getString("id"));
//            orderItem.setAssist_num(itemComboDetail.getString("combo_num"));
//        }
//        // orderItem.setAssist_money(assist_money);
//        // 餐谱明细id orderItem.setDetails_id(details_id);
//
//        orderItem.setItem_count(item.getNumber());
//
//        orderItem.setItem_id(item.getDid());
//        orderItem.setItem_name(item.getName());
//        orderItem.setItem_num(item.getDishsno());
//        orderItem.setItem_price(item.getPrice());
//        orderItem.setItem_property(SysDictionary.ITEM_PROPERTY_SINGLE);
//
//        //orderItem.setItem_remark(item.getRemark());
//        // 点菜序号
//        orderItem.setItem_serial(item_serial);
//        orderItem.setItem_taste(item.getRemark());
//        // 规格名称
//        orderItem.setItem_unit_name(item.getDuName());
//
//        // orderItem.setSale_mode(sale_mode);
//
//        orderItem.setMethod(createItemMethod(item.getCooks()));
//        // 座位号
//        orderItem.setSeat_num(null);
//        orderItem.setSetmeal_id(null);
//        // 套菜点菜号
//        orderItem.setSetmeal_rwid(null);
//        // 规格id
//        orderItem.setUnit_id(item.getDuid());
//        // 等叫标记
//        // orderItem.setWaitcall_tag(waitcall_tag);
//        return orderItem;
//    }
    
    /** 套餐明细
     * @param setmeal
     * @param item
     * @param item_serial
     * @param itemComboDetail
     * @return
     */
	private AcewillOrderItem createOrderItem(AcewillOrderSetmeal setmeal, AcewillOrderSetmealItem item, Integer item_serial, JSONObject itemComboDetail) {
        AcewillOrderItem orderItem = new AcewillOrderItem();
        // 辅助Assist
        if(itemComboDetail!=null) {
            orderItem.setAssist_item_id(itemComboDetail.getString("id"));
            orderItem.setAssist_num(itemComboDetail.getString("combo_num"));
            orderItem.setOrder_number(itemComboDetail.optInt("combo_order"));
        }else {
            orderItem.setAssist_item_id("0");
            orderItem.setAssist_num("0");
        }
        // orderItem.setAssist_money(assist_money);
        // 餐谱明细id orderItem.setDetails_id(details_id);

        orderItem.setItem_count(item.getNumber());

        orderItem.setItem_id(item.getId());
        orderItem.setItem_name(item.getName());
        orderItem.setItem_num(item.getDishsno());
        //orderItem.setItem_price(item.getAprice());
        orderItem.setItem_property(SysDictionary.ITEM_PROPERTY_MEALLIST);
        //口味备注
        orderItem.setItem_taste(setmeal.getRemark());
        // 点菜序号
        orderItem.setItem_serial(item_serial);

        // orderItem.setItem_taste(null);
        // 规格名称
        orderItem.setItem_unit_name(item.getDuName());

        // 做法 orderItem.setMethod(method);

        // orderItem.setSale_mode(sale_mode);
        orderItem.setMethod(createItemMethod(item.getCooks()));
        // 座位号
        orderItem.setSeat_num(null);
        //套餐id
        orderItem.setSetmeal_id(setmeal.getDid());
        // 套菜点菜号
        orderItem.setSetmeal_rwid(item_serial);
        // 规格id
        orderItem.setUnit_id(item.getDuid());
        // 等叫标记
        // orderItem.setWaitcall_tag(waitcall_tag);
        return orderItem;
    }
	
	/** 套餐明细排序
	 * @param setmealItemList
	 * @return
	 * @throws Exception
	 */
	private List<AcewillOrderItem> sortOrderSetmealItem(List<AcewillOrderItem> setmealItemList) throws Exception
	{
		if (null == setmealItemList)
		{
			return null;
		}
		Collections.sort(setmealItemList);
		return setmealItemList;
	}

//    private AcewillOrderItem createOrderItem(AcewillOrderSetmeal item,
//                                             Integer item_serial, JSONObject itemComboDetail) {
//        AcewillOrderItem orderItem = new AcewillOrderItem();
//        // 辅助Assist
//        if(itemComboDetail!=null) {
//            orderItem.setAssist_item_id(itemComboDetail.getString("id"));
//            orderItem.setAssist_num(itemComboDetail.getString("combo_num"));
//        }
//        // orderItem.setAssist_money(assist_money);
//        // 餐谱明细id orderItem.setDetails_id(details_id);
//
//        orderItem.setItem_count(item.getNumber());
//
//        orderItem.setItem_id(item.getDid());
//        orderItem.setItem_name(item.getName());
//        orderItem.setItem_num(item.getDishsno());
//        orderItem.setItem_price(item.getPrice());
//        orderItem.setItem_property(SysDictionary.ITEM_PROPERTY_SINGLE);
//        orderItem.setItem_remark(null);
//        // 点菜序号
//        orderItem.setItem_serial(item_serial);
//        orderItem.setItem_taste(item.getRemark());
//        // 规格名称
//        orderItem.setItem_unit_name(item.getDuName());
//
//        // orderItem.setSale_mode(sale_mode);
//
//        orderItem.setMethod(createItemMethod(item.getCooks()));
//        // 座位号
//        orderItem.setSeat_num(null);
//        orderItem.setSetmeal_id(null);
//        // 套菜点菜号
//        orderItem.setSetmeal_rwid(null);
//        // 规格id
//        orderItem.setUnit_id(item.getDuid());
//        // 等叫标记
//        // orderItem.setWaitcall_tag(waitcall_tag);
//        return orderItem;
//    }
    private List<AcewillOrderItemMethod> createItemMethod(Object cooks)  {
        List<AcewillOrderItemMethod> methods = new ArrayList<>();
        if(null==cooks){
            return methods;
        }

        JSONArray cookArray = JSONArray.fromObject(cooks);
        if(cookArray!=null) {
            for (int i = 0; i < cookArray.size(); i++) {
                JSONObject jsonObject = cookArray.getJSONObject(i);
                AcewillOrderItemMethod method = new AcewillOrderItemMethod();
                String id = jsonObject.optString("id");
                String cookid = jsonObject.optString("cookid");
                if((StringUtils.isBlank(id)||"null".equals(id)) && (StringUtils.isBlank(cookid)||"null".equals(cookid))){
                    continue;
                }else if(StringUtils.isNotBlank(id) && !"null".equals(id)){
                    method.setMethod_id(jsonObject.getString("id"));
                    method.setMethod_name(jsonObject.getString("cook"));
                    methods.add(method);
                }else if(StringUtils.isNotBlank(cookid) && !"null".equals(cookid)){
                    method.setMethod_id(jsonObject.getString("cookid"));
                    method.setMethod_name(jsonObject.getString("cookname"));
                    methods.add(method);
                }
            }
        }
        return methods;
    }


    private Data createOpenTableData(MicroLifeOrder microLifeOrder, AcewillOrderAssist orderAssist) throws Exception {
        MicroLifeOrderInfo orderInfo = microLifeOrder.getOrder_info();
        Data data = new Data();
        data.setTenancy_id(microLifeOrder.getBusiness_id());
        data.setStore_id(microLifeOrder.getShop_id());
        data.setType(Type.ORDERING);
        data.setOper(Oper.add);
        List<JSONObject> list = new ArrayList<>();
        JSONObject object = new JSONObject();
        object.put("mode", 0);
        object.put("shift_id", orderAssist.getShift_id());
        object.put("report_date", orderAssist.getReport_date());
        object.put("table_code", orderInfo.getTableno());
        object.put("pos_num", orderAssist.getPos_num());
        object.put("opt_num", orderAssist.getOpt_num());
        object.put("waiter_num", orderAssist.getOpt_num());
        object.put("item_menu_id", 0);
        object.put("sale_mode", SysDictionary.SALE_MODE_TS01);
        object.put("chanel", SysDictionary.CHANEL_WX02);
        object.put("guest", orderInfo.getPeople());
        object.put("preorderno", microLifeOrder.getOut_order_id());
        object.put("copy_bill_num", "");
        object.put("remark", "");
        object.put("shop_real_amount", 0);
        object.put("platform_charge_amount", 0);
        object.put("settlement_type", "");
        object.put("discount_mode_id", 0);
        object.put("discountk_amount", 0);
        object.put("discount_rate", 0);
        list.add(object);
        data.setData(list);
        return data;
    }

    private AcewillOrderAssist createOrderAssist(JSONObject posBill) {
        AcewillOrderAssist orderAssist = new AcewillOrderAssist();
        orderAssist.setBill_num(posBill.getString("bill_num"));
        orderAssist.setOpt_num(posBill.getString("open_opt"));
        orderAssist.setPos_num(posBill.getString("open_pos_num"));
        orderAssist.setReport_date(posBill.getString("report_date"));
        orderAssist.setShift_id(posBill.getInt("shift_id"));
        return orderAssist;
    }
    private AcewillOrderAssist createOrderAssist(MicroLifeOrder microLifeOrder) throws Exception {
        AcewillOrderAssist orderAssist = new AcewillOrderAssist();
        String tenantId = microLifeOrder.getBusiness_id();
        Integer storeId = microLifeOrder.getShop_id();
        Date reportDate = insertOrderDao.getReportDate(tenantId, storeId);
        orderAssist.setReport_date(DateUtil.format(reportDate));
        List<JSONObject> optStateInfoList = posDishService.getOptStateInfo(tenantId, storeId,
                orderAssist.getReport_date());
        if (optStateInfoList == null || optStateInfoList.size() == 0) {
            throw new OtherSystemException(Constant.CODE_NULL_DATASET, Constant.CHECK_POS_LOGIN_FAILURE, null);
        }
        JSONObject optStateInfo = optStateInfoList.get(0);
        orderAssist.setPos_num(optStateInfo.getString("pos_num"));
        orderAssist.setOpt_num(optStateInfo.getString("opt_num"));
        int shiftId = insertOrderDao.getShiftId(tenantId, storeId, reportDate, orderAssist.getOpt_num(), orderAssist.getPos_num());
        orderAssist.setShift_id(shiftId);
        return orderAssist;
    }
    private Map<String,Class<?>> getInsertOrderClassMap() {
        Map<String, Class<?>> classMap = new HashMap<>();
        classMap.put("order_info", AcewillOrderInfo.class);
        classMap.put("ordermemo", AcewillOrderMemo.class);
        classMap.put("member", AcewillOrderMember.class);
        classMap.put("upgrade", MicroLifeGrade.class);
        classMap.put("setmeal", AcewillOrderSetmeal.class);
        classMap.put("normalitems", AcewillOrderNormalitem.class);
        classMap.put("maindish", AcewillOrderSetmealItem.class);
        classMap.put("mandatory", AcewillOrderSetmealItem.class);
        classMap.put("optional", AcewillOrderSetmealItem.class);
        return classMap;
    }
    private JsonConfig getJsonConfig() {
//        JsonConfig config = new JsonConfig();
//        config.setClassMap(getInsertOrderClassMap());
//        config.setRootClass(MicroLifeOrder.class);
//        config.setJavaPropertyFilter(new PropertyFilter() {
//            @Override
//            public boolean apply(Object source, String name, Object value) {
//                PropertyDescriptor propertyDescriptor = BeanUtils.getPropertyDescriptor(source.getClass(), name);
//                return propertyDescriptor==null;
//            }
//        });
        return WlifeProgramUtil.getJsonConfig(MicroLifeOrder.class, getInsertOrderClassMap());
    }

    @Override
    public void payUploadBill(JSONObject param, JSONObject responseJson) throws Exception {
        String tenancyId = param.optString("business_id");
        int storeId = param.optInt("shop_id");
        String tableCode = param.optString("tableno");
        if (StringUtils.isEmpty(tenancyId) || storeId == 0){
            throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
        }
        if (StringUtils.isEmpty(tableCode)){
            throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
        }
        
        String outOrderId = param.optString("out_order_id");
        JSONObject memberInfo = param.optJSONObject("member");
        String mobile = memberInfo.optString("mobile");
        if(Tools.isNullOrEmpty(mobile))
        {
        	mobile = memberInfo.optString("phone");
        }
        
        String openId = memberInfo.optString("openid");
        if(Tools.isNullOrEmpty(openId))
        {
        	openId = memberInfo.optString("unionid");
        }
        
       /* //先查询是否门店锁单了，是门店已锁单的话就不处理，不再上传订单
        JSONObject lockObj =tableStateDao.getTableOpen(tenancyId, storeId, tableCode);
        if (null != lockObj){
            String lockState = lockObj.optString("lock_state");
            String lockType = lockObj.optString("lock_type");
            if ("1".equals(lockState) && SysDictionary.CHANEL_MD01.equals(lockType))
            {
                JSONObject tableStatus = insertOrderDao.getTableStatus(tenancyId, storeId, tableCode);
                String lockOptNum = tableStatus.getString("lock_opt_num");
                String optName = posDao.getEmpNameById(lockOptNum, tenancyId, storeId);
                throw SystemException.getInstance(PosErrorCode.TABLE_ALREADY_LOCK_ERROR).set("{0}", optName).set("{1}", lockOptNum);
            }
        }*/
//        //判断是否是其他人锁单
//        // 查询桌台状态
//        JSONObject tableStatus = afterPaymentDao.getTableStatus(tenancyId, storeId, tableCode);
//        String state = tableStatus.optString("state");
//        String lockOptNum = tableStatus.optString("lock_opt_num");
//        
//        if(SysDictionary.TABLE_STATE_BUSY.equals(state)&& StringUtil.hasText(lockOptNum)){//锁定
//            if(null !=memberInfo){
//                if(StringUtil.hasText(mobile) && ! mobile.equals(lockOptNum)){
//                    String name = "";
//                    String lockPosNum = tableStatus.optString("lock_pos_num");
//                    if(LOCK_POS_NUM_WXWLIFE.equals(lockPosNum)){
//                        name = tableStatus.optString("opt_name");
//                    }else {
//                        name = posDao.getEmpNameById(lockOptNum, tenancyId, storeId);
//                    }
//                    throw SystemException.getInstance(PosErrorCode.TABLE_ALREADY_LOCK_ERROR).set("{0}", name).set("{1}", lockPosNum);
//                }
//            }
//        }else {
//            afterPaymentDao.lockTableStatus(storeId, tableCode, mobile, LOCK_POS_NUM_WXWLIFE, memberInfo.optString("name"));
//        }

        //查询账单pos_bill，判断账单有没折扣
        JSONObject billObject = preorderDao.getBillDiscount(tenancyId, storeId, tableCode);
        if(null == billObject ){
            LOG.error("门店为{}，桌号为:{}的账单不存在", storeId,tableCode);
            throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_BILL);
        }
        String billNum = billObject.optString("bill_num");
        String orderNum = billObject.optString("order_num");
        if(null != billNum && ! StringUtil.hasText(orderNum))
        {    
            afterPaymentDao.updatePosBillOrderNum(outOrderId,billNum);
        }

        //查询账单 pos是否合并账单
        JSONObject bill = posDishService.isCombineTableOfBillNum(tenancyId,storeId, billNum);
        if(null!=bill){ //存在并台
            tableCode= bill.optString("table_code");
        }

        //会员折扣处理
        List<JSONObject> holdObjs = holdBillDao.getBillLockOpenId(tenancyId,storeId,tableCode);
        if(StringUtil.hasText(openId)){
           if(null != holdObjs && holdObjs.size() >0 ){
               JSONObject holdObj = holdObjs.get(0);
               String holdOpenId = holdObj.optString("open_id");
               String source = billObject.optString("source");
               if(BILL_SOURCE.equals(source)){
                   setMemberDiscount(memberInfo, tenancyId, storeId, tableCode, billObject);
               }else if(!holdOpenId.equals(openId)){
                   setMemberDiscount(memberInfo, tenancyId, storeId, tableCode, billObject);
               }
           }else {
               setMemberDiscount(memberInfo, tenancyId, storeId, tableCode, billObject);
           }

        }else {
            setMemberDiscount(memberInfo, tenancyId, storeId, tableCode, billObject);
            holdBillService.saveOpenId(tenancyId, storeId, tableCode, openId);
        }


        JSONObject orderData = getOrderDetail(tenancyId, storeId, tableCode, WLifeConstant.OPT_TYPE_PAY);
        LOG.info(" getOrderDetail data:" + orderData.toString());
        buildSuccessData(orderData, responseJson, billObject.optString("bill_num"),false);

    }

	/** 使用会员折扣
	 * @param member
	 * @param tenancyId
	 * @param storeId
	 * @param tableCode
	 * @param billObject
	 * @throws Exception
	 */
	private void setMemberDiscount(JSONObject member, String tenancyId, int storeId, String tableCode, JSONObject billObject) throws Exception
	{
		String discountModeId = billObject.optString("discount_mode_id");
		Integer discountRate = billObject.optInt("discount_rate");
		if (!"0".equals(discountModeId) && !String.valueOf(SysDictionary.DISCOUNT_MODE_5).equals(discountModeId) && !String.valueOf(SysDictionary.DISCOUNT_MODE_6).equals(discountModeId))
		{
			// 如果账单有折扣，且不是会员折扣,以门店折扣为准,直接上传dingd
			LOG.info("账单有折扣，直接上传预结订单>>>>");
		}
		else
		{
			// 查询是否启用微生活会员
			String memberType = preorderDao.getMemberType(tenancyId);
			if (false == SysDictionary.CUSTOMER_TYPE_ACEWILL.equals(memberType))
			{
				LOG.info("未开启微生活会员，直接上传预结订单>>>>");
				return;
			}

			// 调用查询会员接口,如果有会员折扣或者会员价，调用账单折扣，否则直接上传
			Integer rate = 100;
			String is_vipprice = "0";
			if (null != member && member.containsKey("cno"))
			{
				JSONObject obj = new JSONObject();
				obj.put("card_code", member.optString("cno"));
				List<JSONObject> dataList = new ArrayList<>();
				dataList.add(obj);

				Data requestData = new Data();
				requestData.setStore_id(storeId);
				requestData.setTenancy_id(tenancyId);
				requestData.setData(dataList);

				Data customerUserInfo = acewillCustomerService.getAcewillCustomerUserInfo(requestData);
				LOG.info("查询会员返回信息：" + JSON.toJSONString(customerUserInfo));
				List<JSONObject> userList = (List<JSONObject>) customerUserInfo.getData();
				if (null != userList && userList.size() > 0)
				{
					member.putAll(userList.get(0));
					// 折扣率，0-100
					rate = userList.get(0).optInt("rate");
					// 是否会员价，0为否，1为是
					is_vipprice = userList.get(0).optString("is_vipprice");
				}
			}

			// 账单折扣是走会员折扣
			if ((rate > 0 && rate < 100) || (discountRate > 0 && discountRate < 100))
			{
				if (rate.intValue() < discountRate.intValue())
				{
					billDiscount(tenancyId, storeId, tableCode, rate,member);
				}
				else
				{
					LOG.info("账单当前折扣大于会员折扣，直接上传预结订单>>>>");
				}
			}
			else if (!String.valueOf(SysDictionary.DISCOUNT_MODE_6).equals(discountModeId) && StringUtil.hasText(is_vipprice) && "1".equals(is_vipprice))
			{
				// 账单折扣走会员价
				billDiscount(tenancyId, storeId, tableCode, null,member);
			}
			else
			{
				LOG.info("微生活会员既没折扣也不走会员价，直接上传预结订单>>>>");
			}
		}
	}

    private void billDiscount(String tenancyId, int storeId, String tableCode, Integer rate,JSONObject member) throws Exception {
        //调用账单折扣
    	JSONObject object = preorderDao.getBillDiscount(tenancyId, storeId, tableCode);
        if (null == object)
        {
            return ;
        }
        //0是取消折扣，1是账单折扣
        object.put("mode", 1);
        //操作员号
        object.put("opt_num", LOCK_POS_NUM_WXWLIFE);
        //批准人编号
        object.put("manager_num", LOCK_POS_NUM_WXWLIFE);
        //折让金额
        object.put("discountr_amount", 0);
        //折让方式
        if (null != rate){
            //会员折扣
            object.put("discount_mode", SysDictionary.DISCOUNT_MODE_5);
            object.put("discount_rate", rate);
        }else {
            //会员价
            object.put("discount_mode", SysDictionary.DISCOUNT_MODE_6);
        }
        
        object.put( "discount_reason_id", "0");
        
        object.put( "card_code", member.optString("card_code"));
		// 手机号
        object.put( "mobil", member.optString("mobil"));
		// 会员编号
        object.put( "customer_code", member.optString("card_code"));
		// 会员名称
        object.put( "customer_name", member.optString("name"));
		// 本次消费前积分
        object.put( "customer_credit", member.optDouble("credit"));
        object.put( "main_balance", member.optDouble("balance"));
        object.put( "reward_balance", 0d);
        
        List<JSONObject> list = new ArrayList<>();
        list.add(object);
        
//    	List<JSONObject> list = getDiscountParam(tenancyId, storeId, tableCode, rate);
		if (null != list && 0 < list.size())
		{
			Data data = new Data();
			data.setType(Type.BILL_DISCOUNT);
			data.setOper(Oper.add);
			data.setTenancy_id(tenancyId);
			data.setStore_id(storeId);
			data.setData(list);
			posDiscountService.newBillDiscount(data);
		}
    }

    //查询会员折扣需要的参数
    @Deprecated
    private List<JSONObject> getDiscountParam(String tenancyId, int storeId, String tableCode, Integer rate) throws Exception {
        
        JSONObject object = preorderDao.getBillDiscount(tenancyId, storeId, tableCode);
        if (null == object)
            return null;
        //0是取消折扣，1是账单折扣
        object.put("mode", 1);
        //操作员号
        object.put("opt_num", "");
        //批准人编号
        object.put("manager_num", "");
        //折让金额
        object.put("discountr_amount", 0);
        //折让方式
        if (null != rate){
            //会员折扣
            object.put("discount_mode", SysDictionary.DISCOUNT_MODE_5);
            object.put("discount_rate", rate);
        }else {
            //会员价
            object.put("discount_mode", SysDictionary.DISCOUNT_MODE_6);
        }
        
        List<JSONObject> list = new ArrayList<>();
        list.add(object);
        return list;
    }

    @Override
    public void billPayment(JSONObject param, JSONObject printJson, JSONObject responseJson) throws Exception {
        String tenancyId = param.optString("business_id");
        int storeId = param.optInt("shop_id");
        String orderNum = param.optString("order_id"); // 平台订单号
        String tableCode = param.optString("tablesno"); // 桌台号
        String isPrintBill = param.optString("isprint_bill", "Y"); // 是否打印结账单

        if (StringUtils.isEmpty(tenancyId) || storeId == 0
                || !param.containsKey("pay_info")
                || param.getJSONArray("pay_info").isEmpty()
                || StringUtils.isEmpty(tableCode)){
            throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
        }

        List<JSONObject> billList = afterPaymentDao.getBillInfoByOrderNum(tenancyId, storeId, orderNum);
        if (billList.size() == 0){
            throw new OtherSystemException(Constant.CODE_NULL_DATASET, Constant.PAYMENT_ALIPAY_QUERY, null);
        }

        JSONObject billInfo = billList.get(0);
        if (!tableCode.equals(billInfo.optString("table_code"))){
            throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_TABLE_CODE);
        }


        List<JSONObject> payItemList = new LinkedList<>();

        JSONObject paymentJson = new JSONObject();
        paymentJson.put("report_date", billInfo.optString("report_date"));
        paymentJson.put("pos_num", billInfo.optString("pos_num"));
        paymentJson.put("shift_id", billInfo.optString("shift_id"));
        paymentJson.put("opt_num", billInfo.optString("opt_num"));
        paymentJson.put("chanel", billInfo.optString("chanel"));
        paymentJson.put("isprint_bill", isPrintBill);
        paymentJson.put("bill_num", billInfo.optString("bill_num"));
        paymentJson.put("payment_amount", billInfo.optString("payment_amount"));
        paymentJson.put("table_code", billInfo.optString("table_code"));
        paymentJson.put("difference", billInfo.optString("difference"));
//        paymentJson.put("is_invoice", );

        // 会员相关
        JSONObject payMember = param.getJSONObject("pay_member"); // 支付前会员信息
        if (!payMember.isEmpty()){
//            paymentJson.put("mobil", );
//            paymentJson.put("customer_code", );
//            paymentJson.put("customer_name", );
//            paymentJson.put("reward_balance", );
            paymentJson.put("card_code", payMember.optString("cno")); // 卡号
            Double credit = payMember.optDouble("credit");
            if (credit.isNaN()){
                credit = 0.0d;
            }
            paymentJson.put("customer_credit", credit); // 积分
            Double mainBalance = payMember.optDouble("balance");
            if (mainBalance.isNaN()){
                mainBalance = 0.0d;
            }
            paymentJson.put("main_balance", mainBalance); // 储值金额
        }

        paymentJson.put("item", payItemList);

        List<JSONObject> paymentList = new ArrayList<>();
        paymentList.add(paymentJson);

        Data paymentData = Data.get();
        paymentData.setTenancy_id(tenancyId);
        paymentData.setStore_id(storeId);
        paymentData.setType(Type.BILL_PAYMENT);
        paymentData.setData(paymentList);

        Data result = Data.get();

        posPaymentService.posBillPayment(paymentData, result, printJson, false);
        if (0 != result.getCode()){
            throw new OtherSystemException(result.getCode(), result.getMsg(), null);
        }

    }

    @Override
    public JSONObject getOrderDetail(String tenancyId, int storeId, String tableCode, String opType) throws Exception {
        Map<String, String> uploadParamMap = getOrderInfoMap(tenancyId, storeId, tableCode, opType);
        if (uploadParamMap == null) return null;
        return JSONObject.fromObject(uploadParamMap);
    }

    private Map<String, String> getOrderInfoMap(String tenancyId, int storeId, String tableCode, String opType) throws Exception {
        //查询订单信息
        Map<String, String> uploadParamMap = new HashMap<>();

        JSONObject billObject = afterPaymentDao.getBillInfo(tenancyId, tableCode, storeId);
        if(null ==billObject){
            LOG.error("getOrderInfoMap is null tenancyId="+tenancyId+",storeId="+storeId+",tableCode="+tableCode+",opType"+opType);
            return uploadParamMap;
        }

        JSONObject orderObject = getOrder(tenancyId, tableCode, storeId,billObject);
        String businessId =null;
        String brandId =null;
        String shopId =null;
        List<JSONObject> paramList = afterPaymentDao.getParam(tenancyId);
        if (null != paramList && paramList.size() > 0) {
            for (int i = 0; i < paramList.size(); i++) {
                String code = paramList.get(i).optString("para_code");
                if (WLifeConstant.BUSSINESS_ID.equals(code)) {
                    businessId = paramList.get(i).optString("para_value");
                } else if (WLifeConstant.BRAND_ID.equals(code)) {
                    brandId = paramList.get(i).optString("para_value");
                } else if (WLifeConstant.SHOP_ID.equals(code)) {
                    shopId = paramList.get(i).optString("para_value");
                }
            }
        }

        if (Tools.isNullOrEmpty(businessId) || Tools.isNullOrEmpty(brandId) || Tools.isNullOrEmpty(shopId)) {
            LOG.error("wLife System Params is null");
            return null;
        }

       String openid =null;

        if (null != orderObject) {
            orderObject.remove("id");
            //
            orderObject.put("business_id", businessId);
            orderObject.put("brand_id", brandId);
            orderObject.put("shop_id", shopId);
            JSONObject member = afterPaymentDao.getWLifeMember(tenancyId, tableCode, storeId);
            if (null != member){
                orderObject.put("name", member.optString("name"));
                orderObject.put("mobile", member.optString("mobile"));
                orderObject.put("balance", member.optString("balance"));
                orderObject.put("credit", member.optString("credit"));
                orderObject.put("openid", member.optString("openid"));
                JSONObject grade = new JSONObject();
                grade.put("cardnum", member.optString("cno"));
                grade.put("orginlevel", "");
                orderObject.put("upgrade", grade);
                openid = member.optString("openid");
            }

            uploadParamMap.put("business_id", businessId);
            uploadParamMap.put("brand_id", brandId);
            uploadParamMap.put("shop_id", shopId);
            uploadParamMap.put("table_sno", tableCode);
            uploadParamMap.put("order_info", orderObject.toString());
            uploadParamMap.put("optype", opType);
            uploadParamMap.put("is_locked", billObject.optString("is_locked"));
            uploadParamMap.put("oid", billObject.optString("oid"));
            uploadParamMap.put("identify", billObject.optString("order_id"));
            uploadParamMap.put("out_order_id", billObject.optString("order_id"));
            uploadParamMap.put("shop_name", billObject.optString("shop_name"));
            // 订单模式 3是后付
            uploadParamMap.put("ordermode", WLifeConstant.WLIFE_ORDER_MODE);
            // 是否预结订单
            if (WLifeConstant.OPT_TYPE_PAY.equals(opType))
                uploadParamMap.put("is_pre_checkout", "1");
            else
                uploadParamMap.put("is_pre_checkout", "2");

            //查询最新的openid,查询的是锁单表的openid
            List<JSONObject> holdBillList = holdBillDao.getBillLockOpenId(tenancyId, storeId, tableCode);
            if (null != holdBillList && holdBillList.size() > 0)
                openid = holdBillList.get(0).optString("open_id");
            uploadParamMap.put("openid", openid);

        }
        return uploadParamMap;
    }
    private JSONObject getOrder(String tenancyId, String tableCode, int storeId, JSONObject billObject) throws Exception {
        //订单信息
        JSONObject orderObject = new JSONObject();

        //设置订单的优惠信息
//        setBillDiscount(billObject, orderObject);
        if (null != billObject){

            JSONObject ordermemo = new JSONObject();
            ordermemo.put("id", null);
            ordermemo.put("text", billObject.optString("ordermemo"));
            orderObject.put("ordermemo", ordermemo);

            //升级   gradeamount 升级所需金额
//            orderObject.put("is_locked", billObject.optInt("is_locked"));
            orderObject.put("total", billObject.optDouble("total"));
            orderObject.put("cost", billObject.optDouble("cost"));
//            orderObject.put("discountable", billObject.optDouble("discountable"));
//            orderObject.put("membercouponsprice", billObject.optDouble("membercouponsprice"));
            orderObject.put("people", billObject.optString("people"));
            orderObject.put("mealfee", billObject.optDouble("mealfee"));
            orderObject.put("oid", billObject.optString("oid"));
            orderObject.put("out_order_id", billObject.optString("order_id"));

        }
        orderObject.put("tableno", tableCode);
        //====
        int discountModeId = billObject.optInt("discount_mode_id");
        if(discountModeId== SysDictionary.DISCOUNT_MODE_6){
            orderObject.put("memberPrice", billObject.optDouble("discountk_amount"));
        }else {
            orderObject.put("memberPrice",0);
        }
        orderObject.put("discount_money", billObject.optDouble("discount_amount"));


        orderObject.put("weiXinPay", 0);

        orderObject.put("discount_info", new Object());

        //会员信息
        //设置订单的会员信息
        JSONObject member = afterPaymentDao.getWLifeMember(tenancyId, tableCode, storeId);
        if (!Tools.isNullOrEmpty(member)){
            orderObject.put("openid", member.optString("openid"));
            orderObject.put("name", member.optString("name"));
            orderObject.put("mobile", member.optString("mobile"));
            orderObject.put("credit", member.optString("credit"));
            orderObject.put("balance", member.optString("balance"));
            //会员等级
            JSONObject upgrade = new JSONObject();
            upgrade.put("cardnum", member.optString("cno"));
            upgrade.put("orginlevel", member.optString("grade"));
            orderObject.put("upgrade", upgrade);
        }else {
            orderObject.put("openid", "");
            orderObject.put("name", "");
            orderObject.put("mobile", "");
            orderObject.put("credit", 0);
            orderObject.put("balance", 0);
            orderObject.put("upgrade", new JSONObject());
        }

//        orderObject.put("ordermode", WLifeConstant.WLIFE_ORDER_MODE);
        //设置代金券是否可用
//        orderObject.put("djqflag", true);
//        orderObject.put("djqRules", "");

        //设置订单中的菜品信息
        setDish(tenancyId, orderObject);
        return orderObject;
    }

    private void setDish(String tenancyId, JSONObject orderObject) throws Exception {
        //桌台的未结账的账单号
        String billNum = orderObject.optString("oid");
        //查询套餐菜品信息(可能一笔订单，多个套餐)
        List<JSONObject> setmealList = afterPaymentDao.getSetmeal(tenancyId, billNum, SysDictionary.ITEM_PROPERTY_SETMEAL);


        if(null == setmealList)
        {
            setmealList = new ArrayList<>();
        }
        //查询套餐中的主菜和必选菜
        if (setmealList.size() > 0){
            for (JSONObject aSetmealList : setmealList) {
                //套餐id
                //String setmealId = setmealList.get(i).optString("did");
                //设置菜品可用券的情况
//                setmealList.get(i).put("cpqflag", true);
//                //菜品券互斥规则提示语
//                setmealList.get(i).put("cpqRules", "");
//                //菜品券抵扣数量
//                setmealList.get(i).put("cpqhasbeen", 0);
//                //抵扣菜品券面值
//                setmealList.get(i).put("cpqDeno", "");

                aSetmealList.put("bbuysno", "");
                aSetmealList.put("bgiftsno", "");
                //可使用会员价的会员等级
                String [] membergid = (String[]) com.tzx.clientorder.wlifeprogram.common.constant.Constant.membergidMap.get(com.tzx.clientorder.wlifeprogram.common.constant.Constant.MEMBERGID);
                aSetmealList.put("membergid", membergid);
//                setmealList.get(i).put("isWeigh", setmealList.get(i).optInt("is_weigh"));
                //查询菜品做饭
                List<JSONObject> cookList = afterPaymentDao.getCooks(tenancyId, aSetmealList.optInt("id"));
                if(null != cookList && cookList.size()>0){
                    aSetmealList.put("cooks", cookList);
                }

                //查询套餐里的主菜信息
                //主菜里的数量，要除以套餐的数量。
                int stemealCount = aSetmealList.optInt("number", 1);
                //套餐id
                String setmealId = aSetmealList.optString("did");
                String setmeal_rwid = aSetmealList.optString("setmeal_rwid");
                List<JSONObject> mainList = afterPaymentDao.getSetmealDish(tenancyId, billNum, "main", stemealCount, setmealId, setmeal_rwid);
                if (null == mainList) {
                    mainList = new ArrayList<>();
                }
                //查询套餐中的必选菜品
                List<JSONObject> mandatoryList = afterPaymentDao.getSetmealDish(tenancyId, billNum, "mandatory", stemealCount, setmealId, setmeal_rwid);
                if (null == mandatoryList) {
                    mandatoryList = new ArrayList<>();
                }
                aSetmealList.put("maindish", mainList);
                aSetmealList.put("mandatory", mandatoryList);
                aSetmealList.put("optional", new JSONArray());

                aSetmealList.put("centPrice", aSetmealList.optDouble("price",0) * 100);
                aSetmealList.put("isWeigh", aSetmealList.optString("isweigh"));
                aSetmealList.remove("isweigh");
            }
            //设置订单中的套餐信息
            orderObject.put("setmeal", setmealList);
        }
        //设置订单中的套餐信息
//        orderObject.put("setmeal", setmealList);
        //非套餐菜品的查询
        List<JSONObject> normalList = afterPaymentDao.getSetmeal(tenancyId, billNum, SysDictionary.ITEM_PROPERTY_SINGLE);
        if (null != normalList && normalList.size() > 0){
            for (JSONObject aNormalList : normalList) {
                aNormalList.put("bbuySno", "");
                aNormalList.put("bgiftSno", "");
                //可使用会员价的会员等级
                String [] membergid = (String[]) com.tzx.clientorder.wlifeprogram.common.constant.Constant.membergidMap.get(com.tzx.clientorder.wlifeprogram.common.constant.Constant.MEMBERGID);
                aNormalList.put("membergid", membergid);
                aNormalList.put("cpqflag", true);
                aNormalList.put("type", "1");
                //查询菜品做饭
                List<JSONObject> cookList = afterPaymentDao.getCooks(tenancyId, aNormalList.optInt("id"));
                if(null != cookList && cookList.size()>0){
                    aNormalList.put("cooks", cookList);
                }

                aNormalList.put("centPrice", aNormalList.optDouble("price",0) * 100);
                aNormalList.put("isWeigh", aNormalList.optString("isweigh"));
                aNormalList.remove("isweigh");

            }
        }
        if(null!=normalList)
        {
            orderObject.put("normalitems", normalList);
        }

    }

//    private  void  setItemCooks(JSONObject item,List<JSONObject> cooks){
//        List<String> cookIds = new ArrayList<>();
//        if(null != cooks){
//            for (JSONObject cook:cooks) {
//                cookIds.add(cook.optString("cid"));
//            }
//        }
//        item.put("cooks", cookIds);
//    }

    @Override
    public void paymentClose(JSONObject param, JSONObject responseJson) throws Exception {

        MicroLifePayOrder microLifePayOrder = (MicroLifePayOrder) JSONObject.toBean(param, getPayJsonConfig());

        //订单信息
        MicroLifePayOrderData payOrderData = microLifePayOrder.getData();
        String reqId=payOrderData.getOid();
        //会员信息
        AcewillPayWLife wlife = microLifePayOrder.getWlife();
        wlife.setBalance(DoubleHelper.div(wlife.getBalance(), 100d, DEFAULT_SCALE));
        //付款明细
        List<AcewillPayInfo> payInfoList = microLifePayOrder.getPay_info();

        String tenancyId = microLifePayOrder.getBusiness_id();
        Integer storeId =microLifePayOrder.getShop_id();
        String msg = null;
        if (null == payInfoList||payInfoList.isEmpty()) {
        	msg = "pay_info is null";
        }else {
           String serilNo=payInfoList.get(0).getSerilNo();
            if(IdempotentUtil.isDuplicate(serilNo,AfterPaymentServiceImpl.class)){
                LOG.info(serilNo+"=============重复请求，不处理...");
                return;
            }

        }


        if(StringUtils.isBlank(payOrderData.getOid())){
        	if(StringUtils.isBlank(payOrderData.getOrder_id())){
        		msg = "oid is null";
        	}else {
        		//查询oid
        		String oid = afterPaymentDao.getBillNumByOrderId(payOrderData.getOrder_id());
        		if(null == oid){
        			throw SystemException.getInstance(PosErrorCode.NOT_NULL_BILL_NUM);
        		}
        		payOrderData.setOid(oid);
        	}
        }
        if(null!=msg){
        	throw SystemException.getInstance(msg, PosErrorCode.PARAM_ERROR);
        }
        //LOGGER.info("pay.success:单号："+microLifePayOrder.getData().getOid()+"=======================================================收到清台消息");

        synchronized (microLifePayOrder.getData().getOid().intern())
        {
        	//查询账单 pos是否合并账单
        	JSONObject bill = posDishService.isCombineTableOfBillNum(tenancyId,storeId, payOrderData.getOid());
        	String sbill_num = null;
        	if(null!=bill){ //存在并台
        		String bill_num = bill.optString("bill_num");
        		sbill_num= bill.optString("sbill_num");
        		payOrderData.setOid(bill_num);
        	}

        	JSONObject posBillJson = paySucessDao.getPosBillByBillNum(tenancyId, payOrderData.getOid());
        	if (posBillJson == null) {
        		// 账单不存在
        		LOG.error("账单号为:{}的账单不存在", payOrderData.getOid());
        		throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_BILL);
        	}
        	AcewillPosBill posBill = (AcewillPosBill) JSONObject.toBean(posBillJson, AcewillPosBill.class);

        	//查询签到班次,获取操作机台
        	List<JSONObject> optStateInfoList = posPaymentService.getOptStateInfo(tenancyId, storeId,posBill.getReport_date());
        	if (optStateInfoList.size() > 0) {
        		JSONObject optStateInfo = optStateInfoList.get(0);
        		posBill.setPos_num(optStateInfo.getString("pos_num"));
        		posBill.setCashier_num(optStateInfo.getString("opt_num"));
        		posBill.setShift_id(optStateInfo.optInt("shift_id",0));
        	}else 
        	{
        		//没有找到签到,取开台人和开台机台
        		posBill.setPos_num(posBill.getOpen_pos_num());
        		posBill.setCashier_num(posBill.getOpen_opt());
        	}

        	// 判断账单状态
        	String billProperty = posBill.getBill_property();
        	if (!SysDictionary.BILL_PROPERTY_OPEN.equals(billProperty)) {
        		LOG.error("账单号为:{}的账单已关闭", payOrderData.getOid());
        		throw SystemException.getInstance("账单已关闭", PosErrorCode.BILL_CLOSED);
        	}
        	String paymentState = posBill.getPayment_state();
        	if (!SysDictionary.PAYMENT_STATE_NOTPAY.equals(paymentState)) {
        		LOG.error("账单号为:{}的账单处于支付中", payOrderData.getOid());
        		throw SystemException.getInstance("账单支付中", PosErrorCode.BILL_PAYMENT_STATE_PAYING_ERROR);
        	}
        	// 判断账单金额
        	Double paymentAmount = posBill.getPayment_amount();
        	payOrderData.setAmount(paymentAmount);

//        	JSONObject memberInfo = afterPaymentDao.getBillMemberInfo(posBill.getBill_num(), SysDictionary.BILL_MEMBERCARD_JFZS06, wlife.getCno());
//        	if (null != memberInfo)
//        	{
//        		if (StringUtil.hasText(memberInfo.optString("mobil")))
//        		{
//        			wlife.setMobil(memberInfo.optString("mobil"));
//        		}
//        	}

        	//获取付款方式
        	Map<String, AcewillPaymentWay> paymentWayMap = getPaymentWayMap(tenancyId, storeId, payInfoList);

        	List<AcewillPosBillPayment> posBillPaymentList = new ArrayList<>();
        	List<AcewillPosBillMember> posBillMemberList = new ArrayList<>();
        	List<CrmCardTradingListEntity> crmCardTradingListEntityList = new ArrayList<>();
        	List<AcewillPosBillPaymentCoupons> posBillPaymentCouponsList = new ArrayList<>();
        	Double sumPaymentAmount = 0d;
        	Double consumeAmount = 0.0;//储值消费金额
        	boolean isInclueBalance = false;//是否有储值消费
        	for (AcewillPayInfo payInfo : payInfoList) {
        		sumPaymentAmount = DoubleHelper.add(sumPaymentAmount, payInfo.getAmount(), DEFAULT_SCALE);
        		// 支付来源：微生活 不生成PosBillPayment
        		if (!WLifeConstant.PAY_SOURCE_WLIFE.equals(payInfo.getSource())) {
        			AcewillPaymentWay paymentWay = getPaymentWay(paymentWayMap, payInfo);
        			if (paymentWay != null) {
        				if(WLifeConstant.PAY_SOURCE_WEIXIN.equals(payInfo.getSource())){
        					Double amount = payInfo.getAmount();
        					if(null !=amount && 0==amount){
        						continue;
        					}
        				}
        				AcewillPosBillPayment createPosBillPayment = createPosBillPayment(tenancyId,storeId,wlife, posBill, paymentWay,
        						payInfo);
        				posBillPaymentList.add(createPosBillPayment);

        				if (WLifeConstant.PAY_SOURCE_COUPON.equals(payInfo.getSource())
        						|| WLifeConstant.PAY_SOURCE_PRODUCT.equals(payInfo.getSource())) {

        					String paymentUid = Md5Utils.md5(JSONObject.fromObject(createPosBillPayment).toString());
        					createPosBillPayment.setPayment_uid(paymentUid);
        					
							if (null != microLifePayOrder.getDeno_gift_couponMoney() && 0 < microLifePayOrder.getDeno_gift_couponMoney().size())
							{
								posBillPaymentCouponsList.addAll(this.createPosBillPaymentCoupons(tenancyId, storeId, paymentUid, posBill, payInfo, microLifePayOrder.getDeno_gift_couponMoney()));
							}
							else
							{
								AcewillPosBillPaymentCoupons posBillPaymentCoupons = this.createPosBillPaymentCoupons(tenancyId, storeId, posBill, payInfo);
								posBillPaymentCoupons.setPayment_id(paymentUid);
								posBillPaymentCouponsList.add(posBillPaymentCoupons);
							}
        				}
        			}
        		}
        		// 如果有微生活会员信息
        		if (wlife != null) {
        			// 判断微生活会员是否启用
        			String sysParameter = CacheTableUtil.getSysParameter("CustomerType");
        			if (sysParameter != null) {

        				//会员操作记录
        				AcewillPosBillMember createPosBillMember = createPosBillMember(tenancyId,storeId,wlife, posBill, payInfo);
        				posBillMemberList.add(createPosBillMember);

        				//会员消费记录
        				if (WLifeConstant.PAY_SOURCE_BALANCE.equals(payInfo.getSource())) {
        					if(payInfo.getAmount()!= null && payInfo.getAmount() > 0){
        						CrmCardTradingListEntity crmCardTradingList = createCrmCardTradingList(tenancyId, storeId, wlife, posBill, payInfo);
        						crmCardTradingListEntityList.add(crmCardTradingList);
        						isInclueBalance = true;
        						consumeAmount = payInfo.getAmount();
        					}
        				}
        			}
        		}
        	}

        	if (DoubleHelper.sub(paymentAmount, sumPaymentAmount, DEFAULT_SCALE) <= 0) {

        		if (null != posBillMemberList && 0 < posBillMemberList.size())
        		{
        			afterPaymentDao.deletePosBillMember(tenancyId, storeId, posBill.getBill_num(), SysDictionary.BILL_MEMBERCARD_JFZS06);
        		}

        		paySucessDao.savePosBillPayment(posBillPaymentList);
        		paySucessDao.savePosBillMember(posBillMemberList);
        		paySucessDao.saveCrmCardTradingList(crmCardTradingListEntityList);
        		paySucessDao.savePosBillPaymentCouponsList(posBillPaymentCouponsList);
        		afterPaymentDao.updatePosBillSource(posBill.getBill_num(),SysDictionary.CHANEL_WSP17,false);

        		JSONObject printJson = new JSONObject();
        		JSONObject resultJson = new JSONObject();
        		String isInvoice = StringUtils.isEmpty(payOrderData.getInvoice()) ? "0" : "1";
        		posPaymentService.closedAcewillPosBill(tenancyId, storeId, posBill.getBill_num(),
        				posBill.getReport_date(), posBill.getShift_id(), posBill.getPos_num(),
        				posBill.getCashier_num(), "Y", isInvoice, resultJson, printJson, "1", com.tzx.pos.base.util.DateUtil.currentTimestamp());

        		//查询账单 pos是否合并账单
        		if(null!=sbill_num && !"".equals(sbill_num)){
        			LOG.info("pos并台账单零结账  "+sbill_num);
        			posPaymentService.combineTableZeroPayment(tenancyId, storeId,sbill_num,DateUtil.parseDate(posBill.getReport_date()), posBill.getShift_id(), posBill.getPos_num(),posBill.getCashier_num(), com.tzx.pos.base.util.DateUtil.currentTimestamp());
        		}

        		LOG.info("pay.success:单号："+posBill.getBill_num()+"=======================================================门店清台成功");
        		// 打印
        		try {
        			posPrintService.printPosBillForPayment(printJson, posPrintNewService);
        			LOG.info("pay.success:单号："+posBill.getBill_num()+"=======================================================门店清台打印完成");
        			if(isInclueBalance){
        				LOG.info("=========================isInclueBalance True===============");
        				printMemberBalance(tenancyId, storeId, wlife, posBill, consumeAmount);
        			}

        		}catch (Exception e) {
        			LOG.error("print failed... bill_number:"+posBill.getBill_num());
        		}
        		buildSuccessData(null, responseJson,null,false);
        	}else {
        		throw SystemException.getInstance(PosErrorCode.BILL_DIFFERENCE_MORE_ZERO_ERROR);
        	}
        }
    }


    private void printMemberBalance(String tenancyId,Integer storeId,AcewillPayWLife wlife, AcewillPosBill posBill, Double consumeAmount) throws Exception {
        //balance print
//        AcewillPayWLife wlife = microLifePayOrder.getWlife();
        JSONObject printMemberBlance = new JSONObject();

        String cardCode = wlife.getCno();
        if(StringUtil.hasText(cardCode)){
            JSONObject memberInfo = afterPaymentDao.getBillMemberInfo(posBill.getBill_num(), SysDictionary.BILL_MEMBERCARD_JFZS06, cardCode);
            if(null != memberInfo){
                if(StringUtil.hasText(memberInfo.optString("mobil"))){
                    printMemberBlance.put("mobil",memberInfo.optString("mobil"));
                }else {
                    printMemberBlance.put("mobil","");
                }
                printMemberBlance.put("useful_credit", memberInfo.optDouble("consume_before_credit"));
            }else {
                printMemberBlance.put("mobil", "");
                printMemberBlance.put("useful_credit",0);
            }

            printMemberBlance.put("isprint", "Y");
            printMemberBlance.put("pos_num", posBill.getPos_num());
            printMemberBlance.put("print_time", DateUtil.format(DateUtil.currentTimestamp()));
            printMemberBlance.put("bill_code", posBill.getBill_num());
            printMemberBlance.put("card_code", cardCode);
            printMemberBlance.put("level_name", wlife.getGrade_name());
            printMemberBlance.put("card_class_name", "");
            printMemberBlance.put("name", wlife.getName());

            printMemberBlance.put("consume_cardmoney", consumeAmount);
            printMemberBlance.put("main_balance", wlife.getBalance());
            printMemberBlance.put("reward_balance", 0);

            String operator = afterPaymentDao.getEmpNameById(posBill.getOpen_opt(), tenancyId, storeId);
            printMemberBlance.put("operator", operator);
            printMemberBlance.put("updatetime", DateUtil.format(DateUtil.currentTimestamp()));
            // 会员卡打印
            LOG.info(" print member balance start pararms ="+printMemberBlance.toString());
            customerService.customerCardConsumePrint(tenancyId, storeId, posBill.getPos_num(), printMemberBlance,
                    SysDictionary.PRINT_CODE_1010);
            LOG.info(" print member balance end tenancyId ="+tenancyId+",storeId="+storeId+",posNum="+posBill.getPos_num());
        }
    }

    private JsonConfig getPayJsonConfig() {
//        JsonConfig config = new JsonConfig();
//        config.setClassMap(getPayMessageClassMap());
//        config.setRootClass(MicroLifePayOrder.class);
//        config.setJavaPropertyFilter(new PropertyFilter() {
//            @Override
//            public boolean apply(Object source, String name, Object value) {
//                PropertyDescriptor propertyDescriptor = BeanUtils.getPropertyDescriptor(source.getClass(), name);
//                return propertyDescriptor == null;
//            }
//        });
        return WlifeProgramUtil.getJsonConfig(MicroLifePayOrder.class, getPayMessageClassMap());
    }

    private Map<String, Class<?>> getPayMessageClassMap() {
        Map<String, Class<?>> classMap = new HashMap<>();
        classMap.put("pay_info", AcewillPayInfo.class);
        classMap.put("wlife", AcewillPayWLife.class);
        classMap.put("receive_coupons", MicroLifeCoupons.class);
        classMap.put("deno_gift_couponMoney", WlifePaymentCoupons.class);
        classMap.put("dishItem", WlifePaymentCouponsItem.class);
        classMap.put("data", MicroLifePayOrderData.class);
        return classMap;
    }
    

    private Map<String, AcewillPaymentWay> getPaymentWayMap(String tenantId, Integer storeId,
    		List<AcewillPayInfo> payInfoList) throws Exception {
        List<String> paymentClassList = new ArrayList<>();
        for (AcewillPayInfo payInfo : payInfoList) {
            String paymentClass = paymentWayMapping.get(payInfo.getSource());
            if (!StringUtils.isEmpty(paymentClass)) {
                paymentClassList.add(paymentClass);
            }
        }
        List<JSONObject> paymentWayList = paySucessDao.findPaymentWay(tenantId, storeId, paymentClassList);
        Map<String, AcewillPaymentWay> paymentWayMap = new HashMap<>();
        if (paymentWayList != null) {
            for (JSONObject jsonObject : paymentWayList) {
                AcewillPaymentWay bean =  JsonUtil.jsonToBean(jsonObject, AcewillPaymentWay.class);
                paymentWayMap.put(bean.getPayment_class(), bean);
            }
        }
        return paymentWayMap;
    }

    private AcewillPaymentWay getPaymentWay(Map<String, AcewillPaymentWay> paymentWayMap, AcewillPayInfo payInfo)
            throws Exception {
        String paymentClass = paymentWayMapping.get(payInfo.getSource());
        if (paymentClass == null) {
            LOG.error("未知的支付方式：{}", payInfo.getSource());
            return null;
        }
        AcewillPaymentWay paymentWay = paymentWayMap.get(paymentClass);
        if (paymentWay == null) {
            LOG.error("支付方式{}未启用", payInfo.getSource());
            return null;
        }
        return paymentWay;
    }


    private AcewillPosBillPayment createPosBillPayment(String tenancyId,Integer storeId, AcewillPayWLife wlife, AcewillPosBill posBill,
                                                       AcewillPaymentWay paymentWay, AcewillPayInfo payInfo) throws Exception {

//        AcewillPayWLife wlife = microLifePayOrder.getWlife();

        AcewillPosBillPayment posBillPayment = new AcewillPosBillPayment();
        posBillPayment.setTenancy_id(tenancyId);
        posBillPayment.setStore_id(storeId);
        posBillPayment.setId(null);
        posBillPayment.setBill_num(posBill.getBill_num());
        posBillPayment.setTable_code(posBill.getTable_code());
        posBillPayment.setType(paymentWay.getPayment_class());
        posBillPayment.setJzid(paymentWay.getId());
        posBillPayment.setName(paymentWay.getPayment_name1());
        posBillPayment.setName_english(paymentWay.getPayment_name2());
        posBillPayment.setAmount(payInfo.getAmount());
        posBillPayment.setCount(1);
        // 付款号码
        if (wlife != null && (SysDictionary.PAYMENT_CLASS_WLIFE_BALANCE.equals(paymentWay.getPayment_class())||SysDictionary.PAYMENT_CLASS_WLIFE_CREDIT.equals(paymentWay.getPayment_class()))) {
            posBillPayment.setNumber(wlife.getCno());
            posBillPayment.setPhone(wlife.getMobil());
        }
        posBillPayment.setReport_date(com.tzx.pos.base.util.DateUtil.parseDate(posBill.getReport_date()));
        posBillPayment.setShift_id(posBill.getShift_id());
        posBillPayment.setPos_num(posBill.getPos_num());
        // 收款员号
        posBillPayment.setCashier_num(posBill.getCashier_num());
        posBillPayment.setLast_updatetime(new Date());
        posBillPayment.setIs_ysk("N");
        posBillPayment.setRate(1d);
        posBillPayment.setCurrency_amount(payInfo.getAmount());
        // 上传标记
        posBillPayment.setUpload_tag(0);
        // 挂账人id
        posBillPayment.setCustomer_id(null);
        // 付款流水号
        posBillPayment.setBill_code(payInfo.getSerilNo());
        posBillPayment.setRemark(null);
        posBillPayment.setPayment_state(SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
        // 与总部通讯的缓存
        posBillPayment.setParam_cach(null);
        // 批次编号
        posBillPayment.setBatch_num(posBill.getBatch_num());
        // 多收礼卷
        posBillPayment.setMore_coupon(0d);
        posBillPayment.setFee(0d);
        posBillPayment.setFee_rate(null);
        // 优惠券类型
//        String coupon_type = posBillCouponTypeMapping.get(payInfo.getSource());
//        posBillPayment.setCoupon_type(coupon_type != null ? Integer.parseInt(coupon_type) : null);
        posBillPayment.setCoupon_type(1);
        // 原付款方式ID
        posBillPayment.setYjzid(null);

//        if (WLifeConstant.PAY_SOURCE_BALANCE.equals(payInfo.getSource())){
//            // 用户实付
//            Double tmpAmount = (payInfo.getStorepay() != null ? payInfo.getStorepay()/100: 0.00);
//            posBillPayment.setCoupon_buy_price(tmpAmount);//用户实付
//            // 券账单净收
//            posBillPayment.setDue(tmpAmount);
//            // 商家优惠承担(差额)
//            posBillPayment.setTenancy_assume(payInfo.getAmount() - tmpAmount);
//        }else {
//            // 商家优惠承担
//            posBillPayment.setTenancy_assume(payInfo.getStorepay());
//        }
        
		Double couponBuyPrice = (payInfo.getStorepay() != null ? payInfo.getStorepay() : 0.00);
		if (WLifeConstant.PAY_SOURCE_BALANCE.equals(payInfo.getSource()))
		{
			couponBuyPrice = (payInfo.getStorepay() != null ? payInfo.getStorepay() / 100 : 0.00);
		}
		else if (WLifeConstant.PAY_SOURCE_CREDIT.equals(payInfo.getSource()))
		{
			couponBuyPrice = 0d;
		}
		else if (WLifeConstant.PAY_SOURCE_COUPON.equals(payInfo.getSource()))
		{
			couponBuyPrice = payInfo.getSaleDenoMoneyY();
		}
		else if (WLifeConstant.PAY_SOURCE_PRODUCT.equals(payInfo.getSource()))
		{
			couponBuyPrice = payInfo.getSaleDishMoneyY() ;
		}
		Double due = couponBuyPrice;
		Double tenancyAssume = payInfo.getAmount() - couponBuyPrice;
		Double thirdAssume = 0d;
		Double thirdFee = 0d;
        
		// 用户实付
		posBillPayment.setCoupon_buy_price(couponBuyPrice);
		// 券账单净收
		posBillPayment.setDue(due);
		// 商家优惠承担
		posBillPayment.setTenancy_assume(tenancyAssume);
		// 第三方优惠承担
		posBillPayment.setThird_assume(thirdAssume);
		// 第三方票券服务费
		posBillPayment.setThird_fee(thirdFee);
        
        return posBillPayment;
    }


    private AcewillPosBillMember createPosBillMember(String tenancyId,Integer storeId,AcewillPayWLife wlife, AcewillPosBill posBill,
                                                     AcewillPayInfo payInfo) throws Exception {
//        AcewillPayWLife wlife = microLifePayOrder.getWlife();
//        Double balance = wlife.getBalance();
//        wlife.setBalance(DoubleHelper.div(wlife.getBalance(), 100d, DEFAULT_SCALE));
        
        AcewillPosBillMember posBillMember = new AcewillPosBillMember();
        posBillMember.setTenancy_id(tenancyId);
        posBillMember.setStore_id(storeId);
        posBillMember.setId(null);
        posBillMember.setBill_num(posBill.getBill_num());
        posBillMember.setReport_date(com.tzx.pos.base.util.DateUtil.parseDate(posBill.getReport_date()));
        // 类型
        posBillMember.setType(posBillMemberTyoeMapping.get(payInfo.getSource()));
        posBillMember.setAmount(payInfo.getAmount());
        if(WLifeConstant.PAY_SOURCE_WLIFE.equals(payInfo.getSource())){
            posBillMember.setCredit(wlife.getReceive_credit());
        }else {
            posBillMember.setCredit(0.0);
        }

        posBillMember.setCard_code(wlife.getCno());
//        JSONObject memberInfo = afterPaymentDao.getBillMemberInfo(posBill.getBill_num(), SysDictionary.BILL_MEMBERCARD_JFZS06, wlife.getCno());
//        if(null != memberInfo){
//            if(StringUtil.hasText(memberInfo.optString("mobil"))){
//                posBillMember.setMobil(memberInfo.optString("mobil"));
//            }
//        }
        String mobil = wlife.getMobil();
        if(wlife.getMobil()==null || "".equals(wlife.getMobil())){
            mobil = customerService.getCustomerMobile(tenancyId, storeId, posBill.getBill_num(), wlife.getCno(),wlife.getCno());
        }
        posBillMember.setMobil(mobil);


        posBillMember.setLast_updatetime(new Date());
        // 上传标记
        posBillMember.setUpload_tag(0);
        posBillMember.setRemark(null);
        posBillMember.setBill_code(payInfo.getSerilNo());
        // 请求状态
        posBillMember.setRequest_state(null);
        posBillMember.setCustomer_code(wlife.getCno());
        posBillMember.setCustomer_name(wlife.getName());
        
        // 交易前积分
        posBillMember.setConsume_before_credit(wlife.getCredit() == null ? null : wlife.getCredit().doubleValue());
        if (WLifeConstant.PAY_SOURCE_CREDIT.equals(payInfo.getSource())) {
            posBillMember.setConsume_before_credit(wlife.getCredit() + payInfo.getAmount());
        }
        // 交易后积分
        posBillMember.setConsume_after_credit(wlife.getCredit() == null ? null : wlife.getCredit().doubleValue());
        if (WLifeConstant.PAY_SOURCE_WLIFE.equals(payInfo.getSource())) 
        {
        	posBillMember.setConsume_after_credit(DoubleHelper.add(wlife.getCredit().doubleValue(), wlife.getReceive_credit(), DEFAULT_SCALE));
        }
        // 交易前主账户余额
        posBillMember.setConsume_before_main_balance(wlife.getBalance());
        if (WLifeConstant.PAY_SOURCE_BALANCE.equals(payInfo.getSource())) {
            posBillMember.setConsume_before_main_balance(wlife.getBalance() + payInfo.getAmount());
        }
        // 交易前赠送账户余额
        posBillMember.setConsume_before_reward_balance(0d);
        // 交易后主账户余额
        posBillMember.setConsume_after_main_balance(wlife.getBalance());
        // 交易后赠送账户余额
        posBillMember.setConsume_after_reward_balance(0d);
//        wlife.setBalance(balance);
        return posBillMember;
    }

   
    /** 生成优惠券明细记录
     * @param tenancyId
     * @param storeId
     * @param posBill
     * @param payInfo
     * @return
     */
    private AcewillPosBillPaymentCoupons createPosBillPaymentCoupons(String tenancyId,Integer storeId,
                                                                     AcewillPosBill posBill, AcewillPayInfo payInfo) {
        AcewillPosBillPaymentCoupons posBillPaymentCoupons = new AcewillPosBillPaymentCoupons();

        // 商户id
        posBillPaymentCoupons.setTenancy_id(tenancyId);
        // 机构id
        posBillPaymentCoupons.setStore_id(storeId);
        // ID-自增
        posBillPaymentCoupons.setId(null);
        // 报表日期
        posBillPaymentCoupons.setBill_num(posBill.getBill_num());
        // 账单编号
        posBillPaymentCoupons.setReport_date(com.tzx.pos.base.util.DateUtil.parseDate(posBill.getReport_date()));
        // 优惠劵号
        posBillPaymentCoupons.setCoupons_code(payInfo.getSerilNo());
        // 面值
        posBillPaymentCoupons.setDeal_value(payInfo.getAmount());
        // 劵类型名称
        posBillPaymentCoupons.setDeal_name(posBillCouponTypeNameMapping.get(payInfo.getSource()));
        // 操作时间
        posBillPaymentCoupons.setLast_updatetime(new Date());
        // 备注
        posBillPaymentCoupons.setRemark(null);
        // 上传标记
        posBillPaymentCoupons.setUpload_tag("0");
        // 是否撤销
        posBillPaymentCoupons.setIs_cancel("0");
        // 优惠券大类ID
        posBillPaymentCoupons.setClass_id(null);
        // 优惠券类型ID
        posBillPaymentCoupons.setType_id(null);
        // 优惠金额
        posBillPaymentCoupons.setDiscount_money(payInfo.getAmount());
        // 抵用数量
        posBillPaymentCoupons.setDiscount_num(1d);
        // 渠道
        posBillPaymentCoupons.setChanel(posBill.getSource());
        // 菜品单价
        posBillPaymentCoupons.setPrice(null);
        // 抵扣菜品
        posBillPaymentCoupons.setItem_id(null);
        // 菜品数量
        posBillPaymentCoupons.setItem_num(null);
        // 优惠劵编码
        String couponsPro = null;
        if(WLifeConstant.PAY_SOURCE_COUPON.equals(payInfo.getSource()))
        {
        	couponsPro = SysDictionary.COUPONS_PRO_DEDUCT;
        }
        else if(WLifeConstant.PAY_SOURCE_PRODUCT.equals(payInfo.getSource()))
        {
        	couponsPro = SysDictionary.COUPONS_PRO_DISH;
        }	
        posBillPaymentCoupons.setCoupons_pro(couponsPro);
        // 优惠劵类型
//        posBillPaymentCoupons.setCoupon_type(Integer.parseInt(posBillCouponTypeMapping.get(payInfo.getSource())));
        posBillPaymentCoupons.setCoupon_type(1);
        posBillPaymentCoupons.setCoupon_buy_price(0d);
        posBillPaymentCoupons.setDue(0d);
        posBillPaymentCoupons.setTenancy_assume(payInfo.getAmount());
        posBillPaymentCoupons.setThird_assume(0d);
        posBillPaymentCoupons.setThird_fee(0d);
        posBillPaymentCoupons.setRequest_state(null);

        return posBillPaymentCoupons;
    }
    
	private List<AcewillPosBillPaymentCoupons> createPosBillPaymentCoupons(String tenancyId, Integer storeId, String paymentUid, AcewillPosBill posBill, AcewillPayInfo payInfo, List<WlifePaymentCoupons> deno_gift_couponMoney)
	{
		List<JSONObject> itemList = null;
		if (WLifeConstant.PAY_SOURCE_PRODUCT.equals(payInfo.getSource()))
		{
			try
			{
				itemList = afterPaymentDao.getBillCouponItemListByBillNum(tenancyId, storeId, posBill.getBill_num());
			}
			catch (Exception e)
			{
				LOG.info("查询账单明细异常", e);
			}
		}
		
		List<AcewillPosBillPaymentCoupons> paymentCouponsList = new ArrayList<AcewillPosBillPaymentCoupons>();
		AcewillPosBillPaymentCoupons posBillPaymentCoupons =null;
		for(WlifePaymentCoupons wlifeCoupon:deno_gift_couponMoney)
		{
			if ((null == payInfo.getCoupons_info() || false==payInfo.getCoupons_info().contains(wlifeCoupon.getId()))// 代金券
					&&(null == payInfo.getCoupon_dishs_info() || false==payInfo.getCoupon_dishs_info().contains(wlifeCoupon.getId())))// 菜品券
			{
				continue;
			}
			
			posBillPaymentCoupons = new AcewillPosBillPaymentCoupons();

	        // 商户id
	        posBillPaymentCoupons.setTenancy_id(tenancyId);
	        // 机构id
	        posBillPaymentCoupons.setStore_id(storeId);
	        // ID-自增
	        posBillPaymentCoupons.setId(null);
	        // 报表日期
	        posBillPaymentCoupons.setBill_num(posBill.getBill_num());
	        // 账单编号
	        posBillPaymentCoupons.setReport_date(DateUtil.parseDate(posBill.getReport_date()));
	        // 渠道
	        posBillPaymentCoupons.setChanel(posBill.getSource());
	        
	        // 优惠劵号
	        posBillPaymentCoupons.setCoupons_code(wlifeCoupon.getId());
	        // 面值
	        posBillPaymentCoupons.setDeal_value(wlifeCoupon.getOriginalMoneyY());
	        // 劵类型名称
			posBillPaymentCoupons.setDeal_name(null != wlifeCoupon.getTitle() ? wlifeCoupon.getTitle() : posBillCouponTypeNameMapping.get(payInfo.getSource()));
	        // 优惠券大类ID
	        posBillPaymentCoupons.setClass_id(null);
	        // 优惠券类型ID
	        posBillPaymentCoupons.setType_id(null);
	        // 优惠金额
	        posBillPaymentCoupons.setDiscount_money(wlifeCoupon.getMoneyY());
	        // 抵用数量
	        posBillPaymentCoupons.setDiscount_num(1d);
	        
			if (WLifeConstant.PAY_SOURCE_PRODUCT.equals(payInfo.getSource()) && null != wlifeCoupon.getDishItem())
			{
				JSONObject couponItem = this.getCouponItem(itemList, wlifeCoupon.getDishItem(), wlifeCoupon.getMoneyY());
				// 菜品单价
				posBillPaymentCoupons.setPrice(ParamUtil.getDoubleValueByObject(couponItem, "item_price"));
				// 抵扣菜品
				posBillPaymentCoupons.setItem_id(ParamUtil.getIntegerValueByObject(couponItem, "item_id"));
				// 菜品数量
				posBillPaymentCoupons.setItem_num(ParamUtil.getIntegerValueByObject(couponItem, "item_count"));
				posBillPaymentCoupons.setItem_unit_id(ParamUtil.getIntegerValueByObject(couponItem, "item_unit_id"));
				posBillPaymentCoupons.setRwid(ParamUtil.getIntegerValueByObject(couponItem, "rwid"));
			}
	        
			Double couponBuyPrice = wlifeCoupon.getSaleMoneyY();
			Double due = couponBuyPrice;
			Double tenancyAssume = wlifeCoupon.getMoneyY() - couponBuyPrice;

			posBillPaymentCoupons.setCoupon_buy_price(couponBuyPrice);
			posBillPaymentCoupons.setDue(due);
			posBillPaymentCoupons.setTenancy_assume(tenancyAssume);
			posBillPaymentCoupons.setThird_assume(0d);
			posBillPaymentCoupons.setThird_fee(0d);
	        
	        // 优惠劵编码
	        String couponsPro = null;
	        if(WLifeConstant.PAY_SOURCE_COUPON.equals(payInfo.getSource()))
	        {
	        	couponsPro = SysDictionary.COUPONS_PRO_DEDUCT;
	        }
	        else if(WLifeConstant.PAY_SOURCE_PRODUCT.equals(payInfo.getSource()))
	        {
	        	couponsPro = SysDictionary.COUPONS_PRO_DISH;
	        }	
	        posBillPaymentCoupons.setCoupons_pro(couponsPro);
	        // 优惠劵类型
	        posBillPaymentCoupons.setCoupon_type(SysDictionary.COUPON_TYPE_CODE);
	        
	        posBillPaymentCoupons.setRequest_state(null);
	        // 操作时间
	        posBillPaymentCoupons.setLast_updatetime(new Date());
	        // 备注
	        posBillPaymentCoupons.setRemark(null);
	        // 上传标记
	        posBillPaymentCoupons.setUpload_tag("0");
	        // 是否撤销
	        posBillPaymentCoupons.setIs_cancel("0");
	        posBillPaymentCoupons.setPayment_id(paymentUid);
	        
	        paymentCouponsList.add(posBillPaymentCoupons);
		}
		return paymentCouponsList;
	}
	
	private JSONObject getCouponItem(List<JSONObject> itemList, WlifePaymentCouponsItem dishItem, Double couponMoney)
	{
		JSONObject couponItemJson = null;
		if (null != itemList)
		{
			for (JSONObject itemJson : itemList)
			{
				String itemNum = ParamUtil.getStringValueByObject(itemJson, "item_num");
				if (CommonUtil.isNullOrEmpty(itemNum))
				{
					itemNum = "";
				}
				String unitId = ParamUtil.getStringValueByObject(itemJson, "item_unit_id");
				if (CommonUtil.isNullOrEmpty(unitId))
				{
					unitId = "";
				}
				Double realAmount = ParamUtil.getDoubleValueByObject(itemJson, "real_amount");
				if (CommonUtil.isNullOrEmpty(realAmount))
				{
					realAmount = 0d;
				}
				
				if (itemNum.equals(dishItem.getDishsno()) && unitId.equals(dishItem.getDuid()) && couponMoney.doubleValue() == realAmount.doubleValue())
				{
					couponItemJson = itemJson;
					break;
				}
				else if (itemNum.equals(dishItem.getDishsno()) && unitId.equals(dishItem.getDuid()) && (null == couponItemJson || realAmount.doubleValue() > ParamUtil.getDoubleValueByObject(couponItemJson, "real_amount").doubleValue()))
				{
					couponItemJson = itemJson;
				}
			}
		}

		if (null == couponItemJson)
		{
			couponItemJson = new JSONObject();
			couponItemJson.put("item_price", dishItem.getPriceY());
			couponItemJson.put("item_id", null);
			couponItemJson.put("item_count", dishItem.getCount());
			couponItemJson.put("item_unit_id", dishItem.getDuid());
			couponItemJson.put("rwid", null);
		}

		return couponItemJson;
	}

    private CrmCardTradingListEntity createCrmCardTradingList(String tenancyId,Integer storeId,AcewillPayWLife wlife, AcewillPosBill posBill,AcewillPayInfo payInfo) {
        CrmCardTradingListEntity crmCardTradingList = new CrmCardTradingListEntity();
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
//        AcewillPayWLife wlife = microLifePayOrder.getWlife();
        // 商户id
        crmCardTradingList.setTenancy_id(tenancyId);
        // Id
        crmCardTradingList.setId(null);
        // 卡id
        crmCardTradingList.setCard_id(null);
        // 卡号 
        crmCardTradingList.setCard_code(wlife.getCno());
        // 交易单号
        crmCardTradingList.setBill_code(payInfo.getSerilNo());
        // 交易渠道
        crmCardTradingList.setChanel(posBill.getSource());
        // 交易门店ID
        crmCardTradingList.setStore_id(storeId);
        // 交易日期
        crmCardTradingList.setBusiness_date(com.tzx.pos.base.util.DateUtil.parseDate(posBill.getReport_date()));
        // 主账户交易金额
        crmCardTradingList.setMain_trading(payInfo.getAmount());
        // 赠送账户交易金额
        crmCardTradingList.setReward_trading(0d);
        // 交易类型
        crmCardTradingList.setOperat_type(SysDictionary.OPERAT_TYPE_XF);

        // 原主账户金额
        crmCardTradingList.setMain_original(
                DoubleHelper.add(wlife.getBalance(),payInfo.getAmount(), DEFAULT_SCALE));
        // 原赠送账户金额
        crmCardTradingList.setReward_original(0d);
        // 押金金额
        crmCardTradingList.setDeposit(0d);
        // 操作员
        crmCardTradingList.setOperator(null);
        // 操作时间
        crmCardTradingList.setOperate_time(timestamp);
        // 账单金额
        crmCardTradingList.setBill_money(posBill.getPayment_amount());
        // 第三方账单号
        crmCardTradingList.setThird_bill_code(posBill.getBill_num());
        // 原账单号
        crmCardTradingList.setBill_code_original(null);
        // 活动ID
        crmCardTradingList.setActivity_id(null);
        // 会员ID
        crmCardTradingList.setCustomer_id(null);
        // 已撤销金额
        crmCardTradingList.setRevoked_trading(0d);
        //
        crmCardTradingList.setBatch_num(posBill.getBatch_num());
        // 最后修改时间
        crmCardTradingList.setLast_updatetime(timestamp);
        // 门店修改时间
        crmCardTradingList.setStore_updatetime(timestamp);
        // 卡类ID
        crmCardTradingList.setCard_class_id(null);
        // 会员name
        crmCardTradingList.setName(wlife.getName());
        // 会员电话
        crmCardTradingList.setMobil(wlife.getMobil());
        // 操作员ID
        crmCardTradingList.setOperator_id(null);
        // 班次ID
        crmCardTradingList.setShift_id(posBill.getShift_id());
        // 卡余额
        crmCardTradingList.setTotal_balance(wlife.getBalance());
        // 卡赠送账户余额
        crmCardTradingList.setReward_balance(0d);
        // 卡主账户余额
        crmCardTradingList.setMain_balance(wlife.getBalance());
        // 付款方式
        crmCardTradingList.setPay_type(null);

        // 销售人员ID
        crmCardTradingList.setSalesman(null);
        // 人员提成金额
        crmCardTradingList.setCommission_saler_money(0d);
        // 机构提成金额
        crmCardTradingList.setCommission_store_money(0d);
        // 可开票金额
        crmCardTradingList.setInvoice_balance(0d);
        //pos机台号
        crmCardTradingList.setPosNum(posBill.getPos_num());

        crmCardTradingList.setIs_invoice("0");
        crmCardTradingList.setPayment_state("1");
        crmCardTradingList.setRecharge_state("1");
        crmCardTradingList.setRequest_status("02");
        crmCardTradingList.setRequest_code(null);
        crmCardTradingList.setRequest_msg(null);

        return crmCardTradingList;
    }




    static {
        paymentWayMapping.put(WLifeConstant.PAY_SOURCE_ALIPAY, "ali_pay_wlife");
        paymentWayMapping.put(WLifeConstant.PAY_SOURCE_WEIXIN, "wechat_pay_wlife");
       /* paymentWayMapping.put(WLifeConstant.PAY_SOURCE_BALANCE, SysDictionary.PAYMENT_CLASS_CARD);
        paymentWayMapping.put(WLifeConstant.PAY_SOURCE_CREDIT, SysDictionary.PAYMENT_CLASS_CARD_CREDIT);
        paymentWayMapping.put(WLifeConstant.PAY_SOURCE_COUPON, SysDictionary.PAYMENT_CLASS_COUPONS);
        paymentWayMapping.put(WLifeConstant.PAY_SOURCE_PRODUCT, SysDictionary.PAYMENT_CLASS_COUPONS);*/
        paymentWayMapping.put(WLifeConstant.PAY_SOURCE_BALANCE, SysDictionary.PAYMENT_CLASS_WLIFE_BALANCE);
        paymentWayMapping.put(WLifeConstant.PAY_SOURCE_CREDIT, SysDictionary.PAYMENT_CLASS_WLIFE_CREDIT);
        paymentWayMapping.put(WLifeConstant.PAY_SOURCE_COUPON, SysDictionary.PAYMENT_CLASS_WLIFE_COUPON);
        paymentWayMapping.put(WLifeConstant.PAY_SOURCE_PRODUCT, SysDictionary.PAYMENT_CLASS_WLIFE_COUPON);
        paymentWayMapping.put(WLifeConstant.PAY_SOURCE_WLIFE, "wlife");
        //美味不用等对接
        paymentWayMapping.put(WLifeConstant.PAY_SOURCE_MEIWEI_ALIPAY, "ali_pay_meiwei");
        paymentWayMapping.put(WLifeConstant.PAY_SOURCE_MEIWEI_WEIXIN, "wechat_pay_meiwei");

//        posBillMemberTyoeMapping.put(WLifeConstant.PAY_SOURCE_ALIPAY, SysDictionary.BILL_MEMBERCARD_HYZK02);
//        posBillMemberTyoeMapping.put(WLifeConstant.PAY_SOURCE_WEIXIN, SysDictionary.BILL_MEMBERCARD_HYZK02);
        posBillMemberTyoeMapping.put(WLifeConstant.PAY_SOURCE_BALANCE, SysDictionary.BILL_MEMBERCARD_CZXF03);
        posBillMemberTyoeMapping.put(WLifeConstant.PAY_SOURCE_CREDIT, SysDictionary.BILL_MEMBERCARD_JFDX05);
        posBillMemberTyoeMapping.put(WLifeConstant.PAY_SOURCE_COUPON, SysDictionary.BILL_MEMBERCARD_YHJ04);
        posBillMemberTyoeMapping.put(WLifeConstant.PAY_SOURCE_PRODUCT, SysDictionary.BILL_MEMBERCARD_YHJ04);
        posBillMemberTyoeMapping.put(WLifeConstant.PAY_SOURCE_WLIFE, SysDictionary.BILL_MEMBERCARD_JFZS06);

        posBillCouponTypeMapping.put(WLifeConstant.PAY_SOURCE_COUPON, SysDictionary.CUSTOMER_COUPON_TYPE_CASH);
        posBillCouponTypeMapping.put(WLifeConstant.PAY_SOURCE_PRODUCT, SysDictionary.CUSTOMER_COUPON_TYPE_DISH);
        posBillCouponTypeNameMapping.put(WLifeConstant.PAY_SOURCE_COUPON, "优惠券");
        posBillCouponTypeNameMapping.put(WLifeConstant.PAY_SOURCE_PRODUCT, "菜品劵");
    }


	@Override
	public void lockOrder(String tenancyId, Integer storeId, JSONObject param, JSONObject responseJson) throws Exception
	{
        String tableCode = param.optString("tableno");
        String outOrderId = param.optString("out_order_id");
        JSONObject memberInfo = param.optJSONObject("member");
        String mobile = memberInfo.optString("mobile");
        if(Tools.isNullOrEmpty(mobile))
        {
        	mobile = memberInfo.optString("phone");
        }
        
        String openId = memberInfo.optString("openid");
        if(Tools.isNullOrEmpty(openId))
        {
        	openId = memberInfo.optString("unionid");
        }
        
		JSONObject tableStatus = afterPaymentDao.getTableStatus(tenancyId, storeId, tableCode);
		if(null==tableStatus || tableStatus.isEmpty())
		{
			throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_TABLE_CODE);
		}
		
        String state = tableStatus.optString("state");
        String lockOptNum = tableStatus.optString("lock_opt_num");
        String lockPosNum = tableStatus.optString("lock_pos_num");
        
        if(false ==SysDictionary.TABLE_STATE_BUSY.equals(state))
        {
        	//桌位不是占用状态,桌位未开台
        	throw SystemException.getInstance(PosErrorCode.TABLE_STATE_EXCEPTION);
        }


        //查询账单 pos是否合并账单
        JSONObject bill = posDishService.isCombineTableOfBillNum(tenancyId,storeId, outOrderId);
        if(null == bill){ //不存在并台
            if(Tools.hv(lockPosNum) && false ==LOCK_POS_NUM_WXWLIFE.equals(lockPosNum))
            {
                //桌位已锁台,但不是微生活锁台
                throw SystemException.getInstance(PosErrorCode.TABLE_ALREADY_LOCK_ERROR).set("{0}", lockOptNum).set("{1}", lockPosNum);
            }
            else
            {
                afterPaymentDao.lockTableStatus(storeId, tableCode, LOCK_POS_NUM_WXWLIFE, mobile, memberInfo.optString("name"));
            }
        }
        responseJson.put(SUCCESS,1);
        responseJson.put(MSG,"SUCCESS");
	}

	@Override
	public void unlockOrder(String tenancyId, Integer storeId, JSONObject param, JSONObject responseJson) throws Exception
	{
		String outOrderId = param.optString("out_order_id");
		if(Tools.isNullOrEmpty(outOrderId))
		{
			throw SystemException.getInstance(PosErrorCode.ORDER_NOT_EXISTS_ERROR);
		}
		
		if(com.tzx.clientorder.wlifeprogram.common.constant.Constant.ORDERMODE_AFTERPAY.equals(afterPaymentDao.getSysParameter(tenancyId, storeId, SysParameterCode.WLIFE_ORDERMODE_KEY)))
		{
            //查询账单 pos是否合并账单
            JSONObject bill = posDishService.isCombineTableOfBillNum(tenancyId,storeId, outOrderId);
            if(null == bill){ //不存在并台
                //根据微生活订单号查询账单
                List<JSONObject> billList = afterPaymentDao.getBillInfoByOrderNum(tenancyId, storeId, outOrderId);
                JSONObject billJson = null;
                if(null!=billList && 0< billList.size())
                {
                    billJson = billList.get(0);
                }

                if(null==billJson || billJson.isEmpty())
                {
                    throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_BILL);
                }

                String tableCode = billJson.optString("table_code");
                JSONObject tableStatus = afterPaymentDao.getTableStatus(tenancyId, storeId, tableCode);
                if (null == tableStatus || tableStatus.isEmpty())
                {
                    throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_TABLE_CODE);
                }

                String state = tableStatus.optString("state");
                String lockOptNum = tableStatus.optString("lock_opt_num");
                String lockPosNum = tableStatus.optString("lock_pos_num");

                if (false == SysDictionary.TABLE_STATE_BUSY.equals(state))
                {
                    // 桌位不是占用状态,桌位未开台
                    //throw SystemException.getInstance(PosErrorCode.TABLE_STATE_EXCEPTION);
                    responseJson.put(SUCCESS, 0);
                    responseJson.put(MSG, "桌位状态异常");
                    return ;
                }

                if (Tools.hv(lockPosNum) && false == LOCK_POS_NUM_WXWLIFE.equals(lockPosNum))
                {
                    // 桌位已锁台,但不是微生活锁台
                    throw SystemException.getInstance(PosErrorCode.TABLE_ALREADY_LOCK_ERROR).set("{0}", lockOptNum).set("{1}", lockPosNum);
                }
                else
                {
                    afterPaymentDao.lockTableStatus(storeId, tableCode, null, null, null);
                }
            }
		}
		responseJson.put(SUCCESS, 1);
		responseJson.put(MSG, "SUCCESS");
	}
}
