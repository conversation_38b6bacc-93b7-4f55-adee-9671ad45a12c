package com.tzx.base.entity;

import java.sql.Timestamp;
import java.util.Date;

/**
 * 退款
 * 
 * <AUTHOR>
 *
 */
public class PosThirdPaymentRefundEntity
{

	private String		tenancy_id;
	private Integer		store_id;
	private Integer		id;
	private Date		report_date;
	private Integer		shift_id;
	private String		pos_num;
	private String		opt_num;
	private String		channel;
	private String		service_type;
	private String		order_num;
	private String		out_request_no;
	private String		form_trade_no;
	private String		trade_no;
	private String		payment_class;
	private Integer		payment_id;
	private Double		total_amount;
	private Double		settle_amount;
	private Timestamp	create_time;
	private String		extra;
	private Timestamp	last_updatetime;
	private String		last_operator;
	private String		failure_code;
	private String		failure_msg;
	private Integer		retry_count	= 0;
	private String		oper_type;
	private String		status;
	private String		finish_status;

	public PosThirdPaymentRefundEntity()
	{
		super();
	}

	public PosThirdPaymentRefundEntity(String tenancy_id, Integer store_id, Date report_date, Integer shift_id, String pos_num, String opt_num, String channel, String service_type, String order_num, String out_request_no, String form_trade_no, String payment_class, Integer payment_id,
			Double total_amount, Double settle_amount, Timestamp create_time, Timestamp last_updatetime, String last_operator, String failure_code, String failure_msg, String oper_type, String status, String finish_status)
	{
		super();
		this.tenancy_id = tenancy_id;
		this.store_id = store_id;
		this.report_date = report_date;
		this.shift_id = shift_id;
		this.pos_num = pos_num;
		this.opt_num = opt_num;
		this.channel = channel;
		this.service_type = service_type;
		this.order_num = order_num;
		this.out_request_no = out_request_no;
		this.form_trade_no = form_trade_no;
		this.payment_class = payment_class;
		this.payment_id = payment_id;
		this.total_amount = total_amount;
		this.settle_amount = settle_amount;
		this.create_time = create_time;
		this.last_updatetime = last_updatetime;
		this.last_operator = last_operator;
		this.failure_code = failure_code;
		this.failure_msg = failure_msg;
		this.oper_type = oper_type;
		this.status = status;
		this.finish_status = finish_status;
	}

	public String getTenancy_id()
	{
		return tenancy_id;
	}

	public void setTenancy_id(String tenancy_id)
	{
		this.tenancy_id = tenancy_id;
	}

	public Integer getStore_id()
	{
		return store_id;
	}

	public void setStore_id(Integer store_id)
	{
		this.store_id = store_id;
	}

	public Integer getId()
	{
		return id;
	}

	public void setId(Integer id)
	{
		this.id = id;
	}

	public Date getReport_date()
	{
		return report_date;
	}

	public void setReport_date(Date report_date)
	{
		this.report_date = report_date;
	}

	public Integer getShift_id()
	{
		return shift_id;
	}

	public void setShift_id(Integer shift_id)
	{
		this.shift_id = shift_id;
	}

	public String getPos_num()
	{
		return pos_num;
	}

	public void setPos_num(String pos_num)
	{
		this.pos_num = pos_num;
	}

	public String getOpt_num()
	{
		return opt_num;
	}

	public void setOpt_num(String opt_num)
	{
		this.opt_num = opt_num;
	}

	public String getChannel()
	{
		return channel;
	}

	public void setChannel(String channel)
	{
		this.channel = channel;
	}

	public String getService_type()
	{
		return service_type;
	}

	public void setService_type(String service_type)
	{
		this.service_type = service_type;
	}

	public String getOrder_num()
	{
		return order_num;
	}

	public void setOrder_num(String order_num)
	{
		this.order_num = order_num;
	}

	public String getOut_request_no()
	{
		return out_request_no;
	}

	public void setOut_request_no(String out_request_no)
	{
		this.out_request_no = out_request_no;
	}

	public String getForm_trade_no()
	{
		return form_trade_no;
	}

	public void setForm_trade_no(String form_trade_no)
	{
		this.form_trade_no = form_trade_no;
	}

	public String getTrade_no()
	{
		return trade_no;
	}

	public void setTrade_no(String trade_no)
	{
		this.trade_no = trade_no;
	}

	public String getPayment_class()
	{
		return payment_class;
	}

	public void setPayment_class(String payment_class)
	{
		this.payment_class = payment_class;
	}

	public Integer getPayment_id()
	{
		return payment_id;
	}

	public void setPayment_id(Integer payment_id)
	{
		this.payment_id = payment_id;
	}

	public Double getTotal_amount()
	{
		return total_amount;
	}

	public void setTotal_amount(Double total_amount)
	{
		this.total_amount = total_amount;
	}

	public Double getSettle_amount()
	{
		return settle_amount;
	}

	public void setSettle_amount(Double settle_amount)
	{
		this.settle_amount = settle_amount;
	}

	public Timestamp getCreate_time()
	{
		return create_time;
	}

	public void setCreate_time(Timestamp create_time)
	{
		this.create_time = create_time;
	}

	public String getExtra()
	{
		return extra;
	}

	public void setExtra(String extra)
	{
		this.extra = extra;
	}

	public Timestamp getLast_updatetime()
	{
		return last_updatetime;
	}

	public void setLast_updatetime(Timestamp last_updatetime)
	{
		this.last_updatetime = last_updatetime;
	}

	public String getLast_operator()
	{
		return last_operator;
	}

	public void setLast_operator(String last_operator)
	{
		this.last_operator = last_operator;
	}

	public String getFailure_code()
	{
		return failure_code;
	}

	public void setFailure_code(String failure_code)
	{
		this.failure_code = failure_code;
	}

	public String getFailure_msg()
	{
		return failure_msg;
	}

	public void setFailure_msg(String failure_msg)
	{
		this.failure_msg = failure_msg;
	}

	public Integer getRetry_count()
	{
		return retry_count;
	}

	public void setRetry_count(Integer retry_count)
	{
		this.retry_count = retry_count;
	}

	public String getOper_type()
	{
		return oper_type;
	}

	public void setOper_type(String oper_type)
	{
		this.oper_type = oper_type;
	}

	public String getStatus()
	{
		return status;
	}

	public void setStatus(String status)
	{
		this.status = status;
	}

	public String getFinish_status()
	{
		return finish_status;
	}

	public void setFinish_status(String finish_status)
	{
		this.finish_status = finish_status;
	}

}
