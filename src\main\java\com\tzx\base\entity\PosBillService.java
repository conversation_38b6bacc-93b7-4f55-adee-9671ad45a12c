package com.tzx.base.entity;

/**
 * 账单服务费表映射实体
 * 
 * <AUTHOR>
 * <AUTHOR>
 */
public class PosBillService
{
	private String	tenancy_id;
	private Integer	store_id;
	private Integer	id;
	private String	bill_num;
	private String	table_code;
	private String	stable_code;
	private Integer	service_id;
	private String	service_type;
	private String	taken_mode;
	private Double	service_scale;
	private Double	service_amount;
	private Double	service_count;
	private Double	service_total;
	private Double	service_rate;
	private Integer	upload_tag;

	public String getTenancy_id()
	{
		return tenancy_id;
	}

	public void setTenancy_id(String tenancy_id)
	{
		this.tenancy_id = tenancy_id;
	}

	public Integer getStore_id()
	{
		return store_id;
	}

	public void setStore_id(Integer store_id)
	{
		this.store_id = store_id;
	}

	public Integer getId()
	{
		return id;
	}

	public void setId(Integer id)
	{
		this.id = id;
	}

	public String getBill_num()
	{
		return bill_num;
	}

	public void setBill_num(String bill_num)
	{
		this.bill_num = bill_num;
	}

	public String getTable_code()
	{
		return table_code;
	}

	public void setTable_code(String table_code)
	{
		this.table_code = table_code;
	}

	public String getStable_code()
	{
		return stable_code;
	}

	public void setStable_code(String stable_code)
	{
		this.stable_code = stable_code;
	}

	public Integer getService_id()
	{
		return service_id;
	}

	public void setService_id(Integer service_id)
	{
		this.service_id = service_id;
	}

	public String getService_type()
	{
		return service_type;
	}

	public void setService_type(String service_type)
	{
		this.service_type = service_type;
	}

	public String getTaken_mode()
	{
		return taken_mode;
	}

	public void setTaken_mode(String taken_mode)
	{
		this.taken_mode = taken_mode;
	}

	public Double getService_scale()
	{
		return service_scale;
	}

	public void setService_scale(Double service_scale)
	{
		this.service_scale = service_scale;
	}

	public Double getService_amount()
	{
		return service_amount;
	}

	public void setService_amount(Double service_amount)
	{
		this.service_amount = service_amount;
	}

	public Double getService_count()
	{
		return service_count;
	}

	public void setService_count(Double service_count)
	{
		this.service_count = service_count;
	}

	public Double getService_total()
	{
		return service_total;
	}

	public void setService_total(Double service_total)
	{
		this.service_total = service_total;
	}

	public Double getService_rate()
	{
		return service_rate;
	}

	public void setService_rate(Double service_rate)
	{
		this.service_rate = service_rate;
	}

	public Integer getUpload_tag()
	{
		return upload_tag;
	}

	public void setUpload_tag(Integer upload_tag)
	{
		this.upload_tag = upload_tag;
	}

}
