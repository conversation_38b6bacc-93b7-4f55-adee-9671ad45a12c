/*  
 * @(#) SpringConext.java Create on 2014-06-01
 *   
 * Copyright 2013 by gxhk  
 */

package com.tzx.framework.common.util;

import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * @SpringConext.java created at 2014-6-1 上午11:38:48 by henry
 * 
 * 
 * <AUTHOR> authorEmail})
 * @version $Revision: 141
 */
@Component
public class SpringConext implements ApplicationContextAware {

	private static volatile ApplicationContext context;

	public void setApplicationContext(ApplicationContext acx) {
		context = acx;
	}

	public static ApplicationContext getApplicationContext() {
		return context;
	}

	public static Object getBean(String beanName){
		 for(;;){
	         if(null!=context) break;
	     }
		return context.getBean(beanName);
	}
}
