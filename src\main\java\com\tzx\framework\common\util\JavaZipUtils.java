package com.tzx.framework.common.util;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

@SuppressWarnings("restriction")
public class JavaZipUtils {

	/**
	 * 使用zip进行压缩
	 * 
	 * @param str
	 *            压缩前的文本
	 * @return 返回压缩后的文本
	 */
	public static final String zip(String str) {
		if (str == null)
			return null;
		byte[] compressed;
		ByteArrayOutputStream out = null;
		ZipOutputStream zout = null;
		String compressedStr = null;
		try {
			out = new ByteArrayOutputStream();
			zout = new ZipOutputStream(out);
			zout.putNextEntry(new ZipEntry("0"));
			zout.write(str.getBytes("UTF-8"));
			zout.closeEntry();
			compressed = out.toByteArray();
			compressedStr = new sun.misc.BASE64Encoder()
					.encodeBuffer(compressed);
		} catch (IOException e) {
			compressed = null;
		} finally {
			if (zout != null) {
				try {
					zout.close();
				} catch (IOException e) {
				}
			}
			if (out != null) {
				try {
					out.close();
				} catch (IOException e) {
				}
			}
		}
		return compressedStr;
	}

	/**
	 * 使用zip进行解压缩
	 * 
	 * @param compressedText
	 *            压缩后的文本
	 * @return 解压后的字符串
	 */
	public static final String unzip(String compressedText) {
		if (compressedText == null) {
			return null;
		}

		ByteArrayOutputStream out = null;
		ByteArrayInputStream in = null;
		ZipInputStream zin = null;
		String decompressed = null;
		try {
			byte[] compressed = new sun.misc.BASE64Decoder()
					.decodeBuffer(compressedText);
			out = new ByteArrayOutputStream();
			in = new ByteArrayInputStream(compressed);
			zin = new ZipInputStream(in);
			zin.getNextEntry();
			byte[] buffer = new byte[1024];
			int offset = -1;
			while ((offset = zin.read(buffer)) != -1) {
				out.write(buffer, 0, offset);
			}
			decompressed = out.toString();
		} catch (IOException e) {
			decompressed = null;
		} finally {
			if (zin != null) {
				try {
					zin.close();
				} catch (IOException e) {
				}
			}
			if (in != null) {
				try {
					in.close();
				} catch (IOException e) {
				}
			}
			if (out != null) {
				try {
					out.close();
				} catch (IOException e) {
				}
			}
		}
		return decompressed;
	}

	/**
	 * 根据zip方式压缩List<JSONObject>为String
	 * 
	 * @param 压缩前的List
	 *            <JSONObject>datas
	 * @return 压缩后的文本
	 */
	public static final String zipJsonList(List<JSONObject> datas) {
		if (datas != null) {
			return JavaZipUtils.zip(datas.toString());
		}
		return null;
	}

	/**
	 * 将压缩后的文本转化为JSONArray的数据格式
	 * 
	 * @param compressedText
	 *            压缩后的文本
	 * @return JSONArray
	 */
	public static final JSONArray unzipJsonList(String compressedText) {
		JSONArray result;
		if (compressedText != null) {
			String jsonString = JavaZipUtils.unzip(compressedText);
			result = JSONArray.fromObject(jsonString);
			jsonString = null;
		} else {
			result = new JSONArray();
		}
		return result;
	}

	/**
	 * 兼容模式下将String类型转化为JSONArray的数据格式
	 * <p>
	 * 先将String转化为 JSONArray格式
	 * <p>
	 * 如果发生错误了，先解压再转化为JSONArray格式
	 * 
	 * @param data
	 * @return
	 */
	public static final JSONArray unzipJsonListWithCompatibility(String data) {
		JSONArray array;
		if (data != null) {
			try {
				// 先直接转JSONArray
				array = JSONArray.fromObject(data);
			} catch (Exception e) {
				// 发生异常 就在解压后 转JSONArray
				String jsonString = JavaZipUtils.unzip(data);
				array = JSONArray.fromObject(jsonString);
				jsonString = null; // 中间变量，可能占用空间比较大，设置为NULL,用于回收
			}
		} else {
			array = new JSONArray();
		}
		return array;
	}

	public static String getStringSize(long size) {
		// 如果字节数少于1024，则直接以B为单位，否则先除于1024，后3位因太少无意义
		if (size < 1024) {
			return String.valueOf(size) + "B";
		} else {
			size = size / 1024;
		}
		// 如果原字节数除于1024之后，少于1024，则可以直接以KB作为单位
		// 因为还没有到达要使用另一个单位的时候
		// 接下去以此类推
		if (size < 1024) {
			return String.valueOf(size) + "KB";
		} else {
			size = size / 1024;
		}
		if (size < 1024) {
			// 因为如果以MB为单位的话，要保留最后1位小数，
			// 因此，把此数乘以100之后再取余
			size = size * 100;
			return String.valueOf((size / 100)) + "."
					+ String.valueOf((size % 100)) + "MB";
		} else {
			// 否则如果要以GB为单位的，先除于1024再作同样的处理
			size = size * 100 / 1024;
			return String.valueOf((size / 100)) + "."
					+ String.valueOf((size % 100)) + "GB";
		}
	}

}