package com.tzx.pos.po.springjdbc.dao.imp;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Repository;

import com.tzx.pos.base.dao.imp.BaseDaoImp;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.po.springjdbc.dao.QuickPassDao;

import net.sf.json.JSONObject;
@Repository(QuickPassDao.NAME)
public class QuickPassDaoImp extends BaseDaoImp implements QuickPassDao {

	@Override
	public int insert(Object[] objs) throws Exception {
		// TODO Auto-generated method stub
		StringBuilder sBuilder=new StringBuilder("insert into quick_pass_log(tenancy_id,store_id,report_date,shift_id,pos_num,opt_num,bill_num,table_code,sale_mode,isprint_bill,is_invoice,is_online_payment,payment_amount,difference,amount,cont,number,phone,reason_id,customer_id,bill_code,state,currency_amount,jzid,mem,jzid_credit,credit,arrivedAmount)values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
		int res=this.update(sBuilder.toString(), objs);
		return res;
	}

	@Override
	public List<JSONObject>  findList(Map<String, Object> map) throws Exception {
		// TODO Auto-generated method stub
		StringBuffer sBuffer=new StringBuffer();
		sBuffer.append("select bill_num,table_code,bill_code,amount,id as quick_id,payment_amount,difference from quick_pass_log where 1=1");
		Object[] objs=null;
		List<Object> list=new ArrayList<Object>();
		String table_code = ParamUtil.getStringValue(map, "table_code", false, null);
		String pos_num = ParamUtil.getStringValue(map, "pos_num", false, null);
		String bill_code = ParamUtil.getStringValue(map, "bill_code", false, null);
		Date report_date = ParamUtil.getDateValue(map, "report_date", false, null);
		int shift_id = ParamUtil.getIntegerValue(map, "shift_id", false, null);
		String opt_num = ParamUtil.getStringValue(map, "opt_num", false, null);
		String bill_num = ParamUtil.getStringValue(map, "bill_num", false, null);
		if(table_code!=null){
			sBuffer.append(" and table_code=?");
			list.add(table_code);
		}
		if(pos_num!=null){
			sBuffer.append(" and pos_num=?");
			list.add(pos_num);
		}
		if(bill_code!=null){
			sBuffer.append(" and bill_code=?");
			list.add(bill_code);
		}
		if(report_date!=null){
			sBuffer.append(" and report_date=?");
			list.add(report_date);
		}
		if(shift_id>0){
			sBuffer.append(" and shift_id=?");
			list.add(shift_id);
		}
		if(opt_num!=null){
			sBuffer.append(" and opt_num=?");
			list.add(opt_num);
		}
		if(bill_num!=null){
			sBuffer.append(" and bill_num=?");
			list.add(bill_num);
		}
		objs=list.toArray();
		List<JSONObject> resLst=this.query4Json("", sBuffer.toString(), objs);
		return resLst;
	}

	@Override
	public int deleteData(String bill_code,int quick_id) throws Exception {
		// TODO Auto-generated method stub
		StringBuffer sBuilder=new StringBuffer("update quick_pass_log set state=0 where ");
		List list=new ArrayList<>();
		Object[] objs =null;
		if(bill_code!=null&&!"".equals(bill_code)){
			sBuilder.append(" bill_code=?");
			list.add(bill_code);
		}
		if(quick_id>0){
			if(list.size()>0){
				sBuilder.append(" and ");
			}
			sBuilder.append(" id=?");
			list.add(quick_id);
		}
		objs=list.toArray();
		
		return this.update(sBuilder.toString(), objs);
	}


	@Override
	public JSONObject getDataByBillNum(String tenancy_id, String bill_num) throws Exception {
		String sql = "select * from pos_bill where bill_num = ?";
		List<JSONObject> data  = query4Json(tenancy_id,sql,new Object[]{bill_num});
		if (data!=null && data.size()>0){
			return  data.get(0);
		}
		return null;
	}
}
