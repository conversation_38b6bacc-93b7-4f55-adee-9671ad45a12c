package com.tzx.base.bo.imp;

import java.sql.CallableStatement;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.CallableStatementCallback;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import com.tzx.base.bo.PosCodeService;
import com.tzx.framework.common.constant.Code;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.pos.po.springjdbc.dao.PosCodeDao;

import net.sf.json.JSONObject;

@Service(PosCodeService.NAME)
public class PosCodeServiceImpl implements PosCodeService
{

	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;
	
	@Resource(name = PosCodeDao.NAME)
	private PosCodeDao 			posCodeDao;
	
//	@Resource(name = RedisTaskDao.NAME)
//	private RedisTaskDao 			redisTaskDao;
	
	@Resource(name = "transactionManager")
	private DataSourceTransactionManager transactionManager;

	@Override
	public String getCode(String tenantId, Code code, JSONObject param) throws Exception
	{
		if (code != null)
		{
			String result = "";
			switch (code)
			{
				/**存入sys_bill_code_rule表中的编码规则*************************/
				case POS_BILL_CODE:
				case POS_RECORD_CODE:
				case POS_BATCH_NUM:
				case POS_THIRD_PAYMENT_ORDER_NUM:
				case POS_BILL_DISH_CODE: //取餐号
					result = getBillCode(tenantId, code, param);
					break;
				case BILL_BOOKED_NUM:	
					result = getBillCode(tenantId, code, param);
					break;
				case SCM_INIT_STORAGE_BILL_NO:
					result = getBillCode(tenantId, code, param);
					break;
				case SCM_SUPPLY_IN_BILL_NO:
					result = getBillCode(tenantId, code, param);
					break;
				case SCM_SUPPLY_RETURN_BILL_NO:
					result = getBillCode(tenantId, code, param);
					break;
				case SCM_TRANSFER_PICKING_BILL_NO:
					result = getBillCode(tenantId, code, param);
					break;
				case SCM_TRANSFER_RETURN_BILL_NO:
					result = getBillCode(tenantId, code, param);
					break;
				case SCM_TRANSFER_TRANSFER_BILL_NO:
					result = getBillCode(tenantId, code, param);
					break;
				case SCM_LOSS_BILL_NO:
					result = getBillCode(tenantId, code, param);
					break;
				case SCM_LOSS_CONSUME_BILL_NO:
					result = getBillCode(tenantId, code, param);
					break;
				case SCM_OUT_BILL_NO:
					result = getBillCode(tenantId, code, param);
					break;
				case SCM_IN_BILL_NO:
					result = getBillCode(tenantId, code, param);
					break;
				case SCM_PROFIT_BILL_NO:
					result = getBillCode(tenantId, code, param);
					break;
				case SCM_STOCKTAKING_PERIOD_BILL_NO:
					result = getBillCode(tenantId, code, param);
					break;
					
				case SCM_STOCKTAKING_WEEK_PERIOD_BILL_NO:
					result = getBillCode(tenantId, code, param);
					break;
					
				case SCM_STOCKTAKING_MONTH_PERIOD_BILL_NO:
					result = getBillCode(tenantId, code, param);
					break;
				case SCM_INQUIRY_BILL_NO:
					result = getBillCode(tenantId, code, param);
					break;	
				case SCM_ORDER_BILL_NO:
					result = getBillCode(tenantId, code, param);
					break;	
				case SCM_TEMPLATE_BILL_NO:
					result = getBillCode(tenantId, code, param);
					break;	
				case SCM_FORECAST_BILL_NO:
					result = getBillCode(tenantId, code, param);
					break;
				case SCM_ORDER_SCHEDULE_BILL_NO:
					result = getBillCode(tenantId, code, param);
					break;	
				case SCM_SETTLEMENT_SUPPLY_BILL_NO:
					result = getBillCode(tenantId, code, param);
					break;
                case SCM_DISTRUBUT_BILL_NO:
                    result = getBillCode(tenantId, code, param);
                    break;
                case SCM_DIRECT_DISTRUBUT_BILL_NO:
                    result = getBillCode(tenantId, code, param);
                    break;
                case SCM_DISTRUBUT_OUT_BILL_NO:
                    result = getBillCode(tenantId, code, param);
                    break;
				case SCM_DC_IN_BILL_NO:
					result = getBillCode(tenantId, code, param);
					break;
				case SCM_DC_OUT_BILL_NO:
					result = getBillCode(tenantId, code, param);
					break;
				case SCM_STORE_OUT_BILL_NO:
					result = getBillCode(tenantId, code, param);
					break;	
				case SCM_STORE_IN_BILL_NO:
					result = getBillCode(tenantId, code, param);
					break;
				case SCM_ORDER_DC_BILL_NO:
					result = getBillCode(tenantId, code, param);
					break;
				case HQ_ITEM_PRICECHANGE_CODE:
					result = getBillCode(tenantId, code, param);
					break;
				case BILL_CODE_CREDIT:
					param.put("new", 1);
					param.put("store_id", 0);
					result = getBillCode(tenantId, code, param);
					break;
				case BILL_CODE_CREDIT_LIST:
					param.put("new", 1);
					param.put("store_id", 0);
					result = getBillCode(tenantId, code, param);
					break;	
				case CRM_CARD_TRADING_LIST:
					param.put("new", 1);
					param.put("store_id", 0);
					result = getBillCode(tenantId, code, param);
					break;
				case CRM_CARD_PAYMENT_LIST:
					param.put("new", 1);
					param.put("store_id", 0);
					result = getBillCode(tenantId, code, param);
					break;	
				case BILL_CODE_INCORPORATION_RECHARGE_LIST:
					result = getBillCode(tenantId, code, param);
					break;
				case BILL_CODE_INCORPORATION_PAYMENT_LIST:
					result = getBillCode(tenantId, code, param);
					break;
				case BILL_CODE_CRM_ARTIFICIAL_LIST:
					result = getBillCode(tenantId, code, param);
					break;	
				case CRM_INCORPORATION_GZLIST:
					result = getBillCode(tenantId, code, param);
					break;	
				case CONTRACT_CODE:
					result = getBillCode(tenantId, code, param);
					break;	
				case CC_ORDER_CODE:
					param.put("new", 1);
					result = getBillCode(tenantId, code, param);
					break;	
				case CC_ORDER_FLOW_CODE:
					result = getBillCode(tenantId, code, param);
					break;
				case CC_ORDER_FLOW_CHANNEL_CODE:
					result = getBillCode(tenantId, code, param);
					break;
				case HQ_TIME_PRICE_CODE:
					result = getBillCode(tenantId, code, param);
					break;
				
				case DISCOUNT_CASE_CODE:
					result = getBillCode(tenantId, code, param);
					break;
					
					
					
				/**存入sys_encoding_scheme表中的编码规则*********************/	
				case HQ_UNUSUAL_REASON_CODE:
					result = getEncodingScheme(tenantId, code, param);
					break;
				case CC_UNUSUAL_REASON_CODE:
					result = getEncodingScheme(tenantId, code, param);
					break;
				case ITEM_TASTE_CODE:
					result = getEncodingScheme(tenantId, code, param);
					break;				
				case TABLES_INFO_CODE:
					result = getEncodingScheme(tenantId, code, param);
					break;
				case HQ_ITEM_CODE:
					result = getEncodingScheme(tenantId, code, param);
					break;
				case ORGAN_CODE:
					result = getEncodingScheme(tenantId, code, param);
					break;
				case HQ_ITEM_CLASS_CODE:
					result = getEncodingScheme(tenantId, code, param);
					break;
				case MATECLASS_NUM:
					result = getEncodingScheme(tenantId, code, param);
					break;
				case ORDERING_CYCLE_NUM:
					result = getEncodingScheme(tenantId, code, param);
					break;
				case ORDER_CATEGORY_NUM:
					result = getEncodingScheme(tenantId, code, param);
					break;
				case SUPPCLASS_NUM:
					result = getEncodingScheme(tenantId, code, param);
					break;
			
					
					
				/**存入sys_baseinfo_code_rule表中的编码规则***********************/
				case ORGAN_BRIEF_CODE:
					result = getBaseinfoCode(tenantId, code, param);
					break;
				case DUTY_ORDER_CODE:
					result = getBaseinfoCode(tenantId, code, param);
					break;
				case HQ_INOUT_TYPE_CODE:
					result = getBaseinfoCode(tenantId, code, param);
					break;
				case HQ_KVS_CODE:
					result = getBaseinfoCode(tenantId, code, param);
					break;
				case PAYMENT_WAY_CODE:
					result = getBaseinfoCode(tenantId, code, param);
					break;
				case HQ_ITEM_GROUP_CODE:
					result = getBaseinfoCode(tenantId, code, param);
					break;
				case HQ_ITEM_MENU_CODE:
					result = getBaseinfoCode(tenantId, code, param);
					break;
				case EMPLOYEE_CODE:
					result = getBaseinfoCode(tenantId, code, param);
					break;
				case CRM_CUSTOMER_CODE:
					param.put("new", 1);
					param.put("store_id",0);
					result = getBaseinfoCode(tenantId, code, param);
					break;
				case ITEM_CODE:
					result = getBaseinfoCode(tenantId, code, param);
					break;					
				case WAREHOUSE_NUM:
					result = getBaseinfoCode(tenantId, code, param);
					break;
				case SUPPLIER_NUM:
					result = getBaseinfoCode(tenantId, code, param);
					break;
				case MATERIAL_NUM:
					result = getBaseinfoCode(tenantId, code, param);
					break;
				case BOMGROUP_NUM:
					result = getBaseinfoCode(tenantId, code, param);
					break;
				case CRM_GROUP_CODE:
					result = getBaseinfoCode(tenantId, code, param);
					break;
				case CRM_LEVEL_CODE:
					result = getBaseinfoCode(tenantId, code, param);
					break;
				case INCORPORATION_CODE:
					result = getBaseinfoCode(tenantId, code, param);
					break;
				case HQ_SERVICE_FEE_TYPE_CODE:
					result = getBaseinfoCode(tenantId, code, param);
					break;	
				case DEVICES_CODE:
					result = getBaseinfoCode(tenantId, code, param);
					break;
				case COUPONS_DISH_CODE:
					result = getBaseinfoCode(tenantId, code, param);
					break;
				case COUPONS_DEDUCT_CODE:
					result = getBaseinfoCode(tenantId, code, param);
					break;
				case ORGAN_GROUP_CODE:
					result = getBaseinfoCode(tenantId, code, param);
				case CRM_CARD_CLASS_CODE:
					result = getBaseinfoCode(tenantId, code, param);	
					break;
				case HQ_QUEUE_CODE:
					result = getBaseinfoCode(tenantId, code, param);	
					break;	
				default:
					break;
			}

			return result;
		}

		return null;
	}

	private String getBillCode(String tenantId, Code code, JSONObject param) throws Exception
	{
		synchronized (code)
		{
			String sql = "select * from sys_bill_code_rule where bill_code='" + code.getType() + "'";

			SqlRowSet rs = dao.query(tenantId, sql);

			if (rs != null && rs.next())
			{
				StringBuilder sb = new StringBuilder();

				// int max_length = rs.getInt("max_length");
				String perfix_1 = rs.getString("perfix_1");
				String perfix_2 = rs.getString("perfix_2");
				String perfix_3 = rs.getString("perfix_3");
				int serial_length = rs.getInt("serial_length");

				if (perfix_1 != null)
				{
					if ("dynamic".equalsIgnoreCase(perfix_1))
					{
						sb.append(param.optString(perfix_1));
					}
					else
					{
						sb.append(perfix_1);
					}
				}

				if (perfix_2 != null)
				{
					sb.append(param.optString(perfix_2));
				}

				if (perfix_3 != null)
				{

					JSONObject obj = param.optJSONObject("busi_date");
					if (obj != null)
					{
						Date d = (Date) JSONObject.toBean(obj, Date.class);

						sb.append(DateUtil.format(d, perfix_3));
					}
					else
					{
						sb.append(DateUtil.format(new Date(), perfix_3));
					}
				}

				if (serial_length > 0)
				{
					//堂食账单与外卖账单001 账单号重复
					//表中是否存在账单号
					List<JSONObject> kk = this.dao.query4Json(tenantId,"select current_value from sys_code_values where code_type='"+code.getType()+"' and store_id="+param.optInt("store_id")+" and prefix='"+sb.toString()+"' LIMIT 1");
					if(kk.size()>0)
					{
						String value = getNextValue(tenantId, code.getType(), param, sb.toString());
						sb.append(StringUtils.leftPad(value, serial_length, "0"));
					}else{ //不存在账单 ，并发账单按序列生成账单号
						DefaultTransactionDefinition definition=new DefaultTransactionDefinition();
						definition.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
						definition.setIsolationLevel(TransactionDefinition.ISOLATION_SERIALIZABLE);
						TransactionStatus status = transactionManager.getTransaction(definition);
						try {
							String value = getNextValue(tenantId, code.getType(), param, sb.toString());
							sb.append(StringUtils.leftPad(value, serial_length, "0"));
							transactionManager.commit(status);
						}catch (SQLException e){
							e.printStackTrace();
							transactionManager.rollback(status);
							throw e;
						}
					}
					// String value = codeValue(tenantId, code.getType(),
					// param.getInt("store_id"), sb.toString());
				}

				return sb.toString();
			}

			return null;
		}
	}

	private String getEncodingScheme(String tenantId, Code code, JSONObject param) throws Exception
	{
		String sql = "select * from sys_encoding_scheme where encoding_code='" + code.getType() + "'";

		SqlRowSet rs = dao.query(tenantId, sql);

		if (rs != null && rs.next())
		{
			StringBuilder sb = new StringBuilder();

			String prefix = param.optString("parent_code");

			sb.append(prefix);

			int max_level = rs.getInt("max_level");

			int current_level = param.optInt("current_level");

			if (max_level >= current_level)
			{
				int[] lengthArray = new int[8];

				lengthArray[0] = rs.getInt("one_length");
				lengthArray[1] = rs.getInt("two_length");
				lengthArray[2] = rs.getInt("three_length");
				lengthArray[3] = rs.getInt("four_length");
				lengthArray[4] = rs.getInt("five_length");
				lengthArray[5] = rs.getInt("six_length");
				lengthArray[6] = rs.getInt("seven_length");
				lengthArray[7] = rs.getInt("eight_length");

				String value = getNextValue(tenantId, code.getType(), param, sb.toString());
//				String value = codeValue(tenantId, code.getType(), param.getInt("store_id"), prefix);
				sb.append(StringUtils.leftPad(value, lengthArray[current_level - 1], "0"));

			}

			return sb.toString();
		}

		return null;
	}

	private String getBaseinfoCode(String tenantId, Code code, JSONObject param) throws Exception
	{
		String sql = "select * from sys_baseinfo_code_rule where baseinfo_code='" + code.getType() + "'";

		SqlRowSet rs = dao.query(tenantId, sql);

		if (rs != null && rs.next())
		{
			StringBuilder sb = new StringBuilder();

			String static_prefix_code = rs.getString("static_prefix_code");
			String dyna_prefix_code = rs.getString("dyna_prefix_code");
			int serial_length = rs.getInt("serial_length");

			
			if (static_prefix_code != null)
			{
				sb.append(static_prefix_code);
			}
			
			if (dyna_prefix_code != null)
			{
				sb.append(param.optString("dyna_code"));
			}
			
			if(serial_length > 0)
			{
				String value = getNextValue(tenantId, code.getType(), param, sb.toString());
				// String value = codeValue(tenantId, code.getType(), param.getInt("store_id"), sb.toString());
				
				sb.append(StringUtils.leftPad(value, serial_length, "0"));
			}

			return sb.toString();
		}

		return null;
	}
	
	
	public  String codeValue(String tenantId, final String type,Integer store_id, final String prefix) throws Exception{
	    synchronized(type){
            String oldCode=posCodeDao.getDataValue(tenantId, type, store_id, prefix);
            Long resuLong = 1L+Long.parseLong(oldCode);
            posCodeDao.delete(type);
            posCodeDao.update(""+resuLong,type);
            int res=posCodeDao.updateDB(tenantId,type, store_id, prefix, resuLong.intValue());
            if(res<1){
                posCodeDao.insertDB(tenantId,type, store_id, prefix, resuLong.intValue());
            }
            return resuLong.toString();
        }
	}
	

	private String getNextValue(String tenantId, final String type, final JSONObject param, final String prefix) throws Exception
	{
		
		String sql = "{?=call get_next_value(?,?,?)}";
		int newp = param.optInt("new");
		String mmk = "seqkey_"+tenantId+"_"+type+"_"+param.optInt("store_id");
		Long resuLong = 0L;
		if(newp==1)
		{
//			resuLong = redisTaskDao.getKey(tenantId,mmk);
			//sql = "{?=call new_get_nextvalue(?,?,?)}";
			Integer result =0;
			List<JSONObject> kk = this.dao.query4Json(tenantId,"select current_value from sys_code_values where code_type='"+type+"' and store_id="+param.optInt("store_id")+" and prefix='"+prefix+"' LIMIT 1");
			if(kk.size()>0)
			{
				result = kk.get(0).optInt("current_value");
			}
			Long xxLong = resuLong +result;
			return xxLong.toString();
		}

		Integer result = dao.getJdbcTemplate(tenantId).execute(sql, new CallableStatementCallback<Integer>()
		{

			@Override
			public Integer doInCallableStatement(CallableStatement callablestatement) throws SQLException, DataAccessException
			{
				callablestatement.registerOutParameter(1, java.sql.Types.INTEGER);
				callablestatement.setInt(2, param.optInt("store_id"));
				callablestatement.setString(3, type);
				callablestatement.setString(4, prefix);

				callablestatement.execute();
				return callablestatement.getInt(1);
			}
		});

		if(result != null)
		{
			Long xxLong = resuLong +result;
			return xxLong.toString();
		}
		
		return "";
	}

	@Override
	public String getCodeValue(String tenantId, int store_id, int length, String prefix, String type) throws Exception
	{
		final int store_idf = store_id;
		final String fprefix = prefix;
		final String ftype = type;
		String sql = "{?=call get_next_value(?,?,?)}";

		Integer result = dao.getJdbcTemplate(tenantId).execute(sql, new CallableStatementCallback<Integer>()
		{

			@Override
			public Integer doInCallableStatement(CallableStatement callablestatement) throws SQLException, DataAccessException
			{
				callablestatement.registerOutParameter(1, java.sql.Types.INTEGER);
				callablestatement.setInt(2, store_idf);
				callablestatement.setString(3, ftype);
				callablestatement.setString(4, fprefix);

				callablestatement.execute();
				return callablestatement.getInt(1);
			}
		});

		if(result != null)
		{
			if(length>0)
			{
				return StringUtils.leftPad(result.toString(), length, "0");
			}
			return result.toString();
		}
		
		return "";
	}

/*	private final AtomicInteger nextVal=new AtomicInteger(0);
    private int compareAndGetNextBillNum(String tenentId,int storeId,Date reportDate,String perfix) throws Exception {

        //查询当前最大订单号
        String currBillNum = null;
        try {
            currBillNum = dao.getJdbcTemplate(tenentId).queryForObject("select bill_num from pos_bill where tenancy_id='" + tenentId + "' and store_id='" + storeId + "' and report_date ='" + reportDate + "' ORDER BY id desc LIMIT 1", String.class);
        } catch (EmptyResultDataAccessException e) {
        }

        if (StringUtils.isEmpty(currBillNum)) {
            nextVal.set(0);
        } else {
            int curr = Integer.valueOf(currBillNum.replace(perfix, ""));
            int val = nextVal.get();

            if (curr != val) {
                nextVal.set(curr);
            }
        }
        return nextVal.incrementAndGet();
    }*/

}
