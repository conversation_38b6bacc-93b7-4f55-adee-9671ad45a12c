package com.tzx.clientorder.wlifeprogram.bo;

import net.sf.json.JSONObject;

/**
 * 调用微生活的接口
 * Created by qingui on 2018-07-24.
 */
public interface WlifeService {
    String NAME = "com.tzx.clientorder.wlifeprogram.bo.WlifeServiceImp";
    
    /**
     * 结账通知
     * @param storeId
     * @param billNum
     * @return
     * @throws Exception
     */
    JSONObject completeOrder(String tenancyId, int storeId, String billNum) throws Exception;

    /**
     * 解锁订单
     * @param billNum
     * @return
     * @throws Exception
     */
    JSONObject unlockOrder(String tenancyId, int storeId, String billNum) throws Exception;

    /**
     * 门店转台
     * @param storeId
     * @param billNum
     * @return
     * @throws Exception
     */
    JSONObject changeTable(String tenancyId, int storeId, String billNum,String targetTableCode) throws Exception;

    /**
     * 门店并台
     * @param storeId
     * @param billNum
     * @return
     * @throws Exception
     */
    JSONObject combineOrder(String tenancyId, int storeId, String billNum,String targetTableCode) throws Exception;

	/**
	 * 门店退单
	 * 
	 * @param storeId
	 * @return
	 * @throws Exception
	 */
	JSONObject orderQuit(String tenancyId, int storeId, String billNum) throws Exception;

	/**
	 * 门店退单
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param outOrderId
	 * @param orderSource
	 * @return
	 * @throws Exception
	 */
	JSONObject orderQuit(String tenancyId, int storeId, String outOrderId, String orderSource) throws Exception;

	/**
	 * 门店退款
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @return
	 * @throws Exception
	 */
	JSONObject orderRefund(String tenancyId, int storeId, String billNum) throws Exception;

    /** 门店退款
     * @param tenancyId
     * @param storeId
     * @param out_order_id
     * @param orderSource
     * @return
     * @throws Exception
     */
    public JSONObject orderRefund(String tenancyId, int storeId, String out_order_id,String orderSource) throws Exception;
    
    /**
     * 上传订单
     * @param tenancyId
     * @param storeId
     * @param billNum
     * @return
     * @throws Exception
     */
    /*JSONObject uploadOrder(String tenancyId, int storeId, String billNum) throws Exception;*/
    
    /** 收银同步菜品接口
     * @param tenancyId
     * @param storeId
     * @param syncType 要同步的数据类型 dish 菜品, dishkind 菜类, all 菜品菜类
     * @param syncForce 是否强制同步数据 1 重置数据（在.net后台所做修改将被覆盖）, 0 同步
     * @throws Exception
     */
    public void synchrodataDish(String tenancyId, int storeId,String syncType,String syncForce) throws Exception;
    
    /**
     * @param tenancyId
     * @param storeId
     * @throws Exception
     */
    public void synchrodataDish(String tenancyId, int storeId) throws Exception;

    public void synchrodataDish(String tenancyId, int storeId,boolean rebuild) throws Exception;
}
