package com.tzx.pos.bo.dto;

import java.io.Serializable;

/**
 * 
 * <AUTHOR> 
 * @日期：2015年5月20日-下午3:41:40
 */

public class PosTableFind implements Serializable
{
	/**
	 * 
	 */
	private static final long	serialVersionUID	= 1L;
	
	private String table_code;
	private String table_name;
	private String billno;
	private String opentabletime;
	private String service_id;
	private String guest;
	private String amt;
	private String state;
	private String table_fw;
	private String seat_counts;
	private String printtimes;
	private String table_property_id;
	private String business_area_id;
	private String lock_pos_num;
	private String lock_opt_num;
	private String payment_state;
	private String stable;
	private String locked_time;
	private String is_locked;
	private String is_exist_dish;
	private String is_combine_table;
	private String combine_table_name;
	private String combine_table_bill_num;

	public String getTable_name()
	{
		return table_name;
	}
	public void setTable_name(String table_name)
	{
		this.table_name = table_name;
	}
	public String getLock_pos_num()
	{
		return lock_pos_num;
	}
	public void setLock_pos_num(String lock_pos_num)
	{
		this.lock_pos_num = lock_pos_num;
	}
	public String getLock_opt_num()
	{
		return lock_opt_num;
	}
	public void setLock_opt_num(String lock_opt_num)
	{
		this.lock_opt_num = lock_opt_num;
	}
	public String getTable_code()
	{
		return table_code;
	}
	public void setTable_code(String table_code)
	{
		this.table_code = table_code;
	}
	public String getBillno()
	{
		return billno;
	}
	public void setBillno(String billno)
	{
		this.billno = billno;
	}
	public String getOpentabletime()
	{
		return opentabletime;
	}
	public void setOpentabletime(String opentabletime)
	{
		this.opentabletime = opentabletime;
	}
	public String getService_id()
	{
		return service_id;
	}
	public void setService_id(String service_id)
	{
		this.service_id = service_id;
	}
	public String getGuest()
	{
		return guest;
	}
	public void setGuest(String guest)
	{
		this.guest = guest;
	}
	public String getAmt()
	{
		return amt;
	}
	public void setAmt(String amt)
	{
		this.amt = amt;
	}
	public String getState()
	{
		return state;
	}
	public void setState(String state)
	{
		this.state = state;
	}
	public String getTable_fw()
	{
		return table_fw;
	}
	public void setTable_fw(String table_fw)
	{
		this.table_fw = table_fw;
	}
	public String getSeat_counts()
	{
		return seat_counts;
	}
	public void setSeat_counts(String seat_counts)
	{
		this.seat_counts = seat_counts;
	}
	public String getPrinttimes()
	{
		return printtimes;
	}
	public void setPrinttimes(String printtimes)
	{
		this.printtimes = printtimes;
	}
	public String getTable_property_id()
	{
		return table_property_id;
	}
	public void setTable_property_id(String table_property_id)
	{
		this.table_property_id = table_property_id;
	}
	public String getBusiness_area_id()
	{
		return business_area_id;
	}
	public void setBusiness_area_id(String business_area_id)
	{
		this.business_area_id = business_area_id;
	}
	public String getPayment_state()
	{
		return payment_state;
	}
	public void setPayment_state(String payment_state)
	{
		this.payment_state = payment_state;
	}
	public String getStable() {
		return stable;
	}
	public void setStable(String stable) {
		this.stable = stable;
	}

	public String getLocked_time() {
		return locked_time;
	}

	public void setLocked_time(String locked_time) {
		this.locked_time = locked_time;
	}

	public String getIs_locked() {
		return is_locked;
	}

	public void setIs_locked(String is_locked) {
		this.is_locked = is_locked;
	}

	public String getIs_exist_dish() {
		return is_exist_dish;
	}

	public void setIs_exist_dish(String is_exist_dish) {
		this.is_exist_dish = is_exist_dish;
	}

	public String getIs_combine_table() {
		return is_combine_table;
	}

	public void setIs_combine_table(String is_combine_table) {
		this.is_combine_table = is_combine_table;
	}

	public String getCombine_table_name() {
		return combine_table_name;
	}

	public void setCombine_table_name(String combine_table_name) {
		this.combine_table_name = combine_table_name;
	}

	public String getCombine_table_bill_num() {
		return combine_table_bill_num;
	}

	public void setCombine_table_bill_num(String combine_table_bill_num) {
		this.combine_table_bill_num = combine_table_bill_num;
	}
}
