package com.tzx.pos.base.dao.imp;

import com.tzx.framework.common.util.dao.impl.GenericDaoImpl;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.dao.ThirdCouponsDao;
import com.tzx.pos.base.util.DateUtil;

import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * @Author: wj
 * @Description:
 * @Date: Created in 2018/1/15 下午5:01
 */

@Repository(ThirdCouponsDao.NAME)
public class ThirdCouponsDaoImp extends GenericDaoImpl implements ThirdCouponsDao {

    private Logger logger	= Logger.getLogger(this.getClass());

    @Override
    public boolean updateThirdCoupons(double coupon_buy_price, double due, double tenancy_assume, double third_assume, double third_fee, String bill_num, String couponCode,  String tenancyId, Integer storeId, String paymentId, int state) throws Exception {

        if (StringUtils.isEmpty(bill_num)){
            logger.warn("参数为空!");
            return false;
        }

        // 请求码券信息成功
        if (state == 1){
            // 更新 pos_bill_payment_coupons 表中的数据
            String updateSql = "update pos_bill_payment_coupons set coupon_buy_price =?, due=?, tenancy_assume=?, third_assume=?, third_fee=?, request_state=?,upload_tag='0' where bill_num=? AND coupons_code=? AND tenancy_id=? AND store_id=?";
            int ret = jdbcTemplate.update(updateSql, new Object[]
                    { coupon_buy_price, due, tenancy_assume, third_assume, third_fee, 1, bill_num, couponCode, tenancyId, storeId});

            // 更新 pos_bill_payment 表
//            String updatePbpSql = "update pos_bill_payment set coupon_buy_price =(select sum(pbpc.coupon_buy_price) FROM pos_bill_payment_coupons pbpc where pos_bill_payment.bill_num=pbpc.bill_num and pos_bill_payment.id||''=pbpc.payment_id), due=(select sum(pbpc.due) FROM pos_bill_payment_coupons pbpc where pos_bill_payment.bill_num=pbpc.bill_num and pos_bill_payment.id||''=pbpc.payment_id), tenancy_assume=(select sum(pbpc.tenancy_assume) FROM pos_bill_payment_coupons pbpc where pos_bill_payment.bill_num=pbpc.bill_num and pos_bill_payment.id||''=pbpc.payment_id), third_assume=(select sum(pbpc.third_assume) FROM pos_bill_payment_coupons pbpc where pos_bill_payment.bill_num=pbpc.bill_num and pos_bill_payment.id||''=pbpc.payment_id), third_fee=(select sum(pbpc.third_fee) FROM pos_bill_payment_coupons pbpc where pos_bill_payment.bill_num=pbpc.bill_num and pos_bill_payment.id||''=pbpc.payment_id) where bill_num=? AND tenancy_id=? AND store_id=?";
//            int retPbp = jdbcTemplate.update(updatePbpSql, new Object[]
//                    { bill_num, tenancyId, storeId});
            
			StringBuffer updatePbpSql = new StringBuffer();
			updatePbpSql.append("update pos_bill_payment bp set coupon_buy_price =pbpc.coupon_buy_price,due=pbpc.due ,tenancy_assume=pbpc.tenancy_assume ,third_assume=pbpc.third_assume,third_fee= pbpc.third_fee,upload_tag='0' ");
			updatePbpSql.append("from (select tenancy_id,store_id,bill_num,payment_id,sum(coupon_buy_price) coupon_buy_price,sum(due) due,sum(tenancy_assume) tenancy_assume,sum(third_assume) third_assume,sum(third_fee) third_fee from pos_bill_payment_coupons group by tenancy_id,store_id,bill_num,payment_id) pbpc ");
			updatePbpSql.append("where bp.tenancy_id=pbpc.tenancy_id and bp.store_id=pbpc.store_id and bp.bill_num=pbpc.bill_num and bp.payment_uid=pbpc.payment_id ");
			updatePbpSql.append("and bp.tenancy_id=? and bp.store_id=? and bp.bill_num=? and bp.type=?");
			int retPbp = jdbcTemplate.update(updatePbpSql.toString(), new Object[]
			{ tenancyId, storeId, bill_num, SysDictionary.PAYMENT_CLASS_MEIDA_COUPONS_PAY });

            // 更新 pos_bill 表
            List<JSONObject> panmentList = selectThirdCouponsFromBillPayment(bill_num,tenancyId, storeId);
            JSONObject object = panmentList.get(0);

            String updateBillSql = "update pos_bill set coupon_buy_price =?, due=?, tenancy_assume=?, third_assume=?, third_fee=?,upload_tag='0' where bill_num=?  AND tenancy_id=? AND store_id=?";
            int retBill = jdbcTemplate.update(updateBillSql, new Object[]
                    { object.optDouble("coupon_buy_price"), object.optDouble("due"), object.optDouble("tenancy_assume"), object.optDouble("third_assume"), object.optDouble("third_fee"), bill_num, tenancyId, storeId});

            return (ret > 0) && (retPbp > 0) && (retBill > 0);
        } else {
            // 更新 pos_bill_payment_coupons 表中的数据
            String updateSql = "update pos_bill_payment_coupons set request_state=? where bill_num=? AND coupons_code=? AND tenancy_id=? AND store_id=?";
            int ret = jdbcTemplate.update(updateSql, new Object[]
                    {2, bill_num, couponCode, tenancyId, storeId});
            return ret > 0;
        }
    }

    @Override
    public List<JSONObject> selectThirdCoupons(String tenancy_id, Integer store_id, String report_date) throws Exception {

		StringBuilder sql = new StringBuilder();
		sql.append("select pbpc.bill_num,pbpc.coupons_code,pbpc.payment_id,pbp.type from pos_bill_payment_coupons pbpc ");
		sql.append("left join pos_bill pb on pbpc.bill_num = pb.bill_num left join pos_bill_payment pbp on pbpc.bill_num = pbp.bill_num and pbpc.payment_id = pbp.payment_uid ");
		sql.append("where pb.bill_property=? and pbp.type = ? and pbpc.request_state=0 and pbp.report_date =? and pbpc.tenancy_id=? and pbpc.store_id=?");

		return query4Json(tenancy_id, sql.toString(), new Object[]
		{ SysDictionary.BILL_PROPERTY_CLOSED, SysDictionary.PAYMENT_CLASS_MEIDA_COUPONS_PAY, DateUtil.parseDate(report_date), tenancy_id, store_id });
    }

    // 根据账单号 查询 pos_bill_payment 表中美团券的合计
    private List<JSONObject> selectThirdCouponsFromBillPayment(String bill_num, String tenancy_id, Integer store_id) throws Exception{

        StringBuilder sql = new StringBuilder();
        sql.append("select sum(coalesce(coupon_buy_price,0)) as coupon_buy_price, sum(coalesce(due,0)) as due, sum(coalesce(tenancy_assume,0)) as tenancy_assume, sum(coalesce(third_assume,0)) as third_assume, sum(coalesce(third_fee,0)) as third_fee FROM pos_bill_payment WHERE ").append("bill_num ='").append(bill_num).append("' AND tenancy_id='").append(tenancy_id).append("' AND store_id='").append(store_id).append("'");
        return query4Json(tenancy_id, sql.toString());
    }


}
