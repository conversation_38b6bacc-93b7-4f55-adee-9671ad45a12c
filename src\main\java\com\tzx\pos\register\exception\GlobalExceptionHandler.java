package com.tzx.pos.register.exception;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.Date;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.HandlerExceptionResolver;
import org.springframework.web.servlet.ModelAndView;

import com.tzx.framework.common.util.JsonDateValueProcessor;

import net.sf.json.JSONObject;
import net.sf.json.JsonConfig;

/**
 * 全局异常处理器
 * 
 */
@Controller("globalExceptionHandler")
public class GlobalExceptionHandler implements HandlerExceptionResolver {

	private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

	@Override
	public ModelAndView resolveException(HttpServletRequest request, HttpServletResponse response, Object handler,
			Exception ex) {

		ModelAndView empty = new ModelAndView();
		Throwable deepestException = deepestException(ex);
		ExceptionLog log = null;
		if (ExceptionLog.class.isAssignableFrom(handler.getClass())) {
			String className = handler.getClass().getName();
			log = new ExceptionLog();
			log.setClassName(className);
			// log.setMethodName(methodName);
			log.setExceptionType(deepestException.getClass().getSimpleName());
			log.setExceptionMsg(deepestException.getMessage()); // 异常详细信息
			log.setAddtime(new Date());
			JsonConfig config = new JsonConfig();
			JsonDateValueProcessor jsonValueProcessor = new JsonDateValueProcessor();
			config.registerJsonValueProcessor(Date.class, jsonValueProcessor);
			logger.error("nanxuesong" + JSONObject.fromObject(log, config));
			PrintWriter out = null;
			JSONObject responseJson = new JSONObject();
			responseJson.put("msg", "系统异常");
			responseJson.put("code", "-99");
			responseJson.put("data", JSONObject.fromObject(log, config));
			try {
				out = response.getWriter();
				out.print(responseJson.toString());
				out.flush();

			} catch (IOException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}

		}

		empty.clear();

		return empty;
	}

	/**
	 * 获取最原始的异常出处，即最初抛出异常的地方
	 */
	private Throwable deepestException(Throwable e) {
		Throwable tmp = e;
		int breakPoint = 0;
		while (tmp.getCause() != null) {
			if (tmp.equals(tmp.getCause())) {
				break;
			}
			tmp = tmp.getCause();
			breakPoint++;
			if (breakPoint > 1000) {
				break;
			}
		}
		return tmp;
	}

	public static void main(String[] args) {

	}
}