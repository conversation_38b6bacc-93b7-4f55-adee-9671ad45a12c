package com.tzx.log.preserve;

import com.tzx.framework.common.entity.Data;
import com.tzx.pos.base.util.ReqDataUtil;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.datasource.DriverManagerDataSource;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.io.File;
import java.io.IOException;
import java.sql.*;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Map;

/**
 * <AUTHOR> on 2018-10-29
 */
@Component
public class SqliteLogSave extends AbstractLogSave {

    private static final Logger LOG = LoggerFactory.getLogger(AbstractLogSave.class);

    static final String dbFile=System.getProperty("catalina.base") + "/log.db";

    static final String driverClassName = "org.sqlite.JDBC";
    static final String url = "jdbc:sqlite:" + dbFile;
    static final String username = "";
    static final String password = "";

    static final String createTableStatement =
            "CREATE TABLE IF NOT EXISTS operate_log_raw (" +
                    "id  INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL," +
                    "bill_num  TEXT," +
                    "type  TEXT," +
                    "request_url  TEXT," +
                    "remote_addr  TEXT," +
                    "request  TEXT," +
                    "response  TEXT," +
                    "start_time  TEXT," +
                    "end_time  TEXT," +
                    "elapsed_time  TEXT," +
                    "exception  TEXT" +
                    ")";


    static final String newEntryStatement =
            "INSERT INTO operate_log_raw (" +
                    " bill_num,type,request_url,remote_addr,request,response,start_time,end_time,elapsed_time,exception " +
                    ")VALUES(?,?,?,?,?,?,?,?,?,?)";

    static final String checkStatement ="select 1 from operate_log_raw LIMIT 1";

    static final DriverManagerDataSource dataSource =new DriverManagerDataSource();
       static {
        dataSource.setDriverClassName(driverClassName);
        dataSource.setUrl(url);
        dataSource.setUsername(username);
        dataSource.setPassword(password);
    };

    static Connection connection=null;

    public void doClean() {
        Connection conn=null;
        try {
            String sql="DELETE FROM operate_log_raw  where start_time<date('now','start of month','-? month')";
            conn=dataSource.getConnection();
            PreparedStatement preparedStatement= conn.prepareStatement(sql);
            preparedStatement.setInt(1, Integer.parseInt(this.keepMonth));
            preparedStatement.execute();
        } catch (SQLException e) {
            LOG.error("logdb清理失败",e);
        }finally {
            if(null!=conn){
                try {
                    conn.close();
                    conn=null;
                } catch (SQLException e) {
                }
            }
        }
    }

    @Override
    protected synchronized void doSave(OperateLogger.Entry entry) throws SQLException {

        Data param = entry.getParam();
        Map<String, Object> map = ReqDataUtil.getDataMap(param);

        String billNum = map.containsKey("bill_num") ? map.get("bill_num").toString() : null;
        String type = param.getType().toString();
        String requestUrl = entry.getRequestUrl();
        String requestAddr= entry.getRemoteAddr();
        String request = param.toString();
        String response = entry.getResult().toString();

        String startTime = currentTimeMillisToDate(entry.getStartTime());
        String endTime = currentTimeMillisToDate(entry.getEndTime());
        String elapsedTime = String.valueOf(entry.getResponseTime()) + "ms";
        String exception = entry.getException() == null ? null : entry.getException().toString();

        Connection connection=getConnection();
        PreparedStatement preparedStatement=null;

        try {
            preparedStatement = connection.prepareStatement(newEntryStatement);
        }catch (SQLException e){
            if (e.getMessage().contains("no such table")) {
                Statement statement = connection.createStatement();
                statement.execute(createTableStatement);
            }else {
                throw e;
            }
        }
        preparedStatement.setString(1, billNum);
        preparedStatement.setString(2, type);
        preparedStatement.setString(3, requestUrl);
        preparedStatement.setString(4, requestAddr);
        preparedStatement.setString(5, request);
        preparedStatement.setString(6, response);
        preparedStatement.setString(7, startTime);
        preparedStatement.setString(8, endTime);
        preparedStatement.setString(9, elapsedTime);
        preparedStatement.setString(10, exception);

        preparedStatement.execute();
    }

    private Connection getConnection() throws SQLException {

        if (null == connection||connection.isClosed()) {
            connection = dataSource.getConnection();
        }

        if (checkIsHeath(connection)) {
            return connection;

        }
        // 修复数据库
        File file = new File(dbFile);
        if (file.exists() && file.isFile()) {
            try {
                FileUtils.moveFile(file, new File(dbFile + "_" + System.currentTimeMillis() + "_broken"));
                connection = dataSource.getConnection();
            } catch (IOException e) {
                LOG.error("无法记录日志，请注销或重启操作系统", e);
            }
        }
        return connection;

    }

    private boolean checkIsHeath(Connection connection) {
        try {
            Statement statement = connection.createStatement();
            statement.execute(checkStatement);
            return true;
        } catch (SQLException e) {
            destoryConnection();
            LOG.error("数据库损坏", e);
        }
        return false;
    }


    private String currentTimeMillisToDate(long currentTimeMillis) {
        Date date = new Date(currentTimeMillis);
        DateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss:SSS");
        return format.format(date);
    }

    @PreDestroy
    public void destoryConnection() {
        if (null != connection) {
            try {
            } finally {
                try {
                    if (null != connection) {
                        connection.close();
                        connection = null;
                    }
                } catch (SQLException e) {
                }
            }

        }
    }
}



