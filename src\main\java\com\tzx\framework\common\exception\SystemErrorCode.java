package com.tzx.framework.common.exception;

/**
 * 系统错误，编码10000开始
 * 
 * <AUTHOR>
 * 
 */
public enum SystemErrorCode implements ErrorCode
{

	/**
	 * 系统内部错误
	 */
	SYSTEM_ERROR(10000),

	/**
	 * 校验重复错误
	 */
	NAME_REPEAT_ERROR(10001),

	/**
	 * 没有默认岗位
	 */
	NOT_DEFAULT_POST_ERROR(10002),
	/**
	 * 用户已过期
	 */
	USER_VALIDATE_ERROR(10003),
	/**
	 * 用户停用
	 */
	USER_DISABLE_ERROR(10004),
	/**
	 * 修改admin密码
	 */
	UPDATE_ADMIN_ERROR(10005),
	/**
	 * 原始密码错误
	 */
	ORIG_PASSWORD_ERROR(10006),
	/**
	 * 初始化用户信息错误
	 */
	INIT_USER_INFO_ERROR(10007),
	/**
	 * 用户名不存在
	 */
	NOT_USER_ERROR(10008),
	/**
	 * 用户名或密码错误
	 */
	USER_PASSWORD_ERROR(10009),
	/**
	 * 登录认证错误，请重试
	 */
	SHIRO_LOGIN_FAILURE(10010),
	/**
	 * 该用户还没有任何模块操作权限
	 */
	NOT_MODULE_ERROR(10011),
	/**
	 * 该日期营业额预估，已存在
	 */
	ESTIMATE_EXIST(10012),
	/**
	 * 该机构没有设置通信参数
	 */
	NOT_COMM_PARAM(10013),

	/**
	 * 执行P_SCM_BILL_DISPOSE_ZL过程出现错误
	 */
	CALL_BILL_DISPOSE_ZL_ERROR(10014),

	/**
	 * 物品冻结
	 */
	GOODS_FREEZE(10015),

	/**
	 * 下发数据错误
	 */
	MQ_COMMUNICATION_ERROR(10016),

	/**
	 * 该用户所属机构没有分配物流，或者没启用物流
	 */
	NOT_START_ORGAN(10017),
	/**
	 * 没有做首次零用金录入
	 */
	NO_CASH_STORE(10018),
	/**
	 * 支出超过当前现金
	 */
	NO_CASH_STORE_COUNT(10019),

	/**
	 * 发送订货单出现错误
	 */
	SEND_ORDER_ERROR(10020),

	/**
	 * 执行过程出现错误
	 */
	CALL_PROCEDURE_ERROR(10021),
	/**
	 * 时薪月薪转换删除失败，已经上传
	 */
	CHANGE_IS_UPLOAD(10022),

	/**
	 * 当前日期已统计
	 */
	ALREADY_STATISTICS_ERROR(10023),

	/**
	 * 当前日期已盘点
	 */
	ALREADY_TAKING_ERROR(10024),

	/**
	 * 该日期还未打样
	 */
	NOT_CLOSING_ERROR(10025),

	/**
	 * 该日期还未审核考勤
	 */
	NOT_ATTENDANCE_ERROR(10026),

	/**
	 * 已过订货时间，不允许订货
	 */
	ORDER_TIME_OUT_ERROR(10027),

	/**
	 * 该日期还未做销售分析
	 */
	NOT_SALES_RECORD_ERROR(10028),

	/**
	 * 物品库存不足
	 */
	NOT_GOODS_STOCK_ERROR(10029),

	/**
	 * 检测数据NULL错误
	 */
	DATA_NULL_ERROR(10030),

	/**
	 * 营业日期大于当前结束日期，不允许删除
	 */
	ORGAN_STOP_BUSI_ERROR(10031),

	/**
	 * 双方机构营业日期不一致
	 */
	PEER_ALREADY_STATISTICS_ERROR(10032),

	/**
	 * 录入金额和当日现金不符，请检查
	 */
	ENTERING_MONEY_ERROR(10033),

	/**
	 * 当前营业日期已经录入
	 */
	OPERATION_INFO_ERROR(10034),

	/**
	 * 还未录入存款与支票，请检查
	 */
	NOT_CASH_CHEQUE_DETAIL_ERROR(10035),

	/**
	 * 验证码输入错误
	 */
	AUTH_CODE_ERROR(10035),

	/**
	 * 已存在日结记录，不允许此操作
	 */
	EXIST_ORGAN_DAY_RECORD_ERROR(10036),

	/**
	 * 已录入存款与支票，不允许此操作
	 */
	EXIST_CASH_CHEQUE_DETAIL_ERROR(10037),

	/**
	 * 调入机构已盘点
	 */
	IN_ALREADY_TAKING_ERROR(10038),

	/**
	 * 请先导入MIS期初数据核对,然后进行日结
	 */
	NOT_BEGIN_ERROR(10039),

	/**
	 * 该日期子店还未打样
	 */
	CHILD_NOT_CLOSING_ERROR(10040),

	/**
	 * 没有原订货单信息
	 */
	NOT_UP_NC_NO_ERROR(10041),

	/**
	 * 已存在该类型盘点单
	 */
	STOCK_EXIST_ERROR(10042),

	/**
	 * 还未进行数据分析，不允许盘点
	 */
	NOT_DATA_ANALYSIS_ERROR(10043),

	/**
	 * {0}没有设置标准单价
	 */
	NOT_GOODS_PRICE_ERROR(10044),

	/**
	 * 没有可用的流水码
	 */
	NOT_USABLE_CODE(10045),

	/**
	 * 还未进行盘点，不允许日结
	 */
	NOT_STOCK_ERROR(10046),

	/**
	 * 现金日报不平衡{0}
	 */
	CASH_BALANCE_ERROR(10047),

	/**
	 * 没有权限
	 */
	NOT_PRESSION_ERROR(10048),
	/**
	 * 桌位不存在
	 */
	NOT_TABLE_ERROR(10049),
	/**
	 * 桌位已占用
	 */
	TABLE_USE_ERROR(10050),
	/**
	 * 存储过程调用异常
	 */
	PROCEDURE_RUN_ERROR(10051),
	/**
	 * 参数异常
	 */
	PARAM_INPUT_ERROR(10052),
	/**
	 * 桌位未占用
	 */
	TABLE_NOT_USE_ERROR(10053),

	/**
	 * 账单不存在
	 */
	NOT_BILLNO(10054),

	/**
	 * 账单被锁定
	 */
	BILLNO_LOCK(10055),

	/**
	 * 服务费用不存在
	 */
	NOT_SERVICEFEE(10056),

	/**
	 * * RWID不能为空
	 */
	RWID_NOT_NULL(10057),

	/**
	 * 不能超过折让上限
	 */
	NOT_UPPER_LIMIT(10058),

	/**
	 * 折扣率不能小于最低折扣率
	 */
	NOT_FLOOR_DISCOUNT(10059),

	/**
	 * 奉送不能超出最大奉送金额
	 */
	NOT_UPPER_COMPLIMENTARY(10060),

	/**
	 * 该机构不存在
	 */
	NOT_EXISTS_TENANT(10061),
	/**
	 * 数据不存在
	 */
	NO_DATA_EXISTS(10062),
	/**
	 * 连接数据库超时
	 */
	CONNECT_DATABASE_TIMEOUT(10063),
	;

	private final int	number;

	private SystemErrorCode(int number)
	{
		this.number = number;
	}

	public int getNumber()
	{
		return number;
	}

	@Override
	public String getMessage()
	{
		return "";
	}

}
