package com.tzx.framework.common.util;

import java.io.File;
import java.io.IOException;
import java.util.List;

import net.sf.json.JSONObject;

import org.apache.log4j.Logger;
import org.dom4j.Attribute;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.springframework.core.io.Resource;

import com.tzx.framework.common.util.SpringConext;


public class XmlUtils {
	
	private static final Logger logger = Logger
			.getLogger(XmlUtils.class);
	
	
	/**
	 * 把没有根节点的xml换成json， 只适合单节点
	 * @param xmlParam
	 * @return
	 * @throws DocumentException
	 */
	public static JSONObject xml2json(String xmlParam) throws DocumentException {
		Element parse = XmlUtils.parse(xmlParam);
		List<Object> content = parse.content();

		JSONObject returnjson = new JSONObject();
		for(Object obj:content){
			Element el = (Element) obj;
			String textTrim2 = el.getTextTrim();
			String name = el.getName();
			returnjson.put(name, textTrim2);
		}
		return returnjson;
	}
	
	
	public static Element getAopConfig() throws IOException, DocumentException{
		Resource resource = SpringConext.getApplicationContext().getResource("classpath:/log/aopControllerLogs_config.xml");
		File file = resource.getFile();
		Element root = XmlUtils.load(file);
		return root;
	}
	


	/**
	 * 将字符串读入dom
	 * @param xmlstr
	 * @return
	 * @throws DocumentException
	 */
	public static Element parse(String xmlstr) throws DocumentException{
		Document document = DocumentHelper.parseText(xmlstr); 
		Element root = document.getRootElement();
		return root;
	}
	
	/**
	 * 将xml文件中的内容读取到内存中，并返回第一个Element
	 * 
	 * @param file
	 * @return
	 * @throws DocumentException
	 */
	public static Element load(File file) throws DocumentException {
		SAXReader reader = new SAXReader();
		Document document = reader.read(file);
		Element root = document.getRootElement();
		String asXML = root.asXML();
		//logger.info(asXML);
		return root;
	}

	/**
	 * 获取element下指定名称的element
	 * @param element
	 * @param names  一层一层向下传 
	 * 				例如：<a><b></b></a>
	 * 				获取b元素传递的参数为    "a","b"
	 * 				注：   <a><b></b><b></b></a> 获取的b为第一个b元素
	 * @return
	 */
	public static Element getElement(Element element, String... names) {
		for(String name:names){
			element = element.element(name);
		}
		return element;
	}
	
	/**
	 * 递归遍历Xml中各个节点，存在json中
	 * @param root
	 * @param json
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static JSONObject listElements(Element root,JSONObject json){
		List<Element> elements = root.elements();
		//遍历所有节点
		for(int i=0;elements!=null && i<elements.size();i++){
			//取出第一个节点
			Element element = elements.get(i);
			//获取tag为name属性
			Attribute nameAttr = element.attribute("name");
			//获取tag为value的属性
			Attribute valueAttr = element.attribute("value");
			if(nameAttr!=null) {
				String name = nameAttr.getText();
				if(valueAttr!=null) {
					String value = valueAttr.getText();
					json.put(name, value);
					//logger.info(name + "@" + value);
				} else {
					JSONObject json2 = new JSONObject();
					//logger.info(name + ">>>");
					listElements(element, json2);
					json.put(name, json2);
				}
			}
		}
		return json;
	}
	
	public static void main(String[] args) throws DocumentException, Exception {
//		Element ee = getAopConfig();
//		System.out.println(ee.toString());
		String url = "E:\\newworkspace\\tzxsaas0525\\resource\\log\\aopControllerLogs_config.xml";
		File file = new File(url);
		Element load = load(file);
		JSONObject json = new JSONObject();
		listElements(load, json);
		System.out.println(json.toString());
		
	}
}
