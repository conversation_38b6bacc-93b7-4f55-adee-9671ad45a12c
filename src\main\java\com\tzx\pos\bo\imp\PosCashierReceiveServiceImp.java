package com.tzx.pos.bo.imp;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.log4j.Logger;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;

import com.tzx.base.entity.PosCashierReceiveLogEntity;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.entity.Pagination;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.Tools;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.base.util.ReqDataUtil;
import com.tzx.pos.bo.PosCashierReceiveService;
import com.tzx.pos.po.springjdbc.dao.PosCashierReceiveDao;

import net.sf.json.JSONObject;

@Service(PosCashierReceiveService.NAME)
public class PosCashierReceiveServiceImp extends PosBaseServiceImp implements PosCashierReceiveService
{
	private static final Logger logger = Logger.getLogger(PosCashierReceiveService.class);

	@Resource(name = PosCashierReceiveDao.NAME)
	private PosCashierReceiveDao				posDao;
	
	@Override
	public List<JSONObject> totalCashierReceive(String tenancyId, int storeId, List<?> param, Pagination pagination) throws Exception
	{
		if (param == null || param.size() <= 0 || param.isEmpty())
		{
			throw new SystemException(PosErrorCode.PARAM_ERROR);
		}
		JSONObject paraJson = JSONObject.fromObject(param.get(0));
		String reportDate = ParamUtil.getDateStringValue(paraJson, "report_date");
		Timestamp currentTime = DateUtil.currentTimestamp();

		//统计pos消费
		StringBuilder cashierRcvSql = new StringBuilder();
		cashierRcvSql.append(" select bl.service_type,sdst.class_item service_type_name,'"+String.valueOf(currentTime.getTime())+"' as query_time,bl.report_date,bl.shift_id,bl.waiter_id,em.name as waiter_name,du.shift_name,bl.bill_amount,coalesce(pcrl.receive_amount,0) as receive_amount,coalesce(coalesce(bl.bill_amount,0)-coalesce(pcrl.receive_amount,0),0) as difference_amount");
		cashierRcvSql.append(" from ( ");
		cashierRcvSql.append("       select cast('"+SysDictionary.SERVICE_TYPE_CONSUME+"' as text) as service_type,bi.tenancy_id,bi.store_id,bi.report_date,bi.shift_id,cast(bi.cashier_num as int) as waiter_id,coalesce(sum(pbp.currency_amount),0) AS bill_amount");
		cashierRcvSql.append("       from pos_bill_payment pbp left join pos_bill bi on pbp.tenancy_id=bi.tenancy_id and pbp.store_id=bi.store_id and pbp.bill_num=bi.bill_num");
		cashierRcvSql.append("       left join hq_devices hd on bi.tenancy_id = hd.tenancy_id and bi.store_id = hd.store_id and bi.pos_num = hd.devices_code where (hd.show_type = 'YD' or hd.show_type = 'PAD') ");
		cashierRcvSql.append("       group by bi.tenancy_id,bi.store_id,bi.report_date,bi.shift_id,bi.cashier_num");
		cashierRcvSql.append("       union all");
		cashierRcvSql.append("       select cast('"+SysDictionary.SERVICE_TYPE_RECHARGE+"' as text) as service_type,cz.tenancy_id,cz.store_id,cz.business_date,cz.shift_id,cz.operator_id as waiter_id,coalesce(sum(cz.main_trading),0) AS bill_amount");
		cashierRcvSql.append("       from (");
		cashierRcvSql.append("              select tl.tenancy_id,tl.store_id,tl.business_date,tl.shift_id,tl.operator_id,pl.third_bill_code,pl.bill_code,tl.main_trading,pl.pay_money,pl.payment_id");
		cashierRcvSql.append("              from crm_card_payment_list pl left join crm_card_trading_list tl on pl.tenancy_id=tl.tenancy_id and pl.third_bill_code=tl.third_bill_code and tl.operat_type in ('"+SysDictionary.OPERAT_TYPE_CZ+"','"+SysDictionary.OPERAT_TYPE_FCZ+"')");
		cashierRcvSql.append("              left join pos_opt_state ot on tl.tenancy_id=ot.tenancy_id and tl.store_id=ot.store_id and tl.business_date=ot.report_date and cast(tl.operator_id as text) =ot.opt_num and ot.content='"+SysDictionary.OPT_STATE_KSSY+"' where ot.id is null");
		cashierRcvSql.append("              group by tl.tenancy_id,tl.store_id,tl.business_date,tl.shift_id,tl.operator_id,pl.third_bill_code,pl.bill_code,tl.main_trading,pl.pay_money,pl.payment_id");
		cashierRcvSql.append("       ) cz group by cz.tenancy_id,cz.store_id,cz.business_date,cz.shift_id,cz.operator_id");
		cashierRcvSql.append(" ) as bl");
		cashierRcvSql.append(" left join ( "); 
		cashierRcvSql.append("       select pbp.tenancy_id,pbp.store_id,pbp.report_date,pbp.pay_shift_id,pbp.waiter_id,sum(pbp.amount) as receive_amount,pbp.service_type");
		cashierRcvSql.append("       from pos_cashier_receive_log pbp group by pbp.tenancy_id,pbp.store_id,pbp.report_date,pbp.pay_shift_id,pbp.waiter_id,pbp.service_type");
		cashierRcvSql.append(" ) as pcrl on bl.tenancy_id = pcrl.tenancy_id and bl.store_id = pcrl.store_id and bl.report_date = pcrl.report_date and bl.shift_id = pcrl.pay_shift_id and bl.waiter_id = pcrl.waiter_id and bl.service_type = pcrl.service_type");
		cashierRcvSql.append(" left join employee em  on bl.tenancy_id = em.tenancy_id and bl.store_id = em.store_id and bl.waiter_id = em.id");
		cashierRcvSql.append(" left join (select dor.tenancy_id,dor.id as shift_id,sd.class_item as shift_name from duty_order as dor left join sys_dictionary sd on  dor.tenancy_id = sd.tenancy_id and dor.name = sd.class_item_code and sd.class_identifier_code ='duty') du on bl.tenancy_id = du.tenancy_id and bl.shift_id = du.shift_id");
		cashierRcvSql.append(" left join sys_dictionary sdst on bl.tenancy_id = sdst.tenancy_id and bl.service_type = sdst.class_item_code and sdst.class_identifier_code='service_type'");
		cashierRcvSql.append(" where bl.tenancy_id = '").append(tenancyId).append("' and bl.store_id = '").append(storeId).append("' and bl.report_date='").append(reportDate).append("'");
		List<JSONObject> receivelist = posDao.query4Json(tenancyId, cashierRcvSql.toString());
		
		JSONObject result = new JSONObject();
		result.put("receivelist", receivelist);

		List<JSONObject> resultList = new ArrayList<JSONObject>();
		resultList.add(result);

		return resultList;
	}

	@Override
	public List<JSONObject> cashierReceiveDetails(String tenancyId, int storeId, List<?> param, Pagination pagination) throws Exception
	{
		if (param == null || param.size() <= 0 || param.isEmpty())
		{
			throw new SystemException(PosErrorCode.PARAM_ERROR);
		}
		JSONObject paraJson = JSONObject.fromObject(param.get(0));
		Date reportDate = DateUtil.parseDate(ParamUtil.getDateStringValue(paraJson, "report_date"));
		String optNum = ParamUtil.getStringValueByObject(paraJson, "opt_num");
		Integer shiftId = ParamUtil.getIntegerValueByObject(paraJson, "shift_id");
		String serviceType = ParamUtil.getStringValueByObject(paraJson, "service_type");
		Long queryTimes = paraJson.optLong("query_time");

		String queryTimeStr = DateUtil.getNowDateYYDDMMHHMMSS();
		if(Tools.hv(queryTimes))
		{
			queryTimeStr = DateUtil.format(new Timestamp(queryTimes));
		}
		
		StringBuilder cashierRcvSql = new StringBuilder();
		if (SysDictionary.SERVICE_TYPE_RECHARGE.equals(serviceType))
		{
			//会员充值
			cashierRcvSql.append(" select bl.service_type,cast('"+queryTimes+"'as text)as query_time,bl.report_date,bl.shift_id,du.shift_name,bl.waiter_id,em.name as waiter_name,bl.jzid,pw.payment_name,bl.bill_amount,coalesce(pcrl.receive_amount,0) receive_amount,(coalesce(bl.bill_amount,0) - coalesce(pcrl.receive_amount, 0)) as difference_amount,bl.unpaid_amount,(coalesce(bl.bill_amount,0) - coalesce(pcrl.receive_amount, 0)-coalesce(bl.unpaid_amount, 0)) as paid_amount");
			cashierRcvSql.append(" from (   ");
			cashierRcvSql.append("       select cast('"+SysDictionary.SERVICE_TYPE_RECHARGE+"' as text) as service_type,cz.tenancy_id,cz.store_id,cz.business_date as report_date,cz.shift_id,cz.operator_id as waiter_id,cz.payment_id as jzid,coalesce(sum(cz.main_trading),0) AS bill_amount,0 as unpaid_amount");
			cashierRcvSql.append("       from (");
			cashierRcvSql.append("              select tl.tenancy_id,tl.store_id,tl.business_date,tl.shift_id,tl.operator_id,pl.third_bill_code,pl.bill_code,tl.main_trading,pl.pay_money,pl.payment_id");
			cashierRcvSql.append("              from crm_card_payment_list pl left join crm_card_trading_list tl on pl.tenancy_id=tl.tenancy_id and pl.third_bill_code=tl.third_bill_code and tl.operat_type in ('"+SysDictionary.OPERAT_TYPE_CZ+"','"+SysDictionary.OPERAT_TYPE_FCZ+"')");
			cashierRcvSql.append("              left join pos_opt_state ot on tl.tenancy_id=ot.tenancy_id and tl.store_id=ot.store_id and tl.business_date=ot.report_date and cast(tl.operator_id as text) =ot.opt_num and ot.content='"+SysDictionary.OPT_STATE_KSSY+"' ");
			cashierRcvSql.append("              where ot.id is null and tl.operate_time<='").append(queryTimeStr).append("'");
			cashierRcvSql.append("              group by tl.tenancy_id,tl.store_id,tl.business_date,tl.shift_id,tl.operator_id,pl.third_bill_code,pl.bill_code,tl.main_trading,pl.pay_money,pl.payment_id");
			cashierRcvSql.append("       ) cz group by cz.tenancy_id,cz.store_id,cz.business_date,cz.shift_id,cz.operator_id,cz.payment_id");
			cashierRcvSql.append(") bl");
			cashierRcvSql.append(" left join (  ");
			cashierRcvSql.append("       select tenancy_id,store_id,report_date,pay_shift_id,waiter_id,payment_id,sum(amount) as receive_amount,bi.service_type from pos_cashier_receive_log bi");
			cashierRcvSql.append("       group by tenancy_id,store_id,report_date,pay_shift_id,waiter_id,payment_id,bi.service_type) as pcrl");
			cashierRcvSql.append(" on bl.tenancy_id = pcrl.tenancy_id and bl.store_id = pcrl.store_id and bl.report_date = pcrl.report_date and bl.shift_id = pcrl.pay_shift_id and bl.waiter_id = pcrl.waiter_id and bl.jzid = pcrl.payment_id and bl.service_type=pcrl.service_type");
			cashierRcvSql.append(" left join employee em on bl.tenancy_id = em.tenancy_id and bl.store_id = em.store_id and bl.waiter_id = em.id");
			cashierRcvSql.append(" left join (select dor.tenancy_id,dor.id as shift_id,sd.class_item as shift_name from duty_order as dor left join sys_dictionary sd on dor.tenancy_id = sd.tenancy_id and dor.name = sd.class_item_code and sd.class_identifier_code ='duty') du on bl.tenancy_id = du.tenancy_id and bl.shift_id = du.shift_id");
			cashierRcvSql.append(" left join (select pw.tenancy_id,pw.id,coalesce(sd.class_item,pw.payment_name1) as payment_name  from payment_way pw left join sys_dictionary sd on pw.payment_name1=sd.class_item_code and sd.class_identifier_code='currency') pw on bl.tenancy_id=pw.tenancy_id and bl.jzid=pw.id");
			cashierRcvSql.append(" where bl.tenancy_id= '").append(tenancyId).append("' and bl.store_id = '").append(storeId).append("' and bl.report_date ='").append(reportDate).append("' and bl.waiter_id='").append(optNum).append("' and bl.shift_id='").append(shiftId).append("'");}
		else
		{
			cashierRcvSql.append(" select bl.service_type,cast('"+queryTimes+"'as text)as query_time,bl.report_date,bl.shift_id,du.shift_name,bl.waiter_id,em.name as waiter_name,bl.jzid,pw.payment_name,bl.bill_amount,coalesce(pcrl.receive_amount,0) receive_amount,(coalesce(bl.bill_amount,0) - coalesce(pcrl.receive_amount, 0)) as difference_amount,bl.unpaid_amount,(coalesce(bl.bill_amount,0) - coalesce(pcrl.receive_amount, 0)-coalesce(bl.unpaid_amount, 0)) as paid_amount");
			cashierRcvSql.append(" from (   ");
			cashierRcvSql.append("       select cast('"+SysDictionary.SERVICE_TYPE_CONSUME+"'as text)as service_type,bi.tenancy_id,bi.store_id,bi.report_date,bi.shift_id,cast(bi.cashier_num as int) as waiter_id,py.jzid,sum(py.currency_amount)as bill_amount,sum(case when bi.bill_property='"+SysDictionary.BILL_PROPERTY_CLOSED+"' then 0 else py.currency_amount end) as unpaid_amount");
			cashierRcvSql.append("       from pos_bill_payment py left join pos_bill bi on py.tenancy_id=bi.tenancy_id and py.store_id=bi.store_id and py.bill_num=bi.bill_num");
			cashierRcvSql.append("       left join hq_devices hd on bi.tenancy_id = hd.tenancy_id and bi.store_id = hd.store_id and bi.pos_num = hd.devices_code");
			cashierRcvSql.append("       where (hd.show_type = 'YD' or hd.show_type = 'PAD') and bi.payment_time<='").append(queryTimeStr).append("'");
			cashierRcvSql.append("       group by bi.tenancy_id,bi.store_id,bi.report_date,bi.shift_id,bi.cashier_num,py.jzid");
			cashierRcvSql.append(") bl");
			cashierRcvSql.append(" left join (  ");
			cashierRcvSql.append("       select tenancy_id,store_id,report_date,pay_shift_id,waiter_id,payment_id,sum(amount) as receive_amount,bi.service_type from pos_cashier_receive_log bi");
			cashierRcvSql.append("       group by tenancy_id,store_id,report_date,pay_shift_id,waiter_id,payment_id,bi.service_type) as pcrl");
			cashierRcvSql.append(" on bl.tenancy_id = pcrl.tenancy_id and bl.store_id = pcrl.store_id and bl.report_date = pcrl.report_date and bl.shift_id = pcrl.pay_shift_id and bl.waiter_id = pcrl.waiter_id and bl.jzid = pcrl.payment_id and bl.service_type=pcrl.service_type");
			cashierRcvSql.append(" left join employee em on bl.tenancy_id = em.tenancy_id and bl.store_id = em.store_id and bl.waiter_id = em.id");
			cashierRcvSql.append(" left join (select dor.tenancy_id,dor.id as shift_id,sd.class_item as shift_name from duty_order as dor left join sys_dictionary sd on dor.tenancy_id = sd.tenancy_id and dor.name = sd.class_item_code and sd.class_identifier_code ='duty') du on bl.tenancy_id = du.tenancy_id and bl.shift_id = du.shift_id");
			cashierRcvSql.append(" left join (select pw.tenancy_id,pw.id,coalesce(sd.class_item,pw.payment_name1) as payment_name  from payment_way pw left join sys_dictionary sd on pw.payment_name1=sd.class_item_code and sd.class_identifier_code='currency') pw on bl.tenancy_id=pw.tenancy_id and bl.jzid=pw.id");
			cashierRcvSql.append(" where bl.tenancy_id= '").append(tenancyId).append("' and bl.store_id = '").append(storeId).append("' and bl.report_date ='").append(reportDate).append("' and bl.waiter_id='").append(optNum).append("' and bl.shift_id='").append(shiftId).append("'");
		}
		List<JSONObject> receivelist = posDao.query4Json(tenancyId, cashierRcvSql.toString());
		JSONObject result = new JSONObject();
		result.put("receivelist", receivelist);
		
		List<JSONObject> resultList = new ArrayList<JSONObject>();
		resultList.add(result);
		
		return resultList;
	}
	
	@Override
	public synchronized void addCashierReceive(String tenancyId, int storeId, List<?> param,JSONObject printJson) throws Exception
	{
		if (param == null || param.size() <= 0 || param.isEmpty())
		{
			throw new SystemException(PosErrorCode.PARAM_ERROR);
		}
		JSONObject paraJson = JSONObject.fromObject(param.get(0));
		if (Tools.isNullOrEmpty(paraJson.opt("report_date")))
		{
			throw new SystemException(PosErrorCode.NOT_NULL_REPORT_DATE);
		}

		String report_date = ParamUtil.getDateStringValue(paraJson, "report_date");
		Date reportDate = DateUtil.parseDate(report_date);
		Integer shiftId = paraJson.optInt("shift_id");
		String operType = paraJson.optString("oper_type");
		Integer waiterId = paraJson.optInt("waiter_id");
		String waiterName = paraJson.optString("waiter_name");
		String deviceNum = paraJson.optString("device_num");
		String cashierId = paraJson.optString("cashier_id");
		String cashierName = paraJson.optString("cashier_name");
		String posNum = paraJson.optString("pos_num");
//		String optNum = paraJson.optString("opt_num");
		double amount = paraJson.optDouble("amount");
		Integer jzid = paraJson.optInt("jzid");
		Integer payShiftId = paraJson.optInt("pay_shift_id");

		String serviceType = ParamUtil.getStringValueByObject(paraJson, "service_type");
		
		Timestamp queryTime = DateUtil.currentTimestamp();
		if (paraJson.containsKey("query_time") && Tools.hv(paraJson.optString("query_time")))
		{
			queryTime = new Timestamp(paraJson.optLong("query_time"));
		}
		
		// 验证是否签到
		posDao.checkKssy(reportDate, cashierId, storeId,shiftId,posNum);

		if (amount <= 0)
		{
			throw SystemException.getInstance(PosErrorCode.AMOUNT_NOT_MORE_ZERO_ERROR);
		}

		if (Tools.isNullOrEmpty(waiterName))
		{
			waiterName = posDao.getEmpNameById(waiterId.toString(), tenancyId, storeId);
		}
		if (Tools.isNullOrEmpty(cashierName))
		{
			cashierName = posDao.getEmpNameById(cashierId.toString(), tenancyId, storeId);
		}
		Timestamp currentTime = DateUtil.currentTimestamp();

		Double receiveAmount = 0d;
		if (SysDictionary.SERVICE_TYPE_RECHARGE.equals(serviceType))
		{
			receiveAmount = posDao.getReceiveAmountByRecharge(tenancyId, storeId, reportDate, waiterId, jzid);

			if (amount > receiveAmount)
			{
				throw SystemException.getInstance(PosErrorCode.RECEIVE_AMOUNT_MORE_PAYMENT_AMOUNT_ERROR);
			}

			PosCashierReceiveLogEntity entity = new PosCashierReceiveLogEntity(tenancyId, storeId, reportDate, shiftId, operType, waiterId, waiterName, deviceNum, amount, Integer.parseInt(cashierId), cashierName, posNum, currentTime, jzid, payShiftId, serviceType);
			posDao.insertCashierReceiveLog(tenancyId, storeId, entity);

			posDao.insertReceiveDetailsByRecharge(tenancyId, storeId, entity, queryTime);
		}
		else
		{
			receiveAmount = posDao.getReceiveAmountByPayment(tenancyId, storeId, reportDate, waiterId, jzid);

			if (amount > receiveAmount)
			{
				throw SystemException.getInstance(PosErrorCode.RECEIVE_AMOUNT_MORE_PAYMENT_AMOUNT_ERROR);
			}

			posDao.insertCashierReceiveLog(tenancyId, storeId, new PosCashierReceiveLogEntity(tenancyId, storeId, reportDate, shiftId, operType, waiterId, waiterName, deviceNum, amount, Integer.parseInt(cashierId), cashierName, posNum, currentTime, jzid, payShiftId, serviceType));
		
//			StringBuilder queryIdSql = new StringBuilder(" select id from pos_cashier_receive_log order by id desc limit 1");
//			SqlRowSet rs = posDao.query4SqlRowSet(queryIdSql.toString(), new Object[] {});
//			Integer receive_id = 0;
//			if (rs.next())
//			{
//				receive_id = rs.getInt("id");
//			}
	//
//			StringBuilder updateCashierRcvDetailsSql = new StringBuilder(" insert into pos_cashier_receive_log_details (tenancy_id,store_id,receive_id,cashier_id,pos_num,waiter_id,bill_num,last_updatetime,upload_tag) " + " select ?,?,?,?,?,?,a.bill_num,?,0 from (select bill_num from pos_bill_payment "
//					+ " where tenancy_id=? and store_id=? and report_date=? and shift_id=? and jzid=? and payment_state='" + SysDictionary.PAYMENT_STATE_PAY_COMPLETE + "' " + "and bill_num not in (select bill_num from pos_cashier_receive_log_details)) a ");
//			posDao.update(updateCashierRcvDetailsSql.toString(), new Object[]
//			{ tenancyId, storeId, receive_id, cashierId, deviceNum, waiterId, currentTime, tenancyId, storeId, reportDate, payShiftId, jzid });
		}

		JSONObject paymentJson = posDao.getPaymentWayByID(tenancyId, storeId, jzid);

		if (null == printJson)
		{
			printJson = new JSONObject();
		}
		printJson.put("cashier_name", cashierName);
		printJson.put("amount", amount);
		printJson.put("waiter_name", waiterName);
		printJson.put("operator", cashierName);
		printJson.put("payment_name", paymentJson.optString("payment_name"));

		printJson.put("print_code", SysDictionary.PRINT_CODE_1303);
		printJson.put("mode", "1");
		printJson.put("pos_num", posNum);
		printJson.put("opt_num", cashierId);
		printJson.put("report_date", report_date);
		printJson.put("printCount", 2);

		// 记录POS日志
		posDao.savePosLog(tenancyId, storeId, posNum, cashierId.toString(), cashierName, shiftId, reportDate, Constant.TITLE, "交款/抽大钞", "收款人：" + cashierId + "，收款机台：" + posNum, "交款人：" + cashierId + "，交款机台：" + posNum + "，交款金额：" + String.format("%.2f", amount));
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public synchronized void addCashierReceiveBatch(String tenancyId, int storeId, Data param, List<JSONObject> printJsonList) throws Exception
	{
		if (param == null)
		{
			throw new SystemException(PosErrorCode.PARAM_ERROR);
		}

		Map<String, Object> map = ReqDataUtil.getDataMap(param);

		String report_date = ParamUtil.getStringValue(map, "report_date", false, null);
		Date reportDate = DateUtil.parseDate(report_date);
		Integer shiftId = ParamUtil.getIntegerValue(map, "shift_id", false, null);
		String posNum = ParamUtil.getStringValue(map, "pos_num", false, null);
		String optNum = ParamUtil.getStringValue(map, "opt_num", false, null);
		Integer cashierId = ParamUtil.getIntegerValue(map, "cashier_id", false, null);
		String cashierName = ParamUtil.getStringValue(map, "cashier_name", false, null);
		String serviceType = ParamUtil.getStringValue(map, "service_type", false, null);
		String operType = ParamUtil.getStringValue(map, "oper_type", false, null);

		Timestamp queryTime = DateUtil.currentTimestamp();
		if (map.containsKey("query_time") && Tools.hv(map.get("query_time")))
		{
			queryTime = new Timestamp(Long.parseLong(String.valueOf(map.get("query_time"))));
		}
		
		List<Map<String, Object>> items = (List<Map<String, Object>>) map.get("item");

		if (Tools.isNullOrEmpty(cashierName))
		{
			cashierName = posDao.getEmpNameById(cashierId.toString(), tenancyId, storeId);
		}

		Timestamp currentTime = DateUtil.currentTimestamp();

		// 验证是否签到
		posDao.checkKssy(reportDate, optNum, storeId,shiftId,posNum);

		StringBuilder newstate = new StringBuilder();
		for (int i = 0; i < items.size(); i++)
		{
			Map<String, Object> detail = (Map<String, Object>) items.get(i);

			double amount = ParamUtil.getDoubleValue(detail, "amount", false, null);
			Integer jzid = ParamUtil.getIntegerValue(detail, "jzid", false, null);
			Integer payShiftId = ParamUtil.getIntegerValue(detail, "pay_shift_id", false, null);
			Integer waiterId = ParamUtil.getIntegerValue(detail, "waiter_id", false, null);
			String waiterName = ParamUtil.getStringValue(detail, "waiter_name", false, null);
			String deviceNum = ParamUtil.getStringValue(map, "device_num", false, null);

			if (Tools.isNullOrEmpty(waiterName))
			{
				waiterName = posDao.getEmpNameById(waiterId.toString(), tenancyId, storeId);
			}

			if (amount <= 0)
			{
				throw SystemException.getInstance(PosErrorCode.AMOUNT_NOT_MORE_ZERO_ERROR);
			}

			if (SysDictionary.SERVICE_TYPE_RECHARGE.equals(serviceType))
			{
				Double receiveAmount = posDao.getReceiveAmountByRecharge(tenancyId, storeId, reportDate, waiterId, jzid);
				if (amount > receiveAmount)
				{
					throw SystemException.getInstance(PosErrorCode.RECEIVE_AMOUNT_MORE_PAYMENT_AMOUNT_ERROR);
				}

				PosCashierReceiveLogEntity receiveEntity = new PosCashierReceiveLogEntity(tenancyId, storeId, reportDate, shiftId, operType, waiterId, waiterName, deviceNum, amount, cashierId, cashierName, posNum, currentTime, jzid, payShiftId, serviceType);
				posDao.insertCashierReceiveLog(tenancyId, storeId, receiveEntity);

				posDao.insertReceiveDetailsByRecharge(tenancyId, storeId, receiveEntity, queryTime);
			}
			else
			{
				Double receiveAmount = posDao.getReceiveAmountByPayment(tenancyId, storeId, reportDate, waiterId, jzid);
				if (amount > receiveAmount)
				{
					throw SystemException.getInstance(PosErrorCode.RECEIVE_AMOUNT_MORE_PAYMENT_AMOUNT_ERROR);
				}

				posDao.insertCashierReceiveLog(tenancyId, storeId, new PosCashierReceiveLogEntity(tenancyId, storeId, reportDate, shiftId, operType, waiterId, waiterName, deviceNum, amount, cashierId, cashierName, posNum, currentTime, jzid, payShiftId, serviceType));

			}

			JSONObject paymentJson = posDao.getPaymentWayByID(tenancyId, storeId, jzid);

			JSONObject printJson = new JSONObject();
			printJson.put("cashier_name", cashierName);
			printJson.put("amount", amount);
			printJson.put("waiter_name", waiterName);
			printJson.put("operator", cashierName);
			printJson.put("payment_name", paymentJson.optString("payment_name"));

			printJson.put("print_code", SysDictionary.PRINT_CODE_1303);
			printJson.put("mode", "1");
			printJson.put("pos_num", posNum);
			printJson.put("opt_num", optNum);
			printJson.put("report_date", report_date);
			printJson.put("printCount", 2);
			printJsonList.add(printJson);

			newstate.append("交款人：" + waiterName + "，支付方式：" + paymentJson.optString("payment_name") + "，交款金额：" + String.format("%.2f", amount));
		}

		// 记录POS日志
		posDao.savePosLog(tenancyId, storeId, posNum, cashierId.toString(), cashierName, shiftId, reportDate, Constant.TITLE, "批量交款", "收款人：" + cashierName + "，收款机台：" + posNum, newstate.toString());
	}

	@Override
	public List<JSONObject> findCashierReceive(String tenancyId, int storeId, List<?> param, Pagination pagination) throws Exception
	{
		if (param == null || param.size() <= 0 || param.isEmpty())
		{
			throw new SystemException(PosErrorCode.PARAM_ERROR);
		}
		
		JSONObject paraJson = JSONObject.fromObject(param.get(0));
		Integer shiftId = paraJson.optInt("shift_id");
		String optNum = paraJson.optString("opt_num");
		String startDate = paraJson.optString("start_date");
		String endDate = paraJson.optString("end_date");
		
		StringBuilder condition = new StringBuilder(" where tenancy_id='" + tenancyId + "' and store_id='" + storeId + "' ");
		if (Tools.hv(shiftId))
		{
			condition.append(" and shift_id='" + shiftId + "' ");
		}
		if (Tools.hv(optNum))
		{
			condition.append(" and cashier_id='" + optNum + "' ");
		}
		if (Tools.hv(startDate))
		{
			condition.append(" and to_char(report_date,'yyyy-MM-dd')>='" + startDate + "' ");
		}
		if (Tools.hv(endDate))
		{
			condition.append(" and to_char(report_date,'yyyy-MM-dd')<='" + endDate + "' ");
		}
		
		StringBuilder querySql = new StringBuilder(" select report_date,shift_id,oper_type,waiter_id,waiter_name,device_num,amount,cashier_id,cashier_name,pos_num,last_updatetime from pos_cashier_receive_log ");
		querySql.append(condition);
		
		int totalCount = posDao.queryForInt("select count(1) as count from (" + querySql.toString() + ") t", new Object[] {});
		
		String orderBy = "waiter_id";
		String ascDesc = "desc";
		String offsetStr=null;
		if (pagination != null && pagination.getPagesize() > 0)
		{
			int pageNo = 1;
			int limit = pagination.getPagesize();
			if ((pagination.getPageno() - 1) > 0)
			{
				pageNo = pagination.getPageno();
			}
			if (!"".equals(pagination.getOrderby())) {
				orderBy = pagination.getOrderby();
			}

			if (pagination.isAsc() == true)
			{
				ascDesc = "asc";
			}

			offsetStr = " limit " + limit + " offset " + ((pageNo - 1) * pagination.getPagesize());
		}
		
		querySql.append(" order by ").append(orderBy).append(" ").append(ascDesc).append(offsetStr);
		
		
		List<JSONObject> receivelist = posDao.query4Json(tenancyId, querySql.toString(), new Object[]{});
		
		pagination.setPageno(pagination.getPageno() > 0 ? pagination.getPageno() : 1);
		pagination.setTotalcount(totalCount);

		if (pagination.getPagesize() == 0)
		{
			pagination.setPagesize(receivelist.size());
		}
		
		StringBuilder totalAmountSql = new StringBuilder(" select coalesce(sum(amount),0) as total_amount from pos_cashier_receive_log ");
		totalAmountSql.append(condition);
		SqlRowSet rst = posDao.query4SqlRowSet(totalAmountSql.toString(), new Object[]{});
		
		JSONObject result =  new JSONObject();
		result.put("receivelist", receivelist);
		if (rst.next())
		{
			result.put("total_amount", rst.getDouble("total_amount"));//交款金额合计
		}
		
		List<JSONObject> resultList = new ArrayList<JSONObject>();
		resultList.add(result);
		return resultList;
	}
	
	@Override
	public void checkReceive(Data param, Data result)  throws Exception
	{
		String tenancyId = param.getTenancy_id();
		Integer storeId = param.getStore_id();

		Map<String, Object> map = ReqDataUtil.getDataMap(param);

		Date reportDate = ParamUtil.getDateValue(map, "report_date", false, null);
		String optNum = ParamUtil.getStringValue(map, "opt_num", false, null);
		String posNum = ParamUtil.getStringValue(map, "pos_num", false, null);
		Integer shift_id = ParamUtil.getIntegerValue(map, "shift_id", false, null);

		// 查询签到时间

		//String sql = new String("select opt_num,last_updatetime from pos_opt_state where opt_num=? and report_date=? and content=? and tag=? and store_id=? and tenancy_id=? order by id desc limit 1");
		String sql = new String("select opt_num,last_updatetime from pos_opt_state where shift_id=? and pos_num=? and report_date=? and content=? and tag=? and store_id=? and tenancy_id=? order by id desc limit 1");
		// 查询签退时间
		//String tcsql = new String("select last_updatetime from pos_opt_state where opt_num=? and report_date=? and content=? and tag=? and store_id=? and tenancy_id=?  order by id desc limit 1");
		String tcsql = new String("select last_updatetime from pos_opt_state where shift_id=? and pos_num=? and report_date=? and content=? and tag=? and store_id=? and tenancy_id=?  order by id desc limit 1");

		Timestamp kssyTime = null;
		Timestamp tcTime = null;
		SqlRowSet rs3 = posDao.query4SqlRowSet(sql, new Object[]
		//{ optNum, reportDate, SysDictionary.OPT_STATE_KSSY, '0', storeId, tenancyId });
		{ shift_id,posNum, reportDate, SysDictionary.OPT_STATE_KSSY, '0', storeId, tenancyId });
		if (rs3.next())
		{
			kssyTime = rs3.getTimestamp("last_updatetime");
		}

		if (kssyTime == null)
		{
			throw new SystemException(PosErrorCode.SHIFT_NOT_SIGNIN_ERROR);
		}
		else
		{
			SqlRowSet rst = posDao.query4SqlRowSet(tcsql, new Object[]
			//{ optNum, reportDate, SysDictionary.OPT_STATE_YYTC, '0', storeId, tenancyId });
			{ shift_id, posNum,reportDate, SysDictionary.OPT_STATE_YYTC, '0', storeId, tenancyId });
			if (rst.next())
			{
				tcTime = rst.getTimestamp("last_updatetime");
			}

			if (tcTime != null)
			{
				throw new SystemException(PosErrorCode.POS_CASHIER_ALREADY_SIGNOUT_ERROR);
			}
		}
		
		if(false == this.checkCashierReceive(tenancyId, storeId, reportDate))
		{
			throw new SystemException(PosErrorCode.CASHIER_RECEIVE_ADD_FAILURE);
		}
		
		result.setCode(Constant.CODE_SUCCESS);
		result.setMsg(Constant.CHECK_RECEIVE_SUCCESS);
	}
	
	@Override
	public boolean checkCashierReceive(String tenantId, Integer storeId, Date reportDate) throws Exception
	{
		String tableMode = posDao.getSysParameter(tenantId, storeId, "ZCDCMS");
		// 自助模式不需要检测交款
		if ("02".equals(tableMode))
		{
			return true;
		}

		Double differenceAmount = posDao.getCashierReceiveDifferenceAmount(tenantId, storeId, reportDate);

		// 交款差额合计>0
		if (0 < differenceAmount)
		{
			return false;
		}
		return true;
	}

    @Override
    public Data cashierLimitVlidate(Data param) throws Exception {
        String tenantId = param.getTenancy_id();
        Integer organId = param.getStore_id();

        Map<String, Object> map = ReqDataUtil.getDataMap(param);

        String reportDate = ParamUtil.getStringValue(map, "report_date", true, PosErrorCode.NOT_NULL_REPORT_DATE);// 报表日期
        String cashierNum = ParamUtil.getStringValue(map, "cashier_num", true, PosErrorCode.NOT_NULL_CASHIER_NUM);

//        StringBuilder condition = new StringBuilder();
//        condition.append("pbp.tenancy_id = '").append(tenantId).append("' and pbp.store_id = ").append(organId).append(" and pbp.report_date='").append(reportDate).append("' ");
//
//        StringBuilder vlidateSql = new StringBuilder();
//        vlidateSql.append(" select em.is_limit_pay,em.pay_limit,coalesce(coalesce(bl.bill_amount,0)-coalesce(pcrl.receive_amount,0),0) as difference_amount");
//        vlidateSql.append(" from (select bi.tenancy_id,bi.store_id,bi.report_date,bi.shift_id,bi.cashier_num as waiter_num,coalesce(sum(pbp.currency_amount),0) AS bill_amount from pos_bill_payment pbp left join pos_bill bi on pbp.tenancy_id=bi.tenancy_id and pbp.store_id=bi.store_id and pbp.bill_num=bi.bill_num left join hq_devices hd on bi.tenancy_id = hd.tenancy_id and bi.store_id = hd.store_id and bi.pos_num = hd.devices_code where ").append(condition).append(" and bi.cashier_num =  '"+cashierNum+"'").append(" and (hd.show_type = 'YD' or hd.show_type = 'PAD') group by bi.tenancy_id,bi.store_id,bi.report_date,bi.shift_id,bi.cashier_num) as bl");
//        vlidateSql.append(" left join (select pbp.tenancy_id,pbp.store_id,pbp.report_date,pbp.pay_shift_id as shift_id,pbp.waiter_id,sum(pbp.amount) as receive_amount from pos_cashier_receive_log pbp where ").append(condition).append(" and pbp.waiter_id = '"+cashierNum+"'").append(" group by pbp.tenancy_id,pbp.store_id,pbp.report_date,pbp.pay_shift_id,pbp.waiter_id) as pcrl on bl.tenancy_id = pcrl.tenancy_id and bl.store_id = pcrl.store_id and bl.report_date = pcrl.report_date and bl.shift_id = pcrl.shift_id and cast(bl.waiter_num as int) = pcrl.waiter_id");
//        vlidateSql.append(" left join employee em on bl.tenancy_id = em.tenancy_id and bl.store_id = em.store_id and cast(bl.waiter_num as int) = em.id");
//        vlidateSql.append(" left join (select doo.tenancy_id,doo.organ_id,dor.id as shift_id,sd.class_item as shift_name from duty_order as dor left join duty_order_of_ogran as doo on dor.tenancy_id = doo.tenancy_id and dor.id = doo.duty_order_id left join sys_dictionary sd on  dor.tenancy_id = sd.tenancy_id and dor.name = sd.class_item_code and sd.class_identifier_code ='duty') du on bl.tenancy_id = du.tenancy_id and bl.store_id = du.organ_id and bl.shift_id = du.shift_id");
        
        StringBuilder vlidateSql = new StringBuilder();
        vlidateSql.append(" select em.is_limit_pay,em.pay_limit,coalesce(coalesce(bl.bill_amount,0)-coalesce(pcrl.receive_amount,0),0) as difference_amount");
        vlidateSql.append(" from ( ");
        vlidateSql.append("    select bl.tenancy_id,bl.store_id,bl.report_date,bl.shift_id,bl.waiter_id,sum(coalesce(bl.bill_amount,0)) as bill_amount from( ");
        vlidateSql.append("       select cast('"+SysDictionary.SERVICE_TYPE_CONSUME+"' as text) as service_type,bi.tenancy_id,bi.store_id,bi.report_date,bi.shift_id,cast(bi.cashier_num as int) as waiter_id,coalesce(sum(pbp.currency_amount),0) AS bill_amount");
        vlidateSql.append("       from pos_bill_payment pbp left join pos_bill bi on pbp.tenancy_id=bi.tenancy_id and pbp.store_id=bi.store_id and pbp.bill_num=bi.bill_num");
        vlidateSql.append("       left join hq_devices hd on bi.tenancy_id = hd.tenancy_id and bi.store_id = hd.store_id and bi.pos_num = hd.devices_code where (hd.show_type = 'YD' or hd.show_type = 'PAD') and  bi.cashier_num ='").append(cashierNum).append("'");
        vlidateSql.append("       group by bi.tenancy_id,bi.store_id,bi.report_date,bi.shift_id,bi.cashier_num");
        vlidateSql.append("       union all");
        vlidateSql.append("       select cast('"+SysDictionary.SERVICE_TYPE_RECHARGE+"' as text) as service_type,cz.tenancy_id,cz.store_id,cz.business_date,cz.shift_id,cz.operator_id as waiter_id,coalesce(sum(cz.main_trading),0) AS bill_amount");
        vlidateSql.append("       from (");
        vlidateSql.append("              select tl.tenancy_id,tl.store_id,tl.business_date,tl.shift_id,tl.operator_id,pl.third_bill_code,pl.bill_code,tl.main_trading,pl.pay_money,pl.payment_id");
        vlidateSql.append("              from crm_card_payment_list pl left join crm_card_trading_list tl on pl.tenancy_id=tl.tenancy_id and pl.third_bill_code=tl.third_bill_code and tl.operat_type in ('"+SysDictionary.OPERAT_TYPE_CZ+"','"+SysDictionary.OPERAT_TYPE_FCZ+"')");
		vlidateSql.append("              left join pos_opt_state ot on tl.tenancy_id=ot.tenancy_id and tl.store_id=ot.store_id and tl.business_date=ot.report_date and cast(tl.operator_id as text) =ot.opt_num and ot.content='"+SysDictionary.OPT_STATE_KSSY+"' where ot.id is null");
		vlidateSql.append("              group by tl.tenancy_id,tl.store_id,tl.business_date,tl.shift_id,tl.operator_id,pl.third_bill_code,pl.bill_code,tl.main_trading,pl.pay_money,pl.payment_id");
		vlidateSql.append("       ) cz group by cz.tenancy_id,cz.store_id,cz.business_date,cz.shift_id,cz.operator_id");
		vlidateSql.append("    )as bl group by bl.tenancy_id,bl.store_id,bl.report_date,bl.shift_id,bl.waiter_id");
		vlidateSql.append(" ) as bl");
		vlidateSql.append(" left join ( "); 
		vlidateSql.append("       select pbp.tenancy_id,pbp.store_id,pbp.report_date,pbp.pay_shift_id,pbp.waiter_id,sum(pbp.amount) as receive_amount");
		vlidateSql.append("       from pos_cashier_receive_log pbp group by pbp.tenancy_id,pbp.store_id,pbp.report_date,pbp.pay_shift_id,pbp.waiter_id");
		vlidateSql.append(" ) as pcrl on bl.tenancy_id = pcrl.tenancy_id and bl.store_id = pcrl.store_id and bl.report_date = pcrl.report_date and bl.shift_id = pcrl.pay_shift_id and bl.waiter_id = pcrl.waiter_id ");
		vlidateSql.append(" left join employee em  on bl.tenancy_id = em.tenancy_id and bl.store_id = em.store_id and bl.waiter_id = em.id");
		vlidateSql.append(" where bl.tenancy_id = '").append(tenantId).append("' and bl.store_id = '").append(organId).append("' and bl.report_date='").append(reportDate).append("' and bl.waiter_id = '").append(cashierNum).append("'");
        
        List<JSONObject> list = posDao.query4Json(tenantId, vlidateSql.toString());

        Data result = param.clone();
        result.setData(null);

        result.setData(list);
        result.setCode(Constant.CODE_SUCCESS);
        result.setMsg(Constant.GET_CASHIER_LIMIT_VLIDATE_SUCCESS);

        return result;
    }
}
