/** 
 * @(#)CodeFormat.java    1.0   2017-04-28
 * 
 * Copyright 北京普照天星科技有限公司. All rights reserved. 
 * PROPRIETARY/CONFIDENTIAL. Use is subject to license terms. 
 */
package com.tzx.base.listener;

import java.io.Serializable;

import com.tzx.base.service.servlet.ProcessMessage;

public class PosConsumerThreadPoolTask implements Runnable,Serializable{

	private static final long serialVersionUID = 7399427033513980362L;
	
	// 保存任务所需要的数据
	private String threadPoolTaskData;
	
	PosConsumerThreadPoolTask(String msg){
		this.threadPoolTaskData = msg;
	}

	@Override
	public void run() {
//		System.out.println(threadPoolTaskData);		
		try {
			new ProcessMessage().doExecute(threadPoolTaskData);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

}
