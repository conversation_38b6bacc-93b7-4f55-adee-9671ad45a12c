package com.tzx.clientorder.mtwechat.bo.imp;

import com.tzx.base.bo.PosCodeService;
import com.tzx.clientorder.mtwechat.bo.InsertOrderService;
import com.tzx.clientorder.mtwechat.po.springjdbc.dao.MtInsertOrderDao;
import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.pos.asyn.pool.ThreadPool;
import com.tzx.pos.asyn.thread.OrderingPrintThread;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.CacheTableUtil;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.bo.*;
import com.tzx.pos.bo.dto.PosBill;
import com.tzx.pos.bo.payment.PosPaymentService;
import com.tzx.clientorder.common.entity.*;
import com.tzx.clientorder.acewillwechat.po.springjdbc.dao.AcewillInsertOrderDao;
import com.tzx.clientorder.acewillwechat.po.springjdbc.dao.AcewillPaySucessDao;
import com.tzx.pos.service.servlet.PosDishServlet;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import net.sf.json.JsonConfig;
import net.sf.json.util.PropertyFilter;

import org.apache.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

import java.beans.PropertyDescriptor;
import java.util.*;

/**
 * 下单（提供美团接口）
 * Created by qingui on 2018-05-30.
 */
@Service(InsertOrderService.NAME)
public class InsertOrderServiceImp implements InsertOrderService{

    private static final Logger logger = Logger.getLogger(InsertOrderServiceImp.class);

    @Resource(name = PosDishService.NAME)
    PosDishService posDishService;
    @Resource(name = PosDishServlet.NAME)
    PosDishServlet posDishServlet;
    @Resource(name = AcewillInsertOrderDao.NAME)
    AcewillInsertOrderDao insertOrderDao;

    @Resource(name = PosCodeService.NAME)
    PosCodeService codeService;
    @Resource(name = PosBaseService.NAME)
    PosBaseService posBaseServiceImp;
    @Resource(name = PosPaymentService.NAME)
    PosPaymentService posPaymentServiceImp;
    @Resource(name = MtInsertOrderDao.NAME)
    MtInsertOrderDao mtInsertOrderDao;
    @Resource(name = AcewillPaySucessDao.NAME)
    AcewillPaySucessDao paySucessDao;
    @Resource(name = PosPrintService.NAME)
    private PosPrintService posPrintService;

    @Resource(name = PosPrintNewService.NAME)
    private PosPrintNewService posPrintNewService;

    @Override
    @Transactional(propagation= Propagation.REQUIRES_NEW)
    public JSONObject insertOrderRequiresNew(String tenancyId, Integer storeId, JSONObject jsonObject) throws Exception{
        logger.info("美团调用下单接口开始-----------------");
        logger.info("入参：" + jsonObject);
        JSONObject result = new JSONObject();
        // 解析传入参数
        AcewillInsertOrder insertOrder = (AcewillInsertOrder) JSONObject.toBean(jsonObject.optJSONObject("data"),getJsonConfig());
        if(null == insertOrder){
            JSONObject orderError = new JSONObject();
            orderError.put("success", false);
            orderError.put("msg", "落单失败：参数格式错误，订单信息获取失败");
            return orderError;
        }
        insertOrder.setStore_id(storeId);
        insertOrder.setTenent_id(tenancyId);
        // 获得订单信息
        AcewillOrderInfo orderInfo = insertOrder.getOrder_info();
        if(orderInfo==null) {
            JSONObject orderError = new JSONObject();
            orderError.put("success", false);
            orderError.put("msg", "落单失败：参数错误，订单信息不能为空");
            return orderError;
        }
        // 查询桌台状态
//        JSONObject tableStatus = insertOrderDao.getTableStatus(tenancyId, storeId, orderInfo.getTable_sno());
        String errorMsg = null;
//        if (tableStatus != null) {
        // 桌台存在
//            String lockOptNum = tableStatus.getString("lock_opt_num");

//            if (StringUtils.isEmpty(lockOptNum) || "null".equals(lockOptNum)) {
        // 未锁台
        AcewillOrderAssist orderAssist = null;
//                String state = tableStatus.getString("state");
//               if (SysDictionary.TABLE_STATE_FREE.equals(state)) {

        JSONObject posBill1 = insertOrderDao.getPosBillByOrderNum(tenancyId, insertOrder.getIdentify());
        if(posBill1!=null) {
            JSONObject orderError = new JSONObject();
            orderError.accumulate("success", false);
            orderError.put("msg", "落单失败：订单号重复");
            return orderError;
        }
        // 未占用，开台
        orderAssist = createOrderAssist(insertOrder);
        /*if (null != orderAssist && "-1".equals(orderAssist.getOpt_num())){
            JSONObject orderError = new JSONObject();
            orderError.accumulate("success", false);
            orderError.put("msg", "落单失败：pos未签到");
            return orderError;
        }*/
        Data createOpenTableData = createOpenTableData(insertOrder, orderAssist);
        List<JSONObject> openTableList = posDishServlet.newestOpenTable(createOpenTableData,
                new JSONObject());
        if (openTableList.size() > 0) {
            JSONObject posBillJson = JSONObject.fromObject(openTableList.get(0));
            orderAssist.setBill_num(posBillJson.getString("bill_num"));
            insertOrderDao.insertOpenId(tenancyId, storeId, orderAssist.getBill_num(), insertOrder.getOpenid());
        }

        // 拼装下单所需参数
        Data param = createOrderDishData(insertOrder, orderAssist);
        JSONObject printParamJson = new JSONObject();
        // 下单
        Data newestOrderDish = posDishService.newestOrderDish(param, printParamJson);

        //打印下单的订单
//        OrderingPrintThread orderingPrintThread = new OrderingPrintThread(printParamJson, param);
//        orderingPrint(orderingPrintThread);

        //更新OpenId
        if (newestOrderDish.getCode() == 0) {
            // 会员信息
            String sysParameter = CacheTableUtil.getSysParameter("CustomerType");
            if("acewill".equals(sysParameter)) {
                savePosBillMember(insertOrder, orderAssist);
            }

            result.put("success", true);
            result.put("msg","下单成功");
            //门店订单号
            PosBill posBill = (PosBill) newestOrderDish.getData().get(0);

            result.put("oid", posBill.getBill_num());
            //美团是先付订单，存上支付信息，调用清台
            JSONObject data = jsonObject.optJSONObject("data");
            List<JSONObject> payInfoList = (List<JSONObject>) data.opt("pay_info");
            if (null != payInfoList && payInfoList.size() > 0){
                JSONObject payInfo = payInfoList.get(0);

                String amount = payInfo.optString("amount");
                String createTime = payInfo.optString("create_time_str");
                String source = payInfo.optString("source");
                String table = payInfo.optString("table");
                //交易流水号
                String serilNo = payInfo.optString("serilNo");
                payInfo.element("store_id", insertOrder.getStore_id());
                payInfo.element("tenancy_id", insertOrder.getTenent_id());
                payInfo.element("bill_num", posBill.getBill_num());
                payInfo.element("table_code", orderInfo.getTableno());
                //支付方式 payment_way
                JSONObject payWay = mtInsertOrderDao.getPaymentWay(tenancyId, source);
                if (null != payWay){
                    payInfo.element("type", payWay.optString("type"));
                    payInfo.element("jzid", payWay.optString("jzid"));
                    payInfo.element("name", payWay.optString("name"));
                    payInfo.element("name_english", payWay.optString("name_english"));
                }

                payInfo.element("count", 1);
                //number phone

                payInfo.element("last_updatetime", DateUtil.getNowDateYYDDMMHHMMSS());
                //is_ysk
                payInfo.element("rate", 1);
                payInfo.element("currency_amount", amount);
                payInfo.element("upload_tag", 0);
                //customer_id
                payInfo.element("bill_code", serilNo);
                //remark
                payInfo.element("payment_state", "01");
                //param_cach
                payInfo.element("batch_num", posBill.getBatch_num());
                payInfo.element("more_coupon", 0);
                //fee fee_rate coupon_type yjzid
                payInfo.element("coupon_buy_price", amount);
                payInfo.element("due", amount);
                payInfo.element("tenancy_assume", 0);
                payInfo.element("third_assume", 0);
                payInfo.element("third_fee", 0);
                //card_code payment_uid payment_uid1

                //关台
                //发票抬头
                String isInvoice = "0";
                JSONObject printJson = new JSONObject();
                JSONObject resultJson = new JSONObject();
                //查询账单信息
                JSONObject posBillJson = paySucessDao.getPosBillByBillNum(tenancyId, posBill.getBill_num());
                if (posBillJson == null) {
                    // 账单不存在
                    logger.error("账单号为:{}的账单不存在");
                }
                AcewillPosBill mtPosBill = (AcewillPosBill) JSONObject.toBean(posBillJson, AcewillPosBill.class);

                payInfo.element("report_date", posBill.getReport_date());
                payInfo.element("shift_id", posBill.getShift_id());
                JSONObject obj = (JSONObject) param.getData().get(0);

                payInfo.element("pos_num", obj.optString("pos_num"));
                payInfo.element("cashier_num", posBill.getCashier_num());

                mtPosBill.setPos_num(obj.optString("pos_num"));

                //插入付款账单
                mtInsertOrderDao.insertPayInfo(tenancyId, payInfo);

                posPaymentServiceImp.closedAcewillPosBill(tenancyId, storeId, mtPosBill.getBill_num(),
                        mtPosBill.getReport_date(), mtPosBill.getShift_id(), mtPosBill.getPos_num(),
                        mtPosBill.getWaiter_num(), "Y", isInvoice, resultJson, printJson, "1", DateUtil.currentTimestamp());

                // 打印
                try {
                    //LOGGER.info("pay.success:单号："+posBill.getBill_num()+"=======================================================门店清台开始打印："+printJson);
                    posPrintService.printPosBillForPayment(printJson, posPrintNewService);
                    //LOGGER.info("pay.success:单号："+posBill.getBill_num()+"=======================================================门店清台打印完成");
                }catch (Exception e) {
                    e.printStackTrace();
                }
            }
            return result;
        } else {
            errorMsg = "落单失败";
        }
//            }
//        } else {
//            errorMsg = "落单失败:当前桌台不存在";
//        }
        JSONObject orderError = new JSONObject();
        orderError.put("success", false);
        orderError.put("msg", errorMsg);
        return orderError;

    }

    @Override
    public void orderingPrint(OrderingPrintThread orderingPrintThread) {
        ThreadPool.getPrintThreadPool().execute(orderingPrintThread);
    }

    public static int partition(int []array,int lo,int hi){
        //固定的切分方式
        int key=array[lo];
        while(lo<hi){
            while(array[hi]>=key&&hi>lo){//从后半部分向前扫描
                hi--;
            }
            array[lo]=array[hi];
            while(array[lo]<=key&&hi>lo){//从前半部分向后扫描
                lo++;
            }
            array[hi]=array[lo];
        }
        array[hi]=key;
        return hi;
    }

    public static void sort(int[] array,int lo ,int hi){
        if(lo>=hi){
            return ;
        }
        int index=partition(array,lo,hi);
        sort(array,lo,index-1);
        sort(array,index+1,hi);
    }

    public static void main(String[] args){
        int[] arr = {5,3,0,2,8,9,2,7};

        sort(arr, 0, arr.length - 1);

        //快速排序(快排)


        //冒泡排序
        /*for (int i = 0;i < arr.length-1; i++){
            for (int j = 0; j < arr.length - 1 - i; j++){
                if (arr[j] > arr[j+1]){
                    int tmp = arr[j+1];
                    arr[j+1] = arr[j];
                    arr[j] = tmp;
                }
            }
        }
        for (int num : arr){
            System.err.print(num + ",");
        }*/
    }

    private AcewillPosBillMember savePosBillMember(AcewillInsertOrder insertOrder, AcewillOrderAssist orderAssist)
            throws Exception {
        AcewillOrderInfo orderInfo = insertOrder.getOrder_info();
        AcewillOrderMember member = orderInfo.getMember();
        AcewillPosBillMember posBillMember = new AcewillPosBillMember();
        posBillMember.setTenancy_id(insertOrder.getTenent_id());
        posBillMember.setStore_id(insertOrder.getStore_id());
        posBillMember.setBill_num(orderAssist.getBill_num());
        posBillMember.setBill_code(insertOrder.getOut_order_id());
        posBillMember.setReport_date(DateUtil.parseDate(orderAssist.getReport_date()));
        posBillMember.setType(SysDictionary.BILL_MEMBERCARD_JFZS06);
        posBillMember.setAmount(null);

        posBillMember.setCredit(member.getCredit());
        posBillMember.setCard_code(member.getCno());
        posBillMember.setMobil(member.getMobile());
        posBillMember.setLast_updatetime(new Date());
        posBillMember.setUpload_tag(0);
        posBillMember.setRemark(null);
        posBillMember.setBill_code(null);
        posBillMember.setRequest_state(null);
        posBillMember.setCustomer_code(member.getCno());
        posBillMember.setCustomer_name(member.getName());
        posBillMember.setConsume_before_credit(member.getCredit());
        posBillMember.setConsume_after_credit(member.getCredit());
        posBillMember.setConsume_before_main_balance(member.getBalance());
        //交易前赠送账户余额
        posBillMember.setConsume_before_reward_balance(0d);
        //交易后主账户余额
        posBillMember.setConsume_after_main_balance(member.getBalance());
        //交易后赠送账户余额
        posBillMember.setConsume_after_reward_balance(0d);
        insertOrderDao.savePosBillMember(posBillMember);
        return posBillMember;
    }

    private JsonConfig getJsonConfig() {
        JsonConfig config = new JsonConfig();
        config.setClassMap(getInsertOrderClassMap());
        config.setRootClass(AcewillInsertOrder.class);
        config.setJavaPropertyFilter(new PropertyFilter() {
            @Override
            public boolean apply(Object source, String name, Object value) {
                PropertyDescriptor propertyDescriptor = BeanUtils.getPropertyDescriptor(source.getClass(), name);
                return propertyDescriptor==null;
            }
        });
        return config;
    }

    private Map<String,Class<?>> getInsertOrderClassMap() {
        Map<String, Class<?>> classMap = new HashMap<>();
        classMap.put("order_info", AcewillOrderInfo.class);
        classMap.put("ordermemo", AcewillOrderMemo.class);
        classMap.put("member", AcewillOrderMember.class);
        classMap.put("upgrade", AcewillOrderUpGrade.class);
        classMap.put("setmeal", AcewillOrderSetmeal.class);
        classMap.put("normalitems", AcewillOrderNormalitem.class);
        classMap.put("maindish", AcewillOrderSetmealItem.class);
        classMap.put("mandatory", AcewillOrderSetmealItem.class);
        classMap.put("optional", AcewillOrderSetmealItem.class);
        return classMap;
    }

    private AcewillOrderAssist createOrderAssist(JSONObject posBill) {
        AcewillOrderAssist orderAssist = new AcewillOrderAssist();
        orderAssist.setBill_num(posBill.getString("bill_num"));
        orderAssist.setOpt_num(posBill.getString("open_opt"));
        orderAssist.setPos_num(posBill.getString("pos_num"));
        orderAssist.setReport_date(posBill.getString("report_date"));
        orderAssist.setShift_id(posBill.getInt("shift_id"));
        return orderAssist;
    }

    private AcewillOrderAssist createOrderAssist(AcewillInsertOrder insertOrder) throws Exception {
        AcewillOrderAssist orderAssist = new AcewillOrderAssist();
        String tenantId = insertOrder.getTenent_id();
        Integer storeId = insertOrder.getStore_id();
        Date reportDate = insertOrderDao.getReportDate(tenantId, storeId);
        orderAssist.setReport_date(DateUtil.formatDate(reportDate));
        List<JSONObject> optStateInfoList = posBaseServiceImp.getOptStateInfo(tenantId, storeId,
                orderAssist.getReport_date());
        if (optStateInfoList.size() > 0) {
            JSONObject optStateInfo = optStateInfoList.get(0);
            orderAssist.setPos_num(optStateInfo.getString("pos_num"));
            orderAssist.setOpt_num(optStateInfo.getString("opt_num"));
        }else {
            //如果pos端未签到，会导致落单失败
            orderAssist.setOpt_num("-1");
        }
        int shiftId = insertOrderDao.getShiftId(tenantId, storeId, reportDate, orderAssist.getOpt_num(), orderAssist.getPos_num());
        orderAssist.setShift_id(shiftId);
        return orderAssist;
    }

    private Data createOpenTableData(AcewillInsertOrder insertOrder, AcewillOrderAssist orderAssist) throws Exception {
        AcewillOrderInfo orderInfo = insertOrder.getOrder_info();
        Data data = new Data();
        data.setTenancy_id(insertOrder.getTenent_id());
        data.setStore_id(insertOrder.getStore_id());
        data.setType(Type.ORDERING);
        data.setOper(Oper.add);
        List<JSONObject> list = new ArrayList<>();
        JSONObject object = new JSONObject();
        //快餐
        object.put("mode", 1);
        object.put("shift_id", orderAssist.getShift_id());
        object.put("report_date", orderAssist.getReport_date());
        object.put("table_code", orderInfo.getTableno());
        object.put("pos_num", orderAssist.getPos_num());
        object.put("opt_num", orderAssist.getOpt_num());
        object.put("waiter_num", orderAssist.getOpt_num());
        object.put("item_menu_id", 0);
        object.put("sale_mode", "TS01");
        object.put("chanel", "WX02");
        object.put("guest", orderInfo.getPeople());
        object.put("preorderno", insertOrder.getIdentify());
        object.put("copy_bill_num", "");
        object.put("remark", "");
        object.put("shop_real_amount", 0);
        object.put("platform_charge_amount", 0);
        object.put("settlement_type", "");
        object.put("discount_mode_id", 0);
        object.put("discountk_amount", 0);
        object.put("discount_rate", 0);
        list.add(object);
        data.setData(list);
        return data;
    }

    private Data createOrderDishData(AcewillInsertOrder insertOrder, AcewillOrderAssist orderAssist) throws Exception {
        Data data = new Data();
        data.setTenancy_id(insertOrder.getTenent_id());
        data.setStore_id(insertOrder.getStore_id());
        data.setType(Type.ORDERING);
        data.setOper(Oper.add);
        data.setSource("cc_order");
        List<JSONObject> orderList = new ArrayList<>();
        AcewillOrder order = createOrder(insertOrder, orderAssist);
        JSONObject orderJsonObject = JSONObject.fromObject(order, getAcewillOrderJsonConfig());
        orderList.add(orderJsonObject);
        data.setData(orderList);
        return data;
    }

    private JsonConfig getAcewillOrderJsonConfig() {
        Map<String, Class<?>> classMap = new HashMap<>();
        classMap.put("item", AcewillOrderItem.class);
        classMap.put("method", AcewillOrderItemMethod.class);
        JsonConfig jsonConfig = new JsonConfig();
        jsonConfig.setClassMap(classMap);
        jsonConfig.setRootClass(AcewillOrder.class);
        return jsonConfig;
    }

    private AcewillOrder createOrder(AcewillInsertOrder insertOrder, AcewillOrderAssist orderAssist) throws Exception {

        AcewillOrderInfo orderInfo = insertOrder.getOrder_info();

        AcewillOrder acewillOrder = new AcewillOrder();
        // 订单号
        acewillOrder.setBill_num(orderAssist.getBill_num());
        // 整单备注
        //String billTaste = insertOrderDao.getBillTaste(insertOrder);
        AcewillOrderMemo ordermemo = orderInfo.getOrdermemo();
        if(ordermemo!=null) {
            acewillOrder.setBill_taste(ordermemo.getText());
        }
        // 是否厨打
        acewillOrder.setIsprint("Y");
        // 0:下单 1:
        acewillOrder.setMode(0);
        // 操作员编号
        acewillOrder.setOpt_num(orderAssist.getOpt_num());
        // 收款机编号
        acewillOrder.setPos_num(orderAssist.getPos_num());
        // 备注
        acewillOrder.setRemark(null);
        // 报表日期
        acewillOrder.setReport_date(orderAssist.getReport_date());
        // 销售模式
        // acewillOrder.setSale_mode(sale_mode);
        // 班次id
        acewillOrder.setShift_id(orderAssist.getShift_id());

        acewillOrder.setTable_code(orderInfo.getTableno());
        // 服务员号
        acewillOrder.setWaiter_num(null);
        List<AcewillOrderItem> orderItems = new ArrayList<>();
        acewillOrder.setItem(orderItems);
        // 点餐序号
        List<AcewillOrderSetmeal> setmealList = orderInfo.getSetmeal();
        Integer item_serial = insertOrderDao.getLastItemSerial(insertOrder.getTenent_id(), orderAssist.getBill_num());
        Map<String, JSONObject> itemComboDetailMap = itemComboDetailMap(insertOrder);
        Map<String, String> setmealUnitMap = getSetmealUnitMap(insertOrder);
        if(setmealList!=null) {
            for (AcewillOrderSetmeal setmeal : setmealList) {
                String setmealUnitId = setmealUnitMap.get(setmeal.getDid());
                if(!StringUtils.isEmpty(setmealUnitId)) {
                    setmeal.setDuid(setmealUnitId);
                }
            }
        }
        Map<String, String> unitNameMap = insertOrderDao.getUnitNameMap(insertOrder);
        if(setmealList!=null) {
            for (AcewillOrderSetmeal setmeal : setmealList) {
                item_serial++;
                List<AcewillOrderSetmealItem> setmealItemList = new ArrayList<>();
                setmealItemList.addAll(setmeal.getMaindish());
                setmealItemList.addAll(setmeal.getMandatory());
                if (null != setmeal.getOptional()){
                    setmealItemList.addAll(setmeal.getOptional());
                }else {
                    List<AcewillOrderSetmealItem> itemList = new ArrayList<>();
                    setmealItemList.addAll(itemList);
                }
                List<String> itemTmpList = new ArrayList<>();
                for (AcewillOrderSetmealItem item : setmealItemList) {

                    item.setNumber(setmeal.getNumber()*item.getNumber());
                    String duName = unitNameMap.get(item.getDuid());
                    JSONObject itemComboDetail = null;
                    if (null != itemTmpList && itemTmpList.size() > 0 && itemTmpList.contains(item.getId())){

                    }else {
                        itemComboDetail = itemComboDetailMap.get(item.getId());
                    }

                    itemTmpList.add(item.getId());

                    item.setDuName(duName);
                    AcewillOrderItem setmealOrderItem = createOrderItem(orderInfo, setmeal, item, item_serial,itemComboDetail);
                    orderItems.add(setmealOrderItem);
                }
                JSONObject itemComboDetail = itemComboDetailMap.get(setmeal.getDid());
                String duName = unitNameMap.get(setmeal.getDuid());
                setmeal.setDuName(duName);
                orderItems.add(createOrderItem(orderInfo, setmeal, item_serial, itemComboDetail));
            }
        }
        List<AcewillOrderNormalitem> normalitemList = orderInfo.getNormalitems();
        if(normalitemList!=null) {
            for (AcewillOrderNormalitem item : normalitemList) {
                item_serial++;
                String duName = unitNameMap.get(item.getDuid());
                item.setDuName(duName);
                JSONObject itemComboDetail = itemComboDetailMap.get(item.getDid());
                AcewillOrderItem setmealOrderItem = createOrderItem(orderInfo, item, item_serial, itemComboDetail);
                orderItems.add(setmealOrderItem);
            }
        }

        return acewillOrder;
    }

    private Map<String, String> getSetmealUnitMap(AcewillInsertOrder insertOrder) throws Exception {
        String TenentId = insertOrder.getTenent_id();
        AcewillOrderInfo orderInfo = insertOrder.getOrder_info();
        List<String> itemIdList = new ArrayList<>();
        List<AcewillOrderSetmeal> setmealList = orderInfo.getSetmeal();
        if(setmealList!=null) {
            for (AcewillOrderSetmeal setmeal : setmealList) {
                itemIdList.add(setmeal.getDid());
            }
        }
        Map<String,String> setmealUnitMap = new HashMap<>();
        List<JSONObject> findItemUnit = insertOrderDao.findItemUnit(TenentId, itemIdList);
        if(findItemUnit!=null) {
            for (JSONObject jsonObject : findItemUnit) {
                setmealUnitMap.put(jsonObject.getString("item_id"), jsonObject.getString("id"));
            }
        }
        return setmealUnitMap;
    }

    private Map<String, JSONObject> itemComboDetailMap(AcewillInsertOrder insertOrder)
            throws Exception {
        String TenentId = insertOrder.getTenent_id();
        AcewillOrderInfo orderInfo = insertOrder.getOrder_info();
        List<Integer> itemIdList = new ArrayList<>();
        List<AcewillOrderSetmeal> setmealList = orderInfo.getSetmeal();
        if (setmealList!=null) {
            for (AcewillOrderSetmeal setmeal : setmealList) {
                List<AcewillOrderSetmealItem> setmealItemList = new ArrayList<>();
                setmealItemList.addAll(setmeal.getMaindish());
                setmealItemList.addAll(setmeal.getMandatory());
                if (null != setmeal.getOptional()){
                    setmealItemList.addAll(setmeal.getOptional());
                }else {
                    List<AcewillOrderSetmealItem> itemList = new ArrayList<>();
                    setmealItemList.addAll(itemList);
                }
                for (AcewillOrderSetmealItem item : setmealItemList) {
                    itemIdList.add(Integer.parseInt(item.getId()));
                }
                itemIdList.add(Integer.parseInt(setmeal.getDid()));
            }
        }
        List<AcewillOrderNormalitem> normalitemList = orderInfo.getNormalitems();
        if(normalitemList!=null) {
            for (AcewillOrderNormalitem item : normalitemList) {
                itemIdList.add(Integer.parseInt(item.getDid()));
            }
        }
        List<JSONObject> itemComboDetails = insertOrderDao.getItemComboDetails(TenentId, itemIdList);
        Map<String,JSONObject> itemComboDetailMap = new HashMap<>();
        if(itemComboDetails!=null) {
            for (JSONObject jsonObject : itemComboDetails) {
                /*if ("N".equals(jsonObject.optString("is_itemgroup"))){
                    itemComboDetailMap.put(jsonObject.getString("details_id"), jsonObject);
                }else {
                    itemComboDetailMap.put(jsonObject.getString("iitem_id"), jsonObject);
                }*/
                itemComboDetailMap.put(jsonObject.getString("iitem_id"), jsonObject);
            }
        }
        return itemComboDetailMap;
    }

    private AcewillOrderItem createOrderItem(AcewillOrderInfo orderInfo, AcewillOrderNormalitem item,
                                             Integer item_serial,JSONObject itemComboDetail) {
        AcewillOrderItem orderItem = new AcewillOrderItem();
        // 辅助Assist
        if(itemComboDetail!=null) {
            orderItem.setAssist_item_id(itemComboDetail.getString("id"));
            orderItem.setAssist_num(itemComboDetail.getString("combo_num"));
        }
        // orderItem.setAssist_money(assist_money);
        // 餐谱明细id orderItem.setDetails_id(details_id);

        orderItem.setItem_count(item.getNumber());

        orderItem.setItem_id(item.getDid());
        orderItem.setItem_name(item.getName());
        orderItem.setItem_num(item.getDishsno());
        orderItem.setItem_price(item.getPrice());
        orderItem.setItem_property(SysDictionary.ITEM_PROPERTY_SINGLE);
        orderItem.setItem_remark(item.getRemark());
        // 点菜序号
        orderItem.setItem_serial(item_serial);
        orderItem.setItem_taste(item.getRemark());
        // 规格名称
        orderItem.setItem_unit_name(item.getDuName());

        // orderItem.setSale_mode(sale_mode);

        orderItem.setMethod(createItemMethod(item.getCooks()));
        // 座位号
        orderItem.setSeat_num(null);
        orderItem.setSetmeal_id(null);
        // 套菜点菜号
        orderItem.setSetmeal_rwid(null);
        // 规格id
        orderItem.setUnit_id(item.getDuid());
        // 等叫标记
        // orderItem.setWaitcall_tag(waitcall_tag);
        return orderItem;
    }

    private AcewillOrderItem createOrderItem(AcewillOrderInfo orderInfo, AcewillOrderSetmeal setmeal,
                                             Integer item_serial,JSONObject itemComboDetail) {
        AcewillOrderItem orderItem = new AcewillOrderItem();
        // 辅助Assist
        if(itemComboDetail!=null) {
            orderItem.setAssist_item_id(itemComboDetail.getString("id"));
            orderItem.setAssist_num(itemComboDetail.getString("combo_num"));
        }
        //assist_money
        //orderItem.setAssist_money(setmeal.getAprice().toString());
        // 餐谱明细id orderItem.setDetails_id(details_id);
        orderItem.setItem_count(setmeal.getNumber());

        orderItem.setItem_id(setmeal.getDid());
        orderItem.setItem_name(setmeal.getName());
        orderItem.setItem_num(setmeal.getDishsno());
        orderItem.setItem_price(setmeal.getPrice());
        orderItem.setItem_property(SysDictionary.ITEM_PROPERTY_SETMEAL);
        orderItem.setItem_remark(setmeal.getRemark());
        // 点菜序号
        orderItem.setItem_serial(item_serial);
        orderItem.setItem_taste(setmeal.getRemark());

        // 规格名称
        orderItem.setItem_unit_name(setmeal.getDuName());
        // orderItem.setSale_mode(sale_mode);
        orderItem.setMethod(createItemMethod(setmeal.getCooks()));
        // 座位号
        orderItem.setSeat_num(orderInfo.getTableno());
        orderItem.setSetmeal_id(setmeal.getDid());
        // 套菜点菜号
        orderItem.setSetmeal_rwid(item_serial);
        // 规格id
        orderItem.setUnit_id(setmeal.getDuid());
        // 等叫标记
        // orderItem.setWaitcall_tag(waitcall_tag);
        return orderItem;
    }

    private AcewillOrderItem createOrderItem(AcewillOrderInfo orderInfo, AcewillOrderSetmeal setmeal,
                                             AcewillOrderSetmealItem item, Integer item_serial,JSONObject itemComboDetail) {
        AcewillOrderItem orderItem = new AcewillOrderItem();
        // 辅助Assist
        if(itemComboDetail!=null) {
            orderItem.setAssist_item_id(itemComboDetail.getString("id"));
            orderItem.setAssist_num(itemComboDetail.getString("combo_num"));
        }
        // add by qin
        //orderItem.setAssist_money(setmeal.getAprice().toString());
        // 餐谱明细id orderItem.setDetails_id(details_id);

        orderItem.setItem_count(item.getNumber());

        orderItem.setItem_id(item.getId());
        orderItem.setItem_name(item.getName());
        orderItem.setItem_num(item.getDishsno());
        //加价
        //orderItem.setItem_price(item.getAprice());
        orderItem.setItem_property(SysDictionary.ITEM_PROPERTY_MEALLIST);
        orderItem.setItem_remark(setmeal.getRemark());
        // 点菜序号
        orderItem.setItem_serial(item_serial);

        // orderItem.setItem_taste(null);
        // 规格名称
        orderItem.setItem_unit_name(item.getDuName());

        orderItem.setMethod(createItemMethod(item.getCooks()));
        // 做法 orderItem.setMethod(method);

        // orderItem.setSale_mode(sale_mode);
        orderItem.setMethod(createItemMethod(setmeal.getCooks()));
        // 座位号
        orderItem.setSeat_num(orderInfo.getTableno());
        orderItem.setSetmeal_id(setmeal.getDid());
        // 套菜点菜号
        orderItem.setSetmeal_rwid(item_serial);
        // 规格id
        orderItem.setUnit_id(item.getDuid());
        // 等叫标记
        // orderItem.setWaitcall_tag(waitcall_tag);
        return orderItem;
    }

    private List<AcewillOrderItemMethod> createItemMethod(Object cooks) {
        List<AcewillOrderItemMethod> methods = new ArrayList<>();
        JSONArray cookArray = JSONArray.fromObject(cooks);
        if(cooks!=null) {
            for (int i = 0; i < cookArray.size(); i++) {
                JSONObject jsonObject = cookArray.getJSONObject(i);
                AcewillOrderItemMethod method = new AcewillOrderItemMethod();
                method.setMethod_id(jsonObject.getString("id"));
                method.setMethod_name(jsonObject.getString("name"));
                methods.add(method);
            }
        }
        return methods;
    }
}
