package com.tzx.thirdpay.po.springjdbc.dao.imp;

import java.util.List;

import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import com.tzx.base.entity.PosThirdPaymentRefundEntity;
import com.tzx.pos.base.dao.imp.BaseDaoImp;
import com.tzx.thirdpay.po.springjdbc.dao.ThirdPaymentRefundDao;

@Repository(ThirdPaymentRefundDao.NAME)
public class ThirdPaymentRefundDaoImp extends BaseDaoImp implements ThirdPaymentRefundDao
{
	@Override
	public int insertPosPaymentOrderRefund(String tenancyId, int storeId, PosThirdPaymentRefundEntity paymentRefund) throws Exception
	{
		StringBuilder insertPaymentRefundSql = new StringBuilder();
		insertPaymentRefundSql
				.append("insert into pos_third_payment_refund(tenancy_id,store_id,report_date,shift_id,pos_num,opt_num,channel,order_num,form_trade_no,out_request_no,trade_no,payment_class,payment_id,total_amount,settle_amount,create_time,extra,last_updatetime,last_operator,failure_code,failure_msg,retry_count,oper_type,status,finish_status,service_type) ");
		insertPaymentRefundSql.append("select ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,? ");
		insertPaymentRefundSql.append("where not exists (select 1 from pos_third_payment_refund where tenancy_id=? and store_id=? and out_request_no = ?) ");

		return this
				.update(insertPaymentRefundSql.toString(),
						new Object[]
						{ tenancyId, storeId, paymentRefund.getReport_date(), paymentRefund.getShift_id(), paymentRefund.getPos_num(), paymentRefund.getOpt_num(), paymentRefund.getChannel(), paymentRefund.getOrder_num(), paymentRefund.getForm_trade_no(), paymentRefund.getOut_request_no(),
								paymentRefund.getTrade_no(), paymentRefund.getPayment_class(), paymentRefund.getPayment_id(), paymentRefund.getTotal_amount(), paymentRefund.getSettle_amount(), paymentRefund.getCreate_time(), paymentRefund.getExtra(), paymentRefund.getLast_updatetime(),
								paymentRefund.getLast_operator(), paymentRefund.getFailure_code(), paymentRefund.getFailure_msg(), paymentRefund.getRetry_count(), paymentRefund.getOper_type(), paymentRefund.getStatus(), paymentRefund.getFinish_status(),paymentRefund.getService_type(), tenancyId, storeId,
								paymentRefund.getOut_request_no() });
	}

	@Override
	public int updateThirdPayOrderRefundForRetry(String tenancyId, int storeId, PosThirdPaymentRefundEntity paymentRefund) throws Exception
	{
		StringBuilder updateSql = new StringBuilder("update pos_third_payment_refund set trade_no=?,last_updatetime=?,last_operator=?,retry_count=?,finish_status=? where tenancy_id=? and store_id=? and id=?");
		return this.update(updateSql.toString(), new Object[]
		{ paymentRefund.getTrade_no(), paymentRefund.getLast_updatetime(), paymentRefund.getLast_operator(), paymentRefund.getRetry_count(), paymentRefund.getFinish_status(), tenancyId, storeId, paymentRefund.getId() });
	}

	@Override
	public PosThirdPaymentRefundEntity getPosThirdPaymentRefund(String tenancyId, int storeId, String outRequestNo) throws Exception
	{
		StringBuffer querySql = new StringBuffer("select * from pos_third_payment_refund pr where pr.tenancy_id=? and pr.store_id=? and pr.out_request_no = ?");
		List<PosThirdPaymentRefundEntity> list = this.jdbcTemplate.query(querySql.toString(), new Object[]
		{ tenancyId, storeId, outRequestNo }, BeanPropertyRowMapper.newInstance(PosThirdPaymentRefundEntity.class));

		PosThirdPaymentRefundEntity entity = null;
		if (null != list && 0 < list.size())
		{
			entity = list.get(0);
		}

		return entity;
	}
	
	
}
