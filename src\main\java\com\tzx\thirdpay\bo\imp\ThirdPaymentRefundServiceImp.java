package com.tzx.thirdpay.bo.imp;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import com.tzx.base.entity.PosThirdPaymentRefundEntity;
import com.tzx.clientorder.wlifeprogram.bo.WlifeService;
import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.entity.Pagination;
import com.tzx.framework.common.exception.CrmErrorCode;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.Tools;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.member.acewill.bo.AcewillCustomerService;
import com.tzx.member.crm.bo.CustomerCardConsumeService;
import com.tzx.member.crm.bo.CustomerCouponService;
import com.tzx.member.crm.bo.CustomerCreditService;
import com.tzx.member.crm.bo.CustomerIncorporationService;
import com.tzx.member.crm.bo.CustomerService;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.thirdpay.bo.MeituanCouponsService;
import com.tzx.thirdpay.bo.ThirdPaymentRefundService;
import com.tzx.thirdpay.bo.ThirdPaymentService;
import com.tzx.thirdpay.po.springjdbc.dao.ThirdPaymentRefundDao;

import net.sf.json.JSONObject;

@Service(ThirdPaymentRefundService.NAME)
public class ThirdPaymentRefundServiceImp implements ThirdPaymentRefundService
{
	private static final Logger				logger					= Logger.getLogger(ThirdPaymentServiceImp.class);

	@Resource(name = ThirdPaymentRefundDao.NAME)
	protected ThirdPaymentRefundDao			paymentDao;

	public static final Map<String, String>	OPER_TYPE_CAPTION_MAP	= new HashMap<String, String>()
																	{
																		private static final long serialVersionUID = 1L;
																		{
																			put("BILL_PAYMENT_CANCEL", "取消支付");
																			put("CANCBILL", "整单取消");
																			put("REGAIN_BILL", "恢复账单");
																		}
																	};

	@Override
	public boolean thirdPaymentRefundFailure(String tenancyId, int storeId, PosThirdPaymentRefundEntity paymentRefund) throws Exception
	{
		int count = paymentDao.insertPosPaymentOrderRefund(tenancyId, storeId, paymentRefund);

		if (0 == count)
		{
			logger.debug(paymentRefund.getOrder_num() + "已存在");
		}
		return true;
	}

	@Override
	public Data getThirdPaymentRefund(Data param) throws Exception
	{
		String tenancyId = param.getTenancy_id();
		Integer storeId = param.getStore_id();

		List<?> dataList = param.getData();
		if (null == dataList || 0 >= dataList.size() || dataList.isEmpty())
		{
			throw new SystemException(PosErrorCode.PARAM_ERROR);
		}

		JSONObject paramJson = JSONObject.fromObject(dataList.get(0));
		Date reportDate = ParamUtil.getDateValueByObject(paramJson, "report_date", true, PosErrorCode.NOT_NULL_REPORT_DATE);
		String orderNum = ParamUtil.getStringValueByObject(paramJson, "order_num");
		String paymentClass = ParamUtil.getStringValueByObject(paramJson, "payment_class");
		String finishStatus = ParamUtil.getStringValueByObject(paramJson, "finish_status");

		StringBuffer sql = new StringBuffer("select pr.id,pr.report_date,pr.pos_num,pr.opt_num,pr.channel,pr.service_type,pr.order_num,pr.out_request_no,pr.form_trade_no,pr.payment_class,pr.payment_id,pr.total_amount,pr.settle_amount,pr.create_time,pr.failure_msg,pr.oper_type,pr.finish_status,pr.last_operator,sd.class_item as payment_name,em.name as opt_name,em2.name as last_operator_name from pos_third_payment_refund pr ");
		sql.append("left join sys_dictionary sd on pr.tenancy_id=sd.tenancy_id and pr.payment_class=sd.class_item_code and sd.class_identifier_code in ('third_pay','payment_class') ");
		sql.append("left join employee em on pr.tenancy_id=em.tenancy_id and pr.store_id=em.store_id and pr.opt_num=cast(em.id as text) ");
		sql.append("left join employee em2 on pr.tenancy_id=em2.tenancy_id and pr.store_id=em2.store_id and pr.last_operator=cast(em2.id as text) ");
		sql.append("where pr.tenancy_id=? and pr.store_id=? and pr.report_date=? ");
		
		if (Tools.hv(orderNum))
		{
			sql.append(" and pr.order_num like '%").append(orderNum).append("%'");
		}
		if (Tools.hv(paymentClass))
		{
			sql.append(" and pr.payment_class='").append(paymentClass).append("'");
		}
		if (Tools.hv(finishStatus))
		{
			sql.append(" and pr.finish_status='").append(finishStatus).append("'");
		}

		// 查询总记录条数
		int totalCount = paymentDao.queryForInt("select count(*) as count from (" + sql.toString() + ") t", new Object[]
		{ tenancyId, storeId, reportDate });

		Pagination pagination = param.getPagination();
		if (null == pagination)
		{
			pagination = new Pagination();
		}

		if (0 < pagination.getPagesize())
		{
			if (Tools.hv(pagination.getOrderby()))
			{
				sql.append(" order by pr.").append(pagination.getOrderby()).append(pagination.isAsc() ? " asc" : " desc");
			}
			else
			{
				sql.append(" order by pr.order_num asc,pr.create_time desc ");
			}

			if ((pagination.getPageno() - 1) < 1)
			{
				sql.append(" limit " + pagination.getPagesize() + " OFFSET " + 0);
			}
			else
			{
				sql.append(" limit " + pagination.getPagesize() + " OFFSET " + (pagination.getPageno() - 1) * pagination.getPagesize());
			}
		}
		else
		{
			sql.append(" order by pr.order_num asc,pr.create_time desc ");
		}

		List<JSONObject> list = null;
		if (0 < totalCount)
		{
			// 查询明细
			list = paymentDao.query4Json(tenancyId, sql.toString(), new Object[]
			{ tenancyId, storeId, reportDate });
		}

		if (null != list)
		{
			//
			for (JSONObject item : list)
			{
				item.put("service_type_caption", SysDictionary.SERVICE_TYPE_CAPTION_MAP.get(item.optString("service_type")));
				item.put("oper_type_caption", OPER_TYPE_CAPTION_MAP.get(item.optString("oper_type")));
				item.put("finish_status_caption", SysDictionary.THIRD_PAY_STATUS_CAPTION_MAP.get(item.optString("finish_status")));
			}
		}
		else
		{
			list = new ArrayList<JSONObject>();
		}

		// 设置返回分页信息
		pagination.setPageno(pagination.getPageno() > 0 ? pagination.getPageno() : 1);
		pagination.setTotalcount(totalCount);
		if (0 == pagination.getPagesize())
		{
			pagination.setPagesize(list.size());
		}

		Data result = param.clone();
		result.setPagination(pagination);
		result.setData(list);
		return result;
	}

	@Override
	public Data thirdPaymentRefundRepeat(Data param) throws Exception
	{
		// TODO 自动生成的方法存根
		String tenancyId = param.getTenancy_id();
		Integer storeId = param.getStore_id();

		List<?> dataList = param.getData();
		if (null == dataList || 0 >= dataList.size() || dataList.isEmpty())
		{
			throw new SystemException(PosErrorCode.PARAM_ERROR);
		}

		JSONObject paramJson = JSONObject.fromObject(dataList.get(0));
		String optNum = ParamUtil.getStringValueByObject(paramJson, "opt_num");
		String posNum = ParamUtil.getStringValueByObject(paramJson, "pos_num");
		String outRequestNo = ParamUtil.getStringValueByObject(paramJson, "out_request_no");

		// 根据参数查询退款异常记录
		PosThirdPaymentRefundEntity entity = paymentDao.getPosThirdPaymentRefund(tenancyId, storeId, outRequestNo);
		if(null == entity)
		{
			param.setCode(Constant.CODE_NULL_DATASET);
			param.setMsg("退款异常记录为空!");
			return param;
		}
		
		if (SysDictionary.THIRD_PAY_STATUS_REFUND_SUCCESS.equals(entity.getFinish_status()) || SysDictionary.THIRD_PAY_STATUS_REVOKED_SUCCESS.equals(entity.getFinish_status()))
		{
			param.setCode(Constant.CODE_SUCCESS);
			return param;
		}

		JSONObject extraJson = null;
		if (Tools.hv(entity.getExtra()))
		{
			extraJson = JSONObject.fromObject(entity.getExtra());
		}

		Data resultData = null;
		if (null != extraJson && SysDictionary.CHANEL_WSP17.equals(extraJson.optString("payment_source")))
		{
			//支付渠道为微生活小程序,请求微生活小程序退款接口
			resultData = this.thirdPaymentRefundRepeatForWlifeProgram(tenancyId, storeId, posNum, optNum, entity);
		}
		else
		{
			// 根据不同的付款类型,请求查询退款状态,
			switch (entity.getPayment_class())
			{
				case SysDictionary.PAYMENT_CLASS_ALI_PAY:
				case SysDictionary.PAYMENT_CLASS_WECHAT_PAY:
                case SysDictionary.PAYMENT_DDM_QUICK_PAY:
				case SysDictionary.PAYMENT_CLASS_SYNCRETIC_PAY:
				case SysDictionary.PAYMENT_CLASS_FLASH_PAY:
				case SysDictionary.PAYMENT_CLASS_JD_PAY:
					resultData = this.thirdPaymentRefundRepeatForThirdPay(tenancyId, storeId, posNum, optNum, entity);
					break;

				case SysDictionary.PAYMENT_CLASS_CARD:
					resultData = this.thirdPaymentRefundRepeatForCard(tenancyId, storeId, posNum, optNum, entity);
					break;

				case SysDictionary.PAYMENT_CLASS_CARD_CREDIT:
					resultData = this.thirdPaymentRefundRepeatForCredit(tenancyId, storeId, posNum, optNum, entity);
					break;

				case SysDictionary.PAYMENT_CLASS_COUPONS:
					resultData = this.thirdPaymentRefundRepeatForCoupons(tenancyId, storeId, posNum, optNum, entity);
					break;

				case SysDictionary.PAYMENT_CLASS_DEBIT_PAY:
					resultData = this.thirdPaymentRefundRepeatForDebitPay(tenancyId, storeId, posNum, optNum, entity);
					break;

				case SysDictionary.PAYMENT_CLASS_INCORPORATION:
					resultData = this.thirdPaymentRefundRepeatForIncorporation(tenancyId, storeId, posNum, optNum, entity);
					break;

				case SysDictionary.PAYMENT_CLASS_MEIDA_COUPONS_PAY:
					resultData = this.thirdPaymentRefundRepeatForMeidaCoupons(tenancyId, storeId, posNum, optNum, entity);
					break;

				case SysDictionary.PAYMENT_CLASS_WLIFE_BALANCE:
				case SysDictionary.PAYMENT_CLASS_WLIFE_COUPON:
				case SysDictionary.PAYMENT_CLASS_WLIFE_CREDIT:
					resultData = this.thirdPaymentRefundRepeatForWlife(tenancyId, storeId, posNum, optNum, entity);
					break;
				default:
					resultData = param.clone();
					resultData.setCode(PosErrorCode.PAYMENT_NOT_SUPPORT_REFUND_REPEAT_ERROR.getNumber());
					resultData.setMsg(PosErrorCode.PAYMENT_NOT_SUPPORT_REFUND_REPEAT_ERROR.getMessage());
					break;
			}
		}

		return resultData.set(param);
	}

	/** 第三方支付退款重试
	 * @param tenancyId
	 * @param storeId
	 * @param posNum
	 * @param optNum
	 * @param entity
	 * @return 
	 * @throws Exception
	 */
	private Data thirdPaymentRefundRepeatForThirdPay(String tenancyId, Integer storeId, String posNum, String optNum, PosThirdPaymentRefundEntity entity) throws Exception
	{
		DBContextHolder.setTenancyid(tenancyId);
		ThirdPaymentService paymentService = (ThirdPaymentService) SpringConext.getApplicationContext().getBean(ThirdPaymentService.NAME);

		// 根据退款状态判断,如果退款成功,返回退款成功
		Data queryResultData = paymentService.queryPayment(tenancyId, storeId, entity.getOut_request_no());
		if (Constant.CODE_SUCCESS == queryResultData.getCode())
		{
			JSONObject responseJson = JSONObject.fromObject(queryResultData.getData().get(0));
			String orderState = responseJson.optString("payment_state");
			if (SysDictionary.THIRD_PAY_STATUS_REFUND_SUCCESS.equals(orderState) || SysDictionary.THIRD_PAY_STATUS_REVOKED_SUCCESS.equals(orderState))
			{
				//退款成功,修改退款记录状态,并返回成功
				entity.setTrade_no(responseJson.optString("transaction_no"));
				entity.setLast_updatetime(DateUtil.currentTimestamp());
				entity.setLast_operator(optNum);
				entity.setRetry_count(entity.getRetry_count() + 1);
				entity.setFinish_status(orderState);
				paymentDao.updateThirdPayOrderRefundForRetry(tenancyId, storeId, entity);
				
				Data resultData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
				resultData.setMsg(Constant.THIRD_PAYMENT_REFUND_RETRY.concat(Constant.CODE_SUCCESS_MSG));
				return resultData;
			}
		}

		// 如果退款失败,则重新发起退款
		JSONObject paramJson = new JSONObject();
		paramJson.put("order_no", entity.getOut_request_no());
		paramJson.put("order_num", entity.getOrder_num());
		paramJson.put("settle_amount", entity.getSettle_amount());// 需退款金额
		paramJson.put("total_amount", entity.getTotal_amount());// 需退款金额
		paramJson.put("payment_id", entity.getPayment_id());
		paramJson.put("service_type", entity.getService_type());
		paramJson.put("channel", entity.getChannel());
		paramJson.put("report_date", DateUtil.formatDate(entity.getReport_date()));
		paramJson.put("shift_id", entity.getShift_id());
		paramJson.put("pos_num", posNum);
		paramJson.put("opt_num", optNum);
		paramJson.put("client_ip", "");
		paramJson.put("currency_name", "");
		paramJson.put("oper_type", entity.getOper_type());
		paramJson.put("refunds_order", entity.getForm_trade_no());

		Data cancelResultData = paymentService.cancelPayment(tenancyId, storeId, paramJson);
		
		String transactionNo = null;
		String orderState = SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL;
		Integer resultCode=cancelResultData.getCode();
		String resultMsg =cancelResultData.getMsg();
		if (Constant.CODE_SUCCESS == resultCode.intValue())
		{
			JSONObject responseJson = JSONObject.fromObject(cancelResultData.getData().get(0));
			orderState = responseJson.optString("payment_state");
			if (SysDictionary.THIRD_PAY_STATUS_REFUND_SUCCESS.equals(orderState) || SysDictionary.THIRD_PAY_STATUS_REVOKED_SUCCESS.equals(orderState))
			{
				transactionNo = responseJson.optString("transaction_no");
				resultMsg = Constant.THIRD_PAYMENT_REFUND_RETRY.concat(Constant.CODE_SUCCESS_MSG);
			}
			else
			{
				resultCode = Constant.CODE_PARAM_FAILURE;
				resultMsg = responseJson.containsKey("failure_msg") ? responseJson.optString("failure_msg") : cancelResultData.getMsg();
			}
		}
		else if (Constant.CODE_CONN_EXCEPTION == resultCode.intValue())
		{
			resultMsg = Constant.CODE_CONN_EXCEPTION_MSG;
		}
		else if (Constant.THIRD_PAYMENT_ORDER_NOT_EXIST_CODE == resultCode.intValue())
		{
			orderState = SysDictionary.THIRD_PAY_STATUS_REVOKED_SUCCESS;
			resultCode = Constant.CODE_SUCCESS;
			resultMsg = Constant.THIRD_PAYMENT_REFUND_RETRY.concat(Constant.CODE_SUCCESS_MSG);
		}
		
		//退款成功,修改退款记录状态,并返回成功
		entity.setTrade_no(transactionNo);
		entity.setLast_updatetime(DateUtil.currentTimestamp());
		entity.setLast_operator(optNum);
		entity.setRetry_count(entity.getRetry_count() + 1);
		entity.setFinish_status(orderState);
		paymentDao.updateThirdPayOrderRefundForRetry(tenancyId, storeId, entity);
		
		Data resultData = Data.get(tenancyId, storeId, resultCode);
		resultData.setMsg(resultMsg);
		return resultData;
	}

	/**会员卡退款重试
	 * @param tenancyId
	 * @param storeId
	 * @param posNum
	 * @param optNum
	 * @param entity
	 * @throws Exception
	 */
	private Data thirdPaymentRefundRepeatForCard(String tenancyId, Integer storeId, String posNum, String optNum, PosThirdPaymentRefundEntity entity) throws Exception
	{
		entity.setLast_updatetime(DateUtil.currentTimestamp());
		entity.setLast_operator(optNum);
		entity.setRetry_count(entity.getRetry_count() + 1);

		DBContextHolder.setTenancyid(tenancyId);
		CustomerCardConsumeService customerService = (CustomerCardConsumeService) SpringConext.getApplicationContext().getBean(CustomerCardConsumeService.NAME);

		// 根据退款状态判断,如果退款成功,返回退款成功

		// 如果退款失败,则重新发起退款
		JSONObject param = JSONObject.fromObject(entity.getExtra());
		param.put("ischeck", "N");
		Data cancelResultData = Data.get();
		customerService.customerCardCancelConsumePost(tenancyId, storeId, param, cancelResultData, "N", entity.getOper_type());

		String transactionNo = null;
		String orderState = SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL;
		Integer resultCode = cancelResultData.getCode();
		String resultMsg = cancelResultData.getMsg();
		if (Constant.CODE_SUCCESS == resultCode.intValue() || CrmErrorCode.REVOKED_TRADING_ERROR.getNumber() == resultCode.intValue())
		{
			JSONObject responseJson = JSONObject.fromObject(cancelResultData.getData().get(0));
			transactionNo = responseJson.optString("bill_code");
			orderState = SysDictionary.THIRD_PAY_STATUS_REFUND_SUCCESS;
			resultMsg = Constant.THIRD_PAYMENT_REFUND_RETRY.concat(Constant.CODE_SUCCESS_MSG);
		}
		else if (Constant.CODE_CONN_EXCEPTION == resultCode.intValue())
		{
			resultMsg = Constant.CODE_CONN_EXCEPTION_MSG;
		}

		// 退款成功,修改退款记录状态,并返回成功
		entity.setTrade_no(transactionNo);
		entity.setFinish_status(orderState);
		paymentDao.updateThirdPayOrderRefundForRetry(tenancyId, storeId, entity);

		Data resultData = Data.get(tenancyId, storeId, resultCode);
		resultData.setMsg(resultMsg);
		return resultData;
	}

	/** 会员积分退款重试
	 * @param tenancyId
	 * @param storeId
	 * @param posNum
	 * @param optNum
	 * @param entity
	 * @throws Exception
	 */
	private Data thirdPaymentRefundRepeatForCredit(String tenancyId, Integer storeId, String posNum, String optNum, PosThirdPaymentRefundEntity entity) throws Exception
	{
		entity.setLast_updatetime(DateUtil.currentTimestamp());
		entity.setLast_operator(optNum);
		entity.setRetry_count(entity.getRetry_count() + 1);

		DBContextHolder.setTenancyid(tenancyId);
		CustomerCreditService creditService = (CustomerCreditService) SpringConext.getApplicationContext().getBean(CustomerCreditService.NAME);
		
		// 根据退款状态判断,如果退款成功,返回退款成功

		// 如果退款失败,则重新发起退款
		JSONObject param = JSONObject.fromObject(entity.getExtra());
		Data cancelResultData = Data.get();
		creditService.customerCreditCancelConsume(tenancyId, storeId, param, cancelResultData, entity.getOper_type());
		
		String orderState = SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL;
		Integer resultCode = cancelResultData.getCode();
		String resultMsg = cancelResultData.getMsg();
		if (Constant.CODE_SUCCESS == resultCode.intValue())
		{
			orderState = SysDictionary.THIRD_PAY_STATUS_REFUND_SUCCESS;
			resultMsg = Constant.THIRD_PAYMENT_REFUND_RETRY.concat(Constant.CODE_SUCCESS_MSG);
		}
		else if (Constant.CODE_CONN_EXCEPTION == resultCode.intValue())
		{
			resultMsg = Constant.CODE_CONN_EXCEPTION_MSG;
		}

		// 退款成功,修改退款记录状态,并返回成功
		entity.setFinish_status(orderState);
		paymentDao.updateThirdPayOrderRefundForRetry(tenancyId, storeId, entity);

		Data resultData = Data.get(tenancyId, storeId, resultCode);
		resultData.setMsg(resultMsg);
		return resultData;
	}

	/**优惠券退款重试
	 * @param tenancyId
	 * @param storeId
	 * @param posNum
	 * @param optNum
	 * @param entity
	 * @throws Exception
	 */
	private Data thirdPaymentRefundRepeatForCoupons(String tenancyId, Integer storeId, String posNum, String optNum, PosThirdPaymentRefundEntity entity) throws Exception
	{
		entity.setLast_updatetime(DateUtil.currentTimestamp());
		entity.setLast_operator(optNum);
		entity.setRetry_count(entity.getRetry_count() + 1);

		DBContextHolder.setTenancyid(tenancyId);
		CustomerCouponService couponService = (CustomerCouponService) SpringConext.getApplicationContext().getBean(CustomerCouponService.NAME);
		// 根据退款状态判断,如果退款成功,返回退款成功

		// 如果退款失败,则重新发起退款
		JSONObject param = JSONObject.fromObject(entity.getExtra());
		Data cancelResultData = Data.get();
		couponService.customerCouponCancelConsume(tenancyId, storeId, param, cancelResultData, entity.getOper_type());
		
		String orderState = SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL;
		Integer resultCode = cancelResultData.getCode();
		String resultMsg = cancelResultData.getMsg();
		if (Constant.CODE_SUCCESS == resultCode.intValue())
		{
			orderState = SysDictionary.THIRD_PAY_STATUS_REFUND_SUCCESS;
			resultMsg = Constant.THIRD_PAYMENT_REFUND_RETRY.concat(Constant.CODE_SUCCESS_MSG);
		}
		else if (Constant.CODE_CONN_EXCEPTION == resultCode.intValue())
		{
			resultMsg = Constant.CODE_CONN_EXCEPTION_MSG;
		}

		// 退款成功,修改退款记录状态,并返回成功
		entity.setFinish_status(orderState);
		paymentDao.updateThirdPayOrderRefundForRetry(tenancyId, storeId, entity);

		Data resultData = Data.get(tenancyId, storeId, resultCode);
		resultData.setMsg(resultMsg);
		return resultData;
	}

	/** 签账卡退款重试
	 * @param tenancyId
	 * @param storeId
	 * @param posNum
	 * @param optNum
	 * @param entity
	 * @throws Exception
	 */
	private Data thirdPaymentRefundRepeatForDebitPay(String tenancyId, Integer storeId, String posNum, String optNum, PosThirdPaymentRefundEntity entity) throws Exception
	{
		entity.setLast_updatetime(DateUtil.currentTimestamp());
		entity.setLast_operator(optNum);
		entity.setRetry_count(entity.getRetry_count() + 1);

		DBContextHolder.setTenancyid(tenancyId);
		CustomerService debitPayService = (CustomerService) SpringConext.getApplicationContext().getBean(CustomerService.NAME);

		// 根据退款状态判断,如果退款成功,返回退款成功

		// 如果退款失败,则重新发起退款
		JSONObject param = JSONObject.fromObject(entity.getExtra());
		
		List<JSONObject> requestList = new ArrayList<JSONObject>();
		requestList.add(param);

		Data requestData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
		requestData.setType(Type.DEBIT_CARD);
		requestData.setOper(Oper.update);
		requestData.setData(requestList);
		
		Data cancelResultData = Data.get();
		debitPayService.commonPost(JSONObject.fromObject(requestData).toString(), cancelResultData);
		
		String transactionNo = null;
		String orderState = SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL;
		Integer resultCode = cancelResultData.getCode();
		String resultMsg = cancelResultData.getMsg();
		if (Constant.CODE_SUCCESS == resultCode.intValue())
		{
			JSONObject responseJson = JSONObject.fromObject(cancelResultData.getData().get(0));
			transactionNo = responseJson.optString("bill_code");
			orderState = SysDictionary.THIRD_PAY_STATUS_REFUND_SUCCESS;
			resultMsg = Constant.THIRD_PAYMENT_REFUND_RETRY.concat(Constant.CODE_SUCCESS_MSG);
		}
		else if (Constant.CODE_CONN_EXCEPTION == resultCode.intValue())
		{
			resultMsg = Constant.CODE_CONN_EXCEPTION_MSG;
		}

		// 退款成功,修改退款记录状态,并返回成功
		entity.setTrade_no(transactionNo);
		entity.setFinish_status(orderState);
		paymentDao.updateThirdPayOrderRefundForRetry(tenancyId, storeId, entity);

		Data resultData = Data.get(tenancyId, storeId, resultCode);
		resultData.setMsg(resultMsg);
		return resultData;
	}

	/** 团体挂账退款重试
	 * @param tenancyId
	 * @param storeId
	 * @param posNum
	 * @param optNum
	 * @param entity
	 * @throws Exception
	 */
	private Data thirdPaymentRefundRepeatForIncorporation(String tenancyId, Integer storeId, String posNum, String optNum, PosThirdPaymentRefundEntity entity) throws Exception
	{
		entity.setLast_updatetime(DateUtil.currentTimestamp());
		entity.setLast_operator(optNum);
		entity.setRetry_count(entity.getRetry_count() + 1);

		DBContextHolder.setTenancyid(tenancyId);
		CustomerIncorporationService incorporationService = (CustomerIncorporationService) SpringConext.getApplicationContext().getBean(CustomerIncorporationService.NAME);

		// 根据退款状态判断,如果退款成功,返回退款成功

		// 如果退款失败,则重新发起退款
		JSONObject param = JSONObject.fromObject(entity.getExtra());
		Data cancelResultData = Data.get();
		incorporationService.customerIncorporationCancelPost(tenancyId, storeId, param, cancelResultData, entity.getOper_type());
		
		String transactionNo = null;
		String orderState = SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL;
		Integer resultCode = cancelResultData.getCode();
		String resultMsg = cancelResultData.getMsg();
		if (Constant.CODE_SUCCESS == resultCode.intValue())
		{
			JSONObject responseJson = JSONObject.fromObject(cancelResultData.getData().get(0));
			transactionNo = responseJson.optString("bill_code");
			orderState = SysDictionary.THIRD_PAY_STATUS_REFUND_SUCCESS;
			resultMsg = Constant.THIRD_PAYMENT_REFUND_RETRY.concat(Constant.CODE_SUCCESS_MSG);
		}
		else if (Constant.CODE_CONN_EXCEPTION == resultCode.intValue())
		{
			resultMsg = Constant.CODE_CONN_EXCEPTION_MSG;
		}

		// 退款成功,修改退款记录状态,并返回成功
		entity.setTrade_no(transactionNo);
		entity.setFinish_status(orderState);
		paymentDao.updateThirdPayOrderRefundForRetry(tenancyId, storeId, entity);

		Data resultData = Data.get(tenancyId, storeId, resultCode);
		resultData.setMsg(resultMsg);
		return resultData;
	}

	/**美团券退款重试
	 * @param tenancyId
	 * @param storeId
	 * @param posNum
	 * @param optNum
	 * @param entity
	 * @throws Exception
	 */
	private Data thirdPaymentRefundRepeatForMeidaCoupons(String tenancyId, Integer storeId, String posNum, String optNum, PosThirdPaymentRefundEntity entity) throws Exception
	{
		entity.setLast_updatetime(DateUtil.currentTimestamp());
		entity.setLast_operator(optNum);
		entity.setRetry_count(entity.getRetry_count() + 1);

		DBContextHolder.setTenancyid(tenancyId);
		MeituanCouponsService meituanCouponService = (MeituanCouponsService) SpringConext.getApplicationContext().getBean(MeituanCouponsService.NAME);
		
		// 根据退款状态判断,如果退款成功,返回退款成功

		// 如果退款失败,则重新发起退款
		JSONObject param = JSONObject.fromObject(entity.getExtra());
		Data cancelResultData = meituanCouponService.couponsCancel(tenancyId, storeId, param);
		
		String orderState = SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL;
		Integer resultCode = cancelResultData.getCode();
		String resultMsg = cancelResultData.getMsg();
		if (Constant.CODE_SUCCESS == resultCode.intValue())
		{
			orderState = SysDictionary.THIRD_PAY_STATUS_REFUND_SUCCESS;
			resultMsg = Constant.THIRD_PAYMENT_REFUND_RETRY.concat(Constant.CODE_SUCCESS_MSG);
		}
		else if (Constant.CODE_CONN_EXCEPTION == resultCode.intValue())
		{
			resultMsg = Constant.CODE_CONN_EXCEPTION_MSG;
		}

		// 退款成功,修改退款记录状态,并返回成功
		entity.setFinish_status(orderState);
		paymentDao.updateThirdPayOrderRefundForRetry(tenancyId, storeId, entity);

		Data resultData = Data.get(tenancyId, storeId, resultCode);
		resultData.setMsg(resultMsg);
		return resultData;
	}

	/** 微生活会员退款重试
	 * @param tenancyId
	 * @param storeId
	 * @param posNum
	 * @param optNum
	 * @param entity
	 * @return 
	 * @throws Exception
	 */
	private Data thirdPaymentRefundRepeatForWlife(String tenancyId, Integer storeId, String posNum, String optNum, PosThirdPaymentRefundEntity entity) throws Exception
	{
		entity.setLast_updatetime(DateUtil.currentTimestamp());
		entity.setLast_operator(optNum);
		entity.setRetry_count(entity.getRetry_count() + 1);
		
		DBContextHolder.setTenancyid(tenancyId);
		AcewillCustomerService customerService = (AcewillCustomerService) SpringConext.getApplicationContext().getBean(AcewillCustomerService.NAME);
		
		// 根据退款状态判断,如果退款成功,返回退款成功
		JSONObject param = new JSONObject();
		param.put("bill_code", entity.getForm_trade_no());
//		ArrayList<JSONObject> dataList = new ArrayList<JSONObject>();
//		dataList.add(param);
//		Data requestData = Data.get();
//		requestData.setData(dataList);
		Data queryResultData = customerService.getAcewillCustomerView(tenancyId, storeId, param);
		
		if (Constant.CODE_SUCCESS == queryResultData.getCode())
		{
			String status = null;
			String type = null;
			if (null != queryResultData && null != queryResultData.getData() && 0 < queryResultData.getData().size())
			{
				JSONObject queryResultJson = JSONObject.fromObject(queryResultData.getData().get(0));
				// 1：预消费 2：成功消费
				status = ParamUtil.getStringValueByObject(queryResultJson, "status");
				// 2:消费 3:撤销消费(撤销冲减记录) 4:被撤销消费(撤销原记录)
				type = ParamUtil.getStringValueByObject(queryResultJson, "type");
			}

			if ("1".equals(status))
			{
				entity.setFinish_status(SysDictionary.THIRD_PAY_STATUS_REVOKED_SUCCESS);
				paymentDao.updateThirdPayOrderRefundForRetry(tenancyId, storeId, entity);

				Data resultData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
				resultData.setMsg(Constant.THIRD_PAYMENT_REFUND_RETRY.concat(Constant.CODE_SUCCESS_MSG));
				return resultData;
			}
			else if ("4".equals(type))
			{
				entity.setFinish_status(SysDictionary.THIRD_PAY_STATUS_REFUND_SUCCESS);
				paymentDao.updateThirdPayOrderRefundForRetry(tenancyId, storeId, entity);

				Data resultData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
				resultData.setMsg(Constant.THIRD_PAYMENT_REFUND_RETRY.concat(Constant.CODE_SUCCESS_MSG));
				return resultData;
			}
		}
		
		// 如果退款失败,则重新发起退款
		JSONObject paramJson = new JSONObject();
		paramJson.put( "business_date",DateUtil.formatDate(entity.getReport_date()));
		paramJson.put( "shift_id",entity.getShift_id());
		paramJson.put( "operator_id",optNum);
		paramJson.put( "pos_num",posNum);
		paramJson.put( "chanel",entity.getChannel());
		paramJson.put( "service_type",entity.getService_type());
//		paramJson.put( "card_code","");
		paramJson.put( "trade_amount",entity.getSettle_amount());
//		paramJson.put( "trade_credit");
		paramJson.put( "third_bill_code",entity.getOrder_num());
		paramJson.put( "bill_code",entity.getForm_trade_no());
		paramJson.put("total_amount",entity.getTotal_amount());
		
		Data cancelResultData = customerService.cancelAcewillCustomerDeal(tenancyId, storeId, paramJson, entity.getOper_type());
		
		String orderState = SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL;
		Integer resultCode = cancelResultData.getCode();
		String resultMsg = cancelResultData.getMsg();
		if (Constant.CODE_SUCCESS == resultCode.intValue())
		{
			orderState = SysDictionary.THIRD_PAY_STATUS_REFUND_SUCCESS;
			resultMsg = Constant.THIRD_PAYMENT_REFUND_RETRY.concat(Constant.CODE_SUCCESS_MSG);
		}
		else if (Constant.CODE_CONN_EXCEPTION == resultCode.intValue())
		{
			resultMsg = Constant.CODE_CONN_EXCEPTION_MSG;
		}
		else
		{
			resultCode = Constant.CODE_PARAM_FAILURE;
			resultMsg = cancelResultData.getMsg();
		}
		
		entity.setFinish_status(orderState);
		paymentDao.updateThirdPayOrderRefundForRetry(tenancyId, storeId, entity);
		
		Data resultData = Data.get(tenancyId, storeId, resultCode);
		resultData.setMsg(resultMsg);
		return resultData;
	}
	
	/** 微生活小程序退款
	 * @param tenancyId
	 * @param storeId
	 * @param posNum
	 * @param optNum
	 * @param entity
	 * @return
	 * @throws Exception
	 */
	private Data thirdPaymentRefundRepeatForWlifeProgram(String tenancyId, Integer storeId, String posNum, String optNum, PosThirdPaymentRefundEntity entity) throws Exception
	{
		entity.setLast_updatetime(DateUtil.currentTimestamp());
		entity.setLast_operator(optNum);
		entity.setRetry_count(entity.getRetry_count() + 1);
		
		DBContextHolder.setTenancyid(tenancyId);
		WlifeService wlifeProgramService = (WlifeService) SpringConext.getApplicationContext().getBean(WlifeService.NAME);
		
		JSONObject programResult = wlifeProgramService.orderRefund(tenancyId, storeId, entity.getOut_request_no());
	
		String orderState = SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL;
		Integer resultCode = programResult.optInt("errcode");
		String resultMsg = programResult.optString("errmsg");
		if (Constant.CODE_SUCCESS == resultCode.intValue())
		{
			orderState = SysDictionary.THIRD_PAY_STATUS_REFUND_SUCCESS;
			resultMsg = Constant.THIRD_PAYMENT_REFUND_RETRY.concat(Constant.CODE_SUCCESS_MSG);
		}
		else if (-1 == resultCode.intValue())
		{
			resultCode = Constant.CODE_CONN_EXCEPTION;
			resultMsg = Constant.CODE_CONN_EXCEPTION_MSG;
		}
		else
		{
			resultCode = Constant.CODE_PARAM_FAILURE;
		}
		
		entity.setFinish_status(orderState);
		paymentDao.updateThirdPayOrderRefundForRetry(tenancyId, storeId, entity);
		
		Data resultData = Data.get(tenancyId, storeId, resultCode);
		resultData.setMsg(resultMsg);
		return resultData;
	}

}
