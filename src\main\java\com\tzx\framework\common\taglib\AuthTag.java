package com.tzx.framework.common.taglib;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import javax.servlet.jsp.JspException;
import javax.servlet.jsp.tagext.TagSupport;
import java.util.Map;

public class AuthTag extends TagSupport
{
	/**
	 * 
	 */
	private static final long	serialVersionUID	= 1L;
	private String mid;

	private String moduleurl;
	
    private HttpServletRequest request;
	    
	private HttpSession session;  
	    
	public String getModuleurl()
	{
		return moduleurl;
	}

	public void setModuleurl(String moduleurl)
	{
		this.moduleurl = moduleurl;
	}
	
	 public String getMid()
	{
		return mid;
	}

	public void setMid(String mid)
	{
		this.mid = mid;
	}

	public void init() 
	 {  
    	request = (HttpServletRequest) pageContext.getRequest();
    	session = request.getSession();
	} 
	
	@Override
	public int doStartTag() throws JspException
	{
		if (moduleurl == null || "".equals(moduleurl)) {
			return EVAL_PAGE;
		}
		init();
		int store_id = Integer.parseInt(session.getAttribute("organ_id")==null?"0":session.getAttribute("organ_id").toString());
		if(0!=store_id)
		{
			@SuppressWarnings("unchecked")
			Map<Integer,Integer> map=(Map<Integer, Integer>)this.session.getAttribute("moduleMap");
			if(map.containsKey(moduleurl))
			{
				return EVAL_BODY_INCLUDE;
			}
			else
			{
				return SKIP_BODY;
			}
		}
		return EVAL_BODY_INCLUDE;
		
	}
}
