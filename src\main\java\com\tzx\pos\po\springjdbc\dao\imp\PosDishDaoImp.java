package com.tzx.pos.po.springjdbc.dao.imp;

import java.lang.reflect.Method;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tzx.framework.common.util.CommonUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.tzx.base.bo.PosCodeService;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.Scm;
import com.tzx.framework.common.util.Tools;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.dao.imp.BaseDaoImp;
import com.tzx.pos.base.util.CacheTableUtil;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.bo.dto.PosBill;
import com.tzx.pos.bo.dto.PosBillBatch;
import com.tzx.pos.bo.dto.PosBillBatchs;
import com.tzx.pos.bo.dto.PosBillBooked;
import com.tzx.pos.bo.dto.PosBillBookedItem;
import com.tzx.pos.bo.dto.PosBillItem;
import com.tzx.pos.bo.dto.PosBillMember;
import com.tzx.pos.bo.dto.PosBillMembers;
import com.tzx.pos.bo.dto.PosBillPaymentWay;
import com.tzx.pos.bo.dto.PosBillPaymentWays;
import com.tzx.pos.bo.dto.PosBillService;
import com.tzx.pos.bo.dto.PosBillServiceEntity;
import com.tzx.pos.bo.dto.PosBillServices;
import com.tzx.pos.bo.dto.PosItemMethod;
import com.tzx.pos.bo.dto.PosLog;
import com.tzx.pos.bo.dto.PosOpenTableBill;
import com.tzx.pos.bo.dto.PosOrderDishItem;
import com.tzx.pos.po.springjdbc.dao.PosDishDao;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

/**
 * 
 * <AUTHOR> 2015年6月24日-下午7:24:30
 */
@Repository(PosDishDao.NAME)
public class PosDishDaoImp extends BaseDaoImp implements PosDishDao
{
	private static final Logger	logger	= Logger.getLogger(PosDishDaoImp.class);

	@Override
	public void insertPosBill(String tenancyId, int storeId, Date reportDate, int shiftId, String billNum, String batchNum, String serialNum, String orderNum, Timestamp opentableTime, String openPosNum, String openOpt, String waiterNum, String tableCode, int guest, int itemMenuId, String saleMode,
			String source, int serviceId, double serviceAmount, String copyBillNum, String remark, String billProperty, String paymentState, int discountModeId, double discountkAmount,double shopRealAmount,double platformChargeAmount,String settlementType,double discountRate,String fictitiousTable,String dinnerType) throws Exception
	{
		double total_fees=0d;
		StringBuffer saveBill = new StringBuffer(
				"insert into pos_bill (tenancy_id,store_id,report_date,shift_id,bill_num,batch_num,serial_num,order_num,opentable_time,open_pos_num,open_opt,waiter_num,table_code,guest,item_menu_id,sale_mode,source,service_id,service_amount,copy_bill_num,remark,bill_property,payment_state,discount_mode_id,discountk_amount,shop_real_amount,platform_charge_amount,total_fees,settlement_type,discount_rate,fictitious_table,dinner_type) ");
		saveBill.append(" values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");

		this.update(saveBill.toString(), new Object[]
		{ tenancyId,storeId,reportDate,shiftId,billNum,batchNum,serialNum,orderNum,opentableTime,openPosNum,openOpt,waiterNum,tableCode,guest,itemMenuId,saleMode,source,serviceId,serviceAmount,copyBillNum,remark,billProperty,paymentState,discountModeId,discountkAmount,shopRealAmount,platformChargeAmount,total_fees,settlementType,discountRate,fictitiousTable,dinnerType});
	}

	@Override
	public  List<JSONObject> openTable(String tenantId, Integer organId, String bill_num,String stable) throws SystemException
	{
		// 查询账单信息
		StringBuffer sql0 = new StringBuffer("SELECT fictitious_table table_code ,table_code as stable,bill_num,opentable_time,service_id,service_amount,discount_case_id,discountk_amount,discountr_amount,");
		sql0.append("maling_amount,guest,subtotal, average_amount,payment_amount,givi_amount,print_count,waiter_num,dinner_type,sale_mode ");
		sql0.append(" FROM pos_bill as ps ");
		sql0.append(" where ps.bill_num = ? and ps.store_id = ? order by bill_num asc ");

		PosOpenTableBill posBill = this.jdbcTemplate.queryForObject(sql0.toString(), new Object[]
		{ bill_num, organId }, BeanPropertyRowMapper.newInstance(PosOpenTableBill.class));
		if (posBill != null)
		{
			if (StringUtils.isNotEmpty(posBill.getService_id())&&Integer.parseInt(posBill.getService_id())>0)
			{
				StringBuffer serSql = new StringBuffer("select name from hq_service_fee_type hsft left join hq_service_fee_of_organ hsfoo on hsft.id = hsfoo.service_fee_id ");
				serSql.append("where hsft.id = " + posBill.getService_id() + " and hsfoo.store_id = " + organId);
				SqlRowSet srs = this.jdbcTemplate.queryForRowSet(serSql.toString());
				while (srs.next())
				{
					posBill.setService_name(srs.getString("name"));
				}
			}

			if (StringUtils.isNotEmpty(posBill.getDiscount_case_id()))
			{
				StringBuffer caseSql = new StringBuffer("select hdc.discount_case_name from hq_discount_case hdc left join hq_discount_case_org hdco on hdc.id = hdco.discount_case_id  ");
				caseSql.append("where hdc.id = " + posBill.getDiscount_case_id() + " and hdco.store_id = " + organId);
				SqlRowSet srs = this.jdbcTemplate.queryForRowSet(caseSql.toString());
				while (srs.next())
				{
					posBill.setDiscount_case_name(srs.getString("discount_case_name"));
				}
			}
		}
		if(StringUtils.isEmpty(stable)){
			posBill.setStable(stable);
		}
		List<JSONObject> list = new ArrayList<JSONObject>();
		list.add(JSONObject.fromObject(posBill));
		
//		//数据上传
//		DataUploadRunnable r = new DataUploadRunnable(tenantId,organId+"","",stable,"",bill_num);
//		Thread thread  =  new Thread(r);
//		thread.start();
		
		return list;
	}

	@Override
	public synchronized Data orderDish(String tenancyid, String billno, Integer organId, String mode) throws SystemException
	{
		Data data = new Data();

		/**
		 * 下面查询账单信息并返回
		 */
		StringBuffer posbill = new StringBuffer("select pb.bill_state, table_code,bill_num,opentable_time,hsft.name as service_name,round(service_amount, 2) as service_amount,hdc.discount_case_name,round(discountk_amount,2) as discountk_amount,round(discountr_amount,2) as discountr_amount, ");
		posbill.append(" round(maling_amount,2) as maling_amount,guest,round(subtotal,2) as subtotal,round(average_amount,2) as average_amount,round(payment_amount,2) as payment_amount, round(givi_amount,2) as givi_amount,print_count,pb.discount_case_id ");
		posbill.append(" FROM pos_bill as pb left join hq_service_fee_type as hsft on hsft.id = pb.service_id ");
		posbill.append(" left join hq_discount_case as hdc on pb.discount_case_id = hdc.id");

		posbill.append(" where pb.store_id=? and pb.bill_num=? ");
		posbill.append(" and pb.bill_property=?");
		/**
		 * 查询账单详细
		 */
		StringBuffer str = new StringBuffer(
				"select item_serial,rwid,item_id,item_num,item_name,item_remark,item_property,round(discount_rate,2) as discount_rate,round(discount_amount,2) as discount_amount,round(item_price,2) as item_price,round(item_count,2) as item_count,round(item_amount,2) as item_amount,round(real_amount,2) as real_amount,remark,print_tag, item_taste from pos_bill_item where bill_num = ? and store_id = ? ");

		String qMethod = new String("select pzi.zfkw_id as method_id,pzi.zfkw_name as method_name,pzi.amount,pbi.item_id from pos_zfkw_item pzi left join pos_bill_item pbi on pzi.rwid = pbi.rwid where pbi.bill_num = ? and pbi.store_id = ?");
		//String qTaste = new String("select pzi.zfkw_id as id,pzi.zfkw_name as name,pzi.amount,pbi.item_id from pos_zfkw_item pzi left join pos_bill_item pbi on pzi.rwid = pbi.rwid where pbi.bill_num = ? and pbi.store_id = ?");

		List<PosBill> list = null;
		List<PosBill> posBills = new ArrayList<PosBill>();
		if (mode.equals("0"))
		{
			// 未结账单
			list = this.jdbcTemplate.query(posbill.toString(), new Object[]
			{ organId, billno, "OPEN" }, BeanPropertyRowMapper.newInstance(PosBill.class));
			for (int i = 0; i < list.size(); i++)
			{
				PosBill bill = list.get(i);
				List<PosBillItem> pitems = this.jdbcTemplate.query(str.toString(), new Object[]
				{ bill.getBill_num(), organId }, BeanPropertyRowMapper.newInstance(PosBillItem.class));

				for (int j = 0; j < pitems.size(); j++)
				{
					List<PosItemMethod> methods = this.jdbcTemplate.query(qMethod, new Object[]
					{ bill.getBill_num(), organId }, BeanPropertyRowMapper.newInstance(PosItemMethod.class));
					pitems.get(j).setMethod(methods);
					//List<PosItemTaste> tastes = this.jdbcTemplate.query(qTaste, new Object[]
					//{ bill.getBill_num(), organId }, BeanPropertyRowMapper.newInstance(PosItemTaste.class));
					//pitems.get(j).setTaste(tastes);
				}

				bill.setDetaillist(pitems);
				posBills.add(bill);
			}
			data.setCode(Constant.CODE_SUCCESS);
			data.setData(posBills);
			data.setMsg(Constant.ORDER_DISH_SUCCESS);
		}
		if (mode.equals("1"))
		{
			// 未结账单
			list = this.jdbcTemplate.query(posbill.toString(), new Object[]
			{ organId, billno, "SORT" }, BeanPropertyRowMapper.newInstance(PosBill.class));
			for (int i = 0; i < list.size(); i++)
			{
				PosBill bill = list.get(i);
				List<PosBillItem> pitems = this.jdbcTemplate.query(str.toString(), new Object[]
				{ bill.getBill_num(), organId }, BeanPropertyRowMapper.newInstance(PosBillItem.class));

				for (int j = 0; j < pitems.size(); j++)
				{
					List<PosItemMethod> methods = this.jdbcTemplate.query(qMethod, new Object[]
					{ bill.getBill_num(), organId }, BeanPropertyRowMapper.newInstance(PosItemMethod.class));
					pitems.get(j).setMethod(methods);
					//List<PosItemTaste> tastes = this.jdbcTemplate.query(qTaste, new Object[]
					//{ bill.getBill_num(), organId }, BeanPropertyRowMapper.newInstance(PosItemTaste.class));
					//pitems.get(j).setTaste(tastes);
				}

				bill.setDetaillist(pitems);
				posBills.add(bill);
			}
			data.setCode(Constant.CODE_SUCCESS);
			data.setData(posBills);
			data.setMsg(Constant.PAUSE_ORDER_DISH_SUCCESS);
		}

		return data;
	}
	
	@Override
	public List<JSONObject> getSurplusDiscountAmount(String tenancyid, String billno, 
			Integer organId,  Date reportDate) throws Exception
	{
		String sql = " SELECT payment_amount,discountk_amount,( SELECT COALESCE (SUM(pbi.real_amount), 0) FROM pos_bill_item pbi WHERE pbi.tenancy_id = pb.tenancy_id" +
				"												AND pbi.store_id = pb.store_id AND pbi.bill_num = pb.bill_num AND pbi.item_property <> ?  AND pbi.discount_state = 'Y'"+
				"											  ) AS discountr_amount"+
				"  FROM  pos_bill pb WHERE  tenancy_id =? AND store_id = ? AND bill_num = ? and report_date = ? ";
		return this.query4Json(tenancyid,sql, new Object[]
				{ SysDictionary.ITEM_PROPERTY_MEALLIST, tenancyid, organId, billno, reportDate });
	}

	
	public Data newOrderDish(String tenancyid, String billno, Integer organId, String mode) throws SystemException
	{
		Data data = new Data();

//		StringBuffer posbill = new StringBuffer("select table_code,bill_num,batch_num,opentable_time,hsft.name as service_name,round(service_amount, 2) as service_amount,hdc.discount_case_name,round(discountk_amount,2) as discountk_amount,round(discountr_amount,2) as discountr_amount, ");
//		posbill.append(" round(maling_amount,2) as maling_amount,guest,round(subtotal,2) as subtotal,round(average_amount,2) as average_amount,round(payment_amount,2) as payment_amount,round(difference,2) as difference, round(givi_amount,2) as givi_amount,print_count,pb.discount_case_id ");
//		posbill.append(" from pos_bill as pb left join hq_service_fee_type as hsft on hsft.id = pb.service_id ");
//		posbill.append(" left join hq_discount_case as hdc on pb.discount_case_id = hdc.id");
//		posbill.append(" where pb.store_id=? and pb.bill_num=? and pb.bill_property=?");
		
		StringBuffer posbill = new StringBuffer();
//		posbill.append(" select pb.table_code,pb.bill_num,pb.batch_num,pb.serial_num,pb.report_date,pb.shift_id,pb.opentable_time,pb.open_pos_num,pb.open_opt,pb.payment_time,pb.pos_num,pb.cashier_num,pb.waiter_num,round(pb.difference, 2) as difference,");
//		posbill.append(" pb.service_id,hsft.name as service_name,round(pb.service_amount, 2) as service_amount,round(pb.subtotal, 2) as subtotal,round(pb.payment_amount, 2) as payment_amount,round(pb.discountk_amount, 2) as discountk_amount,");
//		posbill.append(" round(pb.discountr_amount, 2) as discountr_amount,round(pb.discount_amount, 2) as discount_amount,round(pb.maling_amount, 2) as maling_amount,round(pb.average_amount, 2) as average_amount,round(pb.givi_amount, 2) as givi_amount,");
//		posbill.append(" pb.discount_rate,pb.discount_case_id,hdc.discount_case_name, pb.discount_mode_id,pb.guest,pb.guest_msg,pb.print_count,pb.bill_state,pb.bill_property,pb.remark,pb.payment_state,pb.copy_bill_num");
//		posbill.append(" ,(select round(coalesce(sum(bi.item_amount),0),2) from pos_bill_item bi where bi.tenancy_id=pb.tenancy_id and bi.store_id=pb.store_id and bi.bill_num=pb.bill_num and (bi.item_remark<>'"+SysDictionary.ITEM_REMARK_FS02+"' or bi.item_remark is null) and bi.item_property<>'"+SysDictionary.ITEM_PROPERTY_MEALLIST+"') as subtotal2");
//		posbill.append(" from pos_bill as pb left join hq_service_fee_type as hsft on hsft.id = pb.service_id left join hq_discount_case as hdc on pb.discount_case_id = hdc.id");
//		posbill.append(" where pb.store_id=? and pb.bill_num=? and pb.bill_property=?");
		
		posbill.append(" select pb.dinner_type,pb.bill_taste,pb.order_num,pb.fictitious_table, pb.table_code,pb.bill_num,pb.batch_num,pb.serial_num,pb.report_date,pb.shift_id,pb.opentable_time,pb.open_pos_num,pb.open_opt,pb.payment_time,pb.pos_num,pb.cashier_num,pb.waiter_num,round(pb.difference, 2) as difference,");
		posbill.append(" pb.service_id,hsft.name as service_name,round(pb.service_amount, 2) as service_amount,round(pb.subtotal, 2) as subtotal,round(pb.payment_amount, 2) as payment_amount,round(pb.discountk_amount, 2) as discountk_amount,");
		posbill.append(" round(pb.discountr_amount, 2) as discountr_amount,round(pb.discount_amount, 2) as discount_amount,round(pb.maling_amount, 2) as maling_amount,round(pb.average_amount, 2) as average_amount,round(pb.givi_amount, 2) as givi_amount,");
		posbill.append(" pb.discount_rate,pb.discount_case_id,hdc.discount_case_name, pb.discount_mode_id,pb.guest,pb.guest_msg,pb.print_count,pb.bill_state,pb.bill_property,pb.remark,pb.payment_state,pb.copy_bill_num,pb.source as chanel,pb.sale_mode as sale_mode");
		posbill.append(" ,(select round(coalesce(sum(bi.item_amount),0),2) from pos_bill_item bi where bi.tenancy_id=pb.tenancy_id and bi.store_id=pb.store_id and bi.bill_num=pb.bill_num and (bi.item_remark<>'"+SysDictionary.ITEM_REMARK_FS02+"' or bi.item_remark is null) and bi.item_property<>'"+SysDictionary.ITEM_PROPERTY_MEALLIST+"') as subtotal2");
		posbill.append(" ,(select round(coalesce(sum(bi.item_amount),0),2) from pos_bill_item bi where bi.tenancy_id=pb.tenancy_id and bi.store_id=pb.store_id and bi.bill_num=pb.bill_num and bi.discount_state='N' and bi.item_property<>'"+SysDictionary.ITEM_PROPERTY_MEALLIST+"') as none_discount_amount");
        posbill.append(" ,( select COALESCE(sum(real_amount),0) from pos_bill_item pbi LEFT JOIN hq_item_info hii on pbi.item_id=hii.\"id\" where pbi.bill_num=pb.bill_num and hii.is_ticket='Y' and pbi.item_property <> 'MEALLIST') AS sum_coupon_amount");
		posbill.append(" from pos_bill as pb left join hq_service_fee_type as hsft on hsft.id = pb.service_id left join hq_discount_case as hdc on pb.discount_case_id = hdc.id");
		posbill.append(" where pb.store_id=? and pb.bill_num=? and pb.bill_property=?");

		String billProperty = "";
		if ("0".equals(mode))
		{
			billProperty = SysDictionary.BILL_PROPERTY_OPEN;
		}
		else if ("1".equals(mode))
		{
			billProperty = SysDictionary.BILL_PROPERTY_SORT;
		}
		
		List<PosBill> list = this.jdbcTemplate.query(posbill.toString(), new Object[]
		{ organId, billno, billProperty }, BeanPropertyRowMapper.newInstance(PosBill.class));
		
		List<PosBill> posBills = this.findPosBillItem(tenancyid, organId, list);
		
//		/**
//		 * 查询账单详细
//		 */
//		StringBuffer str = new StringBuffer(
//				"select item_serial,rwid,item_id,item_num,item_name,item_remark,item_property,round(discount_rate,2) as discount_rate,round(discount_amount,2) as discount_amount,round(item_price,2) as item_price,round(item_count,2) as item_count,round(item_amount,2) as item_amount,round(real_amount,2) as real_amount,remark,print_tag from pos_bill_item where bill_num = ? and store_id = ? ");
//
//		String qMethod = new String("select pzi.zfkw_id as method_id,pzi.zfkw_name as method_name,pzi.amount,pbi.item_id from pos_zfkw_item pzi left join pos_bill_item pbi on pzi.rwid = pbi.rwid where pbi.bill_num = ? and pbi.store_id = ?");
//		String qTaste = new String("select pzi.zfkw_id as id,pzi.zfkw_name as name,pzi.amount,pbi.item_id from pos_zfkw_item pzi left join pos_bill_item pbi on pzi.rwid = pbi.rwid where pbi.bill_num = ? and pbi.store_id = ?");

//		String billProperty = "";
//		if ("0".equals(mode))
//		{
//			billProperty = SysDictionary.BILL_PROPERTY_OPEN;
//		}
//		else if ("1".equals(mode))
//		{
//			billProperty = SysDictionary.BILL_PROPERTY_SORT;
//		}

		// 未结账单
		
//		for (int i = 0; i < list.size(); i++)
//		{
//			PosBill bill = list.get(i);
//			List<PosBillItem> pitems = this.jdbcTemplate.query(str.toString(), new Object[]
//			{ bill.getBill_num(), organId }, BeanPropertyRowMapper.newInstance(PosBillItem.class));
//
//			for (int j = 0; j < pitems.size(); j++)
//			{
//				List<PosItemMethod> methods = this.jdbcTemplate.query(qMethod, new Object[]
//				{ bill.getBill_num(), organId }, BeanPropertyRowMapper.newInstance(PosItemMethod.class));
//				pitems.get(j).setMethod(methods);
//				List<PosItemTaste> tastes = this.jdbcTemplate.query(qTaste, new Object[]
//				{ bill.getBill_num(), organId }, BeanPropertyRowMapper.newInstance(PosItemTaste.class));
//				pitems.get(j).setTaste(tastes);
//			}
//
//			bill.setDetaillist(pitems);
//			posBills.add(bill);
//		}
		
		data.setData(posBills);
		data.setCode(Constant.CODE_SUCCESS);
		data.setMsg(Constant.ORDER_DISH_SUCCESS);
//		//数据上传
//		DataUploadRunnable r = new DataUploadRunnable(tenancyid,organId+"","","","",billno);
//		Thread thread  =  new Thread(r);
//		thread.start();

		return data;
	}

	@Override
	public synchronized List<JSONObject> posPayment(String billno, Integer organId, JSONObject object) throws SystemException
	{
		// 查询账单付款方式
		StringBuilder payment = new StringBuilder("select id,jzid,name,amount,count,number,phone,shift_id,pos_num,cashier_num,last_updatetime,customer_id,bill_code,remark,currency_amount from pos_bill_payment where bill_num = ? and store_id = ?");
		List<PosBillPaymentWay> paymentWays = this.jdbcTemplate.query(payment.toString(), new Object[]
		{ billno, organId }, BeanPropertyRowMapper.newInstance(PosBillPaymentWay.class));
		object.put("paymentlist", paymentWays);
		List<JSONObject> list = new ArrayList<JSONObject>();
		list.add(object);

		return list;
	}

	@Override
	public List<PosBill> findPosBillItem(String tenantId, Integer organId, List<PosBill> list) throws SystemException
	{
		try {
			String itemNumPoint = CacheTableUtil.getSysParameter("ITEMNUMPOINT");
			if (Tools.isNullOrEmpty(itemNumPoint) || 4 < Integer.parseInt(itemNumPoint))
			{
				itemNumPoint = "2";
			}
			
			// 查询账单详细
//			StringBuffer str = new StringBuffer("");
//	        //"select item_unit_name,item_serial,rwid,item_id,item_num,item_name,item_remark,item_property,round(discount_rate,2) as discount_rate,round(discount_amount,2) as discount_amount,round(item_price,2) as item_price,round(item_count,2) as item_count,round(item_amount,2) as item_amount,round(real_amount,2) as real_amount,remark,item_unit_id as unit_id,details_id,item_taste,sale_mode,setmeal_id,setmeal_rwid,waitcall_tag,assist_num,assist_money,print_tag,assist_item_id,item_time,waiter_num as waiter_name,item_mac_id,item_time,assist_num,method_money,batch_num from pos_bill_item where bill_num = ? and store_id = ?");
//	
//			str.append("select bi.item_unit_name,bi.item_serial,bi.rwid,bi.item_id,bi.item_num,bi.item_name,bi.item_remark,bi.item_property,round(bi.discount_rate,2) as discount_rate,round(bi.discount_amount,2) as discount_amount,round(bi.item_price,2) as item_price,round(bi.item_count,2) as item_count,round(bi.item_amount,2) as item_amount,round(bi.real_amount,2) as real_amount,bi.remark,bi.item_unit_id as unit_id,bi.details_id,trim(bi.item_taste) as item_taste,bi.sale_mode,bi.setmeal_id,bi.setmeal_rwid,bi.waitcall_tag,bi.assist_num,bi.assist_money,bi.print_tag,bi.assist_item_id,bi.item_time,bi.waiter_num as waiter_name,bi.item_mac_id,bi.item_time,bi.assist_num,bi.method_money,bi.batch_num,coalesce(p.payment_state,'"+SysDictionary.PAYMENT_STATE_NOTPAY+"') as payment_state");
//			str.append(" from pos_bill_item bi left join (select tenancy_id,store_id,bill_num,batch_num,(case when count(nullif(payment_state,'"+SysDictionary.PAYMENT_STATE_PAY_COMPLETE+"'))>0 then '"+SysDictionary.PAYMENT_STATE_PAY+"' else '"+SysDictionary.PAYMENT_STATE_PAY_COMPLETE+"' end) as payment_state from pos_bill_payment where payment_state is null or payment_state in ('"+SysDictionary.PAYMENT_STATE_NOTPAY+"','"+SysDictionary.PAYMENT_STATE_PAY+"','"+SysDictionary.PAYMENT_STATE_PAY_COMPLETE+"') group by tenancy_id,store_id,bill_num,batch_num) p on bi.tenancy_id=p.tenancy_id and bi.store_id=p.store_id and bi.bill_num=p.bill_num and bi.batch_num=p.batch_num where bi.tenancy_id=? and bi.store_id=? and bi.bill_num=?");

			// 做法
//			StringBuilder queryZFKWSql = new StringBuilder("select zfkw_id as method_id,zfkw_name as method_name,amount from pos_zfkw_item where tenancy_id=? and store_id=? and bill_num=? and rwid=? and type=?");
			
			// 账单
			List<PosBill> posBills = new ArrayList<PosBill>();
			
			int listSize = list.size();
			if(listSize == 0){	
				return posBills;
			}

			// 将bill_num字段拼接到sql的in关键字中
			StringBuilder billNumsb = new StringBuilder();
			billNumsb.append("(");
			for(int i = 0; i < listSize; i++){
				billNumsb.append("'");
				billNumsb.append(list.get(i).getBill_num());
				billNumsb.append("'");
				if(listSize - i > 1){ // 最后一位不加逗号
					billNumsb.append(",");
				}
			}
			billNumsb.append(")");
			String billNums = billNumsb.toString();

			// 查询账单明细和做法
			StringBuilder itemMethodSql = new StringBuilder();
			itemMethodSql.append(" select bi.default_state,bi.opt_num,bi.discount_reason_id,bi.discount_mode_id,zfkw.method_id, zfkw.method_name, zfkw.amount, bi.bill_num, bi.item_unit_name, bi.item_serial, bi.rwid, bi.item_id, bi.item_num, bi.item_name, bi.item_remark, bi.item_property, bi.served_state,bi.remark, bi.item_unit_id as unit_id, bi.details_id,bi.sale_mode,bi.returngive_reason_id reason_id,bi.manager_num, ");
			itemMethodSql.append(" round(bi.discount_rate, 2) as discount_rate, round(bi.discount_amount+bi.discountr_amount, 2) as discount_amount, round(bi.item_price, 2) as item_price,round(bi.item_count, "+itemNumPoint+") as item_count, round(bi.item_amount, 2) as item_amount, round(bi.real_amount, 2) as real_amount,  trim (bi.item_taste) as item_taste, ");
			itemMethodSql.append(" (case when bi.item_remark = '"+SysDictionary.ITEM_REMARK_TC01+"' then 0 else bi.waitcall_tag end) as waitcall_tag,(case when bi.item_remark = '"+SysDictionary.ITEM_REMARK_FS02+"' then '"+SysDictionary.PAYMENT_STATE_PAY_COMPLETE+"' else coalesce(p .payment_state, '03') end ) as payment_state,");
			itemMethodSql.append(" bi.setmeal_id, bi.setmeal_rwid,bi.assist_num,bi.assist_money,bi.print_tag,bi.assist_item_id,bi.item_time,bi.waiter_num as waiter_name, bi.item_mac_id, bi.item_time, bi.assist_num, bi.item_assist_num,bi.method_money, bi.batch_num,bi.activity_id,bi.activity_batch_num,bi.activity_rule_id,bi.activity_count,bi.single_amount,bi.single_discount_rate,bi.combo_prop,bi.origin_item_price,bi.original_table,bi.discount_num,bi.price_type,bi.special_price_id ");
			itemMethodSql.append(" from pos_bill_item bi inner join pos_bill b on b.tenancy_id = bi.tenancy_id and b.store_id = bi.store_id and b.bill_num = bi.bill_num");
			itemMethodSql.append(" left join (select tenancy_id,store_id,bill_num,rwid,string_agg(cast(zfkw_id as varchar), '&') method_id, string_agg(zfkw_name, '&') method_name, string_agg(cast(coalesce(amount, 0) as varchar), '&') amount from pos_zfkw_item where type = '"+SysDictionary.ZFKW_TYPE_METHOD+"' group by tenancy_id,store_id,bill_num,rwid) zfkw on zfkw.tenancy_id = bi.tenancy_id and zfkw.store_id = bi.store_id and zfkw.bill_num = bi.bill_num and zfkw.rwid = bi.rwid");
			itemMethodSql.append(" left join (select tenancy_id,store_id,bill_num,batch_num, (case when count (nullif(payment_state, '"+SysDictionary.PAYMENT_STATE_PAY_COMPLETE+"')) > 0 then '"+SysDictionary.PAYMENT_STATE_PAY+"' else '"+SysDictionary.PAYMENT_STATE_PAY_COMPLETE+"' end ) as payment_state from pos_bill_payment group by tenancy_id, store_id, bill_num, batch_num ) p on bi.tenancy_id = p .tenancy_id and bi.store_id = p .store_id and bi.bill_num = p .bill_num and bi.batch_num = p .batch_num");
			itemMethodSql.append(" where bi.bill_num in "+billNums+" and bi.store_id = ? and bi.tenancy_id = ? and (bi.item_remark is null or bi.item_remark <> '"+SysDictionary.ITEM_REMARK_CJ05+"' or b.bill_state='"+SysDictionary.BILL_STATE_CJ01+"')  order by bi.item_serial,bi.item_remark,bi.assist_item_id,bi.rwid ");
			SqlRowSet rs = this.jdbcTemplate.queryForRowSet(itemMethodSql.toString(),new Object[]{organId, tenantId});
			Map<String,List<PosBillItem>> billItemsMap = new HashMap<String, List<PosBillItem>>();
			while (rs.next())
			{
				String billNum = rs.getString("bill_num");
				PosBillItem posBillItemMethod = new PosBillItem();
				posBillItemMethod.setDefault_state(rs.getString("default_state"));
				posBillItemMethod.setOpt_num(rs.getString("opt_num"));
				posBillItemMethod.setDiscount_reason_id(rs.getString("discount_reason_id"));
				posBillItemMethod.setDiscount_mode_id(rs.getString("discount_mode_id"));				
				posBillItemMethod.setBill_num(billNum);
				posBillItemMethod.setItem_unit_name(rs.getString("item_unit_name"));
				posBillItemMethod.setItem_serial(rs.getString("item_serial"));
				posBillItemMethod.setRwid(rs.getString("rwid"));
				posBillItemMethod.setItem_id(rs.getString("item_id"));
				posBillItemMethod.setItem_num(rs.getString("item_num"));
				posBillItemMethod.setItem_name(rs.getString("item_name"));
				posBillItemMethod.setItem_remark(rs.getString("item_remark"));
				posBillItemMethod.setItem_property(rs.getString("item_property"));
				posBillItemMethod.setDiscount_rate(rs.getString("discount_rate"));
				posBillItemMethod.setDiscount_amount(rs.getString("discount_amount"));
				posBillItemMethod.setItem_price(rs.getString("item_price"));
				posBillItemMethod.setServed_state(rs.getString("served_state"));
				posBillItemMethod.setItem_count(rs.getString("item_count"));
				posBillItemMethod.setItem_amount(rs.getString("item_amount"));
				posBillItemMethod.setReal_amount(rs.getString("real_amount"));
				posBillItemMethod.setRemark(rs.getString("remark"));
				posBillItemMethod.setUnit_id(rs.getString("unit_id"));
				posBillItemMethod.setDetails_id(rs.getString("details_id"));
				posBillItemMethod.setItem_taste(rs.getString("item_taste"));
				posBillItemMethod.setSale_mode(rs.getString("sale_mode"));
				posBillItemMethod.setSetmeal_id(rs.getString("setmeal_id"));
				posBillItemMethod.setSetmeal_rwid(rs.getString("setmeal_rwid"));
				posBillItemMethod.setWaitcall_tag(rs.getString("waitcall_tag"));
				posBillItemMethod.setAssist_num(rs.getString("assist_num"));
                posBillItemMethod.setItem_assist_num(rs.getString("item_assist_num"));
				posBillItemMethod.setAssist_money(rs.getString("assist_money"));
				posBillItemMethod.setPrint_tag(rs.getString("print_tag"));
				posBillItemMethod.setAssist_item_id(rs.getString("assist_item_id"));
				posBillItemMethod.setItem_time(rs.getString("item_time"));
				posBillItemMethod.setWaiter_name(rs.getString("waiter_name"));
				posBillItemMethod.setItem_mac_id(rs.getString("item_mac_id"));
				posBillItemMethod.setMethod_money(rs.getString("method_money"));
				posBillItemMethod.setBatch_num(rs.getString("batch_num"));
				posBillItemMethod.setPayment_state(rs.getString("payment_state"));
				posBillItemMethod.setActivity_id(rs.getString("activity_id"));
				posBillItemMethod.setActivity_batch_num(rs.getString("activity_batch_num"));
				posBillItemMethod.setActivity_rule_id(rs.getString("activity_rule_id"));
				posBillItemMethod.setActivity_count(rs.getString("activity_count"));
				posBillItemMethod.setSingle_amount(rs.getString("single_amount"));
				posBillItemMethod.setSingle_discount_rate(rs.getString("single_discount_rate"));
                posBillItemMethod.setOriginal_table(rs.getString("original_table"));
                posBillItemMethod.setDiscount_num(rs.getString("discount_num"));
                posBillItemMethod.setPrice_type(rs.getString("price_type"));
                posBillItemMethod.setSepcial_price_id(rs.getString("special_price_id"));

				// 遍历账单明细的口味做法
				List<PosItemMethod> methodList = new ArrayList<PosItemMethod>();
				String methodId = rs.getString("method_id"); // 口味做法id
				String methodName = rs.getString("method_name"); // 口味做法名称
				if(StringUtils.isNotEmpty(methodId) && StringUtils.isNotEmpty(methodName)){
					String[] methodIds = methodId.split("&");
					String[] methodNames = methodName.split("&");	// 口味做法名称
					String[] amounts = rs.getString("amount").split("&");				// 口味做法价格
					for(int j = 0; j < methodIds.length; j++){
						PosItemMethod method = new PosItemMethod();
						method.setMethod_id(methodIds[j]);
						method.setMethod_name(methodNames[j]);
						method.setAmount(amounts[j]);
						
						methodList.add(method);
					}
				}
				posBillItemMethod.setMethod(methodList);
				
				List<PosBillItem> billItems = null;
				if (billItemsMap.containsKey(billNum))
				{
					billItems = billItemsMap.get(billNum);
				}

				if (null == billItems)
				{
					billItems = new ArrayList<PosBillItem>();
				}
				billItems.add(posBillItemMethod);
				
				billItemsMap.put(billNum, billItems);
			}
//			List<PosBillItemMethod> billItems = this.jdbcTemplate.query(itemMethodSql.toString(), new Object[]{organId, tenantId}, BeanPropertyRowMapper.newInstance(PosBillItemMethod.class));			
			
			// 查询账单付款方式
			StringBuilder payment = new StringBuilder("select id,jzid,name,amount,count,number,phone,shift_id,pos_num,cashier_num,last_updatetime,customer_id,bill_code,remark,currency_amount,batch_num,bill_num from pos_bill_payment where bill_num in "+billNums+" and store_id = ? and tenancy_id=?");
			List<PosBillPaymentWays> paymentWays = this.jdbcTemplate.query(payment.toString(), 
					new Object[]{organId, tenantId}, BeanPropertyRowMapper.newInstance(PosBillPaymentWays.class));
			
			// 账单会员
			StringBuilder bMemberSql = new StringBuilder("select id,type,amount,credit,card_code,mobil,last_updatetime,'' as remark,bill_num from pos_bill_member where bill_num in "+billNums+" and store_id=? and tenancy_id=?");
			List<PosBillMembers> posMember = this.jdbcTemplate.query(bMemberSql.toString(), 
					new Object[]{organId, tenantId}, BeanPropertyRowMapper.newInstance(PosBillMembers.class));
			
			// 开发票
//			StringBuilder queryInvoiceSql = new StringBuilder("select invoice_num,invoice_count,invoice_amount,last_updatetime,remark from pos_bill_invoice where tenancy_id=? and store_id=? and (bill_num=? or copy_bill_num=?) ");
//			StringBuilder queryInvoiceSql = new StringBuilder("select sum(coalesce(invoice_count,0)) as invoice_count,sum(abs(coalesce(invoice_count,0)) * coalesce(invoice_amount,0)) as invoice_amount from pos_bill_invoice where tenancy_id=? and store_id=? and (bill_num=? or copy_bill_num=?) ");
			StringBuilder queryInvoiceSql = new StringBuilder("select bill_num, sum(invoice_count) as invoice_count, sum(invoice_amount) as invoice_amount from (select bill_num,coalesce(invoice_count, 0) as invoice_count,(abs(coalesce(invoice_count, 0)) * coalesce (invoice_amount, 0)) as invoice_amount from pos_bill_invoice where tenancy_id = ? and store_id = ? and bill_num in "+billNums);
			queryInvoiceSql.append(" union all (select copy_bill_num as bill_num,coalesce(invoice_count, 0) as invoice_count,(abs(coalesce(invoice_count, 0)) * coalesce (invoice_amount, 0)) as invoice_amount from pos_bill_invoice where tenancy_id = ? and store_id = ? and copy_bill_num in "+billNums+")) t group by t.bill_num");
			List<JSONObject> invoiceList = this.query4Json(tenantId, queryInvoiceSql.toString(), new Object[]{tenantId, organId, tenantId, organId});
			
			// 查询batch
//			StringBuilder queryBatchSql = new StringBuilder("select id,batch_num,payment_amount,bill_state,bill_property,payment_state from pos_bill_batch where bill_num = ? and tenancy_id=? and store_id=?");
			StringBuilder queryBatchSql = new StringBuilder("select id,batch_num,payment_amount,bill_state,bill_property,payment_state,bill_num from pos_bill_batch where (select coalesce(trim(para_value), '01') as para_value from sys_parameter where para_code = 'ZCDCMS' and valid_state='1') = '02' and bill_num in "+billNums+" and tenancy_id=? and store_id=?");
			List<PosBillBatchs> batchList = this.jdbcTemplate.query(queryBatchSql.toString(), 
					new Object[]{tenantId, organId}, BeanPropertyRowMapper.newInstance(PosBillBatchs.class));

			//查询附加服务费
//			StringBuilder queryService = new StringBuilder(" SELECT service_id, service_type, sum(coalesce(service_count,0)) as service_count, sum(coalesce(service_total,0)) as service_total from pos_bill_service ");
//			queryService.append(" where tenancy_id=? and store_id=? and bill_num=? group by service_id, service_type");
			StringBuilder queryService = new StringBuilder("select bill_num, service_id, service_type, sum (coalesce(service_count, 0)) as service_count, sum (coalesce(service_total, 0)) as service_total");
			queryService.append(" from (SELECT bill_num, service_id, service_type, service_count, service_total from pos_bill_service where tenancy_id =? and store_id =? and bill_num in "+billNums+" ) t group by bill_num, service_id, service_type");
			List<PosBillServices> serviceList = this.jdbcTemplate.query(queryService.toString(), 
					new Object[]{tenantId, organId}, BeanPropertyRowMapper.newInstance(PosBillServices.class)); 
			
			for (int i = 0; i < list.size(); i++)
			{
				PosBill bill = list.get(i);
				String billno = bill.getBill_num();

				/*StringBuilder queryItemSql = new StringBuilder();
				if (SysDictionary.BILL_STATE_CJ01.equalsIgnoreCase(bill.getBill_state()))
				{
					queryItemSql.append(str.toString()).append(" order by bi.item_serial,bi.rwid");
				}
				else
				{
					queryItemSql.append(str.toString()).append(" and (bi.item_remark is null or bi.item_remark<>'"+SysDictionary.ITEM_REMARK_CJ05+"') order by bi.item_serial,bi.rwid");
				}
	
				List<PosBillItem> items = this.jdbcTemplate.query(queryItemSql.toString(), new Object[]
				{ tenantId, organId, billno }, BeanPropertyRowMapper.newInstance(PosBillItem.class));
	
				List<PosBillItem> posItems = new ArrayList<PosBillItem>();
				for(PosBillItem item : items)
				{
					//取出做法
					Integer rwid = null;
					if (StringUtils.isNotEmpty(item.getRwid()))
					{
						rwid = Integer.parseInt(item.getRwid());
					}
	
					if(SysDictionary.ITEM_REMARK_FS02.equals(item.getItem_remark()))
					{
						item.setPayment_state(SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
					}
	
					List<PosItemMethod> methodList = this.jdbcTemplate.query(queryZFKWSql.toString(), new Object[]
							{ tenantId, organId, billno, rwid, SysDictionary.ZFKW_TYPE_METHOD }, 
							BeanPropertyRowMapper.newInstance(PosItemMethod.class));
					item.setMethod(methodList);
	
					posItems.add(item);
				}
				bill.setDetaillist(posItems);*/

//				if(billItems != null && billItems.size() > 0){
//					// 反射
//					Method[] methods = PosBillItem.class.getDeclaredMethods();
//					
//					List<PosBillItem> billItems2 = new ArrayList<PosBillItem>();
//					
//					for(PosBillItemMethod billItem: billItems){
//						if(billno.equals(billItem.getBill_num())){
//							PosBillItem billItem2 = new PosBillItem();
//							for(int n = 0; n < methods.length; n++){
//								if(methods[n].getName().startsWith("get")){
//									// 参数类型
//									Class<?> paramType = methods[n].getReturnType();
//									// 通过反射取值
//									Object value = methods[n].invoke(billItem, new Object[]{});
//									// set方法赋值
//									Method setMethod = PosBillItem.class.getMethod("set"+methods[n].getName().substring(3), paramType);
//									setMethod.invoke(billItem2, value);
//								}
//							}
//							
//							// 遍历账单明细的口味做法
//							List<PosItemMethod> methodList = new ArrayList<PosItemMethod>();
//							String methodId = billItem.getMethod_id(); // 口味做法id
//							String methodName = billItem.getMethod_name(); // 口味做法名称
//							if(StringUtils.isNotEmpty(methodId) && StringUtils.isNotEmpty(methodName)){
//								String[] methodIds = methodId.split("&");
//								String[] methodNames = methodName.split("&");	// 口味做法名称
//								String[] amounts = billItem.getAmount().split("&");				// 口味做法价格
//								for(int j = 0; j < methodIds.length; j++){
//									PosItemMethod method = new PosItemMethod();
//									method.setMethod_id(methodIds[j]);
//									method.setMethod_name(methodNames[j]);
//									method.setAmount(amounts[j]);
//									
//									methodList.add(method);
//								}
//							}
//							billItem2.setMethod(methodList);
//							billItems2.add(billItem2);
//						}
//					}
//					bill.setDetaillist(billItems2);
//				}

				List<PosBillItem> billItems = null;
				if (billItemsMap.containsKey(billno))
				{
					billItems = billItemsMap.get(billno);
				}

				if (null == billItems)
				{
					billItems = new ArrayList<PosBillItem>();
				}
				bill.setDetaillist(billItems);

				// 会员信息
//				List<PosBillMember> posMember = this.jdbcTemplate.query(bMemberSql.toString(), new Object[]
//						{ organId, billno }, BeanPropertyRowMapper.newInstance(PosBillMember.class));
//				bill.setMemberlist(posMember);
				// 在会员信息集合里遍历每一个订单的会员信息，对应盛放
				if(posMember != null && posMember.size() > 0){
					// 反射
					Method[] methods = PosBillMember.class.getDeclaredMethods();
					
					List<PosBillMember> billMembers2 = new ArrayList<PosBillMember>();
					
					for(PosBillMembers member: posMember){
						if(billno.equals(member.getBill_num())){
							PosBillMember member2 = new PosBillMember();
							for(int n = 0; n < methods.length; n++){
								if(methods[n].getName().startsWith("get")){
									// 参数类型
									Class<?> paramType = methods[n].getReturnType();
									// 通过反射取值
									Object value = methods[n].invoke(member, new Object[]{});
									// set方法赋值
									Method setMethod = PosBillMember.class.getMethod("set"+methods[n].getName().substring(3), paramType); 
									setMethod.invoke(member2, value);
								}
							}
							billMembers2.add(member2);
						}
					}
					bill.setMemberlist(billMembers2);
				}

				// 账单付款方式
//				List<PosBillPaymentWay> paymentWays = this.jdbcTemplate.query(payment.toString(), new Object[]
//						{ billno, organId }, BeanPropertyRowMapper.newInstance(PosBillPaymentWay.class));
//				bill.setPaymentlist(paymentWays);
				// 在账单付款方式集合里遍历每一个订单的账单付款方式，对应盛放
				if(paymentWays != null && paymentWays.size() > 0){
					// 反射
					Method[] methods = PosBillPaymentWay.class.getDeclaredMethods();
					
					List<PosBillPaymentWay> paymentWays2 = new ArrayList<PosBillPaymentWay>();
					for(PosBillPaymentWays paymentWay: paymentWays){
						if(billno.equals(paymentWay.getBill_num())){	// 相等
							PosBillPaymentWay paymentWay2 = new PosBillPaymentWay();
							for(int n = 0; n < methods.length; n++){
								if(methods[n].getName().startsWith("get")){
									// 参数类型
									Class<?> paramType = methods[n].getReturnType();
									// 通过反射取值
									Object value = methods[n].invoke(paymentWay, new Object[]{});
									// set方法赋值
									Method setMethod =PosBillPaymentWay.class.getMethod("set"+methods[n].getName().substring(3), paramType); 
									setMethod.invoke(paymentWay2, value);
								}
							}
							paymentWays2.add(paymentWay2);	
						}
					}
					bill.setPaymentlist(paymentWays2);
				}
				
				// 附加服务费
//				List<PosBillService> serviceList = this.jdbcTemplate.query(queryService.toString(), new Object[]
//						{ tenantId, organId, billno }, BeanPropertyRowMapper.newInstance(PosBillService.class));
//				bill.setServicelist(serviceList);
				// 在附加服务费集合里遍历每一个订单的附加服务费，对应盛放
				if(serviceList != null && serviceList.size() > 0){
					// 反射
					Method[] methods = PosBillService.class.getDeclaredMethods(); 
					
					List<PosBillService> billServices2 = new ArrayList<PosBillService>();
					for(PosBillServices service: serviceList){
						if(billno.equals(service.getBill_num())){
							PosBillService service2 = new PosBillService();
							for(int n = 0; n < methods.length; n++){
								if(methods[n].getName().startsWith("get")){
									// 参数类型
									Class<?> paramType = methods[n].getReturnType();
									// 通过反射取值
									Object value = methods[n].invoke(service, new Object[]{});
									// set方法赋值
									Method setMethod = PosBillService.class.getDeclaredMethod("set"+methods[n].getName().substring(3), paramType);
									setMethod.invoke(service2, value);
								}
							}
							billServices2.add(service2);
						}
					}
					bill.setServicelist(billServices2);
				}
				
				// 开发票
				if (!SysDictionary.BILL_STATE_CJ01.equalsIgnoreCase(bill.getBill_state()))
				{
//					List<JSONObject> invoiceList;
//					try
//					{
//						// 开发票
//						invoiceList = this.query4Json(tenantId, queryInvoiceSql.toString(), new Object[]
//								{ tenantId, organId, billno, billno });
//						bill.setInvoicelist(invoiceList);
//					}
//					catch (Exception e)
//					{
//						e.printStackTrace();
//					}
					// 在开发票集合里遍历每一个订单的发票，对应盛放
					if(invoiceList != null && invoiceList.size() > 0){
						List<JSONObject> jsonObjects = new ArrayList<JSONObject>();
						for(JSONObject invoice: invoiceList){
							if(billno.equals(invoice.optString("bill_num"))){
								invoice.remove("bill_num");		// 移除bill_num,避免返回给客户端
								jsonObjects.add(invoice);
							}
						}
						bill.setInvoicelist(jsonObjects);
					}
				}

				// 查询Batch
//				String mode= "01";
//				try
//				{
//					mode = getSysParameter(tenantId, organId, "ZCDCMS");// 01,普通;02,自助;
//					if (Tools.isNullOrEmpty(mode))
//					{
//						mode = "01";
//					}
//				}
//				catch (Exception e)
//				{
//					e.printStackTrace();
//				}
//				if("02".equals(mode))
//				{
//					List<PosBillBatch> batchList = this.jdbcTemplate.query(queryBatchSql.toString(), new Object[]
//							{ tenantId, organId, billno }, BeanPropertyRowMapper.newInstance(PosBillBatch.class));
//					bill.setBatchList(batchList);
//				}
				// Batch根据billno进行分类
				if(batchList != null && batchList.size() > 0){
					// 反射
					Method[] methods = PosBillBatch.class.getDeclaredMethods();
					
					List<PosBillBatch> billBatch2 = new ArrayList<PosBillBatch>();
					for(PosBillBatchs batch: batchList){
						if(billno.equals(batch.getBill_num())){
							PosBillBatch batch2 = new PosBillBatch();
							for(int n = 0; n < methods.length; n++){
								if(methods[n].getName().startsWith("get")){
									// 参数类型
									Class<?> paramType = methods[n].getReturnType();
									// 通过反射取值
									Object value = methods[n].invoke(batch, new Object[]{});
									// set方法赋值
									Method setMethod = PosBillBatch.class.getDeclaredMethod("set"+methods[n].getName().substring(3), paramType);
									setMethod.invoke(batch2, value);
								}
							}
							billBatch2.add(batch2);
						}
					}
					bill.setBatchList(billBatch2);
				}

				posBills.add(bill);
			}
			
			return posBills;
		} catch (Exception e1) {
			// TODO Auto-generated catch block
			logger.info("Exception:" + ExceptionMessage.getExceptionMessage(e1));
			throw SystemException.getInstance(PosErrorCode.OPER_ERROR);
		}
	}
	
	@Override
	public List<JSONObject> findOderDish(String tenantId, Integer organId, String tableCode) throws Exception
	{
		/**
		 * 查询账单明细信息
		 */
		StringBuilder sql = new StringBuilder(
				"SELECT pbi.item_class_id as class_id,hic.itemclass_name as class_name,pbi.item_id,pbi.item_num,pbi.item_name,pbi.item_price,pbi.item_count,pbi.item_taste,pbi.bill_num,pbi.rwid,pbi.item_property,pbi.setmeal_id,pbi.setmeal_rwid,pbi.item_serial,pbi.assist_num,pbi.assist_money,pbi.method_money FROM pos_bill_item as pbi ");
		sql.append("left join pos_bill as pb on pb.bill_num = pbi.bill_num ");
		sql.append("left join hq_item_class as hic on pbi.item_class_id = hic.id ");
		sql.append("WHERE pb.bill_property = 'OPEN' AND pb.table_code = ? AND pb.store_id = ?");

		// 做法口味
		StringBuilder sqlName = new StringBuilder("select a.zfkw_name as name from pos_zfkw_item a ");
		sqlName.append("where a.store_id=? and a.rwid=? and a.type=? union all select a.zfkw_name as name from pos_zfkw_item a ");
		sqlName.append("where a.store_id=? and a.rwid=? and a.type=? ");

		String taste = "TASTE";
		String practices = "METHOD";
		/**
		 * 查询账单明细信息class_id，class_name，item_id，item_num，item_name，item_price，
		 * item_count，item_zfkw
		 */
		logger.debug("查询账单明细sql：" + sql.toString());
		SqlRowSet rs = this.jdbcTemplate.queryForRowSet(sql.toString(), new Object[]
		{ tableCode, organId });

		List<PosOrderDishItem> items = new ArrayList<PosOrderDishItem>();
		while (rs.next())
		{
			PosOrderDishItem dishItem = new PosOrderDishItem();
			Integer rwid = rs.getInt("rwid");
			dishItem.setClass_id("" + rs.getInt("class_id"));
			dishItem.setClass_name(rs.getString("class_name"));
			dishItem.setItem_id("" + rs.getInt("item_id"));
			dishItem.setItem_name(rs.getString("item_name"));
			dishItem.setItem_num(rs.getString("item_num"));
			dishItem.setItem_count("" + rs.getDouble("item_count"));
			dishItem.setItem_price("" + rs.getDouble("item_price"));
			dishItem.setItem_property(rs.getString("item_property"));
			dishItem.setSetmeal_id("" + rs.getInt("setmeal_id"));
			dishItem.setSetmeal_rwid("" + rs.getInt("setmeal_rwid"));
			dishItem.setItem_serial("" + rs.getString("item_serial"));
			dishItem.setAssist_num(rs.getString("assist_num"));
			dishItem.setAssist_money(rs.getString("assist_money"));
			dishItem.setMethod_money(rs.getString("method_money"));

			String rsStr = "";
			SqlRowSet rs1 = this.jdbcTemplate.queryForRowSet(sqlName.toString(), new Object[]
			{ organId, rwid, taste, organId, rwid, practices });
			while (rs1.next())
			{
				rsStr = rsStr + rs1.getString("name") + " ";
			}

			dishItem.setZfkw(rsStr);
			items.add(dishItem);
		}
		@SuppressWarnings("unchecked")
		List<JSONObject> objs = JSONArray.fromObject(items);
		logger.debug("已下单菜品:" + objs.toString());
		return objs;
	}

	@Override
	@Deprecated
	public Data billDiscount(String tenantId, Integer organId, Map<String, Object> map) throws SystemException
	{
		String billno = ParamUtil.getStringValue(map, "bill_num", false, null);

		String mode = ParamUtil.getStringValue(map, "mode", false, null);

		Date reportDate = ParamUtil.getDateValue(map, "report_date", false, null);

		Integer shiftId = ParamUtil.getIntegerValue(map, "shift_id", false, null);

		String posNum = ParamUtil.getStringValue(map, "pos_num", false, null);

		String optNum = ParamUtil.getStringValue(map, "opt_num", false, null);
		
//		String customerCode = ParamUtil.getStringValue(map, "customer_code", false, null);

		// 会员卡号 GJ20160406
//		String card_code = ParamUtil.getStringValue(map, "card_code", false, null);
		// 手机号 GJ20160406
//		String mobil = ParamUtil.getStringValue(map, "mobil", false, null);

		Integer discount_id = null;
		if (Tools.isNullOrEmpty(map.get("discount_id")) == false)
		{
			discount_id = Integer.parseInt(map.get("discount_id").toString());
		}

		// 打折时
		if (discount_id != null && discount_id != 0)
		{
			double zeroNorm = 0d;
			String nowDateS = DateUtil.getNowDateYYDDMM();
			Date nowDate = DateUtil.parseDate(nowDateS);
			// 开始结束期间内的折扣方案
			StringBuilder ssql = new StringBuilder(
					"select hdc.running_cycle,hdc.zero_norm from hq_discount_case as hdc left join hq_discount_case_org as hdco on hdco.discount_case_id = hdc.id where hdc.id = ? and hdc.startdate <= ? and hdc.enddate >= ? and hdc.valid_state = '1' and hdco.store_id = ?");
			SqlRowSet rs = this.jdbcTemplate.queryForRowSet(ssql.toString(), new Object[]
			{ discount_id, nowDate, nowDate, organId });

			String runningCycle = null;
			List<String> weeks = new ArrayList<String>();
			if (rs.next())
			{
				runningCycle = rs.getString("running_cycle");
				weeks = Arrays.asList(runningCycle.split(","));
				zeroNorm = rs.getDouble("zero_norm");
			}

			String week = null;
			try
			{
				// 当前日期是星期几
				week = "0" + String.valueOf(DateUtil.dayForWeek(nowDateS));
			}
			catch (Exception e1)
			{
				logger.info("Exception:" + ExceptionMessage.getExceptionMessage(e1));
				throw new SystemException(PosErrorCode.OPER_ERROR);
			}

			// 不在执行周期内
			if (runningCycle == null || (StringUtils.isNotEmpty(runningCycle) && !weeks.contains(week)))
			{
				throw new SystemException(PosErrorCode.INVALID_DISCOUNT);
			}

			String billSql = new String("select bill_amount from pos_bill where bill_num = ? and store_id = ?");
			rs = this.jdbcTemplate.queryForRowSet(billSql, new Object[]
			{ billno, organId });

			double billAmount = 0d;
			if (rs.next())
			{
				billAmount = rs.getDouble("bill_amount");
			}

			if (billAmount < zeroNorm)
			{
				throw new SystemException(PosErrorCode.LOW_THAN_ZERO_NORM);
			}
		}

		// 还原原来的折扣，有可能是服务员输入错误
		StringBuilder psql = new StringBuilder("update pos_bill set discount_rate=?,discountr_amount=?,discount_case_id=? where bill_num = ? and store_id = ? ");
		this.jdbcTemplate.update(psql.toString(), new Object[]
		{ 100d, null, discount_id, billno, organId });
		StringBuilder pisql = new StringBuilder("update pos_bill_item set discount_reason_id = ?,discount_rate = ? where bill_num = ? and store_id = ? ");
		this.jdbcTemplate.update(pisql.toString(), new Object[]
		{ null, 100d, billno, organId });

		Integer discount_reason_id = 0;
		if (Tools.isNullOrEmpty(map.get("discount_reason_id")) == false)
		{
			discount_reason_id = Integer.parseInt(map.get("discount_reason_id").toString());
		}

		// String manager_num = ParamUtil.getStringValue(map, "manager_num",
		// false, null);

		Double discountr_amount = 0d;
		if (Tools.isNullOrEmpty(map.get("discountr_amount")) == false)
		{
			discountr_amount = Double.parseDouble(map.get("discountr_amount").toString());
		}

		Double discount_rate = ParamUtil.getDoubleValue(map, "discount_rate", false, null);

		Integer discount_mode = ParamUtil.getIntegerValue(map, "discount_mode", false, null);

		Data data = new Data();
		if (StringUtils.isNotEmpty(mode))
		{
			// 更新账单，用来做计算用
			String usql = new String("update pos_bill set discount_mode_id=?,discount_case_id=?,discount_rate=?,discountr_amount=? where bill_num = ? and store_id = ?");
			this.jdbcTemplate.update(usql, new Object[]
			{ discount_mode, discount_id, discount_rate, discountr_amount, billno, organId });

			StringBuilder sql = new StringBuilder("update pos_bill_item set discount_mode_id=?,discount_reason_id = ? where bill_num = ? and store_id = ? ");
			this.jdbcTemplate.update(sql.toString(), new Object[]
			{ discount_mode, discount_reason_id, billno, organId });
			try
			{
				if (mode.equals("0"))
				{
					calcAmount(tenantId, organId, billno);

					data.setCode(Constant.CODE_SUCCESS);
					data.setMsg(Constant.BILL_DISCOUNT_SUCCESS);
					savePosLog(tenantId, organId, posNum, optNum, optNum, shiftId, reportDate, Constant.TITLE, "账单折扣", "账单编号:"+billno, "");
				}
				else if (mode.equals("1"))
				{
//					if (null != discount_mode && 5 == discount_mode)
//					{
//						this.operPosBillMember(tenantId, organId, reportDate, billno, SysDictionary.BILL_MEMBERCARD_ZK02, card_code, mobil,customerCode);
//					}
					calcAmount(tenantId, organId, billno);

					data.setCode(Constant.CODE_SUCCESS);
					data.setMsg(Constant.BILL_DISCOUNT_SUCCESS);
					savePosLog(tenantId, organId, posNum, optNum, optNum, shiftId, reportDate, Constant.TITLE, "固定账单折扣", "账单编号:"+billno, "");
				}
				else if (mode.equals("2"))
				{
					// 取消折扣
					this.jdbcTemplate.update(psql.toString(), new Object[]
					{ 100d, null, discount_id, billno, organId });
					this.jdbcTemplate.update(pisql.toString(), new Object[]
					{ null, 100d, billno, organId });
					calcAmount(tenantId, organId, billno);

					data.setCode(Constant.CODE_SUCCESS);
					data.setMsg(Constant.CANC_BILL_DISCOUNT_SUCCESS);
					savePosLog(tenantId, organId, posNum, optNum, optNum, shiftId, reportDate, Constant.TITLE, "取消账单折扣", "账单编号:"+billno, "");
				}
			}
			catch (SystemException e1)
			{
				throw e1;
			}
			catch (Exception e)
			{
				logger.info("Exception:" + ExceptionMessage.getExceptionMessage(e));
				throw new SystemException(PosErrorCode.OPER_ERROR);
			}

		}
		return data;
	}

	@Override
	@Deprecated
	public Data clearPayment(String tenantId, Integer organId, Map<String, Object> map) throws SystemException
	{
		Data data = new Data();

		return data;
	}

	@Override
	@Deprecated
	public Data setSoldOut(String tenantId, Integer organId, String mode, Timestamp reportDate, List<Map<String, Object>> list, PosLog log) throws SystemException
	{
		Data data = new Data();
		return data;
	}

	@Override
	@Deprecated
	public Data setWorryItem(String tenantId, Integer organId, Map<String, Object> map) throws SystemException
	{
		Data data = new Data();
		return data;
	}

	@Override
	@Deprecated
	public Data modifyServiceFee(String tenantId, Integer organId, Integer serviceAmount) throws SystemException
	{
		Data data = new Data();
		data.setCode(Constant.CODE_SUCCESS);
		data.setMsg(Constant.MODIFY_SERVICE_FEE_SUCCESS + ",新服务费为：" + serviceAmount);
		return data;
	}

	@Override
	@Deprecated
	public Data modifyGuest(String tenantId, Integer organId, String billno) throws SystemException
	{
		Data data = new Data();
		data.setCode(Constant.CODE_SUCCESS);
		data.setMsg(Constant.MODIFY_CUSTOMER_COUNT_SUCCESS + "，订单号：" + billno);
		return data;
	}

	@Override
	@Deprecated
	public void cancBillTaste(String tenantId, Integer organId, Map<String, String> map) throws SystemException
	{

	}

	@Override
	@Deprecated
	public Data guestMsg(String tenantId, Integer organId, Map<String, String> map) throws SystemException
	{
		Data data = new Data();

		return data;
	}

	@Override
	@Deprecated
	public Data changeWaiter(String tenantId, Integer organId, Map<String, String> map) throws SystemException
	{
		Data data = new Data();
		return data;
	}

	@Override
	@Deprecated
	public Data changeRemarkAndZfkw(String tenantId, Integer organId, List<Map<String, Object>> lists) throws SystemException
	{
		Data data = new Data();
		for (int i = 0; i < lists.size(); i++)
		{
			Map<String, Object> map = lists.get(i);

			Date reportDate = ParamUtil.getDateValue(map, "report_date", false, null);

			Integer shiftId = ParamUtil.getIntegerValue(map, "shift_id", false, null);

			String posNum = ParamUtil.getStringValue(map, "pos_num", true, PosErrorCode.NOT_NULL_POS_NUM);

			String optNum = ParamUtil.getStringValue(map, "opt_num", false, null);

			String billno = ParamUtil.getStringValue(map, "bill_num", true, PosErrorCode.NOT_NULL_BILL_NUM);

			Integer rwid = ParamUtil.getIntegerValue(map, "rwid", true, PosErrorCode.NOT_NULL_RWID);

			String mode = ParamUtil.getStringValue(map, "mode", true, PosErrorCode.NOT_NULL_MODE);

			@SuppressWarnings("unchecked")
			List<Map<String, Object>> listMaps = (List<Map<String, Object>>) map.get("zfkw");

			if (StringUtils.isNotEmpty(mode))
			{
				// 保存做法口味卧单库
				StringBuilder sql = new StringBuilder("INSERT INTO pos_zfkw_item (tenancy_id,store_id,bill_num,report_date,rwid,pos_num,type,last_updatetime,zfkw_id,zfkw_name,amount,print_id) VALUES (?,?,?,?,?,?,?,?,?,?,?,?)");
				//
				StringBuilder str = new StringBuilder("select rwid,item_remark,item_property from pos_bill_item where bill_num = ? and store_id = ?");
				// 根据rwid删除做法口味卧单库
				StringBuilder dsql = new StringBuilder("delete from pos_zfkw_item where rwid = ? and bill_num = ? and store_id = ? ");

				SqlRowSet rs = null;
				Object[] objs = null;
				String type = "TASTE";
				String method = "METHOD";
				Integer zfkwId = null;
				String zfkwName = null;

				Timestamp lastUpdateTime = DateUtil.currentTimestamp();

				switch (mode)
				{
					case "0":
						this.jdbcTemplate.update(dsql.toString(), new Object[]
						{ rwid, billno, organId });

						for (int j = 0; j < listMaps.size(); j++)
						{
							Map<String, Object> zfkwMap = listMaps.get(j);
							if (Tools.isNullOrEmpty(zfkwMap.get("zfkw_id")) == false)
							{
								zfkwId = Integer.parseInt(zfkwMap.get("zfkw_id").toString());
							}
							if (Tools.isNullOrEmpty(zfkwMap.get("zfkw_name")) == false)
							{
								zfkwName = zfkwMap.get("zfkw_name").toString();
							}
							objs = new Object[]
							{ tenantId, organId, billno, reportDate, rwid, posNum, type, lastUpdateTime, zfkwId, zfkwName, 0, null };
							this.jdbcTemplate.update(sql.toString(), objs);
						}

						try
						{
							calcAmount(tenantId, organId, billno);
						}
						catch (Exception e)
						{
							logger.info("Exception:" + ExceptionMessage.getExceptionMessage(e));
							throw new SystemException(PosErrorCode.OPER_ERROR);
						}
						savePosLog(tenantId, organId, posNum, optNum, optNum, shiftId, reportDate, Constant.TITLE, "修改单品备注", "账单编号:"+billno, zfkwName);

						data.setCode(Constant.CODE_SUCCESS);
						data.setMsg(Constant.MODIFY_SINGLE_ZFKW_SUCCESS);

						break;
					case "1":
						StringBuilder dasql = new StringBuilder("delete from pos_zfkw_item where bill_num = ? and store_id = ? ");
						this.jdbcTemplate.queryForRowSet(dasql.toString(), new Object[]
						{ billno, organId });

						rs = this.jdbcTemplate.queryForRowSet(str.toString(), new Object[]
						{ billno, organId });
						while (rs.next())
						{
							Integer p_rwid = rs.getInt("rwid");
							this.jdbcTemplate.update(dsql.toString(), new Object[]
							{ p_rwid, billno, organId });
							for (int j = 0; j < listMaps.size(); j++)
							{
								Map<String, Object> zfkwMap = listMaps.get(j);
								if (Tools.isNullOrEmpty(zfkwMap.get("zfkw_id")) == false)
								{
									zfkwId = Integer.parseInt(zfkwMap.get("zfkw_id").toString());
								}
								if (Tools.isNullOrEmpty(zfkwMap.get("zfkw_name")) == false)
								{
									zfkwName = zfkwMap.get("zfkw_name").toString();
								}
								objs = new Object[]
								{ tenantId, organId, billno, reportDate, p_rwid, posNum, type, lastUpdateTime, zfkwId, zfkwName, 0, null };
								this.jdbcTemplate.update(sql.toString(), objs);
							}
						}

						try
						{
							calcAmount(tenantId, organId, billno);
						}
						catch (Exception e)
						{
							logger.info("Exception:" + ExceptionMessage.getExceptionMessage(e));
							throw new SystemException(PosErrorCode.OPER_ERROR);
						}
						savePosLog(tenantId, organId, posNum, optNum, optNum, shiftId, reportDate, Constant.TITLE, "修改整单备注", "", zfkwName);

						data.setCode(Constant.CODE_SUCCESS);
						data.setMsg(Constant.MODIFY_WHOLE_ZFKW_SUCCESS);

						break;
					case "2":
						// 删除以前的口味做法
						this.jdbcTemplate.update(dsql.toString(), new Object[]
						{ rwid, billno, organId });

						StringBuilder qsql = new StringBuilder("select him.makeup_way,him.proportion_money,pbi.item_count from pos_bill_item pbi ");
						qsql.append("left join hq_item_method him on him.item_id = pbi.item_id where pbi.store_id = ? and pbi.rwid = ? ");

						StringBuilder usql = new StringBuilder("update pos_bill_item set method_money = ? where rwid = ? and store_id = ?");

						for (int j = 0; j < listMaps.size(); j++)
						{
							Map<String, Object> zfkwMap = listMaps.get(j);
							if (Tools.isNullOrEmpty(zfkwMap.get("zfkw_id")) == false)
							{
								zfkwId = Integer.parseInt(zfkwMap.get("zfkw_id").toString());
							}
							if (Tools.isNullOrEmpty(zfkwMap.get("zfkw_name")) == false)
							{
								zfkwName = zfkwMap.get("zfkw_name").toString();
							}

							objs = new Object[]
							{ tenantId, organId, billno, reportDate, rwid, posNum, method, lastUpdateTime, zfkwId, zfkwName, 0, null };
							this.jdbcTemplate.update(sql.toString(), objs);
						}

						rs = this.jdbcTemplate.queryForRowSet(qsql.toString(), new Object[]
						{ organId, rwid });

						Double amount = 0d;

						while (rs.next())
						{
							String makeupWay = rs.getString("makeup_way");
							Double itemCount = rs.getDouble("item_count");
							Double proportion = rs.getDouble("proportion_money");
							if (StringUtils.isNotEmpty(makeupWay))
							{
								if (makeupWay.equalsIgnoreCase("ADD"))
								{
									amount = proportion;
								}
								else if (makeupWay.equalsIgnoreCase("MULT"))
								{
									amount = itemCount * proportion;
								}
							}
						}

						this.jdbcTemplate.update(usql.toString(), new Object[]
						{ amount, rwid, organId });

						try
						{
							calcAmount(tenantId, organId, billno);
						}
						catch (Exception e)
						{
							logger.info("Exception:" + ExceptionMessage.getExceptionMessage(e));
							throw new SystemException(PosErrorCode.OPER_ERROR);
						}
						savePosLog(tenantId, organId, posNum, optNum, optNum, shiftId, reportDate, Constant.TITLE, "修改做法", "", zfkwName);

						data.setCode(Constant.CODE_SUCCESS);
						data.setMsg(Constant.MODIFY_DISH_ZFKW_SUCCESS);

						break;
					default:
						data.setCode(Constant.CODE_PARAM_FAILURE);
						data.setMsg(Constant.NOT_MATCHING_MODE);
						break;
				}
			}
			else
			{
				throw new SystemException(PosErrorCode.NOT_NULL_MODE);
			}
		}

		return data;
	}

	@Override
	@Deprecated
	public Data changeItemUnit(String tenantId, Integer organId, List<Map<String, String>> listMaps) throws SystemException
	{
		Data data = new Data();
		return data;
	}

	@Override
	@Deprecated
	public Data giveItem(String tenantId, Integer organId, List<Map<String, Object>> listMaps) throws SystemException
	{
		Data data = new Data();

		return data;
	}

	@Override
	@Deprecated
	public Data retreatFood(String tenantId, Integer organId, List<Map<String, Object>> listMaps) throws SystemException
	{
		Data data = new Data();

		return data;
	}

	@Override
	@Deprecated
	public Data changeDishCount(String tenantId, Integer organId, List<Map<String, Object>> listMaps) throws SystemException
	{
		Data data = new Data();

		return data;
	}

	@Override
	@Deprecated
	public Data changeDishName(String tenantId, Integer organId, List<Map<String, Object>> listMaps) throws SystemException
	{
		Data data = new Data();

		return data;
	}

	@Override
	@Deprecated
	public Data pushFood(String tenantId, Integer organId, Map<String, Object> map) throws SystemException
	{
		Data data = new Data();

		return data;
	}

	@Override
	@Deprecated
	public Data waitCall(String tenantId, Integer organId, Map<String, Object> map) throws SystemException
	{
		Data data = new Data();

		return data;
	}

	@Override
	@Deprecated
	public Data callupFood(String tenantId, Integer organId, Map<String, Object> map) throws SystemException
	{
		Data data = new Data();

		return data;
	}

	@Override
	@Deprecated
	public Data cancelBill(String tenantId, Integer organId, Map<String, Object> map, PosCodeService codeService) throws SystemException
	{
		Data data = new Data();

		return data;
	}

	@Override
	@Deprecated
	public Data recoveryBill(String tenantId, Integer organId, Map<String, Object> map) throws SystemException
	{
		Data data = new Data();

		return data;
	}

	@Override
	@Deprecated
	public Data modSoldOuntCount(String tenantId, Integer organId, Map<String, Object> map) throws SystemException
	{
		Data data = new Data();

		String mode = null;
		if (Tools.isNullOrEmpty(map.get("mode")) == false)
		{
			mode = map.get("mode").toString();
		}
		else
		{
			throw new SystemException(PosErrorCode.NOT_NULL_MODE);
		}
		@SuppressWarnings("unchecked")
		List<Map<String, Object>> items = (List<Map<String, Object>>) map.get("item");

		if (items.size() == 0)
		{
			data.setCode(Constant.CODE_PARAM_FAILURE);
			data.setMsg(Constant.NOT_NULL_ITEM);
			return data;
		}

		Integer item_id = null;
		StringBuilder sql = new StringBuilder("select num from pos_soldout where item_id = ? and store_id = ? ");
		StringBuilder usql = new StringBuilder("update pos_soldout set num = ? where item_id = ? and store_id = ? ");

		for (int i = 0; i < items.size(); i++)
		{
			Double p_count = null;
			Double count = null;
			if (Tools.isNullOrEmpty(items.get(i).get("item_id")) == false)
			{
				item_id = Integer.parseInt(items.get(i).get("item_id").toString());
			}
			else
			{
				throw new SystemException(PosErrorCode.NOT_NULL_ITEM_ID);
			}

			if (Tools.isNullOrEmpty(items.get(i).get("num")) == false)
			{
				p_count = Double.parseDouble(items.get(i).get("num").toString());
			}

			SqlRowSet rs = this.jdbcTemplate.queryForRowSet(sql.toString(), new Object[]
			{ item_id, organId });
			while (rs.next())
			{
				count = rs.getDouble("num");
			}

			if (count != null)
			{
				if (mode.equals("0"))
				{
					if (count < p_count)
					{
						data.setCode(Constant.CODE_PARAM_FAILURE);
						data.setMsg("估清数量大于库存数量，请重新调整。");
						return data;
					}
					else
					{
						this.jdbcTemplate.update(usql.toString(), new Object[]
						{ Scm.psub(count, p_count), item_id, organId });
					}
				}

				if (mode.equals("1"))
				{
					this.jdbcTemplate.update(usql.toString(), new Object[]
					{ Scm.padd(count, p_count), item_id, organId });
				}
			}
		}

		data.setCode(Constant.CODE_SUCCESS);
		data.setMsg(Constant.CHANG_GUQINGCOUNT_SUCCESS);
		return data;
	}
	
//	@Override
//	public void operPosBillMember(String tenantId, int storeId, Date reportDate, String billno, String type, String cardCode, String mobil,String customerCode)
//	{
//		StringBuilder sql = new StringBuilder();
//		sql.append("delete from pos_bill_member pbm where pbm.tenancy_id =? and pbm.store_id =? and pbm.report_date =? and pbm.bill_num =? and type = ?");
//		this.jdbcTemplate.update(sql.toString(), new Object[]
//		{ tenantId, storeId, reportDate, billno, type });
//
//		sql.delete(0, sql.length());
//		sql.append(" insert into pos_bill_member(tenancy_id,store_id,report_date,bill_num,type,card_code,mobil,customer_code,last_updatetime) values (?, ?, ?, ?, ?, ?, ?, ?, ?)");
//		this.jdbcTemplate.update(sql.toString(), new Object[]
//		{ tenantId, storeId, reportDate, billno, type, cardCode, mobil, customerCode,DateUtil.currentTimestamp() });
//	}
//	
//	@Override
//	public void updatePosBillMemberForAmount(String tenantId, int storeId, String billno, String type, double amount,double credit,String billCode,String requestState)throws Exception
//	{
//		StringBuilder sql = new StringBuilder();
//		sql.append("update pos_bill_member set amount=? ,credit=?,bill_code=?,request_state=? where tenancy_id=? and store_id=? and bill_num=? and type=?");
//		this.jdbcTemplate.update(sql.toString(), new Object[]
//		{ amount, credit,billCode,requestState, tenantId, storeId, billno, type});
//	}
	
	@Override
	@Deprecated
	public void setInvoice(String sql, Object[] objs) throws SystemException
	{
		this.jdbcTemplate.update(sql, objs);
	}

	@Override
	@Deprecated
	public List<Map<String, Object>> findInvoice(String sql, Object[] objs) throws Exception
	{
		return this.jdbcTemplate.queryForList(sql, objs);
	}

	@Override
	public int getPaymentCountByState(String tenantId, int storeId, String billNum, String paymentState) throws Exception
	{
		StringBuilder queryPayStateSql = new StringBuilder("select count(*) as count from pos_bill_payment where tenancy_id=? and store_id=? and bill_num=? and payment_state=?");
		return this.queryForInt(queryPayStateSql.toString(), new Object[]
		{ tenantId, storeId, billNum, paymentState });
	}
	
	@Override
	@Deprecated
	public String getBatchNum(String tenantId, int storeId ,String billNum) throws Exception
	{
		StringBuilder sql = new StringBuilder("select batch_num from pos_bill where bill_num = '" + billNum + "' and store_id = " + storeId + " and bill_property='" + SysDictionary.BILL_PROPERTY_OPEN + "' ");
		SqlRowSet rs = this.query(tenantId, sql.toString());
		String batch_num = "";
		if(rs.next())
		{
			batch_num = rs.getString("batch_num");
		}
		return batch_num;
	}
	
	@Override
	@Deprecated
	public List<JSONObject> getCouponCode(String tenantId, int storeId ,String billNum, String batchNum) throws Exception
	{
		StringBuilder sql = new StringBuilder("select number from pos_bill_payment where bill_num = '" + billNum + "' and store_id = " + storeId + " and type='" + SysDictionary.PAYMENT_CLASS_COUPONS + "' and number is not null and number<>'' ");
		if(Tools.hv(batchNum))
		{
			sql.append(" and batch_num='" + batchNum + "' ");
		}
		return this.queryString4Json(tenantId, sql.toString());
	}
	
	@Override
	@Deprecated
	public List<JSONObject> getBillDetails4Coupon(String tenantId, int storeId ,String billNum, String batchNum) throws Exception
	{
		StringBuilder sql = new StringBuilder(" select a.item_id,a.item_unit_id,a.item_price,a.item_count, (a.real_amount - coalesce(a.single_discount_amount,0)) as real_amount,a.item_property,a.discount_rate,a.discountr_amount,b.discount_amount as dismoney " +
				" from pos_bill_item a,pos_bill b where a.tenancy_id='" + tenantId + "' and a.store_id=" + storeId + " and a.bill_num='" + billNum + "' and a.item_property='" + SysDictionary.ITEM_PROPERTY_SINGLE + "' " +
				" and a.bill_num = b.bill_num and b.bill_property='" + SysDictionary.BILL_PROPERTY_OPEN + "' and (a.item_remark is null or a.item_remark='') and a.real_amount>0 ");
		if(Tools.hv(batchNum))
		{
			sql.append(" and b.batch_num='" + batchNum + "' ");
		}
		return this.queryString4Json(tenantId, sql.toString());
	}

	@Override
	public List<JSONObject> totalBillInfo(String tenancyId, int storeId, Date reportDate) throws Exception {
		StringBuffer sb = new StringBuffer("select t.totalunpayamount,t.totalpayamount,t.totalamount,t.insideguest,t.guestcount,t.unpaycount,(t.paycount-t.zdqxcount-t.cjcount) as paycount from (");
		sb.append("select coalesce(sum(unpaybillamount), 0) totalunpayamount, ");
		sb.append("   coalesce(sum(paybillamount), 0) totalpayamount, coalesce(sum(bill_amount), 0) totalamount, ");
		sb.append("   coalesce(sum(insideguest), 0) insideguest, coalesce(sum(guest), 0) guestcount, ");
		sb.append(" COALESCE (count(unpayid), 0) unpaycount,COALESCE (count(payid), 0) paycount,");
		sb.append(" COALESCE (count(zdqxid), 0) zdqxcount,COALESCE (count(cjid), 0) cjcount ");
		sb.append(" from ( ");
		sb.append(" select case when bill_property in ('OPEN', 'SORT') then bill_amount else 0 end as unpaybillamount, ");
		sb.append("    case when bill_property != 'OPEN' then bill_amount else 0 end as paybillamount, bill_amount, ");
		sb.append("    case when bill_property in ('OPEN', 'SORT') then guest else 0 end as insideguest, guest, ");
		sb.append(" CASE WHEN bill_property = 'OPEN' THEN id ELSE null END AS unpayid,");
		sb.append(" CASE WHEN bill_property = 'CLOSED' THEN id ELSE null END AS payid,");
		sb.append(" CASE WHEN bill_state = 'ZDQX02' THEN id ELSE null END AS zdqxid,");
		sb.append(" CASE WHEN bill_state = 'CJ01' THEN id ELSE null END AS cjid ");
		sb.append(" from pos_bill ");
		sb.append(" where store_id='" + storeId + "' and report_date='" + reportDate + "'");
		sb.append(" ) billtotal ) t ");
		return this.query4Json(tenancyId, sb.toString());
	}

	@Override
	public double getBillPaymentForSumAmount(String tenancyId, int storeId, String billNum) throws Exception
	{
		double amount = 0d;
		StringBuilder querySql = new StringBuilder("select sum(currency_amount) as currency_amount from pos_bill_payment where tenancy_id =? and store_id=? and bill_num=?");
		SqlRowSet rs = this.jdbcTemplate.queryForRowSet(querySql.toString(), new Object[]{tenancyId,storeId,billNum});
		if(rs.next())
		{
			amount = rs.getDouble("currency_amount");
		}
		return amount;
	}

//	@Override
//	public void insertPosBillMember(String tenancyId, Integer storeId, String billNum, Date reportDate, String type, double amount, double credit, String cardCode, String mobil, Timestamp lastUpdatetime, String remark, String billCode, String requestState, String customerCode, String customerName,
//			double consumeBeforeCredit, double consumeAfterCredit) throws Exception
//	{
//		// TODO Auto-generated method stub
//		
//	}
//
//	@Override
//	public void updatePosBillMemberForCustomer(String tenancyId, int storeId, String billNum) throws Exception
//	{
//		// TODO Auto-generated method stub
//		
//	}
//
//	@Override
//	public void deletePosBillMember(String tenancyId, int storeId, String billNum, Date reportDate, String type) throws Exception
//	{
//		// TODO Auto-generated method stub
//		
//	}

	@Override
	public List<JSONObject> getBillPaymentForNotChange(String tenancyId, int storeId, String billNum) throws Exception
	{
		// TODO Auto-generated method stub
		StringBuilder sql = new StringBuilder("select pw.payment_class,bp.*,(case when bp.type = ? then (select sum(currency_amount) from pos_bill_payment where bp.tenancy_id=tenancy_id and bp.store_id=store_id and bp.bill_num=bill_num and type=bp.type) else bp.currency_amount end) payment_amount");
		sql.append(" from pos_bill_payment bp left join payment_way pw on bp.jzid=pw.id where bp.tenancy_id=? and bp.store_id=? and bp.bill_num=? and coalesce(bp.name_english,'')<>? order by bp.id");
		return this.query4Json(tenancyId, sql.toString(), new Object[]{SysDictionary.PAYMENT_CLASS_CASH,tenancyId,storeId,billNum,SysDictionary.PAYMENT_CHANGE_ENGLISH_NAME});
	}

	@Override
	public void copyPaymentCouponsByPaymentId(String tenancyId, int storeId, String billNum, String paymentId, String newBillNum, String newPaymentId, Timestamp currentTime) throws Exception
	{
		StringBuilder sql = new StringBuilder("insert into pos_bill_payment_coupons(tenancy_id,store_id,bill_num,report_date,payment_id,coupons_code,deal_value,deal_name,last_updatetime,remark,upload_tag,is_cancel)");
		sql.append(" select tenancy_id,store_id,? as bill_num,report_date,? as payment_id,coupons_code,deal_value,deal_name,? as last_updatetime,remark,'0'as upload_tag,'0' as is_cancel from pos_bill_payment_coupons where tenancy_id=? and store_id=? and bill_num=? and payment_id=?");
		this.update(sql.toString(), new Object[]
		{ newBillNum, newPaymentId, currentTime, tenancyId, storeId, billNum, paymentId });
	}
	
	public List<JSONObject> getRwidsWithoutWaitCallItem(String tenancyId, int storeId, String billNum, String orderRemark, String posNum) throws Exception
	{
		StringBuilder sql = new StringBuilder("select pbi.rwid,pbi.order_number,pbi.waitcall_tag from pos_bill_item pbi where pbi.tenancy_id=? and pbi.store_id=? and pbi.bill_num=? and pbi.order_remark=? and pbi.item_mac_id=? order by pbi.rwid");
		return this.query4Json(tenancyId, sql.toString(), new Object[]
		{ tenancyId, storeId, billNum, orderRemark, posNum });
	}

	@Override
	public String getYtByOrganId(String tenantId, int store_id) throws Exception{
		String sql = "select format_state from organ where id = ?";
		List<JSONObject> lst = this.query4Json(tenantId, sql, new Object[]{store_id});
		if(lst!=null && lst.size()>0){
			JSONObject json = lst.get(0);
			return json.getString("format_state");
		}
		return null;
	}
	@Override
	public List<JSONObject> getPosBillItemByBillNum(String tenantId,
			int store_id, String oldBillNum)throws Exception {
		String sql =" select * from pos_bill_item where tenancy_id=? and store_id=? and bill_num=? order by item_serial,rwid";
		return this.query4Json(tenantId, sql, new Object[]{tenantId,store_id,oldBillNum});
	}
	@Override
	public List<JSONObject> getzfkwItemsByBillNum(String tenantId,
			int store_id, String oldBillNum) throws Exception {
		String sql =" select * from pos_zfkw_item where tenancy_id=? and store_id=? and bill_num=?";
		return this.query4Json(tenantId, sql, new Object[]{tenantId,store_id,oldBillNum});
	}
	@Override
	@Deprecated
	public List<JSONObject> getposBillMemberByBillNum(String tenantId,
			int store_id, String oldBillNum, String type) throws Exception {
		String sql =" select * from pos_bill_member where tenancy_id=? and store_id=? and bill_num=? and type =?";
		return this.query4Json(tenantId, sql, new Object[]{tenantId,store_id,oldBillNum,type});
	}
	@Override
	public int updateTableState(String tenantId, int store_id,String tableCode, String tableStateBusy) throws Exception 
	{
		StringBuffer updateTableState = new StringBuffer("update pos_tablestate set state = ? where table_code = ? and store_id = ? and tenancy_id = ?");
		if(SysDictionary.TABLE_STATE_BUSY.equals(tableStateBusy))
		{
			updateTableState.append(" and state='").append(SysDictionary.TABLE_STATE_FREE).append("'");
		}
		return this.update(updateTableState.toString(), new Object[]
		{ tableStateBusy, tableCode, store_id, tenantId });
	}
	
	@Override
	public int getRwidById(String tenantId, int itemIdNew) throws Exception
	{
		return this.queryForInt("select rwid from pos_bill_item where id = ?", new Object[]
		{ itemIdNew });
	}
	
	@Override
	public List<JSONObject> findBillByOldBillNum(String tenantId, int store_id, String oldBillNum) throws Exception
	{
		StringBuilder sql = new StringBuilder("select * from pos_bill where bill_num = '" + oldBillNum + "' and store_id = " + store_id);
		return this.queryString4Json(tenantId, sql.toString());
	}
	@Override
	public List<JSONObject> findBillServiceByOldBillNum(String tenantId,
			Integer storeId, String oldBillNum) throws Exception {
		StringBuilder sql = new StringBuilder("select * from pos_bill_service where bill_num = '" + oldBillNum + "' and tenancy_id='"+tenantId+"' and store_id = " + storeId );
		return this.queryString4Json(tenantId, sql.toString());
	}
	
	@Override
	public JSONObject getServiceFeeByServiceId(String tenancyId, int storeId, int serviceId) throws Exception
	{
		String querySql = new String("select sf.* from hq_service_fee_of_organ sfo left join hq_service_fee_type sf  on sf.id=sfo.service_fee_id where sfo.tenancy_id=? and sfo.store_id=? and sfo.service_fee_id=?");
		List<JSONObject> list = this.query4Json(tenancyId, querySql.toString(), new Object[]
		{ tenancyId, storeId, serviceId });
		if (null != list && list.size() > 0)
		{
			return list.get(0);
		}
		return null;
	}

	@Override
	public void insertPosBillService(String tenancyId, int storeId, PosBillServiceEntity billService) throws Exception
	{
		String insertSql = new String("insert into pos_bill_service(tenancy_id,store_id,bill_num,table_code,stable_code,service_id,service_type,taken_mode,service_scale,service_amount,service_count,service_total,service_rate,upload_tag) values(?,?,?,?,?,?,?,?,?,?,?,?,?,'0')");
		this.update(insertSql.toString(), new Object[]
		{ tenancyId, storeId, billService.getBillNum(), billService.getTableCode(), billService.getStableCode(), billService.getServiceId(), billService.getServiceType(), billService.getTakenMode(), billService.getServiceScale(), billService.getServiceAmount(), billService.getServiceCount(),
				billService.getServiceTotal(), billService.getServiceRate() });
	}

	@Override
	public void copyPosBillServiceByBillNum(String tenancyId, int storeId, String fromBillNum, String toBillNum, String fromTableCode, String toTableCode, String serviceType) throws Exception
	{
		// TODO Auto-generated method stub
		StringBuilder copyPosBillServiceSql = new StringBuilder("insert into pos_bill_service(tenancy_id,store_id,bill_num,table_code,stable_code,service_id,service_type,taken_mode,service_scale,service_amount,service_count,service_total,service_rate,upload_tag)");
		copyPosBillServiceSql.append(" select tenancy_id,store_id,? as bill_num,? as table_code,? as stable_code,service_id,service_type,taken_mode,service_scale,service_amount,service_count,service_total,service_rate,'0' as upload_tag from pos_bill_service");
		copyPosBillServiceSql.append(" where tenancy_id = ? and store_id = ? and bill_num = ? and service_type=?");

		this.update(copyPosBillServiceSql.toString(), new Object[]
		{ toBillNum, toTableCode, fromTableCode, tenancyId, storeId, fromBillNum, serviceType });
	}
	
	@Override
	public JSONObject getPosTableInfoByTablecode(String tenancyId, int storeId, String tableCode) throws Exception
	{
		StringBuffer posTablestateSql =  new StringBuffer("select count(*) as num from pos_tablestate pt where pt.table_code=? and pt.stable is null");
		SqlRowSet rs= this.query4SqlRowSet(posTablestateSql.toString(), new Object[]{tableCode});
		rs.next();
		int num = rs.getInt("num");
		// TODO Auto-generated method stub
		StringBuffer queryTableSql = new StringBuffer("select pt.state,ti.fwfz_id,ti.table_name,pt.table_open_num from pos_tablestate pt left join tables_info ti on pt.tenancy_id=ti.tenancy_id and pt.store_id=ti.organ_id ");
		if(num != 0){// 未拆台
			queryTableSql.append(" and pt.table_code=ti.table_code ");
		}else{//已拆台
			queryTableSql.append(" and pt.stable=ti.table_code ");
		}
		queryTableSql.append("  where pt.tenancy_id=? and pt.store_id=? and pt.table_code=?");
		List<JSONObject> list = this.query4Json(tenancyId, queryTableSql.toString(), new Object[]
		{ tenancyId, storeId, tableCode });
		if (null != list && list.size() > 0)
		{
			JSONObject json = list.get(0);
			if(num == 0){
				json.put("fwfz_id", 0);
				json.put("table_name", json.getString("table_name")+"-"+json.getInt("table_open_num"));
			}
			
			return json;
		}
		return null;
	}

	@Override
	public JSONObject getOpenBillByTablecode(String tenancyId, int storeId, String tableCode) throws Exception
	{
		StringBuffer ssql = new StringBuffer("select bill_num,order_num,payment_state,coalesce(guest, 0) guest ,discount_amount,sbill_num  from pos_bill where tenancy_id=? and store_id = ? and fictitious_table = ? and bill_property=?");
		List<JSONObject> list = this.query4Json(tenancyId, ssql.toString(), new Object[]
		{ tenancyId, storeId, tableCode, SysDictionary.BILL_PROPERTY_OPEN });
		if (null != list && list.size() > 0)
		{
			return list.get(0);
		}
		return null;
	}

	@Override
	public List<JSONObject> getHqItemInfoByItem(String tenancyId, int storeId, String chanel, List<?> item) throws Exception
	{
		StringBuilder itemIds = new StringBuilder("");
		for (Object obj : item)
		{
			JSONObject itemJson = JSONObject.fromObject(obj);
			itemIds.append(itemJson.optInt("item_id")).append(",");
		}

		if (itemIds.length() > 0)
		{
			itemIds.setLength(itemIds.length() - 1);
			StringBuilder rsStr = new StringBuilder("");
			rsStr.append(" select hii.id as item_id,hii.item_code,coalesce(him.item_name,hii.item_name) as item_name, (case when him.item_english is null or him.item_english = '' then hii.item_english else him.item_english end) as item_english,coalesce(him.class,hii.item_class) as item_class,hii.is_pushmoney,hii.pushmoney_way,hii.proportion_money,hii.is_discount,hii.is_combo,him.details_id from hq_item_info hii");
			rsStr.append(" left join (select himo.tenancy_id,him.id as item_menu_id,himd.id as details_id,himd.item_id,himc.class,himc.item_name,himc.item_english");
			rsStr.append(" from hq_item_menu_organ himo");
			rsStr.append(" left join hq_item_menu him on him.id=himo.item_menu_id and him.tenancy_id=himo.tenancy_id and him.valid_state='1'");
			rsStr.append(" left join hq_item_menu_details himd on himd.item_menu_id=him.id and himd.tenancy_id=him.tenancy_id and himd.valid_state='1'");
			rsStr.append(" left join hq_item_menu_class himc on himc.details_id=himd.id and himc.tenancy_id=himd.tenancy_id");
			rsStr.append(" where himo.tenancy_id=? and himo.store_id=? and himc.chanel=?) him on him.item_id=hii.id and him.tenancy_id=hii.tenancy_id");
			rsStr.append(" where hii.id in (").append(itemIds).append(")");
			return this.query4Json(tenancyId, rsStr.toString(), new Object[]
			{ tenancyId, storeId, chanel });
		}

		return null;
	}

	@Override
	public List<JSONObject> getHqItemComboDetailsByItem(String tenancyId, int storeId,String chanel,List<Integer> item) throws Exception
	{
		StringBuilder itemIds = new StringBuilder("");
		for (Object obj : item)
		{
			itemIds.append(Integer.parseInt(obj.toString())).append(",");
		}

		if (itemIds.length() > 0)
		{
			itemIds.setLength(itemIds.length() - 1);
			StringBuilder rsStr = new StringBuilder("");
			rsStr.append(" select cd.details_id, COALESCE(ip.price,ig.price) price,cd.id,cd.iitem_id from hq_item_combo_details cd");
			rsStr.append(" left join organ og on cd.tenancy_id=og.tenancy_id");
			rsStr.append(" left join hq_item_pricesystem ip on cd.item_unit_id=ip.item_unit_id and ip.price_system||''=og.price_system and ip.chanel=? and cd.is_itemgroup='N'");
			rsStr.append(" left join hq_item_group_pricesystem ig on cd.details_id=ig.item_group_iid and ig.price_system||''=og.price_system and ig.chanel=? and cd.is_itemgroup='Y'");
			rsStr.append(" where og.tenancy_id=? and og.id=?");
			rsStr.append(" and cd.iitem_id in (").append(itemIds).append(")");
			return this.query4Json(tenancyId, rsStr.toString(), new Object[]
			{ chanel, chanel, tenancyId, storeId });
		}
		return null;
	}
	
	@Override
	public List<JSONObject> getHqItemMethodByID(String tenancyId, int storeId, List<Integer> methodIds) throws Exception
	{
		StringBuilder itemIds = new StringBuilder("");
		for (Integer obj : methodIds)
		{
			itemIds.append(obj).append(",");
		}

		if (itemIds.length() > 0)
		{
			itemIds.setLength(itemIds.length() - 1);
			StringBuilder rsStr = new StringBuilder("select id as method_id,item_id,makeup_way,proportion_money,is_default from hq_item_method");
			rsStr.append(" where id in (").append(itemIds).append(")");
			return this.query4Json(tenancyId, rsStr.toString(), new Object[] {});
		}
		return null;
	}

	@Override
	public List<JSONObject> getHqItemUnitByItem(String tenancyId, int storeId, List<?> item) throws Exception
	{
		// TODO Auto-generated method stub
		StringBuilder itemIds = new StringBuilder("");
		for (Object obj : item)
		{
			JSONObject itemJson = JSONObject.fromObject(obj);
			itemIds.append(itemJson.optInt("unit_id")).append(",");
		}

		if (itemIds.length() > 0)
		{
			itemIds.setLength(itemIds.length() - 1);
			StringBuilder rsStr = new StringBuilder("select id as unit_id,item_id,unit_name,standard_price,is_default,COALESCE(count_rate,1) as count_rate from hq_item_unit");
			rsStr.append(" where id in (").append(itemIds).append(")");
			return this.query4Json(tenancyId, rsStr.toString(), new Object[] {});
		}
		return null;
	}

	@Override
	public Map<String, Object> getPosSoldOutByItem(String tenancyId, int storeId, Date reportDate, List<?> item) throws Exception
	{
		// TODO Auto-generated method stub
		StringBuilder itemIds = new StringBuilder("");
		for (Object obj : item)
		{
			JSONObject itemJson = JSONObject.fromObject(obj);
			itemIds.append(itemJson.optInt("item_id")).append(",");
		}
		Map<String, Object> soldOutMap = new HashMap<String, Object>();
		if (itemIds.length() > 0)
		{
			itemIds.setLength(itemIds.length() - 1);
			StringBuilder rsStr = new StringBuilder("select item_id,num from pos_soldout where tenancy_id=? and store_id=? and setdate=?");
			rsStr.append(" and item_id in (").append(itemIds).append(")");
			List<JSONObject> soldOutList = this.query4Json(tenancyId, rsStr.toString(), new Object[]
			{ tenancyId, storeId, reportDate });

			for (JSONObject soldOutJson : soldOutList)
			{
				soldOutMap.put(soldOutJson.optString("item_id"), soldOutJson.optDouble("num"));
			}
		}
		return soldOutMap;
	}

	@Override
	public List<PosBillBooked> findPosBillBookedItem(String tenantId,
			Integer organId, List<PosBillBooked> list) throws SystemException {

		// 查询账单详细
		StringBuffer str = new StringBuffer("");
        //"select item_unit_name,item_serial,rwid,item_id,item_num,item_name,item_remark,item_property,round(discount_rate,2) as discount_rate,round(discount_amount,2) as discount_amount,round(item_price,2) as item_price,round(item_count,2) as item_count,round(item_amount,2) as item_amount,round(real_amount,2) as real_amount,remark,item_unit_id as unit_id,details_id,item_taste,sale_mode,setmeal_id,setmeal_rwid,waitcall_tag,assist_num,assist_money,print_tag,assist_item_id,item_time,waiter_num as waiter_name,item_mac_id,item_time,assist_num,method_money,batch_num from pos_bill_item where bill_num = ? and store_id = ?");

		str.append("select bi.item_unit_name,bi.item_serial,bi.rwid,bi.item_id,bi.bill_booked_num,bi.item_name,bi.item_remark,bi.item_property,round(bi.discount_rate,2) as discount_rate,round(bi.discount_amount,2) as discount_amount,round(bi.item_price,2) as item_price,round(bi.item_count,2) as item_count,round(bi.item_amount,2) as item_amount,round(bi.real_amount,2) as real_amount,bi.remark,bi.item_unit_id as unit_id,bi.details_id,trim(bi.item_taste) as item_taste,bi.sale_mode,bi.setmeal_id,bi.setmeal_rwid,bi.waitcall_tag,bi.assist_num,bi.assist_money,bi.print_tag,bi.assist_item_id,bi.item_time,bi.waiter_num as waiter_name,bi.item_mac_id,bi.item_time,bi.assist_num,bi.method_money,bi.batch_num,coalesce(p.payment_state,'"+SysDictionary.PAYMENT_STATE_NOTPAY+"') as payment_state");
		str.append(" from pos_bill_item_booked bi left join (select tenancy_id,store_id,bill_num,batch_num,(case when count(nullif(payment_state,'"+SysDictionary.PAYMENT_STATE_PAY_COMPLETE+"'))>0 then '"+SysDictionary.PAYMENT_STATE_PAY+"' else '"+SysDictionary.PAYMENT_STATE_PAY_COMPLETE+"' end) as payment_state from pos_bill_payment where payment_state is null or payment_state in ('"+SysDictionary.PAYMENT_STATE_NOTPAY+"','"+SysDictionary.PAYMENT_STATE_PAY+"','"+SysDictionary.PAYMENT_STATE_PAY_COMPLETE+"') group by tenancy_id,store_id,bill_num,batch_num) p on bi.tenancy_id=p.tenancy_id and bi.store_id=p.store_id and bi.bill_booked_num=p.bill_num and bi.batch_num=p.batch_num where bi.tenancy_id=? and bi.store_id=? and bi.bill_booked_num=?");
		System.out.println(str.toString());
		// 做法
		//StringBuilder queryZFKWSql = new StringBuilder("select zfkw_id as method_id,zfkw_name as method_name,amount from pos_zfkw_item where tenancy_id=? and store_id=? and bill_num=? and rwid=? and type=?");

		// 查询账单付款方式
		//StringBuilder payment = new StringBuilder("select id,jzid,name,amount,count,number,phone,shift_id,pos_num,cashier_num,last_updatetime,customer_id,bill_code,remark,currency_amount,batch_num from pos_bill_payment where bill_num = ? and store_id = ? ");

		// 账单会员
		//StringBuilder bMemberSql = new StringBuilder("select id,type,amount,credit,card_code,mobil,last_updatetime,remark from pos_bill_member where store_id=? and bill_num = ?");

		// 开发票
//		StringBuilder queryInvoiceSql = new StringBuilder("select invoice_num,invoice_count,invoice_amount,last_updatetime,remark from pos_bill_invoice where tenancy_id=? and store_id=? and (bill_num=? or copy_bill_num=?) ");
		//StringBuilder queryInvoiceSql = new StringBuilder("select sum(coalesce(invoice_count,0)) as invoice_count,sum(abs(coalesce(invoice_count,0)) * coalesce(invoice_amount,0)) as invoice_amount from pos_bill_invoice where tenancy_id=? and store_id=? and (bill_num=? or copy_bill_num=?) ");

		// 查询batch
		//StringBuilder queryBatchSql = new StringBuilder("select id,batch_num,payment_amount,bill_state,bill_property,payment_state from pos_bill_batch where tenancy_id=? and store_id=? and bill_num=?");

		//查询附加服务费
		//StringBuilder queryService = new StringBuilder(" SELECT service_id, service_type, sum(coalesce(service_count,0)) as service_count, sum(coalesce(service_total,0)) as service_total from pos_bill_service ");
		//queryService.append(" where tenancy_id=? and store_id=? and bill_num=? group by service_id, service_type");

		List<PosBillBooked> posBills = new ArrayList<PosBillBooked>();
		for (int i = 0; i < list.size(); i++)
		{
			PosBillBooked bill = list.get(i);
			String billno = bill.getBill_booked_num();

			StringBuilder queryItemSql = new StringBuilder();
			if (SysDictionary.BILL_STATE_CJ01.equalsIgnoreCase(bill.getBill_state()))
			{
				queryItemSql.append(str.toString()).append(" order by bi.item_serial,bi.rwid");
			}
			else
			{
				queryItemSql.append(str.toString()).append(" and (bi.item_remark is null or bi.item_remark<>'"+SysDictionary.ITEM_REMARK_CJ05+"') order by bi.item_serial,bi.rwid");
			}

			List<PosBillBookedItem> items = this.jdbcTemplate.query(queryItemSql.toString(), new Object[]
			{ tenantId, organId, billno }, BeanPropertyRowMapper.newInstance(PosBillBookedItem.class));

			List<PosBillBookedItem> posItems = new ArrayList<PosBillBookedItem>();
			for(PosBillBookedItem item : items)
			{
				//取出做法
				Integer rwid = null;
				if (StringUtils.isNotEmpty(item.getRwid()))
				{
					rwid = Integer.parseInt(item.getRwid());
				}

				if(SysDictionary.ITEM_REMARK_FS02.equals(item.getItem_remark()))
				{
					item.setPayment_state(SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
				}

				/*List<PosItemMethod> methodList = this.jdbcTemplate.query(queryZFKWSql.toString(), new Object[]
						{ tenantId, organId, billno, rwid, SysDictionary.ZFKW_TYPE_METHOD },
						ParameterizedBeanPropertyRowMapper.newInstance(PosItemMethod.class));
				item.setMethod(methodList);*/

				posItems.add(item);
			}
			bill.setDetaillist(posItems);


		/*	List<PosBillMember> posMember = this.jdbcTemplate.query(bMemberSql.toString(), new Object[]
			{ organId, billno }, ParameterizedBeanPropertyRowMapper.newInstance(PosBillMember.class));
			bill.setMemberlist(posMember);*/

		/*	List<PosBillPaymentWay> paymentWays = this.jdbcTemplate.query(payment.toString(), new Object[]
			{ billno, organId }, ParameterizedBeanPropertyRowMapper.newInstance(PosBillPaymentWay.class));
			bill.setPaymentlist(paymentWays);*/

			/*List<PosBillService> serviceList = this.jdbcTemplate.query(queryService.toString(), new Object[]
					{ tenantId, organId, billno }, ParameterizedBeanPropertyRowMapper.newInstance(PosBillService.class));
			bill.setServicelist(serviceList);*/


		/*	if (!SysDictionary.BILL_STATE_CJ01.equalsIgnoreCase(bill.getBill_state()))
			{
				List<JSONObject> invoiceList;
				try
				{
					invoiceList = this.query4Json(tenantId, queryInvoiceSql.toString(), new Object[]
					{ tenantId, organId, billno, billno });
					bill.setInvoicelist(invoiceList);
				}
				catch (Exception e)
				{
					e.printStackTrace();
				}
			}

			String mode= "01";
			try
			{
				mode = getSysParameter(tenantId, organId, "ZCDCMS");// 01,普通;02,自助;
				if (Tools.isNullOrEmpty(mode))
				{
					mode = "01";
				}
			}
			catch (Exception e)
			{
				e.printStackTrace();
			}*/
			/*if("02".equals(mode))
			{
				List<PosBillBatch> batchList = this.jdbcTemplate.query(queryBatchSql.toString(), new Object[]
						{ tenantId, organId, billno }, ParameterizedBeanPropertyRowMapper.newInstance(PosBillBatch.class));
				bill.setBatchList(batchList);
			}*/

			posBills.add(bill);
		}
		return posBills;
	}

	/**
	 * 根据id查询订单
	 * @param tenancyId
	 * @param storeId
	 * @param billBookedNum
	 * @return
	 * @throws Exception
	 */
	public String getBillBookedId(String tenancyId, Integer storeId,String billBookedNum,String str) throws Exception {
		StringBuilder sql = new StringBuilder();
		sql.append(" select bill_booked_num from pos_bill_booked ");
		sql.append(" where tenancy_id=? and store_id=? and bill_booked_num=?  ");
		if(str.equals("0")){
			sql.append(" and load_state='0'  ");
		}
		SqlRowSet rs = this.jdbcTemplate.queryForRowSet(sql.toString() ,new Object[]{tenancyId,storeId,billBookedNum});
		while(rs.next()){
			return rs.getString("bill_booked_num");
		}
		return null;
	}

	/**
	 * 修改预点状态
	 * @param tenancyId
	 * @param storeId
	 * @param billBookedNum
	 */
	public void updateBillBooked(String tenancyId, Integer storeId, String billBookedNum) {
		StringBuilder sql = new StringBuilder();
		sql.append(" update pos_bill_booked set load_state='1'");
		sql.append(" where tenancy_id=? and store_id=? and bill_booked_num=?");
		this.jdbcTemplate.update(sql.toString(),new Object[]{tenancyId,storeId,billBookedNum});

	}

	@Override
	public List<JSONObject> getPosBillItemByRwid(String tenancyId, Integer storeId, String billNum, Integer rwid) throws Exception
	{
		StringBuilder cpsql = new StringBuilder("select item_id,item_name,item_serial,setmeal_id,setmeal_rwid,item_count,item_remark,item_property,coalesce(return_count,0) return_count,item_amount,discount_amount from pos_bill_item where rwid = ? and bill_num = ? and store_id = ? and tenancy_id=?");
		return this.query4Json(tenancyId, cpsql.toString(), new Object[]
		{ rwid, billNum, storeId, tenancyId });
	}

	/** 记录桌位操作日志
	 * @param tenancyId
	 * @param storeId
	 * @param reportDate
	 * @param jsonObject
	 */
	@Transactional(propagation = Propagation.REQUIRED)
	public void savePosTableLog(String tenancyId, int storeId, Date reportDate, JSONObject jsonObject)  {
		String empName = "";// 操作员姓名
		Timestamp updateTime = DateUtil.currentTimestamp();
		if(null != jsonObject) {
			String optNum = jsonObject.optString("opt_num");
			empName = this.getEmpNameById(optNum, tenancyId, storeId);
			jsonObject.put("opt_name", empName);
		}
		try
		{
			StringBuilder insertSql = new StringBuilder("INSERT INTO pos_table_log (tenancy_id,store_id,pos_num,opt_num,opt_name,from_bill_num,");
			insertSql.append("to_bill_num,from_table_code,to_table_code,table_count,types,update_time,item_id,item_name,item_count,report_date)");
			insertSql.append("values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");

			this.jdbcTemplate.update(insertSql.toString(), new Object[] {tenancyId, storeId, jsonObject.optString("pos_num"),
				jsonObject.optString("opt_num"),jsonObject.optString("opt_name"),jsonObject.optString("from_bill_num"),
				jsonObject.optString("to_bill_num"),jsonObject.optString("from_table_code"),jsonObject.optString("to_table_code"),
				jsonObject.optInt("table_count"),jsonObject.optInt("types"),updateTime,jsonObject.optInt("item_id"),
				jsonObject.optString("item_name"),jsonObject.optDouble("item_count"), reportDate});
		}
		catch (DataAccessException e)
		{
			logger.error("保存桌位操作日志错误", e);
			e.printStackTrace();
		}
	}

	@Override
	public boolean getHqItemTables(String tenantId, Integer storeId, String tableCode) throws Exception{
		boolean result = false;
		StringBuilder tables = new StringBuilder("select table_id from hq_item_tables where store_id = ? and tenancy_id=? and valid_state='1' ");
		List<JSONObject> tableList =  this.query4Json(tenantId, tables.toString(), new Object[]{storeId, tenantId });
		if(tableList.isEmpty()){//若无自动增加菜品区域 默认全部
			result=true;
		}else{
			//根据桌台编号获取桌台id
			StringBuilder tsql = new StringBuilder("SELECT id from tables_info where tenancy_id=? and organ_id=? and table_code=? ");
			List<JSONObject> tableIdList =  this.query4Json(tenantId, tsql.toString(), new Object[]{tenantId, storeId,tableCode });
			Integer id = null;//桌台id
			if(tableIdList.isEmpty()){
				result=false;
			}else{
				id = tableIdList.get(0).getInt("id");
			}
			if(id!=null){
				for(JSONObject o:tableList){
					if(id==o.getInt("table_id")){//判断桌台是否在自动增加菜品区域内
						result=true;
						break;
					}
				}
			}
		}
		return result;
	}
	
	@Override
	public void insertPosBillTake(String tenancyId, int storeId,
			Date reportDate, int shiftId, String billNum, String batchNum,
			String serialNum, String orderNum, Timestamp opentableTime,
			String openPosNum, String openOpt, String waiterNum,
			String tableCode, int guest, int itemMenuId, String saleMode,
			String source, int serviceId, double serviceAmount,
			String copyBillNum, String remark, String billProperty,
			String paymentState, int discountModeId, double discountAmount,
			double shopRealAmount, double platformChargeAmount,
			String settlementType, double discountRate, String fictitiousTable)
			throws Exception {
		double total_fees=0d;
		StringBuffer saveBill = new StringBuffer(
				"insert into pos_bill (tenancy_id,store_id,report_date,shift_id,bill_num,batch_num,serial_num,order_num,opentable_time,open_pos_num,open_opt,waiter_num,table_code,guest,item_menu_id,sale_mode,source,service_id,service_amount,copy_bill_num,remark,bill_property,payment_state,discount_mode_id,discount_amount,shop_real_amount,platform_charge_amount,total_fees,settlement_type,discount_rate,fictitious_table,discountk_amount) ");
		saveBill.append(" values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");

		this.update(saveBill.toString(), new Object[]
		{ tenancyId,storeId,reportDate,shiftId,billNum,batchNum,serialNum,orderNum,opentableTime,openPosNum,openOpt,waiterNum,tableCode,guest,itemMenuId,saleMode,source,serviceId,serviceAmount,copyBillNum,remark,billProperty,paymentState,discountModeId,discountAmount,shopRealAmount,platformChargeAmount,total_fees,settlementType,discountRate,fictitiousTable,discountAmount});
		
	}
	
	@Override
	public int batchUpdateBillItem(String tenancyid, List<com.tzx.base.entity.PosBillItem> itemList) throws Exception
	{
		if (null != itemList && 0 < itemList.size())
		{
			String sql = new String(
					"insert into pos_bill_item (tenancy_id,store_id,bill_num,details_id,item_id,item_num,item_name,item_english,report_date,item_unit_id,item_unit_name,table_code,stable_code,pushmoney_way,proportion,assist_num,item_assist_num,waiter_num,item_price,item_count,discount_rate,discount_state,discount_mode_id,item_class_id,item_property,item_remark,print_tag,setmeal_id,setmeal_rwid,is_setmeal_changitem,item_time,item_serial,item_shift_id,item_mac_id,order_remark,seat_num,sale_mode,item_taste,assist_item_id,waitcall_tag,returngive_reason_id,batch_num,order_number,opt_num,manager_num,discount_reason_id,activity_id,activity_batch_num,activity_rule_id,activity_count,yrwid,print_count,default_state,single_amount,single_discount_rate,discount_num,count_rate,combo_prop,origin_item_price,price_type,special_price_id)  values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
			List<Object[]> batchArgs = new ArrayList<Object[]>();
			for (com.tzx.base.entity.PosBillItem item : itemList)
			{
				batchArgs.add(new Object[]
				{ item.getTenancy_id(), item.getStore_id(), item.getBill_num(), item.getDetails_id(), item.getItem_id(), item.getItem_num(), item.getItem_name(), item.getItem_english(), item.getReport_date(), item.getItem_unit_id(), item.getItem_unit_name(), item.getTable_code(),item.getStable_code(),
						item.getPushmoney_way(), item.getProportion(), item.getAssist_num(),item.getItem_assist_num(), item.getWaiter_num(), item.getItem_price(), item.getItem_count(), item.getDiscount_rate(), item.getDiscount_state(), item.getDiscount_mode_id(), item.getItem_class_id(), item.getItem_property(),
						item.getItem_remark(), item.getPrint_tag(), item.getSetmeal_id(), item.getSetmeal_rwid(), item.getIs_setmeal_changitem(), item.getItem_time(), item.getItem_serial(), item.getItem_shift_id(), item.getItem_mac_id(), item.getOrder_remark(), item.getSeat_num(),
						item.getSale_mode(), item.getItem_taste(), item.getAssist_item_id(), item.getWaitcall_tag(), item.getReturngive_reason_id(), item.getBatch_num(), item.getOrder_number(), item.getOpt_num(), item.getManager_num(), item.getDiscount_reason_id(), item.getActivity_id(),
						item.getActivity_batch_num(), item.getActivity_rule_id(), item.getActivity_count(), item.getYrwid(), item.getPrint_count(), item.getDefault_state(), item.getSingle_amount(), item.getSingle_discount_rate(), item.getDiscount_num(), item.getCount_rate(), item.getCombo_prop(),
						item.getOrigin_item_price() ,item.getPrice_type(),item.getSpecial_price_id()});
			}

			if (0 < batchArgs.size())
			{
				logger.info(batchArgs.size());
				this.batchUpdate(sql, batchArgs);
				return batchArgs.size();
			}
		}
		return 0;
	}
	
	@Override
	public int selectMaxItemSerial(String billNum, int storeId, String tenancyId)
			throws Exception {
		StringBuilder maxSerialSql = new StringBuilder(
				"select coalesce(max(item_serial), 0) from pos_bill_item i where i.bill_num = ? and i.store_id = ? and i.tenancy_id = ?");
		
		return this.queryForInt(maxSerialSql.toString(), new Object[]{billNum, storeId, tenancyId});
	}

	@Override
	public boolean isExistsCombinedAfterBillItem(String tenancyId, int storeId, String bill_num) throws Exception{
		boolean result = true;
		StringBuilder sql = new StringBuilder("select id from pos_bill  where tenancy_id=? and store_id=? and bill_num=? and is_combined_after='"+SysDictionary.IS_COMBINED_AFTER+"'");
		List<JSONObject> list= this.queryString4Json(tenancyId, sql.toString(), new Object[]{ tenancyId, storeId,bill_num });
		if(list== null || list.isEmpty()){
			result = false;
		}
		return  result;
	}

    @Override
	public void checkCombineTable(String tenancyId, int storeId, String bill_num) throws Exception {
		String is_combined_after = null;

		String querySql = "select sbill_num from pos_bill  where tenancy_id=? and store_id=? and bill_num=?";
		List<JSONObject> list = this.queryString4Json(tenancyId, querySql.toString(), new Object[]{tenancyId, storeId, bill_num});
		if (null != list && !list.isEmpty()) {
			String sbill_num = list.get(0).optString("sbill_num");
			if (Tools.hv(sbill_num)) {
				//是并台账单
				is_combined_after = SysDictionary.IS_COMBINED_AFTER;
			} else {
				querySql = "select id,bill_num from pos_bill  where tenancy_id=? and store_id=? and sbill_num like '%" + bill_num + "%'";
				List<JSONObject> slist = this.queryString4Json(tenancyId, querySql.toString(), new Object[]{tenancyId, storeId});
				if (null != slist && !slist.isEmpty()) {
					//是并台账单
					is_combined_after = SysDictionary.IS_COMBINED_AFTER;
					bill_num = slist.get(0).optString("bill_num");
				}
			}

			if (null != is_combined_after && Tools.hv(bill_num)) {
				String updateSql = "update pos_bill  set is_combined_after =? where tenancy_id=? and store_id=? and bill_num=?";
				this.update(updateSql,new Object[]{is_combined_after,tenancyId, storeId, bill_num});
			}
		}
	}

	@Override
	public List<JSONObject> getItemComboDetails(String tenantId, Integer itemId) throws Exception
	{
		if (!org.springframework.util.StringUtils.isEmpty(itemId))
		{
			StringBuilder sql = new StringBuilder();
			sql.append(" select * from hq_item_combo_details where iitem_id ="+itemId);
			return this.query4Json(tenantId, sql.toString());
		}
		return null;
	}

	@Override
	public JSONObject isCombineTableOfBillNum(String tenancyId, int storeId, String bill_num) throws Exception{
		JSONObject billJson  = null;
		String querySql = "select id,sbill_num,bill_num,table_code,order_num from pos_bill  where tenancy_id=? and store_id=? and bill_num=? and bill_property IN ('OPEN') ";
		List<JSONObject> list = this.queryString4Json(tenancyId, querySql.toString(), new Object[]{tenancyId, storeId, bill_num});
		if (null != list && !list.isEmpty()) {
			JSONObject bill =  list.get(0);
			String sbill_num = bill.optString("sbill_num");
			if (null!=sbill_num && !"".equals(sbill_num) && Tools.hv(sbill_num)) {
				logger.info("并台账单信息 "+bill);
				return bill;
			}else {
				querySql = "select id,sbill_num,bill_num,table_code,order_num from pos_bill  where tenancy_id=? and store_id=? and bill_property IN ('OPEN') and sbill_num like '%" + bill_num + "%'";
				List<JSONObject> slist = this.queryString4Json(tenancyId, querySql.toString(), new Object[]{tenancyId, storeId});
				if (null != slist && !slist.isEmpty()) {
					JSONObject json =  slist.get(0);
					logger.info("并台账单信息 "+json);
					//是并台账单
					return json;
				}
			}
		}
		return billJson;
	}

	/**
	 * 根据套餐id 获取套餐明细信息
	 * @param tenancyId
	 * @param storeId
	 * @param chanel
	 * @param item
	 * @return
	 * @throws Exception
	 */
	@Override
	public List<JSONObject> getItemComboDetails(String tenancyId, int storeId, String chanel, List<?> item) throws Exception{
		StringBuilder itemIds = new StringBuilder("");
		for (Object obj : item)
		{
			JSONObject itemJson = JSONObject.fromObject(obj);
			itemIds.append(itemJson.optInt("item_id")).append(",");
		}

		if (itemIds.length() > 0)
		{
			itemIds.setLength(itemIds.length() - 1);
			StringBuilder rsStr = new StringBuilder("");
			rsStr.append(" SELECT d.id as combo_id,hii. ID AS item_id, d.iitem_id, hii.item_code, COALESCE ( him.item_name, hii.item_name ) AS item_name, ( CASE WHEN him.item_english IS NULL OR him.item_english = '' THEN hii.item_english ELSE him.item_english END ) AS item_english, ");
			rsStr.append(" COALESCE (him. CLASS, hii.item_class) AS item_class, hii.is_pushmoney, hii.pushmoney_way, hii.proportion_money, hii.is_discount, hii.is_combo, him.details_id, d.combo_num, d.standardprice as item_amount, d.combo_order,d.item_unit_id,u.unit_name ");
			rsStr.append(" FROM hq_item_combo_details d LEFT JOIN hq_item_info hii ON d.details_id = hii. ID AND d.tenancy_id = hii.tenancy_id ");
			rsStr.append(" LEFT JOIN hq_item_unit u ON d.item_unit_id = u.id and d.tenancy_id=u.tenancy_id ");
			rsStr.append(" LEFT JOIN ( SELECT himo.tenancy_id, him. ID AS item_menu_id, himd. ID AS details_id, himd.item_id, himc. CLASS, himc.item_name, himc.item_english FROM hq_item_menu_organ himo ");
			rsStr.append(" LEFT JOIN hq_item_menu him ON him. ID = himo.item_menu_id AND him.tenancy_id = himo.tenancy_id AND him.valid_state = '1'");
			rsStr.append(" LEFT JOIN hq_item_menu_details himd ON himd.item_menu_id = him. ID AND himd.tenancy_id = him.tenancy_id AND himd.valid_state = '1' ");
			rsStr.append(" LEFT JOIN hq_item_menu_class himc ON himc.details_id = himd. ID AND himc.tenancy_id = himd.tenancy_id WHERE himo.tenancy_id = ? AND himo.store_id = ? AND himc.chanel = ? ) him ");
			rsStr.append(" ON him.item_id = hii. ID AND him.tenancy_id = hii.tenancy_id");
			rsStr.append(" WHERE d.iitem_id in (").append(itemIds).append(") AND d.tenancy_id = ? and d.valid_state='1' ");
			return this.query4Json(tenancyId, rsStr.toString(), new Object[]
					{ tenancyId, storeId, chanel, tenancyId });
		}
		return null;
	}

	/**
	 * 查询菜品档案基本信息
	 *
	 * @param tenancyId
	 * @param itemCode
	 * @return
	 * @throws Exception
	 */
	@Override
	public List<JSONObject> getHqItemInfoByItemCode(String tenancyId, String itemCode) throws Exception {
		StringBuilder rsStr = new StringBuilder();
		rsStr.append(" select hii.id as item_id,hii.item_code,hii.item_name as item_name,hii.item_english,hii.item_class, ");
		rsStr.append("hii.is_pushmoney,hii.pushmoney_way,hii.proportion_money,hii.is_discount,hii.is_combo,0 as details_id,");
		rsStr.append("hiu.id as unitid,hiu.unit_name,hiu.is_default ");
		rsStr.append("from hq_item_info hii ");
		rsStr.append(" left join hq_item_unit hiu on hiu.item_id = hii.id and hiu.tenancy_id = hii.tenancy_id ");
		StringBuilder lastSql = new StringBuilder();
		lastSql.append(rsStr.toString());
		lastSql.append(" where hii.tenancy_id = '" + tenancyId + "' and hii.item_code = '" + itemCode + "' ");
		List<JSONObject> list = this.query4Json(tenancyId, lastSql.toString());
		if (list.size() == 0 &&  !CommonUtil.isNullOrEmpty(itemCode) && StringUtils.isNumeric(itemCode)) {
			lastSql.setLength(0);
			lastSql.append(rsStr.toString());
			lastSql.append(" where hii.tenancy_id = '" + tenancyId + "' and hiu.id = " + Integer.parseInt(itemCode) + " ");
			list = this.query4Json(tenancyId, lastSql.toString());
		}
		return list;
	}
}
