set NEBULA_BOH_HOME=%cd%

cd main
set javabin=%cd%\jre\bin
set JRE_HOME=%cd%\jre
SET CLASSPATH=.;


tasklist | findstr /i "javawBOH.exe"

if errorlevel 1 ( 
start "" "%javabin%\javawBOH" -Xmx512m -XX:MaxPermSize=256m -classpath %CLASSPATH% -jar lib\Console.jar

) else (
start ""  taskkill /f /t /im javawBOH.exe
ping -n 3 127.0.0.1

start "" "%javabin%\javawBOH" -Xmx512m -XX:MaxPermSize=256m -classpath %CLASSPATH% -jar lib\Console.jar

)
start ""  taskkill /f /t /im tasklist.exe
exit