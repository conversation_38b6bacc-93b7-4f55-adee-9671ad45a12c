package com.tzx.pos.po.springjdbc.dao;

import java.util.Date;
import java.util.List;

import net.sf.json.JSONObject;

import com.tzx.framework.common.exception.SystemException;
import com.tzx.pos.base.dao.BaseDao;

public interface PosSoldOutDao extends BaseDao
{
	String	NAME	= "com.tzx.pos.po.springjdbc.dao.imp.PosSoldOutDaoImp";

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param soldOutType
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getSoldOutListBySoldOutType(String tenancyId, Integer storeId, String soldOutType) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param soldOutType
	 * @return
	 * @throws Exception
	 */
	public int deleteSoldOutBySoldOutType(String tenancyId, Integer storeId, String soldOutType) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param soldOutList
	 * @return
	 * @throws Exception
	 */
	public int[] batchInsertSoldOut(String tenancyId, Integer storeId, List<Object[]> soldOutList) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param itemIds
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getItemGroupForSoldOutByItemIds(String tenancyId, Integer storeId, String itemIds) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param itemIds
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getItemComboForSoldOutByItemIds(String tenancyId, Integer storeId, String itemIds) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> findSoldOut(String tenancyId, Integer storeId) throws Exception;
	
	/** 消息推送查询估清
	 * @param tenancyId
	 * @param storeId
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getSoldOut(String tenancyId, Integer storeId) throws Exception;
	
    /**
     * 查询菜品规格
     * @param tenancyId
     * @param storeId
     * @return
     * @throws Exception
     */
    List<JSONObject> getItemUnits(String tenancyId, int storeId, Date reportDate) throws Exception;

    /**
     * 查询菜品沽清数量为0的
     * @param tenancyId
     * @param storeId
     * @param itemIds
     * @return
     * @throws Exception
     */
    List<JSONObject> getItems(String tenancyId, int storeId, String itemIds, Date reportDate) throws Exception;

    /**
     * 查询菜取消沽清的菜品对应的规格
     * @param tenancyId
     * @param itemIds
     * @return
     * @throws Exception
     */
    List<JSONObject> getUnits(String tenancyId, String itemIds) throws Exception;
    
	/**
	 * 根据itemId 获取套餐id
	 * @param itemId
	 * @return
	 * @throws SystemException
	 */
	public  List<JSONObject> getSoldOutComboId(String tenancyId, int storeId,Integer itemId,String str) throws Exception;
	
	/**
	 * 根据套餐id获取关联的套餐Id
	 * @param itemId
	 * @return
	 * @throws SystemException
	 */
	public  List<JSONObject> getSoldDetailsIdComboId(String tenancyId, Integer itemId,String str) throws Exception;
	
	/**
	 * 查询套餐下单品
	 * @param itemId
	 * @return
	 * @throws SystemException
	 */
	public List<JSONObject> getSingleByItemId(String tenancyId, int storeId,String iitem_id) throws Exception ;
	
	/**
	 * 根据itemId 根据单品id查询套餐沽清数据
	 * @param itemId
	 * @return
	 * @throws SystemException
	 */
	public  List<JSONObject> getComboOutBySingelItemId(String tenancyId, int storeId,Integer itemId) throws Exception;
	
	/**
	 * 根据单品关联的套餐id 查询套餐下单品的最小沽清数量
	 * @param itemId
	 * @return
	 * @throws SystemException
	 */
	public Integer getSingleMinByItemId(String tenancyId, int storeId,String iitem_id) throws Exception;
	
	/**
	 * 根据itemId 获取套餐详情itemid
	 * @param itemId
	 * @return
	 * @throws SystemException
	 */
	public  List<JSONObject> getComboItemByItemId(String tenancyId, Integer itemId) throws Exception;
	
	/**
	 * 查询套餐相关的Id
	 * @param itemIdStr
	 * @return
	 * @throws SystemException
	 */
	public  List<JSONObject> findComboItemId(String tenancyId, String itemIdStr) throws Exception;
	
	/**
	 * 查询是否为他餐 移动设备验证点餐
	 * @param itemId
	 * @return
	 * @throws SystemException
	 */
	public boolean isCheckComboByItemId(String tenancyId, String iitem_id) throws Exception ;
}
