package com.tzx.base.listener;

import java.io.Serializable;

import com.tzx.base.service.servlet.PushProcessMessage;

/**
 * 
 * <AUTHOR> email:she<PERSON><PERSON><PERSON>@tzx.com.cn
 * @version 1.0 2017-10-09
 * @see
 * @since JDK7.0
 * @update
 *
 */
public class PushPosConsumerThreadPoolTask implements Runnable,Serializable{

	private static final long serialVersionUID = 7399427033513980362L;
	
	// 保存任务所需要的数据
	private String threadPoolTaskData;
	
	public PushPosConsumerThreadPoolTask(String msg){
		this.threadPoolTaskData = msg;
	}

	@Override
	public void run() {
		try {
			new PushProcessMessage().doExecute(threadPoolTaskData);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

}

