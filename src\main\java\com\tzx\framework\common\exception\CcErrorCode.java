package com.tzx.framework.common.exception;

public enum CcErrorCode implements ErrorCode
{
	//通用信息判断
	/**
	 * 日期格式错误
	 */
	DATE_FORMAT_ERROR(8001),
	//订单基本信息判断
	/**
	 *  预订单类型不能为空
	 */
	ORDER_TYPE_EMPTY_ERROE(8031),
	/**
	 *  订餐渠道不能为空
	 */
	CHANEL_EMPTY_ERROE(8032),
	/**
	 *  商圈不能为空
	 */
	DISTRICT_ID_EMPTY_ERROE(8033),
	/**
	 *  收货地址不能为空
	 */
	ADDRESS_EMPTY_ERROE(8034),
	/**
	 *  订单金额不能为空
	 */
	TOTAL_MONEY_EMPTY_ERROE(6035),
	
	/**
	 *  实际支付金额不能为空
	 */
	ACTUAL_PAY_EMPTY_ERROE(8036),
	
	/**
	 *  送达时间不能为空
	 */
	SEND_TIME_EMPTY_ERROE(8037),
	
	/**
	 *  收货人不能为空
	 */
	CONSIGNER_EMPTY_ERROE(8038),
	
	/**
	 *  收货电话不能为空
	 */
	CONSIGNER_PHONE_EMPTY_ERROE(8039),
	//点菜信息判断
	/**
	 *  菜品信息不能为空
	 */
	ORDER_ITEM_EMPTY_ERROE(8040),
	/**
	 *  菜品id不能为空
	 */
	ITEM_ID_EMPTY_ERROE(8041),
	/**
	 *  菜品规格id不能为空
	 */
	UNIT_ID_EMPTY_ERROE(8042),
	/**
	 *  菜品数量不能为空
	 */
	NUMBER_EMPTY_ERROE(8043),
	/**
	 *  菜品价格不能为空
	 */
	PRICE_EMPTY_ERROE(8044),
	//口味做法判断
	/**
	 *  口味、做法id不能为空
	 */
	TASTE_METHOD_ID_EMPTY_ERROE(8045),
	/**
	 *  口味、做法备注不能为空
	 */
	ITEM_REMARK_EMPTY_ERROE(8046),
	
	/**
	 *  口味、做法类型不能为空
	 */
	TYPE_EMPTY_ERROE(8047),
	/**
	 *  省不能为空
	 */
	PROVINCE_EMPTY_ERROE(8048),
	/**
	 *  市不能为空
	 */
	CITY_EMPTY_ERROE(8049),
	/**
	 *  区不能为空
	 */
	AREA_EMPTY_ERROE(8050),
	
	/**
	 *  微信唯一标识不能为空
	 */
	OPEN_ID_EMPTY_ERROE(8051),
	//套餐信息判断
	TABLE_CODE(8052),

	/**
	 * 订单已存在
	 */
	ORDER_ALREADY_EXISTS_ERROR(8053),
	;
    
	private final int	number;
	
	private CcErrorCode(int number)
	{
		this.number = number;
	}

	public int getNumber()
	{
		return number;
	}
	@Override
	public String getMessage()
	{
		// TODO Auto-generated method stub
		return null;
	}

}
