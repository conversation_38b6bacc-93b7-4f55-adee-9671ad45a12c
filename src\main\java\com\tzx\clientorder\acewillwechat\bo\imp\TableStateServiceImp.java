package com.tzx.clientorder.acewillwechat.bo.imp;


import com.tzx.base.common.util.PosPropertyUtil;
import com.tzx.base.common.util.StringUtil;
import com.tzx.framework.common.util.HttpUtil;
import com.tzx.pos.base.constant.WLifeConstant;
import com.tzx.clientorder.acewillwechat.bo.HoldBillService;
import com.tzx.clientorder.acewillwechat.bo.TableStateService;
import com.tzx.clientorder.acewillwechat.bo.WshPosEntranceService;
import com.tzx.clientorder.acewillwechat.po.springjdbc.dao.OrderDao;
import com.tzx.clientorder.acewillwechat.po.springjdbc.dao.TableStateDao;
import net.sf.json.JSONObject;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by qin-gui on 2018/3/9.
 */
@Service(TableStateService.NAME)
public class TableStateServiceImp implements TableStateService {

    private static final Logger logger = Logger.getLogger(TableStateServiceImp.class);

    @Resource(name = WshPosEntranceService.NAME)
    private WshPosEntranceService wshService;
    
    @Resource(name = TableStateDao.NAME)
    private TableStateDao tableStateDao;
    @Resource(name = OrderDao.NAME)
    private OrderDao orderDao;
    @Resource(name = HoldBillService.NAME)
    private HoldBillService holdBillService;

    @Override
    public JSONObject process(String tenancyId, Integer storeId,JSONObject jsonObject) {
        String tableCode = jsonObject.optString("table_sno");
        String identify = jsonObject.optString("identify");
        if (!StringUtil.hasText(tenancyId))
            return null;

        JSONObject result = null;
        try {
            //logger.info("查询桌态，在锁单表保存微生活的openid");
            holdBillService.saveOpenId(tenancyId, storeId, tableCode, jsonObject.optString("openid"));
            //logger.info("微生活调用查询桌态");
            result = tableStateDao.getTableState(tenancyId, storeId, tableCode);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (null == result){
            result = new JSONObject();
            //查不到桌位
            result.put("status", "0");
        }else if (result.optBoolean("bill_flag")){
            //无订单
            result.put("status", "0");
            result.remove("oid");
        }else {
            result.put("status", "1");
        }
        result.put("type","order");
        result.put("pending", "0"); //待审核，默认为0。本期不加审核功能

        //修改pos_bill的order_num
        if (StringUtil.hasText(identify) && StringUtil.hasText(tableCode)){
            try {
                //如果该桌台order_num没有，则执行update
                if (!StringUtil.hasText(result.optString("order_num"))){
                    tableStateDao.updatePosBill(tenancyId, result.optString("oid"), identify);
                }else {
                    //否则不修改订单的微生活平台订单号
                }

            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        logger.info("查询桌态返回给微生活的信息：" + result.toString());
        result.remove("bill_flag");
        return result;
    }

    @Override
    public void closeTable(String tenancyId, int storeId, String tableCode) throws Exception {
    	if (!wshService.isWlifeOrderH5(tenancyId, storeId))
    	{
    		return;
    	}
        JSONObject billObject = orderDao.getBillOrder(tenancyId, tableCode, storeId);
        if (null != billObject){
            if (!StringUtil.hasText(billObject.optString("out_order_id"))){
                //logger.info("非微生活订单,不需要通知微生活清台");
             } else{
                //logger.info("开始通知微生活清台");
                String shopId = "";
                List<JSONObject> paramList = orderDao.getParam(tenancyId);
                if (null != paramList && paramList.size() > 0){
                    for (int i=0; i < paramList.size(); i++){
                        String code = paramList.get(i).optString("para_code");
                        if (WLifeConstant.SHOP_ID.equals(code)){
                            shopId = paramList.get(i).optString("para_value");
                        }
                    }
                }
                Map<String, String> paramMap = new HashMap<String, String>();
                //微生活的门店id
                paramMap.put("shop_id", shopId);
                //门店的订单号
                paramMap.put("oid", billObject.optString("oid"));
                //平台订单号
                paramMap.put("out_order_id", billObject.optString("out_order_id"));
                //点餐模式, 2:先付 3:后付
                paramMap.put("ordermode", WLifeConstant.WLIFE_ORDER_MODE);
                String url = PosPropertyUtil.getMsg("acewill.request.url");
                url += WLifeConstant.COMPLETE_ORDER_URL;
                String msg = HttpUtil.sendPostRequest(url, paramMap);
                logger.info("微生活清台返回信息：" + msg);
            }
        }
    }
}
