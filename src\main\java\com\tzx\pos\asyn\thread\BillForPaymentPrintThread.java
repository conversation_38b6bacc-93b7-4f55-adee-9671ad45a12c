/** 
 * @(#)CodeFormat.java    1.0   2018-08-21
 * Copyright 北京普照天星科技有限公司. All rights reserved. 
 * PROPRIETARY/CONFIDENTIAL. Use is subject to license terms. 
 */
package com.tzx.pos.asyn.thread;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import javax.servlet.ServletException;
import org.apache.log4j.Logger;
import com.tzx.pos.bo.PosPrintNewService;
import com.tzx.pos.bo.PosPrintService;
import net.sf.json.JSONObject;

/**
 * 下单支付完成后的打印。
 * 
 * <AUTHOR> email:<EMAIL>
 * @version 1.0 2018-08-21
 * @see
 * @since JDK7.0
 * @update
 */
public class BillForPaymentPrintThread implements Runnable{
	
	private Logger logger = Logger.getLogger(BillForPaymentPrintThread.class);
	
	private JSONObject printJson;
	private PosPrintNewService posPrintNewService;
	private PosPrintService posPrintService;
	
	public static ThreadPoolExecutor billForPaymentThreadPool = new ThreadPoolExecutor(5,10,3,TimeUnit.SECONDS, new ArrayBlockingQueue<Runnable>(10000),new ThreadPoolExecutor.DiscardOldestPolicy());
	
	public BillForPaymentPrintThread(JSONObject printJson,PosPrintNewService posPrintNewService,PosPrintService posPrintService){
		this.printJson = printJson;
		this.posPrintNewService = posPrintNewService;
		this.posPrintService = posPrintService;
	}

	/**
	 * @Description 下单支付后的打印操作。
	 * @param    
	 * @return 
	 * @exception ServletException IOException
	 * <AUTHOR> email:<EMAIL>
	 * @version 1.0  2018-08-21
	 * @see
	 */
	@Override
	public void run() {
		try {
			posPrintService.printPosBillForPayment(printJson, posPrintNewService);
		} catch (Exception e) {
			logger.error(e.getMessage());
		}
	}

}
