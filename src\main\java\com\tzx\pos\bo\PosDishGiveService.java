package com.tzx.pos.bo;

import com.tzx.framework.common.entity.Data;

/** 菜品奉送
 * <AUTHOR> 2017-09-22
 *
 */
public interface PosDishGiveService extends PosBaseService
{
	String	NAME	= "com.tzx.pos.bo.imp.PosDishGiveServiceImp";
	
	/**
	 * 奉送校验
	 * 
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void checkGiveItem(Data param, Data result) throws Exception;
	
	/**
	 * 奉送
	 * 
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void giveItem(Data param, Data result) throws Exception;

	/**
	 * 取消奉送
	 * 
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void cancGiveItem(Data param, Data result) throws Exception;
	
    /**
     * 奉送权限验证
     * @param param
     * @param result
     * @throws Exception
     */
    public void giveAuth(Data param, Data result) throws Exception;
}
