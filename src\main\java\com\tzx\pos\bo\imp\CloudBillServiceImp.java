package com.tzx.pos.bo.imp;

import java.util.*;

import javax.annotation.Resource;

import com.tzx.base.common.util.StringUtil;
import com.tzx.clientorder.wxorder.base.util.WxOrderUtil;
import com.tzx.clientorder.wxorder.po.springjdbc.dao.WxInsertOrderDao;
import com.tzx.framework.common.util.*;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;

import com.tzx.base.common.util.PosPropertyUtil;
import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.member.crm.po.springjdbc.dao.CustomerDao;
import com.tzx.pos.asyn.pool.ThreadPool;
import com.tzx.pos.asyn.thread.BillAndMemberPrintThread;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.base.util.ReqDataUtil;
import com.tzx.pos.bo.CloudBillService;
import com.tzx.pos.bo.PosPrintNewService;
import com.tzx.pos.bo.PosService;
import com.tzx.pos.po.springjdbc.dao.CloudBillDao;
import com.tzx.pos.po.springjdbc.dao.PosPaymentDao;

/**
 * Created by MrChen on 2017-12-27.
 */
@Service(CloudBillService.NAME)
public class CloudBillServiceImp extends PosBaseServiceImp implements CloudBillService {
    private static final Logger logger = Logger.getLogger(CloudBillServiceImp.class);

    private static final String		REQUEST_CLOUD_PATH			= "/posRest/post";

    @Resource(name = CloudBillDao.NAME)
    private CloudBillDao cloudBillDao;

    @Autowired
    private PosPaymentDao paymentDao;

    @Resource(name = PosService.NAME)
    private PosService posService;

    @Resource(name = PosPrintNewService.NAME)
    private PosPrintNewService posPrintNewService;

    @Autowired
    private CustomerDao customerDao;
    @Resource(name = WxInsertOrderDao.NAME)
    private WxInsertOrderDao wxInsertOrderDao;

    /**
     * 获取云POS地址
     * @return
     * @throws Exception
     */
    public String getRequestPath() throws Exception
    {
        String cloudUrl = PosPropertyUtil.getMsg("cloud.url");
        cloudUrl = cloudUrl + REQUEST_CLOUD_PATH;
        logger.info("请求云POS地址："+cloudUrl);
        return cloudUrl;
    }

    @Override
    public String findCloudBill(String param) throws Exception {
        List<JSONObject> jsonObjectList = new ArrayList<JSONObject>();
        JSONObject para = JSONObject.fromObject(param);
        String tenancyId = para.optString("tenancy_id");
        int storeId = para.optInt("store_id");// 机构ID
        String billNum = para.optString("bill_num");
        String tableCode = para.optString("table_code");
        String customerId = para.optString("customer_id");
        String openId = para.optString("open_id");
        String mobile = para.optString("mobile");
        String chanel = para.optString("chanel");
        String uuid = para.optString("uuid");
        //微生活会员类型
        String customerType = customerDao.getCustomerType(tenancyId, storeId);
        JSONObject returnJSONObject = new JSONObject();
        JSONObject jsonObject = cloudBillDao.findCloudBill(tenancyId, storeId, tableCode, billNum);
        if(!jsonObject.containsKey("code")) {
            jsonObject.put("code", SysDictionary.BILL_CLOUD_SUCCESS_CODE);
            //向pos_bill_sub表插入记录
//            cloudBillDao.insertPosBillSub(tenancyId, storeId, jsonObject.optString("bill_num",""), jsonObject.optString("report_date"));
            logger.info("================拉单开始插入pos_bill_sub表=================");
            cloudBillDao.insertPosBillSub(tenancyId, storeId, jsonObject);
            List<JSONObject> posBillSubList = cloudBillDao.findPosBillSub(tenancyId, storeId, jsonObject.optString("bill_num",""));
            if(null != posBillSubList && posBillSubList.size() > 0) {
                jsonObject.put("pos_bill_sub", posBillSubList);
            }else {
                JSONArray serList = new JSONArray();
                jsonObject.put("pos_bill_sub", serList);
            }
        }else {
            returnJSONObject.put("code", jsonObject.optInt("code",-1));
        }
        returnJSONObject.put("tenancy_id", tenancyId);
        returnJSONObject.put("store_id", storeId);
        returnJSONObject.put("type", Type.SEND_BILL_CLOUD);
        returnJSONObject.put("oper", Oper.find);
        jsonObject.put("customer_id", customerId);
        jsonObject.put("open_id", openId);
        jsonObject.put("mobile", mobile);
        jsonObject.put("chanel", chanel);
        jsonObject.put("uuid", uuid);
        jsonObject.put("customerType", customerType);
        Integer lock_state=0;
        try{
            //解锁操作star
            String nowTimeStr=DateUtil.getNowDateYYDDMMHHMMSS();
            Date nowTime=DateUtil.parseDateAll(nowTimeStr);
            jsonObject.put("nowTime",nowTimeStr);
            if(Tools.isNullOrEmpty(billNum)){//第一次拉单
                billNum=this.cloudBillDao.findBillNum(tenancyId,storeId,tableCode);
            }
            logger.info("bill_num="+billNum);
            if(!Tools.isNullOrEmpty(billNum)){
                List<JSONObject> lockData=this.cloudBillDao.findLockData(billNum,tenancyId,storeId);
                logger.info("第一次拉单");
                //判断锁单表是否有数据，如果有数据说明是二次锁单或解锁操作否则是首次锁单
                if(lockData.size()>0) {
                    logger.info("第二次拉单，锁单数据为"+lockData.get(0).toString());
                    String open_id = lockData.get(0).optString("open_id", "");
                    Integer customer_id = lockData.get(0).optInt("customer_id", 0);
                    Integer lock_statetmp = lockData.get(0).optInt("lock_state", 0);
                    Integer customerIdtemp=0;
                    if(!customerId.isEmpty()){
                        customerIdtemp=Integer.parseInt(customerId);
                    }
                    logger.info("openId="+openId+">>>open_id="+open_id+"------customer_id="+customer_id+"----customerIdtemp="+customerIdtemp+".....lock_statetmp="+lock_statetmp);
                    if (openId.equals(open_id) && customer_id-customerIdtemp == 0 || lock_statetmp!=1) {
                        int res = this.cloudBillDao.forcedUnlocking(billNum, tenancyId, storeId, nowTime, chanel, 0, "", 0);
                        logger.info("解锁表操作" + res);
                        //门店锁单操作
                        if(res>0){
                            Data MDlock=this.lockMD(billNum,chanel,tenancyId,storeId,open_id,customer_id,nowTimeStr,"1");
                            if(MDlock.getCode()!=SysDictionary.BILL_CLOUD_SUCCESS_CODE){
                                jsonObject.put("code",SysDictionary.BILL_CLOUD_LOCK_CODE);
                                logger.info("门店解锁挂了");
                            }else{
                                logger.info("门店解锁成功");
                            }
                        }
                    }
                }else{
                    Data lockTmp=Data.get(tenancyId,storeId,0);
                    List lockLsttmp=new ArrayList();
                    Map mapTmp=new HashMap();
                    mapTmp.put("table_code",tableCode);
                    lockLsttmp.add(mapTmp);
                    lockTmp.setData(lockLsttmp);
                    List <JSONObject>list=posService.findLockTable(lockTmp);
                    if(!list.isEmpty()&&list.size()>0){
                        JSONObject jsonObject1=list.get(0);
                        if(!jsonObject1.isEmpty()){
                            String lock_pos_num=jsonObject1.optString("lock_pos_num","");
                            if(!lock_pos_num.isEmpty()){
                                lock_state=1;
                            }
                        }
                    }
                    Data paramtmp=new Data();
                    Data resulttmp=new Data();
                    paramtmp.setTenancy_id(tenancyId);
                    paramtmp.setStore_id(storeId);
                    Map map=new HashMap();
                    map.put("bill_num",billNum);

                    map.put("nowTime",nowTimeStr);
                    map.put("lock_state",lock_state);
                    if(lock_state==1){
                        map.put("customer_id",0);
                        map.put("open_id","MD01");
                        map.put("chanel","MD01");
                    }else{
                        map.put("customer_id",customerId);
                        map.put("open_id",openId);
                        map.put("chanel",chanel);
                    }


                    List listtmp=new ArrayList();
                    listtmp.add(map);
                    paramtmp.setData(listtmp);
                    Data resDataLock=this.locking(paramtmp,resulttmp);
                    if(resDataLock.getCode()!=SysDictionary.BILL_CLOUD_SUCCESS_CODE){
                        jsonObject.put("code",SysDictionary.BILL_CLOUD_LOCK_CODE);
                        logger.info("门店初始化锁单表挂了");
                    }else{
                        logger.info("门店初始化锁单表成功");
                    }
                }
            }
            jsonObject.put("is_lock",lock_state);
            //解锁操作end
        }catch (Exception e){
            logger.error("解锁操作挂了，bill_num="+billNum+",不影响流程，你们继续走吧",e);
        }
        jsonObjectList.add(jsonObject);
        returnJSONObject.put("data", jsonObjectList);
        return returnJSONObject.toString();
    }

    @Override
    public String validateCloudBill(String param) throws Exception {
        JSONObject para = JSONObject.fromObject(param);
        String tenancyId = para.optString("tenancy_id");
        int storeId = para.optInt("store_id");// 机构ID
        String uuid = para.optString("uuid");
        //微生活会员类型
        String customerType = customerDao.getCustomerType(tenancyId, storeId);
        long t = System.currentTimeMillis();
        JSONObject dataJSONObject = para.getJSONArray("data").getJSONObject(0);
        List<JSONObject> jsonObjectList = new ArrayList<JSONObject>();
        int code = 0;
        String billNum = dataJSONObject.optString("bill_num");
        int billItemCountCloud = dataJSONObject.optInt("bill_item_count");
        int billItemCountGiveCloud = dataJSONObject.optInt("bill_item_count_give");
        double subtotalCloud = dataJSONObject.optDouble("subtotal");
        double paymentAmountCloud = dataJSONObject.optDouble("payment_amount");
        double discountAmountCloud = dataJSONObject.optDouble("discount_amount");
        double shopPaymentAmountCloud = dataJSONObject.optDouble("shop_payment_amount");
        int billItemCount = 0;
        int billItemCountGive = 0;
        double subtotal = 0d;
        double paymentAmount = 0d;
        double discountAmount = 0d;
        billItemCount = cloudBillDao.selectBillItemCountByBillNum(tenancyId, storeId, billNum);
        if(billItemCount == 0) {
            logger.info(String.valueOf(t)+"<账单"+ billNum + "POS门店菜品明细数量=>billItemCount=" + billItemCount);
            code = SysDictionary.BILL_CLOUD_FAILURE_CODE;
        }else if(billItemCountCloud == 0){
            logger.info(String.valueOf(t)+"<账单"+ billNum + "云POS菜品明细数量=>billItemCountCloud=" + billItemCountCloud);
            code = SysDictionary.BILL_CLOUD_FAILURE_CODE;
        }else {
            //菜品明细数量不相等
            if(billItemCount != billItemCountCloud) {
                logger.info(String.valueOf(t)+"<账单"+ billNum + "菜品明细数量不相等,POS门店菜品明细数量==>billItemCount=" + billItemCount);
                logger.info(String.valueOf(t)+"<账单"+ billNum + "菜品明细数量不相等,云POS菜品明细数量==>billItemCountCloud=" + billItemCountCloud);
                code = SysDictionary.BILL_CLOUD_CHANGE_CODE;
            }
        }
        billItemCountGive = cloudBillDao.selectBillItemCountByBillNum4Give(tenancyId, storeId, billNum);
        //菜品明细奉送数量不相等
        if(billItemCountGive != billItemCountGiveCloud) {
            logger.info(String.valueOf(t)+"<账单"+ billNum + "菜品明细奉送数量不相等,POS门店菜品明细奉送数量==>billItemCountGive=" + billItemCountGive);
            logger.info(String.valueOf(t)+"<账单"+ billNum + "菜品明细奉送数量不相等,云POS菜品明细奉送数量==>billItemCountGiveCloud=" + billItemCountGiveCloud);
            code = SysDictionary.BILL_CLOUD_CHANGE_CODE;
        }
        //账单金额信息
        JSONObject billJsonObject = cloudBillDao.queryBillByBillNum(tenancyId, storeId, billNum);
        if(null == billJsonObject) {
            code = SysDictionary.BILL_CLOUD_FAILURE_CODE;
        }else {
            subtotal = billJsonObject.optDouble("subtotal");
            paymentAmount = billJsonObject.optDouble("paymentAmount");
            discountAmount = billJsonObject.optDouble("discountAmount");
            double difference = DoubleHelper.sub(paymentAmount, shopPaymentAmountCloud, 2);
            if(difference != 0) {
                logger.info(String.valueOf(t)+"<账单"+ billNum + "金额不相等,POS门店账单前一次支付金额==>shopPaymentAmountCloud=" + shopPaymentAmountCloud);
                logger.info(String.valueOf(t)+"<账单"+ billNum + "金额不相等,云POS账单当前支付金额==>paymentAmount=" + paymentAmount);
                code = SysDictionary.BILL_CLOUD_CHANGE_CODE;
            }
            //账单金额不相等
//            if(subtotal != subtotalCloud) {
//                logger.info(String.valueOf(t)+"<账单"+ billNum + "金额不相等,POS门店账单金额==>subtotal=" + subtotal);
//                logger.info(String.valueOf(t)+"<账单"+ billNum + "金额不相等,云POS账单金额==>subtotalCloud=" + subtotalCloud);
//                code = SysDictionary.BILL_CLOUD_CHANGE_CODE;
//            }
//            else if(paymentAmount!= paymentAmountCloud) {//账单应付金额
//                logger.info(String.valueOf(t)+"<账单"+ billNum + "应付金额不相等,POS门店账单应付金额==>paymentAmount=" + paymentAmount);
//                logger.info(String.valueOf(t)+"<账单"+ billNum + "应付金额不相等,云POS账单应付金额==>paymentAmountCloud=" + paymentAmountCloud);
//                code = SysDictionary.BILL_CLOUD_CHANGE_CODE;
//            }
//            else if(discountAmount!= discountAmountCloud) {//账单优惠金额
//                logger.info(String.valueOf(t)+"<账单"+ billNum + "优惠金额不相等,POS门店账单优惠金额==>discountAmount=" + discountAmount);
//                logger.info(String.valueOf(t)+"<账单"+ billNum + "优惠金额不相等,云POS账单优惠金额==>discountAmountCloud=" + discountAmountCloud);
//                code = SysDictionary.BILL_CLOUD_CHANGE_CODE;
//            }
        }
        JSONObject returnJSONObject = new JSONObject();
        JSONObject jsonObject = new JSONObject();
        if(code == SysDictionary.BILL_CLOUD_SUCCESS_CODE) {
            jsonObject = cloudBillDao.findCloudBill(tenancyId, storeId, "", billNum);
            jsonObject.put("code", SysDictionary.BILL_CLOUD_SUCCESS_CODE);
            logger.info("================预支付开始插入pos_bill_sub表=================");
            cloudBillDao.insertPosBillSub(tenancyId, storeId, jsonObject);
            List<JSONObject> posBillSubList = cloudBillDao.findPosBillSub(tenancyId, storeId, jsonObject.optString("bill_num",""));
            if(null != posBillSubList && posBillSubList.size() > 0) {
                jsonObject.put("pos_bill_sub", posBillSubList);
            }else {
                JSONArray serList = new JSONArray();
                jsonObject.put("pos_bill_sub", serList);
            }
            try{
                //锁单操作star
                Integer customer_id = dataJSONObject.optInt("customer_id",0);
                String open_id = dataJSONObject.optString("open_id","");
                String chanel = para.optString("chanel");
                Data paramtmp=new Data();
                Data resulttmp=new Data();
                paramtmp.setTenancy_id(tenancyId);
                paramtmp.setStore_id(storeId);
                Map map=new HashMap();
                String nowTime=DateUtil.getNowDateYYDDMMHHMMSS();
                map.put("bill_num",billNum);
                map.put("customer_id",customer_id);
                map.put("nowTime",nowTime);
                map.put("open_id",open_id);
                map.put("chanel",chanel);
                List listtmp=new ArrayList();
                listtmp.add(map);
                paramtmp.setData(listtmp);
                //lock表锁单操作
                Data resData=this.locking(paramtmp,resulttmp);
                if(resData.getCode()!=0){
                    jsonObject.put("code",resData.getCode());
                }else{
                    jsonObject.put("is_lock",1);
                    jsonObject.put("nowTime",nowTime);
                }
                jsonObject.put("bill_num",billNum);
                //门店锁单操作
                if(resData.getCode()==SysDictionary.BILL_CLOUD_SUCCESS_CODE){
                    Data lockData=this.lockMD(billNum,chanel,tenancyId,storeId,open_id,customer_id,nowTime,"0");
                    if(lockData.getCode()!=SysDictionary.BILL_CLOUD_SUCCESS_CODE){
                        jsonObject.put("code",SysDictionary.BILL_CLOUD_LOCK_CODE);
                    }
                }

                //锁单操作end
            }catch (Exception e){
                logger.error("锁单操作挂了，bill_num="+billNum+",不影响流程，你们继续走吧",e);
                jsonObject.put("code", SysDictionary.BILL_CLOUD_LOCK_CODE);
            }
        }else {
            jsonObject = cloudBillDao.findCloudBill(tenancyId, storeId, "", billNum);
            jsonObject.put("code", code);
        }
        jsonObject.put("customer_id", dataJSONObject.optString("customer_id"));
        jsonObject.put("mobile", dataJSONObject.optString("mobile"));
        jsonObject.put("open_id", dataJSONObject.optString("open_id"));
        jsonObject.put("isUseCredit", dataJSONObject.optString("isUseCredit"));
        logger.info("=============是否可用积分的值isUseCredit=" + dataJSONObject.optString("isUseCredit"));
        jsonObject.put("chanel", dataJSONObject.optString("chanel"));
        jsonObject.put("item", dataJSONObject.optJSONArray("item"));
        jsonObject.put("uuid", uuid);
        jsonObject.put("customerType", customerType);
        jsonObjectList.add(jsonObject);
        returnJSONObject.put("data", jsonObjectList);
        returnJSONObject.put("tenancy_id", tenancyId);
        returnJSONObject.put("store_id", storeId);
        returnJSONObject.put("type", storeId);
        returnJSONObject.put("type", Type.SEND_BILL_CLOUD);
        returnJSONObject.put("oper", Oper.find);
        return returnJSONObject.toString();
    }

    /**
     * 门店锁单
     * @param billnum
     */
    public Data lockMD(String billnum,String chanel,String tenancyId,Integer storeId,String openId,Integer customer_id,String nowTime,String state){
        Data result=Data.get(tenancyId,storeId,0);
        try{
            List<JSONObject> list=this.cloudBillDao.findBillInfo(billnum,tenancyId,storeId);
            if(list!=null&& list.size()>0){
                JSONObject jsonObject=list.get(0);
                if(!jsonObject.isEmpty()){
                    String report_date=jsonObject.optString("report_date","");
//                    String open_pos_num=jsonObject.optString("open_pos_num","");
//                    String pos_num=jsonObject.optString("pos_num","");
                    String table_code=jsonObject.optString("table_code","");
                    Integer shift_id=jsonObject.optInt("shift_id",0);
//                    Integer open_opt=jsonObject.optInt("open_opt",0);
//                    Integer cashier_num=jsonObject.optInt("cashier_num",open_opt);
                    Data data=Data.get(tenancyId,storeId,0);
                    List tablesLst=new ArrayList();
//                    pos_num=pos_num.replace("null","");
                    Map tabes=new HashMap();
                    tabes.put("table_code",table_code);
                    tablesLst.add(tabes);
                    List dataLst=new ArrayList();
                    Map dataMap=new HashMap();
                    dataMap.put("report_date",report_date);
//                    dataMap.put("pos_num",pos_num==null||pos_num.isEmpty()?open_pos_num:pos_num);
                    dataMap.put("pos_num","WX01");
                    dataMap.put("nowTime",nowTime);
                    dataMap.put("shift_id",shift_id);
                    dataMap.put("opt_num","WX01");
                    dataMap.put("open_id",openId);
                    dataMap.put("customer_id",customer_id);
                    dataMap.put("chanel",chanel);
                    dataMap.put("tables",tablesLst);
                    dataMap.put("mode",state);
                    dataLst.add(dataMap);
                    data.setData(dataLst);
                    data.setSource(chanel);
                    data.setOper(Oper.check);
                    data.setSecret("0");
                    data.setType(Type.CLOCK_TABLE);
                    posService.lockOrUnlockTable(data, result);
                }
            }
        }catch (Exception e){
            logger.error("门店锁单挂了",e);
            result.setMsg("门店锁单挂了");
        }
        return result;
    }
    @Override
    public Data locking(Data param, Data result) throws Exception {
        try{

            String tenantId = param.getTenancy_id();
            Integer store_id = param.getStore_id();
            Map<String, Object> map = ReqDataUtil.getDataMap(param);
            String bill_num = ParamUtil.getStringValue(map, "bill_num", false, null);
            Integer lock_state = ParamUtil.getIntegerValue(map, "lock_state", false, null);
            String open_id = ParamUtil.getStringValue(map, "open_id", false, null);
            Integer customer_id = ParamUtil.getIntegerValue(map, "customer_id", false, null);
            String nowTimeStr=ParamUtil.getStringValue(map,"nowTime",false,null);
            Date nowtime=DateUtil.parseDateAll(nowTimeStr);
            String chanel = ParamUtil.getStringValue(map, "chanel", false, null);
            Integer   bill_state = ParamUtil.getIntegerValue(map, "bill_state", false, null);//账单结账状态0表示未结账1表示已结账
            if(Tools.isNullOrEmpty(bill_num)){
                result.setCode(SysDictionary.BILL_CLOUD_BILL_NULL_CODE);
                result.setSuccess(false);
                result.setMsg("订单号不能为空");
                return result;
            }

            if(Tools.isNullOrEmpty(open_id)&&Tools.isNullOrEmpty(customer_id)){
                result.setCode(SysDictionary.BILL_CLOUD_CUSTOMER_NULL_CODE);
                result.setSuccess(false);
                result.setMsg("会员信息不能为空");
                return result;
            }
            if(Tools.isNullOrEmpty(customer_id)){
                customer_id=0;
            }
            if(Tools.isNullOrEmpty(open_id)){
                open_id="";
            }
            if(Tools.isNullOrEmpty(lock_state)){
                lock_state=1;//默认为锁定桌台
            }
            if(Tools.isNullOrEmpty(bill_state)){
                bill_state=0;
            }
            List<JSONObject> lockData=this.cloudBillDao.findLockData(bill_num,tenantId,store_id);
            //判断锁单表是否有数据，如果有数据说明是二次锁单或解锁操作否则是首次锁单
            if(lockData.size()>0){
                String  openId=lockData.get(0).optString("open_id","");
                Integer customerId=lockData.get(0).optInt("customer_id",0);
                Integer lock_statetmp=lockData.get(0).optInt("lock_state",0);

                if(open_id.equals(openId)&&customer_id-customerId==0||lock_statetmp!=1){
                    int rest= this.cloudBillDao.forcedUnlocking(bill_num,tenantId,store_id,nowtime,chanel,1,open_id,customer_id);
                    logger.info(" int rest"+rest);
                    result.setCode(Constant.CODE_SUCCESS);
                    result.setSuccess(true);
                    result.setMsg("锁单成功");
                }else{
                    result.setCode(SysDictionary.BILL_CLOUD_LOCK_CODE);
                    result.setSuccess(false);
                    result.setMsg("账单已被锁定，请解锁后再操作");
                }
            }else{

                List datals=new ArrayList();
                datals.add(tenantId);
                datals.add(store_id);
//            datals.add(UUIDUtil.generateGUID());
                datals.add(bill_num);
                datals.add(chanel);
                datals.add(lock_state);
                datals.add(open_id);
                datals.add(customer_id);
                datals.add(nowtime);
                datals.add(bill_state);
                Object[]data=datals.toArray();
                this.cloudBillDao.insertData(data);
                result.setCode(Constant.CODE_SUCCESS);
                result.setSuccess(true);
                result.setMsg("锁单成功");
            }
        }catch (Exception e){
            logger.error("账单锁定异常",e);
            result.setCode(9);
            result.setSuccess(false);
            result.setMsg("锁单异常");
        }

        return result;
    }

    @Override
    public Data forcedUnlocking(Data param, Data result) throws Exception {
        String tenantId = param.getTenancy_id();
        Integer store_id = param.getStore_id();
        Map<String, Object> map = ReqDataUtil.getDataMap(param);
        String bill_num = ParamUtil.getStringValue(map, "bill_num", false, null);
        String chanel = ParamUtil.getStringValue(map, "chanel", false, null);
        if(Tools.isNullOrEmpty(bill_num)){
            throw new SystemException(PosErrorCode.NOT_NULL_BILL_NUM);
        }
        if(Tools.isNullOrEmpty(chanel)){
            chanel=SysDictionary.CHANEL_MD01;
        }
        this.cloudBillDao.forcedUnlocking(bill_num,tenantId,store_id,new Date(),chanel,0,"",0);
        result.setCode(Constant.CODE_SUCCESS);
        result.setSuccess(true);
        result.setMsg("强制解锁成功");
        return result;
    }

    @Override
    public Data closeBillLock(Data param, Data result) throws Exception {
        String tenantId = param.getTenancy_id();
        Integer store_id = param.getStore_id();
        Map<String, Object> map = ReqDataUtil.getDataMap(param);
        String bill_num = ParamUtil.getStringValue(map, "bill_num", false, null);
        if(Tools.isNullOrEmpty(bill_num)){
            throw new SystemException(PosErrorCode.NOT_NULL_BILL_NUM);
        }
        this.cloudBillDao.closedBillUnlocking(bill_num,tenantId,store_id);
        result.setCode(Constant.CODE_SUCCESS);
        result.setSuccess(true);
        result.setMsg("关闭锁定的账单成功");
        return result;
    }

    @Override
    public Data findBillLockState(Data param, Data result) throws Exception {
        String tenantId = param.getTenancy_id();
        Integer store_id = param.getStore_id();
        Map<String, Object> map = ReqDataUtil.getDataMap(param);
        String bill_num = ParamUtil.getStringValue(map, "bill_num", false, null);
        if(Tools.isNullOrEmpty(bill_num)){
            throw new SystemException(PosErrorCode.NOT_NULL_BILL_NUM);
        }
        List<JSONObject> dataList = new ArrayList<JSONObject>();
        dataList=this.cloudBillDao.findLockData(bill_num,tenantId,store_id);
        if(dataList.size()>0){
            result.setCode(Constant.CODE_SUCCESS);
            result.setMsg("查询锁台信息成功");
            result.setSuccess(true);
        }else{
            result.setCode(Constant.CODE_NULL_DATASET);
            result.setSuccess(false);
            result.setMsg("查询锁台信息失败");
        }
        result.setData(dataList);
        return result;
    }

    /**
     * 云端通知门店结账时，
     * 门店不结账，同时通知云端，记录异常
     * @param tenancyId
     * @param storeId
     * @param billNum
     * @throws Exception
     */
    private void noticeCloudBohCheckOutError(String tenancyId, int storeId, String billNum, double paymentAmount,
        double shopPaymentAmount, String remark, String exceptionType) {
        try
        {
            JSONObject requestJson = new JSONObject();
            requestJson.put("bill_num", billNum);
            requestJson.put("remark", remark);
            requestJson.put("payment_amount", paymentAmount);
            requestJson.put("shop_payment_amount", shopPaymentAmount);
            requestJson.put("error_type", exceptionType);

            List<JSONObject> dataList = new ArrayList<>();
            dataList.add(requestJson);

            Data requestData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
            requestData.setType(Type.TO_CLOUD_CHECKOUT_ABNORMAL);
            requestData.setOper(Oper.update);
            requestData.setData(dataList);

            String requestStr = JSONObject.fromObject(requestData).toString();
            logger.info("<请求云POS接口请求体==>" + requestStr);
            String result = HttpUtil.sendPostRequest(getRequestPath(), requestStr, 20000, 20000);
            logger.info("<请求云POS接口返回体==>" + result);
        }
        catch (Exception e)
        {
            logger.error("请求云POS接口异常", e);
        }
    }

    @Override
    public void syncCloudPaymentBill(String tenancyId, int storeId, JSONObject data) throws Exception {

        if(data == null || data.isEmpty())
        {
            return;
        }

        // 云账单信息
        JSONObject billJson = data.optJSONObject("pos_bill");
        String billNum = billJson != null ? billJson.optString("bill_num") : null;

        if(billJson == null || StringUtils.isEmpty(billNum))
        {
            logger.info("同步到门店的云端账单："+billNum+"，相关账单信息不存在！");
            return;
        }

        // 云端菜品明细数量
        int cloudItemSize = 0;
        if(data.containsKey("pos_bill_item"))
        {
            cloudItemSize = data.optJSONArray("pos_bill_item").size();
        }

        JSONObject posBill = cloudBillDao.getBillAndSubInfo(tenancyId, storeId, billNum);
        if(posBill.isEmpty())
        {
            logger.info("同步到门店的云端账单："+billNum+"不存在！");
            return;
        }

        String billId = posBill.optString("id"); // 账单主键id
        String billProperty = posBill.optString("bill_property");
        String paymentState = posBill.optString("payment_state");
        String reportDate = posBill.optString("report_date");
//        String shiftId = posBill.optString("shift_id");
        String bohTable = posBill.optString("fictitious_table"); // 门店账单桌台（转台后桌位发生变化）
        // 门店账单应支付金额
        double paymentAmount = posBill.optDouble("payment_amount", 0d);
        // 同步到云端前的门店金额
        double shopPaymentAmount = posBill.optDouble("shop_payment_amount", 0d);

        if(shopPaymentAmount != paymentAmount)
        {
            StringBuilder msg = new StringBuilder();
            msg.append("账单号：");
            msg.append(billNum);
            msg.append("，同步到云端前的门店金额：");
            msg.append(shopPaymentAmount);
            msg.append("，与门店账单实际支付金额：");
            msg.append(paymentAmount);
            msg.append("不一致！关单失败!");

            logger.info(msg.toString());

            cloudBillDao.updateBillRemark(tenancyId, storeId, billNum, msg.toString());

            this.noticeCloudBohCheckOutError(tenancyId, storeId, billNum, paymentAmount, shopPaymentAmount, msg.toString(), SysDictionary.EXCE_BILL_CHECKOUT);
            return;
        }

        int bohItemSize = cloudBillDao.getBillItemCount(tenancyId, storeId, billNum, null);

        if(cloudItemSize != bohItemSize)
        {
            StringBuilder msg = new StringBuilder();
            msg.append("账单号：");
            msg.append(billNum);
            msg.append("，云端账单菜品数量：");
            msg.append(cloudItemSize);
            msg.append("，与门店账单菜品数量：");
            msg.append(bohItemSize);
            msg.append("不相等！关单失败!");

            logger.info(msg.toString());

            cloudBillDao.updateBillRemark(tenancyId, storeId, billNum, msg.toString());
            this.noticeCloudBohCheckOutError(tenancyId, storeId, billNum, paymentAmount, shopPaymentAmount, msg.toString(), SysDictionary.EXCE_BILL_SIZE_UN);
            return;
        }

        if (SysDictionary.BILL_PROPERTY_CLOSED.equalsIgnoreCase(billProperty)) {
            logger.info("同步到门店的云端账单："+billNum+"已结账！");
            //如果秒付的是微信后付订单，通知微信端清台
            String orderNum = wxInsertOrderDao.getBillInfo(tenancyId, billNum);
            if (StringUtil.hasText(orderNum) && orderNum.equals("WX" + billNum)){
                //如果是微信后付相关账单，发送给微信通知关单清台
                logger.info("微信后付订单，通知微信关单清台");
                WxOrderUtil.closeBill(tenancyId, billNum);
            }
            return;
        }
        if (SysDictionary.PAYMENT_STATE_PAY_COMPLETE.equalsIgnoreCase(paymentState)) {
            logger.info("同步到门店的云端账单："+billNum+"已付款完成！");
            return;
        }
        if (SysDictionary.PAYMENT_STATE_PAY.equalsIgnoreCase(paymentState)) {
            logger.info("同步到门店的云端账单："+billNum+"正在付款！");
            return;
        }

        List<JSONObject> optList = this.getOptStateInfo(tenancyId, storeId, reportDate);

        String posNum = null;
        String cashierNum = null;
        Integer shiftId = null;
        if(optList != null && optList.size() > 0)
        {
            JSONObject optJson = optList.get(0);
            posNum = optJson.optString("pos_num");
            cashierNum = optJson.optString("opt_num");
            shiftId = optJson.optInt("shift_id");
        }
        else
        {
            logger.info("该笔账单："+billNum+"，收银员未签到！");
        }

        billJson.put("fictitious_table",bohTable);
        billJson.put("table_code",bohTable);
        billJson.put("id", billId);
        // 更新门店数据
		cloudBillDao.syncBillInfo(tenancyId, storeId, billNum, reportDate, posNum, cashierNum, shiftId, data);

        // 关闭锁单的账单
        cloudBillDao.closedBillUnlocking(billNum, tenancyId, storeId);

        // 关闭桌位
        this.closeTable(tenancyId, storeId, billNum, bohTable);
        //如果秒付的是微信后付订单，通知微信端清台
        String orderNum = wxInsertOrderDao.getBillInfo(tenancyId, billNum);
        if (StringUtil.hasText(orderNum) && orderNum.equals("WX" + billNum)){
            //如果是微信后付相关账单，发送给微信通知关单清台
            logger.info("微信后付订单，通知微信关单清台");
            WxOrderUtil.closeBill(tenancyId, billNum);
        }

        try
        {
            posService.updateDevicesDataState();
        }
        catch (Exception e)
        {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }

        // 推送消息到POS
        JSONObject noticeJson = new JSONObject();
        noticeJson.put("message",bohTable+"桌支付"+shopPaymentAmount+"元" );
        Comet4jUtil.comet4J(Collections.singletonList(noticeJson), Type.BILL_PAYMENT, Oper.notice);
        logger.info("秒付消息推送到POS："+noticeJson);

//        String isInvoice = "1"; // 1:打印电子发票，0：不打印电子发票

        // 打印结账单、会员消费单据
        try
        {
            JSONObject printJson = new JSONObject();
            printJson.put("tenancy_id", tenancyId);
            printJson.put("store_id", storeId);
            printJson.put("bill_num", billNum);
            printJson.put("pos_num", posNum);
            printJson.put("opt_num", cashierNum);
            printJson.put("report_date", reportDate);
            printJson.put("shift_id", shiftId);
            printJson.put("source", SysDictionary.SOURCE_WIN_POS);

//            posBill.put("pos_num", posNum);
//            posBill.put("cashier_num", cashierNum);
//            this.generateInvoice(tenancyId, storeId, isInvoice, posBill, printJson);

            BillAndMemberPrintThread printThread = new BillAndMemberPrintThread(printJson);
            ThreadPool.getPrintThreadPool().execute(printThread);
        }
        catch (Exception e)
        {
            e.printStackTrace();
            logger.error("云端账单同步到门店时打印异常：", e);
        }
    }

    private void generateInvoice(String tenancyId, int storeId, String isInvoice, JSONObject billJson, JSONObject printJson) throws Exception
    {
        if(!"1".equals(isInvoice))
        {
            return;
        }
        // 创建新的电子发票二维码
        logger.info("电子发票二维码生成开始!");
        // 准备生成电子发票的数据
        // 获取法人信息
        JSONObject legalPerInfoJo = paymentDao.getLegalPerInfo(tenancyId, storeId, billJson);
        if(!(Tools.isNullOrEmpty(legalPerInfoJo) || Double.isNaN(legalPerInfoJo.optDouble("tax_rate"))))
        {
            // 计算开发票金额
            double invAmt = calcAmountForInvoice(tenancyId, storeId, billJson);
            if(Double.isNaN(invAmt))
            {
                invAmt = 0;
            }
            if(invAmt > 0)
            {
                JSONObject jo = new JSONObject();

                // 生成电子发票，获得电子发票的打印信息
                try
                {
                    jo.put("bill_num", billJson.optString("bill_num"));
                    jo.put("order_num", billJson.optString("order_num"));
                    jo.put("pos_num", billJson.optString("pos_num"));
                    jo.put("opt_num", billJson.optString("cashier_num"));
                    jo.put("recover_count", billJson.optString("recover_count"));
                    jo.put("source", billJson.optString("source"));
                    jo.put("report_date", billJson.optString("report_date"));
                    jo.put("tax_rate", legalPerInfoJo.optDouble("tax_rate"));
                    jo.put("invoice_num", legalPerInfoJo.optString("invoice_num"));
                    jo.put("invoice_amount", invAmt);
                    logger.info("电子发票二维码生成开始：传入参数：" + jo.toString());
                    getPrintInvoiceInfo(tenancyId, storeId, ParamUtil.getDateValueByObject(billJson, "report_date"), jo);
                }
                catch(Exception e)
                {
                    e.printStackTrace();
                    logger.error("电子发票二维码生成失败:", e);
                }
                logger.info("加密前的二维码URL:" + jo.optString("before_encode_url"));
                logger.info("生成的二维码URL: " + jo.optString("url_content"));

                printJson.put("is_invoice", isInvoice);
                printJson.put("url_content", jo.optString("url_content"));
                printJson.put("url_path", jo.optString("url_path"));
            }
            else
            {
                logger.info("电子发票金额为0!");
            }
        }
        else
        {
            logger.info("法人信息设置错误!");
        }
    }

    /**
     * 关闭桌位
     * @param tenancyId
     * @param storeId
     * @param billNum
     * @throws Exception
     */
    private void closeTable(String tenancyId, int storeId, String billNum, String fictitiousTable) throws Exception {
        StringBuilder qureyOrganSql = new StringBuilder("select format_state from organ where tenancy_id=? and id=?");
        SqlRowSet rs = paymentDao.query4SqlRowSet(qureyOrganSql.toString(), new Object[]
                { tenancyId, storeId });
        String formatState = null;
        if (rs.next())
        {
            formatState = rs.getString("format_state");
        }

        String mode = paymentDao.getSysParameter(tenancyId, storeId, "ZCDCMS");// 01,普通;02,自助;
        if (Tools.isNullOrEmpty(mode) || "0".equals(mode) || "1".equals(mode))
        {
            mode = "01";
        }

        // 正餐 更新桌位 更改桌位的状态，将桌位状态改为"空闲"
        if ("1".equals(formatState) && "01".equals(mode))
        {
            /*String fictitiousTable = null;
            StringBuilder qureyfictitiousTableSql = new StringBuilder("select fictitious_table from pos_bill where tenancy_id=? and store_id=? and bill_num =?");
            rs = paymentDao.query4SqlRowSet(qureyfictitiousTableSql.toString(), new Object[]
                    { tenancyId, storeId, billNum});
            if (rs.next())
            {
                fictitiousTable = rs.getString("fictitious_table");
            }*/
            StringBuffer updateTableState = new StringBuffer("update pos_tablestate set state = ?,lock_pos_num = ?,lock_opt_num=? where table_code = ? and store_id = ? and tenancy_id = ?");
            paymentDao.update(updateTableState.toString(), new Object[]
                    { SysDictionary.TABLE_STATE_FREE, null, null, fictitiousTable, storeId, tenancyId });
        }

        if ("2".equals(formatState))
        {
            String updateItemSql2 = new String("update pos_bill_item bi set item_taste = trim(COALESCE(b.bill_taste,'')||' '||COALESCE(bi.item_taste,'')) from pos_bill b where bi.tenancy_id=b.tenancy_id and bi.store_id=b.store_id and bi.bill_num=b.bill_num and bi.tenancy_id=? and bi.store_id=? and bi.bill_num=?");

            paymentDao.update(updateItemSql2, new Object[]{tenancyId,storeId, billNum});
        }
    }

    @Override
    public void syncPosBillToCloudBill(String tenancyId, int storeId, String billNum) throws Exception {

        JSONObject requestJson = new JSONObject();
        requestJson.put("bill_num", billNum);

        List<JSONObject> dataList = new ArrayList<>();
        dataList.add(requestJson);

        Data requestData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
        requestData.setType(Type.POS_TO_CLOUD_STATE);
        requestData.setOper(Oper.update);
        requestData.setData(dataList);

        String requestStr = JSONObject.fromObject(requestData).toString();
        logger.info("<请求云POS接口请求体==>" + requestStr);
        String result = HttpUtil.sendPostRequest(getRequestPath(), requestStr, 20000, 20000);
        logger.info("<请求云POS接口返回体==>" + result);

        if (StringUtils.isNotEmpty(result))
        {
            Data responseData = ParamUtil.stringToData(result);
//            List<?> responseList = responseData.getData();
//            if (responseList == null || responseList.isEmpty())
//            {
//                logger.info("");
//            }
//            JSONObject responseJson = JSONObject.fromObject(responseList.get(0));

            if (Constant.CODE_SUCCESS == responseData.getCode())
            {
                String updateBill = "update pos_bill_sub set sync_state = ?, query_time = ? where bill_num = ? and store_id = ? and tenancy_id = ?";

                cloudBillDao.update(updateBill, new Object[]{SysDictionary.SYNC_STATE_OK, DateUtil.currentTimestamp(), billNum, storeId, tenancyId});
                return;
            }
            else
            {
                logger.info("门店已结账单："+billNum+"通知云端，返回="+responseData.getCode());
            }
        }
        else
        {

        }

        String updateQueryTime = "update pos_bill_sub set query_time = ? where bill_num = ? and store_id = ? and tenancy_id = ?";
        cloudBillDao.update(updateQueryTime, new Object[]{DateUtil.currentTimestamp(), billNum, storeId, tenancyId});
    }

    @Override
    public List<JSONObject> queryCompleteNoSyncBill(String tenancyId, int storeId) throws Exception {
        StringBuilder billSql = new StringBuilder();
        billSql.append(" select s.bill_num, s.query_time, b.payment_time from pos_bill_sub s inner join pos_bill b on s.bill_num = b.bill_num and s.tenancy_id = b.tenancy_id and s.store_id = b.store_id ");
        billSql.append(" where b.bill_property = ? and b.payment_state = ? and b.payment_time is not null and s.handle_client = ? and s.pay_client = ? ");
        billSql.append(" and (s.sync_state is null or s.sync_state = ? or s.sync_state = ?) and s.tenancy_id = ? and s.store_id = ? ");

        List<JSONObject> objectList = cloudBillDao.queryString4Json(tenancyId, billSql.toString(),
                new Object[]{SysDictionary.BILL_PROPERTY_CLOSED, SysDictionary.PAYMENT_STATE_PAY_COMPLETE,
                        SysDictionary.CLOUD_CLIENT_HANDLE, SysDictionary.STORE_CLIENT_PAY, SysDictionary.SYNC_STATE_NO, SysDictionary.SYNC_STATE_ING, tenancyId, storeId});

        return objectList;
    }
}
