package com.tzx.framework.common.util.io;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

public class FilePathUtil {
	/**
	 * 得到跨平台的项目根路径
	* <p>Title: getFileRootPath</p>
	* <p>Description: </p>
	* <AUTHOR>
	* @param servletContext
	* @return
	 */
	public static String getFileRootPath(HttpServletRequest request){
		String homePath = "";
		ServletContext servletContext = null;
		String fileSepator   =   System.getProperties().getProperty("file.separator");
		HttpSession session = request.getSession();
		
		servletContext = session.getServletContext();
		//获取项目根路径,windows: "D:\Workspace\gxhkPlatform\WebRoot\" 或linux: "D:/Workspace/gxhkPlatform/WebRoot/"
		homePath = servletContext.getRealPath("/");
		if (homePath.endsWith(fileSepator)) {
			homePath = servletContext.getRealPath("/").substring(0,  servletContext.getRealPath("/").length()-1);
		}
		//如果是linux
		if (fileSepator.equals("/")) {
			homePath = homePath.replace('\\', '/');
		}
		return homePath;
	}
	
	/**
	 * 得到跨平台的项目根路径
	* <p>Title: getFileRootPath</p>
	* <p>Description: </p>
	* <AUTHOR>
	* @param servletContext
	* @return
	 */
	public static String getFileRootPath(ServletContext servletContext){
		String homePath = "";
		String fileSepator   =   System.getProperties().getProperty("file.separator");
		//获取项目根路径,windows: "D:\Workspace\gxhkPlatform\WebRoot\" 或linux: "D:/Workspace/gxhkPlatform/WebRoot/"
		homePath = servletContext.getRealPath("/");
		//如果是linux
		if (fileSepator.equals("/")) {
			homePath = homePath.replace('\\', '/');
		}
		return homePath;
	}
}
