package com.tzx.clientorder.wlifeprogram.service.rest;

import com.tzx.clientorder.wlifeprogram.bo.FirstPaymentService;
import com.tzx.clientorder.wlifeprogram.common.util.ExceptionPrintUtil;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.OtherSystemException;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.pos.asyn.pool.ThreadPool;
import com.tzx.pos.asyn.thread.OrderingPrintThread;
import com.tzx.pos.base.Constant;
import net.sf.json.JSONObject;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 微生活小程序调用接口（先付业务）
 */

@Controller("FirstPaymentRest")
@RequestMapping("/wlife/firstPaymentRest")
public class FirstPaymentRest {
    private static final Logger logger = Logger.getLogger(FirstPaymentRest.class);
    private static ConcurrentMap<String,Byte> concurrentMap = new ConcurrentHashMap<>();

    @Resource(name = FirstPaymentService.NAME)
    private FirstPaymentService firstPaymentService;
    /**
     * 先付预结下单
     * @param request
     * @param jsobj
     */
    @RequestMapping(value = "/orderPrecheck", method = RequestMethod.POST)
    @ResponseBody
    public JSONObject orderPrecheck(HttpServletRequest request, @RequestBody JSONObject jsobj){
        JSONObject responseJson = new JSONObject();

        logger.info("orderPrecheckRequest 请求入参===" + jsobj.toString());

        try
        {
            setBaseParams(jsobj);
//            String tenancyId = jsobj.optString("business_id");
//            int storeId = jsobj.optInt("shop_id");
//
//            JSONObject printJson = new JSONObject();
            firstPaymentService.orderPrecheck(jsobj, responseJson);
            // 数据上传
//            posDishService.upload(tenancyId, storeId+"", "", "", "", responseJson.optString("oid"));

//            // 异步执行分单打印
//            Data data = new Data();
//            data.setTenancy_id(tenancyId);
//            data.setStore_id(storeId);
//            OrderingPrintThread orderingPrintThread = new OrderingPrintThread(printJson, data);
//            ThreadPool.getPrintThreadPool().execute(orderingPrintThread);
        }
        catch (OtherSystemException se)
        {
            ExceptionPrintUtil.buildWlifeOtherSysExceptionData(logger, se, responseJson, Constant.ORDER_DISH_FAILURE);
        }
        catch (SystemException se)
        {
            ExceptionPrintUtil.buildWlifeSysExceptionData(logger, se, responseJson, Constant.ORDER_DISH_FAILURE);
        }
        catch (Exception e)
        {
            logger.error(" orderPrecheck failied ...  order_id" +jsobj.optString("out_order_id"), e);
            ExceptionPrintUtil.buildWlifeExceptionData(logger, e, responseJson, Constant.ORDER_DISH_FAILURE);
        }

        logger.info("orderPrecheckResponse 响应出参===" + responseJson.toString());
        return responseJson;
    }



    /**
     * 结账接口
     * @param request
     * @param jsobj
     * @return
     */
    @RequestMapping(value = "/firstPay", method = RequestMethod.POST)
    @ResponseBody
    public JSONObject firstPay(HttpServletRequest request, @RequestBody JSONObject jsobj){
        JSONObject responseJson = new JSONObject();

        logger.info("firstPayRequest 请求入参===" + jsobj.toString());
        String outOrderId = jsobj.optString("out_order_id");
        try
        {
            if(concurrentMap.containsKey(outOrderId)){
                logger.error("================"+outOrderId);
                throw SystemException.getInstance("账单支付中", PosErrorCode.BILL_PAYMENT_STATE_PAYING_ERROR);
            }else {
                logger.info("###########"+outOrderId);
                concurrentMap.put(outOrderId, (byte) 1);
            }
            setBaseParams(jsobj);
            firstPaymentService.firstPayClose(jsobj,responseJson);
        }
        catch (OtherSystemException se)
        {
            ExceptionPrintUtil.buildWlifeOtherSysExceptionData(logger, se, responseJson, Constant.ORDER_PAYMENT_FAILURE);
        }
        catch (SystemException se)
        {
            ExceptionPrintUtil.buildWlifeSysExceptionData(logger, se, responseJson, Constant.ORDER_PAYMENT_FAILURE);
        }
        catch (Exception e)
        {
            logger.error(" firstPay failied ...  order_id" +jsobj.optString("out_order_id"), e);
            ExceptionPrintUtil.buildWlifeExceptionData(logger, e, responseJson, Constant.ORDER_PAYMENT_FAILURE);
        }finally {
            concurrentMap.remove(outOrderId);
        }

        logger.info("firstPayResponse 响应出参===" + responseJson.toString());
        return responseJson;
    }

    private  void  setBaseParams(JSONObject params){

        if(com.tzx.framework.common.constant.Constant.systemMap.containsKey("tenent_id"))
        {
            String tenancyId = com.tzx.framework.common.constant.Constant.systemMap.get("tenent_id");
            params.put("business_id", tenancyId);
        }
        if(com.tzx.framework.common.constant.Constant.systemMap.containsKey("store_id"))
        {
            Integer storeId = Integer.valueOf(com.tzx.framework.common.constant.Constant.systemMap.get("store_id"));
            params.put("shop_id", storeId);
        }
    }

}
