package com.tzx.pos.bo.dto;

import java.io.Serializable;
import java.util.List;

public class PosLogin implements Serializable
{
	/**
	 * 
	 */
	private static final long	serialVersionUID	= 4739858224265201273L;
	
	private String	tenancy_id;
	private String	store_id;
	private String  employee_id;
	private String	name;
	private String	shift;
	private String	report_date;
	private String	business_type;
	private String	item_menu_id;
	private String  pos_num;
	private Integer opt_login_number;
	private String token;
	private String third_employee_id;
	private String organ_name;
	private List<?> sys_module;
	private String posUserName;
	
	
	public Integer getOpt_login_number()
	{
		return opt_login_number;
	}

	public void setOpt_login_number(Integer opt_login_number)
	{
		this.opt_login_number = opt_login_number;
	}

	public String getPos_num()
	{
		return pos_num;
	}

	public void setPos_num(String pos_num)
	{
		this.pos_num = pos_num;
	}

	public String getTenancy_id()
	{
		return tenancy_id;
	}

	public void setTenancy_id(String tenancy_id)
	{
		this.tenancy_id = tenancy_id;
	}

	public String getStore_id()
	{
		return store_id;
	}

	public void setStore_id(String store_id)
	{
		this.store_id = store_id;
	}

	public String getName()
	{
		return name;
	}

	public void setName(String name)
	{
		this.name = name;
	}

	public String getShift()
	{
		return shift;
	}

	public void setShift(String shift)
	{
		this.shift = shift;
	}

	public String getReport_date()
	{
		return report_date;
	}

	public void setReport_date(String report_date)
	{
		this.report_date = report_date;
	}

	public String getBusiness_type()
	{
		return business_type;
	}

	public void setBusiness_type(String business_type)
	{
		this.business_type = business_type;
	}

	public String getItem_menu_id()
	{
		return item_menu_id;
	}

	public void setItem_menu_id(String item_menu_id)
	{
		this.item_menu_id = item_menu_id;
	}

	public List<?> getSys_module()
	{
		return sys_module;
	}

	public void setSys_module(List<?> sys_module)
	{
		this.sys_module = sys_module;
	}

	public String getEmployee_id()
	{
		return employee_id;
	}

	public void setEmployee_id(String employee_id)
	{
		this.employee_id = employee_id;
	}

	public String getToken() {
		return token;
	}

	public void setToken(String token) {
		this.token = token;
	}

	public String getThird_employee_id() {
		return third_employee_id;
	}

	public void setThird_employee_id(String third_employee_id) {
		this.third_employee_id = third_employee_id;
	}

	public String getOrgan_name() {
		return organ_name;
	}

	public void setOrgan_name(String organ_name) {
		this.organ_name = organ_name;
	}

	public String getPosUserName() {
		return posUserName;
	}

	public void setPosUserName(String posUserName) {
		this.posUserName = posUserName;
	}
}
