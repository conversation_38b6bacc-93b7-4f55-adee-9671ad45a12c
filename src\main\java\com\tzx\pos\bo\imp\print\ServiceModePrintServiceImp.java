package com.tzx.pos.bo.imp.print;

import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.util.Comet4jUtil;
import com.tzx.framework.common.util.Tools;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.bo.PosPrintModeService;
import com.tzx.pos.bo.imp.PosBaseServiceImp;
import com.tzx.pos.po.springjdbc.dao.PosDao;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 业务单据————且与桌位有关的单据
 * 结账单、补打结账单、预打单、点菜清单
 *
 * PC和触屏    ————> 走站点
 * 移动端设备  ————> 走区域
 */

@Service("serviceModePrintServiceImp")
public class ServiceModePrintServiceImp extends PosBaseServiceImp implements PosPrintModeService
{
	@Resource(name = PosDao.NAME)
	private PosDao				posDao;
	private static final Logger	logger	= Logger.getLogger(ServiceModePrintServiceImp.class);
	
	@Override
	public void posPrint(String tenancyId, int storeId, JSONObject para, String printMode) throws Exception
	{
		try
		{
            // 移动端设备只走区域打印
            String source = para.optString("source");

			// 得到打印机id，是否区域，站点ID(新加)，打印机名称，IP地址，备打名称，备打地址，模板ID(hq_printer_model)
            // 查询的结果集按非区域、区域进行了排序
			List<JSONObject> printerList = posDao.getPrinter(tenancyId, storeId, printMode);
			if(printerList.size() > 0)
			{

                logger.debug(" 模板【" + printMode + "】绑定的打印机：" + printerList.toString());
        print:  for(int i = 0; i < printerList.size(); i++)
				{
                    boolean devicePrint = false; // 站点打印

                    JSONObject printerJo = printerList.get(i);
					int printerId = printerJo.optInt("printer_id");
					if(printerId == 0)
					{
						continue;
					}
					para.put("printer_id", printerId);
					
					int isArea = printerJo.optInt("is_area");

                    // 移动端设备只走区域打印
                    if(SysDictionary.SOURCE_MOVE.contains(source)||SysDictionary.SOURCE_ORDER_DEVICE.equals(source)){
                        if(isArea != 1){
                            logger.info(" 分单失败：打印单据为【"+printMode+"】，【"+source+"】只走区域打印");
                            continue;
                        }
                    }

					// 区域打印
					if(isArea == 1)
					{
						// 判断桌位是否允许区域打印
						JSONObject printerTableJo = posDao.getPrinterTable(tenancyId, storeId, para);
						String areaId = printerTableJo.optString("area_id");
						String tableCode = printerTableJo.optString("table_code");
						if(Tools.isNullOrEmpty(areaId))
						{
							logger.info(" 分单失败：打印机【" + printerId + "】没有绑定桌位:【" + tableCode + "】");
							continue;
						}
						else
						{
							printerJo.put("area_id", areaId);
						}
					}
					else
					{
						// 判断该打印机是否绑定此POS机
						JSONObject deviceJo = posDao.getDeviceFromPrinterModel(tenancyId, storeId, para, printMode);
						String devices_code = deviceJo.optString("devices_code");
						String pos_num = para.optString("pos_num");
						if(Tools.isNullOrEmpty(pos_num) || Tools.isNullOrEmpty(devices_code) || !pos_num.equals(devices_code))
						{
							logger.info(" 分单失败：打印机【" + printerId + "】没有绑定POS【" + pos_num + "】。绑定的POS为【" + devices_code + "】");
							continue;
						}
                        devicePrint = true;
					}
					
					List<JSONObject> printParamList = new ArrayList<JSONObject>();
					try
					{
						printParamList = posDao.getPrintParam(tenancyId, storeId, printMode, printerJo);
					}
					catch (Exception e)
					{
						logger.error("分单失败：打印机【" + printerId + "】获取打印参数失败 " , e);
					}
					
					if(printParamList.size() > 0)
					{
						for(JSONObject printParam : printParamList)
						{
							if(printParam.optString("param_pairs").isEmpty())
							{
								logger.info(" 分单失败：打印机【" + printerId + "】的打印模板【" + printMode + "】未设定打印参数");
								continue;
							}
							JSONObject params = new JSONObject();
							JSONObject paramPairs = JSONObject.fromObject(printParam.opt("param_pairs"));
							
							// 获取打印参数
							JSONArray rows = paramPairs.optJSONArray("rows");
							Object value = null;
							String paramName = null;
							for (Object row : rows)
							{
								JSONObject rowJo = JSONObject.fromObject(row);
								
								paramName = rowJo.optString("paramName");
								value = para.get(paramName);
								if(Tools.isNullOrEmpty(value))
								{
									value = rowJo.opt("value");
								}
								params.put(paramName, value);
							}
							
							// 生成打印任务
							try
							{
								posDao.insertPrintTask(tenancyId, storeId, printerJo, params, printMode);

								if (SysDictionary.PRINT_CODE_1101.equals(printMode))
								{
									if (para.containsKey("bill_num"))
									{
										String uPrintCount = new String("update pos_bill set print_count =(coalesce(print_count,0)+1) where bill_num = ? and store_id = ? and tenancy_id=?");
										posDao.update(uPrintCount, new Object[]
										{ para.optString("bill_num"), storeId,tenancyId });
									}
								}
							}
							catch (Exception e)
							{
								logger.error("分单失败：打印机【" + printerId + "】生成打印任务失败" , e);
							}
							try
							{
								// 发出打印通知
								Data cjData = new Data();
								cjData.setType(Type.PRINT);
								cjData.setOper(Oper.print);
								Comet4jUtil.sendMessage2All(Comet4jUtil.POS_PRINTER, JSONObject.fromObject(cjData).toString());
								logger.debug("打印分单成功：打印机【" + printerId + "】" + "，打印模板【" + printMode + "】");
                                if(devicePrint){
                                    break print;
                                }
							}
							catch (Exception e)
							{
								logger.error("分单失败：打印机【" + printerId + "】服务器连接异常", e);
							}
						}
					}
					else
					{
						logger.info("分单失败：打印机【" + printerId + "】未找到打印模板【" + printMode + "】");
						continue;
					}
				}
			}
			else
			{
				logger.info("分单失败：模板【" + printMode + "】没有设定打印机");
			}
		}
		catch(Exception e)
		{
			logger.error("分单失败：其他原因导致分单失败" , e);
		}
	}
}
