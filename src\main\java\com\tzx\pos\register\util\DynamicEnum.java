package com.tzx.pos.register.util;

import java.lang.reflect.AccessibleObject;
import java.lang.reflect.Array;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import sun.reflect.ConstructorAccessor;
import sun.reflect.FieldAccessor;
import sun.reflect.ReflectionFactory;

public class DynamicEnum {

	private static ReflectionFactory reflectionFactory = ReflectionFactory.getReflectionFactory();

	private static void setFailsafeFieldValue(Field field, Object target, Object value)
			throws NoSuchFieldException, IllegalAccessException {

		field.setAccessible(true);

		Field modifiersField = Field.class.getDeclaredField("modifiers");
		modifiersField.setAccessible(true);
		int modifiers = modifiersField.getInt(field);

		modifiers &= ~Modifier.FINAL;
		modifiersField.setInt(field, modifiers);

		FieldAccessor fa = reflectionFactory.newFieldAccessor(field, false);
		fa.set(target, value);
	}

	private static void blankField(Class<?> enumClass, String fieldName)
			throws NoSuchFieldException, IllegalAccessException {
		for (Field field : Class.class.getDeclaredFields()) {
			if (field.getName().contains(fieldName)) {
				AccessibleObject.setAccessible(new Field[] { field }, true);
				setFailsafeFieldValue(field, enumClass, null);
				break;
			}
		}
	}

	private static void cleanEnumCache(Class<?> enumClass) throws NoSuchFieldException, IllegalAccessException {
		blankField(enumClass, "enumConstantDirectory"); // Sun (Oracle?!?) JDK
														// 1.5/6
		blankField(enumClass, "enumConstants"); // IBM JDK
	}

	private static ConstructorAccessor getConstructorAccessor(Class<?> enumClass, Class<?>[] additionalParameterTypes)
			throws NoSuchMethodException {
		Class<?>[] parameterTypes = new Class[additionalParameterTypes.length + 2];
		parameterTypes[0] = String.class;
		parameterTypes[1] = int.class;
		System.arraycopy(additionalParameterTypes, 0, parameterTypes, 2, additionalParameterTypes.length);
		return reflectionFactory.newConstructorAccessor(enumClass.getDeclaredConstructor(parameterTypes));
	}

	private static Object makeEnum(Class<?> enumClass, String value, int ordinal, Class<?>[] additionalTypes,
			Object[] additionalValues) throws Exception {
		Object[] parms = new Object[additionalValues.length + 2];
		parms[0] = value;
		parms[1] = Integer.valueOf(ordinal);
		System.arraycopy(additionalValues, 0, parms, 2, additionalValues.length);
		return enumClass.cast(getConstructorAccessor(enumClass, additionalTypes).newInstance(parms));
	}

	/**
	 * 动态新增枚举
	 * 
	 * @param enumType
	 * @param enumName
	 */
	@SuppressWarnings("unchecked")
	public static <T extends Enum<?>> void addEnum(Class<T> enumType, String enumName) {

		// 0. Sanity checks
		if (!Enum.class.isAssignableFrom(enumType)) {
			throw new RuntimeException("class " + enumType + " is not an instance of Enum");
		}

		// 1. Lookup "$VALUES" holder in enum class and get previous enum
		// instances
		Field valuesField = null;
		Field[] fields = enumType.getDeclaredFields();
		for (Field field : fields) {
			if (field.getName().contains("$VALUES")) {
				valuesField = field;
				break;
			}
		}
		AccessibleObject.setAccessible(new Field[] { valuesField }, true);

		try {

			// 2. Copy it
			T[] previousValues = (T[]) valuesField.get(enumType);
			List<T> values = new ArrayList<T>(Arrays.asList(previousValues));

			// 3. build new enum
			T newValue = (T) makeEnum(enumType, enumName, values.size(), new Class<?>[] {}, new Object[] {});

			// 4. add new value
			values.add(newValue);

			// 5. Set new values field
			setFailsafeFieldValue(valuesField, null, values.toArray((T[]) Array.newInstance(enumType, 0)));

			// 6. Clean enum cache
			cleanEnumCache(enumType);

		} catch (Exception e) {
			e.printStackTrace();
			throw new RuntimeException(e.getMessage(), e);
		}
	}

	/**
	 * 反射机制给枚举赋值
	 * method
	 * @param enumType
	 * @param methodName
	 * @param values
	 * @param name
	 * @return
	 */
	public static  boolean addEnumOfValuesMethod(Object enumType, String methodName, Object values) {

		Method[] mm = enumType.getClass().getDeclaredMethods();
		for (Method m : mm) {
			if (m.getName().equals(methodName)) {
				try {
					m.invoke(enumType,  values);
					return true;
				} catch (IllegalAccessException e) {
					return false;

				} catch (IllegalArgumentException e) {
					return false;

				} catch (InvocationTargetException e) {
					return false;
				}
			}

		}

		return false;
	}
	/**
	 * 反射机制给枚举赋值
	 * field
	 * @param enumType
	 * @param paramName
	 * @param values
	 * @return
	 */
	public static  boolean addEnumOfValuesField(Object enumType, String paramName, Object values) {
		Field[] fields=enumType.getClass().getDeclaredFields();
		for(Field field:fields){
			if(field.getName().equals(paramName)){
				if(field.getType().getName().equals(values.getClass().getName())){
					try {
						field.set(enumType, values);
					} catch (IllegalArgumentException e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					} catch (IllegalAccessException e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
				}
				
			}
		}
		return true;
	}

	public static void main(String[] args) {
		addEnum(OutParam.class, "d");
		addEnumOfValuesMethod(OutParam.valueOf("d"), "setParam", new String[] { "" });
		addEnumOfValuesField(OutParam.valueOf("d"), "param", new String[] { "xue" });
		String[] str = Enum.valueOf(OutParam.class, "d").getParam();
		for (int i = 0; i < str.length; i++) {
			System.out.println(str[i].toString());
		}

	}

}