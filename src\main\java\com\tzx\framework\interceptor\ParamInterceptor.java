/*
package com.tzx.framework.interceptor;

import com.tzx.framework.common.constant.Constant;
import net.sf.json.JSONObject;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;

*/
/**
 * XML AOP
 * Created by jzq1999 on 2017/4/17.
 *//*

public class ParamInterceptor {

    public void filterMethodParam(JoinPoint jp) throws Throwable{

        Object[] args = jp.getArgs();
        for(Object obj:args) {
            if(obj instanceof JSONObject) {
                JSONObject jsonObject = (JSONObject) obj;
                if(jsonObject.get(Constant.SPRING_SESSION_PARAM) != null) {
                    jsonObject.remove(Constant.SPRING_SESSION_PARAM);
                }
            }
        }

    }

}
*/
