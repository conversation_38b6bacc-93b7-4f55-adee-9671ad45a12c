package com.tzx.clientorder.common.entity;

import java.util.List;

public class AcewillOrder {	
	
	private Integer mode;
	private String isprint;
	private String report_date;
	private Integer shift_id;
	private String pos_num;
	private String opt_num;
	private String bill_num;
	private String sale_mode;
	private String table_code;
	private String waiter_num;
	private String bill_taste;
	private String remark;
	private List<AcewillOrderItem> item;
	//
	private String chanel;

	public Integer getMode() {
		return mode;
	}
	public void setMode(Integer mode) {
		this.mode = mode;
	}
	public String getIsprint() {
		return isprint;
	}
	public void setIsprint(String isprint) {
		this.isprint = isprint;
	}
	public String getReport_date() {
		return report_date;
	}
	public void setReport_date(String report_date) {
		this.report_date = report_date;
	}
	public Integer getShift_id() {
		return shift_id;
	}
	public void setShift_id(Integer shift_id) {
		this.shift_id = shift_id;
	}
	public String getPos_num() {
		return pos_num;
	}
	public void setPos_num(String pos_num) {
		this.pos_num = pos_num;
	}
	public String getOpt_num() {
		return opt_num;
	}
	public void setOpt_num(String opt_num) {
		this.opt_num = opt_num;
	}
	public String getBill_num() {
		return bill_num;
	}
	public void setBill_num(String bill_num) {
		this.bill_num = bill_num;
	}
	public String getSale_mode() {
		return sale_mode;
	}
	public void setSale_mode(String sale_mode) {
		this.sale_mode = sale_mode;
	}
	public String getTable_code() {
		return table_code;
	}
	public void setTable_code(String table_code) {
		this.table_code = table_code;
	}
	public String getWaiter_num() {
		return waiter_num;
	}
	public void setWaiter_num(String waiter_num) {
		this.waiter_num = waiter_num;
	}
	public String getBill_taste() {
		return bill_taste;
	}
	public void setBill_taste(String bill_taste) {
		this.bill_taste = bill_taste;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public List<AcewillOrderItem> getItem() {
		return item;
	}
	public void setItem(List<AcewillOrderItem> item) {
		this.item = item;
	}

	public String getChanel() {
		return chanel;
	}

	public void setChanel(String chanel) {
		this.chanel = chanel;
	}
}
