package com.tzx.member.crm.bo.imp;

import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.util.Tools;
import com.tzx.member.crm.bo.CustomerInvoiceService;
import com.tzx.pos.base.Constant;

import net.sf.json.JSONObject;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 会员充值开票
 * Created by qingui on 2018-07-12.
 */
@Service(CustomerInvoiceService.name)
public class CustomerInvoiceServiceImp extends CustomerServiceImp implements CustomerInvoiceService{

    private final String TIME = "timestamp";
    private Logger logger = Logger.getLogger(CustomerInvoiceServiceImp.class);

    @Override
    public Data getInvoiceBalancePost(String tenancyId, int storeId, String customerId) throws Exception {

        Data resultData = new Data();
        Data resData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
        resData.setType(Type.CRM_INVOICE);
        resData.setOper(Oper.find);
        resData.setT(System.currentTimeMillis());
        resData.setMsg("会员充值发票可开金额查询");
        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        Map<String, String> map = new HashMap<String, String>();
        map.put("customer_id",customerId);
        list.add(map);
        resData.setData(list);

        logger.info("调用crm的查询可开票金额入参是"+ resData);
        //调用crm的接口
        this.commonPost(JSONObject.fromObject(resData).toString(), resultData);
        return resultData;
    }

    private static Map<String, String> cache = new ConcurrentHashMap<String, String>();

    public static void setCache(String key, String obj){
        cache.put(key, obj);
    }

    public static String getCache(String key){
        return cache.get(key);
    }

    @Override
    public Data insertInvoicePost(String tenancyId, Data param) throws Exception {
        Data resultData = new Data();
        Data resData = Data.get(tenancyId, param.getStore_id(), Constant.CODE_SUCCESS);
        resData.setType(Type.CRM_INVOICE);
        resData.setOper(Oper.add);
        resData.setTenancy_id(tenancyId);
        resData.setStore_id(param.getStore_id());
        resData.setMsg("会员开充值发票");

        //时间戳，用于开票的时候校验是不是3秒内多次请求
        long timestamp = System.currentTimeMillis();
        String time = getCache(TIME);
        if (!Tools.isNullOrEmpty(time)){
            long beforeTime = Long.parseLong(time);
            //如果两次开票请求之间小于等于3秒，则不进行这次开票请求
            if ((timestamp - beforeTime)/1000 <=3){
                logger.info("之前开票请求的时间戳是："+ beforeTime + ",当前开票请求的时间戳是："+ timestamp+"相隔时间是："+ (timestamp - beforeTime)/1000 + "秒");
                resultData.setCode(Constant.CODE_CONN_EXCEPTION);
                resultData.setMsg(Constant.SET_INVOICE_FAILURE);
                resultData.setSuccess(false);
                return resultData;
            }
        }
        setCache("timestamp", timestamp + "");
        List<Map<String, String>> list = (List<Map<String, String>>) param.getData();
        if (!Tools.isNullOrEmpty(list)){
            for (Map<String, String> obj : list){
                //商户号+门店+时间戳
                obj.put("third_bill_code", tenancyId + param.getStore_id() + timestamp);
            }
        }
        resData.setData(list);

        //调用crm的接口
        this.commonPost(JSONObject.fromObject(resData).toString(), resultData);
        return resultData;
    }

    @Override
    public Data getInvoiceListPost(String tenancyId, Data param) throws Exception {
        Data resultData = new Data();
        Data resData = Data.get(tenancyId, param.getStore_id(), Constant.CODE_SUCCESS);
        resData.setType(Type.CRM_INVOICE);
        resData.setOper(Oper.load);
        resData.setTenancy_id(tenancyId);
        resData.setMsg("查询会员充值发票历史");
        List<Map<String,String>> list = new ArrayList<Map<String,String>>();

        list.add((Map<String,String>)param.getData().get(0));
        resData.setData(list);

        //调用crm的接口
        this.commonPost(JSONObject.fromObject(resData).toString(), resultData);
        return resultData;
    }
}
