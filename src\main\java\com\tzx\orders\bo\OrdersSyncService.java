package com.tzx.orders.bo;

import net.sf.json.JSONObject;

/** 订单数据同步
 * <AUTHOR>
 *
 */
public interface OrdersSyncService
{
	final String NAME="com.tzx.orders.bo.imp.OrderSyncServiceImp";


	/**
	 * 先付整单取消
	 */
	int PREPAY_ALL_ORDER_CANCEL=0;

	/**
	 * 先付单品退菜
	 */
	int PREPAY_SINGLE_DISH_CANCEL=1;
	
	/**
	 * 后付结账
	 */
	int POSTPAY_ORDER_COMPLETE=2;

	/** 同步订单数据
	 * @param operType 操作类型
	 * @param orderNum 订单编号(注意:不是账单编号)
	 * @param params 其它参数(如取消订单原因等 )
	 */
	void sync(int operType,String orderNum,JSONObject params) throws Exception;
	
	/** 同步订单数据
	 * @param operType 操作类型
	 * @param orderNum 订单编号(注意:不是账单编号)
	 */
	void sync(int operType,String orderNum) throws Exception;

    /** 并台
     * 例如:将单号002,003号单合并到001,则targetOrderNum=001,srcOrderNums=002,003
     * @param targetOrderNum 并台目标单号
     * @param srcOrderNums  并台源单号
     * @throws Exception
     */
    void mergeTable(String targetOrderNum,String... srcOrderNums) throws Exception;

    /** 转台
     * @param orderNum    订单号
     * @param targetTable  转到的桌位号
     * @throws Exception
     */
    void changeTable(String orderNum,String targetTable) throws Exception;
    
    /**
     * @param tenantId
     * @param organId
     * @param orderNum
     * @return
     * @throws Exception
     */
    boolean isSyncOrder(String tenantId,int organId,String orderNum) throws Exception;

}
