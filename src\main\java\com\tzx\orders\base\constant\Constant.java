package com.tzx.orders.base.constant;

import java.util.Arrays;
import java.util.List;

public class Constant
{
    /**
     * 固定保留位数(百度美团等第三方外卖结算时用)
     */
    public static final String NUMBER_EXACT="2";
    
    /**
     * 微信店内点菜
     */
    public static final String WX02_CHANNEL = "WX02";

	public static final String MT11_CHANNEL = "MT11";

	public static final String EL09_CHANNEL = "EL09";

	public static final String MT08_CHANNEL = "MT08";

	public static final String CC04_CHANNEL = "CC04";
	/**
	 * 到家 --货到付款
	 */
	public static final String DJ_CHANNEL   = "DJ01";

	/**
	 * 订单来源 ECO
	 */
	public static final String ECO_ORDER_SOURCE   = "ECO";
    
    /**
     * 微信外卖、电话外卖
     */
    public static final List<String> WM_CHANNEL= Arrays.asList("WM10","CC04");
    /**
     * 第三方外卖 TH01 开发外卖（如：沙绿自营外卖）
     */
    public static final List<String> THIRD_CHANNEL = Arrays.asList("BD06","MT08","EL09","MT11","TH01");

    // 系统参数
    public static final String DZFP_WXDN="dzfp_wxdndcsfkjdzfp";
    public static final String DZFP_WXWM="dzfp_wxwmsfkjdzfp";
    public static final String DZFP_DSFWM="dzfp_dsfwmsfkjdzfp";
    public static final String DZFP_DHWM="dzfp_dhwmsfkjdzfp";

    public static final String THIRD_IS_PRINT_DCD="is_wm_print_dcd";
    public static final String THIRD_IS_PRINT_BILL="is_wm_printbill";
    public static final String THIRD_SCD_COUNT="wm_print_bill_number";

    public static final int NOTIFY_NONE=0;
    public static final int NOTIFY_SAAS=1;
    public static final int NOTIFY_POS=2;
    public static final int NOTIFY_ALL=4;


	/**
	 * 外卖预订单响铃提醒标识，用于发送pos 声音提醒
	 */
	public static final String  ADVANCE_ORDERS_REMIND = "advance_orders_remind";
	/**
     * 退菜标记
     */
    public static final String TC_TAG="退菜";
    /**
     * 奉送标记
     */
    public static final String FS_TAG="奉送";
	/**
	 * POS预打单
	 */
	public static final String	ORDER_PRINT_CODE_1001			= "1001";
	/**
	 * POS结账单
	 */
	public static final String	ORDER_PRINT_CODE_1002			= "1002";
	/**
	 * POS补打结账单
	 */
	public static final String	ORDER_PRINT_CODE_1110			= "1110";
	/**
	 * POS点菜单
	 */
	public static final String	ORDER_PRINT_CODE_1003			= "1003";
	/**
	 * 外卖 随餐单
	 */
	public static final String	ORDER_PRINT_CODE_1201			= "1201";
	/**
	 * 外卖 取消订单
	 */
	public static final String	ORDER_PRINT_CODE_1202			= "1202";

	/**
	 * 付款状态:未付款
	 */
	public static final String	PAYMENT_STATE_UNPAID			= "01";
	/**
	 * 付款状态:已付款
	 */
	public static final String	PAYMENT_STATE_ALREADY			= "02";
	/**
	 * 付款状态:付款完成
	 */
	public static final String	PAYMENT_STATE_COMPLETE			= "03";
	/**
	 * 付款状态:已退款
	 */
	public static final String	PAYMENT_STATE_REFUND			= "04";

	/**
	 * 订单状态:新订单
	 */
	public static final String	ORDER_STATE_NEW					= "01";
	/**
	 * 订单状态:到店
	 */
	public static final String	ORDER_STATE_TOSHOP				= "02";
	/**
	 * 订单状态:下发失败
	 */
	public static final String	ORDER_STATE_TOSHOP_FAILURE		= "03";
	/**
	 * 订单状态:接收
	 */
	public static final String	ORDER_STATE_RECEIVE				= "04";
	/**
	 * 订单状态:付款完成
	 */
	public static final String	ORDER_STATE_COMPLETE_PAYMENT	= "05";
	/**
	 * 订单状态:已派单
	 */
	public static final String	ORDER_STATE_DISPATCH			= "06";
	/**
	 * 订单状态:配送中
	 */
	public static final String	ORDER_STATE_DELIVERY			= "07";
	/**
	 * 订单状态:取消
	 */
	public static final String	ORDER_STATE_CANCEL				= "08";
	/**
	 * 订单状态:取消失败
	 */
	public static final String	ORDER_STATE_CANCEL_FAILURE		= "09";
	/**
	 * 订单状态:已完成
	 */
	public static final String	ORDER_STATE_COMPLETE			= "10";
    /**
     * 订单状态:转账单失败
     */
    public static final String	ORDER_STATE_EXCEPTION           = "11";
    
    /**
     * 订单状态:异常订单完成
     */
    public static final String	ORDER_STATE_EXCEPTION_COMPLETE  = "12";
    
    /**
     * 订单状态:用户退款申请
     */
    public static final String	ORDER_STATE_REFUND_APPLY        = "20";
    
    /**
     * 订单状态:POS收到退款申请
     */
    public static final String	ORDER_STATE_REFUND_RECEIVED	    = "21";
    
    /**
     * 订单状态:商家同意退款
     */
    public static final String	ORDER_STATE_REFUND_AGREE	    = "22";
    
    /**
     * 订单状态:商家不同意退款
     */
    public static final String	ORDER_STATE_REFUND_DISAGREE	    = "23";
    
    /**
     * 订单状态:用户取消退款申请
     */
    public static final String	ORDER_STATE_REFUND_CANCEL	    = "24";
    
    /**
     * 外卖班次
     */
    public static final Integer WM_SHIFT_ID                     = -100;
    
    /**
     * 统一外卖班次下 默认POS机号
     */
    public static final String  WM_POS_NUM                         = "外卖";
    
    /**
     * 统一外卖班次下 默认收银员号
     */
    public static final String  WM_OPT_NUM                         = "外卖";
    
    /**
     * 外卖统一班次下生产序列号前缀
     */
    public static final String  WM_SERIAL_NUM                      = "1000";

    /**
     * 交班之间的班次
     */
    public static final Integer BETWEEN_SHIFT_ID                = 0;
    
    /**
     * 交班之间的 默认 默认POS机号
     */
    public static final String  BETWEEN_SHIFT_POS_NUM           = "-1";
    
    /**
     * 交班之间的 默认收银员号
     */
    public static final String  BETWEEN_SHIFT_OPT_NUM           = "-1";
}
