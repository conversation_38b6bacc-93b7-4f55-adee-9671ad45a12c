package com.tzx.pos.bo.payment.imp.ext;

import java.sql.Timestamp;
import java.util.Date;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Service;

import com.tzx.base.entity.PosBillPayment;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.Tools;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.bo.payment.PaymentWayService;
import com.tzx.pos.bo.payment.imp.PaymentWayServiceImp;

/**
 * 免单
 *
 */
@Service("com.tzx.pos.bo.payment.imp.ext.FreesinglePaymentWayServiceImp")
public class FreesinglePaymentWayServiceImp extends PaymentWayServiceImp implements PaymentWayService
{
	@Override
	public void payment(String tenantId, int storeId, String billNum, String batchNum,Date reportDate, int shiftId, String posNum, String optNum, String tableCode, JSONObject payment, Timestamp currentTime,String isOnlinePay) throws Exception
	{
		double amount = payment.optDouble("amount");
		double count = payment.optDouble("count");

		int jzid = payment.optInt("jzid");
		String paymentClass = payment.optString("payment_class");
		String paymentName = payment.optString("payment_name");
		String nameEnglish = payment.optString("payment_english_name");
		double rate = payment.optDouble("rate");
		String remark = payment.optString("remark");
		String isYsk = payment.optString("is_ysk");
		if (Tools.isNullOrEmpty(isYsk))
		{
			isYsk = "N";
		}

		double paymentAmount = payment.optDouble("payment_amount");
		double discountAmount = payment.optDouble("discount_amount");
		double discountRate = payment.optDouble("discount_rate");
		int discountCaseId = payment.optInt("discount_case_id");

		if (discountCaseId != 0 || discountRate != 100 || discountAmount > 0)
		{
			throw SystemException.getInstance(PosErrorCode.POS_FREESINGLE_NOT_DISCOUNT_ERROR);
		}

		if (paymentAmount != amount)
		{
			throw SystemException.getInstance(PosErrorCode.POS_FREESINGLE_NOT_PAYMENT_ERROR);
		}

		// 免单不允许与其他方式混结
		String sql = new String("select count(*) as count from pos_bill_payment where bill_num =? and store_id = ? and tenancy_id=?");
		int paycount = paymentDao.queryForInt(sql.toString(), new Object[]
		{ billNum, storeId, tenantId });
		if (paycount > 0)
		{
			throw SystemException.getInstance(PosErrorCode.POS_FREESINGLE_NOT_PAYMENT_ERROR);
		}

		// 免单金额=账单金额
		Double due = amount;
		Double tenancyAssume = 0d;
		if ("0".equals(payment.optString("if_income")))
		{
			due = 0d;
			tenancyAssume = amount;
		}
		
		PosBillPayment paymentEntity = new PosBillPayment(tenantId, storeId, reportDate, shiftId, posNum, optNum, billNum, batchNum, tableCode, paymentClass, jzid, paymentName, nameEnglish, amount, amount, Double.valueOf(count).intValue(), null, null, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
		paymentEntity.setRate(rate);
		paymentEntity.setIs_ysk(isYsk);
		paymentEntity.setRemark(remark);
		paymentEntity.setCoupon_buy_price(due);
		paymentEntity.setDue(due);
		paymentEntity.setTenancy_assume(tenancyAssume);
		paymentDao.insertPosBillPayment(tenantId, storeId, paymentEntity);
		
//		paymentDao.insertPosBillPayment(tenantId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, nameEnglish, rate, amount, count, amount, null, null, isYsk, null, null, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum,
//				null, 0d);

//		paymentDao.insertPosBillPaymentLog(tenantId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, nameEnglish, rate, amount, count, amount, null, null, isYsk, null, null, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum,
//				null, 0d);

		String updateBillSql = new String("update pos_bill set free_amount=?,billfree_reason_id=? where bill_num = ? and store_id = ? and tenancy_id = ?");
		paymentDao.update(updateBillSql, new Object[]
		{ amount, payment.optInt("reason_id"), billNum, storeId, tenantId });

		String updateBillItemSql = new String("update pos_bill_item set item_remark=? where bill_num = ? and store_id = ? and tenancy_id = ? and item_remark is null");
		paymentDao.update(updateBillItemSql, new Object[]
		{ SysDictionary.ITEM_REMARK_MD03, billNum, storeId, tenantId });
	}

	@Override
	public void paymentDebit(String tenancyId, int storeId, String billNum,String orderNum,String chanel, Double paymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData) throws Exception
	{
		// TODO Auto-generated method stub

	}

	@Override
	public void findPaymentDebit(String tenancyId, int storeId, String billNum,String orderNum,String chanel,Double paymentAmount,JSONObject payment,Timestamp currentTime,JSONObject resultJson,Data resultData) throws Exception
	{
		// TODO Auto-generated method stub

	}

	@Override
	public void paymentRefund(String tenancyId, int storeId, String billNum,String oldBillNum,String orderNum,String chanel,Double billPaymentAmount,JSONObject payment,Timestamp currentTime,JSONObject resultJson,Data resultData,String operType) throws Exception
	{
		// TODO Auto-generated method stub

	}

	@Override
	public void paymentCancel(String tenancyId, int storeId, String billNum, String orderNum, String chanel, Double billPaymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData, String operType) throws Exception
	{
		// TODO Auto-generated method stub
		
	}

	@Override
	public void paymentRegain(String tenancyId, int storeId, String billNum, String orderNum, String chanel, Double billPaymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData, String operType) throws Exception
	{
		// TODO Auto-generated method stub
		
	}

	@Override
	public void paymentDebitResult(String tenancyId, int storeId, String billNum, JSONObject paymentJson) throws Exception
	{
		// TODO 自动生成的方法存根
		
	}
}
