package com.tzx.pos.base.print;

import java.util.Vector;

/**
 * 菜品打印消息对列,自动增加
 * <AUTHOR>
 * 2015年7月27日-下午3:38:38
 */
public class DishQueue
{
	//使用Vector不用考虑同步问题
	private Vector queue = null;
	
	public DishQueue()
	{
		queue = new Vector();
	}
	
	/**
	 * 这个参数Object可以换成JSONObject
	 * @param obj
	 */
	public void push(Object obj)
	{
		queue.addElement(obj);
	}
	/**
	 * 可以返回JSONObject
	 * @return
	 */
	public Object pull()
	{
		if(queue.size()==0)
		{
			return null;
		}
		Object obj = queue.firstElement();
		queue.removeElementAt(0);
		return obj;
	}
	
	public int size()
	{
		return queue.size();
	}
	
	public boolean isEmpty()
	{
		if (queue.size() == 0)
		{
			return true;
		}else
			return false;
	}
}
