package com.tzx.framework.common.print.template;

import java.awt.Font;
import java.awt.Image;
import java.awt.Toolkit;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;

import net.sf.json.JSONObject;

import org.jsoup.Jsoup;
import org.jsoup.helper.StringUtil;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.Node;
import org.jsoup.nodes.TextNode;
import org.jsoup.select.Elements;
import org.jsoup.select.NodeTraversor;
import org.jsoup.select.NodeVisitor;

import com.tzx.framework.common.print.PrintLine;
import com.tzx.framework.common.print.PrintMete;
import com.tzx.framework.common.util.Tools;

/**
 * 打印模板
 * 
 * <AUTHOR>
 * 
 */
public class PrintTemplate_HTML extends PrintTemplate
{
	private PlainTextVisitor	plainTextVisitor	= new PlainTextVisitor();
	NodeTraversor				traversor			= new NodeTraversor(plainTextVisitor);

	private class PlainTextVisitor implements NodeVisitor
	{

//		private static final int	maxWidth	= 80;
		private int					width;
		private StringBuilder		accum;
		private int					maxFontSize	= nomal;

		private PlainTextVisitor()
		{
			width = 0;
			accum = new StringBuilder();
		}

		public void head(Node node, int depth)
		{

			String name = node.nodeName();
			if (node instanceof TextNode) append(((TextNode) node).text());
			else if (name.equals("li")) append("\n * ");
			else if (name.equals("dt")) append("  ");
			else if (StringUtil.in(name, new String[]
			{ "p", "h1", "h2", "h3", "h4", "h5", "tr" }))
			{
				int m = getSize(name);

				maxFontSize = m > maxFontSize ? m : maxFontSize;
				append("\n");
			}
		}

		public void tail(Node node, int depth)
		{

			String name = node.nodeName();
			if (StringUtil.in(name, new String[]
			{ "br", "dd", "dt", "p", "h1", "h2", "h3", "h4", "h5" })) append("\n");
			else if (name.equals("a")) append(String.format(" <%s>", new Object[]
			{ node.absUrl("href") }));
		}

		private void append(String text)
		{
			if (text.startsWith("\n")) width = 0;
			if (text.equals(" ") && (accum.length() == 0 || StringUtil.in(accum.substring(accum.length() - 1), new String[]
			{ " ", "\n" }))) return;

			if (text.length() + width > 80)
			{
				String words[] = text.split("\\s+");
				for (int i = 0; i < words.length; i++)
				{
					String word = words[i];
					boolean last = i == words.length - 1;
					if (!last) word = (new StringBuilder()).append(word).append(" ").toString();
					if (word.length() + width > 80)
					{
						accum.append("\n").append(word);
						width = word.length();
					}
					else
					{
						accum.append(word);
						width += word.length();
					}
				}
			}
			else
			{
				accum.append(text);
				width += text.length();
			}
		}

		public String toString()
		{
			return accum.toString();
		}

		public void clear()
		{
			accum.setLength(0);
		}

		public int getMaxFontSize()
		{
			return maxFontSize;
		}

	}

	private class FormattingVisitor implements NodeVisitor
	{
		private int				_lineByteSize	= 45;

		private List<Integer>	_linePartsSize;

//		private int				width;
		private List<PrintLine>	printLineList	= new ArrayList<PrintLine>();

		private PrintLine		var_line;
		private PrintMete		var_mete;

		/**
		 * 大于0时表明是同一行输出，数字越大表明层级越多
		 */
		private boolean			_isInTable		= false;
		private int				_isOneRow		= 0;
//		private int				_whole_col		= 1;
//		private int				_current_col	= 0;
		private StringBuilder	_sb				= new StringBuilder();

		private FormattingVisitor()
		{
		}

		public PrintLine getPrintLine()
		{
			return new PrintLine(font_nomal, 0.5f, 0f);
		}

		public PrintMete getPrintMete(Node node)
		{
			if (node.hasAttr("style"))
			{
				if (node.attr("style").contains("text-align:center"))
				{
					return new PrintMete(PrintMete.content_style_STR_CENTER, font_nomal, "", f005);
				}
			}

			return new PrintMete(PrintMete.content_style_STR, font_nomal, "", f005);
		}

		private int getBytesLength(String msg)
		{
			return msg.getBytes(Charset.forName("GB2312")).length;
		}

		private int getLineByteSize(int maxFontSize)
		{
			return _lineByteSize * nomal / maxFontSize;
		}

		public void head(Node node, int depth)
		{
			String name = node.nodeName();
			if (node instanceof TextNode)
			{
				String text = ((TextNode) node).text();

				if ("".equals(text.trim()))
				{
					return;
				}

				if (_sb.length() > 0)
				{
					_sb.append(",");
				}
				_sb.append(text);
			}
			else if (name.equals("strong"))
			{
				if (var_mete != null)
				{
					var_mete.setFont(var_mete.getFont().deriveFont(Font.BOLD));
				}
			}
			else if (name.equals("table"))
			{
				_isInTable = true;
			}
			else if (StringUtil.in(name, new String[]
			{ "p", "h1", "h2", "h3", "h4", "h5", "h6", "tr" }))
			{

				if (_isOneRow == 0)
				{
					var_line = getPrintLine();
					var_mete = getPrintMete(node);
					var_line.getPrintMeteList().add(var_mete);
					printLineList.add(var_line);
				}

				_isOneRow++;

				int fontSize = getSize(name);
				if (nomal != fontSize && var_mete != null)
				{
					Font font = var_mete.getFont();

					var_mete.setFont(font.deriveFont(font.getStyle(), fontSize));
					var_line.setFont(var_mete.getFont());
				}

				if (_linePartsSize == null)
				{

					_linePartsSize = new ArrayList<Integer>();
					int len = 1;

					List<Node> childNode = node.childNodes();

					int maxFontSize = nomal;
					for (int c = 0; c < childNode.size(); c++)
					{
						if (childNode.get(c) instanceof TextNode)
						{
							TextNode t = (TextNode) childNode.get(c);

							if ("".equals(t.text().trim())) continue;
						}

						plainTextVisitor.clear();
						traversor.traverse(node.childNode(c));

//						int l = plainTextVisitor.width;
//						_linePartsSize.add(l);
//						len += l;
						
						int l = getBytesLength(plainTextVisitor.toString().replace("\n", ""));
						if (l == 0)
						{
							l = 1;
						}
						_linePartsSize.add(l);
						len += l;

						maxFontSize = plainTextVisitor.getMaxFontSize() > maxFontSize ? plainTextVisitor.getMaxFontSize() : maxFontSize;
					}

					for (int i = 0; i < _linePartsSize.size(); i++)
					{
						_linePartsSize.set(i, _linePartsSize.get(i) * getLineByteSize(maxFontSize) / len);
					}

				}

			}
		}

		public void tail(Node node, int depth)
		{
			String name = node.nodeName();

			if (StringUtil.in(name, new String[]
			{ "p", "h1", "h2", "h3", "h4", "h5", "h6", "tr" }))
			{
				_isOneRow--;

				if (_isOneRow == 0)
				{
					if(_sb.length() > 0)
					{
						String msgs[] = _sb.toString().split(",");
						_sb.setLength(0);
	
						for (int i = 0; i < _linePartsSize.size(); i++)
						{
							if (i < msgs.length)
							{
								_sb.append(msgs[i].replace("\n", ""));
								if (_isInTable)
								{
									for (int j = 0; j < _linePartsSize.get(i) - getBytesLength(msgs[i].replace("\n", "")) && _linePartsSize.get(i) - getBytesLength(msgs[i].replace("\n", "")) > 0; j++)
									{
										_sb.append(" ");
									}
								}
							}
						}
	
						if (var_mete != null && var_mete.getContent_style()!= PrintMete.CONTENT_STYLE_IMAGE)
						{
							var_mete.setContent(_sb.toString());
						}
						_sb.setLength(0);
					}

					if (!_isInTable)
					{
						_linePartsSize = null;
					}
				}

			}
			else if (name.equals("table"))
			{
				_isInTable = false;
			}else if("img".equals(name)){
				
				String src= node.attr("src");
				Image img=null;
				try
				{
					if(Tools.hv(src)){
						img = Toolkit.getDefaultToolkit().getImage(new URL(src));
					}
				}
				catch (MalformedURLException e)
				{
					e.printStackTrace();
				}
				
				if(null !=img)
				{
					float width= Float.parseFloat(node.attr("width")!=null&& !"".equals(node.attr("width"))?node.attr("width"):"0")/1.5f;
					float height= Float.parseFloat(node.attr("height")!=null&& !"".equals(node.attr("height"))?node.attr("height"):"0")/1.5f;
					if(height<=0){
						height= width/img.getWidth(null)*img.getHeight(null);
					}
	//				if (_isOneRow == 0)
	//				{
	//					var_line = getPrintLine();
	//					var_mete = getPrintMete(node);
	//					var_line.getPrintMeteList().add(var_mete);
	//					printLineList.add(var_line);
	//				}
	//				
	//				var_mete.setContent_style(PrintMete.CONTENT_STYLE_IMAGE);
	//				var_mete.setContent(node.attr("src"));
	//				var_mete.setWidth(width);
	//				var_mete.setHeight(height);
							
					var_line = getPrintLine();
					var_mete = new PrintMete(PrintMete.CONTENT_STYLE_IMAGE, font_nomal, node.attr("src"), f005,width,height);
					var_line.setMarginBottom(0.5f);
					var_line.getPrintMeteList().add(var_mete);
					printLineList.add(var_line);
				}
			}

		}

		public List<PrintLine> getPrintLineList()
		{
			return printLineList;
		}

	}

	public List<PrintLine> setupPrintLineList(Object obj) throws Exception
	{
		Document dom = (Document) obj;

		FormattingVisitor formatter = new FormattingVisitor();
		NodeTraversor traversor = new NodeTraversor(formatter);
		traversor.traverse(dom);

//		System.out.println("解析成功......");

		return formatter.getPrintLineList();
	}

	public String parse(int pageSize, String template, List<JSONObject> param) throws Exception
	{
		Document dom = Jsoup.parse(template, "UTF-8");

		// 处理非Table项
		Elements targets = dom.getElementsByAttribute("title");
		for (Element target : targets)
		{
			boolean notTable = true;
			for (Element parent : target.parents())
			{
				if ("table".equals(parent.tagName()))
				{
					notTable = false;
					break;
				}
			}

			if (notTable)
			{
				String[] keys = target.attr("title").split(":");
				for (JSONObject json : param)
				{
					if (keys[0].equals(json.optString("result_set_title")))
					{
						JSONObject p = json.optJSONArray("result").optJSONObject(0);
						if("img".equalsIgnoreCase(target.tagName())){
							if (p != null && p.containsKey(keys[1].trim()))
							{
								target.attr("src", p.optString(keys[1].trim()));
							}else{
								target.attr("src", "");
							}
							
						}else{
							if (p != null && p.containsKey(keys[1]) && !"null".equals(p.optString(keys[1])))
							{
								target.text(p.optString(keys[1]));
							}
							else
							{
								target.text("--");
							}
						}
						break;
					}
				}
			}

		}

		// 处理Table项
		Elements tables = dom.getElementsByTag("table");
		for (Element table : tables)
		{
			Elements trs = table.getElementsByTag("tr");
			Elements copyTr = new Elements();
			int len = 0;
			for (Element tr : trs)
			{
				Elements ems = tr.getElementsByAttribute("title");
				if (ems.size() > 0)
				{
					for (Element em : ems)
					{
						String[] keys = em.attr("title").split(":");
						for (JSONObject json : param)
						{
							if (keys[0].equals(json.optString("result_set_title")))
							{
								len = json.optJSONArray("result").size() > len ? json.optJSONArray("result").size() : len;
								break;
							}
						}
					}
					copyTr.add(tr);
				}
			}

			int i = 0;
			do
			{
				Elements trs1 = table.getElementsByTag("tr");
				List<Element> copyTr1 = trs1.subList(trs1.size() - copyTr.size(), trs1.size());
				for (Element tr : copyTr1)
				{
					tr.removeAttr("delete");
					boolean isnull=true;
					Elements ems1 = tr.getElementsByAttribute("title");
					for (Element em1 : ems1)
					{
						String[] keys = em1.attr("title").split(":");
						for (JSONObject json : param)
						{
							if (keys[0].equals(json.optString("result_set_title")))
							{
								JSONObject p = json.optJSONArray("result").optJSONObject(i);
								if("img".equalsIgnoreCase(em1.tagName())){
									if (p != null && p.containsKey(keys[1]))
									{
										em1.attr("src", p.optString(keys[1]));
										isnull=false;
									}
								}else{
									if (p != null && p.containsKey(keys[1]) && !"null".equals(p.optString(keys[1])))
									{
										em1.html(p.optString(keys[1]));
//										em1.text(p.optString(keys[1]));
										isnull=false;
									}
								}
								break;
							}
						}
					}
					
					if(isnull){
						tr.attr("delete", "delete");
					}
				}
				if (++i < len)
				{
					trs.last().parent().append(copyTr.toString());
				}
			}
			while (i < len);
			
			Elements trs2 = table.getElementsByTag("tr");
			for (Element tr : trs2){
				if("delete".equals(tr.attr("delete"))){
					tr.remove();
				}
			}
			
		}

		return dom.toString();
	}
	
	public String parseCus(int pageSize, String template, List<JSONObject> param) throws Exception
	{
		Document dom = Jsoup.parse(template, "UTF-8");

		// 处理非Table项
		Elements targets = dom.getElementsByAttribute("title");
		for (Element target : targets)
		{
			boolean notTable = true;
			for (Element parent : target.parents())
			{
				if ("table".equals(parent.tagName()))
				{
					notTable = false;
					break;
				}
			}

			if (notTable)
			{
				String[] keys = target.attr("title").split(":");
				for (JSONObject json : param)
				{
					if("img".equalsIgnoreCase(target.tagName())){
						if (json != null && json.containsKey(keys[1].trim()))
						{
							target.attr("src", json.optString(keys[1].trim()));
						}else{
							target.attr("src", "");
						}
						
					}else{
						if (json != null && json.containsKey(keys[1]) && !"null".equals(json.optString(keys[1])))
						{
							target.text(json.optString(keys[1]));
						}
						else
						{
							target.text("--");
						}
					}
				}
			}

		}

		// 处理Table项
		Elements tables = dom.getElementsByTag("table");
		for (Element table : tables)
		{
			Elements trs = table.getElementsByTag("tr");
			Elements copyTr = new Elements();
			int len = 0;
			for (Element tr : trs)
			{
				Elements ems = tr.getElementsByAttribute("title");
				if (ems.size() > 0)
				{
					for (Element em : ems)
					{
						String[] keys = em.attr("title").split(":");
						for (JSONObject json : param)
						{
							if (keys[0].equals(json.optString("result_set_title")))
							{
								len = json.optJSONArray("result").size() > len ? json.optJSONArray("result").size() : len;
								break;
							}
						}
					}
					copyTr.add(tr);
				}
			}

			int i = 0;
			do
			{
				Elements trs1 = table.getElementsByTag("tr");
				List<Element> copyTr1 = trs1.subList(trs1.size() - copyTr.size(), trs1.size());
				for (Element tr : copyTr1)
				{
					tr.removeAttr("delete");
					boolean isnull=true;
					Elements ems1 = tr.getElementsByAttribute("title");
					for (Element em1 : ems1)
					{
						String[] keys = em1.attr("title").split(":");
						for (JSONObject json : param)
						{
							if (keys[0].equals(json.optString("result_set_title")))
							{
								JSONObject p = json.optJSONArray("result").optJSONObject(i);
								if("img".equalsIgnoreCase(em1.tagName())){
									if (p != null && p.containsKey(keys[1]))
									{
										em1.attr("src", p.optString(keys[1]));
										isnull=false;
									}
								}else{
									if (p != null && p.containsKey(keys[1]) && !"null".equals(p.optString(keys[1])))
									{
										em1.text(p.optString(keys[1]));
										isnull=false;
									}
								}
								break;
							}
						}
					}
					
					if(isnull){
						tr.attr("delete", "delete");
					}
				}
				if (++i < len)
				{
					trs.last().parent().append(copyTr.toString());
				}
			}
			while (i < len);
			
			Elements trs2 = table.getElementsByTag("tr");
			for (Element tr : trs2){
				if("delete".equals(tr.attr("delete"))){
					tr.remove();
				}
			}
			
		}

		return dom.toString();
	}
}
