package com.tzx.base.bo.imp;

import java.io.File;

import org.springframework.beans.BeansException;
import org.springframework.stereotype.Service;

import com.tzx.base.bo.ProcessMessageService;
import com.tzx.base.bo.SpecialService;
import com.tzx.base.constant.Special;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

@Service(SpecialService.NAME)
public class SpecialServiceImp implements SpecialService
{

	@Override
	public String specialManagement(final String tenancyId, final int storeId, JSONObject data) throws Exception
	{
		String returnRes = "";
		if (data.containsKey("data"))
		{
			JSONArray specialsJson = data.optJSONArray("data");
			for (int i = 0; i < specialsJson.size(); i++)
			{
				String special = specialsJson.optString(i);
				if (Special.updateproductimgaction.name().equals(special.toLowerCase()))
				{
					// 加载图片
					new Thread()
					{
						@Override
						public void run()
						{
							try
							{
								DBContextHolder.setTenancyid(tenancyId);
								ProcessMessageService processMessageService = (ProcessMessageService) SpringConext.getBean(ProcessMessageService.NAME);
								processMessageService.updateProductPhoto(tenancyId, storeId);
							}
							catch (BeansException e)
							{
								e.printStackTrace();
							}
							catch (Exception e)
							{
								e.printStackTrace();
							}

						}
					}.start();
				}
				else if (Special.updateversionaction.name().equals(special.toLowerCase()))
				{
					String filePath = System.getProperty("catalina.base") + System.getProperty("file.separator") + "update.lock";
					File file = new File(filePath);
					if (file.exists())
					{
						file.delete();
					}
					// file.getParentFile().mkdirs();
					file.createNewFile();
				}
				else if (Special.boh_img_padpc.name().equals(special.toLowerCase()))
				{
					new Thread()
					{
						@Override
						public void run()
						{
							try
							{
								DBContextHolder.setTenancyid(tenancyId);
								ProcessMessageService processMessageService = (ProcessMessageService) SpringConext.getBean(ProcessMessageService.NAME);
								processMessageService.downloadViceScreenPhoto(tenancyId, storeId);
							}
							catch (BeansException e)
							{
								e.printStackTrace();
							}
							catch (Exception e)
							{
								e.printStackTrace();
							}

						}
					}.start();
				}
				else
				{
//					logger.warn("指令:" + special + "无效!");
					returnRes = "指令:" + special + "无效!";
				}
			}
		}
		returnRes = "指令执行成功!";
	return returnRes;
	}

}
