package com.tzx.orders.po.springjdbc.dao.imp;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

import org.apache.commons.lang.StringUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Repository;

import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.dao.impl.GenericDaoImpl;
import com.tzx.orders.base.constant.Constant;
import com.tzx.orders.bo.dto.OrderState;
import com.tzx.orders.bo.dto.PosBill;
import com.tzx.orders.bo.dto.PosBillItem;
import com.tzx.orders.bo.dto.PosBillPayment;
import com.tzx.orders.po.springjdbc.dao.OrdersManagementDao;
import com.tzx.pos.base.constant.SysDictionary;

import net.sf.json.JSONObject;

@Repository(OrdersManagementDao.NAME)
public class OrdersManagementDaoImp extends GenericDaoImpl implements OrdersManagementDao
{
	@Override
	public JSONObject getCurrentDutyOrder(String tenancyId, int storeId) throws Exception
	{
		JSONObject ret = new JSONObject();
		Date reportDate = new Date();
		String time = DateUtil.formatTime(reportDate);

		StringBuilder sql = new StringBuilder();
		sql.append(" select dt.id as shift_id, dt.date_offset from (");
		sql.append(" select id ,start_time,case when start_property = 'DR01' and end_property = 'CR02' then '24:00' else end_time end end_time,valid_state,0 date_offset from duty_order");
		sql.append(" union select id ,'00:00'start_time,end_time,valid_state,-1 date_offset from  duty_order where start_property = 'DR01' and end_property = 'CR02'");
		sql.append(" ) dt inner join duty_order_of_ogran doo on dt.id = doo.duty_order_id");
		sql.append(" where doo.organ_id=? and dt.valid_state = '1' and dt.start_time<? and ?<dt.end_time");

		SqlRowSet rs = jdbcTemplate.queryForRowSet(sql.toString(), new Object[]
		{ storeId, time, time });

		if (rs.next())
		{
			ret.put("shift_id", rs.getInt("shift_id"));
		}
		else
		{
			throw SystemException.getInstance(PosErrorCode.NOT_USERFUL_SHIFT);
		}
		return ret;
	}

	@Override
	public JSONObject getCurrentReportDate(String tenancyId, int storeId) throws Exception
	{
		JSONObject ret = new JSONObject();
		ret.put("report_date", DateUtil.format(new Date()));
		String qReportDate = new String("select max(report_date) as report_date from pos_opt_state where content = 'DAYBEGAIN' and store_id = ? and tenancy_id = ?");
		SqlRowSet rst = jdbcTemplate.queryForRowSet(qReportDate, new Object[]
		{ storeId, tenancyId });
		if (rst.next() && null != rst.getString("report_date"))
		{
			ret.put("report_date", rst.getString("report_date"));
		}
		return ret;
	}

	@Override
	public boolean checkReportDate(String tenancyId, int storeId, String reportDate) throws Exception
	{
		if (null == reportDate || "".equals(reportDate))
		{
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_REPORT_DATE);
		}
		StringBuilder sql = new StringBuilder("select ");
		sql.append("(select count(*) from pos_opt_state where content = 'DAYBEGAIN' and report_date=? and store_id = ? and tenancy_id = ?) as daybegain,");
		sql.append("(select count(*) from pos_opt_state where content = 'DAYEND' and report_date=? and store_id = ? and tenancy_id = ?) as dayend");

		SqlRowSet rst = jdbcTemplate.queryForRowSet(sql.toString(), new Object[]
		{ DateUtil.parseDate(reportDate), storeId, tenancyId, DateUtil.parseDate(reportDate), storeId, tenancyId });
		if (rst.next())
		{
			if (rst.getInt("daybegain") == 0)
			{
				throw SystemException.getInstance(PosErrorCode.NOT_EXIST_DAYBEGAIN_ERROR);
			}

			if (rst.getInt("dayend") > 0)
			{
				throw SystemException.getInstance(PosErrorCode.ALREADY_EXIST_DAYEND_ERROR);
			}
			return true;
		}
		return false;
	}

	@Override
	public List<JSONObject> getPaymentWay(String tenancyId, int storeId) throws Exception
	{
		String sql = "select distinct t1.id,t1.payment_name1,t1.payment_name2,t1.payment_class,t1.is_standard_money from  payment_way t1 inner join payment_way_of_ogran t2 on t1.id=t2.payment_id and t1.status='1' where t2.organ_id=?";
		return this.query4Json(tenancyId, sql, new Object[]
		{ storeId });
	}

	@Override
	public List<JSONObject> getUnusualReason(String tenancyId, int storeId, JSONObject para) throws Exception
	{
		StringBuilder sql = new StringBuilder("select id,reason_name as text,father_id from hq_unusual_reason where valid_state='1'");
		if (para.containsKey("unusual_type"))
		{
			sql.append(" and unusual_type='").append(para.optString("unusual_type")).append("'");
		}
		if(para.containsKey("reason_code")){
			sql.append(" and reason_code like '").append(para.optString("reason_code")).append("%'");
		}
		return this.query4Json(tenancyId, sql.toString());
	}

	@Override
	public JSONObject getOrganInfo(String tenentid, int storeid) throws Exception
	{
		String sql = "select o.org_uuid,o.organ_code,o.org_full_name,o.org_short_name,o.org_type,o.format_state,o.manage_type,mo.item_menu_id from organ o left join hq_item_menu_organ mo on o.id=mo.store_id where o.tenancy_id=? and o.id=?";

		List<JSONObject> organList = this.query4Json(tenentid, sql, new Object[]
		{ tenentid, storeid });

		if (organList != null && organList.size() > 0)
		{
			return organList.get(0);
		}
		return null;
	}

	@Override
	public PosBill getOrderByOrdercode(String tenancyId, int storeId, String orderCode) throws Exception
	{
		String sql = "select bill_num,table_code,deinner_number as guest,order_code as order_num,total_money as bill_amount,coalesce(shop_fee,0) as payment_amount,0 as difference,(case when order_type='ZT01' then 'WD02' when order_type='WM02' then 'WS03' when order_type='DN03' then 'TS01' end) as sale_mode,chanel as source,service_id,(CASE WHEN chanel = 'WX02' THEN meal_costs::NUMERIC ELSE shop_delivery_fee END ) as service_amount,discount_amount,discount_rate,remark,shop_real_amount,platform_charge_amount,is_online_payment,settlement_type,check_mode,delivery_party,need_invoice,take_meal_number, discount_mode_id, customer_id, consigner_phone,send_time,order_state,package_box_fee,third_order_code from cc_order_list where tenancy_id=? and store_id=? and order_code=?";
		List<PosBill> ret = this.jdbcTemplate.query(sql, new Object[]
		{ tenancyId, storeId, orderCode }, BeanPropertyRowMapper.newInstance(PosBill.class));
		if (ret != null && ret.size() > 0)
		{
			return ret.get(0);
		}
		else
		{
			return null;
		}
	}

	@Override
	public OrderState getOrderStateByOrderCode(String tenancyId, int storeId, String orderCode) throws Exception
	{
		String sql = new String(
				//"select order_code,bill_num,order_state,order_type,chanel,third_order_code,receive_time,take_time,receive_time_qd,dispatch_time,receive_time_dispatch,distribution_time,receive_time_distribution,finish_time,receive_time_finish,receive_time_cancellation,is_online_payment,need_invoice,actual_pay from cc_order_list where tenancy_id=? and store_id=? and order_code=?");
				"select order_code,bill_num,order_state,order_type,chanel,third_order_code,single_time,receive_time," +
						"take_time,receive_time_qd,dispatch_time,receive_time_dispatch,distribution_time," +
						"receive_time_distribution,finish_time,receive_time_finish,receive_time_cancellation," +
						"is_online_payment,need_invoice,actual_pay,refund_recive_time,refund_oper_time," +
						"refund_operator,coalesce(send_time,'立即配送') send_time,third_order_code from cc_order_list where " +
						"tenancy_id=? and store_id=? and order_code=?");
		
		List<OrderState> ret = this.jdbcTemplate.query(sql, new Object[]
		{ tenancyId, storeId, orderCode }, BeanPropertyRowMapper.newInstance(OrderState.class));
		if (ret != null && ret.size() > 0)
		{
			return ret.get(0);
		}
		else
		{
			return null;
		}
	}

	@Override
	public List<PosBillItem> getOrderItemByOrdercode(String tenancyId, int storeId, String orderCode, String chanel) throws Exception
	{
		StringBuilder sql = new StringBuilder();
		sql.append(" select * from (select ol.tenancy_id,ol.store_id,ol.order_code,oid.group_index as item_serial,oid.item_id,oid.unit_id as unit_id,oid.number as item_count,oid.price as item_price,coalesce(oid.product_fee,0) as product_fee,COALESCE (iu.unit_name, oid.unit_name) AS item_unit_name,ii.item_code as item_num,coalesce(it.item_name,oid.item_name) as item_name,it.details_id,oid.item_id as setmeal_id,oid.group_index || '' as setmeal_rwid,'0' as waitcall_tag,(case when ii.is_combo='Y' then 'SETMEAL' else 'SINGLE' end) as item_property,(case when order_type='ZT01' then 'WD02' when order_type='WM02' then 'WS03' when order_type='DN03' then 'TS01' end) as sale_mode,(case when oid.is_gift='Y' then 'FS02' else '' end) as item_remark, '0' as assist_item_id, oid. NUMBER as assist_num ,0 as method_money,oid.activity_id,oid.activity_rule_id,oid.activity_count,oid.vip_price,oid.cart_name");
		sql.append(" from cc_order_item oid");
		sql.append(" inner join cc_order_list ol on ol.order_code=oid.order_code");
		sql.append(" left join hq_item_unit iu on oid.unit_id=iu.id");
		sql.append(" left join hq_item_info ii on oid.item_id=ii.id");
		sql.append(" left join (select imd.id as details_id,imd.item_menu_id,imd.item_id,imc.chanel,imc.class as item_class,imc.item_name,imo.store_id");
		sql.append(" from hq_item_menu_details imd");
		sql.append(" inner join hq_item_menu_class imc on imd.id=imc.details_id");
		sql.append(" inner join hq_item_menu im on im.id=imd.item_menu_id and im.valid_state='1'");
		sql.append(" inner join hq_item_menu_organ imo on imd.item_menu_id=imo.item_menu_id");
		sql.append(" ) it on oid.item_id=it.item_id and it.chanel = ?");
		sql.append(" union select ol.tenancy_id,ol.store_id,ol.order_code,oid.group_index as item_serial,oid.item_id,oid.unit_id as unit_id,oid.number as item_count,oid.price as item_price,oid. NUMBER*oid.price as product_fee,COALESCE (iu.unit_name,oid.unit_name) AS item_unit_name,ii.item_code as item_num,'   ' || coalesce(it.item_name,oid.item_name) as item_name,it.details_id,oi.item_id as setmeal_id,oi.group_index||'' as setmeal_rwid,'0' as waitcall_tag,'MEALLIST' as item_property,(case when order_type='ZT01' then 'WD02' when order_type='WM02' then 'WS03' when order_type='DN03' then 'TS01' end) as sale_mode,(case when oi.is_gift='Y' then 'FS02' else '' end) as item_remark,oid.\"id\" as assist_item_id,oid. NUMBER as assist_num,COALESCE(oid.method_money,0.0000),oi.activity_id,oi.activity_rule_id,oi.activity_count,oi.vip_price,oi.cart_name");
		sql.append(" from cc_order_item_details oid");
		sql.append(" inner join cc_order_item oi on oi.group_index=oid.group_index and oi.order_code=oid.order_code");
		sql.append(" inner join cc_order_list ol on ol.order_code=oi.order_code");
		sql.append(" left join hq_item_unit iu on oid.unit_id=iu.id");
		sql.append(" left join hq_item_info ii on oid.item_id=ii.id");
		sql.append(" left join (select imd.id as details_id,imd.item_menu_id,imd.item_id,imc.chanel,imc.class as item_class,imc.item_name,imo.store_id");
		sql.append(" from hq_item_menu_details imd");
		sql.append(" inner join hq_item_menu_class imc on imd.id=imc.details_id");
		sql.append(" inner join hq_item_menu im on im.id=imd.item_menu_id and im.valid_state='1'");
		sql.append(" inner join hq_item_menu_organ imo on imd.item_menu_id=imo.item_menu_id");
		sql.append(" ) it on oid.item_id=it.item_id and it.chanel = 'MD01') item");
		sql.append(" where item.tenancy_id=? and item.store_id=? and item.order_code=? order by item_serial,setmeal_rwid,assist_item_id");

		List<PosBillItem> resultList = this.jdbcTemplate.query(sql.toString(), new Object[]
		{ chanel, tenancyId, storeId, orderCode }, BeanPropertyRowMapper.newInstance(PosBillItem.class));

		String tasteMethodSql = "select (case when 'ZF01'=it.type then coalesce(im.id,0) else it.taste_method_id end) as taste_method_id,it.item_remark,it.type,it.item_id,it.group_index from cc_order_item_taste it left join hq_item_method im on it.item_id=im.item_id and it.taste_method_id=im.method_name_id where it.order_code=? order by it.type";
		List<JSONObject> tasteMethodList = this.query4Json(tenancyId, tasteMethodSql, new Object[]
		{ orderCode });

		for (PosBillItem item : resultList)
		{
			StringBuilder tasteMethod = new StringBuilder();
			List<JSONObject> tasteList = new ArrayList<JSONObject>();
			List<JSONObject> methodList = new ArrayList<JSONObject>();
			for (JSONObject json : tasteMethodList)
			{
				if ("KW02".equals(json.optString("type")) && item.getItem_id().equals(json.optString("item_id")) && item.getItem_serial().equals(json.optString("group_index")))
				{
					JSONObject taste = new JSONObject();
					if (json.optInt("taste_method_id") == 0)
					{
						throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_TASTE_DISH);
					}
					taste.put("id", json.optString("taste_method_id"));
					taste.put("name", json.optString("item_remark"));
					tasteMethod.append(json.optString("item_remark")).append(" ");
					tasteList.add(taste);
				}

				if ("ZF01".equals(json.optString("type")) && item.getItem_id().equals(json.optString("item_id")) && item.getItem_serial().equals(json.optString("group_index")))
				{
					JSONObject method = new JSONObject();
					if (json.optInt("taste_method_id") == 0)
					{
						throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_METHOD_DISH);
					}
					method.put("method_id", json.optString("taste_method_id"));
					method.put("method_name", json.optString("item_remark"));
					tasteMethod.append(json.optString("item_remark")).append(" ");
					methodList.add(method);
				}
			}
			item.setTaste(tasteList);
			item.setMethod(methodList);
			item.setItem_taste(tasteMethod.toString().trim());
		}

		// String
		// tasteSql="select taste_method_id as id,item_remark as name from cc_order_item_taste where type='kw02' and order_code=? and item_id=? and group_index=?";
		// String
		// methodSql="select taste_method_id as method_id,item_remark as method_name from cc_order_item_taste where type='zf01' and order_code=? and item_id=? and group_index=?";
		// for(PosBillItem item:resultList)
		// {
		// item.setTaste(this.query4Json(tenancyId, tasteSql, new Object[]{}));
		// item.setMethod(this.query4Json(tenancyId, methodSql, new
		// Object[]{}));
		// }

		return resultList;
	}

	@Override
	public List<PosBillPayment> getOrderPaymentByOrdercode(String tenancyId, int storeId, String orderCode) throws Exception
	{
		String sql = "select payment_id as jzid,pay_money as amount,'1' as count,pay_no as number,	pay_money as currency_amount,third_bill_code as bill_code from cc_order_repayment where order_code=?";
		return this.jdbcTemplate.query(sql, new Object[]
		{ orderCode }, BeanPropertyRowMapper.newInstance(PosBillPayment.class));

	}

	@Override
	public boolean updateOrderInfo(String tenancyId, int storeId, JSONObject para) throws Exception
	{
		StringBuilder sql = new StringBuilder();
		StringBuilder condition = new StringBuilder();
		int ret = 0;

		Set<?> keySet = para.keySet();
		for (Object obj : keySet)
		{
			String key = String.valueOf(obj);
			//if ("bill_num".equals(key) || "payment_state".equals(key) || "order_state".equals(key) || "cancle_name".equals(key) || "last_operator".equals(key) || "upload_tag".equals(key))
			if ("bill_num".equals(key) || "payment_state".equals(key) || "order_state".equals(key) || "cancle_name".equals(key) || "last_operator".equals(key) || "refund_operator".equals(key) || "order_state_desc".equals(key)  || "upload_tag".equals(key)  || "print_remind".equals(key))
			{
				sql.append(key).append(" = '").append(para.optString(key)).append("',");
			}
			else if ("actual_pay".equals(key))
			{
				sql.append(key).append(" = ").append(para.optDouble(key)).append(",");
			}
//			else if ("single_time".equals(key) || "receive_time".equals(key) || "take_time".equals(key) || "receive_time_qd".equals(key) || "dispatch_time".equals(key) || "receive_time_dispatch".equals(key) || "distribution_time".equals(key) || "receive_time_distribution".equals(key)
//					|| "finish_time".equals(key) || "receive_time_finish".equals(key) || "cancellation_time".equals(key) || "receive_time_cancellation".equals(key) || "last_updatetime".equals(key))
//			{
			else if ("single_time".equals(key) || "receive_time".equals(key) || "take_time".equals(key) || "receive_time_qd".equals(key) || "dispatch_time".equals(key) || "receive_time_dispatch".equals(key) || "distribution_time".equals(key) || "receive_time_distribution".equals(key)
					|| "finish_time".equals(key) || "receive_time_finish".equals(key) || "cancellation_time".equals(key) || "receive_time_cancellation".equals(key) || "last_updatetime".equals(key) || "refund_recive_time".equals(key) || "refund_oper_time".equals(key))
			{	
				if (null != DateUtil.formatTimestamp(para.optString(key)))
				{
					sql.append(key).append(" = '").append(DateUtil.formatTimestamp(para.optString(key))).append("',");
				}
				else
				{
					sql.append(key).append(" = null,");
				}

			}
			else if ("id".equals(key) || "order_code".equals(key))
			{
				if ("id".equals(key))
				{
					condition.append(" and ").append(key).append(" = ").append(para.optInt(key));
				}
				else
				{
					condition.append(" and ").append(key).append(" = '").append(para.optString(key)).append("'");
				}
			}
		}

		if (sql.length() > 0)
		{

			sql.setLength(sql.length() - 1);
			sql.insert(0, "update cc_order_list set ");

			sql.append(" where tenancy_id='").append(tenancyId).append("' and store_id=").append(storeId).append(condition.toString());

			ret = this.jdbcTemplate.update(sql.toString());
		}
		return ret > 0;
	}

	@Override
	public List<OrderState> getOrderStateForNotUpload(String tenancyId, int storeId) throws Exception
	{
		String sql = new String(
				"select order_code,bill_num,order_state,receive_time,take_time,receive_time_qd,dispatch_time,receive_time_dispatch,distribution_time,receive_time_distribution,finish_time,receive_time_finish,receive_time_cancellation from cc_order_list where tenancy_id=? and store_id=? and (order_state =? or order_state =?) and upload_tag<>'1'");
		return this.jdbcTemplate.query(sql, new Object[]
		{ tenancyId, storeId, Constant.ORDER_STATE_CANCEL, Constant.ORDER_STATE_COMPLETE }, BeanPropertyRowMapper.newInstance(OrderState.class));
	}

	@Override
	public boolean addOrderReasonDetail(String tenancyId, int storeId, String orderCode, List<JSONObject> param) throws Exception
	{
		List<JSONObject> jsonList = new ArrayList<JSONObject>();
		for (JSONObject json : param)
		{
            json.put("tenancy_id", tenancyId);
            json.put("order_code", orderCode);
            jsonList.add(json);
		}
		return this.insertBatchIgnorCase(tenancyId, "cc_order_reason_detail", jsonList).length > 0;
	}

	@Override
	public boolean deleteOrderByOrderCode(String tenancyId, int storeId, String orderCode) throws Exception
	{
		StringBuilder deleteOrderItemTaste = new StringBuilder("delete from cc_order_item_taste");
		deleteOrderItemTaste.append(" where tenancy_id='").append(tenancyId).append("' and order_code='").append(orderCode).append("'");

		StringBuilder deleteOrderItemDetails = new StringBuilder("delete from cc_order_item_details");
		deleteOrderItemDetails.append(" where tenancy_id='").append(tenancyId).append("' and order_code='").append(orderCode).append("'");

		StringBuilder deleteOrderItem = new StringBuilder("delete from cc_order_item");
		deleteOrderItem.append(" where tenancy_id='").append(tenancyId).append("' and order_code='").append(orderCode).append("'");

		StringBuilder deleteOrderRepayment = new StringBuilder("delete from cc_order_repayment");
		deleteOrderRepayment.append(" where tenancy_id='").append(tenancyId).append("' and order_code='").append(orderCode).append("'");

		StringBuilder deleteOrderList = new StringBuilder("delete from cc_order_list");
		deleteOrderList.append(" where tenancy_id='").append(tenancyId).append("' and store_id=").append(storeId).append(" and order_code='").append(orderCode).append("'");

		return this.jdbcTemplate.batchUpdate(new String[]
		{ deleteOrderItemTaste.toString(), deleteOrderItemDetails.toString(), deleteOrderItem.toString(), deleteOrderRepayment.toString(), deleteOrderList.toString() }).length > 0;
	}

	@SuppressWarnings("unchecked")
	@Override
	public boolean addOrderInfo(String tenancyId, int storeId, JSONObject param) throws Exception
	{
		if (param.containsKey("order_list") && !param.optJSONObject("order_list").isEmpty())
		{
			this.insertIgnorCase(tenancyId, "cc_order_list", param.optJSONObject("order_list"));
		}

		if (param.containsKey("order_item") && !param.optJSONArray("order_item").isEmpty())
		{
			this.insertBatchIgnorCase(tenancyId, "cc_order_item", param.optJSONArray("order_item"));
		}

		if (param.containsKey("order_item_details") && !param.optJSONArray("order_item_details").isEmpty())
		{
			this.insertBatchIgnorCase(tenancyId, "cc_order_item_details", param.optJSONArray("order_item_details"));
		}

		if (param.containsKey("order_item_taste") && !param.optJSONArray("order_item_taste").isEmpty())
		{
			this.insertBatchIgnorCase(tenancyId, "cc_order_item_taste", param.optJSONArray("order_item_taste"));
		}

		if (param.containsKey("order_repayment") && !param.optJSONArray("order_repayment").isEmpty())
		{
			this.insertBatchIgnorCase(tenancyId, "cc_order_repayment", param.optJSONArray("order_repayment"));
		}
		
		if (param.containsKey("cc_order_discount") && !param.optJSONArray("cc_order_discount").isEmpty())
		{
			this.insertBatchIgnorCase(tenancyId, "cc_order_discount", param.optJSONArray("cc_order_discount"));
		}

        if (param.containsKey("order_credit") && !param.optJSONArray("order_credit").isEmpty())
        {
            this.insertBatchIgnorCase(tenancyId, "cc_order_credit", param.optJSONArray("order_credit"));
        }
		return true;
	}

	@Override
	public List<JSONObject> getPosBillItemByBillNum(String tenancyId, int storeId, String billNum) throws Exception
	{
		StringBuilder sql = new StringBuilder("select rwid,item_count as count from pos_bill_item where bill_num = ? and store_id = ? and item_remark is null and item_property <>'MEALLIST'");
		return this.query4Json(tenancyId, sql.toString(), new Object[]
		{ billNum, storeId });
	}

	@Override
	public boolean addOrderRepayment(String tenancyId, int storeId, List<JSONObject> param) throws Exception
	{
		// TODO Auto-generated method stub
		if (param != null && param.size() > 0)
		{
			for(JSONObject json:param)
			{
				json.put("tenancy_id", tenancyId);
				json.put("store_id", storeId);
			}
			return this.insertBatchIgnorCase(tenancyId, "cc_order_repayment", param).length > 0;
		}
		else
		{
			return false;
		}

	}

	@Override
    public JSONObject getOrderCredit(String tenancyId, int storeId, String orderCode) throws Exception{
	   List<JSONObject> res= this.query4Json(tenancyId,"select * from cc_order_credit where order_code=? and store_id=?",new Object[]{orderCode,storeId});
	   if(null==res||res.isEmpty()){
	       return null;
       }
       return res.get(0);
    }
	
	
	@Override
	public JSONObject getBillByOrderCode(String tenancyId, int storeId, String orderCode) throws Exception
	{
		
		// 查询账单
		String querySql = "select * from pos_bill where tenancy_id = ? and store_id = ? and order_num = ? ";
		List<JSONObject> billList = this.query4Json(tenancyId, querySql, new Object[]
		{ tenancyId, storeId,orderCode });

		if (billList != null && billList.size() > 0)
		{
			return billList.get(0);
		}
		return null;
	}
	
	@Override
	public boolean deletePosBillByBillNum(String tenancyId, int storeId,JSONObject bill) throws Exception
	{   
		// 删除 pos_bill,pos_bill_item,pos_bill_payment表相应数据
		String bill_num = bill.optString("bill_num");
		Integer id  = bill.optInt("id");
		
		StringBuilder deleteBillPayment = new StringBuilder("delete from pos_bill_payment");
		deleteBillPayment.append(" where tenancy_id='").append(tenancyId).append("' and store_id=").append(storeId).append(" and  bill_num='").append(bill_num).append("'");

		StringBuilder deleteBillItem = new StringBuilder("delete from pos_bill_item");
		deleteBillItem.append(" where tenancy_id='").append(tenancyId).append("' and store_id=").append(storeId).append(" and  bill_num='").append(bill_num).append("'");

		StringBuilder deleteBill = new StringBuilder("delete from pos_bill");
		deleteBill.append(" where id=").append(id);


		return this.jdbcTemplate.batchUpdate(new String[]
		{ deleteBillPayment.toString(), deleteBillItem.toString(), deleteBill.toString()}).length > 0;
	}
	
	public List<JSONObject>  getIdByOrderCode(String tenancyId, int storeId, String tableName,String orderCode) throws Exception{
		// 查询账单
		String querySql = "select * from "+tableName+" where tenancy_id = ? and store_id = ? and order_code = ? ";
		List<JSONObject> orderList = this.query4Json(tenancyId, querySql, new Object[]
		{ tenancyId, storeId,orderCode });

		if (orderList != null && orderList.size() > 0)
		{
			return orderList;
		}
		return null;
	}
	
	@Override
	public OrderState getRefundOrderStateByOrderCode(String tenancyId, int storeId, String orderCode) throws Exception
	{
		String sql = new String(
				"select order_code,bill_num,order_state,order_type,chanel,third_order_code,single_time,receive_time,take_time,receive_time_qd,dispatch_time,receive_time_dispatch,distribution_time,receive_time_distribution,finish_time,receive_time_finish,receive_time_cancellation,is_online_payment,need_invoice,actual_pay,refund_recive_time,refund_oper_time,refund_operator,refund_type from cc_order_list where tenancy_id=? and store_id=? and order_code like '%"+orderCode+"%' and order_state='21' and refund_type in ('1','2') ");
		List<OrderState> ret = this.jdbcTemplate.query(sql, new Object[]
		{ tenancyId, storeId}, BeanPropertyRowMapper.newInstance(OrderState.class));
		if (ret != null && ret.size() > 0)
		{
			return ret.get(0);
		}
		else
		{
			return null;
		}
	}
	
	
	@Override
	public JSONObject getBillByOrderCode(String tenancyId, int storeId, String orderCode,String bill_state) throws Exception
	{
		
		// 查询账单
		String querySql = "select * from v_pos_bill where tenancy_id = ? and store_id = ? and order_num = ? ";
		if(bill_state!=null){
			querySql+=" and bill_state = '"+bill_state+"'";
		}
		List<JSONObject> billList = this.query4Json(tenancyId, querySql, new Object[]
		{ tenancyId, storeId,orderCode });

		if (billList != null && billList.size() > 0)
		{
			return billList.get(0);
		}
		return null;
	}
	
	@Override
	public boolean existPaymentWay(String tenancyId, int storeId,String order_code) throws Exception{
		boolean result = true;
		String querySql = "SELECT pw.id from cc_order_repayment as ccr  INNER JOIN payment_way as pw  on  pw.id =ccr.payment_id where ccr.order_code=?";
		List<JSONObject> paymentWay = this.query4Json(tenancyId, querySql, new Object[]{order_code});

		if (paymentWay == null || paymentWay.size() == 0)
		{
			result = false;
		}
		return result;
	}
	
	@Override
	public JSONObject getBillByBillNum(String tenancyId, int storeId, String bill_num) throws Exception
	{
		
		// 查询账单
		String querySql = "select * from pos_bill where tenancy_id = ? and store_id = ? and bill_num = ? ";
		List<JSONObject> billList = this.query4Json(tenancyId, querySql, new Object[]
		{ tenancyId, storeId,bill_num });

		if (billList != null && billList.size() > 0)
		{
			return billList.get(0);
		}
		return null;
	}

	
	@Override
	public List<JSONObject> getLocalItemChanel(String tenancyId, int storeId, JSONObject para) throws Exception
	{
		StringBuilder sql = new StringBuilder(" SELECT clazz.tenancy_id,clazz.chanel,dic.id,dic.class_item from hq_item_class clazz LEFT JOIN sys_dictionary dic on clazz.chanel = dic.class_item_code where dic.class_identifier_code='chanel' and dic.valid_state='1' and clazz.tenancy_id=?  GROUP BY clazz.tenancy_id,clazz.chanel,dic.id,dic.class_item order by dic.id ");
		return this.query4Json(tenancyId, sql.toString(),new Object[]{ tenancyId});
	}
	@Override
	public List<JSONObject> getLocalItemCate(String tenancyId, int storeId, JSONObject para) throws Exception
	{
		StringBuilder sql = new StringBuilder("SELECT clazz.id,clazz.itemclass_code,clazz.itemclass_name,clazz.father_id from hq_item_class clazz where  clazz.tenancy_id=? and clazz.chanel=? and clazz.valid_state='1'");
		return this.query4Json(tenancyId, sql.toString(),new Object[]{ tenancyId,para.optString("chanel")});
	}
	
	@Override
	public Object addBindingItemInfo(String tenancyId, int storeId, JSONObject param) throws Exception
	{
		if (param != null && param.size() > 0)
		{
			return this.insertIgnorCase(tenancyId, "binding_item_info", param);
		}
		else
		{
			return false;
		}
	}
	
	@Override	
	public boolean deleteBindingItemInfo(String tenancyId, int storeId,JSONObject param) throws Exception
	{   
		StringBuilder deleteBindingItemInfo = new StringBuilder("delete from binding_item_info");
		deleteBindingItemInfo.append(" where tenancy_id='").append(tenancyId).append("' and store_id=").append(storeId);
		if(param!=null && !param.isEmpty()){
			String order_code = param.optString("order_code");
			String cc_order_item_id  = param.optString("cc_order_item_id");
			deleteBindingItemInfo.append(" and  order_code='").append(order_code).append("'").append(" and  cc_order_item_id=").append(cc_order_item_id);
		}
		return this.jdbcTemplate.batchUpdate(new String[]{ deleteBindingItemInfo.toString()}).length > 0;
	}
	
	@Override
	public JSONObject validateBindingItem(String tenancyId, int storeId, JSONObject param) throws Exception
	{
		
		// 查询账单
		String querySql = "select * from binding_item_info where tenancy_id = ? and store_id = ? and cc_order_item_id = ? ";
		List<JSONObject> bindingItem = this.query4Json(tenancyId, querySql, new Object[]
		{ tenancyId, storeId,param.optInt("cc_order_item_id") });

		if (bindingItem != null && bindingItem.size() > 0)
		{
			return bindingItem.get(0);
		}
		return null;
	}
	
	@Override
	public boolean udateOrderDelivery(String tenancyId, int storeId, String orderCode) {
		// TODO Auto-generated method stub
		try {
			String sql = "update cc_order_list set delivery_state = '07',find_state = 1 where tenancy_id = '"+tenancyId+"' and store_id = "+storeId+" and order_code = '"+orderCode+"' ";
	    	return this.execute(tenancyId, sql);
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
	}


	//更新外卖订单提醒标识
	public boolean updateOrderPrintRemind(String tenancyId, int storeId, String orderCode) throws  Exception{
		String sql = "update cc_order_list set print_remind = '1' where tenancy_id = '"+tenancyId+"' and store_id = "+storeId+" and order_code = '"+orderCode+"' ";
		return this.execute(tenancyId, sql);
	}

	@Override
	public String getEmpNameById(String tenancyId, Integer storeId, String optNum) throws Exception
	{
		if (false == StringUtils.isNumeric(optNum))
		{
			return optNum;
		}

		StringBuilder sbSql = new StringBuilder();
		sbSql.append(" select em.id,em.name from employee as em where em.tenancy_id = ? and em.store_id = ? and em.id = ? ");
		SqlRowSet rs = this.jdbcTemplate.queryForRowSet(sbSql.toString(), new Object[]
		{ tenancyId, storeId, Integer.parseInt(optNum) });

		String empName = "";
		if (rs.next())
		{
			empName = rs.getString("name").trim();
		}
		else
		{
			empName = optNum;
		}
		return empName;
	}

	@Override
	public boolean updateBillItem(String tenancyId, String billNum){
		try {
			String sql = "update pos_bill_item set item_class_id = hhi.item_class ,item_num = hhi.item_code from  hq_item_info hhi  where hhi.id = item_id and bill_num = '"+billNum+"'";
			return this.execute(tenancyId, sql);
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
	}

	@Override
	public boolean settingTakeOrderFlag(String tenancyId, int storeId, String orderCode) throws  Exception{
		String sql = "update cc_order_list set is_taking_order = NULL where tenancy_id = '"+tenancyId+"' and store_id = "+storeId+" and order_code = '"+orderCode+"' ";
		return this.execute(tenancyId, sql);
	}

	@Override
	public boolean getTakeOrderFlag(String tenancyId, int storeId, String orderCode) throws  Exception{
		boolean result = false;
		String sql = "select id from cc_order_list  where tenancy_id = '"+tenancyId+"' and store_id = "+storeId+" and order_code = '"+orderCode+"' and  is_taking_order = 'taking'";
		List<JSONObject> list =  this.query4Json(tenancyId,sql);
		if(null!= list && list.size()>0){
			result = true;
		}
		return result;
	}

	@Override
	public List<JSONObject> findPaymentWayByChanel(String tenancyId, int storeId,String chanel) throws Exception{
		List<JSONObject> paymentJson = new ArrayList<>();
		if(null!=chanel && !"".equals(chanel)){
			String payment_class = null;
			switch (chanel){
				case Constant.WX02_CHANNEL: //微信外卖
					payment_class = "wechat_order_pay";
					break;
				case Constant.MT11_CHANNEL: //新美大
				case Constant.MT08_CHANNEL: //美团
					payment_class = "meituan_pay";
					break;
				case Constant.EL09_CHANNEL: //饿了么
					payment_class = "ele_pay";
					break;
				case Constant.CC04_CHANNEL: //电话
					payment_class = "talk_order_pay";
					break;
				case Constant.DJ_CHANNEL: //货到付款
					payment_class = "home_order_pay";
					break;
				case SysDictionary.CHANEL_FS25: //丰食
					payment_class = SysDictionary.PAYMENT_CLASS_FENGSHI_PAY;
					break;
				case SysDictionary.CHANEL_JD31: //京东秒送
					payment_class = SysDictionary.PAYMENT_CLASS_JINGDONG_PAY;
					break;
				default:
					break;
			}

			if(null!= payment_class){
				StringBuilder sql = new StringBuilder();
				sql.append(" SELECT id as jzid,payment_class,payment_name1 as name,if_income from payment_way where payment_class= '"+payment_class+"' and tenancy_id='"+tenancyId+"'");
				paymentJson =  this.query4Json(tenancyId, sql.toString());
				return paymentJson;
			}
		}else{
			return paymentJson;
		}
		return paymentJson;
	}

	@Override
	public List<JSONObject> findPosBillExtendsByBillNum(String tenancyId, int storeId,String bill_num) throws Exception{
		StringBuilder sql = new StringBuilder();
		sql.append(" SELECT id,bill_num,eco_print_msg from pos_bill_extends where bill_num= '"+bill_num+"' and tenancy_id='"+tenancyId+"' and store_id="+storeId);
		List<JSONObject> list =  this.query4Json(tenancyId, sql.toString());
		return list;
	}

	@Override
	public PosBill getBillByOrderNum(String tenancyId, int storeId, String orderCode) throws Exception
	{
		String sql = "select * from pos_bill where tenancy_id=? and store_id=? and order_num=?";
		List<PosBill> ret = this.jdbcTemplate.query(sql, new Object[]
				{ tenancyId, storeId, orderCode }, BeanPropertyRowMapper.newInstance(PosBill.class));
		if (ret != null && ret.size() > 0)
		{
			return ret.get(0);
		}
		else
		{
			return null;
		}
	}
}
