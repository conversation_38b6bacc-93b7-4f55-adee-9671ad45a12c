package com.tzx.pos.bo.dto;

import java.io.Serializable;

/**
 * TODO
 * <AUTHOR> 
 * @日期：2015年5月18日-下午5:37:18
 */

public class BasicDataUnusualReason implements Serializable
{
	/**
	 * 
	 */
	private static final long	serialVersionUID	= -2153629467301926120L;
	
	private String id;
	private String reason_code;
	private String unusual_type;
	private String father_id;
	private String reason_name;
	private String phonetic_code;
	private String five_code;
	private String remark;
	private String valid_state;
	private String third_code;
	
	public String getId()
	{
		return id;
	}
	public void setId(String id)
	{
		this.id = id;
	}
	public String getReason_code()
	{
		return reason_code;
	}
	public void setReason_code(String reason_code)
	{
		this.reason_code = reason_code;
	}
	public String getUnusual_type()
	{
		return unusual_type;
	}
	public void setUnusual_type(String unusual_type)
	{
		this.unusual_type = unusual_type;
	}
	public String getFather_id()
	{
		return father_id;
	}
	public void setFather_id(String father_id)
	{
		this.father_id = father_id;
	}
	public String getReason_name()
	{
		return reason_name;
	}
	public void setReason_name(String reason_name)
	{
		this.reason_name = reason_name;
	}
	public String getPhonetic_code()
	{
		return phonetic_code;
	}
	public void setPhonetic_code(String phonetic_code)
	{
		this.phonetic_code = phonetic_code;
	}
	public String getFive_code()
	{
		return five_code;
	}
	public void setFive_code(String five_code)
	{
		this.five_code = five_code;
	}
	public String getRemark()
	{
		return remark;
	}
	public void setRemark(String remark)
	{
		this.remark = remark;
	}
	public String getValid_state()
	{
		return valid_state;
	}
	public void setValid_state(String valid_state)
	{
		this.valid_state = valid_state;
	}
	public String getThird_code()
	{
		return third_code;
	}
	public void setThird_code(String third_code)
	{
		this.third_code = third_code;
	}
}
