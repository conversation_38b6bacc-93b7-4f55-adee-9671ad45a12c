package com.tzx.pos.base.dao.imp;

import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.DoubleHelper;
import com.tzx.framework.common.util.JsonUtils;
import com.tzx.framework.common.util.Scm;
import com.tzx.framework.common.util.Tools;
import com.tzx.framework.common.util.dao.datasource.MultiDataSourceManager;
import com.tzx.framework.common.util.dao.impl.GenericDaoImpl;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.FunctionCode;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.dao.BaseDao;
import com.tzx.pos.base.print.NewQueueUtil;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.bo.dto.PosLog;

import net.sf.json.JSONObject;

/**
 * 
 * <AUTHOR>
 * @日期：2015年5月26日-下午5:34:43
 */
@EnableAsync
public class BaseDaoImp extends GenericDaoImpl implements BaseDao
{
	private static final Logger	logger	= Logger.getLogger(BaseDaoImp.class);

	/**
	 * 厨打
	 * 
	 * @param tenancyid
	 *            机构id
	 * @param billno
	 *            账单编号
	 * @param print_format
	 *            这个传进来是否是整单或大类
	 * @param organId
	 *            门店id
	 * @throws Exception
	 */
	@Deprecated
	public synchronized void chefPrint(String tenancyid, String billno, String print_format, Integer organId) throws Exception
	{
		/**
		 * 下面是放入消息对列，进行菜品打印 如果菜品是单切的话，直接就放到对列里；如果打印类型是单切或整单，就按类型和账单发一次请求
		 * print_format打印格式 1106 单切 1105大类 1107整台
		 * 
		 */
		StringBuilder sql1108 = new StringBuilder(
				"select ppi.rwid,ppi.bill_num,ppi.printer_id,ppi.printer_name,ppi.print_code,ppi.print_tag,ppi.print_format from pos_print_item ppi where ppi.rwid in (select min(pb.rwid) from pos_print_item pb where pb.bill_num = ? and pb.print_tag = '*' and pb.print_format=?) and ppi.bill_num = ? and ppi.print_format=? and ppi.store_id = ? order by ppi.rwid asc limit 1");

		StringBuilder sql = new StringBuilder(
				"select ppi.rwid,ppi.bill_num,ppi.printer_id,ppi.printer_name,ppi.print_code,ppi.print_tag,ppi.print_format,ppi.print_property from pos_print_item ppi where ppi.bill_num = ? and ppi.print_tag = '*' and ppi.queue_tag = '*' and ppi.store_id = ? order by ppi.rwid asc");
		// 单切更新
		String usql = new String("update pos_print_item set queue_tag = ? where rwid = ? and print_property=? and bill_num = ? and store_id = ? ");
		// 大类或整单更新
		String ucsql = new String("update pos_print_item set queue_tag = ? where print_format = ? and print_property = ? and bill_num = ? and store_id = ? ");

		if (print_format.equals("1106"))
		{
			/**
			 * 把单切数据放到菜品对列里
			 */
			Object[] objs = new Object[]
			{ billno, organId };
			List<JSONObject> list = this.queryString4Json(tenancyid, sql.toString(), objs);

			List<JSONObject> list1105 = new ArrayList<JSONObject>();
			Set<Integer> set = new HashSet<Integer>();

			boolean isWhole = false;

			for (int i = 0; i < list.size(); i++)
			{
				JSONObject obj = list.get(i);
				String fomat = obj.optString("print_format");

				if ("1105".equals(fomat))
				{
					set.add(obj.optInt("printer_id"));
					list1105.add(obj);
				}

				if ("1107".equals(fomat))
				{
					if (!isWhole)
					{
						JSONObject dishA = ParamUtil.setDishParam(tenancyid, billno, obj.optString("print_code"), obj.optString("print_format"), obj.optInt("rwid"), obj.optInt("printer_id"), organId, obj.optString("print_property"));
						logger.debug("整单::::" + dishA.toString());
						NewQueueUtil.push(dishA);
						this.update(ucsql, new Object[]
						{ null, dishA.getString("print_format"), dishA.getString("print_property"), billno, organId });
						isWhole = true;
					}
				}

				if ("1106".equals(fomat))
				{
					JSONObject dishA = ParamUtil.setDishParam(tenancyid, billno, obj.optString("print_code"), obj.optString("print_format"), obj.optInt("rwid"), obj.optInt("printer_id"), organId, obj.optString("print_property"));
					logger.debug("单切::::" + dishA.toString());
					NewQueueUtil.push(dishA);
					this.update(usql, new Object[]
					{ null, dishA.getInt("rwid"), dishA.getString("print_property"), billno, organId });
				}
			}
			String ubcsql = new String("update pos_print_item set queue_tag = ? where printer_id = ? and print_property = ? and bill_num = ? and store_id = ? ");
			for (Integer it : set)
			{
				boolean isBc = false;
				for (int i = 0; i < list1105.size(); i++)
				{
					JSONObject objB = list1105.get(i);
					if (it == objB.optInt("printer_id"))
					{
						if (!isBc)
						{
							JSONObject dishB = ParamUtil.setDishParam(tenancyid, billno, objB.optString("print_code"), objB.optString("print_format"), objB.optInt("rwid"), objB.optInt("printer_id"), organId, objB.optString("print_property"));
							logger.debug("大类::::" + dishB.toString());
							NewQueueUtil.push(dishB);
							this.update(ubcsql, new Object[]
							{ null, dishB.getInt("printer_id"), dishB.getString("print_property"), billno, organId });
							isBc = true;
						}
					}
				}
			}
		}

		if (print_format.equals("1108"))
		{
			/**
			 * 把数据放到菜品对列里
			 */
			try
			{
				Object[] objs = new Object[]
				{ billno, print_format, billno, print_format, organId };
				List<JSONObject> list1108 = this.queryString4Json(tenancyid, sql1108.toString(), objs);

				for (int i = 0; i < list1108.size(); i++)
				{
					JSONObject objD = list1108.get(i);
					JSONObject dishD = ParamUtil.setDishParam(tenancyid, billno, objD.optString("print_code"), objD.optString("print_format"), objD.optInt("rwid"), objD.optInt("printer_id"), organId, objD.optString("print_property"));
					logger.debug("1108::::" + dishD.toString());
					this.update(usql, new Object[]
					{ null, dishD.getInt("rwid"), dishD.getString("print_property"), billno, organId });
					NewQueueUtil.push(dishD);
				}
			}
			catch (Exception e)
			{
				logger.error("转台出错，原因：" + ExceptionMessage.getExceptionMessage(e));
				e.printStackTrace();
			}
		}
	}

	/**
	 * 账单打印
	 * 
	 * @param tenancyid
	 *            机构id
	 * @param billno
	 *            账单编号
	 * @param posNum
	 *            机台编号
	 * @param print_code
	 *            打印模板code
	 * @param printType
	 *            打印类型 1本人本机交班打印 2本人所有机台号 3所有人所有机台号 （3暂时不实现）
	 * @param organId
	 *            门店id mode 0 打印菜品，1打印会员服务
	 * @param isInvoice
	 *            是否打印电子发票
	 * @param strUrlPath
	 *            电子发票二维码存放路径
	 * @param strUrlContent
	 *            电子发票二维码URL     
	 */
	public void billPrint(String mode, String tenancyid, Integer organId, String billno, String posNum, String optNum, Date reportDate, Integer shiftId, String printCode, String printType, String dateRemark, String paymentUrl, String isInvoice, String strUrlPath, String strUrlContent,String orderRemark,String qrcode,String qrcodeUrl)
	{
		Integer printer = null;

		try
		{
			if (SysDictionary.PRINT_CODE_1101.equals(printCode))
			{
				String pa = this.getSysParameter(tenancyid, organId, "QYDYSFDYYDD");
				if ("1".equals(pa))
				{
					String qPrinter = new String("select ti.printer from tables_info ti join pos_bill pb on ti.table_code=pb.table_code where pb.bill_num=? and ti.valid_state='1' and ti.organ_id=? and ti.tenancy_id=?");
					SqlRowSet rsPrinter = this.query4SqlRowSet(qPrinter, new Object[]
					{ billno, organId, tenancyid });
					if (rsPrinter.next())
					{
						printer = rsPrinter.getInt("printer");
					}
				}
			}
			
			if (SysDictionary.PRINT_CODE_1103.equals(printCode))
			{
				String pa1 = this.getSysParameter(tenancyid, organId, "QYDYSFDYDCD");
				if ("1".equals(pa1))
				{
					String qPrinter = new String("select ti.printer from tables_info ti join pos_bill pb on ti.table_code=pb.table_code where pb.bill_num=? and ti.valid_state='1' and ti.organ_id=? and ti.tenancy_id=?");
					SqlRowSet rsPrinter = this.query4SqlRowSet(qPrinter, new Object[]
					{ billno, organId, tenancyid });
					if (rsPrinter.next())
					{
						printer = rsPrinter.getInt("printer");
					}
				}
			}

			if (Tools.isNullOrEmpty(printer) || printer == 0)
			{
				String device = new String("select printer from hq_devices where devices_code = ? and valid_state='1' and store_id = ?");
				SqlRowSet rs = this.query4SqlRowSet(device, new Object[]
				{ posNum, organId });

				if (rs.next())
				{
					printer = rs.getInt("printer");
				}
			}

			// 添加信息到线程池里打印
			JSONObject msg = ParamUtil.setTempParam(mode, tenancyid, billno, printCode, printer, organId, paymentUrl);

			if(SysDictionary.PRINT_CODE_1304.equals(printCode)){ // 零找金
                msg.put("report_date", DateUtil.format(reportDate)); // 报表日期
                msg.put("shift_id", shiftId); // 班次
                msg.put("pos_num", posNum); // 机号
                msg.put("opt_num", optNum); // 收银员
            }
			
			if (SysDictionary.PRINT_CODE_1301.equals(printCode) || SysDictionary.PRINT_CODE_1302.equals(printCode))// 交班单
			{
				if ("1".equalsIgnoreCase(printType))
				{
					msg.put("opt_pos_num", posNum);
				}
				if ("2".equalsIgnoreCase(printType))
				{
					msg.put("opt_pos_num", "");
				}
				if (StringUtils.isNotEmpty(dateRemark) && dateRemark.contains("~"))
				{
					String[] arr = dateRemark.split("~");
					if (arr.length > 0)
					{
						msg.put("start_date", arr[0]);
						msg.put("end_date", arr[1]);
					}
				}
				msg.put("print_type", printType);
				msg.put("shift_id", shiftId);
				msg.put("cashier_num", optNum);
				msg.put("report_date", DateUtil.format(reportDate));
			}
			
			if (SysDictionary.PRINT_CODE_1103.equals(printCode))
			{
				msg.put("order_remark", orderRemark);
			}

			if (SysDictionary.PRINT_CODE_1101.equals(printCode))
			{
				String uPrintCount = new String("update pos_bill set print_count = (select coalesce(print_count,0)+1 as print_count from pos_bill where bill_num = ? and store_id = ?) where bill_num = ? and store_id = ? ");
				this.jdbcTemplate.update(uPrintCount, new Object[]
				{ billno, organId, billno, organId });
				
				msg.put("qrcode", Tools.hv(qrcode)?qrcode:"");
				msg.put("qrcode_url", Tools.hv(qrcodeUrl)?qrcodeUrl:"");
			}

			if (SysDictionary.PRINT_CODE_1201.equals(printCode))
			{
				String sql = new String("select order_num from pos_bill where store_id=? and bill_num=?");
				SqlRowSet rs = this.jdbcTemplate.queryForRowSet(sql, new Object[]
				{ organId, billno });
				if (rs.next())
				{
					msg.put("order_code", rs.getString("order_num"));
					msg.put("url_path", strUrlPath);
				}
			}

			if (SysDictionary.PRINT_CODE_1202.equals(printCode))
			{
				String sql = new String("select order_num from pos_bill where store_id=? and bill_num=?");
				SqlRowSet rs = this.jdbcTemplate.queryForRowSet(sql, new Object[]
				{ organId, billno });
				if (rs.next())
				{
					msg.put("order_code", rs.getString("order_num"));
				}
			}
			
			if (SysDictionary.PRINT_CODE_1110.equals(printCode) || SysDictionary.PRINT_CODE_1102.equals(printCode))
			{
				msg.put("url_path", strUrlPath);
			}
			
			// 是本地打印还是服务器打印
			if (SysDictionary.PRINT_CODE_1008.equals(printCode) 
					|| SysDictionary.PRINT_CODE_1009.equals(printCode) 
					|| SysDictionary.PRINT_CODE_1010.equals(printCode) 
					|| SysDictionary.PRINT_CODE_1011.equals(printCode) 
					|| SysDictionary.PRINT_CODE_1012.equals(printCode) 
					|| SysDictionary.PRINT_CODE_1013.equals(printCode) 
					|| SysDictionary.PRINT_CODE_1101.equals(printCode) 
					|| SysDictionary.PRINT_CODE_1102.equals(printCode) 
					|| SysDictionary.PRINT_CODE_1103.equals(printCode) 
					|| SysDictionary.PRINT_CODE_1110.equals(printCode))
			{
				String isLocalPrinter = getSysParameter(tenancyid, organId, "IsLocalPrinter");

				if (!"1".equals(isLocalPrinter))
				{
					NewQueueUtil.push(msg);
				}
			}
			else
			{
				NewQueueUtil.push(msg);
			}

		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
	}

	/**
	 * 更新discountk_amount,discountr_amount,discount_amount
	 * bill_amount,payment_amount
	 * 
	 * @param billno
	 * @throws Exception
	 */
	@Transactional(propagation = Propagation.REQUIRED)
	@Deprecated
	public void updatePosBillAmount(String tenantId, Integer organId, String billno) throws Exception
	{
		double service_amount = 0;
		double discountk_amount = 0;
		double discount_amount = 0;
		double bill_amount = 0;
		double payment_amount = 0;
		double subtotal = 0;
		double guest = 0;
		double averageAmount = 0;
		double maling_amount = 0;
		double assistMoney = 0;
		double discountrAmount = 0;
		double giviAmount = 0;

		Integer maxRwid = null;
		double maxValue = 0;
		// 从数据库里取数据小数点儿
		Integer zdj = Integer.parseInt(Constant.constantMap.get("ZDJEWS"));

		StringBuffer ubillsql = new StringBuffer("update pos_bill set subtotal=?,discountk_amount = ?,discountr_amount = ?,discount_amount = ?,bill_amount = ?,payment_amount = ?,average_amount = ?,maling_amount=? where bill_num = ? and store_id = ? ");
		StringBuilder pbisql = new StringBuilder("select rwid,item_price,discount_rate,discountr_amount,item_amount,real_amount,item_property,item_remark,discount_state,method_money,discount_amount from pos_bill_item where bill_num = ? and store_id = ?");
		StringBuilder pbsql = new StringBuilder("select givi_amount,discountr_amount,service_amount,guest,average_amount from pos_bill where bill_num = ? and store_id = ?");

		String upbisql = new String("update pos_bill_item set discount_amount = ? where bill_num = ? and rwid = ? and store_id = ?");

		SqlRowSet rs = this.jdbcTemplate.queryForRowSet(pbisql.toString(), new Object[]
		{ billno, organId });
		while (rs.next())
		{
			int rwid = rs.getInt("rwid");
			double real_amount = rs.getDouble("real_amount");
			double discount_rate = rs.getDouble("discount_rate");
			double discountr_amount = rs.getDouble("discountr_amount");
			double item_amount = rs.getDouble("item_amount");

			double discountkAmount = 0;
			double discountAmount = rs.getDouble("discount_amount");

			double method_money = rs.getDouble("method_money");
			String discount_state = rs.getString("discount_state");

			String item_property = rs.getString("item_property");
			String item_remark = rs.getString("item_remark");

			if (!item_property.equalsIgnoreCase("MEALLIST") && StringUtils.isEmpty(item_remark))
			{
				if (StringUtils.isNotEmpty(discount_state))
				{
					if (discount_state.equalsIgnoreCase("Y"))
					{
						discountkAmount = Scm.padd(discountkAmount, Scm.psub(item_amount, Scm.pdiv(Scm.pmui(item_amount, discount_rate), 100d)));
					}
				}
				if ("SETMEAL".equalsIgnoreCase(item_property))
				{
					averMeal(billno, organId, rwid);
				}

				if (maxValue < real_amount)
				{
					maxValue = real_amount;
					maxRwid = rwid;
				}
				if (discountkAmount > 0)
				{
					discountAmount = discountkAmount;
				}

				discountk_amount += discountAmount;

				assistMoney = Scm.padd(assistMoney, method_money);

				subtotal = Scm.padd(subtotal, item_amount);
				bill_amount = Scm.padd(bill_amount, item_amount);
				payment_amount = Scm.padd(payment_amount, Scm.psub(Scm.psub(item_amount, discount_amount), discountr_amount));

				this.jdbcTemplate.update(upbisql, new Object[]
				{ discountAmount, billno, rwid, organId });
			}
		}

		SqlRowSet rs2 = this.jdbcTemplate.queryForRowSet(pbsql.toString(), new Object[]
		{ billno, organId });
		while (rs2.next())
		{
			giviAmount = rs2.getDouble("givi_amount");
			discountrAmount = rs2.getDouble("discountr_amount");
			service_amount = rs2.getDouble("service_amount");
			guest = rs2.getDouble("guest");
			bill_amount = Scm.padd(bill_amount, service_amount);
			payment_amount = Scm.padd(payment_amount, service_amount);
		}
		bill_amount = Scm.padd(bill_amount, giviAmount);
		subtotal = Scm.padd(subtotal, giviAmount);
		discount_amount = Scm.padd(discountk_amount, discountrAmount);

		maling_amount = Scm.psub(DoubleHelper.sub(bill_amount, discount_amount, zdj), Scm.psub(bill_amount, discount_amount));
		// 更新账单明细的抹零金额
		String uItemAmount = new String(" update pos_bill_item set real_amount=?,single_discount_amount=? where rwid = ? and bill_num = ? and store_id = ?");
		this.update(uItemAmount, new Object[]
		{ Scm.padd(maxValue, maling_amount), maling_amount, maxRwid, billno, organId });

		payment_amount = DoubleHelper.sub(Scm.psub(bill_amount, giviAmount), discount_amount, zdj);// 真实价格里就已经把折扣去掉了
		if (guest > 0)
		{
			averageAmount = Scm.pdiv(payment_amount, guest);
		}

		this.jdbcTemplate.update(ubillsql.toString(), new Object[]
		{ subtotal, discountk_amount, discountrAmount, discount_amount, bill_amount, payment_amount, averageAmount, maling_amount, billno, organId });
	}

	@Override
	public void customerPrint(String mode, String tenancyid, String cardCode, String billCode, String posNum, String printCode, Integer organId, double credit, double useful_credit, double main_balance, double reward_balance, double total_main, double total_reward, String operator,
			String updatetime, double income, double consume_cardmoney, double deposit, double sales_price, double main_trading, double reward_trading,String payment_name,String card_class_name,String name,String mobil,String strUrlPath,String strUrlContent,String operatedate,String level_name,String original_card)
	{
		try
		{
			String device = new String("select printer from hq_devices where devices_code = ? and store_id = ?");
			SqlRowSet rs = this.query4SqlRowSet(device, new Object[]
			{ posNum, organId });
			
			Integer printer = null;

			if (rs.next())
			{
				printer = rs.getInt("printer");
			}

			// 添加信息到线程池里打印
			JSONObject msg = ParamUtil.setCusParam(mode, tenancyid, cardCode, billCode, printCode, printer, organId, credit, useful_credit, main_balance, reward_balance, total_main, total_reward, operator, updatetime, income, consume_cardmoney, deposit, sales_price, main_trading, reward_trading,payment_name,card_class_name,name,mobil,strUrlPath,strUrlContent,operatedate,level_name,original_card);

			// 是本地打印还是服务器打印
			if ("1008".equals(printCode) || "1009".equals(printCode) || "1010".equals(printCode) || "1011".equals(printCode) || "1012".equals(printCode) || "1013".equals(printCode))
			{
				String isLocalPrinter = getSysParameter(tenancyid, organId, "IsLocalPrinter");

				if (!"1".equals(isLocalPrinter))
				{
					String printCountStr = "1";
					if("1012".equals(printCode))
					{
						printCountStr =this.getSysParameter(tenancyid, organId, "MemberReceiptCount");
						if(Tools.isNullOrEmpty(printCountStr))
						{
							printCountStr = "0";
						}
					}
					Integer printCount = Integer.parseInt(printCountStr);
					if (printCount <= 0)
					{
						printCount = 1;
					}
					
					for (int i = 0; i < printCount; i++)
					{
						NewQueueUtil.push(msg);
					}
				}
			}
			else
			{
				NewQueueUtil.push(msg);
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
	}
	
	@Override
	public void customerPrint(String tenantId,int storeid,String posNum,String print_code,String mode,JSONObject param,int printCount)throws Exception
	{
		String device = new String("select printer from hq_devices where devices_code = ? and store_id = ?");
		SqlRowSet rs = this.query4SqlRowSet(device, new Object[]
		{ posNum, storeid });

		Integer printer = null;

		if (rs.next())
		{
			printer = rs.getInt("printer");
		}
		
		param.put("tenancy_id", tenantId);
		param.put("store_id", storeid);
		param.put("print_time", DateUtil.getNowDateYYDDMMHHMMSS());
		param.put("mode", mode);
		param.put("printer_id", printer);
		param.put("print_code", print_code);
		for(int i=0;i<printCount;i++)
		{
			NewQueueUtil.push(param);
		}
	}

	/**
	 * 固定折扣更新
	 * 
	 * @param organId
	 * @param billNum
	 */
	@Transactional(propagation = Propagation.REQUIRED)
	@Deprecated
	public void updateWholeDiscountPosBillItem(Integer organId, String billNum)
	{
		/**
		 * 常用计算的字段 item_name,item_count,item_price,item_amount,real_amount,
		 * discount_amount,single_discount_amount,discountr_amount,discount_rate
		 */
		StringBuffer sql = new StringBuffer("select * from pos_bill_item where bill_num = ? and store_id = ?");

		/**
		 * 更新账单明细表信息,主要是计算金额
		 */
		StringBuffer updateItem = new StringBuffer("update pos_bill_item set item_amount = ?,discount_amount = ?,real_amount = ? where rwid = ? and store_id = ?");

		SqlRowSet rs = this.jdbcTemplate.queryForRowSet(sql.toString(), new Object[]
		{ billNum, organId });

		while (rs.next())
		{
			Integer rwid = rs.getInt("rwid"); // 账单详情 id
			double item_price = rs.getDouble("item_price"); // 菜品单价
			double item_count = rs.getDouble("item_count"); // 菜品数量
			double item_amount = 0; // 菜品金额
			double real_amount = 0; // 实结金额
			double discountr_amount = rs.getDouble("discountr_amount"); // 折让金额
			double discount_amount = 0; // 优惠金额

			double method_money = rs.getDouble("method_money");
			String discount_state = rs.getString("discount_state");

			double discount_rate = rs.getDouble("discount_rate"); // 折扣率

			String itemProperty = rs.getString("item_property");
			String itemRemark = rs.getString("item_remark");

			if (!"MEALLIST".equalsIgnoreCase(itemProperty) && StringUtils.isEmpty(itemRemark))
			{
				// 菜品金额
				item_amount = Scm.padd(Scm.pmui(item_price, item_count), method_money);
				// 菜品是否可以折扣
				if (StringUtils.isNotEmpty(discount_state))
				{
					// 优惠金额
					discount_amount = Scm.psub(item_amount, Scm.pdiv(Scm.pmui(item_amount, discount_rate), 100d));
				}
				// 实结金额
				real_amount = Scm.psub(Scm.psub(item_amount, discount_amount), discountr_amount);

				this.jdbcTemplate.update(updateItem.toString(), new Object[]
				{ item_amount, discount_amount, real_amount, rwid, organId });
			}

		}
	}

	/**
	 * 固定会员价折扣
	 * 
	 * @param organId
	 * @param billNum
	 */
	@Transactional(propagation = Propagation.REQUIRED)
	@Deprecated
	public void updateVipPrice(Integer organId, String billNum)
	{
		/**
		 * 常用计算的字段 item_name,item_count,item_price,item_amount,real_amount,
		 * discount_amount,single_discount_amount,discountr_amount,discount_rate
		 */
		StringBuffer sql = new StringBuffer("select * from pos_bill_item where bill_num = ? and store_id = ?");

		/**
		 * 更新账单明细表信息,主要是计算金额
		 */
		StringBuffer updateItem = new StringBuffer("update pos_bill_item set item_amount = ?,discount_amount = ?,real_amount = ? where rwid = ? and store_id = ?");

		SqlRowSet rs = this.jdbcTemplate.queryForRowSet(sql.toString(), new Object[]
		{ billNum, organId });

		while (rs.next())
		{
			Integer rwid = rs.getInt("rwid"); // 账单详情 id
			double item_price = 0;
			double thirdPrice = rs.getDouble("third_price"); // 菜品单价
			double oItemPrice = rs.getDouble("item_price");
			double item_count = rs.getDouble("item_count"); // 菜品数量
			double item_amount = 0; // 菜品金额
			double real_amount = 0; // 实结金额
			double discountr_amount = rs.getDouble("discountr_amount"); // 折让金额
			double discount_amount = 0; // 优惠金额

			double method_money = rs.getDouble("method_money");
			String discount_state = rs.getString("discount_state");

			double discount_rate = rs.getDouble("discount_rate"); // 折扣率

			String itemProperty = rs.getString("item_property");
			String itemRemark = rs.getString("item_remark");

			if (thirdPrice != 0)
			{
				item_price = thirdPrice;
			}
			else
			{
				item_price = oItemPrice;
			}

			if (!"MEALLIST".equalsIgnoreCase(itemProperty) && StringUtils.isEmpty(itemRemark))
			{
				// 菜品金额
				item_amount = Scm.padd(Scm.pmui(item_price, item_count), method_money);
				// 菜品是否可以折扣
				if (StringUtils.isNotEmpty(discount_state))
				{
					// 优惠金额
					discount_amount = Scm.psub(item_amount, Scm.pdiv(Scm.pmui(item_amount, discount_rate), 100d));
				}
				// 实结金额
				real_amount = Scm.psub(Scm.psub(item_amount, discount_amount), discountr_amount);

				this.jdbcTemplate.update(updateItem.toString(), new Object[]
				{ item_amount, discount_amount, real_amount, rwid, organId });
			}
		}
	}

	/**
	 * 更新账单明细的 菜品金额，优惠金额和实结金额(updateEachPosBillItem)
	 * 
	 * @param organId
	 * @param billNum
	 */
	@Transactional(propagation = Propagation.REQUIRED)
	@Deprecated
	public void uEachPosBillItem(Integer organId, String billNum)
	{
		/**
		 * 常用计算的字段 item_name,item_count,item_price,item_amount,real_amount,
		 * discount_amount,single_discount_amount,discountr_amount,discount_rate
		 */
		StringBuffer sql = new StringBuffer("select * from pos_bill_item where bill_num = ? and store_id = ?");

		/**
		 * 更新账单明细表信息,主要是计算金额
		 */
		StringBuffer updateItem = new StringBuffer("update pos_bill_item set item_amount = ?,discount_amount = ?,real_amount = ? where rwid = ? and store_id = ?");

		SqlRowSet rs = this.jdbcTemplate.queryForRowSet(sql.toString(), new Object[]
		{ billNum, organId });

		while (rs.next())
		{
			Integer rwid = rs.getInt("rwid"); // 账单详情 id
			double item_price = rs.getDouble("item_price"); // 菜品单价
			double item_count = rs.getDouble("item_count"); // 菜品数量
			double item_amount = 0; // 菜品金额
			double real_amount = 0; // 实结金额
			double discountr_amount = rs.getDouble("discountr_amount"); // 折让金额
			double discount_amount = 0; // 优惠金额

			double method_money = rs.getDouble("method_money");
			String discount_state = rs.getString("discount_state");

			double discount_rate = rs.getDouble("discount_rate"); // 折扣率

			String itemProperty = rs.getString("item_property");
			String itemRemark = rs.getString("item_remark");

			if (!"MEALLIST".equalsIgnoreCase(itemProperty) && StringUtils.isEmpty(itemRemark))
			{
				// 菜品金额
				item_amount = Scm.padd(Scm.pmui(item_price, item_count), method_money);
				// 菜品是否可以折扣
				if (StringUtils.isNotEmpty(discount_state))
				{
					if ("Y".equalsIgnoreCase(discount_state))
					{
						// 优惠金额
						discount_amount = Scm.psub(item_amount, Scm.pdiv(Scm.pmui(item_amount, discount_rate), 100d));
					}
				}
				// 实结金额
				real_amount = Scm.psub(Scm.psub(item_amount, discount_amount), discountr_amount);

				this.jdbcTemplate.update(updateItem.toString(), new Object[]
				{ item_amount, discount_amount, real_amount, rwid, organId });
			}

		}
	}

	// 处理折让,并均摊到每一个菜品上
	@Transactional(propagation = Propagation.REQUIRED)
	@Deprecated
	public void calcVipZr(String sql, String billNum, Integer organId, double discountrAmount)
	{
		Integer cmj = Integer.parseInt(Constant.constantMap.get("CMJEWS"));
		// 重复
		StringBuffer updateItem = new StringBuffer("update pos_bill_item set item_amount = ?,discount_amount = ?,real_amount = ?  where rwid = ? and store_id = ?");

		SqlRowSet rs = this.jdbcTemplate.queryForRowSet(sql.toString(), new Object[]
		{ billNum, organId });
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		while (rs.next())
		{
			Map<String, Object> map = new HashMap<String, Object>();
			Integer rwid = rs.getInt("rwid"); // 账单详情 id
			double item_price = 0; // 菜品单价
			double item_count = rs.getDouble("item_count"); // 菜品数量
			double item_amount = 0; // 菜品金额
			double real_amount = 0; // 实结金额
			double discountr_amount = rs.getDouble("discountr_amount"); // 折让金额
			double discount_amount = 0; // 优惠金额

			double method_money = rs.getDouble("method_money");
			String discount_state = rs.getString("discount_state"); // 能否折扣

			double discount_rate = rs.getDouble("discount_rate"); // 折扣率

			String itemProperty = rs.getString("item_property");
			String itemRemark = rs.getString("item_remark");

			double thirdPrice = rs.getDouble("third_price");
			double oItemPrice = rs.getDouble("item_price");

			if (thirdPrice != 0)
			{
				item_price = thirdPrice;
			}
			else
			{
				item_price = oItemPrice;
			}

			if (!itemProperty.equalsIgnoreCase("MEALLIST") && StringUtils.isEmpty(itemRemark))
			{
				// 菜品金额
				item_amount = Scm.padd(Scm.pmui(item_price, item_count), method_money);
				// 菜品是否可以折扣
				if (StringUtils.isNotEmpty(discount_state))
				{
					// 优惠金额
					discount_amount = Scm.psub(item_amount, Scm.pdiv(Scm.pmui(item_amount, discount_rate), 100d));
				}
				// 实结金额
				real_amount = Scm.psub(Scm.psub(item_amount, discount_amount), discountr_amount);

				map.put("rwid", rwid);
				map.put("real_amount", real_amount);
				map.put("discountr_amount", discountr_amount);
				map.put("discount_amount", discount_amount);
				map.put("item_amount", item_amount);
				list.add(map);

				this.jdbcTemplate.update(updateItem.toString(), new Object[]
				{ item_amount, discount_amount, real_amount, rwid, organId });
			}
		}
		/**
		 * 获得折让金额discountr_amount然后分摊到各个明细里
		 */
		double total = 0d;
		double subtotal = 0d; // 统计所有菜的折让金额
		Integer maxRwid = 0;
		double maxValue = 0d;
		List<Map<String, Object>> listMaps = new ArrayList<Map<String, Object>>();
		// 取总钱数,最大面额和对应的rwid
		for (int n = 0; n < list.size(); n++)
		{
			Map<String, Object> map = list.get(n);
			double itemAmount = (Double) map.get("item_amount");
			total = Scm.padd(total, itemAmount);

			if (maxValue < itemAmount)
			{
				maxValue = itemAmount;
				maxRwid = (Integer) map.get("rwid");
			}
		}
		// 计算并平摊折让的钱
		for (int k = 0; k < list.size(); k++)
		{
			Map<String, Object> map = list.get(k);
			double itemAmount = (Double) map.get("item_amount");
			double attach = Scm.pmui(Scm.pdiv(itemAmount, total), discountrAmount);
			subtotal = Scm.padd(subtotal, attach);
			map.put("discountr_amount", attach);
			listMaps.add(map);
		}
		// 更新账单明细的折让金额
		String uItemTrAmount = new String(" update pos_bill_item set discountr_amount = ?,discount_amount=?,real_amount=? where rwid = ? and store_id = ?");
		// 更新折让价格
		for (int j = 0; j < listMaps.size(); j++)
		{
			Map<String, Object> map = list.get(j);
			Integer rwid = (Integer) map.get("rwid");
			double distrAmount = (double) map.get("discountr_amount");
			double discount_amount = (double) map.get("discount_amount");
			double real_amount = (double) map.get("real_amount");

			if (rwid == maxRwid)
			{
				distrAmount = DoubleHelper.add(distrAmount, DoubleHelper.sub(discountrAmount, subtotal, cmj), cmj);
			}
			real_amount = DoubleHelper.sub(real_amount, distrAmount, cmj);
			this.jdbcTemplate.update(uItemTrAmount, new Object[]
			{ distrAmount, discount_amount, real_amount, rwid, organId });
		}
	}

	// 处理折让,并均摊到每一个菜品上
	@Transactional(propagation = Propagation.REQUIRED)
	@Deprecated
	public void calcZr(String sql, String billNum, Integer organId, double discountrAmount)
	{
		Integer cmj = Integer.parseInt(Constant.constantMap.get("CMJEWS"));
		// 重复
		StringBuffer updateItem = new StringBuffer("update pos_bill_item set item_amount = ?,discount_amount = ?,real_amount = ?  where rwid = ? and store_id = ?");

		SqlRowSet rs = this.jdbcTemplate.queryForRowSet(sql.toString(), new Object[]
		{ billNum, organId });
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		while (rs.next())
		{
			Map<String, Object> map = new HashMap<String, Object>();
			Integer rwid = rs.getInt("rwid"); // 账单详情 id
			double item_price = rs.getDouble("item_price"); // 菜品单价
			double item_count = rs.getDouble("item_count"); // 菜品数量
			double item_amount = 0; // 菜品金额
			double real_amount = 0; // 实结金额
			double discountr_amount = rs.getDouble("discountr_amount"); // 折让金额
			double discount_amount = 0; // 优惠金额

			double method_money = rs.getDouble("method_money");
			String discount_state = rs.getString("discount_state"); // 能否折扣

			double discount_rate = rs.getDouble("discount_rate"); // 折扣率

			String itemProperty = rs.getString("item_property");
			String itemRemark = rs.getString("item_remark");

			if (!itemProperty.equalsIgnoreCase("MEALLIST") && StringUtils.isEmpty(itemRemark))
			{
				// 菜品金额
				item_amount = Scm.padd(Scm.pmui(item_price, item_count), method_money);
				// 菜品是否可以折扣
				if (StringUtils.isNotEmpty(discount_state))
				{
					// 优惠金额
					discount_amount = Scm.psub(item_amount, Scm.pdiv(Scm.pmui(item_amount, discount_rate), 100d));

					// if ("Y".equalsIgnoreCase(discount_state))
					// {
					// //优惠金额
					// discount_amount =
					// Scm.psub(item_amount,Scm.pdiv(Scm.pmui(item_amount,
					// discount_rate), 100d));
					// }
				}
				// 实结金额
				real_amount = Scm.psub(Scm.psub(item_amount, discount_amount), discountr_amount);

				map.put("rwid", rwid);
				map.put("real_amount", real_amount);
				map.put("discountr_amount", discountr_amount);
				map.put("discount_amount", discount_amount);
				map.put("item_amount", item_amount);
				list.add(map);

				this.jdbcTemplate.update(updateItem.toString(), new Object[]
				{ item_amount, discount_amount, real_amount, rwid, organId });
			}
		}
		/**
		 * 获得折让金额discountr_amount然后分摊到各个明细里
		 */
		double total = 0d;
		double subtotal = 0d; // 统计所有菜的折让金额
		Integer maxRwid = 0;
		double maxValue = 0d;
		List<Map<String, Object>> listMaps = new ArrayList<Map<String, Object>>();
		// 取总钱数,最大面额和对应的rwid
		for (int n = 0; n < list.size(); n++)
		{
			Map<String, Object> map = list.get(n);
			double itemAmount = (Double) map.get("item_amount");
			total = Scm.padd(total, itemAmount);

			if (maxValue < itemAmount)
			{
				maxValue = itemAmount;
				maxRwid = (Integer) map.get("rwid");
			}
		}
		// 计算并平摊折让的钱
		for (int k = 0; k < list.size(); k++)
		{
			Map<String, Object> map = list.get(k);
			double itemAmount = (Double) map.get("item_amount");
			double attach = Scm.pmui(Scm.pdiv(itemAmount, total), discountrAmount);
			subtotal = Scm.padd(subtotal, attach);
			map.put("discountr_amount", attach);
			listMaps.add(map);
		}
		// 更新账单明细的折让金额
		String uItemTrAmount = new String(" update pos_bill_item set discountr_amount = ?,discount_amount=?,real_amount=? where rwid = ? and store_id = ?");
		// 更新折让价格
		for (int j = 0; j < listMaps.size(); j++)
		{
			Map<String, Object> map = list.get(j);
			Integer rwid = (Integer) map.get("rwid");
			double distrAmount = (double) map.get("discountr_amount");
			double discount_amount = (double) map.get("discount_amount");
			double real_amount = (double) map.get("real_amount");

			if (rwid == maxRwid)
			{
				distrAmount = DoubleHelper.add(distrAmount, DoubleHelper.sub(discountrAmount, subtotal, cmj), cmj);
			}
			real_amount = DoubleHelper.sub(real_amount, distrAmount, cmj);
			this.jdbcTemplate.update(uItemTrAmount, new Object[]
			{ distrAmount, discount_amount, real_amount, rwid, organId });
		}
	}

	/**
	 * 这个会有问题，如果不和门店id关联起来的话，查询数据有可能有问题 获得当前时间的班次信息
	 * 当登录后没有获得shiftId或在传参时没有获得shiftId值时调用
	 * 
	 * @return
	 */
	@Deprecated
	public int getShiftId()
	{
		Integer shiftId = null;
		StringBuilder sql = null;

		String time = DateUtil.formatTime(new Date());

		sql = new StringBuilder("select id from duty_order where start_time < '" + time + "' and '" + time + "' < end_time and end_time <= '23:59:59'");

		SqlRowSet rs = jdbcTemplate.queryForRowSet(sql.toString());
		if (rs.next())
		{
			shiftId = rs.getInt("id");
		}
		else
		{
			sql = new StringBuilder("select id from duty_order where start_time < ? and end_time <= '23:59:59' and start_time > end_time");
			rs = jdbcTemplate.queryForRowSet(sql.toString(), new Object[]
			{ time });
			if (rs.next())
			{
				shiftId = rs.getInt("id");
			}
		}

		logger.debug("查询班次时间=" + time);
		logger.debug("查询班次sql==" + sql.toString());
		return shiftId;
	}

	/**
	 * 获得item_name,item_count,item_price,item_amount,real_amount,
	 * discount_amount,single_discount_amount,discountr_amount
	 * 
	 * @param tenantId
	 * @param billNum
	 * @param tableCode
	 * @return
	 */
	@Deprecated
    @Override
	public SqlRowSet getPosBillItemAmount(String tenantId, Integer organId, String billNum, Integer rwid)
	{
		SqlRowSet rs = null;
		StringBuilder sql = new StringBuilder("SELECT item_name,item_count,item_price,item_amount,real_amount,discount_amount,single_discount_amount,discountr_amount FROM pos_bill_item ");
		/**
		 * 账单号查询账单信息
		 */
		if (StringUtils.isNotEmpty(billNum))
		{
			sql.append(" where bill_num = ? and store_id = ?");
			rs = this.jdbcTemplate.queryForRowSet(sql.toString(), new Object[]
			{ billNum, organId });
			return rs;
		}
		/**
		 * 任务id查询账单信息
		 */
		if (rwid != null)
		{
			sql.append(" where rwid = ? and store_id = ?");
			rs = this.jdbcTemplate.queryForRowSet(sql.toString(), new Object[]
			{ rwid, organId });
			return rs;
		}

		return rs;
	}

	

	@Transactional(propagation = Propagation.REQUIRED)
	@Deprecated
	public void savePosLog(PosLog log)
	{
		try
		{
			Timestamp lastUpdateTime = DateUtil.currentTimestamp();
			if (log.getLastUpdateTime() == null)
			{
				log.setLastUpdateTime(lastUpdateTime);
			}
			StringBuilder insertSql = new StringBuilder("INSERT INTO pos_log (tenancy_id,store_id,pos_num,opt_num,opt_name,title,content,oldstate,newstate,shift_id,report_date,last_updatetime) ");
			insertSql.append("values (?,?,?,?,?,?,?,?,?,?,?,?)");

			this.jdbcTemplate.update(insertSql.toString(), new Object[]
			{ log.getTenancyId(), log.getOrganId(), log.getPosNum(), log.getOptNum(), log.getOptName(), log.getTitle(), log.getContent(), log.getOldState(), log.getNewState(), log.getShiftId(), log.getReportDate(), log.getLastUpdateTime() });
		}
		catch (DataAccessException e)
		{
			logger.error("保存Pos日志错误", e);
			e.printStackTrace();
		}
	}



	@Override
	public void savePosLog(String tenantId, Integer organId, String posNum, String optNum, String optName, Integer shiftId, Date reportDate, String title, String content, String oldstate, String newstate)
	{
		try
		{
			String empName ="";
			//外卖日始 未开班 下单 默认用户  ；外卖统一班次
			if (!com.tzx.orders.base.constant.Constant.BETWEEN_SHIFT_OPT_NUM.equals(optNum) && !com.tzx.orders.base.constant.Constant.WM_OPT_NUM.equals(optNum) && Tools.isNullOrEmpty(optName)){
				empName = this.getEmpNameById(optNum, tenantId, organId);
			}
			
			Timestamp lastUpdateTime = DateUtil.currentTimestamp();
			this.savePosLog(tenantId, organId, posNum, optNum, empName, shiftId, reportDate, title, content, oldstate, newstate, lastUpdateTime);
		}
		catch (Exception e)
		{
			logger.info("保存Pos日志错误", e);
			e.printStackTrace();
		}
	}
    @Async
    @Transactional(propagation = Propagation.REQUIRES_NEW,rollbackFor = Exception.class)//生产环境中pos_log容易损坏导致正常业务无法正常完成，故新建事务
	@Override
	public void savePosLog(String tenantId, Integer organId, String posNum, String optNum, String optName, Integer shiftId, Date reportDate, String title, String content, String oldstate, String newstate, Timestamp lastUpdateTime) throws Exception
	{
		if (Tools.isNullOrEmpty(optName))
		{
			optName = this.getEmpNameById(optNum, tenantId, organId);
		}

		if (Tools.isNullOrEmpty(lastUpdateTime))
		{
			lastUpdateTime = DateUtil.currentTimestamp();
		}

		if (null != oldstate && oldstate.length() > 500)
		{
			oldstate = oldstate.substring(0, 497) + "...";
		}

		if (null != newstate && newstate.length() > 500)
		{
			newstate = newstate.substring(0, 497) + "...";
		}

		StringBuilder insertSql = new StringBuilder("INSERT INTO pos_log (tenancy_id,store_id,pos_num,opt_num,opt_name,title,content,oldstate,newstate,shift_id,report_date,last_updatetime) ");
		insertSql.append("values (?,?,?,?,?,?,?,?,?,?,?,?)");

		this.jdbcTemplate.update(insertSql.toString(), new Object[]
		{ tenantId, organId, posNum, optNum, optName, title, content, oldstate, newstate, shiftId, reportDate, lastUpdateTime });
	}

	@Override
	public void saveOptState(String tenancyId, int storeId, Date reportDate, int shiftId, String posNum, String optNum, String content, String managerNum, String remark, Integer loginNumber,Timestamp lastUpdateTime) throws Exception
	{
		try{
			String empName = this.getEmpNameById(optNum, tenancyId, storeId);
			StringBuilder insertSql = new StringBuilder("insert into pos_opt_state(tenancy_id,store_id,report_date,shift_id,pos_num,opt_num,opt_name,content,manager_num,remark,last_updatetime,tag,login_number,upload_tag) ");
			insertSql.append("values (?,?,?,?,?,?,?,?,?,?,?,'0',?,'0')");
	
			this.jdbcTemplate.update(insertSql.toString(), new Object[]
			{ tenancyId,storeId,reportDate,shiftId,posNum,optNum,empName,content,managerNum,remark,lastUpdateTime,loginNumber});
		}
		catch (DataAccessException e)
		{
			logger.error("保存Pos日志错误", e);
			e.printStackTrace();
		}
		
	}
	
	

	@Override
	@Deprecated
	public void sendChef(String tenancyId, String billno, Integer organId)
	{
		StringBuilder sql0 = new StringBuilder(
				"select item.tenancy_id,item.store_id,bill.table_code,table1.table_name,item.bill_num,bill.guest,dish.item_code,item.item_name,item.item_english,item.item_unit_name,item.item_price,item.item_count,CURRENT_TIMESTAMP(0) :: TIMESTAMP WITHOUT TIME ZONE ,'',item.item_property,item.print_tag,item.item_remark,");
		sql0.append("item.waiter_num,shift.name,classprint.printer_id, printer.name,0,'*',item.item_serial,item.rwid,table1.table_property_id,'' ,printformat.class_item_code ");
		sql0.append("from pos_bill_item as item left join pos_bill bill on item.bill_num=bill.bill_num and item.tenancy_id=bill.tenancy_id and item.store_id=bill.store_id ");
		sql0.append(" left join duty_order shift on item.item_shift_id=shift.id left join tables_info table1 on bill.table_code=table1.table_code and table1.organ_id=bill.store_id left join hq_item_info dish on item.item_id=dish.id and dish.tenancy_id=item.tenancy_id ");
		sql0.append(" left join hq_kvs_item printitem on dish.id=printitem.item_id and kvs_id in (select hk.id from hq_kvs hk where hk.tenancy_id='" + tenancyId + "' and hk.store_id= " + organId + " )");
		sql0.append(" left join hq_kvs_printer classprint on printitem.kvs_id=classprint.kvs_id left join hq_print_printer printer on classprint.printer_id=printer.id ");
		sql0.append(" left join hq_print_printer_format printformat on printer.id=printformat.print_printer_id and class_item_code not in('1001','1002','1003','1004') ");
		sql0.append(" where item.print_tag = '*' and item.bill_num = '" + billno + "' and item.store_id = " + organId + " order by item.rwid asc");

		logger.debug("厨打sql：：：：" + sql0);
		/**
		 * 送厨打
		 */
		StringBuilder sql = new StringBuilder("insert into pos_print_item(tenancy_id,store_id,table_code,table_name,bill_num,guest,item_num,item_name,item_english,item_unit_name,item_price,item_count,update_time,zfkwnr,item_property,");
		sql.append("print_property,item_remark,waiter_num,shift_name,printer_id,printer_name,report_id,print_tag,item_serial,print_code,table_property_id,table_property_tag,print_format) ");

		sql.append("select item.tenancy_id,item.store_id,bill.table_code,table1.table_name,item.bill_num,bill.guest,dish.item_code,item.item_name,item.item_english,item.item_unit_name,item.item_price,item.item_count,CURRENT_TIMESTAMP(0) :: TIMESTAMP WITHOUT TIME ZONE ,'',item.item_property,'点菜',item.item_remark,");
		sql.append("item.waiter_num,shift.name,classprint.printer_id, printer.name,0,'*',item.item_serial,item.rwid,table1.table_property_id,'' ,printformat.class_item_code ");

		sql.append("from pos_bill_item as item left join pos_bill bill on item.bill_num=bill.bill_num and item.tenancy_id=bill.tenancy_id and item.store_id=bill.store_id ");

		sql.append(" left join duty_order shift on item.item_shift_id=shift.id left join tables_info table1 on bill.table_code=table1.table_code and table1.organ_id=bill.store_id left join hq_item_info dish on item.item_id=dish.id and dish.tenancy_id=item.tenancy_id ");
		sql.append(" left join hq_kvs_item printitem on dish.id=printitem.item_id and kvs_id in (select hk.id from hq_kvs hk where hk.tenancy_id=? and hk.store_id= ? )");
		sql.append(" left join hq_kvs_printer classprint on printitem.kvs_id=classprint.kvs_id left join hq_print_printer printer on classprint.printer_id=printer.id ");
		sql.append(" left join hq_print_printer_format printformat on printer.id=printformat.print_printer_id and class_item_code not in ('1001','1002','1003','1004') ");

		sql.append(" where item.print_tag = '*' and item.bill_num = ? and item.store_id = ? order by item.rwid asc");

		this.jdbcTemplate.update(sql.toString(), new Object[]
		{ tenancyId, organId, billno, organId });
	}

	@Override
	@Deprecated
	public void operChef(String tenancyId, String billno, Integer organId, String oper, String rwids)
	{
		/**
		 * 送厨打
		 */
		StringBuilder sql = new StringBuilder("insert into pos_print_item(tenancy_id,store_id,table_code,table_name,bill_num,guest,item_num,item_name,item_english,item_unit_name,item_price,item_count,update_time,zfkwnr,item_property,");
		sql.append("print_property,item_remark,waiter_num,shift_name,printer_id,printer_name,report_id,print_tag,item_serial,print_code,table_property_id,table_property_tag,print_format) ");

		sql.append(" select item.tenancy_id,item.store_id,bill.table_code,table1.table_name,item.bill_num,bill.guest,dish.item_code,item.item_name,item.item_english,item.item_unit_name,item.item_price,item.item_count,CURRENT_TIMESTAMP(0) :: TIMESTAMP WITHOUT TIME ZONE ,'',item.item_property,'"
				+ oper + "',item.item_remark,");
		sql.append(" item.waiter_num,shift.name,classprint.printer_id, printer.name,0,'*',item.item_serial,item.rwid,table1.table_property_id,'',printformat.class_item_code from pos_bill_item as item left join pos_bill bill on item.bill_num=bill.bill_num and item.tenancy_id=bill.tenancy_id and item.store_id=bill.store_id ");
		sql.append(" left join duty_order shift on item.item_shift_id=shift.id left join tables_info table1 on bill.table_code=table1.table_code and table1.organ_id=bill.store_id left join hq_item_info dish on item.item_id=dish.id and dish.tenancy_id=item.tenancy_id ");
		sql.append(" left join hq_kvs_item printitem on dish.id=printitem.item_id and kvs_id in(select id from hq_kvs where tenancy_id=? and store_id=?)");
		sql.append(" left join hq_kvs_printer classprint on printitem.kvs_id=classprint.kvs_id left join hq_print_printer printer on classprint.printer_id=printer.id ");
		sql.append(" left join hq_print_printer_format printformat on printer.id=printformat.print_printer_id and class_item_code not in('1001','1002','1003','1004') ");
		sql.append(" where item.bill_num = ? and item.store_id = ? and item.rwid in (" + rwids + ")");

		this.jdbcTemplate.update(sql.toString(), new Object[]
		{ tenancyId, organId, billno, organId });
	}

	// 1固定折扣、2非固定折扣、3折让、4团体会员折扣、5会员折扣、6会员价
	@Transactional(propagation = Propagation.REQUIRED)
	@Deprecated
	public void calcAmount(String tenantId, Integer organId, String billNum, String tableCode) throws SystemException
	{
		/**
		 * 常用计算的字段 item_name,item_count,item_price,item_amount,real_amount,
		 * discount_amount,single_discount_amount,discountr_amount,discount_rate
		 */
		StringBuffer sql = new StringBuffer("select * from pos_bill_item where bill_num = ? and store_id = ?");

		String billsql = new String("select * from pos_bill where bill_num = ? and store_id = ?");

		// String ubillDiscountMode = new
		// String("update pos_bill set discount_mode_id=? where bill_num=? and store_id=?");
		//
		// String uitemDiscountMode = new
		// String("update pos_bill_item set discount_mode_id=? where bill_num=? and store_id=?");
		/**
		 * 账单号查询账单信息 subtotal 已点金额 average_amount 人均消费 payment_amount 应付金额
		 * givi_amount 奉送金额
		 */
		if (StringUtils.isNotEmpty(billNum))
		{
			String usubtotal = new String("update pos_bill set subtotal = (select sum(pbi.item_amount) from pos_bill_item pbi where pbi.bill_num = ? and pbi.store_id = ? and pbi.item_property <> 'MEALLIST' and (pbi.item_remark is null or pbi.item_remark='FS02')) where bill_num = ? and store_id = ?");
			this.jdbcTemplate.update(usubtotal, new Object[]
			{ billNum, organId, billNum, organId });

			Double discountRate = null;
			Integer discountCaseId = null;
			Double discountrAmount = null;
			Integer serviceId = null;
			Double subTotal = null;
			Double discountModeId = null;

			SqlRowSet rs = this.jdbcTemplate.queryForRowSet(billsql, new Object[]
			{ billNum, organId });
			while (rs.next())
			{
				subTotal = rs.getDouble("subtotal");
				discountRate = rs.getDouble("discount_rate");
				discountCaseId = rs.getInt("discount_case_id");
				discountrAmount = rs.getDouble("discountr_amount");
				serviceId = rs.getInt("service_id");
				discountModeId = rs.getDouble("discount_mode_id");
			}

			if (subTotal != null && subTotal == 0d)
			{
				String updateB = new String(
						"update pos_bill set service_id=null,service_amount=null,discountk_amount=null,discountr_amount=null,maling_amount=null,single_discount_amount=null,discount_amount=null,free_amount=null,givi_amount=null,more_coupon=null,average_amount=null,discount_case_id=null,discount_rate=null where bill_num = ? and store_id = ?");
				this.jdbcTemplate.update(updateB, new Object[]
				{ billNum, organId });
				discountRate = null;
				discountCaseId = null;
				discountrAmount = null;
				serviceId = null;
			}

			if (discountRate != null && discountRate == 0d)
			{
				discountRate = 100d;
			}

			if (Tools.isNullOrEmpty(discountModeId) == false && discountModeId == 6d)
			{
				updateVipPrice(organId, billNum);
			}
			else
			{
				// 如果折扣率不为空，手动固定折扣
				if (Tools.isNullOrEmpty(discountRate) == false)
				{
					if (discountRate != 0d && discountCaseId == 0d)
					{
						// this.jdbcTemplate.update(ubillDiscountMode,new
						// Object[]{4,billNum,organId});
						// this.jdbcTemplate.update(uitemDiscountMode,new
						// Object[]{4,billNum,organId});
						// 更新账单表折扣率
						StringBuilder ucase = new StringBuilder("update pos_bill set discount_rate = ? where bill_num = ? and store_id = ? ");
						this.jdbcTemplate.update(ucase.toString(), new Object[]
						{ discountRate, billNum, organId });
						// 更新账单明细折扣率
						ucase = new StringBuilder("update pos_bill_item set discount_rate = ? where bill_num = ? and store_id = ? ");
						this.jdbcTemplate.update(ucase.toString(), new Object[]
						{ discountRate, billNum, organId });
						// 固定折扣
						updateWholeDiscountPosBillItem(organId, billNum);
					}
				}

				if (Tools.isNullOrEmpty(discountCaseId) == false && discountCaseId == 0d)
				{
					/**
					 * 取消折扣 更新每个pos_bill_item 账单折扣
					 */
					if (discountRate == null || discountRate == 100)
					{
						if (discountrAmount == 0d || discountrAmount == null)
						{
							// this.jdbcTemplate.update(ubillDiscountMode,new
							// Object[]{null,billNum,organId});
							// this.jdbcTemplate.update(uitemDiscountMode,new
							// Object[]{null,billNum,organId});

							StringBuffer sqlUpdate = new StringBuffer("update pos_bill set discount_mode_id=?,discount_case_id=?,discount_num=?,discountr_amount = ? where bill_num = ? and store_id = ?");

							this.jdbcTemplate.update(sqlUpdate.toString(), new Object[]
							{ null, discountCaseId, discountRate, 0d, billNum, organId });

							StringBuffer sqlUpdateItem = new StringBuffer("update pos_bill_item set discount_mode_id=?,discount_rate=?,real_amount=?,single_discount_amount=?,discountr_amount=?,discount_amount=? where bill_num = ? and store_id = ?");
							this.jdbcTemplate.update(sqlUpdateItem.toString(), new Object[]
							{ null, 100, 0, 0, 0, 0, billNum, organId });

							uEachPosBillItem(organId, billNum);
						}
					}
				}
				else if (Tools.isNullOrEmpty(discountCaseId) == false && discountCaseId != 0d)
				{
					/**
					 * 折扣
					 */
					StringBuffer sqlUpdate = new StringBuffer("update pos_bill set discountr_amount = ? where bill_num = ? and store_id = ?");

					String discount_case_type = "";// 折扣类型
					double rate_renate = 0;
					/**
					 * 根据discount_case_id查找折扣类库,获得折扣类型和折扣率
					 */
					StringBuilder casestr = new StringBuilder("select hdc.discount_case_type,hdc.rate_renate from hq_discount_case as hdc left join hq_discount_case_org hdco on hdc.id = hdco.discount_case_id where hdc.id = ? and hdco.store_id = ? group by hdc.discount_case_type,hdc.rate_renate");

					rs = this.jdbcTemplate.queryForRowSet(casestr.toString(), new Object[]
					{ discountCaseId, organId });
					while (rs.next())
					{
						discount_case_type = rs.getString("discount_case_type");
						rate_renate = rs.getDouble("rate_renate");
					}

					/**
					 * 判断折扣类型
					 */
					switch (discount_case_type)
					{
						case "GD01": // 固定折扣

							// this.jdbcTemplate.update(ubillDiscountMode,new
							// Object[]{1,billNum,organId});
							// this.jdbcTemplate.update(uitemDiscountMode,new
							// Object[]{1,billNum,organId});

							double rateRenate = 100;
							String gdDiscount = new String("select rate_renate from hq_discount_case WHERE id = ? ");
							SqlRowSet rst = this.jdbcTemplate.queryForRowSet(gdDiscount, new Object[]
							{ discountCaseId });
							while (rst.next())
							{
								rateRenate = rst.getDouble("rate_renate");
							}

							// 更新折扣率
							StringBuilder ucase = new StringBuilder("update pos_bill set discount_case_id = ?,discount_rate = ? where bill_num = ? and store_id = ? ");
							this.jdbcTemplate.update(ucase.toString(), new Object[]
							{ discountCaseId, rateRenate, billNum, organId });

							StringBuilder uItemcase = new StringBuilder("update pos_bill_item set discount_rate = ? where bill_num = ? and store_id = ? ");
							this.jdbcTemplate.update(uItemcase.toString(), new Object[]
							{ rateRenate, billNum, organId });
							// 固定折扣
							updateWholeDiscountPosBillItem(organId, billNum);
							break;
						case "FGD02": // 非固定折扣

							// this.jdbcTemplate.update(ubillDiscountMode,new
							// Object[]{2,billNum,organId});
							// this.jdbcTemplate.update(uitemDiscountMode,new
							// Object[]{2,billNum,organId});

							// 重复
							StringBuffer updateItem = new StringBuffer("update pos_bill_item set item_amount = ?,discount_amount = ?,real_amount = ?,discount_rate = ?,discountr_amount = ? where rwid = ? and bill_num = ? and store_id = ?");
							// hq_discount_case_details中取该菜品+规格定位的那个菜品的折扣率(rate)和减免金额
							StringBuilder rateStr = new StringBuilder(
									"select hdcd.rate,hdcd.derate from hq_discount_case_details hdcd left join hq_discount_case_org hdco on hdcd.discount_case_id = hdco.discount_case_id  where hdcd.discount_case_id = ? and hdcd.item_id=? and hdcd.unit=? and hdco.store_id = ? group by hdcd.rate,hdcd.derate ");

							rs = this.jdbcTemplate.queryForRowSet(sql.toString(), new Object[]
							{ billNum, organId });

							while (rs.next())
							{
								Integer rwid = rs.getInt("rwid"); // 账单详情 id
								double item_price = rs.getDouble("item_price"); // 菜品单价
								Integer item_id = rs.getInt("item_id");// 菜品id
								double item_count = rs.getDouble("item_count"); // 菜品数量
								double item_amount = 0; // 菜品金额
								double real_amount = 0; // 实结金额
								double discountr_amount = rs.getDouble("discountr_amount"); // 折让金额
								double discount_amount = 0; // 优惠金额

								String unitName = rs.getString("item_unit_name");

								double method_money = rs.getDouble("method_money");
								String discount_state = rs.getString("discount_state");

								double discount_rate = 100; // 折扣率
								double derate = 0;

								String itemProperty = rs.getString("item_property");
								String itemRemark = rs.getString("item_remark");

								SqlRowSet rs1 = this.jdbcTemplate.queryForRowSet(rateStr.toString(), new Object[]
								{ discountCaseId, item_id, unitName, organId });
								while (rs1.next())
								{
									discount_rate = rs1.getDouble("rate");
									derate = rs1.getDouble("derate");
								}

								if (StringUtils.isNotEmpty(discount_state))
								{
									// 菜品金额
									item_amount = Scm.padd(Scm.pmui(item_price, item_count), method_money);
									if ("Y".equalsIgnoreCase(discount_state))
									{
										if (!itemProperty.equalsIgnoreCase("MEALLIST") && StringUtils.isEmpty(itemRemark))
										{
											if (discount_rate != 0)
											{
												// 优惠金额
												discount_amount = Scm.psub(item_amount, Scm.pdiv(Scm.pmui(item_amount, discount_rate), 100d));
											}
											if (discount_rate == 0 && derate != 0)
											{
												discount_rate = 100;
												// 折让金额
												discount_amount = Scm.pmui(derate, item_count);
											}
										}
									}
									// 实结金额
									real_amount = Scm.psub(Scm.psub(item_amount, discount_amount), discountr_amount);

									this.jdbcTemplate.update(updateItem.toString(), new Object[]
									{ item_amount, discount_amount, real_amount, discount_rate, discountr_amount, rwid, billNum, organId });
								}
							}
							break;
						case "ZR03": //

							// this.jdbcTemplate.update(ubillDiscountMode,new
							// Object[]{3,billNum,organId});
							// this.jdbcTemplate.update(uitemDiscountMode,new
							// Object[]{3,billNum,organId});

							discountrAmount = rate_renate;
							this.jdbcTemplate.update(sqlUpdate.toString(), new Object[]
							{ discountrAmount, billNum, organId });
							break;
						default:
							StringBuilder udcase = new StringBuilder("update pos_bill set discount_case_id = ?,discount_rate = ? where bill_num = ? and store_id = ? ");
							this.jdbcTemplate.update(udcase.toString(), new Object[]
							{ discountCaseId, discountRate, billNum, organId });
							break;
					}
				}
			}

			// 有服务费
			if (Tools.isNullOrEmpty(serviceId) == false && serviceId != 0)
			{
				double zuidi_xfe = 0d;
				double guding_jj = 0d;
				double fwfl = 0d;
				double subtotal = 0d;
				String takenMode = "";

				Integer cmj = Integer.parseInt(Constant.constantMap.get("ZDJEWS"));

				StringBuilder sqls = new StringBuilder("select coalesce(zuidi_xfe,0) as zuidi_xfe,coalesce(guding_jj,0) as guding_jj,coalesce(fwfl,0) as fwfl,taken_mode,fee_type from hq_service_fee_type where id = ?");
				SqlRowSet rss = this.jdbcTemplate.queryForRowSet(sqls.toString(), new Object[]
				{ serviceId });
				while (rss.next())
				{
					zuidi_xfe = rss.getDouble("zuidi_xfe");
					guding_jj = rss.getDouble("guding_jj");
					fwfl = rss.getDouble("fwfl");
					takenMode = rss.getString("taken_mode");
				}

				StringBuilder str = new StringBuilder("select subtotal from pos_bill where bill_num = ? and store_id = ?");
				rss = this.jdbcTemplate.queryForRowSet(str.toString(), new Object[]
				{ billNum, organId });
				while (rss.next())
				{
					subtotal = rss.getDouble("subtotal");
				}

				double serviceAmount = 0d;

				if (zuidi_xfe != 0)
				{
					if (Scm.psub(zuidi_xfe, subtotal) > 0)
					{
						serviceAmount = DoubleHelper.sub(zuidi_xfe, subtotal, cmj);
					}
				}

				// if (fwfl != 100)
				// {
				// serviceAmount = DoubleHelper.add(serviceAmount,
				// DoubleHelper.div(DoubleHelper.mul(subtotal, fwfl, cmj), 100d,
				// cmj),cmj);
				// }
				//
				// if (guding_jj != 0)
				// {
				// serviceAmount = DoubleHelper.add(serviceAmount, guding_jj,
				// cmj);
				// }

				if ("GD01".equals(takenMode))
				{
					serviceAmount = Scm.padd(serviceAmount, guding_jj);
				}
				else if ("BL02".equals(takenMode))
				{
					serviceAmount = Scm.padd(serviceAmount, Scm.pdiv(Scm.pmui(subtotal, fwfl), 100d));
				}
				else if ("FS03".equals(takenMode))
				{
					StringBuilder itemSql = new StringBuilder("select coalesce(sum(item_count),0) as item_count from pos_bill_item where bill_num=?");
					rs = jdbcTemplate.queryForRowSet(itemSql.toString(), new Object[]
					{ billNum });
					if (rs.next())
					{
						serviceAmount = Scm.padd(serviceAmount, Scm.pmui(guding_jj, rs.getDouble("item_count")));
					}
				}

				sqls = new StringBuilder("update pos_bill set service_id = ?, service_amount = ? where bill_num = ? and store_id = ? ");
				this.jdbcTemplate.update(sqls.toString(), new Object[]
				{ serviceId, serviceAmount, billNum, organId });

			}

			String uGiveAmount = new String("update pos_bill set givi_amount = (select sum(pbi.item_price*pbi.item_count) from pos_bill_item pbi where pbi.item_remark = 'FS02' and pbi.item_property <> 'MEALLIST' and pbi.bill_num = ? and pbi.store_id = ?) where bill_num = ? and store_id = ? ");
			this.jdbcTemplate.update(uGiveAmount, new Object[]
			{ billNum, organId, billNum, organId });

			// 折让可以和折扣或固定折扣同时存在
			if (Tools.isNullOrEmpty(discountrAmount) == false && discountrAmount != 0d)
			{
				if (Tools.isNullOrEmpty(discountModeId) == false && discountModeId == 6d)
				{
					calcVipZr(sql.toString(), billNum, organId, discountrAmount);
				}
				else
				{
					calcZr(sql.toString(), billNum, organId, discountrAmount);
				}
			}
			/**
			 * 更新账单表 金额
			 */
			try
			{
				updatePosBillAmount(tenantId, organId, billNum);
			}
			catch (Exception e)
			{
				e.printStackTrace();
			}

		}
	}

	@Override
	@Deprecated
	public void calcAmount(String tenantId, Integer storeId, String billNum) throws Exception
	{
		String mode = this.getSysParameter(tenantId, storeId, "ZCDCMS");

		StringBuilder querySql = new StringBuilder("select format_state from organ where tenancy_id=? and id=?");
		SqlRowSet rs = this.query4SqlRowSet(querySql.toString(), new Object[]
		{ tenantId, storeId });
		if (rs.next())
		{
			if ("1".equals(rs.getString("format_state")) && "02".equals(mode))
			{
				calcAmountForSelf(tenantId, storeId, billNum);
			}
			else
			{
				calcAmountForStandard(tenantId, storeId, billNum);
			}
		}
	}

	@Override
	@Deprecated
	public void calcAmountForSelf(String tenantId, Integer storeId, String billNum) throws Exception
	{
		if (Tools.isNullOrEmpty(billNum))
		{
			return;
		}

		int dishScale = 2;
		if (Constant.constantMap.containsKey("CMJEWS"))
		{
			dishScale = Integer.parseInt(Constant.constantMap.get("CMJEWS"));
		}
		int billScale = 2;
		if (Constant.constantMap.containsKey("ZDJEWS"))
		{
			billScale = Integer.parseInt(Constant.constantMap.get("ZDJEWS"));
		}
		final int defaultScale = 4;

		/**
		 * 计算账单明细中菜目金额
		 */
		this.updateItemAmount(tenantId, storeId, billNum, dishScale);

		/**
		 * 计算账单菜目合计
		 */
//		String usubtotal = new String("update pos_bill set subtotal = (select sum(pbi.item_amount) from pos_bill_item pbi where pbi.bill_num = ? and pbi.store_id = ? and pbi.item_property <> 'MEALLIST') where bill_num = ? and store_id = ?");
//		this.jdbcTemplate.update(usubtotal, new Object[]
//		{ billNum, storeId, billNum, storeId });

		String billsql = new String("select guest,service_id,discount_mode_id,discount_rate,discount_case_id,discountr_amount,batch_num,source,service_amount,(select coalesce(sum(pbi.item_amount),0) from pos_bill_item pbi where pbi.tenancy_id = pb.tenancy_id and pbi.store_id = pb.store_id and pbi.bill_num = pb.bill_num and pbi.batch_num=pb.batch_num and pbi.item_property <> ?) as subtotal from pos_bill_batch pb where tenancy_id =? and store_id = ? and bill_num = ?");
		SqlRowSet rsBill = this.jdbcTemplate.queryForRowSet(billsql, new Object[]
		{ SysDictionary.ITEM_PROPERTY_MEALLIST, tenantId,storeId,billNum});
		
		Double discountRate = null;
		Double discountrAmount = null;
		Double subtotal = null;
		Double guest = null;
		Integer serviceId = null;
		Integer discountModeId = null;
		Integer discountCaseId = null;
		String batchNum = null;
		String channel = null;
		double serviceAmount = 0d;
		
		while (rsBill.next())
		{
			guest = rsBill.getDouble("guest");
			subtotal = rsBill.getDouble("subtotal");
			discountRate = rsBill.getDouble("discount_rate");
			discountCaseId = rsBill.getInt("discount_case_id");
			discountrAmount = rsBill.getDouble("discountr_amount");
			serviceId = rsBill.getInt("service_id");
			discountModeId = rsBill.getInt("discount_mode_id");
			batchNum = rsBill.getString("batch_num");
			channel = rsBill.getString("source");
			serviceAmount = rsBill.getDouble("service_amount");
			
			/**
			 * 判断菜目合计大于0,
			 */
			if (subtotal != null && subtotal == 0d)
			{
//				String updateB = new String(
//						"update pos_bill set service_id=null,service_amount=null,discountk_amount=null,discountr_amount=null,maling_amount=null,single_discount_amount=null,discount_amount=null,free_amount=null,givi_amount=null,more_coupon=null,average_amount=null,discount_case_id=null,discount_rate=null where bill_num = ? and store_id = ?");
//				this.jdbcTemplate.update(updateB, new Object[]
//				{ billNum, storeId });
				discountRate = null;
				discountCaseId = null;
				discountrAmount = null;
				serviceId = null;
			}
			
			/**
			 * 计算折扣金额 奉送菜品折扣金额为0
			 */
			if (discountRate == null || discountRate == 0d)
			{
				discountRate = 100d;
			}
			
			switch (discountModeId)
			{
				case 1:// 1固定折扣
				case 2:// 2非固定折扣
				case 3:// 3折扣折让
					/**
					 * 根据discount_case_id查找折扣类库,获得折扣类型和折扣率
					 */
					StringBuilder casestr = new StringBuilder("select hdc.discount_case_type,hdc.rate_renate from hq_discount_case as hdc left join hq_discount_case_org hdco on hdc.id = hdco.discount_case_id where hdc.id = ? and hdco.store_id = ? group by hdc.discount_case_type,hdc.rate_renate");
					SqlRowSet rsCase = this.jdbcTemplate.queryForRowSet(casestr.toString(), new Object[]
					{ discountCaseId, storeId });

					String discount_case_type = "";// 折扣类型
					double rate_renate = 0;
					if (rsCase.next())
					{
						discount_case_type = rsCase.getString("discount_case_type");
						rate_renate = rsCase.getDouble("rate_renate");
					}
					
					switch (discount_case_type)
					{
						case "GD01": // 固定折扣
							// 更新折扣率
							StringBuilder ucase = new StringBuilder("update pos_bill_batch set discount_rate = ? where bill_num = ? and store_id = ? ");
							this.jdbcTemplate.update(ucase.toString(), new Object[]
							{ rate_renate, billNum, storeId });
							// 更新账单明细折扣率
							updateDiscountRateAmount(tenantId, storeId, billNum, batchNum, discountRate, true, dishScale);
							break;
						case "FGD02": // 非固定折扣
							// hq_discount_case_details中取该菜品+规格定位的那个菜品的折扣率(rate)和减免金额
							StringBuilder rateStr = new StringBuilder();
							rateStr.append(" select coalesce(cd.rate,0) as rate,coalesce(cd.derate,0) as derate,bi.id,bi.item_id,bi.setmeal_id,bi.item_serial,bi.setmeal_rwid,bi.item_property,bi.item_remark,bi.discount_state,bi.item_amount,bi.item_price,bi.assist_money,bi.method_money,bi.item_count from pos_bill_item bi");
							rateStr.append(" left join hq_discount_case_details cd on bi.item_id=cd.item_id and bi.item_unit_name=cd.unit");
							rateStr.append(" left join hq_discount_case_org co on cd.discount_case_id=co.discount_case_id");
							rateStr.append(" where bi.bill_num=? and cd.discount_case_id =? and co.store_id=?");

							List<JSONObject> itemList = this.query4Json(tenantId, rateStr.toString(), new Object[]
							{ billNum, discountCaseId, storeId });

							List<Object[]> batchArgs = new ArrayList<Object[]>();
							for (JSONObject item : itemList)
							{
								if (!"MEALLIST".equalsIgnoreCase(item.optString("item_property")))
								{
									if ("Y".equalsIgnoreCase(item.optString("discount_state")))
									{
										if (item.optDouble("rate") != 0d && item.optDouble("rate") != 100d)
										{
											batchArgs.addAll(this.getDiscountRateAmount(item, itemList, item.optDouble("rate"), dishScale));
										}
										else if (item.optDouble("derate") > 0d)
										{
											batchArgs.addAll(this.getDiscountDerateAmount(item, itemList, dishScale));
										}
										else
										{
											batchArgs.addAll(this.getDiscountRateAmount(item, itemList, 100d, dishScale));
										}
									}
									else
									{
										batchArgs.addAll(this.getDiscountRateAmount(item, itemList, 100d, dishScale));
									}
								}
							}
							if (batchArgs.size() > 0)
							{
								jdbcTemplate.batchUpdate("update pos_bill_item set discount_rate=?,discount_amount=? where id = ?", batchArgs);
							}
							break;
						case "ZR03": // 折让
							StringBuffer sqlUpdate = new StringBuffer("update pos_bill_batch set discountr_amount = ? where bill_num = ? and store_id = ?");
							discountrAmount = rate_renate;
							this.jdbcTemplate.update(sqlUpdate.toString(), new Object[]
							{ discountrAmount, billNum, storeId });
							break;
						default:
							StringBuilder udcase = new StringBuilder("update pos_bill_item set discount_mode_id=?,discount_rate=?,discount_amount=? where tenancy_id=? and store_id = ? and bill_num = ? and batch_num=?");
							this.jdbcTemplate.update(udcase.toString(), new Object[]
							{ null, 100d, 0d, tenantId, storeId, billNum,batchNum });
							break;
					}
					break;
				case 4:// 4团体会员折扣
				case 5:// 5会员折扣
						// 更新账单表折扣率
					StringBuilder ucase = new StringBuilder("update pos_bill_batch set discount_rate = ? where bill_num = ? and store_id = ? ");
					this.jdbcTemplate.update(ucase.toString(), new Object[]
					{ discountRate, billNum, storeId });
					// 更新账单明细折扣率
					updateDiscountRateAmount(tenantId, storeId, billNum,batchNum, discountRate, false, dishScale);
					break;
				case 6:// 6会员价
					updateDiscountkVipPriceAmount(tenantId, storeId, billNum,batchNum, dishScale);
					break;
				default:
					/**
					 * 取消折扣 更新每个pos_bill_item 账单折扣
					 */
					StringBuffer sqlUpdate = new StringBuffer("update pos_bill_batch set discount_mode_id=?,discount_case_id=?,discount_num=?,discount_rate = ?,discountk_amount = ? where bill_num = ? and store_id = ?");
					this.jdbcTemplate.update(sqlUpdate.toString(), new Object[]
					{ null, null, null, 100d, 0d, billNum, storeId });

					StringBuffer sqlUpdateItem = new StringBuffer("update pos_bill_item set discount_mode_id=?,discount_rate=?,discount_amount=? where tenancy_id=? and store_id = ? and bill_num = ? and batch_num=?");
					this.jdbcTemplate.update(sqlUpdateItem.toString(), new Object[]
					{ null, 100d, 0d, tenantId, storeId, billNum,batchNum });
					break;
			}

			/**
			 * 计算折让金额 奉送菜品折让金额为0 退菜菜品折让金额为0
			 */
			StringBuilder updateItemDiscountSql = new StringBuilder("update pos_bill_item set discountr_amount = 0 where tenancy_id=? and store_id = ? and bill_num = ? and batch_num=?");
			jdbcTemplate.update(updateItemDiscountSql.toString(), new Object[]
			{ tenantId, storeId, billNum,batchNum });

			if (Tools.hv(discountrAmount) && discountrAmount > 0)
			{
				this.updateDiscountrAmount(tenantId, storeId, billNum,batchNum, discountrAmount, dishScale);
			}
			else
			{
				discountrAmount = 0d;
			}

			/**
			 * 计算服务费
			 */
			
			if (Tools.isNullOrEmpty(serviceId) == false && serviceId != 0 && SysDictionary.CHANEL_MD01.equals(channel))
			{
				serviceAmount =0d;
				double zuidi_xfe = 0d;
				double guding_jj = 0d;
				double fwfl = 0d;
				// double subtotal = 0d;
				String takenMode = "";

				StringBuilder sqls = new StringBuilder("select coalesce(zuidi_xfe,0) as zuidi_xfe,coalesce(guding_jj,0) as guding_jj,coalesce(fwfl,0) as fwfl,taken_mode,fee_type from hq_service_fee_type where id = ?");
				SqlRowSet rss = this.jdbcTemplate.queryForRowSet(sqls.toString(), new Object[]
				{ serviceId });
				while (rss.next())
				{
					zuidi_xfe = rss.getDouble("zuidi_xfe");
					guding_jj = rss.getDouble("guding_jj");
					fwfl = rss.getDouble("fwfl");
					takenMode = rss.getString("taken_mode");
				}

				if (zuidi_xfe > 0 && DoubleHelper.sub(zuidi_xfe, subtotal, defaultScale) > 0)
				{
					serviceAmount = DoubleHelper.sub(zuidi_xfe, subtotal, defaultScale);
				}

				if ("GD01".equals(takenMode))
				{
					serviceAmount = DoubleHelper.add(serviceAmount, guding_jj, billScale);
				}
				else if ("BL02".equals(takenMode))
				{
					serviceAmount = DoubleHelper.add(serviceAmount, Scm.pmui(subtotal, Scm.pdiv(fwfl, 100d)), billScale);
				}
				else if ("FS03".equals(takenMode))
				{
					StringBuilder itemSql = new StringBuilder("select coalesce(sum(item_count),0) as item_count from pos_bill_item where tenancy_id=? and store_id = ? and bill_num = ? and batch_num=?");
					SqlRowSet rsItem = jdbcTemplate.queryForRowSet(itemSql.toString(), new Object[]
					{ tenantId, storeId, billNum,batchNum });
					if (rsItem.next())
					{
						serviceAmount = DoubleHelper.add(serviceAmount, Scm.pmui(guding_jj, rsItem.getDouble("item_count")), billScale);
					}
				}
			}

			/**
			 * 统计折扣金额
			 */
			double discountkAmount = 0d;
			StringBuilder countBillItemSql = new StringBuilder("select coalesce(sum(bi.discount_amount),0) as discount_amount from pos_bill_item bi where bi.item_property <> 'MEALLIST' and bi.tenancy_id=? and bi.store_id = ? and bi.bill_num = ? and bi.batch_num=?");
			SqlRowSet rs = jdbcTemplate.queryForRowSet(countBillItemSql.toString(), new Object[]
			{ tenantId, storeId, billNum,batchNum });
			if (rs.next())
			{
				discountkAmount = rs.getDouble("discount_amount");
			}

			/**
			 * 计算奉送金额
			 */
			double giviAmount = 0d;
			StringBuilder countGiveAmountSql = new StringBuilder("select coalesce(sum(bi.item_amount),0) as givi_amount from pos_bill_item bi where bi.item_remark = 'FS02' and bi.item_property <> 'MEALLIST' and bi.tenancy_id=? and bi.store_id = ? and bi.bill_num = ? and bi.batch_num=?");
			rs = jdbcTemplate.queryForRowSet(countGiveAmountSql.toString(), new Object[]
			{ tenantId, storeId, billNum,batchNum });
			if (rs.next())
			{
				giviAmount = rs.getDouble("givi_amount");
			}

			// 账单金额 = 项目小计金额 + 服务费金额
			double billAmount = DoubleHelper.add(subtotal, serviceAmount, defaultScale);

			// 优惠金额 = 折扣金额 + 折让金额
			double discountAmount = DoubleHelper.add(discountrAmount, discountkAmount, defaultScale);

			// 进位前付款金额 = sum(账单明细.实付金额)+服务费 = 账单金额-优惠金额 - 奉送金额
			double paymentAmount = DoubleHelper.sub(billAmount, DoubleHelper.add(discountAmount, giviAmount, defaultScale), defaultScale);

			if (paymentAmount < 0)
			{
				paymentAmount = 0d;
			}
			// 根据账单小数取舍方式计算进位后付款金额
			String billPointType = this.getSysParameter(tenantId, storeId, "billpointtype");
			if(Tools.isNullOrEmpty(billPointType))
			{
				billPointType = "half_adjust";
			}

			double paymentAmount2 = 0d;
			switch (billPointType)
			{
				case "rounding":
					paymentAmount2 = DoubleHelper.roundDown(paymentAmount, billScale);
					break;
				case "one_adjust":
					paymentAmount2 = DoubleHelper.roundUp(paymentAmount, billScale);
					break;
				default:
					paymentAmount2 = DoubleHelper.round(paymentAmount, billScale);
					break;
			}

			// 抹零金额 = 进位后付款金额-进位前付款金额
			double malingAmount = DoubleHelper.sub(paymentAmount2, paymentAmount, dishScale);

			// 人均消费 = 付款金额/顾客数
			double averageAmount = DoubleHelper.div(paymentAmount2, guest, defaultScale);

//			double paymentAmount2 = 0d;
//			double malingAmount = 0d;
//			double averageAmount = 0d;
			
			StringBuilder updateBillSql = new StringBuilder("update pos_bill_batch set subtotal=?,service_amount=?,bill_amount=?,payment_amount=?,maling_amount=?,discountk_amount=?,discountr_amount=?,discount_amount=?,givi_amount=?,average_amount=? where tenancy_id=? and store_id = ? and bill_num = ? and batch_num=?");
			this.jdbcTemplate.update(updateBillSql.toString(), new Object[]
			{ subtotal,serviceAmount, billAmount, paymentAmount2, malingAmount, discountkAmount, discountrAmount, discountAmount, giviAmount, averageAmount, tenantId, storeId, billNum,batchNum });

			/**
			 * 计算抹零平摊金额 奉送菜品抹零平摊金额为0 退菜菜品抹零平摊金额为0
			 */
			if (Tools.hv(malingAmount) && malingAmount != 0d)
			{
				this.updateScrapAmount(tenantId, storeId, billNum, batchNum, malingAmount);
			}

			StringBuilder queryBillItemSql = new StringBuilder("select id,item_amount,discount_amount,single_discount_amount,discountr_amount,item_remark from pos_bill_item where tenancy_id=? and store_id = ? and bill_num = ? and batch_num=?");
			rs = jdbcTemplate.queryForRowSet(queryBillItemSql.toString(), new Object[]
			{ tenantId, storeId, billNum,batchNum });

			List<Object[]> batchItem = new ArrayList<Object[]>();
			while (rs.next())
			{
				// 实付金额=菜目金额+单品折扣-(折扣金额+折让金额)
				double realAmount = DoubleHelper.sub(DoubleHelper.add(rs.getDouble("item_amount"), rs.getDouble("single_discount_amount"), defaultScale), DoubleHelper.add(rs.getDouble("discount_amount"), rs.getDouble("discountr_amount"), defaultScale), defaultScale);

				if ("FS02".equalsIgnoreCase(rs.getString("item_remark")))
				{
					realAmount = 0d;
				}
				batchItem.add(new Object[]
				{ DoubleHelper.round(realAmount, dishScale), rs.getInt("id") });
			}
			if (batchItem.size() > 0)
			{
				jdbcTemplate.batchUpdate("update pos_bill_item set real_amount=? where id = ?", batchItem);
			}

			// 修改差额
			StringBuilder queryBillAmountSql = new StringBuilder(
					"select b.payment_amount,coalesce(sum(p.currency_amount),0) as currency_amount,coalesce(sum(p.more_coupon),0) as more_coupon from pos_bill_batch b left join pos_bill_payment p on b.store_id=p.store_id and b.bill_num=p.bill_num and b.batch_num=p.batch_num where b.tenancy_id=? and b.store_id=? and b.bill_num=? and b.batch_num=? group by b.bill_num,b.payment_amount");
			rs = this.query4SqlRowSet(queryBillAmountSql.toString(), new Object[]
			{ tenantId, storeId, billNum ,batchNum});
			double difference = 0d;
			if (rs.next())
			{
				difference = DoubleHelper.sub(rs.getDouble("payment_amount"), DoubleHelper.sub(rs.getDouble("currency_amount"), rs.getDouble("more_coupon"), 4), 4);
			}

			if (Tools.hv(difference))
			{
				String updateSql = new String("update pos_bill_batch set difference=? where tenancy_id=? and store_id = ? and bill_num = ? and batch_num=?");
				this.update(updateSql, new Object[]
				{ difference, tenantId, storeId, billNum ,batchNum });
			}
		}

		StringBuilder queryBillSql = new StringBuilder("select * from pos_bill where tenancy_id=? and store_id=? and bill_num=?"); 
		SqlRowSet rsGuest = this.query4SqlRowSet(queryBillSql.toString(), new Object[]{tenantId, storeId, billNum});
		double  billGuest = 1d;
		if(rsGuest.next())
		{
			billGuest = rsGuest.getDouble("guest");
		}
		
//		StringBuilder queryBillBatchSql = new StringBuilder("select * from pos_bill_batch where tenancy_id=? and store_id = ? and bill_num = ?");
//		List<JSONObject> batchList = this.query4Json(tenantId,queryBillBatchSql.toString(), new Object[]{tenantId, storeId, billNum ,batchNum});
//		
		double bill_service_amount=0d;
		double bill_subtotal=0d;
		double bill_bill_amount=0d;
		double bill_discountk_amount=0d;
		double bill_discountr_amount=0d;
		double bill_single_discount_amount=0d;
		double bill_discount_amount=0d;
		double bill_free_amount=0d;
		double bill_givi_amount=0d;
		double bill_more_coupon=0d;
		double bill_integraloffset=0d;
		double bill_return_amount=0d;
		double bill_payment_amount=0d;
		double bill_difference=0d;
		double bill_maling_amount=0d;
		double bill_average_amount=0d;
		
		StringBuilder queryBillBatchSql = new StringBuilder("select");
		queryBillBatchSql.append(" coalesce(sum(service_amount),0) as service_amount,");
		queryBillBatchSql.append(" coalesce(sum(subtotal),0) as subtotal,");
		queryBillBatchSql.append(" coalesce(sum(bill_amount),0) as bill_amount,");
		queryBillBatchSql.append(" coalesce(sum(payment_amount),0) as payment_amount,");
		queryBillBatchSql.append(" coalesce(sum(difference),0) as difference,");
		queryBillBatchSql.append(" coalesce(sum(discountk_amount),0) as discountk_amount,");
		queryBillBatchSql.append(" coalesce(sum(discountr_amount),0) as discountr_amount,");
		queryBillBatchSql.append(" coalesce(sum(maling_amount),0) as maling_amount,");
		queryBillBatchSql.append(" coalesce(sum(single_discount_amount),0) as single_discount_amount,");
		queryBillBatchSql.append(" coalesce(sum(discount_amount),0) as discount_amount,");
		queryBillBatchSql.append(" coalesce(sum(free_amount),0) as free_amount,");
		queryBillBatchSql.append(" coalesce(sum(givi_amount),0) as givi_amount,");
		queryBillBatchSql.append(" coalesce(sum(more_coupon),0) as more_coupon,");
		queryBillBatchSql.append(" coalesce(sum(integraloffset),0) as integraloffset,");
		queryBillBatchSql.append(" coalesce(sum(return_amount),0) as return_amount");
		queryBillBatchSql.append(" from pos_bill_batch where tenancy_id=? and store_id = ? and bill_num = ?");
		SqlRowSet rsBatch = this.query4SqlRowSet(queryBillBatchSql.toString(), new Object[]{tenantId, storeId, billNum});
		
		if(rsBatch.next())
		{
			 bill_service_amount=rsBatch.getDouble("service_amount");
			 bill_subtotal=rsBatch.getDouble("subtotal");
			 bill_bill_amount=rsBatch.getDouble("bill_amount");
			 bill_discountk_amount=rsBatch.getDouble("discountk_amount");
			 bill_discountr_amount=rsBatch.getDouble("discountr_amount");
			 bill_single_discount_amount=rsBatch.getDouble("single_discount_amount");
			 bill_discount_amount=rsBatch.getDouble("discount_amount");
			 bill_free_amount=rsBatch.getDouble("free_amount");
			 bill_givi_amount=rsBatch.getDouble("givi_amount");
			 bill_more_coupon=rsBatch.getDouble("more_coupon");
			 bill_integraloffset=rsBatch.getDouble("integraloffset");
			 bill_return_amount=rsBatch.getDouble("return_amount");
			 bill_payment_amount=rsBatch.getDouble("payment_amount");
			 bill_difference=rsBatch.getDouble("difference");
			 bill_maling_amount=rsBatch.getDouble("maling_amount");
			 bill_average_amount=DoubleHelper.div(bill_payment_amount, billGuest, defaultScale);
		}
		StringBuilder updateBillSql = new StringBuilder(
				"update pos_bill set service_amount=?,subtotal=?,bill_amount=?,payment_amount=?,discountk_amount=?,discountr_amount=?,maling_amount=?,single_discount_amount=?,discount_amount=?,free_amount=?,givi_amount=?,more_coupon=?,integraloffset=?,return_amount=?,difference=?,average_amount=? where tenancy_id=? and store_id = ? and bill_num = ?");
		
		this.update(updateBillSql.toString(), new Object[]
		{ bill_service_amount, bill_subtotal, bill_bill_amount, bill_payment_amount, bill_discountk_amount, bill_discountr_amount, bill_maling_amount, bill_single_discount_amount, bill_discount_amount, bill_free_amount, bill_givi_amount, bill_more_coupon, bill_integraloffset, bill_return_amount,
				bill_difference, bill_average_amount, tenantId, storeId, billNum });
	}
	
	/**
	 * @see com.tzx.pos.base.dao.BaseDao#calcAmount(java.lang.String,
	 *      java.lang.Integer, java.lang.String)
	 * <AUTHOR> 2016-01-13
	 */
	@Deprecated
	public void calcAmountForStandard(String tenantId, Integer storeId, String billNum) throws Exception
	{
		if (Tools.isNullOrEmpty(billNum))
		{
			return;
		}

		int dishScale = Integer.parseInt(this.getSysParameter(tenantId, storeId, "CMJEWS"));
		
		int billScale = Integer.parseInt(this.getSysParameter(tenantId, storeId, "ZDJEWS"));
		
		final int defaultScale = 4;

		/**
		 * 计算账单明细中菜目金额
		 */
		this.updateItemAmount(tenantId, storeId, billNum, dishScale);

		/**
		 * 计算账单菜目合计
		 */
//		String usubtotal = new String("update pos_bill set subtotal = (select sum(pbi.item_amount) from pos_bill_item pbi where pbi.bill_num = ? and pbi.store_id = ? and pbi.item_property <> 'MEALLIST') where bill_num = ? and store_id = ?");
//		this.jdbcTemplate.update(usubtotal, new Object[]
//		{ billNum, storeId, billNum, storeId });

		String billsql = new String("select guest,service_id,discount_mode_id,discount_rate,discount_case_id,discountr_amount,discountk_amount,source,service_amount,(select coalesce(sum(pbi.item_amount),0) from pos_bill_item pbi where pbi.tenancy_id = pb.tenancy_id and pbi.store_id = pb.store_id and pbi.bill_num = pb.bill_num and pbi.item_property <> ?) as subtotal from pos_bill pb where tenancy_id =? and store_id = ? and bill_num = ?");
		SqlRowSet rs = this.jdbcTemplate.queryForRowSet(billsql, new Object[]
		{SysDictionary.ITEM_PROPERTY_MEALLIST, tenantId, storeId,billNum });
		
		Double guest = null;
		Double subtotal = null;
		Double discountRate = null;
		Double discountrAmount = null;
		Double discountk_Amount = null;
		Double serviceAmount = null;
		Integer serviceId = null;
		Integer discountModeId = null;
		Integer discountCaseId = null;
		String channel = null;
		
		if (rs.next())
		{
			guest = rs.getDouble("guest");
			subtotal = rs.getDouble("subtotal");
			discountRate = rs.getDouble("discount_rate");
			discountrAmount = rs.getDouble("discountr_amount");
			discountk_Amount = rs.getDouble("discountk_amount");
			serviceAmount = rs.getDouble("service_amount");
			serviceId = rs.getInt("service_id");
			discountModeId = rs.getInt("discount_mode_id");
			discountCaseId = rs.getInt("discount_case_id");
			channel = rs.getString("source");
		}

		/**
		 * 判断菜目合计大于0,
		 */
		if (subtotal != null && subtotal == 0d)
		{
//			String updateB = new String(
//					"update pos_bill set service_id=null,service_amount=null,discountk_amount=null,discountr_amount=null,maling_amount=null,single_discount_amount=null,discount_amount=null,free_amount=null,givi_amount=null,more_coupon=null,average_amount=null,discount_case_id=null,discount_rate=null where bill_num = ? and store_id = ?");
//			this.jdbcTemplate.update(updateB, new Object[]
//			{ billNum, storeId });
			discountRate = null;
			discountCaseId = null;
			discountrAmount = null;
			serviceId = null;
			serviceAmount = 0d;
		}

		/**
		 * 计算折扣金额 奉送菜品折扣金额为0
		 */
		if (discountRate == null || discountRate == 0d)
		{
			discountRate = 100d;
		}

		switch (discountModeId)
		{
			case SysDictionary.DISCOUNT_MODE_1:
				updateDiscountRateAmount(tenantId, storeId, billNum, discountRate, true, dishScale);
				break;

			case SysDictionary.DISCOUNT_MODE_2:
				// hq_discount_case_details中取该菜品+规格定位的那个菜品的折扣率(rate)和减免金额
				StringBuilder rateStr = new StringBuilder();
				rateStr.append(" select coalesce(cd.rate,0) as rate,coalesce(cd.derate,0) as derate,bi.id,bi.item_id,bi.setmeal_id,bi.item_serial,bi.setmeal_rwid,bi.item_property,bi.item_remark,bi.discount_state,bi.item_amount,bi.item_price,bi.assist_money,bi.method_money,bi.item_count from pos_bill_item bi");
				rateStr.append(" left join hq_discount_case_details cd on bi.item_id=cd.item_id and bi.item_unit_name=cd.unit and cd.discount_case_id =?");
				rateStr.append(" where bi.bill_num=?  and bi.store_id=?");

				List<JSONObject> itemList = this.query4Json(tenantId, rateStr.toString(), new Object[]
				{discountCaseId, billNum, storeId });

				List<Object[]> batchArgs = new ArrayList<Object[]>();
				for (JSONObject item : itemList)
				{
					if (!"MEALLIST".equalsIgnoreCase(item.optString("item_property")))
					{
						if ("Y".equalsIgnoreCase(item.optString("discount_state")))
						{
							if (item.optDouble("derate") > 0d)
							{
								batchArgs.addAll(this.getDiscountDerateAmount(item, itemList, dishScale));
							}
							else
							{
								Double rate=item.optDouble("rate");
								if(null == rate || rate.isNaN() || rate==0d)
								{
									rate = 100d;
								}
								batchArgs.addAll(this.getDiscountRateAmount(item, itemList, rate, dishScale));
							}
						}
						else
						{
							batchArgs.addAll(this.getDiscountRateAmount(item, itemList, 100d, dishScale));
						}
					}
				}
				if (batchArgs.size() > 0)
				{
					jdbcTemplate.batchUpdate("update pos_bill_item set discount_rate=?,discount_amount=? where id = ?", batchArgs);
				}
				break;
			case SysDictionary.DISCOUNT_MODE_3:

				break;
			case SysDictionary.DISCOUNT_MODE_4:
				updateDiscountRateAmount(tenantId, storeId, billNum, discountRate, false, dishScale);
				break;
			case SysDictionary.DISCOUNT_MODE_5:
				updateDiscountRateAmount(tenantId, storeId, billNum, discountRate, false, dishScale);
				break;
			case SysDictionary.DISCOUNT_MODE_6:
				updateDiscountkVipPriceAmount(tenantId, storeId, billNum, dishScale);
				break;
			case SysDictionary.DISCOUNT_MODE_7:
				updateDiscountkAmount(tenantId, storeId, billNum, discountk_Amount, dishScale);
				break;
			case SysDictionary.DISCOUNT_MODE_8:
				updateDiscountkVipPriceAmount(tenantId, storeId, billNum, dishScale);
				
				StringBuilder rateStr8 = new StringBuilder();
				rateStr8.append(" select coalesce(cd.rate,0) as rate,coalesce(cd.derate,0) as derate,bi.id,bi.item_id,bi.setmeal_id,bi.item_serial,bi.setmeal_rwid,bi.item_property,bi.item_remark,bi.discount_state,bi.item_amount,bi.item_price,bi.assist_money,bi.method_money,bi.item_count,bi.discount_amount,bi.discountr_amount from pos_bill_item bi");
				rateStr8.append(" left join hq_discount_case_details cd on bi.item_id=cd.item_id and bi.item_unit_name=cd.unit and cd.discount_case_id =?");
//				rateStr.append(" left join hq_discount_case_org co on cd.discount_case_id=co.discount_case_id");
				rateStr8.append(" where bi.bill_num=? and bi.store_id=?");

				List<JSONObject> itemList8 = this.query4Json(tenantId, rateStr8.toString(), new Object[]
				{ discountCaseId,billNum,storeId });

				List<Object[]> batchArgs8 = new ArrayList<Object[]>();
				for (JSONObject item : itemList8)
				{
					if (!"MEALLIST".equalsIgnoreCase(item.optString("item_property")))
					{
						if ("Y".equalsIgnoreCase(item.optString("discount_state")))
						{
							if (item.optDouble("derate") > 0d)
							{
								batchArgs8.addAll(this.getDiscountDerateAmount2(item, itemList8, dishScale));
							}
							else
							{
								Double rate=item.optDouble("rate");
								if(null == rate || rate.isNaN() || rate==0d)
								{
									rate = 100d;
								}
								batchArgs8.addAll(this.getDiscountRateAmount2(item, itemList8, rate, dishScale));
							}
						}
						else
						{
							batchArgs8.addAll(this.getDiscountRateAmount2(item, itemList8, 100d, dishScale));
						}
					}
				}
				if (batchArgs8.size() > 0)
				{
					jdbcTemplate.batchUpdate("update pos_bill_item set discount_rate=?,discount_amount=? where id = ?", batchArgs8);
				}
				break;
			case SysDictionary.DISCOUNT_MODE_9:
				updateDiscountRateAmount(tenantId, storeId, billNum, discountRate, false, dishScale);
				break;
			case SysDictionary.DISCOUNT_MODE_10:
				
				break;
			default:
				/**
				 * 取消折扣 更新每个pos_bill_item 账单折扣
				 */
				StringBuffer sqlUpdate = new StringBuffer("update pos_bill set discount_mode_id=?,discount_case_id=?,discount_num=?,discount_rate = ?,discountk_amount = ? where bill_num = ? and store_id = ?");
				this.jdbcTemplate.update(sqlUpdate.toString(), new Object[]
				{ null, null, null, 100d, 0d, billNum, storeId });

				StringBuffer sqlUpdateItem = new StringBuffer("update pos_bill_item set discount_mode_id=?,discount_rate=?,discount_amount=? where bill_num = ? and store_id = ?");
				this.jdbcTemplate.update(sqlUpdateItem.toString(), new Object[]
				{ null, 100d, 0d, billNum, storeId });
				break;
		}
		
		StringBuilder qureyBillItem = new StringBuilder(
				"select id,item_id,setmeal_id,item_serial,setmeal_rwid,item_property,discount_state,item_remark,item_amount,item_price,assist_money,method_money,item_count,discount_mode_id,discount_rate from pos_bill_item where tenancy_id=? and store_id = ? and bill_num = ?");
		List<JSONObject> itemList = this.query4Json(tenantId, qureyBillItem.toString(), new Object[]
		{ tenantId, storeId, billNum });

		List<Object[]> batchArgs = new ArrayList<Object[]>();
		for (JSONObject item : itemList)
		{
			if (!"MEALLIST".equalsIgnoreCase(item.optString("item_property"))&&SysDictionary.DISCOUNT_MODE_10==item.optInt("discount_mode_id"))
			{
				double itemDiscountRate = item.optDouble("discount_rate");
				
				batchArgs.addAll(getDiscountRateAmount(item, itemList, itemDiscountRate, defaultScale));
			}
		}
		if (batchArgs.size() > 0)
		{
			jdbcTemplate.batchUpdate("update pos_bill_item set discount_rate=?,discount_amount=? where id = ?", batchArgs);
		}
		
//		switch (discountModeId)
//		{
//			case 1:// 1固定折扣
//				StringBuilder updateCaseSql = new StringBuilder("update pos_bill set discount_rate = ? where bill_num = ? and store_id = ? ");
//				this.jdbcTemplate.update(updateCaseSql.toString(), new Object[]
//				{ discountRate, billNum, storeId });
//				// 更新账单明细折扣率
//				updateDiscountRateAmount(tenantId, storeId, billNum, discountRate, true, dishScale);
//				break;
//			case 2:// 2非固定折扣
//				/**
//				 * 根据discount_case_id查找折扣类库,获得折扣类型和折扣率
//				 */
//				StringBuilder casestr = new StringBuilder("select hdc.discount_case_type,hdc.rate_renate from hq_discount_case as hdc left join hq_discount_case_org hdco on hdc.id = hdco.discount_case_id where hdc.id = ? and hdco.store_id = ? group by hdc.discount_case_type,hdc.rate_renate");
//				rs = this.jdbcTemplate.queryForRowSet(casestr.toString(), new Object[]
//				{ discountCaseId, storeId });
//
//				String discount_case_type = "";// 折扣类型
//				double rate_renate = 0;
//				if (rs.next())
//				{
//					discount_case_type = rs.getString("discount_case_type");
//					rate_renate = rs.getDouble("rate_renate");
//				}
//				switch (discount_case_type)
//				{
//					case "GD01": // 固定折扣
//						// 更新折扣率
//						StringBuilder ucase = new StringBuilder("update pos_bill set discount_rate = ? where bill_num = ? and store_id = ? ");
//						this.jdbcTemplate.update(ucase.toString(), new Object[]
//						{ rate_renate, billNum, storeId });
//						// 更新账单明细折扣率
//						updateDiscountRateAmount(tenantId, storeId, billNum, rate_renate, true, dishScale);
//						break;
//					case "FGD02": // 非固定折扣
//						// hq_discount_case_details中取该菜品+规格定位的那个菜品的折扣率(rate)和减免金额
//						StringBuilder rateStr = new StringBuilder();
//						rateStr.append(" select coalesce(cd.rate,0) as rate,coalesce(cd.derate,0) as derate,bi.id,bi.item_id,bi.setmeal_id,bi.item_serial,bi.setmeal_rwid,bi.item_property,bi.item_remark,bi.discount_state,bi.item_amount,bi.item_price,bi.assist_money,bi.method_money,bi.item_count from pos_bill_item bi");
//						rateStr.append(" left join hq_discount_case_details cd on bi.item_id=cd.item_id and bi.item_unit_name=cd.unit and cd.discount_case_id =?");
//						//rateStr.append(" left join hq_discount_case_org co on cd.discount_case_id=co.discount_case_id");
//						rateStr.append(" where bi.bill_num=?  and bi.store_id=?");
//
//						List<JSONObject> itemList = this.query4Json(tenantId, rateStr.toString(), new Object[]
//						{discountCaseId, billNum, storeId });
//
//						List<Object[]> batchArgs = new ArrayList<Object[]>();
//						for (JSONObject item : itemList)
//						{
//							if (!"MEALLIST".equalsIgnoreCase(item.optString("item_property")))
//							{
//								if ("Y".equalsIgnoreCase(item.optString("discount_state")))
//								{
//									if (item.optDouble("rate") != 0d && item.optDouble("rate") != 100d)
//									{
//										batchArgs.addAll(this.getDiscountRateAmount(item, itemList, item.optDouble("rate"), dishScale));
//									}
//									else if (item.optDouble("derate") > 0d)
//									{
//										batchArgs.addAll(this.getDiscountDerateAmount(item, itemList, dishScale));
//									}
//									else
//									{
//										batchArgs.addAll(this.getDiscountRateAmount(item, itemList, 100d, dishScale));
//									}
//								}
//								else
//								{
//									batchArgs.addAll(this.getDiscountRateAmount(item, itemList, 100d, dishScale));
//								}
//							}
//						}
//						if (batchArgs.size() > 0)
//						{
//							jdbcTemplate.batchUpdate("update pos_bill_item set discount_rate=?,discount_amount=? where id = ?", batchArgs);
//						}
//						break;
//					case "ZR03": // 折让
//						StringBuffer sqlUpdate = new StringBuffer("update pos_bill set discountr_amount = ? where bill_num = ? and store_id = ?");
//						discountrAmount = rate_renate;
//						this.jdbcTemplate.update(sqlUpdate.toString(), new Object[]
//						{ discountrAmount, billNum, storeId });
//						break;
//					default:
//						StringBuilder udcase = new StringBuilder("update pos_bill_item set discount_mode_id=?,discount_rate=?,discount_amount=? where bill_num = ? and store_id = ?");
//						this.jdbcTemplate.update(udcase.toString(), new Object[]
//						{ null, 100d, 0d, billNum, storeId });
//						break;
//				}
//				break;
//			case 3:// 3折扣折让
//				break;
//			case 4:// 4团体会员折扣
//			case 5:// 5会员折扣
//					// 更新账单表折扣率
//				StringBuilder ucase = new StringBuilder("update pos_bill set discount_rate = ? where bill_num = ? and store_id = ? ");
//				this.jdbcTemplate.update(ucase.toString(), new Object[]
//				{ discountRate, billNum, storeId });
//				// 更新账单明细折扣率
//				updateDiscountRateAmount(tenantId, storeId, billNum, discountRate, false, dishScale);
//				break;
//			case 6:// 6会员价
//				updateDiscountkVipPriceAmount(tenantId, storeId, billNum, dishScale);
//				break;
//			case 7://第三方平台优惠
//				updateDiscountkAmount(tenantId, storeId, billNum, discountk_Amount, dishScale);
//				break;
//			case 8://会员价+非固定折扣
//				updateDiscountkVipPriceAmount(tenantId, storeId, billNum, dishScale);
//				
//				StringBuilder rateStr = new StringBuilder();
//				rateStr.append(" select coalesce(cd.rate,0) as rate,coalesce(cd.derate,0) as derate,bi.id,bi.item_id,bi.setmeal_id,bi.item_serial,bi.setmeal_rwid,bi.item_property,bi.item_remark,bi.discount_state,bi.item_amount,bi.item_price,bi.assist_money,bi.method_money,bi.item_count,bi.discount_amount,bi.discountr_amount from pos_bill_item bi");
//				rateStr.append(" left join hq_discount_case_details cd on bi.item_id=cd.item_id and bi.item_unit_name=cd.unit and cd.discount_case_id =?");
////				rateStr.append(" left join hq_discount_case_org co on cd.discount_case_id=co.discount_case_id");
//				rateStr.append(" where bi.bill_num=? and bi.store_id=?");
//
//				List<JSONObject> itemList = this.query4Json(tenantId, rateStr.toString(), new Object[]
//				{ discountCaseId,billNum,storeId });
//
//				List<Object[]> batchArgs = new ArrayList<Object[]>();
//				for (JSONObject item : itemList)
//				{
//					if (!"MEALLIST".equalsIgnoreCase(item.optString("item_property")))
//					{
//						if ("Y".equalsIgnoreCase(item.optString("discount_state")))
//						{
//							if (item.optDouble("rate") != 0d && item.optDouble("rate") != 100d)
//							{
//								batchArgs.addAll(this.getDiscountRateAmount2(item, itemList, item.optDouble("rate"), dishScale));
//							}
//							else if (item.optDouble("derate") > 0d)
//							{
//								batchArgs.addAll(this.getDiscountDerateAmount2(item, itemList, dishScale));
//							}
//							else
//							{
//								batchArgs.addAll(this.getDiscountRateAmount2(item, itemList, 100d, dishScale));
//							}
//						}
//						else
//						{
//							batchArgs.addAll(this.getDiscountRateAmount2(item, itemList, 100d, dishScale));
//						}
//					}
//				}
//				if (batchArgs.size() > 0)
//				{
//					jdbcTemplate.batchUpdate("update pos_bill_item set discount_rate=?,discount_amount=? where id = ?", batchArgs);
//				}
//				
//				break;
//			default:
//				/**
//				 * 取消折扣 更新每个pos_bill_item 账单折扣
//				 */
//				StringBuffer sqlUpdate = new StringBuffer("update pos_bill set discount_mode_id=?,discount_case_id=?,discount_num=?,discount_rate = ?,discountk_amount = ? where bill_num = ? and store_id = ?");
//				this.jdbcTemplate.update(sqlUpdate.toString(), new Object[]
//				{ null, null, null, 100d, 0d, billNum, storeId });
//
//				StringBuffer sqlUpdateItem = new StringBuffer("update pos_bill_item set discount_mode_id=?,discount_rate=?,discount_amount=? where bill_num = ? and store_id = ?");
//				this.jdbcTemplate.update(sqlUpdateItem.toString(), new Object[]
//				{ null, 100d, 0d, billNum, storeId });
//				break;
//		}

		/**
		 * 计算折让金额 奉送菜品折让金额为0 退菜菜品折让金额为0
		 */
		StringBuilder updateItemDiscountSql = new StringBuilder("update pos_bill_item set discountr_amount = 0 where bill_num = ? and store_id = ? ");
		jdbcTemplate.update(updateItemDiscountSql.toString(), new Object[]
		{ billNum, storeId });

		if (Tools.hv(discountrAmount) && discountrAmount > 0)
		{
			this.updateDiscountrAmount(tenantId, storeId, billNum, discountrAmount, dishScale);
		}
		else
		{
			discountrAmount = 0d;
		}

		/**
		 * 计算服务费
		 */
		if(SysDictionary.CHANEL_MD01.equals(channel))
		{
			serviceAmount = 0d;
			
			if (Tools.isNullOrEmpty(serviceId) == false && serviceId != 0)
			{
				double zuidi_xfe = 0d;
				double guding_jj = 0d;
				double fwfl = 0d;
				// double subtotal = 0d;
				String takenMode = "";
	
				StringBuilder sqls = new StringBuilder("select coalesce(zuidi_xfe,0) as zuidi_xfe,coalesce(guding_jj,0) as guding_jj,coalesce(fwfl,0) as fwfl,taken_mode,fee_type from hq_service_fee_type where id = ?");
				SqlRowSet rss = this.jdbcTemplate.queryForRowSet(sqls.toString(), new Object[]
				{ serviceId });
				while (rss.next())
				{
					zuidi_xfe = rss.getDouble("zuidi_xfe");
					guding_jj = rss.getDouble("guding_jj");
					fwfl = rss.getDouble("fwfl");
					takenMode = rss.getString("taken_mode");
				}
	
				if (zuidi_xfe > 0 && DoubleHelper.sub(zuidi_xfe, subtotal, defaultScale) > 0)
				{
					serviceAmount = DoubleHelper.sub(zuidi_xfe, subtotal, defaultScale);
				}
	
				if ("GD01".equals(takenMode))
				{
					serviceAmount = DoubleHelper.add(serviceAmount, guding_jj, billScale);
				}
				else if ("BL02".equals(takenMode))
				{
					serviceAmount = DoubleHelper.add(serviceAmount, Scm.pmui(subtotal, Scm.pdiv(fwfl, 100d)), billScale);
				}
				else if ("FS03".equals(takenMode))
				{
					StringBuilder itemSql = new StringBuilder("select coalesce(sum(item_count),0) as item_count from pos_bill_item where bill_num=?");
					rs = jdbcTemplate.queryForRowSet(itemSql.toString(), new Object[]
					{ billNum });
					if (rs.next())
					{
						serviceAmount = DoubleHelper.add(serviceAmount, Scm.pmui(guding_jj, rs.getDouble("item_count")), billScale);
					}
				}
			}
		}

		/**
		 * 统计折扣金额
		 */
		double discountkAmount = 0d;
		StringBuilder countBillItemSql = new StringBuilder("select coalesce(sum(bi.discount_amount),0) as discount_amount from pos_bill_item bi where bi.item_property <> 'MEALLIST' and bi.bill_num = ? and bi.store_id = ?");
		rs = jdbcTemplate.queryForRowSet(countBillItemSql.toString(), new Object[]
		{ billNum, storeId });
		if (rs.next())
		{
			discountkAmount = rs.getDouble("discount_amount");
		}

		/**
		 * 计算奉送金额
		 */
		double giviAmount = 0d;
		StringBuilder countGiveAmountSql = new StringBuilder("select coalesce(sum(bi.item_amount),0) as givi_amount from pos_bill_item bi where bi.item_remark = 'FS02' and bi.item_property <> 'MEALLIST' and bi.bill_num = ? and bi.store_id = ?");
		rs = jdbcTemplate.queryForRowSet(countGiveAmountSql.toString(), new Object[]
		{ billNum, storeId });
		if (rs.next())
		{
			giviAmount = rs.getDouble("givi_amount");
		}

		// 账单金额 = 项目小计金额 + 服务费金额
		double billAmount = DoubleHelper.add(subtotal, serviceAmount, defaultScale);

		// 优惠金额 = 折扣金额 + 折让金额
		double discountAmount = DoubleHelper.add(discountrAmount, discountkAmount, defaultScale);

		// 进位前付款金额 = sum(账单明细.实付金额)+服务费 = 账单金额-优惠金额 - 奉送金额
		double paymentAmount = DoubleHelper.sub(billAmount, DoubleHelper.add(discountAmount, giviAmount, defaultScale), defaultScale);

		if (paymentAmount < 0)
		{
			paymentAmount = 0d;
		}
		// 根据账单小数取舍方式计算进位后付款金额
		String billPointType = this.getSysParameter(tenantId, storeId, "billpointtype");
		if(Tools.isNullOrEmpty(billPointType))
		{
			billPointType = "half_adjust";
		}

		double paymentAmount2 = 0d;
		switch (billPointType)
		{
			case "rounding":
				paymentAmount2 = DoubleHelper.roundDown(paymentAmount, billScale);
				break;
			case "one_adjust":
				paymentAmount2 = DoubleHelper.roundUp(paymentAmount, billScale);
				break;
			default:
				paymentAmount2 = DoubleHelper.round(paymentAmount, billScale);
				break;
		}

		// 抹零金额 = 进位后付款金额-进位前付款金额
		double malingAmount = DoubleHelper.sub(paymentAmount2, paymentAmount, dishScale);

		// 人均消费 = 付款金额/顾客数
		double averageAmount = DoubleHelper.div(paymentAmount2, guest, defaultScale);

		StringBuilder updateBillSql = new StringBuilder("update pos_bill set subtotal=?,service_amount=?,bill_amount=?,payment_amount=?,maling_amount=?,discountk_amount=?,discountr_amount=?,discount_amount=?,givi_amount=?,average_amount=? where bill_num = ? and store_id = ?");
		this.jdbcTemplate.update(updateBillSql.toString(), new Object[]
		{ subtotal,serviceAmount, billAmount, paymentAmount2, malingAmount, discountkAmount, discountrAmount, discountAmount, giviAmount, averageAmount, billNum, storeId });

		/**
		 * 计算抹零平摊金额 奉送菜品抹零平摊金额为0 退菜菜品抹零平摊金额为0
		 */
		StringBuilder updateItemScrapSql = new StringBuilder("update pos_bill_item set single_discount_amount = 0 where bill_num = ? and store_id = ? ");
		jdbcTemplate.update(updateItemScrapSql.toString(), new Object[]
		{ billNum, storeId });
		if (Tools.hv(malingAmount) && malingAmount != 0d)
		{
			this.updateScrapAmount(tenantId, storeId, billNum, malingAmount);
		}

		StringBuilder queryBillItemSql = new StringBuilder("select id,item_amount,discount_amount,single_discount_amount,discountr_amount,item_remark from pos_bill_item where bill_num = ? and store_id = ?");
		rs = jdbcTemplate.queryForRowSet(queryBillItemSql.toString(), new Object[]
		{ billNum, storeId });

		List<Object[]> batchItem = new ArrayList<Object[]>();
		while (rs.next())
		{
			// 实付金额=菜目金额+单品折扣-(折扣金额+折让金额)
			double realAmount = DoubleHelper.sub(DoubleHelper.add(rs.getDouble("item_amount"), rs.getDouble("single_discount_amount"), defaultScale), DoubleHelper.add(rs.getDouble("discount_amount"), rs.getDouble("discountr_amount"), defaultScale), defaultScale);

			if ("FS02".equalsIgnoreCase(rs.getString("item_remark")))
			{
				realAmount = 0d;
			}
			batchItem.add(new Object[]
			{ DoubleHelper.round(realAmount, dishScale), rs.getInt("id") });
		}
		if (batchItem.size() > 0)
		{
			jdbcTemplate.batchUpdate("update pos_bill_item set real_amount=? where id = ?", batchItem);
		}

		// 修改差额
		StringBuilder queryBillAmountSql = new StringBuilder(
				"select b.payment_amount,coalesce(sum(p.currency_amount),0) as currency_amount,coalesce(sum(p.more_coupon),0) as more_coupon from pos_bill b left join pos_bill_payment p on b.store_id=p.store_id and b.bill_num=p.bill_num where b.tenancy_id=? and b.store_id=? and b.bill_num=? group by b.bill_num,b.payment_amount");
		rs = this.query4SqlRowSet(queryBillAmountSql.toString(), new Object[]
		{ tenantId, storeId, billNum });
		double difference = 0d;
		if (rs.next())
		{
			difference = DoubleHelper.sub(rs.getDouble("payment_amount"), DoubleHelper.sub(rs.getDouble("currency_amount"), rs.getDouble("more_coupon"), 4), 4);
		}

		if (Tools.hv(difference))
		{
			String updateSql = new String("update pos_bill set difference=? where bill_num = ? and store_id = ? and tenancy_id = ?");
			this.update(updateSql, new Object[]
			{ difference, billNum, storeId, tenantId });
		}
	}

	/**
	 * 修改折扣率金额
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @param discountRate
	 * @param isNotState
	 *            不区分菜品是否允许折扣
	 * @throws Exception
	 */
	@Deprecated
	private void updateDiscountRateAmount(String tenantId, int storeId, String billNum, double discountRate, boolean isNotState, int scale) throws Exception
	{
		StringBuilder qureyBillItem = new StringBuilder("select id,item_id,setmeal_id,item_serial,setmeal_rwid,item_property,discount_state,item_remark,item_amount,item_price,assist_money,method_money,item_count from pos_bill_item where bill_num = ? and store_id = ?");
		List<JSONObject> itemList = this.query4Json(tenantId, qureyBillItem.toString(), new Object[]
		{ billNum, storeId });

		List<Object[]> batchArgs = new ArrayList<Object[]>();
		for (JSONObject item : itemList)
		{
			if (!"MEALLIST".equalsIgnoreCase(item.optString("item_property")))
			{
				double itemDiscountRate = 100d;
				if (isNotState || "Y".equalsIgnoreCase(item.optString("discount_state")))
				{
					itemDiscountRate = discountRate;
				}
				batchArgs.addAll(getDiscountRateAmount(item, itemList, itemDiscountRate, scale));
			}
		}
		if (batchArgs.size() > 0)
		{
			jdbcTemplate.batchUpdate("update pos_bill_item set discount_rate=?,discount_amount=? where id = ?", batchArgs);
		}
	}

	@Deprecated
	private void updateDiscountRateAmount(String tenantId, int storeId, String billNum,String batchNum, double discountRate, boolean isNotState, int scale) throws Exception
	{
		StringBuilder qureyBillItem = new StringBuilder(
				"select id,item_id,setmeal_id,item_serial,setmeal_rwid,item_property,discount_state,item_remark,item_amount,item_price,assist_money,method_money,item_count from pos_bill_item where tenancy_id=? and store_id = ? and bill_num = ? and batch_num=?");
		List<JSONObject> itemList = this.query4Json(tenantId, qureyBillItem.toString(), new Object[]
		{ tenantId, storeId, billNum, batchNum });

		List<Object[]> batchArgs = new ArrayList<Object[]>();
		for (JSONObject item : itemList)
		{
			if (!"MEALLIST".equalsIgnoreCase(item.optString("item_property")))
			{
				double itemDiscountRate = 100d;
				if (isNotState || "Y".equalsIgnoreCase(item.optString("discount_state")))
				{
					itemDiscountRate = discountRate;
				}
				batchArgs.addAll(getDiscountRateAmount(item, itemList, itemDiscountRate, scale));
			}
		}
		if (batchArgs.size() > 0)
		{
			jdbcTemplate.batchUpdate("update pos_bill_item set discount_rate=?,discount_amount=? where id = ?", batchArgs);
		}
	}
	
	/**
	 * 计算折扣率金额 奉送菜品折扣金额为0
	 * 
	 * @param item
	 * @param itemList
	 * @param itemDiscountRate
	 * @param scale
	 * @return
	 * @throws Exception
	 */
	@Deprecated
	private List<Object[]> getDiscountRateAmount(JSONObject item, List<JSONObject> itemList, double itemDiscountRate, int scale) throws Exception
	{
		final int defaultScale = 4;
		List<Object[]> batchArgs = new ArrayList<Object[]>();
		if (!"MEALLIST".equalsIgnoreCase(item.optString("item_property")))
		{
			if ("TC01".equals(item.optString("item_remark")) || "FS02".equals(item.optString("item_remark")) || "CJ05".equals(item.optString("item_remark")))
			{
				itemDiscountRate = 100d;
			}

			double rate = DoubleHelper.sub(1d, DoubleHelper.div(itemDiscountRate, 100d, defaultScale), defaultScale);
			double itemAmount = item.optDouble("item_amount");
			if (itemAmount == 0d)
			{
				itemAmount = DoubleHelper.add(DoubleHelper.mul(item.optDouble("item_price"), item.optDouble("item_count"), defaultScale), DoubleHelper.add(item.optDouble("method_money"), item.optDouble("assist_money"), defaultScale), defaultScale);
			}
			batchArgs.add(new Object[]
			{ itemDiscountRate, DoubleHelper.mul(itemAmount, rate, scale), item.optInt("id") });

			if ("SETMEAL".equalsIgnoreCase(item.optString("item_property")))
			{
				for (JSONObject detail : itemList)
				{
					if ("MEALLIST".equals(detail.optString("item_property")) && item.optInt("item_id") == detail.optInt("setmeal_id") && item.optInt("item_serial") == detail.optInt("setmeal_rwid"))
					{
						double detailItemAmount = detail.optDouble("item_amount");
						if (detailItemAmount == 0d)
						{
							detailItemAmount = DoubleHelper.add(DoubleHelper.mul(detail.optDouble("item_price"), detail.optDouble("item_count"), defaultScale), DoubleHelper.add(detail.optDouble("method_money"), detail.optDouble("assist_money"), defaultScale), defaultScale);
						}
						batchArgs.add(new Object[]
						{ itemDiscountRate, DoubleHelper.mul(detailItemAmount, rate, scale), detail.optInt("id") });
					}
				}
			}
		}
		return batchArgs;
	}

	/**
	 * 计算折扣率金额 奉送菜品折扣金额为0
	 * 
	 * @param item
	 * @param itemList
	 * @param itemDiscountRate
	 * @param scale
	 * @return
	 * @throws Exception
	 */
	@Deprecated
	private List<Object[]> getDiscountRateAmount2(JSONObject item, List<JSONObject> itemList, double itemDiscountRate, int scale) throws Exception
	{
		final int defaultScale = 4;
		List<Object[]> batchArgs = new ArrayList<Object[]>();
		if (!"MEALLIST".equalsIgnoreCase(item.optString("item_property")))
		{
			if ("TC01".equals(item.optString("item_remark")) || "FS02".equals(item.optString("item_remark")) || "CJ05".equals(item.optString("item_remark")))
			{
				itemDiscountRate = 100d;
			}

			double rate = DoubleHelper.sub(1d, DoubleHelper.div(itemDiscountRate, 100d, defaultScale), defaultScale);
			double itemAmount = item.optDouble("item_amount");
			double discountAmount = item.optDouble("discount_amount");
			double discountrAmount = item.optDouble("discountr_amount");
			
			itemAmount = DoubleHelper.sub(itemAmount, DoubleHelper.add(discountAmount, discountrAmount, scale), scale);
			
			batchArgs.add(new Object[]
			{ itemDiscountRate, DoubleHelper.add(discountAmount, DoubleHelper.mul(itemAmount, rate, scale), scale), item.optInt("id") });

			if ("SETMEAL".equalsIgnoreCase(item.optString("item_property")))
			{
				for (JSONObject detail : itemList)
				{
					if ("MEALLIST".equals(detail.optString("item_property")) && item.optInt("item_id") == detail.optInt("setmeal_id") && item.optInt("item_serial") == detail.optInt("setmeal_rwid"))
					{
						double detailItemAmount = detail.optDouble("item_amount");
						double detailDiscountAmount = detail.optDouble("discount_amount");
						double detailDiscountrAmount = detail.optDouble("discountr_amount");
						
						detailItemAmount = DoubleHelper.sub(detailItemAmount, DoubleHelper.add(detailDiscountAmount, detailDiscountrAmount, scale), scale);
						
						batchArgs.add(new Object[]
						{ itemDiscountRate, DoubleHelper.add(detailDiscountAmount, DoubleHelper.mul(detailItemAmount, rate, scale), scale) , detail.optInt("id") });
					}
				}
			}
		}
		return batchArgs;
	}
	
	/**
	 * 计算折扣减免金额 奉送菜品折扣金额为0
	 * 
	 * @param item
	 * @param itemList
	 * @param derateAmount
	 * @param scale
	 * @return
	 * @throws Exception
	 */
	@Deprecated
	private List<Object[]> getDiscountDerateAmount(JSONObject item, List<JSONObject> itemList, int scale) throws Exception
	{
		final int defaultScale = 4;
		List<Object[]> batchArgs = new ArrayList<Object[]>();
		if (!"MEALLIST".equalsIgnoreCase(item.optString("item_property")))
		{
			// 减免金额
			double itemDiscountAmount = DoubleHelper.mul(item.optDouble("derate"), item.optDouble("item_count"), scale);
			// 奉送菜品折扣金额为0
			if ("TC01".equalsIgnoreCase(item.optString("item_remark")) || "FS02".equalsIgnoreCase(item.optString("item_remark")) || "CJ05".equalsIgnoreCase(item.optString("item_remark")))
			{
				itemDiscountAmount = 0d;
			}
			batchArgs.add(new Object[]
			{ 100d, itemDiscountAmount, item.optInt("id") });

			if ("SETMEAL".equals(item.optString("item_property")))
			{ // 将减免金额平摊到套餐明细
				Integer maxId = null;
				double maxAmount = 0d;
				double sumItemdiscountAmount = 0d;
				double proportion = DoubleHelper.div(itemDiscountAmount, item.optDouble("item_amount"), defaultScale);

				List<JSONObject> detailList = new ArrayList<JSONObject>();
				for (JSONObject detail : itemList)
				{
					if ("MEALLIST".equals(detail.optString("item_property")) && item.optInt("item_id") == detail.optInt("setmeal_id") && item.optInt("item_serial") == detail.optInt("setmeal_rwid"))
					{
						double detailDiscountAmount = DoubleHelper.mul(detail.optDouble("item_amount"), proportion, scale);
						// 奉送菜品折扣金额为0
						if ("TC01".equalsIgnoreCase(detail.optString("item_remark")) || "FS02".equalsIgnoreCase(detail.optString("item_remark")) || "CJ05".equalsIgnoreCase(detail.optString("item_remark")))
						{
							detailDiscountAmount = 0d;
						}
						sumItemdiscountAmount = DoubleHelper.add(sumItemdiscountAmount, detailDiscountAmount, defaultScale);

						detail.put("discount_rate", 100);
						detail.put("discount_amount", detailDiscountAmount);
						detailList.add(detail);

						if (null == maxId || maxAmount < detail.optDouble("item_amount"))
						{
							maxId = detail.optInt("id");
							maxAmount = detail.optDouble("item_amount");
						}
					}
				}

				for (JSONObject detail : detailList)
				{
					double detailDiscountAmount = DoubleHelper.mul(detail.optDouble("item_amount"), proportion, scale);
					if (maxId == detail.optInt("id"))
					{
						detailDiscountAmount = DoubleHelper.add(detailDiscountAmount, DoubleHelper.sub(itemDiscountAmount, sumItemdiscountAmount, defaultScale), scale);
					}
					// 奉送菜品折扣金额为0
					if ("TC01".equalsIgnoreCase(detail.optString("item_remark")) || "FS02".equalsIgnoreCase(detail.optString("item_remark")) || "CJ05".equalsIgnoreCase(detail.optString("item_remark")))
					{
						detailDiscountAmount = 0d;
					}
					batchArgs.add(new Object[]
					{ 100d, detailDiscountAmount, detail.optInt("id") });
				}
			}
		}
		return batchArgs;
	}

	/**
	 * 计算折扣减免金额 奉送菜品折扣金额为0
	 * 
	 * @param item
	 * @param itemList
	 * @param derateAmount
	 * @param scale
	 * @return
	 * @throws Exception
	 */
	@Deprecated
	private List<Object[]> getDiscountDerateAmount2(JSONObject item, List<JSONObject> itemList, int scale) throws Exception
	{
		final int defaultScale = 4;
		List<Object[]> batchArgs = new ArrayList<Object[]>();
		if (!"MEALLIST".equalsIgnoreCase(item.optString("item_property")))
		{
			// 减免金额
			double itemDiscountAmount = DoubleHelper.mul(item.optDouble("derate"), item.optDouble("item_count"), scale);
			// 奉送菜品折扣金额为0
			if ("TC01".equalsIgnoreCase(item.optString("item_remark")) || "FS02".equalsIgnoreCase(item.optString("item_remark")) || "CJ05".equalsIgnoreCase(item.optString("item_remark")))
			{
				itemDiscountAmount = 0d;
			}
			
			double discountAmount = item.optDouble("discount_amount");
			batchArgs.add(new Object[]
			{ 100d, DoubleHelper.add(discountAmount, itemDiscountAmount, scale) , item.optInt("id") });

			if ("SETMEAL".equals(item.optString("item_property")))
			{ // 将减免金额平摊到套餐明细
				Integer maxId = null;
				double maxAmount = 0d;
				double sumItemdiscountAmount = 0d;
				double proportion = DoubleHelper.div(itemDiscountAmount, item.optDouble("item_amount"), defaultScale);

				List<JSONObject> detailList = new ArrayList<JSONObject>();
				for (JSONObject detail : itemList)
				{
					if ("MEALLIST".equals(detail.optString("item_property")) && item.optInt("item_id") == detail.optInt("setmeal_id") && item.optInt("item_serial") == detail.optInt("setmeal_rwid"))
					{
						double detailDiscountAmount = DoubleHelper.mul(detail.optDouble("item_amount"), proportion, scale);
						// 奉送菜品折扣金额为0
						if ("TC01".equalsIgnoreCase(detail.optString("item_remark")) || "FS02".equalsIgnoreCase(detail.optString("item_remark")) || "CJ05".equalsIgnoreCase(detail.optString("item_remark")))
						{
							detailDiscountAmount = 0d;
						}
						sumItemdiscountAmount = DoubleHelper.add(sumItemdiscountAmount, detailDiscountAmount, defaultScale);

						detail.put("discount_rate", 100);
						detail.put("discount_amount", detailDiscountAmount);
						detailList.add(detail);

						if (null == maxId || maxAmount < detail.optDouble("item_amount"))
						{
							maxId = detail.optInt("id");
							maxAmount = detail.optDouble("item_amount");
						}
					}
				}

				for (JSONObject detail : detailList)
				{
					double detailDiscountAmount = DoubleHelper.mul(detail.optDouble("item_amount"), proportion, scale);
					if (maxId == detail.optInt("id"))
					{
						detailDiscountAmount = DoubleHelper.add(detailDiscountAmount, DoubleHelper.sub(itemDiscountAmount, sumItemdiscountAmount, defaultScale), scale);
					}
					// 奉送菜品折扣金额为0
					if ("TC01".equalsIgnoreCase(detail.optString("item_remark")) || "FS02".equalsIgnoreCase(detail.optString("item_remark")) || "CJ05".equalsIgnoreCase(detail.optString("item_remark")))
					{
						detailDiscountAmount = 0d;
					}
					
					batchArgs.add(new Object[]
					{ 100d, DoubleHelper.add(detail.optDouble("discount_amount"), detailDiscountAmount, scale), detail.optInt("id") });
				}
			}
		}
		return batchArgs;
	}
	/**
	 * 修改账单明细菜目金额,及做法金额
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @param isThirdPrice
	 * @param scale
	 * @throws Exception
	 */
	@Deprecated
	private void updateItemAmount(String tenantId, int storeId, String billNum, int scale) throws Exception
	{
		final int defaultScale = 4;

		StringBuilder queryBillItemSql = new StringBuilder();
		queryBillItemSql
				.append(" select coalesce(zi.method_amount,0) as method_amount,coalesce(gd.makeup_money,0) as assist_amount, bi.id,bi.item_id,bi.setmeal_id,bi.item_serial,bi.setmeal_rwid,bi.item_property,bi.item_remark,bi.item_price,bi.assist_money,bi.method_money,bi.item_count,bi.third_price from pos_bill_item bi");
		queryBillItemSql.append(" left join (select gd.*,cd.id as details_id from hq_item_group_details gd left join hq_item_combo_details cd on gd.item_group_id = cd.details_id) gd on bi.assist_item_id=gd.details_id  and gd.item_id=bi.item_id and gd.item_unit_id=bi.item_unit_id");
		queryBillItemSql.append(" left join (select bill_num,rwid,coalesce(sum(amount),0) as method_amount from pos_zfkw_item group by bill_num,rwid) zi on bi.bill_num = zi.bill_num and bi.rwid=zi.rwid");
		queryBillItemSql.append(" where bi.bill_num=? and bi.store_id = ?");
		// queryBillItemSql.append(" group by bi.id,bi.item_id,bi.setmeal_id,bi.item_serial,bi.setmeal_rwid,bi.item_property,bi.item_remark,bi.item_price,bi.assist_money,bi.method_money,bi.item_count,bi.third_price");

		List<JSONObject> itemList = this.query4Json(tenantId, queryBillItemSql.toString(), new Object[]
		{ billNum, storeId });
		List<Object[]> batchArgs = new ArrayList<Object[]>();
		for (JSONObject item : itemList)
		{
			if (!"MEALLIST".equalsIgnoreCase(item.optString("item_property")))
			{
				double methodMoney = DoubleHelper.mul(item.optDouble("method_amount"), item.optDouble("item_count"), scale);
				double assistMoney = DoubleHelper.mul(item.optDouble("assist_amount"), item.optDouble("item_count"), scale);
				if ("SETMEAL".equalsIgnoreCase(item.optString("item_property")))
				{
					methodMoney = 0d;
					assistMoney = 0d;
					for (JSONObject detail : itemList)
					{
						if ("MEALLIST".equals(detail.optString("item_property")) && item.optInt("item_id") == detail.optInt("setmeal_id") && item.optInt("item_serial") == detail.optInt("setmeal_rwid") && item.optString("item_remark").equals(detail.optString("item_remark")))
						{
							double detailMethodMoney = DoubleHelper.mul(detail.optDouble("method_amount"), detail.optDouble("item_count"), scale);
							methodMoney = DoubleHelper.add(methodMoney, detailMethodMoney, scale);

							double detailAssistMoney = DoubleHelper.mul(detail.optDouble("assist_amount"), detail.optDouble("item_count"), scale);
							assistMoney = DoubleHelper.add(assistMoney, detailAssistMoney, scale);

							double itemAmount = DoubleHelper.add(DoubleHelper.mul(detail.optDouble("item_price"), detail.optDouble("item_count"), defaultScale), DoubleHelper.add(detailAssistMoney, detailMethodMoney, defaultScale), scale);

							batchArgs.add(new Object[]
							{ detailMethodMoney, detailAssistMoney, itemAmount, detail.optInt("id") });
						}
					}
				}

				double itemAmount = DoubleHelper.add(DoubleHelper.mul(item.optDouble("item_price"), item.optDouble("item_count"), defaultScale), DoubleHelper.add(assistMoney, methodMoney, defaultScale), scale);

				batchArgs.add(new Object[]
				{ methodMoney, assistMoney, itemAmount, item.optInt("id") });
			}
		}
		if (batchArgs.size() > 0)
		{
			StringBuilder updateBillItemSql = new StringBuilder("update pos_bill_item set method_money=?,assist_money=?,item_amount=?,discount_amount=0,discountr_amount=0,single_discount_amount=0 where id=?");
			this.jdbcTemplate.batchUpdate(updateBillItemSql.toString(), batchArgs);
		}
	}

	/**
	 * 获得折让金额discountr_amount然后分摊到各个明细里 奉送菜品折让金额为0 退菜菜品折让金额为0
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @param discountrAmount
	 * @param scale
	 * @throws Exception
	 */
	@Deprecated
	private void updateDiscountrAmount(String tenantId, int storeId, String billNum, double discountrAmount, int scale) throws Exception
	{
		final int defaultScale = 4;

		StringBuilder queryItemSql = new StringBuilder(
				"select id,item_id,setmeal_id,item_serial,setmeal_rwid,item_property,item_amount,item_count,return_count from pos_bill_item where coalesce(item_remark,'')<>'TC01' and coalesce(item_remark,'')<>'FS02' and coalesce(item_remark,'')<>'CJ05' and bill_num = ? and store_id = ?");
		List<JSONObject> itemList = this.query4Json(tenantId, queryItemSql.toString(), new Object[]
		{ billNum, storeId });

		double averageTotal = 0d;
		double subtotal = 0d; // 统计所有菜的折让金额
		Integer maxId = 0;
		double maxValue = 0d;

		// 取总钱数,最大面额和对应的rwid
		for (JSONObject item : itemList)
		{
			double itemAmount = item.optDouble("item_amount");
			if (Tools.hv(item.opt("return_count")) && item.optDouble("return_count") > 0d)
			{
				double itemPrice = DoubleHelper.div(item.optDouble("item_amount"), item.optDouble("item_count"), defaultScale);
				itemAmount = DoubleHelper.mul(itemPrice, DoubleHelper.sub(item.optDouble("item_count"), item.optDouble("return_count"), defaultScale), scale);
				item.put("item_amount", itemAmount);
			}

			if (!"MEALLIST".equalsIgnoreCase(item.optString("item_property")))
			{
				subtotal = DoubleHelper.add(subtotal, itemAmount, defaultScale);

				if (maxValue < itemAmount)
				{
					maxValue = itemAmount;
					maxId = item.optInt("id");
				}
			}
		}

		// 计算并平摊折让的钱
		for (JSONObject item : itemList)
		{
			if (!"MEALLIST".equalsIgnoreCase(item.optString("item_property")))
			{
				double itemDiscountrAmount = DoubleHelper.mul(DoubleHelper.div(item.optDouble("item_amount"), subtotal, defaultScale), discountrAmount, scale);
				averageTotal = DoubleHelper.add(averageTotal, itemDiscountrAmount, defaultScale);
			}
		}

		List<Object[]> batchArgs = new ArrayList<Object[]>();
		for (JSONObject item : itemList)
		{
			if (!"MEALLIST".equalsIgnoreCase(item.optString("item_property")))
			{
				double itemDiscountrAmount = DoubleHelper.mul(DoubleHelper.div(item.optDouble("item_amount"), subtotal, defaultScale), discountrAmount, scale);
				if (maxId == item.optInt("id"))
				{
					itemDiscountrAmount = DoubleHelper.add(itemDiscountrAmount, DoubleHelper.sub(discountrAmount, averageTotal, defaultScale), scale);
				}
				batchArgs.add(new Object[]
				{ itemDiscountrAmount, item.optInt("id") });

				if ("SETMEAL".equals(item.optString("item_property")))
				{ // 将减免金额平摊到套餐明细
					Integer dMaxId = null;
					double dmaxAmount = 0d;
					double sumAmount = 0d;

					List<JSONObject> detailList = new ArrayList<JSONObject>();
					for (JSONObject detail : itemList)
					{
						if ("MEALLIST".equals(detail.optString("item_property")) && item.optInt("item_id") == detail.optInt("setmeal_id") && item.optInt("item_serial") == detail.optInt("setmeal_rwid"))
						{
							detailList.add(detail);
							double detailDiscountrAmount = DoubleHelper.mul(DoubleHelper.div(detail.optDouble("item_amount"), item.optDouble("item_amount"), defaultScale), itemDiscountrAmount, scale);
							sumAmount = DoubleHelper.add(sumAmount, detailDiscountrAmount, defaultScale);

							if (null == dMaxId || dmaxAmount < detail.optDouble("item_amount"))
							{
								dMaxId = detail.optInt("id");
								dmaxAmount = detail.optDouble("item_amount");
							}
						}
					}

					for (JSONObject detail : detailList)
					{
						double detailDiscountrAmount = DoubleHelper.mul(DoubleHelper.div(detail.optDouble("item_amount"), item.optDouble("item_amount"), defaultScale), itemDiscountrAmount, scale);
						if (dMaxId == detail.optInt("id"))
						{
							detailDiscountrAmount = DoubleHelper.add(detailDiscountrAmount, DoubleHelper.sub(itemDiscountrAmount, sumAmount, defaultScale), scale);
						}
						batchArgs.add(new Object[]
						{ detailDiscountrAmount, detail.optInt("id") });
					}
				}
			}
		}
		// 更新折让金额
		if (batchArgs.size() > 0)
		{
			jdbcTemplate.batchUpdate("update pos_bill_item set discountr_amount = ? where id = ?", batchArgs);
		}
	}

	@Deprecated
	private void updateDiscountrAmount(String tenantId, int storeId, String billNum,String batchNum, double discountrAmount, int scale) throws Exception
	{
		final int defaultScale = 4;

		StringBuilder queryItemSql = new StringBuilder(
				"select id,item_id,setmeal_id,item_serial,setmeal_rwid,item_property,item_amount,item_count,return_count from pos_bill_item where coalesce(item_remark,'')<>'TC01' and coalesce(item_remark,'')<>'FS02' and coalesce(item_remark,'')<>'CJ05' and tenancy_id=? and store_id = ? and bill_num = ? and batch_num=?");
		List<JSONObject> itemList = this.query4Json(tenantId, queryItemSql.toString(), new Object[]
		{ tenantId, storeId, billNum, batchNum });

		double averageTotal = 0d;
		double subtotal = 0d; // 统计所有菜的折让金额
		Integer maxId = 0;
		double maxValue = 0d;

		// 取总钱数,最大面额和对应的rwid
		for (JSONObject item : itemList)
		{
			double itemAmount = item.optDouble("item_amount");
			if (Tools.hv(item.opt("return_count")) && item.optDouble("return_count") > 0d)
			{
				double itemPrice = DoubleHelper.div(item.optDouble("item_amount"), item.optDouble("item_count"), defaultScale);
				itemAmount = DoubleHelper.mul(itemPrice, DoubleHelper.sub(item.optDouble("item_count"), item.optDouble("return_count"), defaultScale), scale);
				item.put("item_amount", itemAmount);
			}

			if (!"MEALLIST".equalsIgnoreCase(item.optString("item_property")))
			{
				subtotal = DoubleHelper.add(subtotal, itemAmount, defaultScale);

				if (maxValue < itemAmount)
				{
					maxValue = itemAmount;
					maxId = item.optInt("id");
				}
			}
		}

		// 计算并平摊折让的钱
		for (JSONObject item : itemList)
		{
			if (!"MEALLIST".equalsIgnoreCase(item.optString("item_property")))
			{
				double itemDiscountrAmount = DoubleHelper.mul(DoubleHelper.div(item.optDouble("item_amount"), subtotal, defaultScale), discountrAmount, scale);
				averageTotal = DoubleHelper.add(averageTotal, itemDiscountrAmount, defaultScale);
			}
		}

		List<Object[]> batchArgs = new ArrayList<Object[]>();
		for (JSONObject item : itemList)
		{
			if (!"MEALLIST".equalsIgnoreCase(item.optString("item_property")))
			{
				double itemDiscountrAmount = DoubleHelper.mul(DoubleHelper.div(item.optDouble("item_amount"), subtotal, defaultScale), discountrAmount, scale);
				if (maxId == item.optInt("id"))
				{
					itemDiscountrAmount = DoubleHelper.add(itemDiscountrAmount, DoubleHelper.sub(discountrAmount, averageTotal, defaultScale), scale);
				}
				batchArgs.add(new Object[]
				{ itemDiscountrAmount, item.optInt("id") });

				if ("SETMEAL".equals(item.optString("item_property")))
				{ // 将减免金额平摊到套餐明细
					Integer dMaxId = null;
					double dmaxAmount = 0d;
					double sumAmount = 0d;

					List<JSONObject> detailList = new ArrayList<JSONObject>();
					for (JSONObject detail : itemList)
					{
						if ("MEALLIST".equals(detail.optString("item_property")) && item.optInt("item_id") == detail.optInt("setmeal_id") && item.optInt("item_serial") == detail.optInt("setmeal_rwid"))
						{
							detailList.add(detail);
							double detailDiscountrAmount = DoubleHelper.mul(DoubleHelper.div(detail.optDouble("item_amount"), item.optDouble("item_amount"), defaultScale), itemDiscountrAmount, scale);
							sumAmount = DoubleHelper.add(sumAmount, detailDiscountrAmount, defaultScale);

							if (null == dMaxId || dmaxAmount < detail.optDouble("item_amount"))
							{
								dMaxId = detail.optInt("id");
								dmaxAmount = detail.optDouble("item_amount");
							}
						}
					}

					for (JSONObject detail : detailList)
					{
						double detailDiscountrAmount = DoubleHelper.mul(DoubleHelper.div(detail.optDouble("item_amount"), item.optDouble("item_amount"), defaultScale), itemDiscountrAmount, scale);
						if (dMaxId == detail.optInt("id"))
						{
							detailDiscountrAmount = DoubleHelper.add(detailDiscountrAmount, DoubleHelper.sub(itemDiscountrAmount, sumAmount, defaultScale), scale);
						}
						batchArgs.add(new Object[]
						{ detailDiscountrAmount, detail.optInt("id") });
					}
				}
			}
		}
		// 更新折让金额
		if (batchArgs.size() > 0)
		{
			jdbcTemplate.batchUpdate("update pos_bill_item set discountr_amount = ? where id = ?", batchArgs);
		}
	}
	/**
	 * 修改抹零金额,放到菜目金额最大的菜品上 奉送菜品抹零平摊金额为0 退菜菜品抹零平摊金额为0
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @param scrapAmount
	 * @throws Exception
	 */
	@Deprecated
	private void updateScrapAmount(String tenantId, int storeId, String billNum, double scrapAmount) throws Exception
	{
		StringBuilder queryItemSql = new StringBuilder(
				"select id,item_id,setmeal_id,item_serial,setmeal_rwid,item_property,item_amount from pos_bill_item where coalesce(item_remark,'')<>'TC01' and coalesce(item_remark,'')<>'FS02' and coalesce(item_remark,'')<>'CJ05' and bill_num = ? and store_id = ?");
		List<JSONObject> itemList = this.query4Json(tenantId, queryItemSql.toString(), new Object[]
		{ billNum, storeId });

		if (itemList != null && itemList.size() > 0)
		{
			JSONObject maxJson = null;
			for (JSONObject item : itemList)
			{
				if (!"MEALLIST".equalsIgnoreCase(item.optString("item_property")))
				{
					if (null == maxJson || maxJson.optDouble("item_amount") < item.optDouble("item_amount"))
					{
						maxJson = item;
					}
				}
			}
			StringBuilder updateItemSql = new StringBuilder("update pos_bill_item set single_discount_amount=? where id = ?");
			if (Tools.hv(maxJson))
			{
				jdbcTemplate.update(updateItemSql.toString(), new Object[]
				{ scrapAmount, maxJson.optInt("id") });
			}

			if ("SETMEAL".equalsIgnoreCase(maxJson.optString("item_property")))
			{
				JSONObject detailMaxJson = null;
				for (JSONObject detail : itemList)
				{
					if ("MEALLIST".equals(detail.optString("item_property")) && maxJson.optInt("item_id") == detail.optInt("setmeal_id") && maxJson.optInt("item_serial") == detail.optInt("setmeal_rwid"))
					{
						if (null == detailMaxJson || detailMaxJson.optDouble("item_amount") < detail.optDouble("item_amount"))
						{
							detailMaxJson = detail;
						}
					}
				}
				if (Tools.hv(detailMaxJson))
				{
					jdbcTemplate.update(updateItemSql.toString(), new Object[]
					{ scrapAmount, detailMaxJson.optInt("id") });
				}
			}
		}
	}

	@Deprecated
	private void updateScrapAmount(String tenantId, int storeId, String billNum, String batchNum,double scrapAmount) throws Exception
	{
		StringBuilder updateItemScrapSql = new StringBuilder("update pos_bill_item set single_discount_amount = 0 where tenancy_id=? and store_id = ? and bill_num = ? and batch_num=?");
		jdbcTemplate.update(updateItemScrapSql.toString(), new Object[]
		{ tenantId, storeId, billNum,batchNum });
		
		StringBuilder queryItemSql = new StringBuilder(
				"select id,item_id,setmeal_id,item_serial,setmeal_rwid,item_property,item_amount from pos_bill_item where coalesce(item_remark,'')<>'TC01' and coalesce(item_remark,'')<>'FS02' and coalesce(item_remark,'')<>'CJ05' and tenancy_id=? and store_id = ? and bill_num = ? and batch_num=?");
		List<JSONObject> itemList = this.query4Json(tenantId, queryItemSql.toString(), new Object[]
		{ tenantId, storeId, billNum,batchNum });

		if (itemList != null && itemList.size() > 0)
		{
			JSONObject maxJson = null;
			for (JSONObject item : itemList)
			{
				if (!"MEALLIST".equalsIgnoreCase(item.optString("item_property")))
				{
					if (null == maxJson || maxJson.optDouble("item_amount") < item.optDouble("item_amount"))
					{
						maxJson = item;
					}
				}
			}
			StringBuilder updateItemSql = new StringBuilder("update pos_bill_item set single_discount_amount=? where id = ?");
			if (Tools.hv(maxJson))
			{
				jdbcTemplate.update(updateItemSql.toString(), new Object[]
				{ scrapAmount, maxJson.optInt("id") });
			}

			if ("SETMEAL".equalsIgnoreCase(maxJson.optString("item_property")))
			{
				JSONObject detailMaxJson = null;
				for (JSONObject detail : itemList)
				{
					if ("MEALLIST".equals(detail.optString("item_property")) && maxJson.optInt("item_id") == detail.optInt("setmeal_id") && maxJson.optInt("item_serial") == detail.optInt("setmeal_rwid"))
					{
						if (null == detailMaxJson || detailMaxJson.optDouble("item_amount") < detail.optDouble("item_amount"))
						{
							detailMaxJson = detail;
						}
					}
				}
				if (Tools.hv(detailMaxJson))
				{
					jdbcTemplate.update(updateItemSql.toString(), new Object[]
					{ scrapAmount, detailMaxJson.optInt("id") });
				}
			}
		}
	}
	/**
	 * 计算会员价,把会员价的差价放到优惠金额里
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @throws Exception
	 */
	@Deprecated
	private void updateDiscountkVipPriceAmount(String tenantId, int storeId, String billNum, int scale) throws Exception
	{
		final int defaultScale = 4;

		StringBuilder queryBillItemSql = new StringBuilder();
		queryBillItemSql.append(" select bi.id,bi.item_id,bi.setmeal_id,bi.item_serial,bi.setmeal_rwid,bi.item_property,bi.item_remark,bi.item_price,bi.assist_money,bi.method_money,bi.item_count,bi.item_amount,coalesce(bi.third_price,0) as third_price");
		queryBillItemSql.append(" from pos_bill_item bi where bi.bill_num=? and bi.store_id = ?");

		List<JSONObject> itemList = this.query4Json(tenantId, queryBillItemSql.toString(), new Object[]
		{ billNum, storeId });
		List<Object[]> batchArgs = new ArrayList<Object[]>();
		for (JSONObject item : itemList)
		{
			if (!"MEALLIST".equalsIgnoreCase(item.optString("item_property")))
			{
				double discountAmount = 0;
				if (item.optDouble("third_price") > 0 && item.optDouble("item_price") > item.optDouble("third_price"))
				{
					discountAmount = DoubleHelper.mul(DoubleHelper.sub(item.optDouble("item_price"), item.optDouble("third_price"), defaultScale), item.optDouble("item_count"), scale);
				}

				if ("TC01".equalsIgnoreCase(item.optString("item_remark")) || "FS02".equalsIgnoreCase(item.optString("item_remark")) || "CJ05".equalsIgnoreCase(item.optString("item_remark")))
				{
					discountAmount = 0d;
				}
				batchArgs.add(new Object[]
				{ 100d, discountAmount, item.optInt("id") });

				if (discountAmount > 0)
				{
					if ("SETMEAL".equals(item.optString("item_property")))
					{ // 将减免金额平摊到套餐明细
						Integer dMaxId = null;
						double dmaxAmount = 0d;
						double sumAmount = 0d;

						List<JSONObject> detailList = new ArrayList<JSONObject>();
						for (JSONObject detail : itemList)
						{
							if ("MEALLIST".equals(detail.optString("item_property")) && item.optInt("item_id") == detail.optInt("setmeal_id") && item.optInt("item_serial") == detail.optInt("setmeal_rwid"))
							{
								detailList.add(detail);
								double detailDiscountAmount = DoubleHelper.mul(DoubleHelper.div(detail.optDouble("item_amount"), item.optDouble("item_amount"), defaultScale), discountAmount, scale);
								sumAmount = DoubleHelper.add(sumAmount, detailDiscountAmount, defaultScale);

								if (null == dMaxId || dmaxAmount < detail.optDouble("item_amount"))
								{
									dMaxId = detail.optInt("id");
									dmaxAmount = detail.optDouble("item_amount");
								}
							}
						}

						for (JSONObject detail : detailList)
						{
							double detailDiscountAmount = DoubleHelper.mul(DoubleHelper.div(detail.optDouble("item_amount"), item.optDouble("item_amount"), defaultScale), discountAmount, scale);
							if (dMaxId == detail.optInt("id"))
							{
								detailDiscountAmount = DoubleHelper.add(detailDiscountAmount, DoubleHelper.sub(discountAmount, sumAmount, defaultScale), scale);
							}
							batchArgs.add(new Object[]
							{ 100d, detailDiscountAmount, detail.optInt("id") });
						}
					}
				}
			}
		}
		if (batchArgs.size() > 0)
		{
			StringBuilder updateBillItemSql = new StringBuilder("update pos_bill_item set discount_rate=?,discount_amount=? where id = ?");
			this.jdbcTemplate.batchUpdate(updateBillItemSql.toString(), batchArgs);
		}
	}

	@Deprecated
	private void updateDiscountkVipPriceAmount(String tenantId, int storeId, String billNum,String batchNum, int scale) throws Exception
	{
		final int defaultScale = 4;

		StringBuilder queryBillItemSql = new StringBuilder();
		queryBillItemSql.append(" select bi.id,bi.item_id,bi.setmeal_id,bi.item_serial,bi.setmeal_rwid,bi.item_property,bi.item_remark,bi.item_price,bi.assist_money,bi.method_money,bi.item_count,bi.item_amount,coalesce(bi.third_price,0) as third_price");
		queryBillItemSql.append(" from pos_bill_item bi where bi.tenancy_id=? and bi.store_id = ? and bi.bill_num = ? and bi.batch_num=?");

		List<JSONObject> itemList = this.query4Json(tenantId, queryBillItemSql.toString(), new Object[]
		{ tenantId, storeId, billNum,batchNum});
		List<Object[]> batchArgs = new ArrayList<Object[]>();
		for (JSONObject item : itemList)
		{
			if (!"MEALLIST".equalsIgnoreCase(item.optString("item_property")))
			{
				double discountAmount = 0;
				if (item.optDouble("third_price") > 0 && item.optDouble("item_price") > item.optDouble("third_price"))
				{
					discountAmount = DoubleHelper.mul(DoubleHelper.sub(item.optDouble("item_price"), item.optDouble("third_price"), defaultScale), item.optDouble("item_count"), scale);
				}

				if ("TC01".equalsIgnoreCase(item.optString("item_remark")) || "FS02".equalsIgnoreCase(item.optString("item_remark")) || "CJ05".equalsIgnoreCase(item.optString("item_remark")))
				{
					discountAmount = 0d;
				}
				batchArgs.add(new Object[]
				{ 100d, discountAmount, item.optInt("id") });

				if (discountAmount > 0)
				{
					if ("SETMEAL".equals(item.optString("item_property")))
					{ // 将减免金额平摊到套餐明细
						Integer dMaxId = null;
						double dmaxAmount = 0d;
						double sumAmount = 0d;

						List<JSONObject> detailList = new ArrayList<JSONObject>();
						for (JSONObject detail : itemList)
						{
							if ("MEALLIST".equals(detail.optString("item_property")) && item.optInt("item_id") == detail.optInt("setmeal_id") && item.optInt("item_serial") == detail.optInt("setmeal_rwid"))
							{
								detailList.add(detail);
								double detailDiscountAmount = DoubleHelper.mul(DoubleHelper.div(detail.optDouble("item_amount"), item.optDouble("item_amount"), defaultScale), discountAmount, scale);
								sumAmount = DoubleHelper.add(sumAmount, detailDiscountAmount, defaultScale);

								if (null == dMaxId || dmaxAmount < detail.optDouble("item_amount"))
								{
									dMaxId = detail.optInt("id");
									dmaxAmount = detail.optDouble("item_amount");
								}
							}
						}

						for (JSONObject detail : detailList)
						{
							double detailDiscountAmount = DoubleHelper.mul(DoubleHelper.div(detail.optDouble("item_amount"), item.optDouble("item_amount"), defaultScale), discountAmount, scale);
							if (dMaxId == detail.optInt("id"))
							{
								detailDiscountAmount = DoubleHelper.add(detailDiscountAmount, DoubleHelper.sub(discountAmount, sumAmount, defaultScale), scale);
							}
							batchArgs.add(new Object[]
							{ 100d, detailDiscountAmount, detail.optInt("id") });
						}
					}
				}
			}
		}
		if (batchArgs.size() > 0)
		{
			StringBuilder updateBillItemSql = new StringBuilder("update pos_bill_item set discount_rate=?,discount_amount=? where id = ?");
			this.jdbcTemplate.batchUpdate(updateBillItemSql.toString(), batchArgs);
		}
	}
	
	/** 第三方优惠平摊
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @param discountrAmount
	 * @param scale
	 * @throws Exception
	 */
	@Deprecated
	private void updateDiscountkAmount(String tenantId, int storeId, String billNum, double discountrAmount, int scale) throws Exception
	{
		final int defaultScale = 4;

		StringBuilder queryItemSql = new StringBuilder(
				"select id,item_id,setmeal_id,item_serial,setmeal_rwid,item_property,item_amount,item_count,return_count from pos_bill_item where coalesce(item_remark,'')<>'TC01' and coalesce(item_remark,'')<>'FS02' and coalesce(item_remark,'')<>'CJ05' and bill_num = ? and store_id = ?");
		List<JSONObject> itemList = this.query4Json(tenantId, queryItemSql.toString(), new Object[]
		{ billNum, storeId });

		double averageTotal = 0d;
		double subtotal = 0d; // 统计所有菜的折让金额
		Integer maxId = 0;
		double maxValue = 0d;

		// 取总钱数,最大面额和对应的rwid
		for (JSONObject item : itemList)
		{
			double itemAmount = item.optDouble("item_amount");
			if (Tools.hv(item.opt("return_count")) && item.optDouble("return_count") > 0d)
			{
				double itemPrice = DoubleHelper.div(item.optDouble("item_amount"), item.optDouble("item_count"), defaultScale);
				itemAmount = DoubleHelper.mul(itemPrice, DoubleHelper.sub(item.optDouble("item_count"), item.optDouble("return_count"), defaultScale), scale);
				item.put("item_amount", itemAmount);
			}

			if (!"MEALLIST".equalsIgnoreCase(item.optString("item_property")))
			{
				subtotal = DoubleHelper.add(subtotal, itemAmount, defaultScale);

				if (maxValue < itemAmount)
				{
					maxValue = itemAmount;
					maxId = item.optInt("id");
				}
			}
		}

		// 计算并平摊折让的钱
		for (JSONObject item : itemList)
		{
			if (!"MEALLIST".equalsIgnoreCase(item.optString("item_property")))
			{
				double itemDiscountrAmount = DoubleHelper.mul(DoubleHelper.div(item.optDouble("item_amount"), subtotal, defaultScale), discountrAmount, scale);
				averageTotal = DoubleHelper.add(averageTotal, itemDiscountrAmount, defaultScale);
			}
		}

		List<Object[]> batchArgs = new ArrayList<Object[]>();
		for (JSONObject item : itemList)
		{
			if (!"MEALLIST".equalsIgnoreCase(item.optString("item_property")))
			{
				double itemDiscountrAmount = DoubleHelper.mul(DoubleHelper.div(item.optDouble("item_amount"), subtotal, defaultScale), discountrAmount, scale);
				if (maxId == item.optInt("id"))
				{
					itemDiscountrAmount = DoubleHelper.add(itemDiscountrAmount, DoubleHelper.sub(discountrAmount, averageTotal, defaultScale), scale);
				}
				batchArgs.add(new Object[]
				{ itemDiscountrAmount, item.optInt("id") });

				if ("SETMEAL".equals(item.optString("item_property")))
				{ // 将减免金额平摊到套餐明细
					Integer dMaxId = null;
					double dmaxAmount = 0d;
					double sumAmount = 0d;

					List<JSONObject> detailList = new ArrayList<JSONObject>();
					for (JSONObject detail : itemList)
					{
						if ("MEALLIST".equals(detail.optString("item_property")) && item.optInt("item_id") == detail.optInt("setmeal_id") && item.optInt("item_serial") == detail.optInt("setmeal_rwid"))
						{
							detailList.add(detail);
							double detailDiscountrAmount = DoubleHelper.mul(DoubleHelper.div(detail.optDouble("item_amount"), item.optDouble("item_amount"), defaultScale), itemDiscountrAmount, scale);
							sumAmount = DoubleHelper.add(sumAmount, detailDiscountrAmount, defaultScale);

							if (null == dMaxId || dmaxAmount < detail.optDouble("item_amount"))
							{
								dMaxId = detail.optInt("id");
								dmaxAmount = detail.optDouble("item_amount");
							}
						}
					}

					for (JSONObject detail : detailList)
					{
						double detailDiscountrAmount = DoubleHelper.mul(DoubleHelper.div(detail.optDouble("item_amount"), item.optDouble("item_amount"), defaultScale), itemDiscountrAmount, scale);
						if (dMaxId == detail.optInt("id"))
						{
							detailDiscountrAmount = DoubleHelper.add(detailDiscountrAmount, DoubleHelper.sub(itemDiscountrAmount, sumAmount, defaultScale), scale);
						}
						batchArgs.add(new Object[]
						{ detailDiscountrAmount, detail.optInt("id") });
					}
				}
			}
		}
		// 更新折扣金额
		if (batchArgs.size() > 0)
		{
			jdbcTemplate.batchUpdate("update pos_bill_item set discount_amount = ? where id = ?", batchArgs);
		}
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	@Deprecated
	public void averageMaling(String billNum, Integer organId, double maLingAmount)
	{

		boolean isPlus = false;
		if (maLingAmount > 0)
		{
			isPlus = true;
		}
		else
		{
			maLingAmount = -maLingAmount;
		}

		// 更新账单明细的抹零金额
		String uItemAmount = new String("update pos_bill_item set real_amount=? where rwid = ? and store_id = ?");

		Integer cmj = Integer.parseInt(Constant.constantMap.get("CMJEWS"));
		String sql = new String("select real_amount,rwid from pos_bill_item where bill_num = ? and store_id = ? and (item_remark is null or item_remark='')");
		SqlRowSet rs = this.jdbcTemplate.queryForRowSet(sql, new Object[]
		{ billNum, organId });
		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		double total = 0;
		Integer maxRwid = 0;
		double maxValue = 0;
		double avertotal = 0;

		while (rs.next())
		{
			Map<String, Object> map = new HashMap<String, Object>();
			Integer rwid = rs.getInt("rwid"); // 账单详情 id
			double real_amount = rs.getDouble("real_amount"); // 实结金额

			real_amount = Scm.padd(real_amount, maLingAmount);

			if (maxValue < real_amount)
			{
				maxValue = real_amount;
				maxRwid = (Integer) map.get("rwid");
			}
			total = Scm.padd(total, real_amount);
			map.put("real_amount", real_amount);
			map.put("rwid", rwid);
			list.add(map);
		}
		this.jdbcTemplate.update(uItemAmount, new Object[]
		{ maxValue, maxRwid, organId });

		List<JSONObject> listObjs = new ArrayList<JSONObject>();
		// 计算并平摊折让的钱
		for (int k = 0; k < list.size(); k++)
		{
			Map<String, Object> map = list.get(k);
			double realAmount = (Double) map.get("real_amount");
			Integer rwid = (Integer) map.get("rwid");
			double attach = DoubleHelper.mul(DoubleHelper.div(maLingAmount, total, 4), realAmount, 4);

			if (maxValue < realAmount)
			{
				maxValue = realAmount;
				maxRwid = (Integer) map.get("rwid");
			}
			avertotal = Scm.padd(avertotal, attach); // 统计所有的平摊金额
			if (isPlus)
			{
				realAmount = Scm.padd(realAmount, attach);
			}
			else
			{
				realAmount = Scm.psub(realAmount, attach);
			}

			JSONObject obj = new JSONObject();
			obj.put("rwid", rwid);
			obj.put("real_amount", realAmount);
			listObjs.add(obj);
		}

		for (int j = 0; j < listObjs.size(); j++)
		{
			JSONObject mobj = listObjs.get(j);

			double realAmount = mobj.optDouble("real_amount");
			Integer rwid = mobj.optInt("rwid");

			if (rwid == maxRwid)
			{
				if (isPlus)
				{
					realAmount = DoubleHelper.sub(total, avertotal, cmj);
				}
				else
				{
					realAmount = DoubleHelper.add(total, avertotal, cmj);
				}
			}
			this.jdbcTemplate.update(uItemAmount, new Object[]
			{ realAmount, rwid, organId });
		}
	}

	@Override
	@Deprecated
	public void pushChef(String tenancyId, String billno, Integer organId, String oper, String rwids)
	{
		/**
		 * 送厨打
		 */
		StringBuilder sql = new StringBuilder("insert into pos_print_item(tenancy_id,store_id,table_code,table_name,bill_num,guest,item_num,item_name,item_english,item_unit_name,item_price,item_count,update_time,zfkwnr,item_property,");
		sql.append("print_property,item_remark,waiter_num,shift_name,printer_id,printer_name,report_id,print_tag,item_serial,print_code,table_property_id,table_property_tag,print_format) ");

		sql.append(" select item.tenancy_id,item.store_id,bill.table_code,table1.table_name,item.bill_num,bill.guest,dish.item_code,item.item_name,item.item_english,item.item_unit_name,item.item_price,item.item_count,CURRENT_TIMESTAMP(0) :: TIMESTAMP WITHOUT TIME ZONE ,'',item.item_property,'"
				+ oper + "',item.item_remark,");
		sql.append(" item.waiter_num,shift.name,classprint.printer_id, printer.name,0,'*',item.item_serial,item.rwid,table1.table_property_id,'',printformat.class_item_code from pos_bill_item as item left join pos_bill bill on item.bill_num=bill.bill_num and item.tenancy_id=bill.tenancy_id and item.store_id=bill.store_id ");
		sql.append(" left join duty_order shift on item.item_shift_id=shift.id left join tables_info table1 on bill.table_code=table1.table_code and table1.organ_id=bill.store_id left join hq_item_info dish on item.item_id=dish.id and dish.tenancy_id=item.tenancy_id ");
		sql.append(" left join hq_kvs_item printitem on dish.id=printitem.item_id and kvs_id in(select id from hq_kvs where tenancy_id=? and store_id=?)");
		sql.append(" left join hq_kvs_printer classprint on printitem.kvs_id=classprint.kvs_id left join hq_print_printer printer on classprint.printer_id=printer.id ");
		sql.append(" left join hq_print_printer_format printformat on printer.id=printformat.print_printer_id and class_item_code not in('1001','1002','1003','1004') ");
		sql.append(" where item.waitcall_tag = '0' and item.bill_num = ? and item.store_id = ? and item.rwid in (" + rwids + ")");

		this.jdbcTemplate.update(sql.toString(), new Object[]
		{ tenancyId, organId, billno, organId });
	}

	@Deprecated
	public void averMeal(String billNum, Integer organId, int rwid)
	{
		// 更新账单明细的抹零金额
		String uItemAmount = new String(" update pos_bill_item set real_amount=? where rwid = ? and bill_num = ? and store_id = ?");
		// 更新套餐明细的折扣和折让
		String uItemDis = new String("update pos_bill_item set discount_amount=?,discountr_amount=? where rwid = ? and bill_num = ? and store_id = ?");
		// 查询套餐的明细
		String sql = new String("select setmeal_id,setmeal_rwid,discount_amount,discountr_amount,real_amount,rwid from pos_bill_item where rwid = ? and bill_num = ? and store_id = ?");

		SqlRowSet rs = this.jdbcTemplate.queryForRowSet(sql, new Object[]
		{ rwid, billNum, organId });

		double maxValue = 0;
		int setmeal_id = 0;
		int setmeal_rwid = 0;
		double discount_amount = 0;
		double discountr_amount = 0;
		double discountAmount = 0;
		double setMealAmount = 0;

		double subDistAmount = 0;
		double subDistrAmount = 0;

		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

		while (rs.next())
		{
			setmeal_id = rs.getInt("setmeal_id");
			setmeal_rwid = rs.getInt("setmeal_rwid");
			discount_amount = rs.getDouble("discount_amount");
			discountr_amount = rs.getDouble("discountr_amount");
			setMealAmount = rs.getDouble("real_amount");
		}
		discountAmount = Scm.padd(discount_amount, discountr_amount);

		double total = 0;
		int maxRwid = 0;
		double avertotal = 0;
		// 查询套餐明细的明细
		String msql = new String("select real_amount,rwid,item_price,item_count,item_amount from pos_bill_item where setmeal_id = ? and setmeal_rwid = ? and item_property = 'MEALLIST' and bill_num = ? and store_id = ?");
		SqlRowSet rst;
		try
		{
			rst = this.query4SqlRowSet(msql, new Object[]
			{ setmeal_id, setmeal_rwid, billNum, organId });
			
			while (rst.next())
			{
				Map<String, Object> map = new HashMap<String, Object>();
				int rrwid = rst.getInt("rwid"); // 账单详情 id
				double item_amount = rst.getDouble("item_amount"); // 实结金额

				if (maxValue < item_amount)
				{
					maxValue = item_amount;
					maxRwid = rrwid;
				}
				total = Scm.padd(total, item_amount);
				map.put("item_amount", item_amount);
				map.put("rwid", rrwid);
				list.add(map);
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}

		List<JSONObject> listObjs = new ArrayList<JSONObject>();
		double moreAmount = 0;
		// 计算并平摊折让的钱
		for (int k = 0; k < list.size(); k++)
		{
			Map<String, Object> map = list.get(k);
			double itemAmount = (Double) map.get("item_amount");
			int lrwid = (Integer) map.get("rwid");
			// 金额平摊
			double attach = DoubleHelper.mul(DoubleHelper.div(itemAmount, total, 4), discountAmount, 4);
			// 折扣平摊
			double distAttach = DoubleHelper.mul(DoubleHelper.div(itemAmount, total, 4), discount_amount, 4);
			// 折让平摊
			double distrAttach = DoubleHelper.mul(DoubleHelper.div(itemAmount, total, 4), discountr_amount, 4);

			subDistAmount = Scm.padd(subDistAmount, distAttach);
			subDistrAmount = Scm.padd(subDistrAmount, distrAttach);

			avertotal = Scm.padd(avertotal, attach); // 统计所有的平摊金额

			itemAmount = Scm.psub(itemAmount, attach);
			moreAmount = Scm.padd(moreAmount, itemAmount);

			JSONObject obj = new JSONObject();
			obj.put("rwid", lrwid);
			obj.put("item_amount", itemAmount);
			obj.put("discount_amount", distAttach);
			obj.put("discountr_amount", distrAttach);
			listObjs.add(obj);
			this.jdbcTemplate.update(uItemDis, new Object[]
			{ distAttach, distrAttach, lrwid, billNum, organId });
		}

		double allAverAmount = 0;
		// 计算并平摊折让的钱
		for (int j = 0; j < listObjs.size(); j++)
		{
			JSONObject mobj = listObjs.get(j);

			double itemAmount = mobj.optDouble("item_amount");
			int mrwid = mobj.optInt("rwid");

			allAverAmount = Scm.padd(allAverAmount, itemAmount);

			if (mrwid == maxRwid)
			{
				maxValue = itemAmount;
			}
			this.jdbcTemplate.update(uItemAmount, new Object[]
			{ itemAmount, mrwid, billNum, organId });
		}
		double avr = Scm.psub(setMealAmount, allAverAmount);
		if (avr != 0)
		{
			// 把计算 的应付金额摊到最大的那个菜上
			this.jdbcTemplate.update(uItemAmount, new Object[]
			{ Scm.padd(maxValue, avr), maxRwid, billNum, organId });
		}

	}

	@Override
	public Date getReportDate(String tenantId, Integer storeId) throws Exception
	{
		String qReportDate = new String("select max(report_date) as report_date from pos_opt_state where content = ? and store_id = ? and tenancy_id = ?");
		SqlRowSet rst = this.query4SqlRowSet(qReportDate, new Object[]
		{ SysDictionary.OPT_STATE_DAYBEGAIN,storeId, tenantId });

		if (rst.next())
		{
			// 这段要判断取出的日期是否是null
			String date = rst.getString("report_date");
			if (StringUtils.isEmpty(date))
			{
				return new Date();
			}
			return DateUtil.parseDate(date);
		}
		else
		{
			return new Date();
		}
	}

	@Override
	public int getShiftId(String tenantId, Integer storeId) throws Exception
	{
		Integer shiftId = null;
		String qdcsql = new String("select count(dor.id) from duty_order dor left join duty_order_of_ogran dooo on dor.id = dooo.duty_order_id where dor.start_property = 'DR01' and dor.end_property = 'CR02' and dor.valid_state = '1' and dooo.organ_id = ?");
		int dutyCount = this.queryForInt(qdcsql, new Object[]{ storeId });

		if (dutyCount > 0)
		{
			String dcsql = new String("select dor.id,dor.start_time,dor.end_time from duty_order dor left join duty_order_of_ogran dooo on dor.id = dooo.duty_order_id where dor.start_property = 'DR01' and dor.end_property = 'CR02' and dor.valid_state = '1' and dooo.organ_id = ?");
			SqlRowSet dcrs = this.query4SqlRowSet(dcsql, new Object[]
			{ storeId });
			String startTime = null;
			String endTime = null;
			if (dcrs.next())
			{
				startTime = dcrs.getString("start_time");
				endTime = dcrs.getString("end_time");
			}
			if (endTime.length() < 5)
			{
				endTime = "0" + endTime;
			}
			String time2 = DateUtil.formatTime(new Date()).substring(0, 5);
			// 获得班次
			StringBuilder sql1 = new StringBuilder("select dor.id from duty_order dor left join duty_order_of_ogran dooo on dor.id = dooo.duty_order_id");
			if (startTime.compareTo(endTime) >= 0)
			{
				if (startTime.compareTo("23:59") <= 0 && time2.compareTo(startTime) > 0)
				{
					sql1.append(" where dor.start_time <= '" + time2 + "' and '23:59' >= '" + time2 + "' and dor.start_property = 'DR01' and dor.end_property = 'CR02' and dor.valid_state='1' and dooo.organ_id = ? ");
				}
				else if (time2.compareTo(endTime) < 0)
				{
					sql1.append(" where '" + endTime + "' >= '" + time2 + "' and dor.start_property = 'DR01' and dor.end_property = 'CR02' and dor.valid_state='1' and dooo.organ_id = ? ");
				}
				else
				{
					sql1.append(" where dor.start_time <= '" + time2 + "' and dor.end_time >= '" + time2 + "' and dor.valid_state='1' and dooo.organ_id = ? ");
				}

				SqlRowSet rsdt = this.query4SqlRowSet(sql1.toString(), new Object[]
				{ storeId });
				while (rsdt.next())
				{
					shiftId = rsdt.getInt("id");
					return shiftId;
				}

				if (Tools.isNullOrEmpty(shiftId))
				{
					throw new SystemException(PosErrorCode.NOT_USERFUL_SHIFT);
				}
			}
			else
			{
				throw SystemException.getInstance(PosErrorCode.NOT_USERFUL_SHIFT);
			}
		}
		else
		{
			String time1 = DateUtil.formatTime(new Date()).substring(0, 5);
		// 获得班次
		StringBuilder sql1 = new StringBuilder("select dor.id from duty_order dor left join duty_order_of_ogran dooo on dor.id = dooo.duty_order_id where '" + time1 + "' > dor.start_time and '" + time1 + "' < dor.end_time and dor.valid_state = '1' and dooo.organ_id = ?");
		SqlRowSet rsdt = this.query4SqlRowSet(sql1.toString(), new Object[]
				{ storeId });
		if (rsdt.next())
		{
			shiftId = rsdt.getInt("id");
			return shiftId;
		}

		if (Tools.isNullOrEmpty(shiftId))
		{
			throw new SystemException(PosErrorCode.NOT_USERFUL_SHIFT);
		}
	}
		return 0;
	}

	@Override
	public int getShiftId(String tenantId, Integer storeId, Date reportDate, String optNum, String posNum) throws Exception
	{
		int shiftId = 0;
		StringBuilder querySql = new StringBuilder();
		querySql.append("select os1.shift_id from pos_opt_state os1 left join pos_opt_state os2 on os1.tenancy_id=os2.tenancy_id and os1.store_id=os2.store_id and os1.report_date=os2.report_date ");
		//querySql.append("and os1.pos_num=os2.pos_num and os1.opt_num=os2.opt_num and os1.content=? and os2.content=? and os1.last_updatetime<os2.last_updatetime ");
		querySql.append("and os1.pos_num=os2.pos_num and os1.content=? and os2.content=? and os1.last_updatetime<os2.last_updatetime ");
		querySql.append("where os1.tenancy_id=? and os1.store_id=? and os1.report_date=? and os1.content=? and os1.tag='0' and os2.id is null ");
		//querySql.append("and ((os1.pos_num=? and os1.opt_num=?) or os1.pos_num in (select pos_num from pos_opt_state_devices where os1.tenancy_id = tenancy_id and os1.store_id= store_id and devices_num=? and is_valid='1')) ");
		querySql.append("and ((os1.pos_num=? ) or os1.pos_num in (select pos_num from pos_opt_state_devices where os1.tenancy_id = tenancy_id and os1.store_id= store_id and devices_num=? and is_valid='1')) ");
		SqlRowSet rs = this.jdbcTemplate.queryForRowSet(querySql.toString(), new Object[]
		{ SysDictionary.OPT_STATE_KSSY, SysDictionary.OPT_STATE_YYTC, tenantId, storeId, reportDate, SysDictionary.OPT_STATE_KSSY, posNum, posNum });
		if (rs.next())
		{
			shiftId = rs.getInt("shift_id");
		}
		
		if(0==shiftId)
		{
			shiftId = this.getShiftId(tenantId, storeId);
		}
		return shiftId;
	}
	
	@Override
	public boolean checkReportDate(String tenantId, Integer storeId, Date reportDate) throws Exception
	{
		String querySql = new String("select count(id) from pos_opt_state where tenancy_id=? and store_id = ? and content = ? and report_date = ? ");
		// 判断是否日始
		int startCount = this.queryForInt(querySql, new Object[]
		{ tenantId, storeId, SysDictionary.OPT_STATE_DAYBEGAIN,reportDate });
		if (startCount > 0)
		{
			// 判断是否打烊
			int endCount = this.queryForInt(querySql, new Object[]
			{ tenantId, storeId, SysDictionary.OPT_STATE_DAYEND,reportDate });
			if (endCount > 0)
			{
				throw SystemException.getInstance(PosErrorCode.ALREADY_EXIST_DAYEND_ERROR);
			}
		}
		else
		{
			throw SystemException.getInstance(PosErrorCode.NOT_EXIST_DAYBEGAIN_ERROR);
		}
		return true;
	}

	/**
	 * 新厨打方法 点菜
	 * 
	 * @param tenancyId
	 * @param billno
	 * @param organId
	 * @param printType
	 *            0:厨打，1：账单，2：会员
	 * @return
	 */
	@Override
	@Deprecated
	public List<Integer> orderChef(String tenancyId, String billno, Integer organId, String printType)
	{
		List<Integer> list = new ArrayList<Integer>();
		/**
		 * 获取当前的序列
		 */
		String qseq = new String("select currval('pos_print_id_seq'::regclass) ");

		Timestamp timestamp = DateUtil.currentTimestamp();
		String printProperty = "点菜";

		String tableCode = null;
		String qtCode = new String(" select table_code from pos_bill where bill_num = ? and store_id = ? ");

		SqlRowSet rs = this.jdbcTemplate.queryForRowSet(qtCode, new Object[]
		{ billno, organId });
		if (rs.next())
		{
			tableCode = rs.getString("table_code");
		}

		/**
		 * 用来给厨打分类，主要是往主表里要插入大类（热菜，凉菜等），整单
		 */
		StringBuilder sql1107 = new StringBuilder(" select bill.guest,bill.waiter_num,shift.name as shift_name,printformat.class_item_code as print_format,classprint.printer_id,printer.name as printer_name from pos_bill_item as item ");
		sql1107.append(" left join pos_bill bill on item.bill_num=bill.bill_num and item.tenancy_id=bill.tenancy_id and item.store_id=bill.store_id left join duty_order shift on item.item_shift_id=shift.id ");
		sql1107.append(" left join tables_info table1 on bill.table_code=table1.table_code and table1.organ_id=bill.store_id left join hq_item_info dish on item.item_id=dish.id and dish.tenancy_id=item.tenancy_id ");
		sql1107.append(" left join hq_kvs_item printitem on dish.id=printitem.item_id and kvs_id in (select hk.id from hq_kvs hk where hk.store_id= ? and hk.tenancy_id=?) ");
		sql1107.append(" left join hq_kvs_printer classprint on printitem.kvs_id=classprint.kvs_id left join hq_print_printer printer on classprint.printer_id=printer.id ");
		sql1107.append(" left join hq_print_printer_format printformat on printer.id=printformat.print_printer_id and class_item_code not in('1001','1002','1003','1004') ");
		sql1107.append(" where item.bill_num = ? and item.print_tag = '*' and item.store_id = ? and printformat.class_item_code = ? group by print_format,printer_id,printer_name,guest,shift_name,bill.waiter_num ");

		StringBuilder sql1106 = new StringBuilder(" select bill.guest,bill.waiter_num,shift.name as shift_name,item.rwid,printformat.class_item_code as print_format,classprint.printer_id,printer.name as printer_name from pos_bill_item as item ");
		sql1106.append(" left join pos_bill bill on item.bill_num=bill.bill_num and item.tenancy_id=bill.tenancy_id and item.store_id=bill.store_id left join duty_order shift on item.item_shift_id=shift.id ");
		sql1106.append(" left join tables_info table1 on bill.table_code=table1.table_code and table1.organ_id=bill.store_id left join hq_item_info dish on item.item_id=dish.id and dish.tenancy_id=item.tenancy_id ");
		sql1106.append(" left join hq_kvs_item printitem on dish.id=printitem.item_id and kvs_id in (select hk.id from hq_kvs hk where hk.store_id= ? and hk.tenancy_id=?) ");
		sql1106.append(" left join hq_kvs_printer classprint on printitem.kvs_id=classprint.kvs_id left join hq_print_printer printer on classprint.printer_id=printer.id ");
		sql1106.append(" left join hq_print_printer_format printformat on printer.id=printformat.print_printer_id and class_item_code not in('1001','1002','1003','1004') ");
		sql1106.append(" where item.bill_num = ? and item.print_tag = '*' and item.store_id = ? and printformat.class_item_code = ? ");

		/**
		 * 往主表里插入打印参数
		 */
		String insertMPrint = new String(" insert into pos_print (tenancy_id,store_id,print_type,print_format,print_property,printer_id,printer_name,bill_num,table_code,print_tag,remark,print_time,finish_time,guest,waiter_num,shift_name) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ");

		/**
		 * 往子表里插入菜品明细，用来打印
		 */
		StringBuilder sql = new StringBuilder(" insert into pos_print_list(tenancy_id,store_id,table_code,table_name,bill_num,item_num,item_name,item_english,item_unit_name,item_price,item_count,update_time,zfkwnr,item_property,");
		sql.append("item_remark,item_serial,print_code,parent_id,waitcall_tag,rwid) ");

		sql.append("select item.tenancy_id,item.store_id,bill.table_code,table1.table_name,item.bill_num,dish.item_code,item.item_name,item.item_english,item.item_unit_name,item.item_price,item.item_count,CURRENT_TIMESTAMP(0) :: TIMESTAMP WITHOUT TIME ZONE ,c.kwzf,item.item_property,item.item_remark,");
		sql.append("item.item_serial,item.rwid,?,item.waitcall_tag,item.rwid ");
		sql.append("from pos_bill_item as item left join pos_bill bill on item.bill_num=bill.bill_num and item.tenancy_id=bill.tenancy_id and item.store_id=bill.store_id ");

		sql.append(" left join duty_order shift on item.item_shift_id=shift.id left join tables_info table1 on bill.table_code=table1.table_code and table1.organ_id=bill.store_id left join hq_item_info dish on item.item_id=dish.id and dish.tenancy_id=item.tenancy_id ");
		sql.append(" left join hq_kvs_item printitem on dish.id=printitem.item_id and kvs_id in (select hk.id from hq_kvs hk where hk.tenancy_id=? and hk.store_id= ? )");
		sql.append(" left join hq_kvs_printer classprint on printitem.kvs_id=classprint.kvs_id left join hq_print_printer printer on classprint.printer_id=printer.id ");
		sql.append(" left join hq_print_printer_format printformat on printer.id=printformat.print_printer_id and class_item_code not in('1001','1002','1003','1004') ");

		sql.append(" left join (select array_to_string(array_accum(zfkw_name), ' ') as kwzf,zfkw.rwid from pos_zfkw_item zfkw where zfkw.bill_num = ? and zfkw.store_id=? group by zfkw.rwid) c on item.rwid = c.rwid ");

		sql.append(" where item.rwid = ? and item.bill_num = ? and item.store_id = ? and classprint.printer_id = ? and printformat.class_item_code = ? ");

		/**
		 * 查询新下单的菜品
		 */
		StringBuilder queryItem = new StringBuilder(
				"select item.rwid,item.tenancy_id,item.store_id,printformat.class_item_code as print_format,classprint.printer_id, printer.name as printer_name,item.bill_num,bill.table_code from pos_bill_item as item left join pos_bill bill on item.bill_num=bill.bill_num and item.tenancy_id=bill.tenancy_id and item.store_id=bill.store_id ");
		queryItem.append(" left join duty_order shift on item.item_shift_id=shift.id left join tables_info table1 on bill.table_code=table1.table_code and table1.organ_id=bill.store_id left join hq_item_info dish on item.item_id=dish.id and dish.tenancy_id=item.tenancy_id ");
		queryItem.append(" left join hq_kvs_item printitem on dish.id=printitem.item_id and kvs_id in (select hk.id from hq_kvs hk where hk.store_id= ? and hk.tenancy_id=?) left join hq_kvs_printer classprint on printitem.kvs_id=classprint.kvs_id ");
		queryItem.append(" left join hq_print_printer printer on classprint.printer_id=printer.id left join hq_print_printer_format printformat on printer.id=printformat.print_printer_id and class_item_code not in('1001','1002','1003','1004') ");
		queryItem.append(" where item.print_tag = '*' and item.bill_num = ? and item.store_id = ? and classprint.printer_id = ? and printformat.class_item_code = ? order by item.rwid asc ");

		if ("0".equalsIgnoreCase(printType))
		{
			List<String> printList = new ArrayList<String>();
			printList.add("1106");
			printList.add("1107");
			// 把新的id放到list，然后做为参数传到打印方法里

			for (int m = 0; m < printList.size(); m++)
			{
				// 打印类型，单切1106，整单1107
				String print_type = printList.get(m);

				if (StringUtils.isNotEmpty(print_type))
				{
					if ("1106".equalsIgnoreCase(print_type))
					{
						// 单切插入主表并记录主表的信息
						SqlRowSet rs1106 = this.jdbcTemplate.queryForRowSet(sql1106.toString(), new Object[]
						{ organId, tenancyId, billno, organId, print_type });
						while (rs1106.next())
						{
							String print_format = rs1106.getString("print_format");
							Integer printer_id = rs1106.getInt("printer_id");
							String printer_name = rs1106.getString("printer_name");
							Integer guest = rs1106.getInt("guest");
							String waiter_num = rs1106.getString("waiter_num");
							String shift_name = rs1106.getString("shift_name");

							Integer rwid = rs1106.getInt("rwid");

							// 存主表
							this.jdbcTemplate.update(insertMPrint, new Object[]
							{ tenancyId, organId, "0", print_format, printProperty, printer_id, printer_name, billno, tableCode, "*", null, timestamp, null, guest, waiter_num, shift_name });
							Integer pid = this.jdbcTemplate.queryForObject(qseq, Integer.class);
							list.add(pid);
							// 存次表明细
							this.jdbcTemplate.update(sql.toString(), new Object[]
							{ pid, tenancyId, organId, billno, organId, rwid, billno, organId, printer_id, print_format });
						}
					}
					if ("1107".equalsIgnoreCase(print_type))
					{
						// 单切插入主表并记录主表的信息
						SqlRowSet rsClass = this.jdbcTemplate.queryForRowSet(sql1107.toString(), new Object[]
						{ organId, tenancyId, billno, organId, print_type });
						while (rsClass.next())
						{
							Map<String, String> map = new HashMap<String, String>();

							String print_format = rsClass.getString("print_format");
							Integer printer_id = rsClass.getInt("printer_id");
							String printer_name = rsClass.getString("printer_name");
							Integer guest = rsClass.getInt("guest");
							String waiter_num = rsClass.getString("waiter_num");
							String shift_name = rsClass.getString("shift_name");

							map.put("print_format", print_format);
							map.put("printer_id", printer_id + "");
							map.put("printer_name", printer_name);

							// 存主表
							this.jdbcTemplate.update(insertMPrint, new Object[]
							{ tenancyId, organId, "0", print_format, printProperty, printer_id, printer_name, billno, tableCode, "*", null, timestamp, null, guest, waiter_num, shift_name });
							Integer pid = this.jdbcTemplate.queryForObject(qseq, Integer.class);
							list.add(pid);

							SqlRowSet rsi = this.jdbcTemplate.queryForRowSet(queryItem.toString(), new Object[]
							{ organId, tenancyId, billno, organId, printer_id, print_format });
							try
							{
								while (rsi.next())
								{
									Integer rwid = rsi.getInt("rwid");
									// 存次表明细
									this.jdbcTemplate.update(sql.toString(), new Object[]
									{ pid, tenancyId, organId, billno, organId, rwid, billno, organId, printer_id, print_format });
								}
							}
							catch (Exception se)
							{
								se.printStackTrace();
							}
						}
					}
				}
			}
		}
		return list;
	}

	@Override
	@Deprecated
	public void orderPrint(String tenancyId, String billno, Integer organId, List<Integer> list)
	{
		String qPrintC = new String("select id,print_format,printer_id,bill_num,printer_name from pos_print where id = ? and bill_num = ? and store_id = ? order by id asc");

		for (int i = 0; i < list.size(); i++)
		{
			int id = list.get(i);

			SqlRowSet rs = this.jdbcTemplate.queryForRowSet(qPrintC, new Object[]
			{ id, billno, organId });
			if (rs.next())
			{
				JSONObject obj = new JSONObject();
				obj.put("tenancy_id", tenancyId);
				obj.put("store_id", organId);
				obj.put("print_format", rs.getString("print_format"));
				obj.put("printer_id", rs.getInt("printer_id"));
				obj.put("bill_num", rs.getString("bill_num"));
				obj.put("printer_name", rs.getString("printer_name"));
				obj.put("id", rs.getInt("id"));

				NewQueueUtil.push(obj);
			}
		}
	}

	@Override
	@Deprecated
	public List<Integer> orderRetreat(String tenancyId, String billno, Integer organId, String oper, String operType, String rwids)
	{
		List<Integer> list = new ArrayList<Integer>();
		/**
		 * 获取当前的序列
		 */
		String qseq = new String("select currval('pos_print_id_seq'::regclass) ");

		StringBuilder retreatSql = new StringBuilder(" select bill.guest,bill.waiter_num,shift.name as shift_name,item.rwid,item.table_code,printformat.class_item_code as print_format,classprint.printer_id,printer.name as printer_name from pos_bill_item as item ");
		retreatSql.append(" left join pos_bill bill on item.bill_num=bill.bill_num and item.tenancy_id=bill.tenancy_id and item.store_id=bill.store_id left join duty_order shift on item.item_shift_id=shift.id ");
		retreatSql.append(" left join tables_info table1 on bill.table_code=table1.table_code and table1.organ_id=bill.store_id left join hq_item_info dish on item.item_id=dish.id and dish.tenancy_id=item.tenancy_id ");
		retreatSql.append(" left join hq_kvs_item printitem on dish.id=printitem.item_id and kvs_id in (select hk.id from hq_kvs hk where hk.store_id= ? and hk.tenancy_id=?) ");
		retreatSql.append(" left join hq_kvs_printer classprint on printitem.kvs_id=classprint.kvs_id left join hq_print_printer printer on classprint.printer_id=printer.id ");
		retreatSql.append(" left join hq_print_printer_format printformat on printer.id=printformat.print_printer_id and class_item_code not in('1001','1002','1003','1004') ");
		retreatSql.append(" where item.bill_num = ? and item.store_id = ? and item.rwid in (" + rwids + ")");

		/**
		 * 往子表里插入菜品明细，用来打印
		 */
		StringBuilder sql = new StringBuilder(" insert into pos_print_list(tenancy_id,store_id,table_code,table_name,bill_num,item_num,item_name,item_english,item_unit_name,item_price,item_count,update_time,zfkwnr,item_property,");
		sql.append("item_remark,item_serial,print_code,parent_id,waitcall_tag,rwid) ");

		sql.append("select item.tenancy_id,item.store_id,bill.table_code,table1.table_name,item.bill_num,dish.item_code,item.item_name,item.item_english,item.item_unit_name,item.item_price,item.item_count,CURRENT_TIMESTAMP(0) :: TIMESTAMP WITHOUT TIME ZONE ,c.kwzf,item.item_property,item.item_remark,");
		sql.append("item.item_serial,item.rwid,?,item.waitcall_tag,item.rwid ");
		sql.append("from pos_bill_item as item left join pos_bill bill on item.bill_num=bill.bill_num and item.tenancy_id=bill.tenancy_id and item.store_id=bill.store_id ");
		sql.append(" left join duty_order shift on item.item_shift_id=shift.id left join tables_info table1 on bill.table_code=table1.table_code and table1.organ_id=bill.store_id left join hq_item_info dish on item.item_id=dish.id and dish.tenancy_id=item.tenancy_id ");
		sql.append(" left join hq_kvs_item printitem on dish.id=printitem.item_id and kvs_id in (select hk.id from hq_kvs hk where hk.tenancy_id=? and hk.store_id= ? )");
		sql.append(" left join hq_kvs_printer classprint on printitem.kvs_id=classprint.kvs_id left join hq_print_printer printer on classprint.printer_id=printer.id ");
		sql.append(" left join hq_print_printer_format printformat on printer.id=printformat.print_printer_id and class_item_code not in('1001','1002','1003','1004') ");

		sql.append(" left join (select array_to_string(array_accum(zfkw_name), ' ') as kwzf,zfkw.rwid from pos_zfkw_item zfkw where zfkw.bill_num = ? and zfkw.store_id=? group by zfkw.rwid) c on item.rwid = c.rwid ");

		sql.append(" where item.rwid = ? and item.bill_num = ? and item.store_id = ? and classprint.printer_id = ? and printformat.class_item_code = ? ");

		/**
		 * 往主表里插入打印参数
		 */
		String insertMPrint = new String(" insert into pos_print (tenancy_id,store_id,print_type,print_format,print_property,printer_id,printer_name,bill_num,table_code,print_tag,remark,print_time,finish_time,guest,waiter_num,shift_name) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ");

		Timestamp timestamp = DateUtil.currentTimestamp();

		// 单切插入主表并记录主表的信息
		SqlRowSet rsRetreat = this.jdbcTemplate.queryForRowSet(retreatSql.toString(), new Object[]
		{ organId, tenancyId, billno, organId });
		while (rsRetreat.next())
		{
			String print_format = rsRetreat.getString("print_format");
			Integer printer_id = rsRetreat.getInt("printer_id");
			String printer_name = rsRetreat.getString("printer_name");
			Integer rwid = rsRetreat.getInt("rwid");
			String tableCode = rsRetreat.getString("table_code");
			Integer guest = rsRetreat.getInt("guest");
			String waiter_num = rsRetreat.getString("waiter_num");
			String shift_name = rsRetreat.getString("shift_name");

			// 存主表
			this.jdbcTemplate.update(insertMPrint, new Object[]
			{ tenancyId, organId, "0", print_format, oper, printer_id, printer_name, billno, tableCode, "*", operType, timestamp, null, guest, waiter_num, shift_name });
			Integer pid = this.jdbcTemplate.queryForObject(qseq, Integer.class);
			list.add(pid);
			// 存次表明细
			this.jdbcTemplate.update(sql.toString(), new Object[]
			{ pid, tenancyId, organId, billno, organId, rwid, billno, organId, printer_id, print_format });
		}
		// 退菜和转台 地理打印机要打印
		// 查询地理打印机
		String qDili = new String("select hpp.id,hpp.name,hppf.class_item_code from hq_print_printer hpp left join hq_print_printer_format hppf on hpp.id = hppf.print_printer_id where hpp.isdili = 'Y' and hpp.valid_state = '1' and hpp.store_id = ? ");

		StringBuilder imsql = new StringBuilder(" select bill.guest,bill.waiter_num,shift.name as shift_name,item.rwid,item.table_code from pos_bill_item as item  ");
		imsql.append(" left join pos_bill bill on item.bill_num=bill.bill_num and item.tenancy_id=bill.tenancy_id and item.store_id=bill.store_id left join duty_order shift on item.item_shift_id=shift.id ");
		imsql.append(" where item.bill_num = ? and item.store_id = ? and item.rwid in (" + rwids + ")");

		/**
		 * 往子表里插入菜品明细，用来打印
		 */
		StringBuilder isubSql = new StringBuilder(" insert into pos_print_list(tenancy_id,store_id,table_code,table_name,bill_num,item_num,item_name,item_english,item_unit_name,item_price,item_count,update_time,zfkwnr,item_property,");
		isubSql.append("item_remark,item_serial,print_code,parent_id,waitcall_tag,rwid) ");

		isubSql.append(" select item.tenancy_id,item.store_id,bill.table_code,table1.table_name,item.bill_num,dish.item_code,item.item_name,item.item_english,item.item_unit_name,item.item_price,item.item_count,CURRENT_TIMESTAMP(0) :: TIMESTAMP WITHOUT TIME ZONE ,'',item.item_property,item.item_remark,item.item_serial,item.rwid,?,item.waitcall_tag,item.rwid ");
		isubSql.append(" from pos_bill_item as item left join pos_bill bill on item.bill_num=bill.bill_num ");
		isubSql.append(" left join hq_item_info dish on item.item_id=dish.id left join tables_info table1 on bill.table_code=table1.table_code and bill.store_id = table1.organ_id ");
		isubSql.append(" where item.rwid = ? and item.bill_num = ? and item.store_id = ? ");

		SqlRowSet rs = this.jdbcTemplate.queryForRowSet(qDili, new Object[]
		{ organId });
		while (rs.next())
		{
			Integer printer_id = rs.getInt("id");
			String printer_name = rs.getString("name");
			String class_item_code = rs.getString("class_item_code");

			if ("TC01".equalsIgnoreCase(operType) || "ZT06".equalsIgnoreCase(operType))
			{
				// 单切插入主表并记录主表的信息
				SqlRowSet rsdili = this.jdbcTemplate.queryForRowSet(imsql.toString(), new Object[]
				{ billno, organId });
				while (rsdili.next())
				{
					Integer rwid = rsdili.getInt("rwid");
					String tableCode = rsdili.getString("table_code");
					Integer guest = rsdili.getInt("guest");
					String waiter_num = rsdili.getString("waiter_num");
					String shift_name = rsdili.getString("shift_name");

					// 存主表
					this.jdbcTemplate.update(insertMPrint, new Object[]
					{ tenancyId, organId, "0", class_item_code, oper, printer_id, printer_name, billno, tableCode, "*", operType, timestamp, null, guest, waiter_num, shift_name });
					Integer pid = this.jdbcTemplate.queryForObject(qseq, Integer.class);
					list.add(pid);
					// 存次表明细
					this.jdbcTemplate.update(isubSql.toString(), new Object[]
					{ pid, rwid, billno, organId });
				}
			}
		}

		return list;
	}

	@Override
	@Deprecated
	public List<Integer> orderOperChef(String tenancyId, String billno, Integer organId, String oper, String operType, String rwids)
	{
		List<Integer> list = new ArrayList<Integer>();
		/**
		 * 获取当前的序列
		 */
		String qseq = new String("select currval('pos_print_id_seq'::regclass) ");

		StringBuilder sql1106 = new StringBuilder(" select bill.guest,bill.waiter_num,shift.name as shift_name,item.rwid,item.table_code,printformat.class_item_code as print_format,classprint.printer_id,printer.name as printer_name from pos_bill_item as item ");
		sql1106.append(" left join pos_bill bill on item.bill_num=bill.bill_num and item.tenancy_id=bill.tenancy_id and item.store_id=bill.store_id left join duty_order shift on item.item_shift_id=shift.id ");
		sql1106.append(" left join tables_info table1 on bill.table_code=table1.table_code and table1.organ_id=bill.store_id left join hq_item_info dish on item.item_id=dish.id and dish.tenancy_id=item.tenancy_id ");
		sql1106.append(" left join hq_kvs_item printitem on dish.id=printitem.item_id and kvs_id in (select hk.id from hq_kvs hk where hk.store_id= ? and hk.tenancy_id=?) ");
		sql1106.append(" left join hq_kvs_printer classprint on printitem.kvs_id=classprint.kvs_id left join hq_print_printer printer on classprint.printer_id=printer.id ");
		sql1106.append(" left join hq_print_printer_format printformat on printer.id=printformat.print_printer_id and class_item_code not in('1001','1002','1003','1004') ");
		sql1106.append(" where item.bill_num = ? and item.store_id = ? and printformat.class_item_code = ? and item.rwid in (" + rwids + ")");

		/**
		 * 往子表里插入菜品明细，用来打印
		 */
		StringBuilder sql = new StringBuilder(" insert into pos_print_list(tenancy_id,store_id,table_code,table_name,bill_num,item_num,item_name,item_english,item_unit_name,item_price,item_count,update_time,zfkwnr,item_property,");
		sql.append("item_remark,item_serial,print_code,parent_id,waitcall_tag,rwid) ");

		sql.append("select item.tenancy_id,item.store_id,bill.table_code,table1.table_name,item.bill_num,dish.item_code,item.item_name,item.item_english,item.item_unit_name,item.item_price,item.item_count,CURRENT_TIMESTAMP(0) :: TIMESTAMP WITHOUT TIME ZONE ,c.kwzf,item.item_property,item.item_remark,");
		sql.append("item.item_serial,item.rwid,?,item.waitcall_tag,item.rwid ");
		sql.append("from pos_bill_item as item left join pos_bill bill on item.bill_num=bill.bill_num and item.tenancy_id=bill.tenancy_id and item.store_id=bill.store_id ");
		sql.append(" left join duty_order shift on item.item_shift_id=shift.id left join tables_info table1 on bill.table_code=table1.table_code and table1.organ_id=bill.store_id left join hq_item_info dish on item.item_id=dish.id and dish.tenancy_id=item.tenancy_id ");
		sql.append(" left join hq_kvs_item printitem on dish.id=printitem.item_id and kvs_id in (select hk.id from hq_kvs hk where hk.tenancy_id=? and hk.store_id= ? )");
		sql.append(" left join hq_kvs_printer classprint on printitem.kvs_id=classprint.kvs_id left join hq_print_printer printer on classprint.printer_id=printer.id ");
		sql.append(" left join hq_print_printer_format printformat on printer.id=printformat.print_printer_id and class_item_code not in('1001','1002','1003','1004') ");

		sql.append(" left join (select array_to_string(array_accum(zfkw_name), ' ') as kwzf,zfkw.rwid from pos_zfkw_item zfkw where zfkw.bill_num = ? and zfkw.store_id=? group by zfkw.rwid) c on item.rwid = c.rwid ");

		sql.append(" where item.rwid = ? and item.bill_num = ? and item.store_id = ? and classprint.printer_id = ? and printformat.class_item_code = ? ");

		/**
		 * 往主表里插入打印参数
		 */
		String insertMPrint = new String(" insert into pos_print (tenancy_id,store_id,print_type,print_format,print_property,printer_id,printer_name,bill_num,table_code,print_tag,remark,print_time,finish_time,guest,waiter_num,shift_name) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ");

		Timestamp timestamp = DateUtil.currentTimestamp();

		// 单切插入主表并记录主表的信息
		SqlRowSet rs1106 = this.jdbcTemplate.queryForRowSet(sql1106.toString(), new Object[]
		{ organId, tenancyId, billno, organId, "1106" });
		while (rs1106.next())
		{
			String print_format = rs1106.getString("print_format");
			Integer printer_id = rs1106.getInt("printer_id");
			String printer_name = rs1106.getString("printer_name");
			Integer rwid = rs1106.getInt("rwid");
			String tableCode = rs1106.getString("table_code");
			Integer guest = rs1106.getInt("guest");
			String waiter_num = rs1106.getString("waiter_num");
			String shift_name = rs1106.getString("shift_name");

			// 存主表
			this.jdbcTemplate.update(insertMPrint, new Object[]
			{ tenancyId, organId, "0", print_format, oper, printer_id, printer_name, billno, tableCode, "*", operType, timestamp, null, guest, waiter_num, shift_name });
			Integer pid = this.jdbcTemplate.queryForObject(qseq, Integer.class);
			list.add(pid);
			// 存次表明细
			this.jdbcTemplate.update(sql.toString(), new Object[]
			{ pid, tenancyId, organId, billno, organId, rwid, billno, organId, printer_id, print_format });
		}
		// 退菜和转台 地理打印机要打印
		// 查询地理打印机
		String qDili = new String("select id,name from hq_print_printer where isdili = 'Y' and valid_state = '1' and store_id = ? ");

		StringBuilder imsql = new StringBuilder(" select bill.guest,bill.waiter_num,shift.name as shift_name,item.rwid,item.table_code from pos_bill_item as item  ");
		imsql.append(" left join pos_bill bill on item.bill_num=bill.bill_num and item.tenancy_id=bill.tenancy_id and item.store_id=bill.store_id left join duty_order shift on item.item_shift_id=shift.id ");
		imsql.append(" where item.bill_num = ? and item.store_id = ? and item.rwid in (" + rwids + ")");

		/**
		 * 往子表里插入菜品明细，用来打印
		 */
		StringBuilder isubSql = new StringBuilder(" insert into pos_print_list(tenancy_id,store_id,table_code,table_name,bill_num,item_num,item_name,item_english,item_unit_name,item_price,item_count,update_time,zfkwnr,item_property,");
		isubSql.append("item_remark,item_serial,print_code,parent_id,waitcall_tag,rwid) ");

		isubSql.append(" select item.tenancy_id,item.store_id,bill.table_code,table1.table_name,item.bill_num,dish.item_code,item.item_name,item.item_english,item.item_unit_name,item.item_price,item.item_count,CURRENT_TIMESTAMP(0) :: TIMESTAMP WITHOUT TIME ZONE ,'',item.item_property,item.item_remark,item.item_serial,item.rwid,?,item.waitcall_tag,item.rwid ");
		isubSql.append(" from pos_bill_item as item left join pos_bill bill on item.bill_num=bill.bill_num ");
		isubSql.append(" left join hq_item_info dish on item.item_id=dish.id left join tables_info table1 on bill.table_code=table1.table_code and bill.store_id = table1.organ_id ");
		isubSql.append(" where item.rwid = ? and item.bill_num = ? and item.store_id = ? ");

		SqlRowSet rs = this.jdbcTemplate.queryForRowSet(qDili, new Object[]
		{ organId });
		while (rs.next())
		{
			Integer printer_id = rs.getInt("id");
			String printer_name = rs.getString("name");
			if ("TC01".equalsIgnoreCase(operType) || "ZT06".equalsIgnoreCase(operType))
			{
				// 单切插入主表并记录主表的信息
				SqlRowSet rsdili = this.jdbcTemplate.queryForRowSet(imsql.toString(), new Object[]
				{ billno, organId });
				while (rsdili.next())
				{
					Integer rwid = rsdili.getInt("rwid");
					String tableCode = rsdili.getString("table_code");
					Integer guest = rsdili.getInt("guest");
					String waiter_num = rsdili.getString("waiter_num");
					String shift_name = rsdili.getString("shift_name");

					// 存主表
					this.jdbcTemplate.update(insertMPrint, new Object[]
					{ tenancyId, organId, "0", SysDictionary.PRINT_CODE_1106, oper, printer_id, printer_name, billno, tableCode, "*", operType, timestamp, null, guest, waiter_num, shift_name });
					Integer pid = this.jdbcTemplate.queryForObject(qseq, Integer.class);
					list.add(pid);
					// 存次表明细
					this.jdbcTemplate.update(isubSql.toString(), new Object[]
					{ pid, rwid, billno, organId });
				}
			}
		}

		return list;
	}

	@Override
	@Deprecated
	public List<Integer> orderSingleChgChef(String tenancyId, String billno, Integer organId, String oper, String operType, String tableTag, String rwids)
	{
		List<Integer> list = new ArrayList<Integer>();
		/**
		 * 获取当前的序列
		 */
		String qseq = new String("select currval('pos_print_id_seq'::regclass) ");
		/**
		 * 往主表里插入打印参数
		 */
		String insertMPrint = new String(" insert into pos_print (tenancy_id,store_id,print_type,print_format,print_property,printer_id,printer_name,bill_num,table_code,print_tag,remark,print_time,finish_time,guest,waiter_num,shift_name) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ");

		Timestamp timestamp = DateUtil.currentTimestamp();

		// 退菜和转台 地理打印机要打印
		// 查询地理打印机
		String qDili = new String("select id,name from hq_print_printer where isdili = 'Y' and valid_state = '1' and store_id = ? ");

		StringBuilder imsql = new StringBuilder(" select bill.guest,bill.waiter_num,shift.name as shift_name,item.rwid,item.table_code from pos_bill_item as item  ");
		imsql.append(" left join pos_bill bill on item.bill_num=bill.bill_num and item.tenancy_id=bill.tenancy_id and item.store_id=bill.store_id left join duty_order shift on item.item_shift_id=shift.id ");
		imsql.append(" where item.bill_num = ? and item.store_id = ? and item.rwid in (" + rwids + ")");

		/**
		 * 往子表里插入菜品明细，用来打印
		 */
		StringBuilder isubSql = new StringBuilder(" insert into pos_print_list(tenancy_id,store_id,table_code,table_name,bill_num,item_num,item_name,item_english,item_unit_name,item_price,item_count,update_time,zfkwnr,item_property,");
		isubSql.append("item_remark,item_serial,print_code,parent_id,waitcall_tag,rwid) ");

		isubSql.append(" select item.tenancy_id,item.store_id,?,?,item.bill_num,dish.item_code,item.item_name,item.item_english,item.item_unit_name,item.item_price,item.item_count,CURRENT_TIMESTAMP(0) :: TIMESTAMP WITHOUT TIME ZONE ,'',item.item_property,item.item_remark,item.item_serial,item.rwid,?,item.waitcall_tag,item.rwid ");
		isubSql.append(" from pos_bill_item as item left join pos_bill bill on item.bill_num=bill.bill_num ");
		isubSql.append(" left join hq_item_info dish on item.item_id=dish.id left join tables_info table1 on bill.table_code=table1.table_code and bill.store_id = table1.organ_id ");
		isubSql.append(" where item.rwid = ? and item.bill_num = ? and item.store_id = ? ");

		SqlRowSet rs = this.jdbcTemplate.queryForRowSet(qDili, new Object[]
		{ organId });
		while (rs.next())
		{
			Integer printer_id = rs.getInt("id");
			String printer_name = rs.getString("name");
			if ("ZT06".equalsIgnoreCase(operType))
			{
				// 单切插入主表并记录主表的信息
				SqlRowSet rsdili = this.jdbcTemplate.queryForRowSet(imsql.toString(), new Object[]
				{ billno, organId });
				while (rsdili.next())
				{
					Integer rwid = rsdili.getInt("rwid");
					Integer guest = rsdili.getInt("guest");
					String waiter_num = rsdili.getString("waiter_num");
					String shift_name = rsdili.getString("shift_name");

					// 存主表
					this.jdbcTemplate.update(insertMPrint, new Object[]
					{ tenancyId, organId, "0", SysDictionary.PRINT_CODE_1106, oper, printer_id, printer_name, billno, tableTag, "*", operType, timestamp, null, guest, waiter_num, shift_name });
					Integer pid = this.jdbcTemplate.queryForObject(qseq, Integer.class);
					list.add(pid);
					// 存次表明细
					this.jdbcTemplate.update(isubSql.toString(), new Object[]
					{ tableTag, tableTag, pid, rwid, billno, organId });
				}
			}
		}

		return list;
	}

	@Override
	@Deprecated
	public List<Integer> orderWholeChgChef(String tenancyId, String billno, Integer organId, String oper, String operType, String tableTag)
	{
		// StringBuilder sql = new
		// StringBuilder(" select printformat.class_item_code as print_format,classprint.printer_id,printer.name as printer_name from pos_bill_item as item ");
		// sql.append(" left join pos_bill bill on item.bill_num=bill.bill_num and item.tenancy_id=bill.tenancy_id and item.store_id=bill.store_id left join duty_order shift on item.item_shift_id=shift.id ");
		// sql.append(" left join tables_info table1 on bill.table_code=table1.table_code and table1.organ_id=bill.store_id left join hq_item_info dish on item.item_id=dish.id and dish.tenancy_id=item.tenancy_id ");
		// sql.append(" left join hq_kvs_item printitem on dish.id=printitem.item_id and kvs_id in (select hk.id from hq_kvs hk where hk.store_id= ? and hk.tenancy_id=?) ");
		// sql.append(" left join hq_kvs_printer classprint on printitem.kvs_id=classprint.kvs_id left join hq_print_printer printer on classprint.printer_id=printer.id ");
		// sql.append(" left join hq_print_printer_format printformat on printer.id=printformat.print_printer_id and class_item_code not in('1001','1002','1003','1004') ");
		// sql.append(" where item.bill_num = ? and item.store_id = ? and printformat.class_item_code = ? ");
		List<Integer> list = new ArrayList<Integer>();
		/**
		 * 获取当前的序列
		 */
		String qseq = new String("select currval('pos_print_id_seq'::regclass) ");

		StringBuilder sb = new StringBuilder("select printer.id as printer_id,printer.name as printer_name from hq_print_printer printer where printer.isdili='Y' and printer.valid_state='1' and store_id = ?");

		String print_format = "1108";

		/**
		 * 往主表里插入打印参数
		 */
		String insertMPrint = new String(" insert into pos_print (tenancy_id,store_id,print_type,print_format,print_property,printer_id,printer_name,bill_num,table_code,print_tag,remark,print_time,finish_time) values (?,?,?,?,?,?,?,?,?,?,?,?,?) ");

		Timestamp timestamp = DateUtil.currentTimestamp();

		// 单切插入主表并记录主表的信息
		SqlRowSet rs = this.jdbcTemplate.queryForRowSet(sb.toString(), new Object[]
		{ organId });
		while (rs.next())
		{
			Integer printer_id = rs.getInt("printer_id");
			String printer_name = rs.getString("printer_name");
			// 存主表
			this.jdbcTemplate.update(insertMPrint, new Object[]
			{ tenancyId, organId, "0", print_format, oper, printer_id, printer_name, billno, tableTag, "*", operType, timestamp, null });
			Integer pid = this.jdbcTemplate.queryForObject(qseq, Integer.class);
			list.add(pid);
		}
		return list;
	}

	@Override
	@Deprecated
	public void checkSign(Date reportDate, String optNum, String posNum, String busiType, Integer storeId, String tenantId)
	{
		String queryOrganCode = new String("select format_state from organ where id = ?");
		if (StringUtils.isEmpty(busiType))
		{
			try
			{
				SqlRowSet rs = query4SqlRowSet(queryOrganCode, new Object[]
				{ storeId });
				if (rs.next())
				{
					busiType = rs.getString("format_state");
				}
			}
			catch (Exception e)
			{
				e.printStackTrace();
			}
		}

		if ("1".equalsIgnoreCase(busiType))
		{
			String sql = new String("select last_updatetime from pos_opt_state where opt_num=? and report_date=? and content='KSSY' and tag='0' and store_id=? order by id desc limit 1");
			String tcsql = new String("select last_updatetime from pos_opt_state where opt_num=? and report_date=? and content='YYTC' and tag='0' and store_id=? order by id desc limit 1");

			Timestamp kssyTime = null;
			Timestamp tcTime = null;
			SqlRowSet rs = this.jdbcTemplate.queryForRowSet(sql, new Object[]
			{ optNum, reportDate, storeId });
			if (rs.next())
			{
				kssyTime = rs.getTimestamp("last_updatetime");
			}
			if (kssyTime == null)
			{
				Data data = new Data();
				data.setCode(Constant.CODE_INNER_EXCEPTION);
				String optName = this.getEmpNameById(optNum, tenantId, storeId);
				data.setMsg(optName + "未签到");
				return;
			}
			else
			{
				SqlRowSet rst = this.jdbcTemplate.queryForRowSet(tcsql, new Object[]
				{ optNum, reportDate, storeId });
				if (rst.next())
				{
					tcTime = rst.getTimestamp("last_updatetime");
				}

				if (tcTime == null)
				{
					throw new SystemException(PosErrorCode.POS_CASHIER_NOT_SIGNOUT_ERROR);
				}
				else
				{
					if (kssyTime.getTime() > tcTime.getTime())
					{
						throw new SystemException(PosErrorCode.POS_CASHIER_NOT_SIGNOUT_ERROR);
					}
				}
			}
		}

		if ("2".equalsIgnoreCase(busiType))
		{
			String sql = new String("select last_updatetime from pos_opt_state where opt_num=? and report_date=? and pos_num=? and content='KSSY' and tag='0' and store_id=? order by id desc limit 1");
			String tcsql = new String("select last_updatetime from pos_opt_state where opt_num=? and report_date=? and pos_num=? and content='YYTC' and tag='0' and store_id=? order by id desc limit 1");

			Timestamp kssyTime = null;
			Timestamp tcTime = null;
			SqlRowSet rs = this.jdbcTemplate.queryForRowSet(sql, new Object[]
			{ optNum, reportDate, posNum, storeId });
			if (rs.next())
			{
				kssyTime = rs.getTimestamp("last_updatetime");
			}
			if (kssyTime == null)
			{
				throw new SystemException(PosErrorCode.POS_CASHIER_NOT_SIGNIN_ERROR);
			}
			else
			{
				SqlRowSet rst = this.jdbcTemplate.queryForRowSet(tcsql, new Object[]
				{ optNum, reportDate, posNum, storeId });
				if (rst.next())
				{
					tcTime = rst.getTimestamp("last_updatetime");
				}

				if (tcTime == null)
				{
					throw new SystemException(PosErrorCode.POS_CASHIER_NOT_SIGNOUT_ERROR);
				}
				else
				{
					if (kssyTime.getTime() > tcTime.getTime())
					{
						throw new SystemException(PosErrorCode.POS_CASHIER_NOT_SIGNOUT_ERROR);
					}
				}
			}
		}
	}

	public void checkDayEnd(Date reportDate, String posNum, String busiType, Integer storeId)
	{
		String queryOrganCode = new String("select format_state from organ where id = ?");
		if (StringUtils.isEmpty(busiType))
		{
			try
			{
				SqlRowSet rs = query4SqlRowSet(queryOrganCode, new Object[]
				{ storeId });
				if (rs.next())
				{
					busiType = rs.getString("format_state");
				}
			}
			catch (Exception e)
			{
				e.printStackTrace();
			}
		}

		if ("1".equalsIgnoreCase(busiType))
		{
			String sql = new String("select opt_num,last_updatetime from pos_opt_state where report_date=? and content='KSSY' and tag='0' and store_id=? order by id desc limit 1");
			String tcsql = new String("select last_updatetime from pos_opt_state where report_date=? and content='YYTC' and tag='0' and store_id=? order by id desc limit 1");

			Timestamp kssyTime = null;
			Timestamp tcTime = null;
			String optNum = "";
			SqlRowSet rs = this.jdbcTemplate.queryForRowSet(sql, new Object[]
			{ reportDate, storeId });
			if (rs.next())
			{
				kssyTime = rs.getTimestamp("last_updatetime");
				optNum = rs.getString("opt_num");
			}
			if (kssyTime == null)
			{
				Data data = new Data();
				data.setCode(Constant.CODE_INNER_EXCEPTION);
				data.setMsg(optNum + "未签到");
				return;
			}
			else
			{
				SqlRowSet rst = this.jdbcTemplate.queryForRowSet(tcsql, new Object[]
				{ reportDate, storeId });
				if (rst.next())
				{
					tcTime = rst.getTimestamp("last_updatetime");
				}

				if (tcTime == null)
				{
					throw new SystemException(PosErrorCode.POS_CASHIER_NOT_SIGNOUT_ERROR);
				}
				else
				{
					if (kssyTime.getTime() > tcTime.getTime())
					{
						throw new SystemException(PosErrorCode.POS_CASHIER_NOT_SIGNOUT_ERROR);
					}
				}
			}
		}

		if ("2".equalsIgnoreCase(busiType))
		{
			String sql = new String("select opt_num,last_updatetime from pos_opt_state where report_date=? and pos_num=? and content='KSSY' and tag='0' and store_id=? order by id desc limit 1");
			String tcsql = new String("select last_updatetime from pos_opt_state where report_date=? and pos_num=? and content='YYTC' and tag='0' and store_id=? order by id desc limit 1");

			Timestamp kssyTime = null;
			Timestamp tcTime = null;
			SqlRowSet rs = this.jdbcTemplate.queryForRowSet(sql, new Object[]
			{ reportDate, posNum, storeId });
			if (rs.next())
			{
				kssyTime = rs.getTimestamp("last_updatetime");
			}
			if (kssyTime != null)
			{
				SqlRowSet rst = this.jdbcTemplate.queryForRowSet(tcsql, new Object[]
				{ reportDate, posNum, storeId });
				if (rst.next())
				{
					tcTime = rst.getTimestamp("last_updatetime");
				}

				if (tcTime == null)
				{
					throw new SystemException(PosErrorCode.POS_CASHIER_NOT_SIGNOUT_ERROR);
				}
				else
				{
					if (kssyTime.getTime() > tcTime.getTime())
					{
						throw new SystemException(PosErrorCode.POS_CASHIER_NOT_SIGNOUT_ERROR);
					}
				}
			}
		}
	}

	@Override
	public void checkKssy(Date reportDate, String optNum, Integer organId,Integer shiftId,String posNum)
	{
		// 查询是否签到
//		String queryOrganCode = new String("select format_state from organ where id = ?");
//		String busiType = null;
//		SqlRowSet rssy = this.jdbcTemplate.queryForRowSet(queryOrganCode, new Object[]
//		{ organId });
//		if (rssy.next())
//		{
//			busiType = rssy.getString("format_state");
//		}

//		if ("1".equalsIgnoreCase(busiType))
//		{
			//String sql = new String("select opt_num,last_updatetime from pos_opt_state where opt_num=? and report_date=? and content='KSSY' and tag='0' and store_id=? order by id desc limit 1");
			String sql = new String("select opt_num,last_updatetime from pos_opt_state where  pos_num=? and shift_id=? and report_date=? and content='KSSY' and tag='0' and store_id=? order by id desc limit 1");

			String tcsql = new String("select last_updatetime from pos_opt_state where  pos_num=? and  shift_id=? and report_date=? and content='YYTC' and tag='0' and store_id=? order by id desc limit 1");
			//String tcsql = new String("select last_updatetime from pos_opt_state where opt_num=? and report_date=? and content='YYTC' and tag='0' and store_id=? order by id desc limit 1");

			Timestamp kssyTime = null;
			Timestamp tcTime = null;
			SqlRowSet rs3 = this.jdbcTemplate.queryForRowSet(sql, new Object[]
			//{ optNum, reportDate, organId });
			{ posNum,shiftId, reportDate, organId });
			if (rs3.next())
			{
				kssyTime = rs3.getTimestamp("last_updatetime");
			}

			if (kssyTime == null)
			{
				//throw new SystemException(PosErrorCode.POS_CASHIER_NOT_SIGNIN_ERROR);
				throw new SystemException(PosErrorCode.POS_CASHIER_NOT_SIGNIN_ERROR);
			}
			else
			{
				SqlRowSet rst = this.jdbcTemplate.queryForRowSet(tcsql, new Object[]
				//{ optNum, reportDate, organId });
				{ posNum,shiftId, reportDate, organId });
				if (rst.next())
				{
					tcTime = rst.getTimestamp("last_updatetime");
				}

				if (tcTime != null)
				{
					//throw new SystemException(PosErrorCode.POS_CASHIER_ALREADY_SIGNOUT_ERROR);
					throw new SystemException(PosErrorCode.POS_CASHIER_ALREADY_SIGNOUT_ERROR);
				}
			}
//		}
	}

	@Override
	public String getEmpNameById(String optNum, String tenancyId, Integer storeId)
	{
//		if (Tools.isNullOrEmpty(optNum))
//		{
//			throw SystemException.getInstance(PosErrorCode.NOT_NULL_OPT_NUM);
//		}
		if (false == StringUtils.isNumeric(optNum))
		{
//			throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_USER);
			return optNum;
		}

		StringBuilder sbSql = new StringBuilder();
		sbSql.append(" select em.id,em.name from employee as em left join user_authority as ua on ua.tenancy_id=em.tenancy_id and ua.store_id=em.store_id and ua.employee_id=em.id where em.tenancy_id = ? and (em.id = ? or ua.pos_user_name = ?) and em.store_id = ? ");
		SqlRowSet rs = this.jdbcTemplate.queryForRowSet(sbSql.toString(), new Object[]
		{ tenancyId, Integer.parseInt(optNum), optNum, storeId });

		String empName = "";
		if (rs.next())
		{
			empName = rs.getString("name").trim();
		}
		else
		{
//			throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_USER);
			empName = optNum;
		}
		return empName;
	}

	@Override
	public String getEmpIdByName(String name, String tenancyId, Integer storeId)
	{
		String employeeId = "";
		if (Tools.isNullOrEmpty(name))
		{
			return employeeId;
		}

		StringBuilder sbSql = new StringBuilder();
		sbSql.append("select ua.employee_id,ua.user_name from user_authority ua left join employee em on ua.tenancy_id=em.tenancy_id and ua.store_id=em.store_id and ua.employee_id=em.id where (ua.pos_user_name = ? or em.name = ?) and ua.tenancy_id = ? and ua.store_id = ?");
		SqlRowSet rs = this.jdbcTemplate.queryForRowSet(sbSql.toString(), new Object[]
		{ name, name,tenancyId, storeId });
		if (rs.next())
		{
			employeeId = rs.getString("employee_id").trim();
		}
		else
		{
			return name;
		}
		return employeeId;
	}

	@Override
	public String getSysParameter(String tenancyId, int storeId, String para) throws Exception
	{
		String sql = "select trim(para_value) as para_value from sys_parameter where para_code = ? and valid_state='1'";

		SqlRowSet rs = jdbcTemplate.queryForRowSet(sql, new Object[]
		{ para });

		if (rs.next())
		{
			return rs.getString("para_value");
		}
		return "";
	}
	
	public Map<String,String> getSysParameter(String tenancyId, int storeId, String... para) throws Exception
	{
		Map<String, String> map = new HashMap<String, String>();

		StringBuffer sql = new StringBuffer("select trim(para_code) as para_code,trim(para_value) as para_value from sys_parameter where valid_state='1'");

		StringBuffer condition = new StringBuffer();
		if (null != para && 0 < para.length)
		{
			for (String param : para)
			{
				if (0 < condition.length())
				{
					condition.append(",");
				}
				condition.append("'").append(param).append("'");
			}

			if (0 < condition.length())
			{
				sql.append(" and para_code in (").append(condition).append(")");
			}
		}

		SqlRowSet rs = jdbcTemplate.queryForRowSet(sql.toString(), new Object[] {});
		while (rs.next())
		{
			map.put(rs.getString("para_code"), rs.getString("para_value"));
		}

		return map;
	}

	@Override
	public String getCustomerType(String tenancyId, int storeId) throws Exception
	{
		String string = this.getSysParameter(tenancyId, storeId, "CustomerType");
		if(Tools.isNullOrEmpty(string))
		{
			string = SysDictionary.CUSTOMER_TYPE_SAAS;
		}
		return string;
	}

	@Override
	public String getBindOptNum(String tenancyId, Integer storeId, Date reportDate, String deviceNum) throws Exception
	{
		String optNum = "";
		if (Tools.isNullOrEmpty(deviceNum))
		{
			return optNum;
		}
		//取得该点菜器绑定的pos_num
		StringBuilder sbSql =new StringBuilder("select pos_num from pos_opt_state_devices where tenancy_id=? and store_id=? and devices_num=? and is_valid='1'");
		SqlRowSet rs = this.jdbcTemplate.queryForRowSet(sbSql.toString(), new Object[]
		{ tenancyId, storeId, deviceNum });
		if (rs.next())
		{
			StringBuilder queryOptSql = new StringBuilder();
			queryOptSql.append(" select os1.opt_num from pos_opt_state os1 left join pos_opt_state os2 on os1.store_id=os2.store_id and os1.report_date=os2.report_date");
			queryOptSql.append(" and os1.pos_num=os2.pos_num and os1.opt_num=os2.opt_num and os1.content=? and os2.content=? and os1.last_updatetime<os2.last_updatetime");
			//queryOptSql.append(" where os1.tenancy_id=? and os1.store_id=? and os1.report_date=? and os1.pos_num=? and os1.content=? and os1.tag='0' and os2.id is null");
			queryOptSql.append(" where os1.tenancy_id=? and os1.store_id=? and os1.report_date=? and os1.pos_num=? and os1.content=? and os1.tag='0' and os2.id is null");

			SqlRowSet rso = this.jdbcTemplate.queryForRowSet(queryOptSql.toString(), new Object[]
			{ SysDictionary.OPT_STATE_KSSY, SysDictionary.OPT_STATE_YYTC, tenancyId, storeId, reportDate, rs.getString("pos_num"), SysDictionary.OPT_STATE_KSSY });

			if(rso.next())
			{
				optNum = rso.getString("opt_num");
			}
			else
			{
				throw SystemException.getInstance(PosErrorCode.POS_CASHIER_NOT_SIGNIN_ERROR);
			}
			
//			// 根据机号pos_num取得该机器上签到未签退的收银员
//			String sql = new String("select opt_num,last_updatetime from pos_opt_state where pos_num=? and report_date=? and content='KSSY' and tag='0' and store_id=? order by id desc limit 1");
//			String tcsql = new String("select last_updatetime from pos_opt_state where pos_num=? and report_date=? and content='YYTC' and tag='0' and store_id=? order by id desc limit 1");
//
//			Timestamp kssyTime = null;
//			Timestamp tcTime = null;
//			SqlRowSet kssyRst = this.jdbcTemplate.queryForRowSet(sql, new Object[]
//			{ rs.getString("pos_num"), reportDate, storeId });
//			if (kssyRst.next())
//			{
//				kssyTime = kssyRst.getTimestamp("last_updatetime");
//			}
//			if (kssyTime == null)
//			{
//				throw new SystemException(PosErrorCode.POS_CASHIER_NOT_SIGNIN_ERROR);
//			}
//			else
//			{
//				SqlRowSet tcRst = this.jdbcTemplate.queryForRowSet(tcsql, new Object[]
//				{ rs.getString("pos_num"), reportDate, storeId });
//				if (tcRst.next())
//				{
//					tcTime = tcRst.getTimestamp("last_updatetime");
//				}
//				if (tcTime != null)
//				{
//					throw new SystemException(PosErrorCode.POS_CASHIER_ALREADY_SIGNOUT_ERROR);
//				}
//			}
//			optNum = kssyRst.getString("opt_num").trim();
		}
		else
		{
			throw SystemException.getInstance(PosErrorCode.DEVICES_NOT_BIND_ERROR).set("{0}",deviceNum);
		}
		return optNum;
	}

	@Override
	public void checkBindOptnum(String tenancyId, Integer storeId, Date reportDate, String deviceNum) throws Exception
	{
		StringBuilder queryDevicesSql =new StringBuilder("select pos_num from pos_opt_state_devices where tenancy_id=? and store_id=? and devices_num=? and is_valid='1'");
		SqlRowSet rs = this.query4SqlRowSet(queryDevicesSql.toString(), new Object[]{tenancyId,storeId,deviceNum});
		if(rs.next())
		{
			// 根据机号判断该机器上是否有收银员签到
			String sql = new String("select last_updatetime from pos_opt_state where pos_num=? and report_date=? and content='KSSY' and tag='0' and store_id=? order by id desc limit 1");
			Timestamp kssyTime = null;
			Timestamp tcTime = null;
			SqlRowSet kssyRst = this.query4SqlRowSet(sql, new Object[]
			{ rs.getString("pos_num"), reportDate, storeId });
			if (kssyRst.next())
			{
				kssyTime = kssyRst.getTimestamp("last_updatetime");
			}

			if (kssyTime == null)
			{
				throw new SystemException(PosErrorCode.POS_CASHIER_NOT_SIGNIN_ERROR);
			}
			else
			{
				String tcsql = new String("select last_updatetime from pos_opt_state where pos_num=? and report_date=? and content='YYTC' and tag='0' and store_id=? order by id desc limit 1");
				SqlRowSet tcRst = this.query4SqlRowSet(tcsql, new Object[]
				{ rs.getString("pos_num"), reportDate, storeId });
				if (tcRst.next())
				{
					tcTime = tcRst.getTimestamp("last_updatetime");
				}

				if (tcTime != null)
				{
					throw new SystemException(PosErrorCode.POS_CASHIER_ALREADY_SIGNOUT_ERROR);
				}
			}
		}
		else
		{
			throw SystemException.getInstance(PosErrorCode.DEVICES_NOT_BIND_ERROR).set("{0}", deviceNum);
		}
	}

	@Override
	public boolean copyDataForTable(String fromTableName, String toTableName, JSONObject json, boolean isCopyId) throws Exception
	{
		return this.copyDataForTable(fromTableName, toTableName, json, isCopyId, false, false);
	}
	
	@Override
	public boolean copyDataForTable(String fromTableName, String toTableName, JSONObject json, boolean isCopyId,boolean isCopyRwid,boolean isCopyUploadTag) throws Exception
	{
		Connection conn = null;
		try
		{
			if(Tools.isNullOrEmpty(fromTableName) || Tools.isNullOrEmpty(toTableName))
			{
				logger.warn("表名不能为空!");
				return false;
			}
			
			//查询条件
			String tenancyId = json.optString("tenancy_id");
			Integer storeId = json.optInt("store_id");
			String reportDate = json.optString("report_date");
			String billNum = json.optString("bill_num");
			
			StringBuilder condition = new StringBuilder();
			if(null != storeId && !"".equals(storeId.toString()))
			{
				condition.append(" store_id='" + storeId + "' ");
			}
			if(null != reportDate && !"".equals(reportDate))
			{
				condition.append(" and report_date='" + reportDate + "' ");
			}
			if(null != tenancyId && !"".equals(tenancyId))
			{
				condition.append(" and tenancy_id='" + tenancyId + "' ");
			}
			if(null != billNum && !"".equals(billNum))
			{
				condition.append(" and bill_num='" + billNum + "' ");
			}
			if(condition.length() != 0)
			{
				condition = condition.insert(0, "where ");
			}
			
			//数据源的表的字段
			conn = this.jdbcTemplate.getDataSource().getConnection();
			DatabaseMetaData dbmd = conn.getMetaData();
			
			String idColumn = null;
			ResultSet rs = dbmd.getPrimaryKeys(null, null, toTableName);
			if (rs.next())
			{
				idColumn = rs.getString(4);
			}
			MultiDataSourceManager.close(null, null, rs);
			
			ResultSet fromRs = dbmd.getColumns(null, null, fromTableName, null);
			if(fromRs == null)
			{
				return false;
			}
			List<String> fromNameList = new ArrayList<String>();
			while(fromRs.next())
			{
				String fromColname = fromRs.getString("COLUMN_NAME");
				fromNameList.add(fromColname);
			}
			MultiDataSourceManager.close(null, null, fromRs);
			//要插入的表的字段
			ResultSet toRs = dbmd.getColumns(null, null, toTableName, null);
			if(toRs == null)
			{
				return false;
			}
			
			StringBuilder fromColumn = new StringBuilder();
			StringBuilder toColumn = new StringBuilder();
			while(toRs.next())
			{
				String toColname = toRs.getString("COLUMN_NAME");
				if (toColname.equals(idColumn))
				{
					//id自增
					if(isCopyId)
					{
						toColumn.append("," + idColumn);
						fromColumn.append("," + idColumn);
					}
				}
				else if ("yrwid".equals(toColname) && isCopyRwid)
				{
					toColumn.append("," + toColname);
					fromColumn.append(",rwid");
				}
                else if ("upload_tag".equals(toColname) && isCopyUploadTag)
                {
                    toColumn.append("," + toColname);
                    fromColumn.append(",'0'");
                }
				else
				{
					toColumn.append("," + toColname);
					//根据要插入的表的字段名，一一查找数据源表的字段名
					String fromName = null;
					String name = "";
					for(int i = 0; i < fromNameList.size(); i++)
					{
						name = fromNameList.get(i);
						if(null == name || "".equals(name))
						{
							continue;
						}
						if(name.equals(toColname))
						{
							fromName = name;
						}
					}
					//如果要插入的表的字段名在数据源表的字段名中不存在，查找传入JSON中是否存在
					if(null == fromName)
					{
						if(json.containsKey(toColname))
						{
							fromName = "'" + json.getString(toColname) + "'";
						}
					}
					fromColumn.append("," + fromName);
				}
			}
			MultiDataSourceManager.close(null, null, toRs);
			
			StringBuilder querySql = new StringBuilder("select " + fromColumn.substring(1) + " from " + fromTableName);
			querySql.append(" " + condition);
			
			StringBuilder updateSql = new StringBuilder("insert into " + toTableName + " ( ");
			updateSql.append(toColumn.substring(1) + " ) " + querySql);
			
			this.jdbcTemplate.update(updateSql.toString());
			
			return true;
		}
		catch (SystemException se)
		{
			throw se;
		}
		catch (Exception e)
		{
			logger.info("Exception:" + ExceptionMessage.getExceptionMessage(e));
			throw SystemException.getInstance(PosErrorCode.OPER_ERROR);
		}
		finally
		{
			MultiDataSourceManager.close(conn, null, null);
		}
	}

	@Override
	public JSONObject getOrganById(String tenancyId, Integer storeId) throws Exception 
	{
		StringBuilder sql = new StringBuilder("select id,organ_code,org_full_name,org_short_name,org_type,org_uuid,address,price_system,format_state,is_wechatdddc,is_wechatwmzt,third_organ_code from organ where tenancy_id=? and id=? ");
		
		List<JSONObject> list = this.query4Json(tenancyId, sql.toString(), new Object[]{tenancyId,storeId});
		
		if(null != list && list.size()>0)
		{
			return list.get(0);
		}
		else
		{
			return null;
		}
	}

	@Override
	public JSONObject getPaymentWayByID(String tenancyId, int storeId, int payId) throws Exception
	{
		JSONObject retJson = new JSONObject();
		StringBuilder findPaymentWaySql = new StringBuilder("select p.id as payment_id,p.payment_class,coalesce(d.class_item,p.payment_name1) as payment_name,(case when d.class_item is not null then p.payment_name1 else '' end) as payment_name1,p.rate,p.if_jifen,p.is_check,p.is_recharge,p.if_invoicing,p.if_income from payment_way p");
		findPaymentWaySql.append(" inner join payment_way_of_ogran o on p.tenancy_id=o.tenancy_id and p.id=o.payment_id left join sys_dictionary d on p.payment_name1=d.class_item_code and p.payment_class=? and d.class_identifier_code='currency' where o.tenancy_id=? and o.organ_id=? and p.id=?");
		
		SqlRowSet rs = this.query4SqlRowSet(findPaymentWaySql.toString(), new Object[]
		{ SysDictionary.PAYMENT_CLASS_CASH,tenancyId, storeId, payId });
		if (rs.next())
		{
			retJson.put("payment_id", rs.getString("payment_id"));
			retJson.put("payment_class", rs.getString("payment_class"));
			retJson.put("payment_name", rs.getString("payment_name"));
			retJson.put("name_english", rs.getString("payment_name1"));
			retJson.put("rate", Tools.hv(rs.getObject("rate")) ? rs.getDouble("rate") : 1d);
			retJson.put("if_jifen", rs.getString("if_jifen"));//是否积分
			retJson.put("is_check", rs.getString("is_check"));//是否验券
			retJson.put("is_recharge", rs.getString("is_recharge"));//是否找零
			retJson.put("if_invoicing", rs.getString("if_invoicing"));//是否开发票
			retJson.put("if_income", rs.getString("if_income"));//是否计入营业收入
		}
		return retJson;
	}
	
	public JSONObject getPaymentWayForOrganByID(String tenancyId, int storeId, int payId) throws Exception
	{

		JSONObject retJson = new JSONObject();
		StringBuilder findPaymentWaySql = new StringBuilder("select p.id as payment_id,p.payment_class,coalesce(d.class_item,p.payment_name1) as payment_name,(case when d.class_item is not null then p.payment_name1 else '' end) as payment_name1,p.rate,p.if_jifen,p.is_check,p.is_recharge,p.if_invoicing,p.if_income,og.org_full_name,og.org_short_name from payment_way p");
		findPaymentWaySql.append(" inner join payment_way_of_ogran o on p.tenancy_id=o.tenancy_id and p.id=o.payment_id left join sys_dictionary d on p.payment_name1=d.class_item_code and p.payment_class=? and d.class_identifier_code='currency' inner join organ og on o.tenancy_id=og.tenancy_id and o.organ_id=og.id where o.tenancy_id=? and o.organ_id=? and p.id=?");
		
		SqlRowSet rs = this.query4SqlRowSet(findPaymentWaySql.toString(), new Object[]
		{ SysDictionary.PAYMENT_CLASS_CASH,tenancyId, storeId, payId });
		if (rs.next())
		{
			retJson.put("payment_id", rs.getString("payment_id"));
			retJson.put("payment_class", rs.getString("payment_class"));
			retJson.put("payment_name", rs.getString("payment_name"));
			retJson.put("name_english", rs.getString("payment_name1"));
			retJson.put("rate", Tools.hv(rs.getObject("rate")) ? rs.getDouble("rate") : 1d);
			retJson.put("if_jifen", rs.getString("if_jifen"));//是否积分
			retJson.put("is_check", rs.getString("is_check"));//是否验券
			retJson.put("is_recharge", rs.getString("is_recharge"));//是否找零
			retJson.put("if_invoicing", rs.getString("if_invoicing"));//是否开发票
			retJson.put("if_income", rs.getString("if_income"));//是否计入营业收入
			retJson.put("org_full_name", rs.getString("org_full_name"));
			retJson.put("org_short_name", rs.getString("org_short_name"));
		}
		return retJson;
	
	}
	
	@Override
	public JSONObject getPaymentWayForStandard(String tenancyId, int storeId) throws Exception
	{
		JSONObject retJson = new JSONObject();
		StringBuilder qureyPayWay = new StringBuilder("select p.id as payment_id,p.payment_class,coalesce(d.class_item,p.payment_name1) as payment_name,(case when d.class_item is not null then p.payment_name1 else '' end) as payment_name1,p.rate,p.if_jifen,p.is_check,p.is_recharge,p.if_invoicing,p.if_income from payment_way p");
		qureyPayWay.append(" inner join payment_way_of_ogran o on p.tenancy_id=o.tenancy_id and p.id=o.payment_id left join sys_dictionary d on p.payment_name1=d.class_item_code and d.class_identifier_code='currency' where p.payment_class=? and o.tenancy_id=? and o.organ_id=? and p.is_standard_money='1' and p.status='1'");

		SqlRowSet rs = this.query4SqlRowSet(qureyPayWay.toString(), new Object[]
		{ SysDictionary.PAYMENT_CLASS_CASH, tenancyId, storeId });
		if (rs.next())
		{
			retJson.put("payment_id", rs.getString("payment_id"));
			retJson.put("payment_class", rs.getString("payment_class"));
			retJson.put("payment_name", rs.getString("payment_name"));
			retJson.put("name_english", rs.getString("payment_name1"));
			retJson.put("rate", Tools.hv(rs.getObject("rate")) ? rs.getDouble("rate") : 1d);
			retJson.put("if_jifen", rs.getString("if_jifen"));// 是否积分
			retJson.put("is_check", rs.getString("is_check"));// 是否验券
			retJson.put("is_recharge", rs.getString("is_recharge"));// 是否找零
			retJson.put("if_invoicing", rs.getString("if_invoicing"));// 是否开发票
			retJson.put("if_income", rs.getString("if_income"));// 是否计入营业收入
		}
		return retJson;
	}
	
	@Override
	public JSONObject getPaymentWayByPaymentClass(String tenancyId, int storeId, String paymentClass) throws Exception
	{
		JSONObject retJson = new JSONObject();
		StringBuilder findPaymentWaySql = new StringBuilder("select p.id as payment_id,p.payment_class,coalesce(d.class_item,p.payment_name1) as payment_name,(case when d.class_item is not null then p.payment_name1 else '' end) as payment_name1,p.rate,p.if_jifen,p.is_check,p.is_recharge,p.if_invoicing,p.if_income from payment_way p");
		findPaymentWaySql.append(" inner join payment_way_of_ogran o on p.tenancy_id=o.tenancy_id and p.id=o.payment_id left join sys_dictionary d on p.payment_name1=d.class_item_code and p.payment_class=? and d.class_identifier_code='currency' where p.status='1' and o.tenancy_id=? and o.organ_id=? and p.payment_class=?");
		
		SqlRowSet rs = this.query4SqlRowSet(findPaymentWaySql.toString(), new Object[]
		{ SysDictionary.PAYMENT_CLASS_CASH,tenancyId, storeId, paymentClass });
		if (rs.next())
		{
			retJson.put("payment_id", rs.getString("payment_id"));
			retJson.put("payment_class", rs.getString("payment_class"));
			retJson.put("payment_name", rs.getString("payment_name"));
			retJson.put("name_english", rs.getString("payment_name1"));
			retJson.put("rate", Tools.hv(rs.getObject("rate")) ? rs.getDouble("rate") : 1d);
			retJson.put("if_jifen", rs.getString("if_jifen"));//是否积分
			retJson.put("is_check", rs.getString("is_check"));//是否验券
			retJson.put("is_recharge", rs.getString("is_recharge"));//是否找零
			retJson.put("if_invoicing", rs.getString("if_invoicing"));//是否开发票
			retJson.put("if_income", rs.getString("if_income"));//是否计入营业收入
		}
		return retJson;
	}
	
	public JSONObject getPaymentWayForOrganByPaymentClass(String tenancyId, int storeId, String paymentClass) throws Exception
	{

		JSONObject retJson = new JSONObject();
		StringBuilder findPaymentWaySql = new StringBuilder("select p.id as payment_id,p.payment_class,coalesce(d.class_item,p.payment_name1) as payment_name,(case when d.class_item is not null then p.payment_name1 else '' end) as payment_name1,p.rate,p.if_jifen,p.is_check,p.is_recharge,p.if_invoicing,p.if_income,og.org_full_name,og.org_short_name from payment_way p");
		findPaymentWaySql.append(" inner join payment_way_of_ogran o on p.tenancy_id=o.tenancy_id and p.id=o.payment_id left join sys_dictionary d on p.payment_name1=d.class_item_code and p.payment_class=? and d.class_identifier_code='currency' inner join organ og on o.tenancy_id=og.tenancy_id and o.organ_id=og.id where p.status='1' and o.tenancy_id=? and o.organ_id=? and p.payment_class=?");
		
		SqlRowSet rs = this.query4SqlRowSet(findPaymentWaySql.toString(), new Object[]
		{ SysDictionary.PAYMENT_CLASS_CASH,tenancyId, storeId, paymentClass });
		if (rs.next())
		{
			retJson.put("payment_id", rs.getString("payment_id"));
			retJson.put("payment_class", rs.getString("payment_class"));
			retJson.put("payment_name", rs.getString("payment_name"));
			retJson.put("name_english", rs.getString("payment_name1"));
			retJson.put("rate", Tools.hv(rs.getObject("rate")) ? rs.getDouble("rate") : 1d);
			retJson.put("if_jifen", rs.getString("if_jifen"));//是否积分
			retJson.put("is_check", rs.getString("is_check"));//是否验券
			retJson.put("is_recharge", rs.getString("is_recharge"));//是否找零
			retJson.put("if_invoicing", rs.getString("if_invoicing"));//是否开发票
			retJson.put("if_income", rs.getString("if_income"));//是否计入营业收入
			retJson.put("org_full_name", rs.getString("org_full_name"));
			retJson.put("org_short_name", rs.getString("org_short_name"));
		}
		return retJson;
	
	}

	@Override
	public List<String> getSysPrintModeList(String tenancyId, int storeId, String functionId, String takeOutOrWeChat) throws Exception
	{
		//打印改造 新增POS点餐清单、外卖点餐清单、微信点餐清单
		StringBuilder sql = new StringBuilder(" select smf.format_type from sys_model_function smf ");
		sql.append(" where smf.tenancy_id=? and smf.function_id=? and (cast(smf.type as varchar)=(select format_state from organ) or smf.type=0)  ");

		// 下单时，判断‘是否门店点菜下单时自动打印点菜单（打印服务V4.0以上）’
        String isPrintMenuVersion = null;
        if(FunctionCode.ORDERING.equals(functionId)){
            isPrintMenuVersion = this.getSysParameter(tenancyId, storeId, "is_print_menu_version4");
        }
        if("0".equals(isPrintMenuVersion)){
            sql.append(" and smf.format_type <> '").append(SysDictionary.PRINT_CODE_1103).append("' ");
        }

        if(takeOutOrWeChat!=null&&!"".equals(takeOutOrWeChat)){
        	if(takeOutOrWeChat.equals("WM1205")){
        	sql.append(" and smf.format_type <> '").append(SysDictionary.PRINT_CODE_1103).append("' ");
        	sql.append(" and smf.format_type <> '").append(SysDictionary.PRINT_CODE_1401).append("' ");
        }else if(takeOutOrWeChat.equals("WX1401")){
        	sql.append(" and smf.format_type <> '").append(SysDictionary.PRINT_CODE_1103).append("' ");
        	sql.append(" and smf.format_type <> '").append(SysDictionary.PRINT_CODE_1205).append("' ");
        }
        }else{
        	sql.append(" and smf.format_type <> '").append(SysDictionary.PRINT_CODE_1205).append("' ");
        	sql.append(" and smf.format_type <> '").append(SysDictionary.PRINT_CODE_1401).append("' ");
        }	
        
        sql.append(" order by smf.id ");

		SqlRowSet rs = jdbcTemplate.queryForRowSet(sql.toString(), new Object[]
		{ tenancyId, functionId });

		List<String> list = new ArrayList<String>();
		while (rs.next())
		{
			list.add(rs.getString("format_type"));
		}
		return list;
//		StringBuilder sql = new StringBuilder(" select smf.format_type from sys_model_function smf ");
//		sql.append(" where smf.tenancy_id=? and smf.function_id=? and (cast(smf.type as varchar)=(select format_state from organ) or smf.type=0)  ");
//
//		// 下单时，判断‘是否门店点菜下单时自动打印点菜单（打印服务V4.0以上）’
//        String isPrintMenuVersion = null;
//        if(FunctionCode.ORDERING.equals(functionId)){
//            isPrintMenuVersion = this.getSysParameter(tenancyId, storeId, "is_print_menu_version4");
//        }
//        if("0".equals(isPrintMenuVersion)){
//            sql.append(" and smf.format_type <> '").append(SysDictionary.PRINT_CODE_1103).append("' ");
//        }
//
//        sql.append(" order by smf.id ");
//
//		SqlRowSet rs = jdbcTemplate.queryForRowSet(sql.toString(), new Object[]
//		{ tenancyId, functionId });
//
//		List<String> list = new ArrayList<String>();
//		while (rs.next())
//		{
//			list.add(rs.getString("format_type"));
//		}
//		return list;
	}
	
	@Override
	@Deprecated
	public void insertPosBillMember(String tenancyId,int storeId,String billNum,Date reportDate,String type,String customerCode,String cardCode,String mobil,Timestamp lastUpdatetime,String remark,String customer_name,double consume_before_credit,double consume_after_credit,double credit,double amount,String bill_code,String request_state) throws Exception
	{
		StringBuilder sql = new StringBuilder();
		sql.append(" insert into pos_bill_member(tenancy_id,store_id,bill_num,report_date,type,customer_code,card_code,mobil,last_updatetime,remark,upload_tag,customer_name,consume_before_credit,consume_after_credit,credit,amount,bill_code,request_state) values (?,?,?,?,?,?,?,?,?,?,0,?,?,?,?,?,?,?)");
		this.jdbcTemplate.update(sql.toString(), new Object[]
		{ tenancyId,storeId,billNum,reportDate,type,customerCode,cardCode,mobil,lastUpdatetime,remark,customer_name,Double.isNaN(consume_before_credit)?0:consume_before_credit,Double.isNaN(consume_after_credit)?0:consume_after_credit,Double.isNaN(credit)?0:credit,amount,bill_code,request_state});
	}
	
	@Override
	@Deprecated
	public void insertPosBillMember(String tenancyId, int storeId, String billNum, Date reportDate, String type, String customerCode, String cardCode, String mobil, Timestamp lastUpdatetime, String remark, String customer_name, double customer_credit) throws Exception
	{
		this.insertPosBillMember(tenancyId, storeId, billNum, reportDate, type, customerCode, cardCode, mobil, lastUpdatetime, remark, customer_name, customer_credit, 0d, 0d, 0d, null, null);
	}
	
	@Override
	@Deprecated
	public void updatePosBillMember(String tenantId, int storeId, String billno, String type, double amount,double credit,String billCode,String requestState,String customerName,Double consumeBeforeCredit,Double consumeAfterCredit)throws Exception
	{
		StringBuilder sql = new StringBuilder();
		sql.append("update pos_bill_member set amount=?,credit=?,bill_code=?,request_state=?,customer_name=?,consume_before_credit=?,consume_after_credit=? where tenancy_id=? and store_id=? and bill_num=? and type=?");
		this.jdbcTemplate.update(sql.toString(), new Object[]
		{ amount,Double.isNaN(credit)?0:credit,billCode,requestState,customerName,Double.isNaN(consumeBeforeCredit)?0:consumeBeforeCredit,Double.isNaN(consumeAfterCredit)?0:consumeAfterCredit, tenantId, storeId, billno, type});
	}

	@Override
	@Deprecated
	public void updatePosBillMemberRegain(String tenantId, int storeId, String billno, String type, double amount,double credit,String billCode,String requestState,String customerName,Double consumeBeforeCredit,Double consumeAfterCredit,Integer regainCount )throws Exception
	{
		StringBuilder sql = new StringBuilder();
		sql.append("update pos_bill_member_regain set amount=?,credit=?,bill_code=?,request_state=?,customer_name=?,consume_before_credit=?,consume_after_credit=? where tenancy_id=? and store_id=? and bill_num=? and type=? and regain_count=?");
		this.jdbcTemplate.update(sql.toString(), new Object[]
		{ amount,credit,billCode,requestState,customerName,consumeBeforeCredit,consumeAfterCredit, tenantId, storeId, billno, type,regainCount});
	}
	
	@Override
	@Deprecated
	public void deletePosBillMember(String tenantId, int storeId, String billno, String type)throws Exception
	{
		StringBuilder sql = new StringBuilder();
		sql.append("delete from pos_bill_member pbm where pbm.tenancy_id =? and pbm.store_id =? and pbm.bill_num =? and type = ?");
		this.jdbcTemplate.update(sql.toString(), new Object[]
		{ tenantId, storeId, billno, type });
	}
	
	@Override
	@Deprecated
	public List<JSONObject> queryPosBillMember(String tenantId, int storeId, String billno, String type) throws Exception
	{
		StringBuilder sql = new StringBuilder("select * from pos_bill_member where tenancy_id =? and store_id =? and bill_num =?");
		if(Tools.hv(type))
		{
			sql.append(" and type = '").append(type).append("'");
		}
		
		return this.query4Json(tenantId, sql.toString(), new Object[]{tenantId,storeId,billno});
	}
	
	@Override
	public JSONObject getPosBillByBillnum(String tenancyId, int storeId, String billNum) throws Exception
	{
		// TODO Auto-generated method stub
		JSONObject resultJson = new JSONObject();
		StringBuilder billAmountSql = new StringBuilder();
		billAmountSql.append(" select b.fictitious_table, b.id,b.waiter_num,coalesce(b.bill_amount,0) as bill_amount,round(coalesce(b.payment_amount,0),2) as payment_amount,coalesce(b.discount_rate,100) as discount_rate,coalesce(b.discount_case_id,0) as discount_case_id,coalesce(b.discount_amount,0) as discount_amount,b.source,b.bill_property");
		billAmountSql.append(" ,coalesce(b.table_code,'') as table_code,coalesce(b.difference,0)  as difference,sale_mode,coalesce(b.order_num,'') as order_num,b.copy_bill_num,b.payment_state,b.batch_num,b.discount_mode_id,b.remark,coalesce(b.recover_count,0) as recover_count,bill_num,payment_time,b.bill_state");
		billAmountSql.append(" ,b.discountk_amount,b.discountr_amount,b.givi_amount,b.maling_amount,b.subtotal,b.print_count,b.transfer_remark,b.pay_no,b.report_date,b.guest,b.discount_reason_id,b.discount_num,b.pos_num,b.cashier_num,b.shift_id ,b.sbill_num ");
		billAmountSql.append(" from v_pos_bill b where b.bill_num = ? and b.store_id = ? and b.tenancy_id = ?");

		List<JSONObject> list = this.query4Json(tenancyId, billAmountSql.toString(), new Object[]
		{ billNum, storeId, tenancyId });

		if (list != null && list.size() > 0)
		{
			resultJson = list.get(0);
		}
		return resultJson;
	}

	public JSONObject getPosBillBySBillnum(String tenancyId, int storeId, String sbillNum) throws Exception
	{
		// TODO Auto-generated method stub
		JSONObject resultJson = new JSONObject();
		StringBuilder billAmountSql = new StringBuilder();
		billAmountSql.append(" select b.fictitious_table, b.id,b.waiter_num,coalesce(b.bill_amount,0) as bill_amount,round(coalesce(b.payment_amount,0),2) as payment_amount,coalesce(b.discount_rate,100) as discount_rate,coalesce(b.discount_case_id,0) as discount_case_id,coalesce(b.discount_amount,0) as discount_amount,b.source,b.bill_property");
		billAmountSql.append(" ,coalesce(b.table_code,'') as table_code,coalesce(b.difference,0)  as difference,sale_mode,coalesce(b.order_num,'') as order_num,b.copy_bill_num,b.payment_state,b.batch_num,b.discount_mode_id,b.remark,coalesce(b.recover_count,0) as recover_count,bill_num,payment_time,b.bill_state");
		billAmountSql.append(" ,b.discountk_amount,b.discountr_amount,b.givi_amount,b.maling_amount,b.subtotal,b.print_count,b.transfer_remark,b.pay_no,b.report_date,b.guest,b.discount_reason_id,b.discount_num,b.pos_num,b.cashier_num,b.shift_id ,b.sbill_num ");
		billAmountSql.append(" from v_pos_bill b where b.sbill_num like '%"+sbillNum+"%' and b.store_id = ? and b.tenancy_id = ?");

		List<JSONObject> list = this.query4Json(tenancyId, billAmountSql.toString(), new Object[]
				{  storeId, tenancyId });

		if (list != null && list.size() > 0)
		{
			resultJson = list.get(0);
		}
		return resultJson;
	}

	@Override
	public JSONObject getInvoiceInfo(String tenancyId, int storeId, JSONObject para) throws Exception
	{
		StringBuilder sql = new StringBuilder(" select pbn.bill_num,pbn.invoice_type,round(coalesce(pbn.invoice_amount,0)-coalesce(b.return_amount,0),2) as invoice_amount,pbn.last_updatetime,pbn.url_content, pbn.id from pos_bill_invoice pbn ");
		sql.append(" left join pos_bill b on b.bill_num=pbn.bill_num ");
		sql.append(" where (pbn.bill_num=? or pbn.copy_bill_num=?) and pbn.tenancy_id=? and pbn.store_id=? and pbn.bill_state is null ");

		List<JSONObject> list = this.queryString4Json(tenancyId, sql.toString(), new Object[]
		{ para.optString("bill_num"), para.optString("bill_num"), tenancyId, storeId });

		if (list != null && list.size() > 0)
		{
			return list.get(0);
		}
		else
		{
			return null;
		}
	}
	
	@Override
	public void insertToInvoice (String tenancyId, int storeId, JSONObject para) throws Exception
	{
		StringBuilder deleteSql = new StringBuilder(" delete from pos_bill_invoice where bill_num=? and tenancy_id=? and store_id=? ");
		
		this.jdbcTemplate.update(deleteSql.toString(),new Object[]
				{ para.optString("bill_num"), tenancyId, storeId });
		
		StringBuilder sql = new StringBuilder(" insert into pos_bill_invoice(tenancy_id,store_id,report_date,pos_num,opt_num,bill_num,invoice_num,invoice_count,invoice_amount,last_updatetime,upload_tag,remark,invoice_type,copy_bill_num,url_content) ");
		sql.append(" values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ");
		
		Date reportDate = DateUtil.parseDate(para.optString("report_date"));
		Timestamp last_updatetime = DateUtil.parseTimestamp(para.optString("last_updatetime"));
		Double invoiceAmount = para.optDouble("invoice_amount");
		if (invoiceAmount.isNaN())
		{
			invoiceAmount = 0d;
		}
		String copyBillNum = "";
		if(para.containsKey("copy_bill_num"))
		{
			copyBillNum = para.optString("copy_bill_num");
		}
		
		String strUrlContent = "";
		if(para.containsKey("url_content"))
		{
			strUrlContent = para.optString("url_content");
		}
		this.update(sql.toString(), new Object[]
				{ tenancyId, storeId, reportDate, para.optString("pos_num"), para.optString("opt_num"), para.optString("bill_num"), para.optString("invoice_num"), 1, invoiceAmount, last_updatetime, 0, null, 2, copyBillNum, strUrlContent });
	}
	
	@Override
	public void deleteInvoiceFromInvoice(String tenancyId, int storeId, JSONObject para) throws Exception
	{
		StringBuilder deleteSql = new StringBuilder(" delete from pos_bill_invoice where bill_num=? and tenancy_id=? and store_id=? ");

		this.jdbcTemplate.update(deleteSql.toString(), new Object[]
		{ para.optString("bill_num"), tenancyId, storeId });
	}
	
	@Override
	public void deleteInvoiceByInvoiceId(String tenancyId, int storeId, String billnum ,Integer invoiceId) throws Exception
	{
		// TODO Auto-generated method stub
		StringBuilder deleteSql = new StringBuilder(" delete from pos_bill_invoice where tenancy_id=? and store_id=? and bill_num=? and id=?");

		this.jdbcTemplate.update(deleteSql.toString(), new Object[]
		{tenancyId, storeId,billnum,invoiceId });
	}
	
	@Override
	public List<JSONObject> getInvoicePayment(String tenancyId, int storeId, JSONObject para) throws Exception
	{
		StringBuilder sql = new StringBuilder(" select (round(sum(coalesce(bb.amount,0)), 2)-round(coalesce((select sum(coalesce(invoice_amount,0)*coalesce(invoice_count,0)) as invoice_amount from pos_bill_invoice where bill_num=?),0), 2)) as invoice_amount from pos_bill_payment bb  ");
		sql.append(" left join payment_way pw on bb.jzid=pw.id ");
		sql.append(" left join payment_way_of_ogran pwoo on pw.id=pwoo.payment_id ");
//		sql.append(" left join (select sum(coalesce(invoice_amount,0)*coalesce(invoice_count,0)) as invoice_amount from pos_bill_invoice where bill_num=?) pbn on 1=1 ");
		sql.append(" where bb.bill_num=? and bb.tenancy_id=? and bb.store_id=? and pw.if_invoicing='1' and bb.tenancy_id=pw.tenancy_id ");
		
		return this.queryString4Json(tenancyId, sql.toString(), new Object[]
				{ para.optString("bill_num"), para.optString("bill_num"), tenancyId, storeId });
	}
	
	@Override
	public List<JSONObject> getInvoicePaymentRetreat(String tenancyId, int storeId, JSONObject para) throws Exception
	{
		String billNum = para.optString("bill_num");
		StringBuilder sql = new StringBuilder(" select round((coalesce(a.payment_amount,0)-coalesce(b.total_invoice_amount,0)),2) as invoice_amount from ");
		sql.append(" (select round(sum(coalesce(pb.payment_amount,0)),2) as payment_amount from pos_bill pb ");
		sql.append(" left join pos_bill_payment bb on pb.bill_num=bb.bill_num ");
		sql.append(" left join payment_way pw on bb.jzid=pw.id ");
		sql.append(" left join payment_way_of_ogran pwoo on pw.id=pwoo.payment_id ");
		sql.append(" where(pb.bill_num=? or pb.copy_bill_num=?) and pb.tenancy_id=? and pb.store_id=? and pw.if_invoicing='1' and pb.tenancy_id=pw.tenancy_id )a, ");
		sql.append(" (select sum(abs(coalesce(invoice_count,0)) * coalesce(invoice_amount,0)) as total_invoice_amount from pos_bill_invoice pbn ");
		sql.append(" where(pbn.bill_num=? or pbn.copy_bill_num=?) and pbn.tenancy_id=? and pbn.store_id=?) b ");
		
		return this.queryString4Json(tenancyId, sql.toString(), new Object[]
				{ billNum, billNum, tenancyId, storeId, billNum, billNum, tenancyId, storeId });
	}
	
	@Override
	public JSONObject getLegalPerInfo(String tenancyId, int storeId, JSONObject para) throws Exception
	{
		StringBuilder sql = new StringBuilder(" select lp.cnpj as invoice_num,round(lp.tax_rate,2) as tax_rate from hq_legal_per_organ_ref lpor ");
		sql.append(" left join hq_legal_per lp on lpor.tenancy_id=lp.tenancy_id and lpor.legal_per_id=lp.id ");
		sql.append(" where lpor.tenancy_id=? and lpor.organ_id=? ");
		
		List<JSONObject> list= this.queryString4Json(tenancyId, sql.toString(), new Object[]
				{ tenancyId, storeId });
		
		if(list!=null && list.size()>0){
			return list.get(0);
		}else{
			return null;
		}
	}
	
	@Override
	public void updateInvoice(String tenancyId, int storeId, JSONObject para) throws Exception
	{
		Timestamp currentTime = DateUtil.currentTimestamp();
		
		StringBuilder sql = new StringBuilder(
				" insert into pos_bill_invoice (tenancy_id,store_id,report_date,pos_num,opt_num,bill_num,invoice_num,invoice_amount,last_updatetime,upload_tag,remark,invoice_type,invoice_count,bill_state,copy_bill_num) ");
		sql.append(" select tenancy_id,store_id,report_date,pos_num,opt_num,bill_num,invoice_num,-invoice_amount,?,0,remark,invoice_type,-invoice_count,?,copy_bill_num from pos_bill_invoice where tenancy_id=? and store_id=? and (bill_num=? or copy_bill_num=?) and bill_state is null ");
		this.update(sql.toString(), new Object[]
		{ currentTime, "CJ01", tenancyId, storeId, para.optString("bill_num"), para.optString("bill_num") });
		
		// 更新原发票记录状态
		StringBuilder usql = new StringBuilder("update pos_bill_invoice set bill_state=?,upload_tag=0 where tenancy_id=? and store_id=? and (bill_num=? or copy_bill_num=?) and bill_state is null ");
		this.update(usql.toString(), new Object[]
		{ "QX02", tenancyId, storeId, para.optString("bill_num"), para.optString("bill_num") });
	}

	@Override
	public void updateMeallistItemPrice(String billNum, String itemProperty, int defaultScale,
			int count, int storeId, String tenancyId) throws Exception {
		// TODO Auto-generated method stub
		StringBuilder updateSql = new StringBuilder("update pos_bill_item set item_price=round(item_amount/item_count,?) where bill_num = ? and item_property = ? and item_count > ? and store_id = ? and tenancy_id = ? ");
		
		this.update(updateSql.toString(), new Object[]{defaultScale, billNum, itemProperty, count, storeId, tenancyId});
	}
}