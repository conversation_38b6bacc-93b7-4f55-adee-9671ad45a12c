package com.tzx.framework.common.util.dao.datasource;

/**
 * 多数据源上下文
 * 
 * <p>
 * Company: 北京国信华科科技有限公司
 * </p>
 * 
 * <AUTHOR>
 * @since 2014-06-01
 * @version 1.0
 */
public class MultiDatasourceContext
{
	public static final String	POSTGRES_DATASOURCE		= "postgres";
	public static final String	POSTGRES_DATASOURCE_POS	= "postgresDs4Pos";
	public static final String	SQLSERVER_DATASOURCE	= "sqlserver";
	public static final String	MYSQL_DATASOURCE		= "mysql";
	public static final String	H2_DATASOURCE			= "h2";
}
