package com.tzx.base.po.springjdbc.dao;

import java.util.List;

import net.sf.json.JSONObject;

import com.tzx.framework.common.util.dao.GenericDao;

public interface OrganDao extends GenericDao
{
	String	NAME	= "com.tzx.base.po.springjdbc.dao.imp.OrganDaoImp";

	/**
	 * @param tenentid
	 * @param employeeOrgIds
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getOrgan(String tenentid, String employeeOrgIds) throws Exception;

	/**
	 * @param tenentid
	 * @param employeeOrgIds
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> findOrgans(String tenentid, String employeeOrgIds) throws Exception;

	// 查询门店的营业状态
	/**
	 * @param tenantId
	 * @param organId
	 * @return
	 * @throws Exception
	 */
	public String getOrganOperingStatus(String tenantId, Integer organId) throws Exception;

	// 根据操作状态查询中文名称
	/**
	 * @param tenantId
	 * @param operating_status
	 * @return
	 * @throws Exception
	 */
	public String getOperatingStatusName(String tenantId, String operating_status) throws Exception;
	
	
	// 根据id 商户id查询shopId
	/**
	 * @param tenantId
	 * @param operating_status
	 * @return
	 * @throws Exception
	 */
	public String getShopId(String tenancyId,Integer organId);
}
