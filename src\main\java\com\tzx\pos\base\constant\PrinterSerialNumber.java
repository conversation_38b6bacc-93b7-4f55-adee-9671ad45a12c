package com.tzx.pos.base.constant;

import net.sf.json.JSONObject;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 2017-10-18.
 */
public class PrinterSerialNumber {
    private PrinterSerialNumber(){}

    /*private static PrinterSerialNumber serialNumber = new PrinterSerialNumber();

    public static PrinterSerialNumber getInstance(){
        return serialNumber;
    }*/

    private static Map<String, Integer> serials = new HashMap<>();

    public static Map<String, Integer> getSerials(){
        return serials;
    }

    // 取值
    public static int getSerialNumber(String ipCom) {
        return serials.get(ipCom) != null ? Integer.parseInt(serials.get(ipCom).toString()) : 1;
    }

    // 赋值
    public static void addSerialNumber(String ipCom, Integer serialNumber){
        serials.put(ipCom, serialNumber);
    }

    public static synchronized void setSerials(List<JSONObject> serialNums) {
        for(JSONObject item: serialNums){
            serials.put(item.optString("ip_com"), item.optInt("serial_number"));
        }
    }
}
