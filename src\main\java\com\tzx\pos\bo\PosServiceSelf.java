package com.tzx.pos.bo;

import java.util.List;

import net.sf.json.JSONObject;

import com.tzx.base.bo.PosCodeService;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.pos.bo.dto.PosTableFind;

/**
 * 自助模式相关
 * 
 * <AUTHOR> 2016-04-06
 * 
 */
public interface PosServiceSelf extends PosBaseService
{
	String	NAME	= "com.tzx.pos.bo.imp.PosServiceSelfImp";

	/**
	 * 桌位查找
	 * 
	 * @param param
	 * @return
	 * @throws SystemException
	 */
	public List<PosTableFind> tableStateSelf(Data param) throws Exception;

	/**
	 * 开台、下单
	 * 
	 * @param param
	 * @param codeService
	 * @param agent
	 * @return
	 * @throws SystemException
	 */
	public Data openTableOrderDish(Data param) throws Exception;

	/**
	 * 结账
	 * 
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void posBillPaymentSelf(Data param, Data result, JSONObject printJson) throws Exception;

	/**
	 * 清除账单明细
	 * 
	 * @param param
	 * @param result
	 * @throws SystemException
	 */
	public void clearBillItemSelf(Data param, Data result) throws Exception;

	/**
	 * 清除付款明细
	 * 
	 * @param param
	 * @param result
	 * @throws SystemException
	 */
	public void clearPaymentSelf(Data param, Data result) throws Exception;

	/**
	 * 清台
	 * 
	 * @param param
	 * @param result
	 * @throws SystemException
	 */
	public void closeTable(Data param, Data result) throws Exception;

	/**
	 * 退菜
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param param
	 * @param result
	 * @param json
	 * @throws Exception
	 */
	public void retreatFood(String tenancyId, int storeId, List<?> param, Data result, JSONObject json, String agent) throws Exception;

	/**
	 * 查询付款记录
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getBillPaymentRecord(String tenancyId, int storeId, List<?> param) throws Exception;

	/**
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void lockOrUnlockTable(Data param, Data result) throws Exception;

	/**
	 * 付款状态查询
	 * 
	 * @param param
	 * @param result
	 * @param printJson
	 * @throws Exception
	 */
	public void queryBillPayment(Data param, Data result, JSONObject printJson) throws Exception;

	/**
	 * 更新账单付款状态
	 * 
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void updateBillPaymentState(Data param, Data result, JSONObject printJson) throws Exception;

	/**
	 * 自助:付款重试
	 * 
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void billPaymentRepeat(Data param, Data result, JSONObject printJson) throws Exception;
	
	/**
	 * @param param
	 * @param codeService
	 * @param result
	 * @param printJson
	 * @throws Exception
	 */
	public void cancelBillSelf(Data param, PosCodeService codeService, Data result, JSONObject printJson) throws Exception;
}
