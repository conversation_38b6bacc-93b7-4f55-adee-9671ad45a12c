package com.tzx.thirdpay.bo;

import java.util.List;

import net.sf.json.JSONObject;

import com.tzx.framework.common.entity.Data;

public interface MeituanCouponsService
{
	String	NAME	= "com.tzx.thirdpay.bo.imp.MeituanCouponsServiceImp";
	
	/**
	 * @return
	 * @throws Exception
	 */
	public String getRequestPath() throws Exception;
	
	/**美团劵验证
	 * @param tenancyId
	 * @param storeId
	 * @param paramList
	 * @return
	 * @throws Exception
	 */
	public void couponsPrepare(String tenancyId,int storeId,List<?> paramList,Data result) throws Exception;
	
	/**美团劵使用
	 * @param tenancyId
	 * @param storeId
	 * @param paramJson
	 * @return
	 * @throws Exception
	 */
	public Data couponsConsume(String tenancyId,int storeId,JSONObject paramJson) throws Exception;
	
	/**美团卷撤销
	 * @param tenancyId
	 * @param storeId
	 * @param paramJson
	 * @return
	 * @throws Exception
	 */
	public Data couponsCancel(String tenancyId,int storeId,JSONObject paramJson) throws Exception;
	
	/** 美团劵使用查询
	 * @param tenancyId
	 * @param storeId
	 * @param paramJson
	 * @return
	 * @throws Exception
	 */
	public Data couponsQuery(String tenancyId,int storeId,JSONObject paramJson) throws Exception;
}
