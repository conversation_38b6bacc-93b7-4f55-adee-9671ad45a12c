package com.tzx.base.entity;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: zhanglg
 * @Date: 2019-09-20 15:28
 */
public class CustomerOperationLogs implements Serializable {
    private static final long serialVersionUID = -7383892353249449072L;

    public Long id;
    public String tenancy_id;
    public int store_id;
    public int customer_id;
    public String customer_name;
    public String mobile;
    public String pos_num;
    public String operation_id;
    public String operation_name;
    public String title;
    public String content;
    public String old_content;
    public String report_date;
    public Date operation_time;
    public String operation_type;
    public String operation_type_name;
    public String remark;
    public String card_code;
    public String shift_id;

    public String getOld_content() {
        return old_content;
    }

    public void setOld_content(String old_content) {
        this.old_content = old_content;
    }

    public String getShift_id() {
        return shift_id;
    }

    public void setShift_id(String shift_id) {
        this.shift_id = shift_id;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTenancy_id() {
        return tenancy_id;
    }

    public void setTenancy_id(String tenancy_id) {
        this.tenancy_id = tenancy_id;
    }

    public int getStore_id() {
        return store_id;
    }

    public void setStore_id(int store_id) {
        this.store_id = store_id;
    }

    public int getCustomer_id() {
        return customer_id;
    }

    public void setCustomer_id(int customer_id) {
        this.customer_id = customer_id;
    }

    public String getCustomer_name() {
        return customer_name;
    }

    public void setCustomer_name(String customer_name) {
        this.customer_name = customer_name;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getPos_num() {
        return pos_num;
    }

    public void setPos_num(String pos_num) {
        this.pos_num = pos_num;
    }

    public String getOperation_id() {
        return operation_id;
    }

    public void setOperation_id(String operation_id) {
        this.operation_id = operation_id;
    }

    public String getOperation_name() {
        return operation_name;
    }

    public void setOperation_name(String operation_name) {
        this.operation_name = operation_name;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getReport_date() {
        return report_date;
    }

    public void setReport_date(String report_date) {
        this.report_date = report_date;
    }

    public Date getOperation_time() {
        return operation_time;
    }

    public void setOperation_time(Date operation_time) {
        this.operation_time = operation_time;
    }

    public String getOperation_type() {
        return operation_type;
    }

    public void setOperation_type(String operation_type) {
        this.operation_type = operation_type;
    }

    public String getOperation_type_name() {
        return operation_type_name;
    }

    public void setOperation_type_name(String operation_type_name) {
        this.operation_type_name = operation_type_name;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCard_code() {
        return card_code;
    }

    public void setCard_code(String card_code) {
        this.card_code = card_code;
    }
}
