package com.tzx.framework.common.constant;

public enum Code
{
	/*-----------------------------------------以为为存入sys_bill_code_rule表中的编码规则-----------------------------------------*/
	/**
	 * POS账单编号，在param中需要传入的参数有【store_id 机构ID】
	 */
	POS_BILL_CODE("pos_bill_code"),

	/**
	 * POS流水单号，在param中需要传入的参数有【store_id 机构ID】【pos_num 机台编号】
	 */
	POS_RECORD_CODE("pos_record_code"),

	/**
	 * 库存起初单流水号
	 */
	SCM_INIT_STORAGE_BILL_NO("scm_init_storage_bill_no"),
	
	/**
	 * 采购入库单流水号
	 */
	SCM_SUPPLY_IN_BILL_NO("scm_supply_in_bill_no"),
	
	/**
	 * 供应商退货入库
	 */
	SCM_SUPPLY_RETURN_BILL_NO("scm_supply_return_bill_no"),
	
	/**
	 * 领料领用单
	 */
	SCM_TRANSFER_PICKING_BILL_NO("scm_transfer_picking_bill_no"),
	
	/**
	 * 退料单
	 */
	SCM_TRANSFER_RETURN_BILL_NO("scm_transfer_return_bill_no"),
	
	/**
	 * 调拨单
	 */
	SCM_TRANSFER_TRANSFER_BILL_NO("scm_transfer_transfer_bill_no"),
	
	/**
	 * 报损单
	 */
	SCM_LOSS_BILL_NO("scm_loss_bill_no"),
	
	/**
	 * 溢出单
	 */
	SCM_PROFIT_BILL_NO("scm_profit_bill_no"),
	
	/**
	 * 消耗单
	 */
	SCM_LOSS_CONSUME_BILL_NO("scm_loss_consume_bill_no"),
	
	/**
	 * 盘点单
	 */
	SCM_STOCKTAKING_PERIOD_BILL_NO("scm_stocktaking_period_bill_no"),
	
	/**
	 * 周盘单
	 */
	SCM_STOCKTAKING_WEEK_PERIOD_BILL_NO("scm_stocktaking_weekperiod_bill_no"),
	
	/**
	 * 月盘单
	 */
	SCM_STOCKTAKING_MONTH_PERIOD_BILL_NO("scm_stocktaking_monthperiod_bill_no"),

	/**
	 * 入库单
	 */
	SCM_IN_BILL_NO("scm_in_bill_no"),
	
	/**
	 * 出库单
	 */
	SCM_OUT_BILL_NO("scm_out_bill_no"),
	
	/**
	 * 供应商结算单
	 */
	SCM_SETTLEMENT_SUPPLY_BILL_NO("scm_settlement_supply_bill_no"),

	/**
	 * 询价单
	 */
	SCM_INQUIRY_BILL_NO("scm_inquiry_bill_no"),

	/**
	 * 订货单
	 */
	SCM_ORDER_BILL_NO("scm_order_bill_no"),

	/**
	 * 模板单
	 */
	SCM_TEMPLATE_BILL_NO("scm_template_bill_no"),
	
	/**
	 * 预估单
	 */
	SCM_FORECAST_BILL_NO("scm_forecast_bill_no"),

    /**
     * 配送单
     */
    SCM_DISTRUBUT_BILL_NO("scm_distrubut_bill_no"),

    /**
     * 直配单
     */
    SCM_DIRECT_DISTRUBUT_BILL_NO("scm_direct_distrubut_bill_no"),

    /**
     * 配送出库单
     */
    SCM_DISTRUBUT_OUT_BILL_NO("scm_distrubut_out_bill_no"),
	
	/**
	 * 排班表
	 */
	SCM_ORDER_SCHEDULE_BILL_NO("scm_order_schedule_bill_no"),

	/**
	 * 配送收货
	 */
	SCM_DC_IN_BILL_NO("scm_dc_in_bill_no"),
	
	/**
	 * 配送退货
	 */
	SCM_DC_OUT_BILL_NO("scm_dc_out_bill_no"),

	/**
	 * 调拨出库
	 */
	SCM_STORE_OUT_BILL_NO("scm_store_out_bill_no"),
	
	/**
	 * 调拨入库
	 */
	SCM_STORE_IN_BILL_NO("scm_store_in_bill_no"),
	
	/**
	 * 直配收货
	 */
	SCM_ORDER_DC_BILL_NO("scm_order_dc_bill_no"),

	/**
	 * POS账单批次号
	 */
	POS_BATCH_NUM("pos_batch_num"),
	
	/**
	 * 使用第三方支付,生成订单号
	 */
	POS_THIRD_PAYMENT_ORDER_NUM("pos_third_payment_order_num"),
	
	

	/*-----------------------------------------以为为存入sys_encoding_scheme表中的编码规则-----------------------------------------*/
	/**
	 * 异常原因编码，在param中需要传入的参数有【store_id 机构ID 必须为0，可以不传】【parent_code
	 * 父节点编号】【current_level 当前级数，计数如1、2、3】
	 */
	HQ_UNUSUAL_REASON_CODE("hq_unusual_reason_code"),

	/**
	 * 订餐异常原因编码设置
	 */
	CC_UNUSUAL_REASON_CODE("cc_unusual_reason_code"),

	/**
	 * 口味，在param中需要传入的参数有【store_id 机构ID 必须为0，可以不传】【parent_code
	 * 父节点编号】【current_level 当前级数，计数如1、2、3】
	 */
	ITEM_TASTE_CODE("item_taste_code"),

	/**
	 * 收支类型，在param中需要传入的参数有【store_id 机构ID 必须为0，可以不传】【parent_code
	 * 父节点编号】【current_level 当前级数，计数如1、2、3】
	 */
	HQ_INOUT_TYPE_CODE("hq_inout_type_code"),

	/**
	 * 支付方式，在param中需要传入的参数有【store_id 机构ID 必须为0，可以不传】【parent_code
	 * 父节点编号】【current_level 当前级数，计数如1、2、3】
	 */
	PAYMENT_WAY_CODE("payment_way_code"),
	
	/**
	 * 班次，在param中需要传入的参数有【store_id 机构ID 必须为0，可以不传】【parent_code
	 * 父节点编号】【current_level 当前级数，计数如1、2、3】
	 */
	DUTY_ORDER_CODE("duty_order_code"),
	
	/**
	 * 服务费，在param中需要传入的参数有【store_id 机构ID 必须为0，可以不传】【parent_code
	 * 父节点编号】【current_level 当前级数，计数如1、2、3】
	 */
	HQ_SERVICE_FEE_TYPE_CODE("hq_service_fee_type_code"),
	
	/**
	 * 桌位，在param中需要传入的参数有【store_id 机构ID 必须为0，可以不传】【parent_code
	 * 父节点编号】【current_level 当前级数，计数如1、2、3】
	 */
	TABLES_INFO_CODE("tables_info_code"),
	
	/**
	 * 在param中需要传入的参数有【store_id 机构ID】
	 */
	HQ_KVS_CODE("hq_kvs_code"),
	
	/**
	 * 机构编码，在param中需要传入的参数有【store_id 机构ID 必须为0，可以不传】【parent_code
	 * 父节点编号】【current_level 当前级数，计数如1、2、3】
	 */
	ORGAN_CODE("organ_code"),
	
	/**
	 * 机构简码
	 */
	ORGAN_BRIEF_CODE("organ_brief_code"),
	
	/**
	 * 菜品档案，在param中需要传入的参数有【store_id 机构ID 必须为0，可以不传】【parent_code
	 * 父节点编号】【current_level 当前级数，计数如1、2、3】
	 */
	HQ_ITEM_CODE("item_code"),
	
	/**
	 * 类别编码，在param中需要传入的参数有【store_id 机构ID 必须为0，可以不传】【parent_code
	 * 父节点编号】【current_level 当前级数，计数如1、2、3】
	 */
	HQ_ITEM_CLASS_CODE("hq_item_class_code"),
	
	/**
	 * 在param中需要传入的参数有【store_id 机构ID】
	 */
	HQ_ITEM_GROUP_CODE("hq_item_group_code"),
	
	/**
	 * 类别编码，在param中需要传入的参数有【store_id 机构ID 必须为0，可以不传】【current_level
	 * 当前级数，计数如1、2、3】
	 */
	HQ_ITEM_MENU_CODE("item_menu_code"),
	
	/**
	 * 员工合同编码，在param中需要传入的参数有【store_id 机构ID 必须为0，可以不传】【current_level
	 * 当前级数，计数如1、2、3】
	 */
	CONTRACT_CODE("contract_code"),
	
	/**
	 * 类别编码，在param中需要传入的参数有【store_id 机构ID 必须为0，可以不传】【current_level
	 * 当前级数，计数如1、2、3】
	 */
	HQ_ITEM_PRICECHANGE_CODE("item_pricechange_code"),
	
	/**
	 * 物料分类编码
	 */
	MATECLASS_NUM("mateclass_num"),
	
	/**
	 * 订货周期编码
	 */
	ORDERING_CYCLE_NUM("ordering_cycle_num"),
	
	/**
	 * 订货分类编码
	 */
	ORDER_CATEGORY_NUM("order_category_num"),
	
	/**
	 * 供应商类别编码
	 */
	SUPPCLASS_NUM("suppclass_num"),
	
	/**
	 * 会员积分账户流水编号，在param中需要传入的参数有【store_id 机构ID】
	 */
	BILL_CODE_CREDIT("bill_code_credit"),
	
	/**
	 * 积分交易流水编号，在param中需要传入的参数有【store_id 机构ID】
	 */
	BILL_CODE_CREDIT_LIST("bill_code_credit_list"),
	
	/**
	 * 卡流水编号，在param中需要传入的参数有【store_id 机构ID】
	 */
	CRM_CARD_TRADING_LIST("crm_card_trading_list"),
	
	/**
	 * 会员团体冲账流水编号，在param中需要传入的参数有【store_id 机构ID】
	 */
	CRM_INCORPORATION_CZ_LIST("crm_incorporation_cz_list"),
	
	/**
	 * 付款流水编号，在param中需要传入的参数有【store_id 机构ID】
	 */
	CRM_CARD_PAYMENT_LIST("crm_card_trading_list"),
	
	/**
	 * cc订单编号，在param中需要传入的参数有【channel渠道、store_id 机构ID yyyyMMdd
	 * 业务日期】【CC_ORDER_CODE】
	 */
	CC_ORDER_CODE("cc_order_code"),
	
	/**
	 * cc订单中心每天的流水号不分渠道，在param中需要传入的参数有【#、store_id 机构ID yyyyMMdd
	 * 业务日期】【cc_order_flow_code】
	 */
	CC_ORDER_FLOW_CODE("cc_order_flow_code"),
	
	/**
	 * cc订单中心每天的流水号不分渠道，在param中需要传入的参数有【渠道、yyyyMMdd 业务日期】【cc_order_flow_code】
	 */
	CC_ORDER_FLOW_CHANNEL_CODE("cc_order_flow_channel_code"),

	
	
	/*-----------------------------------------以下为存入sys_baseinfo_code_rule表中的编码规则-----------------------------------------*/
	/**
	 * 团体会员编码，在param中需要传入的参数有【store_id 机构ID，dyna_code
	 * 为配置表中指定的dyna_prefix_code对应值，可以不传】
	 */
	INCORPORATION_CODE("incorporation_code"),
	
	/**
	 * 员工编码，在param中需要传入的参数有【store_id 机构ID，dyna_code
	 * 为配置表中指定的dyna_prefix_code对应值，可以不传】
	 */
	EMPLOYEE_CODE("employee_code"),
	
	/**
	 * 会员档案编码,在param中需要传入的参数有【store_id 机构ID，dyna_code
	 * 为配置表中指定的dyna_prefix_code对应值，可以不传】
	 */
	CRM_CUSTOMER_CODE("crm_customer_code"),
	
	/**
	 * 仓库编码,在param中需要传入的参数有【store_id 机构ID，dyna_code
	 * 为配置表中指定的dyna_prefix_code对应值，可以不传】
	 */
	WAREHOUSE_NUM("warehouse_num"),
	
	/**
	 * 供应商编码,在param中需要传入的参数有【store_id 机构ID，dyna_code
	 * 为配置表中指定的dyna_prefix_code对应值，可以不传】
	 */
	SUPPLIER_NUM("supplier_num"),
	
	/**
	 * 物料编码,在param中需要传入的参数有【store_id 机构ID，dyna_code
	 * 为配置表中指定的dyna_prefix_code对应值，可以不传】
	 */
	MATERIAL_NUM("material_num"), 
	
	/**
	 * 
	 */
	ITEM_CODE("item_code"),
	
	/**
	 * 物料BOM编码,在param中需要传入的参数有【store_id 机构ID，dyna_code
	 * 为配置表中指定的dyna_prefix_code对应值，可以不传】
	 */
	BOMGROUP_NUM("bomgroup_num"),
	
	/**
	 * 团体会员充值流水编号
	 */
	BILL_CODE_INCORPORATION_RECHARGE_LIST("bill_code_incorporation_recharge_list"),
	
	/**
	 * 团体会员充值付款方式流水
	 */
	BILL_CODE_INCORPORATION_PAYMENT_LIST("bill_code_incorporation_payment_list"),
	

	/**
	 * 手工调账流水
	 */
	BILL_CODE_CRM_ARTIFICIAL_LIST("bill_code_crm_artificial_list"),

	/**
	 * 挂账流水,在param中需要传入的参数有【store_id 机构ID，dyna_code
	 * 为配置表中指定的dyna_prefix_code对应值，可以不传】
	 */
	CRM_INCORPORATION_GZLIST("crm_incorporation_gzlist_code"),

	/**
	 * 会员群体编码
	 * 
	 */
	CRM_GROUP_CODE("crm_group_code"),
	
	/**
	 * 会员等级编码
	 * 
	 */
	CRM_LEVEL_CODE("crm_level_code"),
	
	/**
	 * 设备编号
	 */
	DEVICES_CODE("devices_code"),
	
	/**
	 * 时段特价
	 */
	HQ_TIME_PRICE_CODE("time_price_code"),
	
	/**
	 * 菜品券编码
	 */
	COUPONS_DISH_CODE("coupons_dish_code"),
	
	/**
	 * 代金券编码
	 */
	COUPONS_DEDUCT_CODE("coupons_deduct_code"),
	
	/**
	 * 机构分组编码
	 */
	ORGAN_GROUP_CODE("organ_group_code"),
	
	/**
	 * 卡分类编码
	 */
	CRM_CARD_CLASS_CODE("crm_card_class_code"),
	
	/**
	 * 排队编码
	 */
	HQ_QUEUE_CODE("hq_queue_code"),
	
	/**
	 * 折扣方案
	 */
	DISCOUNT_CASE_CODE("discount_case_code"),
	/**
	 * 机构预付款账户
	 */
	ORGAN_ACCOUNT_CODE("organ_account_code"), 
	
	/**
	 * 取餐号
	 */
	POS_BILL_DISH_CODE("pos_bill_dish_code"),
	/**
	 * POS预点编号，在param中需要传入的参数有【store_id 机构ID】
	 */
	BILL_BOOKED_NUM("bill_booked_num")
	;

	private final String	type;

	private Code(String type)
	{
		this.type = type;
	}

	public String getType()
	{
		return this.type;
	}

}
