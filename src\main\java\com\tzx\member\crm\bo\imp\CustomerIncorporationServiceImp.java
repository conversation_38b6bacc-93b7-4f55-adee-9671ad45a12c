package com.tzx.member.crm.bo.imp;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Service;

import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.member.common.entity.CrmIncorporationArrearListEntity;
import com.tzx.member.common.entity.PosCustomerOperateListEntity;
import com.tzx.member.crm.bo.CustomerIncorporationService;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.DateUtil;

@Service(CustomerIncorporationService.NAME)
public class CustomerIncorporationServiceImp extends CustomerServiceImp implements CustomerIncorporationService
{

	@Override
	public void customerIncorporationPost(String tenancyId, int storeId, JSONObject param, Data resData, String operType) throws Exception
	{
		Integer incorporationId = param.optInt("incorporation_id");
		Integer customerId = param.optInt("customer_id");
		String password = param.optString("password");
		Double gzMoney = param.optDouble("gz_money");
		Double billMoney = param.optDouble("bill_money");
		String billCode = param.optString("bill_code");
		String batchNum = param.optString("batch_num");
		String reportDateStr = param.optString("report_date");
		Integer shiftId = param.optInt("shift_id");
		String optNum = param.optString("opt_num");
		String posNum = param.optString("pos_num");
		String chanel = param.optString("chanel");

		String optName = customerDao.getEmpNameById(optNum, tenancyId, storeId);

		Timestamp currentTime = DateUtil.currentTimestamp();

		PosCustomerOperateListEntity customerOperate = new PosCustomerOperateListEntity();
		customerOperate.setTenancy_id(tenancyId);
		customerOperate.setStore_id(storeId);
		customerOperate.setBusiness_date(DateUtil.parseDate(reportDateStr));
		customerOperate.setOperator_id(Integer.parseInt(optNum));
		customerOperate.setPos_num(posNum);
		customerOperate.setShift_id(shiftId);
		customerOperate.setOperate_time(currentTime);
		customerOperate.setChanel(chanel);
		customerOperate.setService_type(SysDictionary.SERVICE_TYPE_CONSUME);
		customerOperate.setOperat_type(SysDictionary.OPERAT_TYPE_INCORPORATION);
		customerOperate.setCustomer_id(customerId);
		customerOperate.setIncorporation_id(incorporationId);
		customerOperate.setThird_bill_code(billCode);
		customerOperate.setBatch_num(batchNum);
		customerOperate.setTrade_amount(gzMoney);
		customerOperate.setTrade_credit(0d);
		customerOperate.setDeposit(0d);
		customerOperate.setSales_price(0d);
		customerOperate.setOperate_state(SysDictionary.THIRD_PAY_STATUS_PAYING);
		customerOperate.setCancel_state(SysDictionary.CUSTOMER_OPERATE_CANCEL_STATE_N);
		customerOperate.setRequest_status(SysDictionary.REQUEST_STATUS_ING);
		customerOperate.setLast_query_time(currentTime);
		customerOperate.setQuery_count(0);
		customerOperate.setAction_type(operType);

		try
		{
			JSONObject requestJson = new JSONObject();
			requestJson.put("incorporation_id", incorporationId);
			requestJson.put("customer_id", customerId);
			requestJson.put("password", password);
			requestJson.put("gz_money", gzMoney);
			requestJson.put("bill_money", billMoney);
			requestJson.put("bill_code", billCode);
			requestJson.put("batch_num", batchNum);
			requestJson.put("business_date", reportDateStr);// GJ20160408
			requestJson.put("shift_id", shiftId);
			requestJson.put("operator_id", optNum);
			requestJson.put("operator", optName);
			requestJson.put("operate_time", DateUtil.format(currentTime));

			List<JSONObject> requestList = new ArrayList<JSONObject>();
			requestList.add(requestJson);

			Data requestData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
			requestData.setType(Type.INCORPORATION_GZ);
			requestData.setOper(Oper.add);
			requestData.setData(requestList);

			this.commonPost(JSONObject.fromObject(requestData).toString(), resData);

			this.customerIncorporationResult(tenancyId, storeId, customerOperate, resData);
		}
		catch (Exception e)
		{
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

		customerDao.insertPosCustomerOperateList(tenancyId, storeId, customerOperate);
	}

	@Override
	public Data queryCustomerIncorporation(String tenancyId, int storeId, JSONObject param) throws Exception
	{
		// TODO Auto-generated method stub
		String incorporationId = param.optString("incorporation_id");
		String customerId = param.optString("customer_id");
		String billCode = param.optString("bill_code");
		String batchNum = param.optString("batch_no");

		PosCustomerOperateListEntity customerOperate = customerDao.queryPosCustomerOperateListByIncorporation(tenancyId, storeId, incorporationId, customerId, billCode, batchNum,SysDictionary.OPERAT_TYPE_INCORPORATION);

		return this.queryCustomerIncorporation(tenancyId, storeId, customerOperate);
	}
	
	@Override
	public Data queryCustomerIncorporation(String tenancyId, int storeId, PosCustomerOperateListEntity customerOperate)throws Exception
	{
		JSONObject requestJson = new JSONObject();
		requestJson.put("incorporation_id", customerOperate.getIncorporation_id());
		requestJson.put("customer_id", customerOperate.getCustomer_id());
		requestJson.put("bill_code", customerOperate.getThird_bill_code());
		requestJson.put("batch_num", customerOperate.getBatch_num());

		List<JSONObject> requestList = new ArrayList<JSONObject>();
		requestList.add(requestJson);

		Data requestData = Data.get(tenancyId, storeId, 0);
		requestData.setType(Type.INCORPORATION_GZ);
		requestData.setOper(Oper.find);
		requestData.setData(requestList);
		Data responseData = Data.get();
		this.commonPost(JSONObject.fromObject(requestData).toString(), responseData);

		this.customerIncorporationResult(tenancyId, storeId, customerOperate, responseData);
		try
		{
			customerDao.updatePosCustomerOperateList(tenancyId, storeId, customerOperate);
		}
		catch (Exception e)
		{
			e.printStackTrace();
			logger.error("团体挂账pos_customer_operate_list报错:", e);
		}

		return responseData;
	}

	private void customerIncorporationResult(String tenancyId, int storeId, PosCustomerOperateListEntity customerOperate, Data responseData) throws Exception
	{
		Timestamp currentTime = DateUtil.currentTimestamp();
		if (responseData.isSuccess())
		{
			// 插入管体挂账流水表
			JSONObject resultObj = JSONObject.fromObject(responseData.getData().get(0));
			
			String bill_code = resultObj.optString("bill_code");
			String incorporationName = resultObj.optString("name");
			String customer_name = resultObj.optString("customer_name");
			
			JSONObject gzJo = resultObj.optJSONObject("gzlist");
			
			double gz_money = gzJo.optDouble("gz_money");
			if (Double.isNaN(gz_money))
			{
				gz_money = 0d;
			}
			double cz_money = gzJo.optDouble("cz_money");
			if (Double.isNaN(cz_money))
			{
				cz_money = 0d;
			}
			double mz_money = gzJo.optDouble("mz_money");
			if (Double.isNaN(mz_money))
			{
				mz_money = 0d;
			}
			double wcz_money = gzJo.optDouble("wcz_money");
			if (Double.isNaN(wcz_money))
			{
				wcz_money = 0d;
			}
			CrmIncorporationArrearListEntity incorporationGzList = new CrmIncorporationArrearListEntity();
			incorporationGzList.setIncorporation_id(gzJo.optInt("incorporation_id"));
			incorporationGzList.setBusiness_date(DateUtil.parseDate(gzJo.optString("business_date")));
			incorporationGzList.setBill_code(bill_code);
			incorporationGzList.setGz_money(gz_money);
			incorporationGzList.setGz_person(gzJo.optString("gz_person"));
			incorporationGzList.setRemark(gzJo.optString("remark"));
			incorporationGzList.setIs_cz(gzJo.optString("is_cz"));
			incorporationGzList.setCz_money(cz_money);
			incorporationGzList.setMz_money(mz_money);
			incorporationGzList.setWcz_money(wcz_money);
			incorporationGzList.setCz_person(gzJo.optString("cz_person"));
			incorporationGzList.setCz_time(DateUtil.parseDate(gzJo.optString("cz_time")));
			incorporationGzList.setOperate_time(currentTime); 
			incorporationGzList.setOperator(gzJo.optString("operator")); 
			incorporationGzList.setThird_bill_code(gzJo.optString("third_bill_code"));
			incorporationGzList.setValid_state(gzJo.optString("valid_state"));
			incorporationGzList.setName(gzJo.optString("name"));
			incorporationGzList.setCustomer_name(gzJo.optString("customer_name")); 

			try
			{
				customerDao.insertCrmIncorporationGzList(tenancyId, storeId, incorporationGzList);
			}
			catch (Exception e)
			{
				e.printStackTrace();
				logger.info("团体挂账失败：" + e);
				throw SystemException.getInstance(PosErrorCode.OPER_ERROR);
			}

			// customerOperate.setCustomer_code();
			// customerOperate.setMobil();
			 customerOperate.setCustomer_name(customer_name);
			 customerOperate.setIncorporation_name(incorporationName);

			customerOperate.setBill_code(bill_code);
			customerOperate.setFinish_time(currentTime);
			customerOperate.setOperate_state(SysDictionary.THIRD_PAY_STATUS_SUCCESS);
			customerOperate.setRequest_status(SysDictionary.REQUEST_STATUS_COMPLETE);
		}
		else if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
		{
			customerOperate.setOperate_state(SysDictionary.THIRD_PAY_STATUS_PAYING);
			customerOperate.setRequest_status(SysDictionary.REQUEST_STATUS_FAILURE);
			customerOperate.setRequest_code(String.valueOf(responseData.getCode()));
			customerOperate.setRequest_msg(responseData.getMsg());
		}
		else
		{
			customerOperate.setFinish_time(currentTime);
			customerOperate.setOperate_state(SysDictionary.THIRD_PAY_STATUS_FAIL);
			customerOperate.setRequest_status(SysDictionary.REQUEST_STATUS_COMPLETE);
			customerOperate.setRequest_code(String.valueOf(responseData.getCode()));
			customerOperate.setRequest_msg(responseData.getMsg());
		}
	}

	@Override
	public void customerIncorporationCancelPost(String tenancyId, int storeId, JSONObject param, Data resData, String operType) throws Exception
	{
		// TODO Auto-generated method stub
		List<JSONObject> requestList = new ArrayList<JSONObject>();
		requestList.add(param);

		Data requestData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
		requestData.setType(Type.INCORPORATION_GZ);
		requestData.setOper(Oper.update);
		requestData.setData(requestList);

		this.commonPost(JSONObject.fromObject(requestData).toString(), resData);

	}

	@Override
	public Data queryCustomerIncorporationCancel(String tenancyId, int storeId, PosCustomerOperateListEntity customerOperate) throws Exception
	{
		// TODO Auto-generated method stub
		return null;
	}

}
