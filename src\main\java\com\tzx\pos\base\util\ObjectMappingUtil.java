package com.tzx.pos.base.util;

import java.lang.reflect.Field;
import java.util.List;

public class ObjectMappingUtil
{
	/** 反射实体类,组织SQl查询的列字段
	 * @exception 实体类中List类型的属性排除
	 * @param beanClass
	 * @return
	 * @throws Exception
	 */
	public static String getColumnSqlByBean(Class<?> beanClass) throws Exception
	{
		StringBuffer sqlStr=new StringBuffer();
		Field[] fields = beanClass.getDeclaredFields();
		for(Field field :fields)
		{
			if(List.class.equals(field.getType()))
			{
				continue;
			}
			if(sqlStr.length()>0)
			{
				sqlStr.append(",");
			}
			sqlStr.append(field.getName());
		}
		return sqlStr.toString();
	}
}
