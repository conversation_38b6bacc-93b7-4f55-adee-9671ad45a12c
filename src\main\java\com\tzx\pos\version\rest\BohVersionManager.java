package com.tzx.pos.version.rest;

import com.twmacinta.util.MD5;
import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.util.ObjectMapper;

import com.tzx.pos.version.Device;
import net.sf.json.JSONObject;

import org.apache.commons.httpclient.DefaultHttpMethodRetryHandler;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.HttpClientError;
import org.apache.commons.httpclient.HttpStatus;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.methods.StringRequestEntity;
import org.apache.commons.httpclient.params.HttpMethodParams;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.Properties;

/**
 * <AUTHOR> on 2017-03-06
 */
@Controller("BohVersionManager")
@RequestMapping("/versionManagerRest")
public class BohVersionManager {


//    private static final String FEEDBACK_URL="/productUpdateRest/feedback";
    //升级程序2.0地址
    private static final String FEEDBACK_URL="/productUpdateRest/new/feedbackDevicesInfo";

    Logger logger = Logger.getLogger(BohVersionManager.class);

    /**
     * 版本检查
     *
     * @return Data
     */
    @RequestMapping(value = "check", method = RequestMethod.POST)
    @ResponseBody
    public Data versionCheck(HttpServletRequest request, HttpServletResponse response, @RequestBody JSONObject jsobj) {


        ObjectMapper objectMapper = new ObjectMapper();
        Data param = null;
        try {

            param = objectMapper.readValue(jsobj.toString(), Data.class);
            logger.info("版本检查请求："+param);

            if(Type.VERSION.equals(param.getType())&& Oper.check.equals(param.getOper())&&!param.getData().isEmpty()){
                JSONObject object=JSONObject.fromObject(param.getData().get(0));
                String appId=object.optString("app_id");
                String sourceVersion=object.optString("source_version");

                String versionFilePath = request.getServletContext().getRealPath("/config/version.properties");

                String targetVersion= loadProperties(versionFilePath,appId.concat(".version"));
                String pkgname=loadProperties(versionFilePath,appId.concat(".pkgname"));

                String pkgHash=null;
                File pkg=new File(request.getServletContext().getRealPath("/download/"+pkgname));
                if(pkg.exists()&&pkg.isFile()){
                    pkgHash=MD5.asHex(MD5.getHash(pkg));
                }else {
                    throw new IOException("未找到所请求AppId的程序包:"+appId);
                }
                String downloadUrl=request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + request.getContextPath() + "/download/" + pkgname;

                object=new JSONObject();
                object.put("target_version",targetVersion);
                object.put("download_url",downloadUrl);
                object.put("md5",pkgHash);

                param.setSuccess(true);
                param.setCode(0);
                param.setData(Arrays.asList(object));
                param.setMsg("查询版本成功");
            }else{
                param.setSuccess(false);
                param.setCode(2);
                param.setMsg("请求参数不合法!");
            }


        } catch (Exception e) {
            param.setSuccess(false);
            param.setCode(1);
            param.setMsg("系统异常!>>>"+e.getMessage());

        }
        logger.info("版本检查接口响应, code: " + param.getCode() + ", msg: " + param.getMsg());

        return param;
    }

    /**
     * 版本升级反馈
     * @return Data
     */
    @RequestMapping(value = "feedback", method = RequestMethod.POST)
    @ResponseBody
    public Data upgradeFeedback(HttpServletRequest request, @RequestBody JSONObject jsobj) {

        ObjectMapper objectMapper = new ObjectMapper();
        Data param = null;
        try {
            param = objectMapper.readValue(jsobj.toString(), Data.class);
            logger.info("上传升级信息");

            if (Type.VERSION.equals(param.getType()) && Oper.feedback.equals(param.getOper()) && !param.getData().isEmpty()) {
                JSONObject object = JSONObject.fromObject(param.getData().get(0));

                String versionFilePath = request.getServletContext().getRealPath("/config/version.properties");
                String appId = loadProperties(versionFilePath, "app.identifier").concat(".").concat(object.optString("app_id"));
                String sourceVersion = loadProperties(versionFilePath, "app.original.version");
                String targetVersion = loadProperties(versionFilePath, "app.version");
                String serverRoomId = loadProperties(versionFilePath, "app.serverRoomId");

                object.put("store_id", Constant.getSystemMap().get("store_id"));
                object.put("tenancy_id", Constant.getSystemMap().get("tenent_id"));

                object.put("app_id", appId);
                object.put("app_name", Device.getNameByCode(object.optString("app_id")));
                object.put("source_version", sourceVersion);
                object.put("target_version", targetVersion);
                object.put("upgrade_state", object.optString("upgrade_state"));
                object.put("server_room_id", serverRoomId);
                String uploadData = object.toString();

                logger.info("上传升级信息数据: " + uploadData);

                String msg = "";
                HttpClient client = new HttpClient();

                String file = new File(request.getServletContext().getRealPath("/")).getParentFile().getParent() + "/conf/new_console.properties";
                PostMethod method = new PostMethod(loadProperties(file, "update.server.auto.url") + FEEDBACK_URL);
                method.getParams().setParameter(HttpMethodParams.RETRY_HANDLER, new DefaultHttpMethodRetryHandler(3, false));
                try {
                    StringRequestEntity requestEntity = new StringRequestEntity(uploadData, "application/json", "UTF-8");
                    method.setRequestEntity(requestEntity);
                    int statusCode = client.executeMethod(method);

                    if (statusCode != HttpStatus.SC_OK) {
                        logger.error("与OM通讯失败:" + method.getStatusLine());
                        throw new HttpClientError("与OM通讯失败:" + method.getStatusLine());
                    }
                    String res = IOUtils.toString(method.getResponseBodyAsStream(), "UTF-8");
                    JSONObject result = JSONObject.fromObject(res);
                    msg = result.optString("msg");
                    if (0 != result.optInt("code")) {
                        logger.error("更新数据上传失败,OM返回消息:" + msg);
                        throw new Exception("更新数据上传失败,OM返回消息:" + msg);
                    }
                } catch (Exception e) {
                    logger.error("BOH上传更新数据时发生异常", e);
                    throw e;
                } finally {
                    method.releaseConnection();
                }

                param.setSuccess(true);
                param.setCode(0);
                param.setMsg("更新数据上传成功");
            } else {
                param.setSuccess(false);
                param.setCode(2);
                param.setMsg("请求参数不合法");
            }
        } catch (Exception e) {
            logger.error("更新数据上传发生错误", e);
            param.setSuccess(false);
            param.setCode(1);
            param.setMsg("系统异常>>>" + e.getMessage());
        }
        logger.info("版本升级反馈接口响应, code: " + param.getCode() + ", msg: " + param.getMsg());

        return param;

    }

    private String loadProperties(String filePath,String key) throws IOException{

        InputStream is=null;
        File versionFile = new File(filePath);
        Properties properties=new Properties();
        try {
            is=FileUtils.openInputStream(versionFile);
            properties.load(is);
            return properties.getProperty(key);
        } finally {
            IOUtils.closeQuietly(is);
        }

    }
}
