/**
 * @Title: SFTPConstants.java 
 * @Package com.tzx.framework.common.util 
 * @Description: TODO(用一句话描述该文件做什么) 
 * <AUTHOR> A18ccms_gmail_com  
 * @date 2017-1-10 下午6:04:00 
 * @version V1.0  
 */
package com.tzx.framework.common.util;

import java.util.HashMap;
import java.util.Map;

import javax.servlet.ServletContext;

import com.tzx.framework.common.constant.Constant;

/**
 * @ClassName: SFTPConstants 
 * @Description: TODO(这里用一句话描述这个类的作用) 
 * <AUTHOR> a18ccms_gmail_com 
 * @date 2017-1-10 下午6:04:00  
 */
public class SFTPConstants
{
	public static Map<String,String> systemMap = new HashMap<String,String>();

	public static Map<String, String> getSystemMap() {
		return systemMap;
	}

	public static void setSystemMap(Map<String, String> systemMap,ServletContext sc) {
		Constant.systemMap = systemMap;
		
	}
	public static final String SFTP_REQ_HOST ="host";
	public static final String SFTP_REQ_PORT ="port";
	public static final String SFTP_REQ_USERNAME = "username";
	public static final String SFTP_REQ_PASSWORD = "password";
}
