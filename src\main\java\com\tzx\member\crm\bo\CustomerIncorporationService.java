package com.tzx.member.crm.bo;

import net.sf.json.JSONObject;

import com.tzx.framework.common.entity.Data;
import com.tzx.member.common.entity.PosCustomerOperateListEntity;

public interface CustomerIncorporationService extends CustomerService
{
	String	NAME	= "com.tzx.member.crm.bo.imp.CustomerIncorporationServiceImp";

	/**
	 * 团体挂账接口方法
	 * 
	 * @param param
	 * @param resData
	 * @throws Exception
	 */
	public void customerIncorporationPost(String tenancyId, int storeId, JSONObject param, Data resData, String operType) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param param
	 * @param resData
	 * @throws Exception
	 */
	public Data queryCustomerIncorporation(String tenancyId, int storeId, JSONObject param) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param customerOperate
	 * @return
	 * @throws Exception
	 */
	public Data queryCustomerIncorporation(String tenancyId, int storeId, PosCustomerOperateListEntity customerOperate) throws Exception;
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param param
	 * @param resData
	 * @throws Exception
	 */
	public void customerIncorporationCancelPost(String tenancyId, int storeId, JSONObject param, Data resData, String operType) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param customerOperate
	 * @return
	 * @throws Exception
	 */
	public Data queryCustomerIncorporationCancel(String tenancyId, int storeId, PosCustomerOperateListEntity customerOperate) throws Exception;
}
