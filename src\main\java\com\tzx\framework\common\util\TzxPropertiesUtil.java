package com.tzx.framework.common.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.io.Reader;
import java.io.Writer;
import java.util.Properties;

public class TzxPropertiesUtil
{
	
	public static Properties load(File file)
	{
		Properties properties = new Properties();
		
		Reader in = null;
		
		try
		{
			in = new InputStreamReader(new FileInputStream(file), "UTF-8");
			
			properties.load(in);
		}
		catch(Exception e)
		{
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if(in != null)in.close();
			}
			catch (IOException e)
			{
				e.printStackTrace();
			}
		}
		
		return properties;
	}
	
	public static void save(Properties properties, File file, String comments)
	{
		Writer out = null;
		
		try
		{
			out = new OutputStreamWriter(new FileOutputStream(file), "UTF-8");
			
			properties.store(out, comments);
		}
		catch(Exception e)
		{
			e.printStackTrace();
		}
		finally
		{
			try
			{
				if(out != null)out.close();
			}
			catch (IOException e)
			{
				e.printStackTrace();
			}
		}
	}
}
