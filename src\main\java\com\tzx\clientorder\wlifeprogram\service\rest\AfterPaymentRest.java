package com.tzx.clientorder.wlifeprogram.service.rest;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.tzx.clientorder.wlifeprogram.bo.AfterPaymentService;
import com.tzx.clientorder.wlifeprogram.common.constant.WlifePromptMessage;
import com.tzx.clientorder.wlifeprogram.common.util.ExceptionPrintUtil;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.OtherSystemException;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.pos.asyn.pool.ThreadPool;
import com.tzx.pos.asyn.thread.OrderingPrintThread;

import net.sf.json.JSONObject;

/**
 * 微生活小程序调用接口（后付业务）
 */

@Controller("AfterPaymentRest")
@RequestMapping("/wlife/afterPaymentRest")
public class AfterPaymentRest {
    private static final Logger logger = Logger.getLogger(AfterPaymentRest.class);

    @Resource(name = AfterPaymentService.NAME)
    private AfterPaymentService afterPaymentService;
    
    /**后付下单接口
     * @param request
     * @param jsobj
     */
    @RequestMapping(value = "/ordering", method = RequestMethod.POST)
    @ResponseBody
    public JSONObject ordering(HttpServletRequest request, @RequestBody JSONObject jsobj){
        JSONObject responseJson = new JSONObject();

        logger.info("ordering 请求入参===" + jsobj.toString());

        try
        {
            setBaseParams(jsobj);
            String tenancyId = jsobj.optString("business_id");
            int storeId = jsobj.optInt("shop_id");

            JSONObject printJson = new JSONObject();
            afterPaymentService.saveOrUpdateOrder(jsobj, printJson, responseJson);
            // 数据上传
//            posDishService.upload(tenancyId, storeId+"", "", "", "", responseJson.optString("oid"));

            // 异步执行分单打印
            Data data = new Data();
            data.setTenancy_id(tenancyId);
            data.setStore_id(storeId);
            OrderingPrintThread orderingPrintThread = new OrderingPrintThread(printJson, data);
            ThreadPool.getPrintThreadPool().execute(orderingPrintThread);
        }
        catch (OtherSystemException se)
        {
            ExceptionPrintUtil.buildWlifeOtherSysExceptionData(logger, se, responseJson, WlifePromptMessage.ORDER_DISH_FAILURE);
        }
        catch (SystemException se)
        {
            ExceptionPrintUtil.buildWlifeSysExceptionData(logger, se, responseJson, WlifePromptMessage.ORDER_DISH_FAILURE);
        }
        catch (Exception e)
        {
            logger.error(" ordering failied ...  order_id" +jsobj.optString("order_id"), e);
            ExceptionPrintUtil.buildWlifeExceptionData(logger, e, responseJson, WlifePromptMessage.ORDER_DISH_FAILURE);
        }

        logger.info("ordering 响应出参===" + responseJson.toString());
        return responseJson;
    }

    /** 支付上传订单接口
     * @param request
     * @param jsobj
     * @return
     */
    @RequestMapping(value = "/payUploadBill", method = RequestMethod.POST)
    @ResponseBody
    public JSONObject payUploadBill(HttpServletRequest request, @RequestBody JSONObject jsobj){
        JSONObject responseJson = new JSONObject();

        logger.info("payUploadBill 请求入参===" + jsobj.toString());

        try
        {
            setBaseParams(jsobj);
            afterPaymentService.payUploadBill(jsobj, responseJson);
        }
        catch (OtherSystemException se)
        {
            ExceptionPrintUtil.buildWlifeOtherSysExceptionData(logger, se, responseJson, WlifePromptMessage.UPLOAD_BILL_FAILURE);
        }
        catch (SystemException se)
        {
            ExceptionPrintUtil.buildWlifeSysExceptionData(logger, se, responseJson, WlifePromptMessage.UPLOAD_BILL_FAILURE);
        }
        catch (Exception e)
        {
            logger.error(" payUploadBill failed ...  order_id" +jsobj.optString("order_id"), e);
            ExceptionPrintUtil.buildWlifeExceptionData(logger, e, responseJson, WlifePromptMessage.UPLOAD_BILL_FAILURE);
        }

        logger.info("payUploadBill 响应出参===" + responseJson.toString());
        return responseJson;
    }

    /** 结账接口
     * @param request
     * @param jsobj
     * @return
     */
    @RequestMapping(value = "/billPayment", method = RequestMethod.POST)
    @ResponseBody
    public JSONObject billPayment(HttpServletRequest request, @RequestBody JSONObject jsobj){
        JSONObject responseJson = new JSONObject();

        logger.info("billPayment 请求入参===" + jsobj.toString());

        try
        {
            setBaseParams(jsobj);
            afterPaymentService.paymentClose(jsobj,responseJson);
        }
        catch (OtherSystemException se)
        {
            ExceptionPrintUtil.buildWlifeOtherSysExceptionData(logger, se, responseJson, WlifePromptMessage.ORDER_PAYMENT_FAILURE);
        }
        catch (SystemException se)
        {
            ExceptionPrintUtil.buildWlifeSysExceptionData(logger, se, responseJson, WlifePromptMessage.ORDER_PAYMENT_FAILURE);
        }
        catch (Exception e)
        {
            logger.error(" billPayment failied ...  order_id" +jsobj.optString("order_id"), e);
            ExceptionPrintUtil.buildWlifeExceptionData(logger, e, responseJson, WlifePromptMessage.ORDER_PAYMENT_FAILURE);
        }

        logger.info("billPayment 响应出参===" + responseJson.toString());
        return responseJson;
    }
    
	/**
	 * 锁单接口
	 * 
	 * @param request
	 * @param jsobj
	 * @return
	 */
	@RequestMapping(value = "/lockOrder", method = RequestMethod.POST)
	@ResponseBody
	public JSONObject lockOrder(HttpServletRequest request, @RequestBody JSONObject jsobj)
	{
		JSONObject responseJson = new JSONObject();

        logger.info("lockOrder 请求入参===" + jsobj.toString());
        try
        {
        	String tenancyId = null;
    		if (com.tzx.framework.common.constant.Constant.systemMap.containsKey("tenent_id"))
    		{
    			tenancyId = com.tzx.framework.common.constant.Constant.systemMap.get("tenent_id");
    		}
    		Integer storeId = null;
    		if (com.tzx.framework.common.constant.Constant.systemMap.containsKey("store_id"))
    		{
    			storeId = Integer.valueOf(com.tzx.framework.common.constant.Constant.systemMap.get("store_id"));
    		}
    		
    		if (StringUtils.isEmpty(tenancyId) || storeId == 0){
                throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
            }
    		
			afterPaymentService.lockOrder(tenancyId, storeId, jsobj, responseJson);
        }
        catch (OtherSystemException se)
        {
            ExceptionPrintUtil.buildWlifeOtherSysExceptionData(logger, se, responseJson, WlifePromptMessage.LOCK_ORDER_FAILURE);
        }
        catch (SystemException se)
        {
            ExceptionPrintUtil.buildWlifeSysExceptionData(logger, se, responseJson, WlifePromptMessage.LOCK_ORDER_FAILURE);
        }
        catch (Exception e)
        {
            logger.error(" lockOrder failied ...  order_id" +jsobj.optString("order_id"), e);
            ExceptionPrintUtil.buildWlifeExceptionData(logger, e, responseJson, WlifePromptMessage.LOCK_ORDER_FAILURE);
        }

        logger.info("lockOrder 响应出参===" + responseJson.toString());
        return responseJson;
	}

	/**
	 *  解锁接口
	 * 
	 * @param request
	 * @param jsobj
	 * @return
	 */
	@RequestMapping(value = "/unlockOrder", method = RequestMethod.POST)
	@ResponseBody
	public JSONObject unlockOrder(HttpServletRequest request, @RequestBody JSONObject jsobj)
	{
		JSONObject responseJson = new JSONObject();

        logger.info("unlockOrder 请求入参===" + jsobj.toString());
        try
        {
        	String tenancyId = null;
    		if (com.tzx.framework.common.constant.Constant.systemMap.containsKey("tenent_id"))
    		{
    			tenancyId = com.tzx.framework.common.constant.Constant.systemMap.get("tenent_id");
    		}
    		Integer storeId = null;
    		if (com.tzx.framework.common.constant.Constant.systemMap.containsKey("store_id"))
    		{
    			storeId = Integer.valueOf(com.tzx.framework.common.constant.Constant.systemMap.get("store_id"));
    		}
    		
    		if (StringUtils.isEmpty(tenancyId) || storeId == 0){
                throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
            }
    		
			afterPaymentService.unlockOrder(tenancyId, storeId, jsobj, responseJson);
        }
        catch (OtherSystemException se)
        {
            ExceptionPrintUtil.buildWlifeOtherSysExceptionData(logger, se, responseJson, WlifePromptMessage.UNLOCK_ORDER_FAILURE);
        }
        catch (SystemException se)
        {
            ExceptionPrintUtil.buildWlifeSysExceptionData(logger, se, responseJson, WlifePromptMessage.UNLOCK_ORDER_FAILURE);
        }
        catch (Exception e)
        {
            logger.error(" unlockOrder failied ...  order_id" +jsobj.optString("out_order_id"), e);
            ExceptionPrintUtil.buildWlifeExceptionData(logger, e, responseJson, WlifePromptMessage.UNLOCK_ORDER_FAILURE);
        }

        logger.info("unlockOrder 响应出参===" + responseJson.toString());
        return responseJson;
	}
    
    private  void  setBaseParams(JSONObject params){

        if(com.tzx.framework.common.constant.Constant.systemMap.containsKey("tenent_id"))
        {
            String tenancyId = com.tzx.framework.common.constant.Constant.systemMap.get("tenent_id");
            params.put("business_id", tenancyId);
        }
        if(com.tzx.framework.common.constant.Constant.systemMap.containsKey("store_id"))
        {
            Integer storeId = Integer.valueOf(com.tzx.framework.common.constant.Constant.systemMap.get("store_id"));
            params.put("shop_id", storeId);
        }
    }

}
