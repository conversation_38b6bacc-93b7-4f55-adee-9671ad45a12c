package com.tzx.base.service.servlet;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tzx.clientorder.wxorder.base.util.WxOrderUtil;
import com.tzx.clientorder.wxorder.bo.WxInsertOrderService;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.BeansException;

import com.tzx.base.bo.ProcessMessageService;
import com.tzx.base.bo.SpecialService;
import com.tzx.base.common.util.PosPropertyUtil;
import com.tzx.base.constant.Special;
import com.tzx.base.po.springjdbc.dao.DataSyncDao;
import com.tzx.clientorder.acewillwechat.bo.WshPosEntranceService;
import com.tzx.clientorder.wlifeprogram.bo.WlifeService;
import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.file.MqFileDownload;
import com.tzx.framework.common.util.HttpUtil;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.pos.base.util.JsonUtil;
import com.tzx.pos.bo.CloudBillService;
import com.tzx.pos.service.notify.BohStateObservable;

/**
 * 改造通讯机制 10:Push平台+FastDfs;11:Push平台+http回调请求
 * 
 * <AUTHOR> email:<EMAIL>
 * @version 1.0 2017-10-09
 * @see
 * @since JDK7.0
 * @update
 */
public class PushProcessMessage {

	private Logger logger = Logger.getLogger(PushProcessMessage.class);
	private Logger tipLogger = Logger.getLogger("tip");
	
	public static final String SPLIT_CHARACTER = "---"; 

	public PushProcessMessage() {
	}

	/**
	 * Push平台下发机制处理下发消息
	 * 
	 * @param data
	 * @return
	 * @throws Exception
	 */
	public String doExecute(String data) throws Exception {
		String returnRes = "";
		JSONObject getJson = null;
		JSONObject msgData = null;
		boolean isPushDFS = false;
		// String serverType = "3";// 业务类型：微信 、外卖
		String serverType = "";// 业务类型：微信 、外卖
		String type = null;
		try {
			getJson = JSONObject.fromObject(data);
			type = getJson.getString("t"); // t:下发方式
			/*
			 * 如果下发类型为特殊指令，则走特殊指令流程
			 */
			if(getJson.has("type") && Type.SPECIAL.name().equals(getJson.getString("type").toUpperCase())){
				final String tenancy_id = getJson.getString("tenancy_id");
				final String store_id = getJson.getString("store_id");					
				return specialHandle(getJson,tenancy_id,Integer.valueOf(store_id));
			}
			serverType = getJson.getString("b"); // 业务类型
			if (type != null && !"".equals(type)) {
				if ("10".equals(type)) {// 推送平台加fastdfs下发方式
					logger.info("升级改造下发版本:推送平台加fastdfs下发方式!");
					isPushDFS = true;
				} else if ("11".equals(type)) {// 推送平台加http回调方式
					logger.info("升级改造下发版本:推送平台加http回调方式!");
				}
			}
		} catch (Exception e) {
			logger.info("得到的消息是：" + data);
			returnRes = "下发转换出错，字符串转json对象出错";
			return returnRes;
		}

		String tenancy_id = null;
		Integer organId = null;
		Map<String, String> systemMap = Constant.getSystemMap();
		if (isPushDFS) {// 推送平台加fastdfs下发方式 通过文件下发内容
			int len = getJson.optInt("l");
			String url = getJson.optString("u");
			if (len <= 0) {
				logger.error("推送平台加fastdfs下发方式,文件长度错误!长度:" + len);
				return "推送平台加fastdfs下发方式,文件长度错误!长度:" + len;
			}
			if (url == null || "".equals(url)) {
				logger.error("推送平台加fastdfs下发方式，发送消息版本错误,未获取到url!");
				return "推送平台加fastdfs下发方式 ,发送消息版本错误,未获取到url!";
			}
			String msg = null;
			try {
				logger.info("推送平台加fastdfs下发方式，文件下载地址:" + url);
				msg = MqFileDownload.downloadMqStrByBlock(url, len, true);
			} catch (Exception e) {
				e.printStackTrace();
				logger.error("推送平台加fastdfs下发方式,下载文件失败，文件下载地址:" + url);
				return "推送平台加fastdfs下发方式,下载文件失败，文件下载地址:" + url;
			}
			if (msg != null && !"".equals(msg)) {
				logger.info("推送平台加fastdfs下发方式文件下载数据: " + msg);
				try {
					msgData = JSONObject.fromObject(msg);
				} catch (Exception e) {
					e.printStackTrace();
					logger.error("推送平台加fastdfs下发方式,文件解析json出错!");
					return "推送平台加fastdfs下发方式,文件解析json出错!";
				}

				if (msgData == null || msgData.isEmpty()) {
					logger.warn("推送平台加fastdfs下发方式,返回数据为空!");
					return "推送平台加fastdfs下发方式,返回数据为空!";
				} else {
					if (!msgData.containsKey("tenancy_id") || "".equals(msgData.optString("tenancy_id"))
							|| !msgData.containsKey("store_id") || "".equals(msgData.optString("store_id"))) {
						tenancy_id = systemMap.get("tenent_id");
						organId = Integer.parseInt(systemMap.get("store_id"));
					} else {
						tenancy_id = msgData.optString("tenancy_id");
						organId = msgData.optInt("store_id");
					}
				}
			} else {
				logger.error("推送平台加fastdfs下发方式,下载失败或者文件为空!" + url);
				return "推送平台加fastdfs下发方式,文件内容为空!";
			}
		} else {// 推送平台加http回调方式下发参数
			if (type.equals("100")) {
				tenancy_id = systemMap.get("tenent_id");
				String url = getJson.optString("u");
				String dparam = getJson.optString("d");// 回调参数
				List<JSONObject> listJson = (List<JSONObject>) getJson.get("k");
				String downTableCode = getJson.getString("j");
				if (listJson.size() < 0) {
					logger.error("推送平台加http回调方式,未获取到下发的表版本号");
					return "推送平台加http回调方式,未获取到下发的表版本号";
				}
				if (url == null || "".equals(url)) {
					logger.error("推送平台加http回调方式，发送消息版本错误,未获取到回调url!");
					return "推送平台加http回调方式,发送消息版本错误,未获取到回调url!";
				}
				if (dparam == null || "".equals(dparam)) {
					logger.error("推送平台加http回调方式，发送消息版本错误,回调参数!");
					return "推送平台加http回调方式，发送消息版本错误,回调参数!";
				} else {
					JSONObject paramJson = JSONObject.fromObject(dparam);
					if (paramJson.get("unique_code") == null || "".equals(paramJson.get("unique_code"))) {
						logger.warn("推送平台加http回调方式,唯一编号参数信息为空!");
						return "推送平台加http回调方式,唯一编号参数信息为空!";
					}

					// 封装回调参数
					msgData = new JSONObject();
					msgData.put("url", getJson.get("u"));
					msgData.put("unique_code", paramJson.optString("unique_code"));
					msgData.put("para_value", getJson.get("t"));
					msgData.put("tenancy_id", tenancy_id);
					msgData.put("listJson", listJson);
					msgData.put("downTableCode", downTableCode);
					organId = Integer.parseInt(systemMap.get("store_id"));
				}
			} else if (type.equals("11")) {
				tenancy_id = systemMap.get("tenent_id");
				String url = getJson.optString("u");
				String dparam = getJson.optString("d");// 回调参数

				if (url == null || "".equals(url)) {
					logger.error("推送平台加http回调方式，发送消息版本错误,未获取到回调url!");
					return "推送平台加http回调方式,发送消息版本错误,未获取到回调url!";
				}
				if (dparam == null || "".equals(dparam)) {
					logger.error("推送平台加http回调方式，发送消息版本错误,回调参数!");
					return "推送平台加http回调方式，发送消息版本错误,回调参数!";
				} else {
					JSONObject paramJson = JSONObject.fromObject(dparam);
					if (paramJson.get("unique_code") == null || "".equals(paramJson.get("unique_code"))) {
						logger.warn("推送平台加http回调方式,唯一编号参数信息为空!");
						return "推送平台加http回调方式,唯一编号参数信息为空!";
					} else if (paramJson.get("type") == null || "".equals(paramJson.get("type"))) {
						logger.warn("推送平台加http回调方式,类别参数信息为空!");
						return "推送平台加http回调方式,类别参数信息为空!";
					} else if (paramJson.get("oper") == null || "".equals(paramJson.get("oper"))) {
						logger.warn("推送平台加http回调方式,oper参数信息为空!");
						return "推送平台加http回调方式,oper参数信息为空!";
					}

					// 封装回调参数
					JSONObject param = new JSONObject();
					param.put("unique_code", paramJson.optString("unique_code"));
					List<JSONObject> list = new ArrayList<JSONObject>();
					list.add(param);
					paramJson.put("data", list);
					paramJson.put("tenancy_id", tenancy_id);
					dparam = paramJson.toString();
				}
				try {
					// 推送平台加http回调方式
					String msg = HttpUtil.sendPostRequest(url, dparam);
					// 网络重试
					if (msg == null || JSONObject.fromObject(msg).getInt("code") != 0) {
						int i = 0;
						try {
							while (i < 3) {
								logger.debug("推送平台加http回调方式。。。。。重试第" + (i + 1) + "次");
								msg = HttpUtil.sendPostRequest(url, dparam);
								if (msg == null || JSONObject.fromObject(msg).getInt("code") != 0) {
									Thread.sleep(3000);// 暂停3秒
									i = i + 1;
									logger.debug("开始重试第" + i + "次");
								} else {
									break;
								}
							}
						} catch (Exception e) {
							logger.debug("重试第" + i + "次出现异常" + e);
						}
					}
					if (msg != null) {
						logger.info("推送平台加http回调方式回调返回数据：" + msg);
						try {
							getJson = JSONObject.fromObject(msg);
							msgData = JSONObject.fromObject(getJson.getJSONArray("data").get(0));// 获取消息体
						} catch (Exception e) {
							e.printStackTrace();
							logger.info("推送平台加http回调方式获取数据失败");
							returnRes = "推送平台加http回调方式获取数据失败";
							return returnRes;
						}

						if (msgData == null || msgData.isEmpty()) {
							logger.warn("推送平台加http回调方式返回数据为空!");
							return "推送平台加http回调方式返回数据为空!";
						} else {
							if (!msgData.containsKey("tenancy_id") || "".equals(msgData.optString("tenancy_id"))
									|| !msgData.containsKey("store_id") || "".equals(msgData.optString("store_id"))) {
								tenancy_id = systemMap.get("tenent_id");
								organId = Integer.parseInt(systemMap.get("store_id"));
							} else {
								tenancy_id = msgData.optString("tenancy_id");
								organId = msgData.optInt("store_id");
							}
						}
						if ((getJson.getInt("code") != 0)) {
							logger.warn(getJson.getString("msg"));
							return getJson.getString("msg");
						}

					} else {
						logger.error("推送平台加http回调方式,下载失败或者文件为空!" + url);
						return "推送平台加http回调方式,文件内容为空!";
					}
				} catch (Exception e) {
					e.printStackTrace();
					logger.error("http回调出错!");
					return "http回调出错!";
				}

			}
		}

		// 处理消息体
		try {
			if (organId == null || StringUtils.isEmpty(tenancy_id)) {
				logger.error("商户号或者门店id错误!");
				return "商户号或者门店id错误!";
			}
			returnRes = handleMsg(tenancy_id, organId, msgData, serverType);
			logger.error(returnRes);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return returnRes;
	}

	/**
	 * 处理微信、外卖消息体
	 * 
	 * @param tenancy_id
	 *            商户号
	 * @param organId
	 *            门店id
	 * @param getJson
	 *            消息
	 * @param serviceType
	 *            业务类型
	 * @return
	 * @throws Exception
	 */
	private String handleMsg(final String tenancy_id, final Integer organId, JSONObject getJson, String serviceType)
			throws Exception {
		String returnRes = "";
		String data = getJson.toString();
		String type = getJson.optString("type");

		String  saasUrl = PosPropertyUtil.getMsg("saas.url");
		if("2".equals(serviceType)){
			// 基础资料下发加同步锁，防止并发
			synchronized (PushProcessMessage.class) {
				HashMap<String,String> recordCountMap = new HashMap<String,String>();
				String downTableCode = getJson.getString("downTableCode");
				ProcessMessageService processMessageService = (ProcessMessageService)SpringConext.getBean(ProcessMessageService.NAME);
//				ProcessMessageDao processMessageDao = (ProcessMessageDao)SpringConext.getBean(ProcessMessageDao.NAME);
//				PosDao posDao = (PosDao)SpringConext.getBean(PosDao.NAME);
//				CacheManagerService cacheManagerService = (CacheManagerService)SpringConext.getBean(CacheManagerService.NAME);
				WshPosEntranceService wshPosEntranceService = SpringConext.getApplicationContext().getBean(WshPosEntranceService.class);
				//下发
				try{
					logger.info("下发接收到消息");
					processMessageService.updateDataVersion(tenancy_id, organId, "1");     // 重启打印服务用
					long t1 = System.currentTimeMillis();					
		            HttpUtil.sendGetRequest(saasUrl+"/rest/hq/boh/dataSyncResultRest/?success=true&status=1&tenancyId="+tenancy_id+"&storeId="+organId+"&downTableCode="+downTableCode,"UTF-8");// 1 接收到下发的消息
		            tipLogger.info("基础资料下发：收到下发消息。");
		            Map<String, String> systemMap = Constant.getSystemMap();				
					StringBuffer sb = new StringBuffer();
//					String sbs = null;
					List<String> versionSqlList = new ArrayList<String>();
					String insertSql = "";
					String updateSql = "";
					List<JSONObject> listJson = (List<JSONObject>) getJson.get("listJson");
					SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
					String time = sf.format(new Date());                       // 获取时间
					String tenancy_Id = systemMap.get("tenent_id");
					int storeId = Integer.parseInt(systemMap.get("store_id"));
					for (JSONObject json : listJson) {
						String tableName = json.getString("tableName");        // 下发的
						String tableVersion = json.getString("tableVersion");  // 下发的
						String recordCount = json.optString("recordCount");    // 行数
						recordCountMap.put(tableName.toLowerCase().trim(),recordCount);
						List<JSONObject> list = processMessageService.findTableNameId(tableName);
						if (list != null && list.size() > 0) {
							for (JSONObject table : list) {
								String table_name = table.getString("table_name");        // 本地的
								String table_version = table.getString("table_version");  // 本地的
								if (table_name.equals(tableName)) {
									if (table_version.equals(tableVersion)) {
		                                  break;
									} else {								
										updateSql = processMessageService.updateTableNameAndVersion(tenancy_Id, storeId, tableName,
												tableVersion, time,recordCount);//如果下发和本地DB的表加密版本号不一致，则要修改本地版本号和记录行数
										versionSqlList.add(updateSql);
										sb.append(table_name).append(SPLIT_CHARACTER);
									}
								}
							}
						} else {
							//如果本地DB没有对应表的加密版本号，则要添加新的下发的表版本号
							sb.append(tableName).append(SPLIT_CHARACTER);
							insertSql = processMessageService.insertTableNameAndVersion(tenancy_Id, storeId, tableName, tableVersion, time,recordCount);
							versionSqlList.add(insertSql);
						}
					}
					if (sb.length() > 0) {
						logger.info("门店需要下发的表的版本号和总部的不一致，需要下发");
						try {
                            BohStateObservable.doNotify(Type.BOH_HQ_SYNCHRONIZING);
                            //sbs = sb.toString().substring(0, sb.length() - 1);
                            String tenancyId = getJson.getString("tenancy_id");
                            String url = getJson.getString("url");
                            String unique_code = getJson.getString("unique_code");
                            String para_value = getJson.getString("para_value");
                            String requestUrl = url + "?" + "unique_code=" + unique_code + "&" + "para_value=" + para_value + "&"
                                    + "tenancy_id=" + tenancyId + "&" + "downTableCode=" + downTableCode + "&" + "tableNameAndVersion=" + sb;
                            DownData downData = new DownData(requestUrl, downTableCode, sb.toString(), tenancy_id, storeId,recordCountMap);
                            boolean result = downData.downloadData();
                            if (result) {
                                // 修改down_table_version表数据
                                DataSyncDao dataSyncDao = (DataSyncDao) SpringConext.getBean(DataSyncDao.NAME);
                                boolean bo = dataSyncDao.execBatchSqls(versionSqlList);
                                if (bo == false) {
                                    logger.error("修改down_table_version表数据出现异常。");
                                }
                                HttpUtil.sendGetRequest(saasUrl + "/rest/hq/boh/dataSyncResultRest/?success=true&status=6&tenancyId=" + tenancyId + "&storeId=" + storeId + "&downTableCode=" + downTableCode,"UTF-8"); // 6 数据下发成功

                                /*
                                 * 数据下发成功后执行指定操作
                                 */
//                                processMessageService.setXMDCRMConfigToSysParameter(tenancyId, storeId);   // 将新美大CRM对接相关参数配置写入系统参数表
//                                processMessageDao.updateEmployeeForStoreId(tenancyId, storeId);
//                                processMessageDao.updateUserForStoreId(tenancyId, storeId);
//                                posDao.buildBasicVersion(tenancyId, storeId);              // 记录版本到pos_data_version表里，用于版本控制
//                                posDao.updatePosTableState(tenancyId, storeId);
//                                posDao.otherChanelAddMeallist(tenancyId, storeId);         // 其它渠道分配套餐明细
//                                posDao.synzPrinter(tenancyId, storeId);                    // 同步物理打印机（新打印）
//                                cacheManagerService.loadBusinessData(tenancyId, storeId);  // 更新缓存
                                processMessageService.updateBasicVersion(tenancyId, organId);
                                wshPosEntranceService.uploadBaseDataToWsh(tenancyId, organId, getJson);    // 上传基础数据给微生活

                                WlifeService wlifeProgramService = SpringConext.getApplicationContext().getBean(WlifeService.class);
                				wlifeProgramService.synchrodataDish(tenancyId, organId);
                				
                                HttpUtil.sendGetRequest(saasUrl + "/rest/hq/boh/dataSyncResultRest/?success=true&status=7&tenancyId=" + tenancyId + "&storeId=" + storeId + "&downTableCode=" + downTableCode, "UTF-8"); // 7 数据下发成功
                                tipLogger.info("基础资料下发：下发成功（最终结果）。");
                                returnRes = "数据下发成功";
                            } else {
                                HttpUtil.sendGetRequest(saasUrl + "/rest/hq/boh/dataSyncResultRest/?success=false&status=6&tenancyId=" + tenancyId + "&storeId=" + storeId + "&downTableCode=" + downTableCode, "UTF-8"); // 6 数据下发失败
                                tipLogger.info("基础资料下发：下发失败（最终结果）。");
                                returnRes = "数据下发失败";
                            }
                        } finally {
                            BohStateObservable.doNotify(Type.BOH_NORMAL_RUNNING);
                        }
                        //TheeadPools.getConsumerBaseDataThreadPool().execute(downThread);
					} else {
						HttpUtil.sendGetRequest(saasUrl+"/rest/hq/boh/dataSyncResultRest/?success=true&status=7&tenancyId="+tenancy_id+"&storeId="+organId+"&downTableCode="+downTableCode,"UTF-8");//门店需要下发的表的版本号和总部的一致，不需要下发，直接返回总部基础资料下发完成
						logger.info("门店需要下发的表的版本号和总部的一致，不需要下发");
						tipLogger.info("基础资料下发：下发成功（最终结果）。");
						returnRes = "数据下发成功";
					}
					// 打印重启需要5秒时间间隔
					long t2 = System.currentTimeMillis();
					if(t2 - t1 < 12000){	
						Thread.sleep(12000-(t2-t1));
					}
				}catch(Exception ex){
					logger.info("数据下发出现异常，异常信息："+ex.getMessage());
					HttpUtil.sendGetRequest(saasUrl+"/rest/hq/boh/dataSyncResultRest/?success=false&status=7&tenancyId="+tenancy_id+"&storeId="+organId+"&downTableCode="+downTableCode,"UTF-8"); // 7 数据下发成功
					tipLogger.info("基础资料下发：下发失败（最终结果）。");
				}finally {
					processMessageService.updateDataVersion(tenancy_id, organId, "0");    // 重启打印服务用 
				}            
			}			
		}else{
			// 微信、外卖
			if (Type.ORDER.name().equals(type.toUpperCase())) {
//				if (Oper.add.name().equals(getJson.getString("oper"))) {
//					logger.info("接收订单:" + data);
//					try {
//						DBContextHolder.setTenancyid(tenancy_id);
//						ProcessMessageService processMessageService = (ProcessMessageService) SpringConext
//								.getBean(ProcessMessageService.NAME);
//						processMessageService.addOrders(tenancy_id, organId, getJson);
//						returnRes = "接收订单成功";
//					} catch (Exception e) {
//						logger.error("==========接收订单失败!\n", e);
//						returnRes = "接收订单失败";
//					}
//				} else if (Oper.add_modify.name().equals(getJson.getString("oper"))) {
//					logger.info("重新接收订单:" + data);
//					try {
//						DBContextHolder.setTenancyid(tenancy_id);
//						ProcessMessageService processMessageService = (ProcessMessageService) SpringConext
//								.getBean(ProcessMessageService.NAME);
//						processMessageService.modifyOrder(tenancy_id, organId, getJson);
//						returnRes = "重新接收订单";
//					} catch (Exception e) {
//						logger.error("==========重新接收订单!\n", e);
//						returnRes = "重新接收订单";
//					}
//				} else if (Oper.updatedish.name().equals(getJson.getString("oper"))) {
//					logger.info("接收订单[加菜]:" + data);
//					try {
//						DBContextHolder.setTenancyid(tenancy_id);
//						ProcessMessageService processMessageService = (ProcessMessageService) SpringConext
//								.getBean(ProcessMessageService.NAME);
//						processMessageService.updateDish(tenancy_id, organId, getJson);
//						returnRes = "接收订单成功";
//					} catch (Exception e) {
//						logger.error("==========接收订单失败!\n", e);
//						returnRes = "接收订单失败";
//					}
//				} else if (Oper.cancle.name().equals(getJson.getString("oper"))) {
//					try {
//						logger.info("订单取消得到的消息是：" + data);
//						DBContextHolder.setTenancyid(tenancy_id);
//						ProcessMessageService processMessageService = (ProcessMessageService) SpringConext
//								.getBean(ProcessMessageService.NAME);
//						processMessageService.cancleOrders(tenancy_id, organId, getJson);
//						returnRes = "取消订单成功";
//					} catch (Exception e) {
//						logger.info("得到的消息是：" + data);
//						logger.error("取消订单失败", e);
//						e.printStackTrace();
//						returnRes = "取消订单失败";
//					}
//				} else if (Oper.cancle_modify.name().equals(getJson.getString("oper"))) {
//					try {
//						logger.info("订单重新取消得到的消息是：" + data);
//						DBContextHolder.setTenancyid(tenancy_id);
//						ProcessMessageService processMessageService = (ProcessMessageService) SpringConext
//								.getBean(ProcessMessageService.NAME);
//						processMessageService.cancleModifyOrders(tenancy_id, organId, getJson);
//						returnRes = "订单重新取消";
//					} catch (Exception e) {
//						logger.info("得到的消息是：" + data);
//						logger.error("订单重新取消失败", e);
//						e.printStackTrace();
//						returnRes = "订单重新取消失败";
//					}
//				}else if (Oper.opinion.name().equals(getJson.getString("oper"))) {
//					try {
//						DBContextHolder.setTenancyid(tenancy_id);
//						ProcessMessageService processMessageService = (ProcessMessageService) SpringConext
//								.getBean(ProcessMessageService.NAME);
//						processMessageService.opinionOrders(tenancy_id, organId, getJson);
//						returnRes = "投诉订单成功";
//					} catch (Exception e) {
//						logger.info("得到的消息是：" + data);
//						logger.error("投诉订单失败", e);
//						e.printStackTrace();
//						returnRes = "投诉订单失败";
//					}
//				} else if (Oper.complete.name().equals(getJson.getString("oper"))) {
//					try {
//						DBContextHolder.setTenancyid(tenancy_id);
//						ProcessMessageService processMessageService = (ProcessMessageService) SpringConext
//								.getBean(ProcessMessageService.NAME);
//						processMessageService.completeOrders(tenancy_id, organId, getJson);
//						returnRes = "订单完成";
//					} catch (Exception e) {
//						logger.info("得到的消息是：" + data);
//						logger.error("完成订单失败", e);
//						e.printStackTrace();
//						returnRes = "完成订单失败";
//					}
//				} else {
//					returnRes = "Oper无效";
//				}
				
				DBContextHolder.setTenancyid(tenancy_id);
				ProcessMessageService processMessageService = (ProcessMessageService) SpringConext
						.getBean(ProcessMessageService.NAME);
				returnRes = processMessageService.ordersManagement(tenancy_id, organId, getJson);
				
			} else if (Type.BILL_CLOUD.name().equals(type.toUpperCase())
					|| Type.VALIDATE_BILL_CLOUD.name().equals(type.toUpperCase())) {// 云账单查询
				DBContextHolder.setTenancyid(tenancy_id);
				CloudBillService coudBillService = (CloudBillService) SpringConext.getBean(CloudBillService.NAME);
				if (Oper.find.name().equals(getJson.getString("oper"))) {
					logger.info("云POS拉取门店账单:" + data);
					try {
						String cloudBillJson = "";
						if (Type.BILL_CLOUD.name().equals(type.toUpperCase())) {
							cloudBillJson = coudBillService.findCloudBill(data);
						} else if (Type.VALIDATE_BILL_CLOUD.name().equals(type.toUpperCase())) {
							cloudBillJson = coudBillService.validateCloudBill(data);
						}
						this.commonPost(PosPropertyUtil.getMsg("cloud.url") + "/posRest/post", cloudBillJson);
					} catch (Exception e) {
						logger.info("云POS拉取门店账单得到的消息是：" + data);
						logger.error("云POS拉取门店账单失败", e);
						e.printStackTrace();
						returnRes = "云POS拉取门店账单失败";
					}
				} else if (Oper.update.name().equals(getJson.getString("oper"))) {
					try {
						DBContextHolder.setTenancyid(tenancy_id);
						CloudBillService cloudBillService = (CloudBillService) SpringConext
								.getBean(CloudBillService.NAME);
						cloudBillService.syncCloudPaymentBill(tenancy_id, organId, getJson);

						returnRes = "云端数据同步到门店成功";
					} catch (Exception e) {
						logger.error("==========云端数据同步到门店失败", e);
						returnRes = "云端数据同步到门店失败";
						logger.info("云端数据同步到门店失败：" + data);
					}
				}
			}else if (Type.WX_ORDER.name().equals(type.toUpperCase())) {
			    logger.info("-------------------微信后付下单：" + getJson.toString());
                //微信后付下单
                DBContextHolder.setTenancyid(tenancy_id);
                WxInsertOrderService wxInsertOrderService = (WxInsertOrderService) SpringConext
                        .getBean(WxInsertOrderService.NAME);
                returnRes = wxInsertOrderService.ordersManagement(tenancy_id, organId, getJson);
			} else {
				logger.warn("消息体不能被处理,暂不支持业务类型:" + data);
				return null;
			}
		} 

		/*
		 * else { logger.info("新的下发方式暂不支持其他业务类型"); returnRes =
		 * "新的下发方式暂不支持其他业务类型"; }
		 */

		return returnRes;
	}
	
	/**
	 * 特殊指令下发处理过程
	 * 
	 * @param getJson 消息
	 * @param tenancy_id 商户号
	 * @param organId 门店id	 
	 * @return
	 * @throws Exception
	 */
	public String specialHandle(JSONObject getJson, String tenancy_id, int organId) throws Exception {
//		String returnStr = "";
//		if (getJson.containsKey("data")) {
//			JSONArray specialsJson = getJson.optJSONArray("data");
//			for (int i = 0; i < specialsJson.size(); i++) {
//				String special = specialsJson.optString(i);
//				if (Special.updateproductimgaction.name().equals(special.toLowerCase())) {
//					// 加载图片
//					new Thread() {
//						@Override
//						public void run() {
//							try {
//								ProcessMessageService processMessageService = (ProcessMessageService) SpringConext
//										.getBean(ProcessMessageService.NAME);
//								processMessageService.updateProductPhoto(tenancy_id, organId);
//							} catch (BeansException e) {
//								e.printStackTrace();
//							} catch (Exception e) {
//								e.printStackTrace();
//							}
//
//						}
//					}.start();
//				} else if (Special.updateversionaction.name().equals(special.toLowerCase())) {
//					String filePath = System.getProperty("catalina.base") + System.getProperty("file.separator")
//							+ "update.lock";
//					File file = new File(filePath);
//					if (file.exists()) {
//						file.delete();
//					}
//					// file.getParentFile().mkdirs();
//					file.createNewFile();
//				} else if (Special.boh_img_padpc.name().equals(special.toLowerCase())) {
//					new Thread() {
//						@Override
//						public void run() {
//							try {
//								DBContextHolder.setTenancyid(tenancy_id);
//								ProcessMessageService processMessageService = (ProcessMessageService) SpringConext
//										.getBean(ProcessMessageService.NAME);
//								processMessageService.downloadViceScreenPhoto(tenancy_id, organId);
//							} catch (BeansException e) {
//								e.printStackTrace();
//							} catch (Exception e) {
//								e.printStackTrace();
//							}
//
//						}
//					}.start();
//				} else {
//					logger.warn("指令:" + special + "无效!");
//					returnStr = "指令:" + special + "无效!";
//				}
//			}
//		}
//		logger.info("基础资料下发：指令执行成功!");
//		tipLogger.info("基础资料下发：指令执行成功!");
//		returnStr = "基础资料下发：指令执行成功!";
//		return returnStr;
		
		DBContextHolder.setTenancyid(tenancy_id);
		SpecialService messageService = (SpecialService) SpringConext.getBean(SpecialService.NAME);
		return messageService.specialManagement(tenancy_id, organId, getJson);
	}

	private Data commonPost(String url, String param) {
		Data data = new Data();
		try {
			long t = System.currentTimeMillis();
			JSONObject paramJson = JSONObject.fromObject(param);
			logger.debug(String.valueOf(t) + "<发送接口请求体==>type: " + paramJson.optString("type"));
			logger.debug(String.valueOf(t) + "<发送接口请求体==>oper: " + paramJson.optString("oper"));
			logger.debug(String.valueOf(t) + "<发送接口请求体==>" + param);
			String result = HttpUtil.sendPostRequest(url, param);
			logger.debug(String.valueOf(t) + "<发送接口返回体==>" + result);
			if (StringUtils.isEmpty(result)) {
				data.setCode(com.tzx.pos.base.Constant.CODE_CONN_EXCEPTION);
				data.setMsg(com.tzx.pos.base.Constant.CODE_CONN_EXCEPTION_MSG);
				data.setSuccess(false);
				logger.error("连接超时，请检查网络,请求体为：" + param);
			} else {
				data = JsonUtil.JsonToData(JSONObject.fromObject(result));
			}
			logger.debug(String.valueOf(t) + "<调用接口返回体==>code: " + data.getCode());
			logger.debug(String.valueOf(t) + "<调用接口返回体==>msg: " + data.getMsg());
			logger.debug(String.valueOf(t) + "<调用接口返回体==>" + result);
		} catch (Exception se) {
			logger.error("连接超时，请检查网络,请求体为：" + param);
			data.setCode(com.tzx.pos.base.Constant.CODE_CONN_EXCEPTION);
			data.setMsg(com.tzx.pos.base.Constant.CODE_CONN_EXCEPTION_MSG);
			data.setSuccess(false);
		}
		return data;
	}

}
