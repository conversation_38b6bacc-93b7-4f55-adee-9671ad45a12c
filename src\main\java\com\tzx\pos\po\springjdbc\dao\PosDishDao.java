package com.tzx.pos.po.springjdbc.dao;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Map;

import net.sf.json.JSONObject;

import com.tzx.base.bo.PosCodeService;
import com.tzx.base.entity.PosBillItem;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.pos.base.dao.BaseDao;
import com.tzx.pos.bo.dto.PosBill;
import com.tzx.pos.bo.dto.PosBillBooked;
import com.tzx.pos.bo.dto.PosBillServiceEntity;
import com.tzx.pos.bo.dto.PosLog;

/**
 * 
 * <AUTHOR> 2015年6月24日-下午7:21:07
 */
public interface PosDishDao extends BaseDao
{
	String	NAME	= "com.tzx.pos.po.springjdbc.dao.imp.PosDishDaoImp";

	public void insertPosBill(String tenancyId, int storeId, Date reportDate, int shiftId, String billNum, String batchNum, String serialNum, String orderNum, Timestamp opentableTime, String openPosNum, String openOpt, String waiterNum, String tableCode, int guest, int itemMenuId, String saleMode,
			String source, int serviceId, double serviceAmount, String copyBillNum, String remark, String billProperty, String paymentState, int discountModeId, double discountkAmount,double shopRealAmount,double platformChargeAmount,String settlementType,double discountRate,String fictitiousTable,String dinnerType) throws Exception;
	
	public void insertPosBillTake(String tenancyId, int storeId, Date reportDate, int shiftId, String billNum, String batchNum, String serialNum, String orderNum, Timestamp opentableTime, String openPosNum, String openOpt, String waiterNum, String tableCode, int guest, int itemMenuId, String saleMode,
			String source, int serviceId, double serviceAmount, String copyBillNum, String remark, String billProperty, String paymentState, int discountModeId, double discountAmount,double shopRealAmount,double platformChargeAmount,String settlementType,double discountRate,String fictitiousTable) throws Exception;

	// 开台
	public List<JSONObject> openTable(String tenantId, Integer orginId, String bill_num,String stable) throws SystemException;

	// 下单、点菜
	public Data orderDish(String tenancyid, String billno, Integer organId, String mode) throws SystemException;

	// 下单、点菜
	public Data newOrderDish(String tenancyid, String billno, Integer organId, String mode) throws SystemException;

	// 结账
	public List<JSONObject> posPayment(String billno, Integer organId, JSONObject object) throws SystemException;

	// 账单查询
	public List<PosBill> findPosBillItem(String tenantId, Integer organId, List<PosBill> list) throws SystemException;

	// 查看已下单菜品
	public List<JSONObject> findOderDish(String tenantId, Integer organId, String tableCode) throws Exception;

	// 账单折扣
	@Deprecated
	public Data billDiscount(String tenantId, Integer organId, Map<String, Object> map) throws SystemException;

//	/**
//	 * 操作账单会员库表插入数据
//	 * 
//	 * @param tenantId
//	 * @param storeId
//	 * @param report_date
//	 * @param billno
//	 * @param type
//	 * @param cardCode
//	 * @param mobil
//	 */
//	public void operPosBillMember(String tenancyId, int storeId, Date reportDate, String billNum, String type, String cardCode, String mobil,String customerCode);
//	
//	/**
//	 * @param tenancyId
//	 * @param storeId
//	 * @param billNum
//	 * @param reportDate
//	 * @param type
//	 * @param amount
//	 * @param credit
//	 * @param cardCode
//	 * @param mobil
//	 * @param lastUpdatetime
//	 * @param remark
//	 * @param billCode
//	 * @param requestState
//	 * @param customerCode
//	 * @param customerName
//	 * @param consumeBeforeCredit
//	 * @param consumeAfterCredit
//	 * @throws Exception
//	 */
//	public void insertPosBillMember(String tenancyId,Integer storeId,String billNum,Date reportDate,String type,double amount,double credit,String cardCode,String mobil,Timestamp lastUpdatetime,String remark,String billCode,String requestState,String customerCode,String customerName,double consumeBeforeCredit,double consumeAfterCredit) throws Exception;
//	
//	public void updatePosBillMemberForCustomer(String tenancyId, int storeId,String billNum) throws Exception;
//
//	/** 修改会员操作金额
//	 * @param tenantId
//	 * @param storeId
//	 * @param reportDate
//	 * @param billno
//	 * @param type
//	 * @param cardCode
//	 * @param mobil
//	 */
//	public void updatePosBillMemberForAmount(String tenancyId, int storeId, String billNum, String type, double amount,double credit,String billCode,String requestState)throws Exception;
//
//	/**
//	 * @param tenancyId
//	 * @param storeId
//	 * @param billNum
//	 * @param reportDate
//	 * @param type
//	 * @throws Exception
//	 */
//	public void deletePosBillMember(String tenancyId, int storeId,String billNum,Date reportDate,String type) throws Exception;
//	
	/**
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @param paymentState
	 * @return
	 * @throws Exception
	 */
	public int getPaymentCountByState(String tenantId, int storeId, String billNum, String paymentState) throws Exception;
	
	/** 可优惠金额查询
	 * @param tenancyid
	 * @param billno
	 * @param organId
	 * @param reportDate
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getSurplusDiscountAmount(String tenancyid, String billno, 
			Integer organId,  Date reportDate) throws Exception;

	// 沽清
	@Deprecated
	public Data setSoldOut(String tenantId, Integer organId, String mode, Timestamp reportDate, List<Map<String, Object>> list, PosLog log) throws SystemException;

	// 急推菜品
	@Deprecated
	public Data setWorryItem(String tenantId, Integer organId, Map<String, Object> map) throws SystemException;

	// 修改服务费
	@Deprecated
	public Data modifyServiceFee(String tenantId, Integer organId, Integer serviceAmount) throws SystemException;

	// 修改人数
	@Deprecated
	public Data modifyGuest(String tenantId, Integer organId, String billno) throws SystemException;

	// 取消整单备注
	@Deprecated
	public void cancBillTaste(String tenantId, Integer organId, Map<String, String> map) throws SystemException;

	// 客人留言
	@Deprecated
	public Data guestMsg(String tenantId, Integer organId, Map<String, String> map) throws SystemException;

	// 修改服务员
	@Deprecated
	public Data changeWaiter(String tenantId, Integer organId, Map<String, String> map) throws SystemException;

	// 单品备注/整单备注/单品做法
	@Deprecated
	public Data changeRemarkAndZfkw(String tenantId, Integer organId, List<Map<String, Object>> listMaps) throws SystemException;

	// 修改规格
	@Deprecated
	public Data changeItemUnit(String tenantId, Integer organId, List<Map<String, String>> listMaps) throws SystemException;

	// 奉送
	@Deprecated
	public Data giveItem(String tenantId, Integer organId, List<Map<String, Object>> listMaps) throws SystemException;

	// 退菜
	@Deprecated
	public Data retreatFood(String tenantId, Integer organId, List<Map<String, Object>> listMaps) throws SystemException;

	// 修改菜品数量
	@Deprecated
	public Data changeDishCount(String tenantId, Integer organId, List<Map<String, Object>> listMaps) throws SystemException;

	// 修改菜品名称
	@Deprecated
	public Data changeDishName(String tenantId, Integer organId, List<Map<String, Object>> listMaps) throws SystemException;

	// 催菜
	@Deprecated
	public Data pushFood(String tenantId, Integer organId, Map<String, Object> map) throws SystemException;

	// 等叫
	@Deprecated
	public Data waitCall(String tenantId, Integer organId, Map<String, Object> map) throws SystemException;

	// 起菜
	@Deprecated
	public Data callupFood(String tenantId, Integer organId, Map<String, Object> map) throws SystemException;

	// 整单取消
	@Deprecated
	public Data cancelBill(String tenantId, Integer organId, Map<String, Object> map, PosCodeService codeService) throws SystemException;

	// 账单恢复
	@Deprecated
	public Data recoveryBill(String tenantId, Integer organId, Map<String, Object> map) throws SystemException;

	// 清除付款
	@Deprecated
	public Data clearPayment(String tenantId, Integer organId, Map<String, Object> map) throws SystemException;

	// 更改沽清数量
	@Deprecated
	public Data modSoldOuntCount(String tenantId, Integer organId, Map<String, Object> map) throws SystemException;

	// 开发票
	@Deprecated
	public void setInvoice(String sql, Object[] objs) throws SystemException;

	// 查询发票
	@Deprecated
	public List<Map<String, Object>> findInvoice(String sql, Object[] objs) throws Exception;

	// 查询账单表中最新的批次号
	@Deprecated
	public String getBatchNum(String tenantId, int storeId, String billNum) throws Exception;

	// 查询已使用的优惠券号
	@Deprecated
	public List<JSONObject> getCouponCode(String tenantId, int storeId, String billNum, String batchNum) throws Exception;

	// 查询使用优惠券的账单详细
	@Deprecated
	public List<JSONObject> getBillDetails4Coupon(String tenantId, int storeId, String billNum, String batchNum) throws Exception;

	// 获取营业统计信息
	public List<JSONObject> totalBillInfo(String tenancyId, int storeId, Date reportDate) throws Exception;

	/** 查询账单已付金额
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @return
	 * @throws Exception
	 */
	public double getBillPaymentForSumAmount(String tenancyId, int storeId, String billNum) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getBillPaymentForNotChange(String tenancyId, int storeId, String billNum) throws Exception;
	
	/** 退款复制账单优惠劵记录
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param paymentId
	 * @param newBillNum
	 * @param newPaymentId
	 * @throws Exception
	 */
	public void copyPaymentCouponsByPaymentId(String tenancyId, int storeId, String billNum,String paymentId,String newBillNum,String newPaymentId,Timestamp currentTime) throws Exception;
	
	/** 查询rwid（不包含等叫菜品）
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param itemTime
	 * @throws Exception
	 */
	public List<JSONObject> getRwidsWithoutWaitCallItem(String tenancyId, int storeId, String billNum, String orderRemark, String posNum) throws Exception;
	
	/**
	 * 通过门户ID获取门店业态
	 * @param store_id
	 * @return
	 */
	public String getYtByOrganId(String tenantId,int store_id) throws Exception;
	/**
	 * 通过订单号获取订单明细
	 * @param tenantId
	 * @param store_id
	 * @param oldBillNum
	 * @return
	 */
	public List<JSONObject> getPosBillItemByBillNum(String tenantId,int store_id,String oldBillNum) throws Exception;

	/** 
	 * 通过订单号查询菜品做饭
	 * @param tenantId
	 * @param store_id
	 * @param oldBillNum
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getzfkwItemsByBillNum(String tenantId,int store_id, String oldBillNum)throws Exception;
	/**
	 * 通过订单号查询账单会员表
	 * @param tenantId
	 * @param store_id
	 * @param oldBillNum
	 * @param type
	 * @return
	 * @throws Exception
	 */
	@Deprecated
	public List<JSONObject> getposBillMemberByBillNum(String tenantId,int store_id, String oldBillNum, String type)throws Exception;
	/**
	 * 修改桌位状态
	 * @param tenantId
	 * @param store_id
	 * @param tableCode
	 * @param tableStateBusy
	 */
	public int updateTableState(String tenantId, int store_id,String tableCode, String tableStateBusy)throws Exception;
	
	/**
	 * 获取账单明细项目号
	 * @param tenantId
	 * @param itemIdNew
	 * @return
	 */
	public int getRwidById(String tenantId, int itemIdNew)throws Exception;
	/**
	 * 获取账单信息
	 * @param tenantId
	 * @param store_id
	 * @param oldBillNum
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> findBillByOldBillNum(String tenantId, int store_id,
			String oldBillNum)throws Exception;
	/**
	 * 复制服务费
	 * @param tenantId
	 * @param storeId
	 * @param oldBillNum
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> findBillServiceByOldBillNum(String tenantId,
			Integer storeId, String oldBillNum)throws Exception;
	
	/**
	 *  根据桌位编号查询桌位信息
	 * @param tenancyId
	 * @param storeId
	 * @param tableCode
	 * @return
	 * @throws Exception
	 */
	public JSONObject getPosTableInfoByTablecode(String tenancyId, int storeId,String tableCode) throws Exception;
	
	/**根据桌位编号查询未结账单
	 * @param tenancyId
	 * @param storeId
	 * @param tableCode
	 * @return
	 * @throws Exception
	 */
	public JSONObject getOpenBillByTablecode(String tenancyId, int storeId,String tableCode) throws Exception;
	
	/**
	 * 根据服务费ID查询服务费设置
	 * @param tenancyId
	 * @param storeId
	 * @param serviceId
	 * @return
	 * @throws Exception
	 */
	public JSONObject getServiceFeeByServiceId(String tenancyId, int storeId,int serviceId) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param billService
	 * @throws Exception
	 */
	public void insertPosBillService(String tenancyId,int storeId,PosBillServiceEntity billService)throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param fromBillNum
	 * @param toBillNum
	 * @param fromTableCode
	 * @param toTableCode
	 * @param serviceType
	 * @throws Exception
	 */
	public void copyPosBillServiceByBillNum(String tenancyId,int storeId,String fromBillNum,String toBillNum,String fromTableCode,String toTableCode,String serviceType)throws Exception;

	/** 
	 * @param tenancyId
	 * @param storeId
	 * @param item
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getHqItemInfoByItem(String tenancyId, int storeId,String chanel,List<?> item) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param chanel
	 * @param item
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getHqItemComboDetailsByItem(String tenancyId, int storeId,String chanel,List<Integer> item) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getHqItemMethodByID(String tenancyId, int storeId,List<Integer> methodIds) throws Exception;
	
	/** 
	 * @param tenancyId
	 * @param storeId
	 * @param item
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getHqItemUnitByItem(String tenancyId, int storeId,List<?> item) throws Exception;
	
	/** 
	 * @param tenancyId
	 * @param storeId
	 * @param item
	 * @return
	 * @throws Exception
	 */
	public Map<String,Object> getPosSoldOutByItem(String tenancyId, int storeId,Date reportDate,List<?> item) throws Exception;
	
	// 预点账单查询
	public List<PosBillBooked> findPosBillBookedItem(String tenantId, Integer organId, List<PosBillBooked> list) throws SystemException;
	
	/**
	 * 根据id查询订单
	 * @param tenancyId
	 * @param storeId
	 * @param billBookedNum
	 * @return
	 * @throws Exception
	 */
	public String getBillBookedId(String tenancyId, Integer storeId,String billBookedNum,String str) throws Exception;
	
	/**
	 * 修改预点状态
	 * @param tenancyId
	 * @param storeId
	 * @param billBookedNum
	 */
	public void updateBillBooked(String tenancyId, Integer storeId,String billBookedNum) throws Exception;
	
	/** 获取账单明细根据RWID
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param rwid
	 * @throws Exception
	 */
	public List<JSONObject> getPosBillItemByRwid(String tenancyId, Integer storeId,String billNum,Integer rwid) throws Exception;

	/** 记录桌位操作日志
	 * @param tenancyId
	 * @param storeId
	 * @param reportDate
	 * @param jsonObject
	 */
	public void savePosTableLog(String tenancyId, int storeId, Date reportDate,JSONObject jsonObject) throws Exception;

	/**
	  * @Description: 判断是否设置自动增加菜品区域 并且 该桌台是否在设置范围
	  * @Title:getHqItemTables
	  * @param:@param tenantId
	  * @param:@param storeId
	  * @param:@param tableCode
	  * @param:@return
	  * @param:@throws Exception
	  * @return: boolean
	  * @author: shenzhanyu
	  * @version: 1.0
	  * @Date: 2018年4月25日
	 */
	public boolean getHqItemTables(String tenantId, Integer storeId, String tableCode) throws Exception;
	
	/**
	 * @param tenancyid
	 * @param itemList
	 * @return
	 * @throws Exception
	 */
	public int batchUpdateBillItem(String tenancyid, List<PosBillItem> itemList) throws Exception;
	
	/**
	 * 查询最大的item_serial值
	 * @param billNum
	 * @param storeId
	 * @param tenancyId
	 * @return
	 * @throws Exception
	 */
	public int selectMaxItemSerial(String billNum, int storeId, String tenancyId) throws Exception;


	/**
	 * 是否存在并台之后操作的菜品（加菜、退菜、优惠、支付、奉送、取消奉送..）
	 * @param tenancyId
	 * @param storeId
	 * @param bill_num
	 * @return
	 * @throws Exception
	 */
	public boolean isExistsCombinedAfterBillItem(String tenancyId, int storeId, String bill_num) throws Exception;


	/**
	 * 验证是否并台
	 * @param tenancyId
	 * @param storeId
	 * @param bill_num
	 * @return
	 * @throws Exception
	 */
	public void checkCombineTable(String tenancyId, int storeId, String bill_num) throws Exception;



	/** 查询套餐明细
	 * @param tenantId
	 * @param itemIdList
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getItemComboDetails(String tenantId, Integer itemId) throws Exception;


	/**
	 * 校验账单号是否并台 账单号是否并台
	 * @param tenancyId
	 * @param storeId
	 * @param bill_num
	 * @return
	 * @throws Exception
	 */
	public JSONObject isCombineTableOfBillNum(String tenancyId, int storeId, String bill_num) throws Exception;

	/**
	 * 根据套餐id 获取套餐明细信息
	 * @param tenancyId
	 * @param storeId
	 * @param chanel
	 * @param item
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> getItemComboDetails(String tenancyId, int storeId, String chanel, List<?> item) throws Exception;

	/**
	 * 查询菜品档案基本信息
	 *
	 * @param tenancyId
	 * @param itemCode
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> getHqItemInfoByItemCode(String tenancyId,String itemCode) throws Exception;

}