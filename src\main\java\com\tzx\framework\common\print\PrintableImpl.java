package com.tzx.framework.common.print;

import java.awt.BasicStroke;
import java.awt.Color;
import java.awt.Font;
import java.awt.FontMetrics;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.Image;
import java.awt.Toolkit;
import java.awt.print.PageFormat;
import java.awt.print.Printable;
import java.awt.print.PrinterException;
import java.net.URL;
import java.util.List;

class PrintableImpl implements Printable
{

	List<PrintLine>	printLineList;
	Graphics2D		g2d;
	int				width;

	public PrintableImpl(List<PrintLine> printLineList)
	{
		this.printLineList = printLineList;
	}

	public int print(Graphics graphics2d, PageFormat pageformat, int page) throws PrinterException
	{
		// 当打印页号大于需要打印的总页数时，打印工作结束
		if (page != 0)
		{
			return Printable.NO_SUCH_PAGE;
		}

		g2d = (Graphics2D) graphics2d;
		g2d.setPaint(Color.black);
		g2d.translate(pageformat.getImageableX(), pageformat.getImageableY());// 转换坐标，确定打印边界
		width = g2d.getClipBounds().width;
		FontMetrics fontMetrics = g2d.getFontMetrics();
		fontMetrics.getHeight();
		float currentHeigth = 0;

		for (PrintLine printline : printLineList)
		{
			currentHeigth += printline.getMarginTop();
			
			float imgHeight = 0;
			for (PrintMete printmete : printline.getPrintMeteList())
			{
				if (PrintMete.CONTENT_STYLE_IMAGE == printmete.getContent_style())
				{
					if (imgHeight < printmete.getHeight())
					{
						imgHeight = printmete.getHeight();
					}
				}
			}

			if (imgHeight > 0)
			{
				currentHeigth += imgHeight;
			}
			else
			{
				Font font = printline.getFont();
				if (font != null)
				{
					currentHeigth += FontHeightUtil.getfontHeights(font.getSize2D());
				}
			}

			List<PrintMete> meteList = printline.getPrintMeteList();
			drawString(meteList, currentHeigth);

			currentHeigth += printline.getMarginBottom();
		}

		return Printable.PAGE_EXISTS; // 存在打印页时，继续打印工作
	}

	private void drawString(List<PrintMete> meteList, float currentHeigth) throws PrinterException
	{
		for (PrintMete mete : meteList)
		{
			try
			{
				if (mete.getContent_style() == PrintMete.content_style_STR)
				{
					drawString_STR(mete, currentHeigth);
				}
				else if (mete.getContent_style() == PrintMete.content_style_STR_CENTER)
				{
					drawString_STR_CENTER(mete, currentHeigth);
				}
				else if (mete.getContent_style() == PrintMete.content_style_LINE)
				{
					drawLine(mete, currentHeigth);
				}
				else if (mete.getContent_style() == PrintMete.content_style_LINE_With_String)
				{
					drawLineSTR(mete, currentHeigth);
				}
				else if (mete.getContent_style() == PrintMete.CONTENT_STYLE_IMAGE)
				{
					drawImage(mete, currentHeigth);
				}
			}
			catch (Exception e)
			{
				// TODO Auto-generated catch block
				throw new PrinterException(e.getMessage());
			}
		}
	}

	// 文字
	private void drawString_STR(PrintMete mete, float currentHeigth) throws Exception
	{
		Font font = mete.getFont();
		g2d.setFont(font);
		float var = mete.getPoint() * width;
		/*
		 * if(var==0){ var = 15; }
		 */
		g2d.drawString(mete.getContent(), var, currentHeigth);
	}

	// 居中文字
	private void drawString_STR_CENTER(PrintMete mete, float currentHeigth) throws Exception
	{
		Font font = mete.getFont();
		g2d.setFont(font);
		FontMetrics fontMetrics = g2d.getFontMetrics();
		g2d.drawString(mete.getContent(), ((width / 2) - (fontMetrics.stringWidth(mete.getContent()) / 2)), currentHeigth);
	}

	// 格式线
	private void drawLine(PrintMete mete, float currentHeigth) throws Exception
	{
		float[] dash1 =
		{ 2.0f };
		g2d.setStroke(new BasicStroke(0.5f, BasicStroke.CAP_BUTT, BasicStroke.JOIN_MITER, 50.0f, dash1, 2.5f));
		g2d.drawLine(0, (int) currentHeigth, (int) width, (int) currentHeigth);
	}

	// 文字格式线
	private void drawLineSTR(PrintMete mete, float currentHeigth) throws Exception
	{
		FontMetrics fontMetrics = g2d.getFontMetrics();
		int str_long = fontMetrics.stringWidth(mete.getContent());
		int var_point = ((width / 2) - (fontMetrics.stringWidth(mete.getContent()) / 2));

		float[] dash1 =
		{ 2.0f };
		g2d.setStroke(new BasicStroke(0.5f, BasicStroke.CAP_BUTT, BasicStroke.JOIN_MITER, 50.0f, dash1, 2.5f));
		g2d.drawLine(0, (int) currentHeigth, var_point, (int) currentHeigth);

		drawString_STR_CENTER(mete, currentHeigth);
		g2d.drawLine(str_long + var_point, (int) currentHeigth, (int) width, (int) currentHeigth);
	}
	
	/** 打印图片
	 * @param mete
	 * @param currentHeigth
	 * @throws Exception
	 */
	private void drawImage(PrintMete mete, float currentHeigth) throws Exception{
		Image img = Toolkit.getDefaultToolkit().getImage(new URL(mete.getContent()));
		g2d.drawImage(img , (int)(width-mete.getWidth())/2 , Float.valueOf(currentHeigth-mete.getHeight()).intValue(),mete.getWidth().intValue(),mete.getHeight().intValue(), null);
	}

}