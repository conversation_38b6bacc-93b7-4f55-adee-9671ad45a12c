package com.tzx.pos.base.util;

import net.sf.json.JSONObject;

/**
 * <AUTHOR> on 2018-10-12
 */
public class Progress {

    public final static String DATATRANS="datatrans";

    public final static int READY=0;
    public final static int BUSY=1;
    public final static int DONE=100;
    public final static int EXCEPTION=-1;

    private volatile int completed;
    private volatile int state;
    private String msg;


    public boolean isDone(){
       if(state==DONE||state==EXCEPTION) {
           return true;
       }
       return false;
    }


    public int getCompleted() {
        return completed;
    }

    public void setCompleted(int completed) {
        this.completed = completed;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    @Override
    public String toString() {
        return "{" +
                "completed=" + completed +
                ", state=" + state +
                ", msg='" + msg + '\'' +
                '}';
    }
}
