/** 
 * @(#)CodeFormat.java    1.0   2017-06-21
 * 
 * Copyright 北京普照天星科技有限公司. All rights reserved. 
 * PROPRIETARY/CONFIDENTIAL. Use is subject to license terms. 
 */
package com.tzx.pos.asyn.service.impl;

import com.tzx.base.cache.CacheManager;
import com.tzx.base.constant.CacheTableConstant;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.Tools;
import com.tzx.pos.asyn.service.PosPrintNewThreadService;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.CacheTableUtil;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.base.util.SpringContextUtils;
import com.tzx.pos.bo.PosPrintModeService;
import com.tzx.pos.bo.imp.print.*;
import com.tzx.pos.po.springjdbc.dao.PosDao;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

/**
 * 分单、送打功能接口实现类。（异步线程调用）
 *
 * <AUTHOR> email:<EMAIL>
 * @version 1.0  2017-06-19
 * @see     
 * @since   JDK7.0
 * @update  
 */
@Service(PosPrintNewThreadService.NAME)
public class PosPrintNewThreadServiceImpl implements PosPrintNewThreadService {
	
	private static final Logger	logger	= Logger.getLogger(PosPrintNewThreadServiceImpl.class);

	/**
	 * @Description 根据操作找打印模板
	 * @param tenancyId 商户号
	 *        storeId 门店号
	 *        functionId 操作编号
	 *        para 参数
	 * @return 
	 * @exception Exception 系统异常
	 * <AUTHOR> email:<EMAIL>
	 * @version 1.0  2017-06-21
	 * @see
	 */
	@Override
	public void posPrintByFunction(String tenancyId, int storeId, String functionId, JSONObject para) throws Exception {
		// 获取Spring容器中的bean对象。
		PosDao posDao = (PosDao)SpringContextUtils.getBean("com.tzx.pos.po.springjdbc.dao.imp.PosDaoImp");
		
		para.put("function_id", functionId);		
		logger.info(" ============>根据操作找打印模板处理开始：传入参数为->tenancyId:" + tenancyId + ",storeId:" + storeId + "," + para.toString());
		List<String> printModeList = posDao.getSysPrintModeList(tenancyId, storeId, functionId,null);// 这应该返回一List
		logger.debug(" 操作【" + functionId + "】对应的打印模板：" + printModeList.toString());
		if(printModeList.size() > 0)
		{
			for(String printMode : printModeList)
			{
				posPrintByMode(tenancyId, storeId, printMode, para);
			}
		}
		else
		{
			logger.info(" 分单失败：未找到打印模板");
		}
		logger.info(" ============>根据操作找打印模板处理结束");
	}

	/**
	 * @Description 根据打印模板调用打印服务
	 * @param tenancyId 商户号
	 *        storeId 门店号
	 *        printMode 打印模式
	 *        para 参数
	 * @return 
	 * @exception Exception 系统异常
	 * <AUTHOR> email:<EMAIL>
	 * @version 1.0  2017-06-21
	 * @see
	 */
	@Override
	public void posPrintByMode(String tenancyId, int storeId, String printMode, JSONObject para) throws Exception {
		
		// 获取Spring容器中的bean对象。
		PosDao posDao = (PosDao)SpringContextUtils.getBean("com.tzx.pos.po.springjdbc.dao.imp.PosDaoImp");

		if(!para.containsKey("tenancy_id") || "".endsWith(para.optString("tenancy_id")))
		{
			para.put("tenancy_id", tenancyId);
		}
		if(!para.containsKey("store_id") || 0 == para.optInt("store_id"))
		{
			para.put("store_id", storeId);
		}
		
		// 补打结账单(不是开发票的场合) or 补打随餐单 打印电子发票二维码
		if ((SysDictionary.PRINT_CODE_1110.equals(printMode) && !"invoice".equals(para.optString("invoice_repeat"))) 
				|| (SysDictionary.PRINT_CODE_1201.equals(printMode) && "1".equals(para.optString("is_repeat_1201"))))
		{
			JSONObject invJo = posDao.getInvoiceInfo(tenancyId, storeId, para);
			// 打印过电子发票才可以补打带电子发票的结账单
			if (!Tools.isNullOrEmpty(invJo) && "2".equals(invJo.optString("invoice_type")))
			{
				Timestamp last_updatetime = DateUtil.parseTimestamp(invJo.optString("last_updatetime"));
				String billNum = invJo.optString("bill_num");
				
				JSONObject billJo = posDao.getPosBillByBillnum(tenancyId, storeId, billNum);
				String orderNum = billJo.optString("order_num");
				int recoverCnt = billJo.optInt("recover_count");
				String fileName = "";
				if(Tools.isNullOrEmpty(orderNum))
				{
					fileName = billNum + "@" + recoverCnt;
				}
				else
				{
					fileName = orderNum + "@" + recoverCnt;
				}
				fileName += DateUtil.getYYYYMMDDHHMMSS(last_updatetime) + ".png";
				String webPathConst = com.tzx.framework.common.constant.Constant.getSystemMap().get("WEB_PATH");
				String strUrlPath = webPathConst + "img/qr_code/invoice/" + fileName;
				
				para.put("url_path", strUrlPath);
				para.put("is_invoice", "1");
			}
		}
		logger.info(" =========>根据打印模板分单处理开始：打印参数为->tenancyId:" + tenancyId + ",storeId:" + storeId + ",printMode：" + printMode + "," + para.toString());
		
		PosPrintModeService	posPrintModeService = null;
		try
		{
			String isLocalPrinter = posDao.getSysParameter(tenancyId, storeId, "IsLocalPrinter");

            Map<?, ?> sysParameterMap = CacheManager.getCacheMap(CacheTableConstant.SYS_PARAMETER.name());
            // 新打印分单模式
            String isNewPrintLogic = CacheTableUtil.getSysParameter("isNewPrintLogic", sysParameterMap);
            // 厨打是否启用序号打印
            String isPrintSerialNumber = CacheTableUtil.getSysParameter("isPrintSerialNumber", sysParameterMap);
            para.put("isPrintSerialNumber", isPrintSerialNumber);

            if("1".equals(isNewPrintLogic)){
                switch(printMode)
                {
                    case SysDictionary.PRINT_CODE_1106://单切单
                    case SysDictionary.PRINT_CODE_1104://标签切单
                        posPrintModeService = SpringConext.getApplicationContext().getBean(SingleModePrintServiceImp.class);
                        break;
                    case SysDictionary.PRINT_CODE_1113: // 一份一单
                        posPrintModeService = SpringConext.getApplicationContext().getBean(ACopyOfModePrintServiceImp.class);
                        break;
                    case SysDictionary.PRINT_CODE_1107://整台单
                        posPrintModeService = SpringConext.getApplicationContext().getBean(WholeModePrintServiceImp.class);
                        break;
                    case SysDictionary.PRINT_CODE_1105://大类
                        posPrintModeService = SpringConext.getApplicationContext().getBean(ClassModePrintServiceImp.class);
                        break;
                    case SysDictionary.PRINT_CODE_1109://退菜单
                        posPrintModeService = SpringConext.getApplicationContext().getBean(RetreatModePrintServiceImp.class);
                        break;
                    case SysDictionary.PRINT_CODE_1112://单品转台单
                        posPrintModeService = SpringConext.getApplicationContext().getBean(SingleChangeTableModePrintServiceImp.class);
                        break;
                    case SysDictionary.PRINT_CODE_1108://转台
                        posPrintModeService = SpringConext.getApplicationContext().getBean(WholeChangeTableModePrintServiceImp.class);
                        break;
                    case SysDictionary.PRINT_CODE_1103://POS点菜单
                    case SysDictionary.PRINT_CODE_1101://POS预打单
                    case SysDictionary.PRINT_CODE_1102://POS结账单
                    case SysDictionary.PRINT_CODE_1110://补打结账单
                        posPrintModeService = SpringConext.getApplicationContext().getBean(NewServiceModePrintServiceImp.class);
                        break;
                    case SysDictionary.PRINT_CODE_1301://POS交班单
                    case SysDictionary.PRINT_CODE_1302://补打交班单
                        String printType = para.optString("print_type");
                        String posNum = para.optString("pos_num");
                        String optNum = para.optString("opt_num");
                        String dateRemark = para.optString("date_remark");

                        if(!"1".equals(isLocalPrinter))
                        {
                            if ("1".equalsIgnoreCase(printType))
                            {
                                para.put("opt_pos_num", posNum);
                            }
                            else if ("2".equalsIgnoreCase(printType))
                            {
                                para.put("opt_pos_num", "");
                            }
                            if (StringUtils.isNotEmpty(dateRemark) && dateRemark.contains("~"))
                            {
                                String[] arr = dateRemark.split("~");
                                if (arr.length > 0)
                                {
                                    para.put("start_date", arr[0]);
                                    para.put("end_date", arr[1]);
                                }
                            }
                            para.put("cashier_num", optNum);
                            posPrintModeService = SpringConext.getApplicationContext().getBean(MemberModeServiceImp.class);
                        }
                        break;
                    case SysDictionary.PRINT_CODE_1013://会员卡发卡
                    case SysDictionary.PRINT_CODE_1008://会员卡充值
                    case SysDictionary.PRINT_CODE_1009://会员卡反充值
                    case SysDictionary.PRINT_CODE_1010://会员卡消费
                    case SysDictionary.PRINT_CODE_1011://会员卡反消费
                    case SysDictionary.PRINT_CODE_1012://会员卡退卡
                    case SysDictionary.PRINT_CODE_1015://会员卡补卡
                    case SysDictionary.PRINT_CODE_1201://外卖随餐单
                    case SysDictionary.PRINT_CODE_1202://外卖取消订单
                    case SysDictionary.PRINT_CODE_1203://外卖随餐单（异常单据）
                    case SysDictionary.PRINT_CODE_1204://外卖取消订单（异常单据）
                    case SysDictionary.PRINT_CODE_1303://交款凭证
                    case SysDictionary.PRINT_CODE_1304://零找金
                        if(!"1".equals(isLocalPrinter))
                        {
                            posPrintModeService = SpringConext.getApplicationContext().getBean(MemberModeServiceImp.class);
                        }
                        break;
                    default:
                        logger.info("分单失败：未找到打印模板1：" + printMode);
                        break;
                }
            }else{
                switch(printMode)
                {
                    case SysDictionary.PRINT_CODE_1106://单切单
                    case SysDictionary.PRINT_CODE_1104://标签切单
                        posPrintModeService = SpringConext.getApplicationContext().getBean(SingleModePrintServiceImp.class);
                        break;
                    case SysDictionary.PRINT_CODE_1113: // 一份一单
                        posPrintModeService = SpringConext.getApplicationContext().getBean(ACopyOfModePrintServiceImp.class);
                        break;
                    case SysDictionary.PRINT_CODE_1107://整台单
                        posPrintModeService = SpringConext.getApplicationContext().getBean(WholeModePrintServiceImp.class);
                        break;
                    case SysDictionary.PRINT_CODE_1105://大类
                        posPrintModeService = SpringConext.getApplicationContext().getBean(ClassModePrintServiceImp.class);
                        break;
                    case SysDictionary.PRINT_CODE_1109://退菜单
                        posPrintModeService = SpringConext.getApplicationContext().getBean(RetreatModePrintServiceImp.class);
                        break;
                    case SysDictionary.PRINT_CODE_1103://POS点菜单
                        posPrintModeService = SpringConext.getApplicationContext().getBean(OrderModePrintServiceImp.class);
                        break;
                    case SysDictionary.PRINT_CODE_1112://单品转台单
                        posPrintModeService = SpringConext.getApplicationContext().getBean(SingleChangeTableModePrintServiceImp.class);
                        break;
                    case SysDictionary.PRINT_CODE_1108://转台
                        posPrintModeService = SpringConext.getApplicationContext().getBean(WholeChangeTableModePrintServiceImp.class);
                        break;
                    case SysDictionary.PRINT_CODE_1101://POS预打单
                    case SysDictionary.PRINT_CODE_1102://POS结账单
                        if(!"1".equals(isLocalPrinter))
                        {
                            posPrintModeService = SpringConext.getApplicationContext().getBean(PaymentModePrintServiceImp.class);
                        }
                        break;
                    case SysDictionary.PRINT_CODE_1301://POS交班单
                    case SysDictionary.PRINT_CODE_1302://补打交班单
                        String printType = para.optString("print_type");
                        String posNum = para.optString("pos_num");
                        String optNum = para.optString("opt_num");
                        String dateRemark = para.optString("date_remark");

                        if(!"1".equals(isLocalPrinter))
                        {
                            if ("1".equalsIgnoreCase(printType))
                            {
                                para.put("opt_pos_num", posNum);
                            }
                            else if ("2".equalsIgnoreCase(printType))
                            {
                                para.put("opt_pos_num", "");
                            }
                            if (StringUtils.isNotEmpty(dateRemark) && dateRemark.contains("~"))
                            {
                                String[] arr = dateRemark.split("~");
                                if (arr.length > 0)
                                {
                                    para.put("start_date", arr[0]);
                                    para.put("end_date", arr[1]);
                                }
                            }
                            para.put("cashier_num", optNum);
                            posPrintModeService = SpringConext.getApplicationContext().getBean(PrintModeServiceImp.class);
                        }
                        break;
                    case SysDictionary.PRINT_CODE_1110://补打结账单
                    case SysDictionary.PRINT_CODE_1013://会员卡发卡
                    case SysDictionary.PRINT_CODE_1008://会员卡充值
                    case SysDictionary.PRINT_CODE_1009://会员卡反充值
                    case SysDictionary.PRINT_CODE_1010://会员卡消费
                    case SysDictionary.PRINT_CODE_1011://会员卡反消费
                    case SysDictionary.PRINT_CODE_1012://会员卡退卡
                    case SysDictionary.PRINT_CODE_1015://会员卡补卡
                    case SysDictionary.PRINT_CODE_1201://外卖随餐单
                    case SysDictionary.PRINT_CODE_1202://外卖取消订单
                    case SysDictionary.PRINT_CODE_1203://外卖随餐单（异常单据）
                    case SysDictionary.PRINT_CODE_1204://外卖取消订单（异常单据）
                    case SysDictionary.PRINT_CODE_1303://交款凭证
                    case SysDictionary.PRINT_CODE_1304://零找金
                        if(!"1".equals(isLocalPrinter))
                        {
                            posPrintModeService = SpringConext.getApplicationContext().getBean(PrintModeServiceImp.class);
                        }
                        break;
                    default:
                        logger.info("分单失败：未找到打印模板1：" + printMode);
                }
            }

			if(posPrintModeService != null){
                posPrintModeService.posPrint(tenancyId, storeId, para, printMode);
            }
		}
		catch(Exception e)
		{
			logger.error("分单失败：分单处理异常：" , e);
		}
		logger.info("=========>根据打印模板分单处理结束");
	}

	/**
	 * @Description 是否开启新的打印模式
	 * @param tenantId 商户号
	 *        storeId 门店号
	 * @return 
	 * @exception SystemException 系统异常
	 * <AUTHOR> email:<EMAIL>
	 * @version 1.0  2017-06-21
	 * @see
	 */
	@Override
	public boolean isNONewPrint(String tenantId, Integer storeId) throws SystemException{
		// 获取Spring容器中的bean对象。
		PosDao posDao = (PosDao)SpringContextUtils.getBean("com.tzx.pos.po.springjdbc.dao.imp.PosDaoImp");		
		String ret ="";
		try {			
			ret = posDao.getSysParameter(tenantId,storeId,"new_printer_type");
			//System.out.println("getSysParameter返回值："+ ret);
		} catch (Exception e) {
			ret="";
		}
		if(StringUtils.isNotEmpty(ret) && !ret.equals("0")){
			return true;
		}else{
			return false;
		}
	}

}
