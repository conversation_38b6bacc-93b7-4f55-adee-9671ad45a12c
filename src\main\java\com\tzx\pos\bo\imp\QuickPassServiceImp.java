package com.tzx.pos.bo.imp;

import java.util.List;
import java.util.Map;

import com.tzx.pos.po.springjdbc.dao.PosDishDao;
import com.tzx.pos.po.springjdbc.dao.imp.PosBillDaoImp;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.tzx.framework.common.entity.Data;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.base.util.ReqDataUtil;
import com.tzx.pos.bo.QuickPassService;
import com.tzx.pos.po.springjdbc.dao.QuickPassDao;

import net.sf.json.JSONObject;



@Service(QuickPassService.NAME)
public class QuickPassServiceImp implements QuickPassService {
	private static final Logger	logger	= Logger.getLogger(QuickPassServiceImp.class);
	
	@Autowired
	private QuickPassDao quickPassDao;
	@Autowired
	private PosBillDaoImp posBillDaoImp;//bill表添加

	@Override
	public int insert(JSONObject param) throws Exception {
		// TODO Auto-generated method stub
		List<JSONObject> list1=param.getJSONArray("data");
		JSONObject data = JSONObject.fromObject(list1.get(0));
		JSONObject  item= JSONObject.fromObject(data.optString("item"));
		String bill_code=ParamUtil.getStringValueByObject(item,"bill_code",false,null);
		 String tenancy_id = param.getString("tenancy_id");
		 Integer  store_id = ParamUtil.getIntegerValueByObject(param,"store_id",false,null);
		String  report_date=ParamUtil.getDateStringValue(data,"report_date");
		Integer  shift_id=ParamUtil.getIntegerValueByObject(data,"shift_id",false,null);
		String  pos_num=ParamUtil.getStringValueByObject(data,"pos_num",false,null);
		String  opt_num=ParamUtil.getStringValueByObject(data,"opt_num",false,null);
		String  bill_num=ParamUtil.getStringValueByObject(data,"bill_num",false,null);
		String  table_code=ParamUtil.getStringValueByObject(data,"table_code",false,null);
		Double  bill_amount=ParamUtil.getDoubleValueByObject(data,"bill_amount",false,null);//账单总金额
		String  sale_mode=ParamUtil.getStringValueByObject(data,"sale_mode",false,null);
		String  isprint_bill=ParamUtil.getStringValueByObject(data,"isprint_bill",false,null);
		String  is_invoice=ParamUtil.getStringValueByObject(data,"is_invoice",false,null);
		String  is_online_payment= ParamUtil.getStringValueByObject(data,"is_online_payment",false,null);
		Integer  jzid= ParamUtil.getIntegerValueByObject(item,"jzid",false,null);//结账Id
		Double amount =ParamUtil.getDoubleValueByObject(item,"amount",false,null);//消费金额
		Integer count =ParamUtil.getIntegerValueByObject(item,"count",false,null);
		String number=ParamUtil.getStringValueByObject(item,"number",false,null);
		String phone=ParamUtil.getStringValueByObject(item,"phone",false,null);
		String reason_id=ParamUtil.getStringValueByObject(item,"reason_id",false,null);
		Integer customer_id=ParamUtil.getIntegerValueByObject(item,"customer_id",false,null);//会员id
		//二期新加内容
		Integer  jzid_credit= ParamUtil.getIntegerValueByObject(item,"jzid_credit",false,null);//积分结账Id
		Integer mem =ParamUtil.getIntegerValueByObject(item,"mem",false,null);//是否会员
		Double jifen =ParamUtil.getDoubleValueByObject(item,"jifen",false,null);//消费积分
		Double dixian =ParamUtil.getDoubleValueByObject(item,"dixian",false,null);//抵现金额
		double difference=bill_amount-amount;
		//从账单 表取出对应数据
		if(null==pos_num||"".equals(pos_num)){
			List<JSONObject> list=posBillDaoImp.findPosPayment(bill_num);
			if(null!=list&&list.size()>0){
				JSONObject jsonObject=list.get(0);
				pos_num=jsonObject.getString("open_pos_num");
				opt_num=jsonObject.getString("open_opt");
				report_date=jsonObject.getString("report_date");
				sale_mode=jsonObject.getString("sale_mode");
			}
		}
			Object[] objs =new Object[]{
				tenancy_id,store_id,report_date==null?null:DateUtil.parseTimestamp(report_date),shift_id,pos_num,opt_num,bill_num,table_code,sale_mode,
				isprint_bill,is_invoice,is_online_payment,amount,difference,bill_amount,count,number,
				phone,reason_id,customer_id,bill_code,1,amount,jzid,mem,jzid_credit,jifen,dixian};
		int res=quickPassDao.insert(objs);
		logger.info("插入闪付表成功");
		return res;
	}

	@Override
	public Data  findList(Data param) throws Exception {
		// TODO Auto-generated method stub
		Map<String, Object> map = ReqDataUtil.getDataMap(param);
		
		List<JSONObject> resLst=quickPassDao.findList(map);
		Data result=new Data();
		if(resLst==null||resLst.size()<1){
			result.setCode(Constant.CODE_NULL_DATASET);
			result.setMsg(Constant.QUICK_PASS_FAILURE);
		}else{
		result.setCode(Constant.CODE_SUCCESS);
		result.setMsg(Constant.QUICK_PASS_SUCCESS);
		}
		result.setData(resLst);
		return result;
	}

	@Override
	public Data deleteData(Data param) throws Exception {
		Map<String, Object> map = ReqDataUtil.getDataMap(param);
		String bill_code=ParamUtil.getStringValue(map, "bill_code", false, null);
		int quick_id=ParamUtil.getIntegerValue(map, "quick_id", false, null);
		// TODO Auto-generated method stub
		int res=quickPassDao.deleteData(bill_code, quick_id);
		Data result=new Data();
		if(res<1){
			result.setCode(Constant.CODE_NULL_DATASET);
			result.setMsg(Constant.QUICK_PASS_DEL_FAILURE);
		}else{
		result.setCode(Constant.CODE_SUCCESS);
		result.setMsg(Constant.QUICK_PASS_DEL_SUCCESS);
		}
		return result;
	}

}
