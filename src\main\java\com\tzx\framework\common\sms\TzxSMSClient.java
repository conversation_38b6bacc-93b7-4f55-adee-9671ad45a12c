package com.tzx.framework.common.sms;

import java.util.PropertyResourceBundle;
import java.util.ResourceBundle;

import cn.emay.sdk.client.api.Client;

/**
 * <AUTHOR> E-mail: email��ַ
 * @version Jun 28, 2011 9:40:23 AM
 * 
 */

public class TzxSMSClient {
	private static Client client=null;
	
	private TzxSMSClient(){
		
	}
	/**
	 * 获取客户端对象，相应的短信操作的方法都要通过该对象
	 * @return
	 */
	public synchronized static Client getClient(){
		ResourceBundle bundle=PropertyResourceBundle.getBundle("config");
		if(client==null){
			try {
				System.out.println("=========建立client对象==========");
				client=new Client(bundle.getString("softwareSerialNo"),bundle.getString("key"));
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return client;
	}
	
	public synchronized static Client getClient(String softwareSerialNo,String key){
		if(client==null){
			try {
				client=new Client(softwareSerialNo,key);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return client;
	}
	
	
}
