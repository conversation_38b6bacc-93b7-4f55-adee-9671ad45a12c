package com.tzx.pos.bo.imp;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;

import com.tzx.base.bo.PosCodeService;
import com.tzx.framework.common.constant.Code;
import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.CrmErrorCode;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.Comet4jUtil;
import com.tzx.framework.common.util.DoubleHelper;
import com.tzx.framework.common.util.JavaMd5;
import com.tzx.framework.common.util.PropertiesLoader;
import com.tzx.framework.common.util.Tools;
import com.tzx.member.crm.bo.CustomerService;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.base.util.ReqDataUtil;
import com.tzx.pos.bo.PaymentService;
import com.tzx.pos.bo.PosDishService;
import com.tzx.pos.bo.PosPrintService;
import com.tzx.pos.bo.PosService;
import com.tzx.pos.bo.PosServiceSelf;
import com.tzx.pos.bo.dto.PosTableFind;
import com.tzx.pos.po.springjdbc.dao.PosSelfDao;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

@Service(PosServiceSelf.NAME)
public class PosServiceSelfImp extends PosBaseServiceImp implements PosServiceSelf
{

	private static final Logger	logger			= Logger.getLogger(PosServiceSelfImp.class);

	@Resource(name = PosService.NAME)
	private PosService			posService;

	@Resource(name = PosDishService.NAME)
	private PosDishService		posDishService;

	@Resource(name = PaymentService.NAME)
	private PaymentService		paymentService;

	@Resource(name = CustomerService.NAME)
	private CustomerService		customerService;

	@Resource(name = PosPrintService.NAME)
	private PosPrintService		posPrintService;

//	@Resource(name = PaymentClassService.NAME)
//	private PaymentClassService	paymentClassService;

	@Resource(name=PosCodeService.NAME)
	PosCodeService					codeService;

	@Resource(name = PosSelfDao.NAME)
	private PosSelfDao			posSelfDao;
	
//	@Resource(name = PosDishDao.NAME)
//	private PosDishDao			posDishDao;

	private final int			PAYMENT_SCALE	= 2;
	private final int			DEFAULT_SCALE	= 4;

	@Override
	public List<PosTableFind> tableStateSelf(Data param) throws SystemException
	{
		return posService.findTable(param);
		
//		String tenancyId = param.getTenancy_id();
//		Integer storeId = param.getStore_id();
//
//		Map<String, Object> map = ReqDataUtil.getDataMap(param); // 开台传入的参数
//		String tableNo = ParamUtil.getStringValue(map, "table_code", false, null);
//
//		Integer tablePropertyId = ParamUtil.getIntegerValue(map, "table_property_id", false, null);
//
//		String tableState = ParamUtil.getStringValue(map, "tablestate", false, null);
//
//		Integer businessAreaId = ParamUtil.getIntegerValue(map, "business_area_id", false, null);
		
//		return posDao.findTable(tenancyId, storeId, tableNo, tablePropertyId, tableState, businessAreaId);
	}

	@Override
	public Data openTableOrderDish(Data param) throws Exception
	{
		String tenancyId = param.getTenancy_id();
		Integer storeId = param.getStore_id();
		String source = param.getSource();

		// Map<String, Object> map = ReqDataUtil.getDataMap(param); // 开台传入的参数

		JSONObject para = JSONObject.fromObject(param.getData().get(0));

		Date reportDate = getReportDate(tenancyId, storeId, para);
		String optNum = getOptNum(tenancyId, storeId, source, reportDate, para);
		Integer shiftId = getShiftId(tenancyId, storeId, source, reportDate, optNum, para);

		para.put("shift_id", shiftId);
		para.put("report_date", DateUtil.format(reportDate));
		para.put("opt_num", optNum);
		
		String tableCode = para.optString("table_code");
		String billno = "";
		String batchNum = "";

		StringBuilder sql = new StringBuilder("select pb.bill_num,pb.batch_num from pos_bill pb where pb.table_code=? and pb.bill_property=? and pb.store_id=? and pb.tenancy_id=?");
		SqlRowSet rs = posSelfDao.query4SqlRowSet(sql.toString(), new Object[]
		{ tableCode, SysDictionary.BILL_PROPERTY_OPEN, storeId, tenancyId });
		if (rs.next())
		{
			billno = rs.getString("bill_num");
			batchNum = rs.getString("batch_num");
		}
		else
		{
			// 餐谱ID取得
			StringBuffer organStr = new StringBuffer("select item_menu_id from hq_item_menu_organ where store_id=? and tenancy_id=?");
			SqlRowSet organRs = posSelfDao.query4SqlRowSet(organStr.toString(), new Object[]
			{ storeId, tenancyId });
			Integer itemMenuId = null;
			if (organRs.next())
			{
				itemMenuId = organRs.getInt("item_menu_id");
			}

			para.put("item_menu_id", itemMenuId);
			para.put("isprint", "N");
			para.put("mode", "0");// 开台接口 mode:0:开台-正餐用 1:产生新单-快餐用
			para.put("preorderno", "");
			para.put("copy_bill_num", "");
			
			List<JSONObject> dataList = new ArrayList<JSONObject>();
			dataList.add(para);
			param.setData(dataList);

//			List<JSONObject> list = posDishService.newOpenTable(param, codeService);
			List<JSONObject> list = posDishService.newOpenTable(param, codeService, new JSONObject());

			if(list !=null && list.size()>0)
			{
				JSONObject billJson = list.get(0);
				billno = billJson.optString("bill_num");
				batchNum = billJson.optString("batch_num");
			}
		}

		createBatchNum(tenancyId, storeId, billno, batchNum, reportDate, shiftId, para.optString("pos_num"), optNum);
		
		if(para.containsKey("item") && para.optJSONArray("item").size()>0)
		{
			StringBuilder updateBillPaySql = new StringBuilder("update pos_bill set payment_state=? where tenancy_id= ? and store_id=? and bill_num=?");
			posSelfDao.update(updateBillPaySql.toString(), new Object[]
			{ SysDictionary.PAYMENT_STATE_NOTPAY, tenancyId, storeId, billno });
	
			para.put("mode", "0");// 结账接口mode: 0:下单 1:暂存
			para.put("bill_num", billno);
			List<JSONObject> dataList = new ArrayList<JSONObject>();
			dataList.add(para);
			param.setData(dataList);
			param.setSource(SysDictionary.SOURCE_ANDROID_PAD);
			
			JSONObject json = new JSONObject();
			return posDishService.newOrderDish(param, json);
		}
		else
		{
			Data data = posSelfDao.newOrderDish(tenancyId, billno, storeId, "0");
			data.setMsg(Constant.OPEN_TABLE_SUCCESS);
			return data;
		}
	}

	@Override
	public void posBillPaymentSelf(Data param, Data result, JSONObject printJson) throws Exception
	{
		String tenancyId = param.getTenancy_id();
		int storeId = param.getStore_id();
		String source = param.getSource();

		List<?> paramList = param.getData();
		if (paramList == null || paramList.isEmpty())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}

		JSONObject para = JSONObject.fromObject(paramList.get(0));

		Date reportDate = getReportDate(tenancyId, storeId, para);
		String optNum = getOptNum(tenancyId, storeId, source, reportDate, para);
		Integer shiftId = getShiftId(tenancyId, storeId, source, reportDate, optNum, para);

		String billNum = para.optString("bill_num");
		String posNum = para.optString("pos_num");
		String tableCode = para.optString("table_code");
		String isprint_bill = para.optString("isprint_bill");
		if (Tools.isNullOrEmpty(isprint_bill))
		{
			isprint_bill = "N";
		}
		if (Tools.isNullOrEmpty(billNum))
		{
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_BILL_NUM);
		}
		if (Tools.isNullOrEmpty(para.opt("item")))
		{
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_ITEM_LIST);
		}

		Timestamp currentTime = DateUtil.currentTimestamp();

		// 验证签到
		posSelfDao.checkKssy(reportDate, optNum, storeId,shiftId,posNum);

		StringBuilder billAmountSql = new StringBuilder(
				"select coalesce(b.bill_amount,0) as bill_amount,coalesce(b.payment_amount,0) as payment_amount,coalesce(b.discount_rate,100) as discount_rate,coalesce(b.discount_case_id,0) as discount_case_id,coalesce(b.discount_amount,0) as discount_amount,b.source,b.bill_property,b.batch_num,b.payment_state,b.table_code from pos_bill b where b.tenancy_id=? and b.store_id=? and b.bill_num=?");
		SqlRowSet rs = posSelfDao.query4SqlRowSet(billAmountSql.toString(), new Object[]
		{ tenancyId, storeId, billNum });

		double paymentAmount = 0d;
		double discountAmount = 0d;
		double discountRate = 0d;
		int discountCaseId =0;
		String chanel = "";
		String billProperty = "";
		String batch_num = "";

		if (rs.next())
		{
			chanel = rs.getString("source");
			billProperty = rs.getString("bill_property");
			batch_num = rs.getString("batch_num");
			tableCode = rs.getString("table_code");
		}
		else
		{
			throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_BILL);
		}

		if (SysDictionary.BILL_PROPERTY_CLOSED.equalsIgnoreCase(billProperty))
		{
			throw SystemException.getInstance(PosErrorCode.BILL_CLOSED);
		}
		
		JSONObject billBatchJson =posSelfDao.queryBillBatchByBillNum(tenancyId, storeId, billNum, batch_num);
		String billBatchProperty = "";
		String batchPaymentState = "";
		double difference =0d;
		double billAmount = 0d;
		double returnAmount=0d;
		if(!billBatchJson.isEmpty())
		{
			billBatchProperty = billBatchJson.optString("bill_property");
			batchPaymentState = billBatchJson.optString("payment_state");
			paymentAmount = billBatchJson.optDouble("payment_amount");
			if(Double.isNaN(paymentAmount))
			{
				paymentAmount = 0d;
			}
			discountAmount = billBatchJson.optDouble("discount_amount");
			if(Double.isNaN(discountAmount))
			{
				discountAmount = 0d;
			}
			discountCaseId = billBatchJson.optInt("discount_case_id");
			discountRate = billBatchJson.optDouble("discount_rate");
			if (discountRate <= 0)
			{
				discountRate = 100d;
			}
			difference = billBatchJson.optDouble("difference");
			if(Double.isNaN(difference))
			{
				difference = 0d;
			}
			
			billAmount = billBatchJson.optDouble("bill_amount");
			if(Double.isNaN(billAmount))
			{
				billAmount = 0d;
			}
			
			returnAmount = billBatchJson.optDouble("return_amount");
			if(Double.isNaN(returnAmount))
			{
				returnAmount = 0d;
			}
		}
		
		if(SysDictionary.BILL_PROPERTY_CLOSED.equals(billBatchProperty))
		{
			throw SystemException.getInstance(PosErrorCode.BILL_ALREADY_PAY_AMOUNT_ERROR);
		}
		
		if(difference<=0 && SysDictionary.PAYMENT_STATE_PAY.equals(batchPaymentState))
		{
			throw SystemException.getInstance(PosErrorCode.BILL_PAYMENT_STATE_PAYING_ERROR);
		}
		
//		String sumPaysql = new String("select coalesce(sum(currency_amount),0) as amount,count(case when batch_num=? then true else null end) as count,count(nullif(payment_state,?)) unpaid_count from pos_bill_payment where bill_num =? and store_id = ?");
//		rs = posSelfDao.query4SqlRowSet(sumPaysql.toString(), new Object[]
//		{ batch_num, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, billNum, storeId });
//		if (rs.next())
//		{
//			if (0 < rs.getInt("count") && paymentAmount <= rs.getDouble("amount"))
//			{
//				throw SystemException.getInstance(PosErrorCode.BILL_ALREADY_PAY_AMOUNT_ERROR);
//			}
//			if (0 < rs.getInt("unpaid_count"))
//			{
//				throw SystemException.getInstance(PosErrorCode.BILL_PAYMENT_STATE_PAYING_ERROR);
//			}
//		}

		if (paymentAmount <= 0)
		{
			if (billAmount == 0d && returnAmount == 0d)
			{
				throw SystemException.getInstance(PosErrorCode.BILL_AMOUNT_ZERO);
			}
			
			// 账单金额为0,判断是否可以零结账
			String isZeroPay = posSelfDao.getSysParameter(tenancyId, storeId, "IS_ZEROPAY");
			if (!"1".equals(isZeroPay))
			{
				throw SystemException.getInstance(PosErrorCode.BILL_AMOUNT_ZERO);
			}
		}

		JSONArray arr = para.optJSONArray("item");
		StringBuilder newstate = new StringBuilder();

		for (Object obj : arr)
		{
			JSONObject itemJson = JSONObject.fromObject(obj);
			
			if (Tools.isNullOrEmpty(itemJson.opt("jzid")))
			{
				throw SystemException.getInstance(PosErrorCode.NOT_NULL_JZID);
			}
			if (Tools.isNullOrEmpty(itemJson.opt("amount")))
			{
				throw SystemException.getInstance(PosErrorCode.NOT_NULL_PAYMENT_AMOUNT);
			}

			int jzid = itemJson.optInt("jzid");
			double pay_amount = itemJson.optDouble("amount");
			double pay_count = itemJson.optDouble("count");
			double currency_amount = itemJson.optDouble("currency_amount");
			String number = itemJson.optString("number");
			String phone = itemJson.optString("phone");
			int customer_id = itemJson.optInt("customer_id");
			String bill_code = itemJson.optString("bill_code");
			String is_ysk = itemJson.optString("is_ysk");
			String remark = itemJson.optString("remark");

			String paymentClass = "";
			String paymentName = "";
			String paymentEnglishName = "";
			double exchangeRate = 1d;
			boolean isCheck = true;
			
			JSONObject paymentWayJson  =  posSelfDao.getPaymentWayByID(tenancyId, storeId, jzid);
			if(paymentWayJson !=null && !paymentWayJson.isEmpty())
			{
				paymentClass = paymentWayJson.optString("payment_class");
				paymentName = paymentWayJson.optString("payment_name");
				paymentEnglishName = paymentWayJson.optString("name_english");
				exchangeRate = Tools.hv(paymentWayJson.opt("rate")) ? paymentWayJson.optDouble("rate") : 1d;
				
				if(Tools.isNullOrEmpty(itemJson.opt("is_check")))
				{
					isCheck = "1".equals(paymentWayJson.optString("is_check"));
				}
				else
				{
					isCheck = "1".equals(itemJson.optString("is_check"));
				}
			}
			else
			{
				throw SystemException.getInstance(PosErrorCode.NOT_EXIST_JZID);
			}

			newstate.append("支付方式:").append(paymentName).append(",").append("支付金额:").append(pay_amount).append(";");

			if (paymentAmount > 0 && currency_amount > 0)
			{
				if (SysDictionary.PAYMENT_CLASS_FREESINGLE.equals(paymentClass))
				{
					// 如果有优惠提示取消优惠
					if (discountCaseId != 0 || discountRate != 100 || discountAmount > 0)
					{
						throw SystemException.getInstance(PosErrorCode.POS_FREESINGLE_NOT_DISCOUNT_ERROR);
					}

					// 免单不允许与其他方式混结
					int count = posSelfDao.queryPosBillPaymentForCountByBatchNum(tenancyId, storeId, billNum, batch_num);
					if (count > 0)
					{
						throw SystemException.getInstance(PosErrorCode.POS_FREESINGLE_NOT_PAYMENT_ERROR);
					}

					// 免单金额=账单金额
					posSelfDao.insertPosBillPayment(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, paymentAmount, pay_count, paymentAmount, null, null, is_ysk, null, null, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batch_num, null,0d);

					String updateBillSql = new String("update pos_bill set free_amount=(free_amount+?),billfree_reason_id=? where bill_num = ? and store_id = ? and tenancy_id = ?");
					posSelfDao.update(updateBillSql, new Object[]
					{ paymentAmount, itemJson.optInt("reason_id"), billNum, storeId, tenancyId });

					posSelfDao.updateBillBatchForFreeAmountByBatchNum(tenancyId, storeId, billNum, batch_num, paymentAmount, itemJson.optInt("reason_id"));
					
//					String updateBillItemSql = new String("update pos_bill_item set item_remark=? where bill_num = ? and store_id = ? and tenancy_id = ? and batch_num =? and item_remark is null");
//					posSelfDao.update(updateBillItemSql, new Object[]
//					{ SysDictionary.ITEM_REMARK_MD03, billNum, storeId, tenancyId, batch_num });
					
					posSelfDao.updateBillItemForItemRemarkByBatchNum(tenancyId, storeId, billNum, batch_num, SysDictionary.ITEM_REMARK_MD03);

					// 写入付款日志表
					posSelfDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, paymentAmount, pay_count, paymentAmount, null, null, is_ysk, null, null, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batch_num, null,0d);
				}
				else if (SysDictionary.PAYMENT_CLASS_CASH.equals(paymentClass))
				{// 现金 cash
					double cashAmount = 0d;
					StringBuilder findPaymentSql = new StringBuilder("select amount from pos_bill_payment where tenancy_id=? and store_id = ? and bill_num = ? and batch_num=? and jzid = ?");
					rs = posSelfDao.query4SqlRowSet(findPaymentSql.toString(), new Object[]
					{ tenancyId, storeId, billNum, batch_num, jzid });
					if (rs.next())
					{
						cashAmount = rs.getDouble("amount");
					}

					if (cashAmount > 0)
					{
						cashAmount = DoubleHelper.add(cashAmount, pay_amount, PAYMENT_SCALE);
						double currencyAmount = DoubleHelper.mul(cashAmount, exchangeRate, PAYMENT_SCALE);
						StringBuilder updateBillPaymentSql = new StringBuilder("update pos_bill_payment set amount=?,count=?,report_date=?,shift_id=?,pos_num=?,cashier_num=?,last_updatetime=?,rate=?,currency_amount=? where store_id = ? and bill_num = ? and batch_num=? and jzid = ?");
						posSelfDao.update(updateBillPaymentSql.toString(), new Object[]
						{ cashAmount, 1, reportDate, shiftId, posNum, optNum, currentTime, exchangeRate, currencyAmount, storeId, billNum, batch_num, jzid });
					}
					else
					{
						double currencyAmount = DoubleHelper.mul(pay_amount, exchangeRate, PAYMENT_SCALE);
						posSelfDao.insertPosBillPayment(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currencyAmount, null, null, is_ysk, null, null, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batch_num, null,0d);
					}

					// 写入付款日志表
					double currencyAmount = DoubleHelper.mul(pay_amount, exchangeRate, PAYMENT_SCALE);
					posSelfDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currencyAmount, null, null, is_ysk, null, null, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batch_num, null,0d);
				}
				else if (SysDictionary.PAYMENT_CLASS_BANKCARD.equals(paymentClass))
				{// 银行卡 bankcard
					posSelfDao.insertPosBillPayment(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, null, bill_code, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batch_num, null,0d);
					
					posSelfDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, null, bill_code, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batch_num, null,0d);
				}
				else if (SysDictionary.PAYMENT_CLASS_CARD.equals(paymentClass))
				{// 本系统卡 card
					itemJson.put("chanel", chanel);
					itemJson.put("payment_amount", paymentAmount);
					String card_code = number;
					if (Tools.isNullOrEmpty(card_code))
					{
						card_code = itemJson.optString("third_code");
					}
					posSelfDao.insertPosBillPayment(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, card_code, phone, is_ysk, null, null, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY, batch_num, itemJson.toString(),0d);
				}
				else if (SysDictionary.PAYMENT_CLASS_COUPONS.equals(paymentClass))
				{
					itemJson.put("chanel", chanel);
					itemJson.put("payment_amount", paymentAmount);
					posSelfDao.insertPosBillPayment(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, null, null, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY, batch_num, itemJson.toString(),0d);
				}
				else if (SysDictionary.PAYMENT_CLASS_INCORPORATION.equals(paymentClass))
				{
					if (isCheck || !StringUtils.isEmpty(number))
					{
						itemJson.put("chanel", chanel);
						itemJson.put("payment_amount", paymentAmount);
						posSelfDao.insertPosBillPayment(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, customer_id, null, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY, batch_num, itemJson.toString(),0d);
					}
					else
					{
						posSelfDao.insertPosBillPayment(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, customer_id, null, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batch_num, null,0d);
						posSelfDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, customer_id, null, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batch_num, null,0d);
					}
					
				}
				else if (SysDictionary.PAYMENT_CLASS_WECHAT_PAY.equals(paymentClass))
				{// 微信支付 wechat_pay
					posSelfDao.insertPosBillPayment(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, null, null, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batch_num, null,0d);
					posSelfDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, null, null, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batch_num, null,0d);
				}
				else if (SysDictionary.PAYMENT_DDM_QUICK_PAY.equals(paymentClass) || SysDictionary.PAYMENT_CLASS_ALI_PAY.equals(paymentClass))
				{// 支付宝支付 ali_pay
					posSelfDao.insertPosBillPayment(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, null, null, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batch_num, null,0d);
					posSelfDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, null, null, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batch_num, null,0d);
				}
				else
				{
					posSelfDao.insertPosBillPayment(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, customer_id, bill_code, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batch_num, null,0d);
					posSelfDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, customer_id, bill_code, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batch_num, null,0d);
				}
			}
			else
			{
				double amount = paymentAmount > 0 ? pay_amount : paymentAmount;
				posSelfDao.insertPosBillPayment(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, amount, pay_count, amount, number, phone, is_ysk, customer_id, bill_code, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batch_num, null,0d);
				posSelfDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, customer_id, bill_code, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batch_num, null,0d);
			}
		}
		
		String sql = new String("select coalesce(sum(currency_amount),0) as amount from pos_bill_payment where tenancy_id =? and store_id = ? and bill_num =? and batch_num=?");
		rs = posSelfDao.query4SqlRowSet(sql.toString(), new Object[]
		{ tenancyId, storeId, billNum, batch_num });
//		double difference = 0d;
		if (rs.next())
		{
			difference = DoubleHelper.sub(paymentAmount, rs.getDouble("amount"), DEFAULT_SCALE);
		}
		
		if (difference <= 0)
		{
			List<JSONObject> payList = posSelfDao.queryPosBillPaymentForPayingByBatch(tenancyId, storeId, billNum, batch_num);
			if(null != payList && payList.size()>0)
			{
				this.billPaymentDebited(tenancyId, storeId, billNum, payList, result);
			}
			
			this.completePayment(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode,paymentAmount, batch_num, isprint_bill, currentTime, printJson);
		}
		else
		{
			this.updatePosBillBatch(tenancyId, storeId, billNum, batch_num);
			
			this.updatePosBill(tenancyId, storeId, billNum);
		}
		
		this.getBillPaymentList(tenancyId, storeId, billNum, batch_num, "Y", result);
		
		if (0 == result.getCode())
		{
			result.setCode(Constant.CODE_SUCCESS);
			result.setMsg(Constant.PAYMENT_SUCCESS);
		}

		// 写日志pos_log
		posSelfDao.savePosLog(tenancyId, storeId, posNum, optNum, optNum, shiftId, reportDate, Constant.TITLE, "结账", "账单编号:" + billNum + "; 应付金额:" + String.valueOf(paymentAmount), newstate.toString());
	}

	public void posBillPaymentSelf_old(Data param, Data result, JSONObject printJson) throws Exception
	{
		String tenancyId = param.getTenancy_id();
		int storeId = param.getStore_id();
		String source = param.getSource();

		List<?> paramList = param.getData();
		if (paramList == null || paramList.isEmpty())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}

		JSONObject para = JSONObject.fromObject(paramList.get(0));

		Date reportDate = getReportDate(tenancyId, storeId, para);
		String optNum = getOptNum(tenancyId, storeId, source, reportDate, para);
		Integer shiftId = getShiftId(tenancyId, storeId, source, reportDate, optNum, para);

		String billNum = para.optString("bill_num");
		String posNum = para.optString("pos_num");
		String tableCode = para.optString("table_code");
		String isprint_bill = para.optString("isprint_bill");
		if (Tools.isNullOrEmpty(isprint_bill))
		{
			isprint_bill = "N";
		}
		if (Tools.isNullOrEmpty(billNum))
		{
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_BILL_NUM);
		}
		if (Tools.isNullOrEmpty(para.opt("item")))
		{
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_ITEM_LIST);
		}

		Timestamp currentTime = DateUtil.currentTimestamp();

		String optName = posSelfDao.getEmpNameById(optNum, tenancyId, storeId);
		// 验证签到
		posSelfDao.checkKssy(reportDate, optNum, storeId,shiftId,posNum);

		StringBuilder billAmountSql = new StringBuilder(
				"select coalesce(b.bill_amount,0) as bill_amount,coalesce(b.payment_amount,0) as payment_amount,coalesce(b.discount_rate,100) as discount_rate,coalesce(b.discount_case_id,0) as discount_case_id,coalesce(b.discount_amount,0) as discount_amount,b.source,b.bill_property,b.batch_num,b.payment_state,b.table_code from pos_bill b where b.tenancy_id=? and b.store_id=? and b.bill_num=?");
		SqlRowSet rs = posSelfDao.query4SqlRowSet(billAmountSql.toString(), new Object[]
		{ tenancyId, storeId, billNum });

		double paymentAmount = 0d;
		double discountAmount = 0d;
		double discountRate = 0d;
		int discountCaseId =0;
		String chanel = "";
		String billProperty = "";
		String batch_num = "";

		if (rs.next())
		{
			chanel = rs.getString("source");
			billProperty = rs.getString("bill_property");
			batch_num = rs.getString("batch_num");
			tableCode = rs.getString("table_code");
		}
		else
		{
			throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_BILL);
		}

		if (SysDictionary.BILL_PROPERTY_CLOSED.equalsIgnoreCase(billProperty))
		{
			throw SystemException.getInstance(PosErrorCode.BILL_CLOSED);
		}
		
		JSONObject billBatchJson =posSelfDao.queryBillBatchByBillNum(tenancyId, storeId, billNum, batch_num);
		String billBatchProperty = "";
		String batchPaymentState = "";
		if(!billBatchJson.isEmpty())
		{
			billBatchProperty = billBatchJson.optString("bill_property");
			batchPaymentState = billBatchJson.optString("payment_state");
			paymentAmount = billBatchJson.optDouble("payment_amount");
			discountAmount = billBatchJson.optDouble("discount_amount");
			discountCaseId = billBatchJson.optInt("discount_case_id");
			discountRate = billBatchJson.optDouble("discount_rate");
			if (discountRate <= 0)
			{
				discountRate = 100d;
			}
		}
		
		if(SysDictionary.BILL_PROPERTY_CLOSED.equals(billBatchProperty))
		{
			throw SystemException.getInstance(PosErrorCode.BILL_ALREADY_PAY_AMOUNT_ERROR);
		}
		
		if(SysDictionary.PAYMENT_STATE_PAY.equals(batchPaymentState))
		{
			throw SystemException.getInstance(PosErrorCode.BILL_PAYMENT_STATE_PAYING_ERROR);
		}
		
//		String sumPaysql = new String("select coalesce(sum(currency_amount),0) as amount,count(case when batch_num=? then true else null end) as count,count(nullif(payment_state,?)) unpaid_count from pos_bill_payment where bill_num =? and store_id = ?");
//		rs = posSelfDao.query4SqlRowSet(sumPaysql.toString(), new Object[]
//		{ batch_num, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, billNum, storeId });
//		if (rs.next())
//		{
//			if (0 < rs.getInt("count") && paymentAmount <= rs.getDouble("amount"))
//			{
//				throw SystemException.getInstance(PosErrorCode.BILL_ALREADY_PAY_AMOUNT_ERROR);
//			}
//			if (0 < rs.getInt("unpaid_count"))
//			{
//				throw SystemException.getInstance(PosErrorCode.BILL_PAYMENT_STATE_PAYING_ERROR);
//			}
//		}

		if (paymentAmount <= 0)
		{// 账单金额为0,判断是否可以零结账
			String isZeroPay = posSelfDao.getSysParameter(tenancyId, storeId, "IS_ZEROPAY");
			if (!"1".equals(isZeroPay))
			{
				throw SystemException.getInstance(PosErrorCode.BILL_AMOUNT_ZERO);
			}
		}

		JSONArray arr = para.optJSONArray("item");
		JSONObject resultJson = new JSONObject();
		StringBuilder newstate = new StringBuilder();

		for (Object obj : arr)
		{
			JSONObject itemJson = JSONObject.fromObject(obj);
			
			if (Tools.isNullOrEmpty(itemJson.opt("jzid")))
			{
				throw SystemException.getInstance(PosErrorCode.NOT_NULL_JZID);
			}
			if (Tools.isNullOrEmpty(itemJson.opt("amount")))
			{
				throw SystemException.getInstance(PosErrorCode.NOT_NULL_PAYMENT_AMOUNT);
			}

			int jzid = itemJson.optInt("jzid");
			double pay_amount = itemJson.optDouble("amount");
			double pay_count = itemJson.optDouble("count");
			double currency_amount = itemJson.optDouble("currency_amount");
			String number = itemJson.optString("number");
			String phone = itemJson.optString("phone");
			int customer_id = itemJson.optInt("customer_id");
			String bill_code = itemJson.optString("bill_code");
			String is_ysk = itemJson.optString("is_ysk");
			String remark = itemJson.optString("remark");

			String paymentClass = "";
			String paymentName = "";
			String paymentEnglishName = "";
			double exchangeRate = 1d;
			
			JSONObject paymentWayJson  =  posSelfDao.getPaymentWayByID(tenancyId, storeId, jzid);
			if(paymentWayJson !=null && !paymentWayJson.isEmpty())
			{
				paymentClass = paymentWayJson.optString("payment_class");
				paymentName = paymentWayJson.optString("payment_name");
				paymentEnglishName = paymentWayJson.optString("name_english");
				exchangeRate = Tools.hv(paymentWayJson.opt("rate")) ? paymentWayJson.optDouble("rate") : 1d;
			}
			else
			{
				throw SystemException.getInstance(PosErrorCode.NOT_EXIST_JZID);
			}

			newstate.append("支付方式:").append(paymentName).append(",").append("支付金额:").append(pay_amount).append(";");

			if (paymentAmount > 0 && currency_amount > 0)
			{
				if (SysDictionary.PAYMENT_CLASS_FREESINGLE.equals(paymentClass))
				{
					// 如果有优惠提示取消优惠
					if (discountCaseId != 0 || discountRate != 100 || discountAmount > 0)
					{
						throw SystemException.getInstance(PosErrorCode.POS_FREESINGLE_NOT_DISCOUNT_ERROR);
					}

					// 免单不允许与其他方式混结
					int count = posSelfDao.queryPosBillPaymentForCountByBatchNum(tenancyId, storeId, billNum, batch_num);
					if (count > 0)
					{
						throw SystemException.getInstance(PosErrorCode.POS_FREESINGLE_NOT_PAYMENT_ERROR);
					}

					// 免单金额=账单金额
					posSelfDao.insertPosBillPayment(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, paymentAmount, pay_count, paymentAmount, null, null, is_ysk, null, null, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batch_num, null,0d);

					String updateBillSql = new String("update pos_bill set free_amount=(free_amount+?),billfree_reason_id=? where bill_num = ? and store_id = ? and tenancy_id = ?");
					posSelfDao.update(updateBillSql, new Object[]
					{ paymentAmount, itemJson.optInt("reason_id"), billNum, storeId, tenancyId });

					posSelfDao.updateBillBatchForFreeAmountByBatchNum(tenancyId, storeId, billNum, batch_num, paymentAmount, itemJson.optInt("reason_id"));
					
//					String updateBillItemSql = new String("update pos_bill_item set item_remark=? where bill_num = ? and store_id = ? and tenancy_id = ? and batch_num =? and item_remark is null");
//					posSelfDao.update(updateBillItemSql, new Object[]
//					{ SysDictionary.ITEM_REMARK_MD03, billNum, storeId, tenancyId, batch_num });
					
					posSelfDao.updateBillItemForItemRemarkByBatchNum(tenancyId, storeId, billNum, batch_num, SysDictionary.ITEM_REMARK_MD03);

					// 写入付款日志表
					posSelfDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, paymentAmount, pay_count, paymentAmount, null, null, is_ysk, null, null, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batch_num, null,0d);
				}
				else if (SysDictionary.PAYMENT_CLASS_CASH.equals(paymentClass))
				{// 现金 cash
					double cashAmount = 0d;
					StringBuilder findPaymentSql = new StringBuilder("select amount from pos_bill_payment where tenancy_id=? and store_id = ? and bill_num = ? and batch_num=? and jzid = ?");
					rs = posSelfDao.query4SqlRowSet(findPaymentSql.toString(), new Object[]
					{ tenancyId, storeId, billNum, batch_num, jzid });
					if (rs.next())
					{
						cashAmount = rs.getDouble("amount");
					}

					if (cashAmount > 0)
					{
						cashAmount = DoubleHelper.add(cashAmount, pay_amount, PAYMENT_SCALE);
						double currencyAmount = DoubleHelper.mul(cashAmount, exchangeRate, PAYMENT_SCALE);
						StringBuilder updateBillPaymentSql = new StringBuilder("update pos_bill_payment set amount=?,count=?,report_date=?,shift_id=?,pos_num=?,cashier_num=?,last_updatetime=?,rate=?,currency_amount=? where store_id = ? and bill_num = ? and batch_num=? and jzid = ?");
						posSelfDao.update(updateBillPaymentSql.toString(), new Object[]
						{ cashAmount, 1, reportDate, shiftId, posNum, optNum, currentTime, exchangeRate, currencyAmount, storeId, billNum, batch_num, jzid });
					}
					else
					{
						double currencyAmount = DoubleHelper.mul(pay_amount, exchangeRate, PAYMENT_SCALE);
						posSelfDao.insertPosBillPayment(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currencyAmount, null, null, is_ysk, null, null, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batch_num, null,0d);
					}

					// 写入付款日志表
					double currencyAmount = DoubleHelper.mul(pay_amount, exchangeRate, PAYMENT_SCALE);
					posSelfDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currencyAmount, null, null, is_ysk, null, null, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batch_num, null,0d);
				}
				else if (SysDictionary.PAYMENT_CLASS_BANKCARD.equals(paymentClass))
				{// 银行卡 bankcard
					posSelfDao.insertPosBillPayment(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, null, bill_code, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batch_num, null,0d);
					
					posSelfDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, null, bill_code, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batch_num, null,0d);
				}
				else if (SysDictionary.PAYMENT_CLASS_CARD.equals(paymentClass))
				{// 本系统卡 card
					String card_code = number;
					if (Tools.isNullOrEmpty(card_code))
					{
						card_code = itemJson.optString("third_code");
					}
					Integer payId = posSelfDao.insertPosBillPayment(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, card_code, phone, is_ysk, null, null, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY, batch_num, itemJson.toString(),0d);

					JSONObject requestJson = new JSONObject();
					requestJson.put("card_code", number);
					requestJson.put("third_code", itemJson.optString("third_code"));
					requestJson.put("cardpassword", itemJson.optString("password"));
					requestJson.put("consume_totalmoney", paymentAmount);
					requestJson.put("consume_cardmoney", pay_amount);
					requestJson.put("consume_credit", itemJson.optDouble("consume_credit"));
					requestJson.put("consume_creditmoney", pay_amount);
					requestJson.put("chanel", chanel);
					requestJson.put("bill_code", billNum);
					requestJson.put("batch_num", batch_num);
					requestJson.put("updatetime", DateUtil.format(currentTime));
					requestJson.put("business_date", DateUtil.format(reportDate));
					requestJson.put("shift_id", shiftId);
					requestJson.put("operator_id", optNum);
					requestJson.put("operator", optName);
					requestJson.put("pos_num", posNum);
					
					List<JSONObject> requestList = new ArrayList<JSONObject>();
					requestList.add(requestJson);

					Data requestData = Data.get(param);
					requestData.setTenancy_id(tenancyId);
					requestData.setStore_id(storeId);
					requestData.setType(Type.CUSTOMER_CARD_CONSUME);
					requestData.setOper(Oper.add);
					requestData.setData(requestList);

					Data resData = Data.get();
					customerService.customerConsumePost(JSONObject.fromObject(requestData).toString(), resData);

					if (resData.isSuccess())
					{
						JSONObject responseJson = JSONObject.fromObject(resData.getData().get(0));
						// 返回余额,pad显示
						resultJson.put("credit", responseJson.optDouble("credit"));
						resultJson.put("main_balance", responseJson.optDouble("main_balance"));
						resultJson.put("reward_balance", responseJson.optDouble("reward_balance"));

						StringBuilder updatePaySql = new StringBuilder("update pos_bill_payment set number=?,bill_code=?,payment_state=? where store_id =? and id=?");
						posSelfDao.update(updatePaySql.toString(), new Object[]
						{ responseJson.optString("card_code"), responseJson.optString("bill_code"), SysDictionary.PAYMENT_STATE_PAY_COMPLETE, storeId, payId });

						posSelfDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, responseJson.optString("card_code"), phone, is_ysk, null, responseJson.optString("bill_code"), remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batch_num, null,0d);
						
						String printCode = "1010";
						String cardCode = responseJson.optString("card_code");
						String billCode = responseJson.optString("bill_code");
						String operator = responseJson.optString("operator");
						String updatetime = responseJson.optString("updatetime");
						double consume_cardmoney = responseJson.optDouble("consume_cardmoney");
						double main_trading = responseJson.optDouble("main_trading");
						double reward_trading = responseJson.optDouble("reward_trading");
						double main_balance = responseJson.optDouble("main_balance");
						double reward_balance = responseJson.optDouble("reward_balance");
						double total_main = responseJson.optDouble("total_main");
						double total_reward = responseJson.optDouble("total_reward");
						double credit = responseJson.optDouble("credit");
						double useful_credit = responseJson.optDouble("useful_credit");
						String card_class_name = responseJson.optString("card_class_name");
						String name = responseJson.optString("name");
						String mobil = responseJson.optString("mobil");
						double income = 0d;
						double deposit = 0d;
						double sales_price = 0d;

						posPrintService.customerPrint(tenancyId, storeId, posNum, operator, updatetime, printCode, cardCode, billCode, consume_cardmoney, main_trading, reward_trading, main_balance, reward_balance, total_main, total_reward, credit, useful_credit, income, deposit, sales_price,
								card_class_name, name, mobil);
					}
					else
					{
						if (99 == resData.getCode())
						{
							result.setCode(Constant.CODE_CONN_EXCEPTION);
							result.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
						}
						else
						{
//							String deletePaymentSql = new String("delete from pos_bill_payment where id=?");
//							posSelfDao.update(deletePaymentSql.toString(), new Object[]
//							{ payId });
							
							StringBuilder updatePaySql = new StringBuilder("update pos_bill_payment set payment_state=? where store_id =? and id=?");
							posSelfDao.update(updatePaySql.toString(), new Object[]
							{ SysDictionary.PAYMENT_STATE_PAY_FAILURE, storeId, payId });

							posSelfDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, null, null, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_FAILURE, batch_num, null,0d);
							
							this.getPaymentException(result, resData.getCode(), resData.getMsg());
						}
					}
				}
				else if (SysDictionary.PAYMENT_CLASS_COUPONS.equals(paymentClass))
				{
					itemJson.put("chanel", chanel);
					Integer payId = posSelfDao.insertPosBillPayment(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, null, null, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY, batch_num, itemJson.toString(),0d);
					
					JSONObject couponsJson = new JSONObject();
					couponsJson.put("coupons_code", number);

					List<JSONObject> couponsList = new ArrayList<JSONObject>();
					couponsList.add(couponsJson);

					JSONObject requestJson = new JSONObject();
					requestJson.put("chanel", chanel);
					requestJson.put("bill_money", paymentAmount);
					requestJson.put("bill_code", billNum);
					requestJson.put("updatetime", DateUtil.format(currentTime));
					requestJson.put("couponslist", couponsList);
					requestJson.put("business_date", DateUtil.format(reportDate));
					requestJson.put("shift_id", shiftId);
					requestJson.put("operator_id", optNum);
					requestJson.put("operator", optName);

					List<JSONObject> requestList = new ArrayList<JSONObject>();
					requestList.add(requestJson);

					Data requestData = Data.get(param);
					requestData.setTenancy_id(tenancyId);
					requestData.setStore_id(storeId);
					requestData.setType(Type.COUPONS);
					requestData.setOper(Oper.update);
					requestData.setData(requestList);

					Data resData = Data.get();
					customerService.commonPost(JSONObject.fromObject(requestData).toString(), resData);

					if (resData.isSuccess())
					{
						StringBuilder updatePaySql = new StringBuilder("update pos_bill_payment set payment_state=? where store_id =? and id=?");
						posSelfDao.update(updatePaySql.toString(), new Object[]
						{ SysDictionary.PAYMENT_STATE_PAY_COMPLETE, storeId, payId });

						posSelfDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, null, null, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batch_num, null,0d);
					}
					else
					{
						if (99 == resData.getCode())
						{
							result.setCode(Constant.CODE_CONN_EXCEPTION);
							result.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
						}
						else
						{
//							String deletePaymentSql = new String("delete from pos_bill_payment where id=?");
//							posSelfDao.update(deletePaymentSql.toString(), new Object[]
//							{ payId });
							
							StringBuilder updatePaySql = new StringBuilder("update pos_bill_payment set payment_state=? where store_id =? and id=?");
							posSelfDao.update(updatePaySql.toString(), new Object[]
							{ SysDictionary.PAYMENT_STATE_PAY_FAILURE, storeId, payId });

							posSelfDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, null, null, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_FAILURE, batch_num, null,0d);
							
							this.getPaymentException(result, resData.getCode(), resData.getMsg());
						}
					}
				}
				else if (SysDictionary.PAYMENT_CLASS_INCORPORATION.equals(paymentClass))
				{
					Integer payId = posSelfDao.insertPosBillPayment(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, customer_id, null, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY, batch_num, itemJson.toString(),0d);
					
					JSONObject requestJson = new JSONObject();
					requestJson.put("incorporation_id", number);
					requestJson.put("gz_money", pay_amount);
					requestJson.put("bill_code", billNum);
					requestJson.put("bill_money", paymentAmount);
					requestJson.put("customer_id", customer_id);
					requestJson.put("password", itemJson.optString("password"));
					requestJson.put("operate_time", DateUtil.format(currentTime));
					requestJson.put("business_date", DateUtil.format(reportDate));
					requestJson.put("shift_id", shiftId);
					requestJson.put("operator_id", optNum);
					requestJson.put("operator", optName);
					List<JSONObject> requestList = new ArrayList<JSONObject>();
					requestList.add(requestJson);

					Data requestData = Data.get(param);
					requestData.setTenancy_id(tenancyId);
					requestData.setStore_id(storeId);
					requestData.setType(Type.INCORPORATION_GZ);
					requestData.setOper(Oper.add);
					requestData.setData(requestList);

					Data resData = Data.get();
					customerService.commonPost(JSONObject.fromObject(requestData).toString(), resData);

					if (resData.isSuccess())
					{
						StringBuilder updatePaySql = new StringBuilder("update pos_bill_payment set payment_state=? where store_id =? and id=?");
						posSelfDao.update(updatePaySql.toString(), new Object[]
						{ SysDictionary.PAYMENT_STATE_PAY_COMPLETE, storeId, payId });

						posSelfDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, customer_id, null, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batch_num, null,0d);
					}
					else
					{
						if (99 == resData.getCode())
						{
							result.setCode(Constant.CODE_CONN_EXCEPTION);
							result.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
						}
						else
						{
//							String deletePaymentSql = new String("delete from pos_bill_payment where id=?");
//							posSelfDao.update(deletePaymentSql.toString(), new Object[]
//							{ payId });
							
							StringBuilder updatePaySql = new StringBuilder("update pos_bill_payment set payment_state=? where store_id =? and id=?");
							posSelfDao.update(updatePaySql.toString(), new Object[]
							{ SysDictionary.PAYMENT_STATE_PAY_FAILURE, storeId, payId });

							posSelfDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, customer_id, null, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_FAILURE, batch_num, null,0d);

							this.getPaymentException(result, resData.getCode(), resData.getMsg());
						}
					}
				}
				else if (SysDictionary.PAYMENT_CLASS_WECHAT_PAY.equals(paymentClass))
				{// 微信支付 wechat_pay
					Integer payId = posSelfDao.insertPosBillPayment(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, null, null, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY, batch_num, null,0d);
					
					JSONObject requestJson = new JSONObject();
					requestJson.put("bill_num", billNum);
					requestJson.put("pos_num", posNum);
					requestJson.put("opt_num", optNum);
					requestJson.put("report_date", reportDate);
					requestJson.put("shift_id", shiftId);
					requestJson.put("jzid", jzid);
					requestJson.put("polling_flag", false);

					List<JSONObject> requestList = new ArrayList<JSONObject>();
					requestList.add(requestJson);

					Data requestData = Data.get(param);
					requestData.setTenancy_id(tenancyId);
					requestData.setStore_id(storeId);
					requestData.setType(Type.PAYMENT_ORDERQUERY);
					requestData.setOper(Oper.query);
					requestData.setData(requestList);

					List<JSONObject> responseList = paymentService.orderquery(requestData, "");
					JSONObject responseJson = JSONObject.fromObject(responseList.get(0));

					if (responseJson.optBoolean("success") && "finished".equals(responseJson.optString("status")))
					{
						StringBuilder updatePaySql = new StringBuilder("update pos_bill_payment set number=?,bill_code=?,payment_state=? where store_id =? and id=?");
						posSelfDao.update(updatePaySql.toString(), new Object[]
						{ responseJson.optString("buyer_logon_id"), responseJson.optString("trade_no"), SysDictionary.PAYMENT_STATE_PAY_COMPLETE, storeId, payId });

						posSelfDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, responseJson.optString("buyer_logon_id"), phone, is_ysk, null, responseJson.optString("trade_no"), remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batch_num, null,0d);
					}
					else if (!responseJson.optBoolean("success"))
					{
						result.setCode(Constant.CODE_CONN_EXCEPTION);
						result.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
					}
					else
					{
//						String deletePaymentSql = new String("delete from pos_bill_payment where id=?");
//						posSelfDao.update(deletePaymentSql.toString(), new Object[]
//						{ payId });
						
						StringBuilder updatePaySql = new StringBuilder("update pos_bill_payment set payment_state=? where store_id =? and id=?");
						posSelfDao.update(updatePaySql.toString(), new Object[]
						{ SysDictionary.PAYMENT_STATE_PAY_FAILURE, storeId, payId });

						posSelfDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, responseJson.optString("buyer_logon_id"), phone, is_ysk, null, responseJson.optString("trade_no"), remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_FAILURE, batch_num, null,0d);
						
						this.getPaymentException(result, responseJson.optInt("sub_code"), responseJson.optString("sub_msg"));
					}
				}
				else if (SysDictionary.PAYMENT_DDM_QUICK_PAY.equals(paymentClass) || SysDictionary.PAYMENT_CLASS_ALI_PAY.equals(paymentClass))
				{// 支付宝支付 ali_pay
					Integer payId = posSelfDao.insertPosBillPayment(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, null, null, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY, batch_num, null,0d);
					
					JSONObject requestJson = new JSONObject();
					requestJson.put("bill_num", billNum);
					requestJson.put("pos_num", posNum);
					requestJson.put("opt_num", optNum);
					requestJson.put("report_date", reportDate);
					requestJson.put("shift_id", shiftId);
					requestJson.put("jzid", jzid);
					requestJson.put("polling_flag", false);

					List<JSONObject> requestList = new ArrayList<JSONObject>();
					requestList.add(requestJson);

					Data requestData = Data.get(param);
					requestData.setTenancy_id(tenancyId);
					requestData.setStore_id(storeId);
					requestData.setType(Type.PAYMENT_QUERY);
					requestData.setOper(Oper.query);
					requestData.setData(requestList);

					List<JSONObject> responseList = paymentService.query(requestData, "");
					JSONObject responseJson = JSONObject.fromObject(responseList.get(0));

					if (responseJson.optBoolean("success") && "finished".equals(responseJson.optString("status")))
					{
						StringBuilder updatePaySql = new StringBuilder("update pos_bill_payment set number=?,bill_code=?,payment_state=? where store_id =? and id=?");
						posSelfDao.update(updatePaySql.toString(), new Object[]
						{ responseJson.optString("buyer_logon_id"), responseJson.optString("trade_no"), SysDictionary.PAYMENT_STATE_PAY_COMPLETE, storeId, payId });

						posSelfDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, responseJson.optString("buyer_logon_id"), phone, is_ysk, null, responseJson.optString("trade_no"), remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batch_num, null,0d);
						
					}
					else if (!responseJson.optBoolean("success"))
					{
						result.setCode(Constant.CODE_CONN_EXCEPTION);
						result.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
					}
					else
					{
//						String deletePaymentSql = new String("delete from pos_bill_payment where id=?");
//						posSelfDao.update(deletePaymentSql.toString(), new Object[]
//						{ payId });
						
						StringBuilder updatePaySql = new StringBuilder("update pos_bill_payment set payment_state=? where store_id =? and id=?");
						posSelfDao.update(updatePaySql.toString(), new Object[]
						{ SysDictionary.PAYMENT_STATE_PAY_FAILURE, storeId, payId });

						posSelfDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, responseJson.optString("buyer_logon_id"), phone, is_ysk, null, responseJson.optString("trade_no"), remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_FAILURE, batch_num, null,0d);
						
						this.getPaymentException(result, responseJson.optInt("sub_code"), responseJson.optString("sub_msg"));
					}
				}
				else
				{
					posSelfDao.insertPosBillPayment(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, customer_id, bill_code, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batch_num, null,0d);
					
					posSelfDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, customer_id, bill_code, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batch_num, null,0d);
				}
			}
			else
			{
				double amount = paymentAmount > 0 ? pay_amount : paymentAmount;
				posSelfDao.insertPosBillPayment(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, amount, pay_count, amount, number, phone, is_ysk, customer_id, bill_code, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batch_num, null,0d);
				
				posSelfDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, customer_id, bill_code, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batch_num, null,0d);
			}
		}
		
		this.completePayment(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentAmount, batch_num, isprint_bill, currentTime, printJson);

		List<JSONObject> resultList = new ArrayList<JSONObject>();
		resultList.add(resultJson);

		result.setData(resultList);
		if (0 == result.getCode())
		{
			result.setCode(Constant.CODE_SUCCESS);
			result.setMsg(Constant.PAYMENT_SUCCESS);
		}

		// 写日志pos_log
		posSelfDao.savePosLog(tenancyId, storeId, posNum, optNum, optName, shiftId, reportDate, Constant.TITLE, "结账", "账单编号:" + billNum + "; 应付金额:" + String.valueOf(paymentAmount), newstate.toString());

	}


	@Override
	public void clearBillItemSelf(Data param, Data result) throws Exception
	{
		String tenancyId = param.getTenancy_id();
		Integer storeId = param.getStore_id();

		Map<String, Object> map = ReqDataUtil.getDataMap(param);

		String posNum = ParamUtil.getStringValue(map, "pos_num", false, null);
		String optNum = ParamUtil.getStringValue(map, "opt_num", false, null);
		String billNum = ParamUtil.getStringValue(map, "bill_num", true, PosErrorCode.NOT_NULL_BILL_NUM);
		try
		{
			Date reportDate = posSelfDao.getReportDate(tenancyId, storeId);
			if (SysDictionary.SOURCE_ANDROID_PAD.equals(param.getSource()))
			{
				optNum = posSelfDao.getBindOptNum(tenancyId, storeId, reportDate, posNum);
			}

			int shiftId = 0;
			if (SysDictionary.SOURCE_ANDROID_PAD.equals(param.getSource()))
			{
				StringBuilder queryDevicesSql = new StringBuilder("select pos_num from pos_opt_state_devices where tenancy_id=? and store_id=? and devices_num=? and is_valid='1'");
				SqlRowSet rsd = posSelfDao.query4SqlRowSet(queryDevicesSql.toString(), new Object[]
				{ tenancyId, storeId, posNum });
				if (rsd.next())
				{
					shiftId = posSelfDao.getShiftId(tenancyId, storeId, reportDate, optNum, rsd.getString("pos_num"));
				}
			}
			else
			{
				shiftId = posSelfDao.getShiftId(tenancyId, storeId, reportDate, optNum, posNum);
			}

			StringBuilder queryBillSql = new StringBuilder("select batch_num from pos_bill where tenancy_id=? and store_id=? and bill_num=?");
			SqlRowSet rs = posSelfDao.query4SqlRowSet(queryBillSql.toString(), new Object[]
			{ tenancyId, storeId, billNum });
			String batch_num = "";
			if (rs.next())
			{
				batch_num = rs.getString("batch_num");
			}

			StringBuilder qureyPaySql = new StringBuilder("select count(*) from public.pos_bill_payment where tenancy_id=? and store_id=? and bill_num=? and batch_num=?");
			int payCount = posSelfDao.queryForInt(qureyPaySql.toString(), new Object[]
			{ tenancyId, storeId, billNum, batch_num });
			if (payCount > 0)
			{
				throw SystemException.getInstance(PosErrorCode.BILL_ALREADY_PAY_AMOUNT_ERROR);
			}

			StringBuilder querySql = new StringBuilder();
			querySql.append("select bi.item_id,bi.item_count,ps.id,ps.num,ps.item_unit_id,ps.setdate from pos_bill_item bi ");
			querySql.append("left join pos_soldout ps on bi.tenancy_id=ps.tenancy_id and bi.store_id=ps.store_id and bi.item_id=ps.item_id ");
			querySql.append("where ps.id is not null and bi.tenancy_id=? and bi.store_id=? and bi.bill_num=? and bi.batch_num=? ");

			rs = posSelfDao.query4SqlRowSet(querySql.toString(), new Object[]
			{ tenancyId, storeId, billNum, batch_num });

			List<JSONObject> soldOutList = new ArrayList<JSONObject>();
			while (rs.next())
			{
				boolean isNew = true;
				for (JSONObject soldOutJson : soldOutList)
				{
					if (rs.getString("item_id").equals(soldOutJson.optString("item_id")))
					{
						double count = DoubleHelper.add(soldOutJson.optDouble("num"), rs.getDouble("item_count"), 4);
						soldOutJson.put("num", count);
						isNew = false;
					}
				}

				if (isNew)
				{
					double count = DoubleHelper.add(rs.getDouble("item_count"), rs.getDouble("num"), 4);
					// list.add(new
					// Object[]{count,rs.getInt("id"),rs.getInt("item_id")});
					JSONObject soldOutJson = new JSONObject();
					soldOutJson.put("id", rs.getInt("id"));
					soldOutJson.put("item_id", rs.getInt("item_id"));
					soldOutJson.put("item_unit_id", rs.getInt("item_unit_id"));
					soldOutJson.put("setdate", rs.getString("setdate"));
					soldOutJson.put("num", count);
					soldOutList.add(soldOutJson);
				}
			}

			List<Object[]> list = new ArrayList<Object[]>();
			for (JSONObject soldOutJson : soldOutList)
			{
				list.add(new Object[]
				{ soldOutJson.optDouble("num"), soldOutJson.optInt("id"), soldOutJson.optInt("item_id") });
			}
			StringBuilder updateSql = new StringBuilder("update pos_soldout set num = ? where id=? and item_id=?");
			posSelfDao.batchUpdate(updateSql.toString(), list);

			String dzfi = new String("delete from pos_zfkw_item  where  rwid in (select rwid from pos_bill_item where batch_num=? and bill_num = ? and store_id = ? and tenancy_id = ?)");
			posSelfDao.update(dzfi, new Object[]
			{ batch_num, billNum, storeId, tenancyId });

			String dbi = new String("delete from pos_bill_item where batch_num=? and bill_num = ? and store_id = ? and tenancy_id = ?");
			posSelfDao.update(dbi, new Object[]
			{ batch_num, billNum, storeId, tenancyId });

			// String ubillNum = new
			// String("update pos_bill set service_amount=0,service_discount=0,subtotal=0,bill_amount=0,payment_amount=0,difference=0,discountk_amount=0,discountr_amount=0,maling_amount=0,single_discount_amount=0,discount_amount=0,free_amount=0,givi_amount=0,more_coupon=0,average_amount=0,discount_case_id=0,discount_rate=100,guest_msg=null,remark=null where bill_num = ? and store_id = ?");
			// posSelfDao.update(ubillNum,new Object[]{billNum,storeId});

			this.calcAmount(tenancyId, storeId, billNum);

			posSelfDao.savePosLog(tenancyId, storeId, posNum, optNum, optNum, shiftId, reportDate, Constant.TITLE, "清除账单明细", "账单编号:" + billNum, "");

			result.setCode(Constant.CODE_SUCCESS);
			result.setMsg(Constant.CLEAR_BILl_ITEM_SUCCESS);

			if (soldOutList.size() > 0)
			{
				try
				{
					Data cjData = new Data();
					cjData.setType(Type.SOLD_OUT);
					cjData.setOper(Oper.update);
					cjData.setData(soldOutList);

					Comet4jUtil.sendMessage2All(Comet4jUtil.POS_ANDROID, JSONObject.fromObject(cjData).toString());
				}
				catch (Exception e)
				{
					e.printStackTrace();
					logger.error("推送失败::" + e.getMessage());
				}
			}
		}
		catch (Exception se)
		{
			result.setCode(Constant.CODE_INNER_EXCEPTION);
			result.setMsg(Constant.CLEAR_BILL_ITEM_FAILURE);
		}
	}

	@Override
	public void clearPaymentSelf(Data param, Data result) throws Exception
	{
		String tenancyId = param.getTenancy_id();
		Integer storeId = param.getStore_id();
		String source = param.getSource();
		
		Map<String, Object> map = ReqDataUtil.getDataMap(param);
		
		JSONObject para = JSONObject.fromObject(map);
		Date reportDate = getReportDate(tenancyId, storeId, para);
		String optNum = getOptNum(tenancyId, storeId, source, reportDate, para);
		Integer shiftId = getShiftId(tenancyId, storeId, source, reportDate, optNum, para);
		
		String mode = ParamUtil.getStringValue(map, "mode", true, PosErrorCode.NOT_NULL_MODE); // 0:全单清除
		String billno = ParamUtil.getStringValue(map, "bill_num", true, PosErrorCode.NOT_NULL_BILL_NUM);
		String posNum = ParamUtil.getStringValue(map, "pos_num", false, null);
	
		Timestamp currentTime = DateUtil.currentTimestamp();

		StringBuilder queryBillSql = new StringBuilder(
				"select b.batch_num,b.source,b.payment_amount,coalesce(sum(p.currency_amount),0) as pay_amount from pos_bill b left join pos_bill_payment p on b.store_id=p.store_id and b.bill_num=p.bill_num where b.tenancy_id=? and b.store_id=? and b.bill_num=? group by b.bill_num,b.batch_num,b.source,b.payment_amount");
		SqlRowSet rs = posSelfDao.query4SqlRowSet(queryBillSql.toString(), new Object[]
		{ tenancyId, storeId, billno });
		String batchNum = "";
		double paymentAmount = 0d;
		double payAmount = 0d;
		if (rs.next())
		{
			batchNum = rs.getString("batch_num");
			paymentAmount = rs.getDouble("payment_amount");
			payAmount = rs.getDouble("pay_amount");
		}

		List<JSONObject> payList = new ArrayList<JSONObject>();
		StringBuilder queryPaySql = new StringBuilder("select p.*,w.payment_class,w.is_check from pos_bill_payment p left join payment_way w on p.jzid=w.id where p.tenancy_id=? and p.store_id=? and p.bill_num=?");
		if ("1".equals(mode))
		{
			queryPaySql.append(" and p.id=?");
			Integer id = ParamUtil.getIntegerValue(map, "id", true, PosErrorCode.NOT_NULL_ID);
			payList = posSelfDao.query4Json(tenancyId, queryPaySql.toString(), new Object[]
			{ tenancyId, storeId, billno, id });
		}
		else
		{
			Object[] objs = null;
			if (Tools.hv(batchNum))
			{
				queryPaySql.append(" and p.batch_num=?");
				if (paymentAmount > payAmount)
				{
					objs = new Object[]
					{ tenancyId, storeId, billno, batchNum };
				}
				else
				{
					objs = new Object[]
					{ tenancyId, storeId, billno, "" };
				}
			}
			else
			{
				objs = new Object[]
				{ tenancyId, storeId, billno };
			}
			payList = posSelfDao.query4Json(tenancyId, queryPaySql.toString(), objs);
		}

		for(JSONObject payJson:payList)
		{
			posSelfDao.deletePosBillPaymentById(tenancyId, storeId, payJson.optInt("id"));
			
			String tableCode =  payJson.optString("table_code");
			String type=  payJson.optString("type");
			int jzid =payJson.optInt("jzid");
			String name=  payJson.optString("name"); 
			String nameEnglish=  payJson.optString("name_english"); 
			double rate =payJson.optDouble("rate");
			double amount =payJson.optDouble("amount");
			double count =payJson.optDouble("count");
			double currencyAmount = payJson.optDouble("currency_amount");
			String number=  payJson.optString("number"); 
			String phone=  payJson.optString("phone"); 
			String isYsk=  payJson.optString("is_ysk"); 
			int customerId = payJson.optInt("customer_id");
			String billCode=  payJson.optString("bill_code");
			String paymentState=  payJson.optString("payment_state");
			
			posSelfDao.insertPosBillPaymentLog(tenancyId, storeId, billno, reportDate, shiftId, posNum, optNum, tableCode, type, jzid, name, nameEnglish, rate, amount, count, currencyAmount, number, phone, isYsk, customerId, billCode, "清除付款", currentTime, paymentState, batchNum, null,0d);
		}
		
		this.updatePosBillBatch(tenancyId, storeId, billno, batchNum);
		
		this.updatePosBill(tenancyId, storeId, billno);
		
		posSelfDao.savePosLog(tenancyId, storeId, posNum, optNum, optNum, shiftId, reportDate, Constant.TITLE, "清除付款记录", "账单编号:" + billno, "");
		result.setCode(Constant.CODE_SUCCESS);
		result.setMsg(Constant.CLEAR_PAYMENT_SUCCESS);
	}

//	@Override
	public void clearPaymentSelf_self(Data param, Data result) throws Exception
	{
		String tenancyId = param.getTenancy_id();
		Integer storeId = param.getStore_id();
		String source = param.getSource();
		
		Map<String, Object> map = ReqDataUtil.getDataMap(param);
		
		JSONObject para = JSONObject.fromObject(map);
		Date reportDate = getReportDate(tenancyId, storeId, para);
		String optNum = getOptNum(tenancyId, storeId, source, reportDate, para);
		Integer shiftId = getShiftId(tenancyId, storeId, source, reportDate, optNum, para);
		
		String mode = ParamUtil.getStringValue(map, "mode", true, PosErrorCode.NOT_NULL_MODE); // 0:全单清除
		String billno = ParamUtil.getStringValue(map, "bill_num", true, PosErrorCode.NOT_NULL_BILL_NUM);
		String posNum = ParamUtil.getStringValue(map, "pos_num", false, null);
	
		String optName = posSelfDao.getEmpNameById(optNum, tenancyId, storeId);
		String currentTime = DateUtil.getNowDateYYDDMMHHMMSS();

		StringBuilder queryBillSql = new StringBuilder(
				"select b.batch_num,b.source,b.payment_amount,coalesce(sum(p.currency_amount),0) as pay_amount from pos_bill b left join pos_bill_payment p on b.store_id=p.store_id and b.bill_num=p.bill_num where b.tenancy_id=? and b.store_id=? and b.bill_num=? group by b.bill_num,b.batch_num,b.source,b.payment_amount");
		SqlRowSet rs = posSelfDao.query4SqlRowSet(queryBillSql.toString(), new Object[]
		{ tenancyId, storeId, billno });
		String batchNum = "";
		String chanel = "";
		double paymentAmount = 0d;
		double payAmount = 0d;
		if (rs.next())
		{
			batchNum = rs.getString("batch_num");
			chanel = rs.getString("source");
			paymentAmount = rs.getDouble("payment_amount");
			payAmount = rs.getDouble("pay_amount");
		}

		List<JSONObject> payList = new ArrayList<JSONObject>();
		StringBuilder queryPaySql = new StringBuilder("select p.*,w.payment_class,w.is_check from pos_bill_payment p left join payment_way w on p.jzid=w.id where p.tenancy_id=? and p.store_id=? and p.bill_num=?");
		if ("1".equals(mode))
		{
			queryPaySql.append(" and p.id=?");
			Integer id = ParamUtil.getIntegerValue(map, "id", true, PosErrorCode.NOT_NULL_ID);
			payList = posSelfDao.query4Json(tenancyId, queryPaySql.toString(), new Object[]
			{ tenancyId, storeId, billno, id });
		}
		else
		{
			Object[] objs = null;
			if (Tools.hv(batchNum))
			{
				queryPaySql.append(" and p.batch_num=?");
				if (paymentAmount > payAmount)
				{
					objs = new Object[]
					{ tenancyId, storeId, billno, batchNum };
				}
				else
				{
					objs = new Object[]
					{ tenancyId, storeId, billno, "" };
				}
			}
			else
			{
				objs = new Object[]
				{ tenancyId, storeId, billno };
			}
			payList = posSelfDao.query4Json(tenancyId, queryPaySql.toString(), objs);
		}

		if (payList.size() > 0)
		{
			try
			{
				List<Object[]> deleteList = new ArrayList<Object[]>();
				for (JSONObject pay : payList)
				{
					String paymentClass = pay.optString("payment_class");
					if (SysDictionary.PAYMENT_CLASS_CARD.equalsIgnoreCase(paymentClass))
					{
						JSONObject requestJson = new JSONObject();
						requestJson.put("card_code", pay.optString("number"));
						// requestJson.put("third_code",
						// paraJson.optString("third_code"));
						requestJson.put("old_bill_code", pay.optString("bill_code"));
						requestJson.put("bill_code", billno);
						requestJson.put("chanel", chanel);
						requestJson.put("updatetime", currentTime);
						requestJson.put("batch_num", batchNum);
						requestJson.put("business_date", DateUtil.format(reportDate));
						requestJson.put("shift_id", shiftId);
						requestJson.put("operator_id", optNum);
						requestJson.put("operator", optName);
						requestJson.put("pos_num", posNum);

						List<JSONObject> requestList = new ArrayList<JSONObject>();
						requestList.add(requestJson);

						Data requestData = Data.get(tenancyId, storeId, 0);
						requestData.setType(Type.CUSTOMER_CARD_CONSUME);
						requestData.setOper(Oper.update);
						requestData.setData(requestList);

						Data responseData = Data.get(requestData);
						customerService.cancelCardConsume(requestData, responseData,"N");

						if (responseData.isSuccess())
						{
							JSONObject responseJson = JSONObject.fromObject(responseData.getData().get(0));
							try
							{
								String printCode = "1011";
								String cardCode = responseJson.optString("card_code");
								String billCode = responseJson.optString("bill_code");
								String operator = responseJson.optString("operator");
								String updatetime = responseJson.optString("updatetime");
								double consume_cardmoney = 0d;
								double main_trading = responseJson.optDouble("main_trading");
								double reward_trading = responseJson.optDouble("reward_trading");
								double main_balance = responseJson.optDouble("main_balance");
								double reward_balance = responseJson.optDouble("reward_balance");
								double total_main = 0d;
								double total_reward = 0d;
								double credit = responseJson.optDouble("credit");
								double useful_credit = responseJson.optDouble("useful_credit");
								String card_class_name = responseJson.optString("card_class_name");
								String name = responseJson.optString("name");
								String mobil = responseJson.optString("mobil");
								double income = 0d;
								double deposit = 0d;
								double sales_price = 0d;

								posPrintService.customerPrint(tenancyId, storeId, posNum, operator, updatetime, printCode, cardCode, billCode, consume_cardmoney, main_trading, reward_trading, main_balance, reward_balance, total_main, total_reward, credit, useful_credit, income, deposit,
										sales_price, card_class_name, name, mobil);
							}
							catch (Exception e)
							{
								e.printStackTrace();
								logger.info("打印失败", e);
							}
							deleteList.add(new Object[]
							{ pay.optInt("id") });
						}
					}
					else if (SysDictionary.PAYMENT_CLASS_COUPONS.equalsIgnoreCase(paymentClass))
					{
						if ("1".equals(pay.optString("is_check")))
						{
							JSONObject coupons = new JSONObject();
							coupons.put("coupons_code", pay.optString("number"));
							List<JSONObject> couponsList = new ArrayList<JSONObject>();
							couponsList.add(coupons);

							JSONObject paraJson = new JSONObject();
							paraJson.put("chanel", chanel);
							paraJson.put("couponslist", couponsList);
							List<JSONObject> requestList = new ArrayList<JSONObject>();
							requestList.add(paraJson);

							Data requestData = Data.get(tenancyId, storeId, 0);
							requestData.setType(Type.COUPONS);
							requestData.setOper(Oper.init);
							requestData.setData(requestList);

							Data responseData = Data.get(requestData);
							customerService.commonPost(JSONObject.fromObject(requestData).toString(), responseData);
						}
						deleteList.add(new Object[]
						{ pay.optInt("id") });
					}
					else if (SysDictionary.PAYMENT_CLASS_INCORPORATION.equalsIgnoreCase(paymentClass))
					{
						JSONObject paraJson = new JSONObject();
						paraJson.put("incorporation_id", pay.optString("number"));
						paraJson.put("gz_money", pay.optDouble("amount"));
						paraJson.put("bill_code", billno);
						paraJson.put("bill_money", paymentAmount);
						paraJson.put("customer_id", pay.optString("customer_id"));
						paraJson.put("operate_time", currentTime);
						paraJson.put("operator", optNum);

						List<JSONObject> requestList = new ArrayList<JSONObject>();
						requestList.add(paraJson);

						Data requestData = Data.get(tenancyId, storeId, 0);
						requestData.setType(Type.INCORPORATION_GZ);
						requestData.setOper(Oper.update);
						requestData.setData(requestList);

						Data responseData = Data.get(requestData);
						customerService.commonPost(JSONObject.fromObject(requestData).toString(), responseData);

						deleteList.add(new Object[]
						{ pay.optInt("id") });
					}
					else if (SysDictionary.PAYMENT_DDM_QUICK_PAY.equals(paymentClass) || SysDictionary.PAYMENT_CLASS_ALI_PAY.equalsIgnoreCase(paymentClass))
					{
						// JSONObject paraJson = new JSONObject();
						// paraJson.put("incorporation_id",
						// pay.optString("number"));
						// paraJson.put("gz_money", pay.optDouble("amount"));
						// paraJson.put("bill_code", billno);
						// paraJson.put("bill_money", paymentAmount);
						// paraJson.put("customer_id",
						// pay.optString("customer_id"));
						// paraJson.put("operate_time", currentTime);
						// paraJson.put("operator", optNum);
						//
						// List<JSONObject> requestList = new
						// ArrayList<JSONObject>();
						// requestList.add(paraJson);
						//
						// Data requestData = Data.get(tenancyId, storeId, 0);
						// requestData.setType(Type.PAYMENT_CANCLE);
						// requestData.setOper(Oper.cancle);
						// requestData.setData(requestList);
						//
						// paymentService.cancle(requestData, "");

						deleteList.add(new Object[]
						{ pay.optInt("id") });
					}
					else if (SysDictionary.PAYMENT_CLASS_WECHAT_PAY.equalsIgnoreCase(paymentClass))
					{
						// JSONObject paraJson = new JSONObject();
						// paraJson.put("incorporation_id",
						// pay.optString("number"));
						// paraJson.put("gz_money", pay.optDouble("amount"));
						// paraJson.put("bill_code", billno);
						// paraJson.put("bill_money", paymentAmount);
						// paraJson.put("customer_id",
						// pay.optString("customer_id"));
						// paraJson.put("operate_time", currentTime);
						// paraJson.put("operator", optNum);
						//
						// List<JSONObject> requestList = new
						// ArrayList<JSONObject>();
						// requestList.add(paraJson);
						//
						// Data requestData = Data.get(tenancyId, storeId, 0);
						// requestData.setType(Type.PAYMENT_CLOSEORDER);
						// requestData.setOper(Oper.update);
						// requestData.setData(requestList);
						//
						// paymentService.closeorder(requestData, "");

						deleteList.add(new Object[]
						{ pay.optInt("id") });
					}
					else if (SysDictionary.PAYMENT_CLASS_FREESINGLE.equalsIgnoreCase(paymentClass))
					{
						StringBuilder updateBillSql = new StringBuilder("update pos_bill set free_amount=0,billfree_reason_id=null where tenancy_id=? and store_id=? and bill_num=?");
						posSelfDao.update(updateBillSql.toString(), new Object[]
						{ tenancyId, storeId, billno });
						deleteList.add(new Object[]
						{ pay.optInt("id") });
					}
					else
					{
						deleteList.add(new Object[]
						{ pay.optInt("id") });
					}
				}

				if (deleteList.size() > 0)
				{
					StringBuilder deletePaySql = new StringBuilder("delete from pos_bill_payment where id = ?");
					posSelfDao.batchUpdate(deletePaySql.toString(), deleteList);
					
					//修改
				}
				posSelfDao.savePosLog(tenancyId, storeId, posNum, optNum, optNum, shiftId, reportDate, Constant.TITLE, "清除付款记录", "账单编号:" + billno, "");
				result.setCode(Constant.CODE_SUCCESS);
				result.setMsg(Constant.CLEAR_PAYMENT_SUCCESS);
			}
			catch (Exception e)
			{
				e.printStackTrace();
				logger.info("CLEAR_PAYMENT_FAILURE", e);
				throw new SystemException(PosErrorCode.CLEAR_PAYMENT_FAILURE);
			}
		}
	}

	@Override
	public void closeTable(Data param, Data result) throws Exception
	{
		String tenancyId = param.getTenancy_id();
		Integer storeId = param.getStore_id();
		String source = param.getSource();

		Map<String, Object> map = ReqDataUtil.getDataMap(param);

		JSONObject para = JSONObject.fromObject(map);
		Date reportDate = getReportDate(tenancyId, storeId, para);
		String optNum = getOptNum(tenancyId, storeId, source, reportDate, para);
		Integer shiftId = getShiftId(tenancyId, storeId, source, reportDate, optNum, para);

		String tableCode = ParamUtil.getStringValue(map, "table_code", false, null);
		String posNum = ParamUtil.getStringValue(map, "pos_num", false, null);

		// Timestamp currentTime = DateUtil.currentTimestamp();
		// 付款完成验证
		
		JSONObject billJson = posSelfDao.queryPosBillForOpenByTablecode(tenancyId, storeId, tableCode);
		
		if(Tools.hv(billJson))
		{
			String billNum = billJson.optString("bill_num");

			List<JSONObject> lists = posSelfDao.getPosBillBatchListByBillnum(tenancyId, storeId, billNum);
			String batchNum = "";
	//		String paymentState = "";
			if(lists.size() > 0)
			{
				for(JSONObject jo : lists)
				{
					batchNum = jo.optString("batch_num");
	//				paymentState = jo.optString("payment_state");
					
					if(jo.optDouble("bill_amount") > 0)
					{
	//					if(SysDictionary.PAYMENT_STATE_PAY.equals(paymentState))
	//					{
							throw SystemException.getInstance(PosErrorCode.BILL_AMOUNT_MORE_PAY_AMOUNT_ERROR);
	//					}
					}
					else
					{
						StringBuilder updateBillBatchSql = new StringBuilder("update pos_bill_batch set bill_property =?,payment_state=?,payment_time =?,pos_num =?,cashier_num =? where tenancy_id=? and store_id = ? and bill_num =? and batch_num=?");
						posSelfDao.update(updateBillBatchSql.toString(), new Object[]
						{ SysDictionary.BILL_PROPERTY_CLOSED, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, DateUtil.currentTimestamp(), posNum, optNum, tenancyId, storeId, billNum, batchNum });
					}
				}
			}
	
			// 关闭账单
			posSelfDao.closeBill(tenancyId, storeId, billNum);
			
			// 更新桌位状态表
			posSelfDao.updateTableState(tenancyId, storeId, tableCode);
	
			posSelfDao.billPrint("0", tenancyId, storeId, billNum, posNum, optNum, reportDate, shiftId, "1002", "0", "", "", "0", "", "","","","");
	
			posSelfDao.savePosLog(tenancyId, storeId, posNum, optNum, optNum, shiftId, reportDate, Constant.TITLE, "关闭桌位", "桌位编号:" + tableCode, "账单编号:" + billNum);
		}
		else
		{
			posSelfDao.updateTableState(tenancyId, storeId, tableCode);
			posSelfDao.savePosLog(tenancyId, storeId, posNum, optNum, optNum, shiftId, reportDate, Constant.TITLE, "关闭桌位", "桌位编号:" + tableCode, "账单已关闭" );
		}
		result.setCode(Constant.CODE_SUCCESS);
		result.setMsg(Constant.CLOSE_TABLE_SUCCESS);
	}

	@Override
	public void retreatFood(String tenancyId, int storeId, List<?> param, Data result, JSONObject printJson, String source) throws Exception
	{
		if (param == null || param.isEmpty())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}
		JSONObject para = JSONObject.fromObject(param.get(0));
		if (Tools.isNullOrEmpty(para.opt("item")))
		{
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_ITEM_LIST);
		}

		Date reportDate = getReportDate(tenancyId, storeId, para);
		String optNum = getOptNum(tenancyId, storeId, source, reportDate, para);
		Integer shiftId = getShiftId(tenancyId, storeId, source, reportDate, optNum, para);
		String billNum = para.optString("bill_num");
		String posNum = para.optString("pos_num");
		String optName = posSelfDao.getEmpNameById(optNum, tenancyId, storeId);
		Timestamp currentTime = DateUtil.currentTimestamp();

		posSelfDao.checkKssy(reportDate, optNum, storeId,shiftId,posNum);
		
		StringBuilder rwids = new StringBuilder();
		Map<String, JSONObject> rwidMap = new HashMap<String, JSONObject>();
		for (Object obj : para.optJSONArray("item"))
		{
			JSONObject itemJson = JSONObject.fromObject(obj);
			if (rwids.length() > 0)
			{
				rwids.append(",");
			}
			rwids.append(itemJson.optInt("rwid"));
			rwidMap.put(itemJson.optString("rwid"), itemJson);
		}

		if (0 == rwids.length())
		{
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_RWID);
		}

		//String agent = "";
		if (SysDictionary.SOURCE_MOVE.contains(source)||SysDictionary.SOURCE_ORDER_DEVICE.equals(source))
		{
			//agent = "android";
			if (rwids.length() > 0)
			{
				StringBuilder queryItemSql = new StringBuilder("select count(*) from pos_bill_item bi inner join pos_bill_payment bp on bi.tenancy_id=bp.tenancy_id and bi.store_id=bp.store_id and bi.bill_num=bp.bill_num and bi.batch_num=bp.batch_num");
				queryItemSql.append(" where bi.tenancy_id=? and bi.store_id=? and bi.bill_num=? and bi.rwid in (").append(rwids).append(")");
				int count = posSelfDao.queryForInt(queryItemSql.toString(), new Object[]
				{ tenancyId, storeId, billNum });

				if (count > 0)
				{
					throw SystemException.getInstance(PosErrorCode.BILL_ALREADY_PAY_AMOUNT_ERROR);
				}
			}
		}
		
		StringBuilder queryBillItemSql = new StringBuilder("select bi.rwid,bi.item_id,bi.item_serial,bi.item_count,bi.item_remark,bi.item_property,bi.print_tag,(bp.batch_num is not null) as is_pay from pos_bill_item bi");
		queryBillItemSql.append(" left join (select store_id,bill_num,batch_num from pos_bill_payment group by store_id,bill_num,batch_num) bp on bi.store_id=bp.store_id and bi.bill_num=bp.bill_num and bi.batch_num=bp.batch_num");
		queryBillItemSql.append(" where bi.tenancy_id=? and bi.store_id=? and bi.bill_num=? and bi.rwid in (").append(rwids).append(")");

		SqlRowSet rsb = posSelfDao.query4SqlRowSet(queryBillItemSql.toString(), new Object[]
		{ tenancyId, storeId, billNum});
		
		List<JSONObject> retreatitemList = new ArrayList<JSONObject>();
		List<JSONObject> deleteitemList = new ArrayList<JSONObject>();
		while (rsb.next())
		{
			String itemRemark = rsb.getString("item_remark");
			String itemProperty = rsb.getString("item_property");
			double itemCount = rsb.getDouble("item_count");

			JSONObject itemJson = rwidMap.get(rsb.getString("rwid"));
			double count = itemJson.optDouble("count");
			
			if (count > itemCount)
			{
				throw SystemException.getInstance(PosErrorCode.RETREAT_COUNT_MORE_THAN_EXISTS);
			}

			if (Tools.hv(itemRemark) && SysDictionary.ITEM_REMARK_TC01.equalsIgnoreCase(itemRemark))
			{
				throw SystemException.getInstance(PosErrorCode.HAVING_RETREAT_FOOD);
			}
			else if (Tools.hv(itemRemark) && SysDictionary.ITEM_REMARK_FS02.equalsIgnoreCase(itemRemark))
			{
				throw SystemException.getInstance(PosErrorCode.HAVING_GIVE_FOOD);
			}
			else if (Tools.hv(itemRemark) && SysDictionary.ITEM_REMARK_QX04.equalsIgnoreCase(itemRemark))
			{
				throw SystemException.getInstance(PosErrorCode.HAVING_CANC_FOOD);
			}
			else if (Tools.hv(itemRemark) && SysDictionary.ITEM_REMARK_CJ05.equalsIgnoreCase(itemRemark))
			{
				throw SystemException.getInstance(PosErrorCode.HAVING_OFFSET_FOOD);
			}
			
			if (Tools.hv(itemProperty) && SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(itemProperty))
			{
				throw SystemException.getInstance(PosErrorCode.NOT_PERMIT_RETREAT_FOOD);
			}

			if (rsb.getBoolean("is_pay")==false)
			{
				itemJson.put("item_id", rsb.getString("item_id"));
				itemJson.put("item_serial", rsb.getString("item_serial"));
				itemJson.put("item_count", itemCount);
				itemJson.put("item_remark", itemRemark);
				itemJson.put("item_property", itemProperty);
				deleteitemList.add(itemJson);
			}
			else
			{
				retreatitemList.add(itemJson);
			}
		}
		
		if (deleteitemList.size() > 0)
		{
			StringBuilder items = new StringBuilder();
			for (JSONObject itemJson : deleteitemList)
			{
				items.append(itemJson.optInt("item_id")).append(",");
				if (itemJson.optDouble("item_count") > itemJson.optDouble("count"))
				{
					if (SysDictionary.ITEM_PROPERTY_SETMEAL.equals(itemJson.optString("item_property")))
					{
						double itemCount = DoubleHelper.sub(itemJson.optDouble("item_count"), itemJson.optDouble("count"), DEFAULT_SCALE);
						StringBuilder queryItemSql = new StringBuilder("select * from pos_bill_item where tenancy_id=? and store_id=? and bill_num=? and setmeal_id=? and setmeal_rwid=? and item_property=?");
						SqlRowSet itemRs = posSelfDao.query4SqlRowSet(queryItemSql.toString(), new Object[]
						{ tenancyId, storeId, billNum, itemJson.optInt("item_id"), itemJson.optInt("item_serial"), SysDictionary.ITEM_PROPERTY_MEALLIST });
						List<Object[]> itemList = new ArrayList<Object[]>();
						while (itemRs.next())
						{
							double count = DoubleHelper.mul(DoubleHelper.div(itemRs.getDouble("item_count"), itemJson.optDouble("item_count"), DEFAULT_SCALE), itemCount, DEFAULT_SCALE);
							itemList.add(new Object[]
							{ count, tenancyId, storeId, billNum, itemRs.getInt("rwid") });
						}
						itemList.add(new Object[]
						{ itemCount, tenancyId, storeId, billNum, itemJson.optInt("rwid") });

						if (itemList.size() > 0)
						{
							String updateItemSql = new String("update pos_bill_item set item_count =? where tenancy_id=? and store_id=? and bill_num=? and rwid=?");
							posSelfDao.batchUpdate(updateItemSql, itemList);
						}
					}
					else
					{
						double itemCount = DoubleHelper.sub(itemJson.optDouble("item_count"), itemJson.optDouble("count"), DEFAULT_SCALE);
						String updateItemSql = new String("update pos_bill_item set item_count =? where tenancy_id=? and store_id=? and bill_num=? and rwid=?");
						posSelfDao.update(updateItemSql, new Object[]
						{ itemCount, tenancyId, storeId, billNum, itemJson.optInt("rwid") });
					}
				}
				else
				{
					if (SysDictionary.ITEM_PROPERTY_SETMEAL.equals(itemJson.optString("item_property")))
					{
						String deleteZFKWSql = new String("delete from pos_zfkw_item where tenancy_id=? and store_id=? and bill_num=? and rwid in (select rwid from pos_bill_item where tenancy_id=? and store_id=? and bill_num=? and setmeal_id=? and setmeal_rwid=?)");
						posSelfDao.update(deleteZFKWSql, new Object[]
						{ tenancyId, storeId, billNum, tenancyId, storeId, billNum, itemJson.optInt("item_id"), itemJson.optInt("item_serial") });

						String deleteItemSql = new String("delete from pos_bill_item where tenancy_id=? and store_id=? and bill_num=? and setmeal_id=? and setmeal_rwid=?");
						posSelfDao.update(deleteItemSql, new Object[]
						{ tenancyId, storeId, billNum, itemJson.optInt("item_id"), itemJson.optInt("item_serial") });
					}
					else
					{
						String deleteZFKWSql = new String("delete from pos_zfkw_item where tenancy_id=? and store_id=? and bill_num=? and rwid=?");
						posSelfDao.update(deleteZFKWSql, new Object[]
						{ tenancyId, storeId, billNum, itemJson.optInt("rwid") });

						String deleteItemSql = new String("delete from pos_bill_item where tenancy_id=? and store_id=? and bill_num=? and rwid=?");
						posSelfDao.update(deleteItemSql, new Object[]
						{ tenancyId, storeId, billNum, itemJson.optInt("rwid") });
					}
				}
			}

			if (SysDictionary.SOURCE_MOVE.contains(source)||SysDictionary.SOURCE_ORDER_DEVICE.equals(source))
			{
				if (items.length() > 0)
				{
					items.setLength(items.length() - 1);

					StringBuilder querySoldoutSql = new StringBuilder("select id,item_id,num,item_unit_id,setdate from pos_soldout where tenancy_id=? and store_id=?");
					querySoldoutSql.append(" and item_id in (").append(items).append(")");
					SqlRowSet rs = posSelfDao.query4SqlRowSet(querySoldoutSql.toString(), new Object[]
					{ tenancyId, storeId });

					List<JSONObject> soldOutList = new ArrayList<JSONObject>();
					List<Object[]> list = new ArrayList<Object[]>();
					while (rs.next())
					{
						double count = rs.getDouble("num");
						for (JSONObject itemJson : deleteitemList)
						{
							if (rs.getString("item_id").equals(itemJson.optString("item_id")))
							{
								count = DoubleHelper.add(count, itemJson.getDouble("num"), 4);
							}
						}

						JSONObject soldOutJson = new JSONObject();
						soldOutJson.put("id", rs.getInt("id"));
						soldOutJson.put("item_id", rs.getInt("item_id"));
						soldOutJson.put("item_unit_id", rs.getInt("item_unit_id"));
						soldOutJson.put("setdate", rs.getString("setdate"));
						soldOutJson.put("num", count);
						soldOutList.add(soldOutJson);

						list.add(new Object[]
						{ count, rs.getInt("id"), rs.getInt("item_id") });
					}

					if (list.size() > 0)
					{
						StringBuilder updateSql = new StringBuilder("update pos_soldout set num = ? where id=? and item_id=?");
						posSelfDao.batchUpdate(updateSql.toString(), list);
					}
					if (soldOutList.size() > 0)
					{
						try
						{
							Data cjData = new Data();
							cjData.setType(Type.SOLD_OUT);
							cjData.setOper(Oper.update);
							cjData.setData(soldOutList);

							Comet4jUtil.sendMessage2All(Comet4jUtil.POS_ANDROID, JSONObject.fromObject(cjData).toString());
						}
						catch (Exception e)
						{
							e.printStackTrace();
							logger.error("推送失败::" + e.getMessage());
						}
					}
				}
			}
			
			this.calcAmount(tenancyId, storeId, billNum);
		}
		
		if(retreatitemList.size()>0)
		{
			para.put("report_date", DateUtil.format(reportDate));
			para.put("shift_id", shiftId);
			para.put("opt_num", optNum);
			para.put("item", retreatitemList);
			List<JSONObject> paramList = new ArrayList<JSONObject>();
			paramList.add(para);
	
			posDishService.retreatFood(tenancyId, storeId, paramList, result, printJson, source);
	
			StringBuilder queryItemSumSql = new StringBuilder("select abs(sum(pbi.real_amount)) return_amount,pbi.batch_num from pos_bill_item pbi where pbi.item_remark = ? and pbi.item_property <>? and pbi.bill_num = ? and pbi.store_id = ? group by pbi.batch_num,pbi.bill_num");
			SqlRowSet rsItem = posSelfDao.query4SqlRowSet(queryItemSumSql.toString(), new Object[]
			{ SysDictionary.ITEM_REMARK_TC01, SysDictionary.ITEM_PROPERTY_MEALLIST, billNum, storeId });
			
			
			List<Object[]> batchArgs = new ArrayList<Object[]>();
			while (rsItem.next())
			{
				batchArgs.add(new Object[]{rsItem.getDouble("return_amount"),rsItem.getString("batch_num"),billNum, storeId});
			}
			
			if(batchArgs.size()>0)
			{
				String updateReturnAmountSql = new String("update pos_bill_batch set return_amount = ? where batch_num =? and bill_num = ? and store_id = ? ");
				posSelfDao.batchUpdate(updateReturnAmountSql.toString(), batchArgs);
			}
			
			if (0 == result.getCode())
			{
				StringBuilder retreatRwids = new StringBuilder();
				List<JSONObject> paymentList = null;
				if (rwids.length() > 0)
				{
					StringBuilder queryItemRwidSql = new StringBuilder("select bi.rwid from pos_bill_item bi");
					queryItemRwidSql.append(" inner join (select tenancy_id,store_id,bill_num,batch_num from pos_bill_payment group by tenancy_id,store_id,bill_num,batch_num) p");
					queryItemRwidSql.append(" on bi.batch_num=p.batch_num and bi.bill_num=p.bill_num and bi.store_id=p.store_id and bi.tenancy_id=p.tenancy_id");
					queryItemRwidSql.append(" where bi.tenancy_id=? and bi.store_id=? and bi.bill_num=? and bi.item_remark=? and bi.item_property<>?");
					queryItemRwidSql.append(" and (bi.rwid in (").append(rwids).append(") or bi.yrwid in (").append(rwids).append("))");
	
					SqlRowSet rs = posSelfDao.query4SqlRowSet(queryItemRwidSql.toString(), new Object[]
					{ tenancyId, storeId, billNum, SysDictionary.ITEM_REMARK_TC01, SysDictionary.ITEM_PROPERTY_MEALLIST });
					while (rs.next())
					{
						if (retreatRwids.length() > 0)
						{
							retreatRwids.append(",");
						}
						retreatRwids.append(rs.getString("rwid"));
					}
					if (retreatRwids.length() > 0)
					{
						printJson.put("rwids", retreatRwids.toString());
					}
					else
					{
						printJson.clear();
					}
	
					StringBuilder queryItemSql = new StringBuilder("select bi.batch_num,sum(bi.real_amount) as real_amount,b.table_code,b.source from pos_bill_item bi");
					queryItemSql.append(" inner join (select tenancy_id,store_id,bill_num,batch_num from pos_bill_payment group by tenancy_id,store_id,bill_num,batch_num) p");
					queryItemSql.append(" on bi.batch_num=p.batch_num and bi.bill_num=p.bill_num and bi.store_id=p.store_id and bi.tenancy_id=p.tenancy_id");
					queryItemSql.append(" left join pos_bill b on bi.bill_num=b.bill_num and bi.store_id=b.store_id and bi.tenancy_id=b.tenancy_id");
					queryItemSql.append(" where bi.tenancy_id=? and bi.store_id=? and bi.bill_num=? and bi.item_remark=? and bi.item_property<>?");
					queryItemSql.append(" and (bi.rwid in (").append(rwids).append(") or bi.yrwid in (").append(rwids).append(")) group by b.table_code,b.source,b.bill_num,bi.batch_num");
	
					paymentList = posSelfDao.query4Json(tenancyId, queryItemSql.toString(), new Object[]
					{ tenancyId, storeId, billNum, SysDictionary.ITEM_REMARK_TC01, SysDictionary.ITEM_PROPERTY_MEALLIST });
				}
	
				List<String> batchNumList = new ArrayList<String>();
				for (JSONObject itemJson : paymentList)
				{
					String batchNum = itemJson.optString("batch_num");
					Double realAmount = itemJson.optDouble("real_amount");
					String tableCode = itemJson.optString("table_code");
					String chanel = itemJson.optString("source");
	
					boolean isNew = true;
					for(String batch : batchNumList)
					{
						if(batchNum.equals(batch))
						{
							isNew = false;
						}
					}
					if(isNew)
					{
						batchNumList.add(batchNum);
					}
					
					// 免单,优惠劵,微信,支付宝,团体挂账不允许单品退菜
					StringBuilder qureyPayCountSql = new StringBuilder("select pw.payment_class,bp.jzid,bp.name as payment_name from pos_bill_payment bp left join payment_way pw on bp.jzid=pw.id where bp.tenancy_id=? and bp.store_id=? and bp.bill_num=? and bp.batch_num=? group by pw.payment_class,bp.jzid,bp.name ");
					List<JSONObject> payList = posSelfDao.query4Json(tenancyId, qureyPayCountSql.toString(), new Object[]
					{ tenancyId, storeId, billNum, batchNum });
	
					if (null != payList && payList.size() > 0)
					{
						for (JSONObject payJson : payList)
						{
							switch (payJson.optString("payment_class"))
							{
								case SysDictionary.PAYMENT_CLASS_FREESINGLE:
									throw SystemException.getInstance(PosErrorCode.FREESINGLE_BILL_NOT_PERMIT_CANCBILL);// GJ20160329
								case SysDictionary.PAYMENT_CLASS_COUPONS:
								case SysDictionary.PAYMENT_CLASS_INCORPORATION:
								case SysDictionary.PAYMENT_CLASS_ALI_PAY:
                                case SysDictionary.PAYMENT_DDM_QUICK_PAY:
								case SysDictionary.PAYMENT_CLASS_WECHAT_PAY:
									throw SystemException.getInstance(PosErrorCode.PAYMENT_CLASS_NOT_PERMIT_SINGLE_CANCBILL).set("{0}", payJson.optString("payment_name"));
							}
						}
	
						if (payList.size() > 1)
						{// 多种支付方式:会员卡,现金,银行卡,其他退现金本币
							StringBuilder qureyPayWay = new StringBuilder(
									"select pw.id as jzid,pw.payment_class as type,pw.payment_name1 as name_english,sd.class_item as name,pw.rate from payment_way  pw left join payment_way_of_ogran po on pw.id=po.payment_id left join sys_dictionary sd on pw.payment_name1=sd.class_item_code where  po.organ_id=? and pw.is_standard_money='1'");
							SqlRowSet rs = posSelfDao.query4SqlRowSet(qureyPayWay.toString(), new Object[]
							{ storeId });
							if (rs.next())
							{
//								StringBuilder insertPaySql = new StringBuilder(
//										"insert into pos_bill_payment(tenancy_id,store_id,bill_num,table_code,type,jzid,name,name_english,amount,count,report_date,shift_id,pos_num,cashier_num,last_updatetime,rate,currency_amount,upload_tag,remark,batch_num) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,'0','退菜',?)");
//								posSelfDao.update(insertPaySql.toString(), new Object[]
//								{ tenancyId, storeId, billNum, tableCode, rs.getString("type"), rs.getInt("jzid"), rs.getString("name"), rs.getString("name_english"), -realAmount, 1, reportDate, shiftId, posNum, optNum, currentTime, rs.getDouble("rate"), -realAmount, batchNum });
							
								posSelfDao.insertPosBillPayment(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, rs.getString("type"), rs.getInt("jzid"), rs.getString("name"), rs.getString("name_english"), rs.getDouble("rate"), -realAmount, 1d, -realAmount, null, null, "N", null, null, "退菜", currentTime, SysDictionary.PAYMENT_STATE_REFUND_COMPLETE, batchNum, null,0d);
								
								posSelfDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, rs.getString("type"), rs.getInt("jzid"), rs.getString("name"), rs.getString("name_english"), rs.getDouble("rate"), -realAmount, 1d, -realAmount, null, null, "N", null, null, "退菜", currentTime, SysDictionary.PAYMENT_STATE_REFUND_COMPLETE, batchNum, null,0d);
							}
							else
							{
								throw new SystemException(PosErrorCode.NOT_EXIST_PAY_WAY_STANDARD_ERROR);
							}
						}
						else
						{// 一种支付方式:会员卡,现金,银行卡,其他退回原支付方式
							StringBuilder selectSql = new StringBuilder("select bp.*,pw.payment_class from pos_bill_payment bp left join payment_way pw on bp.jzid=pw.id where bp.currency_amount > 0 and bp.batch_num=? and bp.bill_num =? and bp.store_id = ? and bp.tenancy_id=?");
							SqlRowSet rs = posSelfDao.query4SqlRowSet(selectSql.toString(), new Object[]
							{ batchNum, billNum, storeId, tenancyId });
	
//							StringBuilder insertPaySql = new StringBuilder(
//									"insert into pos_bill_payment(tenancy_id,store_id,bill_num,table_code,type,jzid,name,name_english,amount,count,number,phone,report_date,shift_id,pos_num,cashier_num,last_updatetime,is_ysk,rate,currency_amount,upload_tag,customer_id,bill_code,remark,payment_state,batch_num,more_coupon) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,'0',?,?,?,?,?,?)");
							if (rs.next())
							{
								switch (rs.getString("payment_class"))
								{
									case SysDictionary.PAYMENT_CLASS_CARD:
										String batch_num = JavaMd5.toMd5B32(batchNum + String.valueOf(currentTime.getTime()));
										JSONObject cardJson = new JSONObject();
										cardJson.put("card_code", rs.getString("number"));
										cardJson.put("old_bill_code", rs.getString("bill_code"));
										cardJson.put("bill_code", billNum);
										cardJson.put("revoked_trading", realAmount);
										cardJson.put("chanel", chanel);
										cardJson.put("updatetime", DateUtil.format(currentTime));
										cardJson.put("batch_num", batch_num);
										cardJson.put("business_date", DateUtil.format(reportDate));
										cardJson.put("shift_id", shiftId);
										cardJson.put("operator_id", optNum);
										cardJson.put("operator", optName);
										cardJson.put("pos_num", posNum);
										
										List<JSONObject> dataList = new ArrayList<JSONObject>();
										dataList.add(cardJson);
	
										Data paramData = Data.get(tenancyId, storeId, 0);
										paramData.setTenancy_id(tenancyId);
										paramData.setStore_id(storeId);
										paramData.setType(Type.CUSTOMER_CARD_CONSUME);
										paramData.setOper(Oper.update);
										paramData.setData(dataList);
	
//										String paramStr = JSONObject.fromObject(paramData).toString();
										Data resData = new Data();
										customerService.cancelCardConsume(paramData, resData,"N");
	
										if (resData.isSuccess())
										{
											JSONObject reJson = JSONObject.fromObject(resData.getData().get(0));
											try
											{
												String cardCode = reJson.optString("card_code");
												String billCode = reJson.optString("bill_code");
												String operator = reJson.optString("last_operator");
												String updatetime = reJson.optString("last_updatetime");
												double consume_cardmoney = 0d;
												double main_trading = reJson.optDouble("main_trading");
												double reward_trading = reJson.optDouble("reward_trading");
												double main_balance = reJson.optDouble("main_balance");
												double reward_balance = reJson.optDouble("reward_balance");
												double total_main = 0d;
												double total_reward = 0d;
												double credit = reJson.optDouble("credit");
												double useful_credit = reJson.optDouble("useful_credit");
												String card_class_name = reJson.optString("card_class_name");
												String name = reJson.optString("name");
												String mobil = reJson.optString("mobil");
												double income = 0d;
												double deposit = 0d;
												double sales_price = 0d;
	
												posPrintService.customerPrint(tenancyId, storeId, posNum, operator, updatetime, SysDictionary.PRINT_CODE_1011, cardCode, billCode, consume_cardmoney, main_trading, reward_trading, main_balance, reward_balance, total_main, total_reward, credit,
														useful_credit, income, deposit, sales_price, card_class_name, name, mobil);
											}
											catch (Exception e)
											{
												e.printStackTrace();
												logger.info("打印失败", e);
											}
	
											posSelfDao.insertPosBillPayment(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, rs.getString("payment_class"), rs.getInt("jzid"), rs.getString("name"), rs.getString("name_english"), rs.getDouble("rate"), -realAmount, -1, -realAmount, rs.getString("number"), rs.getString("phone"), "N", null, reJson.optString("bill_code"), null, currentTime, SysDictionary.PAYMENT_STATE_REFUND_COMPLETE, batchNum, null, 0d);
//											posSelfDao.update(insertPaySql.toString(), new Object[]
//											{ tenancyId, storeId, billNum, tableCode, rs.getString("payment_class"), rs.getInt("jzid"), rs.getString("name"), rs.getString("name_english"), -realAmount, -1, rs.getString("number"), rs.getString("phone"), reportDate, shiftId, posNum, optNum, currentTime,
//													"N", rs.getDouble("rate"), -realAmount, null, reJson.optString("bill_code"), null, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum });
										}
										else
										{
											if (99 == resData.getCode())
											{
												posSelfDao.insertPosBillPayment(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, rs.getString("payment_class"), rs.getInt("jzid"), rs.getString("name"), rs.getString("name_english"), rs.getDouble("rate"), -realAmount, -1, -realAmount, rs.getString("number"), rs.getString("phone"), "N", null, null, null, currentTime, SysDictionary.PAYMENT_STATE_REFUNDING, batchNum, null, 0d);
//												posSelfDao.update(insertPaySql.toString(), new Object[]
//												{ tenancyId, storeId, billNum, tableCode, rs.getString("payment_class"), rs.getInt("jzid"), rs.getString("name"), rs.getString("name_english"), -realAmount, -1, rs.getString("number"), rs.getString("phone"), reportDate, shiftId, posNum, optNum,
//														currentTime, "N", rs.getDouble("rate"), -realAmount, null, null, null, SysDictionary.PAYMENT_STATE_PAY, batchNum });
											}
											else
											{
												logger.info("退菜失败:  " + JSONObject.fromObject(resData).toString());
												throw SystemException.getInstance(PosErrorCode.RETREAT_FOOD_ERROR).set("{0}", resData.getMsg());
											}
										}
										break;
									case SysDictionary.PAYMENT_CLASS_CASH:
										StringBuilder qureyPayWay = new StringBuilder(
												"select pw.id as jzid,pw.payment_class as type,pw.payment_name1 as name_english,sd.class_item as name,pw.rate from payment_way  pw left join payment_way_of_ogran po on pw.id=po.payment_id left join sys_dictionary sd on pw.payment_name1=sd.class_item_code where  po.organ_id=? and pw.is_standard_money='1'");
										SqlRowSet rsPay = posSelfDao.query4SqlRowSet(qureyPayWay.toString(), new Object[]
										{ storeId });
										if (rsPay.next())
										{
											posSelfDao.insertPosBillPayment(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, rsPay.getString("type"), rsPay.getInt("jzid"), rsPay.getString("name"), rsPay.getString("name_english"), rs.getDouble("rate"), -realAmount, -1, -realAmount, null, null, "N", null, null, null, currentTime, SysDictionary.PAYMENT_STATE_REFUND_COMPLETE, batchNum, null, 0d);
//											posSelfDao.update(insertPaySql.toString(), new Object[]
//											{ tenancyId, storeId, billNum, tableCode, rsPay.getString("type"), rsPay.getInt("jzid"), rsPay.getString("name"), rsPay.getString("name_english"), -realAmount, -1, null, null, reportDate, shiftId, posNum, optNum, currentTime, "N", rs.getDouble("rate"),
//													-realAmount, null, null, null, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum });
										}
										else
										{
											throw new SystemException(PosErrorCode.NOT_EXIST_PAY_WAY_STANDARD_ERROR);
										}
										break;
									default:
										posSelfDao.insertPosBillPayment(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, rs.getString("payment_class"), rs.getInt("jzid"), rs.getString("name"), rs.getString("name_english"), rs.getDouble("rate"), -realAmount, -rs.getInt("count"), -realAmount, rs.getString("number"), rs.getString("phone"), "N", null, rs.getString("bill_code"), rs.getString("remark"), currentTime, SysDictionary.PAYMENT_STATE_REFUND_COMPLETE, batchNum, null, 0d);
//										posSelfDao.update(insertPaySql.toString(), new Object[]
//										{ tenancyId, storeId, billNum, tableCode, rs.getString("payment_class"), rs.getInt("jzid"), rs.getString("name"), rs.getString("name_english"), -realAmount, -rs.getInt("count"), rs.getString("number"), rs.getString("phone"), reportDate, shiftId, posNum, optNum,
//												currentTime, "N", rs.getDouble("rate"), -realAmount, null, rs.getString("bill_code"), rs.getString("remark"), SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum });
										break;
								}
							}
						}
					}
				}
				
				for(String batch :batchNumList)
				{
					this.updatePosBillBatch(tenancyId, storeId, billNum, batch);
				}
				this.updatePosBill(tenancyId, storeId, billNum);
			}
		}
	}

	@Override
	public List<JSONObject> getBillPaymentRecord(String tenancyId, int storeId, List<?> param) throws Exception
	{
		if (param == null || param.isEmpty())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}

		JSONObject para = JSONObject.fromObject(param.get(0));
		if (Tools.isNullOrEmpty(para.opt("report_date")))
		{
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_REPORT_DATE);
		}
		if (Tools.isNullOrEmpty(para.opt("bill_num")))
		{
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_BILL_NUM);
		}
		String billNum = para.optString("bill_num");

		StringBuilder queryBillSql = new StringBuilder(
				"select b.bill_num,b.batch_num,b.payment_amount,coalesce(sum(p.currency_amount),0) as pay_amount from pos_bill b left join pos_bill_payment p on b.store_id=p.store_id and b.bill_num=p.bill_num where b.tenancy_id=? and b.store_id=? and b.bill_num=? group by b.bill_num,b.batch_num,b.payment_amount");
		List<JSONObject> billList = posSelfDao.query4Json(tenancyId, queryBillSql.toString(), new Object[]
		{ tenancyId, storeId, billNum });

		StringBuilder queryPayment = new StringBuilder("select id,jzid,name,amount,currency_amount,count,number,phone,shift_id,pos_num,cashier_num,last_updatetime,more_coupon from pos_bill_payment where tenancy_id=? and store_id=? and bill_num=? and batch_num<>?");
		List<JSONObject> paymentList = null;
		for (JSONObject bill : billList)
		{
			if (bill.optDouble("payment_amount") > bill.optDouble("pay_amount"))
			{
				paymentList = posSelfDao.query4Json(tenancyId, queryPayment.toString(), new Object[]
				{ tenancyId, storeId, billNum, bill.optString("batch_num") });
			}
			else
			{
				paymentList = posSelfDao.query4Json(tenancyId, queryPayment.toString(), new Object[]
				{ tenancyId, storeId, billNum, "" });
			}

			double payAmount = 0d;
			for (JSONObject pay : paymentList)
			{
				payAmount = DoubleHelper.add(payAmount, pay.optDouble("currency_amount"), 4);
			}
			bill.put("pay_amount", payAmount);
			bill.put("paymentlist", paymentList);
		}
		return billList;
	}

	

	@Override
	public void lockOrUnlockTable(Data param, Data result) throws Exception
	{
		String tenancyId = param.getTenancy_id();
		Integer storeId = param.getStore_id();

		Map<String, Object> map = ReqDataUtil.getDataMap(param); // 开台传入的参数
		// String mode = ParamUtil.getStringValue(map, "mode", true,
		// PosErrorCode.NOT_NULL_MODE);
		Integer shiftId = null;
		Date reportDate = null;
		// 报表日期取得
		try
		{
			reportDate = posSelfDao.getReportDate(tenancyId, storeId);
		}
		catch (SystemException se)
		{
			throw se;
		}
		catch (Exception e1)
		{
			throw new SystemException(PosErrorCode.GET_REPORT_DATE_ERROR);
		}
		String posNum = ParamUtil.getStringValue(map, "pos_num", false, null);
		String optNum = ParamUtil.getStringValue(map, "opt_num", false, null);
		if (SysDictionary.SOURCE_ANDROID_PAD.equals(param.getSource()))
		{
			// if (mode.equals("0"))
			// {
			optNum = posSelfDao.getBindOptNum(tenancyId, storeId, reportDate, posNum);
			// }
			// else if (mode.equals("1"))
			// {
			// @SuppressWarnings("unchecked")
			// List<Map<String, Object>> tables = (List<Map<String, Object>>)
			// map.get("tables");
			// String tableCode = ParamUtil.getStringValue(tables.get(0),
			// "table_code", false, null);
			// String ptcount = new
			// String("select lock_opt_num from pos_tablestate where lock_pos_num=? and table_code = ? and store_id = ? and tenancy_id = ?");
			// SqlRowSet rst = posSelfDao.query4SqlRowSet(ptcount, new Object[]
			// { posNum, tableCode, storeId, tenancyId });
			// if (rst.next())
			// {
			// optNum = rst.getString("lock_opt_num");
			// }
			// }
		}
		// 班次取得
		try
		{
			if (SysDictionary.SOURCE_ANDROID_PAD.equals(param.getSource()))
			{
				StringBuilder queryDevicesSql = new StringBuilder("select pos_num from pos_opt_state_devices where tenancy_id=? and store_id=? and devices_num=? and is_valid='1'");
				SqlRowSet rsd = posSelfDao.query4SqlRowSet(queryDevicesSql.toString(), new Object[]
				{ tenancyId, storeId, posNum });
				if (rsd.next())
				{
					shiftId = posSelfDao.getShiftId(tenancyId, storeId, reportDate, optNum, rsd.getString("pos_num"));
				}
			}
			else
			{
				shiftId = posSelfDao.getShiftId(tenancyId, storeId, reportDate, optNum, posNum);
			}
		}
		catch (SystemException se)
		{
			throw se;
		}
		catch (Exception e1)
		{
			throw new SystemException(PosErrorCode.GET_SHIFT_ID_ERROR);
		}

		map.put("opt_num", optNum);
		map.put("shift_id", shiftId);
		map.put("report_date", DateUtil.format(reportDate));

		posService.lockOrUnlockTable(param, result);
	}

	@Override
	public void queryBillPayment(Data param, Data result, JSONObject printJson) throws Exception
	{
		String tenancyId = param.getTenancy_id();
		int storeId = param.getStore_id();
		String source = param.getSource();

		List<?> paramList = param.getData();
		if (paramList == null || paramList.isEmpty())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}

		JSONObject para = JSONObject.fromObject(paramList.get(0));

		Date reportDate = getReportDate(tenancyId, storeId, para);
		String optNum = getOptNum(tenancyId, storeId, source, reportDate, para);
		Integer shiftId = getShiftId(tenancyId, storeId, source, reportDate, optNum, para);

		String billNum = para.optString("bill_num");
		String posNum = para.optString("pos_num");
		// String optNum = para.optString("opt_num");
		String isprint_bill = para.optString("isprint_bill");
		if (Tools.isNullOrEmpty(isprint_bill))
		{
			isprint_bill = "N";
		}

		if (Tools.isNullOrEmpty(billNum))
		{
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_BILL_NUM);
		}

		Timestamp currentTime = DateUtil.currentTimestamp();

		StringBuilder billAmountSql = new StringBuilder("select coalesce(b.payment_amount,0) as payment_amount,coalesce(b.discount_amount,0) as discount_amount,b.source,b.bill_property,b.batch_num,b.payment_state,b.table_code from pos_bill b where b.tenancy_id=? and b.store_id=? and b.bill_num=?");
		SqlRowSet rsb = posSelfDao.query4SqlRowSet(billAmountSql.toString(), new Object[]
		{ tenancyId, storeId, billNum });
		if (!rsb.next())
		{
			throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_BILL);
		}

		StringBuilder billBatchSql = new StringBuilder("select coalesce(b.payment_amount,0) as payment_amount,coalesce(b.discount_amount,0) as discount_amount,b.source,b.bill_property,b.batch_num,b.payment_state,b.table_code from pos_bill_batch b where b.tenancy_id=? and b.store_id=? and b.bill_num=? and b.payment_state=?");
		SqlRowSet rsb2 = posSelfDao.query4SqlRowSet(billBatchSql.toString(), new Object[]
		{ tenancyId, storeId, billNum ,SysDictionary.PAYMENT_STATE_PAY});
		
//		StringBuilder updatePaymentSql = new StringBuilder("update pos_bill_payment set payment_state=?,bill_code=? where tenancy_id=? and store_id=? and bill_num=? and id=?");
//
//		StringBuilder insertPaymentLogSql = new StringBuilder(
//				"insert into pos_bill_payment_log(tenancy_id,store_id,bill_num,report_date,shift_id,pos_num,cashier_num,table_code,type,jzid,name,name_english,rate,amount,count,currency_amount,number,phone,is_ysk,customer_id,bill_code,remark,last_updatetime,upload_tag,payment_state,batch_num) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,'0',?,?)");
		double paymentAmount = 0d;
//		double discountAmount = 0d;
		String chanel = null;
		String batchNum = null;
		String tableCode = null;
		
		while (rsb2.next())
		{
			paymentAmount = rsb2.getDouble("payment_amount");
//			discountAmount = rsb2.getDouble("discount_amount");
			chanel = rsb2.getString("source");
			batchNum = rsb2.getString("batch_num");
			tableCode = rsb2.getString("table_code");
			
			StringBuilder queryPaymentSql = new StringBuilder("select bp.*,pw.payment_class from pos_bill_payment bp left join payment_way pw on bp.tenancy_id=pw.tenancy_id and bp.jzid=pw.id where bp.tenancy_id=? and bp.store_id=? and bp.bill_num=? and batch_num=? and bp.payment_state=?");
			SqlRowSet rs = posSelfDao.query4SqlRowSet(queryPaymentSql.toString(), new Object[]
			{ tenancyId, storeId, billNum, batchNum,SysDictionary.PAYMENT_STATE_PAY });
			while (rs.next())
			{
				tableCode = rs.getString("table_code");
				String batch_num = rs.getString("batch_num");
				int payId = rs.getInt("id");
				int jzid = rs.getInt("jzid");
				String paymentClass = rs.getString("payment_class");
				double pay_amount = rs.getDouble("amount");
				String number = rs.getString("number");
				int customer_id = rs.getInt("customer_id");
				
	//			String paymentName = rs.getString("name");
	//			String paymentEnglishName = rs.getString("name_english");
	//			double exchangeRate = rs.getDouble("rate");
	//			double pay_count = rs.getDouble("count");
	//			double currency_amount = rs.getDouble("currency_amount");
	//			String phone = rs.getString("phone");
	//			String remark = rs.getString("remark");
	//			String is_ysk = Tools.hv(rs.getString("is_ysk")) ? rs.getString("is_ysk") : "N";
	
				if (SysDictionary.PAYMENT_CLASS_CARD.equals(paymentClass))
				{// 本系统卡 card
					JSONObject requestJson = new JSONObject();
					requestJson.put("card_code", number);
					requestJson.put("third_bill_code", billNum);
					requestJson.put("batch_num", batch_num);
	
					List<JSONObject> requestList = new ArrayList<JSONObject>();
					requestList.add(requestJson);
	
					Data requestData = Data.get(tenancyId, storeId, 0);
					requestData.setType(Type.CUSTOMER_BILL);
					requestData.setOper(Oper.find);
					requestData.setData(requestList);
	
					Data responseData = Data.get(requestData);
					customerService.commonPost(JSONObject.fromObject(requestData).toString(), responseData);
					
//					if("1".equals(com.tzx.framework.common.constant.Constant.getSystemMap().get("test2")))
//					{//总部没收到
//						 responseData.setCode(99);
//						 responseData.setSuccess(false);
//						 responseData.setMsg("连接超时");
//					}
//					else if("2".equals(com.tzx.framework.common.constant.Constant.getSystemMap().get("test2")))
//					{//门店没收到
//						customerService.commonPost(JSONObject.fromObject(requestData).toString(), responseData);
//						responseData.setCode(99);
//						responseData.setSuccess(false);
//						responseData.setMsg("连接超时");
//					}
//					else if("3".equals(com.tzx.framework.common.constant.Constant.getSystemMap().get("test2")))
//					{//扣款失败
//						responseData.setCode(5);
//						responseData.setSuccess(false);
//						responseData.setMsg("结账失败");
//					}
//					else
//					{
//						customerService.commonPost(JSONObject.fromObject(requestData).toString(), responseData);
//					}
					
					if (responseData.isSuccess())
					{
						JSONObject responseJson = JSONObject.fromObject(responseData.getData().get(0));
	//					posSelfDao.update(updatePaymentSql.toString(), new Object[]
	//					{ SysDictionary.PAYMENT_STATE_PAY_COMPLETE, responseJson.optString("bill_code"), tenancyId, storeId, billNum, payId });
	//
	//					posSelfDao.update(insertPaymentLogSql.toString(), new Object[]
	//					{ tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, null, responseJson.optString("bill_code"), remark, currentTime,
	//							SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batch_num });
	
						this.billPaymentSuccess(tenancyId, storeId, billNum, payId, number, responseJson.optString("bill_code"),null);
						
						JSONObject resultJson = new JSONObject();
						resultJson.put("credit", responseJson.optDouble("credit"));
						resultJson.put("main_balance", responseJson.optDouble("main_balance"));
						resultJson.put("reward_balance", responseJson.optDouble("reward_balance"));
						
						List<JSONObject> resultList = new ArrayList<JSONObject>();
						resultList.add(resultJson);
						result.setData(resultList);
	
						String printCode = "1010";
						// String posNum = rs.getString("pos_num");
						String cardCode = responseJson.optString("card_code");
						String billCode = responseJson.optString("bill_code");
						String updatetime = responseJson.optString("operate_time");
						double main_trading = responseJson.optDouble("main_trading");
						double reward_trading = responseJson.optDouble("reward_trading");
						double consume_cardmoney = DoubleHelper.add(main_trading, reward_trading, PAYMENT_SCALE);
						double main_balance = DoubleHelper.sub(responseJson.optDouble("main_balance"), main_trading, PAYMENT_SCALE);
						double reward_balance = DoubleHelper.sub(responseJson.optDouble("reward_balance"), reward_trading, PAYMENT_SCALE);
						double total_main = responseJson.optDouble("total_main");
						double total_reward = responseJson.optDouble("total_reward");
						double credit = responseJson.optDouble("credit");
						double useful_credit = responseJson.optDouble("useful_credit");
						String card_class_name = responseJson.optString("card_class_name");
						String name = responseJson.optString("name");
						String mobil = responseJson.optString("mobil");
						double income = 0d;
						double deposit = 0d;
						double sales_price = 0d;
	
						String operatorName = posSelfDao.getEmpNameById(optNum, tenancyId, storeId);
						posPrintService.customerPrint(tenancyId, storeId, posNum, operatorName, updatetime, printCode, cardCode, billCode, consume_cardmoney, main_trading, reward_trading, main_balance, reward_balance, total_main, total_reward, credit, useful_credit, income, deposit, sales_price,
								card_class_name, name, mobil);
					}
					else
					{
						if (99 == responseData.getCode())
						{
							result.setCode(Constant.CODE_CONN_EXCEPTION);
							result.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
						}
						else
						{
	//						String deletePaymentSql = new String("delete from pos_bill_payment where id=?");
	//						posSelfDao.update(deletePaymentSql.toString(), new Object[]
	//						{ payId });
	//
	//						posSelfDao.update(insertPaymentLogSql.toString(), new Object[]
	//						{ tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, null, null, remark, currentTime,
	//								SysDictionary.PAYMENT_STATE_PAY_FAILURE, batch_num });
							
							this.billPaymentFailure(tenancyId, storeId, billNum, payId, responseData.getCode(), responseData.getMsg());
	
							this.getPaymentException(result, responseData.getCode(), responseData.getMsg());
							// if (5040 == responseData.getCode())
							// {
							// result.setCode(responseData.getCode());
							// result.setMsg(PropertiesLoader.getProperty(String.valueOf(responseData.getCode())));
							// }
							// else
							// {
							// int code =
							// PosErrorCode.BILL_PAYMENT_ERROP.getNumber();
							// result.setCode(code);
							// result.setMsg(PropertiesLoader.getProperty(String.valueOf(code)).replace("{0}",
							// responseData.getMsg()));
							// }
						}
					}
				}
				else if (SysDictionary.PAYMENT_CLASS_COUPONS.equals(paymentClass))
				{// 优惠券 coupons
					JSONObject coupons = new JSONObject();
					coupons.put("coupons_code", number);
	
					List<JSONObject> couponsList = new ArrayList<JSONObject>();
					couponsList.add(coupons);
	
					JSONObject paraJson = new JSONObject();
					paraJson.put("chanel", chanel);
					paraJson.put("bill_money", pay_amount);
					paraJson.put("couponslist", couponsList);
	
					List<JSONObject> requestList = new ArrayList<JSONObject>();
					requestList.add(paraJson);
	
					Data requestData = Data.get(tenancyId, storeId, 0);
					requestData.setType(Type.COUPONS);
					requestData.setOper(Oper.check);
					requestData.setData(requestList);
	
					Data responseData = Data.get(requestData);
					customerService.commonPost(JSONObject.fromObject(requestData).toString(), responseData);
	
					if (CrmErrorCode.COUPONS_CODE_USED.getNumber() == responseData.getCode())
					{
	//					posSelfDao.update(updatePaymentSql.toString(), new Object[]
	//					{ SysDictionary.PAYMENT_STATE_PAY_COMPLETE, null, tenancyId, storeId, billNum, payId });
	//
	//					posSelfDao.update(insertPaymentLogSql.toString(), new Object[]
	//					{ tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, null, null, remark, currentTime,
	//							SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batch_num });
						
						this.billPaymentSuccess(tenancyId, storeId, billNum, payId, number, null,null);
					}
					else if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
					{
						result.setCode(Constant.CODE_CONN_EXCEPTION);
						result.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
					}
					else
					{
	//					String deletePaymentSql = new String("delete from pos_bill_payment where id=?");
	//					posSelfDao.update(deletePaymentSql.toString(), new Object[]
	//					{ payId });
	//
	//					posSelfDao.update(insertPaymentLogSql.toString(), new Object[]
	//					{ tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, null, null, remark, currentTime,
	//							SysDictionary.PAYMENT_STATE_PAY_FAILURE, batch_num });
						
						this.billPaymentFailure(tenancyId, storeId, billNum, payId, responseData.getCode(), responseData.getMsg());
	
						if (Constant.CODE_SUCCESS == responseData.getCode())
						{
							this.getPaymentException(result, PosErrorCode.BILL_PAYMENT_ERROP.getNumber(), "优惠劵使用失败");
						}
						else
						{
							this.getPaymentException(result, responseData.getCode(), responseData.getMsg());
						}
					}
				}
				else if (SysDictionary.PAYMENT_CLASS_INCORPORATION.equals(paymentClass))
				{// 团体挂账 incorporation
					JSONObject requestJson = new JSONObject();
					requestJson.put("incorporation_id", number);
					requestJson.put("customer_id", customer_id);
					requestJson.put("bill_code", billNum);
	
					List<JSONObject> requestList = new ArrayList<JSONObject>();
					requestList.add(requestJson);
	
					Data requestData = Data.get(tenancyId, storeId, 0);
					requestData.setType(Type.INCORPORATION_GZ);
					requestData.setOper(Oper.find);
					requestData.setData(requestList);
	
					Data responseData = Data.get(requestData);
					customerService.commonPost(JSONObject.fromObject(requestData).toString(), responseData);
					if (responseData.isSuccess())
					{
						JSONObject responseJson = JSONObject.fromObject(responseData.getData().get(0));
	//					posSelfDao.update(updatePaymentSql.toString(), new Object[]
	//					{ SysDictionary.PAYMENT_STATE_PAY_COMPLETE, responseJson.optString("bill_code"), tenancyId, storeId, billNum, payId });
	//
	//					posSelfDao.update(insertPaymentLogSql.toString(), new Object[]
	//					{ tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, customer_id, responseJson.optString("bill_code"), remark, currentTime,
	//							SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batch_num });
						
						this.billPaymentSuccess(tenancyId, storeId, billNum, payId, number, responseJson.optString("bill_code"),null);
					}
					else
					{
						if (99 == responseData.getCode())
						{
							result.setCode(Constant.CODE_CONN_EXCEPTION);
							result.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
						}
						else
						{
	//						String deletePaymentSql = new String("delete from pos_bill_payment where id=?");
	//						posSelfDao.update(deletePaymentSql.toString(), new Object[]
	//						{ payId });
	//
	//						posSelfDao.update(insertPaymentLogSql.toString(), new Object[]
	//						{ tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, customer_id, null, remark, currentTime,
	//								SysDictionary.PAYMENT_STATE_PAY_FAILURE, batch_num });
	
							this.billPaymentFailure(tenancyId, storeId, billNum, payId, responseData.getCode(), responseData.getMsg());
							this.getPaymentException(result, responseData.getCode(), responseData.getMsg());
						}
					}
				}
				else if (SysDictionary.PAYMENT_CLASS_WECHAT_PAY.equals(paymentClass))
				{// 微信支付 wechat_pay
					JSONObject requestJson = new JSONObject();
					requestJson.put("bill_num", billNum);
					requestJson.put("pos_num", posNum);
					requestJson.put("opt_num", optNum);
					requestJson.put("report_date", reportDate);
					requestJson.put("shift_id", shiftId);
					requestJson.put("jzid", jzid);
					requestJson.put("polling_flag", false);
	
					List<JSONObject> requestList = new ArrayList<JSONObject>();
					requestList.add(requestJson);
	
					Data requestData = Data.get(tenancyId, storeId, 0);
					requestData.setType(Type.PAYMENT_ORDERQUERY);
					requestData.setOper(Oper.query);
					requestData.setData(requestList);
	
					List<JSONObject> responseList = paymentService.orderquery(requestData, "");
					JSONObject responseJson = JSONObject.fromObject(responseList.get(0));
					if (responseJson.optBoolean("success") && "finished".equals(responseJson.optString("status")))
					{
	//					posSelfDao.update(updatePaymentSql.toString(), new Object[]
	//					{ SysDictionary.PAYMENT_STATE_PAY_COMPLETE, responseJson.optString("trade_no"), tenancyId, storeId, billNum, payId });
	//
	//					posSelfDao.update(insertPaymentLogSql.toString(), new Object[]
	//					{ tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, null, responseJson.optString("trade_no"), remark, currentTime,
	//							SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batch_num });
						this.billPaymentSuccess(tenancyId, storeId, billNum, payId, number, responseJson.optString("trade_no"),null);
					}
					else if (!responseJson.optBoolean("success"))
					{
						result.setCode(Constant.CODE_CONN_EXCEPTION);
						result.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
					}
					else
					{
	//					String deletePaymentSql = new String("delete from pos_bill_payment where id=?");
	//					posSelfDao.update(deletePaymentSql.toString(), new Object[]
	//					{ payId });
	//
	//					posSelfDao.update(insertPaymentLogSql.toString(), new Object[]
	//					{ tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, null, null, remark, currentTime,
	//							SysDictionary.PAYMENT_STATE_PAY_FAILURE, batch_num });
						
						this.billPaymentFailure(tenancyId, storeId, billNum, payId, responseJson.optInt("sub_code"), responseJson.optString("sub_msg"));
	
						this.getPaymentException(result, responseJson.optInt("sub_code"), responseJson.optString("sub_msg"));
					}
				}
				else if (SysDictionary.PAYMENT_DDM_QUICK_PAY.equals(paymentClass) || SysDictionary.PAYMENT_CLASS_ALI_PAY.equals(paymentClass))
				{// 支付宝支付 ali_pay
					JSONObject requestJson = new JSONObject();
					requestJson.put("bill_num", billNum);
					requestJson.put("pos_num", posNum);
					requestJson.put("opt_num", optNum);
					requestJson.put("report_date", reportDate);
					requestJson.put("shift_id", shiftId);
					requestJson.put("jzid", jzid);
					requestJson.put("polling_flag", false);
	
					List<JSONObject> requestList = new ArrayList<JSONObject>();
					requestList.add(requestJson);
	
					Data requestData = Data.get(tenancyId, storeId, 0);
					requestData.setType(Type.PAYMENT_QUERY);
					requestData.setOper(Oper.query);
					requestData.setData(requestList);
	
					List<JSONObject> responseList = paymentService.query(requestData, "");
					JSONObject responseJson = JSONObject.fromObject(responseList.get(0));
					if (responseJson.optBoolean("success") && "finished".equals(responseJson.optString("status")))
					{
	//					posSelfDao.update(updatePaymentSql.toString(), new Object[]
	//					{ SysDictionary.PAYMENT_STATE_PAY_COMPLETE, responseJson.optString("trade_no"), tenancyId, storeId, billNum, payId });
	//
	//					posSelfDao.update(insertPaymentLogSql.toString(), new Object[]
	//					{ tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, null, responseJson.optString("trade_no"), remark, currentTime,
	//							SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batch_num });
						
						this.billPaymentSuccess(tenancyId, storeId, billNum, payId, number, responseJson.optString("trade_no"),null);
					}
					else if (!responseJson.optBoolean("success"))
					{
						result.setCode(Constant.CODE_CONN_EXCEPTION);
						result.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
					}
					else
					{
	//					String deletePaymentSql = new String("delete from pos_bill_payment where id=?");
	//					posSelfDao.update(deletePaymentSql.toString(), new Object[]
	//					{ payId });
	//
	//					posSelfDao.update(insertPaymentLogSql.toString(), new Object[]
	//					{ tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, null, null, remark, currentTime,
	//							SysDictionary.PAYMENT_STATE_PAY_FAILURE, batch_num });
	
						this.billPaymentFailure(tenancyId, storeId, billNum, payId, responseJson.optInt("sub_code"), responseJson.optString("sub_msg"));
						this.getPaymentException(result, responseJson.optInt("sub_code"), responseJson.optString("sub_msg"));
					}
				}
			}
			
			this.completePayment(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentAmount, batchNum, isprint_bill, currentTime, printJson);
		}

		this.getBillPaymentList(tenancyId, storeId, billNum, batchNum, "N", result);
		
		// 写日志pos_log
		posSelfDao.savePosLog(tenancyId, storeId, posNum, optNum, optNum, shiftId, reportDate, Constant.TITLE, "付款状态查询", "账单编号:" + billNum, "");

//		List<JSONObject> resultList = new ArrayList<JSONObject>();
//		resultList.add(resultJson);
//
//		result.setData(resultList);
		if (0 == result.getCode())
		{
			result.setCode(Constant.CODE_SUCCESS);
			result.setMsg(Constant.PAYMENT_SUCCESS);
		}
	}

	@Override
	public void updateBillPaymentState(Data param, Data result, JSONObject printJson) throws Exception
	{
		String tenancyId = param.getTenancy_id();
		int storeId = param.getStore_id();
		String source = param.getSource();

		List<?> paramList = param.getData();
		if (paramList == null || paramList.isEmpty())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}
		JSONObject para = JSONObject.fromObject(paramList.get(0));

		Date reportDate = getReportDate(tenancyId, storeId, para);
		String optNum = getOptNum(tenancyId, storeId, source, reportDate, para);
		Integer shiftId = getShiftId(tenancyId, storeId, source, reportDate, optNum, para);

		String billNum = para.optString("bill_num");
		String posNum = para.optString("pos_num");
		String mode = para.optString("mode");// 0,取消付款;1,付款完成;
		String isprint_bill = para.optString("isprint_bill");
		int payId = para.optInt("pay_id");
		if (Tools.isNullOrEmpty(isprint_bill))
		{
			isprint_bill = "N";
		}
		if (Tools.isNullOrEmpty(billNum))
		{
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_BILL_NUM);
		}

		if (Tools.isNullOrEmpty(mode))
		{
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_MODE);
		}

		Timestamp currentTime = DateUtil.currentTimestamp();

		StringBuilder billAmountSql = new StringBuilder("select p.*,b.payment_amount,w.payment_class from public.pos_bill_payment p");
		billAmountSql.append(" inner join public.pos_bill_batch b on p.tenancy_id=b.tenancy_id and p.store_id=b.store_id and p.bill_num=b.bill_num and p.batch_num=b.batch_num");
		billAmountSql.append(" left join payment_way w on p.tenancy_id=w.tenancy_id and p.jzid=w.id where p.tenancy_id=? and p.store_id=? and p.bill_num=? and p.id=?");

		SqlRowSet rsb = posSelfDao.query4SqlRowSet(billAmountSql.toString(), new Object[]
		{ tenancyId, storeId, billNum, payId });

		double paymentAmount = 0d;
		String batchNum = null;
		String tableCode = null;
		String paymentState = "";

		int jzid = 0;
		String paymentClass = "";
		String paymentName = "";
		String paymentEnglishName = "";
		double exchangeRate = 0d;
		double pay_amount = 0d;
		double pay_count = 0d;
		double currency_amount = 0d;
		String number = "";
		String phone = "";
		int customer_id = 0;
		String is_ysk = "N";

		if (rsb.next())
		{
			paymentAmount = rsb.getDouble("payment_amount");
			batchNum = rsb.getString("batch_num");
			tableCode = rsb.getString("table_code");
			paymentState = rsb.getString("payment_state");

			jzid = rsb.getInt("jzid");
			paymentClass = rsb.getString("payment_class");
			paymentName = rsb.getString("name");
			paymentEnglishName = rsb.getString("name_english");
			exchangeRate = rsb.getDouble("rate");
			pay_amount = rsb.getDouble("amount");
			pay_count = rsb.getDouble("count");
			currency_amount = rsb.getDouble("currency_amount");
			number = rsb.getString("number");
			phone = rsb.getString("phone");
			customer_id = rsb.getInt("customer_id");
			is_ysk = Tools.hv(rsb.getString("is_ysk")) ? rsb.getString("is_ysk") : "N";
		}

		if ("0".equals(mode))
		{
			if (SysDictionary.PAYMENT_STATE_PAY.equals(paymentState) || SysDictionary.PAYMENT_STATE_PAY_FAILURE.equals(paymentState))
			{
				posSelfDao.deletePosBillPaymentById(tenancyId, storeId, payId);

				posSelfDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, customer_id, null, "取消付款", currentTime,
						SysDictionary.PAYMENT_STATE_PAY_FAILURE, batchNum, null,0d);
			}
			
			this.completePayment(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentAmount, batchNum, isprint_bill, currentTime, printJson);

			this.getBillPaymentList(tenancyId, storeId, billNum, batchNum, "N", result);
			
			posSelfDao.savePosLog(tenancyId, storeId, posNum, optNum, optNum, shiftId, reportDate, Constant.TITLE, "取消付款", "账单编号:" + billNum, "");

			result.setCode(Constant.CODE_SUCCESS);
			result.setMsg(Constant.SELF_PAYMENT_COMPLETE_SUCCESS);
		}
		else
		{
//			if (SysDictionary.PAYMENT_STATE_PAY.equals(paymentState))
//			{
				this.billPaymentSuccess(tenancyId, storeId, billNum, payId, number, null, "付款完成");
//			}
			
			this.completePayment(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentAmount, batchNum, isprint_bill, currentTime, printJson);

			this.getBillPaymentList(tenancyId, storeId, billNum, batchNum, "N", result);
			
			posSelfDao.savePosLog(tenancyId, storeId, posNum, optNum, optNum, shiftId, reportDate, Constant.TITLE, "付款完成", "账单编号:" + billNum, "");

			result.setCode(Constant.CODE_SUCCESS);
			result.setMsg(Constant.SELF_PAYMENT_CANCEL_SUCCESS);
		}
	}

	@Override
	public void billPaymentRepeat(Data param, Data result, JSONObject printJson)throws Exception {
		String tenantId = param.getTenancy_id();
		Integer storeId = param.getStore_id();
		
		Map<String, Object> map = ReqDataUtil.getDataMap(param);
		
		String optNum = ParamUtil.getStringValue(map, "opt_num", false, null);//操作员编号
		Date reportDate = ParamUtil.getDateValue(map, "report_date", false, null);//报表日期
		String billNum = ParamUtil.getStringValue(map, "bill_num", true, PosErrorCode.NOT_NULL_BILL_NUM);
		String isprint_bill = ParamUtil.getStringValue(map, "isprint_bill", false, null);
		String posNum = ParamUtil.getStringValue(map, "pos_num", false, null);
		int shiftId = ParamUtil.getIntegerValue(map, "shift_id", false, null);
		if (Tools.isNullOrEmpty(isprint_bill))
		{
			isprint_bill = "N";
		}
		
		//付款ID列表
		JSONArray items = JSONArray.fromObject(map.get("item"));
		StringBuilder rwids = new StringBuilder();
		if (items.size() > 0)
		{
			for (Object obj : items)
			{
				JSONObject itemJson = JSONObject.fromObject(obj);
				if (rwids.length() > 0)
				{
					rwids.append(",");
				}
				rwids.append(itemJson.optInt("rwid"));
			}
		}
		
		if(rwids.length()<=0)
		{
			throw new SystemException(PosErrorCode.NOT_NULL_ITEM_LIST);
		}
		
		Timestamp currentTime = DateUtil.currentTimestamp();
		
		posSelfDao.checkKssy(reportDate, optNum, storeId,shiftId,posNum);
		
		StringBuilder sql = null;
		SqlRowSet rs = null;
		
		sql = new StringBuilder("select bill_property,payment_state from pos_bill where bill_num = ? and store_id = ?");
		rs = posSelfDao.query4SqlRowSet(sql.toString(), new Object[]{ billNum, storeId });
		if (rs.next())
		{
			if ("CLOSED".equals(rs.getString("bill_property")))
			{
				throw new SystemException(PosErrorCode.BILL_CLOSED);
			}
		}
		else
		{
			throw new SystemException(PosErrorCode.NOT_EXISTS_BILL);
		}
		
		sql = new StringBuilder("select bp.*,pw.payment_class,pw.is_check from pos_bill_payment bp left join payment_way pw on bp.jzid=pw.id where store_id = ? and bill_num = ? and bp.id in ("+rwids.toString()+")");
		List<JSONObject> payList = posSelfDao.query4Json(tenantId, sql.toString(), new Object[]
		{ storeId, billNum});
		
		//调用付款方法
		billPaymentDebited(tenantId, storeId, billNum, payList, result);
		
		sql = new StringBuilder("select * from pos_bill_batch where tenancy_id=? and store_id=? and bill_num=? and bill_property=?");
		SqlRowSet rsBatch = posSelfDao.query4SqlRowSet(sql.toString(), new Object[]
		{ tenantId, storeId, billNum, SysDictionary.BILL_PROPERTY_OPEN });
		
		while (rsBatch.next())
		{
			String tableCode = rsBatch.getString("table_code");
			double paymentAmount = rsBatch.getDouble("payment_amount");
			String batchNum = rsBatch.getString("batch_num");
			double difference = rsBatch.getDouble("difference"); 
			if(difference<=0)
			{
				this.completePayment(tenantId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentAmount, batchNum, isprint_bill, currentTime, printJson);
			}
		}
		
		if(result.getCode()==Constant.CODE_SUCCESS)
		{
			result.setCode(Constant.CODE_SUCCESS);
			result.setMsg(Constant.SELF_RETRY_PAYMENT_REPEAT_SUCCESS);
		}
	}

	/**
	 * 付款错误封装
	 * 
	 * @param resultData
	 * @param code
	 * @param msg
	 * @throws Exception
	 */
	private void getPaymentException(Data resultData, int code, String msg) throws Exception
	{
		if (Constant.CODE_CONN_EXCEPTION == code)
		{
			resultData.setCode(Constant.CODE_CONN_EXCEPTION);
			resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
		}
		else if (CrmErrorCode.BALANCE_NOTENOUGH_ERROE.getNumber() == code)
		{
			resultData.setCode(code);
			resultData.setMsg(PropertiesLoader.getProperty(String.valueOf(code)));
		}
		else
		{
			int defaultCode = PosErrorCode.BILL_PAYMENT_ERROP.getNumber();
			resultData.setCode(defaultCode);
			resultData.setMsg(PropertiesLoader.getProperty(String.valueOf(defaultCode)).replace("{0}", msg));
		}
	}

	/**
	 * 获取报表日期
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param param
	 * @return
	 * @throws Exception
	 */
	private Date getReportDate(String tenancyId, int storeId, JSONObject param) throws Exception
	{
		Date reportDate = null;
		if (param.containsKey("report_date") && Tools.hv(param.opt("report_date")))
		{
			reportDate = DateUtil.parseDate(ParamUtil.getDateStringValue(param, "report_date"));
		}

		if (Tools.isNullOrEmpty(reportDate))
		{
			reportDate = posSelfDao.getReportDate(tenancyId, storeId);
		}
		return reportDate;
	}

	/**
	 * 获取班次
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param source
	 * @param reportDate
	 * @param optNum
	 * @param param
	 * @return
	 * @throws Exception
	 */
	private int getShiftId(String tenancyId, int storeId, String source, Date reportDate, String optNum, JSONObject param) throws Exception
	{
		Integer shiftId = null;
		if (param.containsKey("shift_id") && Tools.hv(param.optString("shift_id")))
		{
			shiftId = param.optInt("shift_id");
		}

		if (Tools.isNullOrEmpty(shiftId))
		{
			String posNum = param.optString("pos_num");
			if (SysDictionary.SOURCE_ANDROID_PAD.equals(source))
			{
				StringBuilder queryDevicesSql = new StringBuilder("select pos_num from pos_opt_state_devices where tenancy_id=? and store_id=? and devices_num=? and is_valid='1'");
				SqlRowSet rsd = posSelfDao.query4SqlRowSet(queryDevicesSql.toString(), new Object[]
				{ tenancyId, storeId, posNum });
				if (rsd.next())
				{
					shiftId = posSelfDao.getShiftId(tenancyId, storeId, reportDate, optNum, rsd.getString("pos_num"));
				}
			}
			else
			{
				shiftId = posSelfDao.getShiftId(tenancyId, storeId, reportDate, optNum, posNum);
			}
		}
		return shiftId;
	}

	/**
	 * 获取操作人员
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param source
	 * @param reportDate
	 * @param param
	 * @return
	 * @throws Exception
	 */
	private String getOptNum(String tenancyId, int storeId, String source, Date reportDate, JSONObject param) throws Exception
	{
		String posNum = param.optString("pos_num");
		String optNum = param.optString("opt_num");
		if (SysDictionary.SOURCE_ANDROID_PAD.equals(source))
		{
			optNum = posSelfDao.getBindOptNum(tenancyId, storeId, reportDate, posNum);
		}
		return optNum;
	}

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param batch_num
	 * @throws Exception
	 */
	@SuppressWarnings("unused")
	private boolean checkPaymentAmount(String tenancyId, int storeId, String billNum,String batch_num) throws Exception
	{
		StringBuilder querySql = new StringBuilder("select count(*) from pos_bill_payment where tenancy_id=? and store_id=? and bill_num=? and batch_num=?");
		int count = posSelfDao.queryForInt(querySql.toString(), new Object[]
		{ tenancyId, storeId, billNum, batch_num });

		if (count > 0)
		{
			throw SystemException.getInstance(PosErrorCode.BILL_PAYMENT_STATE_PAYING_ERROR);
		}
		return true;
	}
	
	/**
	 * 验证账单批次是否付款完成
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @throws Exception
	 */
	@SuppressWarnings("unused")
	private boolean checkPaymentState(String tenancyId, int storeId, String billNum,String batch_num) throws Exception
	{
		StringBuilder queryBillAmountSql = new StringBuilder("select b.payment_amount,sum(p.currency_amount) as currency_amount,count(p.id) as pay_count,sum(case when p.payment_state ='02' then 1 else 0 end) payment_state from pos_bill_batch b");
		queryBillAmountSql.append(" left join pos_bill_payment p on b.tenancy_id=p.tenancy_id and b.store_id=p.store_id and b.bill_num=p.bill_num and b.batch_num=p.batch_num");
		queryBillAmountSql.append(" where b.tenancy_id=? and b.store_id=? and b.bill_num=? and b.batch_num=? group by b.bill_num,b.payment_amount");

		SqlRowSet rs = posSelfDao.query4SqlRowSet(queryBillAmountSql.toString(), new Object[]
		{ tenancyId, storeId, billNum, batch_num });
		if (rs.next())
		{
			if (rs.getInt("pay_count") > 0 && rs.getDouble("currency_amount") < rs.getDouble("payment_amount"))
			{
				throw SystemException.getInstance(PosErrorCode.BILL_AMOUNT_MORE_PAY_AMOUNT_ERROR);
			}
			else if(rs.getInt("pay_count") > 0 && rs.getInt("payment_state") > 0)
			{
				throw SystemException.getInstance(PosErrorCode.BILL_PAYMENT_STATE_PAYING_ERROR);
			}
		}
		
		return true;
	}
	
	/** 关闭批次
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param reportDate
	 * @param shiftId
	 * @param posNum
	 * @param optNum
	 * @param tableCode
	 * @param discountAmount
	 * @param paymentAmount
	 * @param batchNum
	 * @param isprint_bill
	 * @param currentTime
	 * @param isBatchNum
	 * @param resultJson
	 * @param printJson
	 * @throws Exception
	 */
	private void completePayment(String tenancyId, int storeId, String billNum, Date reportDate, int shiftId, String posNum, String optNum, String tableCode, double paymentAmount, String batchNum, String isprint_bill, Timestamp currentTime,JSONObject printJson) throws Exception
	{
//		String querySql = new String("select coalesce(sum(currency_amount),0) as amount,coalesce(sum(more_coupon),0) as more_coupon from pos_bill_payment p where p.tenancy_id=? and p.store_id=? and p.bill_num=? and p.batch_num<>?");
//		SqlRowSet rs = posSelfDao.query4SqlRowSet(querySql.toString(), new Object[]
//		{ tenancyId, storeId, billNum, batchNum });
//		double currentPay = 0d;
//		double moreCouponBill = 0d;
//		if (rs.next())
//		{
//			moreCouponBill = rs.getDouble("more_coupon");
//			currentPay = DoubleHelper.sub(paymentAmount, DoubleHelper.sub(rs.getDouble("amount"), moreCouponBill, DEFAULT_SCALE), DEFAULT_SCALE);
//		}

		String sql = new String("select coalesce(sum(currency_amount),0) as amount from pos_bill_payment p where p.tenancy_id=? and p.store_id=? and p.bill_num=? and p.batch_num=?");
		SqlRowSet rs = posSelfDao.query4SqlRowSet(sql.toString(), new Object[]
		{ tenancyId, storeId, billNum, batchNum });
		double difference = 0d;
		if (rs.next())
		{
			difference = DoubleHelper.sub(paymentAmount, rs.getDouble("amount"), DEFAULT_SCALE);
		}

		String paymentState = SysDictionary.PAYMENT_STATE_PAY;
		if (difference <= 0)
		{
			StringBuilder queryPayStateSql = new StringBuilder("select count(*) as count from pos_bill_payment where tenancy_id=? and store_id=? and bill_num=? and batch_num=? and (payment_state=? or payment_state=?)");
			int count = posSelfDao.queryForInt(queryPayStateSql.toString(), new Object[]
			{ tenancyId, storeId, billNum,batchNum, SysDictionary.PAYMENT_STATE_PAY,SysDictionary.PAYMENT_STATE_PAY_FAILURE });
			if (count == 0)
			{
				paymentState = SysDictionary.PAYMENT_STATE_PAY_COMPLETE;
			}
		}

		String billProperty = SysDictionary.BILL_PROPERTY_OPEN;
		double moreCoupon = 0d;
		if (difference <= 0 && "01".equals(paymentState))
		{
			if (difference < 0)
			{ // 计算优惠券多收礼券
				String paySql = new String("select coalesce(sum(currency_amount),0) as amount,w.is_recharge from pos_bill_payment p left join payment_way w on p.jzid=w.id where p.tenancy_id=? and p.store_id=? and p.bill_num=? and p.batch_num=? and w.payment_class=? group by p.jzid,w.is_recharge");
				rs = posSelfDao.query4SqlRowSet(paySql.toString(), new Object[]
				{ tenancyId, storeId, billNum, batchNum, SysDictionary.PAYMENT_CLASS_COUPONS });
				if (rs.next())
				{
					if (paymentAmount < rs.getDouble("amount") && "0".equals(rs.getString("is_recharge")))
					{
						moreCoupon = DoubleHelper.sub(rs.getDouble("amount"), paymentAmount, DEFAULT_SCALE);
						difference = 0d;
					}
				}

				if (moreCoupon > 0)
				{
					StringBuilder updateCouponSql = new StringBuilder();
					updateCouponSql.append(" update pos_bill_payment set more_coupon = ? where id = (select bp.id from pos_bill_payment bp left join payment_way pw on bp.jzid=pw.id");
					updateCouponSql.append(" where bp.tenancy_id=? and bp.store_id=? and bp.bill_num=? and bp.batch_num =? and pw.payment_class=? order by bp.last_updatetime desc limit 1 offset 0)");

					posSelfDao.update(updateCouponSql.toString(), new Object[]
					{ moreCoupon, tenancyId, storeId, billNum, batchNum, SysDictionary.PAYMENT_CLASS_COUPONS });
				}
			}

			if (difference < 0)
			{ // 在支付流水表插入找零记录
				StringBuilder pwsql = new StringBuilder("select pw.id,pw.rate from payment_way pw left join payment_way_of_ogran pwoo on pw.id = pwoo.payment_id where pwoo.organ_id = ? and pw.is_standard_money='1' order by pw.id;");
				rs = posSelfDao.query4SqlRowSet(pwsql.toString(), new Object[]
				{ storeId });
				// 保存的是本币对应的jzid
				Integer payWayId = null;
				if (rs.next())
				{
					payWayId = rs.getInt("id");
				}
				posSelfDao.insertPosBillPayment(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, SysDictionary.PAYMENT_CLASS_CASH, payWayId, "找零", "change", 1d, difference, 1d, difference, null, null, "N", null, null, null, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null,0d);
				
				posSelfDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, SysDictionary.PAYMENT_CLASS_CASH, payWayId, "找零", "change", 1d, difference, 1d, difference, null, null, "N", null, null, null, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null,0d);
			}
			
			billProperty = SysDictionary.BILL_PROPERTY_CLOSED;
			
			printJson.put("tenancy_id", tenancyId);
			printJson.put("store_id", storeId);
			printJson.put("bill_num", billNum);
			printJson.put("pos_num", posNum);
			printJson.put("opt_num", optNum);
			printJson.put("report_date", DateUtil.format(reportDate));
			printJson.put("shift_id", shiftId);

			printJson.put("isprint", isprint_bill);
			printJson.put("print_count", Integer.parseInt(posSelfDao.getSysParameter(tenancyId, storeId, "JZDDYSL")));
			JSONObject jo = posSelfDao.getItemTime(tenancyId, storeId, billNum, batchNum);
			printJson.put("item_time", jo.optString("item_time"));
		}
		
		StringBuilder updateBillBatchSql = new StringBuilder("update pos_bill_batch set more_coupon=?,difference =?,bill_property =?,payment_state=?,payment_time =?,pos_num =?,cashier_num =? where tenancy_id=? and store_id = ? and bill_num =? and batch_num=?");
		posSelfDao.update(updateBillBatchSql.toString(), new Object[]
		{ moreCoupon, difference, billProperty, paymentState, currentTime, posNum, optNum, tenancyId, storeId, billNum, batchNum });
	
		this.updatePosBill(tenancyId, storeId, billNum);
		
		StringBuilder updateBillSql = new StringBuilder("update pos_bill set payment_time =?,pos_num =?,cashier_num =? where tenancy_id=? and store_id = ? and bill_num = ?");
		posSelfDao.update(updateBillSql.toString(), new Object[]
		{ currentTime, posNum, optNum, tenancyId, storeId, billNum });

		this.createBatchNum(tenancyId, storeId, billNum, batchNum, reportDate, shiftId, posNum, optNum);
	}
	
	/** 生成批次
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param batch_num
	 * @param reportDate
	 * @param shiftId
	 * @param posNum
	 * @param optNum
	 * @throws Exception
	 */
	private String createBatchNum(String tenancyId, int storeId, String billNum, String batch_num, Date reportDate, int shiftId, String posNum, String optNum) throws Exception
	{
		boolean isBatchNum = true;
		if (Tools.hv(batch_num))
		{
//			StringBuilder queryBillAmountSql = new StringBuilder("select b.payment_amount,coalesce(sum(p.currency_amount),0) as currency_amount,count(p.id) as pay_count,sum(case when p.payment_state ='02' then 1 else 0 end) payment_state from pos_bill_batch b");
//			queryBillAmountSql.append(" left join pos_bill_payment p on b.tenancy_id=p.tenancy_id and b.store_id=p.store_id and b.bill_num=p.bill_num and b.batch_num=p.batch_num");
//			queryBillAmountSql.append(" where b.tenancy_id=? and b.store_id=? and b.bill_num=? and b.batch_num=? group by b.bill_num,b.payment_amount");
//
//			SqlRowSet rs = posSelfDao.query4SqlRowSet(queryBillAmountSql.toString(), new Object[]
//			{ tenancyId, storeId, billNum, batch_num });
//			if (rs.next())
//			{
//				if(rs.getInt("pay_count")==0)
//				{
//					isBatchNum = false;
//				}
//				else if (rs.getInt("pay_count") > 0 && (rs.getDouble("currency_amount") < rs.getDouble("payment_amount")||rs.getInt("payment_state") > 0))
//				{
//					isBatchNum = false;
//				}
//			}
			
			StringBuilder  queryBillBatchSql = new StringBuilder("select count(*) from pos_bill_batch where tenancy_id=? and store_id=? and bill_num=? and bill_property <>?");
			int count = posSelfDao.queryForInt(queryBillBatchSql.toString(), new Object[]{tenancyId, storeId, billNum,SysDictionary.BILL_PROPERTY_CLOSED});
			if(count>0)
			{
				isBatchNum = false;
			}
			
//			JSONObject billBatchJson = posSelfDao.queryBillBatchByBillNum(tenancyId, storeId, billNum, batch_num);
//			if (!SysDictionary.BILL_PROPERTY_CLOSED.equals(billBatchJson.optString("bill_property")))
//			{
//				isBatchNum = false;
//			}
		}

		if (isBatchNum)
		{
			// 新增批次表数据
			List<JSONObject> billList = posSelfDao.queryPosBillByBillNum(tenancyId, storeId, billNum);
			if (billList != null && billList.size() > 0)
			{
				JSONObject object = new JSONObject();
				object.put("store_id", storeId);
				object.put("busi_date", DateUtil.format(reportDate));
				object.put("pos_num", posNum);
				batch_num = codeService.getCode(tenancyId, Code.POS_BATCH_NUM, object);// 调用统一接口来实现

				JSONObject bill = billList.get(0);

				// 处理服务费
				int serviceId = getServiceId(tenancyId, storeId, billNum, bill.getInt("service_id"));

				JSONObject billBatch = new JSONObject();
				billBatch.put("tenancy_id", tenancyId);
				billBatch.put("store_id", storeId);
				billBatch.put("bill_num", billNum);
				billBatch.put("batch_num", batch_num);
				billBatch.put("report_date", DateUtil.format(reportDate));
				billBatch.put("shift_id", shiftId);
				billBatch.put("opentable_time", DateUtil.getNowDateYYDDMMHHMMSS());
				billBatch.put("open_pos_num", posNum);
				billBatch.put("open_opt", optNum);
				billBatch.put("service_id", serviceId);
				billBatch.put("serial_num", bill.opt("serial_num"));
				billBatch.put("order_num", bill.opt("order_num"));
				billBatch.put("copy_bill_num", bill.opt("copy_bill_num"));
				billBatch.put("table_code", bill.opt("table_code"));
				billBatch.put("item_menu_id", bill.opt("item_menu_id"));
				billBatch.put("guest", bill.opt("guest"));
				billBatch.put("waiter_num", bill.opt("waiter_num"));
				billBatch.put("sale_mode", bill.opt("sale_mode"));
				billBatch.put("source", bill.opt("source"));
				billBatch.put("bill_property", SysDictionary.BILL_PROPERTY_OPEN);
				billBatch.put("payment_state", SysDictionary.PAYMENT_STATE_NOTPAY);
				billBatch.put("service_amount", 0d);
				billBatch.put("service_discount", 100d);
				billBatch.put("subtotal", 0d);
				billBatch.put("bill_amount", 0d);
				billBatch.put("payment_amount", 0d);
				billBatch.put("maling_amount", 0d);
				billBatch.put("difference", 0d);
				billBatch.put("average_amount", 0d);
				billBatch.put("discountk_amount", 0d);
				billBatch.put("discountr_amount", 0d);
				billBatch.put("single_discount_amount", 0d);
				billBatch.put("discount_amount", 0d);
				billBatch.put("discount_rate", 100d);
				billBatch.put("free_amount", 0d);
				billBatch.put("givi_amount", 0d);
				billBatch.put("more_coupon", 0d);
				billBatch.put("return_amount", 0d);
				billBatch.put("integraloffset", 0d);
				
				posSelfDao.insertBillBatch(tenancyId, storeId, billBatch);

				// 修改账单表批次
				JSONObject updateBill = new JSONObject();
				updateBill.put("batch_num", batch_num);
				updateBill.put("id", bill.getInt("id"));
				posSelfDao.updatePosBill(tenancyId, storeId, updateBill);
			}
		}
		
		return batch_num;
	}

	/** 获取服务费id
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param serviceId
	 * @return
	 * @throws Exception
	 */
	private int getServiceId(String tenancyId, int storeId, String billNum, int serviceId) throws Exception
	{
		return 0;
	}
	
	/** 更新批次
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param posNum
	 * @param optNum
	 * @param paymentState
	 * @param currentTime
	 * @throws Exception
	 */
	private void updatePosBillBatch(String tenancyId, int storeId, String billNum, String bacthNum) throws Exception
	{
		double difference = 0d;
//		double freeAmount = 0d;
		double moreCoupon = 0d;
//		String paymentState = SysDictionary.PAYMENT_STATE_NOTPAY;

		StringBuilder queryDifferencelSql = new StringBuilder("select b.bill_num,b.batch_num,b.payment_amount,coalesce(sum(p.currency_amount),0) as currency_amount,coalesce(sum(p.more_coupon),0) as more_coupon from pos_bill_batch b");
		queryDifferencelSql.append(" left join pos_bill_payment p on b.tenancy_id=p.tenancy_id and b.store_id=p.store_id and b.bill_num=p.bill_num and b.batch_num=p.batch_num");
		queryDifferencelSql.append(" where b.tenancy_id =? and b.store_id=? and b.bill_num=? and b.batch_num=? group by b.bill_num,b.batch_num,b.payment_amount");

		SqlRowSet rs = posSelfDao.query4SqlRowSet(queryDifferencelSql.toString(), new Object[]
		{ tenancyId, storeId, billNum, bacthNum });
		if (rs.next())
		{
			difference = DoubleHelper.sub(rs.getDouble("payment_amount"), rs.getDouble("currency_amount"), 4);
			moreCoupon = rs.getDouble("more_coupon");
		}
		
//		if(difference<=0)
//		{
//			StringBuilder queryPayStateSql = new StringBuilder("select count(*) as count from pos_bill_payment where tenancy_id=? and store_id=? and bill_num=? and batch_num=? and (payment_state<>? or payment_state is null)");
//			int count = posSelfDao.queryForInt(queryPayStateSql.toString(), new Object[]
//			{ tenancyId, storeId, billNum,bacthNum, SysDictionary.PAYMENT_STATE_PAY_COMPLETE });
//			if (count == 0)
//			{
//				paymentState = SysDictionary.PAYMENT_STATE_PAY_COMPLETE;
//			}
//			else
//			{
//				paymentState = SysDictionary.PAYMENT_STATE_PAY;
//			}
//		}

		StringBuilder updateBillBatchSql = new StringBuilder("update pos_bill_batch set difference=?,more_coupon=? where tenancy_id=? and store_id=? and bill_num=? and batch_num=?");
		posSelfDao.update(updateBillBatchSql.toString(), new Object[]
		{ difference, moreCoupon, tenancyId, storeId, billNum, bacthNum });
	}
	
	/** 更新账单
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param posNum
	 * @param optNum
	 * @param paymentState
	 * @param currentTime
	 * @throws Exception
	 */
	private void updatePosBill(String tenancyId, int storeId, String billNum) throws Exception
	{
		double difference = 0d;
		double freeAmount = 0d;
		double moreCoupon = 0d;
		String paymentState = SysDictionary.PAYMENT_STATE_NOTPAY;
		
		StringBuilder queryBillBatchSql = new StringBuilder("select");
		queryBillBatchSql.append(" coalesce(sum(case when difference>0 then difference else 0 end),0) as difference,");
		queryBillBatchSql.append(" coalesce(sum(free_amount),0) as free_amount,");
		queryBillBatchSql.append(" coalesce(sum(more_coupon),0) as more_coupon,");
		queryBillBatchSql.append(" coalesce(sum(case when payment_state ='02' then 1 else 0 end),0) as payment_state");
		queryBillBatchSql.append(" from pos_bill_batch where tenancy_id=? and store_id = ? and bill_num = ?");
		
		SqlRowSet rs = posSelfDao.query4SqlRowSet(queryBillBatchSql.toString(), new Object[]{tenancyId, storeId, billNum});
		
		if(rs.next())
		{
			difference = rs.getDouble("difference");
			freeAmount = rs.getDouble("free_amount");
			moreCoupon = rs.getDouble("more_coupon");
			
			if(rs.getInt("payment_state")>0)
			{
				paymentState = SysDictionary.PAYMENT_STATE_PAY;
			}
			else if(difference<=0)
			{
				paymentState = SysDictionary.PAYMENT_STATE_PAY_COMPLETE;
			}
		}
		
		StringBuilder updateBillSql = new StringBuilder("update pos_bill set free_amount=?,more_coupon=?,difference=?,payment_state=? where tenancy_id=? and store_id = ? and bill_num = ?");
		posSelfDao.update(updateBillSql.toString(), new Object[]
		{ freeAmount,moreCoupon,difference,paymentState,tenancyId, storeId, billNum });
	}
	
	/** 扣款
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param batchNum
	 * @param payList
	 * @param result
	 * @throws Exception
	 */
	private void billPaymentDebited(String tenancyId, int storeId, String billNum, List<JSONObject> payList,Data result) throws Exception
	{
		Timestamp currentTime = DateUtil.currentTimestamp();
		String currentTimeStr = DateUtil.format(currentTime);

		JSONObject resultJson = new JSONObject(); 
//		StringBuilder deleteSql = new StringBuilder("delete from pos_bill_payment where tenancy_id=? and store_id=? and id=?");
//
//		StringBuilder updateSql = new StringBuilder("update pos_bill_payment set number=?,bill_code=?,remark=?,payment_state=? where tenancy_id=? and store_id=? and id=?");
//
//		StringBuilder insertPaymentLogSql = new StringBuilder(
//				"insert into pos_bill_payment_log(tenancy_id,store_id,bill_num,report_date,shift_id,pos_num,cashier_num,table_code,type,jzid,name,name_english,rate,amount,count,currency_amount,number,phone,is_ysk,customer_id,bill_code,remark,last_updatetime,upload_tag,payment_state,batch_num) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,'0',?,?)");
//
//		StringBuilder selectSql = new StringBuilder("select bp.*,pw.payment_class,pw.is_check from pos_bill_payment bp left join payment_way pw on bp.jzid=pw.id where bp.bill_num =? and bp.store_id = ? and bp.tenancy_id=? and bp.payment_state=? order by bp.last_updatetime");
//		List<JSONObject> payList = posDishDao.query4Json(tenancyId, selectSql.toString(), new Object[]
//		{ billNum, storeId, tenancyId, SysDictionary.PAYMENT_STATE_PAY });

		for (JSONObject payItem : payList)
		{
			String report_date = payItem.optString("report_date");
			int shiftId = payItem.optInt("shift_id");
			String optNum = payItem.optString("cashier_num");
			String posNum = payItem.optString("pos_num");
			String batchNum = payItem.optString("batch_num");
			String optName = posSelfDao.getEmpNameById(optNum, tenancyId, storeId);
			
			int payId = payItem.optInt("id");
			String paymentClass = payItem.optString("payment_class");
			double amount = payItem.optDouble("amount");
			int customer_id = payItem.optInt("customer_id");
			String number = payItem.optString("number");
			
//			int jzid = payItem.optInt("jzid");
//			Date reportDate = DateUtil.parseDate(report_date);
//			int shiftId = payItem.optInt("shift_id");
//			String tableCode = payItem.optString("table_code");
//			String paymentName = payItem.optString("name");
//			String paymentEnglishName = payItem.optString("name_english");
//			double exchangeRate = payItem.optDouble("rate");
//			double count = payItem.optDouble("count");
//			double currency_amount = payItem.optDouble("currency_amount");
//			String phone = payItem.optString("phone");
//			String remark = payItem.optString("remark");
//			String is_ysk = Tools.hv(payItem.optString("is_ysk")) ? payItem.optString("is_ysk") : "N";
			
			JSONObject paraJson = JSONObject.fromObject(payItem.optString("param_cach"));
			String isCheck = paraJson.optString("is_check");
			if (Tools.isNullOrEmpty(isCheck))
			{
				isCheck = payItem.optString("is_check");
			}

			if (SysDictionary.PAYMENT_CLASS_CARD.equals(paymentClass))
			{// 本系统卡 card
				JSONObject requestJson = new JSONObject();
				requestJson.put("card_code", paraJson.optString("number"));
				requestJson.put("third_code", paraJson.optString("third_code"));
				requestJson.put("cardpassword", paraJson.optString("password"));
				requestJson.put("consume_cardmoney", paraJson.optDouble("amount"));
				requestJson.put("consume_credit", paraJson.optDouble("consume_credit"));
				requestJson.put("consume_creditmoney", paraJson.optDouble("consume_creditmoney"));
				requestJson.put("consume_totalmoney", paraJson.optDouble("payment_amount"));
				requestJson.put("chanel", paraJson.optString("chanel"));
				requestJson.put("bill_code", billNum);
				requestJson.put("batch_num", batchNum);
				requestJson.put("operator", optName);
				requestJson.put("operator_id", optNum);
				requestJson.put("shift_id", shiftId);
				requestJson.put("updatetime", currentTimeStr);
				requestJson.put("business_date", report_date);
				requestJson.put("pos_num", posNum);
				
				List<JSONObject> requestList = new ArrayList<JSONObject>();
				requestList.add(requestJson);

				Data requestData = Data.get(tenancyId, storeId, 0);
				requestData.setType(Type.CUSTOMER_CARD_CONSUME);
				requestData.setOper(Oper.add);
				requestData.setData(requestList);

				Data responseData = Data.get(requestData);
				customerService.customerConsumePost(JSONObject.fromObject(requestData).toString(), responseData);
				
//				if("1".equals(com.tzx.framework.common.constant.Constant.getSystemMap().get("test1")))
//				{//总部没收到
//					 responseData.setCode(99);
//					 responseData.setSuccess(false);
//					 responseData.setMsg("连接超时");
//				}
//				else if("2".equals(com.tzx.framework.common.constant.Constant.getSystemMap().get("test1")))
//				{//门店没收到
//					customerService.consumePost(JSONObject.fromObject(requestData).toString(), responseData);
//					responseData.setCode(99);
//					responseData.setSuccess(false);
//					responseData.setMsg("连接超时");
//				}
//				else if("3".equals(com.tzx.framework.common.constant.Constant.getSystemMap().get("test1")))
//				{//扣款失败
//					responseData.setCode(5);
//					responseData.setSuccess(false);
//					responseData.setMsg("结账失败");
//				}
//				else
//				{
//					customerService.consumePost(JSONObject.fromObject(requestData).toString(), responseData);
//				}

				if (responseData.isSuccess())
				{
					JSONObject responseJson = JSONObject.fromObject(responseData.getData().get(0));
					// 返回余额,pad显示
					resultJson.put("credit", responseJson.optDouble("credit"));
					resultJson.put("main_balance", responseJson.optDouble("main_balance"));
					resultJson.put("reward_balance", responseJson.optDouble("reward_balance"));
					
					this.billPaymentSuccess(tenancyId, storeId, billNum, payId, responseJson.optString("card_code"), responseJson.optString("bill_code"),null);
					
//					posDishDao.update(updateSql.toString(), new Object[]
//					{ responseJson.optString("card_code"), responseJson.optString("bill_code"), paraJson.optString("third_code"), SysDictionary.PAYMENT_STATE_PAY_COMPLETE, tenancyId, storeId, payId });
//
//					posDishDao.update(insertPaymentLogSql.toString(), new Object[]
//					{ tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, amount, count, currency_amount, number, phone, is_ysk, null, responseJson.optString("bill_code"), remark, currentTime,
//							SysDictionary.PAYMENT_STATE_PAY_COMPLETE, null });

					// 会员卡打印
//					String printCode = "1010";
					String cardCode = responseJson.optString("card_code");
					String billCode = responseJson.optString("bill_code");
					String operator = responseJson.optString("operator");
					String updatetime = responseJson.optString("updatetime");
					double consume_cardmoney = responseJson.optDouble("consume_cardmoney");
					double main_trading = responseJson.optDouble("main_trading");
					double reward_trading = responseJson.optDouble("reward_trading");
					double main_balance = responseJson.optDouble("main_balance");
					double reward_balance = responseJson.optDouble("reward_balance");
					double total_main = responseJson.optDouble("total_main");
					double total_reward = responseJson.optDouble("total_reward");
					double credit = responseJson.optDouble("credit");
					double useful_credit = responseJson.optDouble("useful_credit");
					String card_class_name = responseJson.optString("card_class_name");
					String name = responseJson.optString("name");
					String mobil = responseJson.optString("mobil");
					double income = 0d;
					double deposit = 0d;
					double sales_price = 0d;

					posPrintService.customerPrint(tenancyId, storeId, posNum, operator, updatetime, SysDictionary.PRINT_CODE_1010, cardCode, billCode, consume_cardmoney, main_trading, reward_trading, main_balance, reward_balance, total_main, total_reward, credit, useful_credit, income, deposit, sales_price,
							card_class_name, name, mobil);
				}
				else
				{
					if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
					{
						this.getPaymentException(result, Constant.CODE_CONN_EXCEPTION, Constant.CODE_CONN_EXCEPTION_MSG);
					}
					else
					{
						this.billPaymentFailure(tenancyId, storeId, billNum, payId, responseData.getCode(), responseData.getMsg());
//						posDishDao.update(deleteSql.toString(), new Object[]
//						{ tenancyId, storeId, payId });
//
//						posDishDao.update(insertPaymentLogSql.toString(), new Object[]
//						{ tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, amount, count, currency_amount, number, phone, is_ysk, null, null, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_FAILURE,
//								null });

						this.getPaymentException(result, responseData.getCode(), responseData.getMsg());
					}
				}
			}
			else if (SysDictionary.PAYMENT_CLASS_COUPONS.equals(paymentClass))
			{// 优惠券 coupons
				if ("1".equals(isCheck) || !StringUtils.isEmpty(number))
				{
					JSONObject coupons = new JSONObject();
					coupons.put("coupons_code", number);

					List<JSONObject> couponsList = new ArrayList<JSONObject>();
					couponsList.add(coupons);

					JSONObject requestJson = new JSONObject();
					requestJson.put("couponslist", couponsList);
					requestJson.put("chanel", paraJson.optString("chanel"));
					requestJson.put("bill_money", paraJson.optDouble("payment_amount"));
					requestJson.put("bill_code", billNum);
					requestJson.put("updatetime", currentTimeStr);
					requestJson.put("business_date", report_date);// **********
					requestJson.put("operator", optName);
					requestJson.put("operator_id", optNum);
					requestJson.put("shift_id", shiftId);

					StringBuilder billDetailsSql = new StringBuilder("select a.item_id,a.item_unit_id,a.item_price,a.item_count,a.real_amount,a.item_property,a.discount_rate,a.discountr_amount from pos_bill_item a");
					billDetailsSql.append(" inner join pos_bill b on a.tenancy_id=b.tenancy_id and a.store_id=b.store_id and a.bill_num = b.bill_num  and COALESCE(a.batch_num,'')=coalesce(b.batch_num,'')");
					billDetailsSql.append(" where b.tenancy_id=? and b.store_id=? and b.bill_num=? and b.bill_property=?  and a.item_property=? and (a.item_remark is null or a.item_remark='')");
					List<JSONObject> billDetailsList = posSelfDao.query4Json(tenancyId, billDetailsSql.toString(), new Object[]
					{ tenancyId, storeId, billNum, SysDictionary.BILL_PROPERTY_OPEN, SysDictionary.ITEM_PROPERTY_SINGLE });
					requestJson.put("billdetails", billDetailsList);

					List<JSONObject> requestList = new ArrayList<JSONObject>();
					requestList.add(requestJson);

					Data requestData = Data.get(tenancyId, storeId, 0);

					requestData.setType(Type.NEW_COUPONS);
					requestData.setOper(Oper.update);
					requestData.setData(requestList);

					Data responseData = Data.get(requestData);
					customerService.commonPost(JSONObject.fromObject(requestData).toString(), responseData);

//					if("1".equals(com.tzx.framework.common.constant.Constant.getSystemMap().get("test1")))
//					{//总部没收到
//						 responseData.setCode(99);
//						 responseData.setSuccess(false);
//						 responseData.setMsg("连接超时");
//					}
//					else if("2".equals(com.tzx.framework.common.constant.Constant.getSystemMap().get("test1")))
//					{//门店没收到
//						customerService.commonPost(JSONObject.fromObject(requestData).toString(), responseData);
//						responseData.setCode(99);
//						responseData.setSuccess(false);
//						responseData.setMsg("连接超时");
//					}
//					else if("3".equals(com.tzx.framework.common.constant.Constant.getSystemMap().get("test1")))
//					{//扣款失败
//						responseData.setCode(5);
//						responseData.setSuccess(false);
//						responseData.setMsg("结账失败");
//					}
//					else
//					{
//						customerService.commonPost(JSONObject.fromObject(requestData).toString(), responseData);
//					}
					
					if (responseData.isSuccess())
					{
//						posDishDao.update(updateSql.toString(), new Object[]
//						{ number, null, null, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, tenancyId, storeId, payId });
//
//						posDishDao.update(insertPaymentLogSql.toString(), new Object[]
//						{ tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, amount, count, currency_amount, number, phone, is_ysk, null, null, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE,
//								null });
						
						this.billPaymentSuccess(tenancyId, storeId, billNum, payId, number, null,null);
					}
					else
					{
						if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
						{
							this.getPaymentException(result, Constant.CODE_CONN_EXCEPTION, Constant.CODE_CONN_EXCEPTION_MSG);
						}
						else
						{
//							posDishDao.update(deleteSql.toString(), new Object[]
//							{ tenancyId, storeId, payId });
//
//							posDishDao.update(insertPaymentLogSql.toString(), new Object[]
//							{ tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, amount, count, currency_amount, number, phone, is_ysk, null, null, remark, currentTime,
//									SysDictionary.PAYMENT_STATE_PAY_FAILURE, null });

							this.billPaymentFailure(tenancyId, storeId, billNum, payId, responseData.getCode(), responseData.getMsg());
							this.getPaymentException(result, responseData.getCode(), responseData.getMsg());
						}
					}
				}
			}
			else if (SysDictionary.PAYMENT_CLASS_INCORPORATION.equals(paymentClass))
			{// 团体挂账 incorporation
				JSONObject requestJson = new JSONObject();
				requestJson.put("incorporation_id", number);
				requestJson.put("gz_money", amount);
				requestJson.put("bill_code", billNum);
				requestJson.put("bill_money", paraJson.optDouble("payment_amount"));
				requestJson.put("customer_id", customer_id);
				requestJson.put("password", paraJson.optString("password"));
				requestJson.put("operate_time", currentTimeStr);
				requestJson.put("operator", optName);
				requestJson.put("operator_id", optNum);
				requestJson.put("shift_id", shiftId);
				requestJson.put("business_date", report_date);// **********

				List<JSONObject> requestList = new ArrayList<JSONObject>();
				requestList.add(requestJson);

				Data requestData = Data.get(tenancyId, storeId, 0);
				requestData.setType(Type.INCORPORATION_GZ);
				requestData.setOper(Oper.add);
				requestData.setData(requestList);

				Data responseData = Data.get(requestData);
				customerService.commonPost(JSONObject.fromObject(requestData).toString(), responseData);

//				if("1".equals(com.tzx.framework.common.constant.Constant.getSystemMap().get("test1")))
//				{//总部没收到
//					 responseData.setCode(99);
//					 responseData.setSuccess(false);
//					 responseData.setMsg("连接超时");
//				}
//				else if("2".equals(com.tzx.framework.common.constant.Constant.getSystemMap().get("test1")))
//				{//门店没收到
//					customerService.commonPost(JSONObject.fromObject(requestData).toString(), responseData);
//					responseData.setCode(99);
//					responseData.setSuccess(false);
//					responseData.setMsg("连接超时");
//				}
//				else if("3".equals(com.tzx.framework.common.constant.Constant.getSystemMap().get("test1")))
//				{//扣款失败
//					responseData.setCode(5);
//					responseData.setSuccess(false);
//					responseData.setMsg("结账模拟失败");
//				}
//				else
//				{
//					customerService.commonPost(JSONObject.fromObject(requestData).toString(), responseData);
//				}
				
				if (responseData.isSuccess())
				{
//					posDishDao.update(updateSql.toString(), new Object[]
//					{ number, null, null, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, tenancyId, storeId, payId });
//
//					posDishDao.update(insertPaymentLogSql.toString(), new Object[]
//					{ tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, amount, count, currency_amount, number, phone, is_ysk, null, null, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE,
//							null });
					
					this.billPaymentSuccess(tenancyId, storeId, billNum, payId, number, null,null);
				}
				else
				{
					if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
					{
						this.getPaymentException(result, Constant.CODE_CONN_EXCEPTION, Constant.CODE_CONN_EXCEPTION_MSG);
					}
					else
					{
//						posDishDao.update(deleteSql.toString(), new Object[]
//						{ tenancyId, storeId, payId });
//
//						posDishDao.update(insertPaymentLogSql.toString(), new Object[]
//						{ tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, amount, count, currency_amount, number, phone, is_ysk, null, null, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_FAILURE,
//								null });

						this.billPaymentFailure(tenancyId, storeId, billNum, payId, responseData.getCode(), responseData.getMsg());
						this.getPaymentException(result, responseData.getCode(), responseData.getMsg());
					}
				}
			}
		}
		
		List<JSONObject> resultList = new ArrayList<JSONObject>();
		resultList.add(resultJson);
		result.setData(resultList);
	}
	
	/** 付款成功
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param payId
	 * @param number
	 * @param billCode
	 * @throws Exception
	 */
	private void billPaymentSuccess(String tenancyId, int storeId,String billNum, int payId,String number,String billCode,String reString) throws Exception
	{
		JSONObject payment = new JSONObject();
		payment.put("number", number);
		payment.put("bill_code", billCode);
		payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
		payment.put("id", payId);
		posSelfDao.updatePosBillPayment(tenancyId, storeId, payment);

		JSONObject paymentLog = new JSONObject();
		paymentLog.put("tenancy_id", tenancyId);
		paymentLog.put("store_id", storeId);
		paymentLog.put("id", payId);
		posSelfDao.copyDataForTable("pos_bill_payment", "pos_bill_payment_log", paymentLog, false);
	}
	
	/** 付款失败
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param payId
	 * @param code
	 * @param msg
	 * @throws Exception
	 */
	private void billPaymentFailure(String tenancyId, int storeId,String billNum, int payId,int code ,String msg) throws Exception
	{
		JSONObject payment = new JSONObject();
		payment.put("remark", msg);
		payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_FAILURE);
		payment.put("id", payId);
		posSelfDao.updatePosBillPayment(tenancyId, storeId, payment);

		JSONObject paymentLog = new JSONObject();
		paymentLog.put("tenancy_id", tenancyId);
		paymentLog.put("store_id", storeId);
		paymentLog.put("id", payId);
		posSelfDao.copyDataForTable("pos_bill_payment", "pos_bill_payment_log", paymentLog, false);
	}
	
	/** 付款信息查询
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param payId
	 * @param number
	 * @param billCode
	 * @throws Exception
	 */
	private void getBillPaymentList(String tenancyId, int storeId,String billNum,String batchNum,String isBatchNum,Data result) throws Exception
	{
		JSONObject resultJson = new JSONObject();
			
		List<?> dataList = result.getData();
		if(null != dataList && dataList.size()>0)
		{
			resultJson = JSONObject.fromObject(dataList.get(0));
		}
				
		List<JSONObject> billList =  posSelfDao.queryPosBillByBillNum(tenancyId, storeId, billNum);
		
		String tableCode ="";
		String paymentState ="";
		double paymentAmount = 0d;
		double discountAmount = 0d;
		double difference = 0d;
		
		if(null != billList && billList.size()>0)
		{
			JSONObject billBatchJson = billList.get(0); 
			
			tableCode = billBatchJson.optString("table_code");
			paymentState = billBatchJson.optString("payment_state");
			paymentAmount = billBatchJson.optDouble("payment_amount");
			discountAmount = billBatchJson.optDouble("discount_amount");
			difference = billBatchJson.optDouble("difference");
		}
		
		StringBuilder reSql = new StringBuilder(
				"select id,jzid,name,amount,count,coalesce(number,'') as number,coalesce(phone,'') as phone,shift_id,pos_num,cashier_num,last_updatetime,coalesce(customer_id,0) as customer_id,coalesce(bill_code,'') as bill_code,coalesce(remark,'') as remark,currency_amount,payment_state,coalesce(more_coupon,0) as more_coupon from pos_bill_payment where tenancy_id =? and store_id=? and bill_num=?");
		List<JSONObject> paymentList = null;
		if ("Y".equals(isBatchNum))
		{
			reSql.append(" and batch_num=?");
			paymentList = posSelfDao.query4Json(tenancyId, reSql.toString(), new Object[]
			{ tenancyId, storeId, billNum, batchNum });
		}
		else
		{
			paymentList = posSelfDao.query4Json(tenancyId, reSql.toString(), new Object[]
			{ tenancyId, storeId, billNum });
		}

		for (JSONObject payJson : paymentList)
		{
			payJson.put("payment_state_text", SysDictionary.PAYMENT_STATE_TEXT_MAP.get(payJson.optString("payment_state")));
		}

		resultJson.put("bill_num", billNum);
		resultJson.put("table_code", tableCode);
		resultJson.put("payment_amount", paymentAmount);
		resultJson.put("discount_amount", discountAmount);
		resultJson.put("payment_state", paymentState);
		resultJson.put("payment_state_text", SysDictionary.PAYMENT_STATE_TEXT_MAP.get(paymentState));
		resultJson.put("difference", difference > 0 ? difference : 0d);
		resultJson.put("change", difference < 0 ? difference : 0d);
		resultJson.put("paymentlist", paymentList);
		
		List<JSONObject> resultList = new ArrayList<JSONObject>();
		resultList.add(resultJson);
		result.setData(resultList);
	}

	@Override
	public void cancelBillSelf(Data param, PosCodeService codeService, Data result, JSONObject printJson) throws Exception
	{
		String tenantId = param.getTenancy_id();
		Integer organId = param.getStore_id();

		Map<String, Object> map = ReqDataUtil.getDataMap(param);

		Date report_date = ParamUtil.getDateValue(map, "report_date", false, null);
		String opt_num = ParamUtil.getStringValue(map, "opt_num", false, null);
		String pos_num = ParamUtil.getStringValue(map, "pos_num", true, PosErrorCode.NOT_NULL_POS_NUM);
		Integer shift_id = ParamUtil.getIntegerValue(map, "shift_id", false, null);
		String bill_num = ParamUtil.getStringValue(map, "bill_num", true, PosErrorCode.NOT_NULL_BILL_NUM);
		String batch_num = ParamUtil.getStringValue(map, "batch_num", true, PosErrorCode.NOT_NULL_BATCH_NUM);
		Integer reason_id = ParamUtil.getIntegerValue(map, "reason_id", false, null);
		String manager_id = ParamUtil.getStringValue(map, "manager_id", false, null);

		String optName = posSelfDao.getEmpNameById(opt_num, tenantId, organId);
		Timestamp currentTime = DateUtil.currentTimestamp();

		// 判断当前登录人是否签到,或者绑定机台是否签到
		posSelfDao.checkKssy(report_date, opt_num, organId,shift_id,pos_num);
		
		// 取得账单信息
		List<JSONObject> billLists = new ArrayList<JSONObject>();
		try
		{
			billLists = posSelfDao.findBill(tenantId, organId, bill_num);
		}
		catch(Exception e)
		{
			e.printStackTrace();
			logger.error("查询账单表失败::" + e.getMessage());
			throw e;
		}
		String bill_state = null;
		if(billLists.size() > 0)
		{
			JSONObject jo = billLists.get(0);
			bill_state = jo.optString("bill_state");
		}
		else
		{
			throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_BILL);
		}
		
		if(SysDictionary.BILL_PROPERTY_CLOSED.equals(bill_state))
		{
			throw SystemException.getInstance(PosErrorCode.BILL_CLOSED);
		}
		
		// 取得账单批次表信息
		List<JSONObject> batchLists = new ArrayList<JSONObject>();
		try
		{
			batchLists = posSelfDao.findBatch(tenantId, organId, bill_num, batch_num);
		}
		catch(Exception e)
		{
			e.printStackTrace();
			logger.error("查询账单批次表失败::" + e.getMessage());
			throw e;
		}
		String table_code = null;
		Double payment_amount = null;
		String orderNum =null;
		String chanel = SysDictionary.CHANEL_MD01;
		Double pay_amount = null;
		if(batchLists.size() > 0)
		{
			JSONObject jo = batchLists.get(0);
			table_code = jo.optString("table_code");
			payment_amount = jo.optDouble("payment_amount");
			chanel = jo.optString("source");
			orderNum = jo.optString("order_num");
			pay_amount = jo.optDouble("payment_amount");
			bill_state = jo.optString("bill_state");
		}
		else
		{
			throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_BILL_BATCH);
		}
		if (Tools.hv(bill_state))
		{
			throw SystemException.getInstance(PosErrorCode.NOT_PERMIT_CANCBILL_STATE);
		}
		
		// 取得支付信息
		List<JSONObject> payLists = new ArrayList<JSONObject>();
		try
		{
			payLists = posSelfDao.findBillPayment(tenantId, organId, bill_num, batch_num);
		}
		catch(Exception e)
		{
			e.printStackTrace();
			logger.error("查询付款方式表失败::" + e.getMessage());
			throw e;
		}
		double currency_amount = 0;
		double more_coupon = 0;
		String pay_state = "";
		if(payLists.size() > 0)
		{
			for(JSONObject jo : payLists)
			{
				currency_amount += jo.optDouble("currency_amount");
				more_coupon += jo.optDouble("more_coupon");
				pay_state = jo.optString("payment_state");
				if(SysDictionary.PAYMENT_STATE_PAY.equals(pay_state))
				{
					break;
				}
			}
			if(!SysDictionary.PAYMENT_STATE_PAY.equals(pay_state))
			{
				if( pay_amount == (currency_amount - more_coupon))
				{
					pay_state = SysDictionary.PAYMENT_STATE_PAY_COMPLETE;
				}
				else
				{
					pay_state = SysDictionary.PAYMENT_STATE_PAY;
				}
			}
		}
		else
		{
			pay_state = SysDictionary.PAYMENT_STATE_NOTPAY;
		}
		
		// 付款中或者支付未完成不可整单取消
		if(SysDictionary.PAYMENT_STATE_PAY.equals(pay_state))
		{
			throw SystemException.getInstance(PosErrorCode.PAYING_BILL_NOT_PERMIT_CANCBILL);
		}
		
		// 取得该批次下的菜品
		List<JSONObject> itemList = new ArrayList<JSONObject>();
		try
		{
			itemList = posSelfDao.findBillItem(tenantId, organId, bill_num, batch_num);
		}
		catch(Exception e)
		{
			e.printStackTrace();
			logger.error("查询账单明细表失败::" + e.getMessage());
			throw e;
		}
		
		if(itemList.size() == 0)
		{
			throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_DISH_ITEM);
		}
		
		// 未付款
		if (SysDictionary.PAYMENT_STATE_NOTPAY.equals(pay_state))
		{
			// 当前批次未付款，删除该批次下所有菜品
			String dbi = new String(" delete from pos_bill_item where bill_num = ? and batch_num = ? and store_id = ? and tenancy_id = ? ");
			posSelfDao.update(dbi, new Object[]
					{ bill_num, batch_num, organId, tenantId });
			
			StringBuilder rwids = new StringBuilder();
			List<Object[]> returnBatchArgs = new ArrayList<Object[]>();
			for (JSONObject item : itemList)
			{
				Integer item_id = item.getInt("item_id");
				Double item_count = item.getDouble("count");
				Double item_real_amount = item.getDouble("real_amount");
				Timestamp item_time = DateUtil.formatTimestamp(item.optString("item_time"));
				Integer rwid = item.getInt("rwid");
				String item_property = item.getString("item_property");
				
				if (rwids.length() > 0)
				{
					rwids.append(",");
				}
				rwids.append(item.optInt("rwid"));
				
				returnBatchArgs.add(new Object[]
						{ rwid, tenantId, organId, bill_num, report_date, "TC01", item_id, item_count, item_real_amount, reason_id, manager_id, shift_id, "1", item_time, pos_num, currentTime, item_property });
			}
			if (0 == rwids.length())
			{
				throw SystemException.getInstance(PosErrorCode.NOT_NULL_RWID);
			}
			
			// 更新退菜流水库 pos_returngive_item
			if (returnBatchArgs.size() > 0)
			{
				StringBuilder isql = new StringBuilder(
						"insert into pos_returngive_item (rwid,tenancy_id,store_id,bill_num,report_date,type,item_id,count,amount,reason_id,manager_num,shift_id,return_type,item_time,pos_num,last_updatetime,item_property) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
				posSelfDao.batchUpdate(isql.toString(), returnBatchArgs);
			}
			
			// 删除做法口味库
			String dzfi = new String(" delete from pos_zfkw_item where bill_num = ? and store_id = ? and tenancy_id = ? and rwid in (" + rwids + ")");
			posSelfDao.update(dzfi, new Object[]
					{ bill_num, organId, tenantId });
		
			// 计算金额
			this.calcAmount(tenantId, organId, bill_num);
			
			posSelfDao.savePosLog(tenantId, organId, pos_num, opt_num, optName, shift_id, report_date, Constant.TITLE, "整单取消", "", "");
		}
		// 付款完成
		else if(SysDictionary.PAYMENT_STATE_PAY_COMPLETE.equals(pay_state))
		{
			String item_remark = "";
			for (JSONObject item : itemList)
			{
				item_remark = item.optString("item_remark");
				if (Tools.hv(item_remark) && SysDictionary.ITEM_REMARK_TC01.equalsIgnoreCase(item_remark))
				{
					throw SystemException.getInstance(PosErrorCode.ALREADY_SINGLE_RETREAT_BILL_NOT_PERMIT_CANCBILL);
				}
				else if (Tools.hv(item_remark) && SysDictionary.ITEM_REMARK_MD03.equalsIgnoreCase(item_remark))
				{
					throw SystemException.getInstance(PosErrorCode.FREESINGLE_BILL_NOT_PERMIT_CANCBILL);
				}
				
				item.put("reason_id", reason_id);
			}
			
			// 根据当前批次下菜品,调用标准退菜接口;
			JSONObject para = JSONObject.fromObject(param.getData().get(0));
			para.put("report_date", DateUtil.format(report_date));
			para.put("shift_id", shift_id);
			para.put("opt_num", opt_num);
			para.put("item", itemList);
			List<JSONObject> paramList = new ArrayList<JSONObject>();
			paramList.add(para);
			
			// 并调用标准退菜接口
			String agent = "";
			posDishService.retreatFood(tenantId, organId, paramList, result, printJson, agent);
			
			// 修改账单批次表退菜金额
			try
			{
				posSelfDao.updateBatchReturnAmt(tenantId, organId, bill_num, batch_num);
			}
			catch(Exception e)
			{
				e.printStackTrace();
				logger.error("更新账单批次表退菜金额失败::" + e.getMessage());
				throw e;
			}
			
			// 调用退款接口（新增batch_num参数）
			paymentRefund(tenantId, organId, bill_num, payment_amount, bill_num, table_code, report_date, shift_id, pos_num, opt_num,optName, chanel, param.getSecret(),orderNum, batch_num);
		
			this.updatePosBillBatch(tenantId, organId, bill_num, batch_num);
			
			this.updatePosBill(tenantId, organId, bill_num);
		}
		result.setCode(Constant.CODE_SUCCESS);
		result.setMsg(Constant.WHOLE_CANC_BILL_SUCCESS);
	}
	
	/**
	 * 退菜退款
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param tableCode
	 * @param paymentAmount
	 * @throws Exception
	 */
	private void paymentRefund(String tenancyId, int storeId, String billNum, double paymentAmount, String newBillNum, String tableCode, Date reportDate, int shiftId, String posNum, String optNum,String optName,String chanel, String secret,String orderNum,String batchNum) throws Exception
	{
		try
		{
			final int scale = 4;
			// 免单 freesingle
			// 现金 cash
			List<JSONObject> cashList = new ArrayList<JSONObject>();
			// 银行卡 bankcard
			List<JSONObject> bankcardList = new ArrayList<JSONObject>();
			// 本系统卡 card
			List<JSONObject> cardList = new ArrayList<JSONObject>();
			// 优惠券 coupons
			List<JSONObject> couponsList = new ArrayList<JSONObject>();
			// 团体挂账 incorporation
			List<JSONObject> incorporationList = new ArrayList<JSONObject>();
			// 微信支付 wechat_pay
			List<JSONObject> wechatpayList = new ArrayList<JSONObject>();
			// 支付宝支付 ali_pay
			List<JSONObject> alipayList = new ArrayList<JSONObject>();

			List<JSONObject> otherList = new ArrayList<JSONObject>();

			Timestamp currentTime = DateUtil.currentTimestamp();
			String currentTimeStr = DateUtil.format(currentTime);
			
			StringBuilder queryPaymentSql = new StringBuilder("select pw.payment_class,bp.* from pos_bill_payment bp left join payment_way pw on bp.jzid=pw.id where bp.store_id=? and bp.bill_num=? and bp.batch_num=? order by bp.id");

			List<JSONObject> paymentList = posSelfDao.query4Json(tenancyId, queryPaymentSql.toString(), new Object[]
			{ storeId, billNum, batchNum });

			if (null != paymentList && paymentList.size() > 0)
			{
				for (JSONObject payment : paymentList)
				{
					switch (payment.optString("payment_class"))
					{
						case SysDictionary.PAYMENT_CLASS_FREESINGLE:
							throw SystemException.getInstance(PosErrorCode.FREESINGLE_BILL_NOT_PERMIT_CANCBILL);
						case SysDictionary.PAYMENT_CLASS_CASH:
							cashList.add(payment);
							break;
						case SysDictionary.PAYMENT_CLASS_BANKCARD:
							bankcardList.add(payment);
							break;
						case SysDictionary.PAYMENT_CLASS_CARD:
							cardList.add(payment);
							break;
						case SysDictionary.PAYMENT_CLASS_COUPONS:
							couponsList.add(payment);
							break;
						case SysDictionary.PAYMENT_CLASS_INCORPORATION:
							incorporationList.add(payment);
							break;
						case SysDictionary.PAYMENT_CLASS_WECHAT_PAY:
							wechatpayList.add(payment);
							break;
						case SysDictionary.PAYMENT_CLASS_ALI_PAY:
							alipayList.add(payment);
							break;
						default:
							otherList.add(payment);
							break;
					}
				}
			}

			List<Object[]> batchArgs = new ArrayList<Object[]>();
			if (cashList.size() > 0)
			{
				double payAmount = 0d;
				for (JSONObject payment : cashList)
				{
					payAmount = DoubleHelper.add(payAmount, payment.optDouble("currency_amount"), scale);
				}

				StringBuilder qureyPayWay = new StringBuilder(
						"select pw.id as jzid,pw.payment_class as type,pw.payment_name1 as name_english,sd.class_item as name,pw.rate from payment_way  pw left join payment_way_of_ogran po on pw.id=po.payment_id left join sys_dictionary sd on pw.payment_name1=sd.class_item_code where  po.organ_id=? and pw.is_standard_money='1'");
				SqlRowSet rs = posSelfDao.query4SqlRowSet(qureyPayWay.toString(), new Object[]
				{ storeId });
				if (rs.next())
				{
					 batchArgs.add(new
					 Object[]{tenancyId,storeId,newBillNum,tableCode,rs.getString("type"),rs.getInt("jzid"),rs.getString("name"),rs.getString("name_english"),-payAmount,-1,null,null,reportDate,shiftId,posNum,optNum,currentTime,"N",rs.getDouble("rate"),-payAmount,null,null,null,SysDictionary.PAYMENT_STATE_REFUND_COMPLETE,null,batchNum,0d});
				}
				else
				{
					throw new SystemException(PosErrorCode.NOT_EXIST_PAY_WAY_STANDARD_ERROR);
				}
			}

			if (bankcardList.size() > 0)
			{
				for (JSONObject payment : bankcardList)
				{
					batchArgs.add(new Object[]
							{ tenancyId,storeId,newBillNum,tableCode,payment.optString("payment_class"),payment.optInt("jzid"),payment.getString("name"),payment.getString("name_english"),-payment.optDouble("amount"),-payment.optInt("count"),payment.optString("number"),payment.optString("phone"),reportDate,shiftId,posNum,optNum,currentTime,"N",payment.getDouble("rate"),-payment.optDouble("currency_amount"),null,payment.optString("bill_code"),payment.optString("remark"),SysDictionary.PAYMENT_STATE_REFUND_COMPLETE,null,batchNum,0d });
						
				}
			}

			if (otherList.size() > 0)
			{
				for (JSONObject payment : otherList)
				{
					batchArgs.add(new Object[]
							{ tenancyId, storeId, newBillNum, tableCode, payment.optString("payment_class"), payment.optInt("jzid"), payment.getString("name"), payment.getString("name_english"), -payment.optDouble("amount"), -payment.optInt("count"), payment.optString("number"), payment.optString("phone"),
									reportDate, shiftId, posNum, optNum, currentTime, "N", payment.getDouble("rate"), -payment.optDouble("currency_amount"), null, payment.optString("bill_code"), payment.optString("remark"),SysDictionary.PAYMENT_STATE_REFUND_COMPLETE,null,batchNum,0d });
						
				}
			}

			if (cardList.size() > 0)
			{
				for (JSONObject payment : cardList)
				{
					JSONObject cardJson = new JSONObject();
					cardJson.put("card_code", payment.optString("number"));
					cardJson.put("old_bill_code", payment.optString("bill_code"));
					cardJson.put("bill_code", newBillNum);
					cardJson.put("chanel", chanel);
					cardJson.put("updatetime", currentTimeStr);
					cardJson.put("business_date", DateUtil.format(reportDate));// **********
					cardJson.put("shift_id", shiftId);
					cardJson.put("operator_id", optNum);
					cardJson.put("operator", optName);
					cardJson.put("pos_num", posNum);

					
					List<JSONObject> dataList = new ArrayList<JSONObject>();
					dataList.add(cardJson);

					Data paramData = Data.get();
					paramData.setTenancy_id(tenancyId);
					paramData.setStore_id(storeId);
					paramData.setType(Type.CUSTOMER_CARD_CONSUME);
					paramData.setOper(Oper.update);
					paramData.setSecret(secret);
					paramData.setData(dataList);

					Data resData = new Data();
//					String param = JSONObject.fromObject(paramData).toString();

					customerService.cancelCardConsume(paramData, resData,"N");

					if(resData.isSuccess())
					{
						JSONObject reJson = JSONObject.fromObject(resData.getData().get(0));
						batchArgs.add(new Object[]
								{ tenancyId, storeId, newBillNum, tableCode, payment.optString("payment_class"), payment.optInt("jzid"), payment.getString("name"), payment.getString("name_english"), -payment.optDouble("amount"), -payment.optInt("count"), payment.optString("number"), payment.optString("phone"),
										reportDate, shiftId, posNum, optNum, currentTime, "N", payment.getDouble("rate"), -payment.optDouble("currency_amount"), null, reJson.optString("bill_code"), payment.optString("remark") ,SysDictionary.PAYMENT_STATE_REFUND_COMPLETE,null,batchNum,0d});
						try
						{
							String printCode = "1011";
							double main_balance = reJson.optDouble("main_balance");
							double reward_balance = reJson.optDouble("reward_balance");
							String operator = reJson.optString("last_operator");
							
//							String operatorName = posSelfDao.getEmpNameById(operator, tenancyId, storeId);
							
							JSONObject printpPara = new JSONObject();
							printpPara.put("bill_code", reJson.optString("bill_code"));
							printpPara.put("card_code", reJson.optString("card_code"));
							printpPara.put("card_class_name", reJson.optString("card_class_name"));
							printpPara.put("name", reJson.optString("name"));
							printpPara.put("mobil", reJson.optString("mobil"));
							printpPara.put("credit", reJson.optDouble("credit"));
							printpPara.put("useful_credit", reJson.optDouble("useful_credit"));
							printpPara.put("main_balance", main_balance);
							printpPara.put("reward_balance", reward_balance);
							printpPara.put("total_balance", DoubleHelper.add(main_balance, reward_balance, 4));
							printpPara.put("main_trading", reJson.optDouble("main_trading"));
							printpPara.put("reward_trading", reJson.optDouble("reward_trading"));
							printpPara.put("consume_cardmoney", 0d);
							printpPara.put("total_main", 0d);
							printpPara.put("total_reward", 0d);
							printpPara.put("income", 0d);
							printpPara.put("deposit", 0d);
							printpPara.put("sales_price", 0d);
							printpPara.put("payment_name1", "");
							printpPara.put("operator", operator);
							printpPara.put("updatetime", reJson.optString("last_updatetime"));
							
							posSelfDao.customerPrint(tenancyId, storeId, posNum, printCode, "1", printpPara, 2);
						}
						catch (Exception e)
						{
							e.printStackTrace();
							logger.info("打印失败", e);
						}
					}
					else
					{
						batchArgs.add(new Object[]
								{ tenancyId, storeId, newBillNum, tableCode, payment.optString("payment_class"), payment.optInt("jzid"), payment.getString("name"), payment.getString("name_english"), -payment.optDouble("amount"), -payment.optInt("count"), payment.optString("number"), payment.optString("phone"),
										reportDate, shiftId, posNum, optNum, currentTime, "N", payment.getDouble("rate"), -payment.optDouble("currency_amount"), null, null, payment.optString("remark") ,SysDictionary.PAYMENT_STATE_REFUND_FAILURE,null,batchNum,null});
						
					}
				}
			}

			if (couponsList.size() > 0)
			{
				for (JSONObject payment : couponsList)
				{
					JSONObject coupons = new JSONObject();
					coupons.put("coupons_code", payment.optString("number"));

					List<JSONObject> couponslist = new ArrayList<JSONObject>();
					couponslist.add(coupons);

					JSONObject couponsJson = new JSONObject();
					couponsJson.put("chanel", chanel);
					couponsJson.put("couponslist", couponslist);

					List<JSONObject> dataList = new ArrayList<JSONObject>();
					dataList.add(couponsJson);

					Data paramData = Data.get();
					paramData.setTenancy_id(tenancyId);
					paramData.setStore_id(storeId);
					paramData.setType(Type.COUPONS);
					paramData.setOper(Oper.init);
					paramData.setSecret(secret);
					paramData.setData(dataList);

					Data resData = new Data();
					String param = JSONObject.fromObject(paramData).toString();

					customerService.commonPost(param, resData);

					double more_coupon = payment.optDouble("more_coupon");
					if(Double.isNaN(more_coupon))
					{
						more_coupon = 0d;
					}
					if(resData.isSuccess())
					{
						JSONObject reJson = JSONObject.fromObject(resData.getData().get(0));
						batchArgs.add(new Object[]
							{ tenancyId, storeId, newBillNum, tableCode, payment.optString("payment_class"), payment.optInt("jzid"), payment.getString("name"), payment.getString("name_english"), -payment.optDouble("amount"), -payment.optInt("count"), payment.optString("number"), payment.optString("phone"),
									reportDate, shiftId, posNum, optNum, currentTime, "N", payment.getDouble("rate"), -payment.optDouble("currency_amount"), null, reJson.optString("bill_code"), payment.optString("remark") ,SysDictionary.PAYMENT_STATE_REFUND_COMPLETE,null,batchNum, -more_coupon});
					}
					else
					{
						batchArgs.add(new Object[]
								{ tenancyId, storeId, newBillNum, tableCode, payment.optString("payment_class"), payment.optInt("jzid"), payment.getString("name"), payment.getString("name_english"), -payment.optDouble("amount"), -payment.optInt("count"), payment.optString("number"), payment.optString("phone"),
										reportDate, shiftId, posNum, optNum, currentTime, "N", payment.getDouble("rate"), -payment.optDouble("currency_amount"), null, null, payment.optString("remark") ,SysDictionary.PAYMENT_STATE_REFUND_FAILURE,null,batchNum, -more_coupon});
					}
				}
			}

			if (incorporationList.size() > 0)
			{
				for (JSONObject payment : incorporationList)
				{
					JSONObject incorporationJson = new JSONObject();
					incorporationJson.put("bill_code", billNum);
					incorporationJson.put("incorporation_id", payment.optInt("number"));
					incorporationJson.put("customer_id", payment.optString("customer_id"));
					incorporationJson.put("gz_money", payment.optDouble("amount"));
					incorporationJson.put("bill_money", paymentAmount);
					incorporationJson.put("operator", optNum);
					incorporationJson.put("operate_time", currentTimeStr);

					List<JSONObject> dataList = new ArrayList<JSONObject>();
					dataList.add(incorporationJson);

					Data paramData = Data.get();
					paramData.setTenancy_id(tenancyId);
					paramData.setStore_id(storeId);
					paramData.setType(Type.INCORPORATION_GZ);
					paramData.setOper(Oper.update);
					paramData.setSecret(secret);
					paramData.setData(dataList);

					Data resData = new Data();
					String param = JSONObject.fromObject(paramData).toString();

					customerService.commonPost(param, resData);

					if(resData.isSuccess())
					{
						JSONObject reJson = JSONObject.fromObject(resData.getData().get(0));
						batchArgs.add(new Object[]
							{ tenancyId, storeId, newBillNum, tableCode, payment.optString("payment_class"), payment.optInt("jzid"), payment.getString("name"), payment.getString("name_english"), -payment.optDouble("amount"), -payment.optInt("count"), payment.optString("number"), payment.optString("phone"),
									reportDate, shiftId, posNum, optNum, currentTime, "N", payment.getDouble("rate"), -payment.optDouble("currency_amount"), payment.optInt("customer_id"), reJson.optString("bill_code"), payment.optString("remark") ,SysDictionary.PAYMENT_STATE_REFUND_COMPLETE,null,batchNum,0d});
					}
					else
					{
						batchArgs.add(new Object[]
								{ tenancyId, storeId, newBillNum, tableCode, payment.optString("payment_class"), payment.optInt("jzid"), payment.getString("name"), payment.getString("name_english"), -payment.optDouble("amount"), -payment.optInt("count"), payment.optString("number"), payment.optString("phone"),
										reportDate, shiftId, posNum, optNum, currentTime, "N", payment.getDouble("rate"), -payment.optDouble("currency_amount"), payment.optInt("customer_id"),null, payment.optString("remark") ,SysDictionary.PAYMENT_STATE_REFUND_FAILURE,null,batchNum,0d});
					}
				}
			}

			if (wechatpayList.size() > 0)
			{
				for (JSONObject payment : wechatpayList)
				{
					JSONObject wechatpayJson = new JSONObject();
					if("WX02".equals(chanel))
					{
						wechatpayJson.put("bill_num", orderNum);
					}
					else
					{
						wechatpayJson.put("bill_num", billNum);
					}
					wechatpayJson.put("total_amount", paymentAmount);
					wechatpayJson.put("refund_amount", payment.optDouble("amount"));
					wechatpayJson.put("device_info", "");
					wechatpayJson.put("operator_id", optNum);

					List<JSONObject> dataList = new ArrayList<JSONObject>();
					dataList.add(wechatpayJson);

					Data paramData = Data.get();
					paramData.setTenancy_id(tenancyId);
					paramData.setStore_id(storeId);
					paramData.setType(Type.PAYMENT_REFUNDORDER);
					paramData.setOper(Oper.refund);
					paramData.setSecret(secret);
					paramData.setData(dataList);

					List<JSONObject> retList = paymentService.refundorder(paramData, null);

					if (null != retList && retList.size() > 0 && retList.get(0).optBoolean("success"))
					{
						batchArgs.add(new Object[]
							{ tenancyId, storeId, newBillNum, tableCode, payment.optString("payment_class"), payment.optInt("jzid"), payment.getString("name"), payment.getString("name_english"), -payment.optDouble("amount"), -payment.optInt("count"), payment.optString("number"), payment.optString("phone"),
									reportDate, shiftId, posNum, optNum, currentTime, "N", payment.getDouble("rate"), -payment.optDouble("currency_amount"), payment.optInt("customer_id"), payment.optString("bill_code"), payment.optString("remark") ,SysDictionary.PAYMENT_STATE_REFUND_COMPLETE,null,batchNum,0d});
					}
					else
					{
						batchArgs.add(new Object[]
								{ tenancyId, storeId, newBillNum, tableCode, payment.optString("payment_class"), payment.optInt("jzid"), payment.getString("name"), payment.getString("name_english"), -payment.optDouble("amount"), -payment.optInt("count"), payment.optString("number"), payment.optString("phone"),
										reportDate, shiftId, posNum, optNum, currentTime, "N", payment.getDouble("rate"), -payment.optDouble("currency_amount"), payment.optInt("customer_id"), payment.optString("bill_code"), payment.optString("remark") ,SysDictionary.PAYMENT_STATE_REFUND_FAILURE,null,batchNum,0d});
					}
				}
			}

			if (alipayList.size() > 0)
			{
				for (JSONObject payment : alipayList)
				{
					JSONObject alipayJson = new JSONObject();
					alipayJson.put("bill_num", billNum);
					alipayJson.put("refund_amount", payment.optDouble("amount"));
					alipayJson.put("out_request_no", "");
					alipayJson.put("refund_reason", "");
					alipayJson.put("alipay_store_id", "");
					alipayJson.put("terminal_id", "");

					List<JSONObject> dataList = new ArrayList<JSONObject>();
					dataList.add(alipayJson);

					Data paramData = Data.get();
					paramData.setTenancy_id(tenancyId);
					paramData.setStore_id(storeId);
					paramData.setType(Type.PAYMENT_REFUND);
					paramData.setOper(Oper.refund);
					paramData.setSecret(secret);
					paramData.setData(dataList);

					List<JSONObject> retList = paymentService.refund(paramData, null);

					if (null != retList && retList.size() > 0 && retList.get(0).optBoolean("success"))
					{
						batchArgs.add(new Object[]
							{ tenancyId, storeId, newBillNum, tableCode, payment.optString("payment_class"), payment.optInt("jzid"), payment.getString("name"), payment.getString("name_english"), -payment.optDouble("amount"), -payment.optInt("count"), payment.optString("number"), payment.optString("phone"),
									reportDate, shiftId, posNum, optNum, currentTime, "N", payment.getDouble("rate"), -payment.optDouble("currency_amount"), payment.optInt("customer_id"), payment.optString("bill_code"), payment.optString("remark") ,SysDictionary.PAYMENT_STATE_REFUND_COMPLETE,null,batchNum,0d});
					}
					else
					{
						batchArgs.add(new Object[]
								{ tenancyId, storeId, newBillNum, tableCode, payment.optString("payment_class"), payment.optInt("jzid"), payment.getString("name"), payment.getString("name_english"), -payment.optDouble("amount"), -payment.optInt("count"), payment.optString("number"), payment.optString("phone"),
										reportDate, shiftId, posNum, optNum, currentTime, "N", payment.getDouble("rate"), -payment.optDouble("currency_amount"), payment.optInt("customer_id"), payment.optString("bill_code"), payment.optString("remark") ,SysDictionary.PAYMENT_STATE_REFUND_FAILURE,null,batchNum,0d});
						
					}
				}
			}

			if (batchArgs.size() > 0)
			{
				StringBuilder insertPaySql = new StringBuilder(
						"insert into pos_bill_payment(tenancy_id,store_id,bill_num,table_code,type,jzid,name,name_english,amount,count,number,phone,report_date,shift_id,pos_num,cashier_num,last_updatetime,is_ysk,rate,currency_amount,upload_tag,customer_id,bill_code,remark,payment_state,param_cach,batch_num,more_coupon) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,'0',?,?,?,?,?,?,?)");
				posSelfDao.batchUpdate(insertPaySql.toString(), batchArgs);
			}
		}
		catch (Exception e)
		{
			throw e;
		}
	}

}
