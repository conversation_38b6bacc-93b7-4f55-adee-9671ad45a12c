package com.tzx.pos.po.springjdbc.dao;

import com.tzx.framework.common.entity.Data;
import com.tzx.pos.base.dao.BaseDao;

/**
 * TODO
 * <AUTHOR> 
 * @日期：2015年5月13日-下午1:30:02
 */

public interface LoginDao extends BaseDao
{
	String NAME = "com.tzx.pos.po.springjdbc.dao.imp.LoginDaoImp";
	//修改密码
	public void updatePasword(String npassword,String optNum,Integer organId,Data result) throws Exception;
}
