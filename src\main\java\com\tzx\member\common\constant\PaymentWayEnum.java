package com.tzx.member.common.constant;


public enum PaymentWayEnum {

	// 现金 value tzx支付方式 label 微生活支付方式
	CASH("cash", "1"), // 现金
	BANK("bankcard", "2"), // 银行卡
	ALi("ali_pay", "4"), // 支付宝支付
	WECHAT_STORE("wechat_pay", "3"), // 店内微信支付
	WECHAT_ONLINE("wechat_pay_wlife", "6"), // 线上微信支付
	OTHER("other", "10"),// 其他
	;

    private final String value;
    private final String label;

    private PaymentWayEnum(String value, String label){
        this.value = value;
        this.label = label;
    }

    /**
     * 获取键
     * @return
     */
    public String getValue(){
        return value;
    }

    /**
     * 获取值
     * @return
     */
    public String getLabel(){
        return label;
    }

    /**
     * 根据键取值
     * @param value
     * @return
     */
    public static String findLabel(String value){
        if(value == null){
            return OTHER.getLabel();
        }
        for(PaymentWayEnum s: values()){
            if(s.value.equals(value)){
                return s.getLabel();
            }
        }
        return OTHER.getLabel();
    }

    /**
     * 根据值取键
     * @param value
     * @return
     */
    public static String findValue(String label){
        if(label == null){
            return OTHER.getValue();
        }
        for(PaymentWayEnum s: values()){
            if(s.label.equals(label)){
                return s.getValue();
            }
        }
        return OTHER.getValue();
    }
}
