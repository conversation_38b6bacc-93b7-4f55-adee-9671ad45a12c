package com.tzx.orders.bo.imp;

import com.tzx.base.common.util.PosPropertyUtil;
import com.tzx.framework.common.exception.HttpRestException;
import com.tzx.framework.common.util.HttpUtil;
import net.sf.json.JSONObject;
import org.apache.log4j.LogManager;
import org.apache.log4j.Logger;

/**
 * 外卖订单错误日志上传至总部
 *
 * <AUTHOR> email:<EMAIL>
 * @version 1.0  2018-07-17
 * @see     
 * @since   JDK7.0
 * @update  
 */
public class OrderLogUpload implements Runnable{
	
	private final Logger logger = LogManager.getLogger(OrderLogUpload.class);
	
	private String tenancyId;
	private int storeId;
	private String reportDate;
	private String log;
	
	/*
	 * 构造方法
	 */
	public OrderLogUpload(String tenancyId, int storeId, String reportDate, String log){
		this.tenancyId = tenancyId;
		this.storeId = storeId;
		this.reportDate = reportDate;
		this.log = log;
	}

	@Override
	public void run() {
		// 调用总部接口，外卖订单异常日志上传至总部		
		String reqURL = PosPropertyUtil.getMsg("saas.url") + "/orderRest/writeLogs";
		JSONObject json  = new JSONObject();
		json.put("tenantId",tenancyId);
		json.put("shopId",storeId);
		json.put("reportDate",reportDate);
		json.put("logs",log);
		try {
			JSONObject result = HttpUtil.post(reqURL,json,3);
			if(!result.optString("error").equals("0")){
				logger.error("调用外卖平台writeLogs服务接口返回异常："+ result.toString());
			}
		} catch (HttpRestException e) {
			logger.error(e.getMessage());
		}		
	}

}
