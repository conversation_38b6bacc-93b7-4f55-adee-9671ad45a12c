/** 
 * @(#)CodeFormat.java    1.0   2017-06-21
 * 
 * Copyright 北京普照天星科技有限公司. All rights reserved. 
 * PROPRIETARY/CONFIDENTIAL. Use is subject to license terms. 
 */
package com.tzx.pos.asyn.service;

import com.tzx.framework.common.exception.SystemException;
import net.sf.json.JSONObject;

/**
 * 分单、送打功能接口。（异步线程调用）
 *
 * <AUTHOR> email:<EMAIL>
 * @version 1.0  2017-06-19
 * @see     
 * @since   JDK7.0
 * @update  
 */
public interface PosPrintNewThreadService {

	public String NAME="com.tzx.pos.asyn.service.PosPrintNewThreadServiceImp";
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param para
	 * @param printMode
	 * @throws Exception
	 */
	public void posPrintByFunction(String tenancyId, int storeId, String functionId, JSONObject para) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param printMode
	 * @param para
	 * @throws Exception
	 */
	public void posPrintByMode(String tenancyId, int storeId, String printMode, JSONObject para) throws Exception;
		
	/**
	 * @Description 是否开启新的打印模式
	 * @param tenantId 商户号
	 *        storeId 门店号
	 * @return 
	 * @exception SystemException 系统异常
	 * <AUTHOR> email:<EMAIL>
	 * @version 1.0  2017-06-21
	 * @see
	 */
	public  boolean isNONewPrint(String tenantId,Integer storeId)throws SystemException;
	
}
