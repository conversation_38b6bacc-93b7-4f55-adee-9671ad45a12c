package com.tzx.pos.bo.dto;

import java.io.Serializable;

/**
 * 
 * <AUTHOR> 
 * @日期：2015年5月21日-下午6:01:42
 */

public class PosOpenTableBill implements Serializable
{

	/**
	 * 
	 */
	private static final long	serialVersionUID	= 1L;
	
	private String id;
	private String table_code;
	private String bill_num;
	private String opentable_time;
	private String service_name;
	private String service_id;
	private String service_amount;
	private String discount_case_name;
	private String discount_case_id;
	
	private String discountk_amount;
	private String discountr_amount;
	private String maling_amount;
	private String guest;
	private String subtotal;
	private String average_amount;
	private String payment_amount;
	private String givi_amount;
	private String print_count;
	private String waiter_num;
	private String stable;
	private String dinner_type;
	private String sale_mode;

    public String getDinner_type() {
        return dinner_type;
    }

    public void setDinner_type(String dinner_type) {
        this.dinner_type = dinner_type;
    }

    public String getId()
	{
		return id;
	}
	public void setId(String id)
	{
		this.id = id;
	}
	public String getTable_code()
	{
		return table_code;
	}
	public void setTable_code(String table_code)
	{
		this.table_code = table_code;
	}
	public String getBill_num()
	{
		return bill_num;
	}
	public void setBill_num(String bill_num)
	{
		this.bill_num = bill_num;
	}
	public String getOpentable_time()
	{
		return opentable_time;
	}
	public void setOpentable_time(String opentable_time)
	{
		this.opentable_time = opentable_time;
	}
	public String getService_name()
	{
		return service_name;
	}
	public void setService_name(String service_name)
	{
		this.service_name = service_name;
	}
	public String getService_id()
	{
		return service_id;
	}
	public void setService_id(String service_id)
	{
		this.service_id = service_id;
	}
	public String getService_amount()
	{
		return service_amount;
	}
	public void setService_amount(String service_amount)
	{
		this.service_amount = service_amount;
	}
	public String getDiscount_case_name()
	{
		return discount_case_name;
	}
	public void setDiscount_case_name(String discount_case_name)
	{
		this.discount_case_name = discount_case_name;
	}
	public String getDiscount_case_id()
	{
		return discount_case_id;
	}
	public void setDiscount_case_id(String discount_case_id)
	{
		this.discount_case_id = discount_case_id;
	}
	public String getDiscountk_amount()
	{
		return discountk_amount;
	}
	public void setDiscountk_amount(String discountk_amount)
	{
		this.discountk_amount = discountk_amount;
	}
	public String getDiscountr_amount()
	{
		return discountr_amount;
	}
	public void setDiscountr_amount(String discountr_amount)
	{
		this.discountr_amount = discountr_amount;
	}
	public String getMaling_amount()
	{
		return maling_amount;
	}
	public void setMaling_amount(String maling_amount)
	{
		this.maling_amount = maling_amount;
	}
	public String getGuest()
	{
		return guest;
	}
	public void setGuest(String guest)
	{
		this.guest = guest;
	}
	public String getSubtotal()
	{
		return subtotal;
	}
	public void setSubtotal(String subtotal)
	{
		this.subtotal = subtotal;
	}
	public String getAverage_amount()
	{
		return average_amount;
	}
	public void setAverage_amount(String average_amount)
	{
		this.average_amount = average_amount;
	}
	public String getPayment_amount()
	{
		return payment_amount;
	}
	public void setPayment_amount(String payment_amount)
	{
		this.payment_amount = payment_amount;
	}
	public String getGivi_amount()
	{
		return givi_amount;
	}
	public void setGivi_amount(String givi_amount)
	{
		this.givi_amount = givi_amount;
	}
	public String getPrint_count()
	{
		return print_count;
	}
	public void setPrint_count(String print_count)
	{
		this.print_count = print_count;
	}
	public String getWaiter_num()
	{
		return waiter_num;
	}
	public void setWaiter_num(String waiter_num)
	{
		this.waiter_num = waiter_num;
	}
	public String getStable() {
		return stable;
	}
	public void setStable(String stable) {
		this.stable = stable;
	}

	public String getSale_mode() {
		return sale_mode;
	}

	public void setSale_mode(String sale_mode) {
		this.sale_mode = sale_mode;
	}
	
}
