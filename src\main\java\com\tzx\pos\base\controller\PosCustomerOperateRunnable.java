package com.tzx.pos.base.controller;

import java.sql.Timestamp;
import java.util.List;

import org.apache.log4j.Logger;

import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.Tools;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.member.common.entity.PosCustomerOperateListEntity;
import com.tzx.pos.base.service.PosCustomerOperateService;

public class PosCustomerOperateRunnable implements Runnable
{

	private static final Logger	logger				= Logger.getLogger(PosCustomerOperateRunnable.class);
	Double						queryCountNum		= 60.0;	//查询总次数
	String[]					sleepTime			= { "3", "15", "15", "30", "180", "1800", "1800", "1800", "1800", "3600", "7200" };
	Integer						i					= 0;
	Integer						queryContDefaule	= 50;

	@Override
	public void run()
	{
		// TODO Auto-generated method stub
		String tenancyId = null;
		if (Constant.systemMap.containsKey("tenent_id"))
		{
			tenancyId = Constant.systemMap.get("tenent_id");
		}

		Integer storeId = Integer.valueOf(Constant.systemMap.get("store_id"));
		if (Constant.systemMap.containsKey("store_id"))
		{
			storeId = Integer.valueOf(Constant.systemMap.get("store_id"));
		}

		if (Tools.isNullOrEmpty(tenancyId) || Tools.isNullOrEmpty(storeId))
		{
			logger.info("参数为空");
			return;
		}

		DBContextHolder.setTenancyid(tenancyId);
		PosCustomerOperateService customerOperateService = SpringConext.getApplicationContext().getBean(PosCustomerOperateService.class);
		try
		{
			//查询处理中的会员操作
			List<PosCustomerOperateListEntity> customerOperateList = customerOperateService.getPosCustomerOperateListNotComplete(tenancyId, storeId, queryCountNum);

			for (PosCustomerOperateListEntity customerOpera : customerOperateList)
			{
				Integer queryCount = customerOpera.getQuery_count();

				try
				{
					Timestamp queryTime = customerOpera.getLast_query_time();

					Timestamp nowTime = DateUtil.getNowTimestamp();

					if (queryCount >= queryContDefaule)
					{
						i = queryCount - queryContDefaule;
						if (i >= sleepTime.length)
						{
							i = sleepTime.length - 1;
						}
					}
					else
					{
						i = 0;
					}

					Long count = Long.parseLong(sleepTime[i]);
					if (nowTime.getTime() < (queryTime.getTime() + count * 1000))
					{
						continue;
					}
					
					//请求总部,查询会员操作的处理状态,并根据返回状态进行相关处理
					customerOperateService.queryPosCustomerOperateListForRunnable(tenancyId, storeId, customerOpera);
				}
				catch (Exception e)
				{
					e.printStackTrace();
					logger.error("会员操作事务报错:", e);
				}
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
			logger.error("会员操作事务报错:", e);
		}

	}

}
