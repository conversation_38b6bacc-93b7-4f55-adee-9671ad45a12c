package com.tzx.framework.common.print;

import java.awt.print.Book;
import java.awt.print.PageFormat;
import java.awt.print.Paper;
import java.awt.print.PrinterJob;
import java.util.List;

import javax.print.PrintService;
import javax.print.attribute.standard.PrinterIsAcceptingJobs;
import javax.print.attribute.standard.PrinterState;

import org.apache.log4j.Logger;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;

import com.tzx.framework.common.print.template.PrintTemplate_HTML;

/**
 * 打印工作将在UI之外的线程中处理，所以打印是越到的异常全部抛出 由负责打印的线程来处理打印异常，否则会因错误导致打印服务线程有异常而停止
 * 
 * <AUTHOR>
 * 
 */
public class PrintHelper
{
	private PrinterJob		printJob;

	private double			page_width;
	private double			page_height;
	
	private static Logger	logger	= Logger.getLogger(PrintHelperNew.class);

	private PrintHelper()
	{
	}

	/**
	 * 初始化
	 * @param width	纸张宽度，单位毫米
	 * @param printname	打印机名称
	 * @return
	 * @throws Exception
	 */
	public static PrintHelper newInstance(double width,String printname)throws Exception
	{
		PrintHelper helper = new PrintHelper();
		helper.page_width = width / 25.4 * 72;
		
		PrintService[] services = PrinterJob.lookupPrintServices();
		PrintService service = null;

		for (int i = 0; i < services.length; i++)
		{
			if (services[i].getName().equalsIgnoreCase(printname))
			{
				service = services[i];
				break;
			}
		}
		
		helper.printJob = PrinterJob.getPrinterJob();
		if (service != null)
		{
			PrinterState printerState = service.getAttribute(PrinterState.class);
			if( null != printerState)
			{
				if(PrinterState.IDLE == printerState)
				{
					logger.info("打印机状态:空闲");
				}
				else if(PrinterState.PROCESSING == printerState)
				{
					logger.info("打印机状态:打印中");
				}
				else if(PrinterState.STOPPED == printerState)
				{
					logger.info("打印机状态:已停止");
				}
				else if(PrinterState.UNKNOWN == printerState)
				{
					logger.info("打印机状态:未知");
				}
			}
			else
			{
				logger.info("打印机作业状态:null");
			}
			
			PrinterIsAcceptingJobs isAcceptingJob =  service.getAttribute(PrinterIsAcceptingJobs.class);
			if( null != isAcceptingJob)
			{
				if(PrinterIsAcceptingJobs.NOT_ACCEPTING_JOBS == service.getAttribute(PrinterIsAcceptingJobs.class))
				{
					logger.info("打印机作业状态:不接受打印作业");
				}
				else if(PrinterIsAcceptingJobs.ACCEPTING_JOBS == service.getAttribute(PrinterIsAcceptingJobs.class))
				{
					logger.info("打印机作业状态:接受打印作业");
				}
			}
			else
			{
				logger.info("打印机状态:null");
			}
			
			helper.printJob.setPrintService(service);
		}
		else
		{
			throw new Exception("未找到指定的打印机：" + printname);
		}
		
		return helper;
	}
	
	/**
	 * 打印
	 * @param dom 将要打印的Jsoup格式文档
	 * @throws Exception
	 */
	public synchronized void print(Document dom) throws Exception
	{
		PrintTemplate_HTML html = new PrintTemplate_HTML();
		
		List<PrintLine> printLineList = html.setupPrintLineList(dom);
		this.page_height = html.computePrintHeight(printLineList);
		
		PrintableImpl printableImpl = new PrintableImpl(printLineList);

		Paper paper = new Paper();
		paper.setSize(this.page_width, this.page_height);
		paper.setImageableArea(0, 0, this.page_width, this.page_height);

		PageFormat pageFormat = new PageFormat();
		pageFormat.setOrientation(PageFormat.PORTRAIT);
		pageFormat.setPaper(paper);

		Book book = new Book();
		book.append(printableImpl, pageFormat);

		this.printJob.setPageable(book);
		this.printJob.print();
	}
	
	/** 打印
	 * @param html 
	 * @param dom 将要打印的Jsoup格式文档
	 * @throws Exception
	 */
	public synchronized void print(PrintTemplate_HTML html, Document dom) throws Exception
	{
		List<PrintLine> printLineList = html.setupPrintLineList(dom);
		this.page_height = html.computePrintHeight(printLineList);
		
		PrintableImpl printableImpl = new PrintableImpl(printLineList);

		Paper paper = new Paper();
		paper.setSize(this.page_width, this.page_height);
		paper.setImageableArea(0, 0, this.page_width, this.page_height);

		PageFormat pageFormat = new PageFormat();
		pageFormat.setOrientation(PageFormat.PORTRAIT);
		pageFormat.setPaper(paper);

		Book book = new Book();
		book.append(printableImpl, pageFormat);

		this.printJob.setPageable(book);
		this.printJob.print();
	}
	

	public static void main(String[] args) throws Exception
	{
		String d = "<!--StartFragment--><h3 style=\"text-align: center;\">结账单</h3><h6 style=\"text-align: left;\">操作员：<em title=\"R1:org_full_name\">&lt;**&gt;</em>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;班次：<em title=\"R1:org_short_name\">&lt;**&gt;</em></h6><p><em title=\"R1:org_short_name\">----------------------------------</em></p><table border=\"0\" style=\"line-height: 22.3999996185303px;\"><tbody><tr><td align=\"center\" style=\"text-align: center;\"><h5 style=\"text-align: center;\"><strong>菜品名称&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</strong></h5></td><td style=\"text-align: center;\"><h5><strong>单价</strong></h5></td><td style=\"text-align: center;\"><h5><strong>数量</strong></h5></td><td style=\"text-align: center;\"><h5><strong>金额</strong></h5></td></tr><tr><td align=\"center\" style=\"text-align: center;\"><em title=\"R1:top_org_id\">&lt;**&gt;</em><br></td><td style=\"text-align: center;\"><em title=\"R1:manage_type\">&lt;**&gt;</em></td><td style=\"text-align: center;\"><em title=\"R1:phone\">&lt;**&gt;</em></td><td style=\"text-align: center;\"><em title=\"R1:fax\">&lt;**&gt;</em></td></tr><tr><td align=\"center\" style=\"text-align: center;\"><em title=\"R1:top_org_id\">&lt;**&gt;</em><br></td><td style=\"text-align: center;\"><em title=\"R1:manage_type\">&lt;**&gt;</em></td><td style=\"text-align: center;\"><em title=\"R1:phone\">&lt;**&gt;</em></td><td style=\"text-align: center;\"><em title=\"R1:fax\">&lt;**&gt;</em></td></tr><tr><td align=\"center\" style=\"text-align: center;\"><em title=\"R1:top_org_id\">&lt;**&gt;</em><br></td><td style=\"text-align: center;\"><em title=\"R1:manage_type\">&lt;**&gt;</em></td><td style=\"text-align: center;\"><em title=\"R1:phone\">&lt;**&gt;</em></td><td style=\"text-align: center;\"><em title=\"R1:fax\">&lt;**&gt;</em></td></tr></tbody></table><p>----------------------------------</p><h5>地址：xxxxxxxxxxxxxxxxxxxxx</h5><h5>电话：xxxxxxxxxxx</h5><!--EndFragment-->";
		Document document = Jsoup.parse(d);
		System.out.println(document.toString());
	}
}
