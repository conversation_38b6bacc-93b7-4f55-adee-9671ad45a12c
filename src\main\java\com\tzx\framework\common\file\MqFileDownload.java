package com.tzx.framework.common.file;

import java.io.BufferedInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InterruptedIOException;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLConnection;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.net.ssl.SSLException;
import javax.net.ssl.SSLHandshakeException;

import org.apache.commons.httpclient.ConnectTimeoutException;
import org.apache.commons.httpclient.NoHttpResponseException;
import org.apache.commons.lang.ArrayUtils;
import org.apache.http.HttpEntityEnclosingRequest;
import org.apache.http.HttpRequest;
import org.apache.http.client.HttpRequestRetryHandler;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.protocol.HttpClientContext;
import org.apache.http.config.ConnectionConfig;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.protocol.BasicHttpContext;
import org.apache.http.protocol.HttpContext;
import org.apache.log4j.Logger;

import com.tzx.framework.common.util.io.ByteUtil;

/**
 * mq消息下载
 * @Description
 * @param
 * @return
 * exception
 * <AUTHOR>
 * @version
 * @see
 */
public class MqFileDownload {
	/**下载超时时间*/
	private static final int time_out_millisecond = 60 * 1000 ;
	/**连接失败重试次数*/
	public static final int try_times = 3;
	/**分批次下载*/
	private static int batch_len = 10;
	/**每次下载的字节数 kb*/
	public static final int batch_down_size_kb = 8 * 1024 ;
//	private static final int batch_down_size_kb = 2 * 1024 * 1024 ;
	/**编码格式*/
	private static final String encode_utf8 = "UTF-8";
	
	
	private static Logger logger = Logger.getLogger(MqFileDownload.class.getName());
	private static Logger tipLogger	= Logger.getLogger("tip");
	
	private static CloseableHttpClient httpClient = null ;
	private static HttpContext context = null;
	
	static{
		if(httpClient == null){
			PoolingHttpClientConnectionManager cm = new PoolingHttpClientConnectionManager();
			cm.setMaxTotal(100);
			
			HttpRequestRetryHandler retryHandler = new HttpRequestRetryHandler() {
				@Override
				public boolean retryRequest(IOException exception,
	                    int executionCount, HttpContext context) {
					if( executionCount >= 5 ){// 如果已经重试了5次，就放弃
						return false;
					}
					if (exception instanceof NoHttpResponseException) {// 如果服务器丢掉了连接，那么就重试
	                    return true;
	                }
	                if (exception instanceof SSLHandshakeException) {// 不要重试SSL握手异常
	                    return false;
	                }
	                if (exception instanceof InterruptedIOException) {// 超时
	                    return false;
	                }
	                if (exception instanceof UnknownHostException) {// 目标服务器不可达
	                    return false;
	                }
	                if (exception instanceof ConnectTimeoutException) {// 连接被拒绝
	                    return false;
	                }
	                if (exception instanceof SSLException) {// SSL握手异常
	                    return false;
	                }

	                HttpClientContext clientContext = HttpClientContext
	                        .adapt(context);
	                HttpRequest request = clientContext.getRequest();
	                // 如果请求是幂等的，就再次尝试
	                if (!(request instanceof HttpEntityEnclosingRequest)) {
	                    return true;
	                }
					return false;
				}
			};
			
			
			
			httpClient = HttpClients.custom()
					.setConnectionManager(cm)
					.setRetryHandler(retryHandler)
					.build();
			
		}
		if(context == null){
		    context =  new BasicHttpContext();
		}
	}
	
	/**
	 * 下载mq消息文件内容
	 * @param url
	 * @param decompress 是否解码
	 * @return
	 * @throws IOException 
	 * @throws MalformedURLException 
	 */
	public static String download(String url , boolean decompress) throws IOException{
		String tmpStr = null;
		//下载文件流
		byte [] bs =  null;
		int i = 0;
		//下载文件流，如果出错，尝试 重试的次数
		while( bs == null && i < try_times){
			bs = downloadBytes(url);
			i++;
		}
		//解码
		if( decompress ){
			byte[] decompressByte = CompressAndDecompress.decompressBytes(bs);
			
			//转换成字符串
			tmpStr = new String(decompressByte,encode_utf8);
		} else {
			tmpStr = new String(bs,encode_utf8);
		}
		
		return tmpStr;
	}
	
	/**
	 * 下载文件
	 * @param url
	 * @return
	 * @throws IOException
	 */
	public static String download(String url)  throws IOException {
		return download(url, false);
	}
	
	/**
	 * 从地址下载文件流
	 * 参考 https://my.oschina.net/zmf/blog/336961
	 * @param url
	 * @return
	 * @throws IOException 
	 * @throws MalformedURLException 
	 */
	public static byte[] downloadBytes(String url) {
		byte[] bs = null;
		
		try {
			HttpGet httpGet = new HttpGet(url);
			//设置请求超时时间
			RequestConfig requestConfig = RequestConfig.custom()
					.setConnectionRequestTimeout(time_out_millisecond) //设置从connect Manager获取Connection 超时时间，单位毫秒。
					.setSocketTimeout(time_out_millisecond)  //请求获取数据的超时时间，单位毫秒
					.setConnectTimeout(time_out_millisecond)//设置连接超时时间，单位毫秒。
					.build();
			httpGet.setConfig(requestConfig);
			
			CloseableHttpResponse response = httpClient.execute(httpGet,context);
			
			
			System.out.println("len:"+response.getEntity().getContentLength());
			
			
			BufferedInputStream bis = new BufferedInputStream(response.getEntity().getContent());
				
			bs = ByteUtil.getBytes(bis);
				
			bis.close();
			
		} catch (Exception e) {
			e.printStackTrace();
		}
		return bs;
	}
	
	/**
	 * 下载mq消息转换成字符串
	 * @param url 下载文件路径
	 * @param fileSize 文件大小
	 * @param decompress 是否需要解压缩
	 * @return
	 */
	public static String downloadMqStrByBlock(String url,int fileSize, boolean decompress){
		String tmpStr = null;
		//byte [] bs = downloadBytesBlock(url, fileSize);
		byte [] bs = downloadBytesBlock(url);
		//文件下载失败
		if(bs == null || bs.length == 0){
			logger.error("下载文件失败!url:"+url);
		}
		try {
			if( decompress ){
				byte[] decompressByte = CompressAndDecompress.decompressBytes(bs);
				tmpStr = new String(decompressByte,encode_utf8);
			} else {
				tmpStr = new String(bs,encode_utf8);
			}
			
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
			logger.error("下载失败!url:"+url);
		}
		logger.info("文件转换为字符串成功，文件地址："+url);
		tipLogger.info("文件转换成功。文件地址："+url);
		return tmpStr;
	}
	
	/**
	 * 分块下载文件
	 * @param url
	 * @param fileSize
	 * @return
	 */
	private static byte[] downloadBytesBlock(String url,int fileSize){
		
		ByteArrayOutputStream baos = new ByteArrayOutputStream();
		//通过分批次下载
//		int per_down = fileSize / batch_len ;
		//通过每次下载量固定
		int per_down = batch_down_size_kb > fileSize ? fileSize : batch_down_size_kb;
		batch_len = fileSize / per_down;
		InputStream in = null;
		for(int i = 0 ;  i < batch_len ; i++ ){
			Date now = new Date();
			try {
				if( i == batch_len - 1){
					in = tryDownloadBytes(url, i * per_down,fileSize - 1 , try_times);
				} else {
					in = tryDownloadBytes(url, i * per_down, i * per_down + per_down - 1 , try_times);
				}
				if(in == null){
					logger.error("下载尝试"+ try_times +"次后未成功,请检查网络情况或者请求地址是否可访问,请求地址如下:"+url);
				}
			
	    	    baos.write(ByteUtil.getBytes(in));
			} catch (IOException e) {
				e.printStackTrace();
				//如果发生错误，则将文件流设置为空
				baos = new ByteArrayOutputStream();
			}
			
			logger.info("下载耗时:"+ (new Date().getTime() - now.getTime()) +" ms");	
		}
		
		
		byte[] bs = baos.toByteArray();
		try {
			baos.close();
		} catch (IOException e) {
		}
		return bs;
	}
	
	/**
	 * 分块下载文件（方法重载）
	 * @param url
	 * @return
	 */
	private static byte[] downloadBytesBlock(String mqUrl){	
		logger.info("全量方式下载，下载地址："+mqUrl);
		tipLogger.info("开始下载数据，下载地址："+mqUrl);
		byte[] getData=null;
        try {
        	URL url = new URL(mqUrl);
        	HttpURLConnection conn = (HttpURLConnection)url.openConnection();
            InputStream inStream = conn.getInputStream();      
            getData = readInputStream(inStream);             
        } catch (Exception e) {
            e.printStackTrace();
        }
        logger.info("数据下载完成。");
        tipLogger.info("下载数据完成。下载地址："+mqUrl);
        return getData;
	}
	
	/** 
	 * 从输入流中获取字节数组 
	 *  
	 * @param inputStream 
	 * @return 
	 * @throws IOException 
	 */  
	public static byte[] readInputStream(InputStream inputStream) throws IOException {  
	    byte[] buffer = new byte[1024];  
	    int len = 0;  
	    ByteArrayOutputStream bos = new ByteArrayOutputStream();  
	    while ((len = inputStream.read(buffer)) != -1) {  
	        bos.write(buffer, 0, len);  
	    }  
	    bos.close();  
	    return bos.toByteArray();  
	}  
	
	/**
	 * 尝试下载文件流
	 * @param url
	 * @param offset
	 * @param end
	 * @param tryTimes
	 * @return
	 * @throws IOException 
	 */
	public static InputStream tryDownloadBytes(String url,int offset , int end,int tryTimes) throws IOException{
		InputStream in = null;
		int i = 0;
		while ( in == null && i < tryTimes){
			
			try {
				switch (i) {
					case 0: //第一次下载
						in = downloadBytes(url, offset, end);
						break;
					default ://下载重试，重试时候减少每次请求数量
						in = downloadBytes(url, offset, end, i+1);
						break;
				}
			} catch (Exception e1) {
				e1.printStackTrace();
			}
			
			
//			if(in == null){
//				//重试等待时间 等待10s后重试
//				try {
//					Thread.sleep(10 * 1000);
//				} catch (InterruptedException e) {
//					e.printStackTrace();
//				}
//			}
			
			i++;
		}
		
		return in;
	}
	
	
	/**
	 * 从地址下载文件流
	 * 参考 https://my.oschina.net/zmf/blog/336961
	 * @param url
	 * @param offset 起始地址   从0开始
 	 * @param end 结束地址  从len-1结束 
	 * @return
	 * @throws IOException 
	 * @throws MalformedURLException 
	 */
	public static InputStream downloadBytes(String url,int offset , int end) {
		InputStream in = null;
		
		try {
			
			HttpGet httpGet = new HttpGet(url);

			//获取指定长度
			httpGet.addHeader("Range", "bytes=" + offset + "-" + end);

			logger.info("下载offset:"+offset +",end:"+end);

			CloseableHttpResponse response = httpClient.execute(httpGet,context);


			in = new BufferedInputStream(response.getEntity().getContent());
				
		} catch (Exception e) {
			e.printStackTrace();
		}
		return in;
	}
	
	
	/**
	 * 从地址下载文件流
	 * 参考 https://my.oschina.net/zmf/blog/336961
	 * @param url
	 * @param offset 起始地址   从0开始
 	 * @param end 结束地址  从len-1结束 
 	 * @param batchDownTimes 分几次下载
	 * @return
	 * @throws IOException 
	 * @throws MalformedURLException 
	 */
	private static InputStream downloadBytes(String url,int offset , int end,int batchDownTimes) throws IOException {
		InputStream in = null;
		
//		//
//		if(batchDownTimes == 2){
//			return in;
//		}
		
		logger.info("尝试第"+batchDownTimes+"次下载...");
		List<byte[]> bsList = new ArrayList<>();
		//通过每次下载量固定
		int per_down = ( end - offset ) / batchDownTimes ;
		for(int i = 0 ;i < batchDownTimes ; i++){
			if( i == batchDownTimes - 1){
				in = downloadBytes(url, i * per_down + offset ,end);
				bsList.add(ByteUtil.getBytes(in));
			} else {
				in = downloadBytes(url, i * per_down + offset , i * per_down + per_down - 1 + offset);
				bsList.add(ByteUtil.getBytes(in));
			}
		}
		
		byte [] union = new byte[0] ;
		for(byte[] bs : bsList){
			union = ArrayUtils.addAll(union, bs);
		}
		in = ByteUtil.getIsFromBytes(union);
		
		return in;
	}
	
	
}
