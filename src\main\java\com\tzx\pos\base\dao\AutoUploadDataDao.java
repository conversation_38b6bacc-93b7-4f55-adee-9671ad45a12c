package com.tzx.pos.base.dao;

import java.util.Date;
import java.util.List;

import net.sf.json.JSONObject;

import com.tzx.framework.common.util.dao.GenericDao;

public interface AutoUploadDataDao extends GenericDao
{
	String NAME = "com.tzx.pos.base.dao.imp.AutoUploadDataDaoImp";
	
	/** 查询系统参数
	 * @param tenancyId
	 * @param storeId
	 * @param para
	 * @return
	 * @throws Exception
	 */
	String getSysParameter(String tenancyId,int storeId,String para) throws Exception;
	
	/** 查询门店系统参数
	 * @param tenancyId
	 * @param storeId
	 * @param para
	 * @return
	 * @throws Exception
	 */
	String getPosParameter(String tenancyId,int storeId,String para) throws Exception;
	
	/** 查询门店上传数据
	 * @param tenancyId
	 * @param storeId
	 * @param tableName
	 * @param param
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> getPosUploadDataItem(String tenancyId,int storeId,String tableName,JSONObject param) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param tableName
	 * @param param
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> getPosAutoUploadDataItem(String tenancyId,int storeId,String tableName,JSONObject param,String isUploadOpenBill) throws Exception;
	
	/** 查询机台操作状态
	 * @param tenancyId
	 * @param storeId
	 * @param param
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> getPosOptState(String tenancyId,int storeId,JSONObject param) throws Exception;
	
	/** 修改门店数据上传状态
	 * 
	 * @see 对于实时上传的表，要注意未结单的状态，上传未结单不要更改上传标记，等结账了上传再修改
	 * @param tenancyId
	 * @param storeId
	 * @param tableName
	 * @param param
	 * @param t
	 * @return
	 * @throws Exception
	 */
	int[] updatePosItemUploadTag(String tenancyId,int storeId,String tableName,List<JSONObject> param,String cond) throws Exception;
	
	/** 更新自动上传时间
	 * @param tenancyId
	 * @param storeId
	 * @return
	 * @throws Exception
	 */
	boolean updatePosUploadDate(String tenancyId,int storeId) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @return
	 * @throws Exception
	 */
	public Date getCurrentReportDate(String tenancyId,int storeId) throws Exception;
	
	
	/** 查询异常打印机
	 * @param tenancyId
	 * @param storeId
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public String getPrintNewData(String tenancyId,int storeId) throws Exception;
}
