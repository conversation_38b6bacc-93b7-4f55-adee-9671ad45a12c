package com.tzx.member.crm.bo;

import com.tzx.framework.common.entity.Data;
import com.tzx.member.common.entity.PosCustomerOperateListEntity;

import net.sf.json.JSONObject;

/**
 * 会员发卡接口实现
 * 
 * <AUTHOR> email:she<PERSON><PERSON><PERSON>@tzx.com.cn
 * @version 1.0 2017-10-25
 * @see
 * @since JDK7.0
 * @update
 */
public interface CustomerCardActivationService extends CustomerService{
	String	NAME	= "com.tzx.member.crm.bo.imp.CustomerCardActivationServiceImp";
	
	
	/**
	 * 记录开卡会员操作流水
	 * @param tenancyId
	 * @param storeId
	 * @param paramJson
	 * @throws Exception
	 */
	public String cardActivationCustomerOperateList(String tenancyId, int storeId, JSONObject paramJson) throws Exception;

	/**
	 * 请求会员发卡接口
	 * @param tenancyId
	 * @param storeId
	 * @param paramJson
	 */
	public Data customerCardActivationPost(String tenancyId, int storeId, JSONObject paramJson) throws Exception;

	/**请求会员发卡接口
	 * @param tenancyId
	 * @param storeId
	 * @param data
	 * @param printJson
	 * @return
	 * @throws Exception
	 */
	public Data customerCardActivationPost(String tenancyId, int storeId, JSONObject data, JSONObject printJson) throws Exception;
	
	/**
	 * 发卡状态查询
	 * @param tenancyId
	 * @param storeId
	 * @param requestData
	 * @param resData
	 * @throws Exception
	 */
	public Data customerCardActivationStatus(String tenancyId, int storeId,JSONObject paramJson) throws Exception;
	
	/**
	 * 获取二维码
	 * @param params
	 * @param path 路径
	 */
	public Data customerCardThirdPaymentPrecreate(Data params, String path) throws Exception;
	
	/**
	 * 取消支付
	 * @param tenancyId
	 * @param storeId
	 * @param paramJson
	 * @return
	 * @throws Exception
	 */
	public Data customerCardCancelThirdPayment(String tenancyId, int storeId, JSONObject paramJson) throws Exception;
	
	/**
	 * 被扫支付
	 * @param params
	 * @return
	 * @throws Exception
	 */
	public Data customerCardThirdPaymentBarcode(Data params) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param paramJson
	 * @return
	 * @throws Exception
	 */
	public PosCustomerOperateListEntity queryCustomerOperateListForCardActivation(String tenancyId, int storeId, JSONObject paramJson) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param paramJson
	 * @throws Exception
	 */
	public void updateCustomerOperateListStateForCardActivation(String tenancyId, int storeId, JSONObject paramJson) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param customerOperate
	 * @throws Exception
	 */
	public void queryCustomerCardActivationStatusForCRM(String tenancyId, int storeId, PosCustomerOperateListEntity customerOperate) throws Exception;
}
