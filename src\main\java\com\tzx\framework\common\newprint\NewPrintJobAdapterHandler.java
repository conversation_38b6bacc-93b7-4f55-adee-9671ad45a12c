package com.tzx.framework.common.newprint;

import java.util.Map;

import javax.print.event.PrintJobAdapter;
import javax.print.event.PrintJobEvent;

import org.apache.log4j.Logger;

import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.GenericDao;


public class NewPrintJobAdapterHandler extends PrintJobAdapter
{
	private Logger	logger	= Logger.getLogger(NewPrintJobAdapterHandler.class);

	private int		taskId;

	public NewPrintJobAdapterHandler(int taskId)
	{
		super();
		this.taskId = taskId;
	}

	public int getTaskId()
	{
		return taskId;
	}

	public void setTaskId(int taskId)
	{
		this.taskId = taskId;
	}

	@Override
	public void printDataTransferCompleted(PrintJobEvent pje)
	{
		try
		{
			Map<String, String> systemMap = Constant.getSystemMap();
			String tenantId = systemMap.get("tenent_id");

			GenericDao dao = (GenericDao) SpringConext.getApplicationContext().getBean("genericDaoImpl");

			// 把打印表pos_print表里的打印状态更为null，默认是*未打印
			String uPitem = new String("update pos_print set print_tag = ? where id = ?");
			dao.getJdbcTemplate(tenantId).update(uPitem, new Object[]
			{ null, this.taskId });
		}
		catch (Throwable e)
		{
			logger.error("修改打印状态失败:",e);
		}
	}

	@Override
	public void printJobFailed(PrintJobEvent pje)
	{
		logger.error("打印失败：厨打任务ID 为 【" + this.taskId + "】");
	}
	
	
	@Override
	public void printJobCanceled(PrintJobEvent paramPrintJobEvent)
	{
		logger.error("打印被取消：厨打任务ID 为 【" + this.taskId + "】");
		
		try
		{
			Map<String, String> systemMap = Constant.getSystemMap();

			String tenantId = systemMap.get("tenent_id");

			GenericDao dao = (GenericDao) SpringConext.getApplicationContext().getBean("genericDaoImpl");

			// 把打印表pos_print表里的打印状态更为null，默认是*未打印
			String uPitem = new String("update pos_print set print_tag = ? where id = ?");
			dao.getJdbcTemplate(tenantId).update(uPitem, new Object[]
			{ "*", this.taskId });
		}
		catch (Throwable e)
		{
			logger.error("修改打印状态失败:",e);
		}
	}
	
	
}
