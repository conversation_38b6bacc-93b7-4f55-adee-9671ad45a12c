package com.tzx.pos.bo.imp;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.tzx.base.bo.PosCodeService;
import com.tzx.framework.common.constant.Code;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.DoubleHelper;
import com.tzx.framework.common.util.Tools;
import com.tzx.pos.asyn.thread.OrderingPrintThread;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.bo.TakeOrderingDishService;
import com.tzx.pos.bo.TakeOrderingRefuseService;
import com.tzx.pos.bo.dto.TakeOrderingParam;
import com.tzx.pos.po.springjdbc.dao.PosDishDao;

@Service(TakeOrderingDishService.NAME)
public class TakeOrderingDishServiceImp extends PosBaseServiceImp implements TakeOrderingDishService {
	@Autowired 
	private PosDishDao posDishDao;
	@Autowired 
	PosCodeService codeService;
	@Autowired 
	private TakeOrderingRefuseService takeOrderingRefuseService;
	
	private static final Logger logger = Logger.getLogger(TakeOrderingDishServiceImp.class);

    /**
     * 下单
     * @param param
     * @return
     * @throws Exception 
     */
	 public TakeOrderingParam insertOrderDish(TakeOrderingParam params,Data result) throws Exception{
		logger.info(Thread.currentThread().getId()+"@-----订单转账单下单开始--  params============="+params.toString());
		String tenantId =params.getTenantId();
		Integer organId = params.getOrganId();
		
		Date reportDate = params.getReportDate();
		String posNum = params.getPosNum();
		String billno = params.getBillNum();
		List<Map<String, Object>> item = params.getItems();
		Integer shiftId = params.getShiftId();
		String optNum = params.getOptNum();
		String mode = params.getMode();
		String saleMode = params.getSaleMode();
		
		//判断菜品集合
		if (item.size() == 0){
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_ITEM_LIST);
		}
		
		int scale =2;
		List<Map<String, Object>> meallistItems = new ArrayList<Map<String,Object>>();
		for(Map<String, Object> oDish : item) {
			String itemProperty = ParamUtil.getStringValue(oDish, "item_property", true, PosErrorCode.NOT_NULL_ITEM_PROPERTY);
			Double itemCount = ParamUtil.getDoubleValue(oDish, "item_count", false, null);
			Double itemPrice = ParamUtil.getDoubleValue(oDish, "item_price", false, null);

			if(itemCount.isNaN() || itemCount<=0){
				itemCount = 1d;
			}

			if(SysDictionary.ITEM_PROPERTY_MEALLIST.equals(itemProperty)){
				meallistItems.add(oDish);
			}

			if(SysDictionary.ITEM_PROPERTY_SETMEAL.equals(itemProperty)){
				List<Map<String, Object>> setmealItemList = new ArrayList<Map<String,Object>>();
				Double meallistItemPriceTotal =0d;
				Map<String, Object> maxItem = null;
				for (Map<String, Object> meallistItem : item){
					String detailsItemProperty = ParamUtil.getStringValue(meallistItem, "item_property", true, PosErrorCode.NOT_NULL_ITEM_PROPERTY);
					Double detailsItemCount = ParamUtil.getDoubleValue(meallistItem, "item_count", false, null);
					Double detailsItemPrice = ParamUtil.getDoubleValue(meallistItem, "item_price", false, null);

					if(detailsItemCount.isNaN() || detailsItemCount<=0){
						detailsItemCount = 1d;
					}

					if(detailsItemPrice.isNaN()){
						detailsItemPrice = 0d;
					}

					if (SysDictionary.ITEM_PROPERTY_MEALLIST.equals(detailsItemProperty)){
						Double assistNum = DoubleHelper.div(detailsItemCount, itemCount, scale);
						detailsItemPrice = DoubleHelper.mul(detailsItemPrice, DoubleHelper.div(detailsItemCount, assistNum, scale), scale);
						meallistItemPriceTotal = DoubleHelper.add(meallistItemPriceTotal, detailsItemPrice, scale);
 						meallistItem.put("item_price", detailsItemPrice);
						meallistItem.put("assist_num", assistNum);
						setmealItemList.add(meallistItem);

						if(null !=maxItem){
							Double maxItemPrice = ParamUtil.getDoubleValue(maxItem, "item_price", false, null);
							if(detailsItemPrice>maxItemPrice){
								maxItem = meallistItem;
							}
						}
						else{
							maxItem = meallistItem;
						}
					}
				}

				double detailsItemPriceTotal =0d;
				for(Map<String, Object> meallistItem : setmealItemList){
					Double detailsItemPrice = ParamUtil.getDoubleValue(meallistItem, "item_price", false, null);
					if(meallistItemPriceTotal >0){
						detailsItemPrice = DoubleHelper.mul(DoubleHelper.div(itemPrice, meallistItemPriceTotal, 4), detailsItemPrice, scale);
					}
					else{
						detailsItemPrice = 0d;
					}

					detailsItemPriceTotal = DoubleHelper.add(detailsItemPriceTotal, detailsItemPrice, scale);
					meallistItem.put("item_price", detailsItemPrice);
				}

				if(detailsItemPriceTotal!=itemPrice){
					Double detailsItemPrice = ParamUtil.getDoubleValue(maxItem, "item_price", false, null);
					detailsItemPrice = DoubleHelper.add(detailsItemPrice, DoubleHelper.sub(itemPrice, detailsItemPriceTotal, scale), scale);
					maxItem.put("item_price", detailsItemPrice);
				}
			}
		}
			
        //查询菜品是否估清
		this.getPosSold(params,result);
		
		//获取当前系统时间
		Timestamp currentTime = DateUtil.currentTimestamp();
		
		String orderRemark = String.valueOf(currentTime.getTime());
		params.setCreateTime(currentTime);
		//插入点菜数据
		this.insertBillItem(params);
		
		//计算账单金额
		if(params.getDiscountkAmount() !=0 && params.getDiscountkAmount() != null){
			this.discountk(organId, billno, params.getDiscountkAmount(), params.getDiscountList());
		}
		this.calcAmountTake(params);

		// 查询账单明细,组织打印参数
		List<JSONObject> rwidList = posDishDao.getRwidsWithoutWaitCallItem(tenantId, organId, billno, orderRemark,posNum);
		
		String rwids = "";
		List<Integer> rwidSet = new ArrayList<Integer>();
		for (JSONObject rwidJson : rwidList){
			Integer rwid = rwidJson.optInt("rwid");
			// 等叫和不等叫菜品全部拼接，在新打印中有是否等叫菜品打印的判断
			if (!Tools.isNullOrEmpty(rwids)){
				rwids += ",";
			}
			rwids += String.valueOf(rwid);
			rwidSet.add(rwid);
		}
		
		Timestamp time = DateUtil.currentTimestamp();
		JSONObject json = new JSONObject();
		// 厨打
		json.put("tenancy_id", tenantId);
		json.put("store_id", organId);
		json.put("mode", mode);
		json.put("bill_num", billno);
		json.put("is_print", "Y");
		json.put("item_time", DateUtil.format(time));
		json.put("rwids", rwids);
		json.put("pos_num", posNum);
		json.put("opt_num", optNum);
		json.put("report_date", DateUtil.format(reportDate));
		json.put("shift_id", shiftId);
        String isPrintMenu = posDishDao.getSysParameter(tenantId, organId, "is_print_menu");

		if ("1".equals(isPrintMenu)){
			json.put("is_print_order", "Y");
            json.put("pos_num", posNum);
			json.put("opt_num", optNum);
			json.put("report_date", DateUtil.format(reportDate));
			json.put("shift_id", shiftId);
			json.put("print_type", "");
			json.put("order_remark", "");
			json.put("mode", "0");
			json.put("print_code", SysDictionary.PRINT_CODE_1103);
		
	    }else{
		    json.put("is_print_order", "Y");
	    }
		
		//厨房打印
		posDishDao.savePosLog(tenantId, organId, posNum, optNum, optNum, shiftId, reportDate, Constant.TITLE, "暂存", "账单编号:" + billno, "改写账单的属性改为卧单！");
		
		//组织厨打json
		//params.setPrintJson(json);
		//数据上传
		upload(tenantId,organId+"","","","",billno);
		this.billPrint(json);
		return params;
	}
	
	/**
	 * 获取菜品规格
	 * @param tenantId
	 * @param organId
	 * @param item
	 * @return
	 * @throws Exception
	 */
	public Map<String,JSONObject> getItemUnitMap(String tenantId, Integer organId, List<Map<String, Object>>  item) throws Exception{
		List<JSONObject> itemUnitList = posDishDao.getHqItemUnitByItem(tenantId, organId, item);
		Map<String, JSONObject> itemUnitMap = new HashMap<String, JSONObject>();
		for (JSONObject itemUnitJson : itemUnitList){
			itemUnitMap.put(itemUnitJson.optString("unit_id"), itemUnitJson);
		}
		return itemUnitMap;
	}
	
	/**
	 * 组织账单数据
	 * @param params
	 * @throws Exception
	 */
	public void insertBillItem(TakeOrderingParam params) throws Exception{
		String tenantId =params.getTenantId();
		Integer organId = params.getOrganId();
		String tableCode = params.getTableCode();
		String chanel = params.getChanel();
		String billCode = params.getBillCode();
		Date reportDate = params.getReportDate();
		String posNum = params.getPosNum();
		String billno = params.getBillNum();
		List<Map<String, Object>> item = params.getItems();
		Integer shiftId = params.getShiftId();
		String optNum = params.getOptNum();
		String mode = params.getMode();
		String saleMode = params.getSaleMode();
		Timestamp currentTime = params.getCreateTime();
		logger.info(Thread.currentThread().getId()+"@-----订单转账单下单  组织整单数据开始--  tenantId=+"+tenantId+"----organId="+organId+"----tableCode="+tableCode+"----chanel="+chanel+"----shiftId="+shiftId+"----posNum="+posNum+"----optNum="+optNum+"----mode="+mode+"----item="+item.toString());
		
		String orderRemark = String.valueOf(currentTime.getTime());
		
		JSONObject billJson = posDishDao.getPosBillByBillnum(tenantId, organId, billno);//根据账单号查询整单信息
		String bill_property = null;
		String batch_num = null;
		String payment_state = null;
		int discount_mode_id = 0;
		Double discount_rate = 0d;
		
		// Double payment_amount = 0d;
		if (null != billJson){
			payment_state = billJson.optString("payment_state");
			bill_property = billJson.optString("bill_property");
			batch_num = billJson.optString("batch_num");
			discount_mode_id = billJson.optInt("discount_mode_id");
			discount_rate = billJson.optDouble("discount_rate");
			
			if(Tools.isNullOrEmpty(saleMode)){
				saleMode = billJson.optString("sale_mode");
			}

			if (Tools.isNullOrEmpty(saleMode)){
				saleMode = SysDictionary.SALE_MODE_TS01;
			}

			if (discount_rate == 0d || discount_rate == 0){
				discount_rate = 100d;
			}
		}
        
		if (SysDictionary.PAYMENT_STATE_PAY.equals(payment_state)){
			throw SystemException.getInstance(PosErrorCode.BILL_PAYMENT_STATE_PAYING_ERROR);
		}
		
		// 获取菜品规格信息
		Map<String, JSONObject> itemUnitMap = getItemUnitMap(tenantId, organId, item);
		String formatState = "";
		String formatmode = "";
		try{
			
			JSONObject org = posDishDao.getOrganById(tenantId, organId);
			if (null != org && !org.isEmpty()){
				formatState = org.optString("format_state");
			}

			formatmode = posDishDao.getSysParameter(tenantId, organId, "ZCDCMS");
			if ("1".equals(formatState) && !"02".equals(formatmode)){
				JSONObject object = new JSONObject();
				object.element("store_id", organId);
				object.element("busi_date", DateUtil.format(reportDate));
				object.put("pos_num", posNum);
				batch_num = codeService.getCode(tenantId, Code.POS_BATCH_NUM, object);// 调用统一接口来实现
			}
		}
		catch (Exception e){
			logger.info("点菜：" + ExceptionMessage.getExceptionMessage(e));
			throw new SystemException(PosErrorCode.GET_BILLNUM_SERVICE_ERROR);
		}
		List<JSONObject> itemInfoList = posDishDao.getHqItemInfoByItem(tenantId, organId, chanel, item);
		Map<String, JSONObject> itemInfoMap = new HashMap<String, JSONObject>();
		for (JSONObject itemInfoJson : itemInfoList)
		{
			itemInfoMap.put(itemInfoJson.optString("item_id"), itemInfoJson);
		}
		List<Object[]> billItemList = new ArrayList<Object[]>();
		Map<String, Map<String, Object>> itemMap = new HashMap<String, Map<String, Object>>();
		List<JSONObject> discountList = new ArrayList<JSONObject>();
		for (int k = 0; k < item.size(); k++){
			int orderNumber = k + 1;
            
			Map<String, Object> detail = (Map<String, Object>) item.get(k);
			
			itemMap.put(String.valueOf(orderNumber), detail);

			Integer itemSerial = ParamUtil.getIntegerValue(detail, "item_serial", false, null);

			Integer item_id = ParamUtil.getIntegerValue(detail, "item_id", false, null);

			String item_num = String.valueOf(currentTime.getTime());

			String item_name = ParamUtil.getStringValue(detail, "item_name", false, null);

			String item_unit_name = ParamUtil.getStringValue(detail, "item_unit_name", false, null);

			Integer itemUnitId = ParamUtil.getIntegerValue(detail, "unit_id", true, PosErrorCode.NOT_NULL_DISH_UNITID);

			Integer reason_id = ParamUtil.getIntegerValue(detail, "reason_id", false, null);

			Integer assist_item_id = ParamUtil.getIntegerValue(detail, "assist_item_id", false, null);

			String item_remark = ParamUtil.getStringValue(detail, "item_remark", false, null);
			String billTaste = ParamUtil.getStringValue(detail, "item_taste", false, null);

			String item_property = ParamUtil.getStringValue(detail, "item_property", true, PosErrorCode.NOT_NULL_ITEM_PROPERTY);
			// 子菜品不用填
			String sale_mode = ParamUtil.getStringValue(detail, "sale_mode", false, null);
			//奉送的批准人员manager_num字段
			String managerNum = ParamUtil.getStringValue(detail, "manager_num", false, null);
			//折扣率                                                                                                                          
			Double discountRate = ParamUtil.getDoubleValue(detail, "discount_rate", false, null);
			//折扣原因id
			Integer discountReasonId = ParamUtil.getIntegerValue(detail, "discount_reason_id", false, null);
			//折扣方式id
			Integer discountModeId = ParamUtil.getIntegerValue(detail, "discount_mode_id", false, null);
			Double discountAmount = ParamUtil.getDoubleValue(detail, "discount_amount", false, null);
			Double discountrAmount = ParamUtil.getDoubleValue(detail, "discountr_amount", false, null);
			JSONObject b = new JSONObject();
			b.put("item_id", item_id);
			b.put("discount_amount", discountAmount);
			b.put("discountr_amount", discountrAmount);
			discountList.add(b);
			params.setDiscountList(discountList);
			if(discountRate.isNaN()){
				discountRate = 100d;
			}
			if(null == discountReasonId){
				discountReasonId = 0;
			} 
			if(null == discountModeId){
				discountModeId = 0;
			}

			Integer waitcall_tag = ParamUtil.getIntegerValue(detail, "waitcall_tag", false, null);
			if (Tools.isNullOrEmpty(waitcall_tag)){
				waitcall_tag = 0;
			}

			double itemPrice = 0d;
			if (Tools.isNullOrEmpty(detail.get("item_price")) == false){
				itemPrice = Double.parseDouble(detail.get("item_price").toString());
			}
			else{
				throw new SystemException(PosErrorCode.NOT_NULL_DISH_ITEM_PRICE);
			}

			// 菜目数量默认显示为1，防止总部套餐明细有时设置为0的情况
			double item_count = 1; 
			if (Tools.isNullOrEmpty(detail.get("item_count")) == false){
				item_count = Double.parseDouble(detail.get("item_count").toString());
				item_count = item_count == 0 ? 1 : item_count;
			}
			else{
				throw new SystemException(PosErrorCode.NOT_NULL_DISH_ITEM_COUNT);
			}
			Double assist_num = 0d;
			if (Tools.isNullOrEmpty(detail.get("assist_num")) == false){
				assist_num = Double.parseDouble(detail.get("assist_num").toString());
			}

			int seat_num = 0;
			if (Tools.isNullOrEmpty(detail.get("seat_num")) == false){
				seat_num = Integer.parseInt(detail.get("seat_num").toString());
			}

			Integer setmeal_rwid = null;
			if (Tools.isNullOrEmpty(detail.get("setmeal_rwid")) == false){
				setmeal_rwid = Integer.parseInt(detail.get("setmeal_rwid").toString());
			}

			Integer setmeal_id = null;
			if (SysDictionary.ITEM_PROPERTY_SETMEAL.equalsIgnoreCase(item_property) || SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item_property)){
				setmeal_id = ParamUtil.getIntegerValue(detail, "setmeal_id", false, null);
			}
 
			String discount_state = null;
			Double proportion = 0d;
			Integer item_class_id = 0;
			String item_english = null;
			String pushmoney_way = null;
			Integer details_id = 0;

			JSONObject itemUnitJson = null;
			if (itemUnitMap.containsKey(String.valueOf(itemUnitId))){
				itemUnitJson = itemUnitMap.get(String.valueOf(itemUnitId));
			}
			
			if (null == itemUnitJson || itemUnitJson.isEmpty()){
				throw new SystemException(PosErrorCode.NOT_EXISTS_DISH_UNIT);
			}
			
			// 获取菜品信息
			JSONObject itemInfoJson = null;
			if (itemInfoMap.containsKey(String.valueOf(item_id)))
			{
				itemInfoJson = itemInfoMap.get(String.valueOf(item_id));
			}
			if (null != itemInfoJson){
				details_id = itemInfoJson.optInt("details_id");

				/*if ((null == details_id || details_id <= 0) && SysDictionary.ITEM_PROPERTY_MEALLIST.equals(item_property))
				{
					if (itemInfoMealMap.containsKey(String.valueOf(item_id)))
					{
						itemInfoJson = itemInfoMealMap.get(String.valueOf(item_id));
						details_id = itemInfoJson.optInt("details_id");
					}
				}
*/
				item_english = itemInfoJson.optString("item_english");
				pushmoney_way = itemInfoJson.optString("pushmoney_way");
				proportion = itemInfoJson.optDouble("proportion_money");
				discount_state = itemInfoJson.optString("is_discount");// 是否可以折扣
				item_class_id = itemInfoJson.optInt("item_class");
				item_num = itemInfoJson.optString("item_code");
				if (proportion.isNaN())
				{
					proportion = 0d;
				}
			}
			else
			{
				throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_DISH_ITEM);
			}
			
			/**
			 * 保存账单明细
			 */
			Object[] objs =  null;
			//单品折扣时
			if(discountModeId.intValue() == 10) {
				objs = new Object[]{ tenantId, organId, billno, details_id, item_id, item_num, item_name, item_english, reportDate, itemUnitId, item_unit_name, tableCode, pushmoney_way, proportion, assist_num,null, itemPrice, item_count, discountRate, discount_state, discountModeId, item_class_id,item_property, item_remark, "*", setmeal_id, setmeal_rwid, null, currentTime, itemSerial, shiftId, posNum, orderRemark, seat_num, sale_mode, billTaste, assist_item_id, waitcall_tag, reason_id, batch_num, orderNumber,Integer.parseInt(optNum),managerNum, discountReasonId,discountAmount,discountrAmount};
			}else {
				objs = new Object[]{tenantId, organId, billno, details_id, item_id, item_num, item_name, item_english, reportDate, itemUnitId, item_unit_name, tableCode, pushmoney_way, proportion, assist_num, null, itemPrice, item_count, discount_rate, discount_state, discount_mode_id, item_class_id,item_property, item_remark, "*", setmeal_id, setmeal_rwid, null, currentTime, itemSerial, shiftId, posNum, orderRemark, seat_num, sale_mode, billTaste, assist_item_id, waitcall_tag, reason_id, batch_num, orderNumber, Integer.parseInt(optNum), managerNum , 0,discountAmount,discountrAmount};
			}
			
			billItemList.add(objs);
			// 验证菜品,规格信息,组织账单明细数据
		}
		
		if (billItemList.size() > 0){
			StringBuilder itemSql = new StringBuilder("insert into pos_bill_item (tenancy_id,store_id,bill_num,details_id,item_id,item_num,item_name,item_english,report_date,item_unit_id,item_unit_name,table_code,pushmoney_way,proportion,assist_num,waiter_num,item_price,item_count,discount_rate,discount_state,discount_mode_id,item_class_id,item_property,item_remark,print_tag,setmeal_id,setmeal_rwid,is_setmeal_changitem,item_time,item_serial,item_shift_id,item_mac_id,order_remark,seat_num,sale_mode,item_taste,assist_item_id,waitcall_tag,returngive_reason_id,batch_num,order_number,opt_num,manager_num,discount_reason_id,discount_amount,discountr_amount) ");
			itemSql.append(" values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
			posDishDao.batchUpdate(itemSql.toString(), billItemList);
		}
		//处理做法
		insertMethodItem(tenantId, organId, billno, reportDate, posNum, currentTime, item, itemMap);
		
		//修改账单状态
		this.updatePosBill(saleMode, batch_num, payment_state, tenantId, organId, billno,mode,bill_property);
		
		logger.info(Thread.currentThread().getId()+">>>-----订单转账单下单  组织整单数据结束--  ");
	}
	
	/**
	 * 修改订单状态
	 * @param saleMode
	 * @param batch_num
	 * @param payment_state
	 * @param tenantId
	 * @param organId
	 * @param billno
	 * @throws Exception
	 */
	public void updatePosBill(String saleMode,String batch_num,String payment_state, String tenantId, Integer organId,String billno,String mode,String billProperty) throws Exception{
		
		logger.info(Thread.currentThread().getId()+"@-----订单转账单  ---修改订单状态--  tenantId=+"+tenantId+"----organId="+organId+"----mode="+mode+"----billno="+billno+"----payment_state="+payment_state+"----batch_num="+batch_num+"----saleMode="+saleMode);
		if(mode.equals("1")){
			String updateSql = "update pos_bill set sale_mode = ?, bill_taste =?,remark = ?,batch_num=?, payment_state=?,bill_property = ? where tenancy_id=? and store_id =? and bill_num = ?";
			posDishDao.update(updateSql, new Object[]{saleMode, null, null, batch_num, payment_state,SysDictionary.BILL_PROPERTY_SORT, tenantId, organId, billno });
		}else{
			String updateSql = "update pos_bill set bill_taste =?,remark = ?,batch_num=?, payment_state=? where tenancy_id=? and store_id =? and bill_num = ?";
			posDishDao.update(updateSql, new Object[]
			{ null, null, batch_num, payment_state, tenantId, organId, billno });
		}
		logger.info(Thread.currentThread().getId()+"@-----订单转账单  ---修改订单状态结束--  ");

	}
	
	/**
	 * 菜品做法
	 * @param tenantId
	 * @param organId
	 * @param billno
	 * @param reportDate
	 * @param posNum
	 * @param currentTime
	 * @param methods
	 * @param itemMap
	 * @throws Exception
	 */
	private void insertMethodItem(String tenantId, Integer organId, String billno, Date reportDate,String posNum,Timestamp currentTime, List<Map<String, Object>> items,Map<String, Map<String, Object>> itemMap) throws Exception{
		logger.info(Thread.currentThread().getId()+"@-----订单转账单下单  菜品做法--  tenantId=+"+tenantId+"----organId="+organId+"----billno="+billno+"----reportDate="+reportDate+"----posNum="+posNum+"----currentTime="+currentTime+"----items="+itemMap+"----items="+itemMap);
		String orderRemark = String.valueOf(currentTime.getTime());
		
		
		List<JSONObject> rwidList = posDishDao.getRwidsWithoutWaitCallItem(tenantId, organId, billno, orderRemark,posNum);
		List<Integer> methodParamList = new ArrayList<Integer>();
		for (Map<String, Object> item : items){
			
			if (Tools.isNullOrEmpty(item.get("method")) == false){
				
				List<Map<String, String>> methods = (List<Map<String, String>>) item.get("method");
				for (int j = 0; j < methods.size(); j++){
					Map<String, String> method = (Map<String, String>) methods.get(j);
					if (StringUtils.isNotEmpty(method.get("method_id"))){
						methodParamList.add(Integer.parseInt(method.get("method_id")));
					}
				}
			}
		}

		if (methodParamList.size() > 0){
			// 查询做法信息
			List<JSONObject> methodList = posDishDao.getHqItemMethodByID(tenantId, organId, methodParamList);
			Map<String, JSONObject> methodInfoMap = new HashMap<String, JSONObject>();
			if (null != methodList && methodList.size() > 0){
				for (JSONObject methodInfoJson : methodList){
					methodInfoMap.put(methodInfoJson.optString("method_id"), methodInfoJson);
				}
			}

			// 验证做法信息,组织做法
			List<Object[]> methodItemList = new ArrayList<Object[]>();
			for (JSONObject rwidJson : rwidList){
				String rwid = rwidJson.optString("rwid");
				Integer methodId = null;
				Map<String, Object> detail = itemMap.get(rwidJson.optString("order_number"));

				String item_property = ParamUtil.getStringValue(detail, "item_property", false, null);
				Double itemPrice = ParamUtil.getDoubleValue(detail, "item_price", false, null);
				Integer item_id = ParamUtil.getIntegerValue(detail, "item_id", false, null);

				if (SysDictionary.ITEM_PROPERTY_SINGLE.equalsIgnoreCase(item_property) || SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item_property)){
					if (Tools.isNullOrEmpty(detail.get("method")) == false){
					
						List<Map<String, String>> methods = (List<Map<String, String>>) detail.get("method");
						for (int j = 0; j < methods.size(); j++){
							Map<String, String> method = (Map<String, String>) methods.get(j);
							if (StringUtils.isNotEmpty(method.get("method_id"))){
								methodId = Integer.parseInt(method.get("method_id"));
							}
							else{
								methodId = 0;
							}
							String methodName = method.get("method_name");

							JSONObject methodJson = null;

							if (methodInfoMap.containsKey(String.valueOf(methodId))){
								methodJson = methodInfoMap.get(String.valueOf(methodId));
							}

							if (null == methodJson || methodJson.isEmpty()){
								throw new SystemException(PosErrorCode.NOT_EXISTS_METHOD_DISH);
							}

							String makeupWay = methodJson.optString("makeup_way");
							Double pro_money = methodJson.optDouble("proportion_money"); // 有可能是数字也有可能会是小数
							double zfamount = 0;

							if (StringUtils.isNotEmpty(makeupWay)){
								if ("ADD".equalsIgnoreCase(makeupWay)){
									zfamount = pro_money;
								}
								if ("MULTI".equalsIgnoreCase(makeupWay)){
									zfamount = DoubleHelper.mul(itemPrice, pro_money, 4);
								}
							}

							methodItemList.add(new Object[]{ tenantId, organId, billno, reportDate, Integer.valueOf(rwid), item_id, posNum, "METHOD", currentTime, methodId, methodName, zfamount, 0 });
						}
					}
				}
			}

			if (methodItemList.size() > 0){
				StringBuilder insertMethod = new StringBuilder("insert into pos_zfkw_item(tenancy_id,store_id,bill_num,report_date,rwid,item_id,pos_num,type,last_updatetime,zfkw_id,zfkw_name,amount,print_id) values (?,?,?,?,?,?,?,?,?,?,?,?,?)");
				posDishDao.batchUpdate(insertMethod.toString(), methodItemList);
			}
		}
	}
	
	/**
	 * 查询菜品估清
	 * @param tenantId
	 * @param organId
	 * @param billNum
	 * @param reportDate
	 * @param items
	 * @throws Exception
	 */
	public void getPosSold(TakeOrderingParam params, Data result) throws Exception{
		String tenantId =params.getTenantId();
		Integer organId = params.getOrganId();
		String chanel = params.getChanel();
		String billCode = params.getBillCode();
		Date reportDate = params.getReportDate();
		String billNum = params.getBillNum();
		List<Map<String, Object>> items = params.getItems();
		logger.info(Thread.currentThread().getId()+"-----订单转账单查询菜品估清方法开始--  tenantId=+"+tenantId+"------organId="+organId+"----billNum="+billNum+"----reportDate="+reportDate+"----item="+items.toString()+"params==="+params);
		List<JSONObject> soldOutList = new ArrayList<JSONObject>();
		List<JSONObject> subSoldList = new ArrayList<JSONObject>();
		Map<Integer,Double> tempMap = new HashMap<Integer, Double>();
		// 修改菜品估清
		Map<String, Object> soldOutMap = posDishDao.getPosSoldOutByItem(tenantId, organId, reportDate, items);
		List<Object[]> soldOutUpdateList = new ArrayList<Object[]>();
		for(Map<String, Object> oDish : items){
			String itemId = ParamUtil.getStringValue(oDish, "item_id", false, null);
			String unitId = ParamUtil.getStringValue(oDish, "unit_id", false, null);
			String itemName = ParamUtil.getStringValue(oDish, "item_name", false, null);
			double itemCount = 0;
			if (Tools.isNullOrEmpty(oDish.get("item_count")) == false){
				itemCount = Double.parseDouble(oDish.get("item_count").toString());
			}
			else{
				throw new SystemException(PosErrorCode.NOT_NULL_DISH_ITEM_COUNT);
			}

			if (null != soldOutMap && soldOutMap.containsKey(itemId)){
				Double soldOutCount = Double.parseDouble(String.valueOf(soldOutMap.get(itemId)));
				if (soldOutCount.isNaN() || soldOutCount <= 0){
					
					takeOrderingRefuseService.refuseOrderingEntry(billCode,  itemName+Constant.TAKE_ITEM_SOLD_OUT, chanel);
					throw SystemException.getInstance(PosErrorCode.DISH_ITEM_SOLD_OUT);
				}
				if (itemCount > soldOutCount){
					takeOrderingRefuseService.refuseOrderingEntry(billCode,"点菜数量大于沽清数量 菜品id"+itemId, chanel);
					throw SystemException.getInstance(PosErrorCode.GREAT_THAN_SOLD_OUT_COUNT);
				}

				Double num = DoubleHelper.sub(soldOutCount, itemCount, 4);

				soldOutMap.put(itemId, num);
				tempMap.put(Integer.parseInt(itemId), num);
				
				soldOutUpdateList.add(new Object[]{ num, tenantId, organId, reportDate, Integer.parseInt(itemId) });

				JSONObject soldOutJson = new JSONObject();
				soldOutJson.put("item_id", itemId);
				soldOutJson.put("item_unit_id", unitId);
				soldOutJson.put("setdate", DateUtil.format(reportDate));
				soldOutJson.put("num", num);
				soldOutList.add(soldOutJson);
				if (num <= 0){
					JSONObject subJson = new JSONObject();
					subJson.put("item_id", itemId);
					subSoldList.add(subJson);
				}
			}
		}

		//对更新数据处理
		List<Object[]> resultList = new ArrayList<Object[]>();
		for (int i = 0; i < soldOutUpdateList.size(); i++) {
			Object[] objects = soldOutUpdateList.get(i);
			if(tempMap.size()>0 && tempMap.get(objects[4]) == objects[0]){
				resultList.add(objects);
			}
		}
	
		if (resultList.size() > 0){
			StringBuilder updateSoldoutSql = new StringBuilder("update pos_soldout set num=? where tenancy_id=? and store_id=? and setdate=? and item_id =?");
			posDishDao.batchUpdate(updateSoldoutSql.toString(), resultList);
		}
		logger.info(Thread.currentThread().getId()+"-----订单转账单查询菜品估清方法结束----------------  ");
	}
	
	
    /**
     * 厨房打印
     * @param json
     * @param source
     */
    public void billPrint(JSONObject json){
    	
    	Data param = new Data();
    	param.setSecret("order_device");
		OrderingPrintThread orderingPrintThread = new OrderingPrintThread(json, param);
		//ThreadPool.getPrintThreadPool().execute(orderingPrintThread);	
		orderingPrintThread.run();
    }
    
//    /**
//	 * 数据上传
//	 * @param tenantId
//	 * @param store_id
//	 * @param old_table_code
//	 * @param new_table_code
//	 * @param old_bill_num
//	 * @param new_bill_num
//	 */
//	public void upload(String tenantId,String store_id,String old_table_code,String new_table_code,String old_bill_num,String new_bill_num){
//		DataUploadRunnable r = new DataUploadRunnable(tenantId,store_id,old_table_code,new_table_code,old_bill_num,new_bill_num);
//		Thread thread  =  new Thread(r);
//		thread.start();
//	}
	
	/**
	 * 计算折让
	 * @param organId
	 * @param billno
	 * @param discount_rate
	 * @throws Exception
	 */
	public void discountk(Integer organId,String billno ,Double discountkAmount,List<JSONObject> list) throws Exception{
		logger.info(Thread.currentThread().getId()+"-----订单转账单计算折扣开始------organId="+organId+"----billNum="+billno+"----discountkAmount="+discountkAmount+"--------");
		//更新账单
	    String sqlBill = " update pos_bill set  discount_mode_id = ?, discount_rate=?, discountk_amount=?,discount_amount=? where bill_num = ? and store_id = ? ";
		posDishDao.update(sqlBill, new Object[]{ SysDictionary.DISCOUNT_MODE_11,100, discountkAmount,discountkAmount, billno, organId });
		//String sqlBillItem = "update pos_bill_item set discount_mode_id = ?, discount_amount = ?  where bill_num = ? and store_id = ? and item_id=?";
		
		//logger.info(Thread.currentThread().getId()+"-----订单转账单计算折扣结束------"+objectList);
	}
}
