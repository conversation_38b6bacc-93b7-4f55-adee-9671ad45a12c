package com.tzx.pos.bo.dto;

/** 查询付款机构返回体
 * <AUTHOR>
 *
 */
public class QueryPaymentDetail
{
	Integer	id;
	Integer	jzid;
	String	payment_class;
	String	name;
	String	name_english;
	Double	amount;
	Integer	count;
	Double	currency_amount;
	Double	more_coupon;
	String	number;
	String	phone;
	Integer	customer_id;
	String	bill_code;
	String	report_date;
	Integer	shift_id;
	String	pos_num;
	String	cashier_num;
	String	last_updatetime;
	String	remark;
	String	payment_state;
	String	payment_state_text;
	String	verify_sms;
	String	verify_password;
	
	public Integer getId()
	{
		return id;
	}
	public void setId(Integer id)
	{
		this.id = id;
	}
	public Integer getJzid()
	{
		return jzid;
	}
	public void setJzid(Integer jzid)
	{
		this.jzid = jzid;
	}
	public String getPayment_class()
	{
		return payment_class;
	}
	public void setPayment_class(String payment_class)
	{
		this.payment_class = payment_class;
	}
	public String getName()
	{
		return name;
	}
	public void setName(String name)
	{
		this.name = name;
	}
	public String getName_english()
	{
		return name_english;
	}
	public void setName_english(String name_english)
	{
		this.name_english = name_english;
	}
	public Double getAmount()
	{
		return amount;
	}
	public void setAmount(Double amount)
	{
		this.amount = amount;
	}
	public Integer getCount()
	{
		return count;
	}
	public void setCount(Integer count)
	{
		this.count = count;
	}
	public Double getCurrency_amount()
	{
		return currency_amount;
	}
	public void setCurrency_amount(Double currency_amount)
	{
		this.currency_amount = currency_amount;
	}
	public Double getMore_coupon()
	{
		return more_coupon;
	}
	public void setMore_coupon(Double more_coupon)
	{
		this.more_coupon = more_coupon;
	}
	public String getNumber()
	{
		return number;
	}
	public void setNumber(String number)
	{
		this.number = number;
	}
	public String getPhone()
	{
		return phone;
	}
	public void setPhone(String phone)
	{
		this.phone = phone;
	}
	public Integer getCustomer_id()
	{
		return customer_id;
	}
	public void setCustomer_id(Integer customer_id)
	{
		this.customer_id = customer_id;
	}
	public String getBill_code()
	{
		return bill_code;
	}
	public void setBill_code(String bill_code)
	{
		this.bill_code = bill_code;
	}
	public String getReport_date()
	{
		return report_date;
	}
	public void setReport_date(String report_date)
	{
		this.report_date = report_date;
	}
	public Integer getShift_id()
	{
		return shift_id;
	}
	public void setShift_id(Integer shift_id)
	{
		this.shift_id = shift_id;
	}
	public String getPos_num()
	{
		return pos_num;
	}
	public void setPos_num(String pos_num)
	{
		this.pos_num = pos_num;
	}
	public String getCashier_num()
	{
		return cashier_num;
	}
	public void setCashier_num(String cashier_num)
	{
		this.cashier_num = cashier_num;
	}
	public String getLast_updatetime()
	{
		return last_updatetime;
	}
	public void setLast_updatetime(String last_updatetime)
	{
		this.last_updatetime = last_updatetime;
	}
	public String getRemark()
	{
		return remark;
	}
	public void setRemark(String remark)
	{
		this.remark = remark;
	}
	public String getPayment_state()
	{
		return payment_state;
	}
	public void setPayment_state(String payment_state)
	{
		this.payment_state = payment_state;
	}
	public String getPayment_state_text()
	{
		return payment_state_text;
	}
	public void setPayment_state_text(String payment_state_text)
	{
		this.payment_state_text = payment_state_text;
	}
	public String getVerify_sms()
	{
		return verify_sms;
	}
	public void setVerify_sms(String verify_sms)
	{
		this.verify_sms = verify_sms;
	}
	public String getVerify_password()
	{
		return verify_password;
	}
	public void setVerify_password(String verify_password)
	{
		this.verify_password = verify_password;
	}
}
