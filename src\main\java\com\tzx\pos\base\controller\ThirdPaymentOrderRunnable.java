package com.tzx.pos.base.controller;

import java.sql.Timestamp;
import java.util.List;

import org.apache.log4j.Logger;

import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.Tools;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.pos.base.service.ThirdPaymentOrderService;
import com.tzx.pos.bo.dto.PosThirdPaymentOrderEntity;

public class ThirdPaymentOrderRunnable implements Runnable
{
	private static final Logger	logger	= Logger.getLogger(ThirdPaymentOrderRunnable.class);
	
	private static final Integer[]	sleepTime			= { 3, 10, 15, 30, 60, 90, 180, 1800, 3600, 7200, 7200 };
	private static final Integer[]	queryContDefaule	= { 100, 30, 20, 20, 3, 3, 1, 1, 1, 1 };
	public static final Integer		QUERY_COUNT_NUM		= 180;
	
	@Override
	public void run()
	{
		String tenancyId = null;
		if (Constant.systemMap.containsKey("tenent_id"))
		{
			tenancyId = Constant.systemMap.get("tenent_id");
		}

		Integer storeId = Integer.valueOf(Constant.systemMap.get("store_id"));
		if (Constant.systemMap.containsKey("store_id"))
		{
			storeId = Integer.valueOf(Constant.systemMap.get("store_id"));
		}

		if (Tools.isNullOrEmpty(tenancyId) || Tools.isNullOrEmpty(storeId))
		{
			logger.info("参数为空");
			return;
		}

		DBContextHolder.setTenancyid(tenancyId);
		ThirdPaymentOrderService posPaymentService = SpringConext.getApplicationContext().getBean(ThirdPaymentOrderService.class);

		try
		{
			List<PosThirdPaymentOrderEntity> paymentList = posPaymentService.getThirdPaymentOrderList(tenancyId, storeId, QUERY_COUNT_NUM.doubleValue());

			for (PosThirdPaymentOrderEntity paymentJson : paymentList)
			{
				Integer queryCount = paymentJson.getQuery_count();

				try
				{
					Timestamp queryTime = paymentJson.getQuery_time();
					Timestamp nowTime = DateUtil.getNowTimestamp();

					int i = 0;
					Integer count = 0;
					for (; i < queryContDefaule.length; i++)
					{
						if (queryCount >= count && queryCount < (count + queryContDefaule[i]))
						{
							break;
						}
						else
						{
							count = count + queryContDefaule[i];
						}
					}

					Integer time = sleepTime[i];
					if (nowTime.getTime() < (queryTime.getTime() + time * 1000))
					{
						continue;
					}

					logger.debug("第三方轮询=============================================>  i=" + i + ";   count=" + count + ";   time=" + time + ";   queryCount=" + queryCount);
					posPaymentService.queryThirdPaymentForRunnable(tenancyId, storeId, paymentJson);
				}
				catch (Exception e)
				{
					logger.error("第三方支付轮询报错:", e);
				}

//				posPaymentService.updateThirdPaymentOrderListState(tenancyId, storeId, paymentJson.getId(), queryCount);
			}
		}
		catch (Exception e)
		{
			// TODO Auto-generated catch block
			e.printStackTrace();
			logger.error("第三方付款状态:", e);
		}

	}

}
