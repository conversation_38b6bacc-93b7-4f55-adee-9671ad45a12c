/*
package com.tzx.framework.common.file;

import java.io.File;
import java.io.UnsupportedEncodingException;
import java.util.Iterator;
import java.util.List;
import java.util.UUID;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemFactory;
import org.apache.commons.fileupload.FileUploadBase;
import org.apache.commons.fileupload.FileUploadException;
import org.apache.commons.fileupload.RequestContext;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.apache.commons.fileupload.servlet.ServletRequestContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;




*/
/**
 * 单文件上传。
 * <AUTHOR>
 *
 *//*

public class SingleFileUpload extends FileUploadBase{
	Logger log = LoggerFactory.getLogger(SingleFileUpload.class);
	
	private FileItem fileItem;
	private int sizeThreshold = 1024 * 1024 * 10;   
	private String encoding = "UTF-8";
	
	*/
/**
	 * 文件上传
	 * @param request		request
	 * @param contextPath	上下文路径
	 * @return
	 * @throws UnsupportedEncodingException
	 * @throws FileUploadException
	 *//*

	public FileItem uploadFile(HttpServletRequest request, String contextPath) throws UnsupportedEncodingException, FileUploadException{
		FileItem rFileItem = null;
		if (ServletFileUpload.isMultipartContent(request)) {
		    // 创建文件处理工厂，它用于生成 FileItem 对象。 
		    DiskFileItemFactory factory = new DiskFileItemFactory(); 
		    
		    //设置文件的缓存路径
		    File d = new File(contextPath + "/imgs/tmp");
	        if(!d.exists()){
	        	d.mkdirs();
	        }
		    factory.setSizeThreshold(sizeThreshold); // 设置最多只允许在内存中存储的数据,单位:字节
		    factory.setRepository(d); // 设置一旦文件大小超过getSizeThreshold()的值时数据存放在硬盘的目录(默认可以不用设置)
		    
		    // Create a new file upload handler
		    ServletFileUpload upload = new ServletFileUpload(factory);
		    // 设置允许用户上传文件大小,单位:字节
		    upload.setSizeMax(sizeThreshold);
		    //上传文件,并解析出所有的表单字段，包括普通字段和文件字段
		    List items = upload.parseRequest(request);
		         //下面对每个字段进行处理，分普通字段和文件字段
		    Iterator it = items.iterator();
		    while(it.hasNext()){
			    FileItem fileItem = (FileItem) it.next();
			    //如果是普通字段
			    if(fileItem.isFormField()){  //是普通的字段
				    */
/*System.out.println(fileItem.getFieldName() + "   " + fileItem.getName() + "   " + new String(fileItem.getString().getBytes("iso8859-1"), "gbk"));
				    fileItem.getFieldName();//得到字段name属性的值
				    fileItem.getName();//得到file字段的文件名全路径名，如果不是file字段，为null
				    fileItem.getString();//得到该字段的值,默认的编码格式
				    fileItem.getString("UTF-8");//指定编码格式
				    *//*

			    }else{//文件字段
			    	log.debug(fileItem.getFieldName());
			    	log.debug(fileItem.getName());    		//得到file字段的文件名全路径名
			    	log.debug(fileItem.isInMemory()+""); 	//用来判断FileItem类对象封装的主体内容是存储在内存中，还是存储在临时文件中，如果存储在内存中则返回true，否则返回false
			        log.debug(fileItem.getContentType()); 	//文件类型
			        log.debug(fileItem.getSize()+"");       //文件大小
			 
			        //保存文件，其实就是把缓存里的数据写到目标路径下
					if(fileItem.getName()!=null && fileItem.getSize()!=0){
						rFileItem = fileItem;
						File newFile = new File(contextPath + "/img/" + request.getSession().getAttribute("tenentid") + "/" + UUID.randomUUID()+"_"+fileItem.getName());
						try {
							fileItem.write(newFile);
						} catch (Exception e) {
							log.debug("文件写入失败");
						}
					}else{
						log.debug("文件没有选择 或 文件内容为空");
					}
			    }
		    }
		}
		return rFileItem;
	}

	*/
/**
	 * 解析文件
	 * @param request
	 * @throws FileUploadException
	 *//*

    public void parseFile(HttpServletRequest request) throws FileUploadException {
    	RequestContext requestContext = new ServletRequestContext(request);
        DiskFileItemFactory factory = new DiskFileItemFactory();
        factory.setSizeThreshold(sizeThreshold);
        factory.setRepository(new File("c:/aaa/"));

//        if (repository != null)
//            factory.setRepository(repository);

        ServletFileUpload upload = new ServletFileUpload(factory);
        upload.setSizeMax(sizeThreshold);

        upload.setHeaderEncoding(encoding);

        List list = null;
        try {
            list = upload.parseRequest(request);
            
            for (Object obj : list) {
            	FileItem item = (FileItem)obj;
                if (item.isFormField()) {
                	fileItem = item;
                	 fileItem.getName();
                     fileItem.isInMemory();
                     fileItem.getContentType();
                     fileItem.getSize();
                     
                	break;
//                    String fieldName = item.getFieldName();
//                    String value = item.getString(encoding);
                } 
            }
            
        } catch (FileUploadException e) {
            e.printStackTrace();
        } 
    }

    */
/**
     * 上传文件, 调用该方法之前必须先调用 parseRequest(HttpServletRequest request)
     * @param parent 存储的目录
     * @throws Exception
     *//*

    public void upload(File parent) throws Exception {
        if (fileItem == null)
            return;

        String name = fileItem.getName();
        File file = new File(parent, name);
        uploadFile(file);
    }
    
    private void uploadFile(File file) throws Exception{
        if (fileItem == null)
            return;

        long fileSize = fileItem.getSize();
        if (super.getSizeMax() > -1 && fileSize > super.getSizeMax()){
            String message = String.format("the request was rejected because its size (%1$s) exceeds the configured maximum (%2$s)", fileSize, super.getSizeMax());
                    
            throw new org.apache.commons.fileupload.FileUploadBase.SizeLimitExceededException(message, fileSize, super.getSizeMax());
        }
        
        fileItem.write(file);
    }
    
    */
/** *//*
*/
/**
     * 获取文件信息
     * 必须先调用 parseRequest(HttpServletRequest request)
     * @return
     *//*

    public FileItem getFileItem() {
        return fileItem;
    }

	@Override
	public FileItemFactory getFileItemFactory() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public void setFileItemFactory(FileItemFactory arg0) {
		// TODO Auto-generated method stub
		
	}

	public int getSizeThreshold() {
		return sizeThreshold;
	}

	public void setSizeThreshold(int sizeThreshold) {
		this.sizeThreshold = sizeThreshold;
	}

	public String getEncoding() {
		return encoding;
	}

	public void setEncoding(String encoding) {
		this.encoding = encoding;
	}

	public void setFileItem(FileItem fileItem) {
		this.fileItem = fileItem;
	}

}
*/
