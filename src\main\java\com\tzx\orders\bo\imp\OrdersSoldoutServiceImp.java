package com.tzx.orders.bo.imp;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import com.tzx.base.common.util.PosPropertyUtil;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.HttpUtil;
import com.tzx.framework.common.util.Tools;
import com.tzx.orders.bo.OrdersSoldoutService;
import com.tzx.orders.po.springjdbc.dao.OrdersManagementDao;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysParameter;
import com.tzx.pos.base.util.CacheTableUtil;
import com.tzx.pos.base.util.ParamUtil;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

/** 外卖估清上传
 * 
 * <AUTHOR> 2019-02-11
 *
 */
@Service(OrdersSoldoutService.NAME)
public class OrdersSoldoutServiceImp implements OrdersSoldoutService
{
	private static final Logger	logger				= Logger.getLogger(OrdersSoldoutService.class);
	
	private final static String FIND_SOLDOUT_URL ="/ca/third/dish/getShareDishStock";
	private final static String UPLOAD_SOLDOUT_URL ="/ca/third/dish/shareDishStockUpdate";
	
	@Resource(name = OrdersManagementDao.NAME)
	private OrdersManagementDao	ordersDao;

	@Override
	public List<JSONObject> findOrderSoldOut(Data param) throws Exception
	{
		String tenantId = param.getTenancy_id();
		Integer organId = param.getStore_id();

		JSONObject paramJson = null;
		if (null != param.getData() && 0 < param.getData().size())
		{
			paramJson = JSONObject.fromObject(param.getData().get(0));
		}
		else
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}

		String optNum = ParamUtil.getStringValueByObject(paramJson, "opt_num");
		
		String wmSharestockSwitch = CacheTableUtil.getSysParameter(SysParameter.WM_SHARESTOCK_SWITCH);
		
		boolean isUser=false;
		//处理系统参数
		if (Tools.hv(wmSharestockSwitch))
		{
			String[] str = wmSharestockSwitch.split(";");
			if (null != str && 0 < str.length)
			{
				isUser = "1".equals(str[0]);
			}
		}
		
		if(false == isUser)
		{
			return null;
		}
		
		String oper = ordersDao.getEmpNameById(tenantId, organId, optNum);

		// 组织请求参数
		JSONObject requestJson = new JSONObject();
		requestJson.put("tenancy_id", tenantId);
		requestJson.put("shop_id", organId);
		requestJson.put("user", oper);

		long t = System.currentTimeMillis();
		logger.info(String.valueOf(t) + "<发送接口请求体==>地址: " + this.getPequestUrl(FIND_SOLDOUT_URL));
		logger.info(String.valueOf(t) + "<发送接口请求体==>" + requestJson.toString());

		String result = HttpUtil.sendPostRequest(this.getPequestUrl(FIND_SOLDOUT_URL), requestJson.toString());

		logger.info(String.valueOf(t) + "<发送接口返回体==>用时: " + String.valueOf((System.currentTimeMillis() - t) / 1000d) + " 秒");
		logger.info(String.valueOf(t) + "<发送接口返回体==>" + result);

		List<JSONObject> itemList = new ArrayList<JSONObject>();
//		List<String> orderItemList = new ArrayList<String>();
		if (Tools.hv(result))
		{
			JSONObject resultJson = JSONObject.fromObject(result);

			JSONArray soldoutDataList = null;
			JSONArray notSoldoutDataList = null;

			// 解析返回结果集
			if (null != resultJson && resultJson.containsKey("data"))
			{
				JSONObject dataJson = resultJson.optJSONObject("data");

				if (null != dataJson && dataJson.containsKey("clear_data"))
				{
					soldoutDataList = dataJson.optJSONArray("clear_data");
				}

				if (null != dataJson && dataJson.containsKey("fill_data"))
				{
					notSoldoutDataList = dataJson.optJSONArray("fill_data");
				}
			}

			Map<String, Map<String, String>> itemMap = new HashMap<String, Map<String, String>>();
			//解析估清菜品
			if (null != soldoutDataList && 0 < soldoutDataList.size())
			{
				for (Object obj : soldoutDataList)
				{
					JSONObject channelJson = JSONObject.fromObject(obj);
					String itemId = ParamUtil.getStringValueByObject(channelJson, "item_code");
					String channel = ParamUtil.getStringValueByObject(channelJson, "channel");

					Map<String, String> channelMap = null;
					if (itemMap.containsKey(itemId))
					{
						channelMap = itemMap.get(itemId);
						channelMap.put(channel, "1");
					}
					else
					{
						channelMap = new HashMap<String, String>();
						channelMap.put(channel, "1");
					}

					itemMap.put(itemId, channelMap);
					
//					if(false == orderItemList.contains(itemId))
//					{
//						orderItemList.add(itemId);
//					}
				}
			}

			//解析未估清菜品
			if (null != notSoldoutDataList && 0 < notSoldoutDataList.size())
			{
				for (Object obj : notSoldoutDataList)
				{
					JSONObject channelJson = JSONObject.fromObject(obj);
					String itemId = ParamUtil.getStringValueByObject(channelJson, "item_code");
					String channel = ParamUtil.getStringValueByObject(channelJson, "channel");

					Map<String, String> channelMap = null;
					if (itemMap.containsKey(itemId))
					{
						channelMap = itemMap.get(itemId);
						if (false == channelMap.containsKey(channel))
						{
							channelMap.put(channel, "0");
						}
					}
					else
					{
						channelMap = new HashMap<String, String>();
						channelMap.put(channel, "0");
					}

					itemMap.put(itemId, channelMap);
				}
			}

			for(String itemId : itemMap.keySet())
			{
				Map<String, String> channelMap  = itemMap.get(itemId);
				List<JSONObject> channelList = new ArrayList<>();
				for(String channel:channelMap.keySet())
				{
					JSONObject channelJson = new JSONObject();
					channelJson.put("channel", channel);
					channelJson.put("is_soldout", channelMap.get(channel));
					channelList.add(channelJson);
				}
				
				JSONObject itemJson = new JSONObject();
				itemJson.put("item_id", itemId);
				itemJson.put("channels", channelList);
				
				itemList.add(itemJson);
			}
		}
		return itemList;
	}
	
	@Override
	public Data uploadOrderSoldOut(String tenancyId, Integer storeId, Date reportDate, String optNum, List<Integer> soldoutList, List<Integer> notSoldList) throws Exception
	{
		String oper = ordersDao.getEmpNameById(tenancyId, storeId, optNum);

		// 组织请求参数
		JSONObject requestJson = new JSONObject();
		requestJson.put("tenancy_id", tenancyId);
		requestJson.put("shop_id", storeId);
		requestJson.put("user", oper);
		requestJson.put("clear_items", soldoutList);
		requestJson.put("fill_items", notSoldList);

		long t = System.currentTimeMillis();
		logger.info(String.valueOf(t) + "<发送接口请求体==>地址: " + this.getPequestUrl(UPLOAD_SOLDOUT_URL));
		logger.info(String.valueOf(t) + "<发送接口请求体==>" + requestJson.toString());

		String result = HttpUtil.sendPostRequest(this.getPequestUrl(UPLOAD_SOLDOUT_URL), requestJson.toString());

		logger.info(String.valueOf(t) + "<发送接口返回体==>用时: " + String.valueOf((System.currentTimeMillis() - t) / 1000d) + " 秒");
		logger.info(String.valueOf(t) + "<发送接口返回体==>" + result);

		Data resultData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);

		if (Tools.hv(result))
		{
			JSONObject resultJson = JSONObject.fromObject(result);
			if (null != resultJson && resultJson.containsKey("error"))
			{
//				if(false == "0".equals(resultJson.optString("error")))
//				{
					JSONArray clearResultList = resultJson.optJSONArray("clear_result");
					JSONArray fillResultList = resultJson.optJSONArray("fill_result");
					
					Map<String,JSONObject> channelMap= new  HashMap<String,JSONObject>();
					List<String> failReasonList = new ArrayList<String>(); 
					List<JSONObject> uploadResultList = new ArrayList<JSONObject>(); 
					
					if(null !=clearResultList)
					{
						for(Object obj: clearResultList)
						{
							JSONObject json = JSONObject.fromObject(obj);
							String channel = ParamUtil.getStringValueByObject(json, "channel");
							Integer eCount= ParamUtil.getIntegerValueByObject(json, "error_count");
							Integer sCount= ParamUtil.getIntegerValueByObject(json, "success_count");
							String msg = ParamUtil.getStringValueForNullByObject(json, "msg");
							
							if (0 == eCount.intValue() && 0 == sCount.intValue() && Tools.isNullOrEmpty(msg))
							{
								continue;
							}
							
							JSONObject uploadResultJson =null;
							if(channelMap.containsKey(channel))
							{
								uploadResultJson = channelMap.get(channel);
								uploadResultJson.put("success_count", (sCount+ParamUtil.getIntegerValueByObject(uploadResultJson, "success_count")));
								uploadResultJson.put("fail_count", (eCount+ParamUtil.getIntegerValueByObject(uploadResultJson, "fail_count")));
								
							}else 
							{
								uploadResultJson = new JSONObject();
								uploadResultJson.put("channel", channel);
								uploadResultJson.put("success_count", sCount);
								uploadResultJson.put("fail_count", eCount);
								uploadResultList.add(uploadResultJson);
								channelMap.put(channel, uploadResultJson);
							}
							
							String[] reasons = msg.split(";");
							for(String reason:reasons)
							{
								if(false == failReasonList.contains(reason))
								{
									failReasonList.add(reason);
								}
							}
						}
					}
					
					if(null !=fillResultList)
					{
						for(Object obj: fillResultList)
						{
							JSONObject json = JSONObject.fromObject(obj);
							String channel = ParamUtil.getStringValueByObject(json, "channel");
							Integer eCount= ParamUtil.getIntegerValueByObject(json, "error_count");
							Integer sCount= ParamUtil.getIntegerValueByObject(json, "success_count");
							String msg = ParamUtil.getStringValueForNullByObject(json, "msg");
							
							if (0 == eCount.intValue() && 0 == sCount.intValue() && Tools.isNullOrEmpty(msg))
							{
								continue;
							}
							
							JSONObject uploadResultJson =null;
							if(channelMap.containsKey(channel))
							{
								uploadResultJson = channelMap.get(channel);
								uploadResultJson.put("success_count", (sCount+ParamUtil.getIntegerValueByObject(uploadResultJson, "success_count")));
								uploadResultJson.put("fail_count", (eCount+ParamUtil.getIntegerValueByObject(uploadResultJson, "fail_count")));
								
							}else 
							{
								uploadResultJson = new JSONObject();
								uploadResultJson.put("channel", channel);
								uploadResultJson.put("success_count", sCount);
								uploadResultJson.put("fail_count", eCount);
								uploadResultList.add(uploadResultJson);
								channelMap.put(channel, uploadResultJson);
							}
							
							String[] reasons = msg.split(";");
							for(String reason:reasons)
							{
								if(false == failReasonList.contains(reason))
								{
									failReasonList.add(reason);
								}
							}
						}
					}
					
					if (0 < uploadResultList.size() || 0 < failReasonList.size())
					{
						JSONObject returnJson = new JSONObject();
						returnJson.put("upload_result", uploadResultList);
						returnJson.put("fail_reason", failReasonList);

						List<JSONObject> returnList = new ArrayList<JSONObject>();
						returnList.add(returnJson);

						resultData.setData(returnList);
						resultData.setCode(PosErrorCode.UPLOAD_ORDER_SOLDOUT_ERROR.getNumber());
						resultData.setMsg(resultJson.optString("msg"));
						resultData.setSuccess(false);
					}
//				}
			}
			else if (null != resultJson && resultJson.containsKey("code"))
			{
				resultData.setCode(resultJson.optInt("code"));
				resultData.setMsg(resultJson.optString("msg"));
				resultData.setSuccess(false);
			}
			else
			{
				resultData.setCode(Constant.CODE_NULL_DATASET);
				resultData.setMsg("返回结果解析失败");
				resultData.setSuccess(false);
			}
		}
		else
		{
			resultData.setCode(Constant.CODE_CONN_EXCEPTION);
			resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
			resultData.setSuccess(false);
		}
		return resultData;
	}
	
	private String getPequestUrl(String url)
	{
		return PosPropertyUtil.getMsg("saas.url") + url;
	}
}
