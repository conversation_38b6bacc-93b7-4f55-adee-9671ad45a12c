package com.tzx.framework.common.util;

public class EhkUtil {

	//基于pos端收银支付方式和scm
	public static int getPayType(int i){
		
		//默认使用的会员储值支付
		int paymentId=0;
		
		switch(i){
			case 1:  //支付宝
				paymentId=5;
				break;
			case 2:  //微信支付
				paymentId=6;
				break;	
			case 3:  //团券支付
				paymentId=1008;
				break;	
			case 4:  //美大支付
				paymentId=1012;
				break;	
			case 5:  //现金支付
				paymentId=4;
				break;
			case 1016:  //会员卡支付
				paymentId=1016;
				break;	
			case 1010:  //优惠券支付
				paymentId=1010;
				break;	
			case 1004:  //积分抵现
				paymentId=1004;
				break;
			case 9:  //不需要三方支付都默认为现金
				paymentId=5;
				break;
			default:
				break;		  
		}
		
		return paymentId;
	}
	
	//将时分秒转化为秒 time格式为mm:ss
	public static int covertTimeToSecond(String time){

		int index=time.indexOf(":");
		int mi=Integer.parseInt(time.substring(0,index));
		int ss=Integer.parseInt(time.substring(index+1));
		
		return mi*60+ss;	
		
	}

}
