package com.tzx.pos.base.service;

import java.util.List;

import com.tzx.member.common.entity.PosCustomerOperateListEntity;

public interface PosCustomerOperateService
{
	String	NAME	= "com.tzx.pos.base.service.impl.PosCustomerOperateServiceImp";

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param queryCountNum
	 * @return
	 * @throws Exception
	 */
	public List<PosCustomerOperateListEntity> getPosCustomerOperateListNotComplete(String tenancyId, int storeId, Double queryCountNum) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param customerOperate
	 * @throws Exception
	 */
	public void queryPosCustomerOperateListForRunnable(String tenancyId, int storeId, PosCustomerOperateListEntity customerOperate) throws Exception;

}
