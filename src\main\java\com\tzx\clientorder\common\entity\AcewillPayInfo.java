package com.tzx.clientorder.common.entity;

import java.util.List;

import com.tzx.framework.common.util.CommonUtil;
import com.tzx.framework.common.util.DoubleHelper;

public class AcewillPayInfo {

	private static final int DEFAULT_SCALE = 4;
	/**
	 * wlife    :wx:微信 alipay:支付宝
	 * balance  :微生活储值
	 * coupon   :代金券
	 * product  :菜品券
	 */
	private String source;
	/**
	 * 支付金额
	 */
	private Double amount;
	/**
	 * 商家承担金额
	 */
	private Double storepay;
	/**
	 * 支付流水号
	 */
	private String serilNo;

	private Double receipt_amount;

	private Double buyer_pay_amount;

	private Double creditCash =0d;
	
	// 代金券
	private Integer			sale_deno_money;
	private List<String>	coupons_info;

	// 菜品券
	private Integer			sale_dish_money;
	private List<String>	coupon_dishs_info;
	
	public String getSource() {
		return source;
	}
	public void setSource(String source) {
		this.source = source;
	}
	public Double getAmount() {
		return amount;
	}
	public void setAmount(Double amount) {
		this.amount = amount;
	}
	public Double getStorepay() {
		return storepay;
	}
	public void setStorepay(Double storepay) {
		this.storepay = storepay;
	}
	public String getSerilNo() {
		return serilNo;
	}
	public void setSerilNo(String serilNo) {
		this.serilNo = serilNo;
	}

	public Double getReceipt_amount() {
		return receipt_amount;
	}

	public void setReceipt_amount(Double receipt_amount) {
		this.receipt_amount = receipt_amount;
	}

	public Double getBuyer_pay_amount() {
		return buyer_pay_amount;
	}

	public void setBuyer_pay_amount(Double buyer_pay_amount) {
		this.buyer_pay_amount = buyer_pay_amount;
	}

	public Double getCreditCash() {
		return creditCash;
	}

	public void setCreditCash(Double creditCash) {
		this.creditCash = creditCash;
	}
	public Integer getSale_deno_money()
	{
		return sale_deno_money;
	}
	public void setSale_deno_money(Integer sale_deno_money)
	{
		this.sale_deno_money = sale_deno_money;
	}
	public List<String> getCoupons_info()
	{
		return coupons_info;
	}
	public void setCoupons_info(List<String> coupons_info)
	{
		this.coupons_info = coupons_info;
	}
	public Integer getSale_dish_money()
	{
		return sale_dish_money;
	}
	public void setSale_dish_money(Integer sale_dish_money)
	{
		this.sale_dish_money = sale_dish_money;
	}
	public List<String> getCoupon_dishs_info()
	{
		return coupon_dishs_info;
	}
	public void setCoupon_dishs_info(List<String> coupon_dishs_info)
	{
		this.coupon_dishs_info = coupon_dishs_info;
	}
	
	/**转换单位为元
	 * @return
	 */
	public Double getSaleDenoMoneyY()
	{
		Double saleDenoMoney = 0d;
		if (CommonUtil.hv(sale_deno_money))
		{
			saleDenoMoney = DoubleHelper.div(sale_deno_money.doubleValue(), 100d, DEFAULT_SCALE);
		}
		return saleDenoMoney;
	}
	
	/**转换单位为元
	 * @return
	 */
	public Double getSaleDishMoneyY()
	{
		Double saleDishMoneyY = 0d;
		if (CommonUtil.hv(sale_dish_money))
		{
			saleDishMoneyY = DoubleHelper.div(sale_dish_money.doubleValue(), 100d, DEFAULT_SCALE);
		}
		return saleDishMoneyY;
	}
}
