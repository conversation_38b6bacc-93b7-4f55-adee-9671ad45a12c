package com.tzx.pos.bo.dto;

import java.io.Serializable;

/**
 * 
 * <AUTHOR>
@Deprecated
public class PosBillPayment implements Serializable
{

	/**
	 * 
	 */
	private static final long	serialVersionUID	= 7333099517181858938L;

	private String				id;
	private String				jzid;
	private String				name;
	private String				amount;
	private String				count;
	private String				number;
	private String				phone;
	private String				shift_id;
	private String				pos_num;
	private String				cashier_num;
	private String				last_updatetime;
	private String				customer_id;
	private String				bill_code;
	private String				remark;
	private String				currency_amount;
	private String				batch_num;

	public String getBatch_num()
	{
		return batch_num;
	}

	public void setBatch_num(String batch_num)
	{
		this.batch_num = batch_num;
	}

	public String getCurrency_amount()
	{
		return currency_amount;
	}

	public void setCurrency_amount(String currency_amount)
	{
		this.currency_amount = currency_amount;
	}

	public String getPayment_state()
	{
		return payment_state;
	}

	public void setPayment_state(String payment_state)
	{
		this.payment_state = payment_state;
	}

	public String getPayment_state_text()
	{
		return payment_state_text;
	}

	public void setPayment_state_text(String payment_state_text)
	{
		this.payment_state_text = payment_state_text;
	}

	private String	payment_state;
	private String	payment_state_text;

	public String getId()
	{
		return id;
	}

	public void setId(String id)
	{
		this.id = id;
	}

	public String getJzid()
	{
		return jzid;
	}

	public void setJzid(String jzid)
	{
		this.jzid = jzid;
	}

	public String getName()
	{
		return name;
	}

	public void setName(String name)
	{
		this.name = name;
	}

	public String getAmount()
	{
		return amount;
	}

	public void setAmount(String amount)
	{
		this.amount = amount;
	}

	public String getCount()
	{
		return count;
	}

	public void setCount(String count)
	{
		this.count = count;
	}

	public String getNumber()
	{
		return number;
	}

	public void setNumber(String number)
	{
		this.number = number;
	}

	public String getPhone()
	{
		return phone;
	}

	public void setPhone(String phone)
	{
		this.phone = phone;
	}

	public String getShift_id()
	{
		return shift_id;
	}

	public void setShift_id(String shift_id)
	{
		this.shift_id = shift_id;
	}

	public String getPos_num()
	{
		return pos_num;
	}

	public void setPos_num(String pos_num)
	{
		this.pos_num = pos_num;
	}

	public String getCashier_num()
	{
		return cashier_num;
	}

	public void setCashier_num(String cashier_num)
	{
		this.cashier_num = cashier_num;
	}

	public String getLast_updatetime()
	{
		return last_updatetime;
	}

	public void setLast_updatetime(String last_updatetime)
	{
		this.last_updatetime = last_updatetime;
	}

	public String getCustomer_id()
	{
		return customer_id;
	}

	public void setCustomer_id(String customer_id)
	{
		this.customer_id = customer_id;
	}

	public String getBill_code()
	{
		return bill_code;
	}

	public void setBill_code(String bill_code)
	{
		this.bill_code = bill_code;
	}

	public String getRemark()
	{
		return remark;
	}

	public void setRemark(String remark)
	{
		this.remark = remark;
	}
}
