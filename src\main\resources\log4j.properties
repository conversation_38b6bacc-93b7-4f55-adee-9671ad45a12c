
log4j.rootLogger=INFO,stdout,D

# stdout is set to be a ConsoleAppender which outputs to System.out.
log4j.appender.stdout=org.apache.log4j.ConsoleAppender
log4j.appender.stdout.layout=org.apache.log4j.PatternLayout
log4j.appender.stdout.layout.ConversionPattern=%d{ISO8601} %r [%t] %p %C{1}.%M(%F:%L) %x: %m%n

# sass to the file appender
log4j.appender.D=org.apache.log4j.DailyRollingFileAppender
log4j.appender.D.layout=org.apache.log4j.PatternLayout
log4j.appender.D.layout.ConversionPattern=%d{ISO8601} %r [%t] %p %C{1}.%M(%F:%L) %x: %.1000000m%n
log4j.appender.D.File=${catalina.base}/logs/saas.log
log4j.appender.D.DatePattern='_'yyyy-MM-dd-HH'.log'

# tip log for Console.jar
log4j.logger.tip=INFO,tip
log4j.appender.tip=org.apache.log4j.RollingFileAppender
log4j.appender.tip.File=${catalina.base}/logs/tip.log
log4j.appender.tip.MaxFileSize=512KB
log4j.appender.tip.MaxBackupIndex=2
log4j.appender.tip.layout=org.apache.log4j.PatternLayout
log4j.appender.tip.layout.ConversionPattern=%d{ISO8601} %p - %.1000m%n

# druid
log4j.logger.druid.sql=warn,stdout
log4j.logger.druid.sql.DataSource=warn,stdout
log4j.logger.druid.sql.Connection=warn,stdout
log4j.logger.druid.sql.Statement=warn,stdout
log4j.logger.druid.sql.ResultSet=warn,stdout