package com.tzx.orders.bo.dto;

import java.io.Serializable;
import java.util.List;

import net.sf.json.JSONObject;

/**
 * 订单状态
 * <AUTHOR>
 *
 */
public class OrderState implements Serializable
{
	/**
	 * 
	 */
	private static final long	serialVersionUID	= 3015097824200078525L;


	private String order_code;
	private String bill_num;
	private String order_state;
	private String order_type;
	private String chanel;
	private String third_order_code;
	private String single_time;
	private String receive_time;
	private String take_time;
	private String receive_time_qd;
	private String dispatch_time;
	private String receive_time_dispatch;
	private String distribution_time;
	private String receive_time_distribution;
	private String finish_time;
	private String receive_time_finish;
	private String cancellation_time;
	private String receive_time_cancellation;
    private String is_online_payment;
	private List<?> order_repayment;
	private String payment_state;
	private String need_invoice;
	private double actual_pay;
	private String refund_type;//2 全单退 1部分退
	private String refund_recive_time;//pos接收时间
	private String refund_operator;//退款同意/不现意操作人
	private String refund_oper_time;//退款同意/不同意操作时间
	private String refund_reason;//申请退款原因
	private String refund_third_order_id;//退款第三方订单号
	private String send_time;//送达时间

	private String device_ip;
	private String device_mac;
	private String report_date;
	
	public static OrderState get(JSONObject param)
	{
		OrderState order = new OrderState();

		order.setOrder_code(param.containsKey("order_code") ? param.optString("order_code") : "");
		order.setBill_num(param.containsKey("bill_num") ?  (param.optString("bill_num").equals("null")? null: param.optString("bill_num")) : "");
		order.setOrder_state(param.containsKey("order_state") ? param.optString("order_state") : "");
		order.setReceive_time(param.containsKey("receive_time") ? param.optString("receive_time") : "");
		order.setTake_time(param.containsKey("take_time") ? param.optString("take_time") : "");
		order.setReceive_time_qd(param.containsKey("receive_time_qd") ? param.optString("receive_time_qd") : "");
		order.setDispatch_time(param.containsKey("dispatch_time") ? param.optString("dispatch_time") : "");
		order.setReceive_time_dispatch(param.containsKey("receive_time_dispatch") ? param.optString("receive_time_dispatch") : "");
		order.setDistribution_time(param.containsKey("distribution_time") ? param.optString("distribution_time") : "");
		order.setReceive_time_distribution(param.containsKey("receive_time_distribution") ? param.optString("receive_time_distribution") : "");
		order.setFinish_time(param.containsKey("finish_time") ? param.optString("finish_time") : "");
		order.setReceive_time_finish(param.containsKey("receive_time_finish") ? param.optString("receive_time_finish") : "");
		order.setReceive_time_cancellation(param.containsKey("receive_time_cancellation") ? param.optString("receive_time_cancellation") : "");
		order.setRefund_recive_time(param.containsKey("refund_recive_time") ? param.optString("refund_recive_time") : "");
		order.setRefund_oper_time(param.containsKey("refund_oper_time") ? param.optString("refund_oper_time") : "");
		order.setRefund_operator(param.containsKey("refund_operator") ? param.optString("refund_operator") : "");
		order.setSingle_time(param.containsKey("single_time") ? param.optString("single_time"): "");
		order.setSend_time(param.containsKey("send_time") ? param.optString("send_time"): "");
		return order;
	}

    public double getActual_pay() {
        return actual_pay;
    }

    public void setActual_pay(double actual_pay) {
        this.actual_pay = actual_pay;
    }

    public String getRefund_type() {
		return refund_type;
	}

	public void setRefund_type(String refund_type) {
		this.refund_type = refund_type;
	}

	public String getRefund_recive_time() {
		return refund_recive_time;
	}

	public void setRefund_recive_time(String refund_recive_time) {
		this.refund_recive_time = refund_recive_time;
	}

	public String getRefund_operator() {
		return refund_operator;
	}

	public void setRefund_operator(String refund_operator) {
		this.refund_operator = refund_operator;
	}

	public String getRefund_oper_time() {
		return refund_oper_time;
	}

	public void setRefund_oper_time(String refund_oper_time) {
		this.refund_oper_time = refund_oper_time;
	}

	public String getRefund_reason() {
		return refund_reason;
	}

	public void setRefund_reason(String refund_reason) {
		this.refund_reason = refund_reason;
	}

	public String getRefund_third_order_id() {
		return refund_third_order_id;
	}

	public void setRefund_third_order_id(String refund_third_order_id) {
		refund_third_order_id = refund_third_order_id;
	}
    
    public String getOrder_code()
	{
		return order_code;
	}
	public void setOrder_code(String order_code)
	{
		this.order_code = order_code;
	}
	public String getBill_num()
	{
		return bill_num;
	}
	public void setBill_num(String bill_num)
	{
		this.bill_num = bill_num;
	}
	public String getOrder_state()
	{
		return order_state;
	}
	public void setOrder_state(String order_state)
	{
		this.order_state = order_state;
	}
	public void setSingle_time(String single_time) {
		this.single_time = single_time;
	}

	public String getSingle_time() {
		return single_time;
	}
	public String getReceive_time()
	{
		return receive_time;
	}
	public void setReceive_time(String receive_time)
	{
		this.receive_time = receive_time;
	}
	public String getTake_time()
	{
		return take_time;
	}
	public void setTake_time(String take_time)
	{
		this.take_time = take_time;
	}
	public String getReceive_time_qd()
	{
		return receive_time_qd;
	}
	public void setReceive_time_qd(String receive_time_qd)
	{
		this.receive_time_qd = receive_time_qd;
	}
	public String getDispatch_time()
	{
		return dispatch_time;
	}
	public void setDispatch_time(String dispatch_time)
	{
		this.dispatch_time = dispatch_time;
	}
	public String getReceive_time_dispatch()
	{
		return receive_time_dispatch;
	}
	public void setReceive_time_dispatch(String receive_time_dispatch)
	{
		this.receive_time_dispatch = receive_time_dispatch;
	}
	public String getDistribution_time()
	{
		return distribution_time;
	}
	public void setDistribution_time(String distribution_time)
	{
		this.distribution_time = distribution_time;
	}
	public String getReceive_time_distribution()
	{
		return receive_time_distribution;
	}
	public void setReceive_time_distribution(String receive_time_distribution)
	{
		this.receive_time_distribution = receive_time_distribution;
	}
	public String getFinish_time()
	{
		return finish_time;
	}
	public void setFinish_time(String finish_time)
	{
		this.finish_time = finish_time;
	}
	public String getReceive_time_finish()
	{
		return receive_time_finish;
	}
	public void setReceive_time_finish(String receive_time_finish)
	{
		this.receive_time_finish = receive_time_finish;
	}
	public String getReceive_time_cancellation()
	{
		return receive_time_cancellation;
	}
	public void setReceive_time_cancellation(String receive_time_cancellation)
	{
		this.receive_time_cancellation = receive_time_cancellation;
	}

	public String getOrder_type()
	{
		return order_type;
	}

	public void setOrder_type(String order_type)
	{
		this.order_type = order_type;
	}

	public List<?> getOrder_repayment()
	{
		return order_repayment;
	}

	public void setOrder_repayment(List<?> order_repayment)
	{
		this.order_repayment = order_repayment;
	}

	public String getChanel()
	{
		return chanel;
	}

	public void setChanel(String chanel)
	{
		this.chanel = chanel;
	}

	public String getThird_order_code()
	{
		return third_order_code;
	}

	public void setThird_order_code(String third_order_code)
	{
		this.third_order_code = third_order_code;
	}

    public String getIs_online_payment() {
        return is_online_payment;
    }

    public void setIs_online_payment(String is_online_payment) {
        this.is_online_payment = is_online_payment;
    }

    public String getPayment_state() {
        return payment_state;
    }

    public void setPayment_state(String payment_state)
    {
        this.payment_state = payment_state;
    }

    public String getCancellation_time() {
        return cancellation_time;
    }

    public void setCancellation_time(String cancellation_time) {
        this.cancellation_time = cancellation_time;
    }

    public String getNeed_invoice() {
        return need_invoice;
    }

    public void setNeed_invoice(String need_invoice) {
        this.need_invoice = need_invoice;
    }

    public String getDevice_ip() {
        return device_ip;
    }

    public void setDevice_ip(String device_ip) {
        this.device_ip = device_ip;
    }

    public String getDevice_mac() {
        return device_mac;
    }

    public void setDevice_mac(String device_mac) {
        this.device_mac = device_mac;
    }

    public String getReport_date() {
        return report_date;
    }

    public void setReport_date(String report_date) {
        this.report_date = report_date;
    }

	public String getSend_time() {
		return send_time;
	}

	public void setSend_time(String send_time) {
		this.send_time = send_time;
	}

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        OrderState that = (OrderState) o;

        if (order_code != null ? !order_code.equals(that.order_code) : that.order_code != null) return false;
        if (bill_num != null ? !bill_num.equals(that.bill_num) : that.bill_num != null) return false;
        if (order_state != null ? !order_state.equals(that.order_state) : that.order_state != null) return false;
        if (order_type != null ? !order_type.equals(that.order_type) : that.order_type != null) return false;
        if (chanel != null ? !chanel.equals(that.chanel) : that.chanel != null) return false;
        if (third_order_code != null ? !third_order_code.equals(that.third_order_code) : that.third_order_code != null)
            return false;
        if (receive_time != null ? !receive_time.equals(that.receive_time) : that.receive_time != null) return false;
        if (take_time != null ? !take_time.equals(that.take_time) : that.take_time != null) return false;
        if (receive_time_qd != null ? !receive_time_qd.equals(that.receive_time_qd) : that.receive_time_qd != null)
            return false;
        if (dispatch_time != null ? !dispatch_time.equals(that.dispatch_time) : that.dispatch_time != null)
            return false;
        if (receive_time_dispatch != null ? !receive_time_dispatch.equals(that.receive_time_dispatch) : that.receive_time_dispatch != null)
            return false;
        if (distribution_time != null ? !distribution_time.equals(that.distribution_time) : that.distribution_time != null)
            return false;
        if (receive_time_distribution != null ? !receive_time_distribution.equals(that.receive_time_distribution) : that.receive_time_distribution != null)
            return false;
        if (finish_time != null ? !finish_time.equals(that.finish_time) : that.finish_time != null) return false;
        if (receive_time_finish != null ? !receive_time_finish.equals(that.receive_time_finish) : that.receive_time_finish != null)
            return false;
        if (cancellation_time != null ? !cancellation_time.equals(that.cancellation_time) : that.cancellation_time != null)
            return false;
        if (receive_time_cancellation != null ? !receive_time_cancellation.equals(that.receive_time_cancellation) : that.receive_time_cancellation != null)
            return false;
        if (is_online_payment != null ? !is_online_payment.equals(that.is_online_payment) : that.is_online_payment != null)
            return false;
        if (order_repayment != null ? !order_repayment.equals(that.order_repayment) : that.order_repayment != null)
            return false;
        return payment_state != null ? payment_state.equals(that.payment_state) : that.payment_state == null;
    }

    @Override
    public int hashCode() {
        int result = order_code != null ? order_code.hashCode() : 0;
        result = 31 * result + (bill_num != null ? bill_num.hashCode() : 0);
        result = 31 * result + (order_state != null ? order_state.hashCode() : 0);
        result = 31 * result + (order_type != null ? order_type.hashCode() : 0);
        result = 31 * result + (chanel != null ? chanel.hashCode() : 0);
        result = 31 * result + (third_order_code != null ? third_order_code.hashCode() : 0);
        result = 31 * result + (receive_time != null ? receive_time.hashCode() : 0);
        result = 31 * result + (take_time != null ? take_time.hashCode() : 0);
        result = 31 * result + (receive_time_qd != null ? receive_time_qd.hashCode() : 0);
        result = 31 * result + (dispatch_time != null ? dispatch_time.hashCode() : 0);
        result = 31 * result + (receive_time_dispatch != null ? receive_time_dispatch.hashCode() : 0);
        result = 31 * result + (distribution_time != null ? distribution_time.hashCode() : 0);
        result = 31 * result + (receive_time_distribution != null ? receive_time_distribution.hashCode() : 0);
        result = 31 * result + (finish_time != null ? finish_time.hashCode() : 0);
        result = 31 * result + (receive_time_finish != null ? receive_time_finish.hashCode() : 0);
        result = 31 * result + (cancellation_time != null ? cancellation_time.hashCode() : 0);
        result = 31 * result + (receive_time_cancellation != null ? receive_time_cancellation.hashCode() : 0);
        result = 31 * result + (is_online_payment != null ? is_online_payment.hashCode() : 0);
        result = 31 * result + (order_repayment != null ? order_repayment.hashCode() : 0);
        result = 31 * result + (payment_state != null ? payment_state.hashCode() : 0);
        result = 31 * result + (refund_recive_time != null ? refund_recive_time.hashCode() : 0);
        result = 31 * result + (refund_oper_time != null ? refund_oper_time.hashCode() : 0);
        result = 31 * result + (refund_operator != null ? refund_operator.hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "OrderState{" +
                "order_code='" + order_code + '\'' +
                ", bill_num='" + bill_num + '\'' +
                ", order_state='" + order_state + '\'' +
                ", order_type='" + order_type + '\'' +
                ", chanel='" + chanel + '\'' +
                ", third_order_code='" + third_order_code + '\'' +
                ", receive_time='" + receive_time + '\'' +
                ", take_time='" + take_time + '\'' +
                ", receive_time_qd='" + receive_time_qd + '\'' +
                ", dispatch_time='" + dispatch_time + '\'' +
                ", receive_time_dispatch='" + receive_time_dispatch + '\'' +
                ", distribution_time='" + distribution_time + '\'' +
                ", receive_time_distribution='" + receive_time_distribution + '\'' +
                ", finish_time='" + finish_time + '\'' +
                ", receive_time_finish='" + receive_time_finish + '\'' +
                ", cancellation_time='" + cancellation_time + '\'' +
                ", receive_time_cancellation='" + receive_time_cancellation + '\'' +
                ", is_online_payment='" + is_online_payment + '\'' +
                ", order_repayment=" + order_repayment +
                ", payment_state='" + payment_state + '\'' +
                ", need_invoice='" + need_invoice + '\'' +
                ", actual_pay=" + actual_pay +
                ", device_ip='" + device_ip + '\'' +
                ", device_mac='" + device_mac + '\'' +
                ", report_date='" + report_date + '\'' +
                ", refund_recive_time='" + refund_recive_time + '\'' +
                ", refund_oper_time='" + refund_oper_time + '\'' +
                ", refund_operator='" + refund_operator + '\'' +
                ", single_time='" + single_time + '\'' +
				", send_time='" + send_time + '\'' +
                '}';
    }
}
