package com.tzx.pos.service.servlet.imp;

import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.Tools;
import com.tzx.member.acewill.bo.AcewillCustomerChargeService;
import com.tzx.member.acewill.bo.AcewillCustomerService;
import com.tzx.member.acewill.bo.CustomerService;
import com.tzx.member.common.entity.PosCustomerOperateListEntity;
import com.tzx.member.common.util.AcewillRequestUtil;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.bo.acewill.AcewillPaymentService;
import com.tzx.pos.service.servlet.AcewillCustomerServlet;
import net.sf.json.JSONObject;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service(AcewillCustomerServlet.NAME)
public class AcewillCustomerServletImp implements AcewillCustomerServlet
{

	private static final Logger		logger			= Logger.getLogger(AcewillCustomerServlet.class);

	@Resource(name = AcewillCustomerService.NAME)
	private AcewillCustomerService	customerService;

	@Resource(name = AcewillPaymentService.NAME)
	private AcewillPaymentService	paymentService;

//	@Resource(name = PosPrintService.NAME)
//	private PosPrintService		posPrintService;
//
//	@Resource(name = PosPrintNewService.NAME)
//	private PosPrintNewService posPrintNewService;

	@Resource(name = CustomerService.NAME)
	private CustomerService	alCustomerService;

	@Resource(name = AcewillCustomerChargeService.NAME)
	private AcewillCustomerChargeService	cardRechargeService;

//	@Resource(name = CustomerDao.NAME)
//	protected CustomerDao				customerDao;

//	@Resource(name = PosPaymentDao.NAME)
//	protected PosPaymentDao	 paymentDao;

	@Override
	public Data queryAcewillCustomerInfo(Data paramData) throws Exception
	{
		return customerService.getAcewillCustomerUserInfo(paramData);
	}
	
	public Data queryAcewillCustomerInfo(String tenantId, Integer storeId, String cardcode) throws Exception
	{
		JSONObject paraJson = new JSONObject();
		paraJson.put("card_code", cardcode);
		List<JSONObject> list = new ArrayList<JSONObject>();
		list.add(paraJson);
		Data paramData = Data.get(tenantId, storeId, Constant.CODE_SUCCESS);
		paramData.setData(list);

		return customerService.getAcewillCustomerUserInfo(paramData);
	}

	@Override
	public Data billPaymentForAcewillPreview(Data paramData) throws Exception {
		String tenantId = paramData.getTenancy_id();
		Integer storeId = paramData.getStore_id();
		String source = paramData.getSource();

		List<?> paramList = paramData.getData();

		if (null == paramList || paramList.isEmpty()) {
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}
		JSONObject paramJson = JSONObject.fromObject(paramList.get(0));

		if (customerService.checkIsAcewillCustomer(tenantId, storeId) == false) {
			throw SystemException.getInstance(PosErrorCode.CUSTOMER_NOT_ACEWILL_ERROR);
		}

		String outTradeNo = paymentService.addPosBillPaymentForAcewillPay(tenantId, storeId, source, paramJson);

		Data resultData = null;
		try {
			paramJson.put("out_trade_no", outTradeNo);
			resultData = paymentService.requestAcewillCustomerDealPreview(tenantId, storeId, paramJson);
			resultData.setOper(paramData.getOper());
			resultData.setType(paramData.getType());
			resultData.setPagination(paramData.getPagination());
			resultData.setSecret(paramData.getSecret());
		} catch (SystemException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			logger.error("微生活交易预览报错", e);
			resultData = paramData.clone();
			resultData.setCode(e.getErrorCode().getNumber());
			resultData.setMsg(e.getErrorMsg());
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			logger.error("微生活交易预览报错", e);
			resultData = paramData.clone();
			resultData.setCode(Constant.CODE_PARAM_FAILURE);
			resultData.setMsg(Constant.ORDER_PAYMENT_FAILURE);
		}

//		if (Constant.CODE_SUCCESS != resultData.getCode())
//		{
//			paymentService.deletePosBillPaymentForAcewillPay(tenantId, storeId, paramJson);
//		}

		return resultData;
	}

	@Override
	public Data billPaymentForAcewillCommit(Data paramData) throws Exception
	{
		String tenantId = paramData.getTenancy_id();
		Integer storeId = paramData.getStore_id();
		String source = paramData.getSource();

		List<?> paramList = paramData.getData();

		if (null == paramList || paramList.isEmpty())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}
		JSONObject paramJson = JSONObject.fromObject(paramList.get(0));

		if(customerService.checkIsAcewillCustomer(tenantId, storeId)==false)
		{
			throw SystemException.getInstance(PosErrorCode.CUSTOMER_NOT_ACEWILL_ERROR);
		}

		Data resultData = null;
		JSONObject printJson = new JSONObject();
		try
		{
			resultData = paymentService.requestAcewillCustomerDealCommit(tenantId, storeId, paramJson,printJson);
			resultData.setOper(paramData.getOper());
			resultData.setType(paramData.getType());
			resultData.setPagination(paramData.getPagination());
			resultData.setSecret(paramData.getSecret());
		}
		catch (SystemException e)
		{
			// TODO Auto-generated catch block
			e.printStackTrace();
			logger.error("微生活交易提交报错", e);
			resultData = paramData.clone();
			resultData.setCode(e.getErrorCode().getNumber());
			resultData.setMsg(e.getErrorMsg());
		}
		catch (Exception e)
		{
			// TODO Auto-generated catch block
			e.printStackTrace();
			logger.error("微生活交易提交报错", e);
			resultData = paramData.clone();
			resultData.setCode(Constant.CODE_PARAM_FAILURE);
			resultData.setMsg(Constant.ORDER_PAYMENT_FAILURE);
		}

		if (Constant.CODE_SUCCESS == resultData.getCode())
		{
			// 打印结账单
			if (null != printJson)
			{
//				if (SysDictionary.SOURCE_MOVE.contains(source)||SysDictionary.SOURCE_ORDER_DEVICE.equals(source))
//				{
//					posPrintService.getBindOptNum(printJson.optString("tenancy_id"), printJson.optInt("store_id"), printJson);
//				}
//				printJson.put("source", source);
//
//				// 打印会员小票
//				customerService.printForCustomer(tenantId, storeId, SysDictionary.PRINT_CODE_1010, printJson);
//
////				if("Y".equals(printJson.optString("isprint")))
////				{
//					posPrintService.printPosBillForPayment(printJson, posPrintNewService);
////				}
				
				paymentService.printPosBillForPayment(tenantId, storeId, source, printJson);
			}

			paymentService.upload(tenantId, String.valueOf(storeId), "", "", "", paramJson.optString("bill_num"));
		}
		return resultData;
	}

	@Override
	public Data checkAcewillCoupons(Data paramData) throws Exception
	{
		String tenantId = paramData.getTenancy_id();
		Integer storeId = paramData.getStore_id();

		List<?> paramList = paramData.getData();

		if (null == paramList || paramList.isEmpty())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}
		JSONObject paramJson = JSONObject.fromObject(paramList.get(0));

		if(customerService.checkIsAcewillCustomer(tenantId, storeId)==false)
		{
			throw SystemException.getInstance(PosErrorCode.CUSTOMER_NOT_ACEWILL_ERROR);
		}

		List<JSONObject> resultList = paymentService.checkAcewillCoupons(tenantId, storeId, paramJson);

		paramData.setData(resultList);

		return paramData;
	}

	@Override
	public Data sendCodeAcewillCustomer(Data paramData) throws Exception
	{
		// TODO Auto-generated method stub
		String tenantId = paramData.getTenancy_id();
		Integer storeId = paramData.getStore_id();

		if(customerService.checkIsAcewillCustomer(tenantId, storeId)==false)
		{
			throw SystemException.getInstance(PosErrorCode.CUSTOMER_NOT_ACEWILL_ERROR);
		}
		return customerService.sendCodeAcewillCustomer(paramData);
	}

	//豪享来 微生活会员对接============================

	/**
	 * 修改会员信息
	 */
	@Override
	public Data editAcewillCustomerUserInfo(Data requestData) throws Exception {
		String tenantId = requestData.getTenancy_id();
		Integer storeId = requestData.getStore_id();
		List<?> paramList = requestData.getData();
		if (null == paramList || paramList.isEmpty())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}
		//校验
		if(customerService.checkIsAcewillCustomer(tenantId, storeId)==false)
		{
			throw SystemException.getInstance(PosErrorCode.CUSTOMER_NOT_ACEWILL_ERROR);
		}
		return alCustomerService.editAcewillCustomerUserInfo(requestData);
	}

	/**
	 *微生活基础信息
	 */
	public Data queryAcewillBasicData(Data requestData) throws Exception{

		String tenantId = requestData.getTenancy_id();
		Integer storeId = requestData.getStore_id();
		//校验
		if(customerService.checkIsAcewillCustomer(tenantId, storeId)==false)
		{
			throw SystemException.getInstance(PosErrorCode.CUSTOMER_NOT_ACEWILL_ERROR);
		}

		JSONObject dataJson = new JSONObject();
		//查询微生活收银员
		Data cashierData  = alCustomerService.findAcewillCashierList(requestData);
		if(Constant.CODE_SUCCESS == cashierData.getCode()){
			dataJson.put("cashierList",cashierData.getData());
		}else{
			dataJson.put("cashierList",new ArrayList<JSONObject>());
		}
		
		//查询微生活门店员工
		Data employeeData  = alCustomerService.findAcewillEmployeeList(requestData);
		if(Constant.CODE_SUCCESS == employeeData.getCode()){
			dataJson.put("employee_list",employeeData.getData());
		}else{
			dataJson.put("employee_list",new ArrayList<JSONObject>());
		}
		
		//储值支付方式
//		Data payTypeData  =alCustomerService.findAcewillDealPayType(requestData);
//		if(Constant.CODE_SUCCESS == payTypeData.getCode()){
//			dataJson.put("payTypeList",payTypeData.getData());
//		}else{
//			dataJson.put("payTypeList",new ArrayList());
//		}

		//门店储值规则设置
		Data chargeRuleData = alCustomerService.findAcewillChargeRule(requestData); 
		if(Constant.CODE_SUCCESS == chargeRuleData.getCode()){
			dataJson.put("chargeRuleList",chargeRuleData.getData());
		}else{
			dataJson.put("chargeRuleList",new ArrayList<JSONObject>());
		}

		List<JSONObject> data = new ArrayList<JSONObject>();
		data.add(dataJson);
		
		Data resData = new Data();
		resData.setData(data);
		return resData;
	}

	/**
	 * 储值预览
	 */
	@Override
	public Data previewAcewillCharge(Data paramData) throws Exception {

		String tenantId = paramData.getTenancy_id();
		Integer storeId = paramData.getStore_id();
		List<?> paramList = paramData.getData();

		//验证参数是否为空
		if (null == paramList || paramList.isEmpty())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}

		//验证是否微生活会员
		if(customerService.checkIsAcewillCustomer(tenantId, storeId)==false)
		{
			throw SystemException.getInstance(PosErrorCode.CUSTOMER_NOT_ACEWILL_ERROR);
		}

		Data resultData = paramData.clone();
		try
		{
			//微生活储值预览
			resultData = cardRechargeService.previewAcewillCharge(paramData);


			if(Constant.CODE_SUCCESS == resultData.getCode()){
				// 插入会员操作流水表
				JSONObject paramJson = JSONObject.fromObject(paramData.getData().get(0));
				JSONObject resultJson = JSONObject.fromObject(resultData.getData().get(0));
				paramJson.put("third_bill_code",ParamUtil.getStringValueByObject(resultJson, "third_bill_code"));
				paramJson.put("third_bill_code_timestamp",ParamUtil.getStringValueByObject(resultJson, "third_bill_code_timestamp"));
				paramJson.put("charge_total", ParamUtil.getDoubleValueByObject(resultJson, "charge_total"));//本次储值金
				paramJson.put("money", ParamUtil.getDoubleValueByObject(resultJson, "money"));//实收金额
				paramJson.put("gift", ParamUtil.getDoubleValueByObject(resultJson, "gift")); //赠送金额
				paramJson.put("award_credit", ParamUtil.getIntegerValueByObject(resultJson, "award_credit"));//赠送积分
				paramJson.put("expired", ParamUtil.getStringValueByObject(resultJson, "expired")); //储值金额有效期
				paramJson.put("deal_id", ParamUtil.getStringValueByObject(resultJson, "deal_id"));//储值订单流水号
				paramJson.put("biz_id", ParamUtil.getStringValueByObject(resultJson, "third_bill_code"));//储值业务号
				paramJson.put("award_coupons",resultJson.get("award_coupons"));//赠送券
				paramJson.put("expired",ParamUtil.getStringValueByObject(resultJson, "expired"));//储值有效期
				logger.debug("paramJson====="+paramJson);
				cardRechargeService.addCustomerOperateListForRecharge(tenantId, storeId, SysDictionary.OPERAT_TYPE_CZ, paramJson);
			}
		}
		catch (SystemException e)
		{
			e.printStackTrace();
			logger.error("微生活储值预览报错", e);
			resultData = paramData.clone();
			resultData.setCode(e.getErrorCode().getNumber());
			resultData.setMsg(e.getErrorMsg());
		}
		catch (Exception e)
		{
			e.printStackTrace();
			logger.error("微生活储值预览报错", e);
			resultData = paramData.clone();
			resultData.setCode(Constant.CODE_INNER_EXCEPTION);
			resultData.setMsg("微生活储值预览报错");
		}
		return resultData;
	}

	/**
	 * 微生活储值提交
	 * @param requestData
	 * @return
	 * @throws Exception
	 */
	@Override
	public Data commitAcewillCharge(Data requestData) throws Exception {

		String tenantId = requestData.getTenancy_id();
		Integer storeId = requestData.getStore_id();

		List<?> paramList = requestData.getData();

		//校验参数
		if (null == paramList || paramList.isEmpty())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}

		//校验会员类型
		if(customerService.checkIsAcewillCustomer(tenantId, storeId)==false)
		{
			throw SystemException.getInstance(PosErrorCode.CUSTOMER_NOT_ACEWILL_ERROR);
		}
		JSONObject paramJson = JSONObject.fromObject(paramList.get(0));
		String thirdBillCode = paramJson.optString("bill_code");//储值订单流水号

		//提交储值
		Data resultData = cardRechargeService.commitAcewillCharge(tenantId,storeId,thirdBillCode,paramJson);

		if(Constant.CODE_SUCCESS == resultData.getCode()){
			
			//打印小票、发票
			JSONObject resultJson = JSONObject.fromObject(resultData.getData().get(0));
			if (Tools.hv(resultJson)) {
				Data paraData = resultData.clone();
				Data customerUserInfo = this.queryAcewillCustomerInfo(paraData);
				if(Constant.CODE_SUCCESS == customerUserInfo.getCode()){
					JSONObject userInfo = JSONObject.fromObject(customerUserInfo.getData().get(0));
					resultJson.put("useful_credit",userInfo.optInt("credit")); //充值后积分
					resultJson.put("before_credit",(userInfo.optInt("credit") - resultJson.optInt("reward_credit")));//充值后前积分
					resultJson.put("main_balance",userInfo.optDouble("balance"));//充值后金额
					resultJson.put("before_balance",userInfo.optDouble("balance")  - resultJson.optDouble("charge_total"));//充值前金额
					resultJson.put("level_name",userInfo.optString("level_name"));
					resultJson.put("card_class_name",userInfo.optString("type_name"));
				}
				cardRechargeService.printForCustomerCardRecharge(tenantId, storeId,resultJson);
			}
		}
		return resultData;
	}

	@Override
	public Data precreatePaymentForRecharge(Data paramData, String path) throws Exception {
		String tenantId = paramData.getTenancy_id();
		Integer storeId = paramData.getStore_id();
		List<?> paramList = paramData.getData();

		//校验参数
		if (null == paramList || paramList.isEmpty())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}

		//校验会员类型
		if(customerService.checkIsAcewillCustomer(tenantId, storeId)==false)
		{
			throw SystemException.getInstance(PosErrorCode.CUSTOMER_NOT_ACEWILL_ERROR);
		}

		JSONObject paramJson = JSONObject.fromObject(paramList.get(0));

		// 获取二维码
		Data resultData = cardRechargeService.precreatePaymentForRecharge(tenantId, storeId, paramJson, path);
		return resultData;
	}

	@Override
	public Data barcodePaymentForRecharge(Data paramData) throws Exception {
		String tenantId = paramData.getTenancy_id();
		Integer storeId = paramData.getStore_id();
		List<?> paramList = paramData.getData();

		if (null == paramList || paramList.isEmpty()) {
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}

		//校验会员类型
		if(customerService.checkIsAcewillCustomer(tenantId, storeId)==false)
		{
			throw SystemException.getInstance(PosErrorCode.CUSTOMER_NOT_ACEWILL_ERROR);
		}

		JSONObject paramJson = JSONObject.fromObject(paramList.get(0));

		Data resultData = paramData.clone();
		// 扫码支付
		resultData = cardRechargeService.barcodePaymentForRecharge(tenantId, storeId, paramJson);

		if (Constant.CODE_SUCCESS == resultData.getCode()) {
			// 支付成功
			JSONObject resultJson = JSONObject.fromObject(resultData.getData().get(0));

			if (SysDictionary.THIRD_PAY_STATUS_SUCCESS.equals(resultJson.optString("payment_state"))) {
				String thirdBillCode = paramJson.optString("bill_code");//储值订单流水号

				//提交储值
				resultData = cardRechargeService.commitAcewillCharge(tenantId,storeId,thirdBillCode,paramJson);
				resultData.setOper(paramData.getOper());

				if (Constant.CODE_SUCCESS == resultData.getCode()) {//储值提交成功
					// 打印小票
					JSONObject rechargeResultJson = JSONObject.fromObject(resultData.getData().get(0));
					if (Tools.hv(rechargeResultJson)) {
						Data paraData = resultData.clone();
						Data customerUserInfo = this.queryAcewillCustomerInfo(paraData);
						if(Constant.CODE_SUCCESS == customerUserInfo.getCode()){
							JSONObject userInfo = JSONObject.fromObject(customerUserInfo.getData().get(0));
							rechargeResultJson.put("useful_credit",userInfo.optInt("credit")); //充值后积分
							rechargeResultJson.put("before_credit",(userInfo.optInt("credit") - rechargeResultJson.optInt("reward_credit")));//充值后前积分
							rechargeResultJson.put("main_balance",userInfo.optDouble("balance"));//充值后金额
							rechargeResultJson.put("before_balance",userInfo.optDouble("balance")  - rechargeResultJson.optDouble("charge_total"));//充值前金额
							rechargeResultJson.put("level_name",userInfo.optString("level_name"));
							rechargeResultJson.put("card_class_name",userInfo.optString("type_name"));

							cardRechargeService.printForCustomerCardRecharge(tenantId, storeId, rechargeResultJson);
						}
					}
				}else{ //储值提交失败
					int i = 0;
					while ( i<3){//重试3次
						if(Constant.CODE_SUCCESS != (resultData = cardRechargeService.commitAcewillCharge(tenantId,storeId,thirdBillCode,paramJson)).getCode()){
							// 请求会员充值
							Thread.sleep(3000);
							i++;
						}else{
							resultData.setOper(paramData.getOper());
							// 打印小票
							JSONObject rechargeResultJson = JSONObject.fromObject(resultData.getData().get(0));
							if (Tools.hv(rechargeResultJson)) {
								Data paraData = resultData.clone();
								Data customerUserInfo = this.queryAcewillCustomerInfo(paraData);
								if(Constant.CODE_SUCCESS == customerUserInfo.getCode()){
									JSONObject userInfo = JSONObject.fromObject(customerUserInfo.getData().get(0));
									rechargeResultJson.put("useful_credit",userInfo.optInt("credit")); //充值后积分
									rechargeResultJson.put("before_credit",(userInfo.optInt("credit") - rechargeResultJson.optInt("reward_credit")));//充值后前积分
									rechargeResultJson.put("main_balance",userInfo.optDouble("balance"));//充值后金额
									rechargeResultJson.put("before_balance",userInfo.optDouble("balance")  - rechargeResultJson.optDouble("charge_total"));//充值前金额
									rechargeResultJson.put("level_name",userInfo.optString("level_name"));
									rechargeResultJson.put("card_class_name",userInfo.optString("type_name"));
									cardRechargeService.printForCustomerCardRecharge(tenantId, storeId, rechargeResultJson);
								}
							}
							break;
						}
					}
					//重试3次未成功

				}
			}
		}
		return resultData;
	}

	@Override
	public Data queryCustomerCardRechargeForState(Data paramData) throws Exception {
		String tenantId = paramData.getTenancy_id();
		Integer storeId = paramData.getStore_id();
		List<?> paramList = paramData.getData();

		if (null == paramList || paramList.isEmpty())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}
		//校验会员类型
		if(customerService.checkIsAcewillCustomer(tenantId, storeId)==false)
		{
			throw SystemException.getInstance(PosErrorCode.CUSTOMER_NOT_ACEWILL_ERROR);
		}

		JSONObject paramJson = JSONObject.fromObject(paramList.get(0));

		// 根据bill_code查询pos_customer_operate_list,返回充值状态
		String cardCode = ParamUtil.getStringValueByObject(paramJson, "card_code");
		String thirdBillCode = ParamUtil.getStringValueByObject(paramJson, "bill_code");
		//thirdBillCode = "TH3920190709000074";
		
		PosCustomerOperateListEntity customer = cardRechargeService.queryCustomerCardRecharge(tenantId, storeId, thirdBillCode, cardCode, SysDictionary.OPERAT_TYPE_CZ);

		JSONObject resultJson = new JSONObject();
		if (null != customer)
		{
			if (SysDictionary.CUSTOMER_OPERATE_STATE_SUCCESS.equals(customer.getOperate_state()))
			{
				// 充值成功,查询充值记录明细
				resultJson = cardRechargeService.queryCustomerCardRechargeForDetails(tenantId, storeId, customer);
				resultJson.put("card_code", customer.getCard_code());
				resultJson.put("bill_code", customer.getThird_bill_code());
				resultJson.put("third_bill_code", customer.getThird_bill_code());
				resultJson.put("payment_state", customer.getPayment_state());
				resultJson.put("operate_state", customer.getOperate_state());
			}
			else if (SysDictionary.THIRD_PAY_STATUS_SUCCESS.equals(customer.getPayment_state()))
			{
				String paymentState = customer.getPayment_state();
	            String operateState = customer.getOperate_state();
				
				JSONObject printJson = new JSONObject();
				Data findData = cardRechargeService.findCustomerCardChargeDetails(tenantId, storeId, customer,printJson);
				if (Constant.CODE_SUCCESS == findData.getCode())
				{
					JSONObject findResultJson = null;
					if (null != findData.getData() && 0 < findData.getData().size())
					{
						findResultJson = JSONObject.fromObject(findData.getData().get(0));
					}
					Integer status = ParamUtil.getIntegerValueByObject(findResultJson, "status");
					
					if(1==status.intValue())
					{
						if (Tools.hv(printJson))
						{
							Data customerUserInfo = this.queryAcewillCustomerInfo(tenantId, storeId, customer.getCard_code());
							if (Constant.CODE_SUCCESS == customerUserInfo.getCode())
							{
								JSONObject userInfo = JSONObject.fromObject(customerUserInfo.getData().get(0));
								printJson.put("useful_credit", userInfo.optInt("credit")); // 充值后积分
								printJson.put("before_credit", (userInfo.optInt("credit") - printJson.optInt("reward_credit")));// 充值后前积分
								printJson.put("main_balance", userInfo.optDouble("balance"));// 充值后金额
								printJson.put("before_balance", userInfo.optDouble("balance") - printJson.optDouble("charge_total"));// 充值前金额
								printJson.put("level_name", userInfo.optString("level_name"));
								printJson.put("card_class_name", userInfo.optString("type_name"));
							}
							cardRechargeService.printForCustomerCardRecharge(tenantId, storeId, printJson);
						}
						
						operateState = SysDictionary.CUSTOMER_OPERATE_STATE_SUCCESS;
					}
					else 
					{
						JSONObject jsonObject = new JSONObject();
						jsonObject.put("is_diy", "0");
						Data commitData = cardRechargeService.commitAcewillCharge(tenantId, storeId, thirdBillCode, jsonObject);
						
						if(Constant.CODE_SUCCESS == commitData.getCode()){
							//打印小票、发票
							printJson = JSONObject.fromObject(commitData.getData().get(0));
							operateState = ParamUtil.getStringValueByObject(printJson, "operate_state");
							
							if (Tools.hv(printJson)) {
								Data customerUserInfo = this.queryAcewillCustomerInfo(tenantId, storeId, customer.getCard_code());
								if(Constant.CODE_SUCCESS == customerUserInfo.getCode()){
									JSONObject userInfo = JSONObject.fromObject(customerUserInfo.getData().get(0));
									printJson.put("useful_credit",userInfo.optInt("credit")); //充值后积分
									printJson.put("before_credit",(userInfo.optInt("credit") - printJson.optInt("reward_credit")));//充值后前积分
									printJson.put("main_balance",userInfo.optDouble("balance"));//充值后金额
									printJson.put("before_balance",userInfo.optDouble("balance")  - printJson.optDouble("charge_total"));//充值前金额
									printJson.put("level_name",userInfo.optString("level_name"));
									printJson.put("card_class_name",userInfo.optString("type_name"));
								}
								cardRechargeService.printForCustomerCardRecharge(tenantId, storeId,printJson);
							}
						}
					}
				}
				
				resultJson.put("card_code", customer.getCard_code());
				resultJson.put("bill_code", customer.getThird_bill_code());
				resultJson.put("third_bill_code", customer.getThird_bill_code());
				resultJson.put("payment_state", paymentState);
				resultJson.put("operate_state", operateState);
				
			}else 
			{
				resultJson.put("card_code", customer.getCard_code());
				resultJson.put("bill_code", customer.getThird_bill_code());
				resultJson.put("third_bill_code", customer.getThird_bill_code());
				resultJson.put("payment_state", customer.getPayment_state());
				resultJson.put("operate_state", customer.getOperate_state());
			}
		}
		else
		{
			resultJson.put("card_code", cardCode);
			resultJson.put("bill_code", thirdBillCode);
			resultJson.put("third_bill_code", thirdBillCode);
			resultJson.put("payment_state", SysDictionary.THIRD_PAY_STATUS_FAIL);
			resultJson.put("operate_state", SysDictionary.CUSTOMER_OPERATE_STATE_FAIL);
		}

		List<JSONObject> resultList = new ArrayList<JSONObject>();
		resultList.add(resultJson);

		Data resultData = paramData.clone();
		resultData.setData(resultList);
		resultData.setCode(Constant.CODE_SUCCESS);
		return resultData;
	}

	@Override
	public Data cancelAcewillCharge(Data paramData) throws Exception {
		String tenantId = paramData.getTenancy_id();
		Integer storeId = paramData.getStore_id();
		List<?> paramList = paramData.getData();
//		StringBuffer newstate = new StringBuffer();

		//校验参数
		if (null == paramList || paramList.isEmpty())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}

		//校验会员类型
		if(customerService.checkIsAcewillCustomer(tenantId, storeId)==false)
		{
			throw SystemException.getInstance(PosErrorCode.CUSTOMER_NOT_ACEWILL_ERROR);
		}

		JSONObject paramJson = JSONObject.fromObject(paramList.get(0));
		Date reportDate = ParamUtil.getDateValueByObject(paramJson, "report_date");
		Integer shiftId = ParamUtil.getIntegerValueByObject(paramJson, "shift_id");
		String optNum = ParamUtil.getStringValueByObject(paramJson, "opt_num");
		String posNum = ParamUtil.getStringValueByObject(paramJson, "pos_num");
		String cardCode = ParamUtil.getStringValueByObject(paramJson, "card_code");
		String thirdBillCode = ParamUtil.getStringValueByObject(paramJson, "third_bill_code");

		//查询会员操作记录
		PosCustomerOperateListEntity customer = cardRechargeService.queryCustomerCardRecharge(tenantId, storeId, thirdBillCode, cardCode, SysDictionary.OPERAT_TYPE_CZ);

		Data resultData = paramData.clone();
		if (customer != null)
		{
			if (SysDictionary.CUSTOMER_OPERATE_STATE_SUCCESS.equals(customer.getOperate_state()))
			{
				// 充值成功,返回前端取消充值失败,
				resultData.setCode(PosErrorCode.CARD_RECHARGE_COMPLETE_NOT_PERMIT_CANCEL_ERROR.getNumber());
				resultData.setMsg(PosErrorCode.CARD_RECHARGE_COMPLETE_NOT_PERMIT_CANCEL_ERROR.getMessage());
			}
			else if (SysDictionary.CUSTOMER_OPERATE_STATE_FAIL.equals(customer.getOperate_state())) //储值失败
			{
				if(SysDictionary.THIRD_PAY_STATUS_SUCCESS.equals(customer.getPayment_state())){//付款成功 调用第三方退款
					resultData = cardRechargeService.cancelThirdPaymentForCardRecharge(tenantId, storeId, thirdBillCode, customer);
				}else{
					// 充值失败,返回前端取消成功,
					resultData.setCode(Constant.CODE_SUCCESS);
					resultData.setMsg(Constant.CUSTOMER_CARD_RECHARGE_SUCCESS);
				}
			}
			else if (SysDictionary.CUSTOMER_OPERATE_STATE_PROCESS.equals(customer.getOperate_state()))
			{
				JSONObject printJson = new JSONObject();
				Data findData = cardRechargeService.findCustomerCardChargeDetails(tenantId, storeId, customer,printJson);
				if (Constant.CODE_SUCCESS == findData.getCode())
				{
					JSONObject findResultJson = null;
					if (null != findData.getData() && 0 < findData.getData().size())
					{
						findResultJson = JSONObject.fromObject(findData.getData().get(0));
					}
					Integer status = ParamUtil.getIntegerValueByObject(findResultJson, "status");
					
					if(1==status.intValue())
					{
						if (Tools.hv(printJson))
						{
							Data customerUserInfo = this.queryAcewillCustomerInfo(tenantId, storeId, customer.getCard_code());
							if (Constant.CODE_SUCCESS == customerUserInfo.getCode())
							{
								JSONObject userInfo = JSONObject.fromObject(customerUserInfo.getData().get(0));
								printJson.put("useful_credit", userInfo.optInt("credit")); // 充值后积分
								printJson.put("before_credit", (userInfo.optInt("credit") - printJson.optInt("reward_credit")));// 充值后前积分
								printJson.put("main_balance", userInfo.optDouble("balance"));// 充值后金额
								printJson.put("before_balance", userInfo.optDouble("balance") - printJson.optDouble("charge_total"));// 充值前金额
								printJson.put("level_name", userInfo.optString("level_name"));
								printJson.put("card_class_name", userInfo.optString("type_name"));
							}
							cardRechargeService.printForCustomerCardRecharge(tenantId, storeId, printJson);
						}
						
						// 充值成功,返回前端取消充值失败,
						resultData.setCode(PosErrorCode.CARD_RECHARGE_COMPLETE_NOT_PERMIT_CANCEL_ERROR.getNumber());
						resultData.setMsg(PosErrorCode.CARD_RECHARGE_COMPLETE_NOT_PERMIT_CANCEL_ERROR.getMessage());
					}
					else 
					{
						resultData = cardRechargeService.cancelThirdPaymentForCardRecharge(tenantId, storeId, thirdBillCode, customer);
					}
				}
				else {
					// 处理中,先撤销充值,撤销充值成功后取消付款
//					paramJson.put("biz_id",thirdBillCode);
//					paramJson.put("opt_num",customer.getOperator_id());
//					List<JSONObject> data = new ArrayList<>();
//					data.add(paramJson);
//					paramData.setData(data);
//					resultData = cardRechargeService.revokeAcewillCharge(tenantId, storeId, thirdBillCode,paramData);
					resultData = cardRechargeService.revokeAcewillCharge(tenantId, storeId, reportDate, shiftId, posNum, optNum, thirdBillCode, customer);
					// 撤销成功,再取消付款
					if (Constant.CODE_SUCCESS == resultData.getCode() || AcewillRequestUtil.TRADE_ABNORMAL_ERROR_CODE == resultData.getCode())
					{
						resultData = cardRechargeService.cancelThirdPaymentForCardRecharge(tenantId, storeId, thirdBillCode, customer);
					}
				}
			}
		}
		else
		{
			resultData.setCode(Constant.CODE_NULL_DATASET);
			resultData.setMsg(Constant.CUSTOMER_CARD_RECHARGE_FAILURE);
		}

		alCustomerService.savePosLog(tenantId, storeId, posNum, optNum, null, shiftId, reportDate, Constant.TITLE, "微生活取消支付", "储值业务号:" + thirdBillCode, "");
		return resultData;
	}

	@Override
	public Data findAcewillChargeUser(Data paramData) throws Exception {
		String tenantId = paramData.getTenancy_id();
		Integer storeId = paramData.getStore_id();
		List<?> paramList = paramData.getData();
		if (null == paramList || paramList.isEmpty())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}

		if(customerService.checkIsAcewillCustomer(tenantId, storeId)==false)
		{
			throw SystemException.getInstance(PosErrorCode.CUSTOMER_NOT_ACEWILL_ERROR);
		}


		return alCustomerService.findAcewillChargeUser(paramData);
	}

	@Override
	public Data findAcewillConsumeUser(Data paramData) throws Exception {
		String tenantId = paramData.getTenancy_id();
		Integer storeId = paramData.getStore_id();
		List<?> paramList = paramData.getData();
		if (null == paramList || paramList.isEmpty())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}

		if(customerService.checkIsAcewillCustomer(tenantId, storeId)==false)
		{
			throw SystemException.getInstance(PosErrorCode.CUSTOMER_NOT_ACEWILL_ERROR);
		}
		return alCustomerService.findAcewillConsumeUser(paramData);
	}

	@Override
	public Data revokeAcewillCharge(Data paramData) throws Exception {
		String tenantId = paramData.getTenancy_id();
		Integer storeId = paramData.getStore_id();
		List<?> paramList = paramData.getData();

		//验证参数
		if (null == paramList || paramList.isEmpty())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}

		//验证会员类型
		if(customerService.checkIsAcewillCustomer(tenantId, storeId)==false)
		{
			throw SystemException.getInstance(PosErrorCode.CUSTOMER_NOT_ACEWILL_ERROR);
		}

		JSONObject paramJson = JSONObject.fromObject(paramList.get(0));

		// 机台号
		String posNum = paramJson.optString("pos_num");
		String optNum = paramJson.optString("opt_num");
		String report_date = paramJson.optString("report_date");
		Integer shift_id = paramJson.optInt("shift_id");

		Data resultData =  paramData.clone();

		String billCodeOriginal = ParamUtil.getStringValueByObject(paramJson, "biz_id");
		String ts = String.valueOf(DateUtil.currentTimestamp().getTime());
		String third_bill_code = alCustomerService.createThirdBillCode(tenantId, storeId, paramJson);

		paramJson.put("bill_code_original", billCodeOriginal);
		paramJson.put("third_bill_code", third_bill_code);
		paramJson.put("third_bill_code_timestamp", ts);

		// 插入会员操作记录表
		//String thirdBillCode = alCustomerService.addCustomerOperateListForRecharge(tenantId, storeId, SysDictionary.OPERAT_TYPE_FCZ, paramJson);

		// 撤销储值
		resultData = cardRechargeService.revokeAcewillCharge(tenantId, storeId, third_bill_code,paramData);

		// 撤销成功,再取消付款
		if (Constant.CODE_SUCCESS == resultData.getCode())
		{
			JSONObject resultJson = JSONObject.fromObject(resultData.getData().get(0));
			// 插入会员操作记录表
			paramJson.put("deal_id",resultJson.optString("deal_id"));
			//微生活支付方式转换成tzx 支付方式
			paramJson.put("charge_type",resultJson.optInt("payment_id"));
			cardRechargeService.addCustomerOperateListForRecharge(tenantId, storeId, SysDictionary.OPERAT_TYPE_FCZ, paramJson);

			PosCustomerOperateListEntity customerOperate = cardRechargeService.queryCustomerCardRecharge(tenantId, storeId, third_bill_code, null, SysDictionary.OPERAT_TYPE_FCZ);

			if (SysDictionary.PAYMENT_CLASS_WECHAT_PAY.equals(customerOperate.getPayment_class()) || SysDictionary.PAYMENT_DDM_QUICK_PAY.equals(customerOperate.getPayment_class()) || SysDictionary.PAYMENT_CLASS_ALI_PAY.equals(customerOperate.getPayment_class()))
			{//调用第三方退款
				logger.info("撤销储值 支付方式："+customerOperate.getPayment_class());
				Data result= cardRechargeService.cancelThirdPaymentForCardRecharge(tenantId, storeId, billCodeOriginal,customerOperate);//原账单号查询会员操作记录
				logger.info("返回结果" +result.getMsg());
			}

			if (Constant.CODE_SUCCESS == resultData.getCode())
			{
				Data paraData = resultData.clone();
				//打印小票、发票
				if (Tools.hv(resultJson)) {
					Data customerUserInfo = this.queryAcewillCustomerInfo(paraData);
					if(Constant.CODE_SUCCESS == customerUserInfo.getCode()){
						JSONObject userInfo = JSONObject.fromObject(customerUserInfo.getData().get(0));
						resultJson.put("useful_credit",userInfo.optInt("credit")); //反充值后积分
						resultJson.put("before_credit",(userInfo.optInt("credit") - customerOperate.getTrade_credit()));//反充值前积分
						resultJson.put("main_balance",userInfo.optDouble("balance"));//反充值后金额
						resultJson.put("before_balance",userInfo.optDouble("balance")  - resultJson.optDouble("charge_total"));//反充值前金额
						resultJson.put("level_name",userInfo.optString("level_name"));
						resultJson.put("card_class_name",userInfo.optString("type_name"));

					}
				}

				// 取消电子发票
				JSONObject invoiceJson = new JSONObject();
				invoiceJson.put("bill_code_original", third_bill_code);
				//cardRechargeService.cancelElectricInvoice(tenantId, storeId, invoiceJson);
				// 打印小票
				resultJson.put("pos_num", posNum);
				cardRechargeService.printForCustomerCardRecharge(tenantId, storeId, SysDictionary.PRINT_CODE_1009, resultJson);
			}

		}
		
		alCustomerService.savePosLog(tenantId, storeId, posNum, optNum, null, shift_id, DateUtil.parseDate(report_date), Constant.TITLE, "微生活储值撤销", "储值业务号:" + billCodeOriginal, "储值业务号:" + third_bill_code);
		return resultData;
	}

	@Override
	public Data findAcewillCreditRule(Data requestData) throws Exception {
		String tenantId = requestData.getTenancy_id();
		Integer storeId = requestData.getStore_id();
		if(customerService.checkIsAcewillCustomer(tenantId, storeId)==false)
		{
			throw SystemException.getInstance(PosErrorCode.CUSTOMER_NOT_ACEWILL_ERROR);
		}
		return alCustomerService.findAcewillCreditRule(requestData);
	}

	@Override
	public Data creditExchangeGifts(Data paramData) throws Exception {
		String tenantId = paramData.getTenancy_id();
		Integer storeId = paramData.getStore_id();
		List<?> paramList = paramData.getData();
		if (null == paramList || paramList.isEmpty())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}

		if(customerService.checkIsAcewillCustomer(tenantId, storeId)==false)
		{
			throw SystemException.getInstance(PosErrorCode.CUSTOMER_NOT_ACEWILL_ERROR);
		}

		JSONObject paramJson = JSONObject.fromObject(paramList.get(0));
		Data resultData = alCustomerService.creditExchangeGifts(paramData);
//		if(Constant.CODE_SUCCESS == resultData.getCode()){
//			String card_code = paramJson.optString("card_code");
//			String  opt_num = paramJson.optString("opt_num");
//			String  pos_num = paramJson.optString("pos_num");
//			Integer  sub_credit = paramJson.optInt("sub_credit");
//			JSONArray desc = paramJson.getJSONArray("desc");
//			//打印
//			String operator = customerDao.getEmpNameById(opt_num, tenantId, storeId);
//			JSONObject printJson = new JSONObject();
//			printJson.put("card_code",card_code);
//			printJson.put("sub_credit",sub_credit);//使用积分
//			printJson.put("operator", operator);
//			printJson.put("updatetime", DateUtil.getNowDateYYDDMMHHMMSS());
//			printJson.put("pos_num", pos_num);
//
//			if(desc.size()>0){
//				for(int i=0;i<desc.size();i++){
//
//				}
//			}
//			if(posPrintNewService.isNONewPrint(tenantId, storeId)){ // 启用新打印
//				posPrintNewService.posPrintByMode(tenantId, storeId, SysDictionary.PRINT_CODE_1017, printJson);  //使用PRINT_CODE_1017进行积分兑换
//			}else{
//				customerDao.customerPrint(tenantId, storeId, pos_num, SysDictionary.PRINT_CODE_1017,"1", printJson, 1);
//			}
//		}

		return resultData;
	}

	public Data findAcewillCustomerGrade(Data requestData) throws Exception{
		String tenantId = requestData.getTenancy_id();
		Integer storeId = requestData.getStore_id();

		if(customerService.checkIsAcewillCustomer(tenantId, storeId)==false)
		{
			throw SystemException.getInstance(PosErrorCode.CUSTOMER_NOT_ACEWILL_ERROR);
		}
		return alCustomerService.findAcewillCustomerGrade(requestData);
	}



	public Data makeAcewillCustomerCard(Data paramData) throws Exception{
		String tenantId = paramData.getTenancy_id();
		Integer storeId = paramData.getStore_id();
		List<?> paramList = paramData.getData();
		if (null == paramList || paramList.isEmpty())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}

		if(customerService.checkIsAcewillCustomer(tenantId, storeId)==false)
		{
			throw SystemException.getInstance(PosErrorCode.CUSTOMER_NOT_ACEWILL_ERROR);
		}

		Data resultData  = alCustomerService.makeAcewillCustomerCard(paramData);//制卡
		if(Constant.CODE_SUCCESS == resultData.getCode() || resultData.getCode() == 22026){ //22026 卡号存在
			resultData  = alCustomerService.checkAcewillCustomerCard(paramData);//验卡
		}
		return  resultData;
	}

	public Data checkAcewillCustomerCard(Data paramData) throws Exception{
		String tenantId = paramData.getTenancy_id();
		Integer storeId = paramData.getStore_id();
		List<?> paramList = paramData.getData();
		if (null == paramList || paramList.isEmpty())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}

		if(customerService.checkIsAcewillCustomer(tenantId, storeId)==false)
		{
			throw SystemException.getInstance(PosErrorCode.CUSTOMER_NOT_ACEWILL_ERROR);
		}
		return alCustomerService.checkAcewillCustomerCard(paramData);
	}


	@Override
	public Data activatedAcewillCustomerCard(Data paramData) throws Exception {
		String tenantId = paramData.getTenancy_id();
		Integer storeId = paramData.getStore_id();
		List<?> paramList = paramData.getData();
		if (null == paramList || paramList.isEmpty())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}

		if(customerService.checkIsAcewillCustomer(tenantId, storeId)==false)
		{
			throw SystemException.getInstance(PosErrorCode.CUSTOMER_NOT_ACEWILL_ERROR);
		}
		JSONObject paramJson = JSONObject.fromObject(paramList.get(0));
		String  opt_num = paramJson.optString("opt_num");
		String  pos_num = paramJson.optString("pos_num");
		String  card_code = paramJson.optString("card_code");

		Data resutlData = alCustomerService.openAcewillCustomerCard(paramData);//开卡;
		if(Constant.CODE_SUCCESS == resutlData.getCode()){
			//打印小票
			String bizId = JSONObject.fromObject(resutlData.getData().get(0)).optString("biz_id");
			alCustomerService.printActivatedAcewillCustomerCard(tenantId, storeId, bizId, card_code, pos_num, opt_num);
		}
		return resutlData;
	}

	@Override
	public Data makeUpCustomerCardConsumePrint(Data paramData) throws Exception{
		String tenantId = paramData.getTenancy_id();
		Integer storeId = paramData.getStore_id();
		List<?> paramList = paramData.getData();
		if (null == paramList || paramList.isEmpty())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}

		if(customerService.checkIsAcewillCustomer(tenantId, storeId)==false)
		{
			throw SystemException.getInstance(PosErrorCode.CUSTOMER_NOT_ACEWILL_ERROR);
		}

		JSONObject paramJson = JSONObject.fromObject(paramList.get(0));

		JSONObject param = new JSONObject();
		String cno = paramJson.optString("card_code");
		if (Tools.hv(cno)) {
			Data customerUserInfo = this.queryAcewillCustomerInfo(paramData);
			if(Constant.CODE_SUCCESS == customerUserInfo.getCode()){
				JSONObject userInfo = JSONObject.fromObject(customerUserInfo.getData().get(0));
				param.put("useful_credit",userInfo.optInt("credit"));
				param.put("main_balance",userInfo.optDouble("balance"));
				param.put("level_name",userInfo.optString("level_name"));
				param.put("card_class_name",userInfo.optString("type_name"));
			}
		}


		param.put("bill_code", paramJson.optString("biz_id"));
		param.put("card_code", cno);//会员卡号
		param.put("name", paramJson.optString("name"));//会员名称
		param.put("mobil", paramJson.optString("mobil"));//会员手机号
		param.put("credit", paramJson.optInt("credit_award"));//赠送积分
		param.put("reward_balance", ""); //赠送账号余额
		param.put("total_balance", "");
		param.put("total_main", "");
		param.put("total_reward", "");
		param.put("consume_cardmoney",paramJson.optDouble("charge_total"));//储值消费
		param.put("operator", ""); //操作人
		param.put("updatetime", paramJson.optString("pay_time")); //交易时间
		param.put("main_trading", paramJson.optDouble("stored_sale_pay")); // 消费主账号金额
		param.put("reward_trading", "");//消费赠送账户金额
		param.put("payment_name1", paramJson.optString("pay_type_name"));//支付方式
		param.put("income", 0d);
		param.put("deposit", 0d);
		param.put("sales_price", 0d);

		Data resultData = paramData.clone();
		try{
			// 打印会员小票
			customerService.printForCustomer(tenantId, storeId, SysDictionary.PRINT_CODE_1010, param);
		}
		catch (Exception e)
		{
			e.printStackTrace();
			logger.error("微生活补打消费明细报错", e);
			resultData.setCode(Constant.CODE_INNER_EXCEPTION);
			resultData.setSuccess(false);
			resultData.setMsg("微生活补打消费明细报错");

		}
		return resultData;
	}


}
