package com.tzx.base.common.util;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import org.apache.log4j.Logger;

import com.tzx.base.listener.PosConsumerMessageStoreListener;
/**
 * 线程池工具类
 * <AUTHOR> email:she<PERSON><PERSON><PERSON>@tzx.com.cn
 * @version 1.0 2017-10-09
 * @see
 * @since JDK7.0
 * @update
 *
 */

public class ConsumerThreadPoolUtil {
	//定义消息消费者线程池
	private static volatile ThreadPoolExecutor consumerThreadPool = null;
	
	//获取线程池
	public static ThreadPoolExecutor getConsumerThreadPool() {
		if(consumerThreadPool == null){
			synchronized (ThreadPoolExecutor.class) {
				if(consumerThreadPool == null){
					/*
					 * 创建固定大小为5的线程池，使用ArrayBlockingQueue阻塞队列，队列大小为1千，线程数超过队列大小时的策略为重试。
					 */
					consumerThreadPool = new ThreadPoolExecutor(5,12,3,TimeUnit.SECONDS, new ArrayBlockingQueue<Runnable>(1000),
							new ThreadPoolExecutor.CallerRunsPolicy());
				}
			}
		}
		return consumerThreadPool;
	}
}
