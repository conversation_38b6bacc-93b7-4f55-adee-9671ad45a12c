package com.tzx.pos.bo.dto;

import java.io.Serializable;

/**
 * TODO
 * <AUTHOR> 
 * @日期：2015年5月18日-上午11:37:28
 */

public class BasicVersion implements Serializable
{
	/**
	 * 
	 */
	private static final long	serialVersionUID	= -8801667522116240766L;
	
	private String type;
	private boolean isNewest;
	private String version;
	
	public String getType()
	{
		return type;
	}
	public void setType(String type)
	{
		this.type = type;
	}
	public boolean isNewest()
	{
		return isNewest;
	}
	public void setNewest(boolean isNewest)
	{
		this.isNewest = isNewest;
	}
	public String getVersion()
	{
		return version;
	}
	public void setVersion(String version)
	{
		this.version = version;
	}
}
