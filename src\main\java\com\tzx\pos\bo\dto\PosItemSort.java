package com.tzx.pos.bo.dto;

import java.io.Serializable;

/**
 * 
 * <AUTHOR> 2015年8月26日-上午10:48:31
 */
public class PosItemSort implements Serializable
{
	/**
	 * 
	 */
	private static final long	serialVersionUID	= -444025537648380149L;

	private Integer				serial;
	private Integer				item_id;
	private String				item_num;
	private String				item_name;
	private String				click_count;
	private int					item_unit_id;
	private String				unit_name;
	private double				standard_price;
	private double				grade				= 5;					// 现在默认为5

	public String getItem_num()
	{
		return item_num;
	}

	public void setItem_num(String item_num)
	{
		this.item_num = item_num;
	}

	public String getClick_count()
	{
		return click_count;
	}

	public void setClick_count(String click_count)
	{
		this.click_count = click_count;
	}

	public double getGrade()
	{
		return this.grade;
	}

	public void setGrade(double grade)
	{
		this.grade = grade;
	}

	public Integer getSerial()
	{
		return serial;
	}

	public void setSerial(Integer serial)
	{
		this.serial = serial;
	}

	public Integer getItem_id()
	{
		return item_id;
	}

	public void setItem_id(Integer item_id)
	{
		this.item_id = item_id;
	}

	public String getItem_name()
	{
		return item_name;
	}

	public void setItem_name(String item_name)
	{
		this.item_name = item_name;
	}

	public Integer getItem_unit_id()
	{
		return item_unit_id;
	}

	public void setItem_unit_id(int item_unit_id)
	{
		this.item_unit_id = item_unit_id;
	}

	public String getUnit_name()
	{
		return unit_name;
	}

	public void setUnit_name(String unit_name)
	{
		this.unit_name = unit_name;
	}

	public double getStandard_price()
	{
		return standard_price;
	}

	public void setStandard_price(double standard_price)
	{
		this.standard_price = standard_price;
	}
}
