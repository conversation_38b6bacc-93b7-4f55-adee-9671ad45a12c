package com.tzx.pos.po.springjdbc.dao.imp;

import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import net.sf.json.JSONObject;

import org.apache.log4j.Logger;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Repository;

import com.tzx.framework.common.util.dao.datasource.MultiDataSourceManager;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.dao.imp.BaseDaoImp;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.po.springjdbc.dao.UploadDataDao;

@Repository(UploadDataDao.NAME)
public class UploadDataDaoImp extends BaseDaoImp implements UploadDataDao
{
	private Logger	logger	= Logger.getLogger(UploadDataDao.class);

	@Override
	public List<JSONObject> getPosUploadDataItem(String tenancyId, int storeId, String tableName, JSONObject param) throws Exception
	{
		if (null == tableName || "".equals(tableName))
		{
			logger.warn("表名不能为空!");
			return null;
		}

		Connection conn = this.jdbcTemplate.getDataSource().getConnection();
		DatabaseMetaData dbmd = conn.getMetaData();
		ResultSet rs = null;

		rs = dbmd.getColumns(null, null, tableName, null);

		StringBuilder column = new StringBuilder();
		while (rs.next())
		{
			String colname = rs.getString("COLUMN_NAME");
			if ("id".equalsIgnoreCase(colname))
			{
				column.append(",id as uid");
			}
			else if ("upload_tag".equalsIgnoreCase(colname))
			{
				column.append(",'1' as upload_tag");
			}
			else
			{
				column.append(",").append(colname);
			}
		}
		MultiDataSourceManager.close(conn, null, rs);

		if (column.length() == 0)
		{
			return null;
		}

		StringBuffer sqlSb = new StringBuffer("select ");

		sqlSb.append(column.substring(1)).append(" from ").append(tableName);
		sqlSb.append(" where tenancy_id='").append(tenancyId).append("' and store_id='").append(storeId).append("'");

		if (param != null && param.containsKey("upload_tag"))
		{
			sqlSb.append(" and upload_tag='").append(param.opt("upload_tag")).append("' ");
		}
		else if (param != null && param.containsKey("report_date"))
		{
			sqlSb.append(" and to_char(report_date,'yyyy-MM-dd')='").append(param.opt("report_date")).append("' ");
		}
		return query4Json(tenancyId, sqlSb.toString());
	}

	@Override
	public List<JSONObject> getPosAutoUploadDataItem(String tenancyId, int storeId, String tableName, JSONObject param, String isUploadOpenBill) throws Exception
	{
		if (null == tableName || "".equals(tableName))
		{
			logger.warn("表名不能为空!");
			return null;
		}

		Connection conn = this.jdbcTemplate.getDataSource().getConnection();
		DatabaseMetaData dbmd = conn.getMetaData();
		ResultSet rs = null;
		rs = dbmd.getColumns(null, null, tableName, null);

		StringBuilder column = new StringBuilder();
		while (rs.next())
		{
			String colname = rs.getString("COLUMN_NAME");
			if ("id".equalsIgnoreCase(colname))
			{
				column.append(",a.id as uid");
			}
			else if ("upload_tag".equalsIgnoreCase(colname))
			{
				column.append(",'1' as upload_tag");
			}
			else
			{
				column.append(",a.").append(colname);
			}
		}
		MultiDataSourceManager.close(conn, null, rs);

		if (column.length() == 0)
		{
			return null;
		}

		StringBuffer sqlSb = new StringBuffer("select ");
		sqlSb.append(column.substring(1)).append(" from ").append(tableName).append(" as a");

		switch (tableName.toLowerCase())
		{
			case "pos_bill":
				sqlSb.append(" where a.tenancy_id='").append(tenancyId).append("' and a.store_id='").append(storeId).append("'").append(" and a.upload_tag='0'");

				if (param != null && param.containsKey("report_date"))
				{
					sqlSb.append(" and to_char(a.report_date,'yyyy-MM-dd')='").append(param.opt("report_date")).append("' ");
				}
				if ("0".equals(isUploadOpenBill))
				{
					sqlSb.append(" and a.bill_property='").append(SysDictionary.BILL_PROPERTY_CLOSED).append("'");
				}
				break;
			case "pos_bill_item":
			case "pos_bill_payment":
				sqlSb.append(" left join pos_bill b on a.tenancy_id=b.tenancy_id and a.store_id=b.store_id and a.bill_num=b.bill_num");
				sqlSb.append(" where b.tenancy_id='").append(tenancyId).append("' and b.store_id='").append(storeId).append("'").append(" and b.upload_tag='0'");

				if (param != null && param.containsKey("report_date"))
				{
					sqlSb.append(" and to_char(b.report_date,'yyyy-MM-dd')='").append(param.opt("report_date")).append("' ");
				}
				if ("0".equals(isUploadOpenBill))
				{
					sqlSb.append(" and b.bill_property='").append(SysDictionary.BILL_PROPERTY_CLOSED).append("'");
				}
				break;
			case "pos_zfkw_item":
			case "pos_bill_invoice":
			case "pos_bill_waiter":
			case "pos_returngive_item":
			case "pos_bill_member":
			case "pos_table_log":
				sqlSb.append(" where a.tenancy_id='").append(tenancyId).append("' and a.store_id='").append(storeId).append("'").append(" and a.upload_tag='0'");

				if (param != null && param.containsKey("report_date"))
				{
					sqlSb.append(" and to_char(a.report_date,'yyyy-MM-dd')='").append(param.opt("report_date")).append("' ");
				}
				break;
			case "pos_kvs_bill_item":
			case "pos_kvs_bill":
			case "pos_bill_service":
			case "pos_bill_payment_coupons":
			case "hq_bill_evaluate":
			case "pos_item_discount_list":
				sqlSb.append(" left join (select tenancy_id,store_id,report_date,bill_num,bill_property,upload_tag from pos_bill union all select tenancy_id,store_id,report_date,bill_num,bill_property,upload_tag from pos_bill2) b");
				sqlSb.append(" on a.tenancy_id=b.tenancy_id and a.store_id=b.store_id and a.bill_num=b.bill_num");
				sqlSb.append(" where b.tenancy_id='").append(tenancyId).append("' and b.store_id='").append(storeId).append("'").append(" and b.upload_tag='0'");

				if (param != null && param.containsKey("report_date"))
				{
					sqlSb.append(" and to_char(b.report_date,'yyyy-MM-dd')='").append(param.opt("report_date")).append("' ");
				}
				if ("0".equals(isUploadOpenBill))
				{
					sqlSb.append(" and b.bill_property='").append(SysDictionary.BILL_PROPERTY_CLOSED).append("'");
				}
				break;
			default:
				sqlSb.append(" where a.tenancy_id='").append(tenancyId).append("' and a.store_id='").append(storeId).append("'").append(" and a.upload_tag='0'");

				if (param != null && param.containsKey("report_date"))
				{
					sqlSb.append(" and to_char(a.report_date,'yyyy-MM-dd')='").append(param.opt("report_date")).append("' ");
				}
				break;
		}

		return query4Json(tenancyId, sqlSb.toString());
	}

//	@Override
//	public int[] updatePosItemUploadTag(String tenancyId, int storeId, String tableName, List<JSONObject> param, String cond) throws Exception
//	{
//		if (null == tableName || "".equals(tableName) || null == param || param.size() <= 0)
//		{
//			return null;
//		}
//		StringBuffer sql = new StringBuffer();
//		sql.append("update ").append(tableName).append(" set upload_tag='1' where id=? and tenancy_id=? and store_id=?");
//
//		if (null != cond && !"".equals(cond))
//		{
//			sql.append(" ").append(cond);
//		}
//
//		List<Object[]> batchArgs = new ArrayList<Object[]>();
//		for (JSONObject paraJson : param)
//		{
//			batchArgs.add(new Object[]
//			{ paraJson.optInt("uid"), tenancyId, storeId });
//		}
//
//		return this.jdbcTemplate.batchUpdate(sql.toString(), batchArgs);
//	}
	
	@Override
	public int[] updatePosItemUploadTag(String tenancyId, int storeId, String tableName, List<JSONObject> param, String cond) throws Exception
	{
		if (null == tableName || "".equals(tableName) || null == param || param.size() <= 0){
			return null;
		}
		String sql = "";
		int rowNum = 0;
		int size = param.size();
		double sum = size;
		int pageSize = 1000;
		int  batch = (int)Math.ceil(sum/pageSize);
		for(int i=0;i<batch;i++){
			int val = pageSize;
			if(i == batch-1){
				val = size-i*pageSize;
			}
			StringBuilder sb = new StringBuilder();
			for(int j=0;j<val;j++){
				int xh = i*pageSize +j;
				if(j==val-1){
					sb.append("'").append(param.get(xh).opt("uid").toString().trim()).append("'");
				}else{
					sb.append("'").append(param.get(xh).opt("uid").toString().trim()).append("',");
				}
			}
			sql = "update "+tableName+" set upload_tag = '1' where id in ("+sb.toString()+")";
			rowNum = this.jdbcTemplate.update(sql);
		}
		return null;
	}

	@Override
	public boolean updatePosUploadDate(String tenancyId, int storeId) throws Exception
	{
		String paraValue = DateUtil.format(new Timestamp(System.currentTimeMillis()));

		String sql = "select id,para_remark from pos_data_version where para_code='AUTOUPLOADDATETIME' and tenancy_id=? and store_id=?";

		SqlRowSet rs = jdbcTemplate.queryForRowSet(sql, new Object[]
		{ tenancyId, storeId });

		if (rs.next())
		{
			String updateSql = "update pos_data_version set para_value=?,para_remark=? where para_code='AUTOUPLOADDATETIME' and tenancy_id=? and store_id=?";
			int ret = jdbcTemplate.update(updateSql, new Object[]
			{ paraValue, rs.getInt("para_remark") + 1, tenancyId, storeId });

			return ret > 0;
		}
		else
		{
			String insertSql = "insert into pos_data_version(tenancy_id,store_id,system_name,model_name,para_name,para_code,para_value,para_type,valid_state,para_remark) values(?,?,'POS','系统参数','自动上传时间','AUTOUPLOADDATETIME',?,'常规','1',1)";

			int ret = jdbcTemplate.update(insertSql, new Object[]
			{ tenancyId, storeId, paraValue });

			return ret > 0;
		}
	}

	@Override
	public Integer getPosBillDataForCountByReportDate(String tenancyId, int storeId, Date reportDate, String isOpenBill) throws Exception
	{
		// TODO Auto-generated method stub
		StringBuffer sql = new StringBuffer("select count(*) as count_num from v_pos_bill bl where bl.tenancy_id=? and bl.store_id=? and bl.report_date=?");
        sql.append(" and coalesce(bl.upload_tag,'0')<>'1' ");

		if ("1".equals(isOpenBill) == false)
		{
			sql.append(" and bl.bill_property='").append(SysDictionary.BILL_PROPERTY_CLOSED).append("'");
		}
		return this.queryForInt(sql.toString(), new Object[]
		{ tenancyId, storeId, reportDate });
	}

	@Override
	public List<JSONObject> getPosBillDataByReportDate(String tenancyId, int storeId, Date reportDate, String isOpenBill, int limitSize) throws Exception
	{
		String column = this.getTableColumnNameForStr(tenancyId, storeId, "pos_bill");

		if (column.length() == 0)
		{
			return null;
		}

		StringBuffer sqlSb = new StringBuffer("select ");
		sqlSb.append(column.substring(1)).append(" from pos_bill bl");
		sqlSb.append(" where bl.tenancy_id='"+tenancyId+"' and bl.store_id="+storeId+" and bl.report_date='"+DateUtil.formatDate(reportDate)+"'");
        sqlSb.append("  and coalesce(bl.upload_tag,'0')<>'1'");

        if ("1".equals(isOpenBill) == false)
		{
			sqlSb.append(" and bl.bill_property='").append(SysDictionary.BILL_PROPERTY_CLOSED).append("'");
		}

		if (0 < limitSize)
		{
			sqlSb.append(" order by bl.id limit ").append(limitSize);
		}

		return query4Json(tenancyId, sqlSb.toString());
	}

	@Override
	public List<JSONObject> getPosBill2DataByReportDate(String tenancyId, int storeId, Date reportDate, String isOpenBill,int limitSize) throws Exception
	{
		logger.info("进入getPosBill2DataByReportDate方法");
		String column = this.getTableColumnNameForStr(tenancyId, storeId, "pos_bill", "pos_bill2");
		logger.info("获取值column："+column);

		if (column.length() == 0)
		{
			return null;
		}

		StringBuffer sqlSb = new StringBuffer("select ");
		sqlSb.append(column.substring(1)).append(" from pos_bill2 bl");
		sqlSb.append(" where bl.tenancy_id='"+tenancyId+"' and bl.store_id="+storeId+" and bl.report_date='"+DateUtil.formatDate(reportDate)+"'");
        sqlSb.append("  and coalesce(bl.upload_tag,'0')<>'1'");
		if ("1".equals(isOpenBill) == false)
		{
			sqlSb.append(" and bl.bill_property='").append(SysDictionary.BILL_PROPERTY_CLOSED).append("'");
		}

		if (0 < limitSize)
		{
			sqlSb.append(" order by bl.id limit ").append(limitSize);
		}

		logger.info("执行SQL语句"+sqlSb.toString());
		return query4Json(tenancyId, sqlSb.toString());
	}

	@Override
	public List<JSONObject> getPosBillLinkDataByBillList(String tenancyId, int storeId, String tableName, List<JSONObject> billList,Date reportDate) throws Exception
	{
		if (null == tableName || "".equals(tableName))
		{
			logger.warn("表名不能为空!");
			return null;
		}

		if (null == billList || billList.size() == 0)
		{
			logger.warn("账单数据为空!");
			return null;
		}

		String column = this.getTableColumnNameForStr(tenancyId, storeId, tableName);
		logger.info("column的值："+column);

		if (column.length() == 0)
		{
			return null;
		}

		StringBuffer sqlSb = new StringBuffer("select ");

		sqlSb.append(column.substring(1)).append(" from ").append(tableName);
		sqlSb.append(" where tenancy_id='").append(tenancyId).append("' and store_id='").append(storeId).append("'");

//		if(isIncrement){
            StringBuffer billNumCond = new StringBuffer();
            for (JSONObject billJson : billList)
            {
                if (billNumCond.length() > 0)
                {
                    billNumCond.append(",");
                }
                billNumCond.append("'").append(billJson.optString("bill_num")).append("'");
            }
            if (billNumCond.length() > 0)
            {
                sqlSb.append(" and bill_num in (").append(billNumCond).append(")");
            }
//        }else {
//		   sqlSb.append(" and report_date='"+reportDate.toString()+"'");
//        }
        logger.info("sql语句："+sqlSb);
        return query4Json(tenancyId, sqlSb.toString());
	}

	@Override
	public List<JSONObject> getPosBillLinkDataByBillList(String tenancyId, int storeId, String tableName, String tableName2, List<JSONObject> billList,Date reportDate) throws Exception
	{
		if (null == tableName || "".equals(tableName))
		{
			logger.warn("表名不能为空!");
			return null;
		}

		if (null == billList || billList.size() == 0)
		{
			logger.warn("账单数据为空!");
			return null;
		}

		String column = this.getTableColumnNameForStr(tenancyId, storeId, tableName, tableName2);
		logger.info("column的值："+column);

		if (column.length() == 0)
		{
			return null;
		}

		StringBuffer sqlSb = new StringBuffer("select ");

		sqlSb.append(column.substring(1)).append(" from ").append(tableName2);
		sqlSb.append(" where tenancy_id='").append(tenancyId).append("' and store_id='").append(storeId).append("'");

//        if (isIncrement) {
            StringBuffer billNumCond = new StringBuffer();
            for (JSONObject billJson : billList) {
                if (billNumCond.length() > 0) {
                    billNumCond.append(",");
                }
                billNumCond.append("'").append(billJson.optString("bill_num")).append("'");
            }
            if (billNumCond.length() > 0) {
                sqlSb.append(" and bill_num in (").append(billNumCond).append(")");
            }
//        } else {
//            sqlSb.append(" and report_date='" + reportDate.toString() + "'");
//        }
        logger.info("sql语句："+sqlSb);
        return query4Json(tenancyId, sqlSb.toString());
	}

	private String getTableColumnNameForStr(String tenancyId, int storeId, String tableName) throws Exception
	{
		Connection conn = this.jdbcTemplate.getDataSource().getConnection();
		DatabaseMetaData dbmd = conn.getMetaData();
		ResultSet rs = dbmd.getColumns(null, null, tableName, null);

		StringBuilder column = new StringBuilder();
		while (rs.next())
		{
			String colname = rs.getString("COLUMN_NAME");
			if ("id".equalsIgnoreCase(colname))
			{
				column.append(",id as uid");
			}
			else if ("upload_tag".equalsIgnoreCase(colname))
			{
				column.append(",'1' as upload_tag");
			}
			else
			{
				column.append(",").append(colname);
			}
		}

		MultiDataSourceManager.close(conn, null, rs);
		return column.toString();
	}

	private String getTableColumnNameForStr(String tenancyId, int storeId, String tableName, String tableName2) throws Exception
	{
		Connection conn = this.jdbcTemplate.getDataSource().getConnection();
		DatabaseMetaData dbmd = conn.getMetaData();
		ResultSet rs = dbmd.getColumns(null, null, tableName2, null);
		List<String> columnList = new ArrayList<String>();
		while (rs.next())
		{
			columnList.add(rs.getString("COLUMN_NAME"));
		}

		rs = dbmd.getColumns(null, null, tableName, null);
		StringBuilder column = new StringBuilder();
		while (rs.next())
		{
			String colname = rs.getString("COLUMN_NAME");
			if ("id".equalsIgnoreCase(colname))
			{
				column.append(",id as uid");
			}
			else if ("upload_tag".equalsIgnoreCase(colname))
			{
				column.append(",'1' as upload_tag");
			}
			else
			{
				if (columnList.contains(colname))
				{
					column.append(",").append(colname);
				}
				else
				{
					column.append(",null as ").append(colname);
				}
			}
		}

		MultiDataSourceManager.close(conn, null, rs);
		return column.toString();
	}

	@Override
	public List<JSONObject> getPosUploadDataByReportDate(String tenancyId, int storeId, Date reportDate, String tableName) throws Exception
	{
		String column = this.getTableColumnNameForStr(tenancyId, storeId, tableName);

		if (column.length() == 0)
		{
			return null;
		}

		StringBuffer sqlSb = new StringBuffer("select ");
		sqlSb.append(column.substring(1)).append(" from ").append(tableName).append(" bl");
		sqlSb.append(" where bl.tenancy_id=? and bl.store_id=? and bl.report_date=? and coalesce(bl.upload_tag,'0')<>'1'");

		return query4Json(tenancyId, sqlSb.toString(), new Object[]
		{ tenancyId, storeId, reportDate });
	}

    @Override
    public boolean resetUploadTagByReportDate(String tenancyId, int storeId, Date reportDate, String[] tableNames) throws Exception {
//	    StringBuilder sql=new StringBuilder();
	    String sql = "";
	    boolean bo = false;
	    for(String table:tableNames){
//            sql.append("update "+table+" set upload_tag='0' where tenancy_id='"+tenancyId+"' and store_id='"+storeId+"' and report_date='"+reportDate+"';");
	    	sql = "update "+table+" set upload_tag='0' where tenancy_id='"+tenancyId+"' and store_id='"+storeId+"' and report_date='"+DateUtil.formatDate(reportDate)+"'";
//	        logger.info("执行"+sql);
	    	bo = this.execute(tenancyId,sql);
	    	if(bo==true) logger.info("执行"+sql+"成功。");
        }
	    return true;
//       return this.execute(tenancyId,sql.toString());
    }

	@Override
	public boolean setUploadTagByReportDate(String tenancyId, int storeId, Date reportDate, String tableName)
			throws Exception {
		StringBuilder sql=new StringBuilder();
		sql.append("update "+tableName+" set upload_tag='1' where tenancy_id='"+tenancyId+"' and store_id='"+storeId+"' and report_date='"+DateUtil.formatDate(reportDate)+"';");
//		logger.info("setUploadTagByReportDate:"+sql.toString());
		return this.execute(tenancyId,sql.toString());
	}

}
