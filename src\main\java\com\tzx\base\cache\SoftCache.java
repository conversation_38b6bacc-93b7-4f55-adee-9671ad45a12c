package com.tzx.base.cache;

import java.lang.ref.SoftReference;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * <AUTHOR> on 2020-12-10
 */
public class SoftCache {
    private static ConcurrentMap map = new ConcurrentHashMap();

    public static Object get(Object key) {
        SoftReference softRef = (SoftReference) map.get(key);

        if (softRef == null)
            return null;

        return softRef.get();
    }

    public static Object put(Object key, Object value) {
        SoftReference softRef = (SoftReference) map.put(key, new SoftReference(value));

        if (softRef == null)
            return null;

        Object oldValue = softRef.get();
        softRef.clear();

        return oldValue;
    }

    public static Object remove(Object key) {
        SoftReference softRef = (SoftReference) map.remove(key);

        if (softRef == null)
            return null;

        Object oldValue = softRef.get();
        softRef.clear();

        return oldValue;
    }

}
