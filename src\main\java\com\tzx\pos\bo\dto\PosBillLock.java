package com.tzx.pos.bo.dto;

import java.io.Serializable;
import java.util.Date;

public class PosBillLock implements Serializable {
    private  String tenancy_id;
    private Integer store_id;
    private String id;
    private String bill_num;
    private String lock_type;
    private Integer lock_state;
    private String open_id;
    private Integer customer_id;
    private Date lock_time;
    private Date unlock_time;
    private Date unlock_type;
    private Integer bill_state;

    public String getTenancy_id() {
        return tenancy_id;
    }

    public void setTenancy_id(String tenancy_id) {
        this.tenancy_id = tenancy_id;
    }

    public Integer getStore_id() {
        return store_id;
    }

    public void setStore_id(Integer store_id) {
        this.store_id = store_id;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getBill_num() {
        return bill_num;
    }

    public void setBill_num(String bill_num) {
        this.bill_num = bill_num;
    }

    public String getLock_type() {
        return lock_type;
    }

    public void setLock_type(String lock_type) {
        this.lock_type = lock_type;
    }

    public Integer getLock_state() {
        return lock_state;
    }

    public void setLock_state(Integer lock_state) {
        this.lock_state = lock_state;
    }

    public String getOpen_id() {
        return open_id;
    }

    public void setOpen_id(String open_id) {
        this.open_id = open_id;
    }

    public Integer getCustomer_id() {
        return customer_id;
    }

    public void setCustomer_id(Integer customer_id) {
        this.customer_id = customer_id;
    }

    public Date getLock_time() {
        return lock_time;
    }

    public void setLock_time(Date lock_time) {
        this.lock_time = lock_time;
    }

    public Date getUnlock_time() {
        return unlock_time;
    }

    public void setUnlock_time(Date unlock_time) {
        this.unlock_time = unlock_time;
    }

    public Date getUnlock_type() {
        return unlock_type;
    }

    public void setUnlock_type(Date unlock_type) {
        this.unlock_type = unlock_type;
    }

    public Integer getBill_state() {
        return bill_state;
    }

    public void setBill_state(Integer bill_state) {
        this.bill_state = bill_state;
    }
}
