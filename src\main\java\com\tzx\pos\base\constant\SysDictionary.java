package com.tzx.pos.base.constant;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SysDictionary
{
	/** 员工操作状态:登录 */
	public static final String				OPT_STATE_YYDL						= "YYDL";
	/** 员工操作状态:签到 */
	public static final String				OPT_STATE_KSSY						= "KSSY";
	/** 员工操作状态:交班 */
	public static final String				OPT_STATE_YYTC						= "YYTC";
	/** 员工操作状态:日始 */
	public static final String				OPT_STATE_DAYBEGAIN					= "DAYBEGAIN";
	/** 员工操作状态:打烊 */
	public static final String				OPT_STATE_DAYEND					= "DAYEND";

	/** 账单属性:未结账 */
	public static final String				BILL_PROPERTY_OPEN					= "OPEN";
	/** 账单属性:已结账 */
	public static final String				BILL_PROPERTY_CLOSED				= "CLOSED";
	/** 账单属性:卧单 */
	public static final String				BILL_PROPERTY_SORT					= "SORT";

	/** 销售模式: 堂食 */
	public static final String				SALE_MODE_TS01						= "TS01";
	/** 销售模式: 外带 */
	public static final String				SALE_MODE_WD02						= "WD02";
	/** 销售模式: 外送 */
	public static final String				SALE_MODE_WS03						= "WS03";

	/** 账单状态: 冲减 */
	public static final String				BILL_STATE_CJ01						= "CJ01";
	/** 账单状态: 部分退 */
	public static final String				BILL_STATE_BFT04					= "BFT04";
	/** 账单状态: 部分退冲减 */
	public static final String				BILL_STATE_BFTCJ01					= "BFTCJ01";
	/** 账单状态: 整单取消 */
	public static final String				BILL_STATE_ZDQX02					= "ZDQX02";
	/** 账单状态: 单品取消 */
	public static final String				BILL_STATE_DPQX03					= "DPQX03";

	/** 卧单备注: 单品 */
	public static final String				ITEM_PROPERTY_SINGLE				= "SINGLE";
	/** 卧单备注: 套餐 */
	public static final String				ITEM_PROPERTY_SETMEAL				= "SETMEAL";
	/** 卧单备注: 明细 */
	public static final String				ITEM_PROPERTY_MEALLIST				= "MEALLIST";

	/** 卧单备注:退菜 */
	public static final String				ITEM_REMARK_TC01					= "TC01";
	/** 卧单备注:奉送 */
	public static final String				ITEM_REMARK_FS02					= "FS02";
	/** 卧单备注:免单 */
	public static final String				ITEM_REMARK_MD03					= "MD03";
	/** 卧单备注:取消 */
	public static final String				ITEM_REMARK_QX04					= "QX04";
	/** 卧单备注:冲减 */
	public static final String				ITEM_REMARK_CJ05					= "CJ05";
	/** 卧单备注:活动 */
	public static final String				ITEM_REMARK_HD07					= "HD07";
	/** 卧单备注:活动赠菜 */
	public static final String				ITEM_REMARK_HDZC08					= "HDZC08";
	/** 卧单备注:活动 第二杯半价 */
	public static final String				ITEM_REMARK_HDBJ09					= "HDBJ09";
	/** 卧单备注:活动 折后统一价 */
	public static final String				ITEM_REMARK_HDTYJ10					= "HDTYJ10";

	/** 支付类型 :免单 */
	public static final String				PAYMENT_CLASS_FREESINGLE			= "freesingle";
	/** 支付类型 :微信支付 */
	public static final String				PAYMENT_CLASS_WECHAT_PAY			= "wechat_pay";
	/** 支付类型 :支付宝支付 */
	public static final String				PAYMENT_CLASS_ALI_PAY				= "ali_pay";
    /** 支付类型 :云闪付 */
    public static final String				PAYMENT_DDM_QUICK_PAY				= "ddm_quick_pay";
	/** 支付类型 :现金 */
	public static final String				PAYMENT_CLASS_CASH					= "cash";
	/** 支付类型 :银行卡 */
	public static final String				PAYMENT_CLASS_BANKCARD				= "bankcard";
	/** 支付类型 :本系统卡 */
	public static final String				PAYMENT_CLASS_CARD					= "card";
	/** 支付类型 :优惠券 */
	public static final String				PAYMENT_CLASS_COUPONS				= "coupons";
	/** 支付类型 :团体挂账 */
	public static final String				PAYMENT_CLASS_INCORPORATION			= "incorporation";
	
	/** 支付类型 :百度付款 */
	public static final String				PAYMENT_CLASS_BAIDU_PAY				= "baidu_pay";
	/** 支付类型 :丰食外卖付款 */
	public static final String				PAYMENT_CLASS_FENGSHI_PAY			= "fengshi_pay";

	/** 支付类型 : 京东秒送外卖付款 */
	public static final String              PAYMENT_CLASS_JINGDONG_PAY          = "jingdong_pay";

	/** 支付类型 :其他 */
	public static final String				PAYMENT_CLASS_OTHER					= "other";
	/** 支付类型 :会员积分 */
	public static final String				PAYMENT_CLASS_CARD_CREDIT			= "card_credit";
	/** 支付类型 :京东钱包 */
	public static final String				PAYMENT_CLASS_JD_PAY				= "jd_pay";
	/** 支付类型 :美团劵 */
	public static final String				PAYMENT_CLASS_MEIDA_COUPONS_PAY		= "meida_coupons_pay_md";
    /** 支付类型 :抖音劵 */
    public static final String				PAYMENT_CLASS_DOUYIN_COUPONS_PAY	= "douyin_coupons";
    /** 支付类型 :抖音劵虚收*/
    public static final String				PAYMENT_CLASS_DOUYIN_COUPONS_PHANTOM_PAY	= "douyin_coupons_phantom";
	/** 支付类型 :闪惠 */
	public static final String				PAYMENT_CLASS_FLASH_PAY				= "shanhui_pay";
	/** 支付类型 :美大会员卡支付 */
	public static final String				PAYMENT_CLASS_MEIDA_CARD_PAY		= "meidahuiyuan_pay";
	/** 支付类型：秒付 **/
	public static final String				PAYMENT_CLASS_SECONDS_PAY			= "second_pay";
	/** 支付类型：秒付积分支付 **/
	public static final String				PAYMENT_CLASS_SECONDS_PAY_CREDIT	= "second_pay_credit";
	/** 签账 **/
	public static final String				PAYMENT_CLASS_DEBIT_PAY				= "debit_card";
	/** 微信/支付宝二码合一 **/
	public static final String				PAYMENT_CLASS_SYNCRETIC_PAY			= "syncretic_pay";
	/** 微信/支付宝扫码合一 **/
	public static final String				PAYMENT_CLASS_SCAN_CODE_PAY			= "scan_code_pay";
	public static final String				PAYMENT_CLASS_SCAN_CODE_PAY_NAME	= "扫码支付";

	/** 微生活会员储值 **/
	public static final String				PAYMENT_CLASS_WLIFE_BALANCE			= "wlife_balance";
	/** 微生活会员积分 **/
	public static final String				PAYMENT_CLASS_WLIFE_CREDIT			= "wlife_credit";
	/** 微生活会员优惠券 **/
	public static final String				PAYMENT_CLASS_WLIFE_COUPON			= "wlife_coupon";
	/** 微生活微信 **/
	public static final String				PAYMENT_CLASS_WLIFE_WECHAT_PAY		= "wechat_pay_wlife";
	/** 微生活支付宝 **/
	public static final String				PAYMENT_CLASS_WLIFE_ALI_PAY			= "ali_pay_wlife";


	public static final String PAYMENT_CLASS_WLIFE_MEITUAN = "wlife_meituan";
	public static final String PAYMENT_CLASS_WLIFE_PHANTOM_MEITUAN = "wlife_meituan_phantom";
	public static final String PAYMENT_CLASS_WLIFE_DOUYIN = "wlife_douyin";
	public static final String PAYMENT_CLASS_WLIFE_PHANTOM_DOUYIN = "wlife_douyin_phantom";
	public static final String PAYMENT_CLASS_WLIFE_ALIPAYCOUPON = "wlife_alipaycoupon";
	public static final String PAYMENT_CLASS_WLIFE_PHANTOM_ALIPAYCOUPON = "wlife_alipaycoupon_phantom";
	public static final String PAYMENT_CLASS_WLIFE_LITTLE_RED_BOOK = "wlife_little_red_book";
	public static final String PAYMENT_CLASS_WLIFE_PHANTOM_LITTLE_RED_BOOK = "wlife_little_red_book_phantom";

	public static final String				PAYMENT_CHANGE_NAME					= "找零";

	public static final String				PAYMENT_CHANGE_ENGLISH_NAME			= "change";

	/** 付款状态:未付款 */
	public static final String				PAYMENT_STATE_NOTPAY				= "03";
	/** 付款状态:付款中 */
	public static final String				PAYMENT_STATE_PAY					= "02";
	/** 付款状态:付款完成 */
	public static final String				PAYMENT_STATE_PAY_COMPLETE			= "01";
	/** 付款状态:付款失败 */
	public static final String				PAYMENT_STATE_PAY_FAILURE			= "06";
	/** 付款状态:秒付付款 */
	public static final String				PAYMENT_STATE_PAY_SECONDS			= "05";
	/** 付款状态:暂存 */
	public static final String				PAYMENT_STATE_PAY_PRESERVE			= "08";

	/** 打印机状态正常 */
	public static final String				PRINT_STATE_OK						= "正常";
	/** 打印机正在重启 */
	public static final String				PRINT_STATE_RESTART					= "正在重启";
	/** 打印机异常 */
	public static final String				PRINT_STATE_EXCEPTION				= "打印机异常";
	/** 打印机服务异常 */
	public static final String				PRINT_SERVICE_UNNSUAL				= "服务异常";
	/** 打印机状态异常 */
	public static final String				PRINT_STATE_UNUSUAL					= "状态异常";

	/** 付款状态:退款中 */
	public static final String				PAYMENT_STATE_REFUNDING				= "11";
	/** 付款状态:退款完成 */
	public static final String				PAYMENT_STATE_REFUND_COMPLETE		= "12";
	/** 付款状态:退款失败 */
	public static final String				PAYMENT_STATE_REFUND_FAILURE		= "13";
	/** 付款状态:取消付款 */
	public static final String				PAYMENT_STATE_CANCEL				= "21";

	public static final Map<String, String>	PAYMENT_STATE_TEXT_MAP				= new HashMap<String, String>()
																				{
																					private static final long serialVersionUID = 1L;

																					{
																						put(PAYMENT_STATE_NOTPAY, "未付款");
																						put(PAYMENT_STATE_PAY, "付款中");
																						put(PAYMENT_STATE_PAY_PRESERVE, "付款中");
																						put(PAYMENT_STATE_PAY_COMPLETE, "付款完成");
																						put(PAYMENT_STATE_PAY_FAILURE, "付款失败");
																						put(PAYMENT_STATE_REFUNDING, "退款中");
																						put(PAYMENT_STATE_REFUND_COMPLETE, "退款完成");
																						put(PAYMENT_STATE_REFUND_FAILURE, "退款失败");
																					}
																				};

	/** 做法口味类型:做法 */
	public static final String				ZFKW_TYPE_METHOD					= "METHOD";
	/** 做法口味类型:口味 */
	public static final String				ZFKW_TYPE_TASTE						= "TASTE";

	/** 桌位状态 :空闲 */
	public static final String				TABLE_STATE_FREE					= "FREE";
	/** 桌位状态 :占用 */
	public static final String				TABLE_STATE_BUSY					= "BUSY";
	/** 桌位状态 :清扫 */
	public static final String				TABLE_STATE_CLEAN					= "CLEAN";
	/** 桌位状态 :维修 */
	public static final String				TABLE_STATE_SERVICE					= "SERVICE";

	/** 会员卡 :发卡 */
	public static final String				OPERAT_TYPE_FK						= "01";
	/** 会员卡 :充值 */
	public static final String				OPERAT_TYPE_CZ						= "02";
	/** 会员卡 :消费 */
	public static final String				OPERAT_TYPE_XF						= "03";
	/** 会员卡 :反充值 */
	public static final String				OPERAT_TYPE_FCZ						= "04";
	/** 会员卡 :反消费 */
	public static final String				OPERAT_TYPE_FXF						= "05";
	/** 会员卡 :补卡 */
	public static final String				OPERAT_TYPE_BK						= "06";
	/** 会员卡 :并卡转入 */
	public static final String				OPERAT_TYPE_BKZR					= "08";
	/** 会员卡 :并卡转出 */
	public static final String				OPERAT_TYPE_BKZC					= "09";
	/** 会员卡 :退卡 */
	public static final String				OPERAT_TYPE_TK						= "12";
	/** 会员卡 :购买会籍 */
	public static final String				OPERAT_TYPE_GMHJ					= "13";
	/** 会员积分消费 */
	public static final String				OPERAT_TYPE_JFXF					= "14";
	/** 撤销会员积分消费 */
	public static final String				OPERAT_TYPE_CXXF					= "15";
	/** 会员积分增加 */
	public static final String				OPERAT_TYPE_JFZJ					= "16";
	/** 撤销会员积分增加 */
	public static final String				OPERAT_TYPE_CXJFZJ					= "17";
	/** 团体挂账 */
	public static final String				OPERAT_TYPE_INCORPORATION			= "101";
	/** 撤销团体挂账 */
	public static final String				OPERAT_TYPE_INCORPORATION_CANCEL	= "102";
	/** 优惠劵 */
	public static final String				OPERAT_TYPE_COUPON_consume			= "103";
	/** 撤销优惠劵 */
	public static final String				OPERAT_TYPE_COUPON__CANCEL			= "104";

	/** 账单会员：积分 */
	@Deprecated
	public static final String				BILL_MEMBERCARD_JF01				= "JF01";
	/** 账单会员：折扣 */
	@Deprecated
	public static final String				BILL_MEMBERCARD_ZK02				= "ZK02";
	/** 账单会员：结账 */
	@Deprecated
	public static final String				BILL_MEMBERCARD_JZ03				= "JZ03";

	/** 账单会员：会员价 */
	public static final String				BILL_MEMBERCARD_HYJ01				= "HYJ04";
	/** 账单会员：会员折扣 */
	public static final String				BILL_MEMBERCARD_HYZK02				= "ZK02";
	/** 账单会员：储值消费 */
	public static final String				BILL_MEMBERCARD_CZXF03				= "JZ03";
	/** 账单会员：优惠劵消费 */
	public static final String				BILL_MEMBERCARD_YHJ04				= "YHJ05";
	/** 账单会员：积分抵现 */
	public static final String				BILL_MEMBERCARD_JFDX05				= "JFDX06";
	/** 账单会员：消费赠积分 */
	public static final String				BILL_MEMBERCARD_JFZS06				= "JF01";

	/** 账单会员备注标记：会员卡结账 */
	@Deprecated
	public static final String				BILL_MEMBER_REMARK_CARD				= "HY01";
	/** 账单会员备注标记：积分抵现结账 */
	@Deprecated
	public static final String				BILL_MEMBER_REMARK_CREDIT			= "JF02";

	/** 打印单套餐显示方式：全部 */
	public static final String				COMBOPRINT_TYPE_ALL					= "all_print";
	/** 打印单套餐显示方式：只打印主项 */
	public static final String				COMBOPRINT_TYPE_SETMEAL				= "onlysetmeal_print";
	/** 打印单套餐显示方式：只打印明细 */
	public static final String				COMBOPRINT_TYPE_MEALLIST			= "onlydetaill_print";

	/* 打印编码规则:会员相关为:10** */
	/** 打印模板编号：会员卡充值 */
	public static final String				PRINT_CODE_1008						= "1008";
	/** 打印模板编号：会员卡反充值 */
	public static final String				PRINT_CODE_1009						= "1009";
	/** 打印模板编号：会员卡消费 */
	public static final String				PRINT_CODE_1010						= "1010";
	/** 打印模板编号：会员卡反消费 */
	public static final String				PRINT_CODE_1011						= "1011";
	/** 打印模板编号：会员卡退卡 */
	public static final String				PRINT_CODE_1012						= "1012";
	/** 打印模板编号：会员卡发卡 */
	public static final String				PRINT_CODE_1013						= "1013";
	/** 打印模板编号：会员卡补卡 */
	public static final String				PRINT_CODE_1015						= "1015";
	/** 打印模板编号：会籍购买 */
	public static final String				PRINT_CODE_1016						= "1016";

	/* 打印编码规则:营业账单相关为:11** */
	/** 打印模板编号：POS预打单 */
	public static final String				PRINT_CODE_1101						= "1001";
	/** 打印模板编号：POS结账单 */
	public static final String				PRINT_CODE_1102						= "1002";
	/** 打印模板编号：POS点菜单 */
	public static final String				PRINT_CODE_1103						= "1003";
	/** 打印模板编号：标签单切 */
	public static final String				PRINT_CODE_1104						= "1104";
	/** 打印模板编号：大类 */
	public static final String				PRINT_CODE_1105						= "1105";
	/** 打印模板编号：单切单 */
	public static final String				PRINT_CODE_1106						= "1106";
	/** 打印模板编号：整台单 */
	public static final String				PRINT_CODE_1107						= "1107";
	/** 打印模板编号：转台 */
	public static final String				PRINT_CODE_1108						= "1108";
	/** 打印模板编号：退菜单 */
	public static final String				PRINT_CODE_1109						= "1109";
	/** 打印模板编号：补打结账单 */
	public static final String				PRINT_CODE_1110						= "1110";
	/** 打印模板编号：单品转台单 */
	public static final String				PRINT_CODE_1112						= "1112";
	/** 打印模板编号：一份一单 */
	public static final String				PRINT_CODE_1113						= "1113";
	/** 打印模板编号：打印所有档口退菜单 */
	public static final String				PRINT_CODE_1114						= "1114";
	/** 打印模板编号：起菜通知单 */
	public static final String				PRINT_CODE_1115						= "1115";

	/* 打印编码规则:交款交班等为:13** */
	/** 打印模板编号：POS交班单 */
	public static final String				PRINT_CODE_1301						= "1004";
	/** 打印模板编号：补打交班单 */
	public static final String				PRINT_CODE_1302						= "1111";
	/** 打印模板编号：交款凭证 */
	public static final String				PRINT_CODE_1303						= "1014";
	/** 打印模板编号：零找金 */
	public static final String				PRINT_CODE_1304						= "1304";

	/* 打印编码规则:外卖相关为:12** */
	/** 打印模板编号：外卖随餐单 */
	public static final String				PRINT_CODE_1201						= "1201";
	/** 打印模板编号：外卖取消订单 */
	public static final String				PRINT_CODE_1202						= "1202";
	/** 打印模板编号：外卖随餐单（异常单据） */
	public static final String				PRINT_CODE_1203						= "1203";
	/** 打印模板编号：外卖取消订单（异常单据） */
	public static final String				PRINT_CODE_1204						= "1204";
	/** 打印模板编号：外卖点餐清单 */
	public static final String				PRINT_CODE_1205						= "1205";

	/** 打印模板编号：ECO外卖异常单 */
	public static final String				PRINT_CODE_1206						= "1206";
	/** 打印模板编号：ECO外卖异常取消单 */
	public static final String				PRINT_CODE_1207						= "1207";

	/* 打印编码规则:微信相关为:14** */
	/** 打印模板编号：微信点餐清单 */
	public static final String				PRINT_CODE_1401						= "1401";

	/** 请求状态:请求中 */
	public static final String				REQUEST_STATUS_ING					= "01";
	/** 请求状态:请求成功 */
	public static final String				REQUEST_STATUS_COMPLETE				= "02";
	/** 请求状态:请求失败 */
	public static final String				REQUEST_STATUS_FAILURE				= "03";

	// /** 请求状态:请求中 */
	// public static final String REQUEST_STATUS_CANCLE_ING = "04";
	// /** 请求状态:请求成功 */
	// public static final String REQUEST_STATUS_CANCLE_COMPLETE = "05";
	// /** 请求状态:请求失败 */
	// public static final String REQUEST_STATUS_CANCLE_FAILURE = "06";

	/** 渠道:默认门店 */
	public static final String				CHANEL_MD01							= "MD01";
	/** 渠道:微信 */
	public static final String				CHANEL_WX02							= "WX02";
	/** 渠道:丰食 */
	public static final String				CHANEL_FS25							= "FS25";

	/** 渠道: 京东秒送 **/
	public static final String              CHANEL_JD31                         = "JD31";

	/** 渠道:饿了么外卖 */
	public static final String				CHANEL_EL09							= "EL09";
	/** 渠道:美团外卖 */
	public static final String				CHANEL_MT08							= "MT08";

	/** 渠道:新美大 */
	public static final String				CHANEL_MT11							= "MT11";

	/** 渠道:微信外卖 **/
	public static final String             CHANNEL_WM10                         = "WM10";
	/** 渠道:话务 */
	public static final String				CHANEL_CC04							= "CC04";
	/** 渠道:到家 */
	public static final String				CHANEL_DJ01							= "DJ01";

	/** 渠道:PAD渠道 */
	public static final String				PD_CHANEL							= "PD08";
	/** 渠道:E自助渠道 */
	public static final String				EZZ_CHANEL							= "EZZ14";
	/** 渠道:微生活点餐小程序 */
	public static final String				CHANEL_WSP17						= "WSP17";
	/** 渠道:微生活点餐H5 */
	public static final String				CHANEL_WSH16						= "WSH16";
	/** 渠道:D5厨房 */
	public static final String				CHANEL_D5							= "D515";

	/** 会员结算明细 : 售卡金额 */
	public static final String				CUSTOMER_SALECARD_MONEY				= "SK01";
	public static final String				CUSTOMER_SALECARD_MONEY_TXT			= "发卡售卡金额";
	/** 会员结算明细 : 押金金额 */
	public static final String				CUSTOMER_DEPOSIT_MONEY				= "YJ02";
	public static final String				CUSTOMER_DEPOSIT_MONEY_TXT			= "押金金额";
	/** 会员结算明细 : 有账单 */
	public static final String				CUSTOMER_DETAIL_HAVE_BILL			= "YZD03";
	public static final String				CUSTOMER_DETAIL_HAVE_BILL_TXT		= "账单消费";
	/** 会员结算明细 : 无账单 */
	public static final String				CUSTOMER_DETAIL_NO_BILL				= "WZD04";
	public static final String				CUSTOMER_DETAIL_NO_BILL_TXT			= "会员消费";
	/** 会员结算明细 : 退卡金额 */
	public static final String				CUSTOMER_BACKCARD_MONEY				= "TK05";
	public static final String				CUSTOMER_BACKCARD_MONEY_TXT			= "退卡金额";
	/** 会员结算明细 : 退押金金额 */
	public static final String				CUSTOMER_BACKDEPOSIT_MONEY			= "TYJ06";
	public static final String				CUSTOMER_BACKDEPOSIT_MONEY_TXT		= "退卡押金金额";
	/** 会员结算明细 : 充值 */
	public static final String				CUSTOMER_RECHARGE					= "CZ07";
	public static final String				CUSTOMER_RECHARGE_TXT				= "充值";
	/** 会员结算明细 : 购买会籍 */
	public static final String				CUSTOMER_BUYVIP						= "GMHJ08";
	public static final String				CUSTOMER_BUYVIP_TXT					= "购买会籍";

	/** 折扣方式 : 整单折扣 */
	public static final int					DISCOUNT_MODE_1						= 1;
	/** 折扣方式 : 折扣方案 */
	public static final int					DISCOUNT_MODE_2						= 2;
	/** 折扣方式 : 折让 */
	public static final int					DISCOUNT_MODE_3						= 3;
	/** 折扣方式 : 团体会员折扣 */
	public static final int					DISCOUNT_MODE_4						= 4;
	/** 折扣方式 : 会员折扣 */
	public static final int					DISCOUNT_MODE_5						= 5;
	/** 折扣方式 : 会员价 */
	public static final int					DISCOUNT_MODE_6						= 6;
	/** 折扣方式 : 线上优惠 */
	public static final int					DISCOUNT_MODE_7						= 7;
	/** 折扣方式 : 会员价+折扣方案(折上折) */
	public static final int					DISCOUNT_MODE_8						= 8;
	/** 折扣方式 : 普通折扣 */
	public static final int					DISCOUNT_MODE_9						= 9;
	/** 折扣方式 : 单品折扣 */
	public static final int					DISCOUNT_MODE_10					= 10;
	/** 折扣方式：活动折扣 */
	public static final int					DISCOUNT_MODE_11					= 11;
	/** 折扣方式：取消折扣 */
	public static final int					DISCOUNT_MODE_SINGLE_CANCEL			= -1;

	/** 第三方支付操作类型 : 支付 */
	public static final String				THIRD_PAY_CHARGE					= "CHARGE";
	/** 第三方支付操作类型 : 退款 */
	public static final String				THIRD_PAY_REFUND					= "REFUND";

	/** 第三方支付状态 : 支付失败 */
	public static final String				THIRD_PAY_STATUS_FAIL				= "0";
	/** 第三方支付状态 : 支付成功 */
	public static final String				THIRD_PAY_STATUS_SUCCESS			= "1";
	/** 第三方支付状态 : 处理中 */
	public static final String				THIRD_PAY_STATUS_PAYING				= "2";
	/** 第三方支付状态 : 未知 */
	public static final String				THIRD_PAY_STATUS_NOOK				= "3";
	/** 第三方支付状态 : 已退款 */
	public static final String				THIRD_PAY_STATUS_REFUND_SUCCESS		= "4";
	/** 第三方支付状态 : 退款中 */
	public static final String				THIRD_PAY_STATUS_REFUND				= "5";
	/** 第三方支付状态 : 退款失败 */
	public static final String				THIRD_PAY_STATUS_REFUND_FAIL		= "7";
	/** 第三方支付状态 : 已取消 */
	public static final String				THIRD_PAY_STATUS_REVOKED_SUCCESS	= "6";
	/** 第三方支付状态 : 取消中 */
	public static final String				THIRD_PAY_STATUS_REVOKED			= "8";
	/** 第三方支付状态 : 取消失败 */
	public static final String				THIRD_PAY_STATUS_REVOKED_FAIL		= "9";
	/** 第三方支付状态 : 人工处理 */
	public static final String				THIRD_PAY_STATUS_MANUAL				= "100";
	
	public static final Map<String, String>	THIRD_PAY_STATUS_CAPTION_MAP				= new HashMap<String, String>()
	{
		private static final long serialVersionUID = 1L;

		{
			put(THIRD_PAY_STATUS_FAIL, "支付失败");
			put(THIRD_PAY_STATUS_SUCCESS, "支付成功");
			put(THIRD_PAY_STATUS_PAYING, "支付中");
			put(THIRD_PAY_STATUS_NOOK, "连接超时");
			put(THIRD_PAY_STATUS_REFUND_SUCCESS, "已退款");
			put(THIRD_PAY_STATUS_REFUND, "退款中");
			put(THIRD_PAY_STATUS_REFUND_FAIL, "退款失败");
			put(THIRD_PAY_STATUS_REVOKED_SUCCESS, "已取消");
			put(THIRD_PAY_STATUS_REVOKED, "取消中");
			put(THIRD_PAY_STATUS_REVOKED_FAIL, "取消失败");
			put(THIRD_PAY_STATUS_MANUAL, "人工处理");
		}
	};

	/** 业务类型 : 会员充值 */
	public static final String				SERVICE_TYPE_RECHARGE				= "hy01";
	/** 业务类型 : 商城券 */
	public static final String				SERVICE_TYPE_COUPON					= "sc02";
	/** 业务类型 : 微点餐/外卖 */
	public static final String				SERVICE_TYPE_ORDER					= "wd03";
	/** 业务类型 : pos点餐消费 */
	public static final String				SERVICE_TYPE_CONSUME				= "pos04";
	/** 业务类型 : 会员发卡 */
	public static final String				SERVICE_TYPE_ACTIVATION				= "hyfk07";
	
	public static final Map<String, String>	SERVICE_TYPE_CAPTION_MAP				= new HashMap<String, String>()
	{
		private static final long serialVersionUID = 1L;

		{
			put(SERVICE_TYPE_RECHARGE, "会员充值");
			put(SERVICE_TYPE_COUPON, "商城券");
			put(SERVICE_TYPE_ORDER, "微点餐/外卖");
			put(SERVICE_TYPE_CONSUME, "POS点餐消费");
			put(SERVICE_TYPE_ACTIVATION, "会员发卡");
		}
	};

	/** 二维扫码方式 主扫 */
	public static final String				PAYMENT_TYPE_ZS						= "1";
	/** 二维扫码方式 被扫 */
	public static final String				PAYMENT_TYPE_BS						= "2";

	/** 服务费方式:固定金额(GD01) */
	public static final String				SERVICE_MODE_GD01					= "GD01";
	/** 服务费方式:按比例(BL02) */
	public static final String				SERVICE_MODE_BL02					= "BL02";

	/** 服务费类别:基本服务费(JB01) */
	public static final String				SERVICE_FEE_TYPE_JB01				= "JB01";
	/** 服务费类别:附加服务费(FJ02) */
	public static final String				SERVICE_FEE_TYPE_FJ02				= "FJ02";

	/** 金额取整类型: 四舍五入取整 **/
	public static final String				BILL_POINT_TYPE_HALF_ADJUST			= "half_adjust";
	/** 金额取整类型: 全舍取整 **/
	public static final String				BILL_POINT_TYPE_ROUNDING			= "rounding";
	/** 金额取整类型: 进1取整 **/
	public static final String				BILL_POINT_TYPE_ONE_ADJUST			= "one_adjust";

	/** 会员操作撤销状态:已撤销 **/
	public static final String				CUSTOMER_OPERATE_CANCEL_STATE_Y		= "1";
	/** 会员操作撤销状态:未撤销 **/
	public static final String				CUSTOMER_OPERATE_CANCEL_STATE_N		= "0";

	/** 会员操作状态:失败 **/
	public static final String				CUSTOMER_OPERATE_STATE_FAIL			= "0";
	/** 会员操作状态:成功 **/
	public static final String				CUSTOMER_OPERATE_STATE_SUCCESS		= "1";
	/** 会员操作状态:待处理 **/
	public static final String				CUSTOMER_OPERATE_STATE_WAIT			= "2";
	/** 会员操作状态:处理中 **/
	public static final String				CUSTOMER_OPERATE_STATE_PROCESS		= "3";

	/**
	 * 消费满减现金
	 */
	public static final String				MARKETING_ACTIVITY_MEJXJ			= "mejxj";
	/**
	 * 消费满打折03
	 */
	public static final String				MARKETING_ACTIVITY_MEDZ				= "medz";
	/**
	 * 消费满加价购
	 */
	public static final String				MARKETING_ACTIVITY_MEJJG			= "mejjg";
	/**
	 * 消费满赠菜
	 */
	public static final String				MARKETING_ACTIVITY_MEZC				= "mezc";
	/**
	 * 折后统一价
	 */
	public static final String				MARKETING_ACTIVITY_ZHTYJ			= "zhtyj";
	/**
	 * 第二份半价
	 */
	public static final String				MARKETING_ACTIVITY_DEFBJ			= "defbj";

	/** 会员类型:SaaS会员 */
	public static final String				CUSTOMER_TYPE_SAAS					= "saas";
	/** 会员类型:微生活 */
	public static final String				CUSTOMER_TYPE_ACEWILL				= "acewill";
	/** 会员类型:新美大会员 */
	public static final String				CUSTOMER_TYPE_XMD					= "xmd";

	/** 优惠劵类型:代金券 */
	public static final String				CUSTOMER_COUPON_TYPE_CASH			= "1";
	/** 优惠劵类型:礼品券/菜品劵 */
	public static final String				CUSTOMER_COUPON_TYPE_DISH			= "2";
	
	/** 优惠券抵扣方式:抵扣菜品全部菜价 */
	public static final String				COUPON_TOTAL_MODE_ALL				= "1";
	/** 优惠券抵扣方式:抵扣菜品部分菜价 */
	public static final String				COUPON_TOTAL_MODE_PART				= "2";
	
	/** 优惠劵类型:有码券 */
	public static final int					COUPON_TYPE_CODE					= 1;
	/** 优惠劵类型:无码券 */
	public static final int					COUPON_TYPE_NO_CODE					= 2;
	/** 优惠劵类型:优惠券 */
	public static final int					COUPON_TYPE_DISCOUNT					= 3;

	/** 优惠券属性:菜品券 */
	public static final String				COUPONS_PRO_DISH					= "coupons_dish";
	/** 优惠券属性:代金券 */
	public static final String				COUPONS_PRO_DEDUCT					= "coupons_deduct";

	/** 账单处理方：云端处理 */
	public static final String				CLOUD_CLIENT_HANDLE					= "CLOUD_HANDLE";
	/** 账单处理方：门店处理 */
	public static final String				STORE_CLIENT_HANDLE					= "STORE_HANDLE";

	/** 账单结账方：云端结账 */
	public static final String				CLOUD_CLIENT_PAY					= "CLOUD_PAY";
	/** 账单结账方：门店结账 */
	public static final String				STORE_CLIENT_PAY					= "STORE_PAY";

	/** 云账单是否同步：未同步 */
	public static final String				SYNC_STATE_NO						= "00";
	/** 云账单是否同步：同步成功 */
	public static final String				SYNC_STATE_OK						= "01";
	/** 云账单是否同步：同步中 */
	public static final String				SYNC_STATE_ING						= "02";
	/** 云账单是否同步：同步失败 */
	public static final String				SYNC_STATE_FAIL						= "03";
	/** 云账单是否同步：未知 */
	public static final String				SYNC_STATE_UNKNOWN					= "04";

	/** 查询账单成功 */
	public static final int					BILL_CLOUD_SUCCESS_CODE				= 0;
	/** 查询账单不存在 */
	public static final int					BILL_CLOUD_FAILURE_CODE				= 1;
	/** 桌台未开台（或已结账） */
	public static final int					BILL_CLOUD_TABLE_CODE				= 2;
	/** 账单有变化 */
	public static final int					BILL_CLOUD_CHANGE_CODE				= 3;
	/** 账单被锁定（不能操作） */
	public static final int					BILL_CLOUD_LOCK_CODE				= 4;
	/** 零元账单（结账成功） */
	public static final int					BILL_CLOUD_ZERO_CODE				= 5;
	/** 桌台不存在 */
	public static final int					BILL_CLOUD_TABLE_EXIST_CODE			= 6;
	/** 会员信息为空 */
	public static final int					BILL_CLOUD_CUSTOMER_NULL_CODE		= 7;
	/** 订单号为空 **/
	public static final int					BILL_CLOUD_BILL_NULL_CODE			= 8;
	/** 无菜零账单 */
	public static final int					BILL_CLOUD_NO_DISH_CODE				= 13;
	/** 奉送、退菜零账单 */
	public static final int					BILL_CLOUD_GIVE_DISH_CODE			= 14;
	/** 网络异常 */
	public static final int					BILL_CLOUD_EXCEPTION_CODE			= 99;

	/** 异常付款记录类型：应付 */
	public static final String				EXCE_PAY_PAYABLE					= "PAYABLE";
	/** 异常付款记录类型：应退 */
	public static final String				EXCE_PAY_REFUNDABLE					= "REFUNDABLE";
	/** 异常记录类型：云端和门店端结账金额不一致 */
	public static final String				EXCE_BILL_CHECKOUT					= "PAYMENT_AMOUNT";
	/** 异常记录类型：云端和门店端菜品数量不相等 */
	public static final String				EXCE_BILL_SIZE_UN					= "SIZE_UNEQUAL";

	/** 请求来源：外卖 */
	public static final String				SOURCE_CC_ORDER						= "cc_order";
	/** 请求来源： 点菜 */
	public static final String				SOURCE_ORDER_DEVICE					= "order_device";
	/** 请求来源： E指馋(IOS) */
	public static final String				SOURCE_ANDROID_PAD					= "android_pad";
	/** 请求来源： POS */
	public static final String				SOURCE_WIN_POS						= "win_pos";
	/** 请求来源：E指馋(安卓) */
	public static final String				SOURCE_NEBULAPOS					= "pad_diancan";
	/** 请求来源： E待客正餐 */
	public static final String				SOURCE_E_DAIKE						= "e_daike";
	/** 请求来源： E待客快餐 */
	public static final String				SOURCE_E_DAIKE_FAST					= "e_daike_fast";
	/** 请求来源： KVS(安卓) */
	public static final String				SOURCE_KVS_ANDROID					= "kvs_android";
	/** 请求来源： KVS(win) */
	public static final String				SOURCE_KVS_WIN						= "kvs_win";
	/** 请求来源： E自助 */
	public static final String				SOURCE_SELFORDER					= "e_zizhu";
	/** 请求来源： E叫号 */
	public static final String				SOURCE_CALLINGQUEUE					= "e_jiaohao";
	/** 请求来源： 移动终端 */
	public static final List<String>		SOURCE_MOVE							= Arrays.asList(SOURCE_ANDROID_PAD, SOURCE_NEBULAPOS, SOURCE_E_DAIKE, SOURCE_E_DAIKE_FAST, SOURCE_SELFORDER);

	/** 估清菜品来源:前端设置 */
	public static final String				SOLDOUT_CHANNEL_SET					= "SZ01";
	/** 估清菜品来源:单品关联 */
	public static final String				SOLDOUT_CHANNEL_LINK				= "GL02";

	/** 冲减是否为隔日退（整单、部分）*/
	public static final String				IS_CROSS_DAY			            = "cross_day";

	/**豪享来并台之后操作加菜退菜*/
	public static final String               IS_COMBINED_AFTER                  ="combined_after";
	/**
	 * 豪享来并台
	 */
	public static final String               COMBINED_TABLE_TYPE                  ="BT01";
	public static final String               CANCLE_COMBINED_TABLE_TYPE           ="QXBT02";
	public static final String               CHANGE_TABLE_TYPE                    ="ZT03";
	public static final String               SINGLE_CHANGE_TABLE_TYPE             ="ZT04";

}
