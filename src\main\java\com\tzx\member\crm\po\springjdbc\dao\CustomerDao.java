package com.tzx.member.crm.po.springjdbc.dao;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import net.sf.json.JSONObject;

import com.tzx.member.common.entity.CrmCardPaymentListEntity;
import com.tzx.member.common.entity.CrmCardTradingListEntity;
import com.tzx.member.common.entity.CrmIncorporationArrearListEntity;
import com.tzx.member.common.entity.PosCustomerOperateListEntity;
import com.tzx.pos.base.dao.BaseDao;
import com.tzx.pos.bo.dto.PosThirdPaymentOrderEntity;

/**
 * <AUTHOR>
 *
 */
public interface CustomerDao extends BaseDao
{
	String	NAME	= "com.tzx.member.crm.po.springjdbc.dao.imp.CustomerDaoImp";

	/**
	 * 新增会员卡交易记录
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param businessDate
	 * @param shiftId
	 * @param operatorId
	 * @param operator
	 * @param chanel
	 * @param cardId
	 * @param cardCode
	 * @param cardClassId
	 * @param name
	 * @param mobil
	 * @param deposit
	 * @param customerId
	 * @param billCode
	 * @param operatType
	 * @param billMoney
	 * @param thirdBillCode
	 * @param billCodeOriginal
	 * @param batchNum
	 * @param activityId
	 * @param mainTrading
	 * @param rewardTrading
	 * @param mainOriginal
	 * @param rewardOriginal
	 * @param revokedTrading
	 * @param lastUpdatetime
	 * @param storeUpdatetime
	 * @param operateTime
	 * @param invoiceBalance
	 * @param totalBalance
	 * @param rewardBalance
	 * @param mainBalance
	 * @param payType
	 * @param salesman
	 * @param commissionSalerMoney
	 * @param commissionStoreMoney
	 * @param isInvoice
	 * @param requestStatus
	 * @throws Exception
	 */
	public void insertCrmCardTradingList(String tenancyId, Integer storeId, Date businessDate, Integer shiftId, Integer operatorId, String operator, String chanel, Integer cardId, String cardCode, Integer cardClassId, String name, String mobil, Double deposit, Integer customerId, String billCode,
			String operatType, Double billMoney, String thirdBillCode, String billCodeOriginal, String batchNum, Integer activityId, Double mainTrading, Double rewardTrading, Double mainOriginal, Double rewardOriginal, Double revokedTrading, Timestamp lastUpdatetime, Timestamp storeUpdatetime,
			Timestamp operateTime, Double invoiceBalance, Double totalBalance, Double rewardBalance, Double mainBalance, String payType, Integer salesman, Double commissionSalerMoney, Double commissionStoreMoney, String isInvoice, String requestStatus, String paymentState, String rechargeState)
			throws Exception;

	/**
	 * 新增会员卡交易记录
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param cardTrading
	 * @throws Exception
	 */
	public void insertCrmCardTradingList(String tenancyId, Integer storeId, CrmCardTradingListEntity cardTrading) throws Exception;

	/**
	 * 充值支付记录
	 * 
	 * @param tenancyId
	 * @param cardId
	 * @param billCode
	 * @param thirdBillCode
	 * @param paymentId
	 * @param rexchangeRate
	 * @param payMoney
	 * @param localCurrency
	 * @param payNo
	 * @param storeUpdatetime
	 * @param lastUpdatetime
	 * @throws Exception
	 */
	public void insertCrmCardPaymentList(String tenancyId, Integer cardId, String billCode, String thirdBillCode, Integer paymentId, Double rexchangeRate, Double payMoney, Double localCurrency, String payNo, Timestamp storeUpdatetime, Timestamp lastUpdatetime) throws Exception;

	/**
	 * @param tenancyId
	 * @param cardId
	 * @param cardPaymentList
	 * @throws Exception
	 */
	public void insertCrmCardPaymentList(String tenancyId, Integer cardId, CrmCardPaymentListEntity cardPaymentList) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param thirdBillCode
	 * @throws Exception
	 */
	public void deleteCrmCardTradingListByThirdBillCode(String tenancyId, Integer storeId, String thirdBillCode) throws Exception;

	/**
	 * @param tenancyId
	 * @param thirdBillCode
	 * @throws Exception
	 */
	public void deleteCrmCardPaymentListByThirdBillCode(String tenancyId, String thirdBillCode) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param thirdBillCode
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getCrmCardTradingListByThirdBillCode(String tenancyId, Integer storeId, String thirdBillCode) throws Exception;

	/**
	 * @param tenancyId
	 * @param thirdBillCode
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getCrmCardPaymentListByThirdBillCode(String tenancyId, String thirdBillCode) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param thirdBillCode
	 * @return
	 * @throws Exception
	 */
	public JSONObject getCrmCardTradingListByBillCode(String tenancyId, Integer storeId, String billCode) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param thirdBillCode
	 * @param paymentState
	 * @param rechargeState
	 * @param requestStatus
	 * @param requestCode
	 * @param requestMsg
	 * @throws Exception
	 */
	public void updateCrmCardTradingListForState(String tenancyId, Integer storeId, String thirdBillCode, String paymentState, String rechargeState, String requestStatus, String requestCode, String requestMsg) throws Exception;

	/**
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param thirdBillCode
	 * @param paymentState
	 * @param rechargeState
	 * @throws Exception
	 */
	public void updateCrmCardTradingPayState(String tenancyId, Integer storeId, String thirdBillCode, String paymentState, String rechargeState) throws Exception;

	/**
	 * 
	 * @param tenancyId
	 * @param thirdBillCode
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getCrmPayMentWayByThirdBillCode(String tenancyId, String thirdBillCode) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param para
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getCrmInvoicePayment(String tenancyId, Integer storeId, JSONObject para) throws Exception;

	/**
	 * @param tenancy_id
	 * @param storeId
	 * @param cardcode
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getCrmCardTradingListByCardcode(String tenancyId, Integer storeId, String cardcode, String thirdBillCode,String operatType) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param para
	 * @return
	 * @throws Exception
	 */
	public JSONObject getCrmLegalPerInfo(String tenancyId, Integer storeId, JSONObject para) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param para
	 * @return
	 * @throws Exception
	 */
	public int getCrmInvoice(String tenancyId, Integer storeId, JSONObject para) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param customerOperate
	 * @throws Exception
	 */
	public void insertPosCustomerOperateList(String tenancyId, Integer storeId, PosCustomerOperateListEntity customerOperate) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param customerOperate
	 * @throws Exception
	 */
	public void updatePosCustomerOperateList(String tenancyId, Integer storeId, PosCustomerOperateListEntity customerOperate) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param billCode
	 * @param paymentState
	 * @param operateState
	 * @param requestStatus
	 * @param requestCode
	 * @param requestMsg
	 * @param finishTime
	 * @throws Exception
	 */
	public void updatePosCustomerOperateList(String tenancyId, Integer storeId, String thirdBillCode,String paymentState,String operateState,String requestStatus,Integer requestCode,String requestMsg,Timestamp finishTime,String operatType) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param thirdBillCode
	 * @param paymentState
	 * @param operateState
	 * @param requestStatus
	 * @param requestCode
	 * @param requestMsg
	 * @param finishTime
	 * @param operatType
	 * @param billCode
	 * @throws Exception
	 */
	public void updatePosCustomerOperateList(String tenancyId, Integer storeId, String thirdBillCode,String paymentState,String operateState,String requestStatus,Integer requestCode,String requestMsg,Timestamp finishTime,String operatType,String billCode,String extendParam) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param billCode
	 * @param paymentState
	 * @param operateState
	 * @param requestStatus
	 * @param requestCode
	 * @param requestMsg
	 * @param finishTime
	 * @param operatType
	 * @param paymentId
	 * @param paymentClass
	 * @throws Exception
	 */
	public void updatePosCustomerOperateList(String tenancyId, Integer storeId, String thirdBillCode,String paymentState,String operateState,String requestStatus,Integer requestCode,String requestMsg,Timestamp finishTime,String operatType,Integer paymentId,String paymentClass) throws Exception;
	
	/** 修改会员操作记录取消状态
	 * @param tenancyId
	 * @param storeId
	 * @param thirdBillCode
	 * @param cancelState
	 * @param operatType
	 * @throws Exception
	 */
	public void updatePosCustomerOperateList(String tenancyId, Integer storeId, String thirdBillCode, String cancelState, String operatType) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param customerCode
	 * @param mobil
	 * @param cardCode
	 * @param incorporationId
	 * @param customerId
	 * @param billCode
	 * @param batchNum
	 * @return
	 * @throws Exception
	 */
	public PosCustomerOperateListEntity queryPosCustomerOperateListByBillcode(String tenancyId, Integer storeId, String customerCode, String mobil,String cardCode,String thirdCode,String incorporationId, String customerId,String thirdBillCode, String batchNum,String actionType) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param paramJson
	 * @return
	 * @throws Exception
	 */
	public PosCustomerOperateListEntity queryPosCustomerOperateListByBillcode(String tenancyId, Integer storeId, JSONObject paramJson) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param cardCode
	 * @param billCode
	 * @param batchNum
	 * @return
	 * @throws Exception
	 */
	public PosCustomerOperateListEntity queryPosCustomerOperateListByCustomerCard(String tenancyId, Integer storeId, String cardCode, String thirdBillCode,String batchNum,String actionType) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param customerCode
	 * @param mobil
	 * @param billCode
	 * @param batchNum
	 * @return
	 * @throws Exception
	 */
	public PosCustomerOperateListEntity queryPosCustomerOperateListByCredit(String tenancyId, Integer storeId, String customerCode, String mobil,String thirdBillCode, String batchNum,String actionType) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param incorporationId
	 * @param customerId
	 * @param billCode
	 * @param batchNum
	 * @return
	 * @throws Exception
	 */
	public PosCustomerOperateListEntity queryPosCustomerOperateListByIncorporation(String tenancyId, Integer storeId, String incorporationId, String customerId,String thirdBillCode, String batchNum,String actionType) throws Exception;
	
	/** 查询会员发卡记录
	 *  查询pos_customer_operate_list
	 * @param tenancyId
	 * @param storeId
	 * @param cardCode
	 * @param billCode
	 * @param batchNum
	 * @param actionType
	 * @return
	 * @throws Exception
	 */
	public PosCustomerOperateListEntity queryCustomerCardActivationStatus(String tenancyId, Integer storeId, String cardCode, String thirdCode,String thirdBillCode) throws Exception;
	
	/** 查询充值记录
	 * @param tenancyId
	 * @param storeId
	 * @param cardCode
	 * @param thirdCode
	 * @param billCode
	 * @param operatType
	 * @return
	 * @throws Exception
	 */
	public PosCustomerOperateListEntity queryCustomerCardRechargeStatus(String tenancyId, Integer storeId, String cardCode, String thirdCode, String thirdBillCode,String operatType) throws Exception;
	
	
	/**
	 * 查询微生活操作记录
	 * @param tenancyId
	 * @param storeId
	 * @param third_bill_code
	 * @param billCode
	 * @param operatType
	 * @return
	 * @throws Exception
	 */
	public PosCustomerOperateListEntity queryAcewillCustomerDeal(String tenancyId, Integer storeId, String cardCode,String thirdBillCode, String billCode,String operateState,String cancelState, String operatType)throws Exception;
	
	/**
	 * 查询微生活有效的消费操作记录
	 * @param tenancyId
	 * @param storeId
	 * @param cardCode
	 * @param thirdBillCode
	 * @param outTradeNo
	 * @param operatType
	 * @return
	 * @throws Exception
	 */
	public PosCustomerOperateListEntity queryCustomerOperateListByAcewillXF(String tenancyId, Integer storeId, String cardCode, String thirdBillCode, String outTradeNo, String operatType) throws Exception;

	/**
	 * 查询付款方式名称
	 * @param tenancyId
	 * @param storeId
	 * @return
	 * @throws Exception
	 */
	public JSONObject findPrintParam(String tenancyId, Integer storeId,int payId) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param incorporationGzList
	 * @throws Exception
	 */
	public void insertCrmIncorporationGzList(String tenancyId, Integer storeId, CrmIncorporationArrearListEntity incorporationGzList) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param aidOrderNo
	 * @param objectName
	 * @return
	 * @throws Exception
	 */
	public PosThirdPaymentOrderEntity getThirdPaymentByAidOrderNum(String tenancyId, int storeId, String aidOrderNo, String objectName) throws Exception;

	/**
	 * 查询会员交易记录
	 * 
	 * @param tenancyId
	 * @param thirdBillCode
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> qureyCrmCardTradingListByThirdBillCode(String tenancyId, Integer storeId, String thirdBillCode) throws Exception;
	
}
