package com.tzx.orders.bo.imp;

import com.tzx.base.bo.PosCodeService;
import com.tzx.base.common.util.NumericConvertUtils;
import com.tzx.base.entity.PosBillItem;
import com.tzx.clientorder.wxorder.base.util.WxOrderUtil;
import com.tzx.framework.common.constant.Code;
import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.CcErrorCode;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.*;
import com.tzx.orders.base.constant.Constant;
import com.tzx.orders.base.util.OrderUtil;
import com.tzx.orders.bo.OrdersManagementService;
import com.tzx.orders.bo.dto.OrderState;
import com.tzx.orders.bo.dto.PosBill;
import com.tzx.orders.bo.dto.PosBillPayment;
import com.tzx.orders.po.springjdbc.dao.OrdersManagementDao;
import com.tzx.pos.base.constant.FunctionCode;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.controller.CometDataNoticeClientRunnable;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.base.util.QrCodeUtils;
import com.tzx.pos.base.util.SendUdpToPos;
import com.tzx.pos.bo.*;
import com.tzx.pos.bo.imp.PosBaseServiceImp;
import com.tzx.pos.bo.payment.PosPaymentService;
import com.tzx.pos.po.springjdbc.dao.PosBillMemberDao;
import com.tzx.pos.po.springjdbc.dao.PosDao;
import com.tzx.pos.po.springjdbc.dao.PosDishDao;
import com.tzx.pos.po.springjdbc.dao.PosPaymentDao;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.LogManager;
import org.apache.log4j.Logger;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.annotation.Resource;
import java.io.File;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

import static com.tzx.orders.base.constant.Constant.*;

@Service(OrdersManagementService.NAME)
public class OrdersManagementServiceImp extends PosBaseServiceImp implements OrdersManagementService {
    private static final Logger logger = LogManager.getLogger(OrdersManagementServiceImp.class);

    public static final JSONObject CONFIG = new JSONObject();

    @Resource(name = OrdersManagementDao.NAME)
    private OrdersManagementDao ordersDao;

    @Resource(name = PosService.NAME)
    private PosService posService;

    @Resource(name = PosDishService.NAME)
    private PosDishService posDishService;

    @Resource(name = PaymentService.NAME)
    private PaymentService paymentService;

    @Resource(name = PosPaymentService.NAME)
    private PosPaymentService posPaymentService;

    @Resource(name = PosPrintService.NAME)
    private PosPrintService posPrintService;

    @Resource(name = PosPrintNewService.NAME)
    private PosPrintNewService posPrintNewService;

    @Resource(name = PosPaymentDao.NAME)
    private PosPaymentDao paymentDao;
    
    @Resource(name = PosBillMemberDao.NAME)
	private PosBillMemberDao			memberDao;

    @Resource(name=PosCodeService.NAME)
    private PosCodeService codeService;

    @Resource(name = "transactionManager")
    private DataSourceTransactionManager transactionManager;

    @Resource(name = PosDishDao.NAME)
	private PosDishDao posDishDao;

    @Resource(name = PosDao.NAME)
    private PosDao	posDao;

    public static ThreadPoolExecutor orderThreadPool = new ThreadPoolExecutor(3,5,3, TimeUnit.SECONDS, new ArrayBlockingQueue<Runnable>(10000),new ThreadPoolExecutor.AbortPolicy());

    // 最近的ECO 新单时间
    protected static AtomicLong ecoOrderTime = new AtomicLong(0L);

    @SuppressWarnings("unchecked")
    @Override
    public JSONObject getOrdersList(String tenentid, int storeid, JSONObject para) throws Exception {
        StringBuilder sb = new StringBuilder();
        StringBuilder sb2 = new StringBuilder();
        sb.append(" select COALESCE(ol.delivery_state,'0') as delivery_state,ol.*,count(rd.id) as reason_count,sum(re.pay_money) as pay_money,(case when ol.order_type='ZT01' then 'WD02' when ol.order_type='WM02' then 'WS03' when ol.order_type='DN03' then 'TS01' end) as sale_mode,string_agg(pw.payment_name1,'+') as payment_name from cc_order_list ol");
        sb.append(" left join cc_order_reason_detail rd on ol.order_code=rd.order_code and rd.type='TS02'");
        sb.append(" left join cc_order_repayment re on re.order_code=ol.order_code");
        sb.append(" left join payment_way pw on re.payment_id=pw.id");
        sb.append(" where ol.tenancy_id='").append(tenentid).append("' and ol.store_id=").append(storeid);
//        sb.append(" and ol.merge_order_code is NULL");

        int pagenum = para.containsKey("page") && para.getInt("page") > 0 ? para.getInt("page") : 1;
        // int limit = para.containsKey("rows") ? para.getInt("rows") : 20;

        Set<String> keys = para.keySet();
        String isContain="";
        String isAdvanceOrders = "";
        for (String key : keys) {
            if ("consigner".equals(key) || "order_phone".equals(key) || "order_code".equals(key) || "ride_name".equals(key)) {
                sb.append(" and ol.").append(key).append(" like '%").append(para.getString(key)).append("%'");
            } else if ("single_time".equals(key)) {
                sb.append(" and ol.single_time").append(" >= '").append(para.getString(key)).append("'");
            } else if ("single_time2".equals(key)) {
                sb.append(" and ol.single_time").append(" <= '").append(para.getString(key)).append("'");
            } else if ("order_state".equals(key)) {
            	if(para.getString(key).contains(",")){
            		 StringBuffer order_state= new StringBuffer("");
            		 String[] state = para.getString(key).split(",");
            		 for(String str:state){
            			 order_state.append("'").append(str).append("',");
            		 }
            		 String orderState =  order_state.toString().substring(0, order_state.toString().length()-1);
            		 sb.append(" and ol.").append(key).append(" in (").append(orderState).append(")");
            		 if(para.getString(key).contains("10") || para.getString(key).contains("12")){
            			 isContain = "yes";
            		 }
            	}else{
            		 if(para.getString(key).contains("11")){
            			 isContain = "yes";
            		 }
            		 sb.append(" and ol.").append(key).append(" = '").append(para.getString(key)).append("'");
            	}
            } else if ("chanel".equals(key)) {
                if (!"".equals(para.optString(key).trim())) {
                    sb.append(" and ol.").append(key).append(" = '").append(para.getString(key)).append("'");
                }

            }else if("delivery_state".equals(key)){
	            if(para.getString(key).contains(",")){
	           		 StringBuffer delivery_state= new StringBuffer("");
	           		 String[] state = para.getString(key).split(",");
	           		 for(String str:state){
	           			delivery_state.append("'").append(str).append("',");
	           		 }
	           		 String deliveryState =  delivery_state.toString().substring(0, delivery_state.toString().length()-1);
              		 //and ol.order_state in ('10','12')
	           		 sb.append("  and  ol.").append(key).append(" in (").append(deliveryState).append(")");
           	     }else{
           	    	 //and ol.order_state in ('10','12') 
              		 sb.append(" and  ol.").append(key).append(" = '").append(para.getString(key)).append("'");
           	     }
            }else if("send_time".equals(key)){
                if(para.getString(key).equals("isAdvanceOrders")){
                    isAdvanceOrders="yes";
                }
            }
        }
        
        sb.append(" and (ol.refund_type is null or ol.refund_type ='0') ");
        sb.append(" group by ol.id");

        if(keys.contains("refund_type")){//存在申请的退款订单 申请状态：21  退款类型 ：refund_type 1：部分退，2：整单退
        	sb.insert(0, "select c.*,cc2.* from (").append(") as c ");
        	//sb.append(", ( SELECT substring(order_code from 0 for (char_length(order_code)-1)) refund_code,refund_type,refund_type as refund_order_type,shop_fee as refund_shop_fee,single_time as refund_single_time,refund_oper_time as oper_time from cc_order_list  where refund_type in ('1','2') and  order_state in ('21','22') ORDER BY refund_single_time DESC )  cc2  where c.order_code = cc2.refund_code and c.order_state in ('10','12') ");
        	sb.append(", ( SELECT SUBSTRING ( cc1.order_code FROM 0 FOR ( CHAR_LENGTH (cc1.order_code) - 1 )) refund_code, cc1.refund_type, cc1.refund_type AS refund_order_type, cc1.shop_fee AS refund_shop_fee, cc1.single_time AS refund_single_time, cc1.refund_oper_time AS oper_time , cc1.order_state AS refund_order_state FROM cc_order_list cc1 INNER JOIN ( SELECT MAX (ID) ID, SUBSTRING ( order_code FROM 0 FOR (CHAR_LENGTH(order_code) - 1)) orderCode FROM cc_order_list WHERE refund_type in ('1','2') and order_state IN ('21') GROUP BY orderCode ) cc ON SUBSTRING ( cc1.order_code FROM 0 FOR ( CHAR_LENGTH (cc1.order_code) - 1 )) = cc.orderCode AND cc. ID = cc1. ID ) cc2 WHERE C .order_code = cc2.refund_code AND C .order_state IN ('10','11', '12') ");
        }else if(!keys.contains("order_state") && !keys.contains("delivery_state")  && "".equals(isAdvanceOrders)){//全部订单
        	sb.insert(0, "select c.*,cc2.* from (").append(") as c ");
        	sb.append(" left join ( SELECT SUBSTRING ( cc1.order_code FROM 0 FOR ( CHAR_LENGTH (cc1.order_code) - 1 )) refund_code, cc1.refund_type, cc1.refund_type AS refund_order_type, cc1.shop_fee AS refund_shop_fee, cc1.single_time AS refund_single_time, cc1.refund_oper_time AS oper_time , cc1.order_state AS refund_order_state FROM cc_order_list cc1 INNER JOIN ( SELECT MAX (ID) ID, SUBSTRING ( order_code FROM 0 FOR (CHAR_LENGTH(order_code) - 1)) orderCode FROM cc_order_list WHERE refund_type in ('1','2') and order_state IN ('21') GROUP BY orderCode ) cc ON SUBSTRING ( cc1.order_code FROM 0 FOR ( CHAR_LENGTH (cc1.order_code) - 1 )) = cc.orderCode AND cc. ID = cc1. ID ) cc2 on C .order_code = cc2.refund_code AND C .order_state IN ('10', '11','12') ");
        }else{//其他类型订单
        	sb.insert(0, "select * from (").append(") as c ");
            if(!"".equals(isAdvanceOrders)){
                sb.append(" where c.send_time <> '立即配送' and to_char(now(),'yyyy-mm-dd hh24:mi:ss')< c.send_time ");
            }
        }	        
        if(!"".equals(isContain)){//完成订单
        	sb.append(" where c.order_code not in  ( SELECT substring(order_code from 0 for (char_length(order_code)-1)) refund_code from cc_order_list  where refund_type in ('1','2') and order_state in ('21') )  ");
        }
        
        long total = ordersDao.countSql(tenentid, sb.toString());

        if (para.containsKey("sort")) {
        	if(keys.contains("refund_type")){
                sb2.append(" order by cc2.").append(para.get("sort"));
                if (para.containsKey("order")) {
                    sb2.append(" ").append(para.get("order"));
                }
        	}else{
                sb2.append(" order by c.").append(para.get("sort"));
                if (para.containsKey("order")) {
                    sb2.append(" ").append(para.get("order"));
                }
        	}
        }
        sb.append(sb2.toString());
        // sb.append(sb2.toString()).append(" limit " + limit + " offset " +
        // (limit * (pagenum - 1)));

        List<JSONObject> list = ordersDao.query4Json(tenentid, sb.toString());
        // for (JSONObject json : list)
        // {
        // json.put("item", ordersDao.getOrderItemByOrdercode(tenentid, storeid,
        // json.optString("order_code"), json.optString("sale_mode")));
        // }
        JSONObject result = new JSONObject();

        result.put("page", pagenum);
        result.put("total", total);
        result.put("rows", list);

        return result;
    }

    @Override
    public JSONObject getPaymentWay(String tenentid, int storeid, JSONObject para) throws Exception {
        JSONObject resultJson = new JSONObject();
        resultJson.put("data", ordersDao.getPaymentWay(tenentid, storeid));
        return resultJson;
    }

    @Override
    public JSONObject getUnusualReason(String tenentid, int storeid, JSONObject para) throws Exception {
        JSONObject resultJson = new JSONObject();

        List<JSONObject> unusualList = ordersDao.getUnusualReason(tenentid, storeid, para);
        List<JSONObject> resultList = new ArrayList<JSONObject>();
        for (JSONObject json1 : unusualList) {
            if ("0".equals(json1.optString("father_id"))) {
                List<JSONObject> childrenList = new ArrayList<JSONObject>();
                for (JSONObject json2 : unusualList) {
                    if (json1.optString("id").equals(json2.optString("father_id"))) {
                        childrenList.add(json2);
                    }
                }

                json1.put("children", childrenList);
                resultList.add(json1);
            }
        }
        resultJson.put("data", resultList);
        return resultJson;
    }

    @Override
    public JSONObject batchTakeOrder(String tenancyId,int storeId) throws Exception {
        String queryNewOrder="SELECT order_code,chanel as channel FROM cc_order_list where order_state='02';";
       List<JSONObject> newOrders= ordersDao.query4Json(tenancyId,queryNewOrder);

       for(JSONObject order:newOrders){
           JSONObject para = getPosOptState(tenancyId, storeId);
           para.put("order_code", order.optString("order_code"));
           para.put("channel", order.optString("channel"));

           TransactionStatus status = getTransctionStatus(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
           JSONObject printBuffer=null;
           try
           {
               printBuffer=takeOrders(tenancyId, storeId, para);
               transactionManager.commit(status);
           }
           catch (Exception e)
           {
               logger.error("批量取单失败:" + para );
               transactionManager.rollback(status);
           }

           if(null!=printBuffer){
               printOrderBuffer(tenancyId,storeId,printBuffer);
           }

       }
        return null;
    }

    @Override
    @Transactional(isolation = Isolation.SERIALIZABLE)
    public JSONObject createPosBillByOrder(String tenentid, int storeid, JSONObject para) throws Exception {
    	logger.info("订单转账单开始："+para);    	

    	String billState = ORDER_STATE_COMPLETE;//订单状态  默认成功 10
    	if(para.containsKey("oper") && para.optString("oper").equals("add_modify")){
    		billState = ORDER_STATE_EXCEPTION_COMPLETE; //订单状态，:异常订单完成 12
    	}

    	// 查询门店业态,判断业态
        JSONObject organ = ordersDao.getOrganInfo(tenentid, storeid);
        if (organ == null || organ.isEmpty()) {
            throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_ORGAN);
        }
        if (!ordersDao.checkReportDate(tenentid, storeid, para.optString("report_date"))) {
            throw SystemException.getInstance(PosErrorCode.CHECK_REPORT_DATE_ERROR);
        }
        
        StringBuffer sqlBuf = new StringBuffer();
        
        int unmatchNum = 0;//未匹配菜品数量
        int paymentNum = 0;//订单支付条数
        String item_menu_id = "";//餐谱ID

        sqlBuf.append("with t1 as ( \n");
        sqlBuf.append("SELECT COUNT(*) as unmatchNum from public.cc_order_item cci where cci.order_code = '"+para.optString("order_code")+"' and (cci.item_id = 0 or cci.item_id is null or cci.unit_id = 0 or cci.unit_id is null)), \n");
        sqlBuf.append("t2 as (SELECT COUNT(*) as paymentNum FROM public.cc_order_repayment ccr WHERE order_code = '"+para.optString("order_code")+"'), \n");
        sqlBuf.append("t3 as (SELECT item_menu_id from public.hq_item_menu_organ where tenancy_id = '"+tenentid+"' and store_id = "+storeid+") \n");
        sqlBuf.append("SELECT * from t1,t2,t3 \n");
        
        List<JSONObject> resultList = ordersDao.query4Json(tenentid, sqlBuf.toString());
        unmatchNum = resultList.get(0).optInt("unmatchnum");
        paymentNum = resultList.get(0).optInt("paymentnum");
        item_menu_id = resultList.get(0).optString("item_menu_id");
        
        logger.info("信息查询："+sqlBuf.toString());    	
        logger.info("未匹配菜品数量："+unmatchNum);    	
        
        //有未匹配菜品
        String not_exits_mapping_items="";
        String not_exits_items="";
        if(unmatchNum > 0){
            billState = ORDER_STATE_EXCEPTION;//订单状态，错误订单修正 11
            sqlBuf.setLength(0);
            sqlBuf.append("SELECT cci.item_name from public.cc_order_item cci where cci.order_code = '"+para.optString("order_code")+"' and (cci.item_id = 0 or cci.item_id is null or cci.unit_id = 0 or cci.unit_id is null )");
            List<JSONObject> itemList = ordersDao.query4Json(tenentid, sqlBuf.toString());
            if(itemList.size()>0){
                StringBuffer itemNames = new StringBuffer();
                for(JSONObject item:itemList){
                    itemNames.append(item.optString("item_name")+"、");
                }
                if(itemNames !=null){
                    not_exits_mapping_items = itemNames.substring(0,itemNames.length()-1);
                }
                logger.info("localserver映射关系为0的菜品 ：：： "+not_exits_mapping_items);
            }
        }else{
        	if(!para.containsKey("oper")){
                sqlBuf.setLength(0);
                sqlBuf.append("SELECT item_id, item_name, unit_id, unit_name FROM cc_order_item WHERE item_id NOT IN (( SELECT cci.item_id FROM cc_order_item cci INNER JOIN hq_item_info hii ON cci.item_id = hii. ID WHERE cci.order_code = '"+para.optString("order_code")+"' ) INTERSECT ( SELECT cci.item_id FROM cc_order_item cci INNER JOIN hq_item_unit hiu ON cci.item_id = hiu.item_id WHERE cci.order_code = '"+para.optString("order_code")+"' )) AND order_code = '"+para.optString("order_code")+"' \n");
                List<JSONObject> itemList = ordersDao.query4Json(tenentid, sqlBuf.toString());
                if(itemList.size()>0){
                    StringBuffer itemNames = new StringBuffer();
                    String item_name ="";
                    for(JSONObject item:itemList){
                        itemNames.append(item.optString("item_name")+"、");
                    }
                    if(itemNames !=null){
                        item_name = itemNames.substring(0,itemNames.length()-1).toString();
                        not_exits_items += itemNames.toString();
                    }
                    logger.info("localserver不存在的菜品 ：：： "+item_name);
                    //throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_DISH_ITEM_UNIT).set("{0}",item_name);
                }

                sqlBuf.setLength(0);
                sqlBuf.append("SELECT item_id, item_name, unit_id, unit_name FROM cc_order_item_details WHERE item_id NOT IN (( SELECT cci.item_id FROM cc_order_item_details cci INNER JOIN hq_item_info hii ON cci.item_id = hii. ID WHERE cci.order_code = '"+para.optString("order_code")+"' ) INTERSECT ( SELECT cci.item_id FROM cc_order_item_details cci INNER JOIN hq_item_unit hiu ON cci.item_id = hiu.item_id WHERE cci.order_code = '"+para.optString("order_code")+"' )) AND order_code = '"+para.optString("order_code")+"' \n");
                List<JSONObject> itemdetails = ordersDao.query4Json(tenentid, sqlBuf.toString());
                if(itemdetails.size()>0){
                    StringBuffer itemNames = new StringBuffer();
                    String item_name ="";
                    for(JSONObject item:itemList){
                        itemNames.append(item.optString("item_name")+"、");
                    }
                    if(itemNames !=null){
                        item_name = itemNames.substring(0,itemNames.length()-1).toString();
                        not_exits_items += itemNames.toString();
                    }
                    logger.info("localserver不存在的菜品明细 ：：： "+item_name);
                    //throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_DISH_ITEM_UNIT).set("{0}",item_name);
                }
                if(!not_exits_items.equals("")){
                    billState = ORDER_STATE_EXCEPTION;//订单状态，错误订单修正 11
                    not_exits_items = not_exits_items.substring(0,not_exits_items.length()-1).toString();
                }
        	}
        }
        
        //账单号
		String bill_num = ParamUtil.getStringValueByObject(para, "bill_num");
		String nserial_num="";
        try {
        	if(StringUtils.isEmpty(bill_num)){
	            // 生成新的账单号
	            JSONObject object = new JSONObject();
	            object.element("store_id", storeid);
	            Date report_date = ParamUtil.getDateValue(para, "report_date", false, null);
	            object.element("busi_date", report_date);
                bill_num = codeService.getCode(tenentid, Code.POS_BILL_CODE, object);// 调用统一接口来实现
    			if(para.optString("pos_num").equals(Constant.WM_POS_NUM)){//外卖班次下默认机器台
    				object.put("pos_num", Constant.WM_SERIAL_NUM);//外卖生产序列号前缀1000
    			}else{
    				object.put("pos_num", para.optString("pos_num"));
    			}
    			nserial_num = codeService.getCode(tenentid, Code.POS_RECORD_CODE, object); // 调用统一接口来实现
            }else {
			    logger.info("订单转账单（重新接收）原账单号:"+bill_num+"::::::: nserial_num: "+nserial_num);
            }
		}
		catch (Exception e)
		{
			throw new SystemException(PosErrorCode.GET_BILLNUM_SERVICE_ERROR);
		}
        logger.info("账单:bill_num："+bill_num+" tenancy_id:"+tenentid+" store_id:"+storeid);
        String currentTime = DateUtil.getNowDateYYDDMMHHMMSS();
        //外卖餐盒开关 1:开;0:关 全渠道 参数值为0  则 订单菜品 不包含餐盒 ， 门店用新的逻辑处理 ，其他情况都按菜品有餐盒的 
        String wm_meals_default_switch = OrderUtil.getSysPara(tenentid, storeid, "wm_meals_default_switch");

        //订单转账单
        //主档  delivery_party 2自配送 1平台送
        sqlBuf.setLength(0);    
        sqlBuf.append("insert into pos_bill( \n");
        sqlBuf.append("tenancy_id,store_id,table_code, guest, \n");//商户号  机构ID 桌位号/取餐号  就餐人数
        sqlBuf.append("opentable_time, payment_time, service_id, service_amount, order_num, \n");//开台时间  结账时间  基本服务费ID  服务费金额  订单号
        if(wm_meals_default_switch.equals("0")){
        	sqlBuf.append("subtotal,package_box_fee,\n");
        }else{
        	sqlBuf.append("subtotal,\n");
        }
        sqlBuf.append("bill_amount, payment_amount,discount_amount,discountr_amount, \n");//菜目合计  账单金额  应付金额  折扣金额  折让金额
        sqlBuf.append("discountk_amount, average_amount,discount_mode_id,sale_mode,source, \n");//优惠金额  人均消费  折扣方式ID  销售模式  渠道
        sqlBuf.append("remark, shop_real_amount,total_fees,platform_charge_amount,settlement_type, \n");//备注  商家实收  手续费合计  第三方平台优惠金额  线上|线下付款
        sqlBuf.append("bill_taste,item_menu_id,difference,bill_property, \n");//整单备注   餐谱ID  付款差额/找零金额   账单属性
        sqlBuf.append("bill_num,serial_num,report_date,open_pos_num,pos_num,waiter_num, \n");//账单编号  报表日期  开台机台  结账机台  服务员
        sqlBuf.append("open_opt,cashier_num,shift_id,payment_state,maling_amount, \n");//开台操作人  结账操作人  班次ID  付款状态  抹零金额
        sqlBuf.append("single_discount_amount,discount_rate,upload_tag,return_amount \n");//单品折扣金额  折扣率  上传标记
        sqlBuf.append(") \n");
        sqlBuf.append("SELECT cc.tenancy_id,cc.store_id,cc.table_code,CAST(cc.deinner_number as INTEGER) as guest, \n");
        sqlBuf.append("'"+currentTime+"' as opentable_time,'"+currentTime+"' as payment_time,cc.meals_id as service_id,cc.shop_delivery_fee as service_amount,cc.order_code as order_num, \n");
        if(wm_meals_default_switch.equals("0")){
        	sqlBuf.append("cc.product_org_total_fee as subtotal,cc.package_box_fee, \n");
        }else{
        	sqlBuf.append("(cc.product_org_total_fee+cc.package_box_fee) as subtotal, \n");
        }
        sqlBuf.append("(cc.product_org_total_fee+cc.package_box_fee+cc.shop_delivery_fee) as bill_amount,cc.shop_real_amount as payment_amount, \n");
        sqlBuf.append(" CASE  when  cc.delivery_party = 1 then  (cc.product_org_total_fee+cc.package_box_fee - cc.shop_real_amount) else  (cc.product_org_total_fee+cc.package_box_fee+cc.shop_delivery_fee - cc.shop_real_amount) end as discount_amount, \n");
        sqlBuf.append("cc.discountr_amount, \n");
        sqlBuf.append(" CASE  when  cc.delivery_party = 1 then  (cc.product_org_total_fee+cc.package_box_fee - cc.shop_real_amount) else (cc.product_org_total_fee+cc.package_box_fee+cc.shop_delivery_fee - cc.shop_real_amount)  end as discountk_amount, \n");
        sqlBuf.append("cc.shop_fee/CAST(COALESCE(cc.deinner_number,'1') as NUMERIC) as average_amount,cc.discount_mode_id,(case when order_type='ZT01' then 'WD02' when order_type='WM02' then 'WS03' when order_type='DN03' then 'TS01' end) as sale_mode,chanel as source, \n");
        sqlBuf.append("case when cc.remark is null then '(期望送达时间:'||cc.send_time||')'   when cc.remark ='' then '(期望送达时间:'||cc.send_time||')' when cc.remark is not null then  cc.remark ||'(期望送达时间:'||cc.send_time||')' end as remark , \n");
    	sqlBuf.append("cc.shop_real_amount as shop_real_amount,cc.total_fees,cc.platform_charge_amount,cc.settlement_type, \n");
        sqlBuf.append("cc.remark as bill_taste,'"+item_menu_id+"' as item_menu_id,case when "+paymentNum+">0 then 0 else cc.shop_fee end as difference,case when "+paymentNum+">0 then 'CLOSED' else 'OPEN'  end as bill_property, \n");
        sqlBuf.append("'"+bill_num+"' bill_num,'"+nserial_num+"' serial_num,'"+para.optString("report_date")+"' as report_date,'"+para.optString("pos_num")+"' as open_pos_num,'"+para.optString("pos_num")+"' as pos_num,'"+para.optString("opt_num")+"' as waiter_num, \n");
        sqlBuf.append("'"+para.optString("opt_num")+"' as open_opt,'"+para.optString("opt_num")+"' as cashier_num,'"+para.optString("shift_id")+"' as shift_id,'"+Constant.PAYMENT_STATE_COMPLETE+"' as payment_state,0 as maling_amount,  \n");
        sqlBuf.append("0 as single_discount_amount,100 as discount_rate,0 as upload_tag,0  \n");
        sqlBuf.append("from cc_order_list cc  WHERE cc.order_code = '"+para.optString("order_code")+"'\n");        
        ordersDao.execute(tenentid, sqlBuf.toString());
        
        
        //明细  添加插入item_serial,assist_item_id ,item_timeitem_remark
        sqlBuf.setLength(0);    
        sqlBuf.append("insert into pos_bill_item( \n");
        sqlBuf.append("tenancy_id,store_id,item_id,item_name, \n");//商户号  机构ID  任务ID  菜品ID  菜品名称
        sqlBuf.append("item_unit_id,item_unit_name,item_count,item_amount,real_amount,  \n");//规格ID  规格名称  菜品数量  菜品金额  应付金额
        sqlBuf.append("discount_amount,single_discount_amount,discountr_amount,discount_state,discount_rate, \n");//折扣金额  抹零金额  折让金额  折扣状态  折扣率
        sqlBuf.append("discount_mode_id,remark,sale_mode,single_amount,origin_item_price, \n");//折扣方式  菜品备注  销售模式  单品折扣金额  原菜品单价
        sqlBuf.append("bill_num,report_date,item_shift_id,item_mac_id,opt_num, \n");//账单编号  报表日期  点菜班次  点菜机台号  点菜操作人
        sqlBuf.append("item_price,item_property,waitcall_tag,net_income_amount,upload_tag,item_serial,assist_item_id,item_time,saas_item_price,saas_item_name,saas_item_num,saas_unit_name,item_num,  \n");//菜品单价  菜品属性（单品:SINGLE,套餐:SETMEAL,套餐明细:MEALLIST） 等叫标记    菜品实（净）收  上传标记
        sqlBuf.append("item_taste,item_class_id  ) \n");//口味备注 、菜品类别
        sqlBuf.append("SELECT cci.tenancy_id,cci.store_id,cci.item_id,cci.item_name, \n");
        sqlBuf.append("unit_id as item_unit_id,case when cci.unit_name is null or cci.unit_name='' then cci.saas_unit_name else   cci.unit_name end  as item_unit_name,cci.number as item_count,cci.product_fee as item_amount,cci.net_income_amount as real_amount,  \n");
        sqlBuf.append("( cci.product_fee -cci.net_income_amount ) as discount_amount,cci.single_discount_amount,cci.discountr_amount,cci.discount_state,cci.discount_rate, \n");
        sqlBuf.append("cci.discount_mode_id,cci.remark as remark,(case when order_type='ZT01' then 'WD02' when order_type='WM02' then 'WS03' when order_type='DN03' then 'TS01' end) as sale_mode,cci.single_discount_amount as single_amount,cci.price as origin_item_price, \n");
        sqlBuf.append("'"+bill_num+"' as bill_num,'"+para.optString("report_date")+"' as report_date,'"+para.optString("shift_id")+"' as item_shift_id,'"+para.optString("pos_num")+"' as item_mac_id,'"+para.optString("opt_num")+"' as opt_num, \n");
        sqlBuf.append("cci.price as item_price,CASE when (select count(*) from cc_order_item_details ccid where ccid.order_code = cci.order_code and ccid.group_index = cci.group_index  ) >0 then 'SETMEAL' else 'SINGLE' end as item_property,'0' as waitcall_tag,cci.net_income_amount,0 as upload_tag,cci.group_index,0,'"+para.optString("single_time")+"',cci.saas_item_price,cci.saas_item_name,cci.saas_item_num,cci.saas_unit_name,cci.saas_item_num, \n");
        sqlBuf.append("case when cci.remark is null  then ( SELECT string_agg(oit.item_remark,' ')  from cc_order_item_taste  oit where oit.order_code=cci.order_code and oit.group_index = cci.group_index and oit.item_id =cci.item_id and oit.type in ('KW02','ZF01') ) else cci.remark end  as item_taste, ");//口味备注
        sqlBuf.append(" (SELECT hhi.item_class from hq_item_info hhi where hhi.id = cci.item_id ) as item_class_id ");//菜品类别加的
        sqlBuf.append("FROM cc_order_item cci INNER JOIN cc_order_list cc on cc.order_code = cci.order_code WHERE cci.order_code = '"+para.optString("order_code")+"' \n");
        ordersDao.execute(tenentid, sqlBuf.toString());
        
        
        //套餐明细  添加插入item_serial,assist_item_id 
        sqlBuf.setLength(0);    
        sqlBuf.append("insert into pos_bill_item( \n");
        sqlBuf.append("tenancy_id,store_id,item_id,item_name, \n");//商户号  机构ID  任务ID  菜品ID  菜品名称
        sqlBuf.append("item_unit_id,item_unit_name,item_count,item_amount,real_amount,  \n");//规格ID  规格名称  菜品数量  菜品金额  应付金额
        sqlBuf.append("discount_amount,single_discount_amount,discountr_amount,discount_state,discount_rate, \n");//折扣金额  抹零金额  折让金额  折扣状态  折扣率
        sqlBuf.append("discount_mode_id,remark,sale_mode,single_amount,origin_item_price, \n");//折扣方式  菜品备注  销售模式  单品折扣金额  原菜品单价
        sqlBuf.append("bill_num,report_date,item_shift_id,item_mac_id,opt_num, \n");//账单编号  报表日期  点菜班次  点菜机台号  点菜操作人
        sqlBuf.append("item_price,item_property,waitcall_tag,net_income_amount,upload_tag,  \n");//菜品单价  菜品属性（单品:SINGLE,套餐:SETMEAL,套餐明细:MEALLIST） 等叫标记   菜品实（净）收  上传标记
        sqlBuf.append("setmeal_id ,item_serial,assist_item_id,item_time,item_num, \n");
        sqlBuf.append("item_taste,item_class_id  ) \n");//口味备注 、菜品类别
        sqlBuf.append("SELECT ccid.tenancy_id,ccid.store_id,ccid.item_id,ccid.item_name, \n");
        sqlBuf.append("ccid.unit_id as item_unit_id,ccid.unit_name as item_unit_name,ccid.number as item_count,ccid.product_fee as item_amount,ccid.net_income_amount as real_amount,  \n");
        sqlBuf.append("( ccid.product_fee -ccid.net_income_amount ) as discount_amount,'0' as single_discount_amount,'0' as discountr_amount,'Y' as discount_state,'100' as discount_rate, \n");
        sqlBuf.append("'7' as discount_mode_id,ccid.remark as remark,'WS03' as sale_mode,null as single_amount,ccid.price as origin_item_price,  \n");
        sqlBuf.append("'"+bill_num+"' as bill_num,'"+para.optString("report_date")+"' as report_date,'"+para.optString("shift_id")+"' as item_shift_id,'"+para.optString("pos_num")+"' as item_mac_id,'"+para.optString("opt_num")+"' as opt_num, \n");
        sqlBuf.append("ccid.price as item_price,'MEALLIST' as item_property,'0' as waitcall_tag,ccid.net_income_amount,0 as upload_tag, \n");
        sqlBuf.append("(SELECT item_id from cc_order_item cci WHERE cci.order_code =ccid.order_code and cci.group_index = ccid.group_index) as setmeal_id,ccid.group_index,ccid.id,'"+para.optString("single_time")+"',ccid.saas_item_num,  \n");
        sqlBuf.append("case when ccid.remark is null then ( SELECT string_agg(oit.item_remark,' ')  from cc_order_item_taste  oit where oit.order_code=ccid.order_code and oit.group_index = ccid.group_index and oit.item_id =ccid.item_id and oit.type in ('KW02','ZF01') ) else ccid.remark  end as item_taste, ");//口味备注
        sqlBuf.append(" (SELECT hhi.item_class from hq_item_info hhi where hhi.id = ccid.item_id ) as item_class_id ");//菜品类别加的
        sqlBuf.append("FROM cc_order_item_details ccid WHERE ccid.order_code = '"+para.optString("order_code")+"' \n");
        ordersDao.execute(tenentid, sqlBuf.toString());

        //插入做法
        sqlBuf.setLength(0);
        sqlBuf.append("insert into pos_zfkw_item(tenancy_id,store_id,bill_num,report_date,item_id,pos_num,type,last_updatetime,zfkw_id,zfkw_name,amount,print_id) \n");
        sqlBuf.append(" SELECT cci.tenancy_id,cci.store_id,'"+bill_num+"','"+para.optString("report_date")+"',cci.item_id,'"+para.optString("pos_num")+"','METHOD' as type,'"+currentTime+"',oit.taste_method_id  as zfkw_id,oit.item_remark as zfkw_name,CASE WHEN him.makeup_way = 'ADD' THEN oit.proportion_money WHEN him.makeup_way = 'MULTI' THEN cci.price * oit.proportion_money END AS amount ,0 as print_id  from cc_order_item_taste   oit,cc_order_item cci,  hq_item_method him where oit.order_code=cci.order_code and oit.group_index = cci.group_index and oit.item_id =cci.item_id and oit.type='ZF01' AND him.item_id = oit.item_id  AND him.id = oit.taste_method_id AND (select count(*) from cc_order_item_details ccid where ccid.order_code = cci.order_code and ccid.group_index = cci.group_index  )<=0" );
        ordersDao.execute(tenentid, sqlBuf.toString());

        sqlBuf.setLength(0);
        sqlBuf.append("insert into pos_zfkw_item(tenancy_id,store_id,bill_num,report_date,item_id,pos_num,type,last_updatetime,zfkw_id,zfkw_name,amount,print_id) \n");
        sqlBuf.append(" SELECT ccid.tenancy_id,ccid.store_id,'"+bill_num+"','"+para.optString("report_date")+"',ccid.item_id,'"+para.optString("pos_num")+"','METHOD' as type,'"+currentTime+"',oit.taste_method_id  as zfkw_id,oit.item_remark as zfkw_name,CASE WHEN him.makeup_way = 'ADD' THEN oit.proportion_money WHEN him.makeup_way = 'MULTI' THEN ccid.price * oit.proportion_money END AS amount ,0 as print_id  from cc_order_item_taste   oit,cc_order_item_details ccid,  hq_item_method him  where oit.order_code=ccid.order_code and oit.group_index = ccid.group_index and oit.item_id =ccid.item_id and oit.type='ZF01' AND him.item_id = oit.item_id  AND him.id = oit.taste_method_id ");
        ordersDao.execute(tenentid, sqlBuf.toString());
        
        //支付方式
        if(paymentNum>0){ //未保存支付名称
        	if(ordersDao.existPaymentWay(tenentid, storeid,para.optString("order_code"))){
	        	sqlBuf.setLength(0);    
	        	sqlBuf.append("insert into pos_bill_payment( \n");
	        	sqlBuf.append("tenancy_id,store_id,bill_num,jzid,amount, \n");//商户号  机构ID  账单编号  付款方式ID  付款金额
	        	sqlBuf.append("NUMBER,last_updatetime,rate,currency_amount,upload_tag, \n");//付款号码  操作时间  换算比率  本币金额  上传标记
	        	sqlBuf.append("bill_code,remark,payment_state,fee,fee_rate, \n");//交易流水号  备注  付款状态  手续费  手续费比率
	        	sqlBuf.append("report_date,shift_id,pos_num,cashier_num,shop_real_amount, \n");//报表日期  班次ID  收银机台号  收银员  商家实（净）收  
	        	sqlBuf.append("count,type,name,table_code   \n");//付款数量   付款方式类型
	        	sqlBuf.append(") \n");
	        	sqlBuf.append("SELECT ccr.tenancy_id,ccr.store_id,'"+bill_num+"' as bill_num,ccr.payment_id as jzid,ccr.shop_real_amount as amount, \n");
	        	sqlBuf.append("ccr.pay_no as NUMBER,now() as last_updatetime,ccr.rexchange_rate as rate,ccr.shop_real_amount as currency_amount,ccr.local_currency as upload_tag, \n");
	        	sqlBuf.append("ccr.third_bill_code as bill_code,ccr.remark,'"+Constant.PAYMENT_STATE_COMPLETE+"' as payment_state,ccr.fee,ccr.rate as fee_rate, \n");
	        	sqlBuf.append("'"+para.optString("report_date")+"' as report_date,'"+para.optString("shift_id")+"' as shift_id,'"+para.optString("pos_num")+"' as pos_num,'"+para.optString("opt_num")+"' as cashier_num,ccr.shop_real_amount, \n");
	        	sqlBuf.append("1 as count,pw.payment_class,pw.payment_name1 as name, \n");
                sqlBuf.append(" (select cc.table_code from cc_order_list cc where cc.order_code='"+para.optString("order_code")+"') as table_code \n");
	        	sqlBuf.append("FROM public.cc_order_repayment ccr INNER JOIN payment_way pw on  pw.id =ccr.payment_id where ccr.order_code = '"+para.optString("order_code")+"' \n");
	            ordersDao.execute(tenentid, sqlBuf.toString());
        	}else{
        		throw SystemException.getInstance(PosErrorCode.NOT_EXIST_JZID);
        	}
        }
        
        //更新订单状态
//        sqlBuf.setLength(0);    
//        sqlBuf.append("update cc_order_list set receive_time=now(),take_time=now(),receive_time_qd=now(),receive_time_finish=now(),finish_time=now(),order_state ="+ORDER_STATE_COMPLETE+" where order_code = '"+para.optString("order_code")+"'");        
//        ordersDao.execute(tenentid, sqlBuf.toString());
        
        // 获取订单状态
        OrderState orderState = ordersDao.getOrderStateByOrderCode(tenentid, storeid, para.optString("order_code"));
        orderState.setBill_num(bill_num);
        para.put("invoice_amount", orderState.getActual_pay());
        if (null==orderState) {
            throw SystemException.getInstance(PosErrorCode.ORDER_NOT_EXISTS_ERROR);
        }

        if(billState.equals(ORDER_STATE_EXCEPTION)){//菜品不存在映射关系
            //更新订单状态
            OrderUtil.changeOrderState(tenentid, storeid, orderState, billState, NOTIFY_ALL);//异常状态
        }else{
            //发送接收订单状态
            OrderUtil.changeOrderState(tenentid,storeid,orderState,ORDER_STATE_RECEIVE,NOTIFY_SAAS);//04接收订单
            
            orderState.setPayment_state(Constant.PAYMENT_STATE_COMPLETE);//支付状态完成
            logger.info("订单状态："+billState);    	
            //更新订单状态
            OrderUtil.changeOrderState(tenentid, storeid, orderState, billState, NOTIFY_ALL);//10 完成 、12修复完成
        }
        
        if(billState.equals(ORDER_STATE_EXCEPTION) ){
        	
            // 发送消息
            List<String> msgList = new ArrayList<String>();
            msgList.add(para.optString("order_code"));
            JSONObject msg = new JSONObject();
            msg.put("order_state", ORDER_STATE_EXCEPTION);
            msg.put("order_code", msgList);

            // 响铃提醒
            List<JSONObject> cometList = new ArrayList<JSONObject>();
            cometList.add(msg);
            Data cometData = new Data();
            cometData.setTenancy_id(tenentid);
            cometData.setStore_id(storeid);
            cometData.setType(Type.ORDER);
            cometData.setOper(Oper.add);
            cometData.setSuccess(true);
            cometData.setData(cometList);

            //POS用到UDP
            String port = new CometDataNoticeClientRunnable(cometData).getPort();
            SendUdpToPos.sendMessagePos(JSONObject.fromObject(cometData).toString(), port);
            logger.info("----推送消息：" + JSONObject.fromObject(cometData).toString() + ", 端口号：" + port);
        }
        
//        if(checkBillState){                    
//            orderState.setPayment_state(Constant.PAYMENT_STATE_COMPLETE);
//
//            OrderUtil.changeOrderState(tenentid, storeid, orderState, ORDER_STATE_COMPLETE, NOTIFY_ALL);
//        }else {
//        	//更改订单状态
//            OrderUtil.changeOrderState(tenentid,storeid,orderState,ORDER_STATE_EXCEPTION,NOTIFY_SAAS);
//        }


        //----增加KVS记录------
        StringBuilder qureyOrganSql = new StringBuilder("select format_state from organ where tenancy_id=? and id=?");
        List<JSONObject> formatStateList = ordersDao.query4Json(tenentid,qureyOrganSql.toString(), new Object[]{tenentid, storeid });
        String formatState = null;
        if (formatStateList!=null && formatStateList.size()>0)
        {
            formatState = formatStateList.get(0).getString("format_state");
        }
        if ("2".equals(formatState) && !"".equals(bill_num) )
        {
            //生成KVS记录
            posPaymentService.writeKvsBill(tenentid, storeid,bill_num,para.getString("report_date"));
        }
        //----增加KVS记录------

        if(billState.equals( ORDER_STATE_EXCEPTION_COMPLETE)){
        	logger.info("重新接受订单异常菜品完成向POS前端发送消息start");
        	List<JSONObject> noticeList = new ArrayList<JSONObject>();
            JSONObject noticeJson = new JSONObject();
            noticeJson.put("message", "菜品映射已完成，异常订单已成功入账");
            noticeList.add(noticeJson);
            Comet4jUtil.comet4J(noticeList, Type.DISH_MODIFY_NOTIFY, Oper.notice);
            logger.info("重新接受订单异常菜品完成向POS前端发送消息end");
        	
        	return null;
        }else{
        	if(billState.equals( ORDER_STATE_EXCEPTION)){//异常单据
        		para.put("is_exception_print",true);
        		para.put("not_exits_mapping_items",not_exits_mapping_items);//未映射菜品
                para.put("not_exits_items",not_exits_items);//本地不存在菜品
        		para.put("isPrint","N");//是否打印厨打和点菜清单标记   Y打印    N不打印
        		para.put("is_printbill",false);

        	}else{//正常单据
        		para.put("is_exception_print",false);
        		para.put("isPrint","Y");//是否打印厨打和点菜清单标记   Y打印    N不打印
        		para.put("is_printbill",true);
        	}
            return this.getPrintMsg(tenentid,storeid,para,bill_num);
        }


    }
    
    private JSONObject getPrintMsg(String tenentid, int storeid, JSONObject para,String bill_num)throws Exception {
    	JSONObject printBuffer = new JSONObject();
    	
    	PosBill order = ordersDao.getOrderByOrdercode(tenentid, storeid, para.optString("order_code"));
        if(order == null){
        	throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_BILL);
        }
    	
        StringBuilder sql = new StringBuilder("SELECT array_to_string (ARRAY(select pbi.rwid from pos_bill_item pbi where pbi.bill_num=? order by rwid),',') as rwids");
        List<JSONObject> rwidsList = ordersDao.query4Json(tenentid, sql.toString(), new Object[]{ bill_num});
        String rwids =rwidsList.get(0).optString("rwids");
    	
        // 厨打
        JSONObject printJson = new JSONObject();
         
	    String mode ="0";// 0:下单  // 1：暂存
	    String billno = bill_num; // 账单编号
	    String isPrint = para.optString("isPrint");
	    String posNum = ParamUtil.getStringValueByObject(para, "pos_num", true, PosErrorCode.NOT_NULL_POS_NUM);// 收款机号
		String optNum = ParamUtil.getStringValueByObject(para, "opt_num", false, null);// 操作员编号
		Date reportDate = ParamUtil.getDateValueByObject(para, "report_date", true, PosErrorCode.NOT_NULL_REPORT_DATE);// 报表日期
		Integer shiftId = ParamUtil.getIntegerValueByObject(para, "shift_id", false, null);// 班次id
		
        printJson.put("tenancy_id", tenentid);         
        printJson.put("store_id", storeid);
        printJson.put("mode", mode);
        printJson.put("bill_num", billno);
        printJson.put("is_print", isPrint);
        printJson.put("rwids", rwids);
        printJson.put("pos_num", posNum);
        printJson.put("opt_num", optNum);
        printJson.put("report_date", DateUtil.format(reportDate));
        printJson.put("shift_id", shiftId);
         
        String formatState = "";
		String formatmode = "";

		JSONObject org = posDishDao.getOrganById(tenentid, storeid);

		if (null != org && !org.isEmpty())
		{
			formatState = org.optString("format_state");
		}

		formatmode = posDishDao.getSysParameter(tenentid, storeid, "ZCDCMS");
		 
 		if ("1".equals(formatState) && !"02".equals(formatmode))
 		{
 			String isPrintMenu = posDishDao.getSysParameter(tenentid, storeid, "is_print_menu");

 			if ("1".equals(isPrintMenu))
 			{
 				printJson.put("is_print_order", "Y");
 				printJson.put("print_type", "");
// 				printJson.put("order_remark", orderRemark);
 				printJson.put("mode", "0");
 				printJson.put("print_code", SysDictionary.PRINT_CODE_1103);
 			}
 		}
 		else
 		{
 			printJson.put("is_print_order", "N");
 		}
    	//add by shenzhanyu 根据渠道区分第三方外卖下单（order_type ：WM1205） 、微信下单（order_type ：WX1401）
		//标记第三方外卖
		 printJson.put("order_type", "WM1205");         
         // 缓冲厨打
         printBuffer.put("chefPrint", printJson);


         //打印通用参数
         JSONObject orderJSON = new JSONObject();
         orderJSON.put("report_date", para.optString("report_date"));
         orderJSON.put("shift_id", Integer.parseInt(para.optString("shift_id")));
         orderJSON.put("pos_num", para.optString("pos_num"));
         orderJSON.put("opt_num", para.optString("opt_num"));
         orderJSON.put("bill_num", bill_num);
         orderJSON.put("mode", "0");
         //打印电子发票参数
         orderJSON.put("order_num", order.getOrder_num());
         orderJSON.put("need_invoice", order.getNeed_invoice());
         orderJSON.put("channel", para.optString("channel"));
         orderJSON.put("invoice_amount", para.optString("invoice_amount"));

         //结账单
         orderJSON.put("is_printbill", para.optBoolean("is_printbill"));
         //打印外卖正常单标记
         orderJSON.put("is_order_print", !para.optBoolean("is_exception_print"));
         //打印外卖异常单标记
         orderJSON.put("is_exception_print", para.optBoolean("is_exception_print"));
         if(para.containsKey("is_exception_print") && para.optBoolean("is_exception_print")){//未映射菜品
             if(para.containsKey("not_exits_mapping_items") && !para.optString("not_exits_mapping_items").equals("")){
                 orderJSON.put("not_exits_mapping_items", para.optString("not_exits_mapping_items"));
             }else if(para.containsKey("not_exits_items") && !para.optString("not_exits_items").equals("")){
                 orderJSON.put("not_exits_items", para.optString("not_exits_items"));
             }
         }
         //外卖退款打印使用
         String refound_num="";
         if(para.containsKey("refound_num") && !"".equals(para.optString("refound_num"))){
        	 refound_num = para.optString("refound_num");
         }
         orderJSON.put("refound_num",refound_num);
         String  refound_bill_num="";
         if(para.containsKey("refound_bill_num") && !"".equals(para.optString("refound_bill_num"))){
        	 refound_bill_num = para.optString("refound_bill_num");
         }
         orderJSON.put("refound_bill_num", refound_bill_num);

        //取餐码
        if("1".equals(OrderUtil.getSysPara(tenentid,storeid,"isPrintTakeCode"))){
             orderJSON.put("qrcode_url",getTakeCodeUrl(para, order.getOrder_num()));
         }
        //取餐一维码
        orderJSON.put("third_order_code", order.getThird_order_code());
        buildOrderOneDimCode(orderJSON,tenentid ,storeid );
         //缓冲打印
         printBuffer.put("orderPrint", orderJSON);

         return printBuffer;
    }

    /**
     * 生成取餐码url
     * @param para
     * @param orderNum
     * @return
     */
    public String getTakeCodeUrl(JSONObject para, String orderNum)  {
        //取餐码
        JSONObject takeCode=new JSONObject();
        if(MT11_CHANNEL.equals(para.optString("channel"))){

            takeCode.put("platform","MEITUAN");
        }
        if(EL09_CHANNEL.equals(para.optString("channel"))){

            takeCode.put("platform","ELEME");
        }
        takeCode.put("platformOrderId", para.optString("third_order_code"));

        String qrcodeUrl = "";
        // 生成新的二维码文件名
        String fileName = orderNum + "@" + System.currentTimeMillis() + ".png";
        String strPhysicalPath = PathUtil.getWebRootPath() + File.separator;
        String strimgPath = "img/qr_code/takecode/";
        strPhysicalPath = strPhysicalPath + strimgPath;
        try {
            QrCodeUtils.encode(takeCode.toString(), strPhysicalPath+fileName);
            String webPathConst = com.tzx.framework.common.constant.Constant.getSystemMap().get("WEB_PATH");
            qrcodeUrl = webPathConst + strimgPath + fileName;
        } catch (Exception e) {
            logger.error(orderNum+" 取餐码生成失败",e);
        }

        return qrcodeUrl;
    }


    @Override
    public  JSONObject takeOrders(String tenentid, int storeid, JSONObject para) throws Exception {

        // 查询门店业态,判断业态
        JSONObject organ = ordersDao.getOrganInfo(tenentid, storeid);
        if (organ == null || organ.isEmpty()) {
            throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_ORGAN);
        }
        if (!ordersDao.checkReportDate(tenentid, storeid, para.optString("report_date"))) {
            throw SystemException.getInstance(PosErrorCode.CHECK_REPORT_DATE_ERROR);
        }


        //订单渠道
        String channel = para.optString("channel");
        //下单时渠道
        String dishChannel=channel;
        // 获取订单信息
        String orderCode = para.optString("order_code").intern();
        PosBill order = ordersDao.getOrderByOrdercode(tenentid, storeid, orderCode);
        if(order == null){
            throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_BILL);
        }
        
        //第三方   取单  调用订单转账单
        if(THIRD_CHANNEL.contains(channel)){//&&"1".equals(OrderUtil.getSysPara(tenentid,storeid,"order_translate_bill_mode"))
        	//return createPosBillByOrder(tenentid, storeid, para);
        	
        	//根据订单号判断账单是否存在 
        	JSONObject printInfo = null;
			if (order.getBill_num() != null && !order.getBill_num().equals("")) { //已经转成账单
				para.put("bill_num", order.getBill_num());
				printInfo = notExitsIxceptionIitems(tenentid, storeid, para);
			}else{
				printInfo = createPosBillByOrder(tenentid, storeid, para);
			}
        	if(printInfo!=null && !printInfo.isEmpty()){
        		Boolean is_exception_print = printInfo.getJSONObject("orderPrint").getBoolean("is_exception_print");
        		if(is_exception_print){//异常订单（菜品未映射）
                    if(printInfo.getJSONObject("orderPrint").containsKey("not_exits_mapping_items") && !printInfo.getJSONObject("orderPrint").optString("not_exits_mapping_items").equals("")){
                        printInfo.put("existsNotMappingItmes", true);
                    }else if(printInfo.getJSONObject("orderPrint").containsKey("not_exits_items") && !printInfo.getJSONObject("orderPrint").optString("not_exits_items").equals("")){
                        printInfo.put("existsNotItmes", true);
                    }
        			return printInfo;
        		}else{
        			return printInfo;
        		}
        	}
        }

        order.setIsprint("Y");
        order.setReport_date(para.optString("report_date"));
        order.setShift_id(para.optString("shift_id"));
        order.setItem_menu_id(organ.optString("item_menu_id"));
        order.setOpt_num(para.optString("opt_num"));
        order.setWaiter_num(para.optString("waiter_num"));
        order.setPos_num(para.optString("pos_num"));
        //菜品明细
        if(THIRD_CHANNEL.contains(channel)){
            dishChannel="MD01";
        }
        order.setItem(ordersDao.getOrderItemByOrdercode(tenentid, storeid, orderCode, dishChannel));
        //付款信息
        order.setPayment(ordersDao.getOrderPaymentByOrdercode(tenentid, storeid, orderCode));

        // 获取订单状态
        OrderState orderState = ordersDao.getOrderStateByOrderCode(tenentid, storeid, orderCode);
        if (null==orderState) {
            throw SystemException.getInstance(PosErrorCode.ORDER_NOT_EXISTS_ERROR);
        }

        //发送接收订单状态
        OrderUtil.changeOrderState(tenentid,storeid,orderState,ORDER_STATE_RECEIVE,NOTIFY_SAAS);

        OrderUtil.hook(channel);

        //开台(订单转账单)
        JSONObject openTableJson = new JSONObject();
        if ( "1".equals(organ.optString("format_state"))) { // 正餐
            openTableJson.put("mode", "0");
        }

        if ("2".equals(organ.optString("format_state")) || WM_CHANNEL.contains(channel) || THIRD_CHANNEL.contains(channel)) { // 快餐
            openTableJson.put("mode", "1");
        }
        String tableCode = "1".equals(OrderUtil.getSysPara(tenentid,storeid,"wechat_takenum_replace_tablecode"))?order.getTake_meal_number():order.getTable_code();

        openTableJson.put("report_date", order.getReport_date());
        openTableJson.put("shift_id", order.getShift_id());
        openTableJson.put("item_menu_id", order.getItem_menu_id());
        openTableJson.put("opt_num", order.getOpt_num());
        openTableJson.put("waiter_num", order.getWaiter_num());
        openTableJson.put("pos_num", order.getPos_num());
        openTableJson.put("table_code",tableCode);
        openTableJson.put("guest", null == order.getGuest() ? "1" : order.getGuest());
        openTableJson.put("preorderno", order.getOrder_num());
        openTableJson.put("sale_mode", order.getSale_mode());
        openTableJson.put("chanel", channel);

        if(order.getSend_time()!=null && !"".equals(order.getSend_time())){
            if(order.getRemark()!=null && !"".equals(order.getRemark())){
            	openTableJson.put("remark", order.getRemark()+"(期望送达时间:"+order.getSend_time()+")");
            }else{
            	openTableJson.put("remark","(期望送达时间:"+order.getSend_time()+")");
            }
        }
        
        openTableJson.put("shop_real_amount", order.getShop_real_amount());
        openTableJson.put("platform_charge_amount", order.getPlatform_charge_amount());
        openTableJson.put("service_id", order.getService_id());
        openTableJson.put("service_amount", order.getService_amount());
        //优惠
        openTableJson.put("discount_rate", order.getDiscount_rate());
        openTableJson.put("discountk_amount", order.getDiscount_amount());

        String discount_mode_id = order.getDiscount_mode_id(); // 折扣方式
        String consigner_phone = order.getConsigner_phone(); // 会员手机号
        String customer_id = order.getCustomer_id(); // 会员卡号
        /**
         *  微信渠道下单，并且折扣方式=6时，判断会员手机号和卡号要不为空
         *  不为空时，在下单前要绑定会员关系pos_bill_member
         */
        boolean isBindMember = false; // 是否绑定会员信息
        if(Constant.WX02_CHANNEL.equals(channel) && discount_mode_id != null && String.valueOf(SysDictionary.DISCOUNT_MODE_6).equals(discount_mode_id)){
            if(Tools.isNullOrEmpty(consigner_phone) || Tools.isNullOrEmpty(customer_id)){
                throw SystemException.getInstance(PosErrorCode.BILL_MEMBERCARD_NULL);
            }else{
                isBindMember = true;
            }
        }
        // 折扣方式由总部下发
        openTableJson.put("discount_mode_id", discount_mode_id);

        //结算方式
        openTableJson.put("settlement_type", order.getSettlement_type());


        List<JSONObject> openTableList = new ArrayList<JSONObject>();
        openTableList.add(openTableJson);

        Data openTableData = Data.get();
        openTableData.setTenancy_id(tenentid);
        openTableData.setStore_id(storeid);
        openTableData.setType(Type.OPEN_TABLE);
        openTableData.setData(openTableList);

        logger.info(">>>>>>>>>>订单【"+order.getOrder_num()+"】开台参数:"+openTableJson);
        List<JSONObject> openTableRetJson = posDishService.newOpenTable(openTableData, codeService, new JSONObject());


        String billNum = null;
        if (openTableRetJson == null || openTableRetJson.isEmpty() || (billNum = openTableRetJson.get(0).optString("bill_num").trim()).isEmpty()) {
            throw SystemException.getInstance("未正确获取账单编号，请重试！", PosErrorCode.OPEN_TABLE_ERROR);
        }
        order.setBill_num(billNum);
        orderState.setBill_num(billNum);

        /**
         * 是否绑定会员信息
         */
        if(isBindMember){
            // 绑定会员折扣信息
            JSONObject memberJson = new JSONObject();
            memberJson.put("report_date", order.getReport_date());
            memberJson.put("bill_num", order.getBill_num());
            memberJson.put("mobil", order.getConsigner_phone());
            memberJson.put("card_code", null);
            memberJson.put("customer_code", null);

            logger.info(">>>>>>>>>绑定会员折扣信息，会员参数："+memberJson.toString());
            memberDao.insertPosBillMember(tenentid, storeid, order.getBill_num(), DateUtil.parseDate(order.getReport_date()), SysDictionary.BILL_MEMBERCARD_HYJ01, null, null, order.getConsigner_phone(),DateUtil.currentTimestamp(),null,null,0d);
        }

        // 下单
        JSONObject orderDishJson = new JSONObject();

        orderDishJson.put("mode", "0");
        orderDishJson.put("isprint", order.getIsprint());
        orderDishJson.put("report_date", order.getReport_date());
        orderDishJson.put("shift_id", order.getShift_id());
        orderDishJson.put("pos_num", order.getPos_num());
        orderDishJson.put("opt_num", order.getOpt_num());
        orderDishJson.put("bill_num", order.getBill_num());
        orderDishJson.put("sale_mode", order.getSale_mode());
//        orderDishJson.put("table_code", order.getTable_code());
        orderDishJson.put("table_code", tableCode);
        orderDishJson.put("waiter_num", order.getWaiter_num());
        orderDishJson.put("bill_taste", order.getRemark());//整单备注
        orderDishJson.put("item", order.getItem());
        orderDishJson.put("chanel", dishChannel);

        //外卖渠道特殊处理
        if(THIRD_CHANNEL.contains(channel)){
            orderDishJson.put("dish_scale",2);
            orderDishJson.put("bill_scale",2);
        }

        List<JSONObject> orderDishList = new ArrayList<JSONObject>();
        orderDishList.add(orderDishJson);

        Data orderDishData = Data.get();
        orderDishData.setTenancy_id(tenentid);
        orderDishData.setStore_id(storeid);
        orderDishData.setType(Type.ORDERING);
        orderDishData.setData(orderDishList);
        orderDishData.setSource(SysDictionary.SOURCE_CC_ORDER);//订单来源，用来估清

        JSONObject printJson = new JSONObject();
        logger.info(">>>>>>>>>>订单【"+order.getOrder_num()+"】下单参数:"+orderDishJson);
        posDishService.newOrderDish(orderDishData, printJson);

        JSONObject printBuffer = new JSONObject();
		//标记第三方外卖
		if(THIRD_CHANNEL.contains(channel)){
			printJson.put("order_type", "WM1205");
		}else{
			//标记微信订单
			printJson.put("order_type", "WX1401");
		}
        // 缓冲厨打
        printBuffer.put("chefPrint", printJson);


        //打印通用参数
        JSONObject orderJSON = new JSONObject();
        orderJSON.put("report_date", order.getReport_date());
        orderJSON.put("shift_id", Integer.parseInt(order.getShift_id()));
        orderJSON.put("pos_num", order.getPos_num());
        orderJSON.put("opt_num", order.getOpt_num());
        orderJSON.put("bill_num", order.getBill_num());
        orderJSON.put("mode", "0");
        //打印电子发票参数
        orderJSON.put("order_num", order.getOrder_num());
        orderJSON.put("need_invoice", order.getNeed_invoice());
        orderJSON.put("channel", channel);
        orderJSON.put("invoice_amount", orderState.getActual_pay());
        orderJSON.put("is_order_print",true);


        //结账
        if (("1".equals(order.getIs_online_payment()) && "PLATFORM".equals(order.getSettlement_type()))//先付平台
                || ("RIDER".equals(order.getSettlement_type()) && "AUTOMATIC".equals(order.getCheck_mode()))//骑手自动
                || ("1".equals(order.getIs_online_payment()) && !Constant.THIRD_CHANNEL.contains(channel))//微信
                ) {

            JSONObject paymentJson = new JSONObject();

            paymentJson.put("report_date", order.getReport_date());
            paymentJson.put("shift_id", order.getShift_id());
            paymentJson.put("pos_num", order.getPos_num());
            paymentJson.put("opt_num", order.getOpt_num());
            paymentJson.put("bill_num", order.getBill_num());
            paymentJson.put("table_code", tableCode);
            paymentJson.put("payment_amount", order.getPayment_amount());
            paymentJson.put("difference", 0);
            paymentJson.put("sale_mode", order.getSale_mode());
            paymentJson.put("isprint_bill", order.getIsprint());

            //付款方式0:后付,1:先付
            paymentJson.put("is_online_payment", order.getIs_online_payment());

            //积分信息
            JSONObject credit=ordersDao.getOrderCredit(tenentid,storeid,orderCode);
            if(null!=credit){
                paymentJson.put("amount",credit.opt("amount"));
                paymentJson.put("credit",credit.opt("credit"));
                paymentJson.put("bill_code",credit.opt("bill_code"));
                paymentJson.put("consume_before_credit",credit.opt("consume_before_credit"));
                paymentJson.put("consume_after_credit",credit.opt("consume_after_credit"));
                paymentJson.put("customer_code",credit.opt("customer_code"));
                paymentJson.put("customer_name",credit.opt("customer_name"));
                paymentJson.put("mobil",credit.opt("mobil"));
                paymentJson.put("card_code",credit.opt("card_code"));
            }

            List<JSONObject> paymentList = new ArrayList<JSONObject>();
            paymentList.add(paymentJson);

            Data paymentData = Data.get();
            paymentData.setTenancy_id(tenentid);
            paymentData.setStore_id(storeid);
            paymentData.setType(Type.BILL_PAYMENT);
            paymentData.setData(paymentList);

            if (null != order.getPayment() && !order.getPayment().isEmpty()) {
                paymentJson.put("item", order.getPayment());
                Data paymentResult = Data.get();
                JSONObject resultJson = new JSONObject();
                try{
                    //新统一结账接口
                    posPaymentService.posBillPayment(paymentData, paymentResult, resultJson, false);
                }catch (SystemException se){
                	throw se;
                }catch (Exception e){
                    logger.error(">>>>>>>>>>订单【"+orderCode+"】付款失败",e);
                }
            } else {
//                throw SystemException.getInstance("先付订单未找到付款信息!", PosErrorCode.NOT_NULL_ITEM_LIST);
                logger.warn(">>>>>>>>>>订单【"+orderCode+"】先付订单未找到付款信息!");
            }

            //结账单
            orderJSON.put("is_printbill", true);
            orderState.setPayment_state(Constant.PAYMENT_STATE_COMPLETE);
            if (WM_CHANNEL.contains(channel)) {
                OrderUtil.changeOrderState(tenentid, storeid, orderState, ORDER_STATE_COMPLETE_PAYMENT, NOTIFY_SAAS);
            } else {
                OrderUtil.changeOrderState(tenentid, storeid, orderState, ORDER_STATE_COMPLETE, NOTIFY_ALL);
            }

            StringBuilder closeBill = new StringBuilder();
            closeBill.append("UPDATE pos_bill SET bill_property = 'CLOSED',payment_time ='"+DateUtil.getNowDateYYDDMMHHMMSS()+"',pos_num ='"+para.optString("pos_num")+"',cashier_num ='"+para.optString("opt_num")+"',shift_id ='"+para.optString("shift_id")+"', payment_state = '01', bill_amount = '" + order.getPayment_amount() + "', payment_amount = '" + order.getPayment_amount() + "', difference = '0' WHERE order_num = '" + orderCode+ "' AND bill_property<>'CLOSED';");
            if(!StringUtils.isEmpty(tableCode)) {
                closeBill.append("update pos_tablestate set state='FREE' where table_code='" + tableCode + "' and state<>'FREE';");
            }
            ordersDao.execute(tenentid, closeBill.toString());
            logger.info(order.getOrder_num()+"关闭账单和台位："+closeBill.toString());
        }

        //取餐一维码
        orderJSON.put("third_order_code", order.getThird_order_code());
        buildOrderOneDimCode(orderJSON,tenentid,storeid);
        //缓冲打印
        printBuffer.put("orderPrint", orderJSON);

        //重置同步订单状态和结算精度
        OrderUtil.unhook();
//        posDishService.upload(tenentid,storeid+"", "","", "", billNum);
        return printBuffer;
    }



    @Override
    public JSONObject takeExceptionOrders(String tenentid, int storeid, JSONObject para, JSONObject resultJson) {
        logger.info("异常订单取单:"+para);
        String orderCode = para.optString("order_code");
        // 获取订单状态
        OrderState orderState = null;
        JSONObject printBuffer = new JSONObject();
        //打印通用参数
        JSONObject orderJSON = new JSONObject();

        try {
            orderState = ordersDao.getOrderStateByOrderCode(tenentid, storeid, orderCode);
            //打印
            orderJSON.put("report_date", para.optString("report_date"));
            orderJSON.put("shift_id", Integer.parseInt(para.optString("shift_id")));
            orderJSON.put("pos_num", para.optString("pos_num"));
            orderJSON.put("opt_num", para.optString("opt_num"));
            orderJSON.put("bill_num", "");
            orderJSON.put("mode", "0");
            //打印电子发票参数
            orderJSON.put("order_num", orderState.getOrder_code());
            orderJSON.put("need_invoice", orderState.getNeed_invoice());
            orderJSON.put("channel", orderState.getChanel());
            orderJSON.put("invoice_amount", orderState.getActual_pay());
            orderJSON.put("is_exception_print",true);
            orderJSON.put("single_time", orderState.getSingle_time());
            //取餐码
            if("1".equals(OrderUtil.getSysPara(tenentid,storeid,"isPrintTakeCode"))){
                orderJSON.put("qrcode_url",getTakeCodeUrl(para, orderState.getOrder_code()));
            }
            //取餐一维码
            orderJSON.put("third_order_code", orderState.getThird_order_code());
            buildOrderOneDimCode(orderJSON,tenentid,storeid);
            //缓冲打印
            printBuffer.put("orderPrint", orderJSON);
            //更改订单状态
            OrderUtil.changeOrderState(tenentid,storeid,orderState,ORDER_STATE_EXCEPTION,NOTIFY_ALL);
        } catch (Exception e) {
            logger.error("更新订单状态失败!"+e);
        }
        return printBuffer;
    }

//    public  void upload(String tenantId,String store_id,String old_table_code,String new_table_code,String old_bill_num,String new_bill_num){
//        DataUploadRunnable r = new DataUploadRunnable(tenantId,store_id,old_table_code,new_table_code,old_bill_num,new_bill_num);
//        Thread thread  =  new Thread(r);
//        thread.start();
//    }


    @Override
    public JSONObject paymentOrders(String tenentid, int storeid, JSONObject para) throws Exception {
        if (!ordersDao.checkReportDate(tenentid, storeid, para.optString("report_date"))) {
            throw SystemException.getInstance(PosErrorCode.CHECK_REPORT_DATE_ERROR);
        }

        // 获取订单信息
        PosBill order = ordersDao.getOrderByOrdercode(tenentid, storeid, para.optString("order_code"));
        // 获取订单状态
        OrderState orderState = ordersDao.getOrderStateByOrderCode(tenentid, storeid, para.optString("order_code"));

        JSONObject printBuffer = new JSONObject();

        OrderUtil.hook(orderState.getChanel());

        // 获取账单信息
        StringBuilder billSql = new StringBuilder(
                "select b.bill_num,b.table_code,b.sale_mode,b.bill_amount,b.payment_amount,b.difference,sum(p.amount) as amount from pos_bill b left join cc_order_list o on b.store_id=o.store_id and b.bill_num=o.bill_num left join pos_bill_payment p on b.bill_num=p.bill_num and b.store_id=p.store_id where o.store_id=? and o.order_code=? group by b.id");
        List<JSONObject> billList = ordersDao.query4Json(tenentid, billSql.toString(), new Object[]
                {storeid, para.optString("order_code")});

        if (null == billList || billList.isEmpty()) {
            throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_BILL);
        }


        double paymentAmount = JsonUtils.getDouble(billList.get(0), "payment_amount");
        double amount = JsonUtils.getDouble(billList.get(0), "amount");
        double difference = JsonUtils.getDouble(billList.get(0), "difference");

        List<JSONObject> orderRepaymentList = new ArrayList<JSONObject>();

        for (Object obj : para.optJSONArray("item")) {
            List<PosBillPayment> paymentItemList = new ArrayList<PosBillPayment>();
            JSONObject json = JSONObject.fromObject(obj);
            PosBillPayment payItem = new PosBillPayment();
            payItem.setJzid(json.optString("id"));
            payItem.setAmount(json.optString("amount"));
            payItem.setCount(json.optString("count"));
            payItem.setCurrency_amount(json.optString("amount"));
            paymentItemList.add(payItem);

            amount = DoubleHelper.add(amount, Double.parseDouble(json.optString("amount")), 4);
            difference = DoubleHelper.sub(paymentAmount, amount, 4);

            JSONObject paymentJson = new JSONObject();

            paymentJson.put("report_date", para.optString("report_date"));
            paymentJson.put("shift_id", para.optString("shift_id"));
            paymentJson.put("pos_num", para.optString("pos_num"));
            paymentJson.put("opt_num", para.optString("opt_num"));
            paymentJson.put("bill_num", billList.get(0).optString("bill_num"));
            paymentJson.put("table_code", billList.get(0).optString("table_code"));
            paymentJson.put("sale_mode", billList.get(0).optString("sale_mode"));
            paymentJson.put("payment_amount", paymentAmount);
            paymentJson.put("difference", difference);
            paymentJson.put("table_code", "");
            paymentJson.put("number", "");
            paymentJson.put("isprint_bill", "Y");
            paymentJson.put("item", paymentItemList);

            List<JSONObject> paymentList = new ArrayList<JSONObject>();
            paymentList.add(paymentJson);

            Data paymentData = Data.get();
            paymentData.setTenancy_id(tenentid);
            paymentData.setStore_id(storeid);
            paymentData.setType(Type.PAYMENT);
            paymentData.setData(paymentList);

            Data paymentResult = Data.get();
            JSONObject resultJson = new JSONObject();
//			paymentService.posPayment(paymentData, paymentResult,resultJson);
            //新统一结账接口
            posPaymentService.posBillPayment(paymentData, paymentResult, resultJson, false);
            //打印参数
            JSONObject orderJSON = new JSONObject();
            orderJSON.put("report_date", para.optString("report_date"));
            orderJSON.put("shift_id",para.optString("shift_id"));
            orderJSON.put("pos_num", para.optString("pos_num"));
            orderJSON.put("opt_num", para.optString("opt_num"));
            orderJSON.put("bill_num", order.getBill_num());
            orderJSON.put("mode", "0");
            orderJSON.put("is_printbill", true);
            //打印电子发票参数
            orderJSON.put("order_num", order.getOrder_num());
            orderJSON.put("need_invoice", order.getNeed_invoice());
            orderJSON.put("channel", order.getSource());

            //取餐一维码
            orderJSON.put("third_order_code", order.getThird_order_code());
            buildOrderOneDimCode(orderJSON,tenentid,storeid);

            printBuffer.put("orderPrint", orderJSON);

           /* if (resultJson != null) {
                if ("Y".equalsIgnoreCase(resultJson.optString("is_print"))) {
                    List<Integer> printList = posPrintService.orderChef(resultJson.optString("tenancy_id"), resultJson.optString("bill_num"), resultJson.optInt("store_id"), "0");
//					posPrintService.orderPrint(resultJson.optString("tenancy_id"), resultJson.optString("bill_num"), resultJson.optInt("store_id"), printList);
                }
            }*/



/*            //付款信息
            JSONObject orderRepaymentJson = new JSONObject();
            orderRepaymentJson.put("payment_id", json.optString("id"));
            orderRepaymentJson.put("order_code", orderState.getOrder_code());
            orderRepaymentJson.put("pay_money", json.optString("amount"));
            orderRepaymentList.add(orderRepaymentJson);*/
        }

//		ordersDao.addOrderRepayment(tenentid, storeid, orderRepaymentList);//结账时同步订单会完成此操作

        if (difference <= 0) {
            orderState.setPayment_state(PAYMENT_STATE_COMPLETE);
            if (WM_CHANNEL.contains(orderState.getChanel())) {
                OrderUtil.changeOrderState(tenentid,storeid,orderState,ORDER_STATE_COMPLETE_PAYMENT, NOTIFY_SAAS);
            } else {
                OrderUtil.changeOrderState(tenentid,storeid,orderState,ORDER_STATE_COMPLETE, NOTIFY_ALL);
            }

        } else {
            orderState.setPayment_state(PAYMENT_STATE_ALREADY);
            OrderUtil.changeOrderState(tenentid,storeid,orderState,ORDER_STATE_RECEIVE, NOTIFY_NONE);
        }

        OrderUtil.unhook();

        return printBuffer;
    }

    @Override
    public JSONObject getQRCodeForPayment(String tenentid, int storeid, JSONObject para, String webRootPath) throws Exception {
//        if (!ordersDao.checkReportDate(tenentid, storeid, para.optString("report_date"))) {
//            throw SystemException.getInstance(PosErrorCode.CHECK_REPORT_DATE_ERROR);
//        }
//
//        // 获取订单信息
//        PosBill order = ordersDao.getOrderByOrdercode(tenentid, storeid, para.optString("order_code"));
//
//        JSONObject paymentJson = new JSONObject();
//
//        paymentJson.put("bill_num", order.getBill_num());
//        paymentJson.put("total_amount", order.getBill_amount());
//        paymentJson.put("pos_num", para.optString("pos_num"));
//        paymentJson.put("opt_num", para.optString("opt_num"));
//
//        paymentJson.put("report_date", para.optString("report_date"));
//        paymentJson.put("shift_id", para.optString("shift_id"));
//        paymentJson.put("jzid", para.optString("id"));
//        paymentJson.put("subject", para.optString("subject"));
//
//        List<JSONObject> paymentList = new ArrayList<JSONObject>();
//        paymentList.add(paymentJson);
//
//        Data paymentData = Data.get();
//        paymentData.setTenancy_id(tenentid);
//        paymentData.setStore_id(storeid);
//        paymentData.setData(paymentList);
//
//        List<JSONObject> paymentResultList = new ArrayList<JSONObject>();
//
//        if ("wechat_pay".equals(para.optString("payment_class"))) {
//            // 微信
//            paymentJson.put("remote_ip", InetAddress.getLocalHost().getHostAddress());
//            paymentData.setType(Type.PAYMENT_UNIFIEDORDER);
//            paymentData.setOper(Oper.init);
//            paymentResultList = paymentService.unifiedorder(paymentData, webRootPath);
//
//            if (null == paymentResultList || paymentResultList.size() == 0) {
//                throw SystemException.getInstance(PosErrorCode.ORDER_NETWORK_REQUEST_ERROR);
//            }
//            if (!paymentResultList.get(0).optBoolean("success")) {
//                throw SystemException.getInstance(PosErrorCode.ORDER_GET_WECHAT_PAY_QRCODE_ERROR).set("{0}", paymentResultList.get(0).optString("error_msg"));
//            }
//            return paymentResultList.get(0);
//        } else if ("ali_pay".equals(para.optString("payment_class"))) {
//            // 支付宝
//            paymentData.setType(Type.PAYMENT_PRECREATE);
//            paymentData.setOper(Oper.scan);
//            paymentResultList = paymentService.precreate(paymentData, webRootPath);
//
//            if (null == paymentResultList || paymentResultList.size() == 0) {
//                throw SystemException.getInstance(PosErrorCode.ORDER_NETWORK_REQUEST_ERROR);
//            }
//            if (!paymentResultList.get(0).optBoolean("success")) {
//                throw SystemException.getInstance(PosErrorCode.ORDER_GET_ALI_PAY_QRCODE_ERROR).set("{0}", paymentResultList.get(0).optString("error_msg"));
//            }
//            return paymentResultList.get(0);
//        } else {
//            throw SystemException.getInstance(PosErrorCode.NOT_EXIST_JZID);
//        }
    	return null;
    }

    @Override
    public JSONObject queryThirdPayment(String tenentid, int storeid, JSONObject para, String webRootPath) throws Exception {
    	JSONObject paymentResultJson = null;
//        if (!ordersDao.checkReportDate(tenentid, storeid, para.optString("report_date"))) {
//            throw SystemException.getInstance(PosErrorCode.CHECK_REPORT_DATE_ERROR);
//        }
//
//        // 获取订单信息
//        PosBill order = ordersDao.getOrderByOrdercode(tenentid, storeid, para.optString("order_code"));
//
//        JSONObject paymentJson = new JSONObject();
//
//        paymentJson.put("bill_num", order.getBill_num());
//        paymentJson.put("polling_flag", true);
//        paymentJson.put("pos_num", para.optString("pos_num"));
//        paymentJson.put("opt_num", para.optString("opt_num"));
//
//        paymentJson.put("report_date", para.optString("report_date"));
//        paymentJson.put("shift_id", para.optString("shift_id"));
//        paymentJson.put("jzid", para.optString("id"));
//
//        List<JSONObject> paymentList = new ArrayList<JSONObject>();
//        paymentList.add(paymentJson);
//
//        Data paymentData = Data.get();
//        paymentData.setTenancy_id(tenentid);
//        paymentData.setStore_id(storeid);
//        paymentData.setData(paymentList);
//
//        List<JSONObject> paymentResultList = new ArrayList<JSONObject>();
//
//        if ("wechat_pay".equals(para.optString("payment_class"))) {
//            // 微信
//            paymentData.setType(Type.PAYMENT_ORDERQUERY);
//            paymentData.setOper(Oper.query);
//            paymentResultList = paymentService.orderquery(paymentData, webRootPath);
//        } else if ("ali_pay".equals(para.optString("payment_class"))) {
//            // 支付宝
//            paymentData.setType(Type.PAYMENT_QUERY);
//            paymentData.setOper(Oper.query);
//            paymentResultList = paymentService.query(paymentData, webRootPath);
//        }
//    	
//        if (paymentResultList != null && paymentResultList.size() > 0) {
//            paymentResultJson = paymentResultList.get(0);
//            if (paymentResultJson.optBoolean("success") && "finished".equals(paymentResultJson.optString("status"))) {
//                JSONObject paymentOrdersJson = new JSONObject();
//
//                paymentOrdersJson.put("tenancy_id", tenentid);
//                paymentOrdersJson.put("store_id", storeid);
//                paymentOrdersJson.put("report_date", para.optString("report_date"));
//                paymentOrdersJson.put("shift_id", para.optString("shift_id"));
//                paymentOrdersJson.put("pos_num", para.optString("pos_num"));
//                paymentOrdersJson.put("opt_num", para.optString("opt_num"));
//                paymentOrdersJson.put("order_code", para.optString("order_code"));
//
//                JSONObject payItemJson = new JSONObject();
//                payItemJson.put("id", para.optString("id"));
//                payItemJson.put("count", 1);
//                payItemJson.put("amount", paymentResultJson.optDouble("total_amount"));
//
//                List<JSONObject> payItemList = new ArrayList<JSONObject>();
//                payItemList.add(payItemJson);
//                paymentOrdersJson.put("item", payItemList);
//
//                this.paymentOrders(tenentid, storeid, paymentOrdersJson);
//            }
//        }
//
////			
////			if (paymentResultJson.optBoolean("success") && "finished".equals(paymentResultJson.optString("status")))
////			{
////				
////				OrderState orderState = ordersDao.getOrderStateByOrderCode(tenentid, storeid, para.optString("order_code"));
////				String currentTime = DateUtil.getNowDateYYDDMMHHMMSS();
////				orderState.setOrder_state(Constant.ORDER_STATE_COMPLETE_PAYMENT);
////				orderState.setFinish_time(currentTime);
////				orderState.setReceive_time_finish(currentTime);
////				
////				List<JSONObject> orderRepaymentList = new ArrayList<JSONObject>();
////				JSONObject orderRepaymentJson = new JSONObject();
////				orderRepaymentJson.put("payment_id", para.optString("id"));
////				orderRepaymentJson.put("order_code", orderState.getOrder_code());
////				orderRepaymentJson.put("pay_money", order.getPayment_amount());
////				orderRepaymentList.add(orderRepaymentJson);
////				
////				if(orderRepaymentList.size()>0)
////				{
////					ordersDao.addOrderRepayment(tenentid, storeid, orderRepaymentList);
////					JSONObject orderJson = new JSONObject();
////					orderJson.put("order_code", orderState.getOrder_code());
////					orderJson.put("payment_state", Constant.PAYMENT_STATE_COMPLETE);
////					ordersDao.updateOrderInfo(tenentid, storeid, orderJson);
////				}
////
////				// 修改订单状态,并返回总部
////				if (ordersDao.updateOrderInfo(tenentid, storeid, JSONObject.fromObject(orderState)))
////				{
////					try
////					{
////						printOrders(tenentid, storeid, para.optString("report_date"), para.optInt("shift_id"), para.optString("pos_num"), para.optString("opt_num"), order.getBill_num(), Constant.ORDER_PRINT_CODE_1002);
////					}
////					catch (Exception e)
////					{
////						e.printStackTrace();
////					}
////					finally
////					{
////						List<OrderState> orderStateList = new ArrayList<OrderState>();
////						orderState.setOrder_repayment(orderRepaymentList);
////						orderStateList.add(orderState);
////						OrderUtil.sendOrderState(tenentid, storeid, Type.ORDER, Oper.update, orderStateList);
////					}
////				}
////			}

        return paymentResultJson;
    }

    @Override
    public void cancelOrders(String tenentid, int storeid, JSONObject para) throws Exception {
        // 获取订单状态
        OrderState orderState = ordersDao.getOrderStateByOrderCode(tenentid, storeid, para.optString("order_code"));
        if (orderState == null || !Constant.ORDER_STATE_TOSHOP.equals(orderState.getOrder_state())) {
            throw SystemException.getInstance(PosErrorCode.ORDER_STATE_NOT_NEW_ERROR);
        }
        String currentTime = DateUtil.getNowDateYYDDMMHHMMSS();

        JSONObject orderJson = new JSONObject();

        orderJson.put("order_code", orderState.getOrder_code());
        orderJson.put("receive_time", orderState.getReceive_time());
        orderJson.put("chanel", orderState.getChanel());
        orderJson.put("third_order_code", orderState.getThird_order_code());
        orderJson.put("order_state", Constant.ORDER_STATE_CANCEL);
        orderJson.put("payment_state", Constant.PAYMENT_STATE_REFUND);
        orderJson.put("cancle_name", para.optString("opt_num"));
        orderJson.put("cancellation_time", currentTime);
        orderJson.put("receive_time_cancellation", currentTime);

        JSONObject reasonJson = new JSONObject();
        reasonJson.put("reason_type", para.optString("reason_type"));
        reasonJson.put("type", "MD03");
        reasonJson.put("complaint_content", para.optString("complaint_content"));
        reasonJson.put("remark", "");
        reasonJson.put("complaints_time", currentTime);
        reasonJson.put("order_state", orderState.getOrder_state());

        List<JSONObject> reasonList = new ArrayList<JSONObject>();
        reasonList.add(reasonJson);

        orderJson.put("order_reason", reasonList);
		ordersDao.addOrderReasonDetail(tenentid, storeid, orderState.getOrder_code(), reasonList);


        List<JSONObject> orderList = new ArrayList<JSONObject>();
        orderList.add(orderJson);

        JSONObject resultJson = OrderUtil.sendOrderState(tenentid, storeid, Type.ORDER, Oper.cancle, orderList);
        if (0 == resultJson.optInt("code")) {
            ordersDao.updateOrderInfo(tenentid, storeid, orderJson);
        }else{
            throw SystemException.getInstance(PosErrorCode.ORDER_SEND_ORDER_STATE_ERROR);
        }


    }

    @Override
    public void cancelWxOrders(String tenentid, int storeid, JSONObject para) throws Exception {
        // 获取订单状态
        OrderState orderState = ordersDao.getOrderStateByOrderCode(tenentid, storeid, para.optString("order_code"));
        if (orderState == null ) {
            throw SystemException.getInstance(PosErrorCode.ORDER_STATE_NOT_NEW_ERROR);
        }
        String currentTime = DateUtil.getNowDateYYDDMMHHMMSS();

        JSONObject orderJson = new JSONObject();

        orderJson.put("order_code", orderState.getOrder_code());
        orderJson.put("receive_time", orderState.getReceive_time());
        orderJson.put("chanel", orderState.getChanel());
        orderJson.put("third_order_code", orderState.getThird_order_code());
        orderJson.put("order_state", Constant.ORDER_STATE_CANCEL);
        orderJson.put("payment_state", Constant.PAYMENT_STATE_REFUND);
        orderJson.put("cancle_name", para.optString("opt_num"));
        orderJson.put("cancellation_time", currentTime);
        orderJson.put("receive_time_cancellation", currentTime);

        JSONObject reasonJson = new JSONObject();
        reasonJson.put("reason_type", para.optString("reason_type"));
        reasonJson.put("type", "MD03");
        reasonJson.put("complaint_content", para.optString("complaint_content"));
        reasonJson.put("remark", "");
        reasonJson.put("complaints_time", currentTime);
        reasonJson.put("order_state", orderState.getOrder_state());

        List<JSONObject> reasonList = new ArrayList<JSONObject>();
        reasonList.add(reasonJson);

        orderJson.put("order_reason", reasonList);
        ordersDao.addOrderReasonDetail(tenentid, storeid, orderState.getOrder_code(), reasonList);


        List<JSONObject> orderList = new ArrayList<JSONObject>();
        orderList.add(orderJson);

        //取消微信后付异常订单，通知微信端
        JSONObject resultJson = WxOrderUtil.cancelOrder(tenentid, orderState.getOrder_code(), "orderCode");

        //JSONObject resultJson = OrderUtil.sendOrderState(tenentid, storeid, Type.ORDER, Oper.cancle, orderList);
        if ("true".equals(resultJson.optString("success"))) {
            ordersDao.updateOrderInfo(tenentid, storeid, orderJson);
        }else{
            throw SystemException.getInstance(PosErrorCode.ORDER_SEND_ORDER_STATE_ERROR);
        }
    }

    @Deprecated
    @Override
    public void printOrders(String tenentid, int storeid, JSONObject para) throws Exception {
        if (!ordersDao.checkReportDate(tenentid, storeid, para.optString("report_date"))) {
            throw SystemException.getInstance(PosErrorCode.CHECK_REPORT_DATE_ERROR);
        }

        OrderState orderState = ordersDao.getOrderStateByOrderCode(tenentid, storeid, para.optString("order_code"));
        if (orderState == null || "".equals(orderState.getBill_num())) {
            throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_BILL);
        }

        String reportDate = para.optString("report_date");
        int shiftId = para.optInt("shift_id");
        String posNum = para.optString("pos_num");
        String optNum = para.optString("opt_num");
        String billNum = orderState.getBill_num();
        if (Constant.ORDER_STATE_COMPLETE_PAYMENT.equals(orderState.getOrder_state())) {
//			printOrders(tenentid, storeid, reportDate, shiftId, posNum, optNum, billNum, Constant.ORDER_PRINT_CODE_1002);
        } else {
//			printOrders(tenentid, storeid, reportDate, shiftId, posNum, optNum, billNum, Constant.ORDER_PRINT_CODE_1001);
        }

    }

    /**
     * 打印账单
     *
     * @param tenentid
     * @param storeid
     * @param params
     * @throws Exception
     */
    private void printBill(String tenentid, int storeid, JSONObject params) throws Exception {
        Data printData = Data.get();
        printData.setTenancy_id(tenentid);
        printData.setStore_id(storeid);
        printData.setType(Type.PRINT_BILL);
        printData.setData(Arrays.asList(params));

        Data resultData = Data.get();
        posPrintService.printPosBill(printData, resultData);
    }

	private void makeInvoiceUrlForOrder(String tenancyId, int storeId, JSONObject params)
	{
		// 创建新的电子发票二维码
		logger.info("电子发票二维码生成开始!");

		try
		{
			// 准备生成电子发票的数据
			// 获取法人信息
			JSONObject legalPerInfoJo = paymentDao.getLegalPerInfo(tenancyId, storeId, params);
			if (!(Tools.isNullOrEmpty(legalPerInfoJo) || Double.isNaN(legalPerInfoJo.optDouble("tax_rate"))))
			{
				// 计算开发票金额
                double invAmt = 0d;
                boolean is_exception_print = params.optBoolean("is_exception_print");
                boolean is_order_print=params.optBoolean("is_order_print");
                if(is_exception_print||is_order_print)
                {
                    invAmt = params.optDouble("invoice_amount");
                }
                else {

                    invAmt = calcAmountForInvoice(tenancyId, storeId, params);
                }
				if (Double.isNaN(invAmt))
				{
					invAmt = 0;
				}
				if (invAmt > 0)
				{
					JSONObject jo = new JSONObject();

					// 生成电子发票，获得电子发票的打印信息
					jo.put("bill_num", params.optString("bill_num"));
					jo.put("order_num", params.optString("order_num"));
					jo.put("channel", params.optString("channel"));
					jo.put("report_date", DateUtil.format(params.optString("report_date")));
					jo.put("pos_num", params.optString("pos_num"));
					jo.put("opt_num", params.optString("opt_num"));
					jo.put("tax_rate", legalPerInfoJo.optDouble("tax_rate"));
					jo.put("invoice_num", legalPerInfoJo.optString("invoice_num"));
					jo.put("invoice_amount", invAmt);
					jo.put("is_exception_print", is_exception_print);
					logger.info("电子发票二维码生成开始：传入参数：" + jo.toString());
					getPrintInvoiceInfo(tenancyId, storeId,ParamUtil.getDateValueByObject(params, "report_date"), jo);

					logger.info("加密前的二维码URL:" + jo.optString("before_encode_url"));
					logger.info("生成的二维码URL: " + jo.optString("url_content"));
					params.put("url_path", jo.optString("url_path"));
				}
				else
				{
					logger.info("电子发票金额为0!");
				}
			}
			else
			{
				logger.info("法人信息设置错误!");
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
			logger.error("电子发票二维码生成失败:", e);
		}

		logger.info("电子发票二维码生成结束!");
	}

    @Override
    public JSONObject getReasonDetail(String tenentid, int storeid, JSONObject para) throws Exception {
        if (null == para || para.isEmpty()) {
            throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
        }

        StringBuilder sql = new StringBuilder("select t2.reason_name,t1.type,t1.complaint_content,t1.complaints_time,t1.order_state from cc_order_reason_detail t1 left join hq_unusual_reason t2 on t1.reason_type=t2.id where 1=1");

        if (para.containsKey("order_code")) {
            sql.append(" and t1.order_code='").append(para.optString("order_code")).append("'");
        }

        if (para.containsKey("type")) {
            if ("QX".equals(para.optString("type"))) {
                sql.append(" and (t1.type ='QX01' or t1.type ='MD03')");
            } else if ("TS".equals(para.optString("type"))) {
                sql.append(" and t1.type ='TS02'");

            }
        }

        sql.append(" order by t1.complaints_time desc");

        List<JSONObject> list = ordersDao.query4Json(tenentid, sql.toString());

        JSONObject result = new JSONObject();
        result.put("page", 1);
        result.put("total", list.size());
        result.put("rows", list);
        return result;
    }

    @Override
    public JSONObject getOrdersItemDetails(String tenentid, int storeid, JSONObject para) throws Exception {
        PosBill bill = ordersDao.getOrderByOrdercode(tenentid, storeid, para.optString("order_code"));
        if (null == bill) {
            throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
        }
        List<?> list = ordersDao.getOrderItemByOrdercode(tenentid, storeid, para.optString("order_code"), bill.getSource());

        JSONObject result = new JSONObject();

        result.put("page", 1);
        result.put("total", list.size());
        result.put("rows", list);

        return result;
    }

    @Override
    public void deliveryOrders(String tenentid, int storeid, JSONObject para) throws Exception {
        if (!ordersDao.checkReportDate(tenentid, storeid, para.optString("report_date"))) {
            throw SystemException.getInstance(PosErrorCode.CHECK_REPORT_DATE_ERROR);
        }

        // 获取订单状态
        OrderState orderState = ordersDao.getOrderStateByOrderCode(tenentid, storeid, para.optString("order_code"));
        if (orderState == null || Constant.ORDER_STATE_CANCEL.equals(orderState.getOrder_state())) {
            throw SystemException.getInstance(PosErrorCode.ORDER_STATE_ALREADY_CANCEL_ERROR);
        }
        if (orderState == null || Constant.ORDER_STATE_COMPLETE.equals(orderState.getOrder_state())) {
            throw SystemException.getInstance(PosErrorCode.ORDER_STATE_ALREADY_COMPLETE_ERROR);
        }
        if (orderState == null || !Constant.ORDER_STATE_COMPLETE_PAYMENT.equals(orderState.getOrder_state())) {
            throw SystemException.getInstance(PosErrorCode.ORDER_STATE_NOT_PAYMENT_ERROR);
        }

        /*orderState.setOrder_state(Constant.ORDER_STATE_DELIVERY);
        String currentTime = DateUtil.getNowDateYYDDMMHHMMSS();
        orderState.setDistribution_time(currentTime);
        orderState.setReceive_time_distribution(currentTime);

        // 修改订单状态,并返回总部
        if (ordersDao.updateOrderInfo(tenentid, storeid, JSONObject.fromObject(orderState))) {
            List<OrderState> orderStateList = new ArrayList<OrderState>();
            orderStateList.add(orderState);
            OrderUtil.sendOrderState(tenentid, storeid, Type.ORDER, Oper.update, orderStateList);
        }*/
        OrderUtil.changeOrderState(tenentid,storeid,orderState,ORDER_STATE_DELIVERY,NOTIFY_ALL);

    }

    @Override
    public void completeOrders(String tenentid, int storeid, JSONObject para) throws Exception {
        // 获取订单状态
        OrderState orderState = ordersDao.getOrderStateByOrderCode(tenentid, storeid, para.optString("order_code"));

        if(Constant.ORDER_STATE_EXCEPTION.equals(orderState.getOrder_state())){
        	OrderUtil.changeOrderState(tenentid,storeid,orderState,Constant.ORDER_STATE_EXCEPTION_COMPLETE,NOTIFY_SAAS);
        }else{
            if (!ordersDao.checkReportDate(tenentid, storeid, para.optString("report_date"))) {
                throw SystemException.getInstance(PosErrorCode.CHECK_REPORT_DATE_ERROR);
            }
            if (orderState == null || Constant.ORDER_STATE_CANCEL.equals(orderState.getOrder_state())) {
                throw SystemException.getInstance(PosErrorCode.ORDER_STATE_ALREADY_CANCEL_ERROR);
            }
            if (orderState == null || Constant.ORDER_STATE_COMPLETE.equals(orderState.getOrder_state())) {
                throw SystemException.getInstance(PosErrorCode.ORDER_STATE_ALREADY_COMPLETE_ERROR);
            }
	        if ("WM02".equalsIgnoreCase(orderState.getOrder_type())) {
	            if (orderState == null || !Constant.ORDER_STATE_DELIVERY.equals(orderState.getOrder_state())) {
	                throw SystemException.getInstance(PosErrorCode.ORDER_STATE_NOT_DELIVERY_ERROR);
	            }
	        } else if ("ZT01".equalsIgnoreCase(orderState.getOrder_type())) {
	            if (orderState == null || !Constant.ORDER_STATE_COMPLETE_PAYMENT.equals(orderState.getOrder_state())) {
	                throw SystemException.getInstance(PosErrorCode.ORDER_STATE_NOT_PAYMENT_ERROR);
	            }
	        }
	       OrderUtil.changeOrderState(tenentid,storeid,orderState,ORDER_STATE_COMPLETE,NOTIFY_SAAS);
        }
    }

    @Override
    public JSONObject makePrintOrder(String tenancyId, int storeId, JSONObject json) throws Exception {
        boolean isNewPrint=posPrintNewService.isNONewPrint(tenancyId, storeId);
        String printType = json.optString("print_type");
        json.discard(printType);
        PosBill order = null;
        String refound_bill_num = "";
        String refound_num="";
        JSONObject refundJson = null;

        // 获取订单信息
        String orderCode = json.optString("order_code");
        order = ordersDao.getOrderByOrdercode(tenancyId, storeId, orderCode);
		PosBill refundOrder = ordersDao.getOrderByOrdercode(tenancyId, storeId, orderCode+"T1");//部分退款
		if(refundOrder!=null){
			refound_bill_num = refundOrder.getBill_num();
			refound_num = refundOrder.getOrder_num();
		}
        if (null == order) {
            throw SystemException.getInstance(PosErrorCode.ORDER_NOT_EXISTS_ERROR);
        }
        //打印参数
        json.put("bill_num", order.getBill_num());
        
        //部分退款补打单
        if(!"".equals(refound_bill_num) && !"".equals(refound_num)){
        	JSONObject para = getPosOptState(tenancyId, storeId);
            para.put("order_code", orderCode);
            para.put("refound_num",refound_num);//退款订单号
            para.put("refound_bill_num", refound_bill_num);//退款账单号
            para.put("order_code", orderCode);
    	   	if(order.getOrder_state().equals(ORDER_STATE_EXCEPTION)){//异常单据
        		para.put("is_exception_print",true);
        		para.put("isPrint","N");//是否打印厨打和点菜清单标记   Y打印    N不打印
        		para.put("is_printbill",false);
        		
        	}else if(order.getOrder_state().equals(ORDER_STATE_COMPLETE) || order.getOrder_state().equals(ORDER_STATE_EXCEPTION_COMPLETE)){//正常单据
        		para.put("is_exception_print",false);
        		para.put("isPrint","Y");//是否打印厨打和点菜清单标记   Y打印    N不打印
        		para.put("is_printbill",true);
        	}
            
    	   	refundJson = this.getPrintMsg(tenancyId,storeId,para,order.getBill_num());
        }
        json.put("mode", "0");
        json.put("order_num",orderCode);
//        //电子发票参数
//        json.put("order_num",order.getOrder_num());
/*        json.put("channel",order.getSource());
        if ("1".equals(order.getNeed_invoice()) && "11".equals(order.getOrder_state())) {
            json.put("is_invoice", OrderUtil.valueOfInvoice(tenancyId,storeId,order.getSource()));//1:电子 2:普通
            // 创建新的电子发票二维码
            makeInvoiceUrlForOrder(tenancyId, storeId, json);
        }*/
//		if(printType.contains("cd01")){//厨打
//			List<Integer> list = posPrintService.orderChef(tenancyId, billNum, storeId, "0");
//			posPrintService.orderPrint(tenancyId,billNum,storeId, list);
//			logger.info("===>补打印厨打:"+list);
//		}



        if (printType.contains("dcd02")) {//点菜单
            if (isNewPrint) {//如果启用新的打印模式
            	if(json.containsKey("channel") && json.getString("channel") !=null && !json.getString("channel").isEmpty()){
            		if(THIRD_CHANNEL.contains(json.getString("channel"))){
            			//第三方外面点菜清单
            			//json.put("order_type", "WM1205");
            			posPrintNewService.posPrintByMode(tenancyId, storeId, SysDictionary.PRINT_CODE_1205, json);
            		}else if(WM_CHANNEL.contains(json.getString("channel")) || WX02_CHANNEL.equals(json.getString("channel"))){
            			//微信点菜清单
            			//json.put("order_type", "WX1401");
            			posPrintNewService.posPrintByMode(tenancyId, storeId, SysDictionary.PRINT_CODE_1401, json);
            		}
            	}
               // posPrintNewService.posPrintByMode(tenancyId, storeId, Constant.ORDER_PRINT_CODE_1003, json);
            } else {
                json.put("print_code", Constant.ORDER_PRINT_CODE_1003);
                printBill(tenancyId, storeId, json);
            }
            logger.info("===>补打印点菜单");
        }

        String dzfpSysValue=OrderUtil.getSysPara(tenancyId,storeId,DZFP_DSFWM);

        if( "2".equals(dzfpSysValue)||("1".equals(dzfpSysValue)&&"1".equals(order.getNeed_invoice()))){
            json.put("is_invoice", "1");//1:电子 2:普通
            // 创建新的电子发票二维码
            json.put("is_order_print",true);
            if(order.getOrder_state().equals(ORDER_STATE_COMPLETE)) {//异常单据
                json.put("is_order_print",true);
            }else if(order.getOrder_state().equals(ORDER_STATE_EXCEPTION)){
                json.put("is_exception_print",true);
            }
            OrderState orderState = ordersDao.getOrderStateByOrderCode(tenancyId, storeId, orderCode);
            json.put("invoice_amount",orderState.getActual_pay());
            // 创建新的电子发票二维码
            makeInvoiceUrlForOrder(tenancyId, storeId, json);
            logger.info("需要开发票:"+(1==json.getInt("is_invoice")?"电子":"纸质"));
        }

        if (printType.contains("jzd03")) {//结账单
            if (isNewPrint) {//如果启用新的打印模式
                posPrintNewService.posPrintByMode(tenancyId, storeId, Constant.ORDER_PRINT_CODE_1110, json);
            } else {
                json.put("print_code", Constant.ORDER_PRINT_CODE_1110);
                printBill(tenancyId, storeId, json);
            }
            logger.info("===>补打印结账单");
        }
        if (printType.contains("scd04")) {//随餐单
            //取餐一维码
            json.put("third_order_code", order.getThird_order_code());
            buildOrderOneDimCode(json, tenancyId,storeId );

            json.put("is_repeat_1201", "1");
            if(refundJson==null){
                if (isNewPrint) {//如果启用新的打印模式
                    if(Constant.ORDER_STATE_EXCEPTION.equals(order.getOrder_state())){//||Constant.ORDER_STATE_EXCEPTION_COMPLETE.equals(order.getOrder_state())
                    	posPrintNewService.posPrintByMode(tenancyId, storeId, SysDictionary.PRINT_CODE_1203, json);
                    }else {
                    	posPrintNewService.posPrintByMode(tenancyId, storeId, Constant.ORDER_PRINT_CODE_1201, json);
                    }
                } else {
                    json.put("print_code", Constant.ORDER_PRINT_CODE_1201);
                    printBill(tenancyId, storeId, json);
                }
                logger.info("补打随餐单::::::::::::::::::::::::::::::::::::");
            }else{
            	JSONObject orderPrint = refundJson.optJSONObject("orderPrint");
                //需要打印结账单/随餐单
                if (orderPrint.optBoolean("is_printbill")) {
                    //随餐单
                    int count = 2;
                    if (Tools.hv(OrderUtil.getSysPara(tenancyId, storeId, Constant.THIRD_SCD_COUNT))) {
                        try {
                            count = Integer.valueOf(OrderUtil.getSysPara(tenancyId, storeId, Constant.THIRD_SCD_COUNT));
                        } catch (NumberFormatException e) {
                            logger.warn("随餐单打印份数设置不正确，使用默认值[2]");
                        }
                    }

                    if (isNewPrint) {
                        posPrintNewService.posPrintByMode(tenancyId, storeId, Constant.ORDER_PRINT_CODE_1202, orderPrint);
                    } else {
                        for (int i = 0; i < count; i++) {
                            orderPrint.put("is_repeat_1202", "1"); // 采用补打的处理
                            orderPrint.put("print_code", Constant.ORDER_PRINT_CODE_1202);
                            printBill(tenancyId, storeId, orderPrint);
                        }
                    }
                    logger.info("打印原单正常退款随餐单::::::::::::::::::::::::::::::::::::");

                }

                //需要打印结账单/随餐单
                if (orderPrint.optBoolean("is_exception_print")) {
                    if (isNewPrint) {
                        posPrintNewService.posPrintByMode(tenancyId, storeId, SysDictionary.PRINT_CODE_1204, orderPrint);
                    }
                    logger.info("打印原单异常退款随餐单::::::::::::::::::::::::::::::::::::");
                }
            }
            
            logger.info("===>补打印随餐单");
        }
        return null;
    }

    @Override
    public void printOrderBuffer(String tenentId, int storeId, JSONObject json) throws Exception {

        boolean isNewPrint = posPrintNewService.isNONewPrint(tenentId, storeId);

        JSONObject chefPrint = json.optJSONObject("chefPrint");
        JSONObject orderPrint = json.optJSONObject("orderPrint");
        String channel = orderPrint.optString("channel");



        //厨打
        if (chefPrint != null && chefPrint.optString("mode").equalsIgnoreCase("0") && chefPrint.optString("is_print").equalsIgnoreCase("Y")) {
        	if ((false == "1".equals(posDishDao.getSysParameter(tenentId, storeId, "SFQDKVS"))) || "1".equals(posDishDao.getSysParameter(tenentId, storeId, "IS_KVS_PRINT_DISH"))){
	            //如果启用kvs 并且  设置kvs不打印厨打 ，外卖 不进行打印厨打
        		if (isNewPrint) {
	                posPrintNewService.posPrintByFunction(tenentId, storeId, FunctionCode.ORDERING, chefPrint);
	            } else {
	                posPrintService.orderChef(tenentId, chefPrint.optString("bill_num"), storeId, "0");
	            }
	            logger.info("打印厨打::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::");
        	}
            //点菜单
            if ((WM_CHANNEL.contains(channel) || THIRD_CHANNEL.contains(channel)) && "0".equals(OrderUtil.getSysPara(tenentId, storeId, Constant.THIRD_IS_PRINT_DCD))) {
                logger.info("已设置为不打印点菜单!");
            } else {
                if (isNewPrint) {
                	  // 新打印在下单=ORDERING时，如果总部配置了点菜单，就会打印点菜单，不受系统参数控制
//                    posPrintNewService.posPrintByMode(tenentId, storeId, Constant.ORDER_PRINT_CODE_1003, orderPrint);
                } else {
                    orderPrint.put("print_code", Constant.ORDER_PRINT_CODE_1003);
                    printBill(tenentId, storeId, orderPrint);
                }
                logger.info("打印点菜单::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::");
            }

        }

        String dzfpSysValue=OrderUtil.getSysPara(tenentId,storeId,DZFP_DSFWM);

        if( "2".equals(dzfpSysValue)||("1".equals(dzfpSysValue)&&"1".equals(orderPrint.optString("need_invoice")))){
            orderPrint.put("is_invoice", "1");//1:电子 2:普通
            // 创建新的电子发票二维码
            makeInvoiceUrlForOrder(tenentId, storeId, orderPrint);
            logger.info("需要开发票:"+(1==orderPrint.getInt("is_invoice")?"电子":"纸质"));
        }

        //需要打印结账单/随餐单
        if (orderPrint.optBoolean("is_printbill")) {

            logger.info("==========打印随餐单或结账单参数:"+orderPrint);

            //结账单
            if ("WX02".equals(channel) || "1".equals(OrderUtil.getSysPara(tenentId, storeId, Constant.THIRD_IS_PRINT_BILL))) {
                if (isNewPrint) {
                    posPrintNewService.posPrintByMode(tenentId, storeId, Constant.ORDER_PRINT_CODE_1002, orderPrint);
                } else {
                    orderPrint.put("print_code", Constant.ORDER_PRINT_CODE_1002);
                    printBill(tenentId, storeId, orderPrint);
                }
                logger.info("打印结账单::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::");
            }

            //随餐单
            if (WM_CHANNEL.contains(channel) || THIRD_CHANNEL.contains(channel)) {

                int count = 2;
                if (Tools.hv(OrderUtil.getSysPara(tenentId, storeId, Constant.THIRD_SCD_COUNT))) {
                    try {
                        count = Integer.valueOf(OrderUtil.getSysPara(tenentId, storeId, Constant.THIRD_SCD_COUNT));
                    } catch (NumberFormatException e) {
                        logger.warn("随餐单打印份数设置不正确，使用默认值[2]");
                    }
                }

                if (isNewPrint) {
                    posPrintNewService.posPrintByMode(tenentId, storeId, Constant.ORDER_PRINT_CODE_1201, orderPrint);
                } else {
                    for (int i = 0; i < count; i++) {
                        orderPrint.put("is_repeat_1201", "1"); // 采用补打的处理
                        orderPrint.put("print_code", Constant.ORDER_PRINT_CODE_1201);
                        printBill(tenentId, storeId, orderPrint);
                    }
                }
                logger.info("打印随餐单::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::");
            }

        }

        //外卖异常随餐单
        if (orderPrint.optBoolean("is_exception_print")) {
            //随餐单
            if (WM_CHANNEL.contains(channel) || THIRD_CHANNEL.contains(channel)) {

                int count = 2;
                if (Tools.hv(OrderUtil.getSysPara(tenentId, storeId, Constant.THIRD_SCD_COUNT))) {
                    try {
                        count = Integer.valueOf(OrderUtil.getSysPara(tenentId, storeId, Constant.THIRD_SCD_COUNT));
                    } catch (NumberFormatException e) {
                        logger.warn("随餐单打印份数设置不正确，使用默认值[2]");
                    }
                }

                if (isNewPrint) {
                    posPrintNewService.posPrintByMode(tenentId, storeId, SysDictionary.PRINT_CODE_1203, orderPrint);
                }
            }
            logger.info("打印随餐单::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::");
        }
    }

    @Override
    public JSONObject getConfig() {
        if (!CONFIG.containsKey("auto_take_order")) {
            CONFIG.put("auto_take_order", "yes");
        }
        return CONFIG;
    }

    @Override
    public void setConfig(JSONObject config) {
        CONFIG.putAll(config);
        logger.debug(config.getString("auto_take_order"));
        Map<String, String> systemMap = com.tzx.framework.common.constant.Constant.getSystemMap();
        String tenentId = systemMap.get("tenent_id");
        Integer storeId = Integer.parseInt(systemMap.get("store_id"));
        try{
            JSONObject para = this.getPosOptState4WX(tenentId, storeId);
            Date reportDate = ParamUtil.getDateValue(para, "report_date", false, null);
            posDishDao.savePosLog(tenentId, storeId, para.optString("pos_num"), para.optString("opt_num"), para.optString("pos_num"), para.optInt("shift_id"),reportDate, com.tzx.pos.base.Constant.TITLE, "修改外卖自动取单设置", "修改外卖自动取单设置"+config.getString("auto_take_order"), "");
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    @Override
    public JSONObject getPosOptState(String tenancyId, int storeId) throws Exception {
        return getPosOptState(tenancyId,storeId,true);
    }
    @Override
    public JSONObject getPosOptState(String tenancyId, int storeId,boolean isAfterChangedShift) throws Exception {
        JSONObject reportDate = ordersDao.getCurrentReportDate(tenancyId, storeId);
        reportDate.put("opt_num", Constant.WM_OPT_NUM);
        reportDate.put("pos_num", Constant.WM_POS_NUM);
        reportDate.put("shift_id",Constant.WM_SHIFT_ID);
        return reportDate;
//        if("1".equals(OrderUtil.getSysPara(tenancyId,storeId,"enable_wm_shift"))){//开启外卖同意班次
//     		reportDate.put("opt_num", Constant.WM_OPT_NUM);
//    		reportDate.put("pos_num", Constant.WM_POS_NUM);
//    		reportDate.put("shift_id",Constant.WM_SHIFT_ID);
//        	return reportDate;
//        }else{
//            StringBuilder queryOptSql = new StringBuilder();
//            queryOptSql.append(" select os1.tenancy_id,os1.store_id,os1.opt_num,os1.pos_num,os1.report_date,os1.shift_id from pos_opt_state os1 left join pos_opt_state os2 on os1.store_id=os2.store_id and os1.report_date=os2.report_date");
//            queryOptSql.append(" and os1.pos_num=os2.pos_num and os1.opt_num=os2.opt_num and os1.content=? and os2.content=? and os1.last_updatetime<os2.last_updatetime");
//            queryOptSql.append(" where os1.tenancy_id=? and os1.store_id=? and os1.report_date=?  and os1.content=?");
//            if(!isAfterChangedShift){
//                queryOptSql.append(" and os1.tag='0' ");//and os2.id is null");
//            }
//
//
//            List<JSONObject> rso = ordersDao.query4Json(tenancyId, queryOptSql.toString(), new Object[]
//                    {SysDictionary.OPT_STATE_KSSY, SysDictionary.OPT_STATE_YYTC, tenancyId, storeId, new SimpleDateFormat("yyyy-MM-dd").parse(reportDate.optString("report_date")), SysDictionary.OPT_STATE_KSSY});
//
//            if (null != rso && !rso.isEmpty()) {
//                return rso.get(0);
//            } else {
//        		reportDate.put("opt_num", Constant.BETWEEN_SHIFT_OPT_NUM);
//        		reportDate.put("pos_num", Constant.BETWEEN_SHIFT_POS_NUM);
//        		reportDate.put("shift_id", Constant.BETWEEN_SHIFT_ID);
//            	return reportDate;
//            }
//        }
    }

    //微信转账单获取班次信息
    public JSONObject getPosOptState4WX(String tenancyId, int storeId) throws Exception {
        JSONObject reportDate = ordersDao.getCurrentReportDate(tenancyId, storeId);

        StringBuilder queryOptSql = new StringBuilder();
        queryOptSql.append(" select os1.tenancy_id,os1.store_id,os1.opt_num,os1.pos_num,os1.report_date,os1.shift_id from pos_opt_state os1 left join pos_opt_state os2 on os1.store_id=os2.store_id and os1.report_date=os2.report_date");
        queryOptSql.append(" and os1.pos_num=os2.pos_num and os1.opt_num=os2.opt_num and os1.content=? and os2.content=? and os1.last_updatetime<os2.last_updatetime");
        queryOptSql.append(" where os1.tenancy_id=? and os1.store_id=? and os1.report_date=?  and os1.content=? and os1.tag='0' and os2.id is null ORDER BY os1.last_updatetime desc");

        List<JSONObject> rso = ordersDao.query4Json(tenancyId, queryOptSql.toString(), new Object[]
                {SysDictionary.OPT_STATE_KSSY, SysDictionary.OPT_STATE_YYTC, tenancyId, storeId, new SimpleDateFormat("yyyy-MM-dd").parse(reportDate.optString("report_date")), SysDictionary.OPT_STATE_KSSY});

        if (null != rso && !rso.isEmpty()) {
            JSONObject res = rso.get(0);
            res.put("report_date", reportDate.optString("report_date"));
            return res;
        } else {
            throw SystemException.getInstance(PosErrorCode.POS_CASHIER_NOT_SIGNIN_ERROR);
        }
    }

    public boolean isAutoTakeOrder(String tenentId, String channel, String payType) throws Exception {
        String sql = "select para_value from sys_parameter where para_code='auto_take_order'";
        List<JSONObject> res = ordersDao.query4Json(tenentId, sql);
        if (null == res || res.size() == 0) {
            return false;
        }
        String resStr = res.get(0).optString("para_value");

        if (resStr.contains(channel + "=3")) {
            return true;
        }
        if ("1".equals(payType) && resStr.contains(channel + "=1")) {
            return true;
        }
        return "0".equals(payType) && resStr.contains(channel + "=2");

    }

    private TransactionStatus getTransctionStatus(int transactionDefinition)
    {
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(transactionDefinition);
        TransactionStatus status = transactionManager.getTransaction(def);
        return status;
    }
    
	/**
	  * 是否存在异常菜品
	  * @Title:notExitsIxceptionIitems
	  * @param:@param tenentid
	  * @param:@param storeid
	  * @param:@param para
	  * @param:@return
	  * @param:@throws Exception
	  * @return: JSONObject
	  * @author: shenzhanyu
	  * @version: 1.0
	  * @Date: 2018年7月29日
	 */
    public JSONObject notExitsIxceptionIitems(String tenentid, int storeid, JSONObject para) throws Exception {
		String bill_num = para.optString("bill_num");
		String billState = ORDER_STATE_COMPLETE;// 订单状态 默认成功 10
		int unmatchNum = 0;// 未匹配菜品数量

		StringBuffer sqlBuf = new StringBuffer();
		sqlBuf.append(" SELECT COUNT(*) as unmatchNum from public.cc_order_item cci where cci.order_code = '"
				+ para.optString("order_code")
				+ "' and (cci.item_id = 0 or cci.item_id is null or cci.unit_id = 0 or cci.unit_id is null)");

		List<JSONObject> resultList = ordersDao.query4Json(tenentid, sqlBuf.toString());
		unmatchNum = resultList.get(0).optInt("unmatchnum");

        // 有未匹配菜品
        String not_exits_mapping_items = "";
        String not_exits_items = "";
        if (unmatchNum > 0) {
            billState = ORDER_STATE_EXCEPTION;// 存在未映射菜品
            sqlBuf.setLength(0);
            sqlBuf.append("SELECT cci.item_name from public.cc_order_item cci where cci.order_code = '"
                    + para.optString("order_code")
                    + "' and (cci.item_id = 0 or cci.item_id is null or cci.unit_id = 0 or cci.unit_id is null)");
            List<JSONObject> itemList = ordersDao.query4Json(tenentid, sqlBuf.toString());
            if (itemList.size() > 0) {
                StringBuffer itemNames = new StringBuffer();
                for (JSONObject item : itemList) {
                    itemNames.append(item.optString("item_name") + "、");
                }
                if (itemNames != null) {
                    not_exits_mapping_items = itemNames.substring(0, itemNames.length() - 1);
                }
                logger.info("localserver映射关系为0的菜品 ：：： " + not_exits_mapping_items);
            }
        } else {
            sqlBuf.setLength(0);
            sqlBuf.append("SELECT item_id, item_name, unit_id, unit_name FROM cc_order_item WHERE item_id NOT IN (( SELECT cci.item_id FROM cc_order_item cci INNER JOIN hq_item_info hii ON cci.item_id = hii. ID WHERE cci.order_code = '" + para.optString("order_code") + "' ) INTERSECT ( SELECT cci.item_id FROM cc_order_item cci INNER JOIN hq_item_unit hiu ON cci.item_id = hiu.item_id WHERE cci.order_code = '" + para.optString("order_code") + "' )) AND order_code = '" + para.optString("order_code") + "' \n");
            List<JSONObject> itemList = ordersDao.query4Json(tenentid, sqlBuf.toString());
            if (itemList.size() > 0) {
                StringBuffer itemNames = new StringBuffer();
                String item_name = "";
                for (JSONObject item : itemList) {
                    itemNames.append(item.optString("item_name") + "、");
                }

                if (itemNames != null) {
                    item_name = itemNames.substring(0, itemNames.length() - 1).toString();
                    not_exits_items += itemNames.toString();
                }
                logger.info("localserver不存在的菜品 ：：： " + item_name);
                //throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_DISH_ITEM_UNIT).set("{0}",item_name);
            }

            sqlBuf.setLength(0);
            sqlBuf.append("SELECT item_id, item_name, unit_id, unit_name FROM cc_order_item_details WHERE item_id NOT IN (( SELECT cci.item_id FROM cc_order_item_details cci INNER JOIN hq_item_info hii ON cci.item_id = hii. ID WHERE cci.order_code = '" + para.optString("order_code") + "' ) INTERSECT ( SELECT cci.item_id FROM cc_order_item_details cci INNER JOIN hq_item_unit hiu ON cci.item_id = hiu.item_id WHERE cci.order_code = '" + para.optString("order_code") + "' )) AND order_code = '" + para.optString("order_code") + "' \n");
            List<JSONObject> itemdetails = ordersDao.query4Json(tenentid, sqlBuf.toString());
            if (itemdetails.size() > 0) {
                StringBuffer itemNames = new StringBuffer();
                String item_name = "";
                for (JSONObject item : itemList) {
                    itemNames.append(item.optString("item_name") + "、");
                }
                if (itemNames != null) {
                    item_name = itemNames.substring(0, itemNames.length() - 1).toString();
                    not_exits_items += itemNames.toString();
                }
                logger.info("localserver不存在的菜品明细 ：：： " + item_name);
                //throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_DISH_ITEM_UNIT).set("{0}",item_name);
            }
            if (!not_exits_items.equals("")) {
                billState = ORDER_STATE_EXCEPTION;//订单状态，错误订单修正 11
                not_exits_items = not_exits_items.substring(0, not_exits_items.length() - 1).toString();
            }
        }
        if (billState.equals(ORDER_STATE_EXCEPTION)) {// 异常单据
            para.put("is_exception_print", true);
            para.put("not_exits_mapping_items", not_exits_mapping_items);// 未映射菜品
            para.put("not_exits_items", not_exits_items);// 本地不存在的菜品
            para.put("isPrint", "N");// 是否打印厨打和点菜清单标记 Y打印 N不打印
            para.put("is_printbill", false);
        } else {// 正常单据
            para.put("is_exception_print", false);
            para.put("isPrint", "Y");// 是否打印厨打和点菜清单标记 Y打印 N不打印
            para.put("is_printbill", true);

            //修改菜品类别
            ordersDao.updateBillItem(tenentid, bill_num);
            OrderState orderState = ordersDao.getOrderStateByOrderCode(tenentid, storeid, para.optString("order_code"));
            //更新订单状态
            OrderUtil.changeOrderState(tenentid, storeid, orderState, billState, NOTIFY_ALL);//10 完成 、12修复完成
        }
        return this.getPrintMsg(tenentid, storeid, para, bill_num);
    }

  //根据原订单号查询退款订单信息
	public List<JSONObject> getRefundOrders(String tenancyId, int storeId, JSONObject obj) throws Exception {
		List<JSONObject> refundOrders = new ArrayList<JSONObject>();
		String[] refundArray = new String[] { "T2", "T1" };
		for (int i = 0; i < refundArray.length; i++) {
			String order_code = obj.optString("order_code");
			order_code = order_code + refundArray[i];
			String queryOrderSql = "SELECT  * from cc_order_list ol where ol.order_code =? and ol.tenancy_id=? and ol.store_id=? ";
			List<JSONObject> partRefund = ordersDao.query4Json(tenancyId, queryOrderSql,
					new Object[] { order_code, tenancyId, storeId });
			if (partRefund != null && !partRefund.isEmpty()) {
				JSONObject order = partRefund.get(0);
				String queryOrderItem = "SELECT  * from cc_order_item oid  where oid.order_code =? and oid.tenancy_id=? and oid.store_id=? ";
				List<JSONObject> partRefundItem = ordersDao.query4Json(tenancyId, queryOrderItem,
						new Object[] { order_code, tenancyId, storeId });
				order.put("refundItem", partRefundItem);
				refundOrders.add(order);
			}
		}
		return refundOrders;
	}

  
  public JSONObject agreeRefundOrders(String tenancyId, int storeId, JSONObject jsonObj) throws Exception {
		OrderState orderState = ordersDao.getRefundOrderStateByOrderCode(tenancyId, storeId,jsonObj.optString("order_code"));
/*		String orderCode = orderState.getOrder_code();

		JSONObject para = this.getPosOptState(tenancyId, storeId);
		para.put("order_code", orderCode);
		para.put("channel", orderState.getChanel());
		String currentTime = DateUtil.getNowDateYYDDMMHHMMSS();
		para.put("single_time", currentTime);

		JSONObject jsonOb = new JSONObject();
		jsonOb.put("chanel", orderState.getChanel());
		jsonOb.put("order_state", com.tzx.orders.base.constant.Constant.ORDER_STATE_REFUND_AGREE);
		jsonOb.put("order_code", orderCode);
		jsonOb.put("operator", para.optString("opt_num"));
		jsonOb.put("reason", "POS同意退款");
*/
		if (null != orderState) {// 申请退款，POS接收到订单
			//JSONObject printBuffer = null;
			TransactionStatus status = null;
			String currentOrderState = Constant.ORDER_STATE_REFUND_AGREE; //商家同意退款
			//String refund_type = orderState.getRefund_type();// 退款类型 1：部分退 2：整单退
			try {
				status = getTransctionStatus(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
				String refund_oper_time = DateUtil.getNowDateYYDDMMHHMMSS();
				orderState.setRefund_oper_time(refund_oper_time);// 操作是否同意、时间
				OrderUtil.changeOrderState(tenancyId, storeId, orderState, currentOrderState, NOTIFY_ALL);
				transactionManager.commit(status);
			} catch (Exception e1) {
				transactionManager.rollback(status);
				throw e1;
			}

/*			if (com.tzx.orders.base.constant.Constant.ORDER_STATE_REFUND_AGREE.equals(currentOrderState)) {
				try {
					// 商家同意退款
					if ("1".equals(refund_type)) {// 部分退款
						// 订单装账单
						printBuffer = this.refundOrders(tenancyId, storeId, para);
					} else if ("2".equals(refund_type)) {// 整单退款
						// 查询是否存在部分退款,若存在对部分退款进行冲减
						if (orderCode.substring(orderCode.length() - 2, orderCode.length()).equals("T2")) {
							orderCode = orderCode.substring(0, orderCode.length() - 1) + "1";
							logger.info("外卖全部退款，对部分退款订单号 ：：： " + orderCode);
							orderState = ordersDao.getOrderStateByOrderCode(tenancyId, storeId, orderCode);
							if (orderState != null) {
								logger.info("外卖全部退款，对部分退款冲减 ：：： " + orderState);
								// 判断是否存在部分退款，如果存在进行冲减
								// 1.对部分退款冲减
								this.writeDownsRefundOrders(tenancyId, storeId, orderState);
							}
						}
					}

					List<JSONObject> sendList = new ArrayList<JSONObject>();
					sendList.add(jsonOb);
					OrderUtil.sendOrderState(tenancyId, storeId, Type.ORDER, Oper.update, sendList);
				} catch (Exception e1) {
					throw e1;
				}
				if (null != printBuffer) {
					logger.info("外卖退菜打印信息  ：：： " + printBuffer);
					this.refundOrderBuffer(tenancyId, storeId, printBuffer);
				}
			}*/
		}
		return null;
	}
  
  
	/**
	  * @Description: 不同意退款申请
	  * @Title:disagreeRefundOrders
	  * @param:@param tenentid
	  * @param:@param storeid
	  * @param:@param para
	  * @param:@throws Exception
	  * @return: void
	  * @author: shenzhanyu
	  * @version: 1.0
	  * @Date: 2018年6月29日
	 */
	public void disagreeRefundOrders(String tenentid, int storeid, JSONObject jsonObj) throws Exception {
		// 获取订单状态
		OrderState orderState = ordersDao.getRefundOrderStateByOrderCode(tenentid, storeid,jsonObj.optString("order_code"));
		if (orderState != null) {
			String currentTime = DateUtil.getNowDateYYDDMMHHMMSS();
			JSONObject para = this.getPosOptState(tenentid, storeid);
			List<JSONObject> sendList = new ArrayList<JSONObject>();
			JSONObject jsonOb = new JSONObject();
			jsonOb.put("chanel", orderState.getChanel());
			jsonOb.put("order_state", Constant.ORDER_STATE_REFUND_DISAGREE);
			jsonOb.put("order_code", orderState.getOrder_code());
			jsonOb.put("operator", para.optString("opt_num"));
			jsonOb.put("reason", jsonObj.optString("refund_reason"));
			sendList.add(jsonOb);
			JSONObject resultJson = OrderUtil.sendOrderState(tenentid, storeid, Type.ORDER, Oper.update, sendList);

			JSONObject reasonJson = new JSONObject();
			reasonJson.put("reason_type", para.optString("reason_type"));
			reasonJson.put("type", "TK08");// 外卖退款
			reasonJson.put("complaint_content", jsonObj.optString("refund_reason"));
			reasonJson.put("remark", "");
			reasonJson.put("complaints_time", currentTime);
			reasonJson.put("reason_type", jsonObj.optString("refund_reason"));
			reasonJson.put("order_state", Constant.ORDER_STATE_REFUND_DISAGREE);

			List<JSONObject> reasonList = new ArrayList<JSONObject>();
			reasonList.add(reasonJson);
			ordersDao.addOrderReasonDetail(tenentid, storeid, orderState.getOrder_code(), reasonList);

			JSONObject orderJson = new JSONObject();
			orderJson.put("order_state", Constant.ORDER_STATE_REFUND_DISAGREE);
			orderJson.put("payment_state", Constant.PAYMENT_STATE_REFUND);
			orderJson.put("refund_operator", para.optString("opt_num"));
			orderJson.put("refund_oper_time", currentTime);
			orderJson.put("order_state_desc", jsonObj.optString("refund_reason")); // 拒绝理由与申请退款理由一个字段显示存在问题
			orderJson.put("order_code", orderState.getOrder_code());

			if (0 == resultJson.optInt("code")) {
				ordersDao.updateOrderInfo(tenentid, storeid, orderJson);
			} else {
				throw SystemException.getInstance(PosErrorCode.ORDER_SEND_ORDER_STATE_ERROR);
			}
		}
	}
	
	/**
     * 外卖订单退款
     */
    public JSONObject refundOrders(String tenentid, int storeid, JSONObject para) throws Exception{
        // 查询门店业态,判断业态
        JSONObject organ = ordersDao.getOrganInfo(tenentid, storeid);
        if (organ == null || organ.isEmpty()) {
            throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_ORGAN);
        }
        if (!ordersDao.checkReportDate(tenentid, storeid, para.optString("report_date"))) {
            throw SystemException.getInstance(PosErrorCode.CHECK_REPORT_DATE_ERROR);
        }
        
		String orderCode = para.optString("order_code");
		OrderState oldOrders = null;
		String oldOrderCode = null;
		if (orderCode.substring(orderCode.length() - 2, orderCode.length()).equals("T1"))
		{
			// 原订单+T1 ：是否为退款订单
			oldOrderCode = orderCode.substring(0, orderCode.length() - 2);// 原订单号
			oldOrders = ordersDao.getOrderStateByOrderCode(tenentid, storeid, oldOrderCode);// 查找原订单
		}

        StringBuffer sqlBuf = new StringBuffer();
        
        int paymentNum = 0;//订单支付条数
        String item_menu_id = "";//餐谱ID
        
        sqlBuf.append("with t2 as (SELECT COUNT(*) as paymentNum FROM public.cc_order_repayment ccr WHERE order_code = '"+orderCode+"'), \n");
        sqlBuf.append("t3 as (SELECT item_menu_id from public.hq_item_menu_organ where tenancy_id = '"+tenentid+"' and store_id = "+storeid+") \n");
        sqlBuf.append("SELECT * from t2,t3 \n");
        
        List<JSONObject> resultList = ordersDao.query4Json(tenentid, sqlBuf.toString());
        paymentNum = resultList.get(0).optInt("paymentnum");
        item_menu_id = resultList.get(0).optString("item_menu_id");
        
        logger.info("信息查询："+sqlBuf.toString());    	

        String bill_num = "";//账单号
		// POS流水单号
		String nserial_num = "";
        try
		{
        	Date report_date = ParamUtil.getDateValueByObject(para, "report_date", false, null);

			// 生成新的账单号
			JSONObject object = new JSONObject();
			object.element("store_id", storeid);
			object.element("busi_date", report_date);
			bill_num = codeService.getCode(tenentid, Code.POS_BILL_CODE, object);// 调用统一接口来实现

			if(para.optString("pos_num").equals(Constant.WM_POS_NUM)){//外卖班次下默认机器台
				object.put("pos_num", Constant.WM_SERIAL_NUM);//外卖生产序列号前缀1000
			}else{
				object.put("pos_num", para.optString("pos_num"));
			}
			nserial_num = codeService.getCode(tenentid, Code.POS_RECORD_CODE, object); // 调用统一接口来实现
		}
		catch (Exception e)
		{
			throw new SystemException(PosErrorCode.GET_BILLNUM_SERVICE_ERROR);
		}
        logger.info("外卖订单退款账单:bill_num："+bill_num+"POS流水单号:bill_num: "+nserial_num+" tenancy_id:"+tenentid+" store_id:"+storeid);
        
		// 获取订单信息
        PosBill order = ordersDao.getOrderByOrdercode(tenentid, storeid, orderCode);
        if(order == null){
        	throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_BILL);
        }

        //外卖餐盒开关 1:开;0:关 全渠道 参数值为0  则 订单菜品 不包含餐盒 ， 门店用新的逻辑处理 ，其他情况都按菜品有餐盒的 
        String wm_meals_default_switch = OrderUtil.getSysPara(tenentid, storeid, "wm_meals_default_switch");
        String currentTime = DateUtil.getNowDateYYDDMMHHMMSS();
        //订单转账单
        //主档
        sqlBuf.setLength(0);    
        sqlBuf.append("insert into pos_bill( \n");
        sqlBuf.append("tenancy_id,store_id,table_code, guest, \n");//商户号  机构ID 桌位号/取餐号  就餐人数
        sqlBuf.append("opentable_time, payment_time, service_id, service_amount, order_num,subtotal,package_box_fee, \n");//开台时间  结账时间  基本服务费ID  服务费金额  订单号
        sqlBuf.append("bill_amount, payment_amount,discount_amount,discountr_amount, \n");//菜目合计  账单金额  应付金额  折扣金额  折让金额
        sqlBuf.append("discountk_amount, average_amount,discount_mode_id,sale_mode,source, \n");//优惠金额  人均消费  折扣方式ID  销售模式  渠道
        sqlBuf.append("remark, shop_real_amount,total_fees,platform_charge_amount,settlement_type, \n");//备注  商家实收  手续费合计  第三方平台优惠金额  线上|线下付款
        sqlBuf.append("bill_taste,item_menu_id,difference,bill_property, \n");//整单备注   餐谱ID  付款差额/找零金额   账单属性
        sqlBuf.append("bill_num,serial_num,report_date,open_pos_num,pos_num,waiter_num, \n");//账单编号  报表日期  开台机台  结账机台  服务员
        sqlBuf.append("open_opt,cashier_num,shift_id,payment_state,maling_amount, \n");//开台操作人  结账操作人  班次ID  付款状态  抹零金额
        sqlBuf.append("single_discount_amount,discount_rate,upload_tag,return_amount,bill_state \n");//单品折扣金额  折扣率  上传标记
        sqlBuf.append(") \n");
        sqlBuf.append("SELECT cc.tenancy_id,cc.store_id,cc.table_code,CAST(cc.deinner_number as INTEGER) as guest, \n");
        sqlBuf.append("'"+currentTime+"' as opentable_time,'"+currentTime+"' as payment_time,cc.meals_id as service_id,cc.shop_delivery_fee as service_amount,cc.order_code as order_num, \n");
//        if(wm_meals_default_switch.equals("0")){
//        	sqlBuf.append("cc.product_org_total_fee as subtotal,cc.package_box_fee, \n");//菜目合计  账单金额  应付金额  折扣金额  折让金额
//        }else{
//        	sqlBuf.append("(cc.product_org_total_fee+cc.package_box_fee) as subtotal,0 as package_box_fee, \n");//菜目合计  账单金额  应付金额  折扣金额  折让金额
//        }
        sqlBuf.append("cc.subtotal,cc.package_box_amount,cc.bill_amount,cc.shop_real_amount as payment_amount,\n");
//        sqlBuf.append("CASE  when  cc.delivery_party = 1 then  (cc.product_org_total_fee+cc.package_box_fee - cc.shop_real_amount) else (cc.product_org_total_fee+cc.package_box_fee+cc.shop_delivery_fee - cc.shop_real_amount)   end as discount_amount, \n");
        sqlBuf.append("(cc.bill_amount - cc.shop_real_amount) as discount_amount,cc.discountr_amount,(cc.bill_amount - cc.shop_real_amount) as discountk_amount, \n");
//        sqlBuf.append("CASE  when  cc.delivery_party = 1 then  (cc.product_org_total_fee+cc.package_box_fee - cc.shop_real_amount) else (cc.product_org_total_fee+cc.package_box_fee+cc.shop_delivery_fee - cc.shop_real_amount)   end as discountk_amount, \n");
        sqlBuf.append("cc.shop_real_amount/CAST(COALESCE(cc.deinner_number,'1') as NUMERIC) as average_amount,cc.discount_mode_id,(case when order_type='ZT01' then 'WD02' when order_type='WM02' then 'WS03' when order_type='DN03' then 'TS01' end) as sale_mode,chanel as source, \n");
        sqlBuf.append("case when cc.remark is null then '(期望送达时间:'||cc.send_time||')'   when cc.remark ='' then '(期望送达时间:'||cc.send_time||')' when cc.remark is not null then  cc.remark ||'(期望送达时间:'||cc.send_time||')' end as remark , \n");
        sqlBuf.append("cc.shop_real_amount as shop_real_amount,cc.total_fees,cc.platform_charge_amount,cc.settlement_type, \n");
        sqlBuf.append("cc.remark as bill_taste,'"+item_menu_id+"' as item_menu_id,case when "+paymentNum+">0 then 0 else cc.shop_fee end as difference, 'CLOSED' as bill_property, \n");
        sqlBuf.append("'"+bill_num+"' bill_num,'"+nserial_num+"' serial_num,'"+para.optString("report_date")+"' as report_date,'"+para.optString("pos_num")+"' as open_pos_num,'"+para.optString("pos_num")+"' as pos_num,'"+para.optString("opt_num")+"' as waiter_num, \n");
        sqlBuf.append("'"+para.optString("opt_num")+"' as open_opt,'"+para.optString("opt_num")+"' as cashier_num,'"+para.optString("shift_id")+"' as shift_id,'"+Constant.PAYMENT_STATE_COMPLETE+"' as payment_state,0 as maling_amount,  \n");
        sqlBuf.append("0 as single_discount_amount,100 as discount_rate,0 as upload_tag,0,'"+SysDictionary.BILL_STATE_BFT04+"'  \n");
//        sqlBuf.append("from cc_order_list cc  WHERE cc.order_code = '"+orderCode+"'\n");    
        sqlBuf.append("from (with item as (SELECT cci.order_code,sum((COALESCE(cciy.price,cci.saas_item_price)*-cci.number)) as item_amount \n");
        sqlBuf.append("FROM cc_order_item cci left join cc_order_item cciy on cci.item_id= cciy.item_id and cci.group_index=cciy.group_index and cciy.order_code = '"+oldOrderCode+"' \n");
        sqlBuf.append("WHERE cci.order_code = '"+orderCode+"' group by cci.order_code ) \n");
        if(wm_meals_default_switch.equals("0")){
        	  sqlBuf.append("select (item.item_amount) as subtotal,cc.package_box_fee as package_box_amount, \n");
              sqlBuf.append("(CASE when cc.delivery_party = 1 then (item.item_amount) else (item.item_amount+cc.shop_delivery_fee) end) as bill_amount,\n");
        }else{
        	  sqlBuf.append("select (item.item_amount+cc.package_box_fee) as subtotal,0 as package_box_amount, \n");
              sqlBuf.append("(CASE when cc.delivery_party = 1 then (item.item_amount+cc.package_box_fee) else (item.item_amount+cc.package_box_fee+cc.shop_delivery_fee) end) as bill_amount,\n");
        }
        sqlBuf.append("cc.* from cc_order_list cc left join item on cc.order_code=item.order_code WHERE cc.order_code = '"+orderCode+"' \n");
        sqlBuf.append(") cc \n");
        ordersDao.execute(tenentid, sqlBuf.toString());
        
        //明细  添加插入
        sqlBuf.setLength(0);    
        sqlBuf.append("insert into pos_bill_item( \n");
        sqlBuf.append("tenancy_id,store_id,item_id,item_name, \n");//商户号  机构ID  任务ID  菜品ID  菜品名称
        sqlBuf.append("item_unit_id,item_unit_name,item_count,item_amount,real_amount,  \n");//规格ID  规格名称  菜品数量  菜品金额  应付金额
        sqlBuf.append("discount_amount,single_discount_amount,discountr_amount,discount_state,discount_rate, \n");//折扣金额  抹零金额  折让金额  折扣状态  折扣率
        sqlBuf.append("discount_mode_id,remark,sale_mode,single_amount,origin_item_price, \n");//折扣方式  菜品备注  销售模式  单品折扣金额  原菜品单价
        sqlBuf.append("bill_num,report_date,item_shift_id,item_mac_id,opt_num, \n");//账单编号  报表日期  点菜班次  点菜机台号  点菜操作人
        sqlBuf.append("item_price,item_property,waitcall_tag,net_income_amount,upload_tag,item_serial,assist_item_id,item_time,item_taste,saas_item_price,saas_item_name,saas_item_num,saas_unit_name,item_num,item_class_id \n");//菜品单价  菜品属性（单品:SINGLE,套餐:SETMEAL,套餐明细:MEALLIST） 等叫标记    菜品实（净）收  上传标记 //菜品类别
        sqlBuf.append(") \n");
        sqlBuf.append("SELECT cci.tenancy_id,cci.store_id,cci.item_id,cci.item_name, \n");
        sqlBuf.append("cci.unit_id as item_unit_id,case when cci.unit_name is null or cci.unit_name='' then cci.saas_unit_name else   cci.unit_name end  as item_unit_name,-cci.number as item_count,(COALESCE(cciy.price,cci.saas_item_price)*-cci.number) as item_amount,cci.net_income_amount as real_amount,  \n");
        sqlBuf.append("((COALESCE(cciy.price,cci.saas_item_price)*-cci.number) -cci.net_income_amount) as discount_amount,cci.single_discount_amount,cci.discountr_amount,hhi.is_discount,cci.discount_rate, \n");
        sqlBuf.append("cci.discount_mode_id,cci.remark as remark,(case when order_type='ZT01' then 'WD02' when order_type='WM02' then 'WS03' when order_type='DN03' then 'TS01' end) as sale_mode,cci.single_discount_amount as single_amount,cci.price as origin_item_price, \n");
        sqlBuf.append("'"+bill_num+"' as bill_num,'"+para.optString("report_date")+"' as report_date,'"+para.optString("shift_id")+"' as item_shift_id,'"+para.optString("pos_num")+"' as item_mac_id,'"+para.optString("opt_num")+"' as opt_num, \n");
        sqlBuf.append("COALESCE(cciy.price,cci.saas_item_price) as item_price,CASE when (select count(*) from cc_order_item_details ccid where ccid.order_code = cci.order_code and ccid.group_index = cci.group_index  ) >0 then 'SETMEAL' else 'SINGLE' end as item_property,'0' as waitcall_tag,cci.net_income_amount,0 as upload_tag,cci.group_index,0,'"+para.optString("single_time")+"',cci.remark,cci.saas_item_price,cci.saas_item_name,cci.saas_item_num,cci.saas_unit_name,cci.saas_item_num, \n");
        sqlBuf.append("hhi.item_class as item_class_id ");//菜品类别
        sqlBuf.append("FROM cc_order_item cci INNER JOIN cc_order_list cc on cc.order_code = cci.order_code \n");
        sqlBuf.append("left join hq_item_info hhi on hhi.id = cci.item_id \n");
        sqlBuf.append("left join cc_order_item cciy on cci.item_id= cciy.item_id and cci.group_index=cciy.group_index and cciy.order_code = '"+oldOrderCode+"' \n");
        sqlBuf.append("WHERE cci.order_code = '"+orderCode+"' \n");
        ordersDao.execute(tenentid, sqlBuf.toString());
        
       //套餐明细  添加插入
        sqlBuf.setLength(0);    
        sqlBuf.append("insert into pos_bill_item( \n");
        sqlBuf.append("tenancy_id,store_id,item_id,item_name, \n");//商户号  机构ID  任务ID  菜品ID  菜品名称
        sqlBuf.append("item_unit_id,item_unit_name,item_count,item_amount,real_amount,  \n");//规格ID  规格名称  菜品数量  菜品金额  应付金额
        sqlBuf.append("discount_amount,single_discount_amount,discountr_amount,discount_state,discount_rate, \n");//折扣金额  抹零金额  折让金额  折扣状态  折扣率
        sqlBuf.append("discount_mode_id,remark,sale_mode,single_amount,origin_item_price, \n");//折扣方式  菜品备注  销售模式  单品折扣金额  原菜品单价
        sqlBuf.append("bill_num,report_date,item_shift_id,item_mac_id,opt_num, \n");//账单编号  报表日期  点菜班次  点菜机台号  点菜操作人
        sqlBuf.append("item_price,item_property,waitcall_tag,net_income_amount,upload_tag,  \n");//菜品单价  菜品属性（单品:SINGLE,套餐:SETMEAL,套餐明细:MEALLIST） 等叫标记   菜品实（净）收  上传标记
        sqlBuf.append("setmeal_id ,item_serial,assist_item_id,item_time,item_taste,item_num,item_class_id  \n");//菜品类别
        sqlBuf.append(") \n");
        sqlBuf.append("SELECT ccid.tenancy_id,ccid.store_id,ccid.item_id,ccid.item_name, \n");
        sqlBuf.append("ccid.unit_id as item_unit_id,ccid.unit_name as item_unit_name,-ccid.number as item_count,ccid.product_fee as item_amount,ccid.net_income_amount as real_amount,  \n");
        sqlBuf.append("(ccid.product_fee -ccid.net_income_amount ) as discount_amount,'0' as single_discount_amount,'0' as discountr_amount,'Y' as discount_state,'100' as discount_rate, \n");
        sqlBuf.append("'7' as discount_mode_id,ccid.remark as remark,'WS03' as sale_mode,null as single_amount,ccid.price as origin_item_price,  \n");
        sqlBuf.append("'"+bill_num+"' as bill_num,'"+para.optString("report_date")+"' as report_date,'"+para.optString("shift_id")+"' as item_shift_id,'"+para.optString("pos_num")+"' as item_mac_id,'"+para.optString("opt_num")+"' as opt_num, \n");
        sqlBuf.append("ccid.price as item_price,'MEALLIST' as item_property,'0' as waitcall_tag,ccid.net_income_amount,0 as upload_tag, \n");
        sqlBuf.append("(SELECT item_id from cc_order_item cci WHERE cci.order_code =ccid.order_code and cci.group_index = ccid.group_index) as setmeal_id,ccid.group_index,ccid.id,'"+para.optString("single_time")+"',ccid.remark,ccid.saas_item_num, \n");
        sqlBuf.append(" (SELECT hhi.item_class from hq_item_info hhi where hhi.id = ccid.item_id ) as item_class_id ");//菜品类别
        sqlBuf.append("FROM cc_order_item_details ccid \n");
        sqlBuf.append("left join hq_item_info hhi on hhi.id = ccid.item_id \n");
        sqlBuf.append("WHERE ccid.order_code = '"+orderCode+"' \n");
        ordersDao.execute(tenentid, sqlBuf.toString());
        
        //支付方式
        if(paymentNum>0){
        	if(ordersDao.existPaymentWay(tenentid, storeid,orderCode)){
        		sqlBuf.setLength(0);    
            	sqlBuf.append("insert into pos_bill_payment( \n");
            	sqlBuf.append("tenancy_id,store_id,bill_num,jzid,name,amount, \n");//商户号  机构ID  账单编号  付款方式ID  付款金额
            	sqlBuf.append("NUMBER,last_updatetime,rate,currency_amount,upload_tag, \n");//付款号码  操作时间  换算比率  本币金额  上传标记
            	sqlBuf.append("bill_code,remark,payment_state,fee,fee_rate, \n");//交易流水号  备注  付款状态  手续费  手续费比率
            	sqlBuf.append("report_date,shift_id,pos_num,cashier_num,shop_real_amount, \n");//报表日期  班次ID  收银机台号  收银员  商家实（净）收  
            	sqlBuf.append("count,type  \n");//付款数量   付款方式类型
            	sqlBuf.append(") \n");
            	sqlBuf.append("SELECT ccr.tenancy_id,ccr.store_id,'"+bill_num+"' as bill_num,ccr.payment_id as jzid,pw.payment_name1 as name,ccr.shop_real_amount as amount, \n");
            	sqlBuf.append("ccr.pay_no as NUMBER,now() as last_updatetime,ccr.rexchange_rate as rate,ccr.shop_real_amount as currency_amount,ccr.local_currency as upload_tag, \n");
            	sqlBuf.append("ccr.third_bill_code as bill_code,ccr.remark,'01' as payment_state,ccr.fee,ccr.rate as fee_rate, \n");
            	sqlBuf.append("'"+para.optString("report_date")+"' as report_date,'"+para.optString("shift_id")+"' as shift_id,'"+para.optString("pos_num")+"' as pos_num,'"+para.optString("opt_num")+"' as cashier_num,ccr.shop_real_amount, \n");
            	sqlBuf.append("1 as count,pw.payment_class \n");
            	sqlBuf.append("FROM cc_order_repayment ccr INNER JOIN payment_way pw on  pw.id =ccr.payment_id where ccr.order_code = '"+orderCode+"' \n");
                ordersDao.execute(tenentid, sqlBuf.toString());
        	}else{
        		throw SystemException.getInstance(PosErrorCode.NOT_EXIST_JZID);
        	}
        }
        
        // 获取订单状态
        OrderState orderState = ordersDao.getOrderStateByOrderCode(tenentid, storeid, orderCode);
        if (null==orderState) {
            throw SystemException.getInstance(PosErrorCode.ORDER_NOT_EXISTS_ERROR);
        }
        sqlBuf.setLength(0);    
    	sqlBuf.append("update cc_order_list set bill_num='"+bill_num+"' where order_code='"+orderCode+"'");
        ordersDao.execute(tenentid, sqlBuf.toString());
        
        para.put("refound_num", orderCode);//退款订单号
        para.put("refound_bill_num", bill_num);//退款账单号
        
        if(Tools.hv(oldOrders)){ //原订单+T1 ：是否为退款订单
    		para.put("invoice_amount", oldOrders.getActual_pay()+orderState.getActual_pay());//发票金额
    		bill_num = oldOrders.getBill_num();//原账单号
    		
			if (ORDER_STATE_EXCEPTION.equals(oldOrders.getOrder_state())){//异常单据
        		para.put("is_exception_print",true);
        		para.put("isPrint","N");//是否打印厨打和点菜清单标记   Y打印    N不打印
        		para.put("is_printbill",false);
			}
			else if (ORDER_STATE_COMPLETE.equals(oldOrders.getOrder_state()) || ORDER_STATE_EXCEPTION_COMPLETE.equals(oldOrders.getOrder_state())){//正常单据
        		para.put("is_exception_print",false);
        		para.put("isPrint","Y");//是否打印厨打和点菜清单标记   Y打印    N不打印
        		para.put("is_printbill",true);
        	}
        }
        
        return this.getPrintMsg(tenentid,storeid,para,bill_num);
    }
	
    public void writeDownsRefundOrders(String tenancyId, int storeId, OrderState orderState) throws Exception{
		try {
			JSONObject reportDate = ordersDao.getCurrentReportDate(tenancyId, storeId);
			Date report_date = new SimpleDateFormat("yyyy-MM-dd").parse(reportDate.optString("report_date"));
			
			String posNum = "";
			String optNum = "";
			Integer shift_id=null;
			String bill_num = orderState.getBill_num();
			
			StringBuilder sql = new StringBuilder(
					"SELECT A .*, b.amount FROM ( SELECT b.open_pos_num pos_num, b.sale_mode, COALESCE (b.table_code, '') AS table_code, b.bill_property, COALESCE (b.bill_state, '') AS bill_state, b.bill_amount, b.payment_amount, b.difference, b.bill_num, b.shift_id FROM v_pos_bill b WHERE b.store_id = ? AND b.bill_num = ? ) A LEFT JOIN ( SELECT SUM (P .amount) AS amount, P .bill_num FROM v_pos_bill_payment P GROUP BY P .bill_num ) b ON A .bill_num = b.bill_num");
					List<JSONObject> billPaymentList = ordersDao.query4Json(tenancyId,sql.toString(),new Object[] { storeId,orderState.getBill_num() });

			if (null == billPaymentList || billPaymentList.isEmpty()) {
				throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_BILL);
			}
			posNum = billPaymentList.get(0).optString("pos_num");
			shift_id =  billPaymentList.get(0).optInt("shift_id");
            optNum=getPosOptState(tenancyId,storeId,false).optString("opt_num");

			// POS账单编号
			String nbill_num = "";
			// POS流水单号
			String nserial_num = "";
			Timestamp currentTime = DateUtil.currentTimestamp();
			try
			{
				JSONObject object = new JSONObject();
				object.element("store_id", storeId);
				object.element("busi_date", report_date);
				nbill_num = codeService.getCode(tenancyId, Code.POS_BILL_CODE, object);// 调用统一接口来实现
				if(posNum.equals(Constant.WM_POS_NUM)){//外卖班次下默认机器台
					object.put("pos_num", Constant.WM_SERIAL_NUM);//外卖生产序列号前缀1000
				}else{
					object.put("pos_num", posNum);
				}
				nserial_num = codeService.getCode(tenancyId, Code.POS_RECORD_CODE, object); // 调用统一接口来实现
			}
			catch (Exception e)
			{
				logger.info("外卖整单退款：" + ExceptionMessage.getExceptionMessage(e));
				throw new SystemException(PosErrorCode.GET_BILLNUM_SERVICE_ERROR);
			}

			//对pos_bill冲减
			
			String query ="select id from pos_bill where bill_num = ? AND store_id =? ";
			List<JSONObject> billJson = ordersDao.query4Json(tenancyId,query.toString(),new Object[] { bill_num,storeId});
			if(billJson!=null && billJson.size()>0){
				StringBuilder str = new StringBuilder(
						"insert into pos_bill (tenancy_id,store_id,table_code,service_id,service_discount,source,sale_mode,bill_property,opentable_time,open_pos_num,open_opt,waiter_num,guest,service_amount,subtotal,bill_amount,payment_amount,difference,discountk_amount,discountr_amount,maling_amount,single_discount_amount,discount_amount,free_amount,givi_amount,more_coupon,average_amount,order_num,discount_rate,billfree_reason_id,transfer_remark,item_menu_id,discount_case_id,discount_mode_id,discount_num,deposit_count,copy_bill_num,guest_msg,integraloffset,remark,return_amount,payment_manager_num,print_time,print_count,payment_num,bill_num,batch_num,serial_num,report_date,shift_id,pos_num,cashier_num,payment_time,bill_state,payment_state,opt_login_number,upload_tag,shop_real_amount,total_fees,platform_charge_amount,settlement_type,bill_taste,fictitious_table,recover_count,tax_rate,tax_money,service_tax_rate,service_tax_money,tax_amount,no_tax_amount,payment_tax_money,payment_notax,bill_tax_money,bill_notax,service_notax,settlement_price,discount_reason_id,item_discountr_amount,service_discountr_amount,package_box_fee  ) ");
				str.append("select tenancy_id,store_id,table_code,service_id,service_discount,source,sale_mode,bill_property,opentable_time,open_pos_num,open_opt,waiter_num,-guest,-service_amount,-subtotal,-bill_amount,-payment_amount,0,-discountk_amount,-discountr_amount,-maling_amount,-single_discount_amount,-discount_amount,free_amount,-givi_amount,-more_coupon,-average_amount,order_num,discount_rate,billfree_reason_id,transfer_remark,item_menu_id,discount_case_id,discount_mode_id,discount_num,deposit_count,bill_num,guest_msg,integraloffset,remark,-return_amount,payment_manager_num,print_time,print_count,payment_num,? ,batch_num,? ,? ,? ,? ,? ,? ,? ,? ,? ,'0',-shop_real_amount,-total_fees,-platform_charge_amount,settlement_type,bill_taste,fictitious_table,recover_count,tax_rate,-tax_money,service_tax_rate,-service_tax_money,-tax_amount,-no_tax_amount,-payment_tax_money,-payment_notax,-bill_tax_money,-bill_notax,-service_notax,-settlement_price,discount_reason_id,-item_discountr_amount,-service_discountr_amount,-package_box_fee from pos_bill where bill_num= ? and store_id=?");
				posDishDao.update(str.toString(), new Object[]
						{ nbill_num, nserial_num, report_date, shift_id, posNum, optNum, currentTime, SysDictionary.BILL_STATE_BFTCJ01, SysDictionary.PAYMENT_STATE_REFUND_COMPLETE, 0, bill_num, storeId });

				// 对pos_bill_item产生一个新的负的账单
				// GJ20160301 item_remark<>'QX04' 把QX04改成TC01
				StringBuilder nstr = new StringBuilder(
						"insert into pos_bill_item (bill_num,item_remark,item_time,item_shift_id,item_mac_id,return_count,opt_num,tenancy_id,store_id,yrwid,details_id,item_id,item_num,item_name,item_english,item_unit_id,item_unit_name,stable_code,table_code,pushmoney_way,proportion,assist_num,assist_money,waiter_num,item_price,item_count,item_amount,real_amount,discount_amount,single_discount_amount,discountr_amount,discount_state,discount_rate,discount_mode_id,item_class_id,item_property,print_tag,waitcall_tag,setmeal_id,setmeal_rwid,is_setmeal_changitem,item_serial,report_date,item_taste,order_remark,seat_num,ticket_num,sale_mode,gqcj_tag,kvscp_tag,discount_reason_id,is_showitem,upload_tag,remark,method_money,assist_item_id,integraloffset,third_price,batch_num,item_remark_his,tax_rate,tax_money,item_notax,payment_tax_money,payment_notax,is_consignment,settlement_price,order_number,main_item,mid,activity_id,activity_batch_num,activity_rule_id,activity_count,single_amount,default_state,combo_prop,served_state,discount_num,single_discount_rate,count_rate,origin_item_price) ");
				nstr.append("select ? as bill_num,? as item_remark,? as item_time,? as item_shift_id,? as item_mac_id,-item_count as return_count,? as opt_num,tenancy_id,store_id,rwid,details_id,item_id,item_num,item_name,item_english,item_unit_id,item_unit_name,stable_code,table_code,pushmoney_way,proportion,-assist_num,-assist_money,waiter_num,item_price,-item_count,-item_amount,-real_amount,-discount_amount,-single_discount_amount,-discountr_amount,discount_state,discount_rate,discount_mode_id,item_class_id,item_property,print_tag,waitcall_tag,setmeal_id,setmeal_rwid,is_setmeal_changitem,item_serial,report_date,item_taste,order_remark,seat_num,ticket_num,sale_mode,gqcj_tag,kvscp_tag,discount_reason_id,is_showitem,0 as upload_tag,remark,-method_money,assist_item_id,-integraloffset,-third_price,batch_num,item_remark as item_remark_his,tax_rate,-tax_money,-item_notax,-payment_tax_money,-payment_notax,is_consignment,settlement_price,order_number,main_item,mid,activity_id,activity_batch_num,activity_rule_id,activity_count,single_amount,default_state,combo_prop,served_state,discount_num,-single_discount_rate,count_rate,origin_item_price ");
				nstr.append("from pos_bill_item where tenancy_id=? and store_id=? and bill_num=? and (item_remark is null or item_remark=?)");
				posDishDao.update(nstr.toString(), new Object[]
				{ nbill_num, SysDictionary.ITEM_REMARK_CJ05, currentTime, shift_id, optNum, optNum, tenancyId, storeId, bill_num, SysDictionary.ITEM_REMARK_FS02 });

				
				//支付记录冲减
				StringBuilder paySql = new StringBuilder(
						" insert into pos_bill_payment(tenancy_id,store_id,bill_num,report_date,shift_id,pos_num,cashier_num,table_code,type,jzid,name,name_english,rate,amount,count,currency_amount,number,phone,is_ysk,customer_id,bill_code,remark,last_updatetime,upload_tag,payment_state,batch_num,param_cach,more_coupon,yjzid) ");
								 paySql.append("select tenancy_id,store_id,?,report_date,shift_id,pos_num,cashier_num,table_code,type,jzid,name,name_english,rate,-amount,count,-currency_amount,number,phone,is_ysk,customer_id,bill_code,remark,?,upload_tag,payment_state,batch_num,param_cach,more_coupon,yjzid from pos_bill_payment where bill_num=? and store_id=?");
				posDishDao.update(paySql.toString(), new Object[]{nbill_num,currentTime, bill_num, storeId});


				// GJ20160301 item_remark='QX04' 把QX04改成TC01
				StringBuilder pusql = new StringBuilder("update pos_bill_item set item_remark_his=item_remark,item_remark=?,return_count=-item_count where tenancy_id=? and store_id=? and bill_num=? and (item_remark is null or item_remark=?)");
				posDishDao.update(pusql.toString(), new Object[]
				{ SysDictionary.ITEM_REMARK_TC01, tenancyId, storeId, bill_num ,SysDictionary.ITEM_REMARK_FS02});

				// 更新原单状态，账单退菜金额return_amount = 账单明细菜品金额item_amount之和（退菜）
				StringBuilder usql = new StringBuilder("update pos_bill set bill_state=?,upload_tag=0,return_amount=-(select sum(item_amount) from pos_bill_item where bill_num = ? and item_remark = ? and item_property<>? and store_id=? and tenancy_id=?) where bill_num=? and store_id=? and tenancy_id=?");
				posDishDao.update(usql.toString(), new Object[]
				{ SysDictionary.BILL_STATE_BFT04, bill_num, SysDictionary.ITEM_REMARK_TC01, SysDictionary.ITEM_PROPERTY_MEALLIST, storeId, tenancyId, bill_num, storeId, tenancyId });
				
			}else{
				
				StringBuilder str = new StringBuilder(
						"insert into pos_bill (tenancy_id,store_id,table_code,service_id,service_discount,source,sale_mode,bill_property,opentable_time,open_pos_num,open_opt,waiter_num,guest,service_amount,subtotal,bill_amount,payment_amount,difference,discountk_amount,discountr_amount,maling_amount,single_discount_amount,discount_amount,free_amount,givi_amount,more_coupon,average_amount,order_num,discount_rate,billfree_reason_id,transfer_remark,item_menu_id,discount_case_id,discount_mode_id,discount_num,deposit_count,copy_bill_num,guest_msg,integraloffset,remark,return_amount,payment_manager_num,print_time,print_count,payment_num,bill_num,batch_num,serial_num,report_date,shift_id,pos_num,cashier_num,payment_time,bill_state,payment_state,opt_login_number,upload_tag,shop_real_amount,total_fees,platform_charge_amount,settlement_type,bill_taste,fictitious_table,recover_count,tax_rate,tax_money,service_tax_rate,service_tax_money,tax_amount,no_tax_amount,payment_tax_money,payment_notax,bill_tax_money,bill_notax,service_notax,settlement_price,discount_reason_id,item_discountr_amount,service_discountr_amount,package_box_fee,is_cross_day  ) ");
				str.append("select tenancy_id,store_id,table_code,service_id,service_discount,source,sale_mode,bill_property,opentable_time,open_pos_num,open_opt,waiter_num,-guest,-service_amount,-subtotal,-bill_amount,-payment_amount,0,-discountk_amount,-discountr_amount,-maling_amount,-single_discount_amount,-discount_amount,free_amount,-givi_amount,-more_coupon,-average_amount,order_num,discount_rate,billfree_reason_id,transfer_remark,item_menu_id,discount_case_id,discount_mode_id,discount_num,deposit_count,bill_num,guest_msg,integraloffset,remark,-return_amount,payment_manager_num,print_time,print_count,payment_num,? ,batch_num,? ,? ,? ,? ,? ,? ,? ,? ,? ,'0',-shop_real_amount,-total_fees,-platform_charge_amount,settlement_type,bill_taste,fictitious_table,recover_count,tax_rate,-tax_money,service_tax_rate,-service_tax_money,-tax_amount,-no_tax_amount,-payment_tax_money,-payment_notax,-bill_tax_money,-bill_notax,-service_notax,-settlement_price,discount_reason_id,-item_discountr_amount,-service_discountr_amount,-package_box_fee,? from pos_bill2 where bill_num= ? and store_id=?");
				posDishDao.update(str.toString(), new Object[]
						{ nbill_num, nserial_num, report_date, shift_id, posNum, optNum, currentTime, SysDictionary.BILL_STATE_BFTCJ01, SysDictionary.PAYMENT_STATE_REFUND_COMPLETE, 0,SysDictionary.IS_CROSS_DAY , bill_num, storeId });

				// 对pos_bill_item产生一个新的负的账单
				// GJ20160301 item_remark<>'QX04' 把QX04改成TC01
				StringBuilder nstr = new StringBuilder(
						"insert into pos_bill_item (bill_num,item_remark,item_time,item_shift_id,item_mac_id,return_count,opt_num,tenancy_id,store_id,yrwid,details_id,item_id,item_num,item_name,item_english,item_unit_id,item_unit_name,stable_code,table_code,pushmoney_way,proportion,assist_num,assist_money,waiter_num,item_price,item_count,item_amount,real_amount,discount_amount,single_discount_amount,discountr_amount,discount_state,discount_rate,discount_mode_id,item_class_id,item_property,print_tag,waitcall_tag,setmeal_id,setmeal_rwid,is_setmeal_changitem,item_serial,report_date,item_taste,order_remark,seat_num,ticket_num,sale_mode,gqcj_tag,kvscp_tag,discount_reason_id,is_showitem,upload_tag,remark,method_money,assist_item_id,integraloffset,third_price,batch_num,item_remark_his,tax_rate,tax_money,item_notax,payment_tax_money,payment_notax,is_consignment,settlement_price,order_number,main_item,mid,activity_id,activity_batch_num,activity_rule_id,activity_count,single_amount,default_state,combo_prop,served_state,discount_num,single_discount_rate,count_rate,origin_item_price,is_cross_day) ");
				nstr.append("select ? as bill_num,? as item_remark,? as item_time,? as item_shift_id,? as item_mac_id,-item_count as return_count,? as opt_num,tenancy_id,store_id,rwid,details_id,item_id,item_num,item_name,item_english,item_unit_id,item_unit_name,stable_code,table_code,pushmoney_way,proportion,-assist_num,-assist_money,waiter_num,item_price,-item_count,-item_amount,-real_amount,-discount_amount,-single_discount_amount,-discountr_amount,discount_state,discount_rate,discount_mode_id,item_class_id,item_property,print_tag,waitcall_tag,setmeal_id,setmeal_rwid,is_setmeal_changitem,item_serial,report_date,item_taste,order_remark,seat_num,ticket_num,sale_mode,gqcj_tag,kvscp_tag,discount_reason_id,is_showitem,0 as upload_tag,remark,-method_money,assist_item_id,-integraloffset,-third_price,batch_num,item_remark as item_remark_his,tax_rate,-tax_money,-item_notax,-payment_tax_money,-payment_notax,is_consignment,settlement_price,order_number,main_item,mid,activity_id,activity_batch_num,activity_rule_id,activity_count,single_amount,default_state,combo_prop,served_state,discount_num,-single_discount_rate,count_rate,origin_item_price,? ");
				nstr.append("from pos_bill_item2 where tenancy_id=? and store_id=? and bill_num=? and (item_remark is null or item_remark=?)");
				posDishDao.update(nstr.toString(), new Object[]
				{ nbill_num, SysDictionary.ITEM_REMARK_CJ05, currentTime, shift_id, optNum, optNum,SysDictionary.IS_CROSS_DAY, tenancyId, storeId, bill_num, SysDictionary.ITEM_REMARK_FS02 });

				
				//支付记录冲减
				StringBuilder paySql = new StringBuilder(
						" insert into pos_bill_payment(tenancy_id,store_id,bill_num,report_date,shift_id,pos_num,cashier_num,table_code,type,jzid,name,name_english,rate,amount,count,currency_amount,number,phone,is_ysk,customer_id,bill_code,remark,last_updatetime,upload_tag,payment_state,batch_num,param_cach,more_coupon,yjzid) ");
								 paySql.append("select tenancy_id,store_id,?,report_date,shift_id,pos_num,cashier_num,table_code,type,jzid,name,name_english,rate,-amount,count,-currency_amount,number,phone,is_ysk,customer_id,bill_code,remark,?,upload_tag,payment_state,batch_num,param_cach,more_coupon,yjzid from pos_bill_payment2 where bill_num=? and store_id=?");
				posDishDao.update(paySql.toString(), new Object[]{nbill_num,currentTime, bill_num, storeId});


				// GJ20160301 item_remark='QX04' 把QX04改成TC01
//				StringBuilder pusql = new StringBuilder("update pos_bill_item2 set item_remark_his=item_remark,item_remark=?,return_count=-item_count where tenancy_id=? and store_id=? and bill_num=? and (item_remark is null or item_remark=?)");
//				posDishDao.update(pusql.toString(), new Object[]
//				{ SysDictionary.ITEM_REMARK_TC01, tenancyId, storeId, bill_num ,SysDictionary.ITEM_REMARK_FS02});
//
//				// 更新原单状态，账单退菜金额return_amount = 账单明细菜品金额item_amount之和（退菜）
//				StringBuilder usql = new StringBuilder("update pos_bill2 set bill_state=?,upload_tag=0,return_amount=-(select sum(item_amount) from pos_bill_item where bill_num = ? and item_remark = ? and item_property<>? and store_id=? and tenancy_id=?) where bill_num=? and store_id=? and tenancy_id=?");
//				posDishDao.update(usql.toString(), new Object[]
//				{ SysDictionary.BILL_STATE_BFT04, bill_num, SysDictionary.ITEM_REMARK_TC01, SysDictionary.ITEM_PROPERTY_MEALLIST, storeId, tenancyId, bill_num, storeId, tenancyId });

			}
		} catch (Exception e) {
			logger.error("======部分退款冲减失败======\n", e);
			throw e;
		}
    }
    
    //部分退款打印随餐单 和 退菜单
    public void refundOrderBuffer(String tenentId,int storeId,JSONObject json) throws Exception{
 	   
        boolean isNewPrint = posPrintNewService.isNONewPrint(tenentId, storeId);

        JSONObject chefPrint = json.optJSONObject("chefPrint");
        JSONObject orderPrint = json.optJSONObject("orderPrint");
        String channel = orderPrint.optString("channel");
 	   if(!"".equals(orderPrint.optString("bill_num")) && !"".equals(orderPrint.optString("refound_bill_num"))){
 		   
 		   JSONObject refundPosBill=ordersDao.getBillByBillNum(tenentId, storeId,orderPrint.optString("refound_bill_num"));
 	       
 			StringBuilder ssql = new StringBuilder(
 					"select item_time,item_serial,rwid,item_id,item_num,item_name,item_remark,item_property,round(discount_rate,2) as discount_rate,round(discount_amount,2) as discount_amount,round(item_price,2) as item_price,round(item_count,2) as item_count,round(item_amount,2) as item_amount,round(real_amount,2) as real_amount,remark,item_unit_id as unit_id,details_id,item_taste,sale_mode,setmeal_id,setmeal_rwid from pos_bill_item where tenancy_id=? and store_id=? and bill_num=? and (item_remark<>? or item_remark is null) order by rwid");
 			List<JSONObject> itemList  = posDishDao.query4Json(tenentId, ssql.toString(), new Object[]
 			{ tenentId, storeId, orderPrint.optString("refound_bill_num"), SysDictionary.ITEM_REMARK_TC01 });
 			//隔日退查询pos_bill_item2表
 			if(itemList==null || itemList.isEmpty()){
 				StringBuilder bssql = new StringBuilder(
 						"select item_time,item_serial,rwid,item_id,item_num,item_name,item_remark,item_property,round(discount_rate,2) as discount_rate,round(discount_amount,2) as discount_amount,round(item_price,2) as item_price,round(item_count,2) as item_count,round(item_amount,2) as item_amount,round(real_amount,2) as real_amount,remark,item_unit_id as unit_id,details_id,item_taste,sale_mode,setmeal_id,setmeal_rwid from pos_bill_item2 where tenancy_id=? and store_id=? and bill_num=? and (item_remark<>? or item_remark is null)");
 				itemList = posDishDao.query4Json(tenentId, bssql.toString(), new Object[]
 				{ tenentId, storeId, orderPrint.optString("refound_bill_num"), SysDictionary.ITEM_REMARK_TC01 });
 			}
 			
 			JSONObject printJson = new JSONObject();
 			if (itemList != null) {
 				StringBuilder rwids = new StringBuilder();
 				for (JSONObject item : itemList) {
 					if (!SysDictionary.ITEM_REMARK_CJ05.equals(item.optString("item_remark"))
 							&& !SysDictionary.ITEM_REMARK_TC01.equals(item.optString("item_remark"))) {
 						rwids.append(item.optString("rwid")).append(",");
 					}
 				}

 				if (rwids.length() > 0) {
 					rwids.setLength(rwids.length() - 1);
 				}

 				printJson.put("tenancy_id", tenentId);
 				printJson.put("store_id", storeId);
 				printJson.put("bill_num", orderPrint.optString("refound_bill_num"));
 				printJson.put("oper", "退菜");
 				printJson.put("oper_type", SysDictionary.ITEM_REMARK_TC01);
 				printJson.put("rwids", rwids.toString());
 				printJson.put("bill_num_tc", orderPrint.optString("refound_bill_num"));
 			}
 	       
 	        //外卖退款 退菜单打印
 	       JSONObject posBill=ordersDao.getBillByBillNum(tenentId, storeId,orderPrint.optString("bill_num"));
 	       if(posBill!=null && !posBill.isEmpty() && refundPosBill!=null && !refundPosBill.isEmpty()){
 	       	String refundOpenTableTime = refundPosBill.optString("opentable_time");
 	       	String openTableTime = posBill.optString("opentable_time");
 	       	if(!refundOpenTableTime.equals("") && !openTableTime.equals("")){
 	       		Date refundTime = DateUtil.parseDateAll(refundOpenTableTime);
 	       		Date openTime = DateUtil.parseDateAll(openTableTime);
 	       		if(refundTime!=null && openTime!=null){
 	       		   logger.info("部分退 与 原单 时间间隔"+(refundTime.getTime()-openTime.getTime())/1000/60);
                    if(refundTime.getTime()-openTime.getTime()<1000*60*30){//部分退款开台时间 -原账账单时间小于30分钟
                 	   if (orderPrint.optBoolean("is_printbill")) {
                            if(isNewPrint) {
                                posPrintNewService.posPrintByFunction(tenentId, storeId, FunctionCode.CANCBILL, printJson);
                                //循环打印厨打退菜单 1113 一份一单       1106 一菜一单       1107 整单
                                String[] printModes = {SysDictionary.PRINT_CODE_1113, SysDictionary.PRINT_CODE_1106, SysDictionary.PRINT_CODE_1107};
                                for (String mode : printModes) {
                                    posPrintNewService.posPrintByMode(printJson.optString("tenancy_id"),printJson.optInt("store_id"),mode,printJson);
                                }
                            } else{
        							List<Integer> ids = posPrintService.orderRetreat(printJson.optString("tenancy_id"),printJson.optString("bill_num"),printJson.optInt("store_id"),printJson.optString("oper"),printJson.optString("oper_type"),printJson.optString("rwids"));
        							posPrintService.orderPrint(printJson.optString("tenancy_id"),printJson.optString("bill_num"),printJson.optInt("store_id"), ids);
                            }
                 	   }
                    }
 	       		}
 	       	}
 	      }
 	   }    

 	   
         //需要打印结账单/随餐单
         if (orderPrint.optBoolean("is_printbill")) {
             //随餐单
             if (WM_CHANNEL.contains(channel) || THIRD_CHANNEL.contains(channel)) {

                 int count = 2;
                 if (Tools.hv(OrderUtil.getSysPara(tenentId, storeId, Constant.THIRD_SCD_COUNT))) {
                     try {
                         count = Integer.valueOf(OrderUtil.getSysPara(tenentId, storeId, Constant.THIRD_SCD_COUNT));
                     } catch (NumberFormatException e) {
                         logger.warn("随餐单打印份数设置不正确，使用默认值[2]");
                     }
                 }

                 if (isNewPrint) {
                     posPrintNewService.posPrintByMode(tenentId, storeId, Constant.ORDER_PRINT_CODE_1202, orderPrint);
                 } else {
                     for (int i = 0; i < count; i++) {
                         orderPrint.put("is_repeat_1202", "1"); // 采用补打的处理
                         orderPrint.put("print_code", Constant.ORDER_PRINT_CODE_1202);
                         printBill(tenentId, storeId, orderPrint);
                     }
                 }
                 logger.info("打印随餐单::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::");
             }

         }

         //需要打印结账单/随餐单
         if (orderPrint.optBoolean("is_exception_print")) {
             //随餐单
             if (WM_CHANNEL.contains(channel) || THIRD_CHANNEL.contains(channel)) {
                 int count = 2;
                 if (Tools.hv(OrderUtil.getSysPara(tenentId, storeId, Constant.THIRD_SCD_COUNT))) {
                     try {
                         count = Integer.valueOf(OrderUtil.getSysPara(tenentId, storeId, Constant.THIRD_SCD_COUNT));
                     } catch (NumberFormatException e) {
                         logger.warn("随餐单打印份数设置不正确，使用默认值[2]");
                     }
                 }

                 if (isNewPrint) {
                     posPrintNewService.posPrintByMode(tenentId, storeId, SysDictionary.PRINT_CODE_1204, orderPrint);
                 }
             }
             logger.info("打印异常随餐单::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::");
         }
    }
    
	  /**
     * 查询退款订单个数
     * @Title:getNumOfRefundOrdes
     * @param:@param tenentid
     * @param:@param storeid
     * @param:@return
     * @param:@throws Exception
     * @return: long
     * @author: shenzhanyu
     * @version: 1.0
     * @Date: 2018年7月19日
    */
   public List<JSONObject> getNumOfRefundOrders(String tenentid,int storeid) throws Exception {
   	StringBuilder sb = new StringBuilder();
    sb.append(" select ol.* from cc_order_list ol");
    sb.append(" where ol.tenancy_id=? and ol.store_id=?");
    sb.append(" and (ol.refund_type is null or ol.refund_type ='0') ");
    sb.append(" group by ol.id");
    
    sb.insert(0, "select cc2.order_code from (").append(") as c ");
    sb.append(", ( SELECT SUBSTRING ( cc1.order_code FROM 0 FOR ( CHAR_LENGTH (cc1.order_code) - 1 )) refund_code,cc1.order_code, cc1.refund_type, cc1.refund_type AS refund_order_type, cc1.shop_fee AS refund_shop_fee, cc1.single_time AS refund_single_time, cc1.refund_oper_time AS oper_time , cc1.order_state AS refund_order_state FROM cc_order_list cc1 INNER JOIN ( SELECT MAX (ID) ID, SUBSTRING ( order_code FROM 0 FOR (CHAR_LENGTH(order_code) - 1)) orderCode FROM cc_order_list WHERE refund_type in ('1','2') and order_state IN ('21') GROUP BY orderCode ) cc ON SUBSTRING ( cc1.order_code FROM 0 FOR ( CHAR_LENGTH (cc1.order_code) - 1 )) = cc.orderCode AND cc. ID = cc1. ID ) cc2 WHERE C .order_code = cc2.refund_code AND C .order_state IN ('10','11', '12') ");
    
    List<JSONObject> orders = ordersDao.query4Json(tenentid,sb.toString(),new Object[] {tenentid,storeid});
    return orders;
   }
    
    
    /**
     * 查询异常订单中缺失菜
     * @Title:getNumOfRefundOrdes
     * @param:@param tenentid
     * @param:@param storeid
     * @param:@return
     * @param:@throws Exception
     * @return: long
     * @author: shenzhanyu
     * @version: 1.0
     * @Date: 2018年7月19日
    */
   public JSONObject getNotMappedDishes(String tenentid,int storeid,JSONObject obj) throws Exception {
 		StringBuilder sb = new StringBuilder();
 		//sb.append("SELECT bind.hq_item_info_item_name, item.* FROM cc_order_item item LEFT JOIN cc_order_list ccl ON item.order_code = ccl.order_code LEFT JOIN binding_item_info bind ON item. ID = bind.cc_order_item_id AND item.order_code = bind.order_code WHERE ccl.order_state = '11' AND ccl.tenancy_id = ? AND ccl.store_id = ? AND ( item.item_id IS NULL OR item.item_id = 0 OR item.unit_id IS NULL OR item.unit_id = 0 ) ");
        sb.append("SELECT bind.hq_item_info_item_name, item.* FROM cc_order_item item LEFT JOIN cc_order_list ccl ON item.order_code = ccl.order_code LEFT JOIN binding_item_info bind ON item. ID = bind.cc_order_item_id AND item.order_code = bind.order_code WHERE ccl.order_state = '11' AND ccl.tenancy_id = ? AND ccl.store_id = ? AND ( item.item_id NOT IN (SELECT ID FROM hq_item_info) OR item.unit_id NOT IN (SELECT ID FROM hq_item_unit) OR item.item_id IS NULL OR item.item_id = 0 OR item.unit_id IS NULL OR item.unit_id = 0 )");
 		Set<String> keys = obj.keySet();
 		for (String key : keys) {
 			if ("single_time".equals(key)) {
 				sb.append(" and ccl.single_time").append(" >= '").append(obj.getString(key)).append("'");
 			} else if ("single_time2".equals(key)) {
 				sb.append(" and ccl.single_time").append(" <= '").append(obj.getString(key)).append("'");
 			}
 		}
 		sb.append(" order by bind.id desc");
 		List<JSONObject> items = ordersDao.query4Json(tenentid, sb.toString(), new Object[] { tenentid, storeid });
 		JSONObject result = new JSONObject();
 		result.put("rows", items);
 		return result;
   }
   
   
   @Override
   public JSONObject getLocalItemCate(String tenentid, int storeid, JSONObject para) throws Exception {
       JSONObject resultJson = new JSONObject();

       List<JSONObject> resultList = new ArrayList<JSONObject>();
       //渠道类型
       List<JSONObject> chanelList = ordersDao.getLocalItemChanel(tenentid, storeid, para);
       for(JSONObject json : chanelList){
     	  //根据渠道类型查找本地菜品类别
     	  List<JSONObject> itemCateList = ordersDao.getLocalItemCate(tenentid,storeid,json);
     	  List<JSONObject> fatherList = new ArrayList<JSONObject>();
           for (JSONObject json1 : itemCateList) {
               if ("0".equals(json1.optString("father_id"))) {
                   List<JSONObject> childrenList = new ArrayList<JSONObject>();
                   for (JSONObject json2 : itemCateList) {
                       if (json1.optString("id").equals(json2.optString("father_id"))) {
                           childrenList.add(json2);
                       }
                   }

                   json1.put("children", childrenList);
                   fatherList.add(json1);
               }
           }
           json.put("father", fatherList);
           resultList.add(json);
       }
       resultJson.put("data", resultList);
       return resultJson;
   }
   
   /**
     * @Description: 获取本地菜品
     * @Title:getLocalItems
     * @param:@param tenentid
     * @param:@param storeid
     * @param:@return
     * @param:@throws Exception
     * @return: JSONObject
     * @author: shenzhanyu
     * @version: 1.0
     * @Date: 2018年7月22日
    */
   public JSONObject getLocalItems(String tenentid,int storeid,JSONObject obj) throws Exception {
 	   StringBuilder sb = new StringBuilder();
 	   JSONObject result = new JSONObject();
 	   //sb.append(" SELECT * from  hq_item_info where tenancy_id=?");
       int pagenum = obj.containsKey("page") && obj.getInt("page") > 0 ? obj.getInt("page") : 1;
       int limit = obj.containsKey("rows") ? obj.getInt("rows") : 50;
 	   sb.append("SELECT unit.id unit_id,unit.unit_name,unit.standard_price,info.* from hq_item_info info RIGHT JOIN hq_item_unit unit on info.id = unit.item_id where info.tenancy_id='"+tenentid+"' ") ;
 	   if(obj.containsKey("item_name") && obj.getString("item_name")!=""){
		   sb.append(" and info.item_name like '%").append(obj.getString("item_name")).append("%'");
	   }
 	   long total = ordersDao.countSql(tenentid, sb.toString());
       sb.append(" order by info.item_code");
       sb.append(" limit " + limit + " offset " +(limit * (pagenum - 1)));
	   List<JSONObject> items = ordersDao.query4Json(tenentid,sb.toString());
       result.put("page", pagenum);
       result.put("total", total);
       result.put("rows", items);
 	   return result;
  }
  
   /**
     * @Description: 菜品修复 异常菜品与本地菜品绑定
     * @Title:bindingItem
     * @param:@param tenentid
     * @param:@param storeid
     * @param:@param obj
     * @param:@return
     * @param:@throws Exception
     * @return: Object
     * @author: shenzhanyu
     * @version: 1.0
     * @Date: 2018年7月22日
    */
   public Object bindingItem (String tenentid,int storeid,JSONObject obj) throws Exception {
 	 return  ordersDao.addBindingItemInfo(tenentid, storeid, obj);
   }
   
   /**
     * @Description: 菜品修复 解除异常菜品与本地菜品绑定
     * @Title:removeBindingItem
     * @param:@param tenentid
     * @param:@param storeid
     * @param:@param obj
     * @param:@throws Exception
     * @return: void
     * @author: shenzhanyu
     * @version: 1.0
     * @Date: 2018年7月22日
    */
   public void  removeBindingItem (String tenentid,int storeid,JSONObject obj) throws Exception {
 	  ordersDao.deleteBindingItemInfo(tenentid, storeid, obj);
   }
   
   /**
     * @Description: 验证异常菜品是否绑定
     * @Title:validateBindingItem
     * @param:@param tenentid
     * @param:@param storeid
     * @param:@param obj
     * @param:@return
     * @param:@throws Exception
     * @return: JSONObject
     * @author: shenzhanyu
     * @version: 1.0
     * @Date: 2018年7月22日
    */
   public JSONObject validateBindingItem (String tenentid,int storeid,JSONObject obj) throws Exception {
 	  return ordersDao.validateBindingItem(tenentid, storeid, obj);
   }
   
   public List<JSONObject> getBindingItems(String tenentid, int storeid) throws Exception{
 		// 1 查询绑定菜品
 		StringBuilder sb = new StringBuilder();
 		sb.append(" SELECT bind.* from  binding_item_info bind where bind.tenancy_id=? and bind.store_id=? ");
 		List<JSONObject> bindingItems = ordersDao.query4Json(tenentid, sb.toString(),new Object[] { tenentid, storeid });
 		if(bindingItems == null || bindingItems.size()==0){
 			return null;
 		}else{
 			return bindingItems;
 		}
   }
   
   /**
     * @Description: 修復綁定菜品
     * @Title:repairItemInfo
     * @param:@param tenentid
     * @param:@param storeid
     * @param:@param obj
     * @param:@return
     * @return: boolean
     * @author: shenzhanyu
     * @version: 1.0
     * @Date: 2018年7月22日
    */
 	public void repairItemInfo(String tenentid, int storeid, JSONObject obj) {
 		try {
 			// 1 查询绑定菜品
 			List<JSONObject> bindingItems = getBindingItems(tenentid,storeid);
 			// 2 循环绑定菜品
 			// 修复账单数据
 			if (bindingItems != null && bindingItems.size() > 0) {
 				for (JSONObject item : bindingItems) {
 					// 修复订单菜品
 					String order_code = item.getString("order_code");// 异常订单订单号
 					String item_name = item.getString("cc_order_item_name");
 					JSONObject json = new JSONObject();
 					json.put("id", item.optInt("cc_order_item_id"));
 					json.put("item_id", item.optInt("hq_item_info_id"));
 					json.put("unit_id", item.optInt("unit_id"));
 					json.put("unit_name", item.optString("unit_name"));
 					json.put("saas_item_price", item.optDouble("standard_price"));
 					json.put("saas_item_name", item.optString("hq_item_info_item_name"));
 					json.put("saas_item_num", item.optString("hq_item_info_item_code"));
 					json.put("saas_unit_name", item.optString("unit_name"));
 					ordersDao.updateIgnorCase(tenentid, "cc_order_item", json);

 					
 					
 					// 修复账单数据 item_id
 					// ,item_unit_id,item_unit_name,saas_item_price,saas_item_name,saas_item_num,saas_unit_name
 					StringBuilder posItemSql = new StringBuilder();
 					posItemSql.append("SELECT item.* from pos_bill_item item where bill_num in ( SELECT ccl.bill_num from cc_order_list ccl where ccl.tenancy_id=? and ccl.store_id=? and ccl.order_code=? ) and item_name=? ");
 					List<JSONObject> posBillItem = ordersDao.query4Json(tenentid, posItemSql.toString(),new Object[] { tenentid, storeid, order_code, item_name });
 					if (posBillItem != null && posBillItem.size() > 0) {
 						for (JSONObject billItem : posBillItem) {
 							JSONObject itemJson = new JSONObject();
 							itemJson.put("id", billItem.optInt("id"));
 							itemJson.put("item_id", item.optInt("hq_item_info_id"));
 							itemJson.put("item_unit_id", item.optInt("unit_id"));
 							itemJson.put("item_unit_name", item.optString("unit_name"));
 							itemJson.put("saas_item_price", item.optDouble("standard_price"));
 							itemJson.put("saas_item_name", item.optString("hq_item_info_item_name"));
 							itemJson.put("saas_item_num", item.optString("hq_item_info_item_code"));
 							itemJson.put("saas_unit_name", item.optString("unit_name"));
 							ordersDao.updateIgnorCase(tenentid, "pos_bill_item", itemJson);
 						}
 					}
 					
 					

 					// 3.异常订单不存在异常菜品的订单修改成完成状态
 					StringBuilder orderSql = new StringBuilder();
 					orderSql.append(" SELECT  item.* FROM cc_order_item item WHERE ( item.item_id IS NULL OR item.item_id = 0 OR item.unit_id IS NULL OR item.unit_id = 0 ) AND item.tenancy_id = ? AND item.store_id = ?  and  item.order_code=?");
 					List<JSONObject> orderItems = ordersDao.query4Json(tenentid, orderSql.toString(),new Object[] { tenentid, storeid, order_code });
 					if (orderItems == null || orderItems.size() == 0) {
 						JSONObject param = new JSONObject();
 						String currentTime = DateUtil.getNowDateYYDDMMHHMMSS();
 						param.put("order_code", order_code);
 						param.put("order_state", ORDER_STATE_COMPLETE);
 						param.put("finish_time", currentTime);
 						param.put("receive_time", currentTime);
 						ordersDao.updateOrderInfo(tenentid, storeid, param);
 					}
 					
 	 				OrderState orderState = ordersDao.getOrderStateByOrderCode(tenentid, storeid, order_code);
 	 				if(orderState!=null && orderState.getOrder_state() !=null &&  orderState.getOrder_state().equals(ORDER_STATE_COMPLETE)){//订单不等于空 并且 订单已经完成 10
                        //修改菜品类别
                        ordersDao.updateBillItem(tenentid, orderState.getBill_num());

 	 	 			    //更新订单状态
 	 					OrderUtil.sendOrderState(tenentid, storeid, Type.ORDER, Oper.update, Arrays.asList(orderState));
 	 				}
 				}

 				// 删除 binding_item_info 中修复的菜品记录
 				ordersDao.deleteBindingItemInfo(tenentid, storeid, new JSONObject());
 			}
 		} catch (Exception e) {
 			logger.error(e.getMessage());
 		}
 	}
 	
 	
     
 	public void repairOrdersBeforeOpenSyy(String tenancyId, int storeId, String report_date) throws Exception {

 		JSONObject json = this.getPosOptState(tenancyId, storeId,false);
 		Date reportDate = new SimpleDateFormat("yyyy-MM-dd").parse(report_date);
 		StringBuffer billBuf = new StringBuffer( //bill.open_pos_num='-1' and bill.open_opt ='-1' and bill.pos_num='-1' and  bill.cashier_num='-1'  and bill.waiter_num ='-1' and 
 				"SELECT bill.id,bill.bill_num,bill.order_num from  pos_bill bill where  bill.shift_id=0 AND bill.tenancy_id=? AND bill.store_id=? AND bill.report_date=? ");
 		List<JSONObject> billList = ordersDao.query4Json(tenancyId, billBuf.toString(),
 				new Object[] { tenancyId, storeId, reportDate });
 		
 		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
 		Date date =df.parse(DateUtil.getNowDateYYDDMMHHMMSS());
 		date.setTime(date.getTime() + 1000);//当前时间+1秒钟
 		String currentTime = df.format(date);
 		
 		String billNums = "";
 		for (JSONObject bill : billList) {
 			JSONObject billJson = new JSONObject();
 			billJson.put("id", bill.optInt("id"));
 			billJson.put("open_pos_num", json.optString("pos_num"));
 			billJson.put("pos_num", json.optString("pos_num"));
 			billJson.put("open_opt", json.optString("opt_num"));
 			billJson.put("cashier_num", json.optString("opt_num"));
 			billJson.put("waiter_num", json.optString("opt_num"));
 			billJson.put("shift_id", json.optInt("shift_id"));
 			billJson.put("shift_id", json.optInt("shift_id"));
 			billJson.put("opentable_time", currentTime); // 开台时间
 			billJson.put("payment_time", currentTime); // 支付时间
 			billJson.put("upload_tag", 0);
 			ordersDao.updateIgnorCase(tenancyId, "pos_bill", billJson);
 			billNums +=",'" + bill.optString("bill_num") + "'";
 		}
 		/*
 		 * StringBuffer ordersBuf = new StringBuffer(
 		 * "SELECT * from  cc_order_list ol where   ol.tenancy_id=? AND ol.store_id=? AND ol.report_date=? AND ol.order_code like '%"
 		 * +bill.opt("order_num")+"%'"); List<JSONObject> ordersList =
 		 * ordersDao.query4Json(tenancyId, billBuf.toString(),new Object[] {
 		 * tenancyId, storeId,reportDate }); if (ordersList != null &&
 		 * ordersList.size() > 0) { }
 		 */

 		
 		if(billNums!=null && !"".equals(billNums)){
 			String billNum = billNums.toString().substring(1, billNums.toString().length());
 			StringBuffer itemBuf = new StringBuffer( //item.item_shift_id = 0 AND item.item_mac_id = '-1' AND item.opt_num = '-1'  AND
 					"SELECT item.id FROM pos_bill_item item WHERE  item.tenancy_id =? AND item.store_id =? AND item.report_date = ? and item.bill_num in ("+ billNum + ")");
 			List<JSONObject> itemList = ordersDao.query4Json(tenancyId, itemBuf.toString(),
 					new Object[] { tenancyId, storeId, reportDate });
 			if (itemList != null && itemList.size() > 0) {
 				for (JSONObject item : itemList) {
 					JSONObject itemJson = new JSONObject();
 					itemJson.put("id", item.optInt("id"));
 					itemJson.put("opt_num", json.optString("opt_num"));
 					itemJson.put("item_mac_id", json.optString("pos_num"));
 					itemJson.put("item_shift_id", json.optInt("shift_id"));
 					itemJson.put("item_time", currentTime);
 					itemJson.put("upload_tag", 0);
 					ordersDao.updateIgnorCase(tenancyId, "pos_bill_item", itemJson);
 				}
 			}

 			StringBuffer payBuf = new StringBuffer(//pay.shift_id = 0 AND pay.pos_num = '-1' and  pay.cashier_num='-1' AND 
 					"SELECT pay.id FROM pos_bill_payment pay where  pay.tenancy_id =? AND pay.store_id =? AND pay.report_date = ?  and pay.bill_num in ("+ billNum + ")");
 			List<JSONObject> payList = ordersDao.query4Json(tenancyId, payBuf.toString(),
 					new Object[] { tenancyId, storeId, reportDate });

 			if (payList != null && payList.size() > 0) {
 				for (JSONObject pay : payList) {
 					JSONObject payJson = new JSONObject();
 					payJson.put("id", pay.optInt("id"));
 					payJson.put("cashier_num", json.optString("opt_num"));
 					payJson.put("pos_num", json.optString("pos_num"));
 					payJson.put("shift_id", json.optInt("shift_id"));
 					payJson.put("last_updatetime", currentTime);
 					payJson.put("upload_tag", 0);
 					ordersDao.updateIgnorCase(tenancyId, "pos_bill_payment", payJson);
 				}
 			}
 		}
 		
 		StringBuffer logBuf = new StringBuffer(
 				"SELECT id from pos_log where pos_num='-1' and opt_num='-1' and opt_name='admin' and shift_id=0 and tenancy_id=? and store_id=? ");
 		List<JSONObject> logList = ordersDao.query4Json(tenancyId, logBuf.toString(),
 				new Object[] { tenancyId, storeId });
 		if (logList != null && logList.size() > 0) {
 			String empName = posDishDao.getEmpNameById(json.optString("opt_num"), tenancyId, storeId);
 			for (JSONObject log : logList) {
 				JSONObject logJson = new JSONObject();
 				logJson.put("id", log.optInt("id"));
 				logJson.put("opt_num", json.optString("opt_num"));
 				logJson.put("pos_num", json.optString("pos_num"));
 				logJson.put("opt_name", empName);
 				logJson.put("shift_id", json.optInt("shift_id"));
 				logJson.put("last_updatetime", currentTime);
 				ordersDao.updateIgnorCase(tenancyId, "pos_log", logJson);
 			}
 		}
 	}

    /**
     * @Description: 打烊时门店外卖订单数据与总部外卖平台核对。
     * @param: tenancyId 商户号
     * @param: storeId	门店号
     * @param: reportDate 报表日期
     * @return: boolean 核对结果
     * @author: zhehong.qiu email:<EMAIL>
     * @version: 1.0
     * @Date: 2018年07月18日
     */
    @Override
    public void analysisOrderAmount(String tenancyId, int storeId, String reportDate) {
        // 查询当天外卖订单应收金额
        try{
            // 获取当天外卖订单总实收
            //String sql = "select sum(payment_amount) from pos_bill where tenancy_id = '"+tenancyId+"' and store_id='"+storeId+"' and report_date = '"+reportDate+"' and sale_mode='"+SysDictionary.SALE_MODE_WS03+"' and source in ('MT11','MT08','BD06','EL09') ";
            String sql = "select sum(b.payment_amount) from pos_bill b INNER JOIN cc_order_list cc on b.bill_num = cc.bill_num where b.tenancy_id = '"+tenancyId+"' and b.store_id='"+storeId+"' and b.report_date = '"+reportDate+"' and b.sale_mode='"+SysDictionary.SALE_MODE_WS03+"' and cc.chanel in ('MT11','MT08','BD06','EL09') and cc.order_state not in ('08','23','24') and (cc.refund_type in ('','1','0') or cc.refund_type is null)";
            double paymentAmount = ordersDao.getDouble(tenancyId,sql);
            // 获取外卖订单最早订单下单时间
            sql = "select  to_char(min(single_time) - interval '10 min','yyyy-MM-dd HH:mm:ss') as single_time from cc_order_list where report_date = '"+reportDate+"'";
            String startDate = ordersDao.getString(tenancyId, sql);
            String endDate = "";
//            int v = 0;//默认外卖账单应收 1.20版本开始 新订单转账单  账单 payment_amount 为cc.shop_real_amount ,1.20版本之前和下单模式  cc.shop_fee
//            if("1".equals(OrderUtil.getSysPara(tenancyId,storeId,"order_translate_bill_mode"))){
//            	v=1;//外卖账单实收
//            }
            OrderAmountAnalysis orderAmountAnalysis = new OrderAmountAnalysis(tenancyId,storeId,startDate,endDate,reportDate,String.valueOf(paymentAmount),1);
            orderThreadPool.execute(orderAmountAnalysis);
        }catch(Exception e){
            logger.error(e.getMessage());
        }
    }

    /**
     * @Description 已有订单数据修复（用于交班时外卖订单数据修复）
     * @param tenancyId : 商户号
     * @param storeId   : 门店号
     * @return void
     * <AUTHOR> email:<EMAIL>
     * @version 1.0  2018-07-12
     * @see
     */
    @Override
    public void existOrderDataRepair(String tenancyId, int storeId,String reportDate){
        try{
            /*
             * 核对门店订单数据和账单数据对应关系
             * 1、订单状态为取消数据验证
             * 2、订单状态为已完成数据验证（验证订单和账单是一对一关系）
             * 3、订单状态为已完成数据验证（验证每个订单都有对应的账单）
             * 4、
             * 导致门店外卖订单状态和外卖平台订单状态不一致的原因
             * 1、门店已取消，外卖平台已完成。原因：门店对外卖订单进行整单取消操作。
             * 2、外卖平台订单状态已取消，门店没有生成充减单。原因：门店没有收到或处理“订单取消消息”。
             * 3、外卖平台订单状态已完成，门店账单没有转账单。原因：门店没有收到或处理外卖订单消息。
             */
            // 订单状态为取消数据验证
            String sql = "select order_code,num from (select t.order_code,count(*) as num from (select c.order_code from cc_order_list c LEFT JOIN pos_bill b ON c.order_code = b.order_num where c.tenancy_id = ? and c.store_id = ? and c.report_date = ? and c.order_state='08') t group by t.order_code ) t1 where t1.num <> 2";
            List<JSONObject> dataList = ordersDao.query4Json(tenancyId,sql, new Object[]{tenancyId,storeId,new SimpleDateFormat("yyyy-MM-dd").parse(reportDate)});
            String order_code = "";
            String num = "";
            String log = "";
            for(JSONObject data : dataList){
                order_code = data.optString("order_code");
                num = data.optString("num");
                log = "外卖订单数据验证：订单号为"+order_code+"的已取消订单，账单表中有"+num+"条数据。";
                logger.error(log);
                // 调用总部接口，记录错误信息。
                OrderLogUpload orderLogUpload = new OrderLogUpload(tenancyId, storeId, reportDate, log);
                orderThreadPool.execute(orderLogUpload);
            }

            // 订单状态为已完成数据验证（验证订单和账单是一对一关系）
            sql = "select order_code,num from (select t.order_code,count(*) as num from (select c.order_code from cc_order_list c LEFT JOIN pos_bill b ON c.order_code = b.order_num where c.tenancy_id = ? and c.store_id = ? and c.report_date = ? and (c.order_state='10' or c.order_state='12')) t group by t.order_code ) t1 where t1.num <> 1 ";
            dataList = ordersDao.query4Json(tenancyId,sql, new Object[]{tenancyId,storeId,new SimpleDateFormat("yyyy-MM-dd").parse(reportDate)});
            order_code = "";
            num = "";
            for(JSONObject data : dataList){
                order_code = data.optString("order_code");
                num = data.optString("num");
                log = "外卖订单数据验证：订单号为"+order_code+"的已完成订单，账单表中有"+num+"条数据。";
                logger.error(log);
                // 调用总部接口，记录错误信息。
                OrderLogUpload orderLogUpload = new OrderLogUpload(tenancyId, storeId, reportDate, log);
                orderThreadPool.execute(orderLogUpload);
            }

            // 订单状态为已完成数据验证（验证每个订单都有对应的账单）
            sql = "select order_code,bill_num from (select c.order_code,b.bill_num from cc_order_list c LEFT JOIN pos_bill b ON c.order_code = b.order_num where c.tenancy_id = ? and c.store_id = ? and c.report_date = ? and (c.order_state='10' or c.order_state='12')) t where bill_num is null";
            dataList = ordersDao.query4Json(tenancyId,sql, new Object[]{tenancyId,storeId,new SimpleDateFormat("yyyy-MM-dd").parse(reportDate)});
            order_code = "";
            for(JSONObject data : dataList){
                order_code = data.optString("order_code");
                log = "外卖订单数据验证：订单号为"+order_code+"的已完成订单，账单表中没有数据。";
                logger.error(log);
                // 调用总部接口，记录错误信息。
                OrderLogUpload orderLogUpload = new OrderLogUpload(tenancyId, storeId, reportDate, log);
                orderThreadPool.execute(orderLogUpload);
            }

            /*
             * 门店外卖订单状态与外卖平台订单状态验证
             * 1、验证门店已存在外卖订单状态验证，对于外卖平台有而门店没有的外卖订单不进行验证。
             */
            final String final_tenancyId = tenancyId;
            final int final_storeId = storeId;
            final String final_reportDate = reportDate;
            Runnable r = new Runnable() {
                public void run() {
                    JSONObject json = OrderUtil.validOrderData(final_tenancyId,final_storeId,final_reportDate,false);
                    logger.info("validOrderData方法返回："+json.toString());
                }
            };
            orderThreadPool.execute(r);
        }catch(Exception e){
            logger.error(e.getMessage());
        }
    }


    /**
     * 删除外卖冗余数据
     * @param tenancyId
     * @param storeId
     * @param para
     */
    public void removeRedundantData(String tenancyId, int storeId,JSONObject para) throws Exception{
            logger.info("备份订单数据================");
            StringBuffer sqlBuf = new StringBuffer();
            String single_time = DateUtil.getYYYYMMDDHHMMSS(new Date());
            sqlBuf.append("CREATE table cc_order_list_bak").append(storeId).append("_").append(single_time).append(" as ");
            sqlBuf.append("select * from cc_order_list  where single_time ::date='").append(para.optString("single_time")).append("' and tenancy_id = '").append(tenancyId).append("' and store_id = ").append(storeId);
            ordersDao.execute(tenancyId, sqlBuf.toString());
            logger.info("备份订单数据：： cc_order_list_bak"+single_time);

            String cc_order_list_bak ="select order_code from cc_order_list_bak"+storeId+"_"+single_time;
            sqlBuf.setLength(0);
            sqlBuf.append("CREATE table cc_order_item_bak").append(storeId).append("_").append(single_time).append(" as ");
            sqlBuf.append(" select * from cc_order_item where order_code in ( ").append(cc_order_list_bak).append(")");
            ordersDao.execute(tenancyId, sqlBuf.toString());
            logger.info("备份订单菜品数据：： cc_order_item_bak"+single_time);

            sqlBuf.setLength(0);
            sqlBuf.append("CREATE table cc_order_item_details_bak").append(storeId).append("_").append(single_time).append(" as ");
            sqlBuf.append(" select * from cc_order_item_details where order_code in ( ").append(cc_order_list_bak).append(")");
            ordersDao.execute(tenancyId, sqlBuf.toString());
            logger.info("备份订单菜品明细数据：： cc_order_item_details_bak"+single_time);

            sqlBuf.setLength(0);
            sqlBuf.append("CREATE table cc_order_discount_bak").append(storeId).append("_").append(single_time).append(" as ");
            sqlBuf.append(" select * from cc_order_discount where order_code in ( ").append(cc_order_list_bak).append(")");
            ordersDao.execute(tenancyId, sqlBuf.toString());
            logger.info("备份订单优惠数据：： cc_order_discount_bak"+single_time);

            sqlBuf.setLength(0);
            sqlBuf.append("CREATE table cc_order_repayment_bak").append(storeId).append("_").append(single_time).append(" as ");
            sqlBuf.append(" select * from cc_order_repayment where order_code in ( ").append(cc_order_list_bak).append(")");
            ordersDao.execute(tenancyId, sqlBuf.toString());
            logger.info("备份订单支付数据：： cc_order_repayment_bak"+single_time);

            logger.info("备份账单数据====================");
            sqlBuf.setLength(0);
            sqlBuf.append("CREATE table pos_bill_bak").append(storeId).append("_").append(single_time).append(" as ");
            sqlBuf.append(" select * from pos_bill where order_num in ( ").append(cc_order_list_bak).append(")");
            ordersDao.execute(tenancyId, sqlBuf.toString());
            logger.info("备份账单数据：： pos_bill_bak"+single_time);

            sqlBuf.setLength(0);
            sqlBuf.append("CREATE table pos_bill2_bak").append(storeId).append("_").append(single_time).append(" as ");
            sqlBuf.append(" select * from pos_bill2 where bill_num in ( ").append(cc_order_list_bak).append(")");
            ordersDao.execute(tenancyId, sqlBuf.toString());
            logger.info("备份账单数据：： pos_bill2_bak"+single_time);


            String pos_bill_bak ="select bill_num from pos_bill_bak"+storeId+"_"+single_time;
            String pos_bill2_bak ="select bill_num from pos_bill2_bak"+storeId+"_"+single_time;
            sqlBuf.setLength(0);
            sqlBuf.append("CREATE table pos_bill_item_bak").append(storeId).append("_").append(single_time).append(" as ");
            sqlBuf.append(" select * from pos_bill_item where bill_num in ( ").append(pos_bill_bak).append(")");
            ordersDao.execute(tenancyId, sqlBuf.toString());
            logger.info("备份账单菜品数据：： pos_bill_item_bak"+single_time);

            sqlBuf.setLength(0);
            sqlBuf.append("CREATE table pos_bill_item2_bak").append(storeId).append("_").append(single_time).append(" as ");
            sqlBuf.append(" select * from pos_bill_item2 where bill_num in ( ").append(pos_bill2_bak).append(")");
            ordersDao.execute(tenancyId, sqlBuf.toString());
            logger.info("备份账单菜品数据：： pos_bill_item2_bak"+single_time);

            sqlBuf.setLength(0);
            sqlBuf.append("CREATE table pos_bill_payment_bak").append(storeId).append("_").append(single_time).append(" as ");
            sqlBuf.append(" select * from pos_bill_payment where bill_num in ( ").append(pos_bill_bak).append(")");
            ordersDao.execute(tenancyId, sqlBuf.toString());
            logger.info("备份账单支付数据：： pos_bill_payment_bak"+single_time);

            sqlBuf.setLength(0);
            sqlBuf.append("CREATE table pos_bill_payment2_bak").append(storeId).append("_").append(single_time).append(" as ");
            sqlBuf.append(" select * from pos_bill_payment2 where bill_num in ( ").append(pos_bill2_bak).append(")");
            ordersDao.execute(tenancyId, sqlBuf.toString());
            logger.info("备份账单支付数据：： pos_bill_payment2_bak"+single_time);

            logger.info("数据备份完毕");


            //删除数据
            sqlBuf.setLength(0);
            sqlBuf.append("DELETE from pos_bill where order_num in (").append(cc_order_list_bak).append(")");
            ordersDao.execute(tenancyId, sqlBuf.toString());

            sqlBuf.setLength(0);
            sqlBuf.append("DELETE from pos_bill2 where order_num in (").append(cc_order_list_bak).append(")");
            ordersDao.execute(tenancyId, sqlBuf.toString());

            sqlBuf.setLength(0);
            sqlBuf.append("DELETE from pos_bill_item where bill_num in (").append(pos_bill_bak).append(")");
            ordersDao.execute(tenancyId, sqlBuf.toString());

            sqlBuf.setLength(0);
            sqlBuf.append("DELETE from pos_bill_item2 where bill_num in (").append(pos_bill2_bak).append(")");
            ordersDao.execute(tenancyId, sqlBuf.toString());

            sqlBuf.setLength(0);
            sqlBuf.append("DELETE from pos_bill_payment where bill_num in (").append(pos_bill_bak).append(")");
            ordersDao.execute(tenancyId, sqlBuf.toString());

            sqlBuf.setLength(0);
            sqlBuf.append("DELETE from pos_bill_payment2 where bill_num in (").append(pos_bill2_bak).append(")");
            ordersDao.execute(tenancyId, sqlBuf.toString());

            //修改订单cc_order_list 订单为 isValid  0有效 1失效 不显示
            sqlBuf.setLength(0);
            sqlBuf.append("update cc_order_list set isValid ='1' where order_code in (").append(cc_order_list_bak).append(")");
            ordersDao.execute(tenancyId, sqlBuf.toString());
            logger.info( para.optString("opt_name")+"（联系电话："+para.optString("opt_phone")+"）, 在"+DateUtil.currentTimestamp()+" 删除下单日期为："+para.optString("single_time")+"的外卖账单数据");
            para.put("tenancyId",tenancyId);
            para.put("storeId",storeId);
            logger.info("para:::::::::::: "+para);
    }

    /**
     * @Description: 查询预订单
     * @Title:findAdvanceOrders
     * @param:@param tenancyId
     * @param:@param storeId
     * @param:@param reportDate
     * @param:@return
     * @param:@throws Exception
     * @return: List<JSONObject>
     * @author: shenzhanyu
     * @version: 1.0
     * @Date: 2018年11月28日
     */
    public List<JSONObject> findAdvanceOrders(String tenancyId, int storeId,String single_time1,String single_time2) throws Exception{
        StringBuffer querySql = new StringBuffer();
        querySql.append(" SELECT *  FROM cc_order_list where send_time<>'立即配送' and tenancy_id='").append(tenancyId).append("' and store_id=").append(storeId).append(" and order_state in ('10','11','12') and (print_remind is null or print_remind<>'1') and single_time BETWEEN  '"+single_time1+"' AND '"+single_time2+"'  order by send_time asc");
        List<JSONObject> list = ordersDao.query4Json(tenancyId, querySql.toString());
        return list;
    }

    /**
     * @Description: 预订单打印提醒
     * @Title:advanceOrdersOfPrintRemind
     * @param:@param tenancyId
     * @param:@param storeId
     * @param:@param json
     * @return: void
     * @author: shenzhanyu
     * @version: 1.0
     * @Date: 2018年11月28日
     */
    public void advanceOrdersOfPrintRemind(String tenancyId, int storeId, JSONObject json) throws Exception {
        String paraValue = OrderUtil.getSysPara(tenancyId, storeId, "waimai_bookorder_switch"); // 查询提醒参数
        JSONObject printMsg = null;
        if (Tools.hv(paraValue)) {
            String[] paras = paraValue.split(";");
            String isEnable = paras[0];// 是否启动 1:开启，0关闭
            if (isEnable.equals("1")) {
                Integer remindTime = Integer.parseInt(paras[1]);
                String printType = paras[2];// 提醒打印类型
                Date sendTime = DateUtil.parseDateAll(ParamUtil.getStringValueByObject(json, "send_time"));// 送达时间
                if (sendTime != null) {
                    Long currentTime = DateUtil.getWebsitCurrentTimeMillis();
                    logger.debug("现在北京时间 ：：："+DateUtil.getWebsiteDatetime());
                    if (sendTime.getTime() >= currentTime) {// 送达时间是否大于当前时间
                        logger.debug(" 送达时间-当前时间：：：  "+ ((sendTime.getTime() - currentTime) / 60000) +"分钟" );
                        logger.debug(" 送达时间-当前时间是否小于等于提醒的时间：：： "+ remindTime +" 分钟 :::"+(((sendTime.getTime() - currentTime) / 60000) <= remindTime));
                        if (((sendTime.getTime() - currentTime) / 60000) <= remindTime) {
                            if (!printType.equals("1")) {// 1:接单时打印 2：提醒是打印 3：接单提醒均打印
                                logger.info("预订单单号 ：：："+json.optString("order_code"));
                                // 业务报表数据
                                JSONObject para = this.getPosOptState(tenancyId, storeId, true);
                                String orderState = ParamUtil.getStringValueByObject(json, "order_state");// 订单状态
                                String bill_num = ParamUtil.getStringValueByObject(json, "bill_num");// 账单编号
                                if (orderState != null) {
                                    para.put("invoice_amount", json.optDouble("actual_pay"));
                                    para.put("order_code", json.optString("order_code"));
                                    para.put("channel", json.optString("chanel"));
                                    if (orderState.equals(ORDER_STATE_EXCEPTION)) {// 异常单据
                                        para.put("is_exception_print", true);
                                        para.put("isPrint", "N");// 是否打印厨打和点菜清单标记 Y打印 N不打印
                                        para.put("is_printbill", false);
                                    } else {// 正常单据
                                        para.put("is_exception_print", false);
                                        para.put("isPrint", "Y");// 是否打印厨打和点菜清单标记 Y打印 N不打印
                                        para.put("is_printbill", true);
                                    }
                                    printMsg = this.getPrintMsg(tenancyId, storeId, para, bill_num);
                                }
                            }
                            // 发送消息
                            List<String> msgList = new ArrayList<String>();
                            msgList.add(json.optString("order_code"));
                            JSONObject msg = new JSONObject();
                            msg.put("order_state", ADVANCE_ORDERS_REMIND); //外卖预订单响铃提醒标识，用于发送pos 声音提醒
                            msg.put("order_code", msgList);

                            // 响铃提醒
                            List<JSONObject> cometList = new ArrayList<JSONObject>();
                            cometList.add(msg);
                            Data cometData = new Data();
                            cometData.setTenancy_id(tenancyId);
                            cometData.setStore_id(storeId);
                            cometData.setType(Type.ORDER);
                            cometData.setOper(Oper.add);
                            cometData.setSuccess(true);
                            cometData.setData(cometList);

                            //POS用到UDP
                            String port = new CometDataNoticeClientRunnable(cometData).getPort();
                            SendUdpToPos.sendMessagePos(JSONObject.fromObject(cometData).toString(), port);
                            logger.info("----外卖预订单推送消息：" + JSONObject.fromObject(cometData).toString() + ", 端口号：" + port);
                            Comet4jUtil.sendMessage2All(Comet4jUtil.CALLCENTER_ORDER,msg.toString());
                            Thread.sleep(1000);
                        }
                    }else{
                        //已经超过配送时间
                        ordersDao.updateOrderPrintRemind(tenancyId, storeId,json.optString("order_code"));//修改已经提示
                    }
                }
            }
        }
        if (null != printMsg) {
            logger.info("预订单打印参数：："+printMsg);
            this.printOrderBuffer(tenancyId, storeId, printMsg);
            logger.info("修改提醒标识的订单号 "+json.optString("order_code"));
            ordersDao.updateOrderPrintRemind(tenancyId, storeId,json.optString("order_code"));//修改已经提示  ： 只提醒一次
        }
    }

    @Override
    public boolean settingTakeOrderFlag(String tenancyId, int storeId, String orderCode) throws  Exception{
        return ordersDao.settingTakeOrderFlag(tenancyId, storeId,orderCode);
    }

    /** 外送费不记录到账单明细*/
    private static final String exclude_item_delivery = "外送费";

    @SuppressWarnings("unchecked")
    @Override
    public synchronized JSONObject createBill(String tenancyId, int storeId, JSONObject para) throws Exception {
        try {
            logger.info("接单参数: " + para);

            // 查询门店业态,判断业态
            JSONObject organ = ordersDao.getOrganInfo(tenancyId, storeId);
            if (organ == null || organ.isEmpty())
            {
                throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_ORGAN);
            }

            // 营业日期、班次数据
            JSONObject jsonObject = this.getPosOptState(tenancyId, storeId, false);
            Integer shiftId = ParamUtil.getIntegerValueByObject(jsonObject, "shift_id", false, null);
            String reportDate = ParamUtil.getStringValueByObject(jsonObject, "report_date", false, null);
            String posNum = ParamUtil.getStringValueByObject(jsonObject, "pos_num", false, null);
            String optNum = ParamUtil.getStringValueByObject(jsonObject, "opt_num", false, null);

            if (!ordersDao.checkReportDate(tenancyId, storeId, reportDate))
            {
                throw SystemException.getInstance(PosErrorCode.CHECK_REPORT_DATE_ERROR);
            }

            Map<String, Object> map = (Map<String, Object>) para;

            // 订单号
            String orderCode = ParamUtil.getStringValue(map, "orderid", false, null);

            JSONObject billJson = ordersDao.getBillByOrderCode(tenancyId, storeId, orderCode);
            if (billJson != null && !billJson.isEmpty())
            {
                String billNum= ParamUtil.getStringValueByObject(billJson, "bill_num");
                throw SystemException.getInstance(CcErrorCode.ORDER_ALREADY_EXISTS_ERROR).set("bill_num", billNum);
            }

            // 判断参数中菜品信息是否为空
            List<Map<String, Object>> items = (List<Map<String, Object>>) para.getJSONArray("items"); //菜品信息
            if (items.size() == 0)
            {
                throw SystemException.getInstance(PosErrorCode.NOT_NULL_ITEM_LIST);
            }

            // 外部订单号
            String out_order_code = ParamUtil.getStringValue(map, "outorderid", false, null);
            // 桌台号
            String tableCode = ParamUtil.getStringValue(map, "table_code", false, null);
            if(null ==tableCode){
                tableCode ="";
            }

            // 就餐人数
            Integer guest = ParamUtil.getIntegerValue(map, "dinners_number", false, null);
            // 商家实收
            Double shop_real_amount = ParamUtil.getDoubleValue(map, "shopfee", false, null);
            // 订单总价
            Double totalprice = ParamUtil.getDoubleValue(map, "totalprice", false, null);
            // 优惠后总金额
            Double total = ParamUtil.getDoubleValue(map, "total", false, null);
            // 顾客实付金额
            Double actualPay = ParamUtil.getDoubleValue(map, "actual_amount", false, null);
            if(actualPay==0d)
            {
                actualPay=shop_real_amount;
                if(para.containsKey("Print_info") && para.get("Print_info")!=null)
                {
                    JSONObject printInfojson = para.optJSONObject("Print_info");
                    if(null!=printInfojson && !printInfojson.isEmpty() && printInfojson.containsKey("Actual_amount"))
                    {
                        actualPay = printInfojson.optDouble("Actual_amount");
                    }
                }
            }

            //堂食:TS01,外送:WS03,外带:WD02
            String sale_mode = SysDictionary.SALE_MODE_WS03;
            Integer ordertype = ParamUtil.getIntegerValue(map, "ordertype", false, null);
            //1-外卖 2-自取 3-堂食
            if (ordertype == 1)
            {
                sale_mode = SysDictionary.SALE_MODE_WS03;
            }
            else if (ordertype == 2)
            {
                sale_mode = SysDictionary.SALE_MODE_WD02;
            }
            else if (ordertype == 3)
            {
                sale_mode = SysDictionary.SALE_MODE_TS01;
            }

            String consigner = ParamUtil.getStringValue(map, "contact", false, null);//顾客名称
            String consignerPhone = ParamUtil.getStringValue(map, "mobile", false, null);//顾客电话
            String address = ParamUtil.getStringValue(map, "address", false, null);//顾客地址
            String taxer_id = ParamUtil.getStringValue(map, "taxer_id", false, null);//纳税人识别号
            String invoiceTitle = ParamUtil.getStringValue(map, "invoiceinfo", false, null);//发票信息
            //String orderstatus = ParamUtil.getStringValue(map, "orderstatus", false, null);//订单状态
            //Integer print_num = ParamUtil.getIntegerValue(map, "print_num", false, null);//打印次数
            //String delivery_status = ParamUtil.getStringValue(map, "delivery_status", false, null);//配送状态
            //平台第几号单 int 类型
            String order_index = ParamUtil.getStringValue(map, "order_index", false, null);
            //菜品合计
            Double subtotal = ParamUtil.getDoubleValue(map, "subtotal", false, null);
            //整单备注
            String remark = ParamUtil.getStringValue(map, "comment", false, null);
            //下单时间
            String singleTime = ParamUtil.getStringValue(map, "createtime", false, null);
            //预约时间
            String send_time = ParamUtil.getStringValue(map, "diningtime", false, null);
            //服务费 (第三方收取佣金)
            Double total_fees = ParamUtil.getDoubleValue(map, "service_money", false, null);
            //餐盒费
            Double package_fee = ParamUtil.getDoubleValue(map, "package_fee", false, null);
//    	//是否打印优惠信息
//    	String isPrintDiscount = ParamUtil.getStringValue(map, "isPrintDiscount", false, null);
//    	//是否将配送费以菜品形式打印到菜品项中
//    	String isDeliveryPay = ParamUtil.getStringValue(map, "isDeliveryPay", false, null);
//    	//来源第几单 如：话务第10号单
//    	String conTactMem = ParamUtil.getStringValue(map, "conTactMem", false, null);
            // 配送方式
            String deliveryType = ParamUtil.getStringValue(map, "delivery_type", false, null);
            // 订单配送费
            Integer service_id = 0;
            Double service_amount = ParamUtil.getDoubleValue(map, "order_delivery_pay", false, null);

            //9-美团 10-饿了么 3-微信 2-话务 11-到家 31-京东秒送 渠道类型 ;
            // WX02微信扫码点餐 、EL09 饿了么外卖、MT11新美大外卖、MT08美团外卖 、DJ01到家、CC04电话
            String source = ParamUtil.getStringValue(map, "source", false, null);
            String chanel_name = ParamUtil.getStringValue(map, "sourcename", false, null);
            String onlineName = ParamUtil.getStringValue(map, "payTypeName", false, null);

            String chanel = SysDictionary.CHANEL_MD01;
            if (null != source && !"".equals(source))
            {
                if (source.equals("9"))
                {
                    chanel = SysDictionary.CHANEL_MT08;
                }
                else if (source.equals("10"))
                {
                    chanel = SysDictionary.CHANEL_EL09;
                }
                else if (source.equals("3"))
                {
                    chanel = SysDictionary.CHANEL_WX02;
                }
                else if (source.equals("2"))
                {
                    chanel = SysDictionary.CHANEL_CC04;
                }
                else if (source.equals("1"))
                {
                    chanel = SysDictionary.CHANEL_DJ01;
                }
                else if("23".equals(source))
                {
                	chanel = SysDictionary.CHANEL_FS25;
                }
                else if("31".equals(source))
                {
                    chanel = SysDictionary.CHANEL_JD31;
                }
            }

            //折让金额
            Double discountrAmount = ParamUtil.getDoubleValue(map, "discountr_amount", false, null);
            //折扣率
            Double discount_rate = ParamUtil.getDoubleValue(map, "discount_rate", false, null);
            if (discount_rate == null || 0d >= discount_rate.doubleValue())
            {
                discount_rate =100d;
            }
            //折扣方案id
            Integer discount_case_id = ParamUtil.getIntegerValue(map, "discount_case_id", false, null);
            //抹零金额
            Double maling_amount = ParamUtil.getDoubleValue(map, "maling_amount", false, null);
            //免单金额
            Double free_amount = ParamUtil.getDoubleValue(map, "free_amount", false, null);
            //奉送金额
            Double givi_amount = ParamUtil.getDoubleValue(map, "givi_amount", false, null);
            //多收礼券金额
            Double more_coupon = ParamUtil.getDoubleValue(map, "more_coupon", false, null);
            //免单原因ID
            Integer billfree_reason_id = ParamUtil.getIntegerValue(map, "billfree_reason_id", false, null);

            //优惠金额
            Double discountkAmount = DoubleHelper.sub(totalprice, shop_real_amount, 2);
            //线上优惠
            Integer discount_mode_id = null;
            Integer discount_reason_id = null;
            String discount_num = null;
            if (discountkAmount != null && discountkAmount > 0)
            {
                discount_mode_id = 7;
                discount_num = optNum;
            }

            // 折扣（打折）金额
            Double discountAmount = DoubleHelper.padd(discountkAmount, discountrAmount);

            //平均消费
            Double average_amount = totalprice;
            if (guest != null && guest > 0)
            {
                average_amount = DoubleHelper.div(totalprice, Double.valueOf(guest), 2);
            }

            String settlement_type = null;
            //是否已经支付  1已支付0未支付
            Integer isOnline = ParamUtil.getIntegerValue(map, "ispaid", false, null);
            //线上支付
            if (1==isOnline)
            {
                settlement_type = "PLATFORM";
            }

            // 是否需要开电子发票
            //    	String lin = ParamUtil.getStringValue(map, "lin", false, null);
            String piaoTongQrCode = ParamUtil.getStringValue(map, "piaoTongQrCode", false, null);

            Map<String, JSONObject> itemInfoMap = new HashMap<String, JSONObject>();
            List<Map<String, Object>> items_list = new ArrayList<Map<String, Object>>();

            for (int k = 0; k < items.size(); k++)
            {
                Map<String, Object> oDish = (Map<String, Object>) items.get(k);
                if(exclude_item_delivery.equals(String.valueOf(oDish.get("itemname")))){
                    continue;
                }
                //tzx 系统中菜品item_code
                String itemCode = ParamUtil.getStringValue(oDish, "itemid", false, null);
                //boh菜品编号
//    		String kdishsno = ParamUtil.getStringValue(oDish, "kdishsno", false, null);
                //菜品名称
                String item_name = ParamUtil.getStringValue(oDish, "itemname", false, null);
                //规格名称
                String properties = ParamUtil.getStringValue(oDish, "properties", false, null);
                //菜品价格
                Double originalprice = ParamUtil.getDoubleValue(oDish, "originalprice", false, null);
                //菜品数量
                Double itemcount = ParamUtil.getDoubleValue(oDish, "itemcount", false, null);
                //菜品口味备注
                String item_taste = ParamUtil.getStringValue(oDish, "nature", false, null);

                JSONObject itemInfoJson = null;
                //tzx菜品id
                Integer itemid = null;
                //规格id
                Integer unitid = null;

                String isCombo = "N";
                // List<JSONObject> itemsList = posDishDao.getHqItemInfoByItemCode(tenancyId, storeId,SysDictionary.CHANEL_MD01,itemCode);
                // 根据菜品编号查询菜品 --存在于菜品档案即可
                List<JSONObject> itemsList = posDishDao.getHqItemInfoByItemCode(tenancyId,itemCode);

                if (null == itemsList || itemsList.size() == 0)
                {
                    logger.error("菜品编号= " + itemCode + " 菜品名称 = " + item_name + " 菜品对应关系异常");
                    throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_ITEM).set("{0}", item_name);
                }
                else
                {
                    if(itemsList.size()==1)
                    {
                        itemInfoJson = itemsList.get(0);
                        itemid = itemInfoJson.optInt("item_id");
                        unitid = itemInfoJson.optInt("unitid");
                        properties = itemInfoJson.optString("unit_name");
                        isCombo = itemInfoJson.optString("is_combo");
                    }
                    else
                    {
                        if(null!=properties && !"".equals(properties))
                        {
                            for(JSONObject itemJson :itemsList)
                            {
                                String unit_name = itemJson.optString("unit_name");
                                if(properties.trim().equals(unit_name))
                                {
                                    itemInfoJson = itemJson;
                                    itemid = itemJson.optInt("item_id");
                                    unitid = itemJson.optInt("unitid");
                                    properties = itemJson.optString("unit_name");
                                    isCombo = itemInfoJson.optString("is_combo");
                                    break;
                                }
                            }
                        }

                        if(null==itemInfoJson)
                        {
                            for(JSONObject itemJson :itemsList){
                                if("Y".equalsIgnoreCase(itemJson.optString("is_default"))){
                                    itemInfoJson = itemJson;
                                    itemid = itemInfoJson.optInt("item_id");
                                    unitid = itemInfoJson.optInt("unitid");
                                    properties = itemInfoJson.optString("unit_name");
                                    isCombo = itemInfoJson.optString("is_combo");
                                    break;
                                }
                            }
                        }

                    }
                }

                //菜品合计
                subtotal = DoubleHelper.add(subtotal, DoubleHelper.mul(originalprice, itemcount, 2), 2);

                itemInfoMap.put(itemid.toString(), itemInfoJson);

                oDish.put("itemid", itemid);
                oDish.put("unit_id", unitid);
                oDish.put("properties", properties);
                oDish.put("item_taste",item_taste);
                // 根据菜品档案套餐属性判断是否套餐
                if (oDish.get("bsetmeal") != null && !(oDish.get("bsetmeal").toString().equals("Y".equals(isCombo)?"1":"0"))) {
                    oDish.put("bsetmeal", "Y".equals(isCombo)?"1":"0");
                }

                items_list.add(oDish);
            }

            StringBuffer queryItemMenuIdSql = new StringBuffer();
            // 餐谱ID
            Integer itemMenuId = 0;
            queryItemMenuIdSql.append("SELECT item_menu_id from public.hq_item_menu_organ where tenancy_id = '" + tenancyId + "' and store_id = " + storeId);
            List<JSONObject> resultList = ordersDao.query4Json(tenancyId, queryItemMenuIdSql.toString());
            itemMenuId = resultList.get(0).optInt("item_menu_id");

            String serial_num = null;
            String bill_num = null;
            String batch_num = null;
            Date report_date = DateUtil.parseDate(reportDate);
            try {
                // 生成新的账单号
                JSONObject object = new JSONObject();
                object.element("store_id", storeId);
                object.element("busi_date", report_date);
                // 调用统一接口来实现
                bill_num = codeService.getCode(tenancyId, Code.POS_BILL_CODE, object);
                //外卖生产序列号前缀1000
                object.put("pos_num", com.tzx.orders.base.constant.Constant.WM_SERIAL_NUM);
                // 调用统一接口来实现
                serial_num = codeService.getCode(tenancyId, Code.POS_RECORD_CODE, object);
                // 调用统一接口来实现
                batch_num = codeService.getCode(tenancyId, Code.POS_BATCH_NUM, object);
            }
            catch (Exception e)
            {
                throw new SystemException(PosErrorCode.GET_BILLNUM_SERVICE_ERROR);
            }

            Timestamp currentTime = DateUtil.currentTimestamp();
            Double difference = 0d;
            Double return_amount = null;
            String billTaste = null;

            // 第三方平台优惠金额 = 净收(shop_real_amount)+佣金(service_money)-实付(actualPay)
            Double platform_charge_amount =  DoubleHelper.sub(DoubleHelper.add(shop_real_amount,total_fees,2), actualPay, 2);
            // 自配送,产生商家收取服务费
            double realServiceAmount = "-1".equals(deliveryType)?service_amount:0d;
            double realPackageFee = package_fee;
            if (DoubleHelper.psub(totalprice, realServiceAmount).doubleValue() == subtotal.doubleValue()) {
                realPackageFee = 0.0d;
            }

            // 转账单
            StringBuffer saveBill = new StringBuffer("insert into pos_bill (tenancy_id,store_id,bill_num,batch_num,serial_num,report_date,table_code,guest,service_amount," +
                    "opentable_time,payment_time,open_pos_num,pos_num,waiter_num,open_opt,cashier_num,shift_id,item_menu_id,service_id,order_num,third_bill_code," +
                    "subtotal,bill_amount,payment_amount,difference,discountk_amount,discountr_amount,maling_amount,discount_amount," +
                    "free_amount,givi_amount,more_coupon,average_amount,discount_case_id,discount_rate,billfree_reason_id,discount_mode_id,sale_mode,remark,bill_taste," +
                    "bill_property,upload_tag,source, return_amount,payment_state,shop_real_amount,settlement_type," +
                    "discount_reason_id,order_source,total_fees,platform_charge_amount,package_box_fee,fictitious_table )");

            saveBill.append(" values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");

            posDishDao.update(saveBill.toString(), new Object[]
                    {tenancyId, storeId, bill_num, batch_num, serial_num, report_date, tableCode, guest, realServiceAmount, currentTime, currentTime,
                            posNum, posNum, optNum, optNum, optNum, shiftId, itemMenuId, service_id, orderCode, out_order_code, subtotal, totalprice, shop_real_amount, difference,
                            discountkAmount, discountrAmount, maling_amount, discountAmount, free_amount, givi_amount, more_coupon, average_amount,
                            discount_case_id, discount_rate, billfree_reason_id, discount_mode_id, sale_mode, remark+"[送达时间："+send_time+"]", billTaste, SysDictionary.BILL_PROPERTY_CLOSED, 0,
                            chanel, return_amount, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, shop_real_amount, settlement_type, discount_reason_id, Constant.ECO_ORDER_SOURCE,
                            total_fees,platform_charge_amount,realPackageFee,order_index});

            List<com.tzx.base.entity.PosBillItem> billItemList = new ArrayList<com.tzx.base.entity.PosBillItem>();
            for (int k = 0; k < items_list.size(); k++)
            {
                Map<String, Object> oDish = (Map<String, Object>) items_list.get(k);
                if(exclude_item_delivery.equals(String.valueOf(oDish.get("itemname")))){
                    continue;
                }

                int item_serial = k + 1;

                Integer item_id = ParamUtil.getIntegerValue(oDish, "itemid", false, null);
                Double itemPrice = ParamUtil.getDoubleValue(oDish, "originalprice", false, null);// 菜品单价
                Double item_count = ParamUtil.getDoubleValue(oDish, "itemcount", false, null);// 菜品数量
                String item_taste = ParamUtil.getStringValue(oDish, "item_taste", false, null);

                //是否套餐  1：是套餐，0：单品
                Integer bsetmeal = ParamUtil.getIntegerValue(oDish, "bsetmeal", true, PosErrorCode.NOT_NULL_ITEM_BSETMEAL);
                String itemProperty = SysDictionary.ITEM_PROPERTY_SINGLE;
                //套餐
                if (1 == bsetmeal.intValue())
                {
                    itemProperty = SysDictionary.ITEM_PROPERTY_SETMEAL;
                }

                Integer itemUnitId = ParamUtil.getIntegerValue(oDish, "unit_id", true, PosErrorCode.NOT_NULL_DISH_UNITID);

                String item_unit_name = ParamUtil.getStringValue(oDish, "properties", false, null);

                JSONObject itemInfoJson = itemInfoMap.get(String.valueOf(item_id));
                Integer details_id = itemInfoJson.optInt("details_id");
                if(details_id==0){
                    details_id = null;
                }
                String item_num = itemInfoJson.optString("item_code");
                Integer item_class_id = itemInfoJson.optInt("item_class");
                String item_name = itemInfoJson.optString("item_name");
                String item_english = itemInfoJson.optString("item_english");
                String pushmoney_way = itemInfoJson.optString("pushmoney_way");
                double proportion = itemInfoJson.optDouble("proportion_money",0);

                // 是否可以折扣
                String discount_state = itemInfoJson.optString("is_discount");

                Double item_amount = DoubleHelper.mul(itemPrice, Double.valueOf(item_count), 2);
                //优惠平台菜品 = 优惠账单优惠金额 * （菜品合计金额 /菜品金额）
                Double itemDiscountAmount = DoubleHelper.mul(discountkAmount, DoubleHelper.div(item_amount, subtotal, 4), 2);
                Double itemDiscountrAmount = 0d;
                Double real_amount = item_amount - itemDiscountAmount;
                //    		Double itemDiscountRate = 100d;

                Integer setmeal_id = 0;
                Integer setmeal_rwid = 0;
                Integer assistItemId = 0;
                Integer orderNumber = 0;
                if (SysDictionary.ITEM_PROPERTY_SETMEAL.equalsIgnoreCase(itemProperty))
                {
                    setmeal_id = item_id;
                    setmeal_rwid = item_serial;
                }

                com.tzx.base.entity.PosBillItem item = new PosBillItem(tenancyId, storeId, bill_num, details_id, item_id,
                        item_num, item_name, item_english, report_date, itemUnitId, item_unit_name, tableCode,
                        pushmoney_way, proportion, 0d, null, optNum, itemPrice, item_count, discount_rate, discount_state,
                        discount_mode_id, item_class_id, itemProperty, null, "*", setmeal_id, setmeal_rwid, null,
                        currentTime, item_serial, shiftId, posNum, null, null, sale_mode, null, assistItemId, 0, 0,
                        batch_num, orderNumber, optNum, null, discount_reason_id, null, null, null, null, null, 1d,
                        "N", 0d, 100d, discount_num, null, null, null, null, null);
                item.setReal_amount(real_amount);
                item.setItem_amount(item_amount);
                item.setDiscount_amount(itemDiscountAmount);
                item.setDiscountr_amount(itemDiscountrAmount);
                item.setAssist_money(0d);
                item.setMethod_money(0d);
                item.setItem_taste(item_taste);
                billItemList.add(item);

                if (SysDictionary.ITEM_PROPERTY_SETMEAL.equalsIgnoreCase(itemProperty))
                {
                    billItemList.addAll(this.getMeallistItem(tenancyId, storeId, item, chanel, 2));
                }
            }

            this.updateDiscountkAmount(tenancyId, storeId, billItemList, discountkAmount, 2);

            this.addPosBillItemByOrder(billItemList);

            //支付记录转账单
            List<JSONObject> paymentJson = ordersDao.findPaymentWayByChanel(tenancyId, storeId, chanel);
            if (!paymentJson.isEmpty())
            {
                StringBuffer insertPaymentSql = new StringBuffer();
                insertPaymentSql.append("insert into pos_bill_payment( ");
                insertPaymentSql.append("tenancy_id,store_id,bill_num,jzid,amount, ");//商户号  机构ID  账单编号  付款方式ID  付款金额
                insertPaymentSql.append("number,last_updatetime,rate,currency_amount,upload_tag, ");//付款号码  操作时间  换算比率  本币金额  上传标记
                insertPaymentSql.append("bill_code,remark,payment_state,fee,fee_rate,");//交易流水号  备注  付款状态  手续费  手续费比率
                insertPaymentSql.append("report_date,shift_id,pos_num,cashier_num,shop_real_amount, ");//报表日期  班次ID  收银机台号  收银员  商家实（净）收
                insertPaymentSql.append("count,type,name,table_code,coupon_buy_price,due,tenancy_assume,third_assume,third_fee ) ");//付款数量   付款方式类型
                insertPaymentSql.append(" values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");

                JSONObject payment = JSONObject.fromObject(paymentJson.get(0));

                Integer jzid = payment.optInt("jzid"); //支付id
                String number = null;
                Integer rate = 1;
                String payment_remark = payment.optString("payment_class");
                String payment_state = SysDictionary.PAYMENT_STATE_PAY_COMPLETE;//完成
                Double fee = null;
                Double fee_rate = null;
                String type = payment.optString("payment_class");
                String name = payment.optString("name");

                Double couponBuyPrice = shop_real_amount;
                Double due = shop_real_amount;
                Double thirdFee = 0d;
                Double tenancyAssume = 0d;
                Double thirdAssume = 0d;

                Object[] objs = new Object[]{tenancyId, storeId, bill_num, jzid, shop_real_amount, number, currentTime, rate, shop_real_amount, 0, out_order_code, payment_remark, payment_state,
                        fee, fee_rate, report_date, shiftId, posNum, optNum, shop_real_amount, 1, type, name, tableCode,couponBuyPrice,due,tenancyAssume,thirdAssume,thirdFee};

                List<Object[]> paymentList = new ArrayList<Object[]>();
                paymentList.add(objs);
                posDishDao.batchUpdate(insertPaymentSql.toString(), paymentList);
            }
            else
            {
                throw SystemException.getInstance(PosErrorCode.NOT_EXIST_JZID);
            }

            // 更新净收值
            StringBuilder updateBilldueSql = new StringBuilder();
            updateBilldueSql.append("update pos_bill bl set coupon_buy_price=bp.coupon_buy_price, due=bp.due, tenancy_assume=bp.tenancy_assume, third_assume=bp.third_assume, third_fee=bp.third_fee ");
            updateBilldueSql.append("from (select bp.tenancy_id,bp.store_id,bp.bill_num,coalesce(sum(bp.coupon_buy_price), 0) coupon_buy_price,coalesce(sum(bp.due), 0) due,coalesce(sum(bp.tenancy_assume), 0) tenancy_assume,coalesce(sum(bp.third_assume), 0) third_assume,coalesce(sum(bp.third_fee), 0) third_fee from pos_bill_payment bp group by bp.tenancy_id,bp.store_id,bp.bill_num) bp ");
            updateBilldueSql.append("where bl.tenancy_id=bp.tenancy_id and bl.store_id=bp.store_id and bl.bill_num=bp.bill_num and bl.tenancy_id=? and bl.store_id=? and bl.bill_num=?");

            posDishDao.update(updateBilldueSql.toString(), new Object[]
                    { tenancyId, storeId, bill_num });

            JSONObject printBuffer = new JSONObject();
            StringBuilder sql = new StringBuilder("SELECT array_to_string (ARRAY(select pbi.rwid from pos_bill_item pbi where pbi.bill_num=? order by rwid),',') as rwids");
            List<JSONObject> rwidsList = ordersDao.query4Json(tenancyId, sql.toString(), new Object[]{bill_num});
            String rwids = rwidsList.get(0).optString("rwids");

            // 厨打
            JSONObject printJson = new JSONObject();
            String mode = "0";// 0:下单
            String isPrint = "Y";
            printJson.put("tenancy_id", tenancyId);
            printJson.put("store_id", storeId);
            printJson.put("mode", mode);
            printJson.put("bill_num", bill_num);
            printJson.put("is_print", isPrint);
            printJson.put("rwids", rwids);
            printJson.put("pos_num", posNum);
            printJson.put("opt_num", optNum);
            printJson.put("report_date", reportDate);
            printJson.put("shift_id", shiftId);

            String formatState = "";
            String formatmode = "";

            JSONObject org = posDishDao.getOrganById(tenancyId, storeId);

            if (null != org && !org.isEmpty())
            {
                formatState = org.optString("format_state");
            }

            formatmode = posDishDao.getSysParameter(tenancyId, storeId, "ZCDCMS");

            if ("1".equals(formatState) && !"02".equals(formatmode))
            {
                String isPrintMenu = posDishDao.getSysParameter(tenancyId, storeId, "is_print_menu");

                if ("1".equals(isPrintMenu)) {
                    printJson.put("is_print_order", "Y");
                    printJson.put("print_type", "");
                    printJson.put("mode", "0");
                    printJson.put("print_code", SysDictionary.PRINT_CODE_1103);
                }
            }
            else
            {
                printJson.put("is_print_order", "N");
            }
            //第三方外卖下单（order_type ：WM1205） 、微信下单（order_type ：WX1401）
            //标记第三方外卖
            //printJson.put("order_type", "WM1205");
            // 缓冲厨打
            printBuffer.put("chefPrint", printJson);


            // 打印通用参数
            JSONObject orderJSON = new JSONObject();
            orderJSON.put("report_date", reportDate);
            orderJSON.put("shift_id", shiftId);
            orderJSON.put("pos_num", posNum);
            orderJSON.put("opt_num", optNum);
            orderJSON.put("bill_num", bill_num);
            orderJSON.put("mode", "0");
            //打印电子发票参数
            orderJSON.put("order_num", orderCode);

            orderJSON.put("chanel_serial_number", order_index);
            orderJSON.put("third_order_code", out_order_code);
            // 餐盒费
            orderJSON.put("package_fee", package_fee);
            // 配送费
            orderJSON.put("service_amount", service_amount);
            // 配送方式
            orderJSON.put("delivery_type", deliveryType);
            String need_invoice = "0";
            String url_path = null;
            if(null!=piaoTongQrCode && !"".equals(piaoTongQrCode)){
                need_invoice="1";
                // 生成电子发票二维码图片
                Timestamp timestamp = DateUtil.currentTimestamp();
                // 生成新的二维码文件名
                String fileName = bill_num + "@" + DateUtil.getYYYYMMDDHHMMSS(timestamp) + ".png";
                String strPhysicalPath = PathUtil.getWebRootPath() + File.separator;
                String strimgPath = "img/qr_code/invoice/" ;
                strPhysicalPath = strPhysicalPath + strimgPath;
                boolean result = QrCodeUtils.GenerateImage(piaoTongQrCode,strPhysicalPath,fileName);
                if(result){
                    String webPathConst = com.tzx.framework.common.constant.Constant.getSystemMap().get("WEB_PATH");
                    url_path = webPathConst + strimgPath+ fileName;
                }
            }

            orderJSON.put("need_invoice", need_invoice);
            orderJSON.put("url_path", url_path);
            orderJSON.put("channel", chanel);
            orderJSON.put("chanel_name", chanel_name);
            orderJSON.put("invoice_amount", actualPay);
            orderJSON.put("consigner", consigner);//订餐人
            orderJSON.put("consigner_phone", consignerPhone);//订餐人电话
            orderJSON.put("address", address);//订餐人地址
            if(Tools.hv(invoiceTitle) && invoiceTitle.contains("\n")){
                invoiceTitle = invoiceTitle.replace("\n","");
            }
            orderJSON.put("invoice_title", invoiceTitle);//发票title
            orderJSON.put("taxpayerid", taxer_id);//税号
            orderJSON.put("single_time", singleTime);//下单时间
            orderJSON.put("send_time", send_time);//送达时间
            orderJSON.put("onlineName", onlineName);//在线支付/ 货到付款
            // 结账单
            orderJSON.put("is_printbill", true);
            orderJSON.put("order_source", "ECO");

            //取餐一维码
            orderJSON.put("third_order_code", out_order_code);
            buildOrderOneDimCode(orderJSON,tenancyId,storeId);

            // 缓冲打印
            printBuffer.put("orderPrint", orderJSON);

            //将eco人员等信息保存 账单扩展表中
            StringBuffer saveBillExtends = new StringBuffer("insert into pos_bill_extends (tenancy_id,store_id,bill_num,eco_print_msg) values (?,?,?,?) ");
            posDishDao.update(saveBillExtends.toString(), new Object[]{tenancyId, storeId, bill_num, printBuffer.toString()});

            //打印成功查询异常表是否有记录进行删除
            StringBuffer selectOrderSql = new StringBuffer("select * from cc_order_exception where order_id ='"+orderCode+"'");
            List<JSONObject> selectOrderList = ordersDao.query4Json(tenancyId, selectOrderSql.toString());
            logger.info("打印成功查询异常表是否有记录进行删除==========query============================================" + selectOrderList.toString());
            if(null != selectOrderList && 0<selectOrderList.size()){
                StringBuffer deleteOrder = new StringBuffer("delete from cc_order_exception where order_id =?");
                int i = posDao.update(deleteOrder.toString(), new Object[]{orderCode});
                logger.error("打印成功查询异常表是否有记录进行删除成功"+i+"==========add============"+selectOrderList.toString());
                printBuffer.put("printStatus","success");
            }

            return printBuffer;
        } catch (SystemException e) {
            logger.error("Eco 接单异常:" + e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.error("Eco 接单失败:" + e.getMessage());
            throw e;
        }

    }

    /**
     * ECO异常打印
     * @param tenancyId
     * @param storeId
     * @param para
     * @param printCode
     * @throws Exception
     */
    @Override
    public synchronized void exceptionPrint(String tenancyId, int storeId, JSONObject para, String printCode) {
        try {
            String orderId = ParamUtil.getStringValueByObject(para, "orderid");
//            if (StringUtils.isEmpty(orderId)) {
//                orderId = ParamUtil.getStringValueByObject(para, "amoid");
//            }

            if (StringUtils.isEmpty(orderId)) {
                logger.info("eco接收到的订单id为空: -> " + orderId + " ,无法继续执行打印操作!");
                return ;
            }

            logger.info("接到eco异常打印参数========================== " + para.toString());

            // 是否打印电子发票二维码
            String piaoTongQrCode = ParamUtil.getStringValueByObject(para, "piaoTongQrCode");

            String sql = "SELECT * FROM cc_order_exception WHERE order_id = ? ";
            List<JSONObject> exceptionList = posDao.query4Json(tenancyId, sql, new Object[]{orderId});

            if (SysDictionary.PRINT_CODE_1206.equals(printCode)) {
                // 新增一条异常记录
                if (exceptionList.size() == 0) {
                    String orderSql = "insert into cc_order_exception (order_id) values (?)";
                    posDao.update(orderSql, new Object[]{orderId});
                }else {
                    if (!para.optBoolean("is_repeat_eco", false)) {
                        logger.info("异常单记录已存在,不再重复打印");
                        return ;
                    }

                }
            }else if (SysDictionary.PRINT_CODE_1207.equals(printCode)) {
                if (exceptionList.size()>0) {
                    // 删除异常单记录
                    ordersDao.execute(tenancyId, "delete from cc_order_exception where order_id = '"+ orderId +"'");
                    logger.info("删除eco异常单记录:" + orderId);
                } else {
                    if (!para.optBoolean("is_repeat_eco", false)) {
                        logger.info("异常单记录不存在,取消单不再重复打印");
                        return ;
                    }

                }
            }

            String date = DateUtil.currentTimestamp().toString();
            // 根据模板编号查询打印机和模板信息
            // 得到打印机id，是否区域，站点ID(新加)，打印机名称，IP地址，备打名称，备打地址，模板ID(hq_printer_model)
            List<JSONObject> printerList = posDao.getPrinter(tenancyId, storeId, printCode);

            String need_invoice = "0";
            String url_path = null;
            if(null!=piaoTongQrCode && !"".equals(piaoTongQrCode)){
                // 生成电子发票二维码图片
                Timestamp timestamp = DateUtil.currentTimestamp();
                need_invoice = "1";

                // 生成新的二维码文件名
                String fileName = orderId + "@" + DateUtil.getYYYYMMDDHHMMSS(timestamp) + ".png";
                String strPhysicalPath = PathUtil.getWebRootPath() + File.separator;
                String strImgPath = "img/qr_code/invoice/" ;
                strPhysicalPath = strPhysicalPath + strImgPath;
                boolean result = QrCodeUtils.GenerateImage(piaoTongQrCode,strPhysicalPath,fileName);
                if(result){
                    String webPathConst = com.tzx.framework.common.constant.Constant.getSystemMap().get("WEB_PATH");
                    url_path = webPathConst + strImgPath + fileName;
                }
            }

            if(null != printerList && 0<printerList.size()) {
                JSONObject printParam = printerList.get(0);
                //查询机构信息
                String organSql = "SELECT o.org_full_name,o.phone,o.address from organ o WHERE o.tenancy_id='"+tenancyId+"' and o.id='"+storeId+"';";
                List<JSONObject> organJson = posDao.query4Json(tenancyId, organSql.toString());
                if(null != organJson && 0<organJson.size()) {
                    JSONObject organObject = organJson.get(0);
                    JSONObject paramsEco = this.paramsData(para, printParam, organObject);
                    if (null != paramsEco){
                        paramsEco.put("templategid","");
                        paramsEco.put("tenancy_id",tenancyId);
                        paramsEco.put("store_id",String.valueOf(storeId));
                        paramsEco.put("bill_num","");
                        paramsEco.put("task_ID","");
                        paramsEco.put("sendtime",date);
                        paramsEco.put("printtime",date);
                        paramsEco.put("printnum","1");
                        paramsEco.put("need_invoice",need_invoice);
                        paramsEco.put("url_path",url_path);
                        logger.info("paramsEco参数是:-> " + paramsEco.toString());

                        // 生成打印任务
                        posDao.insertPrintTask(tenancyId, storeId, printParam, paramsEco, printCode);

                        try  {
                            // 发出打印通知
                            Data cjData = new Data();
                            cjData.setType(Type.PRINT);
                            cjData.setOper(Oper.print);
                            JSONObject printJson = new JSONObject();
                            printJson.put("print_address", printParam.optString("ip_com"));
                            List<JSONObject> list = new ArrayList<>();
                            list.add(printJson);
                            cjData.setData(list);

                            Comet4jUtil.sendMessage2All(Comet4jUtil.POS_PRINTER, JSONObject.fromObject(cjData).toString());
//								logger.info("打印分单成功：打印机【" + printerId + "】" + "，打印模板【" + printMode + "】");
                        } catch (Exception e) {
                            logger.error("分单失败：打印机【" + printParam.optInt("printer_id") + "】服务器连接异常", e);
                        }

                    } else {
                        logger.error("异常打印失败：接到参数为空 ==========");
                    }
                } else {
                    logger.info("异常打印失败：机构信息为空 ==========");
                }
            } else  {
                logger.error("异常打印失败: 打印机和模板信息为空 ==========");
            }
        } catch (Throwable e) {
            logger.error("Eco异常打印失败:" + e.getMessage(), e);
        }

    }

    /**
     * 组织异常打印参数数据
     * @param para
     * @param printParam
     * @param organParam
     * @return
     */
    private JSONObject paramsData(JSONObject para,JSONObject printParam,JSONObject organParam) {
        logger.info("接单参数:-> " + para.toString());
        JSONObject paramsEco = new JSONObject();
        JSONObject order = new JSONObject();
        JSONArray items = ParamUtil.getJSONArray(para, "items");
        JSONArray activityInfo = ParamUtil.getJSONArray(para, "activityInfo");

        String printerAddress = ParamUtil.getStringValueByObject(printParam, "ip_com");
        String printerName = ParamUtil.getStringValueByObject(printParam, "name");
        String sys_shop_name = ParamUtil.getStringValueByObject(organParam, "org_full_name");
        String sys_shop_address = ParamUtil.getStringValueByObject(organParam, "address");
        String sys_shop_telphone = ParamUtil.getStringValueByObject(organParam, "phone");

        String orderid = ParamUtil.getStringValueByObject(para, "orderid");
        String outorderid = ParamUtil.getStringValueByObject(para, "outorderid");
        Integer ispaid = ParamUtil.getIntegerValueByObject(para, "ispaid");
        Double totalprice = ParamUtil.getDoubleValueByObject(para, "totalprice");
        Double shopfee = ParamUtil.getDoubleValueByObject(para, "shopfee");
        Integer billtype = ParamUtil.getIntegerValueByObject(para, "billtype");
        String actual_amount = ParamUtil.getStringValueByObject(para, "actual_amount");

        if(actual_amount==null || "".equals(actual_amount))
        {
            actual_amount = String.valueOf(shopfee);
            if(para.containsKey("Print_info") && para.get("Print_info")!=null)
            {
                JSONObject printInfoJson = para.optJSONObject("Print_info");
                if(null!=printInfoJson && !printInfoJson.isEmpty() && printInfoJson.containsKey("Actual_amount"))
                {
                    actual_amount = String.valueOf(printInfoJson.optDouble("Actual_amount"));
                }
            }
        }

        if (actual_amount==null || "".equals(actual_amount)) {
            actual_amount = "0.00";
        }

        Double total = ParamUtil.getDoubleValueByObject(para, "total");
        String contact = ParamUtil.getStringValueByObject(para, "contact");
        String mobile = ParamUtil.getStringValueByObject(para, "mobile");
        String ispoifirstorder = ParamUtil.getStringValueByObject(para, "ispoifirstorder");
        String isfavorites = ParamUtil.getStringValueByObject(para, "isfavorites");
        String sname = ParamUtil.getStringValueByObject(para, "sname");
        String taxer_id = ParamUtil.getStringValueByObject(para, "taxer_id");
        String invoiceinfo = ParamUtil.getStringValueByObject(para, "invoiceinfo");
        Integer orderstatus = ParamUtil.getIntegerValueByObject(para, "orderstatus");
        Integer ordertype = ParamUtil.getIntegerValueByObject(para, "ordertype");
        Integer delivery_status = ParamUtil.getIntegerValueByObject(para, "delivery_status");
        Integer order_index = ParamUtil.getIntegerValueByObject(para, "order_index");
        String service_money = ParamUtil.getStringValueByObject(para, "service_money");
        String package_fee = ParamUtil.getStringValueByObject(para, "package_fee");
        String isPrintDiscount = ParamUtil.getStringValueByObject(para, "isPrintDiscount");
        String isDeliveryPay = ParamUtil.getStringValueByObject(para, "isDeliveryPay");
        String lin = ParamUtil.getStringValueByObject(para, "lin");
        String conTactMem = ParamUtil.getStringValueByObject(para, "conTactMem");
        String sourcename = ParamUtil.getStringValueByObject(para, "sourcename");
        String source = ParamUtil.getStringValueByObject(para, "source");
        Integer dinners_number = ParamUtil.getIntegerValueByObject(para, "dinners_number");
        String address = ParamUtil.getStringValueByObject(para, "address");
        Double order_delivery_pay = ParamUtil.getDoubleValueByObject(para, "order_delivery_pay");
        Integer delivery_type = ParamUtil.getIntegerValueByObject(para, "delivery_type");
        String createtime = ParamUtil.getStringValueByObject(para, "createtime");
        String diningtime = ParamUtil.getStringValueByObject(para, "diningtime");
        String comment = ParamUtil.getStringValueByObject(para, "comment");
        String is_print = ParamUtil.getStringValueByObject(para, "is_print");
        order.put("orderid",orderid);
        order.put("outorderid",outorderid);
        order.put("ispaid",String.valueOf(ispaid));
        order.put("totalprice",String.valueOf(totalprice));
        order.put("discount_amount", String.valueOf(DoubleHelper.psub(total,totalprice)));
        order.put("shopfee",String.valueOf(shopfee));
        order.put("billtype",String.valueOf(billtype));
        order.put("actual_amount", actual_amount);
        order.put("total",String.valueOf(total));
        order.put("contact",contact);
        order.put("mobile",mobile);
        order.put("ispoifirstorder",ispoifirstorder);
        order.put("isfavorites",isfavorites);
        order.put("sname",sname);
        order.put("invoiceinfo",invoiceinfo);
        order.put("taxer_id",taxer_id);
        order.put("orderstatus",String.valueOf(orderstatus));
        order.put("ordertype",String.valueOf(ordertype));
        order.put("delivery_status",String.valueOf(delivery_status));
        order.put("order_index",String.valueOf(order_index));
        order.put("service_money",service_money);
        order.put("package_fee",package_fee);
        order.put("isPrintDiscount",isPrintDiscount);
        order.put("isDeliveryPay",isDeliveryPay);
        order.put("lin",lin);
        order.put("conTactMem",conTactMem);
        order.put("sourcename",sourcename);
        order.put("source",source);
        order.put("dinners_number",String.valueOf(dinners_number));
        order.put("address",address);
        order.put("order_delivery_pay",String.valueOf(order_delivery_pay));
        order.put("delivery_type",String.valueOf(delivery_type));
        order.put("createtime",createtime);
        order.put("diningtime",diningtime);
        order.put("comment",comment);
        order.put("is_print",is_print);

        JSONArray array = new JSONArray();
        array.add(order);
        paramsEco.put("order",array);
        paramsEco.put("items",items);
        paramsEco.put("activityInfo",activityInfo);
        paramsEco.put("printerAddress",printerAddress);
        paramsEco.put("printerName",printerName);
        paramsEco.put("sys_shop_name",sys_shop_name);
        paramsEco.put("sys_shop_address",sys_shop_address);
        paramsEco.put("sys_shop_telphone",sys_shop_telphone);
        paramsEco.put("task_type","ECO_ERROR");
        return paramsEco;
    }

    /** 获取套餐明细
     * @param tenancyId
     * @param storeId
     * @param item
     * @param chanel
     * @param scale
     * @return
     * @throws Exception
     */
    private List<PosBillItem> getMeallistItem(String tenancyId, int storeId,PosBillItem item, String chanel, int scale) throws Exception
    {
        final int defaultScale = 4;

        List<Object> itemIdList = new ArrayList<>();
        JSONObject itemIds = new JSONObject();
        itemIds.put("item_id", item.getItem_id());
        // 套餐id
        itemIdList.add(itemIds);
        // 根据套餐id查找明细
        List<JSONObject> omboDetailsList = posDishDao.getItemComboDetails(tenancyId, storeId, chanel, itemIdList);
        if (null == omboDetailsList || omboDetailsList.isEmpty())
        {
            // 套餐明细项不存在
            throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_ITEM_COMBO_DETAILS);
        }

        List<PosBillItem> itemList = new ArrayList<PosBillItem>();

        // 菜品金额合计
        Double sumDetailItemAmount = 0d;
        PosBillItem maxDetailItem = null;

        Integer item_id = null;
        String item_num = null;
        String item_name = null;
        String item_english = null;
        String discount_state = null;
        Integer item_class_id = null;
        Integer itemUnitId = null;
        String item_unit_name = null;
        String pushmoney_way = null;
        Double proportion = null;
        String itemProperty = SysDictionary.ITEM_PROPERTY_MEALLIST;
        Integer setmeal_id = item.getItem_id();
        Integer setmeal_rwid = item.getItem_serial();
        for (int i = 0; i < omboDetailsList.size(); i++)
        {
            JSONObject details = JSONObject.fromObject(omboDetailsList.get(i));
            item_id = details.optInt("item_id");
            item_num = details.optString("item_code");
            item_name = "　" + details.optString("item_name");
            item_english = details.optString("item_english");
            discount_state = details.optString("is_discount");
            item_class_id = details.optInt("item_class");

            itemUnitId = details.optInt("item_unit_id");
            item_unit_name = details.optString("unit_name");

            pushmoney_way = details.optString("pushmoney_way");
            proportion = ParamUtil.getDoubleValueByObject(details, "proportion_money");
            if (proportion == null || proportion.isNaN()) {
                proportion = 0d;
            }

            Double assistNum = ParamUtil.getDoubleValueByObject(details, "combo_num");
            if (null == assistNum || 0 == assistNum)
            {
                assistNum = 1d;
            }

            Double detailsCount = DoubleHelper.mul(assistNum, item.getItem_count(), 2);
            Double detailItemPrice = details.optDouble("item_amount");

            Integer assistItemId = ParamUtil.getIntegerValueByObject(details, "combo_id");
            Integer orderNumber = details.optInt("combo_order");

            PosBillItem detail = new PosBillItem(tenancyId, storeId, item.getBill_num(), item.getDetails_id(), item_id, item_num, item_name, item_english, item.getReport_date(), itemUnitId, item_unit_name, item.getTable_code(), pushmoney_way, proportion, assistNum, null, item.getWaiter_num(),
                    detailItemPrice, detailsCount, item.getDiscount_rate(), discount_state, item.getDiscount_mode_id(), item_class_id, itemProperty, null, "*", setmeal_id, setmeal_rwid, null, item.getItem_time(), item.getItem_serial(), item.getItem_shift_id(), item.getItem_mac_id(), null, null,
                    item.getSale_mode(), item.getItem_taste(), assistItemId, 0, 0, item.getBatch_num(), orderNumber, item.getOpt_num(), null, item.getDiscount_reason_id(), null, null, null, null, null, 1d, "N", 0d, 100d, item.getDiscount_num(), null, null, null, null, null);
            detail.setDiscountr_amount(0d);
            detail.setAssist_money(0d);
            detail.setMethod_money(0d);
            itemList.add(detail);

            Double detailItemAmount = DoubleHelper.mul(detailItemPrice, detailsCount, scale);
            sumDetailItemAmount = DoubleHelper.add(sumDetailItemAmount, detailItemAmount, scale);

            Double maxDetailItemAmount = 0d;
            if (null != maxDetailItem)
            {
                maxDetailItemAmount = DoubleHelper.mul(maxDetailItem.getItem_price(), maxDetailItem.getItem_count(), scale);
            }
            if (0.01 == item.getItem_amount().doubleValue())
            {
                if ((1d == detailsCount.doubleValue() && (null == maxDetailItem || maxDetailItemAmount < detailItemAmount)) || (null == maxDetailItem && maxDetailItemAmount < detailItemAmount))
                {
                    maxDetailItem = detail;
                }
            }
            else
            {
                if (null == maxDetailItem || maxDetailItemAmount < detailItemAmount || (maxDetailItemAmount == detailItemAmount && maxDetailItem.getItem_count() > detailsCount))
                {
                    maxDetailItem = detail;
                }
            }
        }

        Double dRatio = DoubleHelper.div(item.getItem_amount(), sumDetailItemAmount, defaultScale);
        Double restItemAmount = item.getItem_amount();
        for (PosBillItem detail : itemList)
        {
            // 计算菜目金额
            Double detailItemAmount = DoubleHelper.mul(DoubleHelper.mul(detail.getItem_price(), detail.getItem_count(), defaultScale), dRatio, scale);

            // 计算单价
            Double detailItemPrice = DoubleHelper.div(detailItemAmount, detail.getItem_count(), scale);

            detailItemAmount = DoubleHelper.mul(detailItemPrice, detail.getItem_count(), scale);

            detail.setItem_amount(detailItemAmount);
            detail.setItem_price(detailItemPrice);

            restItemAmount = DoubleHelper.sub(restItemAmount, detailItemAmount, scale);
        }

        if (0 != restItemAmount.doubleValue())
        {
            Double detailItemAmount = DoubleHelper.add(maxDetailItem.getItem_amount(), restItemAmount, scale);
            // 计算单价
            Double detailItemPrice = DoubleHelper.div(detailItemAmount, maxDetailItem.getItem_count(), scale);

            maxDetailItem.setItem_amount(detailItemAmount);
            maxDetailItem.setItem_price(detailItemPrice);
        }

        return itemList;
    }


    /**计算平摊折扣金额
     * @param tenantId
     * @param storeId
     * @param scale
     * @throws Exception
     */
    private void updateDiscountkAmount(String tenantId, int storeId, List<PosBillItem> itemList, double discountAmount, int scale) throws Exception
    {
        final int defaultScale = 4;

        double subtotal = 0d; // 统计所有菜的折让金额
        PosBillItem maxItem = null;

        // 取总钱数,最大面额和对应的rwid
        int id = 1;
        for (PosBillItem item : itemList)
        {
            item.setId(id++);
            double itemAmount = item.getItem_amount();
            if (CommonUtil.hv(item.getReturn_count()) && item.getReturn_count() > 0d)
            {
                double itemPrice = DoubleHelper.div(item.getItem_amount(), item.getItem_count(), defaultScale);
                itemAmount = DoubleHelper.mul(itemPrice, DoubleHelper.sub(item.getItem_count(), item.getReturn_count().doubleValue(), defaultScale), scale);
                item.setItem_amount(itemAmount);
            }
            item.setDiscount_amount(0d);

            if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.getItem_property()))
            {
                subtotal = DoubleHelper.add(subtotal, itemAmount, defaultScale);

                if (null== maxItem || maxItem.getItem_amount() < itemAmount)
                {
                    maxItem = item;
                }
            }
        }

        // 计算并平摊折让的钱
        double averageTotal = 0d;
        Double ratio = DoubleHelper.div(discountAmount, subtotal, defaultScale);
        for (PosBillItem item : itemList)
        {
            if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.getItem_property()))
            {
                double itemDiscountAmount = DoubleHelper.mul(ratio, item.getItem_amount(), scale);
                averageTotal = DoubleHelper.add(averageTotal, itemDiscountAmount, defaultScale);
            }
        }
        double difference = DoubleHelper.sub(discountAmount, averageTotal, defaultScale);

        for (PosBillItem item : itemList)
        {
            if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.getItem_property()))
            {
                double itemDiscountAmount = DoubleHelper.mul(ratio, item.getItem_amount(), scale);
                if (maxItem.getId() == item.getId())
                {
                    itemDiscountAmount = DoubleHelper.add(itemDiscountAmount, difference, scale);
                }
                Double itemRealAmount = DoubleHelper.sub(item.getItem_amount(), itemDiscountAmount, scale);
                item.setDiscount_amount(itemDiscountAmount);
                item.setReal_amount(itemRealAmount);

                if (SysDictionary.ITEM_PROPERTY_SETMEAL.equals(item.getItem_property()))
                {
                    // 将减免金额平摊到套餐明细
                    PosBillItem dMaxItem = null;
                    double detailAverageTotal = 0d;

                    Double dRatio = DoubleHelper.div(itemDiscountAmount, item.getItem_amount(), defaultScale);
                    List<PosBillItem> detailList = new ArrayList<PosBillItem>();
                    for (PosBillItem detail : itemList)
                    {
                        if (SysDictionary.ITEM_PROPERTY_MEALLIST.equals(detail.getItem_property()) && item.getItem_id() == detail.getSetmeal_id() && item.getItem_serial() == detail.getSetmeal_rwid())
                        {
                            detailList.add(detail);
                            double detailDiscountAmount = DoubleHelper.mul(dRatio, detail.getItem_amount(), scale);
                            detailAverageTotal = DoubleHelper.add(detailAverageTotal, detailDiscountAmount, defaultScale);

                            if (null == dMaxItem || dMaxItem.getItem_amount() < detail.getItem_amount())
                            {
                                dMaxItem = detail;
                            }
                        }
                    }
                    double detailDifference = DoubleHelper.sub(itemDiscountAmount, detailAverageTotal, defaultScale);

                    for (PosBillItem detail : detailList)
                    {
                        double detailDiscountAmount = DoubleHelper.mul(dRatio, detail.getItem_amount(), scale);
                        if (dMaxItem.getId() == detail.getId())
                        {
                            detailDiscountAmount = DoubleHelper.add(detailDiscountAmount, detailDifference, scale);
                        }
                        Double detailRealAmount = DoubleHelper.sub(detail.getItem_amount(), detailDiscountAmount, scale);
                        detail.setDiscount_amount(detailDiscountAmount);
                        detail.setReal_amount(detailRealAmount);
                    }
                }
            }
        }
    }

    /** 添加菜品明细
     * @param billItemList
     * @throws Exception
     */
    private void addPosBillItemByOrder(List<PosBillItem> billItemList) throws Exception
    {
        logger.error("Eco菜品信息: -> " + billItemList.toString());
        if (billItemList.size() > 0)
        {
            StringBuilder itemSql = new StringBuilder("insert into pos_bill_item (tenancy_id,store_id,bill_num,details_id,item_id,item_num,item_name,item_english,report_date,item_unit_id,item_unit_name,table_code,pushmoney_way,proportion,"
                    + "assist_num,waiter_num,real_amount,item_amount,item_price,item_count,discount_rate,discount_state,discount_mode_id,item_class_id,item_property,item_taste,print_tag,setmeal_id,setmeal_rwid,"
                    + "is_setmeal_changitem,item_time,item_serial,item_shift_id,item_mac_id,order_remark,seat_num,sale_mode,assist_item_id,waitcall_tag,returngive_reason_id,batch_num,"
                    + "order_number,opt_num,manager_num,discount_reason_id,activity_id,activity_batch_num,activity_rule_id,activity_count,yrwid,print_count,default_state,discount_amount,discountr_amount) ");

            itemSql.append(" values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");

            List<Object[]> batchArgs = new ArrayList<Object[]>();
            for (PosBillItem item : billItemList)
            {

                batchArgs.add(new Object[]
                        { item.getTenancy_id(), item.getStore_id(), item.getBill_num(), item.getDetails_id(), item.getItem_id(), item.getItem_num(), item.getItem_name(), item.getItem_english(), item.getReport_date(), item.getItem_unit_id(), item.getItem_unit_name(), item.getTable_code(),
                                item.getPushmoney_way(), item.getProportion(), item.getAssist_num(), item.getWaiter_num(), item.getReal_amount(), item.getItem_amount(), item.getItem_price(), item.getItem_count(), item.getDiscount_rate(), item.getDiscount_state(), item.getDiscount_mode_id(),
                                item.getItem_class_id(), item.getItem_property(), item.getItem_taste(), item.getPrint_tag(), item.getSetmeal_id(), item.getSetmeal_rwid(), item.getIs_setmeal_changitem(), item.getItem_time(), item.getItem_serial(), item.getItem_shift_id(), item.getItem_mac_id(),
                                item.getOrder_remark(), item.getSeat_num(), item.getSale_mode(), item.getAssist_item_id(), item.getWaitcall_tag(), item.getReturngive_reason_id(), item.getBatch_num(), item.getOrder_number(), item.getOpt_num(), item.getManager_num(), item.getDiscount_reason_id(),
                                item.getActivity_id(), item.getActivity_batch_num(), item.getActivity_rule_id(), item.getActivity_count(), item.getYrwid(), item.getPrint_count(), item.getDefault_state(), item.getDiscount_amount(), item.getDiscountr_amount() });
            }
            posDishDao.batchUpdate(itemSql.toString(), batchArgs);
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public synchronized void orderBack(String tenancyId, int storeId, JSONObject para) throws Exception {
        logger.info("退单参数 " + para);

        // 查询门店业态,判断业态
        JSONObject organ = ordersDao.getOrganInfo(tenancyId, storeId);
        if (organ == null || organ.isEmpty())
        {
            throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_ORGAN);
        }

        // 营业日期、班次数据
        JSONObject jsonObject = this.getPosOptState(tenancyId, storeId, false);
        // 报表日期
        String reportDate = ParamUtil.getStringValueByObject(jsonObject, "report_date", false, null);

        if (!ordersDao.checkReportDate(tenancyId, storeId, reportDate))
        {
            throw SystemException.getInstance(PosErrorCode.CHECK_REPORT_DATE_ERROR);
        }
        // 班次
        Integer shiftId = ParamUtil.getIntegerValueByObject(jsonObject, "shift_id", false, null);

        // pos编号
        String posNum = ParamUtil.getStringValueByObject(jsonObject, "pos_num", false, null);
        // 操作人编号
        String optNum = ParamUtil.getStringValueByObject(jsonObject, "opt_num", false, null);

        Map<String, Object> map = (Map) para;
        JSONObject printJson = new JSONObject();
        // ECO订单号
        String orderCode = ParamUtil.getStringValue(map, "orderid", false, null);
        // 兼容另一个种格式的退单消息
        if (StringUtils.isEmpty(orderCode)) {
            orderCode = ParamUtil.getStringValue(map, "amoid", false, null);
        }

        // 平台订单号
        String out_order_code = ParamUtil.getStringValue(map, "outorderid", false, null);
        if (StringUtils.isEmpty(out_order_code)) {
            out_order_code = ParamUtil.getStringValue(map, "outid", false, null);
        }
        // 账单号
        String bill_num = ParamUtil.getStringValue(map, "check_id", false, null);
        // 订单状态
        String orderStatus = ParamUtil.getStringValue(map, "orderstatus", false, null);
        // 部分退款金额
        Double part_dish = ParamUtil.getDoubleValue(map, "part_dish", false, null);
        // 退单原因
        Integer reasonId = ParamUtil.getIntegerValue(map, "crid", false, null);

        // 菜品信息
        List<Map<String, Object>> items = null;
        if (para.containsKey("orderPart"))
        {
            items = (List<Map<String, Object>>) para.getJSONArray("orderPart");
        }

        // 根据账单号查询原账单(日结前后账单)
        JSONObject posBill = null;
        String queryBillSql = "SELECT * FROM v_pos_bill WHERE (report_date between ? and ?) and coalesce(bill_state,'') in ('','"+ SysDictionary.BILL_STATE_ZDQX02 +"') and order_num = ? ";
        List<JSONObject> posBillList = ordersDao.query4Json(tenancyId, queryBillSql, new Object[]{DateUtil.parseDate(DateUtil.getDateByDay(reportDate, -45)) , DateUtil.parseDate(reportDate), orderCode });
        if (posBillList != null && posBillList.size() > 0) {
            posBill = posBillList.get(0);
        }

        if (null == posBill || posBill.isEmpty())
        {
            // 账单不存在
            throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_BILL);
        }else {
            if (!posBill.optString("bill_num").equals(bill_num)) {
                logger.info("Eco 传入的check_id 与 POS 账单号不一致======");
                bill_num = posBill.optString("bill_num");
            }

        }

        // 部分退  如果items 不等于空 则是部分退
        if (null != items && items.size() > 0)
        {
            Map<String, Object> oDish = (Map<String, Object>) items.get(0);
            //根据之前原则部分退订单号 = 原订单号 +"T1"
            String refund_order_code = orderCode + "T1";

            //原订单是否存在部分退
            StringBuffer sqlBuf = new StringBuffer();
            sqlBuf.append(" SELECT * FROM v_pos_bill WHERE bill_state = '" + SysDictionary.BILL_STATE_BFT04 + "' AND order_num='" + refund_order_code + "' AND tenancy_id ='" + tenancyId + "' AND store_id =" + storeId);
            List<JSONObject> resultList = ordersDao.query4Json(tenancyId, sqlBuf.toString());

            // 原订单不存在部分退，则创建部分退账单
            if (resultList == null || resultList.size() == 0)
            {
                // 部分退账单号
                String billNum = null;
                //部分退序列号
                String serial_num = null;
                //部分退批次号
                String batch_num = null;
                Date report_date = DateUtil.parseDate(reportDate);
                try {
                    // 生成新的账单号
                    JSONObject object = new JSONObject();
                    object.element("store_id", storeId);
                    object.element("busi_date", report_date);
                    billNum = codeService.getCode(tenancyId, Code.POS_BILL_CODE, object);// 调用统一接口来实现
                    object.put("pos_num", com.tzx.orders.base.constant.Constant.WM_SERIAL_NUM);//外卖生产序列号前缀1000
                    serial_num = codeService.getCode(tenancyId, Code.POS_RECORD_CODE, object); // 调用统一接口来实现
                    batch_num = codeService.getCode(tenancyId, Code.POS_BATCH_NUM, object);// 调用统一接口来实现
                }
                catch (Exception e)
                {
                    throw new SystemException(PosErrorCode.GET_BILLNUM_SERVICE_ERROR);
                }


                Timestamp currentTime = DateUtil.currentTimestamp();

                //生成部分退账单
                StringBuffer saveBill = new StringBuffer("insert into pos_bill (tenancy_id,store_id,bill_num,batch_num,serial_num,report_date,table_code,guest,service_amount," +
                        "opentable_time,payment_time,open_pos_num,pos_num,waiter_num,open_opt,cashier_num,shift_id,item_menu_id,service_id,order_num,third_bill_code," +
                        "subtotal,bill_amount,payment_amount,difference,sale_mode,bill_state,bill_property,upload_tag,source, remark, return_amount,payment_state,shop_real_amount,settlement_type," +
                        "discount_reason_id,order_source )");

                saveBill.append(" values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");

                posDishDao.update(saveBill.toString(), new Object[]
                        {tenancyId, storeId, billNum, batch_num, serial_num, report_date, posBill.optString("table_code"), posBill.optInt("guest"), 0, currentTime, currentTime,
                                posNum, posNum, optNum, optNum, optNum, shiftId, posBill.optInt("item_menu_id"), 0, refund_order_code, out_order_code, -part_dish, -part_dish, -part_dish, 0.0,
                                posBill.optString("sale_mode"), SysDictionary.BILL_STATE_BFT04, SysDictionary.BILL_PROPERTY_CLOSED, 0,
                                posBill.optString("source"), null, 0d, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, -part_dish, posBill.optString("settlement_type"), 0, Constant.ECO_ORDER_SOURCE});

                //原单渠道
                String chanel = posBill.optString("source");
                //原单桌位号
                String tableCode = posBill.optString("table_code");
                //原单销售模式
                String sale_mode = posBill.optString("sale_mode");
                Integer discount_mode_id = posBill.optInt("discount_mode_id");
                List<Object[]> billItemList = new ArrayList<Object[]>();
                for (int k = 0; k < items.size(); k++)
                {
                    int item_serial = k + 1;
                    Map<String, Object> dish = (Map<String, Object>) items.get(k);
                    //tzx 系统中菜品item_code
                    Integer itemid = ParamUtil.getIntegerValue(dish, "itemid", false, null);
                    //boh 菜品id
                    Integer item_id = null;
                    //菜品名称
                    String itemname = ParamUtil.getStringValue(dish, "itemname", false, null);
                    //菜品规格名称
                    String properties = ParamUtil.getStringValue(dish, "properties", false, null);
                    //菜品数量
                    Integer itemcount = ParamUtil.getIntegerValue(dish, "itemcount", false, null);

                    //退菜信息
                    //部分退菜品单价
                    Double item_price = ParamUtil.getDoubleValue(dish, "originalprice", false, null);
                    //部分退菜品数量
                    Double partcount = ParamUtil.getDoubleValue(dish, "partcount", false, null);
                    //部分退菜品单价
                    Double refund_price = ParamUtil.getDoubleValue(dish, "refund_price", false, null);
                    //部分退菜品速记码
                    String kdishsno = ParamUtil.getStringValue(dish, "kdishsno", false, null);
                    //部分退菜品价格 = 单价* 个数
                    Double item_amount = DoubleHelper.mul(refund_price, partcount, 2);

                    JSONObject itemInfoJson = null;
                    //根据菜品编号查询菜品是否存在
                    // List<JSONObject> itemsList = posDishDao.getHqItemInfoByItemCode(tenancyId, storeId, SysDictionary.CHANEL_MD01,kdishsno);
                    List<JSONObject> itemsList = posDishDao.getHqItemInfoByItemCode(tenancyId, kdishsno);

                    if (null == itemsList || itemsList.size() == 0)
                    {
                        throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_ITEM).set("{0}", itemname);
                    }
                    else
                    {
                        if(itemsList.size()==1)
                        {
                            itemInfoJson = itemsList.get(0);
                            item_id = itemInfoJson.optInt("item_id");
                            properties = itemInfoJson.optString("unit_name");
                        }
                        else
                        {
                            if(null!=properties && !"".equals(properties))
                            {
                                for(JSONObject itemJson :itemsList)
                                {
                                    String unit_name = itemJson.optString("unit_name");
                                    if(properties.trim().equals(unit_name))
                                    {
                                        itemInfoJson = itemJson;
                                        item_id = itemJson.optInt("item_id");
                                        properties = itemJson.optString("unit_name");
                                        break;
                                    }
                                }
                            }

                            if(null==itemInfoJson)
                            {
                                for(JSONObject itemJson :itemsList){
                                    if("Y".equalsIgnoreCase(itemJson.optString("is_default"))){
                                        itemInfoJson = itemJson;
                                        item_id = itemInfoJson.optInt("item_id");
                                        properties = itemInfoJson.optString("unit_name");
                                        break;
                                    }
                                }
                            }

                        }
                    }


                    //是否套餐  1：是套餐，0：单品
                    Integer bsetmeal = ParamUtil.getIntegerValue(oDish, "bsetmeal", true, PosErrorCode.NOT_NULL_ITEM_BSETMEAL);
                    String itemProperty = SysDictionary.ITEM_PROPERTY_SINGLE;
                    List<JSONObject> omboDetailsList = null;
                    if (1 == bsetmeal)
                    {
                        // 套餐
                        itemProperty = SysDictionary.ITEM_PROPERTY_SETMEAL;
                        List<Object> itemIdList = new ArrayList<>();
                        JSONObject itemIds = new JSONObject();
                        itemIds.put("item_id", item_id);
                        itemIdList.add(itemIds);//套餐id
                        //根据套餐item_id查找套餐明细
                        omboDetailsList = posDishDao.getItemComboDetails(tenancyId, storeId, chanel, itemIdList);
                        if (null == omboDetailsList || omboDetailsList.isEmpty())
                        {
                            //套餐明细项不存在
                            throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_ITEM_COMBO_DETAILS);
                        }
                    }


                    Integer setmeal_id = null;
                    if (SysDictionary.ITEM_PROPERTY_SETMEAL.equalsIgnoreCase(itemProperty))
                    {
                        setmeal_id = ParamUtil.getIntegerValue(oDish, "setmeal_id", false, null);
                    }

                    Integer details_id = itemInfoJson.optInt("details_id");
                    String item_num = itemInfoJson.optString("item_code");
                    Integer item_class_id = itemInfoJson.optInt("item_class");
                    String item_name = itemInfoJson.optString("item_name");
                    String item_english = itemInfoJson.optString("item_english");
                    Integer itemUnitId = itemInfoJson.optInt("unitid");
                    String pushmoney_way = itemInfoJson.optString("pushmoney_way");
                    Double proportion = itemInfoJson.optDouble("proportion_money",0);
                    // 是否可以折扣
                    String discount_state = itemInfoJson.optString("is_discount");

                    Object[] objs = new Object[]
                            {tenancyId, storeId, billNum, details_id, item_id, item_num, item_name, item_english, report_date, itemUnitId, properties, tableCode, pushmoney_way, proportion,
                                    null, optNum, -item_amount, refund_price, -partcount, 1, discount_state, discount_mode_id, item_class_id,
                                    itemProperty, null, "*", setmeal_id, null, null, currentTime, item_serial, shiftId, posNum, "", 0, sale_mode, 0, null, 0, batch_num, null, optNum, null, 0, 0, null, 0, 0, null, 1, "N", -partcount};

                    billItemList.add(objs);

                    if (omboDetailsList != null)
                    {

                        Double sum_detail_item_amount =0d;
                        for (int i = 0; i < omboDetailsList.size(); i++)
                        {
                            JSONObject details = JSONObject.fromObject(omboDetailsList.get(i));
                            Integer count = details.optInt("combo_num");

                            Double detail_item_amount = details.optDouble("item_amount");
                            //套餐明细单价  =  套餐明细价格 /个数
                            Double detail_item_price = DoubleHelper.div(detail_item_amount, Double.valueOf(count), 2);
                            sum_detail_item_amount =DoubleHelper.add(sum_detail_item_amount,detail_item_price,2);
                        }

                        for (int i = 0; i < omboDetailsList.size(); i++)
                        {
                            JSONObject details = JSONObject.fromObject(omboDetailsList.get(i));
                            item_id = details.optInt("item_id");
                            setmeal_id = details.optInt("iitem_id");
                            item_num = details.optString("item_code");
                            item_name = details.optString("item_name");
                            item_english = details.optString("item_english");

                            itemUnitId = details.optInt("item_unit_id");
                            String item_unit_name = details.optString("unit_name");

                            pushmoney_way = details.optString("pushmoney_way");
                            proportion = details.optDouble("proportion_money",0);
                            Integer count = details.optInt("combo_num");

                            Double detail_item_amount = details.optDouble("item_amount");
                            //套餐明细单价  =  套餐明细价格 /个数
                            //Double detail_item_price = DoubleHelper.div(detail_item_amount, Double.valueOf(count), 2);

                            Double detailsRate = DoubleHelper.div(detail_item_amount, sum_detail_item_amount, 4);//套餐明细在套餐项占比

                            //套餐明细价格
                            detail_item_amount = DoubleHelper.mul(refund_price, detailsRate, 2);

                            //套餐明细单价  =  套餐明细价格 /个数
                            Double detail_item_price = DoubleHelper.div(detail_item_amount, Double.valueOf(count), 2);

                            item_class_id = details.optInt("item_class");

                            Integer orderNumber = details.optInt("combo_order");
                            itemProperty = SysDictionary.ITEM_PROPERTY_MEALLIST;
                            discount_mode_id = null;

                            objs = new Object[]
                                    {tenancyId, storeId, billNum, details_id, item_id, item_num, item_name, item_english, report_date, itemUnitId, item_unit_name, tableCode, pushmoney_way, proportion,
                                            null, optNum, -detail_item_amount, detail_item_price, -count, 1, null, 0, item_class_id,
                                            itemProperty, null, "*", setmeal_id, null, null, currentTime, null, shiftId, posNum,
                                            "", 0, sale_mode, 0, null, 0, batch_num, orderNumber, optNum, null, 0, 0, null, 0, 0, null, 1, "N", -count};
                            billItemList.add(objs);
                        }
                    }
                }


                if (billItemList.size() > 0)
                {
                    StringBuilder itemSql = new StringBuilder(
                            "insert into pos_bill_item (tenancy_id,store_id,bill_num,details_id,item_id,item_num,item_name,item_english,report_date,item_unit_id,item_unit_name,table_code,pushmoney_way,proportion," +
                                    "assist_num,waiter_num,item_amount,item_price,item_count,discount_rate,discount_state,discount_mode_id,item_class_id,item_property,item_taste,print_tag,setmeal_id,setmeal_rwid," +
                                    "is_setmeal_changitem,item_time,item_serial,item_shift_id,item_mac_id,order_remark,seat_num,sale_mode,assist_item_id,waitcall_tag,returngive_reason_id,batch_num," +
                                    "order_number,opt_num,manager_num,discount_reason_id,activity_id,activity_batch_num,activity_rule_id,activity_count,yrwid,print_count,default_state,return_count) ");

                    itemSql.append(" values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
                    posDishDao.batchUpdate(itemSql.toString(), billItemList);
                }


                //支付记录
                StringBuilder paySql = new StringBuilder(
                        " insert into pos_bill_payment(tenancy_id,store_id,bill_num,report_date,shift_id,pos_num,cashier_num,table_code,type,jzid,name,name_english,rate,amount,count,currency_amount,number,phone,is_ysk,customer_id,bill_code,remark,last_updatetime,upload_tag,payment_state,batch_num,param_cach,more_coupon,yjzid) ");
                paySql.append("select tenancy_id,store_id,?,report_date,shift_id,pos_num,cashier_num,table_code,type,jzid,name,name_english,rate,?,-count,?,number,phone,is_ysk,customer_id,bill_code,remark,?,upload_tag,payment_state,batch_num,param_cach,more_coupon,yjzid from pos_bill_payment where bill_num=? and store_id=?");
                posDishDao.update(paySql.toString(), new Object[]{billNum, -part_dish, -part_dish, currentTime, bill_num, storeId});

                List<JSONObject> list = ordersDao.findPosBillExtendsByBillNum(tenancyId, storeId, bill_num);
                if (list != null && list.size() > 0)
                {
                    JSONObject jsonObject1 = list.get(0);
                    JSONObject json = JSONObject.fromObject(jsonObject1.get("eco_print_msg"));
                    JSONObject orderJSON = json.optJSONObject("orderPrint");
                    orderJSON.put("report_date", reportDate);
                    orderJSON.put("shift_id", shiftId);
                    orderJSON.put("pos_num", posNum);
                    orderJSON.put("opt_num", optNum);
                    orderJSON.put("bill_num", bill_num);
                    orderJSON.put("mode", "0");
                    orderJSON.put("order_num", orderCode);
                    orderJSON.put("refound_num", refund_order_code);//退款订单号
                    orderJSON.put("refound_bill_num", billNum);//退款账单号

                    Double payment_amount = posBill.optDouble("payment_amount");
                    if (payment_amount != null && part_dish != null)
                    {
                        Double actualPay = DoubleHelper.sub(payment_amount, part_dish, 2);
                        orderJSON.put("invoice_amount", actualPay);//发票金额
                    }
                    JSONObject printBuffer = new JSONObject();
                    //缓冲打印
                    printBuffer.put("orderPrint", orderJSON);
                    this.refundOrderBuffer(tenancyId, storeId, printBuffer);
                }
            }
        }
        else
        {
            // 整单退
            orderCode = orderCode + "T1";
            //查询部分退是否存在
            StringBuffer sqlBuf = new StringBuffer();
            sqlBuf.append(" SELECT * FROM v_pos_bill WHERE bill_state = '" + SysDictionary.BILL_STATE_BFT04 + "' AND order_num='" + orderCode + "' AND tenancy_id ='" + tenancyId + "' AND store_id =" + storeId);
            List<JSONObject> resultList = ordersDao.query4Json(tenancyId, sqlBuf.toString());
            //原账单存在部分退账单，冲减该部分退账单
            if (resultList != null && resultList.size() > 0)
            {
                //冲减部分退
                JSONObject bill = resultList.get(0); //部分退账单
                if (null != bill && !bill.isEmpty())
                {
                    OrderState orderState = new OrderState();
                    orderState.setBill_num(bill.optString("bill_num"));
                    //冲减部分退账单
                    this.writeDownsRefundOrders(tenancyId, storeId, orderState);
                }
            }

            JSONObject cancelJson = new JSONObject();
            cancelJson.put("report_date", reportDate);
            cancelJson.put("opt_num", optNum);
            cancelJson.put("pos_num", posNum);
            cancelJson.put("shift_id", shiftId);
            cancelJson.put("bill_num", posBill.optString("bill_num"));
            cancelJson.put("reason_id", reasonId);
            cancelJson.put("manager_id", "0");
            cancelJson.put("opt_login_number", "0");
            cancelJson.put("is_online_payment", "1");

            List<JSONObject> cancelList = new ArrayList<JSONObject>();
            cancelList.add(cancelJson);

            Data cancelData = Data.get();
            cancelData.setTenancy_id(tenancyId);
            cancelData.setStore_id(storeId);
            cancelData.setType(Type.CANCBILL);
            cancelData.setData(cancelList);

            Data cancelResultData = Data.get();
            if (posBill != null && !posBill.isEmpty())
            {
                posDishService.cancelBill(cancelData, codeService, cancelResultData, printJson);
            }
            else
            {
                // 隔日退款冲减操作
                posDishService.refundOrderCancelBill(cancelData, codeService, cancelResultData, printJson);
            }

            // 判断打印方式
            boolean isNewPrint = posPrintNewService.isNONewPrint(tenancyId, storeId);

            if (printJson != null)
            {
                printJson.put("refound_bill_num", "");
                printJson.put("refound_num", "");
                if (isNewPrint)
                {
                    //如果启用新的打印模式
                    boolean isPrintReturn = true;//是否打印退菜单
                    boolean isPrintKitchen = true;//是否打印厨打取消单
                    String printMode = SysDictionary.PRINT_CODE_1202;//异常随餐单打印参数，默认正常取消 1202


                    //是否打印退菜单
                    if (isPrintReturn)
                    {
                        posPrintNewService.posPrintByFunction(printJson.optString("tenancy_id"), printJson.optInt("store_id"), FunctionCode.CANCBILL, printJson);
                    }

                    // 打印取消随餐单
                    String is_invoice = printJson.optString("is_invoice");
                    printJson.put("is_invoice", "0");
                    List<JSONObject> list = ordersDao.findPosBillExtendsByBillNum(tenancyId, storeId, bill_num);
                    if (list != null && list.size() > 0) {
                        JSONObject jsonObject1 = list.get(0);
                        JSONObject json = JSONObject.fromObject(jsonObject1.get("eco_print_msg"));
                        JSONObject orderJSON = json.optJSONObject("orderPrint");
                        //printJson.put("chanel_serial_number", orderJSON.optString("chanel_serial_number"));
                        printJson.putAll(orderJSON);
                    }

                    if (SysDictionary.PRINT_CODE_1202.equals(printMode))
                    {
                        JSONObject newPrint = new JSONObject();
                        printJson.put("bill_num",printJson.optString("new_bill_num"));
                        newPrint.putAll(printJson);
                        newPrint.put("order_num", posBill.optString("order_num"));
                        posPrintNewService.posPrintByMode(printJson.optString("tenancy_id"), printJson.optInt("store_id"), printMode, newPrint);
                    }
                    else
                    {
                        posPrintNewService.posPrintByMode(printJson.optString("tenancy_id"), printJson.optInt("store_id"), printMode, printJson);
                    }
                }
                else
                {
                    List<Integer> ids = posPrintService.orderRetreat(printJson.optString("tenancy_id"), printJson.optString("bill_num"), printJson.optInt("store_id"),
                            printJson.optString("oper"), printJson.optString("oper_type"), printJson.optString("rwids"));
                    posPrintService.orderPrint(printJson.optString("tenancy_id"), printJson.optString("bill_num"), printJson.optInt("store_id"), ids);
                }
            }
        }
    }

    @Override
    public void orderRemind(String tenancyId, int storeId, JSONObject para) throws Exception {
        JSONObject organ = ordersDao.getOrganInfo(tenancyId, storeId);
        if (organ == null || organ.isEmpty()) {
            throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_ORGAN);
        }

        //1-新订单 2-申请退单 3-催单
        String orderCode = ParamUtil.getStringValueByObject(para, "orderid", false, null);//订单号
        String type = ParamUtil.getStringValueByObject(para, "type", false, null);//订单号

        String orderState = Constant.ORDER_STATE_NEW;
        if ("1".equals(type))
        {
            // 到店订单提醒
            orderState = Constant.ORDER_STATE_TOSHOP;
        }
        else if ("2".equals(type))
        {
            // 申请退单提醒
            orderState = Constant.ORDER_STATE_REFUND_APPLY;
        }

        this.sendMsg(tenancyId, storeId, orderCode, orderState, false);

//        List<String> msgList = new ArrayList<String>();
//        msgList.add(orderCode);
//        JSONObject msg = new JSONObject();
//        msg.put("order_state", orderState);
//        msg.put("order_source", "ECO");
//        msg.put("order_code", msgList);
//
//        List<JSONObject> cometList = new ArrayList<JSONObject>();
//        cometList.add(msg);
//        Data cometData = new Data();
//        cometData.setTenancy_id(tenancyId);
//        cometData.setStore_id(storeId);
//        cometData.setType(Type.ORDER);
//        cometData.setOper(Oper.add);
//        cometData.setSuccess(true);
//        cometData.setData(cometList);
//
//        //POS用到UDP
//        String port = new CometDataNoticeClientRunnable(cometData).getPort();
//        SendUdpToPos.sendMessagePos(JSONObject.fromObject(cometData).toString(), port);
//        logger.debug("----推送消息：" + JSONObject.fromObject(cometData).toString() + ", 端口号：" + port);

    }

    @Override
    public void orderNum(String tenancyId, int storeId, JSONObject para) throws Exception{
        JSONObject organ = ordersDao.getOrganInfo(tenancyId, storeId);
        if (organ == null || organ.isEmpty()) {
            throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_ORGAN);
        }

        Integer neworder =ParamUtil.getIntegerValueByObject(para, "neworder1", false, null);
        Integer waitorder =ParamUtil.getIntegerValueByObject(para, "waitorder1", false, null);
        Integer waitorder_hand =ParamUtil.getIntegerValueByObject(para, "waitorder_hand1", false, null);
        Integer isEcoState = ParamUtil.getIntegerValueByObject(para, "is_eco_state", false, null);
        logger.info("newOrder: " + neworder + " waitOrder: " + waitorder + " | " + waitorder_hand);

        boolean isRefresh = false;

        String orderState = Constant.ORDER_STATE_NEW;
        if (neworder != null && neworder > 0)
        {
            // 到店订单提醒
            logger.info("到店订单提醒=================");
            orderState = Constant.ORDER_STATE_TOSHOP;
        } else {
            if (null != waitorder && null != waitorder_hand)
            {
                if ((waitorder - waitorder_hand) > 0)
                {
                    // 申请退单提醒
                    logger.info("申请退单提醒=================");
                    orderState = Constant.ORDER_STATE_REFUND_RECEIVED;
                }
            }
        }

        if (!Constant.ORDER_STATE_NEW.equalsIgnoreCase(orderState)){
            long lastTime = ecoOrderTime.get();
            logger.info("当前ECO最后接单时间:" + DateUtil.formatTime(new Date(lastTime)));
            if (isEcoState!=null && isEcoState == 1){
                if ((System.currentTimeMillis() - lastTime)/1000L > 60*5){
                    isRefresh = true;
                }
            } else {
                // 正常ECO推送订单数量ordernum 更新最后接单时间;
                ecoOrderTime.set(System.currentTimeMillis());
                logger.info("更新Eco最后接单时间:" + DateUtil.formatTime(new Date(ecoOrderTime.get())));
            }

        }

        this.sendMsg(tenancyId, storeId, null, orderState, isRefresh);

//        List<String> msgList = new ArrayList<String>();
//
//        JSONObject msg = new JSONObject();
//        msg.put("order_state", orderState);
//        msg.put("order_source", "ECO");
//        msg.put("order_code", msgList);
//
//        List<JSONObject> cometList = new ArrayList<JSONObject>();
//        cometList.add(msg);
//        Data cometData = new Data();
//        cometData.setTenancy_id(tenancyId);
//        cometData.setStore_id(storeId);
//        cometData.setType(Type.ORDER);
//        cometData.setOper(Oper.add);
//        cometData.setSuccess(true);
//        cometData.setData(cometList);
//
//        //POS用到UDP
//        String port = new CometDataNoticeClientRunnable(cometData).getPort();
//        SendUdpToPos.sendMessagePos(JSONObject.fromObject(cometData).toString(), port);
//        logger.debug("----推送消息：" + JSONObject.fromObject(cometData).toString() + ", 端口号：" + port);
    }


    @Override
    public void sendMsg(String tenancyId,Integer storeId, String orderCode,String orderState,boolean isRefresh)
    {
        List<String> msgList = new ArrayList<String>();
        if (CommonUtil.hv(orderCode))
        {
            msgList.add(orderCode);
        }

        JSONObject msg = new JSONObject();
        msg.put("is_refresh", isRefresh);
        if(orderState.equals(ORDER_STATE_EXCEPTION) )
        {
            // 接单异常提醒
            msg.put("order_state",Constant.ORDER_STATE_EXCEPTION);
        }
        else if(orderState.equals(ORDER_STATE_TOSHOP) )
        {
            // 到店订单提醒
            msg.put("order_state", Constant.ORDER_STATE_TOSHOP);
        }
        else if (orderState.equals(ORDER_STATE_REFUND_RECEIVED) )
        {
            // 申请退单提醒
            msg.put("order_state", Constant.ORDER_STATE_REFUND_RECEIVED);
        }else
        {
            return;
        }

        msg.put("order_source", "ECO ");
        msg.put("order_code", msgList);

        List<JSONObject> cometList = new ArrayList<JSONObject>();
        cometList.add(msg);
        Data cometData = new Data();
        cometData.setTenancy_id(tenancyId);
        cometData.setStore_id(storeId);
        cometData.setType(Type.ORDER);
        cometData.setOper(Oper.add);
        cometData.setSuccess(true);
        cometData.setData(cometList);

        // POS用到UDP
        String port = new CometDataNoticeClientRunnable(cometData).getPort();
        SendUdpToPos.sendMessagePos(JSONObject.fromObject(cometData).toString(), port);
        logger.info("----推送消息：" + JSONObject.fromObject(cometData).toString() + ", 端口号：" + port);
    }

    @Override
    public void sendMsgForWindow(String msg, String conTactMem)
    {
        List<JSONObject> noticeList = new ArrayList<JSONObject>();
        JSONObject noticeJson = new JSONObject();
        noticeJson.put("message", msg);
        noticeJson.put("conTactMem", conTactMem);
        noticeList.add(noticeJson);
        Comet4jUtil.comet4J(noticeList, Type.ECO_BILL_EXCEPTION, Oper.notice);
    }

    @Override
    public void printOrderBufferOfECO(String tenentId, int storeId, JSONObject json) throws Exception {
        JSONObject orderPrint = json.optJSONObject("orderPrint");
        String channel = orderPrint.optString("channel");

        boolean isNewPrint = posPrintNewService.isNONewPrint(tenentId, storeId);
        JSONObject chefPrint = json.optJSONObject("chefPrint");
        // 厨打
        if (chefPrint != null && chefPrint.optString("mode").equalsIgnoreCase("0") && chefPrint.optString("is_print").equalsIgnoreCase("Y")) {
            if ((false == "1".equals(posDishDao.getSysParameter(tenentId, storeId, "SFQDKVS"))) || "1".equals(posDishDao.getSysParameter(tenentId, storeId, "IS_KVS_PRINT_DISH"))){
                // 如果启用kvs 并且  设置kvs不打印厨打 ，外卖 不进行打印厨打
                if (isNewPrint) {
                    posPrintNewService.posPrintByFunction(tenentId, storeId, FunctionCode.ORDERING, chefPrint);
                } else {
                    posPrintService.orderChef(tenentId, chefPrint.optString("bill_num"), storeId, "0");
                }
                logger.info("打印厨打::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::");
            }
        }

        String dzfpSysValue = OrderUtil.getSysPara(tenentId,storeId,DZFP_DSFWM);
        boolean isAutoGenerateInvoice = "2".equals(dzfpSysValue)||("1".equals(dzfpSysValue) && "1".equals(orderPrint.optString("need_invoice")));
        if (isAutoGenerateInvoice) {
            // 1:电子 2:普通
            orderPrint.put("is_invoice", "1");
        }

        if(CommonUtil.isNullOrEmpty(orderPrint.optString("url_path")) && isAutoGenerateInvoice){
            orderPrint.put("is_close_bill","true");
            orderPrint.put("is_order_print",true);
            JSONObject invJo = posDao.getInvoiceInfo(tenentId, storeId, orderPrint);
            if (!Tools.isNullOrEmpty(invJo) && "2".equals(invJo.optString("invoice_type"))
                    && Tools.hv(invJo.optString("url_content")) && invJo.optString("url_content").length()>0){
                String strUrlPath = this.generateInvoiceQrCodeFile(tenentId, storeId, invJo);
                orderPrint.put("url_content", invJo.optString("url_content"));
                orderPrint.put("url_path", strUrlPath);

            } else {
                // 创建新的电子发票二维码
                makeInvoiceUrlForOrder(tenentId, storeId, orderPrint);
            }

            if (!CommonUtil.hv(orderPrint.optString("url_content")) && !CommonUtil.hv(orderPrint.optString("url_path"))) {
                orderPrint.put("is_invoice", "0");
                logger.info("未生成有效的电子发票二维码!");
            }

            logger.info("需要开电子发票:" + (1==orderPrint.getInt("is_invoice")?"电子":"纸质"));
        }

        // 需要打印结账单/随餐单
        if (orderPrint.optBoolean("is_printbill")) {
            logger.info("==========" + isNewPrint + " 打印随餐单或结账单参数:" + orderPrint);
            // 随餐单
            if (isNewPrint) {
                posPrintNewService.posPrintByMode(tenentId, storeId, Constant.ORDER_PRINT_CODE_1201, orderPrint);
            } else {
                int count = 2;
                if (Tools.hv(OrderUtil.getSysPara(tenentId, storeId, Constant.THIRD_SCD_COUNT))) {
                    try {
                        count = Integer.valueOf(OrderUtil.getSysPara(tenentId, storeId, Constant.THIRD_SCD_COUNT));
                    } catch (NumberFormatException e) {
                        logger.warn("随餐单打印份数设置不正确，使用默认值[2]");
                    }
                }

                for (int i = 0; i < count; i++) {
                    orderPrint.put("print_code", Constant.ORDER_PRINT_CODE_1201);
                    // 采用补打的处理
                    orderPrint.put("is_repeat_1201", "1");
                    printBill(tenentId, storeId, orderPrint);
                }
            }
            logger.info("打印随餐单::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::");

        }
    }

    @Override
    public void ecoStateQuery(Data param) throws Exception{
        JSONObject paramJson = JSONObject.fromObject(param.getData().get(0));
        // http://ceshi-node1.acewill.cn/w/2018051572/157218520
        String url = paramJson.optString("url");
        if (CommonUtil.hv(url)) {
            String shopKey = url.substring(url.lastIndexOf("/")+1);
            String rootUrl = url.substring(0, url.indexOf("/w/"));

            String apiUrl = rootUrl + "/api/shopapi/getNodeJsNum?shopkey=" + shopKey;
            String orderNums = HttpUtil.sendGetRequest(apiUrl, "utf-8");
            logger.info("ECO状态轮询: " + apiUrl + " \n 返回结果:" + orderNums);
            if (CommonUtil.hv(orderNums) && orderNums.startsWith("{")){
                JSONObject orderJson = JSONObject.fromObject(orderNums);
                JSONObject orderData = orderJson.optJSONObject("data");
                orderData.put("is_eco_state", 1);
                this.orderNum(param.getTenancy_id(), param.getStore_id(), orderData);
            }
        }

    }

    @Override
    public void buildOrderOneDimCode(JSONObject orderJSON, String tenancyId, Integer storeId) throws Exception {

        if ("0".equalsIgnoreCase(OrderUtil.getSysPara(tenancyId, storeId, "print_dimensional_code"))) {
            logger.warn("外卖随餐单设置为不打印出餐一维码");
            return;
        }

        String oneDimOrderCode="";

        if (orderJSON.containsKey("third_order_code")) {

            //美团一维码生成
            if (SysDictionary.CHANEL_MT08.equals(orderJSON.optString("channel")) ||
                    SysDictionary.CHANEL_MT11.equals(orderJSON.optString("channel"))) {
                    String encode = NumericConvertUtils.toOtherNumberSystem62(Long.parseLong(orderJSON.optString("third_order_code")), 62);
                    oneDimOrderCode = "M" + StringUtils.reverse(encode);

            }

            //饿了么一维码生成
            if (SysDictionary.CHANEL_EL09.equals(orderJSON.optString("channel"))) {
                    oneDimOrderCode = "E" + NumericConvertUtils.toOtherNumberSystem58(Long.parseLong(orderJSON.optString("third_order_code")), 58);
            }

            orderJSON.put("one_dim_order_code", oneDimOrderCode);
        }else {
            logger.warn("第三方订单号为空，外卖随餐单无法生成取餐一维码");
        }


    }
}
