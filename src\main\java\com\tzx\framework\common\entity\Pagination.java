package com.tzx.framework.common.entity;

import java.io.Serializable;

public class Pagination implements Serializable
{
	/**
	 * 
	 */
	private static final long	serialVersionUID	= 1L;
	
	private int		pageno;
	private int		pagesize;
	private int		totalcount;
	private String	orderby;
	private boolean	asc;

	public int getPageno()
	{
		return pageno;
	}

	public void setPageno(int pageno)
	{
		this.pageno = pageno;
	}

	public int getPagesize()
	{
		return pagesize;
	}

	public void setPagesize(int pagesize)
	{
		this.pagesize = pagesize;
	}

	public int getTotalcount()
	{
		return totalcount;
	}

	public void setTotalcount(int totalcount)
	{
		this.totalcount = totalcount;
	}

	public String getOrderby()
	{
		return orderby;
	}

	public void setOrderby(String orderby)
	{
		this.orderby = orderby;
	}

	public boolean isAsc()
	{
		return asc;
	}

	public void setAsc(boolean asc)
	{
		this.asc = asc;
	}

}
