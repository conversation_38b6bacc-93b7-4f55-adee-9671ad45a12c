package com.tzx.clientorder.acewillwechat.bo.imp;

import java.beans.PropertyDescriptor;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import net.sf.json.JsonConfig;
import net.sf.json.util.PropertyFilter;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.tzx.base.bo.PosCodeService;
import com.tzx.base.common.exception.AcewillErrorCode;
import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.ErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.PropertiesLoader;
import com.tzx.pos.asyn.pool.ThreadPool;
import com.tzx.pos.asyn.thread.OrderingPrintThread;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.constant.WLifeConstant;
import com.tzx.pos.base.util.CacheTableUtil;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.bo.PosDishService;
import com.tzx.clientorder.acewillwechat.bo.InsertOrderService;
import com.tzx.clientorder.acewillwechat.bo.OrderService;
import com.tzx.clientorder.common.entity.AcewillInsertOrder;
import com.tzx.clientorder.common.entity.AcewillOrder;
import com.tzx.clientorder.common.entity.AcewillOrderAssist;
import com.tzx.clientorder.common.entity.AcewillOrderInfo;
import com.tzx.clientorder.common.entity.AcewillOrderItem;
import com.tzx.clientorder.common.entity.AcewillOrderItemMethod;
import com.tzx.clientorder.common.entity.AcewillOrderMember;
import com.tzx.clientorder.common.entity.AcewillOrderMemo;
import com.tzx.clientorder.common.entity.AcewillOrderNormalitem;
import com.tzx.clientorder.common.entity.AcewillOrderSetmeal;
import com.tzx.clientorder.common.entity.AcewillOrderSetmealItem;
import com.tzx.clientorder.common.entity.AcewillOrderUpGrade;
import com.tzx.clientorder.common.entity.AcewillPosBillMember;
import com.tzx.clientorder.acewillwechat.po.springjdbc.dao.AcewillInsertOrderDao;
import com.tzx.pos.service.servlet.PosDishServlet;

@Service(InsertOrderService.NAME)
public class InsertOrderServiceImpl implements InsertOrderService {

	@Resource(name = PosDishService.NAME)
	PosDishService posDishService;
	@Resource(name = PosDishServlet.NAME)
	PosDishServlet posDishServlet;
	@Resource(name = AcewillInsertOrderDao.NAME)
	AcewillInsertOrderDao insertOrderDao;

	@Resource(name = PosCodeService.NAME)
	PosCodeService codeService;
//	@Resource(name = PosBaseService.NAME)
//	PosBaseService posBaseServiceImp;
	@Autowired
	@Resource(name = OrderService.NAME)
	OrderService OrderServiceimp;
	
	@Override
	@Transactional(propagation=Propagation.REQUIRES_NEW)
	public OrderingPrintThread insertOrderRequiresNew(String tenancyId, Integer storeId, JSONObject jsonObject) {
		// 解析传入参数
		try {
			AcewillInsertOrder insertOrder = (AcewillInsertOrder) JSONObject.toBean(jsonObject,getJsonConfig());
			insertOrder.setStore_id(storeId);
			insertOrder.setTenent_id(tenancyId);
			// 获得订单信息
			AcewillOrderInfo orderInfo = insertOrder.getOrder_info();
			if(orderInfo==null) {
				JSONObject orderError = new JSONObject();
				orderError.accumulate("action", "order.error");
				orderError.put("msg", "落单失败：参数错误，订单信息不能为空");
				throw SystemException.getInstance(AcewillErrorCode.INSERT_ORDER_FAIL).formats(orderError.toString());
			}
			// 查询桌台状态
			JSONObject tableStatus = insertOrderDao.getTableStatus(tenancyId, storeId, orderInfo.getTableno());
			String errorMsg = null;
			if (tableStatus != null) {
				// 桌台存在
				String lockOptNum = tableStatus.getString("lock_opt_num");
				
				if (StringUtils.isEmpty(lockOptNum) || "null".equals(lockOptNum)) {
					// 未锁台
					AcewillOrderAssist orderAssist = null;
					String state = tableStatus.getString("state");
					if (SysDictionary.TABLE_STATE_FREE.equals(state)) {
						
						JSONObject posBill = insertOrderDao.getPosBillByOrderNum(tenancyId, insertOrder.getIdentify());
						if(posBill!=null) {
							JSONObject orderError = new JSONObject();
							orderError.accumulate("action", "order.error");
							orderError.put("msg", "落单失败：订单号重复");
							throw SystemException.getInstance(AcewillErrorCode.INSERT_ORDER_FAIL).formats(orderError.toString());
						}
						// 未占用，开台
						orderAssist = createOrderAssist(insertOrder);
						Data createOpenTableData = createOpenTableData(insertOrder, orderAssist);
						List<JSONObject> openTableList = posDishServlet.newestOpenTable(createOpenTableData,
								new JSONObject());
						if (openTableList.size() > 0) {
							JSONObject posBillJson = JSONObject.fromObject(openTableList.get(0));
							orderAssist.setBill_num(posBillJson.getString("bill_num"));
							insertOrderDao.insertOpenId(tenancyId, storeId, orderAssist.getBill_num(), insertOrder.getOpenid());
						}
					}
					if (SysDictionary.TABLE_STATE_BUSY.equals(state)) {
						// 已占用，订单号
						JSONObject posBill = insertOrderDao.getPosBill(insertOrder.getTenent_id(),
								insertOrder.getStore_id(), orderInfo.getTableno());
						
						orderAssist = createOrderAssist(posBill);
						insertOrderDao.updateOpenId(orderAssist.getBill_num(), insertOrder.getOpenid());
					}
					// 拼装下单所需参数
					Data param = createOrderDishData(insertOrder, orderAssist);
					JSONObject printParamJson = new JSONObject();
					// 下单
					Data newestOrderDish = posDishService.newestOrderDish(param, printParamJson);
					
					//更新OpenId
					if (newestOrderDish.getCode() == 0) {
						// 会员信息
						String sysParameter = CacheTableUtil.getSysParameter("CustomerType");
						if("acewill".equals(sysParameter)) {
							savePosBillMember(insertOrder, orderAssist);
						}
						// 调用上传订单接口
						String opType = SysDictionary.TABLE_STATE_FREE.equals(state)?WLifeConstant.OPT_TYPE_NEW:WLifeConstant.OPT_TYPE_ADD;
						OrderServiceimp.getOrderInfo(insertOrder.getTenent_id(),insertOrder.getStore_id(),orderInfo.getTableno(), opType);
						return new OrderingPrintThread(printParamJson, param);
					} else {
						errorMsg = "落单失败";
					}
				} else {
					errorMsg = "落单失败:已锁单";
				}
			} else {
				errorMsg = "落单失败:当前桌台不存在";
			}
			JSONObject orderError = new JSONObject();
			orderError.put("action", "order.error");
			orderError.put("msg", errorMsg);
			throw SystemException.getInstance(AcewillErrorCode.INSERT_ORDER_FAIL).formats(orderError.toString());
		}
		catch (SystemException e)
		{
//			throw e;
			
			ErrorCode error = e.getErrorCode();
			String msg = e.getErrorMsg();
//			String msg = PropertiesLoader.getProperty(String.valueOf(error.getNumber()));
//			Map<String, Object> map = e.getProperties();
//			for (String key : map.keySet())
//			{
//				msg = msg.replace(key, String.valueOf(map.get(key)));
//			}
			
			JSONObject orderError = new JSONObject();
			orderError.put("action", "order.error");
			orderError.put("msg", msg);
			throw SystemException.getInstance(AcewillErrorCode.INSERT_ORDER_FAIL).formats(orderError.toString());
		}
		catch (Exception e)
		{
			e.printStackTrace();
			JSONObject orderError = new JSONObject();
			orderError.put("action", "order.error");
			orderError.put("msg", "落单失败");
			throw SystemException.getInstance(AcewillErrorCode.INSERT_ORDER_FAIL).formats(orderError.toString());
		}
	}
	@Override
	public void orderingPrint(OrderingPrintThread orderingPrintThread) {
		ThreadPool.getPrintThreadPool().execute(orderingPrintThread);
	}

	private AcewillPosBillMember savePosBillMember(AcewillInsertOrder insertOrder, AcewillOrderAssist orderAssist)
			throws Exception {
		AcewillOrderInfo orderInfo = insertOrder.getOrder_info();
		AcewillOrderMember member = orderInfo.getMember();
		AcewillPosBillMember posBillMember = new AcewillPosBillMember();
		posBillMember.setTenancy_id(insertOrder.getTenent_id());
		posBillMember.setStore_id(insertOrder.getStore_id());
		posBillMember.setBill_num(orderAssist.getBill_num());
		posBillMember.setBill_code(insertOrder.getOut_order_id());
		posBillMember.setReport_date(DateUtil.parseDate(orderAssist.getReport_date()));
		posBillMember.setType(SysDictionary.BILL_MEMBERCARD_JFZS06);
		posBillMember.setAmount(null);
		
		posBillMember.setCredit(member.getCredit());
		posBillMember.setCard_code(member.getCno());
		posBillMember.setMobil(member.getMobile());
		posBillMember.setLast_updatetime(new Date());
		posBillMember.setUpload_tag(0);
		posBillMember.setRemark(null);
		posBillMember.setBill_code(null);
		posBillMember.setRequest_state(null);
		posBillMember.setCustomer_code(member.getCno());
		posBillMember.setCustomer_name(member.getName());
		posBillMember.setConsume_before_credit(member.getCredit());
		posBillMember.setConsume_after_credit(member.getCredit());
		posBillMember.setConsume_before_main_balance(member.getBalance());
		//交易前赠送账户余额
		posBillMember.setConsume_before_reward_balance(0d);
		//交易后主账户余额
		posBillMember.setConsume_after_main_balance(member.getBalance());
		//交易后赠送账户余额
		posBillMember.setConsume_after_reward_balance(0d);
		insertOrderDao.savePosBillMember(posBillMember);
		return posBillMember;
	}

	private JsonConfig getJsonConfig() {
		JsonConfig config = new JsonConfig();
		config.setClassMap(getInsertOrderClassMap());
		config.setRootClass(AcewillInsertOrder.class);
		config.setJavaPropertyFilter(new PropertyFilter() {
			@Override
			public boolean apply(Object source, String name, Object value) {
				PropertyDescriptor propertyDescriptor = BeanUtils.getPropertyDescriptor(source.getClass(), name);
				return propertyDescriptor==null;
			}
		});
		return config;
	}
	
	private Map<String,Class<?>> getInsertOrderClassMap() {
		Map<String, Class<?>> classMap = new HashMap<>();
		classMap.put("order_info", AcewillOrderInfo.class);
		classMap.put("ordermemo", AcewillOrderMemo.class);
		classMap.put("member", AcewillOrderMember.class);
		classMap.put("upgrade", AcewillOrderUpGrade.class);
		classMap.put("setmeal", AcewillOrderSetmeal.class);
		classMap.put("normalitems", AcewillOrderNormalitem.class);
		classMap.put("maindish", AcewillOrderSetmealItem.class);
		classMap.put("mandatory", AcewillOrderSetmealItem.class);
		classMap.put("optional", AcewillOrderSetmealItem.class);
		return classMap;
	}

	private AcewillOrderAssist createOrderAssist(JSONObject posBill) {
		AcewillOrderAssist orderAssist = new AcewillOrderAssist();
		orderAssist.setBill_num(posBill.getString("bill_num"));
		orderAssist.setOpt_num(posBill.getString("open_opt"));
		orderAssist.setPos_num(posBill.getString("pos_num"));
		orderAssist.setReport_date(posBill.getString("report_date"));
		orderAssist.setShift_id(posBill.getInt("shift_id"));
		return orderAssist;
	}

	private AcewillOrderAssist createOrderAssist(AcewillInsertOrder insertOrder) throws Exception {
		AcewillOrderAssist orderAssist = new AcewillOrderAssist();
		String tenantId = insertOrder.getTenent_id();
		Integer storeId = insertOrder.getStore_id();
		Date reportDate = insertOrderDao.getReportDate(tenantId, storeId);
		orderAssist.setReport_date(DateUtil.formatDate(reportDate));
		List<JSONObject> optStateInfoList = posDishService.getOptStateInfo(tenantId, storeId,
				orderAssist.getReport_date());
		if (optStateInfoList.size() > 0) {
			JSONObject optStateInfo = optStateInfoList.get(0);
			orderAssist.setPos_num(optStateInfo.getString("pos_num"));
			orderAssist.setOpt_num(optStateInfo.getString("opt_num"));
			orderAssist.setShift_id(optStateInfo.optInt("shift_id"));
		}
//		int shiftId = insertOrderDao.getShiftId(tenantId, storeId, reportDate, orderAssist.getOpt_num(), orderAssist.getPos_num());
//		orderAssist.setShift_id(shiftId);
		return orderAssist;
	}

	private Data createOpenTableData(AcewillInsertOrder insertOrder, AcewillOrderAssist orderAssist) throws Exception {
		AcewillOrderInfo orderInfo = insertOrder.getOrder_info();
		Data data = new Data();
		data.setTenancy_id(insertOrder.getTenent_id());
		data.setStore_id(insertOrder.getStore_id());
		data.setType(Type.ORDERING);
		data.setOper(Oper.add);
		List<JSONObject> list = new ArrayList<>();
		JSONObject object = new JSONObject();
		object.put("mode", 0);
		object.put("shift_id", orderAssist.getShift_id());
		object.put("report_date", orderAssist.getReport_date());
		object.put("table_code", orderInfo.getTableno());
		object.put("pos_num", orderAssist.getPos_num());
		object.put("opt_num", orderAssist.getOpt_num());
		object.put("waiter_num", orderAssist.getOpt_num());
		object.put("item_menu_id", 0);
		object.put("sale_mode", "TS01");
		object.put("chanel", "WX02");
		object.put("guest", orderInfo.getPeople());
		object.put("preorderno", insertOrder.getIdentify());
		object.put("copy_bill_num", "");
		object.put("remark", "");
		object.put("shop_real_amount", 0);
		object.put("platform_charge_amount", 0);
		object.put("settlement_type", "");
		object.put("discount_mode_id", 0);
		object.put("discountk_amount", 0);
		object.put("discount_rate", 0);
		list.add(object);
		data.setData(list);
		return data;
	}

	private Data createOrderDishData(AcewillInsertOrder insertOrder, AcewillOrderAssist orderAssist) throws Exception {
		Data data = new Data();
		data.setTenancy_id(insertOrder.getTenent_id());
		data.setStore_id(insertOrder.getStore_id());
		data.setType(Type.ORDERING);
		data.setOper(Oper.add);
		data.setSource(SysDictionary.SOURCE_CC_ORDER);
		List<JSONObject> orderList = new ArrayList<>();
		AcewillOrder order = createOrder(insertOrder, orderAssist);
		JSONObject orderJsonObject = JSONObject.fromObject(order, getAcewillOrderJsonConfig());
		orderList.add(orderJsonObject);
		data.setData(orderList);
		return data;
	}

	private JsonConfig getAcewillOrderJsonConfig() {
		Map<String, Class<?>> classMap = new HashMap<>();
		classMap.put("item", AcewillOrderItem.class);
		classMap.put("method", AcewillOrderItemMethod.class);
		JsonConfig jsonConfig = new JsonConfig();
		jsonConfig.setClassMap(classMap);
		jsonConfig.setRootClass(AcewillOrder.class);
		return jsonConfig;
	}

	private AcewillOrder createOrder(AcewillInsertOrder insertOrder, AcewillOrderAssist orderAssist) throws Exception {

		AcewillOrderInfo orderInfo = insertOrder.getOrder_info();

		AcewillOrder acewillOrder = new AcewillOrder();
		// 订单号
		acewillOrder.setBill_num(orderAssist.getBill_num());
		// 整单备注
		//String billTaste = insertOrderDao.getBillTaste(insertOrder);
		AcewillOrderMemo ordermemo = orderInfo.getOrdermemo();
		if(ordermemo!=null) {
			acewillOrder.setBill_taste(ordermemo.getText());
		}
		// 是否厨打
		acewillOrder.setIsprint("Y");
		// 0:下单 1:
		acewillOrder.setMode(0);
		// 操作员编号
		acewillOrder.setOpt_num(orderAssist.getOpt_num());
		// 收款机编号
		acewillOrder.setPos_num(orderAssist.getPos_num());
		// 备注
		acewillOrder.setRemark(null);
		// 报表日期
		acewillOrder.setReport_date(orderAssist.getReport_date());
		// 销售模式
		// acewillOrder.setSale_mode(sale_mode);
		// 班次id
		acewillOrder.setShift_id(orderAssist.getShift_id());

		acewillOrder.setTable_code(orderInfo.getTableno());
		// 服务员号
		acewillOrder.setWaiter_num(null);
		List<AcewillOrderItem> orderItems = new ArrayList<>();
		acewillOrder.setItem(orderItems);
		// 点餐序号
		List<AcewillOrderSetmeal> setmealList = orderInfo.getSetmeal();
		Integer item_serial = insertOrderDao.getLastItemSerial(insertOrder.getTenent_id(), orderAssist.getBill_num());
		Map<String, JSONObject> itemComboDetailMap = itemComboDetailMap(insertOrder);
		Map<String, String> setmealUnitMap = getSetmealUnitMap(insertOrder);
		if(setmealList!=null) {
			for (AcewillOrderSetmeal setmeal : setmealList) {
				String setmealUnitId = setmealUnitMap.get(setmeal.getDid());
				if(!StringUtils.isEmpty(setmealUnitId)) {
					setmeal.setDuid(setmealUnitId);
				}
			}
		}
		Map<String, String> unitNameMap = insertOrderDao.getUnitNameMap(insertOrder);
		if(setmealList!=null) {
			for (AcewillOrderSetmeal setmeal : setmealList) {
				item_serial++;
				List<AcewillOrderSetmealItem> setmealItemList = new ArrayList<>();
				setmealItemList.addAll(setmeal.getMaindish());
				setmealItemList.addAll(setmeal.getMandatory());
				setmealItemList.addAll(setmeal.getOptional());
				for (AcewillOrderSetmealItem item : setmealItemList) {
					item.setNumber(setmeal.getNumber()*item.getNumber());
					String duName = unitNameMap.get(item.getDuid());
					JSONObject itemComboDetail = itemComboDetailMap.get(item.getId());
					item.setDuName(duName);
					AcewillOrderItem setmealOrderItem = createOrderItem(orderInfo, setmeal, item, item_serial,itemComboDetail);
					orderItems.add(setmealOrderItem);
				}
				JSONObject itemComboDetail = itemComboDetailMap.get(setmeal.getDid());
				String duName = unitNameMap.get(setmeal.getDuid());
				setmeal.setDuName(duName);
				orderItems.add(createOrderItem(orderInfo, setmeal, item_serial, itemComboDetail));
			}
		}
		List<AcewillOrderNormalitem> normalitemList = orderInfo.getNormalitems();
		if(normalitemList!=null) {
			for (AcewillOrderNormalitem item : normalitemList) {
				item_serial++;
				String duName = unitNameMap.get(item.getDuid());
				item.setDuName(duName);
				JSONObject itemComboDetail = itemComboDetailMap.get(item.getDid());
				AcewillOrderItem setmealOrderItem = createOrderItem(orderInfo, item, item_serial, itemComboDetail);
				orderItems.add(setmealOrderItem);
			}
		}

		return acewillOrder;
	}

	private Map<String, String> getSetmealUnitMap(AcewillInsertOrder insertOrder) throws Exception {
		String TenentId = insertOrder.getTenent_id();
		AcewillOrderInfo orderInfo = insertOrder.getOrder_info();
		List<String> itemIdList = new ArrayList<>();
		List<AcewillOrderSetmeal> setmealList = orderInfo.getSetmeal();
		if(setmealList!=null) {
			for (AcewillOrderSetmeal setmeal : setmealList) {
				itemIdList.add(setmeal.getDid());
			}
		}
		Map<String,String> setmealUnitMap = new HashMap<>();
		List<JSONObject> findItemUnit = insertOrderDao.findItemUnit(TenentId, itemIdList);
		if(findItemUnit!=null) {
			for (JSONObject jsonObject : findItemUnit) {
				setmealUnitMap.put(jsonObject.getString("item_id"), jsonObject.getString("id"));
			}
		}
		return setmealUnitMap;
	}

	private Map<String, JSONObject> itemComboDetailMap(AcewillInsertOrder insertOrder)
			throws Exception {
		String TenentId = insertOrder.getTenent_id();
		AcewillOrderInfo orderInfo = insertOrder.getOrder_info();
		List<Integer> itemIdList = new ArrayList<>();
		List<AcewillOrderSetmeal> setmealList = orderInfo.getSetmeal();
		if (setmealList!=null) {
			for (AcewillOrderSetmeal setmeal : setmealList) {
				List<AcewillOrderSetmealItem> setmealItemList = new ArrayList<>();
				setmealItemList.addAll(setmeal.getMaindish());
				setmealItemList.addAll(setmeal.getMandatory());
				setmealItemList.addAll(setmeal.getOptional());
				for (AcewillOrderSetmealItem item : setmealItemList) {
					itemIdList.add(Integer.parseInt(item.getId()));
				}
				itemIdList.add(Integer.parseInt(setmeal.getDid()));
			}
		}
		List<AcewillOrderNormalitem> normalitemList = orderInfo.getNormalitems();
		if(normalitemList!=null) {
			for (AcewillOrderNormalitem item : normalitemList) {
				itemIdList.add(Integer.parseInt(item.getDid()));
			}
		}
		List<JSONObject> itemComboDetails = insertOrderDao.getItemComboDetails(TenentId, itemIdList);
		Map<String,JSONObject> itemComboDetailMap = new HashMap<>();
		if(itemComboDetails!=null) {
			for (JSONObject jsonObject : itemComboDetails) {
				//itemComboDetailMap.put(jsonObject.getString("details_id"), jsonObject);
				itemComboDetailMap.put(jsonObject.getString("iitem_id"), jsonObject);
			}
		}
		return itemComboDetailMap;
	}

	private AcewillOrderItem createOrderItem(AcewillOrderInfo orderInfo, AcewillOrderNormalitem item,
			Integer item_serial,JSONObject itemComboDetail) {
		AcewillOrderItem orderItem = new AcewillOrderItem();
		// 辅助Assist
		if(itemComboDetail!=null) {
			orderItem.setAssist_item_id(itemComboDetail.getString("id"));
			orderItem.setAssist_num(itemComboDetail.getString("combo_num"));
		}
		// orderItem.setAssist_money(assist_money); 
		// 餐谱明细id orderItem.setDetails_id(details_id);

		orderItem.setItem_count(item.getNumber());

		orderItem.setItem_id(item.getDid());
		orderItem.setItem_name(item.getName());
		orderItem.setItem_num(item.getDishsno());
		orderItem.setItem_price(item.getPrice());
		orderItem.setItem_property(SysDictionary.ITEM_PROPERTY_SINGLE);

		//orderItem.setItem_remark(item.getRemark());
		// 点菜序号
		orderItem.setItem_serial(item_serial);
		orderItem.setItem_taste(item.getRemark());
		// 规格名称
		orderItem.setItem_unit_name(item.getDuName());

		// orderItem.setSale_mode(sale_mode);

		orderItem.setMethod(createItemMethod(item.getCooks()));
		// 座位号
		orderItem.setSeat_num(null);
		orderItem.setSetmeal_id(null);
		// 套菜点菜号
		orderItem.setSetmeal_rwid(null);
		// 规格id
		orderItem.setUnit_id(item.getDuid());
		// 等叫标记
		// orderItem.setWaitcall_tag(waitcall_tag);
		return orderItem;
	}

	private AcewillOrderItem createOrderItem(AcewillOrderInfo orderInfo, AcewillOrderSetmeal setmeal,
			Integer item_serial,JSONObject itemComboDetail) {
		AcewillOrderItem orderItem = new AcewillOrderItem();
		// 辅助Assist
		if(itemComboDetail!=null) {
			orderItem.setAssist_item_id(itemComboDetail.getString("id"));
			orderItem.setAssist_num(itemComboDetail.getString("combo_num"));
		}
		// orderItem.setAssist_money(assist_money); 
		// 餐谱明细id orderItem.setDetails_id(details_id);
		orderItem.setItem_count(setmeal.getNumber());

		orderItem.setItem_id(setmeal.getDid());
		orderItem.setItem_name(setmeal.getName());
		orderItem.setItem_num(setmeal.getDishsno());
		orderItem.setItem_price(setmeal.getPrice());
		orderItem.setItem_property(SysDictionary.ITEM_PROPERTY_SETMEAL);

		//orderItem.setItem_remark(setmeal.getRemark());
		// 点菜序号
		orderItem.setItem_serial(item_serial);
		orderItem.setItem_taste(setmeal.getRemark());

		// 规格名称
		orderItem.setItem_unit_name(setmeal.getDuName());
		// orderItem.setSale_mode(sale_mode);
		orderItem.setMethod(createItemMethod(setmeal.getCooks()));
		// 座位号
		orderItem.setSeat_num(orderInfo.getTableno());
		orderItem.setSetmeal_id(setmeal.getDid());
		// 套菜点菜号
		orderItem.setSetmeal_rwid(item_serial);
		// 规格id
		orderItem.setUnit_id(setmeal.getDuid());
		// 等叫标记
		// orderItem.setWaitcall_tag(waitcall_tag);
		return orderItem;
	}

	private AcewillOrderItem createOrderItem(AcewillOrderInfo orderInfo, AcewillOrderSetmeal setmeal,
			AcewillOrderSetmealItem item, Integer item_serial,JSONObject itemComboDetail) {
		AcewillOrderItem orderItem = new AcewillOrderItem();
		// 辅助Assist
		if(itemComboDetail!=null) {
			orderItem.setAssist_item_id(itemComboDetail.getString("id"));
			orderItem.setAssist_num(itemComboDetail.getString("combo_num"));
		}
		// orderItem.setAssist_money(assist_money); 
		// 餐谱明细id orderItem.setDetails_id(details_id);

		orderItem.setItem_count(item.getNumber());

		orderItem.setItem_id(item.getId());
		orderItem.setItem_name(item.getName());
		orderItem.setItem_num(item.getDishsno());
		//orderItem.setItem_price(item.getAprice());
		orderItem.setItem_property(SysDictionary.ITEM_PROPERTY_MEALLIST);
		//口味备注
		orderItem.setItem_taste(setmeal.getRemark());
		// 点菜序号
		orderItem.setItem_serial(item_serial);

		// orderItem.setItem_taste(null);
		// 规格名称 
		orderItem.setItem_unit_name(item.getDuName());

		// 做法 orderItem.setMethod(method);

		// orderItem.setSale_mode(sale_mode);
		orderItem.setMethod(createItemMethod(setmeal.getCooks()));
		// 座位号
		orderItem.setSeat_num(orderInfo.getTableno());
		orderItem.setSetmeal_id(setmeal.getDid());
		// 套菜点菜号
		orderItem.setSetmeal_rwid(item_serial);
		// 规格id
		orderItem.setUnit_id(item.getDuid());
		// 等叫标记
		// orderItem.setWaitcall_tag(waitcall_tag);
		return orderItem;
	}

	private List<AcewillOrderItemMethod> createItemMethod(Object cooks) {
		List<AcewillOrderItemMethod> methods = new ArrayList<>();
		JSONArray cookArray = JSONArray.fromObject(cooks);
		if(cooks!=null) {
			for (int i = 0; i < cookArray.size(); i++) {
				JSONObject jsonObject = cookArray.getJSONObject(i);
				AcewillOrderItemMethod method = new AcewillOrderItemMethod();
				method.setMethod_id(jsonObject.getString("id"));
				method.setMethod_name(jsonObject.getString("name"));
				methods.add(method);
			}
		}
		return methods;
	}
	
}
