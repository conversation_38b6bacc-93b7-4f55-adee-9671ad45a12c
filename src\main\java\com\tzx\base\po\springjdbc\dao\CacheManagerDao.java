package com.tzx.base.po.springjdbc.dao;

import com.tzx.base.constant.CacheTableConstant;
import com.tzx.framework.common.util.dao.GenericDao;

import java.util.List;
import java.util.Map;

/**
 * Created by kevin on 2017-06-24.
 */
public interface CacheManagerDao extends GenericDao {

    String	NAME	= "com.tzx.base.po.springjdbc.dao.imp.CacheManagerDaoImp";

    /**
     * 获取业务数据
     * @param cacheTableConstant
     * @param tenancyId
     * @param storeId
     * @return
     * @throws Exception
     */
    public List<Map<String, Object>> getBusinessDataByStoreId(CacheTableConstant cacheTableConstant, String tenancyId, int storeId) throws Exception;
}
