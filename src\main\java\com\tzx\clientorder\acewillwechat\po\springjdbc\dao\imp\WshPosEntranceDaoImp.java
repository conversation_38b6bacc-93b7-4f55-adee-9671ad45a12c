package com.tzx.clientorder.acewillwechat.po.springjdbc.dao.imp;

import java.util.ArrayList;
import java.util.List;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Repository;

import com.tzx.framework.common.util.dao.impl.GenericDaoImpl;
import com.tzx.pos.base.util.SqlUtil;
import com.tzx.clientorder.acewillwechat.po.springjdbc.dao.WshPosEntranceDao;

@Repository(WshPosEntranceDao.NAME)
public class WshPosEntranceDaoImp extends GenericDaoImpl implements WshPosEntranceDao
{

	@Override
	public List<JSONObject> selectDishKinds(String tenancy_id, String store_id)
			throws Exception {
		StringBuilder sql = new StringBuilder();
		sql.append(" select hic.id, himc.details_id as detail_id, himds.item_id, hic.itemclass_name as name ");
		sql.append(" , coalesce(himc2.classorder,0) as seq, hic.itemclass_code as dishkindsno, hic.father_id as pdkid, 0 as pkid ");
		sql.append(" , 0 as must, 0 as must_seq, 0 as suggest, 0 as border, '' as icon ");
		sql.append(" from hq_item_menu_organ himo ");
		sql.append("  join hq_item_menu him on (him.id=himo.item_menu_id and him.valid_state='1') ");
		sql.append("  join hq_item_menu_details himds on (himds.item_menu_id=him.id and himds.valid_state='1') ");
		sql.append("  join hq_item_menu_class himc on (himc.details_id=himds.id and himc.chanel='WX02') ");
		sql.append("  join hq_item_class hic on (hic.id=himc.class and hic.valid_state='1') ");
		sql.append(" left join hq_item_menu_classorder himc2 on (himc2.menu_id=him.id and himc2.class_id=himc.class) ");
		sql.append(" where him.tenancy_id='" + tenancy_id + "' ");
		sql.append(" and himo.store_id=" + store_id + " ");
		sql.append(" order by id ");
		List<JSONObject> query4Json = this.query4Json(tenancy_id, sql.toString());
		return query4Json;
	}

	@Override
	public List<JSONObject> selectDishKinds(String tenancyId, Integer storeId, String chanel) throws Exception
	{
		StringBuilder sql = new StringBuilder();
		sql.append("with recursive item_class as (");
		sql.append(" select hic.id,hic.itemclass_name,himco.classorder,hic.itemclass_code,hic.father_id,him.item_menu_id,count(*) item_count from (");
		sql.append(" select distinct himo.tenancy_id,himo.store_id,him.id as item_menu_id,himd.item_id,himc.class as item_class_id,himc.chanel");
		sql.append(" from hq_item_menu_class himc");
		sql.append(" left join hq_item_menu_details himd on himc.tenancy_id=himd.tenancy_id and himc.details_id=himd.id");
		sql.append(" left join hq_item_menu him on himd.tenancy_id=him.tenancy_id  and himd.item_menu_id=him.id");
		sql.append(" left join hq_item_menu_organ himo on him.tenancy_id=himo.tenancy_id and him.id=himo.item_menu_id");
		sql.append(" where himo.tenancy_id=? and himo.store_id=? and him.valid_state='1' and himd.valid_state='1' and himc.chanel=?) him");
		sql.append(" left join hq_item_class hic on him.tenancy_id=hic.tenancy_id and hic.id=him.item_class_id  ");
		sql.append(" left join hq_item_menu_classorder himco on hic.id=himco.class_id and him.item_menu_id=himco.menu_id");
		sql.append(" group by hic.id,hic.itemclass_name,himco.classorder,hic.itemclass_code,hic.father_id,him.item_menu_id");
		sql.append(" union select hic.id,hic.itemclass_name,himo.classorder,hic.itemclass_code,hic.father_id,item_class.item_menu_id,'0' item_count from hq_item_class hic");
		sql.append(" inner join item_class on hic.id=item_class.father_id ");
		sql.append(" left join hq_item_menu_classorder himo on hic.id=himo.class_id and item_class.item_menu_id=himo.menu_id");
		sql.append(") select item_class.id,item_class.itemclass_name as name,coalesce(item_class.classorder,'0') seq,item_class.itemclass_code dishkindsno,item_class.father_id pdkid,item_class.item_count dish_count from item_class order by item_class.classorder");
		List<JSONObject> query4Json = this.query4Json(tenancyId, sql.toString(), new Object[]
				{ tenancyId, storeId, chanel });
		return query4Json;
	}


	@Override
	public List<JSONObject> selectDishKindParents(String tenancy_id, String store_id, int[] class_ids)
			throws Exception {
		if(class_ids == null || class_ids.length == 0){
			return new ArrayList<JSONObject>();
		}
		StringBuilder sql = new StringBuilder();
		sql.append("  select hic.id, hic.itemclass_name as name ");
		sql.append("  , coalesce(himc2.classorder,0) as seq, hic.itemclass_code as dishkindsno, hic.father_id as pdkid, 0 as pkid   ");
		sql.append("  , 0 as must, 0 as must_seq, 0 as suggest, 0 as border, '' as icon   ");
		sql.append("  from hq_item_menu_organ himo ");
		sql.append("  join hq_item_menu him on (him.id=himo.item_menu_id and him.valid_state='1') ");
		sql.append("  join hq_item_class hic on hic.id in (" + SqlUtil.getInnerStr(class_ids) + ") ");
		sql.append("  left join hq_item_menu_classorder himc2 on (himc2.menu_id=him.id and himc2.class_id=hic.id)   ");
		sql.append("  WHERE himo.store_id=" + store_id + "  ");
		sql.append("  and him.tenancy_id='" + tenancy_id + "' ");
		sql.append("  order by id ");
		List<JSONObject> query4Json = this.query4Json(tenancy_id, sql.toString());
		return query4Json;
	}

	@Override
	public List<JSONObject> selectTables(String tenancy_id, String store_id)
			throws Exception {
		StringBuilder sql = new StringBuilder();
		sql.append(" select ti.id as tid, ti.table_code as sno, ti.table_name as tablename, 0 as mealfee   ");
		sql.append(" from tables_info ti ");
		sql.append(" where ti.tenancy_id='" + tenancy_id + "' ");
		sql.append(" and ti.valid_state='1' ");
		sql.append(" and ti.organ_id='" + store_id + "' ");
		return this.query4Json(tenancy_id, sql.toString());
	}

	@Override
	public List<JSONObject> selectShops(String tenancy_id, String store_id)
			throws Exception {
		StringBuilder sql = new StringBuilder();
		sql.append(" select o.id as sid, '' as bid, o.org_full_name as shopname, COALESCE(o.address, '') as shopadd ");
		sql.append(" , '' as shoplocation, COALESCE(o.longitude, '') as lng, COALESCE(o.latitude, '') as lat ");
		sql.append(" from organ o ");
		sql.append(" where o.tenancy_id='" + tenancy_id + "' ");
		sql.append(" and o.id='" + store_id + "' ");
		sql.append(" and o.org_type='3' ");
		sql.append(" and o.valid_state='1'  ");
		return this.query4Json(tenancy_id, sql.toString());
	}

	@Override
	public List<JSONObject> selectMemo(String tenancy_id, String store_id)
			throws Exception {
		StringBuilder sql = new StringBuilder();
		sql.append(" select it.id as omid, COALESCE(it.father_id, 0) as omkid, it.name as ordermemo  ");
		sql.append(" from item_taste it ");
		sql.append(" left join item_taste_org ito on it.tenancy_id=ito.tenancy_id and it.id=ito.teste_id");
		sql.append(" where ito.tenancy_id='" + tenancy_id + "' and ito.store_id='"+store_id+"'");
		sql.append(" and it.valid_state='1' and (it.father_id is not null and it.father_id<>'0') ");
		sql.append(" order by it.father_id ");
		return this.query4Json(tenancy_id, sql.toString());
	}

	@Override
	public JSONObject selectOrganInfo(String tenancy_id, String store_id)
			throws Exception {
		StringBuilder sql = new StringBuilder();
		sql.append(" select * from organ  ");
		sql.append(" where organ.id='" + store_id + "'  ");
		sql.append(" and organ.valid_state='1'  ");
		sql.append(" and organ.tenancy_id='" + tenancy_id + "' ");
		List<JSONObject> query4Json = this.query4Json(tenancy_id, sql.toString());
		return query4Json.size() > 0 ? query4Json.get(0) : null;
	}

	@Override
	public List<JSONObject> selectItemInfos(String tenancy_id, String store_id)
			throws Exception {
		StringBuilder sql = new StringBuilder();
		sql.append("  select hii.item_code as dishsno, hii.id as id, hii.item_name as name, '' as describ  ");
		sql.append("  , COALESCE(hii.item_description, '') as info, (case when hii.is_combo='Y' then 2 else 1 end) as type  ");
		sql.append("  , (case when hii.is_assemble_combo='1' then false else true end) as wxDishs, himc.class as pkid ");
		sql.append("  , hic.father_id as dishkind, COALESCE(hii.photo1, '') as icon, COALESCE(hii.photo1, '') as image  ");
		sql.append("  , COALESCE(hii.photo1, '') as dishimg, 1 as min_unit, 1 as min_count, 1 as min_reduce ");//1 as soldouttype
		sql.append("  ,(CASE WHEN (EXISTS (select id from pos_soldout WHERE item_id = hii.ID and tenancy_id = hii.tenancy_id)) THEN 1 ELSE 0 END) AS soldouttype ");
		sql.append("  , (case when hii.is_runningprice='Y' then hiu.unit_name else '' end) as priceName ");
		sql.append("  , (case when hii.is_runningprice='Y' then 1 else 0 end) as isWeigh,hiu.id as duid   ");
		sql.append("  from hq_item_menu_organ himo  ");
		sql.append("  join hq_item_menu him on (him.id=himo.item_menu_id and him.valid_state='1')   ");
		sql.append("  join hq_item_menu_details himd on (himd.item_menu_id=him.id and himd.valid_state='1')   ");
		sql.append("  join hq_item_menu_class himc on himc.details_id=himd.id and himc.chanel='WX02'  ");
		sql.append("  join hq_item_menu_classorder himcc on him.id=himcc.menu_id and himc.class=himcc.class_id  ");
		sql.append("  join hq_item_class hic on hic.id=himc.class and hic.chanel=himc.chanel and hic.valid_state='1'   ");
		sql.append("  join hq_item_info hii on (hii.id=himd.item_id and hii.valid_state='1')  ");
		sql.append("  join hq_item_unit hiu on (hiu.item_id=hii.id and hiu.valid_state='1') ");
		sql.append("  where himo.store_id=" + store_id + " ");
		sql.append("  and himo.tenancy_id='" + tenancy_id + "' order by himcc.classorder,cast(himd.menu_item_rank as int4) ");
		return this.query4Json(tenancy_id, sql.toString());
	}

	@Override
	public List<JSONObject> selectUnitInfos(String tenancy_id, int[] item_ids, String price_system)
			throws Exception {
		if(item_ids == null || item_ids.length == 0){
			return new ArrayList<JSONObject>();
		}
		StringBuilder sql = new StringBuilder();
		sql.append(" select hiu.unit_name as name, hiu.id as duid, COALESCE(hip.price, hiu.standard_price) as price, COALESCE(hip.price, hiu.standard_price) as orgprice, 0 as bargainprice ");
		sql.append(" , COALESCE(COALESCE(civs.vip_price, hip.price), hiu.standard_price) as memberprice, 1 as min_unit, 0 as limitCount ");
		sql.append(" , hiu.item_id as item_id ");
		sql.append(" from hq_item_unit hiu  ");
		sql.append(" left join hq_item_pricesystem hip on (hip.item_unit_id=hiu.id and hip.chanel='WX02' and hip.price_system='" + price_system + "') ");
		sql.append(" left join crm_item_vip_sysprice civs on (civs.unit_id=hiu.id and civs.chanel='WX02' and civs.price_system='" + price_system + "') ");
		sql.append(" where hiu.item_id in (" + SqlUtil.getInnerStr(item_ids) + ") ");
		sql.append(" and hiu.valid_state='1' ");
		sql.append(" and hiu.tenancy_id='" + tenancy_id + "' ");
		return this.query4Json(tenancy_id, sql.toString());
	}

	@Override
	public List<JSONObject> selectMethodInfos(String tenancy_id, int[] item_ids)
			throws Exception {
		if(item_ids == null || item_ids.length == 0){
			return new ArrayList<JSONObject>();
		}
		StringBuilder sql = new StringBuilder();
		sql.append(" select DISTINCT him.id as id, sd.class_item as name, case when him.makeup_way = 'ADD' then " +
				" him.proportion_money " +
				" when him.makeup_way = 'MULTI' then him.proportion_money*itp.price" +
				" ELSE 0 end as aprice, ");// 1 as soldouttype
		sql.append(" (CASE WHEN (select coalesce(num,0) from pos_soldout WHERE item_id = hii.ID and tenancy_id = hii.tenancy_id) <= 0 THEN 0 ELSE 1 END) AS soldouttype ");
		sql.append(" ,him.item_id as item_id ");
		sql.append(" from hq_item_method him ");
		sql.append(" left join sys_dictionary sd on (sd.id=him.method_name_id and sd.class_identifier_code='method') ");
		sql.append(" left join hq_item_info hii on him.item_id = hii.id");
		sql.append(" left join hq_item_unit hiu on hiu.item_id = hii.id");
		sql.append(" left join hq_item_pricesystem itp on itp.item_unit_id = hiu.id");
		sql.append(" where him.item_id in (" + SqlUtil.getInnerStr(item_ids) + ") ");
		sql.append(" and him.valid_state='1' ");
		sql.append(" and him.tenancy_id='" + tenancy_id + "' ");
		return this.query4Json(tenancy_id, sql.toString());
	}

	@Override
	public List<JSONObject> selectComboBaseInfo(String tenancy_id,
												int[] combo_item_ids, String price_system) throws Exception {
		if(combo_item_ids == null || combo_item_ids.length == 0){
			return new ArrayList<JSONObject>();
		}
		StringBuilder sql = new StringBuilder();
		sql.append(" select hiu.item_id as id,hiu.item_id as item_id,hiu.id as duid,i.item_code as dishsno,i.item_name as name, COALESCE(hip.price, hiu.standard_price) as price ");
		sql.append(" , COALESCE(hip.price, hiu.standard_price) as orgprice, 0 as bargainprice");
		sql.append(" , COALESCE(COALESCE(civs.vip_price, hip.price), hiu.standard_price) as memberprice, 0 as limitCount ");
		sql.append(" from hq_item_unit hiu ");
//		sql.append(" join hq_item_combo_details hicd on hicd.iitem_id=hiu.item_id and hicd.valid_state='1' ");
		sql.append(" left join hq_item_pricesystem hip on (hip.item_unit_id=hiu.id and hip.chanel='WX02') ");
		sql.append(" left join crm_item_vip_sysprice civs on (civs.unit_id=hiu.id and civs.price_system='" + price_system + "' and civs.chanel='WX02')  ");
		sql.append(" left join hq_item_info i on hiu.item_id = i.id ");
		sql.append(" where hiu.item_id in (" + SqlUtil.getInnerStr(combo_item_ids) + ") ");
		sql.append(" and hiu.is_default='Y' ");
		sql.append(" and hiu.valid_state='1' ");
		sql.append(" and hiu.tenancy_id='" + tenancy_id + "' ");
		return this.query4Json(tenancy_id, sql.toString());
	}

	@Override
	public List<JSONObject> selectMaindishDetails(String tenancy_id, int[] maindishCombIds)
			throws Exception {
		if(maindishCombIds == null || maindishCombIds.length == 0){
			return new ArrayList<JSONObject>();
		}
		StringBuilder sql = new StringBuilder();
		sql.append(" select hicd.id as hicd_id, hii.item_name as name, hicd.details_id as id, hicd.item_unit_id, hii.item_code as dishsno ");
		sql.append(" , hicd.combo_num as number ");
		sql.append(" from hq_item_combo_details hicd ");
		sql.append(" join hq_item_info hii on hii.id=hicd.details_id and hii.valid_state='1' ");
		sql.append(" where hicd.id in (" + SqlUtil.getInnerStr(maindishCombIds) + ") ");
		sql.append(" and hicd.valid_state='1' ");
		sql.append(" and hicd.tenancy_id='" + tenancy_id + "' ");
		return this.query4Json(tenancy_id, sql.toString());
	}

	@Override
	public List<JSONObject> selectMandatoryBaseInfo(String tenancy_id,
													int[] mandatoryCombIds) throws Exception {
		if(mandatoryCombIds == null || mandatoryCombIds.length == 0){
			return new ArrayList<JSONObject>();
		}
		StringBuilder sql = new StringBuilder();
		sql.append(" select hicd.id as hicd_id, hicd.iitem_id as iitem_id, hig.item_group_name as title ");
		sql.append(" , hicd.combo_num as selnum, hig.id as hig_id ");
		sql.append(" from hq_item_combo_details hicd ");
		sql.append(" join hq_item_group hig on hig.id=hicd.details_id and hig.valid_state='1' ");
		sql.append(" where hicd.id in (" + SqlUtil.getInnerStr(mandatoryCombIds) + ") ");
		sql.append(" and hicd.valid_state='1' ");
		sql.append(" and hicd.tenancy_id='" + tenancy_id + "' ");
		return this.query4Json(tenancy_id, sql.toString());
	}

	@Override
	public List<JSONObject> selectGroupDetails(String tenancy_id, int[] groupIds)
			throws Exception {
		if(groupIds == null || groupIds.length == 0){
			return new ArrayList<JSONObject>();
		}
		StringBuilder sql = new StringBuilder();
		sql.append(" select distinct hii.item_name as name, higd.item_id as id, higd.item_unit_id as duid ");
		sql.append(" , hii.item_code as dishsno, higd.quantity_limit as maxnum, higd.makeup_money as aprice ");
		sql.append(" , hig.id as group_id, higd.item_group_id as rpdid,1 as selnum ");
		sql.append(" from hq_item_group_details higd ");
		sql.append(" join hq_item_group hig on hig.id=higd.item_group_id and hig.valid_state='1' ");
		sql.append(" join hq_item_combo_details hicd on hicd.details_id=hig.id and hicd.is_itemgroup='Y' ");
		sql.append(" join hq_item_info hii on hii.id=higd.item_id and hii.valid_state='1'  ");
		sql.append(" where higd.item_group_id in (" + SqlUtil.getInnerStr(groupIds) + ") ");
		sql.append(" and higd.tenancy_id='" + tenancy_id + "' ");
		return this.query4Json(tenancy_id, sql.toString());
	}

	@Override
	public List<JSONObject> selectStoreParams(String tenancy_id, int store_id, String[] paraCodes) throws Exception {
		if(paraCodes == null || paraCodes.length == 0){
			return new ArrayList<JSONObject>();
		}
		StringBuilder sql = new StringBuilder();
		sql.append(" select * from sys_parameter sp  ");
		sql.append(" where sp.para_code in (" + SqlUtil.getInnerStr(paraCodes) + ") ");
		sql.append(" and (sp.store_id='" + store_id + "' or sp.store_id='0')");
		sql.append(" and sp.valid_state='1' ");
		sql.append(" and sp.tenancy_id='"+tenancy_id+"' ");
		return this.query4Json(tenancy_id, sql.toString());
	}

	@Override
	public JSONObject selectStoreInfo(String tenancy_id) throws Exception {
		StringBuilder sql = new StringBuilder();
		sql.append(" select * from organ order by id desc ");
		List<JSONObject> query4Json = this.query4Json(tenancy_id, sql.toString());
		return query4Json.size() > 0 ? query4Json.get(0) : null;
	}

	@Override
	public JSONObject selectUnclosedBill(String tenantId, int store_id, String tableCode)
			throws Exception {
		// TODO Auto-generated method stub
		StringBuilder sql = new StringBuilder();
		sql.append(" SELECT * FROM ");
		sql.append(" 	pos_bill pb ");
		sql.append(" WHERE ");
		sql.append(" 	pb.store_id=" + store_id + " ");
		sql.append(" AND pb.table_code='" + tableCode + "' ");
		sql.append(" AND pb.bill_property='OPEN' ");
		sql.append(" AND pb.tenancy_id='" + tenantId + "' ");
		sql.append(" order by id desc ");
		List<JSONObject> query4Json = this.query4Json(tenantId, sql.toString());
		return query4Json.size() > 0 ? query4Json.get(0) : null;
	}

	@Override
	public JSONObject findLockInfoByBillNum(String tenantId, int store_id,
											String bill_num) throws Exception {
		StringBuilder sql = new StringBuilder();
		sql.append(" select * from pos_bill_lock pbl ");
		sql.append(" where pbl.bill_num='" + bill_num + "' ");
		sql.append(" and pbl.store_id=" + store_id + " ");
		sql.append(" and pbl.tenancy_id='" + tenantId + "' ");
		sql.append(" order by id desc ");
		List<JSONObject> query4Json = this.query4Json(tenantId, sql.toString());
		return query4Json.size() > 0 ? query4Json.get(0) : null;
	}

	@Override
	public JSONObject selectBillByNo(String tenantId, int store_id,
									 String billno) throws Exception {
		StringBuilder sql = new StringBuilder();
		sql.append(" SELECT * from pos_bill pb ");
		sql.append(" where pb.bill_num='" + billno + "' ");
		sql.append(" AND pb.store_id=" + store_id + " ");
		sql.append(" AND pb.tenancy_id='" + tenantId + "' ");
		List<JSONObject> query4Json = this.query4Json(tenantId, sql.toString());
		return query4Json.size() > 0 ? query4Json.get(0) : null;
	}

	@Override
	public List<JSONObject> selectDetailsInfo(String tenancy_id, int[] combo_item_ids)
			throws Exception {
		if(combo_item_ids == null || combo_item_ids.length == 0){
			return new ArrayList<JSONObject>();
		}
		StringBuilder sql = new StringBuilder();
		sql.append(" select hii.item_name as name, hicd.combo_num as number, hicd.is_itemgroup ");
		sql.append(" , hicd.details_id as id, hicd.item_unit_id as duid, hii.item_code as dishsno ");
		sql.append(" , hicd.iitem_id as item_id, hicd.id as hicd_id ");
		sql.append(" from hq_item_combo_details hicd ");
		sql.append(" left join hq_item_combo_pricesystem hicp on (hicp.combo_details_id=hicd.id and hicp.price_system='1' and hicp.chanel='WX02') ");
		sql.append(" left join hq_item_info hii on hii.id=hicd.details_id and hii.valid_state='1' ");
		sql.append(" where hicd.iitem_id in (" + SqlUtil.getInnerStr(combo_item_ids) + ") ");
		sql.append(" and hicd.valid_state='1' ");
		sql.append(" and hicd.tenancy_id='" + tenancy_id + "' order by hicd.iitem_id,hicd.combo_order,hicd.id");
		return this.query4Json(tenancy_id, sql.toString());
	}

	@Override
	public List<JSONObject> getItemInfos(String tenancy_id, String normIds) throws Exception {
		StringBuilder sql = new StringBuilder();
		sql.append(" select hii.item_code as dishsno, hii.id as id, hii.item_name as name, '' as describ");
		sql.append(" , COALESCE(hii.item_description, '') as info, 1 as type");
		sql.append(" , true as wxDishs, '' as pkid");
		sql.append(" , '' as dishkind, COALESCE(hii.photo1, '') as icon, COALESCE(hii.photo1, '') as image");
		sql.append(" , COALESCE(hii.photo1, '') as dishimg, 1 as min_unit, 1 as min_count, 1 as min_reduce");
		sql.append(" ,(CASE WHEN (select coalesce(num,0) from pos_soldout WHERE item_id = hii.ID and tenancy_id = hii.tenancy_id) <= 0 THEN 0 ELSE 1 END) AS soldouttype ");
		sql.append(" , (case when hii.is_runningprice='Y' then hiu.unit_name else '' end) as priceName ");
		sql.append(" , (case when hii.is_runningprice='Y' then 1 else 0 end) as isWeigh,hiu.id as duid");
		sql.append(" from hq_item_info hii");
		sql.append(" join hq_item_unit hiu on (hiu.item_id=hii.id and hiu.valid_state='1')");
		sql.append(" where hii.tenancy_id='"+ tenancy_id +"' and hii.id in ("+ normIds +")");
		return this.query4Json(tenancy_id, sql.toString());
	}

}
