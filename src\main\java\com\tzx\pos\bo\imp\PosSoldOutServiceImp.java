package com.tzx.pos.bo.imp;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;

import com.tzx.clientorder.acewillwechat.bo.SoldOutService;
import com.tzx.clientorder.mtwechat.bo.MtSoldOutService;
import com.tzx.clientorder.mtwechat.po.springjdbc.dao.MtSoldOutDao;
import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.DoubleHelper;
import com.tzx.framework.common.util.EhcacheUtil;
import com.tzx.framework.common.util.Scm;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.Tools;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.member.crm.bo.CustomerService;
import com.tzx.member.crm.bo.imp.CustomerServiceImp;
import com.tzx.orders.bo.OrdersSoldoutService;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.constant.SysParameter;
import com.tzx.pos.base.controller.CometDataNoticeClientRunnable;
import com.tzx.pos.base.controller.SellDishesDataUploadRunnable;
import com.tzx.pos.base.util.CacheTableUtil;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.base.util.ReqDataUtil;
import com.tzx.pos.bo.PosSoldOutService;
import com.tzx.pos.po.springjdbc.dao.PosSoldOutDao;

import net.sf.json.JSONObject;

@Service(PosSoldOutService.NAME)
public class PosSoldOutServiceImp extends PosBaseServiceImp implements PosSoldOutService
{
	private static final Logger	logger				= Logger.getLogger(PosSoldOutService.class);

	@Resource(name = SoldOutService.NAME)
    private SoldOutService soldOutService;

	@Resource(name = MtSoldOutService.NAME)
	private MtSoldOutService mtSoldOutService;
	
	@Resource(name = PosSoldOutDao.NAME)
	private PosSoldOutDao		soldOutDao;

//	@Resource(name = PosDao.NAME)
//	private PosDao			posDao;
	
	@Resource(name = MtSoldOutDao.NAME)
	private MtSoldOutDao mtSoldOutDao;
	
	private final int			scale				= 4;
	
	@SuppressWarnings("unchecked")
	@Override
	public void setSoldOut(Data param, Data result) throws Exception
	{
		String tenantId = param.getTenancy_id();
		Integer organId = param.getStore_id();

		Map<String, Object> map = ReqDataUtil.getDataMap(param);

		/** 0：设置沽清 1:取消沽清 */
		String mode = ParamUtil.getStringValue(map, "mode", true, PosErrorCode.NOT_NULL_MODE);

		Date reportDate = ParamUtil.getDateValue(map, "report_date", false, null);

		String optNum = ParamUtil.getStringValue(map, "opt_num", false, null);

		String posNum = ParamUtil.getStringValue(map, "pos_num", false, null);

		Integer shiftId = ParamUtil.getIntegerValue(map, "shift_id", false, null);
		// 沽清类型 0为临时沽清1为长期沽清
		String soldout_type = ParamUtil.getStringValue(map, "soldout_type", false, null);
		if (null == soldout_type || "".equals(soldout_type))
		{
			soldout_type = "0";
		}

		List<Object> orderItemList = (List<Object>) map.get("order_soldout");
		
		List<Map<String, Object>> list = (List<Map<String, Object>>) map.get("item");

		Timestamp currentTime = DateUtil.currentTimestamp();

		// 查询当前的估清
		List<JSONObject> oldSoldList = soldOutDao.getSoldOut(tenantId, organId);
		if ("0".equals(mode))
		{
//			List<JSONObject> oldSoldList = null;
			try
			{
//				// 先删除后插入
//				oldSoldList = soldOutDao.getSoldOutListBySoldOutType(tenantId, organId, soldout_type);
				// 先删除以前的沽清
				soldOutDao.deleteSoldOutBySoldOutType(tenantId, organId, soldout_type);

				String newstate = "";
				if (list.size() > 0)
				{
					List<Object[]> batchArgs = new ArrayList<Object[]>();
					
					//遍历历史估清
					Map<String,JSONObject> oldSoldMap = new HashMap<String, JSONObject>();
					List<String> excludeList = new  ArrayList<String>();
					List<JSONObject> oldSoldTypeList = new ArrayList<JSONObject>();
					for (JSONObject oldSoldJson : oldSoldList)
					{
						String itemId = oldSoldJson.optString("item_id");
						if(false == soldout_type.equals(oldSoldJson.optString("soldout_type")))
						{
							excludeList.add(itemId);
						}else
						{
							oldSoldMap.put(itemId, oldSoldJson);
							oldSoldTypeList.add(oldSoldJson);
						}
					}
					
					Map<String, Double> oldComboSoldOutMap = this.getSoldOutForCombo(tenantId, organId, oldSoldTypeList);
					List<String> deleteSoldOutList = new  ArrayList<String>();
					for(String itemId : oldComboSoldOutMap.keySet())
					{
						if(false == oldSoldMap.containsKey(itemId))
						{
							deleteSoldOutList.add(itemId);
						}
					}
					
					//遍历估清参数
					List<String> soldOutList = new  ArrayList<String>();//估清菜品ID
//					List<Map<String, Object>> paraList = new ArrayList<Map<String, Object>>();//新增估清菜品
					for (Map<String, Object> paraMap : list)
					{
						String itemId = null;
						if (Tools.isNullOrEmpty(paraMap.get("item_id")) == false)
						{
							itemId = paraMap.get("item_id").toString();
						}
//						Double count = 0d;
//						if (Tools.isNullOrEmpty(paraMap.get("num")) == false)
//						{
//							count = Double.parseDouble(paraMap.get("num").toString());
//						}

						soldOutList.add(itemId);
						
//						//判断参数估清菜品是否在历史估清中已有
//						if (oldSoldMap.containsKey(itemId))
//						{
//							JSONObject oldSoldJson = oldSoldMap.get(itemId);
//							if (false == count.equals(ParamUtil.getDoubleValueByObject(oldSoldJson, "num")))
//							{
//								//历史已有,但数量不一致,为新增估清
//								paraList.add(paraMap);
//							}
//							else
//							{
//								String soldoutChanel = ParamUtil.getStringValueByObject(oldSoldJson, "chanel");
//								if (!SysDictionary.SOLDOUT_CHANNEL_SET.equals(soldoutChanel))
//								{
//									batchArgs.add(new Object[]
//									{ tenantId, organId, Integer.parseInt(itemId), count, count, reportDate, DateUtil.formatDate(reportDate), optNum, currentTime, soldout_type, soldoutChanel });
//									excludeList.add(itemId);
//								}
//							}
//						}
//						else
//						{
//							paraList.add(paraMap);
//						}
					}
					
					//判断删除的估清
//					List<String> deleteSoldOutList = new  ArrayList<String>();
					for(JSONObject oldSoldOutJson : oldSoldList)
					{
						if(false == soldout_type.equals(oldSoldOutJson.optString("soldout_type")))
						{
							continue;
						}
						
						String itemId = oldSoldOutJson.optString("item_id");
						if(false == soldOutList.contains(itemId))
						{
							deleteSoldOutList.add(itemId);
						}
					}
					
					// 获取套餐估清数量
					Map<String, Double> comboSoldOutMap = this.getSoldOutForCombo(tenantId, organId, list);
					
					//通过参数添加单品估清,和手动设置的套餐估清
					List<String> setComboSoldOutList = new  ArrayList<String>();
					for (Iterator<Map<String, Object>> iterator = list.iterator(); iterator.hasNext();)
					{
						Map<String, Object> item = iterator.next();
						String itemId = null;
						if (Tools.isNullOrEmpty(item.get("item_id")) == false)
						{
							itemId = item.get("item_id").toString();
						}
						Double count = 0d;
						if (Tools.isNullOrEmpty(item.get("num")) == false)
						{
							count = Double.parseDouble(item.get("num").toString());
						}
						
						String soldoutChanel = "";
						if (item.containsKey("soldout_chanel"))
						{
							soldoutChanel = String.valueOf(item.get("soldout_chanel"));
						}
						
						if(Tools.isNullOrEmpty(soldoutChanel))
						{
							soldoutChanel = SysDictionary.SOLDOUT_CHANNEL_SET;
						}
						
						if (SysDictionary.SOLDOUT_CHANNEL_SET.equals(soldoutChanel) && comboSoldOutMap.containsKey(itemId))
						{
							if (count <= comboSoldOutMap.get(itemId))
							{
								setComboSoldOutList.add(itemId);
								batchArgs.add(new Object[]
								{ tenantId, organId, Integer.parseInt(itemId), count, count, reportDate, DateUtil.formatDate(reportDate), optNum, currentTime, soldout_type, soldoutChanel });
							}
						}
						else if (SysDictionary.SOLDOUT_CHANNEL_SET.equals(soldoutChanel))
						{
							batchArgs.add(new Object[]
							{ tenantId, organId, Integer.parseInt(itemId), count, count, reportDate, DateUtil.formatDate(reportDate), optNum, currentTime, soldout_type, soldoutChanel });
						}
					}
					
					//添加单品关联估清套餐
					for (String itemId : comboSoldOutMap.keySet())
					{
						if (false == deleteSoldOutList.contains(itemId) && false == setComboSoldOutList.contains(itemId)&& false == excludeList.contains(itemId))
						{
							batchArgs.add(new Object[]
							{ tenantId, organId, Integer.parseInt(itemId), comboSoldOutMap.get(itemId), comboSoldOutMap.get(itemId), reportDate, DateUtil.formatDate(reportDate), optNum, currentTime, soldout_type, SysDictionary.SOLDOUT_CHANNEL_LINK });
						}
					}

					soldOutDao.batchInsertSoldOut(tenantId, organId, batchArgs);

					newstate = "参数中估清菜品:" + list.size() + " ; 最终估清菜品:" + batchArgs.size();
				}

				result.setCode(Constant.CODE_SUCCESS);
				result.setMsg(Constant.SET_GUQING_SUCCESS);
				soldOutDao.savePosLog(tenantId, organId, posNum, optNum, optNum, shiftId, reportDate, Constant.TITLE, "设置沽清", "pos_num=" + posNum + ",shift_id=" + shiftId, newstate.toString());
			}
			catch (Exception e)
			{
				logger.info("设置沽清失败：" + ExceptionMessage.getExceptionMessage(e));
				result.setCode(Constant.CODE_INNER_EXCEPTION);
				result.setMsg(Constant.SET_GUQING_FAILURE);
				e.printStackTrace();
			}

//			try
//			{
//				List<JSONObject> soldList = soldOutDao.getSoldOutListBySoldOutType(tenantId, organId, soldout_type);
//
//				// 上传估清
//				Thread sellDishT = new Thread(new PosSoldOutUploadRunnable(tenantId, organId, reportDate, optNum, oldSoldList, soldList, mode));
//				sellDishT.start();
//
//				// 通知估清到前端
//				Data cjData = new Data();
//				cjData.setType(Type.SOLD_OUT);
//				cjData.setOper(Oper.init);
//				cjData.setData(soldList);
//				CometDataNoticeClientRunnable noticeClientRunnable = new CometDataNoticeClientRunnable(cjData);
//				Thread noticeThread = new Thread(noticeClientRunnable);
//				noticeThread.start();
//			}
//			catch (Exception e)
//			{
//				logger.info("上传估清失败", e);
//				e.printStackTrace();
//			}
		}
		else if ("1".equals(mode))
		{
			List<Object[]> batchArgs = new ArrayList<Object[]>();
			try
			{
				for (int i = 0; i < list.size(); i++)
				{
					Map<String, Object> item = list.get(i);
					String itemId = ParamUtil.getStringValue(item, "item_id", false, null);

					batchArgs.add(new Object[]
					{ itemId, organId });
				}

				if (0 < batchArgs.size())
				{
					soldOutDao.batchUpdate("delete from pos_soldout where item_id = ? and store_id = ?", batchArgs);
				}

				String newstate = (null != list && 0 < list.size()) ? ("取消估清菜品:" + list.size()) : "";
				result.setCode(Constant.CODE_SUCCESS);
				result.setMsg(Constant.DEL_GUQING_SUCCESS);
				soldOutDao.savePosLog(tenantId, organId, posNum, optNum, optNum, shiftId, reportDate, Constant.TITLE, "删除沽清", "pos_num=" + posNum + ",shift_id=" + shiftId, newstate);
			}
			catch (Exception e)
			{
				logger.info("删除沽清失败：" + ExceptionMessage.getExceptionMessage(e));
				result.setCode(Constant.CODE_INNER_EXCEPTION);
				result.setMsg(Constant.DEL_GUQING_FAILURE);
				e.printStackTrace();
			}
		}
		
		try
		{
			List<JSONObject> soldList = soldOutDao.getSoldOut(tenantId, organId);

			//同步估清到外卖系统
			this.uploadOrderSoldOut(tenantId, organId, reportDate, optNum, oldSoldList, soldList, orderItemList, result);
			
			// TODO 上传估清
			Thread sellDishT = new Thread(new PosSoldOutUploadRunnable(tenantId, organId, reportDate, optNum, oldSoldList, soldList, mode));
			sellDishT.start();

			// 通知估清到前端
			Data cjData = new Data();
			cjData.setType(Type.SOLD_OUT);
			cjData.setOper(Oper.init);
			cjData.setData(soldList);
			CometDataNoticeClientRunnable noticeClientRunnable = new CometDataNoticeClientRunnable(cjData);
			Thread noticeThread = new Thread(noticeClientRunnable);
			noticeThread.start();
		}
		catch (Exception e)
		{
			logger.info("上传估清失败", e);
			e.printStackTrace();
		}

	}

	private Map<String, Double> getSoldOutForCombo(String tenancyId, int storeId, List<?> soldOutList) throws Exception
	{
		Map<String, Double> comboSoldOutMap = new HashMap<String, Double>();
		if (null != soldOutList && 0 < soldOutList.size())
		{
			Map<String, Double> soldOutMap = new HashMap<String, Double>();
			StringBuffer itemIds = new StringBuffer();
			for (Object obj : soldOutList)
			{
				JSONObject soldOutJson = JSONObject.fromObject(obj);
				String itemId = ParamUtil.getStringValueByObject(soldOutJson, "item_id");
				Double num = ParamUtil.getDoubleValueByObject(soldOutJson, "num");
				soldOutMap.put(itemId, num);
				if (0 < itemIds.length())
				{
					itemIds.append(",");
				}
				itemIds.append(itemId);
			}

			// 获取项目组估清数量
			Map<String, Double> groupSoldOutMap = this.getSoldOutForGroup(tenancyId, storeId, soldOutList);
			if (null != groupSoldOutMap && false == groupSoldOutMap.isEmpty())
			{
				for (String groupId : groupSoldOutMap.keySet())
				{
					if (0 < itemIds.length())
					{
						itemIds.append(",");
					}
					itemIds.append(groupId);
				}
			}

			List<JSONObject> comboSlodOutList = soldOutDao.getItemComboForSoldOutByItemIds(tenancyId, storeId, itemIds.toString());
			for (JSONObject comboJson : comboSlodOutList)
			{
				String comboId = ParamUtil.getStringValueByObject(comboJson, "iitem_id");
				String isGroup = ParamUtil.getStringValueByObject(comboJson, "is_itemgroup");
				String itemId = ParamUtil.getStringValueByObject(comboJson, "details_id");
				Double assistNum = ParamUtil.getDoubleValueByObject(comboJson, "combo_num");

				// 获取明细菜品估清数量,默认无限大
				Double itemNum = Double.POSITIVE_INFINITY;
				if ("Y".equals(isGroup))
				{
					if (groupSoldOutMap.containsKey(itemId))
					{
						itemNum = groupSoldOutMap.get(itemId);
					}
				}
				else
				{
					if (soldOutMap.containsKey(itemId))
					{
						itemNum = soldOutMap.get(itemId);
					}
				}

				if (Double.POSITIVE_INFINITY != itemNum.doubleValue())
				{
					itemNum = DoubleHelper.roundDown(DoubleHelper.div(itemNum, assistNum, scale), 0);
				}

				// 获取套餐估清数量,等于明细菜品的估清数量最小值
				Double comboNum = itemNum;
				if (comboSoldOutMap.containsKey(comboId))
				{
					comboNum = comboSoldOutMap.get(comboId);

					if (itemNum.doubleValue() < comboNum.doubleValue())
					{
						comboNum = itemNum;
					}
				}

				if (Double.POSITIVE_INFINITY != comboNum.doubleValue())
				{
					comboSoldOutMap.put(comboId, comboNum);
				}
			}
		}
		return comboSoldOutMap;
	}

	/**
	 * 获取估清菜品影响的项目组, 项目组估清数量等于项目组菜品估清数量和
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param soldOutList
	 * @return
	 * @throws Exception
	 */
	private Map<String, Double> getSoldOutForGroup(String tenancyId, int storeId, List<?> soldOutList) throws Exception
	{
		Map<String, Double> groupSoldOutMap = new HashMap<String, Double>();
		if (null != soldOutList && 0 < soldOutList.size())
		{
			Map<String, Double> soldOutMap = new HashMap<String, Double>();
			StringBuffer itemIds = new StringBuffer();
			for (Object obj : soldOutList)
			{
				JSONObject soldOutJson = JSONObject.fromObject(obj);
				String itemId = ParamUtil.getStringValueByObject(soldOutJson, "item_id");
				Double num = ParamUtil.getDoubleValueByObject(soldOutJson, "num");
				soldOutMap.put(itemId, num);
				if (0 < itemIds.length())
				{
					itemIds.append(",");
				}
				itemIds.append(itemId);
			}

			// 根据估清菜品查询影响的项目组
			List<JSONObject> groupList = soldOutDao.getItemGroupForSoldOutByItemIds(tenancyId, storeId, itemIds.toString());
			for (JSONObject groupJson : groupList)
			{
				String itemGroupId = ParamUtil.getStringValueByObject(groupJson, "item_group_id");
				String itemId = ParamUtil.getStringValueByObject(groupJson, "item_id");
				// 获取明细菜品估清数量,默认无限大
				Double itemNum = Double.POSITIVE_INFINITY;
				if (soldOutMap.containsKey(itemId))
				{
					itemNum = soldOutMap.get(itemId);
				}

				// 获取项目组估清数量,等于明细菜品的估清数量和
				Double groupNum = itemNum;
				if (groupSoldOutMap.containsKey(itemGroupId))
				{
					groupNum = groupSoldOutMap.get(itemGroupId);
					// groupNum=groupNum+itemNum(无限大加任何数都无限大)
					if (Double.POSITIVE_INFINITY != itemNum.doubleValue() && Double.POSITIVE_INFINITY != groupNum.doubleValue())
					{
						groupNum = DoubleHelper.add(groupNum, itemNum, scale);
					}
					else
					{
						groupNum = Double.POSITIVE_INFINITY;
					}
				}
//				if (Double.POSITIVE_INFINITY != groupNum.doubleValue())
//				{
//					groupSoldOutMap.put(itemGroupId, groupNum);
//				}
				groupSoldOutMap.put(itemGroupId, groupNum);
			}
			
			Object[] keys = groupSoldOutMap.keySet().toArray();
			for (Object key : keys)
			{
				if (Double.POSITIVE_INFINITY == groupSoldOutMap.get(key))
				{
					groupSoldOutMap.remove(key);
				}
			}
		}
		return groupSoldOutMap;
	}
	
	private void uploadOrderSoldOut(String tenancyId, Integer storeId, Date reportDate, String optNum, List<JSONObject> oldSoldList, List<JSONObject> newSoldList,List<?> orderList,Data resultData) throws Exception
	{
		String wmSharestockSwitch = CacheTableUtil.getSysParameter(SysParameter.WM_SHARESTOCK_SWITCH);

		boolean isUser = false;
		Integer soldoutCount = 0;
		// 处理系统参数
		if (Tools.hv(wmSharestockSwitch))
		{
			String[] str = wmSharestockSwitch.split(";");
			if (null != str && 0 < str.length)
			{
				isUser = "1".equals(str[0]);
			}

			if (null != str && 1 < str.length && StringUtils.isNumeric(str[1]))
			{
				soldoutCount = Integer.parseInt(str[1]);
			}
		}

		if (false == isUser)
		{
			// 未启用,返回成功
			resultData.setCode(Constant.CODE_SUCCESS);
			return;
		}

		// 根据估清阈值,判断估清菜品
		List<Integer> clearItems = new ArrayList<Integer>();
		for (JSONObject json : newSoldList)
		{
			Integer itemId = ParamUtil.getIntegerValueByObject(json, "item_id");
			Integer count = ParamUtil.getIntegerValueByObject(json, "num");
			if (soldoutCount.intValue() >= count.intValue())
			{
				clearItems.add(itemId);
			}
		}

		// 根据外卖历史估清菜品,与现估清菜品对比,判断取消估清菜品
		List<Integer> fillItems = new ArrayList<Integer>();
		if (null != orderList && 0 < orderList.size())
		{
			for (Object obj : orderList)
			{
				String itemId = String.valueOf(obj);
				if (false == clearItems.contains(Integer.parseInt(itemId)))
				{
					fillItems.add(Integer.parseInt(itemId));
				}
			}
		}
		else
		{
			for (JSONObject json : oldSoldList)
			{
				Integer itemId = ParamUtil.getIntegerValueByObject(json, "item_id");
				if (false == clearItems.contains(itemId))
				{
					fillItems.add(itemId);
				}
			}
		}

		if (0 < fillItems.size() || 0 < clearItems.size())
		{
			DBContextHolder.setTenancyid(tenancyId);
			OrdersSoldoutService soldOutService = (OrdersSoldoutService) SpringConext.getApplicationContext().getBean(OrdersSoldoutService.NAME);
			Data result = soldOutService.uploadOrderSoldOut(tenancyId, storeId, reportDate, optNum, clearItems, fillItems);

			if (Constant.CODE_SUCCESS != result.getCode())
			{
				if (PosErrorCode.UPLOAD_ORDER_SOLDOUT_ERROR.getNumber() == result.getCode())
				{
					resultData.setData(result.getData());
				}
				resultData.setCode(result.getCode());
				resultData.setMsg(result.getMsg());
			}
		}
	}

	@Override
	public List<JSONObject> findSoldOut(Data param) throws Exception
	{
		String tenantId = param.getTenancy_id();
		Integer organId = param.getStore_id();

//		Map<String, Object> map = ReqDataUtil.getDataMap(param);

		return soldOutDao.findSoldOut(tenantId, organId);
	}

	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public Data modSoldOutCount(Data param) throws Exception
	{
		try
		{
			String tenantId = param.getTenancy_id();
			Integer organId = param.getStore_id();
			Map<String, Object> map = ReqDataUtil.getDataMap(param);
			Data data = new Data();
			Date reportDate = ParamUtil.getDateValue(map, "report_date", false, null);
			String optNum = ParamUtil.getStringValue(map, "opt_num", false, null);
			String mode = null;
			if (Tools.isNullOrEmpty(map.get("mode")) == false)
			{
				mode = map.get("mode").toString();
			}
			else
			{
				throw new SystemException(PosErrorCode.NOT_NULL_MODE);
			}

			List<Map<String, Object>> items = (List<Map<String, Object>>) map.get("item");

			if (items.size() == 0)
			{
				throw SystemException.getInstance(PosErrorCode.NOT_NULL_ITEM_LIST);
			}

			List<JSONObject> oldSoldList = soldOutDao.getSoldOut(tenantId, organId);
			
			// 2018-05-23添加如果菜品的估清数量已经为0，那么进行退菜时，不再增加估清为0菜品的数量；http://qy.sankuai.com/news/pages/viewpage.action?pageId=15303833
			String operName=String.valueOf(map.get("oper_name"));
			List<String> noRecoverSoldoutName=Arrays.asList("cancbill","copy_pos_bill","retreat_item");

			//如果是增加沽清数量，先查询现在门店沽清菜品为0的。这些菜品就是对应的微生活取消沽清要传的菜品
			List<JSONObject> itemList = null;
			List<JSONObject> mtItemList = null;
			if ("1".equals(mode)){
				String itemIds = "";
				for (int i=0; i < items.size(); i++){
					itemIds += items.get(i).get("item_id") + ",";
				}
				itemIds = itemIds.substring(0, itemIds.length()-1);
//				Date reportDate = ParamUtil.getDateValue(map, "report_date", false, null);
				itemList = soldOutDao.getItems(tenantId, organId, itemIds,reportDate);
				mtItemList = mtSoldOutDao.getItems(tenantId, organId, itemIds,reportDate);

			}

			Integer item_id = null;
			StringBuilder sql = new StringBuilder("select num,item_unit_id,setdate from pos_soldout where item_id = ? and store_id = ? ");
            StringBuilder usql = new StringBuilder("update pos_soldout set num = ? where item_id = ? and store_id = ? ");
			if(noRecoverSoldoutName.contains(operName)){
                usql.append(" and num>0");
            }

			List<JSONObject> soldOutList = new ArrayList<JSONObject>();

			Oper oper = null;
			List<JSONObject> soldList = new ArrayList<JSONObject>();
			try
			{
				EhcacheUtil.initCache("guqing_itemIdstr");
				// 沽清整单取消
				if(items.size()>2){
					//整理重复沽清数据
					 List list1=this.getDataLst2(items);
					 //取得重复次数
					int longMax=getMaxLongByLst(list1);
					for(int i=0;i<longMax;i++){
						List lst=new ArrayList();
						for(int j=0;j<list1.size();j++){
							List list3= (List) list1.get(j);
							if(list3!=null){
								if(list3.size()>i){
									lst.add(list3.get(i));
								}

							}

						}
						changeSoldOutDetail(tenantId, organId, soldOutList,soldList,oper,lst,item_id,mode,sql,usql);
					}
				}else{
					//单品取消或删除
					changeSoldOutDetail(tenantId, organId, soldOutList,soldList,oper,items,item_id,mode,sql,usql);
				}
				EhcacheUtil.remove("guqing_itemIdstr");
			}
			catch (Exception e)
			{
				e.printStackTrace();
				String msg=EhcacheUtil.getValue("item_name","item_name").toString();
				EhcacheUtil.removeAllEhcache("item_name");
				EhcacheUtil.removeAllEhcache("guqing_itemIdstr");
				logger.error("推送失败:", e);
				data.setCode(Constant.CODE_PARAM_FAILURE);
				data.setMsg(msg+"点菜数量大于沽清数量");
				return data;
			}
			
			try
			{
				//TODO 上传估清
				List<JSONObject> newSoldList = soldOutDao.getSoldOut(tenantId, organId);
				if (newSoldList.size() > 0)
				{
					//上传到总部
					Data subParam = new Data();
					subParam =  param.clone();
					subParam.setOper(Oper.add);
//					customerService.dishSoldOut(subParam, soldList);
					SellDishesDataUploadRunnable sellDishesDataUpload = new SellDishesDataUploadRunnable(subParam, newSoldList);
					Thread sellDishT = new Thread(sellDishesDataUpload);
					sellDishT.start();
				}
//				//给前台推送消息
                if(!noRecoverSoldoutName.contains(operName)){
            		Data cjData = new Data();
            		cjData.setType(Type.SOLD_OUT);
            		cjData.setOper(Oper.update);
            		cjData.setData(soldOutList);

            		CometDataNoticeClientRunnable noticeClientRunnable = new CometDataNoticeClientRunnable(cjData);
            		Thread noticeThread = new Thread(noticeClientRunnable);
            		noticeThread.start();
                }
				
				if("0".equals(mode)){
					//减少沽清数量，调用微生活的沽清
					soldOutService.soldOut(tenantId, organId, reportDate);
					//调用美团的沽清
					mtSoldOutService.soldOut(tenantId, organId, reportDate);
				}else if ("1".equals(mode)){
					//增加沽清数量，调用微生活的取消沽清
					soldOutService.cancelSoldOut(tenantId, organId, itemList);
					//调用美团的取消沽清
					mtSoldOutService.cancelSoldOut(tenantId, organId, mtItemList);
				}
				
				Thread sellDishT = new Thread(new PosSoldOutUploadRunnable(tenantId, organId, reportDate, optNum, oldSoldList, newSoldList, "2"));
				sellDishT.start();
			}
			catch (Exception e)
			{
				logger.error("推送失败:", e);
			}

			data.setCode(Constant.CODE_SUCCESS);
			data.setMsg(Constant.CHANG_GUQINGCOUNT_SUCCESS);
			return data;
		}
		catch (SystemException se)
		{
			throw se;
		}
		catch (Exception e)
		{
			e.printStackTrace();
			logger.info("Exception:" + ExceptionMessage.getExceptionMessage(e));
			throw new SystemException(PosErrorCode.OPER_ERROR);
		}
	}
	
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public List getDataLst2(List<Map<String, Object>> items)
	{
		List list = new ArrayList();
		Set<String> set = new HashSet<>();

		Map<String, List<Map<String, Object>>> map = new HashMap();
		List<String> itemIdList = new ArrayList<String>();
		List<Map<String, Object>> list1 = null;

		for (int i = 0; i < items.size(); i++)
		{
			Map<String, Object> map1 = items.get(i);
			String item_id = map1.get("item_id") + "";
			if (!set.contains(item_id))
			{
				itemIdList.add(item_id);
			}
			set.add(item_id);

			if (map.get(item_id) == null)
			{
				list1 = new ArrayList<>();
				list1.add(map1);
				map.put(item_id, list1);
			}
			else
			{
				List<Map<String, Object>> list2 = map.get(item_id);
				list2.add(map1);
				map.put(item_id, list2);
			}
		}

		if (!itemIdList.isEmpty())
		{
			System.out.println(itemIdList);
			for (int i = 0; i < itemIdList.size(); i++)
			{
				list.add(map.get(itemIdList.get(i)));
			}
		}

		return list;
	}
	
	@SuppressWarnings("rawtypes")
	public int getMaxLongByLst(List list){
		int res=0;
		for(int i=0;i<list.size();i++){
			List list1= (List) list.get(i);
			if(list1.size()>res){
				res=list1.size();
			}
		}
		return res;
	}
	
	/**
	 * 沽清业务处理方法
	 * @param soldOutList
	 * @param soldList
	 * @param oper
	 * @param items
	 * @param item_id
	 * @param organId
	 * @param mode
	 * @param sql
	 * @param usql
	 * @param tenancyId 
	 * @throws Exception
	 */
	public void changeSoldOutDetail(String tenancyId,Integer organId,List<JSONObject> soldOutList,List<JSONObject> soldList,Oper oper,List<Map<String, Object>> items,Integer item_id ,String mode,StringBuilder sql,StringBuilder usql) throws Exception {
		String itemsStr = listMapToString(items);
		List<JSONObject> comboItemIdlist = soldOutDao.findComboItemId(tenancyId, itemsStr); // 套餐相关的Id
		List<Map<String, Object>> itemlist = new ArrayList<Map<String, Object>>();
		if (null != comboItemIdlist && comboItemIdlist.size() > 0) {
			this.getComboItems(items, comboItemIdlist, itemlist);
		}
		//处理单个菜品。
		if(items.size()>0){
			boolean result = true;
			
			if(itemlist.size()>0){//验证关联的套餐(可选套餐)是否有沽清为0的数据 避免套餐沽清为0时 选择可选菜品 菜品沽清数量减少。
				result = this.checkComboOutItemId(itemlist, item_id, organId, sql);
			}
			if(mode.equals("0")){//点餐
				if(result){ //单品菜品关联的套餐 没有沽清数为0的套餐 进行点餐
					this.setSingleItemId(tenancyId, organId,soldOutList,soldList,items,item_id,sql,usql,mode,oper);
				}
			}else{//退菜
				this.setSingleItemId(tenancyId, organId,soldOutList,soldList,items,item_id,sql,usql,mode,oper);
			}
		}
		if(itemlist.size()>0){
			//处理套餐沽清。
			this.setComboOutItemId(soldOutList,soldList,itemlist,item_id,organId,sql,usql,mode,oper);
			
			//处理和套餐关联的套餐
			if(comboItemIdlist.size()>0){
				String itemStr1=this.listToString(comboItemIdlist);
				List <JSONObject>list1=soldOutDao.getSoldOutComboId(tenancyId, organId, null,itemStr1);
				try{
					for(JSONObject jsonObject:list1){
						item_id=jsonObject.getInt("item_id");
						List <JSONObject>list11=soldOutDao.getSoldDetailsIdComboId(tenancyId,item_id,itemsStr);
						Double p_count=getUnionComboNum(itemlist,list11);
						Double count = jsonObject.getDouble("num");
						Integer itemUnitId = ParamUtil.getIntegerValueByObject(jsonObject,"item_unit_id",false,null);
						String setdate = DateUtil.format(jsonObject.get("setdate"));
						this.setSoldOut(count, mode, p_count, item_id, organId, usql, setdate, itemUnitId, soldOutList, soldList, oper);
					}
				}catch (Exception e){
					if(Tools.isNullOrEmpty(item_id)==false){
						String msg=this.itemName(item_id);
						EhcacheUtil.initMyCache("item_name");
						EhcacheUtil.setValue("item_name","item_name",msg);
					}
					throw  e;
				}
			}
		}
	}
	
	public String listMapToString(List <Map<String,Object>>list){
		boolean flag=false;
		StringBuilder result=new StringBuilder();
		for(Map<String,Object> jsonObject:list){
			String itemIdStr=jsonObject.get("item_id").toString();
			try{
				EhcacheUtil.setValue("guqing_itemIdstr","item_idStr_"+itemIdStr,itemIdStr);
			}catch (Exception e){
				logger.error("缓存数据出错"+itemIdStr);
			}
			if (flag) {
				result.append(",");
			}else {
				flag=true;
			}
			result.append(itemIdStr);
		}
		return result.toString();
	}
	
	/**
	 * 得到套餐清单的沽清
	 * @return
	 */
	public void getComboItems(List<Map<String,Object>> items,List<JSONObject>list,List<Map<String,Object>> resLst ){
			int a=0;
			for(int i=a;i<items.size();i++){
				a=i;
				Map<String,Object>map=items.get(i);
				String itemIdStr=map.get("item_id").toString();
				if(this.ifExistItem(list,itemIdStr)){
					resLst.add(map);
					items.remove(map);
					i=a-1;
				}

			}

	}
	
	/**
	 * 判断是否存在对应Id
	 * @return
	 */
	public boolean ifExistItem(List<JSONObject>list,String itemId){
		boolean isflag=false;
		int a=0;
		for(int i=0;i<list.size();i++){
			a=i;
			JSONObject jsonObject=list.get(i);
			String item_id=jsonObject.getString("item_id");
			if(itemId.equals(item_id)){
				isflag=true;
				list.remove(jsonObject);
				i=a-1;
				break;
			}
		}
		return isflag;
	}
	
	/**
	 * 验证关联的套餐(可选套餐)是否有沽清为0的数据 避免套餐沽清为0时 选择可选菜品 菜品沽清数量减少
	 * @param items
	 * @param item_id
	 * @param organId
	 * @param sql
	 * @return
	 * @throws Exception
	 */
	public boolean checkComboOutItemId( List<Map<String, Object>> items,Integer item_id,Integer organId,StringBuilder sql)throws  Exception{
		try{
			boolean result = true;
			for (int i = 0; i < items.size(); i++) {
				Double count = null;
				if (Tools.isNullOrEmpty(items.get(i).get("item_id")) == false) {
					item_id = Integer.parseInt(items.get(i).get("item_id").toString());
				} else {
					throw SystemException.getInstance(PosErrorCode.NOT_NULL_ITEM_ID);
				}
				SqlRowSet rs = soldOutDao.query4SqlRowSet(sql.toString(), new Object[]{item_id, organId});
				while (rs.next()) {
					count = rs.getDouble("num");
				}
				if(count==0){
					result=false;
					break;
				}
			}
			return result;
		}catch (Exception e){
			throw  e;
		}


	}
	
	/**
	 * 设置单个菜品沽清
	 * @param soldOutList
	 * @param soldList
	 * @param items
	 * @param item_id
	 * @param organId
	 * @param sql
	 * @param usql
	 * @param mode
	 * @param oper
	 * @param tenancyId 
	 * @return
	 * @throws Exception
	 */
	public void setSingleItemId(String tenancyId,Integer organId,List<JSONObject> soldOutList ,List<JSONObject> soldList, List<Map<String, Object>> items,Integer item_id,StringBuilder sql,StringBuilder usql,String mode,Oper oper)throws  Exception{
		for (int i = 0; i < items.size(); i++) {
			Double p_count = null;
			Double count = null;
			if (Tools.isNullOrEmpty(items.get(i).get("item_id")) == false) {
				item_id = Integer.parseInt(items.get(i).get("item_id").toString()); // 单品id
			} else {
				throw SystemException.getInstance(PosErrorCode.NOT_NULL_ITEM_ID);
			}
			if (Tools.isNullOrEmpty(items.get(i).get("num")) == false) {
				p_count = Double.parseDouble(items.get(i).get("num").toString());
			}
			List<JSONObject> iitemIdLstOut = soldOutDao.getComboItemByItemId(tenancyId, item_id);
			String itmeStrLs = this.listToString(iitemIdLstOut); //套餐id
			if ("".equals(itmeStrLs)) {
				itmeStrLs = item_id + "";
			}
			List<JSONObject> iitemIdLst = soldOutDao.getSoldOutComboId(tenancyId, organId, item_id, itmeStrLs);
			Integer itemUnitId = 0;
			String setdate = null;
			try {
				for (JSONObject obj : iitemIdLst) {
					count = obj.getDouble("num");
					itemUnitId = ParamUtil.getIntegerValueByObject(obj, "item_unit_id", false, null);
					setdate = DateUtil.format(obj.getString("setdate"));
					if (mode.equals("0")) {
						if (item_id == obj.getInt("item_id")) {
							item_id = obj.getInt("item_id");
							this.setSoldOut(count, mode, p_count, item_id, organId, usql, setdate, itemUnitId, soldOutList,
									soldList, oper);
						}
	
					} else if (mode.equals("1")) {
						if (item_id == obj.getInt("item_id")) { // 单品处理退菜 优先处理单品菜品
							this.setSoldOut(count, mode, p_count, item_id, organId, usql, setdate, itemUnitId,
									soldOutList, soldList, oper);
							//单品 还是套餐 移动设备验证
							if(soldOutDao.isCheckComboByItemId(tenancyId, item_id.toString())){
								List<JSONObject> singeleList =  soldOutDao.getSingleByItemId(tenancyId, organId, item_id.toString());
								if(singeleList!=null && singeleList.size()>0){
									for(int j = 0;j<singeleList.size();j++){
										item_id = Integer.parseInt(singeleList.get(j).get("item_id").toString());
										count = Double.parseDouble(singeleList.get(j).get("num").toString());
										this.setSoldOut(count, mode, p_count, item_id, organId, usql, setdate, itemUnitId, soldOutList,
												soldList, oper);										
										List<JSONObject> iitemIdLstOuts = soldOutDao.getComboOutBySingelItemId(tenancyId, organId, item_id);
										for(int k = 0;k<iitemIdLstOuts.size();k++){
											if(!obj.get("item_id").toString().equals(iitemIdLstOuts.get(k).get("item_id").toString())){
												List<JSONObject> list = soldOutDao.getSoldOutComboId(tenancyId, organId, null,iitemIdLstOuts.get(k).getString("item_id"));
												if(list!=null && list.size()>0 ){
													item_id = Integer.parseInt(iitemIdLstOuts.get(k).get("item_id").toString());
													count =  Double.parseDouble(iitemIdLstOuts.get(k).get("num").toString());
													this.setSoldOut(count, mode, p_count, item_id, organId, usql, setdate, itemUnitId, soldOutList,soldList, oper);
												}
											}
										}
									}
								}
							}
						}
					}
				}
				
				// 单品退菜处理套餐
			//	if (mode.equals("0")) {
					for (JSONObject obj : iitemIdLst) {
						itemUnitId = ParamUtil.getIntegerValueByObject(obj, "item_unit_id", false, null);
						setdate = DateUtil.format(obj.getString("setdate"));
						if (item_id != obj.getInt("item_id")) { 
							Integer taocan_item_id = obj.getInt("item_id");
							//根据套餐id 获取套餐关联菜品最小沽清数量
							Integer minNumInt = soldOutDao.getSingleMinByItemId(tenancyId, organId, taocan_item_id.toString()); 
							this.setToCanSoldOut(minNumInt, mode, taocan_item_id, organId, usql, setdate, itemUnitId,
									soldOutList, soldList, oper);
						}
					}
				//}
				
			} catch (Exception e) {
				if (Tools.isNullOrEmpty(item_id) == false) {
					String msg = this.itemName(item_id);
					EhcacheUtil.initMyCache("item_name");
					EhcacheUtil.setValue("item_name", "item_name", msg);
				}

				throw e;
			}

		}

	}
	public String itemName(Integer itemId) throws Exception {
		String sql="select item_name from hq_item_info where id=? ";
		String item_name="";
		try {
			List<JSONObject> list=soldOutDao.query4Json("",sql,new Object[]{itemId});
			if(list.size()>0){
				JSONObject obj=list.get(0);
				item_name=obj.getString("item_name");
			}
		} catch (Exception e) {
			logger.error("查询菜单名报错");
			throw  e;
		}
		return item_name;
	}
	
	/**
	 * 设置沽清数据
	 * 处理沽清业务。
	 * @param count
	 * @param mode
	 * @param p_count
	 * @param item_id
	 * @param organId
	 * @param usql
	 * @param setdate
	 * @param itemUnitId
	 * @param soldOutList
	 * @param soldList
	 * @param oper
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> setSoldOut(Double count,String mode,Double p_count,Integer item_id,Integer organId,StringBuilder usql,String setdate,Integer itemUnitId,List<JSONObject> soldOutList,List<JSONObject> soldList,Oper oper) throws Exception{
		double num = 0d;
		if (count != null)
		{
			if (mode.equals("0"))
			{
				if (count < p_count)
				{

					String  item_idStr=EhcacheUtil.getValue("guqing_itemIdstr","item_idStr_"+item_id)+"";
					if(StringUtils.isNotEmpty(item_idStr) && !"null".equals(item_idStr)){
						if(item_id-Integer.parseInt(item_idStr)==0){
							throw SystemException.getInstance(PosErrorCode.GREAT_THAN_SOLD_OUT_COUNT);
						}
					}else{
						num =0;
						soldOutDao.update(usql.toString(), new Object[]
								{ num , item_id, organId });
						JSONObject soldOutJson = new JSONObject();
						soldOutJson.put("item_id", item_id);
						soldOutJson.put("item_unit_id", itemUnitId);
						soldOutJson.put("setdate", setdate);
						soldOutJson.put("num",num);
						soldOutList.add(soldOutJson);
						if (num == 0)
						{
							oper = Oper.add;
							//长传总部
							JSONObject soldJson = new JSONObject();
							soldJson.put("item_id", item_id.toString());
							soldList.add(soldJson);
						}
					}
					return soldList;
				}
				else
				{
					num = Scm.psub(count, p_count);
					soldOutDao.update(usql.toString(), new Object[]
							{ num , item_id, organId });
					JSONObject soldOutJson = new JSONObject();
					soldOutJson.put("item_id", item_id);
					soldOutJson.put("item_unit_id", itemUnitId);
					soldOutJson.put("setdate", setdate);
					soldOutJson.put("num",num);
					soldOutList.add(soldOutJson);
					if (num >= 0)
					{
						oper = Oper.add;
						//长传总部
						JSONObject soldJson = new JSONObject();
						soldJson.put("item_id", item_id.toString());
						soldList.add(soldJson);
					}
				}
			}
			if (mode.equals("1"))
			{
				num = Scm.padd(count, p_count);
				soldOutDao.update(usql.toString(), new Object[]
						{ num , item_id, organId });
				JSONObject soldOutJson = new JSONObject();
				soldOutJson.put("item_id", item_id);
				soldOutJson.put("item_unit_id", itemUnitId);
				soldOutJson.put("setdate", setdate);
				soldOutJson.put("num", num);
				soldOutList.add(soldOutJson);
			}
		}
		return soldList;
	}
	
	/**
	 * 
	  * @Description: 根据单品沽清数量设置单品关联的套餐沽清数量
	  * @Title:setToCanSoldOut
	  * @param:@param minNumInt
	  * @param:@param mode
	  * @param:@param item_id
	  * @param:@param organId
	  * @param:@param usql
	  * @param:@param setdate
	  * @param:@param itemUnitId
	  * @param:@param soldOutList
	  * @param:@param soldList
	  * @param:@param oper
	  * @param:@return
	  * @param:@throws Exception
	  * @return: List<JSONObject>
	  * @author: shenzhanyu
	  * @version: 1.0
	  * @Date: 2017年12月28日
	 */
	public List<JSONObject> setToCanSoldOut(Integer minNumInt,String mode,Integer item_id,Integer organId,StringBuilder usql,String setdate,Integer itemUnitId,List<JSONObject> soldOutList,List<JSONObject> soldList,Oper oper) throws Exception{
		if (minNumInt != null)
		{
			soldOutDao.update(usql.toString(), new Object[]
					{ minNumInt , item_id, organId });
			JSONObject soldOutJson = new JSONObject();
			soldOutJson.put("item_id", item_id);
			soldOutJson.put("item_unit_id", itemUnitId);
			soldOutJson.put("setdate", setdate);
			soldOutJson.put("num", minNumInt);
			soldOutList.add(soldOutJson);
			if (minNumInt >= 0)
			{
				oper = Oper.add;
				//长传总部
				JSONObject soldJson = new JSONObject();
				soldJson.put("item_id", item_id.toString());
				soldList.add(soldJson);
			}
		}
		return soldList;
	}
	
	/**
	 * 设置套餐沽清
	 * @param soldOutList
	 * @param soldList
	 * @param items
	 * @param item_id
	 * @param organId
	 * @param sql
	 * @param usql
	 * @param mode
	 * @param oper
	 * @return
	 * @throws Exception
	 */
	public void setComboOutItemId(List<JSONObject> soldOutList ,List<JSONObject> soldList, List<Map<String, Object>> items,Integer item_id,Integer organId,StringBuilder sql,StringBuilder usql,String mode,Oper oper)throws  Exception{
		try{
			for (int i = 0; i < items.size(); i++) {
				Double p_count = null;
				Double count = null;
				if (Tools.isNullOrEmpty(items.get(i).get("item_id")) == false) {
					item_id = Integer.parseInt(items.get(i).get("item_id").toString());
				} else {
					throw SystemException.getInstance(PosErrorCode.NOT_NULL_ITEM_ID);
				}

				if (Tools.isNullOrEmpty(items.get(i).get("num")) == false) {
					p_count = Double.parseDouble(items.get(i).get("num").toString());
				}


				SqlRowSet rs = soldOutDao.query4SqlRowSet(sql.toString(), new Object[]
						{item_id, organId});

				Integer itemUnitId = 0;
				String setdate = null;
				while (rs.next()) {
					count = rs.getDouble("num");
					itemUnitId = rs.getInt("item_unit_id");
					setdate = DateUtil.format(rs.getDate("setdate"));
				}
				this.setSoldOut(count, mode, p_count, item_id, organId, usql, setdate, itemUnitId, soldOutList, soldList, oper);
			}
		}catch (Exception e){
			if(Tools.isNullOrEmpty(item_id) == false){
				String msg=this.itemName(item_id);
				EhcacheUtil.initMyCache("item_name");
				EhcacheUtil.setValue("item_name","item_name",msg);
			}
			throw  e;
		}


	}
	
	public String listToString(List <JSONObject>list){
		boolean flag=false;
		StringBuilder result=new StringBuilder();
		for(JSONObject jsonObject:list){
			String itemIdStr=jsonObject.get("item_id").toString();
			if (flag) {
				result.append(",");
			}else {
				flag=true;
			}
			result.append(itemIdStr);
		}
		return result.toString();
	}
	
	/**
	 * 根据itemId 获取对应菜品的数量。
	 * @return
	 */
	public Double getUnionComboNum(List <Map<String,Object>>list,List <JSONObject>list1){
		Double resFlag=0.0;
		for(JSONObject jsonObject:list1){
				String details_id=jsonObject.getString("details_id");
				for(Map<String,Object>map:list){
					String item_id=map.get("item_id").toString();
					if(item_id.equals(details_id)){
						resFlag=Double.parseDouble(map.get("num")+"");
						return resFlag;
					}
				}
		}
		return resFlag;
	}
}


/**
 * 估清上传
 *
 */
class PosSoldOutUploadRunnable implements Runnable
{
	private static final Logger	logger	= Logger.getLogger(PosSoldOutService.class);

	private String				tenancyId;
	private Integer				storeId;
	private Date				reportDate;
	private String				optNum;
	private List<?>				oldSoldList;
	private List<?>				newSoldList;
	/** 0：设置沽清 1:取消沽清 2:修改估清 */
	private String				mode;

	public PosSoldOutUploadRunnable(String tenancyId, Integer storeId, Date reportDate, String optNum, List<?> oldSoldList, List<?> newSoldList, String mode)
	{
		super();
		this.tenancyId = tenancyId;
		this.storeId = storeId;
		this.reportDate = reportDate;
		this.optNum = optNum;
		this.oldSoldList = oldSoldList;
		this.newSoldList = newSoldList;
		this.mode = mode;
	}

	@Override
	public void run()
	{
		try
		{
			DBContextHolder.setTenancyid(tenancyId);
			if ("0".equals(mode))
			{
				this.setSoldOut();
			}
			else if ("2".equals(mode))
			{
				this.updateSoldOut();
			}
			else
			{
				this.cancelSoldOut();
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
			logger.info("上传估清失败:", e);
		}

	}

	private void setSoldOut() throws Exception
	{
		if (null != newSoldList && 0 < newSoldList.size())
		{
			// 当设置估清，且估清菜品不为空时，调用微生活的估清
			SoldOutService soldOutService = (SoldOutService) SpringConext.getApplicationContext().getBean(SoldOutService.NAME);
			soldOutService.wLifeSoldOut(tenancyId, storeId, reportDate);
			// 调用美团沽清
			String itemIds = this.getItemIdStr(oldSoldList, true);
			MtSoldOutService mtSoldOutService = (MtSoldOutService) SpringConext.getApplicationContext().getBean(MtSoldOutService.NAME);
			mtSoldOutService.soldOutAll(tenancyId, storeId, reportDate, itemIds);
		}
		else if (null != oldSoldList && 0 < oldSoldList.size())
		{
			String itemIds = this.getItemIdStr(oldSoldList, true);
			// 微生活取消沽清
			SoldOutService soldOutService = (SoldOutService) SpringConext.getApplicationContext().getBean(SoldOutService.NAME);
			soldOutService.cancelSoldOut(tenancyId, storeId, itemIds);
			// 美团取消沽清
			MtSoldOutService mtSoldOutService = (MtSoldOutService) SpringConext.getApplicationContext().getBean(MtSoldOutService.NAME);
			mtSoldOutService.cancelSoldOut(tenancyId, storeId, itemIds);
		}

		CustomerServiceImp customerService = null;
		if (null != oldSoldList && 0 < oldSoldList.size())
		{
			JSONObject dataJson = new JSONObject();
			dataJson.put("report_date", DateUtil.formatDate(this.reportDate));
			dataJson.put("opt_num", this.optNum);

			List<JSONObject> dataList = new ArrayList<JSONObject>();
			dataList.add(dataJson);
			// 先把总部数据都删了,上传到总部
			Data subParam = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
			subParam.setType(Type.DISH_SOLD_OUT);
			subParam.setOper(Oper.delete);
			subParam.setData(dataList);

			if (null == customerService)
			{
				customerService = (CustomerServiceImp) SpringConext.getApplicationContext().getBean(CustomerService.NAME);
			}
			customerService.dishSoldOut(subParam, oldSoldList);
		}

		if (null != newSoldList && 0 < newSoldList.size())
		{
			JSONObject dataJson = new JSONObject();
			dataJson.put("report_date", DateUtil.formatDate(this.reportDate));
			dataJson.put("opt_num", this.optNum);

			List<JSONObject> dataList = new ArrayList<JSONObject>();
			dataList.add(dataJson);
			// 上传到总部
			Data subParam = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
			subParam.setType(Type.DISH_SOLD_OUT);
			subParam.setOper(Oper.add);
			subParam.setData(dataList);

			if (null == customerService)
			{
				customerService = (CustomerServiceImp) SpringConext.getApplicationContext().getBean(CustomerService.NAME);
			}
			customerService.dishSoldOut(subParam, newSoldList);
		}
	}

	private void cancelSoldOut() throws Exception
	{
		if (0 < oldSoldList.size())
		{
			// 取消沽清，会调用微生活的取消沽清
			String itemIds = this.getItemIdStr(oldSoldList, false);

			// 微生活小程序取消估清
			SoldOutService soldOutService = (SoldOutService) SpringConext.getApplicationContext().getBean(SoldOutService.NAME);
			soldOutService.cancelSoldOut(tenancyId, storeId, itemIds);

			// 调用美团的取消沽清
			MtSoldOutService mtSoldOutService = (MtSoldOutService) SpringConext.getApplicationContext().getBean(MtSoldOutService.NAME);
			mtSoldOutService.cancelSoldOut(tenancyId, storeId, itemIds);
		}

		// 上传估清到总部
		if (0 < newSoldList.size())
		{
			JSONObject dataJson = new JSONObject();
			dataJson.put("report_date", DateUtil.formatDate(this.reportDate));
			dataJson.put("opt_num", this.optNum);

			List<JSONObject> dataList = new ArrayList<JSONObject>();
			dataList.add(dataJson);
			// 上传到总部
			Data subParam = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
			subParam.setType(Type.DISH_SOLD_OUT);
			subParam.setOper(Oper.delete);
			subParam.setData(dataList);

			CustomerServiceImp customerServiceImp = (CustomerServiceImp) SpringConext.getApplicationContext().getBean(CustomerService.NAME);
			customerServiceImp.dishSoldOut(subParam, newSoldList);
		}
	}

	private void updateSoldOut() throws Exception
	{
		String wmSharestockSwitch = CacheTableUtil.getSysParameter(SysParameter.WM_SHARESTOCK_SWITCH);

		boolean isUser = false;
		Integer soldoutCount = 0;
		// 处理系统参数
		if (Tools.hv(wmSharestockSwitch))
		{
			String[] str = wmSharestockSwitch.split(";");
			if (null != str && 0 < str.length)
			{
				isUser = "1".equals(str[0]);
			}

			if (null != str && 1 < str.length && StringUtils.isNumeric(str[1]))
			{
				soldoutCount = Integer.parseInt(str[1]);
			}
		}

		if (false == isUser)
		{
			// 未启用,返回成功
			return;
		}

		Map<String, JSONObject> soldoutMap = new HashMap<String, JSONObject>();
		if (null != oldSoldList)
		{
			for (Object obj : oldSoldList)
			{
				JSONObject soldJson = JSONObject.fromObject(obj);
				soldoutMap.put(ParamUtil.getStringValueByObject(soldJson, "item_id"), soldJson);
			}
		}

		// 根据估清阈值,判断估清菜品
		List<Integer> clearItems = new ArrayList<Integer>();
		List<Integer> fillItems = new ArrayList<Integer>();

		if(null != newSoldList)
		for (Object obj : newSoldList)
		{
			JSONObject itemJson = JSONObject.fromObject(obj);
			String itemId = ParamUtil.getStringValueByObject(itemJson, "item_id");
			Integer count = ParamUtil.getIntegerValueByObject(itemJson, "num");
			if (soldoutMap.containsKey(itemId) && count.intValue() != (ParamUtil.getIntegerValueByObject(soldoutMap.get(itemId), "num")))
			{
				if (soldoutCount.intValue() >= count.intValue())
				{
					clearItems.add(Integer.parseInt(itemId));
				}
				else
				{
					fillItems.add(Integer.parseInt(itemId));
				}
			}
		}

		if (0 < fillItems.size() || 0 < clearItems.size())
		{
			DBContextHolder.setTenancyid(tenancyId);
			OrdersSoldoutService soldOutService = (OrdersSoldoutService) SpringConext.getApplicationContext().getBean(OrdersSoldoutService.NAME);
			soldOutService.uploadOrderSoldOut(tenancyId, storeId, reportDate, optNum, clearItems, fillItems);
		}
	}
	
	public String getItemIdStr(List<?> soldList, boolean isCheckNum) throws Exception
	{
		StringBuffer itemIds = new StringBuffer();
		if (null != soldList && 0 < soldList.size())
		{
			for (Object obj : soldList)
			{
				JSONObject itemJson = JSONObject.fromObject(obj);
				Double num = ParamUtil.getDoubleValueByObject(itemJson, "num");
				if (isCheckNum && 0d == num.doubleValue())
				{
					itemIds.append(ParamUtil.getStringValueByObject(itemJson, "item_id")).append(",");
				}
			}

			if (0 < itemIds.length())
			{
				itemIds.setLength(itemIds.length() - 1);
			}
		}
		return itemIds.toString();
	}
}
