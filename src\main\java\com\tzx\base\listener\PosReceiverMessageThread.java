/**
 * 
 */
package com.tzx.base.listener;

import javax.jms.BytesMessage;
import javax.jms.Connection;
import javax.jms.ConnectionFactory;
import javax.jms.Destination;
import javax.jms.Message;
import javax.jms.MessageConsumer;
import javax.jms.QueueSession;
import javax.jms.StreamMessage;
import javax.jms.TextMessage;
import javax.servlet.ServletContext;

import org.apache.activemq.ActiveMQConnectionFactory;



import com.tzx.base.service.servlet.ProcessMessage;
import com.tzx.framework.common.constant.Constant;

/**
 * <AUTHOR>
 * 
 */
@Deprecated
public class PosReceiverMessageThread extends Thread
{
	private ServletContext		context;
	private ConnectionFactory	connectionFactory;
	private Connection			connection;
	private QueueSession		session;
	private Destination			consumerDstination;
	private MessageConsumer		consumer;
	private Message				message	= null;

	public PosReceiverMessageThread()
	{
		// this.context = context;
	}

	public PosReceiverMessageThread(ServletContext context)
	{
		this.context = context;
	}

	private void init()
	{

		try
		{
			if (connectionFactory == null)
			{
				connectionFactory = new ActiveMQConnectionFactory("", "", "failover://" + Constant.getSystemMap().get("Url"));
			}
			try
			{
				connection = connectionFactory.createConnection();
			}
			catch (Exception e)
			{
				connectionFactory = new ActiveMQConnectionFactory("", "", "failover://" + Constant.getSystemMap().get("Url"));
				connection = connectionFactory.createConnection();
			}
			connection.start();
			session = (QueueSession) connection.createSession(false, 1);
//			if ((Constant.getSystemMap().get("sotreorhq")).equals("hq"))
//			{
//				consumerDstination = session.createQueue(Constant.getSystemMap().get("qtoboh"));
//			}
			if ((Constant.getSystemMap().get("sotreorhq")).equals("boh"))
			{
				consumerDstination = session.createQueue(Constant.getSystemMap().get("qcometoboh"));
			}
			consumer = session.createConsumer(consumerDstination);
		}
		catch (Exception e)
		{
			System.out.println("connect to server error!");
			e.printStackTrace();
		}

	}

	public void run()
	{
		while (true)
		{
			try
			{
				this.init();
				message = consumer.receive();
				if (message == null)
				{
					PosReceiverMessageThread.sleep(5000);
					this.consumer.close();
					this.session.close();
					this.connection.close();
				}
				else
				{
					String msgBody = "";
					if (message instanceof TextMessage)
					{
						TextMessage textMsg = (TextMessage) message;
						// msgBody = new String(textMsg.getText().getBytes());
						msgBody = textMsg.getText();
					}
					else if (message instanceof StreamMessage)
					{
						StreamMessage streamMsg = (StreamMessage) message;
						msgBody = new String(streamMsg.readString().getBytes()); // 目前用的默认编码，所以要处理好操作系统的语言环境
					}
					else if (message instanceof BytesMessage)
					{
						BytesMessage bytesMsg = (BytesMessage) message;
						msgBody = bytesMsg.readUTF();
					}
					else
					{
						System.err.println("不支持的消息实例类型");
					}
					try
					{
						if ((Constant.getSystemMap().get("sotreorhq")).equals("boh"))
						{
							ProcessMessage processMessage = new ProcessMessage();
							String returnRes = processMessage.doExecute(msgBody);
							System.out.println("消息处理返回结果是:" + returnRes);
						}
//						if ((Constant.getSystemMap().get("sotreorhq")).equals("hq"))
//						{
//							ProcessHqMessage processMessage = new ProcessHqMessage(context);
//							String returnRes = processMessage.doExecute(msgBody);
//							System.out.println("消息处理返回结果是:" + returnRes);
//						}
					}
					catch (Exception e)
					{
						System.out.println("...up on message process error!");
						e.printStackTrace();
					}
					finally
					{
						System.out.println("处理消息完毕");
						if (consumer != null) this.consumer.close();
						if (session != null) this.session.close();
						if (connection != null) this.connection.close();
					}
				}
			}
			catch (Exception e)
			{
				e.printStackTrace();
			}
		}
	}

}
