package com.tzx.pos.service.servlet;

import com.tzx.framework.common.entity.Data;

/** 会员充值
 * <AUTHOR>
 *
 */
public interface CustomerCardRechargeServlet
{
	String	NAME	= "com.tzx.pos.service.servlet.imp.CustomerCardRechargeServletImp";
	
	
	/** 获取二维码
	 * @param tenantId
	 * @param storeId
	 * @param paramJson
	 * @param path
	 * @param resultData
	 * @throws Exception
	 */
	public Data precreatePaymentForRecharge(Data paramData, String path) throws Exception;
	
	/**扫码支付
	 * @param tenantId
	 * @param storeId
	 * @param paramJson
	 * @param resultData
	 * @throws Exception
	 */
	public Data barcodePaymentForRecharge(Data paramData) throws Exception;
	
	/**会员充值
	 * @param tenantId
	 * @param storeId
	 * @param paramJson
	 * @param resultData
	 * @return
	 * @throws Exception
	 */
	public Data customerCardRecharge(Data paramData) throws Exception;
	
	/** 查询充值状态
	 * @param tenantId
	 * @param storeId
	 * @param paramList
	 * @param resultData
	 * @throws Exception
	 */
	public Data queryCustomerCardRechargeForState(Data paramData) throws Exception;
	
	/** 取消付款
	 * @param tenantId
	 * @param storeId
	 * @param paramList
	 * @param resultData
	 * @throws Exception
	 */
	public Data cancelThirdPaymentForCustomerCardRecharge(Data paramData) throws Exception;
	
	/** 撤销充值
	 * @param tenantId
	 * @param storeId
	 * @param paramList
	 * @param resultData
	 * @throws Exception
	 */
	public Data cancelCustomerCardRecharge(Data paramData) throws Exception;
	
	
	
	
}
