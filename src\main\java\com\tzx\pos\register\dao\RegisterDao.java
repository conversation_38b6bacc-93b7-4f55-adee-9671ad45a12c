package com.tzx.pos.register.dao;

import org.springframework.jdbc.support.rowset.SqlRowSet;

import com.tzx.pos.base.dao.BaseDao;

public interface RegisterDao extends BaseDao {

	String NAME = "com.tzx.pos.register.dao.impl.RegisterDaoImpl";

	int update(String sql);

	Integer update_hq_devices_sql(String update_hq_devices_sql);

	Integer update_hq_local_devices_sql(String update_hq_local_devices_sql);

	SqlRowSet queryColumnByTableName(String tableName);

}
