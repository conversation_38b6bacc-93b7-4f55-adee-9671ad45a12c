package com.tzx.clientorder.retail.bo.impl;

import java.beans.PropertyDescriptor;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.tzx.base.bo.PosCodeService;
import com.tzx.base.common.util.StringUtil;
import com.tzx.clientorder.acewillwechat.po.springjdbc.dao.AcewillInsertOrderDao;
import com.tzx.clientorder.acewillwechat.po.springjdbc.dao.AcewillPaySucessDao;
import com.tzx.clientorder.acewillwechat.po.springjdbc.dao.WshPosEntranceDao;
import com.tzx.clientorder.common.entity.AcewillOrder;
import com.tzx.clientorder.common.entity.AcewillOrderAssist;
import com.tzx.clientorder.common.entity.AcewillOrderInfo;
import com.tzx.clientorder.common.entity.AcewillOrderItem;
import com.tzx.clientorder.common.entity.AcewillOrderItemMethod;
import com.tzx.clientorder.common.entity.AcewillOrderMember;
import com.tzx.clientorder.common.entity.AcewillOrderMemo;
import com.tzx.clientorder.common.entity.AcewillOrderNormalitem;
import com.tzx.clientorder.common.entity.AcewillOrderSetmeal;
import com.tzx.clientorder.common.entity.AcewillOrderSetmealItem;
import com.tzx.clientorder.common.entity.AcewillPayInfo;
import com.tzx.clientorder.common.entity.AcewillPayWLife;
import com.tzx.clientorder.common.entity.AcewillPaymentWay;
import com.tzx.clientorder.common.entity.AcewillPosBill;
import com.tzx.clientorder.common.entity.AcewillPosBillMember;
import com.tzx.clientorder.common.entity.AcewillPosBillPayment;
import com.tzx.clientorder.common.entity.AcewillPosBillPaymentCoupons;
import com.tzx.clientorder.retail.bo.RetailService;
import com.tzx.clientorder.retail.common.ChannelMapping;
import com.tzx.clientorder.retail.dao.RetailDao;
import com.tzx.clientorder.wlifeprogram.bo.impl.FirstPaymentServiceImpl;
import com.tzx.clientorder.wlifeprogram.common.entity.MicroLifeCoupons;
import com.tzx.clientorder.wlifeprogram.common.entity.MicroLifeGrade;
import com.tzx.clientorder.wlifeprogram.common.entity.MicroLifeOrder;
import com.tzx.clientorder.wlifeprogram.common.entity.MicroLifeOrderInfo;
import com.tzx.clientorder.wlifeprogram.common.entity.MicroLifePayOrder;
import com.tzx.clientorder.wlifeprogram.common.entity.MicroLifePayOrderData;
import com.tzx.clientorder.wlifeprogram.dao.BasicDao;
import com.tzx.clientorder.wlifeprogram.dao.FirstPaymentDao;
import com.tzx.framework.common.constant.Code;
import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.exception.OtherSystemException;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.DoubleHelper;
import com.tzx.framework.common.util.Tools;
import com.tzx.member.common.entity.CrmCardTradingListEntity;
import com.tzx.member.crm.bo.CustomerCardConsumeService;
import com.tzx.pos.asyn.pool.ThreadPool;
import com.tzx.pos.asyn.thread.OrderingPrintThread;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SelectTypeEnum;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.constant.WLifeConstant;
import com.tzx.pos.base.util.JsonUtil;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.bo.PosBaseService;
import com.tzx.pos.bo.PosDishService;
import com.tzx.pos.bo.PosOpenTableService;
import com.tzx.pos.bo.PosPrintNewService;
import com.tzx.pos.bo.PosPrintService;
import com.tzx.pos.bo.payment.PosPaymentService;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import net.sf.json.JsonConfig;
import net.sf.json.util.PropertyFilter;

@Service(RetailService.NAME)
public class RetailServiceImpl implements RetailService
{
	private static Logger				LOG						= LoggerFactory.getLogger(FirstPaymentServiceImpl.class);
	private static final String			MSG						= "msg";
	private static final String			DATA					= "data";
	private static final int			DISH_KIND_MODE_SMALL	= 2;
	private static final int			DISH_KIND_MODE_BIG		= 1;
	private static final String			SET_MEAL_TYPE			= "2";
	private static final boolean		SAVE_MEM				= false;
	private static final String			BILLSOURCE_XCX			= "zhizai_xcx";

	@Resource(name = PosBaseService.NAME)
	private PosBaseService				posBaseService;

	@Resource(name = PosPaymentService.NAME)
	private PosPaymentService			posPaymentService;

	@Resource(name = PosDishService.NAME)
	private PosDishService				posDishService;

	@Resource(name = BasicDao.NAME)
	private BasicDao					basicDao;

	@Resource(name = WshPosEntranceDao.NAME)
	private WshPosEntranceDao			wshPosEntranceDao;

	@Resource(name = RetailDao.NAME)
	private RetailDao					retailDao;

	@Resource(name = AcewillPaySucessDao.NAME)
	private AcewillPaySucessDao			paySucessDao;

	@Resource(name = PosPrintNewService.NAME)
	private PosPrintNewService			posPrintNewService;

	@Resource(name = PosPrintService.NAME)
	private PosPrintService				posPrintService;

	@Resource(name = CustomerCardConsumeService.NAME)
	private CustomerCardConsumeService	customerService;

	@Resource(name = FirstPaymentDao.NAME)
	private FirstPaymentDao				firstPaymentDao;

	@Resource(name = PosOpenTableService.NAME)
	private PosOpenTableService			openTableService;

	@Resource(name = PosCodeService.NAME)
	PosCodeService						codeService;

	@Resource(name = AcewillInsertOrderDao.NAME)
	AcewillInsertOrderDao				insertOrderDao;

	private static String[]				keys					=
	{ "wxDishs", "priceName", "isWeigh", "limitCount" };

	private static Map<String, String>	map						= new ConcurrentHashMap<String, String>();

	public void getBaseData(JSONObject reqObj, JSONObject responseObj) throws Exception
	{
		JSONObject data = new JSONObject();
		String tenancyId = reqObj.optString("tenancyId");
		String storeId = reqObj.optString("storeId");
		fillData(tenancyId, storeId, data);
		buildSuccessData(data, responseObj);
	}

	public void getDishes(JSONObject reqObj, JSONObject responseObj) throws Exception
	{
		JSONObject data = new JSONObject();
		selectDishs(reqObj, data);
		buildSuccessData(data, responseObj);
	}

	public void firstPayClose(JSONObject reqObj, JSONObject responseObj) throws Exception
	{
		String tenancyId = reqObj.optString("tenancyId");
		int storeId = reqObj.optInt("storeId");
		String outOrderId = reqObj.optString("out_order_id");
		JSONObject order = reqObj.getJSONObject("order_info");
		JSONObject payInfo = reqObj.getJSONObject("pay_info");
		if ((StringUtils.isEmpty(tenancyId)) || (storeId == 0) || (!StringUtil.hasText(outOrderId)))
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}
		if ((order.isEmpty()) || (payInfo.isEmpty()))
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}
		MicroLifeOrder microLifeOrder = new MicroLifeOrder();
		microLifeOrder.setShop_id(Integer.valueOf(storeId));
		microLifeOrder.setBusiness_id(tenancyId);

		MicroLifeOrderInfo microLifeOrderInfo = (MicroLifeOrderInfo) JSONObject.toBean(order, getOrderInfoJsonConfig());
		MicroLifePayOrder microLifePayOrder = (MicroLifePayOrder) JSONObject.toBean(payInfo, getPayJsonConfig());
		microLifePayOrder.setBusiness_id(tenancyId);
		microLifePayOrder.setShop_id(Integer.valueOf(storeId));
		MicroLifePayOrderData payOrderData = microLifePayOrder.getData();

		microLifeOrder.setOrder_info(microLifeOrderInfo);
		microLifeOrder.setOut_order_id(outOrderId);

		AcewillPosBill posBill = null;
		String oid = this.firstPaymentDao.getBillNumByOrderId(outOrderId);
		if (null != oid)
		{
			payOrderData.setOid(oid);
			JSONObject posBillObj = this.paySucessDao.getPosBillByBillNum(tenancyId, payOrderData.getOid());
			if (posBillObj == null)
			{
				// 账单不存在
				LOG.error("账单号为:{}的账单不存在", payOrderData.getOid());
				throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_BILL);
			}
			posBill = (AcewillPosBill) JSONObject.toBean(posBillObj, AcewillPosBill.class);
			// 判断账单状态
			String billProperty = posBill.getBill_property();
			if (!SysDictionary.BILL_PROPERTY_OPEN.equals(billProperty))
			{
				LOG.error("账单号为:{}的账单已关闭", payOrderData.getOid());
				throw SystemException.getInstance("账单已关闭", PosErrorCode.BILL_CLOSED);
			}
			String paymentState = posBill.getPayment_state();
			if (!SysDictionary.PAYMENT_STATE_NOTPAY.equals(paymentState))
			{
				LOG.error("账单号为:{}的账单处于支付中", payOrderData.getOid());
				throw SystemException.getInstance("账单支付中", PosErrorCode.BILL_PAYMENT_STATE_PAYING_ERROR);
			}
		}
		// 开台
		AcewillOrderAssist orderAssist = createOrderAssist(microLifeOrder);
		String billNum = openTable(microLifeOrder, false);
		orderAssist.setBill_num(billNum);
		// 下单
		JSONObject orderPrintObj = new JSONObject();
		Data orderParam = createOrderDishData(microLifeOrder, orderAssist);
		Data newestOrderDish = this.posDishService.newestOrderDish(orderParam, orderPrintObj);
		if (newestOrderDish.getCode() == 0)
		{
			LOG.info("newestOrderDish success orderNum=" + outOrderId);
		}
		else {
			throw new OtherSystemException(newestOrderDish.getCode(), newestOrderDish.getMsg(), null);
		}
		// 结账
		if (null == posBill)
		{
			LOG.info(" outOrderId=" + outOrderId + " is new order");
			payOrderData.setOid(billNum);
			JSONObject posBillObj = this.paySucessDao.getPosBillByBillNum(tenancyId, payOrderData.getOid());
			if (posBillObj == null)
			{ // 账单不存在
				LOG.error("账单号为:{}的账单不存在", payOrderData.getOid());
				throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_BILL);
			}
			posBill = (AcewillPosBill) JSONObject.toBean(posBillObj, AcewillPosBill.class);
		}
		List<JSONObject> optStateInfoList = this.posBaseService.getOptStateInfo(tenancyId, storeId, posBill.getReport_date());

		if (optStateInfoList.size() > 0)
		{
			JSONObject optStateInfo = (JSONObject) optStateInfoList.get(0);
			posBill.setPos_num(optStateInfo.getString("pos_num"));
		}

		Double paymentAmount = posBill.getPayment_amount();
		payOrderData.setAmount(paymentAmount);

		Map<String, AcewillPaymentWay> paymentWayMap = getPaymentWayMap(tenancyId, Integer.valueOf(storeId), microLifePayOrder);
		List<AcewillPosBillPayment> posBillPaymentList = new ArrayList<AcewillPosBillPayment>();
		List<AcewillPosBillMember> posBillMemberList = new ArrayList<AcewillPosBillMember>();
		List<CrmCardTradingListEntity> crmCardTradingListEntityList = new ArrayList<CrmCardTradingListEntity>();
		List<AcewillPosBillPaymentCoupons> posBillPaymentCouponsList = new ArrayList<AcewillPosBillPaymentCoupons>();
		Double sumPaymentAmount = Double.valueOf(0.0D);
		Double consumeAmount = Double.valueOf(0.0D);
		boolean isInclueBalance = false;
		dealCreditCash(microLifePayOrder.getPay_info());
		for (AcewillPayInfo acewillPayInfo : microLifePayOrder.getPay_info())
		{
			sumPaymentAmount = DoubleHelper.add(sumPaymentAmount, acewillPayInfo.getAmount(), DEFAULT_SCALE);
			AcewillPaymentWay paymentWay = getPaymentWay(paymentWayMap, acewillPayInfo);
			if (paymentWay != null)
			{
				AcewillPosBillPayment createPosBillPayment = createPosBillPayment(microLifePayOrder, posBill, paymentWay, acewillPayInfo);

				posBillPaymentList.add(createPosBillPayment);
			}

		}

		if (DoubleHelper.sub(paymentAmount, sumPaymentAmount, Integer.valueOf(4)).doubleValue() <= 0.0D)
		{
			saveDevliverInfo(microLifeOrder, order.getJSONObject("deliver_info"), sumPaymentAmount, billNum);
			this.paySucessDao.savePosBillPayment(posBillPaymentList);
			this.paySucessDao.savePosBillMember(posBillMemberList);
			this.paySucessDao.saveCrmCardTradingList(crmCardTradingListEntityList);
			this.paySucessDao.savePosBillPaymentCouponsList(posBillPaymentCouponsList);
			if (StringUtil.hasText(microLifeOrderInfo.getBillsource()))
			{
				this.firstPaymentDao.updatePosBillSource(posBill.getBill_num(), microLifeOrderInfo.getBillsource(), true);
			}

			JSONObject printJson = new JSONObject();
			JSONObject resultJson = new JSONObject();
			String isInvoice = StringUtils.isEmpty(payOrderData.getInvoice()) ? "0" : "1";
			this.posPaymentService.closedAcewillPosBill(tenancyId, storeId, posBill.getBill_num(), posBill.getReport_date(), posBill.getShift_id().intValue(), posBill.getPos_num(), posBill.getWaiter_num(), "Y", isInvoice, resultJson, printJson, "1",
					com.tzx.pos.base.util.DateUtil.currentTimestamp());

			LOG.info("pay.success:单号：" + posBill.getBill_num() + "=======================================================门店清台成功");
			try
			{
				if ((BILLSOURCE_XCX.equals(microLifeOrderInfo.getBillsource())) || (StringUtils.isBlank(microLifeOrderInfo.getBillsource())))
				{
					LOG.info("######closeBill printJson=" + printJson.toString());
					printJson.put("format_state", "2");
					this.posPrintService.printPosBillForPayment(printJson, this.posPrintNewService);
					LOG.info("pay.success:单号：" + posBill.getBill_num() + "=======================================================门店清台打印完成");
					if (isInclueBalance)
					{
						LOG.info("=========================isInclueBalance True===============");
						printMemberBalance(microLifePayOrder, posBill, consumeAmount);
					}

					if (ChannelMapping.TF.getRetailCode().equals(microLifeOrderInfo.getChannel()))
					{
						printJson.remove("format_state");
						printJson.remove("format_mode");
						printJson.remove("isprint");
						printJson.remove("print_count");
						printJson.remove("function_id");
						printJson.remove("rwidsbk");
						printJson.put("mode", "0");
						printJson.put("order_num", outOrderId);
						printJson.put("need_invoice", isInvoice);
						printJson.put("invoice_amount", paymentAmount);
						printJson.put("is_order_print", Boolean.valueOf(true));
						printJson.put("is_printbill", Boolean.valueOf(true));
						LOG.info("######WMBill printJson=" + printJson.toString());
						this.posPrintNewService.posPrintByMode(tenancyId, storeId, SysDictionary.PRINT_CODE_1201, printJson);
					}
				}
				else
				{
					LOG.info("billSource={}", microLifeOrderInfo.getBillsource());
				}
			}
			catch (Exception e)
			{
				LOG.error("print failed... bill_number:" + posBill.getBill_num());
			}
		}
		else
		{
			throw SystemException.getInstance(PosErrorCode.BILL_DIFFERENCE_MORE_ZERO_ERROR);
		}
		JSONObject data = new JSONObject();
		data.put("oid", billNum);
		buildSuccessData(data, responseObj);
	}

	private void saveDevliverInfo(MicroLifeOrder microLifeOrder, JSONObject deliverInfo, Double payAmount, String billNum) throws Exception
	{
		if ((null != deliverInfo) && (!deliverInfo.isEmpty()))
		{
			deliverInfo.put("order_code", microLifeOrder.getOut_order_id());
			deliverInfo.put("source_order_code", microLifeOrder.getOut_order_id());
			deliverInfo.put("actual_price", payAmount);
			deliverInfo.put("shop_id", microLifeOrder.getShop_id());
			deliverInfo.put("channel", microLifeOrder.getOrder_info().getChannel());
			deliverInfo.put("state", "01");
			deliverInfo.put("create_time", com.tzx.framework.common.util.DateUtil.getNowDateYYDDMMHHMMSS());
			deliverInfo.put("is_mock", "0");
			deliverInfo.put("third_order_id", billNum);
			deliverInfo.put("isactive", "1");
			this.retailDao.saveDeliveInfo(microLifeOrder.getBusiness_id(), deliverInfo);
		}
	}

	private void fillData(String tenancy_id, String store_id, JSONObject data) throws Exception
	{
		selectKindMemos(tenancy_id, store_id, data);
		selectMemo(tenancy_id, store_id, data);
		selectDishKinds(tenancy_id, store_id, data, DISH_KIND_MODE_SMALL);
		selectTables(tenancy_id, store_id, data);
		selectHeadActivity(tenancy_id, store_id, data);
		selectMarketing(tenancy_id, store_id, data);
		selectShops(tenancy_id, store_id, data);
	}

	/** 菜品备注数据
	 * @param tenancy_id
	 * @param store_id
	 * @param context
	 * @throws Exception
	 */
	private void selectKindMemos(String tenancy_id, String store_id, JSONObject context) throws Exception
	{
		List<JSONObject> datas = this.basicDao.getMemos(tenancy_id, store_id);
		JSONObject data = new JSONObject();
		data.put("all", datas);
		context.put(SelectTypeEnum.KINDS_MEMO.name, data);
	}

	private void selectMemo(String tenancy_id, String store_id, JSONObject context) throws Exception
	{
		List<JSONObject> datas = this.wshPosEntranceDao.selectMemo(tenancy_id, store_id);
		context.put(SelectTypeEnum.MEMO.name, datas);
	}

	/** 查询菜品类别
	 * @param tenancy_id
	 * @param store_id
	 * @param context
	 * @param chanel
	 * @param classMode
	 * @throws Exception
	 */
	private void selectDishKinds(String tenancy_id, String store_id, JSONObject context, int classMode) throws Exception
	{
		//查询所有渠道的菜品类别
		List<JSONObject> itemClassList = new ArrayList<JSONObject>();
		List<JSONObject> tfItemClassList = null;
		for (ChannelMapping channel : ChannelMapping.values())
		{
			tfItemClassList = this.basicDao.getDishKinds(tenancy_id, Integer.valueOf(store_id), channel.getChannlCode());
			if ((null != tfItemClassList) && (tfItemClassList.size() > 0))
			{
				itemClassList.addAll(tfItemClassList);
			}
		}

		List<JSONObject> retList = new ArrayList<JSONObject>();
		for (JSONObject itemClassJson : itemClassList)
		{
			String pdkid = itemClassJson.optString("pdkid");
			if (Tools.isNullOrEmpty(pdkid))
			{
				pdkid = "0";
			}

			if ((DISH_KIND_MODE_BIG == classMode) && ("0".equals(pdkid)))
			{
				String classId = itemClassJson.optString("id");
				itemClassJson.put("pkid", "0");
				itemClassJson.put("must", "0");
				itemClassJson.put("must_seq", "0");
				itemClassJson.put("suggest", "0");
				itemClassJson.put("icon", "");
				List<String> children = new ArrayList<String>();
				Integer dishCount = Integer.valueOf(0);
				for (JSONObject childrenJson : itemClassList)
				{
					String childrenId = childrenJson.optString("id");
					String fatherId = childrenJson.optString("pdkid");
					if ((classId.equals(fatherId)) && (false == children.contains(childrenId)))
					{
						children.add(childrenId);
						dishCount = Integer.valueOf(dishCount.intValue() + childrenJson.optInt("dish_count"));
					}
				}
				itemClassJson.put("children", children);
				itemClassJson.put("dish_count", dishCount);
				retList.add(itemClassJson);
			}
			else
			{
				itemClassJson.put("pkid", pdkid);
				itemClassJson.put("must", "0");
				itemClassJson.put("must_seq", "0");
				itemClassJson.put("suggest", "0");
				itemClassJson.put("icon", "");
				itemClassJson.put("children", "[]");
				retList.add(itemClassJson);
			}
		}
		context.put(SelectTypeEnum.DISH_KINDS.name, retList);
	}

	/**桌台信息
	 * @param tenancy_id
	 * @param store_id
	 * @param context
	 * @throws Exception
	 */
	private void selectTables(String tenancy_id, String store_id, JSONObject context) throws Exception
	{
		List<JSONObject> datas = this.basicDao.getTables(tenancy_id, store_id);
		context.put(SelectTypeEnum.TABLES.name, datas);
	}

	/**公告信息
	 * @param tenancy_id
	 * @param store_id
	 * @param context
	 * @throws Exception
	 */
	private void selectHeadActivity(String tenancy_id, String store_id, JSONObject context) throws Exception
	{
		// 目前当前系统没有公告信息
		context.put(SelectTypeEnum.HEAD_ACTIVITY.name, new JSONObject());
	}

	/**营销活动信息
	 * @param tenancy_id
	 * @param store_id
	 * @param context
	 * @throws Exception
	 */
	private void selectMarketing(String tenancy_id, String store_id, JSONObject context) throws Exception
	{
		// 目前当前系统没有营销活动
		context.put(SelectTypeEnum.MARKETING.name, new JSONArray());
	}

	/** 门店信息
	 * @param tenancy_id
	 * @param store_id
	 * @param context
	 * @throws Exception
	 */
	private void selectShops(String tenancy_id, String store_id, JSONObject context) throws Exception
	{
		List<JSONObject> datas = retailDao.getShops(tenancy_id, store_id);
		JSONObject shopJson = new JSONObject();
		if (datas.size() > 0)
		{
			shopJson = datas.get(0);
		}
		context.put(SelectTypeEnum.SHOPS.name, shopJson);
	}

	/** 查询菜品信息
	 * @param reqObj
	 * @param context
	 * @throws Exception
	 */
	private void selectDishs(JSONObject reqObj, JSONObject context) throws Exception
	{
		String tenancyId = reqObj.optString("tenancyId");
		String storeId = reqObj.optString("storeId");
		String channel = reqObj.optString("channel");
		int page = reqObj.optInt("page");
		int rows = reqObj.optInt("rows");
		if (rows > 500)
		{
			rows = 500;
		}

		JSONObject organ = this.wshPosEntranceDao.selectOrganInfo(tenancyId, storeId);
		String price_system = organ.optString("price_system", "1");
		JSONObject dishs = new JSONObject();

		JSONObject pageParams = new JSONObject();
		pageParams.put("page", Integer.valueOf(page));
		pageParams.put("rows", Integer.valueOf(rows));
		channel = ChannelMapping.getChannelMapping(channel).getChannlCode();
		List<JSONObject> item_infos = this.retailDao.getItemInfos(pageParams, tenancyId, storeId, channel);

		replaceName(item_infos);
		for (int i = 0; i < item_infos.size(); i++)
		{
			JSONObject json = (JSONObject) item_infos.get(i);
			String dishkind = json.optString("dishkind");

			dishkind = json.optString("pkid");

			if ((dishkind == null) || ("null".equals(dishkind)))
			{
				json.put("dishkind", new String[0]);
			}
			else
			{
				String[] arr =new String[]{ dishkind };
				json.put("dishkind", arr);
			}

			String dishsno = json.optString("dishsno");
			dishs.put(dishsno, json);
		}

		int[] item_ids = getItemIds(item_infos);

		List<JSONObject> unit_infos = this.retailDao.getUnitInfos(tenancyId, item_ids, price_system, channel);
		for (JSONObject unit : unit_infos)
		{
			unit.put("bmemberprice", Integer.valueOf(1));
			unit.put("membergid", "");
		}
		replaceName(unit_infos);
		Map<Object, List<JSONObject>> unitMap = buildMap(unit_infos, "item_id");

		List<JSONObject> cooks_infos = this.retailDao.getMethodInfos(tenancyId, item_ids, storeId, channel);
		Map<Object, List<JSONObject>> cooksMap = buildMap(cooks_infos, "item_id");

		int[] combo_item_ids = getComboItemIds(item_infos);
		List<JSONObject> selectCombInfos = selectCombInfos(tenancyId, combo_item_ids, price_system, channel);
		for (JSONObject comb : selectCombInfos)
		{
			comb.put("membergid", "");
			comb.put("bmemberprice", Integer.valueOf(1));
		}
		replaceName(unit_infos);
		Map<Object, List<JSONObject>> combMap = buildMap(selectCombInfos, "item_id");

		Iterator<?> iterator = dishs.values().iterator();
		while (iterator.hasNext())
		{
			JSONObject json = (JSONObject) iterator.next();
			int item_id = json.optInt("id");
			if (unitMap.containsKey(Integer.valueOf(item_id)))
			{
				json.put("norms", unitMap.get(Integer.valueOf(item_id)));
				if (Tools.isNullOrEmpty(json.optString("priceName"))){
                    json.put("priceName", unitMap.get(item_id).get(0).optString("name"));
                }
			}
			else
			{
				json.put("norms", new JSONArray());
			}

			if (cooksMap.containsKey(Integer.valueOf(item_id))) 
			{
				json.put("cooks", cooksMap.get(Integer.valueOf(item_id)));
			}
			else
			{
				json.put("cooks", new JSONArray());
			}

			if (combMap.containsKey(Integer.valueOf(item_id)))
			{
				json.put("setmeals", ((List) combMap.get(Integer.valueOf(item_id))).get(0));
			}
			else
			{
				json.put("setmeals", new JSONObject());
			}
		}
		long count = this.retailDao.getItemInfoCount(tenancyId, storeId, channel);
		context.put("page", Integer.valueOf(page));
		context.put("total", Long.valueOf(count));
		context.put(SelectTypeEnum.DISHS.name, dishs);
	}

	/**
	 * @param tenancy_id
	 * @param combo_item_ids
	 * @param price_system
	 * @param channel
	 * @return
	 * @throws Exception
	 */
	private List<JSONObject> selectCombInfos(String tenancy_id, int[] combo_item_ids, String price_system, String channel) throws Exception
	{
        //查询套餐信息
        List<JSONObject> default_unit_infos = retailDao.getComboBaseInfo(tenancy_id, combo_item_ids, price_system, channel);

        //套餐的主菜和辅菜都查询出来
        List<JSONObject> detailsInfo = retailDao.selectDetailsInfo(tenancy_id, combo_item_ids, channel);
        // 查询主菜和辅菜
        List<JSONObject> maindish = new ArrayList<JSONObject>();
        Map<Integer, Integer> isGroup = new HashMap<Integer, Integer>();
        for(JSONObject detail : detailsInfo){
            int item_id = detail.optInt("item_id");
            if("N".equals(detail.optString("is_itemgroup"))){
                // 套餐主菜
                maindish.add(detail);
            }else if("Y".equals(detail.optString("is_itemgroup"))){
                // 辅菜
                isGroup.put(detail.optInt("hicd_id"), item_id);
            }
        }
        // 查询主菜信息
        Map<Integer, List<JSONObject>> map0 = buildMap(maindish, "item_id");	// id为菜品信息id

        // 查询辅菜信息
        int[] hicdIds = transferIntegerArray(isGroup.keySet());
        List<JSONObject> mandatoryInfos = getMandatoryInfos(tenancy_id, hicdIds);
        for(JSONObject json : mandatoryInfos){
            Integer hicd_id = json.optInt("hicd_id");
            json.put("item_id", isGroup.get(hicd_id));
        }
        Map<Integer, List<JSONObject>> map1 = buildMap(mandatoryInfos, "item_id");

        // 将主菜和辅菜挂到对应菜品信息下
        for(JSONObject json : default_unit_infos){
            Integer item_id = json.optInt("item_id");
            if(map0.containsKey(item_id)){
                json.put("maindish", map0.get(item_id));
            }else{
                json.put("maindish", new JSONArray());
            }
            if(map1.containsKey(item_id)){
                json.put("mandatory", map1.get(item_id));
            }else{
                json.put("mandatory", new JSONArray());
            }
            json.put("optional", new JSONArray());
            json.put("membergid", new JSONArray());

        }
        return default_unit_infos;
    }

	private List<JSONObject> getMandatoryInfos(String tenancy_id, int[] hicdIds) throws Exception
	{
		 //查询套餐辅菜
        List<JSONObject> list = basicDao.getMandatoryBaseInfo(tenancy_id, hicdIds);
        Set<Integer> groupIdSet = new HashSet<Integer>();
        for(JSONObject json : list){
            Integer id =  json.optInt("mid");
            groupIdSet.add(id);
        }
        int[] groupIds = transferIntegerArray(groupIdSet);
        //查询套餐辅菜的菜品组详细
        List<JSONObject> groupDetails = basicDao.getGroupDetails(tenancy_id, groupIds);
        Map<Integer, List<JSONObject>> groupDetailMap = buildMap(groupDetails, "rpdid");
        for(JSONObject json : list){
            Integer group_id =  json.optInt("mid");
            if(groupDetailMap.containsKey(group_id)){
                json.put("items", groupDetailMap.get(group_id));
            }
        }
        return list;
	}

	private int[] getComboItemIds(List<JSONObject> item_infos)
	{
		Set<Integer> itemIdSet = new HashSet<Integer>();
        for(JSONObject json : item_infos){
            String isComb = json.optString("type", "1");
            if("2".equals(isComb)){
                Integer item_id = json.optInt("id");
                itemIdSet.add(item_id);
            }
        }
        int[] ids = transferIntegerArray(itemIdSet);
        return ids;
	}

	private int[] getItemIds(List<JSONObject> item_infos)
	{
		Set<Integer> itemIdSet = new HashSet<Integer>();
        for(JSONObject json : item_infos){
            Integer item_id = json.optInt("id");
            itemIdSet.add(item_id);
        }
        int[] ids = transferIntegerArray(itemIdSet);
        return ids;
	}

	private int[] transferIntegerArray(Collection<Integer> collection)
	{
		Integer[] array = collection.toArray(new Integer[0]);
        int[] ids = new int[array.length];
        for(int i = 0; i < ids.length; i++){
            ids[i] = array[i];
        }
        return ids;
	}

	private <T> Map<T, List<JSONObject>> buildMap(List<JSONObject> list, String attr)
	{
		Map<T, List<JSONObject>> map = new HashMap<T, List<JSONObject>>();
		for (JSONObject json : list)
		{
			T key = (T) json.opt(attr);
			if (!map.containsKey(key))
			{
				map.put(key, new ArrayList<JSONObject>());
			}
			map.get(key).add(json);
		}
		return map;
	}

	private void buildSuccessData(JSONObject data, JSONObject responseData)
	{
		if (null == data)
		{
			data = new JSONObject();
		}
		responseData.put("success", Integer.valueOf(1));
		responseData.put(MSG, "操作成功！");
		responseData.put(DATA, data);
	}

	private static void replaceName(List<JSONObject> list)
	{
		for (JSONObject json : list)
			replaceName(json);
	}

	private static void replaceName(JSONObject json)
	{
		String[][] temp = new String[2][1];
        Set<String> set = new HashSet<String>(json.keySet());
        Iterator<String> iterator = set.iterator();
        while(iterator.hasNext()){
            String next = iterator.next();
            if(map.containsKey(next)){
                temp[0][0] = next;
                temp[1][0] = map.get(next);
                JsonUtil.renameKey(json, temp[0] , temp[1]);
            }
        }
	}

	private void dealCreditCash(List<AcewillPayInfo> acewillPayInfos)
	{
		double amount = 0.0D;
		for (AcewillPayInfo acewillPayInfo : acewillPayInfos)
		{
			if (WLifeConstant.PAY_SOURCE_CREDIT.equals(acewillPayInfo.getSource()))
			{
				amount = acewillPayInfo.getAmount().doubleValue();
			}
		}
		for (AcewillPayInfo acewillPayInfo : acewillPayInfos)
		{
			if (WLifeConstant.PAY_SOURCE_WLIFE.equals(acewillPayInfo.getSource()))
			{
				acewillPayInfo.setCreditCash(Double.valueOf(amount));
			}
		}
	}

	private void printOrderInfo(String tenancyId, Integer storeId, JSONObject printJson)
	{
		Data data = new Data();
		data.setTenancy_id(tenancyId);
		data.setStore_id(storeId.intValue());
		OrderingPrintThread orderingPrintThread = new OrderingPrintThread(printJson, data);
		ThreadPool.getPrintThreadPool().execute(orderingPrintThread);
	}

	private void setOrderMemberInfo(MicroLifeOrderInfo microLifeOrderInfo, JSONObject orderInfo)
	{
		String name = orderInfo.optString("name");
		String mobile = orderInfo.optString("mobile");
		String openid = orderInfo.optString("openid");
		Double balance = Double.valueOf(orderInfo.optDouble("balance", 0.0D));
		Double credit = Double.valueOf(orderInfo.optDouble("credit", 0.0D));
		AcewillOrderMember member = new AcewillOrderMember();
		member.setName(name);
		member.setOpenid(openid);
		member.setMobile(mobile);
		member.setCredit(credit);
		member.setBalance(balance);
		microLifeOrderInfo.setMember(member);
	}

	private void printMemberBalance(MicroLifePayOrder microLifePayOrder, AcewillPosBill posBill, Double consumeAmount) throws Exception
	{
		AcewillPayWLife wlife = microLifePayOrder.getWlife();
		JSONObject printMemberBlance = new JSONObject();

		String cardCode = wlife.getCno();
		if (StringUtil.hasText(cardCode))
		{
			JSONObject memberInfo = this.firstPaymentDao.getBillMemberInfo(posBill.getBill_num(), "JF01", cardCode);
			if (null != memberInfo)
			{
				if (StringUtil.hasText(memberInfo.optString("mobil"))) printMemberBlance.put("mobil", memberInfo.optString("mobil"));
				else
				{
					printMemberBlance.put("mobil", "");
				}
				printMemberBlance.put("useful_credit", Double.valueOf(memberInfo.optDouble("consume_before_credit")));
			}
			else
			{
				printMemberBlance.put("mobil", "");
				printMemberBlance.put("useful_credit", Integer.valueOf(0));
			}

			printMemberBlance.put("isprint", "Y");
			printMemberBlance.put("pos_num", posBill.getPos_num());
			printMemberBlance.put("print_time", com.tzx.framework.common.util.DateUtil.format(com.tzx.framework.common.util.DateUtil.currentTimestamp()));
			printMemberBlance.put("bill_code", posBill.getBill_num());
			printMemberBlance.put("card_code", cardCode);
			printMemberBlance.put("level_name", wlife.getGrade_name());
			printMemberBlance.put("card_class_name", "");
			printMemberBlance.put("name", wlife.getName());
			if (wlife.getBalance().doubleValue() > 0.0D)
			{
				wlife.setBalance(DoubleHelper.div(wlife.getBalance(), Double.valueOf(100.0D), Integer.valueOf(4)));
			}
			printMemberBlance.put("consume_cardmoney", consumeAmount);
			printMemberBlance.put("main_balance", wlife.getBalance());
			printMemberBlance.put("reward_balance", Integer.valueOf(0));

			String operator = this.firstPaymentDao.getEmpNameById(posBill.getOpen_opt(), microLifePayOrder.getBusiness_id(), microLifePayOrder.getShop_id());
			printMemberBlance.put("operator", operator);
			printMemberBlance.put("updatetime", com.tzx.framework.common.util.DateUtil.format(com.tzx.framework.common.util.DateUtil.currentTimestamp()));

			LOG.info(" print member balance start pararms =" + printMemberBlance.toString());
			this.customerService.customerCardConsumePrint(microLifePayOrder.getBusiness_id(), microLifePayOrder.getShop_id().intValue(), posBill.getPos_num(), printMemberBlance, "1010");

			LOG.info(" print member balance end tenancyId =" + microLifePayOrder.getBusiness_id() + ",storeId=" + microLifePayOrder.getShop_id() + ",posNum=" + posBill.getPos_num());
		}
	}

	private Map<String, AcewillPaymentWay> getPaymentWayMap(String tenantId, Integer storeId, MicroLifePayOrder microLifePayOrder) throws Exception
	{
		List<String> paymentClassList = new ArrayList<>();
        for (AcewillPayInfo payInfo : microLifePayOrder.getPay_info()) {
            String paymentClass = paymentWayMapping.get(payInfo.getSource());
            if (!org.apache.commons.lang.StringUtils.isEmpty(paymentClass)) {
                paymentClassList.add(paymentClass);
            }
        }
        List<JSONObject> paymentWayList = paySucessDao.findPaymentWay(tenantId, storeId, paymentClassList);
        Map<String, AcewillPaymentWay> paymentWayMap = new HashMap<>();
        if (paymentWayList != null) {
            for (JSONObject jsonObject : paymentWayList) {
                AcewillPaymentWay bean = JsonUtil.jsonToBean(jsonObject, AcewillPaymentWay.class);
                paymentWayMap.put(bean.getPayment_class(), bean);
            }
        }
        return paymentWayMap;
	}

	private AcewillPaymentWay getPaymentWay(Map<String, AcewillPaymentWay> paymentWayMap, AcewillPayInfo payInfo) throws Exception
	{
		String paymentClass = (String) paymentWayMapping.get(payInfo.getSource());
		if (paymentClass == null)
		{
			LOG.error("未知的支付方式：{}", payInfo.getSource());
			return null;
		}
		AcewillPaymentWay paymentWay = (AcewillPaymentWay) paymentWayMap.get(paymentClass);
		if (paymentWay == null)
		{
			LOG.error("支付方式{}未启用", payInfo.getSource());
			return null;
		}
		return paymentWay;
	}

	private AcewillPosBillPayment createPosBillPayment(MicroLifePayOrder microLifePayOrder, AcewillPosBill posBill, AcewillPaymentWay paymentWay, AcewillPayInfo payInfo) throws Exception
	{
		AcewillPayWLife wlife = microLifePayOrder.getWlife();

		AcewillPosBillPayment posBillPayment = new AcewillPosBillPayment();
		posBillPayment.setTenancy_id(microLifePayOrder.getBusiness_id());
		posBillPayment.setStore_id(microLifePayOrder.getShop_id());
		posBillPayment.setId(null);
		posBillPayment.setBill_num(posBill.getBill_num());
		posBillPayment.setTable_code(posBill.getTable_code());
		posBillPayment.setType(paymentWay.getPayment_class());
		posBillPayment.setJzid(paymentWay.getId());
		posBillPayment.setName(paymentWay.getPayment_name1());
		posBillPayment.setName_english(paymentWay.getPayment_name2());
		posBillPayment.setAmount(payInfo.getAmount());
		posBillPayment.setCount(1);
		// 付款号码
		if (wlife != null)
		{
			posBillPayment.setNumber(wlife.getCno());
			posBillPayment.setPhone(null);
		}
		posBillPayment.setReport_date(com.tzx.pos.base.util.DateUtil.parseDate(posBill.getReport_date()));
		posBillPayment.setShift_id(posBill.getShift_id());
		posBillPayment.setPos_num(posBill.getPos_num());
		// 收款员号
		posBillPayment.setCashier_num(posBill.getWaiter_num());
		posBillPayment.setLast_updatetime(new Date());
		posBillPayment.setIs_ysk("N");
		posBillPayment.setRate(1d);
		posBillPayment.setCurrency_amount(payInfo.getAmount());
		// 上传标记
		posBillPayment.setUpload_tag(0);
		// 挂账人id
		posBillPayment.setCustomer_id(null);
		// 付款流水号
		posBillPayment.setBill_code(payInfo.getSerilNo());
		posBillPayment.setRemark(null);
		posBillPayment.setPayment_state(SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
		// 与总部通讯的缓存
		posBillPayment.setParam_cach(null);
		// 批次编号
		posBillPayment.setBatch_num(posBill.getBatch_num());
		// 多收礼卷
		posBillPayment.setMore_coupon(0d);
		posBillPayment.setFee(posBill.getService_amount());
		posBillPayment.setFee_rate(posBill.getService_discount() != null ? posBill.getService_discount().doubleValue() : null);
		// 优惠券类型
		String coupon_type = posBillCouponTypeMapping.get(payInfo.getSource());
		posBillPayment.setCoupon_type(coupon_type != null ? Integer.parseInt(coupon_type) : null);
		// 原付款方式ID
		posBillPayment.setYjzid(null);
		// 用户实付
		// posBillPayment.setCoupon_Buy_Price(coupon_buy_price);
		// 券账单净收
		// posBillPayment.setDue(due);
		// // 商家优惠承担
		// posBillPayment.setTenancy_assume(payInfo.getStorepay());
		if (WLifeConstant.PAY_SOURCE_BALANCE.equals(payInfo.getSource()))
		{
			// 用户实付
			Double tmpAmount = (payInfo.getStorepay() != null ? payInfo.getStorepay() / 100 : 0.00);
			posBillPayment.setCoupon_buy_price(tmpAmount);// 用户实付
			// 券账单净收
			posBillPayment.setDue(tmpAmount);
			// 商家优惠承担(差额)
			posBillPayment.setTenancy_assume(payInfo.getAmount() - tmpAmount);
		}
		else
		{
			// 商家优惠承担
			posBillPayment.setTenancy_assume(payInfo.getStorepay());
		}
		// 第三方优惠承担
		// posBillPayment.setThird_Assume(third_assume);
		// 第三方票券服务费
		// posBillPayment.setThird_Fee(third_fee);
		return posBillPayment;
	}

	private AcewillPosBillMember createPosBillMember(MicroLifePayOrder microLifePayOrder, AcewillPosBill posBill, AcewillPayInfo payInfo) throws Exception
	{
		AcewillPayWLife wlife = microLifePayOrder.getWlife();
		Double balance = wlife.getBalance();
		wlife.setBalance(DoubleHelper.div(wlife.getBalance(), 100d, DEFAULT_SCALE));
		AcewillPosBillMember posBillMember = new AcewillPosBillMember();
		posBillMember.setTenancy_id(microLifePayOrder.getBusiness_id());
		posBillMember.setStore_id(microLifePayOrder.getShop_id());
		posBillMember.setId(null);
		posBillMember.setBill_num(posBill.getBill_num());
		posBillMember.setReport_date(com.tzx.pos.base.util.DateUtil.parseDate(posBill.getReport_date()));
		// 类型
		posBillMember.setType(posBillMemberTyoeMapping.get(payInfo.getSource()));
		posBillMember.setAmount(payInfo.getAmount());
		if (WLifeConstant.PAY_SOURCE_WLIFE.equals(payInfo.getSource()))
		{
			posBillMember.setCredit(wlife.getReceive_credit());
		}
		else
		{
			posBillMember.setCredit(0.0);
		}

		posBillMember.setCard_code(wlife.getCno());
		JSONObject memberInfo = firstPaymentDao.getBillMemberInfo(posBill.getBill_num(), SysDictionary.BILL_MEMBERCARD_JFZS06, wlife.getCno());
		if (null != memberInfo)
		{
			if (StringUtil.hasText(memberInfo.optString("mobil")))
			{
				posBillMember.setMobil(memberInfo.optString("mobil"));
			}
		}

		posBillMember.setLast_updatetime(new Date());
		// 上传标记
		posBillMember.setUpload_tag(0);
		posBillMember.setRemark(null);
		posBillMember.setBill_code(payInfo.getSerilNo());
		// 请求状态
		posBillMember.setRequest_state(null);
		posBillMember.setCustomer_code(wlife.getCno());
		posBillMember.setCustomer_name(wlife.getName());
		// 交易前积分
		if (WLifeConstant.PAY_SOURCE_CREDIT.equals(payInfo.getSource()))
		{
			posBillMember.setConsume_before_credit(wlife.getCredit() + payInfo.getAmount());
		}
		else
		{
			Double consumeBeforeCredit = DoubleHelper.add(wlife.getCredit().doubleValue(), payInfo.getCreditCash(), DEFAULT_SCALE);
			posBillMember.setConsume_before_credit(consumeBeforeCredit);
		}
		// 交易后积分
		posBillMember.setConsume_after_credit(wlife.getCredit() == null ? null : wlife.getCredit().doubleValue());
		// 交易前主账户余额
		posBillMember.setConsume_before_main_balance(wlife.getBalance());
		if (WLifeConstant.PAY_SOURCE_BALANCE.equals(payInfo.getSource()))
		{
			posBillMember.setConsume_before_main_balance(wlife.getBalance() + payInfo.getAmount());
		}
		// 交易前赠送账户余额
		posBillMember.setConsume_before_reward_balance(0d);
		// 交易后主账户余额
		posBillMember.setConsume_after_main_balance(wlife.getBalance());
		// 交易后赠送账户余额
		posBillMember.setConsume_after_reward_balance(0d);
		wlife.setBalance(balance);
		return posBillMember;}

	private JsonConfig getJsonConfig()
	{
		JsonConfig config = new JsonConfig();
		config.setClassMap(getInsertOrderClassMap());
		config.setRootClass(MicroLifeOrder.class);
		config.setJavaPropertyFilter(new PropertyFilter()
		{
			public boolean apply(Object source, String name, Object value)
			{
				PropertyDescriptor propertyDescriptor = BeanUtils.getPropertyDescriptor(source.getClass(), name);
				return propertyDescriptor == null;
			}
		});
		return config;
	}

	private JsonConfig getOrderInfoJsonConfig()
	{
		JsonConfig config = new JsonConfig();
		config.setClassMap(getInsertOrderClassMap());
		config.setRootClass(MicroLifeOrderInfo.class);
		config.setJavaPropertyFilter(new PropertyFilter()
		{
			public boolean apply(Object source, String name, Object value)
			{
				PropertyDescriptor propertyDescriptor = BeanUtils.getPropertyDescriptor(source.getClass(), name);
				return propertyDescriptor == null;
			}
		});
		return config;
	}

	private JsonConfig getPayJsonConfig()
	{
		JsonConfig config = new JsonConfig();
		config.setClassMap(getPayMessageClassMap());
		config.setRootClass(MicroLifePayOrder.class);
		config.setJavaPropertyFilter(new PropertyFilter()
		{
			public boolean apply(Object source, String name, Object value)
			{
				PropertyDescriptor propertyDescriptor = BeanUtils.getPropertyDescriptor(source.getClass(), name);
				return propertyDescriptor == null;
			}
		});
		return config;
	}

	private Map<String, Class<?>> getPayMessageClassMap()
	{
		Map<String, Class<?>> classMap = new HashMap<String, Class<?>>();
		classMap.put("pay_info", AcewillPayInfo.class);
		classMap.put("wlife", AcewillPayWLife.class);
		classMap.put("receive_coupons", MicroLifeCoupons.class);
		classMap.put("data", MicroLifePayOrderData.class);
		return classMap;
	}

	private Map<String, Class<?>> getInsertOrderClassMap()
	{
		Map<String, Class<?>> classMap = new HashMap<String, Class<?>>();
		classMap.put("order_info", AcewillOrderInfo.class);
		classMap.put("ordermemo", AcewillOrderMemo.class);
		classMap.put("member", AcewillOrderMember.class);
		classMap.put("upgrade", MicroLifeGrade.class);
		classMap.put("setmeal", AcewillOrderSetmeal.class);
		classMap.put("normalitems", AcewillOrderNormalitem.class);
		classMap.put("maindish", AcewillOrderSetmealItem.class);
		classMap.put("mandatory", AcewillOrderSetmealItem.class);
		classMap.put("optional", AcewillOrderSetmealItem.class);
		return classMap;
	}

	private AcewillPosBillPaymentCoupons createPosBillPaymentCoupons(MicroLifePayOrder microLifePayOrder, AcewillPosBill posBill, AcewillPayInfo payInfo)
	{
		AcewillPosBillPaymentCoupons posBillPaymentCoupons = new AcewillPosBillPaymentCoupons();

        // 商户id
        posBillPaymentCoupons.setTenancy_id(microLifePayOrder.getBusiness_id());
        // 机构id
        posBillPaymentCoupons.setStore_id(microLifePayOrder.getShop_id());
        // ID-自增
        posBillPaymentCoupons.setId(null);
        // 报表日期
        posBillPaymentCoupons.setBill_num(posBill.getBill_num());
        // 账单编号
        posBillPaymentCoupons.setReport_date(com.tzx.pos.base.util.DateUtil.parseDate(posBill.getReport_date()));
        // 付款方式ID
//        posBillPaymentCoupons.setPayment_id(posBill.getPayment_id());
        // 优惠劵号
        posBillPaymentCoupons.setCoupons_code(payInfo.getSerilNo());
        // 面值
        posBillPaymentCoupons.setDeal_value(payInfo.getAmount());
        // 劵类型名称
        posBillPaymentCoupons.setDeal_name(posBillCouponTypeNameMapping.get(payInfo.getSource()));
        // 操作时间
        posBillPaymentCoupons.setLast_updatetime(new Date());
        // 备注
        posBillPaymentCoupons.setRemark(null);
        // 上传标记
        posBillPaymentCoupons.setUpload_tag("0");
        // 是否撤销
        posBillPaymentCoupons.setIs_cancel("0");
        // 优惠券大类ID
        posBillPaymentCoupons.setClass_id(null);
        // 优惠券类型ID
        posBillPaymentCoupons.setType_id(null);
        // 优惠金额
        posBillPaymentCoupons.setDiscount_money(payInfo.getAmount());
        // 抵用数量
        posBillPaymentCoupons.setDiscount_num(null);
        // 渠道
        posBillPaymentCoupons.setChanel(posBill.getSource());
        // 菜品单价
        posBillPaymentCoupons.setPrice(null);
        // 抵扣菜品
        posBillPaymentCoupons.setItem_id(null);
        // 菜品数量
        posBillPaymentCoupons.setItem_num(null);
        // 优惠劵编码
        posBillPaymentCoupons.setCoupons_pro(payInfo.getSerilNo());
        // 优惠劵类型
        posBillPaymentCoupons.setCoupon_type(Integer.parseInt(posBillCouponTypeMapping.get(payInfo.getSource())));
        posBillPaymentCoupons.setCoupon_buy_price(null);
        posBillPaymentCoupons.setDue(null);
        posBillPaymentCoupons.setTenancy_assume(null);
        posBillPaymentCoupons.setThird_assume(null);
        posBillPaymentCoupons.setThird_fee(null);
        posBillPaymentCoupons.setRequest_state(2);

        return posBillPaymentCoupons;
	}

	private CrmCardTradingListEntity createCrmCardTradingList(MicroLifePayOrder microLifePayOrder, AcewillPosBill posBill)
	{
		CrmCardTradingListEntity crmCardTradingList = new CrmCardTradingListEntity();
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        AcewillPayWLife wlife = microLifePayOrder.getWlife();
        // 商户id
        crmCardTradingList.setTenancy_id(microLifePayOrder.getBusiness_id());
        // Id
        crmCardTradingList.setId(null);
        // 卡id
        crmCardTradingList.setCard_id(null);
        // 卡号 
        crmCardTradingList.setCard_code(wlife.getCno());
        // 交易单号
        crmCardTradingList.setBill_code(posBill.getBill_num());
        // 交易渠道
        crmCardTradingList.setChanel(posBill.getSource());
        // 交易门店ID
        crmCardTradingList.setStore_id(microLifePayOrder.getShop_id());
        // 交易日期
        crmCardTradingList.setBusiness_date(new Date());
        // 主账户交易金额
        crmCardTradingList.setMain_trading(microLifePayOrder.getData().getAmount());
        // 赠送账户交易金额
        crmCardTradingList.setReward_trading(null);
        // 交易类型
        crmCardTradingList.setOperat_type(SysDictionary.OPERAT_TYPE_XF);

        // 原主账户金额
        crmCardTradingList.setMain_original(
                DoubleHelper.add(wlife.getBalance(), microLifePayOrder.getData().getAmount(), DEFAULT_SCALE));
        // 原赠送账户金额
        crmCardTradingList.setReward_original(0d);
        // 押金金额
        crmCardTradingList.setDeposit(0d);
        // 操作员
        crmCardTradingList.setOperator(null);
        // 操作时间
        crmCardTradingList.setOperate_time(timestamp);
        // 账单金额
        crmCardTradingList.setBill_money(posBill.getBill_amount());
        // 第三方账单号
        crmCardTradingList.setThird_bill_code(null);
        // 原账单号
        crmCardTradingList.setBill_code_original(null);
        // 活动ID
        crmCardTradingList.setActivity_id(null);
        // 会员ID
        crmCardTradingList.setCustomer_id(null);
        // 已撤销金额
        crmCardTradingList.setRevoked_trading(0d);
        //
        crmCardTradingList.setBatch_num(null);
        // 最后修改时间
        crmCardTradingList.setLast_updatetime(timestamp);
        // 门店修改时间
        crmCardTradingList.setStore_updatetime(timestamp);
        // 卡类ID
        crmCardTradingList.setCard_class_id(null);
        // 会员name
        crmCardTradingList.setName(wlife.getName());
        // 会员电话
        crmCardTradingList.setMobil(null);
        // 操作员ID
        crmCardTradingList.setOperator_id(null);
        // 班次ID
        crmCardTradingList.setShift_id(posBill.getShift_id());
        // 卡余额
        crmCardTradingList.setTotal_balance(0d);
        // 卡赠送账户余额
        crmCardTradingList.setReward_balance(0d);
        // 卡主账户余额
        crmCardTradingList.setMain_balance(wlife.getBalance());
        // 付款方式
        crmCardTradingList.setPay_type(null);

        // 销售人员ID
        crmCardTradingList.setSalesman(null);
        // 人员提成金额
        crmCardTradingList.setCommission_saler_money(0d);
        // 机构提成金额
        crmCardTradingList.setCommission_store_money(0d);
        // 可开票金额
        crmCardTradingList.setInvoice_balance(0d);

        crmCardTradingList.setIs_invoice("0");
        crmCardTradingList.setPayment_state("1");
        crmCardTradingList.setRecharge_state("1");
        crmCardTradingList.setRequest_status("02");
        crmCardTradingList.setRequest_code(null);
        crmCardTradingList.setRequest_msg(null);

        return crmCardTradingList;
	}

	private Data createOrderDishData(MicroLifeOrder microLifeOrder, AcewillOrderAssist orderAssist) throws Exception
	{
		Data data = new Data();
        data.setTenancy_id(microLifeOrder.getBusiness_id());
        data.setStore_id(microLifeOrder.getShop_id());
        data.setType(Type.ORDERING);
        data.setOper(Oper.add);
        data.setSource("cc_order");
        List<JSONObject> orderList = new ArrayList<>();
        AcewillOrder order = createOrder(microLifeOrder, orderAssist);
        JSONObject orderJsonObject = JSONObject.fromObject(order, getAcewillOrderJsonConfig());
        orderList.add(orderJsonObject);
        data.setData(orderList);
        return data;
	}

	private JsonConfig getAcewillOrderJsonConfig()
	{
		Map<String, Class<?>> classMap = new HashMap<>();
		classMap.put("item", AcewillOrderItem.class);
		classMap.put("method", AcewillOrderItemMethod.class);
		JsonConfig jsonConfig = new JsonConfig();
		jsonConfig.setClassMap(classMap);
		jsonConfig.setRootClass(AcewillOrder.class);
		return jsonConfig;
	}

	private AcewillOrder createOrder(MicroLifeOrder microLifeOrder, AcewillOrderAssist orderAssist) throws Exception
	{
		MicroLifeOrderInfo microLifeOrderInfo = microLifeOrder.getOrder_info();

        AcewillOrder acewillOrder = new AcewillOrder();
        // 账单号
        acewillOrder.setBill_num(orderAssist.getBill_num());
        // 整单备注
        //String billTaste = insertOrderDao.getBillTaste(insertOrder);
        AcewillOrderMemo ordermemo = microLifeOrderInfo.getOrdermemo();

//        acewillOrder.setBill_taste(ordermemo.getText());
		String channel = microLifeOrderInfo.getChannel();

        // 是否厨打
        acewillOrder.setIsprint("Y");
        // 0:下单 1:
        acewillOrder.setMode(0);
        acewillOrder.setChanel(ChannelMapping.getChannelMapping(channel).getChannlCode());
        // 操作员编号
        acewillOrder.setOpt_num(orderAssist.getOpt_num());
        // 收款机编号
        acewillOrder.setPos_num(orderAssist.getPos_num());
        // 备注
        if(StringUtil.hasText(ordermemo.getText())){
            acewillOrder.setRemark(ordermemo.getText());
        }

        // 报表日期
        acewillOrder.setReport_date(orderAssist.getReport_date());
        // 销售模式
        // acewillOrder.setSale_mode(sale_mode);
        // 班次id
        acewillOrder.setShift_id(orderAssist.getShift_id());
        //桌号
        acewillOrder.setTable_code(microLifeOrderInfo.getTableno());
        // 服务员号
        acewillOrder.setWaiter_num(null);
        List<AcewillOrderItem> orderItems = new ArrayList<>();
        acewillOrder.setItem(orderItems);
        // 点餐序号
        List<AcewillOrderSetmeal> setmealList = microLifeOrderInfo.getSetmeal();
        Integer item_serial = insertOrderDao.getLastItemSerial(microLifeOrder.getBusiness_id(), orderAssist.getBill_num());
        Map<String, JSONObject> itemComboDetailMap = itemComboDetailMap(microLifeOrder);
        //套餐规格id不需要设置
        Map<String, String> setmealUnitMap = getSetmealUnitMap(microLifeOrder);

        //菜品规格
        if(setmealList!=null) {
            for (AcewillOrderSetmeal setmeal : setmealList) {
                String setmealUnitId = setmealUnitMap.get(setmeal.getDid());
                if(!org.apache.commons.lang.StringUtils.isEmpty(setmealUnitId)) {
                    setmeal.setDuid(setmealUnitId);
                }
            }
        }
        Map<String, String> unitNameMap = firstPaymentDao.getUnitNameMap(microLifeOrder);
        if(setmealList!=null) {
            for (AcewillOrderSetmeal setmeal : setmealList) {
                item_serial++;
                String type = setmeal.getType();
                if(SET_MEAL_TYPE.equals(type)){
                    List<AcewillOrderSetmealItem> setmealItemList = new ArrayList<>();
                    if(null !=setmeal.getMaindish()){
                        setmealItemList.addAll(setmeal.getMaindish());
                    }
                    if(null !=setmeal.getMandatory()){
                        setmealItemList.addAll(setmeal.getMandatory());
                    }
                    if(null !=setmeal.getOptional()){
                        setmealItemList.addAll(setmeal.getOptional());
                    }

                    for (AcewillOrderSetmealItem item : setmealItemList) {
                        item.setNumber(setmeal.getNumber()*item.getNumber());
                        String duName = unitNameMap.get(item.getDuid());
                        JSONObject itemComboDetail = itemComboDetailMap.get(item.getId());
                        item.setMallListName();//设置套餐明细菜品名称
                        item.setDuName(duName);
                        AcewillOrderItem setmealOrderItem = createOrderItem(microLifeOrderInfo, setmeal, item, item_serial,itemComboDetail);
                        orderItems.add(setmealOrderItem);
                    }
                    JSONObject itemComboDetail = itemComboDetailMap.get(setmeal.getDid());
                    String duName = unitNameMap.get(setmeal.getDuid());
                    setmeal.setDuName(duName);
                    orderItems.add(createOrderItem(microLifeOrderInfo, setmeal, item_serial, itemComboDetail));
                }

            }
        }
        List<AcewillOrderNormalitem> normalitemList = microLifeOrderInfo.getNormalitems();
        if(normalitemList!=null) {
            for (AcewillOrderNormalitem item : normalitemList) {
                item_serial++;
                String duName = unitNameMap.get(item.getDuid());
                item.setDuName(duName);
                JSONObject itemComboDetail = itemComboDetailMap.get(item.getDid());
                AcewillOrderItem setmealOrderItem = createOrderItem(item, item_serial, itemComboDetail);
                orderItems.add(setmealOrderItem);
            }
        }

        return acewillOrder;
	}

	private Map<String, String> getSetmealUnitMap(MicroLifeOrder microLifeOrder) throws Exception
	{
		String tenentId = microLifeOrder.getBusiness_id();
		MicroLifeOrderInfo microLifeOrderInfo = microLifeOrder.getOrder_info();
		List<String> itemIdList = new ArrayList<String>();
		List<AcewillOrderSetmeal> setmealList = microLifeOrderInfo.getSetmeal();
		if (setmealList != null)
		{
			for (AcewillOrderSetmeal setmeal : setmealList)
			{
				itemIdList.add(setmeal.getDid());
			}
		}
		Map<String, String> setmealUnitMap = new HashMap<String, String>();
		List<JSONObject> findItemUnit = this.insertOrderDao.findItemUnit(tenentId, itemIdList);
		if (findItemUnit != null)
		{
			for (JSONObject jsonObject : findItemUnit)
			{
				setmealUnitMap.put(jsonObject.getString("item_id"), jsonObject.getString("id"));
			}
		}
		return setmealUnitMap;
	}

	private Map<String, JSONObject> itemComboDetailMap(MicroLifeOrder microLifeOrder) throws Exception
	{
		String TenentId = microLifeOrder.getBusiness_id();
		MicroLifeOrderInfo microLifeOrderInfo = microLifeOrder.getOrder_info();
		List<Integer> itemIdList = new ArrayList<Integer>();
		List<AcewillOrderSetmeal> setmealList = microLifeOrderInfo.getSetmeal();
		if (setmealList != null)
		{
			for (AcewillOrderSetmeal setmeal : setmealList)
			{
				List<AcewillOrderSetmealItem> setmealItemList = new ArrayList<AcewillOrderSetmealItem>();
				if (null != setmeal.getMaindish())
				{
					setmealItemList.addAll(setmeal.getMaindish());
				}
				if (null != setmeal.getMandatory())
				{
					setmealItemList.addAll(setmeal.getMandatory());
				}
				if (null != setmeal.getOptional())
				{
					setmealItemList.addAll(setmeal.getOptional());
				}

				for (AcewillOrderSetmealItem item : setmealItemList)
				{
					itemIdList.add(Integer.valueOf(Integer.parseInt(item.getId())));
				}
				itemIdList.add(Integer.valueOf(Integer.parseInt(setmeal.getDid())));
			}
		}
		List<AcewillOrderNormalitem> normalitemList = microLifeOrderInfo.getNormalitems();
		if (normalitemList != null)
		{
			for (AcewillOrderNormalitem item : normalitemList)
			{
				itemIdList.add(Integer.valueOf(Integer.parseInt(item.getDid())));
			}
		}
		List<JSONObject> itemComboDetails = this.insertOrderDao.getItemComboDetails(TenentId, itemIdList);
		Map<String,JSONObject> itemComboDetailMap = new HashMap<String,JSONObject>();
		if (itemComboDetails != null)
		{
			for (JSONObject jsonObject : itemComboDetails)
			{
				itemComboDetailMap.put(jsonObject.getString("iitem_id"), jsonObject);
			}
		}
		return itemComboDetailMap;
	}

	private AcewillOrderItem createOrderItem(AcewillOrderNormalitem item, Integer item_serial, JSONObject itemComboDetail)
	{
		 AcewillOrderItem orderItem = new AcewillOrderItem();
	        // 辅助Assist
	        if(itemComboDetail!=null) {
	            orderItem.setAssist_item_id(itemComboDetail.getString("id"));
	            orderItem.setAssist_num(itemComboDetail.getString("combo_num"));
	        }
	        // orderItem.setAssist_money(assist_money);
	        // 餐谱明细id orderItem.setDetails_id(details_id);

	        orderItem.setItem_count(item.getNumber());

	        orderItem.setItem_id(item.getDid());
	        orderItem.setItem_name(item.getName());
	        orderItem.setItem_num(item.getDishsno());
	        orderItem.setItem_price(item.getPrice());
	        orderItem.setItem_property(SysDictionary.ITEM_PROPERTY_SINGLE);
	        orderItem.setItem_remark(null);
	        // 点菜序号
	        orderItem.setItem_serial(item_serial);
	        orderItem.setItem_taste(item.getRemark());
	        // 规格名称
	        orderItem.setItem_unit_name(item.getDuName());

	        // orderItem.setSale_mode(sale_mode);

	        orderItem.setMethod(createItemMethod(item.getCooks()));
	        // 座位号
	        orderItem.setSeat_num(null);
	        orderItem.setSetmeal_id(null);
	        // 套菜点菜号
	        orderItem.setSetmeal_rwid(null);
	        // 规格id
	        orderItem.setUnit_id(item.getDuid());
	        // 等叫标记
	        // orderItem.setWaitcall_tag(waitcall_tag);
	        return orderItem;
	}

	private AcewillOrderItem createOrderItem(MicroLifeOrderInfo orderInfo, AcewillOrderSetmeal setmeal, Integer item_serial, JSONObject itemComboDetail)
	{
		AcewillOrderItem orderItem = new AcewillOrderItem();
        // 辅助Assist
        if(itemComboDetail!=null) {
            orderItem.setAssist_item_id(itemComboDetail.getString("id"));
            orderItem.setAssist_num(itemComboDetail.getString("combo_num"));
        }else {
            orderItem.setAssist_item_id("0");
            orderItem.setAssist_num("0");
        }
        // orderItem.setAssist_money(assist_money);
        // 餐谱明细id orderItem.setDetails_id(details_id);
        orderItem.setItem_count(setmeal.getNumber());

        orderItem.setItem_id(setmeal.getDid());
        orderItem.setItem_name(setmeal.getName());
        orderItem.setItem_num(setmeal.getDishsno());
        orderItem.setItem_price(setmeal.getPrice());
        orderItem.setItem_property(SysDictionary.ITEM_PROPERTY_SETMEAL);

        //orderItem.setItem_remark(setmeal.getRemark());
        // 点菜序号
        orderItem.setItem_serial(item_serial);
        orderItem.setItem_taste(setmeal.getRemark());

        // 规格名称
        orderItem.setItem_unit_name(setmeal.getDuName());
        // orderItem.setSale_mode(sale_mode);
        orderItem.setMethod(createItemMethod(setmeal.getCooks()));
        // 座位号
        orderItem.setSeat_num(orderInfo.getTableno());
        orderItem.setSetmeal_id(setmeal.getDid());
        // 套菜点菜号
        orderItem.setSetmeal_rwid(item_serial);
        // 规格id
        orderItem.setUnit_id(setmeal.getDuid());
        // 等叫标记
        // orderItem.setWaitcall_tag(waitcall_tag);
        return orderItem;
	}

	private AcewillOrderItem createOrderItem(MicroLifeOrderInfo orderInfo, AcewillOrderSetmeal setmeal, AcewillOrderSetmealItem item, Integer item_serial, JSONObject itemComboDetail)
	{
		AcewillOrderItem orderItem = new AcewillOrderItem();
        // 辅助Assist
        if(itemComboDetail!=null) {
            orderItem.setAssist_item_id(itemComboDetail.getString("id"));
            orderItem.setAssist_num(itemComboDetail.getString("combo_num"));
        }
        // orderItem.setAssist_money(assist_money);
        // 餐谱明细id orderItem.setDetails_id(details_id);

        orderItem.setItem_count(item.getNumber());

        orderItem.setItem_id(item.getId());
        orderItem.setItem_name(item.getName());
        orderItem.setItem_num(item.getDishsno());
        //orderItem.setItem_price(item.getAprice());
        orderItem.setItem_property(SysDictionary.ITEM_PROPERTY_MEALLIST);
        //口味备注
        orderItem.setItem_taste(setmeal.getRemark());
        // 点菜序号
        orderItem.setItem_serial(item_serial);

        // orderItem.setItem_taste(null);
        // 规格名称
        orderItem.setItem_unit_name(item.getDuName());

        // 做法 orderItem.setMethod(method);

        // orderItem.setSale_mode(sale_mode);
        orderItem.setMethod(createItemMethod(setmeal.getCooks()));
        // 座位号
        orderItem.setSeat_num(orderInfo.getTableno());
        //套餐id
        orderItem.setSetmeal_id(setmeal.getDid());
        // 套菜点菜号
        orderItem.setSetmeal_rwid(item_serial);
        // 规格id
        orderItem.setUnit_id(item.getDuid());
        // 等叫标记
        // orderItem.setWaitcall_tag(waitcall_tag);
        return orderItem;
	}

	private List<AcewillOrderItemMethod> createItemMethod(Object cooks)
	{
		List<AcewillOrderItemMethod> methods = new ArrayList<AcewillOrderItemMethod>();
		if (null == cooks)
		{
			return methods;
		}
		JSONArray cookArray = JSONArray.fromObject(cooks);
		if (cookArray != null) for (int i = 0; i < cookArray.size(); i++)
		{
			JSONObject jsonObject = cookArray.getJSONObject(i);
			AcewillOrderItemMethod method = new AcewillOrderItemMethod();
			String id = jsonObject.optString("id");
			if ((!StringUtils.isBlank(id)) && (!"null".equals(id)))
			{
				method.setMethod_id(jsonObject.getString("id"));
				method.setMethod_name(jsonObject.getString("cook"));
				methods.add(method);
			}
		}
		return methods;
	}

	private Data createOpenTableData(MicroLifeOrder microLifeOrder, AcewillOrderAssist orderAssist) throws Exception
	{
		MicroLifeOrderInfo orderInfo = microLifeOrder.getOrder_info();
		Data data = new Data();
		data.setTenancy_id(microLifeOrder.getBusiness_id());
		data.setStore_id(microLifeOrder.getShop_id().intValue());
		data.setType(Type.ORDERING);
		data.setOper(Oper.add);
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject();
		if (StringUtil.hasText(orderInfo.getTableno()))
		{
			object.put("table_code", orderInfo.getTableno());
			object.put("mode", Integer.valueOf(0));
		}
		else
		{
			object.put("mode", Integer.valueOf(1));
			object.put("table_code", "99");
		}
		String channel = orderInfo.getChannel();
		object.put("shift_id", orderAssist.getShift_id());
		object.put("report_date", orderAssist.getReport_date());
		object.put("table_code", orderInfo.getTableno());
		object.put("pos_num", orderAssist.getPos_num());
		object.put("opt_num", orderAssist.getOpt_num());
		object.put("waiter_num", orderAssist.getOpt_num());
		object.put("item_menu_id", Integer.valueOf(0));
		if (ChannelMapping.PF.getRetailCode().equals(channel))
		{
			object.put("sale_mode", SysDictionary.SALE_MODE_TS01);
		}
		else
		{
			object.put("sale_mode", SysDictionary.SALE_MODE_WS03);
		}

		object.put("chanel", ChannelMapping.getChannelMapping(channel).getChannlCode());
		object.put("guest", orderInfo.getPeople());
		object.put("preorderno", microLifeOrder.getOut_order_id());
		object.put("copy_bill_num", "");
		object.put("remark", "");
		object.put("shop_real_amount", Integer.valueOf(0));
		object.put("platform_charge_amount", Integer.valueOf(0));
		object.put("settlement_type", "");
		object.put("discount_mode_id", Integer.valueOf(0));
		object.put("discountk_amount", Integer.valueOf(0));
		object.put("discount_rate", Integer.valueOf(0));
		list.add(object);
		data.setData(list);
		return data;
	}

	private AcewillOrderAssist createOrderAssist(MicroLifeOrder microLifeOrder) throws Exception
	{
		AcewillOrderAssist orderAssist = new AcewillOrderAssist();
		String tenantId = microLifeOrder.getBusiness_id();
		Integer storeId = microLifeOrder.getShop_id();
		Date reportDate = this.insertOrderDao.getReportDate(tenantId, storeId);
		orderAssist.setReport_date(com.tzx.framework.common.util.DateUtil.format(reportDate));
		List<JSONObject> optStateInfoList = this.posBaseService.getOptStateInfo(tenantId, storeId.intValue(), orderAssist.getReport_date());

		if ((optStateInfoList == null) || (optStateInfoList.size() == 0))
		{
			throw new OtherSystemException(Constant.CODE_NULL_DATASET, Constant.CHECK_POS_LOGIN_FAILURE, null);
		}
		JSONObject optStateInfo = (JSONObject) optStateInfoList.get(0);
		orderAssist.setPos_num(optStateInfo.getString("pos_num"));
		orderAssist.setOpt_num(optStateInfo.getString("opt_num"));
		int shiftId = this.insertOrderDao.getShiftId(tenantId, storeId, reportDate, orderAssist.getOpt_num(), orderAssist.getPos_num());
		orderAssist.setShift_id(Integer.valueOf(shiftId));
		return orderAssist;
	}

	private AcewillPosBillMember savePosBillMember(MicroLifeOrder microLifeOrder, AcewillOrderAssist orderAssist) throws Exception
	{
		MicroLifeOrderInfo orderInfo = microLifeOrder.getOrder_info();
		AcewillOrderMember member = orderInfo.getMember();
		AcewillPosBillMember posBillMember = new AcewillPosBillMember();
		posBillMember.setTenancy_id(microLifeOrder.getBusiness_id());
		posBillMember.setStore_id(microLifeOrder.getShop_id());
		posBillMember.setBill_num(orderAssist.getBill_num());
		posBillMember.setBill_code(microLifeOrder.getOut_order_id());
		posBillMember.setReport_date(com.tzx.pos.base.util.DateUtil.parseDate(orderAssist.getReport_date()));
		posBillMember.setType(SysDictionary.BILL_MEMBERCARD_JFZS06);
		posBillMember.setAmount(null);

		posBillMember.setCredit(Double.valueOf(0.0D));
		posBillMember.setCard_code(member.getCno());
		posBillMember.setMobil(member.getMobile());
		posBillMember.setLast_updatetime(new Date());
		posBillMember.setUpload_tag(Integer.valueOf(0));
		posBillMember.setRemark(null);
		posBillMember.setBill_code(null);
		posBillMember.setRequest_state(null);
		posBillMember.setCustomer_code(member.getCno());
		posBillMember.setCustomer_name(member.getName());
		posBillMember.setConsume_before_credit(member.getCredit());
		posBillMember.setConsume_after_credit(member.getCredit());
		posBillMember.setConsume_before_main_balance(member.getBalance());
		 //交易前赠送账户余额
		posBillMember.setConsume_before_reward_balance(Double.valueOf(0.0D));
		 //交易后主账户余额
		posBillMember.setConsume_after_main_balance(member.getBalance());
		//交易后赠送账户余额
		posBillMember.setConsume_after_reward_balance(Double.valueOf(0.0D));
		this.insertOrderDao.savePosBillMember(posBillMember);
		return posBillMember;
	}

	private String openTable(MicroLifeOrder microLifeOrder, boolean isOrdering) throws Exception
	{
		AcewillOrderAssist orderAssist = createOrderAssist(microLifeOrder);

		Data param = createOpenTableData(microLifeOrder, orderAssist);

		String tenantId = param.getTenancy_id();
		Integer storeId = Integer.valueOf(param.getStore_id());
		String source = param.getSource();

		List<?> paramList = param.getData();

		if ((null == paramList) || (paramList.isEmpty()))
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}
		JSONObject paramJson = JSONObject.fromObject(paramList.get(0));

		Integer shiftId = ParamUtil.getIntegerValueByObject(paramJson, "shift_id", true, PosErrorCode.NOT_NULL_SHIFT_ID);
		Date reportDate = ParamUtil.getDateValueByObject(paramJson, "report_date", true, PosErrorCode.NOT_NULL_REPORT_DATE);
		String optNum = ParamUtil.getStringValueByObject(paramJson, "opt_num", true, PosErrorCode.NOT_NULL_OPT_NUM);
		String posNum = ParamUtil.getStringValueByObject(paramJson, "pos_num", true, PosErrorCode.NOT_EXISTS_POSNUM);
		String tableCode = ParamUtil.getStringValueByObject(paramJson, "table_code", false, null);
		String chanel = ParamUtil.getStringValueByObject(paramJson, "chanel", false, null);

		if (Tools.isNullOrEmpty(chanel))
		{
			chanel = SysDictionary.CHANEL_MD01;
		}

		// 报表日期校验
		this.openTableService.checkReportDate(tenantId, storeId, reportDate);

		 // 班次ID校验
		shiftId = this.openTableService.checkShiftId(tenantId, storeId, reportDate, shiftId, optNum, posNum, source);
		
		String waiterNum = ParamUtil.getStringValueByObject(paramJson, "waiter_num", false, null);
		Integer guest = ParamUtil.getIntegerValueByObject(paramJson, "guest", false, null);
		Integer itemMenuId = ParamUtil.getIntegerValueByObject(paramJson, "item_menu_id", false, null);
		String preorderno = ParamUtil.getStringValueByObject(paramJson, "preorderno", false, null);
		String copyBillNum = ParamUtil.getStringValueByObject(paramJson, "copy_bill_num", false, null);
		String remark = ParamUtil.getStringValueByObject(paramJson, "remark", false, null);
		Double shopRealAmount = ParamUtil.getDoubleValueByObject(paramJson, "shop_real_amount", false, null);
		Double platformChargeAmount = ParamUtil.getDoubleValueByObject(paramJson, "platform_charge_amount", false, null);
		String settlementType = ParamUtil.getStringValueByObject(paramJson, "settlement_type", false, null);

		if (!StringUtil.hasText(remark))
		{
			remark = "";
		}

		int discountModeId = 0;
		if (paramJson.containsKey("discount_mode_id"))
		{
			discountModeId = ParamUtil.getIntegerValueByObject(paramJson, "discount_mode_id", false, null).intValue();
		}

		double discountkAmount = 0.0D;
		if (paramJson.containsKey("discountk_amount"))
		{
			discountkAmount = ParamUtil.getDoubleValueByObject(paramJson, "discountk_amount", false, null).doubleValue();
		}

		double discountRate = 100.0D;
		if (paramJson.containsKey("discount_rate"))
		{
			discountRate = ParamUtil.getDoubleValueByObject(paramJson, "discount_rate", false, null).doubleValue();
		}

		String saleMode = ParamUtil.getStringValueByObject(paramJson, "sale_mode", false, null);
		if (Tools.isNullOrEmpty(saleMode))
		{
			saleMode = SysDictionary.SALE_MODE_TS01;
		}

		if (Tools.isNullOrEmpty(chanel))
		{
			chanel = SysDictionary.CHANEL_MD01;
		}

		if (Tools.isNullOrEmpty(itemMenuId))
		{
			itemMenuId = Integer.valueOf(0);
		}

		if (Tools.isNullOrEmpty(waiterNum))
		{
			waiterNum = optNum;
		}

		double serviceAmount = 0.0D;
		if (paramJson.containsKey("service_amount"))
		{
			serviceAmount = ParamUtil.getDoubleValueByObject(paramJson, "service_amount", false, null).doubleValue();
		}

		Integer serviceId = Integer.valueOf(0);
		if (paramJson.containsKey("service_id"))
		{
			serviceId = ParamUtil.getIntegerValueByObject(paramJson, "service_id", false, null);
		}
		// POS账单编号
		String billNum = "";
		// POS流水单号
		String serialNum = "";

		Timestamp time = com.tzx.pos.base.util.DateUtil.currentTimestamp();
		String bill_property = SysDictionary.BILL_PROPERTY_OPEN;
		try
		{
			JSONObject object = new JSONObject();
			object.put("store_id", storeId);
			object.put("busi_date", reportDate);
			billNum = this.codeService.getCode(tenantId, Code.POS_BILL_CODE, object);
			object.put("pos_num", posNum);
			serialNum = this.codeService.getCode(tenantId, Code.POS_RECORD_CODE, object);
		}
		catch (Exception e)
		{
			LOG.info("开台：" + ExceptionMessage.getExceptionMessage(e));
			throw new SystemException(PosErrorCode.GET_BILLNUM_SERVICE_ERROR);
		}
		if (isOrdering)
		{
			this.firstPaymentDao.savePosBillBooked(tenantId, storeId.intValue(), reportDate, shiftId.intValue(), billNum, null, serialNum, preorderno, time, posNum, optNum, waiterNum, guest.intValue(), itemMenuId.intValue(), saleMode, chanel, serviceId.intValue(), serviceAmount, copyBillNum, remark,
					bill_property, SysDictionary.PAYMENT_STATE_NOTPAY, discountModeId, discountkAmount, shopRealAmount.doubleValue(), platformChargeAmount.doubleValue(), settlementType, discountRate, tableCode);
		}
		else
		{
			this.firstPaymentDao.savePosBill(tenantId, storeId.intValue(), reportDate, shiftId.intValue(), billNum, null, serialNum, preorderno, time, posNum, optNum, waiterNum, guest.intValue(), itemMenuId.intValue(), saleMode, chanel, serviceId.intValue(), serviceAmount, copyBillNum, remark,
					bill_property, SysDictionary.PAYMENT_STATE_NOTPAY, discountModeId, discountkAmount, shopRealAmount.doubleValue(), platformChargeAmount.doubleValue(), settlementType, discountRate, tableCode);
		}

		String str0 = new String("insert into pos_bill_waiter (tenancy_id,store_id,bill_num,waiter_num,report_date) values (?,?,?,?,?)");
		this.firstPaymentDao.update(str0, new Object[]
		{ tenantId, storeId, billNum, waiterNum, reportDate });

		this.firstPaymentDao.savePosLog(tenantId, storeId, posNum, optNum, optNum, shiftId, reportDate, Constant.TITLE, "开台", "桌位编号:" + tableCode, "账单编号:" + billNum);
		return billNum;
	}

	static
	{
		for (String key : keys)
		{
			map.put(key.toLowerCase(), key);
		}
		
        paymentWayMapping.put(WLifeConstant.PAY_SOURCE_ALIPAY, SysDictionary.PAYMENT_CLASS_ALI_PAY);
        paymentWayMapping.put(WLifeConstant.PAY_SOURCE_WEIXIN, SysDictionary.PAYMENT_CLASS_WECHAT_PAY);
        paymentWayMapping.put(WLifeConstant.PAY_SOURCE_BALANCE, SysDictionary.PAYMENT_CLASS_WLIFE_BALANCE);
        paymentWayMapping.put(WLifeConstant.PAY_SOURCE_CREDIT, SysDictionary.PAYMENT_CLASS_WLIFE_CREDIT);
        paymentWayMapping.put(WLifeConstant.PAY_SOURCE_COUPON, SysDictionary.PAYMENT_CLASS_WLIFE_COUPON);
        paymentWayMapping.put(WLifeConstant.PAY_SOURCE_PRODUCT, SysDictionary.PAYMENT_CLASS_WLIFE_COUPON);

        posBillMemberTyoeMapping.put(WLifeConstant.PAY_SOURCE_ALIPAY, SysDictionary.BILL_MEMBERCARD_HYZK02);
        posBillMemberTyoeMapping.put(WLifeConstant.PAY_SOURCE_WEIXIN, SysDictionary.BILL_MEMBERCARD_HYZK02);
        posBillMemberTyoeMapping.put(WLifeConstant.PAY_SOURCE_BALANCE, SysDictionary.BILL_MEMBERCARD_CZXF03);
        posBillMemberTyoeMapping.put(WLifeConstant.PAY_SOURCE_CREDIT, SysDictionary.BILL_MEMBERCARD_JFDX05);
        posBillMemberTyoeMapping.put(WLifeConstant.PAY_SOURCE_COUPON, SysDictionary.BILL_MEMBERCARD_YHJ04);
        posBillMemberTyoeMapping.put(WLifeConstant.PAY_SOURCE_PRODUCT, SysDictionary.BILL_MEMBERCARD_YHJ04);
        posBillMemberTyoeMapping.put(WLifeConstant.PAY_SOURCE_WLIFE, SysDictionary.BILL_MEMBERCARD_JFZS06);

        posBillCouponTypeMapping.put(WLifeConstant.PAY_SOURCE_COUPON, SysDictionary.CUSTOMER_COUPON_TYPE_CASH);
        posBillCouponTypeMapping.put(WLifeConstant.PAY_SOURCE_PRODUCT, SysDictionary.CUSTOMER_COUPON_TYPE_DISH);
        posBillCouponTypeNameMapping.put(WLifeConstant.PAY_SOURCE_COUPON, "优惠券");
        posBillCouponTypeNameMapping.put(WLifeConstant.PAY_SOURCE_PRODUCT, "菜品劵");
    
	}
}