package com.tzx.pos.base.controller;

import java.util.concurrent.TimeUnit;
import org.apache.http.conn.HttpClientConnectionManager;
import org.apache.log4j.Logger;

/**
 * 使用管理器，管理HTTP连接池 无效链接定期清理功能。
 * 
 * <AUTHOR> email:q<PERSON><PERSON><PERSON><PERSON>@tzx.com.cn
 * @version 1.0  2018-12-14
 * @see     
 * @since   JDK7.0
 * @update  
 */
public class HttpClientConnectionMonitorThread extends Thread{
	
	private static final Logger	logger	= Logger.getLogger(HttpClientConnectionMonitorThread.class);

	private final HttpClientConnectionManager connManager;
    private volatile boolean shutdown = false;

    public HttpClientConnectionMonitorThread(HttpClientConnectionManager connManager) {
        super();
        this.setName("http-connection-monitor");
        this.setDaemon(true);
        this.connManager = connManager;
        this.start();
    }

	@Override
	public void run() {
		try {
            while (!shutdown) {
                synchronized (this) {
                    // 等待5秒
                    wait(5000);
                    // 关闭过期的链接
                    connManager.closeExpiredConnections();
                    // 选择关闭 空闲30秒的链接
                    connManager.closeIdleConnections(30, TimeUnit.SECONDS);
                }
            }
        } catch (InterruptedException ex) {
        	logger.error("HTTP连接池无效链接定期清理功能出现异常，异常信息："+ex.getMessage());
        }
	}

}
