package com.tzx.ios.bo;

import java.util.List;

import com.tzx.framework.common.entity.Data;
import com.tzx.pos.bo.PosBaseService;

import net.sf.json.JSONObject;

public interface PosSoldOutIOSService extends PosBaseService
{
	String	NAME	= "com.tzx.ios.bo.imp.PosSoldOutIOSServiceImp";
	
	/**
	 * 查询沽清
	 * 
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> findSoldOut(Data param) throws Exception;
}
