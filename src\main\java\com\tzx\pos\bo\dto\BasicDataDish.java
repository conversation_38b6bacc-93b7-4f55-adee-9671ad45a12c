package com.tzx.pos.bo.dto;

import java.io.Serializable;

/**
 * 
 * <AUTHOR> 
 * @日期：2015年5月21日-上午11:39:34
 */

public class BasicDataDish implements Serializable
{
	/**
	 * 
	 */
	private static final long	serialVersionUID	= 1L;
	
	private String item_menu_id;
	private String print_id;
	private String print_name;
	private String print_ip_com;
	private String details_id;
	private String item_id;
	private String item_no;
	private String item_name;
	private String to_offer;
	private String item_english;
	private String phonetic_code;
	private String five_code;
	private String price;
	private String unit_id;
	private String unit_name;
	private String starttime;
	private String endtime;
	private String valid_state;
	private String item_class_id;
	private String itemclass_code;
	private String item_barcode;
	private String is_discount;
	private String is_pushmoney;
	private String pushmoney_way;
	private String proportion;
	private String is_modifyquantity;
	private String is_modifyname;
	private String is_runningprice;
	private String is_staffmeal;
	private String is_throwaway;
	private String is_seafood;
	private String is_staplefood;
	private String is_combo;
	private String is_show;
	private String is_characteristic;
	private String is_recommendation;
	private String combo_type;
	private String nutrition;
	private String suitable_crowds;
	private String unsuitable_crowds;
	private String processing_technic;
	private String photo1;
	private String photo2;
	private String photo3;
	private String photo4;
	private String photo5;
	private String photo6;
	private String summary;
	private String spicy;
	private String third_code;
	private String item_description;
	
	private Integer menu_item_rank; // 菜品排序字段

    private String vip_price; // 会员价

	private Integer is_assemble_combo;//是否组合套餐

    private String is_assist_num;//是否可设置辅助数量

    public String getPrint_name() {
        return print_name;
    }

    public void setPrint_name(String print_name) {
        this.print_name = print_name;
    }

    public String getPrint_ip_com() {
        return print_ip_com;
    }

    public void setPrint_ip_com(String print_ip_com) {
        this.print_ip_com = print_ip_com;
    }

    public String getPrint_id() {
        return print_id;
    }

    public void setPrint_id(String print_id) {
        this.print_id = print_id;
    }

    public String getIs_assist_num() {
        return is_assist_num;
    }

    public void setIs_assist_num(String is_assist_num) {
        this.is_assist_num = is_assist_num;
    }

    public String getVip_price() {
        return vip_price;
    }

    public void setVip_price(String vip_price) {
        this.vip_price = vip_price;
    }

    public Integer getMenu_item_rank() {
		return menu_item_rank;
	}
	public void setMenu_item_rank(Integer menu_item_rank) {
		this.menu_item_rank = menu_item_rank;
	}
	public String getThird_code()
	{
		return third_code;
	}
	public void setThird_code(String third_code)
	{
		this.third_code = third_code;
	}
	public String getPrice()
	{
		return price;
	}
	public void setPrice(String price)
	{
		this.price = price;
	}
	public String getUnit_id()
	{
		return unit_id;
	}
	public void setUnit_id(String unit_id)
	{
		this.unit_id = unit_id;
	}
	public String getStarttime()
	{
		return starttime;
	}
	public void setStarttime(String starttime)
	{
		this.starttime = starttime;
	}
	public String getEndtime()
	{
		return endtime;
	}
	public void setEndtime(String endtime)
	{
		this.endtime = endtime;
	}
	public String getItem_class_id()
	{
		return item_class_id;
	}
	public void setItem_class_id(String item_class_id)
	{
		this.item_class_id = item_class_id;
	}
	public String getProportion()
	{
		return proportion;
	}
	public void setProportion(String proportion)
	{
		this.proportion = proportion;
	}
	public String getItem_menu_id()
	{
		return item_menu_id;
	}
	public void setItem_menu_id(String item_menu_id)
	{
		this.item_menu_id = item_menu_id;
	}
	public String getDetails_id()
	{
		return details_id;
	}
	public void setDetails_id(String details_id)
	{
		this.details_id = details_id;
	}
	public String getItem_id()
	{
		return item_id;
	}
	public void setItem_id(String item_id)
	{
		this.item_id = item_id;
	}
	public String getItem_no()
	{
		return item_no;
	}
	public void setItem_no(String item_no)
	{
		this.item_no = item_no;
	}
	public String getItem_name()
	{
		return item_name;
	}
	public void setItem_name(String item_name)
	{
		this.item_name = item_name;
	}
	public String getItem_english()
	{
		return item_english;
	}
	public void setItem_english(String item_english)
	{
		this.item_english = item_english;
	}
	public String getPhonetic_code()
	{
		return phonetic_code;
	}
	public void setPhonetic_code(String phonetic_code)
	{
		this.phonetic_code = phonetic_code;
	}
	public String getFive_code()
	{
		return five_code;
	}
	public void setFive_code(String five_code)
	{
		this.five_code = five_code;
	}
	
	public String getUnit_name()
	{
		return unit_name;
	}
	public void setUnit_name(String unit_name)
	{
		this.unit_name = unit_name;
	}
	public String getValid_state()
	{
		return valid_state;
	}
	public void setValid_state(String valid_state)
	{
		this.valid_state = valid_state;
	}
	public String getItemclass_code()
	{
		return itemclass_code;
	}
	public void setItemclass_code(String itemclass_code)
	{
		this.itemclass_code = itemclass_code;
	}
	public String getItem_barcode()
	{
		return item_barcode;
	}
	public void setItem_barcode(String item_barcode)
	{
		this.item_barcode = item_barcode;
	}
	public String getIs_discount()
	{
		return is_discount;
	}
	public void setIs_discount(String is_discount)
	{
		this.is_discount = is_discount;
	}
	public String getIs_pushmoney()
	{
		return is_pushmoney;
	}
	public void setIs_pushmoney(String is_pushmoney)
	{
		this.is_pushmoney = is_pushmoney;
	}
	public String getPushmoney_way()
	{
		return pushmoney_way;
	}
	public void setPushmoney_way(String pushmoney_way)
	{
		this.pushmoney_way = pushmoney_way;
	}
	public String getIs_modifyquantity()
	{
		return is_modifyquantity;
	}
	public void setIs_modifyquantity(String is_modifyquantity)
	{
		this.is_modifyquantity = is_modifyquantity;
	}
	public String getIs_modifyname()
	{
		return is_modifyname;
	}
	public void setIs_modifyname(String is_modifyname)
	{
		this.is_modifyname = is_modifyname;
	}
	public String getIs_runningprice()
	{
		return is_runningprice;
	}
	public void setIs_runningprice(String is_runningprice)
	{
		this.is_runningprice = is_runningprice;
	}
	public String getIs_staffmeal()
	{
		return is_staffmeal;
	}
	public void setIs_staffmeal(String is_staffmeal)
	{
		this.is_staffmeal = is_staffmeal;
	}
	public String getIs_throwaway()
	{
		return is_throwaway;
	}
	public void setIs_throwaway(String is_throwaway)
	{
		this.is_throwaway = is_throwaway;
	}
	public String getIs_seafood()
	{
		return is_seafood;
	}
	public void setIs_seafood(String is_seafood)
	{
		this.is_seafood = is_seafood;
	}
	public String getIs_staplefood()
	{
		return is_staplefood;
	}
	public void setIs_staplefood(String is_staplefood)
	{
		this.is_staplefood = is_staplefood;
	}
	public String getIs_combo()
	{
		return is_combo;
	}
	public void setIs_combo(String is_combo)
	{
		this.is_combo = is_combo;
	}
	public String getIs_characteristic()
	{
		return is_characteristic;
	}
	public void setIs_characteristic(String is_characteristic)
	{
		this.is_characteristic = is_characteristic;
	}
	public String getIs_recommendation()
	{
		return is_recommendation;
	}
	public void setIs_recommendation(String is_recommendation)
	{
		this.is_recommendation = is_recommendation;
	}
	public String getCombo_type()
	{
		return combo_type;
	}
	public void setCombo_type(String combo_type)
	{
		this.combo_type = combo_type;
	}
	public String getNutrition()
	{
		return nutrition;
	}
	public void setNutrition(String nutrition)
	{
		this.nutrition = nutrition;
	}
	public String getSuitable_crowds()
	{
		return suitable_crowds;
	}
	public void setSuitable_crowds(String suitable_crowds)
	{
		this.suitable_crowds = suitable_crowds;
	}
	public String getUnsuitable_crowds()
	{
		return unsuitable_crowds;
	}
	public void setUnsuitable_crowds(String unsuitable_crowds)
	{
		this.unsuitable_crowds = unsuitable_crowds;
	}
	public String getProcessing_technic()
	{
		return processing_technic;
	}
	public void setProcessing_technic(String processing_technic)
	{
		this.processing_technic = processing_technic;
	}
	public String getPhoto1()
	{
		return photo1;
	}
	public void setPhoto1(String photo1)
	{
		this.photo1 = photo1;
	}
	public String getPhoto2()
	{
		return photo2;
	}
	public void setPhoto2(String photo2)
	{
		this.photo2 = photo2;
	}
	public String getPhoto3()
	{
		return photo3;
	}
	public void setPhoto3(String photo3)
	{
		this.photo3 = photo3;
	}
	public String getSummary()
	{
		return summary;
	}
	public void setSummary(String summary)
	{
		this.summary = summary;
	}
	public String getSpicy()
	{
		return spicy;
	}
	public void setSpicy(String spicy)
	{
		this.spicy = spicy;
	}
	public String getPhoto4()
	{
		return photo4;
	}
	public void setPhoto4(String photo4)
	{
		this.photo4 = photo4;
	}
	public String getPhoto5()
	{
		return photo5;
	}
	public void setPhoto5(String photo5)
	{
		this.photo5 = photo5;
	}
	public String getPhoto6()
	{
		return photo6;
	}
	public void setPhoto6(String photo6)
	{
		this.photo6 = photo6;
	}

	public String getTo_offer() {
		return to_offer;
	}

	public void setTo_offer(String to_offer) {
		this.to_offer = to_offer;
	}

	public String getIs_show() {
		return is_show;
	}

	public void setIs_show(String is_show) {
		this.is_show = is_show;
	}
	public String getItem_description()
	{
		return item_description;
	}
	public void setItem_description(String item_description)
	{
		this.item_description = item_description;
	}

	public Integer getIs_assemble_combo() {
		return is_assemble_combo;
	}

	public void setIs_assemble_combo(Integer is_assemble_combo) {
		this.is_assemble_combo = is_assemble_combo;
	}
}
