package com.tzx.pos.bo.imp.calcamount;

import com.tzx.framework.common.util.DoubleHelper;
import com.tzx.framework.common.util.Tools;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.bo.PosCalcAmountService;
import com.tzx.pos.po.springjdbc.dao.ComboSetMealDao;
import com.tzx.pos.po.springjdbc.dao.PosActivityDao;
import com.tzx.pos.po.springjdbc.dao.PosBaseDao;
import net.sf.json.JSONObject;
import org.apache.commons.lang.math.NumberUtils;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service("com.tzx.pos.bo.imp.calcamount.PosCalcAmountServiceImp")
public class PosCalcAmountServiceImp implements PosCalcAmountService
{
	private final int defaultScale = 4;
	private final int amountScale = 2;

	@Resource(name = PosBaseDao.NAME)
	private PosBaseDao			baseDao;

	@Resource(name = ComboSetMealDao.NAME)
	private ComboSetMealDao comboSetMealDao;

	@Resource(name = PosActivityDao.NAME)
	private PosActivityDao			posActivityDao;
	
    @Override
	public void calcAmount(String tenantId, Integer storeId, String billNum)throws Exception {
	    calcAmount(tenantId,storeId,billNum,null,null);
    }

	@Override
	public void calcAmount(String tenantId, Integer storeId, String billNum,Integer dishScale,Integer billScale) throws Exception
	{
		if (Tools.isNullOrEmpty(billNum))
		{
			return;
		}

		if(null==dishScale){
//            dishScale = NumberUtils.toInt(baseDao.getSysParameter(tenantId, storeId, "CMJEWS"),amountScale);
            dishScale = amountScale;
        }
		if(null==billScale){
            billScale = NumberUtils.toInt(baseDao.getSysParameter(tenantId, storeId, "ZDJEWS"),amountScale);
        }

		/**
		 * 计算账单明细中菜目金额
		 */
		updateItemAmount(tenantId, storeId, billNum, dishScale);

		String billsql = new String("select guest,service_id,discount_mode_id,discount_rate,discount_case_id,discountr_amount,discountk_amount,source,service_amount,pb.discount_reason_id,pb.discount_num,(select coalesce(sum(pbi.item_amount),0) from pos_bill_item pbi where pbi.tenancy_id = pb.tenancy_id and pbi.store_id = pb.store_id and pbi.bill_num = pb.bill_num and pbi.item_property <> ?) as subtotal from pos_bill pb where tenancy_id =? and store_id = ? and bill_num = ?");
		SqlRowSet rs = baseDao.query4SqlRowSet(billsql, new Object[]
		{SysDictionary.ITEM_PROPERTY_MEALLIST, tenantId, storeId,billNum });

		Double guest = 0d;
		Double subtotal = 0d;
		Double discountrAmount = 0d;
		Double discountk_Amount = 0d;
		Double serviceAmount = 0d;
//		Integer serviceId = null;
//		String channel = null;
		Integer discountModeId = 0;
		Integer discountCaseId = 0;
		Integer discountReasonId = 0;
		Double discountRate = 100d;
		String discountNum = null;
		
		if (rs.next())
		{
			guest = rs.getDouble("guest");
			subtotal = rs.getDouble("subtotal");
			discountrAmount = rs.getDouble("discountr_amount");
			discountk_Amount = rs.getDouble("discountk_amount");
			serviceAmount = rs.getDouble("service_amount");
//			serviceId = rs.getInt("service_id");
//			channel = rs.getString("source");
//			if (null != subtotal && subtotal > 0)
//			{
				discountModeId = rs.getInt("discount_mode_id");
				discountCaseId = rs.getInt("discount_case_id");
				discountReasonId = rs.getInt("discount_reason_id");
				discountRate = rs.getDouble("discount_rate");
				discountNum = rs.getString("discount_num");
//			}
			
			if(discountrAmount.isNaN())
			{
				discountrAmount = 0d;
			}
			discountrAmount = DoubleHelper.round(discountrAmount, dishScale);
		}

//		/**
//		 * 判断菜目合计大于0,
//		 */
//		if (subtotal != null && subtotal == 0d)
//		{
////			String updateB = new String(
////					"update pos_bill set service_id=null,service_amount=null,discountk_amount=null,discountr_amount=null,maling_amount=null,single_discount_amount=null,discount_amount=null,free_amount=null,givi_amount=null,more_coupon=null,average_amount=null,discount_case_id=null,discount_rate=null where bill_num = ? and store_id = ?");
////			baseDao.update(updateB, new Object[]
////			{ billNum, storeId });
//			discountRate = null;
//			discountCaseId = null;
//			discountrAmount = null;
////			serviceId = null;
//			serviceAmount = 0d;
//		}

		/**
		 * 计算折扣金额 奉送菜品折扣金额为0
		 */
		if (discountRate == null || discountRate == 0d)
		{
			discountRate = 100d;
		}

		switch (discountModeId) {
			case SysDictionary.DISCOUNT_MODE_1:
				updateDiscountAmountByDiscountRate(tenantId, storeId, billNum, discountModeId, discountReasonId, discountRate, discountNum, true, dishScale);
				break;
			case SysDictionary.DISCOUNT_MODE_4:
			case SysDictionary.DISCOUNT_MODE_5:
			case SysDictionary.DISCOUNT_MODE_9:
				updateDiscountAmountByDiscountRate(tenantId, storeId, billNum, discountModeId, discountReasonId, discountRate, discountNum, false, dishScale);
				break;
			case SysDictionary.DISCOUNT_MODE_2:
				this.updateDiscountAmountByDiscountCase(tenantId, storeId, billNum, discountCaseId, discountModeId, discountReasonId, discountNum, false, dishScale);
				break;
			case SysDictionary.DISCOUNT_MODE_6:
				this.updateDiscountAmountByVipPrice(tenantId, storeId, billNum, discountModeId, discountReasonId, discountRate, discountNum, dishScale);
				break;
			case SysDictionary.DISCOUNT_MODE_7:
				updateDiscountkAmount(tenantId, storeId, billNum, discountk_Amount, dishScale);
				break;
			case SysDictionary.DISCOUNT_MODE_8:			
				this.updateDiscountAmountByDiscountCase(tenantId, storeId, billNum, discountCaseId, discountModeId, discountReasonId, discountNum, true, dishScale);
				break;
			case SysDictionary.DISCOUNT_MODE_3:
				break;
			case SysDictionary.DISCOUNT_MODE_10:
				this.updateDiscountAmountBySingleDiscount(tenantId, storeId, billNum, dishScale);
				break;
			case SysDictionary.DISCOUNT_MODE_11:
				StringBuffer activitySqlUpdateItem = new StringBuffer("update pos_bill_item set discount_rate=?,discount_amount=? where bill_num = ? and store_id = ? and discount_mode_id = ? ");
				baseDao.update(activitySqlUpdateItem.toString(), new Object[]
						{100d, 0d, billNum, storeId, SysDictionary.DISCOUNT_MODE_11});
				break;
			default:
				/**
				 * 取消折扣 更新每个pos_bill_item 账单折扣，快餐单品折扣另外处理如下
				 */
				StringBuilder querySql = new StringBuilder("select format_state from organ where tenancy_id=? and id=?");
				SqlRowSet rst = baseDao.query4SqlRowSet(querySql.toString(), new Object[]
						{tenantId, storeId});
				if (rst.next()) {
					if ("2".equals(rst.getString("format_state"))) {
						break;
					}
				}
				StringBuffer sqlUpdate = new StringBuffer("update pos_bill set discount_mode_id=?,discount_case_id=?,discount_num=?,discount_rate = ?,discountk_amount = ? where bill_num = ? and store_id = ?");
				baseDao.update(sqlUpdate.toString(), new Object[]
						{null, null, null, 100d, 0d, billNum, storeId});

				StringBuffer sqlUpdateItem = new StringBuffer("update pos_bill_item set discount_mode_id=?,discount_rate=?,discount_amount=?,discount_reason_id=?,discount_num=?,single_amount =? where bill_num = ? and store_id = ?");
				baseDao.update(sqlUpdateItem.toString(), new Object[]
				{ null, 100d, 0d, 0, null, 0d, billNum, storeId });
				break;
		}

		/**
		 * 统计折扣金额
		 */
		double discountkAmount = 0d;
		StringBuilder countBillItemSql = new StringBuilder("select coalesce(sum(bi.discount_amount),0) as discount_amount from pos_bill_item bi where bi.item_property <> ? and bi.bill_num = ? and bi.store_id = ?");
		rs = baseDao.query4SqlRowSet(countBillItemSql.toString(), new Object[]
		{ SysDictionary.ITEM_PROPERTY_MEALLIST,billNum, storeId });
		if (rs.next())
		{
			discountkAmount = rs.getDouble("discount_amount");
		}

		/**
		 * 计算奉送金额
		 */
		double giviAmount = 0d;
		StringBuilder countGiveAmountSql = new StringBuilder("select coalesce(sum(bi.item_amount),0) as givi_amount from pos_bill_item bi where bi.item_remark = ? and bi.item_property <> ? and bi.bill_num = ? and bi.store_id = ?");
		rs = baseDao.query4SqlRowSet(countGiveAmountSql.toString(), new Object[]
		{ SysDictionary.ITEM_REMARK_FS02,SysDictionary.ITEM_PROPERTY_MEALLIST,billNum, storeId });
		if (rs.next())
		{
			giviAmount = rs.getDouble("givi_amount");
		}
		
		/**
		 * 计算服务费
		 */
//		if(SysDictionary.CHANEL_MD01.equals(channel))
//		{
		serviceAmount = this.calcServiceAmount(tenantId, storeId, billNum, subtotal, giviAmount);
//		}

		Double itemDiscountrAmount = discountrAmount;
		Double serviceDiscountrAmount = 0d;
		if (Tools.hv(itemDiscountrAmount) && itemDiscountrAmount > 0)
		{
			//sum(账单明细.实付金额)+服务费 = 账单金额-优惠金额 - 奉送金额
			Double itemAmount = DoubleHelper.sub(DoubleHelper.sub(subtotal, discountkAmount, defaultScale), giviAmount, defaultScale);
			/** 默认菜品折让金额等于账单折让金额,
			 *  菜品金额小于账单折让金额,菜品折让金额等于菜品金额,
			 *  剩余折让金额为服务费折让金额 
			 *  */
			if (itemAmount < discountrAmount)
			{
				itemDiscountrAmount = itemAmount;
			}
			
			serviceDiscountrAmount = DoubleHelper.sub(discountrAmount, itemDiscountrAmount, defaultScale);
			
			/**
			 * 计算折让金额 奉送菜品折让金额为0 退菜菜品折让金额为0
			 */
			StringBuilder updateItemDiscountSql = new StringBuilder("update pos_bill_item set discountr_amount = 0 where bill_num = ? and store_id = ? ");
			baseDao.update(updateItemDiscountSql.toString(), new Object[]
			{ billNum, storeId });
			
			// 营销活动，折让金额分摊到每个菜品
			if (discountModeId != null && SysDictionary.DISCOUNT_MODE_11 == discountModeId)
			{
				List<JSONObject> jsonItemList = posActivityDao.getusedActivityItemRules(tenantId, storeId, billNum);
				for (JSONObject json : jsonItemList)
				{
					Double hddiscountrAmount = 0d; // 每个活动的折让金额
					String activityType = json.optString("activity_type");
					String activityBatchNum = json.optString("activity_batch_num");

					if (SysDictionary.MARKETING_ACTIVITY_MEJXJ.equals(activityType))
					{
						hddiscountrAmount = hddiscountrAmount + (Double.parseDouble(json.optString("discount_amount")) * Integer.parseInt(json.optString("activity_count")));
					}
					else if (SysDictionary.MARKETING_ACTIVITY_MEDZ.equals(activityType))
					{
						hddiscountrAmount = hddiscountrAmount + (Double.parseDouble(json.optString("item_all_amount")) * (100 - Double.parseDouble(json.optString("discount_amount"))) / 100);
					}
					else if (SysDictionary.MARKETING_ACTIVITY_MEJJG.equals(activityType))
					{
						hddiscountrAmount = hddiscountrAmount + (Double.parseDouble(json.optString("hdzc_item_amount")) - Double.parseDouble(json.optString("discount_amount")));
					}
					else if (SysDictionary.MARKETING_ACTIVITY_MEZC.equals(activityType))
					{
						hddiscountrAmount = hddiscountrAmount + Double.parseDouble(json.optString("hdzc_item_amount"));
					}
					else if (SysDictionary.MARKETING_ACTIVITY_ZHTYJ.equals(activityType))
					{
						hddiscountrAmount = hddiscountrAmount + Double.parseDouble(json.optString("hdtyj_item_amount")) - Double.parseDouble(json.optString("discount_amount"));
					}
					else if (SysDictionary.MARKETING_ACTIVITY_DEFBJ.equals(activityType))
					{
						hddiscountrAmount = hddiscountrAmount + Double.parseDouble(json.optString("hdbj_item_amount")) * (100 - Double.parseDouble(json.optString("discount_amount"))) / 100;
					}
					updateHdDiscountrAmount(activityType, tenantId, storeId, billNum, activityBatchNum, hddiscountrAmount, dishScale);
				}
			}
			else
			{
				updateDiscountrAmount(tenantId, storeId, billNum, itemDiscountrAmount, discountModeId, discountReasonId, discountRate, discountNum, dishScale);
			}
		}

		// 账单金额 = 项目小计金额 + 服务费金额
		serviceAmount = DoubleHelper.round(serviceAmount, dishScale);
		double billAmount = DoubleHelper.add(subtotal, serviceAmount, defaultScale);

		// 优惠金额 = 折扣金额 + 折让金额
		double discountAmount = DoubleHelper.add(discountrAmount, discountkAmount, defaultScale);

		// 进位前付款金额 = sum(账单明细.实付金额)+服务费 = 账单金额-优惠金额 - 奉送金额
		double paymentAmount = DoubleHelper.sub(billAmount, DoubleHelper.add(discountAmount, giviAmount, defaultScale), defaultScale);

		//如有单品折扣和整单折扣混打折减去单品折扣金额
//		paymentAmount = DoubleHelper.sub(paymentAmount,singleAmountTotal,defaultScale);
		if (paymentAmount < 0)
		{
			paymentAmount = 0d;
		}

		// 根据账单小数取舍方式计算进位后付款金额
		String billPointType = baseDao.getSysParameter(tenantId, storeId, "billpointtype");
		if(Tools.isNullOrEmpty(billPointType))
		{
			billPointType = SysDictionary.BILL_POINT_TYPE_HALF_ADJUST;
		}

		double paymentAmount2 = 0d;
		switch (billPointType)
		{
			case SysDictionary.BILL_POINT_TYPE_ROUNDING:
				paymentAmount2 = DoubleHelper.roundDown(paymentAmount, billScale);
				break;
			case SysDictionary.BILL_POINT_TYPE_ONE_ADJUST:
				paymentAmount2 = DoubleHelper.roundUp(paymentAmount, billScale);
				break;
			default:
				paymentAmount2 = DoubleHelper.round(paymentAmount, billScale);
				break;
		}

		// 抹零金额 = 进位后付款金额-进位前付款金额
		double malingAmount = DoubleHelper.sub(paymentAmount2, paymentAmount, dishScale);

		// 人均消费 = 付款金额/顾客数
		double averageAmount = DoubleHelper.div(paymentAmount2, guest, defaultScale);
		
		// 修改差额
		double difference = paymentAmount2;
		StringBuilder queryBillAmountSql = new StringBuilder(
				"select b.payment_amount,coalesce(sum(p.currency_amount),0) as currency_amount,coalesce(sum(p.more_coupon),0) as more_coupon from pos_bill b left join pos_bill_payment p on b.store_id=p.store_id and b.bill_num=p.bill_num where b.tenancy_id=? and b.store_id=? and b.bill_num=? group by b.bill_num,b.payment_amount");
		rs = baseDao.query4SqlRowSet(queryBillAmountSql.toString(), new Object[]
		{ tenantId, storeId, billNum });

		if (rs.next())
		{
			difference = DoubleHelper.sub(paymentAmount2, DoubleHelper.sub(rs.getDouble("currency_amount"), rs.getDouble("more_coupon"), defaultScale), defaultScale);
		}

		StringBuilder updateBillSql = new StringBuilder(
				"update pos_bill set subtotal=?,service_amount=?,bill_amount=?,payment_amount=?,maling_amount=?,discountk_amount=?,discountr_amount=?,discount_amount=?,givi_amount=?,average_amount=?,item_discountr_amount=?,service_discountr_amount=?,difference=? where bill_num = ? and store_id = ?");
		baseDao.update(updateBillSql.toString(), new Object[]
		{ subtotal, serviceAmount, billAmount, paymentAmount2, malingAmount, discountkAmount, discountrAmount, discountAmount, giviAmount, averageAmount, itemDiscountrAmount, serviceDiscountrAmount, difference, billNum, storeId });

		/**
		 * 计算抹零平摊金额 奉送菜品抹零平摊金额为0 退菜菜品抹零平摊金额为0
		 */
		StringBuilder updateItemScrapSql = new StringBuilder("update pos_bill_item set single_discount_amount = 0 where bill_num = ? and store_id = ? ");
		baseDao.update(updateItemScrapSql.toString(), new Object[]
		{ billNum, storeId });
		if (Tools.hv(malingAmount) && malingAmount != 0d)
		{
			updateScrapAmount(tenantId, storeId, billNum, malingAmount,dishScale);
		}

		StringBuilder queryBillItemSql = new StringBuilder("select id,item_amount,discount_amount,single_discount_amount,discountr_amount,coalesce(item_remark_his,item_remark) as item_remark from pos_bill_item where bill_num = ? and store_id = ?");
		rs = baseDao.query4SqlRowSet(queryBillItemSql.toString(), new Object[]
		{ billNum, storeId });

		List<Object[]> batchItem = new ArrayList<Object[]>();
		while (rs.next())
		{
			// 实付金额=菜目金额+单品折扣-(折扣金额+折让金额)
			double realAmount = DoubleHelper.sub(DoubleHelper.add(rs.getDouble("item_amount"), rs.getDouble("single_discount_amount"), defaultScale), DoubleHelper.add(rs.getDouble("discount_amount"), rs.getDouble("discountr_amount"), defaultScale), dishScale);

			if (SysDictionary.ITEM_REMARK_FS02.equalsIgnoreCase(rs.getString("item_remark")))
			{
				realAmount = 0d;
			}
			batchItem.add(new Object[]
			{ DoubleHelper.round(realAmount, dishScale), rs.getInt("id") });
		}
		if (batchItem.size() > 0)
		{
			baseDao.batchUpdate("update pos_bill_item set real_amount=? where id = ?", batchItem);
		}
	}

    /**
     * 通过标准价格均摊计算套餐明细单价（辅助数量大于1的菜品也称单价）
     * @param tenantId
     * @param storeId
     * @param billNum
     * @throws Exception
     */
	@SuppressWarnings("unused")
	@Deprecated
	private void calcMealListItemPriceByStandardPrice(String tenantId, int storeId, String billNum) throws Exception{
        StringBuilder meallistPriceSql = new StringBuilder("select hii.is_assemble_combo,pbi.id, pbi.item_property, pbi.item_id, pbi.setmeal_id, pbi.setmeal_rwid, pbi.item_serial, pbi.assist_num, pbi.item_price, hicd.is_itemgroup, hiu.standard_price, hig.item_group_price from pos_bill_item pbi ");
		meallistPriceSql.append(" left join hq_item_info hii on hii.id = pbi.item_id ");
        meallistPriceSql.append(" left join hq_item_combo_details hicd on pbi.assist_item_id = hicd.id ");
        meallistPriceSql.append(" left join hq_item_unit hiu on hicd.details_id = hiu.item_id and hicd.item_unit_id = hiu.id ");
        meallistPriceSql.append(" left join hq_item_group hig on hicd.details_id = hig.id ");
        meallistPriceSql.append(" where pbi.bill_num = ? and pbi.item_property in (?, ?) and pbi.store_id = ? and pbi.tenancy_id = ? ");

        List<JSONObject> list = baseDao.query4Json(tenantId, meallistPriceSql.toString(), new Object[]
                { billNum, SysDictionary.ITEM_PROPERTY_SETMEAL, SysDictionary.ITEM_PROPERTY_MEALLIST, storeId, tenantId });

        int scale =2;

        for(JSONObject item: list){
            String itemProperty = item.optString("item_property");
            Integer itemId = item.optInt("item_id");
            Integer itemSerial = item.optInt("item_serial");
            Double itemPrice = item.optDouble("item_price");
			int isAssembleCombo = item.optInt("is_assemble_combo");
			//排除使用组合套餐
//			if(isAssembleCombo == 0) {
				if (SysDictionary.ITEM_PROPERTY_SETMEAL.equals(itemProperty)) {
					List<JSONObject> setmealItemList = new ArrayList<>();

					Double meallistItemPriceTotal = 0d; // 套餐明细单价之和
					JSONObject maxItem = null; // 单价最大菜品
					for (JSONObject meallistItem : list) {
						Integer meallistId = meallistItem.optInt("id"); // pos_bill_item主键id
						String isItemgroup = meallistItem.optString("is_itemgroup"); // 是否项目组，N：否，Y：是
						String detailsItemProperty = meallistItem.optString("item_property");
						Integer detailsSetmealId = meallistItem.optInt("setmeal_id");
						Integer detailsSetmealRwid = meallistItem.optInt("setmeal_rwid");
						Double detailsItemAssistNum = meallistItem.optDouble("assist_num"); // 辅助数量
						Double detailsItemStandardPrice = meallistItem.optDouble("standard_price"); // 非项目组单价
						Double detailsItemGroupPrice = meallistItem.optDouble("item_group_price"); // 项目组单价

						if (detailsItemAssistNum.isNaN() || detailsItemAssistNum <= 0) {
							detailsItemAssistNum = 1d;
						}

						if (detailsItemStandardPrice.isNaN()) {
							detailsItemStandardPrice = 0d;
						}

						if (detailsItemGroupPrice.isNaN()) {
							detailsItemGroupPrice = 0d;
						}

						if (SysDictionary.ITEM_PROPERTY_MEALLIST.equals(detailsItemProperty) && itemId.equals(detailsSetmealId) && itemSerial.equals(detailsSetmealRwid)) {
							Double detailsItemPrice = 0d;
							// 乘以辅助数量后得到价格
						if (1 == isAssembleCombo)
						{
							detailsItemPrice = meallistItem.optDouble("item_price");
						}
							else if ("N".equals(isItemgroup)) {
								detailsItemPrice = DoubleHelper.mul(detailsItemStandardPrice, detailsItemAssistNum, scale);
							} else {
								detailsItemPrice = DoubleHelper.mul(detailsItemGroupPrice, detailsItemAssistNum, scale);
							}

							meallistItemPriceTotal = DoubleHelper.add(meallistItemPriceTotal, detailsItemPrice, scale);

							meallistItem.put("id", meallistId);
							meallistItem.put("item_price", detailsItemPrice);
							setmealItemList.add(meallistItem);

							if (null != maxItem) {
								Double maxItemPrice = maxItem.optDouble("item_price");
								if (detailsItemPrice > maxItemPrice) {
									maxItem = meallistItem;
								}
							} else {
								maxItem = meallistItem;
							}
						}
					}

					double detailsItemPriceTotal = 0d;
					for (JSONObject meallistItem : setmealItemList) {
						Double detailsItemPrice = meallistItem.optDouble("item_price");
						if (meallistItemPriceTotal > 0) {
							detailsItemPrice = DoubleHelper.mul(DoubleHelper.div(itemPrice, meallistItemPriceTotal, 4), detailsItemPrice, scale);
						} else {
							detailsItemPrice = 0d;
						}

						detailsItemPriceTotal = DoubleHelper.add(detailsItemPriceTotal, detailsItemPrice, scale);
						meallistItem.put("item_price", detailsItemPrice);
					}

					// 套餐主项单价和套餐明细单价之和，存在差值时，将差值加到单价最大的菜品上
					if (null != maxItem && detailsItemPriceTotal != itemPrice)
					{
						Double detailsItemPrice = maxItem.optDouble("item_price");
						detailsItemPrice = DoubleHelper.add(detailsItemPrice, DoubleHelper.sub(itemPrice, detailsItemPriceTotal, scale), scale);
						maxItem.put("item_price", detailsItemPrice);
					}

					List<Object[]> objects = new ArrayList<>();
					for (JSONObject meallistItem : setmealItemList) {
						Double detailsItemPrice = meallistItem.optDouble("item_price");
						Integer id = meallistItem.optInt("id");
						Object[] obj = new Object[]{detailsItemPrice, id, storeId, tenantId};
						objects.add(obj);
					}
					if (objects.size() > 0) {
						String updatePriceSql = "update pos_bill_item set item_price = ? where id = ? and store_id = ? and tenancy_id = ?";
						baseDao.batchUpdate(updatePriceSql, objects);
					}
				}
//			}
        }

    }

	/**
	 * 修改账单明细菜目金额,及做法金额
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @param scale
	 * @throws Exception
	 */
	private void updateItemAmount(String tenantId, int storeId, String billNum, int scale) throws Exception
	{
//        this.calcMealListItemPriceByStandardPrice(tenantId, storeId, billNum);

//		final int defaultScale = 4;

		StringBuilder queryBillItemSql = new StringBuilder();
		queryBillItemSql
				.append(" select coalesce(zi.method_amount,0) as method_amount,coalesce(gd.makeup_money,0) as assist_amount,coalesce(bi.assist_num,0) as assist_num, bi.id,bi.item_id,bi.setmeal_id,bi.item_serial,bi.setmeal_rwid,bi.item_property,bi.item_remark,bi.item_price,bi.assist_money,bi.method_money,bi.item_count,bi.third_price,combo_prop,discount_mode_id from pos_bill_item bi");
		queryBillItemSql.append(" left join (select gd.*,cd.id as details_id from hq_item_group_details gd left join hq_item_combo_details cd on gd.item_group_id = cd.details_id and cd.is_itemgroup='Y') gd on bi.assist_item_id=gd.details_id  and gd.item_id=bi.item_id and gd.item_unit_id=bi.item_unit_id");
		queryBillItemSql.append(" left join (select bill_num,rwid,coalesce(sum(amount),0) as method_amount from pos_zfkw_item group by bill_num,rwid) zi on bi.bill_num = zi.bill_num and bi.rwid=zi.rwid");
		queryBillItemSql.append(" where bi.bill_num=? and bi.store_id = ?");
		// queryBillItemSql.append(" group by bi.id,bi.item_id,bi.setmeal_id,bi.item_serial,bi.setmeal_rwid,bi.item_property,bi.item_remark,bi.item_price,bi.assist_money,bi.method_money,bi.item_count,bi.third_price");

		List<JSONObject> itemList = baseDao.query4Json(tenantId, queryBillItemSql.toString(), new Object[]
		{ billNum, storeId });
		List<Object[]> batchArgs = new ArrayList<Object[]>();
		for (JSONObject item : itemList)
		{
			if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.optString("item_property")))
			{
				Integer itemId = item.optInt("item_id");
				Integer itemSerial = item.optInt("item_serial");
				String itemRemark = item.optString("item_remark");
				
			    double itemPrice = item.optDouble("item_price"); // 单价
				double methodMoney = DoubleHelper.mul(item.optDouble("method_amount"), item.optDouble("item_count"), scale);
				double assistMoney = DoubleHelper.mul(item.optDouble("assist_amount"), item.optDouble("item_count"), scale);
				double itemAmount = DoubleHelper.mul(item.optDouble("item_price"), item.optDouble("item_count"), scale);
				if (SysDictionary.ITEM_PROPERTY_SETMEAL.equalsIgnoreCase(item.optString("item_property")))
				{
					methodMoney = 0d;
					assistMoney = 0d;
//					for (JSONObject detail : itemList)
//					{
//						if (SysDictionary.ITEM_PROPERTY_MEALLIST.equals(detail.optString("item_property")) && item.optInt("item_id") == detail.optInt("setmeal_id") && item.optInt("item_serial") == detail.optInt("setmeal_rwid") && item.optString("item_remark").equals(detail.optString("item_remark")))
//						{
//							double detailMethodMoney = DoubleHelper.mul(detail.optDouble("method_amount"), detail.optDouble("item_count"), scale);
//							methodMoney = DoubleHelper.add(methodMoney, detailMethodMoney, scale);
//
//							double detailAssistMoney = DoubleHelper.mul(detail.optDouble("assist_amount"), detail.optDouble("item_count"), scale);
//							assistMoney = DoubleHelper.add(assistMoney, detailAssistMoney, scale);
//
//							double assistNum =1d;
//							if(detail.containsKey("assist_num") && detail.optDouble("assist_num")>1)
//							{
//								assistNum  = detail.optDouble("assist_num");
//							}
//							
//							double itemAmount = DoubleHelper.add(DoubleHelper.mul(detail.optDouble("item_price"), DoubleHelper.div(detail.optDouble("item_count"), assistNum, defaultScale), defaultScale), DoubleHelper.add(detailAssistMoney, detailMethodMoney, defaultScale), scale);
//
//                            double detailItemPrice = DoubleHelper.div(detail.optDouble("item_price"), assistNum, 2);
//
//							batchArgs.add(new Object[]
//							{detailItemPrice, detailMethodMoney, detailAssistMoney, itemAmount, detail.optInt("id") });
//						}
//					}

					Integer MaxDetailItemId = null;
					double maxDetailItemAmount = 0d;
					double sumDetailItemAmount = 0d;
					for (JSONObject detail : itemList)
					{
						if (SysDictionary.ITEM_PROPERTY_MEALLIST.equals(detail.optString("item_property")) && itemId == detail.optInt("setmeal_id") && itemSerial == detail.optInt("setmeal_rwid") && itemRemark.equals(detail.optString("item_remark")))
						{
							Double detailItemAmount = ParamUtil.getDoubleValueByObject(detail, "item_amount");
							if(null==detailItemAmount || 0d==detailItemAmount)
							{
								detailItemAmount = DoubleHelper.mul(ParamUtil.getDoubleValueByObject(detail, "item_price"), ParamUtil.getDoubleValueByObject(detail, "item_count"), defaultScale);
							}
							sumDetailItemAmount = DoubleHelper.add(sumDetailItemAmount, detailItemAmount, defaultScale);
							if (null == MaxDetailItemId || maxDetailItemAmount < detailItemAmount)
							{
								MaxDetailItemId = detail.optInt("id");
								maxDetailItemAmount = detailItemAmount;
							}
						}
					}

					Double dRatio = DoubleHelper.div(itemAmount, sumDetailItemAmount, defaultScale);
					Double restItemAmount = itemAmount;
					for (JSONObject detail : itemList)
					{
						if (SysDictionary.ITEM_PROPERTY_MEALLIST.equals(detail.optString("item_property")) && itemId == detail.optInt("setmeal_id") && itemSerial == detail.optInt("setmeal_rwid") && itemRemark.equals(detail.optString("item_remark")))
						{
							Double detailItemAmount = ParamUtil.getDoubleValueByObject(detail, "item_amount");
							Double detailItemCount = ParamUtil.getDoubleValueByObject(detail, "item_count");
							Double detailItemPrice = ParamUtil.getDoubleValueByObject(detail, "item_price");
							if(null==detailItemAmount || 0d==detailItemAmount)
							{
								detailItemAmount = DoubleHelper.mul(detailItemPrice, detailItemCount, defaultScale);
							}
							detailItemAmount = DoubleHelper.mul(detailItemAmount, dRatio, scale);
							restItemAmount = DoubleHelper.sub(restItemAmount, detailItemAmount, scale);
						}
					}

					for (JSONObject detail : itemList)
					{
						if (SysDictionary.ITEM_PROPERTY_MEALLIST.equals(detail.optString("item_property")) && itemId == detail.optInt("setmeal_id") && itemSerial == detail.optInt("setmeal_rwid") && itemRemark.equals(detail.optString("item_remark")))
						{
							//计算菜目金额
							Double detailItemAmount = ParamUtil.getDoubleValueByObject(detail, "item_amount"); 
							Double detailItemCount = ParamUtil.getDoubleValueByObject(detail, "item_count"); 
							Double detailItemPrice = ParamUtil.getDoubleValueByObject(detail, "item_price");
							if(null==detailItemAmount || 0d==detailItemAmount)
							{
								detailItemAmount = DoubleHelper.mul(detailItemPrice, detailItemCount, defaultScale);
							}
							detailItemAmount = DoubleHelper.mul(detailItemAmount, dRatio, scale);
							if (MaxDetailItemId == detail.optInt("id"))
							{
								detailItemAmount = DoubleHelper.add(detailItemAmount, restItemAmount, scale);
							}

							//计算单价
							detailItemPrice = DoubleHelper.div(detailItemAmount, detailItemCount, scale);
							
							//计算做法金额
							double detailMethodMoney = DoubleHelper.mul(detail.optDouble("method_amount"), detailItemCount, scale);
							methodMoney = DoubleHelper.add(methodMoney, detailMethodMoney, scale);

							//计算套餐加价
							double detailAssistMoney = DoubleHelper.mul(detail.optDouble("assist_amount"), detailItemCount, scale);
							assistMoney = DoubleHelper.add(assistMoney, detailAssistMoney, scale);

							detailItemAmount = DoubleHelper.add(detailItemAmount, DoubleHelper.add(detailAssistMoney, detailMethodMoney, defaultScale), scale);

							batchArgs.add(new Object[]
							{ detailItemPrice, detailMethodMoney, detailAssistMoney, detailItemAmount, detail.optInt("id") });
						}
					}
				}

				itemAmount = DoubleHelper.add(itemAmount, DoubleHelper.add(assistMoney, methodMoney, defaultScale), scale);
				//组合套餐做法加价
//				int comboProp = item.optInt("combo_prop");
//				if(comboProp > 0) {
//					methodMoney = item.optDouble("method_money");
//					itemAmount = DoubleHelper.add(itemAmount, item.optDouble("method_money"), scale);
//				}
//				if (SysDictionary.DISCOUNT_MODE_10 != item.optInt("discount_mode_id")) {
					batchArgs.add(new Object[]
							{itemPrice, methodMoney, assistMoney, itemAmount, item.optInt("id")});
//				}
			}
		}
		if (batchArgs.size() > 0)
		{
			StringBuilder updateBillItemSql = new StringBuilder("update pos_bill_item set item_price=?, method_money=?,assist_money=?,item_amount=?,discount_amount=0,discountr_amount=0,single_discount_amount=0 where id=?");
			baseDao.batchUpdate(updateBillItemSql.toString(), batchArgs);
		}
	}
	
	/**
	 * 修改折扣率金额
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @param discountRate
	 * @param isNotState
	 *            不区分菜品是否允许折扣
	 * @throws Exception
	 */
	@SuppressWarnings("unused")
	@Deprecated
	private void updateDiscountRateAmount(String tenantId, int storeId, String billNum, double discountRate, boolean isNotState, int scale) throws Exception
	{
		StringBuilder qureyBillItem = new StringBuilder("select id,item_id,setmeal_id,item_serial,setmeal_rwid,item_property,discount_state,item_remark,item_amount,item_price,assist_money,method_money,item_count,discount_mode_id,discount_rate,discount_amount,real_amount,single_amount,item_amount,combo_prop from pos_bill_item where bill_num = ? and store_id = ?");
		List<JSONObject> itemList = baseDao.query4Json(tenantId, qureyBillItem.toString(), new Object[]
		{ billNum, storeId });

		List<Object[]> batchArgs = new ArrayList<Object[]>();
		List<Object[]> batchArgs2 = new ArrayList<Object[]>();
		for (JSONObject item : itemList)
		{
			if(SysDictionary.DISCOUNT_MODE_10 !=item.optInt("discount_mode_id"))
			{
				if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.optString("item_property")))
				{
					double itemDiscountRate = 100d;
					if (isNotState || "Y".equalsIgnoreCase(item.optString("discount_state")))
					{
						itemDiscountRate = discountRate;
					}
					batchArgs.addAll(getDiscountRateAmount(item, itemList, itemDiscountRate, scale));
				}
			}
			//单品折扣
			else
			{
				double rate = DoubleHelper.sub(1d, DoubleHelper.div(discountRate, 100d, scale), scale);
				double itemDiscountRate = item.optDouble("discount_rate");
				double itemAmount = DoubleHelper.add(DoubleHelper.mul(item.optDouble("item_price"), item.optDouble("item_count"), scale), DoubleHelper.add(item.optDouble("method_money"), item.optDouble("assist_money"), scale), scale);
				double realAmount = item.optDouble("real_amount");
				double singleAmount = item.optDouble("single_amount");
				if(singleAmount == 0d || Double.isNaN(singleAmount)) {
//					double discountAmount =DoubleHelper.mul(realAmount, rate, 4);
					double discountAmount =DoubleHelper.mul(itemAmount, rate, 4);
					batchArgs2.add(new Object[]{itemDiscountRate, discountAmount, discountAmount, item.optInt("id")});
				}else {
//					double discountAmount =DoubleHelper.mul(singleAmount, rate, 4);
					itemAmount = DoubleHelper.sub(itemAmount, singleAmount, 4);
					double discountAmount =DoubleHelper.add(singleAmount, DoubleHelper.mul(itemAmount, rate, 4), 4);
					batchArgs2.add(new Object[]{itemDiscountRate, discountAmount, singleAmount, item.optInt("id")});
				}
			}
		}
		if (batchArgs.size() > 0)
		{
			baseDao.batchUpdate("update pos_bill_item set discount_rate=?,discount_amount=?  where id = ?", batchArgs);
		}
		if (batchArgs2.size() > 0)
		{
			baseDao.batchUpdate("update pos_bill_item set discount_rate=?,discount_amount=?,single_amount=?  where id = ?", batchArgs2);
		}
	}
	
	/**
	 * 计算折扣减免金额 奉送菜品折扣金额为0
	 * 
	 * @param item
	 * @param itemList
	 * @param scale
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("unused")
	@Deprecated
	private List<Object[]> getDiscountDerateAmount(JSONObject item, List<JSONObject> itemList, int scale) throws Exception
	{
		final int defaultScale = 4;
		List<Object[]> batchArgs = new ArrayList<Object[]>();
		if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.optString("item_property")))
		{
			double derate = item.optDouble("derate");
			if(derate>item.optDouble("item_price"))
			{
				derate = item.optDouble("item_price") ;
			}
			// 减免金额
			double itemDiscountAmount = DoubleHelper.mul(derate, item.optDouble("item_count"), scale);
			// 奉送菜品折扣金额为0
			if (SysDictionary.ITEM_REMARK_TC01.equalsIgnoreCase(item.optString("item_remark")) || SysDictionary.ITEM_REMARK_FS02.equalsIgnoreCase(item.optString("item_remark")) || SysDictionary.ITEM_REMARK_CJ05.equalsIgnoreCase(item.optString("item_remark")))
			{
				itemDiscountAmount = 0d;
			}
			batchArgs.add(new Object[]
			{ 100d, itemDiscountAmount, item.optInt("id") });

			if (SysDictionary.ITEM_PROPERTY_SETMEAL.equals(item.optString("item_property")))
			{ // 将减免金额平摊到套餐明细
				Integer maxId = null;
				double maxAmount = 0d;
				double sumItemdiscountAmount = 0d;
				double proportion = DoubleHelper.div(itemDiscountAmount, item.optDouble("item_amount"), defaultScale);

				List<JSONObject> detailList = new ArrayList<JSONObject>();
				for (JSONObject detail : itemList)
				{
					if (SysDictionary.ITEM_PROPERTY_MEALLIST.equals(detail.optString("item_property")) && item.optInt("item_id") == detail.optInt("setmeal_id") && item.optInt("item_serial") == detail.optInt("setmeal_rwid"))
					{
						double detailDiscountAmount = DoubleHelper.mul(detail.optDouble("item_amount"), proportion, scale);
						// 奉送菜品折扣金额为0
						if (SysDictionary.ITEM_REMARK_TC01.equalsIgnoreCase(detail.optString("item_remark")) || SysDictionary.ITEM_REMARK_FS02.equalsIgnoreCase(detail.optString("item_remark")) || SysDictionary.ITEM_REMARK_CJ05.equalsIgnoreCase(detail.optString("item_remark")))
						{
							detailDiscountAmount = 0d;
						}
						sumItemdiscountAmount = DoubleHelper.add(sumItemdiscountAmount, detailDiscountAmount, defaultScale);

						detail.put("discount_rate", 100);
						detail.put("discount_amount", detailDiscountAmount);
						detailList.add(detail);

						if (null == maxId || maxAmount < detail.optDouble("item_amount"))
						{
							maxId = detail.optInt("id");
							maxAmount = detail.optDouble("item_amount");
						}
					}
				}

				for (JSONObject detail : detailList)
				{
					double detailDiscountAmount = DoubleHelper.mul(detail.optDouble("item_amount"), proportion, scale);
					if (maxId == detail.optInt("id"))
					{
						detailDiscountAmount = DoubleHelper.add(detailDiscountAmount, DoubleHelper.sub(itemDiscountAmount, sumItemdiscountAmount, defaultScale), scale);
					}
					// 奉送菜品折扣金额为0
					if (SysDictionary.ITEM_REMARK_TC01.equalsIgnoreCase(detail.optString("item_remark")) || SysDictionary.ITEM_REMARK_FS02.equalsIgnoreCase(detail.optString("item_remark")) || SysDictionary.ITEM_REMARK_CJ05.equalsIgnoreCase(detail.optString("item_remark")))
					{
						detailDiscountAmount = 0d;
					}
					batchArgs.add(new Object[]
					{ 100d, detailDiscountAmount, detail.optInt("id") });
				}
			}
		}
		return batchArgs;
	}
	
	
	/**
	 * 计算折扣率金额 奉送菜品折扣金额为0
	 * 
	 * @param item
	 * @param itemList
	 * @param itemDiscountRate
	 * @param scale
	 * @return
	 * @throws Exception
	 */
	@Deprecated
	private List<Object[]> getDiscountRateAmount(JSONObject item, List<JSONObject> itemList, double itemDiscountRate, int scale) throws Exception
	{
		final int defaultScale = 4;
		List<Object[]> batchArgs = new ArrayList<Object[]>();
		if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.optString("item_property")))
		{
			if (SysDictionary.ITEM_REMARK_TC01.equals(item.optString("item_remark")) || SysDictionary.ITEM_REMARK_FS02.equals(item.optString("item_remark")) || SysDictionary.ITEM_REMARK_CJ05.equals(item.optString("item_remark")))
			{
				itemDiscountRate = 100d;
			}

			double rate = DoubleHelper.sub(1d, DoubleHelper.div(itemDiscountRate, 100d, defaultScale), defaultScale);
			double itemAmount = item.optDouble("item_amount");
			if (itemAmount == 0d)
			{
				itemAmount = DoubleHelper.add(DoubleHelper.mul(item.optDouble("item_price"), item.optDouble("item_count"), defaultScale), DoubleHelper.add(item.optDouble("method_money"), item.optDouble("assist_money"), defaultScale), defaultScale);
			}
			batchArgs.add(new Object[]
			{ itemDiscountRate, DoubleHelper.mul(itemAmount, rate, scale), item.optInt("id") });

			if (SysDictionary.ITEM_PROPERTY_SETMEAL.equalsIgnoreCase(item.optString("item_property")))
			{
				// 套餐明细中，明细金额最大的折扣金额=套主项折扣金额-其它套餐明细折扣金额，否则会产生误差
				Integer dMaxId = null;
				double dItemMaxAmount = 0; // 循环查询明细中金额最大的
				double detailSumAmount = 0; // 套餐明细折扣金额之和
				
				List<JSONObject> detailList = new ArrayList<JSONObject>();
				for (JSONObject detail : itemList)
				{
					if (SysDictionary.ITEM_PROPERTY_MEALLIST.equals(detail.optString("item_property")) && item.optInt("item_id") == detail.optInt("setmeal_id") && item.optInt("item_serial") == detail.optInt("setmeal_rwid"))
					{
						double detailItemAmount = detail.optDouble("item_amount");
						if (detailItemAmount == 0d)
						{
							detailItemAmount = DoubleHelper.add(DoubleHelper.mul(detail.optDouble("item_price"), detail.optDouble("item_count"), defaultScale), DoubleHelper.add(detail.optDouble("method_money"), detail.optDouble("assist_money"), defaultScale), defaultScale);
						}
						// 套餐明细折扣金额
						double discountAmount = DoubleHelper.mul(detailItemAmount, rate, scale); 
						// 套餐明细的折扣金额之和
						detailSumAmount = DoubleHelper.add(detailSumAmount, discountAmount, scale);
						// 套餐中明细金额最大的
						if(dMaxId == null || detailItemAmount > dItemMaxAmount ){
							dMaxId = detail.optInt("id");
							dItemMaxAmount = detailItemAmount;
						}
						
						JSONObject itemA = new JSONObject();
						itemA.put("id", detail.optInt("id"));
						itemA.put("discount_amount", discountAmount);
						detailList.add(itemA);
					}
//					//组合套餐
//					else if(SysDictionary.ITEM_PROPERTY_MEALLIST.equals(detail.optString("item_property")) && item.optInt("item_id") == detail.optInt("setmeal_id") && item.optInt("combo_prop") > 0) {
//						double detailItemAmount = detail.optDouble("item_amount");
//						if (detailItemAmount == 0d)
//						{
//							detailItemAmount = DoubleHelper.add(DoubleHelper.mul(detail.optDouble("item_price"), detail.optDouble("item_count"), defaultScale), DoubleHelper.add(detail.optDouble("method_money"), detail.optDouble("assist_money"), defaultScale), defaultScale);
//						}
//						// 套餐明细折扣金额
//						double discountAmount = DoubleHelper.mul(detailItemAmount, rate, scale);
//						// 套餐明细的折扣金额之和
//						detailSumAmount = DoubleHelper.add(detailSumAmount, discountAmount, scale);
//						// 套餐中明细金额最大的
//						if(dMaxId == null || detailItemAmount > dItemMaxAmount ){
//							dMaxId = detail.optInt("id");
//							dItemMaxAmount = detailItemAmount;
//						}
//
//						JSONObject itemA = new JSONObject();
//						itemA.put("id", detail.optInt("id"));
//						itemA.put("discount_amount", discountAmount);
//						detailList.add(itemA);
//					}
				}
				
				for(JSONObject detail: detailList){
					if(detail.optInt("id") == dMaxId){
						// 套餐主项折扣-套餐明细项之和=折扣的误差
						double detailItemAmount = DoubleHelper.add(detail.optDouble("discount_amount"), DoubleHelper.sub(DoubleHelper.mul(itemAmount, rate, scale), detailSumAmount, scale), scale);
						
						batchArgs.add(new Object[]
								{ itemDiscountRate, detailItemAmount, detail.optInt("id") });
					}else{
						batchArgs.add(new Object[]
								{ itemDiscountRate, detail.optDouble("discount_amount"), detail.optInt("id") });
					}
				}
				
			}
		}
		return batchArgs;
	}

	/**
	 * 计算会员价,把会员价的差价放到优惠金额里
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @throws Exception
	 */
	@SuppressWarnings("unused")
	@Deprecated
	private void updateDiscountkVipPriceAmount(String tenantId, int storeId, String billNum, int scale) throws Exception
	{
		final int defaultScale = 4;

		StringBuilder queryBillItemSql = new StringBuilder();
		queryBillItemSql.append(" select bi.id,bi.item_id,bi.setmeal_id,bi.item_serial,bi.setmeal_rwid,bi.item_property,bi.item_remark,bi.item_price,bi.assist_money,bi.method_money,bi.item_count,bi.item_amount,coalesce(bi.third_price,0) as third_price,bi.discount_mode_id");
		queryBillItemSql.append(" from pos_bill_item bi where bi.bill_num=? and bi.store_id = ?");

		List<JSONObject> itemList = baseDao.query4Json(tenantId, queryBillItemSql.toString(), new Object[]
		{ billNum, storeId });
		List<Object[]> batchArgs = new ArrayList<Object[]>();
		for (JSONObject item : itemList)
		{
			if(SysDictionary.DISCOUNT_MODE_10!=item.optInt("discount_mode_id"))
			{
				if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.optString("item_property")))
				{
					double discountAmount = 0;
					if (item.optDouble("third_price") > 0 && item.optDouble("item_price") > item.optDouble("third_price"))
					{
						discountAmount = DoubleHelper.mul(DoubleHelper.sub(item.optDouble("item_price"), item.optDouble("third_price"), defaultScale), item.optDouble("item_count"), scale);
					}
	
					if (SysDictionary.ITEM_REMARK_TC01.equalsIgnoreCase(item.optString("item_remark")) || SysDictionary.ITEM_REMARK_FS02.equalsIgnoreCase(item.optString("item_remark")) || SysDictionary.ITEM_REMARK_CJ05.equalsIgnoreCase(item.optString("item_remark")))
					{
						discountAmount = 0d;
					}
					batchArgs.add(new Object[]
					{ 100d, discountAmount, item.optInt("id") });
	
					if (discountAmount > 0)
					{
						if (SysDictionary.ITEM_PROPERTY_SETMEAL.equals(item.optString("item_property")))
						{ // 将减免金额平摊到套餐明细
							Integer dMaxId = null;
							double dmaxAmount = 0d;
							double sumAmount = 0d;
	
							List<JSONObject> detailList = new ArrayList<JSONObject>();
							for (JSONObject detail : itemList)
							{
								if (SysDictionary.ITEM_PROPERTY_MEALLIST.equals(detail.optString("item_property")) && item.optInt("item_id") == detail.optInt("setmeal_id") && item.optInt("item_serial") == detail.optInt("setmeal_rwid"))
								{
									detailList.add(detail);
									double detailDiscountAmount = DoubleHelper.mul(DoubleHelper.div(detail.optDouble("item_amount"), item.optDouble("item_amount"), defaultScale), discountAmount, scale);
									sumAmount = DoubleHelper.add(sumAmount, detailDiscountAmount, defaultScale);
	
									if (null == dMaxId || dmaxAmount < detail.optDouble("item_amount"))
									{
										dMaxId = detail.optInt("id");
										dmaxAmount = detail.optDouble("item_amount");
									}
								}
							}
	
							for (JSONObject detail : detailList)
							{
								double detailDiscountAmount = DoubleHelper.mul(DoubleHelper.div(detail.optDouble("item_amount"), item.optDouble("item_amount"), defaultScale), discountAmount, scale);
								if (dMaxId == detail.optInt("id"))
								{
									detailDiscountAmount = DoubleHelper.add(detailDiscountAmount, DoubleHelper.sub(discountAmount, sumAmount, defaultScale), scale);
								}
								batchArgs.add(new Object[]
								{ 100d, detailDiscountAmount, detail.optInt("id") });
							}
						}
					}
				}
			}
		}
		if (batchArgs.size() > 0)
		{
			StringBuilder updateBillItemSql = new StringBuilder("update pos_bill_item set discount_rate=?,discount_amount=? where id = ?");
			baseDao.batchUpdate(updateBillItemSql.toString(), batchArgs);
		}
	}
	
	/** 第三方优惠平摊
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @param discountrAmount
	 * @param scale
	 * @throws Exception
	 */
	private void updateDiscountkAmount(String tenantId, int storeId, String billNum, double discountrAmount, int scale) throws Exception
	{
//		final int defaultScale = 4;

		StringBuilder queryItemSql = new StringBuilder(
				"select id,item_id,setmeal_id,item_serial,setmeal_rwid,item_property,item_amount,item_count,return_count from pos_bill_item where coalesce(item_remark,'')<>'TC01' and coalesce(item_remark,'')<>'FS02' and coalesce(item_remark,'')<>'CJ05' and bill_num = ? and store_id = ?");
		List<JSONObject> itemList = baseDao.query4Json(tenantId, queryItemSql.toString(), new Object[]
		{ billNum, storeId });

		double averageTotal = 0d;
		double subtotal = 0d; // 统计所有菜的折让金额
		Integer maxId = 0;
		double maxValue = 0d;

		// 取总钱数,最大面额和对应的rwid
		for (JSONObject item : itemList)
		{
			double itemAmount = item.optDouble("item_amount");
			if (Tools.hv(item.opt("return_count")) && item.optDouble("return_count") > 0d)
			{
				double itemPrice = DoubleHelper.div(item.optDouble("item_amount"), item.optDouble("item_count"), defaultScale);
				itemAmount = DoubleHelper.mul(itemPrice, DoubleHelper.sub(item.optDouble("item_count"), item.optDouble("return_count"), defaultScale), scale);
				item.put("item_amount", itemAmount);
			}

			if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.optString("item_property")))
			{
				subtotal = DoubleHelper.add(subtotal, itemAmount, defaultScale);

				if (maxValue < itemAmount)
				{
					maxValue = itemAmount;
					maxId = item.optInt("id");
				}
			}
		}

		// 计算并平摊折让的钱
		for (JSONObject item : itemList)
		{
			if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.optString("item_property")))
			{
				double itemDiscountrAmount = DoubleHelper.mul(DoubleHelper.div(item.optDouble("item_amount"), subtotal, defaultScale), discountrAmount, scale);
				averageTotal = DoubleHelper.add(averageTotal, itemDiscountrAmount, defaultScale);
			}
		}

		List<Object[]> batchArgs = new ArrayList<Object[]>();
		for (JSONObject item : itemList)
		{
			if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.optString("item_property")))
			{
				double itemDiscountrAmount = DoubleHelper.mul(DoubleHelper.div(item.optDouble("item_amount"), subtotal, defaultScale), discountrAmount, scale);
				if (maxId == item.optInt("id"))
				{
					itemDiscountrAmount = DoubleHelper.add(itemDiscountrAmount, DoubleHelper.sub(discountrAmount, averageTotal, defaultScale), scale);
				}
				batchArgs.add(new Object[]
				{ itemDiscountrAmount, item.optInt("id") });

				if (SysDictionary.ITEM_PROPERTY_SETMEAL.equals(item.optString("item_property")))
				{ // 将减免金额平摊到套餐明细
					Integer dMaxId = null;
					double dmaxAmount = 0d;
					double sumAmount = 0d;

					List<JSONObject> detailList = new ArrayList<JSONObject>();
					for (JSONObject detail : itemList)
					{
						if (SysDictionary.ITEM_PROPERTY_MEALLIST.equals(detail.optString("item_property")) && item.optInt("item_id") == detail.optInt("setmeal_id") && item.optInt("item_serial") == detail.optInt("setmeal_rwid"))
						{
							detailList.add(detail);
							double detailDiscountrAmount = DoubleHelper.mul(DoubleHelper.div(detail.optDouble("item_amount"), item.optDouble("item_amount"), defaultScale), itemDiscountrAmount, scale);
							sumAmount = DoubleHelper.add(sumAmount, detailDiscountrAmount, defaultScale);

							if (null == dMaxId || dmaxAmount < detail.optDouble("item_amount"))
							{
								dMaxId = detail.optInt("id");
								dmaxAmount = detail.optDouble("item_amount");
							}
						}
					}

					for (JSONObject detail : detailList)
					{
						double detailDiscountrAmount = DoubleHelper.mul(DoubleHelper.div(detail.optDouble("item_amount"), item.optDouble("item_amount"), defaultScale), itemDiscountrAmount, scale);
						if (dMaxId == detail.optInt("id"))
						{
							detailDiscountrAmount = DoubleHelper.add(detailDiscountrAmount, DoubleHelper.sub(itemDiscountrAmount, sumAmount, defaultScale), scale);
						}
						batchArgs.add(new Object[]
						{ detailDiscountrAmount, detail.optInt("id") });
					}
				}
			}
		}
		// 更新折扣金额
		if (batchArgs.size() > 0)
		{
			baseDao.batchUpdate("update pos_bill_item set discount_amount = ? where id = ?", batchArgs);
		}
	}
	
	/**
	 * 计算折扣减免金额 奉送菜品折扣金额为0
	 * 
	 * @param item
	 * @param itemList
	 * @param scale
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("unused")
	@Deprecated
	private List<Object[]> getDiscountDerateAmount2(JSONObject item, List<JSONObject> itemList, int scale) throws Exception
	{
		final int defaultScale = 4;
		List<Object[]> batchArgs = new ArrayList<Object[]>();
		if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.optString("item_property")))
		{
			// 减免金额
			double itemDiscountAmount = DoubleHelper.mul(item.optDouble("derate"), item.optDouble("item_count"), scale);
			// 奉送菜品折扣金额为0
			if (SysDictionary.ITEM_REMARK_TC01.equalsIgnoreCase(item.optString("item_remark")) || SysDictionary.ITEM_REMARK_FS02.equalsIgnoreCase(item.optString("item_remark")) || SysDictionary.ITEM_REMARK_CJ05.equalsIgnoreCase(item.optString("item_remark")))
			{
				itemDiscountAmount = 0d;
			}
			
			double discountAmount = item.optDouble("discount_amount");
			batchArgs.add(new Object[]
			{ 100d, DoubleHelper.add(discountAmount, itemDiscountAmount, scale) , item.optInt("id") });

			if (SysDictionary.ITEM_PROPERTY_SETMEAL.equals(item.optString("item_property")))
			{ // 将减免金额平摊到套餐明细
				Integer maxId = null;
				double maxAmount = 0d;
				double sumItemdiscountAmount = 0d;
				double proportion = DoubleHelper.div(itemDiscountAmount, item.optDouble("item_amount"), defaultScale);

				List<JSONObject> detailList = new ArrayList<JSONObject>();
				for (JSONObject detail : itemList)
				{
					if (SysDictionary.ITEM_PROPERTY_MEALLIST.equals(detail.optString("item_property")) && item.optInt("item_id") == detail.optInt("setmeal_id") && item.optInt("item_serial") == detail.optInt("setmeal_rwid"))
					{
						double detailDiscountAmount = DoubleHelper.mul(detail.optDouble("item_amount"), proportion, scale);
						// 奉送菜品折扣金额为0
						if (SysDictionary.ITEM_REMARK_TC01.equalsIgnoreCase(detail.optString("item_remark")) || SysDictionary.ITEM_REMARK_FS02.equalsIgnoreCase(detail.optString("item_remark")) || SysDictionary.ITEM_REMARK_CJ05.equalsIgnoreCase(detail.optString("item_remark")))
						{
							detailDiscountAmount = 0d;
						}
						sumItemdiscountAmount = DoubleHelper.add(sumItemdiscountAmount, detailDiscountAmount, defaultScale);

						detail.put("discount_rate", 100);
						detail.put("discount_amount", detailDiscountAmount);
						detailList.add(detail);

						if (null == maxId || maxAmount < detail.optDouble("item_amount"))
						{
							maxId = detail.optInt("id");
							maxAmount = detail.optDouble("item_amount");
						}
					}
				}

				for (JSONObject detail : detailList)
				{
					double detailDiscountAmount = DoubleHelper.mul(detail.optDouble("item_amount"), proportion, scale);
					if (maxId == detail.optInt("id"))
					{
						detailDiscountAmount = DoubleHelper.add(detailDiscountAmount, DoubleHelper.sub(itemDiscountAmount, sumItemdiscountAmount, defaultScale), scale);
					}
					// 奉送菜品折扣金额为0
					if (SysDictionary.ITEM_REMARK_TC01.equalsIgnoreCase(detail.optString("item_remark")) || SysDictionary.ITEM_REMARK_FS02.equalsIgnoreCase(detail.optString("item_remark")) || SysDictionary.ITEM_REMARK_CJ05.equalsIgnoreCase(detail.optString("item_remark")))
					{
						detailDiscountAmount = 0d;
					}
					
					batchArgs.add(new Object[]
					{ 100d, DoubleHelper.add(detail.optDouble("discount_amount"), detailDiscountAmount, scale), detail.optInt("id") });
				}
			}
		}
		return batchArgs;
	}
	
	
	/**
	 * 计算折扣率金额 奉送菜品折扣金额为0
	 * 
	 * @param item
	 * @param itemList
	 * @param itemDiscountRate
	 * @param scale
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("unused")
	@Deprecated
	private List<Object[]> getDiscountRateAmount2(JSONObject item, List<JSONObject> itemList, double itemDiscountRate, int scale) throws Exception
	{
		final int defaultScale = 4;
		List<Object[]> batchArgs = new ArrayList<Object[]>();
		if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.optString("item_property")))
		{
			if (SysDictionary.ITEM_REMARK_TC01.equals(item.optString("item_remark")) || SysDictionary.ITEM_REMARK_FS02.equals(item.optString("item_remark")) || SysDictionary.ITEM_REMARK_CJ05.equals(item.optString("item_remark")))
			{
				itemDiscountRate = 100d;
			}

			double rate = DoubleHelper.sub(1d, DoubleHelper.div(itemDiscountRate, 100d, defaultScale), defaultScale);
			double itemAmount = item.optDouble("item_amount");
			double discountAmount = item.optDouble("discount_amount");
			double discountrAmount = item.optDouble("discountr_amount");
			
			itemAmount = DoubleHelper.sub(itemAmount, DoubleHelper.add(discountAmount, discountrAmount, scale), scale);
			
			batchArgs.add(new Object[]
			{ itemDiscountRate, DoubleHelper.add(discountAmount, DoubleHelper.mul(itemAmount, rate, scale), scale), item.optInt("id") });

			if (SysDictionary.ITEM_PROPERTY_SETMEAL.equalsIgnoreCase(item.optString("item_property")))
			{
				for (JSONObject detail : itemList)
				{
					if (SysDictionary.ITEM_PROPERTY_MEALLIST.equals(detail.optString("item_property")) && item.optInt("item_id") == detail.optInt("setmeal_id") && item.optInt("item_serial") == detail.optInt("setmeal_rwid"))
					{
						double detailItemAmount = detail.optDouble("item_amount");
						double detailDiscountAmount = detail.optDouble("discount_amount");
						double detailDiscountrAmount = detail.optDouble("discountr_amount");
						
						detailItemAmount = DoubleHelper.sub(detailItemAmount, DoubleHelper.add(detailDiscountAmount, detailDiscountrAmount, scale), scale);
						
						batchArgs.add(new Object[]
						{ itemDiscountRate, DoubleHelper.add(detailDiscountAmount, DoubleHelper.mul(detailItemAmount, rate, scale), scale) , detail.optInt("id") });
					}
				}
			}
		}
		return batchArgs;
	}

	/*
	 * 计算使用活动的平摊金额
	 * */
	private void updateHdDiscountrAmount(String activityType,String tenantId, int storeId, String billNum,String activityBatchNum, double discountrAmount, int scale) throws Exception
	{
//		final int defaultScale = 4;

		StringBuilder queryItemSql = new StringBuilder(
				"select id,item_id,setmeal_id,item_serial,setmeal_rwid,item_property,item_remark,item_amount,item_count,return_count from pos_bill_item where " +
						"coalesce(item_remark,'')<>'TC01' and coalesce(item_remark,'')<>'FS02' and coalesce(item_remark,'')<>'QX04' and coalesce(item_remark,'')<>'CJ05' and coalesce(item_remark,'')<>'CJ06' and tenancy_id=? and store_id = ? and bill_num = ? and activity_batch_num=?");
		List<JSONObject> itemList = baseDao.query4Json(tenantId, queryItemSql.toString(), new Object[]
				{ tenantId, storeId, billNum, activityBatchNum });

		double averageTotal = 0d;
		double subtotal = 0d; // 统计所有菜的折让金额
		Integer maxId = 0;
		double maxValue = 0d;
		List<Object[]> batchArgs = new ArrayList<Object[]>();
		if(SysDictionary.MARKETING_ACTIVITY_MEJXJ.equals(activityType)
                || SysDictionary.MARKETING_ACTIVITY_MEDZ.equals(activityType)){
			// 取总钱数,最大面额和对应的rwid
			for (JSONObject item : itemList)
			{
				double itemAmount = item.optDouble("item_amount");
				if (Tools.hv(item.opt("return_count")) && item.optDouble("return_count") > 0d)
				{
					double itemPrice = DoubleHelper.div(item.optDouble("item_amount"), item.optDouble("item_count"), defaultScale);
					itemAmount = DoubleHelper.mul(itemPrice, DoubleHelper.sub(item.optDouble("item_count"), item.optDouble("return_count"), defaultScale), scale);
					item.put("item_amount", itemAmount);
				}

				if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.optString("item_property")))
				{
					subtotal = DoubleHelper.add(subtotal, itemAmount, defaultScale);

					if (maxValue < itemAmount)
					{
						maxValue = itemAmount;
						maxId = item.optInt("id");
					}
				}
			}

			// 计算并平摊折让的钱
			for (JSONObject item : itemList)
			{
				if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.optString("item_property")))
				{
					double itemDiscountrAmount = DoubleHelper.mul(DoubleHelper.div(item.optDouble("item_amount"), subtotal, defaultScale), discountrAmount, scale);
					averageTotal = DoubleHelper.add(averageTotal, itemDiscountrAmount, defaultScale);
				}
			}

			for (JSONObject item : itemList)
			{
				if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.optString("item_property")))
				{
					double itemDiscountrAmount = DoubleHelper.mul(DoubleHelper.div(item.optDouble("item_amount"), subtotal, defaultScale), discountrAmount, scale);
					if (maxId == item.optInt("id"))
					{
						itemDiscountrAmount = DoubleHelper.add(itemDiscountrAmount, DoubleHelper.sub(discountrAmount, averageTotal, defaultScale), scale);
					}
					batchArgs.add(new Object[]
							{ itemDiscountrAmount, item.optInt("id") });

					if (SysDictionary.ITEM_PROPERTY_SETMEAL.equals(item.optString("item_property")))
					{ // 将减免金额平摊到套餐明细
						Integer dMaxId = null;
						double dmaxAmount = 0d;
						double sumAmount = 0d;

						List<JSONObject> detailList = new ArrayList<JSONObject>();
						for (JSONObject detail : itemList)
						{
							if (SysDictionary.ITEM_PROPERTY_MEALLIST.equals(detail.optString("item_property")) && item.optInt("item_id") == detail.optInt("setmeal_id") && item.optInt("item_serial") == detail.optInt("setmeal_rwid"))
							{
								detailList.add(detail);
								double detailDiscountrAmount = DoubleHelper.mul(DoubleHelper.div(detail.optDouble("item_amount"), item.optDouble("item_amount"), defaultScale), itemDiscountrAmount, scale);
								sumAmount = DoubleHelper.add(sumAmount, detailDiscountrAmount, defaultScale);

								if (null == dMaxId || dmaxAmount < detail.optDouble("item_amount"))
								{
									dMaxId = detail.optInt("id");
									dmaxAmount = detail.optDouble("item_amount");
								}
							}
						}

						for (JSONObject detail : detailList)
						{
							double detailDiscountrAmount = DoubleHelper.mul(DoubleHelper.div(detail.optDouble("item_amount"), item.optDouble("item_amount"), defaultScale), itemDiscountrAmount, scale);
							if (dMaxId == detail.optInt("id"))
							{
								detailDiscountrAmount = DoubleHelper.add(detailDiscountrAmount, DoubleHelper.sub(itemDiscountrAmount, sumAmount, defaultScale), scale);
							}
							batchArgs.add(new Object[]
									{ detailDiscountrAmount, detail.optInt("id") });
						}
					}
				}
			}
		}else if(SysDictionary.MARKETING_ACTIVITY_MEJJG.equals(activityType)
                || SysDictionary.MARKETING_ACTIVITY_MEZC.equals(activityType)
                || SysDictionary.MARKETING_ACTIVITY_ZHTYJ.equals(activityType)
                || SysDictionary.MARKETING_ACTIVITY_DEFBJ.equals(activityType)){
			for (JSONObject item : itemList)
			{
				double itemAmount = item.optDouble("item_amount");
				if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.optString("item_property")))
				{
					if(item.optString("item_remark").equals(SysDictionary.ITEM_REMARK_HDTYJ10)
                            || item.optString("item_remark").equals(SysDictionary.ITEM_REMARK_HDZC08)
                            || item.optString("item_remark").equals(SysDictionary.ITEM_REMARK_HDBJ09)){
						subtotal = DoubleHelper.add(subtotal, itemAmount, defaultScale);
						if (maxValue < itemAmount)
						{
							maxValue = itemAmount;
							maxId = item.optInt("id");
						}
					}
				}
			}
			for (JSONObject item : itemList)
			{
				if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.optString("item_property")))
				{
					if(item.optString("item_remark").equals(SysDictionary.ITEM_REMARK_HDTYJ10)
                            || item.optString("item_remark").equals(SysDictionary.ITEM_REMARK_HDZC08)
                            || item.optString("item_remark").equals(SysDictionary.ITEM_REMARK_HDBJ09)){
						double itemDiscountrAmount = DoubleHelper.mul(DoubleHelper.div(item.optDouble("item_amount"), subtotal, defaultScale), discountrAmount, scale);
						averageTotal = DoubleHelper.add(averageTotal, itemDiscountrAmount, defaultScale);
					}
				}
			}
			for (JSONObject item : itemList)
			{
				if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.optString("item_property")))
				{
					if(item.optString("item_remark").equals(SysDictionary.ITEM_REMARK_HDTYJ10)
                            || item.optString("item_remark").equals(SysDictionary.ITEM_REMARK_HDZC08)
                            || item.optString("item_remark").equals(SysDictionary.ITEM_REMARK_HDBJ09)){
						double itemDiscountrAmount = DoubleHelper.mul(DoubleHelper.div(item.optDouble("item_amount"), subtotal, defaultScale), discountrAmount, scale);
						//Add By SunFumeng 为什么要屏蔽掉，没有看到。我觉得还是应该放开的 2017-8-11
						/*if (maxId == item.optInt("id"))
						{
							itemDiscountrAmount = DoubleHelper.add(itemDiscountrAmount, DoubleHelper.sub(discountrAmount, averageTotal, defaultScale), scale);
						}*/
						if (maxId == item.optInt("id"))
						{
							itemDiscountrAmount = DoubleHelper.add(itemDiscountrAmount, DoubleHelper.sub(discountrAmount, averageTotal, defaultScale), scale);
						}
						batchArgs.add(new Object[]
								{ itemDiscountrAmount, item.optInt("id") });
					}
				}
			}
		}
		// 更新折让金额
		if (batchArgs.size() > 0)
		{
			baseDao.batchUpdate("update pos_bill_item set discountr_amount = ? where id = ?", batchArgs);
		}
	}

	/**
	 * 获得折让金额discountr_amount然后分摊到各个明细里 奉送菜品折让金额为0 退菜菜品折让金额为0
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @param discountrAmount
	 * @param scale
	 * @throws Exception
	 */
	private void updateDiscountrAmount(String tenantId, int storeId, String billNum, double discountrAmount,Integer discountModeId, Integer discountReasonId, Double discountRate, String discountNum, int scale) throws Exception
	{
//		final int defaultScale = 4;
		StringBuilder queryItemSql = new StringBuilder(
				"select id,item_id,setmeal_id,item_serial,setmeal_rwid,item_property,item_amount,item_count,return_count,coalesce(discount_amount,0) as discount_amount,combo_prop,discount_mode_id,discount_num,discount_rate,discount_reason_id from pos_bill_item where coalesce(item_remark,'')<>? and coalesce(item_remark,'')<>? and coalesce(item_remark,'')<>? and bill_num = ? and store_id = ? ");
		List<JSONObject> itemList = baseDao.query4Json(tenantId, queryItemSql.toString(), new Object[]
		{ SysDictionary.ITEM_REMARK_TC01, SysDictionary.ITEM_REMARK_FS02, SysDictionary.ITEM_REMARK_CJ05, billNum, storeId });

		double subtotalNoDiscount = 0d;
		double subtotalDiscount = 0d;// 统计所有菜的折让金额
		// 取总钱数,最大面额和对应的rwid
		for (JSONObject item : itemList)
		{
			double itemAmount = item.optDouble("item_amount");
			if (Tools.hv(item.opt("return_count")) && item.optDouble("return_count") > 0d)
			{
				double itemPrice = DoubleHelper.div(item.optDouble("item_amount"), item.optDouble("item_count"), defaultScale);
				itemAmount = DoubleHelper.mul(itemPrice, DoubleHelper.sub(item.optDouble("item_count"), item.optDouble("return_count"), defaultScale), scale);
				item.put("item_amount", itemAmount);
			}

			if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.optString("item_property")))
			{				
				double realAmount = DoubleHelper.sub(itemAmount, item.optDouble("discount_amount"), scale);
				subtotalNoDiscount = DoubleHelper.add(subtotalNoDiscount, realAmount, defaultScale);
				if(Tools.hv(item.opt("discount_mode_id")) && 0<item.optInt("discount_mode_id"))
				{
					subtotalDiscount = DoubleHelper.add(subtotalDiscount, realAmount, defaultScale);
				}
			}
		}
		
		boolean isDiscount = false;// 折让是否平摊到未打折菜品
		Double subtotal = subtotalDiscount;
		if (discountrAmount > subtotalDiscount)
		{
			isDiscount = true;
			subtotal = subtotalNoDiscount;
		}

		// 计算并平摊折让的钱
		double averageTotal = 0d;
		Integer maxId = 0;
		double maxValue = 0d;

		String itemProperty = null;
		Integer itemDiscountModeId = null;
		Double itemAmount = 0d;
		Double itemDiscountAmount = 0d;
		for (JSONObject item : itemList)
		{
			itemProperty = item.optString("item_property");
			itemAmount = item.optDouble("item_amount");
			itemDiscountModeId = item.optInt("discount_mode_id");
			itemDiscountAmount = item.optDouble("discount_amount");
			if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(itemProperty) && ((Tools.hv(itemDiscountModeId) && 0 < itemDiscountModeId && 0 < itemDiscountAmount) || isDiscount))
			{
				double realAmoount = DoubleHelper.sub(itemAmount, itemDiscountAmount, scale);
				double itemDiscountrAmount = DoubleHelper.mul(DoubleHelper.div(realAmoount, subtotal, defaultScale), discountrAmount, scale);
				averageTotal = DoubleHelper.add(averageTotal, itemDiscountrAmount, defaultScale);

				if (maxValue < itemAmount)
				{
					maxValue = itemAmount;
					maxId = item.optInt("id");
				}
			}
		}
 
		Double itemDiscountRate = 100d;
		Integer itemDiscountReasonId = 0;
		String itemDiscountNum = null;
		List<Object[]> batchArgs = new ArrayList<Object[]>();
		for (JSONObject item : itemList)
		{
			itemDiscountModeId = ParamUtil.getIntegerValueByObject(item, "discount_mode_id");
			itemProperty = ParamUtil.getStringValueByObject(item, "item_property");
			itemDiscountRate = ParamUtil.getDoubleValueByObject(item, "discount_rate");
			itemDiscountReasonId = ParamUtil.getIntegerValueByObject(item, "discount_reason_id");
			itemDiscountNum = ParamUtil.getStringValueByObject(item, "discount_num");
			if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(itemProperty) && ((Tools.hv(item.opt("discount_mode_id")) && 0 < item.optInt("discount_mode_id") && 0 < item.optDouble("discount_amount")) || isDiscount))
			{
				double realAmoount = DoubleHelper.sub(item.optDouble("item_amount"), item.optDouble("discount_amount"), scale);
				double itemDiscountrAmount = DoubleHelper.mul(DoubleHelper.div(realAmoount, subtotal, defaultScale), discountrAmount, scale);
				if (maxId == item.optInt("id"))
				{
					itemDiscountrAmount = DoubleHelper.add(itemDiscountrAmount, DoubleHelper.sub(discountrAmount, averageTotal, defaultScale), scale);
				}
				if (Tools.hv(itemDiscountModeId) && 0 < itemDiscountModeId)
				{
					batchArgs.add(new Object[]
					{ itemDiscountrAmount, itemDiscountRate, itemDiscountModeId,itemDiscountReasonId , itemDiscountNum, item.optInt("id") });
				}
				else if (0 < itemDiscountrAmount)
				{
					batchArgs.add(new Object[]
					{ itemDiscountrAmount, discountRate, discountModeId, discountReasonId, discountNum, item.optInt("id") });
				}
				else
				{
					batchArgs.add(new Object[]
					{ itemDiscountrAmount, 100d, 0, 0, null, item.optInt("id") });
				}

				if (SysDictionary.ITEM_PROPERTY_SETMEAL.equals(itemProperty))
				{ // 将减免金额平摊到套餐明细
					Integer dMaxId = null;
					double dmaxAmount = 0d;
					double sumAmount = 0d;

					List<JSONObject> detailList = new ArrayList<JSONObject>();
					for (JSONObject detail : itemList)
					{
						if (SysDictionary.ITEM_PROPERTY_MEALLIST.equals(detail.optString("item_property")) && item.optInt("item_id") == detail.optInt("setmeal_id") && item.optInt("item_serial") == detail.optInt("setmeal_rwid"))
						{
							detailList.add(detail);
							double detailDiscountrAmount = DoubleHelper.mul(DoubleHelper.div(detail.optDouble("item_amount"), item.optDouble("item_amount"), defaultScale), itemDiscountrAmount, scale);
							sumAmount = DoubleHelper.add(sumAmount, detailDiscountrAmount, defaultScale);

							if (null == dMaxId || dmaxAmount < detail.optDouble("item_amount"))
							{
								dMaxId = detail.optInt("id");
								dmaxAmount = detail.optDouble("item_amount");
							}
						}
//						// 组合套餐
//						else if (SysDictionary.ITEM_PROPERTY_MEALLIST.equals(detail.optString("item_property")) && item.optInt("item_id") == detail.optInt("setmeal_id") && item.optInt("combo_prop") > 0)
//						{
//							detailList.add(detail);
//							double detailDiscountrAmount = DoubleHelper.mul(DoubleHelper.div(detail.optDouble("item_amount"), item.optDouble("item_amount"), defaultScale), itemDiscountrAmount, scale);
//							sumAmount = DoubleHelper.add(sumAmount, detailDiscountrAmount, defaultScale);
//
//							if (null == dMaxId || dmaxAmount < detail.optDouble("item_amount"))
//							{
//								dMaxId = detail.optInt("id");
//								dmaxAmount = detail.optDouble("item_amount");
//							}
//						}
					}

					for (JSONObject detail : detailList)
					{
						double detailDiscountrAmount = DoubleHelper.mul(DoubleHelper.div(detail.optDouble("item_amount"), item.optDouble("item_amount"), defaultScale), itemDiscountrAmount, scale);
						if (dMaxId == detail.optInt("id"))
						{
							detailDiscountrAmount = DoubleHelper.add(detailDiscountrAmount, DoubleHelper.sub(itemDiscountrAmount, sumAmount, defaultScale), scale);
						}
						
						if (Tools.hv(itemDiscountModeId) && 0 < itemDiscountModeId)
						{
							batchArgs.add(new Object[]
							{ detailDiscountrAmount, itemDiscountRate, itemDiscountModeId,itemDiscountReasonId , itemDiscountNum, detail.optInt("id") });
						}
						else if (0 < detailDiscountrAmount)
						{
							batchArgs.add(new Object[]
							{ detailDiscountrAmount, discountRate, discountModeId, discountReasonId, discountNum, detail.optInt("id") });
						}
						else
						{
							batchArgs.add(new Object[]
							{ detailDiscountrAmount, 100d, 0, 0, null, detail.optInt("id") });
						}
					}
				}
			}
		}
		// 更新折让金额
		if (batchArgs.size() > 0)
		{
			baseDao.batchUpdate("update pos_bill_item set discountr_amount = ?, discount_rate=?, discount_mode_id=?, discount_reason_id=?, discount_num=? where id = ?", batchArgs);
		}
	}

	/**
	 * 修改抹零金额,放到菜目金额最大的菜品上 奉送菜品抹零平摊金额为0 退菜菜品抹零平摊金额为0
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @param scrapAmount
	 * @throws Exception
	 */
	@SuppressWarnings("unused")
	@Deprecated
	private void updateScrapAmount(String tenantId, int storeId, String billNum, double scrapAmount) throws Exception
	{
		StringBuilder queryItemSql = new StringBuilder(
				"select id,item_id,setmeal_id,item_serial,setmeal_rwid,item_property,item_amount from pos_bill_item where coalesce(item_remark,'')<>'TC01' and coalesce(item_remark,'')<>'FS02' and coalesce(item_remark,'')<>'CJ05' and bill_num = ? and store_id = ?");
		List<JSONObject> itemList = baseDao.query4Json(tenantId, queryItemSql.toString(), new Object[]
		{ billNum, storeId });

		if (itemList != null && itemList.size() > 0)
		{
			JSONObject maxJson = null;
			for (JSONObject item : itemList)
			{
				if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.optString("item_property")))
				{
					if (null == maxJson || maxJson.optDouble("item_amount") < item.optDouble("item_amount"))
					{
						maxJson = item;
					}
				}
			}
			StringBuilder updateItemSql = new StringBuilder("update pos_bill_item set single_discount_amount=? where id = ?");
			if (Tools.hv(maxJson))
			{
				baseDao.update(updateItemSql.toString(), new Object[]
				{ scrapAmount, maxJson.optInt("id") });
			}

			if (SysDictionary.ITEM_PROPERTY_SETMEAL.equalsIgnoreCase(maxJson.optString("item_property")))
			{
				JSONObject detailMaxJson = null;
				for (JSONObject detail : itemList)
				{
					if (SysDictionary.ITEM_PROPERTY_MEALLIST.equals(detail.optString("item_property")) && maxJson.optInt("item_id") == detail.optInt("setmeal_id") && maxJson.optInt("item_serial") == detail.optInt("setmeal_rwid"))
					{
						if (null == detailMaxJson || detailMaxJson.optDouble("item_amount") < detail.optDouble("item_amount"))
						{
							detailMaxJson = detail;
						}
					}
				}
				if (Tools.hv(detailMaxJson))
				{
					baseDao.update(updateItemSql.toString(), new Object[]
					{ scrapAmount, detailMaxJson.optInt("id") });
				}
			}
		}	
	}
	
	private void updateScrapAmount(String tenantId, int storeId, String billNum, double scrapAmount, int scale) throws Exception
	{
		StringBuilder queryItemSql = new StringBuilder(
				"select id,item_id,setmeal_id,item_serial,setmeal_rwid,item_property,item_amount,coalesce(discount_amount,0) as discount_amount,coalesce(discountr_amount,0) as discountr_amount,discount_mode_id from pos_bill_item where coalesce(item_remark,'')<>? and coalesce(item_remark,'')<>? and coalesce(item_remark,'')<>? and bill_num = ? and store_id = ?");
		List<JSONObject> itemList = baseDao.query4Json(tenantId, queryItemSql.toString(), new Object[]
		{ SysDictionary.ITEM_REMARK_TC01, SysDictionary.ITEM_REMARK_FS02, SysDictionary.ITEM_REMARK_CJ05, billNum, storeId });

		double subtotalDiscount = 0d;// 统计所有菜的折让金额
		// 取总钱数,最大面额和对应的rwid
		for (JSONObject item : itemList)
		{
			double itemAmount = item.optDouble("item_amount");
			if (Tools.hv(item.opt("return_count")) && item.optDouble("return_count") > 0d)
			{
				double itemPrice = DoubleHelper.div(item.optDouble("item_amount"), item.optDouble("item_count"), defaultScale);
				itemAmount = DoubleHelper.mul(itemPrice, DoubleHelper.sub(item.optDouble("item_count"), item.optDouble("return_count"), defaultScale), scale);
				item.put("item_amount", itemAmount);
			}

			if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.optString("item_property")))
			{
				double realAmount = DoubleHelper.sub(itemAmount, DoubleHelper.add(item.optDouble("discount_amount"), item.optDouble("discountr_amount"), defaultScale), scale);
				if (Tools.hv(item.opt("discount_mode_id")) && 0 < item.optInt("discount_mode_id"))
				{
					subtotalDiscount = DoubleHelper.add(subtotalDiscount, realAmount, defaultScale);
				}
			}
		}

		boolean isDiscount = false;// 抹零是否平摊到未打折菜品
		if (scrapAmount > subtotalDiscount || 0 == subtotalDiscount)
		{
			isDiscount = true;
		}
		
		JSONObject maxJson = null;
		Double discountAmount = 0d;
		List<Integer> maxIdList = new ArrayList<Integer>();
		for(int i=0;i<itemList.size();i++)
		{
			maxJson = null;
			for (JSONObject item : itemList)
			{
				discountAmount = DoubleHelper.add(item.optDouble("discount_amount"), item.optDouble("discountr_amount"), defaultScale);
				if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.optString("item_property")) && ((Tools.hv(item.opt("discount_mode_id")) && 0 < item.optInt("discount_mode_id") && 0 < discountAmount) || isDiscount))
				{
					double maxRealAmount = 0d;
					if (null != maxJson)
					{
						maxRealAmount = DoubleHelper.sub(maxJson.optDouble("item_amount"), DoubleHelper.add(maxJson.optDouble("discount_amount"), maxJson.optDouble("discountr_amount"), defaultScale), scale);
					}
					double realAmount = DoubleHelper.sub(item.optDouble("item_amount"), DoubleHelper.add(item.optDouble("discount_amount"), item.optDouble("discountr_amount"), defaultScale), scale);
					if (maxRealAmount < realAmount && false == maxIdList.contains(item.optInt("id")))
					{
						maxJson = item;
					}
				}
			}
			
			if (null != maxJson && false == maxJson.isEmpty())
			{
				double maxRealAmount = DoubleHelper.sub(maxJson.optDouble("item_amount"), DoubleHelper.add(maxJson.optDouble("discount_amount"), maxJson.optDouble("discountr_amount"), defaultScale), scale);
				Double molingAmount = scrapAmount;
				if (scrapAmount > maxRealAmount)
				{
					molingAmount = maxRealAmount;
				}
				scrapAmount = DoubleHelper.sub(scrapAmount, molingAmount, defaultScale);
				maxIdList.add(maxJson.optInt("id"));
				
				StringBuilder updateItemSql = new StringBuilder("update pos_bill_item set single_discount_amount=? where id = ?");
				baseDao.update(updateItemSql.toString(), new Object[]
				{ molingAmount, maxJson.optInt("id") });

				if (SysDictionary.ITEM_PROPERTY_SETMEAL.equalsIgnoreCase(maxJson.optString("item_property")))
				{
					JSONObject detailMaxJson = null;
					for (JSONObject detail : itemList)
					{
						if (SysDictionary.ITEM_PROPERTY_MEALLIST.equals(detail.optString("item_property")) && maxJson.optInt("item_id") == detail.optInt("setmeal_id") && maxJson.optInt("item_serial") == detail.optInt("setmeal_rwid"))
						{
							if (null == detailMaxJson || detailMaxJson.optDouble("item_amount") < detail.optDouble("item_amount"))
							{
								detailMaxJson = detail;
							}
						}
					}

					if (Tools.hv(detailMaxJson))
					{
						baseDao.update(updateItemSql.toString(), new Object[]
						{ molingAmount, detailMaxJson.optInt("id") });
					}
				}
			}
			
			if(0==scrapAmount)
			{
				break;
			}
		}
	}
	
	/**
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @param discountModeId
	 * @param discountReasonId
	 * @param discountRate
	 * @param discountNum
	 * @param isNotState
	 * @param scale
	 * @throws Exception
	 */
	private void updateDiscountAmountByDiscountRate(String tenantId, int storeId, String billNum, Integer discountModeId, Integer discountReasonId, Double discountRate, String discountNum, boolean isNotState, int scale) throws Exception
	{
		List<JSONObject> itemList = baseDao.getPosBillItemForDiscountCaseByBillnum(tenantId, storeId, billNum, null);

		List<Object[]> batchArgs = new ArrayList<Object[]>();
		for (JSONObject item : itemList)
		{
			String itemProperty = item.optString("item_property");
			if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(itemProperty))
			{
				String itemRemark = item.optString("item_remark");
				Double singleAmount = item.optDouble("single_amount");
				Double itemAmount = item.optDouble("item_amount");

				if (itemAmount == 0d)
				{
					itemAmount = DoubleHelper.add(DoubleHelper.mul(item.optDouble("item_price"), item.optDouble("item_count"), defaultScale), DoubleHelper.add(item.optDouble("method_money"), item.optDouble("assist_money"), defaultScale), defaultScale);
				}

				Integer itemDiscountModeId = discountModeId;
				Integer itemDiscountReasonId = discountReasonId;
				String itemDiscountNum = discountNum;
				double itemDiscountRate = 100d;
				if (isNotState || "Y".equalsIgnoreCase(item.optString("discount_state")))
				{
					itemDiscountRate = discountRate;
				}

				if (SysDictionary.ITEM_REMARK_TC01.equals(itemRemark) || SysDictionary.ITEM_REMARK_FS02.equals(itemRemark) || SysDictionary.ITEM_REMARK_CJ05.equals(itemRemark))
				{
					itemDiscountRate = 100d;
				}
				if(SysDictionary.DISCOUNT_MODE_SINGLE_CANCEL == item.optInt("discount_mode_id"))
				{
					itemDiscountRate = 100d;
					itemDiscountModeId = SysDictionary.DISCOUNT_MODE_SINGLE_CANCEL;
					itemDiscountReasonId = 0;
					itemDiscountNum = null;
				}

				double rate = DoubleHelper.sub(1d, DoubleHelper.div(itemDiscountRate, 100d, defaultScale), defaultScale);

				if (SysDictionary.DISCOUNT_MODE_10 == item.optInt("discount_mode_id") && singleAmount > 0)
				{
					itemAmount = DoubleHelper.sub(itemAmount, singleAmount, defaultScale);
					itemDiscountModeId = SysDictionary.DISCOUNT_MODE_10;
					itemDiscountReasonId = item.optInt("discount_reason_id");
					itemDiscountNum = item.optString("discount_num");
				}

				double itemDiscountAmount = DoubleHelper.add(DoubleHelper.mul(itemAmount, rate, defaultScale), singleAmount, scale);

				if (itemDiscountModeId > 0 && itemDiscountAmount <= 0)
				{
					itemDiscountRate = 100d;
					itemDiscountModeId = 0;
					itemDiscountReasonId = 0;
					itemDiscountNum = null;
					itemDiscountAmount = 0d;
				}

				batchArgs.add(new Object[]
				{ itemDiscountAmount, itemDiscountRate, itemDiscountModeId, itemDiscountReasonId, itemDiscountNum, singleAmount, tenantId, storeId, billNum, item.optInt("id") });

				if (SysDictionary.ITEM_PROPERTY_SETMEAL.equalsIgnoreCase(itemProperty))
				{
					List<JSONObject> detailList = this.getMeallist(item, itemList);

					batchArgs.addAll(this.getMeallistDiscountAmount(tenantId, storeId, billNum, itemDiscountAmount, singleAmount, itemDiscountRate, itemDiscountModeId, itemDiscountReasonId, itemDiscountNum, detailList, scale));
				}
			}
		}

		if (batchArgs.size() > 0)
		{
			String updateSql = new String("update pos_bill_item set discount_amount=?, discount_rate=?, discount_mode_id=?, discount_reason_id=?, discount_num=?, single_amount =? where tenancy_id=? and store_id=? and bill_num=? and id=?");
			baseDao.batchUpdate(updateSql, batchArgs);
		}
	}

	/**
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @param discountAmount
	 * @param singleAmount
	 * @param discountRate
	 * @param discountModeId
	 * @param discountReasonId
	 * @param discountNum
	 * @param itemList
	 * @param scale
	 * @return
	 * @throws Exception
	 */
	private List<Object[]> getMeallistDiscountAmount(String tenantId, int storeId, String billNum, Double discountAmount, Double singleAmount, Double discountRate, Integer discountModeId, Integer discountReasonId, String discountNum, List<JSONObject> itemList, int scale)
			throws Exception
	{
		// 将减免金额平摊到套餐明细
		List<Object[]> batchArgs = new ArrayList<Object[]>();
		Double itemDiscountRate = discountRate;
		Integer itemDiscountModeId = discountModeId;
		Integer itemDiscountReasonId = discountReasonId;
		String itemDiscountNum = discountNum;

		if (discountAmount > 0)
		{
			Integer dMaxId = null;
			double dmaxAmount = 0d;
			double sumItemAmount = 0d;

			for (JSONObject itemJson : itemList)
			{
				Double itemAmount = ParamUtil.getDoubleValueByObject(itemJson, "item_amount");
				sumItemAmount = DoubleHelper.add(sumItemAmount, itemAmount, defaultScale);
				if (null == dMaxId || dmaxAmount < itemAmount)
				{
					dMaxId = itemJson.optInt("id");
					dmaxAmount = itemAmount;
				}
			}

			Double dRatio = DoubleHelper.div(discountAmount, sumItemAmount, defaultScale);
			Double sRatio = DoubleHelper.div(singleAmount, sumItemAmount, defaultScale);
			Double restDiscountAmount = discountAmount;
			Double restSingleAmount = singleAmount;
			for (JSONObject itemJson : itemList)
			{
				Double itemDiscountAmount = DoubleHelper.mul(itemJson.optDouble("item_amount"), dRatio, scale);
				restDiscountAmount = DoubleHelper.sub(restDiscountAmount, itemDiscountAmount, scale);

				Double itemSingleAmount = DoubleHelper.mul(itemJson.optDouble("item_amount"), sRatio, scale);
				restSingleAmount = DoubleHelper.sub(restSingleAmount, itemSingleAmount, scale);
			}

			for (JSONObject itemJson : itemList)
			{
				Double itemDiscountAmount = DoubleHelper.mul(itemJson.optDouble("item_amount"), dRatio, scale);
				Double itemSingleAmount = DoubleHelper.mul(itemJson.optDouble("item_amount"), sRatio, scale);
				if (dMaxId == itemJson.optInt("id"))
				{
					itemDiscountAmount = DoubleHelper.add(itemDiscountAmount, restDiscountAmount, scale);
					itemSingleAmount = DoubleHelper.add(itemSingleAmount, restSingleAmount, scale);
				}

				batchArgs.add(new Object[]
				{ itemDiscountAmount, itemDiscountRate, itemDiscountModeId, itemDiscountReasonId, itemDiscountNum, itemSingleAmount, tenantId, storeId, billNum, itemJson.optInt("id") });
			}
		}
		else
		{
			Double itemDiscountAmount = 0d;
			Double itemSingleAmount = 0d;
			itemDiscountRate = 100d;
			itemDiscountModeId = 0;
			itemDiscountReasonId = 0;
			itemDiscountNum = null;
			for (JSONObject itemJson : itemList)
			{
				batchArgs.add(new Object[]
				{ itemDiscountAmount, itemDiscountRate, itemDiscountModeId, itemDiscountReasonId, itemDiscountNum, itemSingleAmount, tenantId, storeId, billNum, itemJson.optInt("id") });
			}
		}
		return batchArgs;
	}

	/**
	 * @param item
	 * @param itemList
	 * @return
	 * @throws Exception
	 */
	private List<JSONObject> getMeallist(JSONObject item, List<JSONObject> itemList) throws Exception
	{
		List<JSONObject> detailList = new ArrayList<JSONObject>();
		for (JSONObject detail : itemList)
		{
			if (SysDictionary.ITEM_PROPERTY_MEALLIST.equals(detail.optString("item_property")) && item.optInt("item_id") == detail.optInt("setmeal_id") && item.optInt("item_serial") == detail.optInt("setmeal_rwid"))
			{
				detailList.add(detail);
			}
//			else if (SysDictionary.ITEM_PROPERTY_MEALLIST.equals(detail.optString("item_property")) && item.optInt("item_id") == detail.optInt("setmeal_id") && item.optInt("combo_prop") > 0)
//			{
//				// 组合套餐
//				detailList.add(detail);
//			}
		}
		return detailList;
	}

	/**
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @param scale
	 * @throws Exception
	 */
	private void updateDiscountAmountBySingleDiscount(String tenantId, int storeId, String billNum, int scale) throws Exception
	{
		List<JSONObject> itemList = baseDao.getPosBillItemForDiscountCaseByBillnum(tenantId, storeId, billNum, null);

		List<Object[]> batchArgs = new ArrayList<Object[]>();
		for (JSONObject item : itemList)
		{
			String itemProperty = item.optString("item_property");
			if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(itemProperty))
			{
				Double singleAmount = item.optDouble("single_amount");
				Double itemDiscountRate = 100d;
//				Double itemAmount = item.optDouble("item_amount");
				String itemRemark = item.optString("item_remark");
				Integer itemDiscountModeId = item.optInt("discount_mode_id");
				Integer itemDiscountReasonId = item.optInt("discount_reason_id");
				String itemDiscountNum = item.optString("discount_num");

				Double itemDiscountAmount = 0d;
				if (SysDictionary.DISCOUNT_MODE_10 == itemDiscountModeId && singleAmount > 0)
				{
					if (SysDictionary.ITEM_REMARK_TC01.equals(itemRemark) || SysDictionary.ITEM_REMARK_FS02.equals(itemRemark) || SysDictionary.ITEM_REMARK_CJ05.equals(itemRemark))
					{
						singleAmount = 0d;
					}
					if(SysDictionary.DISCOUNT_MODE_SINGLE_CANCEL == item.optInt("discount_mode_id"))
					{
						itemDiscountRate = 100d;
						itemDiscountModeId = SysDictionary.DISCOUNT_MODE_SINGLE_CANCEL;
						itemDiscountReasonId = 0;
						itemDiscountNum = null;
					}

					itemDiscountAmount = DoubleHelper.round(singleAmount, scale);
//					itemDiscountRate = DoubleHelper.mul(DoubleHelper.div(DoubleHelper.sub(itemAmount, itemDiscountAmount, scale), itemAmount, defaultScale), 100d, 2);  
				}

				if (itemDiscountModeId > 0 && itemDiscountAmount <= 0)
				{
					itemDiscountRate = 100d;
					itemDiscountModeId = 0;
					itemDiscountReasonId = 0;
					itemDiscountNum = null;
					itemDiscountAmount = 0d;
				}

				batchArgs.add(new Object[]
				{ itemDiscountAmount, itemDiscountRate, itemDiscountModeId, itemDiscountReasonId, itemDiscountNum, singleAmount, tenantId, storeId, billNum, item.optInt("id") });

				if (SysDictionary.ITEM_PROPERTY_SETMEAL.equalsIgnoreCase(itemProperty))
				{
					List<JSONObject> detailList = this.getMeallist(item, itemList);

					batchArgs.addAll(this.getMeallistDiscountAmount(tenantId, storeId, billNum, itemDiscountAmount, singleAmount, itemDiscountRate, itemDiscountModeId, itemDiscountReasonId, itemDiscountNum, detailList, scale));
				}

			}
		}

		if (batchArgs.size() > 0)
		{
			String updateSql = new String("update pos_bill_item set discount_amount=?, discount_rate=?, discount_mode_id=?, discount_reason_id=?, discount_num=?, single_amount =? where tenancy_id=? and store_id=? and bill_num=? and id=?");
			baseDao.batchUpdate(updateSql, batchArgs);
		}
	}

	/**计算折扣方案优惠金额
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @param discountCaseId
	 * @param discountModeId
	 * @param discountReasonId
	 * @param discountNum
	 * @param isVipPrice 是否使用会员价
	 * @param scale
	 * @throws Exception
	 */
	private void updateDiscountAmountByDiscountCase(String tenantId, int storeId, String billNum, Integer discountCaseId, Integer discountModeId, Integer discountReasonId, String discountNum, boolean isVipPrice, int scale) throws Exception
	{
		List<JSONObject> itemList = baseDao.getPosBillItemForDiscountCaseByBillnum(tenantId, storeId, billNum, discountCaseId);

		List<Object[]> batchArgs = new ArrayList<Object[]>();
		for (JSONObject item : itemList)
		{
			String itemProperty = item.optString("item_property");
			if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.optString("item_property")))
			{
				Integer itemDiscountModeId = discountModeId;
				Integer itemDiscountReasonId = discountReasonId;
				String itemDiscountNum = discountNum;

				String itemRemark = item.optString("item_remark");

				Double singleAmount = ParamUtil.getDoubleValueByObject(item, "single_amount");
				Double thirdPrice = ParamUtil.getDoubleValueByObject(item, "third_price");
				Double itemPrice = ParamUtil.getDoubleValueByObject(item, "item_price");
				Double itemCount = ParamUtil.getDoubleValueByObject(item, "item_count");
				Double itemAmount = ParamUtil.getDoubleValueByObject(item, "item_amount");
				
				Double derate = ParamUtil.getDoubleValueByObject(item, "derate");
				Double itemDiscountRate = ParamUtil.getDoubleValueByObject(item, "rate");
				if (null == itemDiscountRate || itemDiscountRate.isNaN() || itemDiscountRate == 0d) {
					itemDiscountRate = 100d;
				}
				
				Double itemDiscountAmount = 0d;
				if (SysDictionary.ITEM_REMARK_TC01.equalsIgnoreCase(itemRemark) || SysDictionary.ITEM_REMARK_FS02.equalsIgnoreCase(itemRemark) || SysDictionary.ITEM_REMARK_CJ05.equalsIgnoreCase(itemRemark))
				{
					itemDiscountAmount = 0d;
				}
				else if(SysDictionary.DISCOUNT_MODE_SINGLE_CANCEL == item.optInt("discount_mode_id"))
				{
					itemDiscountRate = 100d;
					itemDiscountModeId = SysDictionary.DISCOUNT_MODE_SINGLE_CANCEL;
					itemDiscountReasonId = 0;
					itemDiscountNum = null;
				}
				else if (derate > 0d)
				{
					itemDiscountRate = DoubleHelper.mul(DoubleHelper.div(DoubleHelper.sub(itemAmount, derate, scale), itemAmount, defaultScale), 100d, 2);  
					
					if (SysDictionary.DISCOUNT_MODE_10 == item.optInt("discount_mode_id") & singleAmount > 0)
					{
						itemDiscountAmount = DoubleHelper.round(singleAmount, scale);

						itemDiscountModeId = item.optInt("discount_mode_id");
						itemDiscountReasonId = item.optInt("discount_reason_id");
						itemDiscountNum = item.optString("discount_num");
					}
					else if (isVipPrice && thirdPrice > 0 && itemPrice > thirdPrice)
					{
						itemDiscountAmount = DoubleHelper.mul(DoubleHelper.sub(itemPrice, thirdPrice, defaultScale), itemCount, scale);
					}

					itemDiscountAmount = DoubleHelper.add(itemDiscountAmount, DoubleHelper.mul(derate, itemCount, defaultScale), scale);

					if (itemDiscountAmount > itemAmount)
					{
						itemDiscountAmount = itemAmount;
					}
				}
				else
				{
					if (SysDictionary.DISCOUNT_MODE_10 == item.optInt("discount_mode_id") & singleAmount > 0)
					{
						itemDiscountAmount = DoubleHelper.round(singleAmount, scale);

						itemDiscountModeId = item.optInt("discount_mode_id");
						itemDiscountReasonId = item.optInt("discount_reason_id");
						itemDiscountNum = item.optString("discount_num");
					}
					else if (isVipPrice && thirdPrice > 0 && itemPrice > thirdPrice)
					{
						itemDiscountAmount = DoubleHelper.mul(DoubleHelper.sub(itemPrice, thirdPrice, defaultScale), itemCount, scale);
					}
					itemAmount = DoubleHelper.sub(itemAmount, itemDiscountAmount, defaultScale);

					double rate = DoubleHelper.sub(1d, DoubleHelper.div(itemDiscountRate, 100d, defaultScale), defaultScale);
					itemDiscountAmount = DoubleHelper.add(itemDiscountAmount, DoubleHelper.mul(itemAmount, rate, scale), scale);
				}

				if (itemDiscountModeId >0 && itemDiscountAmount <= 0)
				{
					itemDiscountRate = 100d;
					itemDiscountModeId = 0;
					itemDiscountReasonId = 0;
					itemDiscountNum = null;
					itemDiscountAmount = 0d;
				}

				batchArgs.add(new Object[]
				{ itemDiscountAmount, itemDiscountRate, itemDiscountModeId, itemDiscountReasonId, itemDiscountNum, singleAmount, tenantId, storeId, billNum, item.optInt("id") });

				if (SysDictionary.ITEM_PROPERTY_SETMEAL.equalsIgnoreCase(itemProperty))
				{
					List<JSONObject> detailList = this.getMeallist(item, itemList);

					batchArgs.addAll(this.getMeallistDiscountAmount(tenantId, storeId, billNum, itemDiscountAmount, singleAmount, itemDiscountRate, itemDiscountModeId, itemDiscountReasonId, itemDiscountNum, detailList, scale));
				}
			}
		}

		if (batchArgs.size() > 0)
		{
			String updateSql = new String("update pos_bill_item set discount_amount=?, discount_rate=?, discount_mode_id=?, discount_reason_id=?, discount_num=?, single_amount =? where tenancy_id=? and store_id=? and bill_num=? and id=?");
			baseDao.batchUpdate(updateSql, batchArgs);
		}
	}

	/** 计算会员价优惠
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @param discountModeId
	 * @param discountReasonId
	 * @param discountRate
	 * @param discountNum
	 * @param scale
	 * @throws Exception
	 */
	private void updateDiscountAmountByVipPrice(String tenantId, int storeId, String billNum, Integer discountModeId, Integer discountReasonId, Double discountRate, String discountNum, int scale) throws Exception
	{
		List<JSONObject> itemList = baseDao.getPosBillItemForDiscountCaseByBillnum(tenantId, storeId, billNum, null);
		List<Object[]> batchArgs = new ArrayList<Object[]>();
		Double itemDiscountRate = 100d;
		for (JSONObject item : itemList)
		{
			String itemProperty = item.optString("item_property");
			if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(itemProperty))
			{
				Integer itemDiscountModeId = discountModeId;
				Integer itemDiscountReasonId = discountReasonId;

				String itemDiscountNum = discountNum;

				String itemRemark = item.optString("item_remark");

				Double singleAmount = ParamUtil.getDoubleValueByObject(item, "single_amount");
				Double thirdPrice = ParamUtil.getDoubleValueByObject(item, "third_price");
				Double itemPrice = ParamUtil.getDoubleValueByObject(item, "item_price");
				Double itemCount = ParamUtil.getDoubleValueByObject(item, "item_count");

				Double itemDiscountAmount = 0d;

				if (SysDictionary.ITEM_REMARK_TC01.equalsIgnoreCase(itemRemark) || SysDictionary.ITEM_REMARK_FS02.equalsIgnoreCase(itemRemark) || SysDictionary.ITEM_REMARK_CJ05.equalsIgnoreCase(itemRemark))
				{
					itemDiscountAmount = 0d;
				}
				else if(SysDictionary.DISCOUNT_MODE_SINGLE_CANCEL == item.optInt("discount_mode_id"))
				{
					itemDiscountRate = 100d;
					itemDiscountModeId = SysDictionary.DISCOUNT_MODE_SINGLE_CANCEL;
					itemDiscountReasonId = 0;
					itemDiscountNum = null;
				}
				else if (SysDictionary.DISCOUNT_MODE_10 == item.optInt("discount_mode_id") & singleAmount > 0)
				{
					itemDiscountAmount = DoubleHelper.round(singleAmount, scale);

					itemDiscountModeId = item.optInt("discount_mode_id");
					itemDiscountReasonId = item.optInt("discount_reason_id");
					itemDiscountNum = item.optString("discount_num");
				}
				else if (thirdPrice > 0 && itemPrice > thirdPrice)
				{
					itemDiscountAmount = DoubleHelper.mul(DoubleHelper.sub(itemPrice, thirdPrice, defaultScale), itemCount, scale);
				}

				if (itemDiscountModeId >0 && itemDiscountAmount <= 0)
				{
					itemDiscountRate = 100d;
					itemDiscountModeId = 0;
					itemDiscountReasonId = 0;
					itemDiscountNum = null;
					itemDiscountAmount = 0d;
				}

				batchArgs.add(new Object[]
				{ itemDiscountAmount, itemDiscountRate, itemDiscountModeId, itemDiscountReasonId, itemDiscountNum, singleAmount, tenantId, storeId, billNum, item.optInt("id") });

				if (SysDictionary.ITEM_PROPERTY_SETMEAL.equalsIgnoreCase(itemProperty))
				{
					List<JSONObject> detailList = this.getMeallist(item, itemList);

					batchArgs.addAll(this.getMeallistDiscountAmount(tenantId, storeId, billNum, itemDiscountAmount, singleAmount, itemDiscountRate, itemDiscountModeId, itemDiscountReasonId, itemDiscountNum, detailList, scale));
				}
			}
		}

		if (batchArgs.size() > 0)
		{
			String updateSql = new String("update pos_bill_item set discount_amount=?, discount_rate=?, discount_mode_id=?, discount_reason_id=?, discount_num=?, single_amount =? where tenancy_id=? and store_id=? and bill_num=? and id=?");
			baseDao.batchUpdate(updateSql, batchArgs);
		}
	}
	
	/**
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @param subtotal
	 * @param giviAmount
	 * @return
	 * @throws Exception
	 */
	private Double calcServiceAmount(String tenantId, int storeId, String billNum, Double subtotal,Double giviAmount) throws Exception
	{
		Double serviceAmount = 0d;
		String queryServiceSql = new String("select bs.*,sf.free_count from pos_bill_service bs left join hq_service_fee_type sf on bs.tenancy_id=sf.tenancy_id and bs.service_id=sf.id where bs.tenancy_id =? and bs.store_id=? and bs.bill_num=?");
		List<JSONObject> serviceList = baseDao.query4Json(tenantId, queryServiceSql.toString(), new Object[]
		{ tenantId, storeId, billNum });

		double serviceItemAmount = 0d;
		double serviceTotal = 0d;
		double serviceScale = 0d;

		List<Object[]> serviceBatchItem = new ArrayList<Object[]>();
		for (JSONObject serviceJson : serviceList)
		{
			serviceItemAmount = 0d;
			serviceTotal = 0d;
			serviceScale = serviceJson.optDouble("service_scale");

			switch (serviceJson.optString("taken_mode"))
			{
				case SysDictionary.SERVICE_MODE_GD01:
					serviceItemAmount = serviceScale;
					break;

				case SysDictionary.SERVICE_MODE_BL02:
					if ("Y".equals(serviceJson.optString("free_count")))// 赠送金额是否计算服务费
					{
						serviceItemAmount = DoubleHelper.mul(subtotal, DoubleHelper.div(serviceScale, 100d, defaultScale), defaultScale);
					}
					else
					{
						serviceItemAmount = DoubleHelper.mul(DoubleHelper.sub(subtotal, giviAmount, defaultScale), DoubleHelper.div(serviceScale, 100d, defaultScale), defaultScale);
					}
					break;
				default:
					break;
			}
			
			serviceTotal = DoubleHelper.mul(serviceItemAmount, serviceJson.optDouble("service_count"), defaultScale);
			serviceBatchItem.add(new Object[]
			{ serviceItemAmount, serviceTotal, tenantId, storeId, serviceJson.optInt("id") });

			serviceAmount = DoubleHelper.add(serviceAmount, serviceTotal, defaultScale);
		}

		if (serviceBatchItem.size() > 0)
		{
			String updateServiceSql = new String("update pos_bill_service set service_amount=?,service_total=? where tenancy_id=? and store_id=? and id=?");
			baseDao.batchUpdate(updateServiceSql.toString(), serviceBatchItem);
		}

		return serviceAmount;
	}
}
