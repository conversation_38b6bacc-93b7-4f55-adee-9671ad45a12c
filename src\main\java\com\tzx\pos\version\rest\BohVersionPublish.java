package com.tzx.pos.version.rest;

import net.sf.json.JSONObject;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR> on 2017-03-20
 */
@Controller("BohVersionPublish")
@RequestMapping("/productUpdateRest")
public class BohVersionPublish {

    @RequestMapping(value = "/check")
    @ResponseBody
    public JSONObject check(HttpServletRequest request, HttpServletResponse response) {

        InputStream inputStream = null;
        JSONObject resJson=null;
        JSONObject result = null;

        String downloadFolder = request.getServletContext().getRealPath("/download");

        try {
            inputStream = request.getInputStream();
            resJson = JSONObject.fromObject(IOUtils.toString(inputStream));

            result = JSONObject.fromObject(FileUtils.readFileToString(new File(downloadFolder + "/mask_check.json"), "UTF-8"));
            JSONObject data = result.optJSONObject("data");
            data.putAll(resJson);

            if (data.optString("source_version").equals(data.optString("target_version"))) {
                result.put("code", 100);
                result.put("msg", "up to date.");
            } else {

                String url=data.optString("download_url");
                String publishFilePath = StringUtils.substringBefore(url,"?");
                File publishFile = new File(publishFilePath);
                if (!publishFile.exists()) {
                    throw new IOException("文件不存在:" + publishFile.getAbsolutePath());
                }

                FileUtils.copyFileToDirectory(publishFile, FileUtils.getFile(downloadFolder));
                String downloadUrl = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + request.getContextPath() + "/download/" + publishFile.getName() + "?"+StringUtils.substringAfter(url,"?");
                data.put("download_url", downloadUrl);
            }
        } catch (IOException e) {
            e.printStackTrace();
            IOUtils.closeQuietly(inputStream);
            result.put("code",200);
            result.put("msg","更新服务器内部错误");

        }

        return result;
    }
}
