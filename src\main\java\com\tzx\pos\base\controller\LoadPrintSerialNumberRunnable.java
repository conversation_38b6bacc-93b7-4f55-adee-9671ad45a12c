package com.tzx.pos.base.controller;

import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.pos.bo.PosService;
import com.tzx.pos.bo.imp.PosServiceImp;
import org.apache.log4j.Logger;

import java.util.Map;

/**
 * 加载厨打打印序号
 *
 */

public class LoadPrintSerialNumberRunnable implements Runnable {
	
	private static final Logger logger = Logger.getLogger(LoadPrintSerialNumberRunnable.class);

	// 是否已从内存中加载序号
    private static boolean IS_LOADING = false;

	@Override
	public void run() {
		// TODO Auto-generated method stub
		try{
			Map<String, String> systemMap = Constant.getSystemMap();
			
			if (!systemMap.containsKey("tenent_id") || !systemMap.containsKey("store_id") || "".equals(systemMap.get("tenent_id")) || "".equals(systemMap.get("store_id")))
			{
				logger.info("参数为空");
				return;
			}

            String tenentId = systemMap.get("tenent_id");
            String storeId = systemMap.get("store_id");
            DBContextHolder.setTenancyid(tenentId);

			if(IS_LOADING){

                PosService baseService = (PosServiceImp) SpringConext.getApplicationContext().getBean(PosService.NAME);
                baseService.updatePrintSerialByMap(tenentId, Integer.parseInt(storeId));

            } else {
                while(SpringConext.getApplicationContext() == null){
                    Thread.sleep(500);
                }
                logger.debug("---spring上下文对象---"+SpringConext.getApplicationContext());

                PosService baseService = (PosServiceImp) SpringConext.getApplicationContext().getBean(PosService.NAME);
                baseService.loadPrintSerialNumber(tenentId, Integer.parseInt(storeId));

                logger.info("加载厨打打印序号成功！");

                IS_LOADING = true;
            }

		}catch(Exception e){
			logger.error("加载厨打打印序号失败...", e);
		}
	}

}
