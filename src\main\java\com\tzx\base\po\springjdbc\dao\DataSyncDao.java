/**
  * @Description: TODO
  * @author: z<PERSON><PERSON>n
  * @version: 1.0
  * @Date: 2018年4月3日
*/
package com.tzx.base.po.springjdbc.dao;

import java.util.List;

import com.tzx.framework.common.util.dao.GenericDao;

/**
  * @Description: TODO
  * @param:
  * @author: zhouquan
  * @version: 1.0
  * @Date: 2018年4月3日
*/
public interface DataSyncDao extends GenericDao{

    String	NAME	= "com.tzx.base.po.springjdbc.dao.imp.DataSyncDaoImp";
    
    /*
     * 返回查询结果（整型）
     */
    public int getInt(String sql) throws Exception;

	/**
	  * @Description: TODO
	  * @Title:saveBaseData
	  * @param:@param sqList
	  * @return: void
	  * @author: zhouquan
	  * @version: 1.0
	  * @Date: 2018年4月3日
	*/
	public boolean saveBaseData(String[] sqList);
	
	/*
	 * 事物方式批量执行SQL
	 */
	public boolean execBatchSqls (List<String> sqlList);
}
