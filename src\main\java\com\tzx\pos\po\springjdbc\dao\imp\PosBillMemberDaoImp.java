package com.tzx.pos.po.springjdbc.dao.imp;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import net.sf.json.JSONObject;

import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import com.tzx.base.entity.PosBillMember;
import com.tzx.framework.common.util.Tools;
import com.tzx.pos.base.dao.imp.BaseDaoImp;
import com.tzx.pos.bo.dto.PosBillMembers;
import com.tzx.pos.po.springjdbc.dao.PosBillMemberDao;

@Repository(PosBillMemberDao.NAME)
public class PosBillMemberDaoImp extends BaseDaoImp implements PosBillMemberDao
{
	@Override
	public void insertPosBillMember(String tenancyId, int storeId, String billNum, Date reportDate, String type, String customerCode, String cardCode, String mobil, Timestamp lastUpdatetime, String remark, String customer_name, double consume_before_credit, double consume_after_credit,
			double credit, double amount, String bill_code, String request_state) throws Exception
	{
		StringBuilder sql = new StringBuilder();
		sql.append(" insert into pos_bill_member(tenancy_id,store_id,bill_num,report_date,type,customer_code,card_code,mobil,last_updatetime,remark,upload_tag,customer_name,consume_before_credit,consume_after_credit,credit,amount,bill_code,request_state) values (?,?,?,?,?,?,?,?,?,?,0,?,?,?,?,?,?,?)");
		this.jdbcTemplate.update(sql.toString(), new Object[]
		{ tenancyId, storeId, billNum, reportDate, type, customerCode, cardCode, mobil, lastUpdatetime, remark, customer_name, Double.isNaN(consume_before_credit) ? 0 : consume_before_credit, Double.isNaN(consume_after_credit) ? 0 : consume_after_credit, Double.isNaN(credit) ? 0 : credit, amount,
				bill_code, request_state });
	}

	@Override
	public void insertPosBillMember(String tenancyId, int storeId, String billNum, Date reportDate, String type, String customerCode, String cardCode, String mobil, Timestamp lastUpdatetime, String remark, String customer_name, double customer_credit) throws Exception
	{
		this.insertPosBillMember(tenancyId, storeId, billNum, reportDate, type, customerCode, cardCode, mobil, lastUpdatetime, remark, customer_name, customer_credit, 0d, 0d, 0d, null, null);
	}
	
	@Override
	public void insertPosBillMember(String tenancyId, int storeId, PosBillMember billMember) throws Exception
	{
		StringBuilder sql = new StringBuilder(
				" insert into pos_bill_member(tenancy_id,store_id,bill_num,report_date,type,amount,credit,card_code,mobil,last_updatetime,upload_tag,remark,bill_code,request_state,customer_code,customer_name,consume_before_credit,consume_after_credit,consume_before_main_balance,consume_before_reward_balance,consume_after_main_balance,consume_after_reward_balance,generic_field) values (?,?,?,?,?,?,?,?,?,?,'0',?,?,?,?,?,?,?,?,?,?,?,?)");
		this.jdbcTemplate.update(
				sql.toString(),
				new Object[]
				{ tenancyId, storeId, billMember.getBill_num(), billMember.getReport_date(), billMember.getType(), billMember.getAmount(), billMember.getCredit(), billMember.getCard_code(), billMember.getMobil(), billMember.getLast_updatetime(), billMember.getRemark(), billMember.getBill_code(),
						billMember.getRequest_state(), billMember.getCustomer_code(), billMember.getCustomer_name(), billMember.getConsume_before_credit(), billMember.getConsume_after_credit(), billMember.getConsume_before_main_balance(), billMember.getConsume_before_reward_balance(),
						billMember.getConsume_after_main_balance(), billMember.getConsume_after_reward_balance(),billMember.getGeneric_field() });
	}

	@Override
	public void updatePosBillMember(String tenantId, int storeId, String billno, String type, double amount, double credit, String billCode, String requestState, String customerName, Double consumeBeforeCredit, Double consumeAfterCredit) throws Exception
	{
		StringBuilder sql = new StringBuilder();
		sql.append("update pos_bill_member set amount=?,credit=?,bill_code=?,request_state=?,customer_name=?,consume_before_credit=?,consume_after_credit=? where tenancy_id=? and store_id=? and bill_num=? and type=?");
		this.jdbcTemplate.update(sql.toString(), new Object[]
		{ amount, Double.isNaN(credit) ? 0 : credit, billCode, requestState, customerName, Double.isNaN(consumeBeforeCredit) ? 0 : consumeBeforeCredit, Double.isNaN(consumeAfterCredit) ? 0 : consumeAfterCredit, tenantId, storeId, billno, type });
	}
	
	@Override
	public void updatePosBillMember(String tenantId, int storeId, String billno, String type, Integer id, double amount, double credit, String billCode, String requestState, String customerName, Double consumeBeforeCredit, Double consumeAfterCredit) throws Exception
	{
		StringBuilder sql = new StringBuilder();
		sql.append("update pos_bill_member set amount=?,credit=?,bill_code=?,request_state=?,customer_name=?,consume_before_credit=?,consume_after_credit=? where tenancy_id=? and store_id=? and bill_num=? and type=? and id=?");
		this.jdbcTemplate.update(sql.toString(), new Object[]
		{ amount, Double.isNaN(credit) ? 0 : credit, billCode, requestState, customerName, Double.isNaN(consumeBeforeCredit) ? 0 : consumeBeforeCredit, Double.isNaN(consumeAfterCredit) ? 0 : consumeAfterCredit, tenantId, storeId, billno, type, id });
	}
	
	@Override
	public void updatePosBillMember(String tenantId, int storeId, String billNum, String type, double amount, double credit, String billCode, String requestState, String customerName, Double consumeAfterCredit, Double consumeAfterMainBalance,Double consumeAfterRewardBalance) throws Exception
	{
		amount = Double.isNaN(amount) ? 0 : amount;
		credit = Double.isNaN(credit) ? 0 : credit;
		consumeAfterCredit = Double.isNaN(consumeAfterCredit) ? 0 : consumeAfterCredit;
		consumeAfterMainBalance = Double.isNaN(consumeAfterMainBalance) ? 0 : consumeAfterMainBalance;
		consumeAfterRewardBalance = Double.isNaN(consumeAfterRewardBalance) ? 0 : consumeAfterRewardBalance;
		StringBuilder sql = new StringBuilder();
		sql.append("update pos_bill_member set amount=?,credit=?,bill_code=?,request_state=?,customer_name=?,consume_after_credit=?,consume_after_main_balance=?,consume_after_reward_balance=? where tenancy_id=? and store_id=? and bill_num=? and type=?");
		this.jdbcTemplate.update(sql.toString(), new Object[]
		{ amount, credit, billCode, requestState, customerName, consumeAfterCredit, consumeAfterMainBalance, consumeAfterRewardBalance, tenantId, storeId, billNum, type });
	}

	@Override
	public void deletePosBillMember(String tenantId, int storeId, String billNum, String[] types) throws Exception
	{
		StringBuilder sql = new StringBuilder("delete from pos_bill_member pbm where pbm.tenancy_id =? and pbm.store_id =? and pbm.bill_num =?");
		StringBuilder typeSql = new StringBuilder();
		for (String type : types)
		{
			if (typeSql.length() > 0)
			{
				typeSql.append(" or");
			}
			typeSql.append(" type = '").append(type).append("'");
		}
		if (typeSql.length() > 0)
		{
			sql.append(" and (").append(typeSql.toString()).append(")");
		}
		this.jdbcTemplate.update(sql.toString(), new Object[]
		{ tenantId, storeId, billNum, });
	}

	@Override
	public void deletePosBillMember(String tenantId, int storeId, String billno, String type) throws Exception
	{
		StringBuilder sql = new StringBuilder();
		sql.append("delete from pos_bill_member pbm where pbm.tenancy_id =? and pbm.store_id =? and pbm.bill_num =? and type = ?");
		this.jdbcTemplate.update(sql.toString(), new Object[]
		{ tenantId, storeId, billno, type });
	}
	
	@Override
	public void deletePosBillMemberByBillCode(String tenantId, int storeId, String billNum, String billCode) throws Exception
	{
		StringBuilder sql = new StringBuilder();
		sql.append("delete from pos_bill_member pbm where pbm.tenancy_id =? and pbm.store_id =? and pbm.bill_num =? and bill_code = ?");
		this.jdbcTemplate.update(sql.toString(), new Object[]
		{ tenantId, storeId, billNum, billCode });
	}
	
	@Override
	public List<JSONObject> queryPosBillMember(String tenantId, int storeId, String billno, String type) throws Exception
	{
		StringBuilder sql = new StringBuilder("select * from pos_bill_member where tenancy_id =? and store_id =? and bill_num =?");
		if (Tools.hv(type))
		{
			sql.append(" and type = '").append(type).append("'");
		}

		return this.query4Json(tenantId, sql.toString(), new Object[]
		{ tenantId, storeId, billno });
	}

	@Override
	public List<JSONObject> queryPosBillMember(String tenantId, int storeId, String billNum, String cardCode, String type) throws Exception
	{
		String sql = " select * from pos_bill_member where tenancy_id =? and store_id =? and bill_num =? and card_code =? and type =?";
		return this.query4Json(tenantId, sql, new Object[]
		{ tenantId, storeId, billNum, cardCode, type });
	}

	@Override
	public List<PosBillMembers> selectMembersByBillNum(String tenantId, Integer organId, String billNum) throws Exception
	{
		String membersSql = "select id,type,amount,credit,card_code,mobil,last_updatetime,remark,bill_num from pos_bill_member where bill_num = ? and store_id=? and tenancy_id=?";
		List<PosBillMembers> memberList = this.jdbcTemplate.query(membersSql, new Object[]
		{ billNum, organId, tenantId }, BeanPropertyRowMapper.newInstance(PosBillMembers.class));

		return memberList;
	}

	@Override
	public void updatePosBillMemberRegain(String tenantId, int storeId, String billno, String type, double amount, double credit, String billCode, String requestState, String customerName, Double consumeBeforeCredit, Double consumeAfterCredit, Integer regainCount) throws Exception
	{
		StringBuilder sql = new StringBuilder();
		sql.append("update pos_bill_member_regain set amount=?,credit=?,bill_code=?,request_state=?,customer_name=?,consume_before_credit=?,consume_after_credit=? where tenancy_id=? and store_id=? and bill_num=? and type=? and regain_count=?");
		this.jdbcTemplate.update(sql.toString(), new Object[]
		{ amount, credit, billCode, requestState, customerName, consumeBeforeCredit, consumeAfterCredit, tenantId, storeId, billno, type, regainCount });
	}

	@Override
	public List<JSONObject> getPosBillMemberRegainByBillNum(String tenantId, int storeId, String billNum, String type,Integer regainCount) throws Exception
	{
		String sql = " select * from pos_bill_member_regain where tenancy_id=? and store_id=? and bill_num=? and type =? and regain_count=?";
		return this.query4Json(tenantId, sql, new Object[]
		{ tenantId, storeId, billNum, type, regainCount });
	}

	@Override
	public void copyPosBillMemberByBillNum(String tenantId, int storeId, String fromBillNum, String toBillNum, Date reportDate, String[] types, Timestamp updateTime) throws Exception
	{
		// TODO Auto-generated method stub
		StringBuilder sql = new StringBuilder();
		sql.append(" insert into pos_bill_member(tenancy_id,store_id,bill_num,report_date,type,amount,credit,card_code,mobil,last_updatetime,upload_tag,remark,bill_code,request_state,customer_code,customer_name,consume_before_credit,consume_after_credit,consume_before_main_balance,consume_before_reward_balance,consume_after_main_balance,consume_after_reward_balance)");
		sql.append(" select tenancy_id,store_id,? as bill_num,? as report_date,type,amount,credit,card_code,mobil,? as last_updatetime,'0' as upload_tag,remark,bill_code,request_state,customer_code,customer_name,consume_before_credit,consume_after_credit,consume_before_main_balance,consume_before_reward_balance,consume_after_main_balance,consume_after_reward_balance from pos_bill_member");
		sql.append(" where tenancy_id=? and store_id=? and bill_num=? ");
		StringBuilder typeSql = new StringBuilder();
		for (String type : types)
		{
			if (typeSql.length() > 0)
			{
				typeSql.append(" or");
			}
			typeSql.append(" type = '").append(type).append("'");
		}
		if (typeSql.length() > 0)
		{
			sql.append(" and (").append(typeSql.toString()).append(")");
		}
		
		this.jdbcTemplate.update(sql.toString(),new Object[]{toBillNum,reportDate,updateTime,tenantId,storeId,fromBillNum});
	}

}