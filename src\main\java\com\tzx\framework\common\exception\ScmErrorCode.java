package com.tzx.framework.common.exception;

public enum ScmErrorCode implements ErrorCode
{

	/**
	 * 日结日期验证错误
	 */
	DADATE_ERROR(1001),

	/**
	 * 仓库状态验证错误:禁止入库
	 */
	WAREHOUSE_BUSISTATE_IN_ERROR(1006),
	/**
	 * 仓库状态验证错误:禁止出库
	 */
	WAREHOUSE_BUSISTATE_OUT_ERROR(1007),
	
	/**
	 * 仓库状态验证错误:禁止出入库
	 */
	WAREHOUSE_BUSISTATE_INOUT_ERROR(1008),
	/**
	 * 仓库状态验证错误:禁止封存
	 */
	WAREHOUSE_BUSISTATE_FC_ERROR(1009),
	/**
	 * 没有有效的仓库
	 */
	NO_VALID_WAREHOUSE(1010),
	/**
	 * 成本核算方式错误
	 */
	PRICE_METHOD_ERROR(1015),
	
	/**
	 * 退货超出库存数量
	 */
	OVER_QTY_ERROR(1020),

	/**
	 * 库存不包含该物品
	 */
	UNINCLUDE_ERROR(1021),

	/**
	 * 用户名或密码错误
	 */
	TRANSFER_TYPE_ERROR(1031),

	/**
	 * 订单类型异常
	 */
	ORDER_TYPE_ERROR(1035),
	
	/**
	 * 直拨出库时，未找到库存信息
	 */
	NOT_FIND_OUTSTORAGE_ERROR(1040),
	
	/**
	 * 直拨出库时，未找到库存信息
	 */
	OVER_ZHIBO_QTY_ERROR(1045),
	/**
	 * 供应商付款时，订单类型异常
	 */
	ORDER_TYPE_SUPPLYPAY_ERROR(1050),
	
	/**
	 * 接口异常
	 */
	INTERFACE_ERROR(1055),
	
	/**
	 * 出库金额大于库存金额，禁止出库
	 */
	AMOUNT_LESS_ERROR(1060),
	
	/**
	 * 要货单明细为空
	 */
	INVOICE_DETAIL_EMPTY_ERROR(1070),
	/**
	 * 要货单包含空信息
	 */
	INVOICE_EMPTY_ERROR(1071),
	/**
	 * 冲减数量错误
	 */
	REDUCE_ERROR(1072),
	/**
	 * 未找到菜品信息
	 */
	NOITEM(1073),
	/**
	 * 没有bom配比信息
	 */
	NOBOM(1074),
	/**
	 * 
	 */
	PUR_CONVERSION_ERR0R(1075),
	
	/**
	 * 单据号已存在
	 */
	BILL_NO_EXIST(1076),
	
	/**
	 * 未找到退货单
	 */
	NO_DC_OUT_BILL(1077),
	
	/**
	 * 未找到物品来源
	 */
	NO_MAT_IN(1078),
	
	/**
	 * 配送渠道异常
	 */
	CHANNEL_TYPE_ERROR(1079),
	/**
	 * 第一供应商异常
	 */
	FIR_SUP_ERROR(1080),
	/**
	 * 第二供应商异常
	 */
	SEC_SUP_ERROR(1081),
	/**
	 * 第三供应商异常
	 */
	THR_SUP_ERROR(1082),
	/**
	 * 第一供应商采购占比异常
	 */
	FIR_PER_ERROR(1083),
	/**
	 *  第二供应商采购占比异常
	 */
	SEC_PER_ERROR(1084),
	/**
	 * 第三供应商采购占比异常
	 */
	THR_PER_ERROR(1085),
	/**
	 * 没有供应商配送物品信息
	 */
	NO_SUP_MAT(1086),
	/**
	 * 未设置供应商采购价格
	 */
	NO_SUP_PRICE(1087),
	/**
	 * 未找到调拨出库单
	 */
	NO_STORE_OUT_BILL_ERROR(1088),
	/**
	 * 未找到调拨出库单明细
	 */
	NO_STORE_OUT_DETAIL_ERROR(1089),
	;
	

	private final int	number;

	private ScmErrorCode(int number)
	{
		this.number = number;
	}

	public int getNumber()
	{
		return number;
	}

	@Override
	public String getMessage()
	{
		return "";
	}

}
