package com.tzx.pos.base.print;

import java.util.Vector;

/**
 *	打印队列，自动增加容量
 * <AUTHOR>
 * 2015年7月16日-下午5:12:35
 */
public class PrintQueue
{
	//使用Vector不用考虑同步问题
	private Vector queue = null;
	
	public PrintQueue()
	{
		queue = new Vector();
	}
	/**
	 * 这个参数Object可以换成JSONObject
	 * @param obj
	 */
	public void push(Object obj)
	{
		queue.addElement(obj);
	}
	/**
	 * 可以返回JSONObject
	 * @return
	 */
	public Object pull()
	{
		if(queue.size()==0)
		{
			return null;
		}
		Object obj = queue.firstElement();
		queue.removeElementAt(0);
		return obj;
	}
	
	public int size()
	{
		return queue.size();
	}
	
	public boolean isEmpty()
	{
		if (queue.size() == 0)
		{
			return true;
		}else
			return false;
	}
}
