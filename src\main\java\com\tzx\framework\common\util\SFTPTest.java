/**
 * @Title: SFTPTest.java 
 * @Package com.tzx.framework.common.util 
 * @Description: TODO(用一句话描述该文件做什么) 
 * <AUTHOR> A18ccms_gmail_com  
 * @date 2017-1-10 下午6:31:40 
 * @version V1.0  
 */
package com.tzx.framework.common.util;

import java.util.HashMap;
import java.util.Map;

import com.jcraft.jsch.ChannelSftp;

/**
 * @ClassName: SFTPTest 
 * @Description: TODO(这里用一句话描述这个类的作用) 
 * <AUTHOR> a18ccms_gmail_com 
 * @date 2017-1-10 下午6:31:40  
 */
public class SFTPTest
{
	public SFTPChannel getSFTPChannel() {
        return new SFTPChannel();
    }

    /**
     * @param args
     * @throws Exception
     */
    public static void uploadSFTP(String host,String username,String password,String port,String fileName) throws Exception {
        SFTPTest test = new SFTPTest();

        Map<String, String> sftpDetails = new HashMap<String, String>();
        // 设置主机ip，端口，用户名，密码
        sftpDetails.put(SFTPConstants.SFTP_REQ_HOST,host);
        sftpDetails.put(SFTPConstants.SFTP_REQ_USERNAME, username);
        sftpDetails.put(SFTPConstants.SFTP_REQ_PASSWORD, password);
        sftpDetails.put(SFTPConstants.SFTP_REQ_PORT,port);
        
        String src = fileName; // 本地文件名	
        String dst = "/upload"; // 目标文件名
              
        SFTPChannel channel = test.getSFTPChannel();
        
        ChannelSftp chSftp = channel.getChannel(sftpDetails, 60000);
  
        chSftp.put(src, dst, ChannelSftp.OVERWRITE); // 代码段2  
        chSftp.quit();
        channel.closeChannel();
    }
}
