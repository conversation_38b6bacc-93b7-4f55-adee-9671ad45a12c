package com.tzx.pos.base;

import java.util.HashMap;
import java.util.Map;

import com.tzx.framework.common.exception.CrmErrorCode;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.pos.base.controller.ThirdPaymentOrderRunnable;

/**
 * 
 * <AUTHOR>
 * 2015-5-8-下午2:01:49
 *
 **/

public class Constant {
	
//	public static final String PRINT1105 = "1105";//大类
//	public static final String PRINT1106 = "1106";//单切单
//	public static final String PRINT1107 = "1107";//整台单
//	public static final String PRINT1108 = "1108";//转台
	//code的值
	public static final int CODE_SUCCESS = 0; //成功
	public static final int CODE_PARAM_FAILURE = 1; //参数失败
	public static final int CODE_AUTH_FAILURE = 2; //认证失败
	public static final int CODE_NULL_DATASET = 3; //数据集为空
	public static final int CODE_CHANGE_DATASOURCE_FAILURE = 4;//切换数据源失败
	public static final int CODE_INNER_EXCEPTION = 5; //内部错误
	public static final int CODE_STORE_EXCEPTION = 6; //门店状态异常
	public static final int CODE_CONN_EXCEPTION = 99; //连接超时
	public static final int CODE_CONN_UNKNOWNHOST_EXCEPTION = 404; //连接失败
	
	public static final String CODE_CONN_EXCEPTION_MSG = "网络请求连接超时"; //连接异常
	
	public static final String CODE_SUCCESS_MSG = "成功"; 
	public static final String CODE_FAILURE_MSG = "失败"; 
	//折扣方案id
	public static final Double DISCOUNT_CASE_ID = 0d;
	
	public static final String NOT_EXISTS_TYPE = "未找到对应的type";
	public static final String NOT_EXISTS_OPER = "未找到对应的oper";
	
	public static final String QUERY_ORGAN_STATUS_FAILURE = "查询门店状态失败";
	
	public static final String NOT_EXISTS_HAVING_DISH = "没有已下单菜品，不能取消备注";
	public static final String HAVING_PRINT_CAN_NOT_CANC = "菜品已打印，不能取消备注";
	
	public static final String PAUSE_OPERATING = "暂停营业";
	public static final String CLOSED_STORE = "关店";
	
	public static final String NOT_NULL_ITEM = "参数列表item不能为空";
	
	public static final int				CODE_UPLOAD_DATA_NULL									= 2001;							// 内部错误
	public static final String			UPLOAD_DATA_FAILURE										= "上传数据失败";
	public static final String			UPLOAD_DATA_SUCCESS										= "上传数据成功";
	public static final String			UPLOAD_DATA_NULL										= "数据已全部上传完成";
	
	public static final String POS_CHANGE_DATASOURCE_FAILURE = "切换数据源失败";
	public static final String CLONE_DATA_FAILURE = "Data对象克隆失败";
	public static final String INNER_ERROR = "内部错误";
	public static final String DISH_COUNT_NULL = "菜品数量为0";
	
	public static final String SYS_CONFIG_SUCCESS = "系统设置成功";
	public static final String SYS_CONFIG_FAILURE = "系统设置错误";
	
	public static final String LOGIN_SUCCESS = "登录成功";
	public static final String LOGIN_FAILURE = "登录失败,用户名或密码错误~";
	public static final String LOGIN_ERROR = "登录失败";
	
	public static final String MODIFY_PASSWORD_SUCCESS = "修改密码成功";
	public static final String MODIFY_PASSWORD_FAILURE = "修改密码失败";
	
	public static final String UPLOAD_PRINTFORMAT_SUCCESS = "打印模板上传成功";
	public static final String UPLOAD_PRINTFORMAT_FAILURE = "打印模板上传失败";
	
	public static final String OPEN_TABLE_FAILURE = "开台失败";
	public static final String OPEN_TABLE_SUCCESS = "开台成功";
	
	public static final String WHOLE_CANC_BILL_FAILURE = "整单取消失败";
	public static final String WHOLE_CANC_BILL_SUCCESS = "整单取消成功";
	
	public static final String SYNC_DATA_FAILURE = "同步数据失败";
	public static final String SYNC_DATA_SUCCESS = "同步数据成功";
	
	public static final String FIND_TABLE_NOT_EXISTS = "未查询到对应的桌位，请检查您输入的桌位号或联系管理员";
	public static final String FIND_TABLE_FAILURE = "查询桌位失败";
	public static final String FIND_TABLE_SUCCESS = "查询桌位成功";
	
	public static final String ORDER_DISH_FAILURE = "下单失败,未找到此账单对应的明细";
	public static final String ORDER_DISH_SUCCESS = "下单成功";
	
	public static final String PAUSE_ORDER_DISH_FAILURE = "暂存菜品失败";
	public static final String PAUSE_ORDER_DISH_SUCCESS = "暂存菜品成功";
	
	public static final String PAYMENT_FAILURE = "结账失败";
	public static final String PAYMENT_SUCCESS = "结账成功";
	public static final String PAYMENT_PAYING = "结账付款中";
	
	public static final String CLEAR_PAYMENT_FAILURE = "清除付款记录失败";
	public static final String CLEAR_PAYMENT_SUCCESS = "清除付款记录成功";
	
	public static final String CHANGE_TABLE_FAILURE = "转台失败";
	public static final String CHANGE_TABLE_SUCCESS = "转台成功";
	
	public static final String COMBINE_TABLE_FAILURE = "并台失败";
	public static final String COMBINE_TABLE_SUCCESS = "并台成功";
	public static final String COMBINE_TABLE_CANCLE_SUCCESS = "取消并台成功";
	public static final String COMBINE_TABLE_CANCLE_FAILURE = "取消并台失败";

	public static final String FIND_BILL_FAILURE = "查询账单失败";
	public static final String FIND_BILL_SUCCESS = "查询账单成功";
	
	public static final String SET_GUQING_FAILURE = "设置沽清失败";
	public static final String SET_GUQING_SUCCESS = "设置沽清成功";
	
	public static final String DEL_GUQING_FAILURE = "删除沽清失败";
	public static final String DEL_GUQING_SUCCESS = "删除沽清成功";
	
	public static final String QUERY_GUQING_NOT_EXIST = "未查询到沽清菜品";
	public static final String QUERY_GUQING_FAILURE = "查询沽清失败";
	public static final String QUERY_GUQING_SUCCESS = "查询沽清成功";
	
	public static final String SET_WORRY_DISH_FAILURE = "设置急推菜品失败";
	public static final String SET_WORRY_DISH_SUCCESS = "设置急推菜品成功";
	
	public static final String DEL_WORRY_DISH_FAILURE = "删除急推菜品失败";
	public static final String DEL_WORRY_DISH_SUCCESS = "删除急推菜品成功";
	
	public static final String QUERY_WORRY_DISH_NOT_EXIST = "未查询到急推菜品";
	public static final String QUERY_WORRY_DISH_FAILURE = "查询急推菜品失败";
	public static final String QUERY_WORRY_DISH_SUCCESS = "查询急推菜品成功";
	
	public static final String LOCK_UNLOCK_TABLE_FAILURE = "锁定或解锁桌位失败";
	public static final String LOCK_TABLE_SUCCESS = "锁定桌位成功";
	public static final String UNLOCK_TABLE_SUCCESS = "解锁桌位成功";
	
	public static final String COPY_TABLE_FAILURE = "复制桌位失败";
	public static final String COPY_TABLE_SUCCESS = "复制桌位成功";
	
	public static final String SINGLE_CHANGE_TABLE_FAILURE = "单品转台失败";
	public static final String SINGLE_CHANGE_TABLE_SUCCESS = "单品转台成功";
	
	public static final String QUERY_ORDERED_DISH_FAILURE = "查询已下单菜品失败";
	public static final String QUERY_ORDERED_DISH_SUCCESS = "查询已下单菜品成功";
	
	public static final String QUERY_CHEAP_DISH_FAILURE = "查询特价菜品失败";
	public static final String QUERY_CHEAP_DISH_SUCCESS = "查询特价菜品成功";
	
	public static final String MODIFY_SERVICE_FEE_FAILURE = "修改服务费失败";
	public static final String MODIFY_SERVICE_FEE_SUCCESS = "修改服务费成功";
	
	public static final String MODIFY_CUSTOMER_COUNT_FAILURE = "修改客户人数失败";
	public static final String MODIFY_CUSTOMER_COUNT_SUCCESS = "修改客户人数成功";

    public static final String MODIFY_BILL_HEADER_FAILURE = "修改单头失败";
    public static final String MODIFY_BILL_HEADER_SUCCESS = "修改单头成功";

    public static final String MODIFY_ITEM_WAITER_FAILURE = "修改促销员失败";
    public static final String MODIFY_ITEM_WAITER_SUCCESS = "修改促销员成功";
	
	public static final String BILL_DISCOUNT_FAILURE = "账单折扣失败";
	public static final String BILL_DISCOUNT_SUCCESS = "账单折扣成功";
	
	public static final String BILL_SINGLE_DISCOUNT_FAILURE = "单品折扣失败";
	public static final String BILL_SINGLED_ISCOUNT_SUCCESS  = "单品折扣成功";
	
	public static final String SURPLUS_DISCOUNT_AMOUNT_FAILURE = "优惠金额查询失败";
	public static final String SURPLUS_DISCOUNT_AMOUNT_SUCCESS = "优惠金额查询成功";
	
	public static final String CANC_BILL_DISCOUNT_FAILURE = "取消账单折扣失败";
	public static final String CANC_BILL_DISCOUNT_SUCCESS = "取消账单折扣成功";
	
	public static final String CANC_BILL_REMARK_FAILURE = "取消整单备注失败";
	public static final String CANC_BILL_REMARK_SUCCESS = "取消整单备注成功";
	
	public static final String GUEST_MSG_FAILURE = "客户留言失败";
	public static final String GUEST_MSG_SUCCESS = "客户留言成功";
	
	public static final String DISH_REMARK_FAILURE = "备注失败";
	public static final String DISH_REMARK_SUCCESS = "备注成功";
	
	public static final String CHANGE_WAITER_FAILURE = "更改服务员失败";
	public static final String CHANGE_WAITER_SUCCESS = "更改服务员成功";

    public static final String CHANGE_DINNER_TYPE_FAILURE= "更改就餐类型失败";
    public static final String CHANGE_DINNER_TYPE_SUCCESS= "更改就餐类型成功";

	public static final String CHANGE_DISH_UNIT_FAILURE = "更改菜品规格失败";
	public static final String CHANGE_DISH_UNIT_SUCCESS = "更改菜品规格成功";
	
	public static final String GIVE_DISH_FAILURE = "奉送菜品失败";
	public static final String GIVE_DISH_SUCCESS = "奉送菜品成功";
	
	public static final String RETREAT_DISH_FAILURE = "退菜失败";
	public static final String RETREAT_DISH_SUCCESS = "退菜成功";
	
	public static final String CHANGE_DISH_COUNT_FAILURE = "修改菜品数量失败";
	public static final String CHANGE_DISH_COUNT_SUCCESS = "修改菜品数量成功";

    public static final String CHANGE_ITEM_ASSIST_NUM_FAILURE = "修改菜品辅助数量失败";
    public static final String CHANGE_ITEM_ASSIST_NUM_SUCCESS = "修改菜品辅助数量成功";

	public static final String CHANGE_DISH_NAME_FAILURE = "修改菜品名称失败";
	public static final String CHANGE_DISH_NAME_SUCCESS = "修改菜品名称成功";
	
	public static final String REMINDER_FOOD_FAILURE = "催菜失败";
	public static final String REMINDER_FOOD_SUCCESS = "催菜成功";
	
	public static final String REMINDER_SINGLE_FAILURE = "单品催菜失败";
	public static final String REMINDER_SINGLE_SUCCESS = "单品催菜成功";
	
	public static final String REMINDER_WHOLE_FAILURE = "整单催菜失败";
	public static final String REMINDER_WHOLE_SUCCESS = "整单催菜成功";
	
	public static final String WAIT_CALL_FAILURE = "等叫失败";
	public static final String NOT_FIND_WAIT_CALL_BILLNO = "未找到等叫的账单";
	public static final String WAIT_CALL_SUCCESS = "等叫成功";
	
	public static final String SINGLE_WAIT_CALL_FAILURE = "单品等叫失败";
	public static final String SINGLE_WAIT_CALL_SUCCESS = "单品等叫成功";
	
	public static final String WHOLE_WAIT_CALL_FAILURE = "整单等叫失败";
	public static final String WHOLE_WAIT_CALL_SUCCESS = "整单等叫成功";
	
	public static final String DAY_END_FAILURE = "打烊失败";
	public static final String DAY_END_SUCCESS = "打烊成功";
	
	public static final String DATATRANS_FAILURE = "数据上传失败";
	public static final String DATATRANS_SUCCESS = "数据上传成功";
	
	public static final String QUERY_WAITER_FAILURE = "查询服务员失败";
	public static final String QUERY_WAITER_SUCCESS = "查询服务员成功";
	
	public static final String CHANGE_SHIFT_FAILURE = "交班失败";
	public static final String CHANGE_SHIFT_SUCCESS = "交班成功";
	
	public static final String GET_SHIFT_DATA_FAILURE = "获取交班数据失败";
	public static final String GET_SHIFT_DATA_SUCCESS = "获取交班数据成功";
	
	public static final String CANC_BILL_FAILURE = "整单取消失败";
	public static final String CANC_BILL_SUCCESS = "整单取消成功";
	public static final String CANC_BILL_SUCCESS_PRING_FAILURE = "整单取消成功,打印失败";
	
	public static final String RECOVERY_BILL_FAILURE = "账单恢复失败";
	public static final String RECOVERY_BILL_SUCCESS = "账单恢复成功";
	
	public static final String GET_GUQINGCOUNT_FAILURE = "获取沽清数量失败";
	public static final String GET_GUQINGCOUNT_SUCCESS = "获取沽清数量成功";
	
	public static final String CHANG_GUQINGCOUNT_FAILURE = "更改沽清数量失败";
	public static final String CHANG_GUQINGCOUNT_SUCCESS = "更改沽清数量成功";
	
	public static final String GET_DISH_FAILURE = "获取实时菜品失败";
	public static final String GET_DISH_SUCCESS = "获取实时菜品成功";
	
	public static final String CALL_UP_DISH_FAILURE = "起菜失败";
	public static final String NOT_FIND_CALL_UP_BILLNO = "未找到起菜的账单";
	public static final String CALL_UP_DISH_SUCCESS = "起菜成功";
	
	public static final String SINGLE_CALL_UP_DISH_FAILURE = "单品起菜失败";
	public static final String SINGLE_CALL_UP_DISH_SUCCESS = "单品起菜成功";
	
	public static final String WHOLE_CALL_UP_DISH_FAILURE = "整单起菜失败";
	public static final String WHOLE_CALL_UP_DISH_SUCCESS = "整单起菜成功";
	
	public static final String MODIFY_DISH_ZFKW_FAILURE = "修改单品做法失败";
	public static final String MODIFY_DISH_ZFKW_SUCCESS = "修改单品做法成功";
	
	public static final String MODIFY_SINGLE_ZFKW_FAILURE = "修改单品备注失败";
	public static final String MODIFY_SINGLE_ZFKW_SUCCESS = "修改单品备注成功";
	
	public static final String MODIFY_WHOLE_ZFKW_FAILURE = "修改整单备注失败";
	public static final String MODIFY_WHOLE_ZFKW_SUCCESS = "修改整单备注成功";
	
	public static final String NOT_FIND_GIVE_ITEM = "未找到奉送的菜品";
	public static final String GIVE_COUNT_MORE_THAN_EXISTS = "奉送数量大于现有菜品数量";
	
	public static final String HAVING_RETREAT_FOOD = "该菜品已经是退菜";
	public static final String HAVING_GIVE_FOOD = "该菜品已经奉送";
	public static final String HAVING_CANC_FOOD = "该菜品已经取消";
	
	public static final String NOT_PERMIT_CANCBILL_STATE = "该账单已经做过取消，不允许再次取消账单";
	
	public static final String NOT_MATCHING_MODE = "所传参数mode的值不匹配";
	
	public static final String SET_INVOICE_SUCCESS = "开发票成功";
	public static final String SET_INVOICE_FAILURE = "开发票失败";
	
	public static final String QUERY_INVOICE_SUCCESS = "查询账单发票成功";
	public static final String QUERY_INVOICE_FAILURE = "查询账单发票失败";
	
	public static final String BASIC_VERSION_FAILURE = "版本验证失败";
	public static final String BASIC_VERSION_SUCCESS = "版本验证成功";
	
	public static final String QUERY_LOCK_TABLE_SUCCESS = "查询桌位锁台信息成功";
	public static final String QUERY_LOCK_TABLE_FAILURE = "查询桌位锁台信息失败";
	public static final String NOT_EXIST_LOCK_TABLE = "未查询到桌位锁台信息";
	
	public static final String CANC_GIVE_ITEM_SUCCESS = "取消菜品成功";
	public static final String CANC_GIVE_ITEM_FAILURE = "取消菜品失败";
	
	public static final String CHECK_POS_LOGIN_SUCCESS = "查询机台签到成功";
	public static final String CHECK_POS_LOGIN_FAILURE = "查询机台签到失败";
	
	public static final String CANC_SERVICE_SUCCESS = "取消服务费成功";
	public static final String CANC_SERVICE_FAILURE = "取消服务费失败";
	
	public static final String PRINT_BILL_SUCCESS = "打印账单成功";
	public static final String PRINT_BILL_FAILURE = "打印账单失败";
	
	public static final String CUSTOMER_RECHARGE_CANCEL_SUCCESS = "第三方充值撤销成功";
	public static final String CUSTOMER_RECHARGE_CANCEL_FAILURE = "第三方充值撤销失败";
	
	public static final String QUERY_ITEM_SORT_SUCCESS = "菜品排行成功";
	public static final String QUERY_ITEM_SORT_FAILURE = "菜品排行失败";
	
	public static final String CHECK_AUTHRITORY_SUCCESS = "检验权限成功";
	public static final String CHECK_AUTHRITORY_FAILURE = "检验权限失败";
	
	public static final String PAYMENT_ALIPAY_PRECREATE = "预下单获取二维码失败";
	public static final String PAYMENT_ALIPAY_BARCODE = "条码支付失败";
	public static final String PAYMENT_ALIPAY_QUERY = "查询订单信息失败";
	public static final String PAYMENT_ALIPAY_CANCLE = "取消订单失败";
	public static final String PAYMENT_ALIPAY_REFUND = "退款失败";
	public static final String PAYMENT_ALIPAY_REORDER = "重新下单失败";
	public static final String PAYMENT_WECHAT_UNIFIEDORDER = "微信统一下单失败";
	public static final String PAYMENT_WECHAT_MICROPAY = "微信刷卡支付失败";
	public static final String PAYMENT_WECHAT_ORDERQUERY = "微信订单查询失败";
	public static final String PAYMENT_WECHAT_CLOSEORDER = "微信关闭订单失败";
	public static final String PAYMENT_WECHAT_REFUNDORDER = "微信退款失败";
	public static final String PAYMENT_WECHAT_REFUNDQUERY = "微信退款查询失败";
	
	
	public static final String CANC_DAY_END_SUCCESS = "取消打烊成功，请重新登录";
	public static final String CANC_DAY_END_FAILURE = "取消打烊失败";
	
	public static final String GIVE_SUGGEST_SUCCESS = "提交评价成功";
	public static final String GIVE_SUGGEST_FAILURE = "提交评价失败";
	
	public static final String NOT_DAY_END_YET = "当天还未做过打烊";
	public static final String ALREADY_DAYCOUNT = "当天已经做过日结";
	public static final String ALREADY_DAYCOUNTING = "当天正在做日结";
	public static final String DAY_END_OFFNET_SUCCESS = "打烊成功,但有未上传数据";
	public static final String DAY_END_NOTUPLOAD_FAILURE = "打烊失败,当天数据未完成上传,预计完成时间:";
	
	public static final String APP_VERSION_FAILURE = "APP版本验证失败";
	public static final String APP_VERSION_SUCCESS = "APP版本验证成功";
	
	public static final String GET_MAX_SERIAL_SUCCESS = "获取最大序列成功";
	public static final String GET_MAX_SERIAL_FAILURE = "获取最大序列失败";
	
	public static final String GET_ORGAN_ID_SUCCESS = "获取机构id成功";
	public static final String GET_ORGAN_ID_FAILURE = "获取机构id失败";
	
	public static final String LOAD_BILL_SUCCESS = "查询暂存账单成功";
	public static final String LOAD_BILL_FAILURE = "查询暂存账单失败";
	
	public static final String CLEAR_BILl_ITEM_SUCCESS = "删除账单明细数据成功";
	public static final String CLEAR_BILL_ITEM_FAILURE = "删除账单明细数据失败";
	
	public static final String CHECK_DAY_BEGAIN_SUCCESS = "日始成功,请重新登录";
	public static final String CHECK_DAY_BEGAIN_FAILURE = "日始失败";
	
	public static final String DEVICE_APPLY_SUCCESS = "设备申请成功";
	public static final String DEVICE_APPLY_FAILURE = "设备申请失败";
	
	public static final String DEVICE_SELECT_SUCCESS = "查询设备成功";
	public static final String DEVICE_SELECT_FAILURE = "查询设备失败";
	
	public static final String CHANGET_SUCCESS = "盲交班成功";
	public static final String CHANGET_FAILURE= "盲交班失败";
	
	public static final String VIP_PRICE_SUCCESS = "会员价查询成功";
	public static final String VIP_PRICE_FAILURE= "会员价查询失败";
	
	public static final String CHECK_CHANGSHIFT_SUCCESS = "交班查询成功";
	public static final String CHECK_CHANGSHIFT_FAILURE= "交班查询失败";
	
	public static final String HAD_RETREAT_ITEM = "该菜品已经退菜，不允许进行再次退菜";
	
	public static final String QUERY_CHECK_ITEM_SUCCESS = "账单菜品查询成功";
	public static final String QUERY_CHECK_ITEM_FAILURE = "账单菜品查询失败";
	
	public static final String SINGLE_RETREAT_ITEM_SUCCESS = "单品退菜成功";
	public static final String SINGLE_RETREAT_ITEM_FAILURE = "单品退菜失败";
	
	public static final String OPEN_SYY_SUCCESS = "收银员签到成功";
	public static final String OPEN_SYY_FAILURE = "收银员签到失败";
	
	public static final String CLOSE_SYY_SUCCESS = "收银员签退成功";
	public static final String CLOSE_SYY_FAILURE = "收银员签退失败";
	
	public static final String DELETE_VIP_PRICE_SUCCESS = "取消会员价成功";
	public static final String DELETE_VIP_PRICE_FAILURE = "取消会员价失败";
	
	public static final String QUERY_KSSY_SUCCESS = "查询签到成功";
	public static final String QUERY_KSSY_FAILURE = "查询签到失败";
	
	public static final String ORDER_PAYMENT_SUCCESS = "结账成功";
	public static final String ORDER_PAYMENT_FAILURE = "结账失败";
	
	public static final String GET_SYSMODULE_FAILURE = "查询权限列表失败";
	public static final String GET_SYSMODULE_SUCCESS = "查询权限列表成功";
	
	public static final String CLOSE_TABLE_FAILURE = "关闭桌位失败";
	public static final String CLOSE_TABLE_SUCCESS = "关闭桌位成功";
	
	public static final String OPEN_TABLE_ORDER_DISH_FAILURE = "开台、下单失败";
	public static final String OPEN_TABLE_ORDER_DISH_SUCCESS = "开台、下单成功";
	
	public static final String CUSTOMER_CREDIT_PLUS_FAILURE = "设置会员失败";
	public static final String CUSTOMER_CREDIT_PLUS_SUCCESS = "设置会员成功";

    public static final String CUSTOMER_UNBIND_SUCCESS = "取消绑定会员成功";
    public static final String CUSTOMER_UNBIND_FAILURE= "取消绑定会员失败";


	public static final String DISH_OUT_SUCCESS = "出餐成功";
	public static final String DISH_OUT_FAILURE = "出餐失败";

	public static final String REQUEST_MEMBER_SERVICE_FAILURE = "请求会员服务错误";
	
	public static final String ADD_PRINT_COUNT_FAILURE = "设置打印次数失败";
	public static final String ADD_PRINT_COUNT_SUCCESS = "设置打印次数成功";

	public static final String QUERY_ITEM_DETAILS_SUCCESS = "账单菜品详细查询成功";
	public static final String QUERY_ITEM_DETAILS_FAILURE = "账单菜品详细查询失败";
	
	public static final String BILL_PAYMENT_RECORD_FAILURE = "获取账单付款方式失败";
	
	public static final String CARD_RECHARGE_RECORD_FAILURE = "获取会员充值记录失败";
	
	public static final String OPT_BIND_DEVICES_ADD_SUCCESS = "收银员绑定机台成功";
	public static final String OPT_BIND_DEVICES_ADD_FAILURE = "收银员绑定机台失败";
	
	public static final String OPT_BIND_DEVICES_FIND_FAILURE = "查询收银员绑定机台失败";
	
	
	public static final String OPT_BIND_DEVICES_CANCLE_SUCCESS = "取消收银员绑定机台成功";
	public static final String OPT_BIND_DEVICES_CANCLE_FAILURE = "取消收银员绑定机台失败";
	
	public static final String FIND_KVS_BILL_FAILURE = "账单查询失败";
	public static final String FIND_KVS_BILL_SUCCESS = "账单查询成功";
	
	public static final String QUERY_PRINTER_STATE_SUCCESS = "查询打印机状态成功";
	public static final String QUERY_PRINTER_STATE_FAILURE = "查询打印机状态失败";
	
	public static final String FIND_KVS_MEAL_CONSUME_FAILURE = "备餐时长查询失败";
	public static final String FIND_KVS_MEAL_CONSUME_SUCCESS = "备餐时长查询成功";
	
	
	public static final String Get_POS_OPT_FAILURE = "查询营业收银员,收款机台失败";
	
	public static final String CASHIER_RECEIVE_TOTAL_FAILURE = "交款统计失败";
	
	public static final String POS_CASHIER_RECEIVE_ADD_SUCCESS = "交款成功";
	public static final String POS_CASHIER_RECEIVE_ADD_FAILURE = "交款失败";
	
	public static final String POS_CASHIER_RECEIVE_FIND_FAILURE = "查询交款流水失败";
	
	public static final String CHECK_RECEIVE_SUCCESS = "检查交款差额成功";
	public static final String CHECK_RECEIVE_FAILURE = "检查交款差额失败";
	
	public static final String GET_STORE_OPT_STATE_FAILURE = "获取门店日始打烊及收银员签到签退状态失败";
	
	public static final String EXTRACT_BILLS_FAILURE = "抽大钞失败";
	public static final String EXTRACT_BILLS_SUCCESS = "抽大钞成功";
	
	public static final String GET_DETAIL_CHANGSHIFT_SUCCESS = "历史交班明细查询成功";
	public static final String GET_DETAIL_CHANGSHIFT_FAILURE= "历史交班明细查询失败";
	
	public static final String GET_EXTRACT_BILLS_FAILURE = "查询抽大钞历史失败";
	public static final String GET_EXTRACT_BILLS_SUCCESS = "查询抽大钞历史成功";
	
	public static final String SELF_RETRY_PAYMENT_REPEAT_FAILURE = "付款重试失败";
	public static final String SELF_RETRY_PAYMENT_REPEAT_SUCCESS = "付款重试成功";
	
	public static final String SELF_PAYMENT_COMPLETE_SUCCESS = "取消付款成功";
	public static final String SELF_PAYMENT_CANCEL_SUCCESS = "付款完成";
	
	public static final String PSY_SCCN_JD = "请求扫一扫发送失败";
	

	public static final String TOTAL_BILL_INFO_FAILURE = "查询营业统计信息失败";
	public static final String TOTAL_BILL_INFO_SUCCESS = "查询营业统计信息成功";
	
	public static final String SAVE_BANK_TRANDING_LOG_FAILURE = "银行支付接口保存记录失败";
	public static final String SAVE_BANK_TRANDING_LOG_SUCCESS = "银行支付接口保存记录成功";
	public static final String QUERY_BANK_TRANDING_LOG_FAILURE = "银行支付接口查询记录失败";
	public static final String QUERY_BANK_TRANDING_LOG_SUCCESS = "银行支付接口查询记录成功";
	public static final String CODE_NULL_LOG = "数据集为空"; 

	
	public static final String GET_PREPAY_BARCODE_FAILURE = "获取二维码失败";
	public static final String GET_PREPAY_BARCODE_SUCCESS = "获取二维码成功";
	
	public static final String THIRD_COUPONS_FAILURE = "优惠劵验证失败";
	public static final String THIRD_COUPONS_SUCCESS = "优惠劵验证成功";
	/**本地验证优惠卷相关**/
	public static final String LOCAL_COUPONS_FAILURE="本地处理优惠券失败";
	public static final String LOCAL_COUPONS_SUCCESS="本地处理优惠券成功";
//	public static final String QUERY_LOCAL_COUPONS_FAILURE="本地优惠券查询失败";
//	public static final String BILL_PAYMENT_CANCEL_FAILURE = "取消付款失败";
//	public static final String LOCAL_COUPONS_BILL_MONEY_MES ="订单金额不正确";
//	public static final int LOCAL_COUPONS_AMOUNT_CODE =2;
//	public static final String LOCAL_COUPONS_AMOUNT_MES ="抵扣金额不能大于订单金额";
//	public static final int LOCAL_COUPONS_NO_ITEM_CODE =3;
//	public static final String LOCAL_COUPONS_NO_ITEM_MES ="此卷没有兑换的菜";
//	public static final int LOCAL_COUPONS_START_TIME_CODE=5;
//    public static final int LOCAL_COUPONS_BB_OVERDUE_CODE=6;
//    public static final String LOCAL_COUPONS_BB_OVERDUE_EMS="优惠券已过期";
//    public static final int LOCAL_COUPONS_NOT_ENOUGH_CODE=7;
//    public static final int LOCAL_COUPONS_DAY_EXPIRED_CODE=8;
//    public static final String LOCAL_COUPONS_DAY_EXPIRED_EMS="优惠卷使用周期已到";
//    public static final String LOCAL_COUPONS_NOT_ENOUGH_EMS="该账金额不满足兑换条件";
//	public static final String LOCAL_COUPONS_START_TIME ="优惠卷时间未开始";
	public static final String TITLE = "前台营业";
	
	/**支付宝支付的logo*/
	public static final String LOGO_ALIPAY = "alipay_logo.png";
	/**微信支付的logo*/
	public static final String LOGO_WECHAT = "wechat_logo.png";

	public static final String GET_PAYMENT_WAY_MESSAGE_IS_NULL ="请求总部连接超时";
    public static final String PAY_ORDER_BY_CUSTOMER_FAILURE = "被扫支付失败";
    
    public static final String AMOUNT_FAILURE = "金额不符";
	public static final String QUERY_PAY_STATE_FAILURE = "支付状态查询失败";
	public static final String QUERY_PAY_REFUND_FAILURE = "退款状态查询失败";
	public static final String REFUND_PAY_ORDER_FAILURE = "支付退款失败";
	public static final String CANCEL_PAY_ORDER_FAILURE = "取消支付失败";
	public static final String PAY_ORDER_BY_CUSTOMER_SUCCESS = "被扫支付成功";
	public static final String COPY_POS_BILL_FAILURE = "复制账单失败";
	
	
	
	public static final String GET_CRM_TOKEN_SUCCESS = "获取token成功";
	public static final String GET_CRM_TOKEN_FAILURE = "获取token失败";
	
	
	public static final String GET_CRM_FK_SUCCESS = "CRM发卡成功";
	public static final String GET_CRM_FK_FAILURE = "CRM发卡失败";
	
	public static final String GET_CRM_CZ_SUCCESS = "CRM充值成功";
	public static final String GET_CRM_CZ_FAILURE = "CRM充值失败";
	
	public static final String GET_CRM_CANCEL_SUCCESS = "CRM充值撤销成功";
	public static final String GET_CRM_CANCEL_FAILURE = "CRM充值撤销失败";
	
	public static final String PAY_MENT_CRM_SUCCESS = "CRM付款成功";
	public static final String PAY_MENT_CRM_FAILURE = "CRM付款失败";
	
	public static final String CANCEL_PAYMENT_SUCCESS = "CRM撤销付款成功";
	public static final String CANCEL_PAYMENT_FAILURE = "CRM撤销付款失败";
	
	
	public static final String CRM_MEMBER_SUCCESS = "CRM用户查询成功";
	public static final String CRM_MEMBER_FAILURE = "CRM用户查询失败";
	
	
	public static final String QUICK_PASS_SUCCESS = "秒付查询成功";
	public static final String QUICK_PASS_FAILURE = "秒付查询失败";
	
	public static final String QUICK_PASS_DEL_SUCCESS = "秒付删除成功";
	public static final String QUICK_PASS_DEL_FAILURE = "秒付删除失败";
	
	public static final String WRONG_THIRD_PAYMENT_SUCCESS = "异常支付流水查询成功";
	public static final String WRONG_THIRD_PAYMENT_FAILURE = "异常支付流水查询失败";

	public static final String GET_OPT_NAME_SUCCESS = "获取操作员成功";
	public static final String GET_OPT_NAME_FAILURE = "获取操作员失败";

	/**
	 * 业态   正餐
	 */
	public static final Object FORMAT_STATE_ZC = "1";
	public static final String CHANG_TASTE_FAILURE = "修改备注失败";
	public static final String CHANG_TASTE_SUCCESS = "修改备注成功";

	/**
	 *查询账单总额
	 */
	public static final String FIND_TOTAL_SUCCESS = "查询账单总额成功";
	public static final String FIND_TOTAL_FAILURE = "查询账单总额失败";

    /**
     * 查询收银员未交款金额
     */
	public static final String GET_CASHIER_LIMIT_VLIDATE_SUCCESS = "查询收银员未交款金额成功";
	public static final String GET_CASHIER_LIMIT_VLIDATE_FAILURE = "查询收银员未交款金额失败";
	/**
	 * 更改菜品状态
	 */
	public static final String UPDATE_ITEM_SERVED_SUCCESS = "更改菜品状态成功";
	public static final String UPDATE_ITEM_SERVED_FAILURE = "更改菜品状态失败";

    /**
     * 会员充值查询
     */
	public static final String FIND_CUSTOMER_SUCCESS = "会员充值查询成功";
	public static final String FIND_CUSTOMER_FAILURE = "会员充值查询失败";

	public static final String CUSTOMER_CARD_RECHARGE_SUCCESS = "会员充值取消付款成功";
	public static final String CUSTOMER_CARD_RECHARGE_FAILURE = "会员充值取消付款失败";

	/**
	 * 整单模式
	 */
	public static final Object MODE_ALL = "1";
	/**
	 * 单品模式
	 */
	public static final Object MODE_SINGLETON = "2";
	
	/** 账务基础表格平衡  账单应收不平衡 错误码
	 * 
	 * 
	 */
	public static final String POS_FINANCE_BASEDATA_BAD_CODE_BILL_AMOUNT = "001";
	
	/** 账务基础表格平衡  账单付款金额不平衡 错误码
	 * 
	 * 
	 */
	public static final String POS_FINANCE_BASEDATA_BAD_CODE_PAYMENT_AMOUNT = "002";
	
	
	/**
	 * 账务基础表格平衡   账单实收不平衡 错误码
	 */
	public static final String POS_FINANCE_BASEDATA_BAD_CODE_PAYMENT_AMOUNT_ITEM = "003";
	
	/**
	 * 账务基础表格平衡   付款表中付款金额不平衡 错误码
	 */
	public static final String POS_FINANCE_BASEDATA_BAD_CODE_PAYMENT_AMOUNT_PAYMENT = "004";
	
	public static final String DEVICES_REFRESH_FAILURE = "设备刷新数据操作失败";
	
	/**
	 * 删除日志标志
	 */
	public static final String IS_DEL_BOH_LOG = "logger.delete.isdelete";
	
	/**
	 * 删除日志天数
	 */
	public static final String DEL_BOH_LOG_DAYS = "logger.delete.periodic.days";
	/**
	 * 拉取相关
	 */
	public static final String BILL_BOOKED_PLL = "该预点订单已拉取";
	public static final String BILL_BOOKED_PLL_SUCCESS ="预点订单拉取成功";
	public static final String BILL_BOOKED_FAILURE = "预点单操作失败";
	public static final String BILL_BOOKED_FAILURE_NUM = "没有该预点单";

	/**
	 *美大秒付
	 */
	public static final String				QUICK_PASS_URL											= "saas.url";
//	public static final String				SECONDS_URL												= "secondsURL";
	public static final String				REFUND_URL												= "saas.url";
	public static final String				SOLDOUT_URL												= "soldout.dataupload.url";

	public static final int					PAYMENT_STATUS_PAYING_CODE								= PosErrorCode.BILL_PAYMENT_STATE_PAYING_ERROR.getNumber();
	
	/**
	 * 第三方异常:订单不存在
	 */
	public static final int				THIRD_PAYMENT_ORDER_NOT_EXIST_CODE						= 6789;

	/**
	 * 会员卡异常:订单不存在
	 */
	public static final int				CARD_PAYMENT_ORDER_NOT_EXIST_CODE						= CrmErrorCode.OLDBILLCODE_NOTEXIST_ERROE.getNumber();

	/**
	 * 团体挂账异常:订单不存在
	 */
	public static final int				INCORPORATION_PAYMENT_ORDER_NOT_EXIST_CODE				= CrmErrorCode.INCORPORATION_GZLIST_NOTEXIST_ERROR.getNumber();

	public static final int					THIRD_PAYMENT_MAX_QUERY_COUNT							= ThirdPaymentOrderRunnable.QUERY_COUNT_NUM;

	public static Map<String, String>	constantMap												= new HashMap<String, String>();

	public static Map<String, String> getConstantMap()
	{
		return constantMap;
	}

	public static void setSystemMap(Map<String, String> constantMap)
	{
		Constant.constantMap = constantMap;
	}
	
	/**
	 * 取单号
	 */
	public static final String POS_BILL_DISH_CODE = "pos_bill_dish_code";


	/**
	 * 菜品状态--待生产
	 */
	public static final Integer DISH_STATE_NOTMAKE = 0;

	/**
	 * 菜品状态--已生产
	 */
	public static final Integer DISH_STATE_MAKED = 1;

	/**
	 * 菜品状态--已出餐
	 */
	public static final Integer DISH_STATE_OUT = 2;

	/**
	 * 菜品状态--已完成
	 */
	public static final Integer DISH_STATE_COMPLETE = 3;

	/**
	 * 未生产菜品提示语
	 */
	public static final String  DISH_CANNOT_MOP = "您当前还有菜品未生产，不能销单";
	/**
	 * 签账卡用户图片
	 */
	public static final String DEBIT_USER_IMAGE              ="debitUserImage";

	public static final String FIND_ACTIVITY_INFO_FAILURE = "请求折扣方案活动失败";
	public static final String FIND_ACTIVITY_INFO_SUCCESS = "请求折扣方案活动成功";
	public static final String JOIN_IN_ACTIVITY_FAILURE = "使用折扣方案活动失败";
	public static final String JOIN_IN_ACTIVITY_SUCCESS = "使用折扣方案活动成功";

	public static final String STORE_HEAD = "门店总部";
	public static final String NOT_DAY_COUNT = "未日结，";
	public static final String SAAS_DAY_COUNT = "请先在SAAS云后台完成日结！";

	public static final String COMBO_SET_MEAL_FAILURE ="使用组合套餐失败";
	public static final String COMBO_SET_MEAL_SUCCESS ="使用组合套餐成功";

	public static final String POS_CLOUD_LOCK_SUCCESS ="锁单成功";
	public static final String POS_CLOUD_UNLOCK_SUCCESS ="解锁成功";
	public static final String POS_CLOUD_LOCK_FAILURE ="锁定或解锁失败";

	public static final String POS_CLOUD_CLOSED_SUCCESS ="关单成功";
	public static final String POS_CLOUD_CLOSED_FAILURE ="关单失败";

	/**默认保留尾数  */
	public static final int				DEFAULT_SCALE		= 4;
	/**金额保留尾数  */
	public static final int				AMOUNT_SCALE		= 2;

	public static final String VALIDATE_BILL_FAILURE = "验证门店账单信息失败";
	public static final String VALIDATE_BILL_SUCCESS = "验证门店账单信息成功";

    public static final String QUERY_ITEM_LIST_SUCCESS = "查询菜品流水成功";
    public static final String QUERY_ITEM_LIST_FAILURE = "查询菜品流水失败";


	public static final String SINGLE_CANCEL_DISCOUNT_FAILURE = "单品取消折扣失败";

	public static final String OPEN_CASHBOX_SUCCESS = "打开钱箱成功";
	public static final String OPEN_CASHBOX_FAILURE = "打开钱箱失败";
	
	public static final String QUERY_MEMBER_HISTORYBILL_SUCCESS = "会员查询历史账单成功";
	public static final String QUERY_MEMBER_HISTORYBILL_FAILURE = "会员查询历史账单失败";
	
	public static final String QUERY_MEMBER_HISTORYBILLDETAIL_SUCCESS = "会员查询历史账单明细成功";
	public static final String QUERY_MEMBER_HISTORYBILLDETAIL_FAILURE = "会员查询历史账单明细失败";
	
	public static final String QUERY_TABLESTATE_AND_MEMBERSTATE_SUCCESS = "查询桌态会员成功";
	public static final String QUERY_TABLESTATE_AND_MEMBERSTATE_FAILURE = "查询桌态会员失败";

	public static final String TAKE_ORDERING_FAILURE         ="D5厨房接口调用失败";

	public static final String UPLOAD_BILL_SUCCESS = "上传订单成功";
	public static final String UPLOAD_BILL_FAILURE = "上传订单失败";

	public static final String WLIFE_ORDER_SUCCESS = "微生活查询桌台订单成功";
	public static final String WLIFE_ORDER_FAILURE = "微生活查询桌台订单异常";

	public static final String WLIFE_ORGAN_SUCCESS = "微生活查询门店状态成功";
	public static final String WLIFE_ORGAN_FAILURE = "微生活查询门店状态异常";

	public static final String WLIFE_BASIC_SUCCESS = "微生活查询基础资料成功";
	public static final String WLIFE_BASIC_FAILURE = "微生活查询基础资料异常";
	public static final String WLIFE_BASIC_ERROR = "该门店没有对接微生活";
	public static final String NOT_EXIST_ORGAN_ERROR = "门店信息不存在";

	public static final String WLIFE_SOLD_OUT_SUCCESS = "微生活查询沽清信息成功";
	public static final String WLIFE_SOLD_OUT_FAILURE = "微生活查询沽清信息异常";
	
	//订单配送
	public static final String FIND_ORDER_DELIVERY_ADDRESS_SUCCESS = "查询订单配送地址成功";
	public static final String FIND_ORDER_DELIVERY_ADDRESS_FAILURE = "查询订单配送地址异常";
	
	public static final String ADD_ORDER_DELIVERY_SUCCESS = "新增配送订单成功";
	public static final String ADD_ORDER_DELIVERY_FAILURE = "新增配送订单异常";
	
	public static final String FIND_EXCEPTION_DELIVERY_ORDER_SUCCESS = "异常配送订单查询成功";
	public static final String FIND_EXCEPTION_DELIVERY_ORDER_FAILURE = "异常配送订单查询失败";

    public static final String CHECK_UNCLOSED_BILL_SUCCESS = "未结账单检查成功";
    public static final String CHECK_UNCLOSED_BILL_FAILURE = "未结账单检查失败";
    
	// 核销接口相关
	public static final String	BILL_CODE_IS_NULL							= "订单号为空";

	public static final String	TAKE_CASHIER_NOT_SIGNIN						= "收银员没有签到";
	public static final String	TAKE_ITEM_SOLD_OUT							= "已经估清";

	//会员标签接口相关
	public static final String  FIND_CUSTOMER_SIGN_SUCCESS = "会员标签查询成功";
	public static final String  FIND_CUSTOMER_SIGN_FAILURE = "会员标签查询失败";
	public static final String  ADD_CUSTOMER_SIGN_SUCCESS = "会员标签添加成功";
	public static final String  ADD_CUSTOMER_SIGN_FAILURE = "会员标签添加失败";
	public static final String  PRAISE_CUSTOMER_SIGN_SUCCESS = "会员标签点赞成功";
	public static final String  PRAISE_CUSTOMER_SIGN_FAILURE = "会员标签点赞失败";

	public static final String BILL_PAYMENT_CANCEL_SUCCESS = "取消付款成功";
	public static final String BILL_PAYMENT_CANCEL_FAILURE = "取消付款失败";
//	public static final String BILL_PAYMENT_CANCEL_REFUND_FAILURE = "取消付款成功,退款异常!";
	
	
	public static final String FIND_THIRD_PAYMENT_REFUND = "退款异常查询";
	
	public static final String THIRD_PAYMENT_REFUND_RETRY = "退款重试";

	public static final String ADD_FORBIDDEN_ITEM_SUCCESS = "保存禁用菜品成功";
	public static final String ADD_FORBIDDEN_ITEM_FAILURE = "保存禁用菜品失败";

	public static final String DEL_FORBIDDEN_ITEM_SUCCESS = "删除禁用菜品成功";
	public static final String DEL_FORBIDDEN_ITEM_FAILURE = "删除禁用菜品失败";

	public static final String NOTIFY_SETMEAL_DETAILS_SUCCESS   = "发送设置套餐数据更改通知成功";
	public static final String NOTIFY_SETMEAL_DETAILS_FAILURE   = "发送设置套餐数据更改通知失败";

	public static final String NOTIFY_FORBIDDEN_ITEM_SUCCESS   = "发送禁用菜品数据更改通知成功";
	public static final String NOTIFY_FORBIDDEN_ITEM_FAILURE   = "发送禁用菜品数据更改通知失败";


	public static final String SETTING_SETMEAL_DETAILS_SUCCESS   = "默认套餐项保存成功";
	public static final String SETTING_SETMEAL_DETAILS_FAILURE   = "默认套餐项保存失败";

	public static final String RECOVER_SETMEAL_DETAILS_SUCCESS   = "默认套餐项恢复成功";
	public static final String RECOVER_SETMEAL_DETAILS_FAILURE   = "默认套餐项恢复失败";
	public static final String SETTING_SHIFTS_TIMES_SUCCESS   = "设置班次限制次数成功";
	public static final String SETTING_SHIFTS_TIMES_FAILURE   = "设置班次限制次数失败";
	
	public static final String CANCEL_PAYMENT_TIME_LIMIT   = "账单付款中,不能取消付款,请查询付款结果!";

	public static final String	AUTO_DAILY_FAILURE			  = "打烊成功,日结不成功";
    public static final String	AUTO_DAILY_SUCCESS			  = "打烊成功,日结完毕";

}
