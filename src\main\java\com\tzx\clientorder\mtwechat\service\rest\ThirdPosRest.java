package com.tzx.clientorder.mtwechat.service.rest;

import com.tzx.base.common.util.StringUtil;
import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.exception.ErrorCode;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.PropertiesLoader;
import com.tzx.framework.common.util.Tools;
import com.tzx.clientorder.mtwechat.bo.InsertOrderService;
import com.tzx.clientorder.mtwechat.bo.MtPosEntranceService;
import com.tzx.clientorder.mtwechat.common.constant.WxOrderType;
import net.sf.json.JSONObject;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Map;

/**
 * Created by qingui on 2018-05-30.
 */

@Controller("ThirdPosRest")
@RequestMapping("/thirdPosRest")
public class ThirdPosRest {

    private static final Logger logger	= Logger.getLogger(ThirdPosRest.class);

    @Resource(name = MtPosEntranceService.NAME)
    private MtPosEntranceService mtPosEntranceService;
    @Resource(name = InsertOrderService.NAME)
    private InsertOrderService insertOrderService;

    @RequestMapping(value = "/wechatOrder")
    public void wechatOrder(HttpServletRequest request, HttpServletResponse response){

        JSONObject result = new JSONObject();
        result.element("success", false);
        response.setContentType("text/html; charset=UTF-8");
        PrintWriter out = null;

        JSONObject p = JSONObject.fromObject("{}");
        Map<String, String[]> map = request.getParameterMap();

        for (String key : map.keySet())
        {
            if (map.get(key)[0] != "")
            {
                p.put(key, map.get(key)[0]);
            }
        }


        String type = p.optString("do");
        if (!StringUtil.hasText(p.optString("app")) || !"ClientWeChatOrder".equals(p.optString("app"))){
            result.element("msg", "app参数有误");
        }else if (!StringUtil.hasText(type)){
            result.element("msg", "do参数为空");
        }else {
            String tenancyId = null;
            if(Constant.systemMap.containsKey("tenent_id"))
            {
                tenancyId = Constant.systemMap.get("tenent_id");
            }

            Integer storeId = Integer.valueOf(Constant.systemMap.get("store_id"));
            if(Constant.systemMap.containsKey("store_id"))
            {
                storeId = Integer.valueOf(Constant.systemMap.get("store_id"));
            }

            switch (type) {
                case WxOrderType.HEART_BEAT:
                    result.element("success", true);
                    result.element("msg", "正常");
                    break;
                case WxOrderType.BASIC_INFO:
                    //拉取门店基础信息
                    try {
                        result = mtPosEntranceService.uploadBaseDataToWsh(tenancyId, storeId);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    break;
                /*case WxOrderType.EXPRESS_CHECKOUT:
                    //下单
                    try {
                        result = insertOrderService.insertOrderRequiresNew(tenancyId, storeId, p);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    break;*/
                default:
                    result.element("msg", "参数有误");
                    break;
            }
        }

        try
        {
            out = response.getWriter();
            out.print(result.toString());
            out.flush();
            out.close();
        }
        catch (Exception e)
        {
        }
        finally
        {
            if (out != null) out.close();
        }
    }

    @RequestMapping(value = "/insertOrder", method = RequestMethod.POST)
    public void insertOrder(HttpServletRequest request, HttpServletResponse response){

        JSONObject result = new JSONObject();
        result.element("success", false);
        response.setContentType("text/html; charset=UTF-8");
        PrintWriter out = null;

        String tenancyId = null;
        if(Constant.systemMap.containsKey("tenent_id"))
        {
            tenancyId = Constant.systemMap.get("tenent_id");
        }

        Integer storeId = Integer.valueOf(Constant.systemMap.get("store_id"));
        if(Constant.systemMap.containsKey("store_id"))
        {
            storeId = Integer.valueOf(Constant.systemMap.get("store_id"));
        }
        JSONObject jsobj = JSONObject.fromObject("{}");
        Map<String, String[]> map = request.getParameterMap();

        for (String key : map.keySet())
        {
            if (map.get(key)[0] != "")
            {
                jsobj.put(key, map.get(key)[0]);
            }
        }


        try
        {
            //下单
            result = insertOrderService.insertOrderRequiresNew(tenancyId, storeId, jsobj);

            out = response.getWriter();
            out.print(result.toString());
            out.flush();
            out.close();
        }
        catch (SystemException se)
        {
            buildSysExceptionData(se, result, "落单失败");
            if (null != result){
                result.put("msg", "落单失败:"+result.optString("msg"));
                try {
                    out = response.getWriter();
                } catch (IOException e) {
                    e.printStackTrace();
                }
                out.print(result.toString());
                out.flush();
                out.close();
            }
        }
        catch (Exception e)
        {
            buildExceptionData(e, result, "落单失败");
            try {
                out = response.getWriter();
            } catch (IOException e1) {
                e1.printStackTrace();
            }
            out.print(result.toString());
            out.flush();
            out.close();
        }
        finally
        {
            if (out != null) out.close();
        }
    }

    public void buildSysExceptionData(SystemException se, JSONObject result, String message)
    {
        ErrorCode error = se.getErrorCode();
        String msg = se.getErrorMsg();
//        String msg = PropertiesLoader.getProperty(String.valueOf(error.getNumber()));
//        Map<String, Object> map = se.getProperties();
//        for (String key : map.keySet())
//        {
//            msg = msg.replace(key, String.valueOf(map.get(key)));
//        }

        //result.setCode(error.getNumber());
        result.put("success", false);
        result.put("msg",msg);
    }

    public void buildExceptionData(Exception e, JSONObject result, String message)
    {
        //result.setCode(Constant.CODE_INNER_EXCEPTION);
        result.put("success", false);
        result.put("msg", message);
        logger.error("系统内部错误：" + ExceptionMessage.getExceptionMessage(e));
        e.printStackTrace();
    }


    /*@RequestMapping(value = "/heartBeat")
    public void heartBeat(HttpServletRequest request, HttpServletResponse response){

        response.setContentType("text/html; charset=UTF-8");
        PrintWriter out = null;

        JSONObject result = new JSONObject();
        result.element("success", true);
        result.element("msg", "正常");

        try
        {
            out = response.getWriter();
            out.print(result.toString());
            out.flush();
            out.close();
        }
        catch (Exception e)
        {
        }
        finally
        {
            if (out != null) out.close();
        }
    }

    @RequestMapping(value = "/basicInfo")
    public void basicInfo(HttpServletRequest request, HttpServletResponse response){

        String tenancyId = null;
        if(Constant.systemMap.containsKey("tenent_id"))
        {
            tenancyId = Constant.systemMap.get("tenent_id");
        }

        Integer storeId = Integer.valueOf(Constant.systemMap.get("store_id"));
        if(Constant.systemMap.containsKey("store_id"))
        {
            storeId = Integer.valueOf(Constant.systemMap.get("store_id"));
        }

        if (Tools.isNullOrEmpty(tenancyId)||Tools.isNullOrEmpty(storeId))
        {
            logger.info("参数为空");
            return;
        }
        response.setContentType("text/html; charset=UTF-8");
        PrintWriter out = null;

        JSONObject result = null;
        try {
            result = mtPosEntranceService.uploadBaseDataToWsh(tenancyId, storeId);
        } catch (Exception e) {
            e.printStackTrace();
        }
        try
        {
            out = response.getWriter();
            out.print(result.toString());
            out.flush();
            out.close();
        }
        catch (Exception e)
        {
        }
        finally
        {
            if (out != null) out.close();
        }
    }*/
}
