package com.tzx.clientorder.wlifeprogram.common.entity;

import com.tzx.framework.common.util.CommonUtil;
import com.tzx.framework.common.util.DoubleHelper;

public class WlifePaymentCouponsItem
{
	private static final int DEFAULT_SCALE = 4;
	
	private String	dishsno;
	private String	name;
	private String	toppings;
	private String	cooks;
	private String	duid;
	private Integer	type;
	private Integer count;
	private Integer price;
	private Integer aprice;
	private Integer origin_price;
	
	public String getName()
	{
		return name;
	}
	public void setName(String name)
	{
		this.name = name;
	}
	public Integer getCount()
	{
		return count;
	}
	public void setCount(Integer count)
	{
		this.count = count;
	}
	public Integer getPrice()
	{
		return price;
	}
	public void setPrice(Integer price)
	{
		this.price = price;
	}
	public Integer getAprice()
	{
		return aprice;
	}
	public void setAprice(Integer aprice)
	{
		this.aprice = aprice;
	}
	public Integer getOrigin_price()
	{
		return origin_price;
	}
	public void setOrigin_price(Integer origin_price)
	{
		this.origin_price = origin_price;
	}
	public String getDishsno()
	{
		return dishsno;
	}
	public void setDishsno(String dishsno)
	{
		this.dishsno = dishsno;
	}
	public String getToppings()
	{
		return toppings;
	}
	public void setToppings(String toppings)
	{
		this.toppings = toppings;
	}
	public String getCooks()
	{
		return cooks;
	}
	public void setCooks(String cooks)
	{
		this.cooks = cooks;
	}
	public String getDuid()
	{
		return duid;
	}
	public void setDuid(String duid)
	{
		this.duid = duid;
	}
	public Integer getType()
	{
		return type;
	}
	public void setType(Integer type)
	{
		this.type = type;
	}
	
	/**转换单位为元
	 * @return
	 */
	public Double getPriceY()
	{
		Double priceY = 0d;
		if (CommonUtil.hv(price))
		{
			priceY = DoubleHelper.div(price.doubleValue(), 100d, DEFAULT_SCALE);
		}
		return priceY;
	}
	/**转换单位为元
	 * @return
	 */
	public Double getApriceY()
	{
		Double apriceY = 0d;
		if (CommonUtil.hv(aprice))
		{
			apriceY = DoubleHelper.div(aprice.doubleValue(), 100d, DEFAULT_SCALE);
		}
		return apriceY;
	}
	/**转换单位为元
	 * @return
	 */
	public Double getOriginPriceY()
	{
		Double originPriceY = 0d;
		if (CommonUtil.hv(origin_price))
		{
			originPriceY = DoubleHelper.div(origin_price.doubleValue(), 100d, DEFAULT_SCALE);
		}
		return originPriceY;
	}
}
