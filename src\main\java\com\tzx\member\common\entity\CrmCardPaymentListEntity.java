package com.tzx.member.common.entity;

import java.io.Serializable;
import java.sql.Timestamp;

public class CrmCardPaymentListEntity implements Serializable
{

	/**
	 * 
	 */
	private static final long	serialVersionUID	= 8817256834660938129L;

	private String				tenancy_id;
	private Integer				id;
	private String				bill_code;
	private String				third_bill_code;
	private Integer				card_id;
	private Integer				payment_id;
	private Double				pay_money;
	private Double				local_currency;
	private String				pay_no;
	private Double				rexchange_rate;
	private Timestamp			store_updatetime;
	private Timestamp			last_updatetime;
	
	public String getTenancy_id()
	{
		return tenancy_id;
	}
	public void setTenancy_id(String tenancy_id)
	{
		this.tenancy_id = tenancy_id;
	}
	public Integer getId()
	{
		return id;
	}
	public void setId(Integer id)
	{
		this.id = id;
	}
	public String getBill_code()
	{
		return bill_code;
	}
	public void setBill_code(String bill_code)
	{
		this.bill_code = bill_code;
	}
	public String getThird_bill_code()
	{
		return third_bill_code;
	}
	public void setThird_bill_code(String third_bill_code)
	{
		this.third_bill_code = third_bill_code;
	}
	public Integer getCard_id()
	{
		return card_id;
	}
	public void setCard_id(Integer card_id)
	{
		this.card_id = card_id;
	}
	public Integer getPayment_id()
	{
		return payment_id;
	}
	public void setPayment_id(Integer payment_id)
	{
		this.payment_id = payment_id;
	}
	public Double getPay_money()
	{
		return pay_money;
	}
	public void setPay_money(Double pay_money)
	{
		this.pay_money = pay_money;
	}
	public Double getLocal_currency()
	{
		return local_currency;
	}
	public void setLocal_currency(Double local_currency)
	{
		this.local_currency = local_currency;
	}
	public String getPay_no()
	{
		return pay_no;
	}
	public void setPay_no(String pay_no)
	{
		this.pay_no = pay_no;
	}
	public Double getRexchange_rate()
	{
		return rexchange_rate;
	}
	public void setRexchange_rate(Double rexchange_rate)
	{
		this.rexchange_rate = rexchange_rate;
	}
	public Timestamp getStore_updatetime()
	{
		return store_updatetime;
	}
	public void setStore_updatetime(Timestamp store_updatetime)
	{
		this.store_updatetime = store_updatetime;
	}
	public Timestamp getLast_updatetime()
	{
		return last_updatetime;
	}
	public void setLast_updatetime(Timestamp last_updatetime)
	{
		this.last_updatetime = last_updatetime;
	}
	
	
}
