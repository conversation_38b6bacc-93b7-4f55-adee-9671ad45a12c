package com.tzx.pos.base.util;


import java.security.MessageDigest;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;

/**
 * Created by ni<PERSON><PERSON><PERSON> on 15/12/3.
 * Copied by cuibaosen on 16/8/4
 * 美大signUtils
 */
public final class MeiDaSignUtils {
	
	public static final String MEIDA_DEAL_DETAIL_URL = "/crm/deal/detail";//美大查询订单详情
	
	public static final String MEIDA_DEAL_CANCEL_URL = "/crm/deal/cancel";//美大订单交易撤销
	
	public static final String MEIDA_POST_URL = "/crm/deal/ack";//支付成功回调通知地址
	public static final String MEIDA_GET_USERINFO_URL = "/crm/member/info";//查询会员基础信息
    /**
     * 根据secretKey和参数列表生成sign
     *
     * @param secret secretKey
     * @param params 参数列表
     */
    public static String createSign(String secret, Map<String, String> params) {
        // 自然排序
        Set<String> sortedParams = new TreeSet<String>();
        sortedParams.addAll(params.keySet());

        StringBuilder strB = new StringBuilder();
        // 排除sign和空值参数
        for (String key : sortedParams) {
            if (key.equalsIgnoreCase("sign")) {
                continue;
            }
            String value = params.get(key);
            if (value != null && !value.isEmpty()) {
                strB.append(key).append(value);
            }
        }

        String source = secret + strB.toString();
        return createSign(source);
    }

    /**
     * 生成新sign
     *
     * @param str 字符串
     * @return String
     */
    private static String createSign(String str) {
        if (str == null || str.length() == 0) {
            return null;
        }
        char hexDigits[] = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};
        try {
            MessageDigest mdTemp = MessageDigest.getInstance("SHA1");
            mdTemp.update(str.getBytes("UTF-8"));

            byte[] md = mdTemp.digest();
            int j = md.length;
            char buf[] = new char[j * 2];
            int k = 0;
            for (int i = 0; i < j; i++) {
                byte byte0 = md[i];
                buf[k++] = hexDigits[byte0 >>> 4 & 0xf];
                buf[k++] = hexDigits[byte0 & 0xf];
            }
            return new String(buf);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 校验请求响应sign
     *
     * @param secret secretKey
     * @param params 参数列表
     */
    public static boolean checkSign(String secret, Map<String, String> params) {
        return params.containsKey("sign") && params.get("sign").equals(MeiDaSignUtils.createSign(secret, params));
    }
}