package com.tzx.pos.base.util;

import org.apache.commons.lang.StringUtils;

import java.util.Map;
import java.util.Random;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> on 2018-10-12
 */
public class ProgressMonitor {


    private static Map<String, Progress> map = new ConcurrentHashMap<>();

    private ProgressMonitor() {
    }


    public static void setProgress(String name, int completed) {
        setProgress(name, completed, "");
    }

    public static synchronized void setProgress(String name, int completed, String msg) {

        Progress p = map.get(name);
        if (null == p) {
            p = new Progress();
        }

        if (completed < 0) {
            p.setCompleted(Progress.DONE);
            p.setState(Progress.EXCEPTION);
            p.setMsg(msg);
        } else if (completed == 0) {
            p.setCompleted(completed);
            p.setState(Progress.READY);
            p.setMsg(msg);
        } else if (completed >= 100) {
            completed = 100;
            p.setCompleted(completed);
            p.setState(Progress.DONE);
            p.setMsg(msg);
        } else {
            p.setCompleted(p.getCompleted() > completed ? p.getCompleted() : completed);
            p.setState(Progress.BUSY);
            p.setMsg(msg);
        }
        map.put(name, p);
    }

    public static Progress getProgress(String name) {

        if (StringUtils.isEmpty(name) || null == map.get(name)) {
            Progress p= new Progress();
            map.put(name,p);
        }

        return map.get(name);
    }

    public static Progress getPhantomProgress(String name){
        //获取当前进度
        Progress progress=getProgress(name);
        if (!progress.isDone()){
            int p=progress.getCompleted();
            Random r=new Random();
            int next=r.nextInt(30)+p;
            if(next>99) {
                setProgress(name,99);
            }else {
                setProgress(name,next);
            }
        }
       return progress;
    }

}

