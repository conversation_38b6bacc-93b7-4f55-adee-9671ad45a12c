package com.tzx.framework.common.exception;

import java.io.PrintStream;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.Map;
import java.util.TreeMap;

import com.tzx.framework.common.util.PropertiesLoader;

public class SystemCustomException extends RuntimeException
{

	private static final long			serialVersionUID	= 1L;

	private CustomErrorCode				customErrorCode;

	private Object[]					formats;

	private final Map<String, Object>	properties			= new TreeMap<String, Object>();

	public static SystemCustomException wrap(Throwable exception, CustomErrorCode customErrorCode)
	{
		if (exception instanceof SystemCustomException)
		{
			return (SystemCustomException) exception;
		}
		else
		{
			return new SystemCustomException(exception.getMessage(), exception, customErrorCode);
		}
	}

	public static SystemCustomException getInstance(CustomErrorCode customErrorCode)
	{
		return getInstance(customErrorCode.getMessage(), customErrorCode);
	}

	public static SystemCustomException getInstance(String message, CustomErrorCode customErrorCode)
	{
		return new SystemCustomException(message, customErrorCode);
	}

	public SystemCustomException(CustomErrorCode customErrorCode)
	{
		this.customErrorCode = customErrorCode;
	}

	public SystemCustomException(String message, CustomErrorCode customErrorCode)
	{
		super(message);
		this.customErrorCode = customErrorCode;
	}

	public SystemCustomException(Throwable cause, CustomErrorCode customErrorCode)
	{
		super(cause);
		this.customErrorCode = customErrorCode;
	}

	public SystemCustomException(String message, Throwable cause, CustomErrorCode customErrorCode)
	{
		super(message, cause);
		this.customErrorCode = customErrorCode;
	}

	public CustomErrorCode getCustomErrorCode()
	{
		return customErrorCode;
	}

	public String getErrorInfo()
	{
		String key = customErrorCode.getClass().getSimpleName() + "_" + customErrorCode;
		return String.format(PropertiesLoader.getProperty(key.toUpperCase()), this.formats);
	}

	public SystemCustomException setErrorCode(CustomErrorCode customErrorCode)
	{
		this.customErrorCode = customErrorCode;
		return this;
	}

	public Map<String, Object> getProperties()
	{
		return properties;
	}

	@SuppressWarnings("unchecked")
	public <T> T get(String name)
	{
		return (T) properties.get(name);
	}

	public SystemCustomException set(String name, Object value)
	{
		properties.put(name, value);
		return this;
	}

	public SystemCustomException set(Map<?, ?> map)
	{
		for (Object key : map.keySet())
		{
			properties.put(String.valueOf(key), map.get(key));
		}
		return this;
	}

	public SystemCustomException formats(Object... formats)
	{
		this.formats = formats;
		return this;
	}

	/**
	 * 将ErrorStack转化为String.
	 */
	public String getStackTraceAsString()
	{
		StringWriter stringWriter = new StringWriter();
		this.printStackTrace(new PrintWriter(stringWriter));
		return stringWriter.toString();
	}

	public void printStackTrace(PrintStream s)
	{
		synchronized (s)
		{
			printStackTrace(new PrintWriter(s));
		}
	}

	public void printStackTrace(PrintWriter s)
	{
		synchronized (s)
		{
			if (!properties.isEmpty())
			{
				s.println("\t-------------------------------");
				for (String key : properties.keySet())
				{
					s.println("\t" + key + "=[" + properties.get(key) + "]");
				}
			}
			if (customErrorCode != null)
			{
				s.println("\t-------------------------------");
				s.println("\t" + customErrorCode + ":" + customErrorCode.getClass().getName());
			}
			s.println("\t-------------------------------");
			StackTraceElement[] trace = getStackTrace();
			for (int i = 0; i < trace.length; i++)
				s.println("\tat " + trace[i]);

			Throwable ourCause = getCause();
			if (ourCause != null)
			{
				ourCause.printStackTrace(s);
			}
			s.flush();
		}
	}
}
