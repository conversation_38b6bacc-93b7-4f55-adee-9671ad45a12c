package com.tzx.log.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

/**
 * <AUTHOR> on 2018-10-26
 */
@Configuration
public class LogConfig {

    public static final String WHITE_LIST="whiteList";
    public static final String BLACK_LIST="blackList";


    @Value("${defaultEnable:true}")
    private String defaultEnable;
    @Value("${logStrategy:whiteList,blackList}")
    private String[] logStrategy;
    @Value("${whiteList:}")
    private String[] whiteList;
    @Value("${blackList:}")
    private String[] blackList;
    @Value("${segmentPattern:yyyy-MM}")
    private String segmentPattern;
    @Value("${keepMonth:2}")
    private String keepMonth;

    public String getDefaultEnable() {
        return defaultEnable;
    }


    public String[] getLogStrategy() {
        return logStrategy;
    }


    public String[] getBlackList() {
        return blackList;
    }


    public String[] getWhiteList() {
        return whiteList;
    }



    public String getSegmentPattern() {
        return segmentPattern;
    }

    public String getKeepMonth() {
        return keepMonth;
    }
}
