package com.tzx.base.listener;

import java.io.Serializable;

import com.tzx.base.service.servlet.AcewillWebScoketMessage;

public class AcewillWebSocketConsumerThreadPoolTask implements Runnable,Serializable{


	/**
	 * 
	 */
	private static final long serialVersionUID = -6024244213867807367L;

	
	private String webScoketMessage;
	
	
	public AcewillWebSocketConsumerThreadPoolTask(String msg){
		this.webScoketMessage = msg;
	}
	
	@Override
	public void run() {
		try {
			new AcewillWebScoketMessage().doExecute(webScoketMessage);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

}
