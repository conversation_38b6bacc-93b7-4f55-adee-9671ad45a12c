package com.tzx.clientorder.acewillwechat.bo;

import java.util.HashMap;
import java.util.Map;

import com.tzx.clientorder.acewillwechat.service.servlet.SocketMessageProcessor;

/**
 * Created by 郑鹏飞 on 2018/3/10.
 */
public interface PaySucessService extends SocketMessageProcessor{

    String	NAME	= SocketMessageProcessor.PAY_SUCCESS;
    
    int DEFAULT_SCALE = 4;
    
    Map<String,String> paymentWayMapping = new HashMap<>();
    
    Map<String,String> posBillMemberTyoeMapping = new HashMap<>();
    
    Map<String,String> posBillCouponTypeMapping = new HashMap<>();
    
    Map<String,String> posBillCouponTypeNameMapping = new HashMap<>();
}
