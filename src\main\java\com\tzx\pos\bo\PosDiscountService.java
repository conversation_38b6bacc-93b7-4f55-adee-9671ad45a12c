package com.tzx.pos.bo;

import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.SystemException;

/**
 * Created by kevin on 2017-08-14.
 */
public interface PosDiscountService extends PosBaseService {

    String	NAME	= "com.tzx.pos.bo.imp.PosDiscountServiceImp";
    /**
     * 奉送权限验证
     * @param param
     * @param result
     * @throws Exception
     */
    @Deprecated
    public void giveAuth(Data param, Data result) throws Exception;

    /**
     * 折让权限验证
     * @param param
     * @param result
     * @throws Exception
     */
    public void discountAuth(Data param, Data result) throws Exception;
    
	/**
	 * 新账单折扣
	 * @param pram
	 * @return
	 * @throws SystemException
	 */
	public Data newBillDiscount(Data param) throws Exception;
	
	/** 单品取消折扣
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public Data singleCancelDiscount(Data param) throws Exception;

	/** 取消折扣和删除会员记录
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public Data cancalDiscountDeleteMeber(Data param) throws Exception;
}
