package com.tzx.clientorder.wxorder.base.util;

import com.tzx.base.common.util.PosPropertyUtil;
import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.HttpRestException;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.*;
import com.tzx.orders.base.util.OrderUtil;
import com.tzx.orders.bo.dto.OrderState;
import com.tzx.orders.bo.imp.OrderSyncServiceImp;
import com.tzx.orders.po.springjdbc.dao.OrdersManagementDao;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.util.JsonUtil;
import net.sf.json.JSONObject;
import org.apache.log4j.Logger;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.util.*;

import static com.tzx.orders.base.constant.Constant.*;
import static com.tzx.pos.base.Constant.constantMap;

/**
 * @Description 发送给微信端通知消息
 * <AUTHOR>
 * @Date 2018-11-10 10:57
 */
public class WxOrderUtil {

    private static Logger logger	= Logger.getLogger(WxOrderUtil.class);

    // 获得计算用的小数点位数
    private static String cmjews = constantMap.get("CMJEWS");
    private static String zdjews = constantMap.get("ZDJEWS");

    //取单通知微信
    private static final String GET_ORDER_URL = "/newwx2/weChatMealHome/doOrderAfter";
    //结账通知微信
    private static final String CLOSE_BILL_URL = "/newwx2/weChatMealHome/doPayementOrderAfter";

    //取消异常订单和转台并台通知微信端取消订单
    private static final String CANCEL_BILL_URL = "/newwx2/weChatMealHome/doCancelOrderAfter";

    //通知微信端，该门店对应支持后付点餐
    private static final String POS_CAN_AFTER_ORDER_URL = "/newwx2/weChatMealHome/updateWechatAfterSet";


    public static JSONObject updateWechatAfterSet(String tenancyId, String storeId){
        JSONObject param = new JSONObject();
        param.put("tenancy_id", tenancyId);
        param.put("param_value", 1);
        param.put("store_id", storeId);
        String reqURL = PosPropertyUtil.getMsg("saas.url") + POS_CAN_AFTER_ORDER_URL;
        logger.info("调用微信通知支持后付点餐入参：" + param.toString());
        JSONObject result = new JSONObject();
        try {
            result = HttpUtil.post(reqURL,param);
        }catch (Exception e){
            logger.error(e.getMessage());
        }
        logger.info("调用微信通知支持后付点餐出参：" + result.toString());
        if(null == result)
        {
            result = new JSONObject();
            result.put("success", false);
            result.put("code", Constant.CODE_NULL_DATASET);
            result.put("msg", "通知微信该门店版本支持后付点餐失败");
        }
        return result;
    }

    /**
     * 拉单通知微信
     * @param tenancyId
     * @param billNum
     * @param orginOrderNum
     * @return
     * @throws HttpRestException
     */
    public static JSONObject getOrder(String tenancyId, String billNum, String orginOrderNum, String flag) throws HttpRestException {
        JSONObject param = new JSONObject();
        param.put("tenancy_id", tenancyId);
        param.put("bill_num", billNum);
        param.put("order_code", orginOrderNum);
        param.put("wechat_order_flag", flag);

        String reqURL = PosPropertyUtil.getMsg("saas.url") + GET_ORDER_URL;
        logger.info("调用微信通知拉单入参：" + param.toString());
        JSONObject result = new JSONObject();
        try {
            result = HttpUtil.post(reqURL,param,3,true);
        }catch (Exception e){
            logger.error(e.getMessage());
        }
        logger.info("调用微信通知拉单出参：" + result.toString());
        if(null == result)
        {
            result = new JSONObject();
            result.put("success", false);
            result.put("code", Constant.CODE_NULL_DATASET);
            result.put("msg", "同步微信订单状态失败");
        }
        return result;
    }

    /**
     * 结账清台通知微信
     * @param tenancyId
     * @param billNum
     * @return
     * @throws HttpRestException
     */
    public static JSONObject closeBill(String tenancyId, String billNum) throws HttpRestException {
        JSONObject param = new JSONObject();
        param.put("tenancy_id", tenancyId);
        param.put("bill_num", billNum);

        String reqURL = PosPropertyUtil.getMsg("saas.url") + CLOSE_BILL_URL;
        //String reqURL = "http://192.168.185.117:8080" + CLOSE_BILL_URL;

        logger.info("调用微信关单清台入参：" + param.toString());
        JSONObject result = HttpUtil.post(reqURL,param,3,true);
        logger.info("调用微信关单清台出参：" + result.toString());
        if(null == result)
        {
            result = new JSONObject();
            result.put("success", false);
            result.put("code", Constant.CODE_NULL_DATASET);
            result.put("msg", "通知微信结账清台失败");
        }
        return result;
    }

    /**
     * 取消订单通知微信
     * @param tenancyId
     * @param num
     * @return
     * @throws HttpRestException
     */
    public static JSONObject cancelOrder(String tenancyId, String num, String type) throws HttpRestException {
        JSONObject param = new JSONObject();
        param.put("tenancy_id", tenancyId);
        if ("billNum".equals(type)){
            param.put("bill_num", num);
        }else {
            param.put("order_code", num);
        }

        String reqURL = PosPropertyUtil.getMsg("saas.url") + CANCEL_BILL_URL;

        logger.info("调用微信取消订单入参：" + param.toString());
        JSONObject result = HttpUtil.post(reqURL,param,3,true);
        logger.info("调用微信取消订单出参：" + result.toString());
        if(null == result)
        {
            result = new JSONObject();
            result.put("success", false);
            result.put("code", Constant.CODE_NULL_DATASET);
            result.put("msg", "通知微信取消订单失败");
        }
        return result;
    }

    public  static void hook(String channel){
        synchronized (OrderUtil.class) {
            OrderSyncServiceImp.NO_NEED_SYNC = true;
            if (THIRD_CHANNEL.contains(channel)) {
                if (!NUMBER_EXACT.equals(cmjews)) {
                    constantMap.put("CMJEWS", NUMBER_EXACT);
                }
                if (!NUMBER_EXACT.equals(zdjews)) {
                    constantMap.put("ZDJEWS", NUMBER_EXACT);
                }
            }
            if (WM_CHANNEL.contains(channel)) {
                OrderSyncServiceImp.NO_NEED_SEND = true;
            }
        }
    }

    public static void unhook(){
        OrderSyncServiceImp.NO_NEED_SYNC = false;
        if (!NUMBER_EXACT.equals(cmjews)) {
            constantMap.put("CMJEWS", cmjews);
        }
        if (!NUMBER_EXACT.equals(zdjews)) {
            constantMap.put("ZDJEWS", zdjews);
        }
    }

    /** 订单状态更新
     * @param orderState
     * @param stateValue
     * @param notifyTarget (0:NONE 1:SAAS 2:POS 4:ALL)
     * @return
     */
    public static OrderState changeOrderState(String tenentId,int storeId,OrderState orderState,String stateValue,int notifyTarget) throws Exception {
        String currentTime = DateUtil.getNowDateYYDDMMHHMMSS();
        switch (stateValue) {
            case ORDER_STATE_TOSHOP:
                orderState.setReceive_time(currentTime);
                orderState.setOrder_state(ORDER_STATE_TOSHOP);
                break;
            case ORDER_STATE_RECEIVE:
                orderState.setTake_time(currentTime);
                orderState.setReceive_time_qd(currentTime);
                orderState.setOrder_state(ORDER_STATE_RECEIVE);
                //添加下单时间
                orderState.setSingle_time(currentTime);
                break;
            case ORDER_STATE_EXCEPTION:
                orderState.setTake_time(currentTime);
                orderState.setReceive_time_qd(currentTime);
                orderState.setOrder_state(ORDER_STATE_EXCEPTION);
                break;
            case ORDER_STATE_COMPLETE_PAYMENT:
                orderState.setOrder_state(ORDER_STATE_COMPLETE_PAYMENT);
                break;
            case ORDER_STATE_DELIVERY:
                orderState.setDistribution_time(currentTime);
                orderState.setReceive_time_distribution(currentTime);
                orderState.setOrder_state(ORDER_STATE_DELIVERY);
                break;
            case ORDER_STATE_CANCEL:
                orderState.setCancellation_time(currentTime);
                orderState.setReceive_time_cancellation(currentTime);
                orderState.setOrder_state(ORDER_STATE_CANCEL);
                orderState.setPayment_state(PAYMENT_STATE_REFUND);
                break;
            case ORDER_STATE_COMPLETE:
                orderState.setFinish_time(currentTime);
                orderState.setReceive_time_finish(currentTime);
                orderState.setOrder_state(ORDER_STATE_COMPLETE);
                break;
            case ORDER_STATE_EXCEPTION_COMPLETE:
                orderState.setFinish_time(currentTime);
                orderState.setReceive_time_finish(currentTime);
                orderState.setOrder_state(ORDER_STATE_EXCEPTION_COMPLETE);
                break;
            case ORDER_STATE_REFUND_RECEIVED:
                orderState.setRefund_recive_time(currentTime);
                orderState.setOrder_state(ORDER_STATE_REFUND_RECEIVED);
                break;
            case ORDER_STATE_REFUND_AGREE:
                orderState.setOrder_state(ORDER_STATE_REFUND_AGREE);
                break;
            case ORDER_STATE_REFUND_DISAGREE:
                orderState.setOrder_state(ORDER_STATE_REFUND_DISAGREE);
                break;
            case ORDER_STATE_REFUND_CANCEL:
                orderState.setOrder_state(ORDER_STATE_REFUND_CANCEL);
                break;
            default:
                break;
        }

        orderState.setDevice_ip(getMac().get("ip"));
        orderState.setDevice_mac(getMac().get("mac"));

        JSONObject param=JSONObject.fromObject(orderState);
        OrdersManagementDao ordersDao= (OrdersManagementDao) SpringConext.getBean(OrdersManagementDao.NAME);
        ordersDao.updateOrderInfo(tenentId, storeId, param);

        if(NOTIFY_ALL==notifyTarget||NOTIFY_SAAS==notifyTarget) {
            try {
                Data result= JsonUtil.JsonToData(OrderUtil.sendOrderState(tenentId, storeId, Type.ORDER, Oper.update, Arrays.asList(orderState)));
                if(WM_CHANNEL.contains(orderState.getChanel())&&com.tzx.pos.base.Constant.CODE_SUCCESS!=result.getCode()){
                    throw new SystemException(PosErrorCode.ORDER_SEND_ORDER_STATE_ERROR);
                }
            }catch (HttpRestException e){
                if(WX02_CHANNEL.equals(orderState.getChanel())){
                    logger.error("总部订单状态更新失败:"+orderState);
                }else {
                    throw e;
                }
            }
        }

        if(NOTIFY_ALL==notifyTarget||NOTIFY_POS==notifyTarget) {
            // 向前台发送消息
            JSONObject msg = new JSONObject();
            msg.put("order_code", Arrays.asList(orderState.getOrder_code()));
            msg.put("order_state", stateValue);
            Comet4jUtil.sendMessage2All(Comet4jUtil.CALLCENTER_ORDER, msg.toString());
        }

        return orderState;
    }

    public static Map<String,String> getMac() {
        Map<String,String> map=new HashMap<>();
        StringBuilder sb = new StringBuilder();
        try{
            InetAddress ip = InetAddress.getLocalHost();
            map.put("ip",ip.getHostAddress());
            NetworkInterface network = NetworkInterface.getByInetAddress(ip);
            byte[] mac = network.getHardwareAddress();
            for (int i = 0; i < mac.length; i++) {
                sb.append(String.format("%02X%s", mac[i], (i < mac.length - 1) ? "-" : ""));
            }
            map.put("mac",sb.toString());
        }catch (Exception e){
            logger.error("获取mac地址失败",e);
        }
        return map;
    }

}
