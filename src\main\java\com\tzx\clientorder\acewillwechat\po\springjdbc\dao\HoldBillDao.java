package com.tzx.clientorder.acewillwechat.po.springjdbc.dao;

import com.tzx.framework.common.util.dao.GenericDao;
import net.sf.json.JSONObject;

import java.util.List;

/**
 * Created by qin-gui on 2018-03-14.
 */
public interface HoldBillDao extends GenericDao {
    String NAME = "com.tzx.clientorder.acewillwechat.po.springjdbc.dao.imp.HoldBillDaoImp";

    /**
     * 根据平台订单号查询该桌位锁单状态、账单号等信息
     * @param tenancyId
     * @param orderNum
     * @return
     */
    JSONObject getBillLockStatus(String tenancyId, String orderNum) throws Exception;

    /**
     * 修改桌态表，修改锁单相关的三个状态
     * @param tenancyId
     * @param storeId
     * @param tableCode
     * @throws Exception
     */
    void updateTableState(String tenancyId, int storeId, String tableCode) throws Exception;

    /**
     * 更新锁单表
     * @param tenancyId
     * @param billNum
     * @param storeId
     * @throws Exception
     */
    void updateBillLock(String tenancyId, String billNum, int storeId) throws Exception;

    /**
     * 查询锁单表微生活的openid
     * @param tenancyId
     * @param storeId
     * @param tableCode
     * @return
     * @throws Exception
     */
    List<JSONObject> getBillLockOpenId(String tenancyId, int storeId, String tableCode) throws Exception;

    /**
     * 根据门店和桌位查询门店未结账订单号
     * @param tenancyId
     * @param storeId
     * @param tableCode
     * @return
     * @throws Exception
     */
    String getBillNum(String tenancyId, int storeId, String tableCode) throws Exception;
}
