package com.tzx.thirdpay.bo.imp;

import java.util.*;

import com.tzx.pos.po.springjdbc.dao.PosPaymentDao;
import net.sf.json.JSONArray;
import org.apache.commons.collections.map.LRUMap;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import com.tzx.base.common.util.PosPropertyUtil;
import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.HttpUtil;
import com.tzx.framework.common.util.ObjectMapper;
import com.tzx.framework.common.util.Tools;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.thirdpay.bo.MeituanCouponsService;

import net.sf.json.JSONObject;

import javax.annotation.Resource;

/** 美团券
 * <AUTHOR>
 *
 */
@Service(MeituanCouponsService.NAME)
public class MeituanCouponsServiceImp implements MeituanCouponsService
{

//	private static final Logger	logger					= Logger.getLogger(PaymentWayServiceImp.class);
	private static final Logger	posThirdPaymentLogger	= Logger.getLogger("pos_third_payment");
	
	private static final String	REQUEST_PATH	=  "/orderRest/post";

	public static final Map<String,Map<String,JSONObject>> coupousInfoMap = Collections.synchronizedMap(new LRUMap(200));

	@Resource(name = PosPaymentDao.NAME)
	protected PosPaymentDao paymentDao;
	
	@Override
	public void couponsPrepare(String tenancyId, int storeId, List<?> paramList, Data result) throws Exception
	{
		// TODO Auto-generated method stub
		if (paramList == null || paramList.isEmpty())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}

		String couponCode = JSONObject.fromObject(paramList.get(0)).optString("coupon_code");

		JSONObject requestJson = new JSONObject();
		requestJson.put("couponCode", couponCode);
		requestJson.put("couponType", SysDictionary.PAYMENT_CLASS_MEIDA_COUPONS_PAY);
		requestJson.put("version", "ng");

		List<JSONObject> requestList = new ArrayList<JSONObject>();
		requestList.add(requestJson);

		Data requestData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
		requestData.setType(Type.THIRD_COUPONS);
		requestData.setOper(Oper.prepare);
		requestData.setData(requestList);

		String paramStr = JSONObject.fromObject(requestData).toString();
//		logger.info("<第三方接口请求体==>" + paramStr);
		posThirdPaymentLogger.info("<第三方接口请求体==>" + paramStr);
		String responseStr = HttpUtil.sendPostRequest(getRequestPath(), paramStr, 3000, 5000);
//		logger.info("<第三方接口返回体==>" + responseStr);
		posThirdPaymentLogger.info("<第三方接口返回体==>" + responseStr);


		Data responseData = this.stringToData(responseStr);

		if (Constant.CODE_SUCCESS == responseData.getCode())
		{
			JSONObject responseJson = JSONObject.fromObject(responseData.getData().get(0));
			String code = responseJson.optString("couponCode");

			JSONObject resultJson = new JSONObject();
			resultJson.put("coupon_code", code);
			resultJson.put("deal_title", responseJson.optString("dealTitle"));
			resultJson.put("deal_value", responseJson.optDouble("dealValue"));
			resultJson.put("count", responseJson.optDouble("count"));
			resultJson.put("min_consume", responseJson.optDouble("minConsume"));
			resultJson.put("coupon_buyprice", responseJson.optDouble("couponBuyPrice"));
			resultJson.put("deal_id", responseJson.optString("dealId"));
			resultJson.put("single_value",responseJson.optString("singleValue"));
			//是否代金券,true代表代金券,false代表套餐券
			if(!responseJson.optBoolean("isVoucher",true)||!responseJson.optBoolean("vourcher",true)){

				String thirdProductId = responseJson.optString("thirdProductId");

				Map<String,JSONObject> infoMap=new HashMap<>();


				JSONObject info=new JSONObject();
				info.put("count",1);
				info.put("item_name",responseJson.optString("rawTitle"));

				infoMap.put(thirdProductId,info);
				coupousInfoMap.put(code,infoMap);


				/*Map<String,String> fdcDishSkuIdsMap=new HashMap<>();

				JSONArray dealMenu = responseJson.optJSONArray("dealMenu");

				if(null==dealMenu){
					String dealMenuStr=responseJson.optString("dealMenu").replace("\"[","").replace("\"]","");
					dealMenu=JSONArray.fromObject(dealMenuStr);
				}

				if(null!=dealMenu&&!dealMenu.isEmpty()){
					JSONArray menus= (JSONArray) dealMenu.get(0);
					for(Object menu:menus){
						JSONObject menuJson = (JSONObject) menu;
						//128为菜品信息
						if("128".equals(menuJson.optString("type"))){
							JSONObject info=new JSONObject();
							info.put("count",menuJson.optDouble("amount"));
							info.put("item_name",menuJson.optString("content"));

							String fdcDishSkuId = menuJson.optString("fdcDishSkuId");

							if(StringUtils.isNotEmpty(fdcDishSkuId)&&fdcDishSkuIdsMap.containsKey(fdcDishSkuId)){
								thirdProductId=fdcDishSkuIdsMap.get(fdcDishSkuId);
							}

							if(StringUtils.isNotEmpty(thirdProductId)){

								infoMap.put(thirdProductId,info);
								coupousInfoMap.put(code,infoMap);

								if(StringUtils.isNotEmpty(fdcDishSkuId)){
									fdcDishSkuIdsMap.put(fdcDishSkuId,thirdProductId);
								}

								thirdProductId=null;

							}else {
								throw SystemException.getInstance(PosErrorCode.DOUYIN_COUPOUS_ERROR)
										.set("{0}", "").set("{1}","菜品【"+menuJson.optString("content")+"】没有绑定收银编码");
							}
						}
					}
				}*/
			}

			List<JSONObject> resultList = new ArrayList<JSONObject>();
			resultList.add(resultJson);
			result.setData(resultList);
		}
		else
		{
			result.setCode(responseData.getCode());
			result.setMsg(responseData.getMsg());
		}
	}

	@Override
	public Data couponsConsume(String tenancyId, int storeId, JSONObject paramJson) throws Exception
	{
		// TODO Auto-generated method stub
		List<JSONObject> requestList = new ArrayList<JSONObject>();
		requestList.add(paramJson);

		Data requestData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
		requestData.setType(Type.THIRD_COUPONS);
		requestData.setOper(Oper.consume);
		requestData.setData(requestList);

		String paramStr = JSONObject.fromObject(requestData).toString();
//		logger.info("<第三方接口请求体==>" + paramStr);
		posThirdPaymentLogger.info("<第三方接口请求体==>" + paramStr);
		String responseStr = HttpUtil.sendPostRequest(getRequestPath(), paramStr, 3000, 5000);
//		logger.info("<第三方接口返回体==>" + responseStr);
		posThirdPaymentLogger.info("<第三方接口返回体==>" + responseStr);

		return this.stringToData(responseStr);
	}

	@Override
	public Data couponsCancel(String tenancyId, int storeId, JSONObject paramJson) throws Exception
	{
		// TODO Auto-generated method stub
		List<JSONObject> requestList = new ArrayList<JSONObject>();
		requestList.add(paramJson);

		Data requestData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
		requestData.setType(Type.THIRD_COUPONS);
		requestData.setOper(Oper.cancle);
		requestData.setData(requestList);

		String paramStr = JSONObject.fromObject(requestData).toString();
//		logger.info("<第三方接口请求体==>" + paramStr);
		posThirdPaymentLogger.info("<第三方接口请求体==>" + paramStr);
		String responseStr = HttpUtil.sendPostRequest(getRequestPath(), paramStr, 3000, 5000);
//		logger.info("<第三方接口返回体==>" + responseStr);
		posThirdPaymentLogger.info("<第三方接口返回体==>" + responseStr);

		return this.stringToData(responseStr);
	}

	@Override
	public Data couponsQuery(String tenancyId, int storeId, JSONObject paramJson) throws Exception
	{
		// TODO Auto-generated method stub
		List<JSONObject> requestList = new ArrayList<JSONObject>();
		requestList.add(paramJson);

		Data requestData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
		requestData.setType(Type.THIRD_COUPONS);
		requestData.setOper(Oper.query);
		requestData.setData(requestList);

		String paramStr = JSONObject.fromObject(requestData).toString();
//		logger.info("<第三方接口请求体==>" + paramStr);
		posThirdPaymentLogger.info("<第三方接口请求体==>" + paramStr);
		String responseStr = HttpUtil.sendPostRequest(getRequestPath(), paramStr, 3000, 5000);
//		logger.info("<第三方接口返回体==>" + responseStr);
		posThirdPaymentLogger.info("<第三方接口返回体==>" + responseStr);

		return this.stringToData(responseStr);
	}

	private Data stringToData(String str) throws Exception
	{
		Data resultData = null;
		if (Tools.hv(str))
		{
			ObjectMapper objectMapper = new ObjectMapper();
			resultData = objectMapper.readValue(str, Data.class);
		}
		else
		{
			resultData = Data.get();
			resultData.setCode(Constant.CODE_CONN_EXCEPTION);
			resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
		}
		return resultData;
	}

	@Override
	public String getRequestPath() throws Exception
	{
		return PosPropertyUtil.getMsg("saas.url") + REQUEST_PATH;
	}

}
