/** 
 * @(#)CodeFormat.java    1.0   2018-04-19
 * 
 * Copyright 北京普照天星科技有限公司. All rights reserved. 
 * PROPRIETARY/CONFIDENTIAL. Use is subject to license terms. 
 */
package com.tzx.framework.common.print;

import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import org.apache.log4j.Logger;

import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;

import net.sf.json.JSONObject;

/**
 * 判断打印类型（0：旧打印，1：java打印，2：Delphi打印）。
 * 
 * <AUTHOR> email:<EMAIL>
 * @version 1.0  2018-04-19
 * @see     
 * @since   JDK7.0
 * @update  
 */
public class JedgePrintJobRunnable implements Runnable {
	
	private static final Logger	logger	= Logger.getLogger(JedgePrintJobRunnable.class);

	@Override
	public void run() {
		try
		{
			// 等待Bean初始化完成
			while(SpringConext.getApplicationContext() == null){
				Thread.sleep(1000);
			}  
			logger.info("判断打印类型......");
			Map<String, String> systemMap = Constant.getSystemMap();
			String tenancyId = systemMap.get("tenent_id");
			DBContextHolder.setTenancyid(tenancyId);
			GenericDao dao = (GenericDao) SpringConext.getApplicationContext().getBean("genericDaoImpl");
			List<JSONObject> listnewprint = dao.query4Json(tenancyId, "SELECT para_value FROM sys_parameter	WHERE para_code = 'new_printer_type' ");
			if(listnewprint.size()>0)
			{
				int printType = listnewprint.get(0).optInt("para_value");
				if(printType == 0 || printType == 1){
					logger.info("启动厨打任务......");
					Executors.newScheduledThreadPool(1).scheduleWithFixedDelay(new PrintJobRunnable(), 70, 1, TimeUnit.SECONDS);
				}
			}
		}
		catch (Exception e)
		{
			logger.error("判断打印类型出现错误:", e);
		}
	}

}
