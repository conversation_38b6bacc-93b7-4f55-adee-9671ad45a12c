package com.tzx.pos.version;

/**
 * Created by jzq1999 on 2017-09-25.
 */
public enum Device {

    NEBULA_BOH("nebula_boh", "门店server"), POS("pos", "门店pos"), MOBILE("mobile", "点菜器"), PRINT("print", "打印服务"),
    E_DAIKE("e_daike", "E待客正餐"), E_DATKE_FAST("e_daike_fast", "E待客快餐"), PAD("pad_diancan", "pad点餐"),
    E_JIAOHAO("e_jiaohao", "快餐取餐叫号"), QINGFENG("qingfeng_kpm", "庆丰包子"), KVS("kvs_android", "安卓KVS"),
    E_ZHIZHU("e_zizhu", "E自助");

    private String name;
    private String code;

    Device(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code) {
        for(Device device:Device.values()) {
            if(code.equals(device.code)) {
                return device.name;
            }
        }

        return null;
    }

}
