package com.tzx.framework.common.exception;

public enum FrameworkErrorCode implements ErrorCode
{

	/**
	 * 角色名称已存在，不能重复
	 */
	ROLE_NAME_EXIST_ERROR(6001),
	/**
	 * 身份证号已存在，不能重复
	 */
	PAPER_NO_EXIST_ERROE(6002),
	/**
	 * 用户名已存在，不能重复
	 */
	USER_NAME_EXIST_ERROE(6003),
	/**
	 * 原密码错误，请验证
	 */
	UPDATE_PASS_ERROE(6004),
	/**
	 * 机构未认证
	 */
	ORGAN_NOT_PASS_ERROE(6005),
	
	/**
	 * 不能指定当前角色为自己的上级
	 */
	YOURSELF_CAN_NOT_BE_YOURFATHER_ERROR(6006);

	private final int	number;

	private FrameworkErrorCode(int number)
	{
		this.number = number;
	}

	public int getNumber()
	{
		return number;
	}

	@Override
	public String getMessage()
	{
		return "";
	}

}
