package com.tzx.pos.version;

import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.log4j.Logger;

import java.io.*;
import java.util.Map;
import java.util.Properties;

/**
 * Created by qinhulin on 3/4/17.
 */
public class NebulaBoh {

    private static Logger logger = Logger.getRootLogger();

    public static String DEFAULT_IDENTIFIER="nebula_boh";
    public static String DEFAULT_VERSION="1.7.0";

    private static Properties consoleProperties = new Properties();
    private static Properties nebulaVersionProperties = new Properties();
    private static Properties posConfigProperties=new Properties();
    private static Properties posSystemParamProperties=new Properties();

    public static final String NEBULA_BOH_HOME = System.getenv("NEBULA_BOH_HOME");
    public static final String NEBULA_BOH_BASE = NEBULA_BOH_HOME + "/main";
    //    public static final String NEBULA_BOH_HOME="/home/<USER>/Downloads/nebula_boh";
//    public static final String NEBULA_BOH_HOME="d:/nebula_boh";
    public static final String VERSION_PROPERTIES = NEBULA_BOH_HOME + "/main/webapps/ROOT/config/version.properties";
    public static final String SYSTEM_PROPERTIES = NEBULA_BOH_HOME + "/main/webapps/ROOT/config/systemParam.properties";
    //public static final String CONSOLE_CONFIG_PROPERTIES = NEBULA_BOH_HOME + "/main/conf/console.properties";
    public static final String CONSOLE_CONFIG_PROPERTIES = NEBULA_BOH_HOME + "/main/conf/new_console.properties";

    public static final String SERVER_XML = NEBULA_BOH_HOME + "/main/conf/server.xml";
    public static final String POS_CONFIG = NEBULA_BOH_HOME + "/main/webapps/ROOT/WEB-INF/classes/pos/pos_config.properties";

    public static final String PSSQL_DATA_FOLDER = NEBULA_BOH_HOME + "/main/data/pgsql/data";
    public static final String WEBAPPS_FOLDER = NEBULA_BOH_HOME + "/main/webapps";
    public static final String UPDATE_SQL = NEBULA_BOH_HOME + "/main/update.sql";
    public static final String PSQL_WINDOWS_BIN = NEBULA_BOH_HOME + "/main/data/pgsql/bin/psql.exe";

    public static final String TEMP_FOLDER = NEBULA_BOH_HOME + "/main/temp";
    public static final String RESTORE_FOLDER = NEBULA_BOH_HOME + "/main/restore";

    public static boolean SKIP_SQL;//ns
    public static boolean DISABLE_GUARD;//ng
    public static int CHECK_UPDATE_DELAY_SECONDS = Integer.parseInt(loadConsoleProperties("update.client.minuteDelay")) * 60;//cd

    public static boolean SKIP_CHECK_SUM;//checksum=no
    public static boolean NO_FEEDBACK;//feedback=no

    public static void setArgument(String... args) {
        if (null != args && args.length > 0) {
            for (String arg : args) {
                if ("ng".equals(arg)) {
                    NebulaBoh.DISABLE_GUARD = true;
                }
                if ("ns".equals(arg)) {
                    NebulaBoh.SKIP_SQL = true;
                }
                if (arg.contains("cd=")) {
                    NebulaBoh.CHECK_UPDATE_DELAY_SECONDS = Integer.parseInt(arg.replace("cd=", ""));
                }
                if (arg.contains("checksum=no")) {
                    NebulaBoh.SKIP_CHECK_SUM = true;
                }
                if (arg.contains("feedback=no")) {
                    NebulaBoh.NO_FEEDBACK = true;
                }
            }
        }
    }

    public static File getTempFolder() throws IOException {
        File tempFolder = new File(TEMP_FOLDER);
        FileUtils.forceMkdir(tempFolder);
        return tempFolder;

    }

    public static File getRestoreFolder() throws IOException {
        File restoreFolder = new File(RESTORE_FOLDER);
        FileUtils.forceMkdir(restoreFolder);
        return restoreFolder;

    }

    public static String loadPosConfigProperties(String key){
        if (posConfigProperties.isEmpty()) {
            File file = new File(NebulaBoh.POS_CONFIG);
            FileInputStream inStream = null;
            FileOutputStream outputStream = null;
            try {
                if (file.exists()) {
                    inStream = FileUtils.openInputStream(file);
                    posConfigProperties.load(inStream);
                } else {
                    logger.error("pos_config.properties文件不存在");
                }
            } catch (IOException e) {
                logger.error("pos_config.properties读取错误", e);
            } finally {
                IOUtils.closeQuietly(inStream);
                IOUtils.closeQuietly(outputStream);
            }
        }
        return posConfigProperties.getProperty(key);
    }

    public static String loadSystemConfigProperties(String key){
        if (posSystemParamProperties.isEmpty()) {
            File file = new File(NebulaBoh.SYSTEM_PROPERTIES);
            FileInputStream inStream = null;
            FileOutputStream outputStream = null;
            try {
                if (file.exists()) {
                    inStream = FileUtils.openInputStream(file);
                    posSystemParamProperties.load(inStream);
                } else {
                    logger.error("systemParam.properties文件不存在");
                }
            } catch (IOException e) {
                logger.error("systemParam.properties读取错误", e);
            } finally {
                IOUtils.closeQuietly(inStream);
                IOUtils.closeQuietly(outputStream);
            }
        }
        return posSystemParamProperties.getProperty(key);
    }

    public static String loadVersionProperties(String key) {
        if (nebulaVersionProperties.isEmpty()) {
            File file = new File(NebulaBoh.VERSION_PROPERTIES);
            FileInputStream inStream = null;
            FileOutputStream outputStream = null;
            try {
                if (file.exists()) {
                    inStream = FileUtils.openInputStream(file);
                    nebulaVersionProperties.load(inStream);
                } else {
                    nebulaVersionProperties.setProperty("app.identifier", DEFAULT_IDENTIFIER);
                    nebulaVersionProperties.setProperty("app.version", DEFAULT_VERSION);

                    outputStream = FileUtils.openOutputStream(FileUtils.getFile(NebulaBoh.VERSION_PROPERTIES));
                    nebulaVersionProperties.store(outputStream, null);
                }
            } catch (IOException e) {
                logger.error("nebula_boh版本信息配置文件读取错误", e);
            } finally {
                IOUtils.closeQuietly(inStream);
                IOUtils.closeQuietly(outputStream);
            }
        }

        return nebulaVersionProperties.getProperty(key);
    }

    public static String loadVersionProperties(String key, boolean refresh) {
        if (refresh || nebulaVersionProperties.isEmpty()) {
            File file = new File(NebulaBoh.VERSION_PROPERTIES);
            FileInputStream inStream = null;
            FileOutputStream outputStream = null;
            try {
                if (file.exists()) {
                    inStream = FileUtils.openInputStream(file);
                    nebulaVersionProperties.load(inStream);
                } else {
                    nebulaVersionProperties.setProperty("app.identifier", DEFAULT_IDENTIFIER);
                    nebulaVersionProperties.setProperty("app.version", DEFAULT_VERSION);

                    outputStream = FileUtils.openOutputStream(FileUtils.getFile(NebulaBoh.VERSION_PROPERTIES));
                    nebulaVersionProperties.store(outputStream, null);
                }
            } catch (IOException e) {
                logger.error("nebula_boh版本信息配置文件读取错误", e);
            } finally {
                IOUtils.closeQuietly(inStream);
                IOUtils.closeQuietly(outputStream);
            }
        }

        return nebulaVersionProperties.getProperty(key);

    }

    public static void saveVersionProperties(String key, String value) {
        File nebulaVersionPropertiesFile = new File(NebulaBoh.VERSION_PROPERTIES);
        FileOutputStream os = null;
        FileInputStream is = null;
        try {
            is = FileUtils.openInputStream(nebulaVersionPropertiesFile);
            nebulaVersionProperties.load(is);
            nebulaVersionProperties.setProperty(key, value);
            os = FileUtils.openOutputStream(nebulaVersionPropertiesFile);
            nebulaVersionProperties.store(os, null);
        } catch (IOException e) {
            logger.error("nebula_boh版本信配置文件保存失败", e);
        } finally {
            IOUtils.closeQuietly(is);
            IOUtils.closeQuietly(os);
        }
    }

    public static void saveVersionProperties(Map map){
        File nebulaVersionPropertiesFile = new File(NebulaBoh.VERSION_PROPERTIES);
        FileOutputStream os = null;
        FileInputStream is = null;
        try {
            is = FileUtils.openInputStream(nebulaVersionPropertiesFile);
            nebulaVersionProperties.load(is);
            nebulaVersionProperties.putAll(map);
            os = FileUtils.openOutputStream(nebulaVersionPropertiesFile);
            nebulaVersionProperties.store(os, null);
        } catch (IOException e) {
            logger.error("nebula_boh版本信配置文件保存失败", e);
        } finally {
            IOUtils.closeQuietly(is);
            IOUtils.closeQuietly(os);
        }
    }

    public static String loadConsoleProperties(String key, boolean refresh) {
        if (refresh || consoleProperties.isEmpty()) {
            File newSaveProperties = new File(NebulaBoh.CONSOLE_CONFIG_PROPERTIES);
            InputStream inStream = null;
            FileOutputStream outputStream = null;
            try {
                if (newSaveProperties.exists()) {
                    inStream = FileUtils.openInputStream(newSaveProperties);
                    consoleProperties.load(inStream);
                } else {
                    inStream = ClassLoader.getSystemResourceAsStream(newSaveProperties.getName());
                    consoleProperties.load(inStream);
                    outputStream = FileUtils.openOutputStream(newSaveProperties);
                    consoleProperties.store(outputStream, null);
                }
            } catch (IOException e) {
                logger.error("Console配置文件读取错误", e);
            } finally {
                IOUtils.closeQuietly(inStream);
                IOUtils.closeQuietly(outputStream);
            }
        }

        return consoleProperties.getProperty(key);
    }

    public static String loadConsoleProperties(String key) {
        return loadConsoleProperties(key, false);
    }

    public static void saveConsoleProperties(String key, String value) {
        File newSaveProperties = new File(NebulaBoh.CONSOLE_CONFIG_PROPERTIES);
        consoleProperties.setProperty(key, value);
        FileOutputStream outputStream = null;
        try {
            outputStream = FileUtils.openOutputStream(newSaveProperties);
            consoleProperties.store(outputStream, null);
        } catch (IOException e) {
            logger.error("Console配置文件保存失败", e);
        } finally {
            IOUtils.closeQuietly(outputStream);
        }
    }
}
