package com.tzx.framework.common.sms;

import java.util.HashMap;

/**
 * 新旧短信平台计算短信条数方法
 * <AUTHOR>
 *
 */
public class SMSUtils {

	private final static int SMS_LEN=70;//普通短信长度
    private final static int LONG_SMS_LEN=67;//汉字短信长短信分条长度
    private final static int BYTE_LEN=140;//纯字节短信长度
    private final static int BYTE_LONG=67;//纯字节短信长短信分条长度
	
    /**
     * 漫道(短信平台)条数计算方法
     * 此方法为老平台短信计算方式
     * 该方法入参需去除首尾空格并且要加上用户签名
     * @param msg
     * @return
     */
	public static int oldSmsGetMsgLength(String msg){
		
		int messageLen=msg.length();
		int charLen=msg.getBytes().length;
		int count=1;
		if(messageLen==charLen){
			//说明不包含中文字符					
			if (charLen>BYTE_LEN) {
				count=charLen/BYTE_LONG;
				if(charLen%BYTE_LONG>0)
					count=count+1;
			}
		}else{							
			//说明包含中文字符
			if (messageLen>SMS_LEN) {
				count=messageLen/LONG_SMS_LEN;
				if(messageLen%LONG_SMS_LEN>0)
					count=count+1;
			}
		}
		return count;
	}
	
	public static int calulateMsgCount(String msg){
		int size = 0;
		if (msg.equals("")) {
			size = 1;
		} else {
			int n = msg.length();
			if (n > 70) {
				if (n % 67 == 0) {
					size = n / 67;
				} else {
					size = n / 67 + 1;
				}
			} else {
				size = 1; // 普通短信
			}
		}
		return size;
	}
	
	
	
	/**
	 * 移动时空
	 * 新短信平台获取短信条数方法
	 * */ 
	public static int newSmsGetMsgLength(String msg) {
		int size = 0;
		if (msg.equals("")) {
			size = 1;
		} else {
			int n = msg.length();
			if (n > 70) {
				if (n % 67 == 0) {
					size = n / 67;
				} else {
					size = n / 67 + 1;
				}
			} else {
				size = 1; // 普通短信
			}
		}
		return size;
	}
	
	/**
	 * 云信留客
	 * @param msg
	 * @return
	 */
	public static int YXLKGetMsgLength(String msg) {
		int size = 0;
		if (msg.equals("")) {
			size = 1;
		} else {
			int n = msg.length();
			if (n > 70) {
				if (n % 67 == 0) {
					size = n / 67;
				} else {
					size = n / 67 + 1;
				}
			} else {
				size = 1; 
			}
		}
		return size;
	}
	
	public static void main(String[] args) {
		String ss = "sssssssssssssssssssss我是谁ssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssss";
		System.out.println("ydsk -----"+SMSUtils.newSmsGetMsgLength(ss));
		System.out.println("md -----"+SMSUtils.oldSmsGetMsgLength(ss));
		System.out.println("ydsk -----"+SMSUtils.newSmsGetMsgLength(ss));
		System.out.println("byte"+ss.getBytes().length);
		System.out.println("char"+ss.length());
		
		
	}
}
