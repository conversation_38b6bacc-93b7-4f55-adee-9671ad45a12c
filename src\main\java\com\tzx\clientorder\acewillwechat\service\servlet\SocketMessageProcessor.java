package com.tzx.clientorder.acewillwechat.service.servlet;

import net.sf.json.JSONObject;

public interface SocketMessageProcessor {
	
	/*** 下单 */
	public final String	INSERT_ORDER		= "insert_order";

	/*** */
	public final String	TABLE_STATUS		= "table.status";

	/*** */
	public final String	UPLOAD_PERPAYORDER	= "upload.perpayorder";

	/*** */
	public final String	PAY_SUCCESS			= "pay.success";

	/*** */
	public final String	UPLOAD_ORDER		= "upload_order";

	/*** */
	public final String	LOCK				= "lock";
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param jsonObject
	 * @return
	 * @throws Exception
	 */
	JSONObject process(String tenancyId, Integer storeId, JSONObject jsonObject) throws Exception;

}
