package com.tzx.pos.base.constant;

/**
 * 新美大CRM对接参数
 */

public enum XmdCrmParameterEnum
{
	/**
	 * 美大CRM对接门店认领URL
	 */
	XMD_CRM_GET_TOKEN_URL("xmdcrm.gettoken.url", "美大CRM对接门店认领URL"),
	/**
	 * 美大CRM对接UISDKURL
	 */
	XMD_CRM_UISDK_URL("xmdcrm.uisdk.url", "美大CRM对接UISDKURL"),
	/**
	 * 美大CRM对接开发者ID
	 */
	XMD_CRM_DEVELOPER_ID("xmdcrm.developerid", "美大CRM对接开发者ID"),
	/**
	 * 美大CRM对接signKey
	 */
	XMD_CRM_SIGN_KEY("xmdcrm.signkey", "美大CRM对接signKey"),
	/**
	 * 美大CRM对接业务ID
	 */
	XMD_CRM_BUSINESS_ID("xmdcrm.businessid", "美大CRM对接业务ID"),
	/**
	 * 美大CRM对接REQUESTURL
	 */
	XMD_CRM_REQUEST_URL("xmdcrm.request.url", "美大CRM对接REQUESTURL")
	;

	private final String	value;
	private final String	name;

	private XmdCrmParameterEnum(String value, String name)
	{
		this.value = value;
		this.name = name;
	}

	/**
	 * 获取参数名
	 * 
	 * @return
	 */
	public String getVaue()
	{
		return value;
	}

	/**
	 * 获取参数说明
	 * 
	 * @return
	 */
	public String getName()
	{
		return name;
	}
	
	/**
	 * @return
	 */
	public String getCode()
	{
		return this.name();
	}
	
}
