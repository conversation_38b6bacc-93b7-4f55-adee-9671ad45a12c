package com.tzx.pos.bo.dto;

import java.io.Serializable;

/**人员折扣权限
 * <AUTHOR>
 *
 */
public class BasicDataUserDiscountAuthority implements Serializable
{

	/**
	 * 
	 */
	private static final long	serialVersionUID	= 6044826927386502153L;
	
	private Integer id;
	private Integer employee_id;
	private Integer user_id;
	private Double fixed_discount_limit;
	private Double fall_discount_limit;
	private Double single_discount_limit;
	private String is_discount_case;
	private Double pre_reward_money;
	private Double pre_discount_money;
	private String discount_type;
	private Double single_discount_money;//每单折让上限额
	private Double single_reward_money;//每单奉送上限额
	
	public Double getSingle_discount_money() {
		return single_discount_money;
	}
	public void setSingle_discount_money(Double single_discount_money) {
		this.single_discount_money = single_discount_money;
	}
	
	public Double getSingle_reward_money() {
		return single_reward_money;
	}
	public void setSingle_reward_money(Double single_reward_money) {
		this.single_reward_money = single_reward_money;
	}
	public Integer getId()
	{
		return id;
	}
	public void setId(Integer id)
	{
		this.id = id;
	}
	public Integer getEmployee_id()
	{
		return employee_id;
	}
	public void setEmployee_id(Integer employee_id)
	{
		this.employee_id = employee_id;
	}
	public Integer getUser_id()
	{
		return user_id;
	}
	public void setUser_id(Integer user_id)
	{
		this.user_id = user_id;
	}
	public Double getFixed_discount_limit()
	{
		return fixed_discount_limit;
	}
	public void setFixed_discount_limit(Double fixed_discount_limit)
	{
		this.fixed_discount_limit = fixed_discount_limit;
	}
	public Double getFall_discount_limit()
	{
		return fall_discount_limit;
	}
	public void setFall_discount_limit(Double fall_discount_limit)
	{
		this.fall_discount_limit = fall_discount_limit;
	}
	public Double getSingle_discount_limit()
	{
		return single_discount_limit;
	}
	public void setSingle_discount_limit(Double single_discount_limit)
	{
		this.single_discount_limit = single_discount_limit;
	}
	public String getIs_discount_case()
	{
		return is_discount_case;
	}
	public void setIs_discount_case(String is_discount_case)
	{
		this.is_discount_case = is_discount_case;
	}

	public Double getPre_reward_money() {
		return pre_reward_money;
	}

	public void setPre_reward_money(Double pre_reward_money) {
		this.pre_reward_money = pre_reward_money;
	}

	public Double getPre_discount_money() {
		return pre_discount_money;
	}

	public void setPre_discount_money(Double pre_discount_money) {
		this.pre_discount_money = pre_discount_money;
	}

	public String getDiscount_type() {
		return discount_type;
	}

	public void setDiscount_type(String discount_type) {
		this.discount_type = discount_type;
	}
}
