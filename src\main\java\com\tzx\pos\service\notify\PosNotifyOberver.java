package com.tzx.pos.service.notify;

import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.util.Comet4jUtil;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> on 2018-06-30
 */
public class PosNotifyOberver extends ClientNotifyOberver{

    public Logger logger=LoggerFactory.getLogger(PosNotifyOberver.class);

    @Override
    public void doNotify(Type p) {
        List<JSONObject> noticeList = new ArrayList<JSONObject>();
        JSONObject noticeJson = new JSONObject();
        noticeJson.put("message", p);
        noticeList.add(noticeJson);
        Comet4jUtil.comet4J(noticeList, p, Oper.notice);
        logger.info(">>>>>>消息推送到POS:"+noticeList);
    }
}
