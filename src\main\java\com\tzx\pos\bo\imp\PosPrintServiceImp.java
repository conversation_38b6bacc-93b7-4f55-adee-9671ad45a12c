package com.tzx.pos.bo.imp;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;

import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.DoubleHelper;
import com.tzx.framework.common.util.Tools;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.FunctionCode;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.base.util.ReqDataUtil;
import com.tzx.pos.bo.PosPrintNewService;
import com.tzx.pos.bo.PosPrintService;
import com.tzx.pos.po.springjdbc.dao.PosDao;

/**
 * 
 * <AUTHOR> 2015年12月3日-下午2:55:46
 */
@Service(PosPrintService.NAME)
public class PosPrintServiceImp implements PosPrintService
{
	@Resource(name = PosDao.NAME)
	private PosDao				posDao;

	private static final Logger	logger	= Logger.getLogger(PosPrintServiceImp.class);

	/**
	 * 新厨打方法 点菜
	 * 
	 * @param tenancyId
	 * @param billno
	 * @param organId
	 * @param printType
	 *            0:厨打，1：账单，2：会员
	 * @return
	 */
	@Override
	public List<Integer> orderChef(String tenancyId, String billno, Integer organId, String printType)
	{
		List<Integer> list = new ArrayList<Integer>();
		try
		{
			Timestamp timestamp = DateUtil.currentTimestamp();
			String printProperty = "点菜";

			String tableCode = null;
			String qtCode = new String(" select table_code from pos_bill where bill_num = ? and store_id = ? ");
			SqlRowSet rs = posDao.query4SqlRowSet(qtCode, new Object[]
			{ billno, organId });
			if (rs.next())
			{
				tableCode = rs.getString("table_code");
			}

			String isAreaPrint = posDao.getSysParameter(tenancyId, organId, "XFDYJSFKYQYDY");
			
			// 打印单套餐显示方式
			String comboPrintType = posDao.getSysParameter(tenancyId, organId, "comboprint_type");
			
			// 标签打印是否合并数量
			String isLabPrintMerge = posDao.getSysParameter(tenancyId, organId, "BQDYSFHBSL");
			
			// 下单时是否打印等叫菜品
			String isPrintWaitCallItem = posDao.getSysParameter(tenancyId, organId, "SFDYDJCP");

			StringBuilder sql1106 = new StringBuilder(" select bill.guest,bill.waiter_num,shift.name as shift_name,printformat.class_item_code as print_format,classprint.printer_id,printer.name as printer_name,item.rwid,item.item_count from pos_bill_item as item ");
			sql1106.append(" left join pos_bill bill on item.bill_num=bill.bill_num and item.tenancy_id=bill.tenancy_id and item.store_id=bill.store_id left join duty_order shift on item.item_shift_id=shift.id ");
			sql1106.append(" left join tables_info table1 on bill.table_code=table1.table_code and table1.organ_id=bill.store_id left join hq_item_info dish on item.item_id=dish.id and dish.tenancy_id=item.tenancy_id ");
			sql1106.append(" left join hq_kvs_item printitem on dish.id=printitem.item_id and kvs_id in (select hk.id from hq_kvs hk where hk.store_id= ? and hk.tenancy_id=?) ");
			sql1106.append(" left join hq_kvs_printer classprint on printitem.kvs_id=classprint.kvs_id left join hq_print_printer printer on classprint.printer_id=printer.id ");
			sql1106.append(" left join hq_print_printer_format printformat on printer.id=printformat.print_printer_id and class_item_code not in('1001','1002','1003','1004') ");
			sql1106.append(" where item.bill_num = ? and item.print_tag = '*' and item.store_id = ? and printformat.class_item_code = ?");
			// 仅明细项打印
			if (StringUtils.equals(SysDictionary.COMBOPRINT_TYPE_MEALLIST, comboPrintType))
			{
				sql1106.append("  and (item.item_property = '"+SysDictionary.ITEM_PROPERTY_SINGLE+"' or item.item_property = '"+SysDictionary.ITEM_PROPERTY_MEALLIST+"')");
			}
			else if (StringUtils.equals(SysDictionary.COMBOPRINT_TYPE_SETMEAL, comboPrintType))
			{
				sql1106.append(" and (item.item_property = '"+SysDictionary.ITEM_PROPERTY_SINGLE+"' or item.item_property = '"+SysDictionary.ITEM_PROPERTY_SETMEAL+"')");
			}

			if("1".equals(isAreaPrint))
			{
				sql1106.append(" and classprint.printer_id in (select printer_id from hq_table_printer where tenancy_id='").append(tenancyId).append("' and table_code='").append(tableCode).append("')");
			}
			
			if("0".equals(isPrintWaitCallItem))// 不打印等叫菜品
			{
				sql1106.append(" and item.waitcall_tag<>'1' ");
			}
			
			sql1106.append(" order by rwid ");
			/**
			 * 用来给厨打分类，主要是往主表里要插入大类（热菜，凉菜等），整单
			 */
			StringBuilder sql1107 = new StringBuilder(" select bill.guest,bill.waiter_num,shift.name as shift_name,printformat.class_item_code as print_format,classprint.printer_id,printer.name as printer_name from pos_bill_item as item ");
			sql1107.append(" left join pos_bill bill on item.bill_num=bill.bill_num and item.tenancy_id=bill.tenancy_id and item.store_id=bill.store_id left join duty_order shift on item.item_shift_id=shift.id ");
			sql1107.append(" left join tables_info table1 on bill.table_code=table1.table_code and table1.organ_id=bill.store_id left join hq_item_info dish on item.item_id=dish.id and dish.tenancy_id=item.tenancy_id ");
			sql1107.append(" left join hq_kvs_item printitem on dish.id=printitem.item_id and kvs_id in (select hk.id from hq_kvs hk where hk.store_id= ? and hk.tenancy_id=?) ");
			sql1107.append(" left join hq_kvs_printer classprint on printitem.kvs_id=classprint.kvs_id left join hq_print_printer printer on classprint.printer_id=printer.id ");
			sql1107.append(" left join hq_print_printer_format printformat on printer.id=printformat.print_printer_id and class_item_code not in('1001','1002','1003','1004') ");
			sql1107.append(" where item.bill_num = ? and item.print_tag = '*' and item.store_id = ? and printformat.class_item_code = ?");

			if("1".equals(isAreaPrint))
			{
				sql1107.append(" and classprint.printer_id in (select printer_id from hq_table_printer where tenancy_id='").append(tenancyId).append("' and table_code='").append(tableCode).append("')");
			}
			
			if("0".equals(isPrintWaitCallItem))// 不打印等叫菜品
			{
				sql1107.append(" and item.waitcall_tag<>'1' ");
			}
			
			sql1107.append(" group by print_format,printer_id,printer_name,guest,shift_name,bill.waiter_num");
			/**
			 * 往主表里插入打印参数
			 */
			String insertMPrint = new String(" insert into pos_print (tenancy_id,store_id,print_type,print_format,print_property,printer_id,printer_name,bill_num,table_code,print_tag,remark,print_time,finish_time,guest,waiter_num,shift_name) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ");

			/**
			 * 往子表里插入菜品明细，用来打印
			 */
			StringBuilder sql = new StringBuilder(" insert into pos_print_list(tenancy_id,store_id,table_code,table_name,bill_num,item_num,item_name,item_english,item_unit_name,item_price,item_count,update_time,zfkwnr,item_property,");
			sql.append("item_remark,item_serial,print_code,parent_id,waitcall_tag,rwid) ");
			sql.append("select item.tenancy_id,item.store_id,bill.table_code,table1.table_name,item.bill_num,dish.item_code,item.item_name,item.item_english,item.item_unit_name,item.item_price,item.item_count,CURRENT_TIMESTAMP(0) :: TIMESTAMP WITHOUT TIME ZONE ,concat(c.kwzf,' ',item.item_taste) as kwzf,item.item_property,item.item_remark,");
			sql.append("item.item_serial,item.rwid,?,item.waitcall_tag,item.rwid ");
			sql.append("from pos_bill_item as item left join pos_bill bill on item.bill_num=bill.bill_num and item.tenancy_id=bill.tenancy_id and item.store_id=bill.store_id ");
			sql.append(" left join duty_order shift on item.item_shift_id=shift.id left join tables_info table1 on bill.table_code=table1.table_code and table1.organ_id=bill.store_id left join hq_item_info dish on item.item_id=dish.id and dish.tenancy_id=item.tenancy_id ");
			sql.append(" left join hq_kvs_item printitem on dish.id=printitem.item_id and kvs_id in (select hk.id from hq_kvs hk where hk.tenancy_id=? and hk.store_id= ? )");
			sql.append(" left join hq_kvs_printer classprint on printitem.kvs_id=classprint.kvs_id left join hq_print_printer printer on classprint.printer_id=printer.id ");
			sql.append(" left join hq_print_printer_format printformat on printer.id=printformat.print_printer_id and class_item_code not in('1001','1002','1003','1004') ");
			sql.append(" left join (select array_to_string(array_accum(zfkw_name), ' ') as kwzf,zfkw.rwid from pos_zfkw_item zfkw where zfkw.bill_num = ? and zfkw.store_id=? group by zfkw.rwid) c on item.rwid = c.rwid ");
			sql.append(" where item.rwid = ? and item.bill_num = ? and item.store_id = ? and classprint.printer_id = ? and printformat.class_item_code = ? ");

			/**
			 * 查询新下单的菜品
			 */
			StringBuilder queryItem = new StringBuilder(
					"select item.rwid,item.tenancy_id,item.store_id,printformat.class_item_code as print_format,classprint.printer_id, printer.name as printer_name,item.bill_num,bill.table_code from pos_bill_item as item left join pos_bill bill on item.bill_num=bill.bill_num and item.tenancy_id=bill.tenancy_id and item.store_id=bill.store_id ");
			queryItem.append(" left join duty_order shift on item.item_shift_id=shift.id left join tables_info table1 on bill.table_code=table1.table_code and table1.organ_id=bill.store_id left join hq_item_info dish on item.item_id=dish.id and dish.tenancy_id=item.tenancy_id ");
			queryItem.append(" left join hq_kvs_item printitem on dish.id=printitem.item_id and kvs_id in (select hk.id from hq_kvs hk where hk.store_id= ? and hk.tenancy_id=?) left join hq_kvs_printer classprint on printitem.kvs_id=classprint.kvs_id ");
			queryItem.append(" left join hq_print_printer printer on classprint.printer_id=printer.id left join hq_print_printer_format printformat on printer.id=printformat.print_printer_id and class_item_code not in('1001','1002','1003','1004') ");
			queryItem.append(" where item.print_tag = '*' and item.bill_num = ? and item.store_id = ? and classprint.printer_id = ? and printformat.class_item_code = ? ");
			if("0".equals(isPrintWaitCallItem))// 不打印等叫菜品
			{
				queryItem.append(" and item.waitcall_tag<>'1' ");
			}
			queryItem.append(" order by item.rwid asc ");
			
			/**
			 * 获取当前的序列
			 */
			String qseq = new String("select currval('pos_print_id_seq'::regclass) ");

			if ("0".equalsIgnoreCase(printType))
			{
				List<String> printList = new ArrayList<String>();
				printList.add("1106");// 打印类型，单切1106
				printList.add("1107");// 打印类型，整单1107
				printList.add("1104");// 打印类型，标签单切1104
				// 把新的id放到list，然后做为参数传到打印方法里

				for (int m = 0; m < printList.size(); m++)
				{
					String print_type = printList.get(m);
					if (StringUtils.isNotEmpty(print_type))
					{
						if ("1106".equalsIgnoreCase(print_type))
						{
							// 单切插入主表并记录主表的信息
							SqlRowSet rs1106 = posDao.query4SqlRowSet(sql1106.toString(), new Object[]
							{ organId, tenancyId, billno, organId, print_type });
							while (rs1106.next())
							{
								String print_format = rs1106.getString("print_format");
								Integer printer_id = rs1106.getInt("printer_id");
								String printer_name = rs1106.getString("printer_name");
								Integer guest = rs1106.getInt("guest");
								String waiter_num = rs1106.getString("waiter_num");
								String shift_name = rs1106.getString("shift_name");

								Integer rwid = rs1106.getInt("rwid");

								// 存主表
								posDao.update(insertMPrint, new Object[]
								{ tenancyId, organId, "0", print_format, printProperty, printer_id, printer_name, billno, tableCode, "*", null, timestamp, null, guest, waiter_num, shift_name });
								Integer pid = posDao.queryForInt(qseq, new Object[] {});
								list.add(pid);
								// 存次表明细
								posDao.update(sql.toString(), new Object[]
								{ pid, tenancyId, organId, billno, organId, rwid, billno, organId, printer_id, print_format });
							}
						}
						
						if ( "1104".equalsIgnoreCase(print_type))
						{
							// 单切插入主表并记录主表的信息
							SqlRowSet rs1106 = posDao.query4SqlRowSet(sql1106.toString(), new Object[]
							{ organId, tenancyId, billno, organId, print_type });
							while (rs1106.next())
							{
								String print_format = rs1106.getString("print_format");
								Integer printer_id = rs1106.getInt("printer_id");
								String printer_name = rs1106.getString("printer_name");
								Integer guest = rs1106.getInt("guest");
								String waiter_num = rs1106.getString("waiter_num");
								String shift_name = rs1106.getString("shift_name");
								Integer rwid = rs1106.getInt("rwid");
								
								Double item_count = 0d;
								// 标签打印合并数量的场合，只生成一次打印任务
								if("1".equals(isLabPrintMerge))
								{
									item_count = 1d;
								}
								else
								{
									// 标签打印不合并数量的场合，按数量生成打印任务
									item_count = rs1106.getDouble("item_count");
								}
								
								for(int i=0;i<item_count;i++)
								{
									// 存主表
									posDao.update(insertMPrint, new Object[]
									{ tenancyId, organId, "0", print_format, printProperty, printer_id, printer_name, billno, tableCode, "*", null, timestamp, null, guest, waiter_num, shift_name });
									Integer pid = posDao.queryForInt(qseq, new Object[] {});
									list.add(pid);
									// 存次表明细
									posDao.update(sql.toString(), new Object[]
									{ pid, tenancyId, organId, billno, organId, rwid, billno, organId, printer_id, print_format });
								}
							}
						}
						
						if ("1107".equalsIgnoreCase(print_type))
						{
							// 单切插入主表并记录主表的信息
							SqlRowSet rsClass = posDao.query4SqlRowSet(sql1107.toString(), new Object[]
							{ organId, tenancyId, billno, organId, print_type });
							while (rsClass.next())
							{
								Map<String, String> map = new HashMap<String, String>();

								String print_format = rsClass.getString("print_format");
								Integer printer_id = rsClass.getInt("printer_id");
								String printer_name = rsClass.getString("printer_name");
								Integer guest = rsClass.getInt("guest");
								String waiter_num = rsClass.getString("waiter_num");
								String shift_name = rsClass.getString("shift_name");

								map.put("print_format", print_format);
								map.put("printer_id", printer_id + "");
								map.put("printer_name", printer_name);

								// 存主表
								posDao.update(insertMPrint, new Object[]
								{ tenancyId, organId, "0", print_format, printProperty, printer_id, printer_name, billno, tableCode, "*", null, timestamp, null, guest, waiter_num, shift_name });
								Integer pid = posDao.queryForInt(qseq, new Object[] {});
								list.add(pid);

								SqlRowSet rsi = posDao.query4SqlRowSet(queryItem.toString(), new Object[]
								{ organId, tenancyId, billno, organId, printer_id, print_format });

								while (rsi.next())
								{
									Integer rwid = rsi.getInt("rwid");
									// 存次表明细
									posDao.update(sql.toString(), new Object[]
									{ pid, tenancyId, organId, billno, organId, rwid, billno, organId, printer_id, print_format });
								}
							}
						}
						
						
					}
				}
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
			logger.error("打印失败", e);
		}
		finally
		{
			String uBitem = new String("update pos_bill_item set print_tag = ? where bill_num = ? and store_id = ? and tenancy_id = ?");
			try
			{
				posDao.update(uBitem, new Object[]
				{ null, billno, organId, tenancyId });
			}
			catch (Exception e)
			{
				e.printStackTrace();
				logger.info("Exception:" + ExceptionMessage.getExceptionMessage(e));
				throw SystemException.getInstance(PosErrorCode.OPER_ERROR);
			}
		}
		return list;
	}

	@Override
	@Deprecated
	public void orderPrint(String tenancyId, String billno, Integer organId, List<Integer> list)
	{
//		String qPrintC = new String("select id,print_format,printer_id,bill_num,printer_name from pos_print where id = ? and bill_num = ? and store_id = ? order by id asc");
//
//		for (int i = 0; i < list.size(); i++)
//		{
//			int id = list.get(i);
//
//			SqlRowSet rs = posDao.query4SqlRowSet(qPrintC, new Object[]
//			{ id, billno, organId });
//			if (rs.next())
//			{
//				JSONObject obj = new JSONObject();
//				obj.put("tenancy_id", tenancyId);
//				obj.put("store_id", organId);
//				obj.put("print_format", rs.getString("print_format"));
//				obj.put("printer_id", rs.getInt("printer_id"));
//				obj.put("bill_num", rs.getString("bill_num"));
//				obj.put("printer_name", rs.getString("printer_name"));
//				obj.put("id", rs.getInt("id"));
//
//				NewQueueUtil.push(obj);
//			}
//		}
	}

	@Override
	public List<Integer> orderOperChef(String tenancyId, String billno, Integer organId, String oper, String operType, String rwids)
	{
		List<Integer> list = new ArrayList<Integer>();
		/**
		 * 获取当前的序列
		 */
		String qseq = new String("select currval('pos_print_id_seq'::regclass) ");

		StringBuilder retreatSql = new StringBuilder(" select bill.guest,bill.waiter_num,shift.name as shift_name,item.rwid,item.table_code,printformat.class_item_code as print_format,classprint.printer_id,printer.name as printer_name from pos_bill_item as item ");
		retreatSql.append(" left join pos_bill bill on item.bill_num=bill.bill_num and item.tenancy_id=bill.tenancy_id and item.store_id=bill.store_id left join duty_order shift on item.item_shift_id=shift.id ");
		retreatSql.append(" left join tables_info table1 on bill.table_code=table1.table_code and table1.organ_id=bill.store_id left join hq_item_info dish on item.item_id=dish.id and dish.tenancy_id=item.tenancy_id ");
		retreatSql.append(" left join hq_kvs_item printitem on dish.id=printitem.item_id and kvs_id in (select hk.id from hq_kvs hk where hk.store_id= ? and hk.tenancy_id=?) ");
		retreatSql.append(" left join hq_kvs_printer classprint on printitem.kvs_id=classprint.kvs_id left join hq_print_printer printer on classprint.printer_id=printer.id ");
		retreatSql.append(" left join hq_print_printer_format printformat on printer.id=printformat.print_printer_id and class_item_code not in('1001','1002','1003','1004') ");
		retreatSql.append(" where item.bill_num = ? and item.store_id = ? and item.rwid in (" + rwids + ") and printformat.class_item_code is not null ");

		/**
		 * 往子表里插入菜品明细，用来打印
		 */
		StringBuilder sql = new StringBuilder(" insert into pos_print_list(tenancy_id,store_id,table_code,table_name,bill_num,item_num,item_name,item_english,item_unit_name,item_price,item_count,update_time,zfkwnr,item_property,");
		sql.append("item_remark,item_serial,print_code,parent_id,waitcall_tag,rwid) ");
		sql.append("select item.tenancy_id,item.store_id,bill.table_code,table1.table_name,item.bill_num,dish.item_code,item.item_name,item.item_english,item.item_unit_name,item.item_price,item.item_count,CURRENT_TIMESTAMP(0) :: TIMESTAMP WITHOUT TIME ZONE ,concat(c.kwzf,' ',item.item_taste) as kwzf,item.item_property,item.item_remark,");
		sql.append("item.item_serial,item.rwid,?,item.waitcall_tag,item.rwid ");
		sql.append("from pos_bill_item as item left join pos_bill bill on item.bill_num=bill.bill_num and item.tenancy_id=bill.tenancy_id and item.store_id=bill.store_id ");
		sql.append(" left join duty_order shift on item.item_shift_id=shift.id left join tables_info table1 on bill.table_code=table1.table_code and table1.organ_id=bill.store_id left join hq_item_info dish on item.item_id=dish.id and dish.tenancy_id=item.tenancy_id ");
		sql.append(" left join hq_kvs_item printitem on dish.id=printitem.item_id and kvs_id in (select hk.id from hq_kvs hk where hk.tenancy_id=? and hk.store_id= ? )");
		sql.append(" left join hq_kvs_printer classprint on printitem.kvs_id=classprint.kvs_id left join hq_print_printer printer on classprint.printer_id=printer.id ");
		sql.append(" left join hq_print_printer_format printformat on printer.id=printformat.print_printer_id and class_item_code not in('1001','1002','1003','1004') ");
		sql.append(" left join (select array_to_string(array_accum(zfkw_name), ' ') as kwzf,zfkw.rwid from pos_zfkw_item zfkw where zfkw.bill_num = ? and zfkw.store_id=? group by zfkw.rwid) c on item.rwid = c.rwid ");
		sql.append(" where item.rwid = ? and item.bill_num = ? and item.store_id = ? and classprint.printer_id = ? and printformat.class_item_code = ? ");

		/**
		 * 往主表里插入打印参数
		 */
		String insertMPrint = new String(" insert into pos_print (tenancy_id,store_id,print_type,print_format,print_property,printer_id,printer_name,bill_num,table_code,print_tag,remark,print_time,finish_time,guest,waiter_num,shift_name) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ");

		Timestamp timestamp = DateUtil.currentTimestamp();

		// 单切插入主表并记录主表的信息
		try
		{
			SqlRowSet rsRetreat = posDao.query4SqlRowSet(retreatSql.toString(), new Object[]
			{ organId, tenancyId, billno, organId });
			while (rsRetreat.next())
			{
				String print_format = rsRetreat.getString("print_format");
				Integer printer_id = rsRetreat.getInt("printer_id");
				String printer_name = rsRetreat.getString("printer_name");
				Integer rwid = rsRetreat.getInt("rwid");
				String tableCode = rsRetreat.getString("table_code");
				Integer guest = rsRetreat.getInt("guest");
				String waiter_num = rsRetreat.getString("waiter_num");
				String shift_name = rsRetreat.getString("shift_name");

				// 存主表
				posDao.update(insertMPrint, new Object[]
				{ tenancyId, organId, "0", print_format, oper, printer_id, printer_name, billno, tableCode, "*", operType, timestamp, null, guest, waiter_num, shift_name });
				Integer pid = posDao.queryForInt(qseq, new Object[] {});
				list.add(pid);
				// 存次表明细
				posDao.update(sql.toString(), new Object[]
				{ pid, tenancyId, organId, billno, organId, rwid, billno, organId, printer_id, print_format });
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
			logger.info("Exception:" + ExceptionMessage.getExceptionMessage(e));
			throw SystemException.getInstance(PosErrorCode.OPER_ERROR);
		}
		// 退菜和转台 地理打印机要打印
		// 查询地理打印机
		String qDili = new String("select hpp.id,hpp.name,hppf.class_item_code from hq_print_printer hpp left join hq_print_printer_format hppf on hpp.id = hppf.print_printer_id where hpp.isdili = 'Y' and hpp.valid_state = '1' and hpp.store_id = ? ");

		StringBuilder imsql = new StringBuilder(" select bill.guest,bill.waiter_num,shift.name as shift_name,item.rwid,item.table_code from pos_bill_item as item  ");
		imsql.append(" left join pos_bill bill on item.bill_num=bill.bill_num and item.tenancy_id=bill.tenancy_id and item.store_id=bill.store_id left join duty_order shift on item.item_shift_id=shift.id ");
		imsql.append(" where item.bill_num = ? and item.store_id = ? and item.rwid in (" + rwids + ")");

		/**
		 * 往子表里插入菜品明细，用来打印
		 */
		StringBuilder isubSql = new StringBuilder(" insert into pos_print_list(tenancy_id,store_id,table_code,table_name,bill_num,item_num,item_name,item_english,item_unit_name,item_price,item_count,update_time,zfkwnr,item_property,");
		isubSql.append("item_remark,item_serial,print_code,parent_id,waitcall_tag,rwid) ");

		isubSql.append(" select item.tenancy_id,item.store_id,bill.table_code,table1.table_name,item.bill_num,dish.item_code,item.item_name,item.item_english,item.item_unit_name,item.item_price,item.item_count,CURRENT_TIMESTAMP(0) :: TIMESTAMP WITHOUT TIME ZONE ,'',item.item_property,item.item_remark,item.item_serial,item.rwid,?,item.waitcall_tag,item.rwid ");
		isubSql.append(" from pos_bill_item as item left join pos_bill bill on item.bill_num=bill.bill_num ");
		isubSql.append(" left join hq_item_info dish on item.item_id=dish.id left join tables_info table1 on bill.table_code=table1.table_code and bill.store_id = table1.organ_id ");
		isubSql.append(" where item.rwid = ? and item.bill_num = ? and item.store_id = ? ");

		try
		{
			SqlRowSet rs = posDao.query4SqlRowSet(qDili, new Object[]
			{ organId });
			while (rs.next())
			{
				Integer printer_id = rs.getInt("id");
				String printer_name = rs.getString("name");
				String class_item_code = rs.getString("class_item_code");

				if ("TC01".equalsIgnoreCase(operType) || "ZT06".equalsIgnoreCase(operType))
				{
					// 单切插入主表并记录主表的信息
					SqlRowSet rsdili = posDao.query4SqlRowSet(imsql.toString(), new Object[]
					{ billno, organId });
					while (rsdili.next())
					{
						Integer rwid = rsdili.getInt("rwid");
						String tableCode = rsdili.getString("table_code");
						Integer guest = rsdili.getInt("guest");
						String waiter_num = rsdili.getString("waiter_num");
						String shift_name = rsdili.getString("shift_name");

						// 存主表
						posDao.update(insertMPrint, new Object[]
						{ tenancyId, organId, "0", class_item_code, oper, printer_id, printer_name, billno, tableCode, "*", operType, timestamp, null, guest, waiter_num, shift_name });
						Integer pid = posDao.queryForInt(qseq, new Object[] {});
						list.add(pid);
						// 存次表明细
						posDao.update(isubSql.toString(), new Object[]
						{ pid, rwid, billno, organId });
					}
				}
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
			throw SystemException.getInstance(PosErrorCode.OPER_ERROR);
		}

		return list;
	}

	@Override
	public List<Integer> orderRetreat(String tenancyId, String billno, Integer organId, String oper, String operType, String rwids)
	{
		try
		{
			List<Integer> list = new ArrayList<Integer>();

			if ("TC01".equalsIgnoreCase(operType))
			{
				String tableCode = null;
				String qtCode = new String(" select table_code from pos_bill where bill_num = ? and store_id = ? ");
				SqlRowSet rsTable = posDao.query4SqlRowSet(qtCode, new Object[]
				{ billno, organId });
				if (rsTable.next())
				{
					tableCode = rsTable.getString("table_code");
				}

				// 厨房打印机是否打印单品转台单
				String isChefPrint = posDao.getSysParameter(tenancyId, organId, "CFDYJSFDYTCD");

				if ("1".equals(isChefPrint))
				{
					String chefPrintFormat = posDao.getSysParameter(tenancyId, organId, "CFDYJTCDYMB");

					if (Tools.isNullOrEmpty(chefPrintFormat))
					{
						list.addAll(this.getKitchenPrintData(tenancyId, organId, billno, rwids, oper));
					}
					else
					{
						String isAreaPrint = posDao.getSysParameter(tenancyId, organId, "XFDYJSFKYQYDY");

						String insertMPrint = new String(" insert into pos_print (tenancy_id,store_id,print_type,print_format,print_property,printer_id,printer_name,bill_num,table_code,print_tag,remark,print_time,finish_time,guest,waiter_num,shift_name) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ");
						// 获取当前的序列
						String qseq = new String("select currval('pos_print_id_seq'::regclass) ");
						Timestamp timestamp = DateUtil.currentTimestamp();

						if (SysDictionary.PRINT_CODE_1106.equals(chefPrintFormat))
						{
							StringBuilder sql = new StringBuilder(" insert into pos_print_list(tenancy_id,store_id,table_code,table_name,bill_num,item_num,item_name,item_english,item_unit_name,item_price,item_count,update_time,zfkwnr,item_property,");
							sql.append("item_remark,item_serial,print_code,parent_id,waitcall_tag,rwid) ");
							sql.append("select item.tenancy_id,item.store_id,bill.table_code,table1.table_name,item.bill_num,dish.item_code,item.item_name,item.item_english,item.item_unit_name,item.item_price,item.item_count,CURRENT_TIMESTAMP(0) :: TIMESTAMP WITHOUT TIME ZONE ,concat(c.kwzf,' ',item.item_taste) as kwzf,item.item_property,item.item_remark,");
							sql.append("item.item_serial,item.rwid,?,item.waitcall_tag,item.rwid ");
							sql.append("from pos_bill_item as item left join pos_bill bill on item.bill_num=bill.bill_num and item.tenancy_id=bill.tenancy_id and item.store_id=bill.store_id ");
							sql.append(" left join duty_order shift on item.item_shift_id=shift.id left join tables_info table1 on bill.table_code=table1.table_code and table1.organ_id=bill.store_id left join hq_item_info dish on item.item_id=dish.id and dish.tenancy_id=item.tenancy_id ");
							sql.append(" left join hq_kvs_item printitem on dish.id=printitem.item_id and kvs_id in (select hk.id from hq_kvs hk where hk.tenancy_id=? and hk.store_id= ? )");
							sql.append(" left join hq_kvs_printer classprint on printitem.kvs_id=classprint.kvs_id left join hq_print_printer printer on classprint.printer_id=printer.id ");
							// sql.append(" left join hq_print_printer_format printformat on printer.id=printformat.print_printer_id and class_item_code not in('1001','1002','1003','1004') ");
							sql.append(" left join (select array_to_string(array_accum(zfkw_name), ' ') as kwzf,zfkw.rwid from pos_zfkw_item zfkw where zfkw.bill_num = ? and zfkw.store_id=? group by zfkw.rwid) c on item.rwid = c.rwid ");
							sql.append(" where item.rwid = ? and item.bill_num = ? and item.store_id = ? and classprint.printer_id = ?");

							String comboPrintType = SysDictionary.COMBOPRINT_TYPE_ALL;

							comboPrintType = posDao.getSysParameter(tenancyId, organId, "comboprint_type");

							// 单切 Single
							StringBuilder sql1106 = new StringBuilder(" select bill.guest,bill.waiter_num,shift.name as shift_name,item.rwid,classprint.printer_id,printer.name as printer_name,bill.table_code from pos_bill_item as item ");
							sql1106.append(" left join pos_bill bill on item.bill_num=bill.bill_num and item.tenancy_id=bill.tenancy_id and item.store_id=bill.store_id left join duty_order shift on item.item_shift_id=shift.id ");
							sql1106.append(" left join tables_info table1 on bill.table_code=table1.table_code and table1.organ_id=bill.store_id left join hq_item_info dish on item.item_id=dish.id and dish.tenancy_id=item.tenancy_id ");
							sql1106.append(" left join hq_kvs_item printitem on dish.id=printitem.item_id and kvs_id in (select hk.id from hq_kvs hk where hk.store_id= ? and hk.tenancy_id=?) ");
							sql1106.append(" left join hq_kvs_printer classprint on printitem.kvs_id=classprint.kvs_id left join hq_print_printer printer on classprint.printer_id=printer.id ");
							// sql1106.append(" left join hq_print_printer_format printformat on printer.id=printformat.print_printer_id and class_item_code not in('1001','1002','1003','1004') ");
							sql1106.append(" where item.bill_num = ? and item.rwid in (" + rwids + ") and item.store_id = ? ");
							if (StringUtils.equals(SysDictionary.COMBOPRINT_TYPE_MEALLIST, comboPrintType))
							{
								sql1106.append(" and (item.item_property = '" + SysDictionary.ITEM_PROPERTY_SINGLE + "' or item.item_property = '" + SysDictionary.ITEM_PROPERTY_MEALLIST + "')");
							}
							else if (StringUtils.equals(SysDictionary.COMBOPRINT_TYPE_SETMEAL, comboPrintType))
							{
								sql1106.append(" and (item.item_property = '" + SysDictionary.ITEM_PROPERTY_SINGLE + "' or item.item_property = '" + SysDictionary.ITEM_PROPERTY_SETMEAL + "')");
							}

							if ("1".equals(isAreaPrint))
							{
								sql1106.append(" and classprint.printer_id in (select printer_id from hq_table_printer where tenancy_id='").append(tenancyId).append("' and table_code='").append(tableCode).append("')");
							}

							sql1106.append(" order by rwid ");

							SqlRowSet rs1106 = posDao.query4SqlRowSet(sql1106.toString(), new Object[]
							{ organId, tenancyId, billno, organId });
							while (rs1106.next())
							{
								// String print_format =
								// rs1106.getString("print_format");
								Integer printer_id = rs1106.getInt("printer_id");
								String printer_name = rs1106.getString("printer_name");
								Integer guest = rs1106.getInt("guest");
								String waiter_num = rs1106.getString("waiter_num");
								String shift_name = rs1106.getString("shift_name");
								// String tableCode =
								// rs1106.getString("table_code");
								Integer rwid = rs1106.getInt("rwid");

								// 存主表
								posDao.update(insertMPrint, new Object[]
								{ tenancyId, organId, "0", chefPrintFormat, oper, printer_id, printer_name, billno, tableCode, "*", null, timestamp, null, guest, waiter_num, shift_name });
								Integer pid = posDao.queryForInt(qseq, new Object[] {});

								list.add(pid);

								// 存次表明细
								posDao.update(sql.toString(), new Object[]
								{ pid, tenancyId, organId, billno, organId, rwid, billno, organId, printer_id });
							}

						}

						else if (SysDictionary.PRINT_CODE_1107.equals(chefPrintFormat))
						{
							// 整台 all
							StringBuilder sql = new StringBuilder(" insert into pos_print_list(tenancy_id,store_id,table_code,table_name,bill_num,item_num,item_name,item_english,item_unit_name,item_price,item_count,update_time,zfkwnr,item_property,");
							sql.append("item_remark,item_serial,print_code,parent_id,waitcall_tag,rwid) ");
							sql.append("select item.tenancy_id,item.store_id,bill.table_code,table1.table_name,item.bill_num,dish.item_code,item.item_name,item.item_english,item.item_unit_name,item.item_price,item.item_count,CURRENT_TIMESTAMP(0) :: TIMESTAMP WITHOUT TIME ZONE ,concat(c.kwzf,' ',item.item_taste) as kwzf,item.item_property,item.item_remark,");
							sql.append("item.item_serial,item.rwid,?,item.waitcall_tag,item.rwid ");
							sql.append("from pos_bill_item as item left join pos_bill bill on item.bill_num=bill.bill_num and item.tenancy_id=bill.tenancy_id and item.store_id=bill.store_id ");
							sql.append(" left join duty_order shift on item.item_shift_id=shift.id left join tables_info table1 on bill.table_code=table1.table_code and table1.organ_id=bill.store_id left join hq_item_info dish on item.item_id=dish.id and dish.tenancy_id=item.tenancy_id ");
							sql.append(" left join hq_kvs_item printitem on dish.id=printitem.item_id and kvs_id in (select hk.id from hq_kvs hk where hk.tenancy_id=? and hk.store_id= ? )");
							sql.append(" left join hq_kvs_printer classprint on printitem.kvs_id=classprint.kvs_id left join hq_print_printer printer on classprint.printer_id=printer.id ");
							// sql.append(" left join hq_print_printer_format printformat on printer.id=printformat.print_printer_id and class_item_code not in('1001','1002','1003','1004') ");
							sql.append(" left join (select array_to_string(array_accum(zfkw_name), ' ') as kwzf,zfkw.rwid from pos_zfkw_item zfkw where zfkw.bill_num = ? and zfkw.store_id=? group by zfkw.rwid) c on item.rwid = c.rwid ");
							sql.append(" where item.rwid in (" + rwids + ") and item.bill_num = ? and item.store_id = ? and classprint.printer_id = ? ");

							StringBuilder sql1107 = new StringBuilder(" select bill.guest,bill.waiter_num,shift.name as shift_name,classprint.printer_id,printer.name as printer_name,bill.table_code from pos_bill_item as item ");
							sql1107.append(" left join pos_bill bill on item.bill_num=bill.bill_num and item.tenancy_id=bill.tenancy_id and item.store_id=bill.store_id left join duty_order shift on item.item_shift_id=shift.id ");
							sql1107.append(" left join tables_info table1 on bill.table_code=table1.table_code and table1.organ_id=bill.store_id left join hq_item_info dish on item.item_id=dish.id and dish.tenancy_id=item.tenancy_id ");
							sql1107.append(" left join hq_kvs_item printitem on dish.id=printitem.item_id and kvs_id in (select hk.id from hq_kvs hk where hk.store_id= ? and hk.tenancy_id=?) ");
							sql1107.append(" left join hq_kvs_printer classprint on printitem.kvs_id=classprint.kvs_id left join hq_print_printer printer on classprint.printer_id=printer.id ");
							// sql1107.append(" left join hq_print_printer_format printformat on printer.id=printformat.print_printer_id and class_item_code not in('1001','1002','1003','1004') ");
							sql1107.append(" where item.bill_num = ? and item.rwid in (" + rwids + ") and item.store_id = ? ");

							if ("1".equals(isAreaPrint))
							{
								sql1107.append(" and classprint.printer_id in (select printer_id from hq_table_printer where tenancy_id='").append(tenancyId).append("' and table_code='").append(tableCode).append("')");
							}

							sql1107.append(" group by printer_id,printer_name,guest,shift_name,bill.waiter_num,bill.table_code ");

							SqlRowSet rsClass = posDao.query4SqlRowSet(sql1107.toString(), new Object[]
							{ organId, tenancyId, billno, organId });
							while (rsClass.next())
							{
								Map<String, String> map = new HashMap<String, String>();

								// String print_format =
								// rsClass.getString("print_format");
								Integer printer_id = rsClass.getInt("printer_id");
								String printer_name = rsClass.getString("printer_name");
								Integer guest = rsClass.getInt("guest");
								String waiter_num = rsClass.getString("waiter_num");
								String shift_name = rsClass.getString("shift_name");
								// String tableCode =
								// rsClass.getString("table_code");

								map.put("print_format", chefPrintFormat);
								map.put("printer_id", printer_id + "");
								map.put("printer_name", printer_name);

								// 存主表
								posDao.update(insertMPrint, new Object[]
								{ tenancyId, organId, "0", chefPrintFormat, oper, printer_id, printer_name, billno, tableCode, "*", null, timestamp, null, guest, waiter_num, shift_name });
								Integer pid = posDao.queryForInt(qseq, new Object[] {});

								list.add(pid);

								// SqlRowSet rsi =
								// posDao.query4SqlRowSet(queryItem.toString(),
								// new Object[]
								// { organId, tenancyId, billno, organId,
								// printer_id, chefPrintFormat });

								posDao.update(sql.toString(), new Object[]
								{ pid, tenancyId, organId, billno, organId, billno, organId, printer_id });
							}
						}
						else
						{
							list.addAll(this.getKitchenPrintData(tenancyId, organId, billno, rwids, oper));
						}
					}
				}

				// 退菜和转台 地理打印机要打印
				String isDiliPrint = posDao.getSysParameter(tenancyId, organId, "CCDYJSFDYTCD");

				if ("1".equals(isDiliPrint))
				{
					/**
					 * 获取当前的序列
					 */
					String qseq = new String("select currval('pos_print_id_seq'::regclass) ");
					/**
					 * 往主表里插入打印参数
					 */
					String insertMPrint = new String(" insert into pos_print (tenancy_id,store_id,print_type,print_format,print_property,printer_id,printer_name,bill_num,table_code,print_tag,remark,print_time,finish_time,guest,waiter_num,shift_name) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ");

					Timestamp timestamp = DateUtil.currentTimestamp();

					StringBuilder imsql = new StringBuilder(" select bill.guest,bill.waiter_num,shift.name as shift_name,item.rwid,item.table_code from pos_bill_item as item  ");
					imsql.append(" left join pos_bill bill on item.bill_num=bill.bill_num and item.tenancy_id=bill.tenancy_id and item.store_id=bill.store_id left join duty_order shift on item.item_shift_id=shift.id ");
					imsql.append(" where item.bill_num = ? and item.store_id = ? and item.rwid in (" + rwids + ")");

					/**
					 * 往子表里插入菜品明细，用来打印
					 */
					StringBuilder isubSql = new StringBuilder(
							" insert into pos_print_list(tenancy_id,store_id,table_code,table_name,bill_num,item_num,item_name,item_english,item_unit_name,item_price,item_count,update_time,zfkwnr,item_property,item_remark,item_serial,print_code,parent_id,waitcall_tag,rwid) ");
					isubSql.append(" select item.tenancy_id,item.store_id,bill.table_code,table1.table_name,item.bill_num,dish.item_code,item.item_name,item.item_english,item.item_unit_name,item.item_price,item.item_count,CURRENT_TIMESTAMP(0) :: TIMESTAMP WITHOUT TIME ZONE ,'',item.item_property,item.item_remark,item.item_serial,item.rwid,?,item.waitcall_tag,item.rwid ");
					isubSql.append(" from pos_bill_item as item left join pos_bill bill on item.bill_num=bill.bill_num ");
					isubSql.append(" left join hq_item_info dish on item.item_id=dish.id left join tables_info table1 on bill.table_code=table1.table_code and bill.store_id = table1.organ_id ");
					isubSql.append(" where item.rwid = ? and item.bill_num = ? and item.store_id = ? ");

					String printFormat = posDao.getSysParameter(tenancyId, organId, "TCDYMB");

					String isAreaPrint = posDao.getSysParameter(tenancyId, organId, "CCDYJSFKYQYDY");

					// 查询地理打印机
					StringBuilder qDili = new StringBuilder(
							"select hpp.id,hpp.name,hppf.class_item_code as print_format from hq_print_printer hpp left join hq_print_printer_format hppf on hpp.id = hppf.print_printer_id where hpp.isdili = 'Y' and hpp.valid_state = '1' and hpp.store_id = ? and hpp.tenancy_id=?");
					if ("1".equals(isAreaPrint))
					{
						qDili.append(" and hpp.id in (select printer_id from hq_table_printer where tenancy_id='").append(tenancyId).append("' and table_code='").append(tableCode).append("')");
					}

					SqlRowSet rs = posDao.query4SqlRowSet(qDili.toString(), new Object[]
					{ organId, tenancyId });
					while (rs.next())
					{
						Integer printer_id = rs.getInt("id");
						String printer_name = rs.getString("name");
						// String printFormat = rs.getString("print_format");
						// 单切插入主表并记录主表的信息
						SqlRowSet rsdili = posDao.query4SqlRowSet(imsql.toString(), new Object[]
						{ billno, organId });
						Integer pid = null;
						while (rsdili.next())
						{
							Integer rwid = rsdili.getInt("rwid");
							// String tableCode =
							// rsdili.getString("table_code");
							Integer guest = rsdili.getInt("guest");
							String waiter_num = rsdili.getString("waiter_num");
							String shift_name = rsdili.getString("shift_name");

							if (SysDictionary.PRINT_CODE_1106.equals(printFormat) || Tools.isNullOrEmpty(pid))
							{
								// 存主表
								posDao.update(insertMPrint, new Object[]
								{ tenancyId, organId, "0", printFormat, oper, printer_id, printer_name, billno, tableCode, "*", operType, timestamp, null, guest, waiter_num, shift_name });
								pid = posDao.queryForInt(qseq, new Object[] {});
								list.add(pid);
							}
							// 存次表明细
							posDao.update(isubSql.toString(), new Object[]
							{ pid, rwid, billno, organId });
						}
					}

				}
			}
			return list;
		}
		catch (SystemException se)
		{
			throw se;
		}
		catch (Exception e)
		{
			e.printStackTrace();
			logger.info("退菜打印：" + ExceptionMessage.getExceptionMessage(e));
			throw new SystemException(PosErrorCode.OPER_ERROR);
		}
	}

	@Override
	public List<Integer> orderSingleChgChef(String tenancyId, String billno, Integer organId, String oper, String operType, String tableCode, String tableTag, String rwids)
	{
		List<Integer> list = new ArrayList<Integer>();

		Timestamp timestamp = DateUtil.currentTimestamp();
		
		if ("ZT06".equalsIgnoreCase(operType))
		{
			// 厨房打印机是否打印单品转台单
			String isChefPrint = null;
			try
			{
				isChefPrint = posDao.getSysParameter(tenancyId, organId, "CFDYJSFDYDPZTD");
			}
			catch (Exception e)
			{
				e.printStackTrace();
			}
//			String qcpt = new String(" select para_value from sys_parameter where para_code = 'CFDYJSFDYDPZTD' and valid_state='1' ");
//			SqlRowSet qcptRs = posDao.query4SqlRowSet(qcpt);
//			if (qcptRs.next())
//			{
//				isDili = qcptRs.getString("para_value");
//			}

			if ("1".equals(isChefPrint))
			{
				list.addAll(this.getKitchenPrintData(tenancyId, organId, billno, rwids, oper));
			}

			// 查询地理打印机
			String isDiliPrint = null;
			try
			{
				isDiliPrint = posDao.getSysParameter(tenancyId, organId, "CCDYJSFDYDPZTD");
			}
			catch (Exception e)
			{
				e.printStackTrace();
			}
			
			if ("1".equals(isDiliPrint))
			{
				String isAreaPrint="";
				try
				{
					isAreaPrint = posDao.getSysParameter(tenancyId, organId, "CCDYJSFKYQYDY");
				}
				catch (Exception e1)
				{
					e1.printStackTrace();
				}
				
				// 退菜和转台 地理打印机要打印
				StringBuilder imsql = new StringBuilder(" select bill.guest,bill.waiter_num,shift.name as shift_name,item.rwid,item.table_code from pos_bill_item as item  ");
				imsql.append(" left join pos_bill bill on item.bill_num=bill.bill_num and item.tenancy_id=bill.tenancy_id and item.store_id=bill.store_id left join duty_order shift on item.item_shift_id=shift.id ");
				imsql.append(" where item.bill_num = ? and item.store_id = ? and item.rwid in (" + rwids + ")");
				/**
				 * 往主表里插入打印参数
				 */
				String insertMPrint = new String(" insert into pos_print (tenancy_id,store_id,print_type,print_format,print_property,printer_id,printer_name,bill_num,table_code,print_tag,remark,print_time,finish_time,guest,waiter_num,shift_name) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ");
				/**
				 * 获取当前的序列
				 */
				String qseq = new String("select currval('pos_print_id_seq'::regclass) ");
				/**
				 * 往子表里插入菜品明细，用来打印
				 */
				StringBuilder isubSql = new StringBuilder(" insert into pos_print_list(tenancy_id,store_id,table_code,table_name,bill_num,item_num,item_name,item_english,item_unit_name,item_price,item_count,update_time,zfkwnr,item_property,");
				isubSql.append("item_remark,item_serial,print_code,parent_id,waitcall_tag,rwid) ");
				isubSql.append(" select item.tenancy_id,item.store_id,?,?,item.bill_num,dish.item_code,item.item_name,item.item_english,item.item_unit_name,item.item_price,item.item_count,CURRENT_TIMESTAMP(0) :: TIMESTAMP WITHOUT TIME ZONE ,'',item.item_property,item.item_remark,item.item_serial,item.rwid,?,item.waitcall_tag,item.rwid ");
				isubSql.append(" from pos_bill_item as item left join pos_bill bill on item.bill_num=bill.bill_num ");
				isubSql.append(" left join hq_item_info dish on item.item_id=dish.id left join tables_info table1 on bill.table_code=table1.table_code and bill.store_id = table1.organ_id ");
				isubSql.append(" where item.rwid = ? and item.bill_num = ? and item.store_id = ? ");
				
				StringBuilder qDili = new StringBuilder("select pr.id,pr.name,pf.class_item_code from hq_print_printer pr left join hq_print_printer_format pf on pr.id=pf.print_printer_id and pr.tenancy_id=pf.tenancy_id where pr.isdili = 'Y' and pr.valid_state = '1' and pr.store_id = ?");
				if("1".equals(isAreaPrint))
				{
					qDili.append(" and pr.id in (select printer_id from hq_table_printer where tenancy_id='").append(tenancyId).append("' and table_code='").append(tableCode).append("')");
				}
				try
				{
					SqlRowSet rs = posDao.query4SqlRowSet(qDili.toString(), new Object[]
					{ organId });
					while (rs.next())
					{
						Integer printer_id = rs.getInt("id");
						String printer_name = rs.getString("name");
						String printFormat = rs.getString("class_item_code");
						// 单切插入主表并记录主表的信息
						SqlRowSet rsdili = posDao.query4SqlRowSet(imsql.toString(), new Object[]
						{ billno, organId });
						while (rsdili.next())
						{
							Integer rwid = rsdili.getInt("rwid");
							Integer guest = rsdili.getInt("guest");
							String waiter_num = rsdili.getString("waiter_num");
							String shift_name = rsdili.getString("shift_name");
							// 存主表
							posDao.update(insertMPrint, new Object[]
							{ tenancyId, organId, "0", printFormat, oper, printer_id, printer_name, billno, tableCode, "*", operType, timestamp, null, guest, waiter_num, shift_name });
							//
							Integer pid = posDao.queryForInt(qseq, new Object[] {});
							list.add(pid);
							// 存次表明细
							posDao.update(isubSql.toString(), new Object[]
							{ tableTag, tableTag, pid, rwid, billno, organId });
						}
					}
				}
				catch (Exception e)
				{
					e.printStackTrace();
					logger.info("Exception:" + ExceptionMessage.getExceptionMessage(e));
					throw SystemException.getInstance(PosErrorCode.OPER_ERROR);
				}
			}
		}

		return list;
	}

	@Override
	public List<Integer> orderWholeChgChef(String tenancyId, String billno, Integer organId, String oper, String operType, String tableTag, String tableNameTag)
	{
		try
		{
			List<Integer> list = new ArrayList<Integer>();

			String isAreaPrint = posDao.getSysParameter(tenancyId, organId, "CCDYJSFKYQYDY");

			String tableCode = null;
			String qtCode = new String(" select table_code from pos_bill where bill_num = ? and store_id = ? ");
			SqlRowSet rsTable = posDao.query4SqlRowSet(qtCode, new Object[]
			{ billno, organId });
			if (rsTable.next())
			{
				tableCode = rsTable.getString("table_code");
			}

			StringBuilder sb = new StringBuilder("select printer.id as printer_id,printer.name as printer_name from hq_print_printer printer where printer.isdili='Y' and printer.valid_state='1' and store_id = ?");

			if ("1".equals(isAreaPrint))
			{
				sb.append(" and printer.id in (select printer_id from hq_table_printer where tenancy_id='").append(tenancyId).append("' and table_code='").append(tableCode).append("')");
			}

			// String print_format = "1108";

			/**
			 * 往主表里插入打印参数
			 */
			String insertMPrint = new String(" insert into pos_print (tenancy_id,store_id,print_type,print_format,print_property,printer_id,printer_name,bill_num,table_name,table_code,print_tag,remark,print_time,finish_time) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?) ");

			/**
			 * 获取当前的序列
			 */
			String qseq = new String("select currval('pos_print_id_seq'::regclass) ");

			Timestamp timestamp = DateUtil.currentTimestamp();

			// 单切插入主表并记录主表的信息

			SqlRowSet rs = posDao.query4SqlRowSet(sb.toString(), new Object[]
			{ organId });
			while (rs.next())
			{
				Integer printer_id = rs.getInt("printer_id");
				String printer_name = rs.getString("printer_name");
				// 存主表
				posDao.update(insertMPrint, new Object[]
				{ tenancyId, organId, "0", SysDictionary.PRINT_CODE_1108, oper, printer_id, printer_name, billno, tableNameTag, tableTag, "*", operType, timestamp, null });
				Integer pid = posDao.queryForInt(qseq, new Object[] {});
				list.add(pid);
			}

			return list;
		}
		catch (SystemException se)
		{
			throw se;
		}
		catch (Exception e)
		{
			e.printStackTrace();
			logger.info("打印：" + ExceptionMessage.getExceptionMessage(e));
			throw new SystemException(PosErrorCode.OPER_ERROR);
		}
	}

	/**
	 * @param tenancyId
	 * @param organId
	 * @param billno
	 * @param rwids
	 * @param printProperty
	 * @return
	 */
	private List<Integer> getKitchenPrintData(String tenancyId, Integer organId, String billno, String rwids, String printProperty)
	{
		List<Integer> list = new ArrayList<Integer>();
		try
		{
			Timestamp timestamp = DateUtil.currentTimestamp();
			
			if(Tools.isNullOrEmpty(rwids))
			{
				throw SystemException.getInstance(PosErrorCode.NOT_NULL_RWID);
			}

			String tableCode = null;
			String qtCode = new String(" select table_code from pos_bill where bill_num = ? and store_id = ? ");
			SqlRowSet rs = posDao.query4SqlRowSet(qtCode, new Object[]
			{ billno, organId });
			if (rs.next())
			{
				tableCode = rs.getString("table_code");
			}
			
			String isAreaPrint = posDao.getSysParameter(tenancyId, organId, "XFDYJSFKYQYDY");
			
			// 打印单套餐显示方式
			String comboPrintType = posDao.getSysParameter(tenancyId, organId, "comboprint_type");

			/**
			 * 往主表里插入打印参数
			 */
			String insertMPrint = new String(" insert into pos_print (tenancy_id,store_id,print_type,print_format,print_property,printer_id,printer_name,bill_num,table_code,print_tag,remark,print_time,finish_time,guest,waiter_num,shift_name) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ");

			/**
			 * 往子表里插入菜品明细，用来打印
			 */
			StringBuilder sql = new StringBuilder(" insert into pos_print_list(tenancy_id,store_id,table_code,table_name,bill_num,item_num,item_name,item_english,item_unit_name,item_price,item_count,update_time,zfkwnr,item_property,");
			sql.append("item_remark,item_serial,print_code,parent_id,waitcall_tag,rwid) ");
			sql.append("select item.tenancy_id,item.store_id,bill.table_code,table1.table_name,item.bill_num,dish.item_code,item.item_name,item.item_english,item.item_unit_name,item.item_price,item.item_count,CURRENT_TIMESTAMP(0) :: TIMESTAMP WITHOUT TIME ZONE ,concat(c.kwzf,' ',item.item_taste) as kwzf,item.item_property,item.item_remark,");
			sql.append("item.item_serial,item.rwid,?,item.waitcall_tag,item.rwid ");
			sql.append("from pos_bill_item as item left join pos_bill bill on item.bill_num=bill.bill_num and item.tenancy_id=bill.tenancy_id and item.store_id=bill.store_id ");
			sql.append(" left join duty_order shift on item.item_shift_id=shift.id left join tables_info table1 on bill.table_code=table1.table_code and table1.organ_id=bill.store_id left join hq_item_info dish on item.item_id=dish.id and dish.tenancy_id=item.tenancy_id ");
			sql.append(" left join hq_kvs_item printitem on dish.id=printitem.item_id and kvs_id in (select hk.id from hq_kvs hk where hk.tenancy_id=? and hk.store_id= ? )");
			sql.append(" left join hq_kvs_printer classprint on printitem.kvs_id=classprint.kvs_id left join hq_print_printer printer on classprint.printer_id=printer.id ");
			sql.append(" left join hq_print_printer_format printformat on printer.id=printformat.print_printer_id and class_item_code not in('1001','1002','1003','1004') ");
			sql.append(" left join (select array_to_string(array_accum(zfkw_name), ' ') as kwzf,zfkw.rwid from pos_zfkw_item zfkw where zfkw.bill_num = ? and zfkw.store_id=? group by zfkw.rwid) c on item.rwid = c.rwid ");
			sql.append(" where item.rwid = ? and item.bill_num = ? and item.store_id = ? and classprint.printer_id = ? and printformat.class_item_code = ? ");
			
			StringBuilder sql1106 = new StringBuilder(" select bill.guest,bill.waiter_num,shift.name as shift_name,item.rwid,printformat.class_item_code as print_format,classprint.printer_id,printer.name as printer_name,bill.table_code,item.item_count from pos_bill_item as item ");
			sql1106.append(" left join pos_bill bill on item.bill_num=bill.bill_num and item.tenancy_id=bill.tenancy_id and item.store_id=bill.store_id left join duty_order shift on item.item_shift_id=shift.id ");
			sql1106.append(" left join tables_info table1 on bill.table_code=table1.table_code and table1.organ_id=bill.store_id left join hq_item_info dish on item.item_id=dish.id and dish.tenancy_id=item.tenancy_id ");
			sql1106.append(" left join hq_kvs_item printitem on dish.id=printitem.item_id and kvs_id in (select hk.id from hq_kvs hk where hk.store_id= ? and hk.tenancy_id=?) ");
			sql1106.append(" left join hq_kvs_printer classprint on printitem.kvs_id=classprint.kvs_id left join hq_print_printer printer on classprint.printer_id=printer.id ");
			sql1106.append(" left join hq_print_printer_format printformat on printer.id=printformat.print_printer_id and class_item_code not in('1001','1002','1003','1004') ");
			sql1106.append(" where item.bill_num = ? and item.rwid in (" + rwids + ") and item.store_id = ? and printformat.class_item_code = ? ");
			// 仅明细项打印
			if (StringUtils.equals(SysDictionary.COMBOPRINT_TYPE_MEALLIST, comboPrintType))
			{
				sql1106.append(" and (item.item_property = '"+SysDictionary.ITEM_PROPERTY_SINGLE+"' or item.item_property = '"+SysDictionary.ITEM_PROPERTY_MEALLIST+"')");
			}
			else if (StringUtils.equals(SysDictionary.COMBOPRINT_TYPE_SETMEAL, comboPrintType))
			{
				sql1106.append(" and (item.item_property = '"+SysDictionary.ITEM_PROPERTY_SINGLE+"' or item.item_property = '"+SysDictionary.ITEM_PROPERTY_SETMEAL+"')");
			}
			
			if("1".equals(isAreaPrint))
			{
				sql1106.append(" and classprint.printer_id in (select printer_id from hq_table_printer where tenancy_id='").append(tenancyId).append("' and table_code='").append(tableCode).append("')");
			}
			
			sql1106.append(" order by rwid ");
			
			String qseq = new String("select currval('pos_print_id_seq'::regclass) ");
			
			// 单切插入主表并记录主表的信息
			SqlRowSet rs1106 = posDao.query4SqlRowSet(sql1106.toString(), new Object[]
			{ organId, tenancyId, billno, organId, SysDictionary.PRINT_CODE_1106 });
			while (rs1106.next())
			{
				String print_format = rs1106.getString("print_format");
				Integer printer_id = rs1106.getInt("printer_id");
				String printer_name = rs1106.getString("printer_name");
				Integer guest = rs1106.getInt("guest");
				String waiter_num = rs1106.getString("waiter_num");
				String shift_name = rs1106.getString("shift_name");
//				String tableCode = rs1106.getString("table_code");
				Integer rwid = rs1106.getInt("rwid");

				// 存主表
				posDao.update(insertMPrint, new Object[]
				{ tenancyId, organId, "0", print_format, printProperty, printer_id, printer_name, billno, tableCode, "*", null, timestamp, null, guest, waiter_num, shift_name });
				Integer pid = posDao.queryForInt(qseq, new Object[] {});
				list.add(pid);
				// 存次表明细
				posDao.update(sql.toString(), new Object[]
				{ pid, tenancyId, organId, billno, organId, rwid, billno, organId, printer_id, print_format });
			}
			
			// 单切插入主表并记录主表的信息
			SqlRowSet rs1104 = posDao.query4SqlRowSet(sql1106.toString(), new Object[]
			{ organId, tenancyId, billno, organId, SysDictionary.PRINT_CODE_1104 });
			while (rs1104.next())
			{
				String print_format = rs1104.getString("print_format");
				Integer printer_id = rs1104.getInt("printer_id");
				String printer_name = rs1104.getString("printer_name");
				Integer guest = rs1104.getInt("guest");
				String waiter_num = rs1104.getString("waiter_num");
				String shift_name = rs1104.getString("shift_name");
				// String tableCode = rs1106.getString("table_code");
				Integer rwid = rs1104.getInt("rwid");

				Double item_count = rs1104.getDouble("item_count");

				for(int i=0;i<item_count;i++)
				{
					// 存主表
					posDao.update(insertMPrint, new Object[]
					{ tenancyId, organId, "0", print_format, printProperty, printer_id, printer_name, billno, tableCode, "*", null, timestamp, null, guest, waiter_num, shift_name });
					Integer pid = posDao.queryForInt(qseq, new Object[] {});
					list.add(pid);
					// 存次表明细
					posDao.update(sql.toString(), new Object[]
					{ pid, tenancyId, organId, billno, organId, rwid, billno, organId, printer_id, print_format });
				}
			}

			StringBuilder sql1107 = new StringBuilder(" select bill.guest,bill.waiter_num,shift.name as shift_name,printformat.class_item_code as print_format,classprint.printer_id,printer.name as printer_name,bill.table_code from pos_bill_item as item ");
			sql1107.append(" left join pos_bill bill on item.bill_num=bill.bill_num and item.tenancy_id=bill.tenancy_id and item.store_id=bill.store_id left join duty_order shift on item.item_shift_id=shift.id ");
			sql1107.append(" left join tables_info table1 on bill.table_code=table1.table_code and table1.organ_id=bill.store_id left join hq_item_info dish on item.item_id=dish.id and dish.tenancy_id=item.tenancy_id ");
			sql1107.append(" left join hq_kvs_item printitem on dish.id=printitem.item_id and kvs_id in (select hk.id from hq_kvs hk where hk.store_id= ? and hk.tenancy_id=?) ");
			sql1107.append(" left join hq_kvs_printer classprint on printitem.kvs_id=classprint.kvs_id left join hq_print_printer printer on classprint.printer_id=printer.id ");
			sql1107.append(" left join hq_print_printer_format printformat on printer.id=printformat.print_printer_id and class_item_code not in('1001','1002','1003','1004') ");
			sql1107.append(" where item.bill_num = ? and item.rwid in (" + rwids + ") and item.store_id = ? and printformat.class_item_code = ?");

			if("1".equals(isAreaPrint))
			{
				sql1107.append(" and classprint.printer_id in (select printer_id from hq_table_printer where tenancy_id='").append(tenancyId).append("' and table_code='").append(tableCode).append("')");
			}
			
			sql1107.append(" group by print_format,printer_id,printer_name,guest,shift_name,bill.waiter_num,bill.table_code ");

			/**
			 * 查询新下单的菜品
			 */
			StringBuilder queryItem = new StringBuilder(
					"select item.rwid,item.tenancy_id,item.store_id,printformat.class_item_code as print_format,classprint.printer_id, printer.name as printer_name,item.bill_num,bill.table_code from pos_bill_item as item left join pos_bill bill on item.bill_num=bill.bill_num and item.tenancy_id=bill.tenancy_id and item.store_id=bill.store_id ");
			queryItem.append(" left join duty_order shift on item.item_shift_id=shift.id left join tables_info table1 on bill.table_code=table1.table_code and table1.organ_id=bill.store_id left join hq_item_info dish on item.item_id=dish.id and dish.tenancy_id=item.tenancy_id ");
			queryItem.append(" left join hq_kvs_item printitem on dish.id=printitem.item_id and kvs_id in (select hk.id from hq_kvs hk where hk.store_id= ? and hk.tenancy_id=?) left join hq_kvs_printer classprint on printitem.kvs_id=classprint.kvs_id ");
			queryItem.append(" left join hq_print_printer printer on classprint.printer_id=printer.id left join hq_print_printer_format printformat on printer.id=printformat.print_printer_id and class_item_code not in('1001','1002','1003','1004') ");
			queryItem.append(" where item.rwid in (" + rwids + ") and item.bill_num = ? and item.store_id = ? and classprint.printer_id = ? and printformat.class_item_code = ? order by item.rwid asc ");

			// 整台插入主表并记录主表的信息
			SqlRowSet rsClass = posDao.query4SqlRowSet(sql1107.toString(), new Object[]
			{ organId, tenancyId, billno, organId, SysDictionary.PRINT_CODE_1107 });
			while (rsClass.next())
			{
				Map<String, String> map = new HashMap<String, String>();

				String print_format = rsClass.getString("print_format");
				Integer printer_id = rsClass.getInt("printer_id");
				String printer_name = rsClass.getString("printer_name");
				Integer guest = rsClass.getInt("guest");
				String waiter_num = rsClass.getString("waiter_num");
				String shift_name = rsClass.getString("shift_name");
//				String tableCode = rsClass.getString("table_code");

				map.put("print_format", print_format);
				map.put("printer_id", printer_id + "");
				map.put("printer_name", printer_name);

				// 存主表
				posDao.update(insertMPrint, new Object[]
				{ tenancyId, organId, "0", print_format, printProperty, printer_id, printer_name, billno, tableCode, "*", null, timestamp, null, guest, waiter_num, shift_name });
				Integer pid = posDao.queryForInt(qseq, new Object[] {});
				list.add(pid);

				SqlRowSet rsi = posDao.query4SqlRowSet(queryItem.toString(), new Object[]
				{ organId, tenancyId, billno, organId, printer_id, print_format });

				while (rsi.next())
				{
					Integer rwid = rsi.getInt("rwid");
					// 存次表明细
					posDao.update(sql.toString(), new Object[]
					{ pid, tenancyId, organId, billno, organId, rwid, billno, organId, printer_id, print_format });
				}
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		return list;
	}

	@Override
	public void customerPrint(String tenantId, Integer organId, String posNum, String operator, String updatetime, String printCode, String cardCode, String billCode, double consume_cardmoney, double main_trading, double reward_trading, double main_balance, double reward_balance, double total_main,
			double total_reward, double credit, double useful_credit, double income, double deposit, double sales_price,String card_class_name,String name,String mobil)
	{
//		posDao.customerPrint("1", tenantId, cardCode, billCode, posNum, printCode, organId, credit, useful_credit, main_balance, reward_balance, total_main, total_reward, operator, updatetime, income, consume_cardmoney, deposit, sales_price, main_trading, reward_trading,"",card_class_name,name,mobil);
		try
		{
			JSONObject param = new JSONObject();
			param.put("bill_code", billCode);
			param.put("card_code", cardCode);
			param.put("card_class_name", card_class_name);
			param.put("name", name);
			param.put("mobil", mobil);
			param.put("credit", credit);
			param.put("useful_credit", useful_credit);
			param.put("main_balance", main_balance);
			param.put("reward_balance", reward_balance);
			param.put("total_balance", DoubleHelper.add(main_balance, reward_balance, 4));
			param.put("total_main", total_main);
			param.put("total_reward", total_reward);
			param.put("income", income);
			param.put("consume_cardmoney", consume_cardmoney);
			param.put("deposit", deposit);
			param.put("sales_price", sales_price);
			param.put("operator", operator);
			param.put("updatetime", updatetime);
			param.put("main_trading", main_trading);
			param.put("reward_trading", reward_trading);
			param.put("payment_name1", "");
			posDao.customerPrint(tenantId, organId, posNum, printCode, "1", param, 1);
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
	}

	@Override
	public void printPosBill(Data param, Data result) throws SystemException
	{
		String tenantId = param.getTenancy_id();
		Integer organId = param.getStore_id();

		Map<String, Object> map = ReqDataUtil.getDataMap(param);

		String billNum = ParamUtil.getStringValue(map, "bill_num", false, null);

		String posNum = ParamUtil.getStringValue(map, "pos_num", false, null);// 机号

		String optNum = ParamUtil.getStringValue(map, "opt_num", false, null);

		Date reportDate = ParamUtil.getDateValue(map, "report_date", false, null);

		Integer shiftId = ParamUtil.getIntegerValue(map, "shift_id", false, null);

		String printType = ParamUtil.getStringValue(map, "print_type", false, null);
		if (StringUtils.isEmpty(printType))
		{
			printType = "0";
		}
		String printCode = ParamUtil.getStringValue(map, "print_code", true, PosErrorCode.NOT_NULL_PRINT_CODE);

		String cardCode = ParamUtil.getStringValue(map, "card_code", false, null);

		String billCode = ParamUtil.getStringValue(map, "bill_code", false, null);

		Double credit = null;
		if (Tools.isNullOrEmpty(map.get("credit")) == false)
		{
			credit = Double.parseDouble(map.get("credit").toString());
		}
		else
		{
			credit = 0d;
		}

		Double useful_credit = null;
		if (Tools.isNullOrEmpty(map.get("useful_credit")) == false)
		{
			useful_credit = Double.parseDouble(map.get("useful_credit").toString());
		}
		else
		{
			useful_credit = 0d;
		}

		Double main_balance = ParamUtil.getDoubleValue(map, "main_balance", false, null);
		if (Tools.isNullOrEmpty(map.get("main_balance")) == false)
		{
			main_balance = Double.parseDouble(map.get("main_balance").toString());
		}
		else
		{
			main_balance = 0d;
		}

		Double reward_balance = ParamUtil.getDoubleValue(map, "reward_balance", false, null);
		if (Tools.isNullOrEmpty(map.get("reward_balance")) == false)
		{
			reward_balance = Double.parseDouble(map.get("reward_balance").toString());
		}
		else
		{
			reward_balance = 0d;
		}

		Double total_main = ParamUtil.getDoubleValue(map, "total_main", false, null);
		if (Tools.isNullOrEmpty(map.get("total_main")) == false)
		{
			total_main = Double.parseDouble(map.get("total_main").toString());
		}
		else
		{
			total_main = 0d;
		}

		Double total_reward = ParamUtil.getDoubleValue(map, "total_reward", false, null);
		if (Tools.isNullOrEmpty(map.get("total_reward")) == false)
		{
			total_reward = Double.parseDouble(map.get("total_reward").toString());
		}
		else
		{
			total_reward = 0d;
		}

		Double main_trading = ParamUtil.getDoubleValue(map, "main_trading", false, null);
		if (Tools.isNullOrEmpty(map.get("main_trading")) == false)
		{
			main_trading = Double.parseDouble(map.get("main_trading").toString());
		}
		else
		{
			main_trading = 0d;
		}

		Double reward_trading = ParamUtil.getDoubleValue(map, "reward_trading", false, null);
		if (Tools.isNullOrEmpty(map.get("reward_trading")) == false)
		{
			reward_trading = Double.parseDouble(map.get("reward_trading").toString());
		}
		else
		{
			reward_trading = 0d;
		}

		Double income = ParamUtil.getDoubleValue(map, "income", false, null);
		if (Tools.isNullOrEmpty(map.get("income")) == false)
		{
			income = Double.parseDouble(map.get("income").toString());
		}
		else
		{
			income = 0d;
		}

		Double consume_cardmoney = ParamUtil.getDoubleValue(map, "consume_cardmoney", false, null);
		if (Tools.isNullOrEmpty(map.get("consume_cardmoney")) == false)
		{
			consume_cardmoney = Double.parseDouble(map.get("consume_cardmoney").toString());
		}
		else
		{
			consume_cardmoney = 0d;
		}

		Double deposit = ParamUtil.getDoubleValue(map, "deposit", false, null);
		if (Tools.isNullOrEmpty(map.get("deposit")) == false)
		{
			deposit = Double.parseDouble(map.get("deposit").toString());
		}
		else
		{
			deposit = 0d;
		}

		Double sales_price = ParamUtil.getDoubleValue(map, "sales_price", false, null);
		if (Tools.isNullOrEmpty(map.get("sales_price")) == false)
		{
			sales_price = Double.parseDouble(map.get("sales_price").toString());
		}
		else
		{
			sales_price = 0d;
		}

		String operator = ParamUtil.getStringValue(map, "operator", false, null);

		String updatetime = ParamUtil.getStringValue(map, "updatetime", false, null);

		String mode = ParamUtil.getStringValue(map, "mode", true, PosErrorCode.NOT_NULL_MODE);

		String date_remark = ParamUtil.getStringValue(map, "date_remark", false, null);

		String payment_url = ParamUtil.getStringValue(map, "payment_url", false, null);
		
		String payment_name = ParamUtil.getStringValue(map, "paymenttypename", false, null);
		
		String card_class_name = ParamUtil.getStringValue(map, "card_class_name", false, null);

		String name = ParamUtil.getStringValue(map, "name", false, null);
		
		String mobil = ParamUtil.getStringValue(map, "mobil", false, null);
		
		String operatedate = ParamUtil.getStringValue(map, "operatedate", false, null);
		
		String level_name = ParamUtil.getStringValue(map, "level_name", false, null);
		
		String original_card = ParamUtil.getStringValue(map, "card_code_original", false, null);

		String orderRemark  ="";
		if(map.containsKey("order_remark"))
		{
			orderRemark = ParamUtil.getStringValue(map, "order_remark", false, null);
		}
		
		String isInvoice = ParamUtil.getStringValue(map, "is_invoice", false, null);
		String strUrlPath = ParamUtil.getStringValue(map, "url_path", false, null);
		String strUrlContent = ParamUtil.getStringValue(map, "url_content", false, null);
		
		String qrcode = ParamUtil.getStringValue(map, "qrcode", false, null);
		String qrcodeUrl = ParamUtil.getStringValue(map, "qrcode_url", false, null);
		
		JSONObject printJo = JSONObject.fromObject(param.getData().get(0));
		try
		{
			// 补打结账单(不是开发票的场合) or 补打随餐单 打印电子发票二维码
			if ((SysDictionary.PRINT_CODE_1110.equals(printCode) && !"invoice".equals(printJo.optString("invoice_repeat"))) 
					|| (SysDictionary.PRINT_CODE_1201.equals(printCode) && "1".equals(printJo.optString("is_repeat_1201"))))
			{
				JSONObject invJo = posDao.getInvoiceInfo(tenantId, organId, printJo);
				// 打印过电子发票才可以补打带电子发票的结账单
				if (!Tools.isNullOrEmpty(invJo) && "2".equals(invJo.optString("invoice_type")))
				{
					Timestamp last_updatetime = DateUtil.parseTimestamp(invJo.optString("last_updatetime"));
					billNum = printJo.optString("bill_num");
					
					JSONObject billJo = posDao.getPosBillByBillnum(tenantId, organId, billNum);
					String orderNum = billJo.optString("order_num");
					int recoverCnt = billJo.optInt("recover_count");
					String fileName = "";
					if(Tools.isNullOrEmpty(orderNum))
					{
						fileName = billNum + "@" + recoverCnt;
					}
					else
					{
						fileName = orderNum + "@" + recoverCnt;
					}
					fileName += DateUtil.getYYYYMMDDHHMMSS(last_updatetime) + ".png";
					
					String webPathConst = com.tzx.framework.common.constant.Constant.getSystemMap().get("WEB_PATH");
					
					strUrlPath = webPathConst + "img/qr_code/invoice/" + fileName;
					isInvoice = "1";
					strUrlContent = "";
				}
			}
		}
		catch(Exception e)
		{
			logger.info("补打结账单时电子发票处理失败：" + e);
		}
		
		logger.debug(optNum + "" + reportDate + "" + shiftId + "账单打印个人的类型：" + printType);

		if (StringUtils.isNotEmpty(mode))
		{
			if (mode.equals("0"))
			{
				posDao.billPrint(mode, tenantId, organId, billNum, posNum, optNum, reportDate, shiftId, printCode, printType, date_remark, payment_url, isInvoice, strUrlPath, strUrlContent,orderRemark,qrcode,qrcodeUrl);
			}
			else if (mode.equals("1"))
			{
				posDao.customerPrint(mode, tenantId, cardCode, billCode, posNum, printCode, organId, credit, useful_credit, main_balance, reward_balance, total_main, total_reward, operator, updatetime, income, consume_cardmoney, deposit, sales_price, main_trading, reward_trading,payment_name,card_class_name,name,mobil,strUrlPath,strUrlContent,operatedate,level_name,original_card);
			}
		}
	}

	@Override
	public void customerPrint(String tenantId, Integer organId, JSONObject param)  throws SystemException
	{
		try
		{
			posDao.customerPrint(tenantId, organId, param.optString("pos_num"), param.optString("print_code"), param.optString("mode"), param, 2);
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
	}
	
	@Override
	public void printPosBill(String tenantId, Integer organId,JSONObject param)  throws SystemException
	{
		// TODO Auto-generated method stub
		List<JSONObject> paramList = new ArrayList<JSONObject>();
		paramList.add(param);
		
		Data paramData = Data.get(tenantId, organId, Constant.CODE_SUCCESS);
		paramData.setType(Type.PRINT_BILL);
		paramData.setOper(Oper.check);
		paramData.setData(paramList);
		
		Data result = new Data();
		
		this.printPosBill(paramData, result);
	}
	
	@Override
	public void printPosBillForPayment(JSONObject printJson, PosPrintNewService posPrintNewService) throws Exception
	{
		if (null != printJson)
		{
			String tenancyId = printJson.optString("tenancy_id");
			int storeId = printJson.optInt("store_id");
			String formatState = printJson.optString("format_state");
			String formatMode = printJson.optString("format_mode");
			String billNum = printJson.optString("bill_num");
			String isPrint = printJson.optString("isprint");

            if (posPrintNewService.isNONewPrint(tenancyId, storeId))
			{// 如果启用新的打印模式
                if ("2".equals(formatState) || "02".equals(formatMode))
				{ // 快餐结完账,送厨打
					if ((false == "1".equals(posDao.getSysParameter(tenancyId, storeId, "SFQDKVS"))) || "1".equals(posDao.getSysParameter(tenancyId, storeId, "IS_KVS_PRINT_DISH")))
					{
						posPrintNewService.posPrintByFunction(tenancyId, storeId, FunctionCode.ORDERING, printJson);
					}
                }

                if ("Y".equalsIgnoreCase(isPrint))
				{
                    this.getBindOptNum(printJson.optString("tenancy_id"), printJson.optInt("store_id"), printJson);
                    posPrintNewService.posPrintByFunction(tenancyId, storeId, FunctionCode.BILL_PAYMENT, printJson);
				}
			}
			else
			{
				if ("2".equals(formatState) || "02".equals(formatMode))
				{ // 快餐结完账,送厨打
					if ((false == "1".equals(posDao.getSysParameter(tenancyId, storeId, "SFQDKVS"))) || "1".equals(posDao.getSysParameter(tenancyId, storeId, "IS_KVS_PRINT_DISH")))
					{
						List<Integer> list = this.orderChef(tenancyId, billNum, storeId, "0");
						// this.orderPrint(tenancyId, billNum, storeId, list);
					}
				}

				if ("Y".equalsIgnoreCase(isPrint))
				{
					this.getBindOptNum(printJson.optString("tenancy_id"), printJson.optInt("store_id"), printJson);
					
					printJson.put("print_code", SysDictionary.PRINT_CODE_1102);
					printJson.put("print_type", "0");
					printJson.put("mode", "0");

					List<JSONObject> printList = new ArrayList<JSONObject>();
					printList.add(printJson);

					Data printData = new Data();
					printData.setTenancy_id(tenancyId);
					printData.setStore_id(storeId);
					printData.setType(Type.PRINT_BILL);
					printData.setData(printList);

					Data resultData = new Data();

					int printCount = printJson.optInt("print_count");
					for (int i = 0; i < printCount; i++)
					{
						this.printPosBill(printData, resultData);

					}
				}
			}
		}
	}
	
	public void getBindOptNum(String tenantId, Integer organId, JSONObject param)  throws Exception
	{
		if (Tools.isNullOrEmpty(param.optString("pos_num")))
		{
			throw new SystemException(PosErrorCode.NOT_NULL_POS_NUM);
		}
		
		posDao.getBindOptNum(tenantId, organId, param);
	}
	
}
