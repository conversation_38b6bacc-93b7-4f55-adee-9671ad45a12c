package com.tzx.clientorder.wlifeprogram.dao;

import net.sf.json.JSONObject;

import java.util.List;

/**
 * Created by qingui on 2018-06-27.
 */
public interface BasicDao extends WlifeCommonDao {
    String NAME = "com.tzx.clientorder.wlifeprogram.dao.BasicDaoImp";

    /**
     * 查询门店的菜品沽清信息
     * @param tenancyId
     * @param storeId
     * @return
     * @throws Exception
     */
    List<JSONObject> getSoldOutInfo(String tenancyId, int storeId) throws Exception;

    /**
     * 查询门店信息
     * @param tenancyId
     * @param storeId
     * @return
     * @throws Exception
     */
    List<JSONObject> getShops(String tenancyId, String storeId) throws Exception;

    /**
     * 查询桌台信息
     * @param tenancyId
     * @param storeId
     * @return
     * @throws Exception
     */
    List<JSONObject> getTables(String tenancyId, String storeId) throws Exception;

    /**
     * 查询菜品备注
     * @param tenancyId
     * @param storeId
     * @return
     * @throws Exception
     */
    List<JSONObject> getMemos(String tenancyId, String storeId) throws Exception;

    /**
     * 查询菜品类别
     * @param tenancyId
     * @param storeId
     * @param chanel
     * @return
     * @throws Exception
     */
    List<JSONObject> getDishKinds(String tenancyId, Integer storeId, String chanel) throws Exception;

    /**
     * 菜品信息
     * @param tenancyId
     * @param storeId
     * @return
     * @throws Exception
     */
    List<JSONObject> getItemInfos(String tenancyId, String storeId) throws Exception;
    
    /**
     * @param tenancyId
     * @param storeId
     * @param comboIds
     * @param itemIds
     * @return
     * @throws Exception
     */
	List<JSONObject> getItemInfosForCombo(String tenancyId, String storeId, int[] comboIds, int[] itemIds) throws Exception;

    /**
     * 查询菜品规格信息
     * @param tenancy_id
     * @param item_ids
     * @param price_system
     * @return
     * @throws Exception
     */
    List<JSONObject> getUnitInfos(String tenancy_id, int[] item_ids, String price_system) throws Exception;
    
    /** 查询菜品规格信息(关联时段特价方案)
     * @param tenancyId
     * @param itemIds
     * @param timePriceIds 特价方案id
     * @param priceSystem
     * @return
     * @throws Exception
     */
    List<JSONObject> getUnitInfos(String tenancyId, int[] itemIds, int[] timePriceIds,String priceSystem) throws Exception;

    /**
     * 查询菜品做法信息
     * @param tenancy_id
     * @param item_ids
     * @return
     * @throws Exception
     */
    List<JSONObject> getMethodInfos(String tenancy_id, int[] item_ids, String storeId) throws Exception;

    /**
     * 套餐
     * @param tenancy_id
     * @param combo_item_ids
     * @param price_system
     * @return
     * @throws Exception
     */
    List<JSONObject> getComboBaseInfo(String tenancy_id, int[] combo_item_ids, String price_system) throws Exception;
    
	/**
	 * @param tenancyId
	 * @param comboItemIds
	 * @param timePriceIds
	 * @param priceSystem
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> getComboBaseInfo(String tenancyId, int[] comboItemIds, int[] timePriceIds, String priceSystem) throws Exception;

    /**
     * 套餐-主菜
     * @param tenancy_id
     * @param maindishCombIds
     * @return
     * @throws Exception
     */
    List<JSONObject> getMaindishDetails(String tenancy_id, int[] maindishCombIds) throws Exception;

    /**
     * 套餐-辅菜
     * @param tenancy_id
     * @param mandatoryCombIds
     * @return
     * @throws Exception
     */
    List<JSONObject> getMandatoryBaseInfo(String tenancy_id, int[] mandatoryCombIds) throws Exception;

    /**
     * 套餐-辅菜-菜品组
     * @param tenancy_id
     * @param groupIds
     * @return
     * @throws Exception
     */
    List<JSONObject> getGroupDetails(String tenancy_id, int[] groupIds) throws Exception;
    
    /** 获取时段特价方案
     * @param tenancyId
     * @param storeId
     * @return
     * @throws Exception
     */
    List<JSONObject> getTimePrice(String tenancyId, int storeId) throws Exception;
    
    /**
     * @param tenancyId
     * @param storeId
     * @param tableCode
     * @return
     * @throws Exception
     */
	JSONObject getTableInfoByTableCode(String tenancyId, int storeId, String tableCode) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param propertyId
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> getTableItemForPropertyId(String tenancyId, int storeId, String propertyId) throws Exception;
	
	/**
	 * @param tenancy_id
	 * @param combo_item_ids
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> selectDetailsInfo(String tenancy_id, int[] combo_item_ids) throws Exception;

	/**查询单品备注
	 * @param tenancyId
	 * @param storeId
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> getDishMemos(String tenancyId, Integer storeId) throws Exception;
}
