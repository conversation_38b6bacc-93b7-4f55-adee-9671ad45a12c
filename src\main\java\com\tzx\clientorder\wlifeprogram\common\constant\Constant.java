package com.tzx.clientorder.wlifeprogram.common.constant;

import java.util.concurrent.ConcurrentHashMap;


/**
 * <AUTHOR>
 *
 */
public class Constant
{
	public static final String	WLIFE_REQUEST_URL_KEY		= "wlife.request.url";
	
//	public static final String	USER_WLIFE_ORDER_TYPE_KEY		= "USER_WLIFE_ORDER_TYPE";
//
//	public static final String	USER_WLIFE_ORDER_TYPE_H5		= "H5";
//	public static final String	USER_WLIFE_ORDER_TYPE_PROGRAM	= "PROGRAM";
//	//美味不用等小程序点餐
//	public static final String	USER_WLIFE_ORDER_TYPE_MEIWEI_PROGRAM	= "MEIWEI_PROGRAM";

//	public static final String	WLIFE_ORDER_SOURCE_H5			= SysDictionary.CHANEL_WSH16;
//	public static final String	WLIFE_ORDER_SOURCE_PROGRAM		= SysDictionary.CHANEL_WSP17;

//	public static final String  WLIFE_XCX_URL 					= "wlife_xcx_url";

	public static final String  MEMBERGID						= "membergid";

	public static final String  EMPLOYEE_IMG_URL 					= "employee_img_url";
	
	/**
	 * 点餐模式:后付
	 */
	public static final String ORDERMODE_AFTERPAY="3";
	/**
	 * 点餐模式:先付
	 */
	public static final String ORDERMODE_FIRSTPAY="2";

	// 保存获取的微生活会员等级列表
	public static ConcurrentHashMap<String, String[]> membergidMap = new ConcurrentHashMap<String, String[]>();
	
	/** 菜品备注同步方式 :全部同步 */
	public static final String							WLIFE_SYNC_MEMO_TYPE_ALL	= "1";
	/** 菜品备注同步方式 :按菜品同步 */
	public static final String							WLIFE_SYNC_MEMO_TYPE_DISH	= "2";
}
