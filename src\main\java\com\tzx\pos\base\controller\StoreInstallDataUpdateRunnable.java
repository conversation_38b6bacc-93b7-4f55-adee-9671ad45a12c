package com.tzx.pos.base.controller;

import java.util.Map;

import org.apache.log4j.Logger;

import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.pos.bo.PosRegisterService;

/**
 * 
 * 
 *
 */

public class StoreInstallDataUpdateRunnable implements Runnable {
	
	private static final Logger logger = Logger.getLogger(StoreInstallDataUpdateRunnable.class);
	
	@Override
	public void run() {
		// TODO Auto-generated method stub
		try{
			Map<String, String> systemMap = Constant.getSystemMap();
			
			if (!systemMap.containsKey("tenent_id") || !systemMap.containsKey("store_id") || "".equals(systemMap.get("tenent_id")) || "".equals(systemMap.get("store_id")))
			{
				logger.info("参数为空");
				return;
			}
			
			String tenentId = systemMap.get("tenent_id");
			DBContextHolder.setTenancyid(tenentId);
			
			PosRegisterService registerService = (PosRegisterService) SpringConext.getApplicationContext().getBean(PosRegisterService.NAME);
			registerService.initDataForinstallStore();
			logger.info("门店新安装业务数据更新成功！");
			
		}catch(Exception e){
			logger.error("门店新安装业务数据更新失败...", e);
		}
	}

}
