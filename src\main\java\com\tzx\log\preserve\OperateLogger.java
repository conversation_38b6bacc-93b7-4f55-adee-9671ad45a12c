package com.tzx.log.preserve;

import com.tzx.framework.common.entity.Data;
import com.tzx.pos.base.util.JsonUtil;
import net.sf.json.JSONObject;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR> on 2018-10-26
 */
@Component
@Aspect
public class OperateLogger {

    private static final Logger LOG = LoggerFactory.getLogger(OperateLogger.class);

  @Autowired
  private LogSave logSave;

  @Around("execution(* com.tzx.pos*.service.rest.Pos*.post(..))")
  public JSONObject aspect(ProceedingJoinPoint joinPoint) {


      JSONObject result = null;
      try {

          HttpServletRequest arg0 = (HttpServletRequest) joinPoint.getArgs()[0];
          JSONObject arg1 = (JSONObject) joinPoint.getArgs()[1];

          Data param = null;
          long startTime = 0, endTime = 0, responseTime = 0;
          Exception exception = null;
          try {
              param = JsonUtil.JsonToData(arg1, arg0.getHeader("User-Agent"));


              startTime = System.currentTimeMillis();

              result = (JSONObject) joinPoint.proceed();

              return result;

          } catch (Exception e) {
              exception = e;
          } finally {

              endTime = System.currentTimeMillis();

              responseTime = endTime - startTime;

              Entry entry = new Entry();
              entry.setParam(param);
              entry.setRequestUrl(arg0.getRequestURL().toString());
              entry.setRemoteAddr(arg0.getRemoteAddr());
              entry.setStartTime(startTime);
              entry.setEndTime(endTime);
              entry.setResponseTime(responseTime);
              entry.setResult(result);
              entry.setException(exception);

              logSave.save(entry);
          }

      } catch (Throwable throwable) {
//          throwable.printStackTrace();
          LOG.error("=====================操作日志保存失败!",throwable);
      }
      return result;
  }

  class Entry {

      private Data param;
      private String requestUrl;
      private String remoteAddr;
      private long startTime;
      private long endTime;
      private long responseTime;
      private JSONObject result;
      private Exception exception;

      public Data getParam() {
          return param;
      }

      public String getRequestUrl() {
          return requestUrl;
      }

      public void setRequestUrl(String requestUrl) {
          this.requestUrl = requestUrl;
      }

      public String getRemoteAddr() {
          return remoteAddr;
      }

      public void setRemoteAddr(String remoteAddr) {
          this.remoteAddr = remoteAddr;
      }

      public void setParam(Data param) {
          this.param = param;
      }

      public long getStartTime() {
          return startTime;
      }

      public void setStartTime(long startTime) {
          this.startTime = startTime;
      }

      public long getEndTime() {
          return endTime;
      }

      public void setEndTime(long endTime) {
          this.endTime = endTime;
      }

      public long getResponseTime() {
          return responseTime;
      }

      public void setResponseTime(long responseTime) {
          this.responseTime = responseTime;
      }

      public JSONObject getResult() {
          return result;
      }

      public void setResult(JSONObject result) {
          this.result = result;
      }

      public Exception getException() {
          return exception;
      }

      public void setException(Exception exception) {
          this.exception = exception;
      }

      @Override
      public String toString() {
          return "Entry{" +
                  "param=" + param +
                  ", startTime=" + startTime +
                  ", endTime=" + endTime +
                  ", responseTime=" + responseTime +
                  ", result=" + result +
                  ", exception=" + exception +
                  '}';
      }
  }
}
