package com.tzx.pos.bo.imp.calcamount;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;

import com.tzx.framework.common.util.DoubleHelper;
import com.tzx.framework.common.util.Tools;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.bo.TakePosCalcAmountService;
import com.tzx.pos.bo.dto.TakeOrderingParam;
import com.tzx.pos.po.springjdbc.dao.PosBaseDao;

@Service("com.tzx.pos.bo.imp.calcamount.TakePosCalcAmountServiceImp")
public class TakePosCalcAmountServiceImp implements TakePosCalcAmountService
{

	@Resource(name = PosBaseDao.NAME)
	private PosBaseDao			baseDao;

    @Override
	public void calcAmount(String tenantId, Integer storeId, String billNum,TakeOrderingParam param)throws Exception {
	    calcAmount(tenantId,storeId,billNum,param,null,null);
    }
	@Override
	public void calcAmount(String tenantId, Integer storeId, String billNum,TakeOrderingParam param,Integer dishScale,Integer billScale) throws Exception
	{
		if (Tools.isNullOrEmpty(billNum))
		{
			return;
		}

		if(null==dishScale){
            dishScale = Integer.parseInt(baseDao.getSysParameter(tenantId, storeId, "CMJEWS"));
        }
		if(null==billScale){
            billScale = Integer.parseInt(baseDao.getSysParameter(tenantId, storeId, "ZDJEWS"));
        }

		final int defaultScale = 4;

		/**
		 * 计算账单明细中菜目金额
		 */
		updateItemAmount(tenantId, storeId, billNum, dishScale,param);

		/**
		 * 计算账单菜目合计
		 */
//		String usubtotal = new String("update pos_bill set subtotal = (select sum(pbi.item_amount) from pos_bill_item pbi where pbi.bill_num = ? and pbi.store_id = ? and pbi.item_property <> 'MEALLIST') where bill_num = ? and store_id = ?");
//		baseDao.update(usubtotal, new Object[]
//		{ billNum, storeId, billNum, storeId });

		String billsql = new String("select guest,service_id,discount_mode_id,discount_rate,discount_case_id,discountr_amount,discountk_amount,source,service_amount,(select coalesce(sum(pbi.item_amount),0) from pos_bill_item pbi where pbi.tenancy_id = pb.tenancy_id and pbi.store_id = pb.store_id and pbi.bill_num = pb.bill_num and pbi.item_property <> ?) as subtotal from pos_bill pb where tenancy_id =? and store_id = ? and bill_num = ?");
		SqlRowSet rs = baseDao.query4SqlRowSet(billsql, new Object[]
		{SysDictionary.ITEM_PROPERTY_MEALLIST, tenantId, storeId,billNum });
		
		Double guest = null;
		Double subtotal = null;
		Double discountRate = null;
		Double discountrAmount = null;
		Double discountk_Amount = null;
		Double serviceAmount = null;
//		Integer serviceId = null;
		Integer discountModeId = null;
		Integer discountCaseId = null;
//		String channel = null;
		
		if (rs.next())
		{
			guest = rs.getDouble("guest");
			subtotal = rs.getDouble("subtotal");
			discountRate = rs.getDouble("discount_rate");
			discountrAmount = rs.getDouble("discountr_amount");
			discountk_Amount = rs.getDouble("discountk_amount");
			serviceAmount = rs.getDouble("service_amount");
//			serviceId = rs.getInt("service_id");
			discountModeId = rs.getInt("discount_mode_id");
			discountCaseId = rs.getInt("discount_case_id");
//			channel = rs.getString("source");
		}

		/**
		 * 判断菜目合计大于0,
		 */
		if (subtotal != null && subtotal == 0d)
		{
//			String updateB = new String(
//					"update pos_bill set service_id=null,service_amount=null,discountk_amount=null,discountr_amount=null,maling_amount=null,single_discount_amount=null,discount_amount=null,free_amount=null,givi_amount=null,more_coupon=null,average_amount=null,discount_case_id=null,discount_rate=null where bill_num = ? and store_id = ?");
//			baseDao.update(updateB, new Object[]
//			{ billNum, storeId });
			discountRate = null;
			discountCaseId = null;
			discountrAmount = null;
//			serviceId = null;
			serviceAmount = 0d;
		}

		/**
		 * 计算折扣金额 奉送菜品折扣金额为0
		 */
		if (discountRate == null || discountRate == 0d)
		{
			discountRate = 100d;
		}

		switch (discountModeId)
		{
			case SysDictionary.DISCOUNT_MODE_1:
				updateDiscountRateAmount(tenantId, storeId, billNum, discountRate, true, dishScale);
				break;

			case SysDictionary.DISCOUNT_MODE_2:
				// hq_discount_case_details中取该菜品+规格定位的那个菜品的折扣率(rate)和减免金额
				StringBuilder rateStr = new StringBuilder();
				rateStr.append(" select coalesce(cd.rate,0) as rate,coalesce(cd.derate,0) as derate,bi.id,bi.item_id,bi.setmeal_id,bi.item_serial,bi.setmeal_rwid,bi.item_property,bi.item_remark,bi.discount_state,bi.item_amount,bi.item_price,bi.assist_money,bi.method_money,bi.item_count,bi.discount_mode_id from pos_bill_item bi");
				rateStr.append(" left join hq_discount_case_details cd on bi.item_id=cd.item_id and bi.item_unit_name=cd.unit and cd.discount_case_id =?");
				rateStr.append(" where bi.bill_num=?  and bi.store_id=?");

				List<JSONObject> itemList = baseDao.query4Json(tenantId, rateStr.toString(), new Object[]
				{discountCaseId, billNum, storeId });

				List<Object[]> batchArgs = new ArrayList<Object[]>();
				for (JSONObject item : itemList)
				{
					if(SysDictionary.DISCOUNT_MODE_10 !=item.optInt("discount_mode_id"))
					{
						if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.optString("item_property")))
						{
							if ("Y".equalsIgnoreCase(item.optString("discount_state")))
							{
								if (item.optDouble("derate") > 0d)
								{
									batchArgs.addAll(getDiscountDerateAmount(item, itemList, dishScale));
								}
								else
								{
									Double rate=item.optDouble("rate");
									if(null == rate || rate.isNaN() || rate==0d)
									{
										rate = 100d;
									}
									batchArgs.addAll(getDiscountRateAmount(item, itemList, rate, dishScale));
								}
							}
							else
							{
								batchArgs.addAll(getDiscountRateAmount(item, itemList, 100d, dishScale));
							}
						}
					}
				}
				
				if (batchArgs.size() > 0)
				{
					baseDao.batchUpdate("update pos_bill_item set discount_rate=?,discount_amount=? where id = ?", batchArgs);
				}
				break;
			case SysDictionary.DISCOUNT_MODE_3:

				break;
	
			case SysDictionary.DISCOUNT_MODE_8:
				updateDiscountkVipPriceAmount(tenantId, storeId, billNum, dishScale);
				
				StringBuilder rateStr8 = new StringBuilder();
				rateStr8.append(" select coalesce(cd.rate,0) as rate,coalesce(cd.derate,0) as derate,bi.id,bi.item_id,bi.setmeal_id,bi.item_serial,bi.setmeal_rwid,bi.item_property,bi.item_remark,bi.discount_state,bi.item_amount,bi.item_price,bi.assist_money,bi.method_money,bi.item_count,bi.discount_amount,bi.discountr_amount,bi.discount_mode_id from pos_bill_item bi");
				rateStr8.append(" left join hq_discount_case_details cd on bi.item_id=cd.item_id and bi.item_unit_name=cd.unit and cd.discount_case_id =?");
//				rateStr.append(" left join hq_discount_case_org co on cd.discount_case_id=co.discount_case_id");
				rateStr8.append(" where bi.bill_num=? and bi.store_id=?");

				List<JSONObject> itemList8 = baseDao.query4Json(tenantId, rateStr8.toString(), new Object[]
				{ discountCaseId,billNum,storeId });

				List<Object[]> batchArgs8 = new ArrayList<Object[]>();
				for (JSONObject item : itemList8)
				{
					if(SysDictionary.DISCOUNT_MODE_10!=item.optInt("discount_mode_id"))
					{
						if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.optString("item_property")))
						{
							if ("Y".equalsIgnoreCase(item.optString("discount_state")))
							{
								if (item.optDouble("derate") > 0d)
								{
									batchArgs8.addAll(getDiscountDerateAmount2(item, itemList8, dishScale));
								}
								else
								{
									Double rate=item.optDouble("rate");
									if(null == rate || rate.isNaN() || rate==0d)
									{
										rate = 100d;
									}
									batchArgs8.addAll(getDiscountRateAmount2(item, itemList8, rate, dishScale));
								}
							}
							else
							{
								batchArgs8.addAll(getDiscountRateAmount2(item, itemList8, 100d, dishScale));
							}
						}
					}
				}
				if (batchArgs8.size() > 0)
				{
					baseDao.batchUpdate("update pos_bill_item set discount_rate=?,discount_amount=? where id = ?", batchArgs8);
				}
				break;
			case SysDictionary.DISCOUNT_MODE_9:
				updateDiscountRateAmount(tenantId, storeId, billNum, discountRate, false, dishScale);
				break;
			case SysDictionary.DISCOUNT_MODE_10:
				break;
			case SysDictionary.DISCOUNT_MODE_11:
				//updateDiscountRateAmount(tenantId, storeId, billNum, discountRate, false, dishScale);
				break;
			default:
				/**
				 * 取消折扣 更新每个pos_bill_item 账单折扣，快餐单品折扣另外处理如下
				 */
				StringBuilder querySql = new StringBuilder("select format_state from organ where tenancy_id=? and id=?");
				SqlRowSet rst = baseDao.query4SqlRowSet(querySql.toString(), new Object[]
						{ tenantId, storeId });
				if (rst.next())
				{
					if ("2".equals(rst.getString("format_state")))
					{
						break;
					}
				}
				StringBuffer sqlUpdate = new StringBuffer("update pos_bill set discount_mode_id=?,discount_case_id=?,discount_num=?,discount_rate = ?,discountk_amount = ? where bill_num = ? and store_id = ?");
				baseDao.update(sqlUpdate.toString(), new Object[]
				{ null, null, null, 100d, 0d, billNum, storeId });

				StringBuffer sqlUpdateItem = new StringBuffer("update pos_bill_item set discount_mode_id=?,discount_rate=?,discount_amount=? where bill_num = ? and store_id = ?");
				baseDao.update(sqlUpdateItem.toString(), new Object[]
				{ null, 100d, 0d, billNum, storeId });
				break;
		}
		
		StringBuilder qureyBillItem = new StringBuilder(
				"select id,item_id,setmeal_id,item_serial,setmeal_rwid,item_property,discount_state,item_remark,item_amount,item_price,assist_money,method_money,item_count,discount_mode_id,discount_rate from pos_bill_item where tenancy_id=? and store_id = ? and bill_num = ?");
		List<JSONObject> itemList = baseDao.query4Json(tenantId, qureyBillItem.toString(), new Object[]
		{ tenantId, storeId, billNum });

		List<Object[]> batchArgs = new ArrayList<Object[]>();
		for (JSONObject item : itemList)
		{
			if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.optString("item_property"))&&SysDictionary.DISCOUNT_MODE_10==item.optInt("discount_mode_id"))
			{
				double itemDiscountRate = item.optDouble("discount_rate");
				
				batchArgs.addAll(getDiscountRateAmount(item, itemList, itemDiscountRate, dishScale));
			}
		}
		if (batchArgs.size() > 0)
		{
			baseDao.batchUpdate("update pos_bill_item set discount_rate=?,discount_amount=? where id = ?", batchArgs);
		}
		
		/**
		 * 统计折扣金额
		 */
		double discountkAmount = 0d;
		StringBuilder countBillItemSql = new StringBuilder("select coalesce(sum(bi.discount_amount),0) as discount_amount from pos_bill_item bi where bi.item_property <> ? and bi.bill_num = ? and bi.store_id = ?");
		rs = baseDao.query4SqlRowSet(countBillItemSql.toString(), new Object[]
		{ SysDictionary.ITEM_PROPERTY_MEALLIST,billNum, storeId });
		if (rs.next())
		{
			discountkAmount = rs.getDouble("discount_amount");
		}
		
		/**
		 * 计算折让金额 奉送菜品折让金额为0 退菜菜品折让金额为0
		 */
		StringBuilder updateItemDiscountSql = new StringBuilder("update pos_bill_item set discountr_amount = 0 where bill_num = ? and store_id = ? ");
		baseDao.update(updateItemDiscountSql.toString(), new Object[]
		{ billNum, storeId });

		if (Tools.hv(discountrAmount) && discountrAmount > 0)
		{
			updateDiscountrAmount(tenantId, storeId, billNum, discountrAmount, dishScale);
		}
		else
		{
			discountrAmount = 0d;
		}

		/**
		 * 计算奉送金额
		 */
		double giviAmount = 0d;
		StringBuilder countGiveAmountSql = new StringBuilder("select coalesce(sum(bi.item_amount),0) as givi_amount from pos_bill_item bi where bi.item_remark = ? and bi.item_property <> ? and bi.bill_num = ? and bi.store_id = ?");
		rs = baseDao.query4SqlRowSet(countGiveAmountSql.toString(), new Object[]
		{ SysDictionary.ITEM_REMARK_FS02,SysDictionary.ITEM_PROPERTY_MEALLIST,billNum, storeId });
		if (rs.next())
		{
			giviAmount = rs.getDouble("givi_amount");
		}
		
		serviceAmount = 0d;
		
		double serviceItemAmount = 0d;
		double serviceTotal = 0d;
		
		serviceTotal = DoubleHelper.mul(serviceItemAmount, 1.0, defaultScale);
		serviceAmount = DoubleHelper.add(param.getServiceAmount(), serviceTotal, defaultScale);

		// 账单金额 = 项目小计金额 + 服务费金额
		double billAmount = DoubleHelper.add(subtotal, param.getServiceAmount(), defaultScale);

		// 优惠金额 = 折扣金额 + 折让金额
		double discountAmount = DoubleHelper.add(discountrAmount, discountkAmount, defaultScale);

		// 进位前付款金额 = sum(账单明细.实付金额)+服务费 = 账单金额-优惠金额 - 奉送金额
		double paymentAmount = DoubleHelper.sub(billAmount, DoubleHelper.add(discountAmount, giviAmount, defaultScale), defaultScale);

		if (paymentAmount < 0)
		{
			paymentAmount = 0d;
		}

		//判断折扣金额+折让金额小于等于账单应付金额,否则提示使用折扣失败
//		if(paymentAmount > discountAmount) {
//			throw new SystemException(PosErrorCode.USE_DISCOUNT_FAILURE);
//		}

		
		// 根据账单小数取舍方式计算进位后付款金额
		String billPointType = baseDao.getSysParameter(tenantId, storeId, "billpointtype");
		if(Tools.isNullOrEmpty(billPointType))
		{
			billPointType = "half_adjust";
		}

		double paymentAmount2 = 0d;
		switch (billPointType)
		{
			case "rounding":
				paymentAmount2 = DoubleHelper.roundDown(paymentAmount, billScale);
				break;
			case "one_adjust":
				paymentAmount2 = DoubleHelper.roundUp(paymentAmount, billScale);
				break;
			default:
				paymentAmount2 = DoubleHelper.round(paymentAmount, billScale);
				break;
		}

		// 抹零金额 = 进位后付款金额-进位前付款金额
		double malingAmount = DoubleHelper.sub(paymentAmount2, paymentAmount, dishScale);

		// 人均消费 = 付款金额/顾客数
		double averageAmount = DoubleHelper.div(paymentAmount2, guest, defaultScale);

		StringBuilder updateBillSql = new StringBuilder("update pos_bill set subtotal=?,service_amount=?,bill_amount=?,payment_amount=?,maling_amount=?,discountk_amount=?,discountr_amount=?,discount_amount=?,givi_amount=?,average_amount=? where bill_num = ? and store_id = ?");
		baseDao.update(updateBillSql.toString(), new Object[]
		{ subtotal,serviceAmount, billAmount, paymentAmount2, malingAmount, discountkAmount, discountrAmount, discountAmount, giviAmount, averageAmount, billNum, storeId });

		/**
		 * 计算抹零平摊金额 奉送菜品抹零平摊金额为0 退菜菜品抹零平摊金额为0
		 */
		StringBuilder updateItemScrapSql = new StringBuilder("update pos_bill_item set single_discount_amount = 0 where bill_num = ? and store_id = ? ");
		baseDao.update(updateItemScrapSql.toString(), new Object[]
		{ billNum, storeId });
		if (Tools.hv(malingAmount) && malingAmount != 0d)
		{
			updateScrapAmount(tenantId, storeId, billNum, malingAmount);
		}

		StringBuilder queryBillItemSql = new StringBuilder("select id,item_amount,discount_amount,single_discount_amount,discountr_amount,coalesce(item_remark_his,item_remark) as item_remark from pos_bill_item where bill_num = ? and store_id = ?");
		rs = baseDao.query4SqlRowSet(queryBillItemSql.toString(), new Object[]
		{ billNum, storeId });

		List<Object[]> batchItem = new ArrayList<Object[]>();
		while (rs.next())
		{
			// 实付金额=菜目金额+单品折扣-(折扣金额+折让金额)
			double realAmount = DoubleHelper.sub(DoubleHelper.add(rs.getDouble("item_amount"), rs.getDouble("single_discount_amount"), defaultScale), DoubleHelper.add(rs.getDouble("discount_amount"), rs.getDouble("discountr_amount"), defaultScale), dishScale);

			if (SysDictionary.ITEM_REMARK_FS02.equalsIgnoreCase(rs.getString("item_remark")))
			{
				realAmount = 0d;
			}
			batchItem.add(new Object[]
			{ DoubleHelper.round(realAmount, dishScale), rs.getInt("id") });
		}
		if (batchItem.size() > 0)
		{
			baseDao.batchUpdate("update pos_bill_item set real_amount=? where id = ?", batchItem);
		}

		// 修改差额
		StringBuilder queryBillAmountSql = new StringBuilder(
				"select b.payment_amount,coalesce(sum(p.currency_amount),0) as currency_amount,coalesce(sum(p.more_coupon),0) as more_coupon from pos_bill b left join pos_bill_payment p on b.store_id=p.store_id and b.bill_num=p.bill_num where b.tenancy_id=? and b.store_id=? and b.bill_num=? group by b.bill_num,b.payment_amount");
		rs = baseDao.query4SqlRowSet(queryBillAmountSql.toString(), new Object[]
		{ tenantId, storeId, billNum });
		double difference = 0d;
		if (rs.next())
		{
			difference = DoubleHelper.sub(rs.getDouble("payment_amount"), DoubleHelper.sub(rs.getDouble("currency_amount"), rs.getDouble("more_coupon"), 4), 4);
		}

		if (Tools.hv(difference))
		{
			String updateSql = new String("update pos_bill set difference=? where bill_num = ? and store_id = ? and tenancy_id = ?");
			baseDao.update(updateSql, new Object[]
			{ difference, billNum, storeId, tenantId });
		}
	
	}

	/**
	 * 修改账单明细菜目金额,及做法金额
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @param scale
	 * @throws Exception
	 */
	private void updateItemAmount(String tenantId, int storeId, String billNum, int scale,TakeOrderingParam param) throws Exception
	{
		final int defaultScale = 4;

		StringBuilder queryBillItemSql = new StringBuilder();
		queryBillItemSql
				.append(" select coalesce(zi.method_amount,0) as method_amount,coalesce(gd.makeup_money,0) as assist_amount,coalesce(bi.assist_num,0) as assist_num, bi.id,bi.item_id,bi.setmeal_id,bi.item_serial,bi.setmeal_rwid,bi.item_property,bi.item_remark,bi.item_price,bi.assist_money,bi.method_money,bi.item_count,bi.third_price from pos_bill_item bi");
		queryBillItemSql.append(" left join (select gd.*,cd.id as details_id from hq_item_group_details gd left join hq_item_combo_details cd on gd.item_group_id = cd.details_id and cd.is_itemgroup='Y') gd on bi.assist_item_id=gd.details_id  and gd.item_id=bi.item_id and gd.item_unit_id=bi.item_unit_id");
		queryBillItemSql.append(" left join (select bill_num,rwid,coalesce(sum(amount),0) as method_amount from pos_zfkw_item group by bill_num,rwid) zi on bi.bill_num = zi.bill_num and bi.rwid=zi.rwid");
		queryBillItemSql.append(" where bi.bill_num=? and bi.store_id = ?");
		// queryBillItemSql.append(" group by bi.id,bi.item_id,bi.setmeal_id,bi.item_serial,bi.setmeal_rwid,bi.item_property,bi.item_remark,bi.item_price,bi.assist_money,bi.method_money,bi.item_count,bi.third_price");

		List<JSONObject> itemList = baseDao.query4Json(tenantId, queryBillItemSql.toString(), new Object[]
		{ billNum, storeId });
		List<Object[]> batchArgs = new ArrayList<Object[]>();
		for (JSONObject item : itemList)
		{
			if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.optString("item_property")))
			{
				double methodMoney = DoubleHelper.mul(item.optDouble("method_amount"), item.optDouble("item_count"), scale);
				double assistMoney = DoubleHelper.mul(item.optDouble("assist_amount"), item.optDouble("item_count"), scale);
				if (SysDictionary.ITEM_PROPERTY_SETMEAL.equalsIgnoreCase(item.optString("item_property")))
				{
					methodMoney = 0d;
					assistMoney = 0d;
					for (JSONObject detail : itemList)
					{
						if (SysDictionary.ITEM_PROPERTY_MEALLIST.equals(detail.optString("item_property")) && item.optInt("item_id") == detail.optInt("setmeal_id") && item.optInt("item_serial") == detail.optInt("setmeal_rwid") && item.optString("item_remark").equals(detail.optString("item_remark")))
						{
							double detailMethodMoney = DoubleHelper.mul(detail.optDouble("method_amount"), detail.optDouble("item_count"), scale);
							methodMoney = DoubleHelper.add(methodMoney, detailMethodMoney, scale);

							double detailAssistMoney = DoubleHelper.mul(detail.optDouble("assist_amount"), detail.optDouble("item_count"), scale);
							assistMoney = DoubleHelper.add(assistMoney, detailAssistMoney, scale);

							double assistNum =1d;
							if(detail.containsKey("assist_num") && detail.optDouble("assist_num")>1)
							{
								assistNum  = detail.optDouble("assist_num");
							}
							
							double itemAmount = DoubleHelper.add(DoubleHelper.mul(detail.optDouble("item_price"), DoubleHelper.div(detail.optDouble("item_count"), assistNum, defaultScale), defaultScale), DoubleHelper.add(detailAssistMoney, detailMethodMoney, defaultScale), scale);
							
							batchArgs.add(new Object[]
							{ detailMethodMoney, detailAssistMoney, itemAmount, detail.optInt("id") });
						}
					}
				}
				
				double itemAmount = DoubleHelper.add(DoubleHelper.mul(item.optDouble("item_price"), item.optDouble("item_count"), defaultScale), DoubleHelper.add(assistMoney, methodMoney, defaultScale), scale);

				batchArgs.add(new Object[]
				{ methodMoney, assistMoney, itemAmount, item.optInt("id") });
			}
		}
		if (batchArgs.size() > 0)
		{
			if(param.getDiscountkAmount() != null){
				StringBuilder updateBillItemSql = new StringBuilder("update pos_bill_item set method_money=?,assist_money=?,item_amount=?,single_discount_amount=0 where id=?");
				baseDao.batchUpdate(updateBillItemSql.toString(), batchArgs);
			}else{
				StringBuilder updateBillItemSql = new StringBuilder("update pos_bill_item set method_money=?,assist_money=?,item_amount=?,discount_amount=0,discountr_amount=0,single_discount_amount=0 where id=?");
				baseDao.batchUpdate(updateBillItemSql.toString(), batchArgs);
			}
		
		}
	}
	
	/**
	 * 修改折扣率金额
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @param discountRate
	 * @param isNotState
	 *            不区分菜品是否允许折扣
	 * @throws Exception
	 */
	private void updateDiscountRateAmount(String tenantId, int storeId, String billNum, double discountRate, boolean isNotState, int scale) throws Exception
	{
		StringBuilder qureyBillItem = new StringBuilder("select id,item_id,setmeal_id,item_serial,setmeal_rwid,item_property,discount_state,item_remark,item_amount,item_price,assist_money,method_money,item_count,discount_mode_id from pos_bill_item where bill_num = ? and store_id = ?");
		List<JSONObject> itemList = baseDao.query4Json(tenantId, qureyBillItem.toString(), new Object[]
		{ billNum, storeId });

		List<Object[]> batchArgs = new ArrayList<Object[]>();
		for (JSONObject item : itemList)
		{
			if(SysDictionary.DISCOUNT_MODE_10 !=item.optInt("discount_mode_id"))
			{
				if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.optString("item_property")))
				{
					double itemDiscountRate = 100d;
					if (isNotState || "Y".equalsIgnoreCase(item.optString("discount_state")))
					{
						itemDiscountRate = discountRate;
					}
					batchArgs.addAll(getDiscountRateAmount(item, itemList, itemDiscountRate, scale));
				}
			}
		}
		if (batchArgs.size() > 0)
		{
			baseDao.batchUpdate("update pos_bill_item set discount_rate=?,discount_amount=? where id = ?", batchArgs);
		}
	}
	
	/**
	 * 计算折扣减免金额 奉送菜品折扣金额为0
	 * 
	 * @param item
	 * @param itemList
	 * @param scale
	 * @return
	 * @throws Exception
	 */
	private List<Object[]> getDiscountDerateAmount(JSONObject item, List<JSONObject> itemList, int scale) throws Exception
	{
		final int defaultScale = 4;
		List<Object[]> batchArgs = new ArrayList<Object[]>();
		if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.optString("item_property")))
		{
			double derate = item.optDouble("derate");
			if(derate>item.optDouble("item_price"))
			{
				derate = item.optDouble("item_price") ;
			}
			// 减免金额
			double itemDiscountAmount = DoubleHelper.mul(derate, item.optDouble("item_count"), scale);
			// 奉送菜品折扣金额为0
			if (SysDictionary.ITEM_REMARK_TC01.equalsIgnoreCase(item.optString("item_remark")) || SysDictionary.ITEM_REMARK_FS02.equalsIgnoreCase(item.optString("item_remark")) || SysDictionary.ITEM_REMARK_CJ05.equalsIgnoreCase(item.optString("item_remark")))
			{
				itemDiscountAmount = 0d;
			}
			batchArgs.add(new Object[]
			{ 100d, itemDiscountAmount, item.optInt("id") });

			if (SysDictionary.ITEM_PROPERTY_SETMEAL.equals(item.optString("item_property")))
			{ // 将减免金额平摊到套餐明细
				Integer maxId = null;
				double maxAmount = 0d;
				double sumItemdiscountAmount = 0d;
				double proportion = DoubleHelper.div(itemDiscountAmount, item.optDouble("item_amount"), defaultScale);

				List<JSONObject> detailList = new ArrayList<JSONObject>();
				for (JSONObject detail : itemList)
				{
					if (SysDictionary.ITEM_PROPERTY_MEALLIST.equals(detail.optString("item_property")) && item.optInt("item_id") == detail.optInt("setmeal_id") && item.optInt("item_serial") == detail.optInt("setmeal_rwid"))
					{
						double detailDiscountAmount = DoubleHelper.mul(detail.optDouble("item_amount"), proportion, scale);
						// 奉送菜品折扣金额为0
						if (SysDictionary.ITEM_REMARK_TC01.equalsIgnoreCase(detail.optString("item_remark")) || SysDictionary.ITEM_REMARK_FS02.equalsIgnoreCase(detail.optString("item_remark")) || SysDictionary.ITEM_REMARK_CJ05.equalsIgnoreCase(detail.optString("item_remark")))
						{
							detailDiscountAmount = 0d;
						}
						sumItemdiscountAmount = DoubleHelper.add(sumItemdiscountAmount, detailDiscountAmount, defaultScale);

						detail.put("discount_rate", 100);
						detail.put("discount_amount", detailDiscountAmount);
						detailList.add(detail);

						if (null == maxId || maxAmount < detail.optDouble("item_amount"))
						{
							maxId = detail.optInt("id");
							maxAmount = detail.optDouble("item_amount");
						}
					}
				}

				for (JSONObject detail : detailList)
				{
					double detailDiscountAmount = DoubleHelper.mul(detail.optDouble("item_amount"), proportion, scale);
					if (maxId == detail.optInt("id"))
					{
						detailDiscountAmount = DoubleHelper.add(detailDiscountAmount, DoubleHelper.sub(itemDiscountAmount, sumItemdiscountAmount, defaultScale), scale);
					}
					// 奉送菜品折扣金额为0
					if (SysDictionary.ITEM_REMARK_TC01.equalsIgnoreCase(detail.optString("item_remark")) || SysDictionary.ITEM_REMARK_FS02.equalsIgnoreCase(detail.optString("item_remark")) || SysDictionary.ITEM_REMARK_CJ05.equalsIgnoreCase(detail.optString("item_remark")))
					{
						detailDiscountAmount = 0d;
					}
					batchArgs.add(new Object[]
					{ 100d, detailDiscountAmount, detail.optInt("id") });
				}
			}
		}
		return batchArgs;
	}
	
	
	/**
	 * 计算折扣率金额 奉送菜品折扣金额为0
	 * 
	 * @param item
	 * @param itemList
	 * @param itemDiscountRate
	 * @param scale
	 * @return
	 * @throws Exception
	 */
	private List<Object[]> getDiscountRateAmount(JSONObject item, List<JSONObject> itemList, double itemDiscountRate, int scale) throws Exception
	{
		final int defaultScale = 4;
		List<Object[]> batchArgs = new ArrayList<Object[]>();
		if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.optString("item_property")))
		{
			if (SysDictionary.ITEM_REMARK_TC01.equals(item.optString("item_remark")) || SysDictionary.ITEM_REMARK_FS02.equals(item.optString("item_remark")) || SysDictionary.ITEM_REMARK_CJ05.equals(item.optString("item_remark")))
			{
				itemDiscountRate = 100d;
			}

			double rate = DoubleHelper.sub(1d, DoubleHelper.div(itemDiscountRate, 100d, defaultScale), defaultScale);
			double itemAmount = item.optDouble("item_amount");
			if (itemAmount == 0d)
			{
				itemAmount = DoubleHelper.add(DoubleHelper.mul(item.optDouble("item_price"), item.optDouble("item_count"), defaultScale), DoubleHelper.add(item.optDouble("method_money"), item.optDouble("assist_money"), defaultScale), defaultScale);
			}
			batchArgs.add(new Object[]
			{ itemDiscountRate, DoubleHelper.mul(itemAmount, rate, scale), item.optInt("id") });

			if (SysDictionary.ITEM_PROPERTY_SETMEAL.equalsIgnoreCase(item.optString("item_property")))
			{
				// 套餐明细中，明细金额最大的折扣金额=套主项折扣金额-其它套餐明细折扣金额，否则会产生误差
				Integer dMaxId = null;
				double dItemMaxAmount = 0; // 循环查询明细中金额最大的
				double detailSumAmount = 0; // 套餐明细折扣金额之和
				
				List<JSONObject> detailList = new ArrayList<JSONObject>();
				for (JSONObject detail : itemList)
				{
					if (SysDictionary.ITEM_PROPERTY_MEALLIST.equals(detail.optString("item_property")) && item.optInt("item_id") == detail.optInt("setmeal_id") && item.optInt("item_serial") == detail.optInt("setmeal_rwid"))
					{
						double detailItemAmount = detail.optDouble("item_amount");
						if (detailItemAmount == 0d)
						{
							detailItemAmount = DoubleHelper.add(DoubleHelper.mul(detail.optDouble("item_price"), detail.optDouble("item_count"), defaultScale), DoubleHelper.add(detail.optDouble("method_money"), detail.optDouble("assist_money"), defaultScale), defaultScale);
						}
						// 套餐明细折扣金额
						double discountAmount = DoubleHelper.mul(detailItemAmount, rate, scale); 
						// 套餐明细的折扣金额之和
						detailSumAmount = DoubleHelper.add(detailSumAmount, discountAmount, scale);
						// 套餐中明细金额最大的
						if(dMaxId == null || detailItemAmount > dItemMaxAmount ){
							dMaxId = detail.optInt("id");
							dItemMaxAmount = detailItemAmount;
						}
						
						JSONObject itemA = new JSONObject();
						itemA.put("id", detail.optInt("id"));
						itemA.put("discount_amount", discountAmount);
						detailList.add(itemA);
					}
				}
				
				for(JSONObject detail: detailList){
					if(detail.optInt("id") == dMaxId){
						// 套餐主项折扣-套餐明细项之和=折扣的误差
						double detailItemAmount = DoubleHelper.add(detail.optDouble("discount_amount"), DoubleHelper.sub(DoubleHelper.mul(itemAmount, rate, scale), detailSumAmount, scale), scale);
						
						batchArgs.add(new Object[]
								{ itemDiscountRate, detailItemAmount, detail.optInt("id") });
					}else{
						batchArgs.add(new Object[]
								{ itemDiscountRate, detail.optDouble("discount_amount"), detail.optInt("id") });
					}
				}
				
			}
		}
		return batchArgs;
	}

	/**
	 * 计算会员价,把会员价的差价放到优惠金额里
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @throws Exception
	 */
	private void updateDiscountkVipPriceAmount(String tenantId, int storeId, String billNum, int scale) throws Exception
	{
		final int defaultScale = 4;

		StringBuilder queryBillItemSql = new StringBuilder();
		queryBillItemSql.append(" select bi.id,bi.item_id,bi.setmeal_id,bi.item_serial,bi.setmeal_rwid,bi.item_property,bi.item_remark,bi.item_price,bi.assist_money,bi.method_money,bi.item_count,bi.item_amount,coalesce(bi.third_price,0) as third_price,bi.discount_mode_id");
		queryBillItemSql.append(" from pos_bill_item bi where bi.bill_num=? and bi.store_id = ?");

		List<JSONObject> itemList = baseDao.query4Json(tenantId, queryBillItemSql.toString(), new Object[]
		{ billNum, storeId });
		List<Object[]> batchArgs = new ArrayList<Object[]>();
		for (JSONObject item : itemList)
		{
			if(SysDictionary.DISCOUNT_MODE_10!=item.optInt("discount_mode_id"))
			{
				if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.optString("item_property")))
				{
					double discountAmount = 0;
					if (item.optDouble("third_price") > 0 && item.optDouble("item_price") > item.optDouble("third_price"))
					{
						discountAmount = DoubleHelper.mul(DoubleHelper.sub(item.optDouble("item_price"), item.optDouble("third_price"), defaultScale), item.optDouble("item_count"), scale);
					}
	
					if (SysDictionary.ITEM_REMARK_TC01.equalsIgnoreCase(item.optString("item_remark")) || SysDictionary.ITEM_REMARK_FS02.equalsIgnoreCase(item.optString("item_remark")) || SysDictionary.ITEM_REMARK_CJ05.equalsIgnoreCase(item.optString("item_remark")))
					{
						discountAmount = 0d;
					}
					batchArgs.add(new Object[]
					{ 100d, discountAmount, item.optInt("id") });
	
					if (discountAmount > 0)
					{
						if (SysDictionary.ITEM_PROPERTY_SETMEAL.equals(item.optString("item_property")))
						{ // 将减免金额平摊到套餐明细
							Integer dMaxId = null;
							double dmaxAmount = 0d;
							double sumAmount = 0d;
	
							List<JSONObject> detailList = new ArrayList<JSONObject>();
							for (JSONObject detail : itemList)
							{
								if (SysDictionary.ITEM_PROPERTY_MEALLIST.equals(detail.optString("item_property")) && item.optInt("item_id") == detail.optInt("setmeal_id") && item.optInt("item_serial") == detail.optInt("setmeal_rwid"))
								{
									detailList.add(detail);
									double detailDiscountAmount = DoubleHelper.mul(DoubleHelper.div(detail.optDouble("item_amount"), item.optDouble("item_amount"), defaultScale), discountAmount, scale);
									sumAmount = DoubleHelper.add(sumAmount, detailDiscountAmount, defaultScale);
	
									if (null == dMaxId || dmaxAmount < detail.optDouble("item_amount"))
									{
										dMaxId = detail.optInt("id");
										dmaxAmount = detail.optDouble("item_amount");
									}
								}
							}
	
							for (JSONObject detail : detailList)
							{
								double detailDiscountAmount = DoubleHelper.mul(DoubleHelper.div(detail.optDouble("item_amount"), item.optDouble("item_amount"), defaultScale), discountAmount, scale);
								if (dMaxId == detail.optInt("id"))
								{
									detailDiscountAmount = DoubleHelper.add(detailDiscountAmount, DoubleHelper.sub(discountAmount, sumAmount, defaultScale), scale);
								}
								batchArgs.add(new Object[]
								{ 100d, detailDiscountAmount, detail.optInt("id") });
							}
						}
					}
				}
			}
		}
		if (batchArgs.size() > 0)
		{
			StringBuilder updateBillItemSql = new StringBuilder("update pos_bill_item set discount_rate=?,discount_amount=? where id = ?");
			baseDao.batchUpdate(updateBillItemSql.toString(), batchArgs);
		}
	}
	
	/** 第三方优惠平摊
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @param discountrAmount
	 * @param scale
	 * @throws Exception
	 */
	private void updateDiscountkAmount(String tenantId, int storeId, String billNum, double discountrAmount, int scale) throws Exception
	{
		final int defaultScale = 4;

		StringBuilder queryItemSql = new StringBuilder(
				"select id,item_id,setmeal_id,item_serial,setmeal_rwid,item_property,item_amount,item_count,return_count from pos_bill_item where coalesce(item_remark,'')<>'TC01' and coalesce(item_remark,'')<>'FS02' and coalesce(item_remark,'')<>'CJ05' and bill_num = ? and store_id = ?");
		List<JSONObject> itemList = baseDao.query4Json(tenantId, queryItemSql.toString(), new Object[]
		{ billNum, storeId });

		double averageTotal = 0d;
		double subtotal = 0d; // 统计所有菜的折让金额
		Integer maxId = 0;
		double maxValue = 0d;

		// 取总钱数,最大面额和对应的rwid
		for (JSONObject item : itemList)
		{
			double itemAmount = item.optDouble("item_amount");
			if (Tools.hv(item.opt("return_count")) && item.optDouble("return_count") > 0d)
			{
				double itemPrice = DoubleHelper.div(item.optDouble("item_amount"), item.optDouble("item_count"), defaultScale);
				itemAmount = DoubleHelper.mul(itemPrice, DoubleHelper.sub(item.optDouble("item_count"), item.optDouble("return_count"), defaultScale), scale);
				item.put("item_amount", itemAmount);
			}

			if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.optString("item_property")))
			{
				subtotal = DoubleHelper.add(subtotal, itemAmount, defaultScale);

				if (maxValue < itemAmount)
				{
					maxValue = itemAmount;
					maxId = item.optInt("id");
				}
			}
		}

		// 计算并平摊折让的钱
		for (JSONObject item : itemList)
		{
			if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.optString("item_property")))
			{
				double itemDiscountrAmount = DoubleHelper.mul(DoubleHelper.div(item.optDouble("item_amount"), subtotal, defaultScale), discountrAmount, scale);
				averageTotal = DoubleHelper.add(averageTotal, itemDiscountrAmount, defaultScale);
			}
		}

		List<Object[]> batchArgs = new ArrayList<Object[]>();
		for (JSONObject item : itemList)
		{
			if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.optString("item_property")))
			{
				double itemDiscountrAmount = DoubleHelper.mul(DoubleHelper.div(item.optDouble("item_amount"), subtotal, defaultScale), discountrAmount, scale);
				if (maxId == item.optInt("id"))
				{
					itemDiscountrAmount = DoubleHelper.add(itemDiscountrAmount, DoubleHelper.sub(discountrAmount, averageTotal, defaultScale), scale);
				}
				batchArgs.add(new Object[]
				{ itemDiscountrAmount, item.optInt("id") });

				if (SysDictionary.ITEM_PROPERTY_SETMEAL.equals(item.optString("item_property")))
				{ // 将减免金额平摊到套餐明细
					Integer dMaxId = null;
					double dmaxAmount = 0d;
					double sumAmount = 0d;

					List<JSONObject> detailList = new ArrayList<JSONObject>();
					for (JSONObject detail : itemList)
					{
						if (SysDictionary.ITEM_PROPERTY_MEALLIST.equals(detail.optString("item_property")) && item.optInt("item_id") == detail.optInt("setmeal_id") && item.optInt("item_serial") == detail.optInt("setmeal_rwid"))
						{
							detailList.add(detail);
							double detailDiscountrAmount = DoubleHelper.mul(DoubleHelper.div(detail.optDouble("item_amount"), item.optDouble("item_amount"), defaultScale), itemDiscountrAmount, scale);
							sumAmount = DoubleHelper.add(sumAmount, detailDiscountrAmount, defaultScale);

							if (null == dMaxId || dmaxAmount < detail.optDouble("item_amount"))
							{
								dMaxId = detail.optInt("id");
								dmaxAmount = detail.optDouble("item_amount");
							}
						}
					}

					for (JSONObject detail : detailList)
					{
						double detailDiscountrAmount = DoubleHelper.mul(DoubleHelper.div(detail.optDouble("item_amount"), item.optDouble("item_amount"), defaultScale), itemDiscountrAmount, scale);
						if (dMaxId == detail.optInt("id"))
						{
							detailDiscountrAmount = DoubleHelper.add(detailDiscountrAmount, DoubleHelper.sub(itemDiscountrAmount, sumAmount, defaultScale), scale);
						}
						batchArgs.add(new Object[]
						{ detailDiscountrAmount, detail.optInt("id") });
					}
				}
			}
		}
		// 更新折扣金额
		if (batchArgs.size() > 0)
		{
			baseDao.batchUpdate("update pos_bill_item set discount_amount = ? where id = ?", batchArgs);
		}
	}
	
	/**
	 * 计算折扣减免金额 奉送菜品折扣金额为0
	 * 
	 * @param item
	 * @param itemList
	 * @param scale
	 * @return
	 * @throws Exception
	 */
	private List<Object[]> getDiscountDerateAmount2(JSONObject item, List<JSONObject> itemList, int scale) throws Exception
	{
		final int defaultScale = 4;
		List<Object[]> batchArgs = new ArrayList<Object[]>();
		if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.optString("item_property")))
		{
			// 减免金额
			double itemDiscountAmount = DoubleHelper.mul(item.optDouble("derate"), item.optDouble("item_count"), scale);
			// 奉送菜品折扣金额为0
			if (SysDictionary.ITEM_REMARK_TC01.equalsIgnoreCase(item.optString("item_remark")) || SysDictionary.ITEM_REMARK_FS02.equalsIgnoreCase(item.optString("item_remark")) || SysDictionary.ITEM_REMARK_CJ05.equalsIgnoreCase(item.optString("item_remark")))
			{
				itemDiscountAmount = 0d;
			}
			
			double discountAmount = item.optDouble("discount_amount");
			batchArgs.add(new Object[]
			{ 100d, DoubleHelper.add(discountAmount, itemDiscountAmount, scale) , item.optInt("id") });

			if (SysDictionary.ITEM_PROPERTY_SETMEAL.equals(item.optString("item_property")))
			{ // 将减免金额平摊到套餐明细
				Integer maxId = null;
				double maxAmount = 0d;
				double sumItemdiscountAmount = 0d;
				double proportion = DoubleHelper.div(itemDiscountAmount, item.optDouble("item_amount"), defaultScale);

				List<JSONObject> detailList = new ArrayList<JSONObject>();
				for (JSONObject detail : itemList)
				{
					if (SysDictionary.ITEM_PROPERTY_MEALLIST.equals(detail.optString("item_property")) && item.optInt("item_id") == detail.optInt("setmeal_id") && item.optInt("item_serial") == detail.optInt("setmeal_rwid"))
					{
						double detailDiscountAmount = DoubleHelper.mul(detail.optDouble("item_amount"), proportion, scale);
						// 奉送菜品折扣金额为0
						if (SysDictionary.ITEM_REMARK_TC01.equalsIgnoreCase(detail.optString("item_remark")) || SysDictionary.ITEM_REMARK_FS02.equalsIgnoreCase(detail.optString("item_remark")) || SysDictionary.ITEM_REMARK_CJ05.equalsIgnoreCase(detail.optString("item_remark")))
						{
							detailDiscountAmount = 0d;
						}
						sumItemdiscountAmount = DoubleHelper.add(sumItemdiscountAmount, detailDiscountAmount, defaultScale);

						detail.put("discount_rate", 100);
						detail.put("discount_amount", detailDiscountAmount);
						detailList.add(detail);

						if (null == maxId || maxAmount < detail.optDouble("item_amount"))
						{
							maxId = detail.optInt("id");
							maxAmount = detail.optDouble("item_amount");
						}
					}
				}

				for (JSONObject detail : detailList)
				{
					double detailDiscountAmount = DoubleHelper.mul(detail.optDouble("item_amount"), proportion, scale);
					if (maxId == detail.optInt("id"))
					{
						detailDiscountAmount = DoubleHelper.add(detailDiscountAmount, DoubleHelper.sub(itemDiscountAmount, sumItemdiscountAmount, defaultScale), scale);
					}
					// 奉送菜品折扣金额为0
					if (SysDictionary.ITEM_REMARK_TC01.equalsIgnoreCase(detail.optString("item_remark")) || SysDictionary.ITEM_REMARK_FS02.equalsIgnoreCase(detail.optString("item_remark")) || SysDictionary.ITEM_REMARK_CJ05.equalsIgnoreCase(detail.optString("item_remark")))
					{
						detailDiscountAmount = 0d;
					}
					
					batchArgs.add(new Object[]
					{ 100d, DoubleHelper.add(detail.optDouble("discount_amount"), detailDiscountAmount, scale), detail.optInt("id") });
				}
			}
		}
		return batchArgs;
	}
	
	
	/**
	 * 计算折扣率金额 奉送菜品折扣金额为0
	 * 
	 * @param item
	 * @param itemList
	 * @param itemDiscountRate
	 * @param scale
	 * @return
	 * @throws Exception
	 */
	private List<Object[]> getDiscountRateAmount2(JSONObject item, List<JSONObject> itemList, double itemDiscountRate, int scale) throws Exception
	{
		final int defaultScale = 4;
		List<Object[]> batchArgs = new ArrayList<Object[]>();
		if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.optString("item_property")))
		{
			if (SysDictionary.ITEM_REMARK_TC01.equals(item.optString("item_remark")) || SysDictionary.ITEM_REMARK_FS02.equals(item.optString("item_remark")) || SysDictionary.ITEM_REMARK_CJ05.equals(item.optString("item_remark")))
			{
				itemDiscountRate = 100d;
			}

			double rate = DoubleHelper.sub(1d, DoubleHelper.div(itemDiscountRate, 100d, defaultScale), defaultScale);
			double itemAmount = item.optDouble("item_amount");
			double discountAmount = item.optDouble("discount_amount");
			double discountrAmount = item.optDouble("discountr_amount");
			
			itemAmount = DoubleHelper.sub(itemAmount, DoubleHelper.add(discountAmount, discountrAmount, scale), scale);
			
			batchArgs.add(new Object[]
			{ itemDiscountRate, DoubleHelper.add(discountAmount, DoubleHelper.mul(itemAmount, rate, scale), scale), item.optInt("id") });

			if (SysDictionary.ITEM_PROPERTY_SETMEAL.equalsIgnoreCase(item.optString("item_property")))
			{
				for (JSONObject detail : itemList)
				{
					if (SysDictionary.ITEM_PROPERTY_MEALLIST.equals(detail.optString("item_property")) && item.optInt("item_id") == detail.optInt("setmeal_id") && item.optInt("item_serial") == detail.optInt("setmeal_rwid"))
					{
						double detailItemAmount = detail.optDouble("item_amount");
						double detailDiscountAmount = detail.optDouble("discount_amount");
						double detailDiscountrAmount = detail.optDouble("discountr_amount");
						
						detailItemAmount = DoubleHelper.sub(detailItemAmount, DoubleHelper.add(detailDiscountAmount, detailDiscountrAmount, scale), scale);
						
						batchArgs.add(new Object[]
						{ itemDiscountRate, DoubleHelper.add(detailDiscountAmount, DoubleHelper.mul(detailItemAmount, rate, scale), scale) , detail.optInt("id") });
					}
				}
			}
		}
		return batchArgs;
	}
	
	/**
	 * 获得折让金额discountr_amount然后分摊到各个明细里 奉送菜品折让金额为0 退菜菜品折让金额为0
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @param discountrAmount
	 * @param scale
	 * @throws Exception
	 */
	private void updateDiscountrAmount(String tenantId, int storeId, String billNum, double discountrAmount, int scale) throws Exception
	{
		final int defaultScale = 4;

		StringBuilder queryItemSql = new StringBuilder(
				"select id,item_id,setmeal_id,item_serial,setmeal_rwid,item_property,item_amount,item_count,return_count,coalesce(discount_amount,0) as discount_amount from pos_bill_item where coalesce(item_remark,'')<>'TC01' and coalesce(item_remark,'')<>'FS02' and coalesce(item_remark,'')<>'CJ05' and bill_num = ? and store_id = ?");
		List<JSONObject> itemList = baseDao.query4Json(tenantId, queryItemSql.toString(), new Object[]
		{ billNum, storeId });

		double averageTotal = 0d;
		double subtotal = 0d; // 统计所有菜的折让金额
		Integer maxId = 0;
		double maxValue = 0d;

		// 取总钱数,最大面额和对应的rwid
		for (JSONObject item : itemList)
		{
			double itemAmount = item.optDouble("item_amount");
			if (Tools.hv(item.opt("return_count")) && item.optDouble("return_count") > 0d)
			{
				double itemPrice = DoubleHelper.div(item.optDouble("item_amount"), item.optDouble("item_count"), defaultScale);
				itemAmount = DoubleHelper.mul(itemPrice, DoubleHelper.sub(item.optDouble("item_count"), item.optDouble("return_count"), defaultScale), scale);
				item.put("item_amount", itemAmount);
			}

			if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.optString("item_property")))
			{
				subtotal = DoubleHelper.add(subtotal, DoubleHelper.sub(itemAmount, item.optDouble("discount_amount"), defaultScale), defaultScale);

				if (maxValue < itemAmount)
				{
					maxValue = itemAmount;
					maxId = item.optInt("id");
				}
			}
		}

		// 计算并平摊折让的钱
		for (JSONObject item : itemList)
		{
			if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.optString("item_property")))
			{
				double realAmoount = DoubleHelper.sub(item.optDouble("item_amount"), item.optDouble("discount_amount"), defaultScale);
				double itemDiscountrAmount = DoubleHelper.mul(DoubleHelper.div(realAmoount, subtotal, defaultScale), discountrAmount, scale);
				averageTotal = DoubleHelper.add(averageTotal, itemDiscountrAmount, defaultScale);
			}
		}

		List<Object[]> batchArgs = new ArrayList<Object[]>();
		for (JSONObject item : itemList)
		{
			if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.optString("item_property")))
			{
				double realAmoount = DoubleHelper.sub(item.optDouble("item_amount"), item.optDouble("discount_amount"), defaultScale);
				double itemDiscountrAmount = DoubleHelper.mul(DoubleHelper.div(realAmoount, subtotal, defaultScale), discountrAmount, scale);
				if (maxId == item.optInt("id"))
				{
					itemDiscountrAmount = DoubleHelper.add(itemDiscountrAmount, DoubleHelper.sub(discountrAmount, averageTotal, defaultScale), scale);
				}
				batchArgs.add(new Object[]
				{ itemDiscountrAmount, item.optInt("id") });

				if (SysDictionary.ITEM_PROPERTY_SETMEAL.equals(item.optString("item_property")))
				{ // 将减免金额平摊到套餐明细
					Integer dMaxId = null;
					double dmaxAmount = 0d;
					double sumAmount = 0d;

					List<JSONObject> detailList = new ArrayList<JSONObject>();
					for (JSONObject detail : itemList)
					{
						if (SysDictionary.ITEM_PROPERTY_MEALLIST.equals(detail.optString("item_property")) && item.optInt("item_id") == detail.optInt("setmeal_id") && item.optInt("item_serial") == detail.optInt("setmeal_rwid"))
						{
							detailList.add(detail);
							double detailDiscountrAmount = DoubleHelper.mul(DoubleHelper.div(detail.optDouble("item_amount"), item.optDouble("item_amount"), defaultScale), itemDiscountrAmount, scale);
							sumAmount = DoubleHelper.add(sumAmount, detailDiscountrAmount, defaultScale);

							if (null == dMaxId || dmaxAmount < detail.optDouble("item_amount"))
							{
								dMaxId = detail.optInt("id");
								dmaxAmount = detail.optDouble("item_amount");
							}
						}
					}

					for (JSONObject detail : detailList)
					{
						double detailDiscountrAmount = DoubleHelper.mul(DoubleHelper.div(detail.optDouble("item_amount"), item.optDouble("item_amount"), defaultScale), itemDiscountrAmount, scale);
						if (dMaxId == detail.optInt("id"))
						{
							detailDiscountrAmount = DoubleHelper.add(detailDiscountrAmount, DoubleHelper.sub(itemDiscountrAmount, sumAmount, defaultScale), scale);
						}
						batchArgs.add(new Object[]
						{ detailDiscountrAmount, detail.optInt("id") });
					}
				}
			}
		}
		// 更新折让金额
		if (batchArgs.size() > 0)
		{
			baseDao.batchUpdate("update pos_bill_item set discountr_amount = ? where id = ?", batchArgs);
		}
	}

	/**
	 * 修改抹零金额,放到菜目金额最大的菜品上 奉送菜品抹零平摊金额为0 退菜菜品抹零平摊金额为0
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @param scrapAmount
	 * @throws Exception
	 */
	private void updateScrapAmount(String tenantId, int storeId, String billNum, double scrapAmount) throws Exception
	{
		StringBuilder queryItemSql = new StringBuilder(
				"select id,item_id,setmeal_id,item_serial,setmeal_rwid,item_property,item_amount from pos_bill_item where coalesce(item_remark,'')<>'TC01' and coalesce(item_remark,'')<>'FS02' and coalesce(item_remark,'')<>'CJ05' and bill_num = ? and store_id = ?");
		List<JSONObject> itemList = baseDao.query4Json(tenantId, queryItemSql.toString(), new Object[]
		{ billNum, storeId });

		if (itemList != null && itemList.size() > 0)
		{
			JSONObject maxJson = null;
			for (JSONObject item : itemList)
			{
				if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.optString("item_property")))
				{
					if (null == maxJson || maxJson.optDouble("item_amount") < item.optDouble("item_amount"))
					{
						maxJson = item;
					}
				}
			}
			StringBuilder updateItemSql = new StringBuilder("update pos_bill_item set single_discount_amount=? where id = ?");
			if (Tools.hv(maxJson))
			{
				baseDao.update(updateItemSql.toString(), new Object[]
				{ scrapAmount, maxJson.optInt("id") });
			}

			if (SysDictionary.ITEM_PROPERTY_SETMEAL.equalsIgnoreCase(maxJson.optString("item_property")))
			{
				JSONObject detailMaxJson = null;
				for (JSONObject detail : itemList)
				{
					if (SysDictionary.ITEM_PROPERTY_MEALLIST.equals(detail.optString("item_property")) && maxJson.optInt("item_id") == detail.optInt("setmeal_id") && maxJson.optInt("item_serial") == detail.optInt("setmeal_rwid"))
					{
						if (null == detailMaxJson || detailMaxJson.optDouble("item_amount") < detail.optDouble("item_amount"))
						{
							detailMaxJson = detail;
						}
					}
				}
				if (Tools.hv(detailMaxJson))
				{
					baseDao.update(updateItemSql.toString(), new Object[]
					{ scrapAmount, detailMaxJson.optInt("id") });
				}
			}
		}
	}
	

}
