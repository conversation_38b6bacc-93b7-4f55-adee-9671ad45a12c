package com.tzx.framework.common.print;

import java.util.ArrayList;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import javax.print.DocFlavor;
import javax.print.PrintService;
import javax.print.PrintServiceLookup;
import javax.print.attribute.HashAttributeSet;
import javax.print.attribute.standard.PrinterIsAcceptingJobs;
import javax.print.attribute.standard.PrinterName;
import javax.print.attribute.standard.PrinterState;

import net.sf.json.JSONObject;

import org.apache.log4j.Logger;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;

import com.tzx.framework.common.print.template.PrintTemplate_HTML;

/**
 * 打印工作将在UI之外的线程中处理，所以打印是越到的异常全部抛出 由负责打印的线程来处理打印异常，否则会因错误导致打印服务线程有异常而停止
 * 
 * <AUTHOR>
 * 
 */
public class PrintHelperNew
{

	private ConcurrentMap<String, PrintService>	map		= new ConcurrentHashMap<String, PrintService>();

	private static PrintHelperNew				self	= null;

	private static Logger	logger	= Logger.getLogger(PrintHelperNew.class);
	
	private PrintHelperNew()
	{
	}
	
	private static PrintHelperNew get()
	{
		if(self == null)
		{
			self = new PrintHelperNew();
		}
		
		return self;
	}
	
	public static PrintService getPrintService(String printname)throws Exception
	{
		PrintService service = get().map.get(printname);
		
		if(service != null)
		{
			return service;
		}
		
		HashAttributeSet attrset = new HashAttributeSet();
		attrset.add(new PrinterName(printname, null));
		
		PrintService[] services = PrintServiceLookup.lookupPrintServices(null, attrset);
		
		if(services != null && services.length > 0)
		{
			service = services[0];
			
			PrinterState printerState = service.getAttribute(PrinterState.class);
			if( null != printerState)
			{
				if(PrinterState.IDLE == printerState)
				{
					logger.info("打印机状态:空闲");
				}
				else if(PrinterState.PROCESSING == printerState)
				{
					logger.info("打印机状态:打印中");
				}
				else if(PrinterState.STOPPED == printerState)
				{
					logger.info("打印机状态:已停止");
				}
				else if(PrinterState.UNKNOWN == printerState)
				{
					logger.info("打印机状态:未知");
				}
			}
			else
			{
				logger.info("打印机作业状态:null");
			}
			
			PrinterIsAcceptingJobs isAcceptingJob =  service.getAttribute(PrinterIsAcceptingJobs.class);
			if( null != isAcceptingJob)
			{
				if(PrinterIsAcceptingJobs.NOT_ACCEPTING_JOBS == service.getAttribute(PrinterIsAcceptingJobs.class))
				{
					logger.info("打印机作业状态:不接受打印作业");
				}
				else if(PrinterIsAcceptingJobs.ACCEPTING_JOBS == service.getAttribute(PrinterIsAcceptingJobs.class))
				{
					logger.info("打印机作业状态:接受打印作业");
				}
			}
			else
			{
				logger.info("打印机状态:null");
			}
			
			if(service.isDocFlavorSupported(DocFlavor.SERVICE_FORMATTED.PRINTABLE))
			{
				get().map.put(printname, service);
				
				return service;
			}
			else
			{
				throw new Exception("打印服务【" + printname + "】不支持当前打印实现");
			}
		}
		else
		{
			throw new Exception("未找到打印服务【" + printname + "】");
		}
	}
	
	public static void main(String[] args) throws Exception
	{
		 String d = "<!--StartFragment--><h3 style=\"text-align: center;\">结账单</h3><h6 style=\"text-align: left;\">操作员：<em title=\"R1:org_full_name\">&lt;**&gt;</em>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;班次：<em title=\"R1:org_short_name\">&lt;**&gt;</em></h6><p><em title=\"R1:org_short_name\">----------------------------------</em></p><table border=\"0\" style=\"line-height: 22.3999996185303px;\"><tbody><tr><td align=\"center\" style=\"text-align: center;\"><h5 style=\"text-align: center;\"><strong>菜品名称&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</strong></h5></td><td style=\"text-align: center;\"><h5><strong>单价</strong></h5></td><td style=\"text-align: center;\"><h5><strong>数量</strong></h5></td><td style=\"text-align: center;\"><h5><strong>金额</strong></h5></td></tr><tr><td align=\"center\" style=\"text-align: center;\"><em title=\"R1:top_org_id\">&lt;**&gt;</em><br></td><td style=\"text-align: center;\"><em title=\"R1:manage_type\">&lt;**&gt;</em></td><td style=\"text-align: center;\"><em title=\"R1:phone\">&lt;**&gt;</em></td><td style=\"text-align: center;\"><em title=\"R1:fax\">&lt;**&gt;</em></td></tr><tr><td align=\"center\" style=\"text-align: center;\"><em title=\"R1:top_org_id\">&lt;**&gt;</em><br></td><td style=\"text-align: center;\"><em title=\"R1:manage_type\">&lt;**&gt;</em></td><td style=\"text-align: center;\"><em title=\"R1:phone\">&lt;**&gt;</em></td><td style=\"text-align: center;\"><em title=\"R1:fax\">&lt;**&gt;</em></td></tr><tr><td align=\"center\" style=\"text-align: center;\"><em title=\"R1:top_org_id\">&lt;**&gt;</em><br></td><td style=\"text-align: center;\"><em title=\"R1:manage_type\">&lt;**&gt;</em></td><td style=\"text-align: center;\"><em title=\"R1:phone\">&lt;**&gt;</em></td><td style=\"text-align: center;\"><em title=\"R1:fax\">&lt;**&gt;</em></td></tr></tbody></table><p>----------------------------------</p><h5>地址：xxxxxxxxxxxxxxxxxxxxx</h5><h5>电话：xxxxxxxxxxx</h5><!--EndFragment-->";
		 Document document = Jsoup.parse(d);
		// System.out.println(document.toString());
		//

//		boolean t = PrintHelperNew.get().getPrintService("80mm Series Printer").isAttributeCategorySupported(MediaPrintableArea.class);
//		System.out.println(t);
//		t = PrintHelperNew.get().getPrintService("80mm Series Printer").isAttributeCategorySupported(DocumentName.class);
//		System.out.println(t);
//		t = PrintHelperNew.get().getPrintService("80mm Series Printer").isDocFlavorSupported(DocFlavor.SERVICE_FORMATTED.PRINTABLE);
//		System.out.println(t);
//		t = PrintHelperNew.get().getPrintService("80mm Series Printer").isDocFlavorSupported(DocFlavor.STRING.TEXT_PLAIN);
//		System.out.println(t);
//		t = PrintHelperNew.get().getPrintService("80mm Series Printer").isDocFlavorSupported(DocFlavor.URL.TEXT_PLAIN_UTF_8);
//		System.out.println(t);
		
		
		PrintTemplate_HTML html = new PrintTemplate_HTML();

		String doc = html.parse(80, d, new ArrayList<JSONObject>());
		
		//print("80mm Series Printer", 80, html, Jsoup.parse(doc), 3);
		

	}
}
