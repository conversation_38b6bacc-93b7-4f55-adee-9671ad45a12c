package com.tzx.pos.bo;

import java.util.List;

import net.sf.json.JSONObject;

import com.tzx.framework.common.entity.Data;

public interface PosDishRetreatFoodService extends PosBaseService
{
	String	NAME	= "com.tzx.pos.bo.imp.PosDishRetreatFoodServiceImp";
	
	
	/**退菜校验
	 * @param tenancyId
	 * @param storeId
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void checkRetreatFood(String tenancyId, int storeId, List<?> param, Data result) throws Exception;
	
	/**
	 * 退菜
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param param
	 * @param result
	 * @param json
	 * @param agent
	 * @throws Exception
	 */
	public void retreatFood(String tenancyId, int storeId, List<?> param, Data result, JSONObject json, String agent) throws Exception;

}
