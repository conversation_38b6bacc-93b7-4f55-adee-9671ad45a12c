package com.tzx.pos.base.dao;

import java.sql.Timestamp;
import java.util.List;

import com.tzx.member.common.entity.PosCustomerOperateListEntity;


public interface PosCustomerOperateDao extends BaseDao
{
	 String NAME = "com.tzx.pos.base.dao.imp.PosCustomerOperateDaoImp";

	/** 获取未完成的会员操作记录
	 * @param tenancyId
	 * @param storeId
	 * @param currentTime
	 * @param queryCountNum
	 * @return
	 */
	public List<PosCustomerOperateListEntity> getPosCustomerOperateListNotComplete(String tenancyId, int storeId,Timestamp currentTime, Double queryCountNum)throws Exception;
	
	/** 根据查询结果修改会员操作记录
	 * @param tenancyId
	 * @param storeId
	 * @param customerOperate
	 * @throws Exception
	 */
	public void updatePosCustomerOperateListByQueryResult(String tenancyId, int storeId,PosCustomerOperateListEntity customerOperate) throws Exception;
}
