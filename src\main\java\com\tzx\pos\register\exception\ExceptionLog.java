package com.tzx.pos.register.exception;

import java.util.Date;  

/**  
 * 异常信息日志表  
 *   
 */  
public class ExceptionLog {  
    
  
    /**  
     * 远程访问主机IP  
     */  
    private String ip;  
  
    /**  
     * 类名  
     */  
    private String className;  
  
    /**  
     * 方法名  
     */  
    private String methodName;  
  
    /**  
     * 异常类型  
     */  
    private String exceptionType;  
  
    /**  
     * 异常发生时间  
     */  
    private Date addtime;  
  
    /**  
     * 异常信息  
     */  
    private String exceptionMsg;



	public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}

	public String getClassName() {
		return className;
	}

	public void setClassName(String className) {
		this.className = className;
	}

	public String getMethodName() {
		return methodName;
	}

	public void setMethodName(String methodName) {
		this.methodName = methodName;
	}

	public String getExceptionType() {
		return exceptionType;
	}

	public void setExceptionType(String exceptionType) {
		this.exceptionType = exceptionType;
	}

	public Date getAddtime() {
		return addtime;
	}

	public void setAddtime(Date addtime) {
		this.addtime = addtime;
	}

	

	public String getExceptionMsg() {
		return exceptionMsg;
	}

	public void setExceptionMsg(String exceptionMsg) {
		this.exceptionMsg = exceptionMsg;
	}  
  
         
        // setter、getter方法略......  
    
  
}  
