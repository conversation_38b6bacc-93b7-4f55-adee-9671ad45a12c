package com.tzx.pos.base.service.impl;

import java.util.Date;

import javax.annotation.Resource;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import com.tzx.pos.base.dao.AutoUploadDataDao;
import com.tzx.pos.base.service.AutoUploadDataService;
import com.tzx.pos.bo.UploadDataService;

@Service(AutoUploadDataService.NAME)
public class AutoUploadDataServiceImp implements AutoUploadDataService
{
	private static Logger		logger	= Logger.getLogger(AutoUploadDataService.class);

	@Resource(name = UploadDataService.NAME)
	private UploadDataService	uploadDataService;

	@Resource(name = AutoUploadDataDao.NAME)
	private AutoUploadDataDao	uploadDao;

	@Override
	public void AutoUploadData(String tenancyId, int storeId) throws Exception
	{
		String[] tableNames = uploadDao.getSysParameter(tenancyId, storeId, "autouploaddatatable").split(",");

		String uploadDataWay = uploadDao.getSysParameter(tenancyId, storeId, "upload_data_way");

		if (UploadDataService.UPLOAD_DATA_WAY_HTTP.equals(uploadDataWay))
		{
			Date reportDate = uploadDao.getCurrentReportDate(tenancyId, storeId);

			uploadDataService.UploadDataByHttp(tenancyId, storeId, tableNames, reportDate, UploadDataService.MODE_AUTO_UPLOAD_DATA);
		}
		else
		{
			uploadDataService.AutoUploadDataByMQ(tenancyId, storeId, tableNames);
		}
	}
}
