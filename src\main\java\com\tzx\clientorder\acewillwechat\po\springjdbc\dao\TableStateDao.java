package com.tzx.clientorder.acewillwechat.po.springjdbc.dao;

import com.tzx.framework.common.util.dao.GenericDao;
import net.sf.json.JSONObject;

/**
 * Created by qin-<PERSON><PERSON> on 2018/3/9.
 */
public interface TableStateDao extends GenericDao {
    String	NAME	= "com.tzx.clientorder.acewillwechat.po.springjdbc.dao.imp.TableStateDaoImp";

    /**
     * 扫码查询桌态
     * @param tenancyId
     * @param storeId
     * @param tableCode
     * @return
     * @throws Exception
     */
    JSONObject getTableState(String tenancyId, int storeId, String tableCode) throws Exception;

    /**
     * 查询桌态，存储账单表的订单号
     * @param tenancyId
     * @param billNum
     * @param orderNum
     * @throws Exception
     */
    void updatePosBill(String tenancyId, String billNum, String orderNum) throws Exception;

    /**
     * 查询是否锁单(返回结果不为空，则为锁单状态)
     * @param tenancyId
     * @param storeId
     * @param tableCode
     * @return
     * @throws Exception
     */
    JSONObject getTableOpen(String tenancyId, int storeId, String tableCode) throws Exception;
}
