package com.tzx.estate.base.constant;

public enum Oper
{
	/**
	 * 验证
	 */
	check,
	/**
	 * 全部加载
	 */
	load,
	/**
	 * 新增
	 */
	add,
	/**
	 * 修改
	 */
	update,
	/**
	 * 删除
	 */
	delete,
	/**
	 * 查询
	 */
	find,
	/**
	 * 初始化
	 */
	init,
	/**
	 * 上传
	 */
	upload,
	/**
	 * 预下单支付
	 */
	scan,
	/**
	 * 条码支付
	 */
	barcode,
	/**
	 * 取消订单
	 */
	cancle,
	/**
	 * 微信查询
	 */
	query,
	/**
	 * 申请退款
	 */
	refund,
	/**
	 * 重新下单
	 */
	reorder,
	/**
	 * 查询退款
	 */
	refundquery,
	/**
	 * 撤销订单
	 */
	reverse,
	/**
	 * 投诉
	 */
	opinion,
	/**
	 * 订单完成
	 */
	complete,
	//scm 使用追加
	 
	
	/**
	 * 插号
	 */
	insert,
	
	/**
	 * 开户
	 */
	open,
	/**
	 * 查询明细
	 */
	detail,
	/**
	 * 查询上架物品
	 */
	queryup
	;
}
