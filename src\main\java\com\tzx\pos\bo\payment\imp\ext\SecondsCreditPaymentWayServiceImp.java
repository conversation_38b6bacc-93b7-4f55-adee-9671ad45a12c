package com.tzx.pos.bo.payment.imp.ext;

import java.sql.Timestamp;
import java.util.Date;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Service;

import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.util.Tools;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.bo.payment.PaymentWayService;
import com.tzx.pos.bo.payment.imp.PaymentWayServiceImp;
import com.tzx.pos.po.springjdbc.dao.PosBillMemberDao;

/**
 * Created by MrChen on 2017-09-15.秒付积分
 */
@Service("com.tzx.pos.bo.payment.imp.ext.SecondsCreditPaymentWayServiceImp")
public class SecondsCreditPaymentWayServiceImp extends PaymentWayServiceImp implements PaymentWayService {
	
	@Resource(name = PosBillMemberDao.NAME)
	private PosBillMemberDao			memberDao;
	
    @Override
    public void payment(String tenantId, int storeId, String billNum, String batchNum, Date reportDate, int shiftId, String posNum, String optNum, String tableCode, JSONObject payment, Timestamp currentTime, String isOnlinePay) throws Exception {
       try {
           // String chanel = payment.optString("chanel");
           double amount = payment.optDouble("amount",0);//抵现金额
           double currencyAmount = payment.optDouble("currency_amount",0);
           double count = payment.optDouble("count",0);
           double jifen = payment.optDouble("jifen",0);//积分
           String number = payment.optString("number");
           Double useful_credit = payment.optDouble("useful_credit",0);
           String phone = payment.optString("mobile");
           String name = payment.optString("name");
           String billCode = payment.optString("bill_code");
           Integer customer_id = payment.optInt("customer_id");

           int jzid = payment.optInt("jzid");
           String paymentClass = payment.optString("payment_class");
           String paymentName = payment.optString("payment_name");
           String nameEnglish = payment.optString("payment_english_name");
           double rate = payment.optDouble("rate",0);
           String remark = payment.optString("remark");
           String isYsk = payment.optString("is_ysk");

           if (Tools.isNullOrEmpty(isYsk))
           {
               isYsk = "N";
           }
           if (Tools.isNullOrEmpty(currencyAmount)||currencyAmount<amount)
           {
               currencyAmount = amount;
           }


           memberDao.deletePosBillMember(tenantId, storeId, billNum, SysDictionary.BILL_MEMBERCARD_JFDX05);
//           paymentDao.insertPosBillMember(tenantId, storeId, billNum, reportDate, SysDictionary.BILL_MEMBERCARD_JZ03, number, remark, phone, currentTime, SysDictionary.BILL_MEMBER_REMARK_CREDIT, null, 0);
           memberDao.insertPosBillMember(tenantId, storeId,billNum ,reportDate , SysDictionary.BILL_MEMBERCARD_JFDX05, number, remark, phone, currentTime, null,  name, useful_credit+jifen, useful_credit, jifen, amount, billCode,
                   SysDictionary.REQUEST_STATUS_COMPLETE);
           if ("1".equalsIgnoreCase(isOnlinePay))
           {
               paymentDao.insertPosBillPayment(tenantId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, nameEnglish, rate, amount, count, currencyAmount, number, phone, isYsk, customer_id, billCode, remark, currentTime,
                       SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);

//               paymentDao.insertPosBillPaymentLog(tenantId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, nameEnglish, rate, amount, count, currencyAmount, number, phone, isYsk, customer_id, billCode, remark, currentTime,
//                       SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);

               memberDao.updatePosBillMember(tenantId, storeId, billNum, SysDictionary.BILL_MEMBERCARD_JFDX05, currencyAmount, amount, billCode, SysDictionary.REQUEST_STATUS_COMPLETE, null, 0d, 0d);
           }
           else
           {
               payment.put("pay_state", SysDictionary.PAYMENT_STATE_NOTPAY);
               paymentDao.insertPosBillPayment(tenantId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, nameEnglish, rate, amount, count, currencyAmount, number, phone, isYsk, customer_id, billCode, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY,
                       batchNum, payment.toString(), 0d);
           }
       }catch (Exception e){
           logger.error("会员信息处理失败"+e);
       }


    }

    @Override
    public void paymentDebit(String tenancyId, int storeId, String billNum, String orderNum, String chanel, Double paymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData) throws Exception {

    }

    @Override
    public void findPaymentDebit(String tenancyId, int storeId, String billNum, String orderNum, String chanel, Double paymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData) throws Exception {

    }

    @Override
    public void paymentRefund(String tenancyId, int storeId, String billNum, String oldBillNum, String orderNum, String chanel, Double billPaymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData, String operType) throws Exception {

    }

    @Override
    public void paymentCancel(String tenancyId, int storeId, String billNum, String orderNum, String chanel, Double billPaymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData, String operType) throws Exception {

    }

    @Override
    public void paymentRegain(String tenancyId, int storeId, String billNum, String orderNum, String chanel, Double billPaymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData, String operType) throws Exception {

    }

	@Override
	public void paymentDebitResult(String tenancyId, int storeId, String billNum, JSONObject paymentJson) throws Exception
	{
		// TODO 自动生成的方法存根
		
	}
}
