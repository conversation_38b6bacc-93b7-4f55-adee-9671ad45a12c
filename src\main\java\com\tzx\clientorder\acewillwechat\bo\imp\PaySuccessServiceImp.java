package com.tzx.clientorder.acewillwechat.bo.imp;

import java.beans.PropertyDescriptor;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.sf.json.JSONObject;
import net.sf.json.JsonConfig;
import net.sf.json.util.PropertyFilter;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.tzx.base.common.util.PosPropertyUtil;
import com.tzx.clientorder.acewillwechat.bo.PaySucessService;
import com.tzx.clientorder.acewillwechat.po.springjdbc.dao.AcewillPaySucessDao;
import com.tzx.clientorder.common.entity.AcewillPayData;
import com.tzx.clientorder.common.entity.AcewillPayInfo;
import com.tzx.clientorder.common.entity.AcewillPayMessage;
import com.tzx.clientorder.common.entity.AcewillPayWLife;
import com.tzx.clientorder.common.entity.AcewillPaymentWay;
import com.tzx.clientorder.common.entity.AcewillPosBill;
import com.tzx.clientorder.common.entity.AcewillPosBillMember;
import com.tzx.clientorder.common.entity.AcewillPosBillPayment;
import com.tzx.clientorder.common.entity.AcewillPosBillPaymentCoupons;
import com.tzx.framework.common.util.DoubleHelper;
import com.tzx.framework.common.util.HttpUtil;
import com.tzx.member.common.entity.CrmCardTradingListEntity;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.constant.WLifeConstant;
import com.tzx.pos.base.util.CacheTableUtil;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.base.util.JsonUtil;
import com.tzx.pos.bo.PosPrintNewService;
import com.tzx.pos.bo.PosPrintService;
import com.tzx.pos.bo.payment.PosPaymentService;

@Service(PaySucessService.NAME)
public class PaySuccessServiceImp implements PaySucessService {

	private static final Logger LOGGER = LoggerFactory.getLogger(PaySuccessServiceImp.class);

	@Resource(name = AcewillPaySucessDao.NAME)
	AcewillPaySucessDao paySucessDao;

//	@Resource(name = PosBaseService.NAME)
//	PosBaseService posBaseServiceImp;
	@Resource(name = PosPaymentService.NAME)
	PosPaymentService posPaymentService;

	@Resource(name = PosPrintService.NAME)
	private PosPrintService posPrintService;

	@Resource(name = PosPrintNewService.NAME)
	private PosPrintNewService posPrintNewService;

	static {
		paymentWayMapping.put(WLifeConstant.PAY_SOURCE_ALIPAY, SysDictionary.PAYMENT_CLASS_WLIFE_ALI_PAY);
		paymentWayMapping.put(WLifeConstant.PAY_SOURCE_WEIXIN, SysDictionary.PAYMENT_CLASS_WLIFE_WECHAT_PAY);
		paymentWayMapping.put(WLifeConstant.PAY_SOURCE_BALANCE, SysDictionary.PAYMENT_CLASS_WLIFE_BALANCE);
		paymentWayMapping.put(WLifeConstant.PAY_SOURCE_CREDIT, SysDictionary.PAYMENT_CLASS_WLIFE_CREDIT);
		paymentWayMapping.put(WLifeConstant.PAY_SOURCE_COUPON, SysDictionary.PAYMENT_CLASS_WLIFE_COUPON);
		paymentWayMapping.put(WLifeConstant.PAY_SOURCE_PRODUCT, SysDictionary.PAYMENT_CLASS_WLIFE_COUPON);
		paymentWayMapping.put(WLifeConstant.PAY_SOURCE_WLIFE, "wlife");

		posBillMemberTyoeMapping.put(WLifeConstant.PAY_SOURCE_ALIPAY, SysDictionary.BILL_MEMBERCARD_HYZK02);
		posBillMemberTyoeMapping.put(WLifeConstant.PAY_SOURCE_WEIXIN, SysDictionary.BILL_MEMBERCARD_HYZK02);
		posBillMemberTyoeMapping.put(WLifeConstant.PAY_SOURCE_BALANCE, SysDictionary.BILL_MEMBERCARD_CZXF03);
		posBillMemberTyoeMapping.put(WLifeConstant.PAY_SOURCE_CREDIT, SysDictionary.BILL_MEMBERCARD_JFDX05);
		posBillMemberTyoeMapping.put(WLifeConstant.PAY_SOURCE_COUPON, SysDictionary.BILL_MEMBERCARD_YHJ04);
		posBillMemberTyoeMapping.put(WLifeConstant.PAY_SOURCE_PRODUCT, SysDictionary.BILL_MEMBERCARD_YHJ04);
		posBillMemberTyoeMapping.put(WLifeConstant.PAY_SOURCE_WLIFE, SysDictionary.BILL_MEMBERCARD_JFZS06);

		posBillCouponTypeMapping.put(WLifeConstant.PAY_SOURCE_COUPON, SysDictionary.CUSTOMER_COUPON_TYPE_CASH);
		posBillCouponTypeMapping.put(WLifeConstant.PAY_SOURCE_PRODUCT, SysDictionary.CUSTOMER_COUPON_TYPE_DISH);
		posBillCouponTypeNameMapping.put(WLifeConstant.PAY_SOURCE_COUPON, "优惠券");
		posBillCouponTypeNameMapping.put(WLifeConstant.PAY_SOURCE_PRODUCT, "菜品劵");
	}

	@Override
	public JSONObject process(String tenancyId, Integer storeId, JSONObject jsonObject) throws Exception {
		//LOGGER.info("pay.success:=======================================================收到清台消息");
		// 解析传入参数
		AcewillPayMessage payMessage = (AcewillPayMessage) JSONObject.toBean(jsonObject, getJsonConfig());
		payMessage.setTenent_id(tenancyId);
		payMessage.setStore_id(storeId);
		if (payMessage.getData() == null) {
			// 参数错误
			LOGGER.error("参数错误");
			return null;
		}
		if (payMessage.getPay_info() == null) {
			// 参数错误
			LOGGER.error("参数错误");
			return null;
		}

		synchronized (payMessage.getData().getOid())
		{		
			//LOGGER.info("pay.success:单号："+payMessage.getData().getOid()+"=======================================================收到清台消息");
			JSONObject posBillJson = paySucessDao.getPosBillByBillNum(tenancyId, payMessage.getData().getOid());
			if (posBillJson == null) {
				// 账单不存在
				LOGGER.error("账单号为:{}的账单不存在", payMessage.getData().getOid());
				return null;
			}
			AcewillPosBill posBill = (AcewillPosBill) JSONObject.toBean(posBillJson, AcewillPosBill.class);
			
			List<JSONObject> optStateInfoList = posPaymentService.getOptStateInfo(tenancyId, storeId, posBill.getReport_date());
			if (optStateInfoList.size() > 0) {
				JSONObject optStateInfo = optStateInfoList.get(0);
				posBill.setPos_num(optStateInfo.getString("pos_num"));
				posBill.setCashier_num(optStateInfo.getString("opt_num"));
				posBill.setShift_id(optStateInfo.optInt("shift_id"));
			}
			
			// 判断账单状态
			String billProperty = posBill.getBill_property();
			if (!SysDictionary.BILL_PROPERTY_OPEN.equals(billProperty)) {
				// 账单已关闭
				LOGGER.error("账单号为:{}的账单已关闭", payMessage.getData().getOid());
				return null;
			}
			String paymentState = posBill.getPayment_state();
			if (!SysDictionary.PAYMENT_STATE_NOTPAY.equals(paymentState)) {
				LOGGER.error("账单号为:{}的账单处于支付中", payMessage.getData().getOid());
				// 账单支付中
				return null;
			}
			// 判断账单金额
			Double paymentAmount = posBill.getPayment_amount();
			if (paymentAmount - payMessage.getData().getAmount() != 0) {
				LOGGER.error("账单号为:{}金额不符合", payMessage.getData().getOid());
				// 金额不符合
				return null;
			}
	
			Map<String, AcewillPaymentWay> paymentWayMap = getPaymentWayMap(tenancyId, storeId, payMessage);
			List<AcewillPosBillPayment> posBillPaymentList = new ArrayList<>();
			List<AcewillPosBillMember> posBillMemberList = new ArrayList<>();
			List<CrmCardTradingListEntity> crmCardTradingListEntityList = new ArrayList<>();
			List<AcewillPosBillPaymentCoupons> posBillPaymentCouponsList = new ArrayList<>();
			Double sumPaymentAmount = 0d;
			LOGGER.info("校验paystore的返回值，微生活返回清台信息："+payMessage.toString());
			for (AcewillPayInfo payInfo : payMessage.getPay_info()) {
				sumPaymentAmount = DoubleHelper.add(sumPaymentAmount, payInfo.getAmount(), DEFAULT_SCALE);
				// 支付来源：微生活 不生成PosBillPayment
				if (!WLifeConstant.PAY_SOURCE_WLIFE.equals(payInfo.getSource())) {
					AcewillPaymentWay paymentWay = getPaymentWay(paymentWayMap, payInfo);
					if (paymentWay != null) {
						AcewillPosBillPayment createPosBillPayment = createPosBillPayment(payMessage, posBill, paymentWay,
								payInfo);
						posBillPaymentList.add(createPosBillPayment);
					}
				}
				// 如果有微生活会员信息
				if (payMessage.getWlife() != null) {
					// 判断微生活会员是否启用
					String sysParameter = CacheTableUtil.getSysParameter("CustomerType");
					if (sysParameter != null) {
						AcewillPosBillMember createPosBillMember = createPosBillMember(payMessage, posBill, payInfo);
						posBillMemberList.add(createPosBillMember);
						if (WLifeConstant.PAY_SOURCE_BALANCE.equals(payInfo.getSource())) {
							CrmCardTradingListEntity crmCardTradingList = createCrmCardTradingList(payMessage, posBill,
									payInfo);
							crmCardTradingListEntityList.add(crmCardTradingList);
						}
						if (WLifeConstant.PAY_SOURCE_COUPON.equals(payInfo.getSource())
								|| WLifeConstant.PAY_SOURCE_PRODUCT.equals(payInfo.getSource())) {
							AcewillPosBillPaymentCoupons posBillPaymentCoupons = createPosBillPaymentCoupons(payMessage,
									posBill, payInfo);
							posBillPaymentCouponsList.add(posBillPaymentCoupons);
						}
					}
				}
			}
	
			paySucessDao.savePosBillPayment(posBillPaymentList);
			paySucessDao.savePosBillMember(posBillMemberList);
			paySucessDao.saveCrmCardTradingList(crmCardTradingListEntityList);
			paySucessDao.savePosBillPaymentCouponsList(posBillPaymentCouponsList);
			if (DoubleHelper.sub(paymentAmount, sumPaymentAmount, DEFAULT_SCALE) <= 0) {
				JSONObject printJson = new JSONObject();
				JSONObject resultJson = new JSONObject();
				String isInvoice = StringUtils.isEmpty(payMessage.getData().getInvoice()) ? "0" : "1";
				//LOGGER.info("pay.success:单号："+posBill.getBill_num()+"=======================================================门店开始清台");
				posPaymentService.closedAcewillPosBill(tenancyId, storeId, posBill.getBill_num(),
						posBill.getReport_date(), posBill.getShift_id(), posBill.getPos_num(),
						posBill.getCashier_num(), "Y", isInvoice, resultJson, printJson, "1", DateUtil.currentTimestamp());
				//LOGGER.info("pay.success:单号："+posBill.getBill_num()+"=======================================================门店清台成功");
				// 打印
				try {
					//LOGGER.info("pay.success:单号："+posBill.getBill_num()+"=======================================================门店清台开始打印："+printJson);
					posPrintService.printPosBillForPayment(printJson, posPrintNewService);
					//LOGGER.info("pay.success:单号："+posBill.getBill_num()+"=======================================================门店清台打印完成");
				}catch (Exception e) {
					e.printStackTrace();
				}
				// 调用清台http接口
				//invokeAcewillCompleteOrder(posBill);
			}
			return null;
		}
	}

	@SuppressWarnings("unused")
	private void invokeAcewillCompleteOrder(AcewillPosBill posBill) throws Exception {
		//LOGGER.info("pay.success:单号："+posBill.getBill_num()+"=======================================================调用微生活清台");
		String shopId = CacheTableUtil.getSysParameter("wlife_shop_id");
		Map<String, String> uploadParamMap = new HashMap<String, String>();
		uploadParamMap.put("shop_id", shopId);
		uploadParamMap.put("oid", posBill.getBill_num());
		uploadParamMap.put("meal_number", null);
		uploadParamMap.put("ordermode", WLifeConstant.WLIFE_ORDER_MODE);
		uploadParamMap.put("out_order_id", posBill.getOrder_num());
		String url = PosPropertyUtil.getMsg("acewill.request.url");
		url += WLifeConstant.COMPLETE_ORDER_URL;
		String msg = HttpUtil.sendPostRequest(url, uploadParamMap);
		//LOGGER.info("pay.success:单号："+posBill.getBill_num()+"=======================================================调用微生活清台返回"+msg);
		System.err.println(msg);
	}

	private JsonConfig getJsonConfig() {
		JsonConfig config = new JsonConfig();
		config.setClassMap(getPayMessageClassMap());
		config.setRootClass(AcewillPayMessage.class);
		config.setJavaPropertyFilter(new PropertyFilter() {
			@Override
			public boolean apply(Object source, String name, Object value) {
				PropertyDescriptor propertyDescriptor = BeanUtils.getPropertyDescriptor(source.getClass(), name);
				return propertyDescriptor == null;
			}
		});
		return config;
	}

	private Map<String, Class<?>> getPayMessageClassMap() {
		Map<String, Class<?>> classMap = new HashMap<>();
		classMap.put("data", AcewillPayData.class);
		classMap.put("pay_info", AcewillPayInfo.class);
		classMap.put("wlife", AcewillPayWLife.class);
		return classMap;
	}

	private Map<String, AcewillPaymentWay> getPaymentWayMap(String tenantId, Integer storeId,
			AcewillPayMessage payMessage) throws Exception {
		List<String> paymentClassList = new ArrayList<>();
		for (AcewillPayInfo payInfo : payMessage.getPay_info()) {
			String paymentClass = paymentWayMapping.get(payInfo.getSource());
			if (!StringUtils.isEmpty(paymentClass)) {
				paymentClassList.add(paymentClass);
			}
		}
		List<JSONObject> paymentWayList = paySucessDao.findPaymentWay(tenantId, storeId, paymentClassList);
		Map<String, AcewillPaymentWay> paymentWayMap = new HashMap<>();
		if (paymentWayList != null) {
			for (JSONObject jsonObject : paymentWayList) {
				AcewillPaymentWay bean = JsonUtil.jsonToBean(jsonObject, AcewillPaymentWay.class);
				paymentWayMap.put(bean.getPayment_class(), bean);
			}
		}
		return paymentWayMap;
	}

	private AcewillPaymentWay getPaymentWay(Map<String, AcewillPaymentWay> paymentWayMap, AcewillPayInfo payInfo)
			throws Exception {
		String paymentClass = paymentWayMapping.get(payInfo.getSource());
		if (paymentClass == null) {
			LOGGER.error("未知的支付方式：{}", payInfo.getSource());
			return null;
		}
		AcewillPaymentWay paymentWay = paymentWayMap.get(paymentClass);
		if (paymentWay == null) {
			LOGGER.error("支付方式{}未启用", payInfo.getSource());
			return null;
		}
		return paymentWay;
	}

	private AcewillPosBillPayment createPosBillPayment(AcewillPayMessage payMessage, AcewillPosBill posBill,
			AcewillPaymentWay paymentWay, AcewillPayInfo payInfo) throws Exception {

		AcewillPayWLife wlife = payMessage.getWlife();

		AcewillPosBillPayment posBillPayment = new AcewillPosBillPayment();
		posBillPayment.setTenancy_id(payMessage.getTenent_id());
		posBillPayment.setStore_id(payMessage.getStore_id());
		posBillPayment.setBill_num(posBill.getBill_num());
		posBillPayment.setTable_code(posBill.getTable_code());
		posBillPayment.setType(paymentWay.getPayment_class());
		posBillPayment.setJzid(paymentWay.getId());
		posBillPayment.setName(paymentWay.getPayment_name1());
		posBillPayment.setName_english(paymentWay.getPayment_name2());
		posBillPayment.setAmount(payInfo.getAmount());
		posBillPayment.setCount(1);
		// 付款号码
		if (wlife != null) {
			posBillPayment.setNumber(wlife.getCno());
			posBillPayment.setPhone(null);
		}
		posBillPayment.setReport_date(DateUtil.parseDate(posBill.getReport_date()));
		posBillPayment.setShift_id(posBill.getShift_id());
		posBillPayment.setPos_num(posBill.getPos_num());
		// 收款员号
		posBillPayment.setCashier_num(posBill.getCashier_num());
		posBillPayment.setLast_updatetime(new Date());
		posBillPayment.setIs_ysk("N");
		posBillPayment.setRate(1d);
		posBillPayment.setCurrency_amount(payInfo.getAmount());
		// 上传标记
		posBillPayment.setUpload_tag(0);
		// 挂账人id
		posBillPayment.setCustomer_id(null);
		// 付款流水号
		posBillPayment.setBill_code(payInfo.getSerilNo());
		posBillPayment.setRemark(null);
		posBillPayment.setPayment_state(SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
		// 与总部通讯的缓存
		posBillPayment.setParam_cach(null);
		// 批次编号
		posBillPayment.setBatch_num(posBill.getBatch_num());
		// 多收礼卷
		posBillPayment.setMore_coupon(0d);
		posBillPayment.setFee(posBill.getService_amount());
		posBillPayment.setFee_rate(
				posBill.getService_discount() != null ? posBill.getService_discount().doubleValue() : null);
		// 优惠券类型
		String coupon_type = posBillCouponTypeMapping.get(payInfo.getSource());
		posBillPayment.setCoupon_type(coupon_type != null ? Integer.parseInt(coupon_type) : null);
		// 原付款方式ID
		posBillPayment.setYjzid(null);
		// 用户实付
		// posBillPayment.setCoupon_Buy_Price(coupon_buy_price);
		// 券账单净收
		// posBillPayment.setDue(due);
//		// 商家优惠承担
//		posBillPayment.setTenancy_assume(payInfo.getStorepay());
		if (WLifeConstant.PAY_SOURCE_BALANCE.equals(payInfo.getSource())){
			// 用户实付
			Double tmpAmount = (payInfo.getStorepay() != null ? payInfo.getStorepay()/100: 0.00);
			posBillPayment.setCoupon_buy_price(tmpAmount);
			// 券账单净收
			posBillPayment.setDue(tmpAmount);
			// 商家优惠承担(差额)
			posBillPayment.setTenancy_assume(payInfo.getAmount() - tmpAmount);
		}else {
			// 商家优惠承担
			posBillPayment.setTenancy_assume(payInfo.getStorepay());
		}
		// 第三方优惠承担
		// posBillPayment.setThird_Assume(third_assume);
		// 第三方票券服务费
		// posBillPayment.setThird_Fee(third_fee);
		return posBillPayment;
	}

	private AcewillPosBillMember createPosBillMember(AcewillPayMessage payMessage, AcewillPosBill posBill,
			AcewillPayInfo payInfo) {
		AcewillPayWLife wlife = payMessage.getWlife();
		wlife.setBalance(DoubleHelper.div(wlife.getBalance(), 100d, DEFAULT_SCALE));
		AcewillPosBillMember posBillMember = new AcewillPosBillMember();
		posBillMember.setTenancy_id(payMessage.getTenent_id());
		posBillMember.setStore_id(payMessage.getStore_id());
		posBillMember.setId(null);
		posBillMember.setBill_num(posBill.getBill_num());
		posBillMember.setReport_date(DateUtil.parseDate(posBill.getReport_date()));
		// 类型
		posBillMember.setType(posBillMemberTyoeMapping.get(payInfo.getSource()));
		posBillMember.setAmount(payInfo.getAmount());
		posBillMember.setCredit(wlife.getCredit().doubleValue());
		posBillMember.setCard_code(wlife.getCno());
		posBillMember.setMobil(null);
		posBillMember.setLast_updatetime(new Date());
		// 上传标记
		posBillMember.setUpload_tag(0);
		posBillMember.setRemark(null);
		posBillMember.setBill_code(payInfo.getSerilNo());
		// 请求状态
		posBillMember.setRequest_state(null);
		posBillMember.setCustomer_code(wlife.getCno());
		posBillMember.setCustomer_name(wlife.getName());
		// 交易前积分
		posBillMember.setConsume_before_credit(wlife.getCredit() == null ? null : wlife.getCredit().doubleValue());
		if (WLifeConstant.PAY_SOURCE_CREDIT.equals(payInfo.getSource())) {
			posBillMember.setConsume_before_credit(wlife.getCredit() + payInfo.getAmount());
		}
		// 交易后积分
		posBillMember.setConsume_after_credit(wlife.getCredit() == null ? null : wlife.getCredit().doubleValue());
		// 交易前主账户余额
		posBillMember.setConsume_before_main_balance(wlife.getBalance());
		if (WLifeConstant.PAY_SOURCE_BALANCE.equals(payInfo.getSource())) {
			posBillMember.setConsume_before_main_balance(wlife.getBalance() + payInfo.getAmount());
		}
		// 交易前赠送账户余额
		posBillMember.setConsume_before_reward_balance(0d);
		// 交易后主账户余额
		posBillMember.setConsume_after_main_balance(wlife.getBalance());
		// 交易后赠送账户余额
		posBillMember.setConsume_after_reward_balance(0d);
		return posBillMember;
	}

	private AcewillPosBillPaymentCoupons createPosBillPaymentCoupons(AcewillPayMessage payMessage,
			AcewillPosBill posBill, AcewillPayInfo payInfo) {
		AcewillPosBillPaymentCoupons posBillPaymentCoupons = new AcewillPosBillPaymentCoupons();
		// 商户id
		posBillPaymentCoupons.setTenancy_id(payMessage.getTenent_id());
		// 机构id
		posBillPaymentCoupons.setStore_id(payMessage.getStore_id());
		// ID-自增
		posBillPaymentCoupons.setId(null);
		// 报表日期
		posBillPaymentCoupons.setBill_num(posBill.getBill_num());
		// 账单编号
		posBillPaymentCoupons.setReport_date(DateUtil.parseDate(posBill.getReport_date()));
		// 付款方式ID
//		posBillPaymentCoupons.setPayment_id(posBill.getPayment_id());
		// 优惠劵号
		posBillPaymentCoupons.setCoupons_code(payInfo.getSerilNo());
		// 面值
		posBillPaymentCoupons.setDeal_value(payInfo.getAmount());
		// 劵类型名称
		posBillPaymentCoupons.setDeal_name(posBillCouponTypeNameMapping.get(payInfo.getSource()));
		// 操作时间
		posBillPaymentCoupons.setLast_updatetime(new Date());
		// 备注
		posBillPaymentCoupons.setRemark(null);
		// 上传标记
		posBillPaymentCoupons.setUpload_tag("0");
		// 是否撤销
		posBillPaymentCoupons.setIs_cancel("0");
		// 优惠券大类ID
		posBillPaymentCoupons.setClass_id(null);
		// 优惠券类型ID
		posBillPaymentCoupons.setType_id(null);
		// 优惠金额
		posBillPaymentCoupons.setDiscount_money(payInfo.getAmount());
		// 抵用数量
		posBillPaymentCoupons.setDiscount_num(null);
		// 渠道
		posBillPaymentCoupons.setChanel(posBill.getSource());
		// 菜品单价
		posBillPaymentCoupons.setPrice(null);
		// 抵扣菜品
		posBillPaymentCoupons.setItem_id(null);
		// 菜品数量
		posBillPaymentCoupons.setItem_num(null);
		// 优惠劵编码
		posBillPaymentCoupons.setCoupons_pro(payInfo.getSerilNo());
		// 优惠劵类型
		posBillPaymentCoupons.setCoupon_type(Integer.parseInt(posBillCouponTypeMapping.get(payInfo.getSource())));
		posBillPaymentCoupons.setCoupon_buy_price(null);
		posBillPaymentCoupons.setDue(null);
		posBillPaymentCoupons.setTenancy_assume(null);
		posBillPaymentCoupons.setThird_assume(null);
		posBillPaymentCoupons.setThird_fee(null);
		posBillPaymentCoupons.setRequest_state(2);

		return posBillPaymentCoupons;
	}

	private CrmCardTradingListEntity createCrmCardTradingList(AcewillPayMessage payMessage, AcewillPosBill posBill,
			AcewillPayInfo payInfo) {
		CrmCardTradingListEntity crmCardTradingList = new CrmCardTradingListEntity();
		Timestamp timestamp = new Timestamp(System.currentTimeMillis());
		AcewillPayWLife wlife = payMessage.getWlife();
		// 商户id
		crmCardTradingList.setTenancy_id(payMessage.getTenent_id());
		// Id
		crmCardTradingList.setId(null);
		// 卡id
		crmCardTradingList.setCard_id(null);
		// 卡号 
		crmCardTradingList.setCard_code(wlife.getCno());
		// 交易单号
		crmCardTradingList.setBill_code(posBill.getBill_num());
		// 交易渠道
		crmCardTradingList.setChanel(posBill.getSource());
		// 交易门店ID
		crmCardTradingList.setStore_id(payMessage.getStore_id());
		// 交易日期
		crmCardTradingList.setBusiness_date(new Date());
		// 主账户交易金额
		crmCardTradingList.setMain_trading(payMessage.getData().getAmount());
		// 赠送账户交易金额
		crmCardTradingList.setReward_trading(null);
		// 交易类型
		crmCardTradingList.setOperat_type(SysDictionary.OPERAT_TYPE_XF);
		;
		// 原主账户金额
		crmCardTradingList.setMain_original(
				DoubleHelper.add(wlife.getBalance(), payMessage.getData().getAmount(), DEFAULT_SCALE));
		// 原赠送账户金额
		crmCardTradingList.setReward_original(0d);
		// 押金金额
		crmCardTradingList.setDeposit(0d);
		// 操作员
		crmCardTradingList.setOperator(null);
		// 操作时间
		crmCardTradingList.setOperate_time(timestamp);
		// 账单金额
		crmCardTradingList.setBill_money(posBill.getBill_amount());
		// 第三方账单号
		crmCardTradingList.setThird_bill_code(null);
		// 原账单号
		crmCardTradingList.setBill_code_original(null);
		// 活动ID
		crmCardTradingList.setActivity_id(null);
		// 会员ID
		crmCardTradingList.setCustomer_id(null);
		// 已撤销金额
		crmCardTradingList.setRevoked_trading(0d);
		//
		crmCardTradingList.setBatch_num(null);
		// 最后修改时间
		crmCardTradingList.setLast_updatetime(timestamp);
		// 门店修改时间
		crmCardTradingList.setStore_updatetime(timestamp);
		// 卡类ID
		crmCardTradingList.setCard_class_id(null);
		// 会员name
		crmCardTradingList.setName(wlife.getName());
		// 会员电话
		crmCardTradingList.setMobil(null);
		// 操作员ID
		crmCardTradingList.setOperator_id(null);
		// 班次ID
		crmCardTradingList.setShift_id(posBill.getShift_id());
		// 卡余额
		crmCardTradingList.setTotal_balance(0d);
		// 卡赠送账户余额
		crmCardTradingList.setReward_balance(0d);
		// 卡主账户余额
		crmCardTradingList.setMain_balance(wlife.getBalance());
		// 付款方式
		crmCardTradingList.setPay_type(null);
		;
		// 销售人员ID
		crmCardTradingList.setSalesman(null);
		// 人员提成金额
		crmCardTradingList.setCommission_saler_money(0d);
		// 机构提成金额
		crmCardTradingList.setCommission_store_money(0d);
		// 可开票金额
		crmCardTradingList.setInvoice_balance(0d);

		crmCardTradingList.setPosNum(posBill.getPos_num());

		crmCardTradingList.setIs_invoice("0");
		crmCardTradingList.setPayment_state("1");
		crmCardTradingList.setRecharge_state("1");
		crmCardTradingList.setRequest_status("02");
		crmCardTradingList.setRequest_code(null);
		crmCardTradingList.setRequest_msg(null);

		return crmCardTradingList;
	}

}
