package com.tzx.pos.bo;

import com.tzx.framework.common.entity.Data;

import net.sf.json.JSONObject;


public interface TakeOrderingRefuseService {
     String	NAME	= "com.tzx.pos.bo.imp.TakeOrderingRefuseServiceImp";
     
     public Data refuseOrderingEntry(String billCode,String  reason,String channel) throws Exception;
	 /**
	  * 拒单接口
	  * @param billCode
	  * @param reason
	  * @param channel
	  * @throws Exception
	  */
	 public Data refuseOrderingPost(String billCode,String  reason,String channel) throws Exception;
	 
	 /**
	  * 整单取消调用
	  * @param billCode
	  * @param applyType
	  * @param channel
	  * @return
	  */
	 public void takeCancbill(String billCode,String applyType,String channel,Data result);
}
