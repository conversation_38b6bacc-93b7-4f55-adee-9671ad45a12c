package com.tzx.pos.base.controller;

import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.pos.base.util.SendUdpToPos;
import com.tzx.pos.po.springjdbc.dao.PosDao;
import net.sf.json.JSONObject;

import org.apache.log4j.Logger;

import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.util.Comet4jUtil;

import java.util.Map;


/**
 * 急推菜品通知客户端
 */
public class CometDataNoticeClientRunnable implements  Runnable{

    private static final Logger logger = Logger.getLogger(CometDataNoticeClientRunnable.class);

    private Data cjData;
    private String port;

    public CometDataNoticeClientRunnable(Data cjData)
    {
    	this.cjData = cjData;

        Map<String, String> systemMap = Constant.getSystemMap();
        this.cjData.setTenancy_id(systemMap.get("tenent_id"));
        this.cjData.setStore_id(Integer.parseInt(systemMap.get("store_id")));

        DBContextHolder.setTenancyid(this.cjData.getTenancy_id());
        PosDao posDao = SpringConext.getApplicationContext().getBean(PosDao.class);
        try
        {
            setPort(posDao.getSysParameter(this.cjData.getTenancy_id(), this.cjData.getStore_id(), "SYSUDPPORT"));
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }

    @Override
    public void run()
    {
        try
        {
            String message = JSONObject.fromObject(cjData).toString();
            //POS用到UDP
            SendUdpToPos.sendMessagePos(message, getPort());

        	Comet4jUtil.sendMessage2All(Comet4jUtil.POS_ANDROID, message);
			Comet4jUtil.sendMessage2All(Comet4jUtil.POS_PC, message);
			
        }
        catch (Exception e)
        {
        	logger.info("急推菜品通知客户端失败......");
            e.printStackTrace();
        }
    }

    public String getPort()
    {
        if (null != port && !"".equals(port))
        {
            this.port = String.valueOf(Integer.parseInt(port) + 9);
        }
        else
        {
            this.port = "7999";
        }
        return port;
    }

    public void setPort(String port)
    {
        this.port = port;
    }

	public Data getCjData() {
		return cjData;
	}

	public void setCjData(Data cjData) {
		this.cjData = cjData;
	}

}
