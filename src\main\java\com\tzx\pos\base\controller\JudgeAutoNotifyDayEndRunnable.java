/** 
 * @(#)CodeFormat.java    1.0   2018-04-18 
 * 
 * Copyright 北京普照天星科技有限公司. All rights reserved. 
 * PROPRIETARY/CONFIDENTIAL. Use is subject to license terms. 
 */
package com.tzx.pos.base.controller;

import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import org.apache.log4j.Logger;
import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.pos.po.springjdbc.dao.PosDao;

/**
 * 判断是否需要启用自动提醒打烊。
 * 
 * <AUTHOR> email:<EMAIL>
 * @version 1.0  2018-04-18
 * @see     
 * @since   JDK7.0
 * @update  
 */
public class JudgeAutoNotifyDayEndRunnable implements Runnable {
	
	private static final Logger	logger	= Logger.getLogger(JudgeAutoNotifyDayEndRunnable.class);

	@Override
	public void run() {
		try
		{
			// 等待Bean初始化完成
			while(SpringConext.getApplicationContext() == null){
				Thread.sleep(1000);
			}  
			logger.info("判断是否启动打烊自动提醒......");
			Map<String, String> systemMap = Constant.getSystemMap();
			if (!systemMap.containsKey("tenent_id") || !systemMap.containsKey("store_id")||"".equals(systemMap.get("tenent_id")) || "".equals(systemMap.get("store_id")))
			{
				logger.info("自动提醒打烊: tenent_id: "+systemMap.get("tenent_id")+", store_id: "+systemMap.get("store_id"));
				return;
			}
			String tenancyId = systemMap.get("tenent_id");
			int storeId = Integer.parseInt(systemMap.get("store_id"));
			DBContextHolder.setTenancyid(tenancyId);
			PosDao posDao = SpringConext.getApplicationContext().getBean(PosDao.class);			
			boolean isnotify = posDao.isNotifyDayEnd(tenancyId, storeId);
			if (isnotify)
			{
				logger.info("启动打烊自动提醒......");
				int notifyDayendPeriod = 1;
				Executors.newScheduledThreadPool(1).scheduleWithFixedDelay(new AutoNotifyDayEndRunnable(), 1, notifyDayendPeriod, TimeUnit.MINUTES);
			}
		}
		catch (Exception e)
		{
			logger.error("判断是否启用自动提醒打烊报错:", e);
		}
	}

}
