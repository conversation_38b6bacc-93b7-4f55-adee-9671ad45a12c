package com.tzx.clientorder.retail.service.rest;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.tzx.clientorder.retail.bo.RetailService;
import com.tzx.clientorder.wlifeprogram.common.util.ExceptionPrintUtil;
import com.tzx.clientorder.retail.common.Constant;
import com.tzx.framework.common.exception.OtherSystemException;
import com.tzx.framework.common.exception.SystemException;

import net.sf.json.JSONObject;

@Controller("RetailRest")
@RequestMapping(
{ "/retail/retailRest" })
public class RetailRest
{
	private static Logger	LOG	= Logger.getLogger(RetailRest.class);

	@Resource(name = RetailService.NAME)
	private RetailService	retailService;

	@RequestMapping(value =
	{ "/getBaseData" }, method =
	{ org.springframework.web.bind.annotation.RequestMethod.POST })
	@ResponseBody
	public JSONObject getBaseData(HttpServletRequest request, @RequestBody JSONObject reqObj)
	{
		JSONObject responseJson = new JSONObject();
		try
		{
			LOG.info("getBaseData request obj=" + reqObj.toString());

			setBaseParams(reqObj);
			this.retailService.getBaseData(reqObj, responseJson);
		}
		catch (OtherSystemException se)
		{
			ExceptionPrintUtil.buildWlifeOtherSysExceptionData(LOG, se, responseJson, Constant.RETAIL_BASEDATA_FAILED);
		}
		catch (SystemException se)
		{
			ExceptionPrintUtil.buildWlifeSysExceptionData(LOG, se, responseJson, Constant.RETAIL_BASEDATA_FAILED);
		}
		catch (Exception e)
		{
			ExceptionPrintUtil.buildWlifeExceptionData(LOG, e, responseJson, Constant.RETAIL_BASEDATA_FAILED);
		}
		LOG.info("getBaseData response obj =" + responseJson.toString());
		return responseJson;
	}

	@RequestMapping(value =
	{ "/getDishes" }, method =
	{ org.springframework.web.bind.annotation.RequestMethod.POST })
	@ResponseBody
	public JSONObject getDishes(HttpServletRequest request, @RequestBody JSONObject reqObj)
	{
		JSONObject responseJson = new JSONObject();
		try
		{
			LOG.info("getDishes request obj=" + reqObj.toString());
			setBaseParams(reqObj);
			this.retailService.getDishes(reqObj, responseJson);
		}
		catch (OtherSystemException se)
		{
			ExceptionPrintUtil.buildWlifeOtherSysExceptionData(LOG, se, responseJson, Constant.RETAIL_DISHS_FAILED);
		}
		catch (SystemException se)
		{
			ExceptionPrintUtil.buildWlifeSysExceptionData(LOG, se, responseJson, Constant.RETAIL_DISHS_FAILED);
		}
		catch (Exception e)
		{
			ExceptionPrintUtil.buildWlifeExceptionData(LOG, e, responseJson, Constant.RETAIL_DISHS_FAILED);
		}
		LOG.info("getDishes response obj =" + responseJson.toString());
		return responseJson;
	}

	@RequestMapping(value =
	{ "/firstPay" }, method =
	{ org.springframework.web.bind.annotation.RequestMethod.POST })
	@ResponseBody
	public JSONObject firstPay(HttpServletRequest request, @RequestBody JSONObject reqObj)
	{
		JSONObject responseJson = new JSONObject();
		try
		{
			LOG.info("firstPay request obj=" + reqObj.toString());
			setBaseParams(reqObj);
			this.retailService.firstPayClose(reqObj, responseJson);
		}
		catch (OtherSystemException se)
		{
			ExceptionPrintUtil.buildWlifeOtherSysExceptionData(LOG, se, responseJson, Constant.RETAIL_BILLCLOSE_FAILED);
		}
		catch (SystemException se)
		{
			ExceptionPrintUtil.buildWlifeSysExceptionData(LOG, se, responseJson, Constant.RETAIL_BILLCLOSE_FAILED);
		}
		catch (Exception e)
		{
			ExceptionPrintUtil.buildWlifeExceptionData(LOG, e, responseJson, Constant.RETAIL_BILLCLOSE_FAILED);
		}
		LOG.info("firstPay response obj =" + responseJson.toString());
		return responseJson;
	}

	private void setBaseParams(JSONObject params)
	{
		int shopId = params.optInt("shop_id", 0);
		if (0 == shopId)
		{
			if (com.tzx.framework.common.constant.Constant.systemMap.containsKey("store_id"))
			{
				Integer storeId = Integer.valueOf(com.tzx.framework.common.constant.Constant.systemMap.get("store_id"));
				params.put("storeId", storeId);
			}
		}
		else
		{
			params.put("storeId", Integer.valueOf(shopId));
		}

		if (com.tzx.framework.common.constant.Constant.systemMap.containsKey("tenent_id"))
		{
			String tenancyId = com.tzx.framework.common.constant.Constant.systemMap.get("tenent_id");
			params.put("tenancyId", tenancyId);
		}
	}
}