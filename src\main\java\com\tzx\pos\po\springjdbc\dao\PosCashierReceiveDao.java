package com.tzx.pos.po.springjdbc.dao;

import java.sql.Timestamp;
import java.util.Date;

import com.tzx.base.entity.PosCashierReceiveLogEntity;
import com.tzx.pos.base.dao.BaseDao;

/**交款
 * 
 * <AUTHOR>
 *
 */
public interface PosCashierReceiveDao extends BaseDao
{
	String	NAME	= "com.tzx.pos.po.springjdbc.dao.imp.PosCashierReceiveDaoImp";
	
	/** 获取交款差额
	 * @param tenantId
	 * @param storeId
	 * @param reportDate
	 * @return
	 * @throws Exception
	 */
	public Double getCashierReceiveDifferenceAmount(String tenantId, Integer storeId, Date reportDate)throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param reportDate
	 * @param waiterId
	 * @param paymentId
	 * @return
	 * @throws Exception
	 */
	public Double getReceiveAmountByPayment(String tenancyId, int storeId, Date reportDate, Integer waiterId, Integer paymentId) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param reportDate
	 * @param waiterId
	 * @param paymentId
	 * @return
	 * @throws Exception
	 */
	public Double getReceiveAmountByRecharge(String tenancyId, int storeId, Date reportDate, Integer waiterId, Integer paymentId) throws Exception;

	/** 新增交款记录
	 * @param tenancyId
	 * @param storeId
	 * @param entity 交款表实体
	 * @return 影响记录条数
	 * @throws Exception
	 */
	public int insertCashierReceiveLog(String tenancyId, int storeId,PosCashierReceiveLogEntity entity) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param entity
	 * @param queryTime
	 * @return
	 * @throws Exception
	 */
	public int insertReceiveDetailsByRecharge(String tenancyId, int storeId, PosCashierReceiveLogEntity entity,Timestamp queryTime) throws Exception;
	
}
