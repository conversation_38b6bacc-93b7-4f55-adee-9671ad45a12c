package com.tzx.pos.base.util;



import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import net.sf.json.JSONObject;


public class SignUtils {

	private static final String DEVELOP_TOKEN="000006";
	private static final String TOKEN_PASSWORD="1qaz2wsx";
	private static final String SECRETKEY="fuhua+-2017com";
	private static final String ENCODING="UTF-8";
	private static final String hexDigits[] = { "0", "1", "2", "3", "4", "5","6", "7", "8", "9", "a", "b", "c", "d", "e", "f" };
	
	
	public static JSONObject request(String transCode){
		JSONObject json = new JSONObject();
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("develop_token", DEVELOP_TOKEN);
		map.put("token_password", md5Encode(TOKEN_PASSWORD,ENCODING));
		map.put("trans_code", transCode);//ORGA0000 机构列表 ORG --机构相关   L--列表  0001 接口�?
		String timpStamp = System.currentTimeMillis()+"";
	
		map.put("time_stamp", timpStamp);
		String  sign = getSignStr(map);
		json.put("sign", sign);
		json.put("time_stamp", timpStamp);
		return json;
		

    }
	public static boolean  sign(String secret,Map<String,Object> map){
		String sign= getSignStr(map);
		if(sign.equals(secret)){
			return true;
		}
		return false;
	}
	
	public static String sortParams (Map<String,Object> params){
		 params.put("secretKey", SECRETKEY);
		 Object[] key_arr = params.keySet().toArray();
         Arrays.sort(key_arr);
         StringBuilder str = new StringBuilder();
         for (Object key : key_arr) {
            String val = (String) params.get(key);
            str.append(key).append(val);
        }
        return str.toString();
	}
	
	public static  String getSignStr(Map<String,Object> param) {
	    String sortString=sortParams(param);
	    String sign = md5Encode(sortString.toString(),ENCODING).toUpperCase();
	    return sign;
	}
	
	public static String md5Encode(String origin, String charsetname) {
		String resultString = null;
		try {
			resultString = new String(origin);
			MessageDigest md = MessageDigest.getInstance("MD5");
			if (charsetname == null || "".equals(charsetname))
				resultString = byteArrayToHexString(md.digest(resultString.getBytes()));
			else
				resultString = byteArrayToHexString(md.digest(resultString.getBytes(charsetname)));
		} catch (Exception exception) {
		}
		return resultString;
	}
	
	private static String byteArrayToHexString(byte b[]) {
		StringBuffer resultSb = new StringBuffer();
		for (int i = 0; i < b.length; i++)
			resultSb.append(byteToHexString(b[i]));

		return resultSb.toString();
	}
	
	private static String byteToHexString(byte b) {
		int n = b;
		if (n < 0)
			n += 256;
		int d1 = n / 16;
		int d2 = n % 16;
		return hexDigits[d1] + hexDigits[d2];
	}
}
