package com.tzx.framework.common.umengpush.android;

import java.util.Iterator;
import java.util.Map.Entry;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import com.tzx.framework.common.umengpush.push.AndroidNotification;
import com.tzx.framework.common.umengpush.push.PushClient;
import com.tzx.framework.common.umengpush.push.android.AndroidBroadcast;
import com.tzx.framework.common.umengpush.push.android.AndroidCustomizedcast;
import com.tzx.framework.common.umengpush.push.android.AndroidFilecast;
import com.tzx.framework.common.umengpush.push.android.AndroidGroupcast;
import com.tzx.framework.common.umengpush.push.android.AndroidUnicast;

public class UMPusher {

	private String appkey = "58da2a6db27b0a155b0005a4";
	private String appMasterSecret = "hs8lzrfwdjimt7wkwf1zz8zjmrztcmrb";
	
	private AndroidNotification androidNof;
	
	private PushClient client = new PushClient();
	
	private PushInfo info;
	private JSONObject filterJson;
	
	public UMPusher(PushInfo info){
		this.info = info;
	}
	
	public static UMPusher build(PushInfo info){
		return new UMPusher(info);
	}
	
	private void initAndroidNof() throws Exception{
		if(androidNof != null){
			androidNof.setTicker(info.getTicker());
			androidNof.setTitle(info.getTitle());
			androidNof.setText(info.getText());
			androidNof.goAppAfterOpen();
			androidNof.setDisplayType(AndroidNotification.DisplayType.NOTIFICATION);
			androidNof.setProductionMode();
			if(info.getExtraFields() != null && !info.getExtraFields().isEmpty()){
				Iterator<Entry<String, String>> itr = info.getExtraFields().entrySet().iterator();
				while(itr.hasNext()){
					Entry<String, String> next = itr.next();
					androidNof.setExtraField(next.getKey(), next.getValue());
				}
			}
		}
	}
	
	public void send() throws Exception{
		if(androidNof != null){
			client.send(androidNof);
		}else {
			throw new Exception("没有初始化AndroidNotification对象");
		}
	}

	public AndroidBroadcast initAndroidBroadcast() throws Exception {
		AndroidBroadcast broadcast = new AndroidBroadcast(appkey,appMasterSecret);
		androidNof = broadcast;
		initAndroidNof();
		return broadcast;
	}
	
	public AndroidUnicast initAndroidUnicast(String deviceToken) throws Exception {
		AndroidUnicast unicast = new AndroidUnicast(appkey,appMasterSecret);
		// TODO Set your device token
		unicast.setDeviceToken(deviceToken);
		androidNof = unicast;
		initAndroidNof();
		return unicast;
	}
	
	/*  TODO
	 *  Construct the filter condition:
	 *  "where": 
	 *	{
	 *		"and": 
	 *		[
  	 *			{"tag":"test"},
  	 *			{"tag":"Test"}
	 *		]
	 *	}
	 *	Example:
	 	JSONObject filterJson = new JSONObject();
		JSONObject whereJson = new JSONObject();
		JSONArray tagArray = new JSONArray();
		JSONObject testTag = new JSONObject();
		JSONObject TestTag = new JSONObject();
		testTag.put("tag", "test");
		TestTag.put("tag", "Test");
		tagArray.put(testTag);
		tagArray.put(TestTag);
		whereJson.put("and", tagArray);
		filterJson.put("where", whereJson);
		
		目前开放的筛选字段有(FilterKeyEnum):
		"app_version"(应用版本) 
		"channel"(渠道) 
		"device_model"(设备型号) 
		"province"(省) 
		"tag"(用户标签) 
		"country"(国家) //"country"和"province"的类型定义请参照 附录J 
		"language"(语言) 
		"launch_from"(一段时间内活跃) 
		"not_launch_from"(一段时间内不活跃) 
	 */
	public JSONObject addFilterCondition(JSONArray jsonArray) throws JSONException{
		if(this.filterJson == null){
			this.filterJson = new JSONObject();
			JSONObject whereJson = new JSONObject();
			this.filterJson.put("where", whereJson);
		}
		JSONObject whereJson = (JSONObject)this.filterJson.get("where");
		if(jsonArray != null){
			whereJson.put("and", jsonArray);
		}else{
			throw new IllegalArgumentException("筛选条件不能为 null");
		}
		return this.filterJson;
	}
	/**
	 * 需要添加filter
	 * @return
	 * @throws Exception
	 */
	public AndroidGroupcast initAndroidGroupcast() throws Exception  {
		AndroidGroupcast groupcast = new AndroidGroupcast(appkey,appMasterSecret);
		androidNof = groupcast;
		initAndroidNof();
		if(filterJson != null){
			System.out.println(filterJson.toString());
			groupcast.setFilter(filterJson);
		}else{
			throw new IllegalArgumentException("umeng groupcast push 没有添加 filter");
		}
		return groupcast;
	}
	
	public AndroidCustomizedcast initAndroidCustomizedcast(String alias, String alias_type) throws Exception {
		AndroidCustomizedcast customizedcast = new AndroidCustomizedcast(appkey,appMasterSecret);
		androidNof = customizedcast;
		initAndroidNof();
		// TODO Set your alias here, and use comma to split them if there are multiple alias.
		// And if you have many alias, you can also upload a file containing these alias, then 
		// use file_id to send customized notification.
		customizedcast.setAlias(alias, alias_type);
		return customizedcast;
	}
	
	// 以下未处理 -----------------------------------------------------------
	
	public void sendAndroidCustomizedcastFile() throws Exception {
		AndroidCustomizedcast customizedcast = new AndroidCustomizedcast(appkey,appMasterSecret);
		// TODO Set your alias here, and use comma to split them if there are multiple alias.
		// And if you have many alias, you can also upload a file containing these alias, then 
		// use file_id to send customized notification.
		String fileId = client.uploadContents(appkey,appMasterSecret,"aa"+"\n"+"bb"+"\n"+"alias");
		customizedcast.setFileId(fileId, "alias_type");
		customizedcast.setTicker( "Android customizedcast ticker");
		customizedcast.setTitle(  "中文的title");
		customizedcast.setText(   "Android customizedcast text");
		customizedcast.goAppAfterOpen();
		customizedcast.setDisplayType(AndroidNotification.DisplayType.NOTIFICATION);
		// TODO Set 'production_mode' to 'false' if it's a test device. 
		// For how to register a test device, please see the developer doc.
		customizedcast.setProductionMode();
		client.send(customizedcast);
	}
	
	public void sendAndroidFilecast() throws Exception {
		AndroidFilecast filecast = new AndroidFilecast(appkey,appMasterSecret);
		// TODO upload your device tokens, and use '\n' to split them if there are multiple tokens 
		String fileId = client.uploadContents(appkey,appMasterSecret,"aa"+"\n"+"bb");
		filecast.setFileId( fileId);
		filecast.setTicker( "Android filecast ticker");
		filecast.setTitle(  "中文的title");
		filecast.setText(   "Android filecast text");
		filecast.goAppAfterOpen();
		filecast.setDisplayType(AndroidNotification.DisplayType.NOTIFICATION);
		client.send(filecast);
	}

	
	public PushInfo getInfo() {
		return info;
	}

	public void setInfo(PushInfo info) {
		this.info = info;
	}
	
	
	
}
