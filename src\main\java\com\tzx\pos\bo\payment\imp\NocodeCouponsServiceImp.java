package com.tzx.pos.bo.payment.imp;

import java.sql.Time;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.DoubleHelper;
import com.tzx.framework.common.util.Scm;
import com.tzx.framework.common.util.Tools;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.base.util.ReqDataUtil;
import com.tzx.pos.bo.imp.PosBaseServiceImp;
import com.tzx.pos.bo.payment.NocodeCouponsService;
import com.tzx.pos.po.springjdbc.dao.NocodeCouponsDao;

@Service(NocodeCouponsService.NAME)
public class NocodeCouponsServiceImp extends PosBaseServiceImp implements NocodeCouponsService
{
	private static final Logger	logger	= Logger.getLogger(NocodeCouponsService.class);

	@Resource(name = NocodeCouponsDao.NAME)
	private NocodeCouponsDao	couponsDao;

	@Override
	public void getNocodeCouponsValiData(Data param, Data result) throws Exception
	{
		try{
			String tenancyId = param.getTenancy_id();//门店id
			Integer storeId = param.getStore_id();//机构ID
			Map<String, Object> map = ReqDataUtil.getDataMap(param);
			Map<String, Double> item_unit_realprice = new HashMap<String, Double>();//规格id：金额
			Map<String, Integer> item_unit = new HashMap<String, Integer>();//规格id:菜品ID
			Map<String, Integer> item_unit_count = new HashMap<String, Integer>();//菜品规格id:菜品数量
			String billNum = ParamUtil.getStringValue(map, "bill_code", true, PosErrorCode.NOT_NULL_BILL_NUM);//账单号
			Double billMoney = ParamUtil.getDoubleValue(map, "bill_money", true, PosErrorCode.NOT_NULL_PAYMENT_AMOUNT);//账单金额
			String chanel = ParamUtil.getStringValue(map, "chanel", true, PosErrorCode.CHANEL_NOTEXIST_ERROE);//消费渠道
			String reportDateString = ParamUtil.getStringValue(map, "report_date", false, null);//报表日期
			Date reportDate = ParamUtil.getDateValue(map, "report_date", false, null);//报表日期
			//订单金额不能是0
			if(Double.valueOf("0d").equals(billMoney)){
				result.setCode(PosErrorCode.LOCAL_COUPONS_BILL_MONEY_ERROR.getNumber());
				result.setMsg(PosErrorCode.LOCAL_COUPONS_BILL_MONEY_ERROR.getMessage());
			}
			StringBuffer sb = new StringBuffer();//组织sql
			sb.append("select cct.with_discount,cct.use_cycle,ccc.class_name,cct.activity_subject,"+
			            "ccc.coupons_pro,ccc.help_memory_code,cct.start_coupon,cct.end_coupon,cct.remark,cct.face_value,"+
					    "cct.id as type_id,cct.father_id,cct.used_other,cct.used_main,cct.bill_limit_money,cct.bill_limit_num,"+
			            "cct.class_id,cct.is_appoin_item,cct.is_income,ccc.begintime,ccc.endtime,ccc.coupons_pro,ccc.is_total,"+
					    "(select array_to_string(array(SELECT ccd.unit_id FROM crm_coupons_details ccd WHERE ccd.type_id=cct.class_id),',')) as details,"+
			            "(SELECT array_to_string(ARRAY (SELECT concat(cctt.begintime,'_',cctt.endtime) FROM crm_coupons_type_times cctt "+
					    "WHERE cctt.coupons_type = cct. ID),',')) AS times,(case cct.validity_type when 1 then  now()< concat(to_char((ccc.create_time + COALESCE (cct.validity_days, 0) * INTERVAL '1 day'),'YYYY-MM-DD'),' 23:59:59')::timestamp else true end) as crt   " +
					 	"from crm_coupons_type cct ,crm_coupons_class ccc,crm_coupons_org cco,crm_coupons_chanel cc2 " +
						"WHERE cct.class_id = ccc. ID "+
						"AND cco.type_id = cct. ID AND cco.store_id = "+storeId+
						" AND cc2.type_id = cct. ID AND cc2.chanel = '"+chanel+
					    "' AND ccc.valid_state='1' and ccc.create_time is not null ORDER BY ccc.coupons_pro desc");//1为启用中的优惠券0为停用
			List<JSONObject> couponsList = couponsDao.query4Json(tenancyId,sb.toString());////查出的优惠券list集合
			List<JSONObject> listresult = new ArrayList<JSONObject>();;////过滤的优惠券list集合
			
			List<JSONObject> billList = couponsDao.findBill(tenancyId, storeId, billNum);
			
			firstListFor : for(JSONObject json: couponsList)
			{
				String startCoupon = json.getString("start_coupon");
				String endDate = json.getString("end_coupon");
				String billLimitMoney = json.optString("bill_limit_money");
				String className = json.optString("class_name");
				String classid = json.optString("class_id");
				String typeid = json.optString("type_id");
				int isTotal = json.optInt("is_total");//是否可抵菜品总价；1、抵菜品总价2、按照面值抵菜品价格
				double faceValue = Scm.pround(json.optDouble("face_value",0.0));//面值
				String useCycle = json.getString("use_cycle");//使用周期
				int used_other = json.optInt("used_other");//1 为不与其他券同时使用
				int used_main = json.optInt("used_main");//1 为不与主券同时使用
				String father_id = json.optString("father_id");//主券种id
				String times = json.optString("times");//09:00:00_15:20:59,16:00:00_18:00:59
				int billlimitNum = json.optInt("bill_limit_num");//账单限制数量
				int withDiscount = json.optInt("with_discount");//是否与可以其他优惠同时使用
				int isWithDiscount = 0;//是否与其他优惠同时使用了？
				String[] details = json.optString("details").split(",");//菜品券unit_id规格id
				String couponsPro = json.optString("coupons_pro");//券编码菜品券编码coupons_dish代金券编码coupons_deduct
				String crt = json.optString("crt");//是否过期validity_type=1过期时间x天，validity_type=0有效时间段
				String nowweek = DateUtil.dayForWeek(reportDateString)+"";//返回当天是星期几
				if(nowweek.equals("7")){//为了适应总部存的星期数据
					nowweek = "1";
				}else {
					nowweek = (Integer.parseInt(nowweek) + 1)+"";
				}
				String sysDateTime =DateUtil.format(DateUtil.currentTimestamp(), "yyyy-MM-dd");
	    		long millionSeconds= DateUtil.getMillionSeconds(sysDateTime);
	    		
	    		//判断优惠卷开始时间和结束时间
				if(!StringUtils.isEmpty(startCoupon) && !StringUtils.isEmpty(endDate) && !"null".equals(startCoupon)){
		    		long startCouponSeconds = DateUtil.getMillionSeconds(startCoupon);
		    		long strEndSeconds = DateUtil.getMillionSeconds(endDate);
		    		if(startCouponSeconds > millionSeconds){
		    			//result.setCode(Constant.LOCAL_COUPONS_START_TIME_CODE);
		    			//result.setMsg(Constant.LOCAL_COUPONS_START_TIME);
		    			continue;
		    		}else if(strEndSeconds < millionSeconds){
		    			//result.setCode(Constant.LOCAL_COUPONS_BB_OVERDUE_CODE);
		    			//result.setMsg(Constant.LOCAL_COUPONS_BB_OVERDUE_EMS);
		    			continue;
		    		}
			    }
				
				//账单金额大于预定金额才能使用
			    if(!StringUtils.isEmpty(billLimitMoney)){
			    	if(billMoney < Double.parseDouble(billLimitMoney)){
			    		//result.setCode(Constant.LOCAL_COUPONS_NOT_ENOUGH_CODE);
		    			//result.setMsg(Constant.LOCAL_COUPONS_NOT_ENOUGH_EMS);
			    		continue;
			    	}
			    }
			    
			    //判断useCycle使用周期
			    if(useCycle.indexOf(nowweek)<0){
			    	continue;
			    }
			    
			    //判断使用时段
			    Time now1 = DateUtil.getNowHHMMSSDate();
			    if(times != null && !"".equals(times) && !"null".equals(times))
				{
					String[] timesarr = times.split(",");
					int ff = 0;//不在有效时间内变量
					for(String tbe:timesarr)
					{
						String[] tbearr = tbe.split("_");
						if(tbearr.length==2 && tbearr[0].length()==8 &&tbearr[1].length()==8)
						{
							Time b1 = DateUtil.parseTime(tbearr[0]);
							Time e1 = DateUtil.parseTime(tbearr[1]);
							if(now1.after(b1) && now1.before(e1))
							{
								ff = 1;
								continue;//跳出这for循环
							}
						}
					}
					if(ff == 0)
					{
						continue;
					}
				}
			    
			    //优惠券有效期判断
			    if("false".equals(crt)){
			    	continue;
			    }
			    
			    //判断with_discount是否与其他优惠一起使用
			    for(JSONObject bill: billList){
			    	if(withDiscount > 0 && bill.optDouble("discount_amount") > 0.0){
			    		//一起使用了
			    		isWithDiscount = 1;
			    	}else{
			    		isWithDiscount = 0;
			    	}
			    }
			    if(isWithDiscount > 0){
			    	continue;
			    }
			    
			    //账单限制数量=账单限制总数量-账单使用券数量
			    //根据账单iD查询优惠劵付款记录
			    List<JSONObject> paymentList = couponsDao.getPaymentCouponsByBillNum(tenancyId, storeId, billNum);
			    String usedClassId = "";
			    String usedTypeId = "";
				int discountNum = 0;// 账单已使用数量
			    if(paymentList !=null && paymentList.size() > 0){//已经付过款有记录
			    	for(JSONObject paymentjson : paymentList){
			    		String paytypeid = paymentjson.optString("type_id");
			    		String payclassid = paymentjson.optString("class_id");
			    		Integer new_used_other = paymentjson.optInt("used_other");
			    		if(new_used_other != null && new_used_other == 1){
			    			break firstListFor;//支付过一次之后跳过剩下循环
			    		}
			    		if(payclassid.equals(json.optString("class_id")) && paytypeid.equals(json.optString("type_id")) && paymentjson.optInt("coupon_type")==2){
			    			discountNum = paymentjson.optInt("discount_num");//抵用数量
//			    			billlimitNum = billlimitNum - discountNum;
			    		}
			    		usedClassId = usedClassId + payclassid;
			    		usedTypeId = usedTypeId + paytypeid;
			    	}
			    }
			    
			    //判断是否与其他券同时使用，根据支付订单typeid和classid判断
			    if(used_other == 1){//不与其他券同时使用
			    	if(usedClassId.length() > 0 && usedTypeId.length() > 0){
			    		continue;
			    	}
			    }
			    
			    logger.info("优惠券大类名称："+className+"-----账单限制数量："+billlimitNum);
//			    if(billlimitNum >0){
//			    	json.put("bill_limit_num", billlimitNum);
//			    }else{
//			    	continue;//过了订单最大使用数量，不显示
//			    }
				if (0 < billlimitNum)//券可以数量为0,不限制
				{
					billlimitNum = billlimitNum - discountNum;// 当前可用数量=券可用数量-账单已使用数量
					if (0 >= billlimitNum)
					{
						continue;// 过了订单最大使用数量，不显示
					}
				}
				json.put("bill_limit_num", billlimitNum);
			    
			    StringBuilder bill_item_sql = new StringBuilder("select * from pos_bill_item where bill_num ='"+billNum+"'");
				List<JSONObject> billItems = couponsDao.query4Json(tenancyId, bill_item_sql.toString());//订单菜品集合
			    //菜品券与代金券逻辑稍有不同
				if("coupons_dish".equals(couponsPro))
				{
					for(JSONObject itemjson: billItems)
					{
						double real_amount = itemjson.optDouble("real_amount",0.0);//总金额
						double item_count = itemjson.optDouble("item_count",0.0);//菜品数量
						String item_unit_id = itemjson.optInt("item_unit_id")+"";//菜品规格id
						int item_count2 = itemjson.optInt("item_count");
						if(item_count2==0)
						{
							continue;
						}
						if(real_amount>0 && item_count>0)
						{
							double jsdj = Scm.pdiv(real_amount, item_count);//菜品单价
							item_unit_realprice.put(item_unit_id, jsdj);
							item_unit.put(item_unit_id, itemjson.optInt("item_id"));
							
							if(item_unit_count.containsKey(item_unit_id))
							{
								item_unit_count.put(item_unit_id,item_unit_count.get(item_unit_id)+item_count2);
							}
							else
							{
								item_unit_count.put(item_unit_id, item_count2);
							}
						}
					}
					
					String used_item_unit_id = "";
					double dkje = 0.0;//抵扣金额
					double lsjg = 0.0;//菜品券优惠菜品金额
				
					for(String udid : details)
					{
						if(item_unit_realprice.containsKey(udid))
						{
							double up = item_unit_realprice.get(udid);
							if(up>lsjg)
							{
								used_item_unit_id = udid;
								lsjg = up;
							}
						}
						
					}
					
					if(used_item_unit_id.length() == 0)
					{
						//没有符合条件的菜品或者已经使用完
						continue;
					}						
					double cosjjg = item_unit_realprice.get(used_item_unit_id);//菜品单价
					//抵扣的金额
					dkje = cosjjg;
					//菜品券 is_total 1全价 、2 按面值  
					if(isTotal==2 && faceValue < cosjjg)
					{
						dkje = faceValue;
					}
					json.put("discount", dkje);
					json.put("unit_id",Integer.parseInt(used_item_unit_id));
					json.put("item_id", item_unit.get(used_item_unit_id));
					json.put("item_amount", cosjjg);//菜品单价
					json.put("item_count", item_unit_count.get(used_item_unit_id));//菜品数量
				}
				else
				{
					double djqdkje = faceValue;//代金券抵扣金额
					if(djqdkje<0)
					{
						djqdkje = 0;
					}
					
					json.put("discount", djqdkje);//抵扣金额
					json.put("unit_id",0);
					json.put("item_id", 0);
					json.put("item_amount", 0);//菜品单价
					json.put("item_count", 0);//菜品数量
				}
				json.put("father_id", father_id);//father_id传整数前端会报错，和刘工商量，这里传给他字符类型的
				json.remove("with_discount");//前端用不到的参数，删不删的吧
				json.put("activity_subject", className);//无码券涉及不到活动，所以这里手动赋值，为了activity_subject不为空
				listresult.add(json);
			}
			logger.info("返回有效无码券："+listresult.toString());
			result.setData(listresult);
			result.setCode(Constant.CODE_SUCCESS);
	    	result.setMsg(Constant.LOCAL_COUPONS_SUCCESS);

		}catch(Exception e){
			logger.info(ExceptionMessage.getExceptionMessage(e));
			result.setSuccess(false);
			e.printStackTrace();
		}
		
	}

	@Override
	public void checkNocodeCoupons(Data param, Data result) throws Exception
	{
		// TODO Auto-generated method stub
		String tenancyId = param.getTenancy_id();
		Integer storeId = param.getStore_id();
		final int scale = 4;

		JSONObject paramJson = JSONObject.fromObject(param.getData().get(0));

		String billNum = ParamUtil.getStringValueByObject(paramJson, "bill_code", true, PosErrorCode.NOT_NULL_BILL_NUM);
		String chanel = ParamUtil.getStringValueByObject(paramJson, "chanel", true, PosErrorCode.NOT_NULL_CHANNEL);
		JSONArray couponsList = paramJson.optJSONArray("couponslist");

		// 获取账单信息
		JSONObject billJson = couponsDao.getPosBillByBillnum(tenancyId, storeId, billNum);
		Double discountAmount = 0d;
		Double paymentAmount = 0d;
		Double difference = 0d;
		if (null != billJson)
		{
			paymentAmount = ParamUtil.getDoubleValueByObject(billJson, "payment_amount");
			discountAmount = ParamUtil.getDoubleValueByObject(billJson, "discount_amount");
			difference = ParamUtil.getDoubleValueByObject(billJson, "difference");
		}

		// 获取付款信息
		List<JSONObject> paymentList = couponsDao.getPosBillPaymentForCouponByBillNum(tenancyId, storeId, billNum);

        Integer typeCount=0;
        Integer rwidCount=0;
		Map<String, Integer> typeCountMap = new HashMap<String, Integer>();// 使用优惠券数量
		Map<String, Integer> paymentRwidCountMap = new HashMap<String, Integer>();// 菜品抵扣数量

		if (null != paymentList)
		{
			// 已使用优惠券
			for (JSONObject paymentJson : paymentList)
			{
				Integer couCon = ParamUtil.getIntegerValueByObject(paymentJson, "discount_num");
				String typeId = ParamUtil.getStringValueByObject(paymentJson, "type_id");
				String rwid = ParamUtil.getStringValueByObject(paymentJson, "rwid");
				if (Tools.hv(typeId))
				{
					if (typeCountMap.containsKey(typeId))
					{
                        typeCount = typeCountMap.get(typeId);
					}
					typeCountMap.put(typeId,typeCount+couCon);

				}

				if (Tools.hv(rwid))
				{
					if (paymentRwidCountMap.containsKey(rwid))
					{
                        rwidCount= paymentRwidCountMap.get(rwid);
					}
                    paymentRwidCountMap.put(rwid,rwidCount + couCon);
				}
			}
		}

		// 获取优惠券信息
		List<String> couponTypeIdList = new ArrayList<String>();
		for (Object obj : couponsList)
		{
			String couponType = JSONObject.fromObject(obj).optString("type_id");
			Integer couCon = ParamUtil.getIntegerValueByObject(JSONObject.fromObject(obj), "discount_num");
			if (Tools.hv(couponType))
			{
				couponTypeIdList.add(couponType);

				if (typeCountMap.containsKey(couponType))
				{
                    typeCount= typeCountMap.get(couponType);
				}
				typeCountMap.put(couponType, typeCount+couCon);
			}
		}

		// 获取账单明细信息
		List<JSONObject> itemList = couponsDao.getPosBillItemForCouponByBillNum(tenancyId, storeId, billNum, couponTypeIdList);
		Map<String, Double> itemCountMap = new HashMap<String, Double>();
		Map<String, List<JSONObject>> typeItemMap = new HashMap<String, List<JSONObject>>();
		if (null != itemList)
		{
			List<JSONObject> typeItemList = null;
			for (JSONObject itemJson : itemList)
			{
				String rwid = ParamUtil.getStringValueByObject(itemJson, "rwid");
				Double itemCount = ParamUtil.getDoubleValueByObject(itemJson, "item_count");
				if (paymentRwidCountMap.containsKey(rwid))
				{
					itemCount = DoubleHelper.sub(itemCount, paymentRwidCountMap.get(rwid).doubleValue(), scale);
				}
				if (false == itemCountMap.containsKey(rwid))
				{
					itemCountMap.put(rwid, DoubleHelper.roundUp(itemCount, scale));
				}

				String typeId = ParamUtil.getStringValueByObject(itemJson, "type_id");
				if (typeItemMap.containsKey(typeId))
				{
					typeItemList = typeItemMap.get(typeId);
				}
				if (null == typeItemList)
				{
					typeItemList = new ArrayList<JSONObject>();
				}

				typeItemList.add(itemJson);
				typeItemMap.put(typeId, typeItemList);
			}

			//根据菜品金额排序
//			for (String key : typeItemMap.keySet())
//			{
//				Collections.sort(typeItemMap.get(key), new Comparator<JSONObject>()
//				{
//					public int compare(JSONObject o1, JSONObject o2)
//					{
//						// 排序优惠卷是不是全低
//						int order = o2.getString("is_total").compareTo(o1.getString("is_total"));
//						if (order != 0)
//						{
//							return order > 0 ? -1 : 1;
//						}
//						// 排序部分低价格
//						order = o1.getString("face_value").compareTo(o2.getString("face_value"));
//						if (order != 0)
//						{
//							return order > 0 ? -1 : 1;
//						}
//						return 0;
//					}
//				});
//			}

		}

		List<JSONObject> couponTypeList = couponsDao.getCrmCouponsType(tenancyId, storeId, chanel, couponTypeIdList);
		Map<String, JSONObject> couponTypeMap = new HashMap<String, JSONObject>();
		if (null != couponTypeList)
		{
			for (JSONObject couponTypeJson : couponTypeList)
			{
				String typeId = ParamUtil.getStringValueByObject(couponTypeJson, "type_id");
				couponTypeMap.put(typeId, couponTypeJson);
			}
		}

		Double totalCouponAmount = 0d;
		List<JSONObject> resCouponsList = new ArrayList<JSONObject>();
		JSONObject resCouponJson = null;
		for (Object obj : couponsList)
		{
			JSONObject couponJson = JSONObject.fromObject(obj);
			String typeId = ParamUtil.getStringValueByObject(couponJson, "type_id");
			Double couponCount = ParamUtil.getDoubleValueByObject(couponJson, "discount_num");
			JSONObject couponTypeJson = null;
			if (couponTypeMap.containsKey(typeId))
			{
				couponTypeJson = couponTypeMap.get(typeId);
			}
			else
			{
				throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_CRM_COUPONS_TYPE_ERROR).set("{0}", typeId);
			}

			String className = ParamUtil.getStringValueByObject(couponTypeJson, "class_name");
			String couponsPro = ParamUtil.getStringValueByObject(couponTypeJson, "coupons_pro");
			Double faceValue = ParamUtil.getDoubleValueByObject(couponTypeJson, "face_value");
			Double billLimitMoney = ParamUtil.getDoubleValueByObject(couponTypeJson, "bill_limit_money");

			// 校验优惠券有效性(有效期,使用时段,适用周期,可使用渠道,可使用门店)

			// 每笔账单满N元才能使用
			if (paymentAmount < billLimitMoney)
			{
				 throw SystemException.getInstance(PosErrorCode.COUPONS_NOT_FULL_BILL_LIMIT_MONEY_ERROR).set("{0}", billLimitMoney).set("{1}", className);
			}

			// 不与其它优惠并用
			if ("1".equals(couponTypeJson.optString("with_discount")) && 0 < discountAmount)
			{
				throw SystemException.getInstance(PosErrorCode.OFFSET_DISH_NOT_PERMIT_USER_DISCOUNT_ERROR).set("{0}", className);
			}

			// 每笔账单允许使用N张
			if (0 < couponTypeJson.optInt("bill_limit_num") && couponTypeMap.containsKey(typeId) && typeCountMap.get(typeId) > couponTypeJson.optInt("bill_limit_num"))
			{
				 throw SystemException.getInstance(PosErrorCode.COUPONS_NOT_FULL_BILL_LIMIT_NUM_ERROR).set("{0}", couponTypeJson.optInt("bill_limit_num")).set("{1}", className);
			}
			// 不与其它券并用
			if ("1".equals(couponTypeJson.optString("used_other")) && typeCountMap.keySet().size() > 1)
			{
				 throw SystemException.getInstance(PosErrorCode.COUPONS_NOT_PERMIT_USER_OTHER_ERROR).set("{0}", className);
			}

			// 计算抵扣金额
			Double amount = 0d;
			Double couponsAmount = 0d;
			List<String> itemRwidList = new ArrayList<String>();
			if (SysDictionary.COUPONS_PRO_DISH.equals(couponsPro))
			{
				if (typeItemMap.containsKey(typeId) && typeItemMap.get(typeId).size() > 0)
				{
					for (JSONObject itemJson : typeItemMap.get(typeId))
					{
						String rwid = ParamUtil.getStringValueByObject(itemJson, "rwid");
						Double itemCount = 0d;
						if (itemCountMap.containsKey(String.valueOf(rwid)))
						{
							itemCount = itemCountMap.get(String.valueOf(rwid));
						}

						Double count = itemCount;
						if (0 < itemCount && couponCount <= itemCount)
						{
							count = couponCount;
						}

						if (0 < count)
						{
							couponCount = DoubleHelper.sub(couponCount, count, scale);
							itemCount = DoubleHelper.sub(itemCount, count, scale);
							itemCountMap.put(rwid, itemCount);
							itemRwidList.add(rwid);

							Double realPrice = DoubleHelper.div(itemJson.optDouble("real_amount"), itemJson.optDouble("item_count"), 2);
							Double itemPrice = DoubleHelper.div(itemJson.optDouble("item_amount"), itemJson.optDouble("item_count"), 2);

							Double detailCouponsAmount = 0d;
							Double detailAmount = 0d;
							if ("2".equals(couponTypeJson.optString("is_total")))
							{
								if (faceValue <= realPrice)
								{
									detailCouponsAmount = DoubleHelper.mul(faceValue, count, scale);
								}
								else
								{
									detailCouponsAmount = DoubleHelper.mul(realPrice, count, scale);
									if (0 == itemCount)
									{
										// 差额
										detailCouponsAmount = DoubleHelper.add(detailCouponsAmount, DoubleHelper.sub(itemJson.optDouble("real_amount"), DoubleHelper.mul(realPrice, itemJson.optDouble("item_count"), scale), scale), scale);
									}
								}
								detailAmount = DoubleHelper.mul(faceValue, count, scale);
							}
							else
							{

								detailCouponsAmount = DoubleHelper.mul(realPrice, count, scale);
								detailAmount = DoubleHelper.mul(itemPrice, count, scale);

								if (0 == itemCount)
								{
									// 差额
									detailCouponsAmount = DoubleHelper.add(detailCouponsAmount, DoubleHelper.sub(itemJson.optDouble("real_amount"), DoubleHelper.mul(realPrice, itemJson.optDouble("item_count"), scale), scale), scale);
								}
							}

							difference = DoubleHelper.sub(difference, detailAmount, scale);
							// 校验抵扣金额是否超出账单金额,超出部分不能大于代金券面值
							if (0 > difference && Math.abs(difference) >= itemPrice)
							{
								throw SystemException.getInstance(PosErrorCode.COUPON_AMOUNT_MORE_DIFFERENCE_ERROR);
							}

							couponsAmount = DoubleHelper.add(couponsAmount, detailCouponsAmount, scale);
							amount = DoubleHelper.add(amount, detailAmount, scale);
						}

						// 优惠券抵扣完,退出循环
						if (0 == couponCount)
						{
							break;
						}
					}
				}

				if (0 < couponCount)
				{
					throw SystemException.getInstance(PosErrorCode.COUPON_NOT_OFFSET_DISH_ERROR).set("{0}", className);
				}
			}
			else
			{
				// 代金券,按面值抵扣金额
				couponsAmount = amount = DoubleHelper.mul(faceValue, couponCount.doubleValue(), scale);
				difference = DoubleHelper.sub(difference, couponsAmount, scale);
				// 校验抵扣金额是否超出账单金额,超出部分不能大于代金券面值
				if (0 > difference && Math.abs(difference) >= faceValue)
				{
					throw SystemException.getInstance(PosErrorCode.COUPON_AMOUNT_MORE_DIFFERENCE_ERROR);
				}
			}

			resCouponJson = new JSONObject();
			resCouponJson.put("type_id", ParamUtil.getStringValueByObject(couponTypeJson, "type_id"));
			resCouponJson.put("class_id", ParamUtil.getStringValueByObject(couponTypeJson, "class_id"));
			resCouponJson.put("class_name", className);
			resCouponJson.put("coupons_pro", couponsPro);
			resCouponJson.put("income_amount", ParamUtil.getDoubleValueByObject(couponTypeJson, "income_amount"));
			resCouponJson.put("count", ParamUtil.getDoubleValueByObject(couponJson, "discount_num"));
			resCouponJson.put("face_value", faceValue);
			resCouponJson.put("amount", amount);
			resCouponJson.put("coupons_amount", couponsAmount);
			resCouponJson.put("item_rwids", StringUtils.join(itemRwidList,","));
			resCouponsList.add(resCouponJson);

			totalCouponAmount = DoubleHelper.add(totalCouponAmount, couponsAmount, scale);
		}

		JSONObject resJson = new JSONObject();
		resJson.put("total_coupons_amount", totalCouponAmount);
		resJson.put("couponslist", resCouponsList);

		List<JSONObject> resList = new ArrayList<JSONObject>();
		resList.add(resJson);

//		result = param.clone();
		result.setData(resList);
		result.setCode(Constant.CODE_SUCCESS);
		result.setType(param.getType());
		result.setOper(param.getOper());
	}

}
