package com.tzx.pos.po.springjdbc.dao.imp;

import java.util.List;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Repository;

import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.dao.imp.BaseDaoImp;
import com.tzx.pos.po.springjdbc.dao.NocodeCouponsDao;

@Repository(NocodeCouponsDao.NAME)
public class NocodeCouponsDaoImp extends BaseDaoImp implements NocodeCouponsDao
{

	@Override
	public List<JSONObject> getCrmCouponsType(String tenancyId, Integer storeId, String chanel, List<String> couponTypeIdList) throws Exception
	{
		// TODO Auto-generated method stub
		if (null != couponTypeIdList && couponTypeIdList.size() > 0)
		{
			StringBuilder ids = new StringBuilder();
			for (String typeId : couponTypeIdList)
			{
				if (ids.length() > 0)
				{
					ids.append(",");
				}
				ids.append("'").append(typeId).append("'");
			}

			StringBuilder querySql = new StringBuilder();
			querySql.append("select cc.id class_id,cc.class_name,cc.coupons_pro,cc.is_total,cc.create_time,cc.income_amount,");
			querySql.append("ct.id type_id,ct.face_value,ct.validity_type,ct.validity_days,ct.start_coupon,ct.end_coupon,ct.use_cycle,ct.bill_limit_money,ct.bill_limit_num,ct.used_other,ct.with_discount ");
			querySql.append("from crm_coupons_type ct ");
			querySql.append("left join crm_coupons_class cc on ct.tenancy_id=cc.tenancy_id and ct.class_id=cc.id ");
			querySql.append("left join crm_coupons_org co on ct.tenancy_id=co.tenancy_id and ct.id=co.type_id ");
			querySql.append("left join crm_coupons_chanel ch on ct.tenancy_id=ch.tenancy_id and ct.id=ch.type_id ");
			querySql.append("where ct.state='1' and cc.valid_state='1' and co.tenancy_id=? and co.store_id=? and ch.chanel=? ");
			querySql.append("and ct.id in (").append(ids).append(")");
			
			return this.query4Json(tenancyId, querySql.toString(), new Object[]
			{ tenancyId, storeId, chanel });
		}
		return null;
	}

	@Override
	public List<JSONObject> getPosBillPaymentForCouponByBillNum(String tenancyId, Integer storeId, String billNum) throws Exception
	{
		StringBuilder querySql = new StringBuilder();
		querySql.append("select pc.type_id,pc.discount_num,pc.rwid from pos_bill_payment bp ");
		querySql.append("left join pos_bill_payment_coupons pc on bp.tenancy_id=pc.tenancy_id and bp.store_id=pc.store_id and bp.bill_num=pc.bill_num and bp.payment_uid=pc.payment_id ");
		querySql.append("where bp.type=? and bp.tenancy_id=? and bp.store_id=? and bp.bill_num=? ");

		return this.query4Json(tenancyId, querySql.toString(), new Object[]
		{ SysDictionary.PAYMENT_CLASS_COUPONS, tenancyId, storeId, billNum });
	}

	@Override
	public List<JSONObject> getPosBillItemForCouponByBillNum(String tenancyId, Integer storeId, String billNum, List<String> couponTypeIdList) throws Exception
	{
		if (null != couponTypeIdList && couponTypeIdList.size() > 0)
		{
			StringBuilder typeIds = new StringBuilder();
			for (String typeId : couponTypeIdList)
			{
				if (typeIds.length() > 0)
				{
					typeIds.append(",");
				}
				typeIds.append("'").append(typeId).append("'");
			}

			StringBuilder sql = new StringBuilder("select * from (select bi.rwid,bi.real_amount,bi.item_amount,bi.item_id,bi.item_unit_id,bi.item_count,ct.id type_id,round(bi.real_amount/bi.item_count,2) as real_price ,round(bi.item_amount/bi.item_count,2) item_price ");
			sql.append("from pos_bill_item bi left join crm_coupons_details cd on bi.item_id = cd.item_id and bi.item_unit_id=cd.unit_id left join crm_coupons_type ct on ct.class_id=cd.type_id ");
			sql.append("where bi.tenancy_id=? and bi.store_id=? and bi.bill_num=? and bi.item_property <> ? and ct.id in(").append(typeIds.toString()).append(") and COALESCE(bi.item_remark, '') not in ('TC01', 'FS02','CJ05','QX04','MD03') ) bi order by bi.real_price desc,item_price");
			return this.queryString4Json(tenancyId, sql.toString(), new Object[]
			{ tenancyId, storeId, billNum, SysDictionary.ITEM_PROPERTY_MEALLIST });
		}
		return null;
	}
	
	@Override
	public List<JSONObject> findBill(String tenancyId, int storeId, String billNum) throws Exception
	{
		StringBuilder sql = new StringBuilder("select bill_num,bill_amount,bill_state,payment_amount,table_code,source,order_num,bill_property,payment_state from pos_bill where bill_num = '" + billNum + "' and store_id = " + storeId);
		return this.queryString4Json(tenancyId, sql.toString());
	}

	@Override
	public List<JSONObject> getPaymentCouponsByBillNum(String tenancyId, int storeId, String billNum) throws Exception
	{
		String sql = new String(
				"SELECT pbpc.*,cct.used_other FROM pos_bill_payment_coupons pbpc left join pos_bill_payment bp on pbpc.tenancy_id=bp.tenancy_id and pbpc.store_id=bp.store_id and pbpc.bill_num=bp.bill_num and pbpc.payment_id=bp.payment_uid left join crm_coupons_type cct on pbpc.class_id = cct.class_id AND pbpc.type_id = cct.id WHERE  bp.tenancy_id =? AND bp.store_id =? AND bp.bill_num =? AND pbpc.is_cancel <> '1'");
		return this.query4Json(tenancyId, sql, new Object[]
		{ tenancyId, storeId, billNum });
	}
}
