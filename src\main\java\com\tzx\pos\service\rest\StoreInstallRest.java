package com.tzx.pos.service.rest;

import java.io.File;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.sql.DataSource;

import net.sf.json.JSONObject;

import org.apache.http.HttpEntity;
import org.apache.http.HttpHeaders;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.params.CoreConnectionPNames;
import org.apache.http.params.CoreProtocolPNames;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import com.tzx.base.common.util.PosPropertyUtil;
import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.TzxPropertiesUtil;

/**
 * <AUTHOR> 机构管理
 */
@Controller("StoreInstallRest")
@RequestMapping("/pos/storeInstallRest")
public class StoreInstallRest
{
	private static final Logger	logger	= Logger.getLogger(PosRest.class);
	
	private static String		loginMethod	= "/framework/systemUserRest/userlogin";
	private static String		checkMethod	= "/framework/organContraller/findOrganByOrganCode";

	@RequestMapping(value = "/install")
	public void install(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		String result = "{\"success\": false}";

		HttpClient httpClient = new DefaultHttpClient();

		try
		{
			Map<String, String> p = new HashMap<String, String>();

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				p.put(key, map.get(key)[0]);
			}

			p.put("tenentid", p.get("tenent_id"));

			httpClient.getParams().setParameter(CoreConnectionPNames.CONNECTION_TIMEOUT, 3000);
			httpClient.getParams().setParameter(CoreProtocolPNames.HTTP_CONTENT_CHARSET, "UTF-8");
			httpClient.getParams().setParameter(HttpHeaders.CONTENT_TYPE, "application/x-www-form-urlencoded");
			HttpPost httpPost = new HttpPost(PosPropertyUtil.getMsg("saas.url") + loginMethod);

			logger.info("=====================>"+PosPropertyUtil.getMsg("saas.url") + loginMethod+"  =================>"+JSONObject.fromObject(map));
			
			List<NameValuePair> formParams = new ArrayList<NameValuePair>(); // 创建参数队列

			for (Map.Entry<String, String> entry : p.entrySet())
			{
				formParams.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
			}

			httpPost.setEntity(new UrlEncodedFormEntity(formParams, "UTF-8"));

			HttpResponse r = httpClient.execute(httpPost);

			HttpEntity entity = r.getEntity();
			String responseContent = EntityUtils.toString(entity, "UTF-8");
			EntityUtils.consume(entity);
			
			JSONObject login = null;
			try
			{
				logger.info("pos/storeInstallRest/install 总部返回==============>"+responseContent);
				login = JSONObject.fromObject(responseContent);
			}
			catch(Exception ee)
			{
				logger.info("pos/storeInstallRest/install ==============>网络连接异常！    =======>"+responseContent);
				throw new Exception("网络连接异常！");
			}
			

			if (null != login && login.optBoolean("success"))
			{
				logger.info(PosPropertyUtil.getMsg("saas.url") + checkMethod);
				
				httpPost = new HttpPost(PosPropertyUtil.getMsg("saas.url") + checkMethod);
				httpPost.setEntity(new UrlEncodedFormEntity(formParams, "UTF-8"));

				r = httpClient.execute(httpPost);

				entity = r.getEntity();

				if (null != entity)
				{

					responseContent = EntityUtils.toString(entity, "UTF-8");
					EntityUtils.consume(entity);

					if (responseContent != null)
					{
						result = "{\"success\" : true , \"store\" : " + responseContent + "}";
					}
				}
			}
			else
			{
				result = "{\"success\" : false , \"msg\" : \"用户名或密码错误\"}";
			}

		}
		catch (Exception e)
		{
			e.printStackTrace();
			result = "{\"success\" : false , \"msg\" : \"" + e.getMessage() + "\"}";
			
			logger.info("pos/storeInstallRest/install ==============>"+e);
		}
		finally
		{
			httpClient.getConnectionManager().shutdown();

			try
			{
				out = response.getWriter();
				out.println(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
				e.printStackTrace();
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}

	@RequestMapping(value = "/confirmInstall")
	public void confirmInstall(HttpServletRequest request, HttpServletResponse response)
	{
		if ("hq".equals(Constant.getSystemMap().get("sotreorhq")))
		{
			return;
		}

		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		String result = "{\"success\": false}";

		try
		{
			Map<String, String[]> map = request.getParameterMap();
			
//			String msgFilePath = request.getServletContext().getRealPath("/") + "WEB-INF" + File.separator +"classes"+ File.separator + "messageConfig.properties";
//			File msgFile = new File(msgFilePath);
//			Properties msgProperties = TzxPropertiesUtil.load(msgFile);
//			msgProperties.setProperty("qcometoboh", String.valueOf(map.get("qcometoboh")[0]));
//			msgProperties.setProperty("mqName", "log_system_"+map.get("tenent_id")[0]+"_"+map.get("store_id")[0]);
//			TzxPropertiesUtil.save(msgProperties, msgFile, "");
			
			Map<String, String> p = new HashMap<String, String>();
			for (String key : map.keySet())
			{
				p.put(key, map.get(key)[0]);
			}

			String filePath = request.getServletContext().getRealPath("/") + "config" + File.separator + "systemParam.properties";
			File file = new File(filePath);
			Properties properties = TzxPropertiesUtil.load(file);

			p.remove("tenentid");

			for (String s : p.keySet())
			{
				properties.setProperty(s, p.get(s));
			}

			StringBuilder sb = new StringBuilder();
			sb.append("update multi_tenancy set name='" + p.get("tenent_id") + "',l10n='" + p.get("tenent_id") + "',startdate=CURRENT_DATE,note='" + p.get("tenent_id") + "'");
			JdbcTemplate jdbcTemplate = new JdbcTemplate();
			jdbcTemplate.setDataSource((DataSource) SpringConext.getApplicationContext().getBean("dataSource"));
			jdbcTemplate.execute(sb.toString());

			TzxPropertiesUtil.save(properties, file, "");

			System.exit(0);
		}
		catch (Exception e)
		{
			e.printStackTrace();
			result = "{\"success\" : false , \"msg\" : \"" + e.getMessage() + "\"}";
		}
		finally
		{

			try
			{
				out = response.getWriter();
				out.println(result);
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
				e.printStackTrace();
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
}
