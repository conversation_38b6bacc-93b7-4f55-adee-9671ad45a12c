package com.tzx.member.crm.bo.imp;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.stereotype.Service;

import com.tzx.framework.common.constant.Code;
import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.entity.PrintBean;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.Tools;
import com.tzx.member.common.entity.PosCustomerOperateListEntity;
import com.tzx.member.crm.bo.CustomerCardActivationService;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.base.util.JsonUtil;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.bo.dto.PosThirdPaymentOrderEntity;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

@Service(CustomerCardActivationService.NAME)
public class CustomerCardActivationServiceImp extends CustomerServiceImp implements CustomerCardActivationService
{
//	@Autowired
//	private PosPaymentDao paymentDao;
	/**
	 * 记录开卡会员操作流水
	 */
	@Override
	public String cardActivationCustomerOperateList(String tenancyId, int storeId, JSONObject data) throws Exception{
		String cardCode = ParamUtil.getStringValueByObject(data,"card_code");//会员卡号
		String thirdCode = ParamUtil.getStringValueByObject(data,"third_code");//第三方卡号
		String customerCode = ParamUtil.getStringValueByObject(data,"customer_code");//会员编号 不传则不绑定会员
		Integer cardClassId = ParamUtil.getIntegerValueByObject(data, "card_class_id"); //卡分类id
		String payPassword = ParamUtil.getStringValueByObject(data,"pay_password"); //付款密码
		String salesman = ParamUtil.getStringValueByObject(data,"salesman"); //销售人员
 		Double deposit =  ParamUtil.getDoubleValueByObject(data, "deposit");//押金金额
 		Double salesPrice =  ParamUtil.getDoubleValueByObject(data,"sales_price");//售卡金额
 		String isOnly = ParamUtil.getStringValueByObject(data,"is_only");//是否是一次卡
 		String isPhysicalCard = ParamUtil.getStringValueByObject(data,"is_physical_card");//是否实体卡
 		String reportDateStr = ParamUtil.getStringValueByObject(data,"report_date",true,PosErrorCode.NOT_NULL_REPORT_DATE);//业务日期
 		Integer shiftId = ParamUtil.getIntegerValueByObject(data,"shift_id");//班次id
 		String chanel = ParamUtil.getStringValueByObject(data,"chanel");//渠道
 		String optNum = ParamUtil.getStringValueByObject(data,"opt_num",true,PosErrorCode.NOT_NULL_OPT_NUM);//操作人
 		String posNum = ParamUtil.getStringValueByObject(data,"pos_num");//操作机台
 		
 		Integer paymentId=ParamUtil.getIntegerValueByObject(data, "payment_id");
		if (null == paymentId && data.containsKey("payment"))
		{
			JSONObject payment = JSONObject.fromObject(data.getJSONArray("payment").get(0));
			paymentId = ParamUtil.getIntegerValueByObject(payment, "payment_id");// 付款方式ID
		}
		
		if (Tools.hv(paymentId))
		{
			String authcode = ParamUtil.getStringValueByObject(data, "credential");
			// 查询付款方式
			JSONObject payTypeJson = customerDao.getPaymentWayByID(tenancyId, storeId, paymentId);
			if (null != payTypeJson)
			{
				if (SysDictionary.PAYMENT_CLASS_SCAN_CODE_PAY.equals(payTypeJson.optString("payment_class")) && Tools.hv(authcode))
				{
					payTypeJson = thirdPaymentService.getPaymentClassByuAuthcode(tenancyId, storeId, authcode);
					paymentId = ParamUtil.getIntegerValueByObject(payTypeJson, "payment_id");
				}
			}
			else
			{
				throw SystemException.getInstance(PosErrorCode.NOT_EXIST_JZID);
			}
			data.put("payment_id", paymentId);
		}
 		
 		String thirdBillCode=ParamUtil.getStringValueByObject(data,"bill_code");
 		if("".equals(thirdBillCode) || thirdBillCode == null){
 			JSONObject billJson = new JSONObject();
			billJson.put("report_date", reportDateStr);
			List<JSONObject> reportDateList = new ArrayList<JSONObject>();
			reportDateList.add(billJson);
			Data requestData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
			requestData.setData(reportDateList);
			thirdBillCode = this.rechargePreparePost(requestData);
 		}
		
		Timestamp currentTime = DateUtil.currentTimestamp();
		PosCustomerOperateListEntity customerOperate =  new PosCustomerOperateListEntity();
		customerOperate.setTenancy_id(tenancyId);
		customerOperate.setStore_id(storeId);
		customerOperate.setThird_bill_code(thirdBillCode);
		customerOperate.setCard_code(cardCode);
		customerOperate.setThird_code(thirdCode);
		customerOperate.setCustomer_code(customerCode);
		customerOperate.setCard_class_id(cardClassId);
		customerOperate.setSales_person(salesman);
		customerOperate.setPayment_id(paymentId);
		customerOperate.setBusiness_date(DateUtil.parseDate(reportDateStr));
		customerOperate.setOperator_id(Integer.parseInt(optNum));
		customerOperate.setPos_num(posNum);
		customerOperate.setShift_id(shiftId);
		customerOperate.setOperate_time(currentTime);
		customerOperate.setChanel(chanel);
		customerOperate.setService_type(SysDictionary.SERVICE_TYPE_ACTIVATION);
		customerOperate.setOperat_type(SysDictionary.OPERAT_TYPE_FK);
		customerOperate.setDeposit(deposit);
		customerOperate.setSales_price(salesPrice);
		customerOperate.setTrade_amount(0d);
		customerOperate.setTrade_credit(0d);
		customerOperate.setIs_invoice("0");
		customerOperate.setPayment_state(SysDictionary.THIRD_PAY_STATUS_PAYING);
		customerOperate.setOperate_state(SysDictionary.CUSTOMER_OPERATE_STATE_WAIT);
		customerOperate.setCancel_state(SysDictionary.CUSTOMER_OPERATE_CANCEL_STATE_N);
		customerOperate.setRequest_status(SysDictionary.REQUEST_STATUS_ING);
		customerOperate.setLast_query_time(currentTime);
		customerOperate.setQuery_count(0);
		customerOperate.setAction_type(Type.CUSTOMER_CARD_ACTIVATION.name());
		
		JSONObject extJson = new JSONObject();
		extJson.put("pay_password", payPassword);
		extJson.put("is_only", isOnly);
		extJson.put("is_physical_card", isPhysicalCard);
		customerOperate.setExtend_param(extJson.toString());
		
		
		//PosCustomerOperateListEntity cusrOperate = customerDao.queryCustomerCardActivationStatus(tenancyId, storeId,cardCode, thirdCode,null);
		//if(cusrOperate==null){
			customerDao.insertPosCustomerOperateList(tenancyId, storeId, customerOperate);
		//}
		
		return thirdBillCode;
	}
	
	@Override
	public Data customerCardActivationPost(String tenancyId, int storeId, JSONObject data) throws Exception
	{
		JSONObject printJson = new JSONObject();
		Data resData = this.customerCardActivationPost(tenancyId, storeId, data, printJson);
		try
		{
			if (false == printJson.isEmpty())
			{
				this.printCustomerCardActivation(tenancyId, storeId, printJson.toString()); // 发卡成功打印小票
			}
		}
		catch (Exception e)
		{
			logger.error("打印小票失败", e);
			e.printStackTrace();
		}
		return resData;
	}
	/**
	 *  请求会员发卡接口
	 */
	@Override
	public Data customerCardActivationPost(String tenancyId, int storeId, JSONObject data,JSONObject printJson) throws Exception
	{
		String cardCode = ParamUtil.getStringValueByObject(data, "card_code");// 会员卡号
		String thirdCode = ParamUtil.getStringValueByObject(data, "third_code");// 第三方卡号
		String customerCode = ParamUtil.getStringValueByObject(data, "customer_code");// 会员编号 不传则不绑定会员
		String cardClassId = ParamUtil.getStringValueByObject(data, "card_class_id"); // 卡分类id
		String payPassword = ParamUtil.getStringValueByObject(data, "pay_password"); // 付款密码
		String salesman = ParamUtil.getStringValueByObject(data, "salesman"); // 销售人员
		Double deposit = ParamUtil.getDoubleValueByObject(data, "deposit");// 押金金额
		Double salesPrice = ParamUtil.getDoubleValueByObject(data, "sales_price");// 售卡金额
		String isOnly = ParamUtil.getStringValueByObject(data, "is_only");// 是否是一次卡
		String isPhysicalCard = ParamUtil.getStringValueByObject(data, "is_physical_card");// 是否实体卡
		String reportDateStr = ParamUtil.getStringValueByObject(data, "report_date");// 业务日期
		Integer shiftId = ParamUtil.getIntegerValueByObject(data, "shift_id");// 班次id
		String chanel = ParamUtil.getStringValueByObject(data, "chanel");// 渠道
		String optNum = ParamUtil.getStringValueByObject(data, "opt_num");// 操作人
		String posNum = ParamUtil.getStringValueByObject(data, "pos_num");// 操作机台
		String thirdBillCode = ParamUtil.getStringValueByObject(data, "third_bill_code");
//		Date reportDate = null;
//		if (Tools.hv(reportDateStr)){
//			reportDate = DateUtil.parseDate(reportDateStr);
//		}else{
//			throw SystemException.getInstance(PosErrorCode.NOT_NULL_REPORT_DATE);
//		}

		if (Tools.isNullOrEmpty(optNum)){
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_OPT_NUM);
		}
		
		String operator = customerDao.getEmpNameById(optNum, tenancyId, storeId);
		Timestamp currentTime = DateUtil.currentTimestamp();
		
		Data resData=new Data();
		try{
			
			JSONObject requestJson = new JSONObject();
			requestJson.put("card_code", cardCode);
			requestJson.put("third_code", thirdCode);
			requestJson.put("customer_code", customerCode);
			requestJson.put("card_class_id", cardClassId);
			requestJson.put("pay_password", payPassword);
			requestJson.put("deposit", deposit);
			requestJson.put("sales_price", salesPrice);
			requestJson.put("salesman", salesman);
			requestJson.put("is_only", isOnly);
			requestJson.put("is_physical_card", isPhysicalCard);
			requestJson.put("business_date", reportDateStr);
			requestJson.put("shift_id", shiftId);
			requestJson.put("chanel", chanel);
			requestJson.put("operator_id", optNum);
			requestJson.put("operator", operator);
			requestJson.put("pos_num", posNum);
			requestJson.put("updatetime", DateUtil.format(currentTime));
//			Integer paymentId = null;
//			Double rexchangeRate = null;
			if(data.containsKey("payment")){
				JSONObject paymentJson = JSONObject.fromObject(data.optJSONArray("payment").get(0));
				if(paymentJson.optString("payment_id")!=null && !"".equals(paymentJson.optString("payment_id"))){
//					paymentId  = paymentJson.getInt("payment_id");
					JSONObject billJson = new JSONObject();
					billJson.put("report_date", reportDateStr);
					List<JSONObject> reportDateList = new ArrayList<JSONObject>();
					reportDateList.add(billJson);
					Data requestData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
					requestData.setData(reportDateList);
					String orderNum = this.rechargePreparePost(requestData);
					paymentJson.put("bill_code", orderNum);
					
					List<JSONObject> paymentList = new ArrayList<JSONObject>();
					paymentList.add(paymentJson);
					
					requestJson.put("payment", paymentList);
				}
			}

			
			List<JSONObject> requestList = new ArrayList<JSONObject>();
			requestList.add(requestJson);
			Data requestData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
			requestData.setType(Type.CUSTOMER_CARD_ACTIVATION);
			requestData.setOper(Oper.add);
			requestData.setData(requestList);
			
		    this.commonPost(JSONObject.fromObject(requestData).toString(), resData);
			
			
			if(resData.isSuccess()){
				this.insertCrmActivationLostList(tenancyId, storeId,data,resData);//插入卡片激活/挂失表
				
				PosCustomerOperateListEntity customerOperate = customerDao.queryCustomerCardActivationStatus(tenancyId, storeId,cardCode,thirdCode , thirdBillCode);
				customerOperate.setOperate_time(currentTime);
				customerOperate.setOperate_state(SysDictionary.CUSTOMER_OPERATE_STATE_SUCCESS);//会员操作状态:成功 
				customerDao.updatePosCustomerOperateList(tenancyId, storeId, customerOperate);
//				this.printCustomerCardActivation(tenancyId,storeId,JSONObject.fromObject(data).toString()); //发卡成功打印小票
				
				printJson.put("pos_num", posNum);
				printJson.put("opt_num", optNum);
				printJson.put("card_code", cardCode);
				printJson.put("deposit", deposit);
				printJson.put("sales_price", salesPrice);
			}else{//失败
				PosCustomerOperateListEntity customerOperate = customerDao.queryCustomerCardActivationStatus(tenancyId, storeId,cardCode,thirdCode , thirdBillCode);
				customerOperate.setOperate_time(currentTime);
				customerOperate.setOperate_state(SysDictionary.CUSTOMER_OPERATE_STATE_FAIL);//会员操作状态:失败 
				customerDao.updatePosCustomerOperateList(tenancyId, storeId, customerOperate);
			}
			
			
			
			PosCustomerOperateListEntity customerOperate = customerDao.queryCustomerCardActivationStatus(tenancyId, storeId,cardCode,thirdCode , thirdBillCode);
			resData.setType(Type.CUSTOMER_CARD_ACTIVATION);
			resData.setOper(Oper.add);
			resData.setTenancy_id(customerOperate.getTenancy_id());
			resData.setStore_id(customerOperate.getStore_id());
			JSONObject responseData = new JSONObject();
			responseData.put("card_code",customerOperate.getCard_code());
			responseData.put("deposit",customerOperate.getDeposit());
			responseData.put("sales_price", customerOperate.getSales_price());
			responseData.put("operator", customerOperate.getOperator_id());
			responseData.put("updatetime", customerOperate.getOperate_time());
			responseData.put("bill_code", customerOperate.getBill_code());
			responseData.put("payment_state", customerOperate.getPayment_state());
			responseData.put("operate_state", customerOperate.getOperate_state());
			List<JSONObject> list = new ArrayList<JSONObject>();
			list.add(responseData);
			resData.setData(list);
		}catch(SystemException se)
		{
			throw se;
		}
		catch(Exception e){
			e.printStackTrace();
			logger.info("发卡失败",e);
			throw SystemException.getInstance(PosErrorCode.OPER_ERROR);
		}
		return resData;
	}
	
	
	/**
	 * 发卡状态查询
	 * @param tenancyId
	 * @param storeId
	 * @param requestData
	 * @param resData
	 * @throws Exception
	 */
	public Data customerCardActivationStatus(String tenancyId, int storeId,JSONObject paramJson) throws Exception{
	
		String card_code = paramJson.optString("card_code");
		String billCode = paramJson.optString("bill_code");
		String thirdCode = paramJson.optString("third_code");
		PosCustomerOperateListEntity customerOperate  = null;
		Data resData = new Data();
		resData.setType(Type.CUSTOMER_CARD_ACTIVATION);
		resData.setOper(Oper.find);
		resData.setTenancy_id(tenancyId);
		resData.setStore_id(storeId);
		resData.setMsg("发卡状态查询");
		if(card_code!=null && billCode!=null){
			try{
				customerOperate= customerDao.queryCustomerCardActivationStatus(tenancyId, storeId,card_code,thirdCode,billCode);
			}catch(Exception e){
				e.printStackTrace();
				resData.setCode(99);
			}
		}else{
			resData.setCode(1);
		}

		if(customerOperate!=null){
			resData.setCode(0);
			JSONObject data = new JSONObject();
			data.put("card_code",customerOperate.getCard_code());
			data.put("deposit",customerOperate.getDeposit());
			data.put("sales_price", customerOperate.getSales_price());
			data.put("operator", customerOperate.getOperator_id());
			data.put("updatetime", customerOperate.getOperate_time().toString());
			data.put("bill_code", customerOperate.getBill_code());
			data.put("payment_state", customerOperate.getPayment_state());
			data.put("operate_state", customerOperate.getOperate_state());
			List<JSONObject> list = new ArrayList<JSONObject>();
			list.add(data);
			resData.setData(list);
		}else{
			resData.setCode(3);
			JSONObject data = new JSONObject();
			data.put("card_code","");
			data.put("deposit","");
			data.put("sales_price","");
			data.put("operator", "");
			data.put("updatetime", "");
			data.put("bill_code", "");
			data.put("payment_state", "");
			data.put("operate_state", "");
			List<JSONObject> list = new ArrayList<JSONObject>();
			list.add(data);
			resData.setData(list);
		}
		return resData;
	}
	
	
	/**
	 * 插入卡片激活/挂失表
	 */
	public void insertCrmActivationLostList(String tenantId, int storeId, JSONObject param, Data resData) {

		try {

			String thirdBillCode = param.optString("third_bill_code");//
			Integer paymentId = 0;//
			String payType = null;// 付款方式类型 paymentClass
			Double rexchangeRate = 1d;
			Double payMoney = 0d;//
			
			if(param.containsKey("payment")){
				JSONObject paymentJson = JSONObject.fromObject(param.optJSONArray("payment").get(0));
				
				paymentId = paymentJson.optInt("payment_id");
				payMoney = ParamUtil.getDoubleValueByObject(paymentJson, "amount");
				
				if(null != paymentId && paymentId>0)
				{
					JSONObject paymentWayJson= customerDao.getPaymentWayByID(tenantId, storeId, paymentId);
					payType = paymentWayJson.optString("payment_class");
					rexchangeRate =paymentWayJson.optDouble("rate"); 
				}
			}
			
			// 插入卡片激活/挂失表
			StringBuilder sqlActivation = new StringBuilder(
					"insert into crm_activation_lost_list (tenancy_id,customer_id,card_id,card_code,operat_type,chanel,store_id,operator,updatetime,remark,deposit,sales_price,shift_id,operator_id,business_date,salesman) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");

			JSONObject resultObj = JSONObject.fromObject(resData.getData().get(0));
			Integer customerId = null;//
			Integer cardId = null;// 会员ID
			String cardCode = null;//
			String operatType = null;// 操作类型
			String chanel = null;//
			String operator = null;
			Timestamp updatetime = null;
			String remark = null;
			double deposit = 0d;//
			double salesPrice = 0d;//
			Integer shift_id = null;//
			Integer empId = null;///
			String businessDate = null;//
			Integer salesman = null;//
			Date business = null;//

			Integer cardClassId = null;
			String name = null;
			String mobil = null;

			String billCode = null;//

			Timestamp storeUpdatetime = DateUtil.currentTimestamp();
			Timestamp lastUpdatetime = DateUtil.currentTimestamp();
			Timestamp operateTime = DateUtil.currentTimestamp();

			Double mainTrading = 0d;
			Double rewardTrading = 0d;
			Double mainOriginal = 0d;
			Double rewardOriginal = 0d;
			Double rewardBalance = 0d;
			Double mainBalance = 0d;
			Double totalBalance = 0d;
			Double revokedTrading = 0d;
			Double commissionSalerMoney = 0d;
			Double commissionStoreMoney = 0d;
			Double invoiceBalance = 0d;
			String isInvoice = "0";
			String payNo = null;
			String billCodeOriginal = null;
			String batchNum = null;
			Integer activityId = null;

			Object[] objs = null;
			JSONArray list = resultObj.optJSONArray("crm_activation_lost_list");

			if (list == null || list.size() == 0) {

				List<JSONObject> pList = new ArrayList<JSONObject>();
				pList.add(param);

				Data pData = Data.get(tenantId, storeId, Constant.CODE_SUCCESS);
				pData.setType(Type.CUSTOMER_CARD);
				pData.setOper(Oper.find);
				pData.setData(pList);

				String reqParam = JSONObject.fromObject(pData).toString();

				this.commonPost(reqParam, resData);

				resultObj = JSONObject.fromObject(resData.getData().get(0));

				customerId = resultObj.optInt("id");
				cardId = resultObj.optInt("card_id");
				cardCode = resultObj.optString("card_code");
				operatType = resultObj.optString("operat_type");
				if (Tools.isNullOrEmpty(operatType)) {
					operatType = "01";
				}
				chanel = resultObj.optString("add_chanel");
				operator = resultObj.optString("last_operator");
				empId = Integer.parseInt(customerDao.getEmpIdByName(operator, tenantId, storeId));
				remark = resultObj.optString("remark");
				updatetime = DateUtil.currentTimestamp();
				deposit = resultObj.optDouble("deposit");
				businessDate = resultObj.getString("business_date");

				if (Tools.hv(businessDate)) {
					business = DateUtil.parseDate(businessDate);
				} else {
					throw SystemException.getInstance(PosErrorCode.NOT_NULL_REPORT_DATE);
				}
				salesman = resultObj.getInt("salesman");
				if (Double.isNaN(deposit)) {
					deposit = 0d;
				}
				salesPrice = resultObj.optDouble("sales_price");
				if (Double.isNaN(salesPrice)) {
					salesPrice = 0d;
				}
				shift_id = JSONObject.fromObject(pData.getData().get(0)).optInt("shift_id");

			} else {
				for (int i = 0; i < list.size(); i++) {
					JSONObject activation = list.getJSONObject(i);

					customerId = activation.optInt("customer_id");
					cardId = activation.optInt("card_id");
					cardCode = activation.optString("card_code");
					operatType = activation.optString("operat_type");
					chanel = activation.optString("chanel");
					operator = activation.optString("operator");
					empId = activation.optInt("operator_id");

					if (Tools.isNullOrEmpty(operatType)) {
						operatType = SysDictionary.OPERAT_TYPE_FK;
					}
					remark = activation.optString("remark");
					updatetime = DateUtil.currentTimestamp();
					deposit = activation.optDouble("deposit");
					if (Double.isNaN(deposit)) {
						deposit = 0d;
					}
					salesPrice = activation.optDouble("sales_price");
					if (Double.isNaN(salesPrice)) {
						salesPrice = 0d;
					}
					shift_id = activation.optInt("shift_id");
					businessDate = activation.getString("business_date");
					if (Tools.hv(businessDate)) {
						business = DateUtil.parseDate(businessDate);
					} else {
						throw SystemException.getInstance(PosErrorCode.NOT_NULL_REPORT_DATE);
					}

					salesman = activation.getInt("salesman");

					billCode = activation.optString("bill_code");

					lastUpdatetime = DateUtil.formatTimestamp(activation.optString("last_updatetime"));
					operateTime = DateUtil.formatTimestamp(activation.optString("operate_time"));
				}
			}

			objs = new Object[] { tenantId, customerId, cardId, cardCode, operatType, chanel, storeId, operator,
					updatetime, remark, deposit, salesPrice, shift_id, empId, business, salesman };
			customerDao.update(sqlActivation.toString(), objs);

			customerDao.insertCrmCardTradingList(tenantId, storeId, business, shift_id, empId, operator, chanel, cardId,
					cardCode, cardClassId, name, mobil, deposit, customerId, billCode, operatType, payMoney,
					thirdBillCode, billCodeOriginal, batchNum, activityId, mainTrading, rewardTrading, mainOriginal,
					rewardOriginal, revokedTrading, lastUpdatetime, storeUpdatetime, operateTime, invoiceBalance,
					totalBalance, rewardBalance, mainBalance, payType, salesman, commissionSalerMoney,
					commissionStoreMoney, isInvoice, SysDictionary.REQUEST_STATUS_COMPLETE,
					SysDictionary.THIRD_PAY_STATUS_SUCCESS, SysDictionary.CUSTOMER_OPERATE_STATE_SUCCESS);
			if (null != paymentId && paymentId > 0) {
				customerDao.insertCrmCardPaymentList(tenantId, cardId, billCode, thirdBillCode, paymentId,
						rexchangeRate, payMoney, payMoney, payNo, storeUpdatetime, lastUpdatetime);
			}
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("Exception:" + ExceptionMessage.getExceptionMessage(e));
			throw SystemException.getInstance(PosErrorCode.OPER_ERROR);
		}

	}
	
	/**
	 * 发卡成功打印小票
	 * @param printJson
	 * @param param
	 * @throws Exception
	 */
	public void printCustomerCardActivation(String tenancyId, int storeId, String param)
	{
		JSONObject data = null;
		try
		{
			data = JSONObject.fromObject(param);
		}
		catch (Exception e)
		{
			e.printStackTrace();
			logger.info("参数转换JSON对象失败");
		}

		try
		{
			String operator = customerDao.getEmpNameById(data.optString("opt_num"), tenancyId, storeId);

			/** 打印start ***/
			PrintBean pb = new PrintBean();
			pb.setTenancy_id(tenancyId);
			pb.setStore_id(storeId);
			pb.setMode("1");
			pb.setPos_num(data.optString("pos_num"));
			pb.setPrint_code(SysDictionary.PRINT_CODE_1013);
			pb.setCard_code(data.optString("card_code"));
			pb.setOperator(operator);
			pb.setUpdatetime(DateUtil.getNowDateYYDDMMHHMMSS());
			pb.setDeposit(data.getString("deposit") == null ? "" : data.getString("deposit") + "");
			pb.setSales_price(data.getString("sales_price") == null ? "" : data.getString("sales_price") + "");

			setPrint(pb, SysDictionary.OPERAT_TYPE_FK);
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		/*** 打印 end **/
	}

	
	/**
	 * 打印小票
	 * @param printJson
	 * @param param
	 * @throws Exception
	 */
	public void setPrint(PrintBean param,String type) throws Exception{
		JSONObject printJson=new JSONObject();
		printJson.put("card_code", param.getCard_code());
		printJson.put("operator", param.getOperator());
		printJson.put("updatetime", param.getUpdatetime());
		printJson.put("pos_num", param.getPos_num());

		if(type.equals(SysDictionary.OPERAT_TYPE_FK)){//发卡
			printJson.put("deposit", param.getDeposit());
			printJson.put("sales_price", param.getSales_price());
		}
		if(posPrintNewService.isNONewPrint(param.getTenancy_id(), param.getStore_id())){ // 启用新打印
			posPrintNewService.posPrintByMode(param.getTenancy_id(), param.getStore_id(), param.getPrint_code(), printJson);
		}else{
			customerDao.customerPrint(param.getTenancy_id(), param.getStore_id(), param.getPos_num(), param.getPrint_code(), param.getMode(), printJson, 1);
		}
	}
	
	//生成新的账单号和流水单号
	public String rechargePreparePost(Data param){
		String billCode = "";
		try{
			if (this.commonPostCheck(param)){
				String tenantId=param.getTenancy_id();
				int storeId = param.getStore_id();
				List<?> paramList = param.getData();
				if (paramList == null || paramList.isEmpty()){
					throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
				}
				
				JSONObject dataJson = JSONObject.fromObject(paramList.get(0));
				Date reportDate = DateUtil.parseDate(dataJson.optString("report_date"));
				try{
					// 生成新的账单号和流水单号
					JSONObject object = new JSONObject();
					object.element("store_id", storeId);
					object.element("busi_date", DateUtil.formatDate(reportDate));
					billCode = codeService.getCode(tenantId, Code.POS_THIRD_PAYMENT_ORDER_NUM, object);// 调用统一接口来实现
					if(Tools.isNullOrEmpty(billCode))
					{
						throw new SystemException(PosErrorCode.GET_BILLNUM_SERVICE_ERROR);
					}
				}catch (Exception e){
					logger.info("获取订单编号失败：" + ExceptionMessage.getExceptionMessage(e));
					throw new SystemException(PosErrorCode.GET_BILLNUM_SERVICE_ERROR);
				}
			}
		}catch (SystemException se){
			throw se;
		}catch (Exception e){
			e.printStackTrace();
			logger.info("Exception:" + ExceptionMessage.getExceptionMessage(e));
			throw SystemException.getInstance(PosErrorCode.OPER_ERROR);
		}
		return billCode;
	}

	/**
	 * 获取二维码
	 */
	public Data customerCardThirdPaymentPrecreate(Data params, String path) throws Exception{
		String tenancyId = params.getTenancy_id();
		Integer storeId = params.getStore_id();
		
		JSONObject paramJson = JSONObject.fromObject(params.getData().get(0));
		String thirdBillCode = paramJson.optString("third_bill_code");
		Data returnData = new Data();
		
		paramJson.put("order_no", thirdBillCode);
		paramJson.put("bill_num", thirdBillCode);
		paramJson.put("total_amount", paramJson.optDouble("settle_amount"));
		paramJson.put("settle_amount", paramJson.optDouble("settle_amount"));
		paramJson.put("subject", paramJson.optString("会员发卡获取二维码"));
		paramJson.put("service_type", SysDictionary.SERVICE_TYPE_ACTIVATION);
		paramJson.put("channel",paramJson.getString("chanel"));
		
		returnData = thirdPaymentService.precreatePayment(tenancyId, storeId, paramJson, path);
		PosCustomerOperateListEntity customerOperate = customerDao.queryCustomerCardActivationStatus(tenancyId, storeId,paramJson.getString("card_code"),paramJson.getString("card_code") , thirdBillCode);
		if (Constant.CODE_SUCCESS == returnData.getCode()){
			JSONObject responseJson = JSONObject.fromObject(returnData.getData().get(0));
			String status = responseJson.optString("payment_state");
			String qrCode = responseJson.optString("qrcode");
			if (!SysDictionary.THIRD_PAY_STATUS_PAYING.equals(status) || Tools.isNullOrEmpty(qrCode)){
				//修改会员操作流水表发卡失败
				
				Timestamp currentTime = DateUtil.currentTimestamp();
				customerOperate.setPayment_id(paramJson.optInt("payment_id"));
				customerOperate.setOperate_time(currentTime);
				customerOperate.setOperate_state(SysDictionary.CUSTOMER_OPERATE_STATE_FAIL);//会员操作状态:失败 
				customerOperate.setPayment_state(SysDictionary.THIRD_PAY_STATUS_FAIL);//支付失败
				
				customerDao.updatePosCustomerOperateList(tenancyId, storeId, customerOperate);
				
				PosThirdPaymentOrderEntity chargeThirdJson = customerDao.getThirdPaymentByAidOrderNum(tenancyId, storeId, thirdBillCode, SysDictionary.THIRD_PAY_CHARGE);
				returnData.setType(Type.CUSTOMER_CARD_ACTIVATION);
				returnData.setOper(Oper.precreate);
				returnData.setTenancy_id(tenancyId);
				returnData.setStore_id(storeId);
				
				String pdata = JsonUtil.DataToJson(returnData);
				JSONObject jsobj = JSONObject.fromObject(pdata);
				JSONObject responseData = JSONObject.fromObject(jsobj.getJSONArray("data").get(0));
				responseData.put("bill_code",thirdBillCode);
				responseData.put("metadata",chargeThirdJson.getMetadata());
				List<JSONObject> dataList = new ArrayList<JSONObject>();
				dataList.add(responseData);
				returnData.setData(dataList);
				returnData.setCode(PosErrorCode.CREATE_QRCODE_ERROR.getNumber());
				returnData.setMsg(PosErrorCode.CREATE_QRCODE_ERROR.getMessage());
				
			}else{
				//修改会员操作流水表发卡成功
				Timestamp currentTime = DateUtil.currentTimestamp();
				customerOperate.setThird_bill_code(thirdBillCode);
				customerOperate.setPayment_id(paramJson.optInt("payment_id"));
				customerOperate.setOperate_time(currentTime);
				customerOperate.setOperate_state(SysDictionary.CUSTOMER_OPERATE_STATE_WAIT);//会员操作状态:待处理
				customerOperate.setPayment_state(SysDictionary.THIRD_PAY_STATUS_PAYING);//处理中
				customerDao.updatePosCustomerOperateList(tenancyId, storeId, customerOperate);
				
				PosThirdPaymentOrderEntity chargeThirdJson = customerDao.getThirdPaymentByAidOrderNum(tenancyId, storeId, thirdBillCode, SysDictionary.THIRD_PAY_CHARGE);
				String pdata = JsonUtil.DataToJson(returnData);
				JSONObject jsobj = JSONObject.fromObject(pdata);
				JSONObject responseData = JSONObject.fromObject(jsobj.getJSONArray("data").get(0));
				returnData.setType(Type.CUSTOMER_CARD_ACTIVATION);
				returnData.setOper(Oper.precreate);
				returnData.setTenancy_id(tenancyId);
				returnData.setStore_id(storeId);
				
				responseData.put("bill_code",thirdBillCode);
				responseData.put("metadata",chargeThirdJson.getMetadata());
				List<JSONObject> dataList = new ArrayList<JSONObject>();
				dataList.add(responseData);
				returnData.setData(dataList);
			}
		}else{
			Timestamp currentTime = DateUtil.currentTimestamp();
			customerOperate.setThird_bill_code(thirdBillCode);
			customerOperate.setOperate_time(currentTime);
			customerOperate.setPayment_id(paramJson.optInt("payment_id"));
			customerOperate.setOperate_state(SysDictionary.CUSTOMER_OPERATE_STATE_FAIL);//会员操作状态:失败 
			customerOperate.setPayment_state(SysDictionary.THIRD_PAY_STATUS_FAIL);//支付失败
			customerDao.updatePosCustomerOperateList(tenancyId, storeId, customerOperate);
			
			
			PosThirdPaymentOrderEntity chargeThirdJson = customerDao.getThirdPaymentByAidOrderNum(tenancyId, storeId, thirdBillCode, SysDictionary.THIRD_PAY_CHARGE);
			returnData.setType(Type.CUSTOMER_CARD_ACTIVATION);
			returnData.setOper(Oper.precreate);
			returnData.setTenancy_id(tenancyId);
			returnData.setStore_id(storeId);
			
			String pdata = JsonUtil.DataToJson(returnData);
			JSONObject jsobj = JSONObject.fromObject(pdata);
			JSONObject responseData = JSONObject.fromObject(jsobj.getJSONArray("data").get(0));
			responseData.put("bill_code",thirdBillCode);
			responseData.put("metadata",chargeThirdJson.getMetadata());
			List<JSONObject> dataList = new ArrayList<JSONObject>();
			dataList.add(responseData);
			returnData.setData(dataList);
			returnData.setCode(PosErrorCode.CREATE_QRCODE_ERROR.getNumber());
			returnData.setMsg(PosErrorCode.CREATE_QRCODE_ERROR.getMessage());
			
		}
		return returnData;
	}
	
	/**
	 * 取消支付
	 * @param tenancyId
	 * @param storeId
	 * @param paramJson
	 * @param resData
	 * @param operType
	 * @return
	 * @throws Exception
	 */
	public Data customerCardCancelThirdPayment(String tenancyId, int storeId, JSONObject paramJson) throws Exception {
		String cardCode = paramJson.optString("card_code");//卡号
		String thirdCode = paramJson.optString("third_code");//第三方卡号
		String billCode = paramJson.optString("bill_code");//流水号
		Data resData  = new Data();
		resData.setType(Type.CUSTOMER_CARD_ACTIVATION);
		resData.setOper(Oper.cancle);
		resData.setTenancy_id(tenancyId);
		resData.setStore_id(storeId);
		if (Tools.isNullOrEmpty(cardCode)  || Tools.isNullOrEmpty(billCode)){
			resData.setCode(Constant.CODE_PARAM_FAILURE);
			return resData;
	
		}
		PosCustomerOperateListEntity customerOperate =null;
		PosThirdPaymentOrderEntity chargeThirdJson =null;
		customerOperate = customerDao.queryCustomerCardActivationStatus(tenancyId, storeId, cardCode, thirdCode, billCode);
		chargeThirdJson = customerDao.getThirdPaymentByAidOrderNum(tenancyId, storeId, billCode, SysDictionary.THIRD_PAY_CHARGE);
		if(customerOperate == null || chargeThirdJson==null){
			resData.setCode(Constant.CODE_NULL_DATASET);
			return resData;
		}
		if(customerOperate!=null && chargeThirdJson!=null ){
			JSONObject cancelParamJson = new JSONObject();
			cancelParamJson.put("order_no", billCode);
			cancelParamJson.put("order_num", billCode);
			cancelParamJson.put("settle_amount", chargeThirdJson.getSettle_amount());
			cancelParamJson.put("total_amount", chargeThirdJson.getTotal_amount()==null?0:chargeThirdJson.getTotal_amount());
			cancelParamJson.put("payment_id", customerOperate.getPayment_id());
			cancelParamJson.put("service_type",customerOperate.getService_type());
			cancelParamJson.put("channel", customerOperate.getChanel());
			cancelParamJson.put("report_date", DateUtil.formatDate(customerOperate.getBusiness_date()));
			cancelParamJson.put("shift_id",customerOperate.getShift_id());
			cancelParamJson.put("pos_num", customerOperate.getPos_num());
			cancelParamJson.put("opt_num", chargeThirdJson.getOpt_num());
			cancelParamJson.put("currency_name", chargeThirdJson.getCurrency_name());
			cancelParamJson.put("client_ip", chargeThirdJson.getClient_ip());
			cancelParamJson.put("refunds_order",chargeThirdJson.getTransaction_no());
			cancelParamJson.put("oper_type", Type.CUSTOMER_CARD_ACTIVATION.name());
			try{
				resData =  thirdPaymentService.cancelPayment(tenancyId, storeId, cancelParamJson);
			}catch(Exception e){
				e.printStackTrace();
				resData.setCode(Constant.CODE_CONN_EXCEPTION);
				return resData;
			}
			
		}
		if(resData.isSuccess()){
			//修改会员操作流水表发卡
			Timestamp currentTime = DateUtil.currentTimestamp();	
			customerOperate.setOperate_time(currentTime);
			customerOperate.setOperate_state(SysDictionary.CUSTOMER_OPERATE_STATE_SUCCESS);//会员操作状态:成功
			customerOperate.setPayment_state(SysDictionary.THIRD_PAY_STATUS_REVOKED_SUCCESS);
			customerDao.updatePosCustomerOperateList(tenancyId, storeId, customerOperate);
		}else{
			//修改会员操作流水表发卡
			Timestamp currentTime = DateUtil.currentTimestamp();	
			customerOperate.setOperate_time(currentTime);
			customerOperate.setOperate_state(SysDictionary.CUSTOMER_OPERATE_STATE_FAIL);//会员操作状态:失败 
			customerOperate.setPayment_state(SysDictionary.THIRD_PAY_STATUS_REVOKED_FAIL);
			customerDao.updatePosCustomerOperateList(tenancyId, storeId, customerOperate);
		}		
		return  resData;
	}

	
	/**
	 * 被扫支付
	 * 
	 * @throws Exception
	 */
	@Override
	public Data customerCardThirdPaymentBarcode(Data params) throws Exception{
		String tenancyId = params.getTenancy_id();
		Integer storeId = params.getStore_id();

		Data returnData = new Data();
		returnData.setType(Type.CUSTOMER_CARD_ACTIVATION);
		returnData.setOper(Oper.barcode);
		returnData.setTenancy_id(tenancyId);
		returnData.setStore_id(storeId);
		JSONObject paramJson = null;
		try{
			paramJson = JSONObject.fromObject(params.getData().get(0));
		}catch(Exception e){
			returnData.setCode(Constant.CODE_PARAM_FAILURE);//参数失败
			e.printStackTrace();
			return returnData;
		}
		
		String report_Date = ParamUtil.getDateStringValue(paramJson, "report_date");
		int shiftId = paramJson.optInt("shift_id");
		String posNum = paramJson.optString("pos_num");
		String optNum = paramJson.optString("opt_num");
		String cardCode=paramJson.optString("card_code");
		String thirdCode = paramJson.optString("third_code");
		String thirdBillCode = paramJson.optString("third_bill_code");
		
		paramJson.put("order_no", thirdBillCode);
		paramJson.put("bill_num", thirdBillCode);
		paramJson.put("service_type",SysDictionary.SERVICE_TYPE_ACTIVATION );
		paramJson.put("channel",paramJson.getString("chanel"));
		paramJson.put("subject", "会员卡发卡扫码支付");
		
		try{
			returnData = thirdPaymentService.barcodePayment(tenancyId, storeId, paramJson);
		}catch(Exception e){
			returnData.setCode(Constant.CODE_CONN_EXCEPTION);//连接异常
			e.printStackTrace();
			return returnData;
		}
		
		PosCustomerOperateListEntity customerOperate = customerDao.queryCustomerCardActivationStatus(tenancyId, storeId, cardCode, thirdCode, thirdBillCode);
		String paymentState = SysDictionary.THIRD_PAY_STATUS_NOOK;
		String operateState = SysDictionary.CUSTOMER_OPERATE_STATE_WAIT;
//		String requestMsg = null;
		if (Constant.CODE_SUCCESS == returnData.getCode()){
			List<?> returnDataList = returnData.getData();
			JSONObject returnJson = new JSONObject();
			
			if (null != returnDataList && returnDataList.size() > 0){
				returnJson = JSONObject.fromObject(returnDataList.get(0));
				paymentState = returnJson.optString("payment_state");
			}
			Timestamp currentTime = DateUtil.currentTimestamp();
			if(paymentState.equals(SysDictionary.THIRD_PAY_STATUS_SUCCESS)){
				//修改会员操作流水表
				customerOperate.setOperate_time(currentTime);
				customerOperate.setOperate_state(SysDictionary.CUSTOMER_OPERATE_STATE_PROCESS);//会员操作状态:处理中
				customerOperate.setPayment_state(SysDictionary.THIRD_PAY_STATUS_SUCCESS);//支付成功
				customerDao.updatePosCustomerOperateList(tenancyId, storeId, customerOperate);
				
				JSONObject paymentJson = new JSONObject();
				paymentJson.put("payment_id", paramJson.optString("payment_id"));
				paymentJson.put("amount", ParamUtil.getDoubleValueByObject(paramJson, "settle_amount").toString());
				paymentJson.put("cashier_num", ParamUtil.getStringValueByObject(paramJson, "opt_num"));
				paymentJson.put("rate", "1");
				paymentJson.put("bill_code", customerOperate.getThird_bill_code());

				List<JSONObject> paymentList = new ArrayList<JSONObject>();
				paymentList.add(paymentJson);

				JSONObject extJson = JSONObject.fromObject(customerOperate.getExtend_param());

				JSONObject paramJson2 = new JSONObject();
				paramJson2.put("card_code", customerOperate.getCard_code());
				paramJson2.put("third_code", customerOperate.getThird_code());
				paramJson2.put("customer_code", customerOperate.getCustomer_code());
				paramJson2.put("card_class_id", customerOperate.getCard_class_id());
				paramJson2.put("pay_password", extJson.optString("pay_password"));
				paramJson2.put("salesman", customerOperate.getSales_person());
				paramJson2.put("deposit", customerOperate.getDeposit());
				paramJson2.put("sales_price", customerOperate.getSales_price());
				paramJson2.put("is_only", extJson.optString("is_only"));
				paramJson2.put("is_physical_card", extJson.optString("is_physical_card"));
				paramJson2.put("report_date", DateUtil.formatDate(customerOperate.getBusiness_date()));
				paramJson2.put("shift_id", customerOperate.getShift_id());
				paramJson2.put("chanel", customerOperate.getChanel());
				paramJson2.put("opt_num", customerOperate.getOperator_id());
				paramJson2.put("pos_num", customerOperate.getPos_num());
				paramJson2.put("payment", paymentList);
				paramJson2.put("third_bill_code", thirdBillCode);
				returnData = this.customerCardActivationPost(tenancyId,storeId, paramJson2);
				if (Constant.CODE_SUCCESS == returnData.getCode())
				{
					operateState = SysDictionary.CUSTOMER_OPERATE_STATE_SUCCESS;
				}else if(returnData.getCode()==5024){
					operateState = SysDictionary.CUSTOMER_OPERATE_STATE_FAIL;
//					requestMsg = returnData.getMsg();
				}
			}else if(paymentState.equals(SysDictionary.THIRD_PAY_STATUS_FAIL)){
				returnData.setCode(Constant.CODE_INNER_EXCEPTION);
				returnData.setMsg("支付失败");
				customerOperate.setOperate_time(currentTime);
				customerOperate.setOperate_state(SysDictionary.CUSTOMER_OPERATE_STATE_FAIL);//会员操作状态:失败 
				customerOperate.setPayment_state(SysDictionary.THIRD_PAY_STATUS_FAIL);
				operateState = SysDictionary.CUSTOMER_OPERATE_STATE_FAIL;
				paymentState = SysDictionary.THIRD_PAY_STATUS_FAIL;
				customerDao.updatePosCustomerOperateList(tenancyId, storeId, customerOperate);
			}else{
				customerOperate.setOperate_time(currentTime);
				customerOperate.setOperate_state(SysDictionary.CUSTOMER_OPERATE_STATE_WAIT);//会员操作状态:处理中
				customerOperate.setPayment_state(paymentState);//
				customerOperate.setCancel_state(SysDictionary.CUSTOMER_OPERATE_CANCEL_STATE_N);//未取消
				customerOperate.setPayment_state(SysDictionary.THIRD_PAY_STATUS_FAIL);
				
				operateState = SysDictionary.CUSTOMER_OPERATE_STATE_WAIT;
				customerDao.updatePosCustomerOperateList(tenancyId, storeId, customerOperate);
			}

		}else if (Constant.CODE_CONN_EXCEPTION != returnData.getCode()){
			returnData.setCode(Constant.CODE_INNER_EXCEPTION);
			Timestamp currentTime = DateUtil.currentTimestamp();
			customerOperate.setOperate_time(currentTime);
			customerOperate.setOperate_state(SysDictionary.CUSTOMER_OPERATE_STATE_FAIL);//会员操作状态:失败 
			customerOperate.setPayment_state(SysDictionary.THIRD_PAY_STATUS_FAIL);
			operateState = SysDictionary.CUSTOMER_OPERATE_STATE_FAIL;
			paymentState = SysDictionary.THIRD_PAY_STATUS_FAIL;
			customerDao.updatePosCustomerOperateList(tenancyId, storeId, customerOperate);
		}
		
		JSONObject responseData = new JSONObject();
		responseData.put("card_code",customerOperate.getCard_code());
		responseData.put("deposit",customerOperate.getDeposit());
		responseData.put("sales_price", customerOperate.getSales_price());
		responseData.put("operator", customerOperate.getOperator_id());
		responseData.put("updatetime", customerOperate.getOperate_time());
		responseData.put("bill_code", customerOperate.getThird_bill_code());
		responseData.put("payment_state", paymentState);
		responseData.put("operate_state", operateState);
		
		List<JSONObject> dataList = new ArrayList<JSONObject>();
		dataList.add(responseData);
		returnData.setData(dataList);
		
		customerDao.savePosLog(tenancyId, storeId, posNum, optNum, optNum, shiftId, DateUtil.parseDate(report_Date), Constant.TITLE, "扫码支付", "账单编号:" + thirdBillCode, "Result:" + returnData.getMsg());
		return returnData;
	}

	@Override
	public PosCustomerOperateListEntity queryCustomerOperateListForCardActivation(String tenancyId, int storeId, JSONObject paramJson) throws Exception
	{
		// TODO Auto-generated method stub

		String card_code = paramJson.optString("card_code");
		String billCode = paramJson.optString("bill_code");
		String thirdCode = paramJson.optString("third_code");

		PosCustomerOperateListEntity customerOperate = null;
		if (card_code != null && billCode != null)
		{
			try
			{
				customerOperate = customerDao.queryCustomerCardActivationStatus(tenancyId, storeId, card_code, thirdCode, billCode);
			}
			catch (Exception e)
			{
				e.printStackTrace();
			}
		}
		return customerOperate;
	}

	@Override
	public void updateCustomerOperateListStateForCardActivation(String tenancyId, int storeId, JSONObject paramJson) throws Exception
	{
		// TODO Auto-generated method stub
		String billCode = ParamUtil.getStringValueByObject(paramJson, "bill_code");
		String paymentState = ParamUtil.getStringValueByObject(paramJson, "payment_state");
		String operateState = ParamUtil.getStringValueByObject(paramJson, "operate_state");
		String requestStatus= ParamUtil.getStringValueByObject(paramJson, "request_status");
		Integer requestCode = ParamUtil.getIntegerValueByObject(paramJson, "request_code");
		String requestMsg= ParamUtil.getStringValueByObject(paramJson, "request_msg"); 
		Timestamp finishTime = ParamUtil.getTimestampValueByObject(paramJson, "finish_time");
		
		customerDao.updatePosCustomerOperateList(tenancyId, storeId, billCode, paymentState, operateState, requestStatus, requestCode, requestMsg, finishTime, SysDictionary.OPERAT_TYPE_FK);
	}

	@Override
	public void queryCustomerCardActivationStatusForCRM(String tenancyId, int storeId, PosCustomerOperateListEntity customerOperate) throws Exception
	{
		//请求总部,查询发卡状态
		Data resultData = new Data();
		JSONObject paramJson = new JSONObject();
		paramJson.put("card_code", customerOperate.getCard_code());
		List<JSONObject> requestList = new ArrayList<JSONObject>();
		requestList.add(paramJson);
		Data requestData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
		requestData.setType(Type.CUSTOMER_CARD);
		requestData.setOper(Oper.find);
		requestData.setData(requestList);
		
		JSONObject printJson = new JSONObject();
		printJson.put("pos_num", customerOperate.getPos_num());
		printJson.put("card_code", customerOperate.getCard_code());
		printJson.put("opt_num", customerOperate.getOperator_id());
		printJson.put("deposit", customerOperate.getDeposit());
		printJson.put("sales_price", customerOperate.getSales_price());
		
		
		this.commonPost(JSONObject.fromObject(requestData).toString(), resultData);
		System.out.println(JsonUtil.DataToJson(resultData));
		List<?> resultDataList = resultData.getData();
		JSONObject resultJson = new JSONObject();
		Timestamp currentTime = DateUtil.currentTimestamp();
		if (null != resultDataList && resultDataList.size() > 0){
			resultJson = JSONObject.fromObject(resultDataList.get(0));
			if(resultJson.containsKey("card_code") && resultJson.optString("card_code") !=null &&  !"".equals(resultJson.optString("card_code"))){
				customerOperate.setOperate_time(currentTime);
				customerOperate.setOperate_state(SysDictionary.CUSTOMER_OPERATE_STATE_SUCCESS);//会员操作状态:发卡成功 
				//customerOperate.setPayment_state(SysDictionary.THIRD_PAY_STATUS_SUCCESS);
				customerDao.updatePosCustomerOperateList(tenancyId, storeId, customerOperate);
				this.printCustomerCardActivation(tenancyId,storeId,JSONObject.fromObject(printJson).toString()); //发卡成功打印小票
			}else{
				customerOperate.setOperate_time(currentTime);
				customerOperate.setOperate_state(SysDictionary.CUSTOMER_OPERATE_STATE_FAIL);//会员操作状态:发卡失败 
				//customerOperate.setPayment_state(SysDictionary.THIRD_PAY_STATUS_FAIL);
				customerDao.updatePosCustomerOperateList(tenancyId, storeId, customerOperate);
			}
		}else{
			customerOperate.setOperate_time(currentTime);
			customerOperate.setOperate_state(SysDictionary.CUSTOMER_OPERATE_STATE_FAIL);//会员操作状态:发卡失败
			//customerOperate.setPayment_state(SysDictionary.THIRD_PAY_STATUS_FAIL);
			customerDao.updatePosCustomerOperateList(tenancyId, storeId, customerOperate);
		}
		
	}
}
