package com.tzx.framework.common.util;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Vector;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.tzx.framework.common.util.DoubleHelper;

/**
 * 公共方法
 * 
 * <pre>
 * 注意：
 * 1.hv()表示是否有值
 * 2.eq()表示相等
 * 3.pe()表示打印异常（应该设置一个开关打开异常打印方便程序部署）
 * 4.t*()表示转换成相应的类型
 * 5.Double精度的计算：(1) 价格  (2)价格、数量、金额、税额
 * 6.Double精度计算用的是四舍五入
 * </pre>
 * 
 * <AUTHOR> 2010-11-29
 */
public class Scm
{

	/**
	 * 价格保留小数位
	 */
	public final static int	PRICE_SCALE				= 2;

	/**
	 * 数量和金额、税额保留小数位
	 */
	public final static int	QTY_AMOUNT_RATE_SCALE	= 4;
	/**
	 * 数量和金额、税额保留小数位
	 */
	public final static int	QTY_AMOUNT_RATE_SCALE_SIX	= 6;
	private static Pattern	dPattern				= Pattern.compile("\\d+$");		// 数字
	private static Pattern	wPattern				= Pattern.compile("[a-zA-Z]+$");	// 英文字母

	/**
	 * 是否有值 : hasValue
	 * 
	 * @param rs
	 * @return
	 * @add xiaofeng 2010-11-29
	 */
	public static boolean hv(String rs)
	{
		return rs != null && rs.length() > 0;
	}

	/**
	 * 是否有值 : hasValue
	 * 
	 * @param rs
	 * @return
	 * @add xiaofeng 2010-11-29
	 */
	public static boolean hv(Integer rs)
	{
		return rs != null && rs != 0;
	}

	/**
	 * 是否有值 : hasValue
	 * 
	 * @param rs
	 * @return
	 * @add xiaofeng 2013-03-08
	 */
	public static boolean hv(Double rs)
	{
		return rs != null && rs != 0d;
	}

	/**
	 * 是否有值 : hasValue
	 * 
	 * @param rs
	 * @return
	 * @add xiaofeng 2010-12-15
	 */
	public static boolean hv(Date rs)
	{
		return rs != null;
	}

	/**
	 * 是否有值 : hasValue
	 * 
	 * @param rs
	 * @return
	 * @add xiaofeng 2010-11-29
	 */
	public static boolean hv(Long rs)
	{
		return rs != null;
	}

	/**
	 * 是否有值 : hasValue
	 * 
	 * @param str
	 * @return
	 * @add xiaofeng 2010-11-29
	 */
	public static boolean hv(String[] str)
	{
		return str != null && str.length > 0;
	}

	/**
	 * 是否有值 : hasValue
	 * 
	 * <h1>注意：如果list的第一个元素是null那么返回false</h1>
	 * 
	 * @param list
	 * @return
	 * @add xiaofeng 2010-11-29
	 */
	@SuppressWarnings("rawtypes")
	public static boolean hv(List list)
	{
		if (list != null && list.size() > 0)
		{
			if (hv(list.get(0)))
			{
				return true;
			}
		}
		return false;
	}

	/**
	 * 是否有值 : hasValue
	 * 
	 * @param obj
	 * @return
	 * @add xiaofeng 2010-11-29
	 */
	public static boolean hv(Object obj)
	{
		return obj != null;
	}

	/**
	 * 是否有值 : hasValue
	 * 
	 * <h1>注意：该方法主要用于判断多个参数同时不为null时才用</h1>
	 * <h2> 用法:Scm.hv(obj1,obj2,obj3,...,args)</h2>
	 * 
	 * @param obj
	 *            参数1
	 * @param args
	 *            参数列表
	 * @return
	 * @add xiaofeng 2010-12-03
	 */
	public static boolean hv(Object obj, Object... args)
	{
		if (!hv(obj))
		{
			return false;
		}
		for (Object arg : args)
		{
			if (!hv(arg))
			{
				return false;
			}
		}
		return true;
	}

	/**
	 * 其中一个参数是否有值 : oneHasValue
	 * 
	 * @param args
	 *            参数列表
	 * @return
	 * @add xiaofeng 2010-12-15
	 */
	public static boolean oneHv(Object... args)
	{
		for (Object arg : args)
		{
			if (arg instanceof String)
			{ // 如果类型是字符串特殊处理
				if (hv(Scm.ts(arg)))
				{
					return true;
				}
			}
			else
			{
				if (hv(arg))
				{
					return true;
				}
			}
		}
		return false;
	}

	/**
	 * 是否相等 : equals
	 * 
	 * <h1>注意：src,dest其中一个值不为null</h1>
	 * <h2>用法:Scm.eq(null,1); Scm.eq(1,2); Scm.eq(2,null);等</h2>
	 * 
	 * @param src
	 *            源字符串
	 * @param dest
	 *            目标字符串
	 * @return
	 * @add xiaofeng 2010-12-03
	 */
	public static boolean eq(Object src, Object dest)
	{
		if (src == null && dest == null) return true;
		return hv(src) ? src.equals(dest) : dest.equals(src);
	}

	/**
	 * 转换成String : toString
	 * 
	 * @param obj
	 * @return
	 * @add xiaofeng 2010-11-29
	 */
	public static String ts(Object obj)
	{
		return hv(obj) ? String.valueOf(obj) : null;
	}

	/**
	 * 转换成String : toString
	 * 
	 * @param rs
	 * @return
	 * @add xiaofeng 2011-02-16
	 */
	public static String ts(String rs)
	{
		return rs == null ? "" : rs;
	}

	/**
	 * SQL拼接中单引号处理 : singleQuoteMark
	 * 
	 * @param rs
	 * @return
	 * @add xiaofeng 2011-02-16
	 */
	public static String sqm(String rs)
	{
		return replace(rs, "'", "''");
	}

	/**
	 * 字符串替换
	 * 
	 * 注意：不需要判断rs == null
	 * 
	 * @param rs
	 *            原字符串
	 * @param target
	 *            需要替换的内容
	 * @param replacement
	 *            替换成的内容
	 * @return
	 * @add xiaofeng 2011-02-16
	 */
	public static String replace(String rs, CharSequence target, CharSequence replacement)
	{
		return rs == null ? "" : rs.replace(target, replacement);
	}

	/**
	 * 转换成Integer : toInteger
	 * 
	 * @param obj
	 * @return
	 * @add xiaofeng 2010-11-29
	 */
	public static Integer ti(String rs)
	{
		return hv(rs) ? Integer.parseInt(rs) : null;
	}

	/**
	 * 转换成Integer : toInteger
	 * 
	 * @param rs
	 * @return
	 * @add xiaofeng 2011-1-19
	 */
	public static Integer ti(Long rs)
	{
		return hv(rs) ? rs.intValue() : null;
	}

	/**
	 * 转换成Double : toDouble
	 * 
	 * @param rs
	 * @return
	 * @add xiaofeng 2010-12-03
	 */
	public static Double td(String rs)
	{
		return hv(rs) ? Double.parseDouble(rs) : null;
	}

	/**
	 * 转换成有效的Double类型 : toAmount
	 * 
	 * @add xiaofeng 2010-12-9
	 * @param rs
	 * @return
	 */
	public static Double ta(Double rs)
	{
		return hv(rs) ? rs : 0.00;
	}

	/**
	 * 转换成Long : toLong
	 * 
	 * @param rs
	 * @return
	 * @add xiaofeng 2010-11-29
	 */
	public static Long tl(String str)
	{
		return hv(str) ? Long.parseLong(str) : null;
	}

	/**
	 * 转换成Long : toLong
	 * 
	 * @param i
	 * @return
	 * @add xiaofeng 2010-11-29
	 */
	public static Long tl(Integer i)
	{
		return hv(i) ? Long.valueOf(i) : null;
	}

	/**
	 * Exception输出 ：printStackTrace
	 * 
	 * @param e
	 * @return
	 * @add xiaofeng 2010-11-29
	 */
	public static void pe(Exception e)
	{
		e.printStackTrace();
	}

	/**
	 * 日期格式 : formatDate
	 * 
	 * @param date
	 * @return
	 * @add xiaofeng 2010-11-29
	 */
	public static String fd(Date date)
	{
		return fd(date, "yyyy-MM-dd");
	}

	/**
	 * 日期格式 : formatDate
	 * 
	 * @param date
	 * @param args
	 * @return
	 * @add xiaofeng 2010-11-29
	 */
	public static String fd(Date date, String args)
	{
		return hv(date) ? new SimpleDateFormat(args).format(date) : null;
	}

	/**
	 * 日期格式 : formatDate
	 * 
	 * @param date
	 * @param args
	 * @return
	 * @add xiaofeng 2010-11-29
	 */
	public static String fdA(Date date, String args)
	{
		// date.setHours(23);
		// date.setMinutes(59);
		// date.setSeconds(59);
		Calendar c = Calendar.getInstance();
		c.setTime(date);
		c.set(Calendar.HOUR_OF_DAY, 23);
		c.set(Calendar.MINUTE, 59);
		c.set(Calendar.SECOND, 59);
		// date.setTime(999999);
		return hv(c.getTime()) ? new SimpleDateFormat(args).format(c.getTime()) : null;
	}

	/**
	 * 得到一天中的最大时间
	 * 
	 * @param date
	 * @return
	 */
	public static Date fdA(Date date)
	{
		Calendar c = Calendar.getInstance();
		c.setTime(date);
		c.set(Calendar.HOUR_OF_DAY, 23);
		c.set(Calendar.MINUTE, 59);
		c.set(Calendar.SECOND, 59);
		// date.setTime(999999);
		return c.getTime();
	}

	/**
	 * 返回list.size() : listSize
	 * 
	 * @param list
	 * @return
	 * @add xiaofeng 2010-12-03
	 */
	@SuppressWarnings("rawtypes")
	public static Integer ls(List list)
	{
		return hv(list) ? list.size() : 0;
	}

	// Double精度 start

	/**
	 * 价格相加 : priceAdd
	 * 
	 * @add xiaofeng 2010-12-10
	 * @param v1
	 * @param v2
	 * @return
	 */
	public static Double padd(Double v1, Double v2)
	{
		return DoubleHelper.add(ta(v1), ta(v2), PRICE_SCALE);
	}

	/**
	 * 价格相减 : priceSub
	 * 
	 * @add xiaofeng 2010-12-10
	 * @param v1
	 * @param v2
	 * @return
	 */
	public static Double psub(Double v1, Double v2)
	{
		return DoubleHelper.sub(ta(v1), ta(v2), PRICE_SCALE);
	}

	/**
	 * 价格相乘 : priceMul
	 * 
	 * @add xiaofeng 2010-12-10
	 * @param v1
	 * @param v2
	 * @return
	 */
	public static Double pmui(Double v1, Double v2)
	{
		return DoubleHelper.mul(ta(v1), ta(v2), PRICE_SCALE);
	}

	/**
	 * 价格相除 : priceDiv
	 * 
	 * @add xiaofeng 2010-12-10
	 * @param v1
	 * @param v2
	 * @return
	 */
	public static Double pdiv(Double v1, Double v2)
	{
		return DoubleHelper.div(ta(v1), ta(v2), PRICE_SCALE);
	}

	/**
	 * 价格精度 : priceRound
	 * 
	 * @add xiaofeng 2010-12-10
	 * @param v1
	 * @return
	 */
	public static Double pround(Double v1)
	{
		return DoubleHelper.round(ta(v1), PRICE_SCALE);
	}

	/**
	 * 数量和金额、税额相加 : qtyAdd
	 * 
	 * @add xiaofeng 2010-12-10
	 * @param v1
	 * @param v2
	 * @return
	 */
	public static Double qadd(Double v1, Double v2)
	{
		return DoubleHelper.add(ta(v1), ta(v2), QTY_AMOUNT_RATE_SCALE);
	}

	/**
	 * 数量和金额、税额相减 : qtySub
	 * 
	 * @add xiaofeng 2010-12-10
	 * @param v1
	 * @param v2
	 * @return
	 */
	public static Double qsub(Double v1, Double v2)
	{
		return DoubleHelper.sub(ta(v1), ta(v2), QTY_AMOUNT_RATE_SCALE);
	}

	/**
	 * 数量和金额、税额相乘 : qtyMul
	 * 
	 * @add xiaofeng 2010-12-10
	 * @param v1
	 * @param v2
	 * @return
	 */
	public static Double qmui(Double v1, Double v2)
	{
		return DoubleHelper.mul(ta(v1), ta(v2), QTY_AMOUNT_RATE_SCALE);
	}

	/**
	 * 数量和金额、税额相除 : qtyDiv
	 * 
	 * @add xiaofeng 2010-12-10
	 * @param v1
	 * @param v2
	 * @return
	 */
	public static Double qdiv(Double v1, Double v2)
	{
		return DoubleHelper.div(ta(v1), ta(v2), QTY_AMOUNT_RATE_SCALE);
	}

	/**
	 * 数量和金额、税额精度 : qtyRound
	 * 
	 * @add xiaofeng 2010-12-10
	 * @param v1
	 * @return
	 */
	public static Double qround(Double v1)
	{
		return DoubleHelper.round(ta(v1), QTY_AMOUNT_RATE_SCALE);
	}
	
	public static Double xround(Double v1,int ws)
	{
		
		return DoubleHelper.round(ta(v1), ws);
	}
	
	// Double精度 end

	// DaoHelper
	/**
	 * Dao中like简写
	 * 
	 * @param rs
	 *            字符串
	 * @return "%" + rs + "%"
	 * @add xiaofeng 2010-12-15
	 */
	public static String likeStr(String rs)
	{
		return "%" + rs + "%";
	}

	/**
	 * Dao排序(降序)
	 * 
	 * @param alias
	 *            主表别名
	 * @return
	 * @add xiaofeng 2010-12-23
	 */
	public static String order(String alias)
	{
		if (hv(alias))
		{
			return " ORDER BY " + alias + ".ID DESC";
		}
		else
		{
			return " ORDER BY ID DESC";
		}
	}

	/**
	 * Dao排序(降序)
	 * 
	 * <pre>
	 * 注意：默认主表的别名为A
	 * </pre>
	 * 
	 * @return
	 * @add xiaofeng 2010-12-23
	 */
	public static String order()
	{
		return " ORDER BY A.ID DESC";
	}

	/**
	 * OR字符串链接
	 * 
	 * @param colName
	 *            列名：表名(或别名).字段名
	 * @param str
	 *            字符串数组
	 * @param vt
	 *            Vector对象
	 * @return
	 * @add xiaofeng 2010-12-28
	 */
	public static String or(String colName, String[] str, Vector<String> vt)
	{
		StringBuffer sql = new StringBuffer();
		sql.append(" AND (");
		for (int i = 0, j = str.length; i < j; i++)
		{
			if (i == 0)
			{
				sql.append(colName + "=?");
				vt.add(str[i]);
			}
			else
			{
				sql.append(" OR " + colName + "=?");
				vt.add(str[i]);
			}
		}
		sql.append(")");
		return sql.toString();
	}

	/**
	 * OR字符串链接
	 * 
	 * @param colName
	 *            列名：表名(或别名).字段名
	 * @param str
	 *            字符串数组
	 * @return
	 * @add xiaofeng 2010-12-28
	 */
	public static String or(String colName, String[] str)
	{
		StringBuffer sql = new StringBuffer();
		sql.append("(");
		for (int i = 0, j = str.length; i < j; i++)
		{
			if (i == 0)
			{
				sql.append(colName + "=" + str[i]);
			}
			else
			{
				sql.append(" OR " + colName + "=" + str[i]);
			}
		}
		sql.append(")");
		return sql.toString();
	}

	// DaoHelper

	/**
	 * 某天星期几
	 * 
	 * 注意：1:星期日 2:星期一 3:星期二 4:星期三 5:星期四 6:星期五 7:星期六
	 * 
	 * @param date
	 * @return
	 * @add xiaofeng 2010-12-25
	 */
	public static Integer getDayOfWeek(Date date)
	{
		Calendar c = Calendar.getInstance();
		c.setTime(date);
		return c.get(Calendar.DAY_OF_WEEK);
	}

	/**
	 * 判断字符串中是否包含中文
	 * 
	 * 注意 : 如果str为null返回false
	 * 
	 * @param str
	 * @return
	 * @add xiaofeng 2011-1-4
	 */
	public static boolean isChinese(String str)
	{
		return hv(str) && Pattern.compile("[\\u4e00-\\u9fa5]+").matcher(str).find();
	}

	/**
	 * 字符串首字母是否为数字
	 * 
	 * 注意 : 如果str为null返回false
	 * 
	 * @param str
	 * @return
	 * @add xiaofeng 2011-1-4
	 */
	public static boolean isNaN(String str)
	{
		return hv(str) && Character.isDigit(str.charAt(0));
	}

	/**
	 * 字符串首字母是否为字母
	 * 
	 * 注意 : 如果str为null返回false
	 * 
	 * @param str
	 * @return
	 * @add zhaohao 2017-1-19
	 */
	public static boolean isLetter(String str)
	{
		return hv(str) && Character.isLetter(str.charAt(0));
	}
	
	   public static String roundByScale(double v, int scale) {  
	        if (scale < 0) {  
	            throw new IllegalArgumentException(  
	                    "The   scale   must   be   a   positive   integer   or   zero");  
	        }  
	        if(scale == 0){  
	            return new DecimalFormat("0").format(v);  
	        }  
	        String formatStr = "0.";  
	        for(int i=0;i<scale;i++){  
	            formatStr = formatStr + "0";  
	        }  
	        return new DecimalFormat(formatStr).format(v);  
	    }  

	/**
	 * 字符串转成日期
	 * 
	 * @param str
	 *            日期字符串：例如：2011-11-11
	 * @param pattern
	 *            日期格式 例如 ： yyyy-MM-dd
	 * @return
	 * @add xiaofeng 2011-2-18
	 */
	public static Date str2Date(String str, String pattern)
	{
		if (str == null) return null;

		SimpleDateFormat simpledateformat = new SimpleDateFormat(pattern);
		ParsePosition parseposition = new ParsePosition(0);
		Date date = simpledateformat.parse(str, parseposition);
		return date;
	}

	/**
	 * 字符串转成日期
	 * 
	 * 注意：日期格式"yyyy-MM-dd"
	 * 
	 * @param str
	 *            日期字符串：例如：2011-11-11
	 * @return
	 * @add xiaofeng 2011-2-18
	 */
	public static Date str2Date(String str)
	{
		return str2Date(str, "yyyy-MM-dd");
	}

	/**
	 * 截取
	 * 
	 * <AUTHOR>
	 * @date Mar 7, 2011 9:47:19 AM
	 * @description:
	 * @param str
	 * @return
	 */
	public static String tn(String str)
	{
		if (Scm.hv(str))
		{
			if (str.contains("|"))
			{
				return str.split("\\|")[1];
			}
			else
			{
				return str;
			}
		}
		return str;
	}

	/**
	 * bool为true，返回exp1；为false，返回exp2
	 * 
	 * @param str
	 *            原字符串
	 * @param exp1
	 * @param exp2
	 * @return
	 * @add xiaofeng 2011-3-9
	 */
	public static String nvl(boolean bool, String exp1, String exp2)
	{
		return bool ? exp1 : exp2;
	}

	/**
	 * 得到月末最后一天
	 * 
	 * <AUTHOR>
	 * @date Mar 28, 2011 3:51:08 PM last day of month
	 * @description:
	 * @param date
	 * @return
	 */
	public static String ldom(Date date)
	{
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
		return fd(calendar.getTime());
	}

	/**
	 * 得到月末最后一天
	 * 
	 * <AUTHOR>
	 * @date Mar 28, 2011 3:51:08 PM first day of month
	 * @description:
	 * @param date
	 * @return
	 */
	public static String fdom(Date date)
	{
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMinimum(Calendar.DAY_OF_MONTH));
		return fd(calendar.getTime());
	}

	/**
	 * 是否数字
	 * 
	 * @param code
	 * @return
	 */
	public static boolean isDigital(String code)
	{
		Matcher dMatcher = dPattern.matcher(code);
		return dMatcher.matches();
	}

	/**
	 * 是否英文字母
	 * 
	 * @param code
	 * @return
	 */
	public static boolean isCharacter(String code)
	{
		Matcher wMatcher = wPattern.matcher(code);
		return wMatcher.matches();
	}

	@SuppressWarnings("rawtypes")
	public static String getListPropertys(Collection list)
	{
		StringBuffer buffer = new StringBuffer();
		for (Object val : list)
		{
			if (buffer.length() > 0)
			{
				buffer.append(",");
			}
			buffer.append(val);
		}
		return buffer.toString();
	}
	
	/**
	 * 取模运算  dou1%dou2
	 * @param dou1
	 * @param dou2
	 * @return
	 */
	public static double delivery(Double dou1,Double dou2){
		BigDecimal value1=new BigDecimal(dou1.toString());
		BigDecimal divisor=new BigDecimal(dou2.toString());
		BigDecimal result=null;
		result=value1.divideAndRemainder(divisor)[1];
		return result.doubleValue();
	}
	
	
	
	/******************计算后保留小数位6位*****/
	
	/**
	 * 数量和金额、税额相加 : 6位小数
	 * 
	 * @add bzm 2017-04-10
	 * @param v1
	 * @param v2
	 * @return
	 */
	public static Double qaddSix(Double v1, Double v2)
	{
		return DoubleHelper.add(ta(v1), ta(v2), QTY_AMOUNT_RATE_SCALE_SIX);
	}

	/**
	 * 数量和金额、税额相减 : 6位小数
	 * 
	 * @add bzm 2017-04-10
	 * @param v1
	 * @param v2
	 * @return
	 */
	public static Double qsubSix(Double v1, Double v2)
	{
		return DoubleHelper.sub(ta(v1), ta(v2), QTY_AMOUNT_RATE_SCALE_SIX);
	}

	/**
	 * 数量和金额、税额相乘 : 6位小数
	 * 
	 * @add bzm 2017-04-10
	 * @param v1
	 * @param v2
	 * @return
	 */
	public static Double qmuiSix(Double v1, Double v2)
	{
		return DoubleHelper.mul(ta(v1), ta(v2), QTY_AMOUNT_RATE_SCALE_SIX);
	}

	/**
	 * 数量和金额、税额相除 : 6位小数
	 * 
	 * @add bzm 2017-04-10
	 * @param v1
	 * @param v2
	 * @return
	 */
	public static Double qdivSix(Double v1, Double v2)
	{
		return DoubleHelper.div(ta(v1), ta(v2), QTY_AMOUNT_RATE_SCALE_SIX);
	}

	/**
	 * 数量和金额、税额精度 : 6位小数
	 * 
	 * @add bzm 2017-04-10
	 * @param v1
	 * @return
	 */
	public static Double qroundSix(Double v1)
	{
		return DoubleHelper.round(ta(v1), QTY_AMOUNT_RATE_SCALE_SIX);
	}
	
}
