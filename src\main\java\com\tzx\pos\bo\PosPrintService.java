package com.tzx.pos.bo;

import java.util.List;

import net.sf.json.JSONObject;

import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.SystemException;


/**
 * 
 * <AUTHOR> 2015年12月3日-下午2:55:03
 */
public interface PosPrintService
{
	String	NAME	= "com.tzx.pos.bo.imp.PosPrintServiceImp";

	/**
	 * 新厨打方法 点菜
	 * 
	 * @param tenancyId
	 * @param billno
	 * @param organId
	 * @param printType
	 *            0:厨打，1：账单，2：会员
	 * @return
	 */
	public List<Integer> orderChef(String tenancyId, String billno, Integer organId, String printType);

	/**
	 * 新厨打打印
	 * 
	 * @param tenancyId
	 * @param billno
	 * @param organId
	 */
	public void orderPrint(String tenancyId, String billno, Integer organId, List<Integer> list);

	/**
	 * 打印催菜,等叫,起菜
	 * 
	 * @param tenancyId
	 * @param billno
	 * @param organId
	 * @param oper
	 * @param rwids
	 */
	public List<Integer> orderOperChef(String tenancyId, String billno, Integer organId, String oper, String operType, String rwids);

	/**
	 * 退菜单打印
	 * 
	 * @param tenancyId
	 * @param billno
	 * @param organId
	 * @param oper
	 * @param operType
	 * @param rwids
	 * @return
	 */
	public List<Integer> orderRetreat(String tenancyId, String billno, Integer organId, String oper, String operType, String rwids);

	/**
	 * 获取单品转台打印数据
	 * 
	 * @param tenancyId
	 * @param billno
	 * @param organId
	 * @param oper
	 * @param operType
	 * @param tableCode
	 * @param tableTag
	 * @param rwids
	 * @return
	 */
	public List<Integer> orderSingleChgChef(String tenancyId, String billno, Integer organId, String oper, String operType, String tableCode, String tableTag, String rwids);

	/**
	 * 转台单
	 * 
	 * @param tenancyId
	 * @param billno
	 * @param organId
	 * @param oper
	 * @param operType
	 * @param tableTag
	 * @return
	 */
	public List<Integer> orderWholeChgChef(String tenancyId, String billno, Integer organId, String oper, String operType, String tableTag, String tableNameTag);

	/**
	 * 会员打印
	 * 
	 * @param tenantId
	 *            商户ID
	 * @param organId
	 *            机构ID
	 * @param posNum
	 *            机台号
	 * @param operator
	 *            操作人
	 * @param updatetime
	 *            操作时间
	 * @param printCode
	 *            打印类型
	 * @param cardCode
	 *            会员卡号
	 * @param billCode
	 *            交易单号
	 * @param consume_cardmoney
	 *            本次消费金额
	 * @param main_trading
	 *            本次消费主账户交易金额
	 * @param reward_trading
	 *            本次消费赠送账户交易金额
	 * @param main_balance
	 *            主账户余额
	 * @param reward_balance
	 *            赠送账户余额
	 * @param total_main
	 *            累计消费主账户
	 * @param total_reward
	 *            累计消费赠送账户
	 * @param credit
	 *            本次奖励积分
	 * @param useful_credit
	 *            可用积分
	 * @param income
	 *            充值金额
	 * @param deposit
	 *            押金金额
	 * @param sales_price
	 *            售卡金额
	 */
	public void customerPrint(String tenantId, Integer organId, String posNum, String operator, String updatetime, String printCode, String cardCode, String billCode, double consume_cardmoney, double main_trading, double reward_trading, double main_balance, double reward_balance, double total_main,
			double total_reward, double credit, double useful_credit, double income, double deposit, double sales_price, String card_class_name, String name, String mobil);

	/** 打印账单
	 * @param param
	 * @param result
	 * @throws SystemException
	 */
	public void printPosBill(Data param, Data result) throws SystemException;
	
	/** 打印账单
	 * @param param
	 * @param result
	 * @throws SystemException
	 */
	public void printPosBill(String tenantId, Integer organId,JSONObject param) throws SystemException;

	/**
	 * @param printJson
	 * @param posPrintNewServiceImp
	 * @throws Exception
	 */
	public void printPosBillForPayment(JSONObject printJson,PosPrintNewService posPrintNewService) throws Exception;

	/**
	 * @param tenantId
	 * @param organId
	 * @param param
	 * @throws SystemException
	 */
	public void customerPrint(String tenantId, Integer organId, JSONObject param)  throws SystemException;
	
	/**
	 * @param tenantId
	 * @param organId
	 * @param param
	 * @throws SystemException
	 */
	public void getBindOptNum(String tenantId, Integer organId, JSONObject param)  throws Exception;
	
}
