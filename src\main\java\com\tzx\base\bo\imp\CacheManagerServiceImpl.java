package com.tzx.base.bo.imp;

import com.tzx.base.bo.CacheManagerService;
import com.tzx.base.cache.CacheManager;
import com.tzx.base.constant.CacheTableConstant;
import com.tzx.base.po.springjdbc.dao.CacheManagerDao;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.util.Tools;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * Created by kevin on 2017-06-24.
 * 缓存业务数据
 *
 */
@Service(CacheManagerService.NAME)
public class CacheManagerServiceImpl implements CacheManagerService {

    @Resource(name = CacheManagerDao.NAME)
    private CacheManagerDao cacheManagerDao;

    /**
     * 设置需要缓存的业务数据
     * @param tenancyId
     * @param storeId
     * @throws Exception
     */
    @Override
    public void loadBusinessData(String tenancyId, int storeId) throws Exception {
        //清除缓存
        CacheManager.clearAll();
        //遍历业务数据枚举
        if(CacheTableConstant.values().length > 0) {
            for (CacheTableConstant cacheTableConstant : CacheTableConstant.values()) {
                if (!"".equals(cacheTableConstant.getSql())) {
                    List<Map<String, Object>> list = cacheManagerDao.getBusinessDataByStoreId(cacheTableConstant, tenancyId, storeId);
                    //存入缓存
                    if (!Tools.isNullOrEmpty(list)) {
                        //判断缓存是否保存为map格式
                        if (!cacheTableConstant.getIsToMap()) {
                            CacheManager.putCache(cacheTableConstant.name(), list);
                        } else {
                            //判断以哪个字段为key，注意区分联合主键,多个字段,以逗号分隔
//                            String keys = cacheTableConstant.getKey();
//                            if (!Tools.isNullOrEmpty(keys)) {
                            String[] keyArray = cacheTableConstant.getKeys();
                            if (Tools.hv(keyArray) && keyArray.length>0) {
                                Map<String, Object> cacheMap = new HashMap<String, Object>();
                                for (Map<String, Object> map : list) {
                                	
//									String[] keyArray = keys.split(",");
									String new_key = "";
									for (String key : keyArray)
									{
										if (new_key.length() > 0)
										{
											new_key += "_";
										}
										new_key += String.valueOf(map.get(key));
									}

									if (!"".equals(new_key))
									{
										cacheMap.put(new_key, map);
									}
                                	
//                                    Iterator iterator = map.entrySet().iterator();
//                                    try {
//                                        //联合主键的key
//                                        String new_key = "";
//                                        while (iterator.hasNext()) {
//                                            Map.Entry entry = (Map.Entry) iterator.next();
//                                            String key = (String) entry.getKey();
//                                            if (keys.equals(key)) {
//                                                cacheMap.put(entry.getValue().toString(), map);
//                                            }else {
//                                                if (keys.indexOf(",") != -1) {
//                                                    //联合主键的处理方式
//                                                    String [] keyArray = keys.split(",");
//                                                    for(int i = 0; i < keyArray.length; i++) {
//                                                        if(keyArray[i].equals(key)) {
//                                                            if(i > 0) {
//                                                                new_key += "_";
//                                                            }
//                                                            new_key += entry.getValue().toString();
//                                                        }
//                                                    }
//                                                }
//                                            }
//                                        }
//                                        if(!"".equals(new_key)) {
//                                            cacheMap.put(new_key, map);
//                                        }
//                                    } catch (Exception ex) {
//                                        ex.printStackTrace();
//                                    }
                                }
                                //保存缓存
                                CacheManager.putCache(cacheTableConstant.name(), cacheMap);
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 获取需要缓存的业务数据
     * @param param
     * @param type
     * @return
     * @throws Exception
     */
    @Override
    public List<Map<String, Object>> getBusinessData(Data param, CacheTableConstant type) throws Exception {
        List<Map<String, Object>> list = null;
//        try {
//            try {
//                switch (type) {
//                    case BASIC_DATA_DUTY_ORDER:
//                        // 班次
//                        jsons = cacheManagerDao.getBusinessDataByStoreId(CacheTableConstant.BASIC_DATA_DUTY_ORDER,param.getTenancy_id(), param.getStore_id());
//
//                        break;
//                    case BASIC_DATA_SERVICE_TYPE:
//                        // 服务费种
//                        jsons = cacheManagerDao.getBusinessDataByStoreId(CacheTableConstant.BASIC_DATA_SERVICE_TYPE,param.getTenancy_id(), param.getStore_id());
//                        break;
//                    case BASIC_DATA_METHOD:
//                        // 做法
//                        jsons = cacheManagerDao.getBusinessDataByStoreId(CacheTableConstant.BASIC_DATA_METHOD,param.getTenancy_id(), param.getStore_id());
//                        break;
//                    case BASIC_DATA_DISCOUNT:
//                        // 折扣方案
//                        jsons = cacheManagerDao.getBusinessDataByStoreId(CacheTableConstant.BASIC_DATA_DISCOUNT,param.getTenancy_id(), param.getStore_id());
//                        break;
//                    case BASIC_DATA_PAYMENT_WAY:
//                        // 付款方式
//                        jsons = cacheManagerDao.getBusinessDataByStoreId(CacheTableConstant.BASIC_DATA_PAYMENT_WAY,param.getTenancy_id(), param.getStore_id());
//                        break;
//                    case BASIC_DATA_DISH:
//                        // 菜品
//                        jsons = cacheManagerDao.getBusinessDataByStoreId(CacheTableConstant.BASIC_DATA_DISH,param.getTenancy_id(), param.getStore_id());
//                        break;
//                    case BASIC_DATA_UNIT:
//                        // 规格
//                        jsons = cacheManagerDao.getBusinessDataByStoreId(CacheTableConstant.BASIC_DATA_UNIT,param.getTenancy_id(), param.getStore_id());
//                        break;
//                    case BASIC_DATA_TABLE:
//                        // 桌位
//                        jsons = cacheManagerDao.getBusinessDataByStoreId(CacheTableConstant.BASIC_DATA_TABLE,param.getTenancy_id(), param.getStore_id());
//                        break;
//                    case BASIC_DATA_TIMEPRICE:
//                        // 时段特价方案
//                        jsons = cacheManagerDao.getBusinessDataByStoreId(CacheTableConstant.BASIC_DATA_TIMEPRICE,param.getTenancy_id(), param.getStore_id());
//                        break;
//                    case BASIC_DATA_COMBO_DETAILS:
//                        // 套餐明细
//                        jsons = cacheManagerDao.getBusinessDataByStoreId(CacheTableConstant.BASIC_DATA_COMBO_DETAILS,param.getTenancy_id(), param.getStore_id());
//                        break;
//                    case BASIC_DATA_ITEM_GROUP_DETAILS:
//                        // 项目组明细
//                        jsons = cacheManagerDao.getBusinessDataByStoreId(CacheTableConstant.BASIC_DATA_ITEM_GROUP_DETAILS,param.getTenancy_id(), param.getStore_id());
//                        break;
//                    case BASIC_DATA_SYS_PARAMETER:
//                        // 系统参数
//                        jsons = cacheManagerDao.getBusinessDataByStoreId(CacheTableConstant.BASIC_DATA_SYS_PARAMETER,param.getTenancy_id(), param.getStore_id());
//                        break;
//                    case BASIC_DATA_CRM_INCORPORATION_INFO:
//                        // 菜品劵
//                        jsons = cacheManagerDao.getBusinessDataByStoreId(CacheTableConstant.BASIC_DATA_CRM_INCORPORATION_INFO,param.getTenancy_id(), param.getStore_id());
//                        break;
//                    case BASIC_DATA_CRM_ITEM_VIP:
//                        // 会员价
//                        jsons = cacheManagerDao.getBusinessDataByStoreId(CacheTableConstant.BASIC_DATA_CRM_ITEM_VIP,param.getTenancy_id(), param.getStore_id());
//                        break;
//                    default:
//                        throw new SystemException(PosErrorCode.NOT_EXISTS_SYNC_DATATYPE);
//                }
//
//            } catch (Exception e) {
//                throw new SystemException(PosErrorCode.NOT_EXISTS_SYNC_DATATYPE);
//            }
//        }
//        catch (SystemException se)
//        {
//            throw se;
//        }
//        catch (Exception e)
//        {
//            e.printStackTrace();
//            throw SystemException.getInstance(PosErrorCode.POS_LOAD_BASIC_DATA_ERROR);
//        }
        return list;
    }
}
