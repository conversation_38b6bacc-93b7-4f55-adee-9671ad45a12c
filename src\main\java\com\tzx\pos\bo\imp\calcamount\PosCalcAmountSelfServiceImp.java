package com.tzx.pos.bo.imp.calcamount;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;

import com.tzx.framework.common.util.DoubleHelper;
import com.tzx.framework.common.util.Scm;
import com.tzx.framework.common.util.Tools;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.bo.PosCalcAmountService;
import com.tzx.pos.po.springjdbc.dao.PosBaseDao;

@Service("com.tzx.pos.bo.imp.calcamount.PosCalcAmountSelfServiceImp")
public class PosCalcAmountSelfServiceImp implements PosCalcAmountService
{

	@Resource(name = PosBaseDao.NAME)
	private PosBaseDao	baseDao;

    @Override
    public void calcAmount(String tenantId, Integer storeId, String billNum)throws Exception {
        calcAmount(tenantId,storeId,billNum,null,null);
    }
    @Override
    public void calcAmount(String tenantId, Integer storeId, String billNum,Integer dishScale,Integer billScale) throws Exception
	{

		if (Tools.isNullOrEmpty(billNum))
		{
			return;
		}

		dishScale = 2;
		if (Constant.constantMap.containsKey("CMJEWS"))
		{
			dishScale = Integer.parseInt(Constant.constantMap.get("CMJEWS"));
		}
		billScale = 2;
		if (Constant.constantMap.containsKey("ZDJEWS"))
		{
			billScale = Integer.parseInt(Constant.constantMap.get("ZDJEWS"));
		}
		final int defaultScale = 4;

		/**
		 * 计算账单明细中菜目金额
		 */
		updateItemAmount(tenantId, storeId, billNum, dishScale);

		/**
		 * 计算账单菜目合计
		 */
		// String usubtotal = new
		// String("update pos_bill set subtotal = (select sum(pbi.item_amount) from pos_bill_item pbi where pbi.bill_num = ? and pbi.store_id = ? and pbi.item_property <> 'MEALLIST') where bill_num = ? and store_id = ?");
		// baseDao.update(usubtotal, new Object[]
		// { billNum, storeId, billNum, storeId });

		String billsql = new String(
				"select guest,service_id,discount_mode_id,discount_rate,discount_case_id,discountr_amount,batch_num,source,service_amount,(select coalesce(sum(pbi.item_amount),0) from pos_bill_item pbi where pbi.tenancy_id = pb.tenancy_id and pbi.store_id = pb.store_id and pbi.bill_num = pb.bill_num and pbi.batch_num=pb.batch_num and pbi.item_property <> ?) as subtotal from pos_bill_batch pb where tenancy_id =? and store_id = ? and bill_num = ?");
		SqlRowSet rsBill = baseDao.query4SqlRowSet(billsql, new Object[]
		{ SysDictionary.ITEM_PROPERTY_MEALLIST, tenantId, storeId, billNum });

		Double discountRate = null;
		Double discountrAmount = null;
		Double subtotal = null;
		Double guest = null;
		Integer serviceId = null;
		Integer discountModeId = null;
		Integer discountCaseId = null;
		String batchNum = null;
		String channel = null;
		double serviceAmount = 0d;

		while (rsBill.next())
		{
			guest = rsBill.getDouble("guest");
			subtotal = rsBill.getDouble("subtotal");
			discountRate = rsBill.getDouble("discount_rate");
			discountCaseId = rsBill.getInt("discount_case_id");
			discountrAmount = rsBill.getDouble("discountr_amount");
			serviceId = rsBill.getInt("service_id");
			discountModeId = rsBill.getInt("discount_mode_id");
			batchNum = rsBill.getString("batch_num");
			channel = rsBill.getString("source");
			serviceAmount = rsBill.getDouble("service_amount");

			/**
			 * 判断菜目合计大于0,
			 */
			if (subtotal != null && subtotal == 0d)
			{
				// String updateB = new String(
				// "update pos_bill set service_id=null,service_amount=null,discountk_amount=null,discountr_amount=null,maling_amount=null,single_discount_amount=null,discount_amount=null,free_amount=null,givi_amount=null,more_coupon=null,average_amount=null,discount_case_id=null,discount_rate=null where bill_num = ? and store_id = ?");
				// baseDao.update(updateB, new Object[]
				// { billNum, storeId });
				discountRate = null;
				discountCaseId = null;
				discountrAmount = null;
				serviceId = null;
			}

			/**
			 * 计算折扣金额 奉送菜品折扣金额为0
			 */
			if (discountRate == null || discountRate == 0d)
			{
				discountRate = 100d;
			}

			switch (discountModeId)
			{
				case 1:// 1固定折扣
				case 2:// 2非固定折扣
				case 3:// 3折扣折让
					/**
					 * 根据discount_case_id查找折扣类库,获得折扣类型和折扣率
					 */
					StringBuilder casestr = new StringBuilder("select hdc.discount_case_type,hdc.rate_renate from hq_discount_case as hdc left join hq_discount_case_org hdco on hdc.id = hdco.discount_case_id where hdc.id = ? and hdco.store_id = ? group by hdc.discount_case_type,hdc.rate_renate");
					SqlRowSet rsCase = baseDao.query4SqlRowSet(casestr.toString(), new Object[]
					{ discountCaseId, storeId });

					String discount_case_type = "";// 折扣类型
					double rate_renate = 0;
					if (rsCase.next())
					{
						discount_case_type = rsCase.getString("discount_case_type");
						rate_renate = rsCase.getDouble("rate_renate");
					}

					switch (discount_case_type)
					{
						case "GD01": // 固定折扣
							// 更新折扣率
							StringBuilder ucase = new StringBuilder("update pos_bill_batch set discount_rate = ? where bill_num = ? and store_id = ? ");
							baseDao.update(ucase.toString(), new Object[]
							{ rate_renate, billNum, storeId });
							// 更新账单明细折扣率
							updateDiscountRateAmount(tenantId, storeId, billNum, batchNum, discountRate, true, dishScale);
							break;
						case "FGD02": // 非固定折扣
							// hq_discount_case_details中取该菜品+规格定位的那个菜品的折扣率(rate)和减免金额
							StringBuilder rateStr = new StringBuilder();
							rateStr.append(" select coalesce(cd.rate,0) as rate,coalesce(cd.derate,0) as derate,bi.id,bi.item_id,bi.setmeal_id,bi.item_serial,bi.setmeal_rwid,bi.item_property,bi.item_remark,bi.discount_state,bi.item_amount,bi.item_price,bi.assist_money,bi.method_money,bi.item_count from pos_bill_item bi");
							rateStr.append(" left join hq_discount_case_details cd on bi.item_id=cd.item_id and bi.item_unit_name=cd.unit");
							rateStr.append(" left join hq_discount_case_org co on cd.discount_case_id=co.discount_case_id");
							rateStr.append(" where bi.bill_num=? and cd.discount_case_id =? and co.store_id=?");

							List<JSONObject> itemList = baseDao.query4Json(tenantId, rateStr.toString(), new Object[]
							{ billNum, discountCaseId, storeId });

							List<Object[]> batchArgs = new ArrayList<Object[]>();
							for (JSONObject item : itemList)
							{
								if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.optString("item_property")))
								{
									if ("Y".equalsIgnoreCase(item.optString("discount_state")))
									{
										if (item.optDouble("rate") != 0d && item.optDouble("rate") != 100d)
										{
											batchArgs.addAll(getDiscountRateAmount(item, itemList, item.optDouble("rate"), dishScale));
										}
										else if (item.optDouble("derate") > 0d)
										{
											batchArgs.addAll(getDiscountDerateAmount(item, itemList, dishScale));
										}
										else
										{
											batchArgs.addAll(getDiscountRateAmount(item, itemList, 100d, dishScale));
										}
									}
									else
									{
										batchArgs.addAll(getDiscountRateAmount(item, itemList, 100d, dishScale));
									}
								}
							}
							if (batchArgs.size() > 0)
							{
								baseDao.batchUpdate("update pos_bill_item set discount_rate=?,discount_amount=? where id = ?", batchArgs);
							}
							break;
						case "ZR03": // 折让
							StringBuffer sqlUpdate = new StringBuffer("update pos_bill_batch set discountr_amount = ? where bill_num = ? and store_id = ?");
							discountrAmount = rate_renate;
							baseDao.update(sqlUpdate.toString(), new Object[]
							{ discountrAmount, billNum, storeId });
							break;
						default:
							StringBuilder udcase = new StringBuilder("update pos_bill_item set discount_mode_id=?,discount_rate=?,discount_amount=? where tenancy_id=? and store_id = ? and bill_num = ? and batch_num=?");
							baseDao.update(udcase.toString(), new Object[]
							{ null, 100d, 0d, tenantId, storeId, billNum, batchNum });
							break;
					}
					break;
				case 4:// 4团体会员折扣
				case 5:// 5会员折扣
						// 更新账单表折扣率
					StringBuilder ucase = new StringBuilder("update pos_bill_batch set discount_rate = ? where bill_num = ? and store_id = ? ");
					baseDao.update(ucase.toString(), new Object[]
					{ discountRate, billNum, storeId });
					// 更新账单明细折扣率
					updateDiscountRateAmount(tenantId, storeId, billNum, batchNum, discountRate, false, dishScale);
					break;
				case 6:// 6会员价
					updateDiscountkVipPriceAmount(tenantId, storeId, billNum, batchNum, dishScale);
					break;
				default:
					/**
					 * 取消折扣 更新每个pos_bill_item 账单折扣
					 */
					StringBuffer sqlUpdate = new StringBuffer("update pos_bill_batch set discount_mode_id=?,discount_case_id=?,discount_num=?,discount_rate = ?,discountk_amount = ? where bill_num = ? and store_id = ?");
					baseDao.update(sqlUpdate.toString(), new Object[]
					{ null, null, null, 100d, 0d, billNum, storeId });

					StringBuffer sqlUpdateItem = new StringBuffer("update pos_bill_item set discount_mode_id=?,discount_rate=?,discount_amount=? where tenancy_id=? and store_id = ? and bill_num = ? and batch_num=?");
					baseDao.update(sqlUpdateItem.toString(), new Object[]
					{ null, 100d, 0d, tenantId, storeId, billNum, batchNum });
					break;
			}

			/**
			 * 计算折让金额 奉送菜品折让金额为0 退菜菜品折让金额为0
			 */
			StringBuilder updateItemDiscountSql = new StringBuilder("update pos_bill_item set discountr_amount = 0 where tenancy_id=? and store_id = ? and bill_num = ? and batch_num=?");
			baseDao.update(updateItemDiscountSql.toString(), new Object[]
			{ tenantId, storeId, billNum, batchNum });

			if (Tools.hv(discountrAmount) && discountrAmount > 0)
			{
				updateDiscountrAmount(tenantId, storeId, billNum, batchNum, discountrAmount, dishScale);
			}
			else
			{
				discountrAmount = 0d;
			}

			/**
			 * 计算服务费
			 */

			if (Tools.isNullOrEmpty(serviceId) == false && serviceId != 0 && SysDictionary.CHANEL_MD01.equals(channel))
			{
				serviceAmount = 0d;
				double zuidi_xfe = 0d;
				double guding_jj = 0d;
				double fwfl = 0d;
				// double subtotal = 0d;
				String takenMode = "";

				StringBuilder sqls = new StringBuilder("select coalesce(zuidi_xfe,0) as zuidi_xfe,coalesce(guding_jj,0) as guding_jj,coalesce(fwfl,0) as fwfl,taken_mode,fee_type from hq_service_fee_type where id = ?");
				SqlRowSet rss = baseDao.query4SqlRowSet(sqls.toString(), new Object[]
				{ serviceId });
				while (rss.next())
				{
					zuidi_xfe = rss.getDouble("zuidi_xfe");
					guding_jj = rss.getDouble("guding_jj");
					fwfl = rss.getDouble("fwfl");
					takenMode = rss.getString("taken_mode");
				}

				if (zuidi_xfe > 0 && DoubleHelper.sub(zuidi_xfe, subtotal, defaultScale) > 0)
				{
					serviceAmount = DoubleHelper.sub(zuidi_xfe, subtotal, defaultScale);
				}

				if ("GD01".equals(takenMode))
				{
					serviceAmount = DoubleHelper.add(serviceAmount, guding_jj, billScale);
				}
				else if ("BL02".equals(takenMode))
				{
					serviceAmount = DoubleHelper.add(serviceAmount, Scm.pmui(subtotal, Scm.pdiv(fwfl, 100d)), billScale);
				}
				else if ("FS03".equals(takenMode))
				{
					StringBuilder itemSql = new StringBuilder("select coalesce(sum(item_count),0) as item_count from pos_bill_item where tenancy_id=? and store_id = ? and bill_num = ? and batch_num=?");
					SqlRowSet rsItem = baseDao.query4SqlRowSet(itemSql.toString(), new Object[]
					{ tenantId, storeId, billNum, batchNum });
					if (rsItem.next())
					{
						serviceAmount = DoubleHelper.add(serviceAmount, Scm.pmui(guding_jj, rsItem.getDouble("item_count")), billScale);
					}
				}
			}

			/**
			 * 统计折扣金额
			 */
			double discountkAmount = 0d;
			StringBuilder countBillItemSql = new StringBuilder("select coalesce(sum(bi.discount_amount),0) as discount_amount from pos_bill_item bi where bi.item_property <> 'MEALLIST' and bi.tenancy_id=? and bi.store_id = ? and bi.bill_num = ? and bi.batch_num=?");
			SqlRowSet rs = baseDao.query4SqlRowSet(countBillItemSql.toString(), new Object[]
			{ tenantId, storeId, billNum, batchNum });
			if (rs.next())
			{
				discountkAmount = rs.getDouble("discount_amount");
			}

			/**
			 * 计算奉送金额
			 */
			double giviAmount = 0d;
			StringBuilder countGiveAmountSql = new StringBuilder("select coalesce(sum(bi.item_amount),0) as givi_amount from pos_bill_item bi where bi.item_remark = 'FS02' and bi.item_property <> 'MEALLIST' and bi.tenancy_id=? and bi.store_id = ? and bi.bill_num = ? and bi.batch_num=?");
			rs = baseDao.query4SqlRowSet(countGiveAmountSql.toString(), new Object[]
			{ tenantId, storeId, billNum, batchNum });
			if (rs.next())
			{
				giviAmount = rs.getDouble("givi_amount");
			}

			// 账单金额 = 项目小计金额 + 服务费金额
			double billAmount = DoubleHelper.add(subtotal, serviceAmount, defaultScale);

			// 优惠金额 = 折扣金额 + 折让金额
			double discountAmount = DoubleHelper.add(discountrAmount, discountkAmount, defaultScale);

			// 进位前付款金额 = sum(账单明细.实付金额)+服务费 = 账单金额-优惠金额 - 奉送金额
			double paymentAmount = DoubleHelper.sub(billAmount, DoubleHelper.add(discountAmount, giviAmount, defaultScale), defaultScale);

			if (paymentAmount < 0)
			{
				paymentAmount = 0d;
			}
			// 根据账单小数取舍方式计算进位后付款金额
			String billPointType = baseDao.getSysParameter(tenantId, storeId, "billpointtype");
			if (Tools.isNullOrEmpty(billPointType))
			{
				billPointType = "half_adjust";
			}

			double paymentAmount2 = 0d;
			switch (billPointType)
			{
				case "rounding":
					paymentAmount2 = DoubleHelper.roundDown(paymentAmount, billScale);
					break;
				case "one_adjust":
					paymentAmount2 = DoubleHelper.roundUp(paymentAmount, billScale);
					break;
				default:
					paymentAmount2 = DoubleHelper.round(paymentAmount, billScale);
					break;
			}

			// 抹零金额 = 进位后付款金额-进位前付款金额
			double malingAmount = DoubleHelper.sub(paymentAmount2, paymentAmount, dishScale);

			// 人均消费 = 付款金额/顾客数
			double averageAmount = DoubleHelper.div(paymentAmount2, guest, defaultScale);

			// double paymentAmount2 = 0d;
			// double malingAmount = 0d;
			// double averageAmount = 0d;

			StringBuilder updateBillSql = new StringBuilder(
					"update pos_bill_batch set subtotal=?,service_amount=?,bill_amount=?,payment_amount=?,maling_amount=?,discountk_amount=?,discountr_amount=?,discount_amount=?,givi_amount=?,average_amount=? where tenancy_id=? and store_id = ? and bill_num = ? and batch_num=?");
			baseDao.update(updateBillSql.toString(), new Object[]
			{ subtotal, serviceAmount, billAmount, paymentAmount2, malingAmount, discountkAmount, discountrAmount, discountAmount, giviAmount, averageAmount, tenantId, storeId, billNum, batchNum });

			/**
			 * 计算抹零平摊金额 奉送菜品抹零平摊金额为0 退菜菜品抹零平摊金额为0
			 */
			if (Tools.hv(malingAmount) && malingAmount != 0d)
			{
				updateScrapAmount(tenantId, storeId, billNum, batchNum, malingAmount);
			}

			StringBuilder queryBillItemSql = new StringBuilder("select id,item_amount,discount_amount,single_discount_amount,discountr_amount,item_remark from pos_bill_item where tenancy_id=? and store_id = ? and bill_num = ? and batch_num=?");
			rs = baseDao.query4SqlRowSet(queryBillItemSql.toString(), new Object[]
			{ tenantId, storeId, billNum, batchNum });

			List<Object[]> batchItem = new ArrayList<Object[]>();
			while (rs.next())
			{
				// 实付金额=菜目金额+单品折扣-(折扣金额+折让金额)
				double realAmount = DoubleHelper.sub(DoubleHelper.add(rs.getDouble("item_amount"), rs.getDouble("single_discount_amount"), defaultScale), DoubleHelper.add(rs.getDouble("discount_amount"), rs.getDouble("discountr_amount"), defaultScale), defaultScale);

				if (SysDictionary.ITEM_REMARK_FS02.equalsIgnoreCase(rs.getString("item_remark")))
				{
					realAmount = 0d;
				}
				batchItem.add(new Object[]
				{ DoubleHelper.round(realAmount, dishScale), rs.getInt("id") });
			}
			if (batchItem.size() > 0)
			{
				baseDao.batchUpdate("update pos_bill_item set real_amount=? where id = ?", batchItem);
			}

			// 修改差额
			StringBuilder queryBillAmountSql = new StringBuilder(
					"select b.payment_amount,coalesce(sum(p.currency_amount),0) as currency_amount,coalesce(sum(p.more_coupon),0) as more_coupon from pos_bill_batch b left join pos_bill_payment p on b.store_id=p.store_id and b.bill_num=p.bill_num and b.batch_num=p.batch_num where b.tenancy_id=? and b.store_id=? and b.bill_num=? and b.batch_num=? group by b.bill_num,b.payment_amount");
			rs = baseDao.query4SqlRowSet(queryBillAmountSql.toString(), new Object[]
			{ tenantId, storeId, billNum, batchNum });
			double difference = 0d;
			if (rs.next())
			{
				difference = DoubleHelper.sub(rs.getDouble("payment_amount"), DoubleHelper.sub(rs.getDouble("currency_amount"), rs.getDouble("more_coupon"), 4), 4);
			}

			if (Tools.hv(difference))
			{
				String updateSql = new String("update pos_bill_batch set difference=? where tenancy_id=? and store_id = ? and bill_num = ? and batch_num=?");
				baseDao.update(updateSql, new Object[]
				{ difference, tenantId, storeId, billNum, batchNum });
			}
		}

		StringBuilder queryBillSql = new StringBuilder("select * from pos_bill where tenancy_id=? and store_id=? and bill_num=?");
		SqlRowSet rsGuest = baseDao.query4SqlRowSet(queryBillSql.toString(), new Object[]
		{ tenantId, storeId, billNum });
		double billGuest = 1d;
		if (rsGuest.next())
		{
			billGuest = rsGuest.getDouble("guest");
		}

		// StringBuilder queryBillBatchSql = new
		// StringBuilder("select * from pos_bill_batch where tenancy_id=? and store_id = ? and bill_num = ?");
		// List<JSONObject> batchList =
		// baseDao.query4Json(tenantId,queryBillBatchSql.toString(), new
		// Object[]{tenantId, storeId, billNum ,batchNum});
		//
		double bill_service_amount = 0d;
		double bill_subtotal = 0d;
		double bill_bill_amount = 0d;
		double bill_discountk_amount = 0d;
		double bill_discountr_amount = 0d;
		double bill_single_discount_amount = 0d;
		double bill_discount_amount = 0d;
		double bill_free_amount = 0d;
		double bill_givi_amount = 0d;
		double bill_more_coupon = 0d;
		double bill_integraloffset = 0d;
		double bill_return_amount = 0d;
		double bill_payment_amount = 0d;
		double bill_difference = 0d;
		double bill_maling_amount = 0d;
		double bill_average_amount = 0d;

		StringBuilder queryBillBatchSql = new StringBuilder("select");
		queryBillBatchSql.append(" coalesce(sum(service_amount),0) as service_amount,");
		queryBillBatchSql.append(" coalesce(sum(subtotal),0) as subtotal,");
		queryBillBatchSql.append(" coalesce(sum(bill_amount),0) as bill_amount,");
		queryBillBatchSql.append(" coalesce(sum(payment_amount),0) as payment_amount,");
		queryBillBatchSql.append(" coalesce(sum(difference),0) as difference,");
		queryBillBatchSql.append(" coalesce(sum(discountk_amount),0) as discountk_amount,");
		queryBillBatchSql.append(" coalesce(sum(discountr_amount),0) as discountr_amount,");
		queryBillBatchSql.append(" coalesce(sum(maling_amount),0) as maling_amount,");
		queryBillBatchSql.append(" coalesce(sum(single_discount_amount),0) as single_discount_amount,");
		queryBillBatchSql.append(" coalesce(sum(discount_amount),0) as discount_amount,");
		queryBillBatchSql.append(" coalesce(sum(free_amount),0) as free_amount,");
		queryBillBatchSql.append(" coalesce(sum(givi_amount),0) as givi_amount,");
		queryBillBatchSql.append(" coalesce(sum(more_coupon),0) as more_coupon,");
		queryBillBatchSql.append(" coalesce(sum(integraloffset),0) as integraloffset,");
		queryBillBatchSql.append(" coalesce(sum(return_amount),0) as return_amount");
		queryBillBatchSql.append(" from pos_bill_batch where tenancy_id=? and store_id = ? and bill_num = ?");
		SqlRowSet rsBatch = baseDao.query4SqlRowSet(queryBillBatchSql.toString(), new Object[]
		{ tenantId, storeId, billNum });

		if (rsBatch.next())
		{
			bill_service_amount = rsBatch.getDouble("service_amount");
			bill_subtotal = rsBatch.getDouble("subtotal");
			bill_bill_amount = rsBatch.getDouble("bill_amount");
			bill_discountk_amount = rsBatch.getDouble("discountk_amount");
			bill_discountr_amount = rsBatch.getDouble("discountr_amount");
			bill_single_discount_amount = rsBatch.getDouble("single_discount_amount");
			bill_discount_amount = rsBatch.getDouble("discount_amount");
			bill_free_amount = rsBatch.getDouble("free_amount");
			bill_givi_amount = rsBatch.getDouble("givi_amount");
			bill_more_coupon = rsBatch.getDouble("more_coupon");
			bill_integraloffset = rsBatch.getDouble("integraloffset");
			bill_return_amount = rsBatch.getDouble("return_amount");
			bill_payment_amount = rsBatch.getDouble("payment_amount");
			bill_difference = rsBatch.getDouble("difference");
			bill_maling_amount = rsBatch.getDouble("maling_amount");
			bill_average_amount = DoubleHelper.div(bill_payment_amount, billGuest, defaultScale);
		}
		StringBuilder updateBillSql = new StringBuilder(
				"update pos_bill set service_amount=?,subtotal=?,bill_amount=?,payment_amount=?,discountk_amount=?,discountr_amount=?,maling_amount=?,single_discount_amount=?,discount_amount=?,free_amount=?,givi_amount=?,more_coupon=?,integraloffset=?,return_amount=?,difference=?,average_amount=? where tenancy_id=? and store_id = ? and bill_num = ?");

		baseDao.update(updateBillSql.toString(), new Object[]
		{ bill_service_amount, bill_subtotal, bill_bill_amount, bill_payment_amount, bill_discountk_amount, bill_discountr_amount, bill_maling_amount, bill_single_discount_amount, bill_discount_amount, bill_free_amount, bill_givi_amount, bill_more_coupon, bill_integraloffset, bill_return_amount,
				bill_difference, bill_average_amount, tenantId, storeId, billNum });
	}

	/**
	 * 修改账单明细菜目金额,及做法金额
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @param scale
	 * @throws Exception
	 */
	private void updateItemAmount(String tenantId, int storeId, String billNum, int scale) throws Exception
	{
		final int defaultScale = 4;

		StringBuilder queryBillItemSql = new StringBuilder();
		queryBillItemSql
				.append(" select coalesce(zi.method_amount,0) as method_amount,coalesce(gd.makeup_money,0) as assist_amount,coalesce(bi.assist_num,0) as assist_num, bi.id,bi.item_id,bi.setmeal_id,bi.item_serial,bi.setmeal_rwid,bi.item_property,bi.item_remark,bi.item_price,bi.assist_money,bi.method_money,bi.item_count,bi.third_price from pos_bill_item bi");
		queryBillItemSql
				.append(" left join (select gd.*,cd.id as details_id from hq_item_group_details gd left join hq_item_combo_details cd on gd.item_group_id = cd.details_id and cd.is_itemgroup='Y') gd on bi.assist_item_id=gd.details_id  and gd.item_id=bi.item_id and gd.item_unit_id=bi.item_unit_id");
		queryBillItemSql.append(" left join (select bill_num,rwid,coalesce(sum(amount),0) as method_amount from pos_zfkw_item group by bill_num,rwid) zi on bi.bill_num = zi.bill_num and bi.rwid=zi.rwid");
		queryBillItemSql.append(" where bi.bill_num=? and bi.store_id = ?");
		// queryBillItemSql.append(" group by bi.id,bi.item_id,bi.setmeal_id,bi.item_serial,bi.setmeal_rwid,bi.item_property,bi.item_remark,bi.item_price,bi.assist_money,bi.method_money,bi.item_count,bi.third_price");

		List<JSONObject> itemList = baseDao.query4Json(tenantId, queryBillItemSql.toString(), new Object[]
		{ billNum, storeId });
		List<Object[]> batchArgs = new ArrayList<Object[]>();
		for (JSONObject item : itemList)
		{
			if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.optString("item_property")))
			{
				double itemPrice = item.optDouble("item_price"); // 单价
				double methodMoney = DoubleHelper.mul(item.optDouble("method_amount"), item.optDouble("item_count"), scale);
				double assistMoney = DoubleHelper.mul(item.optDouble("assist_amount"), item.optDouble("item_count"), scale);
				if (SysDictionary.ITEM_PROPERTY_SETMEAL.equalsIgnoreCase(item.optString("item_property")))
				{
					methodMoney = 0d;
					assistMoney = 0d;
					for (JSONObject detail : itemList)
					{
						if (SysDictionary.ITEM_PROPERTY_MEALLIST.equals(detail.optString("item_property")) && item.optInt("item_id") == detail.optInt("setmeal_id") && item.optInt("item_serial") == detail.optInt("setmeal_rwid") && item.optString("item_remark").equals(detail.optString("item_remark")))
						{
							double detailMethodMoney = DoubleHelper.mul(detail.optDouble("method_amount"), detail.optDouble("item_count"), scale);
							methodMoney = DoubleHelper.add(methodMoney, detailMethodMoney, scale);

							double detailAssistMoney = DoubleHelper.mul(detail.optDouble("assist_amount"), detail.optDouble("item_count"), scale);
							assistMoney = DoubleHelper.add(assistMoney, detailAssistMoney, scale);

							double assistNum = 1d;
							if (detail.containsKey("assist_num") && detail.optDouble("assist_num") > 1)
							{
								assistNum = detail.optDouble("assist_num");
							}

							double itemAmount = DoubleHelper.add(DoubleHelper.mul(detail.optDouble("item_price"), DoubleHelper.div(detail.optDouble("item_count"), assistNum, defaultScale), defaultScale), DoubleHelper.add(detailAssistMoney, detailMethodMoney, defaultScale), scale);

							// 套餐明细单价，四舍五入保留2位小数
							double detailAmount = DoubleHelper.div(itemAmount, detail.optDouble("item_count"), 2);

							batchArgs.add(new Object[]
							{ detailMethodMoney, detailAssistMoney, itemAmount, detailAmount, detail.optInt("id") });
						}
					}
				}

				double itemAmount = DoubleHelper.add(DoubleHelper.mul(item.optDouble("item_price"), item.optDouble("item_count"), defaultScale), DoubleHelper.add(assistMoney, methodMoney, defaultScale), scale);

				batchArgs.add(new Object[]
				{ methodMoney, assistMoney, itemAmount, itemPrice, item.optInt("id") });
			}
		}
		if (batchArgs.size() > 0)
		{
			StringBuilder updateBillItemSql = new StringBuilder("update pos_bill_item set method_money=?,assist_money=?,item_amount=?,item_price=?,discount_amount=0,discountr_amount=0,single_discount_amount=0 where id=?");
			baseDao.batchUpdate(updateBillItemSql.toString(), batchArgs);
		}
	}

	/**
	 * 获得折让金额discountr_amount然后分摊到各个明细里 奉送菜品折让金额为0 退菜菜品折让金额为0
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @param discountrAmount
	 * @param scale
	 * @throws Exception
	 */
	private void updateDiscountrAmount(String tenantId, int storeId, String billNum, String batchNum, double discountrAmount, int scale) throws Exception
	{
		final int defaultScale = 4;

		StringBuilder queryItemSql = new StringBuilder(
				"select id,item_id,setmeal_id,item_serial,setmeal_rwid,item_property,item_amount,item_count,return_count from pos_bill_item where coalesce(item_remark,'')<>'TC01' and coalesce(item_remark,'')<>'FS02' and coalesce(item_remark,'')<>'CJ05' and tenancy_id=? and store_id = ? and bill_num = ? and batch_num=?");
		List<JSONObject> itemList = baseDao.query4Json(tenantId, queryItemSql.toString(), new Object[]
		{ tenantId, storeId, billNum, batchNum });

		double averageTotal = 0d;
		double subtotal = 0d; // 统计所有菜的折让金额
		Integer maxId = 0;
		double maxValue = 0d;

		// 取总钱数,最大面额和对应的rwid
		for (JSONObject item : itemList)
		{
			double itemAmount = item.optDouble("item_amount");
			if (Tools.hv(item.opt("return_count")) && item.optDouble("return_count") > 0d)
			{
				double itemPrice = DoubleHelper.div(item.optDouble("item_amount"), item.optDouble("item_count"), defaultScale);
				itemAmount = DoubleHelper.mul(itemPrice, DoubleHelper.sub(item.optDouble("item_count"), item.optDouble("return_count"), defaultScale), scale);
				item.put("item_amount", itemAmount);
			}

			if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.optString("item_property")))
			{
				subtotal = DoubleHelper.add(subtotal, itemAmount, defaultScale);

				if (maxValue < itemAmount)
				{
					maxValue = itemAmount;
					maxId = item.optInt("id");
				}
			}
		}

		// 计算并平摊折让的钱
		for (JSONObject item : itemList)
		{
			if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.optString("item_property")))
			{
				double itemDiscountrAmount = DoubleHelper.mul(DoubleHelper.div(item.optDouble("item_amount"), subtotal, defaultScale), discountrAmount, scale);
				averageTotal = DoubleHelper.add(averageTotal, itemDiscountrAmount, defaultScale);
			}
		}

		List<Object[]> batchArgs = new ArrayList<Object[]>();
		for (JSONObject item : itemList)
		{
			if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.optString("item_property")))
			{
				double itemDiscountrAmount = DoubleHelper.mul(DoubleHelper.div(item.optDouble("item_amount"), subtotal, defaultScale), discountrAmount, scale);
				if (maxId == item.optInt("id"))
				{
					itemDiscountrAmount = DoubleHelper.add(itemDiscountrAmount, DoubleHelper.sub(discountrAmount, averageTotal, defaultScale), scale);
				}
				batchArgs.add(new Object[]
				{ itemDiscountrAmount, item.optInt("id") });

				if (SysDictionary.ITEM_PROPERTY_SETMEAL.equals(item.optString("item_property")))
				{ // 将减免金额平摊到套餐明细
					Integer dMaxId = null;
					double dmaxAmount = 0d;
					double sumAmount = 0d;

					List<JSONObject> detailList = new ArrayList<JSONObject>();
					for (JSONObject detail : itemList)
					{
						if (SysDictionary.ITEM_PROPERTY_MEALLIST.equals(detail.optString("item_property")) && item.optInt("item_id") == detail.optInt("setmeal_id") && item.optInt("item_serial") == detail.optInt("setmeal_rwid"))
						{
							detailList.add(detail);
							double detailDiscountrAmount = DoubleHelper.mul(DoubleHelper.div(detail.optDouble("item_amount"), item.optDouble("item_amount"), defaultScale), itemDiscountrAmount, scale);
							sumAmount = DoubleHelper.add(sumAmount, detailDiscountrAmount, defaultScale);

							if (null == dMaxId || dmaxAmount < detail.optDouble("item_amount"))
							{
								dMaxId = detail.optInt("id");
								dmaxAmount = detail.optDouble("item_amount");
							}
						}
					}

					for (JSONObject detail : detailList)
					{
						double detailDiscountrAmount = DoubleHelper.mul(DoubleHelper.div(detail.optDouble("item_amount"), item.optDouble("item_amount"), defaultScale), itemDiscountrAmount, scale);
						if (dMaxId == detail.optInt("id"))
						{
							detailDiscountrAmount = DoubleHelper.add(detailDiscountrAmount, DoubleHelper.sub(itemDiscountrAmount, sumAmount, defaultScale), scale);
						}
						batchArgs.add(new Object[]
						{ detailDiscountrAmount, detail.optInt("id") });
					}
				}
			}
		}
		// 更新折让金额
		if (batchArgs.size() > 0)
		{
			baseDao.batchUpdate("update pos_bill_item set discountr_amount = ? where id = ?", batchArgs);
		}
	}

	/**
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @param batchNum
	 * @param discountRate
	 * @param isNotState
	 * @param scale
	 * @throws Exception
	 */
	private void updateDiscountRateAmount(String tenantId, int storeId, String billNum, String batchNum, double discountRate, boolean isNotState, int scale) throws Exception
	{
		StringBuilder qureyBillItem = new StringBuilder(
				"select id,item_id,setmeal_id,item_serial,setmeal_rwid,item_property,discount_state,item_remark,item_amount,item_price,assist_money,method_money,item_count from pos_bill_item where tenancy_id=? and store_id = ? and bill_num = ? and batch_num=?");
		List<JSONObject> itemList = baseDao.query4Json(tenantId, qureyBillItem.toString(), new Object[]
		{ tenantId, storeId, billNum, batchNum });

		List<Object[]> batchArgs = new ArrayList<Object[]>();
		for (JSONObject item : itemList)
		{
			if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.optString("item_property")))
			{
				double itemDiscountRate = 100d;
				if (isNotState || "Y".equalsIgnoreCase(item.optString("discount_state")))
				{
					itemDiscountRate = discountRate;
				}
				batchArgs.addAll(getDiscountRateAmount(item, itemList, itemDiscountRate, scale));
			}
		}
		if (batchArgs.size() > 0)
		{
			baseDao.batchUpdate("update pos_bill_item set discount_rate=?,discount_amount=? where id = ?", batchArgs);
		}
	}

	/**
	 * 计算折扣率金额 奉送菜品折扣金额为0
	 * 
	 * @param item
	 * @param itemList
	 * @param itemDiscountRate
	 * @param scale
	 * @return
	 * @throws Exception
	 */
	private List<Object[]> getDiscountRateAmount(JSONObject item, List<JSONObject> itemList, double itemDiscountRate, int scale) throws Exception
	{
		final int defaultScale = 4;
		List<Object[]> batchArgs = new ArrayList<Object[]>();
		if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.optString("item_property")))
		{
			if (SysDictionary.ITEM_REMARK_TC01.equals(item.optString("item_remark")) || SysDictionary.ITEM_REMARK_FS02.equals(item.optString("item_remark")) || SysDictionary.ITEM_REMARK_CJ05.equals(item.optString("item_remark")))
			{
				itemDiscountRate = 100d;
			}

			double rate = DoubleHelper.sub(1d, DoubleHelper.div(itemDiscountRate, 100d, defaultScale), defaultScale);
			double itemAmount = item.optDouble("item_amount");
			if (itemAmount == 0d)
			{
				itemAmount = DoubleHelper.add(DoubleHelper.mul(item.optDouble("item_price"), item.optDouble("item_count"), defaultScale), DoubleHelper.add(item.optDouble("method_money"), item.optDouble("assist_money"), defaultScale), defaultScale);
			}
			batchArgs.add(new Object[]
			{ itemDiscountRate, DoubleHelper.mul(itemAmount, rate, scale), item.optInt("id") });

			if (SysDictionary.ITEM_PROPERTY_SETMEAL.equalsIgnoreCase(item.optString("item_property")))
			{
				// 套餐明细中，明细金额最大的折扣金额=套主项折扣金额-其它套餐明细折扣金额，否则会产生误差
				Integer dMaxId = null;
				double dItemMaxAmount = 0; // 循环查询明细中金额最大的
				double detailSumAmount = 0; // 套餐明细折扣金额之和

				List<JSONObject> detailList = new ArrayList<JSONObject>();
				for (JSONObject detail : itemList)
				{
					if (SysDictionary.ITEM_PROPERTY_MEALLIST.equals(detail.optString("item_property")) && item.optInt("item_id") == detail.optInt("setmeal_id") && item.optInt("item_serial") == detail.optInt("setmeal_rwid"))
					{
						double detailItemAmount = detail.optDouble("item_amount");
						if (detailItemAmount == 0d)
						{
							detailItemAmount = DoubleHelper.add(DoubleHelper.mul(detail.optDouble("item_price"), detail.optDouble("item_count"), defaultScale), DoubleHelper.add(detail.optDouble("method_money"), detail.optDouble("assist_money"), defaultScale), defaultScale);
						}
						// 套餐明细折扣金额
						double discountAmount = DoubleHelper.mul(detailItemAmount, rate, scale);
						// 套餐明细的折扣金额之和
						detailSumAmount = DoubleHelper.add(detailSumAmount, discountAmount, scale);
						// 套餐中明细金额最大的
						if (dMaxId == null || detailItemAmount > dItemMaxAmount)
						{
							dMaxId = detail.optInt("id");
							dItemMaxAmount = detailItemAmount;
						}

						JSONObject itemA = new JSONObject();
						itemA.put("id", detail.optInt("id"));
						itemA.put("discount_amount", discountAmount);
						detailList.add(itemA);
					}
				}

				for (JSONObject detail : detailList)
				{
					if (detail.optInt("id") == dMaxId)
					{
						// 套餐主项折扣-套餐明细项之和=折扣的误差
						double detailItemAmount = DoubleHelper.add(detail.optDouble("discount_amount"), DoubleHelper.sub(DoubleHelper.mul(itemAmount, rate, scale), detailSumAmount, scale), scale);

						batchArgs.add(new Object[]
						{ itemDiscountRate, detailItemAmount, detail.optInt("id") });
					}
					else
					{
						batchArgs.add(new Object[]
						{ itemDiscountRate, detail.optDouble("discount_amount"), detail.optInt("id") });
					}
				}

			}
		}
		return batchArgs;
	}

	/**
	 * 计算折扣减免金额 奉送菜品折扣金额为0
	 * 
	 * @param item
	 * @param itemList
	 * @param scale
	 * @return
	 * @throws Exception
	 */
	private List<Object[]> getDiscountDerateAmount(JSONObject item, List<JSONObject> itemList, int scale) throws Exception
	{
		final int defaultScale = 4;
		List<Object[]> batchArgs = new ArrayList<Object[]>();
		if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.optString("item_property")))
		{
			double derate = item.optDouble("derate");
			if (derate > item.optDouble("item_price"))
			{
				derate = item.optDouble("item_price");
			}
			// 减免金额
			double itemDiscountAmount = DoubleHelper.mul(derate, item.optDouble("item_count"), scale);
			// 奉送菜品折扣金额为0
			if (SysDictionary.ITEM_REMARK_TC01.equalsIgnoreCase(item.optString("item_remark")) || SysDictionary.ITEM_REMARK_FS02.equalsIgnoreCase(item.optString("item_remark")) || SysDictionary.ITEM_REMARK_CJ05.equalsIgnoreCase(item.optString("item_remark")))
			{
				itemDiscountAmount = 0d;
			}
			batchArgs.add(new Object[]
			{ 100d, itemDiscountAmount, item.optInt("id") });

			if (SysDictionary.ITEM_PROPERTY_SETMEAL.equals(item.optString("item_property")))
			{ // 将减免金额平摊到套餐明细
				Integer maxId = null;
				double maxAmount = 0d;
				double sumItemdiscountAmount = 0d;
				double proportion = DoubleHelper.div(itemDiscountAmount, item.optDouble("item_amount"), defaultScale);

				List<JSONObject> detailList = new ArrayList<JSONObject>();
				for (JSONObject detail : itemList)
				{
					if (SysDictionary.ITEM_PROPERTY_MEALLIST.equals(detail.optString("item_property")) && item.optInt("item_id") == detail.optInt("setmeal_id") && item.optInt("item_serial") == detail.optInt("setmeal_rwid"))
					{
						double detailDiscountAmount = DoubleHelper.mul(detail.optDouble("item_amount"), proportion, scale);
						// 奉送菜品折扣金额为0
						if (SysDictionary.ITEM_REMARK_TC01.equalsIgnoreCase(detail.optString("item_remark")) || SysDictionary.ITEM_REMARK_FS02.equalsIgnoreCase(detail.optString("item_remark")) || SysDictionary.ITEM_REMARK_CJ05.equalsIgnoreCase(detail.optString("item_remark")))
						{
							detailDiscountAmount = 0d;
						}
						sumItemdiscountAmount = DoubleHelper.add(sumItemdiscountAmount, detailDiscountAmount, defaultScale);

						detail.put("discount_rate", 100);
						detail.put("discount_amount", detailDiscountAmount);
						detailList.add(detail);

						if (null == maxId || maxAmount < detail.optDouble("item_amount"))
						{
							maxId = detail.optInt("id");
							maxAmount = detail.optDouble("item_amount");
						}
					}
				}

				for (JSONObject detail : detailList)
				{
					double detailDiscountAmount = DoubleHelper.mul(detail.optDouble("item_amount"), proportion, scale);
					if (maxId == detail.optInt("id"))
					{
						detailDiscountAmount = DoubleHelper.add(detailDiscountAmount, DoubleHelper.sub(itemDiscountAmount, sumItemdiscountAmount, defaultScale), scale);
					}
					// 奉送菜品折扣金额为0
					if (SysDictionary.ITEM_REMARK_TC01.equalsIgnoreCase(detail.optString("item_remark")) || SysDictionary.ITEM_REMARK_FS02.equalsIgnoreCase(detail.optString("item_remark")) || SysDictionary.ITEM_REMARK_CJ05.equalsIgnoreCase(detail.optString("item_remark")))
					{
						detailDiscountAmount = 0d;
					}
					batchArgs.add(new Object[]
					{ 100d, detailDiscountAmount, detail.optInt("id") });
				}
			}
		}
		return batchArgs;
	}

	/**
	 * 计算会员价,把会员价的差价放到优惠金额里
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @param batchNum
	 * @param scale
	 * @throws Exception
	 */
	private void updateDiscountkVipPriceAmount(String tenantId, int storeId, String billNum, String batchNum, int scale) throws Exception
	{
		final int defaultScale = 4;

		StringBuilder queryBillItemSql = new StringBuilder();
		queryBillItemSql.append(" select bi.id,bi.item_id,bi.setmeal_id,bi.item_serial,bi.setmeal_rwid,bi.item_property,bi.item_remark,bi.item_price,bi.assist_money,bi.method_money,bi.item_count,bi.item_amount,coalesce(bi.third_price,0) as third_price");
		queryBillItemSql.append(" from pos_bill_item bi where bi.tenancy_id=? and bi.store_id = ? and bi.bill_num = ? and bi.batch_num=?");

		List<JSONObject> itemList = baseDao.query4Json(tenantId, queryBillItemSql.toString(), new Object[]
		{ tenantId, storeId, billNum, batchNum });
		List<Object[]> batchArgs = new ArrayList<Object[]>();
		for (JSONObject item : itemList)
		{
			if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.optString("item_property")))
			{
				double discountAmount = 0;
				if (item.optDouble("third_price") > 0 && item.optDouble("item_price") > item.optDouble("third_price"))
				{
					discountAmount = DoubleHelper.mul(DoubleHelper.sub(item.optDouble("item_price"), item.optDouble("third_price"), defaultScale), item.optDouble("item_count"), scale);
				}

				if (SysDictionary.ITEM_REMARK_TC01.equalsIgnoreCase(item.optString("item_remark")) || SysDictionary.ITEM_REMARK_FS02.equalsIgnoreCase(item.optString("item_remark")) || SysDictionary.ITEM_REMARK_CJ05.equalsIgnoreCase(item.optString("item_remark")))
				{
					discountAmount = 0d;
				}
				batchArgs.add(new Object[]
				{ 100d, discountAmount, item.optInt("id") });

				if (discountAmount > 0)
				{
					if (SysDictionary.ITEM_PROPERTY_SETMEAL.equals(item.optString("item_property")))
					{ // 将减免金额平摊到套餐明细
						Integer dMaxId = null;
						double dmaxAmount = 0d;
						double sumAmount = 0d;

						List<JSONObject> detailList = new ArrayList<JSONObject>();
						for (JSONObject detail : itemList)
						{
							if (SysDictionary.ITEM_PROPERTY_MEALLIST.equals(detail.optString("item_property")) && item.optInt("item_id") == detail.optInt("setmeal_id") && item.optInt("item_serial") == detail.optInt("setmeal_rwid"))
							{
								detailList.add(detail);
								double detailDiscountAmount = DoubleHelper.mul(DoubleHelper.div(detail.optDouble("item_amount"), item.optDouble("item_amount"), defaultScale), discountAmount, scale);
								sumAmount = DoubleHelper.add(sumAmount, detailDiscountAmount, defaultScale);

								if (null == dMaxId || dmaxAmount < detail.optDouble("item_amount"))
								{
									dMaxId = detail.optInt("id");
									dmaxAmount = detail.optDouble("item_amount");
								}
							}
						}

						for (JSONObject detail : detailList)
						{
							double detailDiscountAmount = DoubleHelper.mul(DoubleHelper.div(detail.optDouble("item_amount"), item.optDouble("item_amount"), defaultScale), discountAmount, scale);
							if (dMaxId == detail.optInt("id"))
							{
								detailDiscountAmount = DoubleHelper.add(detailDiscountAmount, DoubleHelper.sub(discountAmount, sumAmount, defaultScale), scale);
							}
							batchArgs.add(new Object[]
							{ 100d, detailDiscountAmount, detail.optInt("id") });
						}
					}
				}
			}
		}
		if (batchArgs.size() > 0)
		{
			StringBuilder updateBillItemSql = new StringBuilder("update pos_bill_item set discount_rate=?,discount_amount=? where id = ?");
			baseDao.batchUpdate(updateBillItemSql.toString(), batchArgs);
		}
	}

	private void updateScrapAmount(String tenantId, int storeId, String billNum, String batchNum, double scrapAmount) throws Exception
	{
		StringBuilder updateItemScrapSql = new StringBuilder("update pos_bill_item set single_discount_amount = 0 where tenancy_id=? and store_id = ? and bill_num = ? and batch_num=?");
		baseDao.update(updateItemScrapSql.toString(), new Object[]
		{ tenantId, storeId, billNum, batchNum });

		StringBuilder queryItemSql = new StringBuilder(
				"select id,item_id,setmeal_id,item_serial,setmeal_rwid,item_property,item_amount from pos_bill_item where coalesce(item_remark,'')<>'TC01' and coalesce(item_remark,'')<>'FS02' and coalesce(item_remark,'')<>'CJ05' and tenancy_id=? and store_id = ? and bill_num = ? and batch_num=?");
		List<JSONObject> itemList = baseDao.query4Json(tenantId, queryItemSql.toString(), new Object[]
		{ tenantId, storeId, billNum, batchNum });

		if (itemList != null && itemList.size() > 0)
		{
			JSONObject maxJson = null;
			for (JSONObject item : itemList)
			{
				if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.optString("item_property")))
				{
					if (null == maxJson || maxJson.optDouble("item_amount") < item.optDouble("item_amount"))
					{
						maxJson = item;
					}
				}
			}
			StringBuilder updateItemSql = new StringBuilder("update pos_bill_item set single_discount_amount=? where id = ?");
			if (Tools.hv(maxJson))
			{
				baseDao.update(updateItemSql.toString(), new Object[]
				{ scrapAmount, maxJson.optInt("id") });
			}

			if (SysDictionary.ITEM_PROPERTY_SETMEAL.equalsIgnoreCase(maxJson.optString("item_property")))
			{
				JSONObject detailMaxJson = null;
				for (JSONObject detail : itemList)
				{
					if (SysDictionary.ITEM_PROPERTY_MEALLIST.equals(detail.optString("item_property")) && maxJson.optInt("item_id") == detail.optInt("setmeal_id") && maxJson.optInt("item_serial") == detail.optInt("setmeal_rwid"))
					{
						if (null == detailMaxJson || detailMaxJson.optDouble("item_amount") < detail.optDouble("item_amount"))
						{
							detailMaxJson = detail;
						}
					}
				}
				if (Tools.hv(detailMaxJson))
				{
					baseDao.update(updateItemSql.toString(), new Object[]
					{ scrapAmount, detailMaxJson.optInt("id") });
				}
			}
		}
	}
}
