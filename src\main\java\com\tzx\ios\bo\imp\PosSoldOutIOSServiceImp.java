package com.tzx.ios.bo.imp;

import java.util.List;

import javax.annotation.Resource;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import com.tzx.framework.common.entity.Data;
import com.tzx.ios.bo.PosSoldOutIOSService;
import com.tzx.ios.po.springjdbc.dao.PosSoldOutDao;
import com.tzx.pos.bo.imp.PosBaseServiceImp;

import net.sf.json.JSONObject;


@Service(PosSoldOutIOSService.NAME)
public class PosSoldOutIOSServiceImp extends PosBaseServiceImp implements PosSoldOutIOSService
{

	private static final Logger	logger				= Logger.getLogger(PosSoldOutIOSService.class);

	@Resource(name = PosSoldOutDao.NAME)
	private PosSoldOutDao		soldOutDao;

	@Override
	public List<JSONObject> findSoldOut(Data param) throws Exception
	{
		String tenantId = param.getTenancy_id();
		Integer organId = param.getStore_id();

		return soldOutDao.findSoldOut(tenantId, organId);
	}
	
	
}
