package com.tzx.framework.common.constant;

public class SysParameterCode
{
	/**
	 * 微信付款码校验规则
	 */
	public static final String	WECHAT_PAY_CODE_CHECK_RULE	= "WECHAT_PAY_CODE_CHECK_RULE";

	/**
	 * 支付宝付款码校验规则
	 */
	public static final String	ALI_PAY_CODE_CHECK_RULE		= "ALI_PAY_CODE_CHECK_RULE";

	/**
	 * 共享估清设置
	 */
	public static final String	WM_SHARESTOCK_SWITCH		= "wm_sharestock_switch";

	/**
	 * 系统参数，是否秒付上传数据
	 */
	public static final String	IFUP_QUICK_PAY				= "ifup_quick_pay";

	/**
	 * 系统参数，是否开启秒付功能
	 */
	public static final String	IS_START_CLOUDPOS_PAY		= "is_start_cloudPos_pay";
	
	/**
	 * 是否启用众赏会员系统
	 * 
	 */
	public static final String	STORE_IS_START_ZS			= "store_is_start_zs";
	
	/**
	 * 众赏appId参数
	 * 
	 */
	public static final String	APPID_ZS					= "appId_zs";
	
	/**
	 * 众赏众赏secretKey参数
	 * 
	 */
	public static final String	SECRET_KEY_ZS				= "secret_key_zs";
	
	/**
	 * 判断是否启用微生活点餐平台对接
	 * 
	 */
	public static final String	IS_USER_WLIFE				= "IS_USER_WLIFE";
	
	/**
	 * 微生活点餐订单模式, 2:先付 3:后付
	 */
	public static final String	WLIFE_ORDERMODE_KEY				= "wlife_ordermode";
	/** 菜品备注同步方式 */
	public static final String	WLIFE_SYNC_MEMO_TYPE_KEY			= "WLIFE_SYNC_MEMO_TYPE";
	/** 付款异常取消付款的限制时间 */
	public static final String	CANCEL_PAYMENT_LIMIT_TIME			= "CANCEL_PAYMENT_LIMIT_TIME";
}
