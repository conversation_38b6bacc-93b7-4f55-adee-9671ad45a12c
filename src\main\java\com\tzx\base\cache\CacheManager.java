package com.tzx.base.cache;

import com.tzx.framework.common.util.Tools;

import java.util.*;

/**
 * Created by kevin on 2017-06-15.
 * 缓存管理
 */
public class CacheManager {

    private static HashMap cacheMap = new HashMap();

    //单实例构造方法
    private CacheManager() {
        super();
    }


    /**
     * 获取缓存
     * @param key
     * @return
     */
    private synchronized static Cache getCache(String key) {
        return (Cache) cacheMap.get(key);
    }

    /**
     * 获取缓存是Map对象
     * @param key
     * @return
     */
    public synchronized static Map getCacheMap(String key) {
        return (Map)cacheMap.get(key);
    }

    /**
     * 获取缓存是List对象
     * @param key
     * @return
     */
    public synchronized static List<?> getCacheList(String key) {
        return (List<?>)cacheMap.get(key);
    }

    /**
     * 获取缓存对象
     * @param key
     * @return
     */
    public synchronized static Object getCacheObject(String key) {
        Object obj = cacheMap.get(key);
        if(!Tools.isNullOrEmpty(obj)) {
            if (obj instanceof Map) {
                return (Map)obj;
            }else if(obj instanceof List) {
                return (List)obj;
            }
        }
        return null;
    }

    /**
     * 判断是否有缓存
     * @param key
     * @return
     */
    public synchronized static boolean hasCache(String key) {
        return cacheMap.containsKey(key);
    }

    /**
     * 清除所有的缓存
     */
    public synchronized static void clearAll() {
        cacheMap.clear();
    }

    /**
     * 清除指定的缓存
     * 通过遍历HASHMAP下的所有对象，来判断它的key与传入的type是否匹配
     * @param type
     */
    private synchronized static void clearAll(String type) {
        Iterator iterator = cacheMap.entrySet().iterator();
        String key;
        List<String> list = new ArrayList<String>();
        try {
            while (iterator.hasNext()) {
                Map.Entry entry = (Map.Entry) iterator.next();
                key = (String) entry.getKey();
                //如果匹配则删除掉
                if (key.startsWith(type)) {
                    list.add(key);
                }
            }
            for (int i = 0; i < list.size(); i++) {
                clearOnly(list.get(i));
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    /**
      * 清除指定的缓存
      * @param key
     */
    public synchronized static void clearOnly(String key) {
        cacheMap.remove(key);
    }

    /**
     * 保存缓存
     * @param key
     * @param obj
     */
    public synchronized static void putCache(String key, Object obj) {
        cacheMap.put(key, obj);
    }

    /**
     * 获取缓存信息
     * @param key
     * @return
     */
    public static Cache getCacheInfo(String key) {

        if (hasCache(key)) {
            Cache cache = getCache(key);
            //调用判断是否终止方法
            if (cacheExpired(cache)) {
                cache.setExpired(true);
            }
            return cache;
        }else
            return null;
    }

    /**
     * 判断缓存是否终止
     * @param cache
     * @return
     */
    public static boolean cacheExpired(Cache cache) {
        //传入的缓存不存在
        if (null == cache) {
            return false;
        }
        //系统当前的毫秒数
        long nowDt = System.currentTimeMillis();
        //缓存内的过期毫秒数
        long cacheDt = cache.getTimeOut();
        //过期时间小于等于零时,或者过期时间大于当前时间时，则为false
        if (cacheDt <= 0||cacheDt > nowDt) {
            return false;
        //大于过期时间 即过期
        } else {
            return true;
        }
    }

    /**
     * 载入缓存信息
     * @param key
     * @param obj
     * @param dt
     * @param expired
     */
    public static void putCacheInfo(String key, Object obj, long dt,boolean expired) {
        Cache cache = new Cache();
        cache.setKey(key);
        //设置多久后更新缓存
        cache.setTimeOut(dt + System.currentTimeMillis());
        cache.setValue(obj);
        //缓存默认载入时，终止状态为false
        cache.setExpired(expired);
        cacheMap.put(key, cache);
    }

    /**
     * 重写载入缓存信息方法，默认载入
     * @param key
     * @param obj
     * @param dt
     */
    public static void putCacheInfo(String key,Object obj,long dt){
        Cache cache = new Cache();
        cache.setKey(key);
        cache.setTimeOut(dt+System.currentTimeMillis());
        cache.setValue(obj);
        cache.setExpired(false);
        cacheMap.put(key,cache);
    }

    /**
     * 获取缓存中的大小
     * @return
     */
    public static int getCacheSize() {
        return cacheMap.size();
    }

    /**
     * 获取指定的类型的大小
     * @param type
     * @return
     */
    public static int getCacheSize(String type) {
        int i = 0;
        Iterator iterator = cacheMap.entrySet().iterator();
        String key;
        try {
            while (iterator.hasNext()) {
                Map.Entry entry = (Map.Entry) iterator.next();
                key = (String) entry.getKey();
                //如果匹配则删除掉
                if (key.indexOf(type) != -1) {
                    i++;
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }

        return i;
    }

    /**
     * 获取缓存对象中的所有键值名称
     * @return
     */
    public static List getCacheAllkey() {
        List<String> list = new ArrayList<String>();
        try {
            Iterator iterator = cacheMap.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry entry = (Map.Entry) iterator.next();
                list.add((String) entry.getKey());
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            return list;
        }
    }

    /**
     * 获取缓存对象中指定类型的键值名称
     * @param type
     * @return
     */
    public static List getCacheListkey(String type) {
        List<String> list = new ArrayList<String>();
        String key;
        try {
            Iterator iterator = cacheMap.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry entry = (Map.Entry) iterator.next();
                key = (String) entry.getKey();
                if (key.indexOf(type) != -1) {
                    list.add(key);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        } finally {
            return list;
        }
    }

}
