package com.tzx.pos.base.service.impl;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.member.common.entity.PosCustomerOperateListEntity;
import com.tzx.member.crm.bo.CustomerCardActivationService;
import com.tzx.member.crm.bo.CustomerCardConsumeService;
import com.tzx.member.crm.bo.CustomerCardRechargeService;
import com.tzx.member.crm.bo.CustomerService;
import com.tzx.member.crm.po.springjdbc.dao.CustomerDao;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.dao.PosCustomerOperateDao;
import com.tzx.pos.base.service.PosCustomerOperateService;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.bo.PosPrintNewService;
import com.tzx.pos.bo.PosPrintService;
import com.tzx.pos.bo.dto.PosThirdPaymentOrderEntity;
import com.tzx.pos.bo.payment.PosPaymentService;
import com.tzx.pos.po.springjdbc.dao.PosPaymentDao;
import com.tzx.thirdpay.bo.ThirdPaymentService;

@Service(PosCustomerOperateService.NAME)
public class PosCustomerOperateServiceImp implements PosCustomerOperateService
{

	private static Logger			logger			= Logger.getLogger(PosCustomerOperateService.class);
	
	@Resource(name = CustomerCardConsumeService.NAME)
	private CustomerCardConsumeService		customerCardService;
	
	@Resource(name = CustomerCardRechargeService.NAME)
	private CustomerCardRechargeService		cardRechargeService;
	
	@Resource(name = CustomerCardActivationService.NAME)
	private CustomerCardActivationService	cardActivationService;
	
	@Resource(name = CustomerService.NAME)
	private CustomerService		customerService;
	
	@Resource(name = PosPrintService.NAME)
	private PosPrintService		posPrintService;
	
	@Resource(name = PosPrintNewService.NAME)
	private PosPrintNewService posPrintNewService;
	
	@Resource(name = PosPaymentService.NAME)
	public PosPaymentService	paymentService;
	
	@Resource(name = ThirdPaymentService.NAME)
	public ThirdPaymentService	thirdPaymentWayService;
	
	@Resource(name = PosCustomerOperateDao.NAME)
	private PosCustomerOperateDao	customerOperateDao;

	@Resource(name = PosPaymentDao.NAME)
	private PosPaymentDao			paymentDao;
	
	@Resource(name = CustomerDao.NAME)
	private CustomerDao			customerDao;
	
	/**
	 * 查询总次数
	 */
	private final Double			QUERY_COUNT_NUM	= 60.0;

	@Override
	public List<PosCustomerOperateListEntity> getPosCustomerOperateListNotComplete(String tenancyId, int storeId, Double queryCountNum) throws Exception
	{
		String customerType = customerDao.getCustomerType(tenancyId, storeId);

		if (SysDictionary.CUSTOMER_TYPE_ACEWILL.equals(customerType)==false)
		{
			Timestamp currentTime = DateUtil.currentTimestamp();
			if (null == queryCountNum || queryCountNum.isNaN() || queryCountNum <= 0)
			{
				queryCountNum = QUERY_COUNT_NUM;
			}
			return customerOperateDao.getPosCustomerOperateListNotComplete(tenancyId, storeId, currentTime, queryCountNum);
		}
		return new ArrayList<PosCustomerOperateListEntity>();
	}

	@Override
	public void queryPosCustomerOperateListForRunnable(String tenancyId, int storeId, PosCustomerOperateListEntity customerOperate) throws Exception
	{
		String customerType = customerDao.getCustomerType(tenancyId, storeId);
		if (SysDictionary.CUSTOMER_TYPE_ACEWILL.equals(customerType)==false)
		{
			this.querySaasCustomerOperateListForRunnable(tenancyId, storeId, customerOperate);
		}
	}
	
	public void querySaasCustomerOperateListForRunnable(String tenancyId, int storeId, PosCustomerOperateListEntity customerOperate) throws Exception
	{
		// TODO Auto-generated method stub
		Timestamp currentTime = DateUtil.currentTimestamp();
		
		switch (customerOperate.getOperat_type())
		{
		/** 会员卡 :发卡 */
			case SysDictionary.OPERAT_TYPE_FK:
				this.queryCustomerCardActivation(tenancyId, storeId, customerOperate);
				break;

			/** 会员卡 :充值 */
			case SysDictionary.OPERAT_TYPE_CZ:
				this.queryCustomerCardRecharge(tenancyId, storeId, customerOperate);
				
				break;

			/** 会员卡 :消费 */
			case SysDictionary.OPERAT_TYPE_XF:
				this.queryCustomerCardConsume(tenancyId, storeId, customerOperate);
				break;

			/** 会员卡 :反充值 */
			case SysDictionary.OPERAT_TYPE_FCZ:
				this.queryCustomerCardCancelRecharge(tenancyId, storeId, customerOperate);
				break;

			/** 会员卡 :反消费 */
			case SysDictionary.OPERAT_TYPE_FXF:
				this.queryCustomerCardCancelConsume(tenancyId, storeId, customerOperate);
				break;

			/** 会员卡 :补卡 */
			case SysDictionary.OPERAT_TYPE_BK:

				break;

			/** 会员卡 :并卡转入 */
			case SysDictionary.OPERAT_TYPE_BKZR:

				break;

			/** 会员卡 :并卡转出 */
			case SysDictionary.OPERAT_TYPE_BKZC:

				break;

			/** 会员卡 :退卡 */
			case SysDictionary.OPERAT_TYPE_TK:

				break;

			/** 会员卡 :购买会籍 */
			case SysDictionary.OPERAT_TYPE_GMHJ:

				break;

			/** 会员积分消费 */
			case SysDictionary.OPERAT_TYPE_JFXF:

				break;
			/** 撤销会员积分消费 */
			case SysDictionary.OPERAT_TYPE_CXXF:

				break;

			/** 会员积分增加 */
			case SysDictionary.OPERAT_TYPE_JFZJ:

				break;

			/** 撤销会员积分增加 */
			case SysDictionary.OPERAT_TYPE_CXJFZJ:

				break;

			default:
				logger.error("操作类型" + customerOperate.getOperat_type() + "不存在!");
				break;
		}

		// 修改操作记录
		customerOperate.setLast_query_time(currentTime);
		customerOperate.setQuery_count(customerOperate.getQuery_count()+1);
		customerOperateDao.updatePosCustomerOperateListByQueryResult(tenancyId, storeId, customerOperate);
	}

	private void queryCustomerCardConsume(String tenancyId, int storeId, PosCustomerOperateListEntity customerOperate) throws Exception
	{
		Data resultData =customerCardService.queryCustomerCardConsume(tenancyId, storeId, customerOperate);
		
		if(Type.BILL_PAYMENT.name().equals(customerOperate.getAction_type()))
		{
			if (Constant.CODE_SUCCESS == resultData.getCode() && resultData.isSuccess())
			{
				JSONObject responseJson = JSONObject.fromObject(resultData.getData().get(0));
				customerService.customerCardConsumePrint(tenancyId, storeId, customerOperate.getPos_num(), responseJson, SysDictionary.PRINT_CODE_1010);
				
				Integer shiftId = paymentDao.getShiftId(tenancyId, storeId, customerOperate.getBusiness_date(), String.valueOf(customerOperate.getOperator_id()), customerOperate.getPos_num());
				
				//支付成功,调用结账
				JSONObject postBill = paymentDao.getPosBillByBillnum(tenancyId, storeId, customerOperate.getThird_bill_code());

				JSONObject data = new JSONObject();
				data.put("report_date", DateUtil.formatDate(customerOperate.getBusiness_date()));
				data.put("shift_id", shiftId);
				data.put("pos_num", customerOperate.getPos_num());
				data.put("opt_num", customerOperate.getOperator_id());
				data.put("bill_num", customerOperate.getThird_bill_code());
				data.put("table_code", postBill.optString("table_code"));
				data.put("payment_amount", postBill.opt("payment_amount"));
				data.put("difference", postBill.opt("difference"));
				data.put("sale_mode", postBill.optString("sale_mode"));
				data.put("isprint_bill", "Y");
				// item

				JSONObject item = paymentDao.getBillPaymentByPaymentClass(tenancyId, storeId, customerOperate.getThird_bill_code(), SysDictionary.PAYMENT_CLASS_CARD);
				item.put("bill_code", customerOperate.getBill_code());
				item.put("consume_credit", 0);
				item.put("consume_creditmoney", 0);
				item.put("integraloffset", 0);
				JSONArray items = new JSONArray();
				items.add(item);
				data.put("item", items);

				List<JSONObject> datas = new ArrayList<JSONObject>();
				datas.add(data);

				Data paramData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
				paramData.setType(Type.BILL_PAYMENT);
				paramData.setData(datas);

				JSONObject printJson = new JSONObject();
				paymentService.billPaymentService(paramData, (new Data()), printJson);
				
//				posDishService.upload(tenantId, organId + "", "", "", "", billno);
				try
				{
//					String source = param.getSource();
//					if (("order_device".equals(source) || "android_pad".equals(source)))
//					{
//						posPrintService.getBindOptNum(printJson.optString("tenancy_id"), printJson.optInt("store_id"), printJson);
//					}

					posPrintService.printPosBillForPayment(printJson, posPrintNewService);
				}
				catch (Exception e1)
				{
					logger.error(e1);
				}
			}
			else if (Constant.CODE_CONN_EXCEPTION != resultData.getCode() && Constant.CODE_CONN_UNKNOWNHOST_EXCEPTION != resultData.getCode())
			{
				//支付失败,取消付款
				JSONObject item = paymentDao.getBillPaymentByPaymentClass(tenancyId, storeId, customerOperate.getThird_bill_code(), SysDictionary.PAYMENT_CLASS_CARD);
				JSONObject payment = new JSONObject();
				payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_FAILURE);
				payment.put("id", item.optInt("id"));
				paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", payment);
			}
		}
	}
	
	
	private void queryCustomerCardCancelConsume(String tenancyId, int storeId, PosCustomerOperateListEntity customerOperate) throws Exception
	{
		Data resultData = customerCardService.queryCustomerCardCancelConsume(tenancyId, storeId, customerOperate);

		if (Type.BILL_PAYMENT_CANCEL.name().equals(customerOperate.getAction_type()))
		{
			if (Constant.CODE_SUCCESS == resultData.getCode() && resultData.isSuccess())
			{
				
			}
			else if (Constant.CODE_CONN_EXCEPTION != resultData.getCode())
			{

			}
		}
		else if (Type.CANCBILL.name().equals(customerOperate.getAction_type()) || Type.RETREAT_ITEM.name().equals(customerOperate.getAction_type()))
		{
			if (Constant.CODE_SUCCESS == resultData.getCode() && resultData.isSuccess())
			{
				
			}
			else if (Constant.CODE_CONN_EXCEPTION != resultData.getCode())
			{

			}
		}
		else if (Type.REGAIN_BILL.name().equals(customerOperate.getAction_type()))
		{
			if (Constant.CODE_SUCCESS == resultData.getCode() && resultData.isSuccess())
			{
				
			}
			else if (Constant.CODE_CONN_EXCEPTION != resultData.getCode())
			{

			}
		}
	}
	
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param customerOperate
	 * @throws Exception
	 */
	private void queryCustomerCardRecharge(String tenancyId, int storeId, PosCustomerOperateListEntity customerOperate) throws Exception
	{
		if (false == SysDictionary.THIRD_PAY_STATUS_SUCCESS.equals(customerOperate.getPayment_state()))
		{
			// 支付中记录,等待支付结果
			return;
		}
		if(SysDictionary.CUSTOMER_OPERATE_STATE_WAIT.equals(customerOperate.getOperate_state()))
		{
			//查询第三方支付状态
			this.queryThirdPaymentForCardRecharge(tenancyId, storeId, customerOperate);
		}
		else
		{
			// 请求总部,查询充值状态
			JSONObject printJson = new JSONObject();
			Data resultData = cardRechargeService.queryCustomerCardRechargeToCrm(tenancyId, storeId, SysDictionary.OPERAT_TYPE_CZ, customerOperate, printJson);

			if (Constant.CODE_SUCCESS == resultData.getCode()&& null!=resultData.getData()&&resultData.getData().size()>0)
			{
				// 判断充值,打印小票
				JSONObject rechargeResultJson = JSONObject.fromObject(resultData.getData().get(0));
				cardRechargeService.printForCustomerCardRecharge(tenancyId, storeId, rechargeResultJson);
			}
		}
	}
	
	private void queryThirdPaymentForCardRecharge(String tenancyId, int storeId, PosCustomerOperateListEntity customerOperate) throws Exception
	{
		JSONObject queryParamJson = new JSONObject();
		queryParamJson.put("aid_order_num", customerOperate.getThird_bill_code());
		queryParamJson.put("object_name", SysDictionary.THIRD_PAY_CHARGE);
		PosThirdPaymentOrderEntity thirdPayment = thirdPaymentWayService.getThirdPaymentOrder(tenancyId, storeId, queryParamJson);
		
		if (null != thirdPayment)
		{
			String paymentState = thirdPayment.getStatus();
			String operateState = customerOperate.getOperate_state();
			String requestStatus = customerOperate.getRequest_status();
			String requestCode = customerOperate.getRequest_code();
			String requestMsg = customerOperate.getRequest_msg();
			Timestamp finishTime = null;

			if (SysDictionary.THIRD_PAY_CHARGE.equals(thirdPayment.getObject_name()))
			{
				switch (paymentState)
				{
					case SysDictionary.THIRD_PAY_STATUS_SUCCESS:

						operateState = SysDictionary.CUSTOMER_OPERATE_STATE_PROCESS;

						JSONObject paramJson = new JSONObject();
						paramJson.put("card_code", customerOperate.getCard_code());
						paramJson.put("third_code", customerOperate.getThird_code());
						paramJson.put("income", thirdPayment.getSettle_amount());
						paramJson.put("payment", thirdPayment.getPayment_id());
						paramJson.put("salesman", customerOperate.getSales_person());
						paramJson.put("pay_no", thirdPayment.getTransaction_no());
						paramJson.put("report_date", DateUtil.formatDate(customerOperate.getBusiness_date()));
						paramJson.put("shift_id", customerOperate.getShift_id());
						paramJson.put("chanel", customerOperate.getChanel());
						paramJson.put("opt_num", customerOperate.getOperator_id());
						paramJson.put("pos_num", customerOperate.getPos_num());
						paramJson.put("is_invoice", customerOperate.getIs_invoice());
						
						JSONObject extJson = JSONObject.fromObject(customerOperate.getExtend_param());
						paramJson.put("reward_money", ParamUtil.getDoubleValueByObject(extJson, "reward_money"));
						paramJson.put("auto_reward_money", ParamUtil.getStringValueByObject(extJson, "auto_reward_money"));
						
						Data returnData = cardRechargeService.customerCardRechargeManage(tenancyId, storeId, customerOperate.getThird_bill_code(), paramJson,customerOperate,Oper.add);

						if (Constant.CODE_SUCCESS == returnData.getCode())
						{
							operateState = SysDictionary.CUSTOMER_OPERATE_STATE_SUCCESS;
							finishTime = DateUtil.currentTimestamp();

							// 打印小票
							JSONObject rechargeResultJson = JSONObject.fromObject(returnData.getData().get(0));
							rechargeResultJson.put("is_invoice", ParamUtil.getStringValueByObject(paramJson, "is_invoice"));
							rechargeResultJson.put("report_date", ParamUtil.getStringValueByObject(paramJson, "report_date"));
							cardRechargeService.printForCustomerCardRecharge(tenancyId, storeId, rechargeResultJson);
						}

						break;
					case SysDictionary.THIRD_PAY_STATUS_FAIL:
						operateState = SysDictionary.CUSTOMER_OPERATE_STATE_FAIL;

						break;
					case SysDictionary.THIRD_PAY_STATUS_PAYING:
						operateState = SysDictionary.CUSTOMER_OPERATE_STATE_WAIT;
						break;
					case SysDictionary.THIRD_PAY_STATUS_REFUND_SUCCESS:
					case SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL:
					case SysDictionary.THIRD_PAY_STATUS_REVOKED_SUCCESS:
					case SysDictionary.THIRD_PAY_STATUS_REVOKED_FAIL:
					case SysDictionary.THIRD_PAY_STATUS_REFUND:
					case SysDictionary.THIRD_PAY_STATUS_REVOKED:
						operateState = SysDictionary.CUSTOMER_OPERATE_STATE_FAIL;
						break;
					default:
						paymentState = SysDictionary.THIRD_PAY_STATUS_NOOK;
						operateState = SysDictionary.CUSTOMER_OPERATE_STATE_WAIT;
						break;
				}
			}

			JSONObject updateJson = new JSONObject();
			updateJson.put("bill_code", customerOperate.getThird_bill_code());
			updateJson.put("payment_state", paymentState);
			updateJson.put("operate_state", operateState);
			updateJson.put("request_status", requestStatus);
			updateJson.put("request_code", requestCode);
			updateJson.put("request_msg", requestMsg);
			updateJson.put("finish_time", DateUtil.format(finishTime));
			updateJson.put("operat_type", customerOperate.getOperat_type());

			cardRechargeService.updateCustomerOperateListStateForCardRecharge(tenancyId, storeId, updateJson);
		}
	}
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param customerOperate
	 * @throws Exception
	 */
	private void queryCustomerCardCancelRecharge(String tenancyId, int storeId, PosCustomerOperateListEntity customerOperate) throws Exception
	{
//		//请求总部,查询撤销充值状态
//		JSONObject printJson = new JSONObject();
//		cardRechargeService.queryCustomerCardRechargeToCrm(tenancyId, storeId, SysDictionary.OPERAT_TYPE_FCZ, customerOperate, printJson);
//		
//		//判断撤销充值成功,取消发票
//		cardRechargeService.cancelElectricInvoice(tenancyId, storeId, printJson);
//		
//		//判断撤销充值成功,打印小票
//		cardRechargeService.printForCustomerCardRecharge(tenancyId, storeId, SysDictionary.PRINT_CODE_1009, printJson);
	}
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param customerOperate
	 * @throws Exception
	 */
	private void queryCustomerCardActivation(String tenancyId, int storeId, PosCustomerOperateListEntity customerOperate) throws Exception
	{
		if(SysDictionary.CUSTOMER_OPERATE_STATE_WAIT.equals(customerOperate.getOperate_state()))
		{
			//查询第三方支付状态
			this.queryThirdPaymentForCustomerCardActivation(tenancyId, storeId, customerOperate);
		}
		else
		{
			cardActivationService.queryCustomerCardActivationStatusForCRM(tenancyId, storeId, customerOperate);
		}
	}
	
	
	private void queryThirdPaymentForCustomerCardActivation(String tenancyId, int storeId, PosCustomerOperateListEntity customerOperate) throws Exception
	{
		JSONObject queryParamJson = new JSONObject();
		queryParamJson.put("aid_order_num", customerOperate.getThird_bill_code());
		queryParamJson.put("object_name", SysDictionary.THIRD_PAY_CHARGE);
		PosThirdPaymentOrderEntity thirdPayment = thirdPaymentWayService.getThirdPaymentOrder(tenancyId, storeId, queryParamJson);

		if (null != thirdPayment)
		{
			String paymentState = thirdPayment.getStatus();
			String operateState = customerOperate.getOperate_state();
			String requestStatus = customerOperate.getRequest_status();
			String requestCode = customerOperate.getRequest_code();
			String requestMsg = customerOperate.getRequest_msg();
			Timestamp finishTime = null;

			if (SysDictionary.THIRD_PAY_CHARGE.equals(thirdPayment.getObject_name()))
			{
				switch (paymentState)
				{
					case SysDictionary.THIRD_PAY_STATUS_SUCCESS:

						operateState = SysDictionary.CUSTOMER_OPERATE_STATE_PROCESS;

						JSONObject paymentJson = new JSONObject();
						paymentJson.put("payment_id", thirdPayment.getPayment_id().toString());
						paymentJson.put("amount", thirdPayment.getSettle_amount().toString());
						paymentJson.put("cashier_num", thirdPayment.getOpt_num().toString());
						paymentJson.put("rate", "1");

						paymentJson.put("bill_code", customerOperate.getThird_bill_code());

						List<JSONObject> paymentList = new ArrayList<JSONObject>();
						paymentList.add(paymentJson);

						JSONObject extJson = JSONObject.fromObject(customerOperate.getExtend_param());

						JSONObject paramJson = new JSONObject();
						paramJson.put("card_code", customerOperate.getCard_code());
						paramJson.put("third_code", customerOperate.getThird_code());
						paramJson.put("customer_code", customerOperate.getCustomer_code());
						paramJson.put("card_class_id", customerOperate.getCard_class_id());
						paramJson.put("pay_password", extJson.optString("pay_password"));
						paramJson.put("salesman", customerOperate.getSales_person());
						paramJson.put("deposit", customerOperate.getDeposit());
						paramJson.put("sales_price", customerOperate.getSales_price());
						paramJson.put("is_only", extJson.optString("is_only"));
						paramJson.put("is_physical_card", extJson.optString("is_physical_card"));
						paramJson.put("report_date", DateUtil.formatDate(customerOperate.getBusiness_date()));
						paramJson.put("shift_id", customerOperate.getShift_id());
						paramJson.put("chanel", customerOperate.getChanel());
						paramJson.put("opt_num", customerOperate.getOperator_id());
						paramJson.put("pos_num", customerOperate.getPos_num());
						paramJson.put("payment", paymentList);

						Data returnData = cardActivationService.customerCardActivationPost(tenancyId, storeId, paramJson);

						if (Constant.CODE_SUCCESS == returnData.getCode())
						{
							operateState = SysDictionary.CUSTOMER_OPERATE_STATE_SUCCESS;
							finishTime = DateUtil.currentTimestamp();
						}

						break;
					case SysDictionary.THIRD_PAY_STATUS_FAIL:
						//
						operateState = SysDictionary.CUSTOMER_OPERATE_STATE_FAIL;

						break;
					case SysDictionary.THIRD_PAY_STATUS_PAYING:
						// 返回待支付
						operateState = SysDictionary.CUSTOMER_OPERATE_STATE_WAIT;
						break;
					case SysDictionary.THIRD_PAY_STATUS_REFUND_SUCCESS:
					case SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL:
					case SysDictionary.THIRD_PAY_STATUS_REVOKED_SUCCESS:
					case SysDictionary.THIRD_PAY_STATUS_REVOKED_FAIL:
					case SysDictionary.THIRD_PAY_STATUS_REFUND:
					case SysDictionary.THIRD_PAY_STATUS_REVOKED:
						operateState = SysDictionary.CUSTOMER_OPERATE_STATE_FAIL;
						break;
					default:
						paymentState = SysDictionary.THIRD_PAY_STATUS_NOOK;
						operateState = SysDictionary.CUSTOMER_OPERATE_STATE_WAIT;
						break;
				}
			}

			JSONObject updateJson = new JSONObject();
			updateJson.put("bill_code", customerOperate.getThird_bill_code());
			updateJson.put("payment_state", paymentState);
			updateJson.put("operate_state", operateState);
			updateJson.put("request_status", requestStatus);
			updateJson.put("request_code", requestCode);
			updateJson.put("request_msg", requestMsg);
			updateJson.put("finish_time", DateUtil.format(finishTime));

			cardActivationService.updateCustomerOperateListStateForCardActivation(tenancyId, storeId, updateJson);
		}
	}
	
	
//	/**
//	 * 发卡成功打印小票
//	 * @param printJson
//	 * @param param
//	 * @throws Exception
//	 */
//	public void printCustomerCardActivation(String tenancyId, int storeId,String param){
//		JSONObject data  = null;
//		try{
//			data  = JSONObject.fromObject(param);
//		}catch(Exception e){
//			e.printStackTrace();
//			logger.info("参数转换JSON对象失败");
//		}
//		
//		/**打印start***/
//		PrintBean pb=new PrintBean();
//		pb.setTenancy_id(tenancyId);
//		pb.setStore_id(storeId);
//		pb.setMode("1");
//		pb.setPos_num(data.optString("pos_num"));
//		pb.setPrint_code(SysDictionary.PRINT_CODE_1013);
//		pb.setCard_code(data.optString("card_code"));
//		pb.setOperator(data.optString("opt_num"));
//		pb.setUpdatetime(DateUtil.getNowDateYYDDMMHHMMSS());
//		pb.setDeposit(data.getString("deposit")==null?"":data.getString("deposit")+"");
//		pb.setSales_price(data.getString("sales_price")==null?"":data.getString("sales_price")+"");
//		
//		try {
//			setPrint(pb,SysDictionary.OPERAT_TYPE_FK);
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//		/***打印 end **/
//	}
//
//	
//	/**
//	 * 打印小票
//	 * @param printJson
//	 * @param param
//	 * @throws Exception
//	 */
//	public void setPrint(PrintBean param,String type) throws Exception{
//		JSONObject printJson=new JSONObject();
//		printJson.put("card_code", param.getCard_code());
//		printJson.put("operator", param.getOperator());
//		printJson.put("updatetime", param.getUpdatetime());
//		
//		if(type.equals(SysDictionary.OPERAT_TYPE_FK)){//发卡
//			printJson.put("deposit", param.getDeposit());
//			printJson.put("sales_price", param.getSales_price());
//		}
//		if(posPrintNewService.isNONewPrint(param.getTenancy_id(), param.getStore_id())){ // 启用新打印
//			posPrintNewService.posPrintByMode(param.getTenancy_id(), param.getStore_id(), param.getPrint_code(), printJson);
//		}else{
//			customerDao.customerPrint(param.getTenancy_id(), param.getStore_id(), param.getPos_num(), param.getPrint_code(), param.getMode(), printJson, 1);
//		}
//	}
	
	
}
