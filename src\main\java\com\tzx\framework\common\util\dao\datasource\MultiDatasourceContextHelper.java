package com.tzx.framework.common.util.dao.datasource;

/**
 * 多数据源上下文处理器
 * 
 * <p>
 * Company: 北京国信华科科技有限公司
 * </p>
 * 
 * <AUTHOR>
 * @since 2014-06-01
 * @version 1.0
 */
public class MultiDatasourceContextHelper
{
	private static final ThreadLocal<Object>	contextHolder	= new ThreadLocal<Object>();

	public static void setDatasource(String dsName)
	{
		contextHolder.set(dsName);
	}

	public static String getDatasource()
	{
		return (String) contextHolder.get();
	}

	public static void clear()
	{
		contextHolder.remove();
	}

}
