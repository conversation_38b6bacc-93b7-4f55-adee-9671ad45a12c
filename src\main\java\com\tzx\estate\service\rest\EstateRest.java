package com.tzx.estate.service.rest;

import java.io.PrintWriter;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import net.sf.json.JSONObject;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.tzx.base.controller.PosBaseController;
import com.tzx.estate.base.Constant;
import com.tzx.estate.bo.EstateService;
import com.tzx.estate.common.entity.Data;
import com.tzx.estate.common.entity.Pagination;
import com.tzx.framework.common.exception.ErrorCode;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.ObjectMapper;
import com.tzx.framework.common.util.PropertiesLoader;
import com.tzx.framework.common.util.TokenUtil;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;

@Controller("EstateRest")
@RequestMapping("/estateRest")
public class EstateRest extends PosBaseController
{
	private static final Logger	logger	= Logger.getLogger(EstateRest.class);

	@Resource(name = EstateService.NAME)
	private EstateService			estateService;

	/**
	 * 请求入口
	 * 
	 * @return JSONObject
	 */
	@RequestMapping(value = "post", method = RequestMethod.POST)
	// @ResponseBody
	public void post(HttpServletRequest request, HttpServletResponse response, @RequestBody
	JSONObject jsobj)
	{
		response.setContentType("text/html; charset=UTF-8");
		PrintWriter out = null;
		JSONObject responseJson = null;

		ObjectMapper objectMapper = new ObjectMapper();
		Data param = null;
		try
		{
			param = objectMapper.readValue(jsobj.toString(), Data.class);
		}
		catch (Exception se)
		{
			se.printStackTrace();
			logger.error("Fastjson 类型解析错误" + ExceptionMessage.getExceptionMessage(se));
			// return buildErrorResult(Constant.CODE_PARAM_FAILURE,
			// "json转data类型错误，请查看传入参数");
			responseJson = buildErrorResult(Constant.CODE_PARAM_FAILURE, "json转data类型错误，请查看传入参数");
		}

		// 检验是否通过认证
		if (!TokenUtil.checkToken(param.getT(), param.getTenancy_id(), param.getSecret()))
		{
			// System.out.println(request.getLocalAddr() + "==" +
			// request.getRemoteHost());
			// System.out.println("验证信息：" + TokenUtil.checkToken(param.getT(),
			// param.getTenancy_id(), param.getSecret()));
		}

		String reqJson = JSONObject.fromObject(param).toString();

		logger.info("接收的请求：" + reqJson);

		try
		{
			DBContextHolder.setTenancyid(param.getTenancy_id());
		}
		catch (Exception e)
		{
			logger.error("切换数据源错误：" + ExceptionMessage.getExceptionMessage(e));
			e.printStackTrace();
			// return buildErrorResult(Constant.CODE_CHANGE_DATASOURCE_FAILURE,
			// Constant.POS_CHANGE_DATASOURCE_FAILURE);
			responseJson = buildErrorResult(Constant.CODE_CHANGE_DATASOURCE_FAILURE, Constant.CHANGE_DATASOURCE_FAILURE);
		}
		Data result = null; // 以前用Data.get(param),现在换成clone
		// try
		// {
		if (param.getPagination() == null)
		{
			param.setPagination(new Pagination());
		}
		result = param.clone();
		result.setData(null);
		// }
		// catch (CloneNotSupportedException e1)
		// {
		// logger.info(Constant.CLONE_DATA_FAILURE + "：" + e1);
		// e1.printStackTrace();
		// return buildErrorResult(Constant.CODE_INNER_EXCEPTION,
		// Constant.POS_CHANGE_DATASOURCE_FAILURE);
		// }

		// 判断门店状态是否正常，关店或装修暂停营业
		// JSONObject oobj = null;
		// try
		// {
		// oobj = this.isStoreNormal(param.getTenancy_id(),
		// param.getStore_id());
		// }catch (Exception se)
		// {
		// logger.info("查询门店状态：" + ExceptionMessage.getExceptionMessage(se));
		// result.setCode(Constant.CODE_INNER_EXCEPTION);
		// result.setMsg("查询门店状态失败");
		// JSONObject postData = JSONObject.fromObject(result);
		// return postData;
		// }
		//
		// if (oobj.optInt("code") != 0)
		// {
		// return oobj;
		// }

//		Data data = null;

		switch (param.getType())
		{
			case POST_SALES_CREATE:
				try
				{
					estateService.estatePostSalesCreate(param, result);
				}
				catch(SystemException se)
				{
					buildSysExceptionData(se, result, Constant.ESTATE_POST_SALES_FAILURE);
				}catch(Exception e)
				{
					buildExceptionData(e, result, Constant.ESTATE_POST_SALES_FAILURE);
				}
				break;
			default:
				result.setCode(Constant.CODE_PARAM_FAILURE);
				result.setMsg(Constant.NOT_EXISTS_TYPE);
				break;
		}

//		if (data != null)
//		{
//			result.setCode(data.getCode());
//			result.setMsg(data.getMsg());
//			result.setData(data.getData());
//		}
//
//		if (data != null && data.getPagination() == null)
//		{
//			Pagination page = result.getPagination();
//			page.setPageno(1);
//			List<?> lists = result.getData();
//			if (lists == null)
//			{
//				lists = new ArrayList<Object>();
//			}
//			page.setPagesize(lists.size());
//			page.setTotalcount(lists.size());
//		}
//		else if (data != null)
//		{
//			result.setPagination(data.getPagination());
//		}

		if (result.getCode() == 0)
		{
			result.setSuccess(true);
		}
		else
		{
			result.setSuccess(false);
		}

		// JSONObject postData = JSONObject.fromObject(result);
		// logger.info(postData.toString().length() + "<==长度，POS查询结果==>" +
		// postData.toString());
		// return postData;

		responseJson = JSONObject.fromObject(result);
		logger.info(responseJson.toString().length() + "<==长度，POS查询结果==>" + responseJson.toString());
		try
		{
			out = response.getWriter();
			out.print(responseJson.toString());
			out.flush();
			out.close();
		}
		catch (Exception e)
		{
		}
		finally
		{
			if (out != null) out.close();
		}
	}

	public void buildSysExceptionData(SystemException se, Data result, String message)
	{
		ErrorCode error = se.getErrorCode();
		String msg = se.getErrorMsg();
//		String msg = PropertiesLoader.getProperty(String.valueOf(error.getNumber()));
//		Map<String, Object> map = se.getProperties();
//		for (String key : map.keySet())
//		{
//			msg = msg.replace(key, String.valueOf(map.get(key)));
//		}

		result.setCode(error.getNumber());
		result.setMsg(msg);
		logger.error(message + ",原因：" + msg + ",错误码：" + error.getNumber());
		logger.error(ExceptionMessage.getExceptionMessage(se));
	}

	public void buildExceptionData(Exception e, Data result, String message)
	{
		result.setCode(Constant.CODE_INNER_EXCEPTION);
		result.setMsg(message);
		logger.error("系统内部错误：" + ExceptionMessage.getExceptionMessage(e));
		e.printStackTrace();
	}

	public void buildDataResult(int code, String msg, List<?> list, Data result)
	{
		result.setCode(code);
		result.setMsg(msg);

		if (list != null && list.size() != 0)
		{
			result.setData(list);
		}
	}

	public JSONObject buildErrorResult(int code, String msg)
	{
		JSONObject obj = new JSONObject();
		obj.put("code", code);
		obj.put("msg", msg);
		return obj;
	}
}
