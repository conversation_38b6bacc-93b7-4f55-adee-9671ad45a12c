package com.tzx.orders.bo.dto;

import java.io.Serializable;
import java.util.List;

public class PosBill implements Serializable {
    /**
     *
     */
    private static final long serialVersionUID = -3028442785468821697L;

    private String bill_num;
    private String report_date;
    private String shift_id;
    private String pos_num;
    private String waiter_num;
    private String opt_num;
    private String table_code;
    private String guest;
    private String order_num;
    private String bill_amount;
    private String payment_amount;
    private String difference;
    private String sale_mode;
    private String item_menu_id;
    private String service_id;
    private String service_amount;
    private String isprint;
    private String source;
    private String discount_amount;
    private String remark;
    private String shop_real_amount;
    private String settlement_type;
    private String check_mode;
    private String platform_charge_amount;
    private String discount_rate;
    private String need_invoice;
    private String take_meal_number;
    private String order_state;

    private List<PosBillItem> item;
    private List<PosBillPayment> payment;

    private String delivery_party;

    private String discount_mode_id; // 折扣方案
    private String customer_id;	// 会员号
    private String consigner_phone; // 会员手机号
    private String send_time;

    private String third_order_code;//saas外卖第三方平台订单号

    public String getOrder_state() {
        return order_state;
    }

    public void setOrder_state(String order_state) {
        this.order_state = order_state;
    }

    public String getSend_time() {
        return send_time;
    }

    public void setSend_time(String send_time) {
        this.send_time = send_time;
    }


    public String getDiscount_mode_id() {
		return discount_mode_id;
	}

	public void setDiscount_mode_id(String discount_mode_id) {
		this.discount_mode_id = discount_mode_id;
	}

	public String getCustomer_id() {
		return customer_id;
	}

	public void setCustomer_id(String customer_id) {
		this.customer_id = customer_id;
	}

	public String getConsigner_phone() {
		return consigner_phone;
	}

	public void setConsigner_phone(String consigner_phone) {
		this.consigner_phone = consigner_phone;
	}

	public String getDiscount_rate() {
        return discount_rate;
    }

    public void setDiscount_rate(String discount_rate) {
        this.discount_rate = discount_rate;
    }


    public String getSettlement_type() {
        return settlement_type;
    }

    public void setSettlement_type(String settlement_type) {
        this.settlement_type = settlement_type;
    }

    public String getCheck_mode() {
        return check_mode;
    }

    public void setCheck_mode(String check_mode) {
        this.check_mode = check_mode;
    }

    public String getIs_online_payment() {
        return is_online_payment;
    }

    public void setIs_online_payment(String is_online_payment) {
        this.is_online_payment = is_online_payment;
    }

    private String is_online_payment;


    public String getShop_real_amount() {
        return shop_real_amount;
    }

    public void setShop_real_amount(String shop_real_amount) {
        this.shop_real_amount = shop_real_amount;
    }

    public String getPlatform_charge_amount() {
        return platform_charge_amount;
    }

    public void setPlatform_charge_amount(String platform_charge_amount) {
        this.platform_charge_amount = platform_charge_amount;
    }



    public String getBill_num() {
        return bill_num;
    }

    public void setBill_num(String bill_num) {
        this.bill_num = bill_num;
    }

    public String getReport_date() {
        return report_date;
    }

    public void setReport_date(String report_date) {
        this.report_date = report_date;
    }

    public String getShift_id() {
        return shift_id;
    }

    public void setShift_id(String shift_id) {
        this.shift_id = shift_id;
    }

    public String getPos_num() {
        return pos_num;
    }

    public void setPos_num(String pos_num) {
        this.pos_num = pos_num;
    }

    public String getWaiter_num() {
        return waiter_num;
    }

    public void setWaiter_num(String waiter_num) {
        this.waiter_num = waiter_num;
    }

    public String getOpt_num() {
        return opt_num;
    }

    public void setOpt_num(String opt_num) {
        this.opt_num = opt_num;
    }

    public String getTable_code() {
        return table_code;
    }

    public void setTable_code(String table_code) {
        this.table_code = table_code;
    }

    public String getGuest() {
        return guest;
    }

    public void setGuest(String guest) {
        this.guest = guest;
    }

    public String getOrder_num() {
        return order_num;
    }

    public void setOrder_num(String order_num) {
        this.order_num = order_num;
    }

    public String getPayment_amount() {
        return payment_amount;
    }

    public void setPayment_amount(String payment_amount) {
        this.payment_amount = payment_amount;
    }

    public String getDifference() {
        return difference;
    }

    public void setDifference(String difference) {
        this.difference = difference;
    }

    public String getSale_mode() {
        return sale_mode;
    }

    public void setSale_mode(String sale_mode) {
        this.sale_mode = sale_mode;
    }

    public String getItem_menu_id() {
        return item_menu_id;
    }

    public void setItem_menu_id(String item_menu_id) {
        this.item_menu_id = item_menu_id;
    }

    public String getIsprint() {
        return isprint;
    }

    public void setIsprint(String isprint) {
        this.isprint = isprint;
    }

    public List<PosBillItem> getItem() {
        return item;
    }

    public void setItem(List<PosBillItem> item) {
        this.item = item;
    }

    public List<PosBillPayment> getPayment() {
        return payment;
    }

    public void setPayment(List<PosBillPayment> payment) {
        this.payment = payment;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getBill_amount() {
        return bill_amount;
    }

    public void setBill_amount(String bill_amount) {
        this.bill_amount = bill_amount;
    }

    public String getService_id() {
        return service_id;
    }

    public void setService_id(String service_id) {
        this.service_id = service_id;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getService_amount() {
        return service_amount;
    }

    public void setService_amount(String service_amount) {
        this.service_amount = service_amount;
    }

    public String getDiscount_amount() {
        return discount_amount;
    }

    public void setDiscount_amount(String discount_amount) {
        this.discount_amount = discount_amount;
    }

    public String getDelivery_party() {
        return delivery_party;
    }

    public void setDelivery_party(String delivery_party) {
        this.delivery_party = delivery_party;
    }

    public String getNeed_invoice() {
        return need_invoice;
    }

    public void setNeed_invoice(String need_invoice)
    {
        this.need_invoice = need_invoice;
    }

    public String getTake_meal_number() {
        return take_meal_number;
    }

    public void setTake_meal_number(String take_meal_number) {
        this.take_meal_number = take_meal_number;
    }

    @Override
    public String toString() {
        return new com.google.gson.Gson().toJson(this);
    }

    public String getThird_order_code() {
        return third_order_code;
    }

    public void setThird_order_code(String third_order_code) {
        this.third_order_code = third_order_code;
    }
}
