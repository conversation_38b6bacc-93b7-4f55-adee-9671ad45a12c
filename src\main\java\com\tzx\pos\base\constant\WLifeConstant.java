package com.tzx.pos.base.constant;

/**
 * 提供微生活对接的url等常量信息
 * Created by qin-gui on 2018-03-13.
 */
public class WLifeConstant {

	/** 上传订单url */
	public static final String	UPLOAD_ORDER_URL		= "/api/Order/uploadOrder";
	/** 门店上传基础资料 */
	public static final String	UPLOAD_SHOP_INFO_URL	= "/api/shop_info";
	/** 沽清 */
	public static final String	SOLD_OUT_URL			= "/api/sold_out";
	/** 取消沽清 */
	public static final String	SOLD_OUT_CALCEL_URL		= "/api/sold_out/calcel";
	/** 取消当前门店所有菜品沽清 */
	public static final String	SOLD_OUT_CANCEL_ALL_URL	= "/api/sold_out/cancel_all";
	/** 解锁订单 */
	public static final String	UNLOCK_ORDER_URL		= "/api/Order/unlockOrder";
	/** 清台 */
	public static final String	COMPLETE_ORDER_URL		= "/api/Order/completeOrder";
	/** 退单 */
	public static final String	CANCEL_ORDER_URL		= "/api/Order/orderQuit";
	/** 收银同步菜品接口 */
	public static final String	SYNCHRO_DATA_DISH_URL		= "/api/synchrodataDish";

	public static final String	BUSSINESS_ID			= "WLIFE_BUSINESS_ID";
	public static final String	BRAND_ID				= "WLIFE_BRAND_ID";
	public static final String	SHOP_ID					= "WLIFE_SHOP_ID";
	public static final String	SHOP_KEY				= "WLIFE_SHOP_KEY";
	public static final String	IS_USER_WLIFE			= "IS_USER_WLIFE";
	/** 上传菜类方式 , 1:上传大类, 2:上传小类 */
	public static final String	WLIFE_DISH_KINDS_MODE	= "WLIFE_DISH_KINDS_MODE";
	
	public static final String	USER_WLIFE_ORDER_TYPE_KEY		= "USER_WLIFE_ORDER_TYPE";

	public static final String	USER_WLIFE_ORDER_TYPE_H5		= "H5";
	public static final String	USER_WLIFE_ORDER_TYPE_PROGRAM	= "PROGRAM";
	//美味不用等小程序点餐
	public static final String	USER_WLIFE_ORDER_TYPE_MEIWEI_PROGRAM	= "MEIWEI_PROGRAM";
	
	/** 上传订单操作状态:微信点餐新订单 */
	public static final String	OPT_TYPE_NEW		= "1";
	/** 上传订单操作状态:加菜 */
	public static final String	OPT_TYPE_ADD		= "2";
	/** 上传订单操作状态:拉取订单 */
	public static final String	OPT_TYPE_LOAD		= "7";
	/** 上传订单操作状态:锁单上传订单 */
	public static final String	OPT_TYPE_LOCK		= "10";
	/** 上传订单操作状态:预结上传订单 */
	public static final String	OPT_TYPE_PAY		= "11";

	/** 桌态表的锁单状态，对应字段  */
	public static final String  LOCK_POS_NUM		="-1";
	public static final String  OPT_NAME			="微信锁单";
	public static final String  LOCK_OPT_NUM		="-1";

	/** 会员类型是微生活会员 */
	public static final String  WLIFE_MEMBER        ="acewill";
	/** 会员类型是saas会员 */
	public static final String  SAAS_MEMBER         ="saas";
	

	/** 订单来源：支付宝  */
	public static final String PAY_SOURCE_ALIPAY = "alipay";
	/** 订单来源：微信  */
	public static final String PAY_SOURCE_WEIXIN = "weixin";
	/** 订单来源：微生活储值  */
	public static final String PAY_SOURCE_BALANCE = "balance";
	/** 订单来源：微生活积分  */
	public static final String PAY_SOURCE_CREDIT = "credit";
	/** 订单来源：微生活代金卷 */
	public static final String PAY_SOURCE_COUPON = "coupon";
	/** 订单来源：微生菜品卷  */
	public static final String PAY_SOURCE_PRODUCT = "product";
	/** 订单来源：微生活支付 */
	public static final String PAY_SOURCE_WLIFE = "wlife";

	/** 订单来源：美味不用等支付宝  */
	public static final String PAY_SOURCE_MEIWEI_ALIPAY = "ali_pay_meiwei";
	/** 订单来源：美味不用等微信  */
	public static final String PAY_SOURCE_MEIWEI_WEIXIN = "wechat_pay_meiwei";

	/**
	 * 美团
	 */
	public static final String PAY_SOURCE_MEITUAN = "meituan";
	/**
	 * 抖音
	 */
	public static final String PAY_SOURCE_DOUYIN = "douyin";
	/**
	 * 支付宝劵
	 */
	public static final String PAY_SOURCE_ALIPAYCOUPON= "alipaycoupon";
	/**
	 * 小红书
	 */
	public static final String PAY_SOURCE_LITTLE_RED_BOOK = "littleRedBook";

	
	/** 门店版本 */
	public static final String	WLIFE_SHOP_VERSION		= "2.11.2";
	/** 订单模式, 2:先付 3:后付 */
	public static final String	WLIFE_ORDER_MODE		= "3";
	public static final String	PRE_WLIFE_ORDER_MODE		= "2";

	/**
	 * 微生活小程序
	 */
	/** 转台url */
	public static final String	CHANGE_TABLE_URL		= "/api/Order/changeTable";
	/** 并台url */
	public static final String	COMBINE_ORDER_URL		= "/api/Order/combineOrder";
	/** 沽清url */
	public static final String	PROGRAM_SOLD_OUT_URL	= "/api/sould_out";
	/** 取消沽清url */
	public static final String	PROGRAM_CANCEL_SOLD_OUT_URL	= "/api/sould_out/calcel";
	/** 门店退单url */
	public static final String	ORDER_QUIT_URL	= "/api/Order/orderQuit";
	/** 门店退款url */
	public static final String	ORDER_REFUND_URL	= "/api/Order/refund";

//	/** 调用微生活接口查询会员卡的等级 */
//	public static final String  WLIFE_MEMBER_GRADES  =  "/grade/rule";
	/** 微生活小程序退款appkey */
	public static final String	WLIFE_PROGRAM_APP_KEY			= "WLIFE_PROGRAM_APP_KEY";

	/** 同步菜品类型: 菜品*/
	public static final String	SYNC_DISH_DATA_TYPE_DISH	= "dish";
	/** 同步菜品类型: 菜类*/
	public static final String	SYNC_DISH_DATA_TYPE_KIND	= "dishkind";
	/** 同步菜品类型: 菜品菜类*/
	public static final String	SYNC_DISH_DATA_TYPE_ALL		= "all";
	
	/** 同步菜品方式: 重置数据（在.net后台所做修改将被覆盖）*/
	public static final String	SYNC_DISH_DATA_FORCE_RESET		= "1";
	/** 同步菜品方式: 同步*/
	public static final String	SYNC_DISH_DATA_FORCE_SYNC		= "0";
}
