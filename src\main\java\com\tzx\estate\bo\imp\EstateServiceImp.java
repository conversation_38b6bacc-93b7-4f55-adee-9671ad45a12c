package com.tzx.estate.bo.imp;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.codehaus.xfire.client.Client;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;

import com.tzx.estate.base.Constant;
import com.tzx.estate.base.constant.SysDictionary;
import com.tzx.estate.base.util.ReqDataUtil;
import com.tzx.estate.bo.EstateService;
import com.tzx.estate.common.entity.Data;
import com.tzx.estate.po.springjdbc.dao.EstateDao;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.Scm;
import com.tzx.pos.base.util.ParamUtil;

@Service(EstateService.NAME)
public class EstateServiceImp implements EstateService
{	
	private static final Logger logger = Logger.getLogger(EstateServiceImp.class);
	
	@Resource(name=EstateDao.NAME)
	private EstateDao estateDao;
	
	
	public void estatePostSalesQlql(Data param,Data result) throws SystemException,Exception
	{
		String tenantId = param.getTenancy_id();
		Integer organId = param.getStore_id();
		
		String sqlParam = "select trim(para_value) as para_value from sys_parameter where para_code = ? and valid_state='1'";

		SqlRowSet rsParam = estateDao.query4SqlRowSet(sqlParam, new Object[]{ "estate_url" });
		String postUrl = null;
		if (rsParam.next())
		{
			postUrl = rsParam.getString("para_value");
		}
		
//		String postUrl = "http://101.230.12.198:8086/salestrans.asmx"; 正是库地址
		String soapAction = "http://tempurl.org/postsalescreate";
		
		Map<String, Object> map = ReqDataUtil.getDataMap(param);
		
		Date reportDate = ParamUtil.getDateValue(map, "report_date", false, null);
		//String optName = ParamUtil.getStringValue(map, "opt_name", false, null);
		
		SimpleDateFormat simpleyyyyMMddFormat = new SimpleDateFormat("yyyyMMdd");
		SimpleDateFormat simpleTimeFormat = new SimpleDateFormat("HHMMss");
		
		StringBuffer billSql = new StringBuffer("select pb.payment_time,pb.bill_state,pb.bill_num,pb.cashier_num,pb.bill_amount,pb.payment_amount,coalesce(pb.discount_amount,0) as discount_amount,o.organ_code from pos_bill pb inner join organ o on pb.store_id=o.id where pb.bill_num not in (select pe.bill_num from pos_estate pe where pe.tenancy_id=? and pe.store_id=? and pe.estate_type='qileqile') and pb.report_date=? and pb.tenancy_id=? and pb.store_id=? and pb.bill_property='CLOSED'");
		billSql.append(" union all select pb2.payment_time,pb2.bill_state,pb2.bill_num,pb2.cashier_num,pb2.bill_amount,pb2.payment_amount,coalesce(pb2.discount_amount,0) as discount_amount,o.organ_code from pos_bill2 pb2 inner join organ o on pb2.store_id=o.id where pb2.bill_num not in (select pe.bill_num from pos_estate pe where pe.tenancy_id=? and pe.store_id=? and pe.estate_type='qileqile') and pb2.report_date=? and pb2.tenancy_id=? and pb2.store_id=? and pb2.bill_property='CLOSED'");
		
	    StringBuffer billItemCount = new StringBuffer("select count(pbi.id) as item_count from pos_bill_item pbi where pbi.report_date=? and pbi.bill_num=? and pbi.tenancy_id=? and pbi.store_id=?");
	     
	    StringBuffer billItem2Count = new StringBuffer("select count(pbi2.id) as item_count from pos_bill_item2 pbi2 where pbi2.report_date=? and pbi2.bill_num=? and pbi2.tenancy_id=? and pbi2.store_id=?");
		
		StringBuffer billItemSql = new StringBuffer("select pbi.rwid,pbi.item_num,pbi.item_count,pbi.item_amount,pbi.real_amount,coalesce(pbi.discountr_amount,0) as discountr_amount,coalesce(pbi.discount_amount,0) as discount_amount from pos_bill_item pbi where pbi.report_date=? and pbi.bill_num=? and pbi.tenancy_id=? and pbi.store_id=?");
		billItemSql.append(" union all select pbi2.rwid,pbi2.item_num,pbi2.item_count,pbi2.item_amount,pbi2.real_amount,coalesce(pbi2.discountr_amount,0) as discountr_amount,coalesce(pbi2.discount_amount,0) as discount_amount from pos_bill_item2 pbi2 where pbi2.report_date=? and pbi2.bill_num=? and pbi2.tenancy_id=? and pbi2.store_id=?");
		
		StringBuffer billPaymentSql = new StringBuffer("select pbp.id,pbp.jzid,pbp.amount,pbp.count from pos_bill_payment pbp where pbp.report_date=? and pbp.bill_num=? and pbp.tenancy_id=? and pbp.store_id=?");
		billPaymentSql.append(" union all select pbp2.id,pbp2.jzid,pbp2.amount,pbp2.count from pos_bill_payment2 pbp2 where pbp2.report_date=? and pbp2.bill_num=? and pbp2.tenancy_id=? and pbp2.store_id=?");
		
		SqlRowSet rsBill = estateDao.query4SqlRowSet(billSql.toString(), new Object[]{tenantId, organId, reportDate, tenantId, organId, tenantId, organId, reportDate, tenantId, organId});
		
		int count = 0;
		int successCount = 0;
		while (rsBill.next())
		{
			int itemCountNum = this.estateDao.queryForInt(billItemCount.toString(), new Object[] { reportDate, rsBill.getString("bill_num"), tenantId, organId });
			
			int item2CountNum = this.estateDao.queryForInt(billItem2Count.toString(), new Object[] { reportDate, rsBill.getString("bill_num"), tenantId, organId });
			
			if (itemCountNum + item2CountNum > 0)
			{
				count = count + 1;
				String xml = "<?xml version=\"1.0\" encoding=\"utf-16\"?>";
				xml += "<soap:Envelope xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\">";
				xml += "<soap:Body>";
				xml += "<postsalescreate xmlns=\"http://tempurl.org\">";
				xml += "<astr_request>";
				// 标头信息
				xml += "<header>";
				// 许可协议号
				xml += "<licensekey>02101LG133B0101</licensekey>";
				// 用户账号
				xml += "<username>02101LG133B01</username>";
				// 用户密码
				xml += "<password>02101LG133B01</password>";
				xml += "<lang />";
				xml += "<pagerecords>100</pagerecords>";
				xml += "<pageno>1</pageno>";
				xml += "<updatecount>0</updatecount>";
				xml += "<messagetype>SALESDATA</messagetype>";
				xml += "<messageid>332</messageid>";
				xml += "<version>V332M</version>";
				xml += "</header>";
				
				// 销售单主表
				xml += "<salestotal>";
				xml += "<localstorecode>" + rsBill.getString("organ_code") + "</localstorecode>";
				xml += "<reservedocno />";
				
				xml += "<txdate_yyyymmdd>" + rsBill.getString("payment_time").substring(0, 10).replaceAll("-", "") + "</txdate_yyyymmdd>";		
				xml += "<txtime_hhmmss>" + rsBill.getString("payment_time").substring(11, 19).replaceAll(":", "") + "</txtime_hhmmss>";
				// 商场编号
				xml += "<mallid>" + "02101" + "</mallid>";
				// 交易店铺号
				xml += "<storecode>" + "02101LG133B01" + "</storecode>";
				xml += "<tillid>01</tillid>";
				
				if (StringUtils.equals("CJ01", rsBill.getString("bill_state")))
				{
					xml += "<salestype>" + "SR" + "</salestype>";
				} else
				{
					xml += "<salestype>" + "SA" + "</salestype>";
				}

				xml += "<txdocno>" + rsBill.getString("bill_num") + "</txdocno>";
				xml += "<orgtxdate_yyyymmdd />";
				xml += "<orgstorecode />";
				xml += "<orgtillid />";
				xml += "<orgtxdocno />";
				xml += "<mallitemcode />";
				xml += "<cashier>" + rsBill.getString("cashier_num") + "</cashier>";
				xml += "<vipcode />";
				xml += "<salesman />";
				xml += "<demographiccode />";
				xml += "<demographicdata />";
				xml += "<netqty>1</netqty>";
				xml += "<originalamount>" + rsBill.getDouble("bill_amount") + "</originalamount>";
				xml += "<sellingamount>" + rsBill.getDouble("payment_amount") + "</sellingamount>";
				xml += "<couponnumber />";
				xml += "<coupongroup />";
				xml += "<coupontype />";
				xml += "<couponqty>0</couponqty>";
				xml += "<totaldiscount>";
				xml += "<salesdiscount>";
				xml += "<discountapprove />";
				xml += "<discountmode />";
				xml += "<discountvalue>0</discountvalue>";
				xml += "<discountless>0</discountless>";
				xml += "</salesdiscount>";
				xml += "</totaldiscount>";
				xml += "<ttltaxamount1>0</ttltaxamount1>";
				xml +="<ttltaxamount2>0</ttltaxamount2>";
				xml += "<netamount>" + rsBill.getDouble("payment_amount") + "</netamount>";
				xml += "<paidamount>" + rsBill.getDouble("payment_amount") + "</paidamount>";
				xml += "<changeamount>1</changeamount>";
				xml += "<priceincludetax />";
				xml += "<shoptaxgroup />";
				xml += "<extendparam />";
				xml += "<invoicetitle />";
				xml += "<invoicecontent />";
				xml += "<issueby>" + rsBill.getString("cashier_num")  + "</issueby>";//创建人optName
				xml += "<issuedate_yyyymmdd>" + simpleyyyyMMddFormat.format(new Date()) + "</issuedate_yyyymmdd>";
				xml += "<issuetime_hhmmss>" + simpleTimeFormat.format(new Date()) + "</issuetime_hhmmss>";
				xml += "<ecorderno />";
				xml += "<buyerremark />";
				xml += "<orderremark />";
				xml += "<status>10</status>";
				xml += "<ttpossalesdocno />";
				xml += "</salestotal>";
				
				// 销售单货品明细表
				xml += "<salesitems>";
				
				SqlRowSet rsBillItem = estateDao.query4SqlRowSet(billItemSql.toString(), new Object[]{reportDate, rsBill.getString("bill_num"), tenantId, organId, reportDate, rsBill.getString("bill_num"), tenantId, organId});

				while (rsBillItem.next())
				{
					xml += "<salesitem>";
					xml += "<iscounteritemcode>1</iscounteritemcode>";
					xml += "<lineno>" + rsBillItem.getString("rwid") + "</lineno>";
					// 交易店铺号
					xml += "<storecode>" + "02101LG133B01" + "</storecode>";
					xml += "<mallitemcode>02101LG133B011</mallitemcode>";
					xml += "<counteritemcode>02101LG133B011</counteritemcode>";
					// 交易货号
					xml += "<itemcode>02101LG133B011</itemcode>";
					xml += "<plucode>02101LG133B011</plucode>";//plucode和itemcode相同
					xml += "<colorcode />";
					xml += "<sizecode />";
					xml += "<itemlotnum>*</itemlotnum>";
					xml += "<serialnum />";
					xml += "<isdeposit>0</isdeposit>";
					xml += "<iswholesale>0</iswholesale>";
					xml += "<invttype>1</invttype>";
					xml += "<qty>" + rsBillItem.getDouble("item_count") + "</qty>";
					xml += "<exstk2sales>1.00</exstk2sales>";
					xml += "<originalprice>" + rsBillItem.getDouble("item_amount") + "</originalprice>";
					xml += "<sellingprice>" + rsBillItem.getDouble("real_amount") + "</sellingprice>";
					xml += "<pricemode />";
					xml += "<priceapprove />";
					xml += "<couponnumber />";
					xml += "<coupongroup />";
					xml += "<coupontype />";
					xml += "<itemdiscount>";
					xml += "<salesdiscount>";
					xml += "<discountapprove />";
					xml += "<discountmode />";
					xml += "<discountvalue>0</discountvalue>";
					xml += "<discountless>0</discountless>";
					xml += "</salesdiscount>";
					xml += "</itemdiscount>";
					xml += "<vipdiscountpercent>0</vipdiscountpercent>";
					xml += "<vipdiscountless>0</vipdiscountless>";
					xml += "<promotion>";
					xml += "<salespromtion>";
					xml += "<promotionid />";
					xml += "<promotionuseqty>0</promotionuseqty>";
					xml += "<promotionless>0</promotionless>";
					xml += "<promotionpkgcount>0</promotionpkgcount>";
					xml += "</salespromtion>";
					xml += "</promotion>";
					xml += "<totaldiscountless1>0</totaldiscountless1>";
					xml += "<totaldiscountless2>0</totaldiscountless2>";
					xml += "<totaldiscountless>0</totaldiscountless>";
					xml += "<tax>";
					xml += "<salestax>";
					xml += "<taxrate>0</taxrate>";
					xml += "<taxamount>0</taxamount>";
					xml += "</salestax>";
					xml += "</tax>";
					xml += "<netamount>" + rsBillItem.getDouble("real_amount") + "</netamount>";
					xml += "<bonusearn>0</bonusearn>";
					xml += "<salesitemremark />";
					xml += "<refundreasoncode />";
					xml += "<extendparam />";
					xml += "</salesitem>";
				}
				xml += "</salesitems>";
				
				// 销售单付款明细表
				xml += "<salestenders>";
				
				SqlRowSet rsBillPayment = estateDao.query4SqlRowSet(billPaymentSql.toString(), new Object[]{reportDate, rsBill.getString("bill_num"), tenantId, organId, reportDate, rsBill.getString("bill_num"), tenantId, organId});

				while (rsBillPayment.next())
				{
					xml += "<salestender>";
					xml += "<lineno>" + rsBillPayment.getString("id") + "</lineno>";
					xml += "<tendercode>OT</tendercode>";
					xml += "<tendertype>0</tendertype>";
					xml += "<tendercategory>" + rsBillPayment.getString("jzid") + "</tendercategory>";
					
					double paymount = Scm.pmui(rsBillPayment.getDouble("amount"),  Math.abs(rsBillPayment.getDouble("count")));
					
					xml += "<payamount>" + paymount + "</payamount>";
					xml += "<baseamount>" + paymount + "</baseamount>";
					xml += "<excessamount>0</excessamount>";
					xml += "<extendparam />";
					xml += "<remark />";
					xml += "</salestender>";
				}			
				xml += "</salestenders>";
				
				// 销售单配送表
				xml += "<salesdlvy>";
				xml += "<receiver_name />";
				xml += "<receiver_country />";
				xml += "<receiver_province />";
				xml += "<receiver_city />";
				xml += "<receiver_district />";
				xml += "<receiver_address1 />";
				xml += "<receiver_address2 />";
				xml += "<receiver_address3 />";
				xml += "<receiver_address4 />";
				xml += "<receiver_postal />";
				xml += "<receiver_mobile />";
				xml += "<receiver_phone />";
				xml += "<logisticscompany />";
				xml += "<logisticsdocno />";
				xml += "<expectdeliverydate_yyyymmdd />";
				xml += "<deliveryremarks />";
				xml += "</salesdlvy>";
				xml += "</astr_request>";
				xml += "</postsalescreate>";
				xml += "</soap:Body>";
				xml += "</soap:Envelope>";

				try {
					URL url = new URL(postUrl);

					URLConnection connection = url.openConnection();
					HttpURLConnection httpconn = (HttpURLConnection) connection;
					ByteArrayOutputStream bout = new ByteArrayOutputStream();
					bout.write(xml.getBytes());
					logger.info("上传的xml数据为：" + xml);
					byte[] b = bout.toByteArray();
					httpconn.setRequestProperty("Content-Length", String
							.valueOf(b.length));
					httpconn.setRequestProperty("Content-Type",
							"text/xml; charset=gb2312");
					httpconn.setRequestProperty("SOAPAction", soapAction);
					httpconn.setRequestMethod("POST");
					httpconn.setDoInput(true);
					httpconn.setDoOutput(true);

					OutputStream out = httpconn.getOutputStream();
					out.write(b);
					out.close();

					InputStream isr = httpconn.getInputStream();
					StringBuffer buff = new StringBuffer();
					byte[] byte_receive = new byte[10240];
					for (int i = 0; (i = isr.read(byte_receive)) != -1;) {
						buff.append(new String(byte_receive, 0, i));
					}
					isr.close();
					String result_before = buff.toString();
					
					logger.info("返回结果为：" + result_before);
					
					int beginIndex = result_before.indexOf("<responsecode>") + 14;
					int endIndex = result_before.indexOf("</responsecode>");
					String responseCode = result_before.substring(beginIndex, endIndex);
					if ("0".equals(responseCode))
					{
						successCount = successCount + 1;
						
						String insertSql = new String("insert into pos_estate (tenancy_id,store_id,bill_num,report_date,estate_type) values (?,?,?,?,?)");
						estateDao.update(insertSql, new Object[]{tenantId, organId, rsBill.getString("bill_num"), reportDate, "qileqile"});
					}
				} catch (Exception e) {
					logger.error("上传销售数据失败：" + ExceptionMessage.getExceptionMessage(e));
					result.setCode(Constant.CODE_INNER_EXCEPTION);
					result.setMsg(Constant.ESTATE_POST_SALES_FAILURE);
					e.printStackTrace();
				}
			}
			else
			{
				logger.info("该账单为空账单，不做数据上传。账单号为：" + rsBill.getString("bill_num"));
			}
		}
		if (successCount == 0 && count == 0)
		{
			result.setCode(Constant.CODE_INNER_EXCEPTION);
			result.setMsg(Constant.ESTATE_POST_SALES_FIND_FAILURE);
		} else if (successCount == count)
		{
			result.setCode(Constant.CODE_SUCCESS);
			result.setMsg(Constant.ESTATE_POST_SALES_SUCCESS);
		} else if (successCount > 0 && successCount < count)
		{
			result.setCode(Constant.CODE_INNER_EXCEPTION);
			result.setMsg(Constant.ESTATE_POST_SALES_PART_FAILURE);
		} else
		{
			result.setCode(Constant.CODE_INNER_EXCEPTION);
			result.setMsg(Constant.ESTATE_POST_SALES_FAILURE);	
		}
		
		/**
		if (successCount == count)
		{
			result.setCode(Constant.CODE_SUCCESS);
			result.setMsg(Constant.ESTATE_POST_SALES_SUCCESS);
		} else if (successCount > 0 && successCount < count)
		{
			result.setCode(Constant.CODE_INNER_EXCEPTION);
			result.setMsg(Constant.ESTATE_POST_SALES_PART_FAILURE);
		} else
		{
			result.setCode(Constant.CODE_INNER_EXCEPTION);
			result.setMsg(Constant.ESTATE_POST_SALES_FAILURE);	
		}
		**/
		
	}
	
	
	public void estatePostSalesBljl(Data param, Data result) throws SystemException, Exception
	{
		String tenantId = param.getTenancy_id();
		Integer organId = param.getStore_id();

		Map<String, Object> map = ReqDataUtil.getDataMap(param);
		
		String sqlParam = "select trim(para_value) as para_value from sys_parameter where para_code = ? and valid_state='1'";
		
		SqlRowSet rsParam = estateDao.query4SqlRowSet(sqlParam, new Object[]{ "estate_url" });
		String url = null;
		if (rsParam.next())
		{
			url = rsParam.getString("para_value");
		}

//		String url = "http://218.17.44.5:7001/frdif/n_frdif.asmx?WSDL";
		
		Date reportDate = ParamUtil.getDateValue(map, "report_date", false, null);
		
		int count = 0;
		int successCount = 0;
		
		// 销售数据查询
		StringBuilder pbsql = new StringBuilder("select pb.bill_num,pb.payment_time,pb.bill_state,pb.payment_amount from pos_bill pb where pb.report_date = ? and pb.store_id = ? and pb.tenancy_id = ? and pb.bill_property='CLOSED' and pb.bill_num not in (select pe.bill_num from pos_estate pe where pe.store_id=? and pe.tenancy_id=? and pe.estate_type=?)");
		pbsql.append(" union all select pb2.bill_num,pb2.payment_time,pb2.bill_state,pb2.payment_amount from pos_bill2 pb2 where pb2.report_date = ? and pb2.store_id = ? and pb2.tenancy_id = ? and pb2.bill_property='CLOSED' and pb2.bill_num not in (select pe.bill_num from pos_estate pe where pe.store_id=? and pe.tenancy_id=? and pe.estate_type=?)");
		SqlRowSet pbRs = estateDao.query4SqlRowSet(pbsql.toString(),new Object[]{reportDate,organId,tenantId,organId,tenantId,"bljl",reportDate,organId,tenantId,organId,tenantId,"bljl"});
		while (pbRs.next())
		{
			String billState = null;
			if (StringUtils.equals("CJ01", pbRs.getString("bill_state")))
			{
				billState = "4";
			} else
			{
				billState = "1";
			}
			try{
				count = count + 1;
				String paymentTime = pbRs.getString("payment_time").substring(0, 19);
				Integer ret = null;
				Client client = new Client(new URL(url));
				String inputpara = "20101060002,0000," + pbRs.getString("bill_num") + "," + paymentTime + "," + billState + ",0001017,1,1," + String.valueOf(Math.abs(pbRs.getDouble("payment_amount"))) + ",01,,," + pbRs.getString("bill_num");
				
				Object paramObjs[]={"mcyp", "1B6BF1AD1829D895", "2000", inputpara,"",0,""};
				Object results[] = client.invoke("processdata", paramObjs);
				ret = Integer.valueOf(results[2].toString());

				if (ret >= 0)
				{
					successCount = successCount + 1;
					String insertSql = new String("insert into pos_estate (tenancy_id,store_id,bill_num,report_date,estate_type) values (?,?,?,?,?)");
					estateDao.update(insertSql, new Object[]{tenantId, organId, pbRs.getString("bill_num"), reportDate, "bljl"});
				}
			} catch (Exception e)
			{
				logger.error("上传销售数据失败：" + ExceptionMessage.getExceptionMessage(e));
				result.setCode(Constant.CODE_INNER_EXCEPTION);
				result.setMsg(Constant.ESTATE_POST_SALES_FAILURE);
			}
		}
		
		if (successCount == count)
		{
			result.setCode(Constant.CODE_SUCCESS);
			result.setMsg(Constant.ESTATE_POST_SALES_SUCCESS);
		} else if (successCount > 0 && successCount < count)
		{
			result.setCode(Constant.CODE_INNER_EXCEPTION);
			result.setMsg(Constant.ESTATE_POST_SALES_PART_FAILURE);
		} else
		{
			result.setCode(Constant.CODE_INNER_EXCEPTION);
			result.setMsg(Constant.ESTATE_POST_SALES_FAILURE);	
		}
	}
	
	
	public void estatePostSalesHlbzf(Data param, Data result)
			throws SystemException, Exception {
		
		final String UPLOAD_TAG_WRITE_DBNAME  = "hlbzf";
		final String UPLOAD_SERVICE_DETAILED = "%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,,,%s";
		
		String tenantId = param.getTenancy_id();
		Integer organId = param.getStore_id();

		Map<String, Object> map = ReqDataUtil.getDataMap(param);
		Date reportDate = ParamUtil.getDateValue(map, "report_date", false, null);
		
		int count = 0;
		int successCount = 0;
		
		//获取物业上传接口地址
		String Posturl = estateDao.getParamCodeByName("estate_url");
		
		StringBuilder sqlSale = new StringBuilder();
		sqlSale.append(" SELECT * FROM ( ");
		sqlSale.append("                  SELECT pbi2.tenancy_id,pbi2.store_id,pbi2.item_num,pbi2.item_mac_id,");
		sqlSale.append("                  pb2.payment_time,round(coalesce(pbi2.item_price,0),4) as item_price, ");
		sqlSale.append("                  round(coalesce(pbi2.item_count,0),2) as item_count, ");
		sqlSale.append("                  round(coalesce(pbi2.real_amount,0),2) as real_amount,pb2.bill_num ");
		sqlSale.append("                  from pos_bill2 AS pb2 JOIN pos_bill_item2 AS pbi2  ");
		sqlSale.append("                  on pbi2.bill_num = pb2.bill_num and pbi2.tenancy_id = pbi2.tenancy_id and pbi2.store_id = pb2.store_id");
		sqlSale.append("                  WHERE pb2.tenancy_id = ? AND pb2.store_id = ? and pb2.report_date =  ? ");//
		sqlSale.append("                  AND coalesce(pbi2.item_remark,'') = ? and coalesce(pbi2.item_property,'') <> ? AND pb2.bill_property = ? ");
		sqlSale.append("               )pb ");
		sqlSale.append(" where NOT EXISTS( ");
		sqlSale.append(" SELECT bill_num from pos_estate as pe WHERE pe.tenancy_id = pb.tenancy_id ");
		sqlSale.append(" AND pe.store_id = pb.store_id AND pe.bill_num = pb.bill_num AND pe.estate_type = ? ); ");
		sqlSale.append("");
		sqlSale.append("");
		sqlSale.append("");
		SqlRowSet rsSale = estateDao.query4SqlRowSet(sqlSale.toString(),new Object[]{
			tenantId, organId, reportDate, "", SysDictionary.ESTATE_ITEM_PROPERTY_MEALLIST,"CLOSED", UPLOAD_TAG_WRITE_DBNAME
		});
		
		try
		{
			String bill_Num_Postfix = null;
			String bill_Num = null;
			String paymentTime = null;
			String pos_num = null;
			
			while (rsSale.next())
			{
				count = count + 1;
				
				//只取后四位POS几号
				pos_num = rsSale.getString("item_mac_id");
				pos_num = pos_num.substring(pos_num.length() - 4, pos_num.length());
				paymentTime = rsSale.getString("payment_time").substring(0, 19);
				bill_Num = rsSale.getString("bill_num");
				//取出账单编号后六位
				bill_Num_Postfix = bill_Num.substring(bill_Num.length() - 6, bill_Num.length());
				bill_Num_Postfix = String.valueOf(Integer.parseInt(bill_Num_Postfix));
				
				String uploadDetailed = String.format(UPLOAD_SERVICE_DETAILED, 
						"10105030001",//店铺编号
						pos_num,     //收银机号
						bill_Num_Postfix,//小票号,每天从1累加
						paymentTime,//交易时间
						"1",//交易类型 1-销售 2-红冲销售 4-退货 5-红冲退货
						rsSale.getString("item_num"),//商品代码,每商户唯 一且有仅一个代码，表示该商户全部商品
						rsSale.getString("item_price"),//单价  4位小数
						rsSale.getString("item_count"),//数量  2位小数
						rsSale.getString("real_amount"),//实收金额 2位小数(均为正数通过交易类型来区分)
						"01",//付款方式
						bill_Num//商户小票流水号
						);
				Object uploadObjs[] = { "mcyp", "1B6BF1AD1829D895", "2000", uploadDetailed,"",0,"" };
				Client client = new Client(new URL(Posturl));
				Object results[] = client.invoke("processdata", uploadObjs);
				Integer ret = Integer.valueOf(results[2].toString());
				if ( ret >= 0)
				{
					successCount = successCount + 1;
					String insertSql = new String(
							"insert into pos_estate (tenancy_id,store_id,bill_num,report_date,estate_type) values (?,?,?,?,?)");
					estateDao.update(insertSql, new Object[] { tenantId,
							organId, bill_Num, reportDate,UPLOAD_TAG_WRITE_DBNAME });
				}
			}
		}catch(Exception e)
		{
			logger.error("上传销售数据失败：" + ExceptionMessage.getExceptionMessage(e));
			result.setCode(Constant.CODE_INNER_EXCEPTION);
			result.setMsg(Constant.ESTATE_POST_SALES_FAILURE);
		}
		if (successCount == count)
		{
			result.setCode(Constant.CODE_SUCCESS);
			result.setMsg(Constant.ESTATE_POST_SALES_SUCCESS);
			
		} else if (successCount > 0 && successCount < count)
		{
			result.setCode(Constant.CODE_INNER_EXCEPTION);
			result.setMsg(Constant.ESTATE_POST_SALES_PART_FAILURE);
			
		} else
		{
			result.setCode(Constant.CODE_INNER_EXCEPTION);
			result.setMsg(Constant.ESTATE_POST_SALES_FAILURE);
			
		}
	}

	@Override
	public void estatePostSalesCreate(Data param, Data result)
			throws SystemException, Exception {
		// TODO Auto-generated method stub
		
		String estateType = estateDao.getParamCodeByName("estate_type");
		
		switch (estateType.toUpperCase())
		{
		    case SysDictionary.ESTATE_POST_SALES_QLQL://齐乐齐乐
			    estatePostSalesQlql(param, result);
			    break;
		    case SysDictionary.ESTATE_POST_SALES_BLJL://百利家乐
		    	estatePostSalesBljl(param, result);
		    	break;
		    case SysDictionary.ESTATE_POST_SALES_HLBZF:///欢乐煲仔饭
		    	estatePostSalesHlbzf(param, result);
		    	break;
		    default:
		    	result.setCode(Constant.CODE_NULL_DATASET);
				result.setMsg(Constant.ESTATE_POST_SALES_PARAMNULL);
		    	break;
		}
	}
}
