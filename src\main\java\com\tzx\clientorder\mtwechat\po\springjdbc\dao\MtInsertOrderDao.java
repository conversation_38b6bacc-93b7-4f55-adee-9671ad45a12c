package com.tzx.clientorder.mtwechat.po.springjdbc.dao;

import net.sf.json.JSONObject;

/**
 * Created by qingui on 2018-06-08.
 */
public interface MtInsertOrderDao {
    String NAME = "com.tzx.clientorder.mewechat.po.springjdbc.dao.MtInsertOrderDaoImp";

    /**
     * 查询支付方式信息
     * @param tenancyId
     * @param code
     * @return
     */
    JSONObject getPaymentWay(String tenancyId, String code);

    /**
     * 保存美团的先付订单的支付信息
     */
    public void insertPayInfo(String tenancyId, JSONObject payInfo) throws Exception;

    /**
     * 更改账单状态为
     * @param tenancyId
     * @param billNum
     * @throws Exception
     */
    void updatePosBill(String tenancyId, String billNum) throws Exception;
}
