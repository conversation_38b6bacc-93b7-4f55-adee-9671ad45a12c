#\u7F8E\u5927CRM\u6A21\u5757\u767B\u5F55\u6388\u6743URL
xmdcrm.gettoken.url=https://open-erp.meituan.com
#\u7F8E\u5927CRM\u6A21\u5757UISDKURL
xmdcrm.uisdk.url=https://vipsdk.meituan.com
#\u7F8E\u5927CRM\u6A21\u5757\u8BF7\u6C42\u63A5\u53E3URL
xmdcrm.request.url=http://api.open.cater.meituan.com
#\u65B0\u7F8E\u5927CRM\u5BF9\u63A5 \u5F00\u53D1\u8005ID
xmdcrm.developerid=100113
#\u65B0\u7F8E\u5927CRM\u5BF9\u63A5 signKey
xmdcrm.signkey=m1fwurrvo09o33c7
#\u65B0\u7F8E\u5927CRM\u5BF9\u63A5 \u4E1A\u52A1ID
xmdcrm.businessid=4

#\u662F\u5426\u5220\u9664saas\u65E5\u5FD7\uFF0C0\u5220\u96641\u4E0D\u5220\u9664
logger.delete.isdelete=0
#\u5220\u9664\u65E5\u5FD7\u5929\u6570
logger.delete.periodic.days=90

#\u4F30\u6E05\u4E0A\u4F20(\u6682\u672A\u542F\u7528)
soldout.dataupload.url=http://saas.xmhxl.cn/estimate
#\u79D2\u4ED8\u4E0A\u4F20
secondpay.dataupload.url=http://saas.xmhxl.cn/tzxsecondpay


# \u65E5\u5FD7\u670D\u52A1\u5730\u5740\u548C\u7AEF\u53E3\u53F7(\u6682\u672A\u542F\u7528)
logger.server=saas.xmhxl.cn:28002
logger.query=saas.xmhxl.cn:9280

#MQ\u53C2\u6570\u914D\u7F6E
tzxmq.url=tcp://mq.xmhxl.cn:6666
#tzxmq.url=tcp://saas.xmhxl.cn:6666
tzxmq.user=
tzxmq.password=

#Push\u63A8\u9001\u5E73\u53F0(\u65F6\u95F4\u5355\u4F4D\u6BEB\u79D2)
websocket.url=push.xmhxl.cn:9092
websocket.wait=60000
websocket.heartbeattime=10000
websocket.divsion=#####

#SAAS\u603B\u90E8\u5730\u5740
#saas.url=http://saas.xmhxl.cn
#SAAS\u603B\u90E8\u5730\u5740
saas.url=http://saas.xmhxl.cn
cloud.url=http://cloudpos.xmhxl.cn
#\u5FAE\u751F\u6D3B\u53C2\u6570\u914D\u7F6E
acewill.url=https://api.acewill.net
acewill.v=2.0

## 1.15.0\u7248\u672C\u4EE5\u540E\u6B64\u53C2\u6570\u901A\u8FC7\u7CFB\u7EDF\u53C2\u6570\u8868\u83B7\u53D6\uFF0C\u53C2\u6570\u540D\u79F0\u5206\u522B\u4E3Awlife_appid,wlife_appkey
#acewill.appid=dp1SsbCWcRl4QvCw99MJWG33
#acewill.appkey=2b70ca14ce63cf455f2bb3ffce87381e

#\u8BA2\u5355\u4E2D\u5FC3redis\u670D\u52A1\u5668
redis.host=order.redis.xmhxl.cn
redis.port=8379
redis.password=tzxredisp@ss5d
redis.pool.maxWaitMillis=5000
redis.pool.maxTotal=15
redis.pool.maxIdle=5
redis.pool.testOnBorrow=true


####\u5386\u53F2\u6570\u636E\u6E05\u7406
#\u5386\u53F2\u6570\u636E\u4FDD\u7559\u5929\u6570
history.keep.days=60
#\u9700\u8981\u6E05\u7406\u7684\u5386\u53F2\u6570\u636E\u8868 (\u591A\u4E2A\u8868\u7528\u90FD\u53F7\u5206\u5272,\u8868\u5FC5\u987B\u542B\u6709report_date\u5B57\u6BB5)
history.clean.tables=pos_bill2,pos_bill_item2,pos_bill_payment2,pos_bill_payment_log,pos_log,pos_print_task,pos_third_payment_order,cc_order_list,cc_order_item,cc_order_repayment
#\u6E05\u7406\u5468\u671F
history.clean.cron=0 0 15,20 * * *

###\u6253\u5370\u670D\u52A1\u7A0B\u5E8F\u8DEF\u5F84
print.server.home=../PrintServer
print.server.launcher=TZXPrint_SVR.exe 

###\u5916\u5356\u8BA2\u5355\u914D\u9001\u5730\u5740
waimai.orderdelivery.url=http://saas.xmhxl.cn/deliverRest


####\u6570\u636E\u81EA\u52A8\u4E0A\u4F20
upload.period.cron=0 1/1 * * * ? 

### http connect and read timeout(ms)
http.connect.maxTotal = 20
http.connect.timeout= 5000
http.read.timeout= 10000

### Is auto synchronize base data on start
auto.synchronize.onStart=no

###\u5916\u5356\u9ED8\u8BA4\u6253\u5370\u65F6\u95F4\u8303\u56F4 \uFF0820\u5206\u949F\u4E4B\u5185\uFF09
waimai.print.time =20

### douyin OAuth API
douyin.api.url = https://open.douyin.com
douyin.client.key = awbe35kzflcl03kx
douyin.client.secret = 32d715403ded984201e5048c6596ec07

activity_exclude_payment_type = wlife_balance,wlife_coupon
