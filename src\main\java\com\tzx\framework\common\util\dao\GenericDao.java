package com.tzx.framework.common.util.dao;

import java.util.List;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.support.rowset.SqlRowSet;

/**
 * 
 */
public interface GenericDao
{
	/**
	 * @param tenantId
	 * @return
	 * @throws Exception
	 */
	JdbcTemplate getJdbcTemplate(String tenantId) throws Exception;

	/**
	 * 执行给定SLQ语句
	 * 
	 * @param tenantId
	 *            租户ID
	 * @param sql
	 * @return
	 * @throws Exception
	 */
	boolean execute(String tenantId, String sql) throws Exception;

	/**
	 * 执行给定SLQ语句，返回缓冲结果集
	 * 
	 * @param tenantId
	 * @param sql
	 * @return
	 * @throws Exception
	 */
	SqlRowSet query(String tenantId, String sql) throws Exception;

	/**
	 * 执行给定SQL语句，返回指定类型的对象集合，注意：属性名称忽略大小写，列名去除下划线，匹配程度决定返回信息多少
	 * 
	 * @param tenantId
	 * @param sql
	 * @param cls
	 * @return
	 * @throws Exception
	 */
	List<?> query(String tenantId, String sql, Class<?> cls) throws Exception;

	/**
	 * @param sql
	 * @param obj
	 * @param className
	 * @return
	 * @throws Exception
	 */
	public <T> List<T> query(String sql, Object[] obj, BeanPropertyRowMapper<T> className) throws Exception;

	/**
	 * @param sql
	 * @return
	 * @throws Exception
	 */
	public SqlRowSet query4SqlRowSet(String sql) throws Exception;
	
	/**
	 * @param sql
	 * @param obj
	 * @return
	 * @throws Exception
	 */
	public SqlRowSet query4SqlRowSet(String sql, Object[] obj) throws Exception;

	/**
	 * @param sql
	 * @param obj
	 * @return
	 * @throws Exception
	 */
	public int queryForInt(String sql, Object[] obj) throws Exception;

	/**
	 * @param sql
	 * @param obj
	 * @param className
	 * @return
	 * @throws Exception
	 */
	public <T> T queryForObject(String sql, Object[] obj, BeanPropertyRowMapper<T> className) throws Exception;

	/**
	 * 加这个方法的原是：两次转JSONObject，后台没有错，返回给andriod或delphi那边是404
	 * 
	 * @param tenantId
	 * @param sql
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> queryString4Json(String tenantId, String sql, Object[] objs) throws Exception;

	/**
	 * @param tenantId
	 * @param sql
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> queryString4Json(String tenantId, String sql) throws Exception;
	
	/**
	 * 执行给定SQL语句，返回JSON对象，列名作为键值，注意：此处不会去除下划线和改变大小写
	 * 
	 * @param tenantId
	 * @param sql
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> query4Json(String tenantId, String sql) throws Exception;

	/**
	 * 执行给定SQL语句，返回JSON对象，列名作为键值，注意：此处不会去除下划线和改变大小写
	 * 
	 * @param tenantId
	 * @param sql
	 * @param param
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> query4Json(String tenantId, String sql, Object[] param) throws Exception;

	/**
	 * 执行给定SQL语句，返回JSONArray对象，列名作为键值，注意：此处不会去除下划线和改变大小写
	 * 
	 * @param tenantId
	 * @param sql
	 * @return
	 * @throws Exception
	 */
	JSONArray query4JSONArray(String tenantId, String sql) throws Exception;

	/**
	 * 执行给定SQL语句，返回JSONArray对象，列名作为键值，注意：此处不会去除下划线和改变大小写
	 * 
	 * @param tenantId
	 * @param sql
	 * @return
	 * @throws Exception
	 */
	JSONArray query4JSONArray(String tenantId, String sql, Object[] objs) throws Exception;

	/**
	 * @param tenantId
	 * @param sql
	 * @return
	 * @throws Exception
	 */
	public int getInt(String tenantId, String sql) throws Exception;

	/**
	 * @param tenantId
	 * @param sql
	 * @return
	 * @throws Exception
	 */
	public Double getDouble(String tenantId, String sql) throws Exception;

	/**
	 * @param tenantId
	 * @param sql
	 * @return
	 * @throws Exception
	 */
	public String getString(String tenantId, String sql) throws Exception;

	/**
	 * @param sql
	 * @param obj
	 * @return
	 * @throws Exception
	 */
	public int update(String sql, Object[] obj) throws Exception;

	/**
	 * @param sql
	 * @param batchArgs
	 * @return
	 * @throws Exception
	 */
	public int[] batchUpdate(String sql, List<Object[]> batchArgs) throws Exception;
	
	/**
	 * 将给定JSON插入指定表中，表中字段去除下划线，与JSON中键值忽略大小写匹配，返回主键ID
	 * JSON中包含主键ID时，不会再生成ID而是沿用传入的ID插入
	 * 
	 * @param tenantId
	 * @param tableName
	 * @param json
	 * @return
	 * @throws Exception
	 */
	Object insertIgnorCase(String tenantId, String tableName, JSONObject json) throws Exception;

	/**
	 * 批量插入记录
	 * 
	 * @param tenantId
	 * @param tableName
	 * @param jsonList
	 * @return
	 * @throws Exception
	 */
	Object[] insertBatchIgnorCase(String tenantId, String tableName, final List<JSONObject> jsonList) throws Exception;

	/**
	 * 将给定JSON更新至指定表中，给定字段可不包含下划线，返回修改行数，如果返回0则表示修改失败
	 * 
	 * @param tenantId
	 * @param tableName
	 * @param json
	 * @return
	 * @throws Exception
	 */
	int updateIgnorCase(String tenantId, String tableName, JSONObject json) throws Exception;

	/**
	 * 批量更新记录，返回数组为批量修改后修改的行数，0代表修改失败
	 * 
	 * @param tenantId
	 * @param tableName
	 * @param jsonList
	 * @return
	 * @throws Exception
	 */
	int[] updateBatchIgnorCase(String tenantId, String tableName, final List<JSONObject> jsonList) throws Exception;

	/**
	 * 删除指定表明的记录
	 * 
	 * @param tenantId
	 * @param tableName
	 * @param id
	 * @return
	 * @throws Exception
	 */
	boolean delete(String tenantId, String tableName, final Object id) throws Exception;

	/**
	 * 批量删除记录，返回删除执行后影响的行数，0代表删除失败
	 * 
	 * @param tenantId
	 * @param tableName
	 * @param jsonList
	 * @return
	 * @throws Exception
	 */
	int[] deleteBatchIgnorCase(String tenantId, String tableName, final List<JSONObject> jsonList) throws Exception;

	/**
	 * 调用存储过程，可传入参数
	 * 
	 * @param procString
	 * @param inParams
	 * @param outParamTypes
	 * @return
	 * @throws Exception
	 */
	Object callProduce(String tenantId, final String procString, final List<Object> inParams, final List<String> outParamTypes) throws Exception;

	/**
	 * 统计总数
	 * 
	 * @param tenantId
	 * @param sql
	 * @return
	 * @throws Exception
	 */
	long countSql(String tenantId, String sql) throws Exception;

	/**
	 * 
	 */
	String buildPageSql(JSONObject param, String sql) throws Exception;

	/**
	 * 支持多列排序的分页查询方法
	 */
	String multiSortPageSql(JSONObject param, String sql) throws Exception;
}
