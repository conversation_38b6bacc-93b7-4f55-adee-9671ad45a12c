package com.tzx.framework.common.exception;

public enum HqErrorCode implements ErrorCode
{

	/**
	 * 法人代码已存在，不能重复
	 */
	LEGAL_PER_CODE_EXIST_ERROE(7001),
	/**
	 * 价格体系名称已存在，不能重复
	 */
	PRICE_SYSTEM_NAME_EXIST_ERROE(7002),
	/**
	 * 同一类别下的名称不允许重复
	 */
	ITEMCLASS_NAME_EXIST_ERROE(7003),
	/**
	 * 菜品名称不允许重复
	 */
	ITEM_NAME_EXIST_ERROE(7004),
	/**
	 * 项目组名称已存在，不允许重复
	 */
	ITEM_GROUP_NAME_EXIST_ERROE(7005),
	/**
	 * 厨打类型已存在，不允许重复
	 */
	KVS_NAME_EXIST_ERROE(7006),
	/**
	 * 餐谱名称不允许重复
	 */
	ITEM_MENU_NAME_EXIST_ERROE(7007),
	/**
	 * 折扣方案名称不允许重复
	 */
	DISCOUNT_CASE_NAME_EXIST_ERROR(7008),
	/**
	 * 折扣方案编号不允许重复
	 */
	DISCOUNT_CASE_CODE_EXIST_ERROR(7009),
	/**
	 * 同一类别下名称不允许重复
	 */
	NAME_CLASS_EXIST_ERROR(7010),
	/**
	 * 同一类别下编号不允许重复
	 */
	CODE_CLASS_EXIST_ERROR(7011),
	/**
	 * 同一类别,只能有一个有效打印格式
	 */
	ONLY_ONE_FORMAT_ERROR(7012),
	/**
	 * 法人名称已存在,不能重复
	 */
	LEGAL_PER_NAME_EXIST_ERROE(7013),

	/**
	 * 切换数据源错误
	 */
	CHANGE_DATASOURCE_ERROR(7100),
	/**
	 * 请求类型无效
	 */
	TYPE_NOT_EXISTS_ERROR(7101),
	/**
	 * 参数信息为空
	 */
	PARAM_ISNULL_ERROR(7102),
	/**
	 * 用户不存在
	 */
	NOT_EXISTS_USER(7103),
	
	;

	private final int	number;

	private HqErrorCode(int number)
	{
		this.number = number;
	}

	public int getNumber()
	{
		return number;
	}

	@Override
	public String getMessage()
	{
		return "";
	}

}
