/** 
 * @(#)CodeFormat.java    1.0   2017-06-19
 * 
 * Copyright 北京普照天星科技有限公司. All rights reserved. 
 * PROPRIETARY/CONFIDENTIAL. Use is subject to license terms. 
 */
package com.tzx.pos.asyn.pool;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 线程池类，根据不同业务种类提供不同的线程池对象.
 *
 * <AUTHOR> email:<EMAIL>
 * @version 1.0  2017-06-19
 * @see     
 * @since   JDK7.0
 * @update  
 */
public class ThreadPool {

	// 定义线程池对象（用于打印业务）
	private static volatile ThreadPoolExecutor printThreadPool = null;
	
	/**
	 * @Description 获取线程池对象（通过双重检查锁实现）。
	 * @param
	 * @return ThreadPoolExecutor 线程池对象
	 * @exception <AUTHOR>                zhehong.qiu email:qiuz<PERSON><EMAIL>
	 * @version 1.0 2017-06-19
	 * @see
	 */
	public static ThreadPoolExecutor getPrintThreadPool() {
		if(printThreadPool == null){
			synchronized (ThreadPoolExecutor.class) {
				if(printThreadPool == null){
					/*
					 * 创建核心线程数为4，最大线程数为8(当任务队列满时才会增加大于核心线程数且小于最大线程数的线程)的线程池，使用ArrayBlockingQueue阻塞队列，队列大小为10，线程数超过队列大小时的策略为重试。
					 */
					printThreadPool = new ThreadPoolExecutor(4,8,3,TimeUnit.SECONDS, new ArrayBlockingQueue<Runnable>(10),
							new ThreadPoolExecutor.CallerRunsPolicy());
				}
			}
		}
		return printThreadPool;
	}
	
}
