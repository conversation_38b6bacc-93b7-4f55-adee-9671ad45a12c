package com.tzx.framework.common.util.dao.pojo;

import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;

import com.tzx.framework.common.util.dao.GenericDao;

public class GenericDaoTableInfo
{
	private static Map<String,JSONObject> tableMap = new ConcurrentHashMap<String, JSONObject>();
	
	private static Map<String,Long> tableMapTimes = new ConcurrentHashMap<String, Long>();
	
	private static Long ONE_HOUR = 3600000L;
	
	public static JSONObject getTableInfo(String tenancy_id,String table_name,GenericDao dao) throws Exception
	{
		String key = tenancy_id+"_"+table_name;
		long now = System.currentTimeMillis();
		long ONE_HOUR_BEFORE = now - ONE_HOUR;
		if(tableMap.contains<PERSON>ey(key))
		{
			if(tableMapTimes.containsKey(key) && tableMapTimes.get(key)<ONE_HOUR_BEFORE)
			{
				return tableMap.get(key);
			}
		}
		StringBuilder sb = new StringBuilder();
		sb.append("SELECT  b.attname as name, b.attnotnull as notnull,c.typname as typename, case when d.conname is null then 0 else 1 end conname");
		sb.append(" FROM pg_class as a ");
		sb.append(" inner join pg_attribute as b on b.attrelid = a.oid and b.attnum>0");
		sb.append(" inner join pg_type as c on c.oid = b.atttypid");
		sb.append(" left join pg_constraint as d on d.conrelid = a.oid  and  b.attnum = ANY  (d.conkey) and d.contype='p' ");
		sb.append(" where a.relname = '"+table_name+"' ");
		List<JSONObject> list = dao.query4Json(tenancy_id,sb.toString());
		
		if(list.size()>0)
		{
			JSONObject tableinfo = new JSONObject();
			for(JSONObject jo:list)
			{
				String keyname = jo.optString("name");
				String typename = jo.optString("typename");
				Integer conname = jo.optInt("conname");
				int type = 0;
				if(conname == 1)
				{
					//主键 
					type = 1;
				}
				else
				{
					//date = 2
					// timestamp = 3
					// numeric = 4
					// int4 = 5;
					switch (typename)
					{
						case "date":
							type = 2;	
							break;
						case "timestamp":
							type = 3;						
							break;
						case "numeric":
							type = 4;
							break;
						case "int4":
							type = 5;
							break;
						case "time":
							type = 6;						
							break;	
						default:
							break;
					}
				}
				tableinfo.put(keyname, type);
				//if(date.)
				
			}
			tableMap.put(key, tableinfo);
			tableMapTimes.put(key, now);
			return tableinfo;
		}
		return null;
	}
	
	@SuppressWarnings("unchecked")
	public static String getInsert(String tenancy_id,String table_name,GenericDao dao,JSONObject jb)throws Exception
	{
		JSONObject tableinfo = getTableInfo(tenancy_id, table_name, dao);
		
		if(null!=tableinfo)
		{
			Iterator<String> keys = tableinfo.keys();
			StringBuilder sb = new StringBuilder();
			sb.append("INSERT INTO "+table_name +" (");
			StringBuilder sb2 = new StringBuilder();
			int keyi = 0;
			boolean flag = false;
			while (keys.hasNext())
			{
				String key = keys.next();
				int keytype = tableinfo.optInt(key);
				switch (keytype)
				{
					case 1:
						flag = true;
						break;
					//date
					case 2:
						if (keyi>0)
						{
							sb.append(",");
							sb2.append(",");
						}
						sb.append(key);
						if(jb.containsKey(key)&&jb.optString(key).length()>0&& !"NULL".equals(jb.optString(key).toUpperCase()))
						{
							sb2.append("'"+jb.optString(key).replaceAll("'", "''")+"'");
						}
						else
						{
							sb2.append("null");
						}
						
						keyi++;
						break;
					//timestamp	
					case 3:
						if (keyi>0)
						{
							sb.append(",");
							sb2.append(",");
						}
						sb.append(key);
						if(jb.containsKey(key)&&jb.optString(key).length()>0&& !"NULL".equals(jb.optString(key).toUpperCase()))
						{
							sb2.append("'"+jb.optString(key).replaceAll("'", "''")+"'");
						}
						else
						{
							sb2.append("null");
						}
						keyi++;
						break;
					//numeric	
					case 4:
						if (keyi>0)
						{
							sb.append(",");
							sb2.append(",");
						}
						sb.append(key);
						if(jb.containsKey(key))
						{
							sb2.append("'"+jb.optDouble(key,0.0)+"'");
						}
						else
						{
							sb2.append("null");
						}
						keyi++;
						break;
					//int4	
					case 5:
						if (keyi>0)
						{
							sb.append(",");
							sb2.append(",");
						}
						sb.append(key);
						if(jb.containsKey(key)&& StringUtils.isNotBlank(jb.optString(key)) && !"NULL".equals(jb.optString(key).toUpperCase()))
						{
							sb2.append("'"+jb.optInt(key)+"'");
						}
						else 
						{
							sb2.append("null");
						}
						keyi++;
						break;
					case 6:
						if (keyi>0)
						{
							sb.append(",");
							sb2.append(",");
						}
						sb.append(key);
						if(jb.containsKey(key)&&jb.optString(key).length()>0&& !"NULL".equals(jb.optString(key).toUpperCase()))
						{
							sb2.append("'"+jb.optString(key).replaceAll("'", "''")+"'");
						}
						else
						{
							sb2.append("null");
						}
						keyi++;
						break;	
					default:
						if (keyi>0)
						{
							sb.append(",");
							sb2.append(",");
						}
						
						sb.append(key);
						if(jb.containsKey(key)&& !"NULL".equals(jb.optString(key).toUpperCase()))
						{
							if("tenancy_id".equals(key.toLowerCase())){
								sb2.append("'"+tenancy_id+"'");
							}else{
								sb2.append("'"+jb.optString(key).replaceAll("'", "''")+"'");
							}
							
						}
						else
						{
							if("tenancy_id".equals(key.toLowerCase())){
								sb2.append("'"+tenancy_id+"'");
							}else{
								sb2.append("null");
							}
						}
						keyi++;
						break;
				}
			}
			sb.append(") VALUES ("+sb2.toString()+") ");
			if(flag){
				sb.append("    RETURNING id");
			}
			return sb.toString();
		}
		else 
		{
			throw new Exception("表"+table_name+"不存在!");
		}
	}
	
	public static String getBatchInsert(String tenancy_id,String table_name,GenericDao dao,List<JSONObject> lt)throws Exception
	{
		JSONObject tableinfo = getTableInfo(tenancy_id, table_name, dao);
		StringBuilder sb = new StringBuilder();
		boolean flag = false;
		if(lt.size()>0)
		{
			sb.append("INSERT INTO "+table_name +" (");
			StringBuilder sb2 = new StringBuilder();
			int lists = 0;
			for(JSONObject jb:lt)
			{
				@SuppressWarnings("unchecked")
				Iterator<String> keys = tableinfo.keys();
				if(lists>0)
				{
					sb2.append(",");
				}
				sb2.append("(");
				int keyi = 0;
				while (keys.hasNext())
				{
					String key = keys.next();
					int keytype = tableinfo.optInt(key);
					switch (keytype)
					{
						case 1:
							flag = true;
							break;
						//date
						case 2:
							if (keyi>0)
							{
								if(lists==0)
								{
									sb.append(",");
								}
								sb2.append(",");
							}
							if(lists==0)
							{
								sb.append(key);
							}

							if(jb.containsKey(key)&&jb.optString(key).length()>0&& !"NULL".equals(jb.optString(key).toUpperCase()))
							{
								sb2.append("'"+jb.optString(key).replaceAll("'", "''")+"'");
							}
							else
							{
								sb2.append("null");
							}
							
							keyi++;
							break;
						//timestamp	
						case 3:
							if (keyi>0)
							{
								if(lists==0)
								{
									sb.append(",");
								}
								sb2.append(",");
							}
							if(lists==0)
							{
								sb.append(key);
							}
							if(jb.containsKey(key)&&jb.optString(key).length()>0&& !"NULL".equals(jb.optString(key).toUpperCase()))
							{
								sb2.append("'"+jb.optString(key).replaceAll("'", "''")+"'");
							}
							else
							{
								sb2.append("null");
							}
							keyi++;
							break;
						//numeric	
						case 4:
							if (keyi>0)
							{
								if(lists==0)
								{
									sb.append(",");
								}
								sb2.append(",");
							}
							if(lists==0)
							{
								sb.append(key);
							}
							if(jb.containsKey(key))
							{
								sb2.append("'"+jb.optDouble(key,0.0)+"'");
							}
							else
							{
								sb2.append("null");
							}
							keyi++;
							break;
						//int4	
						case 5:
							if (keyi>0)
							{
								if(lists==0)
								{
									sb.append(",");
								}
								sb2.append(",");
							}
							if(lists==0)
							{
								sb.append(key);
							}
							if(jb.containsKey(key)&&jb.optString(key).length()>0 && !"NULL".equals(jb.optString(key).toUpperCase()))
							{
								sb2.append("'"+jb.optInt(key)+"'");
							}
							else
							{
								sb2.append("null");
							}
							keyi++;
							break;
						case 6:
							if (keyi>0)
							{
								if(lists==0)
								{
									sb.append(",");
								}
								sb2.append(",");
							}
							if(lists==0)
							{
								sb.append(key);
							}
							if(jb.containsKey(key)&&jb.optString(key).length()>0&& !"NULL".equals(jb.optString(key).toUpperCase()))
							{
								sb2.append("'"+jb.optString(key).replaceAll("'", "''")+"'");
							}
							else
							{
								sb2.append("null");
							}
							keyi++;
							break;	
						default:
							if (keyi>0)
							{
								if(lists==0)
								{
									sb.append(",");
								}
								sb2.append(",");
							}
							if(lists==0)
							{
								sb.append(key);
							}
							if(jb.containsKey(key) && !"NULL".equals(jb.optString(key).toUpperCase()))
							{
								if("tenancy_id".equals(key.toLowerCase())){
									sb2.append("'"+tenancy_id+"'");
								}else{
									sb2.append("'"+jb.optString(key).replaceAll("'", "''")+"'");
								}
								//sb2.append("'"+jb.optString(key).replaceAll("'", "''")+"'");
							}
							else
							{
								if("tenancy_id".equals(key.toLowerCase())){
									sb2.append("'"+tenancy_id+"'");
								}else{
									sb2.append("null");
								}
							}
							keyi++;
							break;
					}
				}
				sb2.append(")");
				lists ++;
			}
			sb.append(") VALUES "+sb2.toString());
			if(flag){
				sb.append("    RETURNING id");
			}
			
		}
		
		return sb.toString();
	}
	
	public static String updateById(String tenancy_id,String table_name,GenericDao dao,JSONObject jb)throws Exception
	{
		JSONObject tableinfo = getTableInfo(tenancy_id, table_name, dao);
		StringBuilder sb = new StringBuilder();
		sb.append("update "+table_name+" set ");
		@SuppressWarnings("unchecked")
		Iterator<String> keys = tableinfo.keys();
		int keyi = 0;
		while (keys.hasNext())
		{
			String key = keys.next();
			int keytype = tableinfo.optInt(key);
			switch (keytype)
			{
				case 1:
					
					break;
				case 2:
					if(jb.containsKey(key) )
					{
						if (keyi>0)
						{
							sb.append(",");
						}
						sb.append(key+"=");
						if(jb.optString(key).length()==0 || "null".equals(jb.optString(key)))
						{
							sb.append("null");
						}
						else
						{
							sb.append("'"+jb.optString(key).replaceAll("'", "''")+"'");
						}
						
						keyi++;
					}
					break;
				//timestamp	
				case 3:
					if(jb.containsKey(key))
					{
						if (keyi>0)
						{
							sb.append(",");
						}
						sb.append(key+"=");
						if(jb.optString(key).length()==0 || "null".equals(jb.optString(key)))
						{
							sb.append("null");
						}
						else
						{
							sb.append("'"+jb.optString(key).replaceAll("'", "''")+"'");
						}
						keyi++;
					}
					break;
				//numeric	
				case 4:
					if(jb.containsKey(key))
					{
						if (keyi>0)
						{
							sb.append(",");
						}
						sb.append(key+"=");
						sb.append("'"+jb.optDouble(key,0.0)+"'");
						keyi++;
					}
					break;
				//int4	
				case 5:
					if(jb.containsKey(key))
					{
						if (keyi>0)
						{
							sb.append(",");
						}
						sb.append(key+"=");
						sb.append("'"+jb.optInt(key)+"'");
						keyi++;
					}
				
					break;	
				case 6:
					if(jb.containsKey(key))
					{
						if (keyi>0)
						{
							sb.append(",");
						}
						sb.append(key+"=");
						if(jb.optString(key).length()==0 || "null".equals(jb.optString(key)))
						{
							sb.append("null");
						}
						else
						{
							sb.append("'"+jb.optString(key).replaceAll("'", "''")+"'");
						}
						keyi++;
					}
					break;	
				default:
					if(jb.containsKey(key) && !"NULL".equals(jb.optString(key).toUpperCase()))
					{
						if (keyi>0)
						{
							sb.append(",");
						}
						sb.append(key+"=");
						sb.append("'"+jb.optString(key).replaceAll("'", "''")+"'");
						keyi++;
					}
					break;
			}
		}
		
		sb.append(" where id="+jb.optInt("id")+";");
		return sb.toString();
	}
	
	public static String updateBatchById(String tenancy_id,String table_name,GenericDao dao,List<JSONObject> lt)throws Exception
	{
		StringBuilder sb2 = new StringBuilder();
		for(JSONObject jb:lt)
		{
			sb2.append(updateById(tenancy_id, table_name, dao, jb));
		}

		return sb2.toString();
	}
	
	
	public static String getPkKey(String tenancy_id,String table_name,GenericDao dao)throws Exception
	{
		JSONObject tableinfo = getTableInfo(tenancy_id, table_name, dao);
		String pkkey = "id";
		@SuppressWarnings("unchecked")
		Iterator<String> keys = tableinfo.keys();
		while (keys.hasNext())
		{
			String key = keys.next();
			int keytype = tableinfo.optInt(key);
			switch (keytype)
			{
				case 1:
					pkkey = key;
					break;
			}
		}
		return pkkey;
	}
	
	
}