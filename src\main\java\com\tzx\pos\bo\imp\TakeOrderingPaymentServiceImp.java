package com.tzx.pos.bo.imp;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import net.sf.json.JSONObject;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;

import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.DoubleHelper;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.Tools;
import com.tzx.orders.bo.OrdersSyncService;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.bo.PosPrintNewService;
import com.tzx.pos.bo.PosPrintService;
import com.tzx.pos.bo.PosService;
import com.tzx.pos.bo.TakeOrderingPaymentService;
import com.tzx.pos.bo.dto.TakeOrderingParam;
import com.tzx.pos.bo.payment.PaymentWayService;
import com.tzx.pos.bo.payment.imp.ext.OtherPaymentWayServiceImp;
import com.tzx.pos.po.springjdbc.dao.PosPaymentDao;


@Service(TakeOrderingPaymentService.NAME)
public class TakeOrderingPaymentServiceImp extends PosBaseServiceImp implements TakeOrderingPaymentService {
	@Autowired 
	private PosService posService;

	@Autowired 
	private PosPaymentDao paymentDao;
//	private PaymentWayService		posPaymentService;
	@Autowired 
	private OrdersSyncService		orderSyncService;
	
	private PaymentWayService		paymentWayService;
	
	@Autowired
	private PosPrintNewService posPrintNewService;
	@Autowired
	private PosPrintService posPrintService;
	
	public static final int			DEFAULT_SCALE	= 4;
	private static final Logger logger = Logger.getLogger(TakeOrderingServiceImp.class);

	
	
	/**
	 * 插入付款信息
	 * @param tenancyId
	 * @param storeId
	 * @param source
	 * @param paramList
	 * @throws Exception 
	 */
	public synchronized void posBillPayment(TakeOrderingParam para,Data result) throws Exception{
		
		logger.info(Thread.currentThread().getId()+"@-----订单转账单 付款被调用--  para=+"+para+"result="+result);

		String tenancyId = para.getTenantId();
		int storeId = para.getOrganId();
		
		JSONObject printJson = new JSONObject();
		List<Map<String, Object>> paramList = new ArrayList<Map<String, Object>>();
		paramList = para.getPaymentList();
		if (paramList == null || paramList.isEmpty()){
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}
		
		String billNum = para.getBillNum();
		int shiftId = para.getShiftId();
		String posNum = para.getPosNum();
		String optNum = para.getOptNum();
		String saleMode = para.getSaleMode();
		String tableCode = para.getTableCode();
		Date reportDate = para.getReportDate();
		String isprint_bill = "Y";
		String isInvoice = "Y";
		isprint_bill = "Y"; //是否打印

		String isOnlineReplayent = "0";
		
		
		Timestamp currentTime = DateUtil.currentTimestamp();
		
		double paymentAmount = 0d;
		double difference = 0d;
		double discountAmount =0d;
		double discountRate = 0d;
//		double discountkAmount;
//		int discountCaseId = 0;
		String paymentState="";
		String chanel = "";
//		String billProperty = "";
		String batchNum = "";
//		String orderNum = "";
		
		JSONObject billJson = paymentDao.getPosBillByBillnum(tenancyId, storeId, billNum);
		if (null != billJson){
			paymentAmount = billJson.optDouble("payment_amount");
			difference = billJson.optDouble("difference");
			discountRate = billJson.optDouble("discount_rate");
			discountAmount = billJson.optDouble("discount_amount");
//			discountkAmount = billJson.optDouble("discountk_amount");
//			discountCaseId = billJson.optInt("discount_case_id");
			chanel = billJson.optString("source");
//			billProperty = billJson.optString("bill_property");
			paymentState = billJson.optString("payment_state");
			if (discountRate <= 0){
				discountRate = 100d;
			}
			batchNum = billJson.optString("batch_num");
//			orderNum = billJson.optString("order_num");
			if(Tools.isNullOrEmpty(saleMode)){
				saleMode = billJson.optString("sale_mode");
			}

		}
		
		if (paymentAmount <= 0){// 账单金额为0,判断是否可以零结账
			String isZeroPay = paymentDao.getSysParameter(tenancyId, storeId, "IS_ZEROPAY");
			if (!"1".equals(isZeroPay)){
				throw SystemException.getInstance(PosErrorCode.BILL_AMOUNT_ZERO);
			}
		}
		
		StringBuilder newstate = new StringBuilder();
		//插入支付相关数据
		insertPayment(newstate, chanel, tenancyId, storeId, billNum, batchNum, reportDate, shiftId, posNum, optNum, tableCode, paymentAmount, currentTime, isOnlineReplayent,para.getPaymentAmount(), paramList,para.getServiceAmount());
		
		difference = DoubleHelper.sub(paymentAmount, paymentDao.getBillPaymentAmount(tenancyId, storeId, billNum), DEFAULT_SCALE);
		JSONObject resultJson = new JSONObject();
		
		if (difference <= 0){
			paymentDao.updatePosBillForStateByBillnum(tenancyId, storeId, billNum, SysDictionary.PAYMENT_STATE_PAY);

			paymentDao.updatePosBillForSalemodeByBillnum(tenancyId, storeId, billNum, saleMode,tableCode,shiftId);

			paymentDao.updatePosBillItemForSalemodeByBillnum(tenancyId, storeId, billNum, saleMode,shiftId);
            
			paymentDao.updatePosBillTaxPriceSeparation(tenancyId, storeId, billNum, saleMode);

			closedPosBill(tenancyId, storeId, billNum, DateUtil.getYYYYMMDDFromDate2(reportDate), shiftId, posNum, optNum, isprint_bill, isInvoice, resultJson, printJson, isOnlineReplayent,currentTime);
            JSONObject paras = (JSONObject) paramList.get(0);
			if ("1".equals(isOnlineReplayent)){
				String mobil = paras.optString("mobil");
				String customerCode = paras.optString("customer_code");
				String customerName = paras.optString("customer_name");
				String cardCode = paras.optString("card_code");
				double consumeBeforeCredit = paras.optDouble("consume_before_credit");
				double consumeAfterCredit = paras.optDouble("consume_after_credit");
				double credit = paras.optDouble("credit");
				double amount = paras.optDouble("amount");
				String billCode = paras.optString("bill_code");

				if (Tools.hv(mobil) && Tools.hv(customerCode) && credit > 0){
					paymentDao.insertPosBillMember(tenancyId, storeId, billNum, reportDate, SysDictionary.BILL_MEMBERCARD_JF01, customerCode, cardCode, mobil, currentTime, null, customerName, consumeBeforeCredit, consumeAfterCredit, credit, amount, billCode,SysDictionary.REQUEST_STATUS_COMPLETE);
				}
			}
		}
		else{
			resultJson = this.getPosBillPaymentByBillNum(tenancyId, storeId, billNum, tableCode, paymentAmount, discountAmount, difference, paymentState);
			paymentDao.updatePosBillForStateByBillnum(tenancyId, storeId, billNum, SysDictionary.PAYMENT_STATE_NOTPAY, difference);
		}
		try
		{
			String source = "";
			if (("order_device".equals(source) || "android_pad".equals(source)))
			{
				posPrintService.getBindOptNum(printJson.optString("tenancy_id"), printJson.optInt("store_id"), printJson);
			}

			posPrintService.printPosBillForPayment(printJson, posPrintNewService);
		}
		catch (Exception e1)
		{
			logger.error(e1);
		}
		// 如果是拆台  把桌位更改为原始桌位
		String posTableStateSql = "select table_code,stable from  pos_tablestate where table_code='" + tableCode + "' and table_open_num is not  null";
		SqlRowSet rs =   paymentDao.query4SqlRowSet(posTableStateSql);
		if(rs.next() ){
			String updatePosBillSql ="update pos_bill set table_code =?  where tenancy_id=? and store_id=? and  bill_num=? ";
			paymentDao.update(updatePosBillSql, new Object[]{rs.getString("stable"),tenancyId,storeId,billNum});
			updatePosBillSql = "update pos_bill_item set table_code = ? where tenancy_id=? and store_id=? and  bill_num=? ";
			paymentDao.update(updatePosBillSql, new Object[]{rs.getString("stable"),tenancyId,storeId,billNum});
			updatePosBillSql = "update pos_bill_payment set table_code = ? where tenancy_id=? and store_id=? and  bill_num=? ";
			paymentDao.update(updatePosBillSql, new Object[]{rs.getString("stable"),tenancyId,storeId,billNum});
		}
		upload(tenancyId,storeId+"", "",tableCode,"",billNum);
		paymentDao.savePosLog(tenancyId, storeId, posNum, optNum, optNum, shiftId, reportDate, com.tzx.pos.base.Constant.TITLE, "结账", "账单编号:" + billNum + "; 应付金额:" + String.valueOf(paymentAmount), newstate.toString());
		logger.info(Thread.currentThread().getId()+"@-----订单转账单 付款被结束-- -----------@--------->>>>>>>>>>>>>>>>");

	}
	
	/**
	 * 插入付款表相关数据
	 * @param newstate
	 * @param chanel 渠道
	 * @param tenancyId 商户id
	 * @param storeId 机构id
	 * @param billNum 订单编号
	 * @param batchNum
	 * @param reportDate 报表日志
	 * @param shiftId 班次id
	 * @param posNum 机台号
	 * @param optNum 收银员编号
	 * @param tableCode 桌台号
	 * @param paymentAmount 金额
	 * @param currentTime 操作时间
	 * @param isOnlineReplayent
	 * @param difference
	 * @param paramList 支付相关list
	 * @throws Exception
	 */
	public void insertPayment(StringBuilder newstate,String chanel,String tenancyId, Integer storeId,String billNum, String batchNum, Date reportDate,Integer shiftId, String posNum, String optNum,String tableCode,Double paymentAmount,Timestamp currentTime,String isOnlineReplayent ,Double paymentAmount2,List<?> paramList,Double serviceAmount) throws Exception{
		
		logger.info(Thread.currentThread().getId()+"-----订单转账单->插入付款表开始--  tenancyId=+"+tenancyId+"------storeId="+storeId+"----billNum="+billNum+"----reportDate="+reportDate+"----shiftId="+shiftId+"----posNum="+posNum+"----optNum"+optNum+"----tableCode="+tableCode+"----paymentAmount"+paymentAmount+"----currentTime"+currentTime+"----paramList"+paramList+"----paramList"+paramList.size());
		double amount=0d;
		for (Object itemObj : paramList){
			
			JSONObject itemJson = JSONObject.fromObject(itemObj);
			itemJson.put("chanel", chanel);
			int jzid = itemJson.optInt("jzid");
			if (Tools.isNullOrEmpty(jzid)){
				throw SystemException.getInstance(PosErrorCode.NOT_NULL_JZID);
			}
			
			amount += itemJson.optDouble("amount");
			
			if (Tools.isNullOrEmpty(itemJson.optDouble("amount"))){
				throw SystemException.getInstance(PosErrorCode.NOT_NULL_PAYMENT_AMOUNT);
			}
            
			String paymentClass = "";
			String paymentName = "";
			String paymentEnglishName = "";
			double exchangeRate = 1d;
			boolean isCheck = true;
			JSONObject paymentWayJson = paymentDao.getPaymentWayByID(tenancyId, storeId, jzid);
			if (null != paymentWayJson && !paymentWayJson.isEmpty()){
				paymentClass = paymentWayJson.getString("payment_class");
				paymentName = paymentWayJson.getString("payment_name");
				paymentEnglishName = paymentWayJson.getString("name_english");
				exchangeRate = Tools.hv(paymentWayJson.get("rate")) ? paymentWayJson.getDouble("rate") : 1d;
				itemJson.put("payment_class", paymentClass);
				itemJson.put("payment_name", paymentName);
				itemJson.put("payment_english_name", paymentEnglishName);
				itemJson.put("rate", exchangeRate);
				if (Tools.isNullOrEmpty(itemJson.opt("is_check"))){
					isCheck = "1".equals(paymentWayJson.getString("is_check"));
				}
				else{
					isCheck = "1".equals(itemJson.getString("is_check"));
				}
				itemJson.put("is_check", isCheck);
			}
			else{
				throw SystemException.getInstance(PosErrorCode.NOT_EXIST_JZID);
			}

		    paymentWayService = SpringConext.getApplicationContext().getBean(OtherPaymentWayServiceImp.class);
			paymentWayService.payment(tenancyId, storeId, billNum, batchNum, reportDate, shiftId, posNum, optNum, tableCode, itemJson, currentTime, isOnlineReplayent);
			newstate.append("支付方式:").append(paymentName).append(",").append("支付金额:").append(amount).append(";");
			logger.info(Thread.currentThread().getId()+"-----订单转账单=================插入付款表结束============ ");
		}
		
		if(paymentAmount != amount){
			logger.info("订单金额不等于支付金额----支付金额="+paymentAmount+"---订单金额="+ amount);
            throw SystemException.getInstance(PosErrorCode.NOT_PAYMEN_TAMOUNT);
        }
		
        if(!paymentAmount2.equals(paymentAmount)){
        	java.text.DecimalFormat   df   =new   java.text.DecimalFormat("#.0");  //再次进行对比 
        	Double pay = Double.valueOf(df.format(paymentAmount));
        	if(!pay.equals(paymentAmount)){
        		logger.info("订单金额不等于支付金额----支付金额b="+paymentAmount2+"---订单金额b="+ paymentAmount);
        		throw SystemException.getInstance(PosErrorCode.NOT_PAYMEN_TAMOUNT);
        	}
        	logger.info("订单金额不等于支付金额----支付金额c="+paymentAmount2+"---订单金额c="+ paymentAmount);
        	throw SystemException.getInstance(PosErrorCode.NOT_PAYMEN_TAMOUNT);
    	}
	}
	
	/**
	 * 关闭账单
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param reportDate
	 * @param shiftId
	 * @param posNum
	 * @param optNum
	 * @param isPrint
	 * @param isInvoice
	 * @param resultJson
	 * @param printJson
	 * @param isOnlinePay
	 * @param currentTime
	 * @throws Exception
	 */
	private void closedPosBill(String tenancyId, int storeId, String billNum, String reportDate, int shiftId, String posNum, String optNum, String isPrint, String isInvoice, JSONObject resultJson, JSONObject printJson, String isOnlinePay,Timestamp currentTime) throws Exception{
		logger.info(Thread.currentThread().getId()+"-----订单转账单->关闭账单方法开始--  tenancyId=+"+tenancyId+"------storeId="+storeId+"----billNum="+billNum+"----reportDate="+reportDate+"----shiftId="+shiftId+"----posNum="+posNum+"----optNum"+optNum+"----isPrint="+isPrint+"----isInvoice"+isInvoice+"----isOnlinePay"+isOnlinePay+"----currentTime"+currentTime+"----resultJson"+resultJson+"----printJson"+printJson);
		JSONObject billJson = paymentDao.getPosBillByBillnum(tenancyId, storeId, billNum);
		double paymentAmount = 0d;
		double discountAmount = 0d;
		String paymentState = null;
		String tableCode = null;
		String billProperty = null;
		String orderNum = null;
		String chanel = null;
		String batchNum = null;
		double difference = 0d;
		int recoverCnt = 0;
		if (null != billJson){
			paymentAmount = billJson.optDouble("payment_amount");
			discountAmount = billJson.optDouble("discount_amount");
			paymentState = billJson.optString("payment_state");
			tableCode = billJson.optString("table_code");
			billProperty = billJson.optString("bill_property");
			orderNum = billJson.optString("order_num");
			chanel = billJson.optString("source");
			difference = billJson.optDouble("difference");
			recoverCnt = billJson.optInt("recover_count");// 取得恢复账单次数
		}

		List<JSONObject> paymentList = null;
		if (SysDictionary.BILL_PROPERTY_CLOSED.equals(billProperty)){
			String reSql = new String("select id,jzid,name,amount,count,coalesce(number,'') as number,coalesce(phone,'') as phone,shift_id,pos_num,cashier_num,last_updatetime,coalesce(customer_id,0) as customer_id,coalesce(bill_code,'') as bill_code,coalesce(remark,'') as remark,currency_amount,payment_state from pos_bill_payment where bill_num=? and store_id=?");
			paymentList = paymentDao.query4Json(tenancyId, reSql, new Object[]{ billNum, storeId });
			double amount = 0d;
			for (JSONObject pay : paymentList){
				pay.put("payment_state_text", SysDictionary.PAYMENT_STATE_TEXT_MAP.get(pay.optString("payment_state")));
				amount = DoubleHelper.add(amount, pay.optDouble("currency_amount"), DEFAULT_SCALE);
			}
			difference = DoubleHelper.sub(paymentAmount, amount, DEFAULT_SCALE);
		}else{
			String sql = new String("select count(nullif(payment_state,?)) as count,coalesce(sum(currency_amount),0) as amount from pos_bill_payment where tenancy_id=? and store_id = ? and bill_num =?");
			SqlRowSet rs = paymentDao.query4SqlRowSet(sql.toString(), new Object[]{ SysDictionary.PAYMENT_STATE_PAY_COMPLETE, tenancyId, storeId, billNum });

			if (rs.next()){
				difference = DoubleHelper.sub(paymentAmount, rs.getDouble("amount"), DEFAULT_SCALE);
				if (0 == rs.getInt("count") && paymentAmount <= rs.getDouble("amount")){
					paymentState = SysDictionary.PAYMENT_STATE_PAY_COMPLETE;
				}
			}

			if (difference <= 0 && SysDictionary.PAYMENT_STATE_PAY_COMPLETE.equals(paymentState)){
				double moreCoupon = 0d;
				if (difference < 0){ 
					
					String paySql = new String("select sum(coalesce(p.currency_amount,0)) as total_amount,sum(case when w.payment_class=? or w.payment_class=? then coalesce(p.currency_amount,0) else 0 end) as coupons_amount,sum(case when w.payment_class=? then coalesce(p.currency_amount,0) else 0 end) as cash_amount from pos_bill_payment p left join payment_way w on p.tenancy_id=w.tenancy_id and p.jzid=w.id where p.tenancy_id=? and p.store_id=? and p.bill_num=?");
					rs = paymentDao.query4SqlRowSet(paySql.toString(), new Object[]{ SysDictionary.PAYMENT_CLASS_COUPONS, SysDictionary.PAYMENT_CLASS_MEIDA_COUPONS_PAY, SysDictionary.PAYMENT_CLASS_CASH, tenancyId, storeId, billNum });

					//double totalAmount = 0d;//付款合计
					double couponsAmount = 0d;//优惠劵金额
					double cashAmount = 0d;//现金金额
					double changeAmount = 0d;//找零金额
					if (rs.next()){
						couponsAmount = rs.getDouble("coupons_amount");
						cashAmount = rs.getDouble("cash_amount");
						if (cashAmount > 0){
							if (Math.abs(difference) > cashAmount){
								changeAmount = -cashAmount;
							}
							else{
								changeAmount = difference;
							}
						}

						if (couponsAmount > 0){
							moreCoupon = Math.abs(DoubleHelper.sub(difference, changeAmount, DEFAULT_SCALE));
						}
						difference = changeAmount;//账单付款差额=找零
					}
					
					if (changeAmount < 0 && cashAmount>0){ 
						Integer payWayId = null;
						// 保存的是本币对应的jzid
						JSONObject paymentWayJson = paymentDao.getPaymentWayForStandard(tenancyId, storeId);
						if (null != paymentWayJson && paymentWayJson.containsKey("payment_id")){
							payWayId = paymentWayJson.optInt("payment_id");
						}
						else{
							throw new SystemException(PosErrorCode.NOT_EXIST_PAY_WAY_STANDARD_ERROR);
						}
						paymentDao.insertPosBillPayment(tenancyId, storeId, billNum, DateUtil.parseDate(reportDate), shiftId, posNum, optNum, tableCode, SysDictionary.PAYMENT_CLASS_CASH, payWayId, "找零", "change", 1d, difference, 1d, difference, null, null, "N", null, null, null, currentTime,SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);
//						paymentDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, DateUtil.parseDate(reportDate), shiftId, posNum, optNum, tableCode, SysDictionary.PAYMENT_CLASS_CASH, payWayId, "找零", "change", 1d, difference, 1d, difference, null, null, "N", null, null, null, currentTime,SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);
					}
				}

				// 关闭账单
				StringBuilder updateBillSql = new StringBuilder("update pos_bill set more_coupon=?,difference =?,bill_property =?,payment_time =?,pos_num =?,cashier_num =?,shift_id =?,payment_state=?,shop_real_amount=(coalesce(payment_amount,0)-coalesce(platform_charge_amount,0)-coalesce(total_fees,0)) where tenancy_id=? and store_id = ? and bill_num =?");
				paymentDao.update(updateBillSql.toString(), new Object[]{ moreCoupon, difference, SysDictionary.BILL_PROPERTY_CLOSED, currentTime, posNum, optNum, shiftId, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, tenancyId, storeId, billNum });

				String updateItemSql = new String("update pos_bill_item set item_shift_id=? where bill_num = ? and store_id =? and tenancy_id=?");
				paymentDao.update(updateItemSql, new Object[]{shiftId, billNum, storeId, tenancyId });
				
				String updatePaymentSql = new String("update pos_bill_payment set shift_id=? where bill_num = ? and store_id =? and tenancy_id=?");
				paymentDao.update(updatePaymentSql, new Object[]{shiftId, billNum, storeId, tenancyId });
				
				// 当套餐明细数量大于1时，更新套餐明细单价item_price=item_amount/item_count，取2位小数，四舍五入
				int defaultScale = 2; // 保留尾数
				paymentDao.updateMeallistItemPrice(billNum, SysDictionary.ITEM_PROPERTY_MEALLIST, defaultScale, 1, storeId, tenancyId);
				
				String reSql = new String("select id,jzid,name,amount,count,coalesce(number,'') as number,coalesce(phone,'') as phone,shift_id,pos_num,cashier_num,last_updatetime,coalesce(customer_id,0) as customer_id,coalesce(bill_code,'') as bill_code,coalesce(remark,'') as remark,currency_amount,payment_state from pos_bill_payment where tenancy_id=? and store_id = ? and bill_num =?");
				paymentList = paymentDao.query4Json(tenancyId, reSql, new Object[]{ tenancyId, storeId, billNum });
				StringBuilder newstate = new StringBuilder();
				for (JSONObject pay : paymentList){
					newstate.append("支付方式:").append(pay.optString("name")).append(",").append("支付金额:").append(pay.optDouble("amount")).append(";");
					pay.put("payment_state_text", SysDictionary.PAYMENT_STATE_TEXT_MAP.get(pay.optString("payment_state")));
				}

				// 写日志pos_log
				paymentDao.savePosLog(tenancyId, storeId, posNum, optNum, optNum, shiftId, DateUtil.parseDate(reportDate), com.tzx.pos.base.Constant.TITLE, "结账-关闭账单", "账单编号:" + billNum + "; 应付金额:" + String.valueOf(paymentAmount), newstate.toString());

				StringBuilder qureyOrganSql = new StringBuilder("select format_state from organ where tenancy_id=? and id=?");
				rs = paymentDao.query4SqlRowSet(qureyOrganSql.toString(), new Object[]{ tenancyId, storeId });
				String formatState = null;
				if (rs.next()){
					formatState = rs.getString("format_state");
				}

				String mode = paymentDao.getSysParameter(tenancyId, storeId, "ZCDCMS");// 01,普通;02,自助;
				if (Tools.isNullOrEmpty(mode)){
					mode = "01";
				}
				
				// 正餐 更新桌位 更改桌位的状态，将桌位状态改为"空闲"
				if ("1".equals(formatState) && "01".equals(mode)){
					String fictitiousTable = null;
					StringBuilder qureyfictitiousTableSql = new StringBuilder("select fictitious_table from pos_bill where tenancy_id=? and store_id=? and bill_num =?");
					rs = paymentDao.query4SqlRowSet(qureyfictitiousTableSql.toString(), new Object[]{ tenancyId, storeId,billNum});
					if (rs.next()){
						fictitiousTable = rs.getString("fictitious_table");
					}
					StringBuffer updateTableState = new StringBuffer("update pos_tablestate set state = ?,lock_pos_num = ?,lock_opt_num=? where table_code = ? and store_id = ? and tenancy_id = ?");
					paymentDao.update(updateTableState.toString(), new Object[]
					{ SysDictionary.TABLE_STATE_FREE, null, null, fictitiousTable, storeId, tenancyId });
				}
				

				int printCount = Integer.parseInt(paymentDao.getSysParameter(tenancyId, storeId, "JZDDYSL"));

				if (null == printJson){
					printJson = new JSONObject();
				}
				printJson.put("tenancy_id", tenancyId);
				printJson.put("store_id", storeId);
				printJson.put("bill_num", billNum);
				printJson.put("pos_num", posNum);
				printJson.put("opt_num", optNum);
				printJson.put("report_date", reportDate);
				printJson.put("shift_id", shiftId);
				printJson.put("channel", chanel);
				printJson.put("format_state", formatState);
				printJson.put("format_mode", mode);
				printJson.put("isprint", isPrint);
				printJson.put("print_count", printCount);

				if (Tools.hv(orderNum)){
					try{
						orderSyncService.sync(OrdersSyncService.POSTPAY_ORDER_COMPLETE, orderNum);
					}
					catch (Exception e){
						e.printStackTrace();
						logger.error("更新订单状态失败:", e);
					}
				}
				
				// 生成电子发票
				JSONObject invoiceJo = paymentDao.getInvoiceInfo(tenancyId, storeId, billJson);
				if(!Tools.isNullOrEmpty(invoiceJo) && "2".equals(invoiceJo.optString("invoice_type"))) {
					if(invoiceJo.optDouble("invoice_amount") != 0){
						isInvoice = "1"; 
						paymentDao.deleteInvoiceByInvoiceId(tenancyId, storeId, billNum, invoiceJo.optInt("id"));
					}
					else{
						logger.info("已经开过电子发票，不可再开！");
					}
				}

				// 开具电子发票
				if("1".equals(isInvoice)){
					// 创建新的电子发票二维码
					logger.info("电子发票二维码生成开始!");
					// 准备生成电子发票的数据
					// 获取法人信息
					JSONObject legalPerInfoJo = paymentDao.getLegalPerInfo(tenancyId, storeId, billJson);
					if(!(Tools.isNullOrEmpty(legalPerInfoJo) || Double.isNaN(legalPerInfoJo.optDouble("tax_rate")))){
						// 计算开发票金额
						double invAmt = calcAmountForInvoice(tenancyId, storeId, billJson);
						if(Double.isNaN(invAmt)){
							invAmt = 0;
						}
						if(invAmt > 0){
							JSONObject jo = new JSONObject();
							// 生成电子发票，获得电子发票的打印信息
							try{
								jo.put("bill_num", billNum);
								jo.put("order_num", orderNum);
								jo.put("pos_num", posNum);
								jo.put("opt_num", optNum);
								jo.put("source", chanel);
								jo.put("report_date", reportDate);
								jo.put("tax_rate", legalPerInfoJo.optDouble("tax_rate"));
								jo.put("invoice_num", legalPerInfoJo.optString("invoice_num"));
								jo.put("invoice_amount", invAmt);
								jo.put("recover_count", recoverCnt);
								logger.info("电子发票二维码生成开始：传入参数：" + jo.toString());
								getPrintInvoiceInfo(tenancyId, storeId,DateUtil.parseDate(reportDate), jo);
							}
							catch(Exception e){
								e.printStackTrace();
								logger.error("电子发票二维码生成失败:", e);
							}
							logger.info("加密前的二维码URL:" + jo.optString("before_encode_url"));
							logger.info("生成的二维码URL: " + jo.optString("url_content"));
							
							printJson.put("is_invoice", isInvoice);
							printJson.put("url_content", jo.optString("url_content"));
							printJson.put("url_path", jo.optString("url_path"));
							resultJson.put("url_content", jo.optString("url_content"));
						}else{
							logger.info("电子发票金额为0!");
						}
					}
					else{
						logger.info("法人信息设置错误!");
					}
				}
				
				try{
					posService.updateDevicesDataState();
				}catch (Exception e){
					e.printStackTrace();
				}
			}
			else{
				String reSql = new String("select id,jzid,name,amount,count,coalesce(number,'') as number,coalesce(phone,'') as phone,shift_id,pos_num,cashier_num,last_updatetime,coalesce(customer_id,0) as customer_id,coalesce(bill_code,'') as bill_code,coalesce(remark,'') as remark,currency_amount,payment_state from pos_bill_payment where bill_num=? and store_id=?");
				paymentList = paymentDao.query4Json(tenancyId, reSql, new Object[]
				{ billNum, storeId });
				double amount = 0d;
				for (JSONObject pay : paymentList)
				{
					pay.put("payment_state_text", SysDictionary.PAYMENT_STATE_TEXT_MAP.get(pay.optString("payment_state")));
					amount = DoubleHelper.add(amount, pay.optDouble("currency_amount"), DEFAULT_SCALE);
				}

				difference = DoubleHelper.sub(paymentAmount, amount, DEFAULT_SCALE);
			}
			logger.info(Thread.currentThread().getId()+"-----订单转账单->关闭账单方法结束-- "+difference);
		}

		resultJson.put("bill_num", billNum);
		resultJson.put("table_code", tableCode);
		resultJson.put("payment_amount", paymentAmount);
		resultJson.put("discount_amount", discountAmount);
		resultJson.put("payment_state", paymentState);
		resultJson.put("payment_state_text", SysDictionary.PAYMENT_STATE_TEXT_MAP.get(paymentState));
		resultJson.put("difference", difference > 0 ? difference : 0d);
		resultJson.put("change", difference < 0 ? difference : 0d);
		resultJson.put("paymentlist", paymentList);
	}
	
	
	/**
	 * 支付类
	 * @param tenancyId
	 * @param paymentClass
	 * @throws Exception
	 */

//	private void getPaymentServiceBeanByPaymentClass(String tenancyId, String paymentClass) throws Exception{
//		switch (paymentClass)
//		{
//			case SysDictionary.PAYMENT_CLASS_FREESINGLE:
//				posPaymentService = SpringConext.getApplicationContext().getBean(FreesinglePaymentWayServiceImp.class);
//				break;
//			case SysDictionary.PAYMENT_CLASS_CASH:
//				posPaymentService = SpringConext.getApplicationContext().getBean(CashPaymentWayServiceImp.class);
//				break;
//			/*case SysDictionary.PAYMENT_CLASS_LOCAL_WECHAT_PAY:
//				posPaymentService = SpringConext.getApplicationContext().getBean(LocalWechatPayPaymentWayServiceImp.class);
//				break;*/
//			
//		}
//	}
	
	
    private JSONObject getPosBillPaymentByBillNum(String tenancyId,int storeId,String billNum,String tableCode,Double paymentAmount,Double discountAmount,Double difference,String paymentState) throws Exception{
		JSONObject paymentJson = new JSONObject();
		String reSql = new String("select id,jzid,name,amount,count,coalesce(number,'') as number,coalesce(phone,'') as phone,shift_id,pos_num,cashier_num,last_updatetime,coalesce(customer_id,0) as customer_id,coalesce(bill_code,'') as bill_code,coalesce(remark,'') as remark,currency_amount,payment_state from pos_bill_payment where bill_num=? and store_id=?");
		List<JSONObject> paymentList = paymentDao.query4Json(tenancyId, reSql, new Object[]{ billNum, storeId });
		double amount = 0d;
		for (JSONObject pay : paymentList){
			pay.put("payment_state_text", SysDictionary.PAYMENT_STATE_TEXT_MAP.get(pay.optString("payment_state")));
			amount = DoubleHelper.add(amount, pay.optDouble("currency_amount"), DEFAULT_SCALE);
		}

		difference = DoubleHelper.sub(paymentAmount, amount, DEFAULT_SCALE);

		JSONObject billJson = new JSONObject();
		billJson.put("bill_num", billNum);
		JSONObject invJo = paymentDao.getInvoiceInfo(tenancyId, storeId, billJson);
		String strUrlContent = "";
		if (null != invJo && !invJo.isEmpty()){
			strUrlContent = invJo.optString("url_content");
		}
		paymentJson.put("bill_num", billNum);
		paymentJson.put("table_code", tableCode);
		paymentJson.put("payment_amount", paymentAmount);
		paymentJson.put("discount_amount", discountAmount);
		paymentJson.put("payment_state", paymentState);
		paymentJson.put("payment_state_text", SysDictionary.PAYMENT_STATE_TEXT_MAP.get(paymentState));
		paymentJson.put("difference", difference > 0 ? difference : 0d);
		paymentJson.put("change", difference < 0 ? difference : 0d);
		paymentJson.put("paymentlist", paymentList);
		paymentJson.put("url_content", strUrlContent);
		return paymentJson;
	}

}
