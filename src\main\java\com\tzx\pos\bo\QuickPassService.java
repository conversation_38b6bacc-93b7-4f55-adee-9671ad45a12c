package com.tzx.pos.bo;

import net.sf.json.JSONObject;

import com.tzx.framework.common.entity.Data;

public interface QuickPassService {
	String	NAME	= "com.tzx.pos.bo.imp.QuickPassServiceImp";
	
	/**
	 * 插入数据
	 * 从MQ里取出数据 插入数据库中
	 * @return
	 */
	public int insert(JSONObject param) throws Exception;
	/**
	 * 查询数据
	 * @return
	 */
	public Data  findList(Data param) throws Exception;
	/**
	 * 删除数据
	 * 修改状态state 
	 * 1正常，0删除
	 * @return
	 */
	public Data deleteData(Data param) throws Exception;
}
