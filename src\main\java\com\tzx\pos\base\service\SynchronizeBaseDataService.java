package com.tzx.pos.base.service;

/**
 * <AUTHOR> on 2017-11-23
 */
public interface SynchronizeBaseDataService {

    /**
     *  同步类型
     */
    enum Type{
        /**
         * 全部数据
         */
        organ,
        /**
         * 终端设备
         */
        terminal;

    }

    /** 同步基础资料
     * @param type
     * @return 成功返回true
     */
    boolean syncData(Type type);

    /** 同步基础资料
     * @param type
     * @return 成功返回true
     */
    boolean syncData(Type type,String saasUrl);

    void syncData(Type organ, String saasUrl, String autoSynchronize);
}
