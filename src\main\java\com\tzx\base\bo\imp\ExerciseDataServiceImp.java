package com.tzx.base.bo.imp;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.tzx.base.bo.ExerciseDataService;
import com.tzx.base.po.springjdbc.dao.ProcessMessageDao;
import com.tzx.framework.common.util.GsonUtil;

import net.sf.json.JSONObject;

/** 删除门店测试数据
 * <AUTHOR>
 *
 */
@Service(ExerciseDataService.NAME)
public class ExerciseDataServiceImp implements ExerciseDataService
{
	@Resource(name = ProcessMessageDao.NAME)
	private ProcessMessageDao processMessageDao;
	
	@Override
	public String exerciseDataDelete(String tenancyId, int storeId, JSONObject param) throws Exception
	{
		String returnRes = "";

		JSONObject dataJson = null;
		if (param.containsKey("data") && 0 < param.optJSONArray("data").size())
		{
			dataJson = param.optJSONArray("data").getJSONObject(0);
		}

		if (null != dataJson && dataJson.containsKey("delStoreDataSql"))
		{
			String[] sqls = GsonUtil.toT(dataJson.opt("delStoreDataSql").toString(), String[].class);
			int[] res = processMessageDao.batchInsert(sqls);
			
			returnRes = "脚本执行成功!";
		}
		else
		{
			returnRes = "下发脚本为空!";
		}

		return returnRes;
	}

}
