package com.tzx.pos.bo.imp;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;

import com.tzx.base.bo.PosCodeService;
import com.tzx.framework.common.constant.Code;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.Tools;
import com.tzx.orders.po.springjdbc.dao.OrdersManagementDao;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.base.util.ReqDataUtil;
import com.tzx.pos.bo.PosService;
import com.tzx.pos.bo.TakeOrderingOpenTableService;
import com.tzx.pos.bo.TakeOrderingRefuseService;
import com.tzx.pos.bo.dto.TakeOrderingParam;
import com.tzx.pos.po.springjdbc.dao.PosDao;
import com.tzx.pos.po.springjdbc.dao.PosDishDao;

@Service(TakeOrderingOpenTableService.NAME)
public class TakeOrderingOpenTableServiceImp extends PosBaseServiceImp implements TakeOrderingOpenTableService {
	@Autowired 
	private PosDishDao posDishDao;
	@Autowired 
	PosCodeService codeService;
	@Autowired 
	private PosService posService;

	@Autowired 
	private PosDao posDao;
	
	@Autowired
	private OrdersManagementDao  ordersDao;
	
	@Autowired
	private TakeOrderingRefuseService takeOrderingRefuseService;

	public static final int			DEFAULT_SCALE	= 4;
	private static final Logger logger = Logger.getLogger(TakeOrderingOpenTableServiceImp.class);
	/**
	 * 开台接口
	 */
	public TakeOrderingParam insertTakeOpenTable(Data param, Data result, JSONObject json)throws Exception {
		
        logger.info(Thread.currentThread().getId() + "@-----------订单转账单开台---接单处理开台--------");
		Map<String, String> systemMap = com.tzx.framework.common.constant.Constant.getSystemMap();
        String tenantId= param.getTenancy_id();
        Integer organId =param.getStore_id();
        if(organId == 0 || organId == null){
        	  organId= Integer.parseInt(systemMap.get("store_id"));
        }
       
        // 开台传入的参数
		Map<String, Object> map = ReqDataUtil.getDataMap(param); 
	
		String chanel =SysDictionary.CHANEL_D5;
		
        String billCode = ParamUtil.getStringValue(map, "order_code", false, null);
        if(StringUtils.isEmpty(billCode)){
        	throw new SystemException(PosErrorCode.ONLY_CARD_PAYMENT);
        }else{
        	Boolean flag = posDao.getBillCode(tenantId, organId, billCode);
        	if(flag){
        		throw new SystemException(PosErrorCode.CANCEL_PAYMENT_FAIL);
        	}
        }
        
        Double paymentAmount = ParamUtil.getDoubleValue(map, "payment_amount", false, null);
        
        Double discountrAmount = ParamUtil.getDoubleValue(map, "discountr_amount", false, null);
       
        
        //获取当前机台号和签到人
        JSONObject optState = this.getPosOptState(tenantId, organId, billCode,chanel);
      
        String optNum =optState.getString("opt_num");
       
        String posNum = optState.getString("pos_num");
        
    	String waiterNum = ParamUtil.getStringValue(map, "waiter_num", false,null);
		Integer guest = ParamUtil.getIntegerValue(map, "guest", false, null); // 就餐人数
		Integer itemMenuId = ParamUtil.getIntegerValue(map, "item_menu_id", false, null);//菜谱id
		String mode = this.getMode(tenantId, organId);
		String tableCode = ParamUtil.getStringValue(map, "table_code", false, null);
		Double discountkAmount =ParamUtil.getDoubleValue(map, "discountk_amount", false, null);
		List<Map<String, Object>> items = (List<Map<String, Object>>) map.get("item"); //菜品相关
		
		List<Map<String, Object>> paymentList = (List<Map<String, Object>>) map.get("paymentlist"); //菜品相关
		double shopRealAmount = ParamUtil.getDoubleValue(map, "shop_real_amount", false, null);

		double platformChargeAmount = ParamUtil.getDoubleValue(map,"platform_charge_amount", false, null);
		String settlementType = ParamUtil.getStringValue(map,"settlement_type", false, null);
		Integer shiftId = posDao.getShiftId(tenantId, organId); //获取班次
        int discountModeId = 0;
		
		String saleMode  = SysDictionary.SALE_MODE_TS01;
		if (Tools.isNullOrEmpty(itemMenuId)) {
			itemMenuId = 0;
		}
		//获取报表日期
		Date reportDate = posDao.getReportDate(tenantId, organId);
		
		// POS账单编号
		String bill_num = "";
		// POS流水单号
		String serial_num = "";
		
		double serviceAmount = 0d;
		if (map.containsKey("service_amount")) {
			serviceAmount = ParamUtil.getDoubleValue(map, "service_amount",false, null);
		}
		
		if(StringUtils.isEmpty(billCode)){
			takeOrderingRefuseService.refuseOrderingEntry(billCode, Constant.BILL_CODE_IS_NULL, chanel);
		}
		
		Timestamp time = DateUtil.currentTimestamp();
		String bill_property = SysDictionary.BILL_PROPERTY_OPEN;
		String stable = tableCode;// 原桌位号
		
		try{
			// 生成新的账单号和流水单号
			JSONObject object = new JSONObject();
			object.element("store_id", organId);
			object.element("busi_date", reportDate);
			bill_num = codeService.getCode(tenantId, Code.POS_BILL_CODE, object);// 调用统一接口来实现
			object.put("pos_num", posNum);
			serial_num = codeService.getCode(tenantId, Code.POS_RECORD_CODE, object); // 调用统一接口来实现
		}
		catch (Exception e){
			logger.info("开台：" + ExceptionMessage.getExceptionMessage(e));
			throw new SystemException(PosErrorCode.GET_BILLNUM_SERVICE_ERROR);
		}
		
		TakeOrderingParam params = new TakeOrderingParam();
		params.setTenantId(tenantId);
		params.setOrganId(organId);
		params.setBillCode(billCode);
		params.setBillNum(bill_num);
		params.setChanel(chanel);
		params.setItems(items);
		params.setMode(saleMode);
		params.setOptNum(optNum);
		params.setPaymentList(paymentList);
		params.setPosNum(posNum);
		params.setReportDate(reportDate);
		params.setSaleMode(saleMode);
		params.setSerialNum(serial_num);
		params.setTableCode(tableCode);
		params.setShiftId(shiftId);
		params.setMode(mode);
		params.setBillProperty(bill_property);
		params.setServiceAmount(serviceAmount);
		params.setPaymentAmount(paymentAmount);
		params.setDiscountrAmount(discountrAmount);
		params.setDiscountkAmount(discountkAmount);
        if(StringUtils.isEmpty(tableCode) && mode.equals("0")){
        	
        	String tableCodes =this.getTables(tenantId, organId);
        	params.setTableCode(tableCodes);
        	tableCode =tableCodes;
        }
		//桌位编号不为空开台模式
        if(StringUtils.isNotEmpty(tableCode) && mode.equals("0")){
        	this.dinnerOpenTable(params);
        }else if(mode.equals("1") && StringUtils.isEmpty(tableCode)){
            this.fastOpenTable(params);
        }
       
		// 处理服务费
		//updateTableService(tenantId, organId, bill_num, chanel, tableCode,params.getServiceId(), serviceAmount);

		// 生成新账单
		String str0 = new String("insert into pos_bill_waiter (tenancy_id,store_id,bill_num,waiter_num,report_date) values (?,?,?,?,?)");
		posDishDao.update(str0, new Object[] { tenantId, organId, bill_num,waiterNum, reportDate });
        
		posDishDao.insertPosBillTake(tenantId, organId, reportDate, shiftId,
				bill_num, null, serial_num, billCode, time, posNum, optNum,
				waiterNum, stable, guest, itemMenuId, saleMode, chanel,
				0, serviceAmount, null, null, bill_property,
				SysDictionary.PAYMENT_STATE_NOTPAY, discountModeId,
				discountrAmount, shopRealAmount, platformChargeAmount,
				settlementType, 0, params.getTableCode());

		posDishDao.savePosLog(tenantId, organId, posNum, optNum, optNum, shiftId,reportDate, Constant.TITLE, "开台", "开台桌位:" + "SSS", "账单编号:"+ bill_num);
		try {
			posService.updateDevicesDataState();
		} catch (Exception e) {
			e.printStackTrace();
		}
		if(discountrAmount!=0){
	        this.updateDiscount(organId, bill_num, discountrAmount);
	    }
		return params;
	}

	/**
	 * 得到收银员编号和机台号
	 * @param tenancyId
	 * @param storeId
	 * @return
	 * @throws Exception
	 */
    public JSONObject getPosOptState(String tenancyId, int storeId,String billCode,String chanel) throws Exception {
    	logger.info(Thread.currentThread().getId() + "@-----------订单转账单--获取签到人员机台号--------tenancyId=="+tenancyId+"----storeId=="+storeId+"----billCode=="+billCode+"----chanel=="+chanel);
        JSONObject reportDate = ordersDao.getCurrentReportDate(tenancyId, storeId);
        StringBuilder queryOptSql = new StringBuilder();
        queryOptSql.append(" select os1.tenancy_id,os1.store_id,os1.opt_num,os1.pos_num,os1.report_date,os1.shift_id from pos_opt_state os1 left join pos_opt_state os2 on os1.store_id=os2.store_id and os1.report_date=os2.report_date");
        queryOptSql.append(" and os1.pos_num=os2.pos_num and os1.opt_num=os2.opt_num and os1.content=? and os2.content=? and os1.last_updatetime<os2.last_updatetime");
        queryOptSql.append(" where os1.tenancy_id=? and os1.store_id=? and os1.report_date=?  and os1.content=? and os1.tag='0' and os2.id is null");

        List<JSONObject> rso = ordersDao.query4Json(tenancyId, queryOptSql.toString(), new Object[]{SysDictionary.OPT_STATE_KSSY, SysDictionary.OPT_STATE_YYTC, tenancyId, storeId, new SimpleDateFormat("yyyy-MM-dd").parse(reportDate.optString("report_date")), SysDictionary.OPT_STATE_KSSY});

        if (null != rso && !rso.isEmpty()) {
        	logger.info(Thread.currentThread().getId() + "@-----------订单转账单--获取签到人员机台号----结束========");
            return rso.get(0);
        } else {
        	takeOrderingRefuseService.refuseOrderingEntry(billCode, Constant.TAKE_CASHIER_NOT_SIGNIN, chanel);
            throw SystemException.getInstance(PosErrorCode.POS_CASHIER_NOT_SIGNIN_ERROR);
        }
    }
   /* *//**
	 * 修改桌台服务费
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @param chanel
	 * @param talbeCode
	 * @param serviceId
	 * @param serviceAmount
	 * @throws Exception
	 *//*
	private void updateTableService(String tenantId, Integer storeId,String billNum, String chanel,String talbeCode,Integer serviceId,Double serviceAmount) throws Exception{
		if (null != serviceId && serviceId != 0){
			StringBuilder sqlService = new StringBuilder("insert into pos_bill_service(tenancy_id,store_id,bill_num,table_code,service_id,service_type,taken_mode,service_scale,service_count, service_rate)");
			sqlService.append(" select tenancy_id,").append(storeId).append(",'").append(billNum).append("','").append(talbeCode).append("',id, fee_type, taken_mode,");
			sqlService.append(" (case taken_mode when '").append( SysDictionary.SERVICE_MODE_GD01 ).append("' then guding_jj else fwfl end) as service_scale,1,100");
			sqlService.append(" from hq_service_fee_type where id ='").append(serviceId).append("' and tenancy_id = '").append(tenantId).append("' ");
			posDishDao.update(sqlService.toString(), new Object[] {});
		}else if (null != serviceAmount && serviceAmount > 0){
			StringBuilder sqlService = new StringBuilder("insert into pos_bill_service(tenancy_id,store_id,bill_num,table_code,service_id,service_type,taken_mode,service_scale,service_count, service_rate)");
			sqlService.append(" values(?,?,?,?,?,?,?,?,?,?)");
			posDishDao.update(sqlService.toString(), new Object[] {tenantId,storeId,billNum,talbeCode,serviceId,SysDictionary.SERVICE_FEE_TYPE_JB01,SysDictionary.SERVICE_MODE_GD01,serviceAmount,1d, 100d});
		}
	}*/
	/**
	 * 桌位编号不为空开台方式
	 * @param tenantId
	 * @param organId
	 * @param tableCode
	 * @param chanel
	 * @param serviceId
	 * @throws Exception
	 */
	public void dinnerOpenTable(TakeOrderingParam params) throws Exception{
		logger.info(Thread.currentThread().getId()+"@-----正餐开台--  params========"+params);
		String tenantId =params.getTenantId();
		Integer organId = params.getOrganId();
		String tableCode = params.getTableCode();
		String chanel = params.getChanel();
		String billCode = params.getBillCode();
		StringBuffer sqlState = new StringBuffer("select ti.table_code,ti.fwfz_id,pt.state from tables_info ti left join pos_tablestate pt on ti.tenancy_id=pt.tenancy_id and ti.organ_id=pt.store_id and ti.table_code=pt.table_code where ti.tenancy_id=? and ti.organ_id=? and ti.table_code=?");
		SqlRowSet tableRs = posDishDao.query4SqlRowSet(sqlState.toString(), new Object[]{tenantId,organId,tableCode});
		if(tableRs.next()){
			if(!SysDictionary.TABLE_STATE_FREE.equals(tableRs.getString("state"))){
				//takeOrderingRefuseService.refuseOrderingEntry(billCode, SysDictionary.TAKE_TABLE_STATE_FREE, chanel);
				//throw SystemException.getInstance(PosErrorCode.TABLE_STATE_BUSY);
				this.removeTable(tableCode, tenantId, organId,params);
			}
		}else{
			throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_TABLE_CODE);
		}

		StringBuffer updateSql = new StringBuffer("update pos_tablestate set state = ?, bill_batch_num=? where table_code = ? and store_id = ? and tenancy_id = ?");
		posDishDao.update(updateSql.toString(), new Object[]{ SysDictionary.TABLE_STATE_BUSY,null, params.getTableCode(), organId, tenantId });
	}
	
	/**
	 * 桌位号为空开台模式
	 * @param tenantId
	 * @param organId
	 * @param reportDate
	 * @param bill_num
	 * @param posNum
	 * @param serial_num
	 * @throws Exception
	 */
	public void fastOpenTable(TakeOrderingParam params) throws Exception{
		logger.info(Thread.currentThread().getId()+"@-----订单转账单快餐开台开始--  params==========="+params);
		String tenantId =params.getTenantId();
		Integer organId = params.getOrganId();
		Date reportDate = params.getReportDate();
		String posNum = params.getPosNum();
		String bill_num = params.getBillNum();
		String serial_num = params.getSerialNum();
		logger.info(Thread.currentThread().getId()+"@-----订单转账单快餐开台开始--  tenantId=+"+tenantId+"----organId="+organId+"----bill_num="+bill_num+"----posNum="+posNum+"----mode="+params.getMode()+"----serial_num="+serial_num);
		JSONObject object = new JSONObject();
		object.put("store_id", organId);
		object.put("busi_date", reportDate);
		object.put("pos_num", posNum);
		params.setMode("1");
        logger.info(Thread.currentThread().getId()+"@生成POS流水单号:"+serial_num);
        logger.info(Thread.currentThread().getId()+"订单转账单快餐开台----结束----");
	}
	
	/**
	 * 拆台模式
	 * @param tableCode
	 * @param tenantId
	 * @param organId
	 * @throws Exception
	 */
	public void removeTable(String tableCode,String tenantId,Integer organId,TakeOrderingParam params) throws Exception{
		logger.info(Thread.currentThread().getId() + "@-----------桌位号=="+tableCode+"--被占用--启动拆台模式------");
		int  openNum = 1; // 拆分编号 默认为 1

		StringBuffer openNumSql = new StringBuffer("select max(table_open_num) as max_table_open_num from pos_tablestate where table_open_num is not null  and stable= '");
		openNumSql.append(tableCode).append("'");
		SqlRowSet openNumRs = posDishDao.query4SqlRowSet(openNumSql.toString());
		if(openNumRs.next()){
			openNum = openNumRs.getInt("max_table_open_num")+1;
		}
		
		String tableCodes = tableCode+"_"+openNum;//拆分后的桌位号
		params.setTableCode(tableCodes);
		String insertTablestate ="insert into pos_tablestate (tenancy_id,store_id,table_code,state,last_updatetime,table_open_num,stable) VALUES (?,?,?,?,?,?,?)";
		posDishDao.update(insertTablestate, new Object[]{tenantId,organId,tableCodes,SysDictionary.TABLE_STATE_BUSY,DateUtil.currentTimestamp(),openNum,tableCode});
	}
	
	/**
	 * 查询正餐还是快餐
	 * @param tenantId
	 * @param organId
	 * @return
	 * @throws Exception
	 */
	public String getMode(String tenantId,Integer organId) throws Exception{
		logger.info(Thread.currentThread().getId() + "@-----------订单转账单开台---查询门店类型----开始----");
		String sql = "select t.format_state from organ t where t.tenancy_id=? and t.id=?";
		SqlRowSet rs = posDao.query4SqlRowSet(sql, new Object[]{tenantId,organId});
		if(rs.next()){
			String mode =rs.getString("format_state");
			if(mode.equals("1"))
				return "0";
			else
				return "1";
		}
		logger.info(Thread.currentThread().getId() + "@-----------订单转账单开台---查询门店类型----结束----");
		return null;
	}
	/**
	 * 正餐桌位传空处理
	 * @param tenantId
	 * @param organId
	 * @return
	 * @throws Exception 
	 */
	public String getTables(String tenantId,Integer organId) throws Exception{
		String sql = "select t.table_code from pos_tablestate t  where t.state='FREE' and t.tenancy_id=? and t.store_id=? and t.stable is null limit 1 ";
		SqlRowSet rs = posDao.query4SqlRowSet(sql, new Object[]{tenantId,organId});
		if(rs.next()){
			String table_code =rs.getString("table_code");
			return table_code;
		}else{
			return this.getBusyTables(tenantId, organId);
		}
	}
	
	public String getBusyTables(String tenantId,Integer organId) throws Exception{
		logger.info(Thread.currentThread().getId() + "@-----------订单转账单开台---桌位沾满情况----");
		String sql = "select * from pos_tablestate t where t.state='BUSY' and t.stable is null and t.tenancy_id=? and t.store_id=? order by random() limit 1  ";
		SqlRowSet rs = posDao.query4SqlRowSet(sql, new Object[]{tenantId,organId});
		if(rs.next()){
			String table_code =rs.getString("table_code");
			return table_code;
		}else{
			
		}
		logger.info(Thread.currentThread().getId() + "@-----------订单转账单开台---查询门店类型----结束----");
		return null;
	}
	
	/**
	 * 账单折扣
	 * @param organId
	 * @param billno
	 * @param discountraAmount
	 * @throws Exception
	 */
	public void updateDiscount(Integer organId ,String billno,Double discountraAmount) throws Exception{
		logger.info(Thread.currentThread().getId() + "@-----------订单转账单---折扣----开始----discountraAmount==="+discountraAmount);
		Integer discount_id = 0;
		Double discount_rate = 100d;
		/*Integer discount_mode = 9;
		Integer discount_reason_id =1005;*/
		String sqlBill = " update pos_bill set  discount_mode_id = ?, discount_rate=?, discountr_amount=?, discount_case_id=? where bill_num = ? and store_id = ? ";
		String sqlBillItem = "update pos_bill_item set discount_mode_id = ?, discount_reason_id = ?, discount_rate = ? where bill_num = ? and store_id = ? ";
		
		posDishDao.update(sqlBill, new Object[] {  null, discount_rate, discountraAmount, discount_id, billno, organId });
		//更新账单明细
		sqlBillItem += " and (discount_mode_id <> 10 or discount_mode_id is null) ";

		posDishDao.update(sqlBillItem.toString(), new Object[]{ null, null, discount_rate, billno, organId });
		logger.info(Thread.currentThread().getId() + "@-----------订单转账单---折扣----结束----");
    }
}
