package com.tzx.base.common.exception;

import com.tzx.framework.common.exception.ErrorCode;

public enum AcewillErrorCode implements ErrorCode
{

	
	/**
	 * 消息不能为空
	 */
	MESSAGE_EMPTY(60001),
	
	/**
	 * 未知的付款来源:{0}
	 */
	PAYMENT_WAY_UNKNOW(60002),
	/**
	 * 付款方式{0}未开通
	 */
	PAYMENT_WAY_NOT_OPEN_ON_STORE(60003),
	/**
	 * 落单失败
	 */
	INSERT_ORDER_FAIL(60004);
	;

	private final int	number;

	private AcewillErrorCode(int number)
	{
		this.number = number;
	}

	public int getNumber()
	{
		return number;
	}

	@Override
	public String getMessage()
	{
		return "";
	}

}
