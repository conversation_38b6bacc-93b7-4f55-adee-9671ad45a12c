package com.tzx.pos.bo.payment.imp.ext;

import java.sql.Timestamp;
import java.util.Date;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Service;

import com.tzx.base.entity.PosBillPayment;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.util.Tools;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.bo.payment.PaymentWayService;
import com.tzx.pos.bo.payment.imp.PaymentWayServiceImp;

/**
 * 银行卡
 *
 */
@Service("com.tzx.pos.bo.payment.imp.ext.BankcardPaymentWayServiceImp")
public class BankcardPaymentWayServiceImp extends PaymentWayServiceImp implements PaymentWayService
{
	@Override
	public void payment(String tenantId, int storeId, String billNum,String batchNum, Date reportDate, int shiftId, String posNum, String optNum, String tableCode, JSONObject payment, Timestamp currentTime,String isOnlinePay) throws Exception
	{
		double amount = payment.optDouble("amount");
		double count = payment.optDouble("count");
		double currencyAmount = payment.optDouble("currency_amount");
		String number = ParamUtil.getStringValueByObject(payment, "number");
		String phone = ParamUtil.getStringValueByObject(payment, "phone");
		String billCode = ParamUtil.getStringValueByObject(payment, "bill_code");

		int jzid = payment.optInt("jzid");
		String paymentClass = payment.optString("payment_class");
		String paymentName = payment.optString("payment_name");
		String nameEnglish = payment.optString("payment_english_name");
		double rate = payment.optDouble("rate");
		String remark = ParamUtil.getStringValueByObject(payment,"remark");
		String isYsk = ParamUtil.getStringValueByObject(payment,"is_ysk");
		if (Tools.isNullOrEmpty(isYsk))
		{
			isYsk = "N";
		}
		
		Double due = currencyAmount;
		Double tenancyAssume = 0d;
		if ("0".equals(payment.optString("if_income")))
		{
			due = 0d;
			tenancyAssume = currencyAmount;
		}
		
		PosBillPayment paymentEntity = new PosBillPayment(tenantId, storeId, reportDate, shiftId, posNum, optNum, billNum, batchNum, tableCode, paymentClass, jzid, paymentName, nameEnglish, amount, currencyAmount, Double.valueOf(count).intValue(), number, phone, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
		paymentEntity.setRate(rate);
		paymentEntity.setIs_ysk(isYsk);
		paymentEntity.setBill_code(billCode);
		paymentEntity.setRemark(remark);
		paymentEntity.setCoupon_buy_price(due);
		paymentEntity.setDue(due);
		paymentEntity.setTenancy_assume(tenancyAssume);
		paymentDao.insertPosBillPayment(tenantId, storeId, paymentEntity);

//		paymentDao.insertPosBillPayment(tenantId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, nameEnglish, rate, amount, count, currencyAmount, number, phone, isYsk, null, billCode, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE,
//				batchNum, null, 0d);

//		paymentDao.insertPosBillPaymentLog(tenantId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, nameEnglish, rate, amount, count, currencyAmount, number, phone, isYsk, null, billCode, remark, currentTime,
//				SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);
	}

	@Override
	public void paymentDebit(String tenancyId, int storeId, String billNum,String orderNum,String chanel, Double paymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData) throws Exception
	{
		// TODO Auto-generated method stub

	}

	@Override
	public void findPaymentDebit(String tenancyId, int storeId, String billNum,String orderNum,String chanel,Double paymentAmount,JSONObject payment,Timestamp currentTime,JSONObject resultJson,Data resultData) throws Exception
	{
		// TODO Auto-generated method stub

	}

	@Override
	public void paymentRefund(String tenancyId, int storeId, String billNum,String oldBillNum,String orderNum,String chanel,Double billPaymentAmount,JSONObject payment,Timestamp currentTime,JSONObject resultJson,Data resultData,String operType) throws Exception
	{
		// TODO Auto-generated method stub

	}

	@Override
	public void paymentCancel(String tenancyId, int storeId, String billNum, String orderNum, String chanel, Double billPaymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData, String operType) throws Exception
	{
		// TODO Auto-generated method stub
		
	}

	@Override
	public void paymentRegain(String tenancyId, int storeId, String billNum, String orderNum, String chanel, Double billPaymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData, String operType) throws Exception
	{
		// TODO Auto-generated method stub
		
	}

	@Override
	public void paymentDebitResult(String tenancyId, int storeId, String billNum, JSONObject paymentJson) throws Exception
	{
		// TODO 自动生成的方法存根
		
	}
}
