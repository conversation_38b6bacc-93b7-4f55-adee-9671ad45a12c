package com.tzx.framework.common.util.io;
import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.StringReader;
import java.io.StringWriter;

public class StringFileUtils {
	// String to InputStream
	public static InputStream String2InputStream(String str) {
		ByteArrayInputStream stream = new ByteArrayInputStream(str.getBytes());
		return stream;
	}

	// InputStream --> String
	private String inputStream2String(InputStream is,String encoding) {
		StringBuffer buffer = new StringBuffer();
		try {
			//BufferedReader in = new BufferedReader(new InputStreamReader(is,"UTF-8"));
			BufferedReader in = new BufferedReader(new InputStreamReader(is,encoding));
			String line = "";
			while ((line = in.readLine()) != null) {
				buffer.append(line);
			}
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return buffer.toString();
	}
	
	// InputStream to String
	public static String inputStream2String(InputStream is) {
		StringBuffer buffer = new StringBuffer();
		try {
			BufferedReader in = new BufferedReader(new InputStreamReader(is,"UTF-8"));
			String line = "";
			while ((line = in.readLine()) != null) {
				buffer.append(line);
			}
		} catch (IOException e) {
			e.printStackTrace();
		}
		return buffer.toString();
	}
	
	
	 /** 
     * ���ַ�д��ָ���ļ�(��ָ���ĸ�·�����ļ��в�����ʱ��������޶�ȥ�������Ա�֤����ɹ���) 
     * 
     * @param res            ԭ�ַ� 
     * @param filePath �ļ�·�� 
     * @return �ɹ���� 
     */ 
    public static boolean string2File(String res, String filePath) { 
            boolean flag = true; 
            BufferedReader bufferedReader = null; 
            BufferedWriter bufferedWriter = null; 
            try { 
                    File distFile = new File(filePath); 
                    if (!distFile.getParentFile().exists()) distFile.getParentFile().mkdirs(); 
                    bufferedReader = new BufferedReader(new StringReader(res)); 
                    bufferedWriter = new BufferedWriter(new FileWriter(distFile)); 
                    char buf[] = new char[1024];         //�ַ���� 
                    int len; 
                    while ((len = bufferedReader.read(buf)) != -1) { 
                            bufferedWriter.write(buf, 0, len); 
                    } 
                    bufferedWriter.flush(); 
                    bufferedReader.close(); 
                    bufferedWriter.close(); 
            } catch (IOException e) { 
                    e.printStackTrace(); 
                    flag = false; 
                    return flag; 
            } finally { 
                    if (bufferedReader != null) { 
                            try { 
                                    bufferedReader.close(); 
                            } catch (IOException e) { 
                                    e.printStackTrace(); 
                            } 
                    } 
            } 
            return flag; 
    } 
    
    
    /** 
     * �ı��ļ�ת��Ϊָ��������ַ� 
     * 
     * @param file         �ı��ļ� 
     * @param encoding �������� 
     * @return ת������ַ� 
     * @throws IOException 
     */ 
    public static String file2String(File file, String encoding) { 
            InputStreamReader reader = null; 
            StringWriter writer = new StringWriter(); 
            try { 
                    if (encoding == null || "".equals(encoding.trim())) { 
                            reader = new InputStreamReader(new FileInputStream(file), encoding); 
                    } else { 
                            reader = new InputStreamReader(new FileInputStream(file)); 
                    } 
                    //��������д������� 
                    char[] buffer = new char[12000555]; // 12000�ǿ����趨�ġ�
                    int n = 0; 
                    while (-1 != (n = reader.read(buffer))) { 
                            writer.write(buffer, 0, n); 
                    } 
            } catch (Exception e) { 
                    e.printStackTrace(); 
                    return null; 
            } finally { 
                    if (reader != null) 
                            try { 
                                    reader.close(); 
                            } catch (IOException e) { 
                                    e.printStackTrace(); 
                            } 
            } 
            //����ת����� 
            if (writer != null) 
                    return writer.toString(); 
            else return null; 
    } 
    
    
    //�Ƽ�ʹ�õ�file2String ����
    public static String fileToString(String file){ 
		StringBuffer sb = null;
		 try{
		       sb = new StringBuffer();
		       BufferedReader in = new BufferedReader(new FileReader(file));
		       String s = "";
		       while((s = in.readLine()) != null){
		    	   sb.append(s);
		    	   sb.append("\n");
		       }
		       in.close();
		      
		      }catch(Exception   e){   
		      //System.out.println(e);     
		      }   
		      return sb.toString();
	}
    

    public static void main(String args[]){
    	//String str = "xxxxxxxxxxxxxxxx";
    	
    	 String returnxml = "<?xml version='1.0' encoding='UTF-8'?><DATAPACKET><MSG><SUBSYSTEM>BASIC</SUBSYSTEM><DATATYPE>SINGLE</DATATYPE><OPERATOR>UPDATE</OPERATOR>"+
		  "<SERVERDATA>VST_SHOPMONITOR</SERVERDATA><CHILDDATA>VST_SHOPMONITOR</CHILDDATA><PRIMARY>ID</PRIMARY><DATAS>" +
		  "<DATA Name='VST_SHOPMONITOR'><ROWDATA>"+
		     "<ROW STATU='0' OPTDATE='"+23323+"'  ORGID='"+2323+"'/>"+
		    "</ROWDATA></DATA></DATAS></MSG></DATAPACKET>";
    	string2File(returnxml,"c:/testt.xml");
    	//File file = new File("c:/reportin_InputXML.xml");
    	////System.out.println(file2String(file,"UTF-8"));
    }

}
