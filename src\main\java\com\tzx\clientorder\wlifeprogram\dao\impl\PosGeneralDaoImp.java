package com.tzx.clientorder.wlifeprogram.dao.impl;

import com.tzx.clientorder.wlifeprogram.dao.PosGenericDao;
import com.tzx.framework.common.util.Tools;
import com.tzx.pos.base.constant.SysDictionary;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by qingui on 2018-06-26.
 */
@Repository(PosGenericDao.name)
public class PosGeneralDaoImp extends WlifeCommonDaoImpl implements PosGenericDao {

    @Override
    public JSONObject getOrganStatus(String tenancyId, int organId) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" select * from pos_opt_state pt ");
        sql.append(" where id >");
        sql.append(" (case when (select count(1) from pos_opt_state ");
        sql.append(" where store_id =pt.store_id and content = 'DAYEND') = 0 then 0 else");
        sql.append(" (select id  from pos_opt_state ");
        sql.append(" where store_id =pt.store_id and content = 'DAYEND'");
        sql.append(" order by id desc limit 1) end)");
        sql.append(" and content = 'DAYBEGAIN' and store_id = " + organId);
        List<JSONObject> list = this.query4Json(tenancyId, sql.toString());
        if (null != list && list.size() > 0)
            return list.get(0);
        return null;
    }

    @Override
    public List<JSONObject> getSetmeal(String tenancyId, String billNum) throws Exception {
        StringBuffer sql = new StringBuffer();
        String itemProperty = SysDictionary.ITEM_PROPERTY_SETMEAL;
        sql.append(" select pbi.id,pbi.item_id as did,pbi.item_num as dishsno,pbi.item_name as name,pbi.item_unit_id as duid,item_count as number,")
                .append(" pbi.item_price as price,pbi.item_price as orgprice,case when pbi.discount_mode_id = 6 then pbi.third_price else pbi.item_price end as memberprice,")
                .append(" 1 as bmemberprice, 0 as bargainprice, '2' as type, pbi.method_money+pbi.assist_money as aprice,")  //case when pbi.discount_mode_id = 6 then 1 else 0 end as bmemberprice
                .append(" pbi.item_taste as memos, pbi.real_amount as realprice, case when pbi.item_remark='FS02' then 1 else 0 end as bgift, case when hii.is_modifyquantity = 'Y' then 1 else 0 end as is_weigh,")
                .append(" '' as bbuysno, '' as bgiftsno, hii.item_class as pkid, pbi.setmeal_rwid")
                .append(" from pos_bill_item pbi")
                .append(" left join hq_item_info hii on pbi.item_id = hii.id")
                .append(" where pbi.item_property = '"+ itemProperty +"' and pbi.bill_num = '"+ billNum +"' and (pbi.item_remark is null or (pbi.item_remark  <> 'TC01' and pbi.item_remark  <> 'CJ05')) ");
        List<JSONObject> list = this.query4Json(tenancyId, sql.toString());
        return list;
    }

    @Override
    public List<JSONObject> getSetmealDish(String tenancyId, String billNum, String type, int stemealCount, String setmealId, String setmeal_rwid) throws Exception {
        String is_itemgroup = "";
        //明细
        String itemProperty = SysDictionary.ITEM_PROPERTY_MEALLIST;
        String column = "";
        //如果是套餐主菜菜品的查询
        if ("main".equals(type)){
            is_itemgroup = "N";
        }else {
            //套餐必选菜
            is_itemgroup = "Y";
            column = "(pbi.method_money+pbi.assist_money)/"+ stemealCount +" as aprice,null as mid,";
        }
        StringBuffer sql = new StringBuffer();
        sql.append(" select bi.bill_num,pbi.item_id as did,pbi.item_num as dishsno,pbi.item_name as name,pbi.item_unit_id as duid,")
                .append(column)
                .append("pbi.item_count/"+ stemealCount +" as number")
                .append(" from pos_bill_item pbi ")
                .append(" left join pos_bill_item bi on pbi.setmeal_id = bi.item_id and pbi.setmeal_rwid = bi.item_serial")
                .append(" left join hq_item_combo_details cd on pbi.assist_item_id=cd.id")
                .append(" where pbi.item_property = '"+ itemProperty +"' and cd.is_itemgroup = '"+ is_itemgroup +"' and pbi.bill_num = '"+ billNum +"' and (pbi.item_remark is null or (pbi.item_remark  <> 'TC01' and pbi.item_remark  <> 'CJ05'))")
                .append(" and cd.iitem_id = '"+ setmealId +"' and bi.bill_num = '"+ billNum +"' and pbi.setmeal_rwid = '" + setmeal_rwid +"'");
        List<JSONObject> list = this.query4Json(tenancyId, sql.toString());
        return list;
    }

    @Override
    public List<JSONObject> getNormalitems(String tenancyId, String billNum) throws Exception {
        StringBuffer sql = new StringBuffer();
        String itemProperty = SysDictionary.ITEM_PROPERTY_SINGLE;
        sql.append(" select pbi.id,pbi.item_id as did,pbi.item_num as dishsno,pbi.item_name as name,pbi.item_unit_id as duid,pbi.item_unit_name as dishunit,pbi.item_unit_name as normalname,pbi.item_count as number,")
                .append(" pbi.item_price as price,pbi.item_price as orgprice,case when pbi.discount_mode_id = 6 then pbi.third_price else pbi.item_price end as memberprice,")
                .append(" '1' as type,case when pbi.item_remark='FS02' then 1 else 0 end as bgift,")
                .append(" case when hii.is_modifyquantity = 'Y' then 1 else 0 end as is_weigh, 1 as bmemberprice,") //case when pbi.discount_mode_id = 6 then 1 else 0 end as bmemberprice
                .append(" 0 as bargainprice, pbi.method_money+pbi.assist_money as aprice,hii.item_class as pkid,pbi.item_taste as memos,pbi.real_amount as realprice")
                .append(" from pos_bill_item pbi")
                .append(" left join pos_bill pb on pbi.bill_num = pb.bill_num ")
                .append(" left join hq_item_info hii on pbi.item_id = hii.id")
                .append(" where pbi.item_property = '"+ itemProperty +"' and pb.bill_num = '"+ billNum +"' and (pbi.item_remark is null or (pbi.item_remark  <> 'TC01' and pbi.item_remark  <> 'CJ05')) ");
        List<JSONObject> list = this.query4Json(tenancyId, sql.toString());
        return list;
    }

    @Override
    public JSONObject getBillInfo(String tenancyId, String tableCode, int storeId) throws Exception {
        StringBuffer sql = new StringBuffer();
        sql.append(" select pb.bill_num as oid,pb.remark as text,pb.bill_amount as total,pb.payment_amount as cost,pb.payment_amount as discountable,pb.payment_amount as membercouponsprice,pb.guest as people,pb.service_amount as mealfee, ")
                .append(" case when t.lock_pos_num is null or t.lock_pos_num = '' then '1' else '3' end as is_locked,pb.order_num as out_order_id,")
                .append(" pb.discount_mode_id,hdc.discount_case_name,pb.discount_rate,pb.discount_amount,o.org_full_name as shop_name,")
                .append(" case when pb.discount_mode_id = " + SysDictionary.DISCOUNT_MODE_6 + " then pb.discountk_amount else 0 end as memberPrice,")
                .append(" pb.discount_amount as discount_money,")
                .append(" case when pbp.type = 'wechat_pay_wlife' then pb.payment_amount else 0 end as weiXinPay ")
                .append(" from pos_bill pb left join pos_tablestate t on pb.store_id = t.store_id and pb.fictitious_table = t.table_code")
                .append(" left join hq_discount_case hdc on pb.discount_case_id = hdc.id ")
                .append(" left join organ o on pb.store_id = o.id ")
                .append(" left join pos_bill_payment pbp on pb.bill_num = pbp.bill_num")
                .append(" where pb.store_id = "+ storeId +" and pb.fictitious_table = '"+ tableCode +"' and pb.bill_property = 'OPEN'");
        List<JSONObject> list = this.query4Json(tenancyId, sql.toString());
        if (null != list && list.size()>0 )
            return list.get(0);
        return null;
    }

    @Override
    public List<JSONObject> getCooks(String tenancyId, Integer id) throws Exception {
        StringBuffer sql = new StringBuffer();
        sql.append(" select pzi.zfkw_id as id,pzi.zfkw_name as cook,pzi.amount as aprice from pos_bill_item pbi")
                .append(" right join pos_zfkw_item pzi on pbi.item_id = pzi.item_id and pbi.store_id = pzi.store_id")
                .append(" and pbi.bill_num = pzi.bill_num and pbi.rwid = pzi.rwid")
                .append(" where pbi.id = " + id );
        return this.query4Json(tenancyId, sql.toString());
    }

    @Override
    public JSONObject getOrderInfo(String tenancyId, String billNum) throws Exception {
        StringBuffer sql = new StringBuffer();
        sql.append(" select store_id,fictitious_table,order_num,order_source from pos_bill where bill_num = '"+ billNum +"' limit 1");
        List<JSONObject> list = this.query4Json(tenancyId, sql.toString());
        if (Tools.isNullOrEmpty(list) || list.size() < 1)
            return null;
        return list.get(0);
    }

    @Override
    public void updateOrderSource(String tenancyId, String billNum, String orderNum) throws Exception {
        StringBuffer sql = new StringBuffer();
        sql.append(" update pos_bill set order_source = '" + SysDictionary.CHANEL_WSP17 + "',order_num = '"+ orderNum +"' where bill_num = '" + billNum + "'");
        this.execute(tenancyId, sql.toString());
    }

    @Override
    public JSONObject getWlifeBill(String tenancyId, String out_order_id) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" select store_id,bill_num,fictitious_table,order_num,bill_property from pos_bill where tenancy_id = '" + tenancyId + "' and order_num = '"+ out_order_id +"' limit 1 ");
        List<JSONObject> list = this.query4Json(tenancyId, sql.toString());
        if (Tools.isNullOrEmpty(list))
            return null;
        return list.get(0);
    }

    @Override
    public String getBillPayNames(String tenancyId, String billNum) throws Exception {
        StringBuilder sql = new StringBuilder("SELECT string_agg(name ||'支付',',') posmark FROM pos_bill_payment WHERE bill_num='");
        sql.append(billNum).append("'")
                .append(" and tenancy_id='").append(tenancyId).append("'");
        String payNames = this.getString(tenancyId, sql.toString());
        return payNames;
    }

    @Override
    public Double getBillItemAmountByDis(String tenancyId, String billNum, String isDiscount) throws Exception {
        StringBuilder sql = new StringBuilder("SELECT COALESCE(sum(pbi.item_price),0) discountAmountPay FROM pos_bill_item pbi");
        sql.append(" LEFT JOIN hq_item_info hii ON pbi.item_id = hii.id")
                .append(" WHERE pbi.bill_num='").append(billNum).append("'AND hii.is_discount='").append(isDiscount)
                .append("' AND pbi.tenancy_id='").append(tenancyId).append("'")
                .append(" AND pbi.item_property in ('SINGLE','SETMEAL')")
                .append(" and (pbi.item_remark is null or (pbi.item_remark <> 'TC01' and pbi.item_remark  <> 'CJ05'))");
        Double val = this.getDouble(tenancyId, sql.toString());
        return val;
    }
}
