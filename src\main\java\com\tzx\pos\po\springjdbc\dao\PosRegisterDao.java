package com.tzx.pos.po.springjdbc.dao;

import java.util.List;

import net.sf.json.JSONObject;

import com.tzx.base.entity.PosOptState;
import com.tzx.pos.base.dao.BaseDao;

public interface PosRegisterDao extends BaseDao
{
	String	NAME	= "com.tzx.pos.po.springjdbc.dao.imp.PosRegisterDaoImp";

	/** 查询单号生成规则
	 * @param tenancyId
	 * @param billCode
	 * @return
	 * @throws Exception
	 */
	public JSONObject getSysBillCodeRule(String tenancyId, String billCode) throws Exception;

	/**修改单号最大值
	 * @param tenancyId
	 * @param storeId
	 * @param billCode
	 * @param prefix
	 * @param currentValue
	 * @param step
	 * @param updatetime
	 * @throws Exception
	 */
	public void updateSysCodeValues(String tenancyId, int storeId, String billCode, String prefix, Integer currentValue) throws Exception;

	/** 插入人员操作记录
	 * @param tenancyId
	 * @param storeId
	 * @param optStateList
	 * @throws Exception
	 */
	public void batchInsertPosOptState(String tenancyId, int storeId, List<PosOptState> optStateList) throws Exception;

	/**获取日始记录数量
	 * @param tenancyId
	 * @param storeId
	 * @throws Exception
	 */
	public int getPosOptStateForDayBegainCount(String tenancyId, int storeId )throws Exception;
}
