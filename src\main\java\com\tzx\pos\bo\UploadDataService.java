package com.tzx.pos.bo;

import java.util.Date;
import java.util.List;

import com.tzx.framework.common.entity.Data;

/**
 * 门店数据上传
 * 
 * <AUTHOR>
 *
 */
public interface UploadDataService
{
	String	NAME	= "com.tzx.pos.bo.imp.UploadDataServiceImp";

	static final int	UPLOAD_DATA_SIZE		= 100000; //根据新的方案将原来的100改成10W

	static final String	UPLOAD_DATA_WAY_HTTP	= "HTTP";

	static final String	UPLOAD_DATA_WAY_MQ		= "MQ";
	
	static final String	MODE_AUTO_UPLOAD_DATA	= "1";

	static final String	MODE_UPLOAD_DATA		= "2";
	
	/**
	 * 自动上传
	 * @param tenancyId
	 * @param storeId
	 * @param tableNames
	 * @throws Exception
	 */
	public void AutoUploadDataByMQ(String tenancyId, int storeId, String[] tableNames) throws Exception;

	/**
	 * 手动上传
	 * @param tenancyId
	 * @param storeId
	 * @param tableNames
	 * @param reportDate
	 * @param reData
	 * @throws Exception
	 */
	public Data UploadDataByMQ(String tenancyId, int storeId, String[] tableNames, Date reportDate) throws Exception;

	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param tableNames
	 * @param reportDate
	 * @param reData
	 * @param uploadMode 1:自动上传;2:手动上传;
	 * @throws Exception
	 */
	public Data UploadDataByHttp(String tenancyId, int storeId, String[] tableNames, Date reportDate,String uploadMode) throws Exception;
	
	/**通过http上传账单数据
	 * @param tenancyId
	 * @param storeId
	 * @param tableNames
	 * @param reportDate
	 * @param reData
	 * @throws Exception
	 */
	public Data UploadDataForBillByHttp(String tenancyId, int storeId, List<String> tableNames, Date reportDate,String isOpenBill,String uploadMode) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param tableNames
	 * @param reportDate
	 * @param reData
	 * @throws Exception
	 */
	public Data UploadDataForOtherByHttp(String tenancyId, int storeId, List<String> tableNames, Date reportDate,String uploadMode) throws Exception;

	/** 上传订单信息
*/
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param tableNames
	 * @param reportDate
	 * @param reData
	 * @param uploadMode 1:自动上传;2:手动上传;
	 * @throws Exception
	 */
	public Data UploadDataForOrder(String tenancyId, int storeId, List<String> tableNames, Date reportDate,String uploadMode) throws Exception;

	/**手动上传数据
	 * @param tenancyId
	 * @param storeId
	 * @param reportDate
	 * @throws Exception
	 */
	public Data UploadData(Data param) throws Exception;
}
