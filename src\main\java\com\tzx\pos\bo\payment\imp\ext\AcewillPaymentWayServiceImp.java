package com.tzx.pos.bo.payment.imp.ext;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.tzx.base.entity.PosBillMember;
import com.tzx.base.entity.PosBillPayment;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.CommonUtil;
import com.tzx.framework.common.util.DoubleHelper;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.Tools;
import com.tzx.member.acewill.bo.AcewillCustomerService;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.JsonUtil;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.bo.acewill.AcewillPaymentService;
import com.tzx.pos.bo.payment.PaymentWayService;
import com.tzx.pos.bo.payment.imp.PaymentWayServiceImp;
import com.tzx.pos.po.springjdbc.dao.PosBillMemberDao;

import net.sf.json.JSONObject;

@Service("com.tzx.pos.bo.payment.imp.ext.AcewillPaymentWayServiceImp")
public class AcewillPaymentWayServiceImp extends PaymentWayServiceImp implements PaymentWayService
{

	@Resource(name = AcewillCustomerService.NAME)
	private AcewillCustomerService	customerService;

	@Resource(name = PosBillMemberDao.NAME)
	private PosBillMemberDao			memberDao;
	
	@Override
	public void payment(String tenantId, int storeId, String billNum, String batchNum, Date reportDate, int shiftId, String posNum, String optNum, String tableCode, JSONObject payment, Timestamp currentTime, String isOnlinePay) throws Exception
	{
		// TODO Auto-generated method stub
		if ("1".equals(isOnlinePay))
		{
			double amount = payment.optDouble("amount");
			double count = payment.optDouble("count");
			double currencyAmount = payment.optDouble("currency_amount");
			String number = payment.optString("number");
			String phone = payment.optString("phone");
			String billCode = payment.optString("bill_code");

			int jzid = payment.optInt("jzid");
			String paymentClass = payment.optString("payment_class");
			String paymentName = payment.optString("payment_name");
			String nameEnglish = payment.optString("payment_english_name");
			double rate = payment.optDouble("rate");
			String remark = payment.optString("remark");
			String isYsk = payment.optString("is_ysk");
			if (Tools.isNullOrEmpty(isYsk))
			{
				isYsk = "N";
			}

			String mobil = ParamUtil.getStringValueByObject(payment, "mobil");
			String customerCode = ParamUtil.getStringValueByObject(payment, "customer_code");
			String customerName = ParamUtil.getStringValueByObject(payment, "customer_name");
			String cardCode = ParamUtil.getStringValueByObject(payment, "card_code");
			Double customerCredit = ParamUtil.getDoubleValueByObject(payment, "customer_credit");
			Double mainBalance = ParamUtil.getDoubleValueByObject(payment, "main_balance");
			Double rewardBalance = ParamUtil.getDoubleValueByObject(payment, "reward_balance");

			if (Tools.isNullOrEmpty(mobil))
			{
				mobil = phone;
			}
			if (Tools.isNullOrEmpty(cardCode))
			{
				cardCode = number;
			}
			if (Tools.isNullOrEmpty(customerCode))
			{
				customerCode = remark;
			}

			memberDao.deletePosBillMember(tenantId, storeId, billNum, SysDictionary.BILL_MEMBERCARD_CZXF03);

			PosBillMember billMember = new PosBillMember();
			billMember.setBill_num(billNum);
			billMember.setReport_date(reportDate);
			billMember.setType(SysDictionary.BILL_MEMBERCARD_CZXF03);
			billMember.setCard_code(cardCode);
			billMember.setMobil(mobil);
			billMember.setCustomer_code(customerCode);
			billMember.setCustomer_name(customerName);
			billMember.setAmount(currencyAmount);
			billMember.setCredit(0d);
			billMember.setConsume_after_credit(customerCredit);
			billMember.setConsume_before_credit(customerCredit);
			billMember.setConsume_after_main_balance(mainBalance);
			billMember.setConsume_before_main_balance(mainBalance);
			billMember.setConsume_after_reward_balance(rewardBalance);
			billMember.setConsume_before_reward_balance(rewardBalance);
			billMember.setBill_code(billCode);
			billMember.setRequest_state(SysDictionary.REQUEST_STATUS_COMPLETE);
			billMember.setLast_updatetime(currentTime);
			memberDao.insertPosBillMember(tenantId, storeId, billMember);

			paymentDao.insertPosBillPayment(tenantId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, nameEnglish, rate, amount, count, currencyAmount, number, phone, isYsk, null, billCode, remark, currentTime,
					SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);

//			paymentDao.insertPosBillPaymentLog(tenantId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, nameEnglish, rate, amount, count, currencyAmount, number, phone, isYsk, null, billCode, remark, currentTime,
//					SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);
		}
		else
		{
			throw SystemException.getInstance(PosErrorCode.POS_TYPE_NOT_EXISTS_ERROR);
		}
	}

	@Override
	public void paymentDebit(String tenancyId, int storeId, String billNum, String orderNum, String chanel, Double paymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData) throws Exception
	{
		if (SysDictionary.PAYMENT_STATE_PAY_PRESERVE.equals(payment.optString("payment_state")))
		{
			// 付款记录暂存,发起支付
			String paymentState = SysDictionary.PAYMENT_STATE_PAY;
			Date reportDate = ParamUtil.getDateValueByObject(payment, "report_date");
			String outTradeNo = ParamUtil.getStringValueByObject(payment, "out_trade_no");
			Data previewData = this.requestDealPreview(tenancyId, storeId, billNum, outTradeNo, chanel, paymentAmount);
			if (Constant.CODE_SUCCESS == previewData.getCode())
			{
				JSONObject responseJson = JSONObject.fromObject(previewData.getData().get(0));
				String verifySms = ParamUtil.getStringValueByObject(responseJson, "verify_sms");
				String verifyPassword = ParamUtil.getStringValueByObject(responseJson, "verify_password");
				// 判断是否需要验证密码,如果不需要,请求交易提交
				if ("0".equals(verifySms) && "0".equals(verifyPassword))
				{
					Data commitData = this.requestDealCommit(tenancyId, storeId, billNum, outTradeNo, reportDate, paymentAmount);
					if (Constant.CODE_SUCCESS == commitData.getCode())
					{
						paymentState = SysDictionary.PAYMENT_STATE_PAY_COMPLETE;
					}
				}
			}
			else if (Constant.CODE_CONN_EXCEPTION != previewData.getCode())
			{
				paymentState = SysDictionary.PAYMENT_STATE_PAY_FAILURE;
			}

			payment.put("payment_state", paymentState);

			resultJson.put("payment_state", paymentState);

			List<JSONObject> resultList = new ArrayList<JSONObject>();
			resultList.add(resultJson);
			
			resultData.setData(resultList);
		}
		else
		{
			// TODO Auto-generated method stub
			payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY);
			resultData.setCode(Constant.CODE_CONN_EXCEPTION);
			resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
		}
	}
	
	private Data requestDealPreview(String tenancyId, int storeId, String billNum, String outTradeNo, String chanel, Double billPaymentAmount) throws Exception
	{
		List<JSONObject> paymentList = paymentDao.getPosBillPaymentByBillnum(tenancyId, storeId, billNum);

		String paymentState = SysDictionary.PAYMENT_STATE_NOTPAY;
		if (null != paymentList)
		{
			for (JSONObject paymentJson : paymentList)
			{
				String paymentClass = ParamUtil.getStringValueByObject(paymentJson, "payment_class");
				if ((SysDictionary.PAYMENT_CLASS_WLIFE_BALANCE.equals(paymentClass) || SysDictionary.PAYMENT_CLASS_WLIFE_CREDIT.equals(paymentClass) || SysDictionary.PAYMENT_CLASS_WLIFE_COUPON.equals(paymentClass)) && null != outTradeNo && outTradeNo.equals(paymentJson.optString("out_trade_no")))
				{
					paymentState = ParamUtil.getStringValueByObject(paymentJson, "payment_state");
				}
			}
		}

		if (!SysDictionary.PAYMENT_STATE_PAY_PRESERVE.equals(paymentState))
		{
			JSONObject resultJson = new JSONObject();
			resultJson.put("payment_state", paymentState);

			List<JSONObject> resultList = new ArrayList<JSONObject>();
			resultList.add(resultJson);

			Data relustData = Data.get(Constant.CODE_AUTH_FAILURE);
			relustData.setData(resultList);
			return relustData;
		}

		AcewillPaymentService paymentService = (AcewillPaymentService) SpringConext.getBean(AcewillPaymentService.NAME);

		JSONObject requestJson = paymentService.getPreviewDealParamJson(tenancyId, storeId, billNum, outTradeNo, chanel, billPaymentAmount, paymentList);
		
		List<JSONObject> paramList = new ArrayList<JSONObject>();
		paramList.add(requestJson);
		
		Data paramData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
		paramData.setData(paramList);
		Data relustData = customerService.previewAcewillCustomerDeal(paramData);
		
		if(Constant.CODE_SUCCESS ==relustData.getCode())
		{
			// 成功返回付款信息
			JSONObject resultJson = JSONObject.fromObject(relustData.getData().get(0));
			String billCode = ParamUtil.getStringValueByObject(resultJson, "tcid");
			String verifySms = ParamUtil.getStringValueByObject(resultJson, "verify_sms");
			String verifyPassword = ParamUtil.getStringValueByObject(resultJson, "verify_password");
			
			List<PosBillPayment> billPaymentList = new ArrayList<PosBillPayment>(); 
			for(JSONObject paymentJson:paymentList)
			{
				String paymentClass = ParamUtil.getStringValueByObject(paymentJson, "payment_class");
				if ((SysDictionary.PAYMENT_CLASS_WLIFE_BALANCE.equals(paymentClass) || SysDictionary.PAYMENT_CLASS_WLIFE_CREDIT.equals(paymentClass) || SysDictionary.PAYMENT_CLASS_WLIFE_COUPON.equals(paymentClass)) && null != outTradeNo && outTradeNo.equals(paymentJson.optString("out_trade_no")))
				{
					PosBillPayment paymentBean = JsonUtil.jsonToBean(paymentJson, PosBillPayment.class);
					paymentBean.setBill_code(billCode);
					paymentBean.setParamCach("verify_sms", verifySms);
					paymentBean.setParamCach("verify_password", verifyPassword);
					paymentBean.setPayment_state(SysDictionary.PAYMENT_STATE_PAY);
					billPaymentList.add(paymentBean);
				}
			}
			paymentDao.updatePosBillPaymentForPaymentState(tenancyId, storeId, billNum, billPaymentList);
		}
		else if (Constant.CODE_CONN_EXCEPTION != relustData.getCode())
		{
			paymentDao.updatePosBillPaymentForPaymentState(tenancyId, storeId, billNum, outTradeNo, SysDictionary.PAYMENT_STATE_PAY_FAILURE, null);
		}
		return relustData;
	}
	
	private Data requestDealCommit(String tenancyId, int storeId, String billNum, String outTradeNo, Date reportDate, Double billPaymentAmount) throws Exception
	{
		JSONObject requestJson = new JSONObject();
		requestJson.put("out_trade_no", outTradeNo);
		requestJson.put("third_bill_code", billNum);
		requestJson.put("verify_code", "");

		List<JSONObject> paramList = new ArrayList<JSONObject>();
		paramList.add(requestJson);
		
		Data paramData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
		paramData.setData(paramList);
		
		Data responseData = customerService.commitAcewillCustomerDeal(paramData);

		if (Constant.CODE_SUCCESS == responseData.getCode())
		{
			JSONObject responseJson = JSONObject.fromObject(responseData.getData().get(0));

			AcewillPaymentService paymentService = (AcewillPaymentService) SpringConext.getBean(AcewillPaymentService.NAME);
			paymentService.requestDealCommitSuccess(tenancyId, storeId, reportDate, billNum, outTradeNo, billPaymentAmount, responseJson);
		}
		return responseData;
	}

	@Override
	public void findPaymentDebit(String tenancyId, int storeId, String billNum, String orderNum, String chanel, Double paymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData) throws Exception
	{
		// TODO Auto-generated method stub
//		String thirdBillCode = null;
//		if ((SysDictionary.CHANEL_MD01.equals(chanel) == false) && Tools.hv(orderNum))
//		{
//			thirdBillCode = orderNum;
//		}
//		else
//		{
//			thirdBillCode = billNum;
//		}
		JSONObject paramJson = new JSONObject();
		paramJson.put("third_bill_code", billNum);
		paramJson.put("bill_code", payment.optString("bill_code"));

		JSONObject resultDealJson = customerService.queryAcewillCustomerDeal(tenancyId, storeId, paramJson);
		String paymentState = SysDictionary.PAYMENT_STATE_PAY;
		if (null != resultDealJson && resultDealJson.isEmpty() == false)
		{
			if (SysDictionary.CUSTOMER_OPERATE_STATE_SUCCESS.equals(resultDealJson.optString("operate_state")))
			{
				paymentState = SysDictionary.PAYMENT_STATE_PAY_COMPLETE;
				// 修改付款状态
				Double currencyPayAmount = ParamUtil.getDoubleValueByObject(payment, "currency_amount");
				Double storedSalePay = ParamUtil.getDoubleValueByObject(payment, "currency_amount");
				if(resultDealJson.containsKey("stored_sale_pay"))
				{
					storedSalePay = ParamUtil.getDoubleValueByObject(resultDealJson, "stored_sale_pay");
				}
				Double tenancyAssume = DoubleHelper.sub(currencyPayAmount, storedSalePay, 4);
				
				JSONObject updatePaymentJson = new JSONObject();
				updatePaymentJson.put("coupon_buy_price", storedSalePay);
				updatePaymentJson.put("due", storedSalePay);
				updatePaymentJson.put("tenancy_assume", tenancyAssume);
				updatePaymentJson.put("payment_state", paymentState);
				updatePaymentJson.put("id", payment.optInt("id"));
				paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", updatePaymentJson);
			}
			
			resultData.setCode(Constant.CODE_SUCCESS);
		}
		else
		{
			paymentState = SysDictionary.PAYMENT_STATE_PAY_FAILURE;
			// 修改付款状态
			JSONObject updatePaymentJson = new JSONObject();
			updatePaymentJson.put("payment_state", paymentState);
			updatePaymentJson.put("id", payment.optInt("id"));
			paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", updatePaymentJson);
			
			resultData.setCode(PosErrorCode.BILL_PAYMENT_ERROP.getNumber());
			resultData.setMsg(PosErrorCode.BILL_PAYMENT_ERROP.getMessage().replace("{0}", "查询订单失败"));
		}
		payment.put("payment_state", paymentState);
	}

	@Override
	public void paymentRefund(String tenancyId, int storeId, String billNum, String oldBillNum, String orderNum, String chanel, Double billPaymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData, String operType) throws Exception
	{
		// TODO Auto-generated method stub
//		String thirdBillCode = null;
//		if ((SysDictionary.CHANEL_MD01.equals(chanel) == false) && Tools.hv(orderNum))
//		{
//			thirdBillCode = orderNum;
//		}
//		else
//		{
//			thirdBillCode = oldBillNum;
//		}

		String payBillCode = ParamUtil.getStringValueByObject(payment, "bill_code");
		// 查询交易撤销状态
		JSONObject qureyParamJson = new JSONObject();
		qureyParamJson.put("third_bill_code", oldBillNum);
		qureyParamJson.put("batch_num", payment.optString("batch_num"));
		qureyParamJson.put("bill_code", payBillCode);

		JSONObject queryResultJson = customerService.queryCancelAcewillCustomerDeal(tenancyId, storeId, qureyParamJson);
		String paymentState = SysDictionary.PAYMENT_STATE_REFUNDING;
		String billCode = null;
		// 请求退款接口
		if (Tools.hv(queryResultJson))
		{
			if (SysDictionary.CUSTOMER_OPERATE_STATE_SUCCESS.equals(queryResultJson.optString("operate_state")))
			{
				paymentState = SysDictionary.PAYMENT_STATE_REFUND_COMPLETE;
				billCode = queryResultJson.optString("bill_code");
			}
		}

		if (SysDictionary.PAYMENT_STATE_REFUND_COMPLETE.equals(paymentState) == false)
		{
			JSONObject cancelParamJson = new JSONObject();
			cancelParamJson.put("third_bill_code", oldBillNum);
			cancelParamJson.put("bill_code", payBillCode);
			cancelParamJson.put("batch_num", payment.optString("batch_num"));
			
			cancelParamJson.put("business_date", payment.optString("report_date"));
			cancelParamJson.put("shift_id", payment.optString("shift_id"));
			cancelParamJson.put("operator_id", payment.optString("cashier_num"));
			cancelParamJson.put("pos_num", payment.optString("pos_num"));
			cancelParamJson.put("chanel", chanel);
			cancelParamJson.put("operat_type", SysDictionary.OPERAT_TYPE_FXF);
			cancelParamJson.put("service_type", SysDictionary.SERVICE_TYPE_CONSUME);
			cancelParamJson.put("card_code", payment.optString("number"));
			cancelParamJson.put("trade_amount", payment.optDouble("currency_amount"));
			cancelParamJson.put("trade_credit", 0d);
			cancelParamJson.put("total_amount", billPaymentAmount);

//			List<JSONObject> cancelParamList = new ArrayList<JSONObject>();
//			cancelParamList.add(cancelParamJson);
//
//			Data cancelParamData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
//			cancelParamData.setData(cancelParamList);

			Data cancelResultData = customerService.cancelAcewillCustomerDeal(tenancyId, storeId, cancelParamJson,operType);

			if (Constant.CODE_SUCCESS == cancelResultData.getCode())
			{
				JSONObject cancelResultJson = JSONObject.fromObject(cancelResultData.getData().get(0));
				paymentState = SysDictionary.PAYMENT_STATE_REFUND_COMPLETE;
				billCode = cancelResultJson.optString("deal_id");
				
				List<JSONObject> memberList = memberDao.queryPosBillMember(tenancyId, storeId, billNum, null);
				for(JSONObject memberJson :memberList)
				{
					String billMemberType = ParamUtil.getStringValueByObject(memberJson, "type");
					String oldBillCode = ParamUtil.getStringValueByObject(memberJson, "bill_code");
					Integer memberId = ParamUtil.getIntegerValueByObject(memberJson, "id");
					if (CommonUtil.hv(payBillCode) && payBillCode.equals(oldBillCode)
							&& (SysDictionary.BILL_MEMBERCARD_JFZS06.equals(billMemberType)||SysDictionary.BILL_MEMBERCARD_CZXF03.equals(billMemberType)||SysDictionary.BILL_MEMBERCARD_YHJ04.equals(billMemberType)||SysDictionary.BILL_MEMBERCARD_JFDX05.equals(billMemberType)))
					{
						String customerName = ParamUtil.getStringValueByObject(memberJson, "customer_name");
						Double consumeBeforeCredit = ParamUtil.getDoubleValueByObject(memberJson, "consume_before_credit");
						Double consumeAfterCredit = ParamUtil.getDoubleValueByObject(memberJson, "consume_after_credit");
						Double credit = ParamUtil.getDoubleValueByObject(memberJson, "credit");
						Double amount = ParamUtil.getDoubleValueByObject(memberJson, "amount");
						
						memberDao.updatePosBillMember(tenancyId, storeId, billNum, billMemberType, memberId, amount, credit, billCode, SysDictionary.REQUEST_STATUS_COMPLETE, customerName, consumeBeforeCredit, consumeAfterCredit);
					}
				}
				//修改冲减支付状态
				paymentDao.updatePosBillPaymentForPaymentState(tenancyId, storeId, billNum, SysDictionary.PAYMENT_STATE_REFUND_COMPLETE, billCode);
			}
			else if (Constant.CODE_CONN_EXCEPTION == cancelResultData.getCode())
			{
				resultData.setCode(Constant.CODE_CONN_EXCEPTION);
				resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
			}
			else
			{
				//修改冲减支付状态
				paymentDao.updatePosBillPaymentForPaymentState(tenancyId, storeId, billNum, SysDictionary.PAYMENT_STATE_REFUND_FAILURE, null);

				resultData.setCode(cancelResultData.getCode());
				resultData.setMsg(cancelResultData.getMsg());
			}
		}

		if (SysDictionary.PAYMENT_STATE_REFUND_COMPLETE.equals(paymentState))
		{
			payment.put("bill_code", billCode);
			resultData.setCode(Constant.CODE_SUCCESS);
			resultData.setMsg("成功");
		}
	}

	@Override
	public void paymentCancel(String tenancyId, int storeId, String billNum, String orderNum, String chanel, Double billPaymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData, String operType) throws Exception
	{
		// TODO Auto-generated method stub
//		String thirdBillCode = null;
//		if ((SysDictionary.CHANEL_MD01.equals(chanel) == false) && Tools.hv(orderNum))
//		{
//			thirdBillCode = orderNum;
//		}
//		else
//		{
//			thirdBillCode = billNum;
//		}

//		String paymentState = SysDictionary.PAYMENT_STATE_REFUNDING;
//		String billCode = null;

		String paymentState = payment.optString("payment_state");

		if (SysDictionary.PAYMENT_STATE_PAY_PRESERVE.equals(paymentState) || SysDictionary.PAYMENT_STATE_PAY.equals(paymentState))
		{
			String cardCode = ParamUtil.getStringValueByObject(payment, "number");
			String lockTradeNo = ParamUtil.getStringValueByObject(payment, "out_trade_no");
			String outTradeNo = ParamUtil.getStringValueByObject(payment, "out_trade_no");
			Data cancelResultData = customerService.unlockAcewillCustomerDeal(tenancyId, storeId, payment.optString("cashier_num"), cardCode, lockTradeNo, outTradeNo);
			resultData.setCode(cancelResultData.getCode());
			resultData.setMsg(cancelResultData.getMsg());
			resultData.setData(cancelResultData.getData());
		}
		
		if (!SysDictionary.PAYMENT_STATE_PAY_PRESERVE.equals(paymentState))
		{
		JSONObject cancelParamJson = new JSONObject();
		cancelParamJson.put("third_bill_code", billNum);
		cancelParamJson.put("batch_num", payment.optString("batch_num"));
		cancelParamJson.put("bill_code", payment.optString("bill_code"));
		
		cancelParamJson.put("business_date", payment.optString("report_date"));
		cancelParamJson.put("shift_id", payment.optString("shift_id"));
		cancelParamJson.put("operator_id", payment.optString("cashier_num"));
		cancelParamJson.put("pos_num", payment.optString("pos_num"));
		cancelParamJson.put("chanel", chanel);
		cancelParamJson.put("operat_type", SysDictionary.OPERAT_TYPE_FXF);
		cancelParamJson.put("service_type", SysDictionary.SERVICE_TYPE_CONSUME);
		cancelParamJson.put("card_code", payment.optString("number"));
		cancelParamJson.put("trade_amount", payment.optDouble("currency_amount"));
		cancelParamJson.put("trade_credit", 0d);
		cancelParamJson.put("total_amount", billPaymentAmount);
		
//		List<JSONObject> cancelParamList = new ArrayList<JSONObject>();
//		cancelParamList.add(cancelParamJson);
//
//		Data cancelParamData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
//		cancelParamData.setData(cancelParamList);

		Data cancelResultData = customerService.cancelAcewillCustomerDeal(tenancyId, storeId, cancelParamJson,operType);
		resultData.setCode(cancelResultData.getCode());
		resultData.setMsg(cancelResultData.getMsg());
		resultData.setData(cancelResultData.getData());
		
//		if (Constant.CODE_SUCCESS == cancelResultData.getCode())
//		{
//			JSONObject cancelResultJson = JSONObject.fromObject(cancelResultData.getData().get(0));
//			paymentState = SysDictionary.PAYMENT_STATE_REFUND_COMPLETE;
//			billCode = cancelResultJson.optString("deal_id");
//		}
//		else
//		{
//			if (Constant.CODE_CONN_EXCEPTION == cancelResultData.getCode())
//			{
//				resultData.setCode(Constant.CODE_CONN_EXCEPTION);
//				resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
//			}
//			else
//			{
//				resultData.setCode(cancelResultData.getCode());
//				resultData.setMsg(cancelResultData.getMsg());
//			}
//		}
//
//		if (SysDictionary.PAYMENT_STATE_REFUND_COMPLETE.equals(paymentState))
//		{
//			payment.put("bill_code", billCode);
//			resultData.setCode(Constant.CODE_SUCCESS);
//			resultData.setMsg("成功");
//		}
			
//		String deleteMemberSql = "delete from pos_bill_member where bill_num = ? and (type = ? or type = ?) and store_id = ? and tenancy_id = ? ";
//		paymentDao.update(deleteMemberSql, new Object[]
//		{ billNum, SysDictionary.BILL_MEMBERCARD_JF01, SysDictionary.BILL_MEMBERCARD_JZ03, storeId, tenancyId });
	
//		memberDao.deletePosBillMember(tenancyId, storeId, billNum, new String[]
//		{ SysDictionary.BILL_MEMBERCARD_JFZS06, SysDictionary.BILL_MEMBERCARD_CZXF03, SysDictionary.BILL_MEMBERCARD_JFDX05, SysDictionary.BILL_MEMBERCARD_YHJ04 });
	
		}
	}
	
	@Override
	public void paymentRegain(String tenancyId, int storeId, String billNum, String orderNum, String chanel, Double billPaymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData, String operType) throws Exception
	{
		// TODO Auto-generated method stub
//		String thirdBillCode = null;
//		if ((SysDictionary.CHANEL_MD01.equals(chanel) == false) && Tools.hv(orderNum))
//		{
//			thirdBillCode = orderNum;
//		}
//		else
//		{
//			thirdBillCode = billNum;
//		}
		// 查询交易撤销状态
		JSONObject qureyParamJson = new JSONObject();
		qureyParamJson.put("third_bill_code", billNum);
		qureyParamJson.put("batch_num", payment.optString("batch_num"));
		qureyParamJson.put("bill_code", payment.optString("bill_code"));

		JSONObject queryResultJson = customerService.queryCancelAcewillCustomerDeal(tenancyId, storeId, qureyParamJson);
		String paymentState = SysDictionary.PAYMENT_STATE_REFUNDING;
		String billCode = null;
		// 请求退款接口
		if (Tools.hv(queryResultJson))
		{
			if (SysDictionary.CUSTOMER_OPERATE_STATE_SUCCESS.equals(queryResultJson.optString("operate_state")))
			{
				paymentState = SysDictionary.PAYMENT_STATE_REFUND_COMPLETE;
				billCode = queryResultJson.optString("bill_code");
			}
		}

		if (SysDictionary.PAYMENT_STATE_REFUND_COMPLETE.equals(paymentState) == false)
		{
			JSONObject cancelParamJson = new JSONObject();
			cancelParamJson.put("third_bill_code", billNum);
			cancelParamJson.put("batch_num", payment.optString("batch_num"));
			cancelParamJson.put("bill_code", payment.optString("bill_code"));
			cancelParamJson.put("business_date", payment.optString("report_date"));
			cancelParamJson.put("shift_id", payment.optString("shift_id"));
			cancelParamJson.put("operator_id", payment.optString("cashier_num"));
			cancelParamJson.put("pos_num", payment.optString("pos_num"));
			cancelParamJson.put("chanel", chanel);
			cancelParamJson.put("operat_type", SysDictionary.OPERAT_TYPE_FXF);
			cancelParamJson.put("service_type", SysDictionary.SERVICE_TYPE_CONSUME);
			cancelParamJson.put("card_code", payment.optString("number"));
			cancelParamJson.put("trade_amount", payment.optDouble("currency_amount"));
			cancelParamJson.put("trade_credit", 0d);
			cancelParamJson.put("total_amount", billPaymentAmount);
			
//			List<JSONObject> cancelParamList = new ArrayList<JSONObject>();
//			cancelParamList.add(cancelParamJson);
//
//			Data cancelParamData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
//			cancelParamData.setData(cancelParamList);

			Data cancelResultData = customerService.cancelAcewillCustomerDeal(tenancyId, storeId, cancelParamJson,operType);

			if (Constant.CODE_SUCCESS == cancelResultData.getCode())
			{
				JSONObject cancelResultJson = JSONObject.fromObject(cancelResultData.getData().get(0));
				paymentState = SysDictionary.PAYMENT_STATE_REFUND_COMPLETE;
				billCode = cancelResultJson.optString("deal_id");
			}
			else
			{
				if (Constant.CODE_CONN_EXCEPTION == cancelResultData.getCode())
				{
					resultData.setCode(Constant.CODE_CONN_EXCEPTION);
					resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
				}
				else
				{
					resultData.setCode(cancelResultData.getCode());
					resultData.setMsg(cancelResultData.getMsg());
				}
			}
		}

		if (SysDictionary.PAYMENT_STATE_REFUND_COMPLETE.equals(paymentState))
		{
			payment.put("bill_code", billCode);
			resultData.setCode(Constant.CODE_SUCCESS);
			resultData.setMsg("成功");
		}
	}

	@Override
	public void paymentDebitResult(String tenancyId, int storeId, String billNum, JSONObject paymentJson) throws Exception
	{
		// TODO 自动生成的方法存根
		JSONObject updatePaymentJson = new JSONObject();
		updatePaymentJson.put("number", paymentJson.optString("number"));
		updatePaymentJson.put("bill_code", paymentJson.optString("bill_code"));
		updatePaymentJson.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
		updatePaymentJson.put("id", paymentJson.optInt("id"));
		paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", updatePaymentJson);
	}
}
