package com.tzx.pos.bo.imp.print;

import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.util.Comet4jUtil;
import com.tzx.framework.common.util.Tools;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.bo.PosPrintModeService;
import com.tzx.pos.bo.imp.PosBaseServiceImp;
import com.tzx.pos.po.springjdbc.dao.PosDao;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 业务单据————且与桌位有关的单据
 * 结账单、补打结账单、预打单、点菜清单
 *
 * PC和触屏    ————> 走站点
 * 移动端设备  ————> 走区域
 *
 * 站点和区域在总部可以并存，既设置了站点又设置的为区域
 */

@Service("newServiceModePrintServiceImp")
public class NewServiceModePrintServiceImp extends PosBaseServiceImp implements PosPrintModeService
{
	@Resource(name = PosDao.NAME)
	private PosDao				posDao;
	private static final Logger	logger	= Logger.getLogger(NewServiceModePrintServiceImp.class);
	
	@Override
	public void posPrint(String tenancyId, int storeId, JSONObject para, String printMode) throws Exception
	{
		try
		{
            // 移动端设备只走区域打印
            String source = para.optString("source");
            // 机台号
            String pos_num = para.optString("pos_num");
            // 账单编号
            String billNum = para.optString("bill_num");

            boolean payWayIsOpen = posDao.payWayIsOpenCashBox(tenancyId, storeId, billNum, printMode);

            if (SysDictionary.SOURCE_MOVE.contains(source)||SysDictionary.SOURCE_ORDER_DEVICE.equals(source) || "".equals(source)) // 移动端设备只走区域打印
            {
                List<JSONObject> printerList = posDao.getAreaPrinter(tenancyId, storeId, printMode, billNum);
                if(printerList.size() > 0){
                    this.insertPrintTask(tenancyId, storeId, para, printMode, printerList, true, payWayIsOpen);

                } else {
                    logger.info("没有匹配的区域打印！");
                }

            } else {
                List<JSONObject> printerList = posDao.getDevicesPrinter(tenancyId, storeId, printMode, pos_num);
                if(printerList.size() > 0){
                    this.insertPrintTask(tenancyId, storeId, para, printMode, printerList, false, payWayIsOpen);

                } else {
                    printerList = posDao.getAreaPrinter(tenancyId, storeId, printMode, billNum);
                    if(printerList.size() > 0){
                        this.insertPrintTask(tenancyId, storeId, para, printMode, printerList, true, payWayIsOpen);

                    } else {
                        logger.info("没有匹配的区域打印！");
                    }
                }
            }
		}
		catch(Exception e)
		{
			logger.error("分单失败：其他原因导致分单失败" , e);
		}
	}

    /**
     *
     * @param tenancyId
     * @param storeId
     * @param para
     * @param printMode
     * @param printerList
     * @param isArea  是否是区域打印
     * @throws Exception
     */
	private void insertPrintTask(String tenancyId, int storeId, JSONObject para, String printMode, List<JSONObject> printerList,
        boolean isArea, boolean payWayIsOpen) throws Exception
    {
      devices:  for(int i = 0; i < printerList.size(); i++)
        {
            JSONObject printerJo = printerList.get(i);
            int printerId = printerJo.optInt("printer_id");
            if(printerId == 0)
            {
                continue;
            }
            para.put("printer_id", printerId);

            int isOpenCashbox = 0; // 是否开钱箱
            String isSite = printerJo.optString("is_site");
            if(payWayIsOpen && (1 == printerJo.optInt("is_open_cashbox")) && "1".equals(isSite))
            {
                isOpenCashbox = 1;
            }

            // IP地址
            String ipCom = printerJo.optString("ip_com");

            // 获取可以在该打印机打印的菜品
            if(SysDictionary.PRINT_CODE_1103.equals(printMode)){
            	
            	String is_print_all = printerJo.optString("is_print_all");
				para.put("is_print_all", is_print_all);
				
                List<JSONObject> printItemList = posDao.getPrintItemList(tenancyId, storeId, para, printMode, 0);
                if(printItemList.size() > 0){
                    String rwids = "";
                    for (JSONObject printItem : printItemList)
                    {
                        if(!Tools.isNullOrEmpty(rwids))
                        {
                            rwids += ",";
                        }
                        rwids += printItem.optString("rwid");
                    }
                    para.put("rwidsbk", rwids);
                }else{
                    logger.info("分单失败：【" + printMode + "】没有可以打印的菜品");
                    return;
                }
            }

            List<JSONObject> printParamList = new ArrayList<JSONObject>();
            try
            {
                printParamList = posDao.getPrintParam(tenancyId, storeId, printMode, printerJo);
            }
            catch (Exception e)
            {
                logger.error("分单失败：打印机【" + printerId + "】获取打印参数失败 " , e);
            }

            if(printParamList.size() > 0)
            {
                for(JSONObject printParam : printParamList)
                {
                    if(printParam.optString("param_pairs").isEmpty())
                    {
                        logger.info(" 分单失败：打印机【" + printerId + "】的打印模板【" + printMode + "】未设定打印参数");
                        continue;
                    }
                    JSONObject params = new JSONObject();
                    JSONObject paramPairs = JSONObject.fromObject(printParam.opt("param_pairs"));

                    // 获取打印参数
                    JSONArray rows = paramPairs.optJSONArray("rows");
                    Object value = null;
                    String paramName = null;
                    for (Object row : rows)
                    {
                        JSONObject rowJo = JSONObject.fromObject(row);

                        paramName = rowJo.optString("paramName");
                        value = para.get(paramName);
                        if(Tools.isNullOrEmpty(value))
                        {
                            value = rowJo.opt("value");
                        }
                        params.put(paramName, value);
                    }
                    if(SysDictionary.PRINT_CODE_1103.equals(printMode)){
                        params.put("rwid", para.optString("rwidsbk"));
                    }

                    params.put("is_open_cashbox", isOpenCashbox);

                    // 生成打印任务
                    try
                    {
                        posDao.insertPrintTask(tenancyId, storeId, printerJo, params, printMode);

                        if (SysDictionary.PRINT_CODE_1101.equals(printMode))
                        {
                            if (para.containsKey("bill_num"))
                            {
                                String uPrintCount = new String("update pos_bill set print_count =(coalesce(print_count,0)+1) where bill_num = ? and store_id = ? and tenancy_id=?");
                                posDao.update(uPrintCount, new Object[]
                                        { para.optString("bill_num"), storeId,tenancyId });
                            }
                        }
                    }
                    catch (Exception e)
                    {
                        logger.error("分单失败：打印机【" + printerId + "】生成打印任务失败" , e);
                    }

                    try
                    {
                        // 发出打印通知
                        Data cjData = new Data();
                        cjData.setType(Type.PRINT);
                        cjData.setOper(Oper.print);
                        JSONObject printJson = new JSONObject();
                        printJson.put("print_address", ipCom);
                        List<JSONObject> list = new ArrayList<>();
                        list.add(printJson);
                        cjData.setData(list);

                        Comet4jUtil.sendMessage2All(Comet4jUtil.POS_PRINTER, JSONObject.fromObject(cjData).toString());
                        logger.debug("打印分单成功：打印机【" + printerId + "】" + "，打印模板【" + printMode + "】");
//                        logger.info("打印分单成功：打印机【" + printerId + "】" + "，打印模板【" + printMode + "】");
                        if(!isArea){
                            break devices;
                        }
                    }
                    catch (Exception e)
                    {
                        logger.error("分单失败：打印机【" + printerId + "】服务器连接异常", e);
                    }
                }
            }
            else
            {
                logger.info("分单失败：打印机【" + printerId + "】未找到打印模板【" + printMode + "】");
            }
        }
    }

}
