package com.tzx.thirdpay.po.springjdbc.dao;

import com.tzx.base.entity.PosThirdPaymentRefundEntity;
import com.tzx.pos.base.dao.BaseDao;

/** 退款异常
 * <AUTHOR>
 *
 */
public interface ThirdPaymentRefundDao extends BaseDao
{
	String	NAME	= "com.tzx.thirdpay.po.springjdbc.dao.imp.ThirdPaymentRefundDaoImp";
	
	/**新增退款异常记录
	 * @param tenancyId
	 * @param storeId
	 * @param paymentRefund
	 * @throws Exception
	 */
	public int insertPosPaymentOrderRefund(String tenancyId, int storeId, PosThirdPaymentRefundEntity paymentRefund) throws Exception;
	
	/**重试修改退款异常记录
	 * @param tenancyId
	 * @param storeId
	 * @param paymentRefund
	 * @return
	 * @throws Exception
	 */
	public int updateThirdPayOrderRefundForRetry(String tenancyId, int storeId, PosThirdPaymentRefundEntity paymentRefund) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param outRequestNo
	 * @return
	 * @throws Exception
	 */
	public PosThirdPaymentRefundEntity getPosThirdPaymentRefund(String tenancyId, int storeId,String outRequestNo) throws Exception;
}
