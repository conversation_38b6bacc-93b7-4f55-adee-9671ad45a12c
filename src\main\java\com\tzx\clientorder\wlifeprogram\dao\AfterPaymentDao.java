package com.tzx.clientorder.wlifeprogram.dao;

import net.sf.json.JSONObject;

import java.util.List;

/**
 *
 */
public interface AfterPaymentDao extends WlifeCommonDao {
    String NAME = "com.tzx.clientorder.wlifeprogram.dao.impl.AfterPaymentDaoImpl";

    /**
     * 获取桌台状态
     * @param tenancyId
     * @param storeId
     * @param tableCode
     * @return
     * @throws Exception
     */
    String getTableState(String tenancyId, int storeId, String tableCode) throws Exception;

    /**
     * 通过订单号获取账单信息
     * @param tenancyId
     * @param storeId
     * @param orderNum
     * @return
     * @throws Exception
     */
    List<JSONObject> getBillInfoByOrderNum(String tenancyId, int storeId, String orderNum) throws Exception;

    /**
     * 查询菜品规格
     * @param microLifeOrder
     * @return
     * @throws Exception
     */
    List<JSONObject> getUnitNameList(String tenancyId, int storeId,List<String> duids) throws Exception;

    /**
     * 查询微生活会员信息
     * @param tenancyId
     * @param tableCode
     * @param storeId
     * @return
     */
    JSONObject getWLifeMember(String tenancyId, String tableCode, int storeId) throws Exception;

    /**
     * 查询订单的信息
     * @param tenancyId
     * @param tableCode
     * @param storeId
     * @return
     * @throws Exception
     */
    JSONObject getBillInfo(String tenancyId, String tableCode, int storeId) throws Exception;

    /**
     * 查询套餐菜品信息
     * @param tenancyId
     * @param billNum
     * @return
     * @throws Exception
     */
    List<JSONObject> getSetmeal(String tenancyId, String billNum,String itemProperty) throws Exception;

    /**
     * 查询套餐的主菜或套餐必选菜的菜品
     * @param tenancyId
     * @param billNum
     * @param type 套餐主菜或是套餐必选菜查询
     * @return
     * @throws Exception
     */
    List<JSONObject> getSetmealDish(String tenancyId, String billNum, String type, int stemealCount, String setmealId, String setmeal_rwid) throws Exception;

    /**
     * 根据pos_bill_item表的菜品id，查询做法
     * @param tenancyId
     * @param id
     * @return
     * @throws Exception
     */
    List<JSONObject> getCooks(String tenancyId, int id) throws Exception;

    /**
     * 查询订单的信息
     * @param tenancyId
     * @param tableCode
     * @param storeId
     * @return
     * @throws Exception
     */
    JSONObject getBillOrder(String tenancyId, String tableCode, int storeId) throws Exception;

    /**
     * 获取菜品做法
     * @param methodId
     * @return
     */
    JSONObject getCookMethodObj(Integer methodId) throws Exception;

    String getBillNumByOrderId(String orderId)throws Exception;

    void updatePosBillSource(String billNum,String sourceVal,boolean isOrdering) throws Exception;
    void updatePosBillOrderNum(String orderNum,String billNum) throws Exception;

    JSONObject getTableStatus(String tenantId,Integer storeId,String tableNo) throws Exception;

	void lockTableStatus(Integer storeId, String tableNo, String lockPosNum, String lockOptNum, String optName) throws Exception;

    JSONObject getBillMemberInfo(String billNum,String type,String cardCode) throws Exception;

    /** 查询套餐明细
     * @param tenantId
     * @param itemIdList
     * @return
     * @throws Exception
     */
    public List<JSONObject> getItemComboDetails(String tenantId, List<Integer> itemIdList) throws Exception;
    
	/** 删除账单会员操作记录
	 * @param tenantId
	 * @param storeId
	 * @param billno
	 * @param type
	 * @throws Exception
	 */
	public void deletePosBillMember(String tenantId, int storeId, String billno, String type)throws Exception;
	
	/**
	 * @param tenantId
	 * @param storeId
	 * @param billno
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getBillCouponItemListByBillNum(String tenantId, int storeId, String billno) throws Exception;
}
