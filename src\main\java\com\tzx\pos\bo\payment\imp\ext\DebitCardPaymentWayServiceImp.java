package com.tzx.pos.bo.payment.imp.ext;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.tzx.base.entity.PosBillPayment;
import com.tzx.base.entity.PosThirdPaymentRefundEntity;
import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.util.PropertiesLoader;
import com.tzx.framework.common.util.Tools;
import com.tzx.member.crm.bo.CustomerService;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.bo.payment.PaymentWayService;
import com.tzx.pos.bo.payment.imp.PaymentWayServiceImp;
import com.tzx.thirdpay.bo.ThirdPaymentRefundService;

import net.sf.json.JSONObject;

/**
 * 签账卡
 *
 */
@Service("com.tzx.pos.bo.payment.imp.ext.DebitCardPaymentWayServiceImp")
public class DebitCardPaymentWayServiceImp extends PaymentWayServiceImp implements PaymentWayService
{
	@Resource(name = CustomerService.NAME)
	private CustomerService	customerService;
	
	@Resource(name = ThirdPaymentRefundService.NAME)
	private ThirdPaymentRefundService		thirdPaymentRefundService;
	
//	@Resource(name = PosPrintNewService.NAME)
//	private PosPrintNewService posPrintNewService;

	@Override
	public void payment(String tenantId, int storeId, String billNum,String batchNum, Date reportDate, int shiftId, String posNum, String optNum, String tableCode, JSONObject payment, Timestamp currentTime,String isOnlinePay) throws Exception
	{
//		String chanel = payment.optString("chanel");

		double amount = payment.optDouble("amount");
		double count = payment.optDouble("count");
		double currencyAmount = payment.optDouble("currency_amount");
		String number = payment.optString("number");
		String phone = payment.optString("phone");
		String billCode = payment.optString("bill_code");

		int jzid = payment.optInt("jzid");
		String paymentClass = payment.optString("payment_class");
		String paymentName = payment.optString("payment_name");
		String nameEnglish = payment.optString("payment_english_name");
		double rate = payment.optDouble("rate");
		String remark = payment.optString("remark");
		String isYsk = payment.optString("is_ysk");
		if (Tools.isNullOrEmpty(isYsk))
		{
			isYsk = "N";
		}

		Double due = currencyAmount;
		Double tenancyAssume = 0d;
		if ("0".equals(payment.optString("if_income")))
		{
			due = 0d;
			tenancyAssume = currencyAmount;
		}
		
		PosBillPayment paymentEntity = new PosBillPayment(tenantId, storeId, reportDate, shiftId, posNum, optNum, billNum, batchNum, tableCode, paymentClass, jzid, paymentName, nameEnglish, amount, currencyAmount, Double.valueOf(count).intValue(), number, phone, currentTime, SysDictionary.PAYMENT_STATE_PAY);
		paymentEntity.setRate(rate);
		paymentEntity.setIs_ysk(isYsk);
		paymentEntity.setBill_code(billCode);
		paymentEntity.setRemark(remark);
		paymentEntity.setCoupon_buy_price(due);
		paymentEntity.setDue(due);
		paymentEntity.setTenancy_assume(tenancyAssume);
		if ("1".equalsIgnoreCase(isOnlinePay))
		{
			paymentEntity.setPayment_state(SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
		}
		else
		{
			payment.put("pay_state", SysDictionary.PAYMENT_STATE_NOTPAY);
			paymentEntity.setParam_cach(payment.toString());
		}
		paymentDao.insertPosBillPayment(tenantId, storeId, paymentEntity);
		
//		if ("1".equalsIgnoreCase(isOnlinePay)){
//			paymentDao.insertPosBillPayment(tenantId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, nameEnglish, rate, amount, count, currencyAmount, number, phone, isYsk, null, billCode, remark, currentTime,SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);
////			paymentDao.insertPosBillPaymentLog(tenantId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, nameEnglish, rate, amount, count, currencyAmount, number, phone, isYsk, null, billCode, remark, currentTime,SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);
//		}
//		else{
//			
//			payment.put("pay_state", SysDictionary.PAYMENT_STATE_NOTPAY);
//			paymentDao.insertPosBillPayment(tenantId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, nameEnglish, rate, amount, count, currencyAmount, number, phone, isYsk, null, billCode, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY,batchNum, payment.toString(), 0d);
//		}
	}

	@Override
	public void paymentDebit(String tenancyId, int storeId, String billNum,String orderNum,String chanel, Double paymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData) throws Exception{
		// TODO Auto-generated method stub
		String optNum = payment.optString("cashier_num");
		String optName = paymentDao.getEmpNameById(optNum, tenancyId, storeId);
		int payId = payment.optInt("id");
		
		JSONObject requestJson = new JSONObject();
		requestJson.put("card_num", payment.optString("number"));
		requestJson.put("last_operator", optName);
		requestJson.put("bill_num", billNum);
		requestJson.put("flow_code",UUID.randomUUID().toString().replaceAll("-", ""));
		requestJson.put("consumption_num", payment.getDouble("currency_amount"));
		requestJson.put("bill_money",  paymentAmount);
		List<JSONObject> requestList = new ArrayList<JSONObject>();
		requestList.add(requestJson);
		Data requestData = Data.get(tenancyId, storeId, 0);
		//Data requestData = Data.get("hdl", 412, 0);
		requestData.setType(Type. DEBIT_CARD);
		requestData.setOper(Oper.add);
		requestData.setData(requestList);

		Data responseData = Data.get(requestData);
		customerService.commonPost(JSONObject.fromObject(requestData).toString(), responseData);

		if (Constant.CODE_SUCCESS == responseData.getCode())
		{
			JSONObject responseJson = JSONObject.fromObject(responseData.getData().get(0));
			String billCode = responseJson.optString("bill_num");
			
			JSONObject paymentJson = new JSONObject();
			paymentJson.put("bill_code", billCode);
			paymentJson.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
			paymentJson.put("id", payId);
			paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", paymentJson);
			
			payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
			resultData.setCode(Constant.CODE_SUCCESS);
		}
		else if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
		{
			payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY);
			resultData.setCode(Constant.CODE_CONN_EXCEPTION);
			resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
		}
		else
		{
			paymentDao.deletePosBillPaymentByID(tenancyId, storeId, billNum, payId);

			Date reportDate = DateUtil.parseDate(payment.optString("report_date"));
			Integer shiftId = payment.optInt("shift_id");
			String posNum = payment.optString("pos_num");

			int jzid = payment.optInt("jzid");
			String number = payment.optString("number");
			String table_code = payment.optString("table_code");
			String paymentClass = payment.optString("type");
			String paymentName = payment.optString("name");
			String paymentEnglishName = payment.optString("name_english");
			double exchangeRate = payment.optDouble("rate");
			double pay_amount = payment.optDouble("amount");
			double pay_count = payment.optDouble("count");
			double currency_amount = payment.optDouble("currency_amount");
			String phone = payment.optString("phone");
			int customer_id = payment.optInt("customer_id");
			String remark = payment.optString("remark");
			String batch_num = payment.optString("batch_num");
			String is_ysk = Tools.hv(payment.optString("is_ysk")) ? payment.optString("is_ysk") : "N";
			paymentDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, table_code, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, customer_id,
					null, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_FAILURE, batch_num, payment.optString("param_cach"), 0d);

			payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_FAILURE);
			resultData.setCode(PosErrorCode.BILL_PAYMENT_ERROP.getNumber());
			resultData.setMsg(PosErrorCode.BILL_PAYMENT_ERROP.getMessage().replace("{0}", responseData.getMsg()));
		}
	}

	@Override
	public void findPaymentDebit(String tenancyId, int storeId, String billNum,String orderNum,String chanel,Double paymentAmount,JSONObject payment,Timestamp currentTime,JSONObject resultJson,Data resultData) throws Exception
	{
		// TODO Auto-generated method stub
		String optNum = payment.optString("cashier_num");
		String optName = paymentDao.getEmpNameById(optNum, tenancyId, storeId);

		JSONObject requestJson = new JSONObject();
		requestJson.put("card_num", payment.optString("number"));
		requestJson.put("last_operator", optName);
		requestJson.put("bill_num", billNum);
		requestJson.put("flow_code",UUID.randomUUID().toString().replaceAll("-", ""));
		requestJson.put("consumption_num", paymentAmount);

		List<JSONObject> requestList = new ArrayList<JSONObject>();
		requestList.add(requestJson);
		Data requestData = Data.get(tenancyId, storeId, 0);
		//Data requestData = Data.get("hdl", 412, 0);
		requestData.setType(Type.CUSTOMER_BILL);
		requestData.setOper(Oper.find);
		requestData.setData(requestList);

		Data responseData = Data.get(requestData);
		customerService.commonPost(JSONObject.fromObject(requestData).toString(), responseData);
		if (responseData.isSuccess())
		{
			JSONObject responseJson = JSONObject.fromObject(responseData.getData().get(0));
			String billCode = responseJson.optString("bill_num");
			resultJson.put("consumption_num", responseJson.optDouble("consumption_num"));
			payment.put("number", responseJson.optString("card_num"));
			payment.put("bill_code", billCode);
			payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
			
			//修改付款状态
			JSONObject updatePaymentJson = new JSONObject();
			updatePaymentJson.put("number", responseJson.optString("card_code"));
			updatePaymentJson.put("bill_code", billCode);
			updatePaymentJson.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
			updatePaymentJson.put("id", payment.optInt("id"));
			paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", updatePaymentJson);
			
			resultData.setCode(Constant.CODE_SUCCESS);
			
		}
		else if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
		{
			payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY);
			resultData.setCode(Constant.CODE_CONN_EXCEPTION);
			resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
		}
		else
		{
			payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_FAILURE);

			// 修改付款状态
			JSONObject updatePaymentJson = new JSONObject();
			updatePaymentJson.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_FAILURE);
			updatePaymentJson.put("id", payment.optInt("id"));
			paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", updatePaymentJson);

			resultData.setCode(PosErrorCode.BILL_PAYMENT_ERROP.getNumber());
			resultData.setMsg(PosErrorCode.BILL_PAYMENT_ERROP.getMessage().replace("{0}", responseData.getMsg()));
		}
	}

	@Override
	public void paymentRefund(String tenancyId, int storeId, String billNum,String oldBillNum,String orderNum,String chanel,Double billPaymentAmount,JSONObject payment,Timestamp currentTime,JSONObject resultJson,Data resultData,String operType) throws Exception{
		// TODO Auto-generated method stub
		String optNum = payment.optString("cashier_num");
		String optName = paymentDao.getEmpNameById(optNum, tenancyId, storeId);
		Double currencyAmount = Math.abs(payment.getDouble("amount"));

		JSONObject requestJson = new JSONObject();
		requestJson.put("card_num", payment.optString("number"));
		requestJson.put("last_operator", optName);
		requestJson.put("bill_num", oldBillNum);
		requestJson.put("flow_code", UUID.randomUUID().toString().replaceAll("-", ""));
		requestJson.put("consumption_num", currencyAmount);
		
		List<JSONObject> requestList = new ArrayList<JSONObject>();
		requestList.add(requestJson);

		//Data requestData = Data.get("hdl", 412, 0);
		Data requestData = Data.get(tenancyId, storeId, 0);
		requestData.setType(Type. DEBIT_CARD);
		requestData.setOper(Oper.update);
		requestData.setData(requestList);

		Data responseData = Data.get(requestData);
		customerService.commonPost(JSONObject.fromObject(requestData).toString(), responseData);

		if(Constant.CODE_SUCCESS == responseData.getCode())
		{
			resultData.setCode(Constant.CODE_SUCCESS);
			
			JSONObject paymentJson = new JSONObject();
			paymentJson.put("bill_code", payment.optString("bill_code"));
			paymentJson.put("payment_state", SysDictionary.PAYMENT_STATE_REFUND_COMPLETE);
			paymentJson.put("id", payment.optInt("id"));
			paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", paymentJson);
		}
		else
		{
			if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
			{
				resultData.setCode(Constant.CODE_CONN_EXCEPTION);
				resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
			}
			else
			{
				JSONObject paymentJson = new JSONObject();
				paymentJson.put("payment_state", SysDictionary.PAYMENT_STATE_REFUND_FAILURE);
				paymentJson.put("id", payment.optInt("id"));
				paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", paymentJson);

				resultData.setCode(responseData.getCode());
				resultData.setMsg(responseData.getMsg());
			}
			
			// 退款失败,记录退款异常
			Integer paymentId = ParamUtil.getIntegerValueByObject(payment, "jzid");
			String paymentClass = ParamUtil.getStringValueByObject(payment, "payment_class");
			String thirdBillCode = oldBillNum;

			String oldBillCode = ParamUtil.getStringValueByObject(payment, "bill_code");
			String posNum = ParamUtil.getStringValueByObject(payment, "pos_num");
			Integer shiftId = ParamUtil.getIntegerValueByObject(payment, "shift_id");
			Date reportDate = ParamUtil.getDateValueByObject(payment, "report_date");
			PosThirdPaymentRefundEntity paymentRefund = new PosThirdPaymentRefundEntity(tenancyId, storeId, reportDate, shiftId, posNum, optNum, chanel, SysDictionary.SERVICE_TYPE_CONSUME, oldBillNum, thirdBillCode, oldBillCode, paymentClass, paymentId, Math.abs(billPaymentAmount), currencyAmount,
					currentTime, currentTime, optNum, String.valueOf(resultData.getCode()), resultData.getMsg(), operType, SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL, SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL);
			paymentRefund.setExtra(requestJson.toString());

			thirdPaymentRefundService.thirdPaymentRefundFailure(tenancyId, storeId, paymentRefund);
		}
	}

	@Override
	public void paymentCancel(String tenancyId, int storeId, String billNum, String orderNum, String chanel, Double billPaymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData, String operType) throws Exception
	{
		// TODO Auto-generated method stub
		String optNum = payment.optString("cashier_num");
		String optName = paymentDao.getEmpNameById(optNum, tenancyId, storeId);
		Double currencyAmount = Math.abs(payment.getDouble("amount"));
		
		JSONObject requestJson = new JSONObject();
		requestJson.put("card_num", payment.optString("number"));
		requestJson.put("last_operator", optName);
		requestJson.put("bill_num", billNum);
		requestJson.put("flow_code",UUID.randomUUID().toString().replaceAll("-", ""));
		requestJson.put("consumption_num",  currencyAmount);
		
		List<JSONObject> requestList = new ArrayList<JSONObject>();
		requestList.add(requestJson);

		//Data requestData = Data.get("hdl", 412, 0);
		Data requestData = Data.get(tenancyId, storeId, 0);
		requestData.setType(Type.DEBIT_CARD);
		requestData.setOper(Oper.update);
		requestData.setData(requestList);

		Data responseData = Data.get(requestData);
		customerService.commonPost(JSONObject.fromObject(requestData).toString(), responseData);

		if(Constant.CODE_SUCCESS == responseData.getCode())
		{
			resultData.setCode(Constant.CODE_SUCCESS);
		}
		else
		{
			if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
			{
				resultData.setCode(Constant.CODE_CONN_EXCEPTION);
				resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
			}
			else
			{
				resultData.setCode(responseData.getCode());
				resultData.setMsg(responseData.getMsg());
			}
			
			// 退款失败,记录退款异常
			Integer paymentId = ParamUtil.getIntegerValueByObject(payment, "jzid");
			String paymentClass = ParamUtil.getStringValueByObject(payment, "payment_class");
			String thirdBillCode = billNum ;
			String oldBillCode = ParamUtil.getStringValueByObject(payment, "bill_code");
			String posNum = ParamUtil.getStringValueByObject(payment, "pos_num");
			Integer shiftId = ParamUtil.getIntegerValueByObject(payment, "shift_id");
			Date reportDate = ParamUtil.getDateValueByObject(payment, "report_date");
			
			PosThirdPaymentRefundEntity paymentRefund = new PosThirdPaymentRefundEntity(tenancyId, storeId, reportDate, shiftId, posNum, optNum, chanel, SysDictionary.SERVICE_TYPE_CONSUME, billNum, thirdBillCode, oldBillCode, paymentClass, paymentId, billPaymentAmount,
					currencyAmount, currentTime, currentTime, optNum, String.valueOf(resultData.getCode()), resultData.getMsg(), operType, SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL, SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL);
			paymentRefund.setExtra(requestJson.toString());

			thirdPaymentRefundService.thirdPaymentRefundFailure(tenancyId, storeId, paymentRefund);
		}
	}

	@Override
	public void paymentRegain(String tenancyId, int storeId, String billNum, String orderNum, String chanel, Double billPaymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData, String operType) throws Exception
	{
		// TODO Auto-generated method stub
		String optNum = payment.optString("cashier_num");
		String optName = paymentDao.getEmpNameById(optNum, tenancyId, storeId);
		Double currencyAmount = Math.abs(payment.getDouble("amount"));
		
		JSONObject requestJson = new JSONObject();
		requestJson.put("card_num", payment.optString("number"));
		requestJson.put("last_operator", optName);
		requestJson.put("bill_num", billNum);
		requestJson.put("flow_code",UUID.randomUUID().toString().replaceAll("-", ""));
		requestJson.put("consumption_num",  currencyAmount);
		
		List<JSONObject> requestList = new ArrayList<JSONObject>();
		requestList.add(requestJson);

		//Data requestData = Data.get("hdl", 412, 0);
		Data requestData = Data.get(tenancyId, storeId, 0);
		requestData.setType(Type. DEBIT_CARD);
		requestData.setOper(Oper.update);
		requestData.setData(requestList);

		Data responseData = Data.get(requestData);
		customerService.commonPost(JSONObject.fromObject(requestData).toString(), responseData);

		if(responseData.isSuccess())
		{
			resultData.setCode(Constant.CODE_SUCCESS);
		}
		else
		{
			if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
			{
				resultData.setCode(Constant.CODE_CONN_EXCEPTION);
				resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
			}
			else
			{
				resultData.setCode(responseData.getCode());
				resultData.setMsg(responseData.getMsg());
			}
			
			// 退款失败,记录退款异常
			Integer paymentId = ParamUtil.getIntegerValueByObject(payment, "jzid");
			String paymentClass = ParamUtil.getStringValueByObject(payment, "payment_class");
			String thirdBillCode = billNum;
			String oldBillCode = ParamUtil.getStringValueByObject(payment, "bill_code");
			String posNum = ParamUtil.getStringValueByObject(payment, "pos_num");
			Integer shiftId = ParamUtil.getIntegerValueByObject(payment, "shift_id");
			Date reportDate = ParamUtil.getDateValueByObject(payment, "report_date");

			PosThirdPaymentRefundEntity paymentRefund = new PosThirdPaymentRefundEntity(tenancyId, storeId, reportDate, shiftId, posNum, optNum, chanel, SysDictionary.SERVICE_TYPE_CONSUME, billNum, thirdBillCode, oldBillCode, paymentClass, paymentId, billPaymentAmount, currencyAmount, currentTime,
					currentTime, optNum, String.valueOf(resultData.getCode()), resultData.getMsg(), operType, SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL, SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL);
			paymentRefund.setExtra(requestJson.toString());

			thirdPaymentRefundService.thirdPaymentRefundFailure(tenancyId, storeId, paymentRefund);
		}
	}

	@Override
	public void paymentDebitResult(String tenancyId, int storeId, String billNum, JSONObject paymentJson) throws Exception
	{
		// TODO 自动生成的方法存根
		JSONObject updatePaymentJson = new JSONObject();
		updatePaymentJson.put("number", paymentJson.optString("number"));
		updatePaymentJson.put("bill_code", paymentJson.optString("bill_code"));
		updatePaymentJson.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
		updatePaymentJson.put("id", paymentJson.optInt("id"));
		paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", updatePaymentJson);
	}
}
