package com.tzx.pos.po.springjdbc.dao;

import java.util.List;

import net.sf.json.JSONObject;

import com.tzx.pos.base.dao.BaseDao;

public interface NocodeCouponsDao extends BaseDao
{
	String	NAME	= "com.tzx.pos.po.springjdbc.dao.imp.NocodeCouponsDaoImp";

	/**
	 * 查询优惠券类型
	 * @param tenancyId
	 * @param storeId
	 * @param chanel
	 * @param couponTypeIdList
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getCrmCouponsType(String tenancyId, Integer storeId, String chanel, List<String> couponTypeIdList) throws Exception;

	/**
	 * 获取优惠券支付信息
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getPosBillPaymentForCouponByBillNum(String tenancyId, Integer storeId, String billNum) throws Exception;
	
	/** 获取账单明细
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getPosBillItemForCouponByBillNum(String tenancyId, Integer storeId, String billNum,List<String> couponTypeIdList) throws Exception;
	
	/**
	 * 查询账单表
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> findBill(String tenancyId, int storeId, String billNum) throws Exception;

	/**
	 * 根据账单iD查询优惠劵付款记录
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param paymentId
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getPaymentCouponsByBillNum(String tenancyId, int storeId, String billNum) throws Exception;
}
