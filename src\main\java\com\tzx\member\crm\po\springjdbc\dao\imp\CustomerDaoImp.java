package com.tzx.member.crm.po.springjdbc.dao.imp;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import net.sf.json.JSONObject;

import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Repository;

import com.tzx.framework.common.util.Tools;
import com.tzx.member.common.entity.CrmCardPaymentListEntity;
import com.tzx.member.common.entity.CrmCardTradingListEntity;
import com.tzx.member.common.entity.CrmIncorporationArrearListEntity;
import com.tzx.member.common.entity.PosCustomerOperateListEntity;
import com.tzx.member.crm.po.springjdbc.dao.CustomerDao;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.dao.imp.BaseDaoImp;
import com.tzx.pos.bo.dto.PosThirdPaymentOrderEntity;

@Repository(CustomerDao.NAME)
public class CustomerDaoImp extends BaseDaoImp implements CustomerDao
{

	@Override
	public void insertCrmCardTradingList(String tenancyId, Integer storeId, Date businessDate, Integer shiftId, Integer operatorId, String operator, String chanel, Integer cardId, String cardCode, Integer cardClassId, String name, String mobil, Double deposit, Integer customerId, String billCode,
			String operatType, Double billMoney, String thirdBillCode, String billCodeOriginal, String batchNum, Integer activityId, Double mainTrading, Double rewardTrading, Double mainOriginal, Double rewardOriginal, Double revokedTrading, Timestamp lastUpdatetime, Timestamp storeUpdatetime,
			Timestamp operateTime, Double invoiceBalance, Double totalBalance, Double rewardBalance, Double mainBalance, String payType, Integer salesman, Double commissionSalerMoney, Double commissionStoreMoney, String isInvoice, String requestStatus, String paymentState, String rechargeState)
			throws Exception
	{
		// TODO Auto-generated method stub
		StringBuilder sql = new StringBuilder(
				"insert into crm_card_trading_list(tenancy_id,store_id,business_date,shift_id,operator_id,operator,chanel,card_id,card_code,card_class_id,name,mobil,deposit,customer_id,bill_code,operat_type,bill_money,third_bill_code,bill_code_original,batch_num,activity_id,main_trading,reward_trading,main_original,reward_original,revoked_trading,last_updatetime,store_updatetime,operate_time,invoice_balance,total_balance,reward_balance,main_balance,pay_type,salesman,commission_saler_money,commission_store_money,is_invoice,request_status,payment_state,recharge_state) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
		this.update(sql.toString(), new Object[]
		{ tenancyId, storeId, businessDate, shiftId, operatorId, operator, chanel, cardId, cardCode, cardClassId, name, mobil, deposit, customerId, billCode, operatType, billMoney, thirdBillCode, billCodeOriginal, batchNum, activityId, mainTrading, rewardTrading, mainOriginal, rewardOriginal,
				revokedTrading, lastUpdatetime, storeUpdatetime, operateTime, invoiceBalance, totalBalance, rewardBalance, mainBalance, payType, salesman, commissionSalerMoney, commissionStoreMoney, isInvoice, requestStatus, paymentState, rechargeState });
	}

	@Override
	public void insertCrmCardTradingList(String tenancyId, Integer storeId, CrmCardTradingListEntity cardTrading) throws Exception
	{
	    synchronized (CustomerDaoImp.class){
            StringBuilder sql = new StringBuilder(
                    "insert into crm_card_trading_list(tenancy_id,store_id,business_date,shift_id,operator_id,operator,chanel,card_id,card_code,card_class_id,name,mobil,deposit,customer_id,bill_code,operat_type,bill_money,third_bill_code,bill_code_original,batch_num,activity_id,main_trading,reward_trading,main_original,reward_original,revoked_trading,last_updatetime,store_updatetime,operate_time,invoice_balance,total_balance,reward_balance,main_balance,pay_type,salesman,commission_saler_money,commission_store_money,is_invoice,request_status,payment_state,recharge_state,pos_num) select ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,? where not exists (select 1 from crm_card_trading_list cctl where cctl.bill_code = ?) ");
            this.update(
                    sql.toString(),
                    new Object[]
                            { tenancyId, storeId, cardTrading.getBusiness_date(), cardTrading.getShift_id(), cardTrading.getOperator_id(), cardTrading.getOperator(), cardTrading.getChanel(), cardTrading.getCard_id(), cardTrading.getCard_code(), cardTrading.getCard_class_id(), cardTrading.getName(),
                                    cardTrading.getMobil(), cardTrading.getDeposit(), cardTrading.getCustomer_id(), cardTrading.getBill_code(), cardTrading.getOperat_type(), cardTrading.getBill_money(), cardTrading.getThird_bill_code(), cardTrading.getBill_code_original(), cardTrading.getBatch_num(),
                                    cardTrading.getActivity_id(), cardTrading.getMain_trading(), cardTrading.getReward_trading(), cardTrading.getMain_original(), cardTrading.getReward_original(), cardTrading.getRevoked_trading(), cardTrading.getLast_updatetime(), cardTrading.getStore_updatetime(),
                                    cardTrading.getOperate_time(), cardTrading.getInvoice_balance(), cardTrading.getTotal_balance(), cardTrading.getReward_balance(), cardTrading.getMain_balance(), cardTrading.getPay_type(), cardTrading.getSalesman(), cardTrading.getCommission_saler_money(),
                                    cardTrading.getCommission_store_money(), cardTrading.getIs_invoice(), cardTrading.getRequest_status(), cardTrading.getPayment_state(), cardTrading.getRecharge_state(),cardTrading.getPosNum(), cardTrading.getBill_code() });
        }
	}

	@Override
	public void deleteCrmCardTradingListByThirdBillCode(String tenancyId, Integer storeId, String thirdBillCode) throws Exception
	{
		// TODO Auto-generated method stub
		StringBuilder sql = new StringBuilder("delete from crm_card_trading_list where tenancy_id=? and store_id=? and third_bill_code=?");
		this.update(sql.toString(), new Object[]
		{ tenancyId, storeId, thirdBillCode });
	}

	@Override
	public void updateCrmCardTradingListForState(String tenancyId, Integer storeId, String thirdBillCode, String paymentState, String rechargeState, String requestStatus, String requestCode, String requestMsg) throws Exception
	{
		// TODO Auto-generated method stub
		StringBuilder sql = new StringBuilder("update crm_card_trading_list set payment_State=?,recharge_state=?,request_status=?,request_code=?,request_msg=? where tenancy_id=? and store_id=? and third_bill_code=?");
		this.update(sql.toString(), new Object[]
		{ paymentState, rechargeState, requestStatus, requestCode, requestMsg, tenancyId, storeId, thirdBillCode });
	}

	@Override
	public void updateCrmCardTradingPayState(String tenancyId, Integer storeId, String thirdBillCode, String paymentState, String rechargeState) throws Exception
	{
		// TODO Auto-generated method stub
		StringBuilder sql = new StringBuilder("update crm_card_trading_list set payment_state = ?,recharge_state=?,is_invoice=? where third_bill_code = ? AND tenancy_id = ? AND store_id = ?");
		this.update(sql.toString(), new Object[]
		{ paymentState, rechargeState, "0", thirdBillCode, tenancyId, storeId });
	}

	@Override
	public List<JSONObject> getCrmCardTradingListByThirdBillCode(String tenancyId, Integer storeId, String thirdBillCode) throws Exception
	{
		// TODO Auto-generated method stub
		StringBuilder sql = new StringBuilder("select * from crm_card_trading_list where tenancy_id=? and store_id=? and third_bill_code=?");
		return this.query4Json(tenancyId, sql.toString(), new Object[]
		{ tenancyId, storeId, thirdBillCode });
	}

	@Override
	public JSONObject getCrmCardTradingListByBillCode(String tenancyId, Integer storeId, String billCode) throws Exception
	{
		StringBuilder sql = new StringBuilder("select * from crm_card_trading_list where tenancy_id=? and store_id=? and bill_code=?");
		List<JSONObject> list = this.query4Json(tenancyId, sql.toString(), new Object[]
		{ tenancyId, storeId, billCode });

		if (null != list && list.size() > 0)
		{
			return list.get(0);
		}
		return null;
	}

	@Override
	public int getCrmInvoice(String tenancyId, Integer storeId, JSONObject para) throws Exception
	{
		StringBuilder sql = new StringBuilder(" select count(*) as invoice_record from crm_card_trading_list where bill_code=? and tenancy_id=? and is_invoice='1' and invoice_balance>0;  ");

		SqlRowSet rs = this.query4SqlRowSet(sql.toString(), new Object[]
		{ para.optString("bill_code"), tenancyId });

		int ret = 0;
		if (rs.next())
		{
			ret = rs.getInt("invoice_record");
		}
		return ret;
	}

	@Override
	public void insertCrmCardPaymentList(String tenancyId, Integer cardId, String billCode, String thirdBillCode, Integer paymentId, Double rexchangeRate, Double payMoney, Double localCurrency, String payNo, Timestamp storeUpdatetime, Timestamp lastUpdatetime) throws Exception
	{
		// TODO Auto-generated method stub
        synchronized (CustomerDaoImp.class){
            StringBuilder sql = new StringBuilder("insert into crm_card_payment_list(tenancy_id,card_id,payment_id,bill_code,pay_money,pay_no,rexchange_rate,local_currency,third_bill_code,store_updatetime,last_updatetime) select ?,?,?,?,?,?,?,?,?,?,? where not exists (select 1 from crm_card_payment_list ccpl where ccpl.bill_code = ?)");
            this.update(sql.toString(), new Object[]
                    { tenancyId, cardId, paymentId, billCode, payMoney, payNo, rexchangeRate, localCurrency, thirdBillCode, storeUpdatetime, lastUpdatetime, billCode });
        }
	}

	@Override
	public void insertCrmCardPaymentList(String tenancyId, Integer storeId, CrmCardPaymentListEntity cardPaymentList) throws Exception
	{
		// TODO Auto-generated method stub
		this.insertCrmCardPaymentList(tenancyId, cardPaymentList.getCard_id(), cardPaymentList.getBill_code(), cardPaymentList.getThird_bill_code(), cardPaymentList.getPayment_id(), cardPaymentList.getRexchange_rate(), cardPaymentList.getPay_money(), cardPaymentList.getLocal_currency(), cardPaymentList.getPay_no(),
				cardPaymentList.getStore_updatetime(), cardPaymentList.getLast_updatetime());
	}

	@Override
	public void deleteCrmCardPaymentListByThirdBillCode(String tenancyId, String thirdBillCode) throws Exception
	{
		// TODO Auto-generated method stub
		StringBuilder sql = new StringBuilder("delete from crm_card_payment_list where tenancy_id=? and third_bill_code=?");
		this.update(sql.toString(), new Object[]
		{ tenancyId, thirdBillCode });
	}

	@Override
	public List<JSONObject> getCrmCardPaymentListByThirdBillCode(String tenancyId, String thirdBillCode) throws Exception
	{
		// TODO Auto-generated method stub
		StringBuilder sql = new StringBuilder("select * from crm_card_payment_list where tenancy_id=? and third_bill_code=?");
		return this.query4Json(tenancyId, sql.toString(), new Object[]
		{ tenancyId, thirdBillCode });
	}

	@Override
	public List<JSONObject> getCrmPayMentWayByThirdBillCode(String tenancyId, String thirdBillCode) throws Exception
	{
		// TODO Auto-generated method stub
		String sql = " select payment_id as pay_id,pay_money as amount,third_bill_code as order_no, pw.payment_class AS pay_type " + " from crm_card_payment_list AS ccpl JOIN payment_way AS pw ON pw.id = ccpl.payment_id " + " WHERE ccpl.tenancy_id = ? AND third_bill_code = ? ";
		return this.query4Json(tenancyId, sql, new Object[]
		{ tenancyId, thirdBillCode });
	}

	@Override
	public List<JSONObject> getCrmInvoicePayment(String tenancyId, Integer storeId, JSONObject para) throws Exception
	{
		StringBuilder sql = new StringBuilder(" select round(sum(ccpl.pay_money),2) as pay_money from crm_card_payment_list ccpl ");
		sql.append(" left join payment_way pw on ccpl.payment_id=pw.id ");
		sql.append(" left join payment_way_of_ogran pwoo on pw.id=pwoo.payment_id ");
		sql.append(" where ccpl.bill_code=? and ccpl.tenancy_id=? and pw.if_invoicing='1' and ccpl.tenancy_id=pw.tenancy_id ");

		return this.queryString4Json(tenancyId, sql.toString(), new Object[]
		{ para.optString("bill_code"), tenancyId });
	}
	
	@Override
	public List<JSONObject> getCrmCardTradingListByCardcode(String tenancyId, Integer storeId, String cardcode, String thirdBillCode,String operatType) throws Exception
	{
		StringBuilder sql = new StringBuilder();
		sql.append("select tl.*,pl.payment_id,pl.pay_money from crm_card_trading_list tl ");
		sql.append("left join crm_card_payment_list pl on tl.tenancy_id=pl.tenancy_id and tl.bill_code=pl.bill_code and tl.third_bill_code=pl.third_bill_code ");
		sql.append("where tl.tenancy_id=? and tl.store_id=? and tl.card_code=? and tl.third_bill_code=? and tl.operat_type=?");

		return this.queryString4Json(tenancyId, sql.toString(), new Object[]
		{ tenancyId, storeId, cardcode, thirdBillCode, operatType });
	}

	@Override
	public void insertCrmIncorporationGzList(String tenancyId, Integer storeId, CrmIncorporationArrearListEntity incorporationGzList) throws Exception
	{
		StringBuilder sqlGz = new StringBuilder(
				"insert into crm_incorporation_gzlist (tenancy_id,incorporation_id,business_date,bill_code,gz_money,gz_person,store_id,remark,is_cz,cz_money,cz_person,cz_time,operate_time,operator,mz_money,wcz_money,third_bill_code,valid_state,name,customer_name) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");

		this.update(
				sqlGz.toString(),
				new Object[]
				{ tenancyId, incorporationGzList.getIncorporation_id(), incorporationGzList.getBusiness_date(), incorporationGzList.getBill_code(), incorporationGzList.getGz_money(), incorporationGzList.getGz_person(), storeId, incorporationGzList.getRemark(), incorporationGzList.getIs_cz(),
						incorporationGzList.getCz_money(), incorporationGzList.getCz_person(), incorporationGzList.getCz_time(), incorporationGzList.getOperate_time(), incorporationGzList.getOperator(), incorporationGzList.getMz_money(), incorporationGzList.getWcz_money(),
						incorporationGzList.getThird_bill_code(), incorporationGzList.getValid_state(), incorporationGzList.getName(), incorporationGzList.getCustomer_name() });
	}

	@Override
	public JSONObject getCrmLegalPerInfo(String tenancyId, Integer storeId, JSONObject para) throws Exception
	{
		StringBuilder sql = new StringBuilder(" select lp.cnpj as invoice_num,round(lp.tax_rate,2) as tax_rate from hq_legal_per_organ_ref lpor ");
		sql.append(" left join hq_legal_per lp on lpor.tenancy_id=lp.tenancy_id and lpor.legal_per_id=lp.id ");
		sql.append(" where lpor.tenancy_id=? and lpor.organ_id=? ");

		List<JSONObject> list = this.queryString4Json(tenancyId, sql.toString(), new Object[]
		{ tenancyId, storeId });

		if (list != null && list.size() > 0)
		{
			return list.get(0);
		}
		else
		{
			return null;
		}
	}

	@Override
	public void insertPosCustomerOperateList(String tenancyId, Integer storeId, PosCustomerOperateListEntity customerOperate) throws Exception
	{
		// TODO Auto-generated method stub
		String sql = new String(
				"insert into pos_customer_operate_list(tenancy_id,store_id,business_date,operator_id,pos_num,shift_id,operate_time,chanel,service_type,operat_type,customer_id,customer_code,mobil,customer_name,card_class_id,card_id,card_code,incorporation_id,incorporation_name,third_bill_code,third_bill_code_timestamp,batch_num,bill_code_original,bill_code,trade_amount,trade_credit,deposit,sales_price,sales_person,payment_id,payment_class,coupons_type_id,coupons_code,is_invoice,finish_time,payment_state,operate_state,cancel_state,request_status,request_code,request_msg,last_query_time,query_count,action_type,third_code,extend_param) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
		this.update(sql, new Object[]
		{ tenancyId, storeId, customerOperate.getBusiness_date(), customerOperate.getOperator_id(), customerOperate.getPos_num(), customerOperate.getShift_id(), customerOperate.getOperate_time(), customerOperate.getChanel(), customerOperate.getService_type(), customerOperate.getOperat_type(),
				customerOperate.getCustomer_id(), customerOperate.getCustomer_code(), customerOperate.getMobil(), customerOperate.getCustomer_name(), customerOperate.getCard_class_id(), customerOperate.getCard_id(), customerOperate.getCard_code(), customerOperate.getIncorporation_id(),
				customerOperate.getIncorporation_name(), customerOperate.getThird_bill_code(),customerOperate.getThird_bill_code_timestamp(),customerOperate.getBatch_num(), customerOperate.getBill_code_original(), customerOperate.getBill_code(), customerOperate.getTrade_amount(), customerOperate.getTrade_credit(), customerOperate.getDeposit(),
				customerOperate.getSales_price(), customerOperate.getSales_person(), customerOperate.getPayment_id(), customerOperate.getPayment_class(), customerOperate.getCoupons_type_id(), customerOperate.getCoupons_code(), customerOperate.getIs_invoice(), customerOperate.getFinish_time(),
				customerOperate.getPayment_state(), customerOperate.getOperate_state(), customerOperate.getCancel_state(), customerOperate.getRequest_status(), customerOperate.getRequest_code(), customerOperate.getRequest_msg(), customerOperate.getLast_query_time(),
				customerOperate.getQuery_count(), customerOperate.getAction_type(), customerOperate.getThird_code(), customerOperate.getExtend_param() });
	}

	@Override
	public void updatePosCustomerOperateList(String tenancyId, Integer storeId, PosCustomerOperateListEntity customerOperate) throws Exception
	{
		// TODO Auto-generated method stub
		String sql = new String(
				"update pos_customer_operate_list set payment_id=?,payment_state=?, customer_id=?,mobil=?,customer_name=?,card_class_id=?,card_id=?,finish_time=?,operate_state=?,cancel_state=?,request_status=?,request_code=?,request_msg=?,bill_code=?,extend_param=?  where tenancy_id=? and store_id=? and id=?");
		this.update(sql,
				new Object[]
				{ customerOperate.getPayment_id(), customerOperate.getPayment_state(), customerOperate.getCustomer_id(), customerOperate.getMobil(), customerOperate.getCustomer_name(), customerOperate.getCard_class_id(), customerOperate.getCard_id(), customerOperate.getFinish_time(),
						customerOperate.getOperate_state(), customerOperate.getCancel_state(), customerOperate.getRequest_status(), customerOperate.getRequest_code(), customerOperate.getRequest_msg(), customerOperate.getBill_code(),customerOperate.getExtend_param(),tenancyId, storeId, customerOperate.getId() });
	}

	@Override
	public void updatePosCustomerOperateList(String tenancyId, Integer storeId, String thirdBillCode, String paymentState, String operateState, String requestStatus, Integer requestCode, String requestMsg, Timestamp finishTime, String operatType) throws Exception
	{
		String sql = new String("update pos_customer_operate_list set finish_time=?,operate_state=?,payment_state=?,request_status=?,request_code=?,request_msg=? where tenancy_id=? and store_id=? and third_bill_code=? and operat_type=?");
		this.update(sql, new Object[]
		{ finishTime, operateState, paymentState, requestStatus, requestCode, requestMsg, tenancyId, storeId, thirdBillCode, operatType });
	}
	
	@Override
	public void updatePosCustomerOperateList(String tenancyId, Integer storeId, String thirdBillCode, String paymentState, String operateState, String requestStatus, Integer requestCode, String requestMsg, Timestamp finishTime, String operatType, String billCode, String extendParam) throws Exception
	{
		String sql = new String("update pos_customer_operate_list set finish_time=?,operate_state=?,payment_state=?,request_status=?,request_code=?,request_msg=?,bill_code=?,extend_param =? where tenancy_id=? and store_id=? and third_bill_code=? and operat_type=?");
		this.update(sql, new Object[]
		{ finishTime, operateState, paymentState, requestStatus, requestCode, requestMsg, billCode, extendParam, tenancyId, storeId, thirdBillCode, operatType });
	}
	
	@Override
	public void updatePosCustomerOperateList(String tenancyId, Integer storeId, String thirdBillCode,String paymentState,String operateState,String requestStatus,Integer requestCode,String requestMsg,Timestamp finishTime,String operatType,Integer paymentId,String paymentClass) throws Exception
	{
		String sql = new String("update pos_customer_operate_list set finish_time=?,operate_state=?,payment_state=?,request_status=?,request_code=?,request_msg=?,payment_id=?,payment_class=? where tenancy_id=? and store_id=? and third_bill_code=? and operat_type=?");
		this.update(sql, new Object[]
		{ finishTime, operateState, paymentState, requestStatus, requestCode, requestMsg, paymentId, paymentClass, tenancyId, storeId, thirdBillCode, operatType });
	}
	
	@Override
	public void updatePosCustomerOperateList(String tenancyId, Integer storeId, String thirdBillCode, String cancelState, String operatType) throws Exception
	{
		String sql = new String("update pos_customer_operate_list set cancel_state=? where tenancy_id=? and store_id=? and third_bill_code=? and operat_type=?");
		this.update(sql, new Object[]
		{ cancelState, tenancyId, storeId, thirdBillCode, operatType });
	}

	@Override
	public PosCustomerOperateListEntity queryPosCustomerOperateListByBillcode(String tenancyId, Integer storeId, String customerCode, String mobil, String cardCode, String thirdCode, String incorporationId, String customerId, String thirdBillCode, String batchNum, String operatType) throws Exception
	{
		StringBuffer sql = new StringBuffer("select * from pos_customer_operate_list where tenancy_id=? and store_id=?");

		if (Tools.hv(customerCode) && !"null".equals(customerCode))
		{
			sql.append(" and customer_code ='").append(customerCode).append("'");
		}
		if (Tools.hv(mobil) && !"null".equals(mobil))
		{
			sql.append(" and mobil ='").append(mobil).append("'");
		}
		if (Tools.hv(cardCode) && !"null".equals(cardCode))
		{
			sql.append(" and card_code ='").append(cardCode).append("'");
		}
		if (Tools.hv(thirdCode) && !"null".equals(thirdCode))
		{
			sql.append(" and third_code ='").append(thirdCode).append("'");
		}
		if (Tools.hv(thirdBillCode) && !"null".equals(thirdBillCode))
		{
			sql.append(" and third_bill_code ='").append(thirdBillCode).append("'");
		}
		if (Tools.hv(batchNum) && !"null".equals(batchNum))
		{
			sql.append(" and batch_num ='").append(batchNum).append("'");
		}
		if (Tools.hv(incorporationId) && !"null".equals(incorporationId))
		{
			sql.append(" and incorporation_id ='").append(incorporationId).append("'");
		}
		if (Tools.hv(customerId) && !"null".equals(customerId))
		{
			sql.append(" and customer_id ='").append(customerId).append("'");
		}
		if (Tools.hv(operatType) && !"null".equals(operatType))
		{
			sql.append(" and operat_type ='").append(operatType).append("'");
		}

		List<PosCustomerOperateListEntity> list = (List<PosCustomerOperateListEntity>) this.query(sql.toString(), new Object[]
		{ tenancyId, storeId }, BeanPropertyRowMapper.newInstance(PosCustomerOperateListEntity.class));

		if (null != list && list.size() > 0)
		{
			return list.get(0);
		}
		return null;
	}
	
	@Override
	public PosCustomerOperateListEntity queryPosCustomerOperateListByCustomerCard(String tenancyId, Integer storeId, String cardCode, String thirdBillCode, String batchNum, String operatType) throws Exception
	{
		return this.queryPosCustomerOperateListByBillcode(tenancyId, storeId, null, null, cardCode, null, null, null, thirdBillCode, batchNum, operatType);
	}

	@Override
	public PosCustomerOperateListEntity queryPosCustomerOperateListByCredit(String tenancyId, Integer storeId, String customerCode, String mobil, String thirdBillCode, String batchNum, String operatType) throws Exception
	{
		return this.queryPosCustomerOperateListByBillcode(tenancyId, storeId, customerCode, mobil, null, null, null, null, thirdBillCode, batchNum, operatType);
	}

	@Override
	public PosCustomerOperateListEntity queryPosCustomerOperateListByIncorporation(String tenancyId, Integer storeId, String incorporationId, String customerId, String thirdBillCode, String batchNum, String operatType) throws Exception
	{
		return this.queryPosCustomerOperateListByBillcode(tenancyId, storeId, null, null, null, null, incorporationId, customerId, thirdBillCode, batchNum, operatType);
	}

	@Override
	public PosCustomerOperateListEntity queryCustomerCardActivationStatus(String tenancyId, Integer storeId, String cardCode, String thirdCode, String thirdBillCode) throws Exception
	{
		return this.queryPosCustomerOperateListByBillcode(tenancyId, storeId, null, null, cardCode, thirdCode, null, null, thirdBillCode, null, SysDictionary.OPERAT_TYPE_FK);
	}

	@Override
	public PosCustomerOperateListEntity queryCustomerCardRechargeStatus(String tenancyId, Integer storeId, String cardCode, String thirdCode, String thirdBillCode,String operatType) throws Exception
	{
		return this.queryPosCustomerOperateListByBillcode(tenancyId, storeId, null, null, cardCode, thirdCode, null, null, thirdBillCode, null, operatType);
	}
	
	@Override
	public PosCustomerOperateListEntity queryPosCustomerOperateListByBillcode(String tenancyId, Integer storeId, JSONObject paramJson) throws Exception
	{
		StringBuffer sql = new StringBuffer("select * from pos_customer_operate_list where tenancy_id=? and store_id=?");

		String[] keyNames = new String[]
		{ "customer_code", "mobil", "card_code", "third_code", "operat_type", "third_bill_code", "batch_num" };

		for (String keyName : keyNames)
		{
			if (null != paramJson && paramJson.containsKey(keyName))
			{
				sql.append(" and ").append(keyName).append(" ='").append(paramJson.optString(keyName)).append("'");
			}
		}

		if (null != paramJson && paramJson.containsKey("incorporation_id"))
		{
			sql.append(" and incorporation_id ='").append(paramJson.optInt("incorporation_id")).append("'");
		}
		if (null != paramJson && paramJson.containsKey("customer_id"))
		{
			sql.append(" and customer_id ='").append(paramJson.optInt("customer_id")).append("'");
		}

		List<PosCustomerOperateListEntity> list = (List<PosCustomerOperateListEntity>) this.query(sql.toString(), new Object[]
		{ tenancyId, storeId }, BeanPropertyRowMapper.newInstance(PosCustomerOperateListEntity.class));

		if (null != list && list.size() > 0)
		{
			return list.get(0);
		}
		return null;
	}
	
	@Override
	public PosCustomerOperateListEntity queryAcewillCustomerDeal(String tenancyId, Integer storeId, String cardCode,String thirdBillCode, String billCode,String operateState,String cancelState, String operatType) throws Exception
	{
		StringBuffer sql = new StringBuffer("select * from pos_customer_operate_list where tenancy_id=? and store_id=?");
		if (Tools.hv(cardCode) && !"null".equals(cardCode))
		{
			sql.append(" and card_code ='").append(cardCode).append("'");
		}
		if (Tools.hv(thirdBillCode) && !"null".equals(thirdBillCode))
		{
			sql.append(" and third_bill_code ='").append(thirdBillCode).append("'");
		}
		if (Tools.hv(billCode) && !"null".equals(billCode))
		{
			sql.append(" and bill_code ='").append(billCode).append("'");
		}
		if (Tools.hv(operatType) && !"null".equals(operatType))
		{
			sql.append(" and operat_type ='").append(operatType).append("'");
		}
		if(Tools.hv(operateState) && !"null".equals(operateState)){
			sql.append(" and operate_state='").append(operateState).append("'");
		}
		if(Tools.hv(cancelState) && !"null".equals(cancelState)){
			sql.append(" and cancel_state='").append(cancelState).append("'");
		}
		sql.append(" order by operate_time desc");
		
		List<PosCustomerOperateListEntity> list = (List<PosCustomerOperateListEntity>) this.query(sql.toString(), new Object[]
		{ tenancyId, storeId }, BeanPropertyRowMapper.newInstance(PosCustomerOperateListEntity.class));

		if (null != list && list.size() > 0)
		{
			return list.get(0);
		}
		return null;
	}
	
	@Override
	public PosCustomerOperateListEntity queryCustomerOperateListByAcewillXF(String tenancyId, Integer storeId, String cardCode,String thirdBillCode, String outTradeNo, String operatType) throws Exception
	{
		StringBuffer sql = new StringBuffer("select * from pos_customer_operate_list where tenancy_id=? and store_id=?");
		if (Tools.hv(cardCode) && !"null".equals(cardCode))
		{
			sql.append(" and card_code ='").append(cardCode).append("'");
		}
		if (Tools.hv(thirdBillCode) && !"null".equals(thirdBillCode))
		{
			sql.append(" and third_bill_code ='").append(thirdBillCode).append("'");
		}
		if (Tools.hv(outTradeNo) && !"null".equals(outTradeNo))
		{
			sql.append(" and third_bill_code_timestamp ='").append(outTradeNo).append("'");
		}
		if (Tools.hv(operatType) && !"null".equals(operatType))
		{
			sql.append(" and operat_type ='").append(operatType).append("'");
		}
	    sql.append(" and operate_state<>'0' and cancel_state<>'1' order by operate_time desc");
		List<PosCustomerOperateListEntity> list = (List<PosCustomerOperateListEntity>) this.query(sql.toString(), new Object[]
		{ tenancyId, storeId }, BeanPropertyRowMapper.newInstance(PosCustomerOperateListEntity.class));

		if (null != list && list.size() > 0)
		{
			return list.get(0);
		}
		return null;
	}

	@Override
	public JSONObject findPrintParam(String tenancyId, Integer storeId, int payId) throws Exception{
		// TODO Auto-generated method stub
		JSONObject result = new JSONObject();
		//查询付款方式名称
		String payment_name1 = "";
		String findPaymentNameSql = "select payment_name1 from payment_way where tenancy_id = '"+tenancyId+"' and id = "+payId+" ";
		SqlRowSet findPaymentNameRowSet = this.query(tenancyId, findPaymentNameSql);
		if(findPaymentNameRowSet!=null){
			while(findPaymentNameRowSet.next()){
				payment_name1 = findPaymentNameRowSet.getString("payment_name1");
			}
		}
		
		//查询门店名称
		String organ_name = "";
		String findOrganNameSql = "select org_full_name from organ where tenancy_id = '"+tenancyId+"' and id = "+storeId+" ";
		SqlRowSet findOrganNameRowSet = this.query(tenancyId, findOrganNameSql);
		if(findOrganNameRowSet!=null){
			while(findOrganNameRowSet.next()){
				organ_name = findOrganNameRowSet.getString("org_full_name");
			}
		}
		
		result.put("organ_name", organ_name);
		result.put("payment_name1", payment_name1);
		
		return result;
	}

	@Override
	public PosThirdPaymentOrderEntity getThirdPaymentByAidOrderNum(String tenancyId, int storeId, String aidOrderNum, String objectName) throws Exception
	{
		String querySql = "select * from pos_third_payment_order where tenancy_id=? and store_id=? and aid_order_num=? and object_name=? and (is_refunded is null or is_refunded=false)";

		List<PosThirdPaymentOrderEntity> list = (List<PosThirdPaymentOrderEntity>) this.query(querySql, new Object[]
		{ tenancyId, storeId, aidOrderNum, objectName },BeanPropertyRowMapper.newInstance(PosThirdPaymentOrderEntity.class));
		if (list != null && list.size() > 0)
		{
			return list.get(0);
		}
		return null;
	}
	
	@Override
	public List<JSONObject> qureyCrmCardTradingListByThirdBillCode(String tenancyId, Integer storeId, String thirdBillCode) throws Exception
	{
		// TODO Auto-generated method stub
		StringBuilder sql = new StringBuilder("select *,cpl.payment_id,cpl.pay_money,cpl.local_currency from crm_card_trading_list ctl left join crm_card_payment_list cpl on ctl.tenancy_id=cpl.tenancy_id and ctl.third_bill_code=cpl.third_bill_code where ctl.tenancy_id=? and ctl.store_id=? and ctl.third_bill_code=?");
		return this.query4Json(tenancyId, sql.toString(), new Object[]
		{ tenancyId, storeId, thirdBillCode });
	}
}
