package com.tzx.framework.common.constant;
 
/**
 * saas
 * <AUTHOR>
 *
 */
public class EngineConstantArea {
 
/*************************************************************************SAAS_BI_2016_02**************************************************************************/
	
	/**
	 *	员工销售提成查询YGCPHZL1（按菜品汇总）
	 */
	public static final String ENGINE_CHECK_THE_FIRST_FLOOR_ACCORDING_TO_THE_DISHES = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_02' AND SQL_TYPE='YGCPHZL1'";
	
	/**
	 *	员工销售提成查询YGCPHZL2
	 */
	public static final String ENGINE_CHECK_THE_SECOND_LAYERS_ACCORDING_TO_THE_DISHES = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_02' AND SQL_TYPE='YGCPHZL2'";
	
	/**
	 *	员工销售提成查询YGCPHZL3
	 */
	public static final String ENGINE_CHECK_THE_THIRD_LAYERS_ACCORDING_TO_THE_DISHES = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_02' AND SQL_TYPE='YGCPHZL3'";
	
	/**
	 *	员工销售提成查询YGCPHZL0
	 */
	public static final String ENGINE_TOTAL_ACCORDING_TO_DISHES_QUERY_SUMMARY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_02' AND SQL_TYPE='YGCPHZL0'";
	
	
	
	/**
	 *	员工销售提成查询YGYGHZL1（按员工汇总）
	 */
	public static final String ENGINE_COLLECT_THE_FIRST_FLOOR_ACCORDING_TO_THE_STAFF = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_02' AND SQL_TYPE='YGYGHZL1'";
	
	/**
	 *	员工销售提成查询YGYGHZL2
	 */
	public static final String ENGINE_COLLECT_SECOND_LAYERS_BY_STAFF = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_02' AND SQL_TYPE='YGYGHZL2'";
	
	/**
	 *	员工销售提成查询YGYGHZL3
	 */
	public static final String ENGINE_COLLECT_THIRD_LAYERS_BY_STAFF = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_02' AND SQL_TYPE='YGYGHZL3'";
	
	/**
	 *	员工销售提成查询YGYGHZL0
	 */
	public static final String ENGINE_TOTAL_COLLECT_BY_EMPLOYEE = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_02' AND SQL_TYPE='YGYGHZL0'";
	
	
	
	/**
	 *	员工销售提成查询YGRQHZL1   （按日期汇总）
	 */
	public static final String ENGINE_COLLECT_THE_FIRST_FLOOR_BY_DATE = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_02' AND SQL_TYPE='YGRQHZL1'";
	
	/**
	 *	员工销售提成查询YGRQHZL2
	 */
	public static final String ENGINE_COLLECT_SECOND_LAYERS_BY_DATE = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_02' AND SQL_TYPE='YGRQHZL2'";
	
	/**
	 *	员工销售提成查询YGRQHZL3
	 */
	public static final String ENGINE_COLLECT_THIRD_LAYERS_BY_DATE = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_02' AND SQL_TYPE='YGRQHZL3'";
	
	/**
	 *	员工销售提成查询YGRQHZL0
	 */
	public static final String ENGINE_TOTAL_AGGREGATED_BY_DATE = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_02' AND SQL_TYPE='YGRQHZL0'";
	
	
	/**
	 *	员工销售提成查询YGJGHZL1（按机构汇总）
	 */
	public static final String ENGINE_COLLECT_THE_FIRST_FLOOR_ACCORDING_TO_THE_ORGANIZATION = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_02' AND SQL_TYPE='YGJGHZL1'";
	
	/**
	 *	员工销售提成查询YGJGHZL2
	 */
	public static final String ENGINE_COLLECT_SECOND_LAYERS_ACCORDING_TO_THE_ORGANIZATION = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_02' AND SQL_TYPE='YGJGHZL2'";
	
	/**
	 *	员工销售提成查询YGJGHZL3
	 */
	public static final String ENGINE_COLLECT_THIRD_LAYERS_ACCORDING_TO_THE_ORGANIZATION = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_02' AND SQL_TYPE='YGJGHZL3'";
	
	/**
	 *	员工销售提成查询YGJGHZL0
	 */
	public static final String ENGINE_TOTAL_COLLECT_BY_ORGANIZATION = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_02' AND SQL_TYPE='YGJGHZL0'";
	
	
	/*************************************************************************SAAS_BI_2016_11**************************************************************************/
	
	/**
	 * 菜品销售汇总第一层
	 */
	public static final String ENGINE_DISHES_SALES_SUMMARY_OF_THE_FIRST_LAYER = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_11' AND SQL_TYPE='L1'";
	
	/**
	 * 菜品销售汇总
	 */
	public static final String ENGINE_FOOD_SALES_SUMMARY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_11' AND SQL_TYPE='L0'";
	
	/**
	 * 菜品销售类别第一层
	 */
	public static final String ENGINE_THE_FIRST_CATEGORY_OF_FOOD_SALES_CATEGORY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_11' AND SQL_TYPE='LB1'";
	
	/**
	 * 菜品销售类别第二层
	 */
	public static final String ENGINE_FOOD_SALES_CATEGORY_SECOND = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_11' AND SQL_TYPE='LB2'";
	
	/**
	 * 菜品销售类别第三层
	 */
	public static final String ENGINE_FOOD_SALES_CATEGORY_THIRD = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_11' AND SQL_TYPE='LB3'";
	
	/**
	 * 菜品销售类别合计
	 */
	public static final String ENGINE_TOTAL_SALES_CATEGORY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_11' AND SQL_TYPE='LB0'";
	
	
	/*************************************************************************SAAS_BI_2016_19**************************************************************************/
	
	/**
	 * 菜品销售实时报表
	 */
	public static final String ENGINE_FOOD_SALES_REAL_TIME_REPORT = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_19' AND SQL_TYPE='CPL1'";
	
	/**
	 * 菜品销售实时报表合计
	 */
	public static final String ENGINE_TOTAL_FOOD_SALES_REAL_TIME_REPORT = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_19' AND SQL_TYPE='CPL0'";
	
	
	/*************************************************************************SAAS_BI_2016_21**************************************************************************/
	
	/**
	 * 按菜品汇总
	 */
	public static final String ENGINE_ACCORDING_TO_DISHES_SUMMARY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_21' AND SQL_TYPE='CPL0'";
	
	/**
	 * 按菜品汇总第一层
	 */
	public static final String ENGINE_ACCORDING_TO_THE_FIRST_LAYER_OF_DISHES = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_21' AND SQL_TYPE='CPL1'";
	
	/**
	 * 按菜品汇总第二层
	 */
	public static final String ENGINE_ACCORDING_TO_THE_SECOND_LAYERS_OF_DISHES_SUMMARY= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_21' AND SQL_TYPE='CPL2'";
	
	/**
	 * 按时段汇总
	 */
	public static final String ENGINE_ACCORDING_TO_TIME_SUMMARY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_21' AND SQL_TYPE='SDL0'";
	
	/**
	 * 按时段汇总第一层
	 */
	public static final String ENGINE_ACCORDING_TO_THE_TIME_OF_THE_FIRST_LAYER = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_21' AND SQL_TYPE='SDL1'";
	
	/**
	 * 按时段汇总第二层
	 */
	public static final String ENGINE_SECOND_LAYERS_ACCORDING_TO_THE_TIME_SUMMARY= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_21' AND SQL_TYPE='SDL2'";
	
	
	/*************************************************************************SAAS_BI_2016_26**************************************************************************/
	/**
	 * 套餐销售汇总报表第一层
	 */
	public static final String ENGINE_PACKAGE_SALES_SUMMARY_REPORT = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_26' AND SQL_TYPE='L1'";
	
	/**
	 * 套餐销售汇总报表汇总
	 */
	public static final String ENGINE_PACKAGE_SALES_SUMMARY_REPORT_SUMMARY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_26' AND SQL_TYPE='L0'";
	
	/**
	 * 套餐销售汇总套餐明细第一层
	 */
	public static final String ENGINE_PACKAGE_SALES_SUMMARY_REPORT_MEAL_SALEL1 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_26' AND SQL_TYPE='TCMXL1'";
	
	/**
	 * 套餐销售汇总套餐明细第二层
	 */
	public static final String ENGINE_PACKAGE_SALES_SUMMARY_REPORT_MEAL_SALEL2 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_26' AND SQL_TYPE='TCMXL2'";
	
	/**
	 * 套餐销售汇总套餐明细汇总
	 */
	public static final String ENGINE_PACKAGE_SALES_SUMMARY_REPORT_SUMMARYL0 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_26' AND SQL_TYPE='TCMXL0'";
	
	/*************************************************************************SAAS_BI_2016_29**************************************************************************/
	/**
	 * 按日期汇总对比分析汇总
	 */
	public static  final String ENGINE_SUMMARY_OF_COMPARATIVE_ANALYSIS_BY_DATE = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_29' AND SQL_TYPE='RQL0'";
	
	/**
	 * 按日期汇总对比分析第一层
	 */
	public static  final String ENGINE_SUMMARIZE_AND_CONTRAST_THE_FIRST_LAYER_BY_DATE = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_29' AND SQL_TYPE='RQL1'";
	
	/**
	 * 按日期汇总对比分析第二层
	 */
	public static  final String ENGINE_SUMMARIZE_AND_CONTRAST_THE_SECOND_LAYE_BY_DATE = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_29' AND SQL_TYPE='RQL2'";
	
	/**
	 * 按日期汇总对比分析第三层
	 */
	public static  final String ENGINE_SUMMARIZE_AND_CONTRAST_THE_THIRD_LAYER_BY_DATE = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_29' AND SQL_TYPE='RQL3'";
	
	
	/**
	 * 按交易门店汇总对比分析汇总
	 */
	public static  final String ENGINE_SUMMARY_OF_COMPARATIVE_ANALYSIS_BY_THE_TRANSACTION_STORE = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_29' AND SQL_TYPE='JGL0'";
	
	/**
	 * 按交易门店对比分析第一层
	 */
	public static  final String ENGINE_A_COMPARATIVE_ANALYSIS_OF_THE_FIRST_LAYER_OF_THE_STORE_BY_THE_TRANSACTION = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_29' AND SQL_TYPE='JGL1'";
	
	/**
	 * 按交易门店对比分析第二层
	 */
	public static  final String ENGINE_ACCORDING_TO_THE_COMPARATIVE_ANALYSIS_OF_THE_SECOND_STORES = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_29' AND SQL_TYPE='JGL2'";
	
	/**
	 * 按交易门店对比分析第三层
	 */
	public static  final String ENGINE_ACCORDING_TO_THE_COMPARATIVE_ANALYSIS_OF_THE_THIRD_STORES = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_29' AND SQL_TYPE='JGL3'";
	
	
	/*************************************************************************SAAS_BI_2016_30**************************************************************************/
	
	/**
	 * 门店应收总部金额情况第一层
	 */
	public static  final String ENGINE_THE_FIRST_PART_OF_THE_TOTAL_AMOUNT_OF_RECEIVABLES = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_30' AND SQL_TYPE='MDL1'";
	
	/**
	 * 门店应收总部金额情况第二层
	 */
	public static  final String ENGINE_THE_SECOND_PART_OF_THE_TOTAL_AMOUNT_OF_ACCOUNTS_RECEIVABLE = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_30' AND SQL_TYPE='MDL2'";
	
	
	/**
	 * 门店应收总部金额情况汇总 
	 */
	public static  final String ENGINE_SUMMARY_OF_THE_TOTAL_RECEIVABLES_RECEIVABLE = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_30' AND SQL_TYPE='MDL0'";
	

	/**
	 * 总部应付门店资金情况第一层   
	 */
	public static  final String ENGINE_HEADQUARTERS_TO_DEAL_WITH_THE_FIRST_TIER_OF_STORE_FUNDS = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_30' AND SQL_TYPE='ZBL1'";
	
	/**
	 * 总部应付门店资金情况第二层   
	 */
	
	public static  final String ENGINE_HEADQUARTERS_TO_DEAL_WITH_THE_STORE_SECOND_LAYERS_OF_FUNDS = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_30' AND SQL_TYPE='ZBL2'";

	
	/**
	 * 总部应付门店资金情况汇总 
	 */
	public static  final String ENGINE_HEADQUARTERS_TO_DEAL_WITH_STORE_FUNDS_SUMMARY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_30' AND SQL_TYPE='ZBL0'";
	
	
	
	/**
	 * 会员卡跨机构结算类型(本本消费或本本充值)
	 */
	public static  final String ENGINE_BOOKS_OR_BOOKS_RECHARGE_CONSUMPTION = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_30' AND SQL_TYPE='LXL1'";
	
	/**
	 * 会员卡跨机构结算类型（LXL1合计）
	 */
	
	public static  final String ENGINE_THE_BOOKS_OR_BOOKS_TOTAL_PREPAID_CONSUMPTION = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_30' AND SQL_TYPE='LXL10'";
	
	
	/**
	 * 会员卡跨机构结算类型（本他消费、本他充值，他本消费、他本充值第一层）
	 */
	
	public static  final String ENGINE_THIS_IS_THE_FIRST_TIME_HE_OR_HE_OR_HIS_CURRENT_CONSUMPTION_OR_RECHARGE = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_30' AND SQL_TYPE='LXL2'";
	
	
	/**
	 * 会员卡跨机构结算类型（LXL2合计）
	 */
	
	public static  final String ENGINE_THIS_IS_THE_SUM_OF_HIS_OR_HER_CURRENT_CONSUMPTION_OR_HIS_CURRENT_CONSUMPTION_OR_THE_TOTAL_VALUE_OF_HIS = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_30' AND SQL_TYPE='LXL0'";
	
	
	
	

	/*************************************************************************SAAS_BI_2016_48**************************************************************************/
	/**
	 * 营业汇总班次查询
	 */
	public static final String ENGINE_BUSINESS_SUMMARY_FREQUENCY_OFFAST_FOOD = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_48' AND SQL_TYPE='Y'";
	
	/*************************************************************************SAAS_BI_2016_49**************************************************************************/
	/**
	 * 营业汇总时段查询
	 */
	public static final String ENGINE_BUSINESS_SUMMARY_PERIOD = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_49' AND SQL_TYPE='Y'";
	
	
	/*************************************************************************SAAS_BI_2016_47**************************************************************************/
	/**
	 * 营业汇总统计查询
	 */
	public static final String ENGINE_BUSINESS_SUMMARY_STATISTICS = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_47' AND SQL_TYPE='Y'";
	
	
	/*************************************************************************SAAS_BI_2016_51**************************************************************************/
	
	/**
	 * 营销活动整体分析
	 */
	public static final String ENGINE_THE_WHOLE_ANALYSIS_OF_MARKETING_ACTIVITIES = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_51' AND SQL_TYPE='G1'";
	
	/**
	 * 营销活动综合分析
	 */
	public static final String ENGINE_COMPREHENSIVE_ANALYSIS_OF_MARKETING_ACTIVITIES = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_51' AND SQL_TYPE='G2'";
	
	/**
	 * 营销活动专项分析
	 */
	public static final String ENGINE_SPECIAL_ANALYSIS_OF_MMARKETINGAACTIVITIES = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_51' AND SQL_TYPE='G3'";
	
	/**
	 * 营销票券回收率分析
	 */
	public static final String ENGINE_ANALYSIS_OF_THE_RATE_OF_RECOVERY_OF_SECURITIES_MARKETING = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_51' AND SQL_TYPE='G4'";

	/**
	 * 日分析
	 */
	public static final String ENGINE_DAY_ANALYSIS = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_51' AND SQL_TYPE='G5'";
	
	/**
	 * 周分析
	 */
	public static final String ENGINE_WEEKLY_ANALYSIS = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_51' AND SQL_TYPE='G6'";
	
	/**
	 * 月分析
	 */
	public static final String ENGINE_MONTHLY_ANALYSIS = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_51' AND SQL_TYPE='G7'";
	
	
	/*************************************************************************SAAS_BI_2016_52**************************************************************************/
	
	/**
	 * 营销活动概要报告
	 */
	public static final String ENGINE_MARKETING_CAMPAIGN_SUMMARYREPORT52 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_52' AND SQL_TYPE='G1'";
	
	/**
	 * 营销活动详细报告
	 */
	public static final String ENGINE_DETAILED_REPORT_ON_MARKETING_ACTIVITIES52 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_52' AND SQL_TYPE='G2'";
	
	/**
	 * 营销票券回收率分析
	 */
	public static final String ENGINE_ANALYSIS_OF_THE_RATE_OF_RECOVERY_OF_SECURITIES_MARKETING52 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_52' AND SQL_TYPE='G3'";
	
	/**
	 * 营销客户响应率
	 */
	public static final String ENGINE_MARKETING_CUSTOMER_RESPONSE_RATE52 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_52' AND SQL_TYPE='G4'";
	
	/**
	 * 营销活动券撬动率
	 */
	public static final String ENGINE_COUPON_RATE_LEVERAGING_MARKETING_ACTIVITIES52 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_52' AND SQL_TYPE='G5'";
	
	/**
	 * 渠道日分析
	 */
	public static final String ENGINE_CHANNEL_DAY_ANALYSIS52 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_52' AND SQL_TYPE='G6'";
	
	/**
	 * 渠道周分析
	 */
	public static final String ENGINE_CHANNEL_WEEKLY_ANALYSIS52 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_52' AND SQL_TYPE='G7'";
	
	/**
	 * 渠道月分析
	 */
	public static final String ENGINE_CHANNEL_ANALYSIS52 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_52' AND SQL_TYPE='G8'";
	
	/**
	 * 机构日分析
	 */
	public static final String ENGINE_INSTITUTIONAL_DAY_ANALYSIS52 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_52' AND SQL_TYPE='G9'";
	
	/**
	 * 机构周分析
	 */
	public static final String ENGINE_MECHANISM_WEEK_ANALYSIS52 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_52' AND SQL_TYPE='G10'";
	
	/**
	 * 机构月分析
	 */
	public static final String ENGINE_INSTITUTIONAL_MONTH_ANALYSIS52 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_52' AND SQL_TYPE='G11'";
	
	
	
	/*************************************************************************SAAS_BI_2016_55**************************************************************************/
 
	/**
	 * 营销活动概要报告
	 */
	public static final String ENGINE_MARKETING_CAMPAIGN_SUMMARYREPORT55 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_55' AND SQL_TYPE='G1'";
	
	/**
	 * 营销活动详细报告
	 */
	public static final String ENGINE_DETAILED_REPORT_ON_MARKETING_ACTIVITIES55 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_55' AND SQL_TYPE='G2'";
	
	/**
	 * 营销票券回收率分析
	 */
	public static final String ENGINE_ANALYSIS_OF_THE_RATE_OF_RECOVERY_OF_SECURITIES_MARKETING55 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_55' AND SQL_TYPE='G3'";
	
	/**
	 * 营销客户响应率
	 */
	public static final String ENGINE_MARKETING_CUSTOMER_RESPONSE_RATE55 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_55' AND SQL_TYPE='G4'";
	
	/**
	 * 营销活动券撬动率
	 */
	public static final String ENGINE_COUPON_RATE_LEVERAGING_MARKETING_ACTIVITIES55 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_55' AND SQL_TYPE='G5'";
	
	/**
	 * 日分析
	 */
	public static final String ENGINE_CHANNEL_DAY_ANALYSIS55 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_55' AND SQL_TYPE='G6'";
	
	/**
	 * 周分析
	 */
	public static final String ENGINE_CHANNEL_WEEKLY_ANALYSIS55 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_55' AND SQL_TYPE='G7'";
	
	/**
	 * 月分析
	 */
	public static final String ENGINE_CHANNEL_ANALYSIS55 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_55' AND SQL_TYPE='G8'";
	
	
	
	/*************************************************************************SAAS_BI_2016_56**************************************************************************/
	 
	/**
	 * 营销活动概要报告
	 */
	public static final String ENGINE_MARKETING_CAMPAIGN_SUMMARYREPORT56 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_56' AND SQL_TYPE='G1'";
	
	/**
	 * 营销活动详细报告
	 */
	public static final String ENGINE_DETAILED_REPORT_ON_MARKETING_ACTIVITIES56 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_56' AND SQL_TYPE='G2'";
	
	/**
	 * 营销票券回收率分析
	 */
	public static final String ENGINE_ANALYSIS_OF_THE_RATE_OF_RECOVERY_OF_SECURITIES_MARKETING56 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_56' AND SQL_TYPE='G3'";
	
	/**
	 * 营销客户响应率
	 */
	public static final String ENGINE_MARKETING_CUSTOMER_RESPONSE_RATE56 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_56' AND SQL_TYPE='G4'";
	
	/**
	 * 营销活动券撬动率
	 */
	public static final String ENGINE_COUPON_RATE_LEVERAGING_MARKETING_ACTIVITIES56 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_56' AND SQL_TYPE='G5'";
	
	/**
	 * 日分析
	 */
	public static final String ENGINE_CHANNEL_DAY_ANALYSIS56 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_56' AND SQL_TYPE='G6'";
	
	/**
	 * 周分析
	 */
	public static final String ENGINE_CHANNEL_WEEKLY_ANALYSIS56 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_56' AND SQL_TYPE='G7'";
	
	/**
	 * 月分析
	 */
	public static final String ENGINE_CHANNEL_ANALYSIS56 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_56' AND SQL_TYPE='G8'";
	
	
	
	
	/*************************************************************************SAAS_BI_2016_57**************************************************************************/
	
	
	/**
	 * 营销活动概要报告
	 */
	public static final String ENGINE_MARKETING_CAMPAIGN_SUMMARYREPORT57 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_57' AND SQL_TYPE='G1'";
	
	/**
	 * 营销活动详细报告
	 */
	public static final String ENGINE_DETAILED_REPORT_ON_MARKETING_ACTIVITIES57 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_57' AND SQL_TYPE='G2'";
	
	/**
	 * 营销票券回收率分析
	 */
	public static final String ENGINE_ANALYSIS_OF_THE_RATE_OF_RECOVERY_OF_SECURITIES_MARKETING57 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_57' AND SQL_TYPE='G3'";
	
	/**
	 * 营销客户响应率
	 */
	public static final String ENGINE_MARKETING_CUSTOMER_RESPONSE_RATE57 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_57' AND SQL_TYPE='G4'";
	
	/**
	 * 营销活动券撬动率
	 */
	public static final String ENGINE_COUPON_RATE_LEVERAGING_MARKETING_ACTIVITIES57 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_57' AND SQL_TYPE='G5'";
	
	/**
	 * 日分析
	 */
	public static final String ENGINE_CHANNEL_DAY_ANALYSIS57 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_57' AND SQL_TYPE='G6'";
	
	/**
	 * 周分析
	 */
	public static final String ENGINE_CHANNEL_WEEKLY_ANALYSIS57 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_57' AND SQL_TYPE='G7'";
	
	/**
	 * 月分析
	 */
	public static final String ENGINE_CHANNEL_ANALYSIS57 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_57' AND SQL_TYPE='G8'";
	
	
	
	
	/*************************************************************************SAAS_BI_2016_58**************************************************************************/
	 
	/**
	 * 营销活动概要报告
	 */
	public static final String ENGINE_MARKETING_CAMPAIGN_SUMMARYREPORT58 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_58' AND SQL_TYPE='G1'";
	
	/**
	 * 营销活动详细报告
	 */
	public static final String ENGINE_DETAILED_REPORT_ON_MARKETING_ACTIVITIES58 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_58' AND SQL_TYPE='G2'";
	
	/**
	 * 营销票券回收率分析
	 */
	public static final String ENGINE_ANALYSIS_OF_THE_RATE_OF_RECOVERY_OF_SECURITIES_MARKETING58 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_58' AND SQL_TYPE='G3'";
	
	/**
	 * 营销客户响应率
	 */
	public static final String ENGINE_MARKETING_CUSTOMER_RESPONSE_RATE58 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_58' AND SQL_TYPE='G4'";
	
	/**
	 * 营销活动券撬动率
	 */
	public static final String ENGINE_COUPON_RATE_LEVERAGING_MARKETING_ACTIVITIES58 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_58' AND SQL_TYPE='G5'";
	
	/**
	 * 日分析
	 */
	public static final String ENGINE_CHANNEL_DAY_ANALYSIS58 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_58' AND SQL_TYPE='G6'";
	
	/**
	 * 周分析
	 */
	public static final String ENGINE_CHANNEL_WEEKLY_ANALYSIS58 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_58' AND SQL_TYPE='G7'";
	
	/**
	 * 月分析
	 */
	public static final String ENGINE_CHANNEL_ANALYSIS58 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_58' AND SQL_TYPE='G8'";
	
	
	
	
	/*************************************************************************SAAS_BI_2016_59**************************************************************************/
	 
	/**
	 * 营销活动概要报告
	 */
	public static final String ENGINE_MARKETING_CAMPAIGN_SUMMARYREPORT59 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_59' AND SQL_TYPE='G1'";
	
	/**
	 * 营销活动详细报告
	 */
	public static final String ENGINE_DETAILED_REPORT_ON_MARKETING_ACTIVITIES59 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_59' AND SQL_TYPE='G2'";
	
	/**
	 * 营销票券回收率分析
	 */
	public static final String ENGINE_ANALYSIS_OF_THE_RATE_OF_RECOVERY_OF_SECURITIES_MARKETING59 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_59' AND SQL_TYPE='G3'";
	
	/**
	 * 营销客户响应率
	 */
	public static final String ENGINE_MARKETING_CUSTOMER_RESPONSE_RATE59 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_59' AND SQL_TYPE='G4'";
	
	/**
	 * 营销活动券撬动率
	 */
	public static final String ENGINE_COUPON_RATE_LEVERAGING_MARKETING_ACTIVITIES59 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_59' AND SQL_TYPE='G5'";
	
	/**
	 * 日分析
	 */
	public static final String ENGINE_CHANNEL_DAY_ANALYSIS59 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_59' AND SQL_TYPE='G6'";
	
	/**
	 * 周分析
	 */
	public static final String ENGINE_CHANNEL_WEEKLY_ANALYSIS59 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_59' AND SQL_TYPE='G7'";
	
	/**
	 * 月分析
	 */
	public static final String ENGINE_CHANNEL_ANALYSIS59 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_59' AND SQL_TYPE='G8'";
	
	
	
	
	/*************************************************************************SAAS_BI_2016_60**************************************************************************/
	 
	/**
	 * 营销活动概要报告
	 */
	public static final String ENGINE_MARKETING_CAMPAIGN_SUMMARYREPORT60 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_60' AND SQL_TYPE='G1'";
	
	/**
	 * 营销活动详细报告
	 */
	public static final String ENGINE_DETAILED_REPORT_ON_MARKETING_ACTIVITIES60 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_60' AND SQL_TYPE='G2'";
	
	/**
	 * 营销票券回收率分析
	 */
	public static final String ENGINE_ANALYSIS_OF_THE_RATE_OF_RECOVERY_OF_SECURITIES_MARKETING60 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_60' AND SQL_TYPE='G3'";
	
	/**
	 * 营销客户响应率
	 */
	public static final String ENGINE_MARKETING_CUSTOMER_RESPONSE_RATE60 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_60' AND SQL_TYPE='G4'";
	
	/**
	 * 营销活动券撬动率
	 */
	public static final String ENGINE_COUPON_RATE_LEVERAGING_MARKETING_ACTIVITIES60 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_60' AND SQL_TYPE='G5'";
	
	/**
	 * 日分析
	 */
	public static final String ENGINE_CHANNEL_DAY_ANALYSIS60 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_60' AND SQL_TYPE='G6'";
	
	/**
	 * 周分析
	 */
	public static final String ENGINE_CHANNEL_WEEKLY_ANALYSIS60 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_60' AND SQL_TYPE='G7'";
	
	/**
	 * 月分析
	 */
	public static final String ENGINE_CHANNEL_ANALYSIS60 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_60' AND SQL_TYPE='G8'";
	
	
	/*************************************************************************SAAS_BI_2016_64**************************************************************************/
	/**
	 * 菜品类别合并第一层
	 */
	public static final String ENGINE_THE_FIRST_LAYER_OF_THE_CATEGORY_OF_DISHES = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_64' AND SQL_TYPE='LB1'";
	
	
	/**
	 * 菜品类别合并第二层
	 */
	public static final String ENGINE_DISHES_CATEGORY_SMALL_CLASS_SECOND = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_64' AND SQL_TYPE='LB2'";
	
	/**
	 * 菜品类别合并合计
	 */
	public static final String ENGINE_FOOD_CATEGORY_CATEGORY_FIRST_TIER_TOTAL = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_64' AND SQL_TYPE='LB0'";
	
	/**
	 * 菜品类别大类查询
	 */
	public static final String ENGINE_CATEGORY_CATEGORY_SALES_CATEGORY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_64' AND SQL_TYPE='DL1'";
	
	/**
	 * 菜品类别大类合计
	 */
	public static final String ENGINE_CATEGORY_CATEGORY_SALES_CATEGORY_TOTAL = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_64' AND SQL_TYPE='DL0'";
	
	/**
	 * 菜品类别小类查询
	 */
	public static final String ENGINE_CATEGORY_SALES_CATEGORY_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_64' AND SQL_TYPE='XL1'";
	
	/**
	 * 菜品类别小类合计
	 */
	public static final String ENGINE_CATEGORY_SALES_CATEGORY_QUERY_TOTAL = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_64' AND SQL_TYPE='XL0'";
	
	
	/*************************************************************************SAAS_BI_2016_63**************************************************************************/
	/**
	 * 会员消费次数月报 按日
	 */
	public static final String ENGINE_MEMBERTRADING_NUMBER_DAY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_63' AND SQL_TYPE='RL1'";
	
	/**
	 * 会员消费次数月报 按周
	 */
	public static final String ENGINE_MEMBERTRADING_NUMBER_WEEK = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_63' AND SQL_TYPE='ZL1'";
	
 
	/**
	 * 会员消费次数月报 按月
	 */
	public static final String ENGINE_MEMBERTRADING_NUMBER_MONTH = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_63' AND SQL_TYPE='YL1'";
	
	/*************************************************************************SAAS_BI_2016_46**************************************************************************/
	/**
	 * 会员售卡趋势分析 按日
	 */
	public static final String ENGINE_MEMBERTRADING_NUMBER_DAY_46 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_46' AND SQL_TYPE='RL1'";
	
	/**
	 * 会员售卡趋势分析 按周
	 */
	public static final String ENGINE_MEMBERTRADING_NUMBER_WEEK_46 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_46' AND SQL_TYPE='ZL1'";
	
	/**
	 * 会员售卡趋势分析 按月
	 */
	public static final String ENGINE_MEMBERTRADING_NUMBER_MONTH_46 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_46' AND SQL_TYPE='YL1'";
	
	/*************************************************************************SAAS_DB_2016_62**************************************************************************/
	/**
	 * 会员操作分析报告 按日
	 */
	public static final String ENGINE_MEMBERTRADING_NUMBER_DAY_62 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_62' AND SQL_TYPE='RL1'";
	
	/**
	 * 会员操作分析报告 按周
	 */
	public static final String ENGINE_MEMBERTRADING_NUMBER_WEEK_62 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_62' AND SQL_TYPE='ZL1'";
	
	/**
	 * 会员操作分析报告 按月
	 */
	public static final String ENGINE_MEMBERTRADING_NUMBER_MONTH_62 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_62' AND SQL_TYPE='YL1'";
	
	/*************************************************************************SAAS_BI_2017_09**************************************************************************/
	/**
	 * 会员操作店间对比 按日
	 */
	public static final String ENGINE_MEMBERTRADING_NUMBER_DAY_09 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_09' AND SQL_TYPE='RL1'";
	
	/**
	 * 会员操作店间对比 按周
	 */
	public static final String ENGINE_MEMBERTRADING_NUMBER_WEEK_09 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_09' AND SQL_TYPE='ZL1'";
	
	/**
	 * 会员操作店间对比 按月
	 */
	public static final String ENGINE_MEMBERTRADING_NUMBER_MONTH_09 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_09' AND SQL_TYPE='YL1'";
	
	
	
	
	/*************************************************************************SAAS_BI_2017_10**************************************************************************/
	/**
	 * 外卖订单汇总查询按机构
	 */
	public static final String ENGINE_TAKEOUT_ORDER_SUMMARY_FIRST_LAYER_QUERY_MECHANISM = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_10' AND SQL_TYPE='JGMXL1'";
	
	/**
	 * 外卖查询日期订单汇总按机构
	 */
	public static final String ENGINE_TAKEOUT_ORDERS_SUMMARY_AGENCY_INQUIRY_SECOND = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_10' AND SQL_TYPE='JGMXL2'";
	
	/**
	 * 外卖订单汇总查询合计按机构
	 */
	public static final String ENGINE_DELIVERY_ORDER_SUMMARY_QUERY_MECHANISM_TOGETHER = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_10' AND SQL_TYPE='JGHJL0'";
	
	
	/**
	 * 外卖订单汇总查询按日期
	 */
	public static final String ENGINE_TAKEOUT_ORDER_SUMMARY_DATE_QUERY_FIRST_LAYER = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_10' AND SQL_TYPE='RQMXL1'";
	
	/**
	 * 外卖订单汇总查询按日期
	 */
	public static final String ENGINE_TAKEOUT_ORDERS_SUMMARY_DATE_QUERY_SECOND = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_10' AND SQL_TYPE='RQMXL2'";
	
	/**
	 * 外卖订单汇总查询合计按日期
	 */
	public static final String ENGINE_TAKEOUT_ORDERS_SUMMARY_DATE_INQUIRY_SECOND_TOTAL = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_10' AND SQL_TYPE='RQHJL0'";
	
	
	
	/*************************************************************************SAAS_BI_2017_11**************************************************************************/
	/**
	 * 外卖订单流水查询
	 */
	public static final String ENGINE_TAKEOUT_ORDER_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_11' AND SQL_TYPE='wmddlsL1'";
	
	/**
	 * 外卖订单流水查询合计
	 */
	public static final String ENGINE_TAKEOUT_ORDER_QUERY_TOTAL = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_11' AND SQL_TYPE='wmddlsL0'";
	
	/**
	 * 订单明细查询
	 */
	public static final String ENGINE_ORDER_DETAIL_INQUIRY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_11' AND SQL_TYPE='ddmxL1'";
	
	/**
	 * 订单明细查询合计
	 */
	public static final String ENGINE_ORDER_DETAIL_INQUIRY_TOTAL = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_11' AND SQL_TYPE='ddmxL0'";
	
	/**
	 * 订单优惠查询
	 */
	public static final String ENGINE_ORDER_DISCOUNT_INQUIRY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_11' AND SQL_TYPE='ddyhL1'";
	
	/**
	 * 订单优惠查询合计
	 */
	public static final String ENGINE_ORDER_DISCOUNT_INQUIRY_TOTAL = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_11' AND SQL_TYPE='ddyhL0'";
	
	/**
	 * 订单付款查询
	 */
	public static final String ENGINE_ORDER_PAYMENT_ENQUIRY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_11' AND SQL_TYPE='ddfkL1'";
	
	/**
	 * 订单付款查询合计
	 */
	public static final String ENGINE_ORDER_PAYMENT_ENQUIRY__TOTAL = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_11' AND SQL_TYPE='ddfkL0'";
	
	
	
	/*************************************************************************SAAS_BI_2017_12**************************************************************************/
	/**
	 * 外卖订单对账查询
	 */
	public static final String ENGINE_TAKEOUT_ORDERS_QUERY_RECONCILIATION = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_12' AND SQL_TYPE='wmdddzL1'";
	
	/**
	 * 外卖订单对账查询合计
	 */
	public static final String ENGINE_TAKEOUT_ORDERS_QUERY_RECONCILIATION_TOTAL = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_12' AND SQL_TYPE='wmdddzL0'";
	
	/**
	 * 外卖订单明细查询
	 */
	public static final String ENGINE_TAKE_OUT_ORDER_DETAILS = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_12' AND SQL_TYPE='wmddmxL1'";
	
	/**
	 * 外卖订单明细查询合计
	 */
	public static final String ENGINE_TAKE_OUT_ORDER_DETAILS_TOTAL = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_12' AND SQL_TYPE='wmddmxL0'";
	
	/**
	 * 外卖订单优惠查询
	 */
	public static final String ENGINE_TAKEOUT_ORDERS_PREFERENTIAL_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_12' AND SQL_TYPE='wmddyhL1'";
	
	/**
	 * 外卖订单优惠查询合计
	 */
	public static final String ENGINE_TAKEOUT_ORDERS_PREFERENTIAL_QUERY_TOTAL = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_12' AND SQL_TYPE='wmddyhL0'";
	
	/**
	 * 外卖订单付款查询
	 */
	public static final String ENGINE_TAKEOUT_ORDERS_PAYMENT_INQUIRY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_12' AND SQL_TYPE='wmddfkL1'";
	
	/**
	 * 外卖订单付款查询合计
	 */
	public static final String ENGINE_TAKEOUT_ORDERS_PAYMENT_INQUIRY_TOTAL = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_12' AND SQL_TYPE='wmddfkL0'";
	
	
	
	/*************************************************************************SAAS_BI_2017_13**************************************************************************/
	/**
	 * 营业汇总实时查询(竖版)
	 */
	public static final String ENGINE_BUSINESS_SUMMARY_REAL_TIME_DINNER_FAST_QUERY = "select * from f_tab_pos_realtime_yyhz('%p_report_date%','%p_store_id%','%p_report_type%')";
	
	
	/*************************************************************************SAAS_BI_2017_14**************************************************************************/
	/**
	 * 营业班次实时查询(竖版)
	 */
	public static final String ENGINE_BUSINESS_HOURS_REAL_TIME_DINNER_FAST_QUERY = "select * from f_tab_pos_realtime_yyhz('%p_report_date%','%p_store_id%','%p_shift_id%','%p_report_type%')";
	
	/*************************************************************************SAAS_BI_2017_15**************************************************************************/
	/**
	 * 营业时段实时查询(竖版)
	 */
	public static final String ENGINE_REAL_TIME_MEALS_DURING_BUSINESS_HOURS = "select * from f_tab_pos_realtime_yyhz('%p_report_date_begin%','%p_report_time_begin%','%p_report_date_end%','%p_report_time_end%','%p_store_id%','%p_report_type%')";
	
 
	
	/*************************************************************************SAAS_BI_2016_17**************************************************************************/
	/**
	 * 账单流水查询
	 */
	public static final String ENGINE_BILLING_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_17' AND SQL_TYPE='ZDLSL1'";
	
	/**
	 * 账单流水查询合计
	 */
	public static final String ENGINE_TOTAL_BILLING_INQUIRIES = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_17' AND SQL_TYPE='ZDLSL0'";
	
	/**
	 * 账单明细
	 */
	public static final String ENGINE_BILL_DETAILS = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_17' AND SQL_TYPE='ZDMXL1'";
	
	/**
	 * 账单明细合计
	 */
	public static final String ENGINE_TOTAL_BILL_DETAILS = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_17' AND SQL_TYPE='ZDMXL0'";
	
	
	/**
	 * 账单付款
	 */
	public static final String ENGINE_BILL_PAYMENT = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_17' AND SQL_TYPE='ZDFKL1'";
	
	/**
	 * 账单付款合计
	 */
	public static final String ENGINE_TOTAL_BILL_PAYMENT = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_17' AND SQL_TYPE='ZDFKL0'";
	
	/**
	 * 账单流水查询 查询条件为 售卖类型
	 */
	public static final String ENGINE_BILLING_QUERY_SALEMODE = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_40' AND SQL_TYPE='queryListL1'";
	
	/*************************************************************************SAAS_BI_2017_34**************************************************************************/
	/**
	 * 日营业指标综合分析
	 */
	public static final String ENGINE_DAY_COMPREHENSIVE_ANALYSIS_OF_BUSINESS_INDICATORS = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_34' AND SQL_TYPE='DAYL1'";
	
	/**
	 * 周营业指标综合分析
	 */
	public static final String ENGINE_WEEK_COMPREHENSIVE_ANALYSIS_OF_BUSINESS_INDICATORS = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_34' AND SQL_TYPE='WEEKL2'";
	
	/**
	 * 月营业指标综合分析
	 */
	public static final String ENGINE_MONTH_COMPREHENSIVE_ANALYSIS_OF_BUSINESS_INDICATORS = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_34' AND SQL_TYPE='MONTHL3'";
	
	
	
	/*************************************************************************SAAS_BI_2017_39**************************************************************************/
	/**
	 * 恢复账单明细查询
	 */
	public static final String ENGINE_RESTORE_BILLING_DETAILS_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_39' AND SQL_TYPE='L1'";
	
	/**
	 * 恢复账单汇总查询
	 */
	public static final String ENGINE_RESTORE_BILLING_SUMMARY_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_39' AND SQL_TYPE='L2'";
	
	/**
	 * 恢复账单菜品明细查询
	 */
	public static final String ENGINERESUME_BILLS_DISHES_DETAILS_INQUIRIES_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_39' AND SQL_TYPE='L3'";
	
	/**
	 * 恢复账单菜品名称查询
	 */
	public static final String ENGINE_RESUME_BILLING_DISHES_INQUIRIES_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_39' AND SQL_TYPE='L4'";
	
	/**
	 * 恢复账单付款方式查询
	 */
	public static final String ENGINE_RESUME_BILLING_DETAIL_INQUIRIES_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_39' AND SQL_TYPE='L5'";
	
	
	
	
	/*************************************************************************SAAS_BI_2017_44**************************************************************************/
	/**
	 * 第三方支付查询
	 */
	public static final String ENGINE_THIRD_PARTY_PAYMENT_RECONCILIATION_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_44' AND SQL_TYPE='L1'";
	
	/**
	 * 第三方支付合计
	 */
	public static final String ENGINE_TOTAL_THIRD_PARTY_PAYMENT_RECONCILIATION_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_44' AND SQL_TYPE='L0'";
	
	
	/*************************************************************************SAAS_BI_2017_47**************************************************************************/
	/**
	 * 营业销售对比分析
	 */
	public static final String ENGINE_COMPARATIVE_ANALYSIS_OF_BUSINESS_SALES_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_47' AND SQL_TYPE='L1'";
	
	/**
	 * 营业销售对比分析合计
	 */
	public static final String ENGINE_TOTAL_COMPARATIVE_ANALYSIS_OF_BUSINESS_SALES_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_47' AND SQL_TYPE='L0'";
	
	/**
	 * 营业销售图形查询
	 */
	public static final String ENGINE_BUSINESS_TRADITION_SALES_INQUIRY_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_47' AND SQL_TYPE='L2'";
	
	
	/*************************************************************************SAAS_BI_2017_48**************************************************************************/
	/**
	 * 销售模式对比分析
	 */
	public static final String ENGINE_COMPARATIVE_ANALYSIS_OF_SALES_MODEL_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_48' AND SQL_TYPE='L1'";
	
	/**
	 * 销售模式对比分析合计
	 */
	public static final String ENGINE_TOTAL_COMPARATIVE_ANALYSIS_OF_SALES_MODEL_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_48' AND SQL_TYPE='L0'";
	
	/**
	 * 销售模式堂外卖食查询
	 */
	public static final String ENGINE_DINE_IN_QUERY_SALES_MODEL_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_48' AND SQL_TYPE='L2'";
	
	/*************************************************************************SAAS_BI_2017_49**************************************************************************/
	/**
	 * 传统网络销售分析
	 */
	public static final String ENGINE_TRADITIONAL_NETWORK_SALES_ANALYSIS_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_49' AND SQL_TYPE='L1'";
	
	/**
	 * 传统网络销售分析合计
	 */
	public static final String ENGINE_TOTAL_RADITIONAL_NETWORK_SALES_ANALYSIS_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_49' AND SQL_TYPE='L0'";
	
	/**
	 * 传统销售查询
	 */
	public static final String ENGINE_TRADITION_SALES_INQUIRY_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_49' AND SQL_TYPE='L2'";
	
	
	/*************************************************************************SAAS_BI_2017_54**************************************************************************/
	/**
	 * 菜品菜品班次同比环比分析
	 */
	public static final String ENGINE_NUMBER_OF_DISHES_YEAR_ON_YEAR_ANALYSIS = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_54' AND SQL_TYPE='L1'";
	
	
	/**
	 * 菜品班次同比环比分析合计
	 */
	public static final String ENGINE_TOTAL_NUMBER_OF_DISHES_YEAR_ON_YEAR_ANALYSIS = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_54' AND SQL_TYPE='L0'";
	
	
	
	/*************************************************************************SAAS_BI_2017_56**************************************************************************/
	/**
	 * 多层级通用机构参数
	 */
	public static final String ENGINE_MULTI_LEVEL_GENERAL_AGENCY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_56' AND SQL_TYPE='L1'";
	
	/*************************************************************************SAAS_BI_2017_58**************************************************************************/
	/**
	 * 餐厅日报
	 */
	public static final String ENGINE_RESTAURANT_DAILY_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_58' AND SQL_TYPE='L1'";
	
	
	/*************************************************************************SAAS_BI_2017_63**************************************************************************/
	/**
	 * 能源消耗汇总报表第一层
	 */
	public static final String ENGINE_THE_ENERGY_CONSUMPTION_REPORT_OF_THE_FIRST_LAYER = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_63' AND SQL_TYPE='nyxhL1'";
	
	/**
	 * 能源消耗汇总报表第二层
	 */
	public static final String ENGINE_ENERGY_CONSUMPTION_SUMMARY_REPORT_SECOND = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_63' AND SQL_TYPE='nyxhL2'";
	
	/**
	 * 能源消耗汇总报表第三层
	 */
	public static final String ENGINE_ENERGY_CONSUMPTION_SUMMARY_REPORT_THIRD = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_63' AND SQL_TYPE='nyxhL3'";
	
	/**
	 * 能源消耗汇总报表合计
	 */
	public static final String ENGINE_TOTAL_THE_TOTAL_ENERGY_CONSUMPTION_IN_THE_SUMMARY_REPORT = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_63' AND SQL_TYPE='nyxhL0'";
	
	
	
	
	/*************************************************************************POS_BI_2017_01**************************************************************************/
	/**
	 * POS菜品类别大类
	 */
	public static final String ENGINE_POS_THE_FIRST_LAYER_OF_THE_CATEGORY_OF_DISHES = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'POS_BI_2017_01' AND SQL_TYPE='DL1'";
	
	/**
	 * POS菜品类别小类
	 */
	public static final String ENGINE_POS_DISHES_CATEGORY_SMALL_CLASS_SECOND = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'POS_BI_2017_01' AND SQL_TYPE='XL1'";
	
	
	/*************************************************************************POS_BI_2017_02**************************************************************************/
	/**
	 * POS菜品销售实时查询
	 */
	public static final String ENGINE_POS_FOOD_SALES_SUMMARY_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'POS_BI_2017_02' AND SQL_TYPE='CPHZL1'";
	
	
	/*************************************************************************POS_BI_2017_03**************************************************************************/
	/**
	 * POS套餐销售汇总查询
	 */
	public static final String ENGINE_POS_PACKAGE_SALES_SUMMARY_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'POS_BI_2017_03' AND SQL_TYPE='TCHZL1'";
	
	
	/*************************************************************************POS_BI_2017_04**************************************************************************/
	/**
	 * POS菜品销售实时查询
	 */
	public static final String ENGINE_POS_REAL_TIME_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'POS_BI_2017_04' AND SQL_TYPE='CPSSL1'";
	
	/*************************************************************************POS_BI_2017_05**************************************************************************/
	/**
	 * POS营业餐位查询
	 */
	public static final String ENGINE_POS_BUSINESS_LUNCH_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'POS_BI_2017_05' AND SQL_TYPE='YYCWL1'";
	
	/*************************************************************************POS_BI_2017_06**************************************************************************/
	/**
	 * POS营业区域查询
	 */
	public static final String ENGINE_POS_BUSINESS_AREA_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'POS_BI_2017_06' AND SQL_TYPE='YYQYL1'";
	
	/*************************************************************************POS_BI_2017_07**************************************************************************/
	/**
	 * POS营业桌位查询
	 */
	public static final String ENGINE_POS_BUSINESS_TABLE_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'POS_BI_2017_07' AND SQL_TYPE='YYZWL1'";
	
	/*************************************************************************POS_BI_2017_08**************************************************************************/
	/**
	 * POS会员交易流水查询
	 */
	public static final String ENGINE_POS_MEMBER_TRANSACTION_QUERY= "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'POS_BI_2017_08' AND SQL_TYPE='HYJYL1'";
	
	/*************************************************************************POS_BI_2017_09**************************************************************************/
	/**
	 * POS会员积分流水查询第一级
	 */
	public static final String ENGINE_POS_MEMBER_INTEGRAL_QUERY = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'POS_BI_2017_09' AND SQL_TYPE='HYJFL1'";
	
	/**
	 * POS会员积分流水查询第二级
	 */
	public static final String ENGINE_POS_MEMBER_INTEGRAL_QUERY_SECONDLEVEL = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'POS_BI_2017_09' AND SQL_TYPE='HYJFL2'";

	/*************************************************************************SCM_BI_2017_02**************************************************************************/

	/**
	 * 要货汇总表查询
	 */
	public static final String ENGINE_SCM_ORGAN_DEMAND_REPORT_QUERY = "select sql from saas_report_engine where report_num = 'SCM_BI_2017_01' and sql_type='JGYHL0'";

	
	/**
	 * 配送出库表查询
	 */
	public static final String ENGINE_SCM_DISTRIBUTION_DELIVERY_REPORT_QUERY = "select sql from saas_report_engine where report_num = 'SCM_BI_2017_02' and sql_type='PSCKL0'";

	/*************************************************************************SAAS_BI_2016_35**************************************************************************/
	/**
	 * 会员交易分析报告 按日
	 */
	public static final String ENGINE_MEMBERTRADING_NUMBER_DAY_35 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_35' AND SQL_TYPE='RL1'";
	
	/**
	 * 会员交易分析报告 按周
	 */
	public static final String ENGINE_MEMBERTRADING_NUMBER_WEEK_35 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_35' AND SQL_TYPE='ZL1'";
	
	/**
	 * 会员交易分析报告 按月
	 */
	public static final String ENGINE_MEMBERTRADING_NUMBER_MONTH_35 = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2016_35' AND SQL_TYPE='YL1'";

	/*************************************************************************SAAS_BI_2017_51**************************************************************************/
	/**
	 * 菜品销售汇总分析
	 */
	public static final String ENGINE_ANALYSIS_OF_SALES_OF_VEGE_TABLES = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_51' AND SQL_TYPE='CPL1'";

	/**
	 * 菜品销售汇总分析
	 */
	public static final String ENGINE_TOTAL_ENGINE_ANALYSIS_OF_SALES_OF_VEGE_TABLES = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_51' AND SQL_TYPE='CPL0'";

	/*************************************************************************SAAS_BI_2017_55**************************************************************************/
	/**
	 * 菜品销售时段分析
	 */
	public static final String ENGINE_ANALYSIS_OF_DISHES_TABLES = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_55' AND SQL_TYPE='L1'";

	/**
	 * 菜品销售时段分析
	 */
	public static final String ENGINE_TOTAL_ANALYSIS_OF_DISHES_TABLES = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_55' AND SQL_TYPE='L0'";

	/*************************************************************************SAAS_BI_2017_52**************************************************************************/
	/**
	 * 菜品销售结构分析
	 */
	public static final String ENGINE_ANALYSIS_OF_SALES_STRUCTURE_OF_FOOD_TABLES = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_52' AND SQL_TYPE='L1'";

	/**
	 * 菜品销售结构分析
	 */
	public static final String ENGINE_TOTAL_ANALYSIS_OF_SALES_STRUCTURE_OF_FOOD_TABLES = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_52' AND SQL_TYPE='L0'";

    /*************************************************************************SAAS_BI_2017_50**************************************************************************/
    /**
     * 支付方式明细分析
     */
    public static final String ENGINE_PAYMENT_METHOD_DETAIL_ANALYSIS_TABLES = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_50' AND SQL_TYPE='L1'";

    /**
     * 支付方式明细分析
     */
    public static final String ENGINE_TOTAL_PAYMENT_METHOD_DETAIL_ANALYSIS_TABLES = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_50' AND SQL_TYPE='L0'";


    /**
     * 支付方式明细分析(图)
     */
    public static final String ENGINE_MAP_PAYMENT_METHOD_DETAIL_ANALYSIS_TABLES = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_50' AND SQL_TYPE='L0'";

    /**
     * 菜品估清
     */

    public static final String ENGINE_MAP_PAYMENT_METHOD_DETAIL_Food_Evaluation = "SELECT SQL FROM SAAS_REPORT_ENGINE WHERE REPORT_NUM = 'SAAS_BI_2017_65' AND SQL_TYPE='L1'";
    

}
