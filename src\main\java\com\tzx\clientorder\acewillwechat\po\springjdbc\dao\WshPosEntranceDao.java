package com.tzx.clientorder.acewillwechat.po.springjdbc.dao;

import java.util.List;

import net.sf.json.JSONObject;

import com.tzx.framework.common.util.dao.GenericDao;

public interface WshPosEntranceDao extends GenericDao
{
	String	NAME	= "com.tzx.clientorder.acewillwechat.po.springjdbc.dao.imp.WshPosEntranceDaoImp";

	public List<JSONObject> selectDishKinds(String tenancy_id, String store_id) throws Exception;

	/**
	 * @param tenancy_id
	 * @param store_id
	 * @param chanel
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> selectDishKinds(String tenancyId, Integer storeId, String chanel) throws Exception;

	public List<JSONObject> selectTables(String tenancy_id, String store_id) throws Exception;

	public List<JSONObject> selectShops(String tenancy_id, String store_id)throws Exception;

	public List<JSONObject> selectMemo(String tenancy_id, String store_id)throws Exception;

	public JSONObject selectOrganInfo(String tenancy_id, String store_id)throws Exception;

	public List<JSONObject> selectItemInfos(String tenancy_id, String store_id)throws Exception;

	public List<JSONObject> selectUnitInfos(String tenancy_id, int[] item_ids, String price_system)throws Exception;

	public List<JSONObject> selectMethodInfos(String tenancy_id, int[] item_ids)throws Exception;

	public List<JSONObject> selectComboBaseInfo(String tenancy_id, int[] combo_item_ids, String price_system)throws Exception;

	public List<JSONObject> selectMaindishDetails(String tenancy_id, int[] maindishCombIds)throws Exception;

	public List<JSONObject> selectMandatoryBaseInfo(String tenancy_id, int[] mandatoryCombIds)throws Exception;

	public List<JSONObject> selectGroupDetails(String tenancy_id, int[] groupIds)throws Exception;

	public List<JSONObject> selectStoreParams(String tenancy_id, int storeId, String[] paraCodes)throws Exception;

	public JSONObject selectStoreInfo(String tenancy_id) throws Exception;

	public JSONObject selectUnclosedBill(String tenantId, int store_id, String tableCode) throws Exception;

	public JSONObject findLockInfoByBillNum(String tenantId, int store_id,
											String bill_num) throws Exception;

	public JSONObject selectBillByNo(String tenantId, int store_id, String billno) throws Exception;

	public List<JSONObject> selectDishKindParents(String tenancy_id, String store_id,
												  int[] class_ids) throws Exception;

	public List<JSONObject> selectDetailsInfo(String tenancy_id, int[] combo_item_ids) throws Exception;

	/**
	 * 根据单品id，查询菜品信息
	 * @param tenancy_id
	 * @param normIds
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> getItemInfos(String tenancy_id, String normIds) throws Exception;

}
