/** 
 * @(#)CodeFormat.java    1.0   2017-06-22
 * 
 * Copyright 北京普照天星科技有限公司. All rights reserved. 
 * PROPRIETARY/CONFIDENTIAL. Use is subject to license terms. 
 */
package com.tzx.pos.asyn.thread;

import java.util.List;
import org.apache.log4j.Logger;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.pos.asyn.service.PosPrintNewThreadService;
import com.tzx.pos.asyn.service.PosPrintThreadService;
import com.tzx.pos.base.constant.FunctionCode;
import com.tzx.pos.base.util.SpringContextUtils;

import net.sf.json.JSONObject;

/**
 * 下单接口打印功能线程处理类（开台操作）.
 *
 * <AUTHOR> email:<EMAIL>
 * @version 1.0  2017-06-22
 * @see     
 * @since   JDK7.0
 * @update  
 */
public class OpenTablePrintThread implements Runnable {
	
	private static final Logger	logger	= Logger.getLogger(OpenTablePrintThread.class);
	private PosPrintThreadService	posPrintThreadService = null;       // pos打印服务
 	private PosPrintNewThreadService posPrintNewThreadService = null;   // pos新打印服务	
	private JSONObject json = null;                                     // request请求参数
	
	public OpenTablePrintThread(JSONObject json){
		this.json = json;
	}

	@Override
	public void run() {
		try
		{
			// 获取Spring容器中的bean对象。				
			posPrintNewThreadService = (PosPrintNewThreadService)SpringContextUtils.getBean("com.tzx.pos.asyn.service.PosPrintNewThreadServiceImp");				
			posPrintThreadService = (PosPrintThreadService)SpringContextUtils.getBean("com.tzx.pos.asyn.service.PosPrintThreadServiceImp");
			
			long t1 = System.currentTimeMillis();
			if (json != null)
			{
				if (json.optString("mode").equalsIgnoreCase("0"))
				{
					if (json.optString("is_print").equalsIgnoreCase("Y"))
					{
						if(posPrintNewThreadService.isNONewPrint(json.optString("tenancy_id"),json.optInt("store_id"))){//如果启用新的打印模式
							if(json.containsKey("rwids") && json.getString("rwids").length() > 0)
							{
								posPrintNewThreadService.posPrintByFunction(json.optString("tenancy_id"), 
										json.optInt("store_id"),
										FunctionCode.ORDERING,
										json);
							}
						}
						else
						{
							List<Integer> list = posPrintThreadService.orderChef(json.optString("tenancy_id"), json.optString("bill_num"), json.optInt("store_id"), "0");
							posPrintThreadService.orderPrint(json.optString("tenancy_id"), json.optString("bill_num"), json.optInt("store_id"), list);
						}
					}
				}
			}
			long t2 = System.currentTimeMillis();
			String runTime = String.valueOf(t2 - t1);
		}
		catch (Exception e)
		{
			logger.error(ExceptionMessage.getExceptionMessage(e));
		}

	}

}
