package com.tzx.framework.common.service;

import java.util.List;

import net.sf.json.JSONObject;

public interface GenericManager
{

	/**
	 * 查询全部基本配置信息
	 * 
	 * @param tenantId
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> findBasicConfig(String tenantId) throws Exception;

	/**
	 * 查询指定基本配置信息
	 * 
	 * @param tenantId
	 * @param keyName
	 * @return
	 * @throws Exception
	 */
	String findBasicConfig(String tenantId, String keyName) throws Exception;

}
