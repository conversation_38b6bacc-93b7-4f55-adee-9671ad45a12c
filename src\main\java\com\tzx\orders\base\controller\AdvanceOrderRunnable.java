package com.tzx.orders.base.controller;

import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.Tools;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.orders.base.util.OrderUtil;
import com.tzx.orders.bo.OrdersManagementService;
import net.sf.json.JSONObject;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.log4j.Logger;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
  * @Description: 外卖预订单提醒
  * @author: shenzhanyu
  * @version: 1.0
  * @since: JDK7.0
  * @date: 2018年11月28日
  * @update:
 */
public class AdvanceOrderRunnable implements Runnable{
	
	private static final Logger	logger	= Logger.getLogger(AdvanceOrderRunnable.class);

	@Override
	public void run() {
		String tenancyId = null;
		if (Constant.systemMap.containsKey("tenent_id"))
		{
			tenancyId = Constant.systemMap.get("tenent_id");
		}

		Integer storeId = Integer.valueOf(Constant.systemMap.get("store_id"));
		if (Constant.systemMap.containsKey("store_id"))
		{
			storeId = Integer.valueOf(Constant.systemMap.get("store_id"));
		}

		if (Tools.isNullOrEmpty(tenancyId) || Tools.isNullOrEmpty(storeId))
		{
			logger.info("参数为空");
			return;
		}

		try{
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
			Calendar calendar = Calendar.getInstance();
			calendar.add(Calendar.DATE,-10);
			String single_time1 = sdf.format(calendar.getTime())+" 00:00:00";
			String single_time2= sdf.format(new Date()) +" 23:59:59";

			String paraValue = OrderUtil.getSysPara(tenancyId, storeId, "waimai_bookorder_switch"); // 查询提醒参数
			if (Tools.hv(paraValue)) {
				String[] paras = paraValue.split(";");
				String isEnable = paras[0];// 是否启动 1:开启，0关闭
				if (isEnable.equals("1")) {
					DBContextHolder.setTenancyid(tenancyId);
					OrdersManagementService ordersService = (OrdersManagementService) SpringConext.getApplicationContext().getBean(OrdersManagementService.NAME);
					List<JSONObject> orders  =ordersService.findAdvanceOrders(tenancyId, storeId, single_time1,single_time2);
					if(orders.size()>0){
						for(JSONObject object:orders){
							ordersService.advanceOrdersOfPrintRemind(tenancyId, storeId, object);
						}
					}
				}
			}
		}catch (Exception e){
			logger.error(e.getMessage());
		}
	}
}
