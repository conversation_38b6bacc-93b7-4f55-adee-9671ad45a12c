package com.tzx.base.po.springjdbc.dao.imp;

import com.tzx.base.constant.CacheTableConstant;
import com.tzx.base.po.springjdbc.dao.CacheManagerDao;
import com.tzx.framework.common.util.dao.impl.GenericDaoImpl;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * Created by kevin on 2017-06-24.
 */
@Repository(CacheManagerDao.NAME)
public class CacheManagerDaoImp extends GenericDaoImpl implements CacheManagerDao {

    /**
     * 获取业务数据
     * @param cacheTableConstant
     * @param tenancyId
     * @param storeId
     * @return
     * @throws Exception
     */
    @Override
    public List<Map<String, Object>> getBusinessDataByStoreId(CacheTableConstant cacheTableConstant, String tenancyId, int storeId) throws Exception {
        StringBuilder sqlBuffer = new StringBuilder(cacheTableConstant.getSql(tenancyId, storeId));
        return this.jdbcTemplate.queryForList(sqlBuffer.toString());
    }
}
