package com.tzx.base.constant;

import java.util.HashMap;
import java.util.Map;

/**
 * 
 * <AUTHOR> 2015-5-8-下午2:01:49
 *
 **/

public class Constant
{
	// code的值
	public static final int				CODE_SUCCESS					= 0;								// 成功
	public static final int				CODE_PARAM_FAILURE				= 1;								// 参数失败
	public static final int				CODE_AUTH_FAILURE				= 2;								// 认证失败
	public static final int				CODE_NULL_DATASET				= 3;								// 数据集为空
	public static final int				CODE_CHANGE_DATASOURCE_FAILURE	= 4;								// 切换数据源失败
	public static final int				CODE_INNER_EXCEPTION			= 5;								// 内部错误
	public static final int				CODE_STORE_EXCEPTION			= 6;								// 门店状态异常
	public static final int				CODE_CONN_EXCEPTION				= 99;								// 连接异常

	public static final String			CODE_CONN_EXCEPTION_MSG			= "网络请求失败";						// 连接异常
	public static final String			POS_CHANGE_DATASOURCE_FAILURE	= "切换数据源失败";
	public static final String			INNER_ERROR						= "内部错误";

	public static final String			QUERY_ORGAN_STATUS_FAILURE		= "查询门店状态失败";

	public static final String			NOT_EXISTS_TYPE					= "未找到对应的type";
	public static final String			NOT_EXISTS_OPER					= "未找到对应的oper";

	public static Map<String, String>	constantMap						= new HashMap<String, String>();

	public static Map<String, String> getConstantMap()
	{
		return constantMap;
	}

	public static void setSystemMap(Map<String, String> constantMap)
	{
		Constant.constantMap = constantMap;
	}
}
