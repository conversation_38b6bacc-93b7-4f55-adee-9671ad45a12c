package com.tzx.pos.po.springjdbc.dao.imp;

import java.util.ArrayList;
import java.util.List;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Repository;

import com.tzx.base.entity.PosOptState;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.dao.imp.BaseDaoImp;
import com.tzx.pos.po.springjdbc.dao.PosRegisterDao;

@Repository(PosRegisterDao.NAME)
public class PosRegisterDaoImp extends BaseDaoImp implements PosRegisterDao 
{

	@Override
	public JSONObject getSysBillCodeRule(String tenancyId, String billCode) throws Exception
	{
		String querySql = new String("select cr.max_length,cr.perfix_1,cr.perfix_2,cr.perfix_3,cr.serial_length from sys_bill_code_rule cr where cr.tenancy_id=? and cr.bill_code=?");
		List<JSONObject> list = this.query4Json(tenancyId, querySql, new Object[]
		{ tenancyId, billCode });
		if (null != list && list.size() > 0)
		{
			return list.get(0);
		}
		return null;
	}

	@Override
	public void updateSysCodeValues(String tenancyId, int storeId, String billCode, String prefix, Integer currentValue) throws Exception
	{
		String updateSql = new String("update sys_code_values set current_value=? where store_id=? and code_type=? and prefix=?");
		int ret = this.update(updateSql, new Object[]
		{ currentValue, storeId, billCode, prefix });

		if (ret <= 0)
		{
			String insertSql = new String("insert into sys_code_values(tenancy_id,store_id,code_type,prefix,current_value) values(?,?,?,?,?)");
			this.update(insertSql, new Object[]
			{ tenancyId, storeId, billCode, prefix, currentValue });
		}
	}

	@Override
	public void batchInsertPosOptState(String tenancyId, int storeId, List<PosOptState> optStateList) throws Exception
	{
		List<Object[]> batchArgs = new ArrayList<Object[]>();
		if (null != optStateList && optStateList.size() > 0)
		{
			for (PosOptState optState : optStateList)
			{
				batchArgs.add(new Object[]
				{ tenancyId, storeId, optState.getReport_date(), optState.getShift_id(), optState.getContent(), optState.getPos_num(), optState.getOpt_num(), optState.getOpt_name(), optState.getManager_num(), optState.getLast_updatetime(), optState.getTag(), optState.getLogin_number(),
						optState.getRemark(), optState.getUpload_tag() });
			}
		}

		if (batchArgs.size() > 0)
		{
			String insertSql = new String("insert into pos_opt_state(tenancy_id,store_id,report_date,shift_id,content,pos_num,opt_num,opt_name,manager_num,last_updatetime,tag,login_number,remark,upload_tag) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
			this.batchUpdate(insertSql, batchArgs);
		}
	}

	@Override
	public int getPosOptStateForDayBegainCount(String tenancyId, int storeId) throws Exception
	{
		String querySql = new String("select count(*) num from pos_opt_state where tenancy_id=? and store_id=? and content = ?");
		return this.queryForInt(querySql, new Object[]
		{ tenancyId, storeId, SysDictionary.OPT_STATE_DAYBEGAIN });
	}
}
