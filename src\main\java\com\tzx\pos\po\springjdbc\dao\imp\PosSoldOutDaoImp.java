package com.tzx.pos.po.springjdbc.dao.imp;

import java.util.Date;
import java.util.List;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Repository;

import com.tzx.framework.common.exception.SystemException;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.dao.imp.BaseDaoImp;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.po.springjdbc.dao.PosSoldOutDao;

@Repository(PosSoldOutDao.NAME)
public class PosSoldOutDaoImp extends BaseDaoImp implements PosSoldOutDao
{
	@Override
	public List<JSONObject> getSoldOutListBySoldOutType(String tenancyId, Integer storeId, String soldOutType) throws Exception
	{
		return this.query4Json(tenancyId, "select item_id,num,coalesce(item_unit_id,0) as item_unit_id,to_char(setdate,'yyyy-mm-dd') as setdate,soldout_type,chanel from pos_soldout where tenancy_id=? and store_id = ? and soldout_type = ? ", new Object[]
		{ tenancyId, storeId, soldOutType });
	}

	@Override
	public int deleteSoldOutBySoldOutType(String tenancyId, Integer storeId, String soldOutType) throws Exception
	{
		StringBuilder dsql = new StringBuilder("delete from pos_soldout where tenancy_id=? and store_id = ? and soldout_type = ?");
		return this.update(dsql.toString(), new Object[]
		{ tenancyId, storeId, soldOutType });
	}

	@Override
	public int[] batchInsertSoldOut(String tenancyId, Integer storeId, List<Object[]> soldOutList) throws Exception
	{
		// 插入沽清，包含以前已经存在的沽清
		StringBuilder sql = new StringBuilder(" insert into pos_soldout (tenancy_id,store_id,item_id,num,count,setdate,report_date,last_operator,last_updatetime,soldout_type,chanel) values (?,?,?,?,?,?,?,?,?,?,?)");
		if (0 < soldOutList.size())
		{
			return this.batchUpdate(sql.toString(), soldOutList);
		}
		return null;
	}

	@Override
	public List<JSONObject> getItemGroupForSoldOutByItemIds(String tenancyId, Integer storeId, String itemIds) throws Exception
	{
		if (null != itemIds && 0 < itemIds.length())
		{
			StringBuffer sql = new StringBuffer();
			sql.append("select gd.item_group_id,gd.item_id,gd.item_unit_id from hq_item_group_details gd ");
			sql.append("left join hq_item_group g on gd.tenancy_id=g.tenancy_id and gd.item_group_id=g.id ");
			sql.append("where g.tenancy_id=? and g.valid_state='1' ");
			sql.append("and g.id in (select item_group_id from hq_item_group_details where tenancy_id=? and item_id in (").append(itemIds).append("))");
			return this.query4Json(tenancyId, sql.toString(), new Object[]
			{ tenancyId, tenancyId });
		}
		return null;
	}

	@Override
	public List<JSONObject> getItemComboForSoldOutByItemIds(String tenancyId, Integer storeId, String itemIds) throws Exception
	{
		if (null != itemIds && 0 < itemIds.length())
		{
			StringBuffer sql = new StringBuffer();
			sql.append("select cd.iitem_id,cd.is_itemgroup,cd.details_id,cd.combo_num,cd.item_unit_id from hq_item_combo_details cd ");
			sql.append("left join hq_item_info hi on cd.tenancy_id=hi.tenancy_id and cd.iitem_id=hi.id ");
			sql.append("where hi.tenancy_id =? and hi.valid_state='1' and cd.valid_state='1' ");
			sql.append("and hi.id in (select iitem_id from hq_item_combo_details where tenancy_id =? and valid_state='1' and details_id in (").append(itemIds).append("))");
			return this.query4Json(tenancyId, sql.toString(), new Object[]
			{ tenancyId, tenancyId });
		}
		return null;
	}

	@Override
	public List<JSONObject> findSoldOut(String tenancyId, Integer storeId) throws Exception
	{
		StringBuilder sql = new StringBuilder("select item_id,num,coalesce(item_unit_id,0) as item_unit_id,to_char(setdate,'yyyy-mm-dd') as setdate,soldout_type,coalesce(chanel,'"+SysDictionary.SOLDOUT_CHANNEL_SET+"') as soldout_chanel from pos_soldout where tenancy_id =? and store_id =?");
		return this.queryString4Json(tenancyId, sql.toString(), new Object[]
		{ tenancyId, storeId });
	}
	
	@Override
	public List<JSONObject> getSoldOut(String tenancyId, Integer storeId) throws Exception
	{
		StringBuilder sql = new StringBuilder("select item_id,num,coalesce(item_unit_id,0) as item_unit_id,to_char(setdate,'yyyy-mm-dd') as setdate,soldout_type,chanel from pos_soldout where tenancy_id =? and store_id =?");
		return this.queryString4Json(tenancyId, sql.toString(), new Object[]
		{ tenancyId, storeId });
	}
	
	@Override
    public List<JSONObject> getItemUnits(String tenancyId, int storeId, Date reportDate) throws Exception {
        StringBuffer sql = new StringBuffer();
        String date = DateUtil.formatDate(reportDate);
        sql.append(" select hiu.item_id as did,hiu.id as duid from pos_soldout ps ")
            .append(" left join hq_item_unit hiu on ps.item_id = hiu.item_id and hiu.valid_state = '1'")
//            .append(" where ps.setdate = '"+ date +"' and ps.num = 0 and ps.store_id = " + storeId);
            .append(" where ps.num = 0 and ps.store_id = " + storeId);
        return this.query4Json(tenancyId, sql.toString());
    }

    @Override
    public List<JSONObject> getItems(String tenancyId, int storeId, String itemIds, Date reportDate) throws Exception {
        StringBuffer sql = new StringBuffer();
        String date = DateUtil.formatDate(reportDate);
        sql.append(" select hiu.item_id as did,hiu.id as duid from pos_soldout ps ")
                .append(" left join hq_item_unit hiu on ps.item_id = hiu.item_id and hiu.valid_state = '1'")
                .append(" where ps.setdate = '"+ date +"' and ps.num = 0 and ps.store_id = " + storeId)
                .append(" and ps.item_id in ("+ itemIds +")");
        return this.query4Json(tenancyId, sql.toString());
    }

    @Override
    public List<JSONObject> getUnits(String tenancyId, String itemIds) throws Exception {
        StringBuffer sql = new StringBuffer();
        sql.append(" select item_id as did,id as duid from hq_item_unit where item_id in ("+ itemIds +") ");
        return this.query4Json(tenancyId, sql.toString());
    }
	
	@Override
	public List<JSONObject> getSoldOutComboId(String tenancyId, int storeId,Integer itemId,String itemIdStr) throws Exception {
		StringBuffer sb=new StringBuffer("select item_id,num,item_unit_id,setdate from pos_soldout where (1=1 ");
		if(itemIdStr!=null&&!"".equals(itemIdStr)){
			sb.append(" and item_id in("+itemIdStr+") ");
		}
		if(null!=itemId&&itemId>0){
			if(itemIdStr!=null&&!"".equals(itemIdStr)){
				sb.append(" or ");
			} else{
				sb.append(" and ");
			}
			sb.append(" item_id="+itemId );
		}
		sb.append(") and store_id =").append(storeId);
		return this.queryString4Json(tenancyId, sb.toString());
	}
	
	@Override
	public  List<JSONObject> getSoldDetailsIdComboId(String tenancyId, Integer itemId,String str) throws Exception{
		StringBuffer sb=new StringBuffer("select details_id from hq_item_combo_details where iitem_id ="+itemId);
		if(str!=null&&!"".equals(str)){
			sb.append("  and details_id in("+str+")");
		}
		return this.queryString4Json(tenancyId, sb.toString());
	}
	
	@Override
	public List<JSONObject> getSingleByItemId(String tenancyId, int storeId,String iitem_id) throws Exception {
		StringBuffer sb = new StringBuffer("select soldout.item_id ,soldout.num  from pos_soldout soldout where 1=1");
		sb.append(" and store_id =").append(storeId);
		if (iitem_id != null && !"".equals(iitem_id)) {
			sb.append(" and soldout.item_id in ( select combo.details_id from hq_item_combo_details combo  where combo.iitem_id='"+ iitem_id + "')");
		}
		List<JSONObject> list = this.queryString4Json(tenancyId, sb.toString());

		return list;
	}
	
	@Override
	public  List<JSONObject> getComboOutBySingelItemId(String tenancyId, int storeId,Integer itemId) throws Exception{
		StringBuffer sb = new StringBuffer("SELECT soldout.item_id,soldout.num from pos_soldout soldout where soldout.item_id in ( select combo.iitem_id from hq_item_combo_details combo where combo.details_id = "+itemId+")");
		sb.append(" and store_id =").append(storeId);
		List<JSONObject> list = this.queryString4Json(tenancyId, sb.toString());
		return list;
	}
	
	/**
	 * 根据单品关联的套餐id 查询套餐下单品的最小沽清数量
	 * @return
	 * @throws SystemException
	 */
	@Override
	public Integer getSingleMinByItemId(String tenancyId, int storeId,String iitem_id) throws Exception {
		StringBuffer sb = new StringBuffer("select soldout.item_id,soldout.num from pos_soldout soldout where 1=1");
		sb.append(" and store_id =").append(storeId);
		if (iitem_id != null && !"".equals(iitem_id)) {
			sb.append(" and soldout.item_id in ( select combo.details_id from hq_item_combo_details combo  where combo.iitem_id='"+ iitem_id + "')");
		}
		sb.append("  order by num asc LIMIT 1");
		List<JSONObject> list = this.queryString4Json(tenancyId, sb.toString());
		Integer num = null;
		if (list != null && list.size() > 0) {
			JSONObject jsonObject = list.get(0);
			num = jsonObject.getInt("num");
		}
		return num;
	}
	
	@Override
	public List<JSONObject> getComboItemByItemId(String tenancyId,Integer itemId) throws Exception {
		StringBuffer sb=new StringBuffer("select iitem_id as item_id from hq_item_combo_details where details_id =" +itemId);
		sb.append(" and is_itemgroup='N' ");
				//"union select details_id as item_id from hq_item_combo_details where iitem_id ="+itemId );
		return this.queryString4Json(tenancyId, sb.toString());
	}
	
	/**
	 * 查询套餐相关的Id
	 * @param itemIdStr
	 * @return
	 * @throws SystemException
	 */
	@Override
	public  List<JSONObject> findComboItemId(String tenancyId, String itemIdStr) throws Exception{
		StringBuffer sb=new StringBuffer("select iitem_id as item_id from hq_item_combo_details where details_id in("+itemIdStr+")" +
				"union select details_id as item_id from hq_item_combo_details where iitem_id in("+itemIdStr+")");
		return this.queryString4Json(tenancyId, sb.toString());
	}
	
	/**
	 * 查询是否为他餐 移动设备验证点餐
	 * @param itemId
	 * @return
	 * @throws SystemException
	 */
	@Override
	public boolean isCheckComboByItemId(String tenancyId, String iitem_id) throws Exception {
		StringBuffer sb = new StringBuffer("select count(combo.details_id) as num from hq_item_combo_details combo where combo.iitem_id='"+ iitem_id + "'");
		List<JSONObject> list = this.queryString4Json(tenancyId, sb.toString());
		Integer num = null;
		boolean result = false;
		if (list != null && list.size() > 0) {
			JSONObject jsonObject = list.get(0);
			num = jsonObject.getInt("num");
		}
		if(num!=null && num>0){
			result = true;
		}
		return result;
	}
}
