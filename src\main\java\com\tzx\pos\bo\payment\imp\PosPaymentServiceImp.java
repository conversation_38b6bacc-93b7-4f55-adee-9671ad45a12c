package com.tzx.pos.bo.payment.imp;

import com.fasterxml.jackson.databind.JsonNode;
import com.tzx.base.entity.PosBillMember;
import com.tzx.base.entity.PosBillPayment;
import com.tzx.clientorder.acewillwechat.bo.TableStateService;
import com.tzx.clientorder.wlifeprogram.bo.WlifeService;
import com.tzx.clientorder.wxorder.base.util.WxOrderUtil;
import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.SysParameterCode;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.entity.Pagination;
import com.tzx.framework.common.exception.CrmErrorCode;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.*;
import com.tzx.member.acewill.bo.AcewillCustomerService;
import com.tzx.member.crm.bo.CustomerCreditService;
import com.tzx.member.meituan.bo.PosNewCrmService;
import com.tzx.orders.bo.OrdersSyncService;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.controller.PosBillToCloudBillRunnable;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.base.util.JsonUtil;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.base.util.ReqDataUtil;
import com.tzx.pos.bo.*;
import com.tzx.pos.bo.douyin.DouyinOpenInvokeException;
import com.tzx.pos.bo.douyin.IDouyinOpen;
import com.tzx.pos.bo.dto.QueryPaymentDetail;
import com.tzx.pos.bo.imp.PosBaseServiceImp;
import com.tzx.pos.bo.payment.PaymentWayService;
import com.tzx.pos.bo.payment.PosPaymentService;
import com.tzx.pos.po.springjdbc.dao.PosBillMemberDao;
import com.tzx.pos.po.springjdbc.dao.PosDao;
import com.tzx.pos.po.springjdbc.dao.PosDishDao;
import com.tzx.pos.po.springjdbc.dao.PosPaymentDao;
import com.tzx.thirdpay.bo.DouyinCouponsService;
import com.tzx.thirdpay.bo.MeituanCouponsService;
import com.tzx.thirdpay.bo.ThirdPaymentService;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;

@Service(PosPaymentService.NAME)
public class PosPaymentServiceImp extends PosBaseServiceImp implements PosPaymentService {
	private static final Logger logger = Logger.getLogger(PosPaymentServiceImp.class);

	@Resource(name = OrdersSyncService.NAME)
	private OrdersSyncService orderSyncService;

//	@Resource(name = CustomerService.NAME)
//	private CustomerService customerService;

	@Resource(name = AcewillCustomerService.NAME)
	protected AcewillCustomerService acewillCustomerService;

	@Resource(name = CustomerCreditService.NAME)
	private CustomerCreditService customerCreditService;

	@Resource(name = PosNewCrmService.NAME)
	private PosNewCrmService posNewCrmService;

	private PaymentWayService posPaymentService;

	@Resource(name = ThirdPaymentService.NAME)
	public ThirdPaymentService thirdPaymentService;

	@Resource(name = MeituanCouponsService.NAME)
	public MeituanCouponsService meituanCouponsService;

    @Resource(name = DouyinCouponsService.NAME)
    public DouyinCouponsService douyinCouponsService;

//	@Resource(name = PosCodeService.NAME)
//	private PosCodeService codeService;

	@Resource(name = PosService.NAME)
	private PosService posService;

	@Resource(name=PosPrintNewService.NAME)
	protected PosPrintNewService posPrintNewService;

	@Resource(name = PosPrintService.NAME)
	protected PosPrintService posPrintService;

	@Resource(name = PosPaymentDao.NAME)
	protected PosPaymentDao paymentDao;

//	@Resource(name = ComboSetMealService.NAME)
//	private ComboSetMealService comboSetMealService;

//	@Resource(name = ComboSetMealDao.NAME)
//	private ComboSetMealDao comboSetMealDao;

	@Resource(name = PosBillMemberDao.NAME)
	protected PosBillMemberDao			memberDao;

	@Resource(name = TableStateService.NAME)
	private TableStateService tableStateService;

	@Resource(name = WlifeService.NAME)
	private WlifeService wlifeProgramService;

	@Resource(name = OrderDeliveryService.NAME)
	private OrderDeliveryService orderDeliveryService;

	@Resource(name = PosDishDao.NAME)
	private PosDishDao			posDishDao;

	@Resource(name = PosDao.NAME)
	private PosDao posDao;

	@Resource(name = PosDishService.NAME)
	private PosDishService posDishService;

    @Autowired
    private IDouyinOpen douyinOpen;

	public static final int PAYMENT_SCALE = 2;
	public static final int DEFAULT_SCALE = 4;

	@Override
	public synchronized void posBillPayment(Data param, Data result, JSONObject printJson, boolean isCheckKssy) throws Exception {

        String tenancyId = param.getTenancy_id();
        int storeId = param.getStore_id();
        String source = param.getSource();

        List<?> paramList = param.getData();
        if (paramList == null || paramList.isEmpty()) {
            throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
        }
        JSONObject para = JSONObject.fromObject(paramList.get(0));

        String report_Date = ParamUtil.getDateStringValue(para, "report_date");
        Date reportDate = DateUtil.parseDate(report_Date);
        String billNum = ParamUtil.getStringValueByObject(para, "bill_num", true, PosErrorCode.NOT_NULL_BILL_NUM);
        int shiftId = ParamUtil.getIntegerValueByObject(para, "shift_id");
        String posNum = ParamUtil.getStringValueByObject(para, "pos_num");
        String optNum = ParamUtil.getStringValueByObject(para, "opt_num");
        String saleMode = ParamUtil.getStringValueByObject(para, "sale_mode");
        String tableCode = ParamUtil.getStringValueByObject(para, "table_code");

        String channel=ParamUtil.getStringValueByObject(para,"chanel");

        String mobil = ParamUtil.getStringValueByObject(para, "mobil");
        String customerCode = ParamUtil.getStringValueByObject(para, "customer_code");
        String customerName = ParamUtil.getStringValueByObject(para, "customer_name");
        String cardCode = ParamUtil.getStringValueByObject(para, "card_code");
        Double customerCredit = ParamUtil.getDoubleValueByObject(para, "customer_credit");
        Double mainBalance = ParamUtil.getDoubleValueByObject(para, "main_balance");
        Double rewardBalance = ParamUtil.getDoubleValueByObject(para, "reward_balance");

        String isprint_bill = ParamUtil.getStringValueByObject(para, "isprint_bill");
        String isInvoice = ParamUtil.getStringValueByObject(para, "is_invoice");
        String isAutoClosed = ParamUtil.getStringValueByObject(para, "is_auto_closed");//是否自动关单

        String sbill_num = null;
        if (Tools.isNullOrEmpty(isprint_bill)) {
            isprint_bill = "N";
        }

        if (Tools.isNullOrEmpty(isAutoClosed)) {
            isAutoClosed = "Y";
        }


        String isOnlineReplayent = "0";
        if (para.containsKey("is_online_payment")) {
            isOnlineReplayent = String.valueOf(para.get("is_online_payment"));
        }

        if (Tools.isNullOrEmpty(reportDate)) {
            throw SystemException.getInstance(PosErrorCode.NOT_NULL_REPORT_DATE);
        }
        if (Tools.isNullOrEmpty(para.opt("item"))) {
            throw SystemException.getInstance(PosErrorCode.NOT_NULL_ITEM_LIST);
        }

        Timestamp currentTime = DateUtil.currentTimestamp();
        StringBuilder oldstate = null;
        StringBuilder newstate = null;
        try {
            oldstate = new StringBuilder("账单编号:" + billNum + ";");
            newstate = new StringBuilder();

            //校验日始
            paymentDao.checkReportDate(tenancyId, storeId, reportDate);

            //校验签到
            if (isCheckKssy) {
                if (SysDictionary.SOURCE_MOVE.contains(source) || SysDictionary.SOURCE_ORDER_DEVICE.equals(source)) {
                    paymentDao.checkBindOptnum(tenancyId, storeId, reportDate, posNum);
                    try {
                        shiftId = paymentDao.getShiftId(tenancyId, storeId, reportDate, optNum, posNum);
                    } catch (Exception e) {
                        e.printStackTrace();
                        throw SystemException.getInstance(PosErrorCode.GET_SHIFT_ID_ERROR);
                    }
                } else {
                    paymentDao.checkKssy(reportDate, optNum, storeId, shiftId, posNum);
                }
            }

            double paymentAmount = 0d;
            double difference = 0d;
            double discountAmount = 0d;
            double discountRate = 0d;
            int discountCaseId = 0;
            String paymentState = "";
//            String chanel = "";
            String billProperty = "";
            String batchNum = "";
            String orderNum = "";

            //获取账单信息
            JSONObject billJson = paymentDao.getPosBillByBillnum(tenancyId, storeId, billNum);
            if (null != billJson) {
                tableCode = billJson.optString("table_code");
                paymentAmount = billJson.optDouble("payment_amount");
                difference = billJson.optDouble("difference");
                discountRate = billJson.optDouble("discount_rate");
                discountAmount = billJson.optDouble("discount_amount");
                discountCaseId = billJson.optInt("discount_case_id");
//                chanel = billJson.optString("source");
                billProperty = billJson.optString("bill_property");
                paymentState = billJson.optString("payment_state");
                sbill_num = billJson.optString("sbill_num");
                if (discountRate <= 0) {
                    discountRate = 100d;
                }
                batchNum = billJson.optString("batch_num");
                orderNum = billJson.optString("order_num");
                if (Tools.isNullOrEmpty(saleMode)) {
                    saleMode = billJson.optString("sale_mode");
                }

                if (Tools.isNullOrEmpty(saleMode)) {
                    saleMode = SysDictionary.SALE_MODE_TS01;
                }

                if (Tools.isNullOrEmpty(tableCode) && Tools.hv(billJson.optString("table_code"))) {
                    tableCode = billJson.optString("table_code");
                }
            }

            oldstate.append(" 应付金额:").append(String.valueOf(paymentAmount)).append(";");

            //校验账单是否关闭
            if (SysDictionary.BILL_PROPERTY_CLOSED.equalsIgnoreCase(billProperty)) {
                JSONObject resultJson = this.getPosBillPaymentByBillNum(tenancyId, storeId, billNum, tableCode, paymentAmount, discountAmount, difference, paymentState);

                List<JSONObject> resultList = new ArrayList<JSONObject>();
                resultList.add(resultJson);

                result.setData(resultList);
                result.setCode(PosErrorCode.BILL_CLOSED.getNumber());
                result.setMsg(PosErrorCode.BILL_CLOSED.getMessage());
                return;
            }

            // 账单金额为0,判断是否可以零结账
            if (paymentAmount <= 0) {
                String isZeroPay = paymentDao.getSysParameter(tenancyId, storeId, "IS_ZEROPAY");
                if (!"1".equals(isZeroPay)) {
                    throw SystemException.getInstance(PosErrorCode.BILL_AMOUNT_ZERO);
                }
            }

            List<String> couponsList = new ArrayList<String>();

            List<String> encryptedCodes = new ArrayList<>();
            String verifyToken=null;

            for (Object itemObj : para.optJSONArray("item")) {

                JSONObject itemJson = JSONObject.fromObject(itemObj);
                itemJson.put("chanel", channel);
                itemJson.put("mobil", mobil);
                itemJson.put("card_code", cardCode);
                itemJson.put("customer_code", customerCode);
                itemJson.put("customer_name", customerName);
                itemJson.put("customer_credit", customerCredit);
                itemJson.put("main_balance", mainBalance);
                itemJson.put("reward_balance", rewardBalance);

                Integer jzid = itemJson.optInt("jzid");
                Double amount = itemJson.optDouble("amount");

                if (Tools.isNullOrEmpty(jzid)) {
                    throw SystemException.getInstance(PosErrorCode.NOT_NULL_JZID);
                }

                if (Tools.isNullOrEmpty(amount)) {
                    throw SystemException.getInstance(PosErrorCode.NOT_NULL_PAYMENT_AMOUNT);
                }

                boolean isCheck = "1".equals(itemJson.optString("is_check"));

                String paymentClass = "";
                String paymentName = "";
                String paymentEnglishName = "";
                double exchangeRate = 1d;
                String isIncome = "";

                JSONObject paymentWayJson = paymentDao.getPaymentWayByID(tenancyId, storeId, jzid);

                if (null != paymentWayJson && !paymentWayJson.isEmpty()) {
                    paymentClass = paymentWayJson.getString("payment_class");
                    paymentName = paymentWayJson.getString("payment_name");
                    paymentEnglishName = paymentWayJson.getString("name_english");
                    exchangeRate = Tools.hv(paymentWayJson.get("rate")) ? paymentWayJson.getDouble("rate") : 1d;
                    isIncome = paymentWayJson.getString("if_income");

                    itemJson.put("payment_class", paymentClass);
                    itemJson.put("payment_name", paymentName);
                    itemJson.put("payment_english_name", paymentEnglishName);
                    itemJson.put("rate", exchangeRate);
                    itemJson.put("if_income", isIncome);

                } else {
                    throw SystemException.getInstance(PosErrorCode.NOT_EXIST_JZID);
                }


                if (SysDictionary.PAYMENT_STATE_PAY.equalsIgnoreCase(paymentState) && 0d >= difference) {
                    // 判断账单付款状态,付款差额,除了第三方付款外,付款中不允许调用结账,提示账单付款中
                    if (!SysDictionary.PAYMENT_DDM_QUICK_PAY.equals(paymentClass) || SysDictionary.PAYMENT_CLASS_ALI_PAY.equals(paymentClass) && !SysDictionary.PAYMENT_CLASS_WECHAT_PAY.equals(paymentClass) && !SysDictionary.PAYMENT_CLASS_JD_PAY.equals(paymentClass) && !SysDictionary.PAYMENT_CLASS_FLASH_PAY.equals(paymentClass)
                            && !SysDictionary.PAYMENT_CLASS_MEIDA_CARD_PAY.equals(paymentClass)) {
                        throw SystemException.getInstance(PosErrorCode.BILL_PAYMENT_STATE_PAYING_ERROR);
                    }
                }

                if (0 >= amount && !SysDictionary.PAYMENT_CLASS_MEIDA_CARD_PAY.equals(paymentClass) && !SysDictionary.PAYMENT_CLASS_CASH.equals(paymentClass)) {
                    throw SystemException.getInstance(PosErrorCode.PAYAMOUNT_NOT_MORE_ZERO_ERROR);
                }

                //除优惠券和现金外,付款不允许大于账单金额
                if (paymentAmount < amount
                        && !SysDictionary.PAYMENT_CLASS_CASH.equals(paymentClass)
                        && !SysDictionary.PAYMENT_CLASS_COUPONS.equals(paymentClass)
                        && !SysDictionary.PAYMENT_CLASS_MEIDA_COUPONS_PAY.equals(paymentClass)
                        && !SysDictionary.PAYMENT_CLASS_WLIFE_COUPON.equals(paymentClass)
                        && !SysDictionary.PAYMENT_CLASS_DOUYIN_COUPONS_PAY.equals(paymentClass)) {
                    throw SystemException.getInstance(PosErrorCode.AMOUNT_NOT_MORE_PAYAMOUNT_ERROR);
                }

                if (SysDictionary.PAYMENT_CLASS_FREESINGLE.equals(paymentClass)) {
                    itemJson.put("payment_amount", paymentAmount);
                    itemJson.put("discount_case_id", discountCaseId);
                    itemJson.put("discount_rate", discountRate);
                    itemJson.put("discount_amount", discountAmount);
                }
                this.getPaymentServiceBeanByPaymentClass(tenancyId, storeId, paymentClass, isCheck);

                posPaymentService.payment(tenancyId, storeId, billNum, batchNum, reportDate, shiftId, posNum, optNum, tableCode, itemJson, currentTime, isOnlineReplayent);

                if(SysDictionary.PAYMENT_CLASS_DOUYIN_COUPONS_PAY.equals(paymentClass)){

                    int count=itemJson.optInt("count");

                    for (int i=0;i<count;i++){
                        encryptedCodes.add(itemJson.optString("number")) ;
                    }

                   if(StringUtils.isEmpty(verifyToken)){
                       verifyToken=itemJson.optString("bill_code");
                   }
                }

                if (!SysDictionary.PAYMENT_CLASS_DOUYIN_COUPONS_PAY.equals(paymentClass)) {

                    JSONObject resultJson = new JSONObject();
                    List<JSONObject> paymentList = paymentDao.getMeiDaBillPaymentByBillnum(tenancyId, storeId, billNum);
                    if (null != paymentList && paymentList.size() > 0)
                    {
                        // 扣款
                        this.billPaymentDebited(tenancyId, storeId, billNum, orderNum, channel, paymentAmount, paymentList, currentTime, resultJson, result);
                    }
                }

                //记录优惠券使用日志
                if (SysDictionary.PAYMENT_CLASS_COUPONS.equals(paymentClass)) {
                    String couponsPro = ParamUtil.getStringValueByObject(itemJson, "coupons_pro");
                    String couponsClassId = ParamUtil.getStringValueByObject(itemJson, "coupons_class_id");
                    String couponsClassName = ParamUtil.getStringValueByObject(itemJson, "coupons_class_name");
                    String couponsTypeId = ParamUtil.getStringValueByObject(itemJson, "coupons_type_id");
                    String key = jzid.toString() + "_" + couponsPro + "_" + couponsClassId + "_" + couponsTypeId + "_" + amount.toString();
                    if (!couponsList.contains(key)) {
                        newstate.append("支付方式:").append(couponsClassName).append(",").append("支付金额:").append(amount).append(";");
                        couponsList.add(key);
                    }
                } else {
                    newstate.append("支付方式:").append(paymentName).append(",").append("支付金额:").append(amount).append(";");
                }
            }

            //抖音验券
            if(CollectionUtils.isNotEmpty(encryptedCodes)) {
                try{
                    JsonNode verifyResult = douyinOpen.verify(verifyToken, encryptedCodes, null);
                    StringBuilder updatePayment=new StringBuilder();
                    for (JsonNode jsonNode : verifyResult.path("verify_results")) {
                        if(0==jsonNode.path("result").asInt()){
                            String code = jsonNode.path("code").asText();
                            String verifyId = jsonNode.path("verify_id").asText();
                            String certificateId = jsonNode.path("certificate_id").asText();
                            updatePayment.append("update pos_bill_payment set out_trade_no='"+verifyId+"_"+certificateId+"' where number='"+code+"';");
                        }else {
                           throw new DouyinOpenInvokeException(jsonNode.path("msg").asText());
                        }
                    }
                    paymentDao.execute(tenancyId,updatePayment.toString());
                }catch (Exception e){
                    throw SystemException.getInstance(PosErrorCode.DOUYIN_COUPOUS_ERROR).set("{0}", "验券").set("{1}",e.getMessage());
                }
            }

            JSONObject resultJson = new JSONObject();

            difference = DoubleHelper.sub(paymentAmount, paymentDao.getBillPaymentAmount(tenancyId, storeId, billNum), DEFAULT_SCALE);

            //修改账单状态
            paymentState = SysDictionary.PAYMENT_STATE_PAY;
            if (difference == paymentAmount) {
                paymentState = SysDictionary.PAYMENT_STATE_NOTPAY;
            }
            JSONObject transferRemark = new JSONObject();//{"isprint_bill":"Y","is_invoice":"0"}
            transferRemark.put("isprint_bill", isprint_bill);
            transferRemark.put("is_invoice", isInvoice);
            paymentDao.updatePosBillForStateByBillnum(tenancyId, storeId, billNum, paymentState, difference, saleMode, tableCode, shiftId, transferRemark.toString());

            if (difference <= 0) {
                billJson.put("sale_mode", saleMode);
                billJson.put("table_code", tableCode);

                if (isAutoClosed == null || "Y".equals(isAutoClosed)) {

                    this.closedPosBill(tenancyId, storeId, billNum, report_Date, shiftId, posNum, optNum, isprint_bill, isInvoice, saleMode, billJson, printJson, isOnlineReplayent, currentTime);
                }

                // 计算账单和账单明细的代销价格
                this.calculateSettlementPrice(tenancyId, storeId, billNum);

                //修改账单税价分离
                paymentDao.updatePosBillTaxPriceSeparation(tenancyId, storeId, billNum, saleMode);

                if ("1".equals(isOnlineReplayent)) {

                    double consumeBeforeCredit = para.optDouble("consume_before_credit");
                    double consumeAfterCredit = para.optDouble("consume_after_credit");
                    double credit = para.optDouble("credit");
                    double amount = para.optDouble("amount");
                    String billCode = para.optString("bill_code");

                    if (Tools.hv(mobil) && Tools.hv(customerCode) && credit > 0) {
                        memberDao.insertPosBillMember(tenancyId, storeId, billNum, reportDate, SysDictionary.BILL_MEMBERCARD_JFZS06, customerCode, cardCode, mobil, currentTime, null, customerName, consumeBeforeCredit, consumeAfterCredit, credit, amount, billCode,
                                SysDictionary.REQUEST_STATUS_COMPLETE);
                    }
                }
            }

            if (Tools.hv(sbill_num)) { //并台账单零结账

                this.combineTableZeroPayment(tenancyId, storeId, sbill_num, reportDate, shiftId, posNum, optNum, currentTime);
            }

            resultJson.putAll(this.getPosBillPaymentByBillNum(tenancyId, storeId, billNum, tableCode, paymentAmount, discountAmount, difference, paymentState));

            List<JSONObject> resultList = new ArrayList<JSONObject>();
            resultList.add(resultJson);

            result.setData(resultList);
            if (Constant.CODE_SUCCESS == result.getCode()) {
                result.setCode(Constant.CODE_SUCCESS);
                result.setMsg(Constant.PAYMENT_SUCCESS);
            } else if (Constant.PAYMENT_STATUS_PAYING_CODE == result.getCode()) {
                result.setCode(Constant.CODE_SUCCESS);
            }
        } catch (SystemException e) {
            newstate = new StringBuilder("结账失败:" + e.getErrorCode().getNumber());
            throw e;
        } catch (Exception e) {
            logger.info("结账失败:", e);
            e.printStackTrace();
            newstate = new StringBuilder("结账失败:" + e.getMessage());
            throw SystemException.getInstance(PosErrorCode.OPER_ERROR);
        } finally {
            try {
                paymentDao.savePosLog(tenancyId, storeId, posNum, optNum, null, shiftId, reportDate, com.tzx.pos.base.Constant.TITLE, "结账", oldstate.toString(), newstate.toString(), currentTime);
            } catch (Exception e) {
                logger.info("保存日志报错:", e);
                e.printStackTrace();
            }
        }

        //数据上传
        upload(tenancyId, storeId + "", "", tableCode, "", billNum);
    }

	@Override
	public void beforehandBillPayment(String tenancyId, int storeId, List<?> paramList, String source, Data result) throws Exception
	{
		if (paramList == null || paramList.isEmpty())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}
		JSONObject para = JSONObject.fromObject(paramList.get(0));

		String report_Date = ParamUtil.getDateStringValue(para, "report_date");
		Date reportDate = DateUtil.parseDate(report_Date);
		String billNum = ParamUtil.getStringValueByObject(para, "bill_num", true, PosErrorCode.NOT_NULL_BILL_NUM);
		Integer shiftId = ParamUtil.getIntegerValueByObject(para, "shift_id");
		String posNum = ParamUtil.getStringValueByObject(para, "pos_num");
		String optNum = ParamUtil.getStringValueByObject(para, "opt_num");
		String saleMode = ParamUtil.getStringValueByObject(para, "sale_mode");
		String tableCode = ParamUtil.getStringValueByObject(para, "table_code");
		String isPrintBill = ParamUtil.getStringValueByObject(para, "isprint_bill");
		String isInvoice = ParamUtil.getStringValueByObject(para, "is_invoice");
		if (Tools.isNullOrEmpty(isPrintBill))
		{
			isPrintBill = "N";
		}

		if (Tools.isNullOrEmpty(reportDate))
		{
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_REPORT_DATE);
		}
		if (Tools.isNullOrEmpty(para.opt("item")))
		{
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_ITEM_LIST);
		}

		Timestamp currentTime = DateUtil.currentTimestamp();

		//校验日始
		paymentDao.checkReportDate(tenancyId, storeId, reportDate);

		//校验签到
		if (SysDictionary.SOURCE_MOVE.contains(source)||SysDictionary.SOURCE_ORDER_DEVICE.equals(source))
		{
			paymentDao.checkBindOptnum(tenancyId, storeId, reportDate, posNum);
			try
			{
				shiftId = paymentDao.getShiftId(tenancyId, storeId, reportDate, optNum, posNum);
			}
			catch (Exception e)
			{
				e.printStackTrace();
				throw SystemException.getInstance(PosErrorCode.GET_SHIFT_ID_ERROR);
			}
		}
		else
		{
			paymentDao.checkKssy(reportDate, optNum, storeId,shiftId,posNum);
		}

		double paymentAmount = 0d;
		double difference = 0d;
		double discountRate = 0d;
		double discountAmount = 0d;
		String chanel = "";
		String billProperty = "";
		String batchNum = "";
		String paymentState="";

		//获取账单信息
		JSONObject billJson = paymentDao.getPosBillByBillnum(tenancyId, storeId, billNum);
		if (null != billJson)
		{
			paymentAmount = billJson.optDouble("payment_amount");
			difference = billJson.optDouble("difference");
			discountRate = billJson.optDouble("discount_rate");
			discountAmount = billJson.optDouble("discount_amount");
			chanel = billJson.optString("source");
			billProperty = billJson.optString("bill_property");
			batchNum = billJson.optString("batch_num");
			paymentState = billJson.optString("payment_state");

			if (discountRate <= 0)
			{
				discountRate = 100d;
			}

			if (Tools.isNullOrEmpty(saleMode))
			{
				saleMode = Tools.isNullOrEmpty(billJson.optString("sale_mode")) ? SysDictionary.SALE_MODE_TS01 : billJson.optString("sale_mode");
			}

			if (Tools.isNullOrEmpty(tableCode) && Tools.hv(billJson.optString("table_code")))
			{
				tableCode = billJson.optString("table_code");
			}
		}

		if (SysDictionary.BILL_PROPERTY_CLOSED.equalsIgnoreCase(billProperty))
		{
			JSONObject resultJson = this.getPosBillPaymentByBillNum(tenancyId, storeId, billNum, tableCode, paymentAmount, discountAmount, difference, paymentState);

			List<JSONObject> resultList = new ArrayList<JSONObject>();
			resultList.add(resultJson);
			result.setData(resultList);
			result.setCode(PosErrorCode.BILL_CLOSED.getNumber());
			result.setMsg(PosErrorCode.BILL_CLOSED.getMessage());
			return;
		}

		if (SysDictionary.PAYMENT_STATE_PAY.equalsIgnoreCase(billJson.optString("payment_state")) && 0d >= difference)
		{
			throw SystemException.getInstance(PosErrorCode.BILL_PAYMENT_STATE_PAYING_ERROR);
		}

		if (0d >= paymentAmount)
		{
			throw SystemException.getInstance(PosErrorCode.BILL_AMOUNT_ZERO);
		}

		JSONArray paymentlist = new JSONArray();
		for (Object itemObj : para.optJSONArray("item"))
		{
			JSONObject itemJson = JSONObject.fromObject(itemObj);
			itemJson.put("chanel", chanel);

			if (Tools.isNullOrEmpty(itemJson.opt("amount")))
			{
				throw SystemException.getInstance(PosErrorCode.NOT_NULL_PAYMENT_AMOUNT);
			}
			double amount = itemJson.optDouble("amount");
			if (0d >= amount)
			{
				throw SystemException.getInstance(PosErrorCode.PAYAMOUNT_NOT_MORE_ZERO_ERROR);
			}

			if (paymentAmount < amount)
			{
				throw SystemException.getInstance(PosErrorCode.AMOUNT_NOT_MORE_PAYAMOUNT_ERROR);
			}

			if (Tools.isNullOrEmpty(itemJson.opt("jzid")))
			{
				throw SystemException.getInstance(PosErrorCode.NOT_NULL_JZID);
			}
			int jzid = itemJson.optInt("jzid");

			String paymentClass = "";
			String paymentName = "";
			String paymentEnglishName = "";
			double exchangeRate = 1d;
			boolean isCheck = false;

			JSONObject item = paymentDao.getBillPaymentByJzid(tenancyId, storeId, billNum, jzid);
			if (null != item && !item.isEmpty())
			{
				throw SystemException.getInstance(PosErrorCode.PAYMENT_NOT_PERMIT_REPEAT_ERROR);
			}

			JSONObject paymentWayJson = paymentDao.getPaymentWayByID(tenancyId, storeId, jzid);
			if (null != paymentWayJson && !paymentWayJson.isEmpty())
			{
				paymentClass = paymentWayJson.getString("payment_class");
				paymentName = paymentWayJson.getString("payment_name");
				paymentEnglishName = paymentWayJson.getString("name_english");
				exchangeRate = Tools.hv(paymentWayJson.get("rate")) ? paymentWayJson.getDouble("rate") : 1d;
				itemJson.put("payment_class", paymentClass);
				itemJson.put("payment_name", paymentName);
				itemJson.put("payment_english_name", paymentEnglishName);
				itemJson.put("rate", exchangeRate);
				itemJson.put("is_check", isCheck);
				itemJson.put("isprint_bill", isPrintBill);
                itemJson.put("source", source);
			}
			else
			{
				throw SystemException.getInstance(PosErrorCode.NOT_EXIST_JZID);
			}
			
			// 插入bill_payment
			PosBillPayment paymentEntity = new PosBillPayment(tenancyId, storeId, reportDate, shiftId, posNum, optNum, billNum, batchNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, amount, itemJson.optDouble("currency_amount"), itemJson.optInt("count"), itemJson.optString("number"), itemJson.optString("phone"), currentTime, SysDictionary.PAYMENT_STATE_PAY);
			paymentEntity.setRate(exchangeRate);
			paymentEntity.setIs_ysk("N");
			paymentEntity.setRemark(itemJson.optString("remark"));
			//设置请求单号
			paymentEntity.setOut_trade_no(ParamUtil.getOutTradeNo(billNum, batchNum, currentTime));
			
			int posBillPaymentId = paymentDao.insertPosBillPayment(tenancyId, storeId, paymentEntity );

//			int posBillPaymentId = paymentDao.insertPosBillPayment(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, amount, itemJson.optDouble("count"), itemJson.optDouble("currency_amount"),
//					itemJson.optString("number"), itemJson.optString("phone"), "N", null, null, itemJson.optString("remark"), currentTime, SysDictionary.PAYMENT_STATE_PAY, batchNum, itemJson.toString(), 0);

			JSONObject payment = new JSONObject();
			payment.put("id", posBillPaymentId);
			payment.put("jzid", jzid);
			payment.put("name", paymentName);
			payment.put("amount", amount);
			payment.put("currency_amount", itemJson.optDouble("currency_amount"));
			payment.put("count", itemJson.optDouble("count"));
			payment.put("number", itemJson.optString("number"));
			payment.put("phone", itemJson.optString("phone"));
			payment.put("shift_id", shiftId);
			payment.put("pos_num", posNum);
			payment.put("cashier_num", optNum);
			payment.put("customer_id", itemJson.opt("customer_id"));
			payment.put("last_updatetime", DateUtil.format(currentTime));
			payment.put("bill_code", itemJson.opt("bill_code"));
			payment.put("remark", itemJson.opt("remark"));
			paymentlist.add(payment);
		}

		JSONObject data = new JSONObject();
//		data.put("order_no", orderNum);
		// 差额
		difference = DoubleHelper.sub(paymentAmount, paymentDao.getBillPaymentAmount(tenancyId, storeId, billNum), DEFAULT_SCALE);

		// 差额小于等于0,修改账单的付款状态 更新 pos_bill 差额 和付款状态
		JSONObject transferRemark = new JSONObject();//{"isprint_bill":"Y","is_invoice":"0"}
		transferRemark.put("isprint_bill", isPrintBill);
		transferRemark.put("is_invoice", isInvoice);
		paymentDao.updatePosBillForStateByBillnum(tenancyId, storeId, billNum, SysDictionary.PAYMENT_STATE_PAY, difference, saleMode, tableCode, shiftId, transferRemark.toString());

		if (difference <= 0)
		{
			data.put("change", difference);
			data.put("difference", 0d);
		}
		else
		{
			data.put("change", 0d);
			data.put("difference", difference);
		}

		if (paymentlist.size() > 0)
		{
			data.put("paymentlist", paymentlist);
		}

		List<JSONObject> ls = new ArrayList<JSONObject>();
		ls.add(data);
		result.setData(ls);

//		if("1".equals(isInvoice))
//		{
//			// 插入发票表
//			JSONObject invoiceJson = new JSONObject();
//			invoiceJson.put("bill_num",billNum);
//			invoiceJson.put("report_date",report_Date);
//			invoiceJson.put("last_updatetime",DateUtil.format(currentTime));
//			invoiceJson.put("invoice_amount",paymentAmount);
//			invoiceJson.put("copy_bill_num","");
//			invoiceJson.put("url_content","");
//			invoiceJson.put("pos_num",posNum);
//			invoiceJson.put("opt_num",optNum);
//			invoiceJson.put("invoice_num","");
//			paymentDao.insertToInvoice(tenancyId, storeId, invoiceJson);
//		}
	}
	


	@Override
	public Data precreateThirdPayment(Data params, String path) throws Exception
	{
		String tenancyId = params.getTenancy_id();
		Integer storeId = params.getStore_id();

		JSONObject paramJson = JSONObject.fromObject(params.getData().get(0));

		String posNum = paramJson.optString("pos_num");
		String optNum = paramJson.optString("opt_num");
		Integer shiftId = Integer.parseInt(paramJson.optString("shift_id"));
		Date reportDate = DateUtil.parseDate(paramJson.optString("report_date"));
		String billNum = paramJson.optString("order_no");
		String channel = paramJson.optString("channel");
		Double totalAmount = paramJson.optDouble("total_amount");
		Integer paymentId = paramJson.optInt("payment_id");

		Timestamp currentTime = DateUtil.currentTimestamp();

		String orderCode = "";
		String batchNum = "";
		String billProperty = "";
		Integer billPaymentId = null;
		Timestamp paymenTime = DateUtil.currentTimestamp();
		String newstate = "";
		try
		{
			JSONObject billJson = paymentDao.getPosBillPaymentByBillnum(tenancyId, storeId, billNum, paramJson.optInt("payment_id"));
			if (null != billJson && !billJson.isEmpty())
			{
				paymenTime = DateUtil.parseTimestamp(billJson.optString("last_updatetime"));
				totalAmount = billJson.optDouble("payment_amount");
				orderCode = billJson.optString("order_num");
				channel = billJson.optString("source");
				batchNum = billJson.optString("batch_num");
				billProperty = billJson.optString("bill_property");
				billPaymentId = billJson.optInt("id");
			}
			if (Tools.isNullOrEmpty(billPaymentId))
			{
				throw SystemException.getInstance(PosErrorCode.BILL_NOT_EXISTS_PAYEMNT_ERROR);
			}

			if (SysDictionary.BILL_PROPERTY_CLOSED.equals(billProperty))
			{
				Data returnData = params.clone();
				JSONObject dataJson = this.getPosBillPaymentByBillNum(tenancyId, storeId, billNum, billJson.optString("table_code"), billJson.optDouble("payment_amount"), billJson.optDouble("discount_amount"), billJson.optDouble("difference"), billJson.optString("payment_state"));
				List<JSONObject> dataList = new ArrayList<JSONObject>();
				dataList.add(dataJson);
				returnData.setData(dataList);
				returnData.setCode(PosErrorCode.BILL_CLOSED.getNumber());
				returnData.setMsg(PosErrorCode.BILL_CLOSED.getMessage());
				returnData.setSuccess(false);
				return returnData;
			}
			
			String outTradeNo = ParamUtil.getStringValueByObject(billJson, "out_trade_no"); 
			if(Tools.isNullOrEmpty(outTradeNo))
			{
				outTradeNo = ParamUtil.getOutTradeNo(billNum, batchNum, orderCode, channel, paymenTime);
			}
			
//			if ((!SysDictionary.CHANEL_MD01.equals(channel)) && Tools.hv(orderCode))
//			{
//				paramJson.put("order_no", orderCode);
//				paramJson.put("bill_num", orderCode);
//			}
//			else
//			{
//				String orderNum = billNum;
//				if (Tools.hv(batchNum) && !"null".equals(batchNum))
//				{
//					orderNum = billNum + "_" + batchNum;
//				}
//				orderNum = orderNum + "@" + String.valueOf(paymenTime.getTime());
//
//				paramJson.put("order_no", orderNum);
//				paramJson.put("bill_num", billNum);
//			}
			
			paramJson.put("order_no", outTradeNo);
			paramJson.put("order_num", billNum);
			paramJson.put("total_amount", totalAmount);

			JSONObject paymentWayJson = paymentDao.getPaymentWayByID(tenancyId, storeId, paymentId);
			String paymentClass = ParamUtil.getStringValueByObject(paymentWayJson, "payment_class");

			this.setItemDetail(tenancyId, storeId, billNum, paymentClass, paramJson);

			Data returnData = thirdPaymentService.precreatePayment(tenancyId, storeId, paramJson, path);

			if (Constant.CODE_SUCCESS == returnData.getCode())
			{
				JSONObject responseJson = JSONObject.fromObject(returnData.getData().get(0));
				String status = responseJson.optString("payment_state");
				String qrCode = responseJson.optString("qrcode");
				if (!SysDictionary.THIRD_PAY_STATUS_PAYING.equals(status) || Tools.isNullOrEmpty(qrCode))
				{
					returnData.setCode(PosErrorCode.CREATE_QRCODE_ERROR.getNumber());
					returnData.setMsg(responseJson.optString("failure_msg"));
				}
			}

			if (Constant.CODE_SUCCESS != returnData.getCode())
			{
				// 获取二维码失败,删除付款
				this.deletePaymentForFailure(tenancyId, storeId, billJson, currentTime);

				this.updateBillPaymentState(tenancyId, storeId, billNum, totalAmount, SysDictionary.PAYMENT_STATE_PAY);

				JSONObject data = new JSONObject();
				data.put("qrcode", "");
				data.put("qrcode_url", "");
				data.put("transaction_no", "");
				data.put("failure_code", "");
				data.put("failure_msg", "");
				data.put("payment_state", SysDictionary.THIRD_PAY_STATUS_FAIL);
				List<JSONObject> dataList = new ArrayList<JSONObject>();
				dataList.add(data);
				returnData.setData(dataList);
			}

			newstate = "Result:" + returnData.getMsg();

			return returnData.set(params);
		}
		catch (SystemException e)
		{
			throw e;
		}
		catch (Exception e)
		{
			logger.info("获取二维码失败", e);
			e.printStackTrace();
			throw SystemException.getInstance(PosErrorCode.OPER_ERROR);
		}
		finally
		{
			try
			{
				paymentDao.savePosLog(tenancyId, storeId, posNum, optNum, optNum, shiftId, reportDate, Constant.TITLE, "获取二维码", "账单编号:" + billNum, newstate, currentTime);
			}
			catch (Exception e)
			{
				logger.info("报错日志报错:", e);
				e.printStackTrace();
			}
		}
	}

	@Override
	public Data barcodeThirdPayment(Data params, JSONObject printJson) throws Exception
	{
		String tenancyId = params.getTenancy_id();
		Integer storeId = params.getStore_id();

		// Data returnData = null;
		JSONObject paramJson = JSONObject.fromObject(params.getData().get(0));

		String report_Date = ParamUtil.getDateStringValue(paramJson, "report_date");
		Date reportDate = DateUtil.parseDate(report_Date);
		int shiftId = paramJson.optInt("shift_id");
		String posNum = paramJson.optString("pos_num");
		String optNum = paramJson.optString("opt_num");
		String billNum = paramJson.optString("order_no");
		String channel = paramJson.optString("channel");
		Double totalAmount = paramJson.optDouble("total_amount");
		Double payAmount = paramJson.optDouble("settle_amount");
		Integer paymentId = paramJson.optInt("payment_id");
		String authcode = ParamUtil.getStringValueByObject(paramJson, "credential");

		Timestamp currentTime = DateUtil.currentTimestamp();

		JSONObject billJson = paymentDao.getPosBillPaymentByBillnum(tenancyId, storeId, billNum, paymentId);

		String orderCode = "";
		String batchNum = "";
		String billProperty = "";
		Integer billPaymentId = null;
		Timestamp paymenTime = DateUtil.currentTimestamp();
		String newstate = "";
		String isOnlineReplayent = "0";
		String isPrintBill = "Y";
		String isInvoice = "0";
		try
		{
			if (null != billJson)
			{
				paymenTime = DateUtil.parseTimestamp(billJson.optString("last_updatetime"));
				totalAmount = billJson.optDouble("payment_amount");
				channel = billJson.optString("source");
				orderCode = billJson.optString("order_num");
				batchNum = billJson.optString("batch_num");
				billProperty = billJson.optString("bill_property");
				billPaymentId = billJson.optInt("id");
				JSONObject transferJson = JSONObject.fromObject(billJson.optString("transfer_remark"));
				if(null!=transferJson && transferJson.containsKey("isprint_bill"))
				{
					isPrintBill = transferJson.optString("isprint_bill");
				}
				if(null!=transferJson && transferJson.containsKey("is_invoice"))
				{
					isInvoice = transferJson.optString("is_invoice");
				}
			}

			if (Tools.isNullOrEmpty(billPaymentId))
			{
				throw SystemException.getInstance(PosErrorCode.BILL_NOT_EXISTS_PAYEMNT_ERROR);
			}

			if (SysDictionary.BILL_PROPERTY_CLOSED.equals(billProperty))
			{
				Data returnData = params.clone();
				JSONObject dataJson = this.getPosBillPaymentByBillNum(tenancyId, storeId, billNum, billJson.optString("table_code"), billJson.optDouble("payment_amount"), billJson.optDouble("discount_amount"), billJson.optDouble("difference"), billJson.optString("payment_state"));
				List<JSONObject> dataList = new ArrayList<JSONObject>();
				dataList.add(dataJson);
				returnData.setData(dataList);
				returnData.setCode(PosErrorCode.BILL_CLOSED.getNumber());
				returnData.setMsg(PosErrorCode.BILL_CLOSED.getMessage());
				returnData.setSuccess(false);
				return returnData;
			}

			String outTradeNo = ParamUtil.getStringValueByObject(billJson, "out_trade_no"); 
			if(Tools.isNullOrEmpty(outTradeNo))
			{
				outTradeNo = ParamUtil.getOutTradeNo(billNum, batchNum, orderCode, channel, paymenTime);
			}
			
//			if ((!SysDictionary.CHANEL_MD01.equals(channel)) && Tools.hv(orderCode))
//			{
//				paramJson.put("order_no", orderCode);
//				paramJson.put("bill_num", orderCode);
//			}
//			else
//			{
//				String orderNum = billNum;
//				if (Tools.hv(batchNum) && !"null".equals(batchNum))
//				{
//					orderNum = billNum + "_" + batchNum;
//				}
//				orderNum = orderNum + "@" + String.valueOf(paymenTime.getTime());
//				paramJson.put("order_no", orderNum);
//				paramJson.put("bill_num", billNum);
//			}
			
			paramJson.put("order_no", outTradeNo);
			paramJson.put("order_num", billNum);
			paramJson.put("total_amount", totalAmount);

			JSONObject paymentWayJson = paymentDao.getPaymentWayByID(tenancyId, storeId, paymentId);
			String paymentClass = ParamUtil.getStringValueByObject(paymentWayJson, "payment_class");

			if(SysDictionary.PAYMENT_CLASS_SCAN_CODE_PAY.equals(paymentClass))
			{
				paymentWayJson = thirdPaymentService.getPaymentClassByuAuthcode(tenancyId, storeId, authcode);
				paymentClass = ParamUtil.getStringValueByObject(paymentWayJson, "payment_class");
				paymentId = ParamUtil.getIntegerValueByObject(paymentWayJson, "payment_id");

				paramJson.put("payment_id",paymentId);

				JSONObject payment = new JSONObject();
				payment.put("type", paymentClass);
				payment.put("jzid", paymentId);
				payment.put("name", paymentWayJson.optString("payment_name"));
				payment.put("name_english", paymentWayJson.optString("name_english"));
				payment.put("rate", paymentWayJson.optDouble("rate"));
				payment.put("yjzid", billJson.optInt("jzid"));
				payment.put("id", billPaymentId);
				paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", payment);
			}

			this.setItemDetail(tenancyId, storeId, billNum, paymentClass, paramJson);

			Data returnData = thirdPaymentService.barcodePayment(tenancyId, storeId, paramJson);

			JSONObject returnJson = null;
			if (null != returnData.getData() && 0 < returnData.getData().size())
			{
				returnJson = JSONObject.fromObject(returnData.getData().get(0));
			}

//			if (null != returnJson && returnJson.containsKey("pay_type") && Tools.hv(returnJson.optString("pay_type")))
//			{
//				paymentWayJson = paymentDao.getPaymentWayByPaymentClass(tenancyId, storeId, returnJson.optString("pay_type"));
//				if (null != paymentWayJson && !paymentWayJson.isEmpty())
//				{
//					paymentId = paymentWayJson.optInt("payment_id");
//					JSONObject payment = new JSONObject();
//					payment.put("type", paymentWayJson.optString("payment_class"));
//					payment.put("jzid", paymentId);
//					payment.put("name", paymentWayJson.optString("payment_name"));
//					payment.put("name_english", paymentWayJson.optString("name_english"));
//					payment.put("rate", paymentWayJson.optDouble("rate"));
//					payment.put("yjzid", billJson.optInt("jzid"));
//					payment.put("id", billPaymentId);
//					paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", payment);
//				}
//			}

//			boolean isQueryPayment = false;
			if (Constant.CODE_SUCCESS == returnData.getCode())
			{
				String paymentState = SysDictionary.THIRD_PAY_STATUS_PAYING;
				if (null != returnJson)
				{
					paymentState = returnJson.optString("payment_state");
				}

				switch (paymentState)
				{
					case SysDictionary.THIRD_PAY_STATUS_SUCCESS:
						String transactionNo = returnJson.optString("transaction_no");// 获取交易号
//						Data payData = this.getPayParam(tenancyId, storeId, report_Date, shiftId, posNum, optNum, billNum, paymentId, transactionNo, "Y");
//						this.thirdPaymentSuccess(payData, returnData, printJson);

						Double buyerPayAmount = 0d;
						if (returnJson.containsKey("buyer_pay_amount") && Tools.hv(returnJson.optString("buyer_pay_amount")))
						{
							buyerPayAmount = ParamUtil.getDoubleValueByObject(returnJson, "buyer_pay_amount");
						}

						Double receiptAmount = 0d;
						if (returnJson.containsKey("receipt_amount") && Tools.hv(returnJson.optString("receipt_amount")))
						{
							receiptAmount = ParamUtil.getDoubleValueByObject(returnJson, "receipt_amount");
						}
						// 账单净收=实收金额-商家优惠承担-佣金
						// 账单净收=用户实付+第三方优惠承担-佣金
						Double thirdFee = 0d;
						Double tenancyAssume = DoubleHelper.sub(payAmount, receiptAmount, DEFAULT_SCALE);
						Double thirdAssume = DoubleHelper.sub(DoubleHelper.sub(payAmount, buyerPayAmount, DEFAULT_SCALE), tenancyAssume, DEFAULT_SCALE);

						JSONObject updatePaymentJson = new JSONObject();
						updatePaymentJson.put("coupon_buy_price", buyerPayAmount);
						updatePaymentJson.put("due", receiptAmount);
						updatePaymentJson.put("tenancy_assume", tenancyAssume);
						updatePaymentJson.put("third_assume", thirdAssume);
						updatePaymentJson.put("third_fee", thirdFee);
						updatePaymentJson.put("bill_code", transactionNo);
						updatePaymentJson.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
						updatePaymentJson.put("id", billPaymentId);
						paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", updatePaymentJson);
						break;
					case SysDictionary.THIRD_PAY_STATUS_FAIL:
						// 删除付款
						this.deletePaymentForFailure(tenancyId, storeId, billJson, currentTime);
//						isQueryPayment = true;
						returnData.setCode(PosErrorCode.THIRD_PAYMENT_FAILURE.getNumber());
						returnData.setMsg(returnJson.optString("failure_msg"));
						break;
					default:
						// 返回账单付款中
//						isQueryPayment = true;
						returnData.setCode(Constant.CODE_SUCCESS);
						returnData.setMsg(returnJson.optString("failure_msg"));
						break;
				}
			}
			else if (Constant.CODE_CONN_EXCEPTION != returnData.getCode())
			{
//				isQueryPayment = true;
//			}
//			else
//			{
				// 删除付款
				this.deletePaymentForFailure(tenancyId, storeId, billJson, currentTime);
				returnData.setCode(PosErrorCode.THIRD_PAYMENT_FAILURE.getNumber());
//				isQueryPayment = true;
			}

//			if (isQueryPayment)
//			{
//				JSONObject dataJson = this.getPosBillPaymentByBillNum(tenancyId, storeId, billNum, billJson.optString("table_code"), billJson.optDouble("payment_amount"), billJson.optDouble("discount_amount"), billJson.optDouble("difference"), billJson.optString("payment_state"));
//				List<JSONObject> dataList = new ArrayList<JSONObject>();
//				dataList.add(dataJson);
//				returnData.setData(dataList);
//			}

			//计算差额,差额小于等于0,则关单
			Double billPaymentAmount = 0d;
			List<JSONObject> paymentList = paymentDao.getBillPaymentByBillnum(tenancyId, storeId, billNum);
			for (JSONObject pay : paymentList)
			{
//				pay.put("payment_state_text", SysDictionary.PAYMENT_STATE_TEXT_MAP.get(pay.optString("payment_state")));
				billPaymentAmount = DoubleHelper.add(billPaymentAmount, DoubleHelper.sub(pay.optDouble("currency_amount"), pay.optDouble("more_coupon"), DEFAULT_SCALE), DEFAULT_SCALE);
			}

			Double difference = DoubleHelper.sub(totalAmount, billPaymentAmount, DEFAULT_SCALE);

			if (0 >= difference)
			{
				this.closedPosBill(tenancyId, storeId, billNum, report_Date, shiftId, posNum, optNum, isPrintBill, isInvoice, billJson.optString("sale_mode"), billJson, printJson, isOnlineReplayent, currentTime);
			}
			else
			{
				String paymentState = this.getBillPaymentStateByList(totalAmount, difference, paymentList);
				paymentDao.updatePosBillForStateByBillnum(tenancyId, storeId, billNum, paymentState, difference);
			}

			newstate = "Result:" + returnData.getMsg();

			//查询付款明细
			JSONObject dataJson = this.getPosBillPaymentByBillNum(tenancyId, storeId, billNum, billJson.optString("table_code"), billJson.optDouble("payment_amount"), billJson.optDouble("discount_amount"), billJson.optDouble("difference"), billJson.optString("payment_state"));
			List<JSONObject> dataList = new ArrayList<JSONObject>();
			dataList.add(dataJson);
			returnData.setData(dataList);

			return returnData.set(params);
		}
		catch (SystemException e)
		{
			newstate = "扫码支付失败:" + e.getErrorCode().getNumber();
			throw e;
		}
		catch (Exception e)
		{
			logger.info("扫码支付失败", e);
			e.printStackTrace();
			newstate = "扫码支付失败:" + e.getMessage();
			throw SystemException.getInstance(PosErrorCode.OPER_ERROR);
		}
		finally
		{
			try
			{
				paymentDao.savePosLog(tenancyId, storeId, posNum, optNum, optNum, shiftId, reportDate, Constant.TITLE, "扫码支付", "账单编号:" + billNum, newstate, currentTime);
			}
			catch (Exception e)
			{
				logger.info("报错日志报错:", e);
				e.printStackTrace();
			}
		}
	}

	/** 添加菜品明细参数
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param paymentClass
	 * @param paramJson
	 * @throws Exception
	 */
	private void setItemDetail(String tenancyId, int storeId, String billNum, String paymentClass, JSONObject paramJson) throws Exception
	{
		if (SysDictionary.PAYMENT_DDM_QUICK_PAY.equals(paymentClass) || SysDictionary.PAYMENT_CLASS_ALI_PAY.equals(paymentClass))
		{
			List<JSONObject> itemList = paymentDao.getPosBillItemForSingleByBillNum(tenancyId, storeId, billNum);
			List<JSONObject> goodsDetailList = new ArrayList<JSONObject>();
			for (JSONObject itemJson : itemList)
			{
				Double itemCount = ParamUtil.getDoubleValueByObject(itemJson, "item_count");
				Double realAmount = DoubleHelper.div(ParamUtil.getDoubleValueByObject(itemJson, "real_amount"), itemCount, PAYMENT_SCALE);

				JSONObject dishJson = new JSONObject();
				dishJson.put("goods_id", ParamUtil.getStringValueByObject(itemJson, "item_num"));
				dishJson.put("goods_name", ParamUtil.getStringValueByObject(itemJson, "item_name"));
				dishJson.put("quantity", itemCount);
				dishJson.put("price", realAmount);
				goodsDetailList.add(dishJson);
			}
			JSONObject aliPayJson = new JSONObject();
			aliPayJson.put("goods_detail", goodsDetailList);

			JSONObject extraJson = new JSONObject();
			extraJson.put("ali_pay", aliPayJson);

			paramJson.put("extra", extraJson);
		}
	}

	@Override
	public void pretrPrintBill(Data paraData, String webPath, Data resultData) throws Exception
	{
		// TODO 预打单打印
		String tenancyId = paraData.getTenancy_id();
		Integer storeId = paraData.getStore_id();
		String source = paraData.getSource();

		JSONObject paraJson = JSONObject.fromObject(paraData.getData().get(0));

		String reportDate = paraJson.optString("report_date");
		Integer shiftId = paraJson.optInt("shift_id");
		String posNum = paraJson.optString("pos_num");
		String optNum = paraJson.optString("opt_num");
		String billNum = paraJson.optString("order_no");
		String isprintBill = paraJson.optString("isprint_bill");
		if(Tools.isNullOrEmpty(isprintBill)){ // 默认打印结账单
            isprintBill = "Y";
        }

		String isPrintPayment = paraJson.optString("is_print_payment");
		String qrCodeUrl = "";
		String qrCode = "";

		// 判断是否打印付款码
		if ("1".equals(isPrintPayment))
		{
			isPrintPayment = "0";

			Integer paymentWayId = paraJson.optInt("payment_id");
			Double paymentAmount = paraJson.optDouble("settle_amount");

			// 判断付款方式是否二码合一
			JSONObject paymentWayJson = paymentDao.getPaymentWayByID(tenancyId, storeId, paymentWayId);
			String paymentClass = paymentWayJson.optString("payment_class");
			if (SysDictionary.PAYMENT_CLASS_SYNCRETIC_PAY.equals(paymentClass))
			{
				// 预付款
				JSONObject paymentItemParaJson = new JSONObject();
				paymentItemParaJson.put("jzid", paymentWayId);
				paymentItemParaJson.put("amount", paymentAmount);
				paymentItemParaJson.put("currency_amount", paymentAmount);
				paymentItemParaJson.put("count", 1d);
				paymentItemParaJson.put("number", "");
				paymentItemParaJson.put("phone", "");
				paymentItemParaJson.put("remark", "");

				List<JSONObject> paymentItemParaList = new ArrayList<JSONObject>();
				paymentItemParaList.add(paymentItemParaJson);

				JSONObject paymentParaJson1 = new JSONObject();
				paymentParaJson1.put("report_date", reportDate);
				paymentParaJson1.put("shift_id", shiftId);
				paymentParaJson1.put("pos_num", posNum);
				paymentParaJson1.put("opt_num", optNum);
				paymentParaJson1.put("bill_num", billNum);
				paymentParaJson1.put("item", paymentItemParaList);
                paymentParaJson1.put("isprint_bill", isprintBill);

				List<JSONObject> paymentParaList1 = new ArrayList<JSONObject>();
				paymentParaList1.add(paymentParaJson1);

				Data paymentResultData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
				this.beforehandBillPayment(tenancyId, storeId, paymentParaList1, source, paymentResultData);

				if (Constant.CODE_SUCCESS == paymentResultData.getCode())
				{
					// 获取付款二维码
					List<JSONObject> paymentParaList2 = new ArrayList<JSONObject>();
					paymentParaList2.add(paraJson);

					Data paymentData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
					paymentData.setData(paymentParaList2);

					paymentResultData = this.precreateThirdPayment(paymentData, webPath);

					if(Constant.CODE_SUCCESS == paymentResultData.getCode())
					{
					// 判断获取付款二维码成功
						JSONObject resultJson = JSONObject.fromObject(paymentResultData.getData().get(0));
						qrCodeUrl = resultJson.optString("qrcode_url");
						qrCode= resultJson.optString("qrcode");
						isPrintPayment = "1";
					}
//					else
//					{
//						// 如果获取付款二维码失败,取消付款
//						JSONObject paramJson = paymentDao.getBillPaymentByJzid(tenancyId, storeId, billNum, paymentWayId);
//						this.thirdPaymentFailure(tenancyId, storeId, paramJson);
//					}
				}
			}
		}

		// 打印预打单
		JSONObject printJson = new JSONObject();
		printJson.put("mode", "0");
		printJson.put("print_code", SysDictionary.PRINT_CODE_1101);
		printJson.put("report_date", reportDate);
		printJson.put("shift_id", shiftId);
		printJson.put("pos_num", posNum);
		printJson.put("opt_num", optNum);
		printJson.put("bill_num", billNum);
		printJson.put("qrcode", qrCode);
		printJson.put("qrcode_url", qrCodeUrl);

		if (SysDictionary.SOURCE_MOVE.contains(source)||SysDictionary.SOURCE_ORDER_DEVICE.equals(source))
		{
			posPrintService.getBindOptNum(tenancyId, storeId, printJson);
		}
        printJson.put("source", source);

		if (posPrintNewService.isNONewPrint(tenancyId, storeId))
		{// 如果启用新的打印模式
			posPrintNewService.posPrintByMode(tenancyId, storeId, SysDictionary.PRINT_CODE_1101, printJson);
		}
		else
		{
			List<JSONObject> printList = new ArrayList<JSONObject>();
			printList.add(printJson);

			Data printData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
			printData.setData(printList);

			posPrintService.printPosBill(printData, null);
		}

		JSONObject resultJson = new JSONObject();
		resultJson.put("is_print_payment", isPrintPayment);

		List<JSONObject> resultList = new ArrayList<JSONObject>();
		resultList.add(resultJson);
		resultData.setData(resultList);
		resultData.setCode(Constant.CODE_SUCCESS);
		resultData.setMsg("打印成功");
	}

	@Override
	public synchronized void cancelPayment(Data param, Data result) throws Exception {

		String tenancyId = param.getTenancy_id();
		int storeId = param.getStore_id();
		List<?> paramList = param.getData();
		if (null == paramList || paramList.isEmpty()) {
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}

		JSONObject para = JSONObject.fromObject(paramList.get(0));

		if (Tools.isNullOrEmpty(para.opt("bill_num"))) {
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_BILL_NUM);
		}

		if (Tools.isNullOrEmpty(para.opt("report_date"))) {
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_REPORT_DATE);
		}

		Date reportDate = DateUtil.parseDate(para.optString("report_date"));
		int shiftId = para.optInt("shift_id");
		String billNum = para.optString("bill_num");
		String posNum = para.optString("pos_num");
		String optNum = para.optString("opt_num");
		int payId = para.optInt("pay_id");
		String mode = "1";//全退,部分退
		if (para.containsKey("mode")) {
			mode = para.optString("mode");
		}
		String isRefund = "Y";
		if (para.containsKey("is_refund")) {
			isRefund = para.optString("is_refund");
		}

		Timestamp currentTime = DateUtil.currentTimestamp();
		StringBuilder oldstate=null;
		StringBuilder newstate=null;
		List<JSONObject> deletePaymentList = null;
		try
		{
			oldstate = new StringBuilder("账单编号:" + billNum + "; ");
			newstate = new StringBuilder();

			double paymentAmount = 0d;
			double difference = 0d;
			double discountAmount = 0d;
			String chanel = "";
			String orderNum = "";
			String billPaymentState = "";
			String tableCode = "";
//			String payNo = null;

			JSONObject billJson = paymentDao.getPosBillByBillnum(tenancyId, storeId, billNum);
			if (null != billJson) {
				paymentAmount = billJson.optDouble("payment_amount");
				difference = billJson.optDouble("difference");
				discountAmount = billJson.optDouble("discount_amount");
				chanel = billJson.optString("source");
				orderNum = billJson.optString("order_num");
				billPaymentState = billJson.optString("payment_state");
				tableCode = billJson.optString("table_code");
//				payNo = billJson.optString("pay_no");

				if (SysDictionary.BILL_PROPERTY_CLOSED.equals(billJson.optString("bill_property"))) {
					JSONObject resultJson = this.getPosBillPaymentByBillNum(tenancyId, storeId, billNum, tableCode, paymentAmount, discountAmount, difference, billPaymentState);

					List<JSONObject> resultList = new ArrayList<JSONObject>();
					resultList.add(resultJson);
					result.setData(resultList);
					result.setCode(PosErrorCode.BILL_CLOSED.getNumber());
					result.setMsg(PosErrorCode.BILL_CLOSED.getMessage());
					return;
					// throw SystemException.getInstance(PosErrorCode.BILL_CLOSED);
				}
			}

			StringBuilder queryPaymentSql = new StringBuilder("select bp.*,pw.payment_class from pos_bill_payment bp left join payment_way pw on bp.tenancy_id=pw.tenancy_id and bp.jzid=pw.id where bp.tenancy_id=? and bp.store_id=? and bp.bill_num=?");

			if ("1".equals(mode)) {
				queryPaymentSql.append(" and (bp.id=? or bp.jzid=?)");
				deletePaymentList = paymentDao.query4Json(tenancyId, queryPaymentSql.toString(), new Object[]
				{ tenancyId, storeId, billNum, payId, payId });
				
				if (null != deletePaymentList && 0 < deletePaymentList.size())
				{
					JSONObject payment = deletePaymentList.get(0);
					String paymentState = ParamUtil.getStringValueByObject(payment, "payment_state");
					String lastUpdatetime = ParamUtil.getStringValueByObject(payment, "last_updatetime");
					if (SysDictionary.PAYMENT_STATE_PAY.equals(paymentState))
					{
						//出现付款异常时,校验操作时间,固定时间内,不允许操作取消付款;
						String paraValue = paymentDao.getSysParameter(tenancyId, storeId, SysParameterCode.CANCEL_PAYMENT_LIMIT_TIME);
						Integer limitTime = (CommonUtil.hv(paraValue) ? Integer.parseInt(paraValue) : 0);
						if (0 < limitTime.intValue() && 0 < DateUtil.timestampCompare(lastUpdatetime, DateUtil.getCurrentDateByDiff(limitTime)))
						{
							JSONObject resultJson = this.getPosBillPaymentByBillNum(tenancyId, storeId, billNum, tableCode, paymentAmount, discountAmount, difference, billPaymentState);

							List<JSONObject> resultList = new ArrayList<JSONObject>();
							resultList.add(resultJson);
							result.setData(resultList);
							result.setCode(Constant.CODE_AUTH_FAILURE);
							result.setMsg(Constant.CANCEL_PAYMENT_TIME_LIMIT);
							return;
						}
					}
				}
				
			} else {
				deletePaymentList = paymentDao.query4Json(tenancyId, queryPaymentSql.toString(), new Object[]
						{tenancyId, storeId, billNum});
			}

			if (null != deletePaymentList && deletePaymentList.size() > 0) {
				// 防并发,判断账单是否关闭
				int count = paymentDao.updatePosBillForStateByBillnum(tenancyId, storeId, billNum, SysDictionary.PAYMENT_STATE_CANCEL, difference);
				if (0 == count)
				{
					JSONObject resultJson = this.getPosBillPaymentByBillNum(tenancyId, storeId, billNum, tableCode, paymentAmount, discountAmount, difference, billPaymentState);

					List<JSONObject> resultList = new ArrayList<JSONObject>();
					resultList.add(resultJson);
					result.setData(resultList);
					result.setCode(PosErrorCode.BILL_CLOSED.getNumber());
					result.setMsg(PropertiesLoader.getProperty(String.valueOf(PosErrorCode.BILL_CLOSED.getNumber())));
					return;
				}

//				String customerType = paymentDao.getCustomerType(tenancyId, storeId);

				boolean isSuccess = true;
				List<String> outTradeNoList = new ArrayList<String>();
				for (JSONObject payment : deletePaymentList) {
					payId = payment.optInt("id");
					int jzid = payment.optInt("jzid");
					String table_code = payment.optString("table_code");
					String paymentClass = payment.optString("payment_class");
					String paymentName = payment.optString("name");
					String paymentEnglishName = payment.optString("name_english");
					double exchangeRate = payment.optDouble("rate");
					double pay_amount = payment.optDouble("amount");
					double pay_count = payment.optDouble("count");
					double currency_amount = payment.optDouble("currency_amount");
					String number = payment.optString("number");
					String phone = payment.optString("phone");
					int customer_id = payment.optInt("customer_id");
					String remark = payment.optString("remark");
					String batch_num = payment.optString("batch_num");
					String is_ysk = Tools.hv(payment.optString("is_ysk")) ? payment.optString("is_ysk") : "N";
					String paymentState = payment.optString("payment_state");
					String paymentUid = payment.optString("payment_uid");
					String outTradeNo = payment.optString("out_trade_no");

					if(SysDictionary.PAYMENT_CLASS_WLIFE_BALANCE.equals(paymentClass) || SysDictionary.PAYMENT_CLASS_WLIFE_CREDIT.equals(paymentClass) || SysDictionary.PAYMENT_CLASS_WLIFE_COUPON.equals(paymentClass))
					{
						if(outTradeNoList.contains(outTradeNo))
						{
							continue;
						}
						
						// 付款中或付款完成,取消付款请求退款接口
						if ("Y".equals(isRefund) && (SysDictionary.PAYMENT_STATE_PAY.equals(paymentState) || SysDictionary.PAYMENT_STATE_PAY_COMPLETE.equals(paymentState) || SysDictionary.PAYMENT_STATE_PAY_PRESERVE.equals(paymentState)))
						{
							outTradeNoList.add(outTradeNo);
							this.getPaymentServiceBeanByPaymentClass(tenancyId, storeId, paymentClass, true);

							JSONObject resultJson = new JSONObject();
							posPaymentService.paymentCancel(tenancyId, storeId, billNum, orderNum, chanel, paymentAmount, payment, currentTime, resultJson, result, Type.BILL_PAYMENT_CANCEL.name());
						}

						paymentDao.insertPosBillPaymentLogByBillmum(tenancyId, storeId, billNum, outTradeNo, SysDictionary.PAYMENT_STATE_PAY_FAILURE, payment.optString("bill_code"));

						paymentDao.deleteBillPaymentCouponsByBillNum(tenancyId, storeId, billNum, outTradeNo);

						paymentDao.deletePosBillPaymentByBillmum(tenancyId, storeId, billNum, outTradeNo);

						memberDao.deletePosBillMemberByBillCode(tenancyId, storeId, billNum, payment.optString("bill_code"));
					}
					else {
						//付款中或付款完成,取消付款请求退款接口
						if ("Y".equals(isRefund) && (SysDictionary.PAYMENT_STATE_PAY.equals(paymentState) || SysDictionary.PAYMENT_STATE_PAY_COMPLETE.equals(paymentState)))
						{
							this.getPaymentServiceBeanByPaymentClass(tenancyId, storeId,payment.optString("payment_class"),true);
	
							JSONObject resultJson = new JSONObject();
							posPaymentService.paymentCancel(tenancyId, storeId, billNum, orderNum, chanel, paymentAmount, payment, currentTime, resultJson, result, Type.BILL_PAYMENT_CANCEL.name());
						}

						paymentDao.deletePosBillPaymentByID(tenancyId, storeId, billNum, payId);

						paymentDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, table_code, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, customer_id,
								payment.optString("bill_code"), remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_FAILURE, batch_num, payment.optString("param_cach"), 0d);

						if (SysDictionary.PAYMENT_CLASS_CARD.equals(paymentClass))
						{
							memberDao.deletePosBillMember(tenancyId, storeId, billNum, SysDictionary.BILL_MEMBERCARD_CZXF03);
						}
						else if (SysDictionary.PAYMENT_CLASS_CARD_CREDIT.equals(paymentClass))
						{
							memberDao.deletePosBillMember(tenancyId, storeId, billNum, SysDictionary.BILL_MEMBERCARD_JFDX05);
						}
						else if (SysDictionary.PAYMENT_CLASS_COUPONS.equals(paymentClass))
						{
							if (Tools.hv(number))
							{
								memberDao.deletePosBillMember(tenancyId, storeId, billNum, SysDictionary.BILL_MEMBERCARD_YHJ04);
							}
							
							paymentDao.deletePosBillPaymentCoupons( tenancyId, storeId, billNum, paymentUid );
						}
						else if (SysDictionary.PAYMENT_CLASS_MEIDA_COUPONS_PAY.equals(paymentClass))
						{
							paymentDao.deletePosBillPaymentCoupons(tenancyId, storeId, billNum, paymentUid);
                            paymentDao.getJdbcTemplate(null).execute("update pos_bill_payment_coupons_mt set state=0 where pay_uuid='"+paymentUid+"'");
						}
					}

					isSuccess = (isSuccess && Constant.CODE_SUCCESS == result.getCode());
				}

				// 删除账单发票记录
				JSONObject invoiceJo = paymentDao.getInvoiceInfo(tenancyId, storeId, para);
				// 判断开具发票状态
				if (!Tools.isNullOrEmpty(invoiceJo)) {
					// 开具过发票/电子发票则删除发票记录
					paymentDao.deleteInvoiceFromInvoice(tenancyId, storeId, para);
				}

				this.updateBillPaymentState(tenancyId, storeId, billNum, paymentAmount, billPaymentState);

				result.setCode(Constant.CODE_SUCCESS);
				result.setMsg(Constant.BILL_PAYMENT_CANCEL_SUCCESS);
				if(false == isSuccess)
				{
					result.setCode(PosErrorCode.BILL_PAYMENT_CANCEL_REFUND_FAILURE.getNumber());
					result.setMsg(PosErrorCode.BILL_PAYMENT_CANCEL_REFUND_FAILURE.getMessage());
				}
			}

			oldstate.append("取消付款记录数:").append(String.valueOf(null != deletePaymentList ? deletePaymentList.size():0));
			newstate.append("账单差额:").append(String.valueOf(difference));
		}
		catch (SystemException e)
		{
			newstate.append("取消付款失败:").append(e.getErrorCode().getNumber());
			throw e;
		}
		catch (Exception e)
		{
			logger.info("取消付款错误",e);
			e.printStackTrace();
			newstate.append("取消付款失败:").append(e.getMessage());
			throw SystemException.getInstance(PosErrorCode.OPER_ERROR);
		}
		finally
		{
			try
			{
				if (null != deletePaymentList && deletePaymentList.size() > 0)
				{
					String content = "取消付款";
					paymentDao.savePosLog(tenancyId, storeId, posNum, optNum, null, shiftId, reportDate, Constant.TITLE, content, oldstate.toString(), newstate.toString(), currentTime);
				}
			}
			catch (Exception e)
			{
				logger.info("报错日志报错:", e);
				e.printStackTrace();
			}
		}
	}

	@Override
	public void paymentStateQuery(Data param, Data result, JSONObject printJson) throws Exception {
		// TODO Auto-generated method stub
		String tenancyId = param.getTenancy_id();
		int storeId = param.getStore_id();
		List<?> paramList = param.getData();
		if (paramList == null || paramList.isEmpty()) {
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}
		JSONObject para = JSONObject.fromObject(paramList.get(0));
		String report_date = ParamUtil.getDateStringValue(para, "report_date");
		Date reportDate = DateUtil.parseDate(report_date);
		String billNum = para.optString("bill_num");
		int shiftId = para.optInt("shift_id");
		String posNum = para.optString("pos_num");
		String optNum = para.optString("opt_num");
		String isPrintBill = para.optString("isprint_bill");
		Integer payId = para.optInt("pay_id");
		if (Tools.isNullOrEmpty(isPrintBill)) {
			isPrintBill = "N";
		}
		String isInvoice ="0";

		if (payId <= 0) {
			payId = para.optInt("payment_id");
		}

		if (Tools.isNullOrEmpty(reportDate)) {
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_REPORT_DATE);
		}
		if (Tools.isNullOrEmpty(billNum)) {
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_BILL_NUM);
		}

		Timestamp currentTime = DateUtil.currentTimestamp();

		// StringBuilder qureyBillSql = new
		// StringBuilder("select coalesce(payment_amount,0) as payment_amount,coalesce(discount_amount,0) as discount_amount,payment_state,table_code,bill_property from pos_bill where tenancy_id=? and store_id=? and bill_num=?");
		// SqlRowSet rs = paymentDao.query4SqlRowSet(qureyBillSql.toString(),
		// new Object[]
		// { tenancyId, storeId, billNum });

		JSONObject billJson = paymentDao.getPosBillByBillnum(tenancyId, storeId, billNum);

		double paymentAmount = 0d;
		double discountAmount = 0d;
		double difference = 0d;
		String paymentState = null;
		String tableCode = null;
		String billProperty = null;
		String orderNum = "";
		String chanel = "";
		String saleMode="";
		JSONObject transferJson = null;
		if (null != billJson) {
			paymentAmount = billJson.optDouble("payment_amount");
			discountAmount = billJson.optDouble("discount_amount");
			paymentState = billJson.optString("payment_state");
			tableCode = billJson.optString("table_code");
			billProperty = billJson.optString("bill_property");
			orderNum = billJson.optString("order_num");
			chanel = billJson.optString("source");
			difference = billJson.optDouble("difference");
			saleMode = billJson.optString("sale_mode");
			if (Tools.hv(billJson.optString("transfer_remark")))
			{
				transferJson = JSONObject.fromObject(billJson.optString("transfer_remark"));
			}
		}

		if (null != transferJson && transferJson.containsKey("isprint_bill"))
		{
			isPrintBill = transferJson.optString("isprint_bill");
		}

		if (null != transferJson && transferJson.containsKey("is_invoice"))
		{
			isInvoice = transferJson.optString("is_invoice");
		}

		if (SysDictionary.BILL_PROPERTY_CLOSED.equals(billProperty)) {

			result.setCode(Constant.CODE_SUCCESS);
			result.setMsg(PosErrorCode.BILL_CLOSED.getMessage());
		}else {

		StringBuilder queryPaymentSql = new StringBuilder("select bp.*,pw.payment_class from pos_bill_payment bp left join payment_way pw on bp.tenancy_id=pw.tenancy_id and bp.jzid=pw.id where bp.tenancy_id=? and bp.store_id=? and bp.bill_num=? and bp.payment_state=?");
		List<JSONObject> payList = null;

		if (null != payId && payId > 0) {
			queryPaymentSql.append(" and (bp.id=? or bp.jzid=?)");
			payList = paymentDao.query4Json(tenancyId, queryPaymentSql.toString(), new Object[]
					{tenancyId, storeId, billNum, SysDictionary.PAYMENT_STATE_PAY, payId, payId});
		} else {
			payList = paymentDao.query4Json(tenancyId, queryPaymentSql.toString(), new Object[]
					{tenancyId, storeId, billNum, SysDictionary.PAYMENT_STATE_PAY});
		}

		JSONObject resultJson = new JSONObject();

		for (JSONObject payItem : payList) {
			String paymentClass = payItem.optString("payment_class");
			boolean isCheck = false;
			if (SysDictionary.PAYMENT_CLASS_COUPONS.equals(paymentClass) && Tools.hv(payItem.optString("number")))
			{
				isCheck = true;
			}
			this.getPaymentServiceBeanByPaymentClass(tenancyId,storeId, paymentClass,isCheck);

			Data resultData = new Data();

//			JSONObject paramCachJson = JSONObject.fromObject(payItem.optString("param_cach"));// payItem.optJSONObject("param_cach");
//			if (null != paramCachJson && paramCachJson.containsKey("pay_state") && SysDictionary.PAYMENT_STATE_NOTPAY.equals(paramCachJson.optString("pay_state"))) {
//				posPaymentService.paymentDebit(tenancyId, storeId, billNum, orderNum, chanel, paymentAmount, payItem, currentTime, resultJson, resultData);
//				continue;
//			} else {
				posPaymentService.findPaymentDebit(tenancyId, storeId, billNum, orderNum, chanel, paymentAmount, payItem, currentTime, resultJson, resultData);
//			}

//			if (Constant.CODE_SUCCESS == resultData.getCode()) {
//				if (SysDictionary.PAYMENT_STATE_PAY_COMPLETE.equals(payItem.optString("payment_state"))) {
//					posPaymentService.paymentDebitSuccess(tenancyId, storeId, billNum, payItem);
//				} else if (SysDictionary.PAYMENT_STATE_PAY_FAILURE.equals(payItem.optString("payment_state"))) {
//					posPaymentService.paymentDebitFailure(tenancyId, storeId, billNum, payItem.optInt("id"), 0, null);
//				}
//			} else if (Constant.CODE_CONN_EXCEPTION == resultData.getCode()) {
//				result.setCode(Constant.CODE_CONN_EXCEPTION);
//				result.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
//			} else {
//				if (SysDictionary.PAYMENT_STATE_PAY_FAILURE.equals(payItem.optString("payment_state"))) {
//					posPaymentService.paymentDebitFailure(tenancyId, storeId, billNum, payItem.optInt("id"), resultData.getCode(), resultData.getMsg());
//				}
////				this.getPaymentException(result, resultData.getCode(), resultData.getMsg());
//				if (CrmErrorCode.BALANCE_NOTENOUGH_ERROE.getNumber() == resultData.getCode())
//				{
//					resultData.setCode(resultData.getCode());
//					resultData.setMsg(PropertiesLoader.getProperty(String.valueOf(resultData.getCode())));
//				}
//				else
//				{
//					resultData.setCode(PosErrorCode.BILL_PAYMENT_ERROP.getNumber());
//					resultData.setMsg(PosErrorCode.BILL_PAYMENT_ERROP.getMessage().replace("{0}", resultData.getMsg()));
//				}
//			}
		}

		if (difference <= 0 && paymentAmount > 0) {
			payList = paymentDao.getMeiDaBillPaymentByBillnum(tenancyId, storeId, billNum);
			List<JSONObject> paymentList = new ArrayList<JSONObject>();
			paymentState = SysDictionary.PAYMENT_STATE_PAY_COMPLETE;
			String payState = null;
			for (JSONObject payItem : payList) {
				payState = payItem.optString("payment_state");
				JSONObject paramCachJson = JSONObject.fromObject(payItem.optString("param_cach"));
				if (null != paramCachJson && paramCachJson.containsKey("pay_state") && SysDictionary.PAYMENT_STATE_PAY.equals(payState)) {
					payState = paramCachJson.optString("pay_state");
				}

				if (SysDictionary.PAYMENT_STATE_NOTPAY.equals(payState)) {
					paymentList.add(payItem);
				} else if (SysDictionary.PAYMENT_STATE_PAY_COMPLETE.equals(payState) == false) {
					paymentState = SysDictionary.PAYMENT_STATE_PAY;
				}
			}

//			if (null != paymentList && paymentList.size() > 0 && SysDictionary.PAYMENT_STATE_PAY_COMPLETE.equals(paymentState)) {
//				this.billPaymentDebited(tenancyId, storeId, billNum, orderNum, chanel, paymentAmount, paymentList, currentTime, resultJson, result);
//			}
		}

		if (paymentAmount > 0) {
			StringBuilder queryPayStateSql = new StringBuilder("select count(case when payment_state=? or payment_state=? then 1 else null end) as count,sum(currency_amount) as currency_amount from pos_bill_payment where tenancy_id=? and store_id=? and bill_num=?");
			SqlRowSet rsp = paymentDao.query4SqlRowSet(queryPayStateSql.toString(), new Object[]
					{SysDictionary.PAYMENT_STATE_PAY, SysDictionary.PAYMENT_STATE_PAY_FAILURE, tenancyId, storeId, billNum});
			if (rsp.next()) {
				if (0 == rsp.getInt("count") && paymentAmount <= rsp.getDouble("currency_amount")) {
					paymentState = SysDictionary.PAYMENT_STATE_PAY_COMPLETE;
				}
			}
		}

			if (Constant.CODE_SUCCESS == result.getCode() && SysDictionary.PAYMENT_STATE_PAY_COMPLETE.equals(paymentState))
			{
				this.closedPosBill(tenancyId, storeId, billNum, DateUtil.format(reportDate), shiftId, posNum, optNum, isPrintBill, isInvoice, saleMode, billJson, printJson, "0", currentTime);

				result.setCode(Constant.CODE_SUCCESS);
				result.setMsg(Constant.PAYMENT_SUCCESS);
			}
			else
			{
				if (Constant.PAYMENT_STATUS_PAYING_CODE == result.getCode())
				{
					result.setCode(Constant.CODE_SUCCESS);
				}
			}
		}

		JSONObject resultJson = this.getPosBillPaymentByBillNum(tenancyId, storeId, billNum, tableCode, paymentAmount, discountAmount, difference, paymentState);

		List<JSONObject> resultList = new ArrayList<JSONObject>();
		resultList.add(resultJson);
		result.setData(resultList);
	}

	@Override
	public void getPosBillPaymentByBillNum(Data param, Data result) throws Exception
	{
		String tenancyId = param.getTenancy_id();
		int storeId = param.getStore_id();

		List<?> paramList = param.getData();
		if (paramList == null || paramList.isEmpty())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}
		JSONObject para = JSONObject.fromObject(paramList.get(0));
		String billNum = ParamUtil.getStringValueByObject(para, "bill_num", true, PosErrorCode.NOT_NULL_BILL_NUM);
		String tableCode = ParamUtil.getStringValueByObject(para, "table_code");

		double paymentAmount = 0d;
		double difference = 0d;
		double discountAmount = 0d;
		String paymentState = "";

		JSONObject billJson = paymentDao.getPosBillByBillnum(tenancyId, storeId, billNum);
		if (null != billJson)
		{
			paymentAmount = billJson.optDouble("payment_amount");
			difference = billJson.optDouble("difference");
			discountAmount = billJson.optDouble("discount_amount");
			paymentState = billJson.optString("payment_state");
			if (Tools.isNullOrEmpty(tableCode) && Tools.hv(billJson.optString("table_code")))
			{
				tableCode = billJson.optString("table_code");
			}
		}

		JSONObject resultJson = this.getPosBillPaymentByBillNum(tenancyId, storeId, billNum, tableCode, paymentAmount, discountAmount, difference, paymentState);

		List<JSONObject> resultList = new ArrayList<JSONObject>();
		resultList.add(resultJson);
		result.setData(resultList);
	}

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param tableCode
	 * @param paymentAmount
	 * @param discountAmount
	 * @param difference
	 * @param paymentState
	 * @return
	 * @throws Exception
	 */
	public JSONObject getPosBillPaymentByBillNum(String tenancyId, int storeId, String billNum, String tableCode, Double paymentAmount, Double discountAmount, Double difference, String paymentState) throws Exception {

		List<String> wlifePaymentClassList = Arrays.asList(SysDictionary.PAYMENT_CLASS_WLIFE_BALANCE, SysDictionary.PAYMENT_CLASS_WLIFE_CREDIT, SysDictionary.PAYMENT_CLASS_WLIFE_COUPON);
		
		List<JSONObject> paymentList = paymentDao.getPosBillPaymentByBillnum(tenancyId, storeId, billNum);
		double amount = 0d;
		String customerName = "";
		String mobil = "";
		String cardCode = "";
		String isCommit = "0";
		String verifySms = "";
		String verifyPassword = "";
		
		List<QueryPaymentDetail> paymentResultList = new ArrayList<QueryPaymentDetail>();
		for (JSONObject pay : paymentList)
		{
			String paymentDetailState = ParamUtil.getStringValueByObject(pay, "payment_state");
			String paymentClass = ParamUtil.getStringValueByObject(pay, "payment_class");

			if (!SysDictionary.PAYMENT_CHANGE_ENGLISH_NAME.equals(pay.optString("name_english")))
			{
				amount = DoubleHelper.add(amount, DoubleHelper.sub(pay.optDouble("currency_amount"), pay.optDouble("more_coupon"), DEFAULT_SCALE), DEFAULT_SCALE);
			}
			
			QueryPaymentDetail paymentDetailBean = JsonUtil.jsonToBean(pay, QueryPaymentDetail.class);
			//付款状态说明
			paymentDetailBean.setPayment_state_text(SysDictionary.PAYMENT_STATE_TEXT_MAP.get(paymentDetailState));
			
			if (SysDictionary.PAYMENT_STATE_PAY.equals(paymentDetailState) && wlifePaymentClassList.contains(paymentClass))
			{
				isCommit = "1";
				JSONObject paramCashJson = ParamUtil.getJSONObject(pay, "param_cach");
				customerName = ParamUtil.getStringValueByObject(paramCashJson, "customer_name");
				mobil = ParamUtil.getStringValueByObject(paramCashJson, "mobil");
				cardCode = ParamUtil.getStringValueByObject(paramCashJson, "card_code");
				verifySms = ParamUtil.getStringValueByObject(paramCashJson, "verify_sms");
				verifyPassword = ParamUtil.getStringValueByObject(paramCashJson, "verify_password");
				
				paymentDetailBean.setVerify_sms(verifySms);
				paymentDetailBean.setVerify_password(verifyPassword);
			}
			paymentResultList.add(paymentDetailBean);
		}
		difference = DoubleHelper.sub(paymentAmount, amount, DEFAULT_SCALE);

		paymentState = this.getBillPaymentStateByList(paymentAmount, difference, paymentList);

		JSONObject paymentJson = new JSONObject();
		paymentJson.put("bill_num", billNum);
		paymentJson.put("table_code", tableCode);
		paymentJson.put("payment_amount", paymentAmount);
		paymentJson.put("discount_amount", discountAmount);
		paymentJson.put("payment_state", paymentState);
		paymentJson.put("payment_state_text", SysDictionary.PAYMENT_STATE_TEXT_MAP.get(paymentState));
		paymentJson.put("difference", difference > 0 ? difference : 0d);
		paymentJson.put("change", difference < 0 ? difference : 0d);
		paymentJson.put("paymentlist", paymentResultList);

		if("1".equals(isCommit))
		{
			paymentJson.put("customer_name", customerName);
			paymentJson.put("card_code", cardCode);
			paymentJson.put("mobil", mobil);
			paymentJson.put("verify_sms", verifySms);
			paymentJson.put("verify_password", verifyPassword);
		}

		if (null != paymentList && 0 < paymentList.size() && SysDictionary.PAYMENT_STATE_PAY_COMPLETE.equals(paymentState))
		{
			JSONObject billJson = new JSONObject();
			billJson.put("bill_num", billNum);
			JSONObject invJo = paymentDao.getInvoiceInfo(tenancyId, storeId, billJson);
			String strUrlContent = "";
			if (null != invJo && !invJo.isEmpty())
			{
				strUrlContent = invJo.optString("url_content");
			}
			paymentJson.put("url_content", strUrlContent);

			List<JSONObject> memberList = memberDao.queryPosBillMember(tenancyId, storeId, billNum, null);
			if (null != memberList && 0 < memberList.size())
			{
				Double consumeBeforeBalance = 0d;
				Double consumeBalance = 0d;
				Double consumeAfterBalance = 0d;
	
				Double consumeBeforeCredit = 0d;
				Double consumeCredit = 0d;
				Double giveCredit = 0d;
				Double consumeAfterCredit = 0d;
	
				Map<String, JSONObject> memberMap = new HashMap<String, JSONObject>();
				for (JSONObject memberJson : memberList)
				{
					memberMap.put(memberJson.optString("type"), memberJson);
				}
	
				JSONObject memberJson = null;
				if (memberMap.containsKey(SysDictionary.BILL_MEMBERCARD_CZXF03))
				{
					memberJson = memberMap.get(SysDictionary.BILL_MEMBERCARD_CZXF03);
					customerName = memberJson.optString("customer_name");
					mobil = memberJson.optString("mobil");
					cardCode = memberJson.optString("card_code");
	
					consumeBeforeBalance = DoubleHelper.add(ParamUtil.getDoubleValueByObject(memberJson,"consume_before_main_balance"), ParamUtil.getDoubleValueByObject(memberJson,"consume_before_reward_balance"), DEFAULT_SCALE);
					consumeBalance = ParamUtil.getDoubleValueByObject(memberJson,"amount");
					consumeAfterBalance = DoubleHelper.add(ParamUtil.getDoubleValueByObject(memberJson,"consume_after_main_balance"), ParamUtil.getDoubleValueByObject(memberJson,"consume_after_reward_balance"), DEFAULT_SCALE);
				}
				else
				{
					memberJson = memberList.get(0);
					consumeBeforeBalance = DoubleHelper.add(ParamUtil.getDoubleValueByObject(memberJson,"consume_before_main_balance"), ParamUtil.getDoubleValueByObject(memberJson,"consume_before_reward_balance"), DEFAULT_SCALE);
					consumeAfterBalance = DoubleHelper.add(ParamUtil.getDoubleValueByObject(memberJson,"consume_after_main_balance"), ParamUtil.getDoubleValueByObject(memberJson,"consume_after_reward_balance"), DEFAULT_SCALE);
				}
	
				boolean isJFDX = false;
				if (memberMap.containsKey(SysDictionary.BILL_MEMBERCARD_JFDX05))
				{
					isJFDX = true;
					memberJson = memberMap.get(SysDictionary.BILL_MEMBERCARD_JFDX05);
					if (Tools.isNullOrEmpty(cardCode))
					{
						customerName = memberJson.optString("customer_name");
						mobil = memberJson.optString("mobil");
						cardCode = memberJson.optString("card_code");
					}
					consumeBeforeCredit = ParamUtil.getDoubleValueByObject(memberJson,"consume_before_credit");
					consumeCredit = ParamUtil.getDoubleValueByObject(memberJson,"credit");
					consumeAfterCredit = ParamUtil.getDoubleValueByObject(memberJson,"consume_after_credit");
				}
	
				boolean isJFZS = false;
				if (memberMap.containsKey(SysDictionary.BILL_MEMBERCARD_JFZS06))
				{
					isJFZS = true;
					memberJson = memberMap.get(SysDictionary.BILL_MEMBERCARD_JFZS06);
					if (Tools.isNullOrEmpty(cardCode))
					{
						customerName = memberJson.optString("customer_name");
						mobil = memberJson.optString("mobil");
						cardCode = memberJson.optString("card_code");
					}
					if (false == isJFDX)
					{
						consumeBeforeCredit = ParamUtil.getDoubleValueByObject(memberJson,"consume_before_credit");
					}
					giveCredit = ParamUtil.getDoubleValueByObject(memberJson,"credit");
					consumeAfterCredit = ParamUtil.getDoubleValueByObject(memberJson,"consume_after_credit");
				}
	
				if (Tools.isNullOrEmpty(cardCode))
				{
					memberJson = memberList.get(0);
					customerName = memberJson.optString("customer_name");
					mobil = memberJson.optString("mobil");
					cardCode = memberJson.optString("card_code");
				}
	
				if (false == isJFDX && false == isJFZS)
				{
					memberJson = memberList.get(0);
					consumeBeforeCredit = ParamUtil.getDoubleValueByObject(memberJson,"consume_before_credit");
					consumeAfterCredit =ParamUtil.getDoubleValueByObject(memberJson,"consume_after_credit");
				}
	
				paymentJson.put("customer_name", customerName);
				paymentJson.put("mobil", mobil);
				paymentJson.put("card_code", cardCode);
				paymentJson.put("consume_before_balance", consumeBeforeBalance);
				paymentJson.put("consume_balance", consumeBalance);
				paymentJson.put("consume_after_balance", consumeAfterBalance);
				paymentJson.put("consume_before_credit", consumeBeforeCredit);
				paymentJson.put("consume_credit", consumeCredit);
				paymentJson.put("give_credit", giveCredit);
				paymentJson.put("consume_after_credit", consumeAfterCredit);
	
			}
		}

		return paymentJson;
	}

	@Override
	public void paymentRefund(String tenancyId, int storeId, JSONObject paramJson, Data result, String operType) throws Exception {
		// TODO Auto-generated method stub

		String billNum = paramJson.optString("bill_num");
//		String opt_Num = paramJson.optString("optNum");
		String oldBillNum = "";
		Double billPaymentAmount = 0d;
		String chanel = SysDictionary.CHANEL_MD01;
		String orderNum = "";
//		String paymentSource = paramJson.optString("paymentSource");

		JSONObject billJson = paymentDao.getPosBillByBillnum(tenancyId, storeId, billNum);

		if (null != billJson) {
			billPaymentAmount = billJson.optDouble("payment_amount");
			chanel = billJson.optString("source");
			orderNum = billJson.optString("order_num");
			oldBillNum = billJson.optString("copy_bill_num");
		}

		List<JSONObject> paymentList = paymentDao.getBillPaymentByPaymentState(tenancyId, storeId, billNum, SysDictionary.PAYMENT_STATE_REFUNDING);

		JSONObject resultJson = new JSONObject();
		Timestamp currentTime = DateUtil.currentTimestamp();

        boolean isSuccess = true;
        for (JSONObject paymentJson : paymentList)
        {
//				paymentJson.put("optNum", opt_Num);
            boolean isCheck = false;
            String paymentClass = paymentJson.optString("payment_class");
            if (SysDictionary.PAYMENT_CLASS_COUPONS.equals(paymentClass) && Tools.hv(paymentJson.optString("number")))
            {
                isCheck = true;
            }

            this.getPaymentServiceBeanByPaymentClass(tenancyId, storeId, paymentClass, isCheck);
            posPaymentService.paymentRefund(tenancyId, storeId, billNum, oldBillNum, orderNum, chanel, billPaymentAmount, paymentJson, currentTime, resultJson, result, operType);

            isSuccess = (isSuccess && Constant.CODE_SUCCESS == result.getCode());
        }

        if (false == isSuccess)
        {
            result.setCode(Constant.CODE_PARAM_FAILURE);
        }
	}

//	/** 退款
//	 * @param tenancyId
//	 * @param storeId
//	 * @param billNum
//	 * @param oldBillNum
//	 * @param orderNum
//	 * @param chanel
//	 * @param billPaymentAmount
//	 * @param paymentJson
//	 * @param currentTime
//	 * @param resultJson
//	 * @param result
//	 * @param operType
//	 * @throws Exception
//	 */
//	private void paymentRefund(String tenancyId, int storeId, String billNum,String oldBillNum,String orderNum,String chanel,Double billPaymentAmount,JSONObject paymentJson,Timestamp currentTime,JSONObject resultJson,Data result,String operType) throws Exception
//	{
//		boolean isCheck = false;
//		String paymentClass = paymentJson.optString("payment_class");
//		if (SysDictionary.PAYMENT_CLASS_COUPONS.equals(paymentClass) && Tools.hv(paymentJson.optString("number")))
//		{
//			isCheck = true;
//		}
//
//		this.getPaymentServiceBeanByPaymentClass(tenancyId, storeId, paymentClass, isCheck);
//
//		// Data resultData = new Data();
//		posPaymentService.paymentRefund(tenancyId, storeId, billNum, oldBillNum, orderNum, chanel, billPaymentAmount, paymentJson, currentTime, resultJson, result, operType);
//
//		if (Constant.CODE_SUCCESS == result.getCode())
//		{
//			posPaymentService.paymentRefundSuccess(tenancyId, storeId, billNum, paymentJson);
//		}
//		else if (Constant.CODE_CONN_EXCEPTION == result.getCode())
//		{
//			result.setCode(Constant.CODE_CONN_EXCEPTION);
//			result.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
//		}
//		else
//		{
//			posPaymentService.paymentRefundFailure(tenancyId, storeId, billNum, paymentJson.optInt("id"), result.getCode(), result.getMsg());
//			this.getPaymentException(result, result.getCode(), result.getMsg());
//		}
//	}

	@Override
	public Data paymentRegain(String tenancyId, int storeId, String billNum, double paymentAmount, String tableCode, Date reportDate, int shiftId, String posNum, String optNum, String optName,String chanel,String orderNum,int regainCount) throws Exception
	{
		Timestamp currentTime = DateUtil.currentTimestamp();

		StringBuilder queryPaymentSql = new StringBuilder("select pw.payment_class,bp.* from pos_bill_payment_regain bp left join payment_way pw on bp.jzid=pw.id where bp.store_id=? and bp.bill_num=? and bp.regain_count=? order by bp.id");
		List<JSONObject> paymentList = paymentDao.query4Json(tenancyId, queryPaymentSql.toString(), new Object[]
		{ storeId, billNum, regainCount });

		List<Object[]> batchArgs = new ArrayList<Object[]>();
		boolean isSuccess= true;
		for (JSONObject paymentJson : paymentList)
		{
			JSONObject resultJson = new JSONObject();
			Data resultData = new Data();

			// 更新currency_amount字段为负值， 因为是从备份表pos_bill_payment_regain里取值，取出的值是正的
			paymentJson.put("currency_amount", -paymentJson.optDouble("currency_amount"));
//			paymentJson.put("optNum", optNum);
			boolean isCheck = false;
			String paymentClass = paymentJson.optString("payment_class");
			if (SysDictionary.PAYMENT_CLASS_COUPONS.equals(paymentClass) && Tools.hv(paymentJson.optString("number")))
			{
				isCheck = true;
			}

			this.getPaymentServiceBeanByPaymentClass(tenancyId, storeId,paymentClass,isCheck);
			posPaymentService.paymentRegain(tenancyId, storeId, billNum, orderNum, chanel, paymentAmount, paymentJson, currentTime, resultJson, resultData, Type.REGAIN_BILL.name());

			if (Constant.CODE_SUCCESS == resultData.getCode())
			{
				batchArgs.add(new Object[]
				{ paymentJson.optString("bill_code"), SysDictionary.PAYMENT_STATE_REFUND_COMPLETE, tenancyId, storeId, billNum, paymentJson.optInt("id") });
			}
			else if (Constant.CODE_CONN_EXCEPTION == resultData.getCode())
			{
				batchArgs.add(new Object[]
				{ null, SysDictionary.PAYMENT_STATE_REFUNDING, tenancyId, storeId, billNum, paymentJson.optInt("id") });
			}
			else
			{
				batchArgs.add(new Object[]
				{ null, SysDictionary.PAYMENT_STATE_REFUND_FAILURE, tenancyId, storeId, billNum, paymentJson.optInt("id") });
			}

			isSuccess = (isSuccess && Constant.CODE_SUCCESS == resultData.getCode());
		}

		if (batchArgs.size() > 0)
		{
			StringBuilder insertPaySql = new StringBuilder("update pos_bill_payment_regain set bill_code=?,payment_state=? where tenancy_id=? and store_id=? and bill_num=? and id=?");
			paymentDao.batchUpdate(insertPaySql.toString(), batchArgs);
		}

		Data result = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
		if(false ==isSuccess)
		{
			result.setCode(PosErrorCode.REGAIN_BILL_REFUND_FAILURE.getNumber());
		}
		return result;
	}

	/**
	 * @param paymentAmount
	 * @param difference
	 * @param paymentList
	 * @return
	 */
	private String getBillPaymentStateByList(Double paymentAmount, Double difference, List<JSONObject> paymentList)
	{
		String paymentState = SysDictionary.PAYMENT_STATE_NOTPAY;
		if (null != paymentList && 0 < paymentList.size())
		{
			if (0 < difference)
			{
				paymentState = SysDictionary.PAYMENT_STATE_PAY;
			}
			else
			{
				paymentState = SysDictionary.PAYMENT_STATE_PAY_COMPLETE;
				for (JSONObject pay : paymentList)
				{
					if (SysDictionary.PAYMENT_STATE_PAY_COMPLETE.equals(paymentState) && SysDictionary.PAYMENT_STATE_PAY_COMPLETE.equals(pay.optString("payment_state")))
					{
						paymentState = SysDictionary.PAYMENT_STATE_PAY_COMPLETE;
					}
					else
					{
						paymentState = SysDictionary.PAYMENT_STATE_PAY;
					}
				}
			}
		}
		return paymentState;
	}



	@Override
	public void prepareThirdCoupons(Data param, Data result) throws Exception
	{
		String tenancyId = param.getTenancy_id();
		int storeId = param.getStore_id();
		List<?> paramList = param.getData();
		if (null != paramList && 0 < paramList.size())
		{
			JSONObject paramJson = JSONObject.fromObject(paramList.get(0));

            String coupousSource=ParamUtil.getStringValueByObject(paramJson, "coupon_source"); //为了向前兼容,不传时默认为美团
            String couponCode = ParamUtil.getStringValueByObject(paramJson, "coupon_code");

           /* String sql = "select count(*) from pos_bill_payment bp where bp.tenancy_id=? and bp.store_id=? and bp.number=?";
            int count = paymentDao.queryForInt(sql, new Object[]
                    {tenancyId, storeId, couponCode});

            if (0 < count) {
                throw SystemException.getInstance(CrmErrorCode.COUPONS_CODE_USED);
            }*/

            if("douyin".equals(coupousSource)){
                douyinCouponsService.couponsPrepare(tenancyId,storeId,paramJson,result);
            }else {
                meituanCouponsService.couponsPrepare(tenancyId, storeId, paramList, result);
            }
		}
	}

    @Override
    public void checkCoupons(Data param, Data result) throws Exception {
        String tenancyId = param.getTenancy_id();
        int storeId = param.getStore_id();
        List<?> paramList = param.getData();
        if (null != paramList && 0 < paramList.size())
        {
            JSONObject paramJson = JSONObject.fromObject(paramList.get(0));

            String coupousSource=ParamUtil.getStringValueByObject(paramJson, "coupon_source"); //为了向前兼容,不传时默认为美团

            if("douyin".equals(coupousSource)){
                douyinCouponsService.couponsCheck(tenancyId,storeId,paramJson,result);
            }
        }
    }

    /**
	 * 扣款
	 *
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param payList
	 * @param result
	 * @throws Exception
	 */
	private void billPaymentDebited(String tenancyId, int storeId, String billNum, String orderNum, String chanel, Double paymentAmount, List<JSONObject> payList, Timestamp currentTime, JSONObject resultJson, Data result) throws Exception
	{
		// String currentTimeStr = DateUtil.format(currentTime);
		// JSONObject resultJson = new JSONObject();
		for (JSONObject payItem : payList)
		{
			String paymentClass = payItem.optString("payment_class");
			boolean isCheck = false;
			if (SysDictionary.PAYMENT_CLASS_COUPONS.equals(paymentClass) && Tools.hv(payItem.optString("number")))
			{
				isCheck = true;
			}
			this.getPaymentServiceBeanByPaymentClass(tenancyId, storeId,paymentClass,isCheck);

			Data resultData = new Data();
			posPaymentService.paymentDebit(tenancyId, storeId, billNum, orderNum, chanel, paymentAmount, payItem, currentTime, resultJson, resultData);
			if (Constant.CODE_SUCCESS != resultData.getCode())
			{
				result.setCode(resultData.getCode());
				result.setMsg(resultData.getMsg());
			}
////			resultData.setCode(Constant.CODE_CONN_EXCEPTION);
//			
//			if (Constant.CODE_SUCCESS == resultData.getCode())
//			{
//				posPaymentService.paymentDebitSuccess(tenancyId, storeId, billNum, payItem);
//				// result.setCode(Constant.CODE_SUCCESS);
//				// result.setMsg(Constant.PAYMENT_SUCCESS);
//			}
//			else if (Constant.CODE_CONN_EXCEPTION == resultData.getCode())
//			{
//				result.setCode(Constant.CODE_CONN_EXCEPTION);
//				result.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
//			}
//			else if (Constant.PAYMENT_STATUS_PAYING_CODE == resultData.getCode())
//			{
//				result.setCode(Constant.PAYMENT_STATUS_PAYING_CODE);
//				result.setMsg(PropertiesLoader.getProperty(String.valueOf(resultData.getCode())));
//			}
//			else
//			{
////				posPaymentService.paymentDebitFailure(tenancyId, storeId, billNum, payItem.optInt("id"), resultData.getCode(), resultData.getMsg());
//				this.deletePaymentForFailure(tenancyId, storeId, payItem, currentTime);
//				this.getPaymentException(result, resultData.getCode(), resultData.getMsg());
//			}
		}

//		this.updateBillPaymentState(tenancyId, storeId, billNum, paymentAmount, SysDictionary.PAYMENT_STATE_PAY);
		// List<JSONObject> resultList = new ArrayList<JSONObject>();
		// resultList.add(resultJson);
		// result.setData(resultList);
	}



	@Override
	public void closedAcewillPosBill(String tenancyId, int storeId, String billNum, String reportDate, int shiftId, String posNum, String optNum, String isPrint, String isInvoice, JSONObject resultJson, JSONObject printJson, String isOnlinePay,Timestamp currentTime) throws Exception
	{
		JSONObject billJson = paymentDao.getPosBillByBillnum(tenancyId, storeId, billNum);

		double paymentAmount = 0d;
		double discountAmount = 0d;
		String paymentState = null;
		String tableCode = null;
		double difference = 0d;
		String saleMode="";
		if (null != billJson)
		{
			paymentAmount = billJson.optDouble("payment_amount");
			discountAmount = billJson.optDouble("discount_amount");
			paymentState = billJson.optString("payment_state");
			tableCode = billJson.optString("table_code");
			difference = billJson.optDouble("difference");
			saleMode = billJson.optString("sale_mode");
		}

		closedPosBill(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, isPrint, isInvoice,saleMode, billJson, printJson, isOnlinePay, currentTime);

		resultJson = this.getPosBillPaymentByBillNum(tenancyId, storeId, billNum, tableCode, paymentAmount, discountAmount, difference, paymentState);
	}

	/**
	 * 关闭账单
	 *
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param reportDate
	 * @param shiftId
	 * @param posNum
	 * @param optNum
	 * @param isPrint
	 * @return
	 * @throws Exception
	 */
	protected void closedPosBill(String tenancyId, int storeId, String billNum, String reportDate, int shiftId, String posNum, String optNum, String isPrint, String isInvoice,String saleMode,JSONObject billJson, JSONObject printJson, String isOnlinePay,Timestamp currentTime) throws Exception
	{
		synchronized (billNum)
		{
//			JSONObject billJson = paymentDao.getPosBillByBillnum(tenancyId, storeId, billNum);

			double paymentAmount = 0d;
//			double discountAmount = 0d;
			String paymentState = null;
			String tableCode = null;
			String fictitiousTable = null;
			String billProperty = null;
			String orderNum = null;
			String chanel = null;
			String batchNum = null;
			double difference = 0d;
			if (null != billJson)
			{
				paymentAmount = billJson.optDouble("payment_amount");
//				discountAmount = billJson.optDouble("discount_amount");
				paymentState = billJson.optString("payment_state");
				tableCode = billJson.optString("table_code");
				fictitiousTable = billJson.optString("fictitious_table");
				billProperty = billJson.optString("bill_property");
				orderNum = billJson.optString("order_num");
				chanel = billJson.optString("source");
				difference = billJson.optDouble("difference");
				if(Tools.isNullOrEmpty(fictitiousTable))
				{
					fictitiousTable = billJson.optString("table_code");
				}
			}

			boolean isClosed = false;
			if (false == SysDictionary.BILL_PROPERTY_CLOSED.equals(billProperty))
			{
				StringBuilder qureyOrganSql = new StringBuilder("select format_state from organ where tenancy_id=? and id=?");
				SqlRowSet rsOrg = paymentDao.query4SqlRowSet(qureyOrganSql.toString(), new Object[]
				{ tenancyId, storeId });
				String formatState = null;
				if (rsOrg.next())
				{
					formatState = rsOrg.getString("format_state");
				}

				String mode = "01";
				if ("1".equals(formatState))
				{
					mode = paymentDao.getSysParameter(tenancyId, storeId, "ZCDCMS");// 01,普通;02,自助;
				}

				List<JSONObject> paymentList = paymentDao.getBillPaymentByBillnum(tenancyId, storeId, billNum);
				Double amount = 0d;
				Double couponsAmount = 0d;// 优惠劵金额
				Double cashAmount = 0d;// 现金金额
				Map<String, String> outTradeNoPaymentMap = new HashMap<String, String>();
				for(JSONObject paymentJson : paymentList)
				{
					Double currencyAmount = ParamUtil.getDoubleValueByObject(paymentJson, "currency_amount");
					Double moreCoupon = ParamUtil.getDoubleValueByObject(paymentJson, "more_coupon");
					String paymentClass = ParamUtil.getStringValueByObject(paymentJson, "payment_class");

					currencyAmount = DoubleHelper.sub(currencyAmount, moreCoupon, DEFAULT_SCALE);
					amount = DoubleHelper.add(amount, currencyAmount, DEFAULT_SCALE);

					if (SysDictionary.PAYMENT_CLASS_CASH.equals(paymentClass))
					{
						cashAmount = DoubleHelper.add(cashAmount, currencyAmount, DEFAULT_SCALE);
					}
					else if (SysDictionary.PAYMENT_CLASS_COUPONS.equals(paymentClass)
                            || SysDictionary.PAYMENT_CLASS_WLIFE_COUPON.equals(paymentClass)
                            || SysDictionary.PAYMENT_CLASS_MEIDA_COUPONS_PAY.equals(paymentClass)
                            || SysDictionary.PAYMENT_CLASS_DOUYIN_COUPONS_PAY.equals(paymentClass)) {
						couponsAmount = DoubleHelper.add(couponsAmount, currencyAmount, DEFAULT_SCALE);
					}

                    logger.info("付款方式："+paymentClass+",当前付款状态:"+paymentState);

					// 支付暂存,发起扣款
					if (SysDictionary.PAYMENT_STATE_PAY_PRESERVE.equals(paymentJson.optString("payment_state")))
					{
						String outTradeNo = paymentJson.optString("out_trade_no");
						if (null != outTradeNo && outTradeNoPaymentMap.containsKey(outTradeNo))
						{
							paymentJson.put("payment_state", outTradeNoPaymentMap.get(outTradeNo));
						}
						else
						{
							this.getPaymentServiceBeanByPaymentClass(tenancyId, storeId, paymentClass, false);
							JSONObject resultJson = new JSONObject();
							Data resultData = new Data();
							posPaymentService.paymentDebit(tenancyId, storeId, billNum, orderNum, chanel, paymentAmount, paymentJson, currentTime, resultJson, resultData);

							String ditPaymentState = ParamUtil.getDataValue(resultData, "payment_state");
							paymentJson.put("payment_state", ditPaymentState);
							outTradeNoPaymentMap.put(outTradeNo, ditPaymentState);
						}
					}/*else if (SysDictionary.PAYMENT_CLASS_MEIDA_COUPONS_PAY.equals(paymentClass)&&SysDictionary.PAYMENT_STATE_PAY.equals(paymentJson.optString("payment_state"))) {

                        this.getPaymentServiceBeanByPaymentClass(tenancyId, storeId, paymentClass, false);
                        JSONObject resultJson = new JSONObject();
                        Data resultData = new Data();
                        posPaymentService.paymentDebit(tenancyId, storeId, billNum, orderNum, chanel, paymentAmount, paymentJson, currentTime, resultJson, resultData);

                    }*/
				}
				difference = DoubleHelper.sub(paymentAmount, amount, DEFAULT_SCALE);
				paymentState = this.getBillPaymentStateByList(paymentAmount, difference, paymentList);

//				String sql = new String("select count(nullif(payment_state,?)) as count,coalesce(sum(currency_amount-more_coupon),0) as amount from pos_bill_payment where tenancy_id=? and store_id = ? and bill_num =?");
//				SqlRowSet rs = paymentDao.query4SqlRowSet(sql.toString(), new Object[]
//				{ SysDictionary.PAYMENT_STATE_PAY_COMPLETE, tenancyId, storeId, billNum });
//
//				if (rs.next())
//				{
//					difference = DoubleHelper.sub(paymentAmount, rs.getDouble("amount"), DEFAULT_SCALE);
//					if ((0 == rs.getInt("count") && paymentAmount <= rs.getDouble("amount")) || (this.findSecondPay(billNum, tenancyId, storeId) && paymentAmount <= rs.getDouble("amount")))
//					{
//						paymentState = SysDictionary.PAYMENT_STATE_PAY_COMPLETE;
//					}
//				}

				if (difference <= 0 && SysDictionary.PAYMENT_STATE_PAY_COMPLETE.equals(paymentState))
				{
					isClosed = true;

					double moreCoupon = 0d;
					double changeAmount = 0d;// 找零金额
					Integer payWayId = null;//本币对应的jzid
					if (difference < 0)
					{ // 计算优惠券多收礼券
//						String paySql = new String(
//								"select sum(coalesce(p.currency_amount,0)) as total_amount,sum(case when w.payment_class=? or w.payment_class=? or w.payment_class=? then coalesce(p.currency_amount,0) else 0 end) as coupons_amount,sum(case when w.payment_class=? then coalesce(p.currency_amount,0) else 0 end) as cash_amount from pos_bill_payment p left join payment_way w on p.tenancy_id=w.tenancy_id and p.jzid=w.id where p.tenancy_id=? and p.store_id=? and p.bill_num=?");
//						rs = paymentDao.query4SqlRowSet(paySql.toString(), new Object[]
//						{ SysDictionary.PAYMENT_CLASS_COUPONS, SysDictionary.PAYMENT_CLASS_WLIFE_COUPON, SysDictionary.PAYMENT_CLASS_MEIDA_COUPONS_PAY, SysDictionary.PAYMENT_CLASS_CASH, tenancyId, storeId, billNum });
//
//						double couponsAmount = 0d;// 优惠劵金额
//						double cashAmount = 0d;// 现金金额
//						
//						if (rs.next())
//						{
//							couponsAmount = rs.getDouble("coupons_amount");
//							cashAmount = rs.getDouble("cash_amount");
//						}

						if (cashAmount > 0)
						{
							if (Math.abs(difference) > cashAmount)
							{
								changeAmount = -cashAmount;
							}
							else
							{
								changeAmount = difference;
							}
						}

						if (couponsAmount > 0)
						{
							moreCoupon = Math.abs(DoubleHelper.sub(difference, changeAmount, DEFAULT_SCALE));
						}

						difference = changeAmount;// 账单付款差额=找零

						if (changeAmount < 0 && cashAmount > 0)
						{ 	// 在支付流水表插入找零记录
							// 保存的是本币对应的jzid
							JSONObject paymentWayJson = paymentDao.getPaymentWayForStandard(tenancyId, storeId);
							if (null != paymentWayJson && paymentWayJson.containsKey("payment_id"))
							{
								payWayId = paymentWayJson.optInt("payment_id");
							}
							else
							{
								throw new SystemException(PosErrorCode.NOT_EXIST_PAY_WAY_STANDARD_ERROR);
							}

							paymentDao.insertPosBillPayment(tenancyId, storeId, billNum, DateUtil.parseDate(reportDate), shiftId, posNum, optNum, tableCode, SysDictionary.PAYMENT_CLASS_CASH, payWayId, SysDictionary.PAYMENT_CHANGE_NAME, SysDictionary.PAYMENT_CHANGE_ENGLISH_NAME, 1d, changeAmount,
									1d, changeAmount, null, null, "N", null, null, null, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);

//							paymentDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, DateUtil.parseDate(reportDate), shiftId, posNum, optNum, tableCode, SysDictionary.PAYMENT_CLASS_CASH, payWayId, "找零", "change", 1d, difference, 1d, difference, null, null, "N", null, null, null, currentTime,
//									SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);
						}
					}

					if (moreCoupon != 0d)
					{ // 多收礼券的钱存入支付明细表中
						String updateMoreCouponSql = "update pos_bill_payment set more_coupon = ? where id = (select p.id from pos_bill_payment p where p.bill_num = ? and (p.type = ? or p.type = ? or p.type = ? ) and p.payment_state = ? and p.tenancy_id = ? and p.store_id = ? and p.more_coupon=0 order by p.currency_amount desc limit 1) ";
						paymentDao.update(updateMoreCouponSql, new Object[]
						{ moreCoupon, billNum, SysDictionary.PAYMENT_CLASS_COUPONS, SysDictionary.PAYMENT_CLASS_WLIFE_COUPON, SysDictionary.PAYMENT_CLASS_MEIDA_COUPONS_PAY, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, tenancyId, storeId });
					}

					String updatePaymentSql = new String("update pos_bill_payment set shift_id=? where bill_num = ? and store_id =? and tenancy_id=?");
					paymentDao.update(updatePaymentSql, new Object[]
					{ shiftId, billNum, storeId, tenancyId });

					String paySql = new String("select coalesce(sum(more_coupon),0) as more_coupon from pos_bill_payment p where p.tenancy_id=? and p.store_id=? and p.bill_num=?");
					SqlRowSet rs = paymentDao.query4SqlRowSet(paySql.toString(), new Object[]
					{ tenancyId, storeId, billNum });
					if (rs.next())
					{
						moreCoupon = rs.getDouble("more_coupon");
					}

					// GJ20160829
					if ("2".equals(formatState))
					{
						// 关闭账单
						StringBuilder updateBillSql = new StringBuilder(
								"update pos_bill set more_coupon=?,difference =?,bill_property =?,payment_time =?,pos_num =?,cashier_num =?,shift_id =?,payment_state=?,sale_mode=?,table_code=coalesce((select pt.stable from pos_tablestate pt where pt.table_code=? and pt.store_id=? limit 1),?),shop_real_amount=(coalesce(payment_amount,0)-coalesce(platform_charge_amount,0)-coalesce(total_fees,0)) where tenancy_id=? and store_id = ? and bill_num =? and payment_amount<=(select sum(currency_amount) from pos_bill_payment where tenancy_id=? and store_id=? and bill_num=?)");
						int count = paymentDao.update(updateBillSql.toString(), new Object[]
						{ moreCoupon, difference, SysDictionary.BILL_PROPERTY_CLOSED, currentTime, posNum, optNum, shiftId, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, saleMode, tableCode, storeId, tableCode, tenancyId, storeId, billNum, tenancyId, storeId, billNum });
						if (0 == count)
						{
							// 修改账单状态失败,删除找零记录
							if (0 < Math.abs(changeAmount))
							{
								String deleteSql = new String("delete from pos_bill_payment where tenancy_id=? and store_id=? and bill_num=? and jzid=? and name_english=?");
								paymentDao.update(deleteSql, new Object[]
								{ tenancyId, storeId, billNum, payWayId, SysDictionary.PAYMENT_CHANGE_ENGLISH_NAME });
							}
							return;
						}

						//快餐,下单未记录销售模式,结账修改销售模式;
						//快餐,口味备注为:整单备注+单品备注
						String updateItemSql2 = new String(
								"update pos_bill_item bi set sale_mode = (case when bi.sale_mode is null or bi.sale_mode='' then ? else bi.sale_mode end ),item_shift_id=?,item_taste = trim(COALESCE(b.bill_taste,'')||' '||COALESCE(bi.item_taste,'')) from pos_bill b where bi.tenancy_id=b.tenancy_id and bi.store_id=b.store_id and bi.bill_num=b.bill_num and bi.tenancy_id=? and bi.store_id=? and bi.bill_num=?");
						paymentDao.update(updateItemSql2, new Object[]
						{ saleMode, shiftId, tenancyId, storeId, billNum });
					}
					else
					{
						// 关闭账单
						StringBuilder updateBillSql = new StringBuilder(
								"update pos_bill set more_coupon=?,difference =?,bill_property =?,payment_time =?,pos_num =?,cashier_num =?,shift_id =?,payment_state=?,shop_real_amount=(coalesce(payment_amount,0)-coalesce(platform_charge_amount,0)-coalesce(total_fees,0)) where tenancy_id=? and store_id = ? and bill_num =? and payment_amount<=(select sum(currency_amount) from pos_bill_payment where tenancy_id=? and store_id=? and bill_num=?)");
						int count = paymentDao.update(updateBillSql.toString(), new Object[]
						{ moreCoupon, difference, SysDictionary.BILL_PROPERTY_CLOSED, currentTime, posNum, optNum, shiftId, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, tenancyId, storeId, billNum, tenancyId, storeId, billNum });
						if (0 == count)
						{
							// 修改账单状态失败,删除找零记录
							if (0 < Math.abs(changeAmount))
							{
								String deleteSql = new String("delete from pos_bill_payment where tenancy_id=? and store_id=? and bill_num=? and jzid=? and name_english=?");
								paymentDao.update(deleteSql, new Object[]
								{ tenancyId, storeId, billNum, payWayId, SysDictionary.PAYMENT_CHANGE_ENGLISH_NAME });
							}
							return;
						}

						String updateItemSql = new String("update pos_bill_item set item_shift_id=? where bill_num = ? and store_id =? and tenancy_id=?");
						paymentDao.update(updateItemSql, new Object[]
						{ shiftId, billNum, storeId, tenancyId });

						if ("1".equals(formatState) && "01".equals(mode))
						{// 正餐 更新桌位 更改桌位的状态，将桌位状态改为"空闲"
							StringBuffer updateTableState = new StringBuffer("update pos_tablestate set state = ?,lock_pos_num = ?,lock_opt_num=? where table_code = ? and store_id = ? and tenancy_id = ?");
							paymentDao.update(updateTableState.toString(), new Object[]
							{ SysDictionary.TABLE_STATE_FREE, null, null, fictitiousTable, storeId, tenancyId });
						}
					}




					try
					{
						// 写日志pos_log
						paymentDao.savePosLog(tenancyId, storeId, posNum, optNum, optNum, shiftId, DateUtil.parseDate(reportDate), com.tzx.pos.base.Constant.TITLE, "结账-关闭账单", "账单编号:" + billNum + "; 应付金额:" + String.valueOf(paymentAmount),
								"找零金额:" + String.valueOf(changeAmount) + "; 多收礼券:" + String.valueOf(moreCoupon), DateUtil.currentTimestamp());
					}
					catch (Exception e)
					{
						logger.info("报错日志报错:", e);
						e.printStackTrace();
					}
				}

				// 账单关闭后续操作
				if (isClosed)
				{
					this.closedPosBillSuccess(tenancyId, storeId, reportDate, shiftId, chanel, optNum, posNum, billNum, orderNum, tableCode, paymentAmount, currentTime, isOnlinePay, billJson, printJson, isInvoice, formatState, mode, isPrint);
				}
			}

//			List<JSONObject> paymentList = paymentDao.getBillPaymentByBillnum(tenancyId, storeId, billNum);
//			double amount = 0d;
//			for (JSONObject pay : paymentList)
//			{
//				pay.put("payment_state_text", SysDictionary.PAYMENT_STATE_TEXT_MAP.get(pay.optString("payment_state")));
//				amount = DoubleHelper.add(amount, pay.optDouble("currency_amount"), DEFAULT_SCALE);
//			}
//
//			difference = DoubleHelper.sub(paymentAmount, amount, DEFAULT_SCALE);
//
//			paymentState = this.getBillPaymentStateByList(paymentAmount, difference, paymentList);
//
//			resultJson.put("bill_num", billNum);
//			resultJson.put("table_code", tableCode);
//			resultJson.put("payment_amount", paymentAmount);
//			resultJson.put("discount_amount", discountAmount);
//			resultJson.put("payment_state", paymentState);
//			resultJson.put("payment_state_text", SysDictionary.PAYMENT_STATE_TEXT_MAP.get(paymentState));
//			resultJson.put("difference", difference > 0 ? difference : 0d);
//			resultJson.put("change", difference < 0 ? difference : 0d);
//			resultJson.put("paymentlist", paymentList);

//			resultJson  = this.getPosBillPaymentByBillNum(tenancyId, storeId, billNum, tableCode, paymentAmount, discountAmount, difference, paymentState);
		}
	}

	/** 计算找零金额
	 * @param difference
	 * @param cashAmount
	 * @return
	 */
	protected Double getChangeAmount(Double difference, Double cashAmount)
	{
		double changeAmount = 0d;// 找零金额
		if (difference < 0 && cashAmount > 0)
		{
			if (Math.abs(difference) > cashAmount)
			{
				changeAmount = -cashAmount;
			}
			else
			{
				changeAmount = difference;
			}
		}
		return changeAmount;
	}


	public void manualClosedPosBill(Data param, Data result, JSONObject printJson) throws Exception {
		String tenancyId = param.getTenancy_id();
		int storeId = param.getStore_id();

		List<?> paramList = param.getData();
		if (paramList == null || paramList.isEmpty()) {
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}

		JSONObject para = JSONObject.fromObject(paramList.get(0));
		Date reportDate = DateUtil.parseDate(ParamUtil.getDateStringValue(para, "report_date"));
		int shiftId = ParamUtil.getIntegerValueByObject(para, "shift_id");
		String posNum = ParamUtil.getStringValueByObject(para, "pos_num");
		String optNum = ParamUtil.getStringValueByObject(para, "opt_num");
		String billNum = ParamUtil.getStringValueByObject(para, "bill_num", true, PosErrorCode.NOT_NULL_BILL_NUM);
		String isInvoice = ParamUtil.getStringValueByObject(para, "is_invoice");
		String isOnlinePay = "0";
		if (para.containsKey("is_online_payment")) {
			isOnlinePay = String.valueOf(para.get("is_online_payment"));
		}

		String isPrintBill = ParamUtil.getStringValueByObject(para, "isprint_bill");
		if (Tools.isNullOrEmpty(isPrintBill)) {
			isPrintBill = "N";
		}

		Timestamp currentTime = DateUtil.currentTimestamp();

		//获取账单信息
		JSONObject billJson = paymentDao.getPosBillByBillnum(tenancyId, storeId, billNum);
		Double paymentAmount = 0d;
		String orderNum = null;
		String fictitiousTable = null;
		String chanel = null;
		String saleMode = null;
		String tableCode = null;
		if (null != billJson && !billJson.isEmpty()) {
			paymentAmount = ParamUtil.getDoubleValueByObject(billJson, "payment_amount");
			orderNum = ParamUtil.getStringValueByObject(billJson, "order_num");
			fictitiousTable = ParamUtil.getStringValueByObject(billJson, "fictitious_table");
			chanel = ParamUtil.getStringValueByObject(billJson, "source");
			saleMode = ParamUtil.getStringValueByObject(billJson, "sale_mode");
			tableCode = ParamUtil.getStringValueByObject(billJson, "table_code");
		}


		List<JSONObject> paymentList = paymentDao.getBillPaymentByBillnum(tenancyId, storeId, billNum);
		Double moreCoupon = 0d;
		Double payAmount = 0d;
		String paymentState = SysDictionary.PAYMENT_STATE_PAY_COMPLETE;
		for (JSONObject payJson : paymentList) {
			moreCoupon = DoubleHelper.add(moreCoupon, ParamUtil.getDoubleValueByObject(payJson, "more_coupon"), DEFAULT_SCALE);
			payAmount = DoubleHelper.add(payAmount, ParamUtil.getDoubleValueByObject(payJson, "currency_amount"), DEFAULT_SCALE);

			if (SysDictionary.PAYMENT_STATE_PAY_COMPLETE.equals(paymentState) && SysDictionary.PAYMENT_STATE_PAY_COMPLETE.equals(payJson.optString("payment_state"))) {
				paymentState = SysDictionary.PAYMENT_STATE_PAY_COMPLETE;
			} else {
				paymentState = SysDictionary.PAYMENT_STATE_PAY;
			}
		}

		Double difference = DoubleHelper.sub(paymentAmount, payAmount, DEFAULT_SCALE);

		if (paymentAmount.doubleValue() > DoubleHelper.sub(payAmount, moreCoupon, DEFAULT_SCALE).doubleValue()) {
			throw SystemException.getInstance(PosErrorCode.BILL_DIFFERENCE_MORE_ZERO_ERROR);
		} else if (!SysDictionary.PAYMENT_STATE_PAY_COMPLETE.equals(paymentState) || paymentAmount.doubleValue() != DoubleHelper.sub(payAmount, moreCoupon, DEFAULT_SCALE).doubleValue()) {
			throw SystemException.getInstance(PosErrorCode.BILL_PAYMENT_STATE_PAYING_ERROR);
		}

		//关单
		StringBuilder updateBillSql = new StringBuilder(
				"update pos_bill set more_coupon=?,difference =?,bill_property =?,payment_time =?,pos_num =?,cashier_num =?,shift_id =?,payment_state=?,shop_real_amount=(coalesce(payment_amount,0)-coalesce(platform_charge_amount,0)-coalesce(total_fees,0)) where tenancy_id=? and store_id = ? and bill_num =? ");
		paymentDao.update(updateBillSql.toString(), new Object[]
				{moreCoupon, difference, SysDictionary.BILL_PROPERTY_CLOSED, currentTime, posNum, optNum, shiftId, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, tenancyId, storeId, billNum});

		String updateItemSql = new String("update pos_bill_item set item_shift_id=? where bill_num = ? and store_id =? and tenancy_id=?");
		paymentDao.update(updateItemSql, new Object[]{shiftId, billNum, storeId, tenancyId});

		String updatePaymentSql = new String("update pos_bill_payment set shift_id=? where bill_num = ? and store_id =? and tenancy_id=?");
		paymentDao.update(updatePaymentSql, new Object[]{shiftId, billNum, storeId, tenancyId});

		// 更改桌位的状态，将桌位状态改为"空闲"
		StringBuffer updateTableState = new StringBuffer("update pos_tablestate set state = ?,lock_pos_num = ?,lock_opt_num=? where table_code = ? and store_id = ? and tenancy_id = ?");
		paymentDao.update(updateTableState.toString(), new Object[]{SysDictionary.TABLE_STATE_FREE, null, null, fictitiousTable, storeId, tenancyId});

		if ("0".equals(isOnlinePay)) {
			try {
				//消费赠送积分
				String isMeiDaCard = paymentDao.getSysParameter(tenancyId, storeId, "SFSYXMDCRMMK");// 是否使用美大会员

				String customerType = paymentDao.getCustomerType(tenancyId, storeId);

				if ("Y".equals(isMeiDaCard)) {

					int isUseMeiDacard = 0;// 账单付款方式中是否使用了美大会员卡支付
					JSONObject meiDaJson = new JSONObject();
					List<JSONObject> checkpaymentList = paymentDao.getBillPaymentByPaymentState(tenancyId, storeId, billNum, SysDictionary.PAYMENT_STATE_PAY_COMPLETE);// 如果是美大先支付再用现金或者银行卡，查出所有付款记录
					for (JSONObject checkpayment : checkpaymentList) {
						if (checkpayment.containsKey("payment_class")) {
							String payWay = checkpayment.getString("payment_class");
							if (SysDictionary.PAYMENT_CLASS_MEIDA_CARD_PAY.equals(payWay))// 是用美大会员卡支付的
							{
								isUseMeiDacard = 1;
								meiDaJson.put("opt_num", optNum);
								meiDaJson.put("bill_num", billNum);
								meiDaJson.put("bill_code", checkpayment.optString("bill_code"));
								meiDaJson.put("payment_amount", paymentAmount);
							}
						}
					}
					if (isUseMeiDacard == 1) {
						// 选择使用新美大会员卡，就不会有增加积分操作了，和本系统卡不能同时使用
						posNewCrmService.completeXMDCRMPayment(tenancyId, storeId, meiDaJson, currentTime);
					}
				} else if (SysDictionary.CUSTOMER_TYPE_ACEWILL.equals(customerType)) {
					this.addCustomerCreditByAcewill(tenancyId, storeId, billNum, orderNum, chanel, DateUtil.formatDate(reportDate), shiftId, optNum, posNum, tableCode,paymentAmount, currentTime);
				} else {
					// 增加积分
					this.addcustomerCredit(tenancyId, storeId, billNum, orderNum, chanel, DateUtil.formatDate(reportDate), shiftId, optNum, posNum, paymentAmount, currentTime);
				}
			} catch (Exception e1) {
				e1.printStackTrace();
				logger.error("消费赠送积分失败:", e1);
			}
		}


		//打印结账单
		if ("Y".equalsIgnoreCase(isPrintBill)) {
			if (null == printJson) {
				printJson = new JSONObject();
			}
			int printCount = Integer.parseInt(paymentDao.getSysParameter(tenancyId, storeId, "JZDDYSL"));

			printJson.put("tenancy_id", tenancyId);
			printJson.put("store_id", storeId);
			printJson.put("bill_num", billNum);
			printJson.put("pos_num", posNum);
			printJson.put("opt_num", optNum);
			printJson.put("report_date", reportDate);
			printJson.put("shift_id", shiftId);
			printJson.put("channel", chanel);
			printJson.put("format_state", "1");
			printJson.put("format_mode", "01");
			printJson.put("isprint", isPrintBill);
			printJson.put("print_count", printCount);
			// 开具电子发票
			if("1".equals(isInvoice))
			{
				this.createInvoiceInfo(tenancyId, storeId, DateUtil.formatDate(reportDate), posNum, optNum, isInvoice, billJson, printJson);
			}
		}

		//手动开具发票插入记录
		if ("2".equals(isInvoice)) {
			this.insertInvoiceInfo(tenancyId, storeId, DateUtil.formatDate(reportDate), posNum, optNum, billJson);
		}

		// 计算账单和账单明细的代销价格
		this.calculateSettlementPrice(tenancyId, storeId, billNum);

		//修改账单税价分离
		paymentDao.updatePosBillTaxPriceSeparation(tenancyId, storeId, billNum, saleMode);

		// 更新账单净收
		this.updateBillDue(billNum, tenancyId, storeId);

		try {
			// 写日志pos_log
			paymentDao.savePosLog(tenancyId, storeId, posNum, optNum, optNum, shiftId, reportDate, com.tzx.pos.base.Constant.TITLE, "关闭账单", "账单编号:" + billNum + "; 应付金额:" + String.valueOf(paymentAmount), " 多收礼券:" + String.valueOf(moreCoupon), DateUtil.currentTimestamp());
		} catch (Exception e) {
			logger.info("报错日志报错:", e);
			e.printStackTrace();
		}

		try {
			this.uploadCloudBill(tenancyId, storeId, billNum);

			if (orderSyncService.isSyncOrder(tenancyId, storeId, orderNum)) {
				orderSyncService.sync(OrdersSyncService.POSTPAY_ORDER_COMPLETE, orderNum);
			}

			if (Tools.hv(orderNum) && orderNum.equals("WX" + billNum)) {
				//如果是微信后付相关账单，发送给微信通知关单清台
				logger.info("微信后付订单，通知微信关单清台");
				WxOrderUtil.closeBill(tenancyId, billNum);
			}
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("更新订单状态失败:", e);
		}
	}

	/** 账单关闭后续操作
	 * @param tenancyId
	 * @param storeId
	 * @param reportDate
	 * @param shiftId
	 * @param chanel
	 * @param optNum
	 * @param posNum
	 * @param billNum
	 * @param orderNum
	 * @param tableCode
	 * @param paymentAmount
	 * @param currentTime
	 * @param isOnlinePay
	 * @param billJson
	 * @param printJson
	 * @param isInvoice
	 * @param formatState
	 * @param mode
	 * @param isPrint
	 * @throws Exception
	 */
	private void closedPosBillSuccess(String tenancyId, int storeId, String reportDate, int shiftId, String chanel, String optNum, String posNum, String billNum, String orderNum, String tableCode, Double paymentAmount, Timestamp currentTime, Object isOnlinePay, JSONObject billJson,
			JSONObject printJson,  String isInvoice, String formatState, String mode, String isPrint) throws Exception
	{
		if ("0".equals(isOnlinePay))
		{
			try
			{
				String isMeiDaCard = paymentDao.getSysParameter(tenancyId, storeId, "SFSYXMDCRMMK");// 是否使用美大会员

				String customerType = paymentDao.getCustomerType(tenancyId, storeId);

				if ("Y".equals(isMeiDaCard))
				{

					int isUseMeiDacard = 0;// 账单付款方式中是否使用了美大会员卡支付
					JSONObject meiDaJson = new JSONObject();
					List<JSONObject> checkpaymentList = paymentDao.getBillPaymentByPaymentState(tenancyId, storeId, billNum, SysDictionary.PAYMENT_STATE_PAY_COMPLETE);// 如果是美大先支付再用现金或者银行卡，查出所有付款记录
					for (JSONObject checkpayment : checkpaymentList)
					{
						if (checkpayment.containsKey("payment_class"))
						{
							String payWay = checkpayment.getString("payment_class");
							if (SysDictionary.PAYMENT_CLASS_MEIDA_CARD_PAY.equals(payWay))// 是用美大会员卡支付的
							{
								isUseMeiDacard = 1;
								meiDaJson.put("opt_num", optNum);
								meiDaJson.put("bill_num", billNum);
								meiDaJson.put("bill_code", checkpayment.optString("bill_code"));
								meiDaJson.put("payment_amount", paymentAmount);
							}
						}
					}
					if (isUseMeiDacard == 1)
					{
						// 选择使用新美大会员卡，就不会有增加积分操作了，和本系统卡不能同时使用
						posNewCrmService.completeXMDCRMPayment(tenancyId, storeId, meiDaJson, currentTime);
					}
				}
				else if (SysDictionary.CUSTOMER_TYPE_ACEWILL.equals(customerType))
				{
					this.addCustomerCreditByAcewill(tenancyId, storeId, billNum, orderNum, chanel, reportDate, shiftId, optNum, posNum, tableCode,paymentAmount, currentTime);
				}
				else
				{
					// 增加积分
					this.addcustomerCredit(tenancyId, storeId, billNum, orderNum, chanel, reportDate, shiftId, optNum, posNum, paymentAmount, currentTime);
				}
			}
			catch (Exception e)
			{
				e.printStackTrace();
				logger.error("消费赠积分失败:", e);
			}
		}

		if ("2".equals(formatState))
		{
			//生成KVS记录
			writeKvsBill(tenancyId, storeId, billNum, reportDate);
		}

		// 更新账单净收
		updateBillDue(billNum, tenancyId, storeId);

		if (null == printJson)
		{
			printJson = new JSONObject();
		}
		int printCount = Integer.parseInt(paymentDao.getSysParameter(tenancyId, storeId, "JZDDYSL"));

		printJson.put("tenancy_id", tenancyId);
		printJson.put("store_id", storeId);
		printJson.put("bill_num", billNum);
		printJson.put("pos_num", posNum);
		printJson.put("opt_num", optNum);
		printJson.put("report_date", reportDate);
		printJson.put("shift_id", shiftId);
		printJson.put("channel", chanel);
		printJson.put("format_state", formatState);
		printJson.put("format_mode", mode);
		printJson.put("isprint", isPrint);
		printJson.put("print_count", printCount);

		this.createInvoiceInfo(tenancyId, storeId, reportDate, posNum, optNum, isInvoice, billJson, printJson);

		this.addOrderDelivery(tenancyId, storeId, billNum);

		this.uploadCloudBill(tenancyId, storeId, billNum);

		posService.updateDevicesDataState();

		try
		{
			if (orderSyncService.isSyncOrder(tenancyId, storeId, orderNum))
			{
				orderSyncService.sync(OrdersSyncService.POSTPAY_ORDER_COMPLETE, orderNum);
			}

			if (orderNum.equals("WX" + billNum)){
				//如果是微信后付相关账单，发送给微信通知关单清台
				logger.info("微信后付订单，通知微信关单清台");
				WxOrderUtil.closeBill(tenancyId, billNum);
			}

			if ("0".equals(isOnlinePay))
			{
				// 调用微生活的清台,通知微生活清台
				logger.info("开始调用微生活的清台");
				tableStateService.closeTable(tenancyId, storeId, tableCode);

				// 调用微生活小程序的清台,通知微生活清台
				wlifeProgramService.completeOrder(tenancyId, storeId, billNum);
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
			logger.error("更新订单状态失败:", e);
		}

		this.updatePosBillForUpload(tenancyId, storeId, billNum);
	}

	/**
	 * 更新 pos_bill_payment, pos_bill 表中的 净收
	 * @param billNum
	 * @param tenancyId
	 * @param storeId
	 * @throws Exception
	 */
	public void updateBillDue(String billNum, String tenancyId, Integer storeId) throws Exception{

//		// 更新净收值
//		StringBuilder updatePaymentdueSql = new StringBuilder("update pos_bill_payment set due = currency_amount where bill_num = ? and store_id =? and tenancy_id=? and type<>? ");
//		String customerType = paymentDao.getCustomerType(tenancyId, storeId);
//		if (SysDictionary.CUSTOMER_TYPE_ACEWILL.equals(customerType))
//		{
//			updatePaymentdueSql.append(" and type<>? and type<>? and type<>?");
//			paymentDao.update(updatePaymentdueSql.toString(), new Object[]
//			{ billNum, storeId, tenancyId, SysDictionary.PAYMENT_CLASS_MEIDA_COUPONS_PAY, SysDictionary.PAYMENT_CLASS_CARD, SysDictionary.PAYMENT_CLASS_CARD_CREDIT, SysDictionary.PAYMENT_CLASS_COUPONS });
//		}
//		else
//		{
//			paymentDao.update(updatePaymentdueSql.toString(), new Object[]
//			{ billNum, storeId, tenancyId, SysDictionary.PAYMENT_CLASS_MEIDA_COUPONS_PAY });
//		}
//		
//		// 更新 pos_bill 表中的净收值
//		String updateBilldueSql = new String("UPDATE pos_bill SET due =(SELECT COALESCE(SUM(pbp.due), 0) FROM pos_bill_payment pbp WHERE pbp.bill_num =? and pbp.store_id =? and pbp.tenancy_id =?) WHERE bill_num =? AND store_id =? AND tenancy_id =?");
//		paymentDao.update(updateBilldueSql, new Object[]
//				{billNum, storeId, tenancyId, billNum, storeId, tenancyId});

		StringBuilder updateBilldueSql = new StringBuilder();
		updateBilldueSql.append("update pos_bill bl set coupon_buy_price=bp.coupon_buy_price, due=bp.due, tenancy_assume=bp.tenancy_assume, third_assume=bp.third_assume, third_fee=bp.third_fee ");
		updateBilldueSql.append("from (select bp.tenancy_id,bp.store_id,bp.bill_num,coalesce(sum(bp.coupon_buy_price), 0) coupon_buy_price,coalesce(sum(bp.due), 0) due,coalesce(sum(bp.tenancy_assume), 0) tenancy_assume,coalesce(sum(bp.third_assume), 0) third_assume,coalesce(sum(bp.third_fee), 0) third_fee from pos_bill_payment bp group by bp.tenancy_id,bp.store_id,bp.bill_num) bp ");
		updateBilldueSql.append("where bl.tenancy_id=bp.tenancy_id and bl.store_id=bp.store_id and bl.bill_num=bp.bill_num and bl.tenancy_id=? and bl.store_id=? and bl.bill_num=?");

		paymentDao.update(updateBilldueSql.toString(), new Object[]
		{ tenancyId, storeId, billNum });
	}

	@Override
	public void updatePosBillTaxPriceSeparation(String tenancyId, int storeId, JSONObject paramJson) throws Exception
	{
		String saleMode =paymentDao.getSaleMode(tenancyId, storeId,paramJson.getString("order_no"));
		paymentDao.updatePosBillTaxPriceSeparation(tenancyId, storeId,paramJson.getString("order_no"), saleMode);
	}

	/**
	 * 增加积分 GJ20160407
	 *
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param reportDate
	 */
	private void addcustomerCredit(String tenancyId, int storeId, String billNum, String orderNum, String chanel, String reportDate, int shiftId, String opt_Num, String pos_num, Double paymentAmount, Timestamp currentTime) throws Exception
	{
		Date report_Date = DateUtil.parseDate(reportDate);

		String type = null;
		String mobil = null;
		String cardCode = null;
		String customerCode = null;
		String customerName = null;
		Double consumeBeforeCredit = null;
		Double consumeBeforeMainBalance = null;
		Double consumeBeforeRewardBalance = null;
		Double consumeAfterCredit = null;
		Double consumeAfterMainBalance = null;
		Double consumeAfterRewardBalance = null;

		List<JSONObject> memberList = memberDao.queryPosBillMember(tenancyId, storeId, billNum, null);
		JSONObject creditMemberJson =null;
		for (JSONObject memberJson : memberList)
		{
			String memberType = memberJson.optString("type");
			if (Tools.hv(type))
			{
				if (SysDictionary.BILL_MEMBERCARD_JFZS06.equals(memberType))
				{
					type = memberType;
					creditMemberJson = memberJson;
				}
				else if (!SysDictionary.BILL_MEMBERCARD_JFZS06.equals(type) && (SysDictionary.BILL_MEMBERCARD_CZXF03.equals(memberType) || SysDictionary.BILL_MEMBERCARD_YHJ04.equals(memberType) || SysDictionary.BILL_MEMBERCARD_JFDX05.equals(memberType)))
				{
					type = memberType;
					creditMemberJson = memberJson;
				}
			}
			else
			{
				type = memberType;
				creditMemberJson = memberJson;
			}
		}

		if (Tools.hv(creditMemberJson))
		{
			mobil = creditMemberJson.optString("mobil");
			cardCode = creditMemberJson.optString("card_code");
			customerCode = creditMemberJson.optString("customer_code");
			customerName = creditMemberJson.optString("customer_name");
			consumeBeforeCredit = ParamUtil.getDoubleValueByObject(creditMemberJson, "consume_before_credit");
			consumeBeforeMainBalance = ParamUtil.getDoubleValueByObject(creditMemberJson, "consume_before_main_balance");
			consumeBeforeRewardBalance = ParamUtil.getDoubleValueByObject(creditMemberJson, "consume_before_reward_balance");
			consumeAfterCredit = ParamUtil.getDoubleValueByObject(creditMemberJson, "consume_after_credit");
			consumeAfterMainBalance = ParamUtil.getDoubleValueByObject(creditMemberJson, "consume_after_main_balance");
			consumeAfterRewardBalance = ParamUtil.getDoubleValueByObject(creditMemberJson, "consume_after_reward_balance");
		}

		if (StringUtils.isEmpty(mobil) && StringUtils.isEmpty(cardCode))
		{
			return;
		}

		if (!SysDictionary.BILL_MEMBERCARD_JFZS06.equals(type))
		{
			PosBillMember billMember = new PosBillMember();
			billMember.setBill_num(billNum);
			billMember.setReport_date(report_Date);
			billMember.setType(SysDictionary.BILL_MEMBERCARD_JFZS06);
			billMember.setCard_code(cardCode);
			billMember.setMobil(mobil);
			billMember.setCustomer_code(customerCode);
			billMember.setCustomer_name(customerName);
			billMember.setAmount(0d);
			billMember.setCredit(0d);
			billMember.setConsume_after_credit(consumeAfterCredit);
			billMember.setConsume_before_credit(consumeBeforeCredit);
			billMember.setConsume_after_main_balance(consumeAfterMainBalance);
			billMember.setConsume_before_main_balance(consumeBeforeMainBalance);
			billMember.setConsume_after_reward_balance(consumeAfterRewardBalance);
			billMember.setConsume_before_reward_balance(consumeBeforeRewardBalance);
			billMember.setLast_updatetime(currentTime);

			memberDao.insertPosBillMember(tenancyId, storeId, billMember);
		}

		StringBuilder sql = new StringBuilder();
		sql.append(" select coalesce(pbp.currency_amount,0) as amount,pbp.jzid,pway.if_jifen from pos_bill_payment pbp ");
		sql.append(" left join payment_way pway on pbp.jzid = pway.id and pbp.tenancy_id = pway.tenancy_id ");
		sql.append(" where pbp.tenancy_id = ? and pbp.store_id = ? and pbp.bill_num=? ");
		SqlRowSet rst = paymentDao.query4SqlRowSet(sql.toString(), new Object[]
				{ tenancyId, storeId, billNum });

		List<Map<String, Object>> paymentsLst=new ArrayList<Map<String, Object>>();
//		Double amount = 0d;
		while(rst.next())
		{
			Double currencyAmount = rst.getDouble("amount");
			Integer jzid = rst.getInt("jzid");
			Map<String, Object> payMap = new HashMap<String, Object>();
			payMap.put("pay_money", currencyAmount);
			payMap.put("payment_id", jzid);
			paymentsLst.add(payMap);
//			if ("1".equals(rst.getString("if_jifen")))
//			{
//				amount = DoubleHelper.add(amount, currencyAmount, 4);
//			}
		}
//		if (paymentAmount < amount)
//		{
//			amount = paymentAmount;
//		}
//		String currentTimeStr = DateUtil.format(currentTime);
		String lastUpdatetimeStr = String.valueOf(currentTime.getTime());

//		if (amount > 0)
//		{
			JSONObject requestJson = new JSONObject();
			requestJson.put("mobil", mobil);// 手机号
            requestJson.put("customer_code", customerCode);// 会员编号
			requestJson.put("card_code", cardCode);// 会员卡号
			requestJson.put("consume_creditmoney", String.valueOf(paymentAmount));// 增加积分数
			requestJson.put("credit", 0d);
			requestJson.put("reason", "消费赠积分");// 原因
			requestJson.put("report_date", reportDate);// 报表日期
			requestJson.put("shift_id", shiftId);
			requestJson.put("opt_num", opt_Num);// 操作人
			requestJson.put("pos_num", pos_num);
			requestJson.put("chanel", chanel);// 渠道
			requestJson.put("payments", paymentsLst);// 渠道
			if ((!SysDictionary.CHANEL_MD01.equals(chanel)) && Tools.hv(orderNum))
			{
				requestJson.put("bill_code", orderNum);
			}
			else
			{
				requestJson.put("bill_code", billNum);
				requestJson.put("batch_no", lastUpdatetimeStr);
			}
			requestJson.put("remark", "");// 备注
//			requestJson.put("updatetime", currentTimeStr);// 操作时间
			requestJson.put("end_date", "");// 失效日期
			requestJson.put("bill_amount", paymentAmount);

//			List<JSONObject> requestList = new ArrayList<JSONObject>();
//			requestList.add(requestJson);
//
//			Data requestData = Data.get(tenancyId, storeId, 0);
//			requestData.setType(Type.CUSTOMER_CREDIT);
//			requestData.setOper(Oper.add);
//			requestData.setData(requestList);
//
//			Data responseData = Data.get(requestData);
//			customerService.commonPost(JSONObject.fromObject(requestData).toString(), responseData);

			Data responseData = Data.get();
			customerCreditService.customerCreditAdd(tenancyId, storeId, requestJson, responseData, Type.BILL_PAYMENT.name());

			if (!responseData.isSuccess())
			{
				JSONObject responseJson = JSONObject.fromObject(responseData.getData().get(0));
				if (Constant.CODE_INNER_EXCEPTION != responseData.getCode() && Constant.CODE_CONN_EXCEPTION != responseData.getCode())
				{
					logger.info("结账失败:  " + responseJson.toString());
					throw SystemException.getInstance(PosErrorCode.BILL_PAYMENT_ERROP).set("{0}", responseData.getMsg());
				}

				memberDao.updatePosBillMember(tenancyId, storeId, billNum, SysDictionary.BILL_MEMBERCARD_JFZS06, paymentAmount, 0d, null, SysDictionary.REQUEST_STATUS_FAILURE, null, 0d, 0d);
			}
			else
			{
				JSONObject responseJson = JSONObject.fromObject(responseData.getData().get(0));

				double rewardCredit = responseJson.optDouble("credit");
				double usefulCredit = responseJson.optDouble("useful_credit");

				memberDao.updatePosBillMember(tenancyId, storeId, billNum, SysDictionary.BILL_MEMBERCARD_JFZS06, paymentAmount, rewardCredit, responseJson.optString("bill_code"), SysDictionary.REQUEST_STATUS_COMPLETE, responseJson.optString("name"),
						DoubleHelper.sub(usefulCredit, rewardCredit, DEFAULT_SCALE), usefulCredit);
			}
//		}
	}
	private void addCustomerCreditByAcewill(String tenancyId, int storeId, String billNum, String orderNum, String chanel, String reportDate, int shiftId, String optNum, String posNum,String tableCode, Double billPaymentAmount, Timestamp currentTime) throws Exception
	{
		List<JSONObject> memberList = memberDao.queryPosBillMember(tenancyId, storeId, billNum, null);
		if (null != memberList && memberList.size() > 0)
		{
			JSONObject creditMemberJson = this.getCreditMemberJson(memberList);
//			boolean isCredit = false;
//			if (SysDictionary.BILL_MEMBERCARD_JFZS06.equals(creditMemberJson.optString("type")))
//			{
//				isCredit = true;
//			}
//			
//			if (isCredit && Tools.hv(ParamUtil.getStringValueByObject(creditMemberJson, "bill_code")))
//			{
//				// 账单已经积分
//				return;
//			}
			
			List<JSONObject> paymentList = paymentDao.getPosBillPaymentByBillnum(tenancyId, storeId, billNum);
//			Double paymentAmount = 0d;// 实际支付金额
//			Integer paymentMode = 0;// 支付方式
//			Double creditAmount = 0d;// 自定义消费赠送积分
//			boolean isMCard = false;
//			if (null != paymentList && paymentList.size() > 0)
//			{
//				String outTradeNo = null;
//				for (JSONObject paymentJson : paymentList)
//				{
//					Double payAmount = ParamUtil.getDoubleValueByObject(paymentJson, "currency_amount");
//					String paymentClass = ParamUtil.getStringValueByObject(paymentJson, "payment_class");
//					paymentAmount = DoubleHelper.add(paymentAmount, payAmount, DEFAULT_SCALE);
//					
//					if (SysDictionary.PAYMENT_CLASS_WLIFE_BALANCE.equals(paymentClass) || SysDictionary.PAYMENT_CLASS_WLIFE_COUPON.equals(paymentClass) || SysDictionary.PAYMENT_CLASS_WLIFE_CREDIT.equals(paymentClass))
//					{
//						if (null != outTradeNo && !outTradeNo.equals(ParamUtil.getStringValueByObject(paymentJson, "out_trade_no")))
//						{
//							isMCard = true;
//						}
//						outTradeNo = ParamUtil.getStringValueByObject(paymentJson, "out_trade_no");
//						
//						if (SysDictionary.PAYMENT_CLASS_WLIFE_BALANCE.equals(paymentClass) && "1".equals(paymentJson.optString("if_jifen")))
//						{
//							creditAmount = DoubleHelper.add(creditAmount, payAmount, DEFAULT_SCALE);
//							paymentMode = 10;
//						}
//					}
//					else if (false == SysDictionary.PAYMENT_CLASS_FREESINGLE.equals(paymentClass) && false == SysDictionary.PAYMENT_CLASS_INCORPORATION.equals(paymentClass))
//					{
//						switch (paymentClass)
//						{
//							case SysDictionary.PAYMENT_CLASS_CASH:
//								paymentMode = (0 == paymentMode || 1 == paymentMode) ? 1 : 10;
//								break;
//							case SysDictionary.PAYMENT_CLASS_BANKCARD:
//								paymentMode = (0 == paymentMode || 2 == paymentMode) ? 2 : 10;
//								break;
//							case SysDictionary.PAYMENT_CLASS_WECHAT_PAY:
//								paymentMode = (0 == paymentMode || 3 == paymentMode) ? 3 : 10;
//								break;
//							case SysDictionary.PAYMENT_CLASS_ALI_PAY:
//								paymentMode = (0 == paymentMode || 4 == paymentMode) ? 4 : 10;
//								break;
//							default:
//								paymentMode = 10;
//								break;
//						}
//
//						if ("1".equals(paymentJson.optString("if_jifen")))
//						{
//							creditAmount = DoubleHelper.add(creditAmount, payAmount, DEFAULT_SCALE);
//						}
//					}
//				}
//			}
//			
//			if(false==isCredit && isMCard)
//			{
//				//多卡支付,且没有绑定会员卡,不积分;
//				return;
//			}
			if (SysDictionary.BILL_MEMBERCARD_JFZS06.equals(ParamUtil.getStringValueByObject(creditMemberJson, "type")))
			{
				// 判断是否已经做过消费赠积分
				if (CommonUtil.hv(creditMemberJson.optString("bill_code")) && (SysDictionary.REQUEST_STATUS_COMPLETE.equals(creditMemberJson.optString("request_state")) || SysDictionary.REQUEST_STATUS_FAILURE.equals(creditMemberJson.optString("request_state"))))
				{
					logger.info("微生活已经增加积分");
					return;
				}
			}
			Double paymentAmount = 0d;// 实际支付金额
			Integer paymentMode = 0;// 支付方式
			Double creditAmount = 0d;// 自定义消费赠送积分
			Double memberPayAmount = 0d;  // 实际支付金额
			Double totalpayAmount = 0d;
			boolean isMemberPay = false;
			if (null != paymentList && 0 < paymentList.size())
			{
				paymentAmount = billPaymentAmount;
				for (JSONObject paymentJson : paymentList)
				{
					String paymentClass = ParamUtil.getStringValueByObject(paymentJson, "payment_class");
					totalpayAmount = DoubleHelper.psub(ParamUtil.getDoubleValueByObject(paymentJson, "currency_amount"), ParamUtil.getDoubleValueByObject(paymentJson, "more_coupon")) ;
					memberPayAmount = DoubleHelper.add(memberPayAmount, totalpayAmount, DEFAULT_SCALE);
					if (SysDictionary.PAYMENT_CLASS_WLIFE_BALANCE.equals(paymentClass) || SysDictionary.PAYMENT_CLASS_WLIFE_COUPON.equals(paymentClass) || SysDictionary.PAYMENT_CLASS_WLIFE_CREDIT.equals(paymentClass))
					{
						isMemberPay = true;           //储值交易记录,在积分时算作其他付款
					}

					switch (paymentClass)
					{
						case SysDictionary.PAYMENT_CLASS_WLIFE_BALANCE:
						case SysDictionary.PAYMENT_CLASS_WLIFE_COUPON:
						case SysDictionary.PAYMENT_CLASS_WLIFE_CREDIT:
							break;
						case SysDictionary.PAYMENT_CLASS_CASH:
							paymentMode = (0 == paymentMode || 1 == paymentMode) ? 1 : 10;
							break;
						case SysDictionary.PAYMENT_CLASS_BANKCARD:
							paymentMode = (0 == paymentMode || 2 == paymentMode) ? 2 : 10;
							break;
						case SysDictionary.PAYMENT_CLASS_WECHAT_PAY:
							paymentMode = (0 == paymentMode || 3 == paymentMode) ? 3 : 10;
							break;
						case SysDictionary.PAYMENT_CLASS_ALI_PAY:
							paymentMode = (0 == paymentMode || 4 == paymentMode) ? 4 : 10;
							break;
						default:
							paymentMode = 10;
							break;
					}
				}
				if (0 == paymentMode)
				{
					paymentMode = 10;
				}
                if (isMemberPay)
				{
					memberPayAmount =0d;
				}
				creditAmount = this.getCreditAmount(null, billPaymentAmount, paymentList);
			}
			if (-1d == creditAmount)
			{
				creditAmount = 0d;
			}

            Double activityExcludeAmount = 0d; //不参与活动的金额

            StringBuilder activityExcludeSql = new StringBuilder();

            activityExcludeSql.append("SELECT COALESCE(SUM(currency_amount), 0) AS no_active_amount ");
            activityExcludeSql.append("FROM pos_bill_payment P ");
            activityExcludeSql.append("WHERE EXISTS ( ");
            activityExcludeSql.append("SELECT T.payment_class ");
            activityExcludeSql.append("FROM (SELECT pw.payment_class FROM payment_way_of_ogran pwo LEFT JOIN payment_way pw ON pwo.payment_id = pw.ID WHERE pwo.is_rebate_coupons = '0') T ");
            activityExcludeSql.append("WHERE P.TYPE = T.payment_class ");
            activityExcludeSql.append(") ");
            activityExcludeSql.append("AND P.bill_num = '"+billNum+"'");

            List<JSONObject> jsonObjects = paymentDao.query4Json(null, activityExcludeSql.toString());
            if (CollectionUtils.isNotEmpty(jsonObjects)) {
                activityExcludeAmount = jsonObjects.get(0).optDouble("no_active_amount");
            }

            memberPayAmount -= activityExcludeAmount;

            logger.info(" 微生活判断增加消费额： " + memberPayAmount + "  增加积分：" + creditAmount);

			if ((creditAmount > 0 || memberPayAmount >0) && !isMemberPay )
			{
				String mobil = ParamUtil.getStringValueByObject(creditMemberJson, "mobil");
				String cardCode = ParamUtil.getStringValueByObject(creditMemberJson, "card_code");
				String customerCode = ParamUtil.getStringValueByObject(creditMemberJson, "customer_code");
				String customerName = ParamUtil.getStringValueByObject(creditMemberJson, "customer_name");
				String levelName = "";
				
				JSONObject requestJson = new JSONObject();
				String outTradeNo = ParamUtil.getOutTradeNoForAcewillCredit(billNum, currentTime);
				requestJson.put("out_trade_no", outTradeNo);
				requestJson.put("third_bill_code", billNum);
				requestJson.put("report_date", reportDate);
				requestJson.put("shift_id", shiftId);
				requestJson.put("pos_num", posNum);
				requestJson.put("opt_num", optNum);
				requestJson.put("chanel", chanel);
				requestJson.put("card_code", cardCode);
				requestJson.put("mobil", mobil);
				requestJson.put("customer_name", customerName);
				requestJson.put("level_name", levelName);
				requestJson.put("consume_amount", billPaymentAmount);
				requestJson.put("payment_amount", paymentAmount);
				requestJson.put("payment_mode", paymentMode);
				requestJson.put("sub_balance", 0d);
				requestJson.put("sub_credit", 0d);
				requestJson.put("credit_amount", creditAmount);
		     	requestJson.put("activity_amount", memberPayAmount);
				requestJson.put("deno_coupon_ids", null);
				requestJson.put("gift_coupons_ids", null);
				requestJson.put("diy_gift_coupon_pay", null);
                requestJson.put("products", getProducts(tenancyId,storeId,billNum));
				requestJson.put("activity_ids", null);
				requestJson.put("count_num", "0");
				requestJson.put("table_id", tableCode);
			    if (creditAmount > 0)
				{
					requestJson.put("remark", "消费赠积分");
				}
			    else
				{
					requestJson.put("remark", "消费信息");
				}

				requestJson.put("from", "16");//预消费时加参数from 值为16时不发送交易验证码

				List<JSONObject> requestList = new ArrayList<JSONObject>();
				requestList.add(requestJson);
				Data requestData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
				requestData.setData(requestList);

				// TODO previewAcewillCustomerDeal
				Data responseData = acewillCustomerService.previewAcewillCustomerDeal(requestData);

				if (Constant.CODE_SUCCESS == responseData.getCode())
				{
					requestJson = new JSONObject();
					requestJson.put("out_trade_no", outTradeNo);
					requestJson.put("third_bill_code", billNum);
					requestJson.put("verify_code", "");

					requestList = new ArrayList<JSONObject>();
					requestList.add(requestJson);

					requestData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
					requestData.setData(requestList);

					// TODO commitAcewillCustomerDeal
					responseData = acewillCustomerService.commitAcewillCustomerDeal(requestData);
					if (Constant.CODE_SUCCESS == responseData.getCode())
					{
						JSONObject responseJson = JSONObject.fromObject(responseData.getData().get(0));
						String billCode = ParamUtil.getStringValueByObject(responseJson, "deal_id");
						customerName = ParamUtil.getStringValueByObject(responseJson, "customer_name");
						Double receiveCredit = ParamUtil.getDoubleValueByObject(responseJson, "receive_credit");
						Double receiveBeforeCredit = ParamUtil.getDoubleValueByObject(responseJson, "credit");
						Double receiveAfterCredit = DoubleHelper.add(receiveBeforeCredit, receiveCredit, Constant.DEFAULT_SCALE);

						String requestState = SysDictionary.REQUEST_STATUS_COMPLETE;
						String remark = null;
						if (SysDictionary.BILL_MEMBERCARD_JFZS06.equals(creditMemberJson.optString("type")))
						{
							receiveCredit = DoubleHelper.padd(receiveCredit, ParamUtil.getDoubleValueByObject(creditMemberJson, "credit"));
							receiveBeforeCredit = ParamUtil.getDoubleValueByObject(creditMemberJson, "consume_before_credit");
							receiveAfterCredit = DoubleHelper.add(receiveBeforeCredit, receiveCredit, Constant.DEFAULT_SCALE);
							Integer id = ParamUtil.getIntegerValueByObject(creditMemberJson, "id");

							memberDao.updatePosBillMember(tenancyId, storeId, billNum, SysDictionary.BILL_MEMBERCARD_JFZS06, id, paymentAmount, receiveCredit, billCode, requestState, customerName, receiveBeforeCredit, receiveAfterCredit);
						}
						else
						{
							memberDao.insertPosBillMember(tenancyId, storeId, billNum, DateUtil.parseDate(reportDate), SysDictionary.BILL_MEMBERCARD_JFZS06, customerCode, cardCode, mobil, currentTime, remark, customerName, receiveBeforeCredit, receiveAfterCredit, receiveCredit, paymentAmount,
									billCode, requestState);
						}
					}
				}

			}
		}
	}
	
	/**计算可积分金额
	 * @param billPaymentAmount
	 * @param billPaymentList
	 * @return
	 */
	protected Double getCreditAmount(String outTradeNo, Double billPaymentAmount, List<JSONObject> billPaymentList)
	{
		Double creditAmount = 0d;// 自定义消费赠送积分
		Double totalPayAmount = 0d;
		Double cashAmount = 0d;// 现金金额
		boolean isCashCredit = false;
		for (JSONObject paymentJson : billPaymentList)
		{
			Double payAmount = ParamUtil.getDoubleValueByObject(paymentJson, "currency_amount");
			String paymentClass = ParamUtil.getStringValueByObject(paymentJson, "payment_class");

			totalPayAmount = DoubleHelper.add(totalPayAmount, payAmount, DEFAULT_SCALE);

			if (SysDictionary.PAYMENT_CLASS_WLIFE_BALANCE.equals(paymentClass))
			{
				String chargeReturnCredit = null;
				if (CommonUtil.hv(paymentJson.optString("param_cach")))
				{
					JSONObject paramCachJson = JSONObject.fromObject(paymentJson.optString("param_cach"));
					chargeReturnCredit = ParamUtil.getStringValueByObject(paramCachJson, "charge_return_credit");
				}

				if ((null != outTradeNo && outTradeNo.equals(paymentJson.optString("out_trade_no"))) && "1".equals(paymentJson.optString("if_jifen")) && "1".equals(chargeReturnCredit))
				{
					creditAmount = DoubleHelper.add(creditAmount, payAmount, DEFAULT_SCALE);
				}
			}
			else if (SysDictionary.PAYMENT_CLASS_CASH.equals(paymentClass))
			{
				cashAmount = DoubleHelper.add(cashAmount, payAmount, DEFAULT_SCALE);
				isCashCredit = ("1".equals(paymentJson.optString("if_jifen")));
				if (isCashCredit)
				{
					creditAmount = DoubleHelper.add(creditAmount, payAmount, DEFAULT_SCALE);
				}
			}
			else if (false == SysDictionary.PAYMENT_CLASS_WLIFE_CREDIT.equals(paymentClass) && false == SysDictionary.PAYMENT_CLASS_WLIFE_COUPON.equals(paymentClass) && false == SysDictionary.PAYMENT_CLASS_FREESINGLE.equals(paymentClass)
					&& false == SysDictionary.PAYMENT_CLASS_INCORPORATION.equals(paymentClass))
			{
				// 除微生活积分,微生活优惠券,免单,团体挂账外,其他付款根据付款方式设置判断是否积分
				if ("1".equals(paymentJson.optString("if_jifen")))
				{
					creditAmount = DoubleHelper.add(creditAmount, payAmount, DEFAULT_SCALE);
				}
			}
		}

		Double difference = DoubleHelper.sub(billPaymentAmount, totalPayAmount, DEFAULT_SCALE);

		if (0 >= difference)
		{
			Double changeAmount = this.getChangeAmount(difference, cashAmount);// 找零金额
			if (isCashCredit)
			{
				creditAmount = DoubleHelper.sub(creditAmount, Math.abs(changeAmount), DEFAULT_SCALE);
			}
		}
//		else if (0 < difference)
//		{
//			creditAmount = -1d;
//		}

		if (0 == creditAmount)
		{
			creditAmount = -1d;
		}

		return creditAmount;
	}
	
	/** 获取积分会员
	 * @param memberList
	 * @return
	 * @throws Exception
	 */
	private JSONObject getCreditMemberJson(List<JSONObject> memberList) throws Exception
	{
		JSONObject creditMemberJson = null;
		String type = null;
		for (JSONObject memberJson : memberList)
		{
			String memberType = memberJson.optString("type");
			if (Tools.hv(type))
			{
				if (SysDictionary.BILL_MEMBERCARD_JFZS06.equals(type))
				{
					Timestamp t1 = ParamUtil.getTimestampValueByObject(creditMemberJson, "last_updatetime");
					Timestamp t2 = ParamUtil.getTimestampValueByObject(memberJson, "last_updatetime");

					if (SysDictionary.BILL_MEMBERCARD_JFZS06.equals(memberType) && (null != t1 && t1.after(t2)))
					{
						type = memberType;
						creditMemberJson = memberJson;
					}
				}
				else
				{
					if (SysDictionary.BILL_MEMBERCARD_JFZS06.equals(memberType))
					{
						type = memberType;
						creditMemberJson = memberJson;
					}
					else if (SysDictionary.BILL_MEMBERCARD_CZXF03.equals(memberType))
					{
						type = memberType;
						creditMemberJson = memberJson;
					}
					else if (!SysDictionary.BILL_MEMBERCARD_CZXF03.equals(type) && SysDictionary.BILL_MEMBERCARD_YHJ04.equals(memberType))
					{
						type = memberType;
						creditMemberJson = memberJson;
					}
					else if (!SysDictionary.BILL_MEMBERCARD_CZXF03.equals(type) && !SysDictionary.BILL_MEMBERCARD_YHJ04.equals(memberType) && SysDictionary.BILL_MEMBERCARD_JFDX05.equals(memberType))
					{
						type = memberType;
						creditMemberJson = memberJson;
					}
				}
			}
			else
			{
				type = memberType;
				creditMemberJson = memberJson;
			}
		}
		return creditMemberJson;
	}

	/** 生成KVS记录
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param reportDate
	 * @throws Exception
	 */
	public void writeKvsBill(String tenancyId, int storeId,String billNum, String reportDate) throws Exception
	{
		if ("1".equals(paymentDao.getSysParameter(tenancyId, storeId, "SFQDKVS")))
		{
			StringBuilder sb = new StringBuilder();
			sb.append(" SELECT DISTINCT hd.devices_code,dkr.kvs_id,pbi.id,ki.item_id FROM hq_kvs_item ki,hq_item_info f,pos_bill_item pbi,hq_devices_kvs_ref dkr,");
			sb.append(" hq_devices hd,pos_bill pb WHERE ki.item_id = pbi.item_id AND  f.id = ki.item_id and dkr.kvs_id = ki.kvs_id AND  ");
			sb.append(" dkr.devices_code = hd.devices_code and ki.kvs_id = dkr.kvs_id and pb.bill_num = ? AND pbi.bill_num = ? ");
			List<JSONObject> list = paymentDao.query4Json(tenancyId, sb.toString(),new Object[]{billNum,billNum});
			if(list != null && list.size()>0){
	//			Timestamp currentTime = DateUtil.currentTimestamp();
				String billSql =
					"	INSERT INTO pos_kvs_bill(tenancy_id,store_id,bill_num,order_time,kvs_num,upload_tag,report_date)       				 " +
					"	SELECT pb.tenancy_id,pb.store_id,pb.bill_num,current_timestamp,posd.kvs_num,0 as upload_tag,pb.report_date from pos_bill as pb  "+
					"	LEFT JOIN( "+
					"				SELECT DISTINCT posd.pos_num,posd.devices_num as kvs_num,posd.tenancy_id,posd.store_id  "+
					"				from hq_devices as hd JOIN pos_opt_state_devices AS posd "+
					"				ON posd.devices_num = hd.devices_code AND posd.is_valid = '1' and "+
					"				posd.tenancy_id = hd.tenancy_id  AND posd.store_id = hd.store_id"+
					"				WHERE show_type = 'KVS' "+
					"			 ) as posd ON posd.pos_num = pb.pos_num and posd.tenancy_id = pb.tenancy_id and posd.store_id = pb.store_id"+
					" 	WHERE pb.tenancy_id = ? and pb.store_id = ? and pb.report_date= ? and pb.bill_num = ?; "+


					"	INSERT INTO pos_kvs_bill_item( tenancy_id,store_id,bill_num,rwid,kvs_num,kvs_rel_num,is_out,upload_tag,report_date ) "+
					"	SELECT DISTINCT pbi.tenancy_id,pbi.store_id,pbi.bill_num,pbi.rwid,posd.kvs_num,dkr.devices_code,0 as is_out,0 as upload_tag,pbi.report_date  FROM pos_bill_item as pbi   join hq_kvs_item hki on pbi.item_id = hki.item_id  join hq_devices_kvs_ref dkr on hki.kvs_id = dkr.kvs_id "+
					"	LEFT JOIN( "+
					"				SELECT DISTINCT posd.pos_num,posd.devices_num as kvs_num,posd.tenancy_id,posd.store_id  "+
					"				from hq_devices as hd JOIN pos_opt_state_devices AS posd "+
					"				ON posd.devices_num = hd.devices_code AND posd.is_valid = '1' and "+
					"				posd.tenancy_id = hd.tenancy_id  AND posd.store_id = hd.store_id"+
					"				WHERE show_type = 'KVS' "+
					"			 ) as posd ON posd.pos_num = pbi.item_mac_id and posd.tenancy_id = pbi.tenancy_id and posd.store_id = pbi.store_id"+
					"	WHERE pbi.tenancy_id = ? and pbi.store_id = ? and pbi.report_date= ? and pbi.bill_num = ?"+
					"   AND pbi.rwid not IN (SELECT pb.rwid FROM pos_bill_item pb,"+
					"  (SELECT pb.rwid,pb.setmeal_id,pb.setmeal_rwid FROM pos_bill_item pb  WHERE pb.item_property= ? "+
					"  AND pb.bill_num= ? and pb.item_id not in (SELECT item_id FROM hq_kvs_item)) bb "+
					"   WHERE pb.bill_num= ? AND pb.setmeal_id =bb.setmeal_id AND pb.setmeal_rwid =bb.setmeal_rwid ) ";

				paymentDao.update(billSql, new Object[]
				{ tenancyId, storeId, DateUtil.parseDate(reportDate), billNum, tenancyId, storeId, DateUtil.parseDate(reportDate), billNum, SysDictionary.ITEM_PROPERTY_SETMEAL, billNum, billNum });

				StringBuilder sql = new StringBuilder();
				sql.append(" UPDATE pos_kvs_bill SET upload_tag=0, mop_time = current_timestamp, meal_consume= to_timestamp(to_char(current_timestamp - order_time, 'HH24:MI:SS'), 'HH24:MI:SS')");
				sql.append(" where bill_num = ? and tenancy_id = ? and store_id = ? AND 0=(select count(*) from pos_kvs_bill_item where bill_num = ? and tenancy_id = ? and store_id = ?)");

				paymentDao.update(sql.toString(), new Object[]
				{ billNum, tenancyId, storeId, billNum, tenancyId, storeId });

				Data cjData = new Data();
				cjData.setType(Type.KVS_INFO);
				cjData.setOper(Oper.find);
				// List<JSONObject> cometList = new ArrayList<JSONObject>();
				// cjData.setData(cometList);
				Comet4jUtil.sendMessage2All(Comet4jUtil.POS_PC, JSONObject.fromObject(cjData).toString());
				Comet4jUtil.sendMessage2All(Comet4jUtil.POS_ANDROID, JSONObject.fromObject(cjData).toString());
			}
		}
	}

    /**
     * 计算账单和账单明细的代销价格
     * @param tenancyId
     * @param storeId
     * @param billNum
     * @throws Exception
     */
	private void calculateSettlementPrice(String tenancyId, int storeId, String billNum) throws Exception{
	    // 更新账单明细表是否代销品 和 代销价格
        StringBuffer updateItemSql = new StringBuffer("update pos_bill_item set is_consignment = (select coalesce(d.is_consignment,0) from hq_item_menu_details d where d.id = details_id), settlement_price = (select case when coalesce(d.is_consignment,0) = 1 then coalesce(settlement_price,0) else 0 end from hq_item_menu_details d where d.id = details_id) where bill_num = ? and store_id = ? and tenancy_id = ? ");
        paymentDao.update(updateItemSql.toString(), new Object[]{billNum, storeId, tenancyId});

        // 套餐主项代销价更新为0
        StringBuffer updateSetmealSql = new StringBuffer("update pos_bill_item set is_consignment = 0, settlement_price=0 where bill_num = ? and item_property = ? and store_id = ? and tenancy_id = ? ");
        paymentDao.update(updateSetmealSql.toString(), new Object[]{billNum, SysDictionary.ITEM_PROPERTY_SETMEAL, storeId, tenancyId});

        // 更新账单表中总代销价格
        StringBuffer updateBillSql = new StringBuffer("update pos_bill set settlement_price = (select sum(coalesce(i.settlement_price,0)*coalesce(i.item_count,1)) from pos_bill_item i where i.bill_num = ? and i.store_id = ? and i.tenancy_id = ?) where bill_num = ? and store_id = ? and tenancy_id = ? ");
        paymentDao.update(updateBillSql.toString(), new Object[]{billNum, storeId, tenancyId, billNum, storeId, tenancyId});
    }

	/** 通知云端
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @throws Exception
	 */
	private void uploadCloudBill(String tenancyId,Integer storeId,String billNum) throws Exception
	{
		try
        {
			//通知云端异常
            String handleClient = "";
            String subSql = "select s.handle_client from pos_bill_sub s where s.bill_num = ? and s.store_id = ? and s.tenancy_id = ?";

            SqlRowSet rowSet = paymentDao.query4SqlRowSet(subSql, new Object[]{billNum, storeId, tenancyId});
            if(rowSet.next())
            {
                handleClient = rowSet.getString("handle_client");
            }

            if(SysDictionary.CLOUD_CLIENT_HANDLE.equals(handleClient))
            {
                String upateSubSql = "update pos_bill_sub set pay_client = ?, query_time = ? where bill_num = ? and store_id = ? and tenancy_id = ?";
                paymentDao.update(upateSubSql, new Object[]{SysDictionary.STORE_CLIENT_PAY, DateUtil.currentTimestamp(), billNum, storeId, tenancyId});

                PosBillToCloudBillRunnable toCloudBillRunnable = new PosBillToCloudBillRunnable(tenancyId, storeId, billNum);
                Thread toCloudT = new Thread(toCloudBillRunnable);
                toCloudT.start();
            }
        }
        catch (Exception e)
        {
            logger.error("门店已结账单："+billNum+"，通知云端异常：", e);
        }
	}

	/** 生成电子发票
	 * @param tenancyId
	 * @param storeId
	 * @param reportDate
	 * @param posNum
	 * @param optNum
	 * @param isInvoice
	 * @param billJson
	 * @param printJson
	 * @throws Exception
	 */
	private void createInvoiceInfo(String tenancyId,Integer storeId,String reportDate,String posNum,String optNum,String isInvoice,JSONObject billJson,JSONObject printJson) throws Exception
	{
		try
		{
			String billNum = null;
			String orderNum = null;
			String chanel = null;
			int recoverCnt = 0;
			if (null != billJson)
			{
				billNum = billJson.optString("bill_num");
				orderNum = billJson.optString("order_num");
				chanel = billJson.optString("source");
				recoverCnt = billJson.optInt("recover_count");// 取得恢复账单次数
			}

			// 生成电子发票
			JSONObject invoiceJo = paymentDao.getInvoiceInfo(tenancyId, storeId, billJson);
	//		if(!"1".equals(isInvoice))  // 如果选择状态是不开电子发票，需要确认是否为第三方支付
	//		{
				if(!Tools.isNullOrEmpty(invoiceJo) && "2".equals(invoiceJo.optString("invoice_type"))) // 第三方支付时，选择了打印电子发票二维码，生成二维码时发票金额为0。恢复账单结账时，生成二维码时发票金额是恢复前账单的金额。以此来区分
				{
					if(invoiceJo.optDouble("invoice_amount") != 0)
					{
						isInvoice = "1";
						paymentDao.deleteInvoiceByInvoiceId(tenancyId, storeId, billNum, invoiceJo.optInt("id"));
					}
					else
					{
						logger.info("已经开过电子发票，不可再开！");
					}
				}
	//		}

			// 开具电子发票
			if("1".equals(isInvoice)) // 再次判断开具发票状态
			{
				// 创建新的电子发票二维码
				logger.info("电子发票二维码生成开始!");
				// 准备生成电子发票的数据
				// 获取法人信息
				JSONObject legalPerInfoJo = paymentDao.getLegalPerInfo(tenancyId, storeId, billJson);
				if(!(Tools.isNullOrEmpty(legalPerInfoJo) || Double.isNaN(legalPerInfoJo.optDouble("tax_rate"))))
				{
					// 计算开发票金额
					double invAmt = calcAmountForInvoice(tenancyId, storeId, billJson);
					if(Double.isNaN(invAmt))
					{
						invAmt = 0;
					}
					if(invAmt > 0)
					{
						// 生成电子发票，获得电子发票的打印信息
						JSONObject jo = new JSONObject();
						jo.put("bill_num", billNum);
						jo.put("order_num", orderNum);
						jo.put("pos_num", posNum);
						jo.put("opt_num", optNum);
						jo.put("source", chanel);
						jo.put("report_date", reportDate);
						jo.put("tax_rate", legalPerInfoJo.optDouble("tax_rate"));
						jo.put("invoice_num", legalPerInfoJo.optString("invoice_num"));
						jo.put("invoice_amount", invAmt);
						jo.put("recover_count", recoverCnt);
						logger.info("电子发票二维码生成开始：传入参数：" + jo.toString());
						getPrintInvoiceInfo(tenancyId, storeId,DateUtil.parseDate(reportDate), jo);

						logger.info("加密前的二维码URL:" + jo.optString("before_encode_url"));
						logger.info("生成的二维码URL: " + jo.optString("url_content"));

						printJson.put("is_invoice", isInvoice);
						printJson.put("url_content", jo.optString("url_content"));
						printJson.put("url_path", jo.optString("url_path"));
//						resultJson.put("url_content", jo.optString("url_content"));
					}
					else
					{
						logger.info("电子发票金额为0!");
					}
				}
				else
				{
					logger.info("法人信息设置错误!");
				}
			}
		}
		catch(Exception e)
		{
			e.printStackTrace();
			logger.error("电子发票二维码生成失败:", e);
		}
	}


	private void insertInvoiceInfo(String tenancyId,Integer storeId,String reportDate,String posNum,String optNum,JSONObject billJson) throws Exception
	{
		try
		{
			String billNum = billJson.optString("bill_num");

			// 计算开发票金额
			double invAmt = calcAmountForInvoice(tenancyId, storeId, billJson);
			if(Double.isNaN(invAmt))
			{
				invAmt = 0;
			}
			if(invAmt > 0)
			{
				Timestamp timestamp = DateUtil.currentTimestamp();
				// 插入发票表
				StringBuilder sql = new StringBuilder("insert into pos_bill_invoice (tenancy_id,store_id,pos_num,opt_num,report_date,bill_num,invoice_num,invoice_count,invoice_amount,last_updatetime,invoice_type) values (?,?,?,?,?,?,?,?,?,?,?)");
				paymentDao.update(sql.toString(), new Object[]{tenancyId, storeId, posNum, optNum, reportDate, billNum, "1", "1", invAmt, DateUtil.format(timestamp), 3});

			}
			else
			{
				logger.info("电子金额为0!");
			}
		}
		catch(Exception e)
		{
			e.printStackTrace();
			logger.error("关单过程中插入发票记录错误:", e);
		}
	}


	/** 发送配送订单
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @throws Exception
	 */
	private void addOrderDelivery(String tenancyId,Integer storeId,String billNum) throws Exception
	{
		try
		{
			//发送配送订单
			String diningType = "";
			JSONObject orderJo = new JSONObject();
			List<JSONObject> orderListJson = new ArrayList<JSONObject>();
			String openThird = paymentDao.getSysParameter(tenancyId, storeId, "KQDSFPSPT");
			if(false == "1".equals(openThird))
			{
				return;
			}

			String diningTypeSql = "select hd.is_open as dinner_type from pos_bill pb LEFT JOIN  hq_dining_type hd on pb.dinner_type = hd.dining_code where pb.tenancy_id = ? and pb.store_id = ? and  pb.bill_num = ?";
			SqlRowSet diningTypeRowSet = paymentDao.query4SqlRowSet(diningTypeSql, new Object[]{tenancyId, storeId,billNum});
			if(diningTypeRowSet.next()){
				diningType = diningTypeRowSet.getString("dinner_type");
			}

			if(false == "1".equals(diningType))
			{
				return;
			}

			String orderSql = "select order_code,type,shop_id,channel,remark,receivername,receiverlat,receiverlng,receiveraddress,actual_price,receiverphone,tips from cc_deliver_list where order_code = ? and shop_id = ?";
			SqlRowSet orderRowSet = paymentDao.query4SqlRowSet(orderSql, new Object[]{billNum,String.valueOf(storeId)});
			if(orderRowSet.next()){
				orderJo.put("tanancyId", tenancyId);
				orderJo.put("type", orderRowSet.getString("type"));
				orderJo.put("shopId", orderRowSet.getString("shop_id"));
				orderJo.put("orderCode", orderRowSet.getString("order_code"));
				orderJo.put("channel", orderRowSet.getString("channel"));
				orderJo.put("remark", orderRowSet.getString("remark"));
				orderJo.put("receiverName", orderRowSet.getString("receivername"));
				orderJo.put("receiverLat", orderRowSet.getString("receiverlat"));
				orderJo.put("receiverLng", orderRowSet.getString("receiverlng"));
				orderJo.put("receiverAddress", orderRowSet.getString("receiveraddress"));
				orderJo.put("cargoPrice", orderRowSet.getDouble("actual_price"));
				orderJo.put("receiverPhone", orderRowSet.getString("receiverphone"));
				orderJo.put("tips", orderRowSet.getDouble("tips"));

				orderListJson.add(orderJo);
			}

			if("1".equals(openThird)&&"1".equals(diningType)&&orderJo.optString("receiverAddress")!=null&&!"".equals(orderJo.optString("receiverAddress"))){
				logger.info("发送配送订单----");
				Data orderParamData = new Data();
				Data orderResultData = new Data();
				orderParamData.setData(orderListJson);
				orderDeliveryService.addOrderDelivery(orderParamData, orderResultData);
			}
		}
		catch (Exception e)
		{
			logger.info("新增配送订单失败",e);
		}
	}

//	private void getPaymentException(Data resultData, int code, String msg) throws Exception
//	{
//		if (Constant.CODE_CONN_EXCEPTION == code)
//		{
//			resultData.setCode(Constant.CODE_CONN_EXCEPTION);
//			resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
//		}
//		else if (CrmErrorCode.BALANCE_NOTENOUGH_ERROE.getNumber() == code)
//		{
//			resultData.setCode(code);
//			resultData.setMsg(PropertiesLoader.getProperty(String.valueOf(code)));
//		}
//		else
//		{
//			resultData.setCode(PosErrorCode.BILL_PAYMENT_ERROP.getNumber());
//			resultData.setMsg(PosErrorCode.BILL_PAYMENT_ERROP.getMessage().replace("{0}", msg));
//		}
//	}

	/**
	 *
	 * @param tenancyId
	 * @param paymentClass
	 * @throws Exception
	 */
	private void getPaymentServiceBeanByPaymentClass(String tenancyId,int storeId, String paymentClass,boolean isCheck) throws Exception
	{
		String customerType = null;
		if (SysDictionary.PAYMENT_CLASS_CARD.equals(paymentClass) || SysDictionary.PAYMENT_CLASS_CARD_CREDIT.equals(paymentClass) || (isCheck && SysDictionary.PAYMENT_CLASS_COUPONS.equals(paymentClass)))
		{
			customerType = paymentDao.getCustomerType(tenancyId, storeId);
		}
		posPaymentService = PaymentWayServiceImp.getPaymentServiceBeanByPaymentClass(tenancyId, storeId, paymentClass, customerType, isCheck);
	}

//	/** 封装消费接口
//	 * @param tenancyId
//	 * @param storeId
//	 * @param reportDate
//	 * @param shiftId
//	 * @param posNum
//	 * @param optNum
//	 * @param billNum
//	 * @param paymentId
//	 * @param transactionNo
//	 * @param isPrintBill
//	 * @return
//	 * @throws Exception
//	 */
//	private Data getPayParam(String tenancyId,Integer storeId,String reportDate,Integer shiftId,String posNum,String optNum,String billNum,Integer paymentId,String transactionNo,String isPrintBill) throws Exception
//	{
//		JSONObject postBill = paymentDao.getPosBillByBillnum(tenancyId, storeId, billNum);
//
//		JSONObject data = new JSONObject();
//		data.put("report_date", reportDate);
//		data.put("shift_id", shiftId);
//		data.put("pos_num", posNum);
//		data.put("opt_num", optNum);
//		data.put("bill_num", billNum);
//		data.put("table_code", postBill.optString("table_code"));
//		data.put("payment_amount", postBill.opt("payment_amount"));
//		data.put("difference", postBill.opt("difference"));
//		data.put("sale_mode", postBill.optString("sale_mode"));
//		data.put("isprint_bill", isPrintBill);
//		// item
//
//		JSONObject item = paymentDao.getBillPaymentByJzid(tenancyId, storeId, billNum, paymentId);
//		item.put("bill_code", transactionNo);
//		item.put("consume_credit", 0);
//		item.put("consume_creditmoney", 0);
//		item.put("integraloffset", 0);
//		JSONArray items = new JSONArray();
//		items.add(item);
//		data.put("item", items);
//
//		List<JSONObject> datas = new ArrayList<JSONObject>();
//		datas.add(data);
//
//		Data retData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
//		retData.setType(Type.BILL_PAYMENT);
//		retData.setData(datas);
//
//		return retData;
//	}

	@Override
	public void wrongPayDetailQuery(Data param, Data result)
			throws Exception {
		// TODO Auto-generated method stub
		String tenantId = param.getTenancy_id();
		Integer organId = param.getStore_id();

		Map<String, Object> map = ReqDataUtil.getDataMap(param);
		// 订单号
		String orderNum = ParamUtil.getStringValue(map, "order_num", false, null);
		// 报表日期
		Date queryDate = ParamUtil.getDateValue(map, "query_date", false, null);
		String reportDateStr = "";
		if(queryDate == null){ // 默认显示当天的异常流水记录
			reportDateStr = com.tzx.pos.base.util.DateUtil.format(new Date(), "yyyy-MM-dd");
		}else{
			reportDateStr = com.tzx.pos.base.util.DateUtil.format(queryDate, "yyyy-MM-dd");
		}
		// 付款方式
		String paymentType = ParamUtil.getStringValue(map, "payment_type", false, null);
		// 业务类型
		String serviceType = ParamUtil.getStringValue(map, "service_type", false, null);
		// 交易类型（操作类型）
		String objectName = ParamUtil.getStringValue(map, "object_name", false, null);
		// 状态
		String status = ParamUtil.getStringValue(map, "status", false, null);
		if(StringUtils.isEmpty(status)){ // 默认状态，失败、付款中、付款失败、退款中
			status = "'"+SysDictionary.THIRD_PAY_STATUS_FAIL+"', '"+SysDictionary.THIRD_PAY_STATUS_PAYING+"', '"+SysDictionary.THIRD_PAY_STATUS_NOOK+"', '"+SysDictionary.THIRD_PAY_STATUS_REFUND+"', '"+SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL+"'";
		}else{
			status = "'"+status+"'";
		}

		// 分页
		Pagination pagination = param.getPagination();

		int limit = 10;
		int pageNo = 1;
		String orderBy = null;
		String ascDesc = "asc";
		int totalCount = 0;

		if (pagination != null && pagination.getPagesize() > 0)
		{
			limit = pagination.getPagesize();
			if ((pagination.getPageno() - 1) > 0)
			{
				pageNo = pagination.getPageno();
			}

			orderBy = pagination.getOrderby();

			if (pagination.isAsc() == false)
			{
				ascDesc = "desc";
			}
		}

		// 拼接sql
		StringBuilder sql = new StringBuilder();
		sql.append("select po.order_num, pw.payment_name1, (case when po.status = '"+SysDictionary.THIRD_PAY_STATUS_FAIL+"' then '支付失败' when po.status = '"+SysDictionary.THIRD_PAY_STATUS_SUCCESS+"' then '支付成功' when po.status = '"+SysDictionary.THIRD_PAY_STATUS_PAYING+"' then '处理中' when po.status = '"+SysDictionary.THIRD_PAY_STATUS_NOOK+"' then '未知' when po.status = '"+SysDictionary.THIRD_PAY_STATUS_REFUND_SUCCESS+"' then '已退款' when po.status = '"+SysDictionary.THIRD_PAY_STATUS_REFUND+"' then '退款中' when po.status = '"+SysDictionary.THIRD_PAY_STATUS_REVOKED+"' then '已取消' when po.status = '"+SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL+"' then '退款失败' else '' end) as status, ");
		sql.append(" (case when po.service_type = '"+SysDictionary.SERVICE_TYPE_RECHARGE+"' then '会员充值' when po.service_type = '"+SysDictionary.SERVICE_TYPE_COUPON+"' then '商城券' when po.service_type = '"+SysDictionary.SERVICE_TYPE_ORDER+"' then '微点餐/外卖' when po.service_type = '"+SysDictionary.SERVICE_TYPE_CONSUME+"' then 'pos点餐消费' else '' end) as service_type, (case when po.object_name = '"+SysDictionary.THIRD_PAY_CHARGE+"' then '支付' when po.object_name = '"+SysDictionary.THIRD_PAY_REFUND+"' then '退款' else '' end) as object_name,");
		sql.append(" po.paid_time, coalesce(po.request_msg, po.failure_msg) as request_msg, po.settle_amount, po.total_amount, e.name opt_name, hd.devices_name from pos_third_payment_order po left join payment_way pw on po.payment_type = cast(pw.id as varchar) left join employee e on po.opt_num = cast(e.id as varchar) left join hq_devices hd on po.pos_num = hd.devices_code where po.report_date = '"+reportDateStr+"' and po.status in ("+status+") and po.store_id = ? and po.tenancy_id = ? ");

		if(StringUtils.isNotEmpty(orderNum)){
			sql.append(" and po.order_num = '"+orderNum+"' ");
		}
		if(StringUtils.isNotEmpty(paymentType)){
			sql.append(" and po.payment_type = '"+paymentType+"' ");
		}
		if(StringUtils.isNotEmpty(serviceType)){
			sql.append(" and po.service_type = '"+serviceType+"' ");
		}
		if(StringUtils.isNotEmpty(objectName)){
			sql.append(" and po.object_name = '"+objectName.toUpperCase()+"' ");
		}

		// 总条数
		totalCount = paymentDao.queryForInt("select count(1) as count from (" + sql.toString() + ") ct", new Object[]{organId, tenantId});

		// 排序后分页
		if (pagination != null && pagination.getPagesize() > 0)
		{
			if (StringUtils.isEmpty(pagination.getOrderby()))
			{
				sql.append(" order by po.order_num desc "); // 默认排序
			}
			else
			{
				sql.append(" order by " + orderBy + " " + ascDesc);
			}

			sql.append(" limit " + limit + " offset " + (pageNo - 1) * pagination.getPagesize());
		} else {
			sql.append(" order by po.order_num desc "); // 默认排序
		}

		// 查询异常支付流水
		List<JSONObject> payList = paymentDao.query4Json(tenantId, sql.toString(), new Object[]{organId, tenantId});

		pagination.setPageno(pagination.getPageno() > 0 ? pagination.getPageno() : 1);
		pagination.setTotalcount(totalCount);

		if (pagination.getPagesize() == 0)
		{
			pagination.setPagesize(payList.size());
		}
		result.setPagination(pagination);

		if (payList != null && payList.size() != 0)
		{
			result.setData(payList);
		}

		result.setCode(Constant.CODE_SUCCESS);
		result.setMsg(Constant.WRONG_THIRD_PAYMENT_SUCCESS);
	}

	@Override
	public synchronized void paymentResultByQuery(String tenancyId, int storeId, JSONObject para, JSONObject printJson) throws Exception
	{
		String report_Date = ParamUtil.getDateStringValue(para, "report_date");
		Date reportDate = DateUtil.parseDate(report_Date);
		String billNum = para.optString("bill_num");
		int shiftId = para.optInt("shift_id");
		String posNum = para.optString("pos_num");
		String optNum = para.optString("opt_num");
		String saleMode = para.optString("sale_mode");
		String tableCode = para.optString("table_code");
		String isprint_bill = para.optString("isprint_bill");
		String isInvoice = para.optString("is_invoice");
		if (Tools.isNullOrEmpty(isprint_bill))
		{
			isprint_bill = "N";
		}
		// 设备类型
		String source = para.optString("source");
		printJson.put("source", source);

		String isOnlineReplayent = "0";

		if (Tools.isNullOrEmpty(reportDate))
		{
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_REPORT_DATE);
		}
		if (Tools.isNullOrEmpty(billNum))
		{
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_BILL_NUM);
		}
		if (Tools.isNullOrEmpty(para.opt("item")))
		{
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_ITEM_LIST);
		}

		Timestamp currentTime = DateUtil.currentTimestamp();

		double paymentAmount = 0d;
		double difference = 0d;
		String paymentState = "";
		String chanel = "";
		String billProperty = "";

		StringBuilder oldstate = null;
		StringBuilder newstate = null;
		try
		{
			oldstate = new StringBuilder("账单编号:" + billNum + "; ");
			newstate = new StringBuilder();

			JSONObject billJson = paymentDao.getPosBillByBillnum(tenancyId, storeId, billNum);
			JSONObject transferJson = null;
			if (null != billJson)
			{
				paymentAmount = billJson.optDouble("payment_amount");
				difference = billJson.optDouble("difference");
				chanel = billJson.optString("source");
				billProperty = billJson.optString("bill_property");
				paymentState = billJson.optString("payment_state");

				if (Tools.isNullOrEmpty(saleMode))
				{
					saleMode = billJson.optString("sale_mode");
				}

				if (Tools.isNullOrEmpty(saleMode))
				{
					saleMode = SysDictionary.SALE_MODE_TS01;
				}

				if (Tools.isNullOrEmpty(tableCode))
				{
					if (Tools.hv(billJson.optString("table_code")))
					{
						tableCode = billJson.optString("table_code");
					}
				}

				if (Tools.hv(billJson.optString("transfer_remark")))
				{
					transferJson = JSONObject.fromObject(billJson.optString("transfer_remark"));
				}
			}

			if (null != transferJson && transferJson.containsKey("isprint_bill"))
			{
				isprint_bill = transferJson.optString("isprint_bill");
			}

			if (null != transferJson && transferJson.containsKey("is_invoice"))
			{
				isInvoice = transferJson.optString("is_invoice");
			}

			oldstate.append("应付金额:").append(String.valueOf(paymentAmount)).append("; ");

			if (SysDictionary.BILL_PROPERTY_CLOSED.equalsIgnoreCase(billProperty))
			{
				throw SystemException.getInstance(PosErrorCode.BILL_CLOSED);
			}

			for (Object itemObj : para.optJSONArray("item"))
			{
				JSONObject itemJson = JSONObject.fromObject(itemObj);
				itemJson.put("chanel", chanel);

				if (Tools.isNullOrEmpty(itemJson.opt("jzid")))
				{
					throw SystemException.getInstance(PosErrorCode.NOT_NULL_JZID);
				}

				if (Tools.isNullOrEmpty(itemJson.opt("amount")))
				{
					throw SystemException.getInstance(PosErrorCode.NOT_NULL_PAYMENT_AMOUNT);
				}

				double amount = itemJson.optDouble("amount");
				String paymentClass = itemJson.optString("type");
				String paymentName = itemJson.optString("name");
				boolean isCheck = true;

				this.getPaymentServiceBeanByPaymentClass(tenancyId, storeId, paymentClass, isCheck);

				posPaymentService.paymentDebitResult(tenancyId, storeId, billNum, itemJson);

				newstate.append("支付方式:").append(paymentName).append(",").append("支付金额:").append(amount).append(";");
			}

			Double billPaymentAmount = 0d;
			List<JSONObject> paymentList = paymentDao.getBillPaymentByBillnum(tenancyId, storeId, billNum);
			for (JSONObject pay : paymentList)
			{
				// pay.put("payment_state_text",SysDictionary.PAYMENT_STATE_TEXT_MAP.get(pay.optString("payment_state")));
				billPaymentAmount = DoubleHelper.add(billPaymentAmount, DoubleHelper.sub(pay.optDouble("currency_amount"), pay.optDouble("more_coupon"), DEFAULT_SCALE), DEFAULT_SCALE);
			}

			difference = DoubleHelper.sub(paymentAmount, billPaymentAmount, DEFAULT_SCALE);

			if (difference <= 0)
			{
				//关闭账单
				this.closedPosBill(tenancyId, storeId, billNum, report_Date, shiftId, posNum, optNum, isprint_bill, isInvoice, saleMode, billJson, printJson, isOnlineReplayent, currentTime);
			}
			else
			{
				//修改账单状态
				paymentState = this.getBillPaymentStateByList(paymentAmount, difference, paymentList);
				paymentDao.updatePosBillForStateByBillnum(tenancyId, storeId, billNum, paymentState, difference);
			}
		}
		catch (SystemException e)
		{
			throw e;
		}
		catch (Exception e)
		{
			logger.info("结账失败", e);
			e.printStackTrace();
			throw SystemException.getInstance(PosErrorCode.OPER_ERROR);
		}
		finally
		{
			try
			{
				paymentDao.savePosLog(tenancyId, storeId, posNum, optNum, optNum, shiftId, reportDate, com.tzx.pos.base.Constant.TITLE, "支付成功", oldstate.toString(), newstate.toString(), currentTime);
			}
			catch (Exception e)
			{
				logger.info("报错日志报错:", e);
				e.printStackTrace();
			}
		}

		// 数据上传
		upload(tenancyId, storeId + "", "", tableCode, "", billNum);
	}

//	@Override
//	public synchronized void thirdPaymentSuccess(Data param, Data result, JSONObject printJson) throws Exception
//	{
//		// TODO Auto-generated method stub
//		String tenancyId = param.getTenancy_id();
//		int storeId = param.getStore_id();
//
//		List<?> paramList = param.getData();
//		if (paramList == null || paramList.isEmpty())
//		{
//			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
//		}
//		JSONObject para = JSONObject.fromObject(paramList.get(0));
//
//		String report_Date = ParamUtil.getDateStringValue(para, "report_date");
//		Date reportDate = DateUtil.parseDate(report_Date);
//		String billNum = para.optString("bill_num");
//		int shiftId = para.optInt("shift_id");
//		String posNum = para.optString("pos_num");
//		String optNum = para.optString("opt_num");
//		String saleMode = para.optString("sale_mode");
//		String tableCode = para.optString("table_code");
//		String isprint_bill = para.optString("isprint_bill");
//		String isInvoice = para.optString("is_invoice");
//		if (Tools.isNullOrEmpty(isprint_bill))
//		{
//			isprint_bill = "N";
//		}
//		// 设备类型
//		String source = para.optString("source");
//        printJson.put("source", source);
//
//		String isOnlineReplayent = "0";
//
//		if (Tools.isNullOrEmpty(reportDate))
//		{
//			throw SystemException.getInstance(PosErrorCode.NOT_NULL_REPORT_DATE);
//		}
//		if (Tools.isNullOrEmpty(billNum))
//		{
//			throw SystemException.getInstance(PosErrorCode.NOT_NULL_BILL_NUM);
//		}
//		if (Tools.isNullOrEmpty(para.opt("item")))
//		{
//			throw SystemException.getInstance(PosErrorCode.NOT_NULL_ITEM_LIST);
//		}
//
//		Timestamp currentTime = DateUtil.currentTimestamp();
//
//		double paymentAmount = 0d;
//		double difference = 0d;
//		double discountAmount = 0d;
//		double discountRate = 0d;
//		int discountCaseId = 0;
//		String paymentState="";
//		String chanel = "";
//		String billProperty = "";
////		String batchNum = "";
////		String orderNum = "";
//		
//		StringBuilder oldstate = null;
//		StringBuilder newstate = null;
//		try
//		{
//			oldstate = new StringBuilder("账单编号:" + billNum + "; ");
//			newstate = new StringBuilder();
//			
//			JSONObject billJson = paymentDao.getPosBillByBillnum(tenancyId, storeId, billNum);
//			if (null != billJson)
//			{
//				paymentAmount = billJson.optDouble("payment_amount");
//				difference = billJson.optDouble("difference");
//				discountRate = billJson.optDouble("discount_rate");
//				discountAmount = billJson.optDouble("discount_amount");
//				discountCaseId = billJson.optInt("discount_case_id");
//				chanel = billJson.optString("source");
//				billProperty = billJson.optString("bill_property");
//				paymentState = billJson.optString("payment_state");
//				if (discountRate <= 0)
//				{
//					discountRate = 100d;
//				}
//	//			batchNum = billJson.optString("batch_num");
//	//			orderNum = billJson.optString("order_num");
//				if(Tools.isNullOrEmpty(saleMode))
//				{
//					saleMode = billJson.optString("sale_mode");
//				}
//				
//				if (Tools.isNullOrEmpty(saleMode))
//				{
//					saleMode = SysDictionary.SALE_MODE_TS01;
//				}
//	
//				if(Tools.isNullOrEmpty(tableCode))
//				{
//					if(Tools.hv(billJson.optString("table_code")))
//					{
//						tableCode = billJson.optString("table_code");
//					}
//				}
//			}
//	
//			oldstate.append("应付金额:").append(String.valueOf(paymentAmount)).append("; ");
//			
//			if (SysDictionary.BILL_PROPERTY_CLOSED.equalsIgnoreCase(billProperty))
//			{
//				throw SystemException.getInstance(PosErrorCode.BILL_CLOSED);
//			}
//	
//			for (Object itemObj : para.optJSONArray("item"))
//			{
//				JSONObject itemJson = JSONObject.fromObject(itemObj);
//				itemJson.put("chanel", chanel);
//	
//				if (Tools.isNullOrEmpty(itemJson.opt("jzid")))
//				{
//					throw SystemException.getInstance(PosErrorCode.NOT_NULL_JZID);
//				}
//	
//				if (Tools.isNullOrEmpty(itemJson.opt("amount")))
//				{
//					throw SystemException.getInstance(PosErrorCode.NOT_NULL_PAYMENT_AMOUNT);
//				}
//	
//				int jzid = itemJson.optInt("jzid");
//				double amount = itemJson.optDouble("amount");
//	
//				String paymentClass = "";
//				String paymentName = "";
//				String paymentEnglishName = "";
//				double exchangeRate = 1d;
//				boolean isCheck = true;
//	
//				JSONObject paymentWayJson = paymentDao.getPaymentWayByID(tenancyId, storeId, jzid);
//	
//				if (null != paymentWayJson && !paymentWayJson.isEmpty())
//				{
//					paymentClass = paymentWayJson.getString("payment_class");
//					paymentName = paymentWayJson.getString("payment_name");
//					paymentEnglishName = paymentWayJson.getString("name_english");
//					exchangeRate = Tools.hv(paymentWayJson.get("rate")) ? paymentWayJson.getDouble("rate") : 1d;
//	
//					if (SysDictionary.PAYMENT_CLASS_COUPONS.equals(paymentClass) && Tools.hv(itemJson.optString("number")))
//					{
//						if (Tools.isNullOrEmpty(itemJson.opt("is_check")))
//						{
//							isCheck = "1".equals(paymentWayJson.getString("is_check"));
//						}
//						else
//						{
//							isCheck = "1".equals(itemJson.getString("is_check"));
//						}
//					}
//	
//					itemJson.put("payment_class", paymentClass);
//					itemJson.put("payment_name", paymentName);
//					itemJson.put("payment_english_name", paymentEnglishName);
//					itemJson.put("rate", exchangeRate);
//					itemJson.put("is_check", isCheck);
//				}
//				else
//				{
//					throw SystemException.getInstance(PosErrorCode.NOT_EXIST_JZID);
//				}
//	
//				if (paymentAmount > 0)
//				{
//					if (SysDictionary.PAYMENT_CLASS_FREESINGLE.equals(paymentClass))
//					{
//						itemJson.put("payment_amount", paymentAmount);
//						itemJson.put("discount_case_id", discountCaseId);
//						itemJson.put("discount_rate", discountRate);
//						itemJson.put("discount_amount", discountAmount);
//					}
//					this.getPaymentServiceBeanByPaymentClass(tenancyId,storeId, paymentClass,isCheck);
//				}
//				else
//				{
//					posPaymentService = SpringConext.getApplicationContext().getBean(OtherPaymentWayServiceImp.class);
//				}
//	
//	//			posPaymentService.payment(tenancyId, storeId, billNum, batchNum, reportDate, shiftId, posNum, optNum, tableCode, itemJson, currentTime, isOnlineReplayent);
//				
//				double currencyAmount = itemJson.optDouble("currency_amount");
//				String number = itemJson.optString("number");
//				String phone = itemJson.optString("phone");
//				String billCode = itemJson.optString("bill_code");
//				
//				JSONObject paymentParam = paymentDao.getBillPaymentByJzid(tenancyId, storeId, billNum, jzid);
//				paymentParam.put("number", number);
//				paymentParam.put("phone", phone);
//				paymentParam.put("bill_code", billCode);
//	
//				if (paymentParam.optDouble("currency_amount") == currencyAmount)
//				{
//					posPaymentService.paymentDebitSuccess(tenancyId, storeId, billNum, paymentParam);
//				}
//				else
//				{
//					posPaymentService.paymentDebitFailure(tenancyId, storeId, billNum, paymentParam.optInt("id"), Constant.CODE_PARAM_FAILURE, Constant.AMOUNT_FAILURE);
//				}
//				
//	//			finalpaymentClass = paymentWayJson.getString("payment_class");
//				
//				newstate.append("支付方式:").append(paymentName).append(",").append("支付金额:").append(amount).append(";");
//			}
//	
//			Double billPaymentAmount = 0d;
//			List<JSONObject> paymentList = paymentDao.getBillPaymentByBillnum(tenancyId, storeId, billNum);
//			for (JSONObject pay : paymentList)
//			{
////				pay.put("payment_state_text", SysDictionary.PAYMENT_STATE_TEXT_MAP.get(pay.optString("payment_state")));
//				billPaymentAmount = DoubleHelper.add(billPaymentAmount, DoubleHelper.sub(pay.optDouble("currency_amount"), pay.optDouble("more_coupon"), DEFAULT_SCALE), DEFAULT_SCALE);
//			}
//			
//			difference = DoubleHelper.sub(paymentAmount, billPaymentAmount, DEFAULT_SCALE);
//	
//	//		JSONObject resultJson = new JSONObject();
//			
//			if (difference <= 0)
//			{
//	//			paymentDao.updatePosBillForStateByBillnum(tenancyId, storeId, billNum, SysDictionary.PAYMENT_STATE_PAY);
//	
//				paymentDao.updatePosBillForStateByBillnum(tenancyId, storeId, billNum, SysDictionary.PAYMENT_STATE_PAY, difference, saleMode, tableCode, shiftId);
//	
//				paymentDao.updatePosBillItemForSalemodeByBillnum(tenancyId, storeId, billNum, saleMode, shiftId);
//	
//	//			List<JSONObject> paymentList = null;
//	//			paymentList = paymentDao.getMeiDaBillPaymentByBillnum(tenancyId, storeId, billNum);
//				/*if("Y".equals(isMeiDaCard)&& finalpaymentClass.equals(SysDictionary.PAYMENT_CLASS_MEIDA_CARD_PAY))//用的美大会员卡支付的
//				{//设置了美大会员卡				
//				}else{
//					paymentList = paymentDao.getBillPaymentUnfinishByBillnum(tenancyId, storeId, billNum);
//				}*/
//	//			if (null != paymentList && paymentList.size() > 0)
//	//			{
//	//				this.billPaymentDebited(tenancyId, storeId, billNum, orderNum, chanel, paymentAmount, paymentList, currentTime, resultJson, result);
//	//			}
//				
//				this.closedPosBill(tenancyId, storeId, billNum, report_Date, shiftId, posNum, optNum, isprint_bill, isInvoice,saleMode, billJson, printJson, isOnlineReplayent,currentTime);
//			
//			}
//			else
//			{
//	//			String reSql = new String(
//	//					"select id,jzid,name,amount,count,coalesce(number,'') as number,coalesce(phone,'') as phone,shift_id,pos_num,cashier_num,last_updatetime,coalesce(customer_id,0) as customer_id,coalesce(bill_code,'') as bill_code,coalesce(remark,'') as remark,currency_amount,payment_state from pos_bill_payment where tenancy_id=? and store_id = ? and bill_num =?");
//	//			List<JSONObject> paymentList = paymentDao.query4Json(tenancyId, reSql, new Object[]
//	//			{ tenancyId, storeId, billNum });
//	//			for (JSONObject pay : paymentList)
//	//			{
//	//				pay.put("payment_state_text", SysDictionary.PAYMENT_STATE_TEXT_MAP.get(pay.optString("payment_state")));
//	//			}
//	//
//	//			resultJson.put("difference", difference);
//	//			resultJson.put("change", 0d);
//	//			resultJson.put("paymentlist", paymentList);
//	//			resultJson.put("bill_num", billNum);
//	//			resultJson.put("table_code", tableCode);
//	//			resultJson.put("payment_amount", paymentAmount);
//	//			resultJson.put("discount_amount", discountAmount);
//	//			resultJson.put("payment_state", SysDictionary.PAYMENT_STATE_NOTPAY);
//	//			resultJson.put("payment_state_text", SysDictionary.PAYMENT_STATE_TEXT_MAP.get(SysDictionary.PAYMENT_STATE_NOTPAY));
//				paymentState = this.getBillPaymentStateByList(paymentAmount, difference, paymentList);
//				paymentDao.updatePosBillForStateByBillnum(tenancyId, storeId, billNum, paymentState, difference);
//			}
//			
//			JSONObject resultJson = this.getPosBillPaymentByBillNum(tenancyId, storeId, billNum, tableCode, paymentAmount, discountAmount, difference, paymentState);
//	
//			List<JSONObject> resultList = new ArrayList<JSONObject>();
//			resultList.add(resultJson);
//	
//			result.setData(resultList);
//			if (Constant.CODE_SUCCESS == result.getCode())
//			{
//				result.setCode(Constant.CODE_SUCCESS);
//				result.setMsg(Constant.PAYMENT_SUCCESS);
//			}
//			else if (Constant.PAYMENT_STATUS_PAYING_CODE == result.getCode())
//			{
//				result.setCode(Constant.CODE_SUCCESS);
//			}
//		}
//		catch (SystemException e)
//		{
//			throw e;
//		}
//		catch (Exception e)
//		{
//			logger.info("结账失败",e);
//			e.printStackTrace();
//			throw SystemException.getInstance(PosErrorCode.OPER_ERROR);
//		}
//		finally
//		{
//			try
//			{
//				paymentDao.savePosLog(tenancyId, storeId, posNum, optNum, optNum, shiftId, reportDate, com.tzx.pos.base.Constant.TITLE, "支付成功", oldstate.toString(), newstate.toString(),currentTime);
//			}
//			catch (Exception e)
//			{
//				logger.info("报错日志报错:", e);
//				e.printStackTrace();
//			}
//		}
//		
//		//数据上传
//		upload(tenancyId,storeId+"", "",tableCode,"",billNum);
//	}

//	@Override
//	public void thirdPaymentFailure(String tenancyId, int storeId,JSONObject paramJson) throws Exception
//	{
//		if (SysDictionary.BILL_PROPERTY_CLOSED.equals(paramJson.optString("bill_property")))
//		{
//			throw SystemException.getInstance(PosErrorCode.BILL_CLOSED);
//		}
//
//		Timestamp currentTime = DateUtil.currentTimestamp();
//
//		String billNum = paramJson.optString("bill_num");
//		Double paymentAmount = paramJson.optDouble("payment_amount");
//
//		String billPaymentState = paramJson.optString("payment_state");
//
//		this.deletePaymentForFailure(tenancyId, storeId, paramJson, currentTime);
//
//		this.updateBillPaymentState(tenancyId, storeId, billNum, paymentAmount, billPaymentState);
//	}

	/** 删除失败付款记录
	 * @param tenancyId
	 * @param storeId
	 * @param paramJson
	 * @param currentTime
	 * @throws Exception
	 */
	private void deletePaymentForFailure(String tenancyId, int storeId, JSONObject paramJson, Timestamp currentTime) throws Exception
	{
		Date reportDate = DateUtil.parseDate(paramJson.optString("report_date"));
		Integer shiftId = paramJson.optInt("shift_id");
		String posNum = paramJson.optString("pos_num");
		String optNum = paramJson.optString("cashier_num");
		String billNum = paramJson.optString("bill_num");

		int payId = paramJson.optInt("id");
		int jzid = paramJson.optInt("jzid");
		String table_code = paramJson.optString("table_code");
		String paymentClass = paramJson.optString("type");
		String paymentName = paramJson.optString("name");
		String paymentEnglishName = paramJson.optString("name_english");
		double exchangeRate = paramJson.optDouble("rate");
		double pay_amount = paramJson.optDouble("amount");
		double pay_count = paramJson.optDouble("count");
		double currency_amount = paramJson.optDouble("currency_amount");
		String number = paramJson.optString("number");
		String phone = paramJson.optString("phone");
		int customer_id = paramJson.optInt("customer_id");
		String remark = paramJson.optString("remark");
		String batch_num = paramJson.optString("batch_num");
		String is_ysk = Tools.hv(paramJson.optString("is_ysk")) ? paramJson.optString("is_ysk") : "N";

		paymentDao.deletePosBillPaymentByID(tenancyId, storeId, billNum, payId);

		paymentDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, table_code, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, customer_id, paramJson.optString("bill_code"),
				remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_FAILURE, batch_num, paramJson.optString("param_cach"), 0d);

//		if (SysDictionary.PAYMENT_CLASS_CARD.equals(paymentClass)||SysDictionary.PAYMENT_CLASS_WLIFE_BALANCE.equals(paymentClass))
//		{
//			memberDao.deletePosBillMember(tenancyId, storeId, billNum, SysDictionary.BILL_MEMBERCARD_CZXF03);
//		}
//		else if (SysDictionary.PAYMENT_CLASS_COUPONS.equals(paymentClass)||SysDictionary.PAYMENT_CLASS_WLIFE_COUPON.equals(paymentClass))
//		{
//			memberDao.deletePosBillMember(tenancyId, storeId, billNum, SysDictionary.BILL_MEMBERCARD_YHJ04);
//		}
//		else if (SysDictionary.PAYMENT_CLASS_CARD_CREDIT.equals(paymentClass)||SysDictionary.PAYMENT_CLASS_WLIFE_CREDIT.equals(paymentClass))
//		{
//			memberDao.deletePosBillMember(tenancyId, storeId, billNum, SysDictionary.BILL_MEMBERCARD_JFDX05);
//		}
	}

	/** 更新账单付款状态已经付款差额
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param paymentAmount
	 * @param billPaymentState
	 * @throws Exception
	 */
	private void updateBillPaymentState(String tenancyId, int storeId,String billNum,Double paymentAmount,String billPaymentState) throws Exception
	{
		Double billPaymentAmount = 0d;
		List<JSONObject> paymentList = paymentDao.getBillPaymentByBillnum(tenancyId, storeId, billNum);
		for (JSONObject pay : paymentList)
		{
			billPaymentAmount = DoubleHelper.add(billPaymentAmount, DoubleHelper.sub(pay.optDouble("currency_amount"), pay.optDouble("more_coupon"), DEFAULT_SCALE), DEFAULT_SCALE);
		}

		double difference = DoubleHelper.sub(paymentAmount, billPaymentAmount, DEFAULT_SCALE);

		billPaymentState = this.getBillPaymentStateByList(paymentAmount, difference, paymentList);

		paymentDao.updatePosBillForStateByBillnum(tenancyId, storeId, billNum, billPaymentState, difference);
	}

//	/**
//	 * 数据上传
//	 * @param tenantId
//	 * @param store_id
//	 * @param old_table_code
//	 * @param new_table_code
//	 * @param old_bill_num
//	 * @param new_bill_num
//	 */
//	private void upload(String tenantId,String store_id,String old_table_code,String new_table_code,String old_bill_num,String new_bill_num){
//		DataUploadRunnable r = new DataUploadRunnable(tenantId,store_id,old_table_code,new_table_code,old_bill_num,new_bill_num);
//		Thread thread  =  new Thread(r);
//		thread.start();
//	}


	@Override
	@Deprecated
	public void paymentComplete(Data param, Data result, JSONObject printJson) throws Exception {
		// TODO Auto-generated method stub
		String tenancyId = param.getTenancy_id();
		int storeId = param.getStore_id();
		List<?> paramList = param.getData();
		if (paramList == null || paramList.isEmpty()) {
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}
		JSONObject para = JSONObject.fromObject(paramList.get(0));

		if (Tools.isNullOrEmpty(para.opt("bill_num"))) {
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_BILL_NUM);
		}

		if (Tools.isNullOrEmpty(para.opt("report_date"))) {
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_REPORT_DATE);
		}
		String report_date = para.optString("report_date");
		Date reportDate = DateUtil.parseDate(report_date);
		int shiftId = para.optInt("shift_id");
		String billNum = para.optString("bill_num");
		String posNum = para.optString("pos_num");
		String optNum = para.optString("opt_num");
		String isprint_bill = para.optString("isprint_bill");
		String isInvoice = para.optString("is_invoice");

		if (Tools.isNullOrEmpty(isprint_bill)) {
			isprint_bill = "N";
		}
		int payId = para.optInt("pay_id");

		Timestamp currentTime = DateUtil.currentTimestamp();

		try
		{
			JSONObject billJson = paymentDao.getPosBillByBillnum(tenancyId, storeId, billNum);

			double paymentAmount = 0d;
			double difference = 0d;
			double discountAmount = 0d;
			String billPaymentState = "";
			String billProperty = "";
			String tableCode = null;
			String saleMode = "";
			if (null != billJson) {
				paymentAmount = billJson.optDouble("payment_amount");
				difference = billJson.optDouble("difference");
				discountAmount = billJson.optDouble("discount_amount");
				billProperty = billJson.optString("bill_property");
				billPaymentState = billJson.optString("payment_state");
				tableCode = billJson.optString("table_code");
				saleMode = billJson.optString("sale_mode");
			}

			if (SysDictionary.BILL_PROPERTY_CLOSED.equalsIgnoreCase(billProperty)) {
				JSONObject resultJson = this.getPosBillPaymentByBillNum(tenancyId, storeId, billNum, tableCode, paymentAmount, discountAmount, difference, billPaymentState);

				List<JSONObject> resultList = new ArrayList<JSONObject>();
				resultList.add(resultJson);

				result.setData(resultList);
				result.setCode(PosErrorCode.BILL_CLOSED.getNumber());
				result.setMsg(PosErrorCode.BILL_CLOSED.getMessage());
				return;
			}

			StringBuilder queryPaymentSql = new StringBuilder("select bp.*,pw.payment_class from pos_bill_payment bp left join payment_way pw on bp.tenancy_id=pw.tenancy_id and bp.jzid=pw.id where bp.tenancy_id=? and bp.store_id=? and bp.bill_num=? and bp.id=?");
			SqlRowSet rs = paymentDao.query4SqlRowSet(queryPaymentSql.toString(), new Object[]
					{tenancyId, storeId, billNum, payId});
			if (rs.next()) {
	//			int jzid = rs.getInt("jzid");
	//			String table_code = rs.getString("table_code");
	//			String paymentClass = rs.getString("payment_class");
	//			String paymentName = rs.getString("name");
	//			String paymentEnglishName = rs.getString("name_english");
	//			double exchangeRate = rs.getDouble("rate");
	//			double pay_amount = rs.getDouble("amount");
	//			double pay_count = rs.getDouble("count");
	//			double currency_amount = rs.getDouble("currency_amount");
	//			String number = rs.getString("number");
	//			String phone = rs.getString("phone");
	//			int customer_id = rs.getInt("customer_id");
	//			String remark = rs.getString("remark");
	//			String batch_num = rs.getString("batch_num");
	//			String is_ysk = Tools.hv(rs.getString("is_ysk")) ? rs.getString("is_ysk") : "N";

				String paymentState = rs.getString("payment_state");
				if (SysDictionary.PAYMENT_STATE_PAY.equals(paymentState) || SysDictionary.PAYMENT_STATE_PAY_FAILURE.equals(paymentState)) {
					StringBuilder updatePaymentSql = new StringBuilder("update pos_bill_payment set payment_state=?,pos_num=?,cashier_num=?,last_updatetime=? where tenancy_id=? and store_id=? and bill_num=? and id=?");
					paymentDao.update(updatePaymentSql.toString(), new Object[]
							{SysDictionary.PAYMENT_STATE_PAY_COMPLETE, posNum, optNum, currentTime, tenancyId, storeId, billNum, payId});

	//				paymentDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, table_code, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, customer_id, rs.getString("bill_code"),
	//						remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batch_num, rs.getString("param_cach"), 0d);
				}
			}

	//		JSONObject resultJson = new JSONObject();
			this.closedPosBill(tenancyId, storeId, billNum, report_date, shiftId, posNum, optNum, isprint_bill, isInvoice,saleMode, billJson, printJson, "0", currentTime);



			JSONObject resultJson  = this.getPosBillPaymentByBillNum(tenancyId, storeId, billNum, tableCode, paymentAmount, discountAmount, difference, billPaymentState);
			List<JSONObject> resultList = new ArrayList<JSONObject>();
			resultList.add(resultJson);

			result.setData(resultList);
			result.setCode(Constant.CODE_SUCCESS);
			result.setMsg(Constant.PAYMENT_SUCCESS);

		}
		catch (SystemException e)
		{
			throw e;
		}
		catch (Exception e)
		{
			logger.info("强制付款完成错误",e);
			e.printStackTrace();
			throw SystemException.getInstance(PosErrorCode.OPER_ERROR);
		}
		finally
		{
			try
			{
				paymentDao.savePosLog(tenancyId, storeId, posNum, optNum, optNum, shiftId, reportDate, Constant.TITLE, "付款完成", "账单编号:" + billNum, "", currentTime);
			}
			catch (Exception e)
			{
				logger.info("报错日志报错:", e);
				e.printStackTrace();
			}
		}
	}



	@Override
	@Deprecated
	public void paymentRepeat(Data param, Data result, JSONObject printJson) throws Exception {
		// TODO paymentRepeat
		String tenantId = param.getTenancy_id();
		Integer storeId = param.getStore_id();

		Map<String, Object> map = ReqDataUtil.getDataMap(param);

		String optNum = ParamUtil.getStringValue(map, "opt_num", false, null);// 操作员编号
		Date reportDate = ParamUtil.getDateValue(map, "report_date", false, null);// 报表日期
		String billNum = ParamUtil.getStringValue(map, "bill_num", true, PosErrorCode.NOT_NULL_BILL_NUM);
		String isprint_bill = ParamUtil.getStringValue(map, "isprint_bill", false, null);
		String posNum = ParamUtil.getStringValue(map, "pos_num", false, null);
		int shiftId = ParamUtil.getIntegerValue(map, "shift_id", false, null);
		String isInvoice = ParamUtil.getStringValue(map, "is_invoice", false, null);
		if (Tools.isNullOrEmpty(isprint_bill)) {
			isprint_bill = "N";
		}

		Timestamp currentTime = DateUtil.currentTimestamp();

		// 付款ID列表
		JSONArray items = JSONArray.fromObject(map.get("item"));
		StringBuilder rwids = new StringBuilder();
		if (items.size() > 0) {
			for (Object obj : items) {
				JSONObject itemJson = JSONObject.fromObject(obj);
				if (rwids.length() > 0) {
					rwids.append(",");
				}
				rwids.append(itemJson.optInt("pay_id"));
			}
		}

		if (rwids.length() <= 0) {
			throw new SystemException(PosErrorCode.NOT_NULL_ITEM_LIST);
		}

		paymentDao.checkKssy(reportDate, optNum, storeId,shiftId,posNum);

		// SqlRowSet rs = null;

		// StringBuilder sql = new
		// StringBuilder("select bill_property,payment_state,payment_amount,source from pos_bill where bill_num = ? and store_id = ?");
		// rs = paymentDao.query4SqlRowSet(sql.toString(), new Object[]{
		// billNum, storeId });
		//
		JSONObject billJson = paymentDao.getPosBillByBillnum(tenantId, storeId, billNum);
		double paymentAmount = 0d;
		String orderNum = "";
		String chanel = "";
		String tableCode = null;
		double discountAmount = 0d;
		double difference = 0d;
		String paymentState = null;
		String saleMode="";
		if (null != billJson)
		{
			paymentAmount = billJson.optDouble("payment_amount");
			orderNum = billJson.optString("order_num");
			chanel = billJson.optString("source");
			tableCode = billJson.optString("table_code");
			paymentAmount = billJson.optDouble("payment_amount");
			discountAmount = billJson.optDouble("discount_amount");
			paymentState = billJson.optString("payment_state");
			saleMode = billJson.optString("sale_mode");
		}
		else
		{
			throw new SystemException(PosErrorCode.NOT_EXISTS_BILL);
		}

		if (SysDictionary.BILL_PROPERTY_CLOSED.equals(billJson.optString("bill_property")))
		{
			throw new SystemException(PosErrorCode.BILL_CLOSED);
		}

		StringBuilder sql = new StringBuilder("select bp.*,pw.payment_class,pw.is_check from pos_bill_payment bp left join payment_way pw on bp.jzid=pw.id where store_id = ? and bill_num = ? and bp.id in (" + rwids.toString() + ")");
		List<JSONObject> payList = paymentDao.query4Json(tenantId, sql.toString(), new Object[]
				{storeId, billNum});

		JSONObject resultJson = new JSONObject();

		this.billPaymentDebited(tenantId, storeId, billNum, orderNum, chanel, paymentAmount, payList, currentTime, resultJson, result);

		this.closedPosBill(tenantId, storeId, billNum, DateUtil.format(reportDate), shiftId, posNum, optNum, isprint_bill, isInvoice,saleMode, billJson, printJson, "0", currentTime);

		resultJson.putAll(this.getPosBillPaymentByBillNum(tenantId, storeId, billNum, tableCode, paymentAmount, discountAmount, difference, paymentState));

		if (result.getCode() == Constant.CODE_SUCCESS) {
			result.setCode(Constant.CODE_SUCCESS);
			result.setMsg(Constant.SELF_RETRY_PAYMENT_REPEAT_SUCCESS);
		} else if (Constant.PAYMENT_STATUS_PAYING_CODE == result.getCode()) {
			result.setCode(Constant.CODE_SUCCESS);
		}
	}

	@Override
	@Deprecated
	public Data queryThirdPayment(Data params, JSONObject printJson) throws Exception
	{
		Data returnData = null;
		String tenantId = params.getTenancy_id();
		int storeId = params.getStore_id();
//		String source = params.getSource();

		JSONObject para = JSONObject.fromObject(params.getData().get(0));
		String report_Date = ParamUtil.getDateStringValue(para, "report_date");
		Date reportDate = DateUtil.parseDate(report_Date);
		String orderNo = para.optString("order_no");
//		int shiftId = para.optInt("shift_id");
//		String posNum = para.optString("pos_num");
//		String optNum = para.optString("opt_num");

		if (Tools.isNullOrEmpty(reportDate))
		{
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_REPORT_DATE);
		}
		if (Tools.isNullOrEmpty(orderNo))
		{
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_BILL_NUM);
		}

//		paymentDao.checkReportDate(tenantId, storeId, reportDate);

//		JSONObject payTypeJson = paymentDao.getPaymentWayByID(tenantId, storeId, para.optInt("pay_id"));
//		para.put("pay_type", payTypeJson.opt("payment_class"));

//		JSONObject billJson = paymentDao.getPosBillPaymentByBillnum(tenantId, storeId, orderNo,para.optInt("payment_id"));
//		
//		String orderNum = para.optString("order_no");
//		Timestamp paymenTime = DateUtil.currentTimestamp();
//		if (null != billJson)
//		{
//			paymenTime = DateUtil.parseTimestamp(billJson.optString("last_updatetime"));
//			para.put("total_amount", billJson.optDouble("payment_amount"));
//			
//			if ((!SysDictionary.CHANEL_MD01.equals(billJson.optString("source"))) && Tools.hv(billJson.optString("order_num")))
//			{
//				orderNum = billJson.optString("order_num");
//			}
//			else
//			{
//				if (Tools.hv(billJson.optString("batch_num")))
//				{
//					orderNum = orderNum + "_" + billJson.optString("batch_num");
//				}
//			}
//			orderNo =  orderNum+ "@" + String.valueOf(paymenTime.getTime());
//		}
//		para.put("order_no", orderNo);

		returnData = thirdPaymentService.queryPayment(tenantId, storeId, orderNo);
		/**
		 * * 若请求成功,根据service_type判断,
		 * service_type=充值(hy01),调用总部充值接口(CUSTOMER_CARD_RECHARGE),返回充值结果;
		 * service_type=消费(pos04),调用结账接口(BILL_PAYMENT),返回结账结果;
		 **/
		String serviceType = para.optString("service_type");

		List<?> returnDataList = returnData.getData();
		String paymentState =SysDictionary.PAYMENT_STATE_PAY;
		JSONObject returnJson = new JSONObject();
		if(null !=returnDataList && returnDataList.size()>0)
		{
			returnJson = JSONObject.fromObject(returnDataList.get(0));
			paymentState = returnJson.optString("payment_state");
		}

		if (SysDictionary.SERVICE_TYPE_CONSUME.equals(serviceType))
		{
			if(orderNo.indexOf("_")>0)
			{
				orderNo = orderNo.substring(0, orderNo.indexOf("_"));
			}
//			String billNum = paymentDao.getBillNumByOrderNo(tenantId, storeId, orderNo);
			switch (paymentState)
			{
				case SysDictionary.THIRD_PAY_STATUS_SUCCESS:
////					JSONObject jsonRetdata = (JSONObject) returnData.getData().get(0);
//					String transactionNo = returnJson.optString("transaction_no");// 获取交易号
////					Data payData = this.getPayParam(params, transactionNo, orderNo);// 重新封装消费接口
//					Data payData = this.getPayParam(tenantId, storeId, report_Date, shiftId, posNum, optNum, billNum, para.optInt("pay_id"), transactionNo, para.optString("isprint_bill"));
//					this.thirdPaymentSuccess(payData, returnData, printJson);
					break;
				case SysDictionary.THIRD_PAY_STATUS_FAIL:
//					//付款失败,修改账单付款状态
//					JSONObject item = paymentDao.getBillPaymentByJzid(tenantId, storeId, billNum, para.optInt("pay_id"));
//					JSONObject payment = new JSONObject();
//					payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_FAILURE);
//					payment.put("id", item.optInt("id"));
//					paymentDao.updateIgnorCase(tenantId, "pos_bill_payment", payment);
//					
					break;
				case SysDictionary.THIRD_PAY_STATUS_PAYING:
					//返回待支付


					break;
				case SysDictionary.THIRD_PAY_STATUS_REFUND_SUCCESS:
					//判断是取消付款,或整单取消,或恢复账单

					break;
				case SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL:


					break;
				case SysDictionary.THIRD_PAY_STATUS_REFUND:


					break;
				case SysDictionary.THIRD_PAY_STATUS_REVOKED_SUCCESS:


					break;
				case SysDictionary.THIRD_PAY_STATUS_REVOKED_FAIL:


					break;
				case SysDictionary.THIRD_PAY_STATUS_REVOKED:


					break;
				default:

					break;
			}
		}
		else if (SysDictionary.SERVICE_TYPE_RECHARGE.equals(serviceType))
		{
			switch (paymentState)
			{
				case SysDictionary.THIRD_PAY_STATUS_SUCCESS:
//					Data cardParam = this.getHyparam(tenantId, storeId, report_Date, shiftId, posNum, optNum, orderNo, returnJson.optString("transaction_no"));
//					customerService.rechargePost(cardParam, returnData);
					break;
				case SysDictionary.THIRD_PAY_STATUS_FAIL:
//					Data calParams = new Data();
//					calParams.setTenancy_id(params.getTenancy_id());
//					calParams.setStore_id(params.getStore_id());
//					JSONObject calData = new JSONObject();
//					calData.put("mode", "1");
//					calData.put("report_date", para.optString("report_date"));
//					calData.put("shift_id", para.optString("shift_id"));
//					calData.put("pos_num", para.optString("pos_num"));
//					calData.put("opt_num", para.optString("opt_num"));
//					calData.put("bill_num", orderNo);
//					calData.put("pay_id", para.optString("pay_id"));
//					calData.put("is_refund", "N");
//					List<JSONObject> calDatas = new ArrayList<JSONObject>();
//					calDatas.add(calData);
//					calParams.setData(calDatas);
//					customerService.cancelCardRecharge(calParams, returnData);
					break;
				default:
//					JSONObject queryJson = new JSONObject();
//					queryJson.put("report_date", report_Date);
//					queryJson.put("shift_id", shiftId);
//					queryJson.put("pos_num", posNum);
//					queryJson.put("opt_num", optNum);
//					queryJson.put("bill_num", orderNo);
//					queryJson.put("pay_id", 0);
//					queryJson.put("isprint_bill", "Y");
//					List<JSONObject> queryList = new ArrayList<JSONObject>();
//					queryList.add(queryJson);
//					Data queryData = Data.get(tenantId, storeId, Constant.CODE_SUCCESS);
//					queryData.setData(queryList);
//					customerService.queryCardRecharge(queryData, returnData);
					break;
			}
		}
		return returnData;
	}



	@Override
	@Deprecated
	public Data cancelThirdPayment(Data params) throws Exception
	{
		JSONObject para = JSONObject.fromObject(params.getData().get(0));
		String tenantId = params.getTenancy_id();
		int storeId = params.getStore_id();
		String source = params.getSource();
		String report_Date = ParamUtil.getDateStringValue(para, "report_date");
		Date reportDate = DateUtil.parseDate(report_Date);
		String orderNo = para.optString("order_no");
//		int shiftId = para.optInt("shift_id");
		String posNum = para.optString("pos_num");
		String optNum = para.optString("opt_num");
		Integer shiftId = para.optInt("shift_id");

		if (Tools.isNullOrEmpty(reportDate))
		{
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_REPORT_DATE);
		}
		if (Tools.isNullOrEmpty(orderNo))
		{
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_BILL_NUM);
		}

		if (SysDictionary.SOURCE_MOVE.contains(source)||SysDictionary.SOURCE_ORDER_DEVICE.equals(source))
		{
			paymentDao.checkBindOptnum(tenantId, storeId, reportDate, posNum);
//			try
//			{
//				shiftId = paymentDao.getShiftId(tenantId, storeId, reportDate, optNum, posNum);
//			}
//			catch (Exception e)
//			{
//				e.printStackTrace();
//				throw SystemException.getInstance(PosErrorCode.GET_SHIFT_ID_ERROR);
//			}
		}
		else
		{
			paymentDao.checkKssy(reportDate, optNum, storeId,shiftId,posNum);
		}

		paymentDao.checkReportDate(tenantId, storeId, reportDate);
		return thirdPaymentService.cancelPayment(tenantId, storeId, para);
	}

	@Override
	@Deprecated
	public Data refundThirdPayment(Data params) throws Exception
	{
		JSONObject para = JSONObject.fromObject(params.getData().get(0));
		String tenantId = params.getTenancy_id();
		int storeId = params.getStore_id();
		String source = params.getSource();
		String report_Date = ParamUtil.getDateStringValue(para, "report_date");
		Date reportDate = DateUtil.parseDate(report_Date);
		String orderNo = para.optString("order_no");
//		int shiftId = para.optInt("shift_id");
		String posNum = para.optString("pos_num");
		String optNum = para.optString("opt_num");
		Integer shiftId = para.optInt("shift_id");

		if (Tools.isNullOrEmpty(reportDate))
		{
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_REPORT_DATE);
		}
		if (Tools.isNullOrEmpty(orderNo))
		{
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_BILL_NUM);
		}

		if (SysDictionary.SOURCE_MOVE.contains(source)||SysDictionary.SOURCE_ORDER_DEVICE.equals(source))
		{
			paymentDao.checkBindOptnum(tenantId, storeId, reportDate, posNum);
//			try
//			{
//				shiftId = paymentDao.getShiftId(tenantId, storeId, reportDate, optNum, posNum);
//			}
//			catch (Exception e)
//			{
//				e.printStackTrace();
//				throw SystemException.getInstance(PosErrorCode.GET_SHIFT_ID_ERROR);
//			}
		}
		else
		{
			paymentDao.checkKssy(reportDate, optNum, storeId,shiftId,posNum);
		}

		paymentDao.checkReportDate(tenantId, storeId, reportDate);
		return thirdPaymentService.refundPayment(tenantId, storeId, para);
	}

	@Override
	@Deprecated
	public Data queryRefundPayment(Data params) throws Exception
	{
		JSONObject para = JSONObject.fromObject(params.getData().get(0));
		String tenantId = params.getTenancy_id();
		int storeId = params.getStore_id();
		String source = params.getSource();
		String report_Date = ParamUtil.getDateStringValue(para, "report_date");
		Date reportDate = DateUtil.parseDate(report_Date);
		String orderNo = para.optString("order_no");
//		int shiftId = para.optInt("shift_id");
		String posNum = para.optString("pos_num");
		String optNum = para.optString("opt_num");
		Integer shiftId = para.optInt("shift_id");
		if (Tools.isNullOrEmpty(reportDate))
		{
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_REPORT_DATE);
		}
		if (Tools.isNullOrEmpty(orderNo))
		{
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_BILL_NUM);
		}

		if (SysDictionary.SOURCE_MOVE.contains(source)||SysDictionary.SOURCE_ORDER_DEVICE.equals(source))
		{
			paymentDao.checkBindOptnum(tenantId, storeId, reportDate, posNum);
//			try
//			{
//				shiftId = paymentDao.getShiftId(tenantId, storeId, reportDate, optNum, posNum);
//			}
//			catch (Exception e)
//			{
//				e.printStackTrace();
//				throw SystemException.getInstance(PosErrorCode.GET_SHIFT_ID_ERROR);
//			}
		}
		else
		{
			paymentDao.checkKssy(reportDate, optNum, storeId,shiftId,posNum);
		}

		paymentDao.checkReportDate(tenantId, storeId, reportDate);

		return thirdPaymentService.queryRefundPayment(tenantId, storeId, para);
	}

	@Override
	public void billPaymentService(Data param, Data result, JSONObject printJson) throws Exception
	{
		// TODO 自动生成的方法存根
		List<?> paramList = param.getData();
		if (paramList == null || paramList.isEmpty())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}
		JSONObject para = JSONObject.fromObject(paramList.get(0));
		String billNum = para.optString("bill_num").intern();

		synchronized(billNum)
		{
			switch (param.getType())
			{
				case BILL_PAYMENT_CANCEL:
					this.cancelPayment(param, result);
					break;
				case BILL_PAYMENT:
					this.paymentResultByQuery(param.getTenancy_id(), param.getStore_id(), para, printJson);
					break;
				case QUERY_PAYMENT:
					this.paymentStateQuery(param, result, printJson);
					break;
				default:
					break;
			}
		}

	}

	public void combineTableZeroPayment(String tenancyId, int storeId, String sbill_num, Date reportDate, int shiftId, String posNum, String optNum,Timestamp currentTime) throws Exception{
		if(Tools.hv(sbill_num)){ //并台账单零结账
			String [] bill_nums = sbill_num.split(",");
			if(bill_nums.length>0){
				for(int i = 0;i<bill_nums.length;i++){
					String bill_no = bill_nums[i];
					if(!Tools.hv(bill_no)){
						continue;
					}else{
						JSONObject sbillJson = paymentDao.getPosBillByBillnum(tenancyId, storeId, bill_no);
						String fromTableCode = sbillJson.getString("table_code");
						String orderNum = sbillJson.optString("bill_num");
						// 清空源桌位账单状态
						StringBuffer sql = new StringBuffer("update pos_bill set guest=?,payment_time=?,pos_num=?,cashier_num=?,shift_id=?,service_id=?,service_amount=?,service_discount=?,subtotal=?,bill_amount=?,");
						sql.append("payment_amount=?,difference=?,discountk_amount=?,discountr_amount=?,maling_amount=?,discount_amount=?,free_amount=?,givi_amount=?,more_coupon=?,average_amount=?,");
						sql.append("discount_num=?,discount_case_id=?,discount_rate=?,billfree_reason_id=?,discount_mode_id=?,bill_property=?,payment_state=?,shop_real_amount=?,platform_charge_amount=?,total_fees=? where bill_num=? and store_id = ?");
						posDishDao.update(sql.toString(), new Object[]
								{ 0, currentTime, posNum, optNum, shiftId, null, 0d, 100, 0d, 0d, 0d, 0d, 0d, 0d, 0d, 0d, 0d, 0d, 0d, 0d, null, null, 100d, null, null, SysDictionary.BILL_PROPERTY_CLOSED, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, 0d, 0d, 0d, bill_no, storeId });

						// 零结账
						JSONObject paymentWayJson = posDishDao.getPaymentWayForStandard(tenancyId, storeId);

						paymentDao.insertPosBillPayment(tenancyId, storeId, bill_no,reportDate, shiftId, posNum, optNum, fromTableCode, paymentWayJson.optString("payment_class"), paymentWayJson.optInt("payment_id"), paymentWayJson.optString("payment_name"), paymentWayJson.optString("name_english"),
								paymentWayJson.optDouble("rate"), 0d, 1, 0d, null, null, "N", null, null, "并台", currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, null, null, 0d);

						// 修改原桌位为空闲
						posDishDao.updateTableState(tenancyId, storeId, fromTableCode, SysDictionary.TABLE_STATE_FREE);

						//解锁桌台
						posDao.lockOrUnlockTable(tenancyId, storeId, optNum, posNum, fromTableCode, "1", new Data());


						try
						{
							// 调用微生活的清台,通知微生活清台
							logger.info("开始调用微生活的清台");
							tableStateService.closeTable(tenancyId, storeId, fromTableCode);

							// 调用微生活小程序的清台,通知微生活清台
							wlifeProgramService.completeOrder(tenancyId, storeId, bill_no);
						}
						catch (Exception e)
						{
							e.printStackTrace();
							logger.error("更新订单状态失败:", e);
						}

					}
				}
			}
		}
	}

    /**
     * 获取菜品明细列表
     * @param tenancyId
     * @param storeId
     * @param billNum
     * @return
     * @throws Exception
     */
    private List<JSONObject> getProducts(String tenancyId, int storeId, String billNum) throws Exception {
        List<JSONObject> productsList = new ArrayList<JSONObject>();

        Map<String,Integer> itemNumIndexMap = new HashMap<String, Integer>();
        List<JSONObject> itemList = paymentDao.getPosBillItemForSingleByBillNum(tenancyId, storeId, billNum);
        if (null != itemList && !itemList.isEmpty())
        {
            for (JSONObject itemJson : itemList)
            {
                String itemName = ParamUtil.getStringValueByObject(itemJson, "item_name");
                String itemNum = ParamUtil.getStringValueByObject(itemJson, "item_num");
//				String itemRwid = ParamUtil.getStringValueByObject(itemJson, "rwid");
                Double itemCount = ParamUtil.getDoubleValueByObject(itemJson, "item_count");
                Double realAmount = ParamUtil.getDoubleValueByObject(itemJson, "real_amount");
                Double itemAmount =  ParamUtil.getDoubleValueByObject(itemJson, "item_amount");
                if(realAmount > itemAmount)
                {
                    realAmount = itemAmount;
                }

                Double itemPrice = DoubleHelper.div(realAmount, itemCount, PAYMENT_SCALE);
                if (itemPrice.isNaN())
                {
                    itemPrice = 0d;
                }


                if (itemNumIndexMap.containsKey(itemNum))
                {
                    JSONObject productJson = productsList.get(itemNumIndexMap.get(itemNum));
                    Double itemCountT = DoubleHelper.add(itemCount, productJson.optDouble("num", 0), 4);
                    productJson.put("num", itemCountT);
                    productJson.put("price", itemPrice);
                    productsList.set(itemNumIndexMap.get(itemNum), productJson);
                }
                else
                {
                    itemNumIndexMap.put(itemNum, productsList.size());
                    List<String> couponsIds = new ArrayList<>();
                    JSONObject productJson = new JSONObject();
                    productJson.put("name", itemName);
                    productJson.put("no", itemNum);
                    productJson.put("num", itemCount);
                    productJson.put("price", itemPrice);
                    productJson.put("is_activity", "2");
                    productJson.put("coupons_ids", couponsIds);
                    productsList.add(productJson);
                }
            }
        }

        return productsList;
    }
}
