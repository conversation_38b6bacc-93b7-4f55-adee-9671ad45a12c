package com.tzx.pos.service.servlet.imp;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import com.tzx.framework.common.entity.Data;
import com.tzx.member.crm.bo.CustomerCardActivationService;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.util.JsonUtil;
import com.tzx.pos.service.servlet.CustomerCardActivationServlet;

/**
 * 会员发卡接口实现
 * 
 * <AUTHOR> email:<EMAIL>
 * @version 1.0 2017-10-25
 * @see
 * @since JDK7.0
 * @update
 */
@Service(CustomerCardActivationServlet.NAME)
public class CustomerCardActivationServletImp implements CustomerCardActivationServlet{

	@Resource(name = CustomerCardActivationService.NAME)
	protected CustomerCardActivationService	customerCardActivationService;
	
	private static final Logger	logger	= Logger.getLogger(CustomerCardActivationServletImp.class);
	
	/**
	 * 会员卡发卡
	 * @param tenancyId 商户ID
	 * @param storeId 门店ID
	 * @param param 输入参数
	 * @param resData 响应参数
	 * @throws Exception
	 */
	public Data customerCardActivation(Data params) throws Exception
	{
		JSONObject paramJson = null;
		String tenancyId = params.getTenancy_id();
		int storeId = params.getStore_id();
		Data resData = params.clone();
		
		try
		{
			paramJson = JSONObject.fromObject(params.getData().get(0));
		}
		catch (Exception e)
		{
			e.printStackTrace();
			logger.info("参数转换JSON对象失败");
			resData.setCode(Constant.CODE_PARAM_FAILURE);
			resData.setMsg("参数转换JSON对象失败");
			return resData;
		}

		// 校验签到
		String thirdBillCode = "";
		try
		{// 记录会员操作流水
			thirdBillCode = customerCardActivationService.cardActivationCustomerOperateList(tenancyId, storeId, paramJson);
		}
		catch (Exception e)
		{
			logger.info("记录会员操作流水失败");
			e.printStackTrace();
			throw e;
		}
		
		try
		{
			// 调用会员卡发卡接口
			paramJson.put("third_bill_code", thirdBillCode);
			resData = customerCardActivationService.customerCardActivationPost(tenancyId, storeId, paramJson);
		}
		catch (Exception e)
		{
			logger.info("调用会员卡发卡接口失败");
			e.printStackTrace();
			throw e;
		}
		return resData;
	}
	
	/**
	 * 查询发卡状态
	 * @param tenancyId
	 * @param storeId
	 * @param paramJson
	 * @param resData
	 * @return
	 * @throws Exception
	 */
	public Data customerCardActivationStatus(Data paramJson) throws Exception{
		JSONObject requestData = null;
		String tenancyId =paramJson.getTenancy_id();
		int storeId = paramJson.getStore_id();
		try{
			requestData = JSONObject.fromObject(paramJson.getData().get(0));
		}catch(Exception e){
			e.printStackTrace();
			logger.info("参数转换JSON对象失败");
		}
		Data resData = new Data();
		try{//查询发卡状态
			resData = customerCardActivationService.customerCardActivationStatus(tenancyId, storeId, requestData);
		}catch(Exception e){
			logger.info("查询发卡状态失败");
			e.printStackTrace();
		}
		return resData;
	}
	
	/**
	 * 会员发卡获取二维码
	 * 
	 * @param params
	 * @param path
	 * @return
	 * @throws Exception
	 */
	public Data customerCardThirdPaymentPrecreate(Data params, String path) throws Exception{
		String tenancyId = params.getTenancy_id();
		int storeId = params.getStore_id();
		JSONObject paramJson = null;
		try{
			paramJson = JSONObject.fromObject(params.getData().get(0));
		}catch(Exception e){
			e.printStackTrace();
			logger.info("参数转换JSON对象失败");
		}
		String thirdBillCode = "";
		try{//记录会员操作流水
			thirdBillCode = customerCardActivationService.cardActivationCustomerOperateList(tenancyId,storeId, paramJson);
		}catch(Exception e){
			logger.info("记录会员操作流水失败");
			e.printStackTrace();
		}
		
		List<JSONObject> dataList = new ArrayList<JSONObject>();
		paramJson.put("third_bill_code", thirdBillCode);
		dataList.add(paramJson);
		params.setData(dataList);
		
		return customerCardActivationService.customerCardThirdPaymentPrecreate(params, path);
	}
	
	/**
	 * 取消支付
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param paramJson
	 * @param resData
	 * @param operType
	 * @return
	 * @throws Exception 
	 */
	@Override
	public Data customerCardCancelThirdPayment(Data params) throws Exception {
		String tenancyId = params.getTenancy_id();
		int storeId = params.getStore_id();
		JSONObject para = null;
		try{
			para = JSONObject.fromObject(params.getData().get(0));
		}catch(Exception e){
			e.printStackTrace();
			logger.info("参数转换JSON对象失败");
		}
		System.out.println("para "+para);
		
		Data resData = new Data();
		resData = customerCardActivationService.customerCardCancelThirdPayment(tenancyId, storeId, para);
		System.out.println(JsonUtil.DataToJson(resData));
		
		return resData;
	}
	
	/**
	 * 会员卡扫码支付 被扫支付(B扫C)
	 * 
	 * @param params
	 * @param jsobj
	 * @return
	 * @throws Exception
	 */
	@Override
	public Data customerCardThirdPaymentBarcode(Data params) throws Exception{
		String tenancyId = params.getTenancy_id();
		int storeId = params.getStore_id();
		JSONObject paramJson = null;
		try{
			paramJson = JSONObject.fromObject(params.getData().get(0));
		}catch(Exception e){
			e.printStackTrace();
			logger.info("参数转换JSON对象失败");
		}
		String thirdBillCode="";
		try{//记录会员操作流水
			thirdBillCode = customerCardActivationService.cardActivationCustomerOperateList(tenancyId,storeId, paramJson);
		}catch(Exception e){
			logger.info("记录会员操作流水失败");
			e.printStackTrace();
		}
		
		List<JSONObject> dataList = new ArrayList<JSONObject>();
		paramJson.put("third_bill_code", thirdBillCode);
		dataList.add(paramJson);
		params.setData(dataList);
		Data resData = new Data();
		resData =  customerCardActivationService.customerCardThirdPaymentBarcode(params);
		return resData;
	}

}
