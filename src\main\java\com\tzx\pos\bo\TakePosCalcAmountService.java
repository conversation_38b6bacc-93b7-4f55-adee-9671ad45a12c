package com.tzx.pos.bo;

import com.tzx.pos.bo.dto.TakeOrderingParam;

/**
 * 账单金额计算
 * 
 * <AUTHOR>
 *
 */
public interface TakePosCalcAmountService
{
    String	NAME	= "com.tzx.pos.bo.TakePosCalcAmountServiceImp";

	/** 金额计算
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @throws Exception
	 */
	public void calcAmount(String tenantId, Integer storeId, String billNum,TakeOrderingParam param) throws Exception;

    void calcAmount(String tenantId, Integer storeId, String billNum, TakeOrderingParam param,Integer dishScale, Integer billScale) throws Exception;

    
}
