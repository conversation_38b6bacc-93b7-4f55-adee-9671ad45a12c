

CREATE OR REPLACE FUNCTION p_addfield(stabname character varying, sfieldname character varying, sfieldtype character varying) RETURNS integer AS
$BODY$
declare
  iMaxID int;
  ssql varchar(1000);  
begin
  
  PERFORM attrelid from pg_attribute where attrelid in (select oid from pg_class where relname=sTabname) and attnum>0 and attname=sfieldname;
  if not found then
    execute 'alter table ' ||sTabname||' add '||sFieldName||' '||sFieldType;   
  end if;
  return 0;
end
$BODY$
  LANGUAGE plpgsql VOLATILE  COST 100;
--
ALTER FUNCTION p_addfield(character varying, character varying, character varying) OWNER TO postgres;
--


CREATE OR REPLACE FUNCTION p_dropfield(stabname character varying, sfieldname character varying) RETURNS integer AS
$BODY$
declare
  iMaxID int;
  ssql varchar(1000);  
begin
  
  PERFORM attrelid from pg_attribute where attrelid in (select oid from pg_class where relname=sTabname) and attnum>0 and attname=sfieldname;
  if found then
    execute 'alter table ' ||sTabname||' drop column '||sFieldName;   
  end if;
  return 0;
end
$BODY$
  LANGUAGE plpgsql VOLATILE  COST 100;
--
ALTER FUNCTION p_dropfield(character varying, character varying) OWNER TO postgres;
--


CREATE OR REPLACE FUNCTION p_renamefield(stabname character varying, sfieldname character varying, tfieldname character varying) RETURNS integer AS
$BODY$
declare
  iMaxID int;
  ssql varchar(1000);  
begin
  
  PERFORM attrelid from pg_attribute where attrelid in (select oid from pg_class where relname=sTabname) and attnum>0 and attname=sfieldname;
  if found then
    execute 'alter table ' ||sTabname||' rename column '||sFieldName||' to '||tfieldname;   
  end if;
  return 0;
end
$BODY$
  LANGUAGE plpgsql VOLATILE  COST 100;
--
ALTER FUNCTION p_renamefield(character varying, character varying, character varying) OWNER TO postgres;
--

select p_addfield('pos_zfkw_item','item_num','integer');
select p_addfield('pos_bill_item','group_id','integer');
select p_addfield('hq_unusual_reason','third_code','character varying(100)');
select p_addfield('payment_way','third_code','character varying(100)');
select p_addfield('duty_order','third_code','character varying(100)');
select p_addfield('hq_item_class','third_code','character varying');

select p_addfield('item_taste','third_code','character varying(100)');


create table if not exists pos_print (
   tenancy_id           character varying(32) null,
   store_id             int                  null,
   id                   SERIAL not null,
   print_type           character varying(30) null,
   print_format         character varying(50) null,
   print_property       character varying(50) null,
   printer_id           int                  null,
   printer_name         character varying(50) null,
   bill_num             character varying(50) null,
   table_name           character varying(50) null,
   table_code           character varying(30) null,
   guest                int                  null,
   table_property_id    int                  null,
   table_property_tag   character varying(50) null,
   waiter_num           character varying(20) null,
   shift_name           character varying(20) null,
   print_tag            character varying(20) null,
   remark               text                 null,
   print_time           timestamp            null,
   finish_time          timestamp            null,
   constraint PK_POS_PRINT primary key (id)
);


create table if not exists pos_print_list (
   tenancy_id           character varying(32) null,
   store_id             int                  null,
   id                   serial               not null,
   bill_num             character varying(20) null,
   item_num             character varying(30) null,
   item_name            character varying(100) null,
   item_english         character varying(100) null,
   item_unit_name       character varying(50) null,
   item_price           DECIMAL(19,4)        null,
   item_count           DECIMAL(19,4)        null,
   update_time          timestamp without time zone null,
   zfkwnr               character varying(100) null,
   item_property        character varying(20) null,
   item_remark          character varying(100) null,
   item_serial          int                  null,
   print_code           character varying(20) null,
   class_id             int                  null,
   parent_id            int                  null,
   table_name           character varying(50) null,
   table_code           character varying(30) null,
   waitcall_tag         character varying(30) null,
   constraint PK_POS_PRINT_LIST primary key (id)
);



create table if not exists hq_organ_franchisees (
   tenancy_id           character varying(32) null,
   store_id             int                  null,
   id                   serial               not null,
   limit_money          decimal(19,4)        null,
   account_state        character varying(32) null,
   current_money        decimal(19,4)        null,
   remark               character varying(100) null,
   last_operator        character varying(30) null,
   last_updatetime      timestamp            null,
   constraint PK_HQ_ORGAN_FRANCHISEES primary key (id)
);

create table if not exists hq_organ_franchisees_record (
   tenancy_id           character varying(32) not null,
   store_id             int                  null,
   id                   serial               not null,
   operate_type         character varying(32) null,
   operate_explain      character varying(200) null,
   last_operator        character varying(30) null,
   last_updatetime      timestamp            null,
   constraint PK_HQ_ORGAN_FRANCHISEES_RECORD primary key (id)
);



select p_addfield('hq_devices','audit_operator','varchar(30)');
select p_addfield('hq_devices','audit_state','varchar(10)');
select p_addfield('hq_devices','audit_updatetime','timestamp(6)');



create table if not exists wx_template_relation(
  id serial not null,
  template_id_short varchar(100),
  template_id varchar(200),
  type int,
  tenancy_id varchar(32),
  remark varchar(100),
  url varchar(150)
);


select p_addfield('crm_customer_address','longitude','varchar(100)');
select p_addfield('crm_customer_address','latitude','varchar(100)');


select p_addfield('wx_comment','status','varchar(10)');

create table if not exists pos_opter_paidrefund (
   tenancy_id           character varying(32) null,
   store_id             int                  null,
   id                   SERIAL not null,
   changshift_id        int                  null,
   start_time           timestamp            null,
   end_time             timestamp            null,
   shift_id             int                  null,
   report_date          date                 null,
   pos_num              character varying(30) null,
   back_num             decimal(19,4)        null,
   back_money           decimal(19,4)        null,
   paid_money           decimal(19,4)        null,
   refund_money         decimal(19,4)        null,
   cashier_num          character varying(30) null,
   cashier_name         character varying(30) null,
   oprater_time         timestamp            null,
   upload_tag           int                  null,
   remark               character varying(100) null,
   constraint PK_POS_OPTER_PAIDREFUND primary key (id)
);

create table if not exists crm_item_vip (
   tenancy_id           character varying(32) null,
   id                   serial               not null,
   big_class_id         int                  null,
   big_class_code       character varying(30) null,
   big_class_name       character varying(100) null,
   small_class_id       int                  null,
   small_class_code     character varying(30) null,
   small_class_name     character varying(100) null,
   item_id              int                  null,
   item_code            character varying(30) null,
   item_name            character varying(100) null,
   unit_id              int                  null,
   unit_code            character varying(30) null,
   unit_name            character varying(100) null,
   standard_price       decimal(19,4)        null,
   vip_price            decimal(19,4)        null,
   remark               character varying(200) null,
   constraint PK_CRM_ITEM_VIP primary key (id)
);


select p_addfield('pos_opter_changshift','begain_shift','timestamp without time zone');
select p_addfield('pos_opter_changshift','end_shift','timestamp without time zone');


CREATE TABLE if not exists crm_info_business (tenancy_id CHARACTER VARYING(32), id SERIAL NOT NULL, code CHARACTER VARYING(100), name CHARACTER VARYING(100), father_id INTEGER, remark CHARACTER VARYING(200), PRIMARY KEY (id));



create table if not exists crm_info_business (
   tenancy_id           character varying(32) null,
   id                   int                  not null,
   code                 character varying(100) null,
   name                 character varying(100) null,
   father_id            int                  null,
   remark               character varying(200) null,
   constraint PK_CRM_INFO_BUSINESS primary key (id)
);


create table if not exists crm_info_trading (
   tenancy_id           character varying(32) null,
   id                   serial               not null,
   bussiness_id         int                  null,
   info_content         text                 null,
   valid_state          character varying(10) null,
   remark               character varying(100) null,
   last_operator        character varying(30) null,
   last_updatetime      timestamp            null,
   constraint PK_CRM_INFO_TRADING primary key (id)
);


create table if not exists crm_info_send (
   tenancy_id           character varying(32) null,
   id                   serial               not null,
   mobil                character varying(32) null,
   trading_type         character varying(32) null,
   info_content         text                 null,
   send_state           character varying(32) null,
   send_time            timestamp            null,
   store_id             int                  null,
   constraint PK_CRM_INFO_SEND primary key (id)
);


create table if not exists crm_info_templet (
   tenancy_id           character varying(32) null,
   id                   serial               not null,
   content              text                 null,
   constraint PK_CRM_INFO_TEMPLET primary key (id)
);


create table if not exists crm_info_variable (
   tenancy_id           character varying(32) null,
   id                   serial               not null,
   type                 character varying(32) null,
   name                 character varying(32) null,
   source               text                 null,
   remark               character varying(100) null,
   constraint PK_CRM_INFO_VARIABLE primary key (id)
);




create table if not exists crm_coupons_class (
   tenancy_id           character varying(32) null,
   id                   serial               not null,
   class_code           character varying(100) null,
   class_name           character varying(100) null,
   coupons_pro          character varying(100) null,
   use_cycle            character varying(100) null,
   begintime            time                 null,
   endtime              time                 null,
   remark               character varying(100) null,
   valid_state          character varying(10) null,
   last_updatetime      timestamp            null,
   last_operator        character varying(30) null,
   constraint PK_CRM_COUPONS_CLASS primary key (id)
);



select p_addfield('crm_coupons_type','class_id','integer');

select p_addfield('crm_activity','back_image','text');

create table if not exists crm_activity_snapup (
   tenancy_id           character varying(32) null,
   id                   serial               not null,
   activity_id          int                  null,
   goods_id             int                  null,
   limit_amount         int                  null,
   activity_price       decimal(19,4)        null,
   start_coupon         date                 null,
   end_coupon           date                 null,
   remark               character varying(200) null,
   constraint PK_CRM_ACTIVITY_SNAPUP primary key (id)
);


create table if not exists crm_activity_snapup_org (
   tenancy_id           character varying(32) null,
   id                   serial               not null,
   snapup_id            int                  null,
   store_id             int                  null,
   constraint PK_CRM_ACTIVITY_SNAPUP_ORG primary key (id)
);



create table if not exists crm_activity_snapup_list (
   tenancy_id           character varying(32) null,
   id                   serial               not null,
   activity_id          int                  null,
   customer_id          int                  null,
   wx_id                character varying(100) null,
   snapup_time          timestamp            null,
   pay_time             timestamp            null,
   goods_bag_id         int                  null,
   pay_state            character varying(100) null,
   remark               character varying(100) null,
   constraint PK_CRM_ACTIVITY_SNAPUP_LIST primary key (id)
);




create table if not exists crm_goods_bag (
   tenancy_id           character varying(32) null,
   id                   serial               not null,
   code                 character varying(30) null,
   name                 character varying(100) null,
   explain              text                 null,
   sale_price           decimal(19,4)        null,
   photo_url            text                 null,
   remark               character varying(200) null,
   constraint PK_CRM_GOODS_BAG primary key (id)
);



create table if not exists crm_goods_bag_details (
   tenancy_id           character varying(32) null,
   id                   serial               not null,
   goods_bag_id         int                  null,
   goods_type           character varying(100) null,
   goods_type_id        int                  null,
   price                decimal(19,4)        null,
   amount               int                  null,
   remark               character varying(200) null,
   constraint PK_CRM_GOODS_BAG_DETAILS primary key (id)
);



select p_addfield('pos_bill_item','third_price','numeric(19,4)');
select p_addfield('pos_bill_item2','third_price','numeric(19,4)');

select p_addfield('pos_payment_order','out_trade_no','varchar(100)');




create table if not exists hq_lineup_time_org (
   tenancy_id           character varying(30) not null,
   ID                   SERIAL               not null,
   store_id             INT4                 null,
   lineup_type          character varying(30) null,
   lineup_name          character varying(30) null,
   lineup_starttime     character varying(30) null,
   lineup_endtime       character varying(30) null,
   valid_state          character varying(10) null,
   remarks              character varying(50) null,
   last_operator        character varying(30) not null,
   last_updatetime      timestamp            not null,
   constraint PK_HQ_LINEUP_TIME_ORG primary key (ID)
);



create table if not exists cc_meals_info (
   tenancy_id           character varying(32) null,
   id                   serial               not null,
   store_id             int                  null,
   service_id           int4                 null,
   service_name         character varying(100) null,
   money                DECIMAL(19,2)        null,
   valuation_method     character varying(30) null,
   remarks              character varying(100) null,
   valid_state          character varying(10) null,
   last_operator        character varying(30) null,
   last_updatetime      timestamp            null,
   meals_type           character varying(10) null,
   channel              character varying(10) null,
   constraint PK_CC_MEALS_INFO primary key (id)
);


create table if not exists cc_meals_info_default (
   tenancy_id           character varying(32) null,
   id                   INT4                 not null,
   meals_id             int4                 null,
   item_id              int                  null,
   unit_id              int                  null,
   price                decimal(19,4)        null,
   remark               character varying(100) null,
   constraint PK_CC_MEALS_INFO_DEFAULT primary key (id)
);


create table if not exists cc_super_district (
   tenancy_id           character varying(32) null,
   id                   serial               not null,
   key                  character varying(100) null,
   store_id             int                  null,
   phonetic_code        character varying(30) null,
   five_code            character varying(30) null,
   address              character varying(100) null,
   remark               character varying(100) null,
   last_operator        character varying(30) null,
   last_updatetime      timestamp            null,
   valid_state          character varying(10) null,
   constraint PK_CC_SUPER_DISTRICT primary key (id)
);











create table if not exists cc_order_item_taste (
   tenancy_id           character varying(32) null,
   id                   serial               not null,
   order_code           character varying(50) null,
   type                 character varying(10) null,
   taste_method_id      int4                 null,
   item_id              int4                 null,
   proportion_money     DECIMAL(19,4)        null,
   group_index          INT4                 null,
   item_remark          character varying(100) null
);



create table if not exists cc_order_item_details (
   tenancy_id           character varying(32) null,
   id                   serial               not null,
   group_index          int4                 null,
   order_code           character varying(50) null,
   item_id              int                  null,
   unit_id              int                  null,
   number               decimal(19,4)        null,
   price                decimal(19,4)        null,
   remark               character varying(100) null,
   constraint PK_CC_ORDER_ITEM_DETAILS primary key (id)
);




create table if not exists cc_order_repayment (
   tenancy_id           character varying(32) null,
   id                   serial               not null,
   payment_id           int4                 null,
   order_code           character varying(50) null,
   pay_money            decimal(19,4)        null,
   pay_no               character varying(50) null,
   rexchange_rate       decimal(19,4)        null,
   local_currency       decimal(19,4)        null,
   third_bill_code      character varying(50) null,
   constraint PK_CC_ORDER_REPAYMENT primary key (id)
);


create table if not exists cc_order_reason_detail (
   tenancy_id           character varying(32) null,
   id                   serial               not null,
   order_code           character varying(50) null,
   type                 character varying(10) null,
   reason_type          int4                 null,
   complaint_content    character varying(100) null,
   remark               character varying(100) null,
   complaints_time      timestamp            null,
   order_state          character varying(10) null,
   entry_name           character varying(30) null,
   constraint PK_CC_ORDER_REASON_DETAIL primary key (id)
);



select p_addfield('hq_organ','cc_bill_state','character varying(10)');

select p_addfield('hq_organ','room_times','INTEGER');

select p_addfield('crm_customer_address','sex','character varying(100)');


select p_addfield('crm_customer_address','is_outdistrict','integer');

select p_addfield('crm_customer_address','baidu_location','character varying(100)');


select p_addfield('cc_order_list','is_online_payment','character varying(10)');

select p_addfield('cc_order_item','item_menu_id','integer');

select p_addfield('cc_order_item','item_name','character varying(100)');


select p_addfield('hq_daycount','tables_num','integer');
select p_addfield('hq_daycount','seat_num','integer');

select p_addfield('hq_daycount_item','table_property_id','integer');
select p_addfield('hq_daycount_item','business_area_id','integer');
select p_addfield('hq_daycount_item','guest_num','integer');
select p_addfield('hq_daycount_item','tables_num','integer');



select p_addfield('cc_order_list','upload_tag','character varying(10)');

select p_addfield('cc_order_list','maling_amount','DECIMAL(19,4)');

select p_addfield('cc_order_list','is_online_payment','character varying(10)');

select p_addfield('cc_order_item','item_menu_id','integer');

select p_addfield('cc_order_item','item_name','character varying(100)');



select p_renamefield('wx_score','"openId"','openid');


select p_addfield('hq_daycount_item_times','tables_num','int4');
select p_addfield('hq_daycount_item_times','guest_num','int4');



select p_addfield('pos_print_list','rwid','integer');

select p_addfield('payment_way','is_check','character varying(30)  default ''1''');
select p_addfield('payment_way','is_recharge','character varying(30) default ''1''');


select p_addfield('pos_bill_item','returngive_reason_id','integer');
select p_addfield('pos_bill_item','manager_num','character varying(30)');
select p_addfield('pos_bill_item','return_type','character varying(30)');
select p_addfield('pos_bill_item','return_count','integer');

select p_addfield('pos_bill_item','setmeal_group_id','integer');
select p_addfield('pos_bill_item','third_price','numeric(19,4)');


select p_addfield('pos_bill_item2','returngive_reason_id','integer');
select p_addfield('pos_bill_item2','manager_num','character varying(30)');
select p_addfield('pos_bill_item2','return_type','character varying(30)');
select p_addfield('pos_bill_item2','return_count','integer');

select p_addfield('pos_bill_item2','setmeal_group_id','integer');
select p_addfield('pos_bill_item2','third_price','numeric(19,4)');


select p_addfield('pos_bill_item_regain','returngive_reason_id','integer');
select p_addfield('pos_bill_item_regain','manager_num','character varying(30)');
select p_addfield('pos_bill_item_regain','return_type','character varying(30)');
select p_addfield('pos_bill_item_regain','return_count','integer');

select p_addfield('pos_bill_item_regain','setmeal_group_id','integer');
select p_addfield('pos_bill_item_regain','third_price','numeric(19,4)');


select p_addfield('pos_bill','return_amount','numeric(19,4)');
select p_addfield('pos_bill2','return_amount','numeric(19,4)');
select p_addfield('pos_bill_regain','return_amount','numeric(19,4)');


select p_addfield('cc_order_list','send_immediately','int');

select p_addfield('cc_order_list','third_order_code','character varying(50)');

select p_addfield('cc_order_list','need_invoice','character varying(50)');

select p_addfield('cc_order_list','invoice_title','character varying(100)');

select p_addfield('cc_order_list','delivery_party','int');

select p_addfield('cc_order_list','longitude','character varying(100)');

select p_addfield('cc_order_list','latitude','character varying(100)');

select p_addfield('cc_order_list','shop_fee','DECIMAL(19,4)');


select p_addfield('cc_order_item','product_fee','DECIMAL(19,4)');

create table if not exists cc_order_discount (
   tenancy_id           character varying(32) null,
   id                   serial             not null,
   order_code           character varying(50) null,
   discount_type        character varying(10) null,
   activity_id          character varying(50) null,
   discount_desc        character varying(100) null,
   discount_fee         decimal(19,4)        null,
   baidu_rate           character varying(20) null,
   shop_rate            character varying(20) null,
   agent_rate           character varying(20) null,
   logistics_rate       character varying(20) null
);



alter table payment_way alter column rate SET DEFAULT 1;


CREATE TABLE if not exists b_dcjryb (
  DCJH char (8) NULL ,
  RYBH varchar (8) NULL ,
  RYXM varchar (8) NULL ,
  BYZD varchar (20) NULL,
  report_date varchar (20) NULL,
  shift varchar (8) NULL,
  pos_num VARCHAR(20)
);

select p_addfield('b_dcjryb','employeeid','VARCHAR(20)');


CREATE TABLE if not exists b_checkrepeat (
  DCJH char (8) NOT NULL ,
  DCNR varchar (20) NULL ,
  BYZD varchar (20) NULL ,
  CZSJ TIMESTAMP NULL
) ;


CREATE TABLE if not exists b_log (
  NR varchar (100) NULL ,
  DCNR varchar (100) NULL ,
  DCJH char (8) NULL ,
  PCID varchar (20) NULL ,
  TIMER varchar (20) NULL 
);



CREATE TABLE if not exists b_dddclog (
  ID SERIAL NOT NULL ,
  CHECKNR varchar (20) NULL ,
  DCJH char (8) NULL ,
  CZSJ timestamp(6) NULL 
);


CREATE TABLE if not exists b_dclog (
"DCNR" varchar(255) COLLATE "default",
"DCSJ" timestamp(6),
"ID"  SERIAL primary key 
);


CREATE TABLE if not exists b_checkstr (
  DCJH varchar (20) NULL ,
  DCNR varchar (20) NULL ,
  BYZD varchar (20) NULL ,
  CZSJ TIMESTAMP NULL ,
  DCCG char (4) NULL DEFAULT ('N')
);




CREATE TABLE if not exists dynamic_code (
id serial NOT NULL,
card_code varchar(100) COLLATE "default",
create_time timestamp(6),
CONSTRAINT dynamic_code_pkey PRIMARY KEY (id)
)
WITH (OIDS=FALSE)
;


select p_addfield('sys_print_format','print_num','integer');
ALTER TABLE sys_print_format ALTER COLUMN print_num SET DEFAULT 1;

select p_addfield('hq_kvs_printer','print_num','integer');
ALTER TABLE hq_kvs_printer ALTER COLUMN print_num SET DEFAULT 1;


create table if not exists crm_activity_wxcupous (
   tenancy_id           character varying(32) null,
   id                   serial               not null,
   activity_id          int                  null,
   sale_total           int                  null,
   sale_money           decimal(19,4)        null,
   is_online            int                  null,
   remark               character varying(300) null,
   constraint PK_CRM_ACTIVITY_WXCUPOUS primary key (id)
);




create table if not exists crm_activity_wxonline_list (
   tenancy_id           character varying(32) null,
   id                   serial               not null,
   activity_id          int                  null,
   customer_id          int                  null,
   wx_id                character varying(100) null,
   coupons_type         int                  null,
   coupons_code         character varying(100) null,
   price                numeric(19,4)                  null,
   pay_state            character varying(100) null,
   pay_type             character varying(20) null,
   pay_time             timestamp            null,
   remark               character varying(100) null,
   constraint PK_CRM_ACTIVITY_WXONLINE_LIST primary key (id)
);





select p_addfield('pos_bill_item','method_money','numeric(19,4)');
select p_addfield('pos_bill_item2','method_money','numeric(19,4)');

select p_addfield('pos_bill_item_regain','method_money','numeric(19,4)');

select p_addfield('pos_bill','advance_payment_amt','DECIMAL(19,4)');


select p_addfield('pos_bill','advance_refund_amt','DECIMAL(19,4)');


select p_addfield('pos_bill','is_refund','character varying(10)');

select p_addfield('pos_bill','payment_id','int4');

select p_addfield('pos_bill','pay_no','character varying(50)');

select p_addfield('pos_bill','third_bill_code','character varying(50)');


select p_addfield('pos_bill2','advance_payment_amt','DECIMAL(19,4)');

select p_addfield('pos_bill2','advance_refund_amt','DECIMAL(19,4)');

select p_addfield('pos_bill2','is_refund','character varying(10)');

select p_addfield('pos_bill2','payment_id','int4');

select p_addfield('pos_bill2','pay_no','character varying(50)');

select p_addfield('pos_bill2','third_bill_code','character varying(50)');

select p_addfield('pos_bill_regain','advance_payment_amt','DECIMAL(19,4)');

select p_addfield('pos_bill_regain','advance_refund_amt','DECIMAL(19,4)');

select p_addfield('pos_bill_regain','is_refund','character varying(10)');

select p_addfield('pos_bill_regain','payment_id','int4');

select p_addfield('pos_bill_regain','pay_no','character varying(50)');

select p_addfield('pos_bill_regain','third_bill_code','character varying(50)');


create table if not exists boh_attendance_list (
   tenancy_id           character varying(32) null,
   store_id             int                  null,
   ID                   int                  not null,
   employee_id          int                  null,
   id_card              character varying(30) null,
   employee_code        character varying(30) null,
   paper_no             character varying(30) null,
   report_date          date                 null,
   start_time           timestamp            null,
   end_time             timestamp            null,
   working_hours        decimal(19,4)        null,
   duty_id              int                  null,
   upload_time          timestamp            null,
   upload_state         character varying(30) null,
   last_operator        character varying(30) null,
   last_updatetime      timestamp            null,
   constraint PK_BOH_ATTENDANCE_LIST primary key (ID)
);




create table if not exists crm_clearzero_list (
   tenancy_id           character varying(32) null,
   id                   serial               not null,
   customer_id          int                  null,
   clearzero_type       character varying(30) null,
   clearzero_credit     int                  null,
   credit_enddate       date                 null,
   remark               character varying(300) null,
   running_state        character varying(30) null,
   last_operator        character varying(30) null,
   last_updatetime      timestamp            null,
   constraint PK_CRM_CLEARZERO_LIST primary key (id)
);


select p_addfield('hq_unusual_reason','is_sys','character varying(10)');

alter table hq_unusual_reason  alter column is_sys  set default '0';


select p_addfield('crm_activity','budget_money','numeric(19,4)');

select p_addfield('crm_activity','deduction_type','character varying(20)');


CREATE TABLE if not exists crm_info_keyword
(
  
tenancy_id character varying(32), 
id serial NOT NULL,
  
remark text,
  
CONSTRAINT pk_crm_info_keyword PRIMARY KEY (id)
);






select p_addfield('crm_activity','budget_money_balance','numeric(19,4)');


select p_addfield('pos_bill_item2','group_id','integer');
select p_addfield('pos_bill_item_regain','group_id','integer');


select p_addfield('wx_doc','point_num','int default 0');


select p_addfield('organ','business_hours','varchar(100)');

select p_addfield('crm_level','last_zero_date','date');

select p_addfield('crm_clearzero_list','credit_stardate','date');


select p_addfield('crm_customer_info','online_password','character varying(32)');

select p_addfield('crm_artificial_list','store_id','int4');
select p_addfield('crm_artificial_list','report_date','date');


select p_dropfield('organ','business_hours');
select p_addfield('hq_organ','business_hours','varchar(100)');

select p_addfield('pos_tablestate','bill_batch_num','character varying(20)');
select p_addfield('pos_bill_regain','free_amount','numeric(19,4)');

create table if not exists crm_customer_mobil (
   tenancy_id           character varying(32) null,
   id                   serial               not null,
   customer_id          int                  null,
   old_mobil            character varying(30) null,
   new_mobil            character varying(30) null,
   remark               character varying(100) null,
   last_operator        character varying(30) null,
   last_updatetime      timestamp            null
);


select p_addfield('crm_customer_card','third_code','character varying(50)');

select p_addfield('crm_card_init','third_code','character varying(50)');

select p_addfield('pos_bill','payment_state','character varying(20)');
select p_addfield('pos_bill2','payment_state','character varying(20)');

select p_addfield('pos_bill_payment','payment_state','character varying(20)');
select p_addfield('pos_bill_payment2','payment_state','character varying(20)');

select p_addfield('pos_bill','payment_manager_num','character varying(30)');
select p_addfield('pos_bill2','payment_manager_num','character varying(30)');


select p_addfield('crm_coupons_type','is_main','character varying(10)');

select p_addfield('crm_coupons_type','used_main','character varying(10)');
select p_addfield('crm_coupons_type','father_id','integer');

select p_addfield('crm_coupons_type','use_cycle','character varying(100)');


create table if not exists crm_coupons_type_times (
   tenancy_id           character varying(32) null,
   id                   serial                 not null,
   begintime            time                 null,
   endtime              time                 null,
   coupons_type         int                  null,
   constraint PK_CRM_COUPONS_TYPE_TIMES primary key (id)
);


select p_addfield('crm_coupons_class','is_total','character varying(10)');

select p_addfield('crm_coupons_class','back_image1','text');

select p_addfield('crm_coupons_class','back_image2','text');

ALTER TABLE employee ALTER COLUMN paper_no DROP NOT NULL;

select p_addfield('crm_card_trading_list','revoked_trading','numeric(19,4)');

CREATE TABLE if not exists wx_authorizer_account
    (
        id serial NOT NULL,
        authorizer_appid CHARACTER VARYING(100) NOT NULL,
        authorizer_access_token CHARACTER VARYING(150) NOT NULL,
        token_createtime TIMESTAMP(6) WITHOUT TIME ZONE,
        authorizer_refresh_token CHARACTER VARYING(150) NOT NULL,
        func_info CHARACTER VARYING(100),
        nick_name CHARACTER VARYING(40),
        head_img CHARACTER VARYING(300),
        service_type_info CHARACTER VARYING(10),
        verify_type_info CHARACTER VARYING(10),
        user_name CHARACTER VARYING(40),
        user_alias CHARACTER VARYING(40),
        open_store CHARACTER VARYING(2),
        open_scan CHARACTER VARYING(2),
        open_pay CHARACTER VARYING(2),
        open_card CHARACTER VARYING(2),
        open_shake CHARACTER VARYING(2),
        qrcode_url CHARACTER VARYING(300),
        is_authorize BOOLEAN,
        is_setindustry BOOLEAN,
        send_time TIMESTAMP(6) WITHOUT TIME ZONE,
        serial_number CHARACTER VARYING(255),
        sendids CHARACTER VARYING(255),
        issend INTEGER,
        front_logo CHARACTER VARYING(255),
        front_slogan CHARACTER VARYING(255),
        wx_title CHARACTER VARYING(255),
        wx_keyword CHARACTER VARYING(255),
        wx_description CHARACTER VARYING(255),
        custom_tel CHARACTER VARYING(255),
        front_url CHARACTER VARYING(255),
        CONSTRAINT pk_wx_authorizer_account PRIMARY KEY (id)
    );


select p_addfield('pos_returngive_item','item_property','character varying(20)');




select p_addfield('hq_daycount_item','discount_money','numeric(19,4)');
select p_addfield('hq_daycount_item','reduction_money','numeric(19,4)');
select p_addfield('hq_daycount','main_trading','numeric(19,4)');
select p_addfield('hq_daycount','total_card','numeric(19,4)');
select p_addfield('hq_daycount_item_times','discount_money','numeric(19,4)');
select p_addfield('hq_daycount_item_times','reduction_money','numeric(19,4)');




select p_addfield('cc_order_list','chanel_serial_number','character varying(30)');
select p_addfield('cc_order_list','serial_number','character varying(30)');











select p_addfield('pos_bill_item','batch_num','character varying(32)');
select p_addfield('pos_bill_payment','batch_num','character varying(20)');

create table if not exists pos_bill_member (
   tenancy_id           character varying(32) null,
   store_id             int                  null,
   id                   SERIAL not null,
   bill_num             character varying(20) null,
   report_date          date                 null,
   type                 character varying(100) null,
   amount               DECIMAL(19,4)        null,
   credit               DECIMAL(19,4)        null,
   card_code            character varying(30) null,
   mobil                character varying(30) null,
   last_updatetime      timestamp without time zone null,
   upload_tag           int                  null default '0',
   remark               character varying(500) null,
   constraint PK_POS_BILL_MEMBER primary key (id)
);



select p_addfield('crm_card_trading_list','batch_num','varchar(32)');
ALTER TABLE wx_defined_menu ALTER COLUMN url TYPE  varchar(700);
ALTER TABLE wx_defined_menu ALTER COLUMN realurl TYPE  varchar(800);
select p_addfield('wx_member','store_id','int4');
select p_addfield('crm_coupons_type','used_other','int4');
select p_addfield('crm_coupons_type','sale_money','numeric(19,4)');
select p_addfield('crm_activity_wxcupous','sold_total','int4');



select p_addfield('crm_activity_wxcupous','package_name','varchar(255)');
select p_addfield('crm_activity_wxcupous','back_image1','varchar(255)');
select p_addfield('crm_activity_wxcupous','back_image2','varchar(255)');
select p_addfield('crm_activation_lost_list','deposit','numeric(19,4)');
select p_addfield('crm_activation_lost_list','sales_price','numeric(19,4)');
select p_addfield('crm_activity_awaken_customer','status','varchar(1)');





create table if not exists pos_opt_state_devices (
   tenancy_id           character varying(32) null,
   store_id             int                  null,
   id                   SERIAL               not null,
   report_date          date                 null,
   pos_num              character varying(32) null,
   opt_num              character varying(32) null,
   devices_id           int                  null,
   devices_num          character varying(32) null,
   is_valid             character varying(8) null,
   is_changshift        character varying(8) null,
   last_updatetime      timestamp            null,
   upload_tag           character varying(8) null,
   constraint PK_POS_OPT_STATE_DEVICES primary key (id)
);

create table if not exists pos_cashier_receive_log (
   tenancy_id           character varying(32) null,
   store_id             int                  null,
   id                   SERIAL               not null,
   report_date          date                 null,
   shift_id             int                  null,
   oper_type            character varying(32) null,
   waiter_id            int                  null,
   waiter_name          character varying(32) null,
   device_num           character varying(32) null,
   amount               decimal(19,4)        null,
   cashier_id           int                  null,
   cashier_name         character varying(32) null,
   pos_num              character varying(32) null,
   last_updatetime      timestamp            null,
   upload_tag           character varying(8) null,
   constraint PK_POS_CASHIER_RECEIVE_LOG primary key (id)
);


select p_addfield('crm_level','is_vipprice','varchar(10)');
select p_addfield('crm_card_trading_list','last_updatetime','timestamp(6)');
select p_addfield('crm_customer_credit_list','last_updatetime','timestamp(6)');


create table if not exists organ_group (
  tenancy_id varchar(32),
  id serial not null,
  code varchar(30),
  name varchar(100),
  remark varchar(100),
  valid_state varchar(10),
  last_operator varchar(30),
  last_updatetime timestamp(6)
);



select p_addfield('hq_organ','group_id','int4');
select p_addfield('wx_member','mac_openid','varchar(50)');
select p_addfield('crm_coupons_type','validity_type','int4');
select p_addfield('crm_coupons_type','validity_days','int4');
select p_addfield('crm_coupons_type','with_discount','int4');


select p_addfield('crm_activity_birthday','level_id','int4');
select p_addfield('crm_activity_xfzq','level_id','int4');
select p_addfield('crm_activity','customer_ids','text');
select p_addfield('crm_activity','send_customer_ids','text');
select p_addfield('crm_level','level_order','int4');
select p_addfield('hq_item_menu_class','menu_class_rank','character varying(10)');
select p_addfield('hq_item_menu_details','menu_item_rank','character varying(10)');
select p_addfield('crm_activity','is_point','character varying(3)');
select p_addfield('crm_activity','customer_levels','character varying(500)');
select p_addfield('crm_activity','customer_groups','character varying(500)');
select p_addfield('crm_activity','customer_ids','text');
select p_addfield('cc_order_list','order_md5','varchar(200)');
select p_addfield('cc_order_item','tag','varchar(30)');
select p_addfield('cc_order_repayment','remark','varchar(100)');


select p_addfield('pos_bill2','payment_state','character varying(20)');
select p_addfield('pos_bill2','payment_manager_num','character varying(30)');
select p_addfield('pos_bill_regain','payment_state','character varying(20)');
select p_addfield('pos_bill_regain','payment_manager_num','character varying(30)');
select p_addfield('pos_bill_item2','batch_num','character varying(32)');
select p_addfield('pos_bill_item_regain','rwid','integer');
select p_addfield('pos_bill_item_regain','batch_num','character varying(20)');
select p_addfield('pos_bill_payment2','payment_state','character varying(20)');
select p_addfield('pos_bill_payment2','batch_num','character varying(20)');
select p_addfield('pos_bill_payment_regain','customer_id','integer');
select p_addfield('pos_bill_payment_regain','bill_code','character varying(100)');
select p_addfield('pos_bill_payment_regain','payment_state','character varying(20)');
select p_addfield('pos_bill_payment_regain','batch_num','character varying(20)');

select p_addfield('crm_customer_credit_list','change_money','numeric(19,4)');
select p_addfield('crm_customer_credit_list','store_updatetime','TIMESTAMP(6) WITHOUT TIME ZONE');
select p_addfield('pos_opt_state','shift_id','integer');



create table if not exists pos_bill_payment_log
(
  tenancy_id character varying(32),
  store_id integer,
  id serial NOT NULL,
  bill_num character varying(20),
  table_code character varying(50),
  type character varying(100),
  jzid integer,
  name character varying(30),
  name_english character varying(30),
  amount numeric(19,4),
  count integer,
  number character varying(100),
  phone character varying(100),
  report_date date,
  shift_id integer,
  pos_num character varying(10),
  cashier_num character varying(20),
  last_updatetime timestamp without time zone,
  is_ysk character varying(10),
  rate numeric(19,4),
  currency_amount numeric(19,4),
  upload_tag integer DEFAULT 0,
  customer_id integer,
  bill_code character varying(100),
  remark character varying(100),
  payment_state character varying(20),
  param_cach character varying(2000),
  batch_num character varying(20)
);


create table if not exists crm_activity_payvip (
   tenancy_id           character varying(32) null,
   id                   serial               not null,
   activity_id          int                  null,
   level                int                  null,
   pay_money            decimal(19,4)        null,
   face_value           decimal(19,4)        null,
   reward_coupon        int                  null
);

create table if not exists crm_activity_payvip_list (
   tenancy_id           character varying(32) null,
   store_id       integer,
   id                   serial               not null,
   order_num            character varying(100) null,
   third_code           character varying(100) null,
   chanel               character varying(30) null,
   activity_id          int                  null,
   customer_id          int                  null,
   customer_name        character varying(100) null,
   mobil                character varying(100) null,
   old_level            int                  null,
   new_level            int                  null,
   request_time         timestamp            null,
   business_date        date                 null,
   pay_id         integer,
   pay_money            decimal(19,4)        null,
   pay_no              character varying(100) null,
   pay_time             timestamp            null,
   pay_state            character varying(100) null,
   remark               character varying(100) null,
   last_updatetime      timestamp            null,
   last_operator        character varying(30) null,
   request_status character varying(20)
);

select p_addfield('crm_card_trading_list','store_updatetime','timestamp(6)');
select p_addfield('crm_card_payment_list','store_updatetime','timestamp(6)');
select p_addfield('crm_card_payment_list','last_updatetime','timestamp(6)');
select p_addfield('crm_customer_card','card_class_id','int4');
select p_addfield('crm_level','user_credit','numeric(19,4)');

create table if not exists hq_queue_info_org (
   tenancy_id           character varying(30) null,
   id                   SERIAL               not null,
   store_id             int                  null,
   queue_code           character varying(50) null,
   queue_prefix         character varying(30) null,
   queue_name           character varying(50) null,
   waiting_time_each    integer              null,
   capacity_volume_min  integer              null,
   capacity_volume_max  integer              null,
   remark               character varying(50) null,
   valid_state          character varying(2) null,
   entry_person         int                  null,
   entry_time           timestamp            null,
   last_operator        int                  null,
   last_updatetime      timestamp            null,
   queue_deskcount      int                  null,
   queue_number         int                  null
);

create table if not exists hq_lineup_time_org (
   tenancy_id           character varying(30) null,
   ID                   SERIAL               not null,
   store_id             INT4                 null,
   lineup_starttime     TIME            null,
   lineup_endtime       TIME            null,
   lineup_name          character varying(30) null,
   remarks              character varying(50) null,
   entry_person         INT4                 null,
   entry_time           timestamp            null
);



select p_addfield('crm_customer_info','uuid','varchar(32)');
select p_addfield('crm_coupons','business_date','date');

select p_addfield('cc_order_item','is_commission','character varying(10)');
select p_addfield('cc_order_item','is_add_dish','varchar(10)');
select p_addfield('cc_order_item_details','is_add_dish','varchar(10)');
select p_addfield('cc_order_item_taste','is_add_dish','varchar(10)');



create table if not exists crm_item_vip (
   tenancy_id           character varying(32) null,
   id                   serial               not null,
   big_class_id         int                  null,
   big_class_code       character varying(30) null,
   big_class_name       character varying(100) null,
   small_class_id       int                  null,
   small_class_code     character varying(30) null,
   small_class_name     character varying(100) null,
   item_id              int                  null,
   item_code            character varying(30) null,
   item_name            character varying(100) null,
   unit_id              int                  null,
   unit_code            character varying(30) null,
   unit_name            character varying(100) null,
   standard_price       decimal(19,4)        null,
   vip_price            decimal(19,4)        null,
   remark               character varying(200) null
);




select p_addfield('crm_card_trading_list','card_class_id','integer');
select p_addfield('crm_card_trading_list','name','character varying(32)');
select p_addfield('crm_card_trading_list','mobil','character varying(16)');

select p_addfield('pos_byjdc_log','face_value','character varying(10)');
select p_addfield('pos_byjdc_log','count','integer');

select p_addfield('hq_item_group','whether_push_over','character varying(10)');


create table if not exists hq_lineup_dinner_org (
   tenancy_id           character varying(30) null,
   ID                   SERIAL               not null,
   queue_id             INT4                 null,
   store_id             INT4                 null,
   wechat               character varying(30) null,
   phone                character varying(11) null,
   queue_name           character varying(50) null,
   lineup_time          TIMESTAMP            null,
   lineup_code          character varying(30) null,
   deinner_number       character varying(10) null,
   operator_code    character varying(10) null,
   state                character varying(2) null,
   advace_payment       DECIMAL(19,2)        null,
   queue_number         int                  null,
   lineup_orderno       character varying(50) null,
   lineup_completetime  timestamp            null,
   CONSTRAINT PK_hq_lineup_dinner_org PRIMARY KEY (id)
);

CREATE TABLE if not exists
    cc_third_organ_info
    (
        id  SERIAL NOT NULL,
        shop_id INTEGER,
        name CHARACTER VARYING(30),
        shop_logo CHARACTER VARYING(100),
        province CHARACTER VARYING(20),
        city CHARACTER VARYING(20),
        county CHARACTER VARYING(20),
        address CHARACTER VARYING(100),
        brand CHARACTER VARYING(30),
        category1 CHARACTER VARYING(30),
        category2 CHARACTER VARYING(30),
        category3 CHARACTER VARYING(30),
        phone CHARACTER VARYING(20),
        service_phone CHARACTER VARYING(20),
        longitude CHARACTER VARYING(20),
        latitude CHARACTER VARYING(20),
        coord_type CHARACTER VARYING(20),
        district_id INTEGER,
        delivery_fee_id INTEGER,
        lineup_time_org_id CHARACTER VARYING(50),
        book_ahead_time CHARACTER VARYING(20),
        invoice_support CHARACTER VARYING(10),
        min_order_price_id INTEGER,
        package_box_price INTEGER,
        threshold_num INTEGER,
        threshold_time CHARACTER VARYING(20),
        shop_code CHARACTER VARYING(10),
        source INTEGER,
        secret CHARACTER VARYING(20),
        channel CHARACTER VARYING(20),
        shop_state CHARACTER VARYING(2),
        baidu_shop_id CHARACTER VARYING(20),
        region_name CHARACTER VARYING(30),
        delivery_fee CHARACTER VARYING(10),
        delivery_time CHARACTER VARYING(10),
        business_time_format CHARACTER VARYING(50),
        min_order_price CHARACTER VARYING(10),
        CONSTRAINT pk_cc_third_organ_info PRIMARY KEY (id)
    );

select p_addfield('cc_order_list','openid','varchar(50)');

select p_addfield('cc_third_organ_info','online_flag','character varying(10)');




alter table hq_daycount_item alter column list_num type numeric(10,4);
alter table hq_daycount_item alter column sales_num type numeric(10,4);
alter table hq_daycount_item alter column back_num type numeric(10,4);
alter table hq_daycount_item alter column free_num type numeric(10,4);
alter table hq_daycount_item alter column actual_num type numeric(10,4);

alter table hq_daycount_item_times alter column list_num type numeric(10,4);
alter table hq_daycount_item_times alter column sales_num type numeric(10,4);
alter table hq_daycount_item_times alter column back_num type numeric(10,4);
alter table hq_daycount_item_times alter column free_num type numeric(10,4);
alter table hq_daycount_item_times alter column actual_num type numeric(10,4);

select p_addfield('crm_card_trading_list','main_balance','numeric(19,4)');

select p_addfield('crm_card_trading_list','reward_balance','numeric(19,4)');

select p_addfield('crm_card_trading_list','total_balance','numeric(19,4)');

select p_addfield('crm_card_trading_list','operator_id','int4');

select p_addfield('crm_card_trading_list','shift_id','int4');

select p_addfield('crm_coupons','operator','varchar(30)');
select p_addfield('crm_coupons','operator_id','int4');
select p_addfield('crm_coupons','shift_id','int4');


create table if not exists boh_img_padpc (

   tenancy_id           character varying(32) null,
   organ_id             INT                  null,
   id                   serial                  not null,
   terminal_type        character varying(100) null,
   img_type             character varying(100) null,
   img_addr             text                 null,
   img_index            int                  null,
   remark               character varying(100) null,
   last_operator        character varying(30) null,
   last_updatetime      timestamp            null,
   constraint PK_BOH_IMG_PADPC primary key (id)

);


create table if not exists crm_activity_signin (

   tenancy_id           character varying(32) null,
   id                   serial               not null,
   activity_id          int                  null,
   signin_days          int                  null,
   reward_credit        int                  null,
   face_value           decimal(19,4)        null,
   reward_coupon        int                  null,
   remark               character varying(200) null,
   constraint PK_CRM_ACTIVITY_SIGNIN primary key (id)

);

select p_addfield('wx_member','sign_in_total','int4 DEFAULT 0');
select p_addfield('wx_member','sign_in_cnum','int4 DEFAULT 0');
select p_addfield('wx_member','last_sign_date','date');
select p_addfield('wx_member','last_sign_time','TIMESTAMP');
select p_addfield('crm_activation_lost_list','shift_id','int4');
select p_addfield('crm_activation_lost_list','operator_id','int4');
select p_addfield('crm_activation_lost_list','business_date','date');
select p_addfield('crm_customer_info','remark2','varchar(100)');


create table if not exists org_mapping
    (
        tenancy_id character varying(32),
        id serial NOT NULL,
        rif_store_id integer,
        rif_store_name character varying(20),
        saas_store_id integer,
        saas_store_name character varying(20),
        last_update_name character varying(20),
        last_update_time timestamp(6) WITHOUT time ZONE,
        remark character varying(500),
        constraint PK_org_mapping primary key (id)
    );

select p_addfield('hq_daycount','backsingle_num','int');
select p_addfield('hq_daycount','backwhole_num','int');
select p_addfield('hq_daycount','backcopy_num','int');

CREATE TABLE if not exists hq_daycount_shift_item
(
  tenancy_id character varying(32),
  id serial NOT NULL,
  store_id integer,
  day_count date,
  shift_id integer,
  sale_model character varying(20),
  combo_id integer,
  combo_unit_id integer,
  combo_price numeric,
  item_id integer,
  item_unit_id integer,
  item_unit_price numeric(19,4),
  item_pro character varying(20),
  list_num numeric,
  list_money numeric,
  sales_num numeric,
  sales_money numeric,
  back_num bigint,
  back_money numeric,
  free_num bigint,
  free_money numeric,
  favor_money numeric,
  discount_money numeric,
  reduction_money numeric,
  change_money numeric,
  actual_num numeric,
  actual_money numeric,
  net_money numeric,
  sale_billnum bigint,
  sale_billaverage numeric(19,4),
  table_property_id integer,
  business_area_id integer,
  guest_num bigint,
  tables_num bigint

);

CREATE TABLE if not exists hq_table_property_item (
tenancy_id varchar(32),
organ_id int4,
id serial NOT NULL,
table_property_id int4,
item_id int4,
unit_id int4,
dishes_sign int4,
remark varchar(100),
last_operator varchar(30),
last_updatetime timestamp(6),
CONSTRAINT pk_hq_table_property_item PRIMARY KEY (id)
);




create table if not exists pos_open_cashbox_log (
   tenancy_id           character varying(32) null,
   store_id             int                  null,
   id                   serial not null,
   report_date          date                 null,
   shift_id             int                  null,
   opt_num              character varying(32) null,
   pos_num              character varying(32) null,
   manage_num           character varying(32) null,
   operat_type          character varying(16) null,
   last_updatetime      timestamp            null,
   remark               character varying(32) null,
   upload_tag           character varying(16) null,
   constraint pk_pos_open_cashbox_log primary key (id)
);  

create table if not exists pos_opter_changshift_main (
   tenancy_id           character varying(32) null,
   store_id             int                  null,
   report_date          date                 null,
   id                   serial not null,
   shift_id             int                  null,
   opt_num              character varying(32) null,
   pos_num              character varying(32) null,
   is_allpos            character varying(16) null,
   begain_time          timestamp            null,
   end_time             timestamp            null,
   business_amount      decimal(19,4)        null,
   change               decimal(19,4)        null,
   discountk_amount     decimal(19,4)        null,
   discountr_amount     decimal(19,4)        null,
   maling_amount        decimal(19,4)        null,
   more_coupon          decimal(19,4)        null,
   givi_amount          decimal(19,4)        null,
   retreat_count        decimal(19,4)        null,
   retreat_amount       decimal(19,4)        null,
   single_retreat_count decimal(19,4)        null,
   single_retreat_amount decimal(19,4)        null,
   bills                decimal(19,4)        null,
   customer_recharge    decimal(19,4)        null,
   customer_consume     decimal(19,4)        null,
   back_card            decimal(19,4)        null,
   customer_deposit     decimal(19,4)        null,
   sale_card_amount   numeric(19,4)    null,
   indemnity_amount     decimal(19,4)        null,
   refund_amount        decimal(19,4)        null,
   last_updatetime      timestamp            null,
   remark               character varying(160) null,
   upload_tag           character varying(16) null,
   constraint pk_pos_opter_changshift_main primary key (id)
);

create table if not exists pos_opter_changshift_customer (
   tenancy_id           character varying(32) null,
   store_id             int                  null,
   id                   serial not null,
   changshift_id        int                  null,
   report_date          date                 null,
   shift_id             int                  null,
   opt_num              character varying(32) null,
   pos_num              character varying(32) null,
   operat_type          character varying(16) null,
   operat_details       character varying(16) null,
   trade_amount         decimal(19,4)        null,
   real_amount          decimal(19,4)        null,
   differ_amount        decimal(19,4)        null,
   last_updatetime      timestamp            null,
   remark               character varying(32) null,
   upload_tag           character varying(16) null,
   constraint pk_pos_opter_changshift_customer primary key (id)
);

create table if not exists pos_cashier_receive_log_details
(
  tenancy_id character varying(32),
  store_id integer,
  id serial not null,
  receive_id int                  null,
  cashier_id           int                  null,
  pos_num              character varying(32) null,
  waiter_id            int                  null,
  bill_num character varying(20),
  last_updatetime      timestamp            null,
  upload_tag           character varying(8) null,
  constraint pk_pos_cashier_receive_log_details primary key (id)
);  
  
select p_addfield('pos_cashier_receive_log','payment_id','integer');

select p_addfield('pos_opter_changshift','changshift_id','integer');

select p_addfield('pos_bill_payment','more_coupon','numeric(19,4)');
select p_addfield('pos_bill_payment2','more_coupon','numeric(19,4)');
select p_addfield('pos_bill_payment_log','more_coupon','numeric(19,4)');
select p_addfield('pos_bill_payment_regain','more_coupon','numeric(19,4)');

select p_addfield('crm_activation_lost_list','deposit','numeric(19,4)');
select p_addfield('crm_activation_lost_list','sales_price','numeric(19,4)');

create table if not exists pos_bill_batch
(
  tenancy_id character varying(32),
  store_id integer,
  id serial NOT NULL,
  bill_num character varying(20),
  batch_num character varying(20),
  serial_num character varying(20),
  order_num character varying(100),
  copy_bill_num character varying(20),
  report_date date,
  shift_id integer,
  table_code character varying(50),
  item_menu_id integer,
  guest integer,
  opentable_time timestamp without time zone,
  open_pos_num character varying(10),
  open_opt character varying(20),
  waiter_num character varying(20),
  payment_time timestamp without time zone,
  pos_num character varying(10),
  cashier_num character varying(20),
  payment_num integer,
  service_id integer,
  service_amount numeric(19,4),
  service_discount integer,
  print_time timestamp without time zone,
  print_count integer,
  subtotal numeric(19,4) DEFAULT 0,
  bill_amount numeric(19,4) DEFAULT 0,
  payment_amount numeric(19,4) DEFAULT 0,
  difference numeric(19,4) DEFAULT 0,
  discountk_amount numeric(19,4) DEFAULT 0,
  discountr_amount numeric(19,4) DEFAULT 0,
  maling_amount numeric(19,4) DEFAULT 0,
  single_discount_amount numeric(19,4) DEFAULT 0,
  discount_amount numeric(19,4) DEFAULT 0,
  free_amount numeric(19,4) DEFAULT 0,
  givi_amount numeric(19,4) DEFAULT 0,
  more_coupon numeric(19,4) DEFAULT 0,
  average_amount numeric(19,4) DEFAULT 0,
  discount_num character varying(20),
  discount_case_id integer,
  discount_rate integer,
  billfree_reason_id integer,
  discount_mode_id integer,
  transfer_remark character varying(100),
  sale_mode character varying(20),
  bill_state character varying(20),
  bill_property character varying(20),
  source character varying(20),
  upload_tag integer DEFAULT 0,
  deposit_count integer,
  opt_login_number integer,
  guest_msg character varying(200),
  integraloffset numeric(19,4),
  remark character varying(500),
  return_amount numeric(19,4),
  advance_payment_amt numeric(19,4),
  advance_refund_amt numeric(19,4), 
  is_refund character varying(10), 
  payment_id integer, 
  pay_no character varying(50), 
  third_bill_code character varying(50), 
  payment_state character varying(20), 
  payment_manager_num character varying(30), 
  CONSTRAINT pk_pos_bill_batch PRIMARY KEY (id)
);

select p_addfield('pos_byjdc_log','authorize_id','character varying(10)');

select p_addfield('cc_order_item','store_id','integer');

select p_addfield('cc_order_item','report_date','date');

select p_addfield('cc_order_repayment','store_id','integer');
select p_addfield('cc_order_repayment','report_date','date');
select p_addfield('cc_order_repayment','upload_tag','int4');

select p_addfield('cc_order_list','report_date','date');

select p_addfield('crm_card_trading_list','card_class_id','integer');
select p_addfield('crm_card_trading_list','shift_id','integer');
select p_addfield('crm_card_trading_list','operator_id','integer');
select p_addfield('crm_card_trading_list','total_balance','numeric(19,4)');
select p_addfield('crm_card_trading_list','reward_balance','numeric(19,4)');
select p_addfield('crm_card_trading_list','main_balance','numeric(19,4)');
select p_addfield('crm_card_trading_list','pay_type','character varying(30)');
select p_addfield('crm_card_trading_list','name','character varying(30)');
select p_addfield('crm_card_trading_list','mobil','character varying(15)');

select p_addfield('crm_activation_lost_list','shift_id','integer');
select p_addfield('crm_activation_lost_list','operator_id','integer');

select p_addfield('crm_activation_lost_list','business_date','date');


select p_dropfield('hq_lineup_dinner_org','lineup_number');
select p_addfield('hq_lineup_dinner_org','lineup_number','int4');
select p_addfield('crm_customer_address','state','int4');


select p_addfield('wx_comment','quality_star','int4');
select p_addfield('wx_comment','service_star','int4');

 
select p_addfield('cc_order_list','is_comment','int4');


CREATE TABLE if not exists pos_soldout (
"tenancy_id" character varying(32) COLLATE "default",
"store_id" int4,
"id" serial NOT NULL,
"item_id" int4,
"num" numeric(19,2),
"count" numeric(19,2),
"item_unit_id" int4,
"setdate" timestamp(6),
"remark" varchar(500) COLLATE "default",
"chanel" varchar(50) COLLATE "default",
"report_date" char(10) COLLATE "default",
"last_operator" varchar(30) COLLATE "default",
"last_updatetime" timestamp(6),
"soldout_type" varchar(30) COLLATE "default",
CONSTRAINT "pk_pos_soldout" PRIMARY KEY ("id")
);


select p_addfield('user_authority_roles','employee_id','int4');
select p_addfield('user_authority_roles','group_sign','int4');

select p_addfield('crm_customer_credit_list','batch_num','VARCHAR(50)');
select p_addfield('crm_customer_credit_list','shift_id','int4');


select p_addfield('b_dcjryb','card_code','varchar(50)');
select p_addfield('b_dcjryb','coupons','varchar(200)');

select p_addfield('pos_item_worrysale','num','int4');


create table if not exists pos_thirdpay (
tenancy_id varchar(32) COLLATE "default",
store_id int4,
id serial NOT NULL,
pos_num varchar(30) COLLATE "default",
report_date date,
third_pos_num varchar(100) COLLATE "default",
third_store_num varchar(100) COLLATE "default",
trading_time timestamp(6),
shift_id int4,
bill_num varchar(100) COLLATE "default",
pay_batch varchar(100) COLLATE "default",
cashier_num varchar(100) COLLATE "default",
card_no varchar(100) COLLATE "default",
card_type varchar(30) COLLATE "default",
trading_type varchar(30) COLLATE "default",
trading_type_num varchar(30) COLLATE "default",
amount numeric(19,4),
payment_id int4,
payment_third_num varchar(100) COLLATE "default",
request_data text COLLATE "default",
response_data text COLLATE "default",
remark varchar(100) COLLATE "default",
old_expire_date varchar(100) COLLATE "default",
old_trading_num varchar(100) COLLATE "default",
old_hostserial_num varchar(100) COLLATE "default",
oldtracenumber varchar(100) COLLATE "default",
oldauth_num varchar(100) COLLATE "default",
old_trading_date date,
request_times int4,
CONSTRAINT pk_pos_thirdpay PRIMARY KEY (id)
);


create table if not exists pos_thirdpay_detail (
tenancy_id varchar(32) COLLATE "default",
store_id int4,
id serial NOT NULL,
father_id int4,
thirdpay_id varchar(100) COLLATE "default",
oldauth_num varchar(100) COLLATE "default",
rejcode varchar(100) COLLATE "default",
trading_num varchar(100) COLLATE "default",
trace_numr varchar(100) COLLATE "default",
pos_trace_num varchar(100) COLLATE "default",
hostserial_num varchar(100) COLLATE "default",
tips numeric(19,4),
total numeric(19,4),
amount numeric(19,4),
balance_amount numeric(19,4),
expire_date varchar(100) COLLATE "default",
third_store_num varchar(100) COLLATE "default",
full_name varchar(100) COLLATE "default",
third_pos_num varchar(100) COLLATE "default",
iss_num varchar(100) COLLATE "default",
iss_name varchar(100) COLLATE "default",
card_no varchar(100) COLLATE "default",
trading_date date,
trading_time varchar(100) COLLATE "default",
re_code_explain text COLLATE "default",
remark varchar(100) COLLATE "default",
CONSTRAINT pk_pos_thirdpay_detail PRIMARY KEY (id)
);

ALTER TABLE cc_order_list ALTER COLUMN upload_tag DROP DEFAULT;

ALTER TABLE cc_order_item ALTER COLUMN upload_tag DROP DEFAULT;

ALTER TABLE cc_order_repayment ALTER COLUMN upload_tag DROP DEFAULT;


create table if not exists cc_commission_organ_info (
   id                   serial               not null,
   tenancy_id           character varying(32) null,
   store_id             int4                 null,
   chanel               character varying(10) null,
   commission_type      character varying(10) null,
   commission_rate      DECIMAL(19,4)        null,
   start_date           DATE                 null,
   end_date             DATE                 null,
   create_operator      character varying(30) null,
   create_time          timestamp            null,
   last_operator        character varying(30) null,
   last_updatetime      timestamp            null,
   constraint PK_CC_COMMISSION_ORGAN_INFO primary key (id)
);



select p_addfield('cc_order_list','commission_rate','DECIMAL(19,4)');

select p_addfield('cc_order_list','commission_amount','DECIMAL(19,4)');

select p_addfield('scm_supply','payment_type','CHARACTER VARYING(100)');
select p_addfield('scm_supply','send_type','CHARACTER VARYING(100)');
select p_addfield('scm_supply','alipay_no','CHARACTER VARYING(100)');

create table if not exists scm_material_in
    (
        tenancy_id CHARACTER VARYING(50),
        id serial NOT NULL,
        store_id INTEGER,
        material_id INTEGER NOT NULL,
        unit_id INTEGER,
        channel_type CHARACTER VARYING(100),
        dc_id INTEGER,
        dc_type CHARACTER VARYING(100),
        charge_type CHARACTER VARYING(100),
        payment_type CHARACTER VARYING(100),
        dc_price NUMERIC(19,4),
        supply_id1 INTEGER,
        sup1_percent INTEGER,
        supply_id2 INTEGER,
        sup2_percent INTEGER,
        supply_id3 INTEGER,
        sup3_percent INTEGER,
        is_temp CHARACTER VARYING(100),
        max_price NUMERIC(19,4),
        max_qty NUMERIC(19,4),
        last_operator CHARACTER VARYING(30),
        remark CHARACTER VARYING(100),
        last_updatetime TIMESTAMP(6) WITHOUT TIME ZONE,
        CONSTRAINT pk_scm_material_in PRIMARY KEY (id)
    );


create table if not exists cc_third_item_class_info (
	id serial NOT NULL,
	shop_id int4,
	item_class_id int4,
	last_send_class_name varchar(100) COLLATE "default",
	cur_class_name varchar(100) COLLATE "default",
	rank varchar(30) COLLATE "default",
	whether_push_over varchar(5) COLLATE "default",
	send_operator varchar(30) COLLATE "default",
	send_time timestamp(6),
	update_operator varchar(30) COLLATE "default",
	update_time timestamp(6),
	channel varchar(30) COLLATE "default",
	tenancy_id varchar(32) COLLATE "default",
	remark varchar(300) COLLATE "default",
	third_class_id int4
);


create table if not exists cc_third_item_info (
id serial NOT NULL,
shop_id int4,
min_order_num int4,
package_box_num int4,
available_times_start varchar(30) COLLATE "default",
available_times_end varchar(30) COLLATE "default",
whether_push_over varchar(5) COLLATE "default",
description varchar(100) COLLATE "default",
item_pic varchar(2000) COLLATE "default",
item_status int4,
item_code varchar(30) COLLATE "default",
send_operator varchar(30) COLLATE "default",
send_time timestamp(6),
update_operator varchar(30) COLLATE "default",
update_time timestamp(6),
channel varchar(30) COLLATE "default",
tenancy_id varchar(32) COLLATE "default",
remark varchar(200) COLLATE "default",
rank varchar(20) COLLATE "default",
unit varchar(20) COLLATE "default",
is_sold_out varchar(10) COLLATE "default",
box_price numeric(19,4),
item_pic_id varchar(32) COLLATE "default",
is_charge_commission varchar(10) COLLATE "default",
online_flag varchar(10) COLLATE "default",
third_item_id varbit(32),
is_commission varchar(32) COLLATE "default"
);

select p_addfield('cc_third_item_class_info','third_class_id','varchar(32)');
select p_addfield('cc_third_item_info','third_item_id','varchar(32)');
select p_addfield('pos_opter_changshift_main','customer_reward','DECIMAL(19,4)');


create table if not exists hq_table_printer (
   tenancy_id           character varying(32),
   organ_id             int,
   id                   serial                  not null,
   table_id             int,
   table_code           character varying(32),
   printer_id           int,
   remark               varchar(255),
   constraint pk_hq_table_printer primary key (id)
);

create table if not exists hq_devices_kvs_ref (
   tenancy_id           character varying(32) not null,
   id                   serial               not null,
   kvs_id               int                  null,
   devices_code         character varying(32) null,
   constraint PK_HQ_DEVICES_KVS_REF primary key (id)
);


select p_addfield('hq_devices','kvs_type','character varying(10)');

select p_addfield('hq_devices','show_mode','character varying(10)');

create table if not exists pos_kvs_bill (
   tenancy_id           character varying(32) null,
   store_id             int                  null,
   ID                   SERIAL               not null,
   bill_num             character varying(32) null,
   order_time           TIMESTAMP            null,
   kvs_num              character varying(16) null,
   mop_time             TIMESTAMP            null,
   complete_time        TIMESTAMP            null,
   meal_consume         TIME                 null,
   make_consume         TIME              null,
   remark               character varying(126) null,
   constraint PK_pos_kvs_bill primary key (id)
);

create table if not exists pos_kvs_bill_item (
   tenancy_id           character varying(32) null,
   store_id             int                  null,
   ID                   SERIAL               not null,
   bill_num             character varying(32) null,
   rwid                 INT4                 null,
   kvs_num              character varying(16) null,
   complete_time        TIMESTAMP            null,
   mop_time             TIMESTAMP            null,
   meal_consume         TIME              null,
   make_consume         TIME              null,
   remark               character varying(126) null,
   constraint PK_pos_kvs_bill_item primary key (id)
);

select p_addfield('pos_opter_changshift_main','customer_reward','NUMERIC (19, 4)');
select p_addfield('sys_notice_info','notice_type','int4');
select p_addfield('hq_devices','mealpreparetime','VARCHAR(20)');

create table if not exists crm_item_vip_sysprice (
   tenancy_id           character varying(32) null,
   id                   serial               not null,
   unit_id               int                  null,
   vip_price            decimal(19,4)        null,
   price_system         int                  null,
   chanel               character varying(30) null,
   constraint PK_CRM_ITEM_VIP_SYSPRICE primary key (id)
);

select p_addfield('pos_bill_member','bill_code','character varying(32)');
select p_addfield('pos_bill_member','request_state','character varying(8)');
select p_addfield('pos_bill_member','customer_code','character varying(32)');
select p_addfield('pos_payment_order','prepay_id','character varying(64)');

select p_addfield('pos_bill','shop_real_amount','DECIMAL(19,4)');
select p_addfield('pos_bill','total_fees','DECIMAL(19,4)');
select p_addfield('pos_bill','platform_charge_amount','DECIMAL(19,4)');
select p_addfield('pos_bill_payment','fee','DECIMAL(19,4)');
select p_addfield('pos_bill_payment','fee_rate','DECIMAL(19,4)');

select p_addfield('pos_bill2','shop_real_amount','DECIMAL(19,4)');
select p_addfield('pos_bill2','total_fees','DECIMAL(19,4)');
select p_addfield('pos_bill2','platform_charge_amount','DECIMAL(19,4)');
select p_addfield('pos_bill_payment2','fee','DECIMAL(19,4)');
select p_addfield('pos_bill_payment2','fee_rate','DECIMAL(19,4)');

select p_addfield('pos_bill_regain','shop_real_amount','DECIMAL(19,4)');
select p_addfield('pos_bill_regain','total_fees','DECIMAL(19,4)');
select p_addfield('pos_bill_regain','platform_charge_amount','DECIMAL(19,4)');
select p_addfield('pos_bill_payment_regain','fee','DECIMAL(19,4)');
select p_addfield('pos_bill_payment_regain','fee_rate','DECIMAL(19,4)');
select p_addfield('pos_opter_changshift_main','shop_real_amount','DECIMAL(19,4)');
select p_addfield('pos_opter_changshift_main','platform_charge_amount','DECIMAL(19,4)');



select p_addfield('scm_transfer_history','in_store_id','INTEGER');
select p_addfield('scm_transfer','in_store_id','INTEGER');
select p_addfield('scm_transfer_history','in_store_name','CHARACTER VARYING(100)');

select p_addfield('scm_transfer','up_bill_id','INTEGER');
select p_addfield('scm_transfer','up_bill_no','CHARACTER VARYING(50)');
select p_addfield('scm_transfer','up_bill_type','CHARACTER VARYING(50)');

select p_addfield('scm_transfer_history','up_bill_id','INTEGER');
select p_addfield('scm_transfer_history','up_bill_no','CHARACTER VARYING(50)');
select p_addfield('scm_transfer_history','up_bill_type','CHARACTER VARYING(50)');
select p_addfield('scm_transfer','in_store_id','INTEGER');

select p_addfield('scm_material','is_week_stock','CHARACTER VARYING(30)');
select p_addfield('scm_material','is_month_stock','CHARACTER VARYING(30)');
select p_addfield('scm_material','is_stat_yield','CHARACTER VARYING(30)');
select p_addfield('scm_material','is_different','CHARACTER VARYING(30)');
select p_addfield('scm_material','yield_norm','NUMERIC(19,2)');
select p_addfield('scm_material','yield_describe','CHARACTER VARYING(100)');

select p_addfield('scm_item_bom_detail','is_norm_deal','CHARACTER VARYING(10)');
select p_addfield('scm_item_bom_detail_material','is_norm_deal','CHARACTER VARYING(10)');

select p_addfield('crm_customer_address','address','varchar(1000)');

select p_addfield('organ','fake_id','int4');
select p_addfield('employee','fake_id','int4');
select p_addfield('user_authority','fake_id','int4');
select p_addfield('tables_info','fake_id','int4');
select p_addfield('hq_price_system','fake_id','int4');
select p_addfield('hq_item_class','fake_id','int4');
select p_addfield('hq_item_info','fake_id','int4');
select p_addfield('hq_item_unit','fake_id','int4');
select p_addfield('hq_item_pricesystem','fake_id','int4');
select p_addfield('hq_item_menu','fake_id','int4');
select p_addfield('hq_item_menu_class','fake_id','int4');
select p_addfield('hq_item_menu_details','fake_id','int4');
select p_addfield('hq_item_menu_organ','fake_id','int4');
select p_addfield('hq_item_group','fake_id','int4');
select p_addfield('hq_item_group_details','fake_id','int4');
select p_addfield('hq_item_combo_details','fake_id','int4');

select p_addfield('cc_third_organ_info','close_description','varchar(200)');
select p_addfield('cc_third_organ_info','deliver_description','varchar(250)');
select p_addfield('cc_third_organ_info','description','varchar(250)');
select p_addfield('cc_third_organ_info','is_bookable','varchar(10)');
select p_addfield('cc_third_organ_info','promotion_info','varchar(300)');

select p_addfield('cc_third_item_info','is_new','varchar(10)');
select p_addfield('cc_third_item_info','is_featured','varchar(10)');
select p_addfield('cc_third_item_info','is_gum','varchar(10)');
select p_addfield('cc_third_item_info','is_spicy','varchar(10)');
select p_addfield('cc_third_item_info','stock','int4');
select p_addfield('cc_third_item_info','max_stock','varchar(10)');

select p_addfield('cc_order_discount','baidu_rate_temp','numeric(19,4)');
select p_addfield('cc_order_discount','shop_rate_temp','numeric(19,4)');
select p_addfield('cc_order_discount','agent_rate_temp','numeric(19,4)');
select p_addfield('cc_order_discount','logistics_rate_temp','numeric(19,4)');


select p_dropfield('cc_order_discount','baidu_rate');
select p_dropfield('cc_order_discount','shop_rate');
select p_dropfield('cc_order_discount','agent_rate');
select p_dropfield('cc_order_discount','logistics_rate');

select p_addfield('cc_order_discount','baidu_rate','numeric(19,4)');
select p_addfield('cc_order_discount','shop_rate','numeric(19,4)');
select p_addfield('cc_order_discount','agent_rate','numeric(19,4)');
select p_addfield('cc_order_discount','logistics_rate','numeric(19,4)');

update cc_order_discount set baidu_rate=baidu_rate_temp,shop_rate=shop_rate_temp,agent_rate=agent_rate_temp,logistics_rate=logistics_rate_temp;

select p_dropfield('cc_order_discount','baidu_rate_temp');
select p_dropfield('cc_order_discount','shop_rate_temp');
select p_dropfield('cc_order_discount','agent_rate_temp');
select p_dropfield('cc_order_discount','logistics_rate_temp');

select p_addfield('cc_order_list','shop_real_amount','DECIMAL(19,4)');
select p_addfield('cc_order_list','total_fees','DECIMAL(19,4)');
select p_addfield('cc_order_list','platform_charge_amount','DECIMAL(19,4)');

select p_addfield('cc_order_repayment','fee','DECIMAL(19,4)');
select p_addfield('cc_order_repayment','rate','DECIMAL(19,4)');


select p_addfield('crm_activation_lost_list','salesman','INT');
select p_addfield('crm_activation_lost_list','commission_saler_money','numeric(19,4)');
select p_addfield('crm_activation_lost_list','commission_store_money','numeric(19,4)');

select p_addfield('crm_card_trading_list','salesman','INT');
select p_addfield('crm_card_trading_list','commission_saler_money','numeric(19,4)');
select p_addfield('crm_card_trading_list','commission_store_money','numeric(19,4)');
select p_addfield('crm_customer_card','recharge_times','INT');

select p_addfield('crm_card_class','is_physical_card','VARCHAR(10)');
select p_addfield('crm_card_class','card_sale_price','numeric(19,4)');
select p_addfield('crm_card_class','card_deposit','numeric(19,4)');
select p_addfield('crm_card_class','limit_prestore_per','numeric(19,4)');
select p_addfield('crm_card_class','commission_saler','numeric(19,4)');
select p_addfield('crm_card_class','commission_store','numeric(19,4)');
select p_addfield('crm_card_class','commision_predeposit_per','numeric(19,4)');
select p_addfield('crm_card_class','commision_predeposit_store','numeric(19,4)');
select p_addfield('crm_card_class','commision_predeposit_per2','numeric(19,4)');
select p_addfield('crm_card_class','commision_predeposit_store2','numeric(19,4)');

select p_addfield('crm_level','credit_cash_bill','numeric(19,4)');
select p_addfield('hq_discount_case','is_vipprice','character varying(2)');

select p_addfield('user_discount_authority','employee_id','int4');
select p_addfield('user_discount_authority','fixed_discount_limit','numeric(19,4)');
select p_addfield('user_discount_authority','fall_discount_limit','numeric(19,4)');
select p_addfield('user_discount_authority','single_discount_limit','numeric(19,4)');
select p_addfield('user_discount_authority','is_discount_case','character varying(2)');
select p_addfield('user_discount_authority','last_operator','character varying(30)');
select p_addfield('user_discount_authority','last_updatetime','timestamp(6)');

select p_addfield('user_discount_case','employee_id','int4');

select p_addfield('crm_customer_card','invoice_balance','numeric(19,4)');
select p_addfield('crm_customer_card','invoice_money','numeric(19,4)');
select p_addfield('crm_customer_card','last_invioce_time','timestamp');

select p_addfield('crm_card_trading_list','invoice_balance','numeric(19,4)');



create table if not exists crm_card_invoice (
   tenancy_id           character varying(32) null,
   id                   serial                 not null,
   bill_code            character varying(50) null,
   invoice_money        decimal(19,4)        null,
   store_id             INT             null,
   business_date        date                 null,
   pos_bum              character varying(10) null,
   remark               character varying(200) null,
   invoice_head         character varying(200) null,
   operator             character varying(10) null,
   operate_time         timestamp            null,
   operator_id          int                  null,
   shift_id             int                  null,
   card_code            character varying(200) null,
   customer_id          int                  null,
   constraint PK_CRM_CARD_INVOICE primary key (id)
);

create table if not exists crm_activity_supplement (
   tenancy_id           character varying(32) null,
   activity_id          int                  null,
   id                   serial               not null,
   supplement           character varying(30) null,
   reward_credit        int                  null,
   face_value           decimal(19,4)        null,
   reward_coupon        int                  null,
   remark               character varying(200) null,
   constraint PK_CRM_ACTIVITY_SUPPLEMENT primary key (id)
);

select p_addfield('payment_way','fake_id','int4');
select p_addfield('payment_way_of_ogran','fake_id','int4');
select p_addfield('hq_item_class','fake_type','int4');
select p_addfield('pos_payment_order','order_code','character varying(50)');


select p_addfield('hq_item_unit','hq_unit_id','int');

select p_addfield('hq_item_info','is_needlunchbox','VARCHAR(10)');
select p_addfield('hq_item_info','price_property','VARCHAR(30)');
select p_addfield('hq_item_info','detailprice_shareway','VARCHAR(30)');
select p_addfield('hq_item_info','combo_property','VARCHAR(30)');
select p_addfield('hq_item_info','is_mustpoint','VARCHAR(10)');

select p_addfield('sys_print_format','store_id','int');

select p_addfield('crm_item_vip','hq_unit_id','INT');

select p_addfield('crm_activity_chz','rebate_type','varchar(2)');


CREATE TABLE IF not EXISTS hq_printer_item (
tenancy_id varchar(32) COLLATE "default",
id SERIAL NOT NULL,
item_id int4,
print_id int4,
print_format_id int4,
constraint PK_hq_printer_item primary key (id)
);

CREATE TABLE if not EXISTS hq_printer_model (
tenancy_id varchar(32) COLLATE "default" NOT NULL,
id SERIAL NOT NULL,
printer_id int4,
is_enables varchar(10) COLLATE "default",
print_num int4,
is_immedistely_print varchar(10) COLLATE "default",
is_item varchar(10) COLLATE "default",
is_area varchar(10) COLLATE "default",
format_type varchar(30) COLLATE "default",
format_id int4,
constraint PK_hq_printer_model primary key (id)
);

create table if not exists hq_printer_new (
tenancy_id varchar(32) collate "default" not null,
store_id int4 not null,
id  serial not null,
code varchar(30) collate "default",
name varchar(50) collate "default",
type varchar(10) collate "default",
ip_com varchar(50) collate "default",
baud_rate varchar(30) collate "default",
is_area varchar(10) collate "default",
spare_id int4,
kitchen_sign varchar(2) collate "default",
remark varchar(100) collate "default",
valid_state varchar(10) collate "default" not null,
last_operator varchar(30) collate "default" not null,
last_updatetime timestamp(6) not null,
site_id int4,
constraint pk_hq_printer_new primary key (id)
);


CREATE TABLE IF not EXISTS hq_printer_table (
tenancy_id varchar(32) COLLATE "default",
id SERIAL NOT NULL,
printer_id int4,
table_id int4,
constraint PK_hq_printer_table primary key (id)
);

CREATE TABLE if not EXISTS sys_model_function (
tenancy_id varchar(32) COLLATE "default" NOT NULL,
id SERIAL NOT NULL,
function_id varchar(30) COLLATE "default",
format_type varchar(30) COLLATE "default",
format_name varchar(32) COLLATE "default",
type int4,
constraint PK_sys_model_function primary key (id)
);

CREATE TABLE if not EXISTS  sys_print_format_new (
tenancy_id varchar(32) COLLATE "default" NOT NULL,
id SERIAL NOT NULL,
model_name varchar(100) COLLATE "default",
class_identifier_code varchar(100) COLLATE "default",
class_item_code varchar(100) COLLATE "default",
format_name varchar(100) COLLATE "default",
page_size varchar(100) COLLATE "default",
character_size varchar(100) COLLATE "default",
param_pairs text COLLATE "default",
content text COLLATE "default",
print_num int4,
param_pairs2 text COLLATE "default",
content2 text COLLATE "default",
param_pairs3 text COLLATE "default",
content3 text COLLATE "default",
param_pairs4 text COLLATE "default",
content4 text COLLATE "default",
valid_state varchar(10) COLLATE "default",
last_operator varchar(50) COLLATE "default",
last_updatetime timestamp(6),
isdot char(1) COLLATE "default",
uuid varchar(50) COLLATE "default",
constraint PK_sys_print_format_new primary key (id)
);

create table IF NOT EXISTS sys_print_format_detail_new (
   tenancy_id           character varying(32) not null,
   id                   SERIAL not null,
   parent_id            int                  null,
   result_set_title     character varying(100) null,
   sql                  text                 null,
   is_test              character varying(10) null,
   parent_uuid          character varying(50) null
);

select p_addfield('cc_order_list','settlement_type','character varying(10)');

select p_addfield('cc_order_list','check_mode','character varying(10)');

select p_addfield('cc_order_list','discountk_amount','DECIMAL(19,4)');

create table if not EXISTS cc_third_check_mode (
   id                   serial               not null,
   tenancy_id           character varying(32) null,
   store_id             int4                 null,
   chanel               character varying(10) null,
   discount_rate        DECIMAL(19,4)        null,
   settlement_type      character varying(10) null,
   check_mode           character varying(10) null,
   rounding_mode        character varying(10) null,
   payment_id           int4                 null,
   create_operator      character varying(30) null,
   create_time          timestamp            null,
   last_operator        character varying(30) null,
   last_updatetime      timestamp            null,
   constraint PK_CC_THIRD_CHECK_MODE primary key (id)
);


create table if not EXISTS hq_paygateway_orders 
(
   id   serial               not null,
   tenancy_id           varchar(32)                    null,
   store_id             int                            null,
   channel              varchar(32)                    null,
   bill_num             varchar(20)                    null,
   pos_num              varchar(10)                    null,
   op_num               varchar(20)                    null,
   shift_id             int4                           null,
   pay_type             varchar(16)                    null,
   order_no             varchar(16)                    null,
   order_type           varchar(8)                     null,
   orgin_order_no       varchar(16)                    null,
   state                int4                           null,
   service_type          varchar(20)                   null,
   client_ip            varchar(16)                    null,
   currency_name        varchar(8)                     null,
   amount               numeric(9,4)                   null,
   create_time          timestamp                      null,
   business_date        timestamp                      null,
   subject              varchar(64)                    null,
   body                 varchar(255)                   null,
   description          varchar(255)                   null,
   expire_time          timestamp                      null,
   settle_time          timestamp                      null,
   transaction_no       varchar(64)                    null,
   cancel_no						varchar(64)										 null,
   is_paid              varchar(8)                     null,
   paid_time            timestamp                      null,
   is_refunded          varchar(8)                     null,
   refunds_id           int4                           null,
   refunded_amount      numeric(9,4)                   null,
   extra                text                           null,
   failure_code         varchar(8)                     null,
   failure_msg          varchar(255)                   null,
	 CONSTRAINT "pk_hq_paygateway_orders" PRIMARY KEY ("id")
);


create table if not exists pos_print_task (
tenancy_id 	varchar(32)  not null,
store_id 	int4,
task_id serial not null,
state int4 default 0,
print_name varchar(32) ,
print_address varchar(32) ,
print_parameter varchar(32) ,
templete_id int4,
params text ,
send_time timestamp(6),
print_time timestamp(6),
order_no int4 default 0,
print_times int4 default 0,
bak_print_name varchar(32) ,
bak_print_address varchar(32) ,
report_date date,
print_result varchar(256) ,
pos_num varchar(16) ,
opt_num varchar(50) ,
site_id int4,
area_id int4,
class_item_code varchar(100) ,
bak_print_parameter varchar(100) ,
print_num int4,
constraint pos_print_task_pkey primary key (task_id)
);


create table if not exists pos_third_payment_order (
   tenancy_id           character varying(32) null,
   store_id             int4                 null,
   id                   serial               not null,
   report_date          date                 null,
   shift_id             int4                 null,
   pos_num              character varying(16) null,
   opt_num              character varying(16) null,
   channel              character varying(16) null,
   object_name          character varying(16) null,
   service_type         character varying(16) null,
   order_num            character varying(64) null,
   payment_class        character varying(32) null,
   payment_id           int4                 null,
   total_amount         decimal(19,4)        null,
   settle_amount        decimal(19,4)        null,
   currency_name        character varying(16) null,
   subject              character varying(32) null,
   body                 character varying(256) null,
   client_ip            character varying(32) null,
   extra                TEXT                 null,
   description          character varying(256) null,
   metadata             TEXT                 null,
   credential           character varying(256) null,
   transaction_no       character varying(64) null,
   qr_code              character varying(256) null,
   create_time          TIMESTAMP            null,
   paid_time            TIMESTAMP            null,
   expire_time          TIMESTAMP            null,
   is_refunded          BOOL                 null,
   refunds_order        character varying(64) null,
   refunds_id           int4                 null,
   amount_refunded      decimal(19,4)        null,
   is_succeed           BOOL                 null,
   status               character varying(16) null,
   failure_code         character varying(32) null,
   failure_msg          character varying(256) null,
   request_status       character varying(16) null,
   request_code         character varying(32) null,
   request_msg          character varying(256) null,
   constraint PK_POS_THIRD_PAYMENT_ORDER primary key (id)
);

CREATE TABLE if not exists wx_print_param
(
  id serial NOT NULL,
  tenancy_id character varying(20) NOT NULL, 
  url character varying(200) NOT NULL, 
  CONSTRAINT wx_print_param_pkey PRIMARY KEY (id)
);

select p_addfield('sys_payment_account_config','service_type','varchar(10)');

select p_addfield('pos_bill','settlement_type','character varying(10)');
select p_addfield('pos_bill2','settlement_type','character varying(10)');

select p_dropfield('hq_paygateway_orders','order_no');

select p_addfield('hq_paygateway_orders','order_no','varchar(30)');

select p_addfield('cc_order_list','collect_more_ticket','numeric(19,4)');
select p_addfield('hq_item_class','is_discount','varchar(10)');


select p_addfield('cc_order_list','more_coupon','numeric(19,4)');
select p_addfield('cc_order_repayment','more_coupon','numeric(19,4)');
select p_addfield('cc_third_organ_info','is_open_suwei','varchar(10)');

select p_addfield('pos_bill_member','customer_name','character varying(100)');
select p_addfield('pos_bill_member','consume_before_credit','numeric(19,4)');
select p_addfield('pos_bill_member','consume_after_credit','numeric(19,4)');


create table if not exists pos_bill_payment_coupons (
   tenancy_id           character varying(32) null,
   store_id             int                  null,
   id                   SERIAL not null,
   bill_num             character varying(32) null,
   report_date          date                 null,
   payment_id           int4                 null,
   coupons_code         character varying(32) null,
   deal_value           DECIMAL(19,4)        null,
   deal_name            character varying(32) null,
   last_updatetime      timestamp            null,
   remark               character varying(256) null,
   upload_tag           character varying(16) null,
   is_cancel            character varying(16) null,
   constraint PK_POS_BILL_PAYMENT_COUPONS primary key (id)
);


CREATE TABLE  IF NOT EXISTS boh_property_interface_parameter(
tenancy_id varchar(50) COLLATE "default",
id serial NOT NULL,
type_id int4 ,
store_id int4 ,
property_name varchar(100) COLLATE "default",
url varchar(2000) COLLATE "default",
licensekey varchar(300) COLLATE "default",
mallid varchar(100) COLLATE "default",
storecode varchar(100) COLLATE "default",
user_name varchar(100) COLLATE "default",
password varchar(100) COLLATE "default",
remark varchar(100) COLLATE "default",
valid_state varchar(10) COLLATE "default" NOT NULL,
last_operator varchar(30) COLLATE "default" NOT NULL,
last_updatetime timestamp(6) NOT NULL,
constraint PK_boh_property_interface_parameter primary key (id)
);

CREATE TABLE  IF NOT EXISTS boh_property_interface_upload_summary(
	tenancy_id varchar(50) COLLATE "default",
	id serial NOT NULL,
	type_id int4 ,
  user_name varchar(100) COLLATE "default",
	store_id int4 ,
	business_date date,
	send_total int4,
	constraint PK_boh_property_interface_upload_summary primary key (id)
);

CREATE TABLE  IF NOT EXISTS boh_property_interface_upload(
tenancy_id varchar(50) COLLATE "default",
id serial NOT NULL,
type_id int4 ,
user_name varchar(100) COLLATE "default",
store_id int4 ,
send_id int4 ,
last_update_person varchar(100) COLLATE "default",
last_update_time timestamp(6),
constraint PK_boh_property_interface_upload primary key (id)
);

CREATE TABLE if not Exists  hq_organ_property_info (
tenancy_id varchar(30) COLLATE "default" NOT NULL,
id serial NOT NULL,
store_id int4,
property_id varchar(100) COLLATE "default",
transfer_mode varchar(10) COLLATE "default",
server_address varchar(50) COLLATE "default",
user_name varchar(30) COLLATE "default",
password varchar(30) COLLATE "default",
file_path varchar(30) COLLATE "default",
file_name varchar(30) COLLATE "default",
file_name_mode varchar(10) COLLATE "default",
file_format varchar(10) COLLATE "default",
file_separator varchar(10) COLLATE "default",
database_name varchar(30) COLLATE "default",
port varchar(10) COLLATE "default",
middle_table_name varchar(30) COLLATE "default",
http_url varchar(300) COLLATE "default",
last_operator varchar(30) COLLATE "default" NOT NULL,
last_updatetime timestamp(6) NOT NULL,
orange_perproty_info_id varchar(32) COLLATE "default",
constraint PK_HQ_ORGAN_PROPERTY_INFO primary key (id)
);

select p_addfield('crm_activity_wxcupous','sale_credit','numeric(19,4)');

select p_addfield('crm_activity_wxonline_list','sale_credit','numeric(19,4)');

select p_addfield('cc_third_organ_info','suwei_organ_id','varchar(50)');

CREATE TABLE if not EXISTS  hq_organ_property_parameters (
"tenancy_id" varchar(30) COLLATE "default" NOT NULL,
"id" serial NOT NULL,
"organ_property_id" int4,
"number" int4,
"parameter_name" varchar(30) COLLATE "default",
"patameter_desc" varchar(100) COLLATE "default",
"is_required" varchar(10) COLLATE "default" NOT NULL,
"default_value" varchar(10) COLLATE "default" NOT NULL,
"field_name" varchar(30) COLLATE "default",
"result_id" int4,
"rule_value" varchar(100) COLLATE "default",
"parent_id" int4,
 constraint PK_HQ_ORGAN_PROPERTY_PARAMETERS primary key (id)
);

CREATE TABLE if not EXISTS cc_order_list_suwei (
	"id" serial NOT NULL,
  "tenancy_id" varchar(32) NULL,
	"order_code" varchar(50) NULL,
	"suwei_order_id" varchar(50) NULL,
	"suwei_status" varchar(10) NULL,
	"box_num" varchar(50) NULL,
	"pass" varchar(50) NULL,
	"des" varchar(50) NULL,
	"last_updatetime" varchar(50) NULL,
	CONSTRAINT "pk_cc_order_list_suwei" PRIMARY KEY ("id")
);

CREATE TABLE if not EXISTS cc_order_log (
	"id" serial NOT NULL,
	"order_code" varchar(50) null,
	"third_order_code" varchar(50) null,
	"request_info" TEXT null,
	"response_info" TEXT null,
	"request_result" varchar(10) null,
	"request_time" TIMESTAMP null,
	CONSTRAINT "pk_cc_order_log" PRIMARY KEY ("id")
);

select p_addfield('cc_order_item_details','is_itemgroup','varchar(19)');

select p_addfield('cc_order_item_details','item_group_id','int4');



select p_addfield('cc_order_list','free_amount','numeric(19,4)');

select p_addfield('cc_order_list','givi_amount','numeric(19,4)');
select p_addfield('cc_order_item','discountk_amount','numeric(19,4)');
select p_addfield('cc_order_item','free_amount','numeric(19,4)');

select p_addfield('cc_order_item','givi_amount','numeric(19,4)');

select p_addfield('cc_order_list','discountr_amount','numeric(19,4)');

select p_addfield('cc_order_item','free_amount','numeric(19,4)');
select p_addfield('cc_order_item','discountk_amount','numeric(19,4)');
select p_addfield('cc_order_item','discountr_amount','numeric(19,4)');

CREATE TABLE if not EXISTS sys_model_function (
tenancy_id varchar(32) COLLATE "default" NOT NULL,
id SERIAL NOT NULL,
function_id varchar(30) COLLATE "default",
format_type varchar(30) COLLATE "default",
format_name varchar(32) COLLATE "default",
type int4,
constraint PK_sys_model_function primary key (id)
);


CREATE TABLE if not EXISTS  sys_print_format_new (
tenancy_id varchar(32) COLLATE "default" NOT NULL,
id SERIAL NOT NULL,
model_name varchar(100) COLLATE "default",
class_identifier_code varchar(100) COLLATE "default",
class_item_code varchar(100) COLLATE "default",
format_name varchar(100) COLLATE "default",
page_size varchar(100) COLLATE "default",
character_size varchar(100) COLLATE "default",
param_pairs text COLLATE "default",
content text COLLATE "default",
print_num int4,
param_pairs2 text COLLATE "default",
content2 text COLLATE "default",
param_pairs3 text COLLATE "default",
content3 text COLLATE "default",
param_pairs4 text COLLATE "default",
content4 text COLLATE "default",
valid_state varchar(10) COLLATE "default",
last_operator varchar(50) COLLATE "default",
last_updatetime timestamp(6),
isdot char(1) COLLATE "default",
uuid varchar(50) COLLATE "default",
constraint PK_sys_print_format_new primary key (id)
);

create table IF NOT EXISTS sys_print_format_detail_new (
   tenancy_id           character varying(32) not null,
   id                   SERIAL not null,
   parent_id            int                  null,
   result_set_title     character varying(100) null,
   sql                  text                 null,
   is_test              character varying(10) null,
   parent_uuid          character varying(50) null
);


create table if not Exists sys_print_format_delphi (
   tenancy_id           character varying(32) not null,
   id                   SERIAL not null,
   model_name           character varying(100) null,
   class_identifier_code character varying(100) null,
   class_item_code      character varying(100) null,
   format_name          character varying(100) null,
   page_size            character varying(100) null,
   character_size       character varying(100) null,
   print_num            int                  null,
   param_pairs          text                 null,
   content              text                 null,
   param_pairs2         text                 null,
   content2             text                 null,
   valid_state          character varying(10) null,
   last_operator        character varying(50) null,
   last_updatetime      timestamp            null,
   uuid                 character varying(50) null,
   isdot                character varying(50) null,
   constraint PK_SYS_PRINT_FORMAT_DELPHI primary key (id)
);

create table if not Exists sys_print_format_detail_delphi (
   tenancy_id           character varying(32) not null,
   id                   SERIAL               not null,
   parent_id            int                  null,
   result_set_title     character varying(100) null,
   sql                  text                 null,
   is_test              character varying(10) null,
   parent_uuid          character varying(50) null,
	constraint PK_SYS_PRINT_FORMAT_DETAIL_DEPHI primary key (id)
);


create table if not exists pos_print_task (
tenancy_id 	varchar(32)  not null,
store_id 	int4,
task_id serial not null,
state int4 default 0,
print_name varchar(32) ,
print_address varchar(32) ,
print_parameter varchar(32) ,
templete_id int4,
params text ,
send_time timestamp(6),
print_time timestamp(6),
order_no int4 default 0,
print_times int4 default 0,
bak_print_name varchar(32) ,
bak_print_address varchar(32) ,
report_date date,
print_result varchar(256) ,
pos_num varchar(16) ,
opt_num varchar(50) ,
site_id int4,
area_id int4,
class_item_code varchar(100) ,
bak_print_parameter varchar(100) ,
print_num int4,
constraint pos_print_task_pkey primary key (task_id)
);


create table if not exists hq_printer_new (
tenancy_id varchar(32) collate "default" not null,
store_id int4 not null,
id  serial not null,
code varchar(30) collate "default",
name varchar(50) collate "default",
type varchar(10) collate "default",
ip_com varchar(50) collate "default",
baud_rate varchar(30) collate "default",
is_area varchar(10) collate "default",
spare_id int4,
kitchen_sign varchar(2) collate "default",
remark varchar(100) collate "default",
valid_state varchar(10) collate "default" not null,
last_operator varchar(30) collate "default" not null,
last_updatetime timestamp(6) not null,
site_id int4,
constraint pk_hq_printer_new primary key (id)
);


CREATE TABLE IF not EXISTS hq_printer_item (
tenancy_id varchar(32) COLLATE "default",
id SERIAL NOT NULL,
item_id int4,
print_id int4,
print_format_id int4,
constraint PK_hq_printer_item primary key (id)
);

CREATE TABLE if not EXISTS hq_printer_model (
tenancy_id varchar(32) COLLATE "default" NOT NULL,
id SERIAL NOT NULL,
printer_id int4,
is_enables varchar(10) COLLATE "default",
print_num int4,
is_immedistely_print varchar(10) COLLATE "default",
is_item varchar(10) COLLATE "default",
is_area varchar(10) COLLATE "default",
format_type varchar(30) COLLATE "default",
format_id int4,
constraint PK_hq_printer_model primary key (id)
);

CREATE TABLE IF not EXISTS hq_printer_table (
tenancy_id varchar(32) COLLATE "default",
id SERIAL NOT NULL,
printer_id int4,
table_id int4,
constraint PK_hq_printer_table primary key (id)
);


select p_addfield('hq_printer_new','is_lobby','varchar(30)');
select p_addfield('hq_printer_new','ctrl_type','varchar(30)');

CREATE TABLE if not exists "public"."cc_third_coupon" (
"id" SERIAL NOT NULL,
"tenancy_id" varchar(32) COLLATE "default",
"store_id" int4,
"coupon_buy_price" numeric(19,4),
"coupon_cancel_status" varchar(10) COLLATE "default",
"coupon_code" varchar(20) COLLATE "default",
"coupon_status_desc" varchar(100) COLLATE "default",
"coupon_use_time" timestamp(6),
"deal_begin_time" timestamp(6),
"deal_title" varchar(100) COLLATE "default",
"deal_value" numeric(19,4),
"verify_acct" varchar(100) COLLATE "default",
"verify_type" varchar(100) COLLATE "default",
"coupon_end_time" timestamp(6),
"deal_id" varchar(20) COLLATE "default",
"deal_price" numeric(19,4),
"min_consume" int4,
"count" int4,
"e_id" varchar(32) COLLATE "default",
"e_name" varchar(100) COLLATE "default",
"e_order_id" varchar(100) COLLATE "default",
"coupon_codes" varchar(255) COLLATE "default",
"poi_id" varchar(32) COLLATE "default",
"consume_time" timestamp(6),
"cancel_time" timestamp(6),
"channel" varchar(20) COLLATE "default",
"status" varchar(20) COLLATE "default",
"prepare_time" timestamp(6),
"consume_count" int4,
CONSTRAINT "cc_third_coupon_pkey" PRIMARY KEY ("id"),
CONSTRAINT "uniques_coupon_code" UNIQUE ("coupon_code")
);


select p_addfield('sys_payment_account_config','agent_num','varchar(100)');
select p_addfield('sys_payment_account_config','machine_num','varchar(100)');


select p_addfield('crm_activity_chz','rebate_type','varchar(2)');
select p_addfield('user_discount_authority','store_id','INT');


CREATE TABLE if not exists baidu_push_chanel (
"id" serial NOT NULL,
"type_id" int4,
"store_id" int4,
"chanel_id" varchar(100) COLLATE "default",
"mac_address" varchar(100) COLLATE "default",
CONSTRAINT "pk_baidu_push_chanel" PRIMARY KEY ("id")
);

select p_addfield('pos_payment_refund','status','int4');
select p_addfield('pos_payment_order','order_num_pre','varchar(100)');
select p_addfield('pos_payment_order','version','varchar(30)');
select p_addfield('pos_payment_order','channel','varchar(32)');
select p_addfield('pos_payment_order','shift','varchar(32)');
select p_addfield('pos_payment_order','report_date','varchar(32)');
select p_addfield('pos_payment_refund','version','varchar(30)');
select p_addfield('pos_payment_refund','service_type','varchar(10)');
select p_addfield('pos_payment_refund','type','int4');
select p_addfield('pos_payment_refund','channel','varchar(32)');
select p_addfield('pos_payment_refund','shift','varchar(32)');
select p_addfield('pos_payment_refund','report_date','varchar(32)');

CREATE TABLE  if not exists   "public"."pos_payment_cancel" (
"id" serial NOT NULL ,
"store_id" varchar(32) COLLATE "default",
"tenancy_id" varchar(32) COLLATE "default",
"order_num" varchar(100) COLLATE "default",
"trade_no" varchar(100) COLLATE "default",
"out_request_no" varchar(100) COLLATE "default",
"finish_time" timestamp(6),
"terminal_id" varchar(32) COLLATE "default",
"status" int4,
"version" varchar(30),
"report_date" varchar(50),
"shift" varchar(32),
"channel" varchar(32),
"service_type" varchar(10),
"type" int4,
CONSTRAINT "pos_payment_cancel_pkey" PRIMARY KEY ("id")
);


select p_addfield('cc_order_list','upload_tag','int4');

select p_addfield('cc_order_list','report_date','date');

select p_addfield('cc_order_item','upload_tag','int4');

select p_addfield('cc_order_item','report_date','date');





select p_addfield('cc_order_list','customer_address_id','int4');


select p_addfield('pos_third_payment_order','payment_type','character varying(16)');
select p_addfield('pos_third_payment_order','aid_order_num','character varying(64)');
select p_addfield('pos_third_payment_order','order_state','character varying(16)');


CREATE TABLE if not exists "public"."cc_order_list" (
"tenancy_id" varchar(32) COLLATE "default",
"id" serial NOT NULL,
"store_id" int4,
"customer_id" int4,
"ride_id" int4,
"order_type" varchar(10) COLLATE "default",
"order_code" varchar(50) COLLATE "default",
"bill_num" varchar(30) COLLATE "default",
"table_code" varchar(30) COLLATE "default",
"table_name" varchar(30) COLLATE "default",
"deinner_number" varchar(10) COLLATE "default",
"order_name" varchar(30) COLLATE "default",
"order_phone" varchar(100) COLLATE "default",
"consigner" varchar(30) COLLATE "default",
"consigner_phone" varchar(100) COLLATE "default",
"sex" varchar(10) COLLATE "default",
"chanel" varchar(30) COLLATE "default",
"taste_like" varchar(100) COLLATE "default",
"province" varchar(10) COLLATE "default",
"city" varchar(10) COLLATE "default",
"area" varchar(100) COLLATE "default",
"district_id" int4,
"address" varchar(300) COLLATE "default",
"total_money" numeric(19,4),
"actual_pay" numeric(19,4),
"meals_id" int4,
"remark" varchar(100) COLLATE "default",
"order_state" varchar(10) COLLATE "default",
"send_time" varchar(29) COLLATE "default",
"single_time" timestamp(6),
"receive_time" timestamp(6),
"response_time" timestamp(6),
"take_time" timestamp(6),
"receive_time_qd" timestamp(6),
"response_time_qd" timestamp(6),
"dispatch_time" timestamp(6),
"receive_time_dispatch" timestamp(6),
"response_time_dispatch" timestamp(6),
"distribution_time" timestamp(6),
"receive_time_distribution" timestamp(6),
"response_time_distribution" timestamp(6),
"finish_time" timestamp(6),
"receive_time_finish" timestamp(6),
"response_time_finish" timestamp(6),
"cancellation_time" timestamp(6),
"receive_time_cancellation" timestamp(6),
"response_time_cancellation" timestamp(6),
"entry_name" varchar(30) COLLATE "default",
"cancle_name" varchar(30) COLLATE "default",
"last_operator" varchar(30) COLLATE "default",
"last_updatetime" timestamp(6),
"service_id" int4,
"discountr_amount" numeric(19,4),
"discount_rate" numeric(19,4),
"integraloffset" numeric(19,4),
"discount_amount" numeric(19,4),
"discount_mode_id" int4,
"payment_state" varchar(10) COLLATE "default",
"is_online_payment" varchar(10) COLLATE "default",
"maling_amount" numeric(19,4),
"send_immediately" int4,
"third_order_code" varchar(100) COLLATE "default",
"need_invoice" int4,
"invoice_title" varchar(100) COLLATE "default",
"delivery_party" int4,
"longitude" varchar(100) COLLATE "default",
"latitude" varchar(100) COLLATE "default",
"shop_fee" numeric(19,4),
"third_order_state" varchar(10) COLLATE "default",
"order_state_desc" varchar(100) COLLATE "default",
"meal_costs" varchar(100) COLLATE "default",
"actual_amount" numeric(19,4),
"meituan_id" varchar(50) COLLATE "default",
"chanel_serial_number" varchar(30) COLLATE "default",
"serial_number" varchar(30) COLLATE "default",
"order_md5" varchar(200) COLLATE "default",
"openid" varchar(50) COLLATE "default",
"commission_rate" numeric(19,4),
"commission_amount" numeric(19,4),
"is_comment" int4,
"shop_real_amount" numeric(19,4),
"total_fees" numeric(19,4),
"platform_charge_amount" numeric(19,4),
"settlement_type" varchar(10) COLLATE "default",
"check_mode" varchar(10) COLLATE "default",
"discountk_amount" numeric(19,4),
"collect_more_ticket" numeric(19,4),
"more_coupon" numeric(19,4),
"free_amount" numeric(19,4),
"givi_amount" numeric(19,4),
"upload_tag" int4,
"report_date" date,
CONSTRAINT "cc_order_list_pkey" PRIMARY KEY ("id")
);

CREATE TABLE if not exists "public"."cc_order_item" (
"tenancy_id" varchar(32) COLLATE "default",
"id" serial NOT NULL,
"group_index" int4,
"order_code" varchar(50) COLLATE "default",
"item_id" int4,
"unit_id" int4,
"number" numeric(19,4),
"price" numeric(19,4),
"remark" varchar(100) COLLATE "default",
"is_gift" varchar(10) COLLATE "default",
"item_menu_id" int4,
"item_name" varchar(100) COLLATE "default",
"product_fee" numeric(19,4),
"share_amount" numeric(19,4),
"real_amount" numeric(19,4),
"discount_amount" numeric(19,4),
"single_discount_amount" numeric(19,4),
"discountr_amount" numeric(19,4),
"discount_state" numeric(19,4),
"discount_rate" numeric(19,4),
"discount_mode_id" int4,
"discount_price" numeric(19,4),
"method_money" numeric(19,4),
"costs" numeric(19,4),
"tag" varchar(30) COLLATE "default",
"store_id" int4,
"is_add_dish" varchar(10) COLLATE "default",
"is_commission" varchar(10) COLLATE "default",
"discountk_amount" numeric(19,4),
"free_amount" numeric(19,4),
"givi_amount" numeric(19,4),
"upload_tag" int4,
"report_date" date,
CONSTRAINT "cc_order_item_pkey" PRIMARY KEY ("id")
);



CREATE TABLE if not exists "public"."cc_order_repayment" (
"tenancy_id" varchar(32) COLLATE "default",
"id" serial NOT NULL,
"payment_id" int4,
"order_code" varchar(50) COLLATE "default",
"pay_money" numeric(19,4),
"pay_no" varchar(50) COLLATE "default",
"rexchange_rate" numeric(19,4),
"local_currency" numeric(19,4),
"third_bill_code" varchar(50) COLLATE "default",
"remark" varchar(100) COLLATE "default",
"store_id" int4,
"rate" numeric(19,4),
"platform_side_charge" numeric(19,4),
"fee" numeric(19,4),
"more_coupon" numeric(19,4),
"upload_tag" int4,
"report_date" date,
CONSTRAINT "cc_order_repayment_pkey" PRIMARY KEY ("id")
);

CREATE TABLE IF NOT EXISTS wx_member_card_rechargecard (
"id" serial NOT NULL,
"store_id" varchar(32) COLLATE "default",
"tenancy_id" varchar(32) COLLATE "default",
"order_num" varchar(100) COLLATE "default",
"pay_state" varchar(32) COLLATE "default",
"chanel" varchar(30),
"request_time" varchar(50),
"card_code" varchar(100),
"income" varchar(50),
CONSTRAINT "wx_member_card_rechargecard_pkey" PRIMARY KEY ("id")
);


select p_addfield('crm_info_send','sms_nums','INT');

select p_addfield('pos_payment_order','service_type','varchar(50)');

select p_dropfield('hq_legal_per','save_bank_code');
select p_dropfield('hq_legal_per','save_bank_name');
select p_dropfield('hq_legal_per','assist_different');

create table if not EXISTS hq_legal_per_organ_ref (
   tenancy_id           character varying(32) not null,
   id                   serial               not null,
   legal_per_id         INT4                 null,
   organ_id             INT4                 null,
   constraint PK_HQ_LEGAL_PER_ORGAN_REF primary key (id)
);


select p_addfield('hq_legal_per','tax_rate','numeric(19,4)');
select p_addfield('hq_legal_per','seller_address','varchar(100)');
select p_addfield('hq_legal_per','brand_english','varchar(100)');
select p_addfield('hq_legal_per','seller_number','varchar(20)');

select p_addfield('cc_order_item','share_amount','numeric(19,4)');
select p_addfield('cc_order_item','real_amount','numeric(19,4)');
select p_addfield('cc_order_item','discount_amount','numeric(19,4)');

select p_addfield('cc_order_item','single_discount_amount','numeric(19,4)');
select p_addfield('cc_order_item','discount_state','numeric(19,4)');

select p_addfield('cc_order_item','discount_rate','numeric(19,4)');
select p_addfield('cc_order_item','discount_mode_id','int4');

select p_addfield('cc_order_item','discount_price','numeric(19,4)');

select p_addfield('hq_legal_per','is_billing','varchar(2)');

select p_addfield('hq_legal_per','seller_name','varchar(100)');

select p_addfield('sys_payment_account_config','account_type','varchar(10)');

select p_addfield('sys_payment_account_config','account_number','varchar(50)');

select p_addfield('sys_payment_account_config','transaction_rate','numeric(19,4)');


select p_addfield('sys_payment_account_config','payment_account_id','int4');

select p_addfield('sys_payment_account_config','account_ref_id','int4');

select p_addfield('sys_payment_account_config','service_type1','varchar(10)');

select p_addfield('sys_payment_account_config','sign','varchar(50)');
select p_addfield('cc_order_item','method_money','numeric(19,4)');
select p_addfield('cc_order_item_details','store_id','int4');

select p_addfield('cc_order_item_details','upload_tag','int4');


select p_addfield('crm_card_trading_list','is_invoice','character varying(16)');
select p_addfield('crm_card_trading_list','payment_state','character varying(16)');
select p_addfield('crm_card_trading_list','recharge_state','character varying(16)');
select p_addfield('crm_card_trading_list','request_status','character varying(16)');
select p_addfield('crm_card_trading_list','request_code','character varying(32)');
select p_addfield('crm_card_trading_list','request_msg','character varying(256)');


select p_addfield('pos_third_payment_order','query_time','TIMESTAMP');
select p_addfield('pos_third_payment_order','query_count','int4');


select p_addfield('cc_order_item_details','report_date','date');


select p_addfield('cc_order_list','take_meal_number','varchar(50)');

select p_addfield('cc_third_organ_info','tenant_id','varchar(50)');

ALTER TABLE cc_order_list ALTER COLUMN upload_tag TYPE varchar(2);

ALTER TABLE cc_order_item ALTER COLUMN upload_tag TYPE varchar(2);

ALTER TABLE cc_order_repayment ALTER COLUMN upload_tag TYPE varchar(2);


create table if not EXISTS "hq_electronic_invoice_info" (
   tenancy_id           character varying(30) not null,
   id                   serial               not null,
   organ_id             int4                 null,
   invoice_flow_number  character varying(50) null,
   invoice_type         character varying(10) null,
   invoice_amount       decimal(19,4)        null,
   tax                  character varying(50) null,
   seller_name          character varying(100) null,
   seller_address       character varying(100) null,
   seller_phone         character varying(20) null,
   buyer_name           character varying(100) null,
   drawer               character varying(20) null,
   total_tax_amount     decimal(19,4)        null,
   tax_amount           decimal(19,4)        null,
   total_amount         decimal(19,4)        null,
   order_date           DATE                 null,
   invoice_state        character varying(10) null,
   invoice_time         TIMESTAMP            null,
   invoice_cancel_time  TIMESTAMP            null,
   open_invoice_number  int                  null,
   invoice_service_type character varying(50) null,
   invoice_code         character varying(12) null,
   invoice_number       character varying(10) null,
   cancle_person        character varying(20) null,
   original_invoice_code character varying(20) null,
   original_invoice_number character varying(10) null,
   tax_rate             decimal(19,4)        null,
   control_device_code  character varying(30) null,
   payee                character varying(20) null,
   review_person        character varying(20) null,
   order_code           character varying(32) null,
	 CONSTRAINT "hq_electronic_invoice_info_pkey" PRIMARY KEY ("id")
);

create table if not EXISTS "hq_electronic_invoice_details" (
   id                   serial               not null,
   electronic_id        int4                 null,
   invoice_type         character varying(30) null,
   name                 character varying(100) null,
   model                character varying(30) null,
   unit                 character varying(30) null,
   number               DECIMAL(19,6)        null,
   price                DECIMAL(19,6)        null,
   amount               DECIMAL(19,2)        null,
   tax_rate             DECIMAL(19,6)        null,
   tax_amount           DECIMAL(19,2)        null,
   sn_no                character varying(100) null,
	 CONSTRAINT "hq_electronic_invoice_details_pkey" PRIMARY KEY ("id")
);

select p_addfield('cc_order_list','shop_delivery_fee','numeric(19,4)');

select p_addfield('hq_service_fee_type','free_count','varchar(10)');




CREATE TABLE if not EXISTS hq_item_menu_classorder (
"id" serial    NOT NULL,
"menu_id" int4,
"class_id" int4,
"classorder" int4,
CONSTRAINT "pk_hq_item_menu_classorder" PRIMARY KEY ("id")
);


CREATE TABLE  if not EXISTS hq_item_menu_organ_task (
"tenancy_id" varchar(32) COLLATE "default" NOT NULL,
"id" serial NOT NULL,
"item_menu_id" int4 NOT NULL,
"store_id" int4 NOT NULL,
"running_state" varchar(10) COLLATE "default",
"start_date" date,
"end_date" date,
"start_person" varchar(100) COLLATE "default",
"start_time" timestamp(6),
"end_person" varchar(100) COLLATE "default",
"end_time" timestamp(6),
CONSTRAINT "pk_hq_item_menu_organ_task" PRIMARY KEY ("id")
);

select p_addfield('pos_bill_invoice','invoice_type','character varying(8)');


create table if not EXISTS pos_bill_service (
   tenancy_id           character varying(32) null,
   store_id             int                  null,
   id                   serial not null,
   bill_num             character varying(32) null,
   table_code           character varying(32) null,
   stable_code          character varying(32) null,
   service_id           int4                 null,
   service_type         character varying(16) null,
   taken_mode           character varying(16) null,
   service_scale        decimal(19,4)        null,
   service_amount       decimal(19,4)        null,
   service_count        decimal(19,4)        null,
   service_total        decimal(19,4)        null,
   service_rate         decimal(19,4)        null,
   upload_tag           int                  null default '0',
   constraint pk_pos_bill_service primary key (id)
);

select p_addfield('pos_bill','bill_taste','char(200)');
select p_addfield('pos_bill2','bill_taste','char(200)');
select p_addfield('scm_supply_in_history','transfer_fee','NUMERIC(19,4)');

select p_addfield('scm_supply_in_history','transfer_fee','NUMERIC(19,4)');

select p_addfield('scm_supply_in','transfer_fee','NUMERIC(19,4)');
select p_addfield('scm_supply_in','is_push','CHARACTER VARYING(10)');
select p_addfield('scm_supply_in_history','is_push','CHARACTER VARYING(10)');
select p_addfield('scm_supply_return','is_push','CHARACTER VARYING(10)');
select p_addfield('scm_supply_return_history','is_push','CHARACTER VARYING(10)');
select p_addfield('sys_modules','order_num','INTEGER');

select p_addfield('wx_member_card_rechargecard','need_invoice','VARCHAR(10)');
select p_addfield('pos_print','print_cont','int4');


                    
CREATE TABLE IF NOT EXISTS hq_lineup_dinner_org_list (
tenancy_id varchar(30) COLLATE "default",
id serial NOT NULL,
queue_id int4,
store_id int4,
wechat varchar(30) COLLATE "default",
phone varchar(11) COLLATE "default",
queue_name varchar(50) COLLATE "default",
lineup_time timestamp(6),
lineup_code varchar(30) COLLATE "default",
deinner_number varchar(10) COLLATE "default",
operator_code varchar(10) COLLATE "default",
state varchar(2) COLLATE "default",
advace_payment numeric(19,2),
lineup_orderno varchar(50) COLLATE "default",
lineup_completetime timestamp(6),
lineup_number int4,
CONSTRAINT pk_hq_lineup_dinner_org_list PRIMARY KEY ("id")
);

                    
CREATE TABLE if not exists hq_item_menu_organ_task_list (
"tenancy_id" varchar(32) COLLATE "default" NOT NULL,
"id" serial NOT NULL,
"item_menu_id" int4 NOT NULL,
"store_id" int4 NOT NULL,
"running_state" varchar(10) COLLATE "default",
"start_date" date,
"end_date" date,
"start_person" varchar(100) COLLATE "default",
"start_time" timestamp(6),
"end_person" varchar(100) COLLATE "default",
"end_time" timestamp(6),
CONSTRAINT "pk_hq_item_menu_organ_task_list" PRIMARY KEY ("id")
);

create table if not exists scm_template_ref 
(
   tenancy_id          varchar(32),
   id                  serial,
   bill_id             integer,
   store_id            integer,
   valid_state         varchar(10),
 
  constraint pk_scm_template_ref primary key (id)
);

select p_addfield('scm_material_in','default_warehouse_id','INTEGER');

select p_addfield('scm_material_in','default_supply_id','INTEGER');

create table if not EXISTS wx_credit_log (
    id 								serial NOT NULL,
	tenancy_id           character varying(32) not null,
	store_id             int not null,
	bill_code            character varying(50) not null UNIQUE,
	credit_code          character varying(50) null,
	openid               character varying(100) null,
	mobile               character varying(20) null,
	total_amount         decimal(19,4) null,
	chanel               character varying(20) null,
	status               int null, 
	updatetime           timestamp null,
	msg                  character varying(500) null,
	constraint PK_WX_CREDIT_LOG primary key (id)
);

select p_addfield('pos_bill_item','order_number','int4');
select p_addfield('pos_bill_item2','order_number','int4');
select p_addfield('cc_order_item_details','method_money','numeric(19,4)');

select p_addfield('pos_bill_item','item_remark_his','character varying(100)');
select p_addfield('pos_bill_item2','item_remark_his','character varying(100)');


CREATE TABLE if not EXISTS crm_operation_logs (
tenancy_id varchar(32) COLLATE "default",
id serial NOT NULL,
store_id int4,
model_id varchar(30),
model_name varchar(30) NOT NULL,
operate_info varchar(30),
remark varchar(500) COLLATE "default",
last_operator varchar(30) COLLATE "default",
last_updatetime timestamp(6),
CONSTRAINT "pk_crm_operation_logs" PRIMARY KEY ("id")
);


select p_addfield('crm_incorporation_gzlist','ogz_money','numeric(19,4)');

ALTER TABLE cc_order_list ALTER COLUMN invoice_title TYPE varchar(1000) ;



select p_addfield('crm_customer_info','name','varchar(300)');
select p_addfield('cc_order_list','order_name','varchar(300)');
select p_addfield('cc_order_list','consigner','varchar(300)');

select p_addfield('cc_third_organ_info','order_mode','varchar(10)');

CREATE TABLE if not exists cc_order_credit (
"tenancy_id" varchar(32) COLLATE "default",
"store_id" int4,
"id" serial,
"bill_num" varchar(20) COLLATE "default",
"order_code" varchar(20) COLLATE "default",
"report_date" date,
"type" varchar(100) COLLATE "default",
"amount" numeric(19,4),
"credit" numeric(19,4),
"bill_code" varchar(32) COLLATE "default",
"consume_before_credit" numeric(19,4),
"consume_after_credit" numeric(19,4),
"customer_code" varchar(100) COLLATE "default",
"customer_name" varchar(100) COLLATE "default",
"mobil" varchar(16) COLLATE "default",
"card_code" varchar(30) COLLATE "default",
"last_updatetime" timestamp(6),
"remark" varchar(500) COLLATE "default",
"upload_tag" int4 DEFAULT 0,
"request_state" varchar(10) COLLATE "default",
CONSTRAINT "pk_cc_order_credit" PRIMARY KEY ("id")
);

select p_addfield('pos_payment_order','buyer_user_id','varchar(50)');

select p_addfield('hq_water_utility','unit_price','numeric(19,4)');

select p_addfield('hq_water_utility','arithmetic','varchar(40)');


select p_addfield('boh_water_utility_record','money','numeric(19,4)');

select p_addfield('boh_water_utility_record','unit_price','numeric(19,4)');

select p_addfield('boh_water_utility_record','beginning','numeric(19,4)');




CREATE TABLE if not exists boh_water_utility_lastreading_record (
tenancy_id varchar(32) COLLATE "default",
id serial NOT NULL,
store_id int4,
water_utilit_id int4,
business_date date,
reading numeric(19,4),
beginning numeric(19,4),
unit_price numeric(19,4),
recprd_type varchar(30),
arithmetic varchar(40),
money numeric(19,4),
remark varchar(100) COLLATE "default",
last_operator varchar(30) COLLATE "default",
last_updatetime timestamp(6),
CONSTRAINT "pk_boh_water_utility_lastreading_record" PRIMARY KEY ("id")
);

CREATE TABLE if not exists crm_customer_blacklist 
(
tenancy_id varchar(32),
id serial not null,
customer_id int,
reason varchar(100),
last_operator varchar(30),
last_updatetime timestamp,
CONSTRAINT pk_crm_customer_blacklist PRIMARY KEY (id)
);


create table if not EXISTS crm_sftpupload_record (
   tenancy_id           character varying(32) null,
   id                   serial            not null,
   store_id            int4,
   operator             character varying(10) null,
   report_date            date                 null,
   operater_time        timestamp            null,
   constraint PK_CRM_SFTPUPLOAD_RECORD primary key (id)
);


CREATE TABLE if not EXISTS cc_order_item_retain (
"tenancy_id" varchar(32) COLLATE "default",
"id" serial,
"group_index" int4,
"order_code" varchar(50) COLLATE "default",
"item_id" int4,
"unit_id" int4,
"number" numeric(19,4),
"price" numeric(19,4),
"remark" varchar(100) COLLATE "default",
"is_gift" varchar(10) COLLATE "default",
"item_menu_id" int4,
"item_name" varchar(100) COLLATE "default",
"product_fee" numeric(19,4),
"share_amount" numeric(19,4) DEFAULT 0,
"real_amount" numeric(19,4),
"discount_amount" numeric(19,4) DEFAULT 0,
"single_discount_amount" numeric(19,4) DEFAULT 0,
"discountr_amount" numeric(19,4) DEFAULT 0,
"discount_state" numeric(19,4) DEFAULT 0,
"discount_rate" numeric(19,4) DEFAULT 1,
"discount_mode_id" int4,
"discount_price" numeric(19,4) DEFAULT 0,
"method_money" numeric(19,4) DEFAULT 0,
"costs" numeric(19,4) DEFAULT 0,
"tag" varchar(30) COLLATE "default",
"store_id" int4,
"is_add_dish" varchar(10) COLLATE "default",
"is_commission" varchar(10) COLLATE "default" DEFAULT 0,
"discountk_amount" numeric(19,4),
"free_amount" numeric(19,4),
"givi_amount" numeric(19,4),
"upload_tag" int4,
"report_date" date,
constraint PK_cc_order_item_retain primary key (id)
);



CREATE TABLE if not EXISTS cc_order_item_details_retain (
"tenancy_id" varchar(32) COLLATE "default",
"id" serial,
"group_index" int4,
"order_code" varchar(50) COLLATE "default",
"item_id" int4,
"unit_id" int4,
"number" numeric(19,4),
"price" numeric(19,4),
"remark" varchar(100) COLLATE "default",
"real_amount" numeric(19,4),
"discount_amount" numeric(19,4),
"single_discount_amount" numeric(19,4),
"discountr_amount" numeric(19,4),
"discount_state" numeric(19,4),
"discount_rate" numeric(19,4),
"discount_mode_id" numeric(19,4),
"product_fee" numeric(19,4),
"method_money" numeric(19,4),
"is_add_dish" varchar(10) COLLATE "default",
"is_itemgroup" varchar(19) COLLATE "default",
"item_group_id" int4,
"store_id" int4,
"upload_tag" int4,
"report_date" date,
constraint PK_cc_order_item_details_retai primary key (id)
);


select p_addfield('cc_order_list','merge_order_code','varchar(100)');


select p_addfield('crm_incorporation_gzlist','name','varchar(100)');
select p_addfield('crm_incorporation_gzlist','customer_name','varchar(30)');


create table if not EXISTS pos_finance_basedata_bad_record 
(
     id                 SERIAL               not null,
   tenancy_id           varchar(32)          not null,
   store_id             int4 not null,
   bill_num             varchar(20) not null,
   report_date          date null,
   bad_code             char(3) null,
   bad_message          text null,
   create_time          timestamp null,
   remark               text null,
   upload_tag           int4 null,
   constraint PK_pos_finance_basedata_bad_record primary key (id)
   
);


CREATE TABLE  if not exists wx_recharge_set
    (
        id serial NOT NULL,
        tenancy_id CHARACTER VARYING(20),
        czje NUMERIC(19,4),
        PRIMARY KEY (id)
    );


ALTER TABLE public.cc_third_organ_info
ALTER COLUMN delivery_fee TYPE varchar(30) ;

ALTER TABLE public.cc_order_discount
ALTER COLUMN discount_desc TYPE varchar(300) ;

ALTER TABLE public.crm_customer_address
ALTER COLUMN baidu_location TYPE varchar(300) ;


select p_addfield('hq_printer_new','printstate','INTEGER');
select p_addfield('hq_printer_new','statedesc','VARCHAR(256)');


CREATE TABLE IF NOT EXISTS pos_bill_member_regain
    (
        tenancy_id CHARACTER VARYING(32),
        store_id INTEGER,
        id SERIAL NOT NULL,
        bill_num CHARACTER VARYING(20),
        report_date DATE,
        type CHARACTER VARYING(100),
        amount NUMERIC(19,4),
        credit NUMERIC(19,4),
        card_code CHARACTER VARYING(30),
        mobil CHARACTER VARYING(30),
        last_updatetime TIMESTAMP(6) WITHOUT TIME ZONE,
        upload_tag INTEGER DEFAULT 0,
        remark CHARACTER VARYING(500),
        bill_code CHARACTER VARYING(32),
        request_state CHARACTER VARYING(8),
        customer_code CHARACTER VARYING(32),
        customer_name CHARACTER VARYING(100),
        consume_before_credit NUMERIC(19,4),
        consume_after_credit NUMERIC(19,4),
        regain_count int4,
        CONSTRAINT pk_pos_bill_member_regain PRIMARY KEY (id)
    );


select p_addfield('crm_activity_wxonline_list','credit_state','varchar(100)');

select p_addfield('hq_daycount_info','crm_sign','character varying(10)');


select p_addfield('cc_order_list','product_org_total_fee','numeric(19,4)');

select p_addfield('cc_order_list','package_box_fee','numeric(19,4)');


select p_addfield('cc_order_discount','store_id','int4');
select p_addfield('cc_order_discount','report_date','date');
select p_addfield('cc_order_discount','operator_time','timestamp(6)');

select p_addfield('hq_electronic_invoice_info','sfhfzd','varchar(50)');

CREATE TABLE if not exists crm_sms_account_details (
tenancy_id varchar(32) COLLATE "default",
id serial NOT NULL,
sms_platform_type  varchar(100),
sms_type  varchar(500),
sms_account varchar(100),
sms_pwd varchar(100),
sms_sign varchar(100),
sms_chanel varchar(50),
price numeric(19,2),
send_order int4,
valid_state  varchar(10) null,
remark varchar(100) COLLATE "default",
last_operator varchar(30) COLLATE "default",
last_updatetime timestamp(6),
CONSTRAINT "pk_crm_sms_account_details" PRIMARY KEY ("id")
);


select p_addfield('crm_info_send','sms_account','varchar(100)');

select p_addfield('crm_info_send','sms_platform_type','varchar(100)');


create table if not exists hq_payment_showtype(
"tenancy_id" varchar(32) collate "default" not null,
"id"  serial not null,
"store_id" int4 not null,
"payment_id" int4  not null,
"showtype_name" varchar(100) not null,
"showtype_code" varchar(50) not null,
"last_operator"       character varying(30) null,
"last_updatetime"      timestamp            null,
constraint pk_hq_payment_showtype primary key (id)
);


CREATE TABLE IF NOT EXISTS hq_item_category_class (
tenancy_id varchar(30) COLLATE "default",
id serial NOT NULL,
category_code varchar(30) COLLATE "default",
category_name varchar(200)  COLLATE "default",
category_father_id int4,
category_father_code varchar(30) COLLATE "default",
category_father_name varchar(200)  COLLATE "default",
valid_state  varchar(10) COLLATE "default" NOT NULL,
remark varchar(200),
last_operator varchar(30) COLLATE "default",
last_updatetime timestamp(6),
CONSTRAINT pk_hq_item_category_class PRIMARY KEY ("id")
);


CREATE TABLE IF NOT EXISTS hq_item_category_class_relation (
tenancy_id varchar(30) COLLATE "default",
id serial NOT NULL,
category_id int4,
item_id int4,
last_operator varchar(30) COLLATE "default",
last_updatetime timestamp(6),
CONSTRAINT pk_hq_item_category_class_relation PRIMARY KEY ("id")
);


select p_addfield('crm_activity','last_execution_time','timestamp(6)');
select p_addfield('crm_activity','execute_cycle','varchar(100)');
select p_addfield('crm_activity','executed_times','int4');

select p_addfield('hq_printer_model','devices_id','int4');


ALTER TABLE hq_item_menu_details ALTER starttime TYPE time(6);
ALTER TABLE hq_item_menu_details ALTER endtime TYPE time(6);

CREATE TABLE  if not exists wx_parameter
    (
        id serial NOT NULL,
        tenancy_id CHARACTER VARYING(20),
        store_id int,
        model_name varchar(255),
        para_name varchar(255),
        para_code varchar(255),
        para_value varchar(500),
        valid_state int,
        para_remark varchar(255),
        PRIMARY KEY (id)
    );
    
select p_addfield('pos_tablestate','table_open_num','int4'); 

create table if not exists hq_devices_datastate 
(
   devices_code         varchar(100)                   not null,
   refresh_flag         char(1)                        null,
   constraint PK_HQ_DEVICES_DATASTATE primary key  (devices_code)
);


select p_addfield('pos_tablestate','stable','varchar(50)'); 


CREATE TABLE  if not exists wx_reward_set_new
    (
        id SERIAL NOT NULL,
        tenancy_id varchar(200),
        ds_status varchar(200),
        ercode_url varchar(500),
        isuse varchar(10),
        is_emporstor varchar(20),   
        emporstor_id INTEGER,
        store_id INTEGER,
        sf_random_je varchar(10),
        je NUMERIC(19,4),
        wish_context varchar(200),
        PRIMARY KEY (id)
    );
    
    
CREATE TABLE  if not exists wx_reward_wish  
    (
        id serial NOT NULL,
        tenancy_id CHARACTER VARYING(20),
				wish_info CHARACTER VARYING(200),
        PRIMARY KEY (id)
    );

CREATE TABLE  if not exists wx_reward_record  
    (
		id serial NOT NULL,
		tenancy_id CHARACTER VARYING(20),
		is_emporstor   CHARACTER VARYING(20),  
		emporstor_id   int ,      
		dssj   TIMESTAMP(6) WITHOUT TIME ZONE, 
		dsje   NUMERIC(19,4),   
		dsly   CHARACTER VARYING(200), 
		order_code   CHARACTER VARYING(200), 
		pay_state    CHARACTER VARYING(20), 
		store_id  int,
		PRIMARY KEY (id)
    );
    
select p_addfield('pos_bill_invoice','opt_count','int4');  
 
 
CREATE TABLE if not EXISTS crm_sms_recharge_detials (
tenancy_id varchar(32) COLLATE "default",
id serial NOT NULL,
telephone varchar(50),
sms_type  varchar(50),
recharge_nums numeric(19,2),
last_operator varchar(30) COLLATE "default",
last_updatetime timestamp(6),
CONSTRAINT "pk_crm_sms_recharge_detials" PRIMARY KEY ("id")
);


select p_addfield('pos_bill','fictitious_table','varchar(50)');  
select p_addfield('pos_bill2','fictitious_table','varchar(50)');  


CREATE TABLE IF NOT EXISTS crm_third_code_keys (
id varchar(100) COLLATE "default" NOT NULL,
key_time timestamp(6) DEFAULT now(),
CONSTRAINT pk_crm_third_code_keys PRIMARY KEY (id)
);


CREATE TABLE IF NOT EXISTS crm_third_code_qx_keys (
id varchar(100) COLLATE "default" NOT NULL,
key_time timestamp(6) DEFAULT now(),
CONSTRAINT pk_crm_third_code_qx_keys PRIMARY KEY (id)
);


CREATE TABLE IF NOT EXISTS crm_third_code_jf_keys (
id varchar(100) COLLATE "default" NOT NULL,
key_time timestamp(6) DEFAULT now(),
CONSTRAINT pk_crm_third_code_jf_keys PRIMARY KEY (id)
);


CREATE TABLE IF NOT EXISTS crm_third_code_jf_qx_keys (
id varchar(100) COLLATE "default" NOT NULL,
key_time timestamp(6) DEFAULT now(),
CONSTRAINT pk_crm_third_code_jf_qx_keys PRIMARY KEY (id)
);



CREATE TABLE if not EXISTS hq_lineup_dinner_org_list (
"tenancy_id" varchar(30) COLLATE "default",
"id" serial NOT NULL,
"queue_id" int4,
"store_id" int4,
"wechat" varchar(30) COLLATE "default",
"phone" varchar(11) COLLATE "default",
"queue_name" varchar(50) COLLATE "default",
"lineup_time" timestamp(6),
"lineup_code" varchar(30) COLLATE "default",
"deinner_number" varchar(10) COLLATE "default",
"operator_code" varchar(10) COLLATE "default",
"state" varchar(2) COLLATE "default",
"advace_payment" numeric(19,2),
"lineup_orderno" varchar(50) COLLATE "default",
"lineup_completetime" timestamp(6),
"lineup_number" int4,
"queue_number" int4,
CONSTRAINT "pk_hq_lineup_dinner_org_list" PRIMARY KEY ("id")
);


select p_addfield('hq_lineup_dinner_org_list','queue_number','int4'); 


CREATE TABLE IF NOT EXISTS wx_organ_category_details (
tenancy_id varchar(32) COLLATE "default",
id serial NOT NULL,
father_id  int4,
category_code varchar(50),
category_name varchar(100),
category_level int4,
category_param varchar(300),
valid_state varchar(10),
last_operator varchar(30) COLLATE "default",
last_updatetime timestamp(6),
CONSTRAINT "pk_wx_organ_category_details" PRIMARY KEY ("id")
);

CREATE TABLE IF NOT EXISTS  wx_organ_details (
"tenancy_id" varchar(30) COLLATE "default",
"id" serial NOT NULL,
"store_id" int4 NOT NULL,
"business_name" varchar(100) COLLATE "default",
"branch_name" varchar(100) COLLATE "default",
"brand_name" varchar(100) COLLATE "default",
"province" varchar(80) COLLATE "default",
"city" varchar(60) COLLATE "default",
"district" varchar(60) COLLATE "default",
"address" varchar(60) COLLATE "default",
"categories" varchar(60) COLLATE "default",
"offset_type" int4,
"longitude" numeric(19,4),
"latitude" numeric(19,4),
"telephone" varchar(60) COLLATE "default",
"recommend" varchar(500) COLLATE "default",
"special" varchar(300) COLLATE "default",
"start_time" varchar(30) COLLATE "default",
"end_time" varchar(30) COLLATE "default",
"avg_price" numeric(19,4),
"introduction" varchar(500) COLLATE "default",
"poi_id" varchar(100) COLLATE "default",
"image_url" text COLLATE "default",
"reason" varchar(500) COLLATE "default",
"valid_state" varchar(10) COLLATE "default",
"synchro_state" int4,
"success_state" int4,
"back_url" varchar(200) COLLATE "default",
"last_operator" varchar(50) COLLATE "default",
"last_updatetime" timestamp(6),
CONSTRAINT "pk_wx_organ_details" PRIMARY KEY ("id")
);


CREATE TABLE IF NOT EXISTS wx_card_register (
"tenancy_id" varchar(30) COLLATE "default",
"id" serial NOT NULL,
"card_class_id" int4 NOT NULL,
"card_type" varchar(100) COLLATE "default",
"brand_name" varchar(200) COLLATE "default",
"logo_url" varchar(200) COLLATE "default",
"background_pic_url" varchar(200) COLLATE "default",
"code_type" varchar(100) COLLATE "default",
"color" varchar(60) COLLATE "default",
"notice" varchar(100) COLLATE "default",
"card_title" varchar(100) COLLATE "default",
"supply_bonus" varchar(20) COLLATE "default",
"supply_bonus_info" varchar(100) COLLATE "default",
"supply_balance" varchar(60) COLLATE "default",
"supply_balance_info" varchar(100) COLLATE "default",
"supply_level" varchar(60) COLLATE "default",
"supply_level_info" varchar(100) COLLATE "default",
"supply_coupon" varchar(60) COLLATE "default",
"supply_coupon_info" varchar(100) COLLATE "default",
"supply_pay" varchar(60) COLLATE "default",
"supply_pay_info" varchar(100) COLLATE "default",
"supply_recharge" varchar(60) COLLATE "default",
"supply_recharge_info" varchar(100) COLLATE "default",
"supply_rule" varchar(60) COLLATE "default",
"supply_rule_info" varchar(100) COLLATE "default",
"supply_card_detials" varchar(60) COLLATE "default",
"supply_card_detials_info" varchar(100) COLLATE "default",
"supply_personal" varchar(60) COLLATE "default",
"supply_personal_info" varchar(100) COLLATE "default",
"supply_storelist" varchar(60) COLLATE "default",
"supply_storelist_info" varchar(100) COLLATE "default",
"supply_publicnum" varchar(60) COLLATE "default",
"supply_publicnum_info" varchar(100) COLLATE "default",
"valid_state" varchar(10) COLLATE "default",
"synchro_state" int4,
"synchro_date" timestamp(6),
"success_state" int4,
"success_date" timestamp(6),
"wx_card_id" varchar(200) COLLATE "default",
"reason" text ,
"remark" text,
"last_operator" varchar(50) COLLATE "default",
"last_updatetime" timestamp(6),
CONSTRAINT "pk_wx_card_register" PRIMARY KEY ("id")
);


ALTER TABLE public.cc_order_discount ALTER COLUMN discount_type TYPE varchar(30) ;


select p_addfield('pos_bill','recover_count','int4'); 

select p_addfield('hq_devices','is_site','varchar(10)'); 



select p_addfield('pos_bill_invoice','opt_count','int4'); 
select p_addfield('pos_bill','recover_count','int4'); 
select p_addfield('pos_bill2','recover_count','int4'); 

create table IF NOT EXISTS employee_third_link (
   tenancy_id           character varying(32) null,
   store_id             int                  null,
   id                   SERIAL not null,
   employee_id          int                  null,
   third_employee_id    character varying(32) null,
   token                character varying(100) null,
   constraint PK_EMPLOYEE_THIRD_LINK primary key (id)
);


select p_addfield('wx_config','shop_name','varchar(100)');

select p_addfield('wx_parameter','para_type','varchar(100)');

select p_addfield('wx_auto_reply','media_id','varchar(100)');

CREATE TABLE  if not exists wx_material_pic  
    (
    id serial NOT NULL,
    tenancy_id CHARACTER VARYING(20),
		pic_path CHARACTER VARYING(200),
		pic_wx_path CHARACTER VARYING(200),
		pic_name CHARACTER VARYING(200),
		pic_thumb_media_id CHARACTER VARYING(255),
		type CHARACTER VARYING(20),  	
		author CHARACTER VARYING(20),   
    title CHARACTER VARYING(100),   
		digest CHARACTER VARYING(255), 	
		content_source_url CHARACTER VARYING(255),  
		content_url CHARACTER VARYING(255),
    content TEXT,  					
    show_cover_pic INTEGER,  
		ordernum INTEGER,  
		cover_media_id	CHARACTER VARYING(50),	
		update_date	TIMESTAMP(6) WITHOUT TIME ZONE,	
    PRIMARY KEY (id)
    );

CREATE TABLE  if not exists wx_material_send
    (
		id serial NOT NULL,
		tenancy_id CHARACTER VARYING(20),	
		msg_id CHARACTER VARYING(32),  
		material_id   character varying(255),  
		material_type CHARACTER VARYING(20),	
		status   int, 
		status_msg   CHARACTER VARYING(32), 
		send_time	TIMESTAMP(6) WITHOUT TIME ZONE,
		group_id	int,  
		contents		text,	
		sent_count	int,	
		error_count	int,	
		create_date   TIMESTAMP(6) WITHOUT TIME ZONE, 
		del_flag int,     
		PRIMARY KEY (id)
    );

select p_addfield('wx_member','blacklist','int4');
select p_addfield('wx_member','remark','CHARACTER VARYING(200)');

CREATE TABLE  if not exists wx_person_midgroup
    (
       id serial NOT NULL,
       tenancy_id CHARACTER VARYING(20),
			 group_id int,
			 openid CHARACTER VARYING(100),
       PRIMARY KEY (id)
    );
CREATE TABLE  if not exists wx_person_group  
    (
        id serial NOT NULL,
        tenancy_id CHARACTER VARYING(20),
				group_name CHARACTER VARYING(200),
				group_id int,
        PRIMARY KEY (id)
    );    

alter table crm_coupons_class ALTER column remark  type varchar(1000) COLLATE "default";


select p_addfield('pos_bill_invoice','bill_state','varchar(10)');


select p_addfield('pos_bill_invoice','copy_bill_num','varchar(20)');


CREATE TABLE if not exists "public"."pos_bill_invoice_regain" (
"tenancy_id" varchar(32) COLLATE "default",
"store_id" int4,
"id" serial not null,
"report_date" date,
"pos_num" varchar(10) COLLATE "default",
"opt_num" varchar(20) COLLATE "default",
"bill_num" varchar(20) COLLATE "default",
"invoice_num" varchar(30) COLLATE "default",
"invoice_amount" numeric(19,4),
"last_updatetime" timestamp(6),
"upload_tag" int4 DEFAULT 0,
"remark" varchar(500) COLLATE "default",
"invoice_type" varchar(8) COLLATE "default",
"invoice_count" int4,
"bill_state" varchar(10) COLLATE "default",
CONSTRAINT "pk_pos_bill_invoice_regain" PRIMARY KEY ("id")
);

select p_addfield('organ','poi_id','varchar(32)');

CREATE TABLE IF NOT EXISTS crm_activity_dbjf_level (
"tenancy_id" varchar(32) COLLATE "default",
"id" serial NOT NULL,
"activity_id" int4,
"level" int4,
"levelname" varchar(100),
"multiple" int4,
"last_operator" varchar(30) COLLATE "default",
"last_updatetime" timestamp(6),
CONSTRAINT "pk_crm_activity_dbjf_level" PRIMARY KEY ("id")
);

CREATE TABLE IF NOT EXISTS crm_activity_dbjf_times (
"tenancy_id" varchar(32) COLLATE "default",
"id" serial NOT NULL,
"activity_id" int4,
"time_type" int4,
"start_date" date,
"end_date" date,
"use_cycle" VARCHAR(100),
"appoint_date"  VARCHAR(200),
"last_operator" varchar(30) COLLATE "default",
"last_updatetime" timestamp(6),
CONSTRAINT "pk_crm_activity_dbjf_times" PRIMARY KEY ("id")
);

select p_addfield('crm_activity','time_type','int4');
select p_addfield('boh_water_utility_record','arithmetic','varchar(40)');

select p_addfield('boh_water_utility_record','trading_volume','numeric(19,4)');
select p_addfield('boh_water_utility_lastreading_record','trading_volume','numeric(19,4)');

select p_addfield('cc_third_check_mode','channel','numeric(19,4)');

alter table "cc_third_check_mode" alter  COLUMN  channel  type varchar(30) ;
alter table "cc_order_credit" alter  COLUMN  upload_tag  type varchar(10) ;
alter table "cc_order_credit" alter  COLUMN  order_code  type varchar(30) ;
alter table "cc_order_credit" alter  COLUMN  bill_num  type varchar(30) ;


select p_addfield('crm_activity','point_by_recharge','int4');
select p_addfield('pos_bill_invoice','url_content','text');

select p_addfield('organ','poi_id','character varying(32)');

CREATE TABLE  if not exists wx_recharge_set_config
    (
        id serial NOT NULL,
        tenancy_id CHARACTER VARYING(20),
        word_name CHARACTER VARYING(100),
        words CHARACTER VARYING(200),
        word_status int4,
        PRIMARY KEY (id)
    );


CREATE TABLE if not exists "public"."eh_order_info" (
"order_no" varchar(32) COLLATE "default",
"order_type" int4,
"tenancy_id" varchar(32) COLLATE "default",
"store_id" int4,
"table_code" varchar(32) COLLATE "default",
"total_amount" numeric(19,4),
"sale_amount" numeric(19,4),
"actual_amount" numeric(19,4),
"replenish_amount" numeric(19,4),
"is_use_bulkcoupon" int4,
"bulk_coupon" varchar(32) COLLATE "default",
"is_use_integral" int4,
"integral" int4,
"integral_amount" numeric(19,4),
"is_use_coupon" int4,
"coupon_code" varchar(32) COLLATE "default",
"coupon_amount" numeric(19,4) DEFAULT 0,
"is_use_memcard" int4,
"mem_cardnum" varchar(32) COLLATE "default",
"mem_cardpayamount" numeric(19,4),
"is_need_thirdpay" int4,
"payamount" numeric(19,4),
"paytype" varchar(32) COLLATE "default",
"mem_no" int4,
"mem_level" int4,
"mem_discount" numeric(19,4),
"mem_name" varchar(32) COLLATE "default",
"mem_phone" varchar(32) COLLATE "default",
"status" int4,
"remark" varchar(1024) COLLATE "default",
"id" int4 NOT NULL,
"operate_op" int4,
"operate_name" varchar(32) COLLATE "default",
"symbol" int4 DEFAULT 1,
"is_use_discount" int4,
"bulk_coupon_amount" numeric(19,4),
"trading_time" date,
"operate_date" timestamp(6),
"billnum" varchar(32) COLLATE "default",
"batchnum" varchar(32) COLLATE "default",
"serialnum" varchar(32) COLLATE "default",
"integral_code" varchar(32) COLLATE "default",
"mem_code" varchar(32) COLLATE "default"
);


CREATE TABLE if not exists "public"."eh_order_detail" (
"order_id" int4,
"order_no" varchar(32) COLLATE "default",
"order_type" int4,
"trading_type" int4,
"pay_type" int4,
"trading_time" date,
"pay_no" int4,
"status" int4,
"mem_no" int4,
"remark" varchar(1024) COLLATE "default",
"id" int4 NOT NULL,
"operate_op" int4,
"operate_name" varchar(32) COLLATE "default",
"symbol" int4 DEFAULT 1,
"tenancy_id" varchar(32) COLLATE "default",
"store_id" int4,
"pay_amount" numeric(19,4),
"operate_date" timestamp(6)
);

create table if not exists mq_transfer_logs (
"tenancy_id" varchar(32) collate "default" not null,
"store_id" int4 not null,
"id"  serial not null,
"transfer_type" varchar(30) collate "default",
"url" varchar(50) collate "default",
"file_size"  varchar(50) collate "default",
"block_size" int4,
"remark" TEXT,
"last_updatetime" timestamp(6) not null,
constraint pk_mq_transfer_logs primary key (id)
);


create table if not exists quick_pass_log (
		tenancy_id           character varying(32) null ,
		store_id             int                  null,
		id                   SERIAL not null,
		customer_id						int                  null,
		report_date						TIMESTAMP(6) WITHOUT TIME ZONE,
		shift_id							int                  null,
		pos_num								character varying(32)null,
		opt_num								character varying(32)null,
		bill_num							character varying(32) null,
		table_code						character varying(32) null,
		sale_mode							character varying(32) null,
		isprint_bill					character varying(32) null,
		is_invoice						character varying(32) null,
		number								character varying(32) null,
		phone									character varying(32) null,
		bill_code							character varying(32) null,
		amount								NUMERIC(19,4),   
		is_online_payment			character varying(32) NULL,
		payment_amount 				NUMERIC(19,4),
		difference 						NUMERIC(19,4),
		cont 									int  null, 
		reason_id 						character 	varying(32) NULL,
		state 								int   null,
		currency_amount 			NUMERIC(19,4),
		jzid 								  int   null,
   constraint PK_QUICK_PASS_LOG primary key (id)
);

select p_addfield('user_discount_authority','discount_type','varchar(2)');
select p_addfield('cc_third_organ_info','item_id','int4');
select p_addfield('cc_third_organ_info','unit_id','int4');



select p_addfield('organ','image3','text');
select p_addfield('crm_customer_info','customer_lable','varchar(100)');
select p_addfield('crm_customer_card','activation_store_his','int4');
select p_addfield('dynamic_code','third_bill_code','varchar(30)');
select p_addfield('wx_config','wx_api_ticket','VARCHAR(150)');

select p_addfield('wx_config','wx_api_ticket_time','VARCHAR(100)');
select p_addfield('pos_payment_order','invoice_amount','DECIMAL(19,4)');
select p_addfield('pos_payment_order','card_balance','DECIMAL(19,4)');
select p_addfield('pos_payment_order','trade_type','varchar(50)');
select p_addfield('pos_payment_order','coupon_fee','DECIMAL(19,4)');
select p_addfield('pos_payment_order','point_amount','DECIMAL(19,4)');
select p_addfield('pos_payment_refund','coupon_refund_fee','DECIMAL(19,4)');
select p_addfield('pos_payment_refund','out_trade_no','character varying(100)');
select p_addfield('pos_payment_refund','settlement_refund_fee','DECIMAL(19,4)');
select p_addfield('pos_payment_refund','cash_refund_fee','DECIMAL(19,4)');
select p_addfield('pos_payment_refund','third_request_no','character varying(100)');
select p_addfield('pos_payment_refund','third_request_no','character varying(100)');
select p_addfield('pos_payment_cancel','action','character varying(100)');
select p_addfield('pos_payment_cancel','out_trade_no','character varying(100)');



CREATE TABLE IF NOT EXISTS POS_PAYMENT_ALIPAY_DISCOUNT (
	tenancy_id CHARACTER VARYING (32) NULL,
	ID serial NOT NULL,
	organ_id int4 NULL,
	pos_payment_id int4 NULL,
	discount_price VARCHAR (10) NULL,
	discuont_title VARCHAR (32) NULL,
	discount_type VARCHAR (10) NULL,
	discount_ratio NUMERIC (19, 4) NULL,
	pos_payment_full NUMERIC (19, 4) NULL,
	pos_payment_cut NUMERIC (19, 4) NULL,
	ticket_threshold NUMERIC (19, 4) NULL,
	CONSTRAINT PK_POS_PAYMENT_ALIPAY_DISCOUNT PRIMARY KEY (ID)
);

create table if not exists pos_payment_flow (
   id                   serial               not null,
   order_num            character varying(100) null,
   store_id             int4                 null,
   tenancy_id           varchar(50)                 null,
   type                 character varying(10) null,
   fund_channel         character varying(50) null,
   amount               DECIMAL(19,4)        null,
   real_amount          DECIMAL(19,4)        null,
   out_trade_no         character varying(100) null,
   out_request_no       character varying(32) null,
   trade_mark           character varying(10) null,
	 constraint PK_POS_PAYMENT_FLOW primary key (id)
);

select p_addfield('pos_payment_refund','trade_time','timestamptz');
select p_addfield('pos_payment_refund','bill_num','varchar(100)');
select p_addfield('pos_payment_refund','last_updatetime','timestamptz');
select p_addfield('pos_payment_cancel','trade_date','date');
select p_addfield('pos_payment_cancel','trade_time','timestamptz');
select p_addfield('pos_payment_cancel','bill_num','varchar(100)');
select p_addfield('pos_payment_cancel','last_updatetime','timestamptz');
select p_addfield('pos_payment_order','trade_date','date');
select p_addfield('pos_payment_order','trade_time','timestamptz');
select p_addfield('pos_payment_order','bill_num','varchar(100)');
select p_addfield('pos_payment_order','last_updatetime','timestamptz');
select p_addfield('hq_item_info','to_offer','int4 default 1');
select p_addfield('user_discount_authority','discount_type','varchar(2)');

create table if not exists mq_transfer_logs (
"tenancy_id" varchar(32) collate "default" not null,
"store_id" int4 not null,
"id"  serial not null,
"transfer_type" varchar(30) collate "default",
"url" varchar(50) collate "default",
"file_size"  varchar(50) collate "default",
"block_size" int4,
"remark" TEXT,
"last_updatetime" timestamp(6) not null,
constraint pk_mq_transfer_logs primary key (id)
);

select p_addfield('payment_way','bank_rate','varchar(50)');
select p_addfield('crm_coupons_class','check_only','int4 default 1');
select p_addfield('crm_coupons_class','help_memory_code','varchar(20)');
select p_addfield('crm_coupons_class','wx_color','varchar(20)');
select p_addfield('crm_coupons_class','syn_status','int4 default 0');
select p_addfield('crm_coupons_class','syn_time','timestamp(6)');
select p_addfield('crm_coupons_class','syn_fail_reason','varchar(500)');
select p_addfield('crm_coupons_class','create_time','timestamp(6)');
select p_addfield('crm_coupons_class','audit_status','int4 default 0');
select p_addfield('crm_coupons_class','audit_time','timestamp(6)');
select p_addfield('crm_coupons_class','audit_fail_reason','varchar(50)');

CREATE TABLE  if not exists wx_vote_info
    (
        id serial NOT NULL,
        tenancy_id CHARACTER VARYING(20),
        vote_name CHARACTER VARYING(100),
        end_time timestamp(6),
        vote_status int4,
        create_time timestamp(6),
        PRIMARY KEY (id)
    );

CREATE TABLE  if not exists wx_vote_note
    (
        id serial NOT NULL,
        tenancy_id CHARACTER VARYING(20),
        vote_note CHARACTER VARYING(100),
        vote_type CHARACTER VARYING(20),
        vote_id int4,
        PRIMARY KEY (id)
    );

CREATE TABLE  if not exists wx_vote_detail
    (
        id serial NOT NULL,
        tenancy_id CHARACTER VARYING(20),
        option CHARACTER VARYING(200),
        pic_path CHARACTER VARYING(200),
        vote_id	int4,
        note_id	int4,
        PRIMARY KEY (id)
    );
	
CREATE TABLE  if not exists wx_vote_member
    (
        id serial NOT NULL,
        tenancy_id CHARACTER VARYING(20),
        vote_id	int4,
        note_id	int4,
        detail_id int4,
        openid CHARACTER VARYING(200),
        create_time timestamp(6),
        PRIMARY KEY (id)
    );
select p_addfield('wx_recharge_set_config','recharge_status','int default 0');

CREATE TABLE  if not exists wx_word_set
    (
        id serial NOT NULL,
        tenancy_id CHARACTER VARYING(20),
        word_name CHARACTER VARYING(500),
        word_type CHARACTER VARYING(200),
        status int4,
        PRIMARY KEY (id)
    );
	
select p_addfield('wx_message_record','is_collection','int4');
select p_addfield('wx_message_record','keytype','CHARACTER VARYING(50)');
select p_addfield('wx_message_record','msgflag','CHARACTER VARYING(50)');
select p_addfield('wx_message_record','pic_id','int4');
select p_addfield('wx_message_record','pic_path','CHARACTER VARYING(200)');
select p_addfield('wx_message_record','pic_title','CHARACTER VARYING(200)');
select p_addfield('wx_message_record','pic_digest','CHARACTER VARYING(200)');
select p_addfield('crm_customer_card','total_recharge_money','numeric (19,4)');

CREATE TABLE IF NOT EXISTS crm_membership_group (
"tenancy_id" varchar(32) COLLATE "default",
"id" serial NOT NULL,
"group_name" VARCHAR(150),
"create_time" timestamp(6),
"refresh_time" timestamp(6),
"use_state" varchar(30),
"usage_count" varchar(30),
"valid_state"  varchar(10) COLLATE "default" NOT NULL,
"members_count"  int4,
"sql_text" TEXT,
"last_operator" varchar(30) COLLATE "default",
"last_updatetime" timestamp(6),
CONSTRAINT "pk_crm_membership_group" PRIMARY KEY ("id")
);

CREATE TABLE IF NOT EXISTS crm_membership_group_sql_conditions (
"id" serial NOT NULL,
"membership_group_id" int4,
"param_type" varchar(100),
"tb_name" varchar(200),             
"tb_field1" varchar(100),
"tb_field_type1" varchar(100),
"tb_field_arithmetic1" varchar(100),
"tb_value1" text,            
"tb_value2" text,
"tb_field_time" varchar(100),
"tb_field_time_type" varchar(100),
"tb_field_time_arithmetic" varchar(100),
"tb_time1" varchar(10),              
"tb_time2" varchar(10),            
CONSTRAINT "pk_crm_membership_group_sql_conditions" PRIMARY KEY ("id")
);


CREATE TABLE IF NOT EXISTS crm_membership_group_function_relation (
"id" serial NOT NULL,
"membership_group_id" int4,
"function_type" VARCHAR(150),
"function_id" int4,
"valid_state" VARCHAR(10),
"last_operator" varchar(30) COLLATE "default",
"last_updatetime" timestamp(6),
CONSTRAINT "pk_crm_membership_group_function_relation" PRIMARY KEY ("id")
);

CREATE TABLE IF NOT EXISTS hq_item_taste_details (
"id" serial NOT NULL,
"item_id" int4,
"taste_id" int4,
"taste_code" VARCHAR(150),
"taste_name" VARCHAR(150),
"last_operator" varchar(30) COLLATE "default",
"last_updatetime" timestamp(6),
CONSTRAINT "pk_hq_item_taste_details" PRIMARY KEY ("id")
);



DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'pos_payment_order' and column_name='invoice_amount';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE pos_payment_order ADD COLUMN invoice_amount DECIMAL(19,4)';
ELSE RAISE NOTICE 'table pos_payment_order column invoice_amount already exists in this table ';
END IF; 
END$DO$; 


DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'pos_payment_order' and column_name='card_balance';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE pos_payment_order ADD COLUMN card_balance DECIMAL(19,4)';
ELSE RAISE NOTICE 'table pos_payment_order column card_balance already exists in this table ';
END IF; 
END$DO$; 



DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'pos_payment_order' and column_name='trade_type';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE pos_payment_order ADD COLUMN trade_type varchar(50)';
ELSE RAISE NOTICE 'table pos_payment_order column trade_type already exists in this table ';
END IF; 
END$DO$; 


DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'pos_payment_order' and column_name='coupon_fee';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE pos_payment_order ADD COLUMN coupon_fee DECIMAL(19,4)';
ELSE RAISE NOTICE 'table pos_payment_order column coupon_fee already exists in this table ';
END IF; 
END$DO$; 


DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'pos_payment_order' and column_name='point_amount';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE pos_payment_order ADD COLUMN point_amount DECIMAL(19,4)';
ELSE RAISE NOTICE 'table pos_payment_order column point_amount already exists in this table ';
END IF; 
END$DO$; 



DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'pos_payment_refund' and column_name='coupon_refund_fee';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE pos_payment_refund ADD COLUMN coupon_refund_fee DECIMAL(19,4)';
ELSE RAISE NOTICE 'table pos_payment_refund column coupon_refund_fee already exists in this table ';
END IF; 
END$DO$; 



DO $DO$
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'pos_payment_refund' and column_name='out_trade_no';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE pos_payment_refund ADD COLUMN out_trade_no character varying(100)';
ELSE RAISE NOTICE 'table pos_payment_refund column out_trade_no already exists in this table ';
END IF; 
END$DO$; 



DO $DO$
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'pos_payment_refund' and column_name='settlement_refund_fee';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE pos_payment_refund ADD COLUMN settlement_refund_fee DECIMAL(19,4)';
ELSE RAISE NOTICE 'table pos_payment_refund column settlement_refund_fee already exists in this table ';
END IF; 
END$DO$; 


DO $DO$
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'pos_payment_refund' and column_name='cash_refund_fee';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE pos_payment_refund ADD COLUMN cash_refund_fee DECIMAL(19,4)';
ELSE RAISE NOTICE 'table pos_payment_refund column cash_refund_fee already exists in this table ';
END IF; 
END$DO$; 



DO $DO$
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'pos_payment_refund' and column_name='third_request_no';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE pos_payment_refund ADD COLUMN third_request_no character varying(100)';
ELSE RAISE NOTICE 'table pos_payment_refund column third_request_no already exists in this table ';
END IF; 
END$DO$;



DO $DO$
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'pos_payment_cancel' and column_name='action';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE pos_payment_cancel ADD COLUMN action character varying(100)';
ELSE RAISE NOTICE 'table pos_payment_cancel column action already exists in this table ';
END IF; 
END$DO$;


DO $DO$
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'pos_payment_cancel' and column_name='out_trade_no';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE pos_payment_cancel ADD COLUMN out_trade_no character varying(100)';
ELSE RAISE NOTICE 'table pos_payment_cancel column out_trade_no already exists in this table ';
END IF; 
END$DO$;


create table if not exists pos_payment_flow (
   id                   serial               not null,
   order_num            character varying(100) null,
   store_id             int4                 null,
   tenancy_id           varchar(50)                 null,
   type                 character varying(10) null,
   fund_channel         character varying(50) null,
   amount               DECIMAL(19,4)        null,
   real_amount          DECIMAL(19,4)        null,
   out_trade_no         character varying(100) null,
   out_request_no       character varying(32) null,
   trade_mark           character varying(10) null,
   constraint PK_POS_PAYMENT_FLOW primary key (id)
);


DO $DO$   
DECLARE num INT;  
BEGIN  
select count(*) into num from information_schema.columns where table_name = 'pos_payment_refund' and column_name='trade_date';  
IF num = 0 THEN  
    EXECUTE ' ALTER TABLE pos_payment_refund ADD COLUMN trade_date  date;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  


DO $DO$   
DECLARE num INT;  
BEGIN  
select count(*) into num from information_schema.columns where table_name = 'pos_payment_refund' and column_name='trade_time';  
IF num = 0 THEN  
    EXECUTE ' ALTER TABLE pos_payment_refund ADD COLUMN trade_time  timestamptz;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  


DO $DO$   
DECLARE num INT;  
BEGIN  
select count(*) into num from information_schema.columns where table_name = 'pos_payment_refund' and column_name='bill_num';  
IF num = 0 THEN  
    EXECUTE ' ALTER TABLE pos_payment_refund ADD COLUMN bill_num  varchar(100);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  


DO $DO$   
DECLARE num INT;  
BEGIN  
select count(*) into num from information_schema.columns where table_name = 'pos_payment_refund' and column_name='last_updatetime';  
IF num = 0 THEN  
    EXECUTE ' ALTER TABLE pos_payment_refund ADD COLUMN last_updatetime  timestamptz;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  





DO $DO$   
DECLARE num INT;  
BEGIN  
select count(*) into num from information_schema.columns where table_name = 'pos_payment_cancel' and column_name='trade_date';  
IF num = 0 THEN  
    EXECUTE ' ALTER TABLE pos_payment_cancel ADD COLUMN trade_date  date;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  


DO $DO$   
DECLARE num INT;  
BEGIN  
select count(*) into num from information_schema.columns where table_name = 'pos_payment_cancel' and column_name='trade_time';  
IF num = 0 THEN  
    EXECUTE ' ALTER TABLE pos_payment_cancel ADD COLUMN trade_time  timestamptz;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  


DO $DO$   
DECLARE num INT;  
BEGIN  
select count(*) into num from information_schema.columns where table_name = 'pos_payment_cancel' and column_name='bill_num';  
IF num = 0 THEN  
    EXECUTE ' ALTER TABLE pos_payment_cancel ADD COLUMN bill_num  varchar(100);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  


DO $DO$   
DECLARE num INT;  
BEGIN  
select count(*) into num from information_schema.columns where table_name = 'pos_payment_cancel' and column_name='last_updatetime';  
IF num = 0 THEN  
    EXECUTE ' ALTER TABLE pos_payment_cancel ADD COLUMN last_updatetime  timestamptz;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  





DO $DO$   
DECLARE num INT;  
BEGIN  
select count(*) into num from information_schema.columns where table_name = 'pos_payment_order' and column_name='trade_date';  
IF num = 0 THEN  
    EXECUTE ' ALTER TABLE pos_payment_order ADD COLUMN trade_date  date;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  


DO $DO$   
DECLARE num INT;  
BEGIN  
select count(*) into num from information_schema.columns where table_name = 'pos_payment_order' and column_name='trade_time';  
IF num = 0 THEN  
    EXECUTE ' ALTER TABLE pos_payment_order ADD COLUMN trade_time  timestamptz;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  


DO $DO$   
DECLARE num INT;  
BEGIN  
select count(*) into num from information_schema.columns where table_name = 'pos_payment_order' and column_name='bill_num';  
IF num = 0 THEN  
    EXECUTE ' ALTER TABLE pos_payment_order ADD COLUMN bill_num  varchar(100);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  


DO $DO$   
DECLARE num INT;  
BEGIN  
select count(*) into num from information_schema.columns where table_name = 'pos_payment_order' and column_name='last_updatetime';  
IF num = 0 THEN  
    EXECUTE ' ALTER TABLE pos_payment_order ADD COLUMN last_updatetime  timestamptz;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  



alter table cc_third_organ_info alter column secret type varchar(50); 


DO $DO$   
DECLARE num INT; 
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'cc_order_list' and column_name='platform_rate';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE  cc_order_list ADD COLUMN platform_rate numeric(19,4)';

ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;



DO $DO$   
DECLARE num INT; 
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'cc_order_list' and column_name='shop_rate';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE "public"."cc_order_list" ADD COLUMN "shop_rate" numeric(19,4)';

ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;


DO $DO$  
DECLARE num INT;  
BEGIN

create table if not exists cc_third_tenanttoken_info(
id serial PRIMARY KEY,
tenancyid VARCHAR(10) not null,
channel VARCHAR(10) not null,
accectoken varchar(100) not null,
refeshtoken varchar(100),
expires_in INTEGER,
tokentype varchar(50),
updatetime timestamp
);
select count(*) into num  from pg_constraint where conname='unique_ccthirdtenanttockeninfo_tenancyid';
if num=0 then
execute 'alter table cc_third_tenanttoken_info add constraint unique_ccthirdtenanttockeninfo_tenancyid unique(tenancyid);';
end if;

END$DO$;  



DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'cc_third_organ_info' and column_name='source2';  
IF num = 0 THEN  
    EXECUTE ' ALTER TABLE cc_third_organ_info ADD COLUMN source2 varchar(50);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  


DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'cc_third_organ_info' and column_name='secret2';  
IF num = 0 THEN  
    EXECUTE ' ALTER TABLE cc_third_organ_info ADD COLUMN secret2 varchar(100);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  

DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'cc_third_organ_info' and column_name='version';  
IF num = 0 THEN  
    EXECUTE ' ALTER TABLE cc_third_organ_info ADD COLUMN version varchar(10);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  





DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'hq_electronic_invoice_info' and column_name='electric_use_choose';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE hq_electronic_invoice_info ADD COLUMN electric_use_choose varchar(10)';
ELSE RAISE NOTICE 'table hq_electronic_invoice_info column electric_use_choose already exists in this table ';
END IF; 
END$DO$; 




DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'hq_electronic_invoice_info' and column_name='buyer_address';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE hq_electronic_invoice_info ADD COLUMN buyer_address varchar(100)';
ELSE RAISE NOTICE 'table hq_electronic_invoice_info column buyer_address already exists in this table ';
END IF; 
END$DO$; 


DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'hq_electronic_invoice_info' and column_name='buyer_phone';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE hq_electronic_invoice_info ADD COLUMN buyer_phone varchar(20)';
ELSE RAISE NOTICE 'table hq_electronic_invoice_info column buyer_phone already exists in this table ';
END IF; 
END$DO$; 


DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'hq_electronic_invoice_info' and column_name='buyer_mail';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE hq_electronic_invoice_info ADD COLUMN buyer_mail varchar(50)';
ELSE RAISE NOTICE 'table hq_electronic_invoice_info column buyer_mail already exists in this table ';
END IF; 
END$DO$; 



DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'hq_electronic_invoice_info' and column_name='bank_no';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE hq_electronic_invoice_info ADD COLUMN bank_no varchar(50)';
ELSE RAISE NOTICE 'table hq_electronic_invoice_info column bank_no already exists in this table ';
END IF; 
END$DO$; 

DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'hq_electronic_invoice_info' and column_name='buyer_mobile_phone';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE hq_electronic_invoice_info ADD COLUMN buyer_mobile_phone varchar(50)';
ELSE RAISE NOTICE 'table hq_electronic_invoice_info column buyer_mobile_phone already exists in this table ';
END IF; 
END$DO$; 
 

DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'hq_electronic_invoice_info' and column_name='buyer_tax';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE hq_electronic_invoice_info ADD COLUMN buyer_tax varchar(50)';
ELSE RAISE NOTICE 'table hq_electronic_invoice_info column buyer_tax already exists in this table ';
END IF; 
END$DO$; 



DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'hq_electronic_invoice_info' and column_name='jym';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE hq_electronic_invoice_info ADD COLUMN jym varchar(30)';
ELSE RAISE NOTICE 'table hq_electronic_invoice_info column jym already exists in this table ';
END IF; 
END$DO$; 


DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'hq_electronic_invoice_info' and column_name='pdf_url';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE hq_electronic_invoice_info ADD COLUMN pdf_url varchar(200)';
ELSE RAISE NOTICE 'table hq_electronic_invoice_info column pdf_url already exists in this table ';
END IF; 
END$DO$; 



DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'hq_electronic_invoice_info' and column_name='sp_url';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE hq_electronic_invoice_info ADD COLUMN sp_url varchar(200)';
ELSE RAISE NOTICE 'table hq_electronic_invoice_info column sp_url already exists in this table ';
END IF; 
END$DO$; 



DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'hq_electronic_invoice_info' and column_name='wx_order_id';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE hq_electronic_invoice_info ADD COLUMN wx_order_id varchar(50)';
ELSE RAISE NOTICE 'table hq_electronic_invoice_info column wx_order_id already exists in this table ';
END IF; 
END$DO$; 



DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'hq_electronic_invoice_info' and column_name='wx_app_id';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE hq_electronic_invoice_info ADD COLUMN wx_app_id varchar(50)';
ELSE RAISE NOTICE 'table hq_electronic_invoice_info column wx_app_id already exists in this table ';
END IF; 
END$DO$; 



DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'hq_electronic_invoice_info' and column_name='zfb_uid';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE hq_electronic_invoice_info ADD COLUMN zfb_uid varchar(50)';
ELSE RAISE NOTICE 'table hq_electronic_invoice_info column zfb_uid already exists in this table ';
END IF; 
END$DO$; 


DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'hq_electronic_invoice_info' and column_name='third_order_code';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE hq_electronic_invoice_info ADD COLUMN third_order_code varchar(50)';
ELSE RAISE NOTICE 'table hq_electronic_invoice_info column third_order_code already exists in this table ';
END IF; 
END$DO$; 






DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'crm_customer_card' and column_name='total_recharge_money';  
IF num = 0 THEN  
    EXECUTE ' ALTER TABLE crm_customer_card ADD COLUMN total_recharge_money  numeric(19,4);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  


  
  

CREATE TABLE IF NOT EXISTS crm_membership_group (
"tenancy_id" varchar(32) COLLATE "default",
"id" serial NOT NULL,
"group_name" VARCHAR(150),
"create_time" timestamp(6),
"refresh_time" timestamp(6),
"use_state" varchar(30),
"usage_count" varchar(30),
"valid_state"  varchar(10) COLLATE "default" NOT NULL,
"members_count"  int4,
"sql_text" TEXT,
"last_operator" varchar(30) COLLATE "default",
"last_updatetime" timestamp(6),
CONSTRAINT "pk_crm_membership_group" PRIMARY KEY ("id")
)
WITH (OIDS=FALSE)
;




CREATE TABLE IF NOT EXISTS crm_membership_group_sql_conditions (
"id" serial NOT NULL,
"membership_group_id" int4,
"param_type" varchar(100),
"tb_name" varchar(200),             
"tb_field1" varchar(100),
"tb_field_type1" varchar(100),
"tb_field_arithmetic1" varchar(100),
"tb_value1" text,            
"tb_value2" text,
"tb_field_time" varchar(100),
"tb_field_time_type" varchar(100),
"tb_field_time_arithmetic" varchar(100),
"tb_time1" varchar(10),              
"tb_time2" varchar(10),            
CONSTRAINT "pk_crm_membership_group_sql_conditions" PRIMARY KEY ("id")
)
WITH (OIDS=FALSE)
;


CREATE TABLE IF NOT EXISTS crm_membership_group_details (
"id" serial NOT NULL,
"membership_group_id" int4,
"customer_id" int4,
CONSTRAINT "pk_crm_membership_group_details" PRIMARY KEY ("id")
)
WITH (OIDS=FALSE)
;


create table if not exists pos_payment_alipay_discount (
	   id                   serial             not null,
	   tenancy_id           varchar(32)                 null,
	   store_id             int4                 null,
	   order_num            character varying(100) null,
	   out_trade_no         character varying(100) null,
	   type                 character varying(10) null,
	   discount_type        character varying(10) null,
	   amount               DECIMAL(19,4)        null,
	   merchant_contribute  DECIMAL(19,4)        null,
	   other_contribute     DECIMAL(19,4)        null,
	   memo                 character varying(100) null,
	   out_request_no       character varying(100) null,
	   trade_mark           character varying(10) null,
		 constraint PK_pos_payment_alipay_discount primary key (id)
	);


	



CREATE TABLE IF NOT EXISTS crm_membership_group_function_relation (
"id" serial NOT NULL,
"membership_group_id" int4,
"function_type" VARCHAR(150),
"function_id" int4,
"valid_state" VARCHAR(10),
"last_operator" varchar(30) COLLATE "default",
"last_updatetime" timestamp(6),
CONSTRAINT "pk_crm_membership_group_function_relation" PRIMARY KEY ("id")
)
WITH (OIDS=FALSE)
;



DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'organ' and column_name='image3';  
IF num = 0 THEN  
    EXECUTE ' ALTER TABLE organ ADD COLUMN image3 text;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  



DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'crm_customer_info' and column_name='customer_lable';  
IF num = 0 THEN  
    EXECUTE ' ALTER TABLE crm_customer_info ADD COLUMN customer_lable varchar(100);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  



DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'crm_customer_card' and column_name='activation_store_his';  
IF num = 0 THEN  
    EXECUTE ' ALTER TABLE crm_customer_card ADD COLUMN activation_store_his int4;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  







CREATE TABLE IF NOT EXISTS hq_item_taste_details (
"id" serial NOT NULL,
"item_id" int4,
"taste_id" int4,
"taste_code" VARCHAR(150),
"taste_name" VARCHAR(150),
"last_operator" varchar(30) COLLATE "default",
"last_updatetime" timestamp(6),
CONSTRAINT "pk_hq_item_taste_details" PRIMARY KEY ("id")
);




DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'user_discount_authority' and column_name='discount_type';  
IF num = 0 THEN  
    EXECUTE ' ALTER TABLE user_discount_authority ADD COLUMN discount_type varchar(2);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  


DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'payment_way' and column_name='bank_rate';  
IF num = 0 THEN  
    EXECUTE ' ALTER TABLE payment_way ADD COLUMN bank_rate varchar(50);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  


DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'pos_kvs_bill_item' and column_name='kvs_rel_num';  
IF num = 0 THEN  
    EXECUTE ' ALTER TABLE pos_kvs_bill_item ADD COLUMN kvs_rel_num varchar(32);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  



DO $DO$
DECLARE num INT; 
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'scm_storage_detail' and column_name='taxed_total_amt';  
IF num = 0 THEN  
		EXECUTE 'ALTER TABLE scm_storage_detail ADD COLUMN taxed_total_amt numeric(19,6);
		update scm_storage_detail set taxed_total_amt=0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;


DO $DO$
DECLARE num INT; 
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'scm_storage_detail' and column_name='tax';  
IF num = 0 THEN  
		EXECUTE 'ALTER TABLE scm_storage_detail ADD COLUMN tax numeric(19,6);update scm_storage_detail set tax=0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;


DO $DO$
DECLARE num INT; 
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'scm_storage_report' and column_name='this_taxed_price';  
IF num = 0 THEN  
		EXECUTE 'ALTER TABLE scm_storage_report ADD COLUMN this_taxed_price numeric(19,6);update scm_storage_report set this_taxed_price=0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;


DO $DO$
DECLARE num INT; 
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'scm_storage_report' and column_name='taxed_total_amt';  
IF num = 0 THEN  
		EXECUTE 'ALTER TABLE scm_storage_report ADD COLUMN taxed_total_amt numeric(19,6);update scm_storage_report set taxed_total_amt=0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;


DO $DO$
DECLARE num INT; 
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'scm_storage_report' and column_name='after_taxed_total_amt';  
IF num = 0 THEN  
		EXECUTE 'ALTER TABLE scm_storage_report ADD COLUMN after_taxed_total_amt numeric(19,6);update scm_storage_report set after_taxed_total_amt=0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;


DO $DO$
DECLARE num INT; 
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'scm_storage_report' and column_name='tax';  
IF num = 0 THEN  
		EXECUTE 'ALTER TABLE scm_storage_report ADD COLUMN tax numeric(19,6);update scm_storage_report set tax=0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;


DO $DO$
DECLARE num INT; 
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'scm_storage_report' and column_name='after_tax';  
IF num = 0 THEN  
		EXECUTE 'ALTER TABLE scm_storage_report ADD COLUMN after_tax numeric(19,6);update scm_storage_report set after_tax=0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;


DO $DO$
DECLARE num INT; 
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'scm_in' and column_name='taxed_total_amt';  
IF num = 0 THEN  
		EXECUTE 'ALTER TABLE scm_in ADD COLUMN taxed_total_amt numeric(19,6);update scm_in set taxed_total_amt=0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;

DO $DO$
DECLARE num INT; 
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'scm_in' and column_name='tax';  
IF num = 0 THEN  
		EXECUTE 'ALTER TABLE scm_in ADD COLUMN tax numeric(19,6);update scm_in set tax=0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;



DO $DO$
DECLARE num INT; 
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'scm_in_detail' and column_name='taxed_total_amt';  
IF num = 0 THEN  
		EXECUTE 'ALTER TABLE scm_in_detail ADD COLUMN taxed_total_amt numeric(19,6);update scm_in_detail set taxed_total_amt=0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;


DO $DO$
DECLARE num INT; 
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'scm_in_detail' and column_name='tax';  
IF num = 0 THEN  
		EXECUTE 'ALTER TABLE scm_in_detail ADD COLUMN tax numeric(19,6);update scm_in_detail set tax=0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;



DO $DO$
DECLARE num INT; 
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'scm_out' and column_name='taxed_total_amt';  
IF num = 0 THEN  
		EXECUTE 'ALTER TABLE scm_out ADD COLUMN taxed_total_amt numeric(19,6);update scm_out set taxed_total_amt=0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;


DO $DO$
DECLARE num INT; 
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'scm_out' and column_name='tax';  
IF num = 0 THEN  
		EXECUTE 'ALTER TABLE scm_out ADD COLUMN tax numeric(19,6);update scm_out set tax=0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;



DO $DO$
DECLARE num INT; 
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'scm_out_detail' and column_name='taxed_total_amt';  
IF num = 0 THEN  
		EXECUTE 'ALTER TABLE scm_out_detail ADD COLUMN taxed_total_amt numeric(19,6);update scm_out_detail set taxed_total_amt=0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;


DO $DO$
DECLARE num INT; 
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'scm_out_detail' and column_name='tax';  
IF num = 0 THEN  
		EXECUTE 'ALTER TABLE scm_out_detail ADD COLUMN tax numeric(19,6);update scm_out_detail set tax=0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;


DO $DO$
DECLARE num INT; 
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'scm_transfer' and column_name='taxed_total_amt';  
IF num = 0 THEN  
		EXECUTE 'ALTER TABLE scm_transfer ADD COLUMN taxed_total_amt numeric(19,6);update scm_transfer set taxed_total_amt=0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;


DO $DO$
DECLARE num INT; 
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'scm_transfer' and column_name='tax';  
IF num = 0 THEN  
		EXECUTE 'ALTER TABLE scm_transfer ADD COLUMN tax numeric(19,6);update scm_transfer set tax=0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;



DO $DO$
DECLARE num INT; 
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'scm_transfer_history' and column_name='taxed_total_amt';  
IF num = 0 THEN  
		EXECUTE 'ALTER TABLE scm_transfer_history ADD COLUMN taxed_total_amt numeric(19,6);update scm_transfer_history set taxed_total_amt=0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;


DO $DO$
DECLARE num INT; 
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'scm_transfer_history' and column_name='tax';  
IF num = 0 THEN  
		EXECUTE 'ALTER TABLE scm_transfer_history ADD COLUMN tax numeric(19,6);update scm_transfer_history set tax=0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;


DO $DO$
DECLARE num INT; 
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'scm_transfer_detail' and column_name='taxed_price';  
IF num = 0 THEN  
		EXECUTE 'ALTER TABLE scm_transfer_detail ADD COLUMN taxed_price numeric(19,6);update scm_transfer_detail set taxed_price=0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;


DO $DO$
DECLARE num INT; 
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'scm_transfer_detail' and column_name='taxed_total_amt';  
IF num = 0 THEN  
		EXECUTE 'ALTER TABLE scm_transfer_detail ADD COLUMN taxed_total_amt numeric(19,6);update scm_transfer_detail set taxed_total_amt=0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;


DO $DO$
DECLARE num INT; 
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'scm_transfer_detail' and column_name='tax';  
IF num = 0 THEN  
		EXECUTE 'ALTER TABLE scm_transfer_detail ADD COLUMN tax numeric(19,6);update scm_transfer_detail set tax=0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;



DO $DO$
DECLARE num INT; 
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'scm_transfer_detail_history' and column_name='taxed_price';  
IF num = 0 THEN  
		EXECUTE 'ALTER TABLE scm_transfer_detail_history ADD COLUMN taxed_price numeric(19,6);update scm_transfer_detail_history set taxed_price=0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;



DO $DO$
DECLARE num INT; 
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'scm_transfer_detail_history' and column_name='taxed_total_amt';  
IF num = 0 THEN  
		EXECUTE 'ALTER TABLE scm_transfer_detail_history ADD COLUMN taxed_total_amt numeric(19,6);update scm_transfer_detail_history set taxed_total_amt=0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;


DO $DO$
DECLARE num INT; 
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'scm_transfer_detail_history' and column_name='tax';  
IF num = 0 THEN  
		EXECUTE 'ALTER TABLE scm_transfer_detail_history ADD COLUMN tax numeric(19,6);update scm_transfer_detail_history set tax=0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;



DO $DO$
DECLARE num INT; 
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'scm_stocktaking_detail' and column_name='taxed_price';  
IF num = 0 THEN  
		EXECUTE 'ALTER TABLE scm_stocktaking_detail ADD COLUMN taxed_price numeric(19,6);update scm_stocktaking_detail set taxed_price=0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;



DO $DO$
DECLARE num INT; 
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'scm_stocktaking_detail' and column_name='taxed_total_amt';  
IF num = 0 THEN  
		EXECUTE 'ALTER TABLE scm_stocktaking_detail ADD COLUMN taxed_total_amt numeric(19,6);update scm_stocktaking_detail set taxed_total_amt=0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;


DO $DO$
DECLARE num INT; 
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'scm_stocktaking_detail' and column_name='tax';  
IF num = 0 THEN  
		EXECUTE 'ALTER TABLE scm_stocktaking_detail ADD COLUMN tax numeric(19,6);update scm_stocktaking_detail set tax=0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;


DO $DO$
DECLARE num INT; 
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'scm_stocktaking_detail_history' and column_name='taxed_price';  
IF num = 0 THEN  
		EXECUTE 'ALTER TABLE scm_stocktaking_detail_history ADD COLUMN taxed_price numeric(19,6);update scm_stocktaking_detail_history set taxed_price=0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;



DO $DO$
DECLARE num INT; 
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'scm_stocktaking_detail_history' and column_name='taxed_total_amt';  
IF num = 0 THEN  
		EXECUTE 'ALTER TABLE scm_stocktaking_detail_history ADD COLUMN taxed_total_amt numeric(19,6);update scm_stocktaking_detail_history set taxed_total_amt=0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;


DO $DO$
DECLARE num INT; 
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'scm_stocktaking_detail_history' and column_name='tax';  
IF num = 0 THEN  
		EXECUTE 'ALTER TABLE scm_stocktaking_detail_history ADD COLUMN tax numeric(19,6);update scm_stocktaking_detail_history set tax=0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;


DO $DO$
DECLARE num INT; 
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'scm_init_storage' and column_name='tax';  
IF num = 0 THEN  
		EXECUTE 'ALTER TABLE scm_init_storage ADD COLUMN tax numeric(19,6);update scm_init_storage set tax=0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;


DO $DO$
DECLARE num INT; 
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'scm_init_storage' and column_name='taxed_total_amt';  
IF num = 0 THEN  
		EXECUTE 'ALTER TABLE scm_init_storage ADD COLUMN taxed_total_amt numeric(19,6);update scm_init_storage set taxed_total_amt=0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;


DO $DO$
DECLARE num INT; 
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'scm_init_storage_detail' and column_name='tax';  
IF num = 0 THEN  
		EXECUTE 'ALTER TABLE scm_init_storage_detail ADD COLUMN tax numeric(19,6);update scm_init_storage_detail set tax=0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;


DO $DO$
DECLARE num INT; 
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'scm_init_storage_detail' and column_name='taxed_total_amt';  
IF num = 0 THEN  
		EXECUTE 'ALTER TABLE scm_init_storage_detail ADD COLUMN taxed_total_amt numeric(19,6);update scm_init_storage_detail set taxed_total_amt=0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;



DO $DO$
DECLARE num INT; 
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'scm_init_storage_detail' and column_name='taxed_price';  
IF num = 0 THEN  
		EXECUTE 'ALTER TABLE scm_init_storage_detail ADD COLUMN taxed_price numeric(19,6);update scm_init_storage_detail set taxed_price=0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;


DO $DO$
DECLARE num INT; 
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'scm_init_storage_detail' and column_name='tax_rate';  
IF num = 0 THEN  
		EXECUTE 'ALTER TABLE scm_init_storage_detail ADD COLUMN tax_rate numeric(19,6);update scm_init_storage_detail set tax_rate=0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;



   
 




DO $DO$   
DECLARE num INT;  
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'scm_supply_in_detail' and column_name='bulk_lot_qty'; 
IF num = 0 THEN  
		EXECUTE 'ALTER TABLE scm_supply_in_detail ADD COLUMN bulk_lot_qty NUMERIC(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$; 


DO $DO$   
DECLARE num INT;  
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'scm_supply_in_detail_history' and column_name='bulk_lot_qty'; 
IF num = 0 THEN  
		EXECUTE ' ALTER TABLE scm_supply_in_detail_history ADD COLUMN bulk_lot_qty NUMERIC(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$; 




DO $DO$   
DECLARE num INT;  
BEGIN  
        select count(*) into num from information_schema.columns where table_name = 'scm_inout_daily_acount' and column_name='taxed_init_amt'; 
IF num = 0 THEN  
        EXECUTE 'alter table scm_inout_daily_acount add COLUMN taxed_init_amt numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$; 

DO $DO$   
DECLARE num INT;  
BEGIN  
        select count(*) into num from information_schema.columns where table_name = 'scm_inout_daily_acount' and column_name='tax_init_amt'; 
IF num = 0 THEN  
        EXECUTE 'alter table scm_inout_daily_acount add COLUMN tax_init_amt numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$; 

DO $DO$   
DECLARE num INT;  
BEGIN  
        select count(*) into num from information_schema.columns where table_name = 'scm_inout_daily_acount' and column_name='taxed_inamt_total'; 
IF num = 0 THEN  
        EXECUTE 'alter table scm_inout_daily_acount add COLUMN taxed_inamt_total numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$; 

DO $DO$   
DECLARE num INT;  
BEGIN  
        select count(*) into num from information_schema.columns where table_name = 'scm_inout_daily_acount' and column_name='taxed_inamt_supply'; 
IF num = 0 THEN  
        EXECUTE 'alter table scm_inout_daily_acount add COLUMN taxed_inamt_supply numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$; 

DO $DO$   
DECLARE num INT;  
BEGIN  
        select count(*) into num from information_schema.columns where table_name = 'scm_inout_daily_acount' and column_name='taxed_inamt_center'; 
IF num = 0 THEN  
        EXECUTE 'alter table scm_inout_daily_acount add COLUMN taxed_inamt_center numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$; 

DO $DO$   
DECLARE num INT;  
BEGIN  
        select count(*) into num from information_schema.columns where table_name = 'scm_inout_daily_acount' and column_name='taxed_inamt_store'; 
IF num = 0 THEN  
        EXECUTE 'alter table scm_inout_daily_acount add COLUMN taxed_inamt_store numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$; 

DO $DO$   
DECLARE num INT;  
BEGIN  
        select count(*) into num from information_schema.columns where table_name = 'scm_inout_daily_acount' and column_name='taxed_inamt_request'; 
IF num = 0 THEN  
        EXECUTE 'alter table scm_inout_daily_acount add COLUMN taxed_inamt_request numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$; 

DO $DO$   
DECLARE num INT;  
BEGIN  
        select count(*) into num from information_schema.columns where table_name = 'scm_inout_daily_acount' and column_name='taxed_inamt_other'; 
IF num = 0 THEN  
        EXECUTE 'alter table scm_inout_daily_acount add COLUMN taxed_inamt_other numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$; 

DO $DO$   
DECLARE num INT;  
BEGIN  
        select count(*) into num from information_schema.columns where table_name = 'scm_inout_daily_acount' and column_name='taxed_inamt_proce'; 
IF num = 0 THEN  
        EXECUTE 'alter table scm_inout_daily_acount add COLUMN taxed_inamt_proce numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$; 

DO $DO$   
DECLARE num INT;  
BEGIN  
        select count(*) into num from information_schema.columns where table_name = 'scm_inout_daily_acount' and column_name='taxed_outamt_total'; 
IF num = 0 THEN  
        EXECUTE 'alter table scm_inout_daily_acount add COLUMN taxed_outamt_total numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$; 

DO $DO$   
DECLARE num INT;  
BEGIN  
        select count(*) into num from information_schema.columns where table_name = 'scm_inout_daily_acount' and column_name='taxed_outamt_supply'; 
IF num = 0 THEN  
        EXECUTE 'alter table scm_inout_daily_acount add COLUMN taxed_outamt_supply numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$; 

DO $DO$   
DECLARE num INT;  
BEGIN  
        select count(*) into num from information_schema.columns where table_name = 'scm_inout_daily_acount' and column_name='taxed_outamt_center'; 
IF num = 0 THEN  
        EXECUTE 'alter table scm_inout_daily_acount add COLUMN taxed_outamt_center numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$; 

DO $DO$   
DECLARE num INT;  
BEGIN  
        select count(*) into num from information_schema.columns where table_name = 'scm_inout_daily_acount' and column_name='taxed_outamt_store'; 
IF num = 0 THEN  
        EXECUTE 'alter table scm_inout_daily_acount add COLUMN taxed_outamt_store numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$; 

DO $DO$   
DECLARE num INT;  
BEGIN  
        select count(*) into num from information_schema.columns where table_name = 'scm_inout_daily_acount' and column_name='taxed_outamt_request'; 
IF num = 0 THEN  
        EXECUTE 'alter table scm_inout_daily_acount add COLUMN taxed_outamt_request numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$; 

DO $DO$   
DECLARE num INT;  
BEGIN  
        select count(*) into num from information_schema.columns where table_name = 'scm_inout_daily_acount' and column_name='taxed_outamt_other'; 
IF num = 0 THEN  
        EXECUTE 'alter table scm_inout_daily_acount add COLUMN taxed_outamt_other numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$; 

DO $DO$   
DECLARE num INT;  
BEGIN  
        select count(*) into num from information_schema.columns where table_name = 'scm_inout_daily_acount' and column_name='taxed_outamt_discard'; 
IF num = 0 THEN  
        EXECUTE 'alter table scm_inout_daily_acount add COLUMN taxed_outamt_discard numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$; 

DO $DO$   
DECLARE num INT;  
BEGIN  
        select count(*) into num from information_schema.columns where table_name = 'scm_inout_daily_acount' and column_name='taxed_outamt_consume'; 
IF num = 0 THEN  
        EXECUTE 'alter table scm_inout_daily_acount add COLUMN taxed_outamt_consume numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$; 

DO $DO$   
DECLARE num INT;  
BEGIN  
        select count(*) into num from information_schema.columns where table_name = 'scm_inout_daily_acount' and column_name='taxed_inamt_overflow'; 
IF num = 0 THEN  
        EXECUTE 'alter table scm_inout_daily_acount add COLUMN taxed_inamt_overflow numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$; 

DO $DO$   
DECLARE num INT;  
BEGIN  
        select count(*) into num from information_schema.columns where table_name = 'scm_inout_daily_acount' and column_name='taxed_outamt_stock_loss'; 
IF num = 0 THEN  
        EXECUTE 'alter table scm_inout_daily_acount add COLUMN taxed_outamt_stock_loss numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$; 

DO $DO$   
DECLARE num INT;  
BEGIN  
        select count(*) into num from information_schema.columns where table_name = 'scm_inout_daily_acount' and column_name='taxed_stock_amt'; 
IF num = 0 THEN  
        EXECUTE 'alter table scm_inout_daily_acount add COLUMN taxed_stock_amt numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$; 

DO $DO$   
DECLARE num INT;  
BEGIN  
        select count(*) into num from information_schema.columns where table_name = 'scm_inout_daily_acount' and column_name='taxed_end_amt'; 
IF num = 0 THEN  
        EXECUTE 'alter table scm_inout_daily_acount add COLUMN taxed_end_amt numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$; 

DO $DO$   
DECLARE num INT;  
BEGIN  
        select count(*) into num from information_schema.columns where table_name = 'scm_inout_daily_acount' and column_name='tax_end_amt'; 
IF num = 0 THEN  
        EXECUTE 'alter table scm_inout_daily_acount add COLUMN tax_end_amt numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$; 






DO $DO$
DECLARE num INT; 
BEGIN  
    select count(*) into num from information_schema.columns 
    where table_name = 'pos_kvs_bill_item' and column_name='kvs_rel_num';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE pos_kvs_bill_item ADD COLUMN kvs_rel_num VARCHAR(32);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;


DO $DO$
DECLARE num INT; 
BEGIN  
    select count(*) into num from information_schema.columns 
    where table_name = 'pos_bill_item' and column_name='opt_num';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE pos_bill_item ADD COLUMN opt_num VARCHAR(50);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;


DO $DO$
DECLARE num INT; 
BEGIN  
    select count(*) into num from information_schema.columns 
    where table_name = 'pos_bill_item2' and column_name='opt_num';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE pos_bill_item2 ADD COLUMN opt_num VARCHAR(50);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;


DO $DO$
DECLARE num INT; 
BEGIN  
    select count(*) into num from information_schema.columns 
    where table_name = 'pos_opter_changshift_main' and column_name='reason';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE pos_opter_changshift_main ADD COLUMN reason VARCHAR(255);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;




CREATE TABLE IF NOT EXISTS hq_item_taste_details (
"id" serial NOT NULL,
"item_id" int4,
"taste_id" int4,
"taste_code" VARCHAR(150),
"taste_name" VARCHAR(150),
"last_operator" varchar(30) COLLATE "default",
"last_updatetime" timestamp(6),
CONSTRAINT "pk_hq_item_taste_details" PRIMARY KEY ("id")
)
WITH (OIDS=FALSE)
;



DO $DO$   
DECLARE num INT;  
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'user_discount_authority' and column_name='discount_type';  
IF num = 0 THEN  
		EXECUTE ' ALTER TABLE user_discount_authority ADD COLUMN discount_type varchar(2);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  



DO $DO$   
DECLARE num INT;  
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'payment_way' and column_name='bank_rate';  
IF num = 0 THEN  
		EXECUTE ' ALTER TABLE payment_way ADD COLUMN bank_rate varchar(50);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  



DO $DO$   
DECLARE num INT;  
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'hq_legal_per' and column_name='docking_merchants';  
IF num = 0 THEN  
		EXECUTE ' ALTER TABLE hq_legal_per ADD COLUMN docking_merchants varchar(10);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  



DO $DO$   
DECLARE num INT;  
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'hq_legal_per' and column_name='certificate_url';  
IF num = 0 THEN  
		EXECUTE ' ALTER TABLE hq_legal_per ADD COLUMN certificate_url varchar(255);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  



DO $DO$   
DECLARE num INT;  
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'hq_legal_per' and column_name='certificate_pwd';  
IF num = 0 THEN  
		EXECUTE ' ALTER TABLE hq_legal_per ADD COLUMN certificate_pwd varchar(100);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  



DO $DO$   
DECLARE num INT;  
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'hq_legal_per' and column_name='wechat_party_id';  
IF num = 0 THEN  
		EXECUTE ' ALTER TABLE hq_legal_per ADD COLUMN wechat_party_id varchar(100);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  



DO $DO$   
DECLARE num INT;  
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'hq_legal_per' and column_name='wechat_invoice_key';  
IF num = 0 THEN  
		EXECUTE ' ALTER TABLE hq_legal_per ADD COLUMN wechat_invoice_key varchar(100);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  


DO $DO$   
DECLARE num INT;  
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'hq_legal_per' and column_name='taxpayers_attr_id';  
IF num = 0 THEN  
		EXECUTE ' ALTER TABLE hq_legal_per ADD COLUMN taxpayers_attr_id varchar(32);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  



DO $DO$   
DECLARE num INT;  
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'crm_customer_card' and column_name='total_recharge_money';  
IF num = 0 THEN  
		EXECUTE ' ALTER TABLE crm_customer_card ADD COLUMN total_recharge_money  numeric(19,4);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  




CREATE TABLE IF NOT EXISTS crm_membership_group (
"tenancy_id" varchar(32) COLLATE "default",
"id" serial NOT NULL,
"group_name" VARCHAR(150),
"create_time" timestamp(6),
"refresh_time" timestamp(6),
"use_state" varchar(30),
"usage_count" varchar(30),
"valid_state"  varchar(10) COLLATE "default" NOT NULL,
"members_count"  int4,
"sql_text" TEXT,
"last_operator" varchar(30) COLLATE "default",
"last_updatetime" timestamp(6),
CONSTRAINT "pk_crm_membership_group" PRIMARY KEY ("id")
)
WITH (OIDS=FALSE)
;



CREATE TABLE IF NOT EXISTS crm_membership_group_sql_conditions (
"id" serial NOT NULL,
"membership_group_id" int4,
"param_type" varchar(100),
"tb_name" varchar(200),             
"tb_field1" varchar(100),
"tb_field_type1" varchar(100),
"tb_field_arithmetic1" varchar(100),
"tb_value1" text,            
"tb_value2" text,
"tb_field_time" varchar(100),
"tb_field_time_type" varchar(100),
"tb_field_time_arithmetic" varchar(100),
"tb_time1" varchar(10),              
"tb_time2" varchar(10),            
CONSTRAINT "pk_crm_membership_group_sql_conditions" PRIMARY KEY ("id")
)
WITH (OIDS=FALSE) ;

CREATE TABLE IF NOT EXISTS crm_membership_group_details (
"id" serial NOT NULL,
"membership_group_id" int4,
"customer_id" int4,
CONSTRAINT "pk_crm_membership_group_details" PRIMARY KEY ("id")
)
WITH (OIDS=FALSE) ;

CREATE TABLE IF NOT EXISTS crm_membership_group_function_relation (
"id" serial NOT NULL,
"membership_group_id" int4,
"function_type" VARCHAR(150),
"function_id" int4,
"valid_state" VARCHAR(10),
"last_operator" varchar(30) COLLATE "default",
"last_updatetime" timestamp(6),
CONSTRAINT "pk_crm_membership_group_function_relation" PRIMARY KEY ("id")
)
WITH (OIDS=FALSE) ;



DO $DO$   
DECLARE num INT;  
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'organ' and column_name='image3';  
IF num = 0 THEN  
		EXECUTE ' ALTER TABLE organ ADD COLUMN image3 text;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  



DO $DO$   
DECLARE num INT;  
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'crm_customer_info' and column_name='customer_lable';  
IF num = 0 THEN  
		EXECUTE ' ALTER TABLE crm_customer_info ADD COLUMN customer_lable varchar(100);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  



DO $DO$   
DECLARE num INT;  
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'crm_customer_card' and column_name='activation_store_his';  
IF num = 0 THEN  
		EXECUTE ' ALTER TABLE crm_customer_card ADD COLUMN activation_store_his int4;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  



DROP TABLE IF EXISTS crm_card_commission;
CREATE TABLE IF NOT EXISTS crm_card_commission (
"tenancy_id" varchar(32) COLLATE "default",
"id" serial NOT NULL,
"store_id" int4,
"card_class_id" int4,
"commission_saler" numeric(19,4),
"commission_store" numeric(19,4),
"commision_predeposit_per" numeric(19,4),
"commision_predeposit_store" numeric(19,4),
"commision_predeposit_fixed_per" numeric(19,4),
"commision_predeposit_fixed_store" numeric(19,4),
"commision_predeposit_per2" numeric(19,4),
"commision_predeposit_store2" numeric(19,4),
"commision_predeposit_fixed_per2" numeric(19,4),
"commision_predeposit_fixed_store2" numeric(19,4),
"last_operator" varchar(32) COLLATE "default",
"last_updatetime" timestamp(6),
CONSTRAINT "pk_crm_card_commission" PRIMARY KEY ("id")
)
WITH (OIDS=FALSE)
;



DO $DO$   
DECLARE num INT;  
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'crm_incorporation_gzlist' and column_name='last_cz_bill_code';  
IF num = 0 THEN  
		EXECUTE ' ALTER TABLE crm_incorporation_gzlist ADD COLUMN last_cz_bill_code varchar(30);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  



DO $DO$   
DECLARE num INT;  
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'crm_incorporation_czlist' and column_name='cz_bill_code';  
IF num = 0 THEN  
		EXECUTE ' ALTER TABLE crm_incorporation_czlist ADD COLUMN cz_bill_code varchar(30);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  




DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'pos_payment_order' and column_name='invoice_amount';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE pos_payment_order ADD COLUMN invoice_amount DECIMAL(19,4)';
ELSE RAISE NOTICE 'table pos_payment_order column invoice_amount already exists in this table ';
END IF; 
END$DO$; 



DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'pos_payment_order' and column_name='card_balance';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE pos_payment_order ADD COLUMN card_balance DECIMAL(19,4)';
ELSE RAISE NOTICE 'table pos_payment_order column card_balance already exists in this table ';
END IF; 
END$DO$; 



DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'pos_payment_order' and column_name='trade_type';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE pos_payment_order ADD COLUMN trade_type varchar(50)';
ELSE RAISE NOTICE 'table pos_payment_order column trade_type already exists in this table ';
END IF; 
END$DO$; 



DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'pos_payment_order' and column_name='coupon_fee';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE pos_payment_order ADD COLUMN coupon_fee DECIMAL(19,4)';
ELSE RAISE NOTICE 'table pos_payment_order column coupon_fee already exists in this table ';
END IF; 
END$DO$; 



DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'pos_payment_order' and column_name='point_amount';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE pos_payment_order ADD COLUMN point_amount DECIMAL(19,4)';
ELSE RAISE NOTICE 'table pos_payment_order column point_amount already exists in this table ';
END IF; 
END$DO$; 


DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'pos_payment_refund' and column_name='coupon_refund_fee';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE pos_payment_refund ADD COLUMN coupon_refund_fee DECIMAL(19,4)';
ELSE RAISE NOTICE 'table pos_payment_refund column coupon_refund_fee already exists in this table ';
END IF; 
END$DO$; 



DO $DO$
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'pos_payment_refund' and column_name='out_trade_no';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE pos_payment_refund ADD COLUMN out_trade_no character varying(100)';
ELSE RAISE NOTICE 'table pos_payment_refund column out_trade_no already exists in this table ';
END IF; 
END$DO$; 



DO $DO$
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'pos_payment_refund' and column_name='settlement_refund_fee';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE pos_payment_refund ADD COLUMN settlement_refund_fee DECIMAL(19,4)';
ELSE RAISE NOTICE 'table pos_payment_refund column settlement_refund_fee already exists in this table ';
END IF; 
END$DO$; 



DO $DO$
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'pos_payment_refund' and column_name='cash_refund_fee';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE pos_payment_refund ADD COLUMN cash_refund_fee DECIMAL(19,4)';
ELSE RAISE NOTICE 'table pos_payment_refund column cash_refund_fee already exists in this table ';
END IF; 
END$DO$; 



DO $DO$
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'pos_payment_refund' and column_name='third_request_no';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE pos_payment_refund ADD COLUMN third_request_no character varying(100)';
ELSE RAISE NOTICE 'table pos_payment_refund column third_request_no already exists in this table ';
END IF; 
END$DO$;


DO $DO$
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'pos_payment_cancel' and column_name='action';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE pos_payment_cancel ADD COLUMN action character varying(100)';
ELSE RAISE NOTICE 'table pos_payment_cancel column action already exists in this table ';
END IF; 
END$DO$;



DO $DO$
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'pos_payment_cancel' and column_name='out_trade_no';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE pos_payment_cancel ADD COLUMN out_trade_no character varying(100)';
ELSE RAISE NOTICE 'table pos_payment_cancel column out_trade_no already exists in this table ';
END IF; 
END$DO$;



create table if not exists pos_payment_alipay_discount (
     id                   serial             not null,
     tenancy_id           varchar(32)                 null,
     store_id             int4                 null,
     order_num            character varying(100) null,
     out_trade_no         character varying(100) null,
     type                 character varying(10) null,
     discount_type        character varying(10) null,
     amount               DECIMAL(19,4)        null,
     merchant_contribute  DECIMAL(19,4)        null,
     other_contribute     DECIMAL(19,4)        null,
     memo                 character varying(100) null,
     out_request_no       character varying(100) null,
     trade_mark           character varying(10) null,
     constraint PK_pos_payment_alipay_discount primary key (id)
  );




create table if not exists pos_payment_flow (
   id                   serial               not null,
   order_num            character varying(100) null,
   store_id             int4                 null,
   tenancy_id           varchar(50)                 null,
   type                 character varying(10) null,
   fund_channel         character varying(50) null,
   amount               DECIMAL(19,4)        null,
   real_amount          DECIMAL(19,4)        null,
   out_trade_no         character varying(100) null,
   out_request_no       character varying(32) null,
   trade_mark           character varying(10) null,
   constraint PK_POS_PAYMENT_FLOW primary key (id)
);



DO $DO$   
DECLARE num INT;  
BEGIN  
select count(*) into num from information_schema.columns where table_name = 'pos_payment_refund' and column_name='trade_date';  
IF num = 0 THEN  
    EXECUTE ' ALTER TABLE pos_payment_refund ADD COLUMN trade_date  date;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  


DO $DO$   
DECLARE num INT;  
BEGIN  
select count(*) into num from information_schema.columns where table_name = 'pos_payment_refund' and column_name='trade_time';  
IF num = 0 THEN  
    EXECUTE ' ALTER TABLE pos_payment_refund ADD COLUMN trade_time  timestamptz;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  


DO $DO$   
DECLARE num INT;  
BEGIN  
select count(*) into num from information_schema.columns where table_name = 'pos_payment_refund' and column_name='bill_num';  
IF num = 0 THEN  
    EXECUTE ' ALTER TABLE pos_payment_refund ADD COLUMN bill_num  varchar(100);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  


DO $DO$   
DECLARE num INT;  
BEGIN  
select count(*) into num from information_schema.columns where table_name = 'pos_payment_refund' and column_name='last_updatetime';  
IF num = 0 THEN  
    EXECUTE ' ALTER TABLE pos_payment_refund ADD COLUMN last_updatetime  timestamptz;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  





DO $DO$   
DECLARE num INT;  
BEGIN  
select count(*) into num from information_schema.columns where table_name = 'pos_payment_cancel' and column_name='trade_date';  
IF num = 0 THEN  
    EXECUTE ' ALTER TABLE pos_payment_cancel ADD COLUMN trade_date  date;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  


DO $DO$   
DECLARE num INT;  
BEGIN  
select count(*) into num from information_schema.columns where table_name = 'pos_payment_cancel' and column_name='trade_time';  
IF num = 0 THEN  
    EXECUTE ' ALTER TABLE pos_payment_cancel ADD COLUMN trade_time  timestamptz;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  


DO $DO$   
DECLARE num INT;  
BEGIN  
select count(*) into num from information_schema.columns where table_name = 'pos_payment_cancel' and column_name='bill_num';  
IF num = 0 THEN  
    EXECUTE ' ALTER TABLE pos_payment_cancel ADD COLUMN bill_num  varchar(100);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  


DO $DO$   
DECLARE num INT;  
BEGIN  
select count(*) into num from information_schema.columns where table_name = 'pos_payment_cancel' and column_name='last_updatetime';  
IF num = 0 THEN  
    EXECUTE ' ALTER TABLE pos_payment_cancel ADD COLUMN last_updatetime  timestamptz;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  




DO $DO$   
DECLARE num INT;  
BEGIN  
select count(*) into num from information_schema.columns where table_name = 'pos_payment_order' and column_name='trade_date';  
IF num = 0 THEN  
    EXECUTE ' ALTER TABLE pos_payment_order ADD COLUMN trade_date  date;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  


DO $DO$   
DECLARE num INT;  
BEGIN  
select count(*) into num from information_schema.columns where table_name = 'pos_payment_order' and column_name='trade_time';  
IF num = 0 THEN  
    EXECUTE ' ALTER TABLE pos_payment_order ADD COLUMN trade_time  timestamptz;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  


DO $DO$   
DECLARE num INT;  
BEGIN  
select count(*) into num from information_schema.columns where table_name = 'pos_payment_order' and column_name='bill_num';  
IF num = 0 THEN  
    EXECUTE ' ALTER TABLE pos_payment_order ADD COLUMN bill_num  varchar(100);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  


DO $DO$   
DECLARE num INT;  
BEGIN  
select count(*) into num from information_schema.columns where table_name = 'pos_payment_order' and column_name='last_updatetime';  
IF num = 0 THEN  
    EXECUTE ' ALTER TABLE pos_payment_order ADD COLUMN last_updatetime  timestamptz;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  


DO $DO$   
DECLARE num INT;  
BEGIN  
select count(*) into num from information_schema.columns
 where table_name = 'cc_third_organ_info' and column_name='secret';  
IF num = 0 THEN  
    EXECUTE ' ALTER TABLE cc_third_organ_info ADD COLUMN secret  varchar(50);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  





DO $DO$   
DECLARE num INT; 
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'cc_order_list' and column_name='platform_rate';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE "public"."cc_order_list" ADD COLUMN "platform_rate" numeric(19,4);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;

DO $DO$   
DECLARE num INT; 
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'cc_order_list' and column_name='shop_rate';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE "public"."cc_order_list" ADD COLUMN "shop_rate" numeric(19,4);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;




create table if not exists cc_third_tenanttoken_info(
id serial PRIMARY KEY,
tenancyid VARCHAR(10) not null,
channel VARCHAR(10) not null,
accectoken varchar(100) not null,
refeshtoken varchar(100),
expires_in INTEGER,
tokentype varchar(50),
updatetime timestamp
);
 



DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'cc_third_organ_info' and column_name='source2';  
IF num = 0 THEN  
    EXECUTE ' ALTER TABLE cc_third_organ_info ADD COLUMN source2 varchar(50);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  


DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'cc_third_organ_info' and column_name='secret2';  
IF num = 0 THEN  
    EXECUTE ' ALTER TABLE cc_third_organ_info ADD COLUMN secret2 varchar(100);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  

DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'cc_third_organ_info' and column_name='version';  
IF num = 0 THEN  
    EXECUTE ' ALTER TABLE cc_third_organ_info ADD COLUMN version varchar(10);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  




DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'hq_electronic_invoice_info' and column_name='electric_use_choose';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE hq_electronic_invoice_info ADD COLUMN electric_use_choose varchar(10)';
ELSE RAISE NOTICE 'table hq_electronic_invoice_info column electric_use_choose already exists in this table ';
END IF; 
END$DO$; 


DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'hq_electronic_invoice_info' and column_name='buyer_address';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE hq_electronic_invoice_info ADD COLUMN buyer_address varchar(100)';
ELSE RAISE NOTICE 'table hq_electronic_invoice_info column buyer_address already exists in this table ';
END IF; 
END$DO$; 


DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'hq_electronic_invoice_info' and column_name='buyer_phone';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE hq_electronic_invoice_info ADD COLUMN buyer_phone varchar(20)';
ELSE RAISE NOTICE 'table hq_electronic_invoice_info column buyer_phone already exists in this table ';
END IF; 
END$DO$; 



DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'hq_electronic_invoice_info' and column_name='buyer_mail';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE hq_electronic_invoice_info ADD COLUMN buyer_mail varchar(50)';
ELSE RAISE NOTICE 'table hq_electronic_invoice_info column buyer_mail already exists in this table ';
END IF; 
END$DO$; 



DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'hq_electronic_invoice_info' and column_name='bank_no';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE hq_electronic_invoice_info ADD COLUMN bank_no varchar(50)';
ELSE RAISE NOTICE 'table hq_electronic_invoice_info column bank_no already exists in this table ';
END IF; 
END$DO$; 



DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'hq_electronic_invoice_info' and column_name='buyer_mobile_phone';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE hq_electronic_invoice_info ADD COLUMN buyer_mobile_phone varchar(50)';
ELSE RAISE NOTICE 'table hq_electronic_invoice_info column buyer_mobile_phone already exists in this table ';
END IF; 
END$DO$; 


DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'hq_electronic_invoice_info' and column_name='buyer_tax';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE hq_electronic_invoice_info ADD COLUMN buyer_tax varchar(50)';
ELSE RAISE NOTICE 'table hq_electronic_invoice_info column buyer_tax already exists in this table ';
END IF; 
END$DO$; 



DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'hq_electronic_invoice_info' and column_name='jym';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE hq_electronic_invoice_info ADD COLUMN jym varchar(30)';
ELSE RAISE NOTICE 'table hq_electronic_invoice_info column jym already exists in this table ';
END IF; 
END$DO$; 



DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'hq_electronic_invoice_info' and column_name='pdf_url';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE hq_electronic_invoice_info ADD COLUMN pdf_url varchar(200)';
ELSE RAISE NOTICE 'table hq_electronic_invoice_info column pdf_url already exists in this table ';
END IF; 
END$DO$; 


DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'hq_electronic_invoice_info' and column_name='sp_url';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE hq_electronic_invoice_info ADD COLUMN sp_url varchar(200)';
ELSE RAISE NOTICE 'table hq_electronic_invoice_info column sp_url already exists in this table ';
END IF; 
END$DO$; 
 


DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'hq_electronic_invoice_info' and column_name='wx_order_id';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE hq_electronic_invoice_info ADD COLUMN wx_order_id varchar(50)';
ELSE RAISE NOTICE 'table hq_electronic_invoice_info column wx_order_id already exists in this table ';
END IF; 
END$DO$; 



DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'hq_electronic_invoice_info' and column_name='wx_app_id';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE hq_electronic_invoice_info ADD COLUMN wx_app_id varchar(50)';
ELSE RAISE NOTICE 'table hq_electronic_invoice_info column wx_app_id already exists in this table ';
END IF; 
END$DO$; 


DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'hq_electronic_invoice_info' and column_name='zfb_uid';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE hq_electronic_invoice_info ADD COLUMN zfb_uid varchar(50)';
ELSE RAISE NOTICE 'table hq_electronic_invoice_info column zfb_uid already exists in this table ';
END IF; 
END$DO$; 



DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'hq_electronic_invoice_info' and column_name='third_order_code';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE hq_electronic_invoice_info ADD COLUMN third_order_code varchar(50)';
ELSE RAISE NOTICE 'table hq_electronic_invoice_info column third_order_code already exists in this table ';
END IF; 
END$DO$; 


DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'cc_third_tenanttoken_info' and column_name='shopid';  
IF num = 0 THEN  
    EXECUTE ' ALTER TABLE cc_third_tenanttoken_info ADD COLUMN shopid varchar(20);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  


DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'cc_third_organ_info' and column_name='auth_level';  
IF num = 0 THEN  
    EXECUTE ' ALTER TABLE cc_third_organ_info ADD COLUMN auth_level varchar(10);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  




DO $DO$   
DECLARE num INT;
DECLARE num2 INT;
begin
select count(*) into num  from pg_constraint where conname='unique_ccthirdtenanttockeninfo_tenancyid';
if num>0 then

execute 'alter table cc_third_tenanttoken_info drop constraint unique_ccthirdtenanttockeninfo_tenancyid;';
end if;

select count(*) into num2  from pg_constraint where conname='unique_ccthirdtenanttockeninfo_tenancyid_channel_shopid';
if num2=0 then
execute 'alter table cc_third_tenanttoken_info add constraint unique_ccthirdtenanttockeninfo_tenancyid_channel_shopid unique(tenancyid,channel,shopid);';
END IF; 
END$DO$; 

DO $DO$   
DECLARE num INT; 
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'cc_order_list' and column_name='platform_rate';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE "public"."cc_order_list" ADD COLUMN "platform_rate" numeric(19,4);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;

DO $DO$   
DECLARE num INT; 
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'cc_order_list' and column_name='shop_rate';  
IF num = 0 THEN  
    EXECUTE 'ALTER TABLE "public"."cc_order_list" ADD COLUMN "shop_rate" numeric(19,4);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;


DO $DO$  
DECLARE num INT;  
BEGIN

create table if not exists cc_third_tenanttoken_info(
id serial PRIMARY KEY,
tenancyid VARCHAR(10) not null,
channel VARCHAR(10) not null,
accectoken varchar(100) not null,
refeshtoken varchar(100),
expires_in INTEGER,
tokentype varchar(50),
updatetime timestamp
);
select count(*) into num  from pg_constraint where conname='unique_ccthirdtenanttockeninfo_tenancyid';
if num=0 then
execute 'alter table cc_third_tenanttoken_info add constraint unique_ccthirdtenanttockeninfo_tenancyid unique(tenancyid);';
end if;

END$DO$;  


DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'cc_third_organ_info' and column_name='source2';  
IF num = 0 THEN  
    EXECUTE ' ALTER TABLE cc_third_organ_info ADD COLUMN source2 varchar(50);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  


DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'cc_third_organ_info' and column_name='secret2';  
IF num = 0 THEN  
    EXECUTE ' ALTER TABLE cc_third_organ_info ADD COLUMN secret2 varchar(100);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  

DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'cc_third_organ_info' and column_name='version';  
IF num = 0 THEN  
    EXECUTE ' ALTER TABLE cc_third_organ_info ADD COLUMN version varchar(10);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  



DO $DO$   
DECLARE num INT;  
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'cc_third_item_class_info' and column_name='start_sell_time';  
IF num = 0 THEN  
		EXECUTE ' ALTER TABLE cc_third_item_class_info ADD COLUMN start_sell_time varchar(8);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  


DO $DO$   
DECLARE num INT;  
BEGIN  
select count(*) into num from information_schema.columns where table_name = 'cc_third_item_class_info' and column_name='end_sell_time';
IF num = 0 THEN  
		EXECUTE ' ALTER TABLE cc_third_item_class_info ADD COLUMN end_sell_time varchar(8);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$; 


DO $DO$   
DECLARE num INT;  
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'cc_third_tenanttoken_info' and column_name='oauth_type';  
IF num = 0 THEN  
		EXECUTE ' ALTER TABLE cc_third_tenanttoken_info ADD COLUMN oauth_type varchar(20);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  


DO $DO$   
DECLARE num INT;  
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'crm_coupons_class' and column_name='check_only';  
IF num = 0 THEN  
		EXECUTE 'alter TABLE crm_coupons_class add  check_only  int4 default 1';
ELSE RAISE NOTICE 'column  already exists in this table ';
END IF; 
END$DO$; 


DO $DO$   
DECLARE num INT;  
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'crm_coupons_class' and column_name='help_memory_code';  
IF num = 0 THEN  
		EXECUTE 'alter TABLE crm_coupons_class add  help_memory_code  varchar(20) ';
ELSE RAISE NOTICE 'column  already exists in this table ';
END IF; 
END$DO$; 


DO $DO$   
DECLARE num INT;  
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'crm_coupons_class' and column_name='wx_color';  
IF num = 0 THEN  
		EXECUTE 'alter TABLE crm_coupons_class add  wx_color  varchar(20) ';
ELSE RAISE NOTICE 'column  already exists in this table ';
END IF; 
END$DO$; 


DO $DO$   
DECLARE num INT;  
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'crm_coupons_class' and column_name='syn_status';  
IF num = 0 THEN  
		EXECUTE 'alter TABLE crm_coupons_class add  syn_status  int4 default 0';
ELSE RAISE NOTICE 'column  already exists in this table ';
END IF; 
END$DO$; 


DO $DO$   
DECLARE num INT;  
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'crm_coupons_class' and column_name='syn_time';  
IF num = 0 THEN  
		EXECUTE 'alter TABLE crm_coupons_class add  syn_time  timestamp(6) ';
ELSE RAISE NOTICE 'column  already exists in this table ';
END IF; 
END$DO$; 


DO $DO$   
DECLARE num INT;  
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'crm_coupons_class' and column_name='syn_fail_reason';  
IF num = 0 THEN  
		EXECUTE 'alter TABLE crm_coupons_class add  syn_fail_reason varchar(50)';
ELSE RAISE NOTICE 'column  already exists in this table ';
END IF; 
END$DO$; 


DO $DO$   
DECLARE num INT;  
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'crm_coupons_class' and column_name='audit_status';  
IF num = 0 THEN  
		EXECUTE 'alter TABLE crm_coupons_class add  audit_status  int4 default 0';
ELSE RAISE NOTICE 'column  already exists in this table ';
END IF; 
END$DO$; 


DO $DO$   
DECLARE num INT;  
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'crm_coupons_class' and column_name='audit_time';  
IF num = 0 THEN  
		EXECUTE 'alter TABLE crm_coupons_class add  audit_time  timestamp(6) ';
ELSE RAISE NOTICE 'column  already exists in this table ';
END IF; 
END$DO$; 


DO $DO$   
DECLARE num INT;  
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'crm_coupons_class' and column_name='audit_fail_reason';  
IF num = 0 THEN  
		EXECUTE 'alter TABLE crm_coupons_class add  audit_fail_reason varchar(50)';
ELSE RAISE NOTICE 'column  already exists in this table ';
END IF; 
END$DO$; 


DO $DO$   
DECLARE num INT;  
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'crm_coupons_class' and column_name='create_time';  
IF num = 0 THEN  
		EXECUTE 'alter TABLE crm_coupons_class add  create_time  timestamp(6) ';
ELSE RAISE NOTICE 'column  already exists in this table ';
END IF; 
END$DO$; 



DO $DO$   
DECLARE num INT;  
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'crm_coupons' and column_name='wx_coupon_state';  
IF num = 0 THEN  
		EXECUTE ' alter TABLE crm_coupons add  wx_coupon_state INT ;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  


DO $DO$   
DECLARE num INT;  
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'crm_coupons' and column_name='wx_coupon_code';  
IF num = 0 THEN  
		EXECUTE ' alter TABLE crm_coupons add  wx_coupon_code VARCHAR(100) ;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  



DO $DO$   
DECLARE num INT;  
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'organ' and column_name='third_organ_code';  
IF num = 0 THEN  
		EXECUTE ' ALTER TABLE organ ADD COLUMN third_organ_code  varchar(50) ;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  



CREATE TABLE if not exists "public"."wx_coupon_register" (
"tenancy_id" varchar(30) COLLATE "default",
"id" serial NOT NULL,
"coupon_class_id" int4 NOT NULL,
"card_type" varchar(100) COLLATE "default",
"brand_name" varchar(200) COLLATE "default",
"logo_url" varchar(200) COLLATE "default",
"code_type" varchar(100) COLLATE "default",
"color" varchar(60) COLLATE "default",
"notice" varchar(100) COLLATE "default",
"card_title" varchar(100) COLLATE "default",
"description" varchar(100) COLLATE "default",
"type" varchar(50) COLLATE "default",
"begin_timestamp" varchar(50) COLLATE "default",
"end_timestamp" varchar(50) COLLATE "default",
"fixed_term" varchar(50) COLLATE "default",
"fixed_begin_term" varchar(50) COLLATE "default",
"center_sub_title" varchar(100) COLLATE "default",
"custom_url_name" varchar(100) COLLATE "default",
"center_title" varchar(100) COLLATE "default",
"custom_url_sub_title" varchar(100) COLLATE "default",
"promotion_url_name" varchar(100) COLLATE "default",
"promotion_url_sub_title" varchar(100) COLLATE "default",
"get_limit" int4 NOT NULL,
"use_limit" int4 NOT NULL,
"can_use_with_other_discount" varchar(20) COLLATE "default",
"accept_category" varchar(200) COLLATE "default",
"reject_category" varchar(200) COLLATE "default",
"abstract" varchar(100) COLLATE "default",
"icon_url_list" text,
"image_url" varchar(100) COLLATE "default",
"text" text,
"time_limit" varchar(100) COLLATE "default",
"valid_state" varchar(10) COLLATE "default",
"synchro_state" int4,
"synchro_date" timestamp(6),
"success_state" int4,
"success_date" timestamp(6),
"wx_card_id" varchar(200) COLLATE "default",
"reason" text ,
"remark" text,
"last_operator" varchar(50) COLLATE "default",
"last_updatetime" timestamp(6),
CONSTRAINT "pk_wx_coupon_register" PRIMARY KEY ("id")
)
WITH (OIDS=FALSE)
;


DO $DO$   
DECLARE num INT;  
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'dynamic_code' and column_name='third_bill_code';  
IF num = 0 THEN  
    EXECUTE ' ALTER TABLE dynamic_code ADD COLUMN third_bill_code varchar(30);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$;  




CREATE TABLE IF NOT EXISTS dynamic_code_history (
"id" int4,
"card_code" varchar(100),
"create_time" timestamp(6),
"third_bill_code" varchar(100),
"delete_time" timestamp(6)
)
WITH (OIDS=FALSE)
;

DO $DO$
DECLARE num INT; 
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment' and column_name='coupon_type';  
IF num = 0 THEN  
    EXECUTE 'alter table pos_bill_payment add COLUMN coupon_type int4;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;


DO $DO$
DECLARE num INT; 
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment2' and column_name='coupon_type';  
IF num = 0 THEN  
    EXECUTE 'alter table pos_bill_payment2 add COLUMN coupon_type int4;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;


DO $DO$
DECLARE num INT; 
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment_log' and column_name='coupon_type';  
IF num = 0 THEN  
    EXECUTE 'alter table pos_bill_payment_log add COLUMN coupon_type int4;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;


DO $DO$
DECLARE num INT; 
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment_regain' and column_name='coupon_type';  
IF num = 0 THEN  
    EXECUTE 'alter table pos_bill_payment_regain add COLUMN coupon_type int4;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;


CREATE TABLE IF NOT EXISTS crm_card_class (
	tenancy_id CHARACTER VARYING (32),
	ID SERIAL NOT NULL,
	code CHARACTER VARYING (30),
	NAME CHARACTER VARYING (100),
	is_only CHARACTER VARYING (10),
	is_back CHARACTER VARYING (10),
	is_lostreport CHARACTER VARYING (10),
	is_namereport CHARACTER VARYING (10),
	valid_state CHARACTER VARYING (10),
	remark CHARACTER VARYING (100),
	last_operator CHARACTER VARYING (30),
	last_updatetime TIMESTAMP WITHOUT TIME ZONE,
	is_physical_card CHARACTER VARYING (10),
	card_sale_price NUMERIC,
	card_deposit NUMERIC,
	limit_prestore_per NUMERIC,
	commission_saler NUMERIC,
	commission_store NUMERIC,
	commision_predeposit_per NUMERIC,
	commision_predeposit_store NUMERIC,
	commision_predeposit_per2 NUMERIC,
	commision_predeposit_store2 NUMERIC,
	CONSTRAINT PK_crm_card_class PRIMARY KEY (ID)
);

DO $DO$   
DECLARE num INT;  
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'hq_item_menu_details' and column_name='is_show';  
IF num = 0 THEN  
		EXECUTE ' ALTER TABLE hq_item_menu_details ADD COLUMN is_show int4 default 1;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$; 


DO $DO$   
DECLARE num INT; 
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'cc_order_list' and column_name='third_commission_amount';  
IF num = 0 THEN  
		EXECUTE 'ALTER TABLE "public"."cc_order_list" ADD COLUMN "third_commission_amount" numeric(19,4);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;

DO $DO$
DECLARE num INT; 
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'wx_coupon_register' and column_name='description';  
IF num <> 0 THEN  
    EXECUTE 'alter table wx_coupon_register alter column description type text;';
ELSE RAISE NOTICE 'column  not exists in this table';
END IF; 
END $DO$;


DO $DO$   
DECLARE num INT;  
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'cc_order_list' and column_name='third_commission_amount';  
IF num > 0 THEN  
		EXECUTE ' ALTER TABLE cc_order_list drop COLUMN third_commission_amount;';
ELSE 
	  RAISE NOTICE 'column  not exists in this table';
END IF; 

select count(*) into num from information_schema.columns where table_name = 'cc_order_list' and column_name='calculate_commission_amount';  
IF num = 0 THEN  
	  EXECUTE ' ALTER TABLE cc_order_list add COLUMN calculate_commission_amount numeric(10,4);';
	  
ELSE 
	  RAISE NOTICE 'column already  exists in this table';
END IF; 
END$DO$;



           
           
DO $DO$   
DECLARE num INT;  
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'cc_order_list' and column_name='local_shop_real_amount';  
IF num = 0 THEN  
		EXECUTE ' ALTER TABLE cc_order_list ADD COLUMN local_shop_real_amount numeric(10,4);';
ELSE 
		RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$; 


DO $DO$
DECLARE num INT; 
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'pos_third_payment_order' and column_name='remark';  
IF num = 0 THEN  
    EXECUTE 'alter table pos_third_payment_order add COLUMN remark CHARACTER VARYING(256);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;

DO $DO$
DECLARE num INT; 
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'pos_third_payment_order' and column_name='upload_tag';  
IF num = 0 THEN  
    EXECUTE 'alter table pos_third_payment_order add COLUMN upload_tag CHARACTER VARYING(16);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;

DO $DO$
DECLARE num INT; 
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'pos_third_payment_order' and column_name='oper_type';  
IF num = 0 THEN  
    EXECUTE 'alter table pos_third_payment_order add COLUMN oper_type CHARACTER VARYING(32);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;

DO $DO$
DECLARE num INT; 
BEGIN  
    select count(*) into num from information_schema.columns where table_name = 'pos_kvs_bill_item' and column_name='is_out';  
IF num = 0 THEN  
    EXECUTE 'alter table pos_kvs_bill_item add COLUMN is_out int4;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END $DO$;

DO $DO$   
DECLARE num INT;  
BEGIN  
		select count(*) into num from information_schema.columns where table_name = 'cc_order_list' and column_name='taxpayerid';  
IF num = 0 THEN  
		EXECUTE ' ALTER TABLE cc_order_list ADD COLUMN taxpayerid varchar(30);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF; 
END$DO$; 

create table if not exists  public.pos_bill_booked (
tenancy_id varchar(32),
store_id int4,
id SERIAL,
bill_booked_num varchar(20),
batch_num varchar(20),
serial_num varchar(20),
report_date date,
guest int4,
opentable_time timestamp(6),
payment_time timestamp(6),
payment_num int4,
open_pos_num varchar(10),
pos_num varchar(10),
waiter_num varchar(20),
open_opt varchar(20),
cashier_num varchar(20),
shift_id int4,
item_menu_id int4,
service_id int4,
service_amount numeric(19,4),
service_discount int4,
order_num varchar(100),
print_time timestamp(6),
print_count int4,
subtotal numeric(19,4) DEFAULT 0,
bill_amount numeric(19,4) DEFAULT 0,
payment_amount numeric(19,4) DEFAULT 0,
difference numeric(19,4) DEFAULT 0,
discountk_amount numeric(19,4) DEFAULT 0,
discountr_amount numeric(19,4) DEFAULT 0,
maling_amount numeric(19,4) DEFAULT 0,
single_discount_amount numeric(19,4) DEFAULT 0,
discount_amount numeric(19,4) DEFAULT 0,
free_amount numeric(19,4) DEFAULT 0,
givi_amount numeric(19,4) DEFAULT 0,
more_coupon numeric(19,4) DEFAULT 0,
average_amount numeric(19,4) DEFAULT 0,
discount_num varchar(20),
discount_case_id int4,
discount_rate int4,
billfree_reason_id int4,
discount_mode_id int4,
transfer_remark varchar(100),
sale_mode varchar(20),
bill_state varchar(20),
bill_property varchar(20),
upload_tag int4 DEFAULT 0,
deposit_count int4,
copy_bill_num varchar(20),
source varchar(20),
opt_login_number int4,
guest_msg varchar(200),
integraloffset numeric(19,4),
remark varchar(500),
return_amount numeric(19,4),
advance_payment_amt numeric(19,4),
advance_refund_amt numeric(19,4),
is_refund varchar(10),
payment_id int4,
pay_no varchar(50),
third_bill_code varchar(50),
payment_state varchar(20),
payment_manager_num varchar(30),
shop_real_amount numeric(19,4),
total_fees numeric(19,4),
platform_charge_amount numeric(19,4),
settlement_type varchar(10),
bill_taste varchar(200),
recover_count int4,
load_state char(1),
CONSTRAINT pk_pos_bill_booked PRIMARY KEY (id)
)
WITH (OIDS=FALSE)
;

DO $DO$   
DECLARE num INT;  
BEGIN  
		select count(*) INTO num from pg_indexes where tablename='pos_bill_booked' and indexname='index_pos_bill_booked_bill_num'; 
IF num = 0 THEN  
		EXECUTE ' CREATE INDEX index_pos_bill_booked_bill_num ON public.pos_bill_booked USING btree (bill_booked_num);';
ELSE RAISE NOTICE 'index already exists in this table';
END IF; 
END$DO$;

DO $DO$   
DECLARE num INT;  
BEGIN  
		select count(*) INTO num from pg_indexes where tablename='pos_bill_booked' and indexname='index_pos_bill_booked_report_date'; 
IF num = 0 THEN  
		EXECUTE 'CREATE INDEX index_pos_bill_booked_report_date ON public.pos_bill_booked USING btree (report_date);';
ELSE RAISE NOTICE 'index already exists in this table';
END IF; 
END$DO$;

create table if not exists public.pos_bill_item_booked (
tenancy_id varchar(32),
store_id int4,
id  SERIAL,
rwid  SERIAL,
yrwid int4,
bill_booked_num varchar(20),
details_id int4,
item_id int4,
item_num varchar(30),
item_name varchar(100),
item_english varchar(100),
item_unit_id int4,
item_unit_name varchar(100),
stable_code varchar(50),
pushmoney_way varchar(50),
proportion numeric(19,4) DEFAULT 0,
assist_num numeric(19,4) DEFAULT 0,
assist_money numeric(19,4) DEFAULT 0,
waiter_num varchar(20),
item_price numeric(19,4) DEFAULT 0,
item_count numeric(19,4) DEFAULT 0,
item_amount numeric(19,4) DEFAULT 0,
real_amount numeric(19,4) DEFAULT 0,
discount_amount numeric(19,4) DEFAULT 0,
single_discount_amount numeric(19,4) DEFAULT 0,
discountr_amount numeric(19,4) DEFAULT 0,
discount_state varchar(20),
discount_rate numeric(19,4),
discount_mode_id int4,
item_class_id int4,
item_property varchar(20),
item_remark varchar(100),
print_tag varchar(10),
waitcall_tag int4,
setmeal_id int4,
setmeal_rwid int4,
is_setmeal_changitem varchar(10),
item_time timestamp(6),
item_serial int4,
report_date date,
item_shift_id int4,
item_mac_id varchar(10),
item_taste varchar(500),
order_remark varchar(20),
seat_num varchar(20),
ticket_num varchar(100),
sale_mode varchar(20),
gqcj_tag int4,
kvscp_tag int4,
discount_reason_id int4,
is_showitem varchar(10),
upload_tag int4 DEFAULT 0,
assist_item_id int4,
integraloffset numeric(19,4),
remark varchar(500),
setmeal_group_id int4,
group_id int4,
third_price numeric(19,4),
returngive_reason_id int4,
manager_num varchar(30),
return_type varchar(30),
return_count int4,
method_money numeric(19,4),
batch_num varchar(20),
order_number int4,
item_remark_his varchar(100),
CONSTRAINT pk_pos_bill_item_booked PRIMARY KEY (id)
)
WITH (OIDS=FALSE) ;


DO $DO$   
DECLARE num INT;  
BEGIN  
		select count(*) INTO num from pg_indexes where tablename='pos_bill_item_booked' and indexname='pos_bill_item_booked_bill_num'; 
IF num = 0 THEN  
		EXECUTE 'CREATE INDEX pos_bill_item_booked_bill_num ON public.pos_bill_item_booked USING btree (bill_booked_num, item_id);';
ELSE RAISE NOTICE 'index already exists in this table';
END IF; 
END$DO$;

DO $DO$   
DECLARE num INT;  
BEGIN  
		select count(*) INTO num from pg_indexes where tablename='pos_bill_item_booked' and indexname='pos_bill_item_booked_reportdate'; 
IF num = 0 THEN  
		EXECUTE 'CREATE INDEX pos_bill_item_booked_reportdate ON public.pos_bill_item_booked USING btree (report_date);';
ELSE RAISE NOTICE 'index already exists in this table';
END IF; 
END$DO$;

select p_addfield('pos_bill','tax_rate','numeric(19,6)');
select p_addfield('pos_bill','tax_money','numeric(19,6)');
select p_addfield('pos_bill','service_tax_rate','numeric(19,6)');
select p_addfield('pos_bill','service_tax_money','numeric(19,6)');
select p_addfield('pos_bill','tax_amount','numeric(19,6)');
select p_addfield('pos_bill','no_tax_amount','numeric(19,6)');
select p_addfield('pos_bill','payment_tax_money','numeric(19,6)');
select p_addfield('pos_bill','payment_notax','numeric(19,6)');
select p_addfield('pos_bill','bill_tax_money','numeric(19,6)');
select p_addfield('pos_bill','bill_notax','numeric(19,6)');
select p_addfield('pos_bill','service_notax','numeric(19,6)');

select p_addfield('pos_bill2','tax_rate','numeric(19,6)');
select p_addfield('pos_bill2','tax_money','numeric(19,6)');
select p_addfield('pos_bill2','service_tax_rate','numeric(19,6)');
select p_addfield('pos_bill2','service_tax_money','numeric(19,6)');
select p_addfield('pos_bill2','tax_amount','numeric(19,6)');
select p_addfield('pos_bill2','no_tax_amount','numeric(19,6)');
select p_addfield('pos_bill2','payment_tax_money','numeric(19,6)');
select p_addfield('pos_bill2','payment_notax','numeric(19,6)');
select p_addfield('pos_bill2','bill_tax_money','numeric(19,6)');
select p_addfield('pos_bill2','bill_notax','numeric(19,6)');
select p_addfield('pos_bill2','service_notax','numeric(19,6)');

select p_addfield('pos_bill_regain','tax_rate','numeric(19,6)');
select p_addfield('pos_bill_regain','tax_money','numeric(19,6)');
select p_addfield('pos_bill_regain','service_tax_rate','numeric(19,6)');
select p_addfield('pos_bill_regain','service_tax_money','numeric(19,6)');
select p_addfield('pos_bill_regain','tax_amount','numeric(19,6)');
select p_addfield('pos_bill_regain','no_tax_amount','numeric(19,6)');
select p_addfield('pos_bill_regain','payment_tax_money','numeric(19,6)');
select p_addfield('pos_bill_regain','payment_notax','numeric(19,6)');
select p_addfield('pos_bill_regain','bill_tax_money','numeric(19,6)');
select p_addfield('pos_bill_regain','bill_notax','numeric(19,6)');
select p_addfield('pos_bill_regain','service_notax','numeric(19,6)');

select p_addfield('pos_bill_item','tax_rate','numeric(19,6)');
select p_addfield('pos_bill_item','tax_money','numeric(19,6)');
select p_addfield('pos_bill_item','item_notax','numeric(19,6)');
select p_addfield('pos_bill_item','payment_tax_money','numeric(19,6)');
select p_addfield('pos_bill_item','payment_notax','numeric(19,6)');

select p_addfield('pos_bill_item2','tax_rate','numeric(19,6)');
select p_addfield('pos_bill_item2','tax_money','numeric(19,6)');
select p_addfield('pos_bill_item2','item_notax','numeric(19,6)');
select p_addfield('pos_bill_item2','payment_tax_money','numeric(19,6)');
select p_addfield('pos_bill_item2','payment_notax','numeric(19,6)');

select p_addfield('pos_bill_item_regain','tax_rate','numeric(19,6)');
select p_addfield('pos_bill_item_regain','tax_money','numeric(19,6)');
select p_addfield('pos_bill_item_regain','item_notax','numeric(19,6)');
select p_addfield('pos_bill_item_regain','payment_tax_money','numeric(19,6)');
select p_addfield('pos_bill_item_regain','payment_notax','numeric(19,6)');

create table if not exists pos_customer_operate_list (
   tenancy_id           VARCHAR(32)          null,
   store_id             INT4                 null,
   ID                   SERIAL               not null,
   business_date        DATE                 null,
   operator_id          INT4                 null,
   pos_num              VARCHAR(16)          null,
   shift_id             INT4                 null,
   operate_time         TIMESTAMP            null,
   chanel               VARCHAR(16)          null,
   service_type         VARCHAR(32)          null,
   operat_type          VARCHAR(32)          null,
   customer_id          int4                 null,
   customer_code        VARCHAR(32)          null,
   mobil                VARCHAR(16)          null,
   customer_name        VARCHAR(64)          null,
   card_class_id        int4                 null,
   card_id              int4                 null,
   card_code            VARCHAR(64)          null,
   incorporation_id     int4                 null,
   incorporation_name   VARCHAR(64)          null,
   third_bill_code      VARCHAR(32)          null,
   batch_num            VARCHAR(32)          null,
   bill_code_original   VARCHAR(32)          null,
   bill_code            VARCHAR(32)          null,
   trade_amount         NUMERIC(19,4)        null,
   trade_credit         NUMERIC(19,4)        null,
   deposit              NUMERIC(19,4)        null,
   sales_price          NUMERIC(19,4)        null,
   sales_person         VARCHAR(32)          null,
   payment_id           int4                 null,
   payment_class        VARCHAR(32)          null,
   coupons_type_id      int4                 null,
   coupons_code         VARCHAR(256)         null,
   is_invoice           VARCHAR(8)           null,
   finish_time          TIMESTAMP            null,
   payment_state        VARCHAR(8)           null,
   operate_state        VARCHAR(8)           null,
   cancel_state         VARCHAR(8)           null,
   request_status       VARCHAR(8)           null,
   request_code         VARCHAR(16)          null,
   request_msg          VARCHAR(256)         null,
   last_query_time      TIMESTAMP            null,
   query_count          int4                 null,
   action_type          VARCHAR(32)          null,
   constraint PK_POS_CUSTOMER_OPERATE_LIST primary key (ID)
);

DO $DO$
DECLARE num INT ;
BEGIN
	SELECT COUNT (*) INTO num FROM information_schema. COLUMNS WHERE TABLE_NAME = 'pos_cashier_receive_log' AND COLUMN_NAME = 'pay_shift_id' ; IF num = 0 THEN EXECUTE 'ALTER TABLE pos_cashier_receive_log ADD COLUMN pay_shift_id INTEGER' ; ELSE RAISE NOTICE 'table pos_cashier_receive_log column pay_shift_id already exists in this table ' ; END IF ;
END $DO$;

ALTER TABLE pos_third_payment_order ALTER COLUMN failure_msg TYPE CHARACTER VARYING(2000);

---无码票券start---
select p_addfield('pos_bill_payment_coupons','class_id','int4');
select p_addfield('pos_bill_payment_coupons','type_id','int4');
select p_addfield('pos_bill_payment_coupons','discount_money','numeric(19,4)');
select p_addfield('pos_bill_payment_coupons','discount_num','numeric(19,4)');
select p_addfield('pos_bill_payment_coupons','chanel','varchar(30)');
select p_addfield('pos_bill_payment_coupons','price','numeric(19,4)');
select p_addfield('pos_bill_payment_coupons','item_id','int4');
select p_addfield('pos_bill_payment_coupons','item_num','int4');
select p_addfield('pos_bill_payment_coupons','coupons_pro','varchar(30)');
select p_addfield('pos_bill_payment_coupons','coupon_type','int4');
select p_addfield('pos_bill_payment','coupon_type','int4');
select p_addfield('pos_bill_payment2','coupon_type','int4');
select p_addfield('pos_bill_payment_log','coupon_type','int4');
---无码票券end---

---营销活动start---
DO $DO$
DECLARE num INT;
BEGIN
    select count(*) into num from information_schema.columns where table_name = 'pos_bill_item' and column_name='main_item';
IF num = 0 THEN
    EXECUTE ' ALTER TABLE public.pos_bill_item ADD COLUMN main_item  character varying(32);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
    select count(*) into num from information_schema.columns where table_name = 'pos_bill_item2' and column_name='main_item';
IF num = 0 THEN
    EXECUTE ' ALTER TABLE public.pos_bill_item2 ADD COLUMN main_item  character varying(32);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;


DO $DO$
DECLARE num INT;
BEGIN
    select count(*) into num from information_schema.columns where table_name = 'pos_bill_item' and column_name='mid';
IF num = 0 THEN
    EXECUTE ' ALTER TABLE public.pos_bill_item ADD COLUMN mid character varying(32);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
    select count(*) into num from information_schema.columns where table_name = 'pos_bill_item2' and column_name='mid';
IF num = 0 THEN
    EXECUTE ' ALTER TABLE public.pos_bill_item2 ADD COLUMN mid character varying(32);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;


DO $DO$
DECLARE num INT;
BEGIN
    select count(*) into num from information_schema.columns where table_name = 'pos_bill_item' and column_name='activity_id';
IF num = 0 THEN
    EXECUTE 'ALTER TABLE public.pos_bill_item ADD COLUMN activity_id int;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
    select count(*) into num from information_schema.columns where table_name = 'pos_bill_item2' and column_name='activity_id';
IF num = 0 THEN
    EXECUTE 'ALTER TABLE public.pos_bill_item2 ADD COLUMN activity_id int;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;


DO $DO$
DECLARE num INT;
BEGIN
    select count(*) into num from information_schema.columns where table_name = 'pos_bill_item' and column_name='activity_batch_num';
IF num = 0 THEN
    EXECUTE 'ALTER TABLE public.pos_bill_item ADD COLUMN activity_batch_num character varying(32);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
    select count(*) into num from information_schema.columns where table_name = 'pos_bill_item2' and column_name='activity_batch_num';
IF num = 0 THEN
    EXECUTE 'ALTER TABLE public.pos_bill_item2 ADD COLUMN activity_batch_num character varying(32);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;


DO $DO$
DECLARE num INT;
BEGIN
    select count(*) into num from information_schema.columns where table_name = 'pos_bill_item' and column_name='activity_rule_id';
IF num = 0 THEN
    EXECUTE 'ALTER TABLE public.pos_bill_item ADD COLUMN activity_rule_id int4;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
    select count(*) into num from information_schema.columns where table_name = 'pos_bill_item2' and column_name='activity_rule_id';
IF num = 0 THEN
    EXECUTE 'ALTER TABLE public.pos_bill_item2 ADD COLUMN activity_rule_id int4;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;


DO $DO$
DECLARE num INT;
BEGIN
    select count(*) into num from information_schema.columns where table_name = 'pos_bill_item' and column_name='activity_count';
IF num = 0 THEN
    EXECUTE 'ALTER TABLE public.pos_bill_item ADD COLUMN activity_count int;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;


DO $DO$
DECLARE num INT;
BEGIN
    select count(*) into num from information_schema.columns where table_name = 'pos_bill_item2' and column_name='activity_count';
IF num = 0 THEN
    EXECUTE 'ALTER TABLE public.pos_bill_item2 ADD COLUMN activity_count int;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;

CREATE TABLE if not exists  public.pos_item_discount_list
(
  tenancy_id character varying(32) NOT NULL,
  id serial NOT NULL,
  store_id int,
  report_date date,
  bill_num character varying(20),
  batch_num character varying(32),
  bill_status character varying(10),
  item_id integer,
  rwid integer,
  active_id integer,
  rule_id integer,
  discount_amount numeric(19,4) DEFAULT 0,
  last_update_time timestamp without time zone,
  upload_tag integer,
  CONSTRAINT pk_pos_item_discount_list PRIMARY KEY (id)
)
WITH (
  OIDS=FALSE
);

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_item' and column_name='is_consignment';
IF num = 0 THEN
		EXECUTE ' ALTER TABLE pos_bill_item ADD COLUMN is_consignment int4;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_item' and column_name='settlement_price';
IF num = 0 THEN
		EXECUTE ' ALTER TABLE pos_bill_item ADD COLUMN settlement_price numeric(19,4);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill' and column_name='settlement_price';
IF num = 0 THEN
		EXECUTE ' ALTER TABLE pos_bill ADD COLUMN settlement_price numeric(19,4);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_item2' and column_name='is_consignment';
IF num = 0 THEN
		EXECUTE ' ALTER TABLE pos_bill_item2 ADD COLUMN is_consignment int4;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_item2' and column_name='settlement_price';
IF num = 0 THEN
		EXECUTE ' ALTER TABLE pos_bill_item2 ADD COLUMN settlement_price numeric(19,4);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill2' and column_name='settlement_price';
IF num = 0 THEN
		EXECUTE ' ALTER TABLE pos_bill2 ADD COLUMN settlement_price numeric(19,4);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;
---营销活动end---

---秒付start---
DO $DO$
DECLARE num INT;
BEGIN
    select count(*) into num from information_schema.columns where table_name = 'quick_pass_log' and column_name='jzid_credit';
IF num = 0 THEN
    EXECUTE 'ALTER TABLE quick_pass_log ADD jzid_credit int;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
    select count(*) into num from information_schema.columns where table_name = 'quick_pass_log' and column_name='credit';
IF num = 0 THEN
    EXECUTE 'ALTER TABLE quick_pass_log ADD credit NUMERIC(19,4);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
    select count(*) into num from information_schema.columns where table_name = 'quick_pass_log' and column_name='arrivedamount';
IF num = 0 THEN
    EXECUTE 'ALTER TABLE quick_pass_log ADD arrivedamount  NUMERIC(19,4) ;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
    select count(*) into num from information_schema.columns where table_name = 'quick_pass_log' and column_name='mem';
IF num = 0 THEN
    EXECUTE 'ALTER TABLE quick_pass_log ADD mem int ;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;
---秒付end---

---代销品start---
DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'hq_item_menu_details' and column_name='settlement_price';
IF num = 0 THEN
		EXECUTE ' ALTER TABLE hq_item_menu_details ADD COLUMN settlement_price numeric(19,4);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'hq_item_menu_details' and column_name='is_consignment';
IF num = 0 THEN
		EXECUTE ' ALTER TABLE hq_item_menu_details ADD COLUMN is_consignment int4;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;
---代销品end---

---预打单增加付款二维码打印start---
select p_addfield('pos_bill_payment','yjzid','int4');
select p_addfield('pos_bill_payment2','yjzid','int4');
select p_addfield('pos_bill_payment_regain','yjzid','int4');
select p_addfield('pos_third_payment_order','pay_type','varchar(32)');
---预打单增加付款二维码打印end---

---单品折扣start---
DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_item' and column_name='single_amount';
IF num = 0 THEN
		EXECUTE ' ALTER TABLE pos_bill_item ADD COLUMN single_amount numeric(19,4) default 0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_item2' and column_name='single_amount';
IF num = 0 THEN
		EXECUTE ' ALTER TABLE pos_bill_item2 ADD COLUMN single_amount numeric(19,4) default 0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_regain' and column_name='single_amount';
IF num = 0 THEN
		EXECUTE ' ALTER TABLE pos_bill_regain ADD COLUMN single_amount numeric(19,4) default 0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;
---单品折扣end---

---菜品描述start---
DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'hq_item_info' and column_name='item_description';
IF num = 0 THEN
		EXECUTE ' ALTER TABLE hq_item_info ADD COLUMN item_description text;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;
---菜品描述end---

-----套餐数量可选状态start---
DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'hq_item_combo_details' and column_name='change_num_state';
IF num = 0 THEN
		EXECUTE ' ALTER TABLE hq_item_combo_details ADD COLUMN change_num_state varchar(10);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;
-----套餐数量可选状态end---

---- 物理打印机序号表start---
CREATE TABLE if not exists  public.pos_printer_serial_number
(
  tenancy_id character varying(32) NOT NULL,
  id serial NOT NULL,
  store_id int,
  report_date date,
  ip_com character varying(50),
  serial_number int,
  last_update_time timestamp without time zone,
  CONSTRAINT pk_pos_printer_serial_number PRIMARY KEY (id)
)
WITH (
  OIDS=FALSE
);
---- 物理打印机序号表end---

---- 新打印任务表，新增序号字段start---
DO $DO$
DECLARE num INT;
BEGIN
    select count(*) into num from information_schema.columns where table_name = 'pos_print_task' and column_name='serial_number';
IF num = 0 THEN
    EXECUTE ' ALTER TABLE public.pos_print_task ADD COLUMN serial_number int;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;
---- 新打印任务表，新增序号字段end---

---微信大众营销start---
DO $DO$
DECLARE num INT;
BEGIN
    select count(*) into num from information_schema.columns where table_name = 'cc_order_item' and column_name='activity_id';
IF num = 0 THEN
    EXECUTE 'ALTER TABLE public.cc_order_item ADD COLUMN activity_id varchar(50);';
ELSE RAISE NOTICE 'column activity_id already exists in cc_order_item';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
    select count(*) into num from information_schema.columns where table_name = 'cc_order_item' and column_name='activity_rule_id';
IF num = 0 THEN
    EXECUTE 'ALTER TABLE public.cc_order_item ADD COLUMN activity_rule_id varchar(50);';
ELSE RAISE NOTICE 'column activity_rule_id already exists in cc_order_item';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
    select count(*) into num from information_schema.columns where table_name = 'cc_order_item' and column_name='activity_count';
IF num = 0 THEN
    EXECUTE 'ALTER TABLE public.cc_order_item ADD COLUMN activity_count int;';
ELSE RAISE NOTICE 'column activity_count already exists in cc_order_item';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'cc_order_list' and column_name='vip_price';
IF num = 0 THEN
		EXECUTE ' ALTER TABLE cc_order_list ADD COLUMN vip_price numeric(19,4) DEFAULT 0;';
ELSE RAISE NOTICE 'column vip_price already exists in this table';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'cc_order_item' and column_name='vip_price';
IF num = 0 THEN
		EXECUTE ' ALTER TABLE cc_order_item ADD COLUMN vip_price numeric(19,4) DEFAULT 0;';
ELSE RAISE NOTICE 'column vip_price already exists in this table';
END IF;
END$DO$;
---微信大众营销end---

---新打印任务表新增序号字段start---
DO $DO$
DECLARE num INT;
BEGIN
    select count(*) into num from information_schema.columns where table_name = 'pos_bill_item' and column_name='print_count';
IF num = 0 THEN
    EXECUTE ' ALTER TABLE public.pos_bill_item ADD COLUMN print_count numeric(19,4);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;


DO $DO$
DECLARE num INT;
BEGIN
    select count(*) into num from information_schema.columns where table_name = 'pos_bill_item2' and column_name='print_count';
IF num = 0 THEN
    EXECUTE ' ALTER TABLE public.pos_bill_item2 ADD COLUMN print_count numeric(19,4);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;
---新打印任务表新增序号字段end---

---会员发卡添加付款方式start---
DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_customer_operate_list' and column_name='third_code';
IF num = 0 THEN
		EXECUTE ' ALTER TABLE pos_customer_operate_list ADD COLUMN third_code CHARACTER VARYING(64);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_customer_operate_list' and column_name='extend_param';
IF num = 0 THEN
		EXECUTE ' ALTER TABLE pos_customer_operate_list ADD COLUMN extend_param CHARACTER VARYING(2000);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;
---会员发卡添加付款方式end---

---微生活重试区分唯一记录添加third_bill_code_timestamp start---
DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_customer_operate_list' and column_name='third_bill_code_timestamp';
IF num = 0 THEN
		EXECUTE ' ALTER TABLE pos_customer_operate_list ADD COLUMN third_bill_code_timestamp CHARACTER VARYING(64);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;
---微生活重试区分唯一记录添加third_bill_code_timestamp end---

---默认菜品规则start---
DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_item' and column_name='default_state';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_item ADD COLUMN default_state varchar(20) default ''N'';';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_item2' and column_name='default_state';
IF num = 0 THEN
		EXECUTE ' ALTER TABLE pos_bill_item2 ADD COLUMN default_state varchar(20);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_item_regain' and column_name='default_state';
IF num = 0 THEN
		EXECUTE ' ALTER TABLE pos_bill_item_regain ADD COLUMN default_state varchar(20) default ''N'';';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;
---默认菜品规则end---

----- pos_bill_regain表中新增字段
DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_regain' and column_name='settlement_price';
IF num = 0 THEN
		EXECUTE ' ALTER TABLE pos_bill_regain ADD COLUMN settlement_price numeric(19,4);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;

---- pos_bill_item_regain表中新增字段start---
DO $DO$
DECLARE num INT;
BEGIN
    select count(*) into num from information_schema.columns where table_name = 'pos_bill_item_regain' and column_name='print_count';
IF num = 0 THEN
    EXECUTE ' ALTER TABLE public.pos_bill_item_regain ADD COLUMN print_count numeric(19,4);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_item_regain' and column_name='is_consignment';
IF num = 0 THEN
		EXECUTE ' ALTER TABLE pos_bill_item_regain ADD COLUMN is_consignment int4;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_item_regain' and column_name='settlement_price';
IF num = 0 THEN
		EXECUTE ' ALTER TABLE pos_bill_item_regain ADD COLUMN settlement_price numeric(19,4);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
    select count(*) into num from information_schema.columns where table_name = 'pos_bill_item_regain' and column_name='activity_count';
IF num = 0 THEN
    EXECUTE 'ALTER TABLE public.pos_bill_item_regain ADD COLUMN activity_count int;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
    select count(*) into num from information_schema.columns where table_name = 'pos_bill_item_regain' and column_name='activity_rule_id';
IF num = 0 THEN
    EXECUTE 'ALTER TABLE public.pos_bill_item_regain ADD COLUMN activity_rule_id int4;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
    select count(*) into num from information_schema.columns where table_name = 'pos_bill_item_regain' and column_name='activity_batch_num';
IF num = 0 THEN
    EXECUTE 'ALTER TABLE public.pos_bill_item_regain ADD COLUMN activity_batch_num character varying(20);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
    select count(*) into num from information_schema.columns where table_name = 'pos_bill_item_regain' and column_name='activity_id';
IF num = 0 THEN
    EXECUTE 'ALTER TABLE public.pos_bill_item_regain ADD COLUMN activity_id int;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
    select count(*) into num from information_schema.columns where table_name = 'pos_bill_item_regain' and column_name='mid';
IF num = 0 THEN
    EXECUTE ' ALTER TABLE public.pos_bill_item_regain ADD COLUMN mid character varying(32);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
    select count(*) into num from information_schema.columns where table_name = 'pos_bill_item_regain' and column_name='main_item';
IF num = 0 THEN
    EXECUTE ' ALTER TABLE public.pos_bill_item_regain ADD COLUMN main_item  character varying(32);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;
---- pos_bill_item_regain表中新增字段end---

--------------------update_sql_version:20171107--------------------

---组合套餐start#OE5104-----
DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_item' and column_name='combo_prop';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_item ADD COLUMN combo_prop int4;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_item2' and column_name='combo_prop';
IF num = 0 THEN
		EXECUTE ' ALTER TABLE pos_bill_item2 ADD COLUMN combo_prop int4;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
    select count(*) into num from information_schema.columns where table_name = 'pos_bill_item_regain' and column_name='combo_prop';
IF num = 0 THEN
    EXECUTE ' ALTER TABLE pos_bill_item_regain ADD COLUMN combo_prop int4;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;

--组合套餐使用的账单明细表
create table if not exists pos_bill_item_combo(
"tenancy_id" varchar(32),
"store_id" int4,
"id" int4  NOT NULL,
"rwid" int4 NOT NULL,
"yrwid" int4,
"bill_num" varchar(20),
"details_id" int4,
"item_id" int4,
"item_num" varchar(30),
"item_name" varchar(100),
"item_english" varchar(100),
"item_unit_id" int4,
"item_unit_name" varchar(100),
"stable_code" varchar(50),
"table_code" varchar(50),
"pushmoney_way" varchar(50),
"proportion" numeric(19,4) DEFAULT 0,
"assist_num" numeric(19,4) DEFAULT 0,
"assist_money" numeric(19,4) DEFAULT 0,
"waiter_num" varchar(20),
"item_price" numeric(19,4) DEFAULT 0,
"item_count" numeric(19,4) DEFAULT 0,
"item_amount" numeric(19,4) DEFAULT 0,
"real_amount" numeric(19,4) DEFAULT 0,
"discount_amount" numeric(19,4) DEFAULT 0,
"single_discount_amount" numeric(19,4) DEFAULT 0,
"discountr_amount" numeric(19,4) DEFAULT 0,
"discount_state" varchar(20),
"discount_rate" numeric(19,4),
"discount_mode_id" int4,
"item_class_id" int4,
"item_property" varchar(20),
"item_remark" varchar(100),
"print_tag" varchar(10),
"waitcall_tag" int4,
"setmeal_id" int4,
"setmeal_rwid" int4,
"is_setmeal_changitem" varchar(10),
"item_time" timestamp(6),
"item_serial" int4,
"report_date" date,
"item_shift_id" int4,
"item_mac_id" varchar(10),
"item_taste" varchar(500),
"order_remark" varchar(20),
"seat_num" varchar(20),
"ticket_num" varchar(100),
"sale_mode" varchar(20),
"gqcj_tag" int4,
"kvscp_tag" int4,
"discount_reason_id" int4,
"is_showitem" varchar(10),
"upload_tag" int4 DEFAULT 0,
"assist_item_id" int4,
"integraloffset" numeric(19,4),
"remark" varchar(500),
"setmeal_group_id" int4,
"group_id" int4,
"third_price" numeric(19,4),
"returngive_reason_id" int4,
"manager_num" varchar(30),
"return_type" varchar(30),
"return_count" int4,
"method_money" numeric(19,4),
"batch_num" varchar(20),
"order_number" int4,
"item_remark_his" varchar(100),
"opt_num" varchar(50),
"tax_rate" numeric(19,6),
"tax_money" numeric(19,6),
"item_notax" numeric(19,6),
"payment_tax_money" numeric(19,6),
"payment_notax" numeric(19,6),
"main_item" varchar(32),
"mid" varchar(32),
"activity_id" int4,
"activity_batch_num" varchar(20),
"activity_rule_id" int4,
"activity_count" int4,
"is_consignment" int4,
"settlement_price" numeric(19,4),
"single_amount" numeric(19,4) DEFAULT 0,
"print_count" numeric(19,4),
"default_state" varchar(20) DEFAULT 'N'::character varying,
"combo_prop" int4
)
WITH (OIDS=FALSE);
---组合套餐end#OE5104-----

---微生活重试区分唯一记录添加third_bill_code_timestamp start---
DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_customer_operate_list' and column_name='third_bill_code_timestamp';
IF num = 0 THEN
		EXECUTE ' ALTER TABLE pos_customer_operate_list ADD COLUMN third_bill_code_timestamp CHARACTER VARYING(64);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;
---微生活重试区分唯一记录添加third_bill_code_timestamp end---

--------------------update_sql_version:20171114--------------------

---E待客菜品是否已上OE#1514 start---
DO $DO$
DECLARE num INT;
BEGIN
   select count(*) into num from information_schema.columns where table_name = 'pos_bill_item' and column_name='served_state';
   IF num = 0 THEN
      EXECUTE 'ALTER TABLE pos_bill_item ADD COLUMN served_state int4;';
   ELSE RAISE NOTICE 'column  already exists in this table';
   END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
   select count(*) into num from information_schema.columns where table_name = 'pos_bill_item2' and column_name='served_state';
   IF num = 0 THEN
      EXECUTE ' ALTER TABLE pos_bill_item2 ADD COLUMN served_state int4;';
   ELSE RAISE NOTICE 'column  already exists in this table';
   END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
   select count(*) into num from information_schema.columns where table_name = 'pos_bill_item_regain' and column_name='served_state';
   IF num = 0 THEN
      EXECUTE ' ALTER TABLE pos_bill_item_regain ADD COLUMN served_state int4;';
   ELSE RAISE NOTICE 'column  already exists in this table';
   END IF;
END$DO$;
---E待客菜品是否已上 end----

---转台操作日志表start---
CREATE TABLE IF NOT EXISTS "public"."pos_table_log" (
"id" serial NOT NULL,
"tenancy_id" varchar(32),
"store_id" int4,
"from_bill_num" varchar(20),
"to_bill_num" varchar(20),
"pos_num" varchar(10),
"opt_num" varchar(20),
"opt_name" varchar(20),
"from_table_code" varchar(50),
"to_table_code" varchar(50),
"table_count" int4 DEFAULT 0,
"types" int4 DEFAULT 0,
"item_id" int4,
"item_name" varchar(100),
"item_count" numeric(19,4) DEFAULT 0,
"upload_tag" int4 DEFAULT 0,
"update_time" timestamp(6),
"remark" varchar(500),
"report_date" date,

constraint PK_POS_TABLE_LOG primary key (id)
)
WITH (OIDS=FALSE);
---转台操作日志表end---

--KVS添加数据上传star--
DO $DO$
DECLARE num INT;
BEGIN
   select count(*) into num from information_schema.columns where table_name = 'pos_kvs_bill' and column_name='upload_tag';
   IF num = 0 THEN
      EXECUTE ' ALTER TABLE pos_kvs_bill ADD COLUMN upload_tag int4;';
   ELSE RAISE NOTICE 'column  already exists in this table';
   END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
   select count(*) into num from information_schema.columns where table_name = 'pos_kvs_bill_item' and column_name='upload_tag';
   IF num = 0 THEN
      EXECUTE ' ALTER TABLE pos_kvs_bill_item ADD COLUMN upload_tag int4;';
   ELSE RAISE NOTICE 'column  already exists in this table';
   END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
   select count(*) into num from information_schema.columns where table_name = 'pos_kvs_bill' and column_name='report_date';
   IF num = 0 THEN
      EXECUTE ' ALTER TABLE pos_kvs_bill ADD COLUMN report_date date;';
   ELSE RAISE NOTICE 'column  already exists in this table';
   END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
   select count(*) into num from information_schema.columns where table_name = 'pos_kvs_bill_item' and column_name='report_date';
   IF num = 0 THEN
      EXECUTE ' ALTER TABLE pos_kvs_bill_item ADD COLUMN report_date date;';
   ELSE RAISE NOTICE 'column  already exists in this table';
   END IF;
END$DO$;

--KVS添加数据上传end--

--------------------update_sql_version:20171128--------------------

--添加折扣操作人,以及折扣原因字段star-
--肖恒
--2017-11-30
--注意:总部需要执行
DO $DO$
DECLARE num INT;
BEGIN
   select count(*) into num from information_schema.columns where table_name = 'pos_bill' and column_name='discount_reason_id';
   IF num = 0 THEN
      EXECUTE ' ALTER TABLE pos_bill ADD COLUMN discount_reason_id int4;';
   ELSE RAISE NOTICE 'column  already exists in this table';
   END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
   select count(*) into num from information_schema.columns where table_name = 'pos_bill2' and column_name='discount_reason_id';
   IF num = 0 THEN
      EXECUTE ' ALTER TABLE pos_bill2 ADD COLUMN discount_reason_id int4;';
   ELSE RAISE NOTICE 'column  already exists in this table';
   END IF;
END$DO$;


DO $DO$
DECLARE num INT;
BEGIN
   select count(*) into num from information_schema.columns where table_name = 'pos_bill_regain' and column_name='discount_reason_id';
   IF num = 0 THEN
      EXECUTE ' ALTER TABLE pos_bill_regain ADD COLUMN discount_reason_id int4;';
   ELSE RAISE NOTICE 'column  already exists in this table';
   END IF;
END$DO$;


DO $DO$
DECLARE num INT;
BEGIN
   select count(*) into num from information_schema.columns where table_name = 'pos_bill_item' and column_name='discount_num';
   IF num = 0 THEN
      EXECUTE ' ALTER TABLE pos_bill_item ADD COLUMN discount_num character varying(32);';
   ELSE RAISE NOTICE 'column  already exists in this table';
   END IF;
END$DO$;


DO $DO$
DECLARE num INT;
BEGIN
   select count(*) into num from information_schema.columns where table_name = 'pos_bill_item2' and column_name='discount_num';
   IF num = 0 THEN
      EXECUTE ' ALTER TABLE pos_bill_item2 ADD COLUMN discount_num character varying(32);';
   ELSE RAISE NOTICE 'column  already exists in this table';
   END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
   select count(*) into num from information_schema.columns where table_name = 'pos_bill_item_regain' and column_name='discount_num';
   IF num = 0 THEN
      EXECUTE ' ALTER TABLE pos_bill_item_regain ADD COLUMN discount_num character varying(32);';
   ELSE RAISE NOTICE 'column  already exists in this table';
   END IF;
END$DO$;
--添加折扣操作人,以及折扣原因字段end-

---半分菜需求pos_bill_item添加count_rate字段start---
DO $DO$
DECLARE num INT;
BEGIN
select count(1) into num from information_schema.columns where table_name = 'pos_bill_item' and column_name='count_rate';
IF num = 0 THEN
EXECUTE 'ALTER TABLE pos_bill_item ADD COLUMN count_rate numeric(19,4) DEFAULT 1;';
ELSE RAISE NOTICE 'column already exists in this table';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
select count(1) into num from information_schema.columns where table_name = 'pos_bill_item2' and column_name='count_rate';
IF num = 0 THEN
EXECUTE 'ALTER TABLE pos_bill_item2 ADD COLUMN count_rate numeric(19,4) DEFAULT 1;';
ELSE RAISE NOTICE 'column already exists in this table';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
select count(1) into num from information_schema.columns where table_name = 'pos_bill_item_regain' and column_name='count_rate';
IF num = 0 THEN
EXECUTE 'ALTER TABLE pos_bill_item_regain ADD COLUMN count_rate numeric(19,4) DEFAULT 1;';
ELSE RAISE NOTICE 'column already exists in this table';
END IF;
END$DO$;
---半分菜需求pos_bill_item添加count_rate字段end--

---外卖自提cc_order_list添加pick_type字段start---
DO $DO$
DECLARE num INT;
BEGIN
select count(1) into num from information_schema.columns where table_name = 'cc_order_list' and column_name='pick_type';
IF num = 0 THEN
EXECUTE ' ALTER TABLE cc_order_list ADD COLUMN pick_type int4;';
ELSE RAISE NOTICE 'column already exists in this table';
END IF;
END$DO$;
---外卖自提cc_order_list添加pick_type字段end---

--- 添加单品折扣率字段 start---
--- 2017-12-12 ---
--- 肖恒 ---
DO $DO$
DECLARE num INT;
BEGIN
   select count(*) into num from information_schema.columns where table_name = 'pos_bill_item' and column_name='single_discount_rate';
   IF num = 0 THEN
      EXECUTE ' ALTER TABLE pos_bill_item ADD COLUMN single_discount_rate numeric(19,4);';
   ELSE RAISE NOTICE 'column  already exists in this table';
   END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
   select count(*) into num from information_schema.columns where table_name = 'pos_bill_item2' and column_name='single_discount_rate';
   IF num = 0 THEN
      EXECUTE ' ALTER TABLE pos_bill_item2 ADD COLUMN single_discount_rate numeric(19,4);';
   ELSE RAISE NOTICE 'column  already exists in this table';
   END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
   select count(*) into num from information_schema.columns where table_name = 'pos_bill_item_regain' and column_name='single_discount_rate';
   IF num = 0 THEN
      EXECUTE ' ALTER TABLE pos_bill_item_regain ADD COLUMN single_discount_rate numeric(19,4);';
   ELSE RAISE NOTICE 'column  already exists in this table';
   END IF;
END$DO$;
---添加单品折扣率字段end---

--- 交班添加uid关联字段 start---
--- 2017-12-15 ---
--- 肖恒 ---
DO $DO$
DECLARE num INT;
BEGIN
   select count(*) into num from information_schema.columns where table_name = 'pos_opter_changshift_main' and column_name='changshift_uid';
   IF num = 0 THEN
      EXECUTE ' ALTER TABLE pos_opter_changshift_main ADD COLUMN changshift_uid CHARACTER VARYING(32);';
   ELSE RAISE NOTICE 'column  already exists in this table';
   END IF;
END$DO$;

ALTER TABLE pos_opter_changshift ALTER COLUMN changshift_id TYPE CHARACTER VARYING(32);
ALTER TABLE pos_opter_changshift_customer ALTER COLUMN changshift_id TYPE CHARACTER VARYING(32);
ALTER TABLE pos_opter_paidrefund ALTER COLUMN changshift_id TYPE CHARACTER VARYING(32);
--- 交班添加uid关联字段 end---

---云POS锁账单表start---

DROP TABLE IF EXISTS pos_bill_lock;
CREATE TABLE IF NOT EXISTS "public"."pos_bill_lock" (
   "id" serial NOT NULL,
   "tenancy_id" varchar(32),
   "store_id" int4,
   "bill_num" varchar(48),
   "lock_type" varchar(48),
   "lock_state" int4,
   "open_id" varchar(48),
   "customer_id" int4,
   "lock_time" timestamp(0),
   "unlock_time" timestamp(0),
   "unlock_type" varchar(48),
   "bill_state" int4,
   CONSTRAINT "pk_pos_bill_lock" PRIMARY KEY ("id")
)
WITH (OIDS=FALSE);

---云POS锁账单表end---

--- 账单会员操作表(pos_bill_member)添加会员储值余额字段   ---start---
--- 2017-12-25 ---
--- 肖恒 ---
--- pos_bill_member ---
DO $DO$
DECLARE num INT;
BEGIN
   select count(*) into num from information_schema.columns where table_name = 'pos_bill_member' and column_name='consume_before_main_balance';
   IF num = 0 THEN
      EXECUTE ' ALTER TABLE pos_bill_member ADD COLUMN consume_before_main_balance numeric(19,4);';
   ELSE RAISE NOTICE 'column  already exists in this table';
   END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
   select count(*) into num from information_schema.columns where table_name = 'pos_bill_member' and column_name='consume_before_reward_balance';
   IF num = 0 THEN
      EXECUTE ' ALTER TABLE pos_bill_member ADD COLUMN consume_before_reward_balance numeric(19,4);';
   ELSE RAISE NOTICE 'column  already exists in this table';
   END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
   select count(*) into num from information_schema.columns where table_name = 'pos_bill_member' and column_name='consume_after_main_balance';
   IF num = 0 THEN
      EXECUTE ' ALTER TABLE pos_bill_member ADD COLUMN consume_after_main_balance numeric(19,4);';
   ELSE RAISE NOTICE 'column  already exists in this table';
   END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
   select count(*) into num from information_schema.columns where table_name = 'pos_bill_member' and column_name='consume_after_reward_balance';
   IF num = 0 THEN
      EXECUTE ' ALTER TABLE pos_bill_member ADD COLUMN consume_after_reward_balance numeric(19,4);';
   ELSE RAISE NOTICE 'column  already exists in this table';
   END IF;
END$DO$;

--- pos_bill_member_regain---
DO $DO$
DECLARE num INT;
BEGIN
   select count(*) into num from information_schema.columns where table_name = 'pos_bill_member_regain' and column_name='consume_before_main_balance';
   IF num = 0 THEN
      EXECUTE ' ALTER TABLE pos_bill_member_regain ADD COLUMN consume_before_main_balance numeric(19,4);';
   ELSE RAISE NOTICE 'column  already exists in this table';
   END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
   select count(*) into num from information_schema.columns where table_name = 'pos_bill_member_regain' and column_name='consume_before_reward_balance';
   IF num = 0 THEN
      EXECUTE ' ALTER TABLE pos_bill_member_regain ADD COLUMN consume_before_reward_balance numeric(19,4);';
   ELSE RAISE NOTICE 'column  already exists in this table';
   END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
   select count(*) into num from information_schema.columns where table_name = 'pos_bill_member_regain' and column_name='consume_after_main_balance';
   IF num = 0 THEN
      EXECUTE ' ALTER TABLE pos_bill_member_regain ADD COLUMN consume_after_main_balance numeric(19,4);';
   ELSE RAISE NOTICE 'column  already exists in this table';
   END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
   select count(*) into num from information_schema.columns where table_name = 'pos_bill_member_regain' and column_name='consume_after_reward_balance';
   IF num = 0 THEN
      EXECUTE ' ALTER TABLE pos_bill_member_regain ADD COLUMN consume_after_reward_balance numeric(19,4);';
   ELSE RAISE NOTICE 'column  already exists in this table';
   END IF;
END$DO$;
--- 账单会员操作表(pos_bill_member)添加会员储值余额字段   ---end---


---订单相关表添加unit_name和item_name start---
DO $DO$
DECLARE num INT;
BEGIN
select count(1) into num from information_schema.columns where table_name = 'cc_order_item' and column_name='unit_name';
IF num = 0 THEN
EXECUTE 'ALTER TABLE cc_order_item ADD COLUMN unit_name varchar(100);';
ELSE RAISE NOTICE 'column already exists in this table';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
select count(1) into num from information_schema.columns where table_name = 'cc_order_item_retain' and column_name='unit_name';
IF num = 0 THEN
EXECUTE 'ALTER TABLE cc_order_item_retain ADD COLUMN unit_name varchar(100);';
ELSE RAISE NOTICE 'column already exists in this table';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
select count(1) into num from information_schema.columns where table_name = 'cc_order_item_details' and column_name='unit_name';
IF num = 0 THEN
EXECUTE 'ALTER TABLE cc_order_item_details ADD COLUMN unit_name varchar(100);';
ELSE RAISE NOTICE 'column already exists in this table';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
select count(1) into num from information_schema.columns where table_name = 'cc_order_item_details_retain' and column_name='unit_name';
IF num = 0 THEN
EXECUTE 'ALTER TABLE cc_order_item_details_retain ADD COLUMN unit_name varchar(100);';
ELSE RAISE NOTICE 'column already exists in this table';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
select count(1) into num from information_schema.columns where table_name = 'cc_order_item_details' and column_name='item_name';
IF num = 0 THEN
EXECUTE 'ALTER TABLE cc_order_item_details ADD COLUMN item_name varchar(100);';
ELSE RAISE NOTICE 'column already exists in this table';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
select count(1) into num from information_schema.columns where table_name = 'cc_order_item_details_retain' and column_name='item_name';
IF num = 0 THEN
EXECUTE 'ALTER TABLE cc_order_item_details_retain ADD COLUMN item_name varchar(100);';
ELSE RAISE NOTICE 'column already exists in this table';
END IF;
END$DO$;
---订单相关表添加unit_name和item_name end---
--------------------update_sql_v20171219b(build-20171228)--------------------

--- pos_bill添加就餐类型字段
select p_addfield('pos_bill','dinner_type','varchar(20)');
select p_addfield('pos_bill2','dinner_type','varchar(20)');

---pos_kvs_bill添加need_call字段
DO $DO$
DECLARE num INT;
BEGIN
select count(1) into num from information_schema.columns where table_name = 'pos_kvs_bill' and column_name='need_call';
IF num = 0 THEN
EXECUTE 'ALTER TABLE pos_kvs_bill ADD COLUMN need_call varchar(32) DEFAULT ''Y'';';
ELSE RAISE NOTICE 'column already exists in this table';
END IF;
END$DO$;

---pos_bill_item表添加字段origin_item_price:菜品原单价	start---
---肖恒
---2018-01-15
DO $DO$
DECLARE num INT;
BEGIN
select count(1) into num from information_schema.columns where table_name = 'pos_bill_item' and column_name='origin_item_price';
IF num = 0 THEN
EXECUTE 'ALTER TABLE pos_bill_item ADD COLUMN origin_item_price numeric(19,4);';
ELSE RAISE NOTICE 'column already exists in this table';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
select count(1) into num from information_schema.columns where table_name = 'pos_bill_item2' and column_name='origin_item_price';
IF num = 0 THEN
EXECUTE 'ALTER TABLE pos_bill_item2 ADD COLUMN origin_item_price numeric(19,4);';
ELSE RAISE NOTICE 'column already exists in this table';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
select count(1) into num from information_schema.columns where table_name = 'pos_bill_item_regain' and column_name='origin_item_price';
IF num = 0 THEN
EXECUTE 'ALTER TABLE pos_bill_item_regain ADD COLUMN origin_item_price numeric(19,4);';
ELSE RAISE NOTICE 'column already exists in this table';
END IF;
END$DO$;
---pos_bill_item表添加字段origin_item_price:菜品原单价	end---


--- 2018-01-17
--- 王国照
--- pos_bill_sub/pos_bill_sub2/pos_bill_sub_regain 表 添加账单副表 start
DROP TABLE IF EXISTS pos_bill_sub;
CREATE TABLE "public"."pos_bill_sub" (
"tenancy_id" varchar(32),
"store_id" int4,
"id" serial NOT NULL,
"bill_num" varchar(20),
"report_date" date,
"create_time" timestamp(6),
"handle_client" varchar(32),
"pay_client" varchar(32),
"sync_state" varchar(32),
"query_time" timestamp(6),
CONSTRAINT "pk_pos_bill_sub" PRIMARY KEY ("id")
)
WITH (OIDS=FALSE)
;

DROP TABLE IF EXISTS pos_bill_sub2;
CREATE TABLE "public"."pos_bill_sub2" (
"tenancy_id" varchar(32),
"store_id" int4,
"id" serial NOT NULL,
"bill_num" varchar(20),
"report_date" date,
"create_time" timestamp(6),
"handle_client" varchar(32),
"pay_client" varchar(32),
"sync_state" varchar(32),
"query_time" timestamp(6),
CONSTRAINT "pk_pos_bill_sub2" PRIMARY KEY ("id")
)
WITH (OIDS=FALSE)
;

DROP TABLE IF EXISTS pos_bill_sub_regain;
CREATE TABLE "public"."pos_bill_sub_regain" (
"tenancy_id" varchar(32),
"store_id" int4,
"id" serial NOT NULL,
"bill_num" varchar(20),
"report_date" date,
"create_time" timestamp(6),
"handle_client" varchar(32),
"pay_client" varchar(32),
"sync_state" varchar(32),
"query_time" timestamp(6),
CONSTRAINT "pk_pos_bill_sub_regain" PRIMARY KEY ("id")
)
WITH (OIDS=FALSE)
;
--- pos_bill_sub/pos_bill_sub2/pos_bill_sub_regain 表 添加账单副表 end

--- pos_third_payment_order 表 添加'预支付交易会话标识'
DO $DO$
DECLARE num INT;
BEGIN
   select count(*) into num from information_schema.columns where table_name = 'pos_third_payment_order' and column_name='prepay_id';
   IF num = 0 THEN
      EXECUTE ' ALTER TABLE pos_third_payment_order ADD COLUMN prepay_id character varying(64);';
   ELSE RAISE NOTICE 'column  already exists in this table';
   END IF;
END$DO$;

-------------- pos_bill pos_bill2 pos_bill_payment  pos_bill_payment2  pos_bill_payment_coupons 新增第三方券对账字段start------------

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill' and column_name='coupon_buy_price';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill ADD COLUMN coupon_buy_price numeric(19,6) DEFAULT 0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill' and column_name='due';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill ADD COLUMN due numeric(19,6) DEFAULT 0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill' and column_name='tenancy_assume';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill ADD COLUMN tenancy_assume numeric(19,6) DEFAULT 0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill' and column_name='third_assume';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill ADD COLUMN third_assume numeric(19,6) DEFAULT 0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill' and column_name='third_fee';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill ADD COLUMN third_fee numeric(19,6) DEFAULT 0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment' and column_name='coupon_buy_price';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_payment ADD COLUMN coupon_buy_price numeric(19,6) DEFAULT 0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment' and column_name='due';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_payment ADD COLUMN due numeric(19,6) DEFAULT 0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment' and column_name='tenancy_assume';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_payment ADD COLUMN tenancy_assume numeric(19,6) DEFAULT 0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment' and column_name='third_assume';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_payment ADD COLUMN third_assume numeric(19,6) DEFAULT 0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment' and column_name='third_fee';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_payment ADD COLUMN third_fee numeric(19,6) DEFAULT 0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment_coupons' and column_name='coupon_buy_price';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_payment_coupons ADD COLUMN coupon_buy_price numeric(19,6) DEFAULT 0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment_coupons' and column_name='due';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_payment_coupons ADD COLUMN due numeric(19,6) DEFAULT 0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment_coupons' and column_name='tenancy_assume';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_payment_coupons ADD COLUMN tenancy_assume numeric(19,6) DEFAULT 0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment_coupons' and column_name='third_assume';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_payment_coupons ADD COLUMN third_assume numeric(19,6) DEFAULT 0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment_coupons' and column_name='third_fee';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_payment_coupons ADD COLUMN third_fee numeric(19,6) DEFAULT 0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment_coupons' and column_name='request_state';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_payment_coupons ADD COLUMN request_state int DEFAULT 0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill2' and column_name='coupon_buy_price';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill2 ADD COLUMN coupon_buy_price numeric(19,6) DEFAULT 0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill2' and column_name='due';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill2 ADD COLUMN due numeric(19,6) DEFAULT 0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill2' and column_name='tenancy_assume';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill2 ADD COLUMN tenancy_assume numeric(19,6) DEFAULT 0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill2' and column_name='third_assume';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill2 ADD COLUMN third_assume numeric(19,6) DEFAULT 0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill2' and column_name='third_fee';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill2 ADD COLUMN third_fee numeric(19,6) DEFAULT 0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment2' and column_name='coupon_buy_price';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_payment2 ADD COLUMN coupon_buy_price numeric(19,6) DEFAULT 0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment2' and column_name='due';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_payment2 ADD COLUMN due numeric(19,6) DEFAULT 0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment2' and column_name='tenancy_assume';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_payment2 ADD COLUMN tenancy_assume numeric(19,6) DEFAULT 0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment2' and column_name='third_assume';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_payment2 ADD COLUMN third_assume numeric(19,6) DEFAULT 0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment2' and column_name='third_fee';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_payment2 ADD COLUMN third_fee numeric(19,6) DEFAULT 0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

/*COMMENT ON COLUMN public.pos_bill.coupon_buy_price IS '用户实付(券购买价)';
COMMENT ON COLUMN public.pos_bill.due IS '券账单净收(结算给商家的钱)';
COMMENT ON COLUMN public.pos_bill.tenancy_assume IS '商家优惠承担';
COMMENT ON COLUMN public.pos_bill.third_assume IS '第三方优惠承担';
COMMENT ON COLUMN public.pos_bill.third_fee IS '第三方票券服务费';

COMMENT ON COLUMN public.pos_bill2.coupon_buy_price IS '用户实付(券购买价)';
COMMENT ON COLUMN public.pos_bill2.due IS '券账单净收(结算给商家的钱)';
COMMENT ON COLUMN public.pos_bill2.tenancy_assume IS '商家优惠承担';
COMMENT ON COLUMN public.pos_bill2.third_assume IS '第三方优惠承担';
COMMENT ON COLUMN public.pos_bill2.third_fee IS '第三方票券服务费';

COMMENT ON COLUMN public.pos_bill_payment.coupon_buy_price IS '用户实付(券购买价)';
COMMENT ON COLUMN public.pos_bill_payment.due IS '券账单净收(结算给商家的钱)';
COMMENT ON COLUMN public.pos_bill_payment.tenancy_assume IS '商家优惠承担';
COMMENT ON COLUMN public.pos_bill_payment.third_assume IS '第三方优惠承担';
COMMENT ON COLUMN public.pos_bill_payment.third_fee IS '第三方票券服务费';

COMMENT ON COLUMN public.pos_bill_payment2.coupon_buy_price IS '用户实付(券购买价)';
COMMENT ON COLUMN public.pos_bill_payment2.due IS '券账单净收(结算给商家的钱)';
COMMENT ON COLUMN public.pos_bill_payment2.tenancy_assume IS '商家优惠承担';
COMMENT ON COLUMN public.pos_bill_payment2.third_assume IS '第三方优惠承担';
COMMENT ON COLUMN public.pos_bill_payment2.third_fee IS '第三方票券服务费';

COMMENT ON COLUMN public.pos_bill_payment_coupons.coupon_buy_price IS '用户实付(券购买价)';
COMMENT ON COLUMN public.pos_bill_payment_coupons.due IS '券账单净收(结算给商家的钱)';
COMMENT ON COLUMN public.pos_bill_payment_coupons.tenancy_assume IS '商家优惠承担';
COMMENT ON COLUMN public.pos_bill_payment_coupons.third_assume IS '第三方优惠承担';
COMMENT ON COLUMN public.pos_bill_payment_coupons.third_fee IS '第三方票券服务费';
COMMENT ON COLUMN public.pos_bill_payment_coupons.request_state IS '请求票券信息状态,0表示没有请求;1表示请求成功;2表示请求返回错误';*/

------- pos_bill_regain  pos_bill_payment_regain 新增 净收字段 -------
DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_regain' and column_name='coupon_buy_price';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_regain ADD COLUMN coupon_buy_price numeric(19,6) DEFAULT 0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_regain' and column_name='due';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_regain ADD COLUMN due numeric(19,6) DEFAULT 0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_regain' and column_name='tenancy_assume';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_regain ADD COLUMN tenancy_assume numeric(19,6) DEFAULT 0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_regain' and column_name='third_assume';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_regain ADD COLUMN third_assume numeric(19,6) DEFAULT 0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_regain' and column_name='third_fee';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_regain ADD COLUMN third_fee numeric(19,6) DEFAULT 0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment_regain' and column_name='coupon_buy_price';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_payment_regain ADD COLUMN coupon_buy_price numeric(19,6) DEFAULT 0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment_regain' and column_name='due';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_payment_regain ADD COLUMN due numeric(19,6) DEFAULT 0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment_regain' and column_name='tenancy_assume';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_payment_regain ADD COLUMN tenancy_assume numeric(19,6) DEFAULT 0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment_regain' and column_name='third_assume';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_payment_regain ADD COLUMN third_assume numeric(19,6) DEFAULT 0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment_regain' and column_name='third_fee';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_payment_regain ADD COLUMN third_fee numeric(19,6) DEFAULT 0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;
-------------- pos_bill pos_bill2 pos_bill_payment  pos_bill_payment2  pos_bill_payment_coupons 新增第三方券对账字段end------------


----------------------pos_bill_sub、pos_bill_sub2、pos_bill_sub_regain增加门店付款金额、账单数据调用接口标识start--------
DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_sub' and column_name='shop_payment_amount';
IF num = 0 THEN
		EXECUTE ' ALTER TABLE pos_bill_sub ADD COLUMN shop_payment_amount numeric(19,4);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;
--COMMENT ON COLUMN pos_bill_sub.shop_payment_amount IS '门店付款金额';


DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_sub2' and column_name='shop_payment_amount';
IF num = 0 THEN
		EXECUTE ' ALTER TABLE pos_bill_sub2 ADD COLUMN shop_payment_amount numeric(19,4);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;
--COMMENT ON COLUMN pos_bill_sub2.shop_payment_amount IS '门店付款金额';

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_sub_regain' and column_name='shop_payment_amount';
IF num = 0 THEN
		EXECUTE ' ALTER TABLE pos_bill_sub_regain ADD COLUMN shop_payment_amount numeric(19,4);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;
--COMMENT ON COLUMN pos_bill_sub_regain.shop_payment_amount IS '门店付款金额';


DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_sub' and column_name='cloud_interface';
IF num = 0 THEN
		EXECUTE ' ALTER TABLE pos_bill_sub ADD COLUMN cloud_interface int4;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;
--COMMENT ON COLUMN pos_bill_sub.cloud_interface IS '账单数据调用接口标识，0表示门店，1表示CRM，2表示微生活';



DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_sub2' and column_name='cloud_interface';
IF num = 0 THEN
		EXECUTE ' ALTER TABLE pos_bill_sub2 ADD COLUMN cloud_interface int4;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;
--COMMENT ON COLUMN pos_bill_sub2.cloud_interface IS '账单数据调用接口标识，0表示门店，1表示CRM，2表示微生活';



DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_sub_regain' and column_name='cloud_interface';
IF num = 0 THEN
		EXECUTE ' ALTER TABLE pos_bill_sub_regain ADD COLUMN cloud_interface int4;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;
--COMMENT ON COLUMN pos_bill_sub_regain.cloud_interface IS '账单数据调用接口标识，0表示门店，1表示CRM，2表示微生活';

--------------------------pos_bill_sub、pos_bill_sub2、pos_bill_sub_regain增加门店付款金额、账单数据调用接口标识 end-------------

------- 更新视图v_pos_bill,v_pos_bill_item,v_pos_bill_payment start-------
------- 肖恒
------- 2018-02-02
DROP VIEW IF EXISTS "public"."v_pos_bill";
CREATE OR REPLACE VIEW "public"."v_pos_bill" AS
SELECT pos_bill.tenancy_id, pos_bill.store_id, pos_bill.id, pos_bill.bill_num, pos_bill.batch_num, pos_bill.serial_num, pos_bill.report_date, pos_bill.table_code, pos_bill.guest, pos_bill.opentable_time, pos_bill.payment_time, pos_bill.payment_num, pos_bill.open_pos_num, pos_bill.pos_num, pos_bill.waiter_num, pos_bill.open_opt, pos_bill.cashier_num, pos_bill.shift_id, pos_bill.item_menu_id, pos_bill.service_id, pos_bill.service_amount, pos_bill.service_discount, pos_bill.order_num, pos_bill.print_time, pos_bill.print_count, pos_bill.subtotal, pos_bill.bill_amount, pos_bill.payment_amount, pos_bill.difference, pos_bill.discountk_amount, pos_bill.discountr_amount, pos_bill.maling_amount, pos_bill.single_discount_amount, pos_bill.discount_amount, pos_bill.free_amount, pos_bill.givi_amount, pos_bill.more_coupon, pos_bill.average_amount, pos_bill.discount_num, pos_bill.discount_case_id, pos_bill.discount_rate, pos_bill.billfree_reason_id, pos_bill.discount_mode_id, pos_bill.transfer_remark, pos_bill.sale_mode, pos_bill.bill_state, pos_bill.bill_property, pos_bill.upload_tag, pos_bill.deposit_count, pos_bill.copy_bill_num, pos_bill.source, pos_bill.opt_login_number, pos_bill.guest_msg, pos_bill.integraloffset, pos_bill.remark, pos_bill.return_amount, pos_bill.advance_payment_amt, pos_bill.advance_refund_amt, pos_bill.is_refund, pos_bill.payment_id, pos_bill.pay_no, pos_bill.third_bill_code, pos_bill.payment_state, pos_bill.payment_manager_num, pos_bill.shop_real_amount, pos_bill.total_fees, pos_bill.platform_charge_amount, pos_bill.settlement_type, pos_bill.bill_taste, pos_bill.fictitious_table, pos_bill.recover_count, pos_bill.tax_rate, pos_bill.tax_money, pos_bill.service_tax_rate, pos_bill.service_tax_money, pos_bill.tax_amount, pos_bill.no_tax_amount, pos_bill.payment_tax_money, pos_bill.payment_notax, pos_bill.bill_tax_money, pos_bill.bill_notax, pos_bill.service_notax, pos_bill.settlement_price, pos_bill.discount_reason_id, pos_bill.dinner_type, pos_bill.coupon_buy_price, pos_bill.due, pos_bill.tenancy_assume, pos_bill.third_assume, pos_bill.third_fee, 1 AS ms,pos_bill.sbill_num FROM pos_bill UNION ALL SELECT pos_bill2.tenancy_id, pos_bill2.store_id, pos_bill2.id, pos_bill2.bill_num, pos_bill2.batch_num, pos_bill2.serial_num, pos_bill2.report_date, pos_bill2.table_code, pos_bill2.guest, pos_bill2.opentable_time, pos_bill2.payment_time, pos_bill2.payment_num, pos_bill2.open_pos_num, pos_bill2.pos_num, pos_bill2.waiter_num, pos_bill2.open_opt, pos_bill2.cashier_num, pos_bill2.shift_id, pos_bill2.item_menu_id, pos_bill2.service_id, pos_bill2.service_amount, pos_bill2.service_discount, pos_bill2.order_num, pos_bill2.print_time, pos_bill2.print_count, pos_bill2.subtotal, pos_bill2.bill_amount, pos_bill2.payment_amount, pos_bill2.difference, pos_bill2.discountk_amount, pos_bill2.discountr_amount, pos_bill2.maling_amount, pos_bill2.single_discount_amount, pos_bill2.discount_amount, pos_bill2.free_amount, pos_bill2.givi_amount, pos_bill2.more_coupon, pos_bill2.average_amount, pos_bill2.discount_num, pos_bill2.discount_case_id, pos_bill2.discount_rate, pos_bill2.billfree_reason_id, pos_bill2.discount_mode_id, pos_bill2.transfer_remark, pos_bill2.sale_mode, pos_bill2.bill_state, pos_bill2.bill_property, pos_bill2.upload_tag, pos_bill2.deposit_count, pos_bill2.copy_bill_num, pos_bill2.source, pos_bill2.opt_login_number, pos_bill2.guest_msg, pos_bill2.integraloffset, pos_bill2.remark, pos_bill2.return_amount, pos_bill2.advance_payment_amt, pos_bill2.advance_refund_amt, pos_bill2.is_refund, pos_bill2.payment_id, pos_bill2.pay_no, pos_bill2.third_bill_code, pos_bill2.payment_state, pos_bill2.payment_manager_num, pos_bill2.shop_real_amount, pos_bill2.total_fees, pos_bill2.platform_charge_amount, pos_bill2.settlement_type, pos_bill2.bill_taste, pos_bill2.fictitious_table, pos_bill2.recover_count, pos_bill2.tax_rate, pos_bill2.tax_money, pos_bill2.service_tax_rate, pos_bill2.service_tax_money, pos_bill2.tax_amount, pos_bill2.no_tax_amount, pos_bill2.payment_tax_money, pos_bill2.payment_notax, pos_bill2.bill_tax_money, pos_bill2.bill_notax, pos_bill2.service_notax, pos_bill2.settlement_price, pos_bill2.discount_reason_id, pos_bill2.dinner_type, pos_bill2.coupon_buy_price, pos_bill2.due, pos_bill2.tenancy_assume, pos_bill2.third_assume, pos_bill2.third_fee, 1 AS ms,pos_bill2.sbill_num FROM pos_bill2 WHERE (NOT ((pos_bill2.bill_num)::text IN (SELECT pos_bill.bill_num FROM pos_bill)));

DROP VIEW IF EXISTS "public"."v_pos_bill_item";
CREATE OR REPLACE VIEW "public"."v_pos_bill_item" AS
SELECT pos_bill_item.tenancy_id, pos_bill_item.store_id, pos_bill_item.id, pos_bill_item.rwid, pos_bill_item.yrwid, pos_bill_item.bill_num, pos_bill_item.details_id, pos_bill_item.item_id, pos_bill_item.item_num, pos_bill_item.item_name, pos_bill_item.item_english, pos_bill_item.item_unit_id, pos_bill_item.item_unit_name, pos_bill_item.stable_code, pos_bill_item.table_code, pos_bill_item.pushmoney_way, pos_bill_item.proportion, pos_bill_item.assist_num, pos_bill_item.assist_money, pos_bill_item.waiter_num, pos_bill_item.item_price, pos_bill_item.item_count, pos_bill_item.item_amount, pos_bill_item.real_amount, pos_bill_item.discount_amount, pos_bill_item.single_discount_amount, pos_bill_item.discountr_amount, pos_bill_item.discount_state, pos_bill_item.discount_rate, pos_bill_item.discount_mode_id, pos_bill_item.item_class_id, pos_bill_item.item_property, pos_bill_item.item_remark, pos_bill_item.print_tag, pos_bill_item.waitcall_tag, pos_bill_item.setmeal_id, pos_bill_item.setmeal_rwid, pos_bill_item.is_setmeal_changitem, pos_bill_item.item_time, pos_bill_item.item_serial, pos_bill_item.report_date, pos_bill_item.item_shift_id, pos_bill_item.item_mac_id, pos_bill_item.item_taste, pos_bill_item.order_remark, pos_bill_item.seat_num, pos_bill_item.ticket_num, pos_bill_item.sale_mode, pos_bill_item.gqcj_tag, pos_bill_item.kvscp_tag, pos_bill_item.discount_reason_id, pos_bill_item.is_showitem, pos_bill_item.upload_tag, pos_bill_item.assist_item_id, pos_bill_item.integraloffset, pos_bill_item.remark, pos_bill_item.setmeal_group_id, pos_bill_item.group_id, pos_bill_item.third_price, pos_bill_item.returngive_reason_id, pos_bill_item.manager_num, pos_bill_item.return_type, pos_bill_item.return_count, pos_bill_item.method_money, pos_bill_item.batch_num, pos_bill_item.order_number, pos_bill_item.item_remark_his, pos_bill_item.opt_num, pos_bill_item.tax_rate, pos_bill_item.tax_money, pos_bill_item.item_notax, pos_bill_item.payment_tax_money, pos_bill_item.payment_notax, pos_bill_item.main_item, pos_bill_item.mid, pos_bill_item.activity_id, pos_bill_item.activity_batch_num, pos_bill_item.activity_rule_id, pos_bill_item.activity_count, pos_bill_item.is_consignment, pos_bill_item.settlement_price, pos_bill_item.single_amount, pos_bill_item.print_count, pos_bill_item.default_state, pos_bill_item.combo_prop, pos_bill_item.served_state, pos_bill_item.discount_num, pos_bill_item.count_rate, pos_bill_item.single_discount_rate, pos_bill_item.origin_item_price FROM pos_bill_item UNION ALL SELECT pos_bill_item2.tenancy_id, pos_bill_item2.store_id, pos_bill_item2.id, pos_bill_item2.rwid, pos_bill_item2.yrwid, pos_bill_item2.bill_num, pos_bill_item2.details_id, pos_bill_item2.item_id, pos_bill_item2.item_num, pos_bill_item2.item_name, pos_bill_item2.item_english, pos_bill_item2.item_unit_id, pos_bill_item2.item_unit_name, pos_bill_item2.stable_code, pos_bill_item2.table_code, pos_bill_item2.pushmoney_way, pos_bill_item2.proportion, pos_bill_item2.assist_num, pos_bill_item2.assist_money, pos_bill_item2.waiter_num, pos_bill_item2.item_price, pos_bill_item2.item_count, pos_bill_item2.item_amount, pos_bill_item2.real_amount, pos_bill_item2.discount_amount, pos_bill_item2.single_discount_amount, pos_bill_item2.discountr_amount, pos_bill_item2.discount_state, pos_bill_item2.discount_rate, pos_bill_item2.discount_mode_id, pos_bill_item2.item_class_id, pos_bill_item2.item_property, pos_bill_item2.item_remark, pos_bill_item2.print_tag, pos_bill_item2.waitcall_tag, pos_bill_item2.setmeal_id, pos_bill_item2.setmeal_rwid, pos_bill_item2.is_setmeal_changitem, pos_bill_item2.item_time, pos_bill_item2.item_serial, pos_bill_item2.report_date, pos_bill_item2.item_shift_id, pos_bill_item2.item_mac_id, pos_bill_item2.item_taste, pos_bill_item2.order_remark, pos_bill_item2.seat_num, pos_bill_item2.ticket_num, pos_bill_item2.sale_mode, pos_bill_item2.gqcj_tag, pos_bill_item2.kvscp_tag, pos_bill_item2.discount_reason_id, pos_bill_item2.is_showitem, pos_bill_item2.upload_tag, pos_bill_item2.assist_item_id, pos_bill_item2.integraloffset, pos_bill_item2.remark, pos_bill_item2.setmeal_group_id, pos_bill_item2.group_id, pos_bill_item2.third_price, pos_bill_item2.returngive_reason_id, pos_bill_item2.manager_num, pos_bill_item2.return_type, pos_bill_item2.return_count, pos_bill_item2.method_money, pos_bill_item2.batch_num, pos_bill_item2.order_number, pos_bill_item2.item_remark_his, pos_bill_item2.opt_num, pos_bill_item2.tax_rate, pos_bill_item2.tax_money, pos_bill_item2.item_notax, pos_bill_item2.payment_tax_money, pos_bill_item2.payment_notax, pos_bill_item2.main_item, pos_bill_item2.mid, pos_bill_item2.activity_id, pos_bill_item2.activity_batch_num, pos_bill_item2.activity_rule_id, pos_bill_item2.activity_count, pos_bill_item2.is_consignment, pos_bill_item2.settlement_price, pos_bill_item2.single_amount, pos_bill_item2.print_count, pos_bill_item2.default_state, pos_bill_item2.combo_prop, pos_bill_item2.served_state, pos_bill_item2.discount_num, pos_bill_item2.count_rate, pos_bill_item2.single_discount_rate, pos_bill_item2.origin_item_price FROM pos_bill_item2 WHERE (NOT ((pos_bill_item2.bill_num)::text IN (SELECT pos_bill.bill_num FROM pos_bill)));

DROP VIEW IF EXISTS "public"."v_pos_bill_payment";
CREATE OR REPLACE VIEW "public"."v_pos_bill_payment" AS
SELECT pos_bill_payment.tenancy_id, pos_bill_payment.store_id, pos_bill_payment.id, pos_bill_payment.bill_num, pos_bill_payment.table_code, pos_bill_payment.type, pos_bill_payment.jzid, pos_bill_payment.name, pos_bill_payment.name_english, pos_bill_payment.amount, pos_bill_payment.count, pos_bill_payment.number, pos_bill_payment.phone, pos_bill_payment.report_date, pos_bill_payment.shift_id, pos_bill_payment.pos_num, pos_bill_payment.cashier_num, pos_bill_payment.last_updatetime, pos_bill_payment.is_ysk, pos_bill_payment.rate, pos_bill_payment.currency_amount, pos_bill_payment.upload_tag, pos_bill_payment.customer_id, pos_bill_payment.bill_code, pos_bill_payment.remark, pos_bill_payment.payment_state, pos_bill_payment.batch_num, pos_bill_payment.more_coupon, pos_bill_payment.fee, pos_bill_payment.fee_rate, pos_bill_payment.coupon_type, pos_bill_payment.yjzid, pos_bill_payment.coupon_buy_price, pos_bill_payment.due, pos_bill_payment.tenancy_assume, pos_bill_payment.third_assume, pos_bill_payment.third_fee FROM pos_bill_payment UNION ALL SELECT pos_bill_payment2.tenancy_id, pos_bill_payment2.store_id, pos_bill_payment2.id, pos_bill_payment2.bill_num, pos_bill_payment2.table_code, pos_bill_payment2.type, pos_bill_payment2.jzid, pos_bill_payment2.name, pos_bill_payment2.name_english, pos_bill_payment2.amount, pos_bill_payment2.count, pos_bill_payment2.number, pos_bill_payment2.phone, pos_bill_payment2.report_date, pos_bill_payment2.shift_id, pos_bill_payment2.pos_num, pos_bill_payment2.cashier_num, pos_bill_payment2.last_updatetime, pos_bill_payment2.is_ysk, pos_bill_payment2.rate, pos_bill_payment2.currency_amount, pos_bill_payment2.upload_tag, pos_bill_payment2.customer_id, pos_bill_payment2.bill_code, pos_bill_payment2.remark, pos_bill_payment2.payment_state, pos_bill_payment2.batch_num, pos_bill_payment2.more_coupon, pos_bill_payment2.fee, pos_bill_payment2.fee_rate, pos_bill_payment2.coupon_type, pos_bill_payment2.yjzid, pos_bill_payment2.coupon_buy_price, pos_bill_payment2.due, pos_bill_payment2.tenancy_assume, pos_bill_payment2.third_assume, pos_bill_payment2.third_fee FROM pos_bill_payment2 WHERE (NOT ((pos_bill_payment2.bill_num)::text IN (SELECT pos_bill.bill_num FROM pos_bill)));
------- 更新视图v_pos_bill,v_pos_bill_item,v_pos_bill_payment end -------

-------张勇 解决纳税人识别号字段长度过短 20180205 start---------------
DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'cc_order_list' and column_name='taxpayerid';
IF num > 0 THEN
		EXECUTE 'ALTER TABLE cc_order_list ALTER taxpayerid type varchar(150)';

ELSE RAISE NOTICE 'column  not exists in this table';
END IF;
END $DO$;
-------张勇 解决纳税人识别号字段长度过短 20180205 end---------------

-- hq_printer_new开钱箱参数

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'hq_printer_new' and column_name='is_open_cashbox';
IF num = 0 THEN
		EXECUTE ' ALTER TABLE hq_printer_new ADD COLUMN is_open_cashbox int4 DEFAULT 0;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;
--COMMENT ON COLUMN hq_printer_new.is_open_cashbox IS '是否开钱箱 0:不开钱箱、1:打开钱箱';

-- 添加订单号和订单属性唯一约束,防止外卖订单重单
DELETE from cc_order_list where id not in(select max from  (select count(1),order_code,max(id) from cc_order_list GROUP BY order_code) t where t."count">1) and (select max from  (select count(1),order_code,max(id) from cc_order_list GROUP BY order_code) t where t."count">1) IS NOT NULL;
ALTER TABLE "public"."cc_order_list" DROP CONSTRAINT if exists uk_cc_order_list_order_code;
ALTER TABLE "public"."cc_order_list" ADD CONSTRAINT "uk_cc_order_list_order_code" UNIQUE ("order_code");

ALTER TABLE "public"."pos_bill" DROP CONSTRAINT if exists uk_pos_bill_order_num_bill_state;
ALTER TABLE "public"."pos_bill" ADD CONSTRAINT "uk_pos_bill_order_num_bill_state" UNIQUE ("order_num", "bill_state");

-------更新cc_order_list字段长度 start---------------
DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'cc_order_list' and column_name='remark';
IF num > 0 THEN
		EXECUTE 'ALTER TABLE cc_order_list ALTER remark type varchar(1000)';

ELSE RAISE NOTICE 'column  not exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'cc_order_list' and column_name='address';
IF num > 0 THEN
		EXECUTE 'ALTER TABLE cc_order_list ALTER address type varchar(1000)';

ELSE RAISE NOTICE 'column  not exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'cc_order_list' and column_name='invoice_title';
IF num > 0 THEN
		EXECUTE 'ALTER TABLE cc_order_list ALTER invoice_title type varchar(1000)';

ELSE RAISE NOTICE 'column  not exists in this table';
END IF;
END $DO$;

-------更新cc_order_list字段长度 end---------------

--------------------update_sql_v20180321(build-20180321)--------------------

--------更新订单转账单相关表 start------------
--删除订单套餐明细表
DROP table IF EXISTS  cc_order_item_details;
--添加套餐明细表
CREATE TABLE
    cc_order_item_details
    (
        tenancy_id CHARACTER VARYING(64),
        id serial  NOT NULL,
        group_index INTEGER,
        order_code CHARACTER VARYING(50),
        item_id INTEGER,
        unit_id INTEGER,
        NUMBER NUMERIC(19,4),
        price NUMERIC(19,4),
        remark CHARACTER VARYING(100),
        real_amount NUMERIC(19,4),
        discount_amount NUMERIC(19,4),
        single_discount_amount NUMERIC(19,4),
        discountr_amount NUMERIC(19,4),
        discount_state NUMERIC(19,4),
        discount_rate NUMERIC(19,4),
        discount_mode_id NUMERIC(19,4),
        product_fee NUMERIC(19,4),
        method_money NUMERIC(19,4),
        is_add_dish CHARACTER VARYING(10),
        is_itemgroup CHARACTER VARYING(19),
        item_group_id INTEGER,
        store_id INTEGER,
        upload_tag INTEGER,
        report_date DATE,
        create_date DATE,
        unit_name CHARACTER VARYING(100),
        item_name CHARACTER VARYING(100),
        net_income_amount NUMERIC(9,2),
        PRIMARY KEY (id)
    );

--订单明细表：添加字段  （菜品实（净）收）
select p_addfield('cc_order_item','net_income_amount','NUMERIC(19,4)');
--订单套餐明细表：添加字段  （菜品实（净）收）
select p_addfield('cc_order_item_details','net_income_amount','NUMERIC(19,4)');
--账单明细表：添加字段  （菜品实（净）收）
select p_addfield('pos_bill_item','net_income_amount','NUMERIC(19,4)');
select p_addfield('pos_bill_item2','net_income_amount','NUMERIC(19,4)');
--订单收款表：添加字段  商家实（净）收
select p_addfield('cc_order_repayment','shop_real_amount','NUMERIC(19,6)');
--账单收款表：添加字段  商家实（净）收
select p_addfield('pos_bill_payment','shop_real_amount','NUMERIC(19,6)');
select p_addfield('pos_bill_payment2','shop_real_amount','NUMERIC(19,6)');

--------更新订单转账单相关表 end------------

------ 设备安装注册相关表 start--------------
--DROP TABLE IF EXISTS hq_local_devices;
CREATE TABLE IF NOT EXISTS hq_local_devices
    (
        tenancy_id CHARACTER VARYING(32),
        store_id INTEGER,
        id serial  NOT NULL,
        business_area INTEGER,
        devices_code CHARACTER VARYING(100),
        devices_name CHARACTER VARYING(100),
        devices_ip CHARACTER VARYING(100),
        show_type CHARACTER VARYING(100),
        show_port CHARACTER VARYING(100),
        remark CHARACTER VARYING(100),
        authorize_code CHARACTER VARYING(500),
        devices_properties CHARACTER VARYING(100),
        valid_state CHARACTER VARYING(10),
        last_operator CHARACTER VARYING(30),
        last_updatetime TIMESTAMP(6) WITHOUT TIME ZONE,
        printer INTEGER,
        audit_operator CHARACTER VARYING(30),
        audit_state CHARACTER VARYING(10),
        audit_updatetime TIMESTAMP(6) WITHOUT TIME ZONE,
        kvs_type CHARACTER VARYING(10),
        show_mode CHARACTER VARYING(10),
        mealpreparetime CHARACTER VARYING(20),
        is_site CHARACTER VARYING(10),
        device_type CHARACTER VARYING(80),
        device_unique_code CHARACTER VARYING(80),
        devices_brands CHARACTER VARYING(160),
        devices_model CHARACTER VARYING(160),
        devices_operation CHARACTER VARYING(160),
        devices_cpu CHARACTER VARYING(160),
        devices_memory CHARACTER VARYING(160),
        devices_disk CHARACTER VARYING(160),
        devices_key CHARACTER VARYING(160),
        status INTEGER,
        CONSTRAINT pk_hq_local_devices PRIMARY KEY (id)
    );

------ 设备安装注册相关表 end--------------

---菜品区域范围表
--DROP TABLE IF EXISTS "public"."hq_item_tables";
CREATE TABLE IF NOT EXISTS "public"."hq_item_tables" (
"tenancy_id" varchar(32) COLLATE "default" NOT NULL,
"id" serial NOT NULL,
"store_id" int4,
"table_id" int4,
"last_updatetime" timestamp(6),
"last_operator" varchar(30),
"valid_state" varchar(10),
constraint pk_hq_item_tables primary key (id)
)
WITH (OIDS=FALSE);

------ 服务费折让 start--------------
--2018-04-24
--肖恒   
select p_addfield('pos_bill','item_discountr_amount','numeric(19,4)');
select p_addfield('pos_bill','service_discountr_amount','numeric(19,4)');
select p_addfield('pos_bill2','item_discountr_amount','numeric(19,4)');
select p_addfield('pos_bill2','service_discountr_amount','numeric(19,4)');
select p_addfield('pos_bill_regain','item_discountr_amount','numeric(19,4)');
select p_addfield('pos_bill_regain','service_discountr_amount','numeric(19,4)');
------ 服务费折让  end--------------

-------外卖同步saas总部字段start-------------
--2018-05-07 沈占玉
select p_addfield('cc_order_item','saas_item_price','numeric(19,4)');
select p_addfield('cc_order_item','saas_item_name','varchar(50)');
select p_addfield('cc_order_item','saas_item_num','varchar(50)');
select p_addfield('cc_order_item','saas_unit_name','varchar(50)');

select p_addfield('pos_bill_item','saas_item_price','numeric(19,4)');
select p_addfield('pos_bill_item','saas_item_name','varchar(50)');
select p_addfield('pos_bill_item','saas_item_num','varchar(50)');
select p_addfield('pos_bill_item','saas_unit_name','varchar(50)');
-------外卖同步saas总部字段end-------------


--折让奉送按单限制--
--ALTER TABLE user_discount_authority ADD single_reward_money smallint;
select p_addfield('user_discount_authority','single_reward_money','smallint');
--ALTER TABLE user_discount_authority ADD single_discount_money smallint;
select p_addfield('user_discount_authority','single_discount_money','smallint');

-------增加优惠券明细记不记收入与显示优惠券详情功能start-------------
--2018-05-31 肖恒
select p_addfield('pos_bill_payment','payment_uid','CHARACTER VARYING(64)');
select p_addfield('pos_bill_payment2','payment_uid','CHARACTER VARYING(64)');
select p_addfield('pos_bill_payment_regain','payment_uid','CHARACTER VARYING(64)');
select p_addfield('pos_bill_payment_coupons','rwid','int4');
select p_addfield('pos_bill_payment_coupons','item_unit_id','int4');
ALTER TABLE pos_bill_payment_coupons ALTER payment_id type CHARACTER VARYING(64);

select p_addfield('pos_opter_changshift','sort_num','CHARACTER VARYING(8)');
select p_addfield('pos_opter_changshift','parent_sort_num','CHARACTER VARYING(8)');
-------增加优惠券明细记不记收入与显示优惠券详情功能end-------------

----------目前由打印服务创建的2张表 start----------
-----2108-06-25 王国照 以后打印服务需要创建表时由boh来维护
-----pos_print_task_bak 打印备打表
-----pos_print_params 打印参数表

----------目前由打印服务创建的2张表 end----------
----- pos_bill_*2 表更新id为主键
ALTER TABLE "public"."pos_bill2" DROP CONSTRAINT IF EXISTS "pos_bill2_pkey";
ALTER TABLE "public"."pos_bill_item2" DROP CONSTRAINT IF EXISTS "pos_bill_item2_pkey";
ALTER TABLE "public"."pos_bill_payment2" DROP CONSTRAINT IF EXISTS "pos_bill_payment2_pkey";

ALTER TABLE pos_bill2 DROP CONSTRAINT if exists pk_pos_bill2;
ALTER TABLE pos_bill_item2 DROP CONSTRAINT if exists pk_pos_bill_item2;
ALTER TABLE pos_bill_payment2 DROP CONSTRAINT if exists pk_pos_bill_payment2;

SELECT setval('"pos_bill2_id_seq"', (select max(id)+1 from pos_bill2)); 
SELECT setval('"pos_bill_item2_id_seq"', (select max(id)+1 from pos_bill_item2)); 
SELECT setval('"pos_bill_payment2_id_seq"', (select max(id)+1 from pos_bill_payment2)); 

----- pos_bill_*2 表更新id为主键   end

------- 更新视图v_pos_bill,v_pos_bill_item,v_pos_bill_payment start-------
------- 肖恒
------- 2018-07-12
select p_addfield('pos_bill_payment2','shop_real_amount','NUMERIC(19,6)');
select p_addfield('pos_bill_payment_regain','shop_real_amount','NUMERIC(19,6)');

DROP VIEW IF EXISTS "public"."v_pos_bill";
CREATE OR REPLACE VIEW "public"."v_pos_bill" AS
   SELECT pos_bill.tenancy_id, pos_bill.store_id, pos_bill.id, pos_bill.bill_num, pos_bill.batch_num, pos_bill.serial_num, pos_bill.report_date, pos_bill.table_code, pos_bill.guest, pos_bill.opentable_time, pos_bill.payment_time, pos_bill.payment_num, pos_bill.open_pos_num, pos_bill.pos_num, pos_bill.waiter_num, pos_bill.open_opt, pos_bill.cashier_num, pos_bill.shift_id, pos_bill.item_menu_id, pos_bill.service_id, pos_bill.service_amount, pos_bill.service_discount, pos_bill.order_num, pos_bill.print_time, pos_bill.print_count, pos_bill.subtotal, pos_bill.bill_amount, pos_bill.payment_amount, pos_bill.difference, pos_bill.discountk_amount, pos_bill.discountr_amount, pos_bill.maling_amount, pos_bill.single_discount_amount, pos_bill.discount_amount, pos_bill.free_amount, pos_bill.givi_amount, pos_bill.more_coupon, pos_bill.average_amount, pos_bill.discount_num, pos_bill.discount_case_id, pos_bill.discount_rate, pos_bill.billfree_reason_id, pos_bill.discount_mode_id, pos_bill.transfer_remark, pos_bill.sale_mode, pos_bill.bill_state, pos_bill.bill_property, pos_bill.upload_tag, pos_bill.deposit_count, pos_bill.copy_bill_num, pos_bill.source, pos_bill.opt_login_number, pos_bill.guest_msg, pos_bill.integraloffset, pos_bill.remark, pos_bill.return_amount, pos_bill.advance_payment_amt, pos_bill.advance_refund_amt, pos_bill.is_refund, pos_bill.payment_id, pos_bill.pay_no, pos_bill.third_bill_code, pos_bill.payment_state, pos_bill.payment_manager_num, pos_bill.shop_real_amount, pos_bill.total_fees, pos_bill.platform_charge_amount, pos_bill.settlement_type, pos_bill.bill_taste, pos_bill.fictitious_table, pos_bill.recover_count, pos_bill.tax_rate, pos_bill.tax_money, pos_bill.service_tax_rate, pos_bill.service_tax_money, pos_bill.tax_amount, pos_bill.no_tax_amount, pos_bill.payment_tax_money, pos_bill.payment_notax, pos_bill.bill_tax_money, pos_bill.bill_notax, pos_bill.service_notax, pos_bill.settlement_price, pos_bill.discount_reason_id, pos_bill.dinner_type, pos_bill.coupon_buy_price, pos_bill.due, pos_bill.tenancy_assume, pos_bill.third_assume, pos_bill.third_fee, pos_bill.item_discountr_amount, pos_bill.service_discountr_amount, 1 AS ms ,pos_bill.sbill_num
   FROM pos_bill
   UNION ALL
   SELECT pos_bill2.tenancy_id, pos_bill2.store_id, pos_bill2.id, pos_bill2.bill_num, pos_bill2.batch_num, pos_bill2.serial_num, pos_bill2.report_date, pos_bill2.table_code, pos_bill2.guest, pos_bill2.opentable_time, pos_bill2.payment_time, pos_bill2.payment_num, pos_bill2.open_pos_num, pos_bill2.pos_num, pos_bill2.waiter_num, pos_bill2.open_opt, pos_bill2.cashier_num, pos_bill2.shift_id, pos_bill2.item_menu_id, pos_bill2.service_id, pos_bill2.service_amount, pos_bill2.service_discount, pos_bill2.order_num, pos_bill2.print_time, pos_bill2.print_count, pos_bill2.subtotal, pos_bill2.bill_amount, pos_bill2.payment_amount, pos_bill2.difference, pos_bill2.discountk_amount, pos_bill2.discountr_amount, pos_bill2.maling_amount, pos_bill2.single_discount_amount, pos_bill2.discount_amount, pos_bill2.free_amount, pos_bill2.givi_amount, pos_bill2.more_coupon, pos_bill2.average_amount, pos_bill2.discount_num, pos_bill2.discount_case_id, pos_bill2.discount_rate, pos_bill2.billfree_reason_id, pos_bill2.discount_mode_id, pos_bill2.transfer_remark, pos_bill2.sale_mode, pos_bill2.bill_state, pos_bill2.bill_property, pos_bill2.upload_tag, pos_bill2.deposit_count, pos_bill2.copy_bill_num, pos_bill2.source, pos_bill2.opt_login_number, pos_bill2.guest_msg, pos_bill2.integraloffset, pos_bill2.remark, pos_bill2.return_amount, pos_bill2.advance_payment_amt, pos_bill2.advance_refund_amt, pos_bill2.is_refund, pos_bill2.payment_id, pos_bill2.pay_no, pos_bill2.third_bill_code, pos_bill2.payment_state, pos_bill2.payment_manager_num, pos_bill2.shop_real_amount, pos_bill2.total_fees, pos_bill2.platform_charge_amount, pos_bill2.settlement_type, pos_bill2.bill_taste, pos_bill2.fictitious_table, pos_bill2.recover_count, pos_bill2.tax_rate, pos_bill2.tax_money, pos_bill2.service_tax_rate, pos_bill2.service_tax_money, pos_bill2.tax_amount, pos_bill2.no_tax_amount, pos_bill2.payment_tax_money, pos_bill2.payment_notax, pos_bill2.bill_tax_money, pos_bill2.bill_notax, pos_bill2.service_notax, pos_bill2.settlement_price, pos_bill2.discount_reason_id, pos_bill2.dinner_type, pos_bill2.coupon_buy_price, pos_bill2.due, pos_bill2.tenancy_assume, pos_bill2.third_assume, pos_bill2.third_fee, pos_bill2.item_discountr_amount, pos_bill2.service_discountr_amount, 1 AS ms  ,pos_bill2.sbill_num
   FROM pos_bill2
   WHERE (NOT ((pos_bill2.bill_num)::text IN (SELECT pos_bill.bill_num FROM pos_bill)));

DROP VIEW IF EXISTS "public"."v_pos_bill_item";
CREATE OR REPLACE VIEW "public"."v_pos_bill_item" AS
SELECT pos_bill_item.tenancy_id, pos_bill_item.store_id, pos_bill_item.id, pos_bill_item.rwid, pos_bill_item.yrwid, pos_bill_item.bill_num, pos_bill_item.details_id, pos_bill_item.item_id, pos_bill_item.item_num, pos_bill_item.item_name, pos_bill_item.item_english, pos_bill_item.item_unit_id, pos_bill_item.item_unit_name, pos_bill_item.stable_code, pos_bill_item.table_code, pos_bill_item.pushmoney_way, pos_bill_item.proportion, pos_bill_item.assist_num, pos_bill_item.assist_money, pos_bill_item.waiter_num, pos_bill_item.item_price, pos_bill_item.item_count, pos_bill_item.item_amount, pos_bill_item.real_amount, pos_bill_item.discount_amount, pos_bill_item.single_discount_amount, pos_bill_item.discountr_amount, pos_bill_item.discount_state, pos_bill_item.discount_rate, pos_bill_item.discount_mode_id, pos_bill_item.item_class_id, pos_bill_item.item_property, pos_bill_item.item_remark, pos_bill_item.print_tag, pos_bill_item.waitcall_tag, pos_bill_item.setmeal_id, pos_bill_item.setmeal_rwid, pos_bill_item.is_setmeal_changitem, pos_bill_item.item_time, pos_bill_item.item_serial, pos_bill_item.report_date, pos_bill_item.item_shift_id, pos_bill_item.item_mac_id, pos_bill_item.item_taste, pos_bill_item.order_remark, pos_bill_item.seat_num, pos_bill_item.ticket_num, pos_bill_item.sale_mode, pos_bill_item.gqcj_tag, pos_bill_item.kvscp_tag, pos_bill_item.discount_reason_id, pos_bill_item.is_showitem, pos_bill_item.upload_tag, pos_bill_item.assist_item_id, pos_bill_item.integraloffset, pos_bill_item.remark, pos_bill_item.setmeal_group_id, pos_bill_item.group_id, pos_bill_item.third_price, pos_bill_item.returngive_reason_id, pos_bill_item.manager_num, pos_bill_item.return_type, pos_bill_item.return_count, pos_bill_item.method_money, pos_bill_item.batch_num, pos_bill_item.order_number, pos_bill_item.item_remark_his, pos_bill_item.opt_num, pos_bill_item.tax_rate, pos_bill_item.tax_money, pos_bill_item.item_notax, pos_bill_item.payment_tax_money, pos_bill_item.payment_notax, pos_bill_item.main_item, pos_bill_item.mid, pos_bill_item.activity_id, pos_bill_item.activity_batch_num, pos_bill_item.activity_rule_id, pos_bill_item.activity_count, pos_bill_item.is_consignment, pos_bill_item.settlement_price, pos_bill_item.single_amount, pos_bill_item.print_count, pos_bill_item.default_state, pos_bill_item.combo_prop, pos_bill_item.served_state, pos_bill_item.discount_num, pos_bill_item.count_rate, pos_bill_item.single_discount_rate, pos_bill_item.origin_item_price 
FROM pos_bill_item 
UNION ALL 
SELECT pos_bill_item2.tenancy_id, pos_bill_item2.store_id, pos_bill_item2.id, pos_bill_item2.rwid, pos_bill_item2.yrwid, pos_bill_item2.bill_num, pos_bill_item2.details_id, pos_bill_item2.item_id, pos_bill_item2.item_num, pos_bill_item2.item_name, pos_bill_item2.item_english, pos_bill_item2.item_unit_id, pos_bill_item2.item_unit_name, pos_bill_item2.stable_code, pos_bill_item2.table_code, pos_bill_item2.pushmoney_way, pos_bill_item2.proportion, pos_bill_item2.assist_num, pos_bill_item2.assist_money, pos_bill_item2.waiter_num, pos_bill_item2.item_price, pos_bill_item2.item_count, pos_bill_item2.item_amount, pos_bill_item2.real_amount, pos_bill_item2.discount_amount, pos_bill_item2.single_discount_amount, pos_bill_item2.discountr_amount, pos_bill_item2.discount_state, pos_bill_item2.discount_rate, pos_bill_item2.discount_mode_id, pos_bill_item2.item_class_id, pos_bill_item2.item_property, pos_bill_item2.item_remark, pos_bill_item2.print_tag, pos_bill_item2.waitcall_tag, pos_bill_item2.setmeal_id, pos_bill_item2.setmeal_rwid, pos_bill_item2.is_setmeal_changitem, pos_bill_item2.item_time, pos_bill_item2.item_serial, pos_bill_item2.report_date, pos_bill_item2.item_shift_id, pos_bill_item2.item_mac_id, pos_bill_item2.item_taste, pos_bill_item2.order_remark, pos_bill_item2.seat_num, pos_bill_item2.ticket_num, pos_bill_item2.sale_mode, pos_bill_item2.gqcj_tag, pos_bill_item2.kvscp_tag, pos_bill_item2.discount_reason_id, pos_bill_item2.is_showitem, pos_bill_item2.upload_tag, pos_bill_item2.assist_item_id, pos_bill_item2.integraloffset, pos_bill_item2.remark, pos_bill_item2.setmeal_group_id, pos_bill_item2.group_id, pos_bill_item2.third_price, pos_bill_item2.returngive_reason_id, pos_bill_item2.manager_num, pos_bill_item2.return_type, pos_bill_item2.return_count, pos_bill_item2.method_money, pos_bill_item2.batch_num, pos_bill_item2.order_number, pos_bill_item2.item_remark_his, pos_bill_item2.opt_num, pos_bill_item2.tax_rate, pos_bill_item2.tax_money, pos_bill_item2.item_notax, pos_bill_item2.payment_tax_money, pos_bill_item2.payment_notax, pos_bill_item2.main_item, pos_bill_item2.mid, pos_bill_item2.activity_id, pos_bill_item2.activity_batch_num, pos_bill_item2.activity_rule_id, pos_bill_item2.activity_count, pos_bill_item2.is_consignment, pos_bill_item2.settlement_price, pos_bill_item2.single_amount, pos_bill_item2.print_count, pos_bill_item2.default_state, pos_bill_item2.combo_prop, pos_bill_item2.served_state, pos_bill_item2.discount_num, pos_bill_item2.count_rate, pos_bill_item2.single_discount_rate, pos_bill_item2.origin_item_price 
FROM pos_bill_item2 
WHERE (NOT ((pos_bill_item2.bill_num)::text IN (SELECT pos_bill.bill_num FROM pos_bill)));

DROP VIEW IF EXISTS "public"."v_pos_bill_payment";
CREATE OR REPLACE VIEW "public"."v_pos_bill_payment" AS
SELECT pos_bill_payment.tenancy_id, pos_bill_payment.store_id, pos_bill_payment.id, pos_bill_payment.bill_num, pos_bill_payment.table_code, pos_bill_payment.type, pos_bill_payment.jzid, pos_bill_payment.name, pos_bill_payment.name_english, pos_bill_payment.amount, pos_bill_payment.count, pos_bill_payment.number, pos_bill_payment.phone, pos_bill_payment.report_date, pos_bill_payment.shift_id, pos_bill_payment.pos_num, pos_bill_payment.cashier_num, pos_bill_payment.last_updatetime, pos_bill_payment.is_ysk, pos_bill_payment.rate, pos_bill_payment.currency_amount, pos_bill_payment.upload_tag, pos_bill_payment.customer_id, pos_bill_payment.bill_code, pos_bill_payment.remark, pos_bill_payment.payment_state, pos_bill_payment.batch_num, pos_bill_payment.more_coupon, pos_bill_payment.fee, pos_bill_payment.fee_rate, pos_bill_payment.coupon_type, pos_bill_payment.yjzid, pos_bill_payment.coupon_buy_price, pos_bill_payment.due, pos_bill_payment.tenancy_assume, pos_bill_payment.third_assume, pos_bill_payment.third_fee, pos_bill_payment.payment_uid, pos_bill_payment.shop_real_amount
FROM pos_bill_payment 
UNION ALL  
SELECT pos_bill_payment2.tenancy_id, pos_bill_payment2.store_id, pos_bill_payment2.id, pos_bill_payment2.bill_num, pos_bill_payment2.table_code, pos_bill_payment2.type, pos_bill_payment2.jzid, pos_bill_payment2.name, pos_bill_payment2.name_english, pos_bill_payment2.amount, pos_bill_payment2.count, pos_bill_payment2.number, pos_bill_payment2.phone, pos_bill_payment2.report_date, pos_bill_payment2.shift_id, pos_bill_payment2.pos_num, pos_bill_payment2.cashier_num, pos_bill_payment2.last_updatetime, pos_bill_payment2.is_ysk, pos_bill_payment2.rate, pos_bill_payment2.currency_amount, pos_bill_payment2.upload_tag, pos_bill_payment2.customer_id, pos_bill_payment2.bill_code, pos_bill_payment2.remark, pos_bill_payment2.payment_state, pos_bill_payment2.batch_num, pos_bill_payment2.more_coupon, pos_bill_payment2.fee, pos_bill_payment2.fee_rate, pos_bill_payment2.coupon_type, pos_bill_payment2.yjzid, pos_bill_payment2.coupon_buy_price, pos_bill_payment2.due, pos_bill_payment2.tenancy_assume, pos_bill_payment2.third_assume, pos_bill_payment2.third_fee, pos_bill_payment2.payment_uid, pos_bill_payment2.shop_real_amount
FROM pos_bill_payment2 
WHERE (NOT ((pos_bill_payment2.bill_num)::text IN (SELECT pos_bill.bill_num FROM pos_bill)));
------- 更新视图v_pos_bill,v_pos_bill_item,v_pos_bill_payment end-------

--------------------update_sql_v20180604(build-20180604)--------------------

-------增加订单来源字段,记录点餐订单来源 start-------------
--2018-07-26 肖恒
select p_addfield('pos_bill','order_source','character varying(32)');
select p_addfield('pos_bill2','order_source','character varying(32)');
select p_addfield('pos_bill_regain','order_source','character varying(32)');
-------增加订单来源字段,记录点餐订单来源 end-------------

-------增加支付渠道字段,记录支付来源 start-------------
--2018-08-02 肖恒
select p_addfield('pos_bill','payment_source','character varying(32)');
select p_addfield('pos_bill2','payment_source','character varying(32)');
select p_addfield('pos_bill_regain','payment_source','character varying(32)');
-------增加支付渠道字段,记录支付来源 end-------------

--------------------hq_upgrade_v1.18.1_0605(build-20180604)--------------------

-------增加会员账单通用字段 start-------------
select p_addfield('pos_bill_member','generic_field','character varying(200)');
select p_addfield('pos_bill_member_regain','generic_field','character varying(200)');
-------增加会员账单通用字段 end-------------

/*-------开充值发票 start------
--修改会员表，添加可开票金额字段
DO $DO$
DECLARE num INT;
BEGIN
  select count(*) into num from information_schema.columns
  where table_name = 'crm_customer_info' and column_name='invoice_balance';
	IF num = 0 THEN
		ALTER TABLE crm_customer_info ADD COLUMN invoice_balance numeric(19,4);
	  	COMMENT ON COLUMN "public"."crm_customer_info"."invoice_balance" IS '可开票金额';
	ELSE
		RAISE NOTICE ' The column crm_customer_info already exists in this table';
	END IF;

END $DO$;


--创建开票流水记录表
CREATE TABLE IF NOT EXISTS "public"."crm_invoice_trading_list" (
"tenancy_id" varchar(32) COLLATE "default",
"id" serial,
"card_id" int4,
"card_code" varchar(50) COLLATE "default",
"bill_code" varchar(50) COLLATE "default",
"chanel" varchar(50) COLLATE "default",
"store_id" int4,
"business_date" date,
"main_trading" numeric(19,4),
"reward_trading" numeric(19,4),
"operat_type" varchar(10) COLLATE "default",
"main_original" numeric(19,4),
"reward_original" numeric(19,4),
"deposit" numeric(19,4),
"operator" varchar(30) COLLATE "default",
"operate_time" timestamp(6),
"bill_money" numeric(19,4),
"third_bill_code" varchar(50) COLLATE "default",
"bill_code_original" varchar(50) COLLATE "default",
"activity_id" int4,
"customer_id" int4,
"revoked_trading" numeric(19,4),
"batch_num" varchar(32) COLLATE "default",
"last_updatetime" timestamp(6),
"store_updatetime" timestamp(6),
"card_class_id" int4,
"name" varchar(32) COLLATE "default",
"mobil" varchar(16) COLLATE "default",
"main_balance" numeric(19,4),
"reward_balance" numeric(19,4),
"total_balance" numeric(19,4),
"operator_id" int4,
"shift_id" int4,
"pay_type" varchar(30) COLLATE "default",
"salesman" int4,
"commission_saler_money" numeric(19,4),
"commission_store_money" numeric(19,4),
"invoice_balance" numeric(19,4),
"invoice_money" numeric(19,4),
"invoice_num" int4,
"remark" varchar(32),
CONSTRAINT "pk_crm_invoice_trading_list" PRIMARY KEY ("id")
);

ALTER TABLE "public"."crm_invoice_trading_list" OWNER TO "tzxdb";

COMMENT ON COLUMN "public"."crm_invoice_trading_list"."revoked_trading" IS '已撤销金额';

COMMENT ON COLUMN "public"."crm_invoice_trading_list"."salesman" IS '销售人员id';

COMMENT ON COLUMN "public"."crm_invoice_trading_list"."commission_saler_money" IS '人员提成金额';

COMMENT ON COLUMN "public"."crm_invoice_trading_list"."commission_store_money" IS '餐厅提成金额';

COMMENT ON COLUMN "public"."crm_invoice_trading_list"."invoice_balance" IS '可开票金额';

COMMENT ON COLUMN "public"."crm_invoice_trading_list"."invoice_money" IS '本次开票金额';

COMMENT ON COLUMN "public"."crm_invoice_trading_list"."invoice_num" IS '开票张数';



CREATE INDEX IF NOT EXISTS "inx_citl_ci" ON "public"."crm_invoice_trading_list" USING btree (customer_id);*/

-------开充值发票 end------

--修改日志表,记录内容字段长度
--2018-08-23 肖恒
ALTER TABLE "pos_log" ALTER COLUMN "oldstate" TYPE CHARACTER VARYING(5000);
ALTER TABLE "pos_log" ALTER COLUMN "newstate" TYPE CHARACTER VARYING(5000);

------------小程序先付-------------------------
select p_addfield('pos_bill_booked','bill_num','character varying(32)');
select p_addfield('pos_bill_booked','table_code','character varying(32)');
select p_addfield('pos_bill_booked','discount_reason_id','int4');
select p_addfield('pos_bill_booked','item_discountr_amount','numeric(19,4)');
select p_addfield('pos_bill_booked','service_discountr_amount','numeric(19,4)');


select p_addfield('pos_bill_item_booked','bill_num','character varying(32)');
select p_addfield('pos_bill_item_booked','combo_prop','int4');
select p_addfield('pos_bill_item_booked','activity_id','int4');
select p_addfield('pos_bill_item_booked','activity_batch_num','int4');
select p_addfield('pos_bill_item_booked','activity_rule_id','int4');
select p_addfield('pos_bill_item_booked','activity_count','int4');
select p_addfield('pos_bill_item_booked','table_code','character varying(32)');
select p_addfield('pos_bill_item_booked','discount_num','character varying(32)');
select p_addfield('pos_bill_item_booked','default_state','character varying(20)');
select p_addfield('pos_bill_item_booked','print_count','numeric(19,4)');
select p_addfield('pos_bill_item_booked','single_amount','numeric(19,4)');
select p_addfield('pos_bill_item_booked','single_discount_rate','numeric(19,4)');
select p_addfield('pos_bill_item_booked','count_rate','numeric(19,4)');
select p_addfield('pos_bill_item_booked','origin_item_price','numeric(19,4)');
--做法--
CREATE TABLE IF NOT EXISTS pos_zfkw_item_booked (
  "tenancy_id" varchar(32) COLLATE "default",
  "store_id" int4,
  "id" serial NOT NULL,
  "bill_num" varchar(20) COLLATE "default",
  "report_date" date,
  "rwid" int4,
  "item_id" int4,
  "pos_num" varchar(10) COLLATE "default",
  "type" varchar(100) COLLATE "default",
  "last_updatetime" timestamp(6),
  "zfkw_id" int4,
  "zfkw_name" varchar(100) COLLATE "default",
  "amount" numeric(19,4),
  "print_id" int4,
  "upload_tag" int4 DEFAULT 0,
  "remark" varchar(500) COLLATE "default",
  "item_num" int4,
  CONSTRAINT "pos_zfkw_item_copy_pkey" PRIMARY KEY ("id")
);
------------小程序先付end-------------------------

-------配送订单信息表------
CREATE TABLE IF NOT EXISTS "cc_deliver_list" (
"id" serial NOT NULL,
"order_code" varchar(50),
"actual_price" numeric(19,2),
"type" varchar(10),
"shop_id" varchar(20),
"remark" varchar(1000),
"channel" varchar(10),
"receivername" varchar(500),
"receiverlat" varchar(100),
"receiverlng" varchar(100),
"receiveraddress" varchar(1000),
"receiverphone" varchar(1000),
"tips" numeric(19,2),
"deliver_name" varchar(100),
"deliver_phone" varchar(30),
"deliver_distance" varchar(100),
"deliver_fee" numeric(19,2),
"deduct_fee" numeric(19,2),
"deliver_third_id" varchar(50),
"state" varchar(6),
"create_time" timestamp(6),
"accept_time" timestamp(6),
"reach_time" timestamp(6),
"fetch_time" timestamp(6),
"finish_time" timestamp(6),
"cancel_time" timestamp(6),
"exception_time" timestamp(6),
"orderfinishcode" varchar(20),
"cancel_reasonid" varchar(20),
"cancel_reason_des" varchar(300),
CONSTRAINT "pk_cc_deliver_list" PRIMARY KEY ("id")
);

select p_addfield('cc_deliver_list','order_state','character varying(20)');
select p_addfield('cc_deliver_list','order_state_reason','character varying(100)');

--删除订单号和订单属性唯一约束
--2018-09-10 肖恒
ALTER TABLE "public"."pos_bill" DROP CONSTRAINT if exists uk_pos_bill_order_num_bill_state;

---新下发 下发版本记录表start -----
CREATE TABLE IF NOT EXISTS "public"."down_table_version" (
"id" serial not null,
"tenancy_id" varchar(32) COLLATE "default",
"table_id" int4,
"type_name" varchar(32) COLLATE "default",
"table_name" varchar(50) COLLATE "default",
"table_version" varchar(50) COLLATE "default",
"createtime" varchar(50) COLLATE "default",
"updatetime" varchar(50) COLLATE "default",
"state" int4,
"store_id" int4,
CONSTRAINT "down_table_version_pkey" PRIMARY KEY ("id")
)
WITH (OIDS=FALSE)
;

---新下发 下发版本记录表end -----

-------配送订单信息表------
CREATE TABLE IF NOT EXISTS "cc_deliver_list" (
"id" serial NOT NULL,
"order_code" varchar(50),
"actual_price" numeric(19,2),
"type" varchar(10),
"shop_id" varchar(20),
"remark" varchar(1000),
"channel" varchar(10),
"receivername" varchar(500),
"receiverlat" varchar(100),
"receiverlng" varchar(100),
"receiveraddress" varchar(1000),
"receiverphone" varchar(1000),
"tips" numeric(19,2),
"deliver_name" varchar(100),
"deliver_phone" varchar(30),
"deliver_distance" varchar(100),
"deliver_fee" numeric(19,2),
"deduct_fee" numeric(19,2),
"deliver_third_id" varchar(50),
"state" varchar(6),
"create_time" timestamp(6),
"accept_time" timestamp(6),
"reach_time" timestamp(6),
"fetch_time" timestamp(6),
"finish_time" timestamp(6),
"cancel_time" timestamp(6),
"exception_time" timestamp(6),
"orderfinishcode" varchar(20),
"cancel_reasonid" varchar(20),
"cancel_reason_des" varchar(300),
CONSTRAINT "pk_cc_deliver_list" PRIMARY KEY ("id")
);

select p_addfield('cc_deliver_list','order_state','character varying(20)');
select p_addfield('cc_deliver_list','order_state_reason','character varying(100)');
select p_addfield('cc_order_list','find_num','int4');
select p_addfield('cc_order_list','find_state','int4');
select p_addfield('cc_order_list','delivery_state','character varying(20)');
select p_addfield('cc_order_list','find_updatetime','timestamp(6)');


------------------------华莱士迁移-------------------

-----------------外卖部分退款 start add by shenzhanyu--------------------------
-- 退款类型字段 1 全单退 2部分退
select p_addfield('cc_order_list','refund_type','varchar(2)');
-- pos接收时间
select p_addfield('cc_order_list','refund_recive_time','timestamp(6)');
-- 退款同意/不现意操作人
select p_addfield('cc_order_list','refund_operator','varchar(50)');
-- 退款同意/不同意操作时间
select p_addfield('cc_order_list','refund_oper_time','timestamp(6)');
-- 申请退款原因
select p_addfield('cc_order_list','refund_reason','varchar(200)');
-- 退款第三方订单号
select p_addfield('cc_order_list','refund_third_order_id','varchar(50)');
-------外卖部分退款 end-------


----------------外卖同步saas总部字段start add by shenzhanyu------------------
select p_addfield('cc_order_item','saas_item_price','numeric(19,4)');
select p_addfield('cc_order_item','saas_item_name','varchar(50)');
select p_addfield('cc_order_item','saas_item_num','varchar(50)');
select p_addfield('cc_order_item','saas_unit_name','varchar(50)');
select p_addfield('cc_order_item_details','real_amount','numeric(19,6)');
select p_addfield('cc_order_item_details','discount_amount','numeric(19,6)');

select p_addfield('pos_bill_item','saas_item_price','numeric(19,4)');
select p_addfield('pos_bill_item','saas_item_name','varchar(50)');
select p_addfield('pos_bill_item','saas_item_num','varchar(50)');
select p_addfield('pos_bill_item','saas_unit_name','varchar(50)');
select p_addfield('pos_bill_item','origin_item_price','numeric(19,6)');

select p_addfield('pos_bill_item2','saas_item_price','numeric(19,4)');
select p_addfield('pos_bill_item2','saas_item_name','varchar(50)');
select p_addfield('pos_bill_item2','saas_item_num','varchar(50)');
select p_addfield('pos_bill_item2','saas_unit_name','varchar(50)');
select p_addfield('pos_bill_item2','origin_item_price','numeric(19,6)');
----------------外卖同步saas总部字段end------------------


--订单明细表：添加字段  （菜品实（净）收）
select p_addfield('cc_order_item','net_income_amount','NUMERIC(19,4)');
--订单套餐明细表：添加字段  （菜品实（净）收）
select p_addfield('cc_order_item_details','net_income_amount','NUMERIC(19,4)');
--账单明细表：添加字段  （菜品实（净）收）
select p_addfield('pos_bill_item','net_income_amount','NUMERIC(19,4)');
select p_addfield('pos_bill_item2','net_income_amount','NUMERIC(19,4)');
--订单收款表：添加字段  商家实（净）收
select p_addfield('cc_order_repayment','shop_real_amount','NUMERIC(19,6)');
--账单收款表：添加字段  商家实（净）收
select p_addfield('pos_bill_payment','shop_real_amount','NUMERIC(19,6)');
--订单明细表：添加字段  （餐盒菜品表示-总部同步）
select p_addfield('cc_order_item','item_sign','varchar(50)');


---- cc_order_payment添加payment_name字段
select p_addfield('cc_order_repayment','payment_name','varchar(50)');

------------异常订单菜品与本地菜品绑定表 add by shenzhanyu----------
create table if not exists binding_item_info (
"id"  serial not null,
"tenancy_id" varchar(32) collate "default" not null,
"store_id" int4 not null,
"order_code" varchar(30) collate "default",
"cc_order_item_id" int4,
"cc_order_item_name" varchar(100) collate "default",
"hq_item_info_id" int4,
"hq_item_info_item_name" varchar(100) collate "default",
"hq_item_info_item_code" varchar(100) collate "default",
"unit_id" int4,
"unit_name" varchar(100) collate "default",
"standard_price" DECIMAL(19,4) null,
CONSTRAINT binding_item_info_pkey PRIMARY KEY (id)
);
------------异常订单菜品与本地菜品绑定表 add by shenzhanyu-------------------

------------账单表增加餐盒费字段 add by shenzhanyu------------------------
select p_addfield('pos_bill','package_box_fee','numeric(19,4)');
select p_addfield('pos_bill2','package_box_fee','numeric(19,4)');
------------账单表增加餐盒费字段end--------------------
select p_addfield('pos_bill_payment','param_cach','character varying(2000)');
select p_addfield('pos_bill_payment2','param_cach','character varying(2000)');

------ 调整cc_order_list 字段order_name,consigner长度  shenzhanyu  start-----
DO $DO$
DECLARE num INT; 
BEGIN  
  select count(*) into num from information_schema.columns 
  where table_name = 'cc_order_list' and column_name='order_name';
	IF num = 1 THEN  
			ALTER TABLE cc_order_list ALTER COLUMN order_name TYPE  varchar(100);
	ELSE 
		RAISE NOTICE ' The column cc_order_list already not exists in this table';
	END IF;

END $DO$;


DO $DO$
DECLARE num INT; 
BEGIN  
  select count(*) into num from information_schema.columns 
  where table_name = 'cc_order_list' and column_name='consigner';
	IF num = 1 THEN  
			ALTER TABLE cc_order_list ALTER COLUMN consigner TYPE  varchar(300);
	ELSE 
		RAISE NOTICE ' The column cc_order_list already not exists in this table';
	END IF;

END $DO$;
------ 调整cc_order_list 字段长度  shenzhanyu  end-----

---菜品辅助数量start---
select p_addfield('pos_bill_item','item_assist_num','numeric(19,4)');
select p_addfield('pos_bill_item2','item_assist_num','numeric(19,4)');
select p_addfield('pos_bill_item_regain','item_assist_num','numeric(19,4)');
---菜品辅助数量end---

---单品转台记录原桌位start---
select p_addfield('pos_bill_item','original_table','varchar(50)');
select p_addfield('pos_bill_item2','original_table','varchar(50)');
select p_addfield('pos_bill_item_regain','original_table','varchar(50)');
---单品转台记录原桌位end---

----------------[1.22.X] - 2018-11-27------------------

---支付退款优化 start---
---2018-11-27 肖恒
create table if not exists pos_third_payment_refund (
	tenancy_id          character varying(32),
	store_id            int                  ,
	id					SERIAL 			not null,
	report_date			date,
	shift_id			int4,
	pos_num				character varying(16),
	opt_num				character varying(16),
	channel				character varying(16),
	service_type			character varying(16),
	order_num			character varying(32),
	out_request_no		character varying(64),
	form_trade_no		character varying(32),
	trade_no			character varying(64),
	payment_class		character varying(32),
	payment_id			int4,
	total_amount		numeric(19,4),
	settle_amount		numeric(19,4),
	create_time			timestamp,
	extra				text,
	last_updatetime		timestamp,
	last_operator		character varying(16),
	failure_code		character varying(64),
	failure_msg			character varying(2000),
	retry_count			int4,					
	oper_type			character varying(64), 
	status				character varying(16), 
	finish_status		character varying(16), 
   constraint PK_POS_THIRD_PAYMENT_REFUND_ID primary key (id)
);
---支付退款优化 end---

------------账单中增加是否跨天（冲减单是否为隔日退款）字段-----------------------
select p_addfield('pos_bill','is_cross_day','varchar(50)');
select p_addfield('pos_bill2','is_cross_day','varchar(50)');
select p_addfield('pos_bill_item','is_cross_day','varchar(50)');
select p_addfield('pos_bill_item2','is_cross_day','varchar(50)');
------------账单中增加是否跨天（冲减单是否为隔日退款）end--------------------

----正餐kvs划菜 start ---
CREATE TABLE IF NOT EXISTS "public"."pos_bill_item_stamp" (
  "id" serial  NOT NULL,
  "item_rwid" int4,
  "stamp_time" timestamp(6),
  "stamp_source" varchar(255) COLLATE "default",
  "stamp_opt" int4,
  "stamp_type" int4,
  "upload_tag" int4 DEFAULT 0,
  "stamp_kvs" varchar(32) COLLATE "default",
  "bill_num" varchar(32) COLLATE "default",
  "table_code" varchar(255) COLLATE "default",
  "table_name" varchar(255) COLLATE "default",
  "item_id" int4,
  "item_name" varchar(255) COLLATE "default",
  "item_unit_name" varchar(255) COLLATE "default",
  "item_count" int4,
  "opt_name" varchar(255) COLLATE "default",
  "item_time" timestamp(6),
  "report_date" date,
  "tenancy_id" varchar(255) COLLATE "default",
  "store_id" int4,
  CONSTRAINT "pos_bill_item_stamp_pkey" PRIMARY KEY ("id")
);
----正餐kvs划菜 end ---


---E待客充值交款 start---
---2018-12-11 肖恒
--交班会员统计表新增付款方式字段
select p_addfield('pos_opter_changshift_customer','payment_id','int4');

--交款添加服务类型
select p_addfield('pos_cashier_receive_log','service_type','varchar(16)');

select p_addfield('pos_cashier_receive_log_details','payment_id','int4');
select p_addfield('pos_cashier_receive_log_details','service_type','varchar(16)');
select p_addfield('pos_cashier_receive_log_details','shift_id','int4');
select p_addfield('pos_cashier_receive_log_details','pay_shift_id','int4');
select p_addfield('pos_cashier_receive_log_details','report_date','date');

ALTER TABLE pos_cashier_receive_log_details ALTER COLUMN receive_id TYPE CHARACTER VARYING(32);
---E待客充值交款 end---



----增加外卖单中配送名称
DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'cc_order_list' and column_name='logistics_name';
IF num = 0 THEN
		EXECUTE ' ALTER TABLE cc_order_list ADD COLUMN logistics_name varchar(50)';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;


----增加外卖预订单中是否已经提醒过打印
DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'cc_order_list' and column_name='print_remind';
IF num = 0 THEN
		EXECUTE ' ALTER TABLE cc_order_list ADD COLUMN print_remind varchar(50)';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;

---增加外卖订单套餐明细saas_item_num 字段，存储到pos_bill_item 表item_num中
DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'cc_order_item_details' and column_name='saas_item_num';
IF num = 0 THEN
		EXECUTE ' ALTER TABLE cc_order_item_details ADD COLUMN saas_item_num varchar(20)';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;

----- hq_item_info 表新增字段:是否可用券，默认值:Y
DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'hq_item_info' and column_name='is_ticket';
IF num = 0 THEN
		ALTER TABLE hq_item_info ADD COLUMN is_ticket varchar(10) DEFAULT 'Y';
ELSE
		RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;


--- 修改 pos_item_discount_list 表batch_num字段长度 shenzhanyu--
DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_item_discount_list' and column_name='batch_num';
IF num > 0 THEN
		EXECUTE 'ALTER TABLE pos_item_discount_list ALTER batch_num type varchar(32)';
ELSE RAISE NOTICE 'column  not exists in this table';
END IF;
END $DO$;
--- 修改 pos_item_discount_list 表batch_num字段长度 shenzhanyu--

-----  pos_bill_item 添加时段特价标记-------
DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_item' and column_name='price_type';
IF num = 0 THEN
		EXECUTE ' ALTER TABLE pos_bill_item ADD COLUMN price_type varchar(50) default ''BJ01''';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_item' and column_name='special_price_id';
IF num = 0 THEN
		EXECUTE ' ALTER TABLE pos_bill_item ADD COLUMN special_price_id varchar(50)';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;

--- 商家替用户承担的配送费 start--
DO $DO$
DECLARE num INT;
BEGIN
   select count(*) into num from information_schema.columns where table_name = 'cc_order_list' and column_name='merchant_assume_deliveryfee';
   IF num = 0 THEN
      EXECUTE ' ALTER TABLE cc_order_list ADD COLUMN merchant_assume_deliveryfee numeric(9,3)';
   ELSE RAISE NOTICE 'column  already exists in this table';
   END IF;
END$DO$;
--商家替用户承担的配送费 end --


--- 菜品所属口袋 --
DO $DO$
DECLARE num INT;
BEGIN
   select count(*) into num from information_schema.columns where table_name = 'cc_order_item' and column_name='cart_name';
   IF num = 0 THEN
      EXECUTE ' ALTER TABLE cc_order_item ADD COLUMN cart_name varchar(100)';
   ELSE RAISE NOTICE 'column  already exists in this table';
   END IF;
END$DO$;
--- 菜品所属口袋 --

---火锅业态0315 start----------------------
--- 冰柜设置相关表start---

CREATE TABLE IF NOT EXISTS "public"."pos_print_bgclass" (
"id" varchar(32) COLLATE "default" NOT NULL,
"bgcode" varchar(255) COLLATE "default",
"bgname" varchar(255) COLLATE "default",
"bgorder" int4,
"tenancy_id" varchar COLLATE "default",
"store_id" int4,
"printer_id" int4,
"printer_address" varchar(255) COLLATE "default",
"printer_name" varchar(255) COLLATE "default",
CONSTRAINT "pos_print_bgclass_pkey" PRIMARY KEY ("id")
)
WITH (OIDS=FALSE) ;

CREATE TABLE IF NOT EXISTS "public"."pos_print_bgclass_item" (
"id" varchar(32) COLLATE "default" NOT NULL,
"bgclassid" varchar(32) COLLATE "default",
"item_id" int4,
"tenancy_id" varchar COLLATE "default",
"store_id" int4,
"item_order" int4,
"item_name" varchar(255) COLLATE "default",
"printer_id" int4,
"printer_name" varchar(255) COLLATE "default",
CONSTRAINT "pos_print_bgclass_item_pkey" PRIMARY KEY ("id")
)
WITH (OIDS=FALSE) ;

--- 冰柜设置相关表end---

--- 菜品类别添加火锅属性和是否必点字段 start ---
select p_addfield('hq_item_class','hotpot_property','varchar(255)');
select p_addfield('hq_item_class','is_must_dish','varchar(10)');
--COMMENT ON COLUMN "public"."hq_item_class"."hotpot_property" IS '火锅属性（bigpot:大锅,smallpot:小锅,condiment:底料）';
--COMMENT ON COLUMN "public"."hq_item_class"."is_must_dish" IS '是否必点(Y:是,N:否)';
--- 菜品类别添加火锅属性和是否必点字段 end ---

--- 火锅桌数/人数start--
select p_addfield('pos_bill','hotpot_table_num','int4');
select p_addfield('pos_bill','hotpot_guest_num','int4');
select p_addfield('pos_bill2','hotpot_table_num','int4');
select p_addfield('pos_bill2','hotpot_guest_num','int4');

--- 火锅桌数/人数end--
---火锅业态0315 start----------------------



----增加外卖订单是否正在自动接单 is_taking_order
DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'cc_order_list' and column_name='is_taking_order';
IF num = 0 THEN
		EXECUTE ' ALTER TABLE cc_order_list ADD COLUMN is_taking_order varchar(50)';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;
----增加外卖订单是否正在自动接单

---------豪享来业务start-------------------
--- 设置套餐默认项表 start--
CREATE TABLE IF NOT EXISTS "public"."pos_item_combo_details_default" (
   "id" serial not null,
   "tenancy_id" varchar(32) COLLATE "default",
   "store_id" int4,
   "setmeal_id" int4,
   "group_id" int4,
   "group_item_id" int4,
   "unit_id" int4,
   "pos_num" varchar(50) COLLATE "default",
   "opt_num" varchar(50) COLLATE "default",
   "updatetime" varchar(50) COLLATE "default",
   CONSTRAINT "pos_item_combo_details_default_pkey" PRIMARY KEY ("id")
)
WITH (OIDS=FALSE)
;
--- 设置套餐默认项表 end--



--- POS禁用菜品 start------
CREATE TABLE IF NOT EXISTS "public"."pos_forbidden_item" (
   "id" serial not null,
   "tenancy_id" varchar(32) COLLATE "default",
   "store_id" int4,
   "item_id" int4,
   -- "item_num" varchar(30) COLLATE "default",
   -- "item_name" varchar(100) COLLATE "default",
   "unit_id" int4,
   -- "unit_name" varchar(100) COLLATE "default",
   "opt_num" varchar(50) COLLATE "default",
   "updatetime" varchar(50) COLLATE "default",
   CONSTRAINT "pos_forbidden_item_pkey" PRIMARY KEY ("id")
)
WITH (OIDS=FALSE)
;
--- POS禁用菜品 end------


---设置班次限制次数 start------
CREATE TABLE IF NOT EXISTS "public"."pos_setting_shifts_times" (
   "id" serial not null,
   "tenancy_id" varchar(32) COLLATE "default",
   "store_id" int4,
   "limit_count" int4,
   "pos_num" varchar(50) COLLATE "default",
   "opt_num" varchar(50) COLLATE "default",
   "updatetime" varchar(50) COLLATE "default",
   CONSTRAINT "pos_setting_shifts_times_pkey" PRIMARY KEY ("id")
)
WITH (OIDS=FALSE)
;

---设置班次限制次数 end------
---并台记录原账单号start--------
select p_addfield('pos_bill','sbill_num','varchar(200)');
select p_addfield('pos_bill2','sbill_num','varchar(200)');
--alter table "pos_bill" alter  COLUMN  sbill_num  type varchar(200) ;
--alter table "pos_bill2" alter  COLUMN  sbill_num  type varchar(200) ;
---并台记录原账单号end--------


---并台记录是否为并台之后操作菜品 加菜、赠送、取消赠送、单品转入、转出、转台 等操作start--------
select p_addfield('pos_bill','is_combined_after','varchar(20)');
select p_addfield('pos_bill2','is_combined_after','varchar(20)');
---并台记录是否为并台之后操作菜品 加菜、赠送、取消赠送、单品转入、转出、转台 等操作end--------

--------修改字段大小start-----------
alter table "pos_table_log" alter  COLUMN  from_bill_num  type varchar(200) ;
alter table "pos_table_log" alter  COLUMN  to_bill_num  type varchar(200) ;
alter table "pos_table_log" alter  COLUMN  from_table_code  type varchar(200) ;
alter table "pos_table_log" alter  COLUMN  to_table_code  type varchar(200) ;
--------修改字段大小end-----------

--------插入设置班次限制次数初始化语句start-----------
INSERT INTO "public"."pos_setting_shifts_times" ( "tenancy_id", "store_id", "limit_count", "pos_num", "opt_num", "updatetime")
SELECT  o.tenancy_id, 0, '2', null, null, now() from organ o  where  NOT EXISTS (SELECT id FROM pos_setting_shifts_times WHERE tenancy_id=o.tenancy_id);
--------插入设置班次限制次数初始化语句end-----------

---- crm_card_trading_list 表增加pos_num 字段start ------
select p_addfield('crm_card_trading_list','pos_num','character varying(32)');
---- crm_card_trading_list 表增加pos_num 字段end ------

--- user_authority表 增加  pos_user_name  字段 start-------------
DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'user_authority' and column_name='pos_user_name';
IF num = 0 THEN
		ALTER TABLE user_authority ADD COLUMN pos_user_name varchar(30) ;
		COMMENT ON COLUMN user_authority.pos_user_name IS 'POS登录账号';
ELSE
		RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

--- user_authority表 增加  pos_user_name  字段 end-------------

---------豪享来业务end-------------------


--- 同步菜品数据缓存 start---
CREATE TABLE IF NOT EXISTS "public"."pos_dish_cache" (
   "id" serial NOT NULL,
   "item_menu_id" varchar(255) COLLATE "default",
   "valid_state" varchar(255) COLLATE "default",
   "item_class_id" varchar(255) COLLATE "default",
   "starttime" varchar(255) COLLATE "default",
   "is_assemble_combo" varchar(255) COLLATE "default",
   "price" varchar(255) COLLATE "default",
   "print_id" varchar(255) COLLATE "default",
   "print_name" varchar(255) COLLATE "default",
   "item_barcode" varchar(255) COLLATE "default",
   "is_runningprice" varchar(255) COLLATE "default",
   "is_recommendation" varchar(255) COLLATE "default",
   "is_seafood" varchar(255) COLLATE "default",
   "is_characteristic" varchar(255) COLLATE "default",
   "print_ip_com" varchar(255) COLLATE "default",
   "spicy" varchar(255) COLLATE "default",
   "unit_name" varchar(255) COLLATE "default",
   "is_combo" varchar(255) COLLATE "default",
   "nutrition" varchar(255) COLLATE "default",
   "photo1" varchar(255) COLLATE "default",
   "is_assist_num" varchar(255) COLLATE "default",
   "combo_type" varchar(255) COLLATE "default",
   "vip_price" varchar(255) COLLATE "default",
   "suitable_crowds" varchar(255) COLLATE "default",
   "third_code" varchar(255) COLLATE "default",
   "proportion" varchar(255) COLLATE "default",
   "photo6" varchar(255) COLLATE "default",
   "unsuitable_crowds" varchar(255) COLLATE "default",
   "photo2" varchar(255) COLLATE "default",
   "photo3" varchar(255) COLLATE "default",
   "is_discount" varchar(255) COLLATE "default",
   "photo4" varchar(255) COLLATE "default",
   "item_no" varchar(255) COLLATE "default",
   "photo5" varchar(255) COLLATE "default",
   "itemclass_code" varchar(255) COLLATE "default",
   "is_modifyname" varchar(255) COLLATE "default",
   "item_english" varchar(255) COLLATE "default",
   "five_code" varchar(255) COLLATE "default",
   "is_staplefood" varchar(255) COLLATE "default",
   "to_offer" varchar(255) COLLATE "default",
   "is_pushmoney" varchar(255) COLLATE "default",
   "unit_id" varchar(255) COLLATE "default",
   "summary" varchar(255) COLLATE "default",
   "menu_item_rank" varchar(255) COLLATE "default",
   "item_id" varchar(255) COLLATE "default",
   "endtime" varchar(255) COLLATE "default",
   "is_modifyquantity" varchar(255) COLLATE "default",
   "phonetic_code" varchar(255) COLLATE "default",
   "item_name" varchar(255) COLLATE "default",
   "is_show" varchar(255) COLLATE "default",
   "pushmoney_way" varchar(255) COLLATE "default",
   "details_id" varchar(255) COLLATE "default",
   "is_staffmeal" varchar(255) COLLATE "default",
   "processing_technic" varchar(255) COLLATE "default",
   "item_description" varchar(255) COLLATE "default",
   "is_throwaway" varchar(255) COLLATE "default",
   "chanel" varchar(255) COLLATE "default",
   CONSTRAINT "pos_dish_cache_pkey" PRIMARY KEY ("id")
)
WITH (OIDS=FALSE)
;

ALTER TABLE "public"."pos_dish_cache" OWNER TO "tzxdbuser";

--- 同步菜品数据缓存 end---

--- 同步规格数据缓存 start---
CREATE TABLE IF NOT EXISTS "public"."pos_unit_cache" (
   "id" serial NOT NULL,
   "chanel" varchar(255) COLLATE "default",
   "unit_id" varchar(255) COLLATE "default",
   "unit_name" varchar(255) COLLATE "default",
   "is_default" varchar(255) COLLATE "default",
   "standard_price" varchar(255) COLLATE "default",
   "combo_display_state" varchar(255) COLLATE "default",
   "count_rate" varchar(255) COLLATE "default",
   "vip_price" varchar(255) COLLATE "default",
   "item_id" varchar(255) COLLATE "default",
   CONSTRAINT "pos_unit_cache_pkey" PRIMARY KEY ("id")
)
WITH (OIDS=FALSE)
;

ALTER TABLE "public"."pos_unit_cache" OWNER TO "tzxdbuser";
--- 同步规格数据缓存 end---

--- 会员操作记录会员操作日志 start---
-- 2019-09-27
-- 张来刚
CREATE TABLE IF NOT EXISTS "public"."customer_operation_logs" (
"tenancy_id" varchar(32) COLLATE "default",
"id" serial,
"store_id" int4,
"customer_id" int4,
"customer_name" varchar(20) COLLATE "default",
"mobile" varchar(20) COLLATE "default",
"pos_num" varchar(10) COLLATE "default",
"operation_id" varchar(20) COLLATE "default",
"operation_name" varchar(20) COLLATE "default",
"title" varchar(100) COLLATE "default",
"content" varchar(500) COLLATE "default",
"report_date" date,
"operation_time" timestamp(6) DEFAULT now(),
"operation_type" varchar(20),
"operation_type_name" varchar(20) COLLATE "default",
"remark" varchar(500) COLLATE "default",
"card_code" varchar(50) COLLATE "default",
"shift_id" int4,
"upload_tag" int4 DEFAULT 0,
"old_content" varchar(500) COLLATE "default",
CONSTRAINT "pk_customer_operation_logs" PRIMARY KEY ("id")
)
WITH (OIDS=FALSE)
;

--- 会员操作记录会员操作日志 end---

--- 付款记录表添加字段 支付请求单号 start---
-- 2019-10-10
-- 肖恒
select p_addfield('pos_bill_payment','out_trade_no','varchar(64)');
select p_addfield('pos_bill_payment2','out_trade_no','varchar(64)');
select p_addfield('pos_bill_payment_regain','out_trade_no','varchar(64)');
--- 付款记录表添加字段 支付请求单号 end---


-- Eco 外卖对接脚本 -----
---pos_bill_extends 账单扩展表 start ---
create table if not exists  "public"."pos_bill_extends" (
id SERIAL NOT NULL,
tenancy_id CHARACTER VARYING(32),
store_id INTEGER,
bill_num CHARACTER VARYING(32),
eco_print_msg varchar(2000) COLLATE "default",
CONSTRAINT "pk_pos_bill_extends_id" PRIMARY KEY ("id")
)
WITH (OIDS=FALSE)
;

-------------eco异常打印,创建cc_order_exception   order_id 存在代表异常订单
CREATE TABLE if not exists "cc_order_exception" (
  "order_id" varchar(50) COLLATE "pg_catalog"."default" NOT NULL DEFAULT NULL::character varying,
  "last_time" timestamp(6) NOT NULL DEFAULT now(),
  CONSTRAINT "uniques_order_id" UNIQUE ("order_id")
);

-- cc_order_exception 表 order_id 字段添加唯一约束
DO $DO$
DECLARE num int;
DECLARE num1 int;
BEGIN
		select count(*) into num from cc_order_exception group by order_id having count(1)>1;
    select count(1) into num1 from pg_constraint  inner join pg_class on pg_constraint.conrelid = pg_class.oid inner join pg_attribute on pg_attribute.attrelid = pg_class.oid
    and  pg_attribute.attnum = pg_constraint.conkey[1] and pg_attribute.attname = 'order_id' inner join pg_type on pg_type.oid = pg_attribute.atttypid  where pg_class.relname = 'cc_order_exception'  and pg_constraint.contype='u';
IF num is null and num1<>1 THEN
		EXECUTE 'alter table cc_order_exception add constraint uniques_order_id unique(order_id)';
ELSE RAISE NOTICE 'constraint data repeat or constraint unique exists ';
END IF;
END $DO$;
-- Eco 外卖对接脚本 over -----

ALTER TABLE "crm_card_trading_list" ALTER COLUMN "salesman" TYPE CHARACTER VARYING(32);

--pos_bill_payment_coupous_douyin --
CREATE TABLE IF NOT EXISTS "public"."pos_bill_payment_coupous_douyin" (
    "id" serial NOT NULL,
    "original_amount" varchar(255) COLLATE "pg_catalog"."default",
    "pay_amount" varchar(255) COLLATE "pg_catalog"."default",
    "encrypted_code" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
    "expire_time" varchar(255) COLLATE "pg_catalog"."default",
    "groupon_type" varchar(255) COLLATE "pg_catalog"."default",
    "market_price" varchar(255) COLLATE "pg_catalog"."default",
    "sku_id" varchar(255) COLLATE "pg_catalog"."default",
    "sold_start_time" varchar(255) COLLATE "pg_catalog"."default",
    "third_sku_id" varchar(255) COLLATE "pg_catalog"."default",
    "title" varchar(255) COLLATE "pg_catalog"."default",
    "status" varchar(255) COLLATE "pg_catalog"."default",
    "can_cancel" varchar(255) COLLATE "pg_catalog"."default",
    "certificate_id" varchar(255) COLLATE "pg_catalog"."default",
    "verifier_unique_id" varchar(255) COLLATE "pg_catalog"."default",
    "verify_id" varchar(255) COLLATE "pg_catalog"."default",
    "verify_time" varchar(255) COLLATE "pg_catalog"."default",
    "verify_type" varchar(255) COLLATE "pg_catalog"."default",
    "origin_code" varchar(255) COLLATE "pg_catalog"."default",
    "account_id" varchar(255) COLLATE "pg_catalog"."default",

    CONSTRAINT "pos_bill_payment_coupous_douyin_pkey" PRIMARY KEY ("id")
);

ALTER TABLE pos_bill_payment_coupons ALTER COLUMN coupons_code TYPE varchar(255);
/*ALTER TABLE "public"."pos_bill_payment_coupous_douyin"
    ADD COLUMN "ticket_amount" varchar(255),
    ADD COLUMN "merchant_ticket_amount" varchar(255),
    ADD COLUMN "payment_discount_amount" varchar(255),
    ADD COLUMN "coupon_pay_amount" varchar(255);*/
select p_addfield('pos_bill_payment_coupous_douyin','ticket_amount','varchar(255)');
select p_addfield('pos_bill_payment_coupous_douyin','merchant_ticket_amount','varchar(255)');
select p_addfield('pos_bill_payment_coupous_douyin','payment_discount_amount','varchar(255)');
select p_addfield('pos_bill_payment_coupous_douyin','coupon_pay_amount','varchar(255)');

select p_addfield('pos_bill_payment_coupous_douyin','times_count','int4');
select p_addfield('pos_bill_payment_coupous_douyin','times_used','int4');

/*ALTER TABLE "public"."pos_bill_payment_coupons"
    ADD COLUMN "rwids" varchar(2000)*/;
select p_addfield('pos_bill_payment_coupons','rwids','varchar(2000)');


CREATE TABLE IF NOT EXISTS "public"."pos_bill_payment_coupons_mt" (
      "id" serial,
      "tenancy_id" varchar(255) COLLATE "pg_catalog"."default",
      "store_id" int4,
      "bill_num" varchar(255) COLLATE "pg_catalog"."default",
      "item_code" varchar(255) COLLATE "pg_catalog"."default",
      "count" int4,
      "pay_uuid" varchar(255) COLLATE "pg_catalog"."default",
      "state" int4,
      "create_time" timestamp(6) DEFAULT now(),
      "coupon_code" varchar(255) COLLATE "pg_catalog"."default",

      CONSTRAINT "pos_bill_payment_coupons_mt_pkey" PRIMARY KEY ("id")
);


delete from down_table_version;
delete from pos_data_version where para_code='DBVERSION';

vacuum full;
analyze;
insert into pos_data_version (tenancy_id,store_id,system_name, para_name, para_code,para_value,valid_state) values('',0,'SYS','DBVERSION','DBVERSION','20250228','1');
