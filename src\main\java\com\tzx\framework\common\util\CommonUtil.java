package com.tzx.framework.common.util;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 公共方法
 * 
 * <pre>
 * 注意：
 * 1.hv()表示是否有值
 * 2.eq()表示相等
 * 3.pe()表示打印异常（应该设置一个开关打开异常打印方便程序部署）
 * 4.t*()表示转换成相应的类型
 * 5.Double精度的计算：(1) 价格  (2)价格、数量、金额、税额
 * 6.Double精度计算用的是四舍五入
 * </pre>
 * 
 * <AUTHOR> 2010-11-29
 */
public class CommonUtil
{
	private static Pattern	dPattern				= Pattern.compile("\\d+$");		// 数字
	private static Pattern	wPattern				= Pattern.compile("[a-zA-Z]+$");	// 英文字母
	private static Pattern	cPattern				= Pattern.compile("[\\u4e00-\\u9fa5]+");	// 中文字符

	/**
	 * 是否有值 : hasValue
	 * 
	 * @param rs
	 * @return
	 * @add xiaofeng 2010-11-29
	 */
	public static boolean hv(String s)
	{
		return s == null || 0 == s.trim().length() || s.trim().isEmpty() || "null".equals(s.toLowerCase()) ? false : true;
	}

	/**
	 * 是否有值 : hasValue
	 * 
	 * @param rs
	 * @return
	 * @add xiaofeng 2010-11-29
	 */
	public static boolean hv(Integer rs)
	{
		return rs != null && rs != 0;
	}

	/**
	 * 是否有值 : hasValue
	 * 
	 * @param rs
	 * @return
	 * @add xiaofeng 2013-03-08
	 */
	public static boolean hv(Double rs)
	{
		return rs != null && rs != 0d;
	}

	/**
	 * 是否有值 : hasValue
	 * 
	 * @param rs
	 * @return
	 * @add xiaofeng 2010-12-15
	 */
	public static boolean hv(Date rs)
	{
		return rs != null;
	}

	/**
	 * 是否有值 : hasValue
	 * 
	 * @param rs
	 * @return
	 * @add xiaofeng 2010-11-29
	 */
	public static boolean hv(Long rs)
	{
		return rs != null;
	}

	/**
	 * 是否有值 : hasValue
	 * 
	 * @param str
	 * @return
	 * @add xiaofeng 2010-11-29
	 */
	public static boolean hv(String[] str)
	{
		return str != null && str.length > 0;
	}

	/**
	 * 是否有值 : hasValue
	 * 
	 * <h1>注意：如果list的第一个元素是null那么返回false</h1>
	 * 
	 * @param list
	 * @return
	 * @add xiaofeng 2010-11-29
	 */
	public static boolean hv(List<?> list)
	{
		if (list != null && list.size() > 0)
		{
			if (hv(list.get(0)))
			{
				return true;
			}
		}
		return false;
	}

	/**
	 * 是否有值 : hasValue
	 * 
	 * @param obj
	 * @return
	 * @add xiaofeng 2010-11-29
	 */
	public static boolean hv(Object obj)
	{
		return obj != null;
	}

	/**
	 * 是否有值 : hasValue
	 * 
	 * <h1>注意：该方法主要用于判断多个参数同时不为null时才用</h1>
	 * <h2> 用法:Scm.hv(obj1,obj2,obj3,...,args)</h2>
	 * 
	 * @param obj
	 *            参数1
	 * @param args
	 *            参数列表
	 * @return
	 * @add xiaofeng 2010-12-03
	 */
	public static boolean hv(Object obj, Object... args)
	{
		if (!hv(obj))
		{
			return false;
		}
		for (Object arg : args)
		{
			if (!hv(arg))
			{
				return false;
			}
		}
		return true;
	}

	/**
	 * 转换成String : toString
	 * 
	 * @param obj
	 * @return
	 * @add xiaofeng 2010-11-29
	 */
	public static String ts(Object obj)
	{
		return hv(obj) ? String.valueOf(obj) : null;
	}

	/**
	 * 转换成String : toString
	 * 
	 * @param rs
	 * @return
	 * @add xiaofeng 2011-02-16
	 */
	public static String ts(String rs)
	{
		return rs == null ? "" : rs;
	}

	/**
	 * 转换成Integer : toInteger
	 * 
	 * @param obj
	 * @return
	 * @add xiaofeng 2010-11-29
	 */
	public static Integer ti(String rs)
	{
		return ti(rs, null);
	}

	/**
	 * 转换成Integer : toInteger
	 * 
	 * @param obj
	 * @param defaultInt 
	 * @return
	 * @throws Exception
	 */
	public static Integer ti(String obj, Integer defaultInt)
	{
		return hv(obj) ? Integer.parseInt(obj) : defaultInt;
	}

	/**
	 * 转换成Integer : toInteger
	 * 
	 * @param rs
	 * @return
	 * @add xiaofeng 2011-1-19
	 */
	public static Integer ti(Long rs)
	{
		return hv(rs) ? rs.intValue() : null;
	}

	/**
	 * 转换成Double : toDouble
	 * 
	 * @param rs
	 * @return
	 * @add xiaofeng 2010-12-03
	 */
	public static Double td(String rs)
	{
		return hv(rs) ? Double.parseDouble(rs) : null;
	}

	/**
	 * 转换成有效的Double类型 : toAmount
	 * 
	 * @add xiaofeng 2010-12-9
	 * @param rs
	 * @return
	 */
	public static Double ta(Double rs)
	{
		return hv(rs) ? rs : 0.00;
	}

	/**
	 * 转换成Long : toLong
	 * 
	 * @param rs
	 * @return
	 * @add xiaofeng 2010-11-29
	 */
	public static Long tl(String str)
	{
		return hv(str) ? Long.parseLong(str) : null;
	}

	/**
	 * 转换成Long : toLong
	 * 
	 * @param i
	 * @return
	 * @add xiaofeng 2010-11-29
	 */
	public static Long tl(Integer i)
	{
		return hv(i) ? Long.valueOf(i) : null;
	}

	/**
	 * 返回list.size() : listSize
	 * 
	 * @param list
	 * @return
	 * @add xiaofeng 2010-12-03
	 */
	public static Integer ls(List<?> list)
	{
		return hv(list) ? list.size() : 0;
	}

	/**
	 * 其中一个参数是否有值 : oneHasValue
	 * 
	 * @param args
	 *            参数列表
	 * @return
	 * @add xiaofeng 2010-12-15
	 */
	public static boolean oneHv(Object... args)
	{
		for (Object arg : args)
		{
			if (arg instanceof String)
			{ // 如果类型是字符串特殊处理
				if (hv(CommonUtil.ts(arg)))
				{
					return true;
				}
			}
			else
			{
				if (hv(arg))
				{
					return true;
				}
			}
		}
		return false;
	}

	/**
	 * 是否相等 : equals
	 * 
	 * <h1>注意：src,dest其中一个值不为null</h1>
	 * <h2>用法:Scm.eq(null,1); Scm.eq(1,2); Scm.eq(2,null);等</h2>
	 * 
	 * @param src
	 *            源字符串
	 * @param dest
	 *            目标字符串
	 * @return
	 * @add xiaofeng 2010-12-03
	 */
	public static boolean eq(Object src, Object dest)
	{
		if (src == null && dest == null) return true;
		return hv(src) ? src.equals(dest) : dest.equals(src);
	}
	
	/**
	 * 验证字符串是否为空，为空时返回false,不为空时返回true
	 * @param str
	 * @return
	 */
	public static boolean hasText(Object str) {
	    // 如果是null,返回false;
	    if (null == str)
	        return false;
	
	    str = str.toString().trim();
	
	    if (("".equals(str)) || ("null".equals(str)))
	        return false;
	
	    return true;
	}
	
	/**
	 * 判断Object对象是NUll或empty
	 * @param obj
	 * @return
	 */
	public static boolean isNullOrEmpty(Object obj) 
	{  
	    if (obj == null || "null".equals(String.valueOf(obj)))  
	        return true;  
	
	    if (obj instanceof CharSequence)
	    {
	    	if (((CharSequence) obj).length() == 0)
	        {
	        	return true;
	        }
	    }
	        
	    if (obj instanceof Collection)  
	        return ((Collection<?>) obj).isEmpty();  
	
	    if (obj instanceof Map)  
	        return ((Map<?,?>) obj).isEmpty();  
	
	    if (obj instanceof Object[]) {  
	        Object[] object = (Object[]) obj;  
	        if (object.length == 0) {  
	            return true;  
	        }  
	        boolean empty = true;  
	        for (int i = 0; i < object.length; i++) {  
	            if (!isNullOrEmpty(object[i])) {  
	                empty = false;  
	                break;  
	            }  
	        }  
	        return empty;  
	    }
		return false;
	}
	
	/**
	 * bool为true，返回exp1；为false，返回exp2
	 * 
	 * @param str
	 *            原字符串
	 * @param exp1
	 * @param exp2
	 * @return
	 * @add xiaofeng 2011-3-9
	 */
	public static String nvl(boolean bool, String exp1, String exp2)
	{
		return bool ? exp1 : exp2;
	}
	
	/**
	 * 字符串替换
	 * 
	 * 注意：不需要判断rs == null
	 * 
	 * @param rs
	 *            原字符串
	 * @param target
	 *            需要替换的内容
	 * @param replacement
	 *            替换成的内容
	 * @return
	 * @add xiaofeng 2011-02-16
	 */
	public static String replace(String rs, CharSequence target, CharSequence replacement)
	{
		return rs == null ? "" : rs.replace(target, replacement);
	}

	/** 数组是否包含参数值
	 * @param st
	 * @param obj
	 * @return
	 */
	public static boolean contains(Object[] st, Object obj)
	{
		if (null == st || 0 == st.length || null == obj)
		{
			return false;
		}
		for (Object s : st)
		{
			if (s.equals(obj))
			{
				return true;
			}
		}
		return false;
	}
	
	/**
	 * 字符串首字母是否为数字
	 * 
	 * 注意 : 如果str为null返回false
	 * 
	 * @param str
	 * @return
	 * @add xiaofeng 2011-1-4
	 */
	public static boolean isDigit(String str)
	{
		return hv(str) && Character.isDigit(str.charAt(0));
	}

	/**
	 * 字符串首字母是否为字母
	 * 
	 * 注意 : 如果str为null返回false
	 * 
	 * @param str
	 * @return
	 * @add zhaohao 2017-1-19
	 */
	public static boolean isLetter(String str)
	{
		return hv(str) && Character.isLetter(str.charAt(0));
	}

	/**
	 * 是否数字
	 * 
	 * @param code
	 * @return
	 */
	public static boolean isDigital(String code)
	{
		return hv(code) && dPattern.matcher(code).matches();
	}

	/**
	 * 是否英文字母
	 * 
	 * @param code
	 * @return
	 */
	public static boolean isCharacter(String code)
	{
		return hv(code) && wPattern.matcher(code).matches();
	}

	/**
	 * 判断字符串中是否包含中文
	 * 
	 * 注意 : 如果str为null返回false
	 * 
	 * @param str
	 * @return
	 * @add xiaofeng 2011-1-4
	 */
	public static boolean isChinese(String str)
	{
		return hv(str) && cPattern.matcher(str).find();
	}
}
