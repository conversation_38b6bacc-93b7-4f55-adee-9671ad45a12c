package com.tzx.clientorder.wxorder.po.springjdbc.dao.imp;

import com.tzx.clientorder.wxorder.po.springjdbc.dao.WxInsertOrderDao;
import com.tzx.framework.common.util.dao.impl.GenericDaoImpl;
import com.tzx.pos.base.util.SqlUtil;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description 微信后付下单
 * <AUTHOR>
 * @Date 2018-11-09 15:36
 */
@Repository(WxInsertOrderDao.NAME)
public class WxInsertOrderDaoImp extends GenericDaoImpl implements WxInsertOrderDao{

    @Override
    public void updatePosBill(String tenancyId, String billNum) throws Exception {
        String orderNum = "WX" + billNum;
        StringBuffer sql = new StringBuffer();
        sql.append(" update pos_bill set order_num = '"+ orderNum +"' where bill_num = '"+ billNum +"'");
        super.execute(tenancyId, sql.toString());
    }

    @Override
    public List<JSONObject> getBillMember(String tenancyId, String billNum) throws Exception {
        StringBuffer sql = new StringBuffer();
        sql.append(" select customer_code from pos_bill_member where bill_num = '"+ billNum +"' and tenancy_id = '"+ tenancyId +"'");
        return super.query4Json(tenancyId, sql.toString());
    }

    @Override
    public String getBillInfo(String tenancyId, String billNum) throws Exception {
        StringBuffer sql = new StringBuffer();
        sql.append(" select order_num from pos_bill where bill_num = '"+ billNum +"' and tenancy_id = '"+ tenancyId +"'");
        List<JSONObject> list = super.query4Json(tenancyId, sql.toString());
        return this.getString(tenancyId, sql.toString());
    }

    @Override
    public void updateCCOrder(String tenancyId, String orderCode, String billNum) throws Exception {
        StringBuffer sql = new StringBuffer();
        sql.append(" update cc_order_list set order_state = '10',bill_num = '"+ billNum +"' where order_code = '"+ orderCode +"' and tenancy_id = '"+ tenancyId +"'");
        super.execute(tenancyId, sql.toString());
    }

    @Override
    public JSONObject getOptInfo(String tenancyId, int storeId) throws Exception {
        StringBuffer sql = new StringBuffer();
        sql.append(" select pos_num,opt_num,shift_id from pos_opt_state where content = 'KSSY' and tenancy_id = '"+ tenancyId +"' and store_id = "+ storeId +" order by id desc limit 1");
        List<JSONObject> list = super.query4Json(tenancyId, sql.toString());
        if (null != list && list.size() > 0)
            return list.get(0);
        return null;
    }

    @Override
    public List<JSONObject> getOrderItems(String tenancyId, String orderCode) throws Exception {
        StringBuffer sql = new StringBuffer();
        sql.append(" select item_id,tag from cc_order_item where order_code = '"+ orderCode +"'");
        return super.query4Json(tenancyId, sql.toString());
    }

    @Override
    public void updateBillItem(String tenancyId, String billNum, List<String> itemIds) throws Exception {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < itemIds.size(); i++){
            if (i > 0)
                sb.append(",");
            sb.append("'").append(itemIds.get(i)).append("'");
        }
        String ids = sb.toString();
        StringBuffer sql = new StringBuffer();
        sql.append(" update pos_bill_item set default_state = 'Y' where tenancy_id = '"+ tenancyId +"' and bill_num = '"+ billNum +"' and item_id in ("+ ids +") ");
        super.execute(tenancyId, sql.toString());
    }

    public static void main(String[] args){
        List<String> list = new ArrayList<String>();
        list.add("lili");
        list.add("lili");
        list.add("lili");
        list.add("miemie");
        list.add("meimei");
        if (list.contains("lili")){
            list.remove("lili");
        }
        for (String tmp : list){
            System.err.println(tmp);
        }

    }
}
