package com.tzx.pos.po.springjdbc.dao.imp;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Repository;

import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.dao.imp.BaseDaoImp;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.po.springjdbc.dao.PosBillDao;

/**
 * Created by MrChen on 2017-04-24.
 */
@Repository(PosBillDao.NAME)
public class PosBillDaoImp extends BaseDaoImp implements PosBillDao {
    @Override
    public List<JSONObject> posPayment(String billno, Integer organId) throws Exception {
        // 查询账单付款方式
        StringBuilder payment = new StringBuilder("select id,jzid,is_ysk,name,amount,count,number,phone,shift_id,pos_num,cashier_num,last_updatetime,customer_id,bill_code,remark,currency_amount from pos_bill_payment where bill_num = ? and store_id = ?");
//		 this.jdbcTemplate.query(payment.toString(), );
        List<JSONObject> list=this.query4Json("",payment.toString(),new Object[]{ billno, organId });
        return list;
    }

    @Override
    public List<JSONObject> findPosPayment(String billno) throws Exception {
        StringBuilder payment = new StringBuilder("select * from pos_bill where  bill_num=? and payment_state=?");
        List<JSONObject> list=this.query4Json("", payment.toString(), new Object[]
                {billno ,SysDictionary.PAYMENT_STATE_NOTPAY  });
        return list;
    }

    @Override
    public List<JSONObject> findPosBill(Timestamp timestamp1,Timestamp timestamp) throws Exception {
        StringBuilder sb=new StringBuilder("select tenancy_id,store_id,bill_num,table_code,opentable_time,open_pos_num,open_opt,payment_state from pos_bill  where payment_state=? and opentable_time>? and opentable_time<?");
        List<JSONObject> list=this.query4Json("", sb.toString(), new Object[]
                { SysDictionary.PAYMENT_STATE_NOTPAY,timestamp1 , timestamp });
        return list;
    }

    @Override
    public List<JSONObject> findPosBillTotal(String tenancyId, Integer storeId, String state) throws Exception {
       StringBuilder sb=new StringBuilder("select sum(bill_amount) as total from pos_bill where bill_property=? and store_id=? and tenancy_id=?");
        List<JSONObject> list=this.query4Json(tenancyId, sb.toString(), new Object[]
                { state,storeId , tenancyId });
        return list;
    }

    @Override
    public List<JSONObject> findPosBillInvoice(String tenancy_id,Integer storeId,Date reportDate,String optNum,String posNum,Map<String, Object> map) throws Exception {
        String order_num = ParamUtil.getStringValue(map, "order_num", false, PosErrorCode.NOT_NULL_POS_NUM);//订单号
        String bill_num  = ParamUtil.getStringValue(map, "bill_num", false, PosErrorCode.NOT_NULL_OPT_NUM);//账单编号
        String table_code = ParamUtil.getStringValue(map, "table_code", false, PosErrorCode.NOT_NULL_OPT_NUM);//桌位
        String table_property_id  = ParamUtil.getStringValue(map, "table_property_id ", false, PosErrorCode.NOT_NULL_OPT_NUM);//桌类
        Integer shift_id  = ParamUtil.getIntegerValue(map, "shift_id", false, PosErrorCode.NOT_NULL_OPT_NUM);// 班次
        String cashier_num   = ParamUtil.getStringValue(map, "cashier_num ", false, PosErrorCode.NOT_NULL_OPT_NUM);// 收款员
        String billState = ParamUtil.getStringValue(map, "bill_state", false, PosErrorCode.NOT_NULL_BILL_NUM);
        
        StringBuilder sb=new StringBuilder("select sum(coalesce(pbi.invoice_count,0)) as invoice_num,sum(abs(coalesce(pbi.invoice_count,0)) * coalesce(pbi.invoice_amount,0)) as invoice_total from  pos_bill_invoice  pbi , pos_bill pb where   pbi.bill_num = pb.bill_num ");
        sb.append(" and  pb.tenancy_id= ? and pb.store_id=? and pb.report_date = ? ");
        List<Object> list1=new ArrayList<Object>();
        list1.add(tenancy_id);
        list1.add(storeId);
        list1.add(reportDate);
        
        String billStateStr=SysDictionary.BILL_PROPERTY_CLOSED;
        if("2".equals(billState)){
            billStateStr=SysDictionary.BILL_PROPERTY_OPEN;
        }
        
        if(billStateStr!=null&&!"".equals(billStateStr)){
            sb.append("  and pb.bill_property = ? ");
            list1.add(billStateStr);
        }
        if(order_num!=null&&!"".equals(order_num)){
            sb.append("  and pb.order_num=? ");
            list1.add(order_num);
        }
        if(bill_num!=null&&!"".equals(bill_num)){
            sb.append("  and pb.bill_num=? ");
            list1.add(bill_num);
        }
        if(table_property_id!=null&&!"".equals(table_property_id)){
            sb.append("  and pb.table_code IN (select ti.table_code from tables_info ti where ti.table_property_id="+table_property_id+") ");
        }
        if(table_code!=null&&!"".equals(table_code)){
            sb.append("  and pb.table_code=? ");
            list1.add(table_code);
        }
        if(shift_id!=null&&!"".equals(shift_id.toString())){
            sb.append("  and pb.shift_id=? ");
            list1.add(shift_id);
        }
        if(cashier_num!=null&&!"".equals(cashier_num)){
            sb.append("  and pb.cashier_num=? ");
            list1.add(cashier_num);
        }
        if(null!=optNum&&!"".equals(optNum)){
            sb.append(" and pb.open_opt = ? ");
            list1.add(optNum);
        }
        if(null!=posNum&&!"".equals(posNum)){
            sb.append(" and pb.pos_num =? ");
            list1.add(posNum);
        }
        Object[] o=list1.toArray();
        List<JSONObject> list=this.query4Json(tenancy_id, sb.toString(), o);
        return list;
    }

    @Override
    public List<JSONObject> findPosBillSumTotal(String tenancyId, Integer storeId,Date reportDate, String optNum,String posNum,Map<String, Object> map) throws Exception {
        String billState = ParamUtil.getStringValue(map, "bill_state", false, PosErrorCode.NOT_NULL_BILL_NUM);
        String order_num = ParamUtil.getStringValue(map, "order_num", false, PosErrorCode.NOT_NULL_POS_NUM);//订单号
        String bill_num  = ParamUtil.getStringValue(map, "bill_num", false, PosErrorCode.NOT_NULL_OPT_NUM);//账单编号
        String table_code = ParamUtil.getStringValue(map, "table_code", false, PosErrorCode.NOT_NULL_OPT_NUM);//桌位
        String table_property_id  = ParamUtil.getStringValue(map, "table_property_id ", false, PosErrorCode.NOT_NULL_OPT_NUM);//桌类
        Integer shift_id  = ParamUtil.getIntegerValue(map, "shift_id", false, PosErrorCode.NOT_NULL_OPT_NUM);// 班次
        String cashier_num   = ParamUtil.getStringValue(map, "cashier_num ", false, PosErrorCode.NOT_NULL_OPT_NUM);// 收款员

     //   StringBuilder sb=new StringBuilder("select sum(pb.print_count) as print_count,sum((select coalesce(sum(pbi.item_amount),0) from pos_bill_item pbi where pbi.tenancy_id = pb.tenancy_id and pbi.store_id = pb.store_id and pbi.bill_num = pb.bill_num ))as subtotal \n" +
        StringBuilder sb=new StringBuilder("select sum(pb.print_count) as print_count,sum(pb.subtotal) as subtotal " +
                " from pos_bill pb where pb.tenancy_id =? and pb.store_id = ? and pb.report_date = ?");
        
        List<Object> list1=new ArrayList<Object>();
        list1.add(tenancyId);
        list1.add(storeId);
        list1.add(reportDate);
        
        String billStateStr=SysDictionary.BILL_PROPERTY_CLOSED;
        if("2".equals(billState)){
            billStateStr=SysDictionary.BILL_PROPERTY_OPEN;
        }
        
        if(billStateStr!=null&&!"".equals(billStateStr)){
            sb.append("  and pb.bill_property=? ");
            list1.add(billStateStr);
        }
        if(order_num!=null&&!"".equals(order_num)){
            sb.append("  and pb.order_num=? ");
            list1.add(order_num);
        }
        if(bill_num!=null&&!"".equals(bill_num)){
            sb.append("  and pb.bill_num=? ");
            list1.add(bill_num);
        }
        if(table_property_id!=null&&!"".equals(table_property_id)){
            sb.append("  and pb.table_code IN (select ti.table_code from tables_info ti where ti.table_property_id="+table_property_id+") ");
        }
        if(table_code!=null&&!"".equals(table_code)){
            sb.append("  and pb.table_code=? ");
            list1.add(table_code);
        }
        if(shift_id!=null&&!"".equals(shift_id.toString())){
            sb.append("  and pb.shift_id=? ");
            list1.add(shift_id);
        }
        if(cashier_num!=null&&!"".equals(cashier_num)){
            sb.append("  and pb.cashier_num=? ");
            list1.add(cashier_num);
        }
        if(null!=optNum&&!"".equals(optNum)){
            sb.append(" and pb.open_opt = ? ");
            list1.add(optNum);
        }
        if(null!=posNum&&!"".equals(posNum)){
            sb.append(" and pb.pos_num =? ");
            list1.add(posNum);
        }
        Object[] o=list1.toArray();
        List<JSONObject> list=this.query4Json(tenancyId, sb.toString(),o);
        return list;
    }
  @Override
    public List<JSONObject>findTableCode(String tableCode)throws Exception{
        StringBuilder sb=new StringBuilder("select table_code from pos_tablestate ");
        if(null!=tableCode&&!"".equals(tableCode)){
            sb.append(" where table_code not in("+tableCode+")");
        }
        List<JSONObject> list=this.queryString4Json("", sb.toString());
        return list;
    }


}
