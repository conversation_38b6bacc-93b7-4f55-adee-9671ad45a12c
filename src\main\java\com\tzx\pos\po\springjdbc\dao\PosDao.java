package com.tzx.pos.po.springjdbc.dao;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.tzx.framework.common.constant.Oper;
import net.sf.json.JSONObject;

import com.tzx.base.bo.PosCodeService;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.entity.Pagination;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.pos.base.dao.BaseDao;
import com.tzx.pos.bo.dto.PosBillMembers;
import com.tzx.pos.bo.dto.PosItemSort;
import com.tzx.pos.bo.dto.PosLog;
import com.tzx.pos.bo.dto.PosTableFind;

public interface PosDao extends BaseDao
{
	String	NAME	= "com.tzx.pos.po.springjdbc.dao.imp.PosDaoImp";

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param paraCode
	 * @param paraValue
	 * @throws Exception
	 */
	public void updateDataVersionValueByCode(String tenancyId,int storeId,String paraCode,String paraValue)throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param paraCode
	 * @return
	 * @throws Exception
	 */
	public JSONObject getDataVersionByCode(String tenancyId,int storeId,String paraCode)throws Exception;
	
	/** 新增数据版本
	 * @param tenancyId
	 * @param storeId
	 * @param systemName
	 * @param modelName
	 * @param paraName
	 * @param paraCode
	 * @param paraValue
	 * @param paraDefaut
	 * @param paraType
	 * @param validState
	 * @param valuesName
	 * @param paraRemark
	 * @throws Exception
	 */
	public void insertDataVersion(String tenancyId,int storeId,String systemName,String modelName,String paraName,String paraCode,String paraValue,String paraDefaut,String paraType,String validState,String valuesName,String paraRemark)throws Exception;

	public void updatePosTableState(String tenantId, Integer organId) throws Exception;
	/**
	 * 添加数据
	 * @param tenantId
	 * @param organId
	 * @throws Exception
	 */
	public int insertPosTableState(String tenantId, Integer organId,String tableCode,String optNum) throws Exception;
	
	// 根据机构id获得班次
	public List<JSONObject> getDutyOrderByOrganId(String tenantId, Integer organId) throws Exception;
	public String getDutyOrderByOrganIdEx(String tenantId, Integer organId) throws Exception;

	// 桌类属性
	public List<JSONObject> getTablePropertyByOrganId(String tenantId, Integer organId) throws Exception;
	public String getTablePropertyByOrganIdEx(String tenantId, Integer organId) throws Exception;

	// 区域
	public List<JSONObject> getBussinessArea(String tenantId, Integer organId) throws Exception;
	public String getBussinessAreaEx(String tenantId, Integer organId) throws Exception;

	// 菜品类别
	public List<JSONObject> getItemClassByTenantId(String tenantId, Integer organId, String channel) throws Exception;
	
	// 菜品格式
	public List<JSONObject> getDishFormat(String tenantId, Integer organId, String channel) throws Exception;
	
	public String getItemClassByTenantIdEx(String tenantId, Integer organId) throws Exception;

	// 服务费种
	public List<JSONObject> getServiceFeeTypeByOrganId(String tenantId, Integer organId) throws Exception;
	public String getServiceFeeTypeByOrganIdEx(String tenantId, Integer organId) throws Exception;

	// 口味备注
	public List<JSONObject> getItemTastes(String tenantId, Integer organId) throws Exception;
	public String getItemTastesEx(String tenantId, Integer organId) throws Exception;

	// 做法
	public List<JSONObject> getItemMethodsByTenantId(String tenantId, Integer organId) throws Exception;
	public String getItemMethodsByTenantIdEx(String tenantId, Integer organId) throws Exception;

	// 退菜、奉送、优惠、恢复账单、免单原因
	public List<JSONObject> getUnusualReasonByTenantId(String tenantId, Integer organId) throws Exception;	
	public String getUnusualReasonByTenantIdEx(String tenantId, Integer organId) throws Exception;

	// 折扣方案
	public List<JSONObject> getDiscountCaseByTenantId(String tenantId, Integer organId) throws Exception;
	public String getDiscountCaseByTenantIdEx(String tenantId, Integer organId) throws Exception;

	// 折扣方案明细
	public Data getDiscountCaseDetailsByTenantId(String tenantId, Integer organId, Pagination pagination) throws Exception;
	public String getDiscountCaseDetailsByTenantIdEx(String tenantId, Integer organId) throws Exception;

	// 付款方式
	public List<JSONObject> getPaymentWayByOrganId(String tenantId, Integer organId) throws Exception;
	public List<JSONObject> getPaymentWayByOrganId(String tenantId, Integer organId, String devicesCode) throws Exception;
	public String getPaymentWayByOrganIdEx(String tenantId, Integer organId) throws Exception;
	
	//付款方式显示类型表
	public List<JSONObject> getPaymentShowtypeByOrganId(String tenantId, Integer organId) throws Exception;	
	public String getPaymentShowtypeByOrganIdEx(String tenantId, Integer organId) throws Exception;
	
	//第三方商户配置信息
	@Deprecated
	public List<JSONObject> getCcThirdOrganInfoByOrganId(String tenantId, Integer organId) throws Exception;
	
	public String getDiningTypeByTenantIdEx(String tenantId, Integer organId) throws Exception;

	/**用户PAd和电脑图片**/
	public List<JSONObject> getBohImgByPcOrPad(String tenantId, Integer organId)throws Exception;	
	public String getBohImgByPcOrPadEx(String tenantId, Integer organId)throws Exception;

	/**沽清菜品**/
	public List<JSONObject> getItemSoldout(String tenantId, Integer organId)throws Exception;

	/**急推菜品**/
	public List<JSONObject> getItemWorrysale(String tenantId, Integer organId)throws Exception;

	// 菜品
	public Data getDishByTenantId(String tenantId, Integer organId, String channel, Pagination pagination) throws Exception;

	//public Data getDishByTenantId(String tenantId, Integer organId) throws Exception;
	public String getDishByTenantIdEx(String tenantId, Integer organId) throws Exception;

	// 规格
	public Data getItemUnitsByTenantId(String tenantId, Integer organId, String channel, Pagination pagination) throws Exception;

	public String getItemUnitsByTenantIdEx(String tenantId, Integer organId) throws Exception;

	// 桌位
	public List<JSONObject> getTablesByTenantIdAndOrganId(String tenantId, Integer organId) throws Exception;
	public String getTablesByTenantIdAndOrganIdEx(String tenantId, Integer organId) throws Exception;

	// 时段价格
	public List<JSONObject> getTimePriceByOrganId(String tenantId, Integer organId, String channel) throws Exception;

	//public List<JSONObject> getTimePriceByOrganId(String tenantId, Integer organId) throws Exception;
	
	public String getTimePriceByOrganIdEx(String tenantId, Integer organId) throws Exception;

	// 时段价格明细
	public List<JSONObject> getTimePriceItemByOrganId(String tenantId, Integer organId, String channel) throws Exception;

//	public List<JSONObject> getTimePriceItemByOrganId(String tenantId, Integer organId) throws Exception;
	
	public String getTimePriceItemByOrganIdEx(String tenantId, Integer organId) throws Exception;

	// 套餐明细
	public List<JSONObject> getItemComboDetailsByOrganId(String tenantId, Integer organId, String channel) throws Exception;

	public String getItemComboDetailsByOrganIdEx(String tenantId, Integer organId) throws Exception;

	// 套餐项目组明细
	
	public List<JSONObject> getItemGroupDetailsByOrganId(String tenantId, Integer organId, String channel) throws Exception;
	public String getItemGroupDetailsByOrganIdEx(String tenantId, Integer organId) throws Exception;

	//会员卡种
	public List<JSONObject> getCrmCardClassByOrganId(String tenantId, Integer organId) throws Exception;	
	public String getCrmCardClassByOrganIdEx(String tenantId, Integer organId) throws Exception;
	
	//会员价
//	public List<JSONObject> getCrmItemVipByOrganId(String tenantId, Integer organId) throws Exception;
	
	public String getCrmItemVipByOrganIdEx(String tenantId, Integer organId) throws Exception;
	
	// 人员同步
	public List<JSONObject> getUsersByTenantId(String tenantId, Integer organId) throws Exception;	
	public String getUsersByTenantIdEx(String tenantId, Integer organId) throws Exception;

	// 系统参数同步
	public List<JSONObject> getSysParameter(String tenantId, Integer organId) throws Exception;	
	public String getSysParameterEx(String tenantId, Integer organId) throws Exception;

	// 获取团体会员
	public List<JSONObject> getCrmInCorporations(String tenantId, Integer organId) throws Exception;
	public String getCrmInCorporationsEx(String tenantId, Integer organId) throws Exception;
	
	// 获取团体会员挂账人
	public List<JSONObject> getCrmInCorporationPersons(String tenantId, Integer organId) throws Exception;
	public String getCrmInCorporationPersonsEx(String tenantId, Integer organId) throws Exception;

	// 套餐项目组同步
	public List<JSONObject> getItemGroup(String tenancyId, int storeId) throws Exception;
	public String getItemGroupEx(String tenancyId, int storeId) throws Exception;

	// 设备同步
	public List<JSONObject> getDevicesByOrgan(String tenantId, Integer organId) throws Exception;	
	public String getDevicesByOrganEx(String tenantId, Integer organId) throws Exception;
	
	/**用户折扣方案权限*/
	public List<JSONObject> getUserDiscountCaseByOrgan(String tenantId, Integer organId) throws Exception;	
	public String getUserDiscountCaseByOrganEx(String tenantId, Integer organId) throws Exception;

	/**用户折扣权限*/
	public List<JSONObject> getUserDiscountAuthorityByOrgan(String tenantId, Integer organId) throws Exception;	
	public String getUserDiscountAuthorityByOrganEx(String tenantId, Integer organId) throws Exception;
		
	// 系统设置
	public void systemConfig(String mac, Integer orginId, String tableCode) throws SystemException;

	// 桌位查询
	public List<PosTableFind> findTable(String tenantId, Integer organId, String tableNo, Integer tablePropertyId, String tableState, Integer businessAreaId) throws SystemException;

	// 转台
	public void changeTable(String tenantId, Integer organId, String stableCode,String state) throws SystemException;

	// 锁定或解锁桌位
	public void lockOrUnlockTable(String tenantId, Integer organId, String optNum, String posNum, String tableCode, String mode, Data result) throws SystemException;

	// 日结
	public void dayReport(String tenantId, Integer organId, Map<String, Object> map, Data result) throws SystemException, Exception;

	// 查询服务员
	public void findWaiter(String tenantId, Integer organId, String billno, Data result) throws SystemException;

	// 交班
	public Data updateOptStateForChangeShift(String tenantId, Integer organId, String optNum, String posNum, Date reportDate, String isAllPos,Integer shiftId) throws SystemException;

	// 获取实时菜品资料
	public Data getDishByClass(String tenantId, Integer organId, String sql) throws SystemException;

	// 查询桌位锁台信息
	public List<JSONObject> findLockTable(String tenantId, String tableCode, Integer organId) throws Exception;
	
	// 查询桌位锁台信息
	public List<JSONObject> findLockTable(String tenantId, Integer organId, String tableCode) throws Exception;

	// 检验机台是否有人登录
	public void checkPosLogin(String tenantId, Integer organId, Date reportDate, String posNum, String optNum, Data result) throws SystemException;

	/**
	 * 提交评价
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param param
	 * @return
	 * @throws Exception
	 * <AUTHOR> 2015-09-16
	 */
	int updateGiveSuggest(String tenancyId, int storeId, JSONObject param) throws Exception;

	// 是否已打烊
	public boolean isNotifyDayEnd(String tenancyId, Integer organId) throws Exception;

	// 菜品排行
	public List<PosItemSort> findItemSort(String qsDate, String tenantId, Integer organId) throws Exception;

	// 特价菜品
	public List<JSONObject> findItemCheap(String tenantId, Integer organId, String reportDate) throws Exception;

	// 查询急推菜品
	public List<JSONObject> findWorryItem(String tenantId, Integer organId, Map<String, Object> map) throws Exception;

	// 获取沽清数量
	public Data getSoldOutCount(String tenantId, Integer organId, Map<String, Object> map) throws SystemException;
	/**
	 * 根据套餐id获取关联的套餐Id
	 * @param itemId
	 * @return
	 * @throws SystemException
	 */
	public  List<JSONObject> getSoldDetailsIdComboId(Integer itemId,String str) throws Exception;
	/**
	 * 根据itemId 获取套餐id
	 * @param itemId
	 * @return
	 * @throws SystemException
	 */
	public  List<JSONObject> getSoldOutComboId(Integer itemId,String str) throws Exception;
	/**
	 * 根据itemId 获取套餐详情itemid
	 * @param itemId
	 * @return
	 * @throws SystemException
	 */
	public  List<JSONObject> getComboItemByItemId(Integer itemId) throws Exception;
	
	/**
	 * 查询套餐相关的Id
	 * @param itemIdStr
	 * @return
	 * @throws SystemException
	 */
	public  List<JSONObject> findComboItemId(String  itemIdStr) throws Exception;

	/**
	 * 根据itemId 获取套餐最小数量
	 * @param itemId
	 * @return
	 * @throws SystemException
	 */
	public  Integer geComboMinByItemId(Integer itemId,String itemIdStr) throws Exception;
	
	public Integer geComboMAXByItemId(String iitem_id) throws Exception ;
	
	/**
	 * 根据单品关联的套餐id 查询套餐下单品的最小沽清数量
	 * @param itemId
	 * @return
	 * @throws SystemException
	 */
	public Integer getSingleMinByItemId(String iitem_id) throws Exception;
	
	/**
	 * 查询是否为他餐 移动设备验证点餐
	 * @param itemId
	 * @return
	 * @throws SystemException
	 */
	public boolean isCheckComboByItemId(String iitem_id) throws Exception ;
	
	/**
	 * 查询套餐下单品
	 * @param itemId
	 * @return
	 * @throws SystemException
	 */
	public List<JSONObject> getSingleByItemId(String iitem_id) throws Exception ;
	
	
	/**
	 * 根据itemId 根据单品id查询套餐沽清数据
	 * @param itemId
	 * @return
	 * @throws SystemException
	 */
	public  List<JSONObject> getComboOutBySingelItemId(Integer itemId) throws Exception;
	
	// 查询沽清
	public List<JSONObject> findSoldOut(String tenantId, Integer organId, Map<String, Object> map) throws Exception;

	// 桌位查询
	@Deprecated
	public List<PosTableFind> tableStateSelf(String tenantId, Integer organId, String tableNo, Integer tablePropertyId, String tableState, Integer businessAreaId) throws SystemException;

	/**
	 * 查询自动上传时间
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @return
	 * @throws Exception
	 * <AUTHOR> 2015-09-16
	 */
	@Deprecated
	Date findPosUploadDate(String tenancyId, int storeId) throws Exception;

	/**
	 * 查询未上传数据
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param param
	 *            {report_date:'',last_updatetime:''}
	 * @return
	 * @throws Exception
	 * <AUTHOR> 2015-09-16
	 */
	@Deprecated
	List<JSONObject> findNotUploadData(String tenancyId, int storeId, JSONObject param) throws Exception;

	// 取消奉送
	@Deprecated
	public void cancGiveItem(String tenantId, Integer organId, String billno, Integer rwid, Data result) throws SystemException;

	// 获取交班数据
	@Deprecated
	public Data getShiftData(String tenantId, Integer organId, Map<String, Object> map) throws SystemException;

	// 数据上传
	@Deprecated
	public Data dataTrans(String tenantId, Integer organId, Date reportDate) throws SystemException;

	// 复制桌位
	@Deprecated
	public Data posCopyTable(String tenantId, Integer organId, List<Map<String, Object>> listParams, PosLog log, PosCodeService codeService) throws SystemException;

	// 单品转台
	@Deprecated
	public Data singleChangeTable(String tenantId, Integer organId, Map<String, Object> map) throws SystemException;
	
	/** 查询交班历史
	 * @param tenancyId
	 * @param storeId
	 * @param reportDate
	 * @param optNum
	 * @param posNum
	 * @return
	 * @throws Exception
	 */
	public List<?> getChangshiftList(String tenancyId, int storeId,Date reportDate,String optNum,String posNum) throws Exception;

	
	/**
	 * 保存三方交易记录表
	 * @param param
	 * @param mainId 
	 * @param data
	 * @throws Exception
	 */
	public void savePosThirdpay(Data param,JSONObject data, int mainId)throws Exception;
	
	/**
	 * 保存三方交易原始数据记录表
	 * @param param
	 * @param mainId 三方交易记录表主键编号
	 * @param thirdpayLogJson 原始数据
	 * @throws Exception
	 */
	public void savePosThirdpayDetail(Data param,JSONObject thirdpayLogJson, int mainId)throws Exception;
	

	/**
	 * 查询三方交易记录表
	 * @param param
	 * @param jsobj
	 * @return
	 * @throws Exception
	 */
	public JSONObject queryPosThirdpay(JSONObject jsobj,Data param)throws Exception;
	
	/**
	 * 查询三方交易原始数据记录表
	 * @param mainId 三方交易记录表主键编号
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> queryPosThirdpayDetail(String tenancyId,int mainId)throws Exception;
	
	/**
	 * 根据打印模板id获取对应的打印机信息
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param printMode
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getPrinter(String tenancyId, int storeId, String printMode) throws Exception;

    /**
     * 根据打印模板id获取相应的站点打印机
     * @param tenancyId
     * @param storeId
     * @param printMode
     * @return
     * @throws Exception
     */
	public List<JSONObject> getDevicesPrinter(String tenancyId, int storeId, String printMode, String posNum) throws Exception;

    /**
     * 根据打印模板id获取相应的区域打印机
     * @param tenancyId
     * @param storeId
     * @param printMode
     * @return
     * @throws Exception
     */
	public List<JSONObject> getAreaPrinter(String tenancyId, int storeId, String printMode, String billNum) throws Exception;

//	/**
//	 * 获取指定打印机绑定的桌位
//	 * 
//	 * @param tenancyId
//	 * @param storeId
//	 * @param printerId
//	 * @return
//	 * @throws Exception
//	 */
//	public List<JSONObject> getBindTableList(String tenancyId, int storeId, String printerId) throws Exception;
	/**
	 * 获取菜品小类
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param printMode
	 * @param para
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getPrintItemClass(String tenancyId, int storeId, String printMode, JSONObject para) throws Exception;
	
	/**
	 * 获取可以在该打印机打印的菜品
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param para
	 * @param printMode
	 * @param itemClass
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getPrintItemList(String tenancyId, int storeId, JSONObject para, String printMode, int itemClass) throws Exception;
	
	/**
	 * 获取区域id
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param para
	 * @return
	 * @throws Exception
	 */
	public JSONObject getPrinterTable(String tenancyId, int storeId, JSONObject para) throws Exception;
	
	/**
	 * 获取打印模板绑定的pos机
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param para
	 * @param printMode
	 * @return
	 * @throws Exception
	 */
	public JSONObject getDeviceFromPrinterModel(String tenancyId, int storeId, JSONObject para, String printMode) throws Exception;

	/**
	 * 获取区域id
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param para
	 * @return
	 * @throws Exception
	 */
	public JSONObject checkPrinerTable(String tenancyId, int storeId, JSONObject para) throws Exception;
	
	/**
	 * 获取打印参数
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param printMode
	 * @param printerJo
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getPrintParam(String tenancyId, int storeId, String printMode, JSONObject printerJo) throws Exception;
	
	/**
	 * 生成打印任务
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param printer
	 * @param params
	 * @param printMode
	 * @param orderNo
	 * @return
	 * @throws Exception
	 */
	public void insertPrintTask(String tenancyId, int storeId, JSONObject printer, JSONObject params, String printMode) throws Exception;
	
	/**
	 * 更新菜品打印状态
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param bill_num
	 * @return
	 * @throws Exception
	 */
	public void UpdateItemPrintTag(String tenancyId, int storeId, String bill_num) throws Exception;
	
	/**
	 * 检查该打印机是否设定厨打模板
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param para
	 * @return
	 * @throws Exception
	 */
	public int checkKitchenModel(String tenancyId, int storeId, JSONObject para) throws Exception;
	
	/**
	 * 检查该打印机是否设定厨打菜品
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param posNum
	 * @param para
	 * @return
	 * @throws Exception
	 */
	public int checkKitchenItem(String tenancyId, int storeId, JSONObject para) throws Exception;

	/**
	 * 清空pos_print_task表
	 * @param tenancyId
	 * @param storeId
	 * @throws Exception
	 */
	void deletePosPrintTask(String tenancyId, int storeId) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @throws Exception
	 */
	public void deletePosPrint(String tenancyId, int storeId) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @throws Exception
	 */
	public void deletePosPrintList(String tenancyId, int storeId) throws Exception;
	
	/**
	 * 账务基础数据平衡 检查
	 * @param tenantId
	 * @param organId
	 * @param map 前端传入参数
	 * @throws Exception
	 */
	public void checkFinanceBasedata(String tenantId, Integer organId,
			Map<String, Object> map)throws Exception;
	
	/**
	 * 本地验证优惠卷
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param couponsList
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getCouponsValiData(String tenancyId, Integer storeId,String billNum,String typeId) throws Exception;
	
	/**
	 * 验证优惠卷是否可以使用
	 * @param storId
	 * @param billNum
	 * @param coupnosCode
	 * @return
	 */
	public List<JSONObject> getCouponsValiCode(String tenancyId, String coupnosCode)throws Exception;
	
		/**
	 * 查询设备状态
	 * @param devicesCode
	 * @param result
	 * @return
	 * @throws Exception
	 */
	public String getDevicesDataState(String devicesCode)throws Exception;
	
	/**
	 * 获得绑定的posnum
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param posNum
	 * @return
	 * @throws Exception
	 */
	public void getBindOptNum(String tenancyId, int storeId, JSONObject para) throws Exception;
	
//	/**
//	 * @param tenancyId
//	 * @param storeId
//	 * @param reportDate
//	 * @param waiterId
//	 * @param paymentId
//	 * @return
//	 * @throws Exception
//	 */
//	public Double getReceiveAmountByPayment(String tenancyId, int storeId,Date reportDate,Integer waiterId,Integer paymentId)throws Exception;


	/***
	 * 无码优惠卷流水插入
	 * @param tenancyId
	 * @param storeId
	 * @param chanel
	 * @param businessDate
	 * @param billCode
	 * @param typeId
	 * @param itemId
	 * @param unitId
	 * @param price
	 * @param itemNum
	 * @param discountMoney
	 * @param discountNum
	 * @param classId
	 * @param dealValue
	 * @param dealName
	 * @param remark
	 * @param couponsPro
	 * @throws Exception
	 */
	@Deprecated
	public void insertPosCouponsUseItem(String tenancyId,Integer storeId, String chanel,Date businessDate ,String billCode,Integer typeId ,Integer itemId,Integer unitId,Double price,Double itemNum,Double discountMoney,Double discountNum, Integer classId, Double dealValue, String dealName, String remark,String couponsPro) throws Exception;
	
	/**
	 * 查询账单绑定的会员信息
	 */
	@Deprecated
	public List<PosBillMembers> selectMembersByBillNum(String tenantId, Integer organId, String billNum) throws Exception;

	/**
	 * 其它渠道分配套餐明细
	 * @param tenancyId
	 * @param storeId
	 * @throws Exception
	 */
	public void otherChanelAddMeallist(String tenancyId, int storeId) throws Exception;

	/**
	 * (同步活动规则)
	 * @param tenancyId
	 * @return
	 */
	public List<JSONObject> getActivityRuleDetails(String tenancyId) throws Exception;

	/**
	 * (同步活动商品限制规则)
	 * @param tenancyId
	 * @return
	 */
	public List<JSONObject> getCrmActivityItem(String tenancyId) throws Exception;
	public List<JSONObject> getSysDictionary(String tenancyId) throws Exception;
	public String getSysDictionaryEx(String tenancyId) throws Exception;
	public List<JSONObject> getAndroidSysDictionary(String tenancyId) throws Exception;

    /**
     * 初始化物理打印机打印序号
     * @param tenancyId
     * @param storeId
     * @param reportDate
     * @throws Exception
     */
	public void initPrinterSerialNumber(String tenancyId, int storeId, Date reportDate) throws Exception;

    /**
     * 同步打印机
     * @param tenancyId
     * @param storeId
     * @throws Exception
     */
	public void synzPrinter(String tenancyId, int storeId) throws Exception;

    /**
     * 清理15天前的打印序号数据
     * @param tenancyId
     * @param storeId
     * @param reportDate
     * @throws Exception
     */
	public void deleteBeforePrinterSerial(String tenancyId, int storeId, Date reportDate) throws Exception;

    /**
     * 菜单按钮配置信息
     * @param tenantId
     * @param organId
     * @return
     * @throws Exception
     */
    public List<JSONObject> getMenuButtonConfig(String tenantId, Integer organId)throws Exception;
    
    /**
     * 
      * @Description: 手机端退菜 根据账单编号 查询可选菜品id 沽清信息
      * @Title:getBatchNumByBillNum
      * @param:@param tenantId
      * @param:@param billNum
      * @param:@return
      * @param:@throws Exception
      * @return: List<JSONObject>
      * @author: shenzhanyu
      * @version: 1.0
      * @Date: 2018年1月10日
     */
    public List<JSONObject> getBatchNumByBillNum(String tenantId ,String billNum,String itemId) throws Exception;

	/**
	 * 支付方式按钮排序配置信息
	 * @param tenantId
	 * @param organId
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getPaymentWayButtonConfig(String tenantId, Integer organId) throws Exception;

    /**
     * 支付方式是否支持打开钱箱
     * @param tenancyId
     * @param storeId
     * @param billNum
     * @param printMode
     * @return
     * @throws Exception
     */
	boolean payWayIsOpenCashBox(String tenancyId, int storeId, String billNum, String printMode) throws Exception;
	
	/**
	 * 查询订单是否存在
	 * @param tenantId
	 * @param organId
	 * @param billCode
	 * @return
	 * @throws Exception
	 */
	public Boolean getBillCode(String tenantId, Integer organId, String billCode) throws Exception;

	/**
	 * 就餐类型信息
	 * @param tenantId
	 * @param organId
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getDiningType(String tenantId, Integer organId) throws Exception ;

    public List<JSONObject> getHqItemTasteDetails(String tenantId, Integer organId) throws Exception;
	
	/** 修改KVS销单
	 * @param tenancyId
	 * @param storeId
	 * @param kvs_num
	 * @param currentTime
	 * @throws Exception
	 */
	public void updateKvsMopState(String tenancyId,Integer storeId,String kvs_num,Timestamp currentTime) throws Exception;

    /** 同步菜品档口信息
     * @param tenantId
     * @param organId
     * @return
     */
    List<JSONObject> getHqPrinter(String tenantId, Integer organId) throws Exception;

	/**
	 * 根据账单号获取菜品
	 * @param tenantId
	 * @param organId
	 * @param bill_num
	 * @return
	 * @throws Exception
	 */
    List<JSONObject> getPosBillItemByBillNum(String tenantId, Integer organId,String bill_num) throws Exception;

	/**
	 * 获取禁用菜品
	 * @param tenantId
	 * @param organId
	 * @return
	 * @throws Exception
	 */
	 List<JSONObject> getPosForbiddenItem(String tenantId, Integer organId) throws Exception;


	/**
	 * 获取修改套餐默认套餐项
	 * @param tenantId
	 * @param organId
	 * @return
	 * @throws Exception
	 */
	 List<JSONObject> getPosItemComboDetailsDefault(String tenantId, Integer organId) throws Exception;

	/**
	 * 根据桌位好查询桌位名称
	 * @param tenantId
	 * @param organId
	 * @param tableCode
	 * @return
	 * @throws Exception
	 */
	String getTableNameByTableCode(String tenantId, Integer organId,String sbill_num)throws Exception;

	/**
	 * 获取角色表
	 * @param tenantId
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> getRoles(String tenantId) throws Exception;


	/**
	 * 根据pos_user_name 查询 user_name
	 * @param tenantId
	 * @param organId
	 * @param posUserName
	 * @return
	 * @throws Exception
	 */

	String findUserName(String tenantId,Integer organId,String posUserName) throws Exception;

	void addEmpAndAuth(String  tenantId,Integer organId,JSONObject paraJson) throws Exception;

	void updateEmployeeInfo(String  tenantId,Integer organId,JSONObject paraJson,Oper opertype) throws Exception;

	JSONObject findItemComboDetails(String tenancyId, int setmeal_id, int item_id) throws Exception;
}
