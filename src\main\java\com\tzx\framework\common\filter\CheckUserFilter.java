package com.tzx.framework.common.filter;

import java.io.IOException;

import javax.annotation.Resource;
import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.framework.common.util.redis.BaseRedisUtil;

public class CheckUserFilter implements Filter
{
	private String			loginPage;
	protected FilterConfig	filterConfig;

	@Autowired
	private BaseRedisUtil redisUtil;

	public void init(FilterConfig config) throws ServletException
	{
		this.filterConfig = config;
		this.loginPage = config.getInitParameter("loginPage");
	}

	public void doFilter(ServletRequest req, ServletResponse res, FilterChain chain) throws IOException, ServletException
	{
		HttpServletRequest hreq = (HttpServletRequest) req;
		HttpServletResponse hres = (HttpServletResponse) res;
		HttpSession session = hreq.getSession();
		try
		{
			//String token = hreq.getHeader(Constant.TOKEN);
			String clientToken = hreq.getParameter(Constant.SPRING_SESSION_PARAM);

			System.out.println("ReqUrl: "+hreq.getRequestURI()+", clientToken: "+clientToken);

			//String token = ((HttpServletRequest) req).getHeader(Constant.TOKEN);
			/*String tenentid = null;
			if(token!=null) {			// web端用户, 使用token
				tenentid = redisUtil.get(token);
			} else {					// 其他端用户，或未使用token
				Object objTenentid = session.getAttribute("tenentid") == null ? hreq.getParameter("tenentid") : session.getAttribute("tenentid");
				if(objTenentid!=null){
					tenentid = objTenentid.toString();
				}
			}*/

			String tenentid = null;
			Object objTenentid = session.getAttribute("tenentid") == null ? hreq.getParameter("tenentid") : session.getAttribute("tenentid");
			if(objTenentid!=null){
				tenentid = objTenentid.toString();
			}

			if (tenentid == null)
			{
				String headerString = hreq.getHeader("X-Requested-With");

				if("XMLHttpRequest".equalsIgnoreCase(headerString))
				{
					hres.sendError(20000, "tzx login.");
				}
				else
				{
					hres.sendRedirect(hreq.getContextPath() + loginPage);
				}
			}
			else
			{
				DBContextHolder.setTenancyid(tenentid);
				chain.doFilter(req, res);
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}

	}

	public void setFilterConfig(final FilterConfig filterConfig)
	{
		this.filterConfig = filterConfig;

	}

	public void destroy()
	{
		this.filterConfig = null;
	}

}