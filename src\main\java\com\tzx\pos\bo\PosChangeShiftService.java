package com.tzx.pos.bo;

import java.util.List;

import net.sf.json.JSONObject;

import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.SystemException;

/**交班
 * 
 * <AUTHOR>
 */
public interface PosChangeShiftService extends PosBaseService
{
	String	NAME	= "com.tzx.pos.bo.imp.PosChangeShiftServiceImp";
	
	/** 获取交班数据
	 * @param param
	 * @param result
	 * @throws SystemException
	 */
	public void getShiftData(Data param, Data result) throws SystemException;
	
	/**
	 * 签退并交班
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param param
	 * @throws Exception
	 */
	public void signOffAndChangeShift(String tenancyId, int storeId, List<?> param, JSONObject printJson) throws Exception;
	
	/**
	 * 交班查询
	 * 
	 * @param param
	 * @param result
	 * @throws SystemException
	 */
	public void checkChangShift(Data param, Data result) throws SystemException;
	
	/**
	 * 历史交班明细查询
	 * 
	 * @param param
	 * @param result
	 * @throws SystemException
	 */
	public void getDetailChangShift(Data param, Data result) throws SystemException;
}
