package com.tzx.pos.bo;

import java.util.List;

import net.sf.json.JSONObject;

import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.SystemException;

/**
 *
 */
@Deprecated
public interface PaymentService extends PosBaseService
{
	String	NAME	= "com.tzx.pos.bo.imp.PaymentServiceImp";

	/**
	 * 支付宝预下单接口
	 * 
	 * @param data
	 * @param path
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> precreate(Data data, String path) throws Exception;

	/**
	 * 支付宝条码支付
	 * 
	 * @param data
	 * @param path
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> barcode(Data data, String path) throws Exception;

	/**
	 * 支付宝订单查询
	 * 
	 * @param data
	 * @param path
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> query(Data data, String path) throws Exception;

	/**
	 * 支付宝取消订单
	 * 
	 * @param data
	 * @param path
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> cancle(Data data, String path) throws Exception;

	/**
	 * 支付宝退款
	 * 
	 * @param data
	 * @param path
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> refund(Data data, String path) throws Exception;

	/**
	 * 微信统一下单
	 *  (扫码支付:用户扫描商户展示在各种场景的二维码进行支付。)
	 * @param data
	 * @param path
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> unifiedorder(Data data, String path) throws Exception;

	/**
	 * 微信刷卡支付
	 * (刷卡支付:商户收银员用扫码设备扫描用户的条码/二维码，商户收银系统提交支付)
	 * @param data
	 * @param path
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> micropay(Data data, String path) throws Exception;

	/** 微信订单查询 
	 * 
	 * @param data
	 * @param path
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> orderquery(Data data, String path) throws Exception;

	/**
	 * 微信关闭订单
	 * 
	 * @param data
	 * @param path
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> closeorder(Data data, String path) throws Exception;

	/**
	 * 微信退款
	 * 
	 * @param data
	 * @param path
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> refundorder(Data data, String path) throws Exception;

	/**
	 * 微信退款查询
	 * 
	 * @param data
	 * @param path
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> refundquery(Data data, String path) throws Exception;
	
	/** 统一结账接口
	 * @param tenancyId
	 * @param storeId
	 * @param param
	 * @return 打印参数
	 * @throws Exception
	 */
	public void posBillPayment(Data param,Data result,JSONObject printJson)throws Exception;
	
	/** 查询账单付款状态
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void queryBillPayment(Data param,Data result,JSONObject printJson)throws Exception;
	
	/** 付款重试
	 * @param param
	 * @param result
	 * @param printJson
	 * @throws Exception
	 */
	public void billPaymentRepeat(Data param, Data result, JSONObject printJson)throws Exception;
	
	/** 付款失败
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void updatePaymentStateForFailure(Data param,Data result)throws Exception;
	
	/** 付款成功
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void updatePaymentStateForSuccess(Data param,Data result,JSONObject printJson)throws Exception;
	
	/**
	 * 点菜器结账
	 * 
	 * @param param
	 * @param result
	 * @throws SystemException
	 */
	public void posPaymentOrder(Data param, Data result, JSONObject json) throws SystemException;
	
	/**
	 * 结账
	 * 
	 * @param param
	 * @param result
	 * @param json
	 * @throws SystemException
	 */
	@Deprecated
	public void posPayment(Data param, Data result, JSONObject json) throws SystemException;

	/** 清除付款
	 * @param param
	 * @param result
	 * @throws SystemException
	 */
	public void clearPayment(Data param, Data result) throws SystemException;
}
