package com.tzx.orders.bo;

import java.util.Date;
import java.util.List;

import com.tzx.framework.common.entity.Data;

import net.sf.json.JSONObject;

/** 外卖估清上传
 * 
 * <AUTHOR> 2019-02-11
 *
 */
public interface OrdersSoldoutService
{
	String NAME="com.tzx.orders.bo.imp.OrdersSoldoutServiceImp";
	
	/** 查询外卖估清
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> findOrderSoldOut(Data param) throws Exception;

	/** 上传外卖估清
	 * @param tenancyId
	 * @param storeId
	 * @param reportDate
	 * @param optNum
	 * @param oldSoldList
	 * @param newSoldList
	 * @return
	 * @throws Exception
	 */
	public Data uploadOrderSoldOut(String tenancyId, Integer storeId, Date reportDate, String optNum, List<Integer> soldoutList, List<Integer> notSoldList) throws Exception;
}
