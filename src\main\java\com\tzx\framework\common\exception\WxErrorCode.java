package com.tzx.framework.common.exception;

public enum WxErrorCode implements ErrorCode
{

	
	TSET(7777),
	/**
	 * 请勿重复下单
	 */
	ORDER_EXISTS(9000),
	/**
	 * 下单失败
	 */
	ORDER_FALSE(9001),
	/**
	 * 购买票劵失败
	 */
	BUY_COUPON_FALSE(9002),
	/**
	 * 卡支付失败
	 */
	USE_CARD_FOR_PAY(9003),
	;

	private final int	number;

	private WxErrorCode(int number)
	{
		this.number = number;
	}

	public int getNumber()
	{
		return number;
	}

	@Override
	public String getMessage()
	{
		return "";
	}

}
