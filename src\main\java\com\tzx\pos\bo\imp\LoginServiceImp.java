package com.tzx.pos.bo.imp;

import java.io.File;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.tzx.base.common.util.PosPropertyUtil;
import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.HttpUtil;
import com.tzx.framework.common.util.PropertiesUtil;
import com.tzx.framework.common.util.Tools;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.print.DishQueueUtil;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.base.util.ReqDataUtil;
import com.tzx.pos.bo.LoginService;
import com.tzx.pos.bo.dto.PosLogin;
import com.tzx.pos.bo.dto.SysModule;
import com.tzx.pos.po.springjdbc.dao.LoginDao;

import common.Logger;
import net.sf.json.JSONObject;

/**
 * <AUTHOR>
 * @日期：2015年5月13日-下午1:34:26
 */
@Service(LoginServiceImp.NAME)
public class LoginServiceImp implements LoginService {
	private static final Logger logger = Logger.getLogger(LoginServiceImp.class);
	@Autowired
	private LoginDao loginDao;

	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public List<JSONObject> login(Data param) throws Exception {
		String tenantId = param.getTenancy_id();
		int storeId = param.getStore_id();
		Map<String, Object> map = ReqDataUtil.getDataMap(param);
		String posNum = ParamUtil.getStringValue(map, "pos_num", false, null);
		String optNum = ParamUtil.getStringValue(map, "opt_num", false, null);
		String id_Card = ParamUtil.getStringValue(map, "id_card", false, null);
		String password = ParamUtil.getStringValue(map, "password", false, null);
		String ipdz = ParamUtil.getStringValue(map, "ipdz", true, PosErrorCode.NOT_NULL_IPDZ);
		// GJ20160304
		if (StringUtils.isEmpty(optNum) && StringUtils.isEmpty(id_Card)) {
			throw new SystemException(PosErrorCode.NOT_EXISTS_POSNUMORIDCARD);
		} else if (StringUtils.isNotEmpty(optNum) && StringUtils.isEmpty(password)) {
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_PASSWORD);
		}

		Timestamp timeStamp = DateUtil.currentTimestamp();

		Integer role_id = 0;
		Integer shiftId = 0;
		String empId = null;// 人员ID
		String empName = null;// 人员姓名
		String pos_user_name = null;// 人员姓名
		String businessType = null;// 业态 快餐还是正餐
		Date reportDate = null;
		PosLogin login = new PosLogin();

		// 验证登录信息 获得tenancy_id，organ_id，name，item_menu_id
		StringBuilder check = new StringBuilder();
		check.append(
				" select emp.id as empid,emp.name as empname,himo.item_menu_id,ua.store_id,ua.roles_id,ora.tenancy_id,ora.format_state,ua.pos_user_name  ");
		check.append(
				" from employee as emp left join user_authority as ua on ua.employee_id=emp.id and ua.store_id=emp.store_id and ua.tenancy_id=emp.tenancy_id and ua.valid_state='1'");
		check.append(
				" left join organ as ora on ora.id=emp.store_id and ora.tenancy_id=emp.tenancy_id and ora.valid_state='1' ");
		check.append(
				" left join hq_item_menu_organ as himo on himo.store_id = emp.store_id and himo.tenancy_id=emp.tenancy_id");
		check.append(" where emp.store_id = " + storeId + " and emp.tenancy_id ='" + tenantId + "' ");
		if (StringUtils.isNotEmpty(optNum)) {
			//check.append(" and ua.password = '" + password + "' and ua.user_name = '" + optNum + "' ");
			check.append(" and ua.password = '" + password + "' and ua.pos_user_name = '" + optNum + "' ");
		} else if (StringUtils.isNotEmpty(id_Card)) {
			check.append(" and emp.id_card = '" + id_Card + "' ");
		}

		SqlRowSet rsEmp = loginDao.query4SqlRowSet(check.toString(), new Object[] {});
		if (rsEmp.next()) {
			empId = rsEmp.getString("empid").trim();
			empName = rsEmp.getString("empname").trim();
			pos_user_name = rsEmp.getString("pos_user_name").trim();
			if (StringUtils.isEmpty(empId) || StringUtils.isEmpty(empName)) {
				throw new SystemException(PosErrorCode.NOT_EXISTS_USER);
			}
			businessType = rsEmp.getString("format_state").trim();
			if (Tools.isNullOrEmpty(businessType)) {
				throw new SystemException(PosErrorCode.ORGAN_FORMAT_STATE_EXCEPTION);
			}
			/** 获取报表日期 */
			reportDate = loginDao.getReportDate(tenantId, storeId);
			login.setReport_date(DateUtil.format(reportDate));
			login.setEmployee_id(empId);
			login.setName(empName);
			login.setPos_num(pos_user_name);
			login.setBusiness_type(businessType);
			role_id = rsEmp.getInt("roles_id");
			login.setStore_id(storeId + "");
			// storeId = rs1.getInt("store_id");
			if (rsEmp.getString("item_menu_id") != null) {
				login.setItem_menu_id(rsEmp.getString("item_menu_id"));
			}
			if (rsEmp.getString("tenancy_id") != null) {
				login.setTenancy_id(rsEmp.getString("tenancy_id"));
			}
		} else {
			throw new SystemException(PosErrorCode.USER_LOGIN_ERROR);
		}

		String ip_device = new String(
				"select hd.devices_code from hq_devices hd where hd.valid_state = '1' and hd.devices_ip = ? and hd.store_id = ? and hd.tenancy_id = ? ");
		SqlRowSet iprs = loginDao.query4SqlRowSet(ip_device, new Object[] { ipdz, storeId, tenantId });
		if (iprs.next()) {
			posNum = iprs.getString("devices_code");
			login.setPos_num(posNum);
		} else {
			String sql = new String(
					"select hd.devices_code from hq_devices hd where hd.valid_state = '0' and hd.devices_ip = ? and hd.store_id = ? and hd.tenancy_id = ? ");
			SqlRowSet rs = loginDao.query4SqlRowSet(sql, new Object[] { ipdz, storeId, tenantId });
			if (rs.next()) {
				throw new SystemException(PosErrorCode.EQUIPMENT_HASBEEN_DISCONTINUED);
			}
			throw new SystemException(PosErrorCode.NOT_EXISTS_POSNUM_WAITRING_SYNCHRONIZED);
		}

		if (SysDictionary.SOURCE_MOVE.contains(param.getSource())||SysDictionary.SOURCE_ORDER_DEVICE.equals(param.getSource())) {
			String bindOptNum = loginDao.getBindOptNum(tenantId, storeId, reportDate, posNum);

			StringBuilder queryDevicesSql = new StringBuilder(
					"select pos_num,opt_num from pos_opt_state_devices where tenancy_id=? and store_id=? and devices_num=? and is_valid='1'");
			SqlRowSet rsd = loginDao.query4SqlRowSet(queryDevicesSql.toString(),
					new Object[] { tenantId, storeId, posNum });
			if (rsd.next()) {
				shiftId = loginDao.getShiftId(tenantId, storeId, reportDate, bindOptNum, rsd.getString("pos_num"));
			}
		} else {
			shiftId = loginDao.getShiftId(tenantId, storeId, reportDate, empId, posNum);
		}
		login.setShift(String.valueOf(shiftId));

		//同一个账号不允许在多个终端登录
//		StringBuilder sbCheck = new StringBuilder();
//		sbCheck.append(" select pos_num,opt_num from pos_opt_state where (content=? or content=?) ");
//		sbCheck.append(" and opt_num = ? and report_date=? and tag='0' and store_id = ? and tenancy_id = ? ");
//		sbCheck.append(" order by id desc limit 1  ");
//		SqlRowSet rsq = loginDao.query4SqlRowSet(sbCheck.toString(), new Object[]
//		{ SysDictionary.OPT_STATE_KSSY, SysDictionary.OPT_STATE_YYTC, empId, reportDate, storeId, tenantId });
//		if (rsq.next())
//		{
//			if (!rsq.getString("pos_num").equals(posNum))
//			{
//				throw SystemException.getInstance(PosErrorCode.OTHER_PSNUM_NOT_CHANGE_SHIFT).set("{0}", optNum).set("{1}", rsq.getString("pos_num"));
//			}
//		}
		
		//快餐,同一机台不允许登录多个账号
		if ("2".equals(businessType))
		{
			if (SysDictionary.SOURCE_MOVE.contains(param.getSource())||SysDictionary.SOURCE_ORDER_DEVICE.equals(param.getSource()))
			{
				loginDao.checkBindOptnum(tenantId, storeId, reportDate, posNum);
			}
			else
			{
//				SqlRowSet rsq2 = null;
				boolean boolCheck = false;
				// 根据机号判断是否有人未做交班
				StringBuilder sbCheck2 = new StringBuilder();
				sbCheck2.append(" select opt_num,report_date,pos_num from pos_opt_state where (content=? or content=? ) ");
				sbCheck2.append(" and tag='0' and pos_num = ? and report_date=? and store_id = ? and tenancy_id = ? ");
				sbCheck2.append(" order by id desc limit 1 ");
				SqlRowSet rsq2 = loginDao.query4SqlRowSet(sbCheck2.toString(), new Object[]
				{ SysDictionary.OPT_STATE_KSSY, SysDictionary.OPT_STATE_YYTC, posNum, reportDate, storeId, tenantId });
				if (rsq2.next())
				{
					boolCheck = empId.equalsIgnoreCase(rsq2.getString("opt_num"));
					if (!boolCheck)
					{
						throw new SystemException(PosErrorCode.OTHER_PEOPLE_NOT_CHANGE_SHIFT);
					}
				}
			}
		}
		/*
		 * // 获得tenancy_id，organ_id，name，item_menu_id StringBuilder sql = new
		 * StringBuilder(
		 * "select himo.item_menu_id,ua.employee_id,ua.store_id,ua.roles_id,ora.tenancy_id,ora.format_state,emp.name from user_authority AS ua "
		 * ); sql.append(
		 * " left JOIN organ as ora on ua.store_id = ora.id and ora.valid_state='1'"
		 * ); sql.append(
		 * " left JOIN employee as emp on ua.employee_id = emp.id ");
		 * sql.append(
		 * " left JOIN hq_item_menu_organ as himo on ua.store_id = himo.store_id "
		 * );
		 * 
		 * logger.info("查询用户是否存在：" + sql + " where ua.employee_id = '" + empId +
		 * "' and ua.password = '" + password + "'");
		 * 
		 * sql.append(
		 * " where ua.valid_state = '1' and ua.employee_id = ? and ua.password = ?"
		 * );
		 * 
		 * // Integer storeId = null; SqlRowSet rs1 =
		 * loginDao.query4SqlRowSet(sql.toString(), new Object[] {
		 * Integer.parseInt(empId), password });
		 * 
		 * while (rs1.next()) { role_id = rs1.getInt("roles_id"); if
		 * (rs1.getString("tenancy_id") != null) {
		 * login.setTenancy_id(rs1.getString("tenancy_id")); }
		 * login.setBusiness_type(rs1.getString("format_state"));
		 * login.setName(rs1.getString("name")); login.setStore_id(storeId +
		 * ""); login.setEmployee_id(rs1.getString("employee_id")); // storeId =
		 * rs1.getInt("store_id"); if (rs1.getString("item_menu_id") != null) {
		 * login.setItem_menu_id(rs1.getString("item_menu_id")); } }
		 */

		// 模块
		StringBuilder sql2 = new StringBuilder(
				"select (case when b.id is null then -1 else b.id end) authority,a.id as sys_module_id,a.module_name,a.states,a.module_link_url,a.father_module_id,a.module_level,a.module_type ");
		sql2.append(
				" from sys_modules a left join (select * from role_module_ref where role_id= ? ) b on a.id=b.sys_module_id where a.module_type='pos' and a.states = '1' ");

		List<SysModule> listMap = new ArrayList<SysModule>();
		SqlRowSet rs = loginDao.query4SqlRowSet(sql2.toString(), new Object[] { role_id });
		while (rs.next()) {
			SysModule sysModule = new SysModule();
			sysModule.setAuthority(rs.getString("authority"));
			sysModule.setId(rs.getString("sys_module_id"));
			sysModule.setModule_name(rs.getString("module_name"));
			sysModule.setStates(rs.getString("states"));
			sysModule.setModule_link_url(rs.getString("module_link_url"));
			// sysModule.setFather_module_id("" +
			// rs.getInt("father_module_id"));
			// sysModule.setModule_level("" + rs.getInt("module_level"));
			listMap.add(sysModule);
		}

		// 查询当天有没有员工登录
		/*
		 * StringBuilder fsql = new StringBuilder(
		 * "select id,tag from pos_opt_state where content = ? and opt_num = ? and pos_num=? and report_date = ? and store_id = ? order by id desc"
		 * ); SqlRowSet rs2 = loginDao.query4SqlRowSet(fsql.toString(), new
		 * Object[] { FYYDL, empId, posNum, reportDate, storeId }); String tag
		 * =""; if (rs2.next()) { tag = rs2.getString("tag"); } StringBuilder
		 * fcsql = new StringBuilder(); fcsql.append(
		 * " select max(login_number+1) from pos_opt_state "); fcsql.append(
		 * " where content = ? and opt_num = ? "); fcsql.append(
		 * " and pos_num=? and report_date = ? and store_id = ? "); Timestamp
		 * timeStamp = DateUtil.currentTimestamp(); int loginCount = 1; if
		 * (StringUtils.isNotEmpty(tag)) { loginCount =
		 * loginDao.queryForInt(fcsql.toString(), new Object[] { FYYDL, empId,
		 * posNum, reportDate, storeId }); if (loginCount == 0) { loginCount =
		 * 1; } }
		 */

		//
		StringBuilder fcsql = new StringBuilder();
		fcsql.append(
				" select COALESCE(max(login_number)+1,1) as login_number from pos_opt_state where content = ? and opt_num = ? ");
		fcsql.append(
				" and pos_num=? and report_date = ? and store_id = ? and tenancy_id=? and (tag <> ? or tag is null)");

		int loginCount = loginDao.queryForInt(fcsql.toString(),
				new Object[] { SysDictionary.OPT_STATE_YYDL, empId, posNum, reportDate, storeId, tenantId, '1' });
		// 机台状态库
		// StringBuilder instr = new StringBuilder("insert into pos_opt_state
		// (tenancy_id,store_id,pos_num,content,opt_num,opt_name,report_date,last_updatetime,tag,login_number)
		// values (?,?,?,?,?,?,?,?,?,?)");
		// loginDao.update(instr.toString(), new Object[]
		// { tenantId, storeId, posNum, SysDictionary.OPT_STATE_YYDL, empId,
		// empName, reportDate, timeStamp, "0", loginCount });
		loginDao.saveOptState(tenantId, storeId, reportDate, shiftId, posNum, empId, SysDictionary.OPT_STATE_YYDL, null,
				null, loginCount, timeStamp);

		login.setOpt_login_number(loginCount);
		login.setSys_module(listMap);

		// 把未打印的数据放到队列里
		// pushQueue(tenantId, storeId);

		loginDao.savePosLog(tenantId, storeId, posNum, empId, empName, shiftId, reportDate, Constant.TITLE, "登录", "",
				"");

		// 获得计算用的小数点位数
		String cmj = new String(
				"select para_code,para_value from sys_parameter where para_code ='CMJEWS' or para_code = 'ZDJEWS'");
		SqlRowSet srs = loginDao.query4SqlRowSet(cmj.toString());
		while (srs.next()) {
			Constant.constantMap.put(srs.getString("para_code"), srs.getString("para_value"));
		}

		// 新加登录返回TOKEN
		StringBuilder tokenSql = new StringBuilder(
				"select third_employee_id,token from  employee_third_link where store_id=? and employee_id=? and tenancy_id=?");
		List<JSONObject> listToken = loginDao.query4Json(login.getTenancy_id(), tokenSql.toString(),
				new Object[] { Integer.parseInt(login.getStore_id()), Integer.parseInt(login.getEmployee_id()),
						login.getTenancy_id() });
		if (listToken != null && listToken.size() > 0) {

			login.setToken(listToken.get(0).getString("token"));
			login.setThird_employee_id(listToken.get(0).getString("third_employee_id"));
		}

		// 登录返回organName
		StringBuilder organNameSql = new StringBuilder("select org_full_name from organ where id=? ");
		List<JSONObject> listOrgan = loginDao.query4Json(login.getStore_id(), organNameSql.toString(),
				new Object[] { Integer.parseInt(login.getStore_id()) });
		if (listOrgan != null && listOrgan.size() > 0) {
			login.setOrgan_name(listOrgan.get(0).getString("org_full_name"));
		}
		JSONObject reusetJson = JSONObject.fromObject(login);
		// 返回boh版本号
		String filePath = System.getProperty("catalina.home") + File.separator
				+ "webapps/ROOT/config/version.properties";
		reusetJson.put("boh_version", PropertiesUtil.readProperties(filePath).getProperty("app.version", ""));

		List<JSONObject> listReturnJson = new ArrayList<JSONObject>();
		listReturnJson.add(reusetJson);
		return listReturnJson;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED)
	public void updatePasword(Data param, Data result) throws Exception {
		String tenantId = param.getTenancy_id();
		int organId = param.getStore_id();

		Map<String, Object> map = ReqDataUtil.getDataMap(param);

		String optNum = ParamUtil.getStringValue(map, "opt_num", true, PosErrorCode.NOT_NULL_OPT_NUM);
		String nPassword = ParamUtil.getStringValue(map, "nPassword", true, PosErrorCode.NOT_NULL_PASSWORD);

		StringBuilder sql = new StringBuilder(
				"select count(id) from user_authority where valid_state =? and pos_user_name = ?");
		int count = loginDao.queryForInt(sql.toString(), new Object[] { '1', optNum });
		if (count == 0) {
			throw new SystemException(PosErrorCode.NOT_EXISTS_USER);
		}
		loginDao.updatePasword(nPassword, optNum, organId, param);

		String postUrl = "/hqRest/post";
		// Map<String, String> systemMap =
		// com.tzx.framework.common.constant.Constant.getSystemMap();

		JSONObject obj = new JSONObject();
		obj.put("pos_user_name", optNum);
		obj.put("password", nPassword);
		List<JSONObject> jsonList = new ArrayList<JSONObject>();
		jsonList.add(obj);

		Data data = Data.get();
		data.setType(Type.CHANGE_PASSWORD);
		data.setOper(Oper.update);
		data.setTenancy_id(tenantId);
		data.setStore_id(organId);
		data.setData(jsonList);

		logger.info("请求地址: " + PosPropertyUtil.getMsg("saas.url") + postUrl + "      请求参数: "
				+ JSONObject.fromObject(data).toString());
		String responseResult = HttpUtil.sendPostRequest(PosPropertyUtil.getMsg("saas.url") + postUrl,
				JSONObject.fromObject(data).toString());
		if (responseResult != null) {
			JSONObject json = JSONObject.fromObject(responseResult);
			boolean success = json.optBoolean("success");
			if (!success) {
				logger.info("总部修改密码失败:"+responseResult.toString());
				throw SystemException.getInstance(PosErrorCode.UPDATE_PASSWORD_ERROR);
			}
		}
	}

	@Override
	public void checkAuth(Data param, Data result) throws Exception {
		// int organId = param.getStore_id();
		String tenantId = param.getTenancy_id();

		Map<String, Object> map = ReqDataUtil.getDataMap(param);
		/*
		 * GJ20160304 String optNum = ParamUtil.getStringValue(map, "opt_num",
		 * true, PosErrorCode.NOT_NULL_OPT_NUM);
		 */

		String optNum = ParamUtil.getStringValue(map, "opt_num", false, null);
		String id_Card = ParamUtil.getStringValue(map, "id_card", false, null);

		String nPassword = ParamUtil.getStringValue(map, "password", false, null);

		String module_link_url = ParamUtil.getStringValue(map, "module_link_url", true,
				PosErrorCode.NOT_NULL_MODULE_URL);

		// GJ20160304
		if (StringUtils.isEmpty(optNum) && StringUtils.isEmpty(id_Card)) {
			throw new SystemException(PosErrorCode.NOT_EXISTS_POSNUMORIDCARD);
		}

		if (Tools.hv(optNum) && StringUtils.isEmpty(id_Card) && StringUtils.isEmpty(nPassword)) {
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_PASSWORD);
		}

		/*
		 * String hasUser = new String(
		 * "select count(id) from user_authority ua where ua.valid_state = '1' and ua.user_name = ? and ua.password = ? and ua.tenancy_id = ?"
		 * ); int count = loginDao.queryForInt(hasUser, new Object[] { optNum,
		 * nPassword, tenantId });
		 */

		// GJ20150304
		StringBuilder check = new StringBuilder();
		check.append(" select ua.employee_id as empid,emp.name as empname,ua.roles_id ");
		check.append(" from user_authority as ua left join employee emp ");
		check.append(" on emp.id=ua.employee_id and emp.store_id = ua.store_id and emp.tenancy_id = ua.tenancy_id ");
		check.append(" where ua.valid_state='1'  ");
		check.append(" and ua.tenancy_id = '" + tenantId + "' ");

		if (StringUtils.isNotEmpty(optNum)) {
			//check.append(" and ua.user_name = '" + optNum + "' ");
			check.append(" and ua.pos_user_name = '" + optNum + "' ");
			check.append(" and ua.password = '" + nPassword + "' ");
		} else if (StringUtils.isNotEmpty(id_Card)) {
			check.append(" and emp.id_card = '" + id_Card + "' ");
		}
		;
		SqlRowSet rs = loginDao.query4SqlRowSet(check.toString(), new Object[] {});
		if (!rs.next()) {
			throw new SystemException(PosErrorCode.USER_LOGIN_ERROR);
		} else {
			String employeeId = rs.getString("empid");
			// Integer storeId = null;
			Integer rolesId = rs.getInt("roles_id");
			if (Tools.isNullOrEmpty(employeeId)) {
				throw new SystemException(PosErrorCode.NOT_EXISTS_USER);
			}

			String queryAu = new String(
					"select count(a.id) from role_module_ref a left join sys_modules b on a.sys_module_id=b.id where a.role_id=? and b.module_link_url=? ");
			int qcount = loginDao.queryForInt(queryAu, new Object[] { rolesId, module_link_url });

			List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
			if (qcount > 0) {
				Map<String, Object> mmap = new HashMap<String, Object>();
				mmap.put("isauthority", true);
				mmap.put("employeeId", employeeId);
				list.add(mmap);
			} else {
				Map<String, Object> mmap = new HashMap<String, Object>();
				mmap.put("isauthority", false);
				mmap.put("employeeId", "");
				list.add(mmap);
			}
			result.setCode(Constant.CODE_SUCCESS);
			result.setMsg(Constant.CHECK_AUTHRITORY_SUCCESS);
			result.setData(list);
		}
	}

	/**
	 * （客户端重启，异常）或服务重启，把未打印的数据放到队列里
	 * 
	 * @param tenantId
	 * @param organId
	 * @throws Exception
	 */
	public void pushQueue(String tenantId, Integer organId) throws Exception {
		List<String> lists = new ArrayList<String>();
		// 查询打印表里的账单编号
		String qBilln = new String(
				"SELECT bill_num from pos_print_item where queue_tag is null and print_tag = '*' and store_id = ? and tenancy_id = ? group by bill_num");
		SqlRowSet rs = loginDao.query4SqlRowSet(qBilln, new Object[] { organId, tenantId });
		while (rs.next()) {
			lists.add(rs.getString("bill_num"));
		}
		// 根据账单编号查询未打印的数据
		StringBuilder qpt = new StringBuilder(
				"select rwid,bill_num,printer_id,printer_name,print_code,print_tag,print_format,print_property from pos_print_item ");
		qpt.append(" where queue_tag is null and print_tag = '*' and bill_num = ? and store_id = ? and tenancy_id = ?");
		// 单切更新
		String usql = new String(
				"update pos_print_item set queue_tag = ? where rwid = ? and print_property=? and bill_num = ? and store_id = ? ");
		// 大类或整单更新
		String ucsql = new String(
				"update pos_print_item set queue_tag = ? where print_format = ? and print_property = ? and bill_num = ? and store_id = ? ");

		for (int w = 0; w < lists.size(); w++) {
			String billno = lists.get(w);
			Object[] objs = new Object[] { billno, organId, tenantId };
			List<JSONObject> list = loginDao.queryString4Json(tenantId, qpt.toString(), objs);

			List<JSONObject> list1105 = new ArrayList<JSONObject>();
			Set<Integer> set = new HashSet<Integer>();

			boolean isWhole = false;

			for (int i = 0; i < list.size(); i++) {
				JSONObject obj = list.get(i);
				String fomat = obj.optString("print_format");

				if ("1105".equals(fomat)) {
					set.add(obj.optInt("printer_id"));
					list1105.add(obj);
				}

				if ("1107".equals(fomat)) {
					if (!isWhole) {
						JSONObject dishA = ParamUtil.setDishParam(tenantId, billno, obj.optString("print_code"),
								obj.optString("print_format"), obj.optInt("rwid"), obj.optInt("printer_id"), organId,
								obj.optString("print_property"));
						logger.info("整单::::" + dishA.toString());
						DishQueueUtil.push(dishA);
						loginDao.update(ucsql, new Object[] { null, dishA.getString("print_format"),
								dishA.getString("print_property"), billno, organId });
						isWhole = true;
					}
				}

				if ("1106".equals(fomat)) {
					JSONObject dishA = ParamUtil.setDishParam(tenantId, billno, obj.optString("print_code"),
							obj.optString("print_format"), obj.optInt("rwid"), obj.optInt("printer_id"), organId,
							obj.optString("print_property"));
					logger.info("单切::::" + dishA.toString());
					DishQueueUtil.push(dishA);
					loginDao.update(usql, new Object[] { null, dishA.getInt("rwid"), dishA.getString("print_property"),
							billno, organId });
				}
			}
			String ubcsql = new String(
					"update pos_print_item set queue_tag = ? where printer_id = ? and print_property = ? and bill_num = ? and store_id = ? ");
			for (Integer it : set) {
				boolean isBc = false;
				for (int i = 0; i < list1105.size(); i++) {
					JSONObject objB = list1105.get(i);
					if (it == objB.optInt("printer_id")) {
						if (!isBc) {
							JSONObject dishB = ParamUtil.setDishParam(tenantId, billno, objB.optString("print_code"),
									objB.optString("print_format"), objB.optInt("rwid"), objB.optInt("printer_id"),
									organId, objB.optString("print_property"));
							logger.info("大类::::" + dishB.toString());
							DishQueueUtil.push(dishB);
							loginDao.update(ubcsql, new Object[] { null, dishB.getInt("printer_id"),
									dishB.getString("print_property"), billno, organId });
							isBc = true;
						}
					}
				}
			}
		}
	}

	@Override
	public void getOrganId(Data param, Data result) throws Exception {
		String tenantId = param.getTenancy_id();

		Map<String, Object> map = ReqDataUtil.getDataMap(param);

		String organ_code = ParamUtil.getStringValue(map, "organ_code", false, null);
		int organId = 0;
		String orgFullName = "";
		String phone = "";
		String address = "";
		String sorganId = new String(
				"select id,org_full_name,phone,address from organ where valid_state='1' and organ_code = ? and tenancy_id = ?");
		SqlRowSet rs = loginDao.query4SqlRowSet(sorganId, new Object[] { organ_code, tenantId });
		if (rs.next()) {
			organId = rs.getInt("id");
			orgFullName = rs.getString("org_full_name");
			phone = rs.getString("phone");
			address = rs.getString("address");
		} else {
			throw new SystemException(PosErrorCode.NOT_EXISTS_ORGAN);
		}

		List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
		Map<String, Object> map0 = new HashMap<String, Object>();
		map0.put("organ_id", organId);
		map0.put("org_full_name", orgFullName);
		map0.put("phone", phone);
		map0.put("address", address);
		list.add(map0);
		result.setCode(Constant.CODE_SUCCESS);
		result.setMsg(Constant.GET_ORGAN_ID_SUCCESS);
		result.setData(list);
	}

	@Override
	public List<SysModule> getSysModule(Data param) throws Exception {
		String tenantId = param.getTenancy_id();
		int storeId = param.getStore_id();

		Map<String, Object> map = ReqDataUtil.getDataMap(param);

		String optNum = ParamUtil.getStringValue(map, "opt_num", true, PosErrorCode.NOT_NULL_OPT_NUM);

		if (!StringUtils.isNumeric(optNum)) {
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_OPT_NUM);
		}

		Integer role_id = null;
		StringBuilder sql = new StringBuilder(
				"select ua.roles_id from user_authority ua where ua.valid_state = '1' and ua.employee_id = ? and ua.tenancy_id = ? and ua.store_id = ? and ua.valid_state = '1' ");

		SqlRowSet rs1 = loginDao.query4SqlRowSet(sql.toString(),
				new Object[] { Integer.parseInt(optNum), tenantId, storeId });

		while (rs1.next()) {
			role_id = rs1.getInt("roles_id");
		}

		// 模块
		StringBuilder sql2 = new StringBuilder(
				"select (case when b.id is null then -1 else b.id end) authority,a.id as sys_module_id,a.module_name,a.states,a.module_link_url,a.father_module_id,a.module_level,a.module_type ");
		sql2.append(
				" from sys_modules a left join (select * from role_module_ref where role_id= ? ) b on a.id=b.sys_module_id where a.module_type='pos' ");

		List<SysModule> listMap = new ArrayList<SysModule>();
		SqlRowSet rs = loginDao.query4SqlRowSet(sql2.toString(), new Object[] { role_id });
		while (rs.next()) {
			SysModule sysModule = new SysModule();
			sysModule.setAuthority(rs.getString("authority"));
			sysModule.setId(rs.getString("sys_module_id"));
			sysModule.setModule_name(rs.getString("module_name"));
			sysModule.setStates(rs.getString("states"));
			sysModule.setModule_link_url(rs.getString("module_link_url"));
			listMap.add(sysModule);
		}

		return listMap;
	}
}
