package com.tzx.base.datasource.dao;

import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.pool.DruidDataSourceFactory;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;

import org.apache.log4j.Logger;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;

import javax.sql.DataSource;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Map;
import java.util.Properties;

/**
 * <AUTHOR>
 * @describe 实现动态数据源切换逻辑
 */
public class DynamicDataSource extends AbstractRoutingDataSource
{
	private Logger log= Logger.getLogger(this.getClass());
	private Map<Object, Object>	_targetDataSources;

	/**
	 * @see AbstractRoutingDataSource#determineCurrentLookupKey()
	 * @describe 数据源为空时，自动切换至默认数据源，即在配置文件中定义的dataSource数据源
	 */
	@Override
	protected Object determineCurrentLookupKey()
	{
		String tenancyid = DBContextHolder.getTenancyid();
		if (tenancyid == null)
		{
			tenancyid = "0";
		}
		
		this.selectDataSource(tenancyid);
		
		return tenancyid;
	}

	public void setTargetDataSources(Map<Object, Object> targetDataSources)
	{
		this._targetDataSources = targetDataSources;
		super.setTargetDataSources(this._targetDataSources);
		afterPropertiesSet();
	}

	public void addTargetDataSource(String key, DruidDataSource dataSource)
	{
		this._targetDataSources.put(key, dataSource);
		this.setTargetDataSources(this._targetDataSources);
	}

	/**
	 * @param tenancyid
	 * @describe 数据源存在时不做处理，不存在时创建新的数据源链接，并将新数据链接添加至缓存
	 */
	public void selectDataSource(String tenancyid)
	{
		if("dataSource".equals(tenancyid))
		{
			return;
		}
		
		Object obj = this._targetDataSources.get(tenancyid);
		if (obj != null)
		{
			return;
		}
		else
		{
			DruidDataSource dataSource = getDynamicDataSource(tenancyid);
			if (null != dataSource)
			{
				this.setDataSource(tenancyid, dataSource);
			}
		}
	}

	/**
	 * @describe 查询serverId对应的数据源记录
	 * @param tenancyid
	 * @return
	 */
	public DruidDataSource getDynamicDataSource(String tenancyid) {
		DataSource dataSource = (DataSource) SpringConext.getApplicationContext().getBean("dataSource");

		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		Properties multiProperties = null;
		try {
			conn = dataSource.getConnection();

			ps = conn.prepareStatement("select d.* from multi_datasource d, multi_tenancy t where t.l10n=? and d.objuid=t.multi_datasource_uid");
			ps.setString(1, tenancyid);
//			if(DBContextHolder.getTTenancyid().equals("null")) {
//				
//			} else {
//				ps.setString(1, DBContextHolder.getTTenancyid());
//			}

			rs = ps.executeQuery();
			multiProperties = new Properties();
			if (rs.next()) {
				multiProperties.put("url", rs.getString("driver_url"));
				multiProperties.put("username", rs.getString("username"));
				multiProperties.put("password", rs.getString("password") == null ? "" : rs.getString("password"));
				multiProperties.put("validationQuery", "select 1");
				multiProperties.put("testOnBorrow", "false");
				multiProperties.put("testWhileIdle", "true");
				multiProperties.put("testOnReturn", "false");
				multiProperties.put("initialSize", "2");
				multiProperties.put("minIdle", "2");
				multiProperties.put("maxActive", "16");
				//超时等待时间以毫秒为单位
				multiProperties.put("maxWait", "3000");
				//在空闲连接回收器线程运行期间休眠的时间值,以毫秒为单位
				multiProperties.put("timeBetweenEvictionRunsMillis", "60000");
				//连接在池中保持空闲而不被空闲连接回收器线程时间
				multiProperties.put("minEvictableIdleTimeMillis", "3600000");

				// 启用监控
				/*multiProperties.put("filters", "stat");
				multiProperties.put("useGlobalDataSourceStat", "true");*/
			}

			if (null != multiProperties) {
				DruidDataSource druidDataSource = (DruidDataSource) DruidDataSourceFactory.createDataSource(multiProperties);
				druidDataSource.setConnectionErrorRetryAttempts(2);
				druidDataSource.setBreakAfterAcquireFailure(true);

				return druidDataSource;
			}
		} catch (Exception e) {
			log.error(e);
		} finally {
			try {
				if(rs != null) {
					rs.close();
				}
			}
			catch (SQLException e) {}

			try {
				if(ps != null) {
					ps.close();
				}
			} catch (SQLException e) {}

			try {
				if(conn != null) {
					conn.close();
				}
			} catch (SQLException e) {}
		}

		return null;
	}

	/**
	 * @describe 查询serverId对应的数据源记录
	 * @param tenancyid
	 * @return
	 */
	/*public BasicDataSource getDataSource(String tenancyid)
	{
		DataSource dataSource = (DataSource) SpringConext.getApplicationContext().getBean("dataSource");

		Connection conn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		Properties multiProperties = null;
		try
		{
			conn = dataSource.getConnection();

			ps = conn.prepareStatement("select d.* from multi_datasource d, multi_tenancy t where t.l10n=? and d.objuid=t.multi_datasource_uid");
			if(DBContextHolder.getTTenancyid().equals("null"))
			{
				ps.setString(1, tenancyid);
			}
			else
			{
				ps.setString(1, DBContextHolder.getTTenancyid());
			}

			rs = ps.executeQuery();
			multiProperties = new Properties();
			if (rs.next())
			{
				multiProperties.put("driverClassName", rs.getString("driver_class"));
				multiProperties.put("url", rs.getString("driver_url"));
				multiProperties.put("username", rs.getString("username"));
				multiProperties.put("password", rs.getString("password") == null ? "" : rs.getString("password"));
				multiProperties.put("validationQuery", "select 1");
				multiProperties.put("testOnBorrow", true);
				multiProperties.put("testWhileIdle", true);
				multiProperties.put("initialSize", 10);
				multiProperties.put("maxIdle", 20);
				multiProperties.put("maxActive", 300);
				//连接被泄露时是否打印
				multiProperties.put("logAbandoned",true);
		        //是否自动回收超时连接
				multiProperties.put("removeAbandoned",true);
		        //超时时间(以秒数为单位)
		        multiProperties.put("removeAbandonedTimeout",30);
		        //超时等待时间以毫秒为单位 1000等于60秒
		        multiProperties.put("maxWait",5000);
		        //在空闲连接回收器线程运行期间休眠的时间值,以毫秒为单位
		        multiProperties.put("timeBetweenEvictionRunsMillis",10000);
		        //在每次空闲连接回收器线程(如果有)运行时检查的连接数量
		        //multiProperties.put("numTestsPerEvictionRun" ,10);
		        //连接在池中保持空闲而不被空闲连接回收器线程时间
		        //multiProperties.put("minEvictableIdleTimeMillis",10000);
			}


			if (null != multiProperties)
			{
				return (BasicDataSource) BasicDataSourceFactory.createDataSource(multiProperties);
			}
		}
		catch (Exception e)
		{
			log.error(e);
		}
		finally
		{
			try
			{
				if(rs != null)
				{
					rs.close();
				}
			}
			catch (SQLException e)
			{
			}

			try
			{
				if(ps != null)
				{
					ps.close();
				}
			}
			catch (SQLException e)
			{
			}

			try
			{
				if(conn != null)
				{
					conn.close();
				}
			}
			catch (SQLException e)
			{
			}
		}

		return null;
	}*/

	/**
	 * @param tenancyid
	 * @param dataSource
	 */
	public void setDataSource(String tenancyid, DruidDataSource dataSource)
	{
		this.addTargetDataSource(tenancyid, dataSource);
		DBContextHolder.setTenancyid(tenancyid);
	}

}
