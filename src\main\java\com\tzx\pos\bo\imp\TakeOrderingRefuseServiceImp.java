package com.tzx.pos.bo.imp;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import net.sf.json.JSONObject;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import com.alipay.api.internal.util.StringUtils;
import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.util.HttpUtil;
import com.tzx.pos.base.util.JsonUtil;
import com.tzx.pos.base.util.SignUtils;
import com.tzx.pos.bo.TakeOrderingRefuseService;

@Service(TakeOrderingRefuseService.NAME)
public class TakeOrderingRefuseServiceImp implements TakeOrderingRefuseService{
	private static final Logger		logger				= Logger.getLogger(TakeOrderingRefuseServiceImp.class);
	private final String			TAKE_POST_URL		= "/OJ910104.do";
	
	private String getPequestUrl(){
		System.out.println(Constant.getSystemMap().get("take.url"));
		return Constant.getSystemMap().get("take.url");
	}
	
	/**
	 * 拒单接口
	 * @param billCode
	 * @param reason
	 * @param channel
	 * @return
	 */
	public Data refuseOrderingEntry(final String billCode,final String  reason,final String channel){
		/*final Data data =refuseOrderingPost(billCode, reason, channel);
		if(data.getCode() !=0 && data.getSource() !="true"){
			final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
			final Runnable beeper = new Runnable() {
				public void run() {
					 logger.warn(Thread.currentThread().getId() + "@-----------拒单接口重试--------");
					 Data dd =refuseOrderingPost(billCode, reason, channel);
					 if(dd.getCode() == 0){
						 scheduler.shutdown();
					 }
				}
			};
			final ScheduledFuture <?> beeperHandle = scheduler.scheduleAtFixedRate(beeper, 5, 20, TimeUnit.SECONDS);
			Runnable rund= new Runnable() {
				public void run() {
					beeperHandle.cancel(true);
					scheduler.shutdown();
				}
			};
			
		    scheduler.schedule(rund, 60, TimeUnit.SECONDS);
		}
		return data;*/
		Data data =refuseOrderingPost(billCode, reason, channel);
		return data;
	}
	
	/**
	 * 拒单
	 */
	public Data refuseOrderingPost(String billCode,String  reason,String channel){
		Data resData = new Data();
		String reqURLs = getPequestUrl()+"/org/syntony/OJ910104.do";
		logger.info("--->>开始调用拒单接口---billCode="+billCode+"---reason="+reason+"channel+"+channel);
		try{
			long t = System.currentTimeMillis();
			JSONObject signJson = SignUtils.request("OJ910104");
			JSONObject para = new JSONObject();
			para.put("sign",signJson.optString("sign"));
			para.put("timeStamp",signJson.optString("time_stamp"));//获取当前系统日期
		    Map<String,Object> dataMap = new HashMap<String,Object>();
		    dataMap.put("orderSn", billCode);
		    dataMap.put("reason", reason);
		    dataMap.put("channel", channel);
		    List<Map<String,Object>> jsonList = new ArrayList<Map<String,Object>>();
		    jsonList.add(dataMap);
		    para.put("data", jsonList);
			String result = HttpUtil.sendPostRequest(reqURLs, para.toString());
			logger.info(String.valueOf(t)+"<发送接口返回体==>" + result);
			if (StringUtils.isEmpty(result)){
				resData.setCode(99);
				resData.setMsg("连接超时，请检查网络");
				resData.setSuccess(false);
				logger.info("连接超时，请检查网络,请求体为：" + para);
			}else{
				try{
					Data data = JsonUtil.JsonToD5Data(JSONObject.fromObject(result));
					if (1 == data.getCode()){
						resData.setCode(0);
						resData.setMsg("网络连接失败");
						logger.info("连接异常,请求体为：" + para);
					}else{
						resData.setCode(data.getCode());
						resData.setMsg(data.getMsg());
					}
					resData.setData(data.getData());
					resData.setSuccess(data.isSuccess());
				}
				catch (Exception se){
					logger.info("拒单接口异常：" + ExceptionMessage.getExceptionMessage(se));
					se.printStackTrace();
				}
			}
			logger.info(String.valueOf(t)+"<调用接口返回体==>code: " + resData.getCode());
			logger.info(String.valueOf(t)+"<调用接口返回体==>msg: " + resData.getMsg());
			logger.info(String.valueOf(t)+"<调用接口返回体==>" + result);
		}
		catch (Exception se){
			logger.info("连接超时，请检查网络,请求体为：");
			resData.setCode(5);
			resData.setMsg("连接超时，请检查网络");
			resData.setSuccess(false);
		}
        return resData;
	}
	
	/**
	  * 整单取消调用
	  * @param billCode
	  * @param applyType
	  * @param channel
	  * @return
	  */
	public void takeCancbill(String billCode, String applyType, String channel,Data resData) {
		logger.info("--->>订单转账单开始-------------调整单取消---billCode="+billCode+"---applyType="+applyType+"channel+"+channel);
		
		String reqURLs = getPequestUrl()+"/org/syntony/RE100102.do";
		try{
			long t = System.currentTimeMillis();
			JSONObject signJson = SignUtils.request("RE100102");
			JSONObject para = new JSONObject();
			para.put("sign",signJson.optString("sign"));
			para.put("timeStamp",signJson.optString("time_stamp"));//获取当前系统日期
		    Map<String,Object> dataMap = new HashMap<String,Object>();
		    dataMap.put("orderSn", billCode);
		    dataMap.put("applyType", applyType);
		    dataMap.put("channel", channel);
		    List<Map<String,Object>> jsonList = new ArrayList<Map<String,Object>>();
		    jsonList.add(dataMap);
		    para.put("data", jsonList);
			String result = HttpUtil.sendPostRequest(reqURLs, para.toString());
			logger.info(String.valueOf(t)+"<发送接口返回体==>" + result);
			if (StringUtils.isEmpty(result)){
				resData.setCode(99);
				resData.setMsg("连接超时，请检查网络");
				resData.setSuccess(false);
				logger.info("连接超时，请检查网络,请求体为：" + para);
			}
			else{
				try{
					Data data = JsonUtil.JsonToD5Data(JSONObject.fromObject(result));
					if (1 == data.getCode()){
						resData.setCode(99);
						resData.setMsg("网络连接失败");
						logger.info("连接异常,请求体为：" + para);
					}else{
						resData.setCode(data.getCode());
						resData.setMsg(data.getMsg());
					}
					
					resData.setData(data.getData());
					resData.setSuccess(data.isSuccess());
				}
				catch (Exception se){
					logger.info("D5整单取消异常--------：" + ExceptionMessage.getExceptionMessage(se));
					se.printStackTrace();
				}
			}
			
			logger.info(String.valueOf(t)+"<调用接口返回体==>code: " + resData.getCode());
			logger.info(String.valueOf(t)+"<调用接口返回体==>msg: " + resData.getMsg());
			logger.info(String.valueOf(t)+"<调用接口返回体==>" + result);
		}
		catch (Exception se){
			logger.info("连接超时，请检查网络,请求体为：");
			resData.setCode(5);
			resData.setMsg("连接超时，请检查网络");
			resData.setSuccess(false);
		}
      
	}

}
