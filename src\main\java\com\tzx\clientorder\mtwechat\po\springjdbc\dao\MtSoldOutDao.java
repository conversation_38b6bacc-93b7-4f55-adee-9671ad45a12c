package com.tzx.clientorder.mtwechat.po.springjdbc.dao;

import net.sf.json.JSONObject;

import java.util.Date;
import java.util.List;

import com.tzx.pos.base.dao.BaseDao;

/**
 * Created by qing<PERSON> on 2018-06-01.
 */
public interface MtSoldOutDao extends BaseDao {
    String NAME = "com.tzx.clientorder.mtwechat.po.springjdbc.dao.imp.SoldOutDaoImp";

    /**
     * 查询菜品规格
     * @param tenancyId
     * @param storeId
     * @return
     * @throws Exception
     */
    List<JSONObject> getItemUnits(String tenancyId, int storeId, Date reportDate) throws Exception;

    /**
     * 查询菜品沽清数量为0的
     * @param tenancyId
     * @param storeId
     * @param itemIds
     * @return
     * @throws Exception
     */
    List<JSONObject> getItems(String tenancyId, int storeId, String itemIds, Date reportDate) throws Exception;

    /**
     * 查询菜取消沽清的菜品对应的规格
     * @param tenancyId
     * @param itemIds
     * @return
     * @throws Exception
     */
    List<JSONObject> getUnits(String tenancyId, String itemIds) throws Exception;
}
