package com.tzx.pos.po.springjdbc.dao.imp;

import com.tzx.base.common.util.StringUtil;
import com.tzx.base.entity.PosBillPayment;
import com.tzx.base.entity.PosBillPaymentCoupons;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.Tools;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.dao.imp.BaseDaoImp;
import com.tzx.pos.bo.dto.PosBillPaymentWay;
import com.tzx.pos.bo.dto.PosThirdPaymentOrderEntity;
import com.tzx.pos.po.springjdbc.dao.PosPaymentDao;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Repository(PosPaymentDao.NAME)
public class PosPaymentDaoImp extends BaseDaoImp implements PosPaymentDao
{
	@Override
	public String getSaleMode(String tenancyId, Integer storeId, String billNum) throws Exception {
		StringBuilder sql = new StringBuilder("select sale_mode from pos_bill where tenancy_id=? and store_id=? and bill_num=?");
		SqlRowSet rs = this.query4SqlRowSet(sql.toString() ,new Object[]{tenancyId,storeId,billNum});
		if(rs.next()){
			return rs.getString("sale_mode");
		}
		return null;
	}
	
	@Override
	public List<JSONObject> findBill(String tenancyId ,int storeId ,String billNum) throws Exception
	{
		StringBuilder sql = new StringBuilder("select bill_num,bill_amount,bill_state,payment_amount,table_code,source,order_num,bill_property,payment_state from pos_bill where bill_num = '" + billNum + "' and store_id = " + storeId);
		return this.queryString4Json(tenancyId, sql.toString());
	}
	
	@Override
	public void updatePosBillForSalemodeByBillnum(String tenancyId, int storeId, String billNum, String saleMode, String tableCode, Integer shiftId) throws Exception
	{
		StringBuilder updateBillPaySql = new StringBuilder("update pos_bill set sale_mode =?,table_code=?,shift_id=? where tenancy_id= ? and store_id=? and bill_num=?");
		this.update(updateBillPaySql.toString(), new Object[]
		{ saleMode, tableCode, shiftId, tenancyId, storeId, billNum });
	}
	
	@Override
	public void updatePosBillForStateByBillnum(String tenancyId, int storeId, String billNum, String paymentState) throws Exception
	{
		StringBuilder updateBillPaySql = new StringBuilder("update pos_bill set payment_state=? where tenancy_id= ? and store_id=? and bill_num=? and bill_property<>?");
		this.update(updateBillPaySql.toString(), new Object[]
		{ paymentState, tenancyId, storeId, billNum, SysDictionary.BILL_PROPERTY_CLOSED });
	}

	@Override
	public int updatePosBillForStateByBillnum(String tenancyId, int storeId, String billNum, String paymentState, Double difference) throws Exception
	{
		StringBuilder updateBillPaySql = new StringBuilder("update pos_bill set payment_state=?,difference=? where tenancy_id= ? and store_id=? and bill_num=? and bill_property<>?");
		return this.update(updateBillPaySql.toString(), new Object[]
		{ paymentState, difference, tenancyId, storeId, billNum, SysDictionary.BILL_PROPERTY_CLOSED });
	}
	
	@Override
	public void updatePosBillForStateByBillnum(String tenancyId, int storeId, String billNum, String paymentState, Double difference, Double paymentAmonut) throws Exception
	{
		StringBuilder updateBillPaySql = new StringBuilder("update pos_bill set payment_state=?,difference=?,payment_amount=? where tenancy_id= ? and store_id=? and bill_num=? and bill_property<>?");
		this.update(updateBillPaySql.toString(), new Object[]
		{ paymentState, difference, paymentAmonut, tenancyId, storeId, billNum, SysDictionary.BILL_PROPERTY_CLOSED });
	}

	@Override
	public void updatePosBillForStateByBillnum(String tenancyId, int storeId, String billNum, String paymentState, Double difference, String saleMode, String tableCode, Integer shiftId) throws Exception
	{
		StringBuilder updateBillPaySql = new StringBuilder(
				"update pos_bill set payment_state=?,difference=?,sale_mode=?,shift_id=?,table_code=coalesce((select pt.stable from pos_tablestate pt where pt.table_code=? and pt.store_id=? limit 1),?) where tenancy_id= ? and store_id=? and bill_num=? and bill_property<>?");
		this.update(updateBillPaySql.toString(), new Object[]
		{ paymentState, difference, saleMode, shiftId, tableCode, storeId, tableCode, tenancyId, storeId, billNum, SysDictionary.BILL_PROPERTY_CLOSED });
	}
	
	@Override
	public void updatePosBillForStateByBillnum(String tenancyId, int storeId, String billNum, String paymentState, Double difference, String saleMode, String tableCode, Integer shiftId, String transferRemark) throws Exception
	{
		StringBuilder updateBillPaySql = new StringBuilder(
				"update pos_bill set payment_state=?,difference=?,sale_mode=?,shift_id=?,table_code=coalesce((select pt.stable from pos_tablestate pt where pt.table_code=? and pt.store_id=? limit 1),?),transfer_remark=? where tenancy_id= ? and store_id=? and bill_num=? and bill_property<>?");
		this.update(updateBillPaySql.toString(), new Object[]
		{ paymentState, difference, saleMode, shiftId, tableCode, storeId, tableCode, transferRemark, tenancyId, storeId, billNum, SysDictionary.BILL_PROPERTY_CLOSED });
	}

	@Override
	public void updatePosBillForStateByBillnum(String tenancyId, int storeId, String billNum, String optNum, String posNum, String paymentState, Double difference, Timestamp paymentTime, String remark, String payNo) throws Exception
	{
		StringBuilder updateBillPaySql = new StringBuilder("update pos_bill set payment_state=?,difference=?,payment_time=?,cashier_num=?,pos_num=?,transfer_remark=?,pay_no=? where tenancy_id= ? and store_id=? and bill_num=? and bill_property<>?");
		this.update(updateBillPaySql.toString(), new Object[]
		{ paymentState, difference, paymentTime, optNum, posNum, remark, payNo, tenancyId, storeId, billNum, SysDictionary.BILL_PROPERTY_CLOSED });
	}
	
	@Override
	public void updatePosBillItemForSalemodeByBillnum(String tenancyId, int storeId, String billNum, String saleMode,Integer shiftId) throws Exception
	{
		String updateSaleMode = new String("update pos_bill_item set sale_mode = (case when sale_mode is null or sale_mode='' then ? else sale_mode end ),item_shift_id=? where bill_num = ? and store_id =? and tenancy_id=?");
		this.update(updateSaleMode, new Object[]
		{ saleMode,shiftId, billNum, storeId, tenancyId });
	}

	@Override
	public List<JSONObject> getBillPaymentUnfinishByBillnum(String tenancyId, int storeId, String billNum) throws Exception
	{
		return this.getBillPaymentByPaymentState(tenancyId, storeId, billNum, SysDictionary.PAYMENT_STATE_PAY );
	}

	@Override
	public List<JSONObject> getBillPaymentByPaymentState(String tenancyId, int storeId, String billNum,String paymentState) throws Exception
	{
		StringBuilder selectSql = new StringBuilder("select bp.*,pw.payment_class,pw.is_check from pos_bill_payment bp left join payment_way pw on bp.jzid=pw.id where bp.bill_num =? and bp.store_id = ? and bp.tenancy_id=? and bp.payment_state=? order by bp.last_updatetime");
		List<JSONObject> payList = this.query4Json(tenancyId, selectSql.toString(), new Object[]
		{ billNum, storeId, tenancyId, paymentState });
		return payList;
	}
	
	@Override
	public List<JSONObject> getMeiDaBillPaymentByBillnum(String tenancyId, int storeId, String billNum) throws Exception
	{
		StringBuilder selectSql = new StringBuilder("select bp.*,pw.payment_class,pw.is_check from pos_bill_payment bp left join payment_way pw on bp.jzid=pw.id where bp.bill_num =? and bp.store_id = ? and bp.tenancy_id=? and (bp.payment_state = '"+
				SysDictionary.PAYMENT_STATE_PAY+"' or (bp.type = '"+SysDictionary.PAYMENT_CLASS_MEIDA_CARD_PAY+"' and bp.payment_state = '"+SysDictionary.PAYMENT_STATE_PAY_COMPLETE+"') ) order by bp.last_updatetime");
		List<JSONObject> payList = this.query4Json(tenancyId, selectSql.toString(), new Object[]
				{ billNum, storeId, tenancyId });
		return payList;
	}
	
	@Override
	public List<JSONObject> getBillPaymentByBillnum(String tenancyId, int storeId, String billNum) throws Exception
	{
		String reSql = new String(
				"select id,jzid,type as payment_class,name,name_english,amount,count,coalesce(number,'') as number,coalesce(phone,'') as phone,report_date,shift_id,pos_num,cashier_num,last_updatetime,coalesce(customer_id,0) as customer_id,coalesce(bill_code,'') as bill_code,coalesce(remark,'') as remark,currency_amount,payment_state,more_coupon,out_trade_no from pos_bill_payment where tenancy_id=? and store_id = ? and bill_num =? order by id ");
		List<JSONObject> paymentList = this.query4Json(tenancyId, reSql, new Object[]
		{ tenancyId, storeId, billNum });
		return paymentList;
	}

	@Override
	public JSONObject getBillPaymentByJzid(String tenancyId, int storeId, String billNum, int jzid) throws Exception
	{
		String sql = new String("select * from pos_bill_payment where tenancy_id=? and store_id = ? and bill_num = ? and jzid = ?");
		List<JSONObject> paymentList = this.query4Json(tenancyId, sql, new Object[]
		{ tenancyId, storeId, billNum, jzid });

		JSONObject resultJson = new JSONObject();
		if (null != paymentList && paymentList.size() > 0)
		{
			resultJson = paymentList.get(0);
		}
		return resultJson;
	}
	
	@Override
	public JSONObject getBillPaymentByPaymentClass(String tenancyId, int storeId, String billNum, String  paymentClass) throws Exception
	{
		String sql = new String("select p.* from pos_bill_payment p left join payment_way pw on p.jzid=pw.id and p.tenancy_id=pw.tenancy_id left join payment_way_of_ogran po on pw.id=po.payment_id and p.store_id=po.organ_id and p.tenancy_id=po.tenancy_id where p.tenancy_id=? and p.store_id=? and  p.bill_num=? and pw.payment_class=?");
		List<JSONObject> paymentList = this.query4Json(tenancyId, sql, new Object[]
		{ tenancyId, storeId, billNum, paymentClass });

		JSONObject resultJson = new JSONObject();
		if (null != paymentList && paymentList.size() > 0)
		{
			resultJson = paymentList.get(0);
		}
		return resultJson;
	}
	
	@Override
	public List<JSONObject> getDebitCardPaymentByBillnum(String tenancyId, int storeId, String billNum) throws Exception {
		StringBuilder selectSql = new StringBuilder("select bp.*,pw.payment_class,pw.is_check from pos_bill_payment bp left join payment_way pw on bp.jzid=pw.id where bp.bill_num =? and bp.store_id = ? and bp.tenancy_id=? and (bp.payment_state = '"+
				SysDictionary.PAYMENT_STATE_PAY+"' or (bp.type = '"+SysDictionary.PAYMENT_CLASS_DEBIT_PAY+"' and bp.payment_state = '"+SysDictionary.PAYMENT_STATE_PAY_COMPLETE+"') ) order by bp.last_updatetime");
		List<JSONObject> payList = this.query4Json(tenancyId, selectSql.toString(), new Object[]
				{ billNum, storeId, tenancyId });
		return payList;
	}
	
	@Override
	public List<JSONObject> getPosBillPaymentByBillnum(String tenancyId, int storeId, String billNum) throws Exception
	{
		StringBuilder selectSql = new StringBuilder("select bp.*,coalesce(pw.payment_class,bp.type) as payment_class,pw.if_jifen from pos_bill_payment bp left join payment_way pw on bp.jzid=pw.id where bp.bill_num =? and bp.store_id = ? and bp.tenancy_id=?");

		return this.query4Json(tenancyId, selectSql.toString(), new Object[]
		{ billNum, storeId, tenancyId });
	}
	
	@Override
	public List<JSONObject> getBillPaymentForCoupon(String tenantId, int storeId, String billNum, String batchNum) throws Exception
	{
		StringBuilder sql = new StringBuilder("select bp.number,pc.discount_num,pc.rwid,pc.item_unit_id from pos_bill_payment bp left join pos_bill_payment_coupons pc on bp.tenancy_id=pc.tenancy_id and bp.store_id=pc.store_id and bp.bill_num=pc.bill_num and bp.payment_uid=pc.payment_id");
		sql.append(" where bp.tenancy_id=? and bp.store_id=? and bp.bill_num=? and bp.type=? ");
		// sql.append(" and number is not null and number<>''");
		if (Tools.hv(batchNum))
		{
			sql.append(" and bp.batch_num='" + batchNum + "' ");
		}
		return this.query4Json(tenantId, sql.toString(), new Object[]
		{ tenantId, storeId, billNum, SysDictionary.PAYMENT_CLASS_COUPONS });
	}
	
	@Override
	public synchronized List<JSONObject> posPayment(String billno, Integer organId, JSONObject object) throws SystemException
	{
		// 查询账单付款方式
		StringBuilder payment = new StringBuilder("select id,jzid,name,amount,count,number,phone,shift_id,pos_num,cashier_num,last_updatetime,customer_id,bill_code,remark,currency_amount from pos_bill_payment where bill_num = ? and store_id = ?");
		List<PosBillPaymentWay> paymentWays = this.jdbcTemplate.query(payment.toString(), new Object[]
		{ billno, organId }, BeanPropertyRowMapper.newInstance(PosBillPaymentWay.class));
		object.put("paymentlist", paymentWays);
		List<JSONObject> list = new ArrayList<JSONObject>();
		list.add(object);

		return list;
	}
	
	@Override
	public double getBillPaymentForSumAmount(String tenancyId, int storeId, String billNum) throws Exception
	{
		double amount = 0d;
		StringBuilder querySql = new StringBuilder("select sum(currency_amount) as currency_amount from pos_bill_payment where tenancy_id =? and store_id=? and bill_num=?");
		SqlRowSet rs = this.jdbcTemplate.queryForRowSet(querySql.toString(), new Object[]{tenancyId,storeId,billNum});
		if(rs.next())
		{
			amount = rs.getDouble("currency_amount");
		}
		return amount;
	}
	
	@Override
	public double getBillPaymentAmount(String tenancyId, int storeId, String billNum) throws Exception
	{
		String sql = new String("select coalesce(sum(currency_amount-more_coupon),0) as amount from pos_bill_payment where bill_num =? and store_id = ?");
		SqlRowSet rs = this.query4SqlRowSet(sql.toString(), new Object[]
		{ billNum, storeId });
		double amount = 0d;
		if (rs.next())
		{
			amount = rs.getDouble("amount");
		}
		return amount;
	}
	
	@Override
	public JSONObject getPosBillPaymentByBillnum(String tenancyId,int storeId,String billNum,Integer paymentId) throws Exception
	{
		JSONObject resultJson = new JSONObject();
		StringBuilder billAmountSql = new StringBuilder();
		billAmountSql.append(" select p.id, p.bill_num,p.type, p.jzid, p.name, p.name_english, p.amount, p.count, p.number, p.phone, p.report_date, p.shift_id, p.pos_num, p.cashier_num, p.last_updatetime, p.currency_amount, p.customer_id, p.bill_code, p.payment_state as item_payment_state, p.batch_num, p.more_coupon, p.fee, p.fee_rate, p.coupon_type,p.rate,p.out_trade_no,");
		billAmountSql.append(" coalesce(b.bill_amount,0) as bill_amount,coalesce(b.payment_amount,0) as payment_amount,coalesce(b.difference,0)  as difference,coalesce(b.discount_amount,0) as discount_amount,sale_mode,b.source,b.bill_property,coalesce(b.table_code,'') as table_code,b.fictitious_table,coalesce(b.order_num,'') as order_num,b.copy_bill_num,b.payment_state,b.bill_state,b.transfer_remark");
		billAmountSql.append(" from pos_bill_payment p left join pos_bill b on b.tenancy_id=p.tenancy_id and b.store_id=p.store_id and b.bill_num=p.bill_num");
		billAmountSql.append(" where b.tenancy_id = ? and b.store_id = ? and b.bill_num = ? and p.jzid=?");
		
		List<JSONObject> list = this.query4Json(tenancyId, billAmountSql.toString(), new Object[]
		{  tenancyId, storeId, billNum,paymentId});

		if (list != null && list.size() > 0)
		{
			resultJson = list.get(0);
		}
		return resultJson;
	}
	
    @Override
    public List<JSONObject> getPosBillPaymentByIDAndClass(String tenancyId, int storeId, int jzid, String paymentClass, String billNum) throws Exception {
        String paymentWaysSql = "select p.bill_num, p.table_code, p.type, p.jzid, p.name, p.amount, p.number, p.phone, p.report_date, p.shift_id, p.pos_num, p.cashier_num, p.currency_amount, p.payment_state from pos_bill_payment p where p.bill_num = ? and p.jzid = ? and p.type = ? and p.tenancy_id = ? and p.store_id = ? ";

        List<JSONObject> paymentWays = this.query4Json(tenancyId, paymentWaysSql, new Object[]{billNum, jzid, paymentClass, tenancyId, storeId});

	    return paymentWays;
    }

	@Override
	public List<JSONObject> getItemInfoByBillNumAndRwid(String tenancyId, int storeId, String billNum, Integer rwid) throws Exception {
		String itemSql = "SELECT  item_price,item_id,item_num,rwid,item_unit_id,bill_num from pos_bill_item where bill_num =? and rwid=? and tenancy_id=? and store_id=? ";

		List<JSONObject> itemInfo = this.query4Json(tenancyId, itemSql, new Object[]{billNum,rwid, tenancyId, storeId});

		return itemInfo;
	}

	@Override
	public int insertPosBillPayment(String tenancyId, int storeId, PosBillPayment paymentEntity) throws Exception
	{
		StringBuilder insertPaymentSql = new StringBuilder(
				"insert into pos_bill_payment(tenancy_id,store_id,bill_num,table_code,type,jzid,name,name_english,amount,count,number,phone,report_date,shift_id,pos_num,cashier_num,last_updatetime,is_ysk,rate,currency_amount,upload_tag,customer_id,bill_code,remark,payment_state,param_cach,batch_num,more_coupon,fee,fee_rate,coupon_type,yjzid,coupon_buy_price,due,tenancy_assume,third_assume,third_fee,payment_uid,out_trade_no) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
		this.update(
				insertPaymentSql.toString(),
				new Object[]
				{ paymentEntity.getTenancy_id(), paymentEntity.getStore_id(), paymentEntity.getBill_num(), paymentEntity.getTable_code(), paymentEntity.getType(), paymentEntity.getJzid(), paymentEntity.getName(), paymentEntity.getName_english(), paymentEntity.getAmount(), paymentEntity.getCount(),
						paymentEntity.getNumber(), paymentEntity.getPhone(), paymentEntity.getReport_date(), paymentEntity.getShift_id(), paymentEntity.getPos_num(), paymentEntity.getCashier_num(), paymentEntity.getLast_updatetime(), paymentEntity.getIs_ysk(), paymentEntity.getRate(),
						paymentEntity.getCurrency_amount(), paymentEntity.getUpload_tag(), paymentEntity.getCustomer_id(), paymentEntity.getBill_code(), paymentEntity.getRemark(), paymentEntity.getPayment_state(), paymentEntity.getParam_cach(), paymentEntity.getBatch_num(),
						paymentEntity.getMore_coupon(), paymentEntity.getFee(), paymentEntity.getFee_rate(), paymentEntity.getCoupon_type(), paymentEntity.getYjzid(), paymentEntity.getCoupon_buy_price(), paymentEntity.getDue(), paymentEntity.getTenancy_assume(), paymentEntity.getThird_assume(),
						paymentEntity.getThird_fee(), paymentEntity.getPayment_uid(),paymentEntity.getOut_trade_no() });

		StringBuilder rwidSql = new StringBuilder("select currval('pos_bill_payment_id_seq'::regclass) ");
		return this.queryForInt(rwidSql.toString(), new Object[] {});
	}
	
	@Override
	public int insertPosBillPayment(String tenancyId, int storeId, String billNum, Date reportDate, int shiftId, String posNum, String optNum, String tableCode, String type, int jzid, String name, String nameEnglish, double rate, double amount, double count, double currencyAmount, String number,
			String phone, String isYsk, Integer customerId, String billCode, String remark, Timestamp lastUpdatetime, String paymentState, String batchNum,String paramCach,double more_coupon,Integer yjzid) throws Exception
	{
		PosBillPayment paymentEntity = new PosBillPayment(tenancyId, storeId, reportDate, shiftId, posNum, optNum, billNum, batchNum, tableCode, type, jzid, name, nameEnglish, amount, currencyAmount, Double.valueOf(count).intValue(), number, phone, lastUpdatetime, paymentState);
		paymentEntity.setRate(rate);
		paymentEntity.setIs_ysk(isYsk);
		paymentEntity.setCustomer_id(customerId);
		paymentEntity.setBill_code(billCode);
		paymentEntity.setRemark(remark);
		paymentEntity.setParam_cach(paramCach);
		paymentEntity.setMore_coupon(more_coupon);
		paymentEntity.setYjzid(yjzid);
		paymentEntity.setCoupon_buy_price(currencyAmount);
		paymentEntity.setDue(currencyAmount);
		return this.insertPosBillPayment(tenancyId, storeId, paymentEntity);
	}
	
	@Override
	public int insertPosBillPayment(String tenancyId, int storeId, String billNum, Date reportDate, int shiftId, String posNum, String optNum, String tableCode, String type, int jzid, String name, String nameEnglish, double rate, double amount, double count, double currencyAmount, String number,
			String phone, String isYsk, Integer customerId, String billCode, String remark, Timestamp lastUpdatetime, String paymentState, String batchNum, String paramCach, double moreCoupon) throws Exception
	{
		PosBillPayment paymentEntity = new PosBillPayment(tenancyId, storeId, reportDate, shiftId, posNum, optNum, billNum, batchNum, tableCode, type, jzid, name, nameEnglish, amount, currencyAmount, Double.valueOf(count).intValue(), number, phone, lastUpdatetime, paymentState);
		paymentEntity.setRate(rate);
		paymentEntity.setIs_ysk(isYsk);
		paymentEntity.setCustomer_id(customerId);
		paymentEntity.setBill_code(billCode);
		paymentEntity.setRemark(remark);
		paymentEntity.setParam_cach(paramCach);
		paymentEntity.setMore_coupon(moreCoupon);
		paymentEntity.setCoupon_buy_price(currencyAmount);
		paymentEntity.setDue(currencyAmount);
		return this.insertPosBillPayment(tenancyId, storeId, paymentEntity);
	}
	
	@Override
	public void batchInsertPodBillPayment(String tenancyId, int storeId, List<PosBillPayment> paymentList) throws Exception
	{
		List<Object[]> batchArgs = new ArrayList<Object[]>();
		for (PosBillPayment payment : paymentList)
		{
			batchArgs.add(new Object[]
			{ tenancyId, storeId, payment.getBill_num(), payment.getTable_code(), payment.getType(), payment.getJzid(), payment.getName(), payment.getName_english(), payment.getAmount(), payment.getCount(), payment.getNumber(), payment.getPhone(), payment.getReport_date(), payment.getShift_id(),
					payment.getPos_num(), payment.getCashier_num(), payment.getLast_updatetime(), payment.getIs_ysk(), payment.getRate(), payment.getCurrency_amount(), payment.getUpload_tag(), payment.getCustomer_id(), payment.getBill_code(), payment.getRemark(), payment.getPayment_state(),
					payment.getParam_cach(), payment.getBatch_num(), payment.getMore_coupon(), payment.getFee(), payment.getFee_rate(), payment.getCoupon_type(), payment.getYjzid(),payment.getPayment_uid(),payment.getOut_trade_no() });
		}

		if (batchArgs.size() > 0)
		{
			String sql = new String(
					"insert into pos_bill_payment(tenancy_id,store_id,bill_num,table_code,type,jzid,name,name_english,amount,count,number,phone,report_date,shift_id,pos_num,cashier_num,last_updatetime,is_ysk,rate,currency_amount,upload_tag,customer_id,bill_code,remark,payment_state,param_cach,batch_num,more_coupon,fee,fee_rate,coupon_type,yjzid,payment_uid,out_trade_no) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
			this.batchUpdate(sql, batchArgs);
		}
	}
	
	@Override
	public void deletePosBillPaymentByBillmum(String tenancyId, int storeId, String billNum) throws Exception
	{
		String sql = new String("delete from pos_bill_payment where tenancy_id=? and store_id=? and bill_num=? ");
		this.update(sql, new Object[]{tenancyId,storeId,billNum});
	}
	
	@Override
	public void deletePosBillPaymentByBillmum(String tenancyId, int storeId, String billNum, String outTradeNo) throws Exception
	{
		String sql = new String("delete from pos_bill_payment where tenancy_id=? and store_id=? and bill_num=? and out_trade_no=?");
		this.update(sql, new Object[]{tenancyId,storeId,billNum,outTradeNo});
	}
	
	@Override
	public void deletePosBillPaymentByID(String tenancyId, int storeId, String billNum, Integer paymentID) throws Exception
	{
		String deletePaymentSql = new String("delete from pos_bill_payment where tenancy_id=? and store_id=? and bill_num =? and id=?");
		this.update(deletePaymentSql.toString(), new Object[]
		{ tenancyId, storeId, billNum, paymentID });
	}
	
	@Override
	public void updatePosBillPaymentForPaymentState(String tenancyId, int storeId, String billNum, String outTradeNo,String paymentState,String billCode) throws Exception
	{
		String sql = new String("update pos_bill_payment set bill_code=? ,payment_state=? where tenancy_id=? and store_id=? and bill_num=? and out_trade_no=? ");
		this.update(sql, new Object[]
		{ billCode, paymentState, tenancyId, storeId, billNum, outTradeNo });
	}
	
	@Override
	public void updatePosBillPaymentForPaymentState(String tenancyId, int storeId, String billNum, String paymentState, String billCode) throws Exception
	{
		String sql = new String("update pos_bill_payment set bill_code=? ,payment_state=? where tenancy_id=? and store_id=? and bill_num=? ");
		this.update(sql, new Object[]
		{ billCode, paymentState, tenancyId, storeId, billNum });
	}
	
	@Override
	public void updatePosBillPaymentForPaymentState(String tenancyId, int storeId, String billNum, List<PosBillPayment> billPaymentList) throws Exception
	{
		List<Object[]> batchArgs = new ArrayList<Object[]>();
		for (PosBillPayment payment : billPaymentList)
		{
			batchArgs.add(new Object[]
			{ payment.getBill_code(), payment.getPayment_state(), payment.getCoupon_buy_price(), payment.getDue(), payment.getTenancy_assume(), payment.getThird_assume(), payment.getThird_fee(),payment.getParam_cach(), payment.getId(), tenancyId, storeId, billNum });
		}

		if (batchArgs.size() > 0)
		{
			String sql = new String("update pos_bill_payment set bill_code=?,payment_state=?,coupon_buy_price=?,due=?,tenancy_assume=?,third_assume=?,third_fee=?,param_cach=? where id=? and tenancy_id=? and store_id=? and bill_num=?");
			this.batchUpdate(sql, batchArgs);
		}
	}
	
	@Override
	public void updatePosBillPayment(String tenancyId, int storeId, String billNum, Date reportDate, int shiftId, String posNum, String optNum, String tableCode, String type, int jzid, String name, String nameEnglish, double rate, double amount, double count, double currencyAmount, String number,
			String phone, String isYsk, Integer customerId, String billCode, String remark, Timestamp lastUpdatetime, int uploadtag,String paymentState, String batchNum,String paramCach,double more_coupon) throws Exception
	{
		StringBuilder sql = new StringBuilder();
		sql.append("update pos_bill_payment set report_date=?,shift_id=?,pos_num=?,cashier_num=?,table_code=?,type=?,jzid=?,name=?,name_english=?,rate=?,amount=?,count=?,currency_amount=?,number=?,phone=?,is_ysk=?,customer_id=?,bill_code=?,remark=?,last_updatetime=?,upload_tag=?,payment_state=?,batch_num=?,param_cach=?,more_coupon=? where tenancy_id=? and store_id=? and bill_num=?");
		this.jdbcTemplate.update(sql.toString(), new Object[]
				{ reportDate,shiftId,posNum,optNum,tableCode,type,jzid,name,nameEnglish,rate,amount,count,currencyAmount,number,phone,isYsk,customerId,billCode,remark,lastUpdatetime,uploadtag,paymentState,batchNum,paramCach,more_coupon,tenancyId,storeId,billNum});
	}
	
	@Override
	public void insertPosBillPaymentLogByBillmum(String tenancyId, int storeId, String billNum, String outTradeNo, String paymentState, String billCode) throws Exception
	{
		StringBuffer sql = new StringBuffer(
				"insert into pos_bill_payment_log(tenancy_id,store_id,bill_num,table_code,type,jzid,name,name_english,amount,count,number,phone,report_date,shift_id,pos_num,cashier_num,last_updatetime,is_ysk,rate,currency_amount,upload_tag,customer_id,bill_code,remark,payment_state,param_cach,batch_num,more_coupon,coupon_type)");
		sql.append(" select tenancy_id,store_id,bill_num,table_code,type,jzid,name,name_english,amount,count,number,phone,report_date,shift_id,pos_num,cashier_num,last_updatetime,is_ysk,rate,currency_amount,'0' as upload_tag,customer_id,? as bill_code,remark,? as payment_state,param_cach,batch_num,more_coupon,coupon_type from pos_bill_payment");
		sql.append(" where tenancy_id=? and store_id=? and bill_num=?");
		if (Tools.hv(outTradeNo))
		{
			sql.append(" and out_trade_no='").append(outTradeNo).append("'");
		}

		this.update(sql.toString(), new Object[]
		{ billCode, paymentState, tenancyId, storeId, billNum });
	}
	
	@Override
	public void insertPosBillPaymentLog(String tenancyId, int storeId, String billNum, Date reportDate, int shiftId, String posNum, String optNum, String tableCode, String type, int jzid, String name, String nameEnglish, double rate, double amount, double count, double currencyAmount,
			String number, String phone, String isYsk, Integer customerId, String billCode, String remark, Timestamp lastUpdatetime, String paymentState, String batchNum,String paramCach,double more_coupon) throws Exception
	{
		StringBuilder insertPaymentLogSql = new StringBuilder(
				"insert into pos_bill_payment_log(tenancy_id,store_id,bill_num,report_date,shift_id,pos_num,cashier_num,table_code,type,jzid,name,name_english,rate,amount,count,currency_amount,number,phone,is_ysk,customer_id,bill_code,remark,last_updatetime,upload_tag,payment_state,batch_num,param_cach,more_coupon) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,0,?,?,?,?)");
		this.update(insertPaymentLogSql.toString(), new Object[]
		{ tenancyId,storeId,billNum,reportDate,shiftId,posNum,optNum,tableCode,type,jzid,name,nameEnglish,rate,amount,count,currencyAmount,number,phone,isYsk,customerId,billCode,remark,lastUpdatetime,paymentState,batchNum,paramCach,more_coupon});
	}
	
	/**
	 * 根据订单号查询第三方支付记录
	 * 
	 * @param thirdPayCharge
	 * @return
	 * @throws Exception
	 */
	@Override
	public JSONObject getPayThirdByOrderNum(String tenancyId, int storeId, String orderNo,String aidOrderNum, String thirdPayCharge) throws Exception
	{
		StringBuffer querySql = new StringBuffer("select * from pos_third_payment_order where tenancy_id=? and store_id=? and order_num=? and object_name=?");
		querySql.append(" and (case when (exists (select 1 from pos_third_payment_order where tenancy_id=? and store_id=? and object_name=? and aid_order_num=?)) then aid_order_num=? else ((is_refunded is null or is_refunded=false) and status<>'0') end )");
		querySql.append(" order by create_time desc");
		List<JSONObject> list = this.query4Json(tenancyId, querySql.toString(), new Object[]
		{ tenancyId, storeId, orderNo, thirdPayCharge, tenancyId, storeId, thirdPayCharge, aidOrderNum, aidOrderNum });
		if (list != null && list.size() > 0)
		{
			return list.get(0);
		}
		return null;
	}
	
	@Override
	public PosThirdPaymentOrderEntity getThirdPaymentByAidOrderNum(String tenancyId, int storeId, String aidOrderNum, String objectName) throws Exception
	{
		String querySql = "select * from pos_third_payment_order where tenancy_id=? and store_id=? and aid_order_num=? and object_name=? and (is_refunded is null or is_refunded=false)";

		List<PosThirdPaymentOrderEntity> list = (List<PosThirdPaymentOrderEntity>) this.query(querySql, new Object[]
		{ tenancyId, storeId, aidOrderNum, objectName },BeanPropertyRowMapper.newInstance(PosThirdPaymentOrderEntity.class));
		if (list != null && list.size() > 0)
		{
			return list.get(0);
		}
		return null;
	}
		
	@Override
	public void insertPaymentCoupons(String tenancyId, int storeId, List<Object[]> coupons) throws Exception
	{
		String sql = new String("insert into pos_bill_payment_coupons(tenancy_id,store_id,bill_num,report_date,payment_id,coupons_code,deal_value,deal_name,last_updatetime,remark,upload_tag,is_cancel) values(?,?,?,?,?,?,?,?,?,?,'0','0')");
		this.batchUpdate(sql, coupons);
	}
	
	@Override
	public int insertPosBillPaymentCoupons(String tenantId, Integer storeId, PosBillPaymentCoupons billPaymentCoupons) throws Exception
	{
		List<PosBillPaymentCoupons> coupons = new ArrayList<PosBillPaymentCoupons>();
		coupons.add(billPaymentCoupons);

		int[] res = this.insertPosBillPaymentCoupons(tenantId, storeId, coupons);
		if (null != res && res.length > 0)
		{
			return res[0];
		}
		return 0;
	}

	@Override
	public int[] insertPosBillPaymentCoupons(String tenantId, Integer storeId, List<PosBillPaymentCoupons> billPaymentCoupons) throws Exception
	{
		List<Object[]> batchArgs = new ArrayList<Object[]>();
		for (PosBillPaymentCoupons coupons : billPaymentCoupons)
		{
			batchArgs.add(new Object[]
			{ coupons.getTenancy_id(), coupons.getStore_id(), coupons.getBill_num(), coupons.getReport_date(), coupons.getPayment_id(), coupons.getCoupons_code(), coupons.getDeal_value(), coupons.getDeal_name(), coupons.getLast_updatetime(), coupons.getRemark(), coupons.getUpload_tag(),
					coupons.getIs_cancel(), coupons.getClass_id(), coupons.getType_id(), coupons.getDiscount_money(), coupons.getDiscount_num(), coupons.getChanel(), coupons.getPrice(), coupons.getItem_id(), coupons.getItem_num(), coupons.getCoupons_pro(), coupons.getCoupon_type(),
					coupons.getCoupon_buy_price(), coupons.getDue(), coupons.getTenancy_assume(), coupons.getThird_assume(), coupons.getThird_fee(), coupons.getRequest_state(), coupons.getRwid(), coupons.getItem_unit_id() ,coupons.getRwids()});
		}
		
		if (batchArgs.size() > 0)
		{
			String insertSql = new String(
					"insert into pos_bill_payment_coupons(tenancy_id,store_id,bill_num,report_date,payment_id,coupons_code,deal_value,deal_name,last_updatetime,remark,upload_tag,is_cancel,class_id,type_id,discount_money,discount_num,chanel,price,item_id,item_num,coupons_pro,coupon_type,coupon_buy_price,due,tenancy_assume,third_assume,third_fee,request_state,rwid,item_unit_id,rwids) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
			return this.batchUpdate(insertSql, batchArgs);
		}
		return null;
	}
	
	@Override
	public int deletePosBillPaymentCoupons(String tenancyId, int storeId, String billNum,String paymentUid) throws Exception
	{
		StringBuilder updateBillCouponsSql = new StringBuilder("delete from pos_bill_payment_coupons where tenancy_id =? and store_id=? and bill_num=?  and payment_id=?");
		return this.update(updateBillCouponsSql.toString(), new Object[]
		{ tenancyId, storeId, billNum, paymentUid });
	}
	
	@Override
	public void updatePaymentCouponsCancelStateById(String tenancyId, int storeId, String billNum, String coupon)throws Exception
	{
		String sql = new String("update pos_bill_payment_coupons set is_cancel='1' where  tenancy_id=? and store_id=? and bill_num=? and coupons_code=?");
		this.update(sql, new Object[]{tenancyId,storeId,billNum,coupon});
	}
	
	@Override
	public List<JSONObject> getPaymentCouponsByPaymentId(String tenancyId, int storeId, String billNum, String paymentId) throws Exception
	{
		String sql = new String("select * from pos_bill_payment_coupons where tenancy_id=? and store_id=? and bill_num=? and payment_id=?");
		return this.query4Json(tenancyId, sql, new Object[]
		{ tenancyId, storeId, billNum, paymentId });
	}

	@Override
	public int deleteBillPaymentCouponsByBillNum(String tenancyId, int storeId, String billNum) throws Exception
	{
		String sql = new String("delete from pos_bill_payment_coupons where tenancy_id=? and store_id=? and bill_num=? ");
		return this.update(sql, new Object[]
		{ tenancyId, storeId, billNum });
	}
	
	@Override
	public int deleteBillPaymentCouponsByPaymentUid(String tenancyId, int storeId, String billNum,String paymentUid) throws Exception
	{
		StringBuilder deleteSql = new StringBuilder("delete from pos_bill_payment_coupons where tenancy_id =? and store_id=? and bill_num=? and payment_id = ?");
		return this.update(deleteSql.toString(), new Object[]
		{ tenancyId, storeId, billNum, paymentUid });
	}
	
	@Override
	public int deleteBillPaymentCouponsByBillNum(String tenancyId, int storeId, String billNum,String payNo) throws Exception
	{
		StringBuffer sql = new StringBuffer("delete from pos_bill_payment_coupons pc where tenancy_id=? and store_id=? and bill_num=?");
		sql.append(" and EXISTS (select payment_uid from pos_bill_payment where tenancy_id=pc.tenancy_id and store_id=pc.store_id and bill_num=pc.bill_num and payment_uid=pc.payment_id and out_trade_no=?)");
		return this.update(sql.toString(), new Object[]
		{ tenancyId, storeId, billNum ,payNo});
	}
	@Override
	public JSONObject getBillItemForCouponByRwid(String tenancyId, int storeId, String billNum, Integer rwid) throws Exception
	{
		JSONArray rwids = new JSONArray();
		rwids.add(rwid);
		List<JSONObject> list = this.getBillItemForCouponByRwid(tenancyId, storeId, billNum, rwids);
		if(null!=list && list.size()>0)
		{
			return list.get(0);
		}
		return null;
	}
	
	@Override
	public List<JSONObject> getBillItemForCouponByRwid(String tenancyId, int storeId, String billNum, JSONArray rwids) throws Exception
	{
		if (null != rwids && 0 < rwids.size())
		{
			StringBuilder rwid = new StringBuilder();
			for (Object obj : rwids)
			{
				if (rwid.length() > 0)
				{
					rwid.append(",");
				}
				rwid.append("'").append(String.valueOf(obj)).append("'");
			}

			StringBuilder sql = new StringBuilder();
			sql.append("select bi.rwid,bi.item_id,bi.item_unit_id,bi.item_name,bi.item_count,bi.item_price,bi.item_amount,bi.real_amount,bi.discount_amount,bi.single_discount_amount,bi.discountr_amount,pc.discount_num,pc.discount_money from pos_bill_item bi ");
			sql.append("left join (select tenancy_id,store_id,bill_num,rwid,sum(discount_num) discount_num,sum(discount_money) discount_money from pos_bill_payment_coupons where rwid is not null group by tenancy_id,store_id,bill_num,rwid ) pc on bi.tenancy_id=pc.tenancy_id and bi.store_id=pc.store_id and bi.bill_num=pc.bill_num and bi.rwid=pc.rwid ");
			sql.append("where bi.tenancy_id=? and bi.store_id = ? and bi.bill_num = ? and bi.rwid in (").append(rwid).append(") ");
			sql.append("order by round(bi.real_amount/bi.item_count,2) desc,round(bi.item_amount/bi.item_count,2) ");
			return this.query4Json(tenancyId, sql.toString(), new Object[]
			{ tenancyId, storeId, billNum });
		}
		return null;
	}

	public List<JSONObject> getBillItemForCouponByRwid(String tenancyId, int storeId, String billNum, JSONArray rwids,String batchNum) throws Exception {

		StringBuilder rwid = new StringBuilder();
		for (Object obj : rwids) {
			if (rwid.length() > 0) {
				rwid.append(",");
			}
			rwid.append("'").append(String.valueOf(obj)).append("'");
		}

		StringBuilder sql = new StringBuilder();
		sql.append("select bi.rwid,bi.item_id,bi.item_unit_id,bi.item_name,bi.item_count,bi.item_price,bi.item_amount," +
				"bi.real_amount,bi.discount_amount,bi.single_discount_amount,bi.discountr_amount,pc.discount_num," +
				"pc.discount_money from pos_bill_item bi ");
		sql.append("left join (select tenancy_id,store_id,bill_num,rwid,sum(discount_num) discount_num," +
				"sum(discount_money) discount_money from pos_bill_payment_coupons where rwid is not null group by tenancy_id,store_id,bill_num,rwid ) pc on bi.tenancy_id=pc.tenancy_id and bi.store_id=pc.store_id and bi.bill_num=pc.bill_num and bi.rwid=pc.rwid ");
		sql.append("where bi.tenancy_id=? and bi.store_id = ? and bi.bill_num = ? and bi.rwid in (").append(rwid).append(") ");
		sql.append("order by round(bi.real_amount/bi.item_count,2) desc,round(bi.item_amount/bi.item_count,2) ");
		return this.query4Json(tenancyId, sql.toString(), new Object[]
				{tenancyId, storeId, billNum});
	}
	
	@Override
	public List<JSONObject> getBillItemForCoupon(String tenantId, int storeId, String billNum, String batchNum) throws Exception
	{
		StringBuilder sql = new StringBuilder(" select a.rwid,a.item_id,a.item_unit_id,a.item_price,a.item_count,a.item_amount, (a.real_amount - coalesce(a.single_discount_amount,0)) as real_amount,a.item_property,a.discount_rate,a.discountr_amount,a.discount_amount as dismoney " +
				" from pos_bill_item a,pos_bill b where a.tenancy_id='" + tenantId + "' and a.store_id=" + storeId + " and a.bill_num='" + billNum + "' and a.item_property<>'" + SysDictionary.ITEM_PROPERTY_MEALLIST + "' " +
				" and a.bill_num = b.bill_num and b.bill_property='" + SysDictionary.BILL_PROPERTY_OPEN + "' and (a.item_remark is null or a.item_remark='') and a.real_amount>0 ");
		if(Tools.hv(batchNum))
		{
			sql.append(" and b.batch_num='" + batchNum + "' ");
		}
		return this.queryString4Json(tenantId, sql.toString());
	}
	
	@Override
	public List<JSONObject> getPosBillItemForSingleByBillNum(String tenancyId, int storeId, String billNum) throws Exception
	{
		StringBuilder selectSql = new StringBuilder("select bi.rwid,bi.item_name,bi.item_id,bi.item_num,bi.item_price,bi.item_count,bi.item_amount,bi.real_amount,bi.discount_amount,bi.discountr_amount,bi.method_money,bi.assist_money from pos_bill_item bi where bi.tenancy_id=? and bi.store_id=? and bi.bill_num=? and bi.item_property<>? and bi.item_remark is null");
		return this.query4Json(tenancyId, selectSql.toString(), new Object[]
		{ tenancyId, storeId, billNum,SysDictionary.ITEM_PROPERTY_MEALLIST });
	}
	
	/** 修改账单税价分离
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param Salemode
	 * @throws Exception
	 */
	public void updatePosBillTaxPriceSeparation(String tenancyId, int storeId, String billNum, String saleMode) throws Exception{
		String sql = new String("select taxpayer_type_code from organ where tenancy_id = ? and id = ? ");
		SqlRowSet rs = this.query4SqlRowSet(sql.toString(), new Object[]
		{ tenancyId, storeId });
		String taxPayertype = "";
		if (rs.next())
		{	//取机构纳税属性：1为一般纳税人；2为小规模纳税人
            taxPayertype = rs.getString("taxpayer_type_code");
			String updateBillItemTax = new String("update pos_bill_item pbi set tax_rate = "+
			"(case when '"+ saleMode +"' = '"+ SysDictionary.SALE_MODE_TS01 +"' then (case when '"+ taxPayertype +"' = '1' then hii.ts_general_rate else hii.ts_smallscale_rate end) "+
			"else (case when  '"+ taxPayertype +"' = '1'  then hii.ws_general_rate else hii.ws_smallscale_rate end) end)/100 "+
			"from hq_item_info hii where pbi.item_id = hii.id and pbi.tenancy_id = hii.tenancy_id and pbi.bill_num = ? and pbi.store_id =? and pbi.tenancy_id=? ");
			this.update(updateBillItemTax, new Object[]
			{billNum, storeId, tenancyId });

			String taxround = "2";
			StringBuilder qureyParaSql = new StringBuilder("select para_value from sys_parameter where para_code='taxround'");
			SqlRowSet rsp = this.query4SqlRowSet(qureyParaSql.toString());
			if (rsp.next())
			{   //取税额保留小数位数（默认2位；最多6位）；
				taxround = rsp.getString("para_value");
			}
			//计算账单明细应收菜品税额,菜品付款税额	
			String updateBillItemTaxMoney = new String("update pos_bill_item pbi set tax_money = round(item_amount * (select MIN(pbi.tax_rate) from pos_bill_item pbi	"+
			"where pbi.bill_num = ? and pbi.store_id =? and pbi.tenancy_id=? )/(1 + (select MIN(pbi.tax_rate) from pos_bill_item pbi "+
			"where pbi.bill_num = ? and pbi.store_id =? and pbi.tenancy_id=? )), "+ taxround +"), "+
			"payment_tax_money = round(real_amount * (select MIN(pbi.tax_rate) from pos_bill_item pbi	"+
			"where pbi.bill_num = ? and pbi.store_id =? and pbi.tenancy_id=? )/(1 + (select MIN(pbi.tax_rate) from pos_bill_item pbi "+
			"where pbi.bill_num = ? and pbi.store_id =? and pbi.tenancy_id=? )), "+ taxround +") where  pbi.bill_num = ? and pbi.store_id =? and pbi.tenancy_id=? ");
			this.update(updateBillItemTaxMoney, new Object[]
			{billNum, storeId, tenancyId, billNum, storeId, tenancyId, billNum, storeId, tenancyId, billNum, storeId, tenancyId,billNum, storeId, tenancyId });
			//计算账单明细应税菜品未税税额,菜品付款未税金额
			String updateBillItem_notax = new String("update pos_bill_item pbi set item_notax = item_amount - tax_money, payment_notax = real_amount - payment_tax_money "+
			"where pbi.bill_num = ? and pbi.store_id =? and pbi.tenancy_id=? ");
			this.update(updateBillItem_notax, new Object[]
			{billNum, storeId, tenancyId});
			//计算账单税率
			String updateBillTax = new String("update pos_bill pb set tax_rate = (select MIN(pbi.tax_rate) from pos_bill_item pbi "+
			"where pbi.bill_num = ? and pbi.store_id =? and pbi.tenancy_id=? ), tax_money =0, tax_amount = (select sum(A.amount) from "+
			"( select amount,if_invoicing from pos_bill_payment pbp left join payment_way pw on pbp.jzid=pw.id where pbp.bill_num = ? "+
			"and pbp.store_id =? and pbp.tenancy_id=? ) A WHERE if_invoicing = '1'), no_tax_amount=0 where pb.bill_num = ? and pb.store_id =? and pb.tenancy_id=? ");
			this.update(updateBillTax, new Object[]
			{billNum, storeId, tenancyId, billNum, storeId, tenancyId, billNum, storeId, tenancyId });
			//计算账单服务费税率
			String updateBillSvrTax = new String("update pos_bill pb set service_tax_rate = (case when '"+ taxPayertype +"' = '1' then hsf.general_rate else hsf.smallscale_rate end)/100 "+
			"from hq_service_fee_type hsf "+
			"where pb.bill_num = ? and pb.store_id =? and pb.tenancy_id=? ");
			this.update(updateBillSvrTax, new Object[]
			{billNum, storeId, tenancyId});			
	        //计算账单计税税额，账单计税未税金额，'账单付款税额、
			//账单付款未税金额，账单应收税额，服务费税率值，服务费付款未税金额
			String updatePayTax = new String("update pos_bill pb set tax_money = round(tax_amount * tax_rate/(1+tax_rate), "+ taxround +"), "+
			"no_tax_amount= round(tax_amount - tax_amount * tax_rate/(1+tax_rate), "+ taxround +"), "+
			"payment_tax_money=round(payment_amount * tax_rate/(1+tax_rate), "+ taxround +"), "+
			"payment_notax=round(payment_amount - payment_amount * tax_rate/(1+tax_rate), "+ taxround +"), "+
			"bill_tax_money=round(bill_amount * tax_rate/(1+tax_rate), "+ taxround +"), "+
			"bill_notax=round(bill_amount - bill_amount * tax_rate/(1+tax_rate), "+ taxround +"), "+
			"service_tax_money=round(service_amount * service_tax_rate/(1+service_tax_rate), "+ taxround +"), "+
			"service_notax= round(service_amount - service_amount * service_tax_rate/(1+service_tax_rate), "+ taxround +") "+
			"where pb.bill_num = ? and pb.store_id =? and pb.tenancy_id=? ");
			this.update(updatePayTax, new Object[]
			{billNum, storeId, tenancyId });
		}	
	}

	@Override
	public JSONObject getEmployees(String tenancyId, String waiterNum) throws Exception {
		if (!StringUtil.hasText(waiterNum))
			return null;
		StringBuilder sql = new StringBuilder();
		sql.append(" select e.employee_code as emp_code,e.name as emp_name, 1 as emp_role ");
		sql.append(" from employee e ");
		sql.append(" where e.tenancy_id = '" + tenancyId +"' and e.id = '" + waiterNum + "' ");
		List<JSONObject> list = this.query4Json(tenancyId, sql.toString());
		if (null != list && list.size() > 0)
			return list.get(0);
		else
			return null;
	}
	
	@Override
	public List<JSONObject> getPosBillPaymentRegainByBillnum(String tenancyId, int storeId, String billNum, Integer regainCount) throws Exception
	{
		StringBuilder selectSql = new StringBuilder("select bp.*,coalesce(pw.payment_class,bp.type) as payment_class,pw.if_jifen from pos_bill_payment_regain bp left join payment_way pw on bp.jzid=pw.id where bp.bill_num =? and bp.regain_count=? and bp.store_id = ? and bp.tenancy_id=?");

		return this.query4Json(tenancyId, selectSql.toString(), new Object[]
		{ billNum, regainCount, storeId, tenancyId });
	}

    @Override
    public List<JSONObject> getPaymentCouponsByBillNum(String tenancyId, Integer storeId, String billNum) throws Exception
    {
        String sql = new String("select * from pos_bill_payment_coupons where tenancy_id=? and store_id=? and bill_num=? ");
        return this.query4Json(tenancyId, sql, new Object[]
                { tenancyId, storeId, billNum });
    }

}
