package com.tzx.pos.base.dao;

import com.tzx.framework.common.util.dao.GenericDao;
import net.sf.json.JSONObject;

import java.util.List;

/**
 * @Author: wj
 * @Description:
 * @Date: Created in 2018/1/15 下午4:59
 */

public interface ThirdCouponsDao extends GenericDao {

    String NAME = "com.tzx.pos.base.dao.imp.ThirdCouponsDaoImp";

    /**
     *  更新第三方票券的信息
     * @param coupon_buy_price 用户实付(券购买价)
     * @param due 券账单净收(结算给商家的钱)
     * @param tenancy_assume 商家优惠承担
     * @param third_assume 第三方优惠承担
     * @param third_fee 第三方票券服务费
     * @param bill_num 账单号
     * @param couponCode 票券号
     * @param state 修改状态
     * @return
     * @throws Exception
     */
    boolean updateThirdCoupons(double coupon_buy_price, double due, double tenancy_assume, double third_assume, double third_fee, String bill_num, String couponCode, String tenancyId, Integer storeId,String paymentId, int state) throws Exception;

    /**
     * 查询未获取的新美大验券 券码
     * @param tenancy_id
     * @param store_id
     * @return
     */
    List<JSONObject> selectThirdCoupons(String tenancy_id, Integer store_id, String report_date) throws Exception;

}
