package com.tzx.orders.bo.imp;

import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.exception.HttpRestException;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.Tools;
import com.tzx.orders.base.util.OrderUtil;
import com.tzx.orders.bo.OrdersSyncService;
import com.tzx.orders.po.springjdbc.dao.OrdersManagementDao;

import net.sf.json.JSONObject;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service(OrdersSyncService.NAME)
public class OrderSyncServiceImp implements OrdersSyncService {
    private Logger logger = Logger.getLogger(this.getClass());

    public static boolean NO_NEED_SYNC;
    public static boolean NO_NEED_SEND;

    @Resource(name = OrdersManagementDao.NAME)
    private OrdersManagementDao ordersDao;

    private StringBuilder sql = null;

    @Override
    public void sync(int operType, String orderNum) throws Exception {
        sync(operType, orderNum, null);
    }

    @Override
    public void mergeTable(String targetOrderNum, String... srcOrderNums) throws Exception {
    	String srcCodes = "";
    	try
		{
	        sql = new StringBuilder();
	
	        if (null == srcOrderNums || srcOrderNums.length == 0) {
	            return;
	        }
	
	        if (null == targetOrderNum) {
	            targetOrderNum = "0";
	        }
	
	        Map<String, String> systemMap = Constant.getSystemMap();
	        String tenantId = systemMap.get("tenent_id");
	        int storeId = Integer.parseInt(systemMap.get("store_id"));
	
	       
	        for (String code : srcOrderNums) {
	            sql.append(getOrderItemSql(code));
	            srcCodes += ",'" + code + "'";
	        }
	        srcCodes = srcCodes.replaceFirst(",", "");
	
	        sql.append("INSERT INTO cc_order_item_retain (tenancy_id, group_index, order_code, item_id, unit_id, \"number\", price, remark, is_gift, item_menu_id, item_name, product_fee, share_amount, real_amount, discount_amount, single_discount_amount, discountr_amount, discount_state, discount_rate, discount_mode_id, discount_price, method_money, costs, tag, store_id, is_add_dish, is_commission, discountk_amount, free_amount, givi_amount, upload_tag, report_date ) SELECT tenancy_id, group_index, order_code, item_id, unit_id, \"number\", price, remark, is_gift, item_menu_id, item_name, product_fee, share_amount, real_amount, discount_amount, single_discount_amount, discountr_amount, discount_state, discount_rate, discount_mode_id, discount_price, method_money, costs, tag, store_id, is_add_dish, is_commission, discountk_amount, free_amount, givi_amount, '0', report_date FROM cc_order_item WHERE order_code in (" + srcCodes + ");");
	        sql.append("INSERT INTO cc_order_item_details_retain (tenancy_id, group_index, order_code, item_id, unit_id, \"number\", price, remark, is_add_dish, store_id, upload_tag, report_date, method_money, is_itemgroup, item_group_id ) SELECT tenancy_id, group_index, order_code, item_id, unit_id, \"number\", price, remark, is_add_dish, store_id, '0', report_date, method_money, is_itemgroup, item_group_id FROM cc_order_item_details WHERE order_code in (" + srcCodes + ");");
	
	        sql.append("update cc_order_list set upload_tag=0,actual_pay=0,order_state='10',merge_order_code='" + targetOrderNum + "' where order_code in (" + srcCodes + ");");
	//        sql.append("update pos_bill set order_num='" + targetOrderNum + "' where order_num in(" + srcCodes + ");");
	
	        sql.append("delete from cc_order_item where order_code in (" + srcCodes + ");");
	        sql.append("delete from cc_order_item_details where order_code in (" + srcCodes + ");");
	
	        ordersDao.execute(tenantId, sql.toString());
	
	        String sql = new String("select order_code from cc_order_list where order_code in (" + srcCodes + ")");
			List<JSONObject> list = ordersDao.query4Json(tenantId, sql);
			List<String> codeList = new ArrayList<String>();
			for (JSONObject orderJson : list)
			{
				codeList.add(orderJson.optString("order_code"));
			}

			for (String orderCode : srcOrderNums)
			{
				if (codeList.contains(orderCode))
				{
					completeSaasOrder(orderCode, tenantId, storeId);
				}
			}
			logger.info("[并台成功]" + srcCodes + "合并到" + targetOrderNum);
		}
		catch (Exception e)
		{
			// TODO Auto-generated catch block
			e.printStackTrace();
			logger.info("[并台失败]" + srcCodes + "合并到" + targetOrderNum);
		}
    }

   @Override
    public void changeTable(String orderNum,String targetTableCode) throws Exception{
       Map<String, String> systemMap = Constant.getSystemMap();
       String tenantId = systemMap.get("tenent_id");
       int storeId = Integer.parseInt(systemMap.get("store_id"));
       ordersDao.execute(tenantId, "update cc_order_list set table_code='"+targetTableCode+"' where order_code='"+orderNum+"'");
       JSONObject msg = new JSONObject();
       msg.put("order_code", orderNum);
       msg.put("chanel","WX02");
       msg.put("order_state", "04");
       msg.put("table_code",targetTableCode);

       JSONObject reqResultJson = OrderUtil.sendOrderState(tenantId, storeId, Type.ORDER, Oper.update, Arrays.asList(msg));
       if (null !=reqResultJson && false==reqResultJson.optBoolean("success")) {
           throw SystemException.getInstance(reqResultJson.optString("msg"), PosErrorCode.ORDER_SEND_ORDER_STATE_ERROR);
       }
       logger.info("[转台成功]" + orderNum + "转到桌位:" + targetTableCode);
    }

    @Override
    public void sync(int operType, String orderNum, JSONObject params) throws Exception {

        switch (operType) {
            case PREPAY_ALL_ORDER_CANCEL:
                cancelAllOrder(orderNum, params);
                break;
            case PREPAY_SINGLE_DISH_CANCEL:
                cancelSingleDish(orderNum);
                break;
            case POSTPAY_ORDER_COMPLETE:
                completeOrder(orderNum);
            default:
                break;
        }
    }


    private void completeOrder(String orderNum) throws Exception {

        Map<String, String> systemMap = Constant.getSystemMap();
        String tenantId = systemMap.get("tenent_id");
        int storeId = Integer.parseInt(systemMap.get("store_id"));

        String channel=null,isOnlinePayment=null;

        sql = new StringBuilder();
        sql.append("select chanel,is_online_payment from cc_order_list where order_code='"+orderNum+"';");
        List<JSONObject> jsonObjects= ordersDao.query4Json(tenantId,sql.toString());
        if(null!=jsonObjects&&!jsonObjects.isEmpty()){
           channel=jsonObjects.get(0).optString("chanel");
           isOnlinePayment=jsonObjects.get(0).optString("is_online_payment");
        }
        sql.setLength(0);
        if(com.tzx.orders.base.constant.Constant.WX02_CHANNEL.equals(channel)&&"0".equals(isOnlinePayment)){//微信后付
            sql.append("UPDATE cc_order_list SET bill_num=T.bill_num,order_state = '10', payment_state = '03', total_money = T .total_money, actual_pay = T .actual_pay,shop_fee= T .actual_pay, meal_costs=T.meal_costs,discount_amount = T .discount_amount, discountr_amount=T.discountr_amount, discountk_amount=T.discountk_amount, free_amount=T.free_amount, givi_amount=T.givi_amount, maling_amount=T.maling_amount, report_date=T.report_date, upload_tag = 0 FROM (SELECT A.bill_num,SUM (A .bill_amount) total_money, SUM (A .payment_amount) actual_pay,SUM (A.service_amount) meal_costs, SUM (A.discount_amount) discount_amount, SUM (A.discountr_amount) discountr_amount, SUM (A.discountk_amount) discountk_amount, SUM (A.free_amount) free_amount, SUM (A.givi_amount) givi_amount, SUM (A.maling_amount) maling_amount, A .order_num, A .report_date FROM pos_bill A WHERE A .order_num = '" + orderNum + "' GROUP BY A.bill_num,A .order_num , A .report_date ) T WHERE order_code = T .order_num;");
            sql.append(getOrderItemSql(orderNum));
            try{
                completeSaasOrder(orderNum,tenantId,storeId);
            }catch (Exception e){
               logger.error("总部订单状态更新失败!",e);
            }
        }else {
            sql.append("update cc_order_list set upload_tag=0,bill_num=t.bill_num, report_date=t.report_date from (select bill_num,report_date from pos_bill where order_num='" + orderNum + "') t where order_code='" + orderNum + "';");
            sql.append("update cc_order_item set report_date=(select report_date from pos_bill where order_num='" + orderNum + "' limit 1),upload_tag=0 where order_code='" + orderNum + "';");
        }



       /* if (NO_NEED_SYNC) {//如果是先付或第三方外卖仅需更新报表日期
            sql.append("update cc_order_list set upload_tag=0,bill_num=t.bill_num, report_date=t.report_date from (select bill_num,report_date from pos_bill where order_num='" + orderNum + "') t where order_code='" + orderNum + "';");
            sql.append("update cc_order_item set report_date=(select report_date from pos_bill where order_num='" + orderNum + "' limit 1),upload_tag=0 where order_code='" + orderNum + "';");
        } else {
            sql.append("UPDATE cc_order_list SET bill_num=T.bill_num,order_state = '10', payment_state = '03', total_money = T .total_money, actual_pay = T .actual_pay,shop_fee= T .actual_pay, meal_costs=T.meal_costs,discount_amount = T .discount_amount, discountr_amount=T.discountr_amount, discountk_amount=T.discountk_amount, free_amount=T.free_amount, givi_amount=T.givi_amount, maling_amount=T.maling_amount, report_date=T.report_date, upload_tag = 0 FROM (SELECT A.bill_num,SUM (A .bill_amount) total_money, SUM (A .payment_amount) actual_pay,SUM (A.service_amount) meal_costs, SUM (A.discount_amount) discount_amount, SUM (A.discountr_amount) discountr_amount, SUM (A.discountk_amount) discountk_amount, SUM (A.free_amount) free_amount, SUM (A.givi_amount) givi_amount, SUM (A.maling_amount) maling_amount, A .order_num, A .report_date FROM pos_bill A WHERE A .order_num = '" + orderNum + "' GROUP BY A.bill_num,A .order_num , A .report_date ) T WHERE order_code = T .order_num;");
            sql.append(getOrderItemSql(orderNum));
        }
*/
        sql.append(getOrderRepaymentSql(orderNum));

        ordersDao.execute(tenantId, sql.toString());

        /*if(!NO_NEED_SEND){
            completeSaasOrder(orderNum, tenantId, storeId);
        }*/


        logger.info("[门店结账]成功更新本地订单! >>> " + sql);

    }

    private void completeSaasOrder(String orderNum, String tenantId, int storeId) throws HttpRestException {
        JSONObject msg = new JSONObject();
        msg.put("order_code", orderNum);
        msg.put("chanel", "WX02");
        msg.put("order_state", "10");

        JSONObject reqResultJson = OrderUtil.sendOrderState(tenantId, storeId, Type.ORDER, Oper.update, Arrays.asList(msg));
        if (null != reqResultJson && reqResultJson.optBoolean("success") == false) {
            throw SystemException.getInstance(reqResultJson.optString("msg"), PosErrorCode.ORDER_SEND_ORDER_STATE_ERROR);
        }
    }

    private void cancelSingleDish(String orderNum) throws Exception {
        Map<String, String> systemMap = Constant.getSystemMap();
        String tenantId = systemMap.get("tenent_id");
//		int storeId = Integer.parseInt(systemMap.get("store_id"));

        sql = new StringBuilder();
        sql.append("UPDATE cc_order_list SET total_money = T .total_money, actual_pay = T .actual_pay, shop_fee=T.actual_pay, upload_tag = 0 FROM (SELECT SUM (A .bill_amount) total_money, SUM (A .payment_amount) actual_pay, A .order_num FROM pos_bill A WHERE A .order_num = '" + orderNum + "' GROUP BY A .order_num ) T WHERE order_code = T .order_num;;");
        sql.append(getOrderItemSql(orderNum));
        sql.append(getOrderRepaymentSql(orderNum));

        ordersDao.execute(tenantId, sql.toString());
        logger.info("[单品取消]成功更新本地订单! >>> " + sql);
    }

    private void cancelAllOrder(String orderNum, JSONObject params) throws Exception {
        if (NO_NEED_SYNC) {
            return;
        }

        Map<String, String> systemMap = Constant.getSystemMap();
        String tenantId = systemMap.get("tenent_id");
//		int storeId = Integer.parseInt(systemMap.get("store_id"));

        sql = new StringBuilder();
        sql.append("UPDATE cc_order_list SET upload_tag = 0, order_state = '08', payment_state = '04',cancellation_time=now() WHERE order_code='" + orderNum + "';");
        sql.append("DELETE FROM cc_order_reason_detail where order_code='" + orderNum + "';INSERT INTO cc_order_reason_detail (tenancy_id, order_code, type, reason_type, complaints_time, order_state ) VALUES ('" + tenantId + "', '" + orderNum + "', 'MD03', '" + params.optString("reason_type") + "', '" + DateUtil.getNowDateYYDDMMHHMMSS() + "', '08');");


        ordersDao.execute(tenantId, sql.toString());
        logger.info("[整单取消]成功更新本地订单! >>> " + sql);
    }

    private String getOrderItemSql(String orderNum) {
        StringBuilder sql = new StringBuilder();
        //cc_order_item
            sql.append("DELETE FROM cc_order_item where order_code='" + orderNum + "'; INSERT INTO cc_order_item  (\"tenancy_id\", \"group_index\", \"order_code\", \"item_id\", \"unit_id\", \"number\", \"price\", \"item_name\", \"product_fee\", \"real_amount\", \"discount_amount\", \"single_discount_amount\", \"discountk_amount\", \"discountr_amount\", \"discount_rate\", \"discount_mode_id\", \"method_money\", \"tag\", \"store_id\", \"upload_tag\", \"report_date\",\"activity_id\",\"activity_rule_id\",\"activity_count\") SELECT * FROM(SELECT A .tenancy_id, A .item_serial group_index, b.order_num AS order_code, A .item_id, A .item_unit_id AS unit_id, SUM (A .item_count) \"number\", A .item_price AS price, A .item_name, SUM (A .item_amount) product_fee, SUM (A .real_amount) real_amount, (coalesce(SUM(A.discount_amount),0)+coalesce(SUM(A.discountr_amount),0)) as discount_amount_all, SUM (A .single_discount_amount) single_discount_amount, SUM(A.discount_amount) as discount_amount, SUM (A .discountr_amount) discountr_amount, A .discount_rate, A .discount_mode_id, SUM(A .method_money) method_money, (CASE WHEN SUM (A .item_count) = 0 THEN '退菜' WHEN SUM (A .item_amount) != 0 AND SUM (A .real_amount) = 0 THEN '奉送'END ) AS tag, A .store_id, 0 AS upload_tag, A .report_date,A.activity_id,A.activity_rule_id,A.activity_count FROM pos_bill_item A, pos_bill b WHERE A .bill_num = b.bill_num AND b.order_num = '" + orderNum + "' AND item_property IN ('SINGLE', 'SETMEAL') GROUP BY A .item_id, b.order_num, A .item_serial, A .item_property, A .tenancy_id, A .item_unit_id, A .item_price, A .item_name, A .discount_rate, A .discount_mode_id, A .report_date, A .store_id,A.activity_id,A.activity_rule_id,A.activity_count ORDER BY A .item_serial) t;");
        //cc_order_item_details
        sql.append("DELETE FROM cc_order_item_details where order_code='" + orderNum + "'; INSERT INTO cc_order_item_details (\"tenancy_id\", \"group_index\", \"order_code\", \"item_id\", \"unit_id\", \"number\", \"price\", \"store_id\", \"upload_tag\", \"report_date\") SELECT * FROM (SELECT A .tenancy_id, A .item_serial group_index, b.order_num AS order_code, A .item_id, A .item_unit_id AS unit_id, SUM (A .item_count) \"number\", A .item_price AS price, A .store_id, 0 AS upload_tag, A .report_date FROM pos_bill_item A, pos_bill b WHERE A .bill_num = b.bill_num AND b.order_num = '" + orderNum + "' AND item_property IN ('MEALLIST') GROUP BY A .tenancy_id, A .item_serial, b.order_num, A .item_id, A .item_unit_id, A .item_price, A .store_id, A .report_date ORDER BY A .item_serial ) T;");
        return sql.toString();
    }

    private String getOrderRepaymentSql(String orderNum) {

        return "DELETE FROM cc_order_repayment WHERE ORDER_CODE = '" + orderNum + "'; INSERT INTO cc_order_repayment (tenancy_id, payment_id, order_code, pay_money, pay_no, rexchange_rate, local_currency, remark, fee, rate, upload_tag, report_date, store_id ) SELECT * FROM (SELECT A .tenancy_id, A .jzid payment_id, b.order_num order_code, SUM (A .amount) pay_money, A . NUMBER pay_no, A .rate rexchange_rate, SUM (A .currency_amount) local_currency, A .remark, A .fee, A .fee_rate rate, 0 upload_tag, A .report_date, A .store_id FROM pos_bill_payment A, pos_bill b WHERE A .bill_num = b.bill_num AND b.order_num = '" + orderNum + "' GROUP BY A .tenancy_id, A .jzid, b.order_num, A . NUMBER, A .rate, A .remark, A .fee, A .fee_rate, A .report_date, A .store_id ) T;";
    }

	@Override
	public boolean isSyncOrder(String tenantId, int organId, String orderNum) throws Exception
	{
		try
		{
			if(Tools.isNullOrEmpty(orderNum))
			{
				return false;
			}
			
			String sql = new String("select order_code from cc_order_list where tenancy_id=? and store_id=? and order_code=?");

			List<JSONObject> list = ordersDao.query4Json(tenantId, sql, new Object[]
			{ tenantId, organId, orderNum });

			return (null != list && list.size() > 0);
		}
		catch (Exception e)
		{
			return false;
		}
	}

}
