/**
  * @Description: TODO
  * @author: z<PERSON><PERSON><PERSON>
  * @version: 1.0
  * @Date: 2018年4月3日
*/
package com.tzx.base.service.servlet;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import org.apache.log4j.Logger;



/**
  * @Description: 线程池定义。
  * @param:
  * @author: zhouquan
  * @version: 1.0
  * @Date: 2018年4月3日
*/
public class TheeadPools {

private static final Logger LOGGER = Logger.getLogger(TheeadPools.class);
	
	// 定义数据下发推送消息消费者线程池
	private static volatile ThreadPoolExecutor consumerBaseDataThreadPool = null;
	
	/**
	 * @Description 获取线程池对象（通过双重检查锁实现）。
	 * eg: TheeadPools.getConsumerBaseDataThreadPool().execute(new RunnableThread(param));
	 * @param null
	 * @return ThreadPoolExecutor 线程池对象
	 * @exception null
	 * <AUTHOR> email:<EMAIL>
	 * @version 1.0 2017-12-25
	 * @see null
	 */
	public static ThreadPoolExecutor getConsumerBaseDataThreadPool() {
		if(consumerBaseDataThreadPool == null){
			synchronized (ThreadPoolExecutor.class) {
				if(consumerBaseDataThreadPool == null){
					/*
					 * 创建固定大小为1的线程池，使用ArrayBlockingQueue阻塞队列，队列大小为1个，线程数超过队列大小时的策略为抛弃旧的任务。
					 */
					consumerBaseDataThreadPool = new ThreadPoolExecutor(1,1,3,TimeUnit.SECONDS, new ArrayBlockingQueue<Runnable>(1),
							new ThreadPoolExecutor.DiscardOldestPolicy());
				}
			}
		}
		return consumerBaseDataThreadPool;
	}
}
