package com.tzx.pos.service.notify;

import com.tzx.framework.common.constant.Type;

import java.util.Observable;
import java.util.Observer;

/**
 * <AUTHOR> on 2018-07-01
 */
public abstract class ClientNotifyOberver implements Observer {

    public static String CURRENT_STATE= Type.BOH_NORMAL_RUNNING.name();

    @Override
    public void update(Observable o, Object arg) {
        CURRENT_STATE=((Type)arg).name();
        doNotify((Type)arg);
    }

    public abstract void doNotify(Type arg);
}
