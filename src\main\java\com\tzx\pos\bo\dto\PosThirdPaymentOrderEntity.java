package com.tzx.pos.bo.dto;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

public class PosThirdPaymentOrderEntity implements Serializable
{
	private static final long	serialVersionUID	= 1L;
	
	private String		tenancy_id;
	private Integer		store_id;
	private Integer		id;
	private Date		report_date;
	private Integer		shift_id;
	private String		pos_num;
	private String		opt_num;
	private String		channel;
	private String		object_name;
	private String		service_type;
	private String		order_num;
	private String		aid_order_num;
	private Integer		payment_id;
	private String		payment_class;
	private Double		total_amount	= 0d;
	private Double		settle_amount	= 0d;
	private String		payment_type;
	private String		currency_name;
	private String		subject;
	private String		body;
	private String		client_ip;
	private String		extra;
	private String		description;
	private String		metadata;
	private String		credential;
	private String		transaction_no;
	private String		qr_code;
	private Timestamp	create_time;
	private Timestamp	paid_time;
	private Timestamp	expire_time;
	private Boolean		is_refunded		= false;
	private String		refunds_order;
	private Integer		refunds_id;
	private Double		amount_refunded	= 0d;
	private Boolean		is_succeed		= false;
	private String		status;
	private String		order_state;
	private String		failure_code;
	private String		failure_msg;
	private String		request_status;
	private String		request_code;
	private String		request_msg;
	private Timestamp	query_time;
	private Integer		query_count		= 0;
	private String		remark;
	private String		upload_tag		= "1";
	private String		oper_type;
	private String		pay_type;

	public PosThirdPaymentOrderEntity()
	{
		super();
	}
	
	public PosThirdPaymentOrderEntity(String tenancy_id, Integer store_id, Date report_date, Integer shift_id, String pos_num, String opt_num, String channel, String object_name, String service_type, String order_num, String aid_order_num, Integer payment_id, String payment_class,
			Double total_amount, Double settle_amount, String payment_type, String subject, String body, Timestamp create_time, String status, String order_state)
	{
		super();
		this.tenancy_id = tenancy_id;
		this.store_id = store_id;
		this.report_date = report_date;
		this.shift_id = shift_id;
		this.pos_num = pos_num;
		this.opt_num = opt_num;
		this.channel = channel;
		this.object_name = object_name;
		this.service_type = service_type;
		this.order_num = order_num;
		this.aid_order_num = aid_order_num;
		this.payment_id = payment_id;
		this.payment_class = payment_class;
		this.total_amount = total_amount;
		this.settle_amount = settle_amount;
		this.payment_type = payment_type;
		this.subject = subject;
		this.body = body;
		this.create_time = create_time;
		this.status = status;
		this.order_state = order_state;
	}

	public String getTenancy_id()
	{
		return tenancy_id;
	}

	public void setTenancy_id(String tenancy_id)
	{
		this.tenancy_id = tenancy_id;
	}

	public Integer getStore_id()
	{
		return store_id;
	}

	public void setStore_id(Integer store_id)
	{
		this.store_id = store_id;
	}

	public Integer getId()
	{
		return id;
	}

	public void setId(Integer id)
	{
		this.id = id;
	}

	public Date getReport_date()
	{
		return report_date;
	}

	public void setReport_date(Date report_date)
	{
		this.report_date = report_date;
	}

	public Integer getShift_id()
	{
		return shift_id;
	}

	public void setShift_id(Integer shift_id)
	{
		this.shift_id = shift_id;
	}

	public String getPos_num()
	{
		return pos_num;
	}

	public void setPos_num(String pos_num)
	{
		this.pos_num = pos_num;
	}

	public String getOpt_num()
	{
		return opt_num;
	}

	public void setOpt_num(String opt_num)
	{
		this.opt_num = opt_num;
	}

	public String getChannel()
	{
		return channel;
	}

	public void setChannel(String channel)
	{
		this.channel = channel;
	}

	public String getObject_name()
	{
		return object_name;
	}

	public void setObject_name(String object_name)
	{
		this.object_name = object_name;
	}

	public String getService_type()
	{
		return service_type;
	}

	public void setService_type(String service_type)
	{
		this.service_type = service_type;
	}

	public String getOrder_num()
	{
		return order_num;
	}

	public void setOrder_num(String order_num)
	{
		this.order_num = order_num;
	}

	public String getAid_order_num()
	{
		return aid_order_num;
	}

	public void setAid_order_num(String aid_order_num)
	{
		this.aid_order_num = aid_order_num;
	}

	public Integer getPayment_id()
	{
		return payment_id;
	}

	public void setPayment_id(Integer payment_id)
	{
		this.payment_id = payment_id;
	}

	public String getPayment_class()
	{
		return payment_class;
	}

	public void setPayment_class(String payment_class)
	{
		this.payment_class = payment_class;
	}

	public Double getTotal_amount()
	{
		return total_amount;
	}

	public void setTotal_amount(Double total_amount)
	{
		this.total_amount = total_amount;
	}

	public Double getSettle_amount()
	{
		return settle_amount;
	}

	public void setSettle_amount(Double settle_amount)
	{
		this.settle_amount = settle_amount;
	}

	public String getPayment_type()
	{
		return payment_type;
	}

	public void setPayment_type(String payment_type)
	{
		this.payment_type = payment_type;
	}

	public String getCurrency_name()
	{
		return currency_name;
	}

	public void setCurrency_name(String currency_name)
	{
		this.currency_name = currency_name;
	}

	public String getSubject()
	{
		return subject;
	}

	public void setSubject(String subject)
	{
		this.subject = subject;
	}

	public String getBody()
	{
		return body;
	}

	public void setBody(String body)
	{
		this.body = body;
	}

	public String getClient_ip()
	{
		return client_ip;
	}

	public void setClient_ip(String client_ip)
	{
		this.client_ip = client_ip;
	}

	public String getExtra()
	{
		return extra;
	}

	public void setExtra(String extra)
	{
		this.extra = extra;
	}

	public String getDescription()
	{
		return description;
	}

	public void setDescription(String description)
	{
		this.description = description;
	}

	public String getMetadata()
	{
		return metadata;
	}

	public void setMetadata(String metadata)
	{
		this.metadata = metadata;
	}

	public String getCredential()
	{
		return credential;
	}

	public void setCredential(String credential)
	{
		this.credential = credential;
	}

	public String getTransaction_no()
	{
		return transaction_no;
	}

	public void setTransaction_no(String transaction_no)
	{
		this.transaction_no = transaction_no;
	}

	public String getQr_code()
	{
		return qr_code;
	}

	public void setQr_code(String qr_code)
	{
		this.qr_code = qr_code;
	}

	public Timestamp getCreate_time()
	{
		return create_time;
	}

	public void setCreate_time(Timestamp create_time)
	{
		this.create_time = create_time;
	}

	public Timestamp getPaid_time()
	{
		return paid_time;
	}

	public void setPaid_time(Timestamp paid_time)
	{
		this.paid_time = paid_time;
	}

	public Timestamp getExpire_time()
	{
		return expire_time;
	}

	public void setExpire_time(Timestamp expire_time)
	{
		this.expire_time = expire_time;
	}

	public boolean isIs_refunded()
	{
		return is_refunded;
	}

	public void setIs_refunded(boolean is_refunded)
	{
		this.is_refunded = is_refunded;
	}

	public String getRefunds_order()
	{
		return refunds_order;
	}

	public void setRefunds_order(String refunds_order)
	{
		this.refunds_order = refunds_order;
	}

	public Integer getRefunds_id()
	{
		return refunds_id;
	}

	public void setRefunds_id(Integer refunds_id)
	{
		this.refunds_id = refunds_id;
	}

	public Double getAmount_refunded()
	{
		return amount_refunded;
	}

	public void setAmount_refunded(Double amount_refunded)
	{
		this.amount_refunded = amount_refunded;
	}

	public boolean isIs_succeed()
	{
		return is_succeed;
	}

	public void setIs_succeed(boolean is_succeed)
	{
		this.is_succeed = is_succeed;
	}

	public String getStatus()
	{
		return status;
	}

	public void setStatus(String status)
	{
		this.status = status;
	}

	public String getOrder_state()
	{
		return order_state;
	}

	public void setOrder_state(String order_state)
	{
		this.order_state = order_state;
	}

	public String getFailure_code()
	{
		return failure_code;
	}

	public void setFailure_code(String failure_code)
	{
		this.failure_code = failure_code;
	}

	public String getFailure_msg()
	{
		return failure_msg;
	}

	public void setFailure_msg(String failure_msg)
	{
		this.failure_msg = failure_msg;
	}

	public String getRequest_status()
	{
		return request_status;
	}

	public void setRequest_status(String request_status)
	{
		this.request_status = request_status;
	}

	public String getRequest_code()
	{
		return request_code;
	}

	public void setRequest_code(String request_code)
	{
		this.request_code = request_code;
	}

	public String getRequest_msg()
	{
		return request_msg;
	}

	public void setRequest_msg(String request_msg)
	{
		this.request_msg = request_msg;
	}

	public Timestamp getQuery_time()
	{
		return query_time;
	}

	public void setQuery_time(Timestamp query_time)
	{
		this.query_time = query_time;
	}

	public Integer getQuery_count()
	{
		return query_count;
	}

	public void setQuery_count(Integer query_count)
	{
		this.query_count = query_count;
	}

	public String getRemark()
	{
		return remark;
	}

	public void setRemark(String remark)
	{
		this.remark = remark;
	}

	public String getUpload_tag()
	{
		return upload_tag;
	}

	public void setUpload_tag(String upload_tag)
	{
		this.upload_tag = upload_tag;
	}

	public String getOper_type()
	{
		return oper_type;
	}

	public void setOper_type(String oper_type)
	{
		this.oper_type = oper_type;
	}

	public String getPay_type()
	{
		return pay_type;
	}

	public void setPay_type(String pay_type)
	{
		this.pay_type = pay_type;
	}

}
