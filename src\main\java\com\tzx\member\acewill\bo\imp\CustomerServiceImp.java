package com.tzx.member.acewill.bo.imp;

import com.tzx.base.bo.PosCodeService;
import com.tzx.base.entity.CustomerOperationLogs;
import com.tzx.framework.common.constant.Code;
import com.tzx.framework.common.constant.CustomerOperationEnum;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.Tools;
import com.tzx.member.acewill.bo.CustomerService;
import com.tzx.member.common.util.AcewillRequestUtil;
import com.tzx.member.common.util.AcewillUtil;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.base.util.JsonUtil;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.thirdpay.bo.ThirdPaymentService;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service(CustomerService.NAME)
public class CustomerServiceImp extends AcewillCustomerServiceImp implements CustomerService {

	private static final Logger logger = Logger.getLogger(CustomerService.class);
	
    @Resource(name = PosCodeService.NAME)
    protected PosCodeService			codeService;

    @Resource(name = ThirdPaymentService.NAME)
    protected ThirdPaymentService	thirdPaymentService;

//    @Resource(name = CustomerCardRechargeService.NAME)
//    CustomerCardRechargeService  customerCardRechargeService;

//    @Resource(name = PosPaymentDao.NAME)
//    protected PosPaymentDao			paymentDao;

    /**
     * 储值支付方式类型
     */
    private final String opt_type_default = "charge";

    /**
     * 有效期常量
     */
    private final String expired_default = "永久有效";


    /**
     * 门店信息
     */
    private static Map<String,JSONObject> cashierMap = new HashMap<>();

    /**
     * 门店信息
     */
    private static Map<String,JSONObject> shopsMap = new HashMap<>();

    private static String open_card_bizid="";

	@Override
	public String createThirdBillCode(String tenancyId, Integer storeId, JSONObject paramJson) throws Exception
	{
		String reportDateStr = ParamUtil.getStringValueByObject(paramJson, "report_date", true, PosErrorCode.NOT_NULL_REPORT_DATE);
		String billCode = "";
		try
		{
			// 生成新的账单号和流水单号
			JSONObject object = new JSONObject();
			object.element("store_id", storeId);
			object.element("busi_date", reportDateStr);
			billCode = codeService.getCode(tenancyId, Code.POS_THIRD_PAYMENT_ORDER_NUM, object);// 调用统一接口来实现
			if (Tools.isNullOrEmpty(billCode))
			{
				throw new SystemException(PosErrorCode.GET_BILLNUM_SERVICE_ERROR);
			}
		}
		catch (Exception e)
		{
			logger.error("会员充值：" + ExceptionMessage.getExceptionMessage(e));
			throw new SystemException(PosErrorCode.GET_BILLNUM_SERVICE_ERROR);
		}
		return billCode;
	}

    /**
     * 会员管理  会员查询  修改会员资料
     * @param requestData
     * @return
     * @throws Exception
     */
    @Override
    public Data editAcewillCustomerUserInfo(Data requestData) throws Exception {

//        String requestUrl = getPequestUrl(EDIT_CUSTOMER_PHONE_URL);
        String tenancyId = requestData.getTenancy_id();
        Integer storeId = requestData.getStore_id();

        Data resData = new Data();
        JSONObject jsonO = JSONObject.fromObject(requestData.getData().get(0));
        StringBuffer sbContent = new StringBuffer();
        JSONObject jsonParam = new JSONObject();
        String carCode = jsonO.optString("card_code");
        sbContent.append("修改["+carCode+"]会员信息，修改后为 卡号:"+ carCode);
        // 组织接口参数
        jsonParam.put("cno", carCode);
        String mobil = jsonO.optString("mobil");
        sbContent.append("，手机号："+mobil);
        String sex = jsonO.optString("sex");
        sbContent.append("，性别："+sex);
        if (Tools.hv(sex)) {
            if ("man".equals(sex)) {
                jsonParam.put("gender", 1);
            } else if ("woman".equals(sex)) {
                jsonParam.put("gender", 2);
            }
        }
        String name = jsonO.optString("name");
        jsonParam.put("username", name);
        sbContent.append("，姓名："+name);
        String birthday = jsonO.optString("birthday");
        jsonParam.put("birth", birthday);
        sbContent.append("，生日："+birthday +"。");

        if (Tools.hv(mobil)) {
            JSONObject phoneJson = new JSONObject();
            phoneJson.put("cno", carCode);
            phoneJson.put("phone", mobil);
            List<JSONObject> data = new ArrayList<JSONObject>();
            data.add(phoneJson);
            Data paramData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
            paramData.setData(data);
            paramData.setType(requestData.getType());
            paramData.setT(requestData.getT());
            paramData.setOper(requestData.getOper());
            //修改手机号
            try {
                resData = this.commonPost(paramData, AcewillRequestUtil.EDIT_CUSTOMER_PHONE_URL);
                sbContent.append("调用接口修改手机号成功");
            } catch (Exception e) {
                logger.info("修改会员手机号请求失败" + e.getLocalizedMessage());
                sbContent.append("调用接口修改手机号失败,"+e.getLocalizedMessage());
                resData = Data.get();
                resData.setMsg("修改会员手机号求失败");
                resData.setCode(Constant.CODE_INNER_EXCEPTION);
                resData.setSuccess(false);
            }

            logger.info("修改会员手机号返回数据" + JsonUtil.DataToJson(resData));

            if (Constant.CODE_SUCCESS == resData.getCode()) {
                data = new ArrayList<JSONObject>();
                data.add(jsonParam);
                paramData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
                paramData.setData(data);
                paramData.setType(requestData.getType());
                paramData.setT(requestData.getT());
                paramData.setOper(requestData.getOper());
                try {
//                    requestUrl = getPequestUrl(EDIT_CUSTOMER_USER_URL);
                    //修改会员信息
                    resData = this.commonPost(paramData, AcewillRequestUtil.EDIT_CUSTOMER_USER_URL);
                    sbContent.append(",调用接口修改会员信息成功");
                } catch (Exception e) {
                    logger.info("修改会员信息请求失败" + e.getLocalizedMessage());
                    sbContent.append(",调用接口修改会员信息失败,"+e.getLocalizedMessage());
                    resData = Data.get();
                    resData.setMsg("修改会员信息求失败");
                    resData.setCode(Constant.CODE_INNER_EXCEPTION);
                    resData.setSuccess(false);
                }
                logger.info("修改会员信息返回数据" + JsonUtil.DataToJson(resData));
            }
        } else {
            List<JSONObject> data = new ArrayList<JSONObject>();
            data.add(jsonParam);
            Data paramData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
            paramData.setData(data);
            paramData.setType(requestData.getType());
            paramData.setT(requestData.getT());
            paramData.setOper(requestData.getOper());
            try {
                logger.info("修改会员信息传递参数  "+JsonUtil.DataToJson(paramData));
//                requestUrl = getPequestUrl(EDIT_CUSTOMER_USER_URL);
                //修改会员信息
                resData = this.commonPost(paramData, AcewillRequestUtil.EDIT_CUSTOMER_USER_URL);
                sbContent.append(",调用接口修改会员信息成功");
            } catch (Exception e) {
                logger.info("修改会员信息请求失败" + e.getLocalizedMessage());
                sbContent.append(",调用接口修改会员信息失败,"+e.getLocalizedMessage());
                resData = Data.get();
                resData.setMsg("修改会员信息求失败");
                resData.setCode(Constant.CODE_INNER_EXCEPTION);
                resData.setSuccess(false);
            }
        }

        try {
            CustomerOperationLogs operationLogs = new CustomerOperationLogs();
            operationLogs.setContent(sbContent.toString());
            operationLogs.setCard_code(carCode);
            operationLogs.setTitle(CustomerOperationEnum.EDIT_USER.getTitle());
            operationLogs.setOperation_type(CustomerOperationEnum.EDIT_USER.getType());
            operationLogs.setOperation_type_name(CustomerOperationEnum.EDIT_USER.getValue());
            operationLogs.setOperation_id(jsonO.getString("opt_num"));
            operationLogs.setShift_id(jsonO.getString("shift_id"));
            operationLogs.setReport_date(jsonO.getString("report_date"));
            operationLogs.setPos_num(jsonO.getString("pos_num"));
            operationLogs.setTenancy_id(tenancyId);
            operationLogs.setStore_id(storeId);
            this.saveCustomerOperationLog(tenancyId,operationLogs);
        }catch (Exception e){
            e.printStackTrace();
        }


        logger.info("修改会员信息返回数据" + JsonUtil.DataToJson(resData));
        return resData;
    }


    @Override
    public Data findAcewillChargeRule(Data requestData) throws Exception {

//        String requestUrl = getPequestUrl(FIND_ACEWILL_CHARGE_RULE_URL);
        String tenancyId = requestData.getTenancy_id();
        Integer storeId = requestData.getStore_id();
        Data resData = new Data();
        // 组织接口参数
        JSONObject jsonParam = new JSONObject();
        jsonParam.put("shop_id", getShopId(tenancyId, storeId));

        // 请求acewill接口
        List<JSONObject> data = new ArrayList<JSONObject>();
        data.add(jsonParam);
        Data paramData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
        paramData.setData(data);
        paramData.setType(requestData.getType());
        paramData.setT(requestData.getT());
        paramData.setOper(requestData.getOper());
        try {
            //查询门店储值设置
            resData = this.commonPost(paramData, AcewillRequestUtil.FIND_ACEWILL_CHARGE_RULE_URL);

            if(Constant.CODE_SUCCESS == resData.getCode()){
                JSONObject resultJson = JSONObject.fromObject(resData.getData().get(0));
                if(null!=resultJson && !resultJson.isEmpty()){
                    String expired = resultJson.optString("expired");//有效期
                    if(!expired_default.equals(expired)){
                        Date expired_date = DateUtil.parseDate(expired);
                        Date now =new Date();
                        if(expired_date.before(now)){
                            logger.info("有效的储值规则不存在");
                            resData = Data.get();
                            resData.setMsg("有效的储值规则不存在");
                            resData.setCode(Constant.CODE_INNER_EXCEPTION);
                            resData.setSuccess(false);
                        }
                    }
                }
            }

        } catch (Exception e) {
            logger.info("查询门店储值设置请求失败" + e.getLocalizedMessage());
            resData = Data.get();
            resData.setMsg("查询门店储值设置求失败");
            resData.setCode(Constant.CODE_INNER_EXCEPTION);
            resData.setSuccess(false);
        }
        return resData;
    }


    /**
     * 收银员列表
     * @param requestData
     * @return
     * @throws Exception
     */
    @Override
    public Data findAcewillCashierList(Data requestData) throws Exception {
//        String requestUrl = getPequestUrl(FIND_ACEWILL_CASHIER_LIST_URL);
        String tenancyId = requestData.getTenancy_id();
        Integer storeId = requestData.getStore_id();
        Data resData = new Data();
        // 组织接口参数
        JSONObject jsonParam = new JSONObject();
        jsonParam.put("shop_id", getShopId(tenancyId, storeId));
//        JSONObject jsonO = JSONObject.fromObject(requestData.getData().get(0));
//        if (jsonO.containsKey("page")) {
//            jsonParam.put("page", jsonO.optInt("page"));
//        }

        // 请求acewill接口
        List<JSONObject> data = new ArrayList<JSONObject>();
        data.add(jsonParam);
        Data paramData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
        paramData.setData(data);
        paramData.setType(requestData.getType());
        paramData.setT(requestData.getT());
        paramData.setOper(requestData.getOper());
        try {
            //查询门店储值设置
            resData = this.commonPost(paramData, AcewillRequestUtil.FIND_ACEWILL_CASHIER_LIST_URL);
            if(Constant.CODE_SUCCESS == resData.getCode()){
                if(cashierMap.isEmpty()){
                    JSONArray  cashiers = JSONArray.fromObject(resData.getData());
                    for(int i = 0;i<cashiers.size();i++){
                        JSONObject cashier = JSONObject.fromObject(cashiers.get(i));
                        cashierMap.put(cashier.optString("cashier_id"),cashier);
                    }
                }
            }
        } catch (Exception e) {
            logger.info("获取收银员列表请求失败" + e.getLocalizedMessage());
            resData = Data.get();
            resData.setMsg("获取收银员列表设置求失败");
            resData.setCode(Constant.CODE_INNER_EXCEPTION);
            resData.setSuccess(false);
        }
        logger.info("获取收银员列表返回数据" + JsonUtil.DataToJson(resData));
        return resData;
    }
    
    @Override
    public Data findAcewillEmployeeList(Data requestData) throws Exception
    {
    	String tenancyId = requestData.getTenancy_id();
    	Integer storeId = requestData.getStore_id();
    	Data resData = new Data();

    	try {
    		// 组织接口参数
    		JSONObject jsonParam = new JSONObject();
    		jsonParam.put("shop_id", getShopId(tenancyId, storeId));

    		// 请求acewill接口
    		List<JSONObject> data = new ArrayList<JSONObject>();
    		data.add(jsonParam);
    		Data paramData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
    		paramData.setData(data);
    		paramData.setType(requestData.getType());
    		paramData.setT(requestData.getT());
    		paramData.setOper(requestData.getOper());

    		//获取门店员工
    		resData = this.commonPost(paramData, AcewillRequestUtil.FIND_ACEWILL_EMPLOYEE_LIST_URL);
    	} catch (Exception e) {
    		logger.info("获取门店员工列表请求失败" + e.getLocalizedMessage());
    		resData = Data.get();
    		resData.setMsg("获取门店员工列表设置求失败");
    		resData.setCode(Constant.CODE_INNER_EXCEPTION);
    		resData.setSuccess(false);
    	}
    	logger.info("获取门店员工列表返回数据" + JsonUtil.DataToJson(resData));
    	return resData;
    }

    /**
     * 获取支付方式
     * @param requestData
     * @return
     * @throws Exception
     */
    @Override
    public Data findAcewillDealPayType(Data requestData) throws Exception {
//        String requestUrl = getPequestUrl(FIND_ACEWILL_DEAL_PAY_TYPE_URL);
        String tenancyId = requestData.getTenancy_id();
        Integer storeId = requestData.getStore_id();
        Data resData = new Data();

        // 组织接口参数
        JSONObject jsonParam = new JSONObject();
        jsonParam.put("optype", opt_type_default);

        // 请求acewill接口
        List<JSONObject> data = new ArrayList<JSONObject>();
        data.add(jsonParam);
        Data paramData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
        paramData.setData(data);
        paramData.setType(requestData.getType());
        paramData.setT(requestData.getT());
        paramData.setOper(requestData.getOper());
        try {
            //查询门店储值设置
            resData = this.commonPost(paramData, AcewillRequestUtil.FIND_ACEWILL_DEAL_PAY_TYPE_URL);
        } catch (Exception e) {
            logger.info("获取储值支付方式请求失败" + e.getLocalizedMessage());
            resData = Data.get();
            resData.setMsg("获取储值支付方式请求失败");
            resData.setCode(Constant.CODE_INNER_EXCEPTION);
            resData.setSuccess(false);
        }

        if(Constant.CODE_SUCCESS == resData.getCode()){
            JSONObject json = JSONObject.fromObject(resData.getData().get(0));
            Map<String, String>  map = (Map<String, String>)json;
            data = new ArrayList<JSONObject>();
            for(Map.Entry<String, String>  entry : map.entrySet()){
                JSONObject payJson = new JSONObject();
                payJson.put("id",entry.getKey());
                payJson.put("name",entry.getValue());
                data.add(payJson);
            }
            resData.setData(data);
        }
        logger.info("获取储值支付方式返回数据" + JsonUtil.DataToJson(resData));
        return resData;
    }

    /**
     * 获取会员卡号
     *
     * @param requestData
     * @return
     */
    private String getCarCode(Data requestData) {
//        String requestUrl = getPequestUrl(CUSTOMER_USER_QUERY_URL);
        Data resData = new Data();
        JSONObject jsonO = JSONObject.fromObject(requestData.getData().get(0));
        String cno = "";


        JSONObject jsonParam = new JSONObject();
        jsonParam.put("cno", jsonO.optString("card_code"));
        List<JSONObject> data = new ArrayList<JSONObject>();
        data.add(jsonParam);
        requestData.setData(data);
        logger.info("获取用户账户信息请求参数" + JsonUtil.DataToJson(requestData));
        try {
            //查询会员信息
            resData = this.commonPost(requestData, AcewillRequestUtil.CUSTOMER_USER_QUERY_URL);
        } catch (Exception e) {
            cno = "";
            e.printStackTrace();
        }

        if (Constant.CODE_SUCCESS == resData.getCode()) {
            JSONArray jsonArray = JSONArray.fromObject(resData.getData());
            JSONObject jsonCon = null;
            for (int i = 0; i < jsonArray.size(); i++) {
                try {
                    jsonCon = JSONObject.fromObject(jsonArray.get(i));
                } catch (Exception e) {
                    cno = "";
                    e.printStackTrace();
                }
                if (jsonCon != null) {
                    cno = ParamUtil.getStringValueForNullByObject(jsonCon, "cno"); // 会员卡号
                }
            }
        }
        logger.info("获取用户账户会员卡号返回" + JsonUtil.DataToJson(resData));
        return cno;
    }



    public void findAcewillShoplist(Data requestData) throws Exception {
//        String requestUrl = getPequestUrl(ACEWILL_SHOP_LIST_URL);
        String tenancyId = requestData.getTenancy_id();
        Integer storeId = requestData.getStore_id();
        Data resData = new Data();

        JSONObject paramJson = JSONObject.fromObject(requestData.getData().get(0));

        JSONObject jsonParam = new JSONObject();
        // 请求acewill接口
        List<JSONObject> data = new ArrayList<JSONObject>();
        data.add(jsonParam);
        Data paramData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
        paramData.setData(data);
        paramData.setType(requestData.getType());
        paramData.setT(requestData.getT());
        paramData.setOper(requestData.getOper());
        try {
            //门店列表
            resData = this.commonPost(paramData, AcewillRequestUtil.ACEWILL_SHOP_LIST_URL);
            if(Constant.CODE_SUCCESS == resData.getCode()){
                JSONArray result = JSONArray.fromObject(resData.getData());
                if(result!=null && !result.isEmpty()){
                    if(shopsMap.isEmpty()){
                        for(int i=0;i<result.size();i++){
                            JSONObject resultJson = JSONObject.fromObject(result.get(i));
                            String shop_id = ParamUtil.getStringValueForNullByObject(resultJson, "shop_id");
                            shopsMap.put(shop_id,resultJson);
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.info("查询门店列表请求失败" + e.getMessage());
        }
        logger.info("查询门店列表返回数据" + JsonUtil.DataToJson(resData));
    }


    public Data findAcewillChargeUser(Data requestData) throws Exception {
//        String requestUrl = getPequestUrl(FIND_ACEWILL_CHARGE_USER);
        String tenancyId = requestData.getTenancy_id();
        Integer storeId = requestData.getStore_id();
        Data resData = requestData.clone();
        JSONObject jsonO = JSONObject.fromObject(requestData.getData().get(0));
        //String cno = this.getCarCode(requestData);
        String cno = jsonO.optString("card_code");

        if (Tools.hv(cno)) {
            // 组织接口参数
            JSONObject jsonParam = new JSONObject();
            jsonParam.put("cno", cno);
            jsonParam.put("begin_date", "1900-01-01");
            jsonParam.put("end_date",  DateUtil.getNowDateYYDDMM());
            //jsonParam.put("shop_id", getShopId(tenancyId, storeId));  //是否本地消费

            // 请求acewill接口
            List<JSONObject> data = new ArrayList<JSONObject>();
            data.add(jsonParam);
            Data paramData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
            paramData.setData(data);
            paramData.setType(requestData.getType());
            paramData.setT(requestData.getT());
            paramData.setOper(requestData.getOper());

            List<JSONObject> dataList = new ArrayList<JSONObject>();
            try {
                //指定会员储值记录列表
                Data resultData = this.commonPost(paramData, AcewillRequestUtil.FIND_ACEWILL_CHARGE_USER);
                JSONObject jsonObj = null;
                if(Constant.CODE_SUCCESS==resultData.getCode()){
                    logger.info("微生活会员储值记录接口 返回数据 ==="+JsonUtil.DataToJson(resultData));

                    if(shopsMap.isEmpty()){
                        findAcewillShoplist(requestData);
                    }

                    JSONObject dataObj = JSONObject.fromObject(resultData.getData().get(0));
                    JSONArray jsonArray  = JSONArray.fromObject(dataObj.getJSONArray("data"));

//                    Map<String,JSONObject> tradingMap = this.getCrmCardTradingListByCardcode(tenancyId, storeId, cno);
                    
                    for(int i = 0;i<jsonArray.size();i++){
                        JSONObject resultJson = JSONObject.fromObject(jsonArray.get(i));
                        String sid = ParamUtil.getStringValueByObject(resultJson, "sid");
                        String thirdBillCode = ParamUtil.getStringValueForNullByObject(resultJson, "biz_id");
                        
                        jsonObj = new JSONObject();
                        jsonObj.put("charge_id", ParamUtil.getStringValueForNullByObject(resultJson, "charge_id")); // 储值流水
                        jsonObj.put("card_code", ParamUtil.getStringValueForNullByObject(resultJson, "cno")); // 会员卡号

                        jsonObj.put("org_id",sid); // 储值门店id
                        jsonObj.put("org_name", ""); // 储值门店名称
                        if(shopsMap.containsKey(sid)){
                            JSONObject shopJson = shopsMap.get(sid);
                            jsonObj.put("org_name", shopJson.optString("shop_name")); // 储值门店名称
                        }
                        jsonObj.put("charge_total", AcewillUtil.changeF2Y(ParamUtil.getIntegerValueByObject(resultJson, "total_fee"))); // 充值金额
                        jsonObj.put("money", AcewillUtil.changeF2Y(ParamUtil.getIntegerValueByObject(resultJson, "fee"))); // 充值金额
                        jsonObj.put("gift", AcewillUtil.changeF2Y(ParamUtil.getIntegerValueByObject(resultJson, "award_fee"))); // 充值金额
                        jsonObj.put("award_coupons", resultJson.get("award_coupons")); // 赠送券

                        jsonObj.put("type", AcewillUtil.getChargeType(ParamUtil.getIntegerValueByObject(resultJson, "type"))); // 交易类型

                        jsonObj.put("pay_time", ParamUtil.getStringValueForNullByObject(resultJson, "pay_time")); // 交易时间

                        Integer pay_type = ParamUtil.getIntegerValueByObject(resultJson, "pay_type");

//                        //微生活支付方式  转换成  tzx支付方式
//						String paymentClass = PaymentWayEnum.findValue(String.valueOf(pay_type));
//						Integer paymentId = 0;
//						if (null != tradingMap && tradingMap.containsKey(thirdBillCode))
//						{
//							// 查询本地充值记录,优先获取本地记录付款方式
//							paymentId = ParamUtil.getIntegerValueByObject(tradingMap.get(thirdBillCode), "payment_id");
//						}
//						else if (SysDictionary.PAYMENT_CLASS_CASH.equals(paymentClass))
//						{
//							JSONObject paymentWayJson = customerDao.getPaymentWayForStandard(tenancyId, storeId);
//							if (null != paymentWayJson && !paymentWayJson.isEmpty())
//							{
//								paymentId = ParamUtil.getIntegerValueByObject(paymentWayJson, "payment_id");
//							}
//						}
//
//						if (null == paymentId || 0 == paymentId.intValue())
//						{
//							JSONObject paymentWayJson = customerDao.getPaymentWayByPaymentClass(tenancyId, storeId, paymentClass);
//							if (null != paymentWayJson && !paymentWayJson.isEmpty())
//							{
//								paymentId = ParamUtil.getIntegerValueByObject(paymentWayJson, "payment_id");
//							}
//						}
                        
                        jsonObj.put("charge_type", pay_type); // 交易方式
                        jsonObj.put("charge_type_name", AcewillUtil.getPayType(pay_type)); // 交易方式名称
                        jsonObj.put("has_receipt",ParamUtil.getIntegerValueByObject(resultJson, "has_receipt")); // 是否开票  1:开票 2:未开票 0:未开票
                        String cashier_id = ParamUtil.getStringValueByObject(resultJson, "cashier_id");
                        jsonObj.put("cashier_id",cashier_id); // 收银员id
                        String cashier_name ="默认收银员";
                        if(cashierMap.containsKey(cashier_id)){
                            JSONObject cashier = cashierMap.get(cashier_id);
                            cashier_name = cashier.optString("username");
                        }
                        jsonObj.put("cashier_name",cashier_name); // 收银员名称
                        jsonObj.put("remark",ParamUtil.getStringValueForNullByObject(resultJson, "remark")); // 备注
                        jsonObj.put("grade",ParamUtil.getIntegerValueByObject(resultJson, "grade")); // 用户等级id
                        jsonObj.put("grade_name",ParamUtil.getStringValueForNullByObject(resultJson, "grade_name")); // 用户等级名称
                        jsonObj.put("biz_id",thirdBillCode);
                        dataList.add(jsonObj);
                    }
                    resData.setData(dataList);
                }


            } catch (Exception e) {
                e.printStackTrace();
                logger.info("获取会员储值记录列表请求失败" + e.getMessage());
                resData = Data.get();
                resData.setMsg("获取会员储值记录列表请求失败");
                resData.setCode(Constant.CODE_INNER_EXCEPTION);
                resData.setSuccess(false);
            }
        } else {
            logger.info("根据输入手机号、卡号查找会员，会员卡号为空");
            resData = Data.get();
            resData.setMsg("获取会员卡号为空");
            resData.setCode(Constant.CODE_INNER_EXCEPTION);
            resData.setSuccess(false);
        }
        return resData;
    }

    @Override
    public Data findAcewillConsumeUser(Data requestData) throws Exception {
//        String requestUrl = getPequestUrl(FIND_ACEWILL_CONSUME_USER);
        String tenancyId = requestData.getTenancy_id();
        Integer storeId = requestData.getStore_id();
        Data resData = requestData.clone();
        JSONObject jsonO = JSONObject.fromObject(requestData.getData().get(0));
        //String cno = this.getCarCode(requestData);
        String cno = jsonO.optString("card_code");
        if (Tools.hv(cno)) {
            String begin_date = jsonO.optString("begin_date");
            String end_date = jsonO.optString("end_date");


            // 组织接口参数
            JSONObject jsonParam = new JSONObject();
            jsonParam.put("cno", cno);
            if(Tools.hv(begin_date)){
                jsonParam.put("begin_date", begin_date);
            }else{
                jsonParam.put("begin_date", "1900-01-01");
            }

            if(Tools.hv(end_date)){
                jsonParam.put("end_date", end_date);
            }else{
                jsonParam.put("end_date", DateUtil.getNowDateYYDDMM());
            }


            //jsonParam.put("shop_id", getShopId(tenancyId, storeId));

            // 请求acewill接口
            List<JSONObject> data = new ArrayList<JSONObject>();
            data.add(jsonParam);
            Data paramData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
            paramData.setData(data);
            paramData.setType(requestData.getType());
            paramData.setT(requestData.getT());
            paramData.setOper(requestData.getOper());
            List<JSONObject> dataList = new ArrayList<JSONObject>();
            try {
                //指定会员消费记录列表
                Data resultData = this.commonPost(paramData, AcewillRequestUtil.FIND_ACEWILL_CONSUME_USER);
                JSONObject jsonObj = null;
                if(Constant.CODE_SUCCESS==resultData.getCode()){

                    if(shopsMap.isEmpty()){
                        findAcewillShoplist(requestData);
                    }
                    logger.info("微生活会员消费记录接口 返回数据 ==="+JsonUtil.DataToJson(resultData));

                    JSONObject dataObj = JSONObject.fromObject(resultData.getData().get(0));
                    JSONArray jsonArray  = JSONArray.fromObject(dataObj.getJSONArray("data"));

                    for(int i = 0;i<jsonArray.size();i++){
                        JSONObject resultJson = JSONObject.fromObject(jsonArray.get(i));
                        jsonObj = new JSONObject();
                        jsonObj.put("deal_id", ParamUtil.getStringValueByObject(resultJson, "deal_id")); // 储值流水
                        jsonObj.put("card_code", ParamUtil.getStringValueForNullByObject(resultJson, "cno")); // 会员卡号

                        String sid = ParamUtil.getStringValueByObject(resultJson, "sid");


                        jsonObj.put("org_id",sid); // 储值门店id
                        jsonObj.put("org_name", ""); // 储值门店名称
                        if(shopsMap.containsKey(sid)){
                            JSONObject shopJson = shopsMap.get(sid);
                            jsonObj.put("org_name", shopJson.optString("shop_name")); // 储值门店名称
                        }

                        jsonObj.put("charge_total", AcewillUtil.changeF2Y(ParamUtil.getIntegerValueByObject(resultJson, "total_fee"))); // 消费总金额
                        jsonObj.put("money", AcewillUtil.changeF2Y(ParamUtil.getIntegerValueByObject(resultJson, "fee"))); // 实收金额
                        jsonObj.put("stored_pay", AcewillUtil.changeF2Y(ParamUtil.getIntegerValueByObject(resultJson, "stored_pay"))); // 使用储值支付金额
                        jsonObj.put("stored_sale_pay", AcewillUtil.changeF2Y(ParamUtil.getIntegerValueByObject(resultJson, "stored_sale_pay"))); // 使用实际储值支付金额
                        jsonObj.put("cash_coupon_pay", AcewillUtil.changeF2Y(ParamUtil.getIntegerValueByObject(resultJson, "cash_coupon_pay"))); // 使用代金券抵扣金额
                        jsonObj.put("gift_coupon_pay", AcewillUtil.changeF2Y(ParamUtil.getIntegerValueByObject(resultJson, "gift_coupon_pay"))); // 使用礼品券抵扣金额
                        jsonObj.put("credit_num", ParamUtil.getIntegerValueByObject(resultJson, "credit_num")); // 使用积分数量
                        jsonObj.put("credit_award", ParamUtil.getIntegerValueByObject(resultJson, "credit_award")); //奖励积分数量
                        jsonObj.put("credit_pay", AcewillUtil.changeF2Y(ParamUtil.getIntegerValueByObject(resultJson, "credit_pay"))); // 使用积分抵扣金额
                        jsonObj.put("type", AcewillUtil.getConsumeType(ParamUtil.getIntegerValueByObject(resultJson, "type"))); // 交易类型
                        jsonObj.put("pay_time", ParamUtil.getStringValueForNullByObject(resultJson, "pay_time")); // 交易时间
                        jsonObj.put("pay_type", ParamUtil.getIntegerValueByObject(resultJson, "pay_type")); //消费支付类型
                        jsonObj.put("pay_type_name", AcewillUtil.getCoumerPayType(ParamUtil.getIntegerValueByObject(resultJson, "pay_type"))); // 消费支付类型名称
                        jsonObj.put("remark",ParamUtil.getStringValueForNullByObject(resultJson, "remark")); // 收银员id
                        jsonObj.put("biz_id",ParamUtil.getStringValueForNullByObject(resultJson, "biz_id")); // 用户等级名称
                        dataList.add(jsonObj);
                    }

                    resData.setData(dataList);
                }
            } catch (Exception e) {
                logger.info("获取会员消费记录列表请求失败" + e.getLocalizedMessage());
                resData = Data.get();
                resData.setMsg("获取会员消费记录列表请求失败");
                resData.setCode(Constant.CODE_INNER_EXCEPTION);
                resData.setSuccess(false);
            }
        } else {
            logger.info("根据输入手机号、卡号查找会员，会员卡号为空");
            resData = Data.get();
            resData.setMsg("获取会员卡号为空");
            resData.setCode(Constant.CODE_INNER_EXCEPTION);
            resData.setSuccess(false);
        }
        return resData;
    }





    @Override
    public Data findAcewillCreditRule(Data requestData) throws Exception {

//        String requestUrl = getPequestUrl(FIND_ACEWILL_CREDIT_RULE_URL);
        String tenancyId = requestData.getTenancy_id();
        Integer storeId = requestData.getStore_id();
        Data resData = new Data();

        // 请求acewill接口
        List<JSONObject> data = new ArrayList<JSONObject>();
        Data paramData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
        data.add(new JSONObject());
        paramData.setData(data);
        paramData.setType(requestData.getType());
        paramData.setT(requestData.getT());
        paramData.setOper(requestData.getOper());
        List<JSONObject> dataList = new ArrayList<JSONObject>();
        try {
            //查看积分换礼规则
            resData = this.commonPost(paramData, AcewillRequestUtil.FIND_ACEWILL_CREDIT_RULE_URL);
            logger.info("积分换礼规则返回数据  "+JsonUtil.DataToJson(resData));

            if (Constant.CODE_SUCCESS == resData.getCode()) {
                JSONObject jsonCon = JSONObject.fromObject(resData.getData().get(0));
                JSONObject jsonObj = null;
                if (jsonCon != null) {
                    logger.info("接口返回jsonCon===  " +jsonCon);
                    JSONObject consumeJson = jsonCon.getJSONObject("consume");
                    jsonObj = new JSONObject();
                    jsonObj.put("openflag", ParamUtil.getIntegerValueByObject(consumeJson, "credit_gift_openflag")); //积分换礼是否开启 1.开启 2.关闭
                    jsonObj.put("desc", consumeJson.optString("credit_gift_desc"));//积分换礼描述
                    jsonObj.put("rule", consumeJson.getJSONArray("credit_gift_rule"));
                    dataList.add(jsonObj);
                }
            }

        } catch (Exception e) {
            logger.info("查看积分换礼规则请求失败" + e.getLocalizedMessage());
            resData = Data.get();
            resData.setMsg("查看积分换礼规则请求求失败");
            resData.setCode(Constant.CODE_INNER_EXCEPTION);
            resData.setSuccess(false);
        }
        resData.setData(dataList);
        logger.info("换礼规则返回数据" + JsonUtil.DataToJson(resData));
        return resData;
    }

    @Override
    public Data creditExchangeGifts(Data requestData) throws Exception {

//        String requestUrl = getPequestUrl(CREDIT_EXCHANGE_GIFTS_URL);
        String tenancyId = requestData.getTenancy_id();
        Integer storeId = requestData.getStore_id();
        Data resData = requestData.clone();
        JSONObject jsonO = JSONObject.fromObject(requestData.getData().get(0));
        String optNum = ParamUtil.getStringValueByObject(jsonO, "opt_num"); 
        String ts = String.valueOf(DateUtil.currentTimestamp().getTime());
        // 组织接口参数
        JSONObject jsonParam = new JSONObject();
        jsonParam.put("cno", jsonO.optString("card_code"));
        jsonParam.put("sub_credit", jsonO.optInt("sub_credit"));
        //数组[{“gift”:”123礼品卡”,”aid”:3033220,”num”:1}]
        jsonParam.put("desc", jsonO.getJSONArray("desc"));
        String billCode = this.createThirdBillCode(tenancyId, storeId, jsonO);
        jsonParam.put("biz_id",billCode);
        jsonParam.put("shop_id", getShopId(tenancyId, storeId));
        jsonParam.put("cashier_id", this.getCashierId(tenancyId, storeId, optNum));
        // 请求acewill接口
        List<JSONObject> data = new ArrayList<JSONObject>();
        data.add(jsonParam);
        Data paramData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
        paramData.setData(data);
        paramData.setType(requestData.getType());
        paramData.setT(requestData.getT());
        paramData.setOper(requestData.getOper());

        try {
            resData = this.commonPost(paramData, AcewillRequestUtil.CREDIT_EXCHANGE_GIFTS_URL);
        } catch (Exception e) {
            logger.info("积分换礼请求失败" + e.getMessage());
            resData = Data.get();
            resData.setMsg("积分换礼请求失败");
            resData.setCode(Constant.CODE_INNER_EXCEPTION);
            resData.setSuccess(false);
        }

        try {
            CustomerOperationLogs operationLogs = new CustomerOperationLogs();
            operationLogs.setTenancy_id(tenancyId);
            operationLogs.setStore_id(storeId);
            operationLogs.setReport_date(ParamUtil.getStringValueByObject(jsonO,"report_date"));
            operationLogs.setOperation_type(CustomerOperationEnum.CREDIT_EXCHANGE.getType());
            operationLogs.setOperation_type_name(CustomerOperationEnum.CREDIT_EXCHANGE.getValue());
            operationLogs.setTitle(CustomerOperationEnum.CREDIT_EXCHANGE.getTitle());
            operationLogs.setPos_num(ParamUtil.getStringValueByObject(jsonO,"pos_num"));
            operationLogs.setOperation_id(ParamUtil.getStringValueByObject(jsonO,"opt_num"));
            operationLogs.setShift_id(ParamUtil.getStringValueByObject(jsonO,"shift_id"));
            this.saveCustomerOperationLog(tenancyId,operationLogs);
        }catch (Exception e){
            e.printStackTrace();
        }

        return resData;
    }



    @Override
    public Data findAcewillCustomerGrade(Data requestData) throws Exception {
//        String requestUrl = getPequestUrl(FIND_ACEWILL_CONSUME_GRADE);
        String tenancyId = requestData.getTenancy_id();
        Integer storeId = requestData.getStore_id();
        Data resData = new Data();

        // 请求acewill接口
        List<JSONObject> data = new ArrayList<JSONObject>();
        Data paramData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
        data.add(new JSONObject());
        paramData.setData(data);
        paramData.setType(requestData.getType());
        paramData.setT(requestData.getT());
        paramData.setOper(requestData.getOper());
        List<JSONObject> dataList = new ArrayList<JSONObject>();
        try {
            //查看会员等级
            resData = this.commonPost(paramData, AcewillRequestUtil.CUSTOMER_GRADE_RULE);
            if (Constant.CODE_SUCCESS == resData.getCode()) {
                JSONObject jsonCon = JSONObject.fromObject(resData.getData().get(0));
                if (jsonCon != null) {
                    JSONArray grades = jsonCon.getJSONArray("grades");
                    for(int i = 0;i<grades.size();i++){
                        JSONObject grade = JSONObject.fromObject(grades.get(i));
                        JSONObject jsonObj = new JSONObject();
                        jsonObj.put("grade_id",grade.optString("id"));
                        jsonObj.put("grade_name",grade.optString("name"));
                        jsonObj.put("isDefaultLevel",grade.optString("isDefaultLevel"));
                        dataList.add(jsonObj);
                    }
                }
            }

        } catch (Exception e) {
            logger.info("查看会员等级请求失败" + e.getMessage());
            resData = Data.get();
            resData.setMsg("查看会员等级请求求失败");
            resData.setCode(Constant.CODE_INNER_EXCEPTION);
            resData.setSuccess(false);
        }
        resData.setData(dataList);
        logger.info("查看会员等级回数据" + JsonUtil.DataToJson(resData));
        return resData;
    }

    @Override
    public Data checkAcewillCustomerCard(Data requestData) throws Exception {
//        String requestUrl = getPequestUrl(CHECK_ACEWILL_CONSUME_CARD);
        String tenancyId = requestData.getTenancy_id();
        Integer storeId = requestData.getStore_id();
        Data resData = new Data();

        JSONObject paramJson = JSONObject.fromObject(requestData.getData().get(0));

        JSONObject jsonParam = new JSONObject();
        jsonParam.put("acno",paramJson.optString("card_code"));
        jsonParam.put("phone",paramJson.optString("mobile"));
        jsonParam.put("sid",getShopId(tenancyId,storeId));
        //验证方式 开卡(默认):open 绑卡:bind
        jsonParam.put("mode","open"); //开卡

        // 请求acewill接口
        List<JSONObject> data = new ArrayList<JSONObject>();
        Data paramData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
        data.add(jsonParam);
        paramData.setData(data);
        paramData.setType(requestData.getType());
        paramData.setT(requestData.getT());
        paramData.setOper(requestData.getOper());
        List<JSONObject> dataList = new ArrayList<JSONObject>();
        try {
            //验证会员卡
            resData = this.commonPost(paramData, AcewillRequestUtil.CHECK_ACEWILL_CONSUME_CARD);
            if (Constant.CODE_SUCCESS == resData.getCode()) {
                JSONObject resultJson = JSONObject.fromObject(resData.getData().get(0));
                if (resultJson != null) {
                    JSONObject jsonObj = new JSONObject();
                    jsonObj.put("is_exist_phone",ParamUtil.getStringValueByObject(resultJson, "is_exist_phone"));
                    jsonObj.put("is_exist_card",ParamUtil.getStringValueByObject(resultJson, "is_exist_card"));
                    jsonObj.put("is_send_code",ParamUtil.getStringValueByObject(resultJson, "is_send_code"));
                    jsonObj.put("is_send_ok",ParamUtil.getStringValueByObject(resultJson, "is_send_ok"));
                    jsonObj.put("card_code",ParamUtil.getStringValueByObject(resultJson, "acno"));
                    dataList.add(jsonObj);
                }
            }

        } catch (Exception e) {
            open_card_bizid="";
            logger.info("验证会员卡请求失败" + e.getMessage());
            resData = Data.get();
            resData.setMsg("验证会员卡请求求失败");
            resData.setCode(Constant.CODE_INNER_EXCEPTION);
            resData.setSuccess(false);
        }
        resData.setData(dataList);
        logger.info("验证会员卡返回数据" + JsonUtil.DataToJson(resData));
        return resData;
    }


    @Override
    public Data makeAcewillCustomerCard(Data requestData) throws Exception {
//        String requestUrl = getPequestUrl(MAKE_ACEWILL_CONSUME_CARD);
        String tenancyId = requestData.getTenancy_id();
        Integer storeId = requestData.getStore_id();
        Data resData = new Data();

        JSONObject paramJson = JSONObject.fromObject(requestData.getData().get(0));

        String ts = String.valueOf(DateUtil.currentTimestamp().getTime());
        JSONObject jsonParam = new JSONObject();
        jsonParam.put("acno",paramJson.optString("card_code"));
        String biz_id = UUID.randomUUID().toString();
        jsonParam.put("biz_id",biz_id);//业务流水号
        open_card_bizid = biz_id;

        jsonParam.put("grade",paramJson.optString("grade_id"));
        //jsonParam.put("money",paramJson.get("money")); //售价

        // 请求acewill接口
        List<JSONObject> data = new ArrayList<JSONObject>();
        Data paramData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
        data.add(jsonParam);
        paramData.setData(data);
        paramData.setType(requestData.getType());
        paramData.setT(requestData.getT());
        paramData.setOper(requestData.getOper());
        List<JSONObject> dataList = new ArrayList<JSONObject>();
        String contentResult = "成功";
        try {
            //会员制卡
            resData = this.commonPost(paramData, AcewillRequestUtil.MAKE_ACEWILL_CONSUME_CARD);
            if (Constant.CODE_SUCCESS == resData.getCode()) {
                JSONObject resultJson = JSONObject.fromObject(resData.getData().get(0));
                if (resultJson != null) {
                    JSONObject jsonObj = new JSONObject();
                    jsonObj.put("biz_id",ParamUtil.getStringValueByObject(resultJson, "biz_id"));
                    jsonObj.put("grade_id",ParamUtil.getStringValueByObject(resultJson, "grade"));
                    jsonObj.put("card_code",ParamUtil.getStringValueByObject(resultJson, "acno"));
                    dataList.add(jsonObj);
                }
            }

        } catch (Exception e) {
            open_card_bizid="";
            logger.info("会员卡制卡请求失败" + e.getMessage());
            resData = Data.get();
            resData.setMsg("会员卡制卡请求求失败");
            resData.setCode(Constant.CODE_INNER_EXCEPTION);
            resData.setSuccess(false);
            contentResult = "失败," + e.getMessage();
        }
        resData.setData(dataList);

        try {
            CustomerOperationLogs operationLogs = new CustomerOperationLogs();
            operationLogs.setTenancy_id(tenancyId);
            operationLogs.setStore_id(storeId);
            operationLogs.setReport_date(ParamUtil.getStringValueByObject(paramJson,"report_date"));
            operationLogs.setShift_id(ParamUtil.getStringValueByObject(paramJson,"shift_id"));
            operationLogs.setPos_num(ParamUtil.getStringValueByObject(paramJson,"pos_num"));
            operationLogs.setOperation_id(ParamUtil.getStringValueByObject(paramJson,"opt_num"));
            operationLogs.setCard_code(ParamUtil.getStringValueByObject(paramJson,"card_code"));
            operationLogs.setOperation_type(CustomerOperationEnum.MAKE_CARD.getType());
            operationLogs.setOperation_type_name(CustomerOperationEnum.MAKE_CARD.getValue());
            operationLogs.setTitle(CustomerOperationEnum.MAKE_CARD.getTitle());
            operationLogs.setContent("制会员卡，卡号："+operationLogs.getCard_code() +",等级："+jsonParam.getString("grade")+",手机号："+ParamUtil.getStringValueByObject(paramJson,"mobile") +",业务流水号:"+jsonParam.getString("biz_id") +"|" + contentResult);
            this.saveCustomerOperationLog(tenancyId,operationLogs);
        }catch (Exception e){
            e.printStackTrace();
        }


        logger.info("会员卡制卡返回数据" + JsonUtil.DataToJson(resData));
        return resData;
    }

    @Override
    public Data openAcewillCustomerCard(Data requestData) throws Exception {
//        String requestUrl = getPequestUrl(OPEN_ACEWILL_CONSUME_CARD);
        String tenancyId = requestData.getTenancy_id();
        Integer storeId = requestData.getStore_id();
        Data resData = new Data();

        JSONObject paramJson = JSONObject.fromObject(requestData.getData().get(0));

        JSONObject jsonParam = new JSONObject();
        jsonParam.put("acno",paramJson.optString("card_code"));
        jsonParam.put("phone",paramJson.optString("mobile"));
        jsonParam.put("sid",getShopId(tenancyId,storeId));
        if(Tools.hv(paramJson.optString("code"))){
            jsonParam.put("code",paramJson.optString("code"));
        }

        if(Tools.hv(paramJson.optString("username"))){
            jsonParam.put("username",paramJson.optString("username"));
        }
        if(Tools.hv(paramJson.optString("gender"))){
            jsonParam.put("gender",paramJson.optString("gender")); //0:未知 1:男 2:女
        }
        if(Tools.hv(paramJson.optString("birthday"))){
            jsonParam.put("birthday",paramJson.optString("birthday")); //日期 2001-01-02
        }
        if(Tools.hv(paramJson.optString("birthflay"))){
            jsonParam.put("birthflay",paramJson.optString("birthflay")); // 生日日期类型 0:公历 1:农历
        }
        if(Tools.hv(paramJson.optString("password"))){
            jsonParam.put("password",paramJson.optString("password")); //交易密码，长度为6位数字
        }

        // 请求acewill接口
        List<JSONObject> data = new ArrayList<JSONObject>();
        Data paramData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
        data.add(jsonParam);
        paramData.setData(data);
        paramData.setType(requestData.getType());
        paramData.setT(requestData.getT());
        paramData.setOper(requestData.getOper());
        try {
            //会员卡开卡
            resData = this.commonPost(paramData, AcewillRequestUtil.OPEN_ACEWILL_CONSUME_CARD);
        } catch (Exception e) {
            logger.info("会员卡开卡请求失败" + e.getMessage());
            resData = Data.get();
            resData.setMsg("会员卡开卡请求求失败");
            resData.setCode(Constant.CODE_INNER_EXCEPTION);
            resData.setSuccess(false);
        }
        List<JSONObject> list = new ArrayList<>();
        JSONObject json = new JSONObject();
        if(resData.getData()!=null){
            json = JSONObject.fromObject(resData.getData().get(0));
            json.put("biz_id",open_card_bizid);
            list.add(json);
        }
        resData.setData(list);

        try {
            CustomerOperationLogs operationLogs = new CustomerOperationLogs();
            operationLogs.setTenancy_id(tenancyId);
            operationLogs.setStore_id(storeId);
            operationLogs.setReport_date(paramJson.optString("report_date"));
            operationLogs.setOperation_id(paramJson.optString("opt_num"));
            operationLogs.setShift_id(paramJson.optString("shift_id"));
            operationLogs.setPos_num(paramJson.optString("pos_num"));
            operationLogs.setCard_code(paramJson.optString("card_code"));
            operationLogs.setOperation_type_name(CustomerOperationEnum.ACTIVATED_CARD.getValue());
            operationLogs.setOperation_type(CustomerOperationEnum.ACTIVATED_CARD.getType());
            operationLogs.setTitle(CustomerOperationEnum.ACTIVATED_CARD.getTitle());
            this.saveCustomerOperationLog(tenancyId,operationLogs);
        }catch (Exception e){
            e.printStackTrace();
        }

        logger.info("会员卡开卡返回数据" + JsonUtil.DataToJson(resData));
        return resData;
    }
    
    @Override
	public void printActivatedAcewillCustomerCard(String tenantId, Integer storeId, String bizId, String cardCode, String posNum, String optNum) throws Exception
	{
		try
		{
			String operator = customerDao.getEmpNameById(optNum, tenantId, storeId);
			JSONObject printJson = new JSONObject();
			printJson.put("card_code", cardCode);
			printJson.put("biz_id", bizId);
			printJson.put("operator", operator);
			printJson.put("updatetime", DateUtil.getNowDateYYDDMMHHMMSS());
			printJson.put("pos_num", posNum);
			printJson.put("print_code", SysDictionary.PRINT_CODE_1013);

			if (posPrintNewService.isNONewPrint(tenantId, storeId))
			{ // 启用新打印
				posPrintNewService.posPrintByMode(tenantId, storeId, SysDictionary.PRINT_CODE_1013, printJson);
			}
			else
			{
				customerDao.customerPrint(tenantId, storeId, posNum, SysDictionary.PRINT_CODE_1013, "1", printJson, 1);
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
	}

	@Override
	public void savePosLog(String tenantId, Integer organId, String posNum, String optNum, String optName, Integer shiftId, Date reportDate, String title, String content, String oldstate, String newstate)
	{
		try
		{
			if (Tools.isNullOrEmpty(optName))
			{
				optName = customerDao.getEmpNameById(optNum, tenantId, organId);
			}
			customerDao.savePosLog(tenantId, organId, posNum, optNum, optName, shiftId, reportDate, title, content, oldstate, newstate);
		}
		catch (Exception e)
		{
			logger.info("记录操作日志失败" + e.getLocalizedMessage());
		}
	}
}
