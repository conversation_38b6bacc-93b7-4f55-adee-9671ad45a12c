package com.tzx.pos.bo.dto;

import java.io.Serializable;
import java.util.List;

public class PosKvsItem implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	private String table_code;
	private String bill_num;
	private String open_pos_num;
	private String open_opt;
	private String sale_mode;
	private String order_time;
	private String kvs_class_name;
	private String kvs_class_id;
	private List<?> item;
	
	public String getKvs_class_id() {
		return kvs_class_id;
	}
	public void setKvs_class_id(String kvs_class_id) {
		this.kvs_class_id = kvs_class_id;
	}
	
	public String getTable_code() {
		return table_code;
	}
	public void setTable_code(String table_code) {
		this.table_code = table_code;
	}
	public String getBill_num() {
		return bill_num;
	}
	public void setBill_num(String bill_num) {
		this.bill_num = bill_num;
	}
	public String getOpen_pos_num() {
		return open_pos_num;
	}
	public void setOpen_pos_num(String open_pos_num) {
		this.open_pos_num = open_pos_num;
	}
	public String getOpen_opt() {
		return open_opt;
	}
	public void setOpen_opt(String open_opt) {
		this.open_opt = open_opt;
	}
	public String getSale_mode() {
		return sale_mode;
	}
	public void setSale_mode(String sale_mode) {
		this.sale_mode = sale_mode;
	}
	public String getOrder_time() {
		return order_time;
	}
	public void setOrder_time(String order_time) {
		this.order_time = order_time;
	}
	public String getKvs_class_name() {
		return kvs_class_name;
	}
	public void setKvs_class_name(String kvs_class_name) {
		this.kvs_class_name = kvs_class_name;
	}
	public List<?> getItem() {
		return item;
	}
	public void setItem(List<?> item) {
		this.item = item;
	}
}
