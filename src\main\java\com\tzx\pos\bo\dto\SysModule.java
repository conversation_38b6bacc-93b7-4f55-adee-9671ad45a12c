package com.tzx.pos.bo.dto;

import java.io.Serializable;

/**
 * 
 * <AUTHOR> 
 * @日期：2015年5月13日-下午3:34:53
 */

public class SysModule implements Serializable
{
	/**
	 * 
	 */
	private static final long	serialVersionUID	= -4975507349649354881L;
	
	private String authority;
	private String	id;
	private String	module_name;
	private String	states;
	private String  module_link_url;
//	private String father_module_id;
//	private String module_level;
	
	public String getAuthority()
	{
		return authority;
	}
	public void setAuthority(String authority)
	{
		this.authority = authority;
	}
//	public String getFather_module_id()
//	{
//		return father_module_id;
//	}
//	public void setFather_module_id(String father_module_id)
//	{
//		this.father_module_id = father_module_id;
//	}
//	public String getModule_level()
//	{
//		return module_level;
//	}
//	public void setModule_level(String module_level)
//	{
//		this.module_level = module_level;
//	}
	public String getId()
	{
		return id;
	}
	public void setId(String id)
	{
		this.id = id;
	}
	public String getModule_name()
	{
		return module_name;
	}
	public void setModule_name(String module_name)
	{
		this.module_name = module_name;
	}
	public String getStates()
	{
		return states;
	}
	public void setStates(String states)
	{
		this.states = states;
	}
	public String getModule_link_url()
	{
		return module_link_url;
	}
	public void setModule_link_url(String module_link_url)
	{
		this.module_link_url = module_link_url;
	}
}
