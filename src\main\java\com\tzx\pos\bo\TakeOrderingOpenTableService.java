package com.tzx.pos.bo;

import net.sf.json.JSONObject;

import com.tzx.framework.common.entity.Data;
import com.tzx.pos.bo.dto.TakeOrderingParam;

public interface TakeOrderingOpenTableService {
     String	NAME	= "com.tzx.pos.bo.imp.TakeOrderingOpenTableServiceImp";
     
	 /**
	  * 开台接口
	  * @param param
	  * @param result
	  */
	 public TakeOrderingParam insertTakeOpenTable(Data param, Data result,JSONObject json) throws Exception;
}
