package com.tzx.framework.common.print;

import java.awt.Font;
import java.util.ArrayList;
import java.util.List;

public class PrintLine
{
	List<PrintMete>	PrintMeteList;
	Font			font;
	float			marginTop;
	float			marginBottom;

	public PrintLine(Font font, float marginTop, float marginBottom)
	{
		this.font = font;
		this.marginTop = marginTop;
		this.marginBottom = marginBottom;
		PrintMeteList = new ArrayList<PrintMete>();
	}

	public List<PrintMete> getPrintMeteList()
	{
		return PrintMeteList;
	}

	public void setPrintMeteList(List<PrintMete> printMeteList)
	{
		PrintMeteList = printMeteList;
	}

	public Font getFont()
	{
		return font;
	}

	public void setFont(Font font)
	{
		this.font = font;
	}

	public float getMarginTop()
	{
		return marginTop;
	}

	public void setMarginTop(float marginTop)
	{
		this.marginTop = marginTop;
	}

	public float getMarginBottom()
	{
		return marginBottom;
	}

	public void setMarginBottom(float marginBottom)
	{
		this.marginBottom = marginBottom;
	}
}
