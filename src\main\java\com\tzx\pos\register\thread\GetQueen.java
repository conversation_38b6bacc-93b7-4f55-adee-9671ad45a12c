package com.tzx.pos.register.thread;

import java.io.IOException;

import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.params.CoreConnectionPNames;
import org.apache.http.util.EntityUtils;

import com.tzx.pos.register.service.RegisterService;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

public class GetQueen extends Thread {
	private UploadQueen queen;
	private RegisterService registerService;
	private boolean flag = false;
	private String result = "";
	private static final String postUrl = "http://test.e7e6.net/rest/hq/deviceRegisterDetailsPost";

	public GetQueen(UploadQueen queen, RegisterService registerService) {
		this.queen = queen;
		this.registerService = registerService;
	}

	@Override
	public void run() {
		while (true) {
			try {
				JSONObject json = queen.take();
				JSONArray jsonArrayDevices = (JSONArray) json.get("devices");
				JSONArray jsonArrayKvsref = (JSONArray) json.get("kvsref");
				for (int i = 0; i < jsonArrayDevices.size(); i++) {
					JSONObject jsonData = (JSONObject) jsonArrayDevices.get(i);// 得到对象中的第i条记录
					jsonData.remove("id");
				}
				for (int i = 0; i < jsonArrayKvsref.size(); i++) {
					JSONObject jsonData = (JSONObject) jsonArrayKvsref.get(i);// 得到对象中的第i条记录
					jsonData.remove("id");
				}
				System.out.println(json);
				// 执行上传方法 根据返回结果判断是否上传成功
				// -- 上传方法 网络断开 ，网络超时 ，返回结果 不成功 都为false----//
				json.put("action", "DEVICE_ACTION_UPLOAD_DEVICE");

				DefaultHttpClient httpClient = new DefaultHttpClient();
				HttpPost httpPost = new HttpPost(postUrl);
				try {
					result = getEntity(httpClient, httpPost, json);
					JSONObject jsonResult = JSONObject.fromObject(result);

					if (jsonResult.getBoolean("success") == true) {
						flag = true;
					}
				} catch (IOException e1) {
					result = e1.getMessage();
					flag = false;
				}

				if (flag) {
					for (int i = 0; i < jsonArrayDevices.size(); i++) {
						JSONObject jsonData = (JSONObject) jsonArrayDevices.get(i);// 得到对象中的第i条记录
						// 修改数据状态为0 已同步上传
						try {
							int count = registerService.updateHqLocalDevices(jsonData.getString("devices_code"));
						} catch (Exception e) {
							// TODO Auto-generated catch block
							e.printStackTrace();
						}
					}

				} else {
					System.out.println(result);
					/*
					 * synchronized (queen) { queen.notify();
					 */
				}

			} catch (InterruptedException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}

	}

	public String getEntity(DefaultHttpClient httpClient, HttpPost method, JSONObject jsonParam)
			throws ClientProtocolException, IOException {
		StringEntity entity = new StringEntity(jsonParam.toString(), "utf-8");// 解决中文乱码问题
		entity.setContentEncoding("UTF-8");
		entity.setContentType("application/json");
		method.setEntity(entity);
		// 请求超时
		httpClient.getParams().setParameter(CoreConnectionPNames.CONNECTION_TIMEOUT, 20000);
		// 读取超时
		httpClient.getParams().setParameter(CoreConnectionPNames.SO_TIMEOUT, 20000);
		HttpResponse result = httpClient.execute(method);

		return EntityUtils.toString(result.getEntity());
	}

}
