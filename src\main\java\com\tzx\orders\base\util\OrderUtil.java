package com.tzx.orders.base.util;

import static com.tzx.orders.base.constant.Constant.DZFP_DHWM;
import static com.tzx.orders.base.constant.Constant.DZFP_WXDN;
import static com.tzx.orders.base.constant.Constant.DZFP_WXWM;
import static com.tzx.orders.base.constant.Constant.NOTIFY_ALL;
import static com.tzx.orders.base.constant.Constant.NOTIFY_POS;
import static com.tzx.orders.base.constant.Constant.NOTIFY_SAAS;
import static com.tzx.orders.base.constant.Constant.NUMBER_EXACT;
import static com.tzx.orders.base.constant.Constant.ORDER_STATE_CANCEL;
import static com.tzx.orders.base.constant.Constant.ORDER_STATE_COMPLETE;
import static com.tzx.orders.base.constant.Constant.ORDER_STATE_COMPLETE_PAYMENT;
import static com.tzx.orders.base.constant.Constant.ORDER_STATE_DELIVERY;
import static com.tzx.orders.base.constant.Constant.ORDER_STATE_EXCEPTION;
import static com.tzx.orders.base.constant.Constant.ORDER_STATE_EXCEPTION_COMPLETE;
import static com.tzx.orders.base.constant.Constant.ORDER_STATE_RECEIVE;
import static com.tzx.orders.base.constant.Constant.ORDER_STATE_REFUND_AGREE;
import static com.tzx.orders.base.constant.Constant.ORDER_STATE_REFUND_CANCEL;
import static com.tzx.orders.base.constant.Constant.ORDER_STATE_REFUND_DISAGREE;
import static com.tzx.orders.base.constant.Constant.ORDER_STATE_REFUND_RECEIVED;
import static com.tzx.orders.base.constant.Constant.ORDER_STATE_TOSHOP;
import static com.tzx.orders.base.constant.Constant.PAYMENT_STATE_REFUND;
import static com.tzx.orders.base.constant.Constant.THIRD_CHANNEL;
import static com.tzx.orders.base.constant.Constant.WM_CHANNEL;
import static com.tzx.orders.base.constant.Constant.WX02_CHANNEL;
import static com.tzx.pos.base.Constant.constantMap;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;

import com.tzx.base.common.util.PosPropertyUtil;
import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.HttpRestException;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.Comet4jUtil;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.HttpUtil;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.Tools;
import com.tzx.orders.bo.dto.OrderState;
import com.tzx.orders.bo.dto.PosBillItem;
import com.tzx.orders.bo.imp.OrderSyncServiceImp;
import com.tzx.orders.po.springjdbc.dao.OrdersManagementDao;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.util.JsonUtil;
import com.tzx.pos.base.util.ParamUtil;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

public class OrderUtil
{
    private static Logger	logger	= Logger.getLogger(OrderUtil.class);

    // 获得计算用的小数点位数
    private static String cmjews = constantMap.get("CMJEWS");
    private static String zdjews = constantMap.get("ZDJEWS");

	/**
	 * 上传订单状态地址
	 */
	private static final String	SEND_ORDER_STATE_URL	= "/postOrderResultRest/post";

    public static JSONObject sendOrderState(String tenancyId, int storeId, Type type, Oper oper, List<?> para) throws HttpRestException {
        Data data = Data.get();
        data.setTenancy_id(tenancyId);
        data.setStore_id(storeId);
        data.setType(type);
        data.setOper(oper);
        data.setData(para);

        String reqURL = PosPropertyUtil.getMsg("saas.url") + SEND_ORDER_STATE_URL;

        //JSONObject result = HttpUtil.post(reqURL,JSONObject.fromObject(data),3,true);
        //饿了么接单超时，将读取超时时间修改60s
        JSONObject result = null;
        for (int i =0;i<3;i++) {
            try {
                if (i > 0) Thread.sleep(3000);
                result = HttpUtil.post(reqURL, JSONObject.fromObject(data), 3, 10000, 60000, true);
                if (null == result || (result.containsKey("code") && result.containsKey("success") && result.optInt("code")!=0 && result.optBoolean("success")!=true)) {
                    continue;
                }
                return result;
            } catch (Exception e) {
                logger.debug("上传订单状态出现异常" + e);
            }
        }

        if(null == result || (result.containsKey("code") && result.containsKey("success") && result.optInt("code")!=0 && result.optBoolean("success")!=true) )
        {
        	result = new JSONObject();
        	result.put("success", false);
        	result.put("code", Constant.CODE_NULL_DATASET);
        	result.put("msg", "同步订单状态失败");
        }
        return result;
    }

    public static String queryOrderState(String tenancyId, int storeId,String orderCode) throws SystemException
    {
        Data data = Data.get();
        data.setTenancy_id(tenancyId);
        data.setStore_id(storeId);
        data.setType(Type.ORDER);
        data.setOper(Oper.order_state_query);

        JSONObject object=new JSONObject();
        object.put("order_code",orderCode);
        data.setData(Arrays.asList(object));

//        String reqURL = Constant.getSystemMap().get("saas_url") + "/orderRest/post";
        String reqURL = PosPropertyUtil.getMsg("saas.url") + "/orderRest/post";
        String sendData = JSONObject.fromObject(data).toString();
        logger.info("查询订单状态: " + reqURL + " 参数: " + sendData);

        String result=  HttpUtil.sendPostRequest(reqURL, sendData, 10000, 10000);
        if (null == result) {
            throw SystemException.getInstance("网络延迟或中断!", PosErrorCode.ORDER_SEND_ORDER_STATE_ERROR);
        }
        JSONObject reqResultJson = JSONObject.fromObject(result);
        logger.info("查询订单状态返回结果: "+ reqResultJson);

        if (reqResultJson.optBoolean("success") == false) {
            throw SystemException.getInstance(reqResultJson.optString("msg"), PosErrorCode.ORDER_SEND_ORDER_STATE_ERROR);
        }
        JSONArray array = reqResultJson.optJSONArray("data");
        if(array.isEmpty()){
            return null; //总部接口有问题，有时已到门店但总部还没有记录
        }else {
            JSONObject jsonObject = JSONObject.fromObject(array.get(0));
            String orderState = jsonObject.optString("order_state");
            return orderState;
        }
    }

    public  static void hook(String channel){
        synchronized (OrderUtil.class) {
            OrderSyncServiceImp.NO_NEED_SYNC = true;
            if (THIRD_CHANNEL.contains(channel)) {
                if (!NUMBER_EXACT.equals(cmjews)) {
                    constantMap.put("CMJEWS", NUMBER_EXACT);
                }
                if (!NUMBER_EXACT.equals(zdjews)) {
                    constantMap.put("ZDJEWS", NUMBER_EXACT);
                }
            }
            if (WM_CHANNEL.contains(channel)) {
                OrderSyncServiceImp.NO_NEED_SEND = true;
            }
        }
    }

    public static void unhook(){
        OrderSyncServiceImp.NO_NEED_SYNC = false;
        if (!NUMBER_EXACT.equals(cmjews)) {
            constantMap.put("CMJEWS", cmjews);
        }
        if (!NUMBER_EXACT.equals(zdjews)) {
            constantMap.put("ZDJEWS", zdjews);
        }
    }

    /** 校验订单明细
     * @param orderItem
     */
    public static List<PosBillItem> checkOrderItem( List<PosBillItem> orderItem ) {
        if (null == orderItem || orderItem.size() == 0) {
            throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_DISH_ITEM);
        }
        for (PosBillItem item : orderItem) {
            if (Tools.isNullOrEmpty(item.getItem_id())) {
                throw SystemException.getInstance(PosErrorCode.NOT_NULL_ITEM_ID);
            }
            if (Tools.isNullOrEmpty(item.getUnit_id())) {
                throw SystemException.getInstance(PosErrorCode.NOT_NULL_DISH_UNITID);
            }
            if (Tools.isNullOrEmpty(item.getItem_count())) {
                throw SystemException.getInstance(PosErrorCode.NOT_NULL_DISH_ITEM_COUNT);
            }
            if (Tools.isNullOrEmpty(item.getItem_property())) {
                throw SystemException.getInstance(PosErrorCode.NOT_NULL_ITEM_PROPERTY);
            }
            if (!"MEALLIST".equals(item.getItem_property())&&Tools.isNullOrEmpty(item.getDetails_id())) {
                throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_DISH_MENU);
            }
            if (Tools.isNullOrEmpty(item.getItem_num())) {
                throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_DISH_ITEM);
            }
            if (Tools.isNullOrEmpty(item.getItem_unit_name())) {
                throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_DISH_UNIT);
            }
        }
        return orderItem;
    }

    public static String getSysPara(String tenancyId, int storeId, String code){
        return ParamUtil.getSysPara(tenancyId,storeId,code);
    }

    /** 订单状态更新
     * @param orderState
     * @param stateValue
     * @param notifyTarget (0:NONE 1:SAAS 2:POS 4:ALL)
     * @return
     */
    public static OrderState changeOrderState(String tenentId,int storeId,OrderState orderState,String stateValue,int notifyTarget) throws Exception {
        String currentTime = DateUtil.getNowDateYYDDMMHHMMSS();
        switch (stateValue) {
            case ORDER_STATE_TOSHOP:
                orderState.setReceive_time(currentTime);
                orderState.setOrder_state(ORDER_STATE_TOSHOP);
                break;
            case ORDER_STATE_RECEIVE:
                orderState.setTake_time(currentTime);
                orderState.setReceive_time_qd(currentTime);
                orderState.setOrder_state(ORDER_STATE_RECEIVE);
                break;
            case ORDER_STATE_EXCEPTION:
                orderState.setTake_time(currentTime);
                orderState.setReceive_time_qd(currentTime);
                orderState.setOrder_state(ORDER_STATE_EXCEPTION);
                break;
            case ORDER_STATE_COMPLETE_PAYMENT:
                orderState.setOrder_state(ORDER_STATE_COMPLETE_PAYMENT);
                break;
            case ORDER_STATE_DELIVERY:
                orderState.setDistribution_time(currentTime);
                orderState.setReceive_time_distribution(currentTime);
                orderState.setOrder_state(ORDER_STATE_DELIVERY);
                break;
            case ORDER_STATE_CANCEL:
                orderState.setCancellation_time(currentTime);
                orderState.setReceive_time_cancellation(currentTime);
                orderState.setOrder_state(ORDER_STATE_CANCEL);
                orderState.setPayment_state(PAYMENT_STATE_REFUND);
                break;
            case ORDER_STATE_COMPLETE:
                orderState.setFinish_time(currentTime);
                orderState.setReceive_time_finish(currentTime);
                orderState.setOrder_state(ORDER_STATE_COMPLETE);
                break;
            case ORDER_STATE_EXCEPTION_COMPLETE:
                orderState.setFinish_time(currentTime);
                orderState.setReceive_time_finish(currentTime);
                orderState.setOrder_state(ORDER_STATE_EXCEPTION_COMPLETE);
                break;
            case ORDER_STATE_REFUND_RECEIVED:
                orderState.setRefund_recive_time(currentTime);
                orderState.setOrder_state(ORDER_STATE_REFUND_RECEIVED);
                break; 
            case ORDER_STATE_REFUND_AGREE:
                orderState.setOrder_state(ORDER_STATE_REFUND_AGREE);
                break;   
            case ORDER_STATE_REFUND_DISAGREE:
            	orderState.setOrder_state(ORDER_STATE_REFUND_DISAGREE);
            	break;   
            case ORDER_STATE_REFUND_CANCEL:
            	orderState.setOrder_state(ORDER_STATE_REFUND_CANCEL);
            	break;    
            default:
                break;
        }

        orderState.setDevice_ip(getMac().get("ip"));
        orderState.setDevice_mac(getMac().get("mac"));

        JSONObject param=JSONObject.fromObject(orderState);
        OrdersManagementDao ordersDao= (OrdersManagementDao) SpringConext.getBean(OrdersManagementDao.NAME);
        ordersDao.updateOrderInfo(tenentId, storeId, param);

        if(NOTIFY_ALL==notifyTarget||NOTIFY_SAAS==notifyTarget) {
            try {
                Data result= JsonUtil.JsonToData(OrderUtil.sendOrderState(tenentId, storeId, Type.ORDER, Oper.update, Arrays.asList(orderState)));
              if(WM_CHANNEL.contains(orderState.getChanel())&&com.tzx.pos.base.Constant.CODE_SUCCESS!=result.getCode()){
                   throw new SystemException(PosErrorCode.ORDER_SEND_ORDER_STATE_ERROR);
              }
            }catch (HttpRestException e){
               if(WX02_CHANNEL.equals(orderState.getChanel())){
                    logger.error("总部订单状态更新失败:"+orderState);
               }else {
                   throw e;
               }
            }
        }

        if(NOTIFY_ALL==notifyTarget||NOTIFY_POS==notifyTarget) {
            // 向前台发送消息
            JSONObject msg = new JSONObject();
            msg.put("order_code", Arrays.asList(orderState.getOrder_code()));
            msg.put("order_state", stateValue);
            Comet4jUtil.sendMessage2All(Comet4jUtil.CALLCENTER_ORDER, msg.toString());
        }

         return orderState;
    }

    /** 发票类型(1:电子 2:纸质)
     * @param channel
     * @return
     */
    public static String valueOfInvoice(String tenancyid,int storeId,String channel) throws Exception {
        List<String> SUPPORT_EINVOICE=new ArrayList<>();

        if( "1".equals( getSysPara(tenancyid,storeId,DZFP_WXDN)) ) SUPPORT_EINVOICE.add("WX02");
        if( "1".equals( getSysPara(tenancyid,storeId,DZFP_WXWM)) ) SUPPORT_EINVOICE.add("WM10");
//        if( !"0".equals( getSysPara(tenancyid,storeId,DZFP_DSFWM)) ) SUPPORT_EINVOICE.addAll(THIRD_CHANNEL);
        if( "1".equals( getSysPara(tenancyid,storeId,DZFP_DHWM)) ) SUPPORT_EINVOICE.add("CC04");

        return SUPPORT_EINVOICE.contains(channel)?"1":"2";
    }

    public static Map<String,String> getMac() {
        Map<String,String> map=new HashMap<>();
        StringBuilder sb = new StringBuilder();
        try{
            InetAddress ip = InetAddress.getLocalHost();
            map.put("ip",ip.getHostAddress());
            NetworkInterface network = NetworkInterface.getByInetAddress(ip);
            byte[] mac = network.getHardwareAddress();
            for (int i = 0; i < mac.length; i++) {
                sb.append(String.format("%02X%s", mac[i], (i < mac.length - 1) ? "-" : ""));
            }
            map.put("mac",sb.toString());
        }catch (Exception e){
            logger.error("获取mac地址失败",e);
        }
        return map;
    }

    /** 核对订单状态
     * @param tenantId
     * @param storeId
     * @param reportDate
     * @param isGetMissOrder
     * @return  异常时返回null
     */
    public static JSONObject validOrderData(String tenantId, int storeId, String reportDate, boolean isGetMissOrder){
        JSONObject result=null;
        String reqURL = PosPropertyUtil.getMsg("saas.url") + "/orderRest/analysisOrderDiffer";

        String startDate="",endDate="";
        String miss=isGetMissOrder?"1":"0";

        JSONArray data=null;
        try {
            OrdersManagementDao ordersDao= (OrdersManagementDao) SpringConext.getBean(OrdersManagementDao.NAME);

            String sqlQueryStartDate="select  to_char(min(single_time) - interval '10 min','yyyy-MM-dd HH:mm:ss') as single_time from cc_order_list where report_date = '"+reportDate+"' and chanel in ('MT11','MT08','BD06','EL09') ";
            List<JSONObject> startjo = ordersDao.query4Json(tenantId, sqlQueryStartDate);
            if (null != startjo && !startjo.isEmpty()) {
                startDate = startjo.get(0).optString("single_time");
            }else {
                logger.info("analysisOrderDiffer未获取到参数startDate");
                return result;
            }

            String sqlQueryOrderState="select order_code,order_state from cc_order_list where report_date = '"+reportDate+"' and  chanel in ('MT11','MT08','BD06','EL09') ";
            data = ordersDao.query4JSONArray(tenantId, sqlQueryOrderState);

            JSONObject request = new JSONObject();
            request.put("tenantId", tenantId);
            request.put("shopId", storeId);
            request.put("startDate", startDate);
            request.put("endDate", endDate);
            request.put("data", data);
            request.put("miss", miss);
            logger.info("analysisOrderDiffer方法输入参数："+request.toString());
            result = HttpUtil.post(reqURL,request,3);
            logger.info("analysisOrderDiffer方法返回值："+result.toString());
            //result = HttpRestUtil.post(reqURL, JSONObject.fromObject(data), 3);
        } catch (Exception e) {
            logger.error(e);
        }
        return result;

    }


}
