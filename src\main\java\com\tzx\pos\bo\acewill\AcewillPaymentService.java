package com.tzx.pos.bo.acewill;

import java.util.Date;
import java.util.List;

import net.sf.json.JSONObject;

import com.tzx.framework.common.entity.Data;
import com.tzx.pos.bo.payment.PosPaymentService;

public interface AcewillPaymentService extends PosPaymentService
{
	String	NAME	= "com.tzx.pos.bo.acewill.imp.AcewillPaymentServiceImp";

	/** 插入付款记录
	 * @param tenancyId
	 * @param storeId
	 * @param devicesType
	 * @param paramJson
	 * @return 请求单号
	 * @throws Exception
	 */
	public String addPosBillPaymentForAcewillPay(String tenancyId, int storeId, String devicesType, JSONObject paramJson) throws Exception;

	/** 消费预览
	 * @param tenancyId
	 * @param storeId
	 * @param paramJson
	 * @return
	 * @throws Exception
	 */
	public Data requestAcewillCustomerDealPreview(String tenancyId, int storeId, JSONObject paramJson) throws Exception;

	/** 消费提交
	 * @param tenancyId
	 * @param storeId
	 * @param paramJson
	 * @return
	 * @throws Exception
	 */
	public Data requestAcewillCustomerDealCommit(String tenancyId, int storeId, JSONObject paramJson,JSONObject printJson) throws Exception;
	
	/**交易提交成功修改,付款
	 * @param tenancyId
	 * @param storeId
	 * @param reportDate
	 * @param billNum
	 * @param outTradeNo
	 * @param billPaymentAmount
	 * @param responseJson
	 * @throws Exception
	 */
	public void requestDealCommitSuccess(String tenancyId, int storeId, Date reportDate, String billNum, String outTradeNo, Double billPaymentAmount, JSONObject responseJson) throws Exception;
	
	/**打印小票
	 * @param tenantId
	 * @param storeId
	 * @param source
	 * @param printJson
	 * @throws Exception
	 */
	public void printPosBillForPayment (String tenantId, Integer storeId, String source,JSONObject printJson) throws Exception;
	
	/**优惠劵验证
	 * @param tenancyId
	 * @param storeId
	 * @param paramJson
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> checkAcewillCoupons(String tenancyId, int storeId, JSONObject paramJson) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param outTradeNo
	 * @param chanel
	 * @param billPaymentAmount
	 * @param paymentList
	 * @return
	 * @throws Exception
	 */
	public JSONObject getPreviewDealParamJson(String tenancyId, int storeId, String billNum, String outTradeNo, String chanel, Double billPaymentAmount, List<JSONObject> paymentList) throws Exception;
}
