package com.tzx.pos.bo;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import net.sf.json.JSONObject;

import com.tzx.base.bo.dto.Version;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.entity.Pagination;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.pos.bo.dto.PosTableFind;

public interface PosService extends PosBaseService
{
	String	NAME	= "com.tzx.pos.bo.imp.PosServiceImp";

	/**
	 * 生成基本数据版本
	 * 
	 * @param tenantId
	 * @param organId
	 * @throws Exception
	 */
	public void buildBasicVersion(String tenantId, Integer organId) throws Exception;
	
	/**
	 * 基础资料版本检查
	 * 
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> basicVersion(Data param) throws Exception;

	/**
	 * 同步基础数据
	 * 
	 * @param param
	 * @param result
	 * @throws SystemException
	 */
	public void syncBaseData(Data param, Data result) throws SystemException;

	/**
	 * 系统设置
	 * 
	 * @param param
	 * @param result
	 * @throws SystemException
	 */
	public void systemConfig(Data param, Data result) throws SystemException;

	/**
	 * 设备申请
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param param
	 * @return
	 * @throws Exception
	 */
	void deviceApply(String tenancyId, int storeId, List<?> param, Data result) throws Exception;

	/**
	 * 设备查询
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public JSONObject findDevice(String tenancyId, int storeId, List<?> param) throws Exception;

	/**
	 * 验证APP版本
	 * 
	 * @param request
	 * @param param
	 * @return
	 * @throws Exception
	 * <AUTHOR> 2015-09-16
	 */
	List<Version> findAppVersion(HttpServletRequest request, List<?> param) throws Exception;

	/**
	 * 检查机台是否有人登录
	 * 
	 * @param param
	 * @param result
	 * @throws SystemException
	 */
	public void checkPosLogin(Data param, Data result) throws SystemException;

	/**
	 * 设置日始
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param param
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> checkDayBegain(String tenancyId, int storeId, List<?> param) throws Exception;

	/**
	 * 打开收银员
	 * 
	 * @param param
	 * @param result
	 * @throws SystemException
	 */
	public void openSyy(Data param, Data result) throws SystemException;

	/**
	 * 关闭收银员
	 * 
	 * @param param
	 * @param result
	 * @throws SystemException
	 */
	public void closeSyy(Data param, Data result) throws SystemException;

	/**
	 * 查询是否签到
	 * 
	 * @param param
	 * @param result
	 * @throws SystemException
	 */
	public void querySyy(Data param, Data result) throws Exception;

	/**
	 * 获取门店日始打烊及收银员签到签退状态
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public List<?> getStoreOptState(String tenancyId, int storeId, List<?> param) throws Exception;

	/**
	 * 交班
	 * 
	 * @param param
	 * @return
	 * @throws SystemException
	 */
	@Deprecated
	public Data changeShift(Data param) throws SystemException;

	/**
	 * 签退并交班
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param param
	 * @throws Exception
	 */
	@Deprecated
	public void signOffAndChangeShift(String tenancyId, int storeId, List<?> param, JSONObject printJson) throws Exception;

//	/**
//	 * 获取交班数据
//	 * 
//	 * @param param
//	 * @param result
//	 * @throws SystemException
//	 */
//	@Deprecated
//	public void getShiftData(Data param, Data result) throws SystemException;

//	/**
//	 * 交班查询
//	 * 
//	 * @param param
//	 * @param result
//	 * @throws SystemException
//	 */
//	public void checkChangShift(Data param, Data result) throws SystemException;

	/**
	 * 日结
	 * 
	 * @param param
	 * @param result
	 * @throws SystemException
	 * @throws Exception
	 */
	public void dayReport(Data param, Data result) throws SystemException, Exception;

	/**
	 * 取消打烊
	 * 
	 * @param param
	 * @param result
	 * @throws SystemException
	 */
	public void cancDayEnd(Data param, Data result) throws SystemException;

	/**
	 * 查询日结状态
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param json
	 * @return 返回true,已经日结;否则返回false
	 * @throws Exception
	 * <AUTHOR> 2015-09-15
	 */
	int checkDaycount(String tenancyId, int storeId, JSONObject json) throws Exception;

	/**
	 * 数据上传
	 * 
	 * @param param
	 * @return
	 * @throws SystemException
	 */
	public Data dataTrans(Data param) throws SystemException;

	/**
	 * 零找金
	 * 
	 * @param param
	 * @param result
	 * @throws SystemException
	 */
	public void blindSuccession(Data param, Data result) throws SystemException;

	/**
	 * 桌位查找
	 * 
	 * @param param
	 * @return
	 * @throws SystemException
	 */
	public List<PosTableFind> findTable(Data param) throws SystemException;

	/**
	 * 锁定或解锁桌位
	 * 
	 * @param param
	 * @param result
	 * @throws SystemException
	 */
	public void lockOrUnlockTable(Data param, Data result) throws SystemException;

	/**
	 * 查询桌位锁台信息
	 * 
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> findLockTable(Data param) throws Exception;

	/**
	 * 估清
	 * 
	 * @param param
	 * @param result
	 * @throws SystemException
	 */
	@Deprecated
	public void setSoldOut(Data param, Data result,List<JSONObject> soldList) throws Exception;

	/**
	 * 查询沽清
	 * 
	 * @param param
	 * @return
	 * @throws Exception
	 */
	@Deprecated
	public List<JSONObject> findSoldOut(Data param) throws Exception;

	/**
	 * 急推菜品
	 * 
	 * @param param
	 * @param result
	 * @throws SystemException
	 */
	public void setWorryItem(Data param, Data result) throws SystemException;

	/**
	 * 获取沽清数量
	 * 
	 * @param param
	 * @return
	 * @throws SystemException
	 */
	public Data getSoldOutCount(Data param) throws SystemException;

	/**
	 * 查询急推菜品
	 * 
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> findWorryItem(Data param) throws Exception;

	/**
	 * 查找服务员
	 * 
	 * @param param
	 * @param result
	 * @throws SystemException
	 */
	public void findWaiter(Data param, Data result) throws SystemException;

	/**
	 * 获取实时菜品
	 * 
	 * @param param
	 * @return
	 * @throws SystemException
	 */
	public Data getDishByClass(Data param) throws SystemException;

	/**
	 * 菜品排行
	 * 
	 * @param param
	 * @param result
	 * @throws SystemException
	 */
	public void findItemSort(Data param, Data result) throws SystemException;

	/**
	 * 特价菜品
	 * 
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> findItemCheap(Data param) throws Exception;

	/**
	 * 开发票
	 * 
	 * @param param
	 * @param result
	 * @throws SystemException
	 */
	public void setInvoice(Data param, Data result) throws SystemException;

	/**
	 * 查询发票
	 * 
	 * @param param
	 * @param result
	 * @throws SystemException
	 */
	public void findInvoice(Data param, Data result) throws SystemException;

	/**
	 * 提交评价
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param param
	 * @return
	 * @throws Exception
	 * <AUTHOR> 2015-09-16
	 */
	boolean updateGiveSuggest(String tenancyId, int storeId, List<?> param) throws Exception;

	/**
	 * 收银员绑定机台
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param param
	 * @throws Exception
	 */
	void addOptBindDevices(String tenancyId, int storeId, List<?> param) throws Exception;

	/**
	 * 查询收银员绑定机台
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param param
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> findOptBindDevices(String tenancyId, int storeId, List<?> param, Pagination pagination) throws Exception;

	/**
	 * 取消收银员绑定机台
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param param
	 * @throws Exception
	 */
	void cancleOptBindDevices(String tenancyId, int storeId, List<?> param) throws Exception;

	/**
	 * 营业收银员,收款机台查询
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param param
	 * @param pagination
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> findOptPosData(String tenancyId, int storeId, List<?> param, Pagination pagination) throws Exception;

//	/**
//	 * TODO 交款统计
//	 * 
//	 * @param tenancyId
//	 * @param storeId
//	 * @param param
//	 * @param pagination
//	 * @return
//	 * @throws Exception
//	 */
//	List<JSONObject> totalCashierReceive(String tenancyId, int storeId, List<?> param, Pagination pagination) throws Exception;
//
//	/**
//	 * 交款详细统计
//	 * 
//	 * @param tenancyId
//	 * @param storeId
//	 * @param param
//	 * @param pagination
//	 * @return
//	 * @throws Exception
//	 */
//	List<JSONObject> cashierReceiveDetails(String tenancyId, int storeId, List<?> param, Pagination pagination) throws Exception;
//
//	/**
//	 * 交款/抽大钞
//	 * 
//	 * @param tenancyId
//	 * @param storeId
//	 * @param param
//	 * @throws Exception
//	 */
//	void addCashierReceive(String tenancyId, int storeId, List<?> param, JSONObject printJson) throws Exception;
//
//	/**
//	 * 交款流水查询
//	 * 
//	 * @param tenancyId
//	 * @param storeId
//	 * @param param
//	 * @param pagination
//	 * @return
//	 * @throws Exception
//	 */
//	List<JSONObject> findCashierReceive(String tenancyId, int storeId, List<?> param, Pagination pagination) throws Exception;
//
//	/**
//	 * 签退时检查交款差额
//	 * 
//	 * @param param
//	 * @param result
//	 * @return
//	 * @throws Exception
//	 */
//	void checkReceive(Data param, Data result) throws Exception;

	/**
	 * 抽大钞
	 * 
	 * @param param
	 * @param result
	 * @return
	 * @throws SystemException
	 */
	public void extractBills(Data param, Data result) throws SystemException;

//	/**
//	 * 历史交班明细查询
//	 * 
//	 * @param param
//	 * @param result
//	 * @throws SystemException
//	 */
//	public void getDetailChangShift(Data param, Data result) throws SystemException;

	/**
	 * 打开钱箱记入log
	 * 
	 * @param param
	 * @param result
	 * @return
	 * @throws SystemException
	 */
	public void saveOpenBoxLog(Data param, Data result) throws SystemException;

	/**
	 * 查询抽大钞历史记录
	 * 
	 * @param param
	 * @param result
	 * @return
	 * @throws SystemException
	 */
	public void getExtractBillsHistory(Data param, Data result) throws SystemException;

	/**
	 * 银行支付接口 保存交易记录
	 * 
	 * @param param
	 * @param result
	 * @throws SystemException
	 */
	public void saveBankTrandingLog(Data param, Data result) throws SystemException;

	/**
	 * /** 银行支付接口 查询交易记录
	 * 
	 * @param param
	 * @param result
	 * @throws SystemException
	 */
	public void queryBankTrandingLog(JSONObject jsobj, Data param, Data result) throws SystemException;

	/**
	 * 门店上传模板信息(delphi)
	 * 
	 * @param
	 * @param
	 * @param prarm
	 * @return
	 * @throws Exception
	 */
	public void uploadPrintFormat(Data prarm) throws Exception;

	/**
	 * 修改口味
	 * 
	 * @param param
	 * @param result
	 */
	public void changTaste(Data param, Data result) throws SystemException;
	
	/**
	 * 更新桌位数据更新状态
	 * @throws Exception
	 */
	public void updateDevicesDataState() throws Exception;
	
	/**
	 * 获取设备更新状态
	 * @param devicesCode
	 * @param result
	 * @throws Exception
	 */
	public void getDevicesDataState(String devicesCode,Data result)  throws Exception;
	
	/**更新设备更新状态
	 * 
	 * @param devicesCode
	 * @param state
	 * @throws Exception
	 */
	public void updateDevicesByCode(String devicesCode,String state)  throws Exception;
	
	/**
	 * 查询打印机状态
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void getPrinterState(Data param,Data result) throws Exception;
	
	/**
	 * 打印机状态推送
	 * @param param
	 * @param result
	 */
	public void pushPrintState(Data param,Data result);
	/**
	 * 本地验证优惠卷
	 * @param param
	 * @param result
	 */
	public void getCouponsValiData(Data param, Data result) throws Exception;

	/**
	 * 设置取餐号
	 * @param tenancyId
	 * @param storeId
	 * @param type
	 * @param reportDate
	 * @return
	 * @throws Exception
	 */
	public void setTakeMealNum(String tenancyId, int storeId, String type, String reportDate) throws Exception;


//	/**
//	 * 无码优惠卷核单
//	 * @param param
//	 * @param result
//	 * @throws Exception
//	 * @throws SystemException
//	 */
//	@Deprecated
//	public void getNocodeCoupons(Data param, Data result) throws Exception,SystemException;
//	
//	/**
//	 * 查询可用优惠卷
//	 * @param param
//	 * @param result
//	 * @throws Exception
//	 * @throws SystemException
//	 */
//	public void getNocodeCouponsValiData(Data param, Data result)throws Exception,SystemException;

	/**
	 * 查询账单总金额
	 * @param param
	 * @param result
	 * @throws Exception
	 * @throws SystemException
	 */
	public void findTotalAmount(Data param, Data result)throws Exception,SystemException;

	/**
	 * 取出关联的没有沽清的itemid
	 * @param list
	 * @param itemStr
	 * @return
	 * @throws Exception 
	 * @throws NumberFormatException 
	 */
	public List <String>getItemIdSoldOut(List <Map<String, Object>>list, String itemStr) throws NumberFormatException, Exception;

	/**
	 * 获取操作员
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void  getOptName(Data param,Data result) throws Exception;
	
	/**
	 * 查询账单模式
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @return
	 * @throws Exception
	 */
	public String getSaleMode(String tenancyId, Integer storeId, String billNum) throws Exception;

	/**
	 * 查询总部前一次日结数据状态
	 * @param tenancyId
	 * @param storeId
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public int getDayCountState(String tenancyId, int storeId, List<?> param) throws Exception;

//	/**
//	 * TODO 交款/抽大钞
//	 *
//	 * @param tenancyId
//	 * @param storeId
//	 * @param param
//	 * @throws Exception
//	 */
//	public void addCashierReceiveBatch(String tenancyId, int storeId, Data param, List<JSONObject> printJsonList) throws Exception;
//    /**
//     * 交款限额验证
//     * @param param
//     * @return
//     * @throws Exception
//     */
//	public Data cashierLimitVlidate(Data param) throws Exception;
	/**
     * 修改菜品状态
     * @param param
     * @return
     * @throws Exception
     */
	public Data updateItemServedState(Data param) throws Exception;

    /**
     * 加载厨打打印序号
     * @param tenancyId
     * @param storeId
     * @throws Exception
     */
	public void loadPrintSerialNumber(String tenancyId, int storeId) throws Exception;

    /**
     * 从缓存中更新打印序号表
     * @param tenancyId
     * @param storeId
     * @throws Exception
     */
    public void updatePrintSerialByMap(String tenancyId, int storeId) throws Exception;

    /**
     * 开钱箱
     * @param param
     * @param result
     * @throws Exception
     */
    void openCashBox(Data param, Data result) throws Exception;


    /** 交班检查
     * @param param
     * @param result
     * @throws Exception
     */
    void checkUnclosedBill(Data param, Data result)  throws Exception;

    /**判断是否是新店
     * @param tenancy_id
     * @param store_id
     * @param data
     * @return
     */
    boolean isNewInstall(String tenancy_id, int store_id, List<?> data);

    /** 进度监控
     * @param param
     * @param result
     */
    void progressMonitor(Data param, Data result);

    void getItemStampList(Data param, Data result);

    void getItemStampByBusinessArea(Data param, Data result);

    void getItemStampDetailByTableCode(Data param, Data result);

    void getItemStampDetailByItem(Data param, Data result);

    void getItemStampByPrintId(Data param, Data result);

    void retreatItemStamp(Data param,JSONObject json);

    void itemStamp(Data param, Data result);

    void getHqPrinter(Data param, Data result);

    void queryPrinterBg(Data param,Data result);

    void savePrinterBg(Data param,Data result);

    void calcHotpotNum(String tenancyId,int storeId,String billNum) throws Exception;

    void calcHotpotNum(String tenancyId,int storeId,String billNum,boolean negative) throws Exception;

    void baseDataPull(Data param, Data result);

    void getFreeSpace(Data result);

    void addForbiddenItem(Data param,Data result);

    void delForbiddenItem(Data param,Data result);

    void checkForbiddenItem(Data param,Data result);

    void updateItemComboDetailsDefault(Data param,Data result);

    void recoverItemComboDetailsDefault(Data param,Data result);

	void checkItemComboDetailsDefault(Data param,Data result);

    void updateSettingShiftsTimes(Data param,Data result);


    void updateTableState(String tenantId,Integer organId);


	/**
	 * 查询人员
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	void findEmployeeInfo(Data param, Data result)  throws Exception;

	void addEmployeeInfo(Data param, Data result) throws Exception;

	void updateEmployeeInfo(Data param, Data result) throws Exception;

	void delEmployeeById(Data param, Data result)  throws  Exception;

    JSONObject autoDailySettlement(String tenancyId, int storeId, JSONObject json) throws Exception;

    JSONObject findAutoDailyInfoByParam(String tenancyId, int storeId, JSONObject json) throws  Exception;

    void updateAutoDailyInfo(String tenancyId, int storeId, JSONObject json)throws  Exception;

    List<JSONObject> findAutoDailyInfo(String tenancyId, int storeId,int daily_count_num) throws  Exception;



}
