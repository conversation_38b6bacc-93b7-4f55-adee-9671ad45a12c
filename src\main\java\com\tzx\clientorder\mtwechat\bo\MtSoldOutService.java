package com.tzx.clientorder.mtwechat.bo;

import net.sf.json.JSONObject;

import java.util.Date;
import java.util.List;

/**
 * Created by qingui on 2018-05-31.
 */
public interface MtSoldOutService {
    String NAME = "com.tzx.clientorder.mtwechat.bo.MtSoldOutServiceImp";

    /**
     * 美团的估清（只调用估清接口）
     * @param tenancyId
     * @throws Exception
     */
    void soldOut(String tenancyId, int storeId, Date reportDate) throws Exception;

    /**
     * 美团的估清(估清调用，包含删除门店的全部估清)
     * @param tenancyId
     * @param storeId
     * @throws Exception
     */
    void soldOutAll(String tenancyId, int storeId, Date reportDate, List<JSONObject> dishs) throws Exception;
    
    /** 美团的估清(估清调用，包含删除门店的全部估清)
     * @param tenancyId
     * @param storeId
     * @param reportDate
     * @param itemIds 历史估清菜品
     * @throws Exception
     */
    void soldOutAll(String tenancyId, int storeId, Date reportDate, String itemIds) throws Exception;

    /**
     * 取消沽清（门店退沽清时，调用美团的取消沽清）
     * @param tenancyId
     * @param dishs
     * @throws Exception
     */
    void cancelSoldOut(String tenancyId, int storeId, List<JSONObject> dishs) throws Exception;
    
    /**
     * @param tenancyId
     * @param storeId
     * @param itemIds
     * @throws Exception
     */
    void cancelSoldOut(String tenancyId, int storeId, String itemIds) throws Exception;
}
