package com.tzx.base.listener;

import com.tzx.base.service.servlet.ProcessMessage;
import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.bo.PosService;
import com.tzx.pos.po.springjdbc.dao.CloudBillDao;
import net.sf.json.JSONObject;
import org.apache.log4j.Logger;
import org.springframework.context.ApplicationContext;

import javax.servlet.ServletContext;
import java.util.*;

/**
 * 定时任务 门店结账轮询超时
 */
public class PosCloudLockTask extends Thread{
    private ServletContext context;
    private ApplicationContext contextspring	= SpringConext.getApplicationContext();
    private Logger logger		= Logger.getLogger(ProcessMessage.class);
    private static int outTime=3;
    public PosCloudLockTask()
    {
        // this.context = context;
    }

    public PosCloudLockTask(ServletContext context)
    {
        this.context = context;
    }
    public void run()
    {
        Map<String, String> systemMap = Constant.getSystemMap();
        String tenent_id= systemMap.get("tenent_id");
        String store_id= systemMap.get("store_id");
        DBContextHolder.setTenancyid(tenent_id);
        if(!tenent_id.isEmpty()&&!store_id.isEmpty()){
            this.findTimeOutLock(tenent_id,Integer.parseInt(store_id));
        }

    }
    public void findTimeOutLock(String tenent_id,Integer store_id){
        StringBuffer sb=new StringBuffer("select *  from pos_bill_lock where  date_part('minute',(now()-lock_time ))>"+outTime+" and lock_state='1' and bill_state='0' and tenancy_id=? and store_id=?");
        CloudBillDao cloudBillDao = (CloudBillDao) SpringConext.getApplicationContext().getBean(CloudBillDao.NAME);
        try {
            List<JSONObject> list=cloudBillDao.query4Json(tenent_id,sb.toString(),new Object[]{tenent_id,store_id});
            if(list.size()>0){
                this.unlocking(cloudBillDao,list);
            }
        } catch (Exception e) {
            logger.error("定时任务 门店结账轮询挂了，挂了------",e);
        }

    }
    public void unlocking(CloudBillDao cloudBillDao,List<JSONObject> list){
        for(JSONObject jsonObject:list){
            try {
                String lock_type=jsonObject.optString("lock_type","WX01");
                if(lock_type!=null&&!"MD01".equals(lock_type)){
                    cloudBillDao.forcedUnlocking(jsonObject.optString("bill_num"),jsonObject.optString("tenancy_id"),jsonObject.optInt("store_id"),new Date(), SysDictionary.CHANEL_MD01,0,"",0);
                    this.unlockMd(cloudBillDao,jsonObject);
                }
            } catch (Exception e) {
                logger.error(jsonObject.optString("bill_num")+"这个账单挂了---轮询解锁的时侯--",e);
            }
        }
    }

    /**
     * 门店解锁
     * @param
     */
    public Data unlockMd(CloudBillDao cloudBillDao,JSONObject jsonObjectTmp){
        String billnum=jsonObjectTmp.optString("bill_num");
         String chanel=SysDictionary.CHANEL_MD01;
         String tenancyId=jsonObjectTmp.optString("tenancy_id");
         Integer storeId=jsonObjectTmp.optInt("store_id");
         Integer customer_id=jsonObjectTmp.optInt("customer_id");
        String openId=jsonObjectTmp.optString("open_id");
        String nowTime= DateUtil.getNowDateYYDDMMHHMMSS();
        String state="1";
        Data result=Data.get(tenancyId,storeId,0);
        try{
            List<JSONObject> list=cloudBillDao.findBillInfo(billnum,tenancyId,storeId);
            if(list!=null&& list.size()>0){
                JSONObject jsonObject=list.get(0);
                if(!jsonObject.isEmpty()){
                    String report_date=jsonObject.optString("report_date","");
                    String table_code=jsonObject.optString("table_code","");
                    Integer shift_id=jsonObject.optInt("shift_id",0);
                    Data data=Data.get(tenancyId,storeId,0);
                    List tablesLst=new ArrayList();
                    Map tabes=new HashMap();
                    tabes.put("table_code",table_code);
                    tablesLst.add(tabes);
                    List dataLst=new ArrayList();
                    Map dataMap=new HashMap();
                    dataMap.put("report_date",report_date);
                    dataMap.put("pos_num","WX01");
                    dataMap.put("nowTime",nowTime);
                    dataMap.put("shift_id",shift_id);
                    dataMap.put("opt_num","WX01");
                    dataMap.put("open_id",openId);
                    dataMap.put("customer_id",customer_id);
                    dataMap.put("chanel",chanel);
                    dataMap.put("tables",tablesLst);
                    dataMap.put("mode",state);
                    dataLst.add(dataMap);
                    data.setData(dataLst);
                    data.setSource(chanel);
                    data.setOper(Oper.check);
                    data.setSecret("0");
                    data.setType(Type.CLOCK_TABLE);
                    logger.info("门店解单-----billnum="+billnum+">>tenancy_id="+tenancyId+"--storeId"+storeId+"--customer_id="+customer_id+"--open_id="+openId+"---state="+state+"----tables="+tablesLst.toString()+"====chanel==="+chanel);
                    PosService posService = (PosService) SpringConext.getApplicationContext().getBean(PosService.NAME);
                    posService.lockOrUnlockTable(data, result);
                }
            }
        }catch (Exception e){
            logger.error("门店解单挂了",e);
            result.setMsg("门店解单挂了");
        }
        return result;
    }
}
