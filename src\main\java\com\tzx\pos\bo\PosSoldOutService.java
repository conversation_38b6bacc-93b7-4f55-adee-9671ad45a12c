package com.tzx.pos.bo;

import java.util.List;

import net.sf.json.JSONObject;

import com.tzx.framework.common.entity.Data;

/**
 * 估清处理
 * 
 * <AUTHOR>
 *
 */
public interface PosSoldOutService extends PosBaseService
{
	String	NAME	= "com.tzx.pos.bo.imp.PosSoldOutServiceImp";

	/**
	 * 设置估清
	 * 
	 * @param param
	 * @param result
	 * @param soldList
	 * @throws Exception
	 */
	public void setSoldOut(Data param, Data result) throws Exception;
	
	/**
	 * 查询沽清
	 * 
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> findSoldOut(Data param) throws Exception;
	
	/**
	 * 更改沽清数量
	 * 
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public Data modSoldOutCount(Data param) throws Exception;
}
