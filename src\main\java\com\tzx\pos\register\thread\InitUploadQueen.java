package com.tzx.pos.register.thread;

import javax.annotation.Resource;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import com.tzx.pos.register.service.RegisterService;

@Controller("uploadSaas")
public class InitUploadQueen implements InitializingBean {
	@Resource(name = RegisterService.NAME)
	private RegisterService registerService;
	@Autowired
	private UploadQueen queen;

	@Override
	public void afterPropertiesSet() throws Exception {
		
		Thread threadPutQueen = new PutQueen(queen, registerService);
		threadPutQueen.start();
		Thread threadGetQueen = new GetQueen(queen, registerService);
		threadGetQueen.start();

	}

}
