package com.tzx.framework.common.newprint;

import java.net.SocketException;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import net.sf.json.JSONObject;

import org.apache.log4j.Logger;

import com.tzx.framework.common.util.dao.GenericDao;

public class NewPrintJob
{
	private String		tenantId;

	private GenericDao	dao;

	private Integer printer_id = 0;
		
	private Integer taskId = 0;
	
	private Logger		logger	= Logger.getLogger(NewPrintJob.class);

	public NewPrintJob(String tenantId, GenericDao dao)
	{
		this.dao = dao;

		this.tenantId = tenantId;
	}
	
	public void printJOB(JSONObject printJob)
	{
		try
		{
			StringBuilder sb = new StringBuilder();

			String print_format = printJob.optString("print_format");
			JSONObject printparams = printJob.optJSONObject("params");
			taskId = printJob.optInt("task_id");
			printer_id = printparams.optInt("printer_id");
			String printer_name = printJob.optString("print_name");
			sb.setLength(0);
			sb.append("select * from hq_printer_new where id=" + printer_id);
			List<JSONObject> listprint = this.dao.query4Json(tenantId, sb.toString());
			JSONObject spare_print = null;
			if (listprint.size() > 0)
			{
				
				JSONObject printer = listprint.get(0);
				printer_name = printer.optString("name");
				int printer_type = printer.optInt("type");
				String ip_com = printer.optString("ip_com");
				int baud_rate = printer.optInt("baud_rate");
				int spare_id = printer.optInt("spare_id");
				sb.setLength(0);
				sb.append("select zz.is_enables,zz.print_num,spfn.format_name,spfn.page_size,spfn.content from( select hpm.format_id,hpm.is_enables,hpm.print_num from hq_printer_model hpm where hpm.id=" + printer_id + " and hpm.format_type='" + printer_type
						+ "') zz left JOIN sys_print_format_new spfn on zz.format_id=spfn.id");
				List<JSONObject> printformatList = this.dao.query4Json(tenantId, sb.toString());
				sb.setLength(0);
				sb.append("select * from hq_printer_new where id=" + spare_id);
				List<JSONObject> listspare = this.dao.query4Json(printer_name, sb.toString());
				if(listspare.size()>0)
				{
					spare_print = listspare.get(0);
				}

				JSONObject printformat = printformatList.get(0);
				int is_enables = printformat.optInt("is_enables");
				int print_num = printformat.optInt("print_num");
				int page_size = printformat.optInt("page_size");

				String htmlTem = printformat.optString("content");
				if (is_enables > 0 && print_num > 0)
				{
					sb.setLength(0);
					sb.append("select result_set_title,sql from sys_print_format_detail where parent_id=-" + print_format);
					List<JSONObject> listsql = this.dao.query4Json(tenantId, sb.toString());
					Map<String, List<JSONObject>> params = new HashMap<String, List<JSONObject>>();

					for (JSONObject josql : listsql)
					{
						String result_set_title = josql.optString("result_set_title");
						String sql = josql.optString("sql");
						@SuppressWarnings("unchecked")
						Iterator<String> keys = printparams.keys();
						while (keys.hasNext())
						{
							String key = keys.next();
							String value = printparams.optString(key);
							sql = sql.replace("{" + key + "}", value);

						}
						try
						{
							List<JSONObject> paramList = this.dao.query4Json(tenantId, sql);
							params.put(result_set_title, paramList);
						}
						catch (Exception e)
						{
							try
							{
								String uPitem = new String("update pos_print_task set state = '3' and print_result=?  where task_id = ?");
								this.dao.getJdbcTemplate(tenantId).update(uPitem, new Object[]
								{ "打印sql错误：" + sql, taskId });
							}
							catch (Throwable e2)
							{
								logger.error("修改打印状态失败:", e2);
							}
							
							logger.info("打印sql错误：" + sql);
						}
						
						Constant.STOPOBJ.put(printer_id, printJob);
						print(printer_name, ip_com, baud_rate, printer_type, page_size, htmlTem, params, print_num, taskId,spare_print);
					}
				}
			}
			else
			{
				logger.info("NewPrint 打印机ID不存在：" + printer_id);
			}
		}
		catch (Throwable e)
		{
			if(Constant.JOBSTOPTIMES.contains(taskId) && Constant.JOBSTOPTIMES.get(taskId)>2)
			{
				Constant.clear(printer_id);
				try
				{
					String uPitem = new String("update pos_print_task set state = '3' and print_result=?  where task_id = ?");
					this.dao.getJdbcTemplate(tenantId).update(uPitem, new Object[]
					{ "连续错误超过3次", taskId });
				}
				catch (Throwable e2)
				{
					logger.error("修改打印状态失败:", e2);
				}
			}
			else
			{
				Constant.setPrint(printer_id, taskId);
			}
			
			e.printStackTrace();
		}

		
		
	}

	public void print()
	{
		try
		{
			StringBuilder sb = new StringBuilder();
			//sb.append("select tenancy_id,COALESCE(store_id,0) as store_id,id,COALESCE(print_type,'') as print_type,COALESCE(print_format,'') as print_format,COALESCE(print_property,'') as print_property,COALESCE(printer_id,0) as printer_id,COALESCE(printer_name,'') as printer_name,COALESCE(bill_num,'') as bill_num,COALESCE(table_name,'') as table_name,guest,COALESCE(table_property_id,0),COALESCE(table_property_tag,'') as table_property_tag,waiter_num,shift_name,print_tag,remark,print_time,rwid from pos_print where print_tag='*' order by id asc limit 20");
			sb.append("select task_id,print_name,state,params,print_times,print_result from pos_print_task ORDER BY task_id asc LIMIT 20");
			//{"printer_id":"31","type":"3","print_format":"'1106'","id":"3204","bill_num":"34420160813000139","store_id":344,"tenancy_id":"hdl"}
			
			// 打印队列
			List<JSONObject> list = this.dao.query4Json(tenantId, sb.toString());
			for (JSONObject printJob : list)
			{
				String print_format = printJob.optString("print_format");
				JSONObject printparams = printJob.optJSONObject("params");
				taskId = printJob.optInt("task_id");
				if(printparams==null)
				{
					logger.info("NewPrin打印任务参数params有误：" + printJob.toString());
					try
					{
						String uPitem = new String("update pos_print_task set state = '3' and print_result=?  where task_id = ?");
						this.dao.getJdbcTemplate(tenantId).update(uPitem, new Object[]
						{ "NewPrin打印任务参数params有误", taskId });
					}
					catch (Throwable e)
					{
						logger.error("修改打印状态失败:", e);
					}
					continue;
				}
				
				printer_id = printparams.optInt("printer_id");
				String printer_name = printJob.optString("print_name");
			
				
				if (print_format.length() == 0 || printer_id == 0)
				{
					try
					{
						String uPitem = new String("update pos_print_task set state = '3' and print_result=?  where task_id = ?");
						this.dao.getJdbcTemplate(tenantId).update(uPitem, new Object[]
						{ "NewPrin打印任务参数有误", taskId });
					}
					catch (Throwable e)
					{
						logger.error("修改打印状态失败:", e);
					}
					logger.info("NewPrin打印任务参数有误：" + printJob.toString());
					continue;
				}
				sb.setLength(0);
				sb.append("select * from hq_printer_new where id=" + printer_id);
				List<JSONObject> listprint = this.dao.query4Json(tenantId, sb.toString());
				JSONObject spare_print = null;
				if (listprint.size() > 0)
				{
					
					JSONObject printer = listprint.get(0);
					printer_name = printer.optString("name");
					int printer_type = printer.optInt("type");
					String ip_com = printer.optString("ip_com");
					int baud_rate = printer.optInt("baud_rate");
					int spare_id = printer.optInt("spare_id");
					int valid_state = printer.optInt("valid_state");
					if (valid_state == 0)
					{
						try
						{
							String uPitem = new String("update pos_print_task set state = '3' and print_result=?  where task_id = ?");
							this.dao.getJdbcTemplate(tenantId).update(uPitem, new Object[]
							{ "NewPrint 打印机已失效", taskId });
						}
						catch (Throwable e)
						{
							logger.error("修改打印状态失败:", e);
						}
						logger.info("NewPrint 打印机已失效：" + printer_id + "[" + print_format + "]");
						continue;
					}
					sb.setLength(0);
					sb.append("select zz.is_enables,zz.print_num,spfn.format_name,spfn.page_size,spfn.content from( select hpm.format_id,hpm.is_enables,hpm.print_num from hq_printer_model hpm where hpm.id=" + printer_id + " and hpm.format_type='" + printer_type
							+ "') zz left JOIN sys_print_format_new spfn on zz.format_id=spfn.id");
					List<JSONObject> printformatList = this.dao.query4Json(tenantId, sb.toString());
					if (print_format.length() == 0)
					{
						try
						{
							String uPitem = new String("update pos_print_task set state = '3' and print_result=?  where task_id = ?");
							this.dao.getJdbcTemplate(tenantId).update(uPitem, new Object[]
							{ "NewPrint 打印模板不存在", taskId });
						}
						catch (Throwable e)
						{
							logger.error("修改打印状态失败:", e);
						}
						
						logger.info("NewPrint 打印模板不存在：" + printer_id + "[" + print_format + "]");
						continue;
					}
					sb.setLength(0);
					sb.append("select * from hq_printer_new where id=" + spare_id);
					List<JSONObject> listspare = this.dao.query4Json(printer_name, sb.toString());
					if(listspare.size()>0)
					{
						spare_print = listspare.get(0);
					}

					JSONObject printformat = printformatList.get(0);
					int is_enables = printformat.optInt("is_enables");
					int print_num = printformat.optInt("print_num");
					int page_size = printformat.optInt("page_size");

					String htmlTem = printformat.optString("content");
					if (is_enables > 0 && print_num > 0)
					{
						sb.setLength(0);
						sb.append("select result_set_title,sql from sys_print_format_detail where parent_id=-" + print_format);
						List<JSONObject> listsql = this.dao.query4Json(tenantId, sb.toString());
						Map<String, List<JSONObject>> params = new HashMap<String, List<JSONObject>>();

						for (JSONObject josql : listsql)
						{
							String result_set_title = josql.optString("result_set_title");
							String sql = josql.optString("sql");
							@SuppressWarnings("unchecked")
							Iterator<String> keys = printparams.keys();
							while (keys.hasNext())
							{
								String key = keys.next();
								String value = printparams.optString(key);
								sql = sql.replace("{" + key + "}", value);

							}
							try
							{
								List<JSONObject> paramList = this.dao.query4Json(tenantId, sql);
								params.put(result_set_title, paramList);
							}
							catch (Exception e)
							{
								try
								{
									String uPitem = new String("update pos_print_task set state = '3' and print_result=?  where task_id = ?");
									this.dao.getJdbcTemplate(tenantId).update(uPitem, new Object[]
									{ "打印sql错误：" + sql, taskId });
								}
								catch (Throwable e2)
								{
									logger.error("修改打印状态失败:", e2);
								}
								
								logger.info("打印sql错误：" + sql);
							}
							JSONObject stopJob = Constant.getStopJob(printer_id);
							if(stopJob!=null)
							{
								printJOB(stopJob);
							}
							Constant.STOPOBJ.put(printer_id, printJob);
							print(printer_name, ip_com, baud_rate, printer_type, page_size, htmlTem, params, print_num, taskId,spare_print);
						}
					}
				}
				else
				{
					logger.info("NewPrint 打印机ID不存在：" + printer_id);
				}
			}
		}
		catch (Throwable e)
		{
			if(Constant.JOBSTOPTIMES.contains(taskId) && Constant.JOBSTOPTIMES.get(taskId)>2)
			{
				Constant.clear(printer_id);
				try
				{
					String uPitem = new String("update pos_print_task set state = '3' and print_result=?  where task_id = ?");
					this.dao.getJdbcTemplate(tenantId).update(uPitem, new Object[]
					{ "连续错误超过3次", taskId });
				}
				catch (Throwable e2)
				{
					logger.error("修改打印状态失败:", e2);
				}
			}
			else
			{
				Constant.setPrint(printer_id, taskId);
			}
			
			e.printStackTrace();
		}

	}

	public void print(String printerName, String ip, int port, int type, int page_size, String htmlTem, Map<String, List<JSONObject>> params, int copies, int taskId,JSONObject spare_print) throws Exception
	{
		if (type == 0)
		{
			htmlTem = HtmlImageGenerators.transformationHtmlTemplate3(page_size, 25, htmlTem, params);
			try
			{
				EscPos.print(ip, port, copies, htmlTem,printer_id);
			}
			catch (SocketException e)
			{
				if(spare_print!=null)
				{
					String printer_name = spare_print.optString("name");
					int printer_type = spare_print.optInt("type");
					String ip_com = spare_print.optString("ip_com");
					int baud_rate = spare_print.optInt("baud_rate");
					int valid_state = spare_print.optInt("valid_state");
					if(printer_type==0 && ip_com.length()>0 && valid_state ==1)
					{
						logger.info("NewPrint-启用备用打印机");
						EscPos.print(ip, baud_rate, copies, htmlTem,printer_id);
						
					}
					else if(printer_type==1 && printer_name.length()>0 && valid_state ==1)
					{
						logger.info("NewPrint-启用备用打印机");
						htmlTem = HtmlImageGenerators.transformationHtmlTemplate3(page_size, 25, htmlTem, params);
						HtmlImageGenerators.print2(printerName, copies, htmlTem,printer_id);
					}
				}
			}
		}
		else
		{
			htmlTem = HtmlImageGenerators.transformationHtmlTemplate3(page_size, 25, htmlTem, params);
			int pct = HtmlImageGenerators.print2(printerName, copies, htmlTem,printer_id);
			if(pct==0)
			{
				String printer_name = spare_print.optString("name");
				int printer_type = spare_print.optInt("type");
				String ip_com = spare_print.optString("ip_com");
				int baud_rate = spare_print.optInt("baud_rate");
				int valid_state = spare_print.optInt("valid_state");
				if(printer_type==0 && ip_com.length()>0 && valid_state ==1)
				{
					logger.info("NewPrint-启用备用打印机");
					EscPos.print(ip, baud_rate, copies, htmlTem,printer_id);
					
				}
				else if(printer_type==1 && printer_name.length()>0 && valid_state ==1)
				{
					logger.info("NewPrint-启用备用打印机");
					htmlTem = HtmlImageGenerators.transformationHtmlTemplate3(page_size, 25, htmlTem, params);
					HtmlImageGenerators.print2(printerName, copies, htmlTem,printer_id);
				}
			}
		}

		logger.info("进入-NewPrint-打印方法-打印成功：");
		Constant.clear(printer_id);
		try
		{
			String uPitem = new String("update pos_print_task set state = ? where task_id = ?");
			this.dao.getJdbcTemplate(tenantId).update(uPitem, new Object[]
			{ "4", taskId });
			logger.info("打印完成（ID:" + taskId + "）");
		}
		catch (Throwable e)
		{
			logger.error("修改打印状态失败:", e);
		}

	}

}
