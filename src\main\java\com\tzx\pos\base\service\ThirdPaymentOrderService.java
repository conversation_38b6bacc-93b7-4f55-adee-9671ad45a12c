package com.tzx.pos.base.service;

import java.util.List;

import com.tzx.pos.bo.dto.PosThirdPaymentOrderEntity;

import net.sf.json.JSONObject;

public interface ThirdPaymentOrderService
{
	String	NAME	= "com.tzx.pos.base.service.impl.ThirdPaymentOrderServiceImp";

	/**
	 * 第三方支付状态查询
	 * 
	 * @param params
	 * @param printJson
	 * @return
	 * @throws Exception
	 */
	public void queryThirdPaymentForRunnable(String tenancyId, int storeId, PosThirdPaymentOrderEntity params) throws Exception;

	/**
	 * 获取未完成支付记录(轮询)
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @return
	 * @throws Exception
	 */
	public List<PosThirdPaymentOrderEntity> getThirdPaymentOrderList(String tenancyId, int storeId, Double queryCount) throws Exception;

//	/**
//	 * 修改第三方支付状态
//	 * 
//	 * @param tenancyId
//	 * @param storeId
//	 * @throws Exception
//	 */
//	public void updateThirdPaymentOrderListState(String tenancyId, int storeId, int paymentId, int queryCount) throws Exception;

	/**
	 * MQ回调
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param data
	 * @throws Exception
	 */
	public void thirdPaymentCallback(String tenancyId, int storeId, JSONObject data) throws Exception;
}
