package com.tzx.pos.po.springjdbc.dao;

import com.tzx.pos.base.dao.BaseDao;
import net.sf.json.JSONObject;

import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017-12-27.
 */
public interface CloudBillDao extends BaseDao {
    String	NAME	= "com.tzx.pos.po.springjdbc.dao.imp.CloudBillDaoImp";
    /**
     *
     * @param tenancy_id
     * @param store_id
     * @param table_code
     * @param bill_num
     * @return
     * @throws Exception
     */
    public JSONObject findCloudBill(String tenancy_id, int store_id, String table_code, String bill_num) throws Exception;

    /**
     *
     * @param tenancy_id
     * @param store_id
     * @param bill_num
     * @return
     * @throws Exception
     */
    public int selectBillItemCountByBillNum(String tenancy_id, int store_id, String bill_num) throws Exception;

    /**
     *
     * @param tenancy_id
     * @param store_id
     * @param bill_num
     * @return
     * @throws Exception
     */
    public JSONObject queryBillByBillNum(String tenancy_id, int store_id, String bill_num) throws Exception;

    /**
     * 查询锁单表信息
     * @param bill_num
     * @param tenantId
     * @param store_id
     * @return
     * @throws Exception
     */
    public List findLockData(String bill_num, String tenantId, Integer store_id) throws Exception;

    /**
     * 强制解锁账单
     * @param bill_num
     * @param tenantId
     * @param store_id
     * @throws Exception
     */
    public int forcedUnlocking(String bill_num, String tenantId, Integer store_id, Date unlockTime, String chanel, Integer lock_state,String openId,Integer customerId) throws Exception;

    /**
     * 关闭锁单的账单
     * @param bill_num
     * @param tenantId
     * @param store_id
     * @return
     * @throws Exception
     */
    public int closedBillUnlocking(String bill_num,String tenantId,Integer store_id) throws Exception;

    /**
     * 首次锁台需要插入数据库数据
     * @param data
     * @return
     * @throws Exception
     */
    public int insertData(Object[] data)throws Exception;

    /**
     * 同步账单相关信息
     * @param tenancyId
     * @param storeId
     * @param json
     * @return
     * @throws Exception
     */
	boolean syncBillInfo(String tenancyId, int storeId, String billNum, String reportDate, String posNum, String cashierNum, Integer shiftId, JSONObject json) throws Exception;

    /**
     * 根据桌台号查订单号
     * @param tenancyId
     * @param storeId
     * @param tableCode
     * @return
     * @throws Exception
     */
    String findBillNum(String tenancyId, int storeId, String tableCode) throws Exception;
    /**
     * 根据订单号查询订单信息
     * @return
     * @throws Exception
     */
    List<JSONObject> findBillInfo(String billnum,String tenancyId,Integer storeId)throws Exception;
    /**
     * 插入pos_bill_sub表
     * @param tenancyId
     * @param storeId
     * @param jsonObject
     * @return
     * @throws Exception
     */
    int insertPosBillSub(String tenancyId, int storeId, JSONObject jsonObject) throws Exception;

    /**
     * 根据账单号查询pos_bill_sub
     * @param tenancyId
     * @param storeId
     * @param billnum
     * @return
     * @throws Exception
     */
    List<JSONObject> findPosBillSub(String tenancyId,int storeId, String billnum)throws Exception;

    /**
     * 查询菜品数量
     * @param tenancyId
     * @param storeId
     * @param billNum
     * @param itemRemark
     * @return
     * @throws Exception
     */
    int getBillItemCount(String tenancyId, int storeId, String billNum, String itemRemark) throws Exception;

    /**
     *
     * @param tenancy_id
     * @param store_id
     * @param bill_num
     * @return
     * @throws Exception
     */
    public int selectBillItemCountByBillNum4Give(String tenancy_id, int store_id, String bill_num) throws Exception;

    /**
     * 查询账单及附表的信息
     * @param tenancyId
     * @param storeId
     * @param billNum
     * @return
     * @throws Exception
     */
    JSONObject getBillAndSubInfo(String tenancyId, int storeId, String billNum) throws Exception;

    /**
     * 更新账单表备注
     * @param tenancyId
     * @param storeId
     * @param billNum
     * @param remark
     * @throws Exception
     */
    void updateBillRemark(String tenancyId, int storeId, String billNum, String remark) throws Exception;
}
