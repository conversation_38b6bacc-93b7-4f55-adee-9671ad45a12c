package com.tzx.clientorder.wlifeprogram.common.constant;

public class WlifePromptMessage
{
	public static final int CODE_NULL_DATASET = 3; //数据集为空
	
	public static final String ORDER_DISH_FAILURE = "下单失败,未找到此账单对应的明细";
	
	public static final String UPLOAD_BILL_FAILURE = "上传订单失败";
	
	public static final String ORDER_PAYMENT_FAILURE = "结账失败";
	
	public static final String LOCK_ORDER_FAILURE = "锁台失败";
	
	public static final String UNLOCK_ORDER_FAILURE = "解锁失败";

	public static final String WLIFE_ORDER_SUCCESS = "微生活查询桌台订单成功";
	public static final String WLIFE_ORDER_FAILURE = "微生活查询桌台订单异常";

	public static final String WLIFE_ORGAN_SUCCESS = "微生活查询门店状态成功";
	public static final String WLIFE_ORGAN_FAILURE = "微生活查询门店状态异常";

	public static final String WLIFE_BASIC_SUCCESS = "微生活查询基础资料成功";
	public static final String WLIFE_BASIC_FAILURE = "微生活查询基础资料异常";
	public static final String WLIFE_BASIC_ERROR = "该门店没有对接微生活";
	public static final String NOT_EXIST_ORGAN_ERROR = "门店信息不存在";

	public static final String WLIFE_SOLD_OUT_SUCCESS = "微生活查询沽清信息成功";
	public static final String WLIFE_SOLD_OUT_FAILURE = "微生活查询沽清信息异常";
}
