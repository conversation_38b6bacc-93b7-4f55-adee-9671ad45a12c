package com.tzx.ios.po.springjdbc.dao.imp;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.tzx.ios.po.springjdbc.dao.PosSoldOutDao;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.dao.imp.BaseDaoImp;

import net.sf.json.JSONObject;

@Repository(PosSoldOutDao.NAME)
public class PosSoldOutDaoImp extends BaseDaoImp implements PosSoldOutDao
{

	@Override
	public List<JSONObject> findSoldOut(String tenancyId, Integer storeId) throws Exception
	{
		StringBuilder sql = new StringBuilder("select item_id,num,coalesce(item_unit_id,0) as item_unit_id,to_char(setdate,'yyyy-mm-dd') as setdate,soldout_type from pos_soldout where tenancy_id =? and store_id =?");
		return this.queryString4Json(tenancyId, sql.toString(), new Object[]
		{ tenancyId, storeId });
	}
}
