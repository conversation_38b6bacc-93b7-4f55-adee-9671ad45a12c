package com.tzx.pos.base.dao.imp;

import java.util.ArrayList;
import java.util.List;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Repository;

import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.dao.impl.GenericDaoImpl;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.dao.DataUploadDao;

/**
 * Created by Administrator on 2017-03-28.
 */
@Repository(DataUploadDao.NAME)
public class DataUploadImp extends GenericDaoImpl implements DataUploadDao{
    private Logger logger = Logger.getLogger(DataUploadImp.class);

	@Override
    public Data uploadData(String tenancy_id, String store_id, String old_table_code, String new_table_code, String old_bill_num, String new_bill_num) throws Exception{
            JSONObject result = new JSONObject();
            JSONObject organjson = getUUID(tenancy_id,store_id);
		    result.put("org_uuid", organjson.get("org_uuid"));
		    result.put("poi_id", organjson.get("poi_id"));
            //根据账单来获取桌台号
             if ((null == new_table_code || "null".equals(new_bill_num) || "".equals(new_table_code)) && StringUtils.isNotEmpty(new_bill_num)) {
            	 getTableCode(tenancy_id, store_id, new_bill_num, result);
			 }else {
				result.put("table_code",new_table_code);
			 }
             result.put("pos_bill",getResult(tenancy_id, store_id, new_table_code,new_bill_num));
             
             if (StringUtils.isEmpty(old_table_code) && StringUtils.isNotEmpty(old_bill_num)) {
            	 getTableCode(tenancy_id, store_id, old_bill_num, result);
			 }else {
				 result.put("old_table_code",old_table_code);
			 }
            result.put("old_pos_bill",getResult(tenancy_id, store_id, old_table_code,old_bill_num));
            Data data = Data.get();
            data.setTenancy_id(tenancy_id);
            data.setStore_id(Integer.valueOf(store_id));
            data.setSource("");
            data.setType(Type.AUTOUPLOADDATA);
            data.setOper(Oper.update);
            data.setMsg("查询账单成功");
            data.setCode(0);
            List<JSONObject> dataList = new ArrayList<JSONObject>();
            dataList.add(result);
            data.setData(dataList);
        return data;
    }
	
	/**
	 * 获取桌台号码
	 * @param tenancy_id
	 * @param store_id
	 * @param bill_num
	 * @param result
	 */
	private void getTableCode(String tenancy_id,String store_id,String bill_num,JSONObject result){
        	String sql = "SELECT b.table_code FROM pos_bill b where b.tenancy_id = ? and b.store_id = ? and b.bill_num = ? ";
        	SqlRowSet rs = jdbcTemplate.queryForRowSet(sql, tenancy_id,Integer.valueOf(store_id),bill_num);
        	while(rs.next()){
        		result.put("table_code",rs.getString("table_code"));
        	}
	}
	
	private JSONObject getUUID(String tenancy_id,String store_id){
		JSONObject obj = new JSONObject();
		String sql = "select org_uuid,poi_id  from organ where id =? ";
		SqlRowSet rs = jdbcTemplate.queryForRowSet(sql,Integer.valueOf(store_id));
		if (rs.next()) {
			obj.put("org_uuid",rs.getString("org_uuid"));
			obj.put("poi_id",rs.getString("poi_id"));
		}
		return obj;
	}
	
	
	private JSONObject getResult(String tenancy_id,String store_id,String table_code,String bill_num){
		try {
			if (StringUtils.isEmpty(bill_num)) {
				logger.warn("账单编号为空!");
				return new JSONObject();
			}
			StringBuilder sb = new StringBuilder();
			sb.append("select b.average_amount,b.batch_num,b.bill_num,b.bill_property,b.bill_state,b.bill_taste,b.cashier_num,b.source,b.copy_bill_num,b.difference,b.discount_amount,b.discount_case_id,dc.discount_case_name,");
			sb.append("b.discount_mode_id,b.discount_rate,b.discountk_amount,b.discountr_amount,b.fictitious_table,b.givi_amount,b.guest,b.guest_msg,b.id,b.maling_amount,b.open_opt,b.open_pos_num,b.opentable_time,b.order_num,");
			sb.append("b.payment_amount,b.payment_state,b.payment_time,b.pos_num,b.print_count,b.remark,b.report_date,b.serial_num,b.service_amount,b.service_id,ft.name as service_name,b.shift_id,b.subtotal,b.bill_amount,b.table_code,b.waiter_num, ");
			sb.append("(select round(coalesce(sum(bi.item_amount),0),2) from pos_bill_item bi where bi.tenancy_id=b.tenancy_id and bi.store_id=b.store_id and bi.bill_num=b.bill_num and (bi.item_remark<>'"+SysDictionary.ITEM_REMARK_FS02+"' or bi.item_remark is null) and bi.item_property<>'"+SysDictionary.ITEM_PROPERTY_MEALLIST+"') as subtotal2 ");
			sb.append(" from pos_bill b left join hq_discount_case dc on b.discount_case_id = dc.id  ");
			sb.append(" left join hq_service_fee_type ft on b.service_id = ft.id ");
			sb.append(" where b.bill_num = ? ");
			List<JSONObject> posbillList = this.query4Json(tenancy_id,sb.toString(),new  Object[]{bill_num});
			if(posbillList == null || posbillList.size()==0){
				logger.info("账单号："+bill_num + "查询结果为空!");
				throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_BILL);
			}
			String json = JSONObject.fromObject(posbillList.get(0)).toString();
			JSONObject ob = JSONObject.fromObject(json);
			sb.setLength(0);
			
			sb.append("select * from pos_bill_service where bill_num = ? ");
			List<JSONObject> feeList = this.query4Json(tenancy_id,sb.toString(),new Object[]{bill_num});
			if(feeList != null && feeList.size()>0){
				ob.put("servicelist",feeList);
			}else {
				JSONArray serList = new JSONArray();
				ob.put("servicelist",serList);
			}
			
			sb.setLength(0);
			sb.append("SELECT pb.assist_item_id,pb.assist_money,pb.assist_num,pb.batch_num,pb.details_id,pb.discount_amount,pb.discount_rate,pb.item_amount,pb.item_count,pb.item_id,");
			sb.append("pb.item_mac_id,pb.item_name,pb.item_num,pb.item_price,pb.item_property,pb.item_remark,pb.item_serial,pb.item_taste,pb.item_time,pb.item_unit_name,pb.method_money,");
			sb.append("pt.payment_state,pb.print_tag,pb.real_amount,pb.remark,pb.rwid,pb.sale_mode,pb.setmeal_id,pb.setmeal_rwid,pb.item_unit_id,pb.waitcall_tag,");
			sb.append("em.name as waiter_name ");
			sb.append(" FROM pos_bill_item pb LEFT JOIN pos_bill_payment pt on pb.bill_num = pt.bill_num LEFT JOIN employee em on pb.waiter_num = concat(em.id)");
			sb.append(" where pb.bill_num = ? and  pb.id not in(select id from pos_bill_item where item_remark='"+SysDictionary.ITEM_REMARK_TC01+"' or item_remark='"+SysDictionary.ITEM_REMARK_CJ05+"' or item_remark='"+SysDictionary.ITEM_REMARK_QX04+"' ) ");
			sb.append(" order by pb.item_serial,pb.assist_item_id");

			List<JSONObject> positemList = this.query4Json(tenancy_id,sb.toString(),new  Object[]{bill_num});
			if (positemList != null && positemList.size()>0) {
				String methodsql = "select * from pos_zfkw_item where bill_num = ?";
				for(JSONObject o : positemList){
					List<JSONObject> methodList = this.query4Json(tenancy_id, methodsql, new  Object[]{bill_num});
					o.put("method",methodList);
				}
			}
			
			ob.put("detaillist",positemList);
			
			sb.setLength(0);
			sb.append("select amount,batch_num,bill_code,cashier_num,count,customer_id,id,jzid,last_updatetime,name,number,phone,pos_num,remark,shift_id ");
			sb.append(" from pos_bill_payment where bill_num = ? ");
			List<JSONObject> paymentList = this.query4Json(tenancy_id,sb.toString(),new  Object[]{bill_num});
			ob.put("paymentlist",paymentList);
			
			sb.setLength(0);
			sb.append("select * from pos_bill_member where bill_num = ?");
			List<JSONObject> memberList = this.query4Json(tenancy_id, sb.toString(), new  Object[]{bill_num});
			ob.put("memberlist",memberList);
			
			return ob;
		} catch (Exception e) {
			logger.info("查询账单数据失败,"+e);
			e.printStackTrace();
		}
		return null;
	}
	
	@Override
	public String getPosSoldOutData(String tenent_id) {
		String data = "";
		String sql = "select tenancy_id as t,store_id as s,item_id as i,item_unit_id as u,num as n from pos_soldout";
		 try {
			List<JSONObject> list = query4Json(tenent_id, sql);
			JSONObject jsonObject=new JSONObject();
			if(list != null && list.size() > 0){
				jsonObject.put("data",list);
			}
			data=jsonObject.toString();
		} catch (Exception e) {
			logger.info("查询门店菜品估清数据失败,"+e);
			e.printStackTrace();
		}		
		return data;
	}
	
}
