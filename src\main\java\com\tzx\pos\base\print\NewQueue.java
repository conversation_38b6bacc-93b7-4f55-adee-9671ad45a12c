package com.tzx.pos.base.print;

import java.util.concurrent.ArrayBlockingQueue;
import org.apache.log4j.Logger;

/**
 *	打印队列，自动增加容量
 * <AUTHOR>
 * 2015年7月16日-下午5:12:35
 */
public class NewQueue
{
	private static final Logger	logger	= Logger.getLogger(NewQueue.class);
	
	// 是一个用数组实现的有界阻塞队列，其内部按先进先出的原则对元素进行排序，其中put方法和take方法为添加和删除的阻塞方法
	public static ArrayBlockingQueue<Object> queue = null;
	
	public NewQueue()
	{
		queue = new ArrayBlockingQueue<Object>(10000);
	}
	
	/**
	 * 这个参数Object可以换成JSONObject
	 * @param obj
	 * @throws InterruptedException 
	 */
	public void push(Object obj)
	{
		try {
			queue.put(obj);
		} catch (InterruptedException e) {
			logger.error(e.getMessage());
		}
	}
	
	/**
	 * 可以返回JSONObject
	 * @return
	 * @throws InterruptedException 
	 */
	public Object pull()
	{
		Object obj = null;
		try {
			obj = queue.take();
		} catch (InterruptedException e) {
			logger.error(e.getMessage());
		}
		return obj;
	}
	
	public int size()
	{
		return queue.size();
	}
	
	public boolean isEmpty()
	{
		if (queue.size() == 0)
		{
			return true;
		}else
			return false;
	}
	
//	//使用Vector不用考虑同步问题
//	private Vector queue = null;
//	
//	public NewQueue()
//	{
//		queue = new Vector();
//	}
//	/**
//	 * 这个参数Object可以换成JSONObject
//	 * @param obj
//	 */
//	public void push(Object obj)
//	{
//		queue.addElement(obj);
//	}
//	/**
//	 * 可以返回JSONObject
//	 * @return
//	 */
//	public Object pull()
//	{
//		if(queue.size()==0)
//		{
//			return null;
//		}
//		Object obj = queue.firstElement();
//		queue.removeElementAt(0);
//		return obj;
//	}
//	
//	public int size()
//	{
//		return queue.size();
//	}
//	
//	public boolean isEmpty()
//	{
//		if (queue.size() == 0)
//		{
//			return true;
//		}else
//			return false;
//	}
}
