package com.tzx.framework.common.newprint;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import net.sf.json.JSONObject;
import org.junit.Test;

public class PrintTest
{
	@Test
	public void test1() throws Exception
	{
		StringBuilder sb = new StringBuilder();
		sb.append("<table title=\"R1\" style=\"width:100%;\" cellpadding=\"2\" cellspacing=\"0\" border=\"1\" displayhead=\"1\">");
		sb.append("<tbody>");
		sb.append("<tr>");
		sb.append("<td style=\"border-width:1px;border-style:solid;\">");
		sb.append("312312312231213213213123213213213213213213");
		sb.append("</td>");
		sb.append("<td>");
		sb.append("3213213213213213123");
		sb.append("</td>");
		sb.append("<td>");
		sb.append("1233123321321321313123123");
		sb.append("</td>");
		sb.append("</tr>");
		sb.append("<tr>");
		sb.append("<td>");
		sb.append("<a title=\"R1:a\" src=\"http://127.0.0.1:8080/tzxsaas/img/main/logo.png\" data-text=\"账单号\" ><img src=\"http://127.0.0.1:8080/tzxsaas/img/my_icons/sjj0.gif\" /> </a>");
		sb.append("</td>");
		sb.append("<td>");
		sb.append("<a title=\"R1:b\" src=\"http://127.0.0.1:8080/tzxsaas/img/main/logo.png\" data-text=\"账单号\" ><img src=\"http://127.0.0.1:8080/tzxsaas/img/my_icons/sjj0.gif\" /> </a>");
		sb.append("</td>");
		sb.append("<td>");
		sb.append("<a title=\"R1:c\" src=\"http://127.0.0.1:8080/tzxsaas/img/main/logo.png\" data-text=\"账单号\" ><img src=\"http://127.0.0.1:8080/tzxsaas/img/my_icons/sjj0.gif\" /> </a>");
		sb.append("</td>");
		
		
		
		
		
		sb.append("</tr>");
		sb.append("</tbody>");
		sb.append("</table>");
		//sb.append("<img src=\"http://127.0.0.1:8080/tzxsaas/img/sms/1.png\" /> ");
		sb.append("123");
	    sb.append("<br />");
		Map<String,List<JSONObject>> map=new HashMap<String,List<JSONObject>>();
		List<JSONObject> list = new ArrayList<JSONObject>();
		for(int i =0;i<1;i++)
		{
			JSONObject jo = new JSONObject();
			jo.put("a", "a"+i);
			jo.put("b", "b"+i);
			jo.put("c", "c"+i);
			list.add(jo);
		}
		map.put("R1", list);
		
		String aaString = HtmlImageGenerators.transformationHtmlTemplate3(80,25, sb.toString(), map);
		
//		System.out.println(aaString);
//		
//		return;
		//HtmlImageGenerators.html2bif(aaString, "12345678.png");
		HtmlImageGenerators.print2("80mm2", 1, aaString,1);
//		HtmlImageGenerators.print(printer, copy, html);
//		EscPos.print("192.168.40.229", 9100, 1, aaString);
//		System.out.println("123xxxxxxxxxxxxx");
		
		
		
	}
	
	

}
