package com.tzx.ios.po.springjdbc.dao;

import java.util.List;

import com.tzx.pos.base.dao.BaseDao;

import net.sf.json.JSONObject;

public interface PosSoldOutDao extends BaseDao
{
	String	NAME	= "com.tzx.ios.po.springjdbc.dao.imp.PosSoldOutDaoImp";
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> findSoldOut(String tenancyId, Integer storeId) throws Exception;
}
