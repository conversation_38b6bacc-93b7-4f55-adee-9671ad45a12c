package com.tzx.pos.service.servlet;

import com.tzx.framework.common.entity.Data;

/**
 * 会员发卡接口
 * 
 * <AUTHOR> email:she<PERSON><PERSON><PERSON>@tzx.com.cn
 * @version 1.0 2017-10-25
 * @see
 * @since JDK7.0
 * @update
 */
public interface CustomerCardActivationServlet {
	String	NAME	= "com.tzx.pos.service.servlet.imp.CustomerCardActivationServletImp";
	/**
	 * 会员卡发卡
	 * @param tenancyId
	 * @param storeId
	 * @param param
	 * @param resData
	 * @param operType
	 * @throws Exception
	 */
	public Data customerCardActivation(Data params) throws Exception;

	/**
	 * 会员发卡状态
	 * @param tenancyId
	 * @param storeId
	 * @param requestData
	 * @param resData
	 * @return
	 * @throws Exception
	 */
	public Data customerCardActivationStatus(Data paramJson) throws Exception;

	/**
	 * 会员发卡获取二维码
	 * 
	 * @param params
	 * @param path
	 * @return
	 * @throws Exception
	 */
	public Data customerCardThirdPaymentPrecreate(Data params, String path) throws Exception;
	
	/**
	 * 会员卡取消支付
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param paramJson
	 * @param resData
	 * @param operType
	 * @return
	 * @throws Exception 
	 */
	public Data customerCardCancelThirdPayment(Data params) throws Exception;
	
	
	/**
	 * 会员卡扫码支付 被扫支付(B扫C)
	 * 
	 * @param params
	 * @param jsobj
	 * @return
	 * @throws Exception
	 */
	public Data customerCardThirdPaymentBarcode(Data params) throws Exception;
	
}
