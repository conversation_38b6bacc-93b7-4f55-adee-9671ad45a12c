package com.tzx.pos.po.springjdbc.dao.imp;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;

import net.sf.json.JSONObject;

import org.apache.log4j.Logger;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import com.tzx.base.entity.PosBill;
import com.tzx.base.entity.PosBillItem;
import com.tzx.base.entity.PosBillPayment;
import com.tzx.base.entity.PosBillService;
import com.tzx.base.entity.PosMethodItem;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.dao.imp.BaseDaoImp;
import com.tzx.pos.base.util.ObjectMappingUtil;
import com.tzx.pos.po.springjdbc.dao.PosBaseDao;

@Repository(PosBaseDao.NAME)
public class PosBaseDaoImp extends BaseDaoImp implements PosBaseDao
{
	
	private static final Logger	logger	= Logger.getLogger(PosBaseDaoImp.class);

	@Override
	public List<PosBill> getPosBillByBillnum(String tenantId, Integer storeId, String billNum) throws Exception
	{
		// TODO Auto-generated method stub
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("select ").append(ObjectMappingUtil.getColumnSqlByBean(PosBill.class)).append(" from pos_bill ");
		sqlStr.append("where tenancy_id=? and store_id=? and bill_num=?");
		return this.jdbcTemplate.query(sqlStr.toString(),new Object[]{tenantId,storeId,billNum}, BeanPropertyRowMapper.newInstance(PosBill.class));
	}

	@Override
	public List<PosBillItem> getPosBillItemByBillnum(String tenantId, Integer storeId, String billNum) throws Exception
	{
		// TODO Auto-generated method stub
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("select ").append(ObjectMappingUtil.getColumnSqlByBean(PosBillItem.class)).append(" from pos_bill_item ");
		sqlStr.append("where tenancy_id=? and store_id=? and bill_num=?");
		return this.jdbcTemplate.query(sqlStr.toString(),new Object[]{tenantId,storeId,billNum}, BeanPropertyRowMapper.newInstance(PosBillItem.class));
	}

	@Override
	public List<PosBillPayment> getPosBillPaymentByBillnum(String tenantId, Integer storeId, String billNum) throws Exception
	{
		// TODO Auto-generated method stub
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("select ").append(ObjectMappingUtil.getColumnSqlByBean(PosBillPayment.class)).append(" from pos_bill_payment ");
		sqlStr.append("where tenancy_id=? and store_id=? and bill_num=?");
		return this.jdbcTemplate.query(sqlStr.toString(),new Object[]{tenantId,storeId,billNum}, BeanPropertyRowMapper.newInstance(PosBillPayment.class));
	}

	@Override
	public List<PosBillService> getPosBillServiceByBillnum(String tenantId, Integer storeId, String billNum) throws Exception
	{
		// TODO Auto-generated method stub
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("select ").append(ObjectMappingUtil.getColumnSqlByBean(PosBillService.class)).append(" from pos_bill_service ");
		sqlStr.append("where tenancy_id=? and store_id=? and bill_num=?");
		return this.jdbcTemplate.query(sqlStr.toString(),new Object[]{tenantId,storeId,billNum}, BeanPropertyRowMapper.newInstance(PosBillService.class));
	}

	@Override
	public List<PosMethodItem> getPosMethodItemByBillnum(String tenantId, Integer storeId, String billNum) throws Exception
	{
		// TODO Auto-generated method stub
		StringBuffer sqlStr = new StringBuffer();
		sqlStr.append("select ").append(ObjectMappingUtil.getColumnSqlByBean(PosMethodItem.class)).append(" from pos_zfkw_item ");
		sqlStr.append("where tenancy_id=? and store_id=? and bill_num=? and type=?");
		return this.jdbcTemplate.query(sqlStr.toString(),new Object[]{tenantId,storeId,billNum,SysDictionary.ZFKW_TYPE_METHOD}, BeanPropertyRowMapper.newInstance(PosMethodItem.class));
	}

	@Override
	public void updatePosBillForAmount(String tenantId, Integer storeId, PosBill bill) throws Exception
	{
		// TODO Auto-generated method stub
		String sql = new String("update pos_bill set subtotal=?,service_amount=?,bill_amount=?,discountk_amount=?,discountr_amount=?,discount_amount=?,payment_amount=?,maling_amount=?,difference=?,givi_amount=?,average_amount=? where bill_num = ? and store_id = ? and tenancy_id = ?");
		this.jdbcTemplate.update(
				sql,
				new Object[]
				{ bill.getSubtotal(), bill.getService_amount(), bill.getBill_amount(), bill.getDiscountk_amount(), bill.getDiscountr_amount(), bill.getDiscount_amount(), bill.getPayment_amount(), bill.getMaling_amount(), bill.getDifference(), bill.getGivi_amount(), bill.getAverage_amount(),
						bill.getBill_num(), storeId, tenantId });
	}

	@Override
	public void updatePosBillItemForAmount(String tenantId, Integer storeId, List<PosBillItem> itemList) throws Exception
	{
		// TODO Auto-generated method stub
		List<Object[]> batchItem = new ArrayList<Object[]>();
		for(PosBillItem item:itemList)
		{
			batchItem.add(new Object[]{item.getMethod_money(),item.getAssist_money(),item.getItem_amount(),item.getDiscount_mode_id(),item.getDiscount_rate(),item.getDiscount_amount(),item.getDiscountr_amount(),item.getSingle_discount_amount(),item.getReal_amount(),tenantId,storeId,item.getBill_num(),item.getId()});
		}
		
		if(batchItem.size()>0)
		{
			String sql = new String("update pos_bill_item set method_money=?,assist_money=?,item_amount=?,discount_mode_id=?,discount_rate=?,discount_amount=?,discountr_amount=?,single_discount_amount=?,real_amount=? where tenancy_id=? and store_id=? and bill_num=? and id=?");
			this.jdbcTemplate.batchUpdate(sql, batchItem);
		}
	}

	@Override
	public void updatePosBillServiceForAmount(String tenantId, Integer storeId, List<PosBillService> serviceList) throws Exception
	{
		// TODO Auto-generated method stub
		List<Object[]> batchItem = new ArrayList<Object[]>();
		for(PosBillService service:serviceList)
		{
			batchItem.add(new Object[]{service.getService_amount(),service.getService_total(),tenantId,storeId,service.getBill_num(),service.getId()});
		}
		
		if(batchItem.size()>0)
		{
			String sql = new String("update pos_bill_service set service_amount=?,service_total=? where tenancy_id=? and store_id=? and bill_num=? and id=?");
			this.jdbcTemplate.batchUpdate(sql, batchItem);
		}
	}

	@Override
	public List<JSONObject> getPosBillItemForDiscountCaseByBillnum(String tenantId, Integer storeId, String billNum, Integer discountCaseId) throws Exception
	{
		StringBuilder rateStr = new StringBuilder();
		rateStr.append(" select bi.id,bi.item_id,bi.setmeal_id,bi.item_serial,bi.setmeal_rwid,bi.item_property,bi.discount_state,bi.item_remark,bi.item_amount,bi.item_price,bi.assist_money,bi.method_money,bi.item_count,bi.discount_mode_id,bi.discount_rate,bi.discount_amount,bi.real_amount,bi.single_amount,bi.item_amount,bi.combo_prop,bi.discount_num,bi.discount_reason_id,coalesce(bi.third_price,0) as third_price,coalesce(cd.rate,0) as rate,coalesce(cd.derate,0) as derate from pos_bill_item bi");
		rateStr.append(" left join hq_discount_case_details cd on bi.item_id=cd.item_id and bi.item_unit_name=cd.unit and cd.discount_case_id =?");
		rateStr.append(" where bi.tenancy_id=? and bi.store_id=? and bi.bill_num=?");

		return this.query4Json(tenantId, rateStr.toString(), new Object[]
		{ discountCaseId, tenantId, storeId, billNum });
	}
	
	@Override
	public void updatePosBillForUploadByBillNum(String tenancyId, int storeId, String tableName, String billNum, String uploadTag) throws Exception
	{
		// TODO Auto-generated method stub
		StringBuffer sql = new StringBuffer();
		sql.append("update ").append(tableName).append(" set upload_tag='").append(uploadTag).append("' where tenancy_id=? and store_id=? and bill_num=?");

		this.jdbcTemplate.update(sql.toString(), new Object[]
		{tenancyId, storeId, billNum });
	}

	@Override
	public List<JSONObject> getIndexNameList() throws Exception{
		String sql ="select indexname from pg_indexes WHERE SCHEMANAME = 'public'";		
		return this.query4Json("", sql);
	}

	@Override
	public boolean execSQLList(List<String> indexNameList) throws Exception {
		if (indexNameList == null || indexNameList.size()==0) return false;
		String sql = "";   
//		String[] sqlList = new String[indexNameList.size()];
		/*
		 * 不采用事物方式执行，某一索引重建失败不影响其他索引的重建
		 */
		for (int i =0;i<indexNameList.size();i++){
			sql = "REINDEX INDEX "+indexNameList.get(i);
			try{
				this.jdbcTemplate.execute(sql);
			}catch(Exception e){
				logger.error("创建索引"+indexNameList.get(i)+"失败，异常信息："+e.getMessage()+" SQL语句："+sql);
			}			
//			sqlList[i] = sql;
		}
//		int[] res = null;
//		res = this.jdbcTemplate.batchUpdate(sqlList);
//		if(res != null && res.length > 0){
//			return true;
//		}else{
//			return false;
//		}
		return true;
	}
	
}
