package com.tzx.pos.bo;

import java.util.Date;
import java.util.List;

import com.tzx.framework.common.entity.Data;

import net.sf.json.JSONObject;

public interface PosOpenTableService extends PosBaseService
{
	String	NAME	= "com.tzx.pos.bo.imp.PosOpenTableServiceImp";

	/** 校验报表日期
	 * @param tenantId
	 * @param storeId
	 * @param reportDate
	 * @throws Exception
	 */
	public void checkReportDate(String tenantId, Integer storeId, Date reportDate) throws Exception;

	/**校验班次
	 * @param tenantId
	 * @param storeId
	 * @param reportDate
	 * @param shiftId
	 * @param optNum
	 * @param posNum
	 * @param source
	 * @return
	 * @throws Exception
	 */
	public Integer checkShiftId(String tenantId, Integer storeId, Date reportDate, Integer shiftId, String optNum, String posNum, String source) throws Exception;

	/**开台修改桌位状态
	 * @param tenantId
	 * @param storeId
	 * @param tableCode
	 * @param optNum
	 * @param posNum
	 * @param splitFlag
	 * @return
	 * @throws Exception
	 */
	public String openTableByTableCode(String tenantId, Integer storeId, String tableCode, String optNum, String posNum, String splitFlag) throws Exception;

	/** 生成账单
	 * @param tenantId
	 * @param storeId
	 * @param reportDate
	 * @param shiftId
	 * @param fictitiousTable
	 * @param paramJson
	 * @return
	 * @throws Exception
	 */
	public String addPosBill(String tenantId, Integer storeId, Date reportDate, Integer shiftId, String fictitiousTable, JSONObject paramJson) throws Exception;

	/**
	 * @param tenantId
	 * @param storeId
	 * @param reportDate
	 * @param shiftId
	 * @param paramJson
	 * @return
	 * @throws Exception
	 */
	public String openTableByTableCode(String tenantId, Integer storeId, Date reportDate, Integer shiftId, JSONObject paramJson) throws Exception;

	/** 添加默认菜品
	 * @param param
	 * @param billNum
	 * @param printJson
	 * @return
	 * @throws Exception
	 */
	public Data getDefaultDish(Data param, String billNum, JSONObject printJson) throws Exception;

	/**获取开台账单数据
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @param tableCode
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getPosBillByBillNum(String tenantId, Integer storeId, String billNum, String tableCode) throws Exception;

	/**桌位状态回滚
	 * @param tenantId
	 * @param storeId
	 * @param tableCode
	 * @throws Exception
	 */
	public void openTableForRollBackByTableCode(String tenantId, Integer storeId, String tableCode) throws Exception;

	/**
	 * @param tenantId
	 * @param storeId
	 * @throws Exception
	 */
	public void updateDevicesDataState(String tenantId, Integer storeId) throws Exception;
	
	
	/**
	  * @Description: 判断是否设置自动增加菜品区域 并且 该桌台是否在设置范围
	  * @Title:getHqItemTables
	  * @param:@param tenantId
	  * @param:@param storeId
	  * @param:@param tableCode
	  * @param:@return
	  * @param:@throws Exception
	  * @return: boolean
	  * @author: shenzhanyu
	  * @version: 1.0
	  * @Date: 2018年4月25日
	 */
	public boolean getHqItemTables(String tenantId, Integer storeId, String tableCode) throws Exception;
}
