package com.tzx.member.crm.bo;

import com.tzx.framework.common.entity.Data;
import net.sf.json.JSONObject;

/**
 * 会员充值发票
 * Created by qingui on 2018-07-12.
 */
public interface CustomerInvoiceService {
    String name = "com.tzx.member.crm.bo.CustomerInvoiceServiceImp";

    /**
     * 查询会员可开票金额
     * @param tenancyId
     * @param customerId
     * @return
     * @throws Exception
     */
    Data getInvoiceBalancePost(String tenancyId, int storeId, String customerId) throws Exception;

    /**
     * 会员开充值发票
     * @param tenancyId
     * @param param
     * @return
     * @throws Exception
     */
    Data insertInvoicePost(String tenancyId, Data param) throws Exception;

    /**
     * 查询会员开票历史流水
     * @param tenancyId
     * @param param
     * @return
     * @throws Exception
     */
    Data getInvoiceListPost(String tenancyId, Data param) throws Exception;
}
