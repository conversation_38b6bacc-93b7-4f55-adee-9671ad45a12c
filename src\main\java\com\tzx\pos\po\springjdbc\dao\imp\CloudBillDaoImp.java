package com.tzx.pos.po.springjdbc.dao.imp;

import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.dao.imp.BaseDaoImp;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.bo.imp.CloudBillServiceImp;
import com.tzx.pos.po.springjdbc.dao.CloudBillDao;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Repository;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017-12-27.
 */
@Repository(CloudBillDao.NAME)
public class CloudBillDaoImp extends BaseDaoImp implements CloudBillDao {
    private static final Logger logger = Logger.getLogger(CloudBillDaoImp.class);

    @Override
    public JSONObject findCloudBill(String tenancy_id, int store_id, String table_code, String bill_num) {
        try {
            List<JSONObject> posbillList = new ArrayList<JSONObject>();
            JSONObject exceptionJson = new JSONObject();
            StringBuilder posBillStringBuilder = new StringBuilder();
            posBillStringBuilder.append("select b.report_date,b.source,b.shift_id,b.source,b.open_pos_num,b.open_opt,b.opentable_time,b.waiter_num,b.pos_num,b.cashier_num,b.payment_time,b.table_code,b.bill_num,b.serial_num,b.item_menu_id,");
            posBillStringBuilder.append("b.batch_num,b.copy_bill_num,b.service_id,b.service_amount,b.subtotal,b.maling_amount,b.payment_amount,b.payment_amount as actual_pay,b.difference,b.discount_mode_id,b.discount_case_id,");
            posBillStringBuilder.append("b.discount_rate,b.discountk_amount,b.discountr_amount,b.discount_amount,b.givi_amount,b.guest,b.average_amount,b.guest_msg,b.print_count,b.bill_state,b.bill_property,b.tenancy_id,b.store_id,");
            posBillStringBuilder.append("remark,payment_state,bill_taste,single_discount_amount,free_amount,more_coupon,upload_tag,shop_real_amount,total_fees,platform_charge_amount,fictitious_table,b.bill_amount");
            posBillStringBuilder.append(" from pos_bill b ");
            //账单号为空则根据桌位号查询最新的未关闭账单
            if (StringUtils.isEmpty(bill_num)) {
                StringBuffer sqlState = new StringBuffer("select ti.table_code,pt.state from tables_info ti left join pos_tablestate pt on ti.tenancy_id=pt.tenancy_id and ti.organ_id=pt.store_id and ti.table_code=pt.table_code where ti.tenancy_id=? and ti.organ_id=? and ti.table_code=?");
                SqlRowSet tableRs = this.query4SqlRowSet(sqlState.toString(), new Object[]{tenancy_id,store_id,table_code});
                if(tableRs.next()) {
                    //未开台
                    if(SysDictionary.TABLE_STATE_FREE.equals(tableRs.getString("state"))) {
                        exceptionJson.put("code", SysDictionary.BILL_CLOUD_TABLE_CODE);
                        return exceptionJson;
                    }
                } else {
                    exceptionJson.put("code", SysDictionary.BILL_CLOUD_TABLE_EXIST_CODE);
                    return exceptionJson;
                }
                posBillStringBuilder.append(" where b.bill_property =  '" + SysDictionary.BILL_PROPERTY_OPEN + "' ");
                posBillStringBuilder.append(" and b.table_code =  ?  order by b.fictitious_table,b.opentable_time desc limit 1 ");
                posbillList = this.query4Json(tenancy_id, posBillStringBuilder.toString(), new Object[]{table_code});
                //从账单中获取账单号
                if(null != posbillList && posbillList.size() > 0) {
                    JSONObject jsonObject = JSONObject.fromObject(posbillList.get(0));
                    bill_num = jsonObject.optString("bill_num");
                }else {
                    exceptionJson.put("code", SysDictionary.BILL_CLOUD_FAILURE_CODE);
                    return exceptionJson;
                }
            } else { //账单号不为空表明不是首次查询订单
                //判断桌位号是否为空
                if(StringUtils.isNotEmpty(table_code)) {
                    posBillStringBuilder.append(" where b.bill_num = ? and table_code = ?");
                    posbillList = this.query4Json(tenancy_id, posBillStringBuilder.toString(), new Object[]{bill_num, table_code});
                }else {
                    posBillStringBuilder.append(" where b.bill_num = ?");
                    posbillList = this.query4Json(tenancy_id, posBillStringBuilder.toString(), new Object[]{bill_num});
                }
                if(null == posbillList || posbillList.size() == 0) {
                    exceptionJson.put("code", SysDictionary.BILL_CLOUD_FAILURE_CODE);
                    return exceptionJson;
                }
            }
            if(null != posbillList) {
                String json = JSONObject.fromObject(posbillList.get(0)).toString();
                JSONObject ob = JSONObject.fromObject(json);
                //处理0元账单
                double subtotal = ob.optDouble("subtotal",0);
                double paymentAmount = ob.optDouble("payment_amount",0);
                String batchNum = ob.optString("batch_num","");
                if(StringUtils.isNotEmpty(batchNum) && !"null".equals(batchNum)) {
                    if (subtotal >= 0 && paymentAmount <= 0) {
                        exceptionJson.put("code", SysDictionary.BILL_CLOUD_GIVE_DISH_CODE);
                        return exceptionJson;
                    }
                }
                StringBuilder posBillServiceSql = new StringBuilder();
                posBillServiceSql.append("select tenancy_id,store_id,id,bill_num,table_code,service_id,service_type,");
                posBillServiceSql.append("taken_mode,service_scale,service_amount,service_count,service_total,service_rate ");
                posBillServiceSql.append(" from pos_bill_service where bill_num = ? ");
                List<JSONObject> feeList = this.query4Json(tenancy_id, posBillServiceSql.toString(), new Object[]{bill_num});
                if (feeList != null && feeList.size() > 0) {
                    ob.put("servicelist", feeList);
                } else {
                    JSONArray serList = new JSONArray();
                    ob.put("servicelist", serList);
                }
                StringBuilder posBillMemberSql = new StringBuilder();
                posBillMemberSql.append("select tenancy_id,store_id,id,bill_num,report_date,type,amount,credit,");
                posBillMemberSql.append("card_code,mobil,last_updatetime,upload_tag,customer_code,");
                posBillMemberSql.append("consume_before_credit,consume_after_credit  from pos_bill_member where bill_num = ?");
                List<JSONObject> memberList = this.query4Json(tenancy_id, posBillMemberSql.toString(), new Object[]{bill_num});
                if (memberList != null && memberList.size() > 0) {
                    ob.put("memberlist", memberList);
                } else {
                    JSONArray merList = new JSONArray();
                    ob.put("memberlist", merList);
                }
                StringBuilder posBillItemStringBuilder = new StringBuilder();
                posBillItemStringBuilder.append("SELECT pb.item_serial,pb.rwid,pb.item_id,pb.item_num,pb.item_name,pb.item_remark,pb.item_property,pb.discount_rate,pb.discount_amount,pb.discount_mode_id,");
                posBillItemStringBuilder.append("pb.item_price,pb.item_count,pb.item_amount,pb.real_amount,pb.third_price,pb.remark,pb.waitcall_tag,pb.print_tag,pb.item_unit_id,pb.item_unit_name,pb.item_unit_name,");
                posBillItemStringBuilder.append("pb.details_id,pb.setmeal_id,pb.setmeal_rwid,pb.sale_mode,pb.item_time,pb.assist_money,pb.assist_num,pb.assist_item_id,pb.item_mac_id,pb.item_taste,pb.item_remark,");
                posBillItemStringBuilder.append("pb.tenancy_id,pb.store_id,pb.yrwid,pb.bill_num,pb.table_code,pb.waiter_num,pb.single_discount_amount,pb.discountr_amount,pb.discount_state,pb.report_date,");
                posBillItemStringBuilder.append("pb.order_remark,pb.seat_num,pb.discount_reason_id,pb.upload_tag,pb.returngive_reason_id,pb.method_money,pb.batch_num,pb.order_number,pb.opt_num,");
                posBillItemStringBuilder.append("pb.activity_id,pb.activity_batch_num,pb.activity_rule_id,pb.activity_count,pb.single_amount,pb.default_state,hii.is_runningprice");
                posBillItemStringBuilder.append(" FROM pos_bill_item pb,hq_item_info hii where pb.bill_num = ? and pb.item_id = hii.id order by pb.item_serial,pb.assist_item_id");

                List<JSONObject> positemList = this.query4Json(tenancy_id, posBillItemStringBuilder.toString(), new Object[]{bill_num});
                if (positemList != null && positemList.size() > 0) {
                    String methodsql = "select * from pos_zfkw_item where bill_num = ? and rwid = ?";
                    for (JSONObject o : positemList) {
                        int rwid = o.optInt("rwid");
                        List<JSONObject> methodList = this.query4Json(tenancy_id, methodsql, new Object[]{bill_num, rwid});
                        o.put("method", methodList);
                    }
                }else {
                    exceptionJson.put("code", SysDictionary.BILL_CLOUD_NO_DISH_CODE);
                    return exceptionJson;
                }
                ob.put("detaillist", positemList);
                return ob;
            }
        }catch (Exception e) {
            logger.info("查询账单数据失败,"+e);
            e.printStackTrace();
        }
        return null;
    }
    @Override
    public int selectBillItemCountByBillNum(String tenancy_id, int store_id, String bill_num) throws Exception {
        StringBuilder billItemCountSql = new StringBuilder();
        int billItemCount = 0;
        billItemCountSql.append("select count(id) as bill_item_count  from pos_bill_item  where tenancy_id = ? and store_id = ? and bill_num = ?");
        SqlRowSet rs = this.query4SqlRowSet(billItemCountSql.toString(), new Object[]{tenancy_id,store_id,bill_num});
        if(rs.next()) {
            billItemCount = rs.getInt("bill_item_count");
        }
        return billItemCount;
    }

    @Override
    public JSONObject queryBillByBillNum(String tenancy_id, int store_id, String bill_num) throws Exception {
        StringBuilder billSql = new StringBuilder();
        billSql.append("select subtotal,payment_amount,discount_amount from pos_bill where tenancy_id= ? and store_id=? and bill_num=?");
        SqlRowSet rs = this.query4SqlRowSet(billSql.toString(), new Object[]{tenancy_id,store_id,bill_num});
        double subtotal = 0d;
        double paymentAmount = 0d;
        double discountAmount = 0d;
        if(rs.next()) {
            subtotal = rs.getDouble("subtotal");
            paymentAmount = rs.getDouble("payment_amount");
            discountAmount = rs.getDouble("discount_amount");
        }else {
            return null;
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("subtotal", subtotal);
        jsonObject.put("paymentAmount", paymentAmount);
        jsonObject.put("discountAmount", discountAmount);
        return jsonObject;
    }
    /**
     * 查询锁单表信息
     * @param bill_num
     * @param tenantId
     * @param store_id
     * @return
     * @throws Exception
     */
    @Override
    public List findLockData(String bill_num, String tenantId, Integer store_id) throws Exception {
        StringBuffer sql=new StringBuffer("select * from pos_bill_lock where bill_num=? and tenancy_id=? and store_id=?");
        return this.query4Json(tenantId,sql.toString(),new Object[]{bill_num,tenantId,store_id});
    }

    @Override
    public int forcedUnlocking(String bill_num, String tenantId, Integer store_id, Date unlockTime, String chanel, Integer lock_state,String openId,Integer customerId) throws Exception {
        StringBuffer sql=new StringBuffer();
        if(lock_state==0){
            sql.append("update pos_bill_lock set lock_state=?,unlock_time=?,unlock_type=? where bill_num=? and tenancy_id=? and store_id=?");
            return this.update(sql.toString(),new Object[]{lock_state,unlockTime,chanel,bill_num,tenantId,store_id});
        }else{
            sql.append("update pos_bill_lock set lock_state=?,lock_time=?,lock_type=?,open_id=?,customer_id=? where bill_num=? and tenancy_id=? and store_id=?");
            return this.update(sql.toString(),new Object[]{lock_state,unlockTime,chanel,openId,customerId,bill_num,tenantId,store_id});
        }

    }

    @Override
    public int closedBillUnlocking(String bill_num, String tenantId, Integer store_id) throws Exception {
        StringBuffer sql=new StringBuffer("update pos_bill_lock set bill_state=1 where bill_num=? and tenancy_id=? and store_id=?");
        return this.update(sql.toString(),new Object[]{bill_num,tenantId,store_id});
    }
    @Override
    public int insertData(Object[] data)throws Exception{
        StringBuffer sb=new StringBuffer("insert into pos_bill_lock (tenancy_id,store_id,bill_num,lock_type,lock_state,open_id,customer_id,lock_time,bill_state)values(?,?,?,?,?,?,?,?,?)");
        return this.update(sb.toString(),data);
    }

    @Override
	public boolean syncBillInfo(String tenancyId, int storeId, String billNum, String reportDate, String posNum, String cashierNum, Integer shiftId, JSONObject json) throws Exception {
        // 更新账单表
        if (json.containsKey("pos_bill") && !json.optJSONObject("pos_bill").isEmpty())
        {
            JSONObject billJson = json.optJSONObject("pos_bill");
            billJson.put("pos_num", posNum);
            billJson.put("cashier_num", cashierNum);
            billJson.put("shift_id", shiftId);

            this.updateIgnorCase(tenancyId, "pos_bill", billJson);
        }

        //更新账单副表
        if(json.containsKey("pos_bill_sub") && !json.optJSONObject("pos_bill_sub").isEmpty())
        {
            JSONObject subJson = json.optJSONObject("pos_bill_sub");
            String billSubSql = "select id from pos_bill_sub s where s.bill_num = ? and s.store_id = ? and s.tenancy_id = ? ";
            SqlRowSet rowSet = this.query4SqlRowSet(billSubSql, new Object[]{subJson.optString("bill_num"), subJson.optInt("store_id"), subJson.optString("tenancy_id") });
            if(rowSet.next())
            {
                subJson.remove("create_time");

                subJson.put("id", rowSet.getInt("id"));
                subJson.put("sync_state", SysDictionary.SYNC_STATE_OK);

                this.updateIgnorCase(tenancyId, "pos_bill_sub", subJson);
            }
        }

        // 更新账单明细表
        if (json.containsKey("pos_bill_item") && !json.optJSONArray("pos_bill_item").isEmpty())
        {
            String itemSql = "select id from pos_bill_item b where b.rwid = ? and b.bill_num = ? and b.tenancy_id = ? and b.store_id = ?";
            JSONArray itemArray = json.optJSONArray("pos_bill_item");
            for(int i = 0; i < itemArray.size(); i++)
            {
                JSONObject itemJson = itemArray.getJSONObject(i);
                SqlRowSet rowSet = this.query4SqlRowSet(itemSql, new Object[]{itemJson.optInt("rwid"), itemJson.optString("bill_num"), itemJson.optString("tenancy_id"), itemJson.optInt("store_id") });
                if(rowSet.next())
                {
                    itemJson.put("id", rowSet.getInt("id"));

                }
            }
            this.updateBatchIgnorCase(tenancyId, "pos_bill_item", itemArray);
        }
        // 付款表
        if (json.containsKey("pos_bill_payment") && !json.optJSONArray("pos_bill_payment").isEmpty())
        {
            String deletePaymentSql = " delete from pos_bill_payment where bill_num = '"+billNum+"' and store_id = "+storeId+" and tenancy_id = '"+tenancyId+"' ";
            this.execute(tenancyId, deletePaymentSql);

            JSONArray payArray = json.optJSONArray("pos_bill_payment");
            for(int i = 0; i < payArray.size(); i++)
            {
                JSONObject payJson = payArray.getJSONObject(i);
                payJson.put("pos_num", posNum);
                payJson.put("cashier_num", cashierNum);
                payJson.put("shift_id", shiftId);
            }
            this.insertBatchIgnorCase(tenancyId, "pos_bill_payment", payArray);
        }

        // 付款日志表
        /*if (json.containsKey("pos_bill_payment_log") && !json.optJSONArray("pos_bill_payment_log").isEmpty())
        {
            this.insertBatchIgnorCase(tenancyId, "pos_bill_payment_log", json.optJSONArray("pos_bill_payment_log"));
        }*/
        // 日志
        /*if (json.containsKey("pos_log") && !json.optJSONArray("pos_log").isEmpty())
        {
            this.insertBatchIgnorCase(tenancyId, "pos_log", json.optJSONArray("pos_log"));
        }*/

        // 会员表
        if (json.containsKey("pos_bill_member") && !json.optJSONArray("pos_bill_member").isEmpty())
        {
            String deleteMemberSql = " delete from pos_bill_member where bill_num = '"+billNum+"' and store_id = "+storeId+" and tenancy_id = '"+tenancyId+"' ";
            this.execute(tenancyId, deleteMemberSql);

            this.insertBatchIgnorCase(tenancyId, "pos_bill_member", json.optJSONArray("pos_bill_member"));
        }
        // 会员操作流水表
        if (json.containsKey("pos_customer_operate_list") && !json.optJSONArray("pos_customer_operate_list").isEmpty())
        {
            JSONArray operateArray = json.optJSONArray("pos_customer_operate_list");
            for(int i = 0; i < operateArray.size(); i++)
            {
                JSONObject operateJson = operateArray.getJSONObject(i);
                operateJson.put("pos_num", posNum);
                operateJson.put("operator_id", StringUtils.isNotEmpty(cashierNum) ? Integer.parseInt(cashierNum) : null);
                operateJson.put("shift_id", shiftId);
            }
            this.insertBatchIgnorCase(tenancyId, "pos_customer_operate_list", operateArray);
        }
        // crm交易流水表
        if (json.containsKey("crm_card_trading_list") && !json.optJSONArray("crm_card_trading_list").isEmpty())
        {
        	String operator = this.getEmpNameById(cashierNum, tenancyId, storeId);
        	JSONArray orderArray = json.optJSONArray("crm_card_trading_list");
            for(int i = 0; i < orderArray.size(); i++)
            {
                JSONObject orderJson = orderArray.getJSONObject(i);
                orderJson.put("operator", operator);
                orderJson.put("operator_id", cashierNum);
                orderJson.put("shift_id", shiftId);
            }
            this.insertBatchIgnorCase(tenancyId, "crm_card_trading_list", orderArray);
        }
        // 第三方订单表
        if (json.containsKey("pos_third_payment_order") && !json.optJSONArray("pos_third_payment_order").isEmpty())
        {
            JSONArray orderArray = json.optJSONArray("pos_third_payment_order");
            for(int i = 0; i < orderArray.size(); i++)
            {
                JSONObject orderJson = orderArray.getJSONObject(i);
                orderJson.put("pos_num", posNum);
                orderJson.put("opt_num", cashierNum);
                orderJson.put("shift_id", shiftId);
            }
            this.insertBatchIgnorCase(tenancyId, "pos_third_payment_order", orderArray);
        }

        return true;
    }

    @Override
    public String findBillNum(String tenancyId, int storeId, String tableCode) throws Exception {
        String sql="select bill_num from pos_bill where tenancy_id=? and store_id=? and bill_property='OPEN' and table_code=? order by id desc limit 1";
        List<JSONObject> list=this.query4Json(tenancyId,sql.toString(),new Object[]{tenancyId,storeId,tableCode});
        String billnum="";
        if(list!=null&&list.size()>0){
            JSONObject jsobj= list.get(0);
            if(!jsobj.isEmpty()){
                billnum=jsobj.optString("bill_num","");
            }
        }
        return billnum;
    }

    @Override
    public List<JSONObject> findBillInfo(String billnum, String tenancyId, Integer storeId) throws Exception {
        String sql="select report_date,pos_num,shift_id,cashier_num,table_code,open_pos_num,open_opt from pos_bill where bill_num=? and tenancy_id=? and store_id=? ";
        return this.query4Json(tenancyId,sql,new Object[]{billnum,tenancyId,storeId});
    }

    @Override
    public int insertPosBillSub(String tenancyId, int storeId, JSONObject jsonObject) throws Exception {
//        String querySql = "select * from pos_bill_sub where tenancy_id = ? and store_id = ? and bill_num = ?";
//        SqlRowSet rs = this.query4SqlRowSet(querySql, new Object[]{tenancyId, storeId, billNum});
//        if(!rs.next()) {
//            String inertSql = "insert into pos_bill_sub(tenancy_id,store_id,bill_num,create_time,handle_client,report_date) values(?,?,?,?,?,?)";
//            return this.update(inertSql.toString(), new Object[]{tenancyId, storeId, billNum, DateUtil.currentTimestamp(), SysDictionary.CLOUD_CLIENT_HANDLE, DateUtil.parseDate(reportDate)});
//        }
//        return 0;
        String billNum =  jsonObject.optString("bill_num", "");
        String reportDate =  jsonObject.optString("report_date", "");
        double paymentAmount = jsonObject.optDouble("payment_amount", 0);
        String querySql = "select * from pos_bill_sub where tenancy_id = ? and store_id = ? and bill_num = ?";
        SqlRowSet rs = this.query4SqlRowSet(querySql, new Object[]{tenancyId, storeId, billNum});
        if(!rs.next()) {
            String inertSql = "insert into pos_bill_sub(tenancy_id,store_id,bill_num,create_time,handle_client,report_date,shop_payment_amount) values(?,?,?,?,?,?,?)";
            return this.update(inertSql.toString(), new Object[]{tenancyId, storeId, billNum, DateUtil.currentTimestamp(), SysDictionary.CLOUD_CLIENT_HANDLE, DateUtil.parseDate(reportDate), paymentAmount});
        }else {
            String updateSql = "update pos_bill_sub set shop_payment_amount = ? where tenancy_id = ? and store_id = ? and bill_num = ?";
            return this.update(updateSql.toString(), new Object[]{paymentAmount, tenancyId, storeId, billNum});
        }
    }

    @Override
    public List<JSONObject> findPosBillSub(String tenancyId, int storeId, String billnum) throws Exception {
        String querySql = "select tenancy_id,store_id,bill_num,report_date,create_time,handle_client,pay_client,sync_state,query_time,shop_payment_amount,cloud_interface from pos_bill_sub where tenancy_id = ? and store_id = ? and bill_num = ?";
        return this.query4Json(tenancyId,querySql,new Object[]{tenancyId, storeId, billnum});
    }

    @Override
    public int getBillItemCount(String tenancyId, int storeId, String billNum, String itemRemark) throws Exception {
        String itemCountSql = "select count(id) count from pos_bill_item i where i.bill_num = ? and i.tenancy_id = ? and i.store_id = ?";

        return this.queryForInt(itemCountSql, new Object[]{billNum, tenancyId, storeId});
    }

    @Override
    public int selectBillItemCountByBillNum4Give(String tenancy_id, int store_id, String bill_num) throws Exception {
        StringBuilder billItemCountSql = new StringBuilder();
        int billItemCount = 0;
        billItemCountSql.append("select count(id) as bill_item_count  from pos_bill_item  where tenancy_id = ? and store_id = ? and bill_num = ? and item_remark = '" + SysDictionary.ITEM_REMARK_FS02 +"'");
        SqlRowSet rs = this.query4SqlRowSet(billItemCountSql.toString(), new Object[]{tenancy_id,store_id,bill_num});
        if(rs.next()) {
            billItemCount = rs.getInt("bill_item_count");

        }
        return billItemCount;
    }

    @Override
    public JSONObject getBillAndSubInfo(String tenancyId, int storeId, String billNum) throws Exception {

        StringBuilder billSql = new StringBuilder();
        billSql.append(" select b.id, b.bill_num, b.bill_property, b.payment_state, b.report_date, b.shift_id, b.fictitious_table, coalesce(b.payment_amount, 0) as payment_amount, coalesce(s.shop_payment_amount, 0) as shop_payment_amount ");
        billSql.append(" from pos_bill b left join pos_bill_sub s on b.bill_num = s.bill_num and b.tenancy_id = s.tenancy_id and b.store_id = s.store_id ");
        billSql.append(" where b.bill_num = ? and b.store_id = ? and b.tenancy_id = ? ");

        List<JSONObject> list = this.query4Json(tenancyId, billSql.toString(), new Object[]{billNum, storeId, tenancyId});

        if(list != null && list.size() > 0)
        {
            return list.get(0);
        }

        return null;
    }

    @Override
    public void updateBillRemark(String tenancyId, int storeId, String billNum, String remark) throws Exception {
        String updateRemarkSql = "update pos_bill set remark = remark||?||';' where bill_num = ? and tenancy_id = ? and store_id = ? ";

        this.update(updateRemarkSql, new Object[]{remark, billNum, tenancyId, storeId});
    }

}
