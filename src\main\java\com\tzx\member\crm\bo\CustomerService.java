package com.tzx.member.crm.bo;

import java.util.List;

import net.sf.json.JSONObject;

import com.tzx.base.bo.PosCodeService;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.entity.Pagination;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.pos.bo.dto.PosThirdPaymentOrderEntity;

/**
 * 
 * <AUTHOR> 2015年7月10日-下午3:41:01
 */
public interface CustomerService
{
	String	NAME	= "com.tzx.member.crm.bo.imp.CustomerServiceImp";

	/**
	 * 公用调会员接口方法
	 * 
	 * @param param
	 * @param resData
	 */
	public void commonPost(String param, Data resData);

	/**
	 * 参数调整
	 * 
	 * @param param
	 * @param jsobj
	 * @throws Exception
	 */
	public void formatParam(Data param, JSONObject jsobj) throws Exception;

	/**
	 * 公用调会员接口方法 验证是否签到
	 * 
	 * @param param
	 * @param resData
	 */
	public boolean commonPostCheck(Data paramData);
	
	/**
	 * @param tenantId
	 * @param storeId
	 * @param paramData
	 * @return
	 */
	public boolean commonPostCheck(String tenantId, Integer storeId, JSONObject paramData);
	
	/**
	 * @param tenantId
	 * @param storeId
	 * @param source
	 * @param paramData
	 * @return
	 */
	public boolean commonPostCheck(String tenantId, Integer storeId,String source, JSONObject paramData);

	/**
	 * 制卡
	 * 
	 * @param param
	 * @param resData
	 */
	public void initCustomerCardPost(String param, Data resData);

	/**
	 * 会员卡激活
	 * 
	 * @param param
	 * @param resData
	 */
	public void activateCustomerCardPost(String param, Data resData);

	/**
	 * 挂失
	 * 
	 * @param param
	 * @param resData
	 */
	@Deprecated
	public void lossCustomerCardPost(String param, Data resData);

	/**
	 * 会员卡补卡接口方法
	 * 
	 * @param param
	 * @param resData
	 */
	public void fillCustomerCardPost(String param, Data resData);

	/**
	 * 会员卡并卡接口方法
	 * 
	 * @param param
	 * @param resData
	 */
	public void mergeCustomerCardPost(String param, Data resData);

	/**
	 * 会员卡退卡接口方法
	 * 
	 * @param param
	 * @param resData
	 */
	public void backOutCustomerCardPost(String param, Data resData);

	// 重置密码

	/**
	 * 会员卡充值
	 * 
	 * @param param
	 * @param resData
	 */
	@Deprecated
	public void rechargePost(Data param, Data resData,String posNum);

	/**
	 * 会员卡预充值
	 * 
	 * @param param
	 * @param resData
	 */
	@Deprecated
	public void rechargePreparePost(Data param, PosCodeService codeService, Data resData);

	/**
	 * 会员卡撤销充值
	 * 
	 * @param param
	 * @param resData
	 */
	@Deprecated
	public void rechargeCancelPost(Data param, Data resData);

	/**
	 * 会员卡消费接口方法
	 * 
	 * @param param
	 * @param resData
	 */
	@Deprecated
	public void customerConsumePost(String param, Data resData);

	/**
	 * 会员卡撤销消费接口方法
	 * 
	 * @param param
	 * @param resData
	 */
	@Deprecated
	public void cancelCardConsume(Data param, Data resData, String isCheck) throws Exception;

	/**
	 * 购买会籍
	 * 
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void buyVipLevel(Data param, Data result) throws Exception;

	/**
	 * 撤销购买会籍
	 * 
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void deleteBuyVipLevel(Data param, Data result) throws Exception;

	/**
	 * 优惠劵验证
	 * 
	 * @param param
	 * @param resData
	 * @throws SystemException
	 */
	@Deprecated
	public void verifyCoupon(Data param, Data resData) throws SystemException;

	/**
	 * 删除会员价
	 * 
	 * @param param
	 * @param result
	 * @throws SystemException
	 */
	@Deprecated
	public void deleteVipPrice(Data param, Data result) throws SystemException;

	/**
	 * 会员价查询
	 * 
	 * @param param
	 * @param result
	 * @throws SystemException
	 */
	@Deprecated
	public void customerVipPrice(JSONObject param, Data result) throws SystemException;

	/**
	 * 会员消费小票打印
	 * 
	 * @param para
	 * @param printCode
	 * @param printCount
	 * @throws Exception
	 */
	public void customerCardConsumePrint(String tenantId, int storeId, String posNum, JSONObject para, String printCode) throws Exception;

	/**
	 * 会员卡充值记录本地查询
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getCardRechargeRecord(String tenancyId, int storeId, List<?> param, Pagination pagination) throws Exception;

	/**
	 * 上传沽清
	 * 
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void dishSoldOut(Data param, List<?> jsParam) throws Exception;

	/**
	 * 查询充值
	 * 
	 * @param param
	 * @param returnData
	 * @throws Exception
	 */
	@Deprecated
	public void queryCardRecharge(Data param, Data returnData) throws Exception;

	/**
	 * 取消充值
	 * 
	 * @param param
	 * @param returnData
	 * @throws Exception
	 */
	@Deprecated
	public void cancelCardRecharge(Data param, Data returnData) throws Exception;

	/**
	 * 充值成功
	 * 
	 * @param param
	 * @param returnData
	 * @throws Exception
	 */
	@Deprecated
	public void completeCardRecharge(Data param, Data returnData) throws Exception;

	/**
	 * 团体挂账接口方法
	 * 
	 * @param param
	 * @param resData
	 */
	@Deprecated
	public void customerIncorporationPost(String param, Data resData);

	/**
	 * 扫码支付
	 * 
	 * @throws Exception
	 */
	@Deprecated
	public Data precreateThirdPayment(Data params, String path) throws Exception;

	/**
	 * 被扫支付
	 * 
	 * @throws Exception
	 */
	@Deprecated
	public Data barcodeThirdPayment(Data params, JSONObject jsobj) throws Exception;

	/**
	 * 第三方支付状态查询
	 * 
	 * @param params
	 * @param printJson
	 * @return
	 * @throws Exception
	 */
	@Deprecated
	public Data queryThirdPaymentForRunnable(String tenancyId, int storeId, PosThirdPaymentOrderEntity params, JSONObject printJson) throws Exception;
	
	/**
	 * 签账卡支付查询
	 * @param param
	 * @param resData
	 */
	public void commonDebitPay(String param, Data resData);

	/**
	 * 会员历史账单查询
	 * @param param
	 * @param resData
	 */
	public void queryMemberHistoryBill(Data param, Data result);
	
	/**
	 * 会员历史账单明细查询
	 * @param param
	 * @param resData
	 */
	public void queryMemberHistoryBillDetail(Data param, Data result);

	/**
	 * 查询桌态会员
	 * @param param
	 * @param resData
	 */
	public void queryTableStateAndMemberState(Data param, Data result);
	/**
	 * 查询会员手机号
	 * @param tenancyId
	 * @param storeId
	 * @param bill_num
	 * @param card_code
	 * @param customer_code
	 * @return
	 * @throws Exception
	 */
	public String getCustomerMobile(String tenancyId,int storeId,String bill_num,String card_code,String customer_code) throws Exception;
}


