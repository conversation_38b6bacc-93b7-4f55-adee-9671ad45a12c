package com.tzx.clientorder.acewillwechat.po.springjdbc.dao.imp;

import com.tzx.framework.common.util.dao.impl.GenericDaoImpl;
import com.tzx.clientorder.acewillwechat.po.springjdbc.dao.TableStateDao;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by qin-gui on 2018/3/9.
 */
@Repository(TableStateDao.NAME)
public class TableStateDaoImp extends GenericDaoImpl implements TableStateDao {

    @Override
    public JSONObject getTableState(String tenancyId, int storeId, String tableCode) throws Exception {
        StringBuffer sql = new StringBuffer();
        sql.append(" select b.bill_num as oid,b.order_num,b.bill_num is null as bill_flag from")
                .append(" pos_tablestate tb ")
                .append(" left join pos_bill b on tb.store_id = b.store_id and tb.table_code=b.fictitious_table and b.bill_property='OPEN'")
                .append(" where  tb.table_code= '"+ tableCode + "' and tb.store_id = "+ storeId);
        List<JSONObject> list = this.query4Json(tenancyId, sql.toString());
        JSONObject object = null;
        if (null != list && list.size() > 0)
            object = list.get(0);
        return object;
    }

    @Override
    public void updatePosBill(String tenancyId, String billNum, String orderNum) throws Exception {
        StringBuffer sql = new StringBuffer();
        sql.append(" update pos_bill set order_num = '"+ orderNum +"'")
                .append(" where bill_num = '"+ billNum + "'");
        this.execute(tenancyId, sql.toString());
    }

    @Override
    public JSONObject getTableOpen(String tenancyId, int storeId, String tableCode) throws Exception {
        StringBuffer sql = new StringBuffer();
        sql.append(" select l.lock_state,l.lock_type from pos_tablestate t")
            .append(" left join pos_bill b on t.store_id = b.store_id")
            .append(" left join pos_bill_lock l on b.bill_num = l.bill_num")
            .append(" where t.store_id = "+ storeId +" and t.table_code = '"+ tableCode +"' and b.bill_property = 'OPEN' and b.fictitious_table = '"+ tableCode +"'");
        List<JSONObject> list = this.query4Json(tenancyId, sql.toString());
        if (null != list && list.size() > 0)
            return list.get(0);
        return null;
    }
}
