package com.tzx.pos.po.springjdbc.dao;

import net.sf.json.JSONObject;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017-04-24.
 */
public interface PosBillDao {
    String	NAME="com.tzx.pos.po.springjdbc.dao.imp.PosBillDaoImp";
    public List<JSONObject> posPayment(String billno, Integer organId) throws  Exception;
    public List<JSONObject> findPosPayment(String billno) throws  Exception;
    public List<JSONObject> findPosBill(Timestamp timestamp1, Timestamp timestamp) throws  Exception;
    public List<JSONObject> findPosBillTotal(String tenancyId,Integer storeId,String state)throws  Exception;
    /**
     * 发票统计
     * @param tenancy_id
     * @param storeId
     * @return
     * @throws Exception
     */
    public List<JSONObject> findPosBillInvoice(String tenancy_id,Integer storeId,Date reportDate,String optNum,String posNum,Map<String, Object> map) throws Exception;

    /**
     * 查询项目小计
     * @param tenancyId
     * @param storeId
     * @param
     * @return
     * @throws Exception
     */
    public List<JSONObject> findPosBillSumTotal(String tenancyId,Integer storeId,Date reportDate,String optNum,String posNum,Map<String, Object> map)throws  Exception;
    /**
     * @param tableCode
     * @return
     * @throws Exception
     */
    public List<JSONObject>findTableCode(String tableCode)throws Exception;
}
