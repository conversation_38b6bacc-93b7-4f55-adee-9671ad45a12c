package com.tzx.pos.base.print;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.Executors;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import net.sf.json.JSONObject;

/**
 *
 * <AUTHOR>
 * 2015年7月27日-下午3:58:14
 */
public class ThreadDishPoolManager
{
	private static ThreadDishPoolManager threadDishPoolManager = new ThreadDishPoolManager();
	// 线程池维护线程的最少数量
	private final static int CORE_POOL_SIZE = 1;
	// 线程池维护线程的最大数量
	private final static int MAX_POOL_SIZE = 20;
	// 线程池维护线程所允许的空闲时间
	private final static int KEEP_ALIVE_TIME = 0;
	// 线程池所使用的缓冲队列大小
	private final static int WORK_QUEUE_SIZE = 20;
	// 消息缓冲队列
	DishQueue dishQueue = DishQueueUtil.getQueue();
	
	// 访问消息缓存的调度线程
	final Runnable accessPrintThread = new Runnable()
	{
		public void run()
		{
			// 查看是否有待定请求，如果有，则创建一个新的pushPringMsgThread，并添加到线程池中
			if( hasMoreAcquire() )
			{
				JSONObject dish = (JSONObject) dishQueue.pull();
				Runnable task = new PrintDishThread( dish );
				threadPool.execute( task );
			}
		}
	};

	final RejectedExecutionHandler handler = new RejectedExecutionHandler()
	{
		public void rejectedExecution( Runnable r, ThreadPoolExecutor executor )
		{
			System.out.println(((PrintDishThread) r).getDish()+"厨打消息放入队列中重新等待执行");
			dishQueue.push(((PrintDishThread) r).getDish() );
		}
	};
		
	// 管理线程池
	@SuppressWarnings({ "rawtypes", "unchecked" })
	final ThreadPoolExecutor threadPool = new ThreadPoolExecutor(CORE_POOL_SIZE, MAX_POOL_SIZE, KEEP_ALIVE_TIME, TimeUnit.SECONDS,new ArrayBlockingQueue( WORK_QUEUE_SIZE ), this.handler );

	//调度线程池
	final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(CORE_POOL_SIZE);

	@SuppressWarnings("rawtypes")
	final ScheduledFuture taskHandler = scheduler.scheduleAtFixedRate(accessPrintThread, 0, 1, TimeUnit.SECONDS );

	public static ThreadDishPoolManager newInstance()
	{
		return threadDishPoolManager;
	}
	
	private ThreadDishPoolManager()
	{
		
	}

	private boolean hasMoreAcquire()
	{
		return !dishQueue.isEmpty();
	}
}
