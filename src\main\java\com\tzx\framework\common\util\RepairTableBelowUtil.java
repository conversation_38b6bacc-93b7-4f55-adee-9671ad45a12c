package com.tzx.framework.common.util;

import java.util.ArrayList;
import java.util.List;

import net.sf.json.JSONObject;

import com.tzx.framework.common.util.dao.GenericDao;


public class RepairTableBelowUtil {
	
	public static String getRepairTable(String tenancyId,GenericDao dao,String tableName) throws Exception
	{
		StringBuilder sb = new StringBuilder();
		sb.append("select column_name,data_type,COALESCE(column_default,'') as column_default,is_nullable,COALESCE(character_maximum_length,100) as character_maximum_length from information_schema.columns where table_name = '"+tableName+"' ");
		List<JSONObject> list = dao.query4Json(tenancyId, sb.toString());
		sb.setLength(0);
		if(list.size()>0)
		{
			Boolean hasId = false;
			for(JSONObject jo : list)
			{
				String column_name = jo.optString("column_name");
				String data_type = jo.optString("data_type");
				//COALESCE(column_default,'') as column_default,
				//is_nullable,
				//COALESCE(character_maximum_length,100) as character_maximum_length
				int length = jo.optInt("character_maximum_length");
				if("character varying".equals(data_type))
				{
					data_type = data_type +"("+length+")";
				}
				if("id".equals(column_name))
				{
					hasId = true;
				}
				else
				{
					sb.append(" DO $DO$");   
					sb.append(" DECLARE num INT;");  
					sb.append(" BEGIN");  
					sb.append(" select count(*) into num from information_schema.columns where table_name = '"+tableName+"' and column_name='"+column_name+"';");  
					sb.append(" IF num = 0 THEN "); 
					sb.append(" EXECUTE 'ALTER TABLE "+tableName+" ADD COLUMN  "+column_name+" "+data_type+";';");
					sb.append(" ELSE RAISE NOTICE 'column  already exists in this table';");
					sb.append(" END IF;");
					sb.append(" END$DO$;");  
				}
			}
//			if(sb.length()>0)
//			{
//				sb.delete(0, 1);
//			}
			
			if(hasId)
			{
				sb = sb.insert(0, "CREATE TABLE if not EXISTS "+tableName+" (id SERIAL NOT NULL,constraint PK_"+tableName+" primary key (id));");
			}
			else
			{
				sb = sb.insert(0, "CREATE TABLE if not EXISTS  "+tableName+" ();");
			}
			
		}
		return sb.toString();

	} 
	public static List<String> getRepairTableList(String tenancyId,GenericDao dao,String tableName) throws Exception
	{
		List<String> list2 = new ArrayList<String>();
		StringBuilder sb = new StringBuilder();
		sb.append("select column_name,data_type,COALESCE(column_default,'') as column_default,is_nullable,COALESCE(character_maximum_length,100) as character_maximum_length from information_schema.columns where table_name = '"+tableName+"' ");
		List<JSONObject> list = dao.query4Json(tenancyId, sb.toString());
		sb.setLength(0);
		if(list.size()>0)
		{
			Boolean hasId = false;
			list2.add("DROP TABLE if EXISTS "+tableName);
			
			sb.append("CREATE TABLE "+tableName+" (");
			for(JSONObject jo : list)
			{
				
				String column_name = jo.optString("column_name");
				String data_type = jo.optString("data_type");
				//COALESCE(column_default,'') as column_default,
				//is_nullable,
				//COALESCE(character_maximum_length,100) as character_maximum_length
				int length = jo.optInt("character_maximum_length");
				if("character varying".equals(data_type))
				{
					data_type = data_type +"("+length+")";
				}
				if("id".equals(column_name))
				{
					hasId = true;
					sb.append("id SERIAL NOT NULL,");
				}
				else
				{
					sb.append(column_name+" "+data_type+",");
				}
				
			}
			if(hasId)
			{
				sb.append("constraint PK_"+tableName+" primary key (id)");
			}
			else
			{
				sb.delete(sb.length()-1,sb.length());
			}
			sb.append(")");
			list2.add(sb.toString());
//			for(JSONObject jo : list)
//			{
//				String column_name = jo.optString("column_name");
//				String data_type = jo.optString("data_type");
//				//COALESCE(column_default,'') as column_default,
//				//is_nullable,
//				//COALESCE(character_maximum_length,100) as character_maximum_length
//				int length = jo.optInt("character_maximum_length");
//				if("character varying".equals(data_type))
//				{
//					data_type = data_type +"("+length+")";
//				}
//				if("id".equals(column_name))
//				{
//					hasId = true;
//				}
//				else
//				{
//					list2.add("ALTER TABLE "+tableName+" DROP COLUMN  IF EXISTS "+column_name+" "); 
//					list2.add("ALTER TABLE "+tableName+" ADD COLUMN  "+column_name+" "+data_type+" ");
//				}
//			}
//
//			if(hasId)
//			{
//				list2.add(0,"CREATE TABLE if not EXISTS "+tableName+" (id SERIAL NOT NULL,constraint PK_"+tableName+" primary key (id)) ");
//			}
//			else
//			{
//				list2.add(0, "CREATE TABLE if not EXISTS  "+tableName+" () ");
//			}
		}
		return list2;
	} 
	
//	public static void main(String[] args)
//	{
//		
//		List<JSONObject> list = new ArrayList<JSONObject>();
//		
//		//column_name,data_type,COALESCE(column_default,'') as column_default,is_nullable,COALESCE(character_maximum_length,100) as character_maximum_length
//		String tableName  = "abc";
//		JSONObject jo11 = new JSONObject();
//		jo11.put("column_name","tenancy_id");
//		jo11.put("data_type","character varying");		
//		jo11.put("column_default","");		
//		jo11.put("character_maximum_length",100);		
//		JSONObject jo1 = new JSONObject();
//
//		jo1.put("column_name","id");
//		jo1.put("data_type","integer");		
//		jo1.put("column_default","");		
//		jo1.put("character_maximum_length",100);	
//		JSONObject jo2 = new JSONObject();
//		jo2.put("column_name","card_id");
//		jo2.put("data_type","integer");		
//		jo2.put("column_default","");		
//		jo2.put("character_maximum_length",100);	
//		JSONObject jo3 = new JSONObject();
//		jo3.put("column_name","card_code");
//		jo3.put("data_type","character varying");		
//		jo3.put("column_default","");		
//		jo3.put("character_maximum_length",100);	
//		JSONObject jo4 = new JSONObject();
//		jo4.put("column_name","bill_code");
//		jo4.put("data_type","character varying");		
//		jo4.put("column_default","");		
//		jo4.put("character_maximum_length",100);	
//		list.add(jo11);
//		list.add(jo1);
//		list.add(jo2);
//		list.add(jo3);
//		list.add(jo4);
//		StringBuilder sb = new StringBuilder();
//		sb.setLength(0);
//		if(list.size()>0)
//		{
//			Boolean hasId = false;
//			for(JSONObject jo : list)
//			{
//				String column_name = jo.optString("column_name");
//				String data_type = jo.optString("data_type");
//				//COALESCE(column_default,'') as column_default,
//				//is_nullable,
//				//COALESCE(character_maximum_length,100) as character_maximum_length
//				int length = jo.optInt("character_maximum_length");
//				if("character varying".equals(data_type))
//				{
//					data_type = data_type +"("+length+")";
//				}
//				if("id".equals(column_name))
//				{
//					hasId = true;
//				}
//				else
//				{
//					sb.append(" DO $DO$");   
//					sb.append(" DECLARE num INT;");  
//					sb.append(" BEGIN");  
//					sb.append(" select count(*) into num from information_schema.columns where table_name = '"+tableName+"' and column_name='"+column_name+"';");  
//					sb.append(" IF num = 0 THEN "); 
//					sb.append(" EXECUTE 'ALTER TABLE "+tableName+" ADD COLUMN  "+column_name+" "+data_type+";';");
//					sb.append(" ELSE RAISE NOTICE 'column  already exists in this table';");
//					sb.append(" END IF;");
//					sb.append(" END$DO$;");  
//				}
//			}
////			if(sb.length()>0)
////			{
////				sb.delete(0, 1);
////			}
//			
//			if(hasId)
//			{
//				sb = sb.insert(0, "CREATE TABLE if not EXISTS "+tableName+" (id SERIAL NOT NULL,constraint PK_"+tableName+" primary key (id));");
//			}
//			else
//			{
//				sb = sb.insert(0, "CREATE TABLE if not EXISTS  "+tableName+" ();");
//			}
//			
//		}
//		
//		
//		System.out.println(sb.toString());
//	}
	
	
	
}
