package com.tzx.clientorder.wlifeprogram.common.util;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;

import java.util.Iterator;

/**
 * json 中的 key 转换成指定的 key
 */
public class JsonKeyTransform {

    public static void main(String[] args) throws Exception {
        JSONObject newParam = new JSONObject();


        JSONObject param = new JSONObject(); // 最外层
        param.put("business_id", 12121);
        param.put("shop_id", "abf");
        param.put("kkk", 2.03);

        JSONObject params = new JSONObject(); // 第二层
        params.put("tableno", 12121);
        params.put("ordermemo", "你好啊啊");
        params.put("kkk", 2.03);

        JSONArray array = new JSONArray(); // 第三层

        JSONObject param1 = new JSONObject();
        param1.put("cno", 12121);
        param1.put("qqQQQ", "abf");
        param1.put("newKKK", 2.03);

        JSONObject param2 = new JSONObject();
        param2.put("tableno", 12121);
        param2.put("vvv", "abf");
        param2.put("zzz", 2.03);

        array.add(param1);
        array.add(param2);

        params.put("items", array);

        JSONObject member = new JSONObject();
        member.put("openid", "0000");
        member.put("name", "0000");
        member.put("cno", "0000");

        params.put("member", member);

        param.put("order_info", params);

        System.out.println("11111========="+param.toString());

        JsonKeyTransform.getNewJson(param, newParam, "");

        System.out.println("22222========="+newParam.toString());



    }

    public static void getNewJson(JSONObject param, JSONObject newParam, String parentKey) throws Exception{
        Iterator<String> it = param.keys();
        if (StringUtils.isNotEmpty(parentKey)){

        }
        while (it.hasNext()){
            String key = it.next();
            // 得到value的值
            Object value = param.get(key);
            if (value instanceof JSONObject){
                // 递归
                getNewJson(JSONObject.fromObject(value), newParam, key);
            } else if (value instanceof JSONArray){
                JSONArray array = JSONArray.fromObject(value);
                for (int i = 0; i < array.size(); i++){
                    getNewJson(array.getJSONObject(i), newParam, key);
                }
            } else {
                if (StringUtils.isEmpty(parentKey)){
                    if ("business_id".equals(key)){
                        newParam.put("tenancy_id", value);
                        continue;
                    } else if ("shop_id".equals(key)){
                        newParam.put("store_id", value);
                        continue;
                    } else {
                        newParam.put(key, value);
                        continue;
                    }
                } else if ("order_info".equals(parentKey)){
                    if ("tableno".equals(key)){ // 桌台号
                        newParam.put("table_code", value);
                        continue;
                    } else if ("ordermemo".equals(key)){ // 备注
                        newParam.put("bill_taste", value);
                        continue;
                    } else {
                        newParam.put(key, value);
                        continue;
                    }
                } else if ("items".equals(parentKey)) {
                    if ("did".equals(key)){ // 菜品id
                        newParam.put("item_id", value);
                        continue;
                    } else if ("dishsno".equals(key)){ // 菜品速记码
                        newParam.put("item_num", value);
                        continue;
                    } else if ("name".equals(key)){ // 菜品名称
                        newParam.put("item_name", value);
                        continue;
                    } else if ("duid".equals(key)){ // 单位id
                        newParam.put("unit_id", value);
                        continue;
                    } else if ("number".equals(key)){ // 数量
                        newParam.put("item_count", value);
                        continue;
                    } else if ("price".equals(key)){ // 现价
                        newParam.put("item_price", value);
                        continue;
                    }

                } else if ("member".equals(parentKey)){
                    if ("cno".equals(key)){ // 卡号
                        newParam.put("card_code", value);
                        continue;
                    } else {
                        newParam.put(key, value);
                        continue;
                    }
                }
            }
        }
    }


}
