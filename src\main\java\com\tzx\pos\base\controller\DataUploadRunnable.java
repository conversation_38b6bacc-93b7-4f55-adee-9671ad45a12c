package com.tzx.pos.base.controller;

import java.io.File;
import java.util.Map;
import java.util.Properties;

import com.tzx.base.common.util.PosPropertyUtil;
import com.tzx.framework.common.util.TzxPropertiesUtil;
import com.tzx.pos.po.springjdbc.dao.PosPaymentDao;
import com.tzx.pos.po.springjdbc.dao.imp.PosPaymentDaoImp;
import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.util.HttpUtil;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.pos.base.dao.DataUploadDao;

/**
 * Created by Administrator on 2017-03-28.
 * 门店数据上传
 */
@Component
public class DataUploadRunnable implements  Runnable{

    private Logger logger = Logger.getLogger(DataUploadRunnable.class);

    private final String	postUrl	= "/api/upload/uploadBill";
    

    private  String tenancy_id;
    private  String store_id;
    private  String old_table_code;
    private  String new_table_code;
    private  String old_pos_bill;
    private  String new_pos_bill;

    public DataUploadRunnable(){}

    public DataUploadRunnable(String tenancy_id, String store_id,String old_table_code, String new_table_code, String old_pos_bill, String new_pos_bill) {
        this.tenancy_id = tenancy_id;
        this.store_id = store_id;
        this.old_table_code = old_table_code;
        this.new_table_code = new_table_code;
        this.old_pos_bill = old_pos_bill;
        this.new_pos_bill = new_pos_bill;
    }

    public void sendPost(String url,Data data){
		int i = 0;
		try {
			while (i < 3){
				logger.debug("数据上传失败。。。。。重试第"+(i+1)+"次");
				String req = HttpUtil.sendPostRequest(url,JSONObject.fromObject(data).toString());
				if (req == null){
						Thread.sleep(10000);//暂停10秒
					i = i +1;
					logger.debug("开始重试第" + i + "次");
				}else{
					break;
				}
			}
		} catch (Exception e) {
			logger.debug("重试第" + i + "次出现异常"+e);
		}
	}

    @Override
    public void run() {
        try {
        	Map<String, String> systemMap = Constant.getSystemMap();
			String postUrlStr=PosPropertyUtil.getMsg("secondpay.dataupload.url");
//			String postUrlStr="";
			if (StringUtils.isEmpty(postUrlStr))
			{
				logger.debug("配置文件获取请求地址参数为空，切换为CLASS方法获取");
				String msgFilePath=DataUploadRunnable.class.getResource("/").getPath()+ File.separator + "pos"+ File.separator +"pos_config.properties";
				File msgFile = new File(msgFilePath);
				Properties msgProperties = TzxPropertiesUtil.load(msgFile);
				postUrlStr=msgProperties.getProperty("secondpay.dataupload.url");
				logger.debug("切换后地址为："+postUrlStr);
			}
			Thread.sleep(1000);//暂停1秒保证数据上传一致
			DBContextHolder.setTenancyid(tenancy_id);
			PosPaymentDaoImp posPaymentServiceImp = (PosPaymentDaoImp) SpringConext.getApplicationContext().getBean(PosPaymentDao.NAME);
			String para_value=posPaymentServiceImp.getSysParameter(tenancy_id,Integer.parseInt(store_id),Constant.IFUP_QUICK_PAY);
			if("0".equals(para_value)||"".equals(para_value)){
				return ;
			}
            DataUploadDao uploadDao = SpringConext.getApplicationContext().getBean(DataUploadDao.class);
            logger.debug("数据上传参数:tenancy_id:"+tenancy_id + " store_id:"+store_id + " old_table_code:"+old_table_code + " new_table_code:"+new_table_code + " old_pos_bill:"+old_pos_bill+ " new_pos_bill:"+new_pos_bill);
            Data data = uploadDao.uploadData(tenancy_id,store_id,old_table_code,new_table_code,old_pos_bill,new_pos_bill);
            logger.info("上传数据查询"+JSONObject.fromObject(data).toString());
//            System.out.println(JSONObject.fromObject(data).toString());
			logger.debug("数据上传地址为post_url:>>>>>>"+postUrlStr+postUrl);
            String req = HttpUtil.sendPostRequest(postUrlStr+postUrl,JSONObject.fromObject(data).toString());
            logger.debug("返回结果"+req);
            if (req == null) {
				sendPost(postUrlStr+postUrl,data);
			}
        }catch (Exception e){
        	logger.debug("数据上传失败。。。。。"+e);
            e.printStackTrace();
        }
    }

	public Logger getLogger() {
		return logger;
	}

	public void setLogger(Logger logger) {
		this.logger = logger;
	}

	public String getTenancy_id() {
		return tenancy_id;
	}

	public void setTenancy_id(String tenancy_id) {
		this.tenancy_id = tenancy_id;
	}

	public String getStore_id() {
		return store_id;
	}

	public void setStore_id(String store_id) {
		this.store_id = store_id;
	}

	public String getOld_table_code() {
		return old_table_code;
	}

	public void setOld_table_code(String old_table_code) {
		this.old_table_code = old_table_code;
	}

	public String getNew_table_code() {
		return new_table_code;
	}

	public void setNew_table_code(String new_table_code) {
		this.new_table_code = new_table_code;
	}

	public String getOld_pos_bill() {
		return old_pos_bill;
	}

	public void setOld_pos_bill(String old_pos_bill) {
		this.old_pos_bill = old_pos_bill;
	}

	public String getNew_pos_bill() {
		return new_pos_bill;
	}

	public void setNew_pos_bill(String new_pos_bill) {
		this.new_pos_bill = new_pos_bill;
	}
    
}
