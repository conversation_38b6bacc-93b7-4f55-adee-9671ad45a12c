package com.tzx.clientorder.wlifeprogram.dao;

import java.util.Date;
import java.util.List;

import com.tzx.framework.common.util.dao.GenericDao;

import net.sf.json.JSONObject;

public interface WlifeCommonDao extends GenericDao
{
	/**
	 * 查询系统参数
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param para
	 * @return
	 * @throws Exception
	 */
	public String getSysParameter(String tenancyId, int storeId, String para) throws Exception;
	
    /**
     * 查询微生活的门店、品牌等参数
     *
     * @param tenancyId
     * @return
     * @throws Exception
     */
    List<JSONObject> getParam(String tenancyId) throws Exception;
    
    String getMemberType(String tenancyId) throws Exception;
	
	/**
	 * 获取报表日期
	 * 
	 * @param tenantId
	 * @param storeId
	 * @return
	 * @throws Exception
	 */
	public Date getReportDate(String tenantId, Integer storeId) throws Exception;
	
	/**
	 * 根据人员ID获取人员姓名
	 * 
	 * @param optNum
	 * @param tenancyId
	 * @param storeId
	 * @return
	 */
	public String getEmpNameById(String optNum, String tenancyId, Integer storeId) throws Exception;
	
	/** 记录日志
	 * @param tenantId
	 * @param organId
	 * @param posNum
	 * @param optNum
	 * @param optName
	 * @param shiftId
	 * @param reportDate
	 * @param title
	 * @param content
	 * @param oldstate
	 * @param newstate
	 */
	public void savePosLog(String tenantId, Integer organId, String posNum, String optNum, String optName, Integer shiftId, Date reportDate, String title, String content, String oldstate, String newstate);

}
