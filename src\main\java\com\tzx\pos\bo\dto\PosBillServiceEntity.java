package com.tzx.pos.bo.dto;

import java.io.Serializable;

public class PosBillServiceEntity implements Serializable
{

	
	/**
	 * 
	 */
	private static final long	serialVersionUID	= -8889951490777988032L;
	
	private int id;
	private String billNum;
	private String tableCode;
	private String stableCode;
	private int serviceId;
	private String serviceType;
	private String takenMode;
	private double serviceScale;
	private double serviceAmount;
	private double serviceCount;
	private double serviceTotal;
	private double serviceRate;
	
	public int getId()
	{
		return id;
	}
	public void setId(int id)
	{
		this.id = id;
	}
	public String getBillNum()
	{
		return billNum;
	}
	public void setBillNum(String billNum)
	{
		this.billNum = billNum;
	}
	public String getTableCode()
	{
		return tableCode;
	}
	public void setTableCode(String tableCode)
	{
		this.tableCode = tableCode;
	}
	public String getStableCode()
	{
		return stableCode;
	}
	public void setStableCode(String stableCode)
	{
		this.stableCode = stableCode;
	}
	public int getServiceId()
	{
		return serviceId;
	}
	public void setServiceId(int serviceId)
	{
		this.serviceId = serviceId;
	}
	public String getServiceType()
	{
		return serviceType;
	}
	public void setServiceType(String serviceType)
	{
		this.serviceType = serviceType;
	}
	public String getTakenMode()
	{
		return takenMode;
	}
	public void setTakenMode(String takenMode)
	{
		this.takenMode = takenMode;
	}
	public double getServiceScale()
	{
		return serviceScale;
	}
	public void setServiceScale(double serviceScale)
	{
		this.serviceScale = serviceScale;
	}
	public double getServiceAmount()
	{
		return serviceAmount;
	}
	public void setServiceAmount(double serviceAmount)
	{
		this.serviceAmount = serviceAmount;
	}
	public double getServiceCount()
	{
		return serviceCount;
	}
	public void setServiceCount(double serviceCount)
	{
		this.serviceCount = serviceCount;
	}
	public double getServiceTotal()
	{
		return serviceTotal;
	}
	public void setServiceTotal(double serviceTotal)
	{
		this.serviceTotal = serviceTotal;
	}
	public double getServiceRate()
	{
		return serviceRate;
	}
	public void setServiceRate(double serviceRate)
	{
		this.serviceRate = serviceRate;
	}
	

}
