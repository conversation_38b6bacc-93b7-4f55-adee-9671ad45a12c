/**
 * Copyright (c) 2005-2011 springside.org.cn
 * 
 * Licensed under the Apache License, Version 2.0 (the "License");
 * 
 * $Id: PropertiesLoader.java 1690 2012-02-22 13:42:00Z calvinxiu $
 */
package com.tzx.framework.common.util;

import java.io.IOException;
import java.io.InputStream;
import java.util.NoSuchElementException;
import java.util.Properties;

import org.apache.commons.io.IOUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Component;

/**
 * Properties文件载入工具类. 可载入多个properties文件,
 * 相同的属性在最后载入的文件中的值将会覆盖之前的值，但以System的Property优先.
 * 
 * <AUTHOR>
 */
@Component
public class PropertiesLoader implements InitializingBean
{

	private static Logger			logger			= Logger.getLogger(PropertiesLoader.class);

	private static ResourceLoader	resourceLoader	= new DefaultResourceLoader();

	private static Properties		properties		= null;

	public static final String[]	resourcesPaths	=
													{ "exceptions.properties"};

	public void afterPropertiesSet() throws Exception
	{
		properties = PropertiesLoader.loadProperties(resourcesPaths);
	}

	public static Properties getProperties()
	{
		return properties;
	}

	/**
	 * 取出Property。
	 */
	private static String getValue(String key)
	{
		String systemProperty = System.getProperty(key);
		if (systemProperty != null)
		{
			return systemProperty;
		}
		return properties.getProperty(key);
	}

	/**
	 * 取出String类型的Property,如果都為Null则抛出异常.
	 */
	public static String getProperty(String key)
	{
		String value = getValue(key);
		if (value == null)
		{
			throw new NoSuchElementException();
		}
		return value;
	}
	
	/**
	 * 取出String类型的Property,如果都為Null则抛出异常.
	 */
	public static String getProperty(Integer key)
	{
		if(key == null)
		{
			throw new NoSuchElementException();
		}
		
		String value = getValue(key.toString());
		if (value == null)
		{
			throw new NoSuchElementException();
		}
		return value;
	}

	/**
	 * 取出String类型的Property.如果都為Null則返回Default值.
	 */
	public static String getProperty(String key, String defaultValue)
	{
		String value = getValue(key);
		return value != null ? value : defaultValue;
	}

	/**
	 * 取出Integer类型的Property.如果都為Null或内容错误则抛出异常.
	 */
	public static Integer getInteger(String key)
	{
		String value = getValue(key);
		if (value == null)
		{
			throw new NoSuchElementException();
		}
		return Integer.valueOf(value);
	}

	/**
	 * 取出Integer类型的Property.如果都為Null則返回Default值，如果内容错误则抛出异常
	 */
	public static Integer getInteger(String key, Integer defaultValue)
	{
		String value = getValue(key);
		return value != null ? Integer.valueOf(value) : defaultValue;
	}

	/**
	 * 取出Double类型的Property.如果都為Null或内容错误则抛出异常.
	 */
	public static Double getDouble(String key)
	{
		String value = getValue(key);
		if (value == null)
		{
			throw new NoSuchElementException();
		}
		return Double.valueOf(value);
	}

	/**
	 * 取出Double类型的Property.如果都為Null則返回Default值，如果内容错误则抛出异常
	 */
	public static Double getDouble(String key, Integer defaultValue)
	{
		String value = getValue(key);
		return value != null ? Double.valueOf(value) : defaultValue;
	}

	/**
	 * 取出Boolean类型的Property.如果都為Null抛出异常,如果内容不是true/false则返回false.
	 */
	public static Boolean getBoolean(String key)
	{
		String value = getValue(key);
		if (value == null)
		{
			throw new NoSuchElementException();
		}
		return Boolean.valueOf(value);
	}

	/**
	 * 取出Boolean类型的Propert.如果都為Null則返回Default值,如果内容不为true/false则返回false.
	 */
	public static Boolean getBoolean(String key, boolean defaultValue)
	{
		String value = getValue(key);
		return value != null ? Boolean.valueOf(value) : defaultValue;
	}

	/**
	 * 载入多个文件, 文件路径使用Spring Resource格式.
	 */
	private static Properties loadProperties(String... resourcesPaths)
	{
		Properties props = new Properties();

		for (String location : resourcesPaths)
		{

			logger.debug("Loading properties file from path:{}"+location);
			InputStream is = null;
			try
			{
				Resource resource = resourceLoader.getResource(location);
				is = resource.getInputStream();
				props.load(is);
			}
			catch (IOException ex)
			{
				logger.info("Could not load properties from path:{}" + ex.getMessage());
			}
			finally
			{
				IOUtils.closeQuietly(is);
			}
		}
		return props;
	}
}
