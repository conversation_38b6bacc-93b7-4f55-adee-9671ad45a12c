package com.tzx.thirdpay.po.springjdbc.dao.imp;

import com.tzx.pos.base.dao.imp.BaseDaoImp;
import com.tzx.thirdpay.po.springjdbc.dao.DouyinCouponsDao;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> at 2021-12-30
 */
@Repository(DouyinCouponsDao.NAME)
public class DouyinCouponsDaoImpl extends BaseDaoImp implements DouyinCouponsDao  {
    @Override
    public void saveDouyinCoupons(List<JSONObject> params) throws Exception {
        String encryptedCodes="";
        for(JSONObject param:params){
            encryptedCodes+= ",'"+ param.optString("encrypted_code")+"'";
        }
        if(StringUtils.isNotEmpty(encryptedCodes)){
            this.execute(null,"delete from pos_bill_payment_coupous_douyin where encrypted_code in ("+encryptedCodes.replaceFirst(",","")+")");
        }
        this.insertBatchIgnorCase(null,"pos_bill_payment_coupous_douyin",params);
    }
}
