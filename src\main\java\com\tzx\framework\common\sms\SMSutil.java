package com.tzx.framework.common.sms;


import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.HttpException;
import org.apache.commons.httpclient.HttpStatus;
import org.apache.commons.httpclient.NameValuePair;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import com.tzx.framework.common.sms.SMSUtils;
import com.tzx.framework.common.sms.WinnerLookSMSUtils;
import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.HttpUtil;
import com.tzx.framework.common.util.Scm;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.impl.GenericDaoImpl;

public class SMSutil
{
	private static String reqURL = "http://qxt.1166.com.cn/qxtsys/recv_center?";
	private static String YDSK_GETBALANCE_URL = "http://qxt.1166.com.cn/qxtsys/QueryBlance";
	private static final Logger		log	= Logger.getLogger(SMSutil.class);
	/**
	 * @param args
	 * @throws IOException
	 * @throws HttpException
	 */
	 private GenericDaoImpl dao = (GenericDaoImpl) SpringConext.getBean("genericDaoImpl");

	
	public static int sendNewSmsP(String[] targetPhoneNumberArray, String sendMessage,String sn,String pwd,String msgSign,int type)
	{
		try
		{
			if((sn==null||"".equals(sn)||"null".equals(sn)) || (pwd==null||"".equals(pwd)||"null".equals(pwd)) || (msgSign==null||"".equals(msgSign)||"null".equals(msgSign)))
			{
				return 0;
			}
			
			//根据租户ID得到所用的账号和密码以及对应的签名信息
			String newkey = sn;
			String newpassword = pwd;
//			String sign = msgSign.replace("[","【").replace("]","】");
//			String content = URLEncoder.encode(sendMessage.trim()+sign, "UTF-8");
			StringBuilder sb = new StringBuilder();
			if (targetPhoneNumberArray != null && targetPhoneNumberArray.length > 0)
			{
				for (String m : targetPhoneNumberArray)
				{
					if(m != null && !m.isEmpty())
					{
						if(sb.length() > 0)
						{
							sb.append(",");
						}
						
						sb.append(m);
					}
				}
			}
			if(type==1)
			{
				String sign = msgSign.replace("[","【").replace("]","】");
				String content = URLEncoder.encode(sendMessage.trim()+sign, "UTF-8");
				StringBuilder sburl = new StringBuilder(reqURL);
				
				sburl = sburl.append("CpName="+sn+"&CpPassword="+pwd+"&DesMobile="+sb.toString()+"&Content="+content);
				//System.out.println(sburl.toString());
				String resultString = HttpUtil.sendPostRequest(sburl.toString(),"");
				JSONObject jo = JSONObject.fromObject(resultString);
				int code = jo.optInt("code");
				if(code!=0)
				{
					System.out.print("发送失败！返回值为：" + resultString + "请查看返回值对照表");
					
				}
				if(code==0)
				{
					return 1;
				}
				return code;
			}
			else
			{
				String sign = msgSign;
				String content = URLEncoder.encode(sendMessage.trim()+sign, "UTF-8");
				Client client = new Client(newkey, newpassword);
				if(sb.length() > 0)
				{
					String result_mt = client.mdSmsSend_u(sb.toString(), content, "", "", "");//content 后面设置1 可以设置新签名
					if (result_mt.startsWith("-") || result_mt.equals(""))// 发送短信，如果是以负号开头就是发送失败。
					{
						System.out.print("发送失败！返回值为：" + result_mt + "请查看webservice返回值对照表");
						return 0;
					}
					else
					{
						return 1;
					}
				}
			}
			
			//短信签名需要放在最后 不然移动端接受短信有问题
			
			
		}
		catch (Throwable e)
		{
			e.printStackTrace();
		}

		return 0;
	}

	/**
	 * 发送交易短信新方法
	 * 会传入多个平台交易短信账户
	 * @param tenancyId
	 * @param accountlist
	 * @param mobils
	 * @param msg
	 * @param count
	 * @return
	 */
	public static int sendSMSbyOrder(String tenancyId,List<JSONObject> accountlist, String[] mobils, String msg,JSONObject count) {
		boolean flag = false;
		StringBuffer sb = new StringBuffer();
		StringBuilder target_mobils = new StringBuilder();
		if (mobils != null && mobils.length > 0){
			for (String m : mobils){
				if(m != null && !m.isEmpty()){
					if(target_mobils.length() > 0){
						target_mobils.append(",");
					}
					target_mobils.append(m);
				}
			}
		}
		
		for(int i=0;i<accountlist.size();i++){
			JSONObject account = JSONObject.fromObject(accountlist.get(i));
			String sms_platform_type = account.optString("sms_platform_type").trim();//短信平台类型
			String sn = account.optString("sms_account").trim();
			String pwd = account.optString("sms_pwd").trim();
			String sign = account.optString("sms_sign");
			String message = msg+sign;
			
			count.put("c_name", sn);
			count.put("c_sign", sign);
			
			if(Constant.SMS_PLATFORM_MD.equals(sms_platform_type.trim())){//漫道
				count.put("c_type", 0);
				count.put("c",SMSUtils.oldSmsGetMsgLength(message));
				JSONObject json = sendMdJySms(target_mobils.toString(), msg, sn, pwd, sign);
				if(json.optBoolean("success")){
					flag = true;
					break;
				}else{
					String content = parseContent(json,"漫道");
					sb.append(content);
					flag = false;
					continue;
				}
				
			}else if(Constant.SMS_PLATFORM_YDSK.equals(sms_platform_type.trim())){//移动时空
				count.put("c_type", 1);
				count.put("c",SMSUtils.newSmsGetMsgLength(message));
				JSONObject json = sendYDSKJySms(target_mobils.toString(), msg, sn, pwd, sign);
				if(json.optBoolean("success")){
					flag = true;
					break;
				}else{
					String content = parseContent(json,"移动时空");
					sb.append(content);
					flag = false;
					continue;
				}
				
			}else if(Constant.SMS_PLATFORM_YXLK.equals(sms_platform_type.trim())){//云信留客
				count.put("c_type", 2);
				count.put("c",SMSUtils.YXLKGetMsgLength(message));
				JSONObject param = new JSONObject();
				param.put("userCode", sn);
				param.put("userPass", pwd);
				param.put("DesNo", target_mobils.toString());
				param.put("Msg", msg);
				param.put("sign", sign);
				param.put("Channel", 0);
				JSONObject json = WinnerLookSMSUtils.sendHighSpeedSmS(param);
				if(json.optBoolean("success")){
					flag = true;
					break;
				}else{
					String content = parseContent(json,"云信留客");
					sb.append(content);
					flag = false;
					continue;
				}
			}
		}
		if(flag){
			return 1;
		}
		
		log.info(sb.toString());
		System.out.println(sb.toString());
		return 0;
	}
	
	
	private static String parseContent(JSONObject json,String name){
		StringBuffer sb = new StringBuffer();
		sb.append(name+"平台短信发送失败,失败原因:"+json.optString("reason")+",");
		sb.append("返回值为:"+json.optString("returnCode")+",");
		if(json.containsKey("exp")){
			sb.append("异常信息为:"+json.optString("exp")+",");
		}
		sb.append("   /n");
		return sb.toString();
	}
	
	/**
	 * 漫道交易短信发送
	 * @param targetPhoneNumberArray
	 * @param sendMessage
	 * @param sn
	 * @param pwd
	 * @param msgSign
	 * @return
	 */
	private static JSONObject sendMdJySms(String targetPhoneNumberArray, String sendMessage,String sn,String pwd,String msgSign){
		JSONObject returnJson = new JSONObject();
		returnJson.put("success", false);
		returnJson.put("returnCode", -1.11);
		String result_mt = "";
		try {
			if((sn==null||"".equals(sn)||"null".equals(sn)) || (pwd==null||"".equals(pwd)||"null".equals(pwd)) || (msgSign==null||"".equals(msgSign)||"null".equals(msgSign)))
			{	
				returnJson.put("success", false);
				returnJson.put("reason", "参数不全");
				return returnJson;
			}
			
			String newkey = sn;
			String newpassword = pwd;
			
			String sign = msgSign;
			String content = URLEncoder.encode(sendMessage.trim()+sign, "UTF-8");
			Client client = new Client(newkey, newpassword);
			if(targetPhoneNumberArray.length() > 0)
			{
				result_mt = client.mdSmsSend_u(targetPhoneNumberArray, content, "", "", "");//content 后面设置1 可以设置新签名
				if (result_mt.startsWith("-") || result_mt.equals(""))// 发送短信，如果是以负号开头就是发送失败。
				{
					
					returnJson.put("success", false);
					returnJson.put("reason", "返回值不正确,短信发送失败");
					returnJson.put("returnCode", result_mt);
					return returnJson;
				}
				else
				{
					returnJson.put("success", true);
					returnJson.put("returnCode", result_mt);
					return returnJson;
				}
			}
		} catch (Exception e) {
			returnJson.put("success", false);
			returnJson.put("reason", "调用发生异常,短信发送失败");
			returnJson.put("returnCode", -2.2);
			StringWriter sw = new StringWriter();   
            e.printStackTrace(new PrintWriter(sw, true));   
            String strs = sw.toString();  
            returnJson.put("exp", strs);
			e.printStackTrace();
			//log.info("漫道短信发送失败,返回值为:"+result_mt+","+e.getMessage());
		}
		return returnJson;
	}
	
	/**
	 * 漫道营销短信发送方法
	 * @param targetPhoneNumberArray
	 * @param sendMessage
	 * @param sn
	 * @param pwd
	 * @param msgSign
	 * @return
	 * 		
	 * 		json对象中key：
	 *				 returnJson.put("success", false);
					 returnJson.put("reason", "返回值不正确,短信发送失败");
					 returnJson.put("returnCode", result_mt);当returnCode = -1.1时表示参数缺失
					 returnJson.put("list", mobilslist);
	 */
	private static JSONObject sendMdYXSms(List<String> mobilslist, String sendMessage,String sn,String pwd,String msgSign,JSONObject loginfo,String tenancyId){
		JSONObject returnJson = new JSONObject();
		returnJson.put("success", false);
		returnJson.put("list", mobilslist);
		String result_mt = "";
		try {
			if((sn==null||"".equals(sn)||"null".equals(sn)) || (pwd==null||"".equals(pwd)||"null".equals(pwd)) || (msgSign==null||"".equals(msgSign)||"null".equals(msgSign)))
			{	
				returnJson.put("success", false);
				returnJson.put("reason", "短信参数不完整");
				returnJson.put("returnCode", -1.1);
				log.info("漫道营销短信发送失败,短信参数不完整,请检查crm_sms_account_details表中数据");
				return returnJson;
			}
			
			String newkey = sn;
			String newpassword = pwd;
			
			String sign = msgSign;
			String content = URLEncoder.encode(sendMessage.trim()+sign, "UTF-8");
			Client client = new Client(newkey, newpassword);
			//获取剩余条数
			Integer balance = Integer.valueOf(client.getBalance());
			if(balance<=0){
				returnJson.put("success", false);
				log.info("漫道营销短信发送失败，查询该平台剩余条数返回值为"+balance);
				return returnJson;
			}
			log.info("漫道接口返回最新剩余短信条数为："+balance);
			
			List<String> mobils = new ArrayList<String>();
			Integer smslength = SMSUtils.oldSmsGetMsgLength(sendMessage.trim()+sign);//短信条数
	
			//最多能发送人数 = 剩余短信条数/短信条数
			Integer mobilslength = balance/ smslength;
			if(mobilslist.size()<=mobilslength){
				mobils = mobilslist.subList(0, mobilslist.size());
			}else{
				mobils = mobilslist;
			}
			log.info("批量发送人数为："+mobils.size()+",累计发送条数为："+mobils.size()*smslength);
			if(mobils.size() > 0)
			{
				result_mt = client.mdSmsSend_u(StringUtils.join(mobils.toArray(),","), content, "", "", "");//content 后面设置1 可以设置新签名
				if (result_mt.startsWith("-") || result_mt.equals("")){// 发送短信，如果是以负号开头就是发送失败。
					
					returnJson.put("success", false);
					returnJson.put("reason", "返回值不正确,短信发送失败");
					returnJson.put("returnCode", result_mt);
					returnJson.put("list", mobilslist);
					return returnJson;
				}else{
					returnJson.put("success", true);
					returnJson.put("returnCode", result_mt);
					loginfo.put("sms_platform_type", 0);
					loginfo.put("sms_nums", smslength);
					saveSMSlogs(mobils, sendMessage.trim()+sign, sn, sign, loginfo,tenancyId,"1","营销短信");
					mobilslist.removeAll(mobils);
					returnJson.put("list", mobilslist);
				
					return returnJson;
				}
			}
		} catch (Exception e) {
			returnJson.put("success", false);
			returnJson.put("reason", "调用发生异常,短信发送失败");
			returnJson.put("returnCode", -2.2);
			StringWriter sw = new StringWriter();   
            e.printStackTrace(new PrintWriter(sw, true));   
            String strs = sw.toString();  
            returnJson.put("exp", strs);
			e.printStackTrace();
		}
		return returnJson;
	}
	
	
	private static JSONObject sendYDSKJySms(String targetPhoneNumberArray, String sendMessage,String sn,String pwd,String msgSign){
		JSONObject returnJson = new JSONObject();
		returnJson.put("success", false);
		returnJson.put("returnCode", -1.11);
		String result_mt = "";
		try {
			if((sn==null||"".equals(sn)||"null".equals(sn)) || (pwd==null||"".equals(pwd)||"null".equals(pwd)) || (msgSign==null||"".equals(msgSign)||"null".equals(msgSign)))
			{	
				returnJson.put("success", false);
				returnJson.put("reason", "参数不全");
				return returnJson;
			}
		
			String sign = msgSign.replace("[","【").replace("]","】");
			String content = URLEncoder.encode(sendMessage.trim()+sign, "UTF-8");
			StringBuilder sburl = new StringBuilder(reqURL);
			sburl = sburl.append("CpName="+sn+"&CpPassword="+pwd+"&DesMobile="+targetPhoneNumberArray+"&Content="+content);

			String resultString = HttpUtil.sendPostRequest(sburl.toString(),"");
			JSONObject jo = JSONObject.fromObject(resultString);
			int code = jo.optInt("code");
			if(code!=0){
				returnJson.put("success", false);
				returnJson.put("reason", "返回值不正确,短信发送失败");
				returnJson.put("returnCode", code);
				return returnJson;
			}
			if(code==0){
				returnJson.put("success", true);
				returnJson.put("returnCode", code);
				return returnJson;
			}
			
			
		} catch (Exception e) {
			returnJson.put("success", false);
			returnJson.put("reason", "调用发生异常,短信发送失败");
			returnJson.put("returnCode", -2.2);
			StringWriter sw = new StringWriter();   
            e.printStackTrace(new PrintWriter(sw, true));   
            String strs = sw.toString();  
            returnJson.put("exp", strs);
            
			e.printStackTrace();
			//log.info("移动时空短信发送失败,返回值为:"+result_mt+","+e.getMessage());
		}
		return returnJson;
	}
	
	
	
	/**
	 * 发送移动时空营销短信
	 * @param targetPhoneNumberArray
	 * @param sendMessage
	 * @param sn
	 * @param pwd
	 * @param msgSign
	 * @return
	 */
	private static JSONObject sendYDSKYxSms(List<String> mobileslist, String sendMessage,String sn,String pwd,String msgSign,JSONObject loginfo,String tenancyId){
		JSONObject returnJson = new JSONObject();
		returnJson.put("success", false);
		returnJson.put("returnCode", -1.11);
		returnJson.put("list", mobileslist);
		try {
			if((sn==null||"".equals(sn)||"null".equals(sn)) || (pwd==null||"".equals(pwd)||"null".equals(pwd)) || (msgSign==null||"".equals(msgSign)||"null".equals(msgSign)))
			{	
				returnJson.put("success", false);
				returnJson.put("reason", "参数不全");
				return returnJson;
			}
			JSONObject balance4ydsk = getBalance4YDSK(sn, pwd);
			if(!balance4ydsk.optBoolean("success")){
				returnJson.put("success", false);
				log.info("移动时空营销短信查询失败，查询该平台剩余条数返回值为"+balance4ydsk.optString("returnCode"));
				return returnJson;
			}
			Integer balance = Integer.valueOf(balance4ydsk.optString("returnCode"));
			log.info("移动时空接口返回最新剩余短信条数为："+balance);
			List<String> mobils = new ArrayList<String>();
			Integer smslength = SMSUtils.newSmsGetMsgLength(sendMessage.trim()+msgSign);//短信条数
			//最多能发送人数 = 剩余短信条数/短信条数
			Integer mobilslength = balance/ smslength;
			if(mobileslist.size()<=mobilslength){
				mobils = mobileslist.subList(0, mobileslist.size());
			}else{
				mobils = mobileslist;
			}
			log.info("批量发送人数为："+mobils.size()+",累计发送条数为："+mobils.size()*smslength);
			
		
			String content = URLEncoder.encode(sendMessage.trim()+msgSign, "UTF-8");
			StringBuilder sburl = new StringBuilder(reqURL);
			
			sburl = sburl.append("CpName="+sn+"&CpPassword="+pwd+"&DesMobile="+StringUtils.join(mobils.toArray(),",")+"&Content="+content);
			String resultString = HttpUtil.sendPostRequest(sburl.toString(),"");
			JSONObject jo = JSONObject.fromObject(resultString);
			int code = jo.optInt("code");
			if(code!=0){
				returnJson.put("success", false);
				returnJson.put("reason", "返回值不正确,短信发送失败");
				returnJson.put("returnCode", code);
				returnJson.put("list", mobileslist);
				return returnJson;
			}
			if(code==0){//成功
				returnJson.put("success", true);
				returnJson.put("returnCode", code);
				loginfo.put("sms_platform_type", 1);
				loginfo.put("sms_nums", smslength);
				saveSMSlogs(mobils, sendMessage.trim()+msgSign, sn, msgSign, loginfo,tenancyId,"1","营销短信");
				mobileslist.removeAll(mobils);
				returnJson.put("list", mobileslist);
				
				return returnJson;
			}
			
			
		} catch (Exception e) {
			returnJson.put("success", false);
			returnJson.put("reason", "调用发生异常,短信发送失败");
			returnJson.put("returnCode", -2.2);
			returnJson.put("list", mobileslist);
			StringWriter sw = new StringWriter();   
            e.printStackTrace(new PrintWriter(sw, true));   
            String strs = sw.toString();  
            returnJson.put("exp", strs);
			e.printStackTrace();
		}
		return returnJson;
	}
	
	private static List<String> arrayToList(String[] arr){
		List<String> list = new ArrayList<String>();
		for (int i = 0; i < arr.length; i++) {
			list.add(arr[i]);
		}
		return list;
	}
	
	
	/**
	 * sendYXSMSbyOrder 营销短信发送轮询方法  该方法一次最多传入500个手机号码
	 * @param tenancyId 商户号码
	 * @param accountlist 账户list
	 * @param mobils 手机号码
	 * @param msg 消息（不包含签名）
	 * @param count 短信日志记录相关内容  该方法会记录相关短信日志
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static int sendYXSMSbyOrder(String tenancyId,List<JSONObject> accountlist, String[] mobils, String msg,
			JSONObject loginfo) {
		
		boolean flag = true;
		StringBuffer sb = new StringBuffer();
		List<String> mobilslist = arrayToList(mobils);
		
		
		for(int i=0;i<accountlist.size();i++){
			if(mobilslist.size()<=0){
				break;
			}
			JSONObject account = JSONObject.fromObject(accountlist.get(i));
			String sms_platform_type = account.optString("sms_platform_type").trim().toUpperCase();
			String sn = account.optString("sms_account").trim();
			String pwd = account.optString("sms_pwd").trim();
			String sign = account.optString("sms_sign");
			loginfo.put("sms_platform_type", sms_platform_type);
			if(Constant.SMS_PLATFORM_MD.equals(sms_platform_type)){
				
				JSONObject sendMdYXSms = sendMdYXSms(mobilslist, msg, sn, pwd, sign,loginfo,tenancyId);
				if(sendMdYXSms.optBoolean("success")){
					mobilslist = sendMdYXSms.getJSONArray("list");
				}else{
					flag = false;
				}
			}else if(Constant.SMS_PLATFORM_YDSK.equals(sms_platform_type)){
				JSONObject sendYDSKYxSms = sendYDSKYxSms(mobilslist, msg, sn, pwd, sign,loginfo,tenancyId);
				if(sendYDSKYxSms.optBoolean("success")){
					mobilslist = sendYDSKYxSms.getJSONArray("list");
				}else{
					flag = false;
				}
			}else if(Constant.SMS_PLATFORM_YXLK.equals(sms_platform_type)){
				JSONObject sendYXLKYxSms = sendYXLKYxSms(mobilslist, msg, sn, pwd, sign,loginfo,tenancyId,account.optDouble("price",0.00),account.optString("sms_chanel"));
				if(sendYXLKYxSms.optBoolean("success")){
					mobilslist = sendYXLKYxSms.getJSONArray("list");
				}else{
					flag = false;
				}
			}
		}
		
		if(mobilslist.size()<=0){
			return 1;
		}
		//保存成功的记录是分别放在每个不同平台的方法中存储，失败了在这里统一存储
		saveSMSlogs(mobilslist, msg, "", "", loginfo, tenancyId,"-1000","营销短信");
		return 0;
	}
	
	/**
	 * 
	 * @param mobilslist
	 * @param msg
	 * @param sn
	 * @param pwd
	 * @param sign
	 * @return
	 * 		returnJson.put("success", false);
			returnJson.put("returnCode", sendNormalSmS.optString("returnCode"));
			returnJson.put("list", mobilslist);
	 */
	private static JSONObject sendYXLKYxSms(List<String> mobilslist,String msg, String sn, String pwd, String sign,JSONObject loginfo,String tenancyId,double price,String chanel) {
		JSONObject returnJson = new JSONObject();
		returnJson.put("success", false);
		returnJson.put("list", mobilslist);
		JSONObject param = new JSONObject();
		param.put("userCode", sn);
		param.put("userPass", pwd);
		JSONObject normalSmSGetBalance = null;
		
		try {
			normalSmSGetBalance = WinnerLookSMSUtils.normalSmSGetBalance(param);
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		if(!normalSmSGetBalance.optBoolean("success")){
			returnJson.put("success", false);
			log.info("云信留客营销短信查询失败，查询该平台剩余条数返回值为"+normalSmSGetBalance.optString("returnCode"));
			return returnJson;
		}
		Integer balance = Scm.pdiv(normalSmSGetBalance.optDouble("returnCode"), price).intValue();
		log.info("云信留客接口返回最新剩余短信条数为："+balance);
		List<String> mobils = new ArrayList<String>();
		Integer smslength = SMSUtils.YXLKGetMsgLength(msg.trim()+sign);//短信条数
		//最多能发送人数 = 剩余短信条数/短信条数
		Integer mobilslength = balance/ smslength;
		if(mobilslist.size()<=mobilslength){
			mobils = mobilslist.subList(0, mobilslist.size());
		}else{
			mobils = mobilslist;
		}
		log.info("批量发送人数为："+mobils.size()+",累计发送条数为："+mobils.size()*smslength);
		
		param.put("DesNo", StringUtils.join(mobils.toArray(), ","));
		param.put("Msg", msg);
		param.put("sign", sign);
		param.put("Channel",chanel);
		JSONObject sendNormalSmS = WinnerLookSMSUtils.sendNormalSmS(param);
		if(sendNormalSmS.optBoolean("success")){
			returnJson.put("success", true);
			returnJson.put("returnCode", sendNormalSmS.optString("returnCode"));
			loginfo.put("sms_platform_type", 2);
			loginfo.put("sms_nums", smslength);
			saveSMSlogs(mobils, msg.trim()+sign, sn, sign, loginfo,tenancyId,"1","营销短信");
			mobilslist.removeAll(mobils);
			returnJson.put("list", mobilslist);
			
		}else{
			returnJson.put("success", false);
			returnJson.put("returnCode", sendNormalSmS.optString("returnCode"));
			returnJson.put("list", mobilslist);
		}
		return returnJson;
	}
	
	

	/**
	 * 
	 * @param sn 用户名
	 * @param pwd 密码
	 * @return success 
	 * 		 returnCode
	 */
	public static JSONObject getBalance4YDSK(String sn,String pwd){
		JSONObject returnjson = new JSONObject();
		int tempValue = 0;
		try {
			String condition = "?CpName="+sn+"&CpPassword="+pwd;
			String returnStr = HttpUtil.sendPostRequest(YDSK_GETBALANCE_URL+condition, "");
			tempValue = Integer.valueOf(returnStr);
			if(tempValue>=0){
				returnjson.put("success", true);
				returnjson.put("returnCode", tempValue);
			}else{
				returnjson.put("success", false);
				returnjson.put("returnCode", tempValue);
			}
		} catch (Exception e) {
			returnjson.put("success", false);
			returnjson.put("returnCode", tempValue);
			e.printStackTrace();
		}
		
		return returnjson;
	}
	
	/**
	 * 保存营销短信日志   发送成功后调用该方法
	 * @param mobilslist 已经发送的手机号码
	 * @param msg 短信内容
	 * @param sn 用户名
	 * @param sign 签名
	 * @param count 短信日志对象
	 * @param sendstate 成功失败标记 
	 * @param jyOrYx 交易或者营销短信  trading_type
	 * @return
	 */
	public static void saveSMSlogs(List<String> mobilslist,String msg, String sn, String sign,JSONObject 
			loginfo,String tenancyId,String sendstate,String jyOrYx){
		
		String sendtime = DateUtil.getNowDateYYDDMMHHMMSS();
		List<JSONObject> loglist = new ArrayList<JSONObject>();
		for(String mobil : mobilslist){
			JSONObject newlog = new JSONObject();
			newlog.put("mobil", mobil);
			newlog.put("trading_type", jyOrYx);
			newlog.put("info_content", msg);
			newlog.put("send_state", sendstate);
			newlog.put("send_time", sendtime);
			newlog.put("store_id", loginfo.optString("store_id"));
			newlog.put("sms_nums", loginfo.optInt("sms_nums"));
			newlog.put("sms_account", sn);
			newlog.put("sms_platform_type",loginfo.optString("sms_platform_type"));
			loglist.add(newlog);
		}
		
		if(loglist.size()>0){
			GenericDaoImpl dao = (GenericDaoImpl) SpringConext.getBean("genericDaoImpl");
			try {
				dao.insertBatchIgnorCase(tenancyId, "crm_info_send", loglist);
			} catch (Exception e) {
				log.info(jyOrYx+"日志保存失败，短信内容为"+msg);
				log.info(jyOrYx+"日志保存失败，发送人："+StringUtils.join(mobilslist.toArray(),","));
				e.printStackTrace();
			}
		}
	}
	
	/**
	 * 用于交易短讯日志记录
	 */
	public static void saveJySmsLogs(List<String> mobilslist,String msg, String sn, String sign,JSONObject loginfo,String tenancyId,String sendstate){
		String sendtime = DateUtil.getNowDateYYDDMMHHMMSS();
		List<JSONObject> loglist = new ArrayList<JSONObject>();
		for(String mobil : mobilslist){
			JSONObject newlog = new JSONObject();
			newlog.put("mobil", mobil);
			newlog.put("trading_type", "营销短信");
			newlog.put("info_content", msg);
			newlog.put("send_state", sendstate);
			newlog.put("send_time", sendtime);
			newlog.put("store_id", loginfo.optString("store_id"));
			newlog.put("sms_nums", loginfo.optInt("sms_nums"));
			newlog.put("sms_account", sn);
			newlog.put("sms_platform_type",loginfo.optString("sms_platform_type"));
			loglist.add(newlog);
		}
		
		if(loglist.size()>0){
			GenericDaoImpl dao = (GenericDaoImpl) SpringConext.getBean("genericDaoImpl");
			try {
				dao.insertBatchIgnorCase(tenancyId, "crm_info_send", loglist);
			} catch (Exception e) {
				log.info("交易短信日志保存失败，短信内容为"+msg);
				log.info("交易短信日志保存失败，发送人："+StringUtils.join(mobilslist.toArray(),","));
				e.printStackTrace();
			}
		}
	}
	
}
