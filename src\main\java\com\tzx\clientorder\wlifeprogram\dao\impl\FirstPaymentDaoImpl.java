package com.tzx.clientorder.wlifeprogram.dao.impl;

import com.tzx.clientorder.wlifeprogram.common.entity.MicroLifeOrder;
import com.tzx.clientorder.wlifeprogram.dao.FirstPaymentDao;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.clientorder.common.entity.AcewillOrderNormalitem;
import com.tzx.clientorder.common.entity.AcewillOrderSetmeal;
import com.tzx.clientorder.common.entity.AcewillOrderSetmealItem;
import net.sf.json.JSONObject;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;
import java.util.*;

/**
 * Created by zds on 2018-08-14.
 */
@Repository(FirstPaymentDao.NAME)
public class FirstPaymentDaoImpl extends WlifeCommonDaoImpl implements FirstPaymentDao {

    @Override
    public String getTableState(String tenancyId, int storeId, String tableCode) throws Exception {
        String sql = "select s.state from pos_tablestate s where s.tenancy_id = ? and s.store_id = ? and s.table_code = ? ";

        SqlRowSet rs = this.jdbcTemplate.queryForRowSet(sql, new Object[]{tenancyId, storeId, tableCode});
        if(rs.next()){
            return rs.getString("state");
        }
        return "";
    }

    @Override
    public List<JSONObject> getBillInfoByOrderNum(String tenancyId, int storeId, String orderNum) throws Exception {
        String sql = "select b.* from pos_bill b where b.order_num = ? and b.tenancy_id = ? and b.store_id = ? ";
        return this.query4Json(tenancyId, sql, new Object[]{orderNum, tenancyId, storeId});
    }

    @Override
    public Map<String, String> getUnitNameMap(MicroLifeOrder microLifeOrder) throws Exception {
        List<String> duids = new ArrayList<>();
        List<AcewillOrderSetmeal> setmealList = microLifeOrder.getOrder_info().getSetmeal();
        List<AcewillOrderNormalitem> normalitems = microLifeOrder.getOrder_info().getNormalitems();
        if(setmealList!=null) {
            for (AcewillOrderSetmeal setmeal : setmealList) {
                List<AcewillOrderSetmealItem> setmealItemList = new ArrayList<>();
                if(null != setmeal.getMaindish()){
                    setmealItemList.addAll(setmeal.getMaindish());
                }
                if(null != setmeal.getMandatory()){
                    setmealItemList.addAll(setmeal.getMandatory());
                }

                if (null != setmeal.getOptional()){
                    setmealItemList.addAll(setmeal.getOptional());
                }else {
                    List<AcewillOrderSetmealItem> itemList = new ArrayList<>();
                    setmealItemList.addAll(itemList);
                }
                for (AcewillOrderSetmealItem setmealItem : setmealItemList) {
                    duids.add(setmealItem.getDuid());
                }
                duids.add(setmeal.getDuid());
            }
        }
        if(normalitems!= null){
            for (AcewillOrderNormalitem normalitem : normalitems) {
                duids.add(normalitem.getDuid());
            }
        }

        String duidStr = StringUtils.collectionToDelimitedString(duids, ",");
        Map<String,String> unitNameMap = new HashMap<>();
        if(!StringUtils.isEmpty(duidStr)) {
            StringBuilder sql = new StringBuilder();
            sql.append("select id,unit_name from hq_item_unit where id in (");
            sql.append(duidStr);
            sql.append(")");
            List<JSONObject> list = this.query4Json(microLifeOrder.getBusiness_id(), sql.toString());
            for (JSONObject jsonObject : list) {
                unitNameMap.put(jsonObject.getString("id"), jsonObject.getString("unit_name"));
            }
        }
        return unitNameMap;
    }

    @Override
    public JSONObject getWLifeMember(String tenancyId, String tableCode, int storeId) throws Exception {
        StringBuffer sql = new StringBuffer();
        //查询未结账的订单，同一门店的同一桌台。未结账(OPEN为未结账)的订单只有一个
        sql.append(" select pbm.customer_code as openid,pbm.customer_name as name,pbm.mobil as mobile,pbm.card_code as cno,pbm.remark as grade,pbm.consume_before_credit as credit,pbm.consume_before_main_balance as balance")
                .append(" from pos_bill_member pbm")
                .append(" left join pos_bill pb on pbm.bill_num = pb.bill_num")
                .append(" where pb.store_id = "+ storeId +" and pb.table_code = '"+ tableCode +"' and pb.bill_property='OPEN' ");
        List<JSONObject> list = this.query4Json(tenancyId, sql.toString());
        if (null != list && list.size()>0 )
            return list.get(0);
        return null;
    }

    @Override
    public JSONObject getWLifeMember(String tenancyId, String billNum) throws Exception {
        StringBuffer sql = new StringBuffer();
        //查询未结账的订单，同一门店的同一桌台。未结账(OPEN为未结账)的订单只有一个
        sql.append(" select pbm.customer_code as openid,pbm.customer_name as name,pbm.mobil as mobile,pbm.card_code as cno,pbm.remark as grade,pbm.consume_before_credit as credit,pbm.consume_before_main_balance as balance")
                .append(" from pos_bill_member pbm")
                .append(" left join pos_bill_booked pb on pbm.bill_num = pb.bill_num")
                .append(" where pb.bill_num = '"+ billNum +"' and pb.bill_property='OPEN' ");
        List<JSONObject> list = this.query4Json(tenancyId, sql.toString());
        if (null != list && list.size()>0 )
            return list.get(0);
        return null;
    }

    @Override
    public JSONObject getBillInfo(String tenancyId, String tableCode, int storeId) throws Exception {
        StringBuffer sql = new StringBuffer();
        sql.append(" select pb.bill_num as oid,pb.remark as ordermemo,pb.bill_amount as total,pb.payment_amount as cost,")
                .append(" pb.payment_amount as discountable,pb.payment_amount as membercouponsprice,pb.guest as people,")
                .append(" pb.service_amount as mealfee, case when t.lock_pos_num is null or t.lock_pos_num = '' then '1' else '3' end as is_locked,")
                .append(" pb.order_num as order_id,pb.discount_mode_id,hdc.discount_case_name,pb.discount_rate,pb.discount_amount,pb.discountk_amount,")
                .append(" o.org_full_name as shop_name,o.id,pb.fictitious_table,pb.table_code as tableno")
                .append(" from pos_bill pb left join pos_tablestate t on pb.store_id = t.store_id and pb.fictitious_table = t.table_code")
                .append(" left join hq_discount_case hdc on pb.discount_case_id = hdc.id ")
                .append(" left join organ o on pb.store_id = o.id ")
                .append(" where pb.store_id = "+ storeId +" and pb.fictitious_table = '"+ tableCode +"' and pb.bill_property = 'OPEN'");
        List<JSONObject> list = this.query4Json(tenancyId, sql.toString());
        if (null != list && list.size()>0 )
            return list.get(0);
        return null;
    }

    @Override
    public JSONObject getBillInfo(String tenancyId, String billNum) throws Exception {
        StringBuffer sql = new StringBuffer();
        sql.append(" select pb.bill_num as oid,pb.remark as ordermemo,pb.bill_amount as total,pb.payment_amount as cost,pb.table_code,")
                .append(" pb.payment_amount as discountable,pb.payment_amount as membercouponsprice,pb.guest as people,")
                .append(" pb.service_amount as mealfee, case when t.lock_pos_num is null or t.lock_pos_num = '' then '1' else '3' end as is_locked,")
                .append(" pb.order_num as order_id,pb.discount_mode_id,hdc.discount_case_name,pb.discount_rate,pb.discount_amount,pb.discountk_amount,")
                .append(" o.org_full_name as shop_name,o.id")
                .append(" from pos_bill_booked pb left join pos_tablestate t on pb.store_id = t.store_id ")
                .append(" left join hq_discount_case hdc on pb.discount_case_id = hdc.id ")
                .append(" left join organ o on pb.store_id = o.id ")
                .append(" where pb.bill_num = '"+ billNum +"' and pb.bill_property = 'OPEN'");
        List<JSONObject> list = this.query4Json(tenancyId, sql.toString());
        if (null != list && list.size()>0 )
            return list.get(0);
        return null;
    }

    @Override
    public List<JSONObject> getSetmeal(String tenancyId, String billNum,String itemProperty) throws Exception {
        StringBuffer sql = new StringBuffer();
        sql.append(" select pbi.id,pbi.item_id as did,pbi.item_num as dishsno,pbi.item_name as name,pbi.item_unit_id as duid,item_count as number,")
                .append(" pbi.item_taste as memos,pbi.item_price as price,pbi.item_price as orgprice,case when pbi.discount_mode_id = 6 then pbi.third_price else pbi.item_price end as memberprice,")
                .append(" hii.photo1 as dishimg,'2' as type,case when pbi.item_remark='FS02' then 1 else 0 end as bgift,")
                .append(" hii.photo1 as dishimg,'2' as type,case when pbi.item_remark='FS02' then 1 else 0 end as is_gift,")
                .append(" case when hii.is_modifyquantity = 'Y' then 1 else 0 end as isWeigh,case when pbi.discount_mode_id = 6 then 1 else 0 end as bmemberprice,0 as bargainprice,")
                .append(" pbi.method_money+pbi.assist_money as aprice,hii.item_class as pkid,pbi.item_taste as remark,pbi.real_amount as realprice,pbi.setmeal_rwid ")
                .append(" from pos_bill_item_booked pbi")
                .append(" left join hq_item_info hii on pbi.item_id = hii.id")
                .append(" where pbi.item_property = '"+ itemProperty +"' and pbi.bill_num = '"+ billNum +"' and (pbi.item_remark is null or (pbi.item_remark  <> 'TC01' and pbi.item_remark  <> 'CJ05')) ");
        List<JSONObject> list = this.query4Json(tenancyId, sql.toString());
        return list;
    }

    @Override
    public List<JSONObject> getSetmealDish(String tenancyId, String billNum, String type, int stemealCount, String setmealId, String setmeal_rwid) throws Exception {
        String is_itemgroup = "";
        //明细
        String itemProperty = SysDictionary.ITEM_PROPERTY_MEALLIST;
        String column = "";
        String column2 = "";
        //如果是套餐主菜菜品的查询
        if ("main".equals(type)){
            is_itemgroup = "N";
        }else {
            //套餐必选菜
            is_itemgroup = "Y";
            column = "round((pbi.method_money + pbi.assist_money)/"+ stemealCount +",2) AS aprice,";
            column2 = "pbi.assist_item_id AS mid,cd.details_id as rpdid,";
        }
        StringBuffer sql = new StringBuffer();
        sql.append(" select pbi.item_id as id,pbi.item_num as dishsno,pbi.item_name as name,pbi.item_unit_id as duid,")
                .append(column).append(column2)
                .append("pbi.item_count/"+ stemealCount +" as number")
                .append(" from pos_bill_item_booked pbi ")
                .append(" left join pos_bill_item_booked bi on pbi.setmeal_id = bi.item_id and pbi.setmeal_rwid = bi.item_serial")
                .append(" left join hq_item_combo_details cd on pbi.assist_item_id=cd.id")
                .append(" where pbi.item_property = '"+ itemProperty +"' and cd.is_itemgroup = '"+ is_itemgroup +"' and pbi.bill_num = '"+ billNum +"' and (pbi.item_remark is null or (pbi.item_remark  <> 'TC01' and pbi.item_remark  <> 'CJ05'))")
                .append(" and cd.iitem_id = '"+ setmealId +"' and bi.bill_num = '"+ billNum +"' and pbi.setmeal_rwid = '" + setmeal_rwid +"'");
        List<JSONObject> list = this.query4Json(tenancyId, sql.toString());
        return list;
    }



    @Override
    public List<JSONObject> getCooks(String tenancyId, int id) throws Exception {
        StringBuffer sql = new StringBuffer();
        sql.append(" select pzi.amount aprice,pzi.zfkw_name cook,pzi.zfkw_id cookid,pzi.zfkw_name cookname,pzi.zfkw_id id,pzi.item_id,pzi.zfkw_name as \"name\" from pos_bill_item_booked pbi")
                .append(" right join pos_zfkw_item_booked pzi on pbi.item_id = pzi.item_id and pbi.store_id = pzi.store_id")
                .append(" and pbi.bill_num = pzi.bill_num and pbi.rwid = pzi.rwid")
                .append(" where pbi.id = " + id );
        List<JSONObject> list = this.query4Json(tenancyId, sql.toString());
        return list;
    }



    @Override
    public JSONObject getBillOrder(String tenancyId, String tableCode, int storeId) throws Exception {
        StringBuffer sql = new StringBuffer();
        sql.append(" select pb.bill_num as oid,pb.remark as text,pb.bill_amount as total,pb.payment_amount as cost,pb.payment_amount as discountable,pb.payment_amount as membercouponsprice,pb.guest as people,pb.service_amount as mealfee, ")
                .append(" case when t.lock_pos_num is null or t.lock_pos_num = '' then '1' else '3' end as is_locked,pb.order_num as out_order_id,")
                .append(" pb.discount_mode_id,hdc.discount_case_name,pb.discount_rate,pb.discount_amount,o.org_full_name as shop_name")
                .append(" from pos_bill pb left join pos_tablestate t on pb.store_id = t.store_id and pb.fictitious_table = t.table_code")
                .append(" left join hq_discount_case hdc on pb.discount_case_id = hdc.id ")
                .append(" left join organ o on pb.store_id = o.id ")
                .append(" where pb.store_id = "+ storeId +" and pb.fictitious_table = '"+ tableCode +"' and pb.bill_property = '"+ SysDictionary.BILL_PROPERTY_CLOSED +"' order by pb.id desc limit 1");
        List<JSONObject> list = this.query4Json(tenancyId, sql.toString());
        if (null != list && list.size()>0 )
            return list.get(0);
        return null;
    }

    @Override
    public JSONObject getCookMethodObj(Integer methodId) throws Exception {

        StringBuilder sb = new StringBuilder();
        sb.append(" select hm.item_id,sd.class_item as method_name from  public.hq_item_method hm")
                .append(" left join public.sys_dictionary sd on hm.method_name_id=sd.id ")
                .append(" and sd.class_identifier_code='method' ")
                .append(" WHERE hm.id =").append(methodId);
        List<JSONObject> list = this.query4Json(null, sb.toString());
        if(null != list && list.size() > 0){
            return list.get(0);
        }
        return null;
    }

    @Override
    public String getBillNumByOrderId(String orderId) throws Exception {
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT bill_num as oid FROM pos_bill  WHERE order_num=")
                .append(" '").append(orderId).append("'");
        List<JSONObject> list = this.query4Json(null, sb.toString());
        if(null!=list && list.size()==1){
            return list.get(0).optString("oid");
        }
        return null;
    }

    @Override
    public void updatePosBillSource(String billNum,String sourceVal,boolean isOrdering) throws Exception {
        String sql = "";
        if(isOrdering){
            sql = " UPDATE pos_bill SET order_source='"+sourceVal+"' WHERE bill_num='" + billNum + "'";
        }else {
            sql = " UPDATE pos_bill SET payment_source='"+sourceVal+"' WHERE bill_num='" + billNum + "'";
        }
        this.execute(null, sql);
    }

    @Override
    public void updatePosBillOrderNum(String orderNum,String billNum) throws Exception {
        String sql = " UPDATE pos_bill SET order_num='"+orderNum+"',order_source='"+ SysDictionary.CHANEL_WSP17 +"' WHERE bill_num='" + billNum + "'";
        this.execute(null, sql);
    }

    @Override
    public JSONObject getTableStatus(String tenantId, Integer storeId, String tableNo) throws Exception {
        if(!StringUtils.isEmpty(tableNo)&&storeId!=null) {
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append("select id,state,lock_opt_num,lock_pos_num,opt_name from pos_tablestate  where table_code = '");
            stringBuilder.append(tableNo);
            stringBuilder.append("' and store_id = ");
            stringBuilder.append(storeId);
            stringBuilder.append(" order by last_updatetime desc limit 1");
            List<JSONObject> list = this.query4Json(tenantId, stringBuilder.toString());
            if(list.size()>0) {
                JSONObject jsonObject = list.get(0);
                return jsonObject;
            }
        }
        return null;
    }

    @Override
    public void lockTableStatus(Integer storeId, String tableNo, String lockOptNum, String lockPosNum, String optName) throws Exception{
        StringBuilder sql = new StringBuilder();
        sql.append(" UPDATE pos_tablestate SET state='BUSY',")
                .append(" lock_pos_num='WXWlife',")
                .append(" lock_opt_num= ?,")
                .append(" opt_name= ?")
                .append(" WHERE table_code= ?")
                .append(" and store_id= ?");
        List<Object> params = new ArrayList<>();

        params.add(lockOptNum);
        params.add(optName);
        params.add(tableNo);
        params.add(storeId);
        this.update(sql.toString(), params.toArray());
    }

    @Override
    public JSONObject getBillMemberInfo(String billNum, String type, String cardCode) throws Exception{
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT * FROM pos_bill_member")
                .append("  WHERE bill_num=? ")
                .append(" and type =?")
                .append(" and card_code=?")
                .append(" ORDER BY id LIMIT 1");
        List<Object> params = new ArrayList<>();
        params.add(billNum);
        params.add(type);
        params.add(cardCode);
        List<JSONObject> list = this.query4Json(null,sb.toString(), params.toArray());
        if(null != list && list.size() > 0){
            return list.get(0);
        }
        return null;
    }

    @Override
    public int selectComboMaxItemSerial(String billNum, int storeId, String tenancyId)
            throws Exception {
        StringBuilder maxSerialSql = new StringBuilder(
                "select coalesce(max(combo_prop), 0) from pos_bill_item i where i.bill_num = ? and i.store_id = ? and i.tenancy_id = ?");

        return this.queryForInt(maxSerialSql.toString(), new Object[]{billNum, storeId, tenancyId});
    }

    @Override
    public void savePosBillBooked(String tenancyId, int storeId, Date reportDate, int shiftId, String billNum, String batchNum, String serialNum, String orderNum, Timestamp opentableTime,
                                  String openPosNum, String openOpt, String waiterNum, int guest, int itemMenuId, String saleMode, String source, int serviceId, double serviceAmount,
                                  String copyBillNum, String remark, String billProperty, String paymentState, int discountModeId,
                                  double discountkAmount, double shopRealAmount, double platformChargeAmount, String settlementType, double discountRate,String tableCode) throws Exception {
        double total_fees=0d;
        StringBuffer saveBill = new StringBuffer(
                "insert into pos_bill_booked (tenancy_id,store_id,report_date,shift_id,bill_num,batch_num,serial_num,order_num,opentable_time,open_pos_num,open_opt,waiter_num,guest,item_menu_id,sale_mode,source,service_id,service_amount,copy_bill_num,remark,bill_property,payment_state,discount_mode_id,discountk_amount,shop_real_amount,platform_charge_amount,total_fees,settlement_type,discount_rate,table_code) ");
        saveBill.append(" values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");

        this.update(saveBill.toString(), new Object[]
                { tenancyId,storeId,reportDate,shiftId,billNum,batchNum,serialNum,orderNum,opentableTime,openPosNum,openOpt,waiterNum,guest,itemMenuId,saleMode,source,serviceId,serviceAmount,copyBillNum,remark,billProperty,paymentState,discountModeId,discountkAmount,shopRealAmount,platformChargeAmount,total_fees,settlementType,discountRate,tableCode});
    }

    @Override
    public void savePosBill(String tenancyId, int storeId, Date reportDate, int shiftId, String billNum, String batchNum, String serialNum, String orderNum, Timestamp opentableTime,
                                  String openPosNum, String openOpt, String waiterNum, int guest, int itemMenuId, String saleMode, String source, int serviceId, double serviceAmount,
                                  String copyBillNum, String remark, String billProperty, String paymentState, int discountModeId,
                                  double discountkAmount, double shopRealAmount, double platformChargeAmount, String settlementType, double discountRate,String tableCode) throws Exception {
        double total_fees=0d;
        StringBuffer saveBill = new StringBuffer(
                "insert into pos_bill (tenancy_id,store_id,report_date,shift_id,bill_num,batch_num,serial_num,order_num,opentable_time,open_pos_num,open_opt,waiter_num,guest,item_menu_id,sale_mode,source,service_id,service_amount,copy_bill_num,remark,bill_property,payment_state,discount_mode_id,discountk_amount,shop_real_amount,platform_charge_amount,total_fees,settlement_type,discount_rate,table_code) ");
        saveBill.append(" values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");

        this.update(saveBill.toString(), new Object[]
                { tenancyId,storeId,reportDate,shiftId,billNum,batchNum,serialNum,orderNum,opentableTime,openPosNum,openOpt,waiterNum,guest,itemMenuId,saleMode,source,serviceId,serviceAmount,copyBillNum,remark,billProperty,paymentState,discountModeId,discountkAmount,shopRealAmount,platformChargeAmount,total_fees,settlementType,discountRate,tableCode});
    }


    @Override
    public List<JSONObject> getRwidsWithoutWaitCallItem(String tenancyId, int storeId, String billNum, String orderRemark) throws Exception {
        StringBuilder sql = new StringBuilder("select pbi.rwid,pbi.order_number,pbi.waitcall_tag from pos_bill_item_booked pbi where pbi.bill_num=? and order_remark=? order by rwid");
        return this.query4Json(tenancyId, sql.toString(), new Object[]{ billNum, orderRemark });
    }

    @Override
    public JSONObject getBillDiscount(String tenancyId, int storeId, String billNum) throws Exception {
        StringBuffer sql = new StringBuffer();
        sql.append(" select COALESCE(pb.discount_mode_id,0) as discount_mode_id,pb.report_date,COALESCE(pb.shift_id,0) as shift_id,COALESCE(pb.pos_num,'') as pos_num,pb.bill_num,COALESCE(pb.discount_case_id,0) as discount_id,pb.bill_num,pb.order_num,source,payment_state,payment_amount,discountr_amount,")
                .append(" pb.discount_rate,COALESCE(pb.discount_reason_id,0) as discount_reason_id,pbm.card_code,COALESCE(pbm.mobil,'') as mobil,COALESCE(pbm.customer_code,'') as customer_code,COALESCE(pbm.customer_name,'') as customer_name,COALESCE(pbm.consume_before_credit,0) as customer_credit")
                .append(" from pos_bill_booked pb")
                .append(" left join pos_bill_member pbm on pb.bill_num = pbm.bill_num")
                .append(" where pb.store_id = "+ storeId +" and pb.bill_num='"+ billNum +"'and pb.bill_property = 'OPEN'");
        List<JSONObject> list = this.query4Json(tenancyId, sql.toString());
        if (null != list && list.size() > 0)
            return list.get(0);
        return null;
    }

    @Override
    public JSONObject getBillDiscountForPay(String tenancyId, int storeId, String billNum) throws Exception {
        StringBuffer sql = new StringBuffer();
        sql.append(" select COALESCE(pb.discount_mode_id,0) as discount_mode_id,pb.report_date,COALESCE(pb.shift_id,0) as shift_id,COALESCE(pb.pos_num,'') as pos_num,pb.bill_num,COALESCE(pb.discount_case_id,0) as discount_id,pb.bill_num,pb.order_num,source,payment_state,payment_amount,discountr_amount,")
                .append(" pb.discount_rate,COALESCE(pb.discount_reason_id,0) as discount_reason_id,pbm.card_code,COALESCE(pbm.mobil,'') as mobil,COALESCE(pbm.customer_code,'') as customer_code,COALESCE(pbm.customer_name,'') as customer_name,COALESCE(pbm.consume_before_credit,0) as customer_credit")
                .append(" from pos_bill pb")
                .append(" left join pos_bill_member pbm on pb.bill_num = pbm.bill_num")
                .append(" where pb.store_id = "+ storeId +" and pb.bill_num='"+ billNum +"'and pb.bill_property = 'OPEN'");
        List<JSONObject> list = this.query4Json(tenancyId, sql.toString());
        if (null != list && list.size() > 0)
            return list.get(0);
        return null;
    }

    @Override
    public List<JSONObject> getPosBillItemByBillNum(String tenantId, int store_id, String oldBillNum) throws Exception {
        String sql =" select * from pos_bill_item_booked where tenancy_id=? and store_id=? and bill_num=? order by item_serial,rwid";
        return this.query4Json(tenantId, sql, new Object[]{tenantId,store_id,oldBillNum});
    }

    @Override
    public List<JSONObject> getPosBillItemForDiscountCaseByBillnum(String tenantId, Integer storeId, String billNum, Integer discountCaseId) throws Exception {
        StringBuilder rateStr = new StringBuilder();
        rateStr.append(" select bi.id,bi.item_id,bi.setmeal_id,bi.item_serial,bi.setmeal_rwid,bi.item_property,bi.discount_state,bi.item_remark,bi.item_amount,bi.item_price,bi.assist_money,bi.method_money,bi.item_count,bi.discount_mode_id,bi.discount_rate,bi.discount_amount,bi.real_amount,bi.single_amount,bi.item_amount,bi.combo_prop,bi.discount_num,bi.discount_reason_id,coalesce(bi.third_price,0) as third_price,coalesce(cd.rate,0) as rate,coalesce(cd.derate,0) as derate from pos_bill_item_booked bi");
        rateStr.append(" left join hq_discount_case_details cd on bi.item_id=cd.item_id and bi.item_unit_name=cd.unit and cd.discount_case_id =?");
        rateStr.append(" where bi.tenancy_id=? and bi.store_id=? and bi.bill_num=?");

        return this.query4Json(tenantId, rateStr.toString(), new Object[]
                { discountCaseId, tenantId, storeId, billNum });
    }

    /**
     * 根据小程序out_order_id 与pos_bill 中order_num 查询账单号---小程序先付跳单问题
     * @param tenantId
     * @param storeId
     * @param outOrderId
     * @return
     */
    public JSONObject getBillNumByOutOrderId(String tenantId, Integer storeId, String outOrderId) throws Exception{
        StringBuilder querySql = new StringBuilder();
        JSONObject  jsonObject = null;
        querySql.append(" select bill_num,serial_num from pos_bill_booked where tenancy_id=? and store_id=? and order_num=?");
        List<JSONObject> list =  this.query4Json(tenantId, querySql.toString(), new Object[]{ tenantId, storeId, outOrderId });
        if(list!=null && list.size()>0){
            jsonObject = list.get(0);
        }
        return jsonObject;
    }


    /**
     * 根据桌位号查询服务id
     * @param tenantId
     * @param storeId
     * @param tableCode
     * @return
     * @throws Exception
     */
    public Integer getServiceIdByTablCode(String tenantId, int storeId, String  tableCode) throws Exception{
        Integer serviceId = 0;
        StringBuffer sqlState = new StringBuffer("select ti.table_code,ti.fwfz_id,pt.state from tables_info ti left join pos_tablestate pt on ti.tenancy_id=pt.tenancy_id and ti.organ_id=pt.store_id and ti.table_code=pt.table_code where ti.tenancy_id=? and ti.organ_id=? and ti.table_code=?");
        List<JSONObject> list = this.query4Json(tenantId,sqlState.toString(),new Object[]{tenantId,storeId,tableCode});
        if(list!=null && list.size()>0){
            JSONObject jsonObject = list.get(0);
            serviceId = jsonObject.optInt("fwfz_id");
        }
        return serviceId;
    }
    
    @Override
	public List<JSONObject> getItemComboDetails(String tenantId, List<Integer> itemIdList) throws Exception
	{
		String itemIds = StringUtils.collectionToDelimitedString(itemIdList, ",");
		if (!StringUtils.isEmpty(itemIds))
		{
			StringBuilder sql = new StringBuilder();
			sql.append(" select * from hq_item_combo_details where iitem_id in (" + itemIds + ")");
			return this.query4Json(tenantId, sql.toString());
		}
		return null;
	}
    
    @Override
	public JSONObject getPosBillByBillnum(String tenancyId, int storeId, String billNum) throws Exception
	{
		// TODO Auto-generated method stub
		JSONObject resultJson = new JSONObject();
		StringBuilder billAmountSql = new StringBuilder();
		billAmountSql.append(" select b.fictitious_table, b.id,b.waiter_num,coalesce(b.bill_amount,0) as bill_amount,round(coalesce(b.payment_amount,0),2) as payment_amount,coalesce(b.discount_rate,100) as discount_rate,coalesce(b.discount_case_id,0) as discount_case_id,coalesce(b.discount_amount,0) as discount_amount,b.source,b.bill_property");
		billAmountSql.append(" ,coalesce(b.table_code,'') as table_code,coalesce(b.difference,0)  as difference,sale_mode,coalesce(b.order_num,'') as order_num,b.copy_bill_num,b.payment_state,b.batch_num,b.discount_mode_id,b.remark,coalesce(b.recover_count,0) as recover_count,bill_num,payment_time,b.bill_state");
		billAmountSql.append(" ,b.discountk_amount,b.discountr_amount,b.givi_amount,b.maling_amount,b.subtotal,b.print_count,b.transfer_remark,b.pay_no,b.report_date,b.guest,b.discount_reason_id,b.discount_num,b.pos_num,b.cashier_num,b.shift_id ,b.sbill_num ");
		billAmountSql.append(" from v_pos_bill b where b.bill_num = ? and b.store_id = ? and b.tenancy_id = ?");

		List<JSONObject> list = this.query4Json(tenancyId, billAmountSql.toString(), new Object[]
		{ billNum, storeId, tenancyId });

		if (list != null && list.size() > 0)
		{
			resultJson = list.get(0);
		}
		return resultJson;
	}
}
