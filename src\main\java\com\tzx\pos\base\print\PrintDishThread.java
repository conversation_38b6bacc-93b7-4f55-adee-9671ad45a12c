package com.tzx.pos.base.print;

import net.sf.json.JSONObject;

import org.apache.log4j.Logger;

import com.tzx.base.bo.PrintFormatService;
import com.tzx.base.bo.imp.PrintFormatServiceImpl;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;

/**
 *
 * <AUTHOR>
 * 2015年7月27日-下午3:43:50
 */
public class PrintDishThread implements Runnable
{
	private static final Logger logger = Logger.getLogger(PrintDishThread.class);
	
	private JSONObject dish;
	
	public JSONObject getDish()
	{
		return dish;
	}

	public void setDish(JSONObject dish)
	{
		this.dish = dish;
	}

	public PrintDishThread()
	{
		super();
	}
	
	public PrintDishThread(JSONObject json)
	{
		this.dish = json;
	}
	
	@Override
	public void run()
	{
		try
		{
			String tenantId = dish.getString("tenancy_id");
			DBContextHolder.setTenancyid(tenantId);
			logger.info("============================ " + dish.toString());
			
//			Thread.sleep(1000);
			PrintFormatServiceImpl printFormatService = (PrintFormatServiceImpl) SpringConext.getApplicationContext().getBean(PrintFormatService.NAME);
			printFormatService.newPrint(tenantId, dish);
		}
		catch (Exception e)
		{
			logger.info("调用厨打线程失败：" + ExceptionMessage.getExceptionMessage(e));
			e.printStackTrace();
		}
	}
}
