package com.tzx.pos.service.servlet.imp;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.Tools;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.base.util.ReqDataUtil;
import com.tzx.pos.bo.PosBaseService;
import com.tzx.pos.bo.PosDiscountService;
import com.tzx.pos.bo.PosDishService;
import com.tzx.pos.bo.PosOpenTableService;
import com.tzx.pos.po.springjdbc.dao.PosBillMemberDao;
import com.tzx.pos.po.springjdbc.dao.PosDiscountDao;
import com.tzx.pos.po.springjdbc.dao.PosDishDao;
import com.tzx.pos.service.servlet.PosDishServlet;

@Service(PosDishServlet.NAME)
public class PosDishServletImp implements PosDishServlet
{
	private static final Logger	logger	= Logger.getLogger(PosDishServlet.class);

	@Resource(name = PosOpenTableService.NAME)
	private PosOpenTableService	openTableService;
	
	@Resource(name = PosBillMemberDao.NAME)
	private PosBillMemberDao	memberDao;
	
	@Resource(name = PosDiscountDao.NAME)
    private PosDiscountDao			posDiscountDao;//
	
	@Resource(name = PosDishDao.NAME)
	private PosDishDao			posDishDao;//
	
	@Resource(name = PosBaseService.NAME)
	private PosBaseService posBaseService;
	
	@Resource(name = PosDishService.NAME)
	private PosDishService		posDishService;//
	
	@Resource(name = PosDiscountService.NAME)
	private PosDiscountService posDiscountService;//

	@Override
	public synchronized List<JSONObject> newestOpenTable(Data param, JSONObject printJson) throws Exception {
		String tenantId = param.getTenancy_id();
		Integer storeId = param.getStore_id();
		String source = param.getSource();
		List<?> paramList = param.getData();
		if (null == paramList || paramList.isEmpty()) {
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}
		JSONObject paramJson = JSONObject.fromObject(paramList.get(0));
		String mode = ParamUtil.getStringValueByObject(paramJson, "mode", true, PosErrorCode.NOT_NULL_MODE);
		Integer shiftId = ParamUtil.getIntegerValueByObject(paramJson, "shift_id", true,
				PosErrorCode.NOT_NULL_SHIFT_ID);
		Date reportDate = ParamUtil.getDateValueByObject(paramJson, "report_date", true,
				PosErrorCode.NOT_NULL_REPORT_DATE);
		String optNum = ParamUtil.getStringValueByObject(paramJson, "opt_num", true, PosErrorCode.NOT_NULL_OPT_NUM);
		String posNum = ParamUtil.getStringValueByObject(paramJson, "pos_num", true, PosErrorCode.NOT_EXISTS_POSNUM);
		String tableCode = ParamUtil.getStringValueByObject(paramJson, "table_code", false, null);
		String chanel = ParamUtil.getStringValueByObject(paramJson, "chanel", false, null);
		if (Tools.isNullOrEmpty(chanel)) {
			chanel = SysDictionary.CHANEL_MD01;
		}
		List<JSONObject> billList = null;
		// 报表日期校验
		openTableService.checkReportDate(tenantId, storeId, reportDate);
		// 班次ID校验
		shiftId = openTableService.checkShiftId(tenantId, storeId, reportDate, shiftId, optNum, posNum, source);
		// 生成账单
		String billNum = null;
		try {
			billNum = openTableService.openTableByTableCode(tenantId, storeId, reportDate, shiftId, paramJson);
			// 添加默认菜品
			if (SysDictionary.CHANEL_MD01.equals(chanel)) {
				try {
					// add by shenzy 判断是否设置自动增加菜品区域 并且 该桌台是否在设置范围
					boolean result = openTableService.getHqItemTables(tenantId, storeId, tableCode);
					if (result) {
						Data dishData = openTableService.getDefaultDish(param, billNum, printJson);
						if ("0".equals(mode)) {
							// 厨打
							printJson.put("tenancy_id", tenantId);
							printJson.put("store_id", storeId);
							printJson.put("mode", mode);
							printJson.put("bill_num", billNum);
							printJson.put("is_print", "Y");
						}
					}
				} catch (Exception e1) {
					logger.info("开台添加默认菜品失败：" + ExceptionMessage.getExceptionMessage(e1));
				}
			}

			// 获取账单数据
			if (null == billList || 0 >= billList.size()) {
				billList = openTableService.getPosBillByBillNum(tenantId, storeId, billNum, tableCode);
			}
			if ("0".equals(mode)) {
				try {
					openTableService.updateDevicesDataState(tenantId, storeId);
				} catch (Exception e) {
					e.printStackTrace();
					logger.error("updateDevicesDataState,修改数据状态失败", e);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			throw e;
		}
		return billList;
	}

	@Override
	public synchronized void optableAndCustomerCreditPlus(Data param, Data result, List<JSONObject> listJson) {
		
		String tenantId = param.getTenancy_id();
		Integer organId = param.getStore_id();
		
		JSONObject jo = listJson.get(0);
		String billNum = jo.getString("bill_num");
		List<Map<String,Object>> listMapFrom = (List<Map<String, Object>>) ReqDataUtil.getDataList(param);
		Map<String,Object> map = listMapFrom.get(0);
		String modeDis = (String) map.get("mode_discount");
		
		map.put("bill_num", billNum);
		map.put("mode",modeDis);
		
		List<Map<String,Object>> listMapTo = new ArrayList<Map<String,Object>>();
		listMapTo.add(map);
		
		param.setData(listMapTo);
		
		try {
			posDishService.customerCreditPlus(param, result);//绑定会员
		} catch (Exception e) {
			// TODO: handle exception
			logger.info("开台并绑定会员绑定会员异常");
			e.printStackTrace();
		}
		
		try {
			result = posDiscountService.newBillDiscount(param);//折扣
			// 数据上传
			posDishService.upload(tenantId,organId+"","","","",billNum);
		} catch (Exception e) {
			// TODO: handle exception
			logger.info("开台并绑定会员折扣异常");
			e.printStackTrace();
		}
		
	}

	public boolean checkCombineTableOpenTable(Data param)throws Exception{
		boolean  result = false;
		String tenantId = param.getTenancy_id();
		Integer storeId = param.getStore_id();
		List<?> paramList = param.getData();
		if (null == paramList || paramList.isEmpty()) {
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}
		JSONObject paramJson = JSONObject.fromObject(paramList.get(0));
		String copyBillNum = ParamUtil.getStringValueByObject(paramJson, "copy_bill_num", false, null);
		String table_code = ParamUtil.getStringValueByObject(paramJson, "table_code", false, null);
		// 是否拆台
		String splitFlag = ParamUtil.getStringValueByObject(paramJson, "split_flag", false, null);
		if (splitFlag != null && "Y".equals(splitFlag))
		{
			JSONObject tableJson = posDishDao.getOpenBillByTablecode(tenantId, storeId, table_code);
			if (null != tableJson && !tableJson.isEmpty()) {
				String bill_num =  ParamUtil.getStringValueByObject(tableJson, "bill_num");
				JSONObject billJson = posDishDao.getPosBillByBillnum(tenantId, storeId,bill_num);
				if(null!= billJson && !billJson.isEmpty()){
					String sbill_num = billJson.optString("sbill_num");
					if(Tools.hv(sbill_num)){
						//throw SystemException.getInstance(PosErrorCode.COMBINE_TABLE_PAYMENT_DISCOUNT_EXISTS);
						result = true;
					}else{
						JSONObject rbillJson = posDishDao.getPosBillBySBillnum(tenantId, storeId,bill_num);
						if(null!=rbillJson && !rbillJson.isEmpty()){
							//throw SystemException.getInstance(PosErrorCode.COMBINE_TABLE_PAYMENT_DISCOUNT_EXISTS);
							result = true;
						}
					}
				}
			}
		}
		return result;
	}

}
