package com.tzx.base.entity.cache;

public class DutyOrderCache
{
	public static String getSql()
	{
		String sql = new String("select dy.tenancy_id,dyo.organ_id as store_id,dy.id as duty_id,dy.name as duty_class,sd.class_item as duty_name,dy.start_time,dy.end_time,dy.valid_state,dy.start_property,dy.end_property,dy.code,dy.third_code from duty_order dy left join duty_order_of_ogran dyo on dy.tenancy_id=dyo.tenancy_id and dy.id=dyo.duty_order_id left join sys_dictionary sd on dy.name=sd.class_item_code and sd.class_identifier_code='duty' where dyo.tenancy_id=::tenancy_id and dyo.organ_id=::store_id");
		return sql;
	}
	
	public static String getKey()
	{
		return "duty_id";
	}

}
