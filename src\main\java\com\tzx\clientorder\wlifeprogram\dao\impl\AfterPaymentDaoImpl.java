package com.tzx.clientorder.wlifeprogram.dao.impl;

import com.tzx.clientorder.wlifeprogram.dao.AfterPaymentDao;
import com.tzx.pos.base.constant.SysDictionary;

import net.sf.json.JSONObject;

import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 微生活后付持久层
 */
@Repository(AfterPaymentDao.NAME)
public class AfterPaymentDaoImpl extends WlifeCommonDaoImpl implements AfterPaymentDao {


    @Override
    public String getTableState(String tenancyId, int storeId, String tableCode) throws Exception {
        String sql = "select s.state from pos_tablestate s where s.tenancy_id = ? and s.store_id = ? and s.table_code = ? ";

        SqlRowSet rs = this.jdbcTemplate.queryForRowSet(sql, new Object[]{tenancyId, storeId, tableCode});
        if(rs.next()){
            return rs.getString("state");
        }
        return "";
    }

    @Override
    public List<JSONObject> getBillInfoByOrderNum(String tenancyId, int storeId, String orderNum) throws Exception {
        String sql = "select b.* from pos_bill b where b.order_num = ? and b.tenancy_id = ? and b.store_id = ? ";
        return this.query4Json(tenancyId, sql, new Object[]{orderNum, tenancyId, storeId});
    }

    @Override
	public List<JSONObject> getUnitNameList(String tenancyId, int storeId, List<String> duids) throws Exception
	{
		String duidStr = StringUtils.collectionToDelimitedString(duids, ",");
		if (!StringUtils.isEmpty(duidStr))
		{
			StringBuilder sql = new StringBuilder();
			sql.append("select id,unit_name from hq_item_unit where id in (");
			sql.append(duidStr);
			sql.append(")");
			return this.query4Json(tenancyId, sql.toString());
		}
		return null;
	}

    @Override
    public JSONObject getWLifeMember(String tenancyId, String tableCode, int storeId) throws Exception {
        StringBuffer sql = new StringBuffer();
        //查询未结账的订单，同一门店的同一桌台。未结账(OPEN为未结账)的订单只有一个
        sql.append(" select pbm.customer_code as openid,pbm.customer_name as name,pbm.mobil as mobile,pbm.card_code as cno,pbm.remark as grade,pbm.consume_before_credit as credit,pbm.consume_before_main_balance as balance")
                .append(" from pos_bill_member pbm")
                .append(" left join pos_bill pb on pbm.bill_num = pb.bill_num")
                .append(" where pb.store_id = "+ storeId +" and pb.table_code = '"+ tableCode +"' and pb.bill_property='OPEN' ");
        List<JSONObject> list = this.query4Json(tenancyId, sql.toString());
        if (null != list && list.size()>0 )
            return list.get(0);
        return null;
    }

    @Override
    public JSONObject getBillInfo(String tenancyId, String tableCode, int storeId) throws Exception {
        StringBuffer sql = new StringBuffer();
        sql.append(" select pb.bill_num as oid,pb.remark as ordermemo,pb.bill_amount as total,pb.payment_amount as cost,")
                .append(" pb.payment_amount as discountable,pb.payment_amount as membercouponsprice,pb.guest as people,")
                .append(" pb.service_amount as mealfee, case when t.lock_pos_num is null or t.lock_pos_num = '' then '1' else '3' end as is_locked,")
                .append(" pb.order_num as order_id,pb.discount_mode_id,hdc.discount_case_name,pb.discount_rate,pb.discount_amount,pb.discountk_amount,")
                .append(" o.org_full_name as shop_name,o.id,pb.fictitious_table,pb.table_code as tableno")
                .append(" from pos_bill pb left join pos_tablestate t on pb.store_id = t.store_id and pb.fictitious_table = t.table_code")
                .append(" left join hq_discount_case hdc on pb.discount_case_id = hdc.id ")
                .append(" left join organ o on pb.store_id = o.id ")
                .append(" where pb.store_id = "+ storeId +" and pb.fictitious_table = '"+ tableCode +"' and pb.bill_property = 'OPEN'");
        List<JSONObject> list = this.query4Json(tenancyId, sql.toString());
        if (null != list && list.size()>0 )
            return list.get(0);
        return null;
    }

    @Override
    public List<JSONObject> getSetmeal(String tenancyId, String billNum,String itemProperty) throws Exception {
        StringBuffer sql = new StringBuffer();
        sql.append(" select pbi.id,pbi.item_id as did,pbi.item_num as dishsno,pbi.item_name as name,pbi.item_unit_id as duid,item_count as number,")
                .append(" pbi.item_taste as memos,pbi.item_price as price,pbi.item_price as orgprice,case when pbi.discount_mode_id = 6 then pbi.third_price else pbi.item_price end as memberprice,")
                .append(" hii.photo1 as dishimg,'2' as type,case when pbi.item_remark='FS02' then 1 else 0 end as bgift,")
                .append(" hii.photo1 as dishimg,'2' as type,case when pbi.item_remark='FS02' then 1 else 0 end as is_gift,")
                .append(" case when hii.is_modifyquantity = 'Y' then 1 else 0 end as isWeigh,case when pbi.discount_mode_id = 6 then 1 else 0 end as bmemberprice,0 as bargainprice,")
                .append(" pbi.method_money+pbi.assist_money as aprice,hii.item_class as pkid,pbi.item_taste as remark,pbi.real_amount as realprice, pbi.setmeal_rwid ")
                .append(" from pos_bill_item pbi")
                .append(" left join hq_item_info hii on pbi.item_id = hii.id")
                .append(" where pbi.item_property = '"+ itemProperty +"' and pbi.bill_num = '"+ billNum +"' and (pbi.item_remark is null or (pbi.item_remark  <> 'TC01' and pbi.item_remark  <> 'CJ05')) ");
        List<JSONObject> list = this.query4Json(tenancyId, sql.toString());
        return list;
    }

    @Override
    public List<JSONObject> getSetmealDish(String tenancyId, String billNum, String type, int stemealCount, String setmealId, String setmeal_rwid) throws Exception {
        String is_itemgroup = "";
        //明细
        String itemProperty = SysDictionary.ITEM_PROPERTY_MEALLIST;
        String column = "";
        //如果是套餐主菜菜品的查询
        if ("main".equals(type)){
            is_itemgroup = "N";
        }else {
            //套餐必选菜
            is_itemgroup = "Y";
            column = "(pbi.method_money+pbi.assist_money)/"+ stemealCount +" as aprice,null as mid,";
        }
        StringBuffer sql = new StringBuffer();
        sql.append(" select bi.bill_num,pbi.item_id as did,pbi.item_num as dishsno,pbi.item_name as name,pbi.item_unit_id as duid,")
                .append(column)
                .append("pbi.item_count/"+ stemealCount +" as number")
                .append(" from pos_bill_item pbi ")
                .append(" left join pos_bill_item bi on pbi.setmeal_id = bi.item_id and pbi.setmeal_rwid = bi.item_serial")
                .append(" left join hq_item_combo_details cd on pbi.assist_item_id=cd.id")
                .append(" where pbi.item_property = '"+ itemProperty +"' and cd.is_itemgroup = '"+ is_itemgroup +"' and pbi.bill_num = '"+ billNum +"' and (pbi.item_remark is null or (pbi.item_remark  <> 'TC01' and pbi.item_remark  <> 'CJ05'))")
                .append(" and cd.iitem_id = '"+ setmealId +"' and bi.bill_num = '"+ billNum +"' and pbi.setmeal_rwid = '" + setmeal_rwid +"'");
        List<JSONObject> list = this.query4Json(tenancyId, sql.toString());
        return list;
    }



    @Override
    public List<JSONObject> getCooks(String tenancyId, int id) throws Exception {
        StringBuffer sql = new StringBuffer();
        sql.append(" select pzi.zfkw_id as cid,pzi.zfkw_name as cook from pos_bill_item pbi")
                .append(" right join pos_zfkw_item pzi on pbi.item_id = pzi.item_id and pbi.store_id = pzi.store_id")
                .append(" and pbi.bill_num = pzi.bill_num and pbi.rwid = pzi.rwid")
                .append(" where pbi.id = " + id );
        List<JSONObject> list = this.query4Json(tenancyId, sql.toString());
        return list;
    }



    @Override
    public JSONObject getBillOrder(String tenancyId, String tableCode, int storeId) throws Exception {
        StringBuffer sql = new StringBuffer();
        sql.append(" select pb.bill_num as oid,pb.remark as text,pb.bill_amount as total,pb.payment_amount as cost,pb.payment_amount as discountable,pb.payment_amount as membercouponsprice,pb.guest as people,pb.service_amount as mealfee, ")
                .append(" case when t.lock_pos_num is null or t.lock_pos_num = '' then '1' else '3' end as is_locked,pb.order_num as out_order_id,")
                .append(" pb.discount_mode_id,hdc.discount_case_name,pb.discount_rate,pb.discount_amount,o.org_full_name as shop_name")
                .append(" from pos_bill pb left join pos_tablestate t on pb.store_id = t.store_id and pb.fictitious_table = t.table_code")
                .append(" left join hq_discount_case hdc on pb.discount_case_id = hdc.id ")
                .append(" left join organ o on pb.store_id = o.id ")
                .append(" where pb.store_id = "+ storeId +" and pb.fictitious_table = '"+ tableCode +"' and pb.bill_property = '"+ SysDictionary.BILL_PROPERTY_CLOSED +"' order by pb.id desc limit 1");
        List<JSONObject> list = this.query4Json(tenancyId, sql.toString());
        if (null != list && list.size()>0 )
            return list.get(0);
        return null;
    }

    @Override
    public JSONObject getCookMethodObj(Integer methodId) throws Exception {

        StringBuilder sb = new StringBuilder();
        sb.append(" select hm.item_id,sd.class_item as method_name from  public.hq_item_method hm")
                .append(" left join public.sys_dictionary sd on hm.method_name_id=sd.id ")
                .append(" and sd.class_identifier_code='method' ")
                .append(" WHERE hm.id =").append(methodId);
        List<JSONObject> list = this.query4Json(null, sb.toString());
        if(null != list && list.size() > 0){
            return list.get(0);
        }
        return null;
    }

    @Override
    public String getBillNumByOrderId(String orderId) throws Exception {
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT bill_num as oid FROM pos_bill  WHERE order_num=")
                .append(" '").append(orderId).append("'");
        List<JSONObject> list = this.query4Json(null, sb.toString());
        if(null!=list && list.size()==1){
            return list.get(0).optString("oid");
        }
        return null;
    }

    @Override
    public void updatePosBillSource(String billNum,String sourceVal,boolean isOrdering) throws Exception {
        String sql = "";
        if(isOrdering){
            sql = " UPDATE pos_bill SET order_source='"+sourceVal+"' WHERE bill_num='" + billNum + "'";
        }else {
            sql = " UPDATE pos_bill SET payment_source='"+sourceVal+"' WHERE bill_num='" + billNum + "'";
        }
        this.execute(null, sql);
    }

    @Override
    public void updatePosBillOrderNum(String orderNum,String billNum) throws Exception {
        String sql = " UPDATE pos_bill SET order_num='"+orderNum+"',order_source='"+ SysDictionary.CHANEL_WSP17 +"' WHERE bill_num='" + billNum + "'";
        this.execute(null, sql);
    }

    @Override
    public JSONObject getTableStatus(String tenantId, Integer storeId, String tableNo) throws Exception {
        if(!StringUtils.isEmpty(tableNo)&&storeId!=null) {
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append("select id,state,lock_opt_num,lock_pos_num,opt_name from pos_tablestate  where table_code = '");
            stringBuilder.append(tableNo);
            stringBuilder.append("' and store_id = ");
            stringBuilder.append(storeId);
            stringBuilder.append(" order by last_updatetime desc limit 1");
            List<JSONObject> list = this.query4Json(tenantId, stringBuilder.toString());
            if(list.size()>0) {
                JSONObject jsonObject = list.get(0);
                return jsonObject;
            }
        }
        return null;
    }

    @Override
    public void lockTableStatus(Integer storeId, String tableNo, String lockPosNum, String lockOptNum, String optName) throws Exception{
		StringBuilder sql = new StringBuilder();
		sql.append(" UPDATE pos_tablestate SET lock_pos_num=?,lock_opt_num= ?,opt_name= ?");
		sql.append(" WHERE table_code= ? and store_id= ?");
		
		this.update(sql.toString(), new Object[]
		{ lockPosNum, lockOptNum, optName, tableNo, storeId });
    }

    @Override
    public JSONObject getBillMemberInfo(String billNum, String type, String cardCode) throws Exception{
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT * FROM pos_bill_member")
                .append("  WHERE bill_num=? ")
                .append(" and type =?")
                .append(" and card_code=?")
                .append(" ORDER BY id LIMIT 1");
        List<Object> params = new ArrayList<>();
        params.add(billNum);
        params.add(type);
        params.add(cardCode);
        List<JSONObject> list = this.query4Json(null,sb.toString(), params.toArray());
        if(null != list && list.size() > 0){
            return list.get(0);
        }
        return null;
    }

	@Override
	public List<JSONObject> getItemComboDetails(String tenantId, List<Integer> itemIdList) throws Exception
	{
		String itemIds = StringUtils.collectionToDelimitedString(itemIdList, ",");
		if (!StringUtils.isEmpty(itemIds))
		{
			StringBuilder sql = new StringBuilder();
			sql.append(" select * from hq_item_combo_details where iitem_id in (" + itemIds + ")");
			return this.query4Json(tenantId, sql.toString());
		}
		return null;
	}
	
	@Override
	public void deletePosBillMember(String tenantId, int storeId, String billno, String type)throws Exception
	{
		StringBuilder sql = new StringBuilder();
		sql.append("delete from pos_bill_member pbm where pbm.tenancy_id =? and pbm.store_id =? and pbm.bill_num =? and type = ?");
		this.jdbcTemplate.update(sql.toString(), new Object[]
		{ tenantId, storeId, billno, type });
	}

	@Override
	public List<JSONObject> getBillCouponItemListByBillNum(String tenantId, int storeId, String billno) throws Exception
	{
		StringBuilder sql = new StringBuilder("select * from pos_bill_item bi where bi.tenancy_id=? and bi.store_id=? and bi.bill_num=? and bi.item_property<>? and (bi.item_remark is null or bi.item_remark='') order by bi.item_time");
		return this.query4Json(tenantId, sql.toString(), new Object[]
		{ tenantId, storeId, billno, SysDictionary.ITEM_PROPERTY_MEALLIST });
	}
}
