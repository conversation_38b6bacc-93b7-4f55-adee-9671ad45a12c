package com.tzx.base.entity;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 * 账单菜品明细表映射实体
 * 
 * <AUTHOR>
 * <AUTHOR>
 */
public class PosBillItem {
	private String tenancy_id;
	private Integer store_id;
	private Integer id;
	private Integer rwid;
	private Integer yrwid;
	private String bill_num;
	private Integer details_id;
	private Integer item_id;
	private String item_num;
	private String item_name;
	private String item_english;
	private Integer item_unit_id;
	private String item_unit_name;
	private String stable_code;
	private String table_code;
	private String pushmoney_way;
	private Double proportion;
	private Integer item_serial;            // 点菜序号
	private Integer setmeal_rwid;            // 套餐主项点菜序号
	private Double assist_num;            // 辅助数量
	private Double item_assist_num;            // 辅助数量
	private Double assist_money;            // 辅助金额(项目组加价)
	private Integer assist_item_id;        // 套餐主项item_id
	private Integer setmeal_id;            // 套餐明细ID(hq_item_combo_details.id)
	private Integer group_id;                // 项目组ID(hq_item_group.id)
	private Integer setmeal_group_id;        // 项目组明细ID(hq_item_group_details.id)
	private String waiter_num;
	private Double item_price;
	private Double item_count;
	private Double item_amount;
	private Double real_amount;
	private Double discount_amount;
	private Double single_discount_amount;
	private Double discountr_amount;
	private String discount_state;
	private Double discount_rate;
	private Integer discount_mode_id;
	private Integer item_class_id;
	private String item_property;
	private String item_remark;
	private String print_tag;
	private Integer waitcall_tag;
	private String is_setmeal_changitem;
	private Timestamp item_time;
	private Date report_date;
	private Integer item_shift_id;
	private String item_mac_id;
	private String item_taste;
	private String order_remark;
	private String seat_num;
	private String ticket_num;
	private String sale_mode;
	private Integer gqcj_tag;
	private Integer kvscp_tag;
	private Integer discount_reason_id;
	private String is_showitem;
	private Integer upload_tag;
	private Double integraloffset;
	private String remark;
	private Double third_price;
	private Integer returngive_reason_id;
	private String manager_num;
	private String return_type;
	private Integer return_count;
	private Double method_money;
	private String batch_num;
	private Integer order_number;
	private String item_remark_his;
	private Integer combo_prop;
	private Double single_amount;
	private String discount_num;
	private String opt_num;
	private Integer activity_id;
	private Integer activity_rule_id;
	private Integer activity_count;
	private Double print_count;
	private Double single_discount_rate;
	private Double count_rate;
	private String activity_batch_num;
	private String default_state;
	private Double origin_item_price;
	private String price_type;
	private String special_price_id;
	private List<PosBillItem> itemMealList;

	public PosBillItem(String tenancy_id, Integer store_id, String bill_num, Integer details_id, Integer item_id, String item_num, String item_name, String item_english, Date report_date, Integer item_unit_id, String item_unit_name, String table_code, String stable_code, String pushmoney_way, Double proportion,
					   Double assist_num, Double item_assist_num, String waiter_num, Double item_price, Double item_count, Double discount_rate, String discount_state, Integer discount_mode_id, Integer item_class_id, String item_property, String item_remark, String print_tag, Integer setmeal_id, Integer setmeal_rwid,
					   String is_setmeal_changitem, Timestamp item_time, Integer item_serial, Integer item_shift_id, String item_mac_id, String order_remark, String seat_num, String sale_mode, String item_taste, Integer assist_item_id, Integer waitcall_tag, Integer returngive_reason_id, String batch_num,
					   Integer order_number, String opt_num, String manager_num, Integer discount_reason_id, Integer activity_id, String activity_batch_num, Integer activity_rule_id, Integer activity_count, Integer yrwid, Double print_count, String default_state, Double single_amount,
					   Double single_discount_rate, String discount_num, Double count_rate, Integer combo_prop, Double origin_item_price, String price_type, String special_price_id

	) {
		super();
		this.tenancy_id = tenancy_id;
		this.store_id = store_id;
		this.yrwid = yrwid;
		this.bill_num = bill_num;
		this.details_id = details_id;
		this.item_id = item_id;
		this.item_num = item_num;
		this.item_name = item_name;
		this.item_english = item_english;
		this.item_unit_id = item_unit_id;
		this.item_unit_name = item_unit_name;
		this.table_code = table_code;
		this.stable_code = stable_code;
		this.pushmoney_way = pushmoney_way;
		this.proportion = proportion;
		this.item_serial = item_serial;
		this.setmeal_rwid = setmeal_rwid;
		this.assist_num = assist_num;
		this.item_assist_num = item_assist_num;
		this.assist_item_id = assist_item_id;
		this.setmeal_id = setmeal_id;
		this.waiter_num = waiter_num;
		this.item_price = item_price;
		this.item_count = item_count;
		this.discount_state = discount_state;
		this.discount_rate = discount_rate;
		this.discount_mode_id = discount_mode_id;
		this.item_class_id = item_class_id;
		this.item_property = item_property;
		this.item_remark = item_remark;
		this.print_tag = print_tag;
		this.waitcall_tag = waitcall_tag;
		this.is_setmeal_changitem = is_setmeal_changitem;
		this.item_time = item_time;
		this.report_date = report_date;
		this.item_shift_id = item_shift_id;
		this.item_mac_id = item_mac_id;
		this.item_taste = item_taste;
		this.order_remark = order_remark;
		this.seat_num = seat_num;
		this.sale_mode = sale_mode;
		this.discount_reason_id = discount_reason_id;
		this.returngive_reason_id = returngive_reason_id;
		this.manager_num = manager_num;
		this.batch_num = batch_num;
		this.order_number = order_number;
		this.combo_prop = combo_prop;
		this.single_amount = single_amount;
		this.discount_num = discount_num;
		this.opt_num = opt_num;
		this.activity_id = activity_id;
		this.activity_rule_id = activity_rule_id;
		this.activity_count = activity_count;
		this.print_count = print_count;
		this.single_discount_rate = single_discount_rate;
		this.count_rate = count_rate;
		this.activity_batch_num = activity_batch_num;
		this.default_state = default_state;
		this.origin_item_price = origin_item_price;
		this.price_type = price_type;
		this.special_price_id = special_price_id;
	}

	public PosBillItem(String tenancy_id, Integer store_id, String bill_num, Integer details_id, Integer item_id, String item_num, String item_name, String item_english, Date report_date, Integer item_unit_id, String item_unit_name, String table_code, String pushmoney_way, Double proportion,
					   Double assist_num,Double item_assist_num, String waiter_num, Double item_price, Double item_count, Double discount_rate, String discount_state, Integer discount_mode_id, Integer item_class_id, String item_property, String item_remark, String print_tag, Integer setmeal_id, Integer setmeal_rwid,
					   String is_setmeal_changitem, Timestamp item_time, Integer item_serial, Integer item_shift_id, String item_mac_id, String order_remark, String seat_num, String sale_mode, String item_taste, Integer assist_item_id, Integer waitcall_tag, Integer returngive_reason_id, String batch_num,
					   Integer order_number, String opt_num, String manager_num, Integer discount_reason_id, Integer activity_id, String activity_batch_num, Integer activity_rule_id, Integer activity_count, Integer yrwid, Double print_count, String default_state, Double single_amount,
					   Double single_discount_rate, String discount_num, Double count_rate, Integer combo_prop, Double origin_item_price,String price_type,String special_price_id

	)
	{
		super();
		this.tenancy_id = tenancy_id;
		this.store_id = store_id;
		this.yrwid = yrwid;
		this.bill_num = bill_num;
		this.details_id = details_id;
		this.item_id = item_id;
		this.item_num = item_num;
		this.item_name = item_name;
		this.item_english = item_english;
		this.item_unit_id = item_unit_id;
		this.item_unit_name = item_unit_name;
		this.table_code = table_code;
		this.pushmoney_way = pushmoney_way;
		this.proportion = proportion;
		this.item_serial = item_serial;
		this.setmeal_rwid = setmeal_rwid;
		this.assist_num = assist_num;
		this.item_assist_num = item_assist_num;
		this.assist_item_id = assist_item_id;
		this.setmeal_id = setmeal_id;
		this.waiter_num = waiter_num;
		this.item_price = item_price;
		this.item_count = item_count;
		this.discount_state = discount_state;
		this.discount_rate = discount_rate;
		this.discount_mode_id = discount_mode_id;
		this.item_class_id = item_class_id;
		this.item_property = item_property;
		this.item_remark = item_remark;
		this.print_tag = print_tag;
		this.waitcall_tag = waitcall_tag;
		this.is_setmeal_changitem = is_setmeal_changitem;
		this.item_time = item_time;
		this.report_date = report_date;
		this.item_shift_id = item_shift_id;
		this.item_mac_id = item_mac_id;
		this.item_taste = item_taste;
		this.order_remark = order_remark;
		this.seat_num = seat_num;
		this.sale_mode = sale_mode;
		this.discount_reason_id = discount_reason_id;
		this.returngive_reason_id = returngive_reason_id;
		this.manager_num = manager_num;
		this.batch_num = batch_num;
		this.order_number = order_number;
		this.combo_prop = combo_prop;
		this.single_amount = single_amount;
		this.discount_num = discount_num;
		this.opt_num = opt_num;
		this.activity_id = activity_id;
		this.activity_rule_id = activity_rule_id;
		this.activity_count = activity_count;
		this.print_count = print_count;
		this.single_discount_rate = single_discount_rate;
		this.count_rate = count_rate;
		this.activity_batch_num = activity_batch_num;
		this.default_state = default_state;
		this.origin_item_price = origin_item_price;
		this.price_type=price_type;
		this.special_price_id=special_price_id;
	}

	public String getPrice_type() {
		return price_type;
	}

	public void setPrice_type(String price_type) {
		this.price_type = price_type;
	}

	public String getSpecial_price_id() {
		return special_price_id;
	}

	public void setSpecial_price_id(String special_price_id) {
		this.special_price_id = special_price_id;
	}

	public String getTenancy_id() {
		return tenancy_id;
	}

	public void setTenancy_id(String tenancy_id) {
		this.tenancy_id = tenancy_id;
	}

	public Integer getStore_id() {
		return store_id;
	}

	public void setStore_id(Integer store_id) {
		this.store_id = store_id;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getRwid() {
		return rwid;
	}

	public void setRwid(Integer rwid) {
		this.rwid = rwid;
	}

	public Integer getYrwid() {
		return yrwid;
	}

	public void setYrwid(Integer yrwid) {
		this.yrwid = yrwid;
	}

	public String getBill_num() {
		return bill_num;
	}

	public void setBill_num(String bill_num) {
		this.bill_num = bill_num;
	}

	public Integer getDetails_id() {
		return details_id;
	}

	public void setDetails_id(Integer details_id) {
		this.details_id = details_id;
	}

	public Integer getItem_id() {
		return item_id;
	}

	public void setItem_id(Integer item_id) {
		this.item_id = item_id;
	}

	public String getItem_num() {
		return item_num;
	}

	public void setItem_num(String item_num) {
		this.item_num = item_num;
	}

	public String getItem_name() {
		return item_name;
	}

	public void setItem_name(String item_name) {
		this.item_name = item_name;
	}

	public String getItem_english() {
		return item_english;
	}

	public void setItem_english(String item_english) {
		this.item_english = item_english;
	}

	public Integer getItem_unit_id() {
		return item_unit_id;
	}

	public void setItem_unit_id(Integer item_unit_id) {
		this.item_unit_id = item_unit_id;
	}

	public String getItem_unit_name() {
		return item_unit_name;
	}

	public void setItem_unit_name(String item_unit_name) {
		this.item_unit_name = item_unit_name;
	}

	public String getStable_code() {
		return stable_code;
	}

	public void setStable_code(String stable_code) {
		this.stable_code = stable_code;
	}

	public String getTable_code() {
		return table_code;
	}

	public void setTable_code(String table_code) {
		this.table_code = table_code;
	}

	public String getPushmoney_way() {
		return pushmoney_way;
	}

	public void setPushmoney_way(String pushmoney_way) {
		this.pushmoney_way = pushmoney_way;
	}

	public Double getProportion() {
		return proportion;
	}

	public void setProportion(Double proportion) {
		this.proportion = proportion;
	}

	public Double getAssist_num() {
		return assist_num;
	}

	public void setAssist_num(Double assist_num) {
		this.assist_num = assist_num;
	}

	public Double getAssist_money() {
		return assist_money;
	}

	public void setAssist_money(Double assist_money) {
		this.assist_money = assist_money;
	}

	public String getWaiter_num() {
		return waiter_num;
	}

	public void setWaiter_num(String waiter_num) {
		this.waiter_num = waiter_num;
	}

	public Double getItem_price() {
		return item_price;
	}

	public void setItem_price(Double item_price) {
		this.item_price = item_price;
	}

	public Double getItem_count() {
		return item_count;
	}

	public void setItem_count(Double item_count) {
		this.item_count = item_count;
	}

	public Double getItem_amount() {
		return item_amount;
	}

	public void setItem_amount(Double item_amount) {
		this.item_amount = item_amount;
	}

	public Double getReal_amount() {
		return real_amount;
	}

	public void setReal_amount(Double real_amount) {
		this.real_amount = real_amount;
	}

	public Double getDiscount_amount() {
		return discount_amount;
	}

	public void setDiscount_amount(Double discount_amount) {
		this.discount_amount = discount_amount;
	}

	public Double getSingle_discount_amount() {
		return single_discount_amount;
	}

	public void setSingle_discount_amount(Double single_discount_amount) {
		this.single_discount_amount = single_discount_amount;
	}

	public Double getDiscountr_amount() {
		return discountr_amount;
	}

	public void setDiscountr_amount(Double discountr_amount) {
		this.discountr_amount = discountr_amount;
	}

	public String getDiscount_state() {
		return discount_state;
	}

	public void setDiscount_state(String discount_state) {
		this.discount_state = discount_state;
	}

	public Double getDiscount_rate() {
		return discount_rate;
	}

	public void setDiscount_rate(Double discount_rate) {
		this.discount_rate = discount_rate;
	}

	public Integer getDiscount_mode_id() {
		return discount_mode_id;
	}

	public void setDiscount_mode_id(Integer discount_mode_id) {
		this.discount_mode_id = discount_mode_id;
	}

	public Integer getItem_class_id() {
		return item_class_id;
	}

	public void setItem_class_id(Integer item_class_id) {
		this.item_class_id = item_class_id;
	}

	public String getItem_property() {
		return item_property;
	}

	public void setItem_property(String item_property) {
		this.item_property = item_property;
	}

	public String getItem_remark() {
		return item_remark;
	}

	public void setItem_remark(String item_remark) {
		this.item_remark = item_remark;
	}

	public String getPrint_tag() {
		return print_tag;
	}

	public void setPrint_tag(String print_tag) {
		this.print_tag = print_tag;
	}

	public Integer getWaitcall_tag() {
		return waitcall_tag;
	}

	public void setWaitcall_tag(Integer waitcall_tag) {
		this.waitcall_tag = waitcall_tag;
	}

	public Integer getSetmeal_id() {
		return setmeal_id;
	}

	public void setSetmeal_id(Integer setmeal_id) {
		this.setmeal_id = setmeal_id;
	}

	public Integer getSetmeal_rwid() {
		return setmeal_rwid;
	}

	public void setSetmeal_rwid(Integer setmeal_rwid) {
		this.setmeal_rwid = setmeal_rwid;
	}

	public String getIs_setmeal_changitem() {
		return is_setmeal_changitem;
	}

	public void setIs_setmeal_changitem(String is_setmeal_changitem) {
		this.is_setmeal_changitem = is_setmeal_changitem;
	}

	public Timestamp getItem_time() {
		return item_time;
	}

	public void setItem_time(Timestamp item_time) {
		this.item_time = item_time;
	}

	public Date getReport_date() {
		return report_date;
	}

	public void setReport_date(Date report_date) {
		this.report_date = report_date;
	}

	public Integer getItem_shift_id() {
		return item_shift_id;
	}

	public void setItem_shift_id(Integer item_shift_id) {
		this.item_shift_id = item_shift_id;
	}

	public String getItem_mac_id() {
		return item_mac_id;
	}

	public void setItem_mac_id(String item_mac_id) {
		this.item_mac_id = item_mac_id;
	}

	public String getItem_taste() {
		return item_taste;
	}

	public void setItem_taste(String item_taste) {
		this.item_taste = item_taste;
	}

	public String getOrder_remark() {
		return order_remark;
	}

	public void setOrder_remark(String order_remark) {
		this.order_remark = order_remark;
	}

	public String getSeat_num() {
		return seat_num;
	}

	public void setSeat_num(String seat_num) {
		this.seat_num = seat_num;
	}

	public String getTicket_num() {
		return ticket_num;
	}

	public void setTicket_num(String ticket_num) {
		this.ticket_num = ticket_num;
	}

	public String getSale_mode() {
		return sale_mode;
	}

	public void setSale_mode(String sale_mode) {
		this.sale_mode = sale_mode;
	}

	public Integer getGqcj_tag() {
		return gqcj_tag;
	}

	public void setGqcj_tag(Integer gqcj_tag) {
		this.gqcj_tag = gqcj_tag;
	}

	public Integer getKvscp_tag() {
		return kvscp_tag;
	}

	public void setKvscp_tag(Integer kvscp_tag) {
		this.kvscp_tag = kvscp_tag;
	}

	public Integer getDiscount_reason_id() {
		return discount_reason_id;
	}

	public void setDiscount_reason_id(Integer discount_reason_id) {
		this.discount_reason_id = discount_reason_id;
	}

	public String getIs_showitem() {
		return is_showitem;
	}

	public void setIs_showitem(String is_showitem) {
		this.is_showitem = is_showitem;
	}

	public Integer getUpload_tag() {
		return upload_tag;
	}

	public void setUpload_tag(Integer upload_tag) {
		this.upload_tag = upload_tag;
	}

	public Integer getAssist_item_id() {
		return assist_item_id;
	}

	public void setAssist_item_id(Integer assist_item_id) {
		this.assist_item_id = assist_item_id;
	}

	public Double getIntegraloffset() {
		return integraloffset;
	}

	public void setIntegraloffset(Double integraloffset) {
		this.integraloffset = integraloffset;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public Integer getSetmeal_group_id() {
		return setmeal_group_id;
	}

	public void setSetmeal_group_id(Integer setmeal_group_id) {
		this.setmeal_group_id = setmeal_group_id;
	}

	public Integer getGroup_id() {
		return group_id;
	}

	public void setGroup_id(Integer group_id) {
		this.group_id = group_id;
	}

	public Double getThird_price() {
		return third_price;
	}

	public void setThird_price(Double third_price) {
		this.third_price = third_price;
	}

	public Integer getReturngive_reason_id() {
		return returngive_reason_id;
	}

	public void setReturngive_reason_id(Integer returngive_reason_id) {
		this.returngive_reason_id = returngive_reason_id;
	}

	public String getManager_num() {
		return manager_num;
	}

	public void setManager_num(String manager_num) {
		this.manager_num = manager_num;
	}

	public String getReturn_type() {
		return return_type;
	}

	public void setReturn_type(String return_type) {
		this.return_type = return_type;
	}

	public Integer getReturn_count() {
		return return_count;
	}

	public void setReturn_count(Integer return_count) {
		this.return_count = return_count;
	}

	public Double getMethod_money() {
		return method_money;
	}

	public void setMethod_money(Double method_money) {
		this.method_money = method_money;
	}

	public String getBatch_num() {
		return batch_num;
	}

	public void setBatch_num(String batch_num) {
		this.batch_num = batch_num;
	}

	public Integer getOrder_number() {
		return order_number;
	}

	public void setOrder_number(Integer order_number) {
		this.order_number = order_number;
	}

	public String getItem_remark_his() {
		return item_remark_his;
	}

	public void setItem_remark_his(String item_remark_his) {
		this.item_remark_his = item_remark_his;
	}

	public Integer getItem_serial() {
		return item_serial;
	}

	public void setItem_serial(Integer item_serial) {
		this.item_serial = item_serial;
	}

	public Integer getCombo_prop() {
		return combo_prop;
	}

	public void setCombo_prop(Integer combo_prop) {
		this.combo_prop = combo_prop;
	}

	public Double getSingle_amount() {
		return single_amount;
	}

	public void setSingle_amount(Double single_amount) {
		this.single_amount = single_amount;
	}

	public String getDiscount_num() {
		return discount_num;
	}

	public void setDiscount_num(String discount_num) {
		this.discount_num = discount_num;
	}

	public String getOpt_num() {
		return opt_num;
	}

	public void setOpt_num(String opt_num) {
		this.opt_num = opt_num;
	}

	public Integer getActivity_id() {
		return activity_id;
	}

	public void setActivity_id(Integer activity_id) {
		this.activity_id = activity_id;
	}

	public Integer getActivity_rule_id() {
		return activity_rule_id;
	}

	public void setActivity_rule_id(Integer activity_rule_id) {
		this.activity_rule_id = activity_rule_id;
	}

	public Integer getActivity_count() {
		return activity_count;
	}

	public void setActivity_count(Integer activity_count) {
		this.activity_count = activity_count;
	}

	public Double getPrint_count() {
		return print_count;
	}

	public void setPrint_count(Double print_count) {
		this.print_count = print_count;
	}

	public Double getSingle_discount_rate() {
		return single_discount_rate;
	}

	public void setSingle_discount_rate(Double single_discount_rate) {
		this.single_discount_rate = single_discount_rate;
	}

	public Double getCount_rate() {
		return count_rate;
	}

	public void setCount_rate(Double count_rate) {
		this.count_rate = count_rate;
	}

	public String getActivity_batch_num() {
		return activity_batch_num;
	}

	public void setActivity_batch_num(String activity_batch_num) {
		this.activity_batch_num = activity_batch_num;
	}

	public String getDefault_state() {
		return default_state;
	}

	public void setDefault_state(String default_state) {
		this.default_state = default_state;
	}

	public Double getOrigin_item_price() {
		return origin_item_price;
	}

	public void setOrigin_item_price(Double origin_item_price) {
		this.origin_item_price = origin_item_price;
	}

	public List<PosBillItem> getItemMealList() {
		return itemMealList;
	}

	public void setItemMealList(List<PosBillItem> itemMealList) {
		this.itemMealList = itemMealList;
	}

	public Double getItem_assist_num() {
		return item_assist_num;
	}

	public void setItem_assist_num(Double item_assist_num) {
		this.item_assist_num = item_assist_num;
	}

}