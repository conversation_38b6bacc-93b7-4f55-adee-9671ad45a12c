package com.tzx.pos.po.springjdbc.dao;

import java.util.List;

import net.sf.json.JSONObject;

import com.tzx.base.entity.PosBill;
import com.tzx.base.entity.PosBillItem;
import com.tzx.base.entity.PosBillPayment;
import com.tzx.base.entity.PosBillService;
import com.tzx.base.entity.PosMethodItem;
import com.tzx.pos.base.dao.BaseDao;

public interface PosBaseDao extends BaseDao
{
	String	NAME	= "com.tzx.pos.po.springjdbc.dao.imp.PosBaseDaoImp";

	/**
	 * 获取账单数据
	 *
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @return
	 * @throws Exception
	 */
	public List<PosBill> getPosBillByBillnum(String tenantId, Integer storeId, String billNum) throws Exception;

	/**
	 * 获取账单明细数据
	 *
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @return
	 * @throws Exception
	 */
	public List<PosBillItem> getPosBillItemByBillnum(String tenantId, Integer storeId, String billNum) throws Exception;

	/**
	 * 获取账单付款明细数据
	 *
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @return
	 * @throws Exception
	 */
	public List<PosBillPayment> getPosBillPaymentByBillnum(String tenantId, Integer storeId, String billNum) throws Exception;

	/**
	 * 账单服务费
	 *
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @return
	 * @throws Exception
	 */
	public List<PosBillService> getPosBillServiceByBillnum(String tenantId, Integer storeId, String billNum) throws Exception;

	/**
	 * 账单明细做法信息
	 *
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @return
	 * @throws Exception
	 */
	public List<PosMethodItem> getPosMethodItemByBillnum(String tenantId, Integer storeId, String billNum) throws Exception;

	/**修改账单金额
	 * @param tenantId
	 * @param storeId
	 * @param bill
	 * @return
	 * @throws Exception
	 */
	public void updatePosBillForAmount(String tenantId, Integer storeId,PosBill bill) throws Exception;

	/**
	 * @param tenantId
	 * @param storeId
	 * @param itemList
	 * @return
	 * @throws Exception
	 */
	public void updatePosBillItemForAmount(String tenantId, Integer storeId,List<PosBillItem> itemList) throws Exception;

	/**
	 * @param tenantId
	 * @param storeId
	 * @param serviceList
	 * @return
	 * @throws Exception
	 */
	public void updatePosBillServiceForAmount(String tenantId, Integer storeId,List<PosBillService> serviceList) throws Exception;
	
	/**
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @param discountCaseId
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getPosBillItemForDiscountCaseByBillnum(String tenantId, Integer storeId, String billNum,Integer discountCaseId) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @throws Exception
	 */
	public void updatePosBillForUploadByBillNum(String tenancyId, int storeId, String tableName, String billNum, String uploadTag) throws Exception;

	/*
	 * 获取数据库所有的索引名称列表
	 */
	public List<JSONObject> getIndexNameList() throws Exception;
	
	/*
	 * 执行索引重建批量SQL
	 */	
	public boolean execSQLList(List<String> indexNameList) throws Exception;
	
}
