package com.tzx.pos.bo;

import java.util.List;

import com.tzx.framework.common.entity.Data;
import com.tzx.pos.bo.dto.SysModule;

import net.sf.json.JSONObject;

/**
 * TODO
 * <AUTHOR> 
 * @日期：2015年5月13日-下午1:33:49
 */

public interface LoginService
{
	String NAME = "com.tzx.pos.bo.imp.LoginServiceImp";
	
	/** 登录
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> login(Data param) throws Exception;
	
	/** 修改密码
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void updatePasword(Data param,Data result) throws Exception;
	
	/** 获取某个操作员对某个功能是否有权限
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void checkAuth(Data param,Data result) throws Exception;
	
	/**
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void getOrganId(Data param,Data result) throws Exception;
	
	/**
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public List<SysModule> getSysModule(Data param) throws Exception;
}
