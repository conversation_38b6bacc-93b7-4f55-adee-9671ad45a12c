package com.tzx.estate.base.util;

import java.util.List;
import java.util.Map;

import com.tzx.estate.common.entity.Data;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;

/**
 * 用来处理Data里的list<?>
 * <AUTHOR>
 * 2015年7月14日-上午10:09:53
 */
public class ReqDataUtil
{
	/**
	 * 获取请求data里的参数列表
	 * @param param
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static List<Map<String,Object>> getDataList(Data param)
	{
		if (param.getData().size() == 0)
		{
			throw new SystemException(PosErrorCode.PARAM_ERROR);
		}
		
		return (List<Map<String, Object>>) param.getData();
	}
	
	@SuppressWarnings("unchecked")
	public static Map<String,Object> getDataMap(Data param)
	{
		if (param.getData().size() == 0)
		{
			throw new SystemException(PosErrorCode.PARAM_ERROR);
		}
		
		return (Map<String, Object>) param.getData().get(0);
	}
}
