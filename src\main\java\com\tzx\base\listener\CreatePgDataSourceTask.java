/** 
 * @(#)CodeFormat.java    1.0   2018-08-21 
 * 
 * Copyright 北京普照天星科技有限公司. All rights reserved. 
 * PROPRIETARY/CONFIDENTIAL. Use is subject to license terms. 
 */
package com.tzx.base.listener;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;
import org.postgresql.ds.PGPoolingDataSource;
import org.springframework.jdbc.core.JdbcTemplate;

import com.alibaba.druid.pool.DruidDataSource;
import com.tzx.framework.common.util.SpringConext;

/**
 * 创建PgDataSource数据库连接池。
 * 
 * <AUTHOR> email:<EMAIL>
 * @version 1.0  2018-08-21
 * @see     
 * @since   JDK7.0
 * @update  
 */
public class CreatePgDataSourceTask implements Runnable{
	
	private static final Logger	logger	= Logger.getLogger(CreatePgDataSourceTask.class);

	@Override
	public void run() {
		try{
			// 等待Bean初始化完成
			while(SpringConext.getApplicationContext() == null){
				Thread.sleep(1000);
			}  	
			DruidDataSource ds =  (DruidDataSource)SpringConext.getApplicationContext().getBean("dataSource");
			String url = ds.getUrl();
			String tmpUrl = "";
			if(url.indexOf("?") != -1){
				tmpUrl = url.substring(url.indexOf("//")+2,url.indexOf("?"));
			}else{
				tmpUrl = url.substring(url.indexOf("//")+2,url.length());
			}		
			String IP = tmpUrl.substring(0,tmpUrl.indexOf(":"));
			String port = tmpUrl.substring(tmpUrl.indexOf(":")+1,tmpUrl.indexOf("/"));
			String dbName = tmpUrl.substring(tmpUrl.indexOf("/")+1,tmpUrl.length());
			String userName = ds.getUsername();
			String pwd = ds.getPassword();				
			PosSystemInitListener.pGPoolingDataSource = new PGPoolingDataSource();  
			PosSystemInitListener.pGPoolingDataSource.setServerName(IP);  			       
			PosSystemInitListener.pGPoolingDataSource.setPortNumber(Integer.valueOf(port));  			  
			PosSystemInitListener.pGPoolingDataSource.setDatabaseName(dbName);  
			PosSystemInitListener.pGPoolingDataSource.setUser(userName);  
			PosSystemInitListener.pGPoolingDataSource.setPassword(pwd);  
			PosSystemInitListener.pGPoolingDataSource.setMaxConnections(2); 
//			testInsert();
		}catch(Exception e){
			logger.error(e.getMessage());
		}
	}
	
	private void testInsert(){
		JdbcTemplate jdbcTemplate =  (JdbcTemplate)SpringConext.getApplicationContext().getBean("jdbcTemplate");
		long t = System.currentTimeMillis();
		List<Object[]> batchArgs = new ArrayList<Object[]>();
		String sql = "insert into b_log(nr,dcnr,dcjh,pcid,timer) values (?,?,?,?,?)";
		String val = "";
		int num = 100000;
		for(int i=0;i<num;i++){
			val = i + "testtest";
			batchArgs.add(new Object[]{val,val,"0",val,val});
			if(i % 2000 == 0){
				jdbcTemplate.batchUpdate(sql, batchArgs);
				batchArgs = new ArrayList<Object[]>();
			}			
		}
		if(batchArgs.size()>0){
			int[] res =jdbcTemplate.batchUpdate(sql, batchArgs);
		}
		System.out.println("批量方式执行"+num+"次插入共用时："+(System.currentTimeMillis()-t)+"毫秒。");
		
//		jdbcTemplate.execute("delete from b_log");
		
//		t = System.currentTimeMillis();
//		for(int i =0;i<num;i++){
//			val = i + "testtest";
//			sql = "insert into b_log(nr,dcnr,dcjh,pcid,timer) values ('"+val+"','"+val+"','0','"+val+"','"+val+"')";
//			jdbcTemplate.execute(sql);
//		}
//		System.out.println("逐行执行"+num+"次插入共用时："+(System.currentTimeMillis()-t)+"毫秒。");
		
	}
	
	public static void main(String[] args){
		String url = "****************************************?rewriteBatchedStatements=true";
		//String url = "****************************************";
		String tmpUrl = "";
		if(url.indexOf("?") != -1){
			tmpUrl = url.substring(url.indexOf("//")+2,url.indexOf("?"));
		}else{
			tmpUrl = url.substring(url.indexOf("//")+2,url.length());
		}		
		System.out.println(tmpUrl);
		String IP = tmpUrl.substring(0,tmpUrl.indexOf(":"));
		System.out.println(IP);
		String port = tmpUrl.substring(tmpUrl.indexOf(":")+1,tmpUrl.indexOf("/"));
		System.out.println(port);
		String dbName = tmpUrl.substring(tmpUrl.indexOf("/")+1,tmpUrl.length());
		System.out.println(dbName);
	}

}
