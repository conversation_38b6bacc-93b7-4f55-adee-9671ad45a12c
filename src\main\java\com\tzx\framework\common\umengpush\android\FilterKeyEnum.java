package com.tzx.framework.common.umengpush.android;

public enum FilterKeyEnum {

	/**
	 * 应用版本
	 */
	APP_VERSION("app_version","应用版本"),
	/**
	 * 渠道
	 */
	CHANNEL("channel","渠道"),
	/**
	 * 设备型号
	 */
	DEVICE_MODEL("device_model","设备型号"),
	/**
	 * 国家
	 */
	COUNTRY("country","国家"),
	/**
	 * 省份
	 */
	PROVINCE("province","省"),
	/**
	 * 用户标签
	 */
	TAG("tag","用户标签"),
	/**
	 * 语言
	 */
	LANGUAGE("language","语言"),
	/**
	 * 一段时间内活跃
	 */
	LAUNCH_FROM("launch_from","一段时间内活跃"),
	/**
	 * 一段时间内不活跃
	 */
	NOT_LAUNCH_FROM("not_launch_from","一段时间内不活跃");
	
	public String keyName;
	public String mark;
	FilterKeyEnum(String keyName, String mark){
		this.keyName = keyName;
		this.mark = mark;
	}
	
}
