package com.tzx.framework.common.util.redis;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by jzq1999 on 2017/4/6.
 */
public abstract  class BaseRedisUtil {

    protected boolean useCache;
    protected int defaultExpireTime = 10800;
    
//    protected int defaultExpireTime = 60;

    public abstract void rpush(String key, String... val);

    public abstract void lpush(String key, String... val);

    public abstract Long llen(String key);

    public abstract List lrange(String key, int start, int end);

    public abstract void lset(String key, int index, String value);

    public abstract String lpop(String key);

    public abstract String rpop(String key);

    public abstract boolean exists(String key);

    public abstract Long del(String key);

    public abstract void set(String key, String val);

    public abstract void set(String key, String val, boolean expire);

    public abstract String get(String key);

    public abstract String getSet(String key, String value);

    public abstract void lrem(String key, Integer count, String val);

    public abstract void sadd(String key, String val);

    public abstract String spop(String key);

    public abstract Set<String> smembers(String key);

    public abstract int scard(String key);

    public abstract void expire(String key, int num);

    public abstract void setex(String key, int seconds, String value);

    public abstract void srem(String key, String val);

    public abstract boolean sismember(String key, String member);

    public abstract Long incr(String key);

    public abstract Long decr(String key);

    public abstract Long hincrBy(String key, String field, Long value);

    public abstract boolean hmexite(String key, String field);

    public abstract Long hgetNum(String key, String field);

    public abstract Long hsetnx(String key, String field, String value);

    public abstract Map<String, String> hgetAll(String key);

    public abstract Object getBySerialize(String key);

    public abstract void setBySerialize(String key, Object val);

    public abstract void setexBySerialize(String key, int seconds, Object val);

    public abstract void zadd(String key, double score, String member);

    public abstract void zrem(String key, String member);

    public abstract Double zscore(String key, String member);

    public abstract List<String> zquery(String key, Long start, Long end, boolean isAsc);

    public abstract Long zcard(String key);

    public abstract String hget(String key, String field);

    public abstract Long hdel(String key, String... fields);

    public abstract Long hset(String key, String field, String value);

    public abstract void sadd(String key, String... member);

    public void setDefaultExpireTime(int defaultExpireTime) {
        this.defaultExpireTime = defaultExpireTime;
    }

    public void setUseCache(boolean useCache) {
        this.useCache = useCache;
    }

    public boolean isUseCache() {
        return this.useCache;
    }

}
