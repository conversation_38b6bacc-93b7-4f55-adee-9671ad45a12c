package com.tzx.pos.base.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import net.sf.json.JSONObject;

import org.apache.log4j.Logger;

import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.util.Comet4jUtil;
import com.tzx.framework.common.util.HttpUtil;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.pos.po.springjdbc.dao.PosDao;

/**
 * 自动提醒打烊
 * 
 * 
 */
public class AutoNotifyDayEndRunnable implements Runnable
{

	private static final Logger	logger	= Logger.getLogger(AutoNotifyDayEndRunnable.class);
	@Override
	public void run()
	{
		try
		{
			Map<String, String> systemMap = Constant.getSystemMap();

			if (!systemMap.containsKey("tenent_id") || !systemMap.containsKey("store_id")||"".equals(systemMap.get("tenent_id")) || "".equals(systemMap.get("store_id")))
			{
				logger.info("自动提醒打烊: tenent_id: "+systemMap.get("tenent_id")+", store_id: "+systemMap.get("store_id"));
				return;
			}

			String tenancyId = systemMap.get("tenent_id");
			int storeId = Integer.parseInt(systemMap.get("store_id"));

			DBContextHolder.setTenancyid(tenancyId);
			PosDao posDao = SpringConext.getApplicationContext().getBean(PosDao.class);
			
			boolean isnotify = posDao.isNotifyDayEnd(tenancyId, storeId);
			
			List<JSONObject> jsonList = new ArrayList<JSONObject>();
			JSONObject json = new JSONObject();
			Data data = new Data();
			data.setStore_id(storeId);
			data.setData(jsonList);
			data.setTenancy_id(tenancyId);
			data.setType(Type.NOTIFYDAYEND);
			data.setOper(Oper.check);
			json.put("isnotify", isnotify);
			if (isnotify)
			{
				logger.info("==========超过设定的打烊时间,推送打烊提醒==============");
				json.put("message", "已超过设定的打烊时间，请及时打烊");
				
				jsonList.add(json);
				
				data.setData(jsonList);
				
				Comet4jUtil.comet4J(jsonList, Type.NOTIFYDAYEND, Oper.notice);
			}
			
			
		}
		catch (Exception e)
		{
			e.printStackTrace();
			logger.error("自动提醒打烊报错:", e);
		}
	}

}
