package com.tzx.pos.bo.dto;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> 
 * @日期：2015年5月18日-上午10:11:28
 */

public class PosOpenTable implements Serializable
{

	/**
	 * 
	 */
	private static final long	serialVersionUID	= -2611838219596888653L;
	
	private Integer mode;
	private Date report_date;
	private Integer shift_id;
	private String opt_num;
	private String waiter_num;
	private String table_code;
	private Integer guest; //顾客人数
	private String preorderno;
	private String pos_num;
	private Integer item_menu_id;
	private String sale_mode;
	
	public Integer getMode()
	{
		return mode;
	}
	public void setMode(Integer mode)
	{
		this.mode = mode;
	}
	public Date getReport_date()
	{
		return report_date;
	}
	public void setReport_date(Date report_date)
	{
		this.report_date = report_date;
	}
	public Integer getShift_id()
	{
		return shift_id;
	}
	public void setShift_id(Integer shift_id)
	{
		this.shift_id = shift_id;
	}
	public String getOpt_num()
	{
		return opt_num;
	}
	public void setOpt_num(String opt_num)
	{
		this.opt_num = opt_num;
	}
	public String getWaiter_num()
	{
		return waiter_num;
	}
	public void setWaiter_num(String waiter_num)
	{
		this.waiter_num = waiter_num;
	}
	public String getTable_code()
	{
		return table_code;
	}
	public void setTable_code(String table_code)
	{
		this.table_code = table_code;
	}
	public Integer getGuest()
	{
		return guest;
	}
	public void setGuest(Integer guest)
	{
		this.guest = guest;
	}
	public String getPreorderno()
	{
		return preorderno;
	}
	public void setPreorderno(String preorderno)
	{
		this.preorderno = preorderno;
	}
	public String getPos_num()
	{
		return pos_num;
	}
	public void setPos_num(String pos_num)
	{
		this.pos_num = pos_num;
	}
	public Integer getItem_menu_id()
	{
		return item_menu_id;
	}
	public void setItem_menu_id(Integer item_menu_id)
	{
		this.item_menu_id = item_menu_id;
	}
	public String getSale_mode()
	{
		return sale_mode;
	}
	public void setSale_mode(String sale_mode)
	{
		this.sale_mode = sale_mode;
	}
}
