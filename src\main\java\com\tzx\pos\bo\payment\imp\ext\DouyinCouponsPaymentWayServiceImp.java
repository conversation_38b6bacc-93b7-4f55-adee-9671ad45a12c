package com.tzx.pos.bo.payment.imp.ext;

import com.tzx.base.entity.PosBillPayment;
import com.tzx.base.entity.PosBillPaymentCoupons;
import com.tzx.base.entity.PosThirdPaymentRefundEntity;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.AmountUtil;
import com.tzx.framework.common.util.DoubleHelper;
import com.tzx.framework.common.util.Tools;
import com.tzx.framework.common.util.UUIDUtil;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.dao.ThirdCouponsDao;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.base.util.SpringContextUtils;
import com.tzx.pos.bo.douyin.IDouyinOpen;
import com.tzx.pos.bo.payment.PaymentWayService;
import com.tzx.pos.bo.payment.imp.PaymentWayServiceImp;
import com.tzx.thirdpay.bo.DouyinCouponsService;
import com.tzx.thirdpay.bo.ThirdPaymentRefundService;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;

import static com.tzx.pos.base.constant.SysDictionary.PAYMENT_CLASS_DOUYIN_COUPONS_PAY;
import static com.tzx.pos.base.constant.SysDictionary.PAYMENT_CLASS_DOUYIN_COUPONS_PHANTOM_PAY;


@Service("com.tzx.pos.bo.payment.imp.ext.DouyinCouponsPaymentWayServiceImp")
public class DouyinCouponsPaymentWayServiceImp extends PaymentWayServiceImp implements PaymentWayService {
    @Resource(name = DouyinCouponsService.NAME)
    private DouyinCouponsService douyinCouponsService;

    @Autowired
    private IDouyinOpen douyinOpen;

    @Resource(name = ThirdPaymentRefundService.NAME)
    private ThirdPaymentRefundService thirdPaymentRefundService;

    private static final Map<String,JSONObject> paymentJzidMap=new HashMap<>();

    @Override
    public void payment(String tenantId, int storeId, String billNum, String batchNum, Date reportDate, int shiftId, String posNum, String optNum, String tableCode, JSONObject payment, Timestamp currentTime, String isOnlinePay) throws Exception {

        Double amount = payment.optDouble("amount");
        Double count = payment.optDouble("count");
        Double currencyAmount = payment.optDouble("currency_amount");
        String number = payment.optString("number");
        String phone = payment.optString("phone");
//        String paymentUid = payment.optString("bill_code");
        String paymentUid = UUIDUtil.generateGUID();

        int jzid = payment.optInt("jzid");
        String paymentClass = payment.optString("payment_class");
        String paymentName = payment.optString("payment_name");
        String nameEnglish = payment.optString("payment_english_name");
        double rate = payment.optDouble("rate");
        String remark = payment.optString("remark");
        String isYsk = payment.optString("is_ysk");
        if (Tools.isNullOrEmpty(isYsk)) {
            isYsk = "N";
        }

        String title=payment.optString("title");
        String chanel = payment.optString("chanel");

        String couponsPro=null;

        JSONArray rwids = payment.optJSONArray("item_rwids");

        Double discountAmount=payment.optDouble("coupon_amount");
//        Double paidAmount=payment.optDouble("due_amount");

        Double couponBuyPrice=0d;
        Double paidAmount=0d;
        Double tenancyAssume=0d;
        Double thirdAssume=0d;

        Double moreCoupon = DoubleHelper.sub(currencyAmount, discountAmount, 4);

//        if(moreCoupon<0){
//            moreCoupon=0d;
//        }

//        Double paidAmount= AmountUtil.changeF2Y(payment.optDouble("pay_amount",0d));
//
//        Double ticketAmount= AmountUtil.changeF2Y(payment.optDouble("ticket_amount",0d)); //平台承担金额
//        Double paymentDiscountAmount= AmountUtil.changeF2Y(payment.optDouble("payment_discount_amount",0d)); //可能是支付优惠，也算到实收里

        //重新计算实收金额
//        paidAmount=DoubleHelper.padd(paidAmount,DoubleHelper.padd(ticketAmount,paymentDiscountAmount));


//        original_amount		100
//        ticket_amount		3
//        merchant_ticket_amount		1
//        payment_discount_amount		1
//        payment_amount		95
//
//        商家实收	paidAmount	payment_amount+ticket_amount+payment_discount_amount	95+3+1=99
//                  original_amount-merchant_ticket_amount	100-1=99
//
//        平台承担优惠	thirdAssume	original_amount-payment_amount-merchant_ticket_amount	100-95-1=4
//        用户购买金额	Coupon_buy_price	payment_amount	95
//        商户承担金额	Tenancy_assume	merchant_ticket_amount	1




        //菜品券
        if (com.tzx.thirdpay.bo.DouyinCouponsService.GROUPON_TYPE_TUANCAN.equals( payment.optString("groupon_type"))
        || DouyinCouponsService.GROUPON_TYPE_CIKA.equals( payment.optString("groupon_type"))) {
            couponsPro = SysDictionary.COUPONS_PRO_DISH;

            paidAmount = payment.optDouble("due_amount");
            tenancyAssume=DoubleHelper.sub(currencyAmount,paidAmount,2);

        }
        //代金券
        if (com.tzx.thirdpay.bo.DouyinCouponsService.GROUPON_TYPE_DAIJIN.equals( payment.optString("groupon_type"))) {
            couponsPro = SysDictionary.COUPONS_PRO_DEDUCT;

            StringBuilder queryCoupousInfo=new StringBuilder();
            queryCoupousInfo.append("select original_amount,merchant_ticket_amount,pay_amount from pos_bill_payment_coupous_douyin where encrypted_code='");
            queryCoupousInfo.append(number);
            queryCoupousInfo.append("' limit 1");

            List<JSONObject> couponsInfos=paymentDao.query4Json(tenantId,queryCoupousInfo.toString());
            if(CollectionUtils.isNotEmpty(couponsInfos)){

                JSONObject  couponsInfo=couponsInfos.get(0);

                Double originalAmount=AmountUtil.changeF2Y(couponsInfo.optDouble("original_amount",0));
                Double merchantTicketTmount=AmountUtil.changeF2Y(couponsInfo.optDouble("merchant_ticket_amount",0));
                Double payAmount=AmountUtil.changeF2Y(couponsInfo.optDouble("pay_amount",0));

                //虚收 = (market_price-original_amount)+merchant_ticket_amount
                tenancyAssume=DoubleHelper.padd(DoubleHelper.psub(currencyAmount,originalAmount),merchantTicketTmount);
                //实收  =market_price-虚收
                paidAmount=DoubleHelper.psub(currencyAmount,tenancyAssume);
                //平台承担优惠 = =  originalAmount-(payAmount+merchantTicketTmount)
                thirdAssume=DoubleHelper.psub(originalAmount,DoubleHelper.padd(payAmount,merchantTicketTmount));

            }
        }

        String rwidStr="";
        String rwidSingle="";

        if(null!=rwids&&rwids.size()>0){

            rwidSingle= (String) rwids.get(0);

            for(Object rwidObj:rwids){
                rwidStr+=","+rwidObj;
            }
        }


        rwidStr=rwidStr.replaceFirst(",","");

        //插入账单优惠券记录
        PosBillPaymentCoupons paymentCoupon = new PosBillPaymentCoupons(tenantId, storeId, billNum, reportDate, paymentUid, number, currencyAmount, title, currentTime, null, null, null, currencyAmount, count, chanel, couponsPro, CouponsPaymentWayServiceImp.COUPON_TYPE_CUSTOMER);
        if (SysDictionary.COUPONS_PRO_DISH.equals(couponsPro)&&StringUtils.isNotEmpty(rwidSingle)) {
            Integer rwid = Integer.valueOf(rwidSingle);
            JSONObject itemJson = paymentDao.getBillItemForCouponByRwid(tenantId, storeId, billNum, Integer.valueOf(rwidSingle));
            if (null != itemJson && false == itemJson.isEmpty()) {
                paymentCoupon.setPrice(itemJson.optDouble("item_price", 0d));
                paymentCoupon.setItem_id(itemJson.optInt("item_id"));
                paymentCoupon.setItem_num(itemJson.optInt("item_count"));
                paymentCoupon.setItem_unit_id(itemJson.optInt("item_unit_id"));
                paymentCoupon.setRwid(rwid);
                paymentCoupon.setRwids(rwidStr);
            }
        }

        paymentCoupon.setDiscount_money(discountAmount);


        paymentCoupon.setCoupon_buy_price(couponBuyPrice);
        paymentCoupon.setDue(paidAmount);
        paymentCoupon.setTenancy_assume(tenancyAssume);
        paymentDao.insertPosBillPaymentCoupons(tenantId, storeId, paymentCoupon);


        PosBillPayment paymentEntity = new PosBillPayment(tenantId, storeId, reportDate, shiftId, posNum, optNum, billNum, batchNum, tableCode, paymentClass, jzid, paymentName, nameEnglish, amount, currencyAmount, count.intValue(), number, phone, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
        paymentEntity.setRemark(remark);
        paymentEntity.setCount(count.intValue());
        paymentEntity.setPayment_uid(paymentUid);
        paymentEntity.setCoupon_buy_price(paidAmount);
        paymentEntity.setDue(paidAmount);
        paymentEntity.setTenancy_assume(tenancyAssume);
        paymentEntity.setMore_coupon(moreCoupon);
        paymentEntity.setThird_assume(thirdAssume);
        paymentDao.insertPosBillPayment(tenantId, storeId, paymentEntity);

        //拆分实收虚收
        splitPayment(tenantId,billNum,paymentUid,couponBuyPrice,paidAmount,tenancyAssume);

    }


    private void splitPayment(String tenancyId,  String billNum,String paymentUid, Double couponBuyPrice,Double due,Double tenancyAssume) throws Exception {


        List<JSONObject> payments = this.paymentDao.query4Json(tenancyId, "select * from pos_bill_payment where payment_uid ='" + paymentUid + "' and bill_num='" + billNum + "'");

        if (CollectionUtils.isNotEmpty(payments)) {

            if (paymentJzidMap.isEmpty()) {
                List<JSONObject> paymentWays = paymentDao.query4Json(tenancyId, "select id,payment_class,payment_name1 from payment_way where payment_class = '" + PAYMENT_CLASS_DOUYIN_COUPONS_PHANTOM_PAY + "'");
                if (CollectionUtils.isEmpty(paymentWays)) {
                    logger.warn("抖音券虚收款方式不存在");
                    throw SystemException.getInstance(PosErrorCode.NOT_EXIST_JZID);
                }

                for (JSONObject paymentWay : paymentWays) {
                    paymentJzidMap.put(paymentWay.optString("payment_class"), paymentWay);
                }
            }

            List<JSONObject> paymentsPhantoms = new ArrayList<JSONObject>();

            for (JSONObject payment : payments) {

                JSONObject paymentPhantom = null;

                String paymentType = payment.optString("type");

                double oldTenancyAssume = payment.optDouble("tenancy_assume", 0);

                if (oldTenancyAssume == 0) {
                    continue;
                }
                paymentPhantom = JSONObject.fromObject(payment);

                paymentPhantom.put("type", PAYMENT_CLASS_DOUYIN_COUPONS_PHANTOM_PAY);
                paymentPhantom.put("jzid", paymentJzidMap.get(PAYMENT_CLASS_DOUYIN_COUPONS_PHANTOM_PAY).optInt("id"));
                paymentPhantom.put("name", paymentJzidMap.get(PAYMENT_CLASS_DOUYIN_COUPONS_PHANTOM_PAY).optString("payment_name1"));
                paymentPhantom.put("name_english", paymentJzidMap.get(PAYMENT_CLASS_DOUYIN_COUPONS_PHANTOM_PAY).optString("payment_name1"));
                paymentPhantom.put("amount", tenancyAssume);
                paymentPhantom.put("currency_amount", tenancyAssume);
                paymentPhantom.put("coupon_buy_price", 0);
                paymentPhantom.put("due", 0);
                paymentPhantom.put("tenancy_assume", tenancyAssume);

                paymentsPhantoms.add(paymentPhantom);

                payment.put("amount", due);
                payment.put("currency_amount", due);
                payment.put("coupon_buy_price", couponBuyPrice);
                payment.put("due", due);
                payment.put("tenancy_assume", 0);


            }
            paymentDao.insertBatchIgnorCase(tenancyId, "pos_bill_payment", paymentsPhantoms);
            paymentDao.updateBatchIgnorCase(tenancyId, "pos_bill_payment", payments);
        }
    }

    @Override
    public void paymentDebit(String tenancyId, int storeId, String billNum, String orderNum, String chanel, Double paymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData) throws Exception {
    }

    @Override
    public void findPaymentDebit(String tenancyId, int storeId, String billNum, String orderNum, String chanel, Double paymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData) throws Exception {
        JSONObject paramJson = new JSONObject();
        paramJson.put("couponCode", payment.optString("number"));
        paramJson.put("couponType", SysDictionary.PAYMENT_CLASS_MEIDA_COUPONS_PAY);

        Data responseData = douyinCouponsService.couponsQuery(tenancyId, storeId, paramJson);

        if (responseData.isSuccess())
        {
            JSONObject responseJson = JSONObject.fromObject(responseData.getData().get(0));
            if ("已使用".equals(responseJson.optString("couponStatusDesc"))||"已验证".equals(responseJson.optString("couponStatusDesc")))
            {
                payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_COMPLETE);

                // 修改付款状态
                JSONObject updatePaymentJson = new JSONObject();
                updatePaymentJson.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
                updatePaymentJson.put("id", payment.optInt("id"));
                paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", updatePaymentJson);

                resultData.setCode(Constant.CODE_SUCCESS);
                // 插入优惠劵记录表

                Date reportDate = DateUtil.parseDate(payment.optString("report_date"));
                String paymentId = payment.optString("payment_uid");

                String dealName = responseJson.optString("dealTitle");
                String couponCode = responseJson.optString("couponCode");
                Double dealValue = responseJson.optDouble("dealValue");

                List<Object[]> couponList = new ArrayList<Object[]>();
                couponList.add(new Object[]{tenancyId,storeId,billNum,reportDate,paymentId,couponCode,dealValue,dealName,currentTime,null});

                if(couponList.size()>0)
                {
                    paymentDao.insertPaymentCoupons(tenancyId, storeId, couponList);
                }
            }
            else
            {
                payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_FAILURE);
                resultData.setCode(Constant.CODE_PARAM_FAILURE);
                resultData.setMsg(Constant.THIRD_COUPONS_FAILURE);
            }
        }
        else if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
        {
            payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY);
            resultData.setCode(Constant.CODE_CONN_EXCEPTION);
            resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
        }
        else
        {
            payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_FAILURE);
            resultData.setCode(responseData.getCode());
            resultData.setMsg(responseData.getMsg());
        }

        if (Constant.CODE_SUCCESS != resultData.getCode() && Constant.CODE_CONN_EXCEPTION != resultData.getCode())
        {
            // 修改付款状态
            JSONObject updatePaymentJson = new JSONObject();
            updatePaymentJson.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_FAILURE);
            updatePaymentJson.put("id", payment.optInt("id"));
            paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", updatePaymentJson);

            resultData.setCode(PosErrorCode.BILL_PAYMENT_ERROP.getNumber());
            resultData.setMsg(PosErrorCode.BILL_PAYMENT_ERROP.getMessage().replace("{0}", resultData.getMsg()));
        }
    }

    @Override
    public void paymentRefund(String tenancyId, int storeId, String billNum, String oldBillNum, String orderNum, String chanel, Double billPaymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData, String operType) throws Exception {
        paymentCancel(tenancyId,storeId,billNum,orderNum,chanel,billPaymentAmount,payment,currentTime,resultJson,resultData,operType);
    }

    @Override
    public void paymentCancel(String tenancyId, int storeId, String billNum, String orderNum, String chanel, Double billPaymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData, String operType) throws Exception {

        try{
            String tradeNo = payment.optString("out_trade_no");
            String[] params= tradeNo.split("_");
            String verifyId=params[0];
            String certificateId=params[1];

            //次卡次数
            int count=Math.abs(payment.optInt("count",1));

            String paymentUid=payment.optString("payment_uid");

            /*for(int i=1;i<=count;i++){
                if(count>1){
                    paymentUid+="$"+i;
                }
                douyinOpen.cancel(verifyId,certificateId,count,paymentUid);
            }*/

            douyinOpen.cancel(verifyId,certificateId,count,paymentUid);

        }catch (Exception e){
            throw SystemException.getInstance(PosErrorCode.DOUYIN_COUPOUS_ERROR).set("{0}", "撤销验券").set("{1}",e.getMessage());
        }
    }

    @Override
    public void paymentRegain(String tenancyId, int storeId, String billNum, String orderNum, String chanel, Double billPaymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData, String operType) throws Exception {
        paymentCancel(tenancyId,storeId,billNum,orderNum,chanel,billPaymentAmount,payment,currentTime,resultJson,resultData,operType);
    }

    @Override
    public void paymentDebitResult(String tenancyId, int storeId, String billNum, JSONObject paymentJson) throws Exception {
        // TODO 自动生成的方法存根
        JSONObject updatePaymentJson = new JSONObject();
        updatePaymentJson.put("bill_code", paymentJson.optString("bill_code"));
        updatePaymentJson.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
        updatePaymentJson.put("id", paymentJson.optInt("id"));
        paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", updatePaymentJson);
    }

}
