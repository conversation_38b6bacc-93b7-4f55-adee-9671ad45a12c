package com.tzx.member.acewill.bo;

import java.util.Date;

import com.tzx.framework.common.entity.Data;
import net.sf.json.JSONObject;

public interface CustomerService {
    String NAME = "com.tzx.member.acewill.bo.imp.CustomerServiceImp";

	/**
	 * 生成订单号
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param paramJson
	 * @return
	 * @throws Exception
	 */
	public String createThirdBillCode(String tenancyId, Integer storeId, JSONObject paramJson) throws Exception;

    /**
     * 修改会员信息
     *
     * @param requestData
     * @return
     * @throws Exception
     */
    public Data editAcewillCustomerUserInfo(Data requestData) throws Exception;


    /**
     * 获取交易/储值支付方式
     *
     * @param requestData
     * @return
     * @throws Exception
     */
    public Data findAcewillDealPayType(Data requestData) throws Exception;

    /**
     * 查看门店储值规则设置
     *
     * @param requestData
     * @return
     * @throws Exception
     */
    public Data findAcewillChargeRule(Data requestData) throws Exception;


    /**
     * 获取收银员列表
     *
     * @param requestData
     * @return
     * @throws Exception
     */
    public Data findAcewillCashierList(Data requestData) throws Exception;

    /**
     * 获取门店员工列表
     *
     * @param requestData
     * @return
     * @throws Exception
     */
    public Data findAcewillEmployeeList(Data requestData) throws Exception;

    /**
     * 指定会员储值记录列表
     *
     * @param requestData
     * @return
     * @throws Exception
     */
    public Data findAcewillChargeUser(Data requestData) throws Exception;

    /**
     * 指定会员消费记录列表
     *
     * @param requestData
     * @return
     * @throws Exception
     */
    public Data findAcewillConsumeUser(Data requestData) throws Exception;


    /**
     * 查看积分设置
     *
     * @param requestData
     * @return
     * @throws Exception
     */
    public Data findAcewillCreditRule(Data requestData) throws Exception;


    /**
     * 积分换礼
     *
     * @param requestData
     * @return
     * @throws Exception
     */
    public Data creditExchangeGifts(Data requestData) throws Exception;

    /**
     * 会员等级查询
     *
     * @param requestData
     * @return
     * @throws Exception
     */
    public Data findAcewillCustomerGrade(Data requestData) throws Exception;


    /**
     * 验证实体卡
     * @param requestData
     * @return
     * @throws Exception
     */
    public Data checkAcewillCustomerCard(Data requestData) throws Exception;

    /**
     *微生会制卡
     * @param requestData
     * @return
     * @throws Exception
     */
    public Data makeAcewillCustomerCard(Data requestData) throws Exception;


    /**
     * 微生会员开卡
     * @param requestData
     * @return
     * @throws Exception
     */
    public Data openAcewillCustomerCard(Data requestData) throws Exception;
    
    /** 打印卡激活小票
     * @param tenantId
     * @param storeId
     * @param bizId
     * @param cardCode
     * @param posNum
     * @param optNum
     * @throws Exception
     */
    public void printActivatedAcewillCustomerCard(String tenantId, Integer storeId, String bizId, String cardCode, String posNum, String optNum) throws Exception;
    
	/** 记录操作日志
	 * @param tenantId
	 * @param organId
	 * @param posNum
	 * @param optNum
	 * @param shiftId
	 * @param reportDate
	 * @param title
	 * @param content
	 * @param oldstate
	 * @param newstate
	 */
	public void savePosLog(String tenantId, Integer organId, String posNum, String optNum, String optName, Integer shiftId, Date reportDate, String title, String content, String oldstate, String newstate);
}
