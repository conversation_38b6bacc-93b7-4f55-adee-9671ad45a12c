package com.tzx.member.crm.bo;


import com.tzx.framework.common.entity.Data;
import net.sf.json.JSONObject;

import java.util.List;

/**
 * @Description 会员标签业务处理
 * <AUTHOR>
 * @Date 2018-09-25 10:15
 */
public interface CustomerSignService {
    String NAME = "com.tzx.member.crm.bo.imp.CustomerSignServiceImp";

    /**
     * 添加会员标签
     * @param tenancyId
     * @param param
     * @throws Exception
     */
    Data addCustomerSign(String tenancyId, Data param) throws Exception;

    /**
     * 查询会员标签
     * @param tenancyId
     * @param param
     * @return
     * @throws Exception
     */
    Data getCustomerSign(String tenancyId, Data param) throws Exception;

    /**
     * 标签点赞
     * @param tenancyId
     * @param param
     * @throws Exception
     */
    Data praiseSign(String tenancyId, Data param) throws Exception;
}
