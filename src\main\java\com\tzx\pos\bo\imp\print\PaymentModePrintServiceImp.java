package com.tzx.pos.bo.imp.print;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.util.Comet4jUtil;
import com.tzx.framework.common.util.Tools;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.bo.PosPrintModeService;
import com.tzx.pos.bo.imp.PosBaseServiceImp;
import com.tzx.pos.po.springjdbc.dao.PosDao;

/**
 * POS预打单
 * POS结账单
 * 
 * <AUTHOR>
 *
 */

@Service("paymentModePrintServiceImp")
public class PaymentModePrintServiceImp extends PosBaseServiceImp implements PosPrintModeService
{
	@Resource(name = PosDao.NAME)
	private PosDao				posDao;
	private static final Logger	logger	= Logger.getLogger(PaymentModePrintServiceImp.class);
	
	@Override
	public void posPrint(String tenancyId, int storeId, JSONObject para, String printMode) throws Exception
	{
		try
		{
			// 得到打印机id，是否区域，站点ID(新加)，打印机名称，IP地址，备打名称，备打地址，模板ID(hq_printer_model)
			List<JSONObject> printerList = posDao.getPrinter(tenancyId, storeId, printMode);

			// 结账单需要打开钱箱（根据支付方式判断是否需要开钱箱）
            String billNum = para.optString("bill_num");
            boolean payWayIsOpen = posDao.payWayIsOpenCashBox(tenancyId, storeId, billNum, printMode);

			if(printerList.size() > 0)
			{
//				logger.info(" 模板【" + printMode + "】绑定的打印机：" + printerList.toString());
				for(int i = 0; i < printerList.size(); i++)
				{
					JSONObject printerJo = printerList.get(i);
					int printerId = printerJo.optInt("printer_id");
					if(printerId == 0)
					{
						continue;
					}
					para.put("printer_id", printerId);

                    int isOpenCashbox = 0; // 是否开钱箱
                    String isSite = printerJo.optString("is_site");
                    if(payWayIsOpen && (1 == printerJo.optInt("is_open_cashbox")) && "1".equals(isSite))
                    {
                        isOpenCashbox = 1;
                    }


                    // IP地址
                    String ipCom = printerJo.optString("ip_com");

					int isArea = printerJo.optInt("is_area");
					int isBeginArea = printerJo.optInt("is_begin_area");
					// 区域打印并且模板启用区域打印
					if(isArea == 1 && isBeginArea == 1)
					{
						// 判断桌位是否允许区域打印
						JSONObject printerTableJo = posDao.getPrinterTable(tenancyId, storeId, para);
						String areaId = printerTableJo.optString("area_id");
						String tableCode = printerTableJo.optString("table_code");
						if(Tools.isNullOrEmpty(areaId))
						{
							logger.info(" 分单失败：打印机【" + printerId + "】没有绑定桌位:【" + tableCode + "】");
							continue;
						}
						else
						{
							printerJo.put("area_id", areaId);
						}
					}
					else
					{
						// 判断该打印机是否绑定此POS机
						JSONObject deviceJo = posDao.getDeviceFromPrinterModel(tenancyId, storeId, para, printMode);
						String devices_code = deviceJo.optString("devices_code");
						String pos_num = para.optString("pos_num");
						if(Tools.isNullOrEmpty(pos_num) || Tools.isNullOrEmpty(devices_code) || !pos_num.equals(devices_code))
						{
							logger.info(" 分单失败：打印机【" + printerId + "】没有绑定POS【" + pos_num + "】。绑定的POS为【" + devices_code + "】");
							continue;
						}
					}
					
					List<JSONObject> printParamList = new ArrayList<JSONObject>();
					try
					{
						printParamList = posDao.getPrintParam(tenancyId, storeId, printMode, printerJo);
					}
					catch (Exception e)
					{
						logger.error("分单失败：打印机【" + printerId + "】获取打印参数失败 " , e);
					}
					
					if(printParamList.size() > 0)
					{
						for(JSONObject printParam : printParamList)
						{
							if(printParam.optString("param_pairs").isEmpty())
							{
								logger.info(" 分单失败：打印机【" + printerId + "】的打印模板【" + printMode + "】未设定打印参数");
								continue;
							}
							JSONObject params = new JSONObject();
							JSONObject paramPairs = JSONObject.fromObject(printParam.opt("param_pairs"));
							
							// 获取打印参数
							JSONArray rows = paramPairs.optJSONArray("rows");
							Object value = null;
							String paramName = null;
							for (Object row : rows)
							{
								JSONObject rowJo = JSONObject.fromObject(row);
								
								paramName = rowJo.optString("paramName");
								value = para.get(paramName);
								if(Tools.isNullOrEmpty(value))
								{
									value = rowJo.opt("value");
								}
								params.put(paramName, value);
							}

                            params.put("is_open_cashbox", isOpenCashbox);

							// 生成打印任务
							try
							{
								posDao.insertPrintTask(tenancyId, storeId, printerJo, params, printMode);
								 
								if (SysDictionary.PRINT_CODE_1101.equals(printMode))
								{
									if (para.containsKey("bill_num"))
									{
										String uPrintCount = new String("update pos_bill set print_count =(coalesce(print_count,0)+1) where bill_num = ? and store_id = ? and tenancy_id=?");
										posDao.update(uPrintCount, new Object[]
										{ para.optString("bill_num"), storeId,tenancyId });
									}
								}
								
							}
							catch (Exception e)
							{
								logger.error("分单失败：打印机【" + printerId + "】生成打印任务失败" , e);
							}
							try
							{
								// 发出打印通知
								Data cjData = new Data();
								cjData.setType(Type.PRINT);
								cjData.setOper(Oper.print);
                                JSONObject printJson = new JSONObject();
                                printJson.put("print_address", ipCom);
                                List<JSONObject> list = new ArrayList<>();
                                list.add(printJson);
                                cjData.setData(list);

								Comet4jUtil.sendMessage2All(Comet4jUtil.POS_PRINTER, JSONObject.fromObject(cjData).toString());
//								logger.info("打印分单成功：打印机【" + printerId + "】" + "，打印模板【" + printMode + "】");
							}
							catch (Exception e)
							{
								logger.error("分单失败：打印机【" + printerId + "】服务器连接异常", e);
							}
						}
					}
					else
					{
						logger.info("分单失败：打印机【" + printerId + "】未找到打印模板【" + printMode + "】");
						continue;
					}
				}
			}
			else
			{
				logger.info("分单失败：模板【" + printMode + "】没有设定打印机");
			}
		}
		catch(Exception e)
		{
			logger.error("分单失败：其他原因导致分单失败" , e);
		}
	}
}
