package com.tzx.base.listener;

import java.io.File;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import com.tzx.base.common.util.PosPropertyUtil;
import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.util.HttpUtil;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.TzxPropertiesUtil;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.pos.base.controller.DataUploadRunnable;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.bo.PosDishService;
import com.tzx.pos.po.springjdbc.dao.PosBillDao;
import com.tzx.pos.po.springjdbc.dao.PosPaymentDao;
import com.tzx.pos.po.springjdbc.dao.imp.PosBillDaoImp;
import com.tzx.pos.po.springjdbc.dao.imp.PosPaymentDaoImp;

/**
 * Created by MrChen on 2017-07-06.
 */
public class PosQuickPassTask implements  Runnable {

    private Logger logger = Logger.getLogger(PosQuickPassTask.class);
    private final String	postUrl	= "/api/billClear";
    @Override
    public void run() {
        try {
            Map<String, String> systemMap = Constant.getSystemMap();
            String tenent_id= systemMap.get("tenent_id");
            String store_id= systemMap.get("store_id");
            DBContextHolder.setTenancyid(tenent_id);
            PosPaymentDaoImp posPaymentServiceImp = (PosPaymentDaoImp) SpringConext.getApplicationContext().getBean(PosPaymentDao.NAME);
            String para_value= posPaymentServiceImp.getSysParameter(tenent_id,Integer.parseInt(store_id), Constant.IFUP_QUICK_PAY);
           //秒付开关
            if("0".equals(para_value)||"".equals(para_value)){
                return ;
            }
            logger.debug("10分钟执行一次，开始更新秒付数据");
            uploadData(tenent_id,store_id);
            logger.debug("10分钟执行一次，更新秒付数据完成");
        } catch (Exception e) {
            logger.error("10分钟执行一次，更新秒付数据异常",e);
        }
    }
    //更新数据业务
    public void uploadData(String tenent_id,String store_id){
        try {
            Calendar calendar1 = Calendar.getInstance();
            /* HOUR_OF_DAY 指示一天中的小时 */
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            calendar1.set(Calendar.HOUR_OF_DAY, calendar1.get(Calendar.HOUR_OF_DAY) - 6);
            Timestamp timestamp1=DateUtil.formatTimestamp(df.format(calendar1.getTime()));
            PosBillDaoImp posBillDaoImp = (PosBillDaoImp) SpringConext.getApplicationContext().getBean(PosBillDao.NAME);
            List<JSONObject> list= posBillDaoImp.findPosBill(timestamp1,DateUtil.getNowTimestamp());//获取6小时之前到现在未结账单
            String tableCodeStr="";
            for(JSONObject jsonObject:list){
                tableCodeStr+="'"+jsonObject.getString("table_code")+"',";
                String bill_num=jsonObject.getString("bill_num");
                PosDishService posDishService = (PosDishService) SpringConext.getApplicationContext().getBean(PosDishService.NAME);
                posDishService.upload(tenent_id,store_id,"","","",bill_num);
            }
            List <JSONObject>tableCodeLst=null;
            if(tableCodeStr.length()>0){
                //查到已经关台的台号
                tableCodeLst=posBillDaoImp.findTableCode(tableCodeStr.substring(0,tableCodeStr.length() - 1));
            }else{
                tableCodeLst=posBillDaoImp.findTableCode(null);
            }

            //把已经关台的redis数据清除
            this.sendClearTabelCode(tenent_id,store_id,tableCodeLst);
        } catch (Exception e) {
            logger.error("uploadData更新秒付数据异常",e);
        }

    }
    public void sendClearTabelCode(String tenent_id,String store_id,List <JSONObject>tableCodeLst){
        List tableCodeStr=this.getListStringByObject(tableCodeLst);
//        Map<String, String> systemMap = Constant.getSystemMap();
        String postUrlStr=PosPropertyUtil.getMsg("secondpay.dataupload.url");
//			String postUrlStr="";
        if (StringUtils.isEmpty(postUrlStr))
        {
            logger.info("配置文件获取请求地址参数为空，切换为CLASS方法获取");
            String msgFilePath=DataUploadRunnable.class.getResource("/").getPath()+ File.separator + "pos"+ File.separator +"pos_config.properties";
            File msgFile = new File(msgFilePath);
            Properties msgProperties = TzxPropertiesUtil.load(msgFile);
            postUrlStr=msgProperties.getProperty("secondpay.dataupload.url");
            logger.info("切换后地址为："+postUrlStr);
        }
        JSONObject clearBill=new JSONObject();
        clearBill.put("tenantId",tenent_id);
        clearBill.put("shopId",store_id);
        clearBill.put("tableNameList",tableCodeStr);
//        String data1="tenantId:"+tenent_id+",shopId:"+store_id+",tableNameList"+tableCodeStr.toString();
        logger.debug("清除的桌台号为:>>>>>>"+clearBill.toString());
        logger.debug("数据上传地址为post_url:>>>>>>"+postUrlStr);
        HttpUtil.sendPostRequest(postUrlStr+postUrl,clearBill.toString());
        logger.debug("清除桌台完成");
    }
    public List <String> getListStringByObject(List <JSONObject>list){
        List<String> list1=new ArrayList();
        for(JSONObject jsonObject:list){
            list1.add(jsonObject.getString("table_code"));
        }
        return list1;
    }
}
