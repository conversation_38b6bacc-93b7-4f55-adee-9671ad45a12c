package com.tzx.base.entity;

import java.sql.Timestamp;
import java.util.Date;

/**
 * 账单做法表映射实体
 * 
 * <AUTHOR>
 * <AUTHOR>
 */
public class PosMethodItem
{
	private String		tenancy_id;
	private Integer		store_id;
	private Integer		id;
	private String		bill_num;
	private Date		report_date;
	private String		pos_num;
	private Integer		rwid;
	private Integer		item_id;
	private Integer		item_num;
	private String		type;
	private Integer		zfkw_id;
	private String		zfkw_name;
	private Double		amount;
	private String		remark;
	private Timestamp	last_updatetime;
	private Integer		print_id;
	private Integer		upload_tag;

	public String getTenancy_id()
	{
		return tenancy_id;
	}

	public void setTenancy_id(String tenancy_id)
	{
		this.tenancy_id = tenancy_id;
	}

	public Integer getStore_id()
	{
		return store_id;
	}

	public void setStore_id(Integer store_id)
	{
		this.store_id = store_id;
	}

	public Integer getId()
	{
		return id;
	}

	public void setId(Integer id)
	{
		this.id = id;
	}

	public String getBill_num()
	{
		return bill_num;
	}

	public void setBill_num(String bill_num)
	{
		this.bill_num = bill_num;
	}

	public Date getReport_date()
	{
		return report_date;
	}

	public void setReport_date(Date report_date)
	{
		this.report_date = report_date;
	}

	public String getPos_num()
	{
		return pos_num;
	}

	public void setPos_num(String pos_num)
	{
		this.pos_num = pos_num;
	}

	public Integer getRwid()
	{
		return rwid;
	}

	public void setRwid(Integer rwid)
	{
		this.rwid = rwid;
	}

	public Integer getItem_id()
	{
		return item_id;
	}

	public void setItem_id(Integer item_id)
	{
		this.item_id = item_id;
	}

	public Integer getItem_num()
	{
		return item_num;
	}

	public void setItem_num(Integer item_num)
	{
		this.item_num = item_num;
	}

	public String getType()
	{
		return type;
	}

	public void setType(String type)
	{
		this.type = type;
	}

	public Integer getZfkw_id()
	{
		return zfkw_id;
	}

	public void setZfkw_id(Integer zfkw_id)
	{
		this.zfkw_id = zfkw_id;
	}

	public String getZfkw_name()
	{
		return zfkw_name;
	}

	public void setZfkw_name(String zfkw_name)
	{
		this.zfkw_name = zfkw_name;
	}

	public Double getAmount()
	{
		return amount;
	}

	public void setAmount(Double amount)
	{
		this.amount = amount;
	}

	public String getRemark()
	{
		return remark;
	}

	public void setRemark(String remark)
	{
		this.remark = remark;
	}

	public Timestamp getLast_updatetime()
	{
		return last_updatetime;
	}

	public void setLast_updatetime(Timestamp last_updatetime)
	{
		this.last_updatetime = last_updatetime;
	}

	public Integer getPrint_id()
	{
		return print_id;
	}

	public void setPrint_id(Integer print_id)
	{
		this.print_id = print_id;
	}

	public Integer getUpload_tag()
	{
		return upload_tag;
	}

	public void setUpload_tag(Integer upload_tag)
	{
		this.upload_tag = upload_tag;
	}

}
