package com.tzx.framework.common.sms;

import java.util.List;
import java.util.PropertyResourceBundle;
import java.util.ResourceBundle;

import cn.emay.sdk.client.api.MO;

/**
 * <AUTHOR> E-mail: email地址
 * @version 创建时间：Jun 28, 2011 11:34:03 AM
 * 
 */

public class TzxSMSService {
	/**
	 * 软件注销
	 * return 
	 * 0--->注销成功
	 * 999--->操作频繁
	 * 305---->服务器端返回错误数据，返回的数据必须是数字
	 */
	public static boolean logOut() {
		int flag = TzxSMSClient.getClient().logout();
		if (flag == 0) {
			System.out.println("注销成功");
			return true;
		} else {
			System.out.println("注销失败");
			return false;
		}
	} 
	
	/**
	 * 软件序列号注册、或则说是激活、软件序列号首次使用必须激活
	 * registEx(String serialpass)
	 * 1、serialpass 软件序列号密码、密码长度为6位的数字字符串、软件序列号和密码请通过亿美销售人员获取
	 * 0--->成功
	 * 999--->操作频繁
	 * 305---->服务器端返回错误数据，返回的数据必须是数字
	 */
	public static boolean registEx(){
		ResourceBundle bundle=PropertyResourceBundle.getBundle("config");
		String serialpass=bundle.getString("password");
		int flag=TzxSMSClient.getClient().registEx(serialpass);
		if (flag == 0) {
			System.out.println("注册成功");
			return true;
		} else {
			System.out.println("注册失败");
			return false;
		}
	}
	/**
	 * 发送短信、可以发送定时和即时短信
	 * sendSMS(String[] mobiles,String smsContent, String addSerial, int smsPriority)
	 * 1、mobiles 手机数组长度不能超过1000
	 * 2、smsContent 最多500个汉字或1000个纯英文、请客户不要自行拆分短信内容以免造成混乱、亿美短信平台会根据实际通道自动拆分、计费以实际拆分条数为准、亿美推荐短信长度70字以内 
	 * 3、addSerial 附加码(长度小于15的字符串) 用户可通过附加码自定义短信类别,或添加自定义主叫号码( 联系亿美索取主叫号码列表)
	 * 4、优先级范围1~5，数值越高优先级越高(相对于同一序列号)
	 * 5、其它短信发送请参考使用手册自己尝试使用
	 */
	public static boolean sendSMS(String[] mobiles,String smsContent,int smsPriority){
		int flag =TzxSMSClient.getClient().sendSMS(mobiles,smsContent,smsPriority);
	  //int flag =SDKClient.getClient().sendSMS(new String[] {"13436506141"}, "同步内容","110",3);//带扩展码
	  //int flag =SDKClient.getClient().sendSMSEx(new String[] {"13436506141"}, "同步内容","110","UTF-8",3);//带扩展码和字符集
		if (flag == 0) {
			System.out.println("短信发送成功");
			return true;
		} else {
			System.out.println("flag =="+flag);
			System.out.println("短信发送失败");
			return false;
		}
	}
	/**
	 * 发送定时短信
	 * sendScheduledSMSEx(String[] mobiles, String smsContent,String sendTime,String srcCharset)
	 * 1、mobiles 手机数组长度不能超过1000
	 * 2、smsContent 最多500个汉字或1000个纯英文、请客户不要自行拆分短信内容以免造成混乱、亿美短信平台会根据实际通道自动拆分、计费以实际拆分条数为准、亿美推荐短信长度70字以内
	 * 3、sendTime 定时短信发送时间 定时时间格式为：年年年年月月日日时时分分秒秒，例如20090801123030 表示2009年8月1日12点30分30秒该条短信会发送到用户手机 
	 * 4、srcCharset 字符编码，默认为"GBK"
	 * 5、其它定时短信发送请参考使用手册自己尝试使用
	 */
	public static boolean sendScheduledSMS(String[] mobiles, String smsContent,String sendTime){
		int flag=TzxSMSClient.getClient().sendScheduledSMS(mobiles, smsContent, sendTime);  
		//TzxSMSClient.getClient().sendScheduledSMS(mobiles, smsContent, sendTime, addSerial, srcCharset)
		if (flag == 0) {
			System.out.println("定时短信发送成功");
			return true;
		} else {
			System.out.println("定时短信发送失败");
			return false;
		}
	}
	/**
	 * 软件序列号充值、如果软件序列号金额不足、那么可以调用该方法给序列号充值
	 * chargeUp(String cardNo, String cardPass)
	 * 1、cardNo 充值卡卡号
	 * 2、cardPass 充值卡密码
	 * 3、充值卡卡号和密码请联系亿美销售人员获得
	 */
	public static boolean chargeUp(String cardNo,String cardPass){
		int flag=TzxSMSClient.getClient().chargeUp(cardNo, cardPass);
		if (flag == 0) {
			System.out.println("充值成功");
			return true;
		} else {
			System.out.println("充值失败");
			return false;
		}
	}
	/**
	 * 企业详细信息注册
	 * registDetailInfo(String eName, String linkMan, String phoneNum,String mobile, String email, String fax, String address,String postcode)
	 * 1、Name 企业名称(最多60字节)
	 * 2、linkMan 联系人姓名(最多20字节)
	 * 3、phoneNum 联系电话(最多20字节)
	 * 4、mobile 联系手机(最多15字节)
	 * 5、email 电子邮件(最多60字节)
	 * 6、fax 联系传真(最多20字节)
	 * 7、address 公司地址(最多60字节)
	 * 8、postcode 邮政编码(最多6字节)
	 * 9、以上参数信息都必须填写、每个参数都不能为空
	 */
	public static boolean registDetailInfo(String name,String linkMan,String phoneNum,String mobile,String email,String fax,String address,String postcode){
		int flag=TzxSMSClient.getClient().registDetailInfo(name, linkMan, phoneNum, mobile, email, fax, address, postcode);
		if (flag == 0) {
			System.out.println("注册成功");
			return true;
		}else if(flag == -1){
			System.out.println("企业信息不符合要求");
			return false;
		}else if(flag == 307){
			System.out.println("目标手机号码不正确");
			return false;
		}else{
			System.out.println("操作频繁");
			return false;
		}
	}
	/**
	 * 修改软件序列号密码、注意修改软件序列号密码以后不需要重新注册(激活)
	 * serialPwdUpd(String serialPwd, String serialPwdNew)
	 * 1、serialPwd 旧密码
	 * 2、serialPwdNew 新密码、长度为6位的数字字符串
	 */
	public static boolean serialPwdUpd(String serialPwd,String serialPwdNew){
		int flag=TzxSMSClient.getClient().serialPwdUpd(serialPwd, serialPwdNew);
		if (flag == 0) {
			System.out.println("密码修改成功");
			return true;
		}else if(flag == -1){
			System.out.println("企业信息不符合要求");
			return false;
		}else if(flag == 307){
			System.out.println("目标手机号码不正确");
			return false;
		}else{
			System.out.println("操作频繁");
			return false;
		}
	}
	/**
	 * 获取软件序列号的余额, 
	 */
	public static double getBalance(){
		double a=0;
		try {
			a = TzxSMSClient.getClient().getBalance();
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return a;
	}
	/**
	 * 获取发送一条短信所需要的费用
	 * @return 
	 */
	public static double getEachFee(){
		double a=TzxSMSClient.getClient().getEachFee();
		return a;
	}
	/**
	 * 1、从EUCP平台接收手机用户上行的短信
	 * 2、返回值list中的每个元素为一个Mo对象
	 * 4、Mo具体数据结构参考使用手册
	 * mo.getSmsContent()  短信内容
	 * mo.getChannelnumber()  通道号
	 * mo.getMobileNumber()  手机号
	 * mo.getAddSerialRev()  接收者附加号码 （只有当接收者为满意通网络版时可能存在此项，单机版获得此项为空）
	 * mo.getAddSerial()  发送者附加号码  （只有当发送者为满意通网络版时存在此项）
	 */
	public static List<MO> getMO(){
		List<MO> a=null;
		try {
			a = TzxSMSClient.getClient().getMO();
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return a;
	}
	/**
	 * 注册转接功能是指当用户直接回复我们发送的短信时、EUCP平台会把用户的回复信息直接转发到我们注册转接的手机号上、
	 * 最多可以注册10个手机号码或上行服务代码
	 * setMOForward(String forwardMobile)
	 * 1、forwardMobile 目标手机号码，有且只能有一个手机号码
	 * 2、其中还有一个每次可以设置多个转接号码，自己可以结合使用手册尝试使用
	 */
	public static boolean SetMOForward(String forwardMobile){
		int flag=TzxSMSClient.getClient().setMOForward(forwardMobile);
		if (flag == 0) {
			System.out.println("注册转接成功");
			return true;
		}else if(flag == 305){
			System.out.println("服务器返回数据错误，返回的数据不能转换成数字");
			return false;
		}else if(flag == 307){
			System.out.println("目标手机号码不正确");
			return false;
		}else{
			System.out.println("操作频繁");
			return false;
		}
	}
	
	/**
	 * 取消转发功能，即用户的上行短信不会再直接转发到我们注册转接功能所指定的手机号码中
	 */
	public static boolean cancelMOForward(){
		int flag=TzxSMSClient.getClient().cancelMOForward();
		if (flag == 0) {
			System.out.println("注册转接成功");
			return true;
		}else if(flag == 305){
			System.out.println("服务器返回数据错误，返回的数据不能转换成数字");
			return false;
		}else{
			System.out.println("操作频繁");
			return false;
		}
	}
	/**
	 * 关闭通道
	 */
	public static void closeChannel(){
		//TzxSMSClient.getClient().closeChannel();
	}
//	public static void main(String[] args){
//		TzxSMSService.registEx();
//		//TzxSMSService.logOut();
//		//TzxSMSService.sendSMS(new String[]{"15110094559"}, "测试短信", "", 3);
//	}
}
