package com.tzx.thirdpay.bo;

import com.tzx.framework.common.entity.Data;
import net.sf.json.JSONObject;

import java.util.List;

public interface DouyinCouponsService {
    String NAME = "com.tzx.thirdpay.bo.imp.DouyinCouponsServiceImp";

//   团购类型（type=1团餐券; type=2代金券）
    String GROUPON_TYPE_TUANCAN="1";
    String GROUPON_TYPE_DAIJIN="2";
    //次卡
    String GROUPON_TYPE_CIKA="3";

    void couponsPrepare(String tenancyId, int storeId, JSONObject paramJson, Data result) throws Exception;

    void couponsCheck(String tenancyId, int storeId, JSONObject paramJson,Data result) throws Exception;

    void couponsConsume(String tenancyId, int storeId, String token, List<String> encryptedCodes) throws Exception;

    Data couponsCancel(String tenancyId, int storeId, JSONObject paramJson) throws Exception;

    Data couponsQuery(String tenancyId, int storeId, JSONObject paramJson) throws Exception;

}
