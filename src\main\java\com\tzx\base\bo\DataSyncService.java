/**
  * @Description: TODO
  * @author: <PERSON><PERSON><PERSON><PERSON>
  * @version: 1.0
  * @Date: 2018年4月3日
*/
package com.tzx.base.bo;

import java.util.HashMap;

import net.lingala.zip4j.core.ZipFile;

/**
  * @Description: TODO
  * @param:
  * @author: z<PERSON><PERSON><PERSON>
  * @version: 1.0
  * @Date: 2018年4月3日
*/
public interface DataSyncService {

	 String	NAME	= "com.tzx.base.bo.imp.DataSyncServiceImpl";

	/**
	  * @Description: 下发
	  * @Title:BaseDataSyncFromSaas
	  * @param:@param zipFile
	  * @return: void
	  * @author: zhouquan
	  * @version: 1.0
	  * @Date: 2018年4月3日
	*/
	public boolean BaseDataSyncFromSaas(ZipFile zipFile,String baseFilePath,String downTableCode,String tables,HashMap<String,String> recordCountMap);
}
