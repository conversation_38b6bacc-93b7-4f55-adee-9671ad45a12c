package com.tzx.pos.service.notify;

import com.tzx.framework.common.constant.Type;
import com.tzx.pos.bo.imp.PosServiceImp;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR> on 2019-01-11
 */
public class SystemParameterUpdateNotifyOberver extends ClientNotifyOberver {
    private static final Logger logger= LoggerFactory.getLogger(SystemParameterUpdateNotifyOberver.class);
    @Override
    public void doNotify(Type arg) {
        PosServiceImp.COUNTDOWN_TIME=0;
        logger.info("系统参数已更新");
    }
}
