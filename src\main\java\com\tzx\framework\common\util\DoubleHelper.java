package com.tzx.framework.common.util;

import java.math.BigDecimal;

public class DoubleHelper {
	/**
	 * 保留2位小数位
	 */
	public final static int	SCALE_2				= 2;

	/**
	 * 提供精确的加法运算。
	 * 
	 * @param v1
	 *            被加数
	 * @param v2
	 *            加数
	 * @return 两个参数的和
	 */
	public static Double add(Double v1, Double v2, Integer scale) {
		BigDecimal b1 = new BigDecimal(Double.toString(v1));
		BigDecimal b2 = new BigDecimal(Double.toString(v2));
		return round(b1.add(b2).doubleValue(), scale);
	}

	/**
	 * 提供精确的减法运算。
	 * 
	 * @param v1
	 *            被减数
	 * @param v2
	 *            减数
	 * @return 两个参数的差
	 */
	public static Double sub(Double v1, Double v2, Integer scale) {
		BigDecimal b1 = new BigDecimal(Double.toString(v1));
		BigDecimal b2 = new BigDecimal(Double.toString(v2));
		return round(b1.subtract(b2).doubleValue(), scale);
	}

	/**
	 * 提供精确的乘法运算。
	 * 
	 * @param v1
	 *            被乘数
	 * @param v2
	 *            乘数
	 * @return 两个参数的积
	 */
	public static Double mul(Double v1, Double v2, Integer scale) {
		BigDecimal b1 = new BigDecimal(Double.toString(v1));
		BigDecimal b2 = new BigDecimal(Double.toString(v2));
		return round(b1.multiply(b2).doubleValue(),scale);
	}

	/**
	 * 提供（相对）精确的除法运算。当发生除不尽的情况时，由scale参数指 定精度，以后的数字四舍五入。
	 * 
	 * @param v1
	 *            被除数
	 * @param v2
	 *            除数
	 * @param scale
	 *            表示表示需要精确到小数点以后几位。
	 * @return 两个参数的商
	 */
	public static Double div(Double v1, Double v2, Integer scale) {
		if (scale < 0) {
			throw new IllegalArgumentException("The scale must be a positive Integer or zero");
		}
		BigDecimal b1 = new BigDecimal(Double.toString(v1));
		BigDecimal b2 = new BigDecimal(Double.toString(v2));
		return b2.doubleValue()==0?0.00:b1.divide(b2, scale, BigDecimal.ROUND_HALF_UP).doubleValue();
	}

	/**
	 * 提供精确的小数位四舍五入处理。
	 * 
	 * @param v
	 *            需要四舍五入的数字
	 * @param scale
	 *            小数点后保留几位
	 * @return 四舍五入后的结果
	 */
	public static Double round(Double v, Integer scale) {
		if (scale < 0) {
			throw new IllegalArgumentException("The scale must be a positive Integer or zero");
		}
		BigDecimal rs = new BigDecimal(Double.toString(v));
		return rs.setScale(scale, BigDecimal.ROUND_HALF_UP).doubleValue();
	}
	
	/** 提供精确的小数位全舍取整处理。
	 * @param v
	 * @param scale
	 * @return
	 */
	public static Double roundDown(Double v, Integer scale) {
		if (scale < 0) {
			throw new IllegalArgumentException("The scale must be a positive Integer or zero");
		}
		BigDecimal rs = new BigDecimal(Double.toString(v));
		return rs.setScale(scale, BigDecimal.ROUND_FLOOR).doubleValue();
	}
	
	/** 提供精确的小数位进1取整处理。
	 * @param v       需要四舍五入的数字
	 * @param scale   小数点后保留几位
	 * @return        四舍五入后的结果
	 */
	public static Double roundUp(Double v, Integer scale) {
		if (scale < 0) {
			throw new IllegalArgumentException("The scale must be a positive Integer or zero");
		}
		BigDecimal rs = new BigDecimal(Double.toString(v));
		return rs.setScale(scale, BigDecimal.ROUND_CEILING).doubleValue();
	}

	/**
	 * 价格相加 : priceAdd
	 *
	 * @add xiaofeng 2010-12-10
	 * @param v1
	 * @param v2
	 * @return
	 */
	public static Double padd(Double v1, Double v2)
	{
		return add(CommonUtil.ta(v1), CommonUtil.ta(v2), SCALE_2);
	}

	/**
	 * 价格相减 : priceSub
	 *
	 * @add xiaofeng 2010-12-10
	 * @param v1
	 * @param v2
	 * @return
	 */
	public static Double psub(Double v1, Double v2)
	{
		return sub(CommonUtil.ta(v1), CommonUtil.ta(v2), SCALE_2);
	}

	/**
	 * 价格相乘 : priceMul
	 *
	 * @add xiaofeng 2010-12-10
	 * @param v1
	 * @param v2
	 * @return
	 */
	public static Double pmui(Double v1, Double v2)
	{
		return mul(CommonUtil.ta(v1), CommonUtil.ta(v2), SCALE_2);
	}

	/**
	 * 价格相除 : priceDiv
	 *
	 * @add xiaofeng 2010-12-10
	 * @param v1
	 * @param v2
	 * @return
	 */
	public static Double pdiv(Double v1, Double v2)
	{
		return div(CommonUtil.ta(v1), CommonUtil.ta(v2), SCALE_2);
	}
}
