package com.tzx.framework.common.exception;

import java.util.List;

import net.sf.json.JSONObject;

public class MessageCustomErrorCode implements CustomErrorCode
{

	private int					number;

	private List<JSONObject>	mes;

	public MessageCustomErrorCode(int number, List<JSONObject> mes)
	{
		this.number = number;
		this.mes = mes;

	}

	@Override
	public String getMessage()
	{
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public int getNumber()
	{
		// TODO Auto-generated method stub
		return this.number;
	}

	@Override
	public List<JSONObject> getMes()
	{
		// TODO Auto-generated method stub
		return this.mes;
	}

}
