package com.tzx.pos.bo.dto;

import java.io.Serializable;
import java.util.List;

import net.sf.json.JSONObject;

/**
 * 
 * <AUTHOR> 
 * @日期：2015年5月19日-下午6:51:46
 */

public class PosBillBookedItem implements Serializable
{

	/**
	 * 
	 */
	private static final long	serialVersionUID	= 1L;
	
	private String item_serial;
	private String rwid;
	private String item_id; //
	private String bill_booked_num;
	private String item_name;
	private String item_remark;
	private String item_property;
	
	private String discount_rate;
	private String discount_amount;
	
	private String item_price;
	private String item_count;
	private String item_amount;
	private String real_amount;
	private String remark;
	private String unit_id;
	private String details_id;
	
	private String item_taste;
	private String sale_mode;
	private String setmeal_id;
	private String setmeal_rwid;
	
	private String waitcall_tag;
	
	private String assist_num;
	private String assist_money;
	
	private String assist_item_id;
	
	private String print_tag;
	private String item_unit_name;
	
	private String item_mac_id;
	private String item_time;
	private String waiter_name;
	private String method_money;
	private String payment_state;
	private List<?> method;
	private String batch_num;
	private List<JSONObject> numOutList;
	public String getBatch_num()
	{
		return batch_num;
	}
	public void setBatch_num(String batch_num)
	{
		this.batch_num = batch_num;
	}
	public String getMethod_money()
	{
		return method_money;
	}
	public void setMethod_money(String method_money)
	{
		this.method_money = method_money;
	}
	public String getItem_mac_id() {
		return item_mac_id;
	}
	public void setItem_mac_id(String item_mac_id) {
		this.item_mac_id = item_mac_id;
	}
	public String getItem_time() {
		return item_time;
	}
	public void setItem_time(String item_time) {
		this.item_time = item_time;
	}
	public String getWaiter_name() {
		return waiter_name;
	}
	public void setWaiter_name(String waiter_name) {
		this.waiter_name = waiter_name;
	}
	public String getItem_unit_name()
	{
		return item_unit_name;
	}
	public void setItem_unit_name(String item_unit_name)
	{
		this.item_unit_name = item_unit_name;
	}
	public String getAssist_item_id()
	{
		return assist_item_id;
	}
	public void setAssist_item_id(String assist_item_id)
	{
		this.assist_item_id = assist_item_id;
	}
	public String getPrint_tag()
	{
		return print_tag;
	}
	public void setPrint_tag(String print_tag)
	{
		this.print_tag = print_tag;
	}
	public String getAssist_num()
	{
		return assist_num;
	}
	public void setAssist_num(String assist_num)
	{
		this.assist_num = assist_num;
	}
	public String getAssist_money()
	{
		return assist_money;
	}
	public void setAssist_money(String assist_money)
	{
		this.assist_money = assist_money;
	}
	public String getWaitcall_tag()
	{
		return waitcall_tag;
	}
	public void setWaitcall_tag(String waitcall_tag)
	{
		this.waitcall_tag = waitcall_tag;
	}
	public String getItem_taste()
	{
		return item_taste;
	}
	public void setItem_taste(String item_taste)
	{
		this.item_taste = item_taste;
	}
	public String getSale_mode()
	{
		return sale_mode;
	}
	public void setSale_mode(String sale_mode)
	{
		this.sale_mode = sale_mode;
	}
	public String getSetmeal_id()
	{
		return setmeal_id;
	}
	public void setSetmeal_id(String setmeal_id)
	{
		this.setmeal_id = setmeal_id;
	}
	public String getSetmeal_rwid()
	{
		return setmeal_rwid;
	}
	public void setSetmeal_rwid(String setmeal_rwid)
	{
		this.setmeal_rwid = setmeal_rwid;
	}
	public List<?> getMethod()
	{
		return method;
	}
	public void setMethod(List<?> method)
	{
		this.method = method;
	}
	public String getUnit_id()
	{
		return unit_id;
	}
	public void setUnit_id(String unit_id)
	{
		this.unit_id = unit_id;
	}
	public String getDetails_id()
	{
		return details_id;
	}
	public void setDetails_id(String details_id)
	{
		this.details_id = details_id;
	}
	public String getItem_serial()
	{
		return item_serial;
	}
	public void setItem_serial(String item_serial)
	{
		this.item_serial = item_serial;
	}
	public String getRwid()
	{
		return rwid;
	}
	public void setRwid(String rwid)
	{
		this.rwid = rwid;
	}
	public String getItem_id()
	{
		return item_id;
	}
	public void setItem_id(String item_id)
	{
		this.item_id = item_id;
	}

	public String getBill_booked_num() {
		return bill_booked_num;
	}
	public void setBill_booked_num(String bill_booked_num) {
		this.bill_booked_num = bill_booked_num;
	}
	public String getItem_name()
	{
		return item_name;
	}
	public void setItem_name(String item_name)
	{
		this.item_name = item_name;
	}
	public String getItem_remark()
	{
		return item_remark;
	}
	public void setItem_remark(String item_remark)
	{
		this.item_remark = item_remark;
	}
	public String getItem_property()
	{
		return item_property;
	}
	public void setItem_property(String item_property)
	{
		this.item_property = item_property;
	}
	public String getDiscount_rate()
	{
		return discount_rate;
	}
	public void setDiscount_rate(String discount_rate)
	{
		this.discount_rate = discount_rate;
	}
	public String getDiscount_amount()
	{
		return discount_amount;
	}
	public void setDiscount_amount(String discount_amount)
	{
		this.discount_amount = discount_amount;
	}
	public String getItem_price()
	{
		return item_price;
	}
	public void setItem_price(String item_price)
	{
		this.item_price = item_price;
	}
	public String getItem_count()
	{
		return item_count;
	}
	public void setItem_count(String item_count)
	{
		this.item_count = item_count;
	}
	public String getItem_amount()
	{
		return item_amount;
	}
	public void setItem_amount(String item_amount)
	{
		this.item_amount = item_amount;
	}
	public String getReal_amount()
	{
		return real_amount;
	}
	public void setReal_amount(String real_amount)
	{
		this.real_amount = real_amount;
	}
	public String getRemark()
	{
		return remark;
	}
	public void setRemark(String remark)
	{
		this.remark = remark;
	}
	public String getPayment_state()
	{
		return payment_state;
	}
	public void setPayment_state(String payment_state)
	{
		this.payment_state = payment_state;
	}
	public List<JSONObject> getNumOutList() {
		return numOutList;
	}
	public void setNumOutList(List<JSONObject> numOutList) {
		this.numOutList = numOutList;
	}
	
	
	
}
