package com.tzx.pos.base.constant;

/**
 * 基础资料详细信息
 * 
 * <AUTHOR> 2015年8月21日-下午6:28:49
 */
public enum BasicData
{
	/**
	 * 班次
	 */
	DUTY_ORDER(50000, "BASIC_DATA_DUTY_ORDER"),
	/**
	 * 桌位属性
	 */
	TABLE_PROPERTY(50001, "BASIC_DATA_TABLE_PROPERTY"),
	/**
	 * 桌位区域
	 */
	BUSINESS_AREA(50002, "BASIC_DATA_BUSINESS_AREA"),
	/**
	 * 菜品类别
	 */
	ITEM_CLASS(50003, "BASIC_DATA_ITEM_CLASS"),
	/**
	 * 服务费种
	 */
	SERVICE_TYPE(50004, "BASIC_DATA_SERVICE_TYPE"),
	/**
	 * 口味备注
	 */
	TASTE(50005, "BASIC_DATA_TASTE"),
	/**
	 * 做法
	 */
	METHOD(50006, "BASIC_DATA_METHOD"),
	/**
	 * 异常原因
	 */
	REASON(50007, "BASIC_DATA_REASON"),
	/**
	 * 折扣方案
	 */
	DISCOUNTCASE(50008, "BASIC_DATA_DISCOUNT"),
	/**
	 * 折扣方案明细
	 */
	DISCOUNT_DETAIL(50009, "BASIC_DATA_DISCOUNT_DETAIL"),
	/**
	 * 付款方式
	 */
	PAYMENT_WAY(50010, "BASIC_DATA_PAYMENT_WAY"),
	/**
	 * 菜品信息
	 */
	DISH(50011, "BASIC_DATA_DISH"),
	/**
	 * 菜品规格
	 */
	UNIT(50012, "BASIC_DATA_UNIT"),
	/**
	 * 桌位
	 */
	TABLE(50013, "BASIC_DATA_TABLE"),
	/**
	 * 
	 */
	TIMEPRICE(50014, "BASIC_DATA_TIMEPRICE"),
	/**
	 * 
	 */
	TIMEPRICE_ITEM(50015, "BASIC_DATA_TIMEPRICE_ITEM"),
	/**
	 * 套餐明细
	 */
	COMBO_DETAILS(50016, "BASIC_DATA_COMBO_DETAILS"),
	/**
	 * 套餐项目组明细
	 */
	ITEM_GROUP_DETAILS(50017, "BASIC_DATA_ITEM_GROUP_DETAILS"),
	/**
	 * 用户信息
	 */
	USER(50018, "BASIC_DATA_USER"),
	/**
	 * 系统参数
	 */
	SYS_PARAMETER(50019, "BASIC_DATA_SYS_PARAMETER"),
	/**
	 * 
	 */
	CRM_INCORPORATION_INFO(50020, "BASIC_DATA_CRM_INCORPORATION_INFO"),
	/**
	 * 
	 */
	CRM_INCORPORATION_PERSON(50021, "BASIC_DATA_CRM_INCORPORATION_PERSON"),
	/**
	 * 终端设备
	 */
	DEVICES(50022, "BASIC_DATA_DEVICES"),
	/**
	 * 会员卡类型
	 */
	CRM_CARD_CLASS(50023, "BASIC_DATA_CRM_CARD_CLASS"),
	/**
	 * 会员价菜品
	 */
	CRM_ITEM_VIP(50024, "BASIC_DATA_CRM_ITEM_VIP"),
	/**
	 * 用户折扣权限
	 */
	USER_DISCOUNT_AUTHORITY(50025, "BASIC_DATA_USER_DISCOUNT_AUTHORITY"),
	/**
	 * 用户折扣方案权限
	 */
	USER_DISCOUNT_CASE(50026, "BASIC_DATA_USER_DISCOUNT_CASE"),
	/**
	 * 付款方式显示设置
	 */
	HQ_PAYMENT_SHOWTYPE(50027, "BASIC_DATA_HQ_PAYMENT_SHOWTYPE"),
	/**
	 * 副屏轮播图片
	 */
	BOH_IMG_PADPC(50028, "BASIC_DATA_BOH_IMG_PADPC"),
	/**
	 * 套餐项目组
	 */
	ITEM_GROUP(50029,"BASIC_DATA_ITEM_GROUP"),
	
	/**
	 * 估清菜品
	 */
	SOLDOUT(50030, "SOLDOUT"),
	/**
	 * 急推菜
	 */
	WORRYSALE(50031,"WORRYSALE"),
	/**
	 * 菜品格式
	 */
	DISH_FORMAT(50032, "BASIC_DATA_DISH_FORMAT"),
	/**
	 * 活动规则数据
	 */
	CRM_ACTIVITY_RULE(50033, "CRM_ACTIVITY_RULE"),
	/**
	 * 活动商品规则数据
	 */
	CRM_ACTIVITY_ITEM(50034, "CRM_ACTIVITY_ITEM"),
	/**
	 * POS系统参数字典
	 */
	SYS_DICTIONARY(50035, "SYS_DICTIONARY"),
	/**
	 * 安卓系统参数字典
	 */
	ANDROID_SYS_DICTIONARY(50036, "ANDROID_SYS_DICTIONARY"),
    /**
     * 门店
     */
	ORGAN(50037, "BASIC_DATA_ORGAN"),
    /**
     * 菜单按钮配置
     */
    MENU_BUTTON_CONFIG(50038, "MENU_BUTTON_CONFIG"),
	/**
	 * 支付方式排序配置
	 */
	PAYMENTWAY_BUTTON_CONFIG(50039, "PAYMENTWAY_BUTTON_CONFIG"),
	/**
	 * 就餐类型
	 */
	DINING_TYPE(50040, "DINING_TYPE"),
    /**
     * 菜品口味表
     */
    ITEM_TASTE(50041, "ITEM_TASTE"),
    /**
     * 打印档口信息
     */
    HQ_PRINTER(50042,"HQ_PRINTER"),
	/**
	 * 禁用菜品
	 */
	FORBIDDEN_ITEM(50043,"FORBIDDEN_ITEM"),
	/**
	 * 设置套餐默认项
	 */
	SETTING_SETMEAL_DETAILS(50044,"SETTING_SETMEAL_DETAILS"),

	/**
	 * 设置班次次数
	 */
	SETTING_SHIFTS_TIMES(50045,"SETTING_SHIFTS_TIMES"),


	/**
	 * 设置班次次数
	 */
	ROLES(50045,"ROLES"),
	;
	
	

	private final Integer	code;
	private final String	type;

	private BasicData(Integer code, String type)
	{
		this.code = code;
		this.type = type;
	}

	public Integer getCode()
	{
		return this.code;
	}

	public String getType()
	{
		return this.type;
	}
}
