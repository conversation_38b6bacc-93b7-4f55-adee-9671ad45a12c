/**
  * @Description: TODO
  * @author: z<PERSON>quan
  * @version: 1.0
  * @Date: 2018年4月3日
*/
package com.tzx.base.po.springjdbc.dao.imp;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.apache.log4j.Logger;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import com.tzx.base.po.springjdbc.dao.DataSyncDao;
import com.tzx.framework.common.util.dao.impl.GenericDaoImpl;

/**
  * @Description: TODO
  * @param:
  * @author: zhouquan
  * @version: 1.0
  * @Date: 2018年4月3日
*/
@Repository(DataSyncDao.NAME)
public class DataSyncDaoImpl extends GenericDaoImpl implements DataSyncDao{

	public final static Logger logger = Logger.getLogger(DataSyncDaoImpl.class);
	
	@Resource
	private JdbcTemplate jdbcTemplate;
	
	@Override
	public int getInt(String sql) throws Exception{
		int res = -1;
		Connection connection = null;
		try {
			connection = this.jdbcTemplate.getDataSource().getConnection();
			PreparedStatement ps = connection.prepareStatement(sql);
			ResultSet rs = ps.executeQuery();
			while(rs.next()){
				res = rs.getInt(1);
				break;
			}			
			ps.close();
		}catch (Exception e) {		
		} finally {
			try {
				connection.close();
			} catch (SQLException e) {
				logger.error(e.getMessage(), e);
			}
		}
		return res;
	}
	
	@Override
	public boolean saveBaseData(String[] sqList) {
//		String tableName = null;
//		StringBuilder sb = new StringBuilder();
//		StringBuilder line = null;
//		for (int i = 0; i < sqList.length; i++) {
//			line = sb.append(sqList[0]);
//		}
//		String[] lineList = line.toString().split(" ");
//		for (int i = 0; i < lineList.length; i++) {
//			if (lineList[i].equals("EXISTS")) {
//				tableName = lineList[i + 1];
//			}
//		}
		Connection connection = null;
		Statement stm = null;
		try {
			connection = this.jdbcTemplate.getDataSource().getConnection();
			connection.setAutoCommit(false);
			stm = connection.createStatement();
			
			for (int k = 0; k < sqList.length; k++) {
				stm.addBatch(sqList[k]);
			}
			stm.executeBatch();
			connection.commit();
			connection.setAutoCommit(true);
			stm.clearBatch();//提交后，Batch清空
			logger.info("添加成功");
//			System.out.println("表：" + tableName + "添加成功");
			return true;
		} catch (Exception e) {
			try {
				connection.rollback();
				connection.setAutoCommit(true);
//				LOGGER.info("表：" + tableName + "添加失败");
			} catch (SQLException e1) {
				logger.error(e1.getMessage(), e1);
			}
			return false;
		} finally {
			try {
				connection.close();
				stm.close();
			} catch (SQLException e) {
				logger.error(e.getMessage(), e);
			}
		}
		//return false;
	}
	
	/*
	 * 事物方式批量执行SQL
	 * @see com.tzx.base.po.springjdbc.dao.DataSyncDao#execBatchSqls(java.util.List)
	 */
	@Override
	public boolean execBatchSqls (List<String> sqlList) {
		if(sqlList == null) return false;
		Connection connection = null;
		Statement stm = null;
		try {
			connection = this.jdbcTemplate.getDataSource().getConnection();
			connection.setAutoCommit(false);
			stm = connection.createStatement();			
			for (int k = 0; k < sqlList.size(); k++) {
				stm.addBatch(sqlList.get(k));
			}
			stm.executeBatch();
			connection.commit();
			connection.setAutoCommit(true);
			stm.clearBatch();  //提交后，Batch清空
			return true;
		} catch (Exception e) {
			try {
				connection.rollback();
				connection.setAutoCommit(true);
			} catch (SQLException e1) {
				logger.error(e1.getMessage(), e1);
			}
			logger.error(e.getMessage());
			return false;
		} finally {
			try {
				connection.close();
				stm.close();
			} catch (SQLException e) {
				logger.error(e.getMessage(), e);
			}
		}
	}

}
