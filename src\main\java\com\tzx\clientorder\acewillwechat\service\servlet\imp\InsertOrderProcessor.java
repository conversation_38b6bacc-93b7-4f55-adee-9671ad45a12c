package com.tzx.clientorder.acewillwechat.service.servlet.imp;


import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Service;

import com.tzx.clientorder.acewillwechat.bo.InsertOrderService;
import com.tzx.clientorder.acewillwechat.service.servlet.SocketMessageProcessor;
import com.tzx.pos.asyn.thread.OrderingPrintThread;

@Service(SocketMessageProcessor.INSERT_ORDER)
public class InsertOrderProcessor implements SocketMessageProcessor {

	@Resource(name = InsertOrderService.NAME)
	InsertOrderService insertOrderService;

	@Override
	public JSONObject process(String tenancyId, Integer storeId, JSONObject jsonObject) throws Exception {
		OrderingPrintThread orderingPrintThread = insertOrderService.insertOrderRequiresNew(tenancyId, storeId, jsonObject);
		insertOrderService.orderingPrint(orderingPrintThread);
		return null;
	}
	
}
