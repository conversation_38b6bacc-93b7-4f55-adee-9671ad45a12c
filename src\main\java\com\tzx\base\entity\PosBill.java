package com.tzx.base.entity;

import java.sql.Timestamp;
import java.util.Date;

/**
 * 账单表映射实体
 * 
 * <AUTHOR>
 * <AUTHOR>
 */
public class PosBill
{

	private String		tenancy_id;
	private Integer		store_id;
	private Integer		id;
	private String		bill_num;
	private String		batch_num;
	private String		serial_num;
	private Date		report_date;
	private String		table_code;
	private Integer		guest;
	private Timestamp	opentable_time;
	private Timestamp	payment_time;
	private Integer		payment_num;
	private String		open_pos_num;
	private String		pos_num;
	private String		waiter_num;
	private String		open_opt;
	private String		cashier_num;
	private Integer		shift_id;
	private Integer		item_menu_id;
	private Integer		service_id;
	private Double		service_amount;
	private Integer		service_discount;
	private String		order_num;
	private Timestamp	print_time;
	private Integer		print_count;
	private Double		subtotal;
	private Double		bill_amount;
	private Double		payment_amount;
	private Double		difference;
	private Double		discountk_amount;
	private Double		discountr_amount;
	private Double		maling_amount;
	private Double		single_discount_amount;
	private Double		discount_amount;
	private Double		free_amount;
	private Double		givi_amount;
	private Double		more_coupon;
	private Double		average_amount;
	private String		discount_num;
	private Integer		discount_case_id;
	private Integer		discount_rate;
	private Integer		billfree_reason_id;
	private Integer		discount_mode_id;
	private String		transfer_remark;
	private String		sale_mode;
	private String		bill_state;
	private String		bill_property;
	private Integer		upload_tag;
	private Integer		deposit_count;
	private String		copy_bill_num;
	private String		source;
	private Integer		opt_login_number;
	private String		guest_msg;
	private Double		integraloffset;
	private String		remark;
	private Double		return_amount;
	private Double		advance_payment_amt;
	private Double		advance_refund_amt;
	private String		is_refund;
	private Integer		payment_id;
	private String		pay_no;
	private String		third_bill_code;
	private String		payment_state;
	private String		payment_manager_num;
	private Double		shop_real_amount;
	private Double		total_fees;
	private Double		platform_charge_amount;
	private String		settlement_type;
	private String		bill_taste;
	private String		fictitious_table;
	private Integer		recover_count;
	private Integer		discount_reason_id;

	public String getTenancy_id()
	{
		return tenancy_id;
	}

	public void setTenancy_id(String tenancy_id)
	{
		this.tenancy_id = tenancy_id;
	}

	public Integer getStore_id()
	{
		return store_id;
	}

	public void setStore_id(Integer store_id)
	{
		this.store_id = store_id;
	}

	public Integer getId()
	{
		return id;
	}

	public void setId(Integer id)
	{
		this.id = id;
	}

	public String getBill_num()
	{
		return bill_num;
	}

	public void setBill_num(String bill_num)
	{
		this.bill_num = bill_num;
	}

	public String getBatch_num()
	{
		return batch_num;
	}

	public void setBatch_num(String batch_num)
	{
		this.batch_num = batch_num;
	}

	public String getSerial_num()
	{
		return serial_num;
	}

	public void setSerial_num(String serial_num)
	{
		this.serial_num = serial_num;
	}

	public Date getReport_date()
	{
		return report_date;
	}

	public void setReport_date(Date report_date)
	{
		this.report_date = report_date;
	}

	public String getTable_code()
	{
		return table_code;
	}

	public void setTable_code(String table_code)
	{
		this.table_code = table_code;
	}

	public Integer getGuest()
	{
		return guest;
	}

	public void setGuest(Integer guest)
	{
		this.guest = guest;
	}

	public Timestamp getOpentable_time()
	{
		return opentable_time;
	}

	public void setOpentable_time(Timestamp opentable_time)
	{
		this.opentable_time = opentable_time;
	}

	public Timestamp getPayment_time()
	{
		return payment_time;
	}

	public void setPayment_time(Timestamp payment_time)
	{
		this.payment_time = payment_time;
	}

	public Integer getPayment_num()
	{
		return payment_num;
	}

	public void setPayment_num(Integer payment_num)
	{
		this.payment_num = payment_num;
	}

	public String getOpen_pos_num()
	{
		return open_pos_num;
	}

	public void setOpen_pos_num(String open_pos_num)
	{
		this.open_pos_num = open_pos_num;
	}

	public String getPos_num()
	{
		return pos_num;
	}

	public void setPos_num(String pos_num)
	{
		this.pos_num = pos_num;
	}

	public String getWaiter_num()
	{
		return waiter_num;
	}

	public void setWaiter_num(String waiter_num)
	{
		this.waiter_num = waiter_num;
	}

	public String getOpen_opt()
	{
		return open_opt;
	}

	public void setOpen_opt(String open_opt)
	{
		this.open_opt = open_opt;
	}

	public String getCashier_num()
	{
		return cashier_num;
	}

	public void setCashier_num(String cashier_num)
	{
		this.cashier_num = cashier_num;
	}

	public Integer getShift_id()
	{
		return shift_id;
	}

	public void setShift_id(Integer shift_id)
	{
		this.shift_id = shift_id;
	}

	public Integer getItem_menu_id()
	{
		return item_menu_id;
	}

	public void setItem_menu_id(Integer item_menu_id)
	{
		this.item_menu_id = item_menu_id;
	}

	public Integer getService_id()
	{
		return service_id;
	}

	public void setService_id(Integer service_id)
	{
		this.service_id = service_id;
	}

	public Double getService_amount()
	{
		return service_amount;
	}

	public void setService_amount(Double service_amount)
	{
		this.service_amount = service_amount;
	}

	public Integer getService_discount()
	{
		return service_discount;
	}

	public void setService_discount(Integer service_discount)
	{
		this.service_discount = service_discount;
	}

	public String getOrder_num()
	{
		return order_num;
	}

	public void setOrder_num(String order_num)
	{
		this.order_num = order_num;
	}

	public Timestamp getPrint_time()
	{
		return print_time;
	}

	public void setPrint_time(Timestamp print_time)
	{
		this.print_time = print_time;
	}

	public Integer getPrint_count()
	{
		return print_count;
	}

	public void setPrint_count(Integer print_count)
	{
		this.print_count = print_count;
	}

	public Double getSubtotal()
	{
		return subtotal;
	}

	public void setSubtotal(Double subtotal)
	{
		this.subtotal = subtotal;
	}

	public Double getBill_amount()
	{
		return bill_amount;
	}

	public void setBill_amount(Double bill_amount)
	{
		this.bill_amount = bill_amount;
	}

	public Double getPayment_amount()
	{
		return payment_amount;
	}

	public void setPayment_amount(Double payment_amount)
	{
		this.payment_amount = payment_amount;
	}

	public Double getDifference()
	{
		return difference;
	}

	public void setDifference(Double difference)
	{
		this.difference = difference;
	}

	public Double getDiscountk_amount()
	{
		return discountk_amount;
	}

	public void setDiscountk_amount(Double discountk_amount)
	{
		this.discountk_amount = discountk_amount;
	}

	public Double getDiscountr_amount()
	{
		return discountr_amount;
	}

	public void setDiscountr_amount(Double discountr_amount)
	{
		this.discountr_amount = discountr_amount;
	}

	public Double getMaling_amount()
	{
		return maling_amount;
	}

	public void setMaling_amount(Double maling_amount)
	{
		this.maling_amount = maling_amount;
	}

	public Double getSingle_discount_amount()
	{
		return single_discount_amount;
	}

	public void setSingle_discount_amount(Double single_discount_amount)
	{
		this.single_discount_amount = single_discount_amount;
	}

	public Double getDiscount_amount()
	{
		return discount_amount;
	}

	public void setDiscount_amount(Double discount_amount)
	{
		this.discount_amount = discount_amount;
	}

	public Double getFree_amount()
	{
		return free_amount;
	}

	public void setFree_amount(Double free_amount)
	{
		this.free_amount = free_amount;
	}

	public Double getGivi_amount()
	{
		return givi_amount;
	}

	public void setGivi_amount(Double givi_amount)
	{
		this.givi_amount = givi_amount;
	}

	public Double getMore_coupon()
	{
		return more_coupon;
	}

	public void setMore_coupon(Double more_coupon)
	{
		this.more_coupon = more_coupon;
	}

	public Double getAverage_amount()
	{
		return average_amount;
	}

	public void setAverage_amount(Double average_amount)
	{
		this.average_amount = average_amount;
	}

	public String getDiscount_num()
	{
		return discount_num;
	}

	public void setDiscount_num(String discount_num)
	{
		this.discount_num = discount_num;
	}

	public Integer getDiscount_case_id()
	{
		return discount_case_id;
	}

	public void setDiscount_case_id(Integer discount_case_id)
	{
		this.discount_case_id = discount_case_id;
	}

	public Integer getDiscount_rate()
	{
		return discount_rate;
	}

	public void setDiscount_rate(Integer discount_rate)
	{
		this.discount_rate = discount_rate;
	}

	public Integer getBillfree_reason_id()
	{
		return billfree_reason_id;
	}

	public void setBillfree_reason_id(Integer billfree_reason_id)
	{
		this.billfree_reason_id = billfree_reason_id;
	}

	public Integer getDiscount_mode_id()
	{
		return discount_mode_id;
	}

	public void setDiscount_mode_id(Integer discount_mode_id)
	{
		this.discount_mode_id = discount_mode_id;
	}

	public String getTransfer_remark()
	{
		return transfer_remark;
	}

	public void setTransfer_remark(String transfer_remark)
	{
		this.transfer_remark = transfer_remark;
	}

	public String getSale_mode()
	{
		return sale_mode;
	}

	public void setSale_mode(String sale_mode)
	{
		this.sale_mode = sale_mode;
	}

	public String getBill_state()
	{
		return bill_state;
	}

	public void setBill_state(String bill_state)
	{
		this.bill_state = bill_state;
	}

	public String getBill_property()
	{
		return bill_property;
	}

	public void setBill_property(String bill_property)
	{
		this.bill_property = bill_property;
	}

	public Integer getUpload_tag()
	{
		return upload_tag;
	}

	public void setUpload_tag(Integer upload_tag)
	{
		this.upload_tag = upload_tag;
	}

	public Integer getDeposit_count()
	{
		return deposit_count;
	}

	public void setDeposit_count(Integer deposit_count)
	{
		this.deposit_count = deposit_count;
	}

	public String getCopy_bill_num()
	{
		return copy_bill_num;
	}

	public void setCopy_bill_num(String copy_bill_num)
	{
		this.copy_bill_num = copy_bill_num;
	}

	public String getSource()
	{
		return source;
	}

	public void setSource(String source)
	{
		this.source = source;
	}

	public Integer getOpt_login_number()
	{
		return opt_login_number;
	}

	public void setOpt_login_number(Integer opt_login_number)
	{
		this.opt_login_number = opt_login_number;
	}

	public String getGuest_msg()
	{
		return guest_msg;
	}

	public void setGuest_msg(String guest_msg)
	{
		this.guest_msg = guest_msg;
	}

	public Double getIntegraloffset()
	{
		return integraloffset;
	}

	public void setIntegraloffset(Double integraloffset)
	{
		this.integraloffset = integraloffset;
	}

	public String getRemark()
	{
		return remark;
	}

	public void setRemark(String remark)
	{
		this.remark = remark;
	}

	public Double getReturn_amount()
	{
		return return_amount;
	}

	public void setReturn_amount(Double return_amount)
	{
		this.return_amount = return_amount;
	}

	public Double getAdvance_payment_amt()
	{
		return advance_payment_amt;
	}

	public void setAdvance_payment_amt(Double advance_payment_amt)
	{
		this.advance_payment_amt = advance_payment_amt;
	}

	public Double getAdvance_refund_amt()
	{
		return advance_refund_amt;
	}

	public void setAdvance_refund_amt(Double advance_refund_amt)
	{
		this.advance_refund_amt = advance_refund_amt;
	}

	public String getIs_refund()
	{
		return is_refund;
	}

	public void setIs_refund(String is_refund)
	{
		this.is_refund = is_refund;
	}

	public Integer getPayment_id()
	{
		return payment_id;
	}

	public void setPayment_id(Integer payment_id)
	{
		this.payment_id = payment_id;
	}

	public String getPay_no()
	{
		return pay_no;
	}

	public void setPay_no(String pay_no)
	{
		this.pay_no = pay_no;
	}

	public String getThird_bill_code()
	{
		return third_bill_code;
	}

	public void setThird_bill_code(String third_bill_code)
	{
		this.third_bill_code = third_bill_code;
	}

	public String getPayment_state()
	{
		return payment_state;
	}

	public void setPayment_state(String payment_state)
	{
		this.payment_state = payment_state;
	}

	public String getPayment_manager_num()
	{
		return payment_manager_num;
	}

	public void setPayment_manager_num(String payment_manager_num)
	{
		this.payment_manager_num = payment_manager_num;
	}

	public Double getShop_real_amount()
	{
		return shop_real_amount;
	}

	public void setShop_real_amount(Double shop_real_amount)
	{
		this.shop_real_amount = shop_real_amount;
	}

	public Double getTotal_fees()
	{
		return total_fees;
	}

	public void setTotal_fees(Double total_fees)
	{
		this.total_fees = total_fees;
	}

	public Double getPlatform_charge_amount()
	{
		return platform_charge_amount;
	}

	public void setPlatform_charge_amount(Double platform_charge_amount)
	{
		this.platform_charge_amount = platform_charge_amount;
	}

	public String getSettlement_type()
	{
		return settlement_type;
	}

	public void setSettlement_type(String settlement_type)
	{
		this.settlement_type = settlement_type;
	}

	public String getBill_taste()
	{
		return bill_taste;
	}

	public void setBill_taste(String bill_taste)
	{
		this.bill_taste = bill_taste;
	}

	public String getFictitious_table()
	{
		return fictitious_table;
	}

	public void setFictitious_table(String fictitious_table)
	{
		this.fictitious_table = fictitious_table;
	}

	public Integer getRecover_count()
	{
		return recover_count;
	}

	public void setRecover_count(Integer recover_count)
	{
		this.recover_count = recover_count;
	}

	public Integer getDiscount_reason_id()
	{
		return discount_reason_id;
	}

	public void setDiscount_reason_id(Integer discount_reason_id)
	{
		this.discount_reason_id = discount_reason_id;
	}

}
