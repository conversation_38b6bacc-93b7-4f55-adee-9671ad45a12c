package com.tzx.clientorder.acewillwechat.bo;

import net.sf.json.JSONObject;


public interface WshPosEntranceService
{

	String	NAME	= "com.tzx.clientorder.acewillwechat.bo.imp.WshPosEntranceServiceImp";

	/** 向微生活上传基础资料信息
	 * @param tenancyID
	 * @param storeId
	 * @param params
	 * @return
	 * @throws Exception
	 */
	JSONObject uploadBaseDataToWsh(String tenancyID, int storeId, JSONObject params) throws Exception;
	
	/** 解锁订单通知
	 * @param tenancyID
	 * @param storeId
	 * @param tableCode
	 * @return
	 * @throws Exception
	 */
	JSONObject notifyWshUnlockOrder(String tenancyID, int storeId, String tableCode) throws Exception;
	
	/**
	 * 预退单（担心pos退单操作会改变pos_bill表内容，先把退单需要的数据预存起来）
	 */
	JSONObject preCancelBill(String tenancyID, int storeId, String billno) throws Exception;
	
	/**
	 * 退单
	 */
	JSONObject cancelBill(String tenancyID, int storeId, String orderNum) throws Exception;

	/** 是否启用微生活H5点餐
	 * @param tenancyID
	 * @param storeId
	 * @return
	 */
	boolean isWlifeOrderH5(String tenancyID, int storeId);
}
