package com.tzx.clientorder.wlifeprogram.task.runable;

import java.util.List;

import org.apache.log4j.Logger;

import com.tzx.clientorder.wlifeprogram.bo.BasicService;
import com.tzx.clientorder.wlifeprogram.bo.WlifeService;
import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.Tools;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.pos.base.constant.WLifeConstant;
import com.tzx.pos.base.util.CacheTableUtil;

import net.sf.json.JSONObject;

public class SyncTimePriceRunable implements Runnable
{
	Logger logger = Logger.getLogger(SyncTimePriceRunable.class);
	
	@Override
	public void run()
	{
		try
		{
			String tenancyId = null;
			if (Constant.getSystemMap().containsKey("tenent_id"))
			{
				tenancyId = Constant.getSystemMap().get("tenent_id");
			}

			Integer storeId = null;
			if (Constant.getSystemMap().containsKey("store_id"))
			{
				storeId = Integer.valueOf(Constant.getSystemMap().get("store_id"));
			}

			if (Tools.isNullOrEmpty(tenancyId) || Tools.isNullOrEmpty(storeId))
			{
				logger.info("参数为空");
				return;
			}
			
			if (false == WLifeConstant.USER_WLIFE_ORDER_TYPE_PROGRAM.equals(CacheTableUtil.getSysParameter(WLifeConstant.USER_WLIFE_ORDER_TYPE_KEY)))
			{
				logger.info("未启用微生活小程序");
				return;
			}
			
			DBContextHolder.setTenancyid(tenancyId);

			WlifeService wlifeService = (WlifeService) SpringConext.getApplicationContext().getBean(WlifeService.NAME);
			wlifeService.synchrodataDish(tenancyId, storeId,false);

		}
		catch (Exception e)
		{
			logger.info("同步菜品数据到微生活小程序失败:",e);
			e.printStackTrace();
		}
		
	}

}
