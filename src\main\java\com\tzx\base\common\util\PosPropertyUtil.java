package com.tzx.base.common.util;

import java.io.*;
import java.net.URISyntaxException;
import java.net.URL;
import java.util.Enumeration;
import java.util.Properties;

import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.core.io.Resource;

import com.tzx.framework.common.util.SpringConext;

/**
 * 配置信息读取工具类
 *
 * <AUTHOR>
 * @since 2016年11月2日18:07:07
 */
public class PosPropertyUtil {
    /**
     *
     */
    private static final Logger logger = Logger.getLogger(PosPropertyUtil.class);

    /**
     *
     */
    private static volatile Properties posSystemProperties = new Properties();

    /**
     *
     */
    private static volatile Properties properties = new Properties();

    /**
     * @param key
     * @param strs
     * @return
     */
    public static String getMsg(String key, String... strs) {
        return String.format(get(key), strs);
    }

    /**
     * 获取配置值
     *
     * @param key
     * @return
     */
    private static String get(String key) {
        if (properties.isEmpty()) {
            File file = FileUtils.toFile(PosPropertyUtil.class.getResource("/pos"));
            File[] files = file.listFiles(new FilenameFilter() {
                @Override
                public boolean accept(File dir, String name) {
                    return FilenameUtils.isExtension(name, "properties");
                }
            });
            FileInputStream inStream = null;
            for (File f : files) {
                try {
                    inStream = FileUtils.openInputStream(f);
                    properties.load(inStream);
                } catch (IOException e) {
                    logger.error("加载配置文件" + f.getName() + "错误！", e);
                } finally {
                    IOUtils.closeQuietly(inStream);
                }
            }
        }
        return StringUtils.defaultIfEmpty(properties.getProperty(key), StringUtils.EMPTY);
    }

    /**
     * @param key
     * @return
     */
    public static String loadPosSystemProperties(String key) {
        if (posSystemProperties.isEmpty()) {
            File file = new File(System.getProperty("catalina.home") + "/webapps/ROOT/config/systemParam.properties");
            FileInputStream inStream = null;
            try {
                if (!file.exists()) {
                    file = new File(PosPropertyUtil.class.getClassLoader().getResource("/").getPath().replace("WEB-INF/classes","config/systemParam.properties"));
                }
                inStream = FileUtils.openInputStream(file);
                posSystemProperties.load(inStream);
            } catch (IOException e) {
                logger.error("加载systemParam.properties文件出错!", e);
            } finally {
                IOUtils.closeQuietly(inStream);
            }
        }
        return posSystemProperties.getProperty(key);
    }

}
