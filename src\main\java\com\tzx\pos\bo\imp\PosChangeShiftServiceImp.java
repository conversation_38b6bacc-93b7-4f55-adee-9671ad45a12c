package com.tzx.pos.bo.imp;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;

import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.DoubleHelper;
import com.tzx.framework.common.util.JavaMd5;
import com.tzx.framework.common.util.Tools;
import com.tzx.orders.base.util.OrderUtil;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.base.util.ReqDataUtil;
import com.tzx.pos.bo.PosChangeShiftService;
import com.tzx.pos.po.springjdbc.dao.PosDao;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

@Service(PosChangeShiftService.NAME)
public class PosChangeShiftServiceImp extends PosBaseServiceImp implements PosChangeShiftService
{

	private static final Logger	logger	= Logger.getLogger(PosChangeShiftService.class);
	
	@Resource(name = PosDao.NAME)
	private PosDao				posDao;
	
	@Override
	public void getShiftData(Data param, Data result) throws SystemException
	{
		try
		{
			String tenantId = param.getTenancy_id();
			Integer organId = param.getStore_id();
			int scale = 4;

			Map<String, Object> map = ReqDataUtil.getDataMap(param);
			String mode = ParamUtil.getStringValue(map, "mode", true, PosErrorCode.NOT_NULL_MODE);
			Date reportDate = ParamUtil.getDateValue(map, "report_date", true, PosErrorCode.NOT_NULL_REPORT_DATE);
			String posNum = ParamUtil.getStringValue(map, "pos_num", true, PosErrorCode.NOT_NULL_POS_NUM);
			String optNum = ParamUtil.getStringValue(map, "opt_num", true, PosErrorCode.NOT_NULL_OPT_NUM);
			String isAllPos = ParamUtil.getStringValue(map, "isallpos", false, null);
			int shift_id = ParamUtil.getIntegerValue(map, "shift_id", false, null);
			if (StringUtils.isEmpty(isAllPos))
			{
				isAllPos = "N";
			}

			Timestamp startTimestamp = null;
			Timestamp endTimestamp = DateUtil.currentTimestamp();

//			String lastUpdateTime = "";
//			String time = DateUtil.format(endTimestamp);

			JSONObject obj = new JSONObject();
			// 获取当前机台当前人员的交班数据
			if ("0".equalsIgnoreCase(mode))
			{
				String formatState = "";
				String formatMode = posDao.getSysParameter(tenantId, organId, "ZCDCMS");

				StringBuilder querySql = new StringBuilder("select format_state, org_short_name from organ where tenancy_id=? and id=?");
				SqlRowSet rsOrgan = posDao.query4SqlRowSet(querySql.toString(), new Object[]
				{ tenantId, organId });
				if (rsOrgan.next())
				{
					formatState = rsOrgan.getString("format_state");
					obj.put("org_short_name", rsOrgan.getString("org_short_name"));
				}

				// 查询last_updatetime
				StringBuilder str = new StringBuilder("select sd.class_item as name,pos.pos_num,pos.last_updatetime,pos.shift_id from pos_opt_state pos left join duty_order dor on dor.id = pos.shift_id ");
				str.append(" left join sys_dictionary sd on sd.class_item_code= dor.name and sd.class_identifier_code = 'duty' ");
				//str.append(" where pos.tenancy_id=? and pos.store_id=? and pos.report_date=? and pos.opt_num=? and  pos.content=? and pos.tag='0' order by pos.id asc");
				str.append(" where pos.tenancy_id=? and pos.store_id=? and pos.report_date=? and pos.shift_id =? and pos_num=? and  pos.content=? and pos.tag='0' order by pos.id asc");
				SqlRowSet rs = posDao.query4SqlRowSet(str.toString(), new Object[]
				{ tenantId, organId, reportDate,shift_id,posNum, SysDictionary.OPT_STATE_KSSY });

				String kssyPosNum = "";
				if (rs.next())
				{
					startTimestamp = rs.getTimestamp("last_updatetime");
//					lastUpdateTime = rs.getString("last_updatetime");
					obj.put("duty_order_name", rs.getString("name"));
					kssyPosNum = rs.getString("pos_num");
				}

//				if (Tools.isNullOrEmpty(startTimestamp))
//				{
//					throw SystemException.getInstance(PosErrorCode.POS_CASHIER_NOT_SIGNIN_ERROR);
//				}

				obj.put("report_date", DateUtil.format(reportDate, "yyyy-MM-dd"));
				obj.put("work_datetime", DateUtil.format(startTimestamp) + "~" + DateUtil.format(endTimestamp));

				// 营业收入统计
				if ("Y".equalsIgnoreCase(isAllPos))
				{
					this.getShiftDataForBusinessByAllPos(tenantId, organId, reportDate, optNum, startTimestamp, endTimestamp, obj, formatState, formatMode, scale);
				}
				else if ("N".equalsIgnoreCase(isAllPos))
				{
//					if (!posNum.equals(kssyPosNum))
//					{
//						throw SystemException.getInstance(PosErrorCode.POSNUM_NOT_EQUALS_CURRENT_ERROR).set("{0}", kssyPosNum);
//					}

					this.getShiftDataForBusiness(tenantId, organId, reportDate, optNum, posNum, startTimestamp, endTimestamp, obj, formatState, formatMode, scale);
				}


				//让利明细
				this.getShiftDataForConcessionDetails(tenantId, organId, reportDate, optNum, posNum, startTimestamp, endTimestamp, obj, formatState, formatMode, scale);

				//线下团购明细
				this.getOfflineGroupDetails(tenantId, organId, reportDate, optNum, posNum, startTimestamp, endTimestamp, obj, formatState, formatMode, scale);

				// 交款数据
				this.getShiftDataForCashierReceive(tenantId, organId, reportDate, optNum, posNum, startTimestamp, endTimestamp, obj, scale);

				//会员卡储值金额
				this.getShiftDataTotalMoneyForCardRecharge(tenantId, organId, reportDate, optNum, posNum, startTimestamp, endTimestamp, obj, scale);




				JSONArray arrCard = new JSONArray();
				// 发卡
				this.getShiftDataForCardActivation(tenantId, organId, reportDate, optNum, posNum, startTimestamp, endTimestamp, arrCard, scale);

				// 充值
				this.getShiftDataForCardRecharge(tenantId, organId, reportDate, optNum,posNum,startTimestamp, endTimestamp, arrCard,obj, scale);


				// 消费
				this.getShiftDataForCardConsume(tenantId, organId, reportDate, optNum,posNum, startTimestamp, endTimestamp, arrCard, scale);



				// 退卡
				this.getShiftDataForCardBack(tenantId, organId, reportDate, optNum,posNum, startTimestamp, endTimestamp, arrCard, scale);

				// 购买会籍
				this.getShiftDataForBuyVip(tenantId, organId, reportDate, optNum,posNum, startTimestamp, endTimestamp, arrCard, scale);

				obj.put("card_item", arrCard);

				// 充值合计，赠送合计
				this.getShiftDataForCustomerTotal(tenantId, organId, reportDate, optNum,posNum, startTimestamp, endTimestamp, obj, scale);

				// 会员卡付款统计
				this.getShiftDataForCustomerPayment(tenantId, organId, reportDate, optNum, posNum,startTimestamp, endTimestamp, obj, scale);

				// 大类名称、大类数量、大类金额
				this.getShiftDataForItemClass(tenantId, organId, reportDate, optNum, posNum, startTimestamp, endTimestamp, obj, scale);

				// 外卖统计
				//默认改成启用统一班次
//				if(!"1".equals(OrderUtil.getSysPara(tenantId,organId,"enable_wm_shift"))){//是否外卖统一班次 '0:关闭;1:启用;'
//					this.getShiftDataForOrder(tenantId, organId, reportDate, optNum, startTimestamp, endTimestamp, obj, scale);
//				}
			}

			List<JSONObject> list = new ArrayList<JSONObject>();
			list.add(obj);
			result.setData(list);
			result.setCode(Constant.CODE_SUCCESS);
			result.setMsg(Constant.GET_SHIFT_DATA_SUCCESS);
		}
		catch (SystemException se)
		{
			throw se;
		}
		catch (Exception e)
		{
			e.printStackTrace();
			logger.info("获取交班数据：" + ExceptionMessage.getExceptionMessage(e));
			throw new SystemException(PosErrorCode.OPER_ERROR);
		}
	}
	
	/** 统计当前操作人当前机台营业收入
	 * @param tenantId
	 * @param organId
	 * @param reportDate
	 * @param optNum
	 * @param posNum
	 * @param startTimestamp
	 * @param endTimestamp
	 * @param obj
	 * @param formatState
	 * @param formatMode
	 * @param scale
	 * @throws Exception
	 */
	@SuppressWarnings("unused")
	private void getShiftDataForBusiness(String tenantId, int organId,Date reportDate,String optNum, String posNum,Timestamp startTimestamp,Timestamp endTimestamp,JSONObject obj,String formatState,String formatMode,int scale) throws Exception
	{
		double back_num = 0;
		double back_money = 0;
		double retreat_num = 0;
		double retreat_money = 0;
		JSONArray arr = new JSONArray();


		//不包含团购
		StringBuilder sql = new StringBuilder("select pd.*,pd2.give_amount,pd2.give_count,pd2.differ_amount from (");
		sql.append(" select pa.jzid,coalesce(pw.payment_class,pa.type) as payment_class,coalesce((case when pw.payment_class='").append(SysDictionary.PAYMENT_CLASS_CASH).append("' then sd.class_item else pw.payment_name1 end),pa.name) as payment_name,sum(pa.currency_amount) as amount,");
		sql.append(" count(case when pa.name_english='").append(SysDictionary.PAYMENT_CHANGE_ENGLISH_NAME).append("' or bi.bill_state='").append(SysDictionary.BILL_STATE_CJ01).append("' or bi.bill_state='").append(SysDictionary.BILL_STATE_ZDQX02).append("' then null else 1 end) as count");
		sql.append(" from pos_bill_payment pa left join payment_way pw on pa.jzid=pw.id and pa.tenancy_id=pw.tenancy_id left join sys_dictionary sd on pw.payment_name1 = sd.class_item_code");
		sql.append(" left join pos_bill bi on bi.tenancy_id=pa.tenancy_id and bi.store_id=pa.store_id and bi.bill_num=pa.bill_num");
		//sql.append(" where bi.store_id = ? and bi.report_date = ? and bi.cashier_num=? and bi.pos_num=? and bi.payment_time > ? and bi.payment_time < ? group by pa.jzid,payment_name,payment_class,pa.type) pd");
		sql.append(" where bi.store_id = ? and bi.report_date = ?  and bi.pos_num=? and bi.payment_time > ? and bi.payment_time < ? and pw.payment_class<>'groupbuycoupon' group by pa.jzid,payment_name,payment_class,pa.type) pd");
		sql.append(" left join (select jzid,coalesce(sum(give_amount),0) as give_amount,coalesce(sum(give_count),0) as give_count,coalesce(sum(differ_amount),0) as differ_amount from pos_opter_changshift");
		//sql.append(" where store_id = ? and report_date=? and cashier_num=? and pos_num = ? group by jzid) pd2");
		sql.append(" where store_id = ? and report_date=?  and pos_num = ? group by jzid) pd2");
		sql.append(" on pd.jzid=pd2.jzid order by pd.jzid desc");

		SqlRowSet rs = posDao.query4SqlRowSet(sql.toString(), new Object[]
		//{ organId, reportDate, optNum, posNum, startTimestamp, endTimestamp, organId, reportDate, optNum, posNum });
		{ organId, reportDate, posNum, startTimestamp, endTimestamp, organId, reportDate, posNum });
		int i=0;
		while (rs.next())
		{
			JSONObject json = new JSONObject();
			json.put("jzid", rs.getInt("jzid"));
			json.put("payment_name", rs.getString("payment_name"));
			json.put("payment_amount", rs.getDouble("amount"));
			json.put("payment_count", rs.getInt("count"));
			json.put("give_amount", 0);
			json.put("give_count", 0);
			json.put("differ_amount", 0);
			json.put("can_modify", "Y");
			json.put("sort_num", String.valueOf(++i));
			json.put("parent_sort_num", "0");
			arr.add(json);
			
			//查询优惠券明细
			if(SysDictionary.PAYMENT_CLASS_COUPONS.equals(rs.getString("payment_class")))
			{
				String isShow = posDao.getSysParameter(tenantId, organId, "changshift_is_show_coupons_details");
				if("1".equals(isShow))
				{
					String supOrderCode = String.valueOf(i);
					
					StringBuilder couponSql = new StringBuilder("");
					couponSql.append("select pd.*,pd2.give_amount,pd2.give_count,pd2.differ_amount from (");
					couponSql.append("select coalesce(pc.type_id,bp.jzid) as type_id,coalesce(pc.deal_name,bp.name) as deal_name,sum(coalesce(pc.deal_value*pc.discount_num,bp.currency_amount)) as discount_money,sum(coalesce(pc.discount_num,bp.count)) as discount_num from pos_bill_payment bp ");
					couponSql.append("left join pos_bill_payment_coupons pc on bp.tenancy_id=pc.tenancy_id and bp.bill_num=pc.bill_num and bp.payment_uid=pc.payment_id left join pos_bill bi on bp.tenancy_id=bi.tenancy_id and bp.bill_num=bi.bill_num ");
					//couponSql.append("where bp.type=? and bi.bill_property=? and bi.store_id = ? and bi.report_date = ? and bi.cashier_num=? and bi.pos_num=? and bi.payment_time > ? and bi.payment_time < ? group by bp.jzid,bp.name,pc.type_id,pc.deal_name) pd ");
					couponSql.append("where bp.type=? and bi.bill_property=? and bi.store_id = ? and bi.report_date = ?  and bi.pos_num=? and bi.payment_time > ? and bi.payment_time < ? group by bp.jzid,bp.name,pc.type_id,pc.deal_name) pd ");
					//couponSql.append("left join (select jzid,coalesce(sum(give_amount),0) as give_amount,coalesce(sum(give_count),0) as give_count,coalesce(sum(differ_amount),0) as differ_amount from pos_opter_changshift where store_id = ? and report_date=? and cashier_num=? and pos_num = ? group by jzid) pd2 on pd.type_id=pd2.jzid order by pd.type_id desc");
					couponSql.append("left join (select jzid,coalesce(sum(give_amount),0) as give_amount,coalesce(sum(give_count),0) as give_count,coalesce(sum(differ_amount),0) as differ_amount from pos_opter_changshift where store_id = ? and report_date=?  and pos_num = ? group by jzid) pd2 on pd.type_id=pd2.jzid order by pd.type_id desc");

					SqlRowSet couponRs = posDao.query4SqlRowSet(couponSql.toString(), new Object[]
					//{ SysDictionary.PAYMENT_CLASS_COUPONS,SysDictionary.BILL_PROPERTY_CLOSED, organId, reportDate, optNum, posNum, startTimestamp, endTimestamp, organId, reportDate, optNum, posNum });
					{ SysDictionary.PAYMENT_CLASS_COUPONS,SysDictionary.BILL_PROPERTY_CLOSED, organId, reportDate, posNum, startTimestamp, endTimestamp, organId, reportDate, posNum });
					while (couponRs.next())
					{
						JSONObject couponJson = new JSONObject();
						couponJson.put("jzid", couponRs.getInt("type_id"));
						couponJson.put("payment_name", "　"+couponRs.getString("deal_name"));
						couponJson.put("payment_amount", couponRs.getDouble("discount_money"));
						couponJson.put("payment_count", Double.valueOf(couponRs.getDouble("discount_num")).intValue());
						couponJson.put("give_amount", 0);
						couponJson.put("give_count", 0);
						couponJson.put("differ_amount", 0);
						couponJson.put("can_modify", "N");
						couponJson.put("sort_num", String.valueOf(++i));
						couponJson.put("parent_sort_num", supOrderCode);
						arr.add(couponJson);
					}
				}
			}
		}

		//线下团购
		StringBuilder offlineCouponSql = new StringBuilder(" select sum (pd.amount) as amount, sum (pd. count) as count, sum (pd2.give_amount) as give_amount, sum (pd2.give_count) as give_count, sum (pd2.differ_amount) as differ_amount,pd.jzid,payment_name from  ");
		offlineCouponSql.append(" ( select pa.jzid, coalesce (pw.payment_class, pa. type) as payment_class, '线下团购' as payment_name, sum (pa.currency_amount) as amount, ");
		offlineCouponSql.append("count ( case when pa.name_english = 'change' or bi.bill_state = 'cj01' or bi.bill_state = 'zdqx02' then null else 1 end ) as count from pos_bill_payment pa ");
		offlineCouponSql.append("left join payment_way pw on pa.jzid = pw. id and pa.tenancy_id = pw.tenancy_id left join sys_dictionary sd on pw.payment_name1 = sd.class_item_code ");
		offlineCouponSql.append("left join pos_bill bi on bi.tenancy_id = pa.tenancy_id and bi.store_id = pa.store_id and bi.bill_num = pa.bill_num where bi.store_id = ? and ");
		offlineCouponSql.append("bi.report_date = ? and bi.pos_num =? and bi.payment_time > ? and bi.payment_time < ? and pw.payment_class = 'groupbuycoupon' and pw.payment_name1 <> '美团团购' ");
		offlineCouponSql.append("group by pa.jzid, payment_name, payment_class, pa. type ) pd ");
		offlineCouponSql.append("left join ( select jzid, coalesce (sum(give_amount), 0) as give_amount, coalesce (sum(give_count), 0) as give_count, coalesce (sum(differ_amount), 0) as differ_amount from ");
		offlineCouponSql.append("pos_opter_changshift where store_id = ? and report_date = ? and pos_num = ? group by jzid ) pd2 on pd.jzid = pd2.jzid group by pd.payment_class, pd.payment_name,pd.jzid");


		SqlRowSet offlineRs= posDao.query4SqlRowSet(offlineCouponSql.toString(), new Object[]
				{ organId, reportDate, posNum, startTimestamp, endTimestamp, organId, reportDate, posNum });

		while (offlineRs.next()) {
			JSONObject json = new JSONObject();
			json.put("jzid", offlineRs.getInt("jzid"));
			json.put("payment_name", offlineRs.getString("payment_name"));
			json.put("payment_amount", offlineRs.getDouble("amount"));
			json.put("payment_count", offlineRs.getInt("count"));
			json.put("give_amount", 0);
			json.put("give_count", 0);
			json.put("differ_amount", 0);
			json.put("can_modify", "Y");
			json.put("sort_num", String.valueOf(++i));
			json.put("parent_sort_num", "0");
			arr.add(json);
		}


		obj.put("pay_item", arr);
		double bill_amount = 0d;// 营业应收
		double more_coupon = 0d; //多收礼券
		double sale_person_num = 0d;//消费客数
		double sale_billnum = 0d;//账单数

		double coupon_buy_price = 0d; // 美团票券用户实付
		double due = 0d; // 美团票券账单净收
		double tenancy_assume = 0d; // 美团票券商户承担
		double third_assume = 0d;	// 美团票券第三方承担
		double third_fee = 0d; 	// 美团票券第三方服务费

		StringBuilder sqlAmount = new StringBuilder("select sum(pb.discountk_amount) as discountk_amount,sum(pb.discountr_amount) as discountr_amount,sum(pb.maling_amount) as maling_amount,sum(pb.givi_amount) as givi_amount,sum(pb.more_coupon) as more_coupon,sum(coalesce(pb.shop_real_amount,0)) as shop_real_amount,sum(coalesce(pb.platform_charge_amount,0)) as platform_charge_amount,sum(coalesce(service_amount,0)) as service_amount,sum(coalesce(pb.coupon_buy_price,0)) as coupon_buy_price, sum(coalesce(pb.due,0)) as due, sum(coalesce(pb.tenancy_assume,0)) as tenancy_assume, sum(coalesce(pb.third_assume,0)) as third_assume, sum(coalesce(pb.third_fee,0)) as third_fee,");
		sqlAmount.append("coalesce(sum(guest), 0) as sale_person_num,count(case when coalesce(pb.bill_state, 'zc') not in ('CJ01', 'ZDQX02') then pb.bill_num end) as sale_billnum,sum(pb.bill_amount) as bill_amount");
		//sqlAmount.append(" from pos_bill pb where pb.store_id = ? and pb.report_date = ? and pb.payment_time > ? and pb.payment_time < ? and pb.cashier_num = ? and pb.pos_num =? and pb.bill_property = ?");
		sqlAmount.append(" from pos_bill pb where pb.store_id = ? and pb.report_date = ? and pb.payment_time > ? and pb.payment_time < ?  and pb.pos_num =? and pb.bill_property = ?");
		SqlRowSet rsAmount = posDao.query4SqlRowSet(sqlAmount.toString(), new Object[]
		//{ organId, reportDate, startTimestamp, endTimestamp, optNum, posNum,SysDictionary.BILL_PROPERTY_CLOSED });
		{ organId, reportDate, startTimestamp, endTimestamp, posNum,SysDictionary.BILL_PROPERTY_CLOSED });
		if (rsAmount.next())
		{
			bill_amount =  rsAmount.getDouble("bill_amount");
			sale_person_num = rsAmount.getDouble("sale_person_num");
			sale_billnum =  rsAmount.getDouble("sale_billnum");
			more_coupon =  rsAmount.getDouble("more_coupon");

			// 折扣
			obj.put("discountk_amount", rsAmount.getDouble("discountk_amount"));
			// 折让
			obj.put("discountr_amount", rsAmount.getDouble("discountr_amount"));
			// 抹零
			obj.put("maling_amount", rsAmount.getDouble("maling_amount"));
			// 奉送
			obj.put("givi_amount", rsAmount.getDouble("givi_amount"));
			// 多收礼券
			obj.put("more_coupon", more_coupon);
			// 商家实收
			obj.put("shop_real_amount", rsAmount.getDouble("shop_real_amount"));
			// 外卖平台收取金额
			obj.put("platform_charge_amount", rsAmount.getDouble("platform_charge_amount"));
			// 服务费
			obj.put("service_amount", rsAmount.getDouble("service_amount"));
			// 营业应收
			obj.put("bill_amount", bill_amount);
			//消费客数
			obj.put("sale_person_num", sale_person_num);
			//账单数
			obj.put("sale_billnum", sale_billnum);
			//人均均值
			if(sale_person_num!=0){
				obj.put("sale_person_average", DoubleHelper.div(bill_amount+more_coupon,sale_person_num,2));
			}else{
				obj.put("sale_person_average",0);
			}
			//账单均值
			if(sale_person_num!=0){
				obj.put("sale_billaverage", DoubleHelper.div(bill_amount+more_coupon,sale_billnum,2));
			}else{
				obj.put("sale_billaverage",0);
			}

			coupon_buy_price = rsAmount.getDouble("coupon_buy_price");
			due = rsAmount.getDouble("due");
			tenancy_assume = rsAmount.getDouble("tenancy_assume");
			third_assume = rsAmount.getDouble("third_assume");
			third_fee = rsAmount.getDouble("third_fee");

			// 美团票券虚收  =  商家承担 + 第三方服务费
			obj.put("tenancy_assume", DoubleHelper.round(tenancy_assume + third_fee, 2));
			// 美团票券第三方承担
			obj.put("third_assume", DoubleHelper.round(third_assume, 2));

			// 顾客实付= 账单应收-第三方支付优惠(商家承担 + 第三方承担)
			double customer_real_pay = DoubleHelper.round(bill_amount - tenancy_assume - third_assume, 2);
			// 账单净收 = 账单应收 - 商家承担优惠 - 票券服务费
			double bill_due = DoubleHelper.round(bill_amount - tenancy_assume - third_fee, 2) ;
			double service_amount = rsAmount.getDouble("service_amount");
			// 菜品净收 = 账单净收- 服务费
			double item_actual = DoubleHelper.round(bill_due - service_amount, 2);

			// 顾客实付
			obj.put("customer_real_pay", customer_real_pay);
			// 账单净收
			obj.put("bill_due", bill_due);
			// 菜品净收
			obj.put("item_actual", item_actual);
		}
		double  payment_total = 0d;//营业实收  = 收款方式合计
		StringBuffer paymentTotalSql = new StringBuffer(" select coalesce(sum(p.currency_amount), 0) as payment_total  from pos_bill_payment p LEFT JOIN pos_bill pb on  p.bill_num = pb.bill_num ");
		//paymentTotalSql.append(" where pb.store_id = ? and pb.report_date = ? and pb.payment_time > ? and pb.payment_time < ? and pb.bill_property = ?  and pb.cashier_num = ? and pb.pos_num =?");
		paymentTotalSql.append(" where pb.store_id = ? and pb.report_date = ? and pb.payment_time > ? and pb.payment_time < ? and pb.bill_property = ?   and pb.pos_num =?");
		SqlRowSet paymentTotalRs = posDao.query4SqlRowSet(paymentTotalSql.toString(), new Object[]
				//{ organId, reportDate, startTimestamp, endTimestamp ,SysDictionary.BILL_PROPERTY_CLOSED,optNum, posNum});
				{ organId, reportDate, startTimestamp, endTimestamp ,SysDictionary.BILL_PROPERTY_CLOSED, posNum});
		if(paymentTotalRs.next()){
			payment_total = paymentTotalRs.getDouble("payment_total");
		}
		obj.put("payment_total",payment_total);
		obj.put("real_amount",DoubleHelper.sub(payment_total, more_coupon, scale));
		
		double  no_income_money = 0d;//不计收入付款方式金额
		StringBuffer noIncomeMoneySql = new StringBuffer(" select sum(case when w.if_income='0' then coalesce(p.currency_amount, 0) else 0 end)  no_income_money  from pos_bill_payment p LEFT JOIN pos_bill pb on  p.bill_num = pb.bill_num ");
		noIncomeMoneySql.append(" left join (select id,payment_class,if_income from payment_way where status='1') w on p.jzid = w.id");
		//noIncomeMoneySql.append(" where pb.store_id = ? and pb.report_date = ? and pb.payment_time > ? and pb.payment_time < ? and pb.bill_property = ?  and pb.cashier_num = ? and pb.pos_num =?");
		noIncomeMoneySql.append(" where pb.store_id = ? and pb.report_date = ? and pb.payment_time > ? and pb.payment_time < ? and pb.bill_property = ?   and pb.pos_num =?");
		SqlRowSet noIncomeMoneyRs = posDao.query4SqlRowSet(noIncomeMoneySql.toString(), new Object[]
				//{ organId, reportDate, startTimestamp, endTimestamp ,SysDictionary.BILL_PROPERTY_CLOSED,optNum, posNum});
				{ organId, reportDate, startTimestamp, endTimestamp ,SysDictionary.BILL_PROPERTY_CLOSED, posNum});
		if(noIncomeMoneyRs.next()){
			no_income_money = noIncomeMoneyRs.getDouble("no_income_money");
		}
		obj.put("no_income_money",no_income_money);
		obj.put("real_income",payment_total-no_income_money);//付款实际收入
		
		StringBuffer tableDataSql = new StringBuffer("select coalesce(count(t.id),0)  as tables_num, coalesce(sum(t.seat_counts), 0) as seat_num from tables_info t where t.tenancy_id=? and t.organ_id=? and t.valid_state = '1' ");
		SqlRowSet tableDataRs = posDao.query4SqlRowSet(tableDataSql.toString(), new Object[]
		{ tenantId, organId });
		
		if(tableDataRs.next()){
			double tables_num =  tableDataRs.getDouble("tables_num");//桌台数
			double seat_num =  tableDataRs.getDouble("seat_num");//座位数
			obj.put("tables_num", tables_num);
			obj.put("seat_num", seat_num);
			//翻台率 =【消费账单】/【桌台数】
			if(tables_num != 0 ){
				obj.put("num_ft", DoubleHelper.div(sale_billnum,tables_num,2));//翻台率
			}else{
				obj.put("num_ft", 0d);//翻台率
			}
			// 上座率 = 【消费客数】/【座位数】
			if(seat_num != 0 ){
				obj.put("num_sz", DoubleHelper.div(sale_person_num,seat_num,2));//上座率
			}else{
				obj.put("num_sz", 0d);//上座率
			}
		}
		if ("1".equals(formatState) && "02".equals(formatMode))
		{// 自助
			obj.put("back_num", back_num);
			obj.put("back_money", back_money);

			// 单品退菜数量和金额
			StringBuilder singleRetreatSb = new StringBuilder(
					"select coalesce(count(b.id),'0') as single_retreat_count,coalesce(sum(bi.real_amount),'0') as single_retreat_amount from pos_bill_batch b inner join pos_bill_item bi on b.tenancy_id=bi.tenancy_id and b.store_id=bi.store_id and b.bill_num=bi.bill_num and b.batch_num=bi.batch_num");
			//singleRetreatSb.append(" where b.tenancy_id=? and b.store_id =? and b.payment_time >? and b.payment_time <? and b.cashier_num =? and b.pos_num=? and bi.item_remark='TC01' and bi.item_property in ('SINGLE','SETMEAL')");
			singleRetreatSb.append(" where b.tenancy_id=? and b.store_id =? and b.payment_time >? and b.payment_time <?  and b.pos_num=? and bi.item_remark='TC01' and bi.item_property in ('SINGLE','SETMEAL')");

			SqlRowSet singleRetreatRs = posDao.query4SqlRowSet(singleRetreatSb.toString(), new Object[]
			//{ tenantId, organId, startTimestamp, endTimestamp, optNum, posNum });
			{ tenantId, organId, startTimestamp, endTimestamp, posNum });
			if (singleRetreatRs.next())
			{
				retreat_num = singleRetreatRs.getDouble("single_retreat_count");
				retreat_money = singleRetreatRs.getDouble("single_retreat_amount");
			}
			obj.put("single_retreat_count", retreat_num);
			obj.put("single_retreat_amount", retreat_money);
		}
		else
		{
			// 用来获取退款数量和退款金额
			StringBuilder qpaidMoney = new StringBuilder("select count(bi.id) as back_num,sum(bi.payment_amount) as back_money from pos_bill bi");
			qpaidMoney.append(" inner join pos_bill bi2 on bi.bill_num=bi2.copy_bill_num and bi.store_id=bi2.store_id and bi.bill_state='ZDQX02' and bi.bill_property='CLOSED' and bi2.bill_state='CJ01' and bi2.bill_property='CLOSED'");
			qpaidMoney.append(" where bi2.payment_time>? and bi2.payment_time<? and bi2.store_id = ?  ");
			//qpaidMoney.append(" and bi2.cashier_num =? and bi2.pos_num =?");
			qpaidMoney.append("  and bi2.pos_num =?");
			SqlRowSet qrst = posDao.query4SqlRowSet(qpaidMoney.toString(), new Object[]
			//{ startTimestamp, endTimestamp, organId, optNum, posNum });
			{ startTimestamp, endTimestamp, organId, posNum });
			if (qrst.next())
			{
				back_num = qrst.getDouble("back_num");
				back_money = qrst.getDouble("back_money");
			}
			obj.put("back_num", back_num);
			obj.put("back_money", back_money);

			// 单品退菜数量和金额
			StringBuilder singleRetreatSb = new StringBuilder(
					"select coalesce(count(p.bill_num),'0') as single_retreat_count,sum(p.payment_amount) as single_retreat_amount from pos_bill p inner join pos_bill pb on p.copy_bill_num = pb.bill_num and (pb.bill_state is null or pb.bill_state <> 'ZDQX02')");
			//singleRetreatSb.append(" where p .payment_time >? and p .payment_time <? and p.cashier_num =? and p.pos_num=? and p .tenancy_id =? and p .store_id =? and p.bill_state = 'CJ01'");
			singleRetreatSb.append(" where p .payment_time >? and p .payment_time <?  and p.pos_num=? and p .tenancy_id =? and p .store_id =? and p.bill_state = 'CJ01'");
			SqlRowSet singleRetreatRs = posDao.query4SqlRowSet(singleRetreatSb.toString(), new Object[]
			//{ startTimestamp, endTimestamp, optNum, posNum, tenantId, organId });
			{ startTimestamp, endTimestamp, posNum, tenantId, organId });
			if (singleRetreatRs.next())
			{
				retreat_num = singleRetreatRs.getDouble("single_retreat_count");
				retreat_money = singleRetreatRs.getDouble("single_retreat_amount");
			}
			obj.put("single_retreat_count", retreat_num);
			obj.put("single_retreat_amount", retreat_money);
		}

		// 零找金


		String qamount = new String("select coalesce(sum(amount),'0') as amount from pos_byjdc_log where pos_num = ?  " +
				"and last_updatetime > ? and last_updatetime < ? and report_date = ? and store_id = ? and opt_type = ?");
		double amount = 0;
		SqlRowSet rsChangetAmount = posDao.query4SqlRowSet(qamount, new Object[]{ posNum, startTimestamp, endTimestamp, reportDate, organId, "changet"});
		if (rsChangetAmount.next())
		{
			amount = rsChangetAmount.getDouble("amount");
		}
		obj.put("changet_amount", amount);


		qamount = new String("select coalesce(sum(amount),'0') as amount from pos_byjdc_log where pos_num = ? and opt_num = ? and last_updatetime > ? and last_updatetime < ? and report_date = ? and store_id = ? and opt_type = ?");
		// 抽大钞
		double bills_amount = 0;
		optNum = null;
		SqlRowSet billsRs = posDao.query4SqlRowSet(qamount, new Object[]
		{ posNum, optNum, startTimestamp, endTimestamp, reportDate, organId, "bills" });
		if (billsRs.next())
		{
			bills_amount = billsRs.getDouble("amount");
		}
		obj.put("bills", bills_amount);
		getBillItemReport(tenantId, organId, null,posNum, startTimestamp, endTimestamp, obj, reportDate);
	
	}
	
	/** 统计当前操作人所有机台营业收入
	 * @param tenantId
	 * @param organId
	 * @param reportDate
	 * @param optNum
	 * @param posNum
	 * @param startTimestamp
	 * @param endTimestamp
	 * @param obj
	 * @param formatState
	 * @param formatMode
	 * @param scale
	 * @throws Exception
	 */
	@SuppressWarnings("unused")
	private void getShiftDataForBusinessByAllPos(String tenantId, int organId,Date reportDate,String optNum,Timestamp startTimestamp,Timestamp endTimestamp,JSONObject obj,String formatState,String formatMode,int scale) throws Exception
	{
		double back_num = 0;
		double back_money = 0;
		double retreat_num = 0;
		double retreat_money = 0;
		JSONArray arr = new JSONArray();

		StringBuilder sql = new StringBuilder("select pd.*,pd2.give_amount,pd2.give_count,pd2.differ_amount from (");
		sql.append(" select pa.jzid,(case when pw.payment_class='").append(SysDictionary.PAYMENT_CLASS_CASH).append("' then sd.class_item else pw.payment_name1 end) as payment_name,sum(pa.currency_amount) as amount,");
		sql.append(" count(case when pa.name_english='").append(SysDictionary.PAYMENT_CHANGE_ENGLISH_NAME).append("' or bi.bill_state='").append(SysDictionary.BILL_STATE_CJ01).append("' or bi.bill_state='").append(SysDictionary.BILL_STATE_ZDQX02).append("' then null else 1 end) as count");
		sql.append(" from pos_bill_payment pa left join payment_way pw on pa.jzid=pw.id and pa.tenancy_id=pw.tenancy_id and pw.status='1'");
		sql.append(" left join sys_dictionary sd on pw.payment_name1 = sd.class_item_code left join pos_bill bi on bi.tenancy_id=pa.tenancy_id and bi.store_id=pa.store_id and bi.bill_num=pa.bill_num");
		//sql.append(" where bi.store_id = ? and bi.report_date = ?  and bi.payment_time > ? and bi.payment_time < ? and bi.cashier_num = ? group by pa.jzid,payment_name) pd");
		sql.append(" where bi.store_id = ? and bi.report_date = ?  and bi.payment_time > ? and bi.payment_time < ?  group by pa.jzid,payment_name) pd");
		sql.append(" left join (select jzid,coalesce(sum(give_amount),0) as give_amount,coalesce(sum(give_count),0) as give_count,coalesce(sum(differ_amount),0) as differ_amount from pos_opter_changshift where store_id = ? and report_date=?   group by jzid) pd2");
		sql.append(" on pd.jzid=pd2.jzid order by pd.jzid desc");
		
		SqlRowSet rs = posDao.query4SqlRowSet(sql.toString(), new Object[]
		//{ organId, reportDate, startTimestamp, endTimestamp,optNum, organId, reportDate });
		{ organId, reportDate, startTimestamp, endTimestamp, organId, reportDate });

		while (rs.next())
		{
			JSONObject json = new JSONObject();
			json.put("jzid", rs.getInt("jzid"));
			json.put("payment_name", rs.getString("payment_name"));
			json.put("payment_amount", rs.getDouble("amount"));
			json.put("payment_count", rs.getInt("count"));
			json.put("give_amount", 0);
			json.put("give_count", 0);
			json.put("differ_amount", 0);
			json.put("can_modify", "Y");
			arr.add(json);
		}
		obj.put("pay_item", arr);
		double bill_amount = 0d;// 营业应收
		double more_coupon = 0d; //多收礼券
		double sale_person_num = 0d;//消费客数
		double sale_billnum = 0d;//账单数

		double coupon_buy_price = 0d; // 美团票券用户实付
		double due = 0d; // 美团票券账单净收
		double tenancy_assume = 0d; // 美团票券商户承担
		double third_assume = 0d;	// 美团票券第三方承担
		double third_fee = 0d; 	// 美团票券第三方服务费

		StringBuilder sqlAmount = new StringBuilder("select sum(pb.discountk_amount) as discountk_amount,sum(pb.discountr_amount) as discountr_amount,sum(pb.maling_amount) as maling_amount,sum(pb.givi_amount) as givi_amount,sum(pb.more_coupon) as more_coupon,sum(coalesce(pb.shop_real_amount,0)) as shop_real_amount,sum(coalesce(pb.platform_charge_amount,0)) as platform_charge_amount,sum(coalesce(service_amount,0)) as service_amount,sum(coalesce(pb.coupon_buy_price,0)) as coupon_buy_price, sum(coalesce(pb.due,0)) as due, sum(coalesce(pb.tenancy_assume,0)) as tenancy_assume, sum(coalesce(pb.third_assume,0)) as third_assume, sum(coalesce(pb.third_fee,0)) as third_fee, ");

		sqlAmount.append("coalesce(sum(guest), 0) as sale_person_num,");
		sqlAmount.append("count(case when coalesce(pb.bill_state, 'zc') not in ('CJ01', 'ZDQX02') then pb.bill_num end) as sale_billnum,");
		sqlAmount.append("sum(pb.bill_amount) as bill_amount from pos_bill pb ");
		//sqlAmount.append(" where pb.store_id = ? and pb.report_date = ? and pb.payment_time > ? and pb.payment_time < ? and pb.bill_property = ? and  pb.cashier_num = ? ");
		sqlAmount.append(" where pb.store_id = ? and pb.report_date = ? and pb.payment_time > ? and pb.payment_time < ? and pb.bill_property = ? ");
		SqlRowSet rsAmount = posDao.query4SqlRowSet(sqlAmount.toString(), new Object[]
		//{ organId, reportDate, startTimestamp, endTimestamp ,SysDictionary.BILL_PROPERTY_CLOSED,optNum});
		{ organId, reportDate, startTimestamp, endTimestamp ,SysDictionary.BILL_PROPERTY_CLOSED});
		while (rsAmount.next())
		{
			bill_amount =  rsAmount.getDouble("bill_amount");
			sale_person_num = rsAmount.getDouble("sale_person_num");
			sale_billnum =  rsAmount.getDouble("sale_billnum");
			more_coupon =  rsAmount.getDouble("more_coupon");
			// 折扣
			obj.put("discountk_amount", rsAmount.getDouble("discountk_amount"));
			// 折让
			obj.put("discountr_amount", rsAmount.getDouble("discountr_amount"));
			// 抹零
			obj.put("maling_amount", rsAmount.getDouble("maling_amount"));
			// 奉送
			obj.put("givi_amount", rsAmount.getDouble("givi_amount"));
			// 多收礼券
			obj.put("more_coupon", more_coupon);
			// 商家实收
			obj.put("shop_real_amount", rsAmount.getDouble("shop_real_amount"));
			// 外卖平台收取金额
			obj.put("platform_charge_amount", rsAmount.getDouble("platform_charge_amount"));
			// 服务费
			obj.put("service_amount", rsAmount.getDouble("service_amount"));
			// 营业应收
			obj.put("bill_amount", bill_amount);
			//消费客数
			obj.put("sale_person_num", sale_person_num);
			//账单数
			obj.put("sale_billnum", sale_billnum);
			//人均均值
			if(sale_person_num != 0){
				obj.put("sale_person_average", DoubleHelper.div(bill_amount+more_coupon,sale_person_num,2));
			}else{
				obj.put("sale_person_average",0);
			}
			//账单均值
			if(sale_person_num != 0){
				obj.put("sale_billaverage", DoubleHelper.div(bill_amount+more_coupon,sale_billnum,2));
			}else{
				obj.put("sale_billaverage",0);
			}

			coupon_buy_price = rsAmount.getDouble("coupon_buy_price");
			due = rsAmount.getDouble("due");
			tenancy_assume = rsAmount.getDouble("tenancy_assume");
			third_assume = rsAmount.getDouble("third_assume");
			third_fee = rsAmount.getDouble("third_fee");

			// 美团票券虚收  =  商家承担 + 第三方服务费
			obj.put("tenancy_assume", DoubleHelper.round(tenancy_assume + third_fee, 2));
			// 美团票券第三方承担
			obj.put("third_assume", DoubleHelper.round(third_assume, 2));

			// 顾客实付= 账单应收-第三方支付优惠(商家承担 + 第三方承担)
			double customer_real_pay = DoubleHelper.round(bill_amount - tenancy_assume - third_assume, 2);
			// 账单净收 = 账单应收 - 商家承担优惠 - 票券服务费
			double bill_due = DoubleHelper.round(bill_amount - tenancy_assume - third_fee, 2) ;
			double service_amount = rsAmount.getDouble("service_amount");
			// 菜品净收 = 账单净收- 服务费
			double item_actual = DoubleHelper.round(bill_due - service_amount, 2);

			// 顾客实付
			obj.put("customer_real_pay", customer_real_pay);
			// 账单净收
			obj.put("bill_due", bill_due);
			// 菜品净收
			obj.put("item_actual", item_actual);

		}
		double  payment_total = 0d;//营业实收  = 收款方式合计
		StringBuffer paymentTotalSql = new StringBuffer(" select coalesce(sum(p.currency_amount), 0) as payment_total  from pos_bill_payment p LEFT JOIN pos_bill pb on  p.bill_num = pb.bill_num ");
		//paymentTotalSql.append(" where pb.store_id = ? and pb.report_date = ? and pb.payment_time > ? and pb.payment_time < ? and pb.bill_property = ? and  pb.cashier_num = ?");
		paymentTotalSql.append(" where pb.store_id = ? and pb.report_date = ? and pb.payment_time > ? and pb.payment_time < ? and pb.bill_property = ? ");
		SqlRowSet paymentTotalRs = posDao.query4SqlRowSet(paymentTotalSql.toString(), new Object[]
				//{ organId, reportDate, startTimestamp, endTimestamp ,SysDictionary.BILL_PROPERTY_CLOSED,optNum});
				{ organId, reportDate, startTimestamp, endTimestamp ,SysDictionary.BILL_PROPERTY_CLOSED});
		if(paymentTotalRs.next()){
			payment_total = paymentTotalRs.getDouble("payment_total");
		}
		obj.put("payment_total",payment_total);
		obj.put("real_amount",DoubleHelper.sub(payment_total, more_coupon, scale));
		
		double  no_income_money = 0d;//不计收入付款方式金额
		StringBuffer noIncomeMoneySql = new StringBuffer(" select sum(case when w.if_income='0' then coalesce(p.currency_amount, 0) else 0 end)  no_income_money  from pos_bill_payment p LEFT JOIN pos_bill pb on  p.bill_num = pb.bill_num ");
		noIncomeMoneySql.append(" left join (select id,payment_class,if_income from payment_way where status='1') w on p.jzid = w.id");
		noIncomeMoneySql.append(" where pb.store_id = ? and pb.report_date = ? and pb.payment_time > ? and pb.payment_time < ? and pb.bill_property = ? ");
		SqlRowSet noIncomeMoneyRs = posDao.query4SqlRowSet(noIncomeMoneySql.toString(), new Object[]
				//{ organId, reportDate, startTimestamp, endTimestamp ,SysDictionary.BILL_PROPERTY_CLOSED,optNum});
				{ organId, reportDate, startTimestamp, endTimestamp ,SysDictionary.BILL_PROPERTY_CLOSED});
		if(noIncomeMoneyRs.next()){
			no_income_money = noIncomeMoneyRs.getDouble("no_income_money");
		}
		obj.put("no_income_money",no_income_money);
		obj.put("real_income",payment_total-no_income_money);//付款实际收入
		
		StringBuffer tableDataSql = new StringBuffer("select coalesce(count(t.id),0) as tables_num, coalesce(sum(t.seat_counts), 0) as seat_num from tables_info t where t.tenancy_id=? and t.organ_id=? and t.valid_state = '1' ");
		SqlRowSet tableDataRs = posDao.query4SqlRowSet(tableDataSql.toString(), new Object[]{tenantId,organId});
		if(tableDataRs.next()){
			double tables_num =  tableDataRs.getDouble("tables_num");//桌台数
			double seat_num =  tableDataRs.getDouble("seat_num");//座位数
			obj.put("tables_num", tables_num);
			obj.put("seat_num", seat_num);
			//翻台率 =【消费账单】/【桌台数】
			if(tables_num != 0 ){
				obj.put("num_ft", DoubleHelper.div(sale_billnum,tables_num,2));//翻台率
			}else{
				obj.put("num_ft", 0d);//翻台率
			}
			// 上座率 = 【消费客数】/【座位数】
			if(seat_num != 0 ){
				obj.put("num_sz", DoubleHelper.div(sale_person_num,seat_num,2));//上座率
			}else{
				obj.put("num_sz", 0d);//上座率
			}
		}
		if ("1".equals(formatState) && "02".equals(formatMode))
		{// 自助
			// 用来获取退款数量和退款金额
			obj.put("back_num", back_num);
			obj.put("back_money", back_money);

			// 查询单品退菜数量和金额
			StringBuilder singleRetreatSb = new StringBuilder(
					"select coalesce(count(b.id),'0') as single_retreat_count,coalesce(sum(bi.real_amount),'0') as single_retreat_amount from pos_bill_batch b inner join pos_bill_item bi on b.tenancy_id=bi.tenancy_id and b.store_id=bi.store_id and b.bill_num=bi.bill_num and b.batch_num=bi.batch_num");
			//singleRetreatSb.append(" where b.tenancy_id=? and b.store_id =? and b.payment_time >? and b.payment_time <? and b.cashier_num =? and bi.item_remark='TC01' and bi.item_property in ('SINGLE','SETMEAL') ");
			singleRetreatSb.append(" where b.tenancy_id=? and b.store_id =? and b.payment_time >? and b.payment_time <? and bi.item_remark='TC01' and bi.item_property in ('SINGLE','SETMEAL') ");
			SqlRowSet singleRetreatRs = posDao.query4SqlRowSet(singleRetreatSb.toString(), new Object[]
			//{ tenantId, organId, startTimestamp, endTimestamp, optNum });
			{ tenantId, organId, startTimestamp, endTimestamp });
			if (singleRetreatRs.next())
			{
				retreat_num = singleRetreatRs.getDouble("single_retreat_count");
				retreat_money = singleRetreatRs.getDouble("single_retreat_amount");
			}
			obj.put("single_retreat_count", retreat_num);
			obj.put("single_retreat_amount", retreat_money);
		}
		else
		{
			// 用来获取退款数量和退款金额
			StringBuilder qpaidMoney = new StringBuilder("select count(bi.id) as back_num,sum(bi.payment_amount) as back_money from pos_bill bi");
			qpaidMoney.append(" inner join pos_bill bi2 on bi.tenancy_id=bi2.tenancy_id and bi.store_id=bi2.store_id and bi.bill_num=bi2.copy_bill_num and bi.bill_state='ZDQX02' and bi.bill_property='CLOSED' and bi2.bill_state='CJ01' and bi2.bill_property='CLOSED'");
			//qpaidMoney.append(" where bi2.tenancy_id=? and bi2.store_id =? and bi2.cashier_num =? and bi2.payment_time>? and bi2.payment_time<?");
			qpaidMoney.append(" where bi2.tenancy_id=? and bi2.store_id =?  and bi2.payment_time>? and bi2.payment_time<?");
			SqlRowSet qrst = posDao.query4SqlRowSet(qpaidMoney.toString(), new Object[]
			//{ tenantId, organId, optNum, startTimestamp, endTimestamp });
			{ tenantId, organId, startTimestamp, endTimestamp });
			if (qrst.next())
			{
				back_num = qrst.getDouble("back_num");
				back_money = qrst.getDouble("back_money");
			}

			obj.put("back_num", back_num);
			obj.put("back_money", back_money);

			// 查询单品退菜数量和金额
			StringBuilder singleRetreatSb = new StringBuilder(
					"select coalesce(count(p.bill_num),'0') as single_retreat_count,coalesce(round(sum (p.payment_amount),2),0) as single_retreat_amount from pos_bill p inner join pos_bill pb on p.copy_bill_num = pb.bill_num and (pb.bill_state is null or pb.bill_state <> 'ZDQX02')");
			//singleRetreatSb.append(" where p .payment_time >? and p .payment_time <? and p.cashier_num =? and p .tenancy_id =? and p .store_id =? and p.bill_state = 'CJ01'");
			singleRetreatSb.append(" where p .payment_time >? and p .payment_time <? and p .tenancy_id =? and p .store_id =? and p.bill_state = 'CJ01'");
			SqlRowSet singleRetreatRs = posDao.query4SqlRowSet(singleRetreatSb.toString(), new Object[]
			//{ startTimestamp, endTimestamp, optNum, tenantId, organId });
			{ startTimestamp, endTimestamp, tenantId, organId });
			if (singleRetreatRs.next())
			{
				retreat_num = singleRetreatRs.getDouble("single_retreat_count");
				retreat_money = singleRetreatRs.getDouble("single_retreat_amount");
			}
			obj.put("single_retreat_count", retreat_num);
			obj.put("single_retreat_amount", retreat_money);
		}

		//String qamount = new String("select coalesce(sum(amount),'0') as amount from pos_byjdc_log where opt_num = ? and last_updatetime > ? and last_updatetime < ? and report_date = ? and store_id = ? and opt_type = ?");
		String qamount = new String("select coalesce(sum(amount),'0') as amount from pos_byjdc_log where last_updatetime > ? and last_updatetime < ? and report_date = ? and store_id = ? and opt_type = ?");
		double amount = 0;
		SqlRowSet rsChangetAmount = posDao.query4SqlRowSet(qamount, new Object[]
		//{ optNum, startTimestamp, endTimestamp, reportDate, organId, "changet" });
		{  startTimestamp, endTimestamp, reportDate, organId, "changet" });
		if (rsChangetAmount.next())
		{
			amount = rsChangetAmount.getDouble("amount");
		}
		obj.put("changet_amount", amount);

		// 抽大钞
		double bills_money = 0;
		optNum = null;
		qamount = new String("select coalesce(sum(amount),'0') as amount from pos_byjdc_log where last_updatetime > ? and last_updatetime < ? and report_date = ? and store_id = ? and opt_type = ?");
		SqlRowSet billsRs = posDao.query4SqlRowSet(qamount, new Object[]
		//{ optNum, startTimestamp, endTimestamp, reportDate, organId, "bills" });
		{  startTimestamp, endTimestamp, reportDate, organId, "bills" });
		if (billsRs.next())
		{
			bills_money = billsRs.getDouble("amount");
		}
		obj.put("bills", bills_money);
		getBillItemReport(tenantId, organId, optNum,null, startTimestamp, endTimestamp, obj, reportDate);
	
	}


	/**
	 * 让利明细
	 * @param tenantId
	 * @param organId
	 * @param reportDate
	 * @param optNum
	 * @param posNum
	 * @param startTimestamp
	 * @param endTimestamp
	 * @param obj
	 * @param formatState
	 * @param formatMode
	 * @param scale
	 * @throws Exception
	 */
	private void getShiftDataForConcessionDetails(String tenantId, int organId,Date reportDate,String optNum, String posNum,Timestamp startTimestamp,Timestamp endTimestamp,JSONObject obj,String formatState,String formatMode,int scale) throws Exception
	{
		JSONArray arr = new JSONArray();
		Double concession_money = 0d;
		Integer concession_total_count = 0;

		StringBuilder couponSql = new StringBuilder("");
		couponSql.append("SELECT P .jzid, w.payment_name1, SUM (P .currency_amount) AS discount_money, SUM (P . COUNT) AS discount_num FROM pos_bill_payment P ");
		couponSql.append("LEFT JOIN pos_bill bi ON P .tenancy_id = bi.tenancy_id AND P .bill_num = bi.bill_num LEFT JOIN payment_way w ON P .jzid = w. ID " );
		//couponSql.append("WHERE w.payment_class IN ( 'groupbuycoupon', 'other', 'freesingle', 'coupons' ) and bi.bill_property =? AND bi.store_id = ? " );
		couponSql.append("WHERE w.payment_class IN ('other', 'freesingle', 'coupons' ) and w.payment_name1 not in ('口碑单品券','辰森会员卡结账')  and bi.bill_property =? AND bi.store_id = ? " );
		couponSql.append("AND bi.report_date = ? AND bi.pos_num =? AND bi.payment_time > ? AND bi.payment_time < ? GROUP BY P .jzid, w.payment_name1;");

		SqlRowSet couponRs = posDao.query4SqlRowSet(couponSql.toString(), new Object[]
				{ SysDictionary.BILL_PROPERTY_CLOSED, organId, reportDate, posNum, startTimestamp, endTimestamp});
		while (couponRs.next())
		{
			JSONObject concessionJson = new JSONObject();
            Double discount_money = couponRs.getDouble("discount_money");
			concessionJson.put("payment_name", "　"+couponRs.getString("payment_name1"));
			concessionJson.put("discount_money",discount_money);
			concessionJson.put("discount_num", Double.valueOf(couponRs.getDouble("discount_num")).intValue());
            concession_money = DoubleHelper.add(concession_money, discount_money, 4);
			concession_total_count = concession_total_count+Double.valueOf(couponRs.getDouble("discount_num")).intValue();
			arr.add(concessionJson);
		}

		//折扣方案
		StringBuilder discountCaseSql = new StringBuilder("");
		discountCaseSql.append("SELECT bi.discount_case_id, SUM (bi.discountk_amount) AS discount_money, hdc.discount_case_name AS payment_name, COUNT (bi.discount_case_id) AS discount_num ");
		discountCaseSql.append(" FROM pos_bill bi LEFT JOIN hq_discount_case hdc ON bi.discount_case_id = hdc. ID ");
		discountCaseSql.append(" WHERE bi.discount_mode_id = 2 AND bi.discount_case_id IS NOT NULL AND bi.bill_property =? AND bi.store_id = ? AND bi.report_date = ? AND bi.pos_num =? AND bi.payment_time > ? AND bi.payment_time < ? ");
		discountCaseSql.append(" GROUP BY bi.discount_case_id, hdc.discount_case_name");
		SqlRowSet discountCaseRe = posDao.query4SqlRowSet(discountCaseSql.toString(), new Object[]
				{ SysDictionary.BILL_PROPERTY_CLOSED, organId, reportDate, posNum, startTimestamp, endTimestamp});

		while (discountCaseRe.next())
		{
			JSONObject concessionJson = new JSONObject();
			Double discount_money = discountCaseRe.getDouble("discount_money");
			concessionJson.put("payment_name", discountCaseRe.getString("payment_name"));
			concessionJson.put("discount_money",discount_money);
			concessionJson.put("discount_num", Double.valueOf(discountCaseRe.getDouble("discount_num")).intValue());
			concession_money = DoubleHelper.add(concession_money, discount_money, 4);
			concession_total_count = concession_total_count+Double.valueOf(discountCaseRe.getDouble("discount_num")).intValue();
			arr.add(concessionJson);
		}


		// 微生活 电子券
		StringBuilder wlifeSql = new StringBuilder("");
		wlifeSql.append("SELECT P .jzid,  case when w.payment_class='wlife_credit' then '微生活积分' when w.payment_class='wlife_coupon' then '会员电子券' end as payment_name,w.payment_class, SUM (P.currency_amount) AS discount_money, SUM (P . COUNT) AS discount_num FROM pos_bill_payment P ");
		wlifeSql.append("LEFT JOIN pos_bill bi ON P .tenancy_id = bi.tenancy_id AND P .bill_num = bi.bill_num LEFT JOIN payment_way w ON P .jzid = w. ID " );
		wlifeSql.append("WHERE w.payment_class IN ('wlife_credit','wlife_coupon') and bi.bill_property =? AND bi.store_id = ? " );
		wlifeSql.append("AND bi.report_date = ? AND bi.pos_num =? AND bi.payment_time > ? AND bi.payment_time < ? GROUP BY P .jzid,payment_name,w.payment_class;");

		SqlRowSet wlifeSet = posDao.query4SqlRowSet(wlifeSql.toString(), new Object[]
				{ SysDictionary.BILL_PROPERTY_CLOSED, organId, reportDate, posNum, startTimestamp, endTimestamp});
		while (wlifeSet.next())
		{
			JSONObject concessionJson = new JSONObject();
			Double discount_money = wlifeSet.getDouble("discount_money");
			concessionJson.put("payment_name", wlifeSet.getString("payment_name"));
			concessionJson.put("discount_money",discount_money);
			concessionJson.put("discount_num", Double.valueOf(wlifeSet.getDouble("discount_num")).intValue());
			concession_money = DoubleHelper.add(concession_money, discount_money, 4);
			concession_total_count = concession_total_count+Double.valueOf(wlifeSet.getDouble("discount_num")).intValue();
			arr.add(concessionJson);
		}

		//美团团购
		StringBuilder meituanSql = new StringBuilder("");
		meituanSql.append("SELECT P .jzid, w.payment_name1, SUM (P .currency_amount) AS discount_money, SUM (P . COUNT) AS discount_num FROM pos_bill_payment P ");
		meituanSql.append("LEFT JOIN pos_bill bi ON P .tenancy_id = bi.tenancy_id AND P .bill_num = bi.bill_num LEFT JOIN payment_way w ON P .jzid = w. ID " );
		meituanSql.append("WHERE w.payment_class = 'groupbuycoupon' and w.payment_name1='美团团购'  and bi.bill_property =? AND bi.store_id = ? " );
		meituanSql.append("AND bi.report_date = ? AND bi.pos_num =? AND bi.payment_time > ? AND bi.payment_time < ? GROUP BY P .jzid, w.payment_name1;");

		SqlRowSet meituanRs = posDao.query4SqlRowSet(meituanSql.toString(), new Object[]
				{ SysDictionary.BILL_PROPERTY_CLOSED, organId, reportDate, posNum, startTimestamp, endTimestamp});
		while (meituanRs.next())
		{
			JSONObject concessionJson = new JSONObject();
			Double discount_money = meituanRs.getDouble("discount_money");
			concessionJson.put("payment_name", "美团团购优惠");
			concessionJson.put("discount_money",discount_money);
			concessionJson.put("discount_num", Double.valueOf(meituanRs.getDouble("discount_num")).intValue());
			concession_money = DoubleHelper.add(concession_money, discount_money, 4);
			concession_total_count = concession_total_count+Double.valueOf(meituanRs.getDouble("discount_num")).intValue();
			arr.add(concessionJson);
		}


		//外卖优惠
		StringBuilder waimaiSql = new StringBuilder("");
		waimaiSql.append("SELECT P .jzid,case when w.payment_class='ele_pay' then '饿了么外卖优惠' when w.payment_class='baidu_pay' then '百度外卖优惠' when w.payment_class='meituan_pay' then '美团外卖优惠' end  as payment_name,w.payment_class, SUM (bi.discount_amount) AS discount_money, SUM (P.COUNT) AS discount_num FROM pos_bill_payment P ");
		waimaiSql.append("LEFT JOIN pos_bill bi ON P .tenancy_id = bi.tenancy_id AND P .bill_num = bi.bill_num LEFT JOIN payment_way w ON P .jzid = w. ID " );
		waimaiSql.append("WHERE w.payment_class in ('ele_pay','baidu_pay','meituan_pay')  and bi.bill_property =? AND bi.store_id = ? " );
		waimaiSql.append("AND bi.report_date = ? AND bi.pos_num =? AND bi.payment_time > ? AND bi.payment_time < ? GROUP BY P .jzid,payment_name,w.payment_class ;");

		SqlRowSet waimaiRe = posDao.query4SqlRowSet(waimaiSql.toString(), new Object[]
				{ SysDictionary.BILL_PROPERTY_CLOSED, organId, reportDate, posNum, startTimestamp, endTimestamp});
		while (waimaiRe.next())
		{
			JSONObject concessionJson = new JSONObject();
			Double discount_money = waimaiRe.getDouble("discount_money");
			concessionJson.put("payment_name", waimaiRe.getString("payment_name"));
			concessionJson.put("discount_money",discount_money);
			concessionJson.put("discount_num", Double.valueOf(waimaiRe.getDouble("discount_num")).intValue());
			concession_money = DoubleHelper.add(concession_money, discount_money, 4);
			concession_total_count = concession_total_count+Double.valueOf(waimaiRe.getDouble("discount_num")).intValue();
			arr.add(concessionJson);
		}


		StringBuilder concession = new StringBuilder("");
		concession.append("SELECT SUM (pi.item_amount)  as discount_money FROM pos_bill_item pi LEFT JOIN pos_bill bi ON pi.tenancy_id = bi.tenancy_id AND pi.store_id = bi.store_id AND pi.bill_num = bi.bill_num " );
		concession.append("LEFT JOIN crm_card_trading_list cctl ON cctl.tenancy_id = bi.tenancy_id AND cctl.store_id = bi.store_id AND ( cctl.third_bill_code = bi.bill_num OR cctl.third_bill_code = bi.order_num ) ");
		concession.append("WHERE cctl.operat_type IN ('03') AND pi.item_remark = 'FS02' AND bi.bill_property = ?  AND bi.store_id = ?  AND bi.report_date = ? AND bi.pos_num =? AND bi.payment_time > ? AND bi.payment_time < ? ");
		SqlRowSet concessionRs = posDao.query4SqlRowSet(couponSql.toString(), new Object[]
				{ SysDictionary.BILL_PROPERTY_CLOSED, organId, reportDate, posNum, startTimestamp, endTimestamp });
		while (concessionRs.next())
		{
			JSONObject concessionJson = new JSONObject();
            Double discount_money = concessionRs.getDouble("discount_money");
			concessionJson.put("payment_name","会员卡赠送");
			concessionJson.put("discount_money", discount_money);
			concessionJson.put("discount_num", "");
            concession_money = DoubleHelper.add(concession_money, discount_money, 4);
			arr.add(concessionJson);
		}
		obj.put("concession_details", arr);
		obj.put("concession_total_money", concession_money);
		obj.put("concession_total_count", concession_total_count);

        //让利总额 = 让利明细金额+折扣+折让
        concession_money = DoubleHelper.add(concession_money, obj.getDouble("discountk_amount"), 4);
        concession_money = DoubleHelper.add(concession_money, obj.getDouble("discountr_amount"),4);
		concession_money = DoubleHelper.add(concession_money, obj.getDouble("maling_amount"),4);
        obj.put("concession_money",concession_money);

	}

	//线下团购明细
	private void getOfflineGroupDetails(String tenantId, int organId,Date reportDate,String optNum, String posNum,Timestamp startTimestamp,Timestamp endTimestamp,JSONObject obj,String formatState,String formatMode,int scale) throws Exception{
		StringBuilder offlineGroupSql = new StringBuilder("");
		JSONArray arr = new JSONArray();
		offlineGroupSql.append("SELECT P .jzid, w.payment_name1, SUM (P .currency_amount) AS discount_money, SUM (P . COUNT) AS discount_num FROM pos_bill_payment P ");
		offlineGroupSql.append("LEFT JOIN pos_bill bi ON P .tenancy_id = bi.tenancy_id AND P .bill_num = bi.bill_num LEFT JOIN payment_way w ON P .jzid = w. ID " );
		offlineGroupSql.append("WHERE w.payment_class = 'groupbuycoupon' and w.payment_name1='美团团购'  and bi.bill_property =? AND bi.store_id = ? " );
		offlineGroupSql.append("AND bi.report_date = ? AND bi.pos_num =? AND bi.payment_time > ? AND bi.payment_time < ? GROUP BY P .jzid, w.payment_name1;");

		SqlRowSet offlineGroupSqlRs = posDao.query4SqlRowSet(offlineGroupSql.toString(), new Object[]
				{ SysDictionary.BILL_PROPERTY_CLOSED, organId, reportDate, posNum, startTimestamp, endTimestamp});
		while (offlineGroupSqlRs.next())
		{
			JSONObject concessionJson = new JSONObject();
			Double discount_money = offlineGroupSqlRs.getDouble("discount_money");
			concessionJson.put("payment_name", offlineGroupSqlRs.getDouble("payment_name1"));
			concessionJson.put("discount_money",discount_money);
			concessionJson.put("discount_num", Double.valueOf(offlineGroupSqlRs.getDouble("discount_num")).intValue());
			arr.add(concessionJson);
		}
		obj.put("offlineGroup_details", arr);
	}

	
	/**统计交款数据
	 * @param tenantId
	 * @param organId
	 * @param reportDate
	 * @param optNum
	 * @param posNum
	 * @param startTimestamp
	 * @param endTimestamp
	 * @param obj
	 * @param scale
	 * @throws Exception
	 */
	private void getShiftDataForCashierReceive(String tenantId, int organId,Date reportDate,String optNum, String posNum,Timestamp startTimestamp,Timestamp endTimestamp,JSONObject obj,int scale) throws Exception
	{
		// 交款数据
		StringBuilder cashierReceive = new StringBuilder();
		cashierReceive.append(" select emp.name as opt_name,pcr.service_type,sd.class_item as service_type_name,");
		cashierReceive.append(" (case when d.id is not null then d.class_item else pw.payment_name1 end) as payment_name,");
		cashierReceive.append(" coalesce (pcr.amount, '0') as receive_amount,coalesce (pbp.amount, '0') as payment_amount ");
		cashierReceive.append(" from(  ");
		cashierReceive.append("       select pcr.service_type,pcr.waiter_id,pcr.payment_id,sum(pcr.amount) as amount from pos_cashier_receive_log AS pcr");
		//cashierReceive.append("       where pcr.tenancy_id = ? and pcr.store_id = ? and pcr.report_date = ? and cast (pcr.cashier_id as varchar) = ? and pcr.last_updatetime > ? and pcr.last_updatetime < ? ");
		cashierReceive.append("       where pcr.tenancy_id = ? and pcr.store_id = ? and pcr.report_date = ?  and pcr.last_updatetime > ? and pcr.last_updatetime < ? ");
		cashierReceive.append("       group by pcr.waiter_id,pcr.payment_id,pcr.service_type ");
		cashierReceive.append("    ) pcr left join(  ");
		cashierReceive.append("       select cast('"+SysDictionary.SERVICE_TYPE_CONSUME+"'as text)as service_type,bi.cashier_num,pbp.jzid,sum(pbp.currency_amount) as amount from pos_bill_payment as pbp");
		cashierReceive.append("       left join pos_bill bi on pbp.tenancy_id=bi.tenancy_id and pbp.store_id=bi.store_id and pbp.bill_num=bi.bill_num");
		cashierReceive.append("       left join pos_opt_state_devices as osd on osd.devices_num = bi.pos_num");
		cashierReceive.append("       where bi.tenancy_id = ? and bi.store_id = ? and bi.report_date = ? and osd.pos_num = ? and bi.payment_time > ? and bi.payment_time < ? ");
		cashierReceive.append("       and exists ( select devices_code from hq_devices as hd where (show_type = 'YD' or show_type = 'PAD') and hd.devices_code = osd.devices_num ) ");
		cashierReceive.append("       group by bi.cashier_num,pbp.jzid ");
		cashierReceive.append("     union all ");
		cashierReceive.append("       select cast('"+SysDictionary.SERVICE_TYPE_RECHARGE+"'as text) as service_type,cast(cz.operator_id as text) as cashier_num,cz.payment_id, coalesce(sum(cz.main_trading),0) AS amount from (");
		cashierReceive.append("         select tl.tenancy_id,tl.store_id,tl.business_date,tl.shift_id,tl.operator_id,pl.third_bill_code,pl.bill_code,tl.main_trading,pl.pay_money,pl.payment_id from crm_card_payment_list pl");
		cashierReceive.append("         left join crm_card_trading_list tl on pl.tenancy_id=tl.tenancy_id and pl.third_bill_code=tl.third_bill_code and tl.operat_type in ('"+SysDictionary.OPERAT_TYPE_CZ+"','"+SysDictionary.OPERAT_TYPE_FCZ+"')");
		cashierReceive.append("         left join pos_opt_state ot on tl.tenancy_id=ot.tenancy_id and tl.store_id=ot.store_id and tl.business_date=ot.report_date and cast(tl.operator_id as text) =ot.opt_num and ot.content='"+SysDictionary.OPT_STATE_KSSY+"'");
		cashierReceive.append("         where ot.id is null and tl.tenancy_id =? and tl.store_id =? and tl.business_date = ? and tl.operate_time > ? and tl.operate_time < ?");
		cashierReceive.append("         group by tl.tenancy_id,tl.store_id,tl.business_date,tl.shift_id,tl.operator_id,pl.third_bill_code,pl.bill_code,tl.main_trading,pl.pay_money,pl.payment_id) cz");
		cashierReceive.append("       group by cz.operator_id,cz.payment_id ");
		cashierReceive.append("    ) as pbp on cast (pbp.cashier_num as varchar) = cast (pcr.waiter_id as varchar) and pcr.payment_id = pbp.jzid and pcr.service_type = pbp.service_type ");
		cashierReceive.append(" left join employee emp on pcr.waiter_id = emp.id ");
		cashierReceive.append(" left join payment_way pw on pcr.payment_id = pw.id ");
//		cashierReceive.append(" left join payment_way_of_ogran o on pw.id = o.payment_id ");
		cashierReceive.append(" left join sys_dictionary d on d.class_item_code = pw.payment_name1 and d.class_identifier_code='currency' and pw.payment_class = 'cash'");
		cashierReceive.append(" left join sys_dictionary sd on sd.class_item_code = pcr.service_type and sd.class_identifier_code='service_type' ");
		cashierReceive.append(" group by emp.name,payment_name,pcr.amount,pbp.amount,pcr.service_type,sd.class_item order by pcr.service_type,emp.name");

		JSONArray arrCashierReceive = new JSONArray();
		SqlRowSet rsCashierReceive = posDao.query4SqlRowSet(cashierReceive.toString(), new Object[]
		//{ tenantId, organId, reportDate, optNum, startTimestamp, endTimestamp, tenantId, organId, reportDate, posNum, startTimestamp, endTimestamp, tenantId, organId, reportDate, startTimestamp, endTimestamp });
		{ tenantId, organId, reportDate, startTimestamp, endTimestamp, tenantId, organId, reportDate, posNum, startTimestamp, endTimestamp, tenantId, organId, reportDate, startTimestamp, endTimestamp });

		while (rsCashierReceive.next())
		{
			JSONObject json = new JSONObject();
			json.put("service_type", rsCashierReceive.getString("service_type"));
			json.put("service_type_name", rsCashierReceive.getString("service_type_name"));
			json.put("opt_name", rsCashierReceive.getString("opt_name"));
			json.put("payment_name", rsCashierReceive.getString("payment_name"));
			json.put("payment_amount", rsCashierReceive.getDouble("payment_amount"));
			json.put("receive_amount", rsCashierReceive.getDouble("receive_amount"));
			arrCashierReceive.add(json);
		}
		obj.put("receive_item", arrCashierReceive);

		// 交款
		StringBuilder cashierReceiveSum = new StringBuilder();
		cashierReceiveSum.append("select payment_class,payment_name,sum(amount) as amount from (");
		cashierReceiveSum.append("select pw.payment_class,(case when pw.payment_class = 'cash' and d.id is not null then d.class_item else pw.payment_name1 end) as payment_name,coalesce(sum(pcrl.amount),'0') as amount");
		cashierReceiveSum.append(" from pos_cashier_receive_log pcrl left join payment_way pw on pcrl.payment_id = pw.id left join sys_dictionary d on d.class_item_code = pw.payment_name1");
		//cashierReceiveSum.append(" where pcrl.tenancy_id = ? and pcrl.store_id = ? and pcrl.last_updatetime > ? and pcrl.last_updatetime < ? and cast(pcrl.cashier_id as varchar) = ? and pcrl.pos_num = ? group by pw.payment_class,payment_name");
		cashierReceiveSum.append(" where pcrl.tenancy_id = ? and pcrl.store_id = ? and pcrl.last_updatetime > ? and pcrl.last_updatetime < ? and pcrl.pos_num = ? group by pw.payment_class,payment_name");
		cashierReceiveSum.append(" union all select pw.payment_class,(case when pw.payment_class = 'cash' and d.id is not null then d.class_item else pw.payment_name1 end) as payment_name,0 as amount from payment_way pw left join sys_dictionary d on d.class_item_code = pw.payment_name1");
		cashierReceiveSum.append(" where (payment_class in('card','ali_pay','wechat_pay') or (payment_class ='cash' and is_standard_money='1' )) and status='1') t group by payment_class,payment_name");

		List<JSONObject> receiveSumList = posDao.query4Json(tenantId, cashierReceiveSum.toString(), new Object[]
		//{ tenantId, organId, startTimestamp, endTimestamp, optNum, posNum });
		{ tenantId, organId, startTimestamp, endTimestamp, posNum });

		obj.put("receive_sum_list", receiveSumList);
	}
	
	/** 按类别统计菜品
	 * @param tenancyId
	 * @param storeId
	 * @param reportDate
	 * @param optNum
	 * @param posNum
	 * @param startTimestamp
	 * @param endTimestamp
	 * @param obj
	 * @param scale
	 * @throws Exception
	 */
	private void getShiftDataForItemClass(String tenancyId, int storeId,Date reportDate,String optNum, String posNum,Timestamp startTimestamp,Timestamp endTimestamp,JSONObject obj,int scale) throws Exception
	{
		// 大类名称、大类数量、大类金额
		StringBuffer classSql = new StringBuffer();
		classSql.append("select hicc.itemclass_name, count(hicc.id) as numbers, round(sum(pbi.real_amount), 2) as real_amount from pos_bill_item pbi inner join (select bill_num, store_id, report_date from pos_bill where payment_time >= ? and payment_time <= ? ");
		//classSql.append(" and report_date = ? and store_id = ? and tenancy_id = ? and cashier_num = ? and pos_num = ? and bill_property = 'CLOSED') as pb on pbi.bill_num = pb.bill_num ");
		classSql.append(" and report_date = ? and store_id = ? and tenancy_id = ? and pos_num = ? and bill_property = 'CLOSED') as pb on pbi.bill_num = pb.bill_num ");
		classSql.append(" and pbi.report_date=pb.report_date and pbi.store_id =pb.store_id left join hq_item_menu_details himd on pbi.details_id = himd.id and pbi.item_id = himd.item_id left join hq_item_menu_organ himo on himd.item_menu_id = himo.item_menu_id left join ");
		classSql.append(" hq_item_menu_class himc on himd. id = himc.details_id left join hq_item_class hic on himc. class = hic.id left join hq_item_class hicc on hic.father_id = hicc.id where hicc.valid_state = '1' and hic.valid_state = '1' and himd.valid_state = '1' and himo.store_id = ? group by hicc. id, hicc.itemclass_name ");

		SqlRowSet classRows = posDao.query4SqlRowSet(classSql.toString(), 
				//new Object[]{startTimestamp, endTimestamp, reportDate, storeId, tenancyId, optNum, posNum, storeId});
				new Object[]{startTimestamp, endTimestamp, reportDate, storeId, tenancyId, posNum, storeId});

		JSONArray classList = new JSONArray(); 
		while(classRows.next()){
			JSONObject classItem = new JSONObject();
			classItem.put("itemclass_name", classRows.getString("itemclass_name"));
			classItem.put("numbers", classRows.getInt("numbers"));
			classItem.put("real_amount", classRows.getDouble("real_amount"));
			
			classList.add(classItem);
		}
		obj.put("class_list", classList);
	}
	
	/** 发卡
	 * @param tenancyId
	 * @param storeId
	 * @param reportDate
	 * @param optNum
	 * @param posNum
	 * @param startTimestamp
	 * @param endTimestamp
	 * @param arrCard
	 * @param scale
	 * @throws Exception
	 */
	private void getShiftDataForCardActivation(String tenancyId, int storeId,Date reportDate,String optNum,String posNum,Timestamp startTimestamp,Timestamp endTimestamp,JSONArray arrCard,int scale) throws Exception
	{
		StringBuilder cardActivation = new StringBuilder("");
		cardActivation.append(" select pl.payment_id,pl.payment_name,sum(pl.deposit) as deposit,sum(pl.sales_price) as sales_price from (");
		cardActivation.append(" select pl.payment_id,(case when pw.payment_class = 'cash' and sd.id is not null then sd.class_item else pw.payment_name1 end) payment_name,coalesce(tl.deposit,0) as deposit,(coalesce(pl.pay_money,0)-coalesce(tl.deposit,0)) as sales_price");
		cardActivation.append(" from crm_card_trading_list tl left join crm_card_payment_list pl on tl.tenancy_id=pl.tenancy_id and tl.card_id=pl.card_id and tl.bill_code=pl.bill_code");
		cardActivation.append(" left join payment_way pw on pl.tenancy_id=pw.tenancy_id and pl.payment_id=pw.id left join sys_dictionary sd on pw.tenancy_id=sd.tenancy_id and pw.payment_name1=sd.class_item_code and sd.class_identifier_code='currency'");
		//cardActivation.append(" where tl.operat_type=? and pl.id is not null and tl.tenancy_id=? and tl.store_id=? and tl.operator_id=? and tl.store_updatetime>? and tl.store_updatetime<?) pl group by pl.payment_id,pl.payment_name");
		cardActivation.append(" where tl.operat_type=? and tl.pos_num=? and pl.id is not null and tl.tenancy_id=? and tl.store_id=?  and tl.store_updatetime>? and tl.store_updatetime<?) pl group by pl.payment_id,pl.payment_name");

		//List<JSONObject> cardActivationList = posDao.query4Json(tenancyId, cardActivation.toString(), new Object[]{SysDictionary.OPERAT_TYPE_FK,tenancyId,storeId,Integer.parseInt(optNum),startTimestamp, endTimestamp});
		List<JSONObject> cardActivationList = posDao.query4Json(tenancyId, cardActivation.toString(), new Object[]{SysDictionary.OPERAT_TYPE_FK,posNum,tenancyId,storeId,startTimestamp, endTimestamp});

		for(JSONObject cardActivationJson :cardActivationList)
		{
			Double salesPrice = ParamUtil.getDoubleValueByObject(cardActivationJson, "sales_price");
			JSONObject jsonMoney = new JSONObject();
//			jsonMoney.put("operat_type_code", SysDictionary.CUSTOMER_SALECARD_MONEY);
//			jsonMoney.put("operat_type", "发卡售卡金额");
//			jsonMoney.put("jzid", cardActivationJson.optInt("payment_id"));
//			jsonMoney.put("payment_name", cardActivationJson.optString("payment_name"));
			
			jsonMoney.put("operat_type_code", SysDictionary.OPERAT_TYPE_FK);
			jsonMoney.put("operat_details", SysDictionary.CUSTOMER_SALECARD_MONEY);
			jsonMoney.put("jzid", cardActivationJson.optInt("payment_id"));
			jsonMoney.put("operat_type", SysDictionary.CUSTOMER_SALECARD_MONEY_TXT);
			jsonMoney.put("payment_name", cardActivationJson.optString("payment_name"));
			jsonMoney.put("payment_amount", salesPrice);
			jsonMoney.put("payment_count", "");
			jsonMoney.put("give_amount", 0);
			jsonMoney.put("differ_amount", 0);
			jsonMoney.put("can_modify", "Y");
			arrCard.add(jsonMoney);
		}
		
		for(JSONObject cardActivationJson :cardActivationList)
		{
			Double deposit = ParamUtil.getDoubleValueByObject(cardActivationJson, "deposit");
			JSONObject jsonMoney = new JSONObject();
//			jsonMoney.put("operat_type_code", SysDictionary.CUSTOMER_DEPOSIT_MONEY);
//			jsonMoney.put("operat_type", "发卡押金金额");
//			jsonMoney.put("jzid", cardActivationJson.optInt("payment_id"));
//			jsonMoney.put("payment_name", cardActivationJson.optString("payment_name"));
			
			jsonMoney.put("operat_type_code", SysDictionary.OPERAT_TYPE_FK);
			jsonMoney.put("operat_details", SysDictionary.CUSTOMER_DEPOSIT_MONEY);
			jsonMoney.put("jzid", cardActivationJson.optInt("payment_id"));
			jsonMoney.put("operat_type", SysDictionary.CUSTOMER_DEPOSIT_MONEY_TXT);
			jsonMoney.put("payment_name", cardActivationJson.optString("payment_name"));
			jsonMoney.put("payment_amount", deposit);
			jsonMoney.put("payment_count", "");
			jsonMoney.put("give_amount", 0);
			jsonMoney.put("differ_amount", 0);
			jsonMoney.put("can_modify", "Y");
			arrCard.add(jsonMoney);
		}
	}

	private void getShiftDataTotalMoneyForCardRecharge(String tenancyId, int storeId, Date reportDate, String optNum,String posNum, Timestamp startTimestamp, Timestamp endTimestamp, JSONObject obj, int scale) throws Exception
	{
		// 充值
		StringBuilder cardRecharge = new StringBuilder("SELECT COALESCE (SUM(pay_money), '0') AS total_charge_money FROM crm_card_trading_list cctl LEFT JOIN crm_card_payment_list ccpl ON cctl.bill_code = ccpl.bill_code " +
				"LEFT JOIN payment_way pw ON ccpl.payment_id = pw. ID LEFT JOIN pos_cashier_receive_log_details rd ON cctl.third_bill_code = rd.bill_num AND cctl.tenancy_id = rd.tenancy_id WHERE cctl.operat_type " +
				"IN ('02', '04') AND (( cctl.operate_time > ? AND cctl.operate_time < ? ) OR ( rd.last_updatetime >? AND rd.last_updatetime <? )) " +
				"AND cctl.pos_num = ? AND cctl.tenancy_id = ? AND cctl.store_id = ? ");

		SqlRowSet rsCardRecharge = posDao.query4SqlRowSet(cardRecharge.toString(), new Object[]
				{  startTimestamp, endTimestamp, startTimestamp, endTimestamp,posNum, tenancyId, storeId });
		Double total_payment_mount=0d;
		while (rsCardRecharge.next())
		{
			total_payment_mount = rsCardRecharge.getDouble("total_charge_money");
			obj.put("total_payment_mount",total_payment_mount);
		}
	}

	
	/** 充值
	 * @param tenancyId
	 * @param storeId
	 * @param reportDate
	 * @param optNum
	 * @param posNum
	 * @param startTimestamp
	 * @param endTimestamp
	 * @param arrCard
	 * @param scale
	 * @throws Exception
	 */
	private void getShiftDataForCardRecharge(String tenancyId, int storeId, Date reportDate, String optNum,String posNum, Timestamp startTimestamp, Timestamp endTimestamp, JSONArray arrCard, JSONObject obj,int scale) throws Exception
	{
		// 充值
		StringBuilder cardRecharge = new StringBuilder("select m.payment_id,m.operat_type_name,coalesce(sum(m.pay_money),'0') as money ,count(payment_id) as payment_count from (");
		cardRecharge.append(" select ccpl.payment_id,(case when pw.payment_class = 'cash' and d.id is not null then d.class_item else pw.payment_name1 end) as operat_type_name,ccpl.pay_money from crm_card_trading_list cctl");
		cardRecharge.append(" left join crm_card_payment_list ccpl on cctl.bill_code = ccpl.bill_code left join payment_way pw on ccpl.payment_id = pw.id left join sys_dictionary d on d.class_item_code = pw.payment_name1 and d.class_identifier_code='currency'");
		cardRecharge.append(" left join pos_cashier_receive_log_details rd on cctl.third_bill_code=rd.bill_num and cctl.tenancy_id=rd.tenancy_id");
		//cardRecharge.append(" where cctl.operat_type in ('02','04') and ((cctl.operator_id = ? and cctl.operate_time > ? and cctl.operate_time < ?) or (rd.cashier_id=? and rd.last_updatetime>? and rd.last_updatetime<?)) and cctl.tenancy_id = ? and cctl.store_id = ?) m group by m.payment_id,m.operat_type_name");
		cardRecharge.append(" where cctl.operat_type in ('02','04') and (( cctl.operate_time > ? and cctl.operate_time < ?) or ( rd.last_updatetime>? and rd.last_updatetime<?)) and cctl.pos_num=? and cctl.tenancy_id = ? and cctl.store_id = ?) m group by m.payment_id,m.operat_type_name");

		SqlRowSet rsCardRecharge = posDao.query4SqlRowSet(cardRecharge.toString(), new Object[]
		//{ Integer.parseInt(optNum), startTimestamp, endTimestamp,Integer.parseInt(optNum), startTimestamp, endTimestamp, tenancyId, storeId });
		{  startTimestamp, endTimestamp, startTimestamp, endTimestamp, posNum,tenancyId, storeId });
		Double customer_recharge_total =0d;
		Integer customer_recharge_total_count=0;
		while (rsCardRecharge.next())
		{
			JSONObject json = new JSONObject();
//			json.put("operat_type_code", SysDictionary.OPERAT_TYPE_CZ);
//			json.put("operat_type", "充值");
//			json.put("jzid", rsCardRecharge.getString("payment_id"));
//			json.put("payment_name", rsCardRecharge.getString("operat_type_name"));
			
			json.put("operat_type_code", SysDictionary.OPERAT_TYPE_CZ);
			json.put("operat_details", SysDictionary.CUSTOMER_RECHARGE);
			json.put("jzid", rsCardRecharge.getString("payment_id"));
			json.put("operat_type", SysDictionary.CUSTOMER_RECHARGE_TXT);
			json.put("payment_name", rsCardRecharge.getString("operat_type_name"));
			json.put("payment_amount", rsCardRecharge.getDouble("money"));


			customer_recharge_total = DoubleHelper.add(customer_recharge_total, rsCardRecharge.getDouble("money"), 4);

			json.put("payment_count", rsCardRecharge.getString("payment_count"));
			customer_recharge_total_count = customer_recharge_total_count+Integer.parseInt(rsCardRecharge.getString("payment_count"));
			json.put("give_amount", 0);
			json.put("differ_amount", 0);
			json.put("can_modify", "Y");
			arrCard.add(json);
		}
		obj.put("customer_recharge_total",customer_recharge_total);
		obj.put("customer_recharge_total_count",customer_recharge_total_count);
	}
	
	/** 消费
	 * @param tenancyId
	 * @param storeId
	 * @param reportDate
	 * @param optNum
	 * @param posNum
	 * @param startTimestamp
	 * @param endTimestamp
	 * @param arrCard
	 * @param scale
	 * @throws Exception
	 */
	private void getShiftDataForCardConsume(String tenancyId, int storeId,Date reportDate,String optNum,String posNum, Timestamp startTimestamp,Timestamp endTimestamp,JSONArray arrCard,int scale) throws Exception
	{
		// 消费
		StringBuilder cardConsumeCount = new StringBuilder("select count(cctl.id) as count_num from crm_card_trading_list cctl");
		//cardConsumeCount.append(" where cctl.operat_type in ('03','05') and cctl.operator_id = ? and cctl.operate_time > ? and cctl.operate_time < ? and cctl.tenancy_id = ? and cctl.store_id = ?");
		cardConsumeCount.append(" where cctl.operat_type in ('03','05') and cctl.operate_time > ? and cctl.operate_time < ? and cctl.pos_num=? and cctl.tenancy_id = ? and cctl.store_id = ?");
		int consumeCount = posDao.queryForInt(cardConsumeCount.toString(), new Object[]
		//{ Integer.parseInt(optNum), startTimestamp, endTimestamp, tenancyId, storeId });
		{  startTimestamp, endTimestamp, posNum,tenancyId, storeId });
		if (consumeCount > 0)
		{
			StringBuilder cardConsumeHaveBill = new StringBuilder("select coalesce(sum(cctl.main_trading+cctl.reward_trading),'0') as money from crm_card_trading_list cctl");
			cardConsumeHaveBill.append(" left join pos_bill bi on cctl.tenancy_id=bi.tenancy_id and cctl.store_id=bi.store_id and (cctl.third_bill_code=bi.bill_num or cctl.third_bill_code=bi.order_num)");
			//cardConsumeHaveBill.append(" where cctl.operat_type in ('03','05') and cctl.operator_id = ? and cctl.operate_time > ? and cctl.operate_time < ? and bi.bill_num is not null and cctl.tenancy_id = ? and cctl.store_id = ?");
			cardConsumeHaveBill.append(" where cctl.operat_type in ('03','05') and cctl.operate_time > ? and cctl.operate_time < ? and bi.bill_num is not null and cctl.pos_num=? and cctl.tenancy_id = ? and cctl.store_id = ?");
			SqlRowSet rsCardConsumeHaveBill = posDao.query4SqlRowSet(cardConsumeHaveBill.toString(), new Object[]
			//{ Integer.parseInt(optNum), startTimestamp, endTimestamp, tenancyId, storeId });
			{ startTimestamp, endTimestamp, posNum,tenancyId, storeId });
			if (rsCardConsumeHaveBill.next())
			{
				JSONObject json = new JSONObject();
//				json.put("operat_type_code", SysDictionary.OPERAT_TYPE_XF);
//				json.put("operat_type", "消费");
//				json.put("jzid", SysDictionary.CUSTOMER_DETAIL_HAVE_BILL);
//				json.put("payment_name", "有账单");
				
				json.put("operat_type_code", SysDictionary.OPERAT_TYPE_XF);
				json.put("operat_details", SysDictionary.CUSTOMER_DETAIL_HAVE_BILL);
				json.put("jzid", "0");
				json.put("operat_type", "消费");
				json.put("payment_name", SysDictionary.CUSTOMER_DETAIL_HAVE_BILL_TXT);
				json.put("payment_amount", rsCardConsumeHaveBill.getDouble("money"));
				json.put("payment_count", "");
				json.put("give_amount", 0);
				json.put("differ_amount", 0);
				json.put("can_modify", "Y");
				arrCard.add(json);
			}

			StringBuilder cardConsumeNoBill = new StringBuilder("select coalesce(sum(cctl.main_trading+cctl.reward_trading),'0') as money from crm_card_trading_list cctl");
			cardConsumeNoBill.append(" left join pos_bill bi on cctl.tenancy_id=bi.tenancy_id and cctl.store_id=bi.store_id and (cctl.third_bill_code=bi.bill_num or cctl.third_bill_code=bi.order_num)");
			//cardConsumeNoBill.append(" where cctl.operat_type in ('03','05') and cctl.operator_id = ? and cctl.operate_time > ? and cctl.operate_time < ? and bi.bill_num is null and cctl.tenancy_id = ? and cctl.store_id = ?");
			cardConsumeNoBill.append(" where cctl.operat_type in ('03','05') and cctl.operate_time > ? and cctl.operate_time < ? and bi.bill_num is null and cctl.pos_num=? and cctl.tenancy_id = ? and cctl.store_id = ?");
			SqlRowSet rsCardConsumeNoBill = posDao.query4SqlRowSet(cardConsumeNoBill.toString(), new Object[]
			//{ Integer.parseInt(optNum), startTimestamp, endTimestamp, tenancyId, storeId });
			{startTimestamp, endTimestamp,posNum, tenancyId, storeId });
			if (rsCardConsumeNoBill.next())
			{
				JSONObject json = new JSONObject();
//				json.put("operat_type_code", SysDictionary.OPERAT_TYPE_XF);
//				json.put("operat_type", "消费");
//				json.put("jzid", SysDictionary.CUSTOMER_DETAIL_NO_BILL);
//				json.put("payment_name", "无账单");
				
				json.put("operat_type_code", SysDictionary.OPERAT_TYPE_XF);
				json.put("operat_details", SysDictionary.CUSTOMER_DETAIL_NO_BILL);
				json.put("jzid", "0");
				json.put("operat_type", "消费");
				json.put("payment_name", SysDictionary.CUSTOMER_DETAIL_NO_BILL_TXT);
				json.put("payment_amount", rsCardConsumeNoBill.getDouble("money"));
				json.put("give_amount", 0);
				json.put("differ_amount", 0);
				json.put("can_modify", "Y");
				arrCard.add(json);
			}
		}
	}
	
	/** 退卡
	 * @param tenancyId
	 * @param storeId
	 * @param reportDate
	 * @param optNum
	 * @param posNum
	 * @param startTimestamp
	 * @param endTimestamp
	 * @param arrCard
	 * @param scale
	 * @throws Exception
	 */
	private void getShiftDataForCardBack(String tenancyId, int storeId,Date reportDate,String optNum,String posNum,Timestamp startTimestamp,Timestamp endTimestamp,JSONArray arrCard,int scale) throws Exception
	{
		// 退卡
		StringBuilder cardBack = new StringBuilder("select coalesce(sum(cctl.main_trading),'0') as money,coalesce(sum(cctl.deposit),'0') as deposit,count(cctl.id) as count_num from crm_card_trading_list cctl");
		//cardBack.append(" where cctl.operat_type = '12' and cctl.operator_id = ? and cctl.operate_time > ? and cctl.operate_time < ? and cctl.tenancy_id = ? and cctl.store_id = ?");
		cardBack.append(" where cctl.operat_type = '12'  and cctl.operate_time > ? and cctl.operate_time < ? and cctl.pos_num=? and cctl.tenancy_id = ? and cctl.store_id = ?");
		SqlRowSet rsCardBack = posDao.query4SqlRowSet(cardBack.toString(), new Object[]
		//{ Integer.parseInt(optNum), startTimestamp, endTimestamp, tenancyId, storeId });
		{  startTimestamp, endTimestamp, posNum,tenancyId, storeId });
		double depositBack = 0d;
		double moneyBack = 0d;
		if (rsCardBack.next())
		{
			depositBack = rsCardBack.getDouble("deposit");
			moneyBack = rsCardBack.getDouble("money");
			if (rsCardBack.getDouble("count_num") > 0)
			{
				JSONObject jsonMoney = new JSONObject();
//				jsonMoney.put("operat_type_code", SysDictionary.OPERAT_TYPE_TK);
//				jsonMoney.put("operat_type", "退卡");
//				jsonMoney.put("jzid", SysDictionary.CUSTOMER_BACKCARD_MONEY);
//				jsonMoney.put("payment_name", "退卡金额");
				
				jsonMoney.put("operat_type_code", SysDictionary.OPERAT_TYPE_TK);
				jsonMoney.put("operat_details", SysDictionary.CUSTOMER_BACKCARD_MONEY);
				jsonMoney.put("jzid", "0");
				jsonMoney.put("operat_type", "退卡");
				jsonMoney.put("payment_name", SysDictionary.CUSTOMER_BACKCARD_MONEY_TXT);
				jsonMoney.put("payment_amount", moneyBack);
				jsonMoney.put("payment_count", "");
				jsonMoney.put("give_amount", 0);
				jsonMoney.put("differ_amount", 0);
				jsonMoney.put("can_modify", "Y");
				arrCard.add(jsonMoney);

				JSONObject jsonDeposit = new JSONObject();
//				jsonDeposit.put("operat_type_code", SysDictionary.OPERAT_TYPE_TK);
//				jsonDeposit.put("operat_type", "退卡");
//				jsonDeposit.put("jzid", SysDictionary.CUSTOMER_BACKDEPOSIT_MONEY);
//				jsonDeposit.put("payment_name", "退押金金额");
				
				jsonDeposit.put("operat_type_code", SysDictionary.OPERAT_TYPE_TK);
				jsonDeposit.put("operat_details", SysDictionary.CUSTOMER_BACKDEPOSIT_MONEY);
				jsonDeposit.put("jzid", "0");
				jsonDeposit.put("operat_type", "退卡");
				jsonDeposit.put("payment_name", SysDictionary.CUSTOMER_BACKDEPOSIT_MONEY_TXT);
				jsonDeposit.put("payment_amount", depositBack);
				jsonMoney.put("payment_count", "");
				jsonDeposit.put("give_amount", 0);
				jsonDeposit.put("differ_amount", 0);
				jsonDeposit.put("can_modify", "Y");
				arrCard.add(jsonDeposit);
			}
		}
	}
	
	/** 购买会籍
	 * @param tenancyId
	 * @param storeId
	 * @param reportDate
	 * @param optNum
	 * @param posNum
	 * @param startTimestamp
	 * @param endTimestamp
	 * @param arrCard
	 * @param scale
	 * @throws Exception
	 */
	private void getShiftDataForBuyVip(String tenancyId, int storeId,Date reportDate,String optNum, String posNum,Timestamp startTimestamp,Timestamp endTimestamp,JSONArray arrCard,int scale) throws Exception
	{
		// 购买会籍
		StringBuilder gmhjSql = new StringBuilder("select  way.id as way_id, CASE WHEN  way.payment_class ='cash' THEN (SELECT sd.class_item FROM sys_dictionary sd WHERE sd.class_item_code=way.payment_name1 ) ");
		gmhjSql.append("  ELSE way.payment_name1 END payment_name, SUM(ls.pay_money) as pay_money from crm_activity_payvip_list ls  left  JOIN payment_way way on ls.pay_id = way.id ");
		gmhjSql.append(" where ls.pay_time>=? and ls.pay_time<=?  and ls.tenancy_id=? and ls.store_id=? ");
		gmhjSql.append("  group by way.payment_name1,way.id,payment_class ");
		SqlRowSet rsgmhj = posDao.query4SqlRowSet(gmhjSql.toString(), new Object[]
				{ startTimestamp, endTimestamp, tenancyId, storeId });
		while (rsgmhj.next())
		{
			JSONObject json = new JSONObject();
//			json.put("operat_type_code", SysDictionary.OPERAT_TYPE_GMHJ);
//			json.put("operat_type", "购买会籍");
//			json.put("jzid", rsgmhj.getString("way_id"));
//			json.put("payment_name", rsgmhj.getString("payment_name"));
			
			json.put("operat_type_code", SysDictionary.OPERAT_TYPE_GMHJ);
			json.put("operat_details", SysDictionary.CUSTOMER_BUYVIP);
			json.put("jzid", rsgmhj.getString("way_id"));
			json.put("operat_type", SysDictionary.CUSTOMER_BUYVIP_TXT);
			json.put("payment_name", rsgmhj.getString("payment_name"));
			json.put("payment_amount", rsgmhj.getDouble("pay_money"));
			json.put("payment_count", "");
			json.put("give_amount", 0);
			json.put("differ_amount", 0);
			json.put("can_modify", "Y");
			arrCard.add(json);
		}
	}
	
	/** 发卡
	 * @param tenancyId
	 * @param storeId
	 * @param reportDate
	 * @param optNum
	 * @param posNum
	 * @param startTimestamp
	 * @param endTimestamp
	 * @param obj
	 * @param scale
	 * @throws Exception
	 */
	private void getShiftDataForCustomerTotal(String tenancyId, int storeId,Date reportDate,String optNum,String posNum, Timestamp startTimestamp,Timestamp endTimestamp,JSONObject obj,int scale) throws Exception
	{
		double recharge_money = 0;
		double reward_money = 0;
		double consume_money = 0;
		double backout_money = 0;
		double depositBack = 0d;
		double customerSell = 0d;
		double depositActivation = 0d;
		
		// 发卡
		StringBuilder cardActivation = new StringBuilder("");
		cardActivation.append("select sum(coalesce(tl.deposit,0)) as deposit,sum(coalesce(pl.pay_money,0)-coalesce(tl.deposit,0)) as sales_price ");
		cardActivation.append("from crm_card_trading_list tl left join crm_card_payment_list pl on tl.tenancy_id=pl.tenancy_id and tl.card_id=pl.card_id and tl.bill_code=pl.bill_code ");
		//cardActivation.append("where tl.operat_type=? and pl.id is not null and tl.tenancy_id=? and tl.store_id=? and tl.operator_id=? and tl.store_updatetime>? and tl.store_updatetime<?");
		cardActivation.append("where tl.operat_type=? and pl.id is not null and tl.pos_num=? and tl.tenancy_id=? and tl.store_id=? and tl.store_updatetime>? and tl.store_updatetime<?");

		SqlRowSet activationRs = posDao.query4SqlRowSet(cardActivation.toString(), new Object[]
		//{ SysDictionary.OPERAT_TYPE_FK, tenancyId, storeId, Integer.parseInt(optNum), startTimestamp, endTimestamp });
		{ SysDictionary.OPERAT_TYPE_FK, posNum,tenancyId, storeId, startTimestamp, endTimestamp });
		if (activationRs.next())
		{
			customerSell = activationRs.getDouble("sales_price");
			depositActivation = activationRs.getDouble("deposit");
		}
		// 售卡金额
		obj.put("customer_sell", customerSell);

		// 充值合计，赠送合计
		StringBuilder rechargeSb = new StringBuilder(
				"select coalesce(sum(cctl.main_trading),'0') as recharge_money, coalesce(sum(cctl.reward_trading),'0') as reward_money from crm_card_trading_list cctl ");
		rechargeSb.append(" left join pos_cashier_receive_log_details rd on cctl.third_bill_code=rd.bill_num and cctl.tenancy_id=rd.tenancy_id");
		//rechargeSb.append(" where cctl.operat_type in ('02','04') and ((cctl.operator_id = ? and cctl.operate_time > ? and cctl.operate_time < ?) or (rd.cashier_id=? and rd.last_updatetime>? and rd.last_updatetime<?)) and cctl.tenancy_id=? and cctl.store_id=?");
		rechargeSb.append(" where cctl.operat_type in ('02','04') and ((cctl.operate_time > ? and cctl.operate_time < ?) or (rd.last_updatetime>? and rd.last_updatetime<?)) and cctl.pos_num=? and cctl.tenancy_id=? and cctl.store_id=?");
		SqlRowSet rechargeRs = posDao.query4SqlRowSet(rechargeSb.toString(), new Object[]
		//{ Integer.parseInt(optNum), startTimestamp, endTimestamp,Integer.parseInt(optNum), startTimestamp, endTimestamp, tenancyId, storeId });
		{ startTimestamp, endTimestamp, startTimestamp, endTimestamp, posNum,tenancyId, storeId });
		if (rechargeRs.next())
		{
			recharge_money = rechargeRs.getDouble("recharge_money");
			reward_money = rechargeRs.getDouble("reward_money");
		}
		obj.put("customer_recharge", recharge_money);
		obj.put("customer_reward", reward_money);

		// 消费合计
//		StringBuilder consumeSb = new StringBuilder(
//				"select coalesce(sum(cctl.main_trading + cctl.reward_trading),'0') as consume_money from crm_card_trading_list cctl where cctl.operat_type in ('03','05') and cctl.operator_id =? and cctl.operate_time >? and cctl.operate_time <? and cctl.tenancy_id=? and cctl.store_id=?");
		StringBuilder consumeSb = new StringBuilder(
				"select coalesce(sum(cctl.main_trading + cctl.reward_trading),'0') as consume_money from crm_card_trading_list cctl where cctl.operat_type in ('03','05') and cctl.operate_time >? and cctl.operate_time <? and cctl.pos_num=? and cctl.tenancy_id=? and cctl.store_id=?");
		SqlRowSet consumeRs = posDao.query4SqlRowSet(consumeSb.toString(), new Object[]
		//{ Integer.parseInt(optNum), startTimestamp, endTimestamp, tenancyId, storeId });
		{  startTimestamp, endTimestamp, posNum,tenancyId, storeId });
		if (consumeRs.next())
		{
			consume_money = consumeRs.getDouble("consume_money");
		}
		obj.put("customer_consume", consume_money);

		// 退卡金额
//		StringBuilder backoutSb = new StringBuilder(
//				"select coalesce(sum(cctl.main_trading),'0') as backout_money,coalesce(sum(cctl.deposit),'0') as deposit from crm_card_trading_list cctl where cctl.operat_type='12' and cctl.operator_id =? and cctl.operate_time >? and cctl.operate_time <? and cctl.tenancy_id=? and cctl.store_id=?");
		StringBuilder backoutSb = new StringBuilder(
				"select coalesce(sum(cctl.main_trading),'0') as backout_money,coalesce(sum(cctl.deposit),'0') as deposit from crm_card_trading_list cctl where cctl.operat_type='12' and cctl.operate_time >? and cctl.operate_time <?  and cctl.pos_num=? and cctl.tenancy_id=? and cctl.store_id=?");
		SqlRowSet backoutRs = posDao.query4SqlRowSet(backoutSb.toString(), new Object[]
		//{ Integer.parseInt(optNum), startTimestamp, endTimestamp, tenancyId, storeId });
		{ startTimestamp, endTimestamp,posNum, tenancyId, storeId });
		if (backoutRs.next())
		{
			backout_money = Math.abs(backoutRs.getDouble("backout_money"));
			depositBack = backoutRs.getDouble("deposit");
		}
		obj.put("back_card", backout_money);

		// 金额沉淀
		double precipitate_amount = DoubleHelper.sub(DoubleHelper.sub(recharge_money, consume_money, 2), backout_money, 2);
		obj.put("precipitate_amount", precipitate_amount);

		// 未退押金金额
		double customerDeposit = DoubleHelper.add(depositActivation, depositBack, 2);
		obj.put("customer_deposit", customerDeposit);
	}
	
	/** 会员卡付款统计
	 * @param tenancyId
	 * @param storeId
	 * @param reportDate
	 * @param optNum
	 * @param posNum
	 * @param startTimestamp
	 * @param endTimestamp
	 * @param obj
	 * @param scale
	 * @throws Exception
	 */
	private void getShiftDataForCustomerPayment(String tenancyId, int storeId,Date reportDate,String optNum,String posNum, Timestamp startTimestamp,Timestamp endTimestamp,JSONObject obj,int scale) throws Exception
	{
		//会员卡付款统计
		//本系统卡
		double total_card = 0d ;
		//前台消费卡主帐户金额
		double qt_xf_main_trading = 0d;
		//前台消费卡赠送账户金额
		double qt_xf_reward_trading =0d;
		//【后台消费卡主帐户金额】
		double ht_xf_main_trading = 0d;
		//【后台消费卡赠送账户金额】
		double ht_xf_reward_trading =0d;
		
		StringBuffer loadcardSql = new StringBuffer("select coalesce(sum(case when bi.bill_num is not null then coalesce(main_trading, 0)end),0) as qt_xf_main_trading,");
		loadcardSql.append(" coalesce(sum(case when bi.bill_num is not null then coalesce(reward_trading, 0)end),0) as qt_xf_reward_trading,");
		loadcardSql.append(" coalesce(sum(case when bi.bill_num is null then coalesce(main_trading, 0)end),0) as ht_xf_main_trading,");
		loadcardSql.append(" coalesce(sum(case when bi.bill_num is null then coalesce(reward_trading, 0)end),0) as ht_xf_reward_trading");
		loadcardSql.append(" from crm_card_trading_list tl left join pos_bill bi on tl.third_bill_code=bi.bill_num");
		//loadcardSql.append(" where tl.tenancy_id=? and tl.store_id=? and tl.operate_time>=? and tl.operate_time<=? and tl.operat_type in (?, ?) and tl.operator_id=?");
		loadcardSql.append(" where tl.tenancy_id=? and tl.store_id=? and tl.operate_time>=? and tl.operate_time<=? and tl.operat_type in (?, ?) and tl.pos_num=? ");

		SqlRowSet loadcardRs = posDao.query4SqlRowSet(loadcardSql.toString(), new Object[]
		//{ tenancyId, storeId, startTimestamp, endTimestamp, SysDictionary.OPERAT_TYPE_XF, SysDictionary.OPERAT_TYPE_FXF, Integer.parseInt(optNum) });
		{ tenancyId, storeId, startTimestamp, endTimestamp, SysDictionary.OPERAT_TYPE_XF, SysDictionary.OPERAT_TYPE_FXF,posNum });

		if (loadcardRs.next())
		{
			qt_xf_main_trading = loadcardRs.getDouble("qt_xf_main_trading");
			qt_xf_reward_trading = loadcardRs.getDouble("qt_xf_reward_trading");
			total_card = DoubleHelper.add(qt_xf_main_trading, qt_xf_reward_trading, 4);
			
			ht_xf_main_trading = loadcardRs.getDouble("ht_xf_main_trading");
			ht_xf_reward_trading = loadcardRs.getDouble("ht_xf_reward_trading");
		}
		
		obj.put("total_card", total_card);
		obj.put("qt_xf_main_trading", qt_xf_main_trading);
		obj.put("qt_xf_reward_trading", qt_xf_reward_trading);
		obj.put("ht_xf_main_trading", ht_xf_main_trading);
		obj.put("ht_xf_reward_trading", ht_xf_reward_trading);
	}
	
	/** 外卖统计
	 * @param tenancyId
	 * @param storeId
	 * @param reportDate
	 * @param optNum
	 * @param posNum
	 * @param startTimestamp
	 * @param endTimestamp
	 * @param obj
	 * @param scale
	 * @throws Exception
	 */
	private void getShiftDataForOrder(String tenancyId, int storeId,Date reportDate,String optNum,Timestamp startTimestamp,Timestamp endTimestamp,JSONObject obj,int scale) throws Exception
	{
		//外卖统计
        obj.put("meituanAmount",0);
        obj.put("baiduAmount",0);
        obj.put("eleAmount",0);
        String orderSumSql="select type,sum(amount) from pos_bill_payment where type in('meituan_pay','baidu_pay','ele_pay') and store_id=? and tenancy_id=? and report_date = ? and shift_id <> "+com.tzx.orders.base.constant.Constant.WM_SHIFT_ID+" GROUP BY type";
        SqlRowSet orderSum=posDao.query4SqlRowSet(orderSumSql,new Object[]{storeId,tenancyId,reportDate});
        while (orderSum.next()){
            if(orderSum.getString("type").equals("meituan_pay")){
                obj.put("meituanAmount",orderSum.getDouble("sum"));
            }else if(orderSum.getString("type").equals("baidu_pay")){
                obj.put("baiduAmount",orderSum.getDouble("sum"));
            }else if(orderSum.getString("type").equals("ele_pay")){
                obj.put("eleAmount",orderSum.getDouble("sum"));
            }
        }

        obj.put("thirdOrderAmount",obj.optDouble("meituanAmount")+obj.optDouble("baiduAmount")+obj.optDouble("eleAmount"));

        obj.put("meituanExceNum",0);
        obj.put("baiduExceNum",0);
        obj.put("eleExceNum",0);
        String orderExceSum="select chanel,count(*) from cc_order_list where order_state='11' and store_id=? and tenancy_id=? and report_date = ? GROUP BY chanel";
        SqlRowSet orderExce=posDao.query4SqlRowSet(orderExceSum,new Object[]{storeId,tenancyId,reportDate});
        while (orderExce.next()){
            if(orderExce.getString("chanel").equals("MT08")){
                obj.put("meituanExceNum",orderExce.getInt("count"));
            }else if(orderExce.getString("chanel").equals("BD06")){
                obj.put("baiduExceNum",orderExce.getInt("count"));
            }else if(orderExce.getString("chanel").equals("EL09")){
                obj.put("eleExceNum",orderExce.getInt("count"));
            }
        }
        obj.put("thirdOrderExceNum",obj.optInt("meituanExceNum")+obj.optInt("baiduExceNum")+obj.optInt("eleExceNum"));
	}
	
	
	/**
	 * 获得菜品销售数据和售卖模式数据
	 * @param tenancyId
	 * @param storeId
	 * @param optNum
	 * @param posNum
	 * @param startTimestamp
	 * @param endTimestamp
	 * @param obj
	 * @param reportDate
	 * @throws Exception
	 */
	private void getBillItemReport(String tenancyId, int storeId,String optNum, String posNum,Timestamp startTimestamp,Timestamp endTimestamp,JSONObject obj,Date reportDate) throws Exception{
		// 菜品销售实收
		double item_actual_money = 0d;
		//菜品优惠金额
		double item_yh_money = 0d; 
		//菜品奉送金额
		double  free_money_item = 0d;
		//菜品退菜金额
		double  back_money_item = 0d;
		// 菜品销售流水
		double  item_list_money = 0d;
		//售卖模式数据  堂食
		double  ts_actual_money = 0d;
		//售卖模式数据 外带
		double  wd_actual_money = 0d;
		//售卖模式数据 外送
		double  ws_actual_num = 0d;
        //外送销售金额
        double ws_actual_amount=0d;
		// 售卖模式数据 合计
		double  countsell = 0d;
		SqlRowSet rs = null ;
		StringBuffer itemSql = new StringBuffer( "select coalesce(sum(case when bi.item_remark in ('CJ05', 'TC01') then 0 else coalesce(bi.real_amount, 0) end ),0) as item_actual_money,  " );
		itemSql.append(" coalesce(sum((bi.discount_amount + bi.discountr_amount - bi.single_discount_amount) ), 0) as item_yh_money ,  ");
		itemSql.append(" coalesce(sum(case when bi.item_remark = 'FS02' then coalesce(bi.item_amount, 0) end),0) as free_money_item,");
		itemSql.append(" coalesce( sum(case when bi.item_remark in ('TC01', 'QX04') then coalesce(bi.item_amount, 0) end),0) as back_money_item, ");
		itemSql.append(" coalesce( sum(case when bi.item_remark in ('CJ05') then 0 else coalesce(bi.item_amount, 0)end),0) as item_list_money,");
		itemSql.append(" coalesce(sum(case when bi.sale_mode = 'TS01'  then coalesce(bi.real_amount, 0)end),0) as ts_actual_money, ");
		itemSql.append("  coalesce(sum(case when bi.sale_mode = 'WD02'  then coalesce(bi.real_amount, 0) end),0) as wd_actual_money,");
		itemSql.append("  coalesce(sum(case when bi.sale_mode = 'WS03' then bi.item_count else 0 end),0) as ws_actual_num,");
        itemSql.append("  coalesce(sum(case when bi.sale_mode = 'WS03' then coalesce(bi.real_amount, 0) end),0)  as ws_actual_amount");
		itemSql.append(" from pos_bill_item bi inner join pos_bill pb on pb.bill_num = bi.bill_num where (bi.item_property ='SINGLE'or  bi.item_property ='SETMEAL') and pb.bill_property = 'CLOSED' and pb.report_date=? and pb.payment_time>=? and pb.payment_time<=? and pb.tenancy_id=? and pb.store_id=? ");
		if(StringUtils.isEmpty(optNum)){
			if(StringUtils.isEmpty(posNum)){
				rs = posDao.query4SqlRowSet(itemSql.toString(), new Object[]{reportDate,startTimestamp,endTimestamp,tenancyId,storeId});
			}else{
				itemSql.append(" and pb.pos_num = ? ");
				rs = posDao.query4SqlRowSet(itemSql.toString(), new Object[]{reportDate,startTimestamp,endTimestamp,tenancyId,storeId,posNum});
			}
			
		}else{
			if(StringUtils.isEmpty(posNum)){
				itemSql.append(" and pb.cashier_num = ? ");
				rs = posDao.query4SqlRowSet(itemSql.toString(), new Object[]{reportDate,startTimestamp,endTimestamp,tenancyId,storeId,optNum});
			}else{
				itemSql.append(" and pb.cashier_num = ? ");
				itemSql.append(" and pb.pos_num = ? ");
				 rs = posDao.query4SqlRowSet(itemSql.toString(), new Object[]{reportDate,startTimestamp,endTimestamp,tenancyId,storeId,optNum,posNum});
			}
			
		}
		
		
		if(rs.next()){
			item_actual_money =  rs.getDouble("item_actual_money");
			item_yh_money =  rs.getDouble("item_yh_money");
			free_money_item =  rs.getDouble("free_money_item");
			back_money_item =  rs.getDouble("back_money_item");
			item_list_money =  rs.getDouble("item_list_money");
			
			ts_actual_money =  rs.getDouble("ts_actual_money");
			wd_actual_money =  rs.getDouble("wd_actual_money");
			ws_actual_num =  rs.getDouble("ws_actual_num");
            ws_actual_amount=  rs.getDouble("ws_actual_amount");
			countsell =  ts_actual_money+wd_actual_money+ws_actual_amount;
		}
		obj.put("item_actual_money",item_actual_money );
		obj.put("item_yh_money",item_yh_money );
		obj.put("free_money_item",free_money_item );
		obj.put("back_money_item", back_money_item);
		obj.put("item_list_money", item_list_money);
		obj.put("ts_actual_money", ts_actual_money);
		obj.put("wd_actual_money", wd_actual_money);
		obj.put("ws_actual_num", ws_actual_num);
        obj.put("ws_actual_amount", ws_actual_amount);
		obj.put("countsell", countsell);
		
	}

	@SuppressWarnings("unchecked")
	@Override
	public void signOffAndChangeShift(String tenancyId, int storeId, List<?> param, JSONObject printJson) throws Exception
	{
		if (param == null || param.size() <= 0 || param.isEmpty())
		{
			throw new SystemException(PosErrorCode.PARAM_ERROR);
		}
		
		JSONObject paraJson = JSONObject.fromObject(param.get(0));
		
		String reportDateStr = ParamUtil.getStringValueByObject(paraJson, "report_date", true, PosErrorCode.NOT_NULL_REPORT_DATE);
		Date reportDate = DateUtil.parseDate(reportDateStr);
		String optNum = ParamUtil.getStringValueByObject(paraJson, "opt_num", true, PosErrorCode.NOT_NULL_OPT_NUM);
		String posNum = ParamUtil.getStringValueByObject(paraJson, "pos_num", true, PosErrorCode.NOT_NULL_POS_NUM);
		Integer shiftId = ParamUtil.getIntegerValueByObject(paraJson, "shift_id");
		String reason = ParamUtil.getStringValueByObject(paraJson, "reason"); // 溢缺原因备注
		String busiType = ParamUtil.getStringValueByObject(paraJson, "business_type", true, PosErrorCode.NOT_NULL_BUSSINESS_TYPE);

		Integer loginCount = 0;
		if (Tools.isNullOrEmpty(paraJson.opt("opt_login_number")) == false)
		{
			loginCount = paraJson.optInt("opt_login_number");
		}
		
		String isAllPos = paraJson.optString("isallpos");
		if (StringUtils.isEmpty(isAllPos))
		{
			isAllPos = "N";
		}
		
		String empName = posDao.getEmpNameById(optNum, tenancyId, storeId);
		
		Timestamp timestamp = DateUtil.currentTimestamp();
		
		Timestamp startTime = null;
		Timestamp endTime = null;

		String workTime = paraJson.optString("work_datetime");

		if (StringUtils.isNotEmpty(workTime))
		{
			String[] array = workTime.split("~");
			if (array.length > 0)
			{
				startTime = DateUtil.formatTimestamp(array[0]);
				endTime = DateUtil.formatTimestamp(array[1]);
			}
		}
		
		posDao.checkReportDate(tenancyId, storeId, reportDate);

//		if ("2".equalsIgnoreCase(busiType))
//		{
			// 只有快餐去查

        //数据清理，删除无效数据
        StringBuilder cleanSql=new StringBuilder();
        cleanSql.append("delete from pos_bill_item i where not EXISTS (select 1 from pos_bill b where i.bill_num=b.bill_num) and report_date='"+reportDate.toString()+"';");
        cleanSql.append("delete from pos_bill_payment p where not EXISTS (select 1 from pos_bill b where p.bill_num=b.bill_num) and report_date='"+reportDate.toString()+"';");
//        cleanSql.append("update pos_bill set bill_property='CLOSED' where order_num is not null and bill_property<>'CLOSED' and report_date='"+reportDate.toString()+"';");
        posDao.getJdbcTemplate(tenancyId).execute(cleanSql.toString());
		
		// 交班是否验证有未结账单   1,是;0,否
		String isHasUnpaid = posDao.getSysParameter(tenancyId, storeId, "JBSFYZYWJZD");
		if(Tools.isNullOrEmpty(isHasUnpaid))
		{
			isHasUnpaid = "1";
		}

		StringBuilder qureySql = new StringBuilder("select count(id) as tcCount from pos_opt_state where tag='1' and content=? and shift_id=? and pos_num=? and report_date=? and store_id=? and tenancy_id=?");
		SqlRowSet sqlRowSet = posDao.query4SqlRowSet(qureySql.toString(), new Object[]
				{ SysDictionary.OPT_STATE_YYTC, shiftId, posNum,reportDate, storeId, tenancyId });
		Integer tcCount = null;
		if (sqlRowSet.next())
		{
			tcCount = sqlRowSet.getInt("tcCount");
		}

		String shiftsTimesSql ="select limit_count  from pos_setting_shifts_times";
		SqlRowSet rowSet = posDao.query4SqlRowSet(shiftsTimesSql.toString(), new Object[]{ });
		Integer limit_count = null;
		if (rowSet.next())
		{
			limit_count = rowSet.getInt("limit_count");
		}


		if("1".equals(isHasUnpaid) && tcCount!=null && limit_count!=null && limit_count>0  && tcCount+1>=limit_count)
		{


			//String daysql = new String("select count(id) as count,bill_num,open_opt,open_pos_num from pos_bill where tenancy_id=? and store_id = ? and report_date = ? and ((open_pos_num = ? and open_opt = ?) or open_pos_num in (select pd.devices_num from pos_opt_state_devices pd where pd.tenancy_id = ? and pd.store_id = ? and pd.pos_num = ? and pd.is_valid = '1')) and bill_property<>? group by bill_num,open_opt,open_pos_num");
			String daysql = new String("select count(id) as count,bill_num,open_opt,open_pos_num from pos_bill where tenancy_id=? and store_id = ? and report_date = ? and shift_id = ? and ((open_pos_num = ? ) or open_pos_num in (select pd.devices_num from pos_opt_state_devices pd where pd.tenancy_id = ? and pd.store_id = ? and pd.pos_num = ? and pd.is_valid = '1')) and bill_property<>? group by bill_num,open_opt,open_pos_num");
			SqlRowSet rsb = posDao.query4SqlRowSet(daysql, new Object[]
					//{ tenancyId, storeId, reportDate, posNum, optNum, tenancyId, storeId, posNum, SysDictionary.BILL_PROPERTY_CLOSED });
					{ tenancyId, storeId, reportDate, shiftId, posNum, tenancyId, storeId, posNum, SysDictionary.BILL_PROPERTY_CLOSED });
			if (rsb.next())
			{
				int dcount = rsb.getInt("count");
				String bill_num = rsb.getString("bill_num");
				String open_opt = rsb.getString("open_opt");
				String open_pos_num = rsb.getString("open_pos_num");
				if (dcount > 0)
				{
					String openOptName = posDao.getEmpNameById(open_opt, tenancyId, storeId);
					throw SystemException.getInstance(PosErrorCode.CHANGESHIFT_HAS_BILL_NOT_CLOSED_ERROR).set("{0}", bill_num).set("{1}", openOptName).set("{2}", open_pos_num);
				}
			}
		}
//		}

		//StringBuilder qureyOptStateSql = new StringBuilder("select id,pos_num from pos_opt_state where tag='0' and content=? and opt_num=? and report_date=? and store_id = ? and tenancy_id=?");
		StringBuilder qureyOptStateSql = new StringBuilder("select id,pos_num from pos_opt_state where tag='0' and content=? and shift_id=? and pos_num=? and report_date=? and store_id=? and tenancy_id=?");

		SqlRowSet rs = posDao.query4SqlRowSet(qureyOptStateSql.toString(), new Object[]
		//{ SysDictionary.OPT_STATE_KSSY, optNum, reportDate, storeId, tenancyId });
		{ SysDictionary.OPT_STATE_KSSY, shiftId,posNum, reportDate, storeId, tenancyId });
		int beginId = 0;
		String pos_num = "";
		if (rs.next())
		{
			beginId = rs.getInt("id");
			pos_num = rs.getString("pos_num");
		}

		if (beginId == 0)
		{
			throw SystemException.getInstance(PosErrorCode.SHIFT_NOT_SIGNIN_ERROR);
		}

		rs = posDao.query4SqlRowSet(qureyOptStateSql.toString(), new Object[]
		//{ SysDictionary.OPT_STATE_YYTC, optNum, reportDate, storeId, tenancyId });
		{ SysDictionary.OPT_STATE_YYTC, shiftId,posNum, reportDate, storeId, tenancyId });
		int endId = 0;
		if (rs.next())
		{
			endId = rs.getInt("id");
		}

		if (endId > 0 && endId > beginId)
		{
			throw SystemException.getInstance(PosErrorCode.POS_CASHIER_ALREADY_SIGNOUT_ERROR);
		}

		if (!posNum.equals(pos_num))
		{
			throw SystemException.getInstance(PosErrorCode.POS_OPEN_CLOSE_POSNUM_NOT_EQUALS).set("{0}", pos_num);
		}

		// 营业销售数据
		List<Map<String, Object>> list = (List<Map<String, Object>>) paraJson.get("pay_item");

		// 验证交班溢缺
        String paraValueStr=posDao.getSysParameter(tenancyId, storeId, "shift_limitmoney");
		Double paraValue = 0d;
		if (Tools.hv(paraValueStr))
		{
			paraValue = Double.parseDouble(paraValueStr);
		}

		if (0d < paraValue && null != list && 0 < list.size())
		{
			for (int k = 0; k < list.size(); k++)
			{
				Map<String, Object> map = list.get(k);
				double differ_amount = ParamUtil.getDoubleValue(map, "differ_amount", false, null);
				if (Math.abs(differ_amount) > paraValue)
				{
					throw SystemException.getInstance(PosErrorCode.CHANGE_SHIFT_LIMITMONEY_FAIL);
				}
			}
		}
		
		posDao.saveOptState(tenancyId, storeId, reportDate, shiftId, posNum, optNum, SysDictionary.OPT_STATE_YYTC, null, null, loginCount,endTime);



		posDao.updateOptStateForChangeShift(tenancyId, storeId, optNum, posNum, reportDate, isAllPos, shiftId);

		
		posDao.savePosLog(tenancyId, storeId, posNum, optNum, optNum, shiftId, reportDate, Constant.TITLE, "收银员签退", "", empName + "签退");
		
		String remark = paraJson.optString("remark");

		Double paid_money = ParamUtil.getDoubleValueByObject(paraJson, "paid_money");
		Double refund_money = ParamUtil.getDoubleValueByObject(paraJson, "refund_money");
		Double businessAmount = ParamUtil.getDoubleValueByObject(paraJson, "business_amount");
		Double change = ParamUtil.getDoubleValueByObject(paraJson, "change");
		Double discountkAmount = ParamUtil.getDoubleValueByObject(paraJson, "discountk_amount");
		Double discountrAmount = ParamUtil.getDoubleValueByObject(paraJson, "discountr_amount");
		Double malingAmount = ParamUtil.getDoubleValueByObject(paraJson, "maling_amount");
		Double moreCoupon = ParamUtil.getDoubleValueByObject(paraJson, "more_coupon");
		Double giviAmount = ParamUtil.getDoubleValueByObject(paraJson, "givi_amount");
		Double retreatCount = ParamUtil.getDoubleValueByObject(paraJson, "retreat_count");
		Double retreatAmount = ParamUtil.getDoubleValueByObject(paraJson, "retreat_amount");
		Double singleRetreatCount = ParamUtil.getDoubleValueByObject(paraJson, "single_retreat_count");
		Double singleRetreatAmount = ParamUtil.getDoubleValueByObject(paraJson, "single_retreat_amount");
		Double bills = ParamUtil.getDoubleValueByObject(paraJson, "bills");
		Double customerRecharge = ParamUtil.getDoubleValueByObject(paraJson, "customer_recharge");
		Double customerConsume = ParamUtil.getDoubleValueByObject(paraJson, "customer_consume");
		Double backCard = ParamUtil.getDoubleValueByObject(paraJson, "back_card");
		Double customerDeposit = ParamUtil.getDoubleValueByObject(paraJson, "customer_deposit");
		Double indemnityAmount = ParamUtil.getDoubleValueByObject(paraJson, "paid_money");
		Double refundAmount = ParamUtil.getDoubleValueByObject(paraJson, "refund_money");
		Double saleCardAmount = ParamUtil.getDoubleValueByObject(paraJson, "sale_card_amount");
		Double customerReward = ParamUtil.getDoubleValueByObject(paraJson, "customer_reward");
		Double shopDealAmount = ParamUtil.getDoubleValueByObject(paraJson, "shop_real_amount");
		Double platformChargeAmount = ParamUtil.getDoubleValueByObject(paraJson, "platform_charge_amount");
		
		StringBuffer plainText = new StringBuffer();
		plainText.append(tenancyId).append("_").append(storeId).append("_").append(reportDateStr).append("_");
		plainText.append(shiftId).append("_").append(optNum).append("_").append(posNum).append("_");
		plainText.append(startTime.getTime()).append("_").append(endTime.getTime()).append("_").append(timestamp.getTime());
		
		String changshiftUid = JavaMd5.toMd5B16(plainText.toString());

		String insertMainSql = new String(
				"insert into pos_opter_changshift_main (tenancy_id,store_id,report_date,shift_id,opt_num,pos_num,is_allpos,begain_time,end_time,business_amount,change,discountk_amount,discountr_amount,maling_amount,more_coupon,givi_amount,retreat_count,retreat_amount,single_retreat_count,single_retreat_amount,bills,customer_recharge,customer_consume,back_card,customer_deposit,indemnity_amount,refund_amount,last_updatetime,remark,upload_tag,sale_card_amount,customer_reward,shop_real_amount,platform_charge_amount,reason,changshift_uid) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ");

		posDao.update(insertMainSql, new Object[]
		{ tenancyId, storeId, reportDate, shiftId, optNum, posNum, isAllPos, startTime, endTime, businessAmount, change, discountkAmount, discountrAmount, malingAmount, moreCoupon, giviAmount, retreatCount, retreatAmount, singleRetreatCount, singleRetreatAmount, bills, customerRecharge, customerConsume, backCard, customerDeposit, indemnityAmount, refundAmount, timestamp, remark, 0, saleCardAmount, customerReward,shopDealAmount,platformChargeAmount,reason ,changshiftUid});
		
//		/**
//		 * 获取当前的序列
//		 */
//		String qseq = new String("select currval('pos_opter_changshift_main_id_seq'::regclass) ");
//		Integer pid = posDao.queryForInt(qseq, new Object[] {});

		if (list != null && list.size() > 0)
		{
			List<Object[]> batchArgs = new ArrayList<Object[]>();
			for (int k = 0; k < list.size(); k++)
			{
				Map<String, Object> map = list.get(k);

				double payment_count = ParamUtil.getDoubleValue(map, "payment_count", false, null);
				double payment_amount = ParamUtil.getDoubleValue(map, "payment_amount", false, null);
				double differ_amount = ParamUtil.getDoubleValue(map, "differ_amount", false, null);
				double give_count = ParamUtil.getDoubleValue(map, "give_count", false, null);
				double give_amount = ParamUtil.getDoubleValue(map, "give_amount", false, null);
				Integer jzid = ParamUtil.getIntegerValue(map, "jzid", false, null);
				String payment_name = ParamUtil.getStringValue(map, "payment_name", false, null);
				String sort_num = ParamUtil.getStringValue(map, "sort_num", false, null);
				String parent_sort_num = ParamUtil.getStringValue(map, "parent_sort_num", false, null);

				batchArgs.add(new Object[]
				{ tenancyId, storeId, optNum, empName, payment_count, payment_amount, give_count, give_amount, loginCount, posNum, jzid, payment_name, differ_amount, reportDate, shiftId, posNum, optNum, timestamp, startTime, endTime, changshiftUid, sort_num, parent_sort_num });
			}

			try
			{
				if (0 < batchArgs.size())
				{
					// 收银员交班金额库
					StringBuilder sql = new StringBuilder(
							"insert into pos_opter_changshift (tenancy_id,store_id,cashier_num,cashier_name,payment_count,payment_amount,give_count,give_amount,opt_login_number,pos_num,jzid,payment_name,differ_amount,report_date,shift_id,opt_pos_num,opt_num,last_updatetime,begain_shift,end_shift,changshift_id,sort_num,parent_sort_num) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");

					posDao.batchUpdate(sql.toString(), batchArgs);
				}
			}
			catch (Exception se)
			{
				logger.info("收银员交班写库错误：" + ExceptionMessage.getExceptionMessage(se));
				throw new SystemException(PosErrorCode.OPER_ERROR);
			}
		}
		
		if ("2".equalsIgnoreCase(busiType))
		{
			// 用来获取退款数量和退款金额
			String qpaidMoney = new String("select count(id) as back_num,sum(payment_amount) as back_money from pos_bill where bill_property='CLOSED' and bill_state='ZDQX02' and payment_time>? and payment_time<? and store_id = ?");
			double back_num = 0;
			double back_money = 0;

			SqlRowSet qrst = posDao.query4SqlRowSet(qpaidMoney, new Object[]
			{ startTime, endTime, storeId });
			while (qrst.next())
			{
				back_num = qrst.getDouble("back_num");
				back_money = qrst.getDouble("back_money");
			}

			String insertSql = new String(
					"insert into pos_opter_paidrefund (tenancy_id,store_id,changshift_id,start_time,end_time,shift_id,report_date,pos_num,paid_money,refund_money,cashier_num,cashier_name,oprater_time,upload_tag,back_num,back_money,remark) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ");

			posDao.update(insertSql, new Object[]
			{ tenancyId, storeId, changshiftUid, startTime, endTime, shiftId, reportDate, posNum, paid_money, refund_money, optNum, empName, timestamp, 0, back_num, back_money, remark });
		}
		
		// 会员结算数据
		List<Map<String, Object>> listActivation = (List<Map<String, Object>>) paraJson.get("card_item");
		
		if (listActivation != null && listActivation.size() > 0)
		{
			List<Object[]> batchArgs = new ArrayList<Object[]>();
			for (int k = 0; k < listActivation.size(); k++)
			{
				Map<String, Object> map = listActivation.get(k);

				String operatType = ParamUtil.getStringValue(map, "operat_type_code", false, null);
				String operatDetails = ParamUtil.getStringValue(map, "operat_details", false, null);
				Integer paymentId = ParamUtil.getIntegerValue(map, "jzid", false, null);
				double tradeAmount = ParamUtil.getDoubleValue(map, "payment_amount", false, null);
				double realAmount = ParamUtil.getDoubleValue(map, "give_amount", false, null);
				double differAmount = ParamUtil.getDoubleValue(map, "differ_amount", false, null);

				batchArgs.add(new Object[]
				{ tenancyId, storeId, changshiftUid, reportDate, shiftId, optNum, posNum, operatType, operatDetails, paymentId, tradeAmount, realAmount, differAmount, timestamp, remark, 0 });
			}

			try
			{
				if (0 < batchArgs.size())
				{
					// 收银员交班会员结算表
					StringBuilder sql = new StringBuilder(
							"insert into pos_opter_changshift_customer (tenancy_id,store_id,changshift_id,report_date,shift_id,opt_num,pos_num,operat_type,operat_details,payment_id,trade_amount,real_amount,differ_amount,last_updatetime,remark,upload_tag) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");

					posDao.batchUpdate(sql.toString(), batchArgs);
				}
			}
			catch (Exception se)
			{
				logger.info("收银员交班写库错误：" + ExceptionMessage.getExceptionMessage(se));
				throw new SystemException(PosErrorCode.OPER_ERROR);
			}
		}
		
		//交班单打印
		if(null == printJson)
		{
			printJson = new JSONObject();
		}
		printJson.put("tenancy_id", tenancyId);
		printJson.put("store_id", storeId);
		printJson.put("print_code", SysDictionary.PRINT_CODE_1301);
		printJson.put("mode", "0");
		printJson.put("print_type", "1");
		
		printJson.put("report_date", reportDateStr);
		printJson.put("shift_id", shiftId);
		printJson.put("pos_num",posNum);
		printJson.put("opt_num",optNum);
		printJson.put("date_remark", DateUtil.format(startTime)+"~"+DateUtil.format(endTime));
	}
	
	@Override
	public void checkChangShift(Data param, Data result) throws SystemException
	{
		String tenantId = param.getTenancy_id();
		Integer organId = param.getStore_id();

		Map<String, Object> map = ReqDataUtil.getDataMap(param);

		String posNum = ParamUtil.getStringValue(map, "pos_num", false, null);// 机号
		String optNum = ParamUtil.getStringValue(map, "opt_num", true, PosErrorCode.NOT_NULL_OPT_NUM);
		Date reportDate = ParamUtil.getDateValue(map, "report_date", true, PosErrorCode.NOT_NULL_REPORT_DATE);

		try
		{
			List<?> dataList = posDao.getChangshiftList(tenantId, organId, reportDate, optNum, posNum);

			result.setCode(Constant.CODE_SUCCESS);
			result.setMsg(Constant.CHECK_CHANGSHIFT_SUCCESS);
			result.setData(dataList);
		}
		catch (Exception se)
		{
			result.setCode(Constant.CODE_INNER_EXCEPTION);
			result.setMsg(Constant.CHECK_CHANGSHIFT_FAILURE);
			logger.info("交班查询失败，原因：" + ExceptionMessage.getExceptionMessage(se));
			se.printStackTrace();
		}
	}
	
	@Override
	public void getDetailChangShift(Data param, Data result) throws SystemException
	{
		String tenantId = param.getTenancy_id();
		Integer organId = param.getStore_id();

		int scale = 4;
		
		Map<String, Object> map = ReqDataUtil.getDataMap(param);

		String posNum = ParamUtil.getStringValue(map, "pos_num", false, null);// 机号
		String optNum = ParamUtil.getStringValue(map, "opt_num", true, PosErrorCode.NOT_NULL_OPT_NUM);
		Date reportDate = ParamUtil.getDateValue(map, "report_date", true, PosErrorCode.NOT_NULL_REPORT_DATE);
		String startTime = ParamUtil.getStringValue(map, "start_time", false, null);
		String endTime = ParamUtil.getStringValue(map, "end_time", false, null);
		String changShiftId = ParamUtil.getStringValue(map, "changshift_id", false, null);

		SqlRowSet rs = null;
	
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject obj = new JSONObject();
		//obj.put("work_datetime",reportDate);
		obj.put("report_date",DateUtil.format(reportDate, "yyyy-MM-dd"));
		try
		{
			StringBuilder querySql = new StringBuilder("select format_state, org_short_name from organ where tenancy_id=? and id=?");
			SqlRowSet rsOrgan = posDao.query4SqlRowSet(querySql.toString(), new Object[]
			{ tenantId, organId });
			if (rsOrgan.next())
			{
				obj.put("org_short_name", rsOrgan.getString("org_short_name"));
			}
			
			Timestamp startTimestamp = null;
			if (StringUtils.isNotEmpty(startTime))
			{
				startTimestamp = DateUtil.parseTimestamp(startTime);
			}
			Timestamp endTimestamp = null;
			if (StringUtils.isNotEmpty(endTime))
			{
				endTimestamp = DateUtil.parseTimestamp(endTime);
			}
			
//			StringBuilder msql = new StringBuilder("select * from pos_opter_changshift_main where tenancy_id = ? and store_id = ? and changshift_uid = ? ");
			StringBuilder msql = new StringBuilder("select pm.*,sd.class_item as name from pos_opter_changshift_main pm ");
			msql.append(" left join duty_order dor on dor.id = pm.shift_id left join sys_dictionary sd on sd.class_item_code= dor.name and sd.class_identifier_code = 'duty' ");
			msql.append(" where pm.tenancy_id=? and pm.store_id=? and pm.report_date=? and pm.changshift_uid=? ");
			rs = posDao.query4SqlRowSet(msql.toString(), new Object[]
			{ tenantId, organId, reportDate, changShiftId });

			if (rs.next())
			{
				// 营业数据
				obj.put("bills", rs.getDouble("bills"));
				obj.put("changet_amount", rs.getDouble("change"));
				obj.put("paid_money", rs.getDouble("indemnity_amount"));
				obj.put("refund_amount", rs.getDouble("refund_amount"));
				obj.put("business_amount", rs.getDouble("business_amount"));
				obj.put("discountk_amount", rs.getDouble("discountk_amount"));
				obj.put("discountr_amount", rs.getDouble("discountr_amount"));
				obj.put("maling_amount", rs.getDouble("maling_amount"));
				obj.put("more_coupon", rs.getDouble("more_coupon"));
				obj.put("givi_amount", rs.getDouble("givi_amount"));
				obj.put("back_num", rs.getDouble("retreat_count"));
				obj.put("back_money", rs.getDouble("retreat_amount"));
				obj.put("single_retreat_count", rs.getDouble("single_retreat_count"));
				obj.put("single_retreat_amount", rs.getDouble("single_retreat_amount"));
				obj.put("shop_real_amount", rs.getDouble("shop_real_amount"));
				obj.put("platform_charge_amount", rs.getDouble("platform_charge_amount"));
				// 会员结算数据
				obj.put("customer_sell", rs.getDouble("sale_card_amount"));
				obj.put("customer_recharge", rs.getDouble("customer_recharge"));
				obj.put("customer_consume", rs.getDouble("customer_consume"));
				obj.put("back_card", rs.getDouble("back_card"));
				double precipitate_amount = DoubleHelper.sub(DoubleHelper.sub(rs.getDouble("customer_recharge"), rs.getDouble("customer_consume"), 2), rs.getDouble("back_card"), 2);
				obj.put("precipitate_amount", precipitate_amount);
				obj.put("customer_deposit", rs.getDouble("customer_deposit"));
				//溢缺原因备注
				obj.put("reason", rs.getString("reason"));
				obj.put("duty_order_name", rs.getString("name"));
			}
					
			// 营业数据
			StringBuilder changshiftsql = new StringBuilder("select payment_name,payment_count,payment_amount,give_count,give_amount,differ_amount,coalesce(sort_num,'0') sort_num,coalesce(parent_sort_num,'0') parent_sort_num from pos_opter_changshift where tenancy_id = ? and store_id = ? and changshift_id = ? order by sort_num ");
			rs = posDao.query4SqlRowSet(changshiftsql.toString(), new Object[]{ tenantId, organId, changShiftId });
			
			JSONArray arr = new JSONArray();
			while (rs.next())
			{
				JSONObject json = new JSONObject();
				json.put("payment_name", rs.getString("payment_name"));
				json.put("payment_count", rs.getInt("payment_count"));
				json.put("payment_amount", rs.getDouble("payment_amount"));
				json.put("give_count", rs.getInt("give_count"));
				json.put("give_amount", rs.getDouble("give_amount"));
				json.put("differ_amount", rs.getDouble("differ_amount"));
				json.put("can_modify", "Y");
				json.put("sort_num", rs.getString("sort_num"));
				json.put("parent_sort_num", rs.getString("parent_sort_num"));
				arr.add(json);
			}
			obj.put("pay_item", arr);
			
			//TODO 会员结算数据
//			StringBuilder changshifCustsql = new StringBuilder("select case when pocc.operat_type='SK01' then '发卡售卡金额' when pocc.operat_type='YJ02' then '发卡押金金额' when pocc.operat_type='02' then '充值' when pocc.operat_type='03' then '消费' when pocc.operat_type='12' then '退卡' when pocc.operat_type='13' then '购买会籍' else '' end as operat_type,case when pocc.operat_details='YZD03' then '有账单' when pocc.operat_details='WZD04' then '无账单' when pocc.operat_details='TK05' then '退卡金额' when pocc.operat_details='TYJ06' then '退押金金额' else (case when pw.payment_class = 'cash' and d.id is not null then d.class_item else pw.payment_name1 end) end as payment_name,pocc.trade_amount,pocc.real_amount,pocc.differ_amount");
			StringBuilder changshifCustsql = new StringBuilder("select pocc.operat_type,pocc.operat_details,(case when d.id is not null then d.class_item else pw.payment_name1 end) as payment_name,pocc.trade_amount,pocc.real_amount,pocc.differ_amount");
			changshifCustsql.append(" from pos_opter_changshift_customer pocc left join payment_way pw on pocc.payment_id = pw.id left join sys_dictionary d on d.class_item_code = pw.payment_name1 and d.class_identifier_code='currency' where pocc.tenancy_id = ? and pocc.store_id = ? and pocc.changshift_id = ? order by operat_type,pocc.operat_details");
			rs = posDao.query4SqlRowSet(changshifCustsql.toString(), new Object[]{ tenantId, organId, changShiftId });
			
			JSONArray arrCustomer = new JSONArray();
			while (rs.next())
			{
				String operatType = "";
				String paymentName = "";
				
				switch (rs.getString("operat_type"))
				{
					case SysDictionary.OPERAT_TYPE_FK:
						if (SysDictionary.CUSTOMER_SALECARD_MONEY.equals(rs.getString("operat_details")))
						{
							operatType = SysDictionary.CUSTOMER_SALECARD_MONEY_TXT;
							paymentName = rs.getString("payment_name");
						}
						else
						{
							operatType = SysDictionary.CUSTOMER_DEPOSIT_MONEY_TXT;
							paymentName = rs.getString("payment_name");
						}
						break;
					case SysDictionary.OPERAT_TYPE_CZ:
						operatType = SysDictionary.CUSTOMER_RECHARGE_TXT;
						paymentName = rs.getString("payment_name");
						break;
					case SysDictionary.OPERAT_TYPE_XF:
						if (SysDictionary.CUSTOMER_DETAIL_HAVE_BILL.equals(rs.getString("operat_details")))
						{
							operatType = "消费";
							paymentName = SysDictionary.CUSTOMER_DETAIL_HAVE_BILL_TXT;
						}
						else
						{
							operatType = "消费";
							paymentName = SysDictionary.CUSTOMER_DETAIL_NO_BILL_TXT;
						}
						break;
					case SysDictionary.OPERAT_TYPE_TK:
						if (SysDictionary.CUSTOMER_BACKCARD_MONEY.equals(rs.getString("operat_details")))
						{
							operatType = "退卡";
							paymentName = SysDictionary.CUSTOMER_BACKCARD_MONEY_TXT;
						}
						else
						{
							operatType = "退卡";
							paymentName = SysDictionary.CUSTOMER_BACKDEPOSIT_MONEY_TXT;
						}
						break;
					case SysDictionary.OPERAT_TYPE_GMHJ:
						operatType = SysDictionary.CUSTOMER_BUYVIP_TXT;
						paymentName = rs.getString("payment_name");
						break;
				}
				
				
				JSONObject json = new JSONObject();
				json.put("operat_type", operatType);
				json.put("payment_name", paymentName);
				json.put("payment_amount", rs.getDouble("trade_amount"));
				json.put("give_amount", rs.getDouble("real_amount"));
				json.put("differ_amount", rs.getDouble("differ_amount"));
				json.put("can_modify", "Y");
				arrCustomer.add(json);
			}
			obj.put("card_item", arrCustomer);
			
			// 查询交款数据
			this.getShiftDataForCashierReceiveForHis(tenantId, organId, reportDate, optNum, posNum, startTimestamp, endTimestamp, obj, scale);
			
			/**交班详细 **/
			double bill_amount = 0d;// 营业应收
			double more_coupon = 0d; //多收礼券
			double sale_person_num = 0d;//消费客数
			double sale_billnum = 0d;//账单数

//			double coupon_buy_price = 0d; // 美团票券用户实付
//			double due = 0d; // 美团票券账单净收
			double tenancy_assume = 0d; // 美团票券商户承担
			double third_assume = 0d;	// 美团票券第三方承担
			double third_fee = 0d; 	// 美团票券第三方服务费

			StringBuilder sqlAmount = new StringBuilder("select sum(pb.discountk_amount) as discountk_amount,sum(pb.discountr_amount) as discountr_amount,sum(pb.maling_amount) as maling_amount,sum(pb.givi_amount) as givi_amount,sum(pb.more_coupon) as more_coupon,sum(coalesce(pb.shop_real_amount,0)) as shop_real_amount,sum(coalesce(pb.platform_charge_amount,0)) as platform_charge_amount,sum(coalesce(service_amount,0)) as service_amount,");
			sqlAmount.append("sum(coalesce(pb.coupon_buy_price,0)) as coupon_buy_price, sum(coalesce(pb.due,0)) as due, sum(coalesce(pb.tenancy_assume,0)) as tenancy_assume, sum(coalesce(pb.third_assume,0)) as third_assume, sum(coalesce(pb.third_fee,0)) as third_fee,coalesce(sum(guest), 0) as sale_person_num,count(case when coalesce(pb.bill_state, 'zc') not in ('CJ01', 'ZDQX02') then pb.bill_num end) as sale_billnum,sum(pb.bill_amount) as bill_amount");
			//sqlAmount.append(" from pos_bill pb where pb.store_id = ? and pb.report_date = ? and pb.payment_time > ? and pb.payment_time < ? and pb.bill_property = ? and  pb.cashier_num = ? and pb.pos_num=?");
			sqlAmount.append(" from pos_bill pb where pb.store_id = ? and pb.report_date = ? and pb.payment_time > ? and pb.payment_time < ? and pb.bill_property = ? and pb.pos_num=?");
			SqlRowSet rsAmount = posDao.query4SqlRowSet(sqlAmount.toString(), new Object[]
			//{ organId, reportDate, startTimestamp, endTimestamp ,SysDictionary.BILL_PROPERTY_CLOSED,optNum,posNum});
			{ organId, reportDate, startTimestamp, endTimestamp ,SysDictionary.BILL_PROPERTY_CLOSED,posNum});
			while (rsAmount.next())
			{
				bill_amount =  rsAmount.getDouble("bill_amount");
				sale_person_num = rsAmount.getDouble("sale_person_num");
				sale_billnum =  rsAmount.getDouble("sale_billnum");
				more_coupon =  rsAmount.getDouble("more_coupon");
				// 折扣
				obj.put("discountk_amount", rsAmount.getDouble("discountk_amount"));
				// 折让
				obj.put("discountr_amount", rsAmount.getDouble("discountr_amount"));
				// 抹零
				obj.put("maling_amount", rsAmount.getDouble("maling_amount"));
				// 奉送
				obj.put("givi_amount", rsAmount.getDouble("givi_amount"));
				// 多收礼券
				obj.put("more_coupon", more_coupon);
				// 商家实收
				obj.put("shop_real_amount", rsAmount.getDouble("shop_real_amount"));
				// 外卖平台收取金额
				obj.put("platform_charge_amount", rsAmount.getDouble("platform_charge_amount"));
				// 服务费
				obj.put("service_amount", rsAmount.getDouble("service_amount"));
				// 营业应收
				obj.put("bill_amount", bill_amount);
				//消费客数
				obj.put("sale_person_num", sale_person_num);
				//账单数
				obj.put("sale_billnum", sale_billnum);
				//人均均值
				if(sale_person_num != 0){
					obj.put("sale_person_average", DoubleHelper.div(bill_amount+more_coupon,sale_person_num,2));
				}else{
					obj.put("sale_person_average",0);
				}
				//账单均值
				if(sale_person_num != 0){
					obj.put("sale_billaverage", DoubleHelper.div(bill_amount+more_coupon,sale_billnum,2));
				}else{
					obj.put("sale_billaverage",0);
				}

//				coupon_buy_price = rsAmount.getDouble("coupon_buy_price");
//				due = rsAmount.getDouble("due");
				tenancy_assume = rsAmount.getDouble("tenancy_assume");
				third_assume = rsAmount.getDouble("third_assume");
				third_fee = rsAmount.getDouble("third_fee");

				// 美团票券虚收  =  商家承担 + 第三方服务费
				obj.put("tenancy_assume", DoubleHelper.round(tenancy_assume + third_fee, 2));
				// 美团票券第三方承担
				obj.put("third_assume", DoubleHelper.round(third_assume, 2));

				// 顾客实付= 账单应收-第三方支付优惠(商家承担 + 第三方承担)
				double customer_real_pay = DoubleHelper.round(bill_amount - tenancy_assume - third_assume, 2);
				// 账单净收 = 账单应收 - 商家承担优惠 - 票券服务费
				double bill_due = DoubleHelper.round(bill_amount - tenancy_assume - third_fee, 2) ;
				double service_amount = rsAmount.getDouble("service_amount");
				// 菜品净收 = 账单净收- 服务费
				double item_actual = DoubleHelper.round(bill_due - service_amount, 2);

				// 顾客实付
				obj.put("customer_real_pay", customer_real_pay);
				// 账单净收
				obj.put("bill_due", bill_due);
				// 菜品净收
				obj.put("item_actual", item_actual);

			}
			
			double  payment_total = 0d;//营业实收  = 收款方式合计
			StringBuffer paymentTotalSql = new StringBuffer(" select coalesce(sum(p.currency_amount), 0) as payment_total  from pos_bill_payment p left join pos_bill pb on p.bill_num = pb.bill_num ");
			//paymentTotalSql.append(" where pb.store_id = ? and pb.report_date = ? and pb.payment_time > ? and pb.payment_time < ? and pb.bill_property = ? and  pb.cashier_num = ? and pb.pos_num=?");
			paymentTotalSql.append(" where pb.store_id = ? and pb.report_date = ? and pb.payment_time > ? and pb.payment_time < ? and pb.bill_property = ? and pb.pos_num=?");
			SqlRowSet paymentTotalRs = posDao.query4SqlRowSet(paymentTotalSql.toString(), new Object[]
					//{ organId, reportDate, startTimestamp, endTimestamp ,SysDictionary.BILL_PROPERTY_CLOSED,optNum,posNum});
					{ organId, reportDate, startTimestamp, endTimestamp ,SysDictionary.BILL_PROPERTY_CLOSED,posNum});
			if(paymentTotalRs.next()){
				payment_total = paymentTotalRs.getDouble("payment_total");
			}
			obj.put("payment_total",payment_total);
			obj.put("real_amount",DoubleHelper.sub(payment_total, more_coupon, scale));
			
			double  no_income_money = 0d;//不计收入付款方式金额
			StringBuffer noIncomeMoneySql = new StringBuffer(" select sum(case when w.if_income='0' then coalesce(p.currency_amount, 0) else 0 end) no_income_money ");
			noIncomeMoneySql.append(" from pos_bill_payment p left join pos_bill pb on p.bill_num = pb.bill_num left join payment_way w on p.jzid = w.id");
			//noIncomeMoneySql.append(" where pb.store_id = ? and pb.report_date = ? and pb.payment_time > ? and pb.payment_time < ? and pb.bill_property = ? and  pb.cashier_num = ? and pb.pos_num =?");
			noIncomeMoneySql.append(" where pb.store_id = ? and pb.report_date = ? and pb.payment_time > ? and pb.payment_time < ? and pb.bill_property = ? and pb.pos_num =?");
			SqlRowSet noIncomeMoneyRs = posDao.query4SqlRowSet(noIncomeMoneySql.toString(), new Object[]
					//{ organId, reportDate, startTimestamp, endTimestamp ,SysDictionary.BILL_PROPERTY_CLOSED,optNum,posNum});
					{ organId, reportDate, startTimestamp, endTimestamp ,SysDictionary.BILL_PROPERTY_CLOSED,posNum});
			if(noIncomeMoneyRs.next()){
				no_income_money = noIncomeMoneyRs.getDouble("no_income_money");
			}
			obj.put("no_income_money",no_income_money);
			obj.put("real_income",payment_total-no_income_money);//付款实际收入
			
			StringBuffer tableDataSql = new StringBuffer("select coalesce(count(t.id),0) as tables_num, coalesce(sum(t.seat_counts), 0) as seat_num from tables_info t where t.tenancy_id=? and t.organ_id=? and t.valid_state = '1' ");
			SqlRowSet tableDataRs = posDao.query4SqlRowSet(tableDataSql.toString(), new Object[]
			{ tenantId, organId });
			
			if(tableDataRs.next()){
				double tables_num =  tableDataRs.getDouble("tables_num");//桌台数
				double seat_num =  tableDataRs.getDouble("seat_num");//座位数
				obj.put("tables_num", tables_num);
				obj.put("seat_num", seat_num);
				//翻台率 =【消费账单】/【桌台数】
				if(tables_num != 0 ){
					obj.put("num_ft", DoubleHelper.div(sale_billnum, tables_num, 2));//翻台率
				}else{
					obj.put("num_ft", 0d);//翻台率
				}
				// 上座率 = 【消费客数】/【座位数】
				if(seat_num != 0 ){
					obj.put("num_sz", DoubleHelper.div(sale_person_num,seat_num,2));//上座率
				}else{
					obj.put("num_sz", 0d);//上座率
				}
			}

			double back_num = 0;
			double back_money = 0;
			double retreat_num = 0;
			double retreat_money = 0;
			double recharge_money = 0;
			double reward_money = 0;
			double consume_money = 0;
			double backout_money = 0;
			// 用来获取退款数量和退款金额
			StringBuilder qpaidMoney = new StringBuilder("select count(bi.id) as back_num,sum(bi.payment_amount) as back_money from pos_bill bi");
			qpaidMoney.append(" inner join pos_bill bi2 on bi.tenancy_id=bi2.tenancy_id and bi.store_id=bi2.store_id and bi.bill_num=bi2.copy_bill_num and bi.bill_state='ZDQX02' and bi.bill_property='CLOSED' and bi2.bill_state='CJ01' and bi2.bill_property='CLOSED'");
			//qpaidMoney.append(" where bi2.tenancy_id=? and bi2.store_id =? and bi2.cashier_num =? and bi2.payment_time>? and bi2.payment_time<? and bi2.pos_num = ?");
			qpaidMoney.append(" where bi2.tenancy_id=? and bi2.store_id =? and bi2.payment_time>? and bi2.payment_time<? and bi2.pos_num = ?");
			SqlRowSet qrst = posDao.query4SqlRowSet(qpaidMoney.toString(), new Object[]
			//{ tenantId, organId, optNum, startTimestamp, endTimestamp ,posNum});
			{ tenantId, organId, startTimestamp, endTimestamp ,posNum});
			if (qrst.next())
			{
				back_num = qrst.getDouble("back_num");
				back_money = qrst.getDouble("back_money");
			}

			obj.put("back_num", back_num);
			obj.put("back_money", back_money);

			// 查询单品退菜数量和金额
			StringBuilder singleRetreatSb = new StringBuilder(
					"select coalesce(count(p.bill_num),'0') as single_retreat_count,coalesce(round(sum (p.payment_amount),2),0) as single_retreat_amount from pos_bill p inner join pos_bill pb on p.copy_bill_num = pb.bill_num and (pb.bill_state is null or pb.bill_state <> 'ZDQX02')");
			//singleRetreatSb.append(" where p .payment_time >? and p .payment_time <? and p.cashier_num =? and p .tenancy_id =? and p .store_id =? and p.bill_state = 'CJ01' and p.pos_num=?");
			singleRetreatSb.append(" where p .payment_time >? and p .payment_time <?  and p .tenancy_id =? and p .store_id =? and p.bill_state = 'CJ01' and p.pos_num=?");
			SqlRowSet singleRetreatRs = posDao.query4SqlRowSet(singleRetreatSb.toString(), new Object[]
			//{ startTimestamp, endTimestamp, optNum, tenantId, organId,posNum });
			{ startTimestamp, endTimestamp, tenantId, organId,posNum });
			if (singleRetreatRs.next())
			{
				retreat_num = singleRetreatRs.getDouble("single_retreat_count");
				retreat_money = singleRetreatRs.getDouble("single_retreat_amount");
			}
			obj.put("single_retreat_count", retreat_num);
			obj.put("single_retreat_amount", retreat_money);
			
			String qamount = new String("select coalesce(sum(amount),'0') as amount from pos_byjdc_log where last_updatetime > ? and last_updatetime < ? and report_date = ? and store_id = ? and opt_type = ?");
			//备用金
			double amount = 0;
			SqlRowSet rsChangetAmount = posDao.query4SqlRowSet(qamount, new Object[]
			//{ optNum, startTimestamp, endTimestamp, reportDate, organId, "changet" });
			{  startTimestamp, endTimestamp, reportDate, organId, "changet" });
			if (rsChangetAmount.next())
			{
				amount = rsChangetAmount.getDouble("amount");
			}
			obj.put("changet_amount", amount);

			// 抽大钞
			qamount = new String("select coalesce(sum(amount),'0') as amount from pos_byjdc_log where last_updatetime > ? and last_updatetime < ? and report_date = ? and store_id = ? and opt_type = ?");
			double bills_money = 0;
			SqlRowSet billsRs = posDao.query4SqlRowSet(qamount, new Object[]
			{ startTimestamp, endTimestamp, reportDate, organId, "bills" });
			if (billsRs.next())
			{
				bills_money = billsRs.getDouble("amount");
			}
			obj.put("bills", bills_money);
			optNum= null;
			getBillItemReport(tenantId, organId, optNum,posNum, startTimestamp, endTimestamp, obj, reportDate);
			
			// 充值合计，赠送合计
			StringBuilder rechargeSb = new StringBuilder("select coalesce(sum(cctl.main_trading),'0') as recharge_money, coalesce(sum(cctl.reward_trading),'0') as reward_money from crm_card_trading_list cctl ");
			rechargeSb.append(" left join pos_cashier_receive_log_details rd on cctl.third_bill_code=rd.bill_num and cctl.tenancy_id=rd.tenancy_id");
			//rechargeSb.append(" where cctl.operat_type in ('02','04') and ((cctl.operator_id = ? and cctl.operate_time > ? and cctl.operate_time < ?) or (rd.cashier_id=? and rd.last_updatetime>? and rd.last_updatetime<?)) and cctl.tenancy_id=? and cctl.store_id=?");
			rechargeSb.append(" where cctl.operat_type in ('02','04') and (( cctl.operate_time > ? and cctl.operate_time < ?) or ( rd.last_updatetime>? and rd.last_updatetime<?)) and cctl.tenancy_id=? and cctl.store_id=?");
			SqlRowSet rechargeRs = posDao.query4SqlRowSet(rechargeSb.toString(), new Object[]
			//{ Integer.parseInt(optNum), startTimestamp, endTimestamp,Integer.parseInt(optNum), startTimestamp, endTimestamp, tenantId, organId });
			{ startTimestamp, endTimestamp, startTimestamp, endTimestamp, tenantId, organId });
			if (rechargeRs.next())
			{
				recharge_money = rechargeRs.getDouble("recharge_money");
				reward_money = rechargeRs.getDouble("reward_money");
			}
			obj.put("customer_recharge", recharge_money);
			obj.put("customer_reward", reward_money);

			// 消费合计
//			StringBuilder consumeSb = new StringBuilder(
//					"select coalesce(sum(cctl.main_trading + cctl.reward_trading),'0') as consume_money from crm_card_trading_list cctl where cctl.operat_type in ('03','05') and cctl.operator_id =? and cctl.operate_time >? and cctl.operate_time <? and cctl.tenancy_id=? and cctl.store_id=?");
			StringBuilder consumeSb = new StringBuilder(
					"select coalesce(sum(cctl.main_trading + cctl.reward_trading),'0') as consume_money from crm_card_trading_list cctl where cctl.operat_type in ('03','05') and cctl.operate_time >? and cctl.operate_time <? and cctl.tenancy_id=? and cctl.store_id=?");
			SqlRowSet consumeRs = posDao.query4SqlRowSet(consumeSb.toString(), new Object[]
			//{ Integer.parseInt(optNum), startTimestamp, endTimestamp, tenantId, organId });
			{  startTimestamp, endTimestamp, tenantId, organId });
			if (consumeRs.next())
			{
				consume_money = consumeRs.getDouble("consume_money");
			}
			obj.put("customer_consume", consume_money);

			// 退卡金额
			//StringBuilder backoutSb = new StringBuilder("select coalesce(sum(cctl.main_trading),'0') as backout_money from crm_card_trading_list cctl where cctl.operat_type='12' and cctl.operator_id =? and cctl.operate_time >? and cctl.operate_time <? and cctl.tenancy_id=? and cctl.store_id=?");
			StringBuilder backoutSb = new StringBuilder("select coalesce(sum(cctl.main_trading),'0') as backout_money from crm_card_trading_list cctl where cctl.operat_type='12' and cctl.operate_time >? and cctl.operate_time <? and cctl.tenancy_id=? and cctl.store_id=?");
			SqlRowSet backoutRs = posDao.query4SqlRowSet(backoutSb.toString(), new Object[]
			//{ Integer.parseInt(optNum), startTimestamp, endTimestamp, tenantId, organId });
			{  startTimestamp, endTimestamp, tenantId, organId });
			if (backoutRs.next())
			{
				backout_money = Math.abs(backoutRs.getDouble("backout_money"));
			}
			obj.put("back_card", backout_money);

			// 金额沉淀
			double precipitate_amount = DoubleHelper.sub(DoubleHelper.sub(recharge_money, consume_money, 2), backout_money, 2);
			obj.put("precipitate_amount", precipitate_amount);

			
			//会员卡付款统计
			//本系统卡
			double total_card = 0d ;
			//前台消费卡主帐户金额
			double qt_xf_main_trading = 0d;
			//前台消费卡赠送账户金额
			double qt_xf_reward_trading =0d;
			//【后台消费卡主帐户金额】
			double ht_xf_main_trading = 0d;
			//【后台消费卡赠送账户金额】
			double ht_xf_reward_trading =0d;
			
			StringBuffer loadcardSql = new StringBuffer("select coalesce(sum(case when bi.bill_num is not null then coalesce(main_trading, 0)end),0) as qt_xf_main_trading,");
			loadcardSql.append(" coalesce(sum(case when bi.bill_num is not null then coalesce(reward_trading, 0)end),0) as qt_xf_reward_trading,");
			loadcardSql.append(" coalesce(sum(case when bi.bill_num is null then coalesce(main_trading, 0)end),0) as ht_xf_main_trading,");
			loadcardSql.append(" coalesce(sum(case when bi.bill_num is null then coalesce(reward_trading, 0)end),0) as ht_xf_reward_trading");
			loadcardSql.append(" from crm_card_trading_list tl left join pos_bill bi on tl.third_bill_code=bi.bill_num");
			//loadcardSql.append(" where tl.tenancy_id=? and tl.store_id=? and tl.operate_time>=? and tl.operate_time<=? and tl.operat_type in (?, ?) and tl.operator_id=?");
			loadcardSql.append(" where tl.tenancy_id=? and tl.store_id=? and tl.operate_time>=? and tl.operate_time<=? and tl.operat_type in (?, ?)");

			SqlRowSet loadcardRs = posDao.query4SqlRowSet(loadcardSql.toString(), new Object[]
			//{ tenantId, organId, startTimestamp, endTimestamp, SysDictionary.OPERAT_TYPE_XF, SysDictionary.OPERAT_TYPE_FXF, Integer.parseInt(optNum) });
			{ tenantId, organId, startTimestamp, endTimestamp, SysDictionary.OPERAT_TYPE_XF, SysDictionary.OPERAT_TYPE_FXF });

			if (loadcardRs.next())
			{
				qt_xf_main_trading = loadcardRs.getDouble("qt_xf_main_trading");
				qt_xf_reward_trading = loadcardRs.getDouble("qt_xf_reward_trading");
				total_card = DoubleHelper.add(qt_xf_main_trading, qt_xf_reward_trading, 4);

				ht_xf_main_trading = loadcardRs.getDouble("ht_xf_main_trading");
				ht_xf_reward_trading = loadcardRs.getDouble("ht_xf_reward_trading");
			}
			
			obj.put("total_card", total_card);
			obj.put("qt_xf_main_trading", qt_xf_main_trading);
			obj.put("qt_xf_reward_trading", qt_xf_reward_trading);
			obj.put("ht_xf_main_trading", ht_xf_main_trading);
			obj.put("ht_xf_reward_trading", ht_xf_reward_trading);
			
			// 大类名称、大类数量、大类金额
			StringBuffer classSql = new StringBuffer();
			classSql.append("select hicc.itemclass_name, count(hicc.id) as numbers, round(sum(pbi.real_amount), 2) as real_amount from pos_bill_item pbi inner join (select bill_num, store_id, report_date from pos_bill where payment_time >= ? and payment_time <= ? ");
			//classSql.append(" and report_date = ? and store_id = ? and tenancy_id = ? and cashier_num = ? and pos_num = ? and bill_property = 'CLOSED') as pb on pbi.bill_num = pb.bill_num ");
			classSql.append(" and report_date = ? and store_id = ? and tenancy_id = ? and pos_num = ? and bill_property = 'CLOSED') as pb on pbi.bill_num = pb.bill_num ");
			classSql.append(" and pbi.report_date=pb.report_date and pbi.store_id =pb.store_id left join hq_item_menu_details himd on pbi.details_id = himd.id and pbi.item_id = himd.item_id left join hq_item_menu_organ himo on himd.item_menu_id = himo.item_menu_id left join ");
			classSql.append(" hq_item_menu_class himc on himd. id = himc.details_id left join hq_item_class hic on himc. class = hic.id left join hq_item_class hicc on hic.father_id = hicc.id where hicc.valid_state = '1' and hic.valid_state = '1' and himd.valid_state = '1' and himo.store_id = ? group by hicc. id, hicc.itemclass_name ");

			SqlRowSet classRows = posDao.query4SqlRowSet(classSql.toString(), 
					//new Object[]{startTimestamp, endTimestamp, reportDate, organId, tenantId, optNum, posNum, organId});
					new Object[]{startTimestamp, endTimestamp, reportDate, organId, tenantId, posNum, organId});

			JSONArray classList = new JSONArray(); 
			while(classRows.next()){
				JSONObject classItem = new JSONObject();
				classItem.put("itemclass_name", classRows.getString("itemclass_name"));
				classItem.put("numbers", classRows.getInt("numbers"));
				classItem.put("real_amount", classRows.getDouble("real_amount"));
				
				classList.add(classItem);
			}
			obj.put("class_list", classList);
			
			list.add(obj);
			
			result.setData(list);
			result.setCode(Constant.CODE_SUCCESS);
			result.setMsg(Constant.GET_DETAIL_CHANGSHIFT_SUCCESS);
		}
		catch (Exception se)
		{
			result.setCode(Constant.CODE_INNER_EXCEPTION);
			result.setMsg(Constant.GET_DETAIL_CHANGSHIFT_FAILURE);
			logger.info("历史交班明细查询失败，原因：" + ExceptionMessage.getExceptionMessage(se));
			se.printStackTrace();
		}
	}
	
	private void getShiftDataForCashierReceiveForHis(String tenantId, int organId,Date reportDate,String optNum, String posNum,Timestamp startTimestamp,Timestamp endTimestamp,JSONObject obj,int scale) throws Exception
	{
		// 交款数据
		StringBuilder cashierReceive = new StringBuilder();
		cashierReceive.append(" select emp.name as opt_name,pcr.service_type,sd.class_item as service_type_name,");
		cashierReceive.append(" (case when pw.payment_class = 'cash' and d.id is not null then d.class_item else pw.payment_name1 end) as payment_name,");
		cashierReceive.append(" coalesce (pcr.amount, '0') as receive_amount,coalesce (pcr.amount, '0') as payment_amount ");
		cashierReceive.append(" from (  ");
		cashierReceive.append("       select cast('"+SysDictionary.SERVICE_TYPE_CONSUME+"'as text)as service_type,bi.cashier_num,pbp.jzid,sum(pbp.currency_amount) as amount from v_pos_bill_payment as pbp");
		cashierReceive.append("       left join v_pos_bill bi on pbp.tenancy_id=bi.tenancy_id and pbp.store_id=bi.store_id and pbp.bill_num=bi.bill_num");
		cashierReceive.append("       left join pos_opt_state_devices as osd on osd.devices_num = bi.pos_num");
		cashierReceive.append("       where bi.tenancy_id = ? and bi.store_id = ? and bi.report_date = ? and osd.pos_num = ? and bi.payment_time > ? and bi.payment_time < ? ");
		cashierReceive.append("       and exists ( select devices_code from hq_devices as hd where (show_type = 'YD' or show_type = 'PAD') and hd.devices_code = osd.devices_num ) ");
		cashierReceive.append("       group by bi.cashier_num,pbp.jzid ");
		cashierReceive.append("     union all ");
		cashierReceive.append("       select cast('"+SysDictionary.SERVICE_TYPE_RECHARGE+"'as text) as service_type,cast(cz.operator_id as text) as cashier_num,cz.payment_id, coalesce(sum(cz.main_trading),0) AS amount from (");
		cashierReceive.append("         select tl.tenancy_id,tl.store_id,tl.business_date,tl.shift_id,tl.operator_id,pl.third_bill_code,pl.bill_code,tl.main_trading,pl.pay_money,pl.payment_id from crm_card_payment_list pl");
		cashierReceive.append("         left join crm_card_trading_list tl on pl.tenancy_id=tl.tenancy_id and pl.third_bill_code=tl.third_bill_code and tl.operat_type in ('"+SysDictionary.OPERAT_TYPE_CZ+"','"+SysDictionary.OPERAT_TYPE_FCZ+"')");
		cashierReceive.append("         left join pos_opt_state ot on tl.tenancy_id=ot.tenancy_id and tl.store_id=ot.store_id and tl.business_date=ot.report_date and cast(tl.operator_id as text) =ot.opt_num and ot.content='"+SysDictionary.OPT_STATE_KSSY+"'");
		cashierReceive.append("         where ot.id is null and tl.tenancy_id =? and tl.store_id =? and tl.business_date = ? and tl.operate_time > ? and tl.operate_time < ?");
		cashierReceive.append("         group by tl.tenancy_id,tl.store_id,tl.business_date,tl.shift_id,tl.operator_id,pl.third_bill_code,pl.bill_code,tl.main_trading,pl.pay_money,pl.payment_id) cz");
		cashierReceive.append("       group by cz.operator_id,cz.payment_id ");
		cashierReceive.append("    ) pbp join(  ");
		cashierReceive.append("       select pcr.service_type,pcr.waiter_id,pcr.payment_id,sum (pcr.amount) as amount from pos_cashier_receive_log as pcr       ");
		//cashierReceive.append("       where pcr.tenancy_id = ? and pcr.store_id = ? and pcr.report_date = ? and cast (pcr.cashier_id as varchar) = ? and pcr.last_updatetime > ? and pcr.last_updatetime < ? ");
		cashierReceive.append("       where pcr.tenancy_id = ? and pcr.store_id = ? and pcr.report_date = ?  and pcr.last_updatetime > ? and pcr.last_updatetime < ? ");
		cashierReceive.append("       group by pcr.waiter_id,pcr.payment_id,pcr.service_type ");
		cashierReceive.append("    ) as pcr on cast (pbp.cashier_num as varchar) = cast (pcr.waiter_id as varchar) and pcr.payment_id = pbp.jzid and pcr.service_type = pbp.service_type");
		cashierReceive.append(" left join employee emp on pbp.cashier_num = cast (emp.id as varchar) ");
		cashierReceive.append(" left join payment_way pw on pbp.jzid = pw.id ");
		cashierReceive.append(" left join sys_dictionary d on d.class_item_code = pw.payment_name1 and d.class_identifier_code='currency' and pw.payment_class = 'cash'");
		cashierReceive.append(" left join sys_dictionary sd on sd.class_item_code = pcr.service_type and sd.class_identifier_code='service_type' ");
		cashierReceive.append(" group by emp.name,payment_name,pcr.amount,pbp.amount,pcr.service_type,sd.class_item order by pcr.service_type,emp.name");

		JSONArray arrCashierReceive = new JSONArray();
		SqlRowSet rsCashierReceive = posDao.query4SqlRowSet(cashierReceive.toString(), new Object[]
		//{ tenantId, organId, reportDate, posNum, startTimestamp, endTimestamp, tenantId, organId, reportDate, startTimestamp, endTimestamp, tenantId, organId, reportDate, optNum, startTimestamp, endTimestamp });
		{ tenantId, organId, reportDate, posNum, startTimestamp, endTimestamp, tenantId, organId, reportDate, startTimestamp, endTimestamp, tenantId, organId, reportDate, startTimestamp, endTimestamp });

		while (rsCashierReceive.next())
		{
			JSONObject json = new JSONObject();
			json.put("service_type", rsCashierReceive.getString("service_type"));
			json.put("service_type_name", rsCashierReceive.getString("service_type_name"));
			json.put("opt_name", rsCashierReceive.getString("opt_name"));
			json.put("payment_name", rsCashierReceive.getString("payment_name"));
			json.put("payment_amount", rsCashierReceive.getDouble("payment_amount"));
			json.put("receive_amount", rsCashierReceive.getDouble("receive_amount"));
			arrCashierReceive.add(json);
		}
		obj.put("receive_item", arrCashierReceive);

		// 交款
		StringBuilder cashierReceiveSum = new StringBuilder("select pw.payment_class,(case when pw.payment_class = 'cash' and d.id is not null then d.class_item else pw.payment_name1 end) as payment_name,coalesce(sum(pcrl.amount),'0') as amount");
		cashierReceiveSum.append(" from pos_cashier_receive_log pcrl left join payment_way pw on pcrl.payment_id = pw.id left join sys_dictionary d on d.class_item_code = pw.payment_name1");
		//cashierReceiveSum.append(" where pcrl.tenancy_id = ? and pcrl.store_id = ? and pcrl.last_updatetime > ? and pcrl.last_updatetime < ? and cast(pcrl.cashier_id as varchar) = ? and pcrl.pos_num = ? group by pw.payment_class,payment_name");
		cashierReceiveSum.append(" where pcrl.tenancy_id = ? and pcrl.store_id = ? and pcrl.last_updatetime > ? and pcrl.last_updatetime < ? and pcrl.pos_num = ? group by pw.payment_class,payment_name");
		List<JSONObject> receiveSumList = posDao.query4Json(tenantId, cashierReceiveSum.toString(), new Object[]
		//{ tenantId, organId, startTimestamp, endTimestamp, optNum, posNum });
		{ tenantId, organId, startTimestamp, endTimestamp, posNum });
		obj.put("receive_sum_list", receiveSumList);
	}
}
