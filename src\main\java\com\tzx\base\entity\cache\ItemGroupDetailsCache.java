package com.tzx.base.entity.cache;

public class ItemGroupDetailsCache
{
	public static String getSql()
	{
		String sql = new String("select distinct higd.tenancy_id,higd.id as item_group_id,higd.item_id,higd.item_unit_id,higd.makeup_money,hig.id as group_id,   hig.item_group_name,hig.item_group_price from hq_item_group_details higd left join hq_item_group hig on higd.tenancy_id=hig.tenancy_id and higd.item_group_id=hig.id where hig.valid_state='1' and hig.tenancy_id=::tenancy_id");
		return sql;
	}
	
	public static String getKey()
	{
		return "group_id,item_id,item_unit_id";
	}
}
