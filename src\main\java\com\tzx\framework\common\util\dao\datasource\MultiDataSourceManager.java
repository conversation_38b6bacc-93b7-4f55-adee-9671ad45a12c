package com.tzx.framework.common.util.dao.datasource;

import net.sf.json.JSONObject;

import java.lang.reflect.Field;
import java.sql.*;
import java.util.ArrayList;
import java.util.List;


/**
 * 多数据源管理器
 * 
 * <AUTHOR>
 * 
 */
public class MultiDataSourceManager
{
	/*private static Map<String, BasicDataSource>	h2DataSourceMap			= new ConcurrentHashMap<String, BasicDataSource>();
	private static Map<String, BasicDataSource>	postgresDataSourceMap	= new ConcurrentHashMap<String, BasicDataSource>();
	private static Map<String, BasicDataSource>	sqlserverDataSourceMap	= new ConcurrentHashMap<String, BasicDataSource>();

	*//**
	 * 新增数据源
	 * 
	 * @param tenantId
	 * @return
	 * @throws Exception
	 *//*
	public static void addDataSource(String tenantId) throws Exception
	{
		Connection conn = null;
		Statement stm = null;
		ResultSet rs = null;

		try
		{
			DataSource datasource = (DataSource) SpringConext.getApplicationContext().getBean("dataSource");
				
			conn = datasource.getConnection();

			stm = conn.createStatement();

			//String sql = "SELECT D.* FROM MULTI_DATASOURCE D WHERE D.OBJUID IN (SELECT T.MULTI_DATASOURCE_UID FROM MULTI_TENANCY T WHERE T.ID='" + tenantId + "') OR D.OBJUID IN (SELECT T.MODEL_DATASOURCE_UID FROM MULTI_TENANCY T WHERE T.ID='" + tenantId + "') OR D.OBJUID IN (SELECT T.POS_DATASOURCE_UID FROM MULTI_TENANCY T WHERE T.ID='" + tenantId + "')";
            String sql = "select d.* from multi_datasource d, multi_tenancy t where t.l10n='"+tenantId+"' and d.objuid=t.multi_datasource_uid;";
			//System.out.println(sql);
			rs = stm.executeQuery(sql);

			Properties multiProperties = null;
			BasicDataSource dataSource = null;

			while (rs.next())
			{
	    		multiProperties = new Properties();
				multiProperties.put("driverClassName", rs.getString("driver_class"));
				multiProperties.put("url", rs.getString("driver_url"));
				multiProperties.put("username", rs.getString("username"));
				multiProperties.put("password", rs.getString("password") == null ? "" : rs.getString("password"));
                
				 if ("postgresql".equals(rs.getString("DIALECT")))
					{
						dataSource = (BasicDataSource) BasicDataSourceFactory.createDataSource(multiProperties);

						postgresDataSourceMap.put(tenantId, dataSource);
					}
				else if ("h2".equals(rs.getString("DIALECT")))
				{
					dataSource = (BasicDataSource) BasicDataSourceFactory.createDataSource(multiProperties);

					h2DataSourceMap.put(tenantId, dataSource);
				}
				else if ("sqlserver".equals(rs.getString("DIALECT")))
				{
					dataSource = (BasicDataSource) BasicDataSourceFactory.createDataSource(multiProperties);

					sqlserverDataSourceMap.put(tenantId, dataSource);
				}
			}
		}
		catch (Exception e)
		{
			throw e;
		}
		finally
		{
			close(conn, stm, rs);
		}
	}

	*//**
	 * 根据MultiDatasourceContextHelper获取线程标识，默认获取postgres的数据源
	 * 
	 * @param tenantId
	 * @return
	 * @throws Exception
	 *//*
	public static BasicDataSource getDataSource(String tenantId) throws Exception
	{
		if (tenantId == null || tenantId.isEmpty())
		{
			tenantId = "0";
		}

		*//*if(MultiDatasourceContext.POSTGRES_DATASOURCE.equals(MultiDatasourceContextHelper.getDatasource())){
			
			return getPostgresDataSource(tenantId);
			
		}else if (MultiDatasourceContext.SQLSERVER_DATASOURCE.equals(MultiDatasourceContextHelper.getDatasource()))
		{
			return getSqlserverDataSource(tenantId);
		}
		else  //if(MultiDatasourceContext.H2_DATASOURCE.equals(MultiDatasourceContextHelper.getDatasource()))
		{
			 return getH2DataSource(tenantId);
		}*//*
		
        if(MultiDatasourceContext.POSTGRES_DATASOURCE.equals(Constant.getSystemMap().get("datasourcetype"))){
	
			return getPostgresDataSource(tenantId);
			
		}
        if (MultiDatasourceContext.SQLSERVER_DATASOURCE.equals(Constant.getSystemMap().get("datasourcetype")))
		{
			return getSqlserverDataSource(tenantId);
		} 
        if(MultiDatasourceContext.H2_DATASOURCE.equals(MultiDatasourceContextHelper.getDatasource()))
		{
			 return getH2DataSource(tenantId);
		}
        
        return null;
		
	}

	*//**
	 * 获取业务库连接
	 * 
	 * @param tenantId
	 * @return
	 * @throws Exception
	 *//*
	public static BasicDataSource getPostgresDataSource(String tenantId) throws Exception
	{
		BasicDataSource dataSource = postgresDataSourceMap.get(tenantId);

		if (dataSource == null || dataSource.isClosed())
		{
			addDataSource(tenantId);
		}

		if (postgresDataSourceMap.containsKey(tenantId))
		{
			return postgresDataSourceMap.get(tenantId);
		}
		else
		{
			throw new Exception("no data source");
		}
	}

	*//**
	 * 获取业务库连接
	 * 
	 * @param tenantId
	 * @return
	 * @throws Exception
	 *//*
	public static BasicDataSource getSqlserverDataSource(String tenantId) throws Exception
	{
		BasicDataSource dataSource = sqlserverDataSourceMap.get(tenantId);

		if (dataSource == null || dataSource.isClosed())
		{
			addDataSource(tenantId);
		}

		if (sqlserverDataSourceMap.containsKey(tenantId))
		{
			return sqlserverDataSourceMap.get(tenantId);
		}
		else
		{
			throw new Exception("no data source");
		}
	}

	*//**
	 * 获取配置库连接
	 * 
	 * @param tenantId
	 * @return
	 * @throws Exception
	 *//*
	public static BasicDataSource getH2DataSource(String tenantId) throws Exception
	{
		BasicDataSource dataSource = h2DataSourceMap.get(tenantId);

		if (dataSource == null || dataSource.isClosed())
		{
			addDataSource(tenantId);
		}

		if (h2DataSourceMap.containsKey(tenantId))
		{
			return h2DataSourceMap.get(tenantId);
		}
		else
		{
			throw new Exception("no data source");
		}
	}*/

	/**
	 * 关闭数据源
	 * 
	 * @param conn
	 * @param stm
	 * @param rs
	 */
	public static void close(Connection conn, Statement stm, ResultSet rs)
	{
		if (rs != null)
		{
			try
			{
				rs.close();
			}
			catch (SQLException e)
			{
				e.printStackTrace();
			}
		}
		if (stm != null)
		{
			try
			{
				stm.close();
			}
			catch (SQLException e)
			{
				e.printStackTrace();
			}
		}
		if (conn != null)
		{
			try
			{
				conn.close();
			}
			catch (SQLException e)
			{
				e.printStackTrace();
			}
		}
	}
	 public static Connection buildOracleConnection(String dbDriver,String dbUrl, String dbUsername, String dbPassword) throws Exception {
		    Class.forName(dbDriver);
		    return DriverManager.getConnection(dbUrl, dbUsername, dbPassword);
		  }
	 public static PreparedStatement getPstmt(Connection conn, String sql) throws Exception {
		    return conn.prepareStatement(sql);
		  }
	 public static <T> List<T> parseResultSet(ResultSet rs, Class<T> clz) throws Exception {
		    List objList = new ArrayList();
		    Field[] fields = clz.getDeclaredFields();
		    List colNames = new ArrayList();
		    ResultSetMetaData rsmd = rs.getMetaData();
		    for (int i = 1; i <= rsmd.getColumnCount(); i++) {
		      colNames.add(rsmd.getColumnLabel(i).toLowerCase());
		    }
		    while (rs.next()) {
		      Object obj = clz.newInstance();
		      JSONObject jsonobject =(JSONObject) obj;
		      for (int i=0; i<colNames.size();i++) {
//		    	  if(colNames.get(i).toString().equalsIgnoreCase("ORG_LEVEL")){
//		    		  jsonobject.put("level", rs.getString(colNames.get(i).toString()));
//		    	  }else{
//		    		  
//		    	  }
		    	  jsonobject.put(colNames.get(i).toString().toLowerCase(), rs.getString(colNames.get(i).toString()));
//		    	  jsonobject.put(colNames.get(i).toString().toLowerCase(), rs.getObject(colNames.get(i).toString()));
		      }
		      objList.add(jsonobject);
		    }
		    return objList;
		  }
}
