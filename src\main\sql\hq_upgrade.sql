--- 重要说明：
--- 1.本脚本是HQ升级脚本，切勿在门店本地数据库执行
--- 2.本脚本从版本1.13.0_1219开始维护，且仅包含与门店pos业务相关的升级脚本


---------------------------------------------------hq_upgrade.sql-------------------------------------------------------------

--添加折扣操作人,以及折扣原因字段star-
--肖恒
--2017-11-30
--注意:总部需要执行
DO $DO$
DECLARE num INT;
BEGIN
   select count(*) into num from information_schema.columns where table_name = 'pos_bill' and column_name='discount_reason_id';
   IF num = 0 THEN
      EXECUTE ' ALTER TABLE pos_bill ADD COLUMN discount_reason_id int4;';
   ELSE RAISE NOTICE 'column  already exists in this table';
   END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
   select count(*) into num from information_schema.columns where table_name = 'pos_bill2' and column_name='discount_reason_id';
   IF num = 0 THEN
      EXECUTE ' ALTER TABLE pos_bill2 ADD COLUMN discount_reason_id int4;';
   ELSE RAISE NOTICE 'column  already exists in this table';
   END IF;
END$DO$;


DO $DO$
DECLARE num INT;
BEGIN
   select count(*) into num from information_schema.columns where table_name = 'pos_bill_regain' and column_name='discount_reason_id';
   IF num = 0 THEN
      EXECUTE ' ALTER TABLE pos_bill_regain ADD COLUMN discount_reason_id int4;';
   ELSE RAISE NOTICE 'column  already exists in this table';
   END IF;
END$DO$;


DO $DO$
DECLARE num INT;
BEGIN
   select count(*) into num from information_schema.columns where table_name = 'pos_bill_item' and column_name='discount_num';
   IF num = 0 THEN
      EXECUTE ' ALTER TABLE pos_bill_item ADD COLUMN discount_num character varying(32);';
   ELSE RAISE NOTICE 'column  already exists in this table';
   END IF;
END$DO$;


DO $DO$
DECLARE num INT;
BEGIN
   select count(*) into num from information_schema.columns where table_name = 'pos_bill_item2' and column_name='discount_num';
   IF num = 0 THEN
      EXECUTE ' ALTER TABLE pos_bill_item2 ADD COLUMN discount_num character varying(32);';
   ELSE RAISE NOTICE 'column  already exists in this table';
   END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
   select count(*) into num from information_schema.columns where table_name = 'pos_bill_item_regain' and column_name='discount_num';
   IF num = 0 THEN
      EXECUTE ' ALTER TABLE pos_bill_item_regain ADD COLUMN discount_num character varying(32);';
   ELSE RAISE NOTICE 'column  already exists in this table';
   END IF;
END$DO$;
--添加折扣操作人,以及折扣原因字段end-


--E待客是否勾选打印结账单（系统参数）start--
DO $DO$
DECLARE num INT;
BEGIN
    select count(*) into num from sys_parameter p where para_code = 'e_daike_billprint_check_state' and p.tenancy_id = tenancy_id() and p.store_id = 0;
IF num = 0 THEN
    EXECUTE ' INSERT INTO sys_parameter (tenancy_id,  store_id, system_name, model_name, para_name, para_code, para_value, para_defaut, para_type, valid_state, values_name, para_remark)
VALUES (tenancy_id(), 0, ''POS'', ''系统参数'', ''E待客是否勾选打印结账单'', ''e_daike_billprint_check_state'', ''0'', ''0'', ''常规'', ''1'', ''0代表勾选，1代表不勾选'', '''');';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;
--E待客是否勾选打印结账单（系统参数）end--

--芒果点菜器是否自动开台(系统参数) start --
DO $DO$
DECLARE num INT;
BEGIN
    select count(*) into num from sys_parameter p where para_code = 'mobileIsOpenTable' and p.tenancy_id = tenancy_id() and p.store_id = 0;
IF num = 0 THEN
    EXECUTE ' INSERT INTO sys_parameter (tenancy_id,  store_id, system_name, model_name, para_name, para_code, para_value, para_defaut, para_type, valid_state, values_name, para_remark)
VALUES (tenancy_id(), 0, ''POS'', ''系统参数'', ''芒果点菜器是否自动开台'', ''mobileIsOpenTable'', ''1'', ''1'', ''常规'', ''1'', ''0：不开台，1：开台'', '''');';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;
--芒果点菜器是否自动开台(系统参数) end --

--pos_bill_item添加count_rate字段 start---
DO $DO$
DECLARE num INT;
BEGIN
select count(1) into num from information_schema.columns where table_name = 'pos_bill_item' and column_name='count_rate';
IF num = 0 THEN
EXECUTE 'ALTER TABLE pos_bill_item ADD COLUMN count_rate numeric(19,4) default 1;';
ELSE RAISE NOTICE 'column already exists in this table';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
select count(1) into num from information_schema.columns where table_name = 'pos_bill_item2' and column_name='count_rate';
IF num = 0 THEN
EXECUTE 'ALTER TABLE pos_bill_item2 ADD COLUMN count_rate numeric(19,4) default 1;';
ELSE RAISE NOTICE 'column already exists in this table';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
select count(1) into num from information_schema.columns where table_name = 'pos_bill_item_regain' and column_name='count_rate';
IF num = 0 THEN
EXECUTE 'ALTER TABLE pos_bill_item_regain ADD COLUMN count_rate numeric(19,4) default 1;';
ELSE RAISE NOTICE 'column already exists in this table';
END IF;
END$DO$;
--pos_bill_item添加count_rate字段 end---

--- 添加单品折扣率字段 start---
--- 2017-12-12 ---
--- 肖恒 ---
DO $DO$
DECLARE num INT;
BEGIN
   select count(*) into num from information_schema.columns where table_name = 'pos_bill_item' and column_name='single_discount_rate';
   IF num = 0 THEN
      EXECUTE ' ALTER TABLE pos_bill_item ADD COLUMN single_discount_rate numeric(19,4);';
   ELSE RAISE NOTICE 'column  already exists in this table';
   END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
   select count(*) into num from information_schema.columns where table_name = 'pos_bill_item2' and column_name='single_discount_rate';
   IF num = 0 THEN
      EXECUTE ' ALTER TABLE pos_bill_item2 ADD COLUMN single_discount_rate numeric(19,4);';
   ELSE RAISE NOTICE 'column  already exists in this table';
   END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
   select count(*) into num from information_schema.columns where table_name = 'pos_bill_item_regain' and column_name='single_discount_rate';
   IF num = 0 THEN
      EXECUTE ' ALTER TABLE pos_bill_item_regain ADD COLUMN single_discount_rate numeric(19,4);';
   ELSE RAISE NOTICE 'column  already exists in this table';
   END IF;
END$DO$;
---添加单品折扣率字段end---

---修改快餐单品取消按钮名称为退单--
update sys_modules set module_name='退单' where id='800213' and module_name <> '退单';

--- 交班添加uid关联字段 start---
--- 2017-12-15 ---
--- 肖恒 ---
DO $DO$
DECLARE num INT;
BEGIN
   select count(*) into num from information_schema.columns where table_name = 'pos_opter_changshift_main' and column_name='changshift_uid';
   IF num = 0 THEN
      EXECUTE ' ALTER TABLE pos_opter_changshift_main ADD COLUMN changshift_uid CHARACTER VARYING(32);';
   ELSE RAISE NOTICE 'column  already exists in this table';
   END IF;
END$DO$;

ALTER TABLE pos_opter_changshift ALTER COLUMN changshift_id TYPE CHARACTER VARYING(32);
ALTER TABLE pos_opter_changshift_customer ALTER COLUMN changshift_id TYPE CHARACTER VARYING(32);
ALTER TABLE pos_opter_paidrefund ALTER COLUMN changshift_id TYPE CHARACTER VARYING(32);
--- 交班添加uid关联字段 end---

--------------------------------------------------20171219发版--------------------------------------------------------
--- 账单会员操作表(pos_bill_member)添加会员储值余额字段   ---start---
--- 2017-12-25 ---
--- 肖恒 ---
--- pos_bill_member ---
DO $DO$
DECLARE num INT;
BEGIN
   select count(*) into num from information_schema.columns where table_name = 'pos_bill_member' and column_name='consume_before_main_balance';
   IF num = 0 THEN
      EXECUTE ' ALTER TABLE pos_bill_member ADD COLUMN consume_before_main_balance numeric(19,4);';
   ELSE RAISE NOTICE 'column  already exists in this table';
   END IF;
END$DO$;


DO $DO$
DECLARE num INT;
BEGIN
   select count(*) into num from information_schema.columns where table_name = 'pos_bill_member' and column_name='consume_before_reward_balance';
   IF num = 0 THEN
      EXECUTE ' ALTER TABLE pos_bill_member ADD COLUMN consume_before_reward_balance numeric(19,4);';
   ELSE RAISE NOTICE 'column  already exists in this table';
   END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
   select count(*) into num from information_schema.columns where table_name = 'pos_bill_member' and column_name='consume_after_main_balance';
   IF num = 0 THEN
      EXECUTE ' ALTER TABLE pos_bill_member ADD COLUMN consume_after_main_balance numeric(19,4);';
   ELSE RAISE NOTICE 'column  already exists in this table';
   END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
   select count(*) into num from information_schema.columns where table_name = 'pos_bill_member' and column_name='consume_after_reward_balance';
   IF num = 0 THEN
      EXECUTE ' ALTER TABLE pos_bill_member ADD COLUMN consume_after_reward_balance numeric(19,4);';
   ELSE RAISE NOTICE 'column  already exists in this table';
   END IF;
END$DO$;

--- pos_bill_member_regain---
DO $DO$
DECLARE num INT;
BEGIN
   select count(*) into num from information_schema.columns where table_name = 'pos_bill_member_regain' and column_name='consume_before_main_balance';
   IF num = 0 THEN
      EXECUTE ' ALTER TABLE pos_bill_member_regain ADD COLUMN consume_before_main_balance numeric(19,4);';
   ELSE RAISE NOTICE 'column  already exists in this table';
   END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
   select count(*) into num from information_schema.columns where table_name = 'pos_bill_member_regain' and column_name='consume_before_reward_balance';
   IF num = 0 THEN
      EXECUTE ' ALTER TABLE pos_bill_member_regain ADD COLUMN consume_before_reward_balance numeric(19,4);';
   ELSE RAISE NOTICE 'column  already exists in this table';
   END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
   select count(*) into num from information_schema.columns where table_name = 'pos_bill_member_regain' and column_name='consume_after_main_balance';
   IF num = 0 THEN
      EXECUTE ' ALTER TABLE pos_bill_member_regain ADD COLUMN consume_after_main_balance numeric(19,4);';
   ELSE RAISE NOTICE 'column  already exists in this table';
   END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
   select count(*) into num from information_schema.columns where table_name = 'pos_bill_member_regain' and column_name='consume_after_reward_balance';
   IF num = 0 THEN
      EXECUTE ' ALTER TABLE pos_bill_member_regain ADD COLUMN consume_after_reward_balance numeric(19,4);';
   ELSE RAISE NOTICE 'column  already exists in this table';
   END IF;
END$DO$;
--- 账单会员操作表(pos_bill_member)添加会员储值余额字段   ---end---

---订单相关表添加unit_name和item_name start---
DO $DO$
DECLARE num INT;
BEGIN
select count(1) into num from information_schema.columns where table_name = 'cc_order_item' and column_name='unit_name';
IF num = 0 THEN
EXECUTE 'ALTER TABLE cc_order_item ADD COLUMN unit_name varchar(100);';
ELSE RAISE NOTICE 'column already exists in this table';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
select count(1) into num from information_schema.columns where table_name = 'cc_order_item_retain' and column_name='unit_name';
IF num = 0 THEN
EXECUTE 'ALTER TABLE cc_order_item_retain ADD COLUMN unit_name varchar(100);';
ELSE RAISE NOTICE 'column already exists in this table';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
select count(1) into num from information_schema.columns where table_name = 'cc_order_item_details' and column_name='unit_name';
IF num = 0 THEN
EXECUTE 'ALTER TABLE cc_order_item_details ADD COLUMN unit_name varchar(100);';
ELSE RAISE NOTICE 'column already exists in this table';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
select count(1) into num from information_schema.columns where table_name = 'cc_order_item_details_retain' and column_name='unit_name';
IF num = 0 THEN
EXECUTE 'ALTER TABLE cc_order_item_details_retain ADD COLUMN unit_name varchar(100);';
ELSE RAISE NOTICE 'column already exists in this table';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
select count(1) into num from information_schema.columns where table_name = 'cc_order_item_details' and column_name='item_name';
IF num = 0 THEN
EXECUTE 'ALTER TABLE cc_order_item_details ADD COLUMN item_name varchar(100);';
ELSE RAISE NOTICE 'column already exists in this table';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
select count(1) into num from information_schema.columns where table_name = 'cc_order_item_details_retain' and column_name='item_name';
IF num = 0 THEN
EXECUTE 'ALTER TABLE cc_order_item_details_retain ADD COLUMN item_name varchar(100);';
ELSE RAISE NOTICE 'column already exists in this table';
END IF;
END$DO$;
---订单相关表添加unit_name和item_name end---

--- 外卖随餐单（异常单据）基础数据【新打印】
DELETE FROM sys_model_function WHERE tenancy_id = tenancy_id() AND format_type = '1203';
INSERT INTO sys_model_function (tenancy_id, id, function_id, format_type, format_name, type) VALUES (tenancy_id(), (select max(id)+1 from sys_model_function), 'TAKEAWAY_ORDER', '1203', '外卖随餐单（异常单据）', '1');
INSERT INTO sys_model_function (tenancy_id, id, function_id, format_type, format_name, type) VALUES (tenancy_id(), (select max(id)+1 from sys_model_function), 'TAKEAWAY_ORDER', '1203', '外卖随餐单（异常单据）', '2');
--- 外卖取消订单（异常单据）基础数据【新打印】
DELETE FROM sys_model_function WHERE tenancy_id = tenancy_id() AND format_type = '1204';
INSERT INTO sys_model_function (tenancy_id, id, function_id, format_type, format_name, type) VALUES (tenancy_id(), (select max(id)+1 from sys_model_function), 'TAKEAWAY_ORDER_CANCEL', '1204', '外卖取消订单（异常单据）', '1');
INSERT INTO sys_model_function (tenancy_id, id, function_id, format_type, format_name, type) VALUES (tenancy_id(), (select max(id)+1 from sys_model_function), 'TAKEAWAY_ORDER_CANCEL', '1204', '外卖取消订单（异常单据）', '2');


-----------------------------------------------------20171228发版------------------------------------------------

--- 开台添加就餐类型start---
INSERT INTO sys_dictionary ( "id", "tenancy_id", "model_name", "application_model", "class_identifier", "class_identifier_code", "class_item", "class_item_code", "is_sys", "remark", "valid_state", "last_operator", "last_updatetime") SELECT (select max(id)+1 from sys_dictionary), tenancy_id (), 'pos', '开台参数', '就餐类型', 'dinner_type', '零点', 'LD01', 'N', NULL, '1', 'SYSTEM', now() WHERE NOT EXISTS ( SELECT 1 FROM sys_dictionary WHERE class_identifier_code = 'dinner_type' AND class_item_code = 'LD01');
INSERT INTO sys_dictionary ( "id", "tenancy_id", "model_name", "application_model", "class_identifier", "class_identifier_code", "class_item", "class_item_code", "is_sys", "remark", "valid_state", "last_operator", "last_updatetime") SELECT (select max(id)+1 from sys_dictionary), tenancy_id (), 'pos', '开台参数', '就餐类型', 'dinner_type', '宴会', 'YH02', 'N', NULL, '1', 'SYSTEM', now() WHERE NOT EXISTS ( SELECT 1 FROM sys_dictionary WHERE class_identifier_code = 'dinner_type' AND class_item_code = 'YH02');
INSERT INTO sys_dictionary ( "id", "tenancy_id", "model_name", "application_model", "class_identifier", "class_identifier_code", "class_item", "class_item_code", "is_sys", "remark", "valid_state", "last_operator", "last_updatetime") SELECT (select max(id)+1 from sys_dictionary), tenancy_id (), 'pos', '开台参数', '就餐类型', 'dinner_type', '招待餐', 'ZD03', 'N', NULL, '1', 'SYSTEM', now() WHERE NOT EXISTS ( SELECT 1 FROM sys_dictionary WHERE class_identifier_code = 'dinner_type' AND class_item_code = 'ZD03');
--- 【开台是否选择就餐类型】参数
DO $DO$
DECLARE num INT;
BEGIN
    select count(*) into num from sys_parameter p where para_code = 'openTableSetDinnerType' and p.tenancy_id = tenancy_id() and p.store_id = 0;
IF num = 0 THEN
    EXECUTE ' INSERT INTO sys_parameter (tenancy_id,  store_id, system_name, model_name, para_name, para_code, para_value, para_defaut, para_type, valid_state, values_name, para_remark)
VALUES (tenancy_id(), 0, ''POS'', ''系统参数'', ''开台是否选择就餐类型'', ''openTableSetDinnerType'', ''0'', ''0'', ''常规'', ''1'', ''0:不选择 1：选择'', '''');';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;
--- pos_bill添加就餐类型字段
select p_addfield('pos_bill','dinner_type','varchar(20)');
select p_addfield('pos_bill2','dinner_type','varchar(20)');
--- 开台添加就餐类型end---

--- 数据上传修改start---
--- 肖恒---
--- 2018-01-11---
/*
DELETE FROM sys_parameter WHERE para_code='upload_data_way';
INSERT INTO sys_parameter (tenancy_id,  store_id, system_name, model_name, para_name, para_code, para_value, para_defaut, para_type, valid_state, values_name, para_remark)
VALUES (tenancy_id(), 0, 'POS', '系统参数', '数据上传方式', 'upload_data_way', 'HTTP', 'MQ', '常规', '1', 'MQ:MQ方式;HTTP：HTTP方式;', '');
*/

DO $DO$
DECLARE num INT;
BEGIN
    select count(*) into num from sys_parameter p where para_code = 'upload_data_way' and p.tenancy_id = tenancy_id() and p.store_id = 0;
IF num = 0 THEN
    EXECUTE ' INSERT INTO sys_parameter (tenancy_id,  store_id, system_name, model_name, para_name, para_code, para_value, para_defaut, para_type, valid_state, values_name, para_remark)
VALUES (tenancy_id(), 0, ''POS'', ''系统参数'', ''数据上传方式'', ''upload_data_way'', ''HTTP'', ''MQ'', ''常规'', ''1'', ''MQ:MQ方式;HTTP：HTTP方式;'', '''');';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DELETE FROM sys_parameter_detail WHERE para_code='upload_data_way';

INSERT INTO sys_parameter_detail (tenancy_id,id, para_code, para_value, para_value_code)
select tenancy_id(), max(id)+1, 'upload_data_way', 'HTTP方式', 'HTTP' from sys_parameter_detail;

INSERT INTO sys_parameter_detail (tenancy_id, id,para_code, para_value, para_value_code)
select tenancy_id(), max(id)+1 ,'upload_data_way', 'MQ方式', 'MQ' from sys_parameter_detail;
--- 数据上传修改end---

---pos_kvs_bill添加need_call字段
DO $DO$
DECLARE num INT;
BEGIN
select count(1) into num from information_schema.columns where table_name = 'pos_kvs_bill' and column_name='need_call';
IF num = 0 THEN
EXECUTE 'ALTER TABLE pos_kvs_bill ADD COLUMN need_call varchar(32);';
ELSE RAISE NOTICE 'column already exists in this table';
END IF;
END$DO$;

---pos_bill_item表添加字段origin_item_price:菜品原单价	start---
---肖恒
---2018-01-15
DO $DO$
DECLARE num INT;
BEGIN
select count(1) into num from information_schema.columns where table_name = 'pos_bill_item' and column_name='origin_item_price';
IF num = 0 THEN
EXECUTE 'ALTER TABLE pos_bill_item ADD COLUMN origin_item_price numeric(19,4);';
ELSE RAISE NOTICE 'column already exists in this table';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
select count(1) into num from information_schema.columns where table_name = 'pos_bill_item2' and column_name='origin_item_price';
IF num = 0 THEN
EXECUTE 'ALTER TABLE pos_bill_item2 ADD COLUMN origin_item_price numeric(19,4);';
ELSE RAISE NOTICE 'column already exists in this table';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
select count(1) into num from information_schema.columns where table_name = 'pos_bill_item_regain' and column_name='origin_item_price';
IF num = 0 THEN
EXECUTE 'ALTER TABLE pos_bill_item_regain ADD COLUMN origin_item_price numeric(19,4);';
ELSE RAISE NOTICE 'column already exists in this table';
END IF;
END$DO$;
---pos_bill_item表添加字段origin_item_price:菜品原单价	end---

-----------------------------------------------------20180116发版 (2018.1.23已升级)------------------------------------------------

-------------- pos_bill pos_bill2 pos_bill_payment  pos_bill_payment2  pos_bill_payment_coupons 新增第三方券对账字段start------------
DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill' and column_name='coupon_buy_price';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill ADD COLUMN coupon_buy_price numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill' and column_name='due';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill ADD COLUMN due numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill' and column_name='tenancy_assume';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill ADD COLUMN tenancy_assume numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill' and column_name='third_assume';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill ADD COLUMN third_assume numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill' and column_name='third_fee';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill ADD COLUMN third_fee numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment' and column_name='coupon_buy_price';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_payment ADD COLUMN coupon_buy_price numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment' and column_name='due';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_payment ADD COLUMN due numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment' and column_name='tenancy_assume';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_payment ADD COLUMN tenancy_assume numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment' and column_name='third_assume';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_payment ADD COLUMN third_assume numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment' and column_name='third_fee';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_payment ADD COLUMN third_fee numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment_coupons' and column_name='coupon_buy_price';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_payment_coupons ADD COLUMN coupon_buy_price numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment_coupons' and column_name='due';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_payment_coupons ADD COLUMN due numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment_coupons' and column_name='tenancy_assume';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_payment_coupons ADD COLUMN tenancy_assume numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment_coupons' and column_name='third_assume';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_payment_coupons ADD COLUMN third_assume numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment_coupons' and column_name='third_fee';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_payment_coupons ADD COLUMN third_fee numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment_coupons' and column_name='request_state';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_payment_coupons ADD COLUMN request_state int;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill2' and column_name='coupon_buy_price';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill2 ADD COLUMN coupon_buy_price numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill2' and column_name='due';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill2 ADD COLUMN due numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill2' and column_name='tenancy_assume';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill2 ADD COLUMN tenancy_assume numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill2' and column_name='third_assume';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill2 ADD COLUMN third_assume numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill2' and column_name='third_fee';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill2 ADD COLUMN third_fee numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment2' and column_name='coupon_buy_price';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_payment2 ADD COLUMN coupon_buy_price numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment2' and column_name='due';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_payment2 ADD COLUMN due numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment2' and column_name='tenancy_assume';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_payment2 ADD COLUMN tenancy_assume numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment2' and column_name='third_assume';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_payment2 ADD COLUMN third_assume numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment2' and column_name='third_fee';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_payment2 ADD COLUMN third_fee numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

COMMENT ON COLUMN public.pos_bill.coupon_buy_price IS '用户实付(券购买价)';
COMMENT ON COLUMN public.pos_bill.due IS '券账单净收(结算给商家的钱)';
COMMENT ON COLUMN public.pos_bill.tenancy_assume IS '商家优惠承担';
COMMENT ON COLUMN public.pos_bill.third_assume IS '第三方优惠承担';
COMMENT ON COLUMN public.pos_bill.third_fee IS '第三方票券服务费';

COMMENT ON COLUMN public.pos_bill2.coupon_buy_price IS '用户实付(券购买价)';
COMMENT ON COLUMN public.pos_bill2.due IS '券账单净收(结算给商家的钱)';
COMMENT ON COLUMN public.pos_bill2.tenancy_assume IS '商家优惠承担';
COMMENT ON COLUMN public.pos_bill2.third_assume IS '第三方优惠承担';
COMMENT ON COLUMN public.pos_bill2.third_fee IS '第三方票券服务费';

COMMENT ON COLUMN public.pos_bill_payment.coupon_buy_price IS '用户实付(券购买价)';
COMMENT ON COLUMN public.pos_bill_payment.due IS '券账单净收(结算给商家的钱)';
COMMENT ON COLUMN public.pos_bill_payment.tenancy_assume IS '商家优惠承担';
COMMENT ON COLUMN public.pos_bill_payment.third_assume IS '第三方优惠承担';
COMMENT ON COLUMN public.pos_bill_payment.third_fee IS '第三方票券服务费';

COMMENT ON COLUMN public.pos_bill_payment2.coupon_buy_price IS '用户实付(券购买价)';
COMMENT ON COLUMN public.pos_bill_payment2.due IS '券账单净收(结算给商家的钱)';
COMMENT ON COLUMN public.pos_bill_payment2.tenancy_assume IS '商家优惠承担';
COMMENT ON COLUMN public.pos_bill_payment2.third_assume IS '第三方优惠承担';
COMMENT ON COLUMN public.pos_bill_payment2.third_fee IS '第三方票券服务费';

COMMENT ON COLUMN public.pos_bill_payment_coupons.coupon_buy_price IS '用户实付(券购买价)';
COMMENT ON COLUMN public.pos_bill_payment_coupons.due IS '券账单净收(结算给商家的钱)';
COMMENT ON COLUMN public.pos_bill_payment_coupons.tenancy_assume IS '商家优惠承担';
COMMENT ON COLUMN public.pos_bill_payment_coupons.third_assume IS '第三方优惠承担';
COMMENT ON COLUMN public.pos_bill_payment_coupons.third_fee IS '第三方票券服务费';
COMMENT ON COLUMN public.pos_bill_payment_coupons.request_state IS '请求票券信息状态,0表示没有请求;1表示请求成功;2表示请求返回错误';


------- pos_bill_regain  pos_bill_payment_regain 新增 净收字段 -------
DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_regain' and column_name='coupon_buy_price';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_regain ADD COLUMN coupon_buy_price numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_regain' and column_name='due';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_regain ADD COLUMN due numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_regain' and column_name='tenancy_assume';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_regain ADD COLUMN tenancy_assume numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_regain' and column_name='third_assume';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_regain ADD COLUMN third_assume numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_regain' and column_name='third_fee';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_regain ADD COLUMN third_fee numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment_regain' and column_name='coupon_buy_price';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_payment_regain ADD COLUMN coupon_buy_price numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment_regain' and column_name='due';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_payment_regain ADD COLUMN due numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment_regain' and column_name='tenancy_assume';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_payment_regain ADD COLUMN tenancy_assume numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment_regain' and column_name='third_assume';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_payment_regain ADD COLUMN third_assume numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment_regain' and column_name='third_fee';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_payment_regain ADD COLUMN third_fee numeric(19,6);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

---交班是否显示净收 系统参数---
DO $DO$
DECLARE num INT ;
BEGIN
	SELECT
		COUNT(*) INTO num
	FROM
		sys_parameter P
	WHERE
		para_code = 'posShowDueData'
	AND P .tenancy_id = tenancy_id()
	AND P .store_id = 0 ;
	IF num = 0 THEN
		EXECUTE ' INSERT INTO sys_parameter (tenancy_id,  store_id, system_name, model_name, para_name, para_code, para_value, para_defaut, para_type, valid_state, values_name, para_remark)
VALUES (tenancy_id(), 0, ''POS'', ''交班参数'', ''交班是否显示净收'', ''posShowDueData'', ''0'', ''0'', ''常规'', ''1'', ''0表示不体现，1、表示体现；'', '''');' ;
	ELSE
		RAISE NOTICE 'column  already exists in this table' ;
END IF ;
END $DO$;



------- 更新视图v_pos_bill,v_pos_bill_item,v_pos_bill_payment start-------
------- 肖恒
------- 2018-02-02

/*DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment' and column_name='yjzid';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_payment ADD COLUMN yjzid int4;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment2' and column_name='yjzid';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_payment2 ADD COLUMN yjzid int4;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

drop view if exists v_pos_bill;
CREATE VIEW v_pos_bill
(tenancy_id, store_id, id, bill_num, batch_num, serial_num, report_date, table_code, guest, opentable_time, payment_time, payment_num, open_pos_num, pos_num, waiter_num, open_opt, cashier_num, shift_id, item_menu_id, service_id, service_amount, service_discount, order_num, print_time, print_count, subtotal, bill_amount, payment_amount, difference, discountk_amount, discountr_amount, maling_amount, single_discount_amount, discount_amount, free_amount, givi_amount, more_coupon, average_amount, discount_num, discount_case_id, discount_rate, billfree_reason_id, discount_mode_id, transfer_remark, sale_mode, bill_state, bill_property, upload_tag, deposit_count, copy_bill_num, source, opt_login_number, guest_msg, integraloffset, remark, return_amount, advance_payment_amt, advance_refund_amt, is_refund, payment_id, pay_no, third_bill_code, payment_state, payment_manager_num, shop_real_amount, total_fees, platform_charge_amount, settlement_type, bill_taste, fictitious_table, recover_count, tax_rate, tax_money, service_tax_rate, service_tax_money, tax_amount, no_tax_amount, payment_tax_money, payment_notax, bill_tax_money, bill_notax, service_notax, settlement_price, discount_reason_id, dinner_type, coupon_buy_price, due, tenancy_assume, third_assume, third_fee,ms) AS
select tenancy_id, store_id, id, bill_num, batch_num, serial_num, report_date, table_code, guest, opentable_time, payment_time, payment_num, open_pos_num, pos_num, waiter_num, open_opt, cashier_num, shift_id, item_menu_id, service_id, service_amount, service_discount, order_num, print_time, print_count, subtotal, bill_amount, payment_amount, difference, discountk_amount, discountr_amount, maling_amount, single_discount_amount, discount_amount, free_amount, givi_amount, more_coupon, average_amount, discount_num, discount_case_id, discount_rate, billfree_reason_id, discount_mode_id, transfer_remark, sale_mode, bill_state, bill_property, upload_tag, deposit_count, copy_bill_num, source, opt_login_number, guest_msg, integraloffset, remark, return_amount, advance_payment_amt, advance_refund_amt, is_refund, payment_id, pay_no, third_bill_code, payment_state, payment_manager_num, shop_real_amount, total_fees, platform_charge_amount, settlement_type, bill_taste, fictitious_table, recover_count, tax_rate, tax_money, service_tax_rate, service_tax_money, tax_amount, no_tax_amount, payment_tax_money, payment_notax, bill_tax_money, bill_notax, service_notax, settlement_price, discount_reason_id, dinner_type, coupon_buy_price, due, tenancy_assume, third_assume, third_fee,1 AS ms from pos_bill
union all select tenancy_id, store_id, id, bill_num, batch_num, serial_num, report_date, table_code, guest, opentable_time, payment_time, payment_num, open_pos_num, pos_num, waiter_num, open_opt, cashier_num, shift_id, item_menu_id, service_id, service_amount, service_discount, order_num, print_time, print_count, subtotal, bill_amount, payment_amount, difference, discountk_amount, discountr_amount, maling_amount, single_discount_amount, discount_amount, free_amount, givi_amount, more_coupon, average_amount, discount_num, discount_case_id, discount_rate, billfree_reason_id, discount_mode_id, transfer_remark, sale_mode, bill_state, bill_property, upload_tag, deposit_count, copy_bill_num, source, opt_login_number, guest_msg, integraloffset, remark, return_amount, advance_payment_amt, advance_refund_amt, is_refund, payment_id, pay_no, third_bill_code, payment_state, payment_manager_num, shop_real_amount, total_fees, platform_charge_amount, settlement_type, bill_taste, fictitious_table, recover_count, tax_rate, tax_money, service_tax_rate, service_tax_money, tax_amount, no_tax_amount, payment_tax_money, payment_notax, bill_tax_money, bill_notax, service_notax, settlement_price, discount_reason_id, dinner_type, coupon_buy_price, due, tenancy_assume, third_assume, third_fee,1 AS ms from pos_bill2
WHERE (NOT (( pos_bill2.bill_num)::text IN(SELECT pos_bill.bill_num FROM pos_bill)));

drop view if exists v_pos_bill_item;
CREATE VIEW v_pos_bill_item
( tenancy_id,store_id,id,rwid,yrwid,bill_num,details_id,item_id,item_num,item_name,item_english,item_unit_id,item_unit_name,stable_code,table_code,pushmoney_way,proportion,assist_num,assist_money,waiter_num,item_price,item_count,item_amount,real_amount,discount_amount,single_discount_amount,discountr_amount,discount_state,discount_rate,discount_mode_id,item_class_id,item_property,item_remark,print_tag,waitcall_tag,setmeal_id,setmeal_rwid,is_setmeal_changitem,item_time,item_serial,report_date,item_shift_id,item_mac_id,item_taste,order_remark,seat_num,ticket_num,sale_mode,gqcj_tag,kvscp_tag,discount_reason_id,is_showitem,upload_tag,assist_item_id,integraloffset,remark,setmeal_group_id,group_id,third_price,returngive_reason_id,manager_num,return_type,return_count,method_money,batch_num,order_number,item_remark_his,opt_num,tax_rate,tax_money,item_notax,payment_tax_money,payment_notax,main_item,mid,activity_id,activity_batch_num,activity_rule_id,activity_count,is_consignment,settlement_price,single_amount,print_count,default_state,combo_prop,served_state,discount_num,count_rate,single_discount_rate,origin_item_price) AS
select tenancy_id,store_id,id,rwid,yrwid,bill_num,details_id,item_id,item_num,item_name,item_english,item_unit_id,item_unit_name,stable_code,table_code,pushmoney_way,proportion,assist_num,assist_money,waiter_num,item_price,item_count,item_amount,real_amount,discount_amount,single_discount_amount,discountr_amount,discount_state,discount_rate,discount_mode_id,item_class_id,item_property,item_remark,print_tag,waitcall_tag,setmeal_id,setmeal_rwid,is_setmeal_changitem,item_time,item_serial,report_date,item_shift_id,item_mac_id,item_taste,order_remark,seat_num,ticket_num,sale_mode,gqcj_tag,kvscp_tag,discount_reason_id,is_showitem,upload_tag,assist_item_id,integraloffset,remark,setmeal_group_id,group_id,third_price,returngive_reason_id,manager_num,return_type,return_count,method_money,batch_num,order_number,item_remark_his,opt_num,tax_rate,tax_money,item_notax,payment_tax_money,payment_notax,main_item,mid,activity_id,activity_batch_num,activity_rule_id,activity_count,is_consignment,settlement_price,single_amount,print_count,default_state,combo_prop,served_state,discount_num,count_rate,single_discount_rate,origin_item_price from pos_bill_item
union all select tenancy_id,store_id,id,rwid,yrwid,bill_num,details_id,item_id,item_num,item_name,item_english,item_unit_id,item_unit_name,stable_code,table_code,pushmoney_way,proportion,assist_num,assist_money,waiter_num,item_price,item_count,item_amount,real_amount,discount_amount,single_discount_amount,discountr_amount,discount_state,discount_rate,discount_mode_id,item_class_id,item_property,item_remark,print_tag,waitcall_tag,setmeal_id,setmeal_rwid,is_setmeal_changitem,item_time,item_serial,report_date,item_shift_id,item_mac_id,item_taste,order_remark,seat_num,ticket_num,sale_mode,gqcj_tag,kvscp_tag,discount_reason_id,is_showitem,upload_tag,assist_item_id,integraloffset,remark,setmeal_group_id,group_id,third_price,returngive_reason_id,manager_num,return_type,return_count,method_money,batch_num,order_number,item_remark_his,opt_num,tax_rate,tax_money,item_notax,payment_tax_money,payment_notax,main_item,mid,activity_id,activity_batch_num,activity_rule_id,activity_count,is_consignment,settlement_price,single_amount,print_count,default_state,combo_prop,served_state,discount_num,count_rate,single_discount_rate,origin_item_price from pos_bill_item2
where (NOT ((pos_bill_item2.bill_num)::text IN( SELECT pos_bill.bill_num FROM pos_bill)));

drop view if exists v_pos_bill_payment;
CREATE VIEW v_pos_bill_payment
(tenancy_id,store_id,id,bill_num,table_code,type,jzid,name,name_english,amount,count,number,phone,report_date,shift_id,pos_num,cashier_num,last_updatetime,is_ysk,rate,currency_amount,upload_tag,customer_id,bill_code,remark,payment_state,batch_num,more_coupon,fee,fee_rate,coupon_type,yjzid,coupon_buy_price,due,tenancy_assume,third_assume,third_fee ) AS
select tenancy_id,store_id,id,bill_num,table_code,type,jzid,name,name_english,amount,count,number,phone,report_date,shift_id,pos_num,cashier_num,last_updatetime,is_ysk,rate,currency_amount,upload_tag,customer_id,bill_code,remark,payment_state,batch_num,more_coupon,fee,fee_rate,coupon_type,yjzid,coupon_buy_price,due,tenancy_assume,third_assume,third_fee from pos_bill_payment
union all select tenancy_id,store_id,id,bill_num,table_code,type,jzid,name,name_english,amount,count,number,phone,report_date,shift_id,pos_num,cashier_num,last_updatetime,is_ysk,rate,currency_amount,upload_tag,customer_id,bill_code,remark,payment_state,batch_num,more_coupon,fee,fee_rate,coupon_type,yjzid,coupon_buy_price,due,tenancy_assume,third_assume,third_fee from pos_bill_payment2
WHERE (NOT ((pos_bill_payment2.bill_num)::text IN(SELECT pos_bill.bill_num FROM pos_bill)));*/

------- 更新视图v_pos_bill,v_pos_bill_item,v_pos_bill_payment end -------

-----------------------------------------------------20180116发版------------------------------------------------

--取消优惠按钮
DELETE FROM sys_modules where module_link_url= '5328' ;
INSERT INTO "public"."sys_modules" ( "id", "module_name", "module_type", "create_date", "create_person", "states", "module_level", "father_module_id", "module_link_url", "module_use_img", "module_class", "if_super_module", "if_ui_display", "shortcuts","version") VALUES (805328, '取消优惠', 'pos', '2018-02-06', 'admin', '1', '4', '800019', '5328', '', NULL, NULL, '1', NULL,'1.0');

---菜品实时流水查询按钮
DELETE FROM sys_modules where module_link_url= '3023' ;
INSERT INTO "public"."sys_modules" ( "id","module_name", "module_type", "create_date", "create_person", "states", "module_level", "father_module_id", "module_link_url", "module_use_img", "module_class", "if_super_module", "if_ui_display", "shortcuts","version") VALUES (800519,'菜品实时流水查询', 'pos', '2018-01-09', 'admin', '1', '4', '800500', '3023', '', NULL, NULL,'1', NULL,'1.0');

-----------------------------------------------------20180116a发版------------------------------------------------

--添加系统参数 third_order_channel

DELETE FROM sys_parameter WHERE para_code='third_order_channel';
INSERT INTO sys_parameter (tenancy_id,  store_id, system_name, model_name, para_name, para_code, para_value, para_defaut, para_type, valid_state, values_name, para_remark)
VALUES (tenancy_id(), 0, 'POS', '系统参数', '外卖订单渠道', 'third_order_channel', 'BD06,MT08,EL09,MT11,WM10', 'BD06,MT08,EL09,MT11,WM10', '常规', '1', '外卖订单渠道', '');

---添加系统参数  点餐菜品有附选项时是否自动弹出确认

DO $DO$
DECLARE num INT ;
BEGIN
	SELECT
		COUNT(*) INTO num
	FROM
		sys_parameter P
	WHERE
		para_code = 'selectAutoShow'
	AND P .tenancy_id = tenancy_id()
	AND P .store_id = 0 ;
	IF num = 0 THEN
		EXECUTE ' INSERT INTO sys_parameter (tenancy_id,  store_id, system_name, model_name, para_name, para_code, para_value, para_defaut, para_type, valid_state, values_name, para_remark)
VALUES (tenancy_id(), 0, ''POS'', ''POS参数'', ''点餐菜品有附选项时是否自动弹出确认'', ''selectAutoShow'', ''0'', ''0'', ''常规'', ''1'', ''0:不自动弹出,1:自动弹出'', '''');' ;
	ELSE
		RAISE NOTICE 'column  already exists in this table' ;
END IF ;
END $DO$;

--- 快餐历史账单查询按钮
DELETE FROM sys_modules WHERE module_link_url = '5733';
INSERT INTO sys_modules (id, module_name, module_type, create_date, create_person, states, module_level, father_module_id, module_link_url, module_use_img, module_class, if_super_module, if_ui_display, shortcuts,version)
VALUES ('805733', '历史账单查询', 'pos', '2018-03-19', 'admin', '1', '3', '800020', '5733', '', NULL, NULL, '1', NULL,'1.0');

--------------------hq_upgrade_v1.15.0_0321(build-20180321)--------------------

--重置门店接单辅助配置参数start --
DELETE FROM sys_parameter WHERE para_code = 'ORDER_ASSIST_CONFIG';
DO $DO$
DECLARE num INT;
BEGIN
    select count(*) into num from sys_parameter p where para_code = 'ORDER_ASSIST_CONFIG' and p.tenancy_id = tenancy_id() and p.store_id = 0;
IF num = 0 THEN
    EXECUTE ' INSERT INTO sys_parameter (tenancy_id,  store_id, system_name, model_name, para_name, para_code, para_value, para_defaut, para_type, valid_state, values_name, para_remark)
VALUES (tenancy_id(), 0, ''SYS'', ''系统参数'', ''门店接单辅助参数配置'', ''ORDER_ASSIST_CONFIG'', ''redisPull:90,lostPull:60'', ''redisPull:90,lostPull:60'', ''常规'', ''1'', ''参数格式: redisPull:90,lostPull:60 (单位为秒，值为0时禁用)'', '''');';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;
--重置门店接单辅助配置参数end--
-- 注意：此脚本需要商户下所有门店升级到1.15.3_0321c以上执行，否则可能会引发重单问题

--------------------hq_upgrade_v1.15.3_0321c(build-20180416)--------------------

--- 修改促销员按钮
DELETE FROM sys_modules WHERE module_link_url = '5330';
INSERT INTO sys_modules (id, module_name, module_type, create_date, create_person, states, module_level, father_module_id, module_link_url, module_use_img, module_class, if_super_module, if_ui_display, shortcuts,version)
VALUES ('805330', '修改促销员', 'pos', '2018-03-27', 'admin', '1', '4', '800019', '5330', '', NULL, NULL, '1', NULL,'1.0');

--- 修改单头按钮
DELETE FROM sys_modules WHERE module_link_url = '5329';
INSERT INTO sys_modules (id, module_name, module_type, create_date, create_person, states, module_level, father_module_id, module_link_url, module_use_img, module_class, if_super_module, if_ui_display, shortcuts,version)
VALUES ('805329', '修改单头', 'pos', '2018-03-27', 'admin', '1', '4', '800019', '5329', '', NULL, NULL, '1', NULL,'1.0');

--- 禁用修改服务员和修改人数按钮
--UPDATE "public"."sys_modules" SET "states" = '0' WHERE "module_link_url" in( '5012','5007') and  "states" = '1';

--微生活点餐平台对接
--2018-03-18
--肖恒
---系统参数设置
DELETE FROM sys_parameter WHERE system_name='POS' AND para_code='WLIFE_BUSINESS_ID';
INSERT INTO sys_parameter (tenancy_id, store_id, system_name, model_name, para_name, para_code, para_value, para_defaut, para_type, valid_state, values_name, para_remark)
VALUES (tenancy_id(), 0, 'POS', '微生活点餐平台对接', '商户ID', 'WLIFE_BUSINESS_ID', '', '', '常规', '1', '商户提供', null);
DELETE FROM sys_parameter WHERE system_name='POS' AND para_code='WLIFE_BRAND_ID';
INSERT INTO sys_parameter (tenancy_id, store_id, system_name, model_name, para_name, para_code, para_value, para_defaut, para_type, valid_state, values_name, para_remark)
VALUES (tenancy_id(), 0, 'POS', '微生活点餐平台对接', '品牌ID', 'WLIFE_BRAND_ID', '', '', '常规', '1', '商户提供', null);
DELETE FROM sys_parameter WHERE system_name='POS' AND para_code='WLIFE_SHOP_ID';
INSERT INTO sys_parameter (tenancy_id, store_id, system_name, model_name, para_name, para_code, para_value, para_defaut, para_type, valid_state, values_name, para_remark)
VALUES (tenancy_id(), 0, 'POS', '微生活点餐平台对接', '门店ID', 'WLIFE_SHOP_ID', '', '', '常规', '1', '商户提供', null);
DELETE FROM sys_parameter WHERE system_name='POS' AND para_code='WLIFE_SHOP_KEY';
INSERT INTO sys_parameter (tenancy_id, store_id, system_name, model_name, para_name, para_code, para_value, para_defaut, para_type, valid_state, values_name, para_remark)
VALUES (tenancy_id(), 0, 'POS', '微生活点餐平台对接', '门店密码', 'WLIFE_SHOP_KEY', '', '', '常规', '1', '商户提供', null);
DELETE FROM sys_parameter WHERE system_name='POS' AND para_code='IS_USER_WLIFE';
INSERT INTO sys_parameter (tenancy_id, store_id, system_name, model_name, para_name, para_code, para_value, para_defaut, para_type, valid_state, values_name, para_remark)
VALUES (tenancy_id(), 0, 'POS', '微生活点餐平台对接', '是否启用微生活点餐平台', 'IS_USER_WLIFE', '0', '0', '常规', '1', '0:否, 1:是', null);
DELETE FROM sys_parameter WHERE system_name='POS' AND para_code='WLIFE_DISH_KINDS_MODE';
INSERT INTO sys_parameter (tenancy_id, store_id, system_name, model_name, para_name, para_code, para_value, para_defaut, para_type, valid_state, values_name, para_remark)
VALUES (tenancy_id(), 0, 'POS', '微生活点餐平台对接', '上传菜品类别', 'WLIFE_DISH_KINDS_MODE', '2', '2', '常规', '1', '1:上传大类, 2:上传小类', null);

---付款方式设置
DELETE FROM sys_dictionary WHERE class_identifier_code='third_pay' AND class_item_code='wechat_pay_wlife';
INSERT INTO sys_dictionary (tenancy_id, model_name, application_model, class_identifier, class_identifier_code, class_item, class_item_code, is_sys, remark, valid_state, last_operator, last_updatetime, fake_id)
VALUES (tenancy_id(), 'hq', '付款方式', '第三方支付', 'third_pay', '微生活微信支付', 'wechat_pay_wlife', 'Y', '系统内部使用', '1', 'system', '2018-03-18 00:00:00', null);
DELETE FROM sys_dictionary WHERE class_identifier_code='third_pay' AND class_item_code='ali_pay_wlife';
INSERT INTO sys_dictionary (tenancy_id, model_name, application_model, class_identifier, class_identifier_code, class_item, class_item_code, is_sys, remark, valid_state, last_operator, last_updatetime, fake_id)
VALUES (tenancy_id(), 'hq', '付款方式', '第三方支付', 'third_pay', '微生活支付宝支付', 'ali_pay_wlife', 'Y', '系统内部使用', '1', 'system', '2018-03-18 00:00:00', null);

--微生活点餐平台对接  --------end


---添加系统参数  账单整单备注的有效时效
DO $DO$
DECLARE num INT ;
BEGIN
	SELECT
		COUNT(*) INTO num
	FROM
		sys_parameter P
	WHERE
		para_code = 'bill_taste_effect_batch'
	AND P .tenancy_id = tenancy_id()
	AND P .store_id = 0 ;
	IF num = 0 THEN
		EXECUTE ' INSERT INTO sys_parameter (tenancy_id,  store_id, system_name, model_name, para_name, para_code, para_value, para_defaut, para_type, valid_state, values_name, para_remark)
VALUES (tenancy_id(), 0, ''POS'', ''POS参数'', ''账单整单备注的有效时效'', ''bill_taste_effect_batch'', ''0'', ''0'', ''常规'', ''1'', ''0：一直有效(不清空)   1：下单时当批次有效(下单后清空)'', '''');' ;
	ELSE
		RAISE NOTICE 'column  already exists in this table' ;
END IF ;
END $DO$;


--修改系统参数  外卖随餐单参数控制
update sys_parameter set values_name ='0：表示不支持, 1：表示支持，受外卖平台参数影响, 2：表示支持，不受外卖平台参数影响' where para_code='dzfp_dsfwmsfkjdzfp';


--点菜数量限制设置 --------
--2018-04-18
--肖恒
delete from sys_parameter where para_code='DISH_COUNT_LIMIT';
insert into sys_parameter (tenancy_id, store_id, system_name, model_name, para_name, para_code, para_value, para_defaut, para_type, valid_state, values_name, para_remark) 
values (tenancy_id(), 0, 'POS', '系统参数', '点菜数量限制设置', 'DISH_COUNT_LIMIT', '100', '100', '常规', '1', '0为不限制，点菜超过数量时会弹出提醒；', null);	

--点菜数量限制设置  --------end
  --------------------hq_upgrade_v1.16.0_0411(build-20180416)--------------------


  ---添加系统参数  E自助是否显示游戏入口 (已于2018年04-25执行升级)

DO $DO$
DECLARE num INT ;
BEGIN
	SELECT
		COUNT(*) INTO num
	FROM
		sys_parameter P
	WHERE
		para_code = 'e_zizhu_is_show_game'
	AND P .tenancy_id = tenancy_id()
	AND P .store_id = 0 ;
	IF num = 0 THEN
		EXECUTE ' INSERT INTO sys_parameter (tenancy_id,  store_id, system_name, model_name, para_name, para_code, para_value, para_defaut, para_type, valid_state, values_name, para_remark)
VALUES (tenancy_id(), 0, ''POS'', ''POS参数'', ''E自助是否显示游戏入口'', ''e_zizhu_is_show_game'', ''0'', ''0'', ''常规'', ''1'', ''0:不显示,1:显示 (默认为0不显示)'', '''');' ;
	ELSE
		RAISE NOTICE 'column  already exists in this table' ;
END IF ;
END $DO$;

--- 重置sys_parameter表序列,防止ID冲突
DO $DO$
DECLARE num INT;
BEGIN
		select max(id)+1 into num  FROM sys_parameter;
IF num >0 THEN
		EXECUTE 'alter sequence sys_parameter_id_seq restart with '|| num;
else  alter sequence sys_parameter_id_seq restart with 1;
END IF;
END $DO$;




------ 服务费折让 start--------------  (肖恒已于发版前单独执行)
--2018-04-24
--肖恒
DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill' and column_name='item_discountr_amount';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill ADD COLUMN item_discountr_amount numeric(19,4);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill' and column_name='service_discountr_amount';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill ADD COLUMN service_discountr_amount numeric(19,4);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill2' and column_name='item_discountr_amount';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill2 ADD COLUMN item_discountr_amount numeric(19,4);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill2' and column_name='service_discountr_amount';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill2 ADD COLUMN service_discountr_amount numeric(19,4);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_regain' and column_name='item_discountr_amount';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_regain ADD COLUMN item_discountr_amount numeric(19,4);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_regain' and column_name='service_discountr_amount';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_regain ADD COLUMN service_discountr_amount numeric(19,4);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;
------ 服务费折让  end--------------

--- 外卖订单转账单模式start---
DELETE FROM sys_parameter WHERE para_code='order_translate_bill_mode';
INSERT INTO sys_parameter (tenancy_id,  store_id, system_name, model_name, para_name, para_code, para_value, para_defaut, para_type, valid_state, values_name, para_remark)
VALUES (tenancy_id(), 0, 'POS', '系统参数', '订单转账单方式', 'order_translate_bill_mode', '0', '0', '常规', '1', '0:调用下单接口;1:订单直接转存为账单;', '');
--- 外卖订单转账单模式end---

--- 是否启用预打账单后自动锁定桌台start---
DELETE FROM sys_parameter WHERE para_code='preprint_is_lock_table';
INSERT INTO sys_parameter (tenancy_id,  store_id, system_name, model_name, para_name, para_code, para_value, para_defaut, para_type, valid_state, values_name, para_remark)
VALUES (tenancy_id(), 0, 'POS', '系统参数', '是否启用预打账单后自动锁定桌台', 'preprint_is_lock_table', '0', '0', '常规', '1', '0:否;1:是;', '');
--- 是否启用预打账单后自动锁定桌台end---

--- 更新开台权限按钮(按需根据商户执行)
--update sys_modules set module_type='pos',module_link_url='3024',module_name='开台' where module_link_url='3001';

--- 是否启用新美大秒付
DELETE FROM sys_parameter WHERE para_code='ifup_quick_pay';
INSERT INTO sys_parameter (tenancy_id,  store_id, system_name, model_name, para_name, para_code, para_value, para_defaut, para_type, valid_state, values_name, para_remark)
VALUES (tenancy_id(), 0, 'POS', '系统参数', '是否启用新美大秒付', 'ifup_quick_pay', '0', '0', '常规', '1', '0:关闭;1:启用;', '');

--- 门店POS是否启用网络异常提醒
DELETE FROM sys_parameter WHERE para_code='network_disconnet_notify';
INSERT INTO sys_parameter (tenancy_id,  store_id, system_name, model_name, para_name, para_code, para_value, para_defaut, para_type, valid_state, values_name, para_remark)
VALUES (tenancy_id(), 0, 'POS', '系统参数', '是否开启网络异常提醒', 'network_disconnet_notify', '1', '0', '常规', '1', '0:关闭;1:启用;', '');

--- 增加优惠券明细记不记收入与显示优惠券详情功能start---
DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment' and column_name='payment_uid';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_payment ADD COLUMN payment_uid CHARACTER VARYING(64);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment2' and column_name='payment_uid';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_payment2 ADD COLUMN payment_uid CHARACTER VARYING(64);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment_regain' and column_name='payment_uid';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_payment_regain ADD COLUMN payment_uid CHARACTER VARYING(64);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment_coupons' and column_name='rwid';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_payment_coupons ADD COLUMN rwid int4;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_payment_coupons' and column_name='item_unit_id';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_bill_payment_coupons ADD COLUMN item_unit_id int4;';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

ALTER TABLE pos_bill_payment_coupons ALTER payment_id type CHARACTER VARYING(64);

DO $DO$
DECLARE num INT ;
BEGIN
	SELECT
		COUNT(*) INTO num
	FROM
		sys_parameter P
	WHERE
		para_code = 'changshift_is_show_coupons_details'
	AND P .tenancy_id = tenancy_id()
	AND P .store_id = 0 ;
	IF num = 0 THEN
		EXECUTE ' INSERT INTO sys_parameter (tenancy_id,  store_id, system_name, model_name, para_name, para_code, para_value, para_defaut, para_type, valid_state, values_name, para_remark)
VALUES (tenancy_id(), 0, ''POS'', ''系统参数'', ''POS交班显示优惠券核销详情'', ''changshift_is_show_coupons_details'', ''0'', ''0'', ''常规'', ''1'', ''0:不显示,1:显示 (默认为0不显示)'', '''');' ;
	ELSE
		RAISE NOTICE 'column  already exists in this table' ;
END IF ;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_opter_changshift' and column_name='sort_num';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_opter_changshift ADD COLUMN sort_num CHARACTER VARYING(8);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_opter_changshift' and column_name='parent_sort_num';
IF num = 0 THEN
		EXECUTE 'ALTER TABLE pos_opter_changshift ADD COLUMN parent_sort_num CHARACTER VARYING(8);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;
--- 增加优惠券明细记不记收入与显示优惠券详情功能end---

--------------------hq_upgrade_v1.17.0_0605(build-20180604)--------------------

--- 增加微生活会员付款方式start---
--- 肖恒 2018-07-03---
INSERT INTO sys_dictionary ( tenancy_id,id, model_name, application_model, class_identifier, class_identifier_code, class_item, class_item_code, is_sys, remark, valid_state, last_operator, last_updatetime) 
SELECT tenancy_id(),'11026', 'hq', '付款方式设置', '第三方支付', 'third_pay', '微生活会员', 'wlife_balance', 'Y', NULL, '1', 'system', now() 
WHERE NOT EXISTS ( SELECT 1 FROM sys_dictionary WHERE class_identifier_code = 'third_pay' AND class_item_code = 'wlife_balance');

INSERT INTO sys_dictionary ( tenancy_id,id, model_name, application_model, class_identifier, class_identifier_code, class_item, class_item_code, is_sys, remark, valid_state, last_operator, last_updatetime) 
SELECT tenancy_id(),'11027', 'hq', '付款方式设置', '第三方支付', 'third_pay', '微生活会员积分', 'wlife_credit', 'Y', NULL, '1', 'system', now() 
WHERE NOT EXISTS ( SELECT 1 FROM sys_dictionary WHERE class_identifier_code = 'third_pay' AND class_item_code = 'wlife_credit');

INSERT INTO sys_dictionary ( tenancy_id,id, model_name, application_model, class_identifier, class_identifier_code, class_item, class_item_code, is_sys, remark, valid_state, last_operator, last_updatetime) 
SELECT tenancy_id(),'11028', 'hq', '付款方式设置', '第三方支付', 'third_pay', '微生活优惠券', 'wlife_coupon', 'Y', NULL, '1', 'system', now() 
WHERE NOT EXISTS ( SELECT 1 FROM sys_dictionary WHERE class_identifier_code = 'third_pay' AND class_item_code = 'wlife_coupon');
--- 增加微生活会员付款方式end---

--------------------hq_upgrade_v1.17.1_0713(build-20180713)--------------------

--修改会员表，添加可开票金额字段
DO $DO$
DECLARE num INT; 
BEGIN  
  select count(*) into num from information_schema.columns 
  where table_name = 'crm_customer_info' and column_name='invoice_balance';
	IF num = 0 THEN  
		ALTER TABLE crm_customer_info ADD COLUMN invoice_balance numeric(19,4); 
	  	COMMENT ON COLUMN "public"."crm_customer_info"."invoice_balance" IS '可开票金额';
	ELSE 
		RAISE NOTICE ' The column crm_customer_info already exists in this table';
	END IF;

END $DO$;


--创建开票流水记录表
CREATE TABLE IF NOT EXISTS "public"."crm_invoice_trading_list" (
"tenancy_id" varchar(32) COLLATE "default",
"id" serial,
"card_id" int4,
"card_code" varchar(50) COLLATE "default",
"bill_code" varchar(50) COLLATE "default",
"chanel" varchar(50) COLLATE "default",
"store_id" int4,
"business_date" date,
"main_trading" numeric(19,4),
"reward_trading" numeric(19,4),
"operat_type" varchar(10) COLLATE "default",
"main_original" numeric(19,4),
"reward_original" numeric(19,4),
"deposit" numeric(19,4),
"operator" varchar(30) COLLATE "default",
"operate_time" timestamp(6),
"bill_money" numeric(19,4),
"third_bill_code" varchar(50) COLLATE "default",
"bill_code_original" varchar(50) COLLATE "default",
"activity_id" int4,
"customer_id" int4,
"revoked_trading" numeric(19,4),
"batch_num" varchar(32) COLLATE "default",
"last_updatetime" timestamp(6),
"store_updatetime" timestamp(6),
"card_class_id" int4,
"name" varchar(32) COLLATE "default",
"mobil" varchar(16) COLLATE "default",
"main_balance" numeric(19,4),
"reward_balance" numeric(19,4),
"total_balance" numeric(19,4),
"operator_id" int4,
"shift_id" int4,
"pay_type" varchar(30) COLLATE "default",
"salesman" int4,
"commission_saler_money" numeric(19,4),
"commission_store_money" numeric(19,4),
"invoice_balance" numeric(19,4),
"invoice_money" numeric(19,4),
"invoice_num" int4,
"remark" varchar(32),
CONSTRAINT "pk_crm_invoice_trading_list" PRIMARY KEY ("id")
);

ALTER TABLE "public"."crm_invoice_trading_list" OWNER TO "tzxdb";

COMMENT ON COLUMN "public"."crm_invoice_trading_list"."revoked_trading" IS '已撤销金额';

COMMENT ON COLUMN "public"."crm_invoice_trading_list"."salesman" IS '销售人员id';

COMMENT ON COLUMN "public"."crm_invoice_trading_list"."commission_saler_money" IS '人员提成金额';

COMMENT ON COLUMN "public"."crm_invoice_trading_list"."commission_store_money" IS '餐厅提成金额';

COMMENT ON COLUMN "public"."crm_invoice_trading_list"."invoice_balance" IS '可开票金额';

COMMENT ON COLUMN "public"."crm_invoice_trading_list"."invoice_money" IS '本次开票金额';

COMMENT ON COLUMN "public"."crm_invoice_trading_list"."invoice_num" IS '开票张数';

CREATE INDEX IF NOT EXISTS "inx_citl_ci" ON "public"."crm_invoice_trading_list" USING btree (customer_id);

--------------------hq_upgrade_v1.17.2_0719(build-20180719)--------------------

---添加启用微生活点餐类型参数
---2018-07-25 肖恒
INSERT INTO sys_parameter (tenancy_id, store_id, system_name, model_name, para_name, para_code, para_value, para_defaut, para_type, valid_state, values_name, para_remark)
SELECT tenancy_id(), 0, 'POS', '微生活点餐平台对接', '启用微生活点餐类型', 'USER_WLIFE_ORDER_TYPE', '0', '0', '常规', '1', '0:不启用微生活点餐;H5:H5点餐;PROGRAM:小程序点餐;MEIWEI_PROGRAM:美味不用等小程序点餐', null
WHERE NOT EXISTS ( SELECT 1 FROM sys_parameter WHERE system_name='POS' AND para_code='USER_WLIFE_ORDER_TYPE');

DELETE FROM sys_parameter_detail WHERE para_code='USER_WLIFE_ORDER_TYPE';
INSERT INTO sys_parameter_detail (tenancy_id,id, para_code, para_value, para_value_code)
select tenancy_id(), max(id)+1, 'USER_WLIFE_ORDER_TYPE', '不启用微生活点餐', '0' from sys_parameter_detail;
INSERT INTO sys_parameter_detail (tenancy_id, id,para_code, para_value, para_value_code)
select tenancy_id(), max(id)+1 ,'USER_WLIFE_ORDER_TYPE', 'H5点餐', 'H5' from sys_parameter_detail;
INSERT INTO sys_parameter_detail (tenancy_id, id,para_code, para_value, para_value_code)
select tenancy_id(), max(id)+1 ,'USER_WLIFE_ORDER_TYPE', '小程序点餐', 'PROGRAM' from sys_parameter_detail;
INSERT INTO sys_parameter_detail (tenancy_id, id,para_code, para_value, para_value_code)
select tenancy_id(), max(id)+1 ,'USER_WLIFE_ORDER_TYPE', '美味不用等小程序点餐', 'MEIWEI_PROGRAM' from sys_parameter_detail;
---添加启用微生活点餐类型参数end---

---添加微生活点餐小程序退款appkey参数
---2018-08-01 秦桂
INSERT INTO sys_parameter (tenancy_id, store_id, system_name, model_name, para_name, para_code, para_value, para_defaut, para_type, valid_state, values_name, para_remark)
SELECT tenancy_id(), 0, 'POS', '微生活点餐平台对接', '微生活点餐小程序退款appkey', 'WLIFE_PROGRAM_APP_KEY', '', 'ACEWILL&WSH', '常规', '1', '微生活小程序退款appkey', null
WHERE NOT EXISTS ( SELECT 1 FROM sys_parameter WHERE system_name='POS' AND para_code='WLIFE_PROGRAM_APP_KEY');
---添加微生活点餐小程序退款appkey参数end---
-- 微生活点餐增加点餐模式--
-- 2018-08-22 张东升
DO $DO$
DECLARE num INT;
BEGIN
  select count(*) into num from sys_parameter p where para_code = 'wlife_ordermode' and p.tenancy_id = tenancy_id() and p.store_id = 0;
  IF num = 0 THEN
    INSERT INTO sys_parameter (tenancy_id,  store_id, system_name, model_name, para_name, para_code, para_value, para_defaut, para_type, valid_state, values_name, para_remark)
    VALUES (tenancy_id(), 0, 'POS', '微生活点餐平台对接', '微生活小程序点餐模式(先付/后付)', 'wlife_ordermode', 3, 3, '常规', '1', '1：扫付，2：先付，3：后付', '');
  ELSE RAISE NOTICE 'column  already exists in this table';
  END IF;
END $DO$;
--微生活点餐增加点餐模式 end--


---添加系统参数 折扣时是否需要选择折扣原因

DO $DO$
DECLARE num INT ;
BEGIN
	SELECT
		COUNT(*) INTO num
	FROM
		sys_parameter P
	WHERE
		para_code = 'selectDiscountReasonShow'
	AND P .tenancy_id = tenancy_id()
	AND P .store_id = 0 ;
	IF num = 0 THEN
		EXECUTE ' INSERT INTO sys_parameter (tenancy_id,  store_id, system_name, model_name, para_name, para_code, para_value, para_defaut, para_type, valid_state, values_name, para_remark)
VALUES (tenancy_id(), 0, ''POS'', ''POS参数'', ''折扣时是否需要选择折扣原因'', ''selectDiscountReasonShow'', ''1'', ''1'', ''常规'', ''1'', ''0:不需要选择,1:需要选择'', '''');' ;
	ELSE
		RAISE NOTICE 'column  already exists in this table' ;
END IF ;
END $DO$;

---付款方式设置(对接美味不用等小程序点餐)
DELETE FROM sys_dictionary WHERE class_identifier_code='third_pay' AND class_item_code='wechat_pay_meiwei';
INSERT INTO sys_dictionary (tenancy_id, model_name, application_model, class_identifier, class_identifier_code, class_item, class_item_code, is_sys, remark, valid_state, last_operator, last_updatetime, fake_id)
VALUES (tenancy_id(), 'hq', '付款方式', '第三方支付', 'third_pay', '美味不用等微信支付', 'wechat_pay_meiwei', 'Y', '系统内部使用', '1', 'system', '2018-08-24 00:00:00', null);
DELETE FROM sys_dictionary WHERE class_identifier_code='third_pay' AND class_item_code='ali_pay_meiwei';
INSERT INTO sys_dictionary (tenancy_id, model_name, application_model, class_identifier, class_identifier_code, class_item, class_item_code, is_sys, remark, valid_state, last_operator, last_updatetime, fake_id)
VALUES (tenancy_id(), 'hq', '付款方式', '第三方支付', 'third_pay', '美味不用等支付宝支付', 'ali_pay_meiwei', 'Y', '系统内部使用', '1', 'system', '2018-08-24 00:00:00', null);


--------------------hq_upgrade_v1.18.1_0605(build-20180604)--------------------

---1.19.0_0829 ---------------------
--将  "开台是否选择就餐类型" 修改为    "是否开启就餐类型"
update sys_parameter set para_name = '是否开启就餐类型' where para_code = 'openTableSetDinnerType' ;

-- 新增系统参数  "开启第三方配送平台"
insert into sys_parameter (tenancy_id, store_id, system_name,model_name,para_name,para_value,para_defaut,para_type,valid_state,values_name,last_operator,last_updatetime,para_code)
SELECT tenancy_id (),0,'POS','系统参数','开启第三方配送平台',0,0,'常规',1,'0:不开启;1:开启;','admin','2018-08-23 15:33:42','KQDSFPSPT' 
where not exists (select 1 from sys_parameter where para_code = 'KQDSFPSPT');

INSERT INTO sys_dictionary ( tenancy_id,id, model_name, application_model, class_identifier, class_identifier_code, class_item, class_item_code, is_sys, remark, valid_state, last_operator, last_updatetime) 
SELECT tenancy_id(),'101001', 'cc', '配送管理', '配送渠道', 'deliver_type', '达达配送', 'dada', 'Y', NULL, '1', 'SYSTEM', now() 
WHERE NOT EXISTS ( SELECT 1 FROM sys_dictionary WHERE class_identifier_code = 'deliver_type' AND class_item_code = 'dada');

INSERT INTO sys_dictionary ( tenancy_id,id, model_name, application_model, class_identifier, class_identifier_code, class_item, class_item_code, is_sys, remark, valid_state, last_operator, last_updatetime) 
SELECT tenancy_id(),'101002', 'cc', '配送管理', '配送渠道', 'deliver_type', '蜂鸟配送', 'fn', 'Y', NULL, '0', 'SYSTEM', now() 
WHERE NOT EXISTS ( SELECT 1 FROM sys_dictionary WHERE class_identifier_code = 'deliver_type' AND class_item_code = 'fn');

INSERT INTO sys_dictionary ( tenancy_id,id, model_name, application_model, class_identifier, class_identifier_code, class_item, class_item_code, is_sys, remark, valid_state, last_operator, last_updatetime) 
SELECT tenancy_id(),'101003', 'cc', '配送管理', '配送渠道', 'deliver_type', '顺风配送', 'xf', 'Y', NULL, '0', 'SYSTEM', now() 
WHERE NOT EXISTS ( SELECT 1 FROM sys_dictionary WHERE class_identifier_code = 'deliver_type' AND class_item_code = 'xf');


INSERT INTO sys_dictionary ( tenancy_id,id, model_name, application_model, class_identifier, class_identifier_code, class_item, class_item_code, is_sys, remark, valid_state, last_operator, last_updatetime) 
SELECT tenancy_id(),'101004', 'cc', '配送管理', '配送订单来源', 'deliver_order_channel', '外卖平台订单', 'WM', 'Y', NULL, '1', 'SYSTEM', now() 
WHERE NOT EXISTS ( SELECT 1 FROM sys_dictionary WHERE class_identifier_code = 'deliver_order_channel' AND class_item_code = 'WM');

INSERT INTO sys_dictionary ( tenancy_id,id, model_name, application_model, class_identifier, class_identifier_code, class_item, class_item_code, is_sys, remark, valid_state, last_operator, last_updatetime) 
SELECT tenancy_id(),'101005', 'cc', '配送管理', '配送订单来源', 'deliver_order_channel', '微信订单', 'WX', 'Y', NULL, '1', 'SYSTEM', now() 
WHERE NOT EXISTS ( SELECT 1 FROM sys_dictionary WHERE class_identifier_code = 'deliver_order_channel' AND class_item_code = 'WX');

INSERT INTO sys_dictionary ( tenancy_id,id, model_name, application_model, class_identifier, class_identifier_code, class_item, class_item_code, is_sys, remark, valid_state, last_operator, last_updatetime) 
SELECT tenancy_id(),'101006', 'cc', '配送管理', '配送订单来源', 'deliver_order_channel', '堂食外送', 'TS', 'Y', NULL, '1', 'SYSTEM', now() 
WHERE NOT EXISTS ( SELECT 1 FROM sys_dictionary WHERE class_identifier_code = 'deliver_order_channel' AND class_item_code = 'TS');


INSERT INTO sys_modules (id, module_name, module_type, create_date, create_person, states, module_level, father_module_id, module_link_url, module_use_img, module_class, if_super_module, if_ui_display, shortcuts) 
SELECT '805736', '账单配送', 'pos', '2018-08-27', 'admin', '1', '3', '800020', '5736', '', NULL, NULL, '1', NULL
WHERE NOT EXISTS ( SELECT 1 FROM sys_modules WHERE id='805736' AND module_name = '账单配送' AND module_link_url = '5736');

INSERT INTO sys_modules (id, module_name, module_type, create_date, create_person, states, module_level, father_module_id, module_link_url, module_use_img, module_class, if_super_module, if_ui_display, shortcuts) 
SELECT '805737', '三方配送异常', 'pos', '2018-08-27', 'admin', '1', '3', '800020', '5737', '', NULL, NULL, '1', NULL
WHERE NOT EXISTS ( SELECT 1 FROM sys_modules WHERE id='805736' AND module_name = '三方配送异常' AND module_link_url = '5737');

--- 设备功能配置 
--- 新增"三方配送异常"
DO $DO$
DECLARE v_store_id INT;
 DECLARE v_sql TEXT;
BEGIN
  for v_store_id in (select id from organ where org_type = '3') loop

  delete from hq_device_button_config where store_id = v_store_id and business_id = '5737';
  
  insert into hq_device_button_config (tenancy_id, id, store_id, type, business_id, display_order)
  values (tenancy_id(), nextval('hq_device_button_config_id_seq'), v_store_id, 2, '5737', 13);

  raise notice '%', v_store_id;
  end loop;
END$DO$;
---1.19.0_0829 -end------------------

---1.19.4_0917 -------------------
update sys_dictionary set valid_state='1' where class_identifier_code = 'deliver_type' AND class_item_code = 'xf';
update sys_dictionary set valid_state='1' where class_identifier_code = 'deliver_type' AND class_item_code = 'fn';

--三方配送环境接口路径系统参数
INSERT INTO sys_parameter (tenancy_id,store_id,system_name,model_name,para_name,para_code,para_value,para_defaut,para_type,valid_state,values_name,para_remark) 
SELECT tenancy_id (),0,'POS','系统参数','三方配送环境路径','PSHJJKLJ','0','0','常规','1','0:正式环境；1:测试环境',NULL
WHERE NOT EXISTS (SELECT 1 FROM sys_parameter WHERE para_code = 'PSHJJKLJ' AND store_id = 0);

---1.19.4_0917 -end------------------

---1.19.5_0918 -end------------------
--是否启用kvs设置,默认为不启用
--2018-09-18 肖恒
update sys_parameter set para_value='0',para_name='是否启用KVS设置' where para_code='SFQDKVS'; 

---1.19.5_0918 -end------------------

---1.19.6_1009 -end------------------
--启用KVS设置时是否打印厨打,默认为打印
--2018-10-09 肖恒
INSERT INTO sys_parameter (tenancy_id,store_id,system_name,model_name,para_name,para_code,para_value,para_defaut,para_type,valid_state,values_name,para_remark) 
SELECT tenancy_id (),0,'POS','KVS设置','启用KVS设置时是否打印厨打','IS_KVS_PRINT_DISH','1','1','常规','1','0:否；1:是',NULL
WHERE NOT EXISTS (SELECT 1 FROM sys_parameter WHERE para_code = 'IS_KVS_PRINT_DISH' AND store_id = 0);

---1.19.6_1009 -end------------------

---付款方式设置(合作里加入工商银行pos刷卡的支付方式)
DELETE FROM sys_dictionary WHERE class_identifier_code='third_pay' AND class_item_code='icbc_bank_pay';
INSERT INTO sys_dictionary (tenancy_id, model_name, application_model, class_identifier, class_identifier_code, class_item, class_item_code, is_sys, remark, valid_state, last_operator, last_updatetime, fake_id)
VALUES (tenancy_id(), 'hq', '付款方式', '第三方支付', 'third_pay', '工商银行支付', 'icbc_bank_pay', 'Y', '系统内部使用', '1', 'system', '2018-08-24 00:00:00', null);

--------------------hq_upgrade_v1.20.0_0913(build-********)--------------------

--- 调单/暂存二合一为暂存/调出  (暂不执行)
delete from sys_modules where module_link_url='5706';
INSERT INTO sys_modules (id, module_name, module_type, create_date, create_person, states, module_level, father_module_id, module_link_url, module_use_img, module_class, if_super_module, if_ui_display, shortcuts,version)
VALUES ('805706', '暂存/调出', 'pos', '2018-07-05', 'admin', '1', '4', '800020', '5706', '', NULL, NULL, '1', NULL,'1.0');
delete from sys_modules where module_link_url='5707';

-- 菜品口味表增加到基础资料下发列表中
DELETE FROM hq_data_delivery_base_info WHERE table_name='hq_item_taste_details';
INSERT INTO "hq_data_delivery_base_info" ( "tenancy_id", "module_code", "father_id", "node_code", "node_name", "table_name", "with_organ", "add_where", "remark", "create_time", "create_operator", "update_time", "update_operator", "valid_state", "delete_state", "info_type", "content") VALUES ( 'tzxstar', 'organ_management', '34', '2010020', '菜品口味表', 'hq_item_taste_details', '0', '', '', '2018-07-02 16:42:36', '', '2018-07-02 16:42:18', '', '1', '0', '1', '');

--- 是否启用外卖班次start---
DELETE FROM sys_parameter WHERE para_code='enable_wm_shift';
INSERT INTO sys_parameter (tenancy_id,  store_id, system_name, model_name, para_name, para_code, para_value, para_defaut, para_type, valid_state, values_name, para_remark)
VALUES (tenancy_id(), 0, 'POS', '系统参数', '是否启用外卖统一班次', 'enable_wm_shift', '0', '0', '常规', '1', '0:关闭;1:启用;', '');
--- 是否启用外卖班次end---

----增加外卖取消、退款拒绝原因start------
INSERT INTO "sys_dictionary" ("tenancy_id","model_name", "application_model", "class_identifier", "class_identifier_code", "class_item", "class_item_code", "is_sys", "remark", "valid_state", "last_operator", "last_updatetime", "fake_id") 
VALUES (tenancy_id(), 'hq', '异常原因', '异常类型', 'unusual_type', '外卖退款', 'TK08', 'Y', '系统内部使用', '1', 'admin', now(), NULL);

INSERT INTO "hq_unusual_reason" ("id","tenancy_id", "reason_code", "unusual_type", "father_id", "reason_name", "phonetic_code", "five_code", "remark", "valid_state", "last_operator", "last_updatetime", "third_code", "is_sys", "fake_id", "fake_type") VALUES (1007,tenancy_id(), '24', 'TK08', '0', '取消退款原因', 'qxtkyy', 'bivfdl', '', '1', 'admin', '2018-07-03 09:56:37', '', '0', NULL, NULL);
INSERT INTO "hq_unusual_reason" ("id","tenancy_id", "reason_code", "unusual_type", "father_id", "reason_name", "phonetic_code", "five_code", "remark", "valid_state", "last_operator", "last_updatetime", "third_code", "is_sys", "fake_id", "fake_type") VALUES (1008,tenancy_id(), '25', 'TK08', '0', '拒绝退款原因', 'jjtkyy', 'rxvfdl', '', '1', 'admin', '2018-07-03 09:56:55', '', '0', NULL, NULL);

INSERT INTO "hq_unusual_reason" ("tenancy_id", "reason_code", "unusual_type", "father_id", "reason_name", "phonetic_code", "five_code", "remark", "valid_state", "last_operator", "last_updatetime", "third_code", "is_sys", "fake_id", "fake_type") VALUES (tenancy_id(), '2401', 'TK08', '1007', '店铺太忙', 'dptm', 'yqdn', '', '1', 'admin', '2018-07-03 09:57:34', '', '0', NULL, NULL);
INSERT INTO "hq_unusual_reason" ("tenancy_id", "reason_code", "unusual_type", "father_id", "reason_name", "phonetic_code", "five_code", "remark", "valid_state", "last_operator", "last_updatetime", "third_code", "is_sys", "fake_id", "fake_type") VALUES (tenancy_id(), '2402', 'TK08', '1007', '商品已售完', 'spysw', 'uknwp', '', '1', 'admin', '2018-07-03 09:57:59', '', '0', NULL, NULL);
INSERT INTO "hq_unusual_reason" ("tenancy_id", "reason_code", "unusual_type", "father_id", "reason_name", "phonetic_code", "five_code", "remark", "valid_state", "last_operator", "last_updatetime", "third_code", "is_sys", "fake_id", "fake_type") VALUES (tenancy_id(), '2501', 'TK08', '1008', '已和用户电话沟通', 'yhyhdhgt', 'nteyjyic', '', '1', 'admin', '2018-07-03 09:58:29', '', '0', NULL, NULL);
INSERT INTO "hq_unusual_reason" ("tenancy_id", "reason_code", "unusual_type", "father_id", "reason_name", "phonetic_code", "five_code", "remark", "valid_state", "last_operator", "last_updatetime", "third_code", "is_sys", "fake_id", "fake_type") VALUES (tenancy_id(), '2403', 'TK08', '1007', '地址无法配送', 'dzwfps', 'fffisu', '', '1', 'admin', '2018-07-03 10:02:14', '', '0', NULL, NULL);
INSERT INTO "hq_unusual_reason" ("tenancy_id", "reason_code", "unusual_type", "father_id", "reason_name", "phonetic_code", "five_code", "remark", "valid_state", "last_operator", "last_updatetime", "third_code", "is_sys", "fake_id", "fake_type") VALUES (tenancy_id(), '2404', 'TK08', '1007', '店铺已打烊', 'dpydy', 'yqnro', '', '1', 'admin', '2018-07-03 10:02:35', '', '0', NULL, NULL);
INSERT INTO "hq_unusual_reason" ("tenancy_id", "reason_code", "unusual_type", "father_id", "reason_name", "phonetic_code", "five_code", "remark", "valid_state", "last_operator", "last_updatetime", "third_code", "is_sys", "fake_id", "fake_type") VALUES (tenancy_id(), '2405', 'TK08', '1007', '联系不上用户', 'lxbsyh', 'btghey', '', '1', 'admin', '2018-07-03 10:02:54', '', '0', NULL, NULL);
INSERT INTO "hq_unusual_reason" ("tenancy_id", "reason_code", "unusual_type", "father_id", "reason_name", "phonetic_code", "five_code", "remark", "valid_state", "last_operator", "last_updatetime", "third_code", "is_sys", "fake_id", "fake_type") VALUES (tenancy_id(), '2503', 'TK08', '1008', '用户已收餐', 'yhysc', 'eynnh', '', '1', 'admin', '2018-07-03 10:03:51', '', '0', NULL, NULL);
INSERT INTO "hq_unusual_reason" ("tenancy_id", "reason_code", "unusual_type", "father_id", "reason_name", "phonetic_code", "five_code", "remark", "valid_state", "last_operator", "last_updatetime", "third_code", "is_sys", "fake_id", "fake_type") VALUES (tenancy_id(), '2502', 'TK08', '1008', '菜已做正在配送中', 'cyzzzpsz', 'anwgdsuk', '', '1', 'admin', '2018-07-03 10:03:28', '', '0', NULL, NULL);
----增加外卖取消、退款拒绝原因end------

---系统参数:更新不允许操作退单操作渠道 start-----
update sys_parameter set para_value='DP07,BD06,MT08,YC09,EL09,MT11,WM10', para_defaut='DP07,BD06,MT08,YC09,EL09,MT11,WM10' where para_code ='BYXCZZDLYQD';
---系统参数:更新不允许操作退单操作渠道 end-----

--------------------hq_upgrade_v1.20.0_0913(build-********)--------------------

--------------------hq_upgrade_v1.20.3_1102(build-20181102)--------------------
--微信支付/支付宝支付扫码合一
--2018-11-02 肖恒
INSERT INTO sys_parameter (tenancy_id,store_id,system_name,model_name,para_name,para_code,para_value,para_defaut,para_type,valid_state,values_name,para_remark) 
SELECT tenancy_id (),0,'POS','第三方支付','微信付款码校验规则','WECHAT_PAY_CODE_CHECK_RULE','^(10|11|12|13|14|15)[0-9]{16}$','','初始化','1','正则表达式',NULL
WHERE NOT EXISTS (SELECT 1 FROM sys_parameter WHERE para_code = 'WECHAT_PAY_CODE_CHECK_RULE' AND store_id = 0);	

INSERT INTO sys_parameter (tenancy_id,store_id,system_name,model_name,para_name,para_code,para_value,para_defaut,para_type,valid_state,values_name,para_remark) 
SELECT tenancy_id (),0,'POS','第三方支付','支付宝付款码校验规则','ALI_PAY_CODE_CHECK_RULE','^(25|26|27|28|29|30)[0-9]{14,22}$','','初始化','1','正则表达式',NULL
WHERE NOT EXISTS (SELECT 1 FROM sys_parameter WHERE para_code = 'ALI_PAY_CODE_CHECK_RULE' AND store_id = 0);	

INSERT INTO sys_dictionary (tenancy_id, model_name, application_model, class_identifier, class_identifier_code, class_item, class_item_code, is_sys, remark, valid_state, last_operator, last_updatetime, fake_id) 
SELECT tenancy_id(), 'hq', '付款方式', '第三方支付', 'third_pay', '扫码付', 'scan_code_pay', 'Y', '系统内部使用', '1', 'system', '2018-11-01 00:00:00', null
WHERE NOT EXISTS (SELECT 1 FROM sys_dictionary WHERE class_identifier_code='third_pay' AND class_item_code='scan_code_pay');
--------------------hq_upgrade_v1.20.3_1102(build-20181102)--------------------

---菜品辅助数量start---
DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_item' and column_name='item_assist_num';
IF num = 0 THEN
		EXECUTE ' ALTER TABLE pos_bill_item ADD COLUMN item_assist_num numeric(19,4);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;
COMMENT ON COLUMN pos_bill_item.item_assist_num IS '菜品辅助数量';

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_item2' and column_name='item_assist_num';
IF num = 0 THEN
		EXECUTE ' ALTER TABLE pos_bill_item2 ADD COLUMN item_assist_num numeric(19,4);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;
COMMENT ON COLUMN pos_bill_item2.item_assist_num IS '菜品辅助数量';

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_item_regain' and column_name='item_assist_num';
IF num = 0 THEN
		EXECUTE ' ALTER TABLE pos_bill_item_regain ADD COLUMN item_assist_num numeric(19,4);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;
COMMENT ON COLUMN pos_bill_item_regain.item_assist_num IS '菜品辅助数量';
---菜品辅助数量end---


---单品转台记录原桌位start---
DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_item' and column_name='original_table';
IF num = 0 THEN
		EXECUTE ' ALTER TABLE pos_bill_item ADD COLUMN original_table varchar(50);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_item2' and column_name='original_table';
IF num = 0 THEN
		EXECUTE ' ALTER TABLE pos_bill_item2 ADD COLUMN original_table varchar(50);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
		select count(*) into num from information_schema.columns where table_name = 'pos_bill_item_regain' and column_name='original_table';
IF num = 0 THEN
		EXECUTE ' ALTER TABLE pos_bill_item_regain ADD COLUMN original_table varchar(50);';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END$DO$;
---单品转台记录原桌位end---

--kvs是否自动销单（系统参数）start--
DO $DO$
DECLARE num INT;
BEGIN
    select count(*) into num from sys_parameter p where para_code = 'kvs_bill_auto_complete' and p.tenancy_id = tenancy_id() and p.store_id = 0;
IF num = 0 THEN
    EXECUTE ' INSERT INTO sys_parameter (tenancy_id,  store_id, system_name, model_name, para_name, para_code, para_value, para_defaut, para_type, valid_state, values_name, para_remark)
VALUES (tenancy_id(), 0, ''POS'', ''系统参数'', ''kvs自动销单'', ''kvs_bill_auto_complete'', ''0'', ''0'', ''常规'', ''1'', ''0禁用，1启动'', '''');';
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;
--kvs是否自动销单（系统参数）end--


--- 外卖隔日退款方案（原单不修改，隔日冲减单标识is_cross_day =cross_day ）隔日退 增加字段
DO $DO$
DECLARE num INT;
BEGIN
   select count(1) into num from information_schema.columns where table_name = 'pos_bill' and column_name='is_cross_day';
   IF num = 0 THEN
      EXECUTE 'ALTER TABLE pos_bill ADD COLUMN is_cross_day varchar(50) ;';
   ELSE RAISE NOTICE 'column already exists in this table';
   END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
   select count(1) into num from information_schema.columns where table_name = 'pos_bill2' and column_name='is_cross_day';
   IF num = 0 THEN
      EXECUTE 'ALTER TABLE pos_bill2 ADD COLUMN is_cross_day varchar(50) ;';
   ELSE RAISE NOTICE 'column already exists in this table';
   END IF;
END$DO$;


DO $DO$
DECLARE num INT;
BEGIN
   select count(1) into num from information_schema.columns where table_name = 'pos_bill_item' and column_name='is_cross_day';
   IF num = 0 THEN
      EXECUTE 'ALTER TABLE pos_bill_item ADD COLUMN is_cross_day varchar(50) ;';
   ELSE RAISE NOTICE 'column already exists in this table';
   END IF;
END$DO$;

DO $DO$
DECLARE num INT;
BEGIN
   select count(1) into num from information_schema.columns where table_name = 'pos_bill_item2' and column_name='is_cross_day';
   IF num = 0 THEN
      EXECUTE 'ALTER TABLE pos_bill_item2 ADD COLUMN is_cross_day varchar(50) ;';
   ELSE RAISE NOTICE 'column already exists in this table';
   END IF;
END$DO$;
--- 外卖隔日退款方案（原单不修改，隔日冲减单标识is_cross_day =cross_day ）隔日退 增加字段
--------------------hq_upgrade_v1.21.0_1015(build-20181015)--------------------

INSERT INTO sys_modules (id, module_name, module_type, create_date, create_person, states, module_level, father_module_id, module_link_url, module_use_img, module_class, if_super_module, if_ui_display, shortcuts, version) 
SELECT '803025', '异常退款查询', 'pos', '2018-11-16', 'admin', '1', '4', '800100', '3025', '', NULL, NULL, '1', NULL, '1.0'  
WHERE NOT EXISTS (select 1 from sys_modules where module_link_url = '3025' ); 

--------------------hq_upgrade_v1.22.0_1127(build-20181127)--------------------

---E待客充值交款 start---
---2018-12-11 肖恒
----
INSERT INTO sys_dictionary (tenancy_id, model_name, application_model, class_identifier, class_identifier_code, class_item, class_item_code, is_sys, remark, valid_state, last_operator, last_updatetime, fake_id) 
SELECT tenancy_id(), 'pos', '交班', '会员结算类型', 'customer_operat_details', '发卡售卡金额', 'SK01', 'Y', '系统内部使用', '1', 'system', '2018-12-12 00:00:00', null
WHERE NOT EXISTS (SELECT 1 FROM sys_dictionary WHERE class_identifier_code='customer_operat_details' AND class_item_code='SK01');
INSERT INTO sys_dictionary (tenancy_id, model_name, application_model, class_identifier, class_identifier_code, class_item, class_item_code, is_sys, remark, valid_state, last_operator, last_updatetime, fake_id) 
SELECT tenancy_id(), 'pos', '交班', '会员结算类型', 'customer_operat_details', '发卡押金金额', 'YJ02', 'Y', '系统内部使用', '1', 'system', '2018-12-12 00:00:00', null
WHERE NOT EXISTS (SELECT 1 FROM sys_dictionary WHERE class_identifier_code='customer_operat_details' AND class_item_code='YJ02');
INSERT INTO sys_dictionary (tenancy_id, model_name, application_model, class_identifier, class_identifier_code, class_item, class_item_code, is_sys, remark, valid_state, last_operator, last_updatetime, fake_id) 
SELECT tenancy_id(), 'pos', '交班', '会员结算类型', 'customer_operat_details', '账单消费', 'YZD03', 'Y', '系统内部使用', '1', 'system', '2018-12-12 00:00:00', null
WHERE NOT EXISTS (SELECT 1 FROM sys_dictionary WHERE class_identifier_code='customer_operat_details' AND class_item_code='YZD03');
INSERT INTO sys_dictionary (tenancy_id, model_name, application_model, class_identifier, class_identifier_code, class_item, class_item_code, is_sys, remark, valid_state, last_operator, last_updatetime, fake_id) 
SELECT tenancy_id(), 'pos', '交班', '会员结算类型', 'customer_operat_details', '会员消费', 'WZD04', 'Y', '系统内部使用', '1', 'system', '2018-12-12 00:00:00', null
WHERE NOT EXISTS (SELECT 1 FROM sys_dictionary WHERE class_identifier_code='customer_operat_details' AND class_item_code='WZD04');
INSERT INTO sys_dictionary (tenancy_id, model_name, application_model, class_identifier, class_identifier_code, class_item, class_item_code, is_sys, remark, valid_state, last_operator, last_updatetime, fake_id) 
SELECT tenancy_id(), 'pos', '交班', '会员结算类型', 'customer_operat_details', '退卡金额', 'TK05', 'Y', '系统内部使用', '1', 'system', '2018-12-12 00:00:00', null
WHERE NOT EXISTS (SELECT 1 FROM sys_dictionary WHERE class_identifier_code='customer_operat_details' AND class_item_code='TK05');
INSERT INTO sys_dictionary (tenancy_id, model_name, application_model, class_identifier, class_identifier_code, class_item, class_item_code, is_sys, remark, valid_state, last_operator, last_updatetime, fake_id) 
SELECT tenancy_id(), 'pos', '交班', '会员结算类型', 'customer_operat_details', '退卡押金金额', 'TYJ06', 'Y', '系统内部使用', '1', 'system', '2018-12-12 00:00:00', null
WHERE NOT EXISTS (SELECT 1 FROM sys_dictionary WHERE class_identifier_code='customer_operat_details' AND class_item_code='TYJ06');
INSERT INTO sys_dictionary (tenancy_id, model_name, application_model, class_identifier, class_identifier_code, class_item, class_item_code, is_sys, remark, valid_state, last_operator, last_updatetime, fake_id) 
SELECT tenancy_id(), 'pos', '交班', '会员结算类型', 'customer_operat_details', '充值', 'CZ07', 'Y', '系统内部使用', '1', 'system', '2018-12-12 00:00:00', null
WHERE NOT EXISTS (SELECT 1 FROM sys_dictionary WHERE class_identifier_code='customer_operat_details' AND class_item_code='CZ07');
INSERT INTO sys_dictionary (tenancy_id, model_name, application_model, class_identifier, class_identifier_code, class_item, class_item_code, is_sys, remark, valid_state, last_operator, last_updatetime, fake_id) 
SELECT tenancy_id(), 'pos', '交班', '会员结算类型', 'customer_operat_details', '购买会籍', 'GMHJ08', 'Y', '系统内部使用', '1', 'system', '2018-12-12 00:00:00', null
WHERE NOT EXISTS (SELECT 1 FROM sys_dictionary WHERE class_identifier_code='customer_operat_details' AND class_item_code='GMHJ08');
----
--交班会员统计表新增付款方式字段
select p_addfield('pos_opter_changshift_customer','payment_id','int4');

--交款添加服务类型
select p_addfield('pos_cashier_receive_log','service_type','varchar(16)');

select p_addfield('pos_cashier_receive_log_details','payment_id','int4');
select p_addfield('pos_cashier_receive_log_details','service_type','varchar(16)');
select p_addfield('pos_cashier_receive_log_details','shift_id','int4');
select p_addfield('pos_cashier_receive_log_details','pay_shift_id','int4');
select p_addfield('pos_cashier_receive_log_details','report_date','date');

ALTER TABLE pos_cashier_receive_log_details ALTER COLUMN receive_id TYPE CHARACTER VARYING(32);
---E待客充值交款 end---

---E待客工商银联POS start---
INSERT INTO sys_dictionary (tenancy_id, model_name, application_model, class_identifier, class_identifier_code, class_item, class_item_code, is_sys, remark, valid_state, last_operator, last_updatetime, fake_id) 
SELECT tenancy_id(), 'hq', '付款方式', '银联机具', 'third_pay', '工商银联POS', 'unionpay_pos_icbc', 'Y', '系统内部使用', '1', 'system', '2018-12-04 00:00:00', null
WHERE NOT EXISTS (SELECT 1 FROM sys_dictionary WHERE class_identifier_code='third_pay' AND class_item_code='unionpay_pos_icbc');
---E待客工商银联POS end---

---火锅业态0315 start----------------------
---火锅业态0315 start----------------------
--- 冰柜设置相关表start---

CREATE TABLE IF NOT EXISTS "public"."pos_print_bgclass" (
"id" varchar(32) COLLATE "default" NOT NULL,
"bgcode" varchar(255) COLLATE "default",
"bgname" varchar(255) COLLATE "default",
"bgorder" int4,
"tenancy_id" varchar COLLATE "default",
"store_id" int4,
"printer_id" int4,
"printer_address" varchar(255) COLLATE "default",
"printer_name" varchar(255) COLLATE "default",
CONSTRAINT "pos_print_bgclass_pkey" PRIMARY KEY ("id")
)
WITH (OIDS=FALSE) ;

CREATE TABLE IF NOT EXISTS "public"."pos_print_bgclass_item" (
"id" varchar(32) COLLATE "default" NOT NULL,
"bgclassid" varchar(32) COLLATE "default",
"item_id" int4,
"tenancy_id" varchar COLLATE "default",
"store_id" int4,
"item_order" int4,
"item_name" varchar(255) COLLATE "default",
"printer_id" int4,
"printer_name" varchar(255) COLLATE "default",
CONSTRAINT "pos_print_bgclass_item_pkey" PRIMARY KEY ("id")
)
WITH (OIDS=FALSE) ;

--- 冰柜设置相关表end---

--- 菜品类别添加火锅属性和是否必点字段 start ---
select p_addfield('hq_item_class','hotpot_property','varchar(255)');
select p_addfield('hq_item_class','is_must_dish','varchar(10)');
COMMENT ON COLUMN "public"."hq_item_class"."hotpot_property" IS '火锅属性（bigpot:大锅,smallpot:小锅,condiment:底料）';
COMMENT ON COLUMN "public"."hq_item_class"."is_must_dish" IS '是否必点(Y:是,N:否)';
--- 菜品类别添加火锅属性和是否必点字段 end ---

--- 火锅桌数/人数start--
select p_addfield('pos_bill','hotpot_table_num','int4');
select p_addfield('pos_bill','hotpot_guest_num','int4');
select p_addfield('pos_bill2','hotpot_table_num','int4');
select p_addfield('pos_bill2','hotpot_guest_num','int4');
--- 火锅桌数/人数end--
---火锅业态0315 start----------------------


---------豪享来业务start-------------------

--- 增加门店管理权限start-------------  
delete FROM sys_modules WHERE id='802012' AND module_name = '门店管理' AND module_link_url = '2012' ;
INSERT INTO sys_modules (id, module_name, module_type, create_date, create_person, states, module_level, father_module_id, module_link_url, module_use_img, module_class, if_super_module, if_ui_display, shortcuts)
SELECT '802012', '门店管理', 'pos', '2019-05-16', 'admin', '1', '3', '800016', '2012', '', NULL, NULL, '', NULL
WHERE NOT EXISTS ( SELECT 1 FROM sys_modules WHERE id='802012' AND module_name = '门店管理' AND module_link_url = '2012');

INSERT INTO sys_modules (id, module_name, module_type, create_date, create_person, states, module_level, father_module_id, module_link_url, module_use_img, module_class, if_super_module, if_ui_display, shortcuts)
SELECT '805801', '套餐维护', 'pos', '2019-05-16', 'admin', '1', '4', '802012', '5801', '', NULL, NULL, '', NULL
WHERE NOT EXISTS ( SELECT 1 FROM sys_modules WHERE id='805801' AND module_name = '套餐维护' AND module_link_url = '5801');

INSERT INTO sys_modules (id, module_name, module_type, create_date, create_person, states, module_level, father_module_id, module_link_url, module_use_img, module_class, if_super_module, if_ui_display, shortcuts)
SELECT '805802', '菜品禁用', 'pos', '2019-05-16', 'admin', '1', '4', '802012', '5802', '', NULL, NULL, '', NULL
WHERE NOT EXISTS ( SELECT 1 FROM sys_modules WHERE id='805802' AND module_name = '菜品禁用' AND module_link_url = '5802');

INSERT INTO sys_modules (id, module_name, module_type, create_date, create_person, states, module_level, father_module_id, module_link_url, module_use_img, module_class, if_super_module, if_ui_display, shortcuts)
SELECT '805803', '员工维护', 'pos', '2019-05-16', 'admin', '1', '4', '802012', '5803', '', NULL, NULL, '', NULL
WHERE NOT EXISTS ( SELECT 1 FROM sys_modules WHERE id='805803' AND module_name = '员工维护' AND module_link_url = '5803');

--系统设置
 INSERT INTO sys_modules (id, module_name, module_type, create_date, create_person, states, module_level, father_module_id, module_link_url, module_use_img, module_class, if_super_module, if_ui_display, shortcuts, version)
SELECT '804020', '系统设置', 'pos', '2019-05-24', 'admin', '1', '3', '800016', '4020', '', NULL, NULL, '1', NULL, '1.0'
WHERE NOT EXISTS (select 1 from sys_modules where module_link_url = '4020' );

--补单不出单
 INSERT INTO sys_modules (id, module_name, module_type, create_date, create_person, states, module_level, father_module_id, module_link_url, module_use_img, module_class, if_super_module, if_ui_display, shortcuts, version)
SELECT '805334', '补单不出单', 'pos', '2019-05-24', 'admin', '1', '4', '800019', '5334', '', NULL, NULL, '1', NULL, '1.0'
WHERE NOT EXISTS (select 1 from sys_modules where module_link_url = '5334' );


--- 增加门店管理权限start-------------

---并台记录原账单号start--------
select p_addfield('pos_bill','sbill_num','varchar(200)');
select p_addfield('pos_bill2','sbill_num','varchar(200)');
alter table "pos_bill" alter  COLUMN  sbill_num  type varchar(200) ;
alter table "pos_bill2" alter  COLUMN  sbill_num  type varchar(200) ;
---并台记录原账单号end--------

---并台记录是否为并台之后操作菜品 加菜、赠送、取消赠送、单品转入、转出、转台 等操作start--------
select p_addfield('pos_bill','is_combined_after','varchar(20)');
select p_addfield('pos_bill2','is_combined_after','varchar(20)');
---并台记录是否为并台之后操作菜品 加菜、赠送、取消赠送、单品转入、转出、转台 等操作end--------


--------修改字段大小start-----------
alter table "pos_table_log" alter  COLUMN  from_bill_num  type varchar(200) ;
alter table "pos_table_log" alter  COLUMN  to_bill_num  type varchar(200) ;
alter table "pos_table_log" alter  COLUMN  from_table_code  type varchar(200) ;
alter table "pos_table_log" alter  COLUMN  to_table_code  type varchar(200) ;
--------修改字段大小end-----------

---- crm_card_trading_list 表增加pos_num 字段 start----
select p_addfield('crm_card_trading_list','pos_num','character varying(32)');
---- crm_card_trading_list 表增加pos_num 字段 start----


--修改系统参数  POS点餐是否支持开具电子发票 为 POS点餐是否支持开具发票
update sys_parameter set values_name ='0:表示不支持，1:表示支持电子发票，2:表示支持电子发票(包括快速结账)，3：表示支持手动发票(包括快速结账)' ,para_name='POS点餐是否支持开具发票' where para_code='dzfp_posdcsfkjdzfp';

---------豪享来业务end-------------------

---添加微生活小程序菜品备注同步方式参数
---2019-08-02 肖恒
INSERT INTO sys_parameter (tenancy_id, store_id, system_name, model_name, para_name, para_code, para_value, para_defaut, para_type, valid_state, values_name, para_remark)
SELECT tenancy_id(), 0, 'POS', '微生活点餐平台对接', '菜品备注同步方式', 'WLIFE_SYNC_MEMO_TYPE', '1', '1', '常规', '1', '1:全部同步;2:按菜品同步;', null
WHERE NOT EXISTS ( SELECT 1 FROM sys_parameter WHERE system_name='POS' AND para_code='WLIFE_SYNC_MEMO_TYPE');

DELETE FROM sys_parameter_detail WHERE para_code='WLIFE_SYNC_MEMO_TYPE';
INSERT INTO sys_parameter_detail (tenancy_id,para_code, para_value, para_value_code)
VALUES( tenancy_id(),'WLIFE_SYNC_MEMO_TYPE', '全部同步', '1' );
INSERT INTO sys_parameter_detail (tenancy_id,para_code, para_value, para_value_code)
VALUES( tenancy_id(),'WLIFE_SYNC_MEMO_TYPE', '按菜品同步', '2' );
---添加微生活小程序菜品备注同步方式参数 end---

--- 会员操作记录会员操作日志 start---
-- 2019-09-27
-- 张来刚

--新增日志记录表
CREATE TABLE "public"."customer_operation_logs" (
"tenancy_id" varchar(32) COLLATE "default",
"id" serial,
"store_id" int4,
"customer_id" int4,
"customer_name" varchar(20) COLLATE "default",
"mobile" varchar(20) COLLATE "default",
"pos_num" varchar(10) COLLATE "default",
"operation_id" varchar(20) COLLATE "default",
"operation_name" varchar(20) COLLATE "default",
"title" varchar(100) COLLATE "default",
"content" varchar(500) COLLATE "default",
"report_date" date,
"operation_time" timestamp(6) DEFAULT now(),
"operation_type" varchar(20),
"operation_type_name" varchar(20) COLLATE "default",
"remark" varchar(500) COLLATE "default",
"card_code" varchar(50) COLLATE "default",
"shift_id" int4,
"upload_tag" int4 DEFAULT 0,
"old_content" varchar(500) COLLATE "default",
CONSTRAINT "pk_customer_operation_logs" PRIMARY KEY ("id")
)
WITH (OIDS=FALSE)
;

-- 修改上传用表参数
UPDATE sys_parameter sp SET para_value=para_value || ',customer_operation_logs' WHERE (sp.para_code='uploaddatatable' OR sp.para_code='autouploaddatatable')
and not EXISTS (select id FROM sys_parameter where para_code = sp.para_code AND store_id = sp.store_id and para_value like '%customer_operation_logs%');

-- 总部执行菜单
INSERT INTO "public"."sys_modules" ("id","module_name","module_type","create_date","create_person","states","module_level","father_module_id","module_link_url","module_use_img","module_class","if_super_module","if_ui_display","shortcuts","order_num","version","new_url","button_sign")
SELECT '107005004007','会员操作日志查询','RPT',now(),'admin','1','4','107005004','/reportpages/reportcenter/crmreport/basicsettings/customeroperationlog.jsp',NULL,'页面',NULL,1,NULL,6,'2.0',NULL,NULL
WHERE NOT EXISTS (select id FROM sys_modules where id=107005004007) LIMIT 1;

-- 微生活会员转卡
INSERT INTO sys_modules (id, module_name, module_type, create_date, create_person, states, module_level, father_module_id, module_link_url, module_use_img, module_class, if_super_module, if_ui_display, shortcuts, version)
SELECT '804087', '微生活会员转卡', 'pos', '2020-05-02', 'admin', '1', '4', '800070', '4087', '', NULL, NULL, '1', NULL, '1.0'
WHERE NOT EXISTS (select 1 from sys_modules where module_link_url = '4087' );
-- 微生活会员信息修改
INSERT INTO sys_modules (id, module_name, module_type, create_date, create_person, states, module_level, father_module_id, module_link_url, module_use_img, module_class, if_super_module, if_ui_display, shortcuts, version)
SELECT '804088', '微生活会员信息修改', 'pos', '2020-05-06', 'admin', '1', '4', '800070', '4088', '', NULL, NULL, '1', NULL, '1.0'
WHERE NOT EXISTS (select 1 from sys_modules where module_link_url = '4088' );


--- 会员操作记录会员操作日志 end---

--- ECO对接增加丰食渠道 start---
--- 2020-06-15
--- 新增渠道 
INSERT INTO "sys_dictionary" ("tenancy_id","model_name", "application_model", "class_identifier", "class_identifier_code", "class_item", "class_item_code", "is_sys", "remark", "valid_state", "last_operator", "last_updatetime", "fake_id", "third_code", "third_name") select tenancy_id(), 'hq', '菜品类别表', '渠道', 'chanel', '丰食', 'FS25', 'Y', '系统内部使用', '1', 'admin', '2020-06-03 11:31:33', NULL, NULL, NULL WHERE not exists (select 1 from sys_dictionary where class_identifier_code = 'chanel' AND class_item_code = 'FS25');

INSERT INTO "sys_channel_setting" ("tenancy_id", "chanel_id", "start_state", "valid_state", "last_operator", "last_updatetime")
select tenancy_id(),(select id from sys_dictionary where class_identifier_code = 'chanel' AND class_item_code = 'FS25' ),'1','1','admin', '2020-06-07 17:00:00' ;

--- 新增外卖付款方式:
INSERT INTO sys_dictionary (tenancy_id, model_name, application_model, class_identifier, class_identifier_code, class_item, class_item_code, is_sys, remark, valid_state, last_operator, last_updatetime, fake_id)
SELECT tenancy_id(), 'hq', '付款方式', 'ECO外卖', 'third_pay', '丰食外卖付款', 'fengshi_pay', 'Y', '系统内部使用', '1', 'system', '2020-06-15 00:00:00', null
WHERE NOT EXISTS (SELECT 1 FROM sys_dictionary WHERE class_identifier_code ='third_pay' AND class_item_code='fengshi_pay'); 
--- ECO对接增加丰食渠道 end---

--- 微生活新建会员卡显示默认卡类别---
INSERT INTO sys_parameter (tenancy_id, store_id, system_name, model_name, para_name, para_code, para_value, para_defaut, para_type, valid_state, values_name, para_remark)
SELECT tenancy_id(), 0, 'POS', '系统参数', '微生活新建会员卡显示默认卡类别', 'WLIFE_ISSHOW_DEFAULTLEVEL', '0', '0', '常规', '1', '0:关闭;1:启用;', '关闭时显示所有类别；启用时显示默认类别'
WHERE NOT EXISTS ( SELECT 1 FROM sys_parameter WHERE system_name='POS' AND para_code='WLIFE_ISSHOW_DEFAULTLEVEL');
--- ECO 微生活新建会员卡显示默认卡类别 end---

--- 付款异常取消付款的限制时间---
INSERT INTO sys_parameter (tenancy_id, store_id, system_name, model_name, para_name, para_code, para_value, para_defaut, para_type, valid_state, values_name, para_remark)
SELECT tenancy_id(), 0, 'POS', '系统参数', '付款异常取消付款的限制时间', 'CANCEL_PAYMENT_LIMIT_TIME', '0', '0', '常规', '1', ',取值为正整数,取值为0时,不限制,单位:秒;', ''
WHERE NOT EXISTS ( SELECT 1 FROM sys_parameter WHERE system_name='POS' AND para_code='CANCEL_PAYMENT_LIMIT_TIME');
--- 付款异常取消付款的限制时间 end---





--天子星折扣、天子星优惠券、微生活会员支付(储值、积分、券)是否增加互斥判断 start--
DO $DO$
DECLARE num INT;
BEGIN
  select count(*) into num from sys_parameter p where para_code = 'IsCheckAceWilMutexCouponoOrDiscount' and p.tenancy_id = tenancy_id() and p.store_id = 0;
  IF num = 0 THEN
    INSERT INTO sys_parameter (tenancy_id,  store_id, system_name, model_name, para_name, para_code, para_value, para_defaut, para_type, valid_state, values_name, para_remark)
    VALUES (tenancy_id(), 0, 'POS', '系统参数', '天子星折扣、天子星优惠券、微生活会员支付(储值、积分、券)是否增加互斥判断', 'IsCheckAceWilMutexCouponoOrDiscount', 1, 1, '常规', '1', '1:是(默认值);0:否', '');
  ELSE RAISE NOTICE 'column  already exists in this table';
  END IF;
END $DO$;
--天子星折扣、天子星优惠券、微生活会员支付(储值、积分、券)是否增加互斥判断 end--

--随餐单打印存餐码 start--
DO $DO$
DECLARE num INT;
BEGIN
select count(*) into num from sys_parameter p where para_code = 'isPrintTakeCode' and p.tenancy_id = tenancy_id() and p.store_id = 0;
IF num = 0 THEN
    INSERT INTO sys_parameter (tenancy_id,  store_id, system_name, model_name, para_name, para_code, para_value, para_defaut, para_type, valid_state, values_name, para_remark)
    VALUES (tenancy_id(), 0, 'POS', '系统参数', '随餐单是否打印存餐码', 'isPrintTakeCode', 0, 0, '常规', '1', '0:否(默认值);1:是', '');
ELSE RAISE NOTICE 'column  already exists in this table';
END IF;
END $DO$;
--随餐单打印存餐码 end--

-- 门店报表权限
INSERT INTO sys_modules(id, module_name, module_type, create_date, create_person, states, module_level, father_module_id, module_link_url, module_use_img, module_class, if_super_module, if_ui_display, shortcuts, order_num, version, new_url, button_sign)
SELECT '804101', '门店报表权限', 'pos', '2020-11-23', 'admin', '1', 3, '800016', '4101', '', '', '', '1', NULL, 1, '1.0', NULL, NULL
WHERE NOT EXISTS ( SELECT 1 FROM sys_modules WHERE id='804101' AND module_name = '门店报表权限' AND module_link_url = '4101');

-- 交班汇总
INSERT INTO sys_modules(id, module_name, module_type, create_date, create_person, states, module_level, father_module_id, module_link_url, module_use_img, module_class, if_super_module, if_ui_display, shortcuts, order_num, version, new_url, button_sign)
SELECT '804102', '交班汇总显示', 'pos', '2020-11-23', 'admin', '1', 4, '804101', '4102', '', '', '', '1', NULL, 1, '1.0', NULL, NULL
WHERE NOT EXISTS ( SELECT 1 FROM sys_modules WHERE id='804102' AND module_name = '交班汇总显示' AND module_link_url = '4102');

INSERT INTO sys_modules(id, module_name, module_type, create_date, create_person, states, module_level, father_module_id, module_link_url, module_use_img, module_class, if_super_module, if_ui_display, shortcuts, order_num, version, new_url, button_sign)
SELECT '804103', '交班汇总查询', 'pos', '2020-11-23', 'admin', '1', 4, '804101', '4103', '', '', '', '1', NULL, 1, '1.0', NULL, NULL
WHERE NOT EXISTS ( SELECT 1 FROM sys_modules WHERE id='804103' AND module_name = '交班汇总查询' AND module_link_url = '4103');

INSERT INTO sys_modules(id, module_name, module_type, create_date, create_person, states, module_level, father_module_id, module_link_url, module_use_img, module_class, if_super_module, if_ui_display, shortcuts, order_num, version, new_url, button_sign)
SELECT '804104', '交班汇总打印', 'pos', '2020-11-23', 'admin', '1', 4, '804101', '4104', '', '', '', '1', NULL, 1, '1.0', NULL, NULL
WHERE NOT EXISTS ( SELECT 1 FROM sys_modules WHERE id='804104' AND module_name = '交班汇总打印' AND module_link_url = '4104');


-- 综合日报
INSERT INTO sys_modules(id, module_name, module_type, create_date, create_person, states, module_level, father_module_id, module_link_url, module_use_img, module_class, if_super_module, if_ui_display, shortcuts, order_num, version, new_url, button_sign)
SELECT '804105', '综合日报显示', 'pos', '2020-11-23', 'admin', '1', 4, '804101', '4105', '', '', '', '1', NULL, 1, '1.0', NULL, NULL
WHERE NOT EXISTS ( SELECT 1 FROM sys_modules WHERE id='804105' AND module_name = '综合日报显示' AND module_link_url = '4105');

INSERT INTO sys_modules(id, module_name, module_type, create_date, create_person, states, module_level, father_module_id, module_link_url, module_use_img, module_class, if_super_module, if_ui_display, shortcuts, order_num, version, new_url, button_sign)
SELECT '804106', '综合日报查询', 'pos', '2020-11-23', 'admin', '1', 4, '804101', '4106', '', '', '', '1', NULL, 1, '1.0', NULL, NULL
WHERE NOT EXISTS ( SELECT 1 FROM sys_modules WHERE id='804106' AND module_name = '综合日报查询' AND module_link_url = '4106');

INSERT INTO sys_modules(id, module_name, module_type, create_date, create_person, states, module_level, father_module_id, module_link_url, module_use_img, module_class, if_super_module, if_ui_display, shortcuts, order_num, version, new_url, button_sign)
SELECT '804107', '综合日报打印', 'pos', '2020-11-23', 'admin', '1', 4, '804101', '4107', '', '', '', '1', NULL, 1, '1.0', NULL, NULL
WHERE NOT EXISTS ( SELECT 1 FROM sys_modules WHERE id='804107' AND module_name = '综合日报打印' AND module_link_url = '4107');

-- 菜品销售汇总
INSERT INTO sys_modules(id, module_name, module_type, create_date, create_person, states, module_level, father_module_id, module_link_url, module_use_img, module_class, if_super_module, if_ui_display, shortcuts, order_num, version, new_url, button_sign)
SELECT '804108', '菜品销售汇总显示', 'pos', '2020-11-23', 'admin', '1', 4, '804101', '4108', '', '', '', '1', NULL, 1, '1.0', NULL, NULL
WHERE NOT EXISTS ( SELECT 1 FROM sys_modules WHERE id='804108' AND module_name = '菜品销售汇总显示' AND module_link_url = '4108');

INSERT INTO sys_modules(id, module_name, module_type, create_date, create_person, states, module_level, father_module_id, module_link_url, module_use_img, module_class, if_super_module, if_ui_display, shortcuts, order_num, version, new_url, button_sign)
SELECT '804109', '菜品销售汇总查询', 'pos', '2020-11-23', 'admin', '1', 4, '804101', '4109', '', '', '', '1', NULL, 1, '1.0', NULL, NULL
WHERE NOT EXISTS ( SELECT 1 FROM sys_modules WHERE id='804109' AND module_name = '菜品销售汇总查询' AND module_link_url = '4109');

INSERT INTO sys_modules(id, module_name, module_type, create_date, create_person, states, module_level, father_module_id, module_link_url, module_use_img, module_class, if_super_module, if_ui_display, shortcuts, order_num, version, new_url, button_sign)
SELECT '804110', '菜品销售汇总打印', 'pos', '2020-11-23', 'admin', '1', 4, '804101', '4110', '', '', '', '1', NULL, 1, '1.0', NULL, NULL
WHERE NOT EXISTS ( SELECT 1 FROM sys_modules WHERE id='804110' AND module_name = '菜品销售汇总打印' AND module_link_url = '4110');


-- 销售时段报表
INSERT INTO sys_modules(id, module_name, module_type, create_date, create_person, states, module_level, father_module_id, module_link_url, module_use_img, module_class, if_super_module, if_ui_display, shortcuts, order_num, version, new_url, button_sign)
SELECT '804111', '销售时段报表显示', 'pos', '2020-11-23', 'admin', '1', 4, '804101', '4111', '', '', '', '1', NULL, 1, '1.0', NULL, NULL
WHERE NOT EXISTS ( SELECT 1 FROM sys_modules WHERE id='804111' AND module_name = '销售时段报表显示' AND module_link_url = '4111');

INSERT INTO sys_modules(id, module_name, module_type, create_date, create_person, states, module_level, father_module_id, module_link_url, module_use_img, module_class, if_super_module, if_ui_display, shortcuts, order_num, version, new_url, button_sign)
SELECT '804112', '销售时段报表查询', 'pos', '2020-11-23', 'admin', '1', 4, '804101', '4112', '', '', '', '1', NULL, 1, '1.0', NULL, NULL
WHERE NOT EXISTS ( SELECT 1 FROM sys_modules WHERE id='804112' AND module_name = '销售时段报表查询' AND module_link_url = '4112');

INSERT INTO sys_modules(id, module_name, module_type, create_date, create_person, states, module_level, father_module_id, module_link_url, module_use_img, module_class, if_super_module, if_ui_display, shortcuts, order_num, version, new_url, button_sign)
SELECT '804113', '销售时段报表打印', 'pos', '2020-11-23', 'admin', '1', 4, '804101', '4113', '', '', '', '1', NULL, 1, '1.0', NULL, NULL
WHERE NOT EXISTS ( SELECT 1 FROM sys_modules WHERE id='804113' AND module_name = '销售时段报表打印' AND module_link_url = '4113');


---抖音券start--
INSERT INTO "public"."sys_dictionary" ("tenancy_id","model_name","application_model","class_identifier","class_identifier_code","class_item","class_item_code","is_sys","remark","valid_state","last_operator","last_updatetime","fake_id")
SELECT tenancy_id (),'hq','支付方式','第三方支付','third_pay','抖音券','douyin_coupons','Y','系统内部使用','1','system','2021-12-21 00:00:01',NULL
    WHERE NOT EXISTS (SELECT ID FROM sys_dictionary WHERE class_identifier_code='third_pay' AND class_item_code='douyin_coupons');

INSERT INTO "public"."sys_dictionary" ("tenancy_id","model_name","application_model","class_identifier","class_identifier_code","class_item","class_item_code","is_sys","remark","valid_state","last_operator","last_updatetime","fake_id")
SELECT tenancy_id (),'hq','支付方式','第三方支付','third_pay','抖音券虚收','douyin_coupons_phantom','Y','系统内部使用','1','system','2021-12-21 00:00:01',NULL
    WHERE NOT EXISTS (SELECT ID FROM sys_dictionary WHERE class_identifier_code='third_pay' AND class_item_code='douyin_coupons_phantom');

INSERT INTO sys_parameter (tenancy_id, store_id, system_name, model_name, para_name, para_code, para_value, para_defaut, para_type, valid_state, values_name, para_remark)
SELECT tenancy_id(), 0, 'POS', '系统参数', '抖音开放接口URL', 'DOUYIN_API_URL', 'https://open.douyin.com', '', '常规', '1', '', ''
    WHERE NOT EXISTS ( SELECT 1 FROM sys_parameter WHERE system_name='POS' AND para_code='DOUYIN_CLIENT_KEY');

INSERT INTO sys_parameter (tenancy_id, store_id, system_name, model_name, para_name, para_code, para_value, para_defaut, para_type, valid_state, values_name, para_remark)
SELECT tenancy_id(), 0, 'POS', '系统参数', '抖音应用唯一标识', 'DOUYIN_CLIENT_KEY', 'awbe35kzflcl03kx', '', '常规', '1', '', ''
    WHERE NOT EXISTS ( SELECT 1 FROM sys_parameter WHERE system_name='POS' AND para_code='DOUYIN_CLIENT_KEY');

INSERT INTO sys_parameter (tenancy_id, store_id, system_name, model_name, para_name, para_code, para_value, para_defaut, para_type, valid_state, values_name, para_remark)
SELECT tenancy_id(), 0, 'POS', '系统参数', '抖音应用唯一标识对应的密钥', 'DOUYIN_CLIENT_SECRET', '32d715403ded984201e5048c6596ec07', '', '常规', '1', '', ''
    WHERE NOT EXISTS ( SELECT 1 FROM sys_parameter WHERE system_name='POS' AND para_code='DOUYIN_CLIENT_SECRET');


INSERT INTO sys_parameter (tenancy_id, store_id, system_name, model_name, para_name, para_code, para_value, para_defaut, para_type, valid_state, values_name, para_remark)
SELECT tenancy_id(), 0, 'POS', '系统参数', '抖音门店id', 'DOUYIN_STORE_ID', '', '', '常规', '1', '', ''
    WHERE NOT EXISTS ( SELECT 1 FROM sys_parameter WHERE system_name='POS' AND para_code='DOUYIN_STORE_ID');
---抖音券end---

---外卖随餐单是否打印出餐一维码
INSERT INTO "sys_parameter"("tenancy_id", "store_id", "system_name", "model_name", "para_name", "para_code", "para_value", "para_defaut", "para_type", "valid_state", "values_name", "para_remark", "last_operator", "last_updatetime")
select tenancy_id(), 0, 'HQ', '打印相关', '外卖随餐单是否打印出餐一维码', 'print_dimensional_code', '1', '1', '常规', '1', '参数说明：0，否；1，是', '', 'admin', now()
    where not exists (select 1 from sys_parameter where para_code = 'print_dimensional_code') ;


---微生活三方券付款
INSERT INTO "public"."sys_dictionary" ("tenancy_id","model_name","application_model","class_identifier","class_identifier_code","class_item","class_item_code","is_sys","remark","valid_state","last_operator","last_updatetime","fake_id")
SELECT tenancy_id (),'hq','支付方式','第三方支付','third_pay','微生活美团券','wlife_meituan','Y','系统内部使用','1','system','2024-04-24 00:00:01',NULL
WHERE NOT EXISTS (SELECT ID FROM sys_dictionary WHERE class_identifier_code='third_pay' AND class_item_code='wlife_meituan');

INSERT INTO "public"."sys_dictionary" ("tenancy_id","model_name","application_model","class_identifier","class_identifier_code","class_item","class_item_code","is_sys","remark","valid_state","last_operator","last_updatetime","fake_id")
SELECT tenancy_id (),'hq','支付方式','第三方支付','third_pay','微生活抖音券','wlife_douyin','Y','系统内部使用','1','system','2024-04-24 00:00:01',NULL
WHERE NOT EXISTS (SELECT ID FROM sys_dictionary WHERE class_identifier_code='third_pay' AND class_item_code='wlife_douyin');

INSERT INTO "public"."sys_dictionary" ("tenancy_id","model_name","application_model","class_identifier","class_identifier_code","class_item","class_item_code","is_sys","remark","valid_state","last_operator","last_updatetime","fake_id")
SELECT tenancy_id (),'hq','支付方式','第三方支付','third_pay','微生活支付宝券','wlife_alipaycoupon','Y','系统内部使用','1','system','2024-04-24 00:00:01',NULL
WHERE NOT EXISTS (SELECT ID FROM sys_dictionary WHERE class_identifier_code='third_pay' AND class_item_code='wlife_alipaycoupon');

INSERT INTO "public"."sys_dictionary" ("tenancy_id","model_name","application_model","class_identifier","class_identifier_code","class_item","class_item_code","is_sys","remark","valid_state","last_operator","last_updatetime","fake_id")
SELECT tenancy_id (),'hq','支付方式','第三方支付','third_pay','微生活小红书券','wlife_little_red_book','Y','系统内部使用','1','system','2024-04-24 00:00:01',NULL
WHERE NOT EXISTS (SELECT ID FROM sys_dictionary WHERE class_identifier_code='third_pay' AND class_item_code='wlife_little_red_book');

---微生活三方券虚收付款
INSERT INTO "public"."sys_dictionary" ("tenancy_id","model_name","application_model","class_identifier","class_identifier_code","class_item","class_item_code","is_sys","remark","valid_state","last_operator","last_updatetime","fake_id")
SELECT tenancy_id (),'hq','支付方式','第三方支付','third_pay','微生活美团券虚收','wlife_meituan_phantom','Y','系统内部使用','1','system','2024-04-24 00:00:01',NULL
WHERE NOT EXISTS (SELECT ID FROM sys_dictionary WHERE class_identifier_code='third_pay' AND class_item_code='wlife_meituan_phantom');

INSERT INTO "public"."sys_dictionary" ("tenancy_id","model_name","application_model","class_identifier","class_identifier_code","class_item","class_item_code","is_sys","remark","valid_state","last_operator","last_updatetime","fake_id")
SELECT tenancy_id (),'hq','支付方式','第三方支付','third_pay','微生活抖音券虚收','wlife_douyin_phantom','Y','系统内部使用','1','system','2024-04-24 00:00:01',NULL
WHERE NOT EXISTS (SELECT ID FROM sys_dictionary WHERE class_identifier_code='third_pay' AND class_item_code='wlife_douyin_phantom');

INSERT INTO "public"."sys_dictionary" ("tenancy_id","model_name","application_model","class_identifier","class_identifier_code","class_item","class_item_code","is_sys","remark","valid_state","last_operator","last_updatetime","fake_id")
SELECT tenancy_id (),'hq','支付方式','第三方支付','third_pay','微生活支付宝券虚收','wlife_alipaycoupon_phantom','Y','系统内部使用','1','system','2024-04-24 00:00:01',NULL
WHERE NOT EXISTS (SELECT ID FROM sys_dictionary WHERE class_identifier_code='third_pay' AND class_item_code='wlife_alipaycoupon_phantom');

INSERT INTO "public"."sys_dictionary" ("tenancy_id","model_name","application_model","class_identifier","class_identifier_code","class_item","class_item_code","is_sys","remark","valid_state","last_operator","last_updatetime","fake_id")
SELECT tenancy_id (),'hq','支付方式','第三方支付','third_pay','微生活小红书券虚收','wlife_little_red_book_phantom','Y','系统内部使用','1','system','2024-04-24 00:00:01',NULL
WHERE NOT EXISTS (SELECT ID FROM sys_dictionary WHERE class_identifier_code='third_pay' AND class_item_code='wlife_little_red_book_phantom');




