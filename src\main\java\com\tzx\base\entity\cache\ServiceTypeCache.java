package com.tzx.base.entity.cache;

public class ServiceTypeCache
{

	public static String getSql()
	{
		String sql = new String("select hsft.id as service_id,hsft.name,hsft.guding_jj,hsft.zuidi_xfe,hsft.fwfl,hsft.taken_mode,hsft.ismodify,hsft.fee_type,hsft.service_type_code from hq_service_fee_type hsft left join hq_service_fee_of_organ hsfo on hsft.tenancy_id=hsfo.tenancy_id and hsft.id=hsfo.service_fee_id where hsft.valid_state='1' and hsft.tenancy_id =::tenancy_id and hsfo.store_id=::store_id");
		return sql;
	}
	
	public static String getKey()
	{
		return "service_id";
	}

}
