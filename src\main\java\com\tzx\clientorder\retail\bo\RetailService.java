package com.tzx.clientorder.retail.bo;

import java.util.HashMap;
import java.util.Map;
import net.sf.json.JSONObject;

public abstract interface RetailService
{
	public static final String				NAME							= "com.tzx.clientorder.retail.bo.impl.RetailServiceImpl";

	public static final Map<String, String>	paymentWayMapping				= new HashMap<String, String>();

	public static final Map<String, String>	posBillMemberTyoeMapping		= new HashMap<String, String>();

	public static final Map<String, String>	posBillCouponTypeMapping		= new HashMap<String, String>();

	public static final Map<String, String>	posBillCouponTypeNameMapping	= new HashMap<String, String>();

	public static final int					DEFAULT_SCALE					= 4;

	/**
	 * @param reqObj
	 * @param responseObj
	 * @throws Exception
	 */
	public abstract void getBaseData(JSONObject reqObj, JSONObject responseObj) throws Exception;

	/**
	 * @param reqObj
	 * @param responseObj
	 * @throws Exception
	 */
	public abstract void getDishes(JSONObject reqObj, JSONObject responseObj) throws Exception;

	/**
	 * @param reqObj
	 * @param responseObj
	 * @throws Exception
	 */
	public abstract void firstPayClose(JSONObject reqObj, JSONObject responseObj) throws Exception;
}
