package com.tzx.framework.common.util;

import java.beans.IntrospectionException;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Date;
import java.sql.Time;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import net.sf.json.JSONArray;
import net.sf.json.JSONNull;
import net.sf.json.JSONObject;
import net.sf.json.JsonConfig;
import net.sf.json.processors.JsonValueProcessor;
import net.sf.json.util.CycleDetectionStrategy;
import net.sf.json.util.JSONUtils;
import net.sf.json.util.PropertyFilter;

import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.log4j.Logger;

public class JsonUtils
{
	protected static Logger		logger			= Logger.getLogger(JsonUtils.class);
	public static JsonConfig	gxhkJsonConfig	= new JsonConfig();

	static
	{
		gxhkJsonConfig.registerJsonValueProcessor(java.sql.Time.class, new JsonValueProcessor()
		{
			public Object processObjectValue(String key, Object value, JsonConfig jsonConfig)
			{
				return value == null ? "" : DateUtil.format((Time) value);
			}

			public Object processArrayValue(Object value, JsonConfig jsonConfig)
			{
				return value;
			}
		});

		gxhkJsonConfig.registerJsonValueProcessor(java.sql.Timestamp.class, new JsonValueProcessor()
		{
			public Object processObjectValue(String key, Object value, JsonConfig jsonConfig)
			{
				return value == null ? "" : DateUtil.format((Timestamp) value);
			}

			public Object processArrayValue(Object value, JsonConfig jsonConfig)
			{
				return value;
			}
		});

		gxhkJsonConfig.registerJsonValueProcessor(java.sql.Date.class, new JsonValueProcessor()
		{
			public Object processObjectValue(String key, Object value, JsonConfig jsonConfig)
			{
				return value == null ? "" : DateUtil.format((Date) value);
			}

			public Object processArrayValue(Object value, JsonConfig jsonConfig)
			{
				return value;
			}
		});

	}

	public static String object2json(Object obj)
	{
		StringBuilder json = new StringBuilder();
		if (obj == null || obj instanceof JSONNull)
		{
			json.append("\"\"");
		}
		else if (obj instanceof String)
		{
			json.append("\"").append(string2json(obj.toString())).append("\"");
		}
		else if (obj instanceof Integer || obj instanceof Float || obj instanceof Boolean || obj instanceof Short || obj instanceof Double || obj instanceof Long || obj instanceof BigDecimal || obj instanceof BigInteger || obj instanceof Byte)
		{
			json.append(string2json(obj.toString()));
		}
		else if (obj instanceof Object[])
		{
			json.append(array2json2((Object[]) obj));
		}
		else if (obj instanceof List)
		{
			json.append(list2json((List<?>) obj));
		}
		else if (obj instanceof Map)
		{
			json.append(map2json((Map<?, ?>) obj));
		}
		else if (obj instanceof Set)
		{
			json.append(set2json((Set<?>) obj));
		}
		else if (obj instanceof java.util.Date || obj instanceof Time || obj instanceof Timestamp)
		{
			json.append("\"").append(DateUtil.format(obj)).append("\"");
		}
		else
		{
			json.append(bean2json(obj));
		}
		return json.toString();
	}

	public static JSONObject StringToJSONOBject(String str)
	{
		if (str == null || str.trim().length() == 0)
		{
			return null;
		}
		JSONObject jsonObject = null;
		try
		{
			jsonObject = JSONObject.fromObject(str);
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		return jsonObject;

	}

	public static String bean2json(Object bean)
	{
		StringBuilder json = new StringBuilder();
		json.append("{");
		PropertyDescriptor[] props = null;
		try
		{
			props = Introspector.getBeanInfo(bean.getClass(), Object.class).getPropertyDescriptors();
		}
		catch (IntrospectionException e)
		{
		}
		if (props != null)
		{
			for (int i = 0; i < props.length; i++)
			{
				try
				{
					String name = object2json(props[i].getName());
					String value = object2json(props[i].getReadMethod().invoke(bean));
					json.append(name);
					json.append(":");
					json.append(value);
					json.append(",");
				}
				catch (Exception e)
				{
				}
			}
			json.setCharAt(json.length() - 1, '}');
		}
		else
		{
			json.append("}");
		}
		return json.toString();
	}

	public static String list2json(List<?> list)
	{
		StringBuilder json = new StringBuilder();
		json.append("[");
		if (list != null && list.size() > 0)
		{
			for (Object obj : list)
			{
				json.append(object2json(obj));
				json.append(",");
			}
			json.setCharAt(json.length() - 1, ']');
		}
		else
		{
			json.append("]");
		}
		return json.toString();
	}

	public static String array2json(Object[] array)
	{
		StringBuilder json = new StringBuilder();
		json.append("[");
		if (array != null && array.length > 0)
		{
			for (Object obj : array)
			{
				json.append(object2json(obj));
				json.append(",");
			}
			json.setCharAt(json.length() - 1, ']');
		}
		else
		{
			json.append("]");
		}
		return json.toString();
	}

	public static String array2json2(Object[] array)
	{
		StringBuilder json = new StringBuilder();
		json.append("{");
		if (array != null && array.length > 0)
		{
			for (Object obj : array)
			{
				json.append(object2json(obj));
				json.append(",");
			}
			json.setCharAt(json.length() - 1, ']');
		}
		else
		{
			json.append("}");
		}
		return json.toString();
	}

	public static String map2json(Map<?, ?> map)
	{
		StringBuilder json = new StringBuilder();
		json.append("{");
		if (map != null && !JSONUtils.isNull(map))
		{
			for (Object key : map.keySet())
			{
				json.append(object2json(key));
				json.append(":");
				json.append(object2json(map.get(key)));
				json.append(",");
			}
			json.setCharAt(json.length() - 1, '}');
		}
		else
		{
			json.append("}");
		}
		return json.toString();

	}

	public static String set2json(Set<?> set)
	{
		StringBuilder json = new StringBuilder();
		json.append("[");
		if (set != null && set.size() > 0)
		{
			for (Object obj : set)
			{
				json.append(object2json(obj));
				json.append(",");
			}
			json.setCharAt(json.length() - 1, ']');
		}
		else
		{
			json.append("]");
		}
		return json.toString();
	}

	public static String string2json(String s)
	{
		if (s == null) return "";
		StringBuilder sb = new StringBuilder();
		for (int i = 0; i < s.length(); i++)
		{
			char ch = s.charAt(i);
			switch (ch)
			{
				case '"':
					sb.append("\\\"");
					break;
				case '\\':
					sb.append("\\\\");
					break;
				case '\b':
					sb.append("\\b");
					break;
				case '\f':
					sb.append("\\f");
					break;
				case '\n':
					sb.append("\\n");
					break;
				case '\r':
					sb.append("\\r");
					break;
				case '\t':
					sb.append("\\t");
					break;
				case '/':
					sb.append("\\/");
					break;
				default:
					if (ch >= '\u0000' && ch <= '\u001F')
					{
						String ss = Integer.toHexString(ch);
						sb.append("\\u");
						for (int k = 0; k < 4 - ss.length(); k++)
						{
							sb.append('0');
						}
						sb.append(ss.toUpperCase());
					}
					else
					{
						sb.append(ch);
					}
			}
		}
		return sb.toString();
	}

	/**
	 * 将指定值替换为Constans.GLOBAL_CITY_ID
	 * 
	 * @param jsonStr
	 *            json字符串
	 * @param param
	 *            要替换的key
	 * @return
	 */
	public static String replaceValue(String jsonStr, String[] param)
	{
		// JSONObject json=new JSONObject(jsonStr);

		String value = "";
		// String[] jsonIndex=jsonStr.split(",");
		// for(String str:param){
		// for(int i=0;i<jsonIndex.length;i++){
		// if(jsonIndex[i].indexOf(str)!=-1){
		// value+=jsonIndex[i].replace("\"\"","\""+Constans.GLOBAL_CITY_ID+"\"")+",";
		// }
		// else
		// {
		// value+=jsonIndex[i]+",";
		// }
		// }
		// }
		// value=value.substring(0,value.length()-1);
		return value;
	}

	/**
	 * 去掉制定字符串的双引号
	 * 
	 * @param jsonStr
	 *            需要替换的json字符串
	 * @param param
	 *            要替换的key
	 * @return
	 */
	public static String replaceQuotation(String jsonStr, String[] param)
	{
		// String[] jsonIndex=jsonStr.split(",");
		// String value="";
		// for(int i=0;i<jsonIndex.length;i++){
		// String value2="";
		// for(int j=0;j<param.length;j++){
		// if(jsonIndex[i].indexOf(param[j])!=-1){
		// value2=jsonIndex[i].replace("\"","").replace(param[j],"\""+param[j]+"\"")+",";
		// }
		// }
		// if(value2!=null&&!value2.equals("")){
		// value+=value2;
		// }
		// else{
		// value+=jsonIndex[i]+",";
		// }
		// }
		// value=value.substring(0,value.length()-1);
		// return value;
		return jsonStr;
	}

	/**
	 * 从json中取String
	 * 
	 * @Title: getString
	 * @data:2013-6-25上午9:37:44
	 * @author:zhanghongliang
	 * 
	 * @param json
	 * @param key
	 * @return
	 */
	public static final String getString(JSONObject json, String key)
	{
		String result = "";
		Object obj = getObject(json, key);
		if (obj != null)
		{
			if (StringUtils.isEmpty(obj.toString()))
			{
				result = null;
			}
			else
			{
				result = obj.toString();
			}
		}
		return result;
	}

	public static final int getInt(JSONObject json, String key)
	{
		int result = 0;
		Object obj = getObject(json, key);
		if (obj != null)
		{
			result = NumberUtils.toInt(obj.toString(), result);
		}

		return result;
	}

	public static final boolean getBoolean(JSONObject json, String key)
	{
		boolean result = false;
		Object obj = getObject(json, key);
		if (obj != null)
		{
			result = BooleanUtils.toBoolean(obj.toString());
		}

		return result;
	}

	public static final Double getDouble(JSONObject json, String key)
	{
		double result = 0;
		Object obj = getObject(json, key);
		if (obj != null)
		{
			result = NumberUtils.toDouble(obj.toString(), result);
		}
		return Double.isNaN(result) ? 0d : result;
	}

	public static final JSONObject getJSONObject(JSONObject json, String key)
	{
		JSONObject result = null;
		Object obj = getObject(json, key);
		if (obj != null && obj instanceof JSONObject)
		{
			result = (JSONObject) obj;
		}

		return result;
	}

	public static final JSONArray getJSONArray(JSONObject json, String key)
	{
		JSONArray result = null;
		Object obj = getObject(json, key);
		if (obj != null && obj instanceof JSONArray)
		{
			result = (JSONArray) obj;
		}

		return result;
	}

	public static final Object getObject(JSONObject json, String key)
	{
		Object result = null;
		if (json != null && StringUtils.isNotEmpty(key) && json.containsKey(key))
		{
			result = json.get(key);
		}
		return result;
	}

	public static final <T> T JSONToBean(JSONObject jsonData, Class<T> clazz)
	{
		return JSONToBean(jsonData, clazz, null);
	}

	/**
	 * json对象转bean
	 * 
	 * @Title: JSONToBean
	 * @data:2013-7-3上午9:37:35
	 * @author:zhanghongliang
	 * 
	 * @param jsonData
	 *            {@link JSONObject}
	 * @param clazz
	 *            {@link Class}
	 * @param jsonConfig
	 *            {@link JsonConfig}
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static final <T> T JSONToBean(JSONObject jsonData, Class<T> clazz, JsonConfig jsonConfig)
	{
		T result = null;
		if (jsonData == null || jsonData.size() == 0 || clazz == null)
		{
			return result;
		}
		if (jsonConfig == null)
		{
			jsonConfig = getJSConfig(null, null, false);
		}
		try
		{
			result = clazz.newInstance();
			result = (T) JSONObject.toBean(jsonData, result, jsonConfig);
		}
		catch (Exception e)
		{
			e.printStackTrace();
			logger.error(ExceptionUtils.getFullStackTrace(e));
		}
		return result;
	}

	/**
	 * 
	 * @Title: JSONToBean
	 * @data:2013-7-3上午9:40:06
	 * @author:zhanghongliang
	 * 
	 * @param jsonData
	 *            {@link JSONObject}json对象
	 * @param clazz
	 *            要转换的成的对象
	 * @param excludes
	 *            不包含的字段名称
	 * @param datePattern
	 *            日期的正则
	 * @param includeNull
	 *            是否包括null，空串字段
	 * @return
	 */
	public static final <T> T JSONToBean(JSONObject jsonData, Class<T> clazz, String[] excludes, String datePattern, Boolean includeNull)
	{
		JsonConfig jsonConfig = getJSConfig(excludes, datePattern, includeNull);
		return JSONToBean(jsonData, clazz, jsonConfig);
	}

	/**
	 * 取jsonConfig
	 * 
	 * @Title: getJSConfig
	 * @data:2013-6-28上午11:07:28
	 * @author:zhanghongliang
	 * 
	 * @param excludes
	 *            不包含的字段，哪些字段需要过滤掉
	 * @param datePattern
	 *            日期转换格式
	 * @param includeNull
	 *            是否包含值为null的字段，默认包含 true 包含，false不包含
	 * @return
	 */
	public static JsonConfig getJSConfig(String[] excludes, String datePattern, Boolean includeNull)
	{
		JsonConfig result = new JsonConfig();
		if (null != excludes) result.setExcludes(excludes);

		result.setIgnoreDefaultExcludes(false);
		result.setCycleDetectionStrategy(CycleDetectionStrategy.LENIENT);
		result.registerJsonValueProcessor(Date.class, new JsonDateValueProcessor(datePattern));
		if (includeNull != null && includeNull == false)
		{
			// 忽略属性值为null的字段
			result.setJsonPropertyFilter(new PropertyFilter()
			{
				public boolean apply(Object source, String name, Object value)
				{
					// 忽略birthday属性
					if (value == null)
					{
						return true;
					}
					return false;
				}
			});

			// result.setJavaPropertyFilter(new PropertyFilter() {
			//
			// @Override
			// public boolean apply(Object source, String name, Object value) {
			// if(value==null || StringUtils.isBlank(value.toString())){
			// return true;
			// }
			// return false;
			// }
			// }
			// );
		}
		return result;
	}

	public JSONObject strToJSONObject(String str, JSONObject jsonObject)
	{
		JSONObject result = null;
		return result;
	}

	public static JSONObject parseToJSONObject(String str)
	{
		JSONObject result = null;
		if (StringUtils.isNotBlank(str) && str.startsWith("{") && str.endsWith("}"))
		{
			result = JSONObject.fromObject(str);
		}
		if (result == null)
		{
			result = new JSONObject();
		}
		return result;
	}

	public static Object fromJsonToJava(JSONObject json, Class<?> pojo) throws Exception
	{
		// 首先得到pojo所定义的字段
		Field[] fields = pojo.getDeclaredFields();
		// 根据传入的Class动态生成pojo对象
		Object obj = pojo.newInstance();
		for (Field field : fields)
		{
			// 设置字段可访问（必须，否则报错）
			field.setAccessible(true);
			// 得到字段的属性名
			String name = field.getName();
			// 这一段的作用是如果字段在JSONObject中不存在会抛出异常，如果出异常，则跳过。
			try
			{
				json.get(name);
			}
			catch (Exception ex)
			{
				continue;
			}
			if (json.get(name) != null && !"".equals(json.getString(name)))
			{
				// 根据字段的类型将值转化为相应的类型，并设置到生成的对象中。
				if (field.getType().equals(Long.class) || field.getType().equals(long.class))
				{
					field.set(obj, Long.parseLong(json.getString(name)));
				}
				else if (field.getType().equals(String.class))
				{
					field.set(obj, json.getString(name));
				}
				else if (field.getType().equals(Double.class) || field.getType().equals(double.class))
				{
					field.set(obj, Double.parseDouble(json.getString(name)));
				}
				else if (field.getType().equals(Integer.class) || field.getType().equals(int.class))
				{
					field.set(obj, Integer.parseInt(json.getString(name)));
				}
				else if (field.getType().equals(java.util.Date.class))
				{
					field.set(obj, Date.parse(json.getString(name)));
				}
				else
				{
					continue;
				}
			}
		}
		return obj;
	}
	
	//对json数据字段进行过滤操作
	public static JSONObject filterJSONObject(JSONObject json, String[] arry) throws Exception
	{
		JSONObject jsonObj=new JSONObject();
		//对arry数组进行循环，比较json对象中是否包含该节点，包含就将该节点计入到jsonObj中，不包含，就继续执行下一次循环。
		for(int i=arry.length-1;i>=0;i--){
		  if(json.has(arry[i])){
			  jsonObj.put(arry[i], json.optString(arry[i]));
		  }
		}
		return jsonObj;
	 }
	
	//对jsonarray数据字段进行过滤操作
	public static JSONArray filterJSONArray(JSONArray json, String[] arry) throws Exception
	{
		JSONArray jsonArray = new JSONArray();
		
		//对arry数组进行循环，比较json对象中是否包含该节点，包含就将该节点计入到jsonObj中，不包含，就继续执行下一次循环。
		for(int j=0;j<json.size();j++){
			JSONObject jsonObj=json.getJSONObject(j);
			JSONObject jsons=new JSONObject();
			
			for(int i=arry.length-1;i>=0;i--){
				  if(jsonObj.has(arry[i])){
					  jsons.put(arry[i], jsonObj.optString(arry[i]));
				  }
		    }
			
			jsonArray.add(jsons);
			
		}

		return jsonArray;
	 }

	//比较两个json的差异，并加上中文备注字段，中文备注以数组的方式传参
	public static String diffJsonsAddName(JSONObject oldJson,JSONObject newJson,String tableName)
	{
		String jsonStr="";
		String[] arrayColumns = arrayColumn(tableName);
		String[] arrayNames = arrayColumnName(tableName);
		if(arrayNames.length!=arrayColumns.length){
		  return "字段名和字段个数不符，请检查！";
		}else{
			for(int i=0;i<arrayColumns.length;i++){
				if(newJson.has(arrayColumns[i])){
				  if(jsonStr.equals("")){
					  jsonStr = arrayNames[i]+":"+oldJson.optString(arrayColumns[i])+"-->"+newJson.optString(arrayColumns[i]); 
				  }else{
					if(!newJson.optString(arrayColumns[i]).equals(oldJson.optString(arrayColumns[i]))){
						jsonStr = jsonStr+";"+arrayNames[i]+":"+oldJson.optString(arrayColumns[i])+"-->"+newJson.optString(arrayColumns[i]);
					}				  
				  }
				}
			}			
		}
		return jsonStr;

	}
	
	
	//根据表名的不同，给出相应的表字段结构
	public static String[] arrayColumn(String tableName){
		if(tableName.equals("scm_material")){
		  String[] array={"material_properties_name","unit_id","is_daily_stock","is_week_stock","is_month_stock","is_testing","storage_mode","stock_count_mode","expiration_warning","expiration_date","safe_stock","turnover_date","turnover_warning","high_stock","purchase_unit_id","pur_conversion","material_model_purchase","order_quantity_calc","ordering_cycle_id","largest_amount","largest_quantity","lowest_quantity","order_category_id","bomunit_id","bom_conversion","matebomgroup_id","tax_rate","price_method","is_trace_different","is_stat_yield","is_different","yield_norm","yield_describe","phonetic_code","five_code","bar_code","bar_code2","third_code","unit_volume","unit_weight","photo_url"};	
		  return array;
		}else{
		  String[] array={};
		  return array;
		}
	}
	
	//根据表名的不同，给出相应表字段中文结构，主要需要保持与arrayColumn给出的字段结构一一对应
	public static String[] arrayColumnName(String tableName){
		if(tableName.equals("scm_material")){
		  String[] array={"物料属性","库存主单位","是否日盘（1 是 0 否）","是否周盘（1 是 0 否）","是否月盘（1 是 0 否）","是否质检(1 是 0 否)","存储模式","盘点处理方式","保质期预警天数","保质期","安全库存","库存周转期","呆滞预警天数","高限库存","默认采购单位","采购单位换算率","采购规格","订货量计算方式","订货周期类型","单次最大进货额","单次最大进货量","单次最低进货量","订货类别","BOM消耗单位","BOM消耗单位换算率","BOM物料分组","税率","计价方式","是否追踪差异(1 追踪 0 不追踪)","统计应产率(1 统计 0 不统计)","统计差异(1 统计 0 不统计)","应产率标准","应产率标准描述","拼音简码","五笔简码","国标码","自编码","第三方编码","单位体积","单位质量","图片及路径"};	
		  return array;
		}else{
		  String[] array={};
		  return array;
		}
	}
	
	public static void main(String args[])
	{

		// String jsonStr
		// ="{'name':'zfj','dd':[{'ddr':'1','encrypt':'2','ff':'1','length':'23','ffe':'editStyled','ill':'1','pkor':'2','name':'zfj','isKey':'2','alias':'ffff'}],'addRelations':[{'type':'2','ld':'zfj','ld':'2'}]} ";
//		String jsonStr = "{'data':[{'ddr':'1','encrypt':'2','ff':'1','length':'23','ffe':'editStyled','ill':'1','pkor':'2','name':'zfj','isKey':'2','alias':'ffff'}],'addRelations':[{'type':'2','ld':'zfj','ld':'2'}]}";
//		// 错误方式 String jsonStr
//		// ="[{'ddr':'1','encrypt':'2','ff':'1','length':'23','ffe':'editStyled','ill':'1','pkor':'2','name':'zfj','isKey':'2','alias':'ffff'}],'addRelations':[{'type':'2','ld':'zfj','ld':'2'}]";
//		JsonUtils.StringToJSONOBject(jsonStr);
	}
}