package com.tzx.pos.po.springjdbc.dao;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import net.sf.json.JSONObject;

import com.tzx.base.entity.PosBillMember;
import com.tzx.pos.base.dao.BaseDao;
import com.tzx.pos.bo.dto.PosBillMembers;

public interface PosBillMemberDao extends BaseDao
{
	String	NAME	= "com.tzx.pos.po.springjdbc.dao.imp.PosBillMemberDaoImp";

	/**
	 * 操作账单会员库表插入数据
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param report_date
	 * @param billno
	 * @param type
	 * @param cardCode
	 * @param mobil
	 * @param customer_name
	 * @param customer_credit
	 */
	public void insertPosBillMember(String tenancyId, int storeId, String billNum, Date reportDate, String type, String customerCode, String cardCode, String mobil, Timestamp lastUpdatetime, String remark, String customer_name, double customer_credit) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param reportDate
	 * @param type
	 * @param customerCode
	 * @param cardCode
	 * @param mobil
	 * @param lastUpdatetime
	 * @param remark
	 * @param customer_name
	 * @param customer_credit
	 * @param consume_after_credit
	 * @param credit
	 * @param amount
	 * @param bill_code
	 * @param request_state
	 * @throws Exception
	 */
	public void insertPosBillMember(String tenancyId, int storeId, String billNum, Date reportDate, String type, String customerCode, String cardCode, String mobil, Timestamp lastUpdatetime, String remark, String customer_name, double customer_credit, double consume_after_credit, double credit,
			double amount, String bill_code, String request_state) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param billMember
	 * @throws Exception
	 */
	public void insertPosBillMember(String tenancyId, int storeId, PosBillMember billMember) throws Exception;

	/**
	 * 修改会员操作金额
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param reportDate
	 * @param billno
	 * @param type
	 * @param cardCode
	 * @param mobil
	 */
	public void updatePosBillMember(String tenantId, int storeId, String billNum, String type, double amount, double credit, String billCode, String requestState, String customerName, Double consumeBeforeCredit, Double consumeAfterCredit) throws Exception;
	
	/**修改会员操作金额
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @param type
	 * @param id
	 * @param amount
	 * @param credit
	 * @param billCode
	 * @param requestState
	 * @param customerName
	 * @param consumeBeforeCredit
	 * @param consumeAfterCredit
	 * @throws Exception
	 */
	public void updatePosBillMember(String tenantId, int storeId, String billNum, String type, Integer id, double amount, double credit, String billCode, String requestState, String customerName, Double consumeBeforeCredit, Double consumeAfterCredit) throws Exception;
	
	/**
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @param type
	 * @param amount
	 * @param credit
	 * @param billCode
	 * @param requestState
	 * @param customerName
	 * @param consumeAfterCredit
	 * @param consumeAfterMainBalance
	 * @param consumeAfterRewardBalance
	 * @throws Exception
	 */
	public void updatePosBillMember(String tenantId, int storeId, String billNum, String type, double amount, double credit, String billCode, String requestState, String customerName, Double consumeAfterCredit, Double consumeAfterMainBalance,Double consumeAfterRewardBalance) throws Exception;

	/**
	 * 删除账单会员操作记录
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param billno
	 * @param type
	 * @throws Exception
	 */
	public void deletePosBillMember(String tenantId, int storeId, String billNum, String[] types) throws Exception;

	/**
	 * 删除账单会员操作记录
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param billno
	 * @param type
	 * @throws Exception
	 */
	public void deletePosBillMember(String tenantId, int storeId, String billNum, String type) throws Exception;
	
	/** 删除会员操作记录
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @param billCode
	 * @throws Exception
	 */
	public void deletePosBillMemberByBillCode(String tenantId, int storeId, String billNum, String billCode) throws Exception;

	/**
	 * 查询
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param billno
	 * @param type
	 *            若为空,则查询所有
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> queryPosBillMember(String tenantId, int storeId, String billNum, String type) throws Exception;

	/**
	 * 通过订单号查询账单会员表
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @param type
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> queryPosBillMember(String tenantId, int storeId, String billNum, String cardCode, String type) throws Exception;

	/**
	 * 查询账单绑定的会员信息
	 * 
	 * @param tenantId
	 * @param organId
	 * @param billNum
	 * @return
	 * @throws Exception
	 */
	public List<PosBillMembers> selectMembersByBillNum(String tenantId, Integer storeId, String billNum) throws Exception;

	/**
	 * 修改会员操作金额
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param reportDate
	 * @param billno
	 * @param type
	 * @param cardCode
	 * @param mobil
	 */
	public void updatePosBillMemberRegain(String tenantId, int storeId, String billNum, String type, double amount, double credit, String billCode, String requestState, String customerName, Double consumeBeforeCredit, Double consumeAfterCredit, Integer regainCount) throws Exception;

	/**
	 * 通过订单号查询账单会员表
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @param type
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getPosBillMemberRegainByBillNum(String tenantId, int storeId, String billNum, String type,Integer regainCount) throws Exception;

	/**
	 * @param tenantId
	 * @param storeId
	 * @param fromBillNum
	 * @param toBillNum
	 * @param reportDate
	 * @param types
	 * @param updateTime
	 * @throws Exception
	 */
	public void copyPosBillMemberByBillNum(String tenantId, int storeId,String fromBillNum,String toBillNum,Date reportDate,String[] types,Timestamp updateTime)throws Exception;
}
