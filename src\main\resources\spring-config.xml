<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:cache="http://www.springframework.org/schema/cache"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xmlns:task="http://www.springframework.org/schema/task"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
	   http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
       http://www.springframework.org/schema/cache
       http://www.springframework.org/schema/cache/spring-cache.xsd
       http://www.springframework.org/schema/aop
       http://www.springframework.org/schema/aop/spring-aop-4.0.xsd
       http://www.springframework.org/schema/context
       http://www.springframework.org/schema/context/spring-context-4.0.xsd
       http://www.springframework.org/schema/tx
       http://www.springframework.org/schema/tx/spring-tx-4.0.xsd
       http://www.springframework.org/schema/task
       http://www.springframework.org/schema/task/spring-task-4.0.xsd"
       default-autowire="byName">
    <!-- 加载配置文件 -->
    <bean class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
        <property name="locations">
            <list>
                <value>classpath*:pos/*_config.properties</value>
                <value>classpath*:../../config/systemParam.properties</value>
            </list>
        </property>
    </bean>

    <!-- 使用annotation 自动注册bean,并检查@Required,@Autowired的属性已被注入 -->
    <context:component-scan base-package="com"/>
    <aop:aspectj-autoproxy proxy-target-class="true"/>

    <!-- 自动创建代理织入切面  默认使用jdk动态代理织入-->
    <aop:aspectj-autoproxy/>

    <!-- 统一处理json问题 -->
    <context:annotation-config/>
    <bean class="org.springframework.web.servlet.mvc.annotation.DefaultAnnotationHandlerMapping"/>
    <bean class="org.springframework.web.servlet.mvc.annotation.AnnotationMethodHandlerAdapter">
        <property name="messageConverters">
            <ref bean="jacksonMessageConverter"/>
        </property>
    </bean>
    <bean id="jacksonMessageConverter" class="org.springframework.http.converter.json.MappingJackson2HttpMessageConverter">
        <property name="supportedMediaTypes">
            <value>application/json;charset=UTF-8</value>
        </property>
        <property name="objectMapper">
            <bean class="org.springframework.http.converter.json.Jackson2ObjectMapperFactoryBean">
                <property name="failOnEmptyBeans" value="false"/>
                <property name="serializers">
                    <array>
                        <bean class="com.tzx.framework.common.util.JSONObjectSerializer"/>
                    </array>
                </property>
            </bean>
        </property>
    </bean>

    <bean id="viewResolver" class="org.springframework.web.servlet.view.InternalResourceViewResolver">
        <property name="viewClass" value="org.springframework.web.servlet.view.JstlView"/>
        <property name="prefix" value="/"></property>
    </bean>

    <!--
        <bean id="multipartResolver" class="org.springframework.web.multipart.commons.CommonsMultipartResolver"/>
    -->

    <!-- 配置事物管理器 -->
    <bean id="transactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="dataSource"/>
    </bean>

    <!-- 配置处理事务的通知 -->
    <tx:advice id="txAdvice" transaction-manager="transactionManager">
        <tx:attributes>
            <!-- 对get/load/search/find开头的方法要求只读事务 -->
            <tx:method name="get*" propagation="SUPPORTS" read-only="true"/>
            <tx:method name="load*" propagation="SUPPORTS" read-only="true"/>
            <tx:method name="search*" propagation="SUPPORTS" read-only="true"/>
            <tx:method name="find*" propagation="SUPPORTS" read-only="true"/>
            <!-- 卡充值、消费、新建事务   -->
            <tx:method name="customerCard*" propagation="REQUIRES_NEW" rollback-for="Exception"/>
            <tx:method name="requiresnew*" propagation="REQUIRES_NEW" rollback-for="Exception"/>
            <tx:method name="bonusPointConsume*" propagation="REQUIRES_NEW" rollback-for="Exception"/>
            <tx:method name="customerCredit*" propagation="REQUIRES_NEW" rollback-for="Exception"/>
            <tx:method name="coupons*" propagation="REQUIRES_NEW" rollback-for="Exception"/>
            <!-- 微生活微信点餐对接下单方法需要新建事务 -->
            <tx:method name="insertOrderRequiresNew" propagation="REQUIRES_NEW" rollback-for="Exception"/>
            <tx:method name="saveOrUpdateOrder" propagation="REQUIRES_NEW" rollback-for="Exception"/>
            <!-- 第三方支付方法需要新建事务 -->
            <tx:method name="barcodePayment" propagation="REQUIRES_NEW" rollback-for="Exception"/>
            <tx:method name="precreatePayment" propagation="REQUIRES_NEW" rollback-for="Exception"/>
            <!-- 对其它方法要求事务 -->
            <tx:method name="*" propagation="REQUIRED" rollback-for="Exception"/>
        </tx:attributes>
    </tx:advice>
    <aop:config proxy-target-class="true">
        <!-- 前面配置的transactionManager是专对Hibernate的事务管理器, 对所有com.tzx.bo包及其子包下的所有方法添加事务管理 。 -->
        <aop:pointcut id="serviceMethods" expression="execution(* com.tzx.*.bo..*.*(..)) or execution(* com.tzx.clientorder.*.bo..*.*(..))"/>
        <!-- 织入 -->
        <aop:advisor advice-ref="txAdvice" pointcut-ref="serviceMethods"/>
    </aop:config>

    <bean id="jdbcTemplate" class="org.springframework.jdbc.core.JdbcTemplate">
        <property name="dataSource" ref="dataSource"/>
    </bean>

    <bean id="dataSource" class="com.alibaba.druid.pool.DruidDataSource" init-method="init" destroy-method="close">

        <!-- <property name="url" value="****************************************?rewriteBatchedStatements=true"/> -->
        <property name="url" value="****************************************"/>
        <property name="username" value="tzxdbuser"/>
        <property name="password" value="p@ss2d"/>

        <property name="maxActive" value="200" />
        <property name="initialSize" value="10" />
        <property name="maxWait" value="60000" />
        <property name="minIdle" value="10" />

        <property name="timeBetweenEvictionRunsMillis" value="60000" />
        <property name="minEvictableIdleTimeMillis" value="300000" />

        <property name="testWhileIdle" value="true" />
        <property name="testOnBorrow" value="false" />
        <property name="testOnReturn" value="false" />

        <property name="poolPreparedStatements" value="true" />
        <property name="maxOpenPreparedStatements" value="20" />

        <property name="filters" value="stat" />
        <property name= "validationQuery" value="select 1" /> 
        <property name= "numTestsPerEvictionRun" value="30" />

    </bean>

    <bean id="dynamicDataSource"
          class="com.tzx.base.datasource.dao.DynamicDataSource">
        <property name="targetDataSources">
            <map></map>
        </property>
        <property name="defaultTargetDataSource" ref="dataSource"/>
    </bean>

     <!--Druid Spring statis -->

    <bean id="stat-filter" class="com.alibaba.druid.filter.stat.StatFilter">
        <property name="mergeSql" value="true" />
        <property name="slowSqlMillis" value="3000" />
        <property name="logSlowSql" value="true" />
    </bean>

      <bean id="druid-stat-interceptor"
           class="com.alibaba.druid.support.spring.stat.DruidStatInterceptor">
     </bean>

    <bean id="druid-stat-pointcut" class="org.springframework.aop.support.JdkRegexpMethodPointcut"
          scope="prototype">
        <property name="patterns">
            <list>
                <value>com.tzx.*.bo.imp.*</value>
                <value>com.tzx.*.po.*.dao.imp.*</value>
            </list>
        </property>
    </bean>

     <aop:config>
         <aop:advisor advice-ref="druid-stat-interceptor" pointcut-ref="druid-stat-pointcut" />
     </aop:config>

    <!--订单中心redis start-->
    <bean id="ordercenterRedisTemplate" class="org.springframework.data.redis.core.StringRedisTemplate">
        <property name="connectionFactory" ref="ordercenterConnectionFactory" />
        <property name="keySerializer">
            <bean
                    class="org.springframework.data.redis.serializer.StringRedisSerializer" />
        </property>
        <property name="hashKeySerializer">
            <bean
                    class="org.springframework.data.redis.serializer.StringRedisSerializer" />
        </property>
        <property name="valueSerializer">
            <bean
                    class="org.springframework.data.redis.serializer.StringRedisSerializer" />
        </property>
        <property name="hashValueSerializer">
            <bean
                    class="org.springframework.data.redis.serializer.StringRedisSerializer" />
        </property>
    </bean>

    <bean id="ordercenterConnectionFactory"
          class="org.springframework.data.redis.connection.jedis.JedisConnectionFactory">
        <property name="hostName" value="${redis.host}" />
        <property name="port" value="${redis.port}" />
        <property name="password" value="${redis.password}" />
        <property name="poolConfig" ref="orderCenterJedisPoolConfig" />
    </bean>

    <bean id="orderCenterJedisPoolConfig" class="redis.clients.jedis.JedisPoolConfig">
        <property name="maxIdle" value="${redis.pool.maxIdle}" />
        <property name="maxTotal" value="${redis.pool.maxTotal}" />
        <property name="maxWaitMillis" value="${redis.pool.maxWaitMillis}" />
        <property name="testOnBorrow" value="${redis.pool.testOnBorrow}" />
    </bean>
    <!--订单中心redis end-->

    <!-- 2017-06-21增加，裘浙洪 -->
    <bean id="springUtils" class="com.tzx.pos.base.util.SpringContextUtils"/>

    <!-- 导入配置文件 -->
    <import resource="spring/ehcache/cache-config.xml"/>

    <!-- 作为消费端，如果不想作为mq消费端，请注释掉下边的配置文件引入 -->
    <import resource="spring/jms/jmsConnectionFactoryPosStoreListener.xml"/>
    <!-- pos门店日志文件上传到om系统 -->
<!--     <import resource="spring/jms/jmsConnectionFactoryLogStoreListener.xml"/> -->
</beans>