package com.tzx.clientorder.common.entity;

public class AcewillOrderSetmealItem {

	private String id;
	private String name;
	private String dishsno;
	private Double aprice;
	private String selnum;
	private Integer maxnum;
	private String duid;
	private String duName;
	private Integer number;
	private Integer soldout;

	private Object cooks;

//	private String did;
	private String mid;
	private String rpdid;
	
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getDishsno() {
		return dishsno;
	}
	public void setDishsno(String dishsno) {
		this.dishsno = dishsno;
	}

	public Double getAprice() {
		return aprice;
	}
	public void setAprice(Double aprice) {
		this.aprice = aprice;
	}
	public String getSelnum() {
		return selnum;
	}
	public void setSelnum(String selnum) {
		this.selnum = selnum;
	}
	public Integer getMaxnum() {
		return maxnum;
	}
	public void setMaxnum(Integer maxnum) {
		this.maxnum = maxnum;
	}
	public String getDuid() {
		return duid;
	}
	public void setDuid(String duid) {
		this.duid = duid;
	}
	public String getDuName() {
		return duName;
	}
	public void setDuName(String duName) {
		this.duName = duName;
	}
	public Integer getNumber() {
		return number;
	}
	public void setNumber(Integer number) {
		this.number = number;
	}
	public Integer getSoldout() {
		return soldout;
	}
	public void setSoldout(Integer soldout) {
		this.soldout = soldout;
	}

	public Object getCooks() {
		return cooks;
	}

	public void setCooks(Object cooks) {
		this.cooks = cooks;
	}

	public String getMid() {
		return mid;
	}

	public void setMid(String mid) {
		this.mid = mid;
	}


	public void setMallListName(){
		StringBuilder sb = new StringBuilder("　");
		sb.append(this.name);
		this.name = sb.toString();
	}
	public String getRpdid()
	{
		return rpdid;
	}
	public void setRpdid(String rpdid)
	{
		this.rpdid = rpdid;
	}
}


