package com.tzx.pos.bo.imp;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;

import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.Comet4jUtil;
import com.tzx.framework.common.util.DoubleHelper;
import com.tzx.framework.common.util.Scm;
import com.tzx.framework.common.util.Tools;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.controller.CometDataNoticeClientRunnable;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.bo.PosDishRetreatFoodService;
import com.tzx.pos.bo.PosDishService;
import com.tzx.pos.bo.PosSoldOutService;
import com.tzx.pos.po.springjdbc.dao.PosDao;
import com.tzx.pos.po.springjdbc.dao.PosDishDao;
import com.tzx.pos.po.springjdbc.dao.PosSoldOutDao;

import common.Logger;

@Service(PosDishRetreatFoodService.NAME)
public class PosDishRetreatFoodServiceImp extends PosBaseServiceImp implements PosDishRetreatFoodService
{

	private static final Logger	logger	= Logger.getLogger(PosDishRetreatFoodServiceImp.class);

	@Resource(name = PosSoldOutService.NAME)
	private PosSoldOutService		soldOutService;

	@Resource(name = PosDishDao.NAME)
	private PosDishDao			posDishDao;

	@Resource(name = PosDao.NAME)
	private PosDao				posDao;
	
	@Resource(name = PosSoldOutDao.NAME)
	private PosSoldOutDao		soldOutDao;

	/**
	 * 退菜
	 */
	@Override
	public void retreatFood(String tenancyId, int storeId, List<?> param, Data result, JSONObject json, String agent) throws Exception
	{
		if (param == null || param.isEmpty())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}
		JSONObject para = JSONObject.fromObject(param.get(0));
		if (Tools.isNullOrEmpty(para.opt("report_date")))
		{
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_REPORT_DATE);
		}
		if (Tools.isNullOrEmpty(para.opt("bill_num")))
		{
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_BILL_NUM);
		}
		final int scale = 4;

		Date reportDate = DateUtil.parseDate(ParamUtil.getDateStringValue(para, "report_date"));
		String posNum = para.optString("pos_num");
		String optNum = para.optString("opt_num");
		String billNum = para.optString("bill_num");
		Integer shiftId = para.optInt("shift_id");
		String managerId = para.optString("manager_id");

		// 验证客户端传入报表日期是否正确
        try
        {
            posDishDao.checkReportDate(tenancyId, storeId, reportDate);
        }
        catch (SystemException se)
        {
            throw se;
        }
        catch (Exception e1)
        {
            throw SystemException.getInstance(PosErrorCode.CHECK_REPORT_DATE_ERROR);
        }

		Timestamp currentTime = DateUtil.currentTimestamp();
		
		JSONObject billJson = posDishDao.getPosBillByBillnum(tenancyId, storeId, billNum);

		Double subtotal = 0d;
		Double discountkAmount = 0d;
		Double discountrAmount = 0d;
		Double giviAmount = 0d;
		Double malingAmount = 0d;
		String billProperty=null;
        String paymentState = null;
//		String table_code = "";

		// 查询退菜类型(0 正常退菜 1预打后预退菜)
		String returnType = "0";
		if (null != billJson)
		{
			subtotal = billJson.optDouble("subtotal");
			discountkAmount = billJson.optDouble("discountk_amount");
			discountrAmount = billJson.optDouble("discountr_amount");
			giviAmount = billJson.optDouble("givi_amount");
			malingAmount = billJson.optDouble("maling_amount");
			billProperty = billJson.optString("bill_property");
            paymentState = billJson.optString("payment_state");

			
			if (billJson.optInt("print_count") > 0)
			{
				returnType = "1";
			}
//			table_code = billJson.optString("table_code");
			
			// 账单有折扣不能退菜
			if (billJson.optDouble("discount_rate") != 0d && billJson.optDouble("discount_rate") != 100d && billJson.optInt("discount_case_id") > 0)
			{
				SystemException.getInstance(PosErrorCode.BILL_EXIST_DISCOUNT_NOT_RETREATFOOD);
			}
		}
		
		if(SysDictionary.BILL_PROPERTY_CLOSED.equals(billProperty))
		{
			throw SystemException.getInstance(PosErrorCode.BILL_CLOSED);
		}
        if(SysDictionary.PAYMENT_STATE_PAY.equals(paymentState))
        {
            throw SystemException.getInstance(PosErrorCode.BILL_PAYMENT_STATE_PAYING_ERROR);
        }

		// 查询操作员姓名
		String optName = posDishDao.getEmpNameById(optNum, tenancyId, storeId);

		List<String> idlist = new ArrayList<String>();
		List<JSONObject> soldOutList = new ArrayList<JSONObject>();

		Double retreatItemAmount = 0d;
		Double retreatItemDiscountAmount = 0d;
		
		for (Object obj : para.optJSONArray("item"))
		{
			JSONObject itemJson = JSONObject.fromObject(obj);
			if (Tools.isNullOrEmpty(itemJson.opt("rwid")))
			{
				throw SystemException.getInstance(PosErrorCode.NOT_NULL_RWID);
			}
			Integer rwid = itemJson.optInt("rwid");

			double count = 0;
			if (Tools.hv(itemJson.opt("count")))
			{
				count = itemJson.optDouble("count");
			}
			Integer reasonId = itemJson.optInt("reason_id");

			Integer item_serial = null;
			Integer item_id = null;
			double item_count = 0d;
			double return_count = 0d;
			String item_remark = null;
			String item_property = null;

			Double itemAmount = 0d;
			Double itemDiscountAmount = 0d;
			Double itemCount = 0d;
			/**
			 * 判断退菜菜品是否存在
			 */
			List<JSONObject> itemList = posDishDao.getPosBillItemByRwid(tenancyId, storeId, billNum, rwid);

			if (null != itemList && itemList.size() > 0)
			{
				JSONObject billItemJson = itemList.get(0);
				item_serial = billItemJson.optInt("item_serial");
				item_id = billItemJson.optInt("item_id");
				itemAmount = billItemJson.optDouble("item_amount");
				itemDiscountAmount = billItemJson.optDouble("discount_amount");

				item_count = billItemJson.optDouble("item_count");
				return_count = billItemJson.optDouble("return_count");
				itemCount = Scm.psub(item_count, return_count);

				item_remark = billItemJson.optString("item_remark");
				item_property = billItemJson.optString("item_property");
			}
			else
			{
				// 判断入参中的菜品是否存在，如果不存在msg返回“未找到退菜的菜品”
				throw SystemException.getInstance(PosErrorCode.NOT_FIND_RETREAT_FOOD);
			}

			if (Tools.hv(item_remark) && SysDictionary.ITEM_REMARK_TC01.equalsIgnoreCase(item_remark))
			{
				// 判断pos_bill_item.item_remark=’ TC01’的，msg返回“该菜品已经是退菜”
				throw SystemException.getInstance(PosErrorCode.HAVING_RETREAT_FOOD);
			}
			else if (Tools.hv(item_remark) && SysDictionary.ITEM_REMARK_FS02.equalsIgnoreCase(item_remark))
			{
				// 判断pos_bill_item.item_remark=’FS02’的，msg返回“该菜品已经奉送”
				throw SystemException.getInstance(PosErrorCode.HAVING_GIVE_FOOD);
			}
			else if (Tools.hv(item_remark) && SysDictionary.ITEM_REMARK_QX04.equalsIgnoreCase(item_remark))
			{
				// 判断pos_bill_item.item_remark=’QX04’的，msg返回“该菜品已经取消”
				throw SystemException.getInstance(PosErrorCode.HAVING_CANC_FOOD);
			}
			else if (Tools.hv(item_remark) && SysDictionary.ITEM_REMARK_CJ05.equalsIgnoreCase(item_remark))
			{
				// 判断pos_bill_item.item_remark=’CJ05’的，msg返回“该菜品已经取消”
				throw SystemException.getInstance(PosErrorCode.HAVING_OFFSET_FOOD);
			}

			if (Tools.hv(item_property) && SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item_property))
			{
				// 判断pos_bill_item. item_property=’ MEALLIST’的，msg返回“明细菜品不允许退菜”
				throw SystemException.getInstance(PosErrorCode.NOT_PERMIT_RETREAT_FOOD);
			}
			
			retreatItemAmount = DoubleHelper.add(retreatItemAmount, DoubleHelper.mul(DoubleHelper.div(itemAmount, itemCount, scale), count, scale), scale);
			retreatItemDiscountAmount = DoubleHelper.add(retreatItemDiscountAmount, DoubleHelper.mul(DoubleHelper.div(itemDiscountAmount, itemCount, scale), count, scale), scale);

			Integer offsetRwid = null;
			Integer offsetSerial = null;

			if (count > item_count)
			{
				// 判断入参中的退菜数量是否大于菜品数量，如果是msg返回“退菜数量不能大于菜品数量”
				throw SystemException.getInstance(PosErrorCode.RETREAT_COUNT_MORE_THAN_EXISTS);
			}
			else if (count == item_count)
			{
				// 菜品明细，将waitcall_tag=0去掉，更新时，不再更新waitcall_tag字段
				StringBuilder updateItemSql = new StringBuilder("update pos_bill_item set item_remark=?,return_count=?,return_type=?,returngive_reason_id=?,manager_num=? where bill_num = ? and store_id = ?");
				if (SysDictionary.ITEM_PROPERTY_SETMEAL.equalsIgnoreCase(item_property))
				{
					updateItemSql.append(" and setmeal_id = ? and setmeal_rwid = ?");
					posDishDao.update(updateItemSql.toString(), new Object[]
					{ SysDictionary.ITEM_REMARK_TC01, count, returnType, reasonId, managerId, billNum, storeId, item_id, item_serial });

					StringBuilder queryItemSql = new StringBuilder("select rwid from pos_bill_item where bill_num = ? and store_id = ? and setmeal_id = ? and item_serial = ?");
					SqlRowSet rwidRs = posDishDao.query4SqlRowSet(queryItemSql.toString(), new Object[]
					{ billNum, storeId, item_id, item_serial });
					while (rwidRs.next())
					{
						idlist.add(rwidRs.getString("rwid"));
					}
				}
				else
				{
					updateItemSql.append(" and rwid = ?");
					posDishDao.update(updateItemSql.toString(), new Object[]
					{ SysDictionary.ITEM_REMARK_TC01, count, returnType, reasonId, managerId, billNum, storeId, rwid });
					idlist.add(String.valueOf(rwid));
				}
				offsetRwid = rwid;
				offsetSerial = item_serial;
			}
			else if (count < item_count)
			{
				// 查询最大点菜序号
				StringBuilder querySerialSql = new StringBuilder("select coalesce(max(item_serial),0)+1 as serial from pos_bill_item where bill_num = ? and store_id = ?");
				Integer maxItemSerial = posDishDao.queryForInt(querySerialSql.toString(), new Object[]
				{ billNum, storeId });

				if (SysDictionary.ITEM_PROPERTY_SETMEAL.equalsIgnoreCase(item_property))
				{
					StringBuilder updateBillItemSql = new StringBuilder("update pos_bill_item set item_count=(item_count-(assist_num*?)),return_count=? where tenancy_id = ? and store_id = ? and bill_num = ? and setmeal_id = ? and setmeal_rwid = ?");
					posDishDao.update(updateBillItemSql.toString(), new Object[]
					{ count, null, tenancyId, storeId, billNum, item_id, item_serial });

					// 新增退菜明细
					StringBuilder insertBillItemSql = new StringBuilder();
					insertBillItemSql.append(" insert into pos_bill_item(tenancy_id,store_id,bill_num,details_id,item_id,item_num,item_name,item_english,item_unit_id,item_unit_name,stable_code,table_code,pushmoney_way,proportion,assist_num,");
					insertBillItemSql.append(" assist_money,method_money,item_price,discount_state,discount_rate,discount_mode_id,item_class_id,item_property,item_taste,seat_num,ticket_num,sale_mode,gqcj_tag,kvscp_tag,");
					insertBillItemSql.append(" discount_reason_id,is_showitem,integraloffset,remark,third_price,setmeal_id,is_setmeal_changitem,assist_item_id,group_id,setmeal_group_id,report_date,item_shift_id,order_remark,batch_num,");
					insertBillItemSql.append(" yrwid,item_count,return_count,item_serial,setmeal_rwid,item_remark,item_time,item_mac_id,waiter_num,manager_num,return_type,returngive_reason_id,");
					insertBillItemSql.append(" item_amount,real_amount,discount_amount,single_discount_amount,discountr_amount,print_tag,waitcall_tag,upload_tag,opt_num,discount_num,single_discount_rate,count_rate,origin_item_price)");
					insertBillItemSql.append(" select tenancy_id,store_id,bill_num,details_id,item_id,item_num,item_name,item_english,item_unit_id,item_unit_name,stable_code,table_code,pushmoney_way,proportion,assist_num,");
					insertBillItemSql.append(" assist_money,method_money,item_price,discount_state,discount_rate,discount_mode_id,item_class_id,item_property,item_taste,seat_num,ticket_num,sale_mode,gqcj_tag,kvscp_tag,");
					insertBillItemSql.append(" discount_reason_id,is_showitem,integraloffset,remark,third_price,setmeal_id,is_setmeal_changitem,assist_item_id,group_id,setmeal_group_id,report_date,item_shift_id,order_remark,batch_num,");
					insertBillItemSql.append(" rwid,(assist_num*?),(assist_num*?),?,?,?,?,?,?,?,?,?,0,0,0,0,0,null,0,0,opt_num,discount_num,single_discount_rate,count_rate,origin_item_price");
					insertBillItemSql.append(" from pos_bill_item where tenancy_id = ? and store_id = ? and bill_num = ? and setmeal_id = ? and setmeal_rwid = ? order by rwid");

					posDishDao.update(insertBillItemSql.toString(), new Object[]
					{ count, count, maxItemSerial, maxItemSerial, SysDictionary.ITEM_REMARK_TC01, currentTime, posNum, optNum, managerId, returnType, reasonId, tenancyId, storeId, billNum, item_id, item_serial });

					// 新增做法口味
					StringBuilder insertItemKWZFSql = new StringBuilder();
					insertItemKWZFSql.append(" insert into pos_zfkw_item(tenancy_id,store_id,bill_num,item_id,type,zfkw_id,zfkw_name,amount,remark,print_id,report_date,pos_num,last_updatetime,item_num,rwid,upload_tag)");
					insertItemKWZFSql.append(" select zf.tenancy_id,zf.store_id,zf.bill_num,zf.item_id,zf.type,zf.zfkw_id,zf.zfkw_name,zf.amount,zf.remark,zf.print_id,zf.report_date,?,?,?,bi.rwid,'0' from pos_zfkw_item zf");
					insertItemKWZFSql.append(" left join pos_bill_item bi on zf.tenancy_id=bi.tenancy_id and zf.store_id=bi.store_id and zf.bill_num=bi.bill_num and zf.rwid=bi.yrwid");
					insertItemKWZFSql.append(" where bi.tenancy_id = ? and bi.store_id = ? and bi.bill_num = ? and bi.setmeal_id = ? and bi.setmeal_rwid = ? order by rwid");

					posDishDao.update(insertItemKWZFSql.toString(), new Object[]
					{ posNum, currentTime, maxItemSerial, tenancyId, storeId, billNum, item_id, item_serial });

					String queryNewRwidSql = new String("select rwid,item_property from pos_bill_item where tenancy_id = ? and store_id = ? and bill_num = ? and setmeal_id = ? and setmeal_rwid = ? order by rwid");
					Integer newRwid = 0;
					SqlRowSet rsRwid = posDishDao.query4SqlRowSet(queryNewRwidSql, new Object[]
					{ tenancyId, storeId, billNum, item_id, maxItemSerial });
					while (rsRwid.next())
					{
						newRwid = rsRwid.getInt("rwid");
						idlist.add(String.valueOf(newRwid));

						if (SysDictionary.ITEM_PROPERTY_SETMEAL.equalsIgnoreCase(rsRwid.getString("item_property")))
						{
							offsetRwid = newRwid;
						}
					}
				}
				else
				{
					// 修改原明细
					StringBuilder updateBillItemSql = new StringBuilder("update pos_bill_item set item_count=?,return_count=? where rwid = ? and bill_num = ? and store_id = ?");
					posDishDao.update(updateBillItemSql.toString(), new Object[]
					{ DoubleHelper.sub(item_count, count, scale), null, rwid, billNum, storeId });

					// 新增退菜明细
					StringBuilder insertBillItemSql = new StringBuilder();
					insertBillItemSql.append(" insert into pos_bill_item(tenancy_id,store_id,bill_num,details_id,item_id,item_num,item_name,item_english,item_unit_id,item_unit_name,stable_code,table_code,pushmoney_way,proportion,assist_num,");
					insertBillItemSql.append(" assist_money,method_money,item_price,discount_state,discount_rate,discount_mode_id,item_class_id,item_property,item_taste,seat_num,ticket_num,sale_mode,gqcj_tag,kvscp_tag,");
					insertBillItemSql.append(" discount_reason_id,is_showitem,integraloffset,remark,third_price,setmeal_id,is_setmeal_changitem,assist_item_id,group_id,setmeal_group_id,");
					insertBillItemSql.append(" item_time,report_date,item_shift_id,item_mac_id,waiter_num,yrwid,item_serial,setmeal_rwid,manager_num,return_type,returngive_reason_id,item_remark,order_remark,return_count,");
					insertBillItemSql.append(" item_count,item_amount,real_amount,discount_amount,single_discount_amount,discountr_amount,print_tag,waitcall_tag,upload_tag,batch_num,opt_num,discount_num,single_discount_rate,count_rate,origin_item_price)");
					insertBillItemSql.append(" select tenancy_id,store_id,bill_num,details_id,item_id,item_num,item_name,item_english,item_unit_id,item_unit_name,stable_code,table_code,pushmoney_way,proportion,assist_num,");
					insertBillItemSql.append(" assist_money,method_money,item_price,discount_state,discount_rate,discount_mode_id,item_class_id,item_property,item_taste,seat_num,ticket_num,sale_mode,gqcj_tag,kvscp_tag,");
					insertBillItemSql.append(" discount_reason_id,is_showitem,integraloffset,remark,third_price,setmeal_id,is_setmeal_changitem,assist_item_id,group_id,setmeal_group_id,");
					insertBillItemSql.append(" ?,?,?,?,?,?,?,?,?,?,?,?,order_remark,?,?,?,?,?,?,?,null,'0','0',batch_num,opt_num,discount_num,single_discount_rate,count_rate,origin_item_price");
					insertBillItemSql.append(" from pos_bill_item where rwid = ? and bill_num = ? and store_id = ?");

					posDishDao.update(insertBillItemSql.toString(), new Object[]
					{ currentTime, reportDate, shiftId, posNum, optNum, rwid, maxItemSerial, 0d, managerId, returnType, reasonId, SysDictionary.ITEM_REMARK_TC01, count, count, 0d, 0d, 0d, 0d, 0d, rwid, billNum, storeId });

					String queryNewRwidSql = new String("select currval('pos_bill_item_rwid_seq'::regclass) as rwid from pos_bill_item");
					Integer newRwid = 0;
					SqlRowSet rsRwid = posDishDao.query(tenancyId, queryNewRwidSql.toString());
					if (rsRwid.next())
					{
						newRwid = rsRwid.getInt("rwid");
						idlist.add(String.valueOf(newRwid));
					}

					// 新增做法口味
					StringBuilder insertItemKWZFSql = new StringBuilder();
					insertItemKWZFSql.append(" insert into pos_zfkw_item(tenancy_id,store_id,bill_num,item_id,type,zfkw_id,zfkw_name,amount,remark,print_id,report_date,pos_num,last_updatetime,item_num,rwid,upload_tag)");
					insertItemKWZFSql.append(" select tenancy_id,store_id,bill_num,item_id,type,zfkw_id,zfkw_name,amount,remark,print_id,?,?,?,?,?,'0' from pos_zfkw_item");
					insertItemKWZFSql.append(" where rwid = ? and bill_num = ? and store_id = ?");

					posDishDao.update(insertItemKWZFSql.toString(), new Object[]
					{ reportDate, posNum, currentTime, maxItemSerial, newRwid, rwid, billNum, storeId });

					offsetRwid = newRwid;
					// maxItemSerial++;
				}

				offsetSerial = maxItemSerial;
			}

			// 新增退菜明细
			StringBuilder insertBillItemSql = new StringBuilder();
			insertBillItemSql.append(" insert into pos_bill_item(tenancy_id,store_id,bill_num,details_id,item_id,item_num,item_name,item_english,item_unit_id,item_unit_name,stable_code,table_code,pushmoney_way,proportion,assist_num,");
			insertBillItemSql.append(" assist_money,method_money,item_price,discount_state,discount_rate,discount_mode_id,item_class_id,item_property,item_taste,seat_num,ticket_num,sale_mode,gqcj_tag,kvscp_tag,");
			insertBillItemSql.append(" discount_reason_id,is_showitem,integraloffset,remark,third_price,setmeal_id,is_setmeal_changitem,assist_item_id,group_id,setmeal_group_id,");
			insertBillItemSql.append(" item_time,report_date,item_shift_id,item_mac_id,waiter_num,yrwid,item_serial,setmeal_rwid,manager_num,return_type,returngive_reason_id,item_remark,order_remark,return_count,");
			insertBillItemSql.append(" item_count,item_amount,real_amount,discount_amount,single_discount_amount,discountr_amount,print_tag,waitcall_tag,upload_tag,batch_num,opt_num,discount_num,single_discount_rate,count_rate,origin_item_price)");
			insertBillItemSql.append(" select tenancy_id,store_id,bill_num,details_id,item_id,item_num,item_name,item_english,item_unit_id,item_unit_name,stable_code,table_code,pushmoney_way,proportion,assist_num,");
			insertBillItemSql.append(" assist_money,method_money,item_price,discount_state,discount_rate,discount_mode_id,item_class_id,item_property,item_taste,seat_num,ticket_num,sale_mode,gqcj_tag,kvscp_tag,");
			insertBillItemSql.append(" discount_reason_id,is_showitem,integraloffset,remark,third_price,setmeal_id,is_setmeal_changitem,assist_item_id,group_id,setmeal_group_id,");
			insertBillItemSql.append(" ?,?,?,?,?,?,?,?,?,?,?,?,order_remark,-item_count,-item_count,0,0,0,0,0,null,'0','0',batch_num,opt_num,discount_num,single_discount_rate,count_rate,origin_item_price");
			insertBillItemSql.append(" from pos_bill_item where rwid = ? and bill_num = ? and store_id = ?");

			// 新增做法口味
			StringBuilder insertItemKWZFSql = new StringBuilder();
			insertItemKWZFSql.append(" insert into pos_zfkw_item(tenancy_id,store_id,bill_num,item_id,type,zfkw_id,zfkw_name,amount,remark,print_id,report_date,pos_num,last_updatetime,item_num,rwid,upload_tag)");
			insertItemKWZFSql.append(" select tenancy_id,store_id,bill_num,item_id,type,zfkw_id,zfkw_name,amount,remark,print_id,?,?,?,?,?,'0' from pos_zfkw_item");
			insertItemKWZFSql.append(" where rwid = ? and bill_num = ? and store_id = ?");

			// 判断是否为套餐
			if (SysDictionary.ITEM_PROPERTY_SETMEAL.equalsIgnoreCase(item_property))
			{
				String queryNewRwidSql = new String("select currval('pos_bill_item_rwid_seq'::regclass) as rwid from pos_bill_item");

				StringBuilder qureyItemDetailSql = new StringBuilder("select rwid,item_id,coalesce(return_count,0) as return_count,item_price,assist_money,method_money,item_time from pos_bill_item pbi where setmeal_id = ? and item_serial = ? and  bill_num = ? and store_id = ? order by rwid");
				SqlRowSet rs = posDishDao.query4SqlRowSet(qureyItemDetailSql.toString(), new Object[]
				{ item_id, offsetSerial, billNum, storeId });

				SqlRowSet rsRwid = null;
				while (rs.next())
				{
					Integer detailRwid = rs.getInt("rwid");

					posDishDao.update(insertBillItemSql.toString(), new Object[]
					{ currentTime, reportDate, shiftId, posNum, optNum, detailRwid, offsetSerial, offsetSerial, managerId, returnType, reasonId, SysDictionary.ITEM_REMARK_CJ05, detailRwid, billNum, storeId });

					// 获取cc
					Integer detailNewRwid = 0;
					rsRwid = posDishDao.query(tenancyId, queryNewRwidSql.toString());
					if (rsRwid.next())
					{
						detailNewRwid = rsRwid.getInt("rwid");
					}
					// 新增做法口味
					posDishDao.update(insertItemKWZFSql.toString(), new Object[]
					{ reportDate, posNum, currentTime, offsetSerial, detailNewRwid, detailRwid, billNum, storeId });

				}
			}
			else
			{
				posDishDao.update(insertBillItemSql.toString(), new Object[]
				{ currentTime, reportDate, shiftId, posNum, optNum, offsetRwid, offsetSerial, 0d, managerId, returnType, reasonId, SysDictionary.ITEM_REMARK_CJ05, offsetRwid, billNum, storeId });

				String queryNewRwidSql = new String("select currval('pos_bill_item_rwid_seq'::regclass) as rwid from pos_bill_item");
				Integer newRwid = 0;
				SqlRowSet rsRwid = posDishDao.query(tenancyId, queryNewRwidSql.toString());
				if (rsRwid.next())
				{
					newRwid = rsRwid.getInt("rwid");
				}

				posDishDao.update(insertItemKWZFSql.toString(), new Object[]
				{ reportDate, posNum, currentTime, offsetSerial, newRwid, offsetRwid, billNum, storeId });
			}

////			if ("android_pad".equals(agent) || "cc_order".equals(agent)|| "win_pos".equals(agent))
////			{
//				List<Map<String, Object>> soldOutList1 = new ArrayList<Map<String, Object>>();
//				Data data1 = new Data();
//				List<Map<String, Object>> item2 = new ArrayList<>();
//				//2018-01-10 注释修改
//				//退菜：把点的两份汉堡套餐和两份干锅鸡杂都退掉，查看汉堡套餐的沽清数量为4.预期结果：汉堡套餐沽清数量应该为2
//				//1.干锅排骨沽清2分 干锅套餐关联2分  下1份排骨  排骨1 干锅套餐1 退菜 则 干锅套餐2  排骨1
//				List<JSONObject> list = posDao.findComboItemId(item_id + "");
//				String itemStr = listToString(list);
//				List<JSONObject> list1 = posDao.getSoldOutComboId(item_id, itemStr);
//				
//				/*if (list1 != null && list1.size() > 0)  //1.干锅排骨沽清2分 干锅套餐关联2分  下1份排骨  排骨1 干锅套餐1 退菜 则 干锅套餐2  排骨1
//				{
//					for (JSONObject jsonObject : list1)
//					{
//						Map<String, Object> map = new HashMap<String, Object>();
//						map.put("item_id", jsonObject.get("item_id"));
//						map.put("num", count);
//						item2.add(map);
//					}
//				}
//				else
//				{
//					Map<String, Object> map = new HashMap<String, Object>();
//					map.put("item_id", item_id);
//					map.put("num", count);
//					item2.add(map);
//				}*/
//				
//				List<JSONObject> soldOuts = posDao.getBatchNumByBillNum(tenancyId,billNum,item_id.toString());
//				if(soldOuts==null || soldOuts.size()==0){
//					soldOuts = posDao.getBatchNumByBillNum(tenancyId,billNum,"");
//					if(list1 != null && list1.size()>0 && soldOuts.size()==1){ //沽清套餐 下单套餐   固定菜品 与 主菜 重复沽清
//						soldOuts=null;
//					}
//				}
//				if(soldOuts!=null && soldOuts.size()>0){
//					for (JSONObject jsonObject : soldOuts)
//					{
//						Map<String, Object> map = new HashMap<String, Object>();
//						map.put("item_id", jsonObject.get("item_id"));
//						map.put("num", jsonObject.get("item_count"));
//						item2.add(map);
//					}
//				}else{
//					Map<String, Object> map = new HashMap<String, Object>();
//					map.put("item_id", item_id);
//					map.put("num", count);
//					item2.add(map);
//				}
//				 
//
//				data1.setTenancy_id(tenancyId);
//				data1.setStore_id(storeId);
//				data1.setOper(Oper.check);
//				data1.setSource(agent);
//				Map<String, Object> map2 = new HashMap<String, Object>();
//				map2.put("report_date", DateUtil.formatDate(reportDate));
//				map2.put("pos_num", posNum);
//				map2.put("shift_id", shiftId);
//				map2.put("opt_num", optNum);
//				map2.put("chanel", SysDictionary.CHANEL_MD01);
//				map2.put("mode", "1");
//				map2.put("item", item2);
//				map2.put("oper_name","retreat_item");
//				soldOutList1.add(map2);
//				data1.setData(soldOutList1);
//				
//				soldOutService.modSoldOutCount(data1);
////			}
			
			JSONObject soldOutJson = new JSONObject();
			soldOutJson.put("item_id", item_id);
			soldOutJson.put("num", count);
			soldOutList.add(soldOutJson);
		}
		
		Double paymentAmout = getPaymentAmout(subtotal, discountkAmount, discountrAmount, giviAmount, malingAmount, retreatItemAmount, retreatItemDiscountAmount);
		if (0d < discountrAmount && 0d > paymentAmout)
		{
			StringBuilder updateSql = new StringBuilder("update pos_bill set discountr_amount = 0 where tenancy_id=? and store_id=? and bill_num=?");
			posDishDao.update(updateSql.toString(), new Object[]
			{ tenancyId, storeId, billNum });
		}
		
		// 计算金额
		this.calcAmount(tenancyId, storeId, billNum);

		// 修改账单退菜金额，退菜金额应该为菜品金额，不是real_amount(实际金额)
		String updateReturnAmountSql = new String("update pos_bill set return_amount = (select abs(sum(pbi.item_amount)) from pos_bill_item pbi where pbi.item_remark = ? and pbi.item_property <>'MEALLIST' and pbi.bill_num = ? and pbi.store_id = ?) where bill_num = ? and store_id = ? ");
		posDishDao.update(updateReturnAmountSql.toString(), new Object[]
		{ SysDictionary.ITEM_REMARK_TC01, billNum, storeId, billNum, storeId });

		// 送厨打
		json.put("tenancy_id", tenancyId);
		json.put("store_id", storeId);
		json.put("bill_num", billNum);
		json.put("oper", "退菜");
		json.put("oper_type", SysDictionary.ITEM_REMARK_TC01);
		json.put("rwids", idlist.toString().replaceAll("\\[", "").replaceAll("\\]", ""));

        //是否并台之后操作退菜 添加并台之后操作标识 combined_after
		posDishDao.checkCombineTable(tenancyId, storeId,billNum);
		// 写日志
		posDishDao.savePosLog(tenancyId, storeId, posNum, optNum, optName, shiftId, reportDate, Constant.TITLE, "退菜", "账单编号:" + billNum, "");

//		if ("android_pad".equals(agent) || "cc_order".equals(agent))
//		{
			try
			{
				if (null != soldOutList && 0 < soldOutList.size())
				{
					Map<String, Object> map2 = new HashMap<String, Object>();
					map2.put("report_date", DateUtil.formatDate(reportDate));
					map2.put("pos_num", posNum);
					map2.put("shift_id", shiftId);
					map2.put("opt_num", optNum);
					map2.put("chanel", SysDictionary.CHANEL_MD01);
					map2.put("mode", "1");
					map2.put("item", soldOutList);
					map2.put("oper_name","retreat_item");
					
					List<Map<String, Object>> soldOutList1 = new ArrayList<Map<String, Object>>();
					soldOutList1.add(map2);
					
					Data data1 = new Data();
					data1.setTenancy_id(tenancyId);
					data1.setStore_id(storeId);
					data1.setOper(Oper.check);
					data1.setSource(agent);
					data1.setData(soldOutList1);
					
					soldOutService.modSoldOutCount(data1);
					
					soldOutList = soldOutDao.getSoldOut(tenancyId, storeId);
					
					Data cjData = new Data();
					cjData.setType(Type.SOLD_OUT);
					cjData.setOper(Oper.update);
					cjData.setData(soldOutList);
					CometDataNoticeClientRunnable noticeClientRunnable = new CometDataNoticeClientRunnable(cjData);
					Thread noticeThread = new Thread(noticeClientRunnable);
					noticeThread.start();
					Comet4jUtil.sendMessage2All(Comet4jUtil.POS_ANDROID, JSONObject.fromObject(cjData).toString());
				}
			}
			catch (Exception e)
			{
				e.printStackTrace();
				logger.error("推送失败:", e);
			}
//		}

//		// 数据上传
//		upload(tenancyId, storeId + "", "", table_code, "", billNum);
		
		this.updatePosBillForUpload(tenancyId, storeId, billNum);

		result.setCode(Constant.CODE_SUCCESS);
		result.setMsg(Constant.RETREAT_DISH_SUCCESS);
	}

	public String listToString(List<JSONObject> list)
	{
		boolean flag = false;
		StringBuilder result = new StringBuilder();
		for (JSONObject jsonObject : list)
		{
			String itemIdStr = jsonObject.get("item_id").toString();
			if (flag)
			{
				result.append(",");
			}
			else
			{
				flag = true;
			}
			result.append(itemIdStr);
		}
		return result.toString();
	}

	@SuppressWarnings("unused")
	@Override
	public void checkRetreatFood(String tenancyId, int storeId, List<?> param, Data result) throws Exception
	{
		if (param == null || param.isEmpty())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}
		JSONObject para = JSONObject.fromObject(param.get(0));

		if (Tools.isNullOrEmpty(para.opt("report_date")))
		{
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_REPORT_DATE);
		}
		if (Tools.isNullOrEmpty(para.opt("bill_num")))
		{
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_BILL_NUM);
		}

		final int scale = 4;
		Date reportDate = DateUtil.parseDate(ParamUtil.getDateStringValue(para, "report_date"));
		String posNum = para.optString("pos_num");
		String optNum = para.optString("opt_num");
		Integer shiftId = para.optInt("shift_id");

		String billNum = para.optString("bill_num");

		JSONObject billJson = posDishDao.getPosBillByBillnum(tenancyId, storeId, billNum);

		Double subtotal = 0d;
		Double discountkAmount = 0d;
		Double discountrAmount = 0d;
		Double giviAmount = 0d;
		Double malingAmount = 0d;
		if (null != billJson)
		{
			subtotal = billJson.optDouble("subtotal");
			discountkAmount = billJson.optDouble("discountk_amount");
			discountrAmount = billJson.optDouble("discountr_amount");
			giviAmount = billJson.optDouble("givi_amount");
			malingAmount = billJson.optDouble("maling_amount");

			// 账单有折扣不能退菜
			if (billJson.optDouble("discount_rate") != 0d && billJson.optDouble("discount_rate") != 100d && billJson.optInt("discount_case_id") > 0)
			{
				SystemException.getInstance(PosErrorCode.BILL_EXIST_DISCOUNT_NOT_RETREATFOOD);
			}
		}

		Double retreatItemAmount = 0d;
		Double retreatItemDiscountAmount = 0d;

		for (Object obj : para.optJSONArray("item"))
		{
			JSONObject itemJson = JSONObject.fromObject(obj);
			if (Tools.isNullOrEmpty(itemJson.opt("rwid")))
			{
				throw SystemException.getInstance(PosErrorCode.NOT_NULL_RWID);
			}
			Integer rwid = itemJson.optInt("rwid");

			double count = 0;
			if (Tools.hv(itemJson.opt("count")))
			{
				count = itemJson.optDouble("count");
			}
			double item_count = 0d;
			double return_count = 0d;
			String item_remark = null;
			String item_property = null;

			Double itemAmount = 0d;
			Double itemDiscountAmount = 0d;
			Double itemCount = 0d;

			List<JSONObject> itemList = posDishDao.getPosBillItemByRwid(tenancyId, storeId, billNum, rwid);

			if (null != itemList && itemList.size() > 0)
			{
				JSONObject billItemJson = itemList.get(0);

				itemAmount = billItemJson.optDouble("item_amount");
				itemDiscountAmount = billItemJson.optDouble("discount_amount");

				item_count = billItemJson.optDouble("item_count");
				return_count = billItemJson.optDouble("return_count");
				itemCount = Scm.psub(item_count, return_count);

				item_remark = billItemJson.optString("item_remark");
				item_property = billItemJson.optString("item_property");

			}
			else
			{
				throw SystemException.getInstance(PosErrorCode.NOT_FIND_RETREAT_FOOD);
			}

			if (Tools.hv(item_remark) && SysDictionary.ITEM_REMARK_TC01.equalsIgnoreCase(item_remark))
			{
				// 判断pos_bill_item.item_remark=’ TC01’的，msg返回“该菜品已经是退菜”
				throw SystemException.getInstance(PosErrorCode.HAVING_RETREAT_FOOD);
			}
			else if (Tools.hv(item_remark) && SysDictionary.ITEM_REMARK_FS02.equalsIgnoreCase(item_remark))
			{
				// 判断pos_bill_item.item_remark=’FS02’的，msg返回“该菜品已经奉送”
				throw SystemException.getInstance(PosErrorCode.HAVING_GIVE_FOOD);
			}
			else if (Tools.hv(item_remark) && SysDictionary.ITEM_REMARK_QX04.equalsIgnoreCase(item_remark))
			{
				// 判断pos_bill_item.item_remark=’QX04’的，msg返回“该菜品已经取消”
				throw SystemException.getInstance(PosErrorCode.HAVING_CANC_FOOD);
			}
			else if (Tools.hv(item_remark) && SysDictionary.ITEM_REMARK_CJ05.equalsIgnoreCase(item_remark))
			{
				// 判断pos_bill_item.item_remark=’CJ05’的，msg返回“该菜品已经取消”
				throw SystemException.getInstance(PosErrorCode.HAVING_OFFSET_FOOD);
			}

			if (Tools.hv(item_property) && item_property.equalsIgnoreCase("MEALLIST"))
			{
				// 判断pos_bill_item. item_property=’ MEALLIST’的，msg返回“明细菜品不允许退菜”
				throw SystemException.getInstance(PosErrorCode.NOT_PERMIT_RETREAT_FOOD);
			}

			if (count > item_count)
			{
				// 判断入参中的退菜数量是否大于菜品数量，如果是msg返回“退菜数量不能大于菜品数量”
				throw SystemException.getInstance(PosErrorCode.RETREAT_COUNT_MORE_THAN_EXISTS);
			}

			retreatItemAmount = DoubleHelper.add(retreatItemAmount, DoubleHelper.mul(DoubleHelper.div(itemAmount, itemCount, scale), count, scale), scale);
			retreatItemDiscountAmount = DoubleHelper.add(retreatItemDiscountAmount, DoubleHelper.mul(DoubleHelper.div(itemDiscountAmount, itemCount, scale), count, scale), scale);
		}

		Double paymentAmout = getPaymentAmout(subtotal, discountkAmount, discountrAmount, giviAmount, malingAmount, retreatItemAmount, retreatItemDiscountAmount);

		String isCancleDiscountr = "0";

		//折让后，退菜，最终价格为0时，提示折让金额自动取消
		if (0d < discountrAmount && 0d > paymentAmout)
		{
			isCancleDiscountr = "1";
		}

		JSONObject resultJson = new JSONObject();
		resultJson.put("is_cancle_discountr", isCancleDiscountr);

		List<JSONObject> resultList = new ArrayList<JSONObject>();
		resultList.add(resultJson);

		result.setData(resultList);
		result.setCode(Constant.CODE_SUCCESS);
		result.setMsg(Constant.RETREAT_DISH_SUCCESS);
	}
	
	private Double getPaymentAmout(Double subtotal,Double discountkAmount,Double discountrAmount,Double giveAmount,Double malingAmount,Double itemAmount,Double itemDiscountAmount)
	{
		int scale = 4;
		// 实付金额=账单金额+抹零金额-(折扣金额+折让金额)-奉送
		// Double paymentAmout = (subtotal-itemAmount) + malingAmount - ((discountkAmount -itemDiscountAmount) + discountrAmount) - giviAmount;
		
		Double discountAmount = DoubleHelper.add(DoubleHelper.sub(discountkAmount, itemDiscountAmount, scale), discountrAmount, scale);
		Double retreatSubtotal = DoubleHelper.sub(subtotal, itemAmount, scale);
		Double paymentAmout = DoubleHelper.sub(DoubleHelper.sub(DoubleHelper.add(retreatSubtotal, malingAmount, scale), discountAmount, scale), giveAmount, scale);
		return paymentAmout;
	}
}
