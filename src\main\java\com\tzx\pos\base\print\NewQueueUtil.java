package com.tzx.pos.base.print;

/**
 *
 * <AUTHOR>
 * 2015年7月27日-下午4:00:21
 */
public class NewQueueUtil
{
	private static final NewQueue newQueue = new NewQueue();
	
	public static NewQueue getQueue()
	{
		return newQueue;
	}
	
	public static void push(Object obj)
	{
		newQueue.push(obj);
	}
	
	public static Object pull()
	{
		return newQueue.pull();
	}
	
	public static int size()
	{
		return newQueue.size();
	}
}
