package com.tzx.thirdpay.bo.imp;

import java.io.File;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

import javax.annotation.Resource;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.tzx.base.common.util.PosPropertyUtil;
import com.tzx.base.entity.PosThirdPaymentRefundEntity;
import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.HttpUtil;
import com.tzx.framework.common.util.Tools;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.constant.SysParameter;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.base.util.JsonUtil;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.base.util.PathUtil;
import com.tzx.pos.base.util.QrCodeUtils;
import com.tzx.pos.bo.dto.PosThirdPaymentOrderEntity;
import com.tzx.thirdpay.bo.ThirdPaymentRefundService;
import com.tzx.thirdpay.bo.ThirdPaymentService;
import com.tzx.thirdpay.po.springjdbc.dao.ThirdPaymentDao;

import net.sf.json.JSONObject;

/** 三方支付
 * <AUTHOR>
 *
 */
@Service(ThirdPaymentService.NAME)
public class ThirdPaymentServiceImp implements ThirdPaymentService
{
	private static final String		REQUEST_PATH			= "/payment/news/post";

	private static final String		PHYSICAL_PATH			= PathUtil.getWebRootPath() + File.separator + "img" + File.separator + "qr_code" + File.separator;

	private static final String		LOGO_PATH				= PHYSICAL_PATH + "logo" + File.separator;

	private static final Logger		posThirdPaymentLogger	= Logger.getLogger("pos_third_payment");

//	protected static final Logger	logger					= Logger.getLogger(ThirdPaymentServiceImp.class);

	private static int				CONNECTION_TIMEOUT			= 3000;
	private static int				SOCKET_TIMEOUT				= 5000;

	private static int				QUERY_CONNECTION_TIMEOUT	= 5000;
	private static int				QUERY_SOCKET_TIMEOUT		= 10000;

	@Resource(name = ThirdPaymentRefundService.NAME)
	private ThirdPaymentRefundService		thirdPaymentRefundService;
	
	@Resource(name = ThirdPaymentDao.NAME)
	private ThirdPaymentDao			paymentDao;

	@Override
	public String getRequestPath() throws Exception
	{
		return PosPropertyUtil.getMsg("saas.url") + REQUEST_PATH;
	}
	
	/**
	 * @param requestData
	 * @param connectionTimeout
	 * @param socketTimeout
	 * @return
	 * @throws Exception
	 */
	private Data sendPostRequest(Data requestData,int connectionTimeout,int socketTimeout) throws Exception
	{
		// 根据总部返回结果 进行相应的业务出来
		String paramStr = JSONObject.fromObject(requestData).toString();
//		logger.info("<第三方接口请求体==>" + paramStr);
		posThirdPaymentLogger.info("<第三方接口请求地址==>" + getRequestPath());
		posThirdPaymentLogger.info("<第三方接口请求体==>" + paramStr);
		String result = HttpUtil.sendPostRequest(getRequestPath(), paramStr, connectionTimeout, socketTimeout);
//		logger.info("<第三方接口返回体==>" + result);
		posThirdPaymentLogger.info("<第三方接口返回体==>" + result);

		Data responseData = null;// 总部返回结果集
		if (Tools.hv(result))
		{
			responseData = ParamUtil.stringToData(result);
			if (Constant.CODE_SUCCESS == responseData.getCode())
			{
				List<?> responseList = responseData.getData();
				if (responseList == null || 0 == responseList.size() || responseList.isEmpty())
				{
					responseData.setCode(PosErrorCode.THIRD_PAYMENT_FAILURE.getNumber());
					responseData.setMsg(PosErrorCode.THIRD_PAYMENT_FAILURE.getMessage());
				}
			}
		}
		else
		{// 返回为空
			responseData = Data.get(requestData);
			responseData.setCode(Constant.CODE_CONN_EXCEPTION);
			responseData.setMsg(Constant.GET_PAYMENT_WAY_MESSAGE_IS_NULL);
		}
		responseData.setSuccess(Constant.CODE_SUCCESS == responseData.getCode());
		return responseData;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public Data precreatePayment(String tenantId, int storeId, JSONObject paramJson, String path) throws Exception
	{
		Timestamp currentTimes = DateUtil.currentTimestamp();
		String aidOrderNum = paramJson.optString("order_no");
//		String billNum = paramJson.optString("bill_num");
		Double settleAmount = paramJson.optDouble("settle_amount");
		Double totalAmount = paramJson.optDouble("total_amount");
		Integer paymentId = paramJson.optInt("payment_id");
		String remark = paramJson.optString("subject");
		
//		/**
//		 * 点菜器第三方支付请求支付的时候账单号是单号+时间A 导致发起退款的时候，只能获取到单号+时间B的账单号， 关联不上单号+时间A的账单号
//		 */
//		if (aidOrderNum.indexOf("@") == -1)
//		{
//			aidOrderNum = aidOrderNum + "@" + String.valueOf(currentTimes.getTime());
//		}

		PosThirdPaymentOrderEntity paymentOrder = paymentDao.getThirdPaymentByAidOrderNum(tenantId, storeId, aidOrderNum, SysDictionary.THIRD_PAY_CHARGE);
		// 根据订单号查询未撤销,未退款的支付记录
		if (null != paymentOrder)
		{
			if ((SysDictionary.THIRD_PAY_STATUS_PAYING.equals(paymentOrder.getStatus())) && (settleAmount == paymentOrder.getSettle_amount() && totalAmount == paymentOrder.getTotal_amount()))
			{// 如果相等
				Data returnData = new Data();
				returnData.setCode(Constant.CODE_SUCCESS);
				returnData.setMsg(Constant.GET_PREPAY_BARCODE_SUCCESS);
				JSONObject data = new JSONObject();
				data.put("qrcode", paymentOrder.getQr_code());
				String fileName = tenantId + "_" + String.valueOf(storeId) + "_" + paymentOrder.getAid_order_num() + ".png";
				String qrCodeUrl = path + "img/qr_code/" + paymentOrder.getPayment_class() + "/" + fileName;
				data.put("qrcode_url", qrCodeUrl);
				data.put("payment_state", SysDictionary.THIRD_PAY_STATUS_PAYING);
				data.put("transaction_no", paymentOrder.getTransaction_no());
				data.put("failure_code", paymentOrder.getFailure_code());
				data.put("failure_msg", paymentOrder.getFailure_msg());
				List<JSONObject> dataList = new ArrayList<JSONObject>();
				dataList.add(data);
				returnData.setData(dataList);

				return returnData;
			}
		}

		// 插入三方记录信息
//		JSONObject storeJson = paymentDao.getOrganById(tenantId, storeId);
		JSONObject payTypeJson = paymentDao.getPaymentWayForOrganByID(tenantId, storeId, paymentId);

		String paymentClass = payTypeJson.optString("payment_class");
		String subject = payTypeJson.optString("org_short_name").trim() + payTypeJson.optString("payment_name");

//		JSONObject insertJson = JSONObject.fromObject(paramJson);
//		insertJson.put("tenancy_id", tenantId);
//		insertJson.put("store_id", storeId);
//		insertJson.put("object_name", SysDictionary.THIRD_PAY_CHARGE);
//		insertJson.put("create_time", DateUtil.format(currentTimes));
//		insertJson.put("payment_class", paymentClass);
//		insertJson.put("aid_order_num", aidOrderNum);
//		insertJson.put("payment_type", SysDictionary.PAYMENT_TYPE_ZS);
//		insertJson.put("is_refunded", false);
//		insertJson.put("is_succeed", false);
//		insertJson.put("status", SysDictionary.THIRD_PAY_STATUS_PAYING);
//		insertJson.put("order_state", SysDictionary.THIRD_PAY_STATUS_PAYING);
//		insertJson.put("query_time", DateUtil.format(currentTimes));
//		insertJson.put("query_count", 0);
//		insertJson.put("subject", subject);
//		insertJson.put("remark", remark);
//		insertJson.put("upload_tag", 1);
		
		paymentOrder = JsonUtil.jsonToBean(paramJson, PosThirdPaymentOrderEntity.class);
		paymentOrder.setTenancy_id(tenantId);
		paymentOrder.setStore_id(storeId);
		paymentOrder.setObject_name(SysDictionary.THIRD_PAY_CHARGE);
		paymentOrder.setCreate_time(currentTimes);
		paymentOrder.setPayment_class(paymentClass);
		paymentOrder.setAid_order_num(aidOrderNum);
		paymentOrder.setPayment_type(SysDictionary.PAYMENT_TYPE_ZS);
		paymentOrder.setStatus(SysDictionary.THIRD_PAY_STATUS_PAYING);
		paymentOrder.setOrder_state(SysDictionary.THIRD_PAY_STATUS_PAYING);
		paymentOrder.setQuery_time(currentTimes);
		paymentOrder.setSubject(subject);
		paymentOrder.setRemark(remark);
		paymentOrder.setOper_type(Type.GET_PREPAY_BARCODE.name());

		int id = paymentDao.insertThirdPaymentOrder(tenantId, paymentOrder);
		paymentOrder.setId(id);
		
		// 组织参数,请求总部
		JSONObject requestJson = new JSONObject();
		requestJson.put("order_no", paymentOrder.getAid_order_num());
		requestJson.put("bill_num", paymentOrder.getOrder_num());
		requestJson.put("amount", paymentOrder.getSettle_amount());
		requestJson.put("pay_type", paymentOrder.getPayment_class());
		requestJson.put("subject", paymentOrder.getSubject());
		requestJson.put("service_type", paymentOrder.getService_type());
		requestJson.put("report_date", DateUtil.formatDate(paymentOrder.getReport_date()));
		requestJson.put("shift", paymentOrder.getShift_id());
		requestJson.put("pos_num", paymentOrder.getPos_num());
		requestJson.put("opt_num", paymentOrder.getOpt_num());
		requestJson.put("channel", paymentOrder.getChannel());
		requestJson.put("currency", paymentOrder.getCurrency_name());
		requestJson.put("body", paymentOrder.getBody());
		requestJson.put("client_ip", paymentOrder.getClient_ip());
		requestJson.put("description", paymentOrder.getDescription());
		requestJson.put("extra", paymentOrder.getExtra());
		if(paramJson.containsKey("goods_detail"))
		{
			requestJson.put("goods_detail", paramJson.optJSONArray("goods_detail"));
		}

		List<JSONObject> requestList = new ArrayList<JSONObject>();
		requestList.add(requestJson);

		Data requestData = Data.get(tenantId, storeId, Constant.CODE_SUCCESS);
		requestData.setType(Type.GET_PREPAY_BARCODE);
		requestData.setOper(Oper.find);
		requestData.setData(requestList);

		boolean is_succeed = false;
		String status = SysDictionary.THIRD_PAY_STATUS_NOOK;// 交易状态
		String order_state = SysDictionary.THIRD_PAY_STATUS_NOOK;// 交易状态
		String transaction_no = "";// 交易流水号
		String qrCode = ""; // 二维码编号

		String request_status = SysDictionary.REQUEST_STATUS_COMPLETE;// 请求状态
		String request_code = "";// 请求错误码 总部给出的
		String request_msg = "";// 请求错误描述 总部给出的
		String failure_code = ""; // 第三方给出的订单的错误码
		String failure_msg = "";// 第三方订单的错误描述

		Data responseData = this.sendPostRequest(requestData, CONNECTION_TIMEOUT, SOCKET_TIMEOUT);
		// 根据总部返回结果处理
		if (Constant.CODE_SUCCESS == responseData.getCode())
		{
			JSONObject responseJson = JSONObject.fromObject(responseData.getData().get(0));
			// 成功:生成二维码
			status = responseJson.optString("payment_state");
			qrCode = responseJson.optString("qrcode");

			if (SysDictionary.THIRD_PAY_STATUS_PAYING.equals(status) && Tools.hv(qrCode))
			{
				is_succeed = false;
				status = SysDictionary.THIRD_PAY_STATUS_PAYING;
				order_state = SysDictionary.THIRD_PAY_STATUS_PAYING;

				transaction_no = responseJson.optString("transaction_no");
				try
				{
					// 存储的物理地址
					String fileName = tenantId + "_" + String.valueOf(storeId) + "_" + aidOrderNum + ".png";
					String imgPath = PHYSICAL_PATH + paymentClass + File.separator + fileName;// 图片存储地址
					String logoPath = LOGO_PATH + paymentClass + "_logo.png";
					String qrCodeUrl = path + "img/qr_code/" + paymentClass + "/" + fileName;
					// 生成带logo的二维码
					QrCodeUtils.encode(qrCode, logoPath, imgPath, true);
					// 向返回客户端的结果中添加二维码地址
					responseJson.put("qrcode_url", qrCodeUrl);
				}
				catch (Exception e)
				{
					// 取消付款
					this.cancelPayment(tenantId, storeId, paymentOrder);

					e.printStackTrace();
					responseData.setCode(PosErrorCode.CREATE_QRCODE_ERROR.getNumber());
					responseData.setMsg(PosErrorCode.CREATE_QRCODE_ERROR.getMessage());
				}
			}
			else 
			{
				is_succeed = true;
				status = SysDictionary.THIRD_PAY_STATUS_FAIL;// 交易状态
				order_state = SysDictionary.THIRD_PAY_STATUS_FAIL;// 交易状态

				failure_code = responseJson.optString("failure_code");
				if (Tools.isNullOrEmpty(failure_code))
				{
					failure_code = String.valueOf(responseData.getCode());
					responseJson.put("failure_code", failure_code);
				}

				failure_msg = responseJson.optString("failure_msg");
				if (Tools.isNullOrEmpty(failure_msg))
				{
					failure_msg = responseData.getMsg();
					responseJson.put("failure_msg", failure_msg);
				}
			}

			List<JSONObject> resultList = new ArrayList<JSONObject>();
			resultList.add(responseJson);
			responseData.setData(resultList);
		}
		else if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
		{
			request_status = SysDictionary.REQUEST_STATUS_FAILURE;
			request_code = String.valueOf(Constant.CODE_CONN_EXCEPTION);
			request_msg = Constant.GET_PAYMENT_WAY_MESSAGE_IS_NULL;

			is_succeed = true;
			status = SysDictionary.THIRD_PAY_STATUS_NOOK;// 交易状态
			order_state = SysDictionary.THIRD_PAY_STATUS_NOOK;// 交易状态

			responseData.setCode(Constant.CODE_CONN_EXCEPTION);
			responseData.setMsg(Constant.GET_PAYMENT_WAY_MESSAGE_IS_NULL);

			// 取消付款
			this.cancelPayment(tenantId, storeId, paymentOrder);
		}
		else
		{
			request_status = SysDictionary.REQUEST_STATUS_FAILURE;
			request_code = String.valueOf(responseData.getCode());
			request_msg = responseData.getMsg();

			is_succeed = true;
			status = SysDictionary.THIRD_PAY_STATUS_FAIL;// 交易状态
			order_state = SysDictionary.THIRD_PAY_STATUS_FAIL;// 交易状态
		}

		// 修改交易记录
		JSONObject updateJson = new JSONObject();
		updateJson.put("is_succeed", is_succeed);
		updateJson.put("transaction_no", transaction_no);
		updateJson.put("qr_code", qrCode);
		updateJson.put("status", status);
		updateJson.put("failure_code", failure_code);
		updateJson.put("failure_msg", failure_msg);
		updateJson.put("request_status", request_status);
		updateJson.put("request_code", request_code);
		updateJson.put("request_msg", request_msg);
		updateJson.put("order_state", order_state);

		paymentDao.updateThirdPaymentOrder(id, tenantId, updateJson);
		// 返回结果记录本地
		return responseData;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public Data barcodePayment(String tenantId, int storeId, JSONObject paramJson) throws Exception
	{
		Timestamp currentTimes = DateUtil.currentTimestamp();
		String aidOrderNum = paramJson.optString("order_no");
//		String billNum = paramJson.optString("bill_num");
		Double settleAmount = paramJson.optDouble("settle_amount");
		Double totalAmount = paramJson.optDouble("total_amount");
		String authcode = ParamUtil.getStringValueByObject(paramJson, "credential");
		String body = ParamUtil.getStringValueByObject(paramJson, "body");
		Integer paymentId = paramJson.optInt("payment_id");
		String remark = ParamUtil.getStringValueByObject(paramJson, "subject");
		
		PosThirdPaymentOrderEntity paymentOrder = paymentDao.getThirdPaymentByAidOrderNum(tenantId, storeId, aidOrderNum, SysDictionary.THIRD_PAY_CHARGE);
		// 根据订单号查询未撤销,未退款的支付记录
		int id = 0;
		String subject = "";
		String paymentClass = "";
		String payType =  null;
		if (null != paymentOrder  && settleAmount == paymentOrder.getSettle_amount() && totalAmount == paymentOrder.getTotal_amount())
		{
			id = paymentOrder.getId();
			subject = paymentOrder.getSubject();
			paymentClass = paymentOrder.getPayment_class();
			if (SysDictionary.THIRD_PAY_STATUS_PAYING.equals(paymentOrder.getStatus()))
			{// 交易成功
				Data returnData = new Data();
				returnData.setCode(Constant.CODE_SUCCESS);
				returnData.setMsg(Constant.PAY_ORDER_BY_CUSTOMER_SUCCESS);
				JSONObject data = new JSONObject();
				data.put("transaction_no", paymentOrder.getTransaction_no());
				data.put("payment_state", SysDictionary.THIRD_PAY_STATUS_SUCCESS);
				data.put("failure_code", paymentOrder.getFailure_code());
				data.put("failure_msg", paymentOrder.getFailure_msg());
				List<JSONObject> dataList = new ArrayList<JSONObject>();
				dataList.add(data);
				returnData.setData(dataList);
				return returnData;
			}
		}
		else
		{
			JSONObject payTypeJson = paymentDao.getPaymentWayForOrganByID(tenantId, storeId, paymentId);

			paymentClass = payTypeJson.optString("payment_class");
			subject = payTypeJson.optString("org_short_name").trim() + payTypeJson.optString("payment_name");

//			JSONObject insertJson = JSONObject.fromObject(paramJson);
//			insertJson.put("tenancy_id", tenantId);
//			insertJson.put("store_id", storeId);
//			insertJson.put("aid_order_num", aidOrderNum);
//			insertJson.put("payment_class", paymentClass);
//			insertJson.put("object_name", SysDictionary.THIRD_PAY_CHARGE);
//			insertJson.put("create_time", DateUtil.format(currentTimes));
//			insertJson.put("payment_type", SysDictionary.PAYMENT_TYPE_BS);
//			insertJson.put("is_refunded", false);
//			insertJson.put("is_succeed", false);
//			insertJson.put("status", SysDictionary.THIRD_PAY_STATUS_PAYING);
//			insertJson.put("order_state", SysDictionary.THIRD_PAY_STATUS_PAYING);
//			insertJson.put("query_time", DateUtil.format(currentTimes));
//			insertJson.put("query_count", 0);
//			insertJson.put("subject", subject);
//			insertJson.put("remark", paramJson.optString("subject"));
//			insertJson.put("upload_tag", 1);
			
			paymentOrder = JsonUtil.jsonToBean(paramJson, PosThirdPaymentOrderEntity.class);
			paymentOrder.setTenancy_id(tenantId);
			paymentOrder.setStore_id(storeId);
			paymentOrder.setAid_order_num(aidOrderNum);
			paymentOrder.setPayment_class(paymentClass);
			paymentOrder.setObject_name(SysDictionary.THIRD_PAY_CHARGE);
			paymentOrder.setCreate_time(currentTimes);
			paymentOrder.setPayment_type(SysDictionary.PAYMENT_TYPE_BS);
			paymentOrder.setStatus(SysDictionary.THIRD_PAY_STATUS_PAYING);
			paymentOrder.setOrder_state(SysDictionary.THIRD_PAY_STATUS_PAYING);
			paymentOrder.setQuery_time(currentTimes);
			paymentOrder.setSubject(subject);
			paymentOrder.setRemark(remark);
			paymentOrder.setOper_type(Type.PAY_ORDER_BY_CUSTOMER.name());
			
			if(SysDictionary.PAYMENT_CLASS_SCAN_CODE_PAY.equals(paymentClass))
			{
				try
				{
//					// 扫码合一,校验授权码,判断是否微信,支付宝
//					Map<String, String> sysParamMap = paymentDao.getSysParameter(tenantId, storeId, SysParameter.ALI_PAY_CODE_CHECK_RULE, SysParameter.WECHAT_PAY_CODE_CHECK_RULE);
//					if (Tools.isNullOrEmpty(sysParamMap.get(SysParameter.ALI_PAY_CODE_CHECK_RULE)) || Tools.isNullOrEmpty(sysParamMap.get(SysParameter.WECHAT_PAY_CODE_CHECK_RULE)))
//					{
//						throw SystemException.getInstance(PosErrorCode.NOT_PAY_CODE_CHECK_RULE_ERROR);
//					}
//
//					if (Pattern.matches(sysParamMap.get(SysParameter.ALI_PAY_CODE_CHECK_RULE), authcode))
//					{
//						paymentClass = SysDictionary.PAYMENT_CLASS_ALI_PAY;
//						payType = SysDictionary.PAYMENT_CLASS_ALI_PAY;
//					}
//					else if (Pattern.matches(sysParamMap.get(SysParameter.WECHAT_PAY_CODE_CHECK_RULE), authcode))
//					{
//						paymentClass = SysDictionary.PAYMENT_CLASS_WECHAT_PAY;
//						payType = SysDictionary.PAYMENT_CLASS_WECHAT_PAY;
//					}
//					else
//					{
//						throw SystemException.getInstance(PosErrorCode.PAYMENT_AUTHCODE_INVALID_ERROR);
//					}
//
//					if (Tools.hv(payType))
//					{
//						JSONObject paymentJson = paymentDao.getPaymentWayForOrganByPaymentClass(tenantId, storeId, payType);
//
//						if (null != paymentJson && paymentJson.containsKey("payment_class"))
//						{
//							subject = paymentJson.optString("org_short_name").trim() + paymentJson.optString("payment_name");
//							body = body.replace(SysDictionary.PAYMENT_CLASS_SCAN_CODE_PAY_NAME, paymentJson.optString("payment_name"));
//							paymentId = paymentJson.optInt("payment_id");
//							
//							paymentOrder.setPayment_id(paymentId);
//							paymentOrder.setPayment_class(paymentClass);
//							paymentOrder.setPay_type(payType);
//							paymentOrder.setSubject(subject);
//							paymentOrder.setBody(body);
//							
//						}
//						else
//						{
//							throw SystemException.getInstance(PosErrorCode.NOT_EXIST_JZID);
//						}
//					}
					
					JSONObject paymentJson = this.getPaymentClassByuAuthcode(tenantId, storeId, authcode);

					payType = paymentJson.optString("payment_class");
					paymentClass = paymentJson.optString("payment_class");
					paymentId = paymentJson.optInt("payment_id");

					subject = paymentJson.optString("org_short_name").trim() + paymentJson.optString("payment_name");
					body = body.replace(SysDictionary.PAYMENT_CLASS_SCAN_CODE_PAY_NAME, paymentJson.optString("payment_name"));

					paymentOrder.setPayment_id(paymentId);
					paymentOrder.setPayment_class(paymentClass);
					paymentOrder.setPay_type(payType);
					paymentOrder.setSubject(subject);
					paymentOrder.setBody(body);
				}
				catch (Exception e)
				{
					//微信/支付宝付款码校验失败,返回支付失败
					Data returnData = Data.get(tenantId, storeId, Constant.CODE_SUCCESS);
					if(e instanceof SystemException)
					{
						returnData.setCode(((SystemException) e).getErrorCode().getNumber());
						returnData.setMsg(((SystemException) e).getErrorCode().getMessage());
					}
					else 
					{
						returnData.setCode(PosErrorCode.OPER_ERROR.getNumber());
						returnData.setMsg("微信/支付宝付款码校验失败,原因:"+e.getMessage());
					}
					
					paymentOrder.setIs_succeed( true);
					paymentOrder.setStatus( SysDictionary.THIRD_PAY_STATUS_FAIL);
					paymentOrder.setOrder_state( SysDictionary.THIRD_PAY_STATUS_FAIL);
					paymentOrder.setExpire_time( currentTimes);
					paymentOrder.setRequest_status( SysDictionary.REQUEST_STATUS_FAILURE);
					paymentOrder.setRequest_code( String.valueOf(returnData.getCode()));
					paymentOrder.setRequest_msg( returnData.getMsg());
					paymentDao.insertThirdPaymentOrder(tenantId, paymentOrder);

					return returnData;
				}
			}
			
			id = paymentDao.insertThirdPaymentOrder(tenantId, paymentOrder);
			paymentOrder.setId(id);
		}
		
		// 组织参数,请求总部
		JSONObject requestJson = new JSONObject();
		requestJson.put("order_no", paymentOrder.getAid_order_num());
		requestJson.put("bill_num", paymentOrder.getOrder_num());
		requestJson.put("service_type", paymentOrder.getService_type());
		requestJson.put("pos_num", paymentOrder.getPos_num());
		requestJson.put("opt_num", paymentOrder.getOpt_num());
		requestJson.put("pay_type", paymentOrder.getPayment_class());
		requestJson.put("amount", paymentOrder.getSettle_amount());
		requestJson.put("client_ip", paymentOrder.getClient_ip());
		requestJson.put("currency", paymentOrder.getCurrency_name());
		requestJson.put("body", paymentOrder.getBody());
		requestJson.put("report_date", DateUtil.formatDate(paymentOrder.getReport_date()));
		requestJson.put("shift", paymentOrder.getShift_id());
		requestJson.put("channel", paymentOrder.getChannel());
		requestJson.put("subject", paymentOrder.getSubject());
		requestJson.put("description", paymentOrder.getDescription());
		requestJson.put("extra", paymentOrder.getExtra());
		requestJson.put("authcode", paymentOrder.getCredential());

		if(paramJson.containsKey("goods_detail"))
		{
			requestJson.put("goods_detail", paramJson.optJSONArray("goods_detail"));
		}
		
		List<JSONObject> data = new ArrayList<JSONObject>();
		data.add(requestJson);

		Data params = Data.get(tenantId, storeId, Constant.CODE_SUCCESS);
		params.setType(Type.PAY_ORDER_BY_CUSTOMER);
		params.setOper(Oper.add);
		params.setData(data);
		
		Data responseData = this.sendPostRequest(params, CONNECTION_TIMEOUT, SOCKET_TIMEOUT);
		
		boolean is_succeed = false;// 是否完成交易
		String status = SysDictionary.THIRD_PAY_STATUS_NOOK;// 交易状态
		String order_state = SysDictionary.THIRD_PAY_STATUS_NOOK;// 交易状态
		String transaction_no = "";// 交易流水号
		String qrCode = ""; // 二维码编号
		String paid_time = "";// 完成时间
		String failure_code = ""; // 第三方给出的订单的错误码
		String failure_msg = "";// 第三方订单的错误描述

		String request_status = SysDictionary.REQUEST_STATUS_COMPLETE;// 请求状态
		String request_code = "";// 请求错误码 总部给出的
		String request_msg = "";// 请求错误描述 总部给出的
		
		JSONObject responseJson = null;
		if(null != responseData.getData() && 0<responseData.getData().size())
		{
			responseJson = JSONObject.fromObject(responseData.getData().get(0));
		}
		if (null == responseJson)
		{
			responseJson = new JSONObject();
		}

		if (Tools.hv(payType))
		{
			responseJson.put("pay_type", payType);
		}
		
		if (Constant.CODE_SUCCESS == responseData.getCode())
		{
			status = responseJson.optString("payment_state");
			request_status = SysDictionary.REQUEST_STATUS_COMPLETE;

			if (SysDictionary.THIRD_PAY_STATUS_SUCCESS.equals(status))
			{
				is_succeed = true;
				paid_time = DateUtil.format(currentTimes);
				transaction_no = responseJson.optString("transaction_no");
				order_state = SysDictionary.THIRD_PAY_STATUS_SUCCESS;
			}
			else if (SysDictionary.THIRD_PAY_STATUS_FAIL.equals(status))
			{
				is_succeed = true;
				paid_time = DateUtil.format(currentTimes);
				failure_code = responseJson.optString("failure_code");
				failure_msg = responseJson.optString("failure_msg");
				order_state = SysDictionary.THIRD_PAY_STATUS_FAIL;// 交易状态
			}
			else if (SysDictionary.THIRD_PAY_STATUS_PAYING.equals(status))
			{
				failure_code = responseJson.optString("failure_code");
				failure_msg = responseJson.optString("failure_msg");

				is_succeed = false;// 是否完成交易
				order_state = SysDictionary.THIRD_PAY_STATUS_PAYING;// 交易状态
			}
			else
			{
				failure_code = responseJson.optString("failure_code");
				failure_msg = responseJson.optString("failure_msg");
				
				is_succeed = false;// 是否完成交易
				order_state = SysDictionary.THIRD_PAY_STATUS_NOOK;
				
				status = SysDictionary.THIRD_PAY_STATUS_NOOK;
			}
		}
		else if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
		{
			request_status = SysDictionary.REQUEST_STATUS_FAILURE;
			request_code = String.valueOf(Constant.CODE_CONN_EXCEPTION);
			request_msg = Constant.GET_PAYMENT_WAY_MESSAGE_IS_NULL;

			is_succeed = false;
			status = SysDictionary.THIRD_PAY_STATUS_NOOK;// 交易状态
			order_state = SysDictionary.THIRD_PAY_STATUS_NOOK;// 交易状态

			responseData.setCode(Constant.CODE_CONN_EXCEPTION);
			responseData.setMsg(Constant.GET_PAYMENT_WAY_MESSAGE_IS_NULL);
		}
		else
		{
			request_status = SysDictionary.REQUEST_STATUS_FAILURE;
			request_code = String.valueOf(responseData.getCode());
			request_msg = responseData.getMsg();

			is_succeed = true;
			status = SysDictionary.THIRD_PAY_STATUS_FAIL;// 交易状态
			order_state = SysDictionary.THIRD_PAY_STATUS_FAIL;// 交易状态
		}

		JSONObject updateJson = new JSONObject();
		updateJson.put("is_succeed", is_succeed);
		updateJson.put("transaction_no", transaction_no);
		updateJson.put("status", status);
		updateJson.put("qrCode", qrCode);
		updateJson.put("paid_time", paid_time);
		updateJson.put("request_status", request_status);
		updateJson.put("request_code", request_code);
		updateJson.put("request_msg", request_msg);
		updateJson.put("failure_code", failure_code);
		updateJson.put("failure_msg", failure_msg);
		updateJson.put("order_state", order_state);
//		if(Tools.hv(payType))
//		{
//			updateJson.put("pay_type", SysDictionary.PAYMENT_CLASS_SCAN_CODE_PAY);
//			updateJson.put("payment_class", paymentClass);
//			updateJson.put("subject", subject);
//			updateJson.put("body", body);
//			updateJson.put("payment_id", paymentId);
//		}
		paymentDao.updateThirdPaymentOrder(id, tenantId, updateJson);
		// 返回结果记录本地
		List<JSONObject> dataList = new ArrayList<JSONObject>();
		dataList.add(responseJson);
		responseData.setData(dataList);
		return responseData;
	}

	@Override
	public Data queryPayment(String tenantId, int storeId, String aidOrderNum) throws Exception
	{
		// String aidOrderNum = paramJson.optString("order_no");
		// 获取第三方支付记录表Id
		PosThirdPaymentOrderEntity thirdPaymentJson = paymentDao.getThirdPaymentByAidOrderNum(tenantId, storeId, aidOrderNum, SysDictionary.THIRD_PAY_CHARGE);
		int thirdPaymentId = 0;
		if (null != thirdPaymentJson)
		{
			thirdPaymentId = thirdPaymentJson.getId();
		}
		if (0 == thirdPaymentId)
		{
			thirdPaymentJson = paymentDao.getThirdPaymentByAidOrderNum(tenantId, storeId, aidOrderNum, SysDictionary.THIRD_PAY_REFUND);
		}

		return this.queryPayment(tenantId, storeId, thirdPaymentJson);
	}
	
	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public Data queryPayment(String tenantId, int storeId, PosThirdPaymentOrderEntity paymentOrder) throws Exception
	{
		Data responseData = new Data();// 总部返回结果集
		if (null != paymentOrder && SysDictionary.THIRD_PAY_STATUS_SUCCESS.equals(paymentOrder.getStatus()))
		{
			JSONObject requestJson = new JSONObject();
			requestJson.put("payment_state", paymentOrder.getStatus());
			requestJson.put("transaction_no", paymentOrder.getTransaction_no());
			requestJson.put("pay_type", paymentOrder.getPay_type());
			requestJson.put("failure_code", "");
			requestJson.put("failure_msg", "");

			List<JSONObject> resultList = new ArrayList<JSONObject>();
			resultList.add(requestJson);
			responseData.setData(resultList);
			responseData.setCode(Constant.CODE_SUCCESS);
			return responseData;
		}

		JSONObject requestJson = new JSONObject();
		requestJson.put("service_type", paymentOrder.getService_type());
		requestJson.put("pos_num", paymentOrder.getPos_num());
		requestJson.put("opt_num", paymentOrder.getOpt_num());
		requestJson.put("pay_type", paymentOrder.getPayment_class());
		requestJson.put("polling_flag", "N");
		requestJson.put("query_period", 0);
		requestJson.put("query_time", 0);
		requestJson.put("order_no", paymentOrder.getAid_order_num());

		List<JSONObject> requestList = new ArrayList<JSONObject>();
		requestList.add(requestJson);

		Data requestData = Data.get(tenantId, storeId, Constant.CODE_SUCCESS);
		requestData.setType(Type.QUERY_PAY_STATE);
		requestData.setOper(Oper.check);
		requestData.setData(requestList);

//		// 根据总部返回结果 进行相应的业务出来
//		String paramStr = JSONObject.fromObject(requestData).toString();
//		logger.info("<第三方接口请求体==>" + paramStr);
//		posThirdPaymentLogger.info("<第三方接口请求体==>" + paramStr);
//		String result = HttpUtil.sendPostRequest(getRequestPath(), paramStr, QUERY_CONNECTION_TIMEOUT, QUERY_SOCKET_TIMEOUT);
//		logger.info("<第三方接口返回体==>" + result);
//		posThirdPaymentLogger.info("<第三方接口返回体==>" + result);
//
//		responseData = this.queryPaymentResult(tenantId, storeId, result, paymentOrder);
		
		responseData = this.sendPostRequest(requestData, QUERY_CONNECTION_TIMEOUT, QUERY_SOCKET_TIMEOUT);

		//查询结果
		this.queryPaymentResult(tenantId, storeId, responseData, paymentOrder);
		
		//修改查询次数
		Timestamp currentTime = DateUtil.currentTimestamp();
		paymentDao.updateThirdPaymentOrderForQuery(tenantId, storeId, paymentOrder.getId(), currentTime, (paymentOrder.getQuery_count() + 1));
		
		return responseData;
	}
	
	/**
	 * @param tenantId
	 * @param storeId
	 * @param responseData
	 * @param thirdPaymentJson
	 * @throws Exception
	 */
	@Override
	public void queryPaymentResult(String tenantId, int storeId, Data responseData, PosThirdPaymentOrderEntity thirdPaymentJson) throws Exception
	{
		synchronized (thirdPaymentJson.getAid_order_num())
		{
			if (SysDictionary.THIRD_PAY_CHARGE.equals(thirdPaymentJson.getObject_name()))
			{
				//支付记录
				this.queryPaymentResultForCharge(tenantId, storeId, responseData, thirdPaymentJson);
			}
			else
			{
				//退款,取消付款记录
				this.queryPaymentResultForRefund(tenantId, storeId, responseData, thirdPaymentJson);
			}
		}
	}
	
	private void queryPaymentResultForCharge(String tenantId, int storeId, Data responseData, PosThirdPaymentOrderEntity thirdPaymentJson) throws Exception
	{
		Timestamp currentTimes = DateUtil.currentTimestamp();

		Integer thirdPaymentId = thirdPaymentJson.getId();

		boolean is_succeed = false;// 是否完成交易
		String transaction_no = "";// 交易流水号
		String pay_type = "";
		String paid_time = "";// 完成时间
		String status = SysDictionary.THIRD_PAY_STATUS_NOOK;// 交易状态
		String order_state = "";
		String failure_code = ""; // 第三方给出的订单的错误码
		String failure_msg = "";// 第三方订单的错误描述

		String request_status = SysDictionary.REQUEST_STATUS_ING;// 请求状态
		String request_code = "";// 请求错误码 总部给出的
		String request_msg = "";// 请求错误描述 总部给出的

		// 根据总部返回结果处理
		if (Constant.CODE_SUCCESS == responseData.getCode())
		{
			JSONObject responseJson = JSONObject.fromObject(responseData.getData().get(0));
			order_state = status = responseJson.optString("payment_state");

			request_status = SysDictionary.REQUEST_STATUS_COMPLETE;
			request_code = "";
			request_msg = "";

			switch (status)
			{
				case SysDictionary.THIRD_PAY_STATUS_SUCCESS:
					is_succeed = true;
					transaction_no = responseJson.optString("transaction_no");
					pay_type = responseJson.optString("pay_type");
					paid_time = DateUtil.format(currentTimes);
					failure_code = "";
					failure_msg = "";
					break;

				case SysDictionary.THIRD_PAY_STATUS_PAYING:
					is_succeed = false;
					transaction_no = "";
					paid_time = "";
					failure_code = responseJson.optString("failure_code");
					failure_msg = responseJson.optString("failure_msg");
					break;
				default:
					is_succeed = true;
					transaction_no = "";
					paid_time = DateUtil.format(currentTimes);
					failure_code = responseJson.optString("failure_code");
					failure_msg = responseJson.optString("failure_msg");
					break;
			}
		}
		else
		{
			is_succeed = false;
			transaction_no = "";
			paid_time = "";
			status = SysDictionary.THIRD_PAY_STATUS_NOOK;
			order_state = SysDictionary.THIRD_PAY_STATUS_NOOK;
			failure_code = "";
			failure_msg = "";

			request_status = SysDictionary.REQUEST_STATUS_FAILURE;
			request_code = String.valueOf(responseData.getCode());
			request_msg = responseData.getMsg();
		}

		// 修改交易记录
		JSONObject updateJson = new JSONObject();
		updateJson.put("is_succeed", is_succeed);
		updateJson.put("transaction_no", transaction_no);
		updateJson.put("pay_type", pay_type);
		updateJson.put("status", status);
		updateJson.put("paid_time", paid_time);
		updateJson.put("request_status", request_status);
		updateJson.put("request_code", request_code);
		updateJson.put("request_msg", request_msg);
		updateJson.put("failure_code", failure_code);
		updateJson.put("failure_msg", failure_msg);
		updateJson.put("order_state", order_state);

		paymentDao.updateThirdPaymentOrder(thirdPaymentId, tenantId, updateJson);
	}
	
	private void queryPaymentResultForRefund(String tenantId, int storeId, Data responseData, PosThirdPaymentOrderEntity thirdPaymentJson) throws Exception
	{
		Timestamp currentTimes = DateUtil.currentTimestamp();

		Integer thirdPaymentId = thirdPaymentJson.getId();
		Integer thirdPaymentRefundsId = thirdPaymentJson.getRefunds_id();
		Integer queryCount = thirdPaymentJson.getQuery_count();

		boolean is_succeed = false;// 是否完成交易
		String transaction_no = "";// 交易流水号
		String pay_type = "";
		String paid_time = "";// 完成时间
		String status = SysDictionary.THIRD_PAY_STATUS_NOOK;// 交易状态
		String order_state = "";
		String failure_code = ""; // 第三方给出的订单的错误码
		String failure_msg = "";// 第三方订单的错误描述

		String request_status = SysDictionary.REQUEST_STATUS_ING;// 请求状态
		String request_code = "";// 请求错误码 总部给出的
		String request_msg = "";// 请求错误描述 总部给出的

		// 根据总部返回结果处理
		if (Constant.CODE_SUCCESS == responseData.getCode())
		{
			JSONObject responseJson = JSONObject.fromObject(responseData.getData().get(0));
			status = responseJson.optString("payment_state");

			request_status = SysDictionary.REQUEST_STATUS_COMPLETE;
			request_code = "";
			request_msg = "";

			switch (status)
			{
//				case SysDictionary.THIRD_PAY_STATUS_SUCCESS:
//				case SysDictionary.THIRD_PAY_STATUS_PAYING:
//					//退款记录,退款状态为付款完成,付款中重新发起付款;
//					try
//					{
//						JSONObject cancelParamJson = new JSONObject();
//						cancelParamJson.put("order_no", thirdPaymentJson.getAid_order_num());
//						cancelParamJson.put("order_num", thirdPaymentJson.getOrder_num());
//						cancelParamJson.put("settle_amount", thirdPaymentJson.getSettle_amount());
//						cancelParamJson.put("total_amount", thirdPaymentJson.getTotal_amount());
//						cancelParamJson.put("payment_id", thirdPaymentJson.getPayment_id());
//						cancelParamJson.put("service_type", thirdPaymentJson.getService_type());
//						cancelParamJson.put("channel", thirdPaymentJson.getChannel());
//						cancelParamJson.put("report_date", thirdPaymentJson.getReport_date());
//						cancelParamJson.put("shift_id", thirdPaymentJson.getShift_id());
//						cancelParamJson.put("pos_num", thirdPaymentJson.getPos_num());
//						cancelParamJson.put("opt_num", thirdPaymentJson.getOpt_num());
//						cancelParamJson.put("currency_name", thirdPaymentJson.getCurrency_name());
//						cancelParamJson.put("client_ip", thirdPaymentJson.getClient_ip());
//						cancelParamJson.put("refunds_order", thirdPaymentJson.getTransaction_no());
//						cancelParamJson.put("oper_type", thirdPaymentJson.getOper_type());
//						this.cancelPayment(tenantId, storeId, cancelParamJson);
//						return;
//					}
//					catch (Exception e)
//					{
//						e.printStackTrace();
//					}
//					break;

				case SysDictionary.THIRD_PAY_STATUS_FAIL:
					//退款记录,退款状态为付款失败,退款状态修改付款失败
					is_succeed = true;
					transaction_no = "";
					paid_time = DateUtil.format(currentTimes);
					status = SysDictionary.THIRD_PAY_STATUS_FAIL;
					order_state = SysDictionary.THIRD_PAY_STATUS_FAIL;
					failure_code = responseJson.optString("failure_code");
					failure_msg = responseJson.optString("failure_msg");
					break;

				case SysDictionary.THIRD_PAY_STATUS_REFUND_SUCCESS:
				case SysDictionary.THIRD_PAY_STATUS_REVOKED_SUCCESS:
					is_succeed = true;
					transaction_no = responseJson.optString("transaction_no");
					pay_type = responseJson.optString("pay_type");
					paid_time = DateUtil.format(currentTimes);
					order_state = status;
					failure_code = "";
					failure_msg = "";
					break;
					
				case SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL:
				case SysDictionary.THIRD_PAY_STATUS_REVOKED_FAIL:
					is_succeed = true;
					transaction_no = "";
					paid_time = DateUtil.format(currentTimes);
					order_state = status;
					failure_code = responseJson.optString("failure_code");
					failure_msg = responseJson.optString("failure_msg");
					break;
					
				case SysDictionary.THIRD_PAY_STATUS_REFUND:
				case SysDictionary.THIRD_PAY_STATUS_REVOKED:
					is_succeed = false;
					transaction_no = "";
					paid_time = "";
					order_state = status;
					failure_code = responseJson.optString("failure_code");
					failure_msg = responseJson.optString("failure_msg");
					break;

				default:
					is_succeed = false;
					transaction_no = "";
					paid_time = "";
					status = SysDictionary.THIRD_PAY_STATUS_NOOK;
					order_state = SysDictionary.THIRD_PAY_STATUS_NOOK;
					failure_code = responseJson.optString("failure_code");
					failure_msg = responseJson.optString("failure_msg");
					break;
			}
		}
		else
		{
			is_succeed = false;
			transaction_no = "";
			paid_time = "";
			status = SysDictionary.THIRD_PAY_STATUS_NOOK;
			order_state = SysDictionary.THIRD_PAY_STATUS_NOOK;
			failure_code = "";
			failure_msg = "";

			request_status = SysDictionary.REQUEST_STATUS_FAILURE;
			request_code = String.valueOf(responseData.getCode());
			request_msg = responseData.getMsg();
		}
		
//		if(false == is_succeed && Constant.THIRD_PAYMENT_MAX_QUERY_COUNT <= queryCount)
//		{
//			//超过最大查询次数,仍没有查询结果,状态置为人工处理
//			is_succeed = true;
//			paid_time = DateUtil.format(currentTimes);
//			order_state = SysDictionary.THIRD_PAY_STATUS_MANUAL;
//		}

		// 修改交易记录
		JSONObject updateJson = new JSONObject();
		updateJson.put("is_succeed", is_succeed);
		updateJson.put("transaction_no", transaction_no);
		updateJson.put("pay_type", pay_type);
		updateJson.put("status", status);
		updateJson.put("paid_time", paid_time);
		updateJson.put("request_status", request_status);
		updateJson.put("request_code", request_code);
		updateJson.put("request_msg", request_msg);
		updateJson.put("failure_code", failure_code);
		updateJson.put("failure_msg", failure_msg);
		updateJson.put("order_state", order_state);

		paymentDao.updateThirdPaymentOrder(thirdPaymentId, tenantId, updateJson);

		if (null != thirdPaymentRefundsId && 0 < thirdPaymentRefundsId)
		{
			JSONObject updateRefundsJson = new JSONObject();
			updateRefundsJson.put("order_state", order_state);
			paymentDao.updateThirdPaymentOrder(thirdPaymentRefundsId, tenantId, updateRefundsJson);
		}
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRES_NEW)
	public Data cancelPayment(String tenancyId, Integer storeId, JSONObject paramJson) throws Exception
	{
		String aidOrderNum = ParamUtil.getStringValueByObject(paramJson,"order_no");
		synchronized (aidOrderNum)
		{
			Timestamp currentTimes = DateUtil.currentTimestamp();
			Data responseData = null;

			String orderNum = ParamUtil.getStringValueByObject(paramJson,"order_num");
			BigDecimal payAmount = BigDecimal.valueOf(paramJson.optDouble("settle_amount"));// 需退款金额
			BigDecimal totalAmount = BigDecimal.valueOf(paramJson.optDouble("total_amount"));// 需退款金额
			Integer paymentId = ParamUtil.getIntegerValueByObject(paramJson,"payment_id");
			String serviceType = ParamUtil.getStringValueByObject(paramJson,"service_type");
			String channel = ParamUtil.getStringValueByObject(paramJson,"channel");
			String reportDateStr = ParamUtil.getStringValueByObject(paramJson,"report_date");
			Integer shiftId = ParamUtil.getIntegerValueByObject(paramJson,"shift_id");
			String posNum = ParamUtil.getStringValueByObject(paramJson,"pos_num");
			String optNum = ParamUtil.getStringValueByObject(paramJson,"opt_num");
			String clientIp = ParamUtil.getStringValueByObject(paramJson,"client_ip");
			String currencyName = ParamUtil.getStringValueByObject(paramJson,"currency_name");
			String operType = ParamUtil.getStringValueByObject(paramJson,"oper_type");
			String refundsOrder = ParamUtil.getStringValueByObject(paramJson,"refunds_order");

//			// // 获取第三方支付记录表
//			PosThirdPaymentOrderEntity chargeThirdJson = paymentDao.getThirdPaymentByAidOrderNum(tenancyId, storeId, aidOrderNum, SysDictionary.THIRD_PAY_CHARGE);
//
//			JSONObject updateJson = null;
//			Integer refundId = 0;
//			Integer chargeId = 0;
//			String paymentClass = "";
//			if (chargeThirdJson != null)
//			{// 如果有记录
//				Double amount_refunded = chargeThirdJson.getAmount_refunded();
//				if (null == amount_refunded || amount_refunded.isNaN())
//				{
//					amount_refunded = 0d;
//				}
//				BigDecimal amountRefunded = BigDecimal.valueOf(amount_refunded);// 已退款总金额
//				BigDecimal settleAmount = BigDecimal.valueOf(chargeThirdJson.getSettle_amount());// 支付金额
//
//				if (settleAmount.compareTo(payAmount.add(amountRefunded)) >= 0)
//				{ // 如果退款金额小于等于支付金额 可以退款
//					// 修改交易记录
//					updateJson = new JSONObject();
//					updateJson.put("amount_refunded", payAmount.add(amountRefunded).doubleValue());
//					updateJson.put("is_refunded", true);
//					updateJson.put("expire_time", DateUtil.format(currentTimes));
//
//					chargeId = chargeThirdJson.getId();
//					paymentClass = chargeThirdJson.getPayment_class();
//
//					PosThirdPaymentOrderEntity refundThirdJson = paymentDao.getThirdPaymentByAidOrderNum(tenancyId, storeId, aidOrderNum, SysDictionary.THIRD_PAY_REFUND);
//
//					if (null != refundThirdJson)
//					{
//						String paymentState = refundThirdJson.getStatus();
//						if (SysDictionary.THIRD_PAY_STATUS_REFUND_SUCCESS.equals(paymentState) || SysDictionary.THIRD_PAY_STATUS_REVOKED_SUCCESS.equals(paymentState))
//						{
//							JSONObject responseJson = new JSONObject();
//							responseJson.put("payment_state", paymentState);
//							responseJson.put("failure_code", "");
//							responseJson.put("failure_msg", "");
//
//							List<JSONObject> responseList = new ArrayList<JSONObject>();
//							responseList.add(responseJson);
//							responseData.setData(responseList);
//							responseData.setCode(Constant.CODE_SUCCESS);
//							return responseData;
//						}
//						paymentClass = refundThirdJson.getPayment_class();
//						chargeId = refundThirdJson.getRefunds_id();
//						refundId = refundThirdJson.getId();
//					}
//					else
//					{
//						JSONObject reFound = new JSONObject();
//						reFound.put("tenancy_id", tenancyId);
//						reFound.put("store_id", storeId);
//						reFound.put("aid_order_num", aidOrderNum);
//						reFound.put("report_date", reportDateStr);
//						reFound.put("shift_id", shiftId);
//						reFound.put("pos_num", posNum);
//						reFound.put("opt_num", optNum);
//						reFound.put("channel", channel);
//						reFound.put("service_type", serviceType);
//						reFound.put("order_num", orderNum);
//						reFound.put("payment_id", paymentId);
//						reFound.put("total_amount", totalAmount);
//						reFound.put("settle_amount", payAmount);
//						reFound.put("currency_name", currencyName);
//						reFound.put("client_ip", chargeThirdJson.getClient_ip());
//						reFound.put("refunds_order", chargeThirdJson.getTransaction_no());
//						reFound.put("object_name", SysDictionary.THIRD_PAY_REFUND);
//						reFound.put("payment_class", paymentClass);
//						reFound.put("subject", chargeThirdJson.getSubject());
//						reFound.put("body", chargeThirdJson.getBody());
//						reFound.put("extra", chargeThirdJson.getExtra());
//						reFound.put("description", chargeThirdJson.getDescription());
//						reFound.put("metadata", chargeThirdJson.getMetadata());
//						reFound.put("payment_type", chargeThirdJson.getPayment_type());
//						reFound.put("create_time", DateUtil.format(currentTimes));
//						reFound.put("is_refunded", false);
//						reFound.put("refunds_id", chargeId);
//						reFound.put("amount_refunded", 0d);
//						reFound.put("is_succeed", false);
//						reFound.put("status", SysDictionary.THIRD_PAY_STATUS_REFUND);
//						reFound.put("request_status", SysDictionary.REQUEST_STATUS_ING);
//						reFound.put("order_state", SysDictionary.THIRD_PAY_STATUS_REFUND);
//						reFound.put("query_time", DateUtil.format(currentTimes));
//						reFound.put("query_count", "0");
//						reFound.put("remark", chargeThirdJson.getRemark());
//						reFound.put("upload_tag", 1);
//						reFound.put("oper_type", operType);
//
//						refundId = paymentDao.insertThirdPaymentOrder(tenancyId, reFound);
//					}
//				}
//				else
//				{// 如果退款金额大于支付金额 不可以退款
//					responseData.setCode(PosErrorCode.AMOUNT_IS_BIG_SETTLE_AMOUNT.getNumber());
//					responseData.setMsg(PosErrorCode.AMOUNT_IS_BIG_SETTLE_AMOUNT.getMessage());
//					return responseData;
//				}
//			}
//			else
//			{
//				PosThirdPaymentOrderEntity refundThirdJson = paymentDao.getThirdPaymentByAidOrderNum(tenancyId, storeId, aidOrderNum, SysDictionary.THIRD_PAY_REFUND);
//
//				if (null != refundThirdJson)
//				{
//					String paymentState = refundThirdJson.getStatus();
//					if (SysDictionary.THIRD_PAY_STATUS_REFUND_SUCCESS.equals(paymentState) || SysDictionary.THIRD_PAY_STATUS_REVOKED_SUCCESS.equals(paymentState))
//					{
//						JSONObject responseJson = new JSONObject();
//						responseJson.put("payment_state", paymentState);
//						responseJson.put("failure_code", "");
//						responseJson.put("failure_msg", "");
//
//						List<JSONObject> responseList = new ArrayList<JSONObject>();
//						responseList.add(responseJson);
//						responseData.setData(responseList);
//						responseData.setCode(Constant.CODE_SUCCESS);
//						return responseData;
//					}
//					paymentClass = refundThirdJson.getPayment_class();
//					chargeId = refundThirdJson.getRefunds_id();
//					refundId = refundThirdJson.getId();
//				}
//				else
//				{
//					JSONObject storeJson = paymentDao.getOrganById(tenancyId, storeId);
//					JSONObject payTypeJson = paymentDao.getPaymentWayByID(tenancyId, storeId, paymentId);
//
//					String subject = storeJson.optString("org_short_name").trim() + payTypeJson.optString("payment_name");
//					String body = payTypeJson.optString("payment_name") + "，订单号：" + aidOrderNum + "，金额：" + String.valueOf(payAmount);
//
//					paymentClass = payTypeJson.optString("payment_class");
//
//					JSONObject reFound = new JSONObject();
//					reFound.put("tenancy_id", tenancyId);
//					reFound.put("store_id", storeId);
//					reFound.put("report_date", reportDateStr);
//					reFound.put("shift_id", shiftId);
//					reFound.put("pos_num", posNum);
//					reFound.put("opt_num", optNum);
//					reFound.put("channel", channel);
//					reFound.put("object_name", SysDictionary.THIRD_PAY_REFUND);
//					reFound.put("service_type", serviceType);
//					reFound.put("order_num", orderNum);
//					reFound.put("aid_order_num", aidOrderNum);
//					reFound.put("payment_class", paymentClass);
//					reFound.put("payment_id", paymentId);
//					reFound.put("total_amount", totalAmount);
//					reFound.put("settle_amount", payAmount);
//					reFound.put("currency_name", currencyName);
//					reFound.put("subject", subject);
//					reFound.put("body", body);
//					reFound.put("client_ip", clientIp);
//					reFound.put("create_time", DateUtil.format(currentTimes));
//					reFound.put("is_refunded", false);
//					reFound.put("amount_refunded", "0");
//					reFound.put("refunds_order", refundsOrder);
//					reFound.put("payment_type", "0");
//					reFound.put("is_succeed", false);
//					reFound.put("status", SysDictionary.THIRD_PAY_STATUS_REFUND);
//					reFound.put("request_status", SysDictionary.REQUEST_STATUS_ING);
//					reFound.put("order_state", SysDictionary.THIRD_PAY_STATUS_REFUND);
//					reFound.put("query_time", DateUtil.format(currentTimes));
//					reFound.put("query_count", "0");
//					reFound.put("remark", "线上支付");
//					reFound.put("upload_tag", 1);
//					reFound.put("oper_type", operType);
//
//					refundId = paymentDao.insertThirdPaymentOrder(tenancyId, reFound);
//				}
//			}
//			
//			//组织参数请求总部接口
//			JSONObject requestJson = new JSONObject();
//			requestJson.put("order_no", aidOrderNum);
//			requestJson.put("service_type", serviceType);
//			requestJson.put("pos_num", posNum);
//			requestJson.put("opt_num", optNum);
//			requestJson.put("amount", payAmount);
//			requestJson.put("pay_type", paymentClass);
//			requestJson.put("currency", currencyName);
//			requestJson.put("report_date", reportDateStr);
//			requestJson.put("shift", shiftId);
//			requestJson.put("channel", channel);
//
//			List<JSONObject> requestList = new ArrayList<JSONObject>();
//			requestList.add(requestJson);
//
//			Data requestData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
//			requestData.setType(Type.CANCEL_PAY_ORDER_SUP);
//			requestData.setOper(Oper.cancle);
//			requestData.setData(requestList);
//
//			// 组织参数,请求总部
//			responseData = this.sendPostRequest(requestData, CONNECTION_TIMEOUT, SOCKET_TIMEOUT);
//
//			boolean is_succeed = false;// 是否完成交易
//			String transaction_no = "";// 交易流水号
//			String status = "";// 交易状态
//			String order_state = "";
//			String paid_time = "";// 完成时间
//			String request_status = "";// 请求状态
//			String request_code = "";// 请求错误码 总部给出的
//			String request_msg = "";// 请求错误描述 总部给出的
//			String failure_code = ""; // 第三方给出的订单的错误码
//			String failure_msg = "";// 第三方订单的错误描述
//
//			// 根据总部返回结果处理
//			boolean isRefundSuccess = false;
//			if (Constant.CODE_SUCCESS == responseData.getCode())
//			{
//				JSONObject responseJson = JSONObject.fromObject(responseData.getData().get(0));
//
//				request_status = SysDictionary.REQUEST_STATUS_COMPLETE;
//				transaction_no = responseJson.optString("transaction_no");
//				failure_code = responseJson.optString("failure_code");
//				failure_msg = responseJson.optString("failure_msg");
//				status = responseJson.optString("payment_state");
//				switch (status)
//				{
//					case SysDictionary.THIRD_PAY_STATUS_REFUND_SUCCESS:
//					case SysDictionary.THIRD_PAY_STATUS_REVOKED_SUCCESS:
//						is_succeed = true;
//						paid_time = DateUtil.format(currentTimes);
//						order_state = status;
//						isRefundSuccess = true;
//						break;
//
//					case SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL:
//					case SysDictionary.THIRD_PAY_STATUS_REVOKED_FAIL:
//						is_succeed = true;
//						paid_time = DateUtil.format(currentTimes);
//						order_state = status;
//						break;
//
//					case SysDictionary.THIRD_PAY_STATUS_REFUND:
//					case SysDictionary.THIRD_PAY_STATUS_REVOKED:
//						is_succeed = false;
//						order_state = status;
//						break;
//
//					default:
//						is_succeed = false;
//						status = SysDictionary.THIRD_PAY_STATUS_NOOK;
//						order_state = SysDictionary.THIRD_PAY_STATUS_NOOK;
//						break;
//				}
//			}
//			else if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
//			{
//				// 返回为空
//				is_succeed = false;
//				status = SysDictionary.THIRD_PAY_STATUS_NOOK;
//				order_state = SysDictionary.THIRD_PAY_STATUS_NOOK;
//
//				request_status = SysDictionary.REQUEST_STATUS_FAILURE;
//				request_code = String.valueOf(responseData.getCode());
//				request_msg = Constant.GET_PAYMENT_WAY_MESSAGE_IS_NULL;
//
//				responseData.setMsg(request_msg);
//			}
//			else
//			{
//				is_succeed = true;
//				status = SysDictionary.THIRD_PAY_STATUS_REVOKED_FAIL;
//				order_state = SysDictionary.THIRD_PAY_STATUS_REVOKED_FAIL;
//
//				request_status = SysDictionary.REQUEST_STATUS_FAILURE;
//				request_code = String.valueOf(responseData.getCode());
//				request_msg = responseData.getMsg();
//			}
//
//			// 修改交易记录
//			JSONObject updateRefundsJson = new JSONObject();
//			updateRefundsJson.put("is_succeed", is_succeed);
//			updateRefundsJson.put("transaction_no", transaction_no);
//			updateRefundsJson.put("status", status);
//			updateRefundsJson.put("paid_time", paid_time);
//			updateRefundsJson.put("request_status", request_status);
//			updateRefundsJson.put("request_code", request_code);
//			updateRefundsJson.put("request_msg", request_msg);
//			updateRefundsJson.put("failure_code", failure_code);
//			updateRefundsJson.put("failure_msg", failure_msg);
//			updateRefundsJson.put("order_state", order_state);
//
//			paymentDao.updateThirdPaymentOrder(refundId, tenancyId, updateRefundsJson);
//
//			if (null != updateJson && null != chargeId)
//			{
//				updateJson.put("order_state", order_state);
//				paymentDao.updateThirdPaymentOrder(chargeId, tenancyId, updateJson);
//			}
//			
			String paymentClass = null;
			
			// 查询是存在退款记录
			PosThirdPaymentOrderEntity refundThirdOrder = paymentDao.getThirdPaymentAllByAidOrderNum(tenancyId, storeId, aidOrderNum, SysDictionary.THIRD_PAY_REFUND);
			if (null != refundThirdOrder)
			{
				paymentClass = refundThirdOrder.getPayment_class();
				String paymentState = refundThirdOrder.getStatus();
				//已退款成功,直接返回成功
				if (SysDictionary.THIRD_PAY_STATUS_REFUND_SUCCESS.equals(paymentState) || SysDictionary.THIRD_PAY_STATUS_REVOKED_SUCCESS.equals(paymentState))
				{
					JSONObject responseJson = new JSONObject();
					responseJson.put("payment_state", paymentState);
					responseJson.put("failure_code", "");
					responseJson.put("failure_msg", "");

					List<JSONObject> responseList = new ArrayList<JSONObject>();
					responseList.add(responseJson);
					
					responseData = Data.get();
					responseData.setData(responseList);
					responseData.setCode(Constant.CODE_SUCCESS);
					return responseData;
				}
			}
			else
			{
				// 查询是否有支付记录,
				PosThirdPaymentOrderEntity chargeThirdOrder = paymentDao.getThirdPaymentAllByAidOrderNum(tenancyId, storeId, aidOrderNum, SysDictionary.THIRD_PAY_CHARGE);
				if (null != chargeThirdOrder)
				{
					// 如果有记录
					paymentClass = chargeThirdOrder.getPayment_class();
					Double amount_refunded = chargeThirdOrder.getAmount_refunded();
					if (null == amount_refunded || amount_refunded.isNaN())
					{
						amount_refunded = 0d;
					}
					BigDecimal amountRefunded = BigDecimal.valueOf(amount_refunded);// 已退款总金额
					BigDecimal settleAmount = BigDecimal.valueOf(chargeThirdOrder.getSettle_amount());// 支付金额

					if (0 > settleAmount.compareTo(payAmount.add(amountRefunded)))
					{
						// 如果退款金额大于支付金额 不可以退款
						responseData = Data.get();
						responseData.setCode(PosErrorCode.AMOUNT_IS_BIG_SETTLE_AMOUNT.getNumber());
						responseData.setMsg(PosErrorCode.AMOUNT_IS_BIG_SETTLE_AMOUNT.getMessage());
						return responseData;
					}
					
					//修改支付记录为退款
					JSONObject updateJson = new JSONObject();
					updateJson.put("amount_refunded", payAmount.add(amountRefunded).doubleValue());
					updateJson.put("is_refunded", true);
					updateJson.put("expire_time", DateUtil.format(currentTimes));
					updateJson.put("order_state", SysDictionary.THIRD_PAY_STATUS_REVOKED_SUCCESS);
					paymentDao.updateThirdPaymentOrder(chargeThirdOrder.getId(), tenancyId, updateJson);
					
					refundThirdOrder = new PosThirdPaymentOrderEntity(tenancyId, storeId, DateUtil.parseDate(reportDateStr), shiftId, posNum, optNum, channel, SysDictionary.THIRD_PAY_REFUND, serviceType, orderNum, aidOrderNum, paymentId, paymentClass, totalAmount.doubleValue(),
							payAmount.doubleValue(), chargeThirdOrder.getPayment_type(), chargeThirdOrder.getSubject(), chargeThirdOrder.getBody(), currentTimes, SysDictionary.THIRD_PAY_STATUS_REFUND, SysDictionary.THIRD_PAY_STATUS_REFUND);
				
					refundThirdOrder.setCurrency_name(currencyName);
					refundThirdOrder.setClient_ip(chargeThirdOrder.getClient_ip());
					refundThirdOrder.setRefunds_order(chargeThirdOrder.getTransaction_no());
					refundThirdOrder.setExtra(chargeThirdOrder.getExtra());
					refundThirdOrder.setDescription(chargeThirdOrder.getDescription());
					refundThirdOrder.setMetadata(chargeThirdOrder.getMetadata());
					refundThirdOrder.setRefunds_id(chargeThirdOrder.getId());
					refundThirdOrder.setRequest_status(SysDictionary.REQUEST_STATUS_ING);
					refundThirdOrder.setQuery_time(currentTimes);
					refundThirdOrder.setRemark(chargeThirdOrder.getRemark());
					refundThirdOrder.setOper_type(operType);
				}
				else
				{
					JSONObject storeJson = paymentDao.getOrganById(tenancyId, storeId);
					JSONObject payTypeJson = paymentDao.getPaymentWayByID(tenancyId, storeId, paymentId);

					String subject = storeJson.optString("org_short_name").trim() + payTypeJson.optString("payment_name");
					String body = payTypeJson.optString("payment_name") + "，订单号：" + aidOrderNum + "，金额：" + String.valueOf(payAmount);

					paymentClass = payTypeJson.optString("payment_class");

					refundThirdOrder = new PosThirdPaymentOrderEntity(tenancyId, storeId, DateUtil.parseDate(reportDateStr), shiftId, posNum, optNum, channel, SysDictionary.THIRD_PAY_REFUND, serviceType, orderNum, aidOrderNum, paymentId, paymentClass, totalAmount.doubleValue(),
							payAmount.doubleValue(), "0", subject, body, currentTimes, SysDictionary.THIRD_PAY_STATUS_REFUND, SysDictionary.THIRD_PAY_STATUS_REFUND);
					refundThirdOrder.setCurrency_name(currencyName);
					refundThirdOrder.setClient_ip(clientIp);
					refundThirdOrder.setRefunds_order(refundsOrder);
					refundThirdOrder.setQuery_time(currentTimes);
					refundThirdOrder.setRemark("线上支付");
					refundThirdOrder.setOper_type(operType);
				}
				
				Integer refundId = paymentDao.insertThirdPaymentOrder(tenancyId, refundThirdOrder);
				refundThirdOrder.setId(refundId);
			}
			
			responseData = this.cancelPayment(tenancyId, storeId, refundThirdOrder);
			
			String status = "";// 交易状态
			String failure_code = ""; // 第三方给出的订单的错误码
			String failure_msg = "";// 第三方订单的错误描述
			boolean isRefundSuccess = false;
			if (Constant.CODE_SUCCESS == responseData.getCode())
			{
				JSONObject responseJson = JSONObject.fromObject(responseData.getData().get(0));

				failure_code = responseJson.optString("failure_code");
				failure_msg = responseJson.optString("failure_msg");
				status = responseJson.optString("payment_state");
				if (SysDictionary.THIRD_PAY_STATUS_REFUND_SUCCESS.equals(status) || SysDictionary.THIRD_PAY_STATUS_REVOKED_SUCCESS.equals(status))
				{
					isRefundSuccess = true;
				}
			}
			else if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
			{
				status = SysDictionary.THIRD_PAY_STATUS_NOOK;
				failure_code = String.valueOf(responseData.getCode());
				failure_msg = Constant.GET_PAYMENT_WAY_MESSAGE_IS_NULL;
			}
			else
			{
				status = SysDictionary.THIRD_PAY_STATUS_REVOKED_FAIL;
				failure_code = String.valueOf(responseData.getCode());
				failure_msg = responseData.getMsg();
			}

			// 退款失败,记录退款异常
			if (false == isRefundSuccess)
			{
				PosThirdPaymentRefundEntity paymentRefund = new PosThirdPaymentRefundEntity(tenancyId, storeId, DateUtil.parseDate(reportDateStr), shiftId, posNum, optNum, channel, serviceType, orderNum, aidOrderNum, refundsOrder, paymentClass, paymentId, totalAmount.doubleValue(),
						payAmount.doubleValue(), currentTimes, currentTimes, optNum, failure_code, failure_msg, operType, status, status);

				thirdPaymentRefundService.thirdPaymentRefundFailure(tenancyId, storeId, paymentRefund);
			}
			
			// 返回结果记录本地
			return responseData;
		}
	}

	public Data cancelPayment(String tenancyId, Integer storeId, PosThirdPaymentOrderEntity thirdPaymentOrder) throws Exception
	{
		Timestamp currentTimes = DateUtil.currentTimestamp();
		
		//组织参数请求总部接口
		JSONObject requestJson = new JSONObject();
		requestJson.put("order_no", thirdPaymentOrder.getAid_order_num());
		requestJson.put("service_type", thirdPaymentOrder.getService_type());
		requestJson.put("pos_num", thirdPaymentOrder.getPos_num());
		requestJson.put("opt_num", thirdPaymentOrder.getOpt_num());
		requestJson.put("amount", thirdPaymentOrder.getSettle_amount());
		requestJson.put("pay_type", thirdPaymentOrder.getPayment_class());
		requestJson.put("currency", thirdPaymentOrder.getCurrency_name());
		requestJson.put("report_date", DateUtil.formatDate(thirdPaymentOrder.getReport_date()));
		requestJson.put("shift", thirdPaymentOrder.getShift_id());
		requestJson.put("channel", thirdPaymentOrder.getChannel());

		List<JSONObject> requestList = new ArrayList<JSONObject>();
		requestList.add(requestJson);

		Data requestData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
		requestData.setType(Type.CANCEL_PAY_ORDER_SUP);
		requestData.setOper(Oper.cancle);
		requestData.setData(requestList);

		// 组织参数,请求总部
		Data responseData = this.sendPostRequest(requestData, CONNECTION_TIMEOUT, SOCKET_TIMEOUT);

		boolean is_succeed = false;// 是否完成交易
		String transaction_no = "";// 交易流水号
		String status = "";// 交易状态
		String order_state = "";
		String paid_time = "";// 完成时间
		String request_status = "";// 请求状态
		String request_code = "";// 请求错误码 总部给出的
		String request_msg = "";// 请求错误描述 总部给出的
		String failure_code = ""; // 第三方给出的订单的错误码
		String failure_msg = "";// 第三方订单的错误描述

		// 根据总部返回结果处理
		if (Constant.CODE_SUCCESS == responseData.getCode())
		{
			JSONObject responseJson = JSONObject.fromObject(responseData.getData().get(0));

			request_status = SysDictionary.REQUEST_STATUS_COMPLETE;
			transaction_no = responseJson.optString("transaction_no");
			failure_code = responseJson.optString("failure_code");
			failure_msg = responseJson.optString("failure_msg");
			status = responseJson.optString("payment_state");
			switch (status)
			{
				case SysDictionary.THIRD_PAY_STATUS_REFUND_SUCCESS:
				case SysDictionary.THIRD_PAY_STATUS_REVOKED_SUCCESS:
					is_succeed = true;
					paid_time = DateUtil.format(currentTimes);
					order_state = status;
					break;

				case SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL:
				case SysDictionary.THIRD_PAY_STATUS_REVOKED_FAIL:
					is_succeed = true;
					paid_time = DateUtil.format(currentTimes);
					order_state = status;
					break;

				case SysDictionary.THIRD_PAY_STATUS_REFUND:
				case SysDictionary.THIRD_PAY_STATUS_REVOKED:
					is_succeed = false;
					order_state = status;
					break;

				default:
					is_succeed = false;
					status = SysDictionary.THIRD_PAY_STATUS_NOOK;
					order_state = SysDictionary.THIRD_PAY_STATUS_NOOK;
					break;
			}
		}
		else if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
		{
			// 返回为空
			is_succeed = false;
			status = SysDictionary.THIRD_PAY_STATUS_NOOK;
			order_state = SysDictionary.THIRD_PAY_STATUS_NOOK;

			request_status = SysDictionary.REQUEST_STATUS_FAILURE;
			request_code = String.valueOf(responseData.getCode());
			request_msg = Constant.GET_PAYMENT_WAY_MESSAGE_IS_NULL;

			responseData.setMsg(request_msg);
		}
		else
		{
			is_succeed = true;
			status = SysDictionary.THIRD_PAY_STATUS_REVOKED_FAIL;
			order_state = SysDictionary.THIRD_PAY_STATUS_REVOKED_FAIL;

			request_status = SysDictionary.REQUEST_STATUS_FAILURE;
			request_code = String.valueOf(responseData.getCode());
			request_msg = responseData.getMsg();
		}

		// 修改交易记录
		JSONObject updateRefundsJson = new JSONObject();
		updateRefundsJson.put("is_succeed", is_succeed);
		updateRefundsJson.put("transaction_no", transaction_no);
		updateRefundsJson.put("status", status);
		updateRefundsJson.put("paid_time", paid_time);
		updateRefundsJson.put("request_status", request_status);
		updateRefundsJson.put("request_code", request_code);
		updateRefundsJson.put("request_msg", request_msg);
		updateRefundsJson.put("failure_code", failure_code);
		updateRefundsJson.put("failure_msg", failure_msg);
		updateRefundsJson.put("order_state", order_state);

		paymentDao.updateThirdPaymentOrder(thirdPaymentOrder.getId(), tenancyId, updateRefundsJson);

		if (Tools.hv(thirdPaymentOrder.getRefunds_id()) && 0<thirdPaymentOrder.getRefunds_id())
		{
			JSONObject updateJson = new JSONObject();
			updateJson.put("order_state", order_state);
			paymentDao.updateThirdPaymentOrder(thirdPaymentOrder.getRefunds_id(), tenancyId, updateJson);
		}
		
		// 返回结果记录本地
		return responseData;
	}
	
	@Override
	@Deprecated
	public Data refundPayment(String tenancyId, int storeId, JSONObject paramJson) throws Exception
	{
		Timestamp currentTime = DateUtil.currentTimestamp();
		Data responseData = new Data();// 返回结果集

		BigDecimal amount = BigDecimal.valueOf(paramJson.optDouble("amount"));// 需退款金额
		// 获取第三方支付记录表
		JSONObject payTypeJson = paymentDao.getPaymentWayByID(tenancyId, storeId, paramJson.optInt("pay_id"));
		paramJson.put("pay_type", payTypeJson.opt("payment_class"));

		JSONObject posThirdJson = paymentDao.getPayThirdByOrderNum(tenancyId, storeId, paramJson.optString("order_no"), paramJson.optString("service_type"), paramJson.optString("pay_type"), SysDictionary.THIRD_PAY_CHARGE);

		if (posThirdJson != null)
		{// 如果有记录
			BigDecimal amountRefunded = BigDecimal.valueOf(posThirdJson.optDouble("amount_refunded", 0.00));// 已退款总金额
			BigDecimal settleAmount = BigDecimal.valueOf(posThirdJson.optDouble("settle_amount"));// 支付金额
			if (settleAmount.compareTo(amount.add(amountRefunded)) >= 0)
			{ // 如果退款金额小于等于支付金额 可以退款
				// 修改交易记录
				JSONObject updateJson = new JSONObject();
				updateJson.put("amount_refunded", amount.add(amountRefunded).doubleValue());
				updateJson.put("is_refunded", true);
				// updateJson.put("paid_time", DateUtil.format(currentTime));
				// updateJson.put("expire_time", DateUtil.format(currentTime));
				// updateJson.put("status",
				// SysDictionary.THIRD_PAY_STATUS_REVOKED);
				// updateJson.put("is_succeed", true);

				paymentDao.updateThirdPaymentOrder(posThirdJson.optInt("id"), tenancyId, updateJson);
			}
			else
			{// 如果退款金额大于支付金额 不可以退款
				responseData.setCode(PosErrorCode.AMOUNT_IS_BIG_SETTLE_AMOUNT.getNumber());
				responseData.setMsg(PosErrorCode.AMOUNT_IS_BIG_SETTLE_AMOUNT.getMessage());
				return responseData;
			}
		}

		// 插入退款信息
		JSONObject reFound = new JSONObject();

		reFound.put("tenancy_id", tenancyId);
		reFound.put("store_id", storeId);

		reFound.put("report_date", paramJson.optString("report_date"));
		reFound.put("shift_id", paramJson.optInt("shift_id"));
		reFound.put("pos_num", paramJson.optString("pos_num"));
		reFound.put("opt_num", paramJson.optString("opt_num"));
		reFound.put("channel", paramJson.optString("channel"));
		reFound.put("object_name", SysDictionary.THIRD_PAY_REFUND);
		reFound.put("service_type", paramJson.optString("service_type"));
		reFound.put("payment_class", paramJson.optString("pay_type"));
		reFound.put("payment_id", paramJson.optInt("pay_id"));
		reFound.put("settle_amount", paramJson.optDouble("amount"));
		reFound.put("order_num", paramJson.optString("order_no"));

		reFound.put("create_time", DateUtil.format(currentTime));
		reFound.put("amount_refunded", 0d);

		String aidOrderNum = "";
		if (posThirdJson != null)
		{
			reFound.put("payment_type", posThirdJson.optString("payment_type"));
			reFound.put("total_amount", posThirdJson.optDouble("total_amount"));
			reFound.put("currency_name", posThirdJson.optString("currency_name"));
			reFound.put("subject", posThirdJson.optString("subject"));
			reFound.put("body", posThirdJson.optString("body"));
			reFound.put("client_ip", posThirdJson.optString("client_ip"));
			reFound.put("extra", posThirdJson.optString("extra"));
			reFound.put("description", posThirdJson.optString("description"));
			reFound.put("metadata", posThirdJson.optString("metadata"));
			reFound.put("credential", posThirdJson.optString("credential"));

			reFound.put("refunds_order", posThirdJson.opt("transaction_no"));
			reFound.put("refunds_id", posThirdJson.optInt("id"));
			aidOrderNum = posThirdJson.optString("aid_order_num");
		}
		else
		{
			aidOrderNum = paramJson.optString("order_no");
			reFound.put("refunds_order", "线上支付");
		}
		reFound.put("aid_order_num", aidOrderNum);

		// reFound.put("order_no",paramJson.optString("order_no"));
		// reFound.put("amount",paramJson.optDouble("amount"));
		// orderParam.put("order_num", orderParam.optString("order_no"));
		// orderParam.put("settle_amount", orderParam.optString("amount"));

		int tkId = paymentDao.insertThirdPaymentOrder(tenancyId, reFound);

		/** 根据总部返回结果 进行相应的业务处理 **/
		paramJson.put("order_no", aidOrderNum);

		List<JSONObject> requestList = new ArrayList<JSONObject>();
		requestList.add(paramJson);

		Data requestData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
		requestData.setType(Type.REFUND_PAY_ORDER);
		requestData.setOper(Oper.delete);
		requestData.setData(requestList);

		String paramStr = JSONObject.fromObject(requestData).toString();
//		logger.info("<第三方接口请求体==>" + paramStr);
		posThirdPaymentLogger.info("<第三方接口请求体==>" + paramStr);
		String result = HttpUtil.sendPostRequest(getRequestPath(), paramStr);
//		logger.info("<第三方接口返回体==>" + result);
		posThirdPaymentLogger.info("<第三方接口返回体==>" + result);

		// 根据总部返回结果处理
		boolean is_succeed = true;// 是否完成交易
		String transaction_no = "";// 交易流水号
		String status = SysDictionary.THIRD_PAY_STATUS_NOOK;// 交易状态
		String paid_time = "";// 完成时间
		// expire_time // 失效时间 先不需要
		String request_status = SysDictionary.REQUEST_STATUS_ING;// 请求状态
		String request_code = "";// 请求错误码 总部给出的
		String request_msg = "";// 请求错误描述 总部给出的
		String failure_code = ""; // 第三方给出的订单的错误码
		String failure_msg = "";// 第三方订单的错误描述
		if (null != result && !"".equals(result))
		{
			responseData = ParamUtil.stringToData(result);
			List<?> responseList = responseData.getData();
			if (responseList == null || responseList.isEmpty())
			{
				throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
			}
			JSONObject responseJson = JSONObject.fromObject(responseList.get(0));
			if (Constant.CODE_SUCCESS == responseData.getCode())
			{
				responseData.setCode(Constant.CODE_SUCCESS);
				List<JSONObject> resultList = new ArrayList<JSONObject>();
				resultList.add(responseJson);
				responseData.setData(resultList);
				transaction_no = responseJson.optString("transaction_no");
				paid_time = DateUtil.currentTimestamp().toString();
				status = responseJson.optString("payment_state");
				request_status = SysDictionary.REQUEST_STATUS_COMPLETE;
			}
			else
			{
				request_status = SysDictionary.REQUEST_STATUS_FAILURE;
				request_code = String.valueOf(responseData.getCode());
				request_msg = responseData.getMsg();
				is_succeed = false;
				failure_code = responseJson.optString("failure_code");
				failure_msg = responseJson.optString("failure_msg");
			}
		}
		else
		{// 返回为空
			responseData.setCode(Constant.CODE_NULL_DATASET);
			responseData.setMsg(Constant.GET_PAYMENT_WAY_MESSAGE_IS_NULL);
		}

		JSONObject reFoundUpdate = new JSONObject();
		reFoundUpdate.put("transaction_no", transaction_no);
		reFoundUpdate.put("paid_time", paid_time);
		reFoundUpdate.put("is_succeed", is_succeed);
		reFoundUpdate.put("status", status);
		reFoundUpdate.put("failure_code", failure_code);
		reFoundUpdate.put("failure_msg", failure_msg);
		reFoundUpdate.put("request_status", request_status);
		reFoundUpdate.put("request_code", request_code);
		reFoundUpdate.put("request_msg", request_msg);
		paymentDao.updateThirdPaymentOrder(tkId, tenancyId, reFoundUpdate);

		return responseData;
	}

	@Override
	@Deprecated
	public Data queryRefundPayment(String tenancyId, int storeId, JSONObject paramJson) throws Exception
	{
		Data responseData = new Data();// 总部返回结果集

		JSONObject payTypeJson = paymentDao.getPaymentWayByID(tenancyId, storeId, paramJson.optInt("pay_id"));
		paramJson.put("pay_type", payTypeJson.opt("payment_class"));
		JSONObject posThirdJson = paymentDao.getPayThirdByOrderNum(tenancyId, storeId, paramJson.optString("order_no"), paramJson.optString("service_type"), paramJson.optString("pay_type"), SysDictionary.THIRD_PAY_REFUND);
		if (posThirdJson == null)
		{
			throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_BILL);
		}
		else
		{
			if (posThirdJson.optString("status").equals(SysDictionary.THIRD_PAY_STATUS_SUCCESS))
			{
				responseData.setCode(Constant.CODE_SUCCESS);
				List<JSONObject> resultList = new ArrayList<JSONObject>();
				resultList.add(posThirdJson);
				responseData.setData(resultList);
				return responseData;
			}
			else if (posThirdJson.optString("status").equals(SysDictionary.THIRD_PAY_STATUS_FAIL))
			{
				responseData.setCode(PosErrorCode.THIRD_PAYMENT_FAILURE.getNumber());
				responseData.setMsg(PosErrorCode.THIRD_PAYMENT_FAILURE.getMessage());
				List<JSONObject> resultList = new ArrayList<JSONObject>();
				resultList.add(posThirdJson);
				responseData.setData(resultList);
				return responseData;
			}
			// 获取第三方支付记录表Id
			int posThirdId = posThirdJson.optInt("id");

			paramJson.put("order_no", payTypeJson.optString("aid_order_num"));

			List<JSONObject> requestList = new ArrayList<JSONObject>();
			requestList.add(paramJson);

			Data requestData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
			requestData.setType(Type.QUERY_PAY_REFUND);
			requestData.setOper(Oper.check);
			requestData.setData(requestList);

			String paramStr = JSONObject.fromObject(requestData).toString();
			// 根据总部返回结果 进行相应的业务出来
//			logger.info("<第三方接口请求体==>" + paramStr);
			posThirdPaymentLogger.info("<第三方接口请求体==>" + paramStr);
			String result = HttpUtil.sendPostRequest(getRequestPath(), paramStr);
//			logger.info("<第三方接口返回体==>" + result);
			posThirdPaymentLogger.info("<第三方接口返回体==>" + result);

			boolean is_succeed = true;// 是否完成交易
			String transaction_no = "";// 交易流水号
			String status = SysDictionary.THIRD_PAY_STATUS_NOOK;// 交易状态
			String request_status = SysDictionary.REQUEST_STATUS_ING;// 请求状态
			String request_code = "";// 请求错误码 总部给出的
			String request_msg = "";// 请求错误描述 总部给出的
			String failure_code = ""; // 第三方给出的订单的错误码
			String failure_msg = "";// 第三方订单的错误描述

			// 根据总部返回结果处理
			if (null != result && !"".equals(result))
			{
				responseData = ParamUtil.stringToData(result);
				List<?> responseList = responseData.getData();
				if (responseList == null || responseList.isEmpty())
				{
					throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
				}
				JSONObject responseJson = JSONObject.fromObject(responseList.get(0));
				if (Constant.CODE_SUCCESS == responseData.getCode())
				{
					responseData.setCode(Constant.CODE_SUCCESS);
					List<JSONObject> resultList = new ArrayList<JSONObject>();
					resultList.add(responseJson);
					responseData.setData(resultList);
					transaction_no = responseJson.optString("transaction_no");
					status = responseJson.optString("payment_state");
					request_status = SysDictionary.REQUEST_STATUS_COMPLETE;
				}
				else
				{
					request_status = SysDictionary.REQUEST_STATUS_FAILURE;
					request_code = String.valueOf(responseData.getCode());
					request_msg = responseData.getMsg();
					is_succeed = false;
					failure_code = responseJson.optString("failure_code");
					failure_msg = responseJson.optString("failure_msg");
				}
			}
			else
			{// 返回为空
				is_succeed = false;
				request_status = SysDictionary.REQUEST_STATUS_FAILURE;
				responseData.setCode(Constant.CODE_NULL_DATASET);
				responseData.setMsg(Constant.GET_PAYMENT_WAY_MESSAGE_IS_NULL);
			}
			// 修改交易记录
			JSONObject updateJson = new JSONObject();
			updateJson.put("is_succeed", is_succeed);
			updateJson.put("transaction_no", transaction_no);
			updateJson.put("status", status);
			updateJson.put("request_status", request_status);
			updateJson.put("request_code", request_code);
			updateJson.put("request_msg", request_msg);
			updateJson.put("failure_code", failure_code);
			updateJson.put("failure_msg", failure_msg);
			paymentDao.updateThirdPaymentOrder(posThirdId, tenancyId, updateJson);
			return responseData;
		}
	}

	@Override
	public PosThirdPaymentOrderEntity getThirdPaymentOrder(String tenantId, int storeId, JSONObject paramJson) throws Exception
	{
		String aidOrderNo = ParamUtil.getStringValueByObject(paramJson, "aid_order_num");
		String objectName = ParamUtil.getStringValueByObject(paramJson, "object_name");
		
		return paymentDao.getThirdPaymentAllByAidOrderNum(tenantId, storeId, aidOrderNo, objectName);
	}

	@Override
	public JSONObject getPaymentClassByuAuthcode(String tenantId, Integer storeId, String authcode) throws Exception
	{
		//查询授权码校验规则
		Map<String, String> sysParamMap = paymentDao.getSysParameter(tenantId, storeId, SysParameter.ALI_PAY_CODE_CHECK_RULE, SysParameter.WECHAT_PAY_CODE_CHECK_RULE);
		if (Tools.isNullOrEmpty(sysParamMap.get(SysParameter.ALI_PAY_CODE_CHECK_RULE)) || Tools.isNullOrEmpty(sysParamMap.get(SysParameter.WECHAT_PAY_CODE_CHECK_RULE)))
		{
			throw SystemException.getInstance(PosErrorCode.NOT_PAY_CODE_CHECK_RULE_ERROR);
		}

		// 扫码合一,校验授权码,判断是否微信,支付宝
		String payType = null;
		if (Pattern.matches(sysParamMap.get(SysParameter.ALI_PAY_CODE_CHECK_RULE), authcode))
		{
			payType = SysDictionary.PAYMENT_CLASS_ALI_PAY;
		}
		else if (Pattern.matches(sysParamMap.get(SysParameter.WECHAT_PAY_CODE_CHECK_RULE), authcode))
		{
			payType = SysDictionary.PAYMENT_CLASS_WECHAT_PAY;
		}
		else
		{
			throw SystemException.getInstance(PosErrorCode.PAYMENT_AUTHCODE_INVALID_ERROR);
		}

		// 查询付款方式
		JSONObject paymentJson = null;
		if (Tools.hv(payType))
		{
			paymentJson = paymentDao.getPaymentWayForOrganByPaymentClass(tenantId, storeId, payType);
		}

		if (null != paymentJson && false == paymentJson.isEmpty())
		{
			return paymentJson;
		}
		else
		{
			throw SystemException.getInstance(PosErrorCode.NOT_EXIST_JZID);
		}
	}

}
