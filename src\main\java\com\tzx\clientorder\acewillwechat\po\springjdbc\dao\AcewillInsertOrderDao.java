package com.tzx.clientorder.acewillwechat.po.springjdbc.dao;

import java.util.List;
import java.util.Map;

import com.tzx.pos.base.dao.BaseDao;
import com.tzx.clientorder.common.entity.AcewillInsertOrder;
import com.tzx.clientorder.common.entity.AcewillPosBillMember;

import net.sf.json.JSONObject;

public interface AcewillInsertOrderDao extends BaseDao{

	String	NAME	= "com.tzx.clientorder.acewillwechat.po.springjdbc.dao.imp.AcewillInsertOrderDaoImpl";
	
	JSONObject getTableStatus(String tenantId, Integer storeId, String tableNo) throws Exception;

	JSONObject getPosBill(String tenantId, Integer storeId, String tableNo) throws Exception;

	void savePosBillMember(AcewillPosBillMember posBillMember) throws Exception;

	String getBillTaste(AcewillInsertOrder insertOrder) throws Exception;

	Map<String, String> getUnitNameMap(AcewillInsertOrder insertOrder) throws Exception;

	Integer getLastItemSerial(String TenentId, String billNUm) throws Exception;

	List<JSONObject> getItemComboDetails(String TenentId, List<Integer> itemIdList) throws Exception;

	void updateOpenId(String billnum, String openId) throws Exception;
	
	List<JSONObject> findItemUnit(String tenentId,List<String> itemIdList) throws Exception;

	void insertOpenId(String tenancyId, Integer storeId, String billnum, String openId) throws Exception;

	JSONObject getPosBillByOrderNum(String tenantId, String orderNum) throws Exception;

	
}
