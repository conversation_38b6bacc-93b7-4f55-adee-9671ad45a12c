package com.tzx.member.common.entity;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

public class CrmIncorporationArrearListEntity implements Serializable
{

	/**
	 * 
	 */
	private static final long	serialVersionUID	= -2032089082211123596L;
	
	private String tenancy_id;
	private Integer id;
	private Integer store_id;
	private Date business_date;
	private String bill_code;
	private String third_bill_code;
	private Double gz_money;
	private Integer incorporation_id;
	private String gz_person;
	private String name;
	private String customer_name;
	private Timestamp operate_time;
	private String operator;
	private String remark;
	private String is_cz;
	private Double cz_money;
	private Double wcz_money;
	private String cz_person;
	private Date cz_time;
	private String last_cz_bill_code;
	private Double mz_money;
	private Double ogz_money;
	private String valid_state;
	
	public String getTenancy_id()
	{
		return tenancy_id;
	}
	public void setTenancy_id(String tenancy_id)
	{
		this.tenancy_id = tenancy_id;
	}
	public Integer getId()
	{
		return id;
	}
	public void setId(Integer id)
	{
		this.id = id;
	}
	public Integer getStore_id()
	{
		return store_id;
	}
	public void setStore_id(Integer store_id)
	{
		this.store_id = store_id;
	}
	public Date getBusiness_date()
	{
		return business_date;
	}
	public void setBusiness_date(Date business_date)
	{
		this.business_date = business_date;
	}
	public String getBill_code()
	{
		return bill_code;
	}
	public void setBill_code(String bill_code)
	{
		this.bill_code = bill_code;
	}
	public String getThird_bill_code()
	{
		return third_bill_code;
	}
	public void setThird_bill_code(String third_bill_code)
	{
		this.third_bill_code = third_bill_code;
	}
	public Double getGz_money()
	{
		return gz_money;
	}
	public void setGz_money(Double gz_money)
	{
		this.gz_money = gz_money;
	}
	public Integer getIncorporation_id()
	{
		return incorporation_id;
	}
	public void setIncorporation_id(Integer incorporation_id)
	{
		this.incorporation_id = incorporation_id;
	}
	public String getGz_person()
	{
		return gz_person;
	}
	public void setGz_person(String gz_person)
	{
		this.gz_person = gz_person;
	}
	public String getName()
	{
		return name;
	}
	public void setName(String name)
	{
		this.name = name;
	}
	public String getCustomer_name()
	{
		return customer_name;
	}
	public void setCustomer_name(String customer_name)
	{
		this.customer_name = customer_name;
	}
	public Timestamp getOperate_time()
	{
		return operate_time;
	}
	public void setOperate_time(Timestamp operate_time)
	{
		this.operate_time = operate_time;
	}
	public String getOperator()
	{
		return operator;
	}
	public void setOperator(String operator)
	{
		this.operator = operator;
	}
	public String getRemark()
	{
		return remark;
	}
	public void setRemark(String remark)
	{
		this.remark = remark;
	}
	public String getIs_cz()
	{
		return is_cz;
	}
	public void setIs_cz(String is_cz)
	{
		this.is_cz = is_cz;
	}
	public Double getCz_money()
	{
		return cz_money;
	}
	public void setCz_money(Double cz_money)
	{
		this.cz_money = cz_money;
	}
	public Double getWcz_money()
	{
		return wcz_money;
	}
	public void setWcz_money(Double wcz_money)
	{
		this.wcz_money = wcz_money;
	}
	public String getCz_person()
	{
		return cz_person;
	}
	public void setCz_person(String cz_person)
	{
		this.cz_person = cz_person;
	}
	public Date getCz_time()
	{
		return cz_time;
	}
	public void setCz_time(Date cz_time)
	{
		this.cz_time = cz_time;
	}
	public String getLast_cz_bill_code()
	{
		return last_cz_bill_code;
	}
	public void setLast_cz_bill_code(String last_cz_bill_code)
	{
		this.last_cz_bill_code = last_cz_bill_code;
	}
	public Double getMz_money()
	{
		return mz_money;
	}
	public void setMz_money(Double mz_money)
	{
		this.mz_money = mz_money;
	}
	public Double getOgz_money()
	{
		return ogz_money;
	}
	public void setOgz_money(Double ogz_money)
	{
		this.ogz_money = ogz_money;
	}
	public String getValid_state()
	{
		return valid_state;
	}
	public void setValid_state(String valid_state)
	{
		this.valid_state = valid_state;
	}
}
