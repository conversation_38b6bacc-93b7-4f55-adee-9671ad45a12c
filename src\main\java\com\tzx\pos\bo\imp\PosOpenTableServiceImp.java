package com.tzx.pos.bo.imp;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.apache.log4j.Logger;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;

import com.tzx.base.bo.PosCodeService;
import com.tzx.framework.common.constant.Code;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.Tools;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.base.util.JsonUtil;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.base.util.ReqDataUtil;
import com.tzx.pos.bo.PosDishService;
import com.tzx.pos.bo.PosOpenTableService;
import com.tzx.pos.bo.dto.PosBillItem;
import com.tzx.pos.bo.dto.PosItemMethod;
import com.tzx.pos.po.springjdbc.dao.PosDishDao;

@Service(PosOpenTableService.NAME)
public class PosOpenTableServiceImp extends PosBaseServiceImp implements PosOpenTableService
{
	private static final Logger	logger	= Logger.getLogger(PosOpenTableService.class);

	@Resource(name = PosCodeService.NAME)
	PosCodeService				codeService;

	@Resource(name = PosDishService.NAME)
	private PosDishService		posDishService;

	@Resource(name = PosDishDao.NAME)
	private PosDishDao			posDishDao;

	@Override
	public void checkReportDate(String tenantId, Integer storeId, Date reportDate) throws Exception
	{
		try
		{
			posDishDao.checkReportDate(tenantId, storeId, reportDate);
		}
		catch (SystemException se)
		{
			throw se;
		}
		catch (Exception e1)
		{
			logger.error("报表日期验证失败:", e1);
			throw SystemException.getInstance(PosErrorCode.CHECK_REPORT_DATE_ERROR);
		}
	}

	@Override
	public Integer checkShiftId(String tenantId, Integer storeId, Date reportDate, Integer shiftId, String optNum, String posNum, String source) throws Exception
	{
		if (SysDictionary.SOURCE_MOVE.contains(source)||SysDictionary.SOURCE_ORDER_DEVICE.equals(source))
		{
			try
			{
				shiftId = posDishDao.getShiftId(tenantId, storeId, reportDate, optNum, posNum);
			}
			catch (SystemException se)
			{
				throw se;
			}
			catch (Exception e)
			{
				e.printStackTrace();
				throw SystemException.getInstance(PosErrorCode.GET_SHIFT_ID_ERROR);
			}
		}

		StringBuffer sql = new StringBuffer("select count(dor.id) from duty_order dor left join duty_order_of_ogran dooo on dor.id = dooo.duty_order_id where dor.id = ? and dooo.organ_id = ? ");
		int dutyCount = posDishDao.queryForInt(sql.toString(), new Object[]
		{ shiftId, storeId });
		if (dutyCount == 0)
		{
			throw new SystemException(PosErrorCode.NOT_EXISTS_DUTY);
		}

		return shiftId;
	}

	@Override
	public synchronized String openTableByTableCode(String tenantId, Integer storeId, String tableCode, String optNum, String posNum, String splitFlag) throws Exception
	{
		StringBuffer sqlState = new StringBuffer("select ti.table_code,ti.fwfz_id,pt.state from tables_info ti left join pos_tablestate pt on ti.tenancy_id=pt.tenancy_id and ti.organ_id=pt.store_id and ti.table_code=pt.table_code where ti.tenancy_id=? and ti.organ_id=? and ti.table_code=?");
		SqlRowSet tableRs = posDishDao.query4SqlRowSet(sqlState.toString(), new Object[]
		{ tenantId, storeId, tableCode });

		String fictitiousTable = null;
		if (tableRs.next())
		{
			if (splitFlag != null && "Y".equals(splitFlag))
			{
				int openNum = 1; // 拆分编号 默认为 1

				StringBuffer openNumSql = new StringBuffer("select max(table_open_num) as max_table_open_num from pos_tablestate where tenancy_id=? and store_id=? and table_open_num is not null and stable= ?");

				SqlRowSet openNumRs = posDishDao.query4SqlRowSet(openNumSql.toString(), new Object[]
				{ tenantId, storeId, tableCode });
				if (openNumRs.next())
				{
					openNum = openNumRs.getInt("max_table_open_num") + 1;
				}

				fictitiousTable = tableCode + "_" + openNum;// 拆分后的桌位号

				String insertTablestate = "insert into pos_tablestate (tenancy_id,store_id,table_code,state,last_updatetime,table_open_num,stable) VALUES (?,?,?,?,?,?,?)";
				posDishDao.update(insertTablestate, new Object[]
				{ tenantId, storeId, fictitiousTable, SysDictionary.TABLE_STATE_BUSY, DateUtil.currentTimestamp(), openNum, tableCode });
			}
			else
			{
				if (!SysDictionary.TABLE_STATE_FREE.equals(tableRs.getString("state")))
				{
					throw SystemException.getInstance(PosErrorCode.TABLE_STATE_BUSY);
				}

				StringBuffer updateSql = new StringBuffer("update pos_tablestate set state = ?, bill_batch_num=? where table_code = ? and store_id = ? and tenancy_id = ? and state = ?");
				int updatecount = posDishDao.update(updateSql.toString(), new Object[]
				{ SysDictionary.TABLE_STATE_BUSY, null, tableCode, storeId, tenantId, SysDictionary.TABLE_STATE_FREE });
				if (0 == updatecount)
				{
					throw SystemException.getInstance(PosErrorCode.TABLE_STATE_BUSY);
				}
				fictitiousTable = tableCode;
			}
		}
		else
		{
			throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_TABLE_CODE);
		}
		return fictitiousTable;
	}

	@Override
	public String addPosBill(String tenantId, Integer storeId, Date reportDate, Integer shiftId, String fictitiousTable, JSONObject paramJson) throws Exception
	{
		String tableCode = ParamUtil.getStringValueByObject(paramJson, "table_code", false, null);
		String mode = ParamUtil.getStringValueByObject(paramJson, "mode", true, PosErrorCode.NOT_NULL_MODE);
		String optNum = ParamUtil.getStringValueByObject(paramJson, "opt_num", false, null);
		String waiterNum = ParamUtil.getStringValueByObject(paramJson, "waiter_num", false, null);
		String posNum = ParamUtil.getStringValueByObject(paramJson, "pos_num", true, PosErrorCode.NOT_EXISTS_POSNUM);
		Integer guest = ParamUtil.getIntegerValueByObject(paramJson, "guest", false, null);
		Integer itemMenuId = ParamUtil.getIntegerValueByObject(paramJson, "item_menu_id", false, null);
		String preorderno = ParamUtil.getStringValueByObject(paramJson, "preorderno", false, null);
		String copyBillNum = ParamUtil.getStringValueByObject(paramJson, "copy_bill_num", false, null);
		String remark = ParamUtil.getStringValueByObject(paramJson, "remark", false, null);
		Double shopRealAmount = ParamUtil.getDoubleValueByObject(paramJson, "shop_real_amount", false, null);
		Double platformChargeAmount = ParamUtil.getDoubleValueByObject(paramJson, "platform_charge_amount", false, null);
		String settlementType = ParamUtil.getStringValueByObject(paramJson, "settlement_type", false, null);
		// 是否拆台
		String splitFlag = ParamUtil.getStringValueByObject(paramJson, "split_flag", false, null);
		// 就餐类型
		String dinnerType = ParamUtil.getStringValueByObject(paramJson, "dinner_type", false, null);

		int discountModeId = 0;
		if (paramJson.containsKey("discount_mode_id"))
		{
			discountModeId = ParamUtil.getIntegerValueByObject(paramJson, "discount_mode_id", false, null);
		}

		double discountkAmount = 0d;
		if (paramJson.containsKey("discountk_amount"))
		{
			discountkAmount = ParamUtil.getDoubleValueByObject(paramJson, "discountk_amount", false, null);
		}

		double discountRate = 100d;
		if (paramJson.containsKey("discount_rate"))
		{
			discountRate = ParamUtil.getDoubleValueByObject(paramJson, "discount_rate", false, null);
		}

		String saleMode = ParamUtil.getStringValueByObject(paramJson, "sale_mode", false, null);
		if (Tools.isNullOrEmpty(saleMode))
		{
			saleMode = SysDictionary.SALE_MODE_TS01;
		}

		String chanel = ParamUtil.getStringValueByObject(paramJson, "chanel", false, null);
		if (Tools.isNullOrEmpty(chanel))
		{
			chanel = SysDictionary.CHANEL_MD01;
		}

		if (Tools.isNullOrEmpty(itemMenuId))
		{
			itemMenuId = 0;
		}

		if (Tools.isNullOrEmpty(waiterNum))
		{
			waiterNum = optNum;
		}

		double serviceAmount = 0d;
		if (paramJson.containsKey("service_amount"))
		{
			serviceAmount = ParamUtil.getDoubleValueByObject(paramJson, "service_amount", false, null);
		}

		Integer serviceId = 0;
		if (paramJson.containsKey("service_id"))
		{
			serviceId = ParamUtil.getIntegerValueByObject(paramJson, "service_id", false, null);
		}		
		
		// POS账单编号
		String billNum = "";
		// POS流水单号
		String serialNum = "";

		Timestamp time = DateUtil.currentTimestamp();
		String bill_property = SysDictionary.BILL_PROPERTY_OPEN;

		// String stable = tableCode;//原桌位号

		if (mode.equals("0"))
		{
			if (false == "Y".equals(splitFlag))
			{
				StringBuffer sqlState = new StringBuffer("select ti.table_code,ti.fwfz_id from tables_info ti where ti.tenancy_id=? and ti.organ_id=? and ti.table_code=?");
				SqlRowSet tableRs = posDishDao.query4SqlRowSet(sqlState.toString(), new Object[]
				{ tenantId, storeId, tableCode });

				if (tableRs.next())
				{
					if (SysDictionary.CHANEL_MD01.equals(chanel))
					{
						serviceId = tableRs.getInt("fwfz_id");
					}
				}
			}

			try
			{
				// 生成新的账单号和流水单号
				JSONObject object = new JSONObject();
				object.element("store_id", storeId);
				object.element("busi_date", reportDate);
				billNum = codeService.getCode(tenantId, Code.POS_BILL_CODE, object);// 调用统一接口来实现
				object.put("pos_num", posNum);
				serialNum = codeService.getCode(tenantId, Code.POS_RECORD_CODE, object); // 调用统一接口来实现
			}
			catch (Exception e)
			{
				logger.info("开台：" + ExceptionMessage.getExceptionMessage(e));
				throw new SystemException(PosErrorCode.GET_BILLNUM_SERVICE_ERROR);
			}

			posDishDao.insertPosBill(tenantId, storeId, reportDate, shiftId, billNum, null, serialNum, preorderno, time, posNum, optNum, waiterNum, tableCode, guest, itemMenuId, saleMode, chanel, serviceId, serviceAmount, copyBillNum, remark, bill_property, SysDictionary.PAYMENT_STATE_NOTPAY,
					discountModeId, discountkAmount, shopRealAmount, platformChargeAmount, settlementType, discountRate, fictitiousTable, dinnerType);

			// 处理服务费
			updateTableService(tenantId, storeId, billNum, chanel, tableCode, serviceId, serviceAmount);

			// 生成新账单
			String str0 = new String("insert into pos_bill_waiter (tenancy_id,store_id,bill_num,waiter_num,report_date) values (?,?,?,?,?)");
			posDishDao.update(str0, new Object[]
			{ tenantId, storeId, billNum, waiterNum, reportDate });

			posDishDao.savePosLog(tenantId, storeId, posNum, optNum, optNum, shiftId, reportDate, Constant.TITLE, "开台", "开台桌位:" + fictitiousTable, "账单编号:" + billNum);
			
//			try
//			{
//				this.updateDevicesDataState();
//			}
//			catch (Exception e)
//			{
//				e.printStackTrace();
//				logger.error("updateDevicesDataState,修改数据状态失败", e);
//			}
		}
		else if (mode.equals("1"))
		{// 快餐开台
			try
			{
				JSONObject object = new JSONObject();
				object.put("store_id", storeId);
				object.put("busi_date", reportDate);
				billNum = codeService.getCode(tenantId, Code.POS_BILL_CODE, object);// 调用统一接口来实现
				object.put("pos_num", posNum);
				serialNum = codeService.getCode(tenantId, Code.POS_RECORD_CODE, object); // 调用统一接口来实现
			}
			catch (Exception e)
			{
				logger.info("开台：" + ExceptionMessage.getExceptionMessage(e));
				throw new SystemException(PosErrorCode.GET_BILLNUM_SERVICE_ERROR);
			}

			posDishDao.insertPosBill(tenantId, storeId, reportDate, shiftId, billNum, null, serialNum, preorderno, time, posNum, optNum, waiterNum, tableCode, guest, itemMenuId, saleMode, chanel, serviceId, serviceAmount, copyBillNum, remark, bill_property, SysDictionary.PAYMENT_STATE_NOTPAY,
					discountModeId, discountkAmount, shopRealAmount, platformChargeAmount, settlementType, discountRate, tableCode, dinnerType);

			// 处理服务费
			updateTableService(tenantId, storeId, billNum, chanel, null, serviceId, serviceAmount);

			String str0 = new String("insert into pos_bill_waiter (tenancy_id,store_id,bill_num,waiter_num,report_date) values (?,?,?,?,?)");
			posDishDao.update(str0, new Object[]
			{ tenantId, storeId, billNum, waiterNum, reportDate });

			posDishDao.savePosLog(tenantId, storeId, posNum, optNum, optNum, shiftId, reportDate, Constant.TITLE, "开台", "桌位编号:" + tableCode, "账单编号:" + billNum);
		}
		return billNum;
	}
	
	@Override
	public String openTableByTableCode(String tenantId, Integer storeId, Date reportDate, Integer shiftId, JSONObject paramJson) throws Exception
	{
		String tableCode = ParamUtil.getStringValueByObject(paramJson, "table_code", false, null);
		String mode = ParamUtil.getStringValueByObject(paramJson, "mode", true, PosErrorCode.NOT_NULL_MODE);
		String optNum = ParamUtil.getStringValueByObject(paramJson, "opt_num", false, null);
		String waiterNum = ParamUtil.getStringValueByObject(paramJson, "waiter_num", false, null);
		String posNum = ParamUtil.getStringValueByObject(paramJson, "pos_num", true, PosErrorCode.NOT_EXISTS_POSNUM);
		Integer guest = ParamUtil.getIntegerValueByObject(paramJson, "guest", false, null);
		Integer itemMenuId = ParamUtil.getIntegerValueByObject(paramJson, "item_menu_id", false, null);
		String preorderno = ParamUtil.getStringValueByObject(paramJson, "preorderno", false, null);
		String copyBillNum = ParamUtil.getStringValueByObject(paramJson, "copy_bill_num", false, null);
		String remark = ParamUtil.getStringValueByObject(paramJson, "remark", false, null);
		Double shopRealAmount = ParamUtil.getDoubleValueByObject(paramJson, "shop_real_amount", false, null);
		Double platformChargeAmount = ParamUtil.getDoubleValueByObject(paramJson, "platform_charge_amount", false, null);
		String settlementType = ParamUtil.getStringValueByObject(paramJson, "settlement_type", false, null);
		// 是否拆台
		String splitFlag = ParamUtil.getStringValueByObject(paramJson, "split_flag", false, null);
		String splitTableCode = ParamUtil.getStringValueByObject(paramJson, "split_table_code", false, null);
		// 就餐类型
		String dinnerType = ParamUtil.getStringValueByObject(paramJson, "dinner_type", false, null);

		if(guest>99999){
            throw SystemException.getInstance(PosErrorCode.EXCEEDING_THE_MAXIMUM_NUMBER_OF_PEOPLE_ALLOWED);
        }

		int discountModeId = 0;
		if (paramJson.containsKey("discount_mode_id"))
		{
			discountModeId = ParamUtil.getIntegerValueByObject(paramJson, "discount_mode_id", false, null);
		}

		double discountkAmount = 0d;
		if (paramJson.containsKey("discountk_amount"))
		{
			discountkAmount = ParamUtil.getDoubleValueByObject(paramJson, "discountk_amount", false, null);
		}

		double discountRate = 100d;
		if (paramJson.containsKey("discount_rate"))
		{
			discountRate = ParamUtil.getDoubleValueByObject(paramJson, "discount_rate", false, null);
		}
		
		String saleMode = ParamUtil.getStringValueByObject(paramJson, "sale_mode", false, null);
		if (Tools.isNullOrEmpty(saleMode))
		{
			saleMode = SysDictionary.SALE_MODE_TS01;
		}

		String chanel = ParamUtil.getStringValueByObject(paramJson, "chanel", false, null);
		if (Tools.isNullOrEmpty(chanel))
		{
			chanel = SysDictionary.CHANEL_MD01;
		}

		if (Tools.isNullOrEmpty(itemMenuId))
		{
			itemMenuId = 0;
		}

		if (Tools.isNullOrEmpty(waiterNum))
		{
			waiterNum = optNum;
		}

		double serviceAmount = 0d;
		if (paramJson.containsKey("service_amount"))
		{
			serviceAmount = ParamUtil.getDoubleValueByObject(paramJson, "service_amount", false, null);
		}

		Integer serviceId = 0;
		if (paramJson.containsKey("service_id"))
		{
			serviceId = ParamUtil.getIntegerValueByObject(paramJson, "service_id", false, null);
		}
		
		// POS账单编号
		String billNum = "";
		// POS流水单号
		String serialNum = "";

		Timestamp time = DateUtil.currentTimestamp();
		String bill_property = SysDictionary.BILL_PROPERTY_OPEN;

		// String stable = tableCode;//原桌位号

		if (mode.equals("0"))
		{

			StringBuffer sqlState = new StringBuffer("select ti.table_code,ti.fwfz_id,pt.state from tables_info ti left join pos_tablestate pt on ti.tenancy_id=pt.tenancy_id and ti.organ_id=pt.store_id and ti.table_code=pt.table_code where ti.tenancy_id=? and ti.organ_id=? and ti.table_code=?");
			SqlRowSet tableRs = posDishDao.query4SqlRowSet(sqlState.toString(), new Object[]
			{ tenantId, storeId, tableCode });

			String fictitiousTable = null;
			if (tableRs.next())
			{
				if (splitFlag != null && "Y".equals(splitFlag))
				{
					int openNum = 1; // 拆分编号 默认为 1
					String state = "";
					if(Tools.hv(splitTableCode)){ //点菜宝调用拆台时 根据传单参数拆台
						fictitiousTable =splitTableCode;// 拆分后的桌位号
						//点菜宝拆台 验证 splitTableCode 是否已经存在 和 状态
						StringBuffer stateSql = new StringBuffer("SELECT id,state from  pos_tablestate where tenancy_id=? and store_id=? and table_open_num is not null and stable= ? and table_code = ?");
						SqlRowSet stateRs = posDishDao.query4SqlRowSet(stateSql.toString(), new Object[]{ tenantId, storeId, tableCode,splitTableCode});
						if (stateRs.next())
						{
							state = stateRs.getString("state");
						}
						if(Tools.isNullOrEmpty(state)){
							if(splitTableCode.contains("_")){
								String[] splitTableCodes = splitTableCode.split("_");
								if(splitTableCodes.length>0){
									openNum = Integer.parseInt(splitTableCodes[1]);
								}

							}
							String insertTablestate = "insert into pos_tablestate (tenancy_id,store_id,table_code,state,last_updatetime,table_open_num,stable) VALUES (?,?,?,?,?,?,?)";
							posDishDao.update(insertTablestate, new Object[]{ tenantId, storeId, fictitiousTable, SysDictionary.TABLE_STATE_BUSY, DateUtil.currentTimestamp(), openNum, tableCode });
						}else{
							if(SysDictionary.TABLE_STATE_BUSY.equals(state)){
								throw new SystemException(PosErrorCode.SPLIT_TABLE_STATE_BUSY); //拆台桌台号已经存在
							}else{
								String updateTablestate = "update pos_tablestate set state =?,last_updatetime=? where stable= ? and table_code = ? and tenancy_id =? and store_id=?";
								posDishDao.update(updateTablestate, new Object[]{ SysDictionary.TABLE_STATE_BUSY, DateUtil.currentTimestamp(),tableCode,fictitiousTable,tenantId, storeId});
							}
						}
					}else{

						StringBuffer openNumSql = new StringBuffer("select max(table_open_num) as max_table_open_num from pos_tablestate where tenancy_id=? and store_id=? and table_open_num is not null and stable= ?");

						SqlRowSet openNumRs = posDishDao.query4SqlRowSet(openNumSql.toString(), new Object[]
								{ tenantId, storeId, tableCode });
						if (openNumRs.next())
						{
							openNum = openNumRs.getInt("max_table_open_num") + 1;
						}
						fictitiousTable = tableCode + "_" + openNum;// 拆分后的桌位号
						String insertTablestate = "insert into pos_tablestate (tenancy_id,store_id,table_code,state,last_updatetime,table_open_num,stable) VALUES (?,?,?,?,?,?,?)";
						posDishDao.update(insertTablestate, new Object[]
								{ tenantId, storeId, fictitiousTable, SysDictionary.TABLE_STATE_BUSY, DateUtil.currentTimestamp(), openNum, tableCode });
					}
				}
				else
				{
					if (SysDictionary.CHANEL_MD01.equals(chanel))
					{
						serviceId = tableRs.getInt("fwfz_id");
					}

					if (!SysDictionary.TABLE_STATE_FREE.equals(tableRs.getString("state")))
					{
						throw SystemException.getInstance(PosErrorCode.TABLE_STATE_BUSY);
					}

					StringBuffer updateSql = new StringBuffer("update pos_tablestate set state = ?, bill_batch_num=? where table_code = ? and store_id = ? and tenancy_id = ? and state = ?");
					int i = posDishDao.update(updateSql.toString(), new Object[]
					{ SysDictionary.TABLE_STATE_BUSY, null, tableCode, storeId, tenantId, SysDictionary.TABLE_STATE_FREE });

					if (0 == i)
					{
						throw SystemException.getInstance(PosErrorCode.TABLE_STATE_BUSY);
					}

					fictitiousTable = tableCode;
				}
			}
			else
			{
				throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_TABLE_CODE);
			}

			try
			{
				// 生成新的账单号和流水单号
				JSONObject object = new JSONObject();
				object.element("store_id", storeId);
				object.element("busi_date", reportDate);
				billNum = codeService.getCode(tenantId, Code.POS_BILL_CODE, object);// 调用统一接口来实现
				object.put("pos_num", posNum);
				serialNum = codeService.getCode(tenantId, Code.POS_RECORD_CODE, object); // 调用统一接口来实现
				if(Tools.isNullOrEmpty(billNum))
				{
					throw new SystemException(PosErrorCode.GET_BILLNUM_SERVICE_ERROR);
				}
			}
			catch (Exception e)
			{
				logger.info("开台：" + ExceptionMessage.getExceptionMessage(e));
				throw new SystemException(PosErrorCode.GET_BILLNUM_SERVICE_ERROR);
			}

			posDishDao.insertPosBill(tenantId, storeId, reportDate, shiftId, billNum, null, serialNum, preorderno, time, posNum, optNum, waiterNum, tableCode, guest, itemMenuId, saleMode, chanel, serviceId, serviceAmount, copyBillNum, remark, bill_property, SysDictionary.PAYMENT_STATE_NOTPAY,
					discountModeId, discountkAmount, shopRealAmount, platformChargeAmount, settlementType, discountRate, fictitiousTable, dinnerType);

			// 处理服务费
			updateTableService(tenantId, storeId, billNum, chanel, tableCode, serviceId, serviceAmount);

			// 生成新账单
			String str0 = new String("insert into pos_bill_waiter (tenancy_id,store_id,bill_num,waiter_num,report_date) values (?,?,?,?,?)");
			posDishDao.update(str0, new Object[]
			{ tenantId, storeId, billNum, waiterNum, reportDate });

			posDishDao.savePosLog(tenantId, storeId, posNum, optNum, optNum, shiftId, reportDate, Constant.TITLE, "开台", "开台桌位:" + fictitiousTable, "账单编号:" + billNum);
		}
		else if (mode.equals("1"))
		{// 快餐开台
			try
			{
				JSONObject object = new JSONObject();
				object.put("store_id", storeId);
				object.put("busi_date", reportDate);
				billNum = codeService.getCode(tenantId, Code.POS_BILL_CODE, object);// 调用统一接口来实现
				object.put("pos_num", posNum);
				serialNum = codeService.getCode(tenantId, Code.POS_RECORD_CODE, object); // 调用统一接口来实现
				
				if(Tools.isNullOrEmpty(billNum))
				{
					throw new SystemException(PosErrorCode.GET_BILLNUM_SERVICE_ERROR);
				}
			}
			catch (Exception e)
			{
				logger.info("开台：" + ExceptionMessage.getExceptionMessage(e));
				throw new SystemException(PosErrorCode.GET_BILLNUM_SERVICE_ERROR);
			}

			posDishDao.insertPosBill(tenantId, storeId, reportDate, shiftId, billNum, null, serialNum, preorderno, time, posNum, optNum, waiterNum, tableCode, guest, itemMenuId, saleMode, chanel, serviceId, serviceAmount, copyBillNum, remark, bill_property, SysDictionary.PAYMENT_STATE_NOTPAY,
					discountModeId, discountkAmount, shopRealAmount, platformChargeAmount, settlementType, discountRate, tableCode, dinnerType);

			// 处理服务费
			updateTableService(tenantId, storeId, billNum, chanel, null, serviceId, serviceAmount);

			String str0 = new String("insert into pos_bill_waiter (tenancy_id,store_id,bill_num,waiter_num,report_date) values (?,?,?,?,?)");
			posDishDao.update(str0, new Object[]
			{ tenantId, storeId, billNum, waiterNum, reportDate });

			posDishDao.savePosLog(tenantId, storeId, posNum, optNum, optNum, shiftId, reportDate, Constant.TITLE, "开台", "桌位编号:" + tableCode, "账单编号:" + billNum);
		}
		return billNum;
	}

	private void updateTableService(String tenantId, Integer storeId, String billNum, String chanel, String talbeCode, Integer serviceId, Double serviceAmount) throws Exception
	{
		if (null != serviceId && serviceId != 0)
		{
			StringBuilder sqlService = new StringBuilder("insert into pos_bill_service(tenancy_id,store_id,bill_num,table_code,service_id,service_type,taken_mode,service_scale,service_count, service_rate)");
			sqlService.append(" select tenancy_id,").append(storeId).append(",'").append(billNum).append("','").append(talbeCode).append("',id, fee_type, taken_mode,");
			sqlService.append(" (case taken_mode when '").append(SysDictionary.SERVICE_MODE_GD01).append("' then guding_jj else fwfl end) as service_scale,1,100");
			sqlService.append(" from hq_service_fee_type where id ='").append(serviceId).append("' and tenancy_id = '").append(tenantId).append("' ");
			posDishDao.update(sqlService.toString(), new Object[] {});
		}
		else if (null != serviceAmount && serviceAmount > 0)
		{
			StringBuilder sqlService = new StringBuilder("insert into pos_bill_service(tenancy_id,store_id,bill_num,table_code,service_id,service_type,taken_mode,service_scale,service_count, service_rate)");
			sqlService.append(" values(?,?,?,?,?,?,?,?,?,?)");

			posDishDao.update(sqlService.toString(), new Object[]
			{ tenantId, storeId, billNum, talbeCode, serviceId, SysDictionary.SERVICE_FEE_TYPE_JB01, SysDictionary.SERVICE_MODE_GD01, serviceAmount, 1d, 100d });
		}
	}

	@Override
	public void updateDevicesDataState(String tenantId, Integer storeId) throws Exception
	{
//		String deleteSql = "delete from hq_devices_datastate ";
//		posDishDao.update(deleteSql, null);
//		String insertSql = "insert into hq_devices_datastate (devices_code,refresh_flag) select distinct devices_code,'0' from hq_devices where devices_code is not null and devices_code <> ''";
//		posDishDao.update(insertSql, null);
		
		String insertSql = "insert into hq_devices_datastate (devices_code,refresh_flag) select distinct hd.devices_code,'0' from hq_devices hd left join hq_devices_datastate hdd on hd.devices_code=hdd.devices_code where hd.valid_state='1' and hd.devices_code is not null and hd.devices_code <> '' and hdd.devices_code is null";
		posDishDao.update(insertSql, null);
		String updateSql = "update hq_devices_datastate set refresh_flag='0' where refresh_flag<>'0'";
		posDishDao.update(updateSql, null);
	}

	@Override
	public Data getDefaultDish(Data param, String billNum, JSONObject printJson) throws Exception
	{
		String tenantId = param.getTenancy_id();
		Integer organId = param.getStore_id();
		Map<String, Object> maps = ReqDataUtil.getDataMap(param);
		String guest = ParamUtil.getStringValue(maps, "guest", false, null);

		Data data = new Data();
		List<JSONObject> dishs = getDefaultDishList(tenantId, organId, guest, printJson);
		if (null != dishs && dishs.size() > 0)
		{
			JSONObject para = JSONObject.fromObject(param.getData().get(0));
			para.put("bill_num", billNum);
			para.put("isprint", "Y");
			para.put("item", dishs);
			para.put("mode", "0");

			List<JSONObject> paraList = new ArrayList<JSONObject>();
			paraList.add(para);

			param.setData(paraList);
			data = posDishService.newestOrderDish(param, printJson);
		}		
		return data;
	}

	public List<JSONObject> getDefaultDishList(String tenantId, Integer storeId, String guest, JSONObject printJson) throws Exception
	{
		StringBuilder sql = new StringBuilder();
		sql.append(" select distinct cmid.item_id,cmid.unit_id,him.details_id,him.item_name,him.item_english,him.class,hii.item_code,hiu.unit_name,coalesce(hip.price,hiu.standard_price) as price,him.menu_item_rank,him.classorder from cc_meals_info cmi");
		sql.append(" left join cc_meals_info_default cmid on cmid.tenancy_id=cmi.tenancy_id and cmid.meals_id=cmi.id");
		sql.append(" left join (select himo.*,himd.id as details_id,himd.item_id,himc.chanel,himc.class,himc.item_name,himc.item_english,himd.menu_item_rank,himco.classorder from hq_item_menu_organ himo");
		sql.append(" left join hq_item_menu him on himo.tenancy_id=him.tenancy_id and him.id=himo.item_menu_id");
		sql.append(" left join hq_item_menu_details himd on himd.tenancy_id=him.tenancy_id and himd.item_menu_id=him.id");
		sql.append(" left join hq_item_menu_class himc on himd.tenancy_id=himc.tenancy_id and himc.details_id=himd.id ");
		sql.append(" left join hq_item_menu_classorder himco on himc.class=himco.class_id and him.id=himco.menu_id");
		sql.append(" where him.valid_state='1' and himd.valid_state='1' ) him");
		sql.append(" on him.tenancy_id=cmi.tenancy_id and him.store_id = cmi.store_id and him.item_id=cmid.item_id and him.chanel=cmi.channel");
		sql.append(" left join organ og on og.tenancy_id=cmi.tenancy_id and og.id=cmi.store_id");
		sql.append(" left join hq_item_info hii on hii.tenancy_id=him.tenancy_id and hii.id=him.item_id and hii.valid_state='1' and hii.is_combo='N'");
		sql.append(" left join hq_item_unit hiu on hiu.tenancy_id=hii.tenancy_id and hiu.item_id=hii.id and hiu.id=cmid.unit_id and hiu.valid_state='1'");
		sql.append(" left join hq_item_pricesystem hip on hip.tenancy_id=hiu.tenancy_id and hip.item_unit_id=hiu.id and hip.chanel=him.chanel and hip.price_system||''=og.price_system");
		sql.append(" where cmi.meals_type='MR03' and cmi.valid_state ='1' and him.details_id is not null and (hip.price is not null or hiu.standard_price is not null)");
		sql.append(" and cmi.tenancy_id=? and cmi.store_id=? and cmi.channel=? order by him.classorder,him.menu_item_rank");
		List<JSONObject> lists = posDishDao.query4Json(tenantId, sql.toString(), new Object[]
		{ tenantId, storeId, SysDictionary.CHANEL_MD01 });
		List<JSONObject> dishs = new ArrayList<JSONObject>();
		if (lists.size() > 0)
		{
			for (int i = 0; i < lists.size(); i++)
			{
				JSONObject jo = lists.get(i);
				JSONObject defaultDish = new JSONObject();
				defaultDish.put("item_serial", i + 1);
				defaultDish.put("details_id", jo.optString("details_id"));
				defaultDish.put("sale_mode", "");
				defaultDish.put("assist_num", "");
				defaultDish.put("seat_num", "");
				defaultDish.put("unit_id", jo.optString("unit_id"));
				defaultDish.put("item_count", guest);
				defaultDish.put("item_price", jo.optString("price"));
				defaultDish.put("item_remark", "");
				defaultDish.put("item_id", jo.optString("item_id"));
				defaultDish.put("item_num", jo.optString("item_code"));
				defaultDish.put("item_name", jo.optString("item_name"));
				defaultDish.put("item_unit_name", jo.optString("unit_name"));
				defaultDish.put("assist_money", "");
				defaultDish.put("item_property", SysDictionary.ITEM_PROPERTY_SINGLE);
				defaultDish.put("reason_id", "");
				defaultDish.put("item_taste", "");
				defaultDish.put("setmeal_id", "0");
				defaultDish.put("setmeal_rwid", "0");
				defaultDish.put("assist_item_id", "0");
				defaultDish.put("waitcall_tag", "");
				defaultDish.put("taste", "");
				defaultDish.put("method", "");
				defaultDish.put("default_state", "Y");
				dishs.add(defaultDish);
			}
			return dishs;
		}
		else
		{
			return null;
		}
	}

	@Override
	public List<JSONObject> getPosBillByBillNum(String tenantId, Integer storeId, String billNum, String tableCode) throws Exception
	{
		List<JSONObject> billList = posDishDao.openTable(tenantId, storeId, billNum, tableCode);
		// 查询账单明细和做法
		for(JSONObject billJson:billList)
		{
			StringBuilder itemMethodSql = new StringBuilder();
			itemMethodSql.append(" select bi.default_state,bi.opt_num,bi.discount_reason_id,bi.discount_mode_id,zfkw.methods,bi.bill_num, bi.item_unit_name, bi.item_serial, bi.rwid, bi.item_id, bi.item_num, bi.item_name, bi.item_remark, bi.item_property, bi.served_state,bi.remark, bi.item_unit_id as unit_id, bi.details_id,bi.sale_mode,bi.returngive_reason_id reason_id,bi.manager_num, ");
			itemMethodSql.append(" round(bi.discount_rate, 2) as discount_rate, round(bi.discount_amount+bi.discountr_amount, 2) as discount_amount, round(bi.item_price, 2) as item_price,round(bi.item_count, 4) as item_count, round(bi.item_amount, 2) as item_amount, round(bi.real_amount, 2) as real_amount,  trim (bi.item_taste) as item_taste, ");
			itemMethodSql.append(" (case when bi.item_remark = '" + SysDictionary.ITEM_REMARK_TC01 + "' then 0 else bi.waitcall_tag end) as waitcall_tag,(case when bi.item_remark = '" + SysDictionary.ITEM_REMARK_FS02 + "' then '" + SysDictionary.PAYMENT_STATE_PAY_COMPLETE+ "' else coalesce(p .payment_state, '03') end ) as payment_state,");
			itemMethodSql.append(" bi.setmeal_id, bi.setmeal_rwid,bi.assist_num,bi.assist_money,bi.print_tag,bi.assist_item_id,bi.item_time,bi.waiter_num as waiter_name, bi.item_mac_id, bi.item_time, bi.assist_num, bi.method_money, bi.batch_num,bi.activity_id,bi.activity_batch_num,bi.activity_rule_id,bi.activity_count,bi.single_amount,bi.single_discount_rate,bi.combo_prop,bi.origin_item_price ");
			itemMethodSql.append(" from pos_bill_item bi inner join pos_bill b on b.tenancy_id = bi.tenancy_id and b.store_id = bi.store_id and b.bill_num = bi.bill_num");
			itemMethodSql.append(" left join (select tenancy_id,store_id,bill_num,rwid,string_agg(cast(zfkw_id as varchar)||'#'||zfkw_name||'#'||cast(coalesce(amount, 0) as varchar), '&') methods from pos_zfkw_item where type = '"+ SysDictionary.ZFKW_TYPE_METHOD + "' group by tenancy_id,store_id,bill_num,rwid) zfkw on zfkw.tenancy_id = bi.tenancy_id and zfkw.store_id = bi.store_id and zfkw.bill_num = bi.bill_num and zfkw.rwid = bi.rwid");
			itemMethodSql.append(" left join (select tenancy_id,store_id,bill_num,batch_num, (case when count (nullif(payment_state, '" + SysDictionary.PAYMENT_STATE_PAY_COMPLETE + "')) > 0 then '" + SysDictionary.PAYMENT_STATE_PAY + "' else '" + SysDictionary.PAYMENT_STATE_PAY_COMPLETE+ "' end ) as payment_state from pos_bill_payment group by tenancy_id, store_id, bill_num, batch_num ) p on bi.tenancy_id = p .tenancy_id and bi.store_id = p .store_id and bi.bill_num = p .bill_num and bi.batch_num = p .batch_num");
			itemMethodSql.append(" where bi.bill_num =? and bi.store_id = ? and bi.tenancy_id = ? and (bi.item_remark is null or bi.item_remark <> '" + SysDictionary.ITEM_REMARK_CJ05 + "')  order by bi.item_serial, bi.rwid ");

			List<JSONObject> itemJsonList = posDishDao.query4Json(tenantId, itemMethodSql.toString(), new Object[]
			{ billJson.optString("bill_num"), storeId, tenantId });
					
			List<JSONObject> itemList = new ArrayList<JSONObject>();
			if (itemJsonList != null && itemJsonList.size() > 0)
			{
				JSONObject billItem = null;
				for (JSONObject itemJson : itemJsonList)
				{
					billItem = JsonUtil.jsonFormat(itemJson, PosBillItem.class);

					// 遍历账单明细的口味做法
					List<PosItemMethod> methodList = new ArrayList<PosItemMethod>();
					String methodsStr = itemJson.optString("methods"); // 口味做法
					if (Tools.hv(methodsStr))
					{
						String[] methods = methodsStr.split("&");
						for (String method : methods)
						{
							String[] methodStr = Tools.hv(method) ? method.split("#") : null;

							if (null != methodStr && 0 < methodStr.length)
							{
								PosItemMethod itemMethod = new PosItemMethod();
								itemMethod.setMethod_id(methodStr[0]);
								itemMethod.setMethod_name(methodStr[1]);
								itemMethod.setAmount(methodStr[2]);

								methodList.add(itemMethod);
							}
						}
					}
					billItem.put("method", methodList);

					itemList.add(billItem);
				}
			}		
			billJson.put("detaillist", itemList);
		}
		return billList;
	}

	@Override
	public void openTableForRollBackByTableCode(String tenantId, Integer storeId, String tableCode) throws Exception
	{
		StringBuffer tableStateSql = new StringBuffer("select * from pos_tablestate where tenancy_id=? and store_id=? and table_code= ?");

		SqlRowSet tableStateRs = posDishDao.query4SqlRowSet(tableStateSql.toString(), new Object[]
		{ tenantId, storeId, tableCode });
		if (tableStateRs.next())
		{
			if (0 > tableStateRs.getInt("table_open_num"))
			{
				String deleteTablestate = "delete from pos_tablestate where table_code = ? and store_id = ? and tenancy_id = ?";
				posDishDao.update(deleteTablestate, new Object[]
				{ tableCode, storeId, tenantId });
			}
			else
			{
				StringBuffer updateSql = new StringBuffer("update pos_tablestate set state = ?, bill_batch_num=? where table_code = ? and store_id = ? and tenancy_id = ?");
				posDishDao.update(updateSql.toString(), new Object[]
				{ SysDictionary.TABLE_STATE_FREE, null, tableCode, storeId, tenantId });
			}
		}
	}
	
	@Override
	public boolean getHqItemTables(String tenantId, Integer storeId, String tableCode) throws Exception{
		return  posDishDao.getHqItemTables(tenantId, storeId, tableCode);
	}

}
