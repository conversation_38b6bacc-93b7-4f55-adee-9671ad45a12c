package com.tzx.pos.bo.dto;

import java.sql.Timestamp;
import java.util.Date;

/**
 * <AUTHOR> 
 * @日期：2015年5月18日-下午2:51:14
 */

public class PosLog
{
	private String tenancyId;
	private Integer organId;
	private String posNum;
	private String optNum;
	private String optName;
	private String title;
	private String content;
	private String oldState;
	private String newState;
	private Integer shiftId;
	private Date reportDate;
	private Timestamp lastUpdateTime;
	public String getTenancyId()
	{
		return tenancyId;
	}
	public void setTenancyId(String tenancyId)
	{
		this.tenancyId = tenancyId;
	}
	public Integer getOrganId()
	{
		return organId;
	}
	public void setOrganId(Integer organId)
	{
		this.organId = organId;
	}
	public String getPosNum()
	{
		return posNum;
	}
	public void setPosNum(String posNum)
	{
		this.posNum = posNum;
	}
	public String getOptNum()
	{
		return optNum;
	}
	public void setOptNum(String optNum)
	{
		this.optNum = optNum;
	}
	public String getOptName()
	{
		return optName;
	}
	public void setOptName(String optName)
	{
		this.optName = optName;
	}
	public String getTitle()
	{
		return title;
	}
	public void setTitle(String title)
	{
		this.title = title;
	}
	public String getContent()
	{
		return content;
	}
	public void setContent(String content)
	{
		this.content = content;
	}
	public String getOldState()
	{
		return oldState;
	}
	public void setOldState(String oldState)
	{
		this.oldState = oldState;
	}
	public String getNewState()
	{
		return newState;
	}
	public void setNewState(String newState)
	{
		this.newState = newState;
	}
	public Integer getShiftId()
	{
		return shiftId;
	}
	public void setShiftId(Integer shiftId)
	{
		this.shiftId = shiftId;
	}
	public Date getReportDate()
	{
		return reportDate;
	}
	public void setReportDate(Date reportDate)
	{
		this.reportDate = reportDate;
	}
	public Timestamp getLastUpdateTime()
	{
		return lastUpdateTime;
	}
	public void setLastUpdateTime(Timestamp lastUpdateTime)
	{
		this.lastUpdateTime = lastUpdateTime;
	}
}
