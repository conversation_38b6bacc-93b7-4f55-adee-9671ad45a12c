package com.tzx.clientorder.mtwechat.bo;

import com.tzx.pos.asyn.thread.OrderingPrintThread;
import net.sf.json.JSONObject;

/**
 * 下单（提供美团接口）
 * Created by qingui on 2018-05-30.
 */
public interface InsertOrderService {
    String	NAME	= "com.tzx.clientorder.mtwechat.bo.imp.InsertOrderServiceImpl";

    /**
     * 下单
     * @param tenancyId
     * @param storeId
     * @param jsonObject
     * @return
     */
    JSONObject insertOrderRequiresNew(String tenancyId, Integer storeId, JSONObject jsonObject) throws Exception;

    /**
     * 下单的打印
     * @param orderingPrintThread
     */
    void orderingPrint(OrderingPrintThread orderingPrintThread);
}
