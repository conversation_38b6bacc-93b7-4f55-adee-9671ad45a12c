/**
  * @Description: TODO
  * @author: z<PERSON><PERSON><PERSON>
  * @version: 1.0
  * @Date: 2018年4月3日
*/
package com.tzx.base.service.servlet;

import java.io.File;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;

import org.apache.log4j.Logger;

import com.tzx.base.bo.DataSyncService;
import com.tzx.base.common.util.PosPropertyUtil;
import com.tzx.base.listener.PosSystemInitListener;
import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.util.HttpUtil;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.Tools;

import net.lingala.zip4j.core.ZipFile;

/**
  * @Description: 基础资料下发线程
  * @param:
  * @author: z<PERSON>quan
  * @version: 1.0
  * @Date: 2018年4月3日
*/
public class DownData{

	private Logger logger = Logger.getLogger(DownData.class);
	public static List<String> outFilePathList = new ArrayList<String>(); 
	private Logger				tipLogger	= Logger.getLogger("tip");
	
	private String requestUrl;
	private String downTableCode;
	private String tenancyId;
	private int storeId;
	private String tables;                               // 下发的数据表集合
	private HashMap<String,String> recordCountMap;       // 记录表名和对应的数据行数
	
	public DownData(String requestUrl,String downTableCode,String tables,String tenancyId,int storeId,HashMap<String,String> recordCountMap) {
		this.requestUrl = requestUrl;
		this.downTableCode = downTableCode;
		this.tables = tables;
		this.tenancyId = tenancyId;
		this.storeId = storeId;
		this.recordCountMap = recordCountMap;
	}
	
	public boolean downloadData() {
		boolean bo = false;
		logger.info("异步处理下发");
		try {
			String  saasUrl = PosPropertyUtil.getMsg("saas.url");
			HttpUtil.sendGetRequest(saasUrl+"/rest/hq/boh/dataSyncResultRest/?success=true&status=2&tenancyId="+tenancyId+"&storeId="+storeId+"&downTableCode="+downTableCode,"UTF-8");// 2 门店开始下载数据
			tipLogger.info("基础资料下发：开始下载数据。");
//			String baseFilePath= DownData.class.getResource("/").getPath()+ "file"+ File.separator + downTableCode;
//		    File file = new File(DownData.class.getResource("/").getPath()+ "file");
			String contextPath = "";
            try {
//            	contextPath = URLDecoder.decode(System.getProperty("contextPath"),"utf-8");
            	contextPath = URLDecoder.decode(Constant.CONTEXT_PATH,"utf-8");
			} catch (UnsupportedEncodingException e) {
				logger.error(e.getMessage());
			}
			String baseFilePath = contextPath + "file"+ File.separator + downTableCode;
			File file = new File(contextPath + "file");
			boolean tmpBo = false;
			if(!file.exists()){
				tmpBo = file.mkdir();
				if(!tmpBo){
            		logger.info("文件创建失败，文件路径："+Constant.CONTEXT_PATH + "file");
            	}
            }
		    file = new File(baseFilePath);
            if(!file.exists()){
            	tmpBo = file.mkdir();
            	if(!tmpBo){
            		logger.info("文件创建失败，文件路径："+Constant.CONTEXT_PATH + "file");
            	}
            }
            String outFilePath = baseFilePath + File.separator + "base.zip";
			//String outFilePath = Thread.currentThread().getContextClassLoader().getResource("file").getPath()+ "/" + "base.zip";
			outFilePath = URLDecoder.decode(outFilePath, "UTF-8");
			HttpUtil.getHttpInputStream(requestUrl, outFilePath);
			file = new File(outFilePath);	
			if(!file.exists()){
				// 重试下载数据
				logger.info("第一次下载数据失败，正在重试下载数据。");
				HttpUtil.getHttpInputStream(requestUrl, outFilePath);
				file = new File(outFilePath);
			}
			ZipFile zipFile = new ZipFile(file);				
			List<?> fileList = zipFile.getFileHeaders();
			logger.info("下发共有----"+(fileList.size()-1)+"----个文件");		
			if((fileList.size()-1)>0){
				HttpUtil.sendGetRequest(saasUrl+"/rest/hq/boh/dataSyncResultRest/?success=true&status=3&tenancyId="+tenancyId+"&storeId="+storeId+"&downTableCode="+downTableCode,"UTF-8");// 3 门店下载数据成功
				tipLogger.info("基础资料下发：下载数据成功。");
			}else{
				HttpUtil.sendGetRequest(saasUrl+"/rest/hq/boh/dataSyncResultRest/?success=false&status=3&tenancyId="+tenancyId+"&storeId="+storeId+"&downTableCode="+downTableCode,"UTF-8");// 3 门店下载数据失败
				tipLogger.info("基础资料下发：下载数据失败。");
			}
			HttpUtil.sendGetRequest(saasUrl+"/rest/hq/boh/dataSyncResultRest/?success=true&status=4&tenancyId="+tenancyId+"&storeId="+storeId+"&downTableCode="+downTableCode,"UTF-8"); // 4 门店开始处理数据
			tipLogger.info("基础资料下发：开始处理数据。");
			DataSyncService dataSyncService = (DataSyncService) SpringConext.getBean(DataSyncService.NAME);
			boolean isOrNot = dataSyncService.BaseDataSyncFromSaas(zipFile,baseFilePath,downTableCode,tables,recordCountMap);
			if(isOrNot){
				HttpUtil.sendGetRequest(saasUrl+"/rest/hq/boh/dataSyncResultRest/?success=true&status=5&tenancyId="+tenancyId+"&storeId="+storeId+"&downTableCode="+downTableCode,"UTF-8"); // 5 门店处理数据成功
				logger.info("下发数据处理成功。");
				tipLogger.info("基础资料下发：处理数据成功。");
			}else{
				HttpUtil.sendGetRequest(saasUrl+"/rest/hq/boh/dataSyncResultRest/?success=false&status=5&tenancyId="+tenancyId+"&storeId="+storeId+"&downTableCode="+downTableCode,"UTF-8"); // 5 门店处理数据失败
				logger.info("下发数据失败！");
				tipLogger.info("基础资料下发：处理数据失败。");
			}			
			bo = isOrNot;
			// 删除下发文件
			outFilePathList.add(outFilePath);
			file = new File(outFilePath);
			if(file.exists()){
				file.delete();
			}
			file = new File(baseFilePath);
			if(file.exists() && file.isDirectory()){
				Tools.deleteDir(file);
			}
			/*
			 * 压缩文件outFilePathList由于被其他程序占用，可能会出现无法删除情况。
			 * 把outFilePathList加入到全局数组中，每次下发后判断是否有未删除的压缩文件，如果有就删除。
			 */
			for(int i =0;i<outFilePathList.size();i++){
				file = new File(outFilePathList.get(i));
				if(file.exists()){
					file.delete();
				}
			}
			// 清除基础资料下发产生的历史文件
			String delFile= DownData.class.getResource("/").getPath()+ "file";
			file = new File(delFile);
			if(file.exists() && file.isDirectory()){
				Tools.deleteDir(file);
			}		
		} catch (Exception e) {
			e.printStackTrace();
			return false;
		}
		return bo;
	}

}