package com.tzx.clientorder.wlifeprogram.common.util;

import com.tzx.framework.common.exception.ErrorCode;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.exception.OtherSystemException;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.PropertiesLoader;
import com.tzx.pos.base.Constant;
import net.sf.json.JSONObject;
import org.apache.log4j.Logger;

import java.util.Map;

/**
 * 异常输出统一调用
 */
public class ExceptionPrintUtil {

    /**
     * 自定义其它异常
     * @param logger
     * @param se
     * @param responseJson
     * @param message
     */
    public static void buildOtherSysExceptionData(Logger logger, OtherSystemException se, JSONObject responseJson, String message)
    {
        responseJson.put("code", se.getCode());
        responseJson.put("msg", se.getMsg());
        logger.error(message + ",原因：" + se.getMsg() + ",错误码：" + se.getCode());
        logger.error(ExceptionMessage.getExceptionMessage(se));
    }

    /**
     * 自定义异常
     * @param logger
     * @param se
     * @param responseJson
     * @param message
     */
    public static void buildSysExceptionData(Logger logger, SystemException se, JSONObject responseJson, String message)
    {
        ErrorCode error = se.getErrorCode();
        String msg = se.getErrorMsg();
//        String msg = PropertiesLoader.getProperty(String.valueOf(error.getNumber()));
//        Map<String, Object> map = se.getProperties();
//        for (String key : map.keySet())
//        {
//            msg = msg.replace(key, String.valueOf(map.get(key)));
//        }
        responseJson.put("code", error.getNumber());
        responseJson.put("msg", msg);
        logger.error(message + ",原因：" + msg + ",错误码：" + error.getNumber());
        logger.error(ExceptionMessage.getExceptionMessage(se));
    }

    /**
     * 系统异常
     * @param logger
     * @param e
     * @param responseJson
     * @param message
     */
    public static void buildExceptionData(Logger logger, Exception e, JSONObject responseJson, String message)
    {
        responseJson.put("code", Constant.CODE_INNER_EXCEPTION);
        responseJson.put("msg", message);
        logger.error("系统内部错误：" + ExceptionMessage.getExceptionMessage(e));
    }
    
    public static void buildWlifeExceptionData(Logger logger, Exception e, JSONObject responseJson, String message)
    {
        responseJson.put("success", 0);
        responseJson.put("msg", message);
        logger.error("系统内部错误：" + ExceptionMessage.getExceptionMessage(e));
    }
    
    public static void buildWlifeSysExceptionData(Logger logger, SystemException se, JSONObject responseJson, String message)
    {
        ErrorCode error = se.getErrorCode();
        String msg = se.getErrorMsg();
//        String msg = PropertiesLoader.getProperty(String.valueOf(error.getNumber()));
//        Map<String, Object> map = se.getProperties();
//        for (String key : map.keySet())
//        {
//            msg = msg.replace(key, String.valueOf(map.get(key)));
//        }
        
//        responseJson.put("success", error.getNumber());
        responseJson.put("success", 0);
        responseJson.put("msg", msg);
        logger.error(message + ",原因：" + msg + ",错误码：" + error.getNumber());
        logger.error(ExceptionMessage.getExceptionMessage(se));
    }

    public static void buildWlifeOtherSysExceptionData(Logger logger, OtherSystemException se, JSONObject responseJson, String message)
    {
//        responseJson.put("success", se.getCode());
        responseJson.put("success", 0);
        responseJson.put("msg", se.getMsg());
        logger.error(message + ",原因：" + se.getMsg() + ",错误码：" + se.getCode());
        logger.error(ExceptionMessage.getExceptionMessage(se));
    }

}
