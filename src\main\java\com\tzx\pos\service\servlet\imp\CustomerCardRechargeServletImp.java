package com.tzx.pos.service.servlet.imp;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Service;

import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.Tools;
import com.tzx.member.common.entity.PosCustomerOperateListEntity;
import com.tzx.member.crm.bo.CustomerCardRechargeService;
import com.tzx.member.crm.po.springjdbc.dao.CustomerDao;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.service.servlet.CustomerCardRechargeServlet;

@Service(CustomerCardRechargeServlet.NAME)
public class CustomerCardRechargeServletImp implements CustomerCardRechargeServlet
{

	@Resource(name = CustomerCardRechargeService.NAME)
	protected CustomerCardRechargeService	cardRechargeService;

	@Resource(name = CustomerDao.NAME)
	protected CustomerDao				customerDao;
	
	@Override
	public Data precreatePaymentForRecharge(Data paramData, String path) throws Exception
	{
		// TODO Auto-generated method stub
		String tenantId = paramData.getTenancy_id();
		Integer storeId = paramData.getStore_id();
		List<?> paramList = paramData.getData();
		
		if (null == paramList || paramList.isEmpty())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}
		JSONObject paramJson = JSONObject.fromObject(paramList.get(0));
		
		Data resultData =  paramData.clone();
		// 校验是否签到
		if (cardRechargeService.commonPostCheck(tenantId, storeId,paramData.getSource(), paramJson))
		{
			// 插入会员操作流水表
			String billCode = cardRechargeService.addCustomerOperateListForRecharge(tenantId, storeId, SysDictionary.OPERAT_TYPE_CZ, paramJson);

			// 获取二维码
			resultData = cardRechargeService.precreatePaymentForRecharge(tenantId, storeId, billCode, paramJson, path);
		}
		return resultData;
	}

	@Override
	public Data barcodePaymentForRecharge(Data paramData) throws Exception
	{
		// TODO Auto-generated method stub
		String tenantId = paramData.getTenancy_id();
		Integer storeId = paramData.getStore_id();
		List<?> paramList = paramData.getData();

		if (null == paramList || paramList.isEmpty())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}
		JSONObject paramJson = JSONObject.fromObject(paramList.get(0));

		Data resultData = paramData.clone();
		// 校验是否签到
		if (cardRechargeService.commonPostCheck(tenantId, storeId,paramData.getSource(), paramJson))
		{
			// 插入会员操作流水表
			String billCode = cardRechargeService.addCustomerOperateListForRecharge(tenantId, storeId, SysDictionary.OPERAT_TYPE_CZ, paramJson);

			// 扫码支付
			resultData = cardRechargeService.barcodePaymentForRecharge(tenantId, storeId, billCode, paramJson);

			if (Constant.CODE_SUCCESS == resultData.getCode())
			{
				// 支付成功
				JSONObject resultJson = JSONObject.fromObject(resultData.getData().get(0));

				if (SysDictionary.THIRD_PAY_STATUS_SUCCESS.equals(resultJson.optString("payment_state")))
				{
					// 请求会员充值
					resultData = cardRechargeService.customerCardRecharge(tenantId, storeId, billCode, paramJson);

					if (Constant.CODE_SUCCESS == resultData.getCode())
					{
						// 打印小票
						JSONObject rechargeResultJson = JSONObject.fromObject(resultData.getData().get(0));
						rechargeResultJson.put("is_invoice", ParamUtil.getStringValueByObject(paramJson, "is_invoice"));
						rechargeResultJson.put("report_date", ParamUtil.getStringValueByObject(paramJson, "report_date"));
						rechargeResultJson.put("pos_num", ParamUtil.getStringValueByObject(paramJson, "pos_num"));
						cardRechargeService.printForCustomerCardRecharge(tenantId, storeId,rechargeResultJson);
					}
				}
			}
		}

		return resultData;
	}

	@Override
	public Data customerCardRecharge(Data paramData) throws Exception
	{
		// TODO Auto-generated method stub
		String tenantId = paramData.getTenancy_id();
		Integer storeId = paramData.getStore_id();
		List<?> paramList = paramData.getData();

		if (null == paramList || paramList.isEmpty())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}
		JSONObject paramJson = JSONObject.fromObject(paramList.get(0));
		// 机台号
        String posNum = paramJson.optString("pos_num");

		Data resultData = paramData.clone();
		// 校验是否签到
		if (cardRechargeService.commonPostCheck(tenantId, storeId,paramData.getSource(), paramJson))
		{
			// 插入会员操作流水表
			String billCode = cardRechargeService.addCustomerOperateListForRecharge(tenantId, storeId, SysDictionary.OPERAT_TYPE_CZ, paramJson);

			// 请求会员充值
			resultData = cardRechargeService.customerCardRecharge(tenantId, storeId, billCode, paramJson);

			if (Constant.CODE_SUCCESS == resultData.getCode())
			{
				JSONObject resultJson = JSONObject.fromObject(resultData.getData().get(0));
				resultJson.put("is_invoice", ParamUtil.getStringValueByObject(paramJson, "is_invoice"));
				resultJson.put("report_date", ParamUtil.getStringValueByObject(paramJson, "report_date"));
                resultJson.put("pos_num", posNum);
				cardRechargeService.printForCustomerCardRecharge(tenantId, storeId,resultJson);
			}
		}
		return resultData;
	}

	@Override
	public Data queryCustomerCardRechargeForState(Data paramData) throws Exception
	{
		// TODO Auto-generated method stub
		String tenantId = paramData.getTenancy_id();
		Integer storeId = paramData.getStore_id();
		List<?> paramList = paramData.getData();

		if (null == paramList || paramList.isEmpty())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}
		JSONObject paramJson = JSONObject.fromObject(paramList.get(0));

		// 根据bill_code查询pos_customer_operate_list,返回充值状态
		String cardCode = ParamUtil.getStringValueByObject(paramJson, "card_code");
		String thirdCode = ParamUtil.getStringValueByObject(paramJson, "third_code");
		String thirdBillCode = ParamUtil.getStringValueByObject(paramJson, "bill_code");
		
		JSONObject queryJson = new JSONObject();
		queryJson.put("third_bill_code", thirdBillCode);
		if (Tools.hv(cardCode))
		{
			queryJson.put("card_code", cardCode);
		}
		if (Tools.hv(cardCode))
		{
			queryJson.put("third_code", thirdCode);
		}
		
		PosCustomerOperateListEntity customer = cardRechargeService.queryCustomerCardRecharge(tenantId, storeId, queryJson);

		JSONObject resultJson = new JSONObject();
		if (null != customer && SysDictionary.CUSTOMER_OPERATE_STATE_SUCCESS.equals(customer.getOperate_state()))
		{
			//充值成功,查询充值记录明细
			resultJson = cardRechargeService.queryCustomerCardRechargeForDetails(tenantId, storeId, customer);

			if(resultJson.isEmpty())
			{
				resultJson.put("card_code", customer.getCard_code());
				resultJson.put("bill_code", customer.getThird_bill_code());
				resultJson.put("third_bill_code", customer.getThird_bill_code());
				resultJson.put("payment_state", customer.getPayment_state());
				resultJson.put("operate_state", customer.getOperate_state());
			}
		}
		else if (null != customer)
		{
			resultJson.put("card_code", customer.getCard_code());
			resultJson.put("bill_code", customer.getThird_bill_code());
			resultJson.put("third_bill_code", customer.getThird_bill_code());
			resultJson.put("payment_state", customer.getPayment_state());
			resultJson.put("operate_state", customer.getOperate_state());
		}
		else
		{
			resultJson.put("card_code", cardCode);
			resultJson.put("bill_code", thirdBillCode);
			resultJson.put("third_bill_code", thirdBillCode);
			resultJson.put("payment_state", SysDictionary.THIRD_PAY_STATUS_FAIL);
			resultJson.put("operate_state", SysDictionary.CUSTOMER_OPERATE_STATE_FAIL);
		}

		List<JSONObject> resultList = new ArrayList<JSONObject>();
		resultList.add(resultJson);

		Data resultData = paramData.clone();
		resultData.setData(resultList);
		resultData.setCode(Constant.CODE_SUCCESS);
		return resultData;
	}

	@Override
	public Data cancelThirdPaymentForCustomerCardRecharge(Data paramData) throws Exception
	{
		// TODO Auto-generated method stub
		String tenantId = paramData.getTenancy_id();
		Integer storeId = paramData.getStore_id();
		List<?> paramList = paramData.getData();

		if (null == paramList || paramList.isEmpty())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}
		JSONObject paramJson = JSONObject.fromObject(paramList.get(0));
		
		String cardCode = ParamUtil.getStringValueByObject(paramJson, "card_code");
		String thirdCode = ParamUtil.getStringValueByObject(paramJson, "third_code");
		String thirdBillCode = ParamUtil.getStringValueByObject(paramJson, "bill_code");
		
		// 获取充值状态
		JSONObject queryJson = new JSONObject();
		queryJson.put("third_bill_code", thirdBillCode);
		if (Tools.hv(cardCode))
		{
			queryJson.put("card_code", cardCode);
		}
		if (Tools.hv(cardCode))
		{
			queryJson.put("third_code", thirdCode);
		}
		PosCustomerOperateListEntity customer = cardRechargeService.queryCustomerCardRecharge(tenantId, storeId, queryJson);

		Data resultData = paramData.clone();
		if (customer != null)
		{
			// 判断充值操作状态
//			switch (customer.getOperate_state())
//			{
//				case SysDictionary.CUSTOMER_OPERATE_STATE_SUCCESS: // 成功
//					// 充值成功,返回前端取消充值失败,
//					resultData.setCode(Constant.CODE_AUTH_FAILURE);
//					resultData.setMsg(Constant.GET_CRM_CZ_SUCCESS);
//					break;
//
//				case SysDictionary.CUSTOMER_OPERATE_STATE_FAIL: // 失败
//					// 充值失败,返回前端取消成功,
//					resultData.setCode(Constant.CODE_SUCCESS);
//					resultData.setMsg(Constant.CUSTOMER_CARD_RECHARGE_SUCCESS);
//					break;
//
//				case SysDictionary.CUSTOMER_OPERATE_STATE_WAIT: // 待处理
//					// 待处理,取消付款
//					resultData = cardRechargeService.cancelThirdPaymentForCardRecharge(tenantId, storeId, thirdBillCode, customer);
//					break;
//
//				default: // 处理中
//					// 处理中,先撤销充值,撤销充值成功后取消付款(? 撤销失败)
//					resultData = cardRechargeService.cancelCustomerCardRecharge(tenantId, storeId, customer);
//					// 撤销成功,再取消付款
//					if (Constant.CODE_SUCCESS == resultData.getCode())
//					{
//						resultData = cardRechargeService.cancelThirdPaymentForCardRecharge(tenantId, storeId, thirdBillCode, customer);
//					}
//					break;
//			}
			
			if (SysDictionary.CUSTOMER_OPERATE_STATE_SUCCESS.equals(customer.getOperate_state()))
			{
				// 充值成功,返回前端取消充值失败,
				resultData.setCode(PosErrorCode.CARD_RECHARGE_COMPLETE_NOT_PERMIT_CANCEL_ERROR.getNumber());
				resultData.setMsg(PosErrorCode.CARD_RECHARGE_COMPLETE_NOT_PERMIT_CANCEL_ERROR.getMessage());
			}
			else if (SysDictionary.CUSTOMER_OPERATE_STATE_FAIL.equals(customer.getOperate_state()))
			{
				// 充值失败,返回前端取消成功,
				resultData.setCode(Constant.CODE_SUCCESS);
				resultData.setMsg(Constant.CUSTOMER_CARD_RECHARGE_SUCCESS);
			}
			else
			{
				// 处理中,先撤销充值,撤销充值成功后取消付款(? 撤销失败)
				resultData = cardRechargeService.customerCardRechargeManage(tenantId, storeId, customer.getThird_bill_code(), null, customer,Oper.cancle);


				// 撤销成功,再取消付款
				if (Constant.CODE_SUCCESS == resultData.getCode())
				{
					resultData = cardRechargeService.cancelThirdPaymentForCardRecharge(tenantId, storeId, thirdBillCode, customer);
				}
			}
		}
		else
		{
			resultData.setCode(Constant.CODE_NULL_DATASET);
			resultData.setMsg(Constant.CUSTOMER_CARD_RECHARGE_FAILURE);
		}
		return resultData;
	}

	@Override
	public Data cancelCustomerCardRecharge(Data paramData) throws Exception
	{
		// TODO Auto-generated method stub
		String tenantId = paramData.getTenancy_id();
		Integer storeId = paramData.getStore_id();
		List<?> paramList = paramData.getData();
		
		if (null == paramList || paramList.isEmpty())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}
		JSONObject paramJson = JSONObject.fromObject(paramList.get(0));

		// 机台号
        String posNum = paramJson.optString("pos_num");

		Data resultData =  paramData.clone();
		String billCodeOriginal = ParamUtil.getStringValueByObject(paramJson, "old_bill_code");
		// 插入会员操作记录表
		paramJson.put("bill_code_original", billCodeOriginal);
		String thirdBillCode = cardRechargeService.addCustomerOperateListForRecharge(tenantId, storeId, SysDictionary.OPERAT_TYPE_FCZ, paramJson);
		
		// 撤销成功,
        resultData = cardRechargeService.revokeCustomerCardRecharge(tenantId, storeId, thirdBillCode,paramJson);
		// 再取消付款
		if (Constant.CODE_SUCCESS == resultData.getCode())
		{
			JSONObject resultJson = JSONObject.fromObject(resultData.getData().get(0));
			
			JSONObject queryJson = new JSONObject();
			queryJson.put("third_bill_code", thirdBillCode);
			queryJson.put("operat_type", SysDictionary.OPERAT_TYPE_FCZ);
			PosCustomerOperateListEntity customerOperate = cardRechargeService.queryCustomerCardRecharge(tenantId, storeId, queryJson);

			if (SysDictionary.PAYMENT_CLASS_WECHAT_PAY.equals(customerOperate.getPayment_class()) || SysDictionary.PAYMENT_DDM_QUICK_PAY.equals(customerOperate.getPayment_class()) || SysDictionary.PAYMENT_CLASS_ALI_PAY.equals(customerOperate.getPayment_class()))
			{
				JSONObject tradingJson = customerDao.getCrmCardTradingListByBillCode(tenantId, storeId, billCodeOriginal);
				if(null !=tradingJson)
				{
					thirdBillCode = tradingJson.optString("third_bill_code");
					cardRechargeService.cancelThirdPaymentForCardRecharge(tenantId, storeId, thirdBillCode,customerOperate);
				}
				
			}

			if (Constant.CODE_SUCCESS == resultData.getCode())
			{
				// 取消电子发票
				JSONObject invoiceJson = new JSONObject();
				invoiceJson.put("bill_code_original", thirdBillCode);
				cardRechargeService.cancelElectricInvoice(tenantId, storeId, invoiceJson);
				// 打印小票
                resultJson.put("pos_num", posNum);
				cardRechargeService.printForCustomerCardRecharge(tenantId, storeId, SysDictionary.PRINT_CODE_1009, resultJson);
			}
		}
        return resultData;
	}
}
