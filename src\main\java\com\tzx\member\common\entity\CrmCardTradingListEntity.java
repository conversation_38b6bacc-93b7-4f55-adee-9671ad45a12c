package com.tzx.member.common.entity;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

public class CrmCardTradingListEntity implements Serializable
{
	private static final long	serialVersionUID	= 5387339117285508622L;

	private String				tenancy_id;
	private Integer				store_id;
	private Integer				id;
	private Date				business_date;
	private Integer				shift_id;
	private String				chanel;
	private String				operat_type;
	private Integer				card_id;
	private String				card_code;
	private Integer				card_class_id;
	private String				name;
	private String				mobil;
	private String				bill_code;
	private String				bill_code_original;
	private String				third_bill_code;
	private String				batch_num;
	private Double				bill_money;
	private Double				main_trading;
	private Double				reward_trading;
	private Double				revoked_trading;
	private Double				main_original;
	private Double				reward_original;
	private Double				total_balance;
	private Double				reward_balance;
	private Double				main_balance;
	private Double				deposit;
	private Integer				activity_id;
	private Integer				customer_id;
	private String				pay_type;
	private String				salesman;
	private Double				commission_saler_money;
	private Double				commission_store_money;
	private Double				invoice_balance;
	private String				is_invoice;
	private Timestamp			last_updatetime;
	private Timestamp			store_updatetime;
	private Integer				operator_id;
	private String				operator;
	private Timestamp			operate_time;
	private String				payment_state;
	private String				recharge_state;
	private String				request_status;
	private String				request_code;
	private String				request_msg;
	private String              posNum;
	
	public String getTenancy_id()
	{
		return tenancy_id;
	}
	public void setTenancy_id(String tenancy_id)
	{
		this.tenancy_id = tenancy_id;
	}
	public Integer getStore_id()
	{
		return store_id;
	}
	public void setStore_id(Integer store_id)
	{
		this.store_id = store_id;
	}
	public Integer getId()
	{
		return id;
	}
	public void setId(Integer id)
	{
		this.id = id;
	}
	public Date getBusiness_date()
	{
		return business_date;
	}
	public void setBusiness_date(Date business_Date)
	{
		this.business_date = business_Date;
	}
	public Integer getShift_id()
	{
		return shift_id;
	}
	public void setShift_id(Integer shift_id)
	{
		this.shift_id = shift_id;
	}
	public String getChanel()
	{
		return chanel;
	}
	public void setChanel(String chanel)
	{
		this.chanel = chanel;
	}
	public String getOperat_type()
	{
		return operat_type;
	}
	public void setOperat_type(String operat_type)
	{
		this.operat_type = operat_type;
	}
	public Integer getCard_id()
	{
		return card_id;
	}
	public void setCard_id(Integer card_id)
	{
		this.card_id = card_id;
	}
	public String getCard_code()
	{
		return card_code;
	}
	public void setCard_code(String card_code)
	{
		this.card_code = card_code;
	}
	public Integer getCard_class_id()
	{
		return card_class_id;
	}
	public void setCard_class_id(Integer card_class_id)
	{
		this.card_class_id = card_class_id;
	}
	public String getName()
	{
		return name;
	}
	public void setName(String name)
	{
		this.name = name;
	}
	public String getMobil()
	{
		return mobil;
	}
	public void setMobil(String mobil)
	{
		this.mobil = mobil;
	}
	public String getBill_code()
	{
		return bill_code;
	}
	public void setBill_code(String bill_code)
	{
		this.bill_code = bill_code;
	}
	public String getBill_code_original()
	{
		return bill_code_original;
	}
	public void setBill_code_original(String bill_code_original)
	{
		this.bill_code_original = bill_code_original;
	}
	public String getThird_bill_code()
	{
		return third_bill_code;
	}
	public void setThird_bill_code(String third_bill_code)
	{
		this.third_bill_code = third_bill_code;
	}
	public String getBatch_num()
	{
		return batch_num;
	}
	public void setBatch_num(String batch_num)
	{
		this.batch_num = batch_num;
	}
	public Double getBill_money()
	{
		return bill_money;
	}
	public void setBill_money(Double bill_money)
	{
		this.bill_money = bill_money;
	}
	public Double getMain_trading()
	{
		return main_trading;
	}
	public void setMain_trading(Double main_trading)
	{
		this.main_trading = main_trading;
	}
	public Double getReward_trading()
	{
		return reward_trading;
	}
	public void setReward_trading(Double reward_trading)
	{
		this.reward_trading = reward_trading;
	}
	public Double getRevoked_trading()
	{
		return revoked_trading;
	}
	public void setRevoked_trading(Double revoked_trading)
	{
		this.revoked_trading = revoked_trading;
	}
	public Double getMain_original()
	{
		return main_original;
	}
	public void setMain_original(Double main_original)
	{
		this.main_original = main_original;
	}
	public Double getReward_original()
	{
		return reward_original;
	}
	public void setReward_original(Double reward_original)
	{
		this.reward_original = reward_original;
	}
	public Double getTotal_balance()
	{
		return total_balance;
	}
	public void setTotal_balance(Double total_balance)
	{
		this.total_balance = total_balance;
	}
	public Double getReward_balance()
	{
		return reward_balance;
	}
	public void setReward_balance(Double reward_balance)
	{
		this.reward_balance = reward_balance;
	}
	public Double getMain_balance()
	{
		return main_balance;
	}
	public void setMain_balance(Double main_balance)
	{
		this.main_balance = main_balance;
	}
	public Double getDeposit()
	{
		return deposit;
	}
	public void setDeposit(Double deposit)
	{
		this.deposit = deposit;
	}
	public Integer getActivity_id()
	{
		return activity_id;
	}
	public void setActivity_id(Integer activity_id)
	{
		this.activity_id = activity_id;
	}
	public Integer getCustomer_id()
	{
		return customer_id;
	}
	public void setCustomer_id(Integer customer_id)
	{
		this.customer_id = customer_id;
	}
	public String getPay_type()
	{
		return pay_type;
	}
	public void setPay_type(String pay_type)
	{
		this.pay_type = pay_type;
	}
	public String getSalesman()
	{
		return salesman;
	}
	public void setSalesman(String salesman)
	{
		this.salesman = salesman;
	}
	public Double getCommission_saler_money()
	{
		return commission_saler_money;
	}
	public void setCommission_saler_money(Double commission_saler_money)
	{
		this.commission_saler_money = commission_saler_money;
	}
	public Double getCommission_store_money()
	{
		return commission_store_money;
	}
	public void setCommission_store_money(Double commission_store_money)
	{
		this.commission_store_money = commission_store_money;
	}
	public Double getInvoice_balance()
	{
		return invoice_balance;
	}
	public void setInvoice_balance(Double invoice_balance)
	{
		this.invoice_balance = invoice_balance;
	}
	public String getIs_invoice()
	{
		return is_invoice;
	}
	public void setIs_invoice(String is_invoice)
	{
		this.is_invoice = is_invoice;
	}
	public Timestamp getLast_updatetime()
	{
		return last_updatetime;
	}
	public void setLast_updatetime(Timestamp last_updatetime)
	{
		this.last_updatetime = last_updatetime;
	}
	public Timestamp getStore_updatetime()
	{
		return store_updatetime;
	}
	public void setStore_updatetime(Timestamp store_updatetime)
	{
		this.store_updatetime = store_updatetime;
	}
	public Integer getOperator_id()
	{
		return operator_id;
	}
	public void setOperator_id(Integer operator_id)
	{
		this.operator_id = operator_id;
	}
	public String getOperator()
	{
		return operator;
	}
	public void setOperator(String operator)
	{
		this.operator = operator;
	}
	public Timestamp getOperate_time()
	{
		return operate_time;
	}
	public void setOperate_time(Timestamp operate_time)
	{
		this.operate_time = operate_time;
	}
	public String getPayment_state()
	{
		return payment_state;
	}
	public void setPayment_state(String payment_state)
	{
		this.payment_state = payment_state;
	}
	public String getRecharge_state()
	{
		return recharge_state;
	}
	public void setRecharge_state(String recharge_state)
	{
		this.recharge_state = recharge_state;
	}
	public String getRequest_status()
	{
		return request_status;
	}
	public void setRequest_status(String request_status)
	{
		this.request_status = request_status;
	}
	public String getRequest_code()
	{
		return request_code;
	}
	public void setRequest_code(String request_code)
	{
		this.request_code = request_code;
	}
	public String getRequest_msg()
	{
		return request_msg;
	}
	public void setRequest_msg(String request_msg)
	{
		this.request_msg = request_msg;
	}

	public String getPosNum() {
		return posNum;
	}

	public void setPosNum(String posNum) {
		this.posNum = posNum;
	}
}
