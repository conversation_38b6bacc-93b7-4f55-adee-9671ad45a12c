package com.tzx.framework.common.baidupush;

import java.util.List;
import org.apache.log4j.Logger;
import net.sf.json.JSONObject;
import com.baidu.yun.core.log.YunLogEvent;
import com.baidu.yun.core.log.Yun<PERSON><PERSON>Handler;
import com.baidu.yun.push.auth.PushKeyPair;
import com.baidu.yun.push.client.BaiduPushClient;
import com.baidu.yun.push.constants.BaiduPushConstants;
import com.baidu.yun.push.exception.PushClientException;
import com.baidu.yun.push.exception.PushServerException;
import com.baidu.yun.push.model.PushMsgToSingleDeviceRequest;
import com.baidu.yun.push.model.PushMsgToSingleDeviceResponse;
import com.tzx.framework.common.util.dao.GenericDao;

public class AndroidPushMsgToSingleDevice {
	
	private static final Logger				logger	= Logger.getLogger(AndroidPushMsgToSingleDevice.class);
	
	public static JSONObject getSendJSONObject(Object id,Object queue_id,String state,String lineup_time,String queue_name,String lineup_code,String phone,String waiting_time,String nick_name,String head_url,int type,int deinner_number)
	{
		JSONObject jo = new JSONObject();
		jo.put("id", id);
		jo.put("queue_id", queue_id);
		jo.put("state", state);
		jo.put("lineup_time", lineup_time);
		jo.put("queue_name", queue_name);
		jo.put("lineup_code", lineup_code);
		jo.put("phone", phone);
		jo.put("waiting_time", waiting_time);
		jo.put("nick_name", nick_name);
		jo.put("head_url", head_url);
		jo.put("type", type);
		if(deinner_number<1)
		{
			deinner_number = 1; 
		}
		
		jo.put("deinner_number", deinner_number);
		return jo;
	}
	
	public static void pushMsg(String tenancy_id,GenericDao dao,Integer store_id,Integer type,Object id,Object queue_id,String state,String lineup_time,String queue_name,String lineup_code,String phone,String waiting_time,String nick_name,String head_url,Integer deinner_number) throws Exception
	{
		JSONObject sendJo = getSendJSONObject(id, queue_id, state, lineup_time, queue_name, lineup_code, phone, waiting_time, nick_name, head_url, type,deinner_number);
		push(tenancy_id, type, dao, sendJo, store_id);
	}
	
	public static JSONObject getApiKeyAndSecretKey(String tenancy_id,GenericDao dao) throws Exception
	{
		JSONObject jo = null;
		
		List<JSONObject> listkey = dao.query4Json(tenancy_id, "SELECT para_value as api,(select para_value from sys_parameter where para_code = 'bdtssercretkey' and store_id=0 and valid_state='1' LIMIT 1 ) as sercretkey  from sys_parameter where para_code = 'bdtsapi' and store_id=0 and valid_state='1' LIMIT 1 ");
		if(listkey.size()>0)
		{
			JSONObject jo2 = listkey.get(0);
			String key = jo2.optString("api");
			String skey = jo2.optString("sercretkey");
			if(key.length()>5 && skey.length()>5)
			{
				jo = jo2;
			}
		}
		
		return jo;
	}    
	
	public static String getChanelId(String tenancy_id,GenericDao dao,int store_id,int type) throws Exception
	{
		String result = null;
		List<JSONObject> chanelId = dao.query4Json(tenancy_id, "select chanel_id from baidu_push_chanel where store_id="+store_id+" and type_id="+type);
		if(chanelId.size()>0)
		{
			JSONObject joc = chanelId.get(0);
			String chanel_id = joc.optString("chanel_id");
			if(chanel_id.length()>4)
			{
				result = chanel_id;
			}
		}
		return result;
	} 
	
	public static void push(String tenancy_id,int type,GenericDao dao,JSONObject jb,int store_id) throws Exception
	{
		JSONObject keyjo = getApiKeyAndSecretKey(tenancy_id, dao);
		if(keyjo!=null)
		{
			String chanel_id = getChanelId(tenancy_id, dao, store_id, type);
			if(chanel_id!=null)
			{
				String api_key = keyjo.optString("api");
				String secret_key = keyjo.optString("sercretkey");
				sendMsg(api_key, secret_key, chanel_id, jb);	
			}
		}
	}
	
	public static void sendMsg(String apiKey,String secretKey,String addChannelId,JSONObject jb)
	{
		
		PushKeyPair pair = new PushKeyPair(apiKey, secretKey);

		// 2. build a BaidupushClient object to access released interfaces
		BaiduPushClient pushClient = new BaiduPushClient(pair,
				BaiduPushConstants.CHANNEL_REST_URL);

		// 3. register a YunLogHandler to get detail interacting information
		// in this request.
		pushClient.setChannelLogHandler(new YunLogHandler() {
			@Override
			public void onHandle(YunLogEvent event) {
				//完成回调
				//System.out.println(event.getMessage());
			}
		});

		try {
			// 4. specify request arguments
			//创建 Android的通知
			JSONObject notification = new JSONObject();
			notification.put("title", "Tzx_PaiHao");
			notification.put("description","push_msg");
			notification.put("notification_builder_id", 0);
			notification.put("notification_basic_style", 4);
			notification.put("open_type", 1);
			notification.put("url", "http://push.baidu.com");
//			JSONObject jsonCustormCont = new JSONObject();
//			jsonCustormCont.put("key", "value"); //自定义内容，key-value
			notification.put("custom_content", jb);
			logger.info("addChannelId:"+addChannelId);
			PushMsgToSingleDeviceRequest request = new PushMsgToSingleDeviceRequest()
					.addChannelId(addChannelId)
					.addMsgExpires(new Integer(3600)). // message有效时间
					addMessageType(0).  // + //1：通知,0:透传消息. 默认为0 注：IOS只有通知.
					addMessage(notification.toString()).
					addDeviceType(3);   // deviceType => 3:android, 4:ios
			// 5. http request
			PushMsgToSingleDeviceResponse response = pushClient
					.pushMsgToSingleDevice(request);
			// Http请求结果解析打印
			System.out.println("msgId: " + response.getMsgId() + ",sendTime: "
					+ response.getSendTime());
		} 
		catch (PushClientException e) 
		{
			e.printStackTrace();
			logger.info("baidupsuh-PushClientException:"+e.getMessage());
		} 
		catch (PushServerException e) 
		{	e.printStackTrace();
			logger.info(e.getMessage());
			logger.info(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>");
			logger.info(e.getErrorCode());
			logger.info(e.getRequestId());
			logger.info(e.getErrorMsg());
			logger.info("<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<");
			logger.info("baidupsuh-PushServerException:"+e.getMessage());
		}
		catch (Exception e) 
		{
			e.printStackTrace();
			logger.info("baidupsuh-Exception:"+e.getMessage());
		}
		
	}
	
	public static void pushOc(String tenancy_id,int type,GenericDao dao,JSONObject jb,int store_id) throws Exception
	{
		JSONObject keyjo = getApiKeyAndSecretKey(tenancy_id, dao);
		if(keyjo!=null)
		{
			List<JSONObject> chanelId_list = dao.query4Json(tenancy_id, "select chanel_id from baidu_push_chanel where store_id="+store_id+" and type_id="+type);
			if(chanelId_list.size()>0){
				for(JSONObject obj:chanelId_list){
					String api_key = keyjo.optString("api");
					String secret_key = keyjo.optString("sercretkey");
					sendMsg(api_key, secret_key, obj.optString("chanel_id"), jb);	
				}
				
			}
		}
	}
	public static void main(String[] args) throws PushClientException,
			PushServerException {
		// 1. get apiKey and secretKey from developer console
		String apiKey = "AykFKG4wAQtttsjsfdLQ0Lr3";
		String secretKey = "VRra43RK7jI7c6X2aGzvUdCO52k7lYjE";
		PushKeyPair pair = new PushKeyPair(apiKey, secretKey);

		// 2. build a BaidupushClient object to access released interfaces
		BaiduPushClient pushClient = new BaiduPushClient(pair,
				BaiduPushConstants.CHANNEL_REST_URL);

		// 3. register a YunLogHandler to get detail interacting information
		// in this request.
		pushClient.setChannelLogHandler(new YunLogHandler() {
			@Override
			public void onHandle(YunLogEvent event) {
				System.out.println("123");
				System.out.println(event.getMessage());
			}
		});

		try {
			// 4. specify request arguments
			//创建 Android的通知
			JSONObject notification = new JSONObject();
			notification.put("title", "TEST666");
			notification.put("description","Hello Baidu Push");
			notification.put("notification_builder_id", 0);
			notification.put("notification_basic_style", 4);
			notification.put("open_type", 1);
			notification.put("url", "http://push.baidu.com");
			JSONObject jsonCustormCont = new JSONObject();
			jsonCustormCont.put("key", "value"); //自定义内容，key-value
			notification.put("custom_content", jsonCustormCont);

			PushMsgToSingleDeviceRequest request = new PushMsgToSingleDeviceRequest()
					.addChannelId("3595883595731275375")
					.addMsgExpires(new Integer(3600)). // message有效时间
					addMessageType(1).  // + //1：通知,0:透传消息. 默认为0 注：IOS只有通知.
					addMessage(notification.toString()).
					addDeviceType(3);   // deviceType => 3:android, 4:ios
			// 5. http request
			PushMsgToSingleDeviceResponse response = pushClient
					.pushMsgToSingleDevice(request);
			// Http请求结果解析打印
			System.out.println("msgId: " + response.getMsgId() + ",sendTime: "
					+ response.getSendTime());
		} catch (PushClientException e) {
			/*
			 * ERROROPTTYPE 用于设置异常的处理方式 -- 抛出异常和捕获异常,'true' 表示抛出, 'false' 表示捕获。
			 */
			if (BaiduPushConstants.ERROROPTTYPE) {
				throw e;
			} else {
				e.printStackTrace();
			}
		} catch (PushServerException e) {
			if (BaiduPushConstants.ERROROPTTYPE) {
				throw e;
			} else {
				System.out.println(String.format(
						"requestId: %d, errorCode: %d, errorMessage: %s",
						e.getRequestId(), e.getErrorCode(), e.getErrorMsg()));
			}
		}
	}
}
