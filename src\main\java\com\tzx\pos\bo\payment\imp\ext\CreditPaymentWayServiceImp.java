package com.tzx.pos.bo.payment.imp.ext;

import java.sql.Timestamp;
import java.util.Date;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.tzx.base.entity.PosBillMember;
import com.tzx.base.entity.PosThirdPaymentRefundEntity;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.util.DoubleHelper;
import com.tzx.framework.common.util.PropertiesLoader;
import com.tzx.framework.common.util.Tools;
import com.tzx.member.crm.bo.CustomerCreditService;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.bo.payment.PaymentWayService;
import com.tzx.pos.bo.payment.imp.PaymentWayServiceImp;
import com.tzx.pos.po.springjdbc.dao.PosBillMemberDao;
import com.tzx.thirdpay.bo.ThirdPaymentRefundService;

import net.sf.json.JSONObject;

/**
 * 会员积分
 *
 */
@Service("com.tzx.pos.bo.payment.imp.ext.CreditPaymentWayServiceImp")
public class CreditPaymentWayServiceImp extends PaymentWayServiceImp implements PaymentWayService
{
	@Resource(name = CustomerCreditService.NAME)
	private CustomerCreditService	customerService;
	
	@Resource(name = ThirdPaymentRefundService.NAME)
	private ThirdPaymentRefundService		thirdPaymentRefundService;

	@Resource(name = PosBillMemberDao.NAME)
	private PosBillMemberDao			memberDao;
	
	@Override
	public void payment(String tenantId, int storeId, String billNum, String batchNum, Date reportDate, int shiftId, String posNum, String optNum, String tableCode, JSONObject payment, Timestamp currentTime, String isOnlinePay) throws Exception
	{
		// TODO Auto-generated method stub
		// String chanel = payment.optString("chanel");

		double amount = payment.optDouble("amount");
		double currencyAmount = payment.optDouble("currency_amount");
		double count = payment.optDouble("count");
		String number = payment.optString("number");
		String phone = payment.optString("phone");
		String billCode = payment.optString("bill_code");

		int jzid = payment.optInt("jzid");
		String paymentClass = payment.optString("payment_class");
		String paymentName = payment.optString("payment_name");
		String nameEnglish = payment.optString("payment_english_name");
		double rate = payment.optDouble("rate");
		String remark = payment.optString("remark");
		String isYsk = payment.optString("is_ysk");
		if (Tools.isNullOrEmpty(isYsk))
		{
			isYsk = "N";
		}
		
		String mobil = ParamUtil.getStringValueByObject(payment, "mobil");
		String customerCode = ParamUtil.getStringValueByObject(payment, "customer_code");
		String customerName = ParamUtil.getStringValueByObject(payment, "customer_name");
		String cardCode = ParamUtil.getStringValueByObject(payment, "card_code");
		Double customerCredit = ParamUtil.getDoubleValueByObject(payment, "customer_credit");
		Double mainBalance = ParamUtil.getDoubleValueByObject(payment, "main_balance");
		Double rewardBalance = ParamUtil.getDoubleValueByObject(payment, "reward_balance");

		memberDao.deletePosBillMember(tenantId, storeId, billNum, SysDictionary.BILL_MEMBERCARD_JFDX05);
		
		PosBillMember billMember = new PosBillMember();
		billMember.setBill_num(billNum);
		billMember.setReport_date(reportDate);
		billMember.setType(SysDictionary.BILL_MEMBERCARD_JFDX05);
		billMember.setCard_code(cardCode);
		billMember.setMobil(mobil);
		billMember.setCustomer_code(customerCode);
		billMember.setCustomer_name(customerName);
		billMember.setAmount(currencyAmount);
		billMember.setCredit(amount);
		billMember.setConsume_after_credit(customerCredit);
		billMember.setConsume_before_credit(customerCredit);
		billMember.setConsume_after_main_balance(mainBalance);
		billMember.setConsume_before_main_balance(mainBalance);
		billMember.setConsume_after_reward_balance(rewardBalance);
		billMember.setConsume_before_reward_balance(rewardBalance);
		billMember.setLast_updatetime(currentTime);
		memberDao.insertPosBillMember(tenantId, storeId, billMember);

		if ("1".equalsIgnoreCase(isOnlinePay))
		{
			paymentDao.insertPosBillPayment(tenantId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, nameEnglish, rate, amount, count, currencyAmount, number, phone, isYsk, null, billCode, remark, currentTime,
					SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);

//			paymentDao.insertPosBillPaymentLog(tenantId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, nameEnglish, rate, amount, count, currencyAmount, number, phone, isYsk, null, billCode, remark, currentTime,
//					SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);

			memberDao.updatePosBillMember(tenantId, storeId, billNum, SysDictionary.BILL_MEMBERCARD_JFDX05, currencyAmount, amount, billCode, SysDictionary.REQUEST_STATUS_COMPLETE, null, 0d, 0d);
		}
		else
		{
			payment.put("pay_state", SysDictionary.PAYMENT_STATE_NOTPAY);
			paymentDao.insertPosBillPayment(tenantId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, nameEnglish, rate, amount, count, currencyAmount, number, phone, isYsk, null, null, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY,
					batchNum, payment.toString(), 0d);
		}
	}

	@Override
	public void paymentDebit(String tenancyId, int storeId, String billNum, String orderNum, String chanel, Double paymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData) throws Exception
	{
		// TODO Auto-generated method stub
		// String currentTimeStr = DateUtil.format(currentTime);

		JSONObject paramCachJson = JSONObject.fromObject(payment.optString("param_cach"));

		String optNum = payment.optString("cashier_num");
		int payId = payment.optInt("id");
		String number = payment.optString("number");
		// String optName = paymentDao.getEmpNameById(optNum, tenancyId,
		// storeId);

		JSONObject requestJson = new JSONObject();
		requestJson.put("customer_code", payment.optString("number"));
		requestJson.put("mobil", payment.optString("phone"));
		requestJson.put("cash_money", payment.optDouble("currency_amount"));
		requestJson.put("credit", paramCachJson.optDouble("consume_credit"));
		requestJson.put("chanel", paramCachJson.optString("chanel"));
		if ((!SysDictionary.CHANEL_MD01.equals(chanel)) && Tools.hv(orderNum))
		{
			requestJson.put("bill_code", orderNum);
			// requestJson.put("batch_no", "");
		}
		else
		{
			requestJson.put("bill_code", billNum);
			requestJson.put("batch_num", payment.optString("batch_num") + "_" + String.valueOf(currentTime.getTime()));
		}
		requestJson.put("pos_num", payment.optString("pos_num"));
		requestJson.put("opt_num", optNum);
		requestJson.put("card_code", payment.optString("remark"));
		requestJson.put("report_date", payment.optString("report_date"));
		requestJson.put("shift_id", payment.optInt("shift_id"));
		requestJson.put("bill_amount", paymentAmount);

		// List<JSONObject> requestList = new ArrayList<JSONObject>();
		// requestList.add(requestJson);
		//
		// Data requestData = Data.get(tenancyId, storeId, 0);
		// requestData.setType(Type.CUSTOMER_CREDIT_COUNSUME);
		// requestData.setOper(Oper.add);
		// requestData.setData(requestList);
		//
		// Data responseData = Data.get(requestData);
		// customerService.commonPost(JSONObject.fromObject(requestData).toString(),
		// responseData);

		Data responseData = Data.get();
		customerService.customerCreditConsume(tenancyId, storeId, requestJson, responseData, Type.BILL_PAYMENT.name());

		if (Constant.CODE_SUCCESS == responseData.getCode())
		{
			JSONObject responseJson = JSONObject.fromObject(responseData.getData().get(0));

			String billCode = responseJson.optString("bill_code");
			Double consumeAmount = responseJson.optDouble("change_money");
			Double consumeCredit = Math.abs(responseJson.optDouble("credit"));
			Double usefulCredit = responseJson.optDouble("useful_credit");

			memberDao.updatePosBillMember(tenancyId, storeId, billNum, SysDictionary.BILL_MEMBERCARD_JFDX05, consumeAmount, consumeCredit, billCode, SysDictionary.REQUEST_STATUS_COMPLETE, responseJson.optString("name"), DoubleHelper.add(usefulCredit, consumeCredit, DEFAULT_SCALE), usefulCredit);

			JSONObject paymentJson = new JSONObject();
			paymentJson.put("bill_code", billCode);
			paymentJson.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
			paymentJson.put("id", payId);
			paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", paymentJson);
			
			payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
			resultData.setCode(Constant.CODE_SUCCESS);
		}
		else if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
		{
			payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY);
			resultData.setCode(Constant.CODE_CONN_EXCEPTION);
			resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
		}
		else if (Constant.PAYMENT_STATUS_PAYING_CODE == responseData.getCode())
		{
			payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY);
			resultData.setCode(Constant.PAYMENT_STATUS_PAYING_CODE);
			resultData.setMsg(PropertiesLoader.getProperty(String.valueOf(responseData.getCode())));
		}
		else
		{
			paymentDao.deletePosBillPaymentByID(tenancyId, storeId, billNum, payId);

			Date reportDate = DateUtil.parseDate(payment.optString("report_date"));
			Integer shiftId = payment.optInt("shift_id");
			String posNum = payment.optString("pos_num");

			int jzid = payment.optInt("jzid");
			String table_code = payment.optString("table_code");
			String paymentClass = payment.optString("type");
			String paymentName = payment.optString("name");
			String paymentEnglishName = payment.optString("name_english");
			double exchangeRate = payment.optDouble("rate");
			double pay_amount = payment.optDouble("amount");
			double pay_count = payment.optDouble("count");
			double currency_amount = payment.optDouble("currency_amount");
			String phone = payment.optString("phone");
			int customer_id = payment.optInt("customer_id");
			String remark = payment.optString("remark");
			String batch_num = payment.optString("batch_num");
			String is_ysk = Tools.hv(payment.optString("is_ysk")) ? payment.optString("is_ysk") : "N";
			paymentDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, table_code, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, customer_id, null, remark, currentTime,
					SysDictionary.PAYMENT_STATE_PAY_FAILURE, batch_num, payment.optString("param_cach"), 0d);

			memberDao.deletePosBillMember(tenancyId, storeId, billNum, SysDictionary.BILL_MEMBERCARD_JFDX05);

			payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_FAILURE);
			resultData.setCode(PosErrorCode.BILL_PAYMENT_ERROP.getNumber());
			resultData.setMsg(PosErrorCode.BILL_PAYMENT_ERROP.getMessage().replace("{0}", responseData.getMsg()));
		}
	}

	@Override
	public void findPaymentDebit(String tenancyId, int storeId, String billNum, String orderNum, String chanel, Double paymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData) throws Exception
	{
		// TODO Auto-generated method stub
		String number = payment.getString("number");
		String phone = payment.getString("phone");

		// JSONObject cachParamJson =
		// JSONObject.fromObject(payment.getString("param_cach"));
		JSONObject requestJson = new JSONObject();

		requestJson.put("code", number);
		requestJson.put("mobil", phone);
		requestJson.put("wechat", "");
		if ((!SysDictionary.CHANEL_MD01.equals(chanel)) && Tools.hv(orderNum))
		{
			requestJson.put("bill_code", orderNum);
			// requestJson.put("batch_no", "");
		}
		else
		{
			String batch_num = payment.getString("batch_num");
			String lastUpdatetimeStr = String.valueOf(DateUtil.formatTimestamp(payment.getString("last_updatetime")).getTime());

			requestJson.put("bill_code", billNum);
			requestJson.put("batch_no", batch_num + "_" + lastUpdatetimeStr);
		}
		requestJson.put("shift_id", payment.optInt("shift_id"));
		requestJson.put("type", "0");

		// List<JSONObject> requestList = new ArrayList<JSONObject>();
		// requestList.add(requestJson);
		//
		// Data requestData = Data.get(tenancyId, storeId, 0);
		// requestData.setType(Type.CUSTOMER_CREDIT_COUNSUME);
		// requestData.setOper(Oper.check);
		// requestData.setData(requestList);
		//
		// Data responseData = Data.get(requestData);
		// customerService.commonPost(JSONObject.fromObject(requestData).toString(),
		// responseData);

		Data responseData = customerService.queryCustomerCreditConsume(tenancyId, storeId, requestJson);

		if (responseData.isSuccess())
		{
			JSONObject responseJson = JSONObject.fromObject(responseData.getData().get(0));

			String billCode = responseJson.optString("bill_code");
			Double consumeAmount = responseJson.optDouble("change_money");
			if (consumeAmount.isNaN())
			{
				consumeAmount = 0d;
			}
			Double consumeCredit = responseJson.optDouble("credit");
			if (consumeCredit.isNaN())
			{
				consumeCredit = 0d;
			}

			Double usefulCredit = responseJson.optDouble("useful_credit");
			if (usefulCredit.isNaN())
			{
				usefulCredit = 0d;
			}

			payment.put("bill_code", billCode);
			payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
			
			JSONObject updatePaymentJson = new JSONObject();
			updatePaymentJson.put("bill_code", billCode);
			updatePaymentJson.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
			updatePaymentJson.put("id", payment.optInt("id"));
			paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", updatePaymentJson);
			
			resultData.setCode(Constant.CODE_SUCCESS);

			memberDao.updatePosBillMember(tenancyId, storeId, billNum, SysDictionary.BILL_MEMBERCARD_JFDX05, consumeAmount, consumeCredit, billCode, SysDictionary.REQUEST_STATUS_COMPLETE, responseJson.optString("name"), DoubleHelper.add(usefulCredit, consumeCredit, DEFAULT_SCALE), usefulCredit);
		}
		else if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
		{
			payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY);
			resultData.setCode(Constant.CODE_CONN_EXCEPTION);
			resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
		}
		else
		{
			payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_FAILURE);

			JSONObject updatePaymentJson = new JSONObject();
			updatePaymentJson.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_FAILURE);
			updatePaymentJson.put("id", payment.optInt("id"));
			paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", updatePaymentJson);

			resultData.setCode(PosErrorCode.BILL_PAYMENT_ERROP.getNumber());
			resultData.setMsg(PosErrorCode.BILL_PAYMENT_ERROP.getMessage().replace("{0}", responseData.getMsg()));
		}
	}

	@Override
	public void paymentRefund(String tenancyId, int storeId, String billNum, String oldBillNum, String orderNum, String chanel, Double billPaymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData, String operType) throws Exception
	{
		// TODO Auto-generated method stub
//		String currentTimeStr = DateUtil.format(currentTime);
		String lastUpdatetimeStr = String.valueOf(DateUtil.formatTimestamp(payment.optString("last_updatetime")).getTime());

		String optNum = ParamUtil.getStringValueByObject(payment,"cashier_num");
		String oldBillCode = ParamUtil.getStringValueByObject(payment, "bill_code");
		String reportDateStr = ParamUtil.getStringValueByObject(payment, "report_date");
		Integer shiftId = ParamUtil.getIntegerValueByObject(payment, "shift_id");
		String posNum = ParamUtil.getStringValueByObject(payment, "pos_num");
		Double currencyAmount = Math.abs(ParamUtil.getDoubleValueByObject(payment, "currency_amount"));
//		String optName = paymentDao.getEmpNameById(optNum, tenancyId, storeId);

		JSONObject creditJson = new JSONObject();
		creditJson.put("code", payment.optString("number"));
		creditJson.put("mobil", payment.optString("phone"));
		creditJson.put("old_bill_code", oldBillCode);
		if ((!SysDictionary.CHANEL_MD01.equals(chanel)) && Tools.hv(orderNum))
		{
			creditJson.put("bill_code", orderNum);
			// creditJson.put("batch_no", "");
		}
		else
		{
			creditJson.put("bill_code", oldBillNum);
			creditJson.put("batch_no", payment.optString("batch_num") + "_" + lastUpdatetimeStr);
		}
		creditJson.put("chanel", chanel);
		creditJson.put("report_date", reportDateStr);
		creditJson.put("shift_id", shiftId);
		creditJson.put("opt_num", optNum);
		creditJson.put("pos_num", posNum);
		creditJson.put("amount", currencyAmount);

		Data responseData = Data.get();
		customerService.customerCreditCancelConsume(tenancyId, storeId, creditJson, responseData, operType);

		if (Constant.CODE_SUCCESS == responseData.getCode())
		{
			resultData.setCode(Constant.CODE_SUCCESS);
			
			JSONObject responseJson = JSONObject.fromObject(responseData.getData().get(0));

			JSONObject paymentJson = new JSONObject();
			paymentJson.put("bill_code", responseJson.optString("bill_code"));
			paymentJson.put("payment_state", SysDictionary.PAYMENT_STATE_REFUND_COMPLETE);
			paymentJson.put("id", payment.optInt("id"));
			paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", paymentJson);
		}
		else
		{
			if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
			{
				resultData.setCode(Constant.CODE_CONN_EXCEPTION);
				resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
			}
			else
			{
				JSONObject paymentJson = new JSONObject();
				paymentJson.put("payment_state", SysDictionary.PAYMENT_STATE_REFUND_FAILURE);
				paymentJson.put("id", payment.optInt("id"));
				paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", paymentJson);

				resultData.setCode(responseData.getCode());
				resultData.setMsg(responseData.getMsg());
			}

			// 退款失败,记录退款异常
			Integer paymentId = ParamUtil.getIntegerValueByObject(payment, "jzid");
			String paymentClass = ParamUtil.getStringValueByObject(payment, "payment_class");
			String thirdBillCode = billNum + "_" + lastUpdatetimeStr;

			PosThirdPaymentRefundEntity paymentRefund = new PosThirdPaymentRefundEntity(tenancyId, storeId, DateUtil.parseDate(reportDateStr), shiftId, posNum, optNum, chanel, SysDictionary.SERVICE_TYPE_CONSUME, oldBillNum, thirdBillCode, oldBillCode, paymentClass, paymentId,
					Math.abs(billPaymentAmount), currencyAmount, currentTime, currentTime, optNum, String.valueOf(resultData.getCode()), resultData.getMsg(), operType, SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL, SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL);
			paymentRefund.setExtra(creditJson.toString());

			thirdPaymentRefundService.thirdPaymentRefundFailure(tenancyId, storeId, paymentRefund);
		}
	}

	@Override
	public void paymentCancel(String tenancyId, int storeId, String billNum, String orderNum, String chanel, Double billPaymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData, String operType) throws Exception
	{
		// TODO Auto-generated method stub
//		String currentTimeStr = DateUtil.format(currentTime);
		String lastUpdatetimeStr = String.valueOf(DateUtil.formatTimestamp(payment.optString("last_updatetime")).getTime());

		String optNum = payment.optString("cashier_num");
		String oldBillCode = ParamUtil.getStringValueByObject(payment, "bill_code");
		String reportDateStr = ParamUtil.getStringValueByObject(payment, "report_date");
		Integer shiftId = ParamUtil.getIntegerValueByObject(payment, "shift_id");
		String posNum = ParamUtil.getStringValueByObject(payment, "pos_num");
		Double currencyAmount = Math.abs(ParamUtil.getDoubleValueByObject(payment, "currency_amount"));
//		String optName = paymentDao.getEmpNameById(optNum, tenancyId, storeId);

		JSONObject creditJson = new JSONObject();
		creditJson.put("code", payment.optString("number"));
		creditJson.put("mobil", payment.optString("phone"));
		creditJson.put("old_bill_code", oldBillCode);
		if ((!SysDictionary.CHANEL_MD01.equals(chanel)) && Tools.hv(orderNum))
		{
			creditJson.put("bill_code", orderNum);
			// creditJson.put("batch_no", "");
		}
		else
		{
			creditJson.put("bill_code", billNum);
			creditJson.put("batch_no", payment.optString("batch_num") + "_" + lastUpdatetimeStr);
		}
		creditJson.put("chanel", chanel);
//		creditJson.put("operator_id", optNum);
//		creditJson.put("operator", optName);
//		creditJson.put("updatetime", currentTimeStr);
		creditJson.put("report_date", reportDateStr);
		creditJson.put("shift_id", shiftId);
		creditJson.put("opt_num", optNum);
		creditJson.put("pos_num", posNum);
		creditJson.put("amount", currencyAmount);

		// List<JSONObject> dataList = new ArrayList<JSONObject>();
		// dataList.add(creditJson);
		//
		// Data paramData = Data.get(tenancyId,storeId,Constant.CODE_SUCCESS);
		// paramData.setType(Type.CUSTOMER_CREDIT_COUNSUME);
		// paramData.setOper(Oper.update);
		// paramData.setData(dataList);
		//
		// Data responseData = new Data();
		// String param = JSONObject.fromObject(paramData).toString();
		//
		// customerService.commonPost(param, responseData);

		Data responseData = Data.get();
		customerService.customerCreditCancelConsume(tenancyId, storeId, creditJson, responseData, operType);

		if (Constant.CODE_SUCCESS == responseData.getCode())
		{
			JSONObject responseJson = JSONObject.fromObject(responseData.getData().get(0));
			payment.put("bill_code", responseJson.optString("bill_code"));

			resultData.setCode(Constant.CODE_SUCCESS);
		}
		else if (Constant.CARD_PAYMENT_ORDER_NOT_EXIST_CODE == responseData.getCode())
		{
			resultData.setCode(Constant.CODE_SUCCESS);
		}
		else
		{
			if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
			{
				resultData.setCode(Constant.CODE_CONN_EXCEPTION);
				resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
			}
			else
			{
				resultData.setCode(responseData.getCode());
				resultData.setMsg(responseData.getMsg());
			}
			
			// 退款失败,记录退款异常
			Integer paymentId = ParamUtil.getIntegerValueByObject(payment, "jzid");
			String paymentClass = ParamUtil.getStringValueByObject(payment, "payment_class");
			String thirdBillCode = billNum + "_" + lastUpdatetimeStr;
			
			PosThirdPaymentRefundEntity paymentRefund = new PosThirdPaymentRefundEntity(tenancyId, storeId, DateUtil.parseDate(reportDateStr), shiftId, posNum, optNum, chanel, SysDictionary.SERVICE_TYPE_CONSUME, billNum, thirdBillCode, oldBillCode, paymentClass, paymentId, billPaymentAmount, currencyAmount, currentTime,
					currentTime, optNum, String.valueOf(resultData.getCode()), resultData.getMsg(), operType, SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL, SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL);
			paymentRefund.setExtra(creditJson.toString());
			
			thirdPaymentRefundService.thirdPaymentRefundFailure(tenancyId, storeId, paymentRefund);

		}
	}

	@Override
	public void paymentRegain(String tenancyId, int storeId, String billNum, String orderNum, String chanel, Double billPaymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData, String operType) throws Exception
	{
		// TODO Auto-generated method stub
//		String currentTimeStr = DateUtil.format(currentTime);
		String lastUpdatetimeStr = String.valueOf(DateUtil.formatTimestamp(payment.optString("last_updatetime")).getTime());

		String optNum = ParamUtil.getStringValueByObject(payment, "cashier_num");
		String oldBillCode = ParamUtil.getStringValueByObject(payment, "bill_code");
		String reportDateStr = ParamUtil.getStringValueByObject(payment, "report_date");
		Integer shiftId = ParamUtil.getIntegerValueByObject(payment, "shift_id");
		String posNum = ParamUtil.getStringValueByObject(payment, "pos_num");
		Double currencyAmount = Math.abs(ParamUtil.getDoubleValueByObject(payment, "currency_amount"));
//		String optName = paymentDao.getEmpNameById(optNum, tenancyId, storeId);

		JSONObject creditJson = new JSONObject();
		creditJson.put("code", payment.optString("number"));
		creditJson.put("mobil", payment.optString("phone"));
		creditJson.put("old_bill_code", oldBillCode);
		if ((!SysDictionary.CHANEL_MD01.equals(chanel)) && Tools.hv(orderNum))
		{
			creditJson.put("bill_code", orderNum);
			// creditJson.put("batch_no", "");
		}
		else
		{
			creditJson.put("bill_code", billNum);
			creditJson.put("batch_no", payment.optString("batch_num") + "_" + lastUpdatetimeStr);
		}
		creditJson.put("chanel", chanel);
//		creditJson.put("operator_id", optNum);
//		creditJson.put("operator", optName);
//		creditJson.put("updatetime", currentTimeStr);
		creditJson.put("report_date", reportDateStr);
		creditJson.put("shift_id", shiftId);
		creditJson.put("opt_num", optNum);
		creditJson.put("pos_num", posNum);
		creditJson.put("amount", currencyAmount);

		// List<JSONObject> dataList = new ArrayList<JSONObject>();
		// dataList.add(creditJson);
		//
		// Data paramData = Data.get(tenancyId,storeId,Constant.CODE_SUCCESS);
		// paramData.setType(Type.CUSTOMER_CREDIT_COUNSUME);
		// paramData.setOper(Oper.update);
		// paramData.setData(dataList);
		//
		// Data responseData = new Data();
		// String param = JSONObject.fromObject(paramData).toString();
		//
		// customerService.commonPost(param, responseData);

		Data responseData = Data.get();
		customerService.customerCreditCancelConsume(tenancyId, storeId, creditJson, responseData, operType);

		if (responseData.isSuccess())
		{
			JSONObject responseJson = JSONObject.fromObject(responseData.getData().get(0));
			payment.put("bill_code", responseJson.optString("bill_code"));

			resultData.setCode(Constant.CODE_SUCCESS);
		}
		else
		{
			if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
			{
				resultData.setCode(Constant.CODE_CONN_EXCEPTION);
				resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
			}
			else
			{
				resultData.setCode(responseData.getCode());
				resultData.setMsg(responseData.getMsg());
			}

			// 退款失败,记录退款异常
			Integer paymentId = ParamUtil.getIntegerValueByObject(payment, "jzid");
			String paymentClass = ParamUtil.getStringValueByObject(payment, "payment_class");
			String thirdBillCode = billNum + "_" + lastUpdatetimeStr;

			PosThirdPaymentRefundEntity paymentRefund = new PosThirdPaymentRefundEntity(tenancyId, storeId, DateUtil.parseDate(reportDateStr), shiftId, posNum, optNum, chanel, SysDictionary.SERVICE_TYPE_CONSUME, billNum, thirdBillCode, oldBillCode, paymentClass, paymentId, billPaymentAmount,
					currencyAmount, currentTime, currentTime, optNum, String.valueOf(resultData.getCode()), resultData.getMsg(), operType, SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL, SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL);
			paymentRefund.setExtra(creditJson.toString());

			thirdPaymentRefundService.thirdPaymentRefundFailure(tenancyId, storeId, paymentRefund);
		}
	}

	@Override
	public void paymentDebitResult(String tenancyId, int storeId, String billNum, JSONObject paymentJson) throws Exception
	{
		// TODO 自动生成的方法存根
		JSONObject updatePaymentJson = new JSONObject();
		updatePaymentJson.put("bill_code", paymentJson.optString("bill_code"));
		updatePaymentJson.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
		updatePaymentJson.put("id", paymentJson.optInt("id"));
		paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", updatePaymentJson);
	}

}
