package com.tzx.thirdpay.po.springjdbc.dao;

import java.sql.Timestamp;
import java.util.List;

import com.tzx.pos.base.dao.BaseDao;
import com.tzx.pos.bo.dto.PosThirdPaymentOrderEntity;

import net.sf.json.JSONObject;

/**
 * <AUTHOR>
 *
 */
public interface ThirdPaymentDao extends BaseDao
{
	String	NAME	= "com.tzx.thirdpay.po.springjdbc.dao.imp.ThirdPaymentDaoImp";
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param aidOrderNo
	 * @param objectName
	 * @return
	 * @throws Exception
	 */
	public PosThirdPaymentOrderEntity getThirdPaymentAllByAidOrderNum(String tenancyId, int storeId, String aidOrderNo, String objectName) throws Exception;

	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param aidOrderNo
	 * @param objectName
	 * @return
	 * @throws Exception
	 */
	public PosThirdPaymentOrderEntity getThirdPaymentByAidOrderNum(String tenancyId, int storeId, String aidOrderNo, String objectName) throws Exception;
	
	/**
	 * 根据订单号查询第三方支付记录
	 * 
	 * @param tenancy_id
	 * @param order_no
	 * @param service_type
	 * @param pay_type
	 * @param thirdPayCharge
	 * @return
	 * @throws Exception
	 */
	public JSONObject getPayThirdByOrderNum(String tenancyId, int storeId, String order_no, String service_type, String pay_type, String thirdPayCharge) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @return
	 * @throws Exception
	 */
	public List<PosThirdPaymentOrderEntity> getThirdPaymentOrderForNotComplete(String tenancyId, int storeId, Timestamp queryTime, Double queryCount) throws Exception;

	/**
	 * 插入支付信息记录
	 * 
	 * @param orderParam
	 * @param tenantId
	 * @return
	 * @throws Exception
	 */
	public Integer insertThirdPaymentOrder(String tenantId, JSONObject orderParam) throws Exception;
	
	/**
	 * @param tenantId
	 * @param orderParam
	 * @return
	 * @throws Exception
	 */
	public Integer insertThirdPaymentOrder(String tenantId, PosThirdPaymentOrderEntity orderParam) throws Exception;

	/**
	 * 修改支付信息记录
	 * 
	 * @param id
	 * @param tenantId
	 * @param orderParam
	 * @throws Exception
	 */
	public void updateThirdPaymentOrder(int id, String tenantId, JSONObject orderParam) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param paymentId
	 * @param queryTime
	 * @param queryCount
	 * @throws Exception
	 */
	public void updateThirdPaymentOrderForQuery(String tenancyId, int storeId, int paymentId, Timestamp queryTime, int queryCount) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param orderNo
	 * @return
	 * @throws Exception
	 */
	public String getBillNumByOrderNo(String tenancyId, int storeId, String orderNo) throws Exception;
	
	/**
	 * 根据结账ID获取结账信息
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @return
	 * @throws Exception
	 */
	public JSONObject getBillPaymentByJzid(String tenancyId, int storeId, String billNum, int jzid) throws Exception;
	
	/** 根据账单编号获取付款信息
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @return
	 * @throws Exception
	 */
	public JSONObject getPosBillPaymentByBillnum(String tenancyId,int storeId,String billNum,Integer paymentId) throws Exception;
	
	/** 根据原账单编号获取账单整单取消付款信息
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getPosBillPaymentByCancBill(String tenancyId,int storeId,String billNum,Integer paymentId) throws Exception;
	
	/** 根据账单编号获取恢复账单原付款信息
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param paymentId
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getPosBillPaymentByRegainBill(String tenancyId,int storeId,String billNum,Integer paymentId) throws Exception;
	
}
