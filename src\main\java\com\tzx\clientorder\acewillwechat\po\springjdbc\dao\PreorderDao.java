package com.tzx.clientorder.acewillwechat.po.springjdbc.dao;

import com.tzx.framework.common.util.dao.GenericDao;
import net.sf.json.JSONObject;

/**
 * Created by q<PERSON>-<PERSON><PERSON> on 2018-03-15.
 */
public interface PreorderDao extends GenericDao{

    String NAME = "com.tzx.clientorder.acewillwechat.po.springjdbc.dao.imp.PreorderDaoImp";

    /**
     * 查询参数表的会员类型
     * @param tenancyId
     * @return
     * @throws Exception
     */
    String getMemberType(String tenancyId) throws Exception;

    /**
     * 查询账单折扣需要的信息
     * @param tenancyId
     * @param storeId
     * @param tableCode
     * @return
     * @throws Exception
     */
    JSONObject getBillDiscount(String tenancyId, int storeId, String tableCode) throws Exception;
}
