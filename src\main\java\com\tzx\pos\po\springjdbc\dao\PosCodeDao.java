package com.tzx.pos.po.springjdbc.dao;

public interface PosCodeDao {
	String NAME = "com.tzx.pos.po.springjdbc.dao.imp.PosCodeDaoImp";
	/**
	 * 查询对应CodeValue值
	 * @param tenantId
	 * @param type
	 * @param store_id
	 * @param prefix
	 * @return
	 * @throws Exception
	 */
	public String getDataValue(String tenantId, final String type, Integer store_id, final String prefix) throws Exception;
	/**
	 * 删除缓存对应值
	 * @param type
	 */
	public void delete(String type);
	/**
	 * 更新缓存对应值
	 * @param res
	 * @param type
	 * @return
	 */
	public String update(String res,String type);
	
	/**
	 * 更新数据 库信息
	 * @param res
	 * @param type
	 * @return
	 * @throws Exception 
	 */
	public int updateDB(String tenantId, final String type, Integer store_id, final String prefix,Integer current_value) throws Exception;
	
	/**
	 * 添加数据库信息
	 * @param tenantId
	 * @param type
	 * @param store_id
	 * @param prefix
	 * @param current_value
	 * @return
	 * @throws Exception
	 */
	public int insertDB(String tenantId, final String type, Integer store_id, final String prefix,Integer current_value) throws Exception;
}
