package com.tzx.pos.po.springjdbc.dao;

import com.tzx.framework.common.entity.Data;
import com.tzx.pos.base.dao.BaseDao;
import com.tzx.pos.bo.dto.BasicDataUserDiscountAuthority;




import java.util.Date;
import java.util.List;

import net.sf.json.JSONObject;

/**
 * Created by kevin on 2017-08-14.
 */
public interface PosDiscountDao extends BaseDao {

    String	NAME	= "com.tzx.pos.po.springjdbc.dao.imp.PosDiscountDaoImp";
    /**
     *
     * @param tenancyId
     * @param storeId
     * @param optNum
     * @return
     * @throws Exception
     */
    public List<BasicDataUserDiscountAuthority> getUserDiscountCase(String tenancyId, int storeId, String optNum) throws Exception;

    /**
     *
     * @param tenancyId
     * @param storeId
     * @param optNum
     * @return
     * @throws Exception
     */
    public int getRoleIdByOptNum(String tenancyId, int storeId, String optNum) throws Exception;

    /**
     *
     * @param tenancyId
     * @param roleId
     * @return
     * @throws Exception
     */
    public List<BasicDataUserDiscountAuthority> getRoleDiscountCase(String tenancyId, int roleId) throws Exception;
    
    /**
     * @param tenancyId
     * @param storeId
     * @param optNum
     * @return
     * @throws Exception
     */
    public List<BasicDataUserDiscountAuthority> getRoleDiscountAuthority(String tenancyId, int storeId, String optNum) throws Exception;

    /**
     *
     * @param param
     * @param employeeId
     * @param reportDate
     * @return
     * @throws Exception
     */
    public Double getItemAmountByManagerNum(Data param, String employeeId, Date reportDate) throws Exception ;
    
    /**
     * 根据账单号获取已奉送金额
     * @param param
     * @param employeeId
     * @param billNum
     * @return
     * @throws Exception
     */
    public Double getItemAmountByBillNum(Data param, String employeeId, String billNum) throws Exception ;

    /**
     *
     * @param param
     * @param employeeId
     * @param reportDate
     * @return
     * @throws Exception
     */
    public Double getDiscountAmountByDiscountNum(Data param, int employeeId, Date reportDate,String billNum) throws Exception;
    
    /**
     * @param tenancyId
     * @param storeId
     * @param billNum
     * @param rwid
     * @return
     * @throws Exception
     */
    public JSONObject getPosBillItemByRwid(String tenancyId, int storeId, String billNum,Integer rwid) throws Exception;
    
    /**
     * @param tenancyId
     * @param storeId
     * @param billNum
     * @param rwid
     * @param discountModeId
     * @param discountNum
     * @param discountRate
     * @param discountReasonId
     * @throws Exception
     */
    public void updateBillItemForDiscountByRwid(String tenancyId, int storeId, String billNum,Integer rwid,Integer discountModeId,String discountNum,Double discountRate,Integer discountReasonId)throws Exception;
}
