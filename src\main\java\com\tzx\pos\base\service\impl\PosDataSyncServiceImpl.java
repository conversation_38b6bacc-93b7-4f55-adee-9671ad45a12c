/** 
 * @(#)CodeFormat.java    1.0   2017-05-16
 * 
 * Copyright 北京普照天星科技有限公司. All rights reserved. 
 * PROPRIETARY/CONFIDENTIAL. Use is subject to license terms. 
 */
package com.tzx.pos.base.service.impl;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import com.tzx.pos.base.dao.DataUploadDao;
import com.tzx.pos.base.service.PosDataSyncService;
import com.tzx.pos.po.springjdbc.dao.PosBaseDao;

import net.sf.json.JSONObject;

/**
 * 门店端数据同步程序.
 *
 * <AUTHOR> email:q<PERSON><PERSON><PERSON><PERSON>@tzx.com.cn
 * @version 1.0  2017-05-16
 * @see     
 * @since   JDK7.0
 * @update  
 */
@Service
public class PosDataSyncServiceImpl implements PosDataSyncService{	
	
	private static Logger logger = Logger.getLogger(PosDataSyncServiceImpl.class);
	
	@Resource(name = DataUploadDao.NAME)
	private DataUploadDao dataUploadDao;
	
	@Resource(name = PosBaseDao.NAME)
	private PosBaseDao	baseDao;
	
//	@Resource(name = TaskRedisDao.NAME)
//	private TaskRedisDao taskRedisDao;
	
	// 定义数据同步线程池对象
	private static volatile ThreadPoolExecutor dataSyncThreadPool = null;
	
	public static final long validHours = 8;
	
	/**
	 * @Description 获取线程池对象（通过双重检查锁实现）。
	 * @param
	 * @return ThreadPoolExecutor 线程池对象
	 * @exception <AUTHOR>                zhehong.qiu email:<EMAIL>
	 * @version 1.0 2017-05-16
	 * @see
	 */
	public static ThreadPoolExecutor getConsumerThreadPool() {
		if(dataSyncThreadPool == null){
			synchronized (ThreadPoolExecutor.class) {
				if(dataSyncThreadPool == null){
					/*
					 * 创建固定大小为1的线程池，使用ArrayBlockingQueue阻塞队列，队列大小为1，线程数超过队列大小时的策略为抛弃旧任务然后重试。
					 * 线程池队列大小为1，即数据同步操作频繁时，新任务立刻取代等待队列中的任务，减轻服务端同步压力，减少网络传输量。
					 */
					dataSyncThreadPool = new ThreadPoolExecutor(1,1,3,TimeUnit.SECONDS, new ArrayBlockingQueue<Runnable>(1),
							new ThreadPoolExecutor.DiscardOldestPolicy());
				}
			}
		}
		return dataSyncThreadPool;
	}

	/**
	 * @Description 门店端pos_soldout表数据同步至redis缓存（全量数据同步）。
	 * @param 
	 * @return 
	 * @exception
	 * <AUTHOR> email:<EMAIL>
	 * @version 1.0  2017-05-16
	 * @see
	 */
	@Override
	public void dataSyncForPosSoldOut(final String tenentId,final String storeId) {		

/*		String data = dataUploadDao.getPosSoldOutData(tenentId);
//		String data = "{\"data\":[{\"t\":\"jiulixiang\",\"s\":\"6\",\"i\":\"261\",\"u\":\"0\",\"n\":\"0\"},{\"t\":\"jiulixiang\",\"s\":\"6\",\"i\":\"221\",\"u\":\"0\",\"n\":\"0\"},{\"t\":\"jiulixiang\",\"s\":\"6\",\"i\":\"302\",\"u\":\"0\",\"n\":\"0\"}]}";
		logger.info("沽清上传redis数据 ："+data);

		*//*
		 * 压缩Json格式数据
		 *//*
		byte[] srtbyte = data.getBytes();
		// 进行数据压缩
		final byte[] compressByte = CompressAndDecompress.compressBytes(srtbyte);

		*//*
		 * 从线程池获取线程，执行数据步至redis操作。
		 *//*
		Runnable r = new Runnable() {
			@Override
			public void run() {
				// 同步数据至redis。
				// 调用总部端方法，联系李
				// 凯凯
				InputStreamBody bin = new InputStreamBody(new ByteArrayInputStream(compressByte), "file");
				String data1="tenancyId="+tenentId+"&storeId="+storeId+"&data="+bin;
				logger.info("沽清上传redis压缩数据 ："+data1);
				String url= PosPropertyUtil.getMsg(Constant.SOLDOUT_URL);
				String res=HttpClientPostUtil.sendPostRequest(url,data1);
				logger.info("沽清redis返回数据 ："+res);
			}
		};
		getConsumerThreadPool().execute(r);   // 执行线程*/
	}

	@Override
	public void reIndex(){
		try {
			Calendar now = Calendar.getInstance();  
			int day = now.get(Calendar.DAY_OF_MONTH);
			if(day % 7 == 0){
				List<String> indexNameList = new ArrayList<String>();
				String sql = "";
				List<JSONObject> indexList = baseDao.getIndexNameList();
				for(JSONObject json : indexList){
					indexNameList.add(json.optString("indexname"));
				}
				boolean bo = baseDao.execSQLList(indexNameList);
				if(bo){
					logger.info("重建数据库索引成功！");
				}else{
					logger.error("重建数据库索引失败！");
				}
			}else{
				logger.info("当天天数不是7的倍数，不执行重建数据库索引操作。");
			}			
		} catch (Exception e) {
			logger.error("重建数据库索引出现异常，异常信息："+e.getMessage());
		}
	}

}
