package com.tzx.ios.po.springjdbc.dao.imp;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.List;

import com.tzx.framework.common.util.Tools;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Repository;

import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.entity.Pagination;
import com.tzx.ios.po.springjdbc.dao.BaseDataSyncDao;
import com.tzx.pos.base.dao.imp.BaseDaoImp;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.bo.dto.BasicDataDiscountCase;
import com.tzx.ios.bo.dto.BasicDataDish;
import com.tzx.pos.bo.dto.BasicDataServiceFeeType;
import com.tzx.pos.bo.dto.BasicDataUnusualReason;
import com.tzx.pos.bo.dto.BasicDataUserDiscountAuthority;
import com.tzx.pos.bo.dto.BasicDataUserDiscountCase;
import com.tzx.pos.bo.dto.BasicDevice;
import com.tzx.pos.bo.dto.ItemGroup;
import com.tzx.pos.bo.dto.PosSysParam;
import com.tzx.pos.bo.dto.PosTableInfo;
import com.tzx.pos.po.springjdbc.dao.imp.PosDaoImp;

@Repository(BaseDataSyncDao.NAME)
public class BaseDataSyncDaoImp extends BaseDaoImp implements BaseDataSyncDao
{
	private static final Logger	logger	= Logger.getLogger(PosDaoImp.class);

	@Override
	public List<JSONObject> getDutyOrderByOrganId(String tenantId, Integer organId) throws Exception
	{
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject(); // data数组里的对象

		StringBuilder sql = new StringBuilder(" select dor.id, sd.class_item as name,dor.start_time,dor.end_time,dor.start_property,dor.end_property  from duty_order as dor ");
		sql.append(" left JOIN duty_order_of_ogran as doo on dor.id = doo.duty_order_id left join sys_dictionary sd on dor.name = sd.class_item_code and sd.class_identifier_code = 'duty' ");
		sql.append(" where dor.valid_state = '1' and doo.organ_id = " + organId);
		sql.append(" group by dor.id,sd.class_item,dor.start_time,dor.end_time order by dor.id asc ");
		logger.debug("getDutyOrderByOrganId:::" + sql.toString());

		List<JSONObject> dutyOrders = this.queryString4Json(tenantId, sql.toString());
		if (dutyOrders != null)
		{
			JSONArray dutyOrderArray = JSONArray.fromObject(dutyOrders);
			object.element("duty_order", dutyOrderArray);
		}
		list.add(object);
		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return list;
	}

	@Override
	public List<JSONObject> getTablePropertyByOrganId(String tenantId, Integer organId) throws Exception
	{
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject(); // data数组里的对象

		StringBuilder sql = new StringBuilder("select ti.table_property_id,sd.class_item_code,sd.class_item as table_property_name from tables_info as ti ");
		sql.append(" left join sys_dictionary as sd on ti.table_property_id = sd.id and sd.class_identifier_code = 'table_property' where ti.valid_state = '1' and ti.organ_id = ");
		sql.append(organId + " group by table_property_id,class_item_code,table_property_name ");
		sql.append(" order by ti.table_property_id asc ");

		logger.debug("getTablePropertyByOrganId:::" + sql.toString());

		List<JSONObject> tableProperties = this.queryString4Json(tenantId, sql.toString());
		if (tableProperties != null)
		{
			JSONArray tablePropertyArray = JSONArray.fromObject(tableProperties);
			object.element("table_property", tablePropertyArray);
		}
		list.add(object);

		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return list;
	}

	@Override
	public List<JSONObject> getBussinessArea(String tenantId, Integer organId) throws Exception
	{
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject(); // data数组里的对象

		StringBuilder sql = new StringBuilder("select ti.business_area_id,sd.class_item_code,sd.class_item as business_area_name from tables_info as ti ");
		sql.append(" left join sys_dictionary as sd on ti.business_area_id = sd.id and sd.class_identifier_code='business_area' where ti.valid_state = '1' and ti.organ_id = ");
		sql.append(organId + " group by business_area_id,class_item_code,business_area_name");
		sql.append(" order by ti.business_area_id asc ");
		logger.debug("getBussinessArea:::" + sql.toString());

		List<JSONObject> businessAreas = this.queryString4Json(tenantId, sql.toString());
		if (businessAreas != null)
		{
			JSONArray busiAreaArray = JSONArray.fromObject(businessAreas);
			object.element("business_area", busiAreaArray);
		}
		list.add(object);

		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return list;
	}

	@Override
	public List<JSONObject> getItemClassByTenantId(String tenantId, Integer organId, String channel) throws Exception
	{
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject(); // data数组里的对象

		StringBuilder sql1 = new StringBuilder("select hic.id,hic.chanel,hic.father_id,trim(hic.five_code) as five_code,trim(hic.phonetic_code) as phonetic_code,hic.itemclass_code,hic.itemclass_name, coalesce(himco.classorder,0) as classorder from hq_item_menu_class himc ");
		sql1.append(" left join hq_item_menu_details as himd on himc.details_id = himd.id left join hq_item_menu_organ himo on himd.item_menu_id = himo.item_menu_id left join hq_item_class hic on himc.class = hic.id  ");
		sql1.append(" left join hq_item_menu_classorder himco on himco.menu_id = himo.item_menu_id and himco.class_id = hic.id ");
		sql1.append(" where hic.valid_state = '1' and himd.valid_state='1' and himo.store_id = " + organId + " and hic.chanel = '" + channel + "' group by hic.id, himco.classorder ");

		List<JSONObject> list1 = this.queryString4Json(tenantId, sql1.toString());
		if (list1.size() > 0)
		{
			StringBuilder sql2 = new StringBuilder("select hic.id,hic.chanel,hic.father_id,trim(hic.five_code) as five_code,trim(hic.phonetic_code) as phonetic_code,hic.itemclass_code,hic.itemclass_name from hq_item_class hic where hic.valid_state='1' and hic.id in (");
			for (int i = 0; i < list1.size(); i++)
			{
				JSONObject obj = list1.get(i);
				sql2.append(obj.optInt("father_id"));
				if (i != list1.size() - 1)
				{
					sql2.append(",");
				}
			}
			sql2.append(")");
			// 去重，由于客户石碧东那边的序列重新计算，新增的大类和以前的数据有重
			List<JSONObject> list2 = this.queryString4Json(tenantId, sql2.toString());
			for (int j = 0; j < list2.size(); j++)
			{
				JSONObject obj2 = list2.get(j);
				for (int m = 0; m < list1.size(); m++)
				{
					if (obj2.optInt("id") == list1.get(m).optInt("id"))
					{
						break;
					}
					else
					{
						continue;
					}
				}
				list1.add(list2.get(j));
			}

			logger.debug("getItemClassByTenantId:::" + sql1.toString());
		}

		JSONArray itemClassArray = JSONArray.fromObject(list1);
		object.element("item_class", itemClassArray);
		list.add(object);

		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return list;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<JSONObject> getServiceFeeTypeByOrganId(String tenantId, Integer organId) throws Exception
	{
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject(); // data数组里的对象

		StringBuilder sql = new StringBuilder("select hsft.id,hsft.service_type_code,hsft.name,hsft.guding_jj,hsft.zuidi_xfe,hsft.fwfl,hsft.taken_mode,hsft.ismodify,hsft.fee_type,hsft.valid_state ");
		sql.append(" from hq_service_fee_type as hsft ");
		sql.append(" left join hq_service_fee_of_organ as hsfoo on hsft.id = hsfoo.service_fee_id ");
		sql.append(" where hsft.valid_state = '1' and hsfoo.store_id = " + organId);
		sql.append(" group by hsft.id,hsft.service_type_code,hsft.name,hsft.guding_jj,hsft.zuidi_xfe,hsft.fwfl,hsft.taken_mode,hsft.ismodify,hsft.fee_type,hsft.valid_state order by hsft.id asc ");
		logger.debug("getServiceFeeTypeByOrganId:::" + sql.toString());

		List<JSONObject> serviceFeeTypes = (List<JSONObject>) this.query(tenantId, sql.toString(), BasicDataServiceFeeType.class);
		if (serviceFeeTypes != null)
		{
			JSONArray serviceFeeArray = JSONArray.fromObject(serviceFeeTypes);
			object.element("service_type", serviceFeeArray);
		}
		list.add(object);

		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return list;
	}

	@Override
	public List<JSONObject> getItemTastes(String tenantId, Integer organId) throws Exception
	{
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject(); // data数组里的对象

		StringBuilder sql = new StringBuilder("select distinct(it.id) id,it.code,it.name,it.pinyin_sy,it.father_id,it.third_code from item_taste it left join item_taste_org ito on it.id = ito.teste_id");
		sql.append(" where (it.valid_state = '1' and ito.store_id =?) or it.father_id = 0 order by it.father_id");

		logger.debug("getItemTastes:::" + sql.toString());

		List<JSONObject> itemTastes = this.queryString4Json(tenantId, sql.toString(), new Object[]
		{ organId });
		if (itemTastes != null)
		{
			JSONArray itemTasteArray = JSONArray.fromObject(itemTastes);
			object.element("taste", itemTasteArray);
		}
		list.add(object);

		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return list;
	}

	@Override
	public List<JSONObject> getItemMethodsByTenantId(String tenantId, Integer organId) throws Exception
	{
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject(); // data数组里的对象

		StringBuilder sql = new StringBuilder("select distinct hime.id,hime.item_id,sd.class_item as method_name,hime.makeup_way,hime.proportion_money,sd.class_item_code as third_code");
		sql.append(" from hq_item_method hime");
		sql.append(" left join sys_dictionary as sd on hime.method_name_id=sd.id");
		sql.append(" left join hq_item_menu_details himd on hime.item_id=himd.item_id");
		sql.append(" left join hq_item_menu him on himd.item_menu_id = him.id");
		sql.append(" left join hq_item_menu_organ himo on him.id = himo.item_menu_id");
		sql.append(" where himo.tenancy_id=? and himo.store_id=? and him.valid_state='1' and hime.valid_state='1' and sd.class_identifier_code='method' order by hime.id asc ");

		logger.debug("getItemMethodsByTenantId:::" + sql.toString());

		List<JSONObject> itemMethods = this.queryString4Json(tenantId, sql.toString(), new Object[]
		{ tenantId, organId });
		if (itemMethods != null)
		{
			JSONArray itemMethodArray = JSONArray.fromObject(itemMethods);
			object.element("method", itemMethodArray);
		}
		list.add(object);

		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return list;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<JSONObject> getUnusualReasonByTenantId(String tenantId, Integer organId) throws Exception
	{
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject(); // data数组里的对象

		StringBuilder sql = new StringBuilder("select k.id,k.reason_code,k.unusual_type,k.father_id, k.reason_name, k.phonetic_code, k.five_code, k.remark,k.valid_state,k.third_code from ");
		sql.append(" (SELECT huur.id,huur.reason_code,huur.unusual_type,huur.father_id,huur.reason_name,huur.phonetic_code,huur.five_code,huur.remark,huur.valid_state,huur.third_code FROM hq_unusual_reason as huur where huur.father_id = 0 union ");
		sql.append(" SELECT DISTINCT(hur.id) id,hur.reason_code,hur.unusual_type,hur.father_id,hur.reason_name,hur.phonetic_code,hur.five_code,hur.remark,hur.valid_state,hur.third_code FROM hq_unusual_reason as hur left join hq_unusual_reason_org as huro on huro.unusual_reason_id = hur.id ");
		sql.append(" where hur.valid_state = '1' and huro.store_id = " + organId + " ) k order by k.father_id asc ");

		logger.debug("getUnusualReasonByTenantId:::" + sql.toString());

		List<JSONObject> reasons = (List<JSONObject>) this.query(tenantId, sql.toString(), BasicDataUnusualReason.class);
		if (reasons != null)
		{
			JSONArray reasonArray = JSONArray.fromObject(reasons);
			object.element("reason", reasonArray);
		}
		list.add(object);

		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return list;
	}

	@Override
	public List<JSONObject> getDiscountCaseByTenantId(String tenantId, Integer organId) throws Exception
	{
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject(); // data数组里的对象

		String nowDate = DateUtil.getNowDateYYDDMM();

		StringBuilder sql = new StringBuilder("select distinct hdc.id,discount_case_name,discount_case_type,rate_renate,startdate,sarttime,enddate,endtime,running_cycle,day_running_cycle,zero_norm,discount_case_code,hdc.is_vipprice ");
		sql.append(" from hq_discount_case as hdc LEFT JOIN hq_discount_case_org as hdco on hdco.discount_case_id = hdc.id ");
		sql.append(" where hdc.valid_state='1' and hdc.startdate<='" + nowDate + "' and hdc.enddate>='" + nowDate + "' and hdco.store_id = " + organId + " order by hdc.id asc ");

		SqlRowSet rs = this.query(tenantId, sql.toString());

		String week = "0" + String.valueOf(DateUtil.dayForWeek(nowDate));

		String dayOfMonth = StringUtils.leftPad("" + Calendar.getInstance().get(Calendar.DAY_OF_MONTH), 2, "0");

		List<BasicDataDiscountCase> discountCaseList = new ArrayList<BasicDataDiscountCase>();

		String runningCycle = null;
		String dayRunnintCycle = null;

		List<String> weeks = null;
		List<String> days = null;

		while (rs.next())
		{
			runningCycle = rs.getString("running_cycle");
			dayRunnintCycle = rs.getString("day_running_cycle");
			BasicDataDiscountCase discountCase = new BasicDataDiscountCase();
			if (StringUtils.isNotEmpty(runningCycle))
			{
				weeks = Arrays.asList(runningCycle.split(","));
			}
			if (StringUtils.isNotEmpty(dayRunnintCycle))
			{
				days = Arrays.asList(dayRunnintCycle.split(","));
			}

			if ((null != weeks && weeks.contains(week)) || (null != days && days.contains(dayOfMonth)))
			{
				discountCase.setId(rs.getInt("id"));
				discountCase.setDiscount_case_name(rs.getString("discount_case_name"));
				discountCase.setDiscount_case_type(rs.getString("discount_case_type"));
				discountCase.setRate_renate(rs.getDouble("rate_renate"));
				discountCase.setStartdate(rs.getString("startdate"));
				discountCase.setSarttime(rs.getString("sarttime"));
				discountCase.setEnddate(rs.getString("enddate"));
				discountCase.setEndtime(rs.getString("endtime"));
				discountCase.setRunning_cycle(runningCycle);
				discountCase.setDay_running_cycle(dayRunnintCycle);
				discountCase.setZero_norm(rs.getDouble("zero_norm"));
				discountCase.setDiscount_case_code(rs.getString("discount_case_code"));
				discountCase.setIs_vipprice(rs.getString("is_vipprice"));
				discountCaseList.add(discountCase);
			}
		}
		object.element("discountcase", discountCaseList);
		list.add(object);

		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return list;
	}

	@Override
	public Data getDiscountCaseDetailsByTenantId(String tenantId, Integer organId,Pagination pagination) throws Exception
	{
		Data data = new Data();
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject(); // data数组里的对象

		if (pagination == null)
		{
			pagination = new Pagination();
		}

		StringBuilder sql = new StringBuilder("select hdcd.discount_case_id,hdcd.id,hdcd.item_id,hdcd.unit,rate,hdcd.derate,hdcd.item_unit_id from hq_discount_case_details as hdcd ");
		sql.append(" LEFT JOIN hq_discount_case_org as hdco on hdco.discount_case_id = hdcd.discount_case_id  ");
		sql.append(" where hdco.store_id = " + organId + " group by hdcd.discount_case_id,hdcd.id,hdcd.item_id,hdcd.unit,rate,hdcd.derate order by hdcd.id asc ");
		int totalCount = 0;
		SqlRowSet rs = this.jdbcTemplate.queryForRowSet("select count(*) as count from ("+sql.toString()+") t");
		while (rs.next())
		{
			totalCount = rs.getInt("count");
		}
		if (pagination != null && pagination.getPagesize() > 0)
		{
			if ((pagination.getPageno()-1) < 1)
			{
				sql.append(" limit " + pagination.getPagesize() + " OFFSET " + 0);
			}else
			{
				sql.append(" limit " + pagination.getPagesize() + " OFFSET " + (pagination.getPageno()-1)*pagination.getPagesize());
			}
		}

		logger.debug("getDiscountCaseDetailsByTenantId:::" + sql.toString());
		List<JSONObject> discountCaseDetails = this.queryString4Json(tenantId, sql.toString());
		if (discountCaseDetails != null)
		{
			JSONArray discountDetailArray = JSONArray.fromObject(discountCaseDetails);
			object.element("discount_detail", discountDetailArray);
		}
		pagination.setPageno(pagination.getPageno() > 0 ? pagination.getPageno() : 1);
		pagination.setTotalcount(totalCount);

		if (pagination.getPagesize() == 0)
		{
			pagination.setPagesize(discountCaseDetails.size());
		}

		list.add(object);

		data.setPagination(pagination);
		data.setData(list);

		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return data;

	}

	@Override
	public List<JSONObject> getPaymentWayByOrganId(String tenantId, Integer organId) throws Exception
	{
		StringBuilder sql = new StringBuilder(
				"select pw2.id,coalesce(sd.class_item,pw2.payment_name1) payment_name1,pw2.payment_name2,pw2.is_standard_money,pw2.payment_class,pw2.if_prepay,pw2.is_check,pw2.is_recharge,pw2.rate,pw2.third_code, coalesce(pw2.is_open_cashbox, 1) as is_open_cashbox from payment_way as pw2");
		sql.append(" left join payment_way_of_ogran as pwoo2 on pw2.id = pwoo2.payment_id left join sys_dictionary sd on pw2.payment_name1 = sd.class_item_code and sd.class_identifier_code='currency' and pw2.payment_class='cash'");
		sql.append(" where pw2.status = '1' and pwoo2.organ_id = ").append(organId);

		logger.debug("getPaymentWayByOrganId:::" + sql.toString());
		List<JSONObject> paymentWays = this.queryString4Json(tenantId, sql.toString());

		JSONObject object = new JSONObject(); // data数组里的对象
		if (paymentWays != null)
		{
			JSONArray paymentWayArray = JSONArray.fromObject(paymentWays);
			object.element("payment_way", paymentWayArray);
		}

		List<JSONObject> list = new ArrayList<JSONObject>();
		list.add(object);

		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return list;
	}

	@Override
	public List<JSONObject> getPaymentShowtypeByOrganId(String tenantId, Integer organId) throws Exception
	{
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject(); // data数组里的对象

		StringBuilder sql = new StringBuilder("select ps.* from hq_payment_showtype as ps left join payment_way_of_ogran as po on ps.tenancy_id=po.tenancy_id and ps.store_id=po.organ_id and ps.payment_id=po.payment_id left join payment_way as pw on  po.payment_id=pw.id");
		sql.append(" where po.tenancy_id='").append(tenantId).append("' and po.organ_id=").append(organId).append(" and pw.status='1'");
		logger.debug("getPaymentWayByOrganId:::" + sql.toString());
		List<JSONObject> paymentWays = this.queryString4Json(tenantId, sql.toString());
		if (paymentWays != null)
		{
			JSONArray paymentWayArray = JSONArray.fromObject(paymentWays);
			object.element("hq_payment_showtype", paymentWayArray);
		}
		list.add(object);

		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return list;
	}

	@Override
	public Data getDishByTenantId(String tenantId, Integer organId, String channel, Pagination pagination) throws Exception
	{
		Data data = new Data();
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject(); // data数组里的对象

		if (pagination == null)
		{
			pagination = new Pagination();
		}

		StringBuilder str = new StringBuilder(
				"select distinct himd.item_menu_id,himd.id as details_id,himd.item_id,(case when himd.menu_item_rank is null then 0 when himd.menu_item_rank = '' then 0 else cast(himd.menu_item_rank as int) end) as menu_item_rank,hii.item_code as item_no,himc.item_name,himc.item_english, himc.phonetic_code, himc.five_code,");
		str.append("coalesce(hip.price,hiu.standard_price) as price,hiu.id as unit_id,hiu.unit_name,himd.starttime,himd.endtime,coalesce(himd.is_show,1) as is_show,himd.valid_state,hic.id as item_class_id,");
		str.append("hic.itemclass_code,hii.item_barcode,hii.is_discount,hii.is_pushmoney,hii.pushmoney_way,hii.proportion_money as proportion,hii.is_modifyquantity,hii.third_code,hii.is_turnover,hii.is_new,");
		str.append("hii.is_modifyname,hii.is_runningprice,hii.is_staffmeal,hii.is_throwaway,hii.is_seafood,hii.is_staplefood,hii.is_combo,hii.is_characteristic,hii.remark,hii.entry_person,hii.entry_time,");
		str.append("hii.is_recommendation,hii.to_offer,hii.combo_type,hii.nutrition,hii.suitable_crowds,hii.unsuitable_crowds,hii.processing_technic,hii.photo1,hii.photo2,hii.photo3,hii.photo4,hii.photo5,hii.photo6,hii.summary,hii.spicy,hii.is_points,hii.item_description,coalesce(civs.vip_price, civ.vip_price) as vip_price,hii.is_assemble_combo ");
		str.append(" from hq_item_info as hii ");
		str.append(" left join hq_item_menu_details as himd on hii.id = himd.item_id");
		str.append(" left join hq_item_menu him on himd.item_menu_id = him.id");
		str.append(" left join hq_item_menu_organ as himo on him.id = himo.item_menu_id");
		str.append(" left join organ o on himo.store_id = o.id");
		str.append(" left join hq_item_menu_class as himc on himc.details_id = himd.id");
		str.append(" left join hq_item_class as hic on himc.class = hic.id");
		str.append(" left join hq_item_unit as hiu on hii.id = hiu.item_id  and hiu.is_default = 'Y' and hiu.valid_state = '1'");
		str.append(" left join crm_item_vip civ on hiu.item_id = civ.item_id and hiu.id = civ.unit_id");
		str.append(" left join crm_item_vip_sysprice civs on civ.unit_id = civs.unit_id and civs.chanel = himc.chanel and civs.price_system::text = o.price_system");
		str.append(" left join hq_item_pricesystem hip on hiu.id=hip.item_unit_id and hip.chanel=himc.chanel and hip.price_system::text =o.price_system");
		str.append(" where hiu.id is not null and hii.valid_state = '1' and him.valid_state = '1' and himc.chanel='" + channel + "' and himo.store_id = " + organId);

		int totalCount = 0;
		SqlRowSet rs = this.jdbcTemplate.queryForRowSet("select count(*) as count from (" + str.toString() + ") t");
		while (rs.next())
		{
			totalCount = rs.getInt("count");
		}

		str.append(" order by himd.item_id asc ");

		if (pagination != null && pagination.getPagesize() > 0)
		{
			if ((pagination.getPageno() - 1) < 1)
			{
				str.append(" limit " + pagination.getPagesize() + " OFFSET " + 0);
			}
			else
			{
				str.append(" limit " + pagination.getPagesize() + " OFFSET " + (pagination.getPageno() - 1) * pagination.getPagesize());
			}
		}

		logger.debug("getDishByTenantId:::" + str.toString());
		List<BasicDataDish> dishes = this.jdbcTemplate.query(str.toString(), BeanPropertyRowMapper.newInstance(BasicDataDish.class));
		if (dishes != null)
		{
			JSONArray itemMenuDetailArray = JSONArray.fromObject(dishes);
			object.element("dish", itemMenuDetailArray);
		}
		pagination.setPageno(pagination.getPageno() > 0 ? pagination.getPageno() : 1);
		pagination.setTotalcount(totalCount);

		if (pagination.getPagesize() == 0)
		{
			pagination.setPagesize(dishes.size());
		}

		list.add(object);

		data.setPagination(pagination);
		data.setData(list);

		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return data;
	}

	@Override
	public Data getItemUnitsByTenantId(String tenantId, Integer organId, String channel, Pagination pagination) throws Exception
	{
		Data data = new Data();
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject();
		if (pagination == null)
		{
			pagination = new Pagination();
		}
		StringBuilder str = new StringBuilder("select distinct hiu.id,hiu.item_id,hiu.unit_name,hiu.is_default,coalesce(hip.price,hiu.standard_price) as standard_price,hiu.combo_display_state,hiu.count_rate");
		str.append(" from hq_item_unit as hiu");
		str.append(" left join hq_item_menu_details as himd on himd.item_id = hiu.item_id");
		str.append(" left join hq_item_menu_class as himc on himc.details_id = himd.id");
		str.append(" left join hq_item_menu_organ as himo on himo.item_menu_id = himd.item_menu_id");
		str.append(" left join organ o on himo.store_id = o.id");
		str.append(" left join hq_item_pricesystem hip on hiu.id=hip.item_unit_id and hip.chanel=himc.chanel and hip.price_system||'' =o.price_system");
		str.append(" left join crm_item_vip_sysprice iv on iv.unit_id=hiu.id and iv.chanel=himc.chanel and iv.price_system||'' =o.price_system");
		str.append(" where hiu.valid_state='1' and himo.store_id = " + organId + " and himc.chanel= '" + channel + "' ");

		int totalCount = 0;
		SqlRowSet rs = this.jdbcTemplate.queryForRowSet("select count(*) from (" + str.toString() + ") t");
		while (rs.next())
		{
			totalCount = rs.getInt("count");
		}

		str.append(" order by hiu.id asc ");
		logger.debug("getItemUnitsByTenantId:::" + str.toString());

		if (pagination != null && pagination.getPagesize() > 0)
		{
			str.append(" limit " + pagination.getPagesize() + " OFFSET " + (pagination.getPageno() - 1) * pagination.getPagesize());
		}

		List<JSONObject> itemUnits = this.queryString4Json(tenantId, str.toString());
		JSONArray itemUnitArray = JSONArray.fromObject(itemUnits);
		object.element("unit", itemUnitArray);
		list.add(object);
		pagination.setTotalcount(totalCount);

		if (pagination.getPagesize() == 0)
		{
			pagination.setPagesize(itemUnits.size());
		}

		data.setPagination(pagination);
		data.setData(list);

		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return data;
	}

	@Override
	public List<JSONObject> getTablesByTenantIdAndOrganId(String tenantId, Integer organId) throws Exception
	{
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject();

		StringBuilder sql = new StringBuilder("select id,business_area_id,table_code,table_name,table_property_id,seat_counts,floor,fwfz_id,table_fw,is_cyfq,free_icon,use_icon,danzhuo_yy from tables_info ");
		sql.append(" where valid_state='1' and organ_id = " + organId + " order by id asc ");
		List<PosTableInfo> tableInfos = this.jdbcTemplate.query(sql.toString(), BeanPropertyRowMapper.newInstance(PosTableInfo.class));

		logger.debug("getTablesByTenantIdAndOrganId:::" + sql.toString());

		if (tableInfos != null)
		{
			JSONArray tablesArray = JSONArray.fromObject(tableInfos);
			object.element("table", tablesArray);
		}
		list.add(object);

		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return list;
	}

	@Override
	public List<JSONObject> getTimePriceByOrganId(String tenantId, Integer organId, String channel) throws Exception
	{
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject();

		StringBuilder sql = new StringBuilder("select htp.id,htp.time_price_name,htp.remark,htp.startdate,htp.sarttime,htp.enddate,htp.endtime,htp.running_cycle,htp.zero_norm,htp.running_status,htp.valid_state,htp.last_updatetime,htp.stop_person,htp.stop_time from hq_time_price as htp ");
		sql.append(" left JOIN hq_time_price_org as htpo ON htpo.time_price_id = htp.id where htp.valid_state='1' and htpo.store_id = " + organId);
		sql.append(" group by htp.id,htp.time_price_name,htp.remark,htp.startdate,htp.sarttime,htp.enddate,htp.endtime,htp.running_cycle,htp.zero_norm,htp.running_status,htp.valid_state,htp.last_updatetime,htp.stop_person,htp.stop_time order by htp.id asc ");

		logger.debug("getTimePriceByOrganId:::" + sql.toString());
		List<JSONObject> timePrices = this.queryString4Json(tenantId, sql.toString());

		if (timePrices != null)
		{
			JSONArray pricesArray = JSONArray.fromObject(timePrices);
			object.element("timeprice", pricesArray);
		}

		list.add(object);

		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return list;
	}

	@Override
	public List<JSONObject> getTimePriceItemByOrganId(String tenantId, Integer organId, String channel) throws Exception
	{
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject();

		StringBuilder sql = new StringBuilder();
		sql.append(" select distinct htpd.id,htpd.time_price_id,htpd.item_id,coalesce(htpp.price,htpd.afterprice) as afterprice,htpd.item_unit_id,htpd.remark");
		sql.append(" from hq_time_price_details as htpd");
		sql.append(" left join hq_time_price_org as htpo on htpo.time_price_id = htpd.time_price_id");
		sql.append(" left join organ o on o.id=htpo.store_id");
		sql.append(" left join hq_time_price_pricesystem htpp on htpp.time_price_details_iid=htpd.id and htpp.price_system||''=o.price_system");
		sql.append(" where htpd.valid_state='1' and htpo.store_id = ").append(organId).append(" and htpp.chanel='").append(channel).append("' order by htpd.id asc");

		logger.debug("getTimePriceItemByOrganId:::" + sql.toString());

		List<JSONObject> timePriceItems = this.queryString4Json(tenantId, sql.toString());
		if (timePriceItems != null)
		{
			JSONArray priceItemArray = JSONArray.fromObject(timePriceItems);
			object.element("timeprice_item", priceItemArray);
		}

		list.add(object);

		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return list;
	}

	@Override
	public List<JSONObject> getItemComboDetailsByOrganId(String tenantId, Integer organId, String channel) throws Exception
	{
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject();

		StringBuilder sql = new StringBuilder();
		sql.append(" select distinct hicd.id,hicd.iitem_id,hicd.is_itemgroup,hicd.details_id,hicd.combo_num,coalesce(hicp.price,hicd.standardprice) as standardprice,coalesce(hicd.combo_order,0) as order,hicd.valid_state,hicd.item_unit_id,coalesce(hicd.change_num_state,'N') as change_num_state ");
		sql.append(" from hq_item_combo_details as hicd");
		sql.append(" left join hq_item_combo_pricesystem as hicp on hicp.combo_details_id = hicd.id");
		sql.append(" left join organ o on o.price_system=hicp.price_system||''");
		sql.append(" where hicd.valid_state='1' and hicp.chanel = '").append(channel).append("' and o.id=").append(organId).append(" order by hicd.id asc ");

		logger.debug("getItemComboDetailsByOrganId:::" + sql.toString());

		List<JSONObject> combo_details = this.queryString4Json(tenantId, sql.toString());
		if (combo_details != null)
		{
			JSONArray comboDetailsArray = JSONArray.fromObject(combo_details);
			object.element("combo_details", comboDetailsArray);
		}

		list.add(object);

		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return list;
	}

	@Override
	public List<JSONObject> getItemGroupDetailsByOrganId(String tenantId, Integer organId, String channel) throws Exception
	{
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject();

		StringBuilder sql = new StringBuilder("select higd.id,higd.item_group_id,higd.item_id,higd.isdefault,higd.makeup_money,higd.quantity_limit,higd.item_unit_id,coalesce(higd.details_order,0) as details_order from hq_item_group_details as higd ");
		sql.append(" left join hq_item_group_pricesystem as higp on higp.item_group_iid = higd.item_group_id ");
		sql.append(" where higp.chanel = '" + channel + "' group by higd.id,higd.item_group_id,higd.item_id,higd.isdefault,higd.makeup_money,higd.quantity_limit,higd.item_unit_id order by higd.id asc ");

		logger.debug("getItemGroupDetailsByOrganId:::" + sql.toString());
		List<JSONObject> item_group_details = this.queryString4Json(tenantId, sql.toString());
		if (item_group_details != null)
		{
			JSONArray groupDetailsArray = JSONArray.fromObject(item_group_details);
			object.element("item_group_details", groupDetailsArray);
		}

		list.add(object);

		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return list;
	}

	// 会员卡种
	@Override
	public List<JSONObject> getCrmCardClassByOrganId(String tenantId, Integer organId) throws Exception
	{
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject();

		StringBuilder sql = new StringBuilder("select tenancy_id,id,code,name,is_only,is_back,is_lostreport,is_namereport,valid_state,remark,last_operator,last_updatetime,card_sale_price,card_deposit,is_physical_card,limit_prestore_per from crm_card_class where tenancy_id='").append(tenantId)
				.append("' and valid_state='1'");

		logger.debug("getCrmCardClassByOrganId:::" + sql.toString());

		List<JSONObject> crm_card_class = this.queryString4Json(tenantId, sql.toString());
		if (crm_card_class != null)
		{
			JSONArray crmCardClassArray = JSONArray.fromObject(crm_card_class);
			object.element("crm_card_class", crmCardClassArray);
		}

		list.add(object);
		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return list;
	}

	@Override
	public List<JSONObject> getUsersByTenantId(String tenantId, Integer organId) throws Exception
	{
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject();

		StringBuilder sql = new StringBuilder("select employee_id,user_name,user_type,name as employee_name,password,remark,a.roles_id from user_authority a left join employee b on a.employee_id=b.id and a.tenancy_id=b.tenancy_id and a.store_id=b.store_id ");
		sql.append(" where a.valid_state='1' and a.store_id=? and a.tenancy_id=? and a.user_name<>'' ");

		logger.debug("getUsersByTenantId:::" + sql.toString());

		List<JSONObject> users = this.queryString4Json(tenantId, sql.toString(), new Object[]
		{ organId, tenantId });
		if (users != null)
		{
			JSONArray usersArray = JSONArray.fromObject(users);
			object.element("user", usersArray);
		}

		list.add(object);

		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return list;
	}

	@Override
	public List<JSONObject> getSysParameter(String tenantId, Integer organId) throws Exception
	{
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject obj = new JSONObject();
		StringBuilder sql = new StringBuilder("select DISTINCT para_name,para_code,para_value,system_name from sys_parameter where (system_name='CRM' or system_name='POS' or system_name='hq') and (store_id = 0 or store_id = ?) and valid_state = '1'");
		logger.debug("getSysParameter--sql:::" + sql.toString());
		List<PosSysParam> params = this.jdbcTemplate.query(sql.toString(), new Object[]
		{ organId }, BeanPropertyRowMapper.newInstance(PosSysParam.class));
		if (params != null)
		{
			/** pos端建库需要,没有实际意义 */
			PosSysParam temp = new PosSysParam();
			temp.setPara_name("8ef9e8f294abb8a54c1a8396a04d52d1");
			temp.setPara_code("8ef9e8f294abb8a54c1a8396a04d52d1");
			temp.setPara_value("8ef9e8f294abb8a54c1a8396a04d52d1");
			temp.setSystem_name("8ef9e8f294abb8a54c1a8396a04d52d1");
			params.add(0, temp);

			JSONArray paramsArray = JSONArray.fromObject(params);
			obj.element("sys_parameter", paramsArray);
		}
		list.add(obj);
		logger.debug("同步数据，查询返回数据:--getSysParameter:::" + obj.toString());
		return list;
	}

	@Override
	public List<JSONObject> getDevicesByOrgan(String tenantId, Integer organId) throws Exception
	{
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject obj = new JSONObject();

		StringBuilder sql = new StringBuilder(
				"select id,business_area,devices_code,devices_name,devices_ip,show_type,show_port,remark,authorize_code,devices_properties, is_site as issite, (case when is_site = '1'  and ( select max(is_open_cashbox) from hq_printer_new box where box.tenancy_id = dev.tenancy_id and box.store_id = dev.store_id and box.site_id = dev.id ) = '1' then '1' else '0' end) is_site from hq_devices AS dev where valid_state = '1' and store_id = ? ");
		List<BasicDevice> devices = this.jdbcTemplate.query(sql.toString(), new Object[]
		{ organId }, BeanPropertyRowMapper.newInstance(BasicDevice.class));
		if (devices != null)
		{
			JSONArray paramsArray = JSONArray.fromObject(devices);
			obj.element("devices", paramsArray);
		}
		logger.debug("同步数据，查询返回数据:--getDevices:::" + obj.toString());
		list.add(obj);
		return list;
	}

	@Override
	public List<JSONObject> getUserDiscountCaseByOrgan(String tenantId, Integer organId) throws Exception
	{
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject obj = new JSONObject();

		StringBuilder sql = new StringBuilder(
				"select dc.id,dc.employee_id,dc.discount_id,dc.discount_authority_id,da.discount_type from user_discount_case dc left join user_discount_authority da on dc.tenancy_id=da.tenancy_id and dc.discount_authority_id=da.id and dc.employee_id=da.employee_id where da.tenancy_id=?");
		List<BasicDataUserDiscountCase> devices = this.jdbcTemplate.query(sql.toString(), new Object[]
		{ tenantId }, BeanPropertyRowMapper.newInstance(BasicDataUserDiscountCase.class));
		if (devices != null)
		{
			JSONArray paramsArray = JSONArray.fromObject(devices);
			obj.element("user_discount_case", paramsArray);
		}
		logger.debug("同步数据，查询返回数据:--getUserDiscountCaseByOrgan:::" + obj.toString());
		list.add(obj);
		return list;
	}

	@Override
	public List<JSONObject> getUserDiscountAuthorityByOrgan(String tenantId, Integer organId) throws Exception
	{
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject obj = new JSONObject();

		StringBuilder sql = new StringBuilder("select id,employee_id,user_id,fixed_discount_limit,fall_discount_limit,single_discount_limit,is_discount_case,discount_type from user_discount_authority where tenancy_id=? and (store_id =? or store_id is NULL)");
		List<BasicDataUserDiscountAuthority> devices = this.jdbcTemplate.query(sql.toString(), new Object[]
		{ tenantId, organId }, BeanPropertyRowMapper.newInstance(BasicDataUserDiscountAuthority.class));
		if (devices != null)
		{
			JSONArray paramsArray = JSONArray.fromObject(devices);
			obj.element("user_discount_authority", paramsArray);
		}
		logger.debug("同步数据，查询返回数据:--getUserDiscountAuthorityByOrgan:::" + obj.toString());
		list.add(obj);
		return list;
	}

	@Override
	public List<JSONObject> getCrmInCorporations(String tenantId, Integer organId) throws Exception
	{
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject();

		StringBuilder sql = new StringBuilder("select cii.id,cii.name,cii.code,cii.phone,cii.address,cii.credit_limit,cii.discount,cii.phonetic_code,cii.five_code,cii.main_balance,cii.reward_balance,cii.arrears,cii.derate_money from crm_incorporation_info cii ");
		sql.append(" left join crm_incorporation_org cio on cii.id = cio.incorporation_id where cio.store_id = " + organId);
		sql.append(" order by cii.id desc");

		logger.debug("getCrmInCorporations:::" + sql.toString());

		List<JSONObject> corporations = this.queryString4Json(tenantId, sql.toString());
		if (corporations != null)
		{
			JSONArray corporationsArray = JSONArray.fromObject(corporations);
			object.element("crm_incorporation_info", corporationsArray);
		}

		list.add(object);

		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return list;
	}

	@Override
	public List<JSONObject> getCrmInCorporationPersons(String tenantId, Integer organId) throws Exception
	{
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject();

		StringBuilder sql = new StringBuilder("select cip.incorporation_id,cip.autograph,cip.customer_id from crm_incorporation_person cip ");
		sql.append(" left join crm_incorporation_org cio on cip.incorporation_id = cio.incorporation_id where cio.store_id = " + organId);
		sql.append(" order by cip.incorporation_id desc ");

		logger.debug("getCrmInCorporationPersons:::" + sql.toString());

		List<JSONObject> corpPersons = this.queryString4Json(tenantId, sql.toString());
		if (corpPersons != null)
		{
			JSONArray corpPersonsArray = JSONArray.fromObject(corpPersons);
			object.element("crm_incorporation_person", corpPersonsArray);
		}

		list.add(object);

		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return list;
	}

	@Override
	public List<JSONObject> getItemGroup(String tenancyId, int storeId) throws Exception
	{
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject obj = new JSONObject();

		StringBuilder sql = new StringBuilder("select id,item_group_code,item_group_name,five_code,phonetic_code,item_group_price,remark from hq_item_group where valid_state = '1' and tenancy_id = ?");
		List<ItemGroup> itemGroup = this.jdbcTemplate.query(sql.toString(), new Object[]
		{ tenancyId }, BeanPropertyRowMapper.newInstance(ItemGroup.class));
		if (itemGroup != null)
		{
			JSONArray paramsArray = JSONArray.fromObject(itemGroup);
			obj.element("item_group", paramsArray);
		}
		logger.debug("同步数据，查询返回数据:--getDevices:::" + obj.toString());
		list.add(obj);
		return list;
	}

	@Override
	public List<JSONObject> getBohImgByPcOrPad(String tenantId, Integer organId) throws Exception
	{
		StringBuilder sb = new StringBuilder("select id,terminal_type,img_type,img_addr,img_index,remark,last_updatetime from boh_img_padpc where tenancy_id=? and organ_id=?");
		Object[] objs = new Object[]
		{ tenantId, organId };
		JSONArray jsonList = this.query4JSONArray(tenantId, sb.toString(), objs);
		List<JSONObject> jsonObjects = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject();
		object.put("boh_img_padpc", jsonList);
		jsonObjects.add(object);
		return jsonObjects;
	}

	@Override
	public List<JSONObject> getItemSoldout(String tenantId, Integer organId) throws Exception
	{
		StringBuilder sql = new StringBuilder(" SELECT id, item_id, num, item_unit_id from pos_soldout where tenancy_id = ? and store_id = ? ");
		Object[] obj = new Object[]
		{ tenantId, organId };
		List<JSONObject> jsons = this.query4Json(tenantId, sql.toString(), obj);
		JSONObject json = new JSONObject();
		json.put("soldout", jsons);
		List<JSONObject> jsonsList = new ArrayList<JSONObject>();
		jsonsList.add(json);
		return jsonsList;
	}

	@Override
	public List<JSONObject> getItemWorrysale(String tenantId, Integer organId) throws Exception
	{
		StringBuilder sql = new StringBuilder(" SELECT id, item_id, item_unit_id, num from pos_item_worrysale where tenancy_id = ? and store_id = ?");
		Object[] obj = new Object[]
		{ tenantId, organId };
		List<JSONObject> jsons = this.query4Json(tenantId, sql.toString(), obj);
		JSONObject json = new JSONObject();
		json.put("worrysale", jsons);
		List<JSONObject> jsonList = new ArrayList<>();
		jsonList.add(json);
		return jsonList;
	}

	@Override
	public List<JSONObject> getDishFormat(String tenantId, Integer organId, String channel) throws Exception
	{
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject();

		StringBuilder sql = new StringBuilder();
		sql.append(" select d.item_class_id, d.page, d.show_count from hq_pad_item_show s ");
		sql.append(" inner join hq_pad_item_show_detail d on s.id = d.show_id ");
		sql.append(" inner join (select hic.id from hq_item_class hic left join hq_item_menu_class himc on himc.class = hic.id ");
		sql.append(" left join hq_item_menu_details himd on himc.details_id = himd.id ");
		sql.append(" left join hq_item_menu_organ himo on himd.item_menu_id = himo.item_menu_id ");
		sql.append(" where hic.chanel = '" + channel + "' and hic.valid_state = '1' and himc.chanel = '" + channel + "' ");
		sql.append(" and himd.valid_state = '1' and himo.store_id = " + organId + " and himo.tenancy_id = '" + tenantId + "' group by hic.id) t on d.item_class_id = t.id ");
		sql.append(" where s.valid_state = '1' ");

		logger.debug("getDishFormat:::" + sql.toString());

		List<JSONObject> dishFormats = this.queryString4Json(tenantId, sql.toString());
		if (dishFormats != null)
		{
			JSONArray dishFormatArray = JSONArray.fromObject(dishFormats);
			object.element("dish_format", dishFormatArray);
		}

		list.add(object);

		logger.debug("同步数据，菜品格式返回数据:---" + object.toString());
		return list;
	}

	@Override
	public List<JSONObject> getActivityRuleDetails(String tenancyId) throws Exception
	{
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject(); // data数组里的对象
		StringBuilder sql = new StringBuilder(
				"SELECT car.item_unit_id,car.tenancy_id,car. ID,car.activity_id,car.reach_amount,car.discount_amount,COALESCE(car.item_id,0) as item_id,COALESCE(car.item_num,0) as item_num,ca.subject,ca.activity_type,cas.if_loop,cas.if_parallel,cas.bill_limit_times,cas.if_auto,cas.if_all_item FROM "
						+ "crm_activity_rule car,crm_activity ca,crm_activity_sub_t cas WHERE	ca. ID = car.activity_id AND cas.activity_id = ca. ID AND car.tenancy_id = ?");
		logger.debug("getActivityRuleDetails:::" + sql.toString());
		List<JSONObject> brandList = this.queryString4Json(tenancyId, sql.toString(), new Object[]
		{ tenancyId });
		if (brandList != null)
		{
			JSONArray activityRuleArray = JSONArray.fromObject(brandList);
			object.element("crm_activity_rule", activityRuleArray);
		}
		list.add(object);

		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return list;
	}

	@Override
	public List<JSONObject> getCrmActivityItem(String tenancyId) throws Exception
	{
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject(); // data数组里的对象
		StringBuilder sql = new StringBuilder("select cai.item_unit_id, cai.tenancy_id,cai.id,cai.activity_id,cai.item_type,cai.item_id,cai.rule_code,cai.item_num from crm_activity_item cai where tenancy_id=?");
		logger.debug("getCrmActivityItem:::" + sql.toString());
		List<JSONObject> brandList = this.queryString4Json(tenancyId, sql.toString(), new Object[]
		{ tenancyId });
		if (brandList != null)
		{
			JSONArray activityRuleArray = JSONArray.fromObject(brandList);
			object.element("crm_activity_item", activityRuleArray);
		}
		list.add(object);

		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return list;
	}

	/**
	 * 同步数据字典
	 */
	public List<JSONObject> getSysDictionary(String tenancyId) throws Exception
	{
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject(); // data数组里的对象
		StringBuilder sql = new StringBuilder("select t.tenancy_id,t.id,t.model_name,t.application_model,t.class_identifier,t.class_identifier_code ");
		sql.append(" ,class_item,class_item_code,is_sys,remark,valid_state,last_operator,last_updatetime");
		sql.append(" from sys_dictionary t where t.tenancy_id=?  and class_identifier_code in ('pos_type','dinner_type','sales_model','deliver_type') and t.valid_state='1'");
		logger.debug("getSysDictionary:::" + sql.toString());
		List<JSONObject> authorizationList = this.queryString4Json(tenancyId, sql.toString(), new Object[]
		{ tenancyId });
		if (authorizationList != null)
		{
			JSONArray sysDictionary = JSONArray.fromObject(authorizationList);
			object.element("sys_dictionary", sysDictionary);
		}
		list.add(object);
		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return list;
	}

	/**
	 * E待客同步数据字典
	 */
	public List<JSONObject> getAndroidSysDictionary(String tenancyId) throws Exception
	{
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject(); // data数组里的对象
		StringBuilder sql = new StringBuilder("select t.tenancy_id,t.id,t.model_name,t.application_model,t.class_identifier,t.class_identifier_code ");
		sql.append(" ,class_item,class_item_code,is_sys,remark,valid_state,last_operator,last_updatetime");
		sql.append(" from sys_dictionary t where t.tenancy_id=? and class_identifier_code = 'bank'");
		logger.debug("getAndroidSysDictionary:::" + sql.toString());
		List<JSONObject> authorizationList = this.queryString4Json(tenancyId, sql.toString(), new Object[]
		{ tenancyId });
		if (authorizationList != null)
		{
			JSONArray sysDictionary = JSONArray.fromObject(authorizationList);
			object.element("android_sys_dictionary", sysDictionary);
		}
		list.add(object);
		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return list;
	}

	@Override
	public List<JSONObject> getMenuButtonConfig(String tenantId, Integer organId) throws Exception
	{
		String buttonConfigSql = "select c.business_id, c.type, c.display_order, (select pw.payment_class from payment_way pw where cast(pw.id as varchar) = c.business_id and c.type='3') as payment_class from hq_device_button_config c where c.tenancy_id = ? and c.store_id = ? order by c.type asc ";

		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject obj = new JSONObject();
		List<JSONObject> configList = this.query4Json(tenantId, buttonConfigSql, new Object[]
		{ tenantId, organId });
		if (configList != null)
		{
			JSONArray configInfos = JSONArray.fromObject(configList);
			obj.element("menu_button_config", configInfos);
		}

		list.add(obj);
		logger.debug("----菜单按钮配置信息----" + obj.toString());

		return list;
	}

	@Override
	public List<JSONObject> getPaymentWayButtonConfig(String tenantId, Integer organId) throws Exception
	{

		String buttonConfigSql = "SELECT tenancy_id, id, payment_id, organ_id, sorting_type, sorting_code, create_time FROM pos_payment_way_sorting WHERE tenancy_id=? AND organ_id=? ";

		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject obj = new JSONObject();
		List<JSONObject> configList = this.query4Json(tenantId, buttonConfigSql, new Object[]
		{ tenantId, organId });
		if (configList != null)
		{
			JSONArray configInfos = JSONArray.fromObject(configList);
			obj.element("paymentway_button_config", configInfos);
		}

		list.add(obj);
		logger.debug("----支付按钮配置信息----" + obj.toString());

		return list;
	}

	@Override
	public List<JSONObject> getDiningType(String tenantId, Integer organId) throws Exception
	{
		String diningTypeSql = "select  hdt.id,hdt.dining_code,hdt.dining_name,COALESCE(hdt.order_num,'0') order_num,hdt.remark,hdt.valid_state,hdt.is_open from hq_dining_type hdt LEFT JOIN hq_dining_type_organ hdto on hdt.tenancy_id = hdto.tenancy_id AND hdt.id = hdto.dining_type_id where hdt.tenancy_id = ? and hdto.store_id = ? and hdt.valid_state = '1' order by hdt.dining_code asc";
		List<JSONObject> list = new ArrayList<JSONObject>();
            JSONObject obj = new JSONObject();
		List<JSONObject> configList = this.query4Json(tenantId, diningTypeSql, new Object[]
		{ tenantId, organId });
		if (configList != null)
		{
			JSONArray configInfos = JSONArray.fromObject(configList);
			obj.element("dining_type", configInfos);
		}

		list.add(obj);
		logger.debug("----就餐类型信息----" + obj.toString());

		return list;
	}

	@Override
	public List<JSONObject> getHqItemTasteDetails(String tenantId, Integer organId) throws Exception
	{
		String sql = "select id,id as tid,item_id,taste_id,taste_code,taste_name,last_operator,last_updatetime from hq_item_taste_details";
		List<JSONObject> jsonList = this.queryString4Json(tenantId, sql);
		JSONObject json = new JSONObject();
		json.put("item_taste", jsonList);
		return Arrays.asList(json);
	}



	public List<JSONObject> getPosForbiddenItem(String tenantId, Integer organId) throws Exception{
		String querySql = "SELECT  item_id,unit_id,opt_num,updatetime from pos_forbidden_item where tenancy_id='"+tenantId+"' and store_id="+organId;
		List<JSONObject> jsonList = this.query4Json(tenantId, querySql);
		JSONObject json = new JSONObject();
		json.put("forbidden_item", jsonList);
		return Arrays.asList(json);
	}

	public List<JSONObject> getPosItemComboDetailsDefault(String tenantId, Integer organId) throws Exception{
		String querySql = "SELECT  id,tenancy_id,store_id,setmeal_id,group_id,group_item_id,unit_id,pos_num,opt_num,updatetime from pos_item_combo_details_default where tenancy_id='"+tenantId+"' and store_id="+organId;
		List<JSONObject> jsonList = this.query4Json(tenantId, querySql);
		JSONObject json = new JSONObject();
		json.put("setting_setmeal_details", jsonList);
		return Arrays.asList(json);
	}




	public List<JSONObject> getRoles(String tenantId) throws Exception{
		String querySql = "select id,role_name,role_level,uplevel_role_id  from roles where tenancy_id='"+tenantId+"' and  state ='1'";
		List<JSONObject> jsonList = this.query4Json(tenantId, querySql);
		JSONObject json = new JSONObject();
		json.put("roles", jsonList);
		return Arrays.asList(json);
	}
}
