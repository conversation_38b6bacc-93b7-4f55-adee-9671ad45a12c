/**
 * @Title: WriteStringToTxt.java
 * @Package com.tzx.framework.common.util
 * @Description: TODO(用一句话描述该文件做什么)
 * <AUTHOR> A18ccms_gmail_com
 * @date 2017-1-12 上午11:12:19
 * @version V1.0
 */
package com.tzx.framework.common.util;


import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.Writer;

/**
 * @ClassName: WriteStringToTxt
 * @Description: TODO(这个工具类用来给指定目录生成指定的txt文件 字符流)
 * <AUTHOR> a18ccms_gmail_com
 * @date 2017-1-12 上午11:12:19
 */
public class WriteStringToTxt
{
	public static String WriteStringToFile(String filePath,String txtname, String str) throws IOException,Exception
	{
		File tempPathFile = new File(filePath+File.separator+"temp");// 缓冲区目录
		String upUrl = filePath+File.separator+"temp"+File.separator+"upload";		
		File pathf =new File(upUrl);
		if (!tempPathFile.exists())
		{
			tempPathFile.mkdirs();
		}
		if (!pathf.exists())
		{
			pathf.mkdirs();
		}
		String name = filePath+File.separator+txtname+".txt";
		String filename = new String(name.getBytes(), "GB2312");
		File f = new File(filename);// 声明File
		 OutputStreamWriter write = new OutputStreamWriter(new FileOutputStream(f), "GB2312");
		   BufferedWriter writer =null ;
		try 
		{
			writer = new BufferedWriter(write);
		} catch (Exception e) {
			e.printStackTrace();
			throw new Exception("文件创建失败");
		
		}		   
		   try {
			   System.out.println(str);
			   String stro = new String(str.getBytes(),"GB2312");
			   writer.write(str);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}  
		   writer.flush();
		   writer.close();

		return name;

	}
	
	public  static boolean deleteFile(String filePath)
	{
		boolean flag = false;
		File file = new File(filePath);
		if (file.isFile() && file.exists()) {// 路径为文件且不为空则进行删除
			file.delete();// 文件删除
			flag = true;
		}
		return flag;
	}

}
