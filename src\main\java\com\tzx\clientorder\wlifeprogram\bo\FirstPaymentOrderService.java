package com.tzx.clientorder.wlifeprogram.bo;

import com.tzx.framework.common.entity.Data;
import net.sf.json.JSONObject;

/**
 * Created by zds on 2018-08-15.
 */
public interface FirstPaymentOrderService {

    String NAME = "com.tzx.clientorder.wlifeprogram.bo.impl.FirstPaymentOrderServiceImpl";

    /**
     * 保存先付订单
     * @param param
     * @param json
     * @return
     * @throws Exception
     */
    public Data saveTempOrder(Data param, JSONObject json) throws Exception;
    /**
     * 获取订单信息
     * @param tenancyId
     * @param billNum
     * @return
     * @throws Exception
     */
    public JSONObject getOrderDetail(String tenancyId, String billNum) throws Exception;
}
