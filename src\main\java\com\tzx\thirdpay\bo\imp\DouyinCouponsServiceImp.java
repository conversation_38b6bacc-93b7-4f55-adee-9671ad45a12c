package com.tzx.thirdpay.bo.imp;

import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.*;
import com.tzx.pos.base.Constant;
import com.tzx.pos.bo.douyin.IDouyinOpen;
import com.tzx.pos.po.springjdbc.dao.PosPaymentDao;
import com.tzx.thirdpay.bo.DouyinCouponsService;
import com.tzx.thirdpay.po.springjdbc.dao.DouyinCouponsDao;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;


@Service(DouyinCouponsService.NAME)
public class DouyinCouponsServiceImp implements DouyinCouponsService {

    private static final Logger logger = Logger.getLogger(DouyinCouponsServiceImp.class);

    @Autowired
    private IDouyinOpen douyinOpen;
    @Resource(name = PosPaymentDao.NAME)
    protected PosPaymentDao paymentDao;
    @Resource(name = DouyinCouponsDao.NAME)
    private DouyinCouponsDao douyinCouponsDao;

    @Override
    public void couponsPrepare(String tenancyId, int storeId, JSONObject paramJson, Data result) throws Exception {

        String couponCode = paramJson.optString("coupon_code");
        String billNum = paramJson.optString("bill_num");

        if (StringUtils.isEmpty(couponCode) || StringUtils.isEmpty(billNum)) {
            throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
        }

        JSONObject resultData = new JSONObject();

        try {
            JSONObject douyinPreRes = JSONObject.fromObject(douyinOpen.prepare(couponCode).toString());

            JSONArray certs=douyinPreRes.optJSONArray("certificates");

            List<JSONObject> saveParams=new ArrayList<>();

            Iterator certIter = certs.iterator();

            while (certIter.hasNext()){

                Object cert=certIter.next();

                JSONObject jsonCert=JSONObject.fromObject(cert) ;

                JSONObject amount = jsonCert.optJSONObject("amount");


                JSONObject savaparam=new JSONObject();
                savaparam.put("original_amount", amount.optString("original_amount"));
                savaparam.put("pay_amount", amount.optString("pay_amount"));

                // 平台承担金额
                savaparam.put("ticket_amount",amount.optString("ticket_amount"));
                // 商家营销金额，单位分
                savaparam.put("merchant_ticket_amount",amount.optString("merchant_ticket_amount"));
                // 支付优惠金额，单位分
                savaparam.put("payment_discount_amount",amount.optString("payment_discount_amount"));
                // 券实付金额（=用户实付金额+支付优惠金额），单位分
                savaparam.put("coupon_pay_amount",amount.optString("coupon_pay_amount"));

                savaparam.put("encrypted_code", jsonCert.optString("encrypted_code"));
                savaparam.put("expire_time", jsonCert.optString("expire_time"));

                JSONObject sku = jsonCert.optJSONObject("sku");
                savaparam.put("groupon_type", sku.optString("groupon_type"));
                savaparam.put("market_price", sku.optString("market_price"));
                savaparam.put("sku_id", sku.optString("sku_id"));
                savaparam.put("sold_start_time", sku.optString("sold_start_time"));
                savaparam.put("third_sku_id", sku.optString("third_sku_id"));
                savaparam.put("title", sku.optString("title"));

                saveParams.add(savaparam);


                JSONObject timeCard = jsonCert.optJSONObject("time_card");

                //总次数
                int timesCount=1;
                //已使用次数
                int timesUsed=0;

                if(null!=timeCard){
                    timesCount=timeCard.optInt("times_count",1);
                    timesUsed=timeCard.optInt("times_used",0);

                    savaparam.put("times_count", timesCount);
                    savaparam.put("times_used", timesUsed);

                    JSONObject singleAmount = new JSONObject();

                    Double singleCouponPayAmount = 0d;
                    Double singleMerchantTicketAmount = 0d;
                    Double singleoriginalAmount = 0d;
                    Double singlePayAmount = 0d;
                    Double singlePaymentDiscountAmount = 0d;
                    Double singleTicketAmount = 0d;
                    //剩余可用次数
                    if(timesCount-timesUsed>0){

                        //是最后一次使用
                        if(timesCount==timesUsed+1){

                        }else {
//                            singleCouponPayAmount=  DoubleHelper.add(couponsAmount, currencyAmount, 2);
//                            singleMerchantTicketAmount=  DoubleHelper.add(couponsAmount, currencyAmount, 2);
//                            singleoriginalAmount=  DoubleHelper.add(couponsAmount, currencyAmount, 2);
//                            singlePayAmount=  DoubleHelper.add(couponsAmount, currencyAmount, 2);
//                            singlePaymentDiscountAmount=  DoubleHelper.add(couponsAmount, currencyAmount, 2);
//                            singleTicketAmount=  DoubleHelper.add(couponsAmount, currencyAmount, 2);

                        }
                    }
                }
            }


            douyinCouponsDao.saveDouyinCoupons(saveParams);
            resultData.put("coupous_info", douyinPreRes);
            result.setData(Collections.singletonList(resultData));
            result.setCode(0);
        } catch (Exception e) {
            logger.error(e);
            throw SystemException.getInstance(PosErrorCode.DOUYIN_COUPOUS_ERROR).set("{0}", "验券准备").set("{1}", e.getMessage());
        }
    }



    @Override
    public void couponsConsume(String tenancyId, int storeId, String token, List<String> encryptedCodes) throws Exception {


        JSONObject resultData = new JSONObject();

        try {

            JSONObject douyinPreRes = JSONObject.fromObject(douyinOpen.prepare(token).toString());

            JSONArray certs=douyinPreRes.optJSONArray("certificates");

            List<JSONObject> saveParams=new ArrayList<>();
            for(Object cert:certs){
                JSONObject savaparam=new JSONObject();
                JSONObject jsonCert=JSONObject.fromObject(cert) ;

                JSONObject amount=jsonCert.optJSONObject("amount");
                // 券原始金额，单位分
                savaparam.put("original_amount",amount.optString("original_amount"));
                // 用户实付金额，单位分
                savaparam.put("pay_amount",amount.optString("pay_amount"));

                // 平台承担金额
                savaparam.put("ticket_amount",amount.optString("ticket_amount"));
                // 商家营销金额，单位分
                savaparam.put("merchant_ticket_amount",amount.optString("merchant_ticket_amount"));
                // 支付优惠金额，单位分
                savaparam.put("payment_discount_amount",amount.optString("payment_discount_amount"));
                // 券实付金额（=用户实付金额+支付优惠金额），单位分
                savaparam.put("coupon_pay_amount",amount.optString("coupon_pay_amount"));


                savaparam.put("encrypted_code",jsonCert.optString("encrypted_code"));
                savaparam.put("expire_time",jsonCert.optString("expire_time"));

                JSONObject sku=jsonCert.optJSONObject("sku");
                savaparam.put("groupon_type",sku.optString("groupon_type"));
                savaparam.put("market_price",sku.optString("market_price"));
                savaparam.put("sku_id",sku.optString("sku_id"));
                savaparam.put("sold_start_time",sku.optString("sold_start_time"));
                savaparam.put("third_sku_id",sku.optString("third_sku_id"));
                savaparam.put("title",sku.optString("title"));

                saveParams.add(savaparam);
            }

            douyinCouponsDao.saveDouyinCoupons(saveParams);
            resultData.put("coupous_info", douyinPreRes);
        } catch (Exception e) {
            logger.error(e);
            throw SystemException.getInstance(PosErrorCode.DOUYIN_COUPOUS_ERROR).set("{0}", "验券准备").set("{1}", e.getMessage());
        }
    }

    @Override
    public Data couponsCancel(String tenancyId, int storeId, JSONObject paramJson) throws Exception {
        return null;
    }

   /* @Override
    public Data couponsQuery(String tenancyId, int storeId, JSONObject paramJson) throws Exception {
        return null;
    }*/

    @Override
    public Data couponsQuery(String tenancyId, int storeId, JSONObject paramJson) throws Exception
    {
        // TODO Auto-generated method stub
        List<JSONObject> requestList = new ArrayList<JSONObject>();
        requestList.add(paramJson);

        Data requestData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
        requestData.setType(Type.THIRD_COUPONS);
        requestData.setOper(Oper.query);
        requestData.setData(requestList);

        String paramStr = JSONObject.fromObject(requestData).toString();
//		logger.info("<第三方接口请求体==>" + paramStr);
        String responseStr = HttpUtil.sendPostRequest(null, paramStr, 3000, 5000);

        return this.stringToData(responseStr);
    }

    private Data stringToData(String str) throws Exception
    {
        Data resultData = null;
        if (Tools.hv(str))
        {
            ObjectMapper objectMapper = new ObjectMapper();
            resultData = objectMapper.readValue(str, Data.class);
        }
        else
        {
            resultData = Data.get();
            resultData.setCode(Constant.CODE_CONN_EXCEPTION);
            resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
        }
        return resultData;
    }

    @Override
    public void couponsCheck(String tenancyId, int storeId, JSONObject paramJson,Data result) throws Exception {

        try{

            List<JSONObject> douyinCheckRes=new ArrayList<>();

            JSONArray couponCodes = paramJson.optJSONArray("coupon_codes");
            String billNum = paramJson.optString("bill_num");

//            String encryptedCodes = "";
//            for (Object code : couponCodes) {
//                encryptedCodes += ",'" + code + "'";
//            }
//
//            List<JSONObject> coupousObjects = douyinCouponsDao.query4Json(tenancyId, "select * from pos_bill_payment_coupous_douyin where encrypted_code in (" + encryptedCodes.replaceFirst(",", "") + ")");

            StringBuilder qureyCoupousInfo=new StringBuilder();

            //统计每个优惠券使用次数
            Map<String, Integer>  statMap=new HashMap<>();

            for (Object code : couponCodes) {
                
                String codeStr=code.toString();
                
                if(null==statMap.get(code)){
                    statMap.put(codeStr,1);
                }else {
                    int pre=statMap.get(codeStr);
                    statMap.put(codeStr,++pre);
                }

                qureyCoupousInfo.append(" UNION ALL ");
                qureyCoupousInfo.append("select * from pos_bill_payment_coupous_douyin where encrypted_code = '" + codeStr + "'");

            }

            List<JSONObject> coupousObjects = douyinCouponsDao.query4Json(tenancyId, qureyCoupousInfo.toString().replaceFirst(" UNION ALL ",""));

            //[{item_id= {rwid=item_count}}]  菜品id={rwid=菜品可用数量}
            Map<String, Map<Integer, Double>> itemUsableMap = new HashMap<>();

            //[{rwid=coupon_amount}]  任务id=单份菜品抵扣金额
            Map<Integer, Double> rwidAmount = new HashMap<>();

            List<JSONObject> items = paymentDao
                    .query4Json(tenancyId, "select rwid,item_num item_id,item_count,round(real_amount/item_count,2) coupon_amount from pos_bill_item where bill_num='"
                            + billNum + "'  and  (item_remark is null or item_remark not in  ('TC01', 'FS02','CJ05','QX04','MD03')) and  item_property <> 'MEALLIST' ORDER BY coupon_amount desc");


            for (JSONObject item : items) {

                Map<Integer, Double> value = new HashMap<>();

                String key = item.optString("item_id");
                if (null == itemUsableMap.get(key)) {
                    value.put(item.optInt("rwid"), item.optDouble("item_count"));
                    itemUsableMap.put(key, value);
                } else {
                    value = itemUsableMap.get(key);
                    value.put(item.optInt("rwid"), item.optDouble("item_count"));
                }

                rwidAmount.put(item.optInt("rwid"), item.optDouble("coupon_amount"));
            }

            for (JSONObject coupon : coupousObjects) {

                JSONObject object = new JSONObject();
                douyinCheckRes.add(object);

                String encryptedCode = coupon.optString("encrypted_code");
                String grouponType = coupon.optString("groupon_type");
                String title=coupon.optString("title");
                Double couponAmount=coupon.optDouble("original_amount");

                object.put("encrypted_code", encryptedCode);
                object.put("groupon_type", grouponType);
                object.put("usable", true);
                object.put("rwid", null);
                object.put("coupon_amount", null);
                object.put("usable_count", null);

//                object.put("original_amount", AmountUtil.changeF2Y(coupon.optDouble("original_amount")));
//                object.put("pay_amount", AmountUtil.changeF2Y(coupon.optDouble("pay_amount")));
//                object.put("ticket_amount", AmountUtil.changeF2Y(coupon.optDouble("ticket_amount")));
//                object.put("payment_discount_amount", AmountUtil.changeF2Y(coupon.optDouble("payment_discount_amount")));

                Long expireTime = coupon.optLong("expire_time");
                //过期
                if (expireTime < System.currentTimeMillis() / 1000) {
                    object.put("usable", false);
                    object.put("msg", String.format("优惠券【%s】已过有效期限",title));
                    break;
                }


                int timesCount = coupon.optInt("times_count", 1);
                int timesUsed = coupon.optInt("times_used", 0);
                int remainCount=timesCount-timesUsed;

                if(timesCount>0&&statMap.get(encryptedCode)>(timesCount-timesUsed)){
                    object.put("usable", false);
                    object.put("msg", String.format("优惠券【%s】超出剩余可用次数%d",title,remainCount));
                    break;

                }

                object.put("times_count", timesCount);
                object.put("times_used", timesUsed);

                if (GROUPON_TYPE_TUANCAN.equals(grouponType)||GROUPON_TYPE_CIKA.equals(grouponType)) {
                    String itemId = coupon.optString("third_sku_id");
                    //未映射菜品
                    if (StringUtils.isEmpty(itemId)) {
                        object.put("usable", false);
                        object.put("msg", String.format("优惠券【%s】未设置可用菜品",title));
                        break;
                    }

                    if (null == itemUsableMap.get(itemId)) {
                        object.put("usable", false);
                        object.put("msg", String.format("优惠券【%s】未找到可用菜品",title));
                        break;
                    } else {
                        //{rwid=菜品可用数量}
                        /*
                         * item_id={
                         *          rwid=1,count=2
                         *          rwid=2,count=1
                         *          rwid=3,conut=1
                         * }
                         */
                        Map<Integer, Double> rwidMap = itemUsableMap.get(itemId);

                        Integer toUseRwid = null;

                        for (Map.Entry<Integer, Double> entry : rwidMap.entrySet()) {
                            Integer rwid = entry.getKey();
                            Double usableCount = entry.getValue();
                            if (usableCount < 1) {
                                continue;
                            }
                            toUseRwid = rwid;
                            rwidMap.put(toUseRwid, --usableCount);
                            break;
                        }

                        if (null != toUseRwid) {
                            object.put("rwid", toUseRwid);

                            object.put("used_count",1);
//                            object.put("times_count",coupon.optInt("times_count",1));
//                            object.put("times_used",coupon.optInt("times_used",0));

                            //菜品金额
                            Double itemAmount = rwidAmount.get(toUseRwid);
                            //优惠券抵扣金额
                            object.put("coupon_amount", itemAmount);
                            //付款金额
                            object.put("payment_amount", AmountUtil.changeF2Y(coupon.optDouble("market_price",0d)));

                            //coupon.pay_amount+coupon.payment_discount_amount+coupon.ticket_amount
                            //券实付金额（=用户实付金额+支付优惠金额）
//                            Double payAmount=AmountUtil.changeF2Y(coupon.optDouble("coupon_pay_amount"));
                            //支付优惠金额
//                            Double paymentDiscountAmount=AmountUtil.changeF2Y(coupon.optDouble("payment_discount_amount"));
                            //平台承担优惠 (新版接口抖音未开放此字段)
//                            Double ticketAmount=AmountUtil.changeF2Y(coupon.optDouble("ticket_amount"));
                            //商家实收
//                            Double dueAmount=DoubleHelper.add(payAmount,ticketAmount,2);
//                            Double dueAmount=payAmount;

                            //实收  =original_amount-merchant_ticket_amount
                            Double originalAmount = AmountUtil.changeF2Y(coupon.optDouble("original_amount", 0));
                            Double merchantTiketAmount = AmountUtil.changeF2Y(coupon.optDouble("merchant_ticket_amount",0));
                            Double dueAmount= DoubleHelper.sub(originalAmount,merchantTiketAmount,2);
                            object.put("due_amount", dueAmount);


                        } else {
                            object.put("usable", false);
                            object.put("msg",String.format("优惠券【%s】超出本账单最多可用数量",title));
                            break;
                        }

                    }

                }

                if(GROUPON_TYPE_DAIJIN.equals(grouponType)){
                    object.put("used_count", 1);
//                    object.put("times_count",coupon.optInt("times_count",1));
//                    object.put("times_used",coupon.optInt("times_used",0));

                    //优惠券抵扣金额
                    object.put("coupon_amount", AmountUtil.changeF2Y(coupon.optDouble("market_price",0d)));
                    //付款金额
                    object.put("payment_amount", AmountUtil.changeF2Y(coupon.optDouble("market_price",0d)));
                    //coupon.pay_amount+coupon.payment_discount_amount+coupon.ticket_amount

                    //券实付金额（=用户实付金额+支付优惠金额）
//                    Double payAmount=AmountUtil.changeF2Y(coupon.optDouble("coupon_pay_amount"));
                    //支付优惠金额
//                  Double paymentDiscountAmount=AmountUtil.changeF2Y(coupon.optDouble("payment_discount_amount"));
                    //平台承担优惠 (新版接口抖音未开放此字段)
//                  Double ticketAmount=AmountUtil.changeF2Y(coupon.optDouble("ticket_amount"));
                    //商家实收
//                  Double dueAmount=DoubleHelper.add(payAmount,ticketAmount,2);

                    //实收  =original_amount-merchant_ticket_amount
                    Double originalAmount = AmountUtil.changeF2Y(coupon.optDouble("original_amount", 0));
                    Double merchantTiketAmount = AmountUtil.changeF2Y(coupon.optDouble("merchant_ticket_amount",0));
                    Double dueAmount= DoubleHelper.sub(originalAmount,merchantTiketAmount,2);
                    object.put("due_amount", dueAmount);

                }

            }

            //对次卡验券结果进一步处理

    /*        {
                "encrypted_code": "CgwIARC1HhifIyABKAESLgosXRia5wuCzMUT5kqahAhjLOy2nF2OwSsSoSYedk1PVAj+HI1uaT+4MiBEGHcaAA==",
                "groupon_type": "3",
                "usable": true,
                "rwid": [180582,
                180582],   //抵扣的菜品rwid
                "coupon_amount": 16, //优惠券抵扣金额   (菜品券=菜品金额，次卡=菜品金额x抵扣次数，代金券=券面值)
                "used_count":2 , //券码核销的次数   (statMap.get(code))
                "payment_amount":16 // 付款金额(菜品券/代金券=coupon.original_amount，
                                            //次卡=没用到最后一次:coupon.original_amount/总次数 x  used_count
                                                    已用到最后一次：=coupon.original_amount-ROUND(coupon.original_amount/coupon.times_count,2)x(coupon.times_count-1))
                "due_amount":16 ,//商家实收金额  (菜品券/代金券= due_amount=coupon.pay_amount+coupon.payment_discount_amount+coupon.ticket_amount，
                                            //次卡=没用到最后一次:ROUND(due_amount/总次数,2) x  used_count
                                                   已用到最后一次：=due_amount-ROUND(due_amount/coupon.times_count,2)x(coupon.times_count-1))
            }
            */

            List success=new ArrayList<>();
            List<JSONObject> fail=new ArrayList<>();


            Map<String,JSONObject> encryptedCodeSet=new HashMap<>();

            Map<String,Integer> useStat=new HashMap<>();

            Iterator<JSONObject> iterator = douyinCheckRes.iterator();

            while (iterator.hasNext()){
                JSONObject json=iterator.next();

                if(json.optBoolean("usable")){



                    String encrytepdCode=json.optString("encrypted_code");

                    String grouponType=json.optString("groupon_type");

                    JSONObject aggJson=encryptedCodeSet.get(encrytepdCode);

                    if(null!=aggJson) {

                        if (GROUPON_TYPE_TUANCAN.equals(grouponType) || GROUPON_TYPE_CIKA.equals(grouponType)) {

                            List<String> rwids = aggJson.optJSONArray("rwid");
                            rwids.add(json.optString("rwid"));
                            aggJson.put("rwid", rwids);

//                          Double tempCouponAmount=aggJson.optDouble("coupon_amount");
//                          Double  couponAmount=json.optDouble("coupon_amount");


                            aggJson.put("coupon_amount", DoubleHelper.add(aggJson.optDouble("coupon_amount"),
                                    json.optDouble("coupon_amount"), 2));

                        }

                    }else {

                        aggJson=json;

                        if(GROUPON_TYPE_TUANCAN.equals(grouponType)||GROUPON_TYPE_CIKA.equals(grouponType)){

                            List<String> rwids=new ArrayList<>();
                            rwids.add(aggJson.optString("rwid"));
                            aggJson.put("rwid",rwids);
                        }

                        Integer useCount=statMap.get(encrytepdCode);

                        aggJson.put("used_count",useCount);

                        Double paymentAmount=0d;
                        Double dueAmount=0d;




                        int timesCount = aggJson.optInt("times_count");
                        int timesUsed = aggJson.optInt("times_used");


//                        object.put("original_amount", AmountUtil.changeF2Y(coupon.optDouble("original_amount")));
//                        object.put("pay_amount", AmountUtil.changeF2Y(coupon.optDouble("pay_amount")));
//                        object.put("ticket_amount", AmountUtil.changeF2Y(coupon.optDouble("ticket_amount")));
//                        object.put("payment_discount_amount", AmountUtil.changeF2Y(coupon.optDouble("payment_discount_amount")));

                        paymentAmount=calcAmoumt(aggJson.optDouble("payment_amount"),timesCount,timesUsed,useCount);
                        dueAmount=calcAmoumt(aggJson.optDouble("due_amount"),timesCount,timesUsed,useCount);

                        aggJson.put("payment_amount",paymentAmount);
                        aggJson.put("due_amount",dueAmount);

                        encryptedCodeSet.put(encrytepdCode,aggJson);

                    }

                    if(!iterator.hasNext()){

                        Iterator<JSONObject> successItertor = encryptedCodeSet.values().iterator();

                        while (successItertor.hasNext()){

                            JSONObject resJson=successItertor.next();

                            if(GROUPON_TYPE_TUANCAN.equals(grouponType)||GROUPON_TYPE_CIKA.equals(grouponType)) {

                                Double paymentAmount = resJson.optDouble("payment_amount", 0);

                                Double couponAmount = resJson.optDouble("coupon_amount", 0);

                                if (couponAmount > paymentAmount) {
                                    resJson.put("payment_amount", couponAmount);
                                }
                            }

                            success.add(resJson);
                        }

                    }

                }else {
                    fail.add(json);
                    break;
                }

            }

            result.setData(fail.isEmpty()?success:fail);

            result.setCode(0);
        }catch (Exception e){
            logger.error("验券失败",e);
            throw SystemException.getInstance(PosErrorCode.DOUYIN_COUPOUS_ERROR).set("{0}", "验券失败").set("{1}", e.getMessage());
        }

    }

    private Double calcAmoumt(double paymentAmount, int timesCount, int timesUsed, Integer useCount) {
        if(timesCount==useCount){
            return  paymentAmount;
        }
        Double result=0d;
        for(int i=1;i<=useCount;i++){
           Double calcAmoumt=doCalcAmoumt(paymentAmount,timesCount,timesUsed++);

           result=DoubleHelper.add(result,calcAmoumt,2);
        }

        return result;

    }


    /***
     *
     * @param amount  券面值
     * @return
     */
    //算法：coupon.original_amount-ROUND(coupon.original_amount/coupon.times_count,2)x(coupon.times_count-1))
    private Double doCalcAmoumt(Double amount,int timesCount, int timesUsed){

            int timesRemain=timesCount-timesUsed;
            //存在除不尽情况,且是最后一次
            if(timesRemain==1&&amount%timesCount>0){
                return DoubleHelper.sub(amount, DoubleHelper.mul(
                        DoubleHelper.div(amount, (double) timesCount,2),(double)(timesCount-1),2),2);

            }else{
                return DoubleHelper.div(amount, (double) timesCount,2);
            }

    }

    public static void main(String[] args) {

        JSONObject o=new JSONObject();

        JSONArray array=new JSONArray();
        JSONObject j1=new JSONObject();
        j1.put("name","a");
        array.add(j1);

        o.put("data",array);

        System.out.println(o.toString());

        Iterator iterator = array.iterator();

        while (iterator.hasNext()){
            Object next = iterator.next();
            JSONObject j=JSONObject.fromObject(next);
            j.put("name","b");
            array.add(j);
            array.remove(next);

        }

        o.put("data",array);

        System.out.println("--------------");
        System.out.println(o.toString());

        System.out.println(99.0%2);

        int  arr[]={0,1,2,3};
        System.out.println(Arrays.toString(arr));

    }

}
