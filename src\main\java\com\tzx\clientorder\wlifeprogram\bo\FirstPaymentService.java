package com.tzx.clientorder.wlifeprogram.bo;

import net.sf.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by zds on 2018-08-14.
 */
public interface FirstPaymentService {
    Map<String,String> paymentWayMapping = new HashMap<>();

    Map<String,String> posBillMemberTyoeMapping = new HashMap<>();

    Map<String,String> posBillCouponTypeMapping = new HashMap<>();

    Map<String,String> posBillCouponTypeNameMapping = new HashMap<>();

    int DEFAULT_SCALE = 4;

    String NAME = "com.tzx.clientorder.wlifeprogram.bo.impl.FirstPaymentServiceImpl";
    /**
     * 先付预结下单
     * @param param
     * @param responseJson
     * @return
     */
    public JSONObject orderPrecheck(JSONObject param,JSONObject responseJson) throws Exception;

    /**
     * 先付结账结账
     * @param param
     * @param responseJson
     * @return
     */
    public JSONObject firstPayClose(JSONObject param,JSONObject responseJson) throws Exception;
}
