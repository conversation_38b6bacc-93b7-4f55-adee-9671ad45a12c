package com.tzx.pos.bo;

import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.SystemException;

import java.util.List;

/**
 * 
 * <AUTHOR> 2017年6月2日-上午10:49:47
 */
public interface PosActivityService extends PosBaseService
{
	String	NAME	= "com.tzx.pos.bo.imp.PosActivityServiceImp";
	/**
	 * 获取有效活动列表
	 * @param param
	 * @param result
	 * @return
	 * @throws SystemException
	 */
	public void getValidActivity(Data param, Data result) throws SystemException;
	/**
	 * 取消活动
	 * @param param
	 * @param result
	 * @return
	 * @throws SystemException
	 * @throws Exception
	 */
	public void cancelActivity(Data param, Data result) throws Exception;
	/**
	 * 参加活动
	 * 满减活动mejxj,加价购活动mejjg,满折活动medz,满额赠菜mezx
	 * @param param
	 * @param result
	 * @return
	 * @throws SystemException
	 * @throws Exception
	 */
	public Data joinInActivity(Data param, Data result) throws SystemException;
	/**
	 * 查询使用过的活动
	 * @param param
	 * @param result
	 * @return
	 * @throws SystemException
	 * @throws Exception
	 */
	public void getUsedActivity(Data param, Data result) throws SystemException;
	/**
	 * 下单使用活动
	 * @param param
	 * @param result
	 * @return
	 * @throws SystemException
	 * @throws Exception
	 */
	public void orderingJoinInActivity(String tenancyId, Integer storeId, String billNum) throws SystemException;


}
