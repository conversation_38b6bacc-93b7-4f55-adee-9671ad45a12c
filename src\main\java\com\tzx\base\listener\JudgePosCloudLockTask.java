/** 
 * @(#)CodeFormat.java    1.0   2018-04-20
 * 
 * Copyright 北京普照天星科技有限公司. All rights reserved. 
 * PROPRIETARY/CONFIDENTIAL. Use is subject to license terms. 
 */
package com.tzx.base.listener;

import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import org.apache.log4j.Logger;
import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.pos.po.springjdbc.dao.PosPaymentDao;
import com.tzx.pos.po.springjdbc.dao.imp.PosPaymentDaoImp;

/**
 * 判断是否启用门店定时解锁云账单功能
 * 
 * <AUTHOR> email:<EMAIL>
 * @version 1.0  2018-04-20
 * @see     
 * @since   JDK7.0
 * @update  
 */
public class JudgePosCloudLockTask implements Runnable {
	
	private static final Logger	logger	= Logger.getLogger(JudgePosCloudLockTask.class);

	@Override
	public void run() {
		try{
			// 等待Bean初始化完成
			while(SpringConext.getApplicationContext() == null){
				Thread.sleep(1000);
			}  
			logger.info("判断是否启动门店定时解锁云账单功能......");
			Map<String, String> systemMap = Constant.getSystemMap();
            String tenent_id= systemMap.get("tenent_id");
            String store_id= systemMap.get("store_id");
            DBContextHolder.setTenancyid(tenent_id);
            PosPaymentDaoImp posPaymentServiceImp = (PosPaymentDaoImp) SpringConext.getApplicationContext().getBean(PosPaymentDao.NAME);
            String para_value=posPaymentServiceImp.getSysParameter(tenent_id,Integer.parseInt(store_id),Constant.IS_START_CLOUDPOS_PAY);
            if("0".equals(para_value)||"".equals(para_value)){
                return ;
            }else{
            	logger.info("门店定时解锁云账单");
            	Executors.newScheduledThreadPool(1).scheduleWithFixedDelay(new PosCloudLockTask(), 3, 3, TimeUnit.SECONDS);
            }
		}catch(Exception e){
			logger.error("判断是否启用门店订单解锁云账单功能失败："+e);
		}
	}

}
