package com.tzx.clientorder.retail.dao.impl;

import com.tzx.clientorder.retail.dao.RetailDao;
import com.tzx.pos.base.dao.imp.BaseDaoImp;
import com.tzx.pos.base.util.SqlUtil;
import java.util.ArrayList;
import java.util.List;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Repository;

@Repository(RetailDao.NAME)
public class RetailDaoImpl extends BaseDaoImp
  implements RetailDao
{
  public List<JSONObject> getItemInfos(JSONObject pageParams, String tenancyId, String storeId, String channel)
    throws Exception
  {
    StringBuilder sql = getItemInfoSql(tenancyId, storeId, channel);
    
//    String pageSql = buildPageSql(pageParams, sql.toString());
    int pagenum = pageParams.containsKey("page") ? (pageParams.optInt("page") == 0 ? 1 : pageParams.optInt("page")) : 1;
	int limit = pageParams.containsKey("rows") ? pageParams.optInt("rows") : 10;
	String pageSql = "select * from (" + sql.toString() + ") datasql limit " + limit + " offset " + (limit * (pagenum - 1));
	
    return query4Json(tenancyId, pageSql);
  }

  private StringBuilder getItemInfoSql(String tenancyId, String storeId, String channel)
  {
    StringBuilder sql = new StringBuilder();
    sql.append("  select distinct hii.id as id, hii.item_name as name, hii.item_code as dishsno, '' as describ  ");
    sql.append("  , COALESCE(hii.item_description, '') as info, (case when hii.is_combo='Y' then 2 else 1 end) as type  ");
    sql.append("  , hii.is_new,hii.is_characteristic,hii.is_recommendation,hii.item_barcode");
    sql.append("  , (case when himd.is_show='1' then false else true end) as wxDishs, himc.class as pkid ");
    sql.append("  , hic.father_id as dishkind, COALESCE(hii.photo1, '') as icon, COALESCE(hii.photo1, '') as image  ");
    sql.append("  , COALESCE(hii.photo1, '') as dishimg, 1 as min_unit, 1 as min_count, 1 as min_reduce ");
    sql.append("  ,(CASE WHEN (select coalesce(num,0) from pos_soldout WHERE item_id = hii.ID and tenancy_id = hii.tenancy_id) <= 0 THEN 1 ELSE 0 END) AS soldout ");
    sql.append("  ,(CASE WHEN (select num from pos_soldout WHERE item_id = hii.ID and tenancy_id = hii.tenancy_id) is not null THEN (SELECT COALESCE (num, 0) AS num FROM pos_soldout WHERE item_id = hii. ID AND tenancy_id = hii.tenancy_id) ELSE -1 END) AS leftamount ");
    sql.append("  , (case when hii.is_modifyquantity='Y' then hiu.unit_name else '' end) as priceName ");
    sql.append("  , (case when hii.is_modifyquantity='Y' then 1 else 0 end) as isWeigh   ");
    sql.append("  , himcc.classorder,CAST (himd.menu_item_rank AS int4)");
    sql.append("  from hq_item_menu_organ himo  ");
    sql.append("  join hq_item_menu him on (him.id=himo.item_menu_id and him.valid_state='1') ");
    sql.append("  join hq_item_menu_details himd on (himd.item_menu_id=him.id and himd.valid_state='1') ");
    sql.append("  join hq_item_menu_class himc on himc.details_id=himd.id and himc.chanel='").append(channel).append("'");
    sql.append("  join hq_item_menu_classorder himcc on him.id=himcc.menu_id and himc.class=himcc.class_id ");
    sql.append("  join hq_item_class hic on hic.id=himc.class and hic.chanel=himc.chanel and hic.valid_state='1' ");
    sql.append("  join hq_item_info hii on (hii.id=himd.item_id and hii.valid_state='1') ");
    sql.append("  join hq_item_unit hiu on (hiu.item_id=hii.id and hiu.valid_state='1') and hiu.is_default='Y' ");
	sql.append("  where himo.store_id=" + storeId).append(" and himo.tenancy_id='" + tenancyId + "'");
    sql.append("  order by himcc.classorder,cast(himd.menu_item_rank as int4) ");
    return sql;
  }

  public long getItemInfoCount(String tenancyId, String storeId, String channel) throws Exception
  {
    StringBuilder sql = getItemInfoSql(tenancyId, storeId, channel);
    return countSql(tenancyId, sql.toString());
  }

  public List<JSONObject> getShops(String tenancyId, String storeId) throws Exception
  {
    StringBuilder sql = new StringBuilder();
    sql.append(" select o.id as sid, '' as bid, o.org_full_name as shopname, COALESCE(o.address, '') as shopadd ");
    sql.append(" , '' as shoplocation, COALESCE(o.longitude, '') as lng, COALESCE(o.latitude, '') as lat ");
    sql.append(",2 as ordermode");
    sql.append(", 0 as is_bind_user ");
    sql.append(" from organ o ");
    sql.append(" where o.tenancy_id='" + tenancyId + "' ");
    sql.append(" and o.id='" + storeId + "' ");
    sql.append(" and o.org_type='3' ");
    sql.append(" and o.valid_state='1'  ");
    return query4Json(tenancyId, sql.toString());
  }

  public List<JSONObject> getUnitInfos(String tenancy_id, int[] item_ids, String price_system, String channel) throws Exception
  {
    if ((item_ids == null) || (item_ids.length == 0)) {
      return new ArrayList<JSONObject>();
    }
    StringBuilder sql = new StringBuilder();
    sql.append(" select hiu.id as duid, hiu.unit_name as name, COALESCE(hip.price, hiu.standard_price) as price, COALESCE(hip.price, hiu.standard_price) as orgprice, 0 as bargainprice ");
    sql.append(" , 0 as limitCount, COALESCE(COALESCE(civs.vip_price, hip.price), hiu.standard_price) as memberprice, 1 as min_unit ");
    sql.append(" , hiu.item_id as item_id ");
    sql.append(" from hq_item_unit hiu  ");
    sql.append(" left join hq_item_pricesystem hip on (hip.item_unit_id=hiu.id and hip.chanel='" + channel + "' and hip.price_system='" + price_system + "') ");
    sql.append(" left join crm_item_vip_sysprice civs on (civs.unit_id=hiu.id and civs.chanel='" + channel + "' and civs.price_system='" + price_system + "') ");
    sql.append(" where hiu.item_id in (" + SqlUtil.getInnerStr(item_ids) + ") ");
    sql.append(" and hiu.valid_state='1' ");
    sql.append(" and hiu.tenancy_id='" + tenancy_id + "' ");
    return query4Json(tenancy_id, sql.toString());
  }

  public List<JSONObject> getMethodInfos(String tenancy_id, int[] item_ids, String storeId, String channel) throws Exception
  {
    if ((item_ids == null) || (item_ids.length == 0)) {
      return new ArrayList<JSONObject>();
    }
    StringBuilder sql = new StringBuilder();
    sql.append(" select DISTINCT him.id as id, sd.class_item as name, case when him.makeup_way = 'ADD' then  him.proportion_money  when him.makeup_way = 'MULTI' then him.proportion_money*itp.price ELSE 0 end as aprice, ");

    sql.append(" (CASE WHEN (select coalesce(num,0) from pos_soldout WHERE item_id = hii.ID and tenancy_id = hii.tenancy_id) <= 0 THEN 1 ELSE 0 END) AS soldout, '' as icon ");
    sql.append(" ,him.item_id as item_id ");
    sql.append(" from hq_item_method him ");
    sql.append(" left join sys_dictionary sd on (sd.id=him.method_name_id and sd.class_identifier_code='method') ");
    sql.append(" left join hq_item_info hii on him.item_id = hii.id");
    sql.append(" left join hq_item_unit hiu on hiu.item_id = hii.id");
    sql.append(" left join hq_item_pricesystem itp on itp.item_unit_id = hiu.id and itp.chanel = '" + channel + "'");
    sql.append(" left join organ o on itp.price_system::varchar = o.price_system ");
    sql.append(" where him.item_id in (" + SqlUtil.getInnerStr(item_ids) + ") ");
    sql.append(" and him.valid_state='1' ");
    sql.append(" and him.tenancy_id='" + tenancy_id + "' and o.id = " + storeId);
    return query4Json(tenancy_id, sql.toString());
  }

  public List<JSONObject> getComboBaseInfo(String tenancy_id, int[] combo_item_ids, String price_system, String channel) throws Exception
  {
    if ((combo_item_ids == null) || (combo_item_ids.length == 0)) {
      return new ArrayList<JSONObject>();
    }
    StringBuilder sql = new StringBuilder();
    sql.append(" select hiu.item_id as item_id,i.item_name as name, COALESCE(hip.price, hiu.standard_price) as price ");
    sql.append(" , COALESCE(hip.price, hiu.standard_price) as orgprice, 0 as bargainprice");
    sql.append(" , 0 as limitCount, COALESCE(COALESCE(civs.vip_price, hip.price), hiu.standard_price) as memberprice ");
    sql.append(" from hq_item_unit hiu ");

    sql.append(" left join hq_item_pricesystem hip on (hip.item_unit_id=hiu.id and hip.chanel='" + channel + "') ");
    sql.append(" left join crm_item_vip_sysprice civs on (civs.unit_id=hiu.id and civs.price_system='" + price_system + "' and civs.chanel='" + channel + "')  ");
    sql.append(" left join hq_item_info i on hiu.item_id = i.id ");
    sql.append(" where hiu.item_id in (" + SqlUtil.getInnerStr(combo_item_ids) + ") ");
    sql.append(" and hiu.is_default='Y' ");
    sql.append(" and hiu.valid_state='1' ");
    sql.append(" and hiu.tenancy_id='" + tenancy_id + "' ");
    return query4Json(tenancy_id, sql.toString());
  }

  public List<JSONObject> selectDetailsInfo(String tenancy_id, int[] combo_item_ids, String channel) throws Exception
  {
    if ((combo_item_ids == null) || (combo_item_ids.length == 0)) {
      return new ArrayList<JSONObject>();
    }
    StringBuilder sql = new StringBuilder();
    sql.append(" select hii.item_name as name, hicd.combo_num as number, hicd.is_itemgroup ");
    sql.append(" , hicd.details_id as id, hicd.item_unit_id as duid, hii.item_code as dishsno ");
    sql.append(" , hicd.iitem_id as item_id, hicd.id as hicd_id ");
    sql.append(" from hq_item_combo_details hicd ");
    sql.append(" left join hq_item_combo_pricesystem hicp on (hicp.combo_details_id=hicd.id and hicp.price_system='1' and hicp.chanel='" + channel + "') ");
    sql.append(" left join hq_item_info hii on hii.id=hicd.details_id and hii.valid_state='1' ");
    sql.append(" where hicd.iitem_id in (" + SqlUtil.getInnerStr(combo_item_ids) + ") ");
    sql.append(" and hicd.valid_state='1' ");
    sql.append(" and hicd.tenancy_id='" + tenancy_id + "' order by hicd.iitem_id,hicd.combo_order,hicd.id");
    return query4Json(tenancy_id, sql.toString());
  }

  public void saveDeliveInfo(String teancyId, JSONObject deliverInfo) throws Exception
  {
    insertIgnorCase(teancyId, "cc_deliver_list", deliverInfo);
  }
}