package com.tzx.member.meituan.bo;

import java.sql.Timestamp;
import java.util.Map;

import net.sf.json.JSONObject;

import com.tzx.framework.common.entity.Data;

/**
 * 新美大CRM接口
 * 
 * <AUTHOR>
 *
 */
public interface PosNewCrmService
{

	String	NAME	= "com.tzx.member.meituan.bo.imp.PosNewCrmServiceImp";

	/**
	 * 获取Token
	 * 
	 * @param param
	 * @param result
	 * @return
	 * @throws Exception
	 */
	public Data getToken(Data param) throws Exception;

	/**
	 * 发卡
	 * 
	 * @param param
	 * @param result
	 * @return
	 * @throws Exception
	 */
	public Data creditCard(Data param) throws Exception;

	/**
	 * 充值
	 * 
	 * @param param
	 * @param result
	 * @return
	 * @throws Exception
	 */
	public Data recharge(Data param) throws Exception;

	/**
	 * 撤销充值
	 * 
	 * @param param
	 * @param result
	 * @return
	 * @throws Exception
	 */
	public Data cancelRecharge(Data param) throws Exception;

	/**
	 * 查询用户信息
	 * 
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public Data findUserInfo(Data param) throws Exception;

	/**
	 * 付款完成,记录新美大消费流水,并打印消费小票
	 * 
	 * @param param
	 * @param result
	 * @return
	 * @throws Exception
	 */
	public Data paymentCRM(String tenantId,Integer storeId,Map<String, Object> map) throws Exception;

	/**
	 * 取消付款
	 * 
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public void cancelPayMent(String tenantId,Integer storeId,JSONObject para,Data resultData) throws Exception;
	
	/**查询新美大支付状态
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public void queryXMDCRMPayment(String tenantId,Integer storeId,JSONObject para,Data resultData) throws Exception;
	
	/** 确认支付完成
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public void completeXMDCRMPayment(String tenantId,Integer storeId,JSONObject para,Timestamp currentTime) throws Exception;
}
