package com.tzx.base.constant;

import com.tzx.base.entity.cache.*;
import com.tzx.pos.base.constant.BasicData;

public enum CacheTableConstant
{
	/**
	 * 系统参数
	 */
	SYS_PARAMETER(BasicData.SYS_PARAMETER.getCode(), SysParameterCache.getSql(), SysParameterCache.getKey(), true),
	/**
	 * 服务费种
	 */
	SERVICE_TYPE(BasicData.SERVICE_TYPE.getCode(), ServiceTypeCache.getSql(), ServiceTypeCache.getKey(), true),
	/**
	 * 班次
	 */
	DUTY_ORDER(BasicData.DUTY_ORDER.getCode(), DutyOrderCache.getSql(), DutyOrderCache.getKey(), true),
	/**
	 * 做法
	 */
	METHOD(BasicData.METHOD.getCode(), ItemMethodCache.getSql(), ItemMethodCache.getKey(), true),
	/**
	 * 折扣方案明细
	 */
	DISCOUNT_DETAIL(BasicData.DISCOUNT_DETAIL.getCode(), DiscountCaseDetailsCache.getSql(), DiscountCaseDetailsCache.getKey(), true),
	/**
	 * 菜品
	 */
	DISH(BasicData.DISH.getCode(), DishInfoCache.getSql(), DishInfoCache.getKey(), true),
	/**
	 * 规格
	 */
	UNIT(BasicData.UNIT.getCode(), ItemUnitCache.getSql(), ItemUnitCache.getKey(), true),
	/**
	 * 套餐明细
	 */
	COMBO_DETAILS(BasicData.COMBO_DETAILS.getCode(), ComboDetailsCache.getSql(), ComboDetailsCache.getKey(), true),
	/**
	 * 项目组明细
	 */
	ITEM_GROUP_DETAILS(BasicData.ITEM_GROUP_DETAILS.getCode(), ItemGroupDetailsCache.getSql(), ItemGroupDetailsCache.getKey(), true),
    /**
     * 门店
     */
    ORGAN(BasicData.ORGAN.getCode(), OrganCache.getSql(), OrganCache.getKey(), true),
    ;

	private final Integer	code;
	private final String	sql;
	private final String	key;		// 联合主键,多个字段,以逗号分隔
	private final boolean	isToMap;

	private CacheTableConstant()
	{
		this.code = 0;
		this.sql = "";
		this.key = "";
		this.isToMap = false;
	}

	private CacheTableConstant(Integer code, String sql)
	{
		this.code = code;
		this.sql = sql;
		this.key = "";
		this.isToMap = false;
	}

	private CacheTableConstant(Integer code, String sql, String key, boolean isMap)
	{
		this.code = code;
		this.sql = sql;
		this.key = key;
		this.isToMap = isMap;
	}

	public Integer getCode()
	{
		return this.code;
	}

	public String getSql()
	{
		return this.sql;
	}

	public String getKey()
	{
		return this.key;
	}

	public boolean getIsToMap()
	{
		return this.isToMap;
	}

	public String[] getKeys()
	{
		if (null != this.key && this.key.length() > 0)
		{
			return this.key.split(",");
		}
		return new String[] {};
	}

	public String getSql(String tenancyId, Integer storeID)
	{
		return this.sql.replaceAll("::tenancy_id", "'" + tenancyId + "'").replaceAll("::store_id", String.valueOf(storeID));
	}
}
