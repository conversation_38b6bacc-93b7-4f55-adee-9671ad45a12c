package com.tzx.framework.common.util;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Random;
import java.util.concurrent.ConcurrentHashMap;

import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;

public class test555 {

	 
	private static Random	random	= new Random();
	private static List<Integer> test = new ArrayList<Integer>();
	private static List<Integer> list0 = new ArrayList<Integer>();
	private static List<Integer> list1 = new ArrayList<Integer>();
	private static List<Integer> list2 = new ArrayList<Integer>();
	private static List<Integer> list3 = new ArrayList<Integer>();
	private static List<Integer> list4 = new ArrayList<Integer>();
	private static List<Integer> list5 = new ArrayList<Integer>();
	private static List<Integer> list6 = new ArrayList<Integer>();
	private static List<Integer> list7 = new ArrayList<Integer>();
	private static List<Integer> list8 = new ArrayList<Integer>();
	private static List<Integer> list9 = new ArrayList<Integer>();
	private static ConcurrentHashMap<Integer, List<Integer>> sssmap = new ConcurrentHashMap<Integer, List<Integer>>();
	
	static{
		list0.add(2);list0.add(9);list0.add(1);list0.add(4);list0.add(8);list0.add(0);list0.add(7);list0.add(5);list0.add(6);list0.add(3);
		list1.add(5);list1.add(0);list1.add(8);list1.add(1);list1.add(7);list1.add(4);list1.add(9);list1.add(3);list1.add(2);list1.add(6);
		list2.add(7);list2.add(3);list2.add(6);list2.add(1);list2.add(8);list2.add(4);list2.add(2);list2.add(5);list2.add(0);list2.add(9);
		list3.add(9);list3.add(5);list3.add(4);list3.add(2);list3.add(0);list3.add(3);list3.add(6);list3.add(1);list3.add(7);list3.add(8);
		list4.add(8);list4.add(4);list4.add(6);list4.add(1);list4.add(9);list4.add(7);list4.add(0);list4.add(3);list4.add(2);list4.add(5);
		list5.add(7);list5.add(1);list5.add(9);list5.add(4);list5.add(3);list5.add(2);list5.add(8);list5.add(5);list5.add(0);list5.add(6);
		list6.add(2);list6.add(8);list6.add(6);list6.add(5);list6.add(7);list6.add(3);list6.add(9);list6.add(0);list6.add(1);list6.add(4);
		list7.add(8);list7.add(5);list7.add(0);list7.add(2);list7.add(9);list7.add(1);list7.add(7);list7.add(6);list7.add(4);list7.add(3);
		list8.add(5);list8.add(4);list8.add(3);list8.add(6);list8.add(0);list8.add(8);list8.add(2);list8.add(1);list8.add(9);list8.add(7);
		list9.add(3);list9.add(8);list9.add(0);list9.add(2);list9.add(9);list9.add(1);list9.add(7);list9.add(4);list9.add(5);list9.add(6);
		sssmap.put(0, list0);sssmap.put(1, list1);sssmap.put(2, list2);sssmap.put(3, list3);sssmap.put(4, list4);sssmap.put(5, list5);
		sssmap.put(6, list6);sssmap.put(7, list7);sssmap.put(8, list8);sssmap.put(9, list9);
	}
	
	
	public static String encryptDynamicCode(String id){
		int id_len = id.length();
		StringBuffer allStr = new StringBuffer();
		StringBuffer idstr = new StringBuffer(String.valueOf(id));
		int first = random.nextInt(8);
	
		List<Integer> nowlist = sssmap.get(first);
		if(id_len <10){
			allStr.append(String.valueOf(first));
			int leftLen  =   10 - id_len;
			StringBuffer randomStr = new StringBuffer();
			for(int i = 0;i<leftLen -1;i++){
				Integer num = nowlist.get(i);
				randomStr.append(String.valueOf(num));
			}
			randomStr.append(idstr.toString());
			allStr.append(randomStr.toString());
			if(id_len/10 == 0){//只有一位数
				allStr.append(String.valueOf(0)).append(String.valueOf(id_len));
			}else{
				allStr.append(String.valueOf(0)).append(String.valueOf(id_len));
			}
			String newStr = allStr.toString();
			int temp_last = 0;
			for (int i = 1; i < newStr.length(); i++) {
				String sub = newStr.substring(i-1,i);
				temp_last += Integer.valueOf(sub);
			}
			int last_num = Math.abs(10 - first);
			allStr.append(String.valueOf(last_num));
		}else{
			String first_num = "9";
			int left_len = 13 - id_len;
			allStr.append(String.valueOf(first_num));
			String rightPad = StringUtils.rightPad(allStr.toString(), left_len, "0");
			allStr.setLength(0);
			allStr.append(rightPad).append(String.valueOf(id));
		}
		return allStr.toString();
	}
	
	
	
	public static JSONObject unEncryptDynamicCode(String dynamicCode){
		int id_len = dynamicCode.length();
		JSONObject json = new JSONObject();
		Long newId = 0l;
		StringBuffer allStr = new StringBuffer();
		StringBuffer idstr = new StringBuffer(String.valueOf(dynamicCode));
		String first_num = dynamicCode.substring(0,1);
		if("9".equals(first_num)){
			String newStr = dynamicCode.substring(1, dynamicCode.length());
			newId = Long.parseLong(newStr);
			allStr.append(newId);
			json.put("success", true);
			json.put("id", allStr.toString());
		}else{
			Integer last_num = Math.abs(10 - Integer.valueOf(first_num));
			Integer truelast = Integer.valueOf(dynamicCode.substring(12, 13));
			if(last_num != truelast){
				json.put("success", false);
				return json;
			}
			Integer idlenth = Integer.parseInt(dynamicCode.substring(10, 12));
			String id = dynamicCode.substring(10-idlenth, 10);
			json.put("success", true);
			json.put("id", id);
		}
		return json;
	}
	
	
	
	
	
	public static void main(String[] args) {
		String encryptId = test555.encryptDynamicCode("3344");
		System.out.println(encryptId);
		
		JSONObject unEncryptDynamicCode = test555.unEncryptDynamicCode(encryptId);
		if(unEncryptDynamicCode.optBoolean("success")){
			System.out.println(unEncryptDynamicCode.optString("id"));
		}
		//3954203344047

		
		//System.out.println(StringUtils.rightPad("9", 3, "0"));
	/*	String ss ="123456";
		System.out.println(ss.substring(1, ss.length()));
		for (int i = 1; i <= ss.length(); i++) {
			String s = ss.substring(i-1, i);
			System.out.println(s);
			
		}*/
		
		
		//System.out.println(ss.toString());
		
		/*
		int i = 55;
		System.out.println(i/10);*/
		/*test.add(0);test.add(1);test.add(2);test.add(3);test.add(4);test.add(5);test.add(6);test.add(7);test.add(8);test.add(9);
		StringBuffer sb = new StringBuffer();
		HashMap<Integer, Integer> map = new HashMap<Integer, Integer>();
		for (int i = 0; i < 100; i++) {
			int ss = random.nextInt(10);
			Integer ii = test.get(ss);
			if(map.containsKey(ii)){
				continue;
			}else{
				map.put(ii, ii);
				sb.append("list9.add("+ii+");");
			}
			
		}
		System.out.println(sb.toString());;*/
	/*	String ss = "123";
		System.out.println(ss.substring(0, 1));*/
	}
	
	
}
