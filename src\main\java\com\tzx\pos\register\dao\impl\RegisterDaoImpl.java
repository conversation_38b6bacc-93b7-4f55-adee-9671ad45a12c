package com.tzx.pos.register.dao.impl;

import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Repository;

import com.tzx.pos.base.dao.imp.BaseDaoImp;
import com.tzx.pos.register.dao.RegisterDao;

@Repository(RegisterDao.NAME)
public class RegisterDaoImpl extends BaseDaoImp implements RegisterDao {

	@Override
	public int update(String sql) {

		int count = this.jdbcTemplate.update(sql);
		return count;
	}

	@Override
	public Integer update_hq_devices_sql(String update_hq_devices_sql) {
		int count = this.jdbcTemplate.update(update_hq_devices_sql);
		return count;

	}

	@Override
	public Integer update_hq_local_devices_sql(String update_hq_local_devices_sql) {
		int count = this.jdbcTemplate.update(update_hq_local_devices_sql);
		return count;
	}

	@Override
	public SqlRowSet queryColumnByTableName(String tableName) {
		String sql = "select  * from  " + tableName;
		SqlRowSet sqlRowSet = this.jdbcTemplate.queryForRowSet(sql);

		return sqlRowSet;

	}

}
