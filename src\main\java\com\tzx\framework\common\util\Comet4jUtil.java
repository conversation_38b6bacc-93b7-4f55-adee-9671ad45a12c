package com.tzx.framework.common.util;

import java.util.ArrayList;
import java.util.List;

import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;

import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.pos.base.controller.CometDataNoticeClientRunnable;
import net.sf.json.JSONObject;
import org.comet4j.core.CometConnection;
import org.comet4j.core.CometContext;

public class Comet4jUtil implements ServletContextListener
{

	public static final String	CALLCENTER_ORDER	= "callcenter_order";
	public static final String	POS_ANDROID			= "android";
	public static final String	POS_PC				= "pc";
	public static final String	POS_PRINTER			= "printer";
	

	private static CometContext				cc					= CometContext.getInstance();


	public void contextInitialized(ServletContextEvent arg0)
	{
		cc.registChannel(CALLCENTER_ORDER);
		cc.registChannel(POS_ANDROID);
		cc.registChannel(POS_PC);
		cc.registChannel(POS_PRINTER);
	}

	public static void sendMessage2All(String channel, String message)
	{
		try
		{
			List<CometConnection> list = new ArrayList<CometConnection>();
			list.addAll(cc.getEngine().getConnections());
			
			cc.getEngine().sendTo(channel, list, message);
		}
		catch (Throwable e)
		{
			e.printStackTrace();
		}
	}

	public void contextDestroyed(ServletContextEvent arg0)
	{

	}
	/**
	 * 给前台推送消息
	 * @param dataList
	 */
	public static void comet4J(List <JSONObject>dataList,Type type,Oper oper){
		Data cjData = new Data();
		cjData.setType(type);
		cjData.setOper(oper);
		cjData.setData(dataList);
		CometDataNoticeClientRunnable noticeClientRunnable = new CometDataNoticeClientRunnable(cjData);
		Thread noticeThread = new Thread(noticeClientRunnable);
		noticeThread.start();
	}
}