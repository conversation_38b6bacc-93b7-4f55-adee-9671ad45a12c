package com.tzx.pos.base.constant;

/**
 * 发票渠道 
 */

public enum InvoiceChannel {
	
	/**
	 * dzfp_posdcsfkjdzfp
	 */
	MD01("MD01", "1"), 
	
	/**
	 * dzfp_wxdndcsfkjdzfp
	 */
	WX02("WX02", "2"),
	
	/**
	 * dzfp_dsfwmsfkjdzfp_cc
	 */
	CC04("CC04", "4"),
	
	/**
	 * dzfp_dsfwmsfkjdzfp_bd
	 */
	BD06("BD06", "6"),
	
	/**
	 * dzfp_dsfwmsfkjdzfp_mt
	 */
	MT08("MT08", "8"),
	
	/**
	 * dzfp_dsfwmsfkjdzfp_el
	 */
	EL09("EL09", "9"),
	
	/**
	 * dzfp_wxwmsfkjdzfp
	 */
	WM10("WM10", "10"),
    /**
     * 新美大外卖
     */
    MT11("MT11","11"),
    /**
     * 沙绿自营外卖
     */
    TH01("TH01","11"),
	;
	
	private final String value;
	private final String label;
	
	private InvoiceChannel(String value, String label){
		this.value = value;
		this.label = label;
	}
	
	/**
	 * 获取键
	 * @return
	 */
	public String getValue(){
		return value;
	}
	
	/**
	 * 获取值
	 * @return
	 */
	public String getLabel(){
		return label;
	}
	
	/**
	 * 根据键取值
	 * @param value
	 * @return
	 */
	public static InvoiceChannel find(String value){
		if(value == null){
			return null;
		}
		for(InvoiceChannel s: values()){
			if(s.value.equals(value)){
				return s;
			}
		}
		return null;
	}
}
