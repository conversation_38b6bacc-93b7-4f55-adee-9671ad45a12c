package com.tzx.pos.task;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;

/**
 * 历史数据定时清理任务
 *
 * <AUTHOR> on 2018/6/26
 */
@Component
public class AutoStartPrintServer implements IPostConstruct {

    Logger logger = LoggerFactory.getLogger(AutoStartPrintServer.class);

    @Value("${print.server.home}")
    private String printServerHome;

    @Value("${print.server.launcher}")
    private String printServerLauncher;

    @PostConstruct
    @Override
    public void postConstructExecute() {
        String nebulaHome = System.getenv("NEBULA_BOH_HOME");


        if (StringUtils.isEmpty(nebulaHome)) {
            logger.warn("NEBULA_BOH_HOME环境变量未配置,无法启动打印服务!");
            return;
        }

        String path = nebulaHome + File.separator + printServerHome + File.separator + printServerLauncher;

        if (!new File(path).exists()) {
            logger.warn("未找到打印服务程序，无法启动打印服务!");
            return;
        }

        try {
            if(ProcessUtil.isProcessRunning(printServerLauncher)){
                logger.info("检测到打印服务已启动,跳过启动打印服务……");
                return;
            }
            Runtime.getRuntime().exec(path);
            logger.info("启动打印服务……");
        } catch (IOException e) {
            logger.warn("打印服务启动失败!", e);
        } catch (Exception e) {
            logger.warn("打印服务启动失败!", e);
        }

    }

    static class ProcessUtil {

        private static final String TASKLIST = "tasklist";
        private static final String KILL = "taskkill /F /IM ";

        public static boolean isProcessRunning(String serviceName) throws Exception {

            boolean isRunning = false;

            Process p = Runtime.getRuntime().exec(TASKLIST);
            BufferedReader reader = new BufferedReader(new InputStreamReader(
                    p.getInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {

                if (line.contains(serviceName)) {
                    isRunning = true;
                }
            }
            reader.close();
            return isRunning;
        }

        public static void killProcess(String serviceName) throws Exception {

            Runtime.getRuntime().exec(KILL + serviceName);
        }
    }
}
