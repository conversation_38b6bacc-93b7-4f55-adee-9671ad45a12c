package com.tzx.pos.bo.payment;

import java.sql.Timestamp;
import java.util.Date;

import net.sf.json.JSONObject;

import com.tzx.framework.common.entity.Data;
import com.tzx.pos.bo.PosBaseService;

public interface PaymentWayService extends PosBaseService
{	
	/** 插入付款中记录
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @param reportDate
	 * @param shiftId
	 * @param posNum
	 * @param optNum
	 * @param tableCode
	 * @param payment
	 * @param currentTime
	 * @throws Exception
	 */
	public void payment(String tenantId, int storeId, String billNum,String batchNum,Date reportDate,int shiftId, String posNum,String optNum,String tableCode,JSONObject payment,Timestamp currentTime,String isDebited) throws Exception;
	
	/** 扣款
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @param paymentAmount
	 * @param payment
	 * @param currentTime
	 * @param resultJson
	 * @param resultData
	 * @throws Exception
	 */
	public void paymentDebit(String tenantId, int storeId, String billNum,String orderNum,String chanel,Double paymentAmount,JSONObject payment,Timestamp currentTime,JSONObject resultJson,Data resultData) throws Exception;
	
//	/** 扣款成功
//	 * @param tenantId
//	 * @param storeId
//	 * @param billNum
//	 * @param payment
//	 * @throws Exception
//	 */
//	public void paymentDebitSuccess(String tenantId, int storeId, String billNum,JSONObject payment) throws Exception;
//	
//	/** 扣款失败
//	 * @param tenancyId
//	 * @param storeId
//	 * @param billNum
//	 * @param payId
//	 * @param code
//	 * @param msg
//	 * @throws Exception
//	 */
//	public void paymentDebitFailure(String tenancyId, int storeId,String billNum, int payId,int code ,String msg) throws Exception;
	
	/** 扣款状态查询
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param orderNum
	 * @param chanel
	 * @param paymentAmount
	 * @param payment
	 * @param currentTime
	 * @param resultJson
	 * @param resultData
	 * @throws Exception
	 */
	public void findPaymentDebit(String tenancyId, int storeId, String billNum,String orderNum,String chanel,Double paymentAmount,JSONObject payment,Timestamp currentTime,JSONObject resultJson,Data resultData) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param paymentJson
	 * @throws Exception
	 */
	public void paymentDebitResult(String tenancyId, int storeId, String billNum,JSONObject paymentJson) throws Exception;
	
	/** 退款
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param oldBillNum
	 * @param orderNum
	 * @param chanel
	 * @param billPaymentAmount
	 * @param payment
	 * @param currentTime
	 * @param resultJson
	 * @param resultData
	 * @param operType
	 * @throws Exception
	 */
	public void paymentRefund(String tenancyId, int storeId, String billNum,String oldBillNum,String orderNum,String chanel,Double billPaymentAmount,JSONObject payment,Timestamp currentTime,JSONObject resultJson,Data resultData,String operType) throws Exception;
	
//	/** 退款成功
//	 * @param tenancyId
//	 * @param storeId
//	 * @param billNum
//	 * @param payment
//	 * @throws Exception
//	 */
//	public void paymentRefundSuccess(String tenancyId, int storeId, String billNum,JSONObject payment) throws Exception;
//	
//	/** 退款失败
//	 * @param tenancyId
//	 * @param storeId
//	 * @param billNum
//	 * @param payId
//	 * @param code
//	 * @param msg
//	 * @throws Exception
//	 */
//	public void paymentRefundFailure(String tenancyId, int storeId,String billNum, int payId,int code ,String msg) throws Exception;
	
	/** 取消付款
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param oldBillNum
	 * @param orderNum
	 * @param chanel
	 * @param billPaymentAmount
	 * @param payment
	 * @param currentTime
	 * @param resultJson
	 * @param resultData
	 * @param operType
	 * @throws Exception
	 */
	public void paymentCancel(String tenancyId, int storeId, String billNum,String orderNum,String chanel,Double billPaymentAmount,JSONObject payment,Timestamp currentTime,JSONObject resultJson,Data resultData,String operType) throws Exception;
	
	/**恢复账单
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param oldBillNum
	 * @param orderNum
	 * @param chanel
	 * @param billPaymentAmount
	 * @param payment
	 * @param currentTime
	 * @param resultJson
	 * @param resultData
	 * @param operType
	 * @throws Exception
	 */
	public void paymentRegain(String tenancyId, int storeId, String billNum,String orderNum,String chanel,Double billPaymentAmount,JSONObject payment,Timestamp currentTime,JSONObject resultJson,Data resultData,String operType) throws Exception;
}
