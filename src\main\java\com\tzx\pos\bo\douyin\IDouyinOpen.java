package com.tzx.pos.bo.douyin;

import com.fasterxml.jackson.databind.JsonNode;
import com.mashape.unirest.http.exceptions.UnirestException;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR> at 2021-11-03
 */
public interface IDouyinOpen {


//    String baseUrl = "https://open.douyin.com";
//    String clientKey = "awbe35kzflcl03kx";
//    String clientSecret = "32d715403ded984201e5048c6596ec07";
    String grantType = "client_credential";


    String GET_ACCESS_TOKEN_URL = "/oauth/client_token/";
    /*String PREPARE_URL = "/namek/fulfilment/prepare/";
    String VERIFY_URL = "/namek/fulfilment/verify/";
    String CANCEL_URL = "/namek/fulfilment/cancel/";
    String QUERY_CERTIFICATE_URL = "/namek/fulfilment/query/certificate/";*/

    String PREPARE_URL = "/goodlife/v1/fulfilment/certificate/prepare/";
    String VERIFY_URL = "/goodlife/v1/fulfilment/certificate/verify/";
    String CANCEL_URL = "/goodlife/v1/fulfilment/certificate/cancel/";
    String QUERY_CERTIFICATE_URL = "/goodlife/v1/fulfilment/certificate/query/";


    /**
     * 生成client_token
     * https://open.douyin.com/platform/doc/6848806493387573256
     *
     * @return
     */
    String getAccessToken() throws UnirestException, IOException;

    /**
     * 验券准备接口
     * https://bytedance.feishu.cn/docs/doccngzPY6tIPlPKfYkI8DU2prh#l5BfYX
     */
    JsonNode prepare(String code) throws UnirestException, IOException;

    /**
     * 验券（核销）接口
     * https://bytedance.feishu.cn/docs/doccngzPY6tIPlPKfYkI8DU2prh#NAAZNV
     *
     * @return
     */
    JsonNode verify(String verifyToken, List<String> encryptedCodes, List<String> codes) throws UnirestException, IOException;

    /**
     * 撤销验券接口
     * https://bytedance.feishu.cn/docs/doccngzPY6tIPlPKfYkI8DU2prh#zKOu1L
     *
     * @return
     */
    JsonNode cancel(String verifyId, String certificateId, int timesCardCancelCount, String cancelToken) throws UnirestException, IOException;

    /***
     * 券状态查询接口
     * https://bytedance.feishu.cn/docs/doccngzPY6tIPlPKfYkI8DU2prh#0zMziv
     * @return
     */
    JsonNode queryCertificate();

}
