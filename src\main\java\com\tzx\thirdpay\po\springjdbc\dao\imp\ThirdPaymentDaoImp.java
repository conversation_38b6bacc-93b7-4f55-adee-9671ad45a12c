package com.tzx.thirdpay.po.springjdbc.dao.imp;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Repository;

import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.dao.imp.BaseDaoImp;
import com.tzx.pos.base.util.JsonUtil;
import com.tzx.pos.bo.dto.PosThirdPaymentOrderEntity;
import com.tzx.thirdpay.po.springjdbc.dao.ThirdPaymentDao;

import net.sf.json.JSONObject;

@Repository(ThirdPaymentDao.NAME)
public class ThirdPaymentDaoImp extends BaseDaoImp implements ThirdPaymentDao
{
	@Override
	public PosThirdPaymentOrderEntity getThirdPaymentByAidOrderNum(String tenancyId, int storeId, String aidOrderNum, String objectName) throws Exception
	{
		String querySql = "select * from pos_third_payment_order where tenancy_id=? and store_id=? and aid_order_num=? and object_name=? and (is_refunded is null or is_refunded=false)";

		List<PosThirdPaymentOrderEntity> list = (List<PosThirdPaymentOrderEntity>) this.query(querySql, new Object[]
		{ tenancyId, storeId, aidOrderNum, objectName },BeanPropertyRowMapper.newInstance(PosThirdPaymentOrderEntity.class));
		if (list != null && list.size() > 0)
		{
			return list.get(0);
		}
		return null;
	}
	
	@Override
	public PosThirdPaymentOrderEntity getThirdPaymentAllByAidOrderNum(String tenancyId, int storeId, String aidOrderNum, String objectName) throws Exception
	{
		String querySql = "select * from pos_third_payment_order where tenancy_id=? and store_id=? and aid_order_num=? and object_name=?";

		List<PosThirdPaymentOrderEntity> list = (List<PosThirdPaymentOrderEntity>) this.query(querySql, new Object[]
		{ tenancyId, storeId, aidOrderNum, objectName },BeanPropertyRowMapper.newInstance(PosThirdPaymentOrderEntity.class));
		if (list != null && list.size() > 0)
		{
			return list.get(0);
		}
		return null;
	}
	
	@Override
	public JSONObject getPayThirdByOrderNum(String tenancyId, int storeId, String order_no, String service_type, String pay_type, String thirdPayCharge) throws Exception
	{
		String querySql = "select * from pos_third_payment_order where tenancy_id=? and store_id=? and order_num=? and service_type=? and payment_class=? and object_name=? and (is_refunded is null or is_refunded=false)";

		List<JSONObject> list = this.query4Json(tenancyId, querySql, new Object[]
		{ tenancyId, storeId, order_no, service_type, pay_type, thirdPayCharge });
		if (list != null && list.size() > 0)
		{
			return list.get(0);
		}
		return null;
	}
	
	@Override
	public List<PosThirdPaymentOrderEntity> getThirdPaymentOrderForNotComplete(String tenancyId, int storeId,Timestamp queryTime,Double queryCount) throws Exception
	{
		StringBuffer querySql =new StringBuffer("select * from pos_third_payment_order where tenancy_id=? and store_id=? ");
//		querySql.append(" and (is_refunded is null or is_refunded=false) and (is_succeed is null or is_succeed = false) ");
		//2017年8月15日起，田老师大排档SAAS POS数据上传失败
		querySql.append(" and is_refunded=false and is_succeed = false ");
		List<Object> list=new ArrayList<Object>();
		list.add(tenancyId);
		list.add(storeId);
		if (null != queryCount && 0 < queryCount)
		{
			querySql.append(" and (query_count is null or query_count < ?) ");
			list.add(queryCount);
		}
		Object[]o=list.toArray();
//		return this.query4Json(tenancyId, querySql.toString(), o);
		return (List<PosThirdPaymentOrderEntity>) this.query(querySql.toString(), o, BeanPropertyRowMapper.newInstance(PosThirdPaymentOrderEntity.class));
	}
	
	/**
	 * 插入支付信息记录
	 *
	 * @param orderParam
	 * @param tenantId
	 * @return
	 * @throws Exception
	 */
	@Override
	public Integer insertThirdPaymentOrder(String tenantId, JSONObject orderParam) throws Exception
	{
		return (Integer) this.insertIgnorCase(tenantId, "pos_third_payment_order", orderParam);
	}

	@Override
	public Integer insertThirdPaymentOrder(String tenantId, PosThirdPaymentOrderEntity orderParam) throws Exception
	{
		return (Integer) this.insertIgnorCase(tenantId, "pos_third_payment_order", JsonUtil.fromObject(orderParam));
	}
	
	/**
	 * 修改支付信息记录
	 * @param id
	 * @param tenantId
	 * @param orderParam
	 * @throws Exception
	 */
	@Override
	public void updateThirdPaymentOrder(int id ,String tenantId,JSONObject orderParam) throws Exception {
		orderParam.put("id",id);
		this.updateIgnorCase(tenantId,"pos_third_payment_order",orderParam);

	}
	
	@Override
	public void updateThirdPaymentOrderForQuery(String tenancyId, int storeId, int paymentId, Timestamp queryTime, int queryCount) throws Exception
	{
		String querySql = "update pos_third_payment_order set query_time=?,query_count=? where tenancy_id=? and store_id=? and id=?";
		this.update(querySql.toString(), new Object[]{queryTime,queryCount,tenancyId,storeId,paymentId});
	}
	
	@Override
	public String getBillNumByOrderNo(String tenancyId, int storeId, String orderNo) throws Exception
	{
		String billNum = "";
		StringBuilder billAmountSql = new StringBuilder();
		billAmountSql.append("select b.bill_num from pos_bill b where (b.bill_num = ? or b.order_num = ?) and b.store_id = ? and b.tenancy_id = ?");
		SqlRowSet rs = this.query4SqlRowSet(billAmountSql.toString(), new Object[]
		{ orderNo, orderNo, storeId, tenancyId });
		if (rs.next())
		{
			billNum = rs.getString("bill_num");
		}
		return billNum;
	}
	
	@Override
	public JSONObject getBillPaymentByJzid(String tenancyId, int storeId, String billNum, int jzid) throws Exception
	{
		String sql = new String("select * from pos_bill_payment where tenancy_id=? and store_id = ? and bill_num = ? and jzid = ?");
		List<JSONObject> paymentList = this.query4Json(tenancyId, sql, new Object[]
		{ tenancyId, storeId, billNum, jzid });

		JSONObject resultJson = new JSONObject();
		if (null != paymentList && paymentList.size() > 0)
		{
			resultJson = paymentList.get(0);
		}
		return resultJson;
	}
	
	@Override
	public JSONObject getPosBillPaymentByBillnum(String tenancyId,int storeId,String billNum,Integer paymentId) throws Exception
	{
		JSONObject resultJson = new JSONObject();
		StringBuilder billAmountSql = new StringBuilder();
		billAmountSql.append(" select p.id, p.bill_num,p.type, p.jzid, p.name, p.name_english, p.amount, p.count, p.number, p.phone, p.report_date, p.shift_id, p.pos_num, p.cashier_num, p.last_updatetime, p.currency_amount, p.customer_id, p.bill_code, p.payment_state as item_payment_state, p.batch_num, p.more_coupon, p.fee, p.fee_rate, p.coupon_type,p.rate,");
		billAmountSql.append(" coalesce(b.bill_amount,0) as bill_amount,coalesce(b.payment_amount,0) as payment_amount,b.source,b.bill_property,coalesce(b.table_code,'') as table_code,coalesce(b.difference,0)  as difference,coalesce(b.discount_amount,0) as discount_amount,sale_mode,coalesce(b.order_num,'') as order_num,b.copy_bill_num,b.payment_state,b.bill_state");
		billAmountSql.append(" from pos_bill_payment p left join pos_bill b on b.tenancy_id=p.tenancy_id and b.store_id=p.store_id and b.bill_num=p.bill_num");
		billAmountSql.append(" where b.tenancy_id = ? and b.store_id = ? and b.bill_num = ? and p.jzid=?");
		
		List<JSONObject> list = this.query4Json(tenancyId, billAmountSql.toString(), new Object[]
		{  tenancyId, storeId, billNum,paymentId});

		if (list != null && list.size() > 0)
		{
			resultJson = list.get(0);
		}
		return resultJson;
	}
	
	@Override
	public List<JSONObject> getPosBillPaymentByCancBill(String tenancyId,int storeId,String billNum,Integer paymentId) throws Exception
	{
		StringBuilder billAmountSql = new StringBuilder();
		billAmountSql.append(" select p.id, p.bill_num,p.type, p.jzid, p.name, p.name_english, p.amount, p.count, p.number, p.phone, p.report_date, p.shift_id, p.pos_num, p.cashier_num, p.last_updatetime, p.currency_amount, p.customer_id, p.bill_code, p.payment_state as item_payment_state, p.batch_num, p.more_coupon, p.fee, p.fee_rate, p.coupon_type,p.rate,");
		billAmountSql.append(" coalesce(b.bill_amount,0) as bill_amount,coalesce(b.payment_amount,0) as payment_amount,b.source,b.bill_property,coalesce(b.table_code,'') as table_code,coalesce(b.difference,0)  as difference,coalesce(b.discount_amount,0) as discount_amount,sale_mode,coalesce(b.order_num,'') as order_num,b.copy_bill_num,b.payment_state,b.bill_state");
		billAmountSql.append(" from pos_bill_payment p left join pos_bill b on b.tenancy_id=p.tenancy_id and b.store_id=p.store_id and b.bill_num=p.bill_num");
		billAmountSql.append(" where b.tenancy_id = ? and b.store_id = ? and b.copy_bill_num = ? and p.jzid=? and b.bill_state=?");

		return this.query4Json(tenancyId, billAmountSql.toString(), new Object[]
		{ tenancyId, storeId, billNum, paymentId, SysDictionary.BILL_STATE_CJ01 });
	}
	
	@Override
	public List<JSONObject> getPosBillPaymentByRegainBill(String tenancyId,int storeId,String billNum,Integer paymentId) throws Exception
	{
		StringBuilder billAmountSql = new StringBuilder();
		billAmountSql.append(" select p.id, p.bill_num,p.type, p.jzid, p.name, p.name_english, p.amount, p.count, p.number, p.phone, p.report_date, p.shift_id, p.pos_num, p.cashier_num, p.last_updatetime, p.currency_amount, p.customer_id, p.bill_code, p.payment_state as item_payment_state, p.batch_num, p.more_coupon, p.fee, p.fee_rate, p.coupon_type,p.rate,");
		billAmountSql.append(" coalesce(b.bill_amount,0) as bill_amount,coalesce(b.payment_amount,0) as payment_amount,b.source,b.bill_property,coalesce(b.table_code,'') as table_code,coalesce(b.difference,0) as difference,coalesce(b.discount_amount,0) as discount_amount,sale_mode,coalesce(b.order_num,'') as order_num,b.copy_bill_num,b.payment_state,b.bill_state");
		billAmountSql.append(" from pos_bill_payment_regain p left join pos_bill_regain b on b.tenancy_id=p.tenancy_id and b.store_id=p.store_id and b.bill_num=p.bill_num and b.regain_count=p.regain_count");
		billAmountSql.append(" where b.tenancy_id = ? and b.store_id = ? and b.bill_num = ? and p.jzid=? ");

		return this.query4Json(tenancyId, billAmountSql.toString(), new Object[]
				{ tenancyId, storeId, billNum, paymentId });
	}
}
