package com.tzx.clientorder.common.entity;

import java.util.Date;

public class AcewillPosBillPayment {

	/**
	 * 商户id
	 */
	private String tenancy_id;
	/**
	 * 机构id
	 */
	private Integer store_id;
	/**
	 * ID-表自增
	 */
	private Integer id;
	/**
	 * 账单编号
	 */
	private String bill_num;
	/**
	 * 桌位编号
	 */
	private String table_code;
	/**
	 * 付款类型属性
	 */
	private String type;
	/**
	 * 付款方式编号
	 */
	private Integer jzid;
	/**
	 * 付款方式名称1
	 */
	private String name;
	/**
	 * 付款方式名称2-其他语言
	 */
	private String name_english;
	/**
	 * 付款金额
	 */
	private Double amount;
	/**
	 * 付款数量
	 */
	private Integer count;
	/**
	 * 付款号码
	 */
	private String number;
	/**
	 * 联系电话
	 */
	private String phone;
	/**
	 * 报表日期
	 */
	private Date report_date;
	/**
	 * 结账班次ID
	 */
	private Integer shift_id;
	/**
	 * 结账收款机号
	 */
	private String pos_num;
	/**
	 * 收款员号
	 */
	private String cashier_num;
	/**
	 * 操作时间/结账时间
	 */
	private Date last_updatetime;
	/**
	 * 是否预收款
	 */
	private String is_ysk;
	/**
	 * 利率
	 */
	private Double rate;
	/**
	 * 本币金额
	 */
	private Double currency_amount;
	/**
	 * 上传标记
	 */
	private Integer upload_tag;
	/**
	 * 挂账人id
	 */
	private Integer customer_id;
	/**
	 * 付款流水号
	 */
	private String bill_code;
	/**
	 * 付款备注
	 */
	private String remark;
	/**
	 * 支付状态
	 */
	private String payment_state;
	/**
	 * 与总部通讯的缓存
	 */
	private String param_cach;
	/**
	 * 批次编号
	 */
	private String batch_num;
	/**
	 * 多收礼卷
	 */
	private Double more_coupon;
	/**
	 * 服务费
	 */
	private Double fee;
	/**
	 * 费用率
	 */
	private Double fee_rate;
	/**
	 * 优惠券类型
	 */
	private Integer coupon_type;
	/**
	 * 原付款方式ID
	 */
	private Integer yjzid;
	/**
	 * 用户实付
	 */
	private Double coupon_buy_price;
	/**
	 * 券账单净收
	 */
	private Double due;
	/**
	 * 商家优惠承担
	 */
	private Double tenancy_assume;
	/**
	 * 第三方优惠承担
	 */
	private Double third_assume=0d;
	/**
	 * 第三方票券服务费
	 */
	private Double third_fee=0d;
	
	private String payment_uid;
	
	public String getTenancy_id() {
		return tenancy_id;
	}
	public void setTenancy_id(String tenancy_id) {
		this.tenancy_id = tenancy_id;
	}
	public Integer getStore_id() {
		return store_id;
	}
	public void setStore_id(Integer store_id) {
		this.store_id = store_id;
	}
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public String getBill_num() {
		return bill_num;
	}
	public void setBill_num(String bill_num) {
		this.bill_num = bill_num;
	}
	public String getTable_code() {
		return table_code;
	}
	public void setTable_code(String table_code) {
		this.table_code = table_code;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public Integer getJzid() {
		return jzid;
	}
	public void setJzid(Integer jzid) {
		this.jzid = jzid;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getName_english() {
		return name_english;
	}
	public void setName_english(String name_english) {
		this.name_english = name_english;
	}
	public Double getAmount() {
		return amount;
	}
	public void setAmount(Double amount) {
		this.amount = amount;
	}
	public Integer getCount() {
		return count;
	}
	public void setCount(Integer count) {
		this.count = count;
	}
	public String getNumber() {
		return number;
	}
	public void setNumber(String number) {
		this.number = number;
	}
	public String getPhone() {
		return phone;
	}
	public void setPhone(String phone) {
		this.phone = phone;
	}
	public Date getReport_date() {
		return report_date;
	}
	public void setReport_date(Date report_date) {
		this.report_date = report_date;
	}
	public Integer getShift_id() {
		return shift_id;
	}
	public void setShift_id(Integer shift_id) {
		this.shift_id = shift_id;
	}
	public String getPos_num() {
		return pos_num;
	}
	public void setPos_num(String pos_num) {
		this.pos_num = pos_num;
	}
	public String getCashier_num() {
		return cashier_num;
	}
	public void setCashier_num(String cashier_num) {
		this.cashier_num = cashier_num;
	}
	public Date getLast_updatetime() {
		return last_updatetime;
	}
	public void setLast_updatetime(Date last_updatetime) {
		this.last_updatetime = last_updatetime;
	}
	public String getIs_ysk() {
		return is_ysk;
	}
	public void setIs_ysk(String is_ysk) {
		this.is_ysk = is_ysk;
	}
	public Double getRate() {
		return rate;
	}
	public void setRate(Double rate) {
		this.rate = rate;
	}
	public Double getCurrency_amount() {
		return currency_amount;
	}
	public void setCurrency_amount(Double currency_amount) {
		this.currency_amount = currency_amount;
	}
	public Integer getUpload_tag() {
		return upload_tag;
	}
	public void setUpload_tag(Integer upload_tag) {
		this.upload_tag = upload_tag;
	}
	public Integer getCustomer_id() {
		return customer_id;
	}
	public void setCustomer_id(Integer customer_id) {
		this.customer_id = customer_id;
	}
	public String getBill_code() {
		return bill_code;
	}
	public void setBill_code(String bill_code) {
		this.bill_code = bill_code;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public String getPayment_state() {
		return payment_state;
	}
	public void setPayment_state(String payment_state) {
		this.payment_state = payment_state;
	}
	public String getParam_cach() {
		return param_cach;
	}
	public void setParam_cach(String param_cach) {
		this.param_cach = param_cach;
	}
	public String getBatch_num() {
		return batch_num;
	}
	public void setBatch_num(String batch_num) {
		this.batch_num = batch_num;
	}
	public Double getMore_coupon() {
		return more_coupon;
	}
	public void setMore_coupon(Double more_coupon) {
		this.more_coupon = more_coupon;
	}
	public Double getFee() {
		return fee;
	}
	public void setFee(Double fee) {
		this.fee = fee;
	}
	public Double getFee_rate() {
		return fee_rate;
	}
	public void setFee_rate(Double fee_rate) {
		this.fee_rate = fee_rate;
	}
	public Integer getCoupon_type() {
		return coupon_type;
	}
	public void setCoupon_type(Integer coupon_type) {
		this.coupon_type = coupon_type;
	}
	public Integer getYjzid() {
		return yjzid;
	}
	public void setYjzid(Integer yjzid) {
		this.yjzid = yjzid;
	}
	public Double getCoupon_buy_price() {
		return coupon_buy_price;
	}
	public void setCoupon_buy_price(Double coupon_buy_price) {
		this.coupon_buy_price = coupon_buy_price;
	}
	public Double getDue() {
		return due;
	}
	public void setDue(Double due) {
		this.due = due;
	}
	public Double getTenancy_assume() {
		return tenancy_assume;
	}
	public void setTenancy_assume(Double tenancy_assume) {
		this.tenancy_assume = tenancy_assume;
	}
	public Double getThird_assume() {
		return third_assume;
	}
	public void setThird_assume(Double third_assume) {
		this.third_assume = third_assume;
	}
	public Double getThird_fee() {
		return third_fee;
	}
	public void setThird_fee(Double third_fee) {
		this.third_fee = third_fee;
	}
	public String getPayment_uid()
	{
		return payment_uid;
	}
	public void setPayment_uid(String payment_uid)
	{
		this.payment_uid = payment_uid;
	}
	
}
