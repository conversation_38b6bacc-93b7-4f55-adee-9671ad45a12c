package com.tzx.pos.bo.dto;

import java.util.List;

/**
 * <AUTHOR> on 2019-03-07
 */
public class PrinterBg {

    private String id;
    private String bgcode;
    private String bgname;
    private int bgorder;
    private int printer_id;
    private String printer_address;
    private String printer_name;
    private List<Item> item;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getBgcode() {
        return bgcode;
    }

    public void setBgcode(String bgcode) {
        this.bgcode = bgcode;
    }

    public String getBgname() {
        return bgname;
    }

    public void setBgname(String bgname) {
        this.bgname = bgname;
    }

    public int getBgorder() {
        return bgorder;
    }

    public void setBgorder(int bgorder) {
        this.bgorder = bgorder;
    }

    public int getPrinter_id() {
        return printer_id;
    }

    public void setPrinter_id(int printer_id) {
        this.printer_id = printer_id;
    }

    public String getPrinter_address() {
        return printer_address;
    }

    public void setPrinter_address(String printer_address) {
        this.printer_address = printer_address;
    }

    public String getPrinter_name() {
        return printer_name;
    }

    public void setPrinter_name(String printer_name) {
        this.printer_name = printer_name;
    }

    public List<Item> getItem() {
        return item;
    }

    public void setItem(List<Item> item) {
        this.item = item;
    }

    public static class Item {
        private int item_id;
        private int item_order;
        private String item_name;

        public int getItem_id() {
            return item_id;
        }

        public void setItem_id(int item_id) {
            this.item_id = item_id;
        }

        public int getItem_order() {
            return item_order;
        }

        public void setItem_order(int item_order) {
            this.item_order = item_order;
        }

        public String getItem_name() {
            return item_name;
        }

        public void setItem_name(String item_name) {
            this.item_name = item_name;
        }
    }
}
