package com.tzx.pos.bo.imp;

import com.tzx.base.bo.PosCodeService;
import com.tzx.framework.common.constant.Code;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.DoubleHelper;
import com.tzx.framework.common.util.Tools;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.base.util.ReqDataUtil;
import com.tzx.pos.bo.PosActivityService;
import com.tzx.pos.bo.PosDishService;
import com.tzx.pos.bo.dto.PosBill;
import com.tzx.pos.po.springjdbc.dao.PosActivityDao;
import com.tzx.pos.po.springjdbc.dao.PosDishDao;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Time;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;


/**
 * 
 * <AUTHOR> 2017年6月2日-上午10:49:47
 */
@Service(PosActivityService.NAME)
public class PosActivityServiceImp extends PosBaseServiceImp implements PosActivityService {
	private static final Logger	logger	= Logger.getLogger(PosActivityServiceImp.class);

	@Resource(name = PosDishDao.NAME)
	private PosDishDao posDishDao;
	
	@Resource(name= PosCodeService.NAME)
    PosCodeService codeService;
	
	@Resource(name= PosActivityDao.NAME)
    PosActivityDao posActivityDao;

	@Resource(name = PosActivityService.NAME)
	PosActivityService posActivityService;

	@Resource(name = PosDishService.NAME)
	PosDishService posDishService;
	/**
	 * 有效活动查询
	 * 
	 * @param param
	 * @param result
	 * @throws SystemException
	 */
	@SuppressWarnings("unchecked")
	@Override
	public void getValidActivity(Data param, Data result)
			throws SystemException {
		try {
			String tenancyId = param.getTenancy_id();// 门店id
			Integer storeId = param.getStore_id();// 机构ID
			String source = param.getSource();

			Map<String, Object> map = ReqDataUtil.getDataMap(param);
			String billNum = ParamUtil.getStringValue(map, "bill_code", true,
					PosErrorCode.NOT_NULL_BILL_NUM);// 账单号
			Double billMoney = 0.00;// 传入的菜品总金额
			String reportDateString = ParamUtil.getStringValue(map,
					"report_date", false, null);// 报表日期
			
			String channel = ParamUtil.getStringValue(map,
					"chanel", false, null);		
            if (channel==null||channel.equals("")){
            	channel = "MD01";
            }
			String isMember = ParamUtil.getStringValue(map,
					"is_member", false, null);// 是否是会员0不是会员1是会员
			String chanel = ParamUtil.getStringValue(map,
					"chanel", false, null);// 渠道
			Integer isAutoactivity = ParamUtil.getIntegerValue(map,
					 "is_auto_activity", false, null);// 是否是自动执行的活动0否1是
			List<Map<String, Object>> items = (List<Map<String, Object>>) map
					.get("item");//菜品
			List<Map<String, Object>> usedActivityList = (List<Map<String, Object>>) map
					.get("activities");//使用的活动信息
			//结果列表
			List<JSONObject> listresult = new ArrayList<JSONObject>();
			//有效的活动集合列表
			List<JSONObject> activityList = posActivityDao.getValidActivity(tenancyId, storeId,chanel);
			if(SysDictionary.SOURCE_MOVE.contains(source)){
				List<JSONObject> usedActivityList1 = posActivityDao.getUsedActivityInfo(tenancyId,storeId,billNum);//使用过的活动
				if(usedActivityList1.size() >0 && usedActivityList1 !=null){
					HashMap<String, Object> data = new HashMap<String, Object>();  
					for(JSONObject jsonObject : usedActivityList1){
						 Iterator it = jsonObject.keys(); 
						 while (it.hasNext())  
					       {  
					           String key = String.valueOf(it.next());  
					           String value =  jsonObject.get(key)+"";  
					           data.put(key, value);  
					       }  
						 usedActivityList.add(data);
					}
				}
			}
			//账单已经使用的活动数据集合列表 06-29修改方案废弃
			//List<JSONObject> usedActivityList = posActivityDao.getUsedActivityInfo(tenancyId,storeId,billNum);
			//使用活动又取消的菜品数据集合列表(activity_id is null) 06-29修改方案废弃
			/*List<?> cancelActivityItemMapList = posActivityDao.getcancelActivityItemInfo(tenancyId,storeId,billNum);
			for(int i = 0;i<cancelActivityItemMapList.size();i++){
				Map<String,Object> tempmap = (Map<String, Object>) cancelActivityItemMapList.get(i);
				items.add(tempmap);
			}*/
			
			//循环传入菜品计算总金额
			for (int r = 0; r < items.size(); r++)
			{
				Map<String, Object> detail = (Map<String, Object>) items.get(r);
				String item_property = detail.get("item_property").toString();
				if(!item_property.equalsIgnoreCase("MEALLIST")){
					if(item_property.equalsIgnoreCase("SETMEAL")){//是套餐					
						billMoney = billMoney + Double.parseDouble(detail.get("assist_money").toString()) + Double.parseDouble(detail.get("item_price").toString()) * Double.parseDouble(detail.get("item_count").toString());
					}else{
						billMoney = billMoney + Double.parseDouble(detail.get("item_price").toString()) * Double.parseDouble(detail.get("item_count").toString());
					}
                    //加上口味做法金额
                    billMoney = billMoney + Double.parseDouble(detail.get("method_money").toString());
				}
				
			}


			firstListFor: for (JSONObject json : activityList) {
				Integer activityId = json.getInt("id");
				String ifAuto = json.optString("if_auto");//活动是否自动执行
				if(isAutoactivity == 1 && ifAuto.equals("0")){
					continue;
				}
				if(usedActivityList.size() >0 && usedActivityList !=null){
					for(int i = 0;i<usedActivityList.size();i++){
						if(usedActivityList.get(i).get("if_parallel").equals("0") && !usedActivityList.get(i).get("activity_id").equals(activityId+"")){//使用的活动里不与其他活动并用
							logger.info("使用的活动activity_id:"+usedActivityList.get(i).get("activity_id")+"里不与其他活动activityId:"+activityId+"并用");
							continue firstListFor;
						}
					}
				}
				String startTime = json.getString("start_time");
				String endTime = json.getString("end_time");
				String applyDate = json.getString("apply_date");// 使用周期
				String ifAllDay = json.optString("if_all_day");//是否全天: 0不限制，是全天1自定义不是全天
				String ifNoLimit = json.optString("if_no_limit");//不适用时间是否限制： 0不限制；1限制
				String ifAllItem = json.optString("if_all_item");//是否全部商品可用 0不可用；1可用
				String allDaytimes = json.optString("alldaytimes");//适用时段
				String noLimittimes = json.optString("nolimittimes");//不适用时间
				String details = json.optString("details");//活动主规则：赠菜，加价购等 满减:100_10_0_0_1,200_20_0_0_2
				String targetGroup = json.optString("target_group");//目标人群,1全部，2会员，3非会员
				String memberGroupid = json.optString("member_group_id");//会员群体id
				Integer billLimitTimes = json.optInt("bill_limit_times");//账单限制使用次数
				Integer ifLoop = json.optInt("if_loop");//活动是否循环执行，0:否, 1是
				Integer ifParallel = json.optInt("if_parallel");//活动是否与其他活动并用，0:否, 1是
				String subject = json.optString("subject");//活动主题
				String introduction = json.optString("introduction");//活动介绍
				String activityType = json.optString("activity_type");//活动类型
				String activityArea = json.optString("activity_area");//活动区域
				String thirdBillCode = json.optString("third_bill_code");//第三方编号
				String ifAlipayCompresence = json.optString("if_alipay_compresence");//活动是否与支付宝优惠并存：0不并存，1并存
				String organType = json.optString("organ_type");//机构类型
				//Modify By SunFumeng 2017-8-16 不要使用报表日期，应该使用当前日期
				//Modify --Bak --Begin
				//String nowweek = DateUtil.dayForWeek(reportDateString) + "";// 返回当天是星期几
				//Modify --Bak --End
				//Modify --Add --Begin
				String nowweek = DateUtil.dayForWeek(DateUtil.getNowDateYYDDMM()) + "";
				//Modify --Add --End
				String sysDateTime = DateUtil.format(DateUtil.currentTimestamp(), "yyyy-MM-dd");
				long millionSeconds = DateUtil.getMillionSeconds(sysDateTime);
				
				//Add By SunFumeng 2017-8-10 
				//第二份半价和折后统一价的特殊处理变量
				Boolean defbjRule = false; //是否点了享受第二份半价活动的商品
				Boolean zhtyjRule = false;//是否全部点了折后统一价的商品
				//Add End
				List<JSONObject> detailList = new ArrayList<JSONObject>();
				if(details.length() > 0){
					String[] detailsArr = details.split(",");
					String[] detailMin = detailsArr[0].split("_");
					if((billMoney-Double.parseDouble(detailMin[0])) < 0){
						logger.info("activityid:"+activityId+"--所点菜品金额不满足活动“最低”金额条件");
						continue;
					}
					if(activityType.equals(SysDictionary.MARKETING_ACTIVITY_MEJXJ) || activityType.equals(SysDictionary.MARKETING_ACTIVITY_MEDZ)){
						for(int s = detailsArr.length;s > 0;s --){
							JSONObject temjson = new JSONObject();
							String[] detailArr = detailsArr[s-1].split("_");
							if((billMoney - Double.parseDouble(detailArr[0])) >= 0){
								temjson.put("reach_amount", detailArr[0]);
								temjson.put("discount_amount", detailArr[1]);
								temjson.put("item_id", detailArr[2]);
								temjson.put("item_num", detailArr[3]);
								temjson.put("rule_id", detailArr[4]);
                                temjson.put("item_unit_id", detailArr[5]);
								detailList.add(temjson);
								break;
							}
						}
					//   add by SunFumeng 2017-8-9
					//  修改原因:获取加价购或赠菜的活动要判断菜品是否在本地库中真实存在，否则会造成前端显示错误
					}else if (activityType.equals(SysDictionary.MARKETING_ACTIVITY_MEZC) || activityType.equals(SysDictionary.MARKETING_ACTIVITY_MEJXJ)){
						
						List<JSONObject> itemInfoList = null;//赠送菜品的信息列表
//						List<JSONObject> detailItem = new ArrayList<JSONObject>();
						String detailItemIds = "";
						//将数字转换为JsonList
						for (String detail:detailsArr) {
//							JSONObject temjson = new JSONObject();
							String[] detailArr = detail.split("_");
//							temjson.put("item_id", detailArr[2]);
//							detailItem.add(temjson);
							if (detailItemIds.equals("")){
								detailItemIds = detailArr[2];
							}else{
								detailItemIds = detailItemIds + "," + detailArr[2];
							}
							
						}
						//获取本地菜品中真实存在的菜品信息
//						itemInfoList = posDishDao.getHqItemInfoByItem(
//								tenancyId, storeId, chanel, detailItem);//不要用这个方法了，这个方法是左连接的，查出来的是hq_item_info的，不对。
						
						//这里是从同步基础资料拿过来的语句，这才是正确的真正Pos里的内容。
						StringBuilder str = new StringBuilder("select distinct himd.item_id from hq_item_info as hii ");
						str.append(" left join hq_item_menu_details as himd on hii.id = himd.item_id ");
						str.append(" left join hq_item_menu_organ as himo on himd.item_menu_id = himo.item_menu_id ");
						str.append(" left join hq_item_menu him on himo.item_menu_id = him.id ");
						str.append(" left join hq_item_menu_class as himc on himc.details_id = himd.id ");
						str.append(" left join hq_item_class as hic on himc.class = hic.id ");
						str.append(" left join hq_item_unit as hiu on hii.id = hiu.item_id ");
						str.append(" left join organ o on himo.store_id = o.id ");
						str.append(" left join hq_item_pricesystem hip on hiu.id=hip.item_unit_id and hip.chanel=himc.chanel and hip.price_system||'' =o.price_system ");
						str.append(" where hiu.is_default = 'Y' and him.valid_state = '1' and hiu.valid_state = '1' and hii.valid_state = '1' and himc.chanel='"+channel+"' and himo.store_id = " + storeId);
//						str.append(" and hii.item_code in (SELECT item_code FROM hq_item_authorization where tenancy_id='"+tenancyId+"' and store_id="+storeId+")");
						if(!detailItemIds.equals("")){
							str.append(" and himd.item_id in (" + detailItemIds + ")");
						}
						
						str.append(" order by himd.item_id asc ");
						String itemIDs = "";
						itemInfoList = posDishDao.query4Json(tenancyId, str.toString());
						for (JSONObject jsonObject : itemInfoList) {
							
							itemIDs = itemIDs + ";" + jsonObject.getString("item_id");
						}
						itemIDs = itemIDs + ";";
						for(String detail:detailsArr){
							JSONObject temjson = new JSONObject();
							String[] detailArr = detail.split("_");
							//只有真实数据库中有的菜添加进去
							if (itemIDs.indexOf(";" + detailArr[2] + ";") > -1){
								temjson.put("reach_amount", detailArr[0]);
								temjson.put("discount_amount", detailArr[1]);
								temjson.put("item_id", detailArr[2]);
								temjson.put("item_num", detailArr[3]);
								temjson.put("rule_id", detailArr[4]);
                                temjson.put("item_unit_id", detailArr[5]);
								detailList.add(temjson);								
							}

						}						
						//Add End	
						
						//Add By SunFumeng 2017-8-10
						//增加原因：折后统一价和第二份半价规则发生改变：二者都可以设置商品规则，再设置享受活动的商品。
						//所以这里要增加是否已经点了已经享受活动的商品的判断。另外，第二份半价的活动商品要增加过滤掉
						//未点商品的活动详情不返回的过滤。比如设置可乐、雪碧、芬达第二份半价，但是只点了可乐，那么details
						//数组就应该只有可乐
						//Add --Begin
					}else if (SysDictionary.MARKETING_ACTIVITY_DEFBJ.equals(activityType) ){
						
						for(String detail:detailsArr){//循环活动设置的可享受半价的商品
							
							String[] detailArr = detail.split("_");
							int itemDish_Cnt = 0;//当前菜品个数
							defbjitemFor:for (int k =0;k<items.size();k++) {
                                //当前商品个数+1，如果第二份半价点的菜item_count大于等于2
								double itemCount = Double.parseDouble(items.get(k).get("item_count").toString());
								// 排除套餐明细
								if(!SysDictionary.ITEM_PROPERTY_MEALLIST.equals(items.get(k).get("item_property").toString())){
                                    if(itemCount >= 2) {
                                        for(int i = 0 ; i < itemCount ; i++) {
                                            String unit_id = items.get(k).get("unit_id") == null ? "" : items.get(k).get("unit_id").toString();
                                            if (items.get(k).get("item_id").equals(detailArr[2]) && unit_id.equals(detailArr[5])){
                                                itemDish_Cnt ++;
                                            }
                                        }
                                    }else {
                                        String unit_id = items.get(k).get("unit_id") == null ? "" : items.get(k).get("unit_id").toString();
                                        if (items.get(k).get("item_id").equals(detailArr[2]) && unit_id.equals(detailArr[5])) {
                                            itemDish_Cnt++;
                                        }
                                    }
                                }
								//只要找到一个可享受活动的商品，那么就认为满足活动条件了,并且只把满足条件的
								//商品放到Details中								
								if(itemDish_Cnt>=2){
									defbjRule = true;//第二份半价是否满足的标识设置为True
									JSONObject temjson = new JSONObject();
									temjson.put("reach_amount", detailArr[0]);
									temjson.put("discount_amount", detailArr[1]);
									temjson.put("item_id", detailArr[2]);
									temjson.put("item_num", detailArr[3]);
									temjson.put("rule_id", detailArr[4]);
                                    temjson.put("item_unit_id", detailArr[5]);
									detailList.add(temjson);	
									break defbjitemFor;								
								}
							}
						}						
						
					}else if (SysDictionary.MARKETING_ACTIVITY_ZHTYJ.equals(activityType) ){
						int rule_Cnt = 0;//满足条件的Detail个数
						for(String detail:detailsArr){//循环活动设置的可享受半价的商品
							int itemDish_Cnt = 0;//当前菜品个数
							String[] detailArr = detail.split("_");
							zhtyjitemFor:for (int k =0;k<items.size();k++) {
								//只要找到一个可享受活动的商品，那么就认为满足活动条件了,并且只把满足条件的
								//商品放到Details中，如果第二份半价点的菜item_count大于等于2
                                double itemCount = Double.parseDouble(items.get(k).get("item_count").toString());
								// 排除套餐明细
								if(!SysDictionary.ITEM_PROPERTY_MEALLIST.equals(items.get(k).get("item_property").toString())) {
									if (itemCount >= 2) {
										for (int i = 0; i < itemCount; i++) {
                                            String unit_id = items.get(k).get("unit_id") == null ? "" : items.get(k).get("unit_id").toString();
											if (items.get(k).get("item_id").equals(detailArr[2]) && unit_id.equals(detailArr[5])) {
												itemDish_Cnt++;
											}
										}
									} else {
                                        String unit_id = items.get(k).get("unit_id") == null ? "" : items.get(k).get("unit_id").toString();
										if (items.get(k).get("item_id").equals(detailArr[2]) && unit_id.equals(detailArr[5])) {
											itemDish_Cnt++;
										}
									}
								}
                                //如果找到的已点菜品个数超过了设置的个数，那么就不再循环这个菜了
                                if (itemDish_Cnt>=Integer.parseInt(detailArr[3])){
                                    //满足了条件的的Detail个数+1
                                    rule_Cnt ++;
                                    break zhtyjitemFor;
                                }
							}
						}		
						//如果满足条件的Detail个数和数组的Detial总个数相同，那么就认为全部可享活动商品都满足了，那么这个活动也满足条件
						if (rule_Cnt == detailsArr.length){
							zhtyjRule = true;
							//满足条件后再插入DetailList
							for(String detail:detailsArr){
								JSONObject temjson = new JSONObject();
								String[] detailArr = detail.split("_");
								temjson.put("reach_amount", detailArr[0]);
								temjson.put("discount_amount", detailArr[1]);
								temjson.put("item_id", detailArr[2]);
								temjson.put("item_num", detailArr[3]);
								temjson.put("rule_id", detailArr[4]);
                                temjson.put("item_unit_id", detailArr[5]);
								detailList.add(temjson);
							}							
						}
						//Add --End	
					}else{
											
						for(String detail:detailsArr){
							JSONObject temjson = new JSONObject();
							String[] detailArr = detail.split("_");
							temjson.put("reach_amount", detailArr[0]);
							temjson.put("discount_amount", detailArr[1]);
							temjson.put("item_id", detailArr[2]);
							temjson.put("item_num", detailArr[3]);
							temjson.put("rule_id", detailArr[4]);
                            temjson.put("item_unit_id", detailArr[5]);
							detailList.add(temjson);
						}
					}
				}
				//add By SunFumeng 2017-8-9 判断加价购和满额赠菜的是否赠送(换购)的菜品信息为空
				if ((activityType.equals(SysDictionary.MARKETING_ACTIVITY_MEZC) || activityType.equals(SysDictionary.MARKETING_ACTIVITY_MEJXJ))&&detailList.size() ==0){
					if (activityType.equals(SysDictionary.MARKETING_ACTIVITY_MEZC) ){
						logger.info("activityid:"+activityId+"--没有可用的赠送菜品");
					}else{
						logger.info("activityid:"+activityId+"--没有可用的加价购菜品");
					}
					
					continue;					
				}
				//Add End
				
				// Add By SunFumeng 2017-8-10 第二份半价和折后统一价的特殊活动是否满足了条件
				if (SysDictionary.MARKETING_ACTIVITY_DEFBJ.equals(activityType) ){
					if (!defbjRule){
						logger.info("activityid:"+activityId+"--没有点可享受第二份半价的商品");
						continue;					
					}
					//对之前数据库中存在老数据的异常处理：如果DetialList为空即上面的操作没有找到可享受活动的商品就认为非法
					if (detailList.size() ==0){
						logger.info("activityid:"+activityId+"--未找到可享受第二杯半价活动的商品");
						continue;						
					}					
				}

				if (SysDictionary.MARKETING_ACTIVITY_ZHTYJ.equals(activityType)){
					if (!zhtyjRule){
						logger.info("activityid:"+activityId+"--没有点满可享受折后统一价的商品");
						continue;					
					}	
					//对之前数据库中存在老数据的异常处理：如果DetialList为空即上面的操作没有找到可享受活动的商品就认为非法
					if (detailList.size() ==0){
						logger.info("activityid:"+activityId+"--为点满享受折后统一价活动的商品");
						continue;						
					}					
				}				

				//Add End
				// 判断活动开始时间和结束时间
				if (!StringUtils.isEmpty(startTime) && !StringUtils.isEmpty(endTime) && !"null".equals(startTime)) {
					long startTimeSeconds = DateUtil.getMillionSeconds(startTime);
					long EndTimeSeconds = DateUtil.getMillionSeconds(endTime);
					if (startTimeSeconds > millionSeconds) {
						logger.info("activityid:"+activityId+"--当前时间小于活动开始时间");
						continue;
					} else if (EndTimeSeconds < millionSeconds) {
						logger.info("activityid:"+activityId+"--当前时间大于活动结束时间");
						continue;
					}
				}


				// 判断useCycle使用周期
				if (applyDate.indexOf(nowweek) < 0) {
					logger.info("activityid:"+activityId+"--今天周"+nowweek+"不在使用周期"+applyDate+"之内");
					continue;
				}

				// 判断时间段
				Time now1 = DateUtil.getNowHHMMSSDate();
				if("1".equals(ifAllDay)){
					if (allDaytimes != null && !"".equals(allDaytimes) && !"null".equals(allDaytimes)) {
						String[] timesarr = allDaytimes.split(",");
						int ff = 0;// 不在有效时间内变量
						for (String tbe : timesarr) {
							String[] tbearr = tbe.split("_");
							if (tbearr.length == 2 && tbearr[0].length() == 5
									&& tbearr[1].length() == 5) {
								//Time b1 = DateUtil.parseTime(tbearr[0]);
								Time b1 = new Time((new SimpleDateFormat("HH:mm")).parse(tbearr[0]).getTime());
								Time e1 = new Time((new SimpleDateFormat("HH:mm")).parse(tbearr[1]).getTime());
								//Time e1 = DateUtil.parseTime(tbearr[1]);
								if (now1.after(b1) && now1.before(e1)) {
									ff = 1;
									continue;// 跳出这for循环
								}
							}
						}
						if (ff == 0) {
							logger.info("activityid:"+activityId+"--不在活动有效适用时间内");
							continue;
						}
					}
				}

				//判断不适用日期
				if("1".equals(ifNoLimit)){
					if (noLimittimes != null && !"".equals(noLimittimes) && !"null".equals(noLimittimes)) {
						String[] timesarr = noLimittimes.split(",");
						int ff = 0;// 不在有效日期内变量
						for (String tbe : timesarr) {
							String[] tbearr = tbe.split("_");
							long startTimeSeconds = DateUtil.getMillionSeconds(tbearr[0]);
							long EndTimeSeconds = DateUtil.getMillionSeconds(tbearr[1]);
							//modify by SunFumeng 解决不适用日期判断出错的问题,应该使用》=或者<=
							//if (startTimeSeconds < millionSeconds && EndTimeSeconds > millionSeconds) {
							if (startTimeSeconds <= millionSeconds && EndTimeSeconds >= millionSeconds) {
								ff = 1;
								continue;
							}
						}
						if (ff == 1) {
							logger.info("activityid:"+activityId+"--当前时间在活动不适用日期之内");
							continue;
						}
					}
				}
				
				//判断是否会员参加活动
				if("2".equals(targetGroup)){//会员
					if("0".equals(isMember)){
						logger.info("activityid:"+activityId+"--活动只限会员参加");
						continue;
					}
				}
				
				//判断商品设置规则crm_activity_item
				if("0".equals(ifAllItem)){ //部分商品可用
					//查出的商品限制list集合
					List<JSONObject> activityItemList = posActivityDao.getActivityItemRules(tenancyId, activityId);
					//Delete By SunFumeng 2017-8-10 第二杯半价和活动统一价的规则改变，也需要设置规则菜品。所以 这里就不用特殊处理了，判断规则和其他的一样。
					//Delete --Begin

					if("defbj".equals(activityType)){ //第二份半价活动
						int ifCanItem = 1;//是否符合规则，0符合，1不符合
						int ifAllCanItem = 0;//是否符合规则，0符合，1不符合
						for (JSONObject activityItem : activityItemList) {
							List<String> itemIdList = new ArrayList<String>();//菜品list
							String ruleCode = activityItem.optString("rule_code");//商品规则
							String itemrules = activityItem.optString("itemrules");//商品类型和(菜品id,大类id,小类id)1_1,2_2
							double item_num = activityItem.optDouble("item_num");//菜品份数
							String[] itemrulesarr = itemrules.split(",");
							double sumNum = 0.0d;//满足规则的 点菜总数量
							itemruleFor:for (String itemrule : itemrulesarr) {
								String[] itemarr = itemrule.split("_");
								String itemType = itemarr[0];//商品类型（大类，小类，单品）
								String itemId = itemarr[1];//菜品id,大类id,小类id
                                String itemUnitId = itemarr[2]; // 规格id
								for(int i = 0;i<items.size();i++){
									if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(items.get(i).get("item_property").toString()))
									{
										String unit_id =  items.get(i).get("unit_id").toString();// 规格id
										String item_id =  items.get(i).get("item_id").toString();//菜品id
										String item_class_id =  items.get(i).get("item_class_id").toString();//菜品小类id
										String item_fater_id =  items.get(i).get("item_fater_id").toString();//菜品品牌id
										double num = Double.parseDouble(items.get(i).get("item_count").toString());//菜品数量
										if(itemType.equals("1")){//大类
											if(itemId.equals(item_fater_id)){
												sumNum +=num;
											}
										}else if(itemType.equals("2")){
											if(itemId.equals(item_class_id)){
												sumNum +=num;
											}
										}else{
											if(itemId.equals(item_id) && unit_id.equals(itemUnitId)){
												sumNum +=num;
											}
										}
									}
								}
								if(sumNum >= item_num) {
									ifCanItem = 0;
								}
							}
							if(ifCanItem == 1){
								ifAllCanItem = 1;
								break;//不符合规则跳出
							}else{
								ifCanItem = 1;//满足规则之后，下一规则循环默认不符合
							}
						}
						if(ifAllCanItem == 1){
							logger.info("activityid:"+activityId+"--第二份半价活动不满足商品设置规则");
							continue;//不符合规则跳出
						}
					}else{

//					{
						int ifCanItem = 1;//是否符合规则，0符合，1不符合
						int ifAllCanItem = 0;//是否符合规则，0符合，1不符合

                        // 查询出大类和小类，在同一个规则下出现的次数
                        Map<String, Integer> classCounts = new HashMap<>();
                        for(JSONObject activityItem : activityItemList){
                            String rule_code = activityItem.optString("rule_code"); // 商品规则
                            String item_type = activityItem.optString("item_type"); // 商品类型
                            String classKey = item_type + "_" + rule_code; // 商品规则和商品类型拼成的key

                            if("1".equals(item_type) || "2".equals(item_type)){ // 大类和小类
                                if(classCounts.containsKey(classKey)){ // 如果存在，则加1
                                    classCounts.put(classKey, classCounts.get(classKey)+1);
                                }else{
                                    classCounts.put(classKey, 1);
                                }
                            }
                        }


						for (JSONObject activityItem : activityItemList) {
							String activity_id = activityItem.optString("activity_id");// 规则id
							String ruleCode = activityItem.optString("rule_code");//商品规则
							String itemrules = activityItem.optString("itemrules");//商品类型和(菜品id,大类id,小类id)1_1,2_2
							int item_num = activityItem.optInt("item_num");//菜品总份数

							String[] itemrulesarr = itemrules.split(",");
							int sumNum = 1;//满足规则的 点菜总数量
                            boolean controlSumNum = true; // 通过参数控制sumNum的数量，sumNum

                            // 选择的菜品，出现的的大类和小类，再同一规则下出现的次数
                            Map<String, Integer> isFitClassCounts = new HashMap<>();

							itemrule:for (String itemrule : itemrulesarr) {
								String[] itemarr = itemrule.split("_");
								String itemType = itemarr[0];//商品类型（大类，小类，单品）
								String itemId = itemarr[1];//菜品id,大类id,小类id
                                String itemUnitId = itemarr[2]; // 规格id
                                String rule_code = itemarr[3]; // 商品规则

                                // 单独处理大类=1，小类=2
                                boolean classIsFit = false; // 不符合

								for(int i = 0;i<items.size();i++){
									if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(items.get(i).get("item_property").toString()))
									{
									    String unit_id = items.get(i).get("unit_id").toString();// 规格id
										String item_id =  items.get(i).get("item_id").toString();//菜品id
										String item_class_id =  items.get(i).get("item_class_id").toString();//菜品小类id
										String item_fater_id =  items.get(i).get("item_fater_id").toString();//菜品品牌id
										double num = Double.parseDouble(items.get(i).get("item_count").toString());//菜品数量
										if(num >= item_num || sumNum >= item_num){
                                            if(controlSumNum){
                                                controlSumNum = false;
                                                sumNum = 0;
                                            }
											if(itemType.equals("1")){//大类
												if(itemId.equals(item_fater_id)){
                                                    classIsFit = true;
													ifCanItem = 0;
													sumNum = 1;
													break itemrule;//满足规则，跳出
												}
											}else if(itemType.equals("2")){
												if(itemId.equals(item_class_id)){
                                                    classIsFit = true;
													ifCanItem = 0;
													sumNum = 1;
													break itemrule;//满足规则，跳出
												}
											}else{
												if(itemId.equals(item_id) && unit_id.equals(itemUnitId)){
													ifCanItem = 0;
													sumNum = 1;
													break itemrule;//满足规则，跳出
												}
											}
										}else{
										    if(controlSumNum){
                                                controlSumNum = false;
                                                sumNum = 0;
                                            }
											if(itemType.equals("1")){//大类
												if(itemId.equals(item_fater_id)){
                                                    classIsFit = true;
													sumNum +=num;
												}
											}else if(itemType.equals("2")){
												if(itemId.equals(item_class_id)){
                                                    classIsFit = true;
													sumNum +=num;
												}
											}else{
												if(itemId.equals(item_id) && unit_id.equals(itemUnitId)){
													sumNum +=num;
												}
											}
										}
									}
								}
                                /**
                                 * 循环大类和小类：
                                 * 1、没有符合的直接循环下一个活动
                                 * 2、有符合的，但数量不符合的也直接循环下一个活动
                                 */
                                if(("1".equals(itemType) || "2".equals(itemType)) && !classIsFit){
                                    if(sumNum < item_num) {
                                        String isFitClassKey = itemType + "_" + rule_code;
                                        if(isFitClassCounts.containsKey(isFitClassKey)){ // 是否包含
                                            int fitClassCount = isFitClassCounts.get(isFitClassKey);
                                            isFitClassCounts.put(isFitClassKey, fitClassCount+1);
                                            if(classCounts.containsKey(isFitClassKey)){
                                                if(classCounts.get(isFitClassKey) == fitClassCount+1){
                                                    continue firstListFor;
                                                }
                                            }
                                        }else{
                                            isFitClassCounts.put(isFitClassKey, 1);      // 初次存1
                                            if(classCounts.containsKey(isFitClassKey)){
                                                if(classCounts.get(isFitClassKey) == 1){
                                                    continue firstListFor;
                                                }
                                            }
                                        }
                                    }
                                }

								if(sumNum >= item_num) {
									ifCanItem = 0;
									sumNum = 1;
									break itemrule;
								}
							}
							if(ifCanItem == 1){
								ifAllCanItem = 1;
								break;//不符合规则跳出
							}else{
								ifCanItem = 1;//满足规则之后，下一规则默认不符合
							}
						}
						if(ifAllCanItem == 1){
							logger.info("activityid:"+activityId+"--不满足商品设置规则");
							continue;//不符合规则跳出
						}
					}
					
				}else{ //全部商品可用
					//Delete By SunFumeng 2017-8-10 
					//修改原因： 第二份半价活动规则发生改变 ：即要设置活动规则商品，同时还有设置享受半价的商品。即activity_item和activity_rule都要写入值
					//其中activity_item是要满足的规则，activity_rule是享受半价活动的商品。所以，这里不用判断是否点了享受活动的商品了，
					//前面已经统一
					//根据activity_rule来判断
					//Delete --Begin
					/*
					if("defbj".equals(activityType) ){//第二份半价活动
						int ifAllCanItem = 1;//是否符合规则，0符合，1不符合
						StringBuffer itemstr = new StringBuffer();
						for(int i = 0;i<items.size();i++){
							int temnum = 0;//菜品数量
							if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(items.get(i).get("item_property").toString()))
							{
								String item_id =  items.get(i).get("item_id").toString();//菜品id
								itemstr.append(item_id);
								for(int j = 0;j<items.size();j++){
									if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(items.get(j).get("item_property").toString()))
									{
										String item_id_j =  items.get(j).get("item_id").toString();//菜品id
										if(item_id_j.equals(item_id)){
											temnum ++;
										}
									}
								}
								if(temnum >=2){
									ifAllCanItem = 0;
								}
							}
						}
						if(ifAllCanItem == 1){
							logger.info("activityid:"+activityId+"--第二杯半价活动不满足全部商品设置规则");
							continue;//不符合规则跳出
						}
					}
					*/
					//Delete -End 
				}

				
				//判断账单限制使用数量
				if(usedActivityList.size() > 0 && usedActivityList!=null){
					int ifnum = 0;//是否超过数量
					for(int g = 0;g < usedActivityList.size(); g++){
						if(usedActivityList.get(g).get("activity_id").equals(activityId.toString())){
							int num = Integer.parseInt(usedActivityList.get(g).get("num").toString());
							if(num >= billLimitTimes && billLimitTimes !=0){//0是不限制
								ifnum = 1;//超过了
								break;
							}
						}
					}
					if(ifnum == 1){
						logger.info("活动id:"+activityId+"超过账单限制使用数量");
						continue;
					}
				}
				
				//是否与其他活动并用
				if(ifParallel == 0 && usedActivityList.size() > 0){
					int isParaAct = 0;//有活动可以同时执行标记
					for(int i=0;i<usedActivityList.size();i++){
						if(!usedActivityList.get(i).get("activity_id").equals(activityId+"")){
							isParaAct = 1;//不可以同时存在
							break;
						}
					}
					if(isParaAct == 1){
						logger.info("活动id："+activityId+"不能与其中使用过的活动并用");
						continue;
					}
				}
				
				json.put("details", detailList);
				json.remove("alldaytimes");
				json.remove("nolimittimes");
				listresult.add(json);
			}
			
			logger.info("返回有效活动：" + listresult.toString());
			result.setData(listresult);
			result.setCode(Constant.CODE_SUCCESS);
			result.setMsg(Constant.FIND_ACTIVITY_INFO_SUCCESS);

		} catch (Exception e) {
			logger.info("返回有效活动错误："+ ExceptionMessage.getExceptionMessage(e));
			result.setSuccess(false);
			e.printStackTrace();
		}

	}
	

	/*
	 * 使用活动
	 * */
	@Override
	public Data joinInActivity(Data param, Data result) throws SystemException {
		try {
			logger.info("使用活动start===========================================");
			String tenancyId = param.getTenancy_id();// 门店id
			Integer storeId = param.getStore_id();// 机构ID
			String source = param.getSource();//来源
			Map<String, Object> map = ReqDataUtil.getDataMap(param);
			String billNum = ParamUtil.getStringValue(map, "bill_code", true,
					PosErrorCode.NOT_NULL_BILL_NUM);// 账单号
			String activityBatchNum = ParamUtil.getStringValue(map, "activity_batch_num", false,null);// 活动批次号
			Integer activityId = ParamUtil.getIntegerValue(map, "activity_id", false,null);// 活动id
			String activityType = ParamUtil.getStringValue(map, "activity_type", false,null);// 活动类型
			Integer activityCount = ParamUtil.getIntegerValue(map, "activity_count", false,null);// 活动使用次数
			Integer isOrdering = ParamUtil.getIntegerValue(map, "is_ordering", false,null);// 是否全部下过单0否1是
			List<Map<String, Object>> bjRwids = (List<Map<String, Object>>) map.get("hd_rwids");// 活动半价商品rwid或者折后统一价商品rwid
			List<Map<String, Object>> details = (List<Map<String, Object>>) map.get("details");//具体折扣方案赠送菜品
			Integer ruleId = 0;//使用活动中使用的规则id
			String isdinner = ParamUtil.getStringValue(map, "isdinner", false,null);// Y:正餐，N：快餐
			// 验证菜品,规格信息,组织账单明细数据
			for (int k = 0; k < details.size(); k++)
			{
				Map<String, Object> detail = (Map<String, Object>) details.get(k);
				ruleId = ParamUtil.getIntegerValue(detail, "rule_id", false,null);//规则id
			}
			//06-29修改方案 新加
			String sqlBillItem = "update pos_bill_item set activity_batch_num = ?,item_remark = ?,activity_id = ?,discount_mode_id = ?,activity_rule_id = ?,activity_count = ? " +
					"where tenancy_id= ? and bill_num = ? and store_id = ? and (activity_id is null or activity_id = 0 )and item_remark is null";
			posActivityDao.update(sqlBillItem, new Object[]
					{ activityBatchNum, SysDictionary.ITEM_REMARK_HD07,activityId,11,ruleId,activityCount,tenancyId, billNum, storeId });
			if(bjRwids.size() > 0){
				for (int i = 0; i < bjRwids.size(); i++)
				{
					Map<String, Object> bjRwid = (Map<String, Object>) bjRwids.get(i);
					if(activityType.equals(SysDictionary.MARKETING_ACTIVITY_DEFBJ)){//第二杯半价活动
						String sqlUpdateItem = "update pos_bill_item set item_remark = ? where rwid=?";
						posActivityDao.update(sqlUpdateItem, new Object[]{SysDictionary.ITEM_REMARK_HDBJ09,Integer.parseInt(bjRwid.get("rwid").toString())});
						break;
					}else if(activityType.equals(SysDictionary.MARKETING_ACTIVITY_ZHTYJ)){
						String sqlUpdateItem = "update pos_bill_item set item_remark = ? where rwid=?";
						posActivityDao.update(sqlUpdateItem, new Object[]{SysDictionary.ITEM_REMARK_HDTYJ10,Integer.parseInt(bjRwid.get("rwid").toString())});
					}
				}
			}

			if(isOrdering == 1){
				orderingJoinInActivity(tenancyId,storeId,billNum);
			}
			else{
				logger.info("使用活动，参加活动菜品没有全部下单，boh不操作");
			}
			logger.info("使用活动成功end===========================================");
			if(activityType.equals(SysDictionary.MARKETING_ACTIVITY_MEJJG)
					|| activityType.equals(SysDictionary.MARKETING_ACTIVITY_MEZC)) {//消费满加价购
				//更新pos_bill_item数据便于POS前端显示
				posActivityDao.updatePosBillItem(tenancyId, storeId, billNum, activityBatchNum);
			}
			//快餐返回账单查询数据
			if(!StringUtils.isEmpty(isdinner) && isdinner.equals("N")) {
				List<PosBill> billList = posActivityDao.newFindPosBill(param, result);
				result.setData(billList);
			}
			result.setCode(0);
			result.setSuccess(true);
			result.setMsg("参加活动成功");
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			result.setCode(1);
			result.setSuccess(false);
			result.setMsg("参加活动错误");
			logger.info("参加活动错误："+ ExceptionMessage.getExceptionMessage(e));
			return result;
		}
	}
	
	/*
	 * 查询菜品相关信息，插入菜品明细表
	 * 修改估清菜品(android_pad 和cc_order)
	 * 修改口味做法
	 * */
	private Map<String, JSONObject>  insertPosBillItem(String stableCode,String source,String tenancyId, Integer storeId,
			String billNum, String chanel, Date reportDate,
			List<Map<String, Object>> items, String tableCode,
			String waiterNum, Integer shiftId, String posNum,
			Timestamp currentTime, String orderRemark,String saleMode) throws Exception {
		List<Map<String, Object>> meallistItems = new ArrayList<Map<String,Object>>(); 
		//获取套餐明细
		meallistItems = getMeallistItem(items, meallistItems);
		//修改估清菜品
		updateSoldout(source, tenancyId, storeId, reportDate, items);
		
		String formatState = "";
		double itemPrice = 0d;//菜品价格
		JSONObject object = new JSONObject();
		object.element("store_id", storeId);
		object.element("busi_date", DateUtil.format(reportDate));
		object.put("pos_num", posNum);
		String batch_num = codeService.getCode(tenancyId, Code.POS_BATCH_NUM, object);// 调用统一接口来实现
		
		//插入账单明细
		Map<String, JSONObject> itemInfoMap = insertBillItemDetail(stableCode,tenancyId,
				storeId, billNum, chanel, reportDate, items, tableCode,
				waiterNum, shiftId, posNum, currentTime, orderRemark, saleMode,
				meallistItems, formatState, itemPrice, batch_num);
		//处理做法口味
		takeMethodInfo(tenancyId, storeId, billNum, reportDate, items, posNum,
				currentTime, orderRemark);
		
		itemInfoMap.put("pos_bill_item", JSONObject.fromObject("{\"batch_num\":'"+batch_num+"'}"));
		return itemInfoMap;
	}

	/*
	 * 插入账单明细
	 * */
	private Map<String, JSONObject> insertBillItemDetail(String stableCode,String tenancyId,
			Integer storeId, String billNum, String chanel, Date reportDate,
			List<Map<String, Object>> items, String tableCode,
			String waiterNum, Integer shiftId, String posNum,
			Timestamp currentTime, String orderRemark, String saleMode,
			List<Map<String, Object>> meallistItems, String formatState,
			double itemPrice, String batch_num) throws Exception {
		// 获取菜品信息
		Map<String, JSONObject> itemInfoMealMap = new HashMap<String, JSONObject>();
		if (meallistItems.size() > 0)
		{
			List<JSONObject> itemInfoMealList = posDishDao.getHqItemInfoByItem(tenancyId, storeId, SysDictionary.CHANEL_MD01, meallistItems);
			if (null != itemInfoMealList && itemInfoMealList.size() > 0)
			{
				for (JSONObject itemInfoJson : itemInfoMealList)
				{
					itemInfoMealMap.put(itemInfoJson.optString("item_id"), itemInfoJson);
				}
			}
		}
		
		// 获取菜品信息
		List<JSONObject> itemInfoList = posDishDao.getHqItemInfoByItem(tenancyId, storeId, chanel, items);
		Map<String, JSONObject> itemInfoMap = new HashMap<String, JSONObject>();
		for (JSONObject itemInfoJson : itemInfoList)
		{
			itemInfoMap.put(itemInfoJson.optString("item_id"), itemInfoJson);
		}
				
		//组织账单明细数据
		List<Object[]> billItemList = new ArrayList<Object[]>();
		for (int k = 0; k < items.size(); k++)
		{
			int orderNumber = k + 1;
			
			Map<String, Object> detail = (Map<String, Object>) items.get(k);

			
			Integer itemSerial = ParamUtil.getIntegerValue(detail, "item_serial", false, null);
			if(Tools.isNullOrEmpty(itemSerial)){
				itemSerial = 0;//点餐顺序
			}
			Integer item_id = ParamUtil.getIntegerValue(detail, "item_id", false, null);

			
			Integer item_class_id = ParamUtil.getIntegerValue(detail, "item_class_id", false, null);

			String item_num = ParamUtil.getStringValue(detail, "item_num", false, null);

			String item_name = ParamUtil.getStringValue(detail, "item_name", false, null);

			String item_unit_name = ParamUtil.getStringValue(detail, "item_unit_name", false, null);

			Integer itemUnitId = ParamUtil.getIntegerValue(detail, "unit_id", true, PosErrorCode.NOT_NULL_DISH_UNITID);
			Integer reason_id = ParamUtil.getIntegerValue(detail, "reason_id", false, null);

			Integer assist_item_id = ParamUtil.getIntegerValue(detail, "assist_item_id", false, null);

			String item_remark = ParamUtil.getStringValue(detail, "item_remark", false, null);
			// 备注
			String itemTaste = ParamUtil.getStringValue(detail, "item_taste", false, null);
			String item_property = ParamUtil.getStringValue(detail, "item_property", true, PosErrorCode.NOT_NULL_ITEM_PROPERTY);
			String mainItem = ParamUtil.getStringValue(detail, "main_item", false, null);
			String mid = ParamUtil.getStringValue(detail, "mid", false, null);
			if(Tools.isNullOrEmpty(item_property)){
				item_property = SysDictionary.ITEM_PROPERTY_SINGLE;
			}
			
			if ("1".equals(formatState) && Tools.isNullOrEmpty(saleMode))
			{
				saleMode = SysDictionary.SALE_MODE_TS01;
			}

			Integer waitcall_tag = ParamUtil.getIntegerValue(detail, "waitcall_tag", false, null);

			if (Tools.isNullOrEmpty(waitcall_tag))
			{
				waitcall_tag = 0;
			}

			
			
			
			Integer item_count = 1;
			if (Tools.isNullOrEmpty(detail.get("item_count")) == false)
			{
				item_count = Integer.parseInt((detail.get("item_count").toString()));
			}

			Double assist_num = 0d;
			if (Tools.isNullOrEmpty(detail.get("assist_num")) == false)
			{
				assist_num = Double.parseDouble(detail.get("assist_num").toString());
			}

			int seat_num = 0;
			if (Tools.isNullOrEmpty(detail.get("seat_num")) == false)
			{
				seat_num = Integer.parseInt(detail.get("seat_num").toString());
			}

			Integer setmeal_rwid = null;
			if (Tools.isNullOrEmpty(detail.get("setmeal_rwid")) == false)
			{
				setmeal_rwid = Integer.parseInt(detail.get("setmeal_rwid").toString());
			}

			Integer setmeal_id = null;
			if (SysDictionary.ITEM_PROPERTY_SETMEAL.equalsIgnoreCase(item_property) || SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item_property))
			{
				setmeal_id = ParamUtil.getIntegerValue(detail, "setmeal_id", false, null);
			}

			JSONObject itemInfoJson = null;
			if (itemInfoMap.containsKey(String.valueOf(item_id)))
			{
				itemInfoJson = itemInfoMap.get(String.valueOf(item_id));
			}
			
			String discount_state = null;
			Double proportion = 0d;
			String item_english = null;
			String pushmoney_way = null;
			Integer details_id = 0;
			Integer if_pay_service=0;
			if (null != itemInfoJson)
			{
				details_id = itemInfoJson.optInt("details_id");
				if ((null == details_id || details_id <= 0) && SysDictionary.ITEM_PROPERTY_MEALLIST.equals(item_property))
				{
					if (itemInfoMealMap.containsKey(String.valueOf(item_id)))
					{
						itemInfoJson = itemInfoMealMap.get(String.valueOf(item_id));
						details_id = itemInfoJson.optInt("details_id");
					}
				}
				item_english = itemInfoJson.optString("item_english");
				pushmoney_way = itemInfoJson.optString("pushmoney_way");
				proportion = itemInfoJson.optDouble("proportion_money");
				discount_state = itemInfoJson.optString("is_discount");// 是否可以折扣
				item_class_id = itemInfoJson.optInt("item_class");
				item_num = itemInfoJson.optString("item_code");
				item_name = itemInfoJson.optString("item_name");
				itemUnitId = itemInfoJson.optInt("item_unit_id");
				item_unit_name = itemInfoJson.optString("item_unit_name");
				if (Tools.isNullOrEmpty(detail.get("item_price")) == false)
				{
					itemPrice = Double.parseDouble(detail.get("item_price").toString());

				}else{
					itemPrice = itemInfoJson.optDouble("standard_price");
				}
				
				if (proportion.isNaN())
				{
					proportion = 0d;
				}
			}
			else
			{
				throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_DISH_ITEM);
			}
			
			//保存账单明细
			Object[] objs = new Object[]
			{ tenancyId, storeId, billNum, details_id, item_id, item_num, item_name, item_english, reportDate, itemUnitId, item_unit_name, tableCode, pushmoney_way, proportion, assist_num,waiterNum, itemPrice, item_count, 100,discount_state, null, item_class_id,
					item_property, item_remark, "*", setmeal_id, setmeal_rwid, null, currentTime, itemSerial, shiftId, posNum, orderRemark, seat_num, saleMode, itemTaste, assist_item_id, waitcall_tag, reason_id, batch_num, orderNumber,stableCode,mainItem,mid,if_pay_service };

			billItemList.add(objs);
		}

		if (billItemList.size() > 0)
		{
			 //插入账单明细表
			StringBuilder itemSql = new StringBuilder(
					"insert into pos_bill_item (tenancy_id,store_id,bill_num,details_id,item_id,item_num,item_name,item_english,report_date,item_unit_id,item_unit_name,table_code,pushmoney_way,proportion,assist_num,waiter_num,item_price,item_count,discount_rate,discount_state,discount_mode_id,item_class_id,item_property,item_remark,print_tag,setmeal_id,setmeal_rwid,is_setmeal_changitem,item_time,item_serial,item_shift_id,item_mac_id,order_remark,seat_num,sale_mode,item_taste,assist_item_id,waitcall_tag,returngive_reason_id,batch_num,order_number,stable_code,main_item,mid,if_pay_service) ");
			itemSql.append(" values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
			posDishDao.batchUpdate(itemSql.toString(), billItemList);
		}
		return itemInfoMap;
	}

	/*
	 * 修改估清菜品
	 * */
	private void updateSoldout(String source, String tenancyId,
			Integer storeId, Date reportDate, List<Map<String, Object>> items)
			throws Exception {
		List<JSONObject> soldOutList = new ArrayList<JSONObject>();
		List<JSONObject> subSoldList = new ArrayList<JSONObject>();
		if (SysDictionary.SOURCE_MOVE.contains(source) || SysDictionary.SOURCE_CC_ORDER.equals(source))
		{
			Map<Integer,Double> tempMap = new HashMap<Integer, Double>();
			// 修改菜品估清
			Map<String, Object> soldOutMap = posDishDao.getPosSoldOutByItem(tenancyId, storeId, reportDate, items);
			List<Object[]> soldOutUpdateList = new ArrayList<Object[]>();
			for (int i = 0; i < items.size(); i++)
			{
				Map<String, Object> oDish = (Map<String, Object>) items.get(i);
				String itemId = ParamUtil.getStringValue(oDish, "item_id", false, null);
				String unitId = ParamUtil.getStringValue(oDish, "unit_id", false, null);
				double itemCount = 0;
				if (Tools.isNullOrEmpty(oDish.get("item_count")) == false)
				{
					itemCount = Double.parseDouble(oDish.get("item_count").toString());
				}
				else
				{
					throw new SystemException(PosErrorCode.NOT_NULL_DISH_ITEM_COUNT);
				}

				if (null != soldOutMap && soldOutMap.containsKey(itemId))
				{
					Double soldOutCount = Double.parseDouble(String.valueOf(soldOutMap.get(itemId)));
					if (soldOutCount.isNaN() || soldOutCount <= 0)
					{
						throw SystemException.getInstance(PosErrorCode.DISH_ITEM_SOLD_OUT);
					}
					if (itemCount > soldOutCount)
					{
						throw SystemException.getInstance(PosErrorCode.GREAT_THAN_SOLD_OUT_COUNT);
					}

					Double num = DoubleHelper.sub(soldOutCount, itemCount, 4);

					soldOutMap.put(itemId, num);
					tempMap.put(Integer.parseInt(itemId), num);
					
					
					soldOutUpdateList.add(new Object[]
					{ num, tenancyId, storeId, reportDate, Integer.parseInt(itemId) });

					JSONObject soldOutJson = new JSONObject();
					soldOutJson.put("item_id", itemId);
					soldOutJson.put("item_unit_id", unitId);
					soldOutJson.put("setdate", DateUtil.format(reportDate));
					soldOutJson.put("num", num);
					soldOutList.add(soldOutJson);
					if (num <= 0)
					{
						JSONObject subJson = new JSONObject();
						subJson.put("item_id", itemId);
						subSoldList.add(subJson);
					}
				}
			}

			System.out.println(tempMap.size());
			//对更新数据处理
			List<Object[]> resultList = new ArrayList<Object[]>();
			for (int i = 0; i < soldOutUpdateList.size(); i++) {
				Object[] objects = soldOutUpdateList.get(i);
				if(tempMap.size()>0 && tempMap.get(objects[4]) == objects[0]){
					resultList.add(objects);
				}
			}
		
			if (resultList.size() > 0)
			{
				StringBuilder updateSoldoutSql = new StringBuilder("update pos_soldout set num=? where tenancy_id=? and store_id=? and setdate=? and item_id =?");
				posDishDao.batchUpdate(updateSoldoutSql.toString(), resultList);
			}
		}
	}

	/*
	 * 处理做法口味
	 * */
	private void takeMethodInfo(String tenancyId, Integer storeId,
			String billNum, Date reportDate, List<Map<String, Object>> items,
			String posNum, Timestamp currentTime, String orderRemark)
			throws Exception {
		// 查询账单明细,组织打印参数
		List<JSONObject> rwidList = posDishDao.getRwidsWithoutWaitCallItem(tenancyId, storeId, billNum, orderRemark,posNum);
		String rwids = "";
		List<Integer> rwidSet = new ArrayList<Integer>();
		for (JSONObject rwidJson : rwidList)
		{
			Integer rwid = rwidJson.optInt("rwid");
			// 等叫和不等叫菜品全部拼接，在新打印中有是否等叫菜品打印的判断
			if (!Tools.isNullOrEmpty(rwids))
			{
				rwids += ",";
			}
			rwids += String.valueOf(rwid);
			
			rwidSet.add(rwid);
		}
					
		List<Integer> methodParamList = new ArrayList<Integer>();
		Map<String, Map<String, Object>> itemMap = new HashMap<String, Map<String, Object>>();
		for (Map<String, Object> item : items)
		{
			if (Tools.isNullOrEmpty(item.get("method")) == false)
			{
				@SuppressWarnings("unchecked")
				List<Map<String, String>> methods = (List<Map<String, String>>) item.get("method");
				for (int j = 0; j < methods.size(); j++)
				{
					Map<String, String> method = (Map<String, String>) methods.get(j);
					if (StringUtils.isNotEmpty(method.get("method_id")))
					{
						methodParamList.add(Integer.parseInt(method.get("method_id")));
					}
				}
			}
		}

		if (methodParamList.size() > 0)
		{
			// 查询做法信息
			List<JSONObject> methodList = posDishDao.getHqItemMethodByID(tenancyId, storeId, methodParamList);
			Map<String, JSONObject> methodInfoMap = new HashMap<String, JSONObject>();
			if (null != methodList && methodList.size() > 0)
			{
				for (JSONObject methodInfoJson : methodList)
				{
					methodInfoMap.put(methodInfoJson.optString("method_id"), methodInfoJson);
				}
			}
			
			// 验证做法信息,组织做法
			List<Object[]> methodItemList = new ArrayList<Object[]>();
			for (JSONObject rwidJson : rwidList)
			{
				String rwid = rwidJson.optString("rwid");
				Integer methodId = null;
				Map<String, Object> detail = itemMap.get(rwidJson.optString("order_number"));

				String item_property = ParamUtil.getStringValue(detail, "item_property", false, null);
				Double itemPrice = ParamUtil.getDoubleValue(detail, "item_price", false, null);
				Integer item_id = ParamUtil.getIntegerValue(detail, "item_id", false, null);

				if (SysDictionary.ITEM_PROPERTY_SINGLE.equalsIgnoreCase(item_property) || SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item_property))
				{
					if (Tools.isNullOrEmpty(detail.get("method")) == false)
					{
						@SuppressWarnings("unchecked")
						List<Map<String, String>> methods = (List<Map<String, String>>) detail.get("method");
						for (int j = 0; j < methods.size(); j++)
						{
							Map<String, String> method = (Map<String, String>) methods.get(j);
							if (StringUtils.isNotEmpty(method.get("method_id")))
							{
								methodId = Integer.parseInt(method.get("method_id"));
							}
							else
							{
								methodId = 0;
							}
							String methodName = method.get("method_name");

							JSONObject methodJson = null;

							if (methodInfoMap.containsKey(String.valueOf(methodId)))
							{
								methodJson = methodInfoMap.get(String.valueOf(methodId));
							}

							if (null == methodJson || methodJson.isEmpty())
							{
								throw new SystemException(PosErrorCode.NOT_EXISTS_METHOD_DISH);
							}

							String makeupWay = methodJson.optString("makeup_way");
							Double pro_money = methodJson.optDouble("proportion_money"); // 有可能是数字也有可能会是小数
							double zfamount = 0;

							if (StringUtils.isNotEmpty(makeupWay))
							{
								if ("ADD".equalsIgnoreCase(makeupWay))
								{
									zfamount = pro_money;
								}
								if ("MULTI".equalsIgnoreCase(makeupWay))
								{
									zfamount = DoubleHelper.mul(itemPrice, pro_money, 4);
								}
							}

							methodItemList.add(new Object[]
							{ tenancyId, storeId, billNum, reportDate, Integer.valueOf(rwid), item_id, posNum, "METHOD", currentTime, methodId, methodName, zfamount, 0 });
						}
					}
				}
			}

			if (methodItemList.size() > 0)
			{
				/**
				 * 插入做法表
				 */
				StringBuilder insertMethod = new StringBuilder("insert into pos_zfkw_item(tenancy_id,store_id,bill_num,report_date,rwid,item_id,pos_num,type,last_updatetime,zfkw_id,zfkw_name,amount,print_id) values (?,?,?,?,?,?,?,?,?,?,?,?,?)");

				posDishDao.batchUpdate(insertMethod.toString(), methodItemList);
			}
		}
	}

	/*
	 * 获取菜品套餐明细
	 * */
	private List<Map<String, Object>> getMeallistItem(List<Map<String, Object>> items,
			List<Map<String, Object>> meallistItems) {
		int scale =2;
		for (int i = 0; i < items.size(); i++)
		{
			Map<String, Object> oDish = (Map<String, Object>) items.get(i);
			
			String itemProperty = ParamUtil.getStringValue(oDish, "item_property", true, PosErrorCode.NOT_NULL_ITEM_PROPERTY);
			Integer itemId = ParamUtil.getIntegerValue(oDish, "item_id", true, PosErrorCode.NOT_NULL_ITEM_ID);
			Integer itemSerial = ParamUtil.getIntegerValue(oDish, "item_serial", false, null);
			Double itemCount = ParamUtil.getDoubleValue(oDish, "item_count", false, null);
			Double itemPrice = ParamUtil.getDoubleValue(oDish, "item_price", false, null);
			
			if(itemCount.isNaN() || itemCount<=0)
			{
				itemCount = 1d;
			}
			
			if(SysDictionary.ITEM_PROPERTY_MEALLIST.equals(itemProperty))
			{
				meallistItems.add(oDish);
			}
			
			if(SysDictionary.ITEM_PROPERTY_SETMEAL.equals(itemProperty))
			{
				List<Map<String, Object>> setmealItemList = new ArrayList<Map<String,Object>>();
				Double meallistItemPriceTotal =0d;
				Map<String, Object> maxItem = null;
				for (Map<String, Object> meallistItem : items)
				{
					String detailsItemProperty = ParamUtil.getStringValue(meallistItem, "item_property", true, PosErrorCode.NOT_NULL_ITEM_PROPERTY);
					Integer detailsSetmealId = ParamUtil.getIntegerValue(meallistItem, "setmeal_id", false, null);
					Integer detailsSetmealRwid = ParamUtil.getIntegerValue(meallistItem, "setmeal_rwid", false, null);
					Double detailsItemCount = ParamUtil.getDoubleValue(meallistItem, "item_count", false, null);
					Double detailsItemPrice = ParamUtil.getDoubleValue(meallistItem, "item_price", false, null);
					
					if(detailsItemCount.isNaN() || detailsItemCount<=0)
					{
						detailsItemCount = 1d;
					}
					
					if(detailsItemPrice.isNaN())
					{
						detailsItemPrice = 0d;
					}
					
					if (SysDictionary.ITEM_PROPERTY_MEALLIST.equals(detailsItemProperty) && itemId.equals(detailsSetmealId) && itemSerial.equals(detailsSetmealRwid))
					{
						Double assistNum = DoubleHelper.div(detailsItemCount, itemCount, scale);
						detailsItemPrice = DoubleHelper.mul(detailsItemPrice, DoubleHelper.div(detailsItemCount, assistNum, scale), scale);
						meallistItemPriceTotal = DoubleHelper.add(meallistItemPriceTotal, detailsItemPrice, scale);
						
						meallistItem.put("item_price", detailsItemPrice);
						meallistItem.put("assist_num", assistNum);
						setmealItemList.add(meallistItem);
						
						if(null !=maxItem)
						{
							Double maxItemPrice = ParamUtil.getDoubleValue(maxItem, "item_price", false, null);
							if(detailsItemPrice>maxItemPrice)
							{
								maxItem = meallistItem;
							}
						}
						else
						{
							maxItem = meallistItem;
						}
					}
				}
				
				double detailsItemPriceTotal =0d;
				for(Map<String, Object> meallistItem : setmealItemList)
				{
					Double detailsItemPrice = ParamUtil.getDoubleValue(meallistItem, "item_price", false, null);
					if(meallistItemPriceTotal >0)
					{
						detailsItemPrice = DoubleHelper.mul(DoubleHelper.div(itemPrice, meallistItemPriceTotal, 4), detailsItemPrice, scale);
					}
					else
					{
						detailsItemPrice = 0d;
					}
					
					detailsItemPriceTotal = DoubleHelper.add(detailsItemPriceTotal, detailsItemPrice, scale);
					meallistItem.put("item_price", detailsItemPrice);
				}
				
				if(detailsItemPriceTotal!=itemPrice)
				{
					Double detailsItemPrice = ParamUtil.getDoubleValue(maxItem, "item_price", false, null);
					detailsItemPrice = DoubleHelper.add(detailsItemPrice, DoubleHelper.sub(itemPrice, detailsItemPriceTotal, scale), scale);
					maxItem.put("item_price", detailsItemPrice);
				}
				
			}
		}
		return meallistItems;
	}

	/*
	 * 查询使用活动
	 * */
	@Override
	public void getUsedActivity(Data param, Data result) throws SystemException {
		String tenancyId = param.getTenancy_id();// 门店id
		Integer storeId = param.getStore_id();// 机构ID
		Map<String, Object> map = ReqDataUtil.getDataMap(param);
		String billNum = ParamUtil.getStringValue(map, "bill_code", true,
				PosErrorCode.NOT_NULL_BILL_NUM);// 账单号
		try {
			List<JSONObject> activityList = posActivityDao.getUsedActivityInfo(tenancyId,storeId,billNum);
			logger.info("返回使用的活动：" + activityList.toString());
			result.setData(activityList);
			result.setCode(Constant.CODE_SUCCESS);
			result.setMsg(Constant.FIND_ACTIVITY_INFO_SUCCESS);
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("查询使用活动错误："+ ExceptionMessage.getExceptionMessage(e));
		}
		
	}
	
	/*
	 * 取消活动
	 */
	@Override
	public void cancelActivity(Data param, Data result)
			throws Exception {
		logger.info("cancelActivity:==========================start");
		String tenancyId = param.getTenancy_id();// 门店id
		Integer storeId = param.getStore_id();// 机构ID
		Map<String, Object> map = ReqDataUtil.getDataMap(param);
		String billNum = ParamUtil.getStringValue(map, "bill_code", true,
				PosErrorCode.NOT_NULL_BILL_NUM);// 账单号
		Integer  isOrdering = ParamUtil.getIntegerValue(map, "is_ordering", false,null);// 是否下过单0否1是
		//06-29修改方案 新加
		String isdinner = ParamUtil.getStringValue(map, "isdinner", false,null);// Y:正餐，N：快餐
		List<Map<String, Object>> details = (List<Map<String, Object>>) map.get("details");//要取消的活动
		String sqlBill = " update pos_bill set discount_mode_id = ?, discount_rate=?, discountr_amount=? where bill_num = ? and store_id = ? ";
		String sqlBillItem = "update pos_bill_item set activity_batch_num = null,item_remark = null,activity_id = null,discount_mode_id = null,activity_rule_id = null,activity_count = null where tenancy_id= ? and bill_num = ? and store_id = ? and activity_id = ? and activity_batch_num = ?";
		List<Object[]> itemList = new ArrayList<Object[]>();
		// 验证菜品,规格信息,组织账单明细数据
		for (int k = 0; k < details.size(); k++)
		{
			Map<String, Object> detail = (Map<String, Object>) details.get(k);
			String activityBatchNum = ParamUtil.getStringValue(detail, "activity_batch_num", false,null);//活动批次号
			Integer activityId = ParamUtil.getIntegerValue(detail, "activity_id", false,null);//活动id
			Integer ruleId = ParamUtil.getIntegerValue(detail, "rule_id", false,null);//规则id
			String activityType = ParamUtil.getStringValue(detail, "activity_type", false,null);// 活动类型
			Object[] objs = new Object[]{tenancyId, storeId, billNum,activityId,activityBatchNum};
			itemList.add(objs);
			if(activityType.equals(SysDictionary.MARKETING_ACTIVITY_MEJJG) || activityType.equals(SysDictionary.MARKETING_ACTIVITY_MEZC)){//删除赠菜和加价购菜品
				posActivityDao.deleteActivityGift(tenancyId, storeId, billNum, activityBatchNum);
			}
			
			posActivityDao.update(sqlBillItem, new Object[]
					{tenancyId, billNum, storeId,activityId,activityBatchNum });
		}
		//删除账单折扣表数据
		posActivityDao.deleteItemDiscountInfo(itemList);
		if(isOrdering == 1){
			//修改订单和明细数据,取消了全部折扣
			posActivityDao.update(sqlBill, new Object[]{ 0, 100d, 0, billNum, storeId });
			this.calcAmount(tenancyId, storeId, billNum);
			
			//重新计算使用活动的折扣
			Double discountrAmount = posActivityDao.getSumDiscountAmount(tenancyId, storeId, billNum);
			//更新账单
			posActivityDao.update(sqlBill, new Object[]
					{ SysDictionary.DISCOUNT_MODE_11, 100d, discountrAmount, billNum, storeId });
			this.calcAmount(tenancyId, storeId, billNum);
			//快餐返回账单查询数据
			if(!StringUtils.isEmpty(isdinner) && isdinner.equals("N")) {
				List<PosBill> billList = posActivityDao.newFindPosBill(param, result);
				result.setData(billList);
			}
		}else{
			logger.info("取消活动，参加活动菜品没有全部下单，boh不操作");
		}
		logger.info("cancelActivity:==========================end");
	}

	/*
	 * 落单过程中使用活动计算金额
	 * */
	@Override
	public void orderingJoinInActivity(String tenancyId,Integer storeId,String billNum)
			throws SystemException {
		try {
			logger.info("账单号："+billNum+"使用活动start==========");
			//1:整单折扣、2:折扣方案、3:折让（作废）、4:团体会员折扣、5:会员折扣、6:会员价7:线上优惠  8：会员价+折扣方案9：普通折扣率  10：单品折扣 11：活动折扣
			Integer discount_mode_id = SysDictionary.DISCOUNT_MODE_11;
			Double discount_rate = 100d; //折扣率 默认100
			Double discountrAmount = 0d; //折让金额
			Integer discount_reason_id = 0; //折扣原因id 默认0
//			String bill_status = SysDictionary.BILL_PROPERTY_OPEN;
//			Integer ruleId = 0; //使用活动中使用的规则id
		
			List<JSONObject> jsonItemList = posActivityDao.getusedActivityItemRules(tenancyId, storeId, billNum);
			if(jsonItemList.size() > 0){
				for (JSONObject json : jsonItemList) {
					String activityType = json.optString("activity_type");
					if(SysDictionary.MARKETING_ACTIVITY_MEJXJ.equals(activityType)){
						discountrAmount = discountrAmount +(Double.parseDouble(json.optString("discount_amount")) * Integer.parseInt(json.optString("activity_count")));
					}else if(SysDictionary.MARKETING_ACTIVITY_MEDZ.equals(activityType)){
						discountrAmount = discountrAmount +(Double.parseDouble(json.optString("item_all_amount")) * (100-Double.parseDouble(json.optString("discount_amount")))/100);
					}else if(SysDictionary.MARKETING_ACTIVITY_MEJJG.equals(activityType)){
						discountrAmount = discountrAmount + (Double.parseDouble(json.optString("hdzc_item_amount")) - Double.parseDouble(json.optString("discount_amount")));
					}else if(SysDictionary.MARKETING_ACTIVITY_MEZC.equals(activityType)){
						discountrAmount = discountrAmount + Double.parseDouble(json.optString("hdzc_item_amount"));
					}else if(SysDictionary.MARKETING_ACTIVITY_ZHTYJ.equals(activityType)){
						discountrAmount = discountrAmount + Double.parseDouble(json.optString("hdtyj_item_amount")) - Double.parseDouble(json.optString("discount_amount"));
					}else if(SysDictionary.MARKETING_ACTIVITY_DEFBJ.equals(activityType)){
						discountrAmount = discountrAmount + Double.parseDouble(json.optString("hdbj_item_amount")) * (100-Double.parseDouble(json.optString("discount_amount")))/100;
					}
				}
				//更新账单
				String sqlBill = " update pos_bill set discount_mode_id = ?, discount_rate=?, discountr_amount=? where bill_num = ? and store_id = ? ";
				posActivityDao.update(sqlBill, new Object[]
						{ discount_mode_id, discount_rate, discountrAmount, billNum, storeId });

				//修改数据库中activity_id为空的菜品
				//更新账单明细
				String sqlBillItem = "update pos_bill_item set discount_mode_id = ?, discount_reason_id = ?, discount_rate = ? where bill_num = ? and store_id = ?";
				sqlBillItem += " and (discount_mode_id <> 10 or discount_mode_id is null) ";
				posActivityDao.update(sqlBillItem, new Object[]
						{discount_mode_id, discount_reason_id, discount_rate, billNum, storeId });
				
				this.calcAmount(tenancyId, storeId, billNum);

				logger.info("保存折扣明细----start--billNum:"+billNum);
				posActivityDao.insertItemDiscountList(tenancyId,storeId, billNum);
				logger.info("保存折扣明细----end--billNum:"+billNum);

				logger.info("账单号："+billNum+"使用活动end=============");
			}else{
				this.calcAmount(tenancyId, storeId, billNum);
				logger.info("账单号："+billNum+"未使用活动end===========");
			}
			
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("使用活动错误："+ ExceptionMessage.getExceptionMessage(e));
		}
		
	}

}
