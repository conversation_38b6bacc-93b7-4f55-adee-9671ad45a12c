/** 
 * @(#)CodeFormat.java    1.0   2017-05-16
 * 
 * Copyright 北京普照天星科技有限公司. All rights reserved. 
 * PROPRIETARY/CONFIDENTIAL. Use is subject to license terms. 
 */
package com.tzx.pos.base.service;

/**
 * 门店端数据同步程序.
 *
 * <AUTHOR> email:q<PERSON><PERSON><PERSON><PERSON>@tzx.com.cn
 * @version 1.0  2017-05-16
 * @see     
 * @since   JDK7.0
 * @update  
 */
public interface PosDataSyncService{	
		
	/**
	 * @Description 门店端pos_soldout表数据同步至redis缓存（全量数据同步）。
	 * @param 
	 * @return 
	 * @exception
	 * <AUTHOR> email:<EMAIL>
	 * @version 1.0  2017-05-16
	 * @see
	 */
	void dataSyncForPosSoldOut(String tenentId,String storeId);
	
	/**
	 * @Description 重建数据库索引（不使用事物）
	 * @return 
	 * <AUTHOR> email:qiu<PERSON><PERSON><PERSON>@tzx.com.cn
	 * @version 1.0  2018-12-07
	 * @see
	 */
	public void reIndex();	
		
}
