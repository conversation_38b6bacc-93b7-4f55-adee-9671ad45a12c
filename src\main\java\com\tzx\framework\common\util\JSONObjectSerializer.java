/*
 * Copyright Etao.cn
 */

package com.tzx.framework.common.util;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import net.sf.json.JSON;

import java.io.IOException;

/**
 * JSONObject自定义转换
 * <AUTHOR>
 * @since 16-5-24 下午2:46.
 */
public class JSONObjectSerializer extends JsonSerializer<JSON> {
    @Override
    public Class<JSON> handledType() {
        return JSON.class;
    }
    @Override
    public void serialize(J<PERSON><PERSON> jsonObject, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException, JsonProcessingException {
        jsonGenerator.writeRawValue(jsonObject.toString());
    }
}
