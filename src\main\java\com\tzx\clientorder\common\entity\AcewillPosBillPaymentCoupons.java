package com.tzx.clientorder.common.entity;

import java.util.Date;

public class AcewillPosBillPaymentCoupons {

	/**
	 * 商户id
	 */
	private String tenancy_id;
	/**
	 * 机构id
	 */
	private Integer store_id;
	/**
	 * ID-自增
	 */
	private Integer id;
	/**
	 * 报表日期
	 */
	private String bill_num;
	/**
	 * 账单编号
	 */
	private Date report_date;
	/**
	 * 付款明细UID
	 */
	private String payment_id;
	/**
	 * 优惠劵号
	 */
	private String coupons_code;
	/**
	 * 面值
	 */
	private Double deal_value;
	/**
	 * 劵类型名称
	 */
	private String deal_name;
	/**
	 * 操作时间
	 */
	private Date last_updatetime;
	/**
	 * 备注
	 */
	private String remark;
	/**
	 * 上传标记
	 */
	private String upload_tag;
	/**
	 * 是否撤销
	 */
	private String is_cancel;
	/**
	 * 优惠券大类ID
	 */
	private Integer class_id;
	/**
	 * 优惠券类型ID
	 */
	private Integer type_id;
	/**
	 * 优惠金额
	 */
	private Double discount_money;
	/**
	 * 抵用数量
	 */
	private Double discount_num;
	/**
	 * 渠道
	 */
	private String chanel;
	/**
	 * 菜品单价
	 */
	private Double price;
	/**
	 * 抵扣菜品
	 */
	private Integer item_id;
	/**
	 * 菜品数量
	 */
	private Integer item_num;
	private Integer rwid;
	private Integer item_unit_id;
	/**
	 * 优惠劵编码
	 */
	private String coupons_pro;
	/**
	 * 优惠劵类型
	 */
	private Integer coupon_type;
	private Double coupon_buy_price;
	private Double due;
	private Double tenancy_assume;
	private Double third_assume;
	private Double third_fee;
	private Integer request_state;
	public String getTenancy_id() {
		return tenancy_id;
	}
	public void setTenancy_id(String tenancy_id) {
		this.tenancy_id = tenancy_id;
	}
	public Integer getStore_id() {
		return store_id;
	}
	public void setStore_id(Integer store_id) {
		this.store_id = store_id;
	}
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public String getBill_num() {
		return bill_num;
	}
	public void setBill_num(String bill_num) {
		this.bill_num = bill_num;
	}
	public Date getReport_date() {
		return report_date;
	}
	public void setReport_date(Date report_date) {
		this.report_date = report_date;
	}
	public String getPayment_id() {
		return payment_id;
	}
	public void setPayment_id(String payment_id) {
		this.payment_id = payment_id;
	}
	public String getCoupons_code() {
		return coupons_code;
	}
	public void setCoupons_code(String coupons_code) {
		this.coupons_code = coupons_code;
	}
	public Double getDeal_value() {
		return deal_value;
	}
	public void setDeal_value(Double deal_value) {
		this.deal_value = deal_value;
	}
	public String getDeal_name() {
		return deal_name;
	}
	public void setDeal_name(String deal_name) {
		this.deal_name = deal_name;
	}
	public Date getLast_updatetime() {
		return last_updatetime;
	}
	public void setLast_updatetime(Date last_updatetime) {
		this.last_updatetime = last_updatetime;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public String getUpload_tag() {
		return upload_tag;
	}
	public void setUpload_tag(String upload_tag) {
		this.upload_tag = upload_tag;
	}
	public String getIs_cancel() {
		return is_cancel;
	}
	public void setIs_cancel(String is_cancel) {
		this.is_cancel = is_cancel;
	}
	public Integer getClass_id() {
		return class_id;
	}
	public void setClass_id(Integer class_id) {
		this.class_id = class_id;
	}
	public Integer getType_id() {
		return type_id;
	}
	public void setType_id(Integer type_id) {
		this.type_id = type_id;
	}
	public Double getDiscount_money() {
		return discount_money;
	}
	public void setDiscount_money(Double discount_money) {
		this.discount_money = discount_money;
	}
	public Double getDiscount_num() {
		return discount_num;
	}
	public void setDiscount_num(Double discount_num) {
		this.discount_num = discount_num;
	}
	public String getChanel() {
		return chanel;
	}
	public void setChanel(String chanel) {
		this.chanel = chanel;
	}
	public Double getPrice() {
		return price;
	}
	public void setPrice(Double price) {
		this.price = price;
	}
	public Integer getItem_id() {
		return item_id;
	}
	public void setItem_id(Integer item_id) {
		this.item_id = item_id;
	}
	public Integer getItem_num() {
		return item_num;
	}
	public void setItem_num(Integer item_num) {
		this.item_num = item_num;
	}
	public Integer getRwid()
	{
		return rwid;
	}
	public void setRwid(Integer rwid)
	{
		this.rwid = rwid;
	}
	public Integer getItem_unit_id()
	{
		return item_unit_id;
	}
	public void setItem_unit_id(Integer item_unit_id)
	{
		this.item_unit_id = item_unit_id;
	}
	public String getCoupons_pro() {
		return coupons_pro;
	}
	public void setCoupons_pro(String coupons_pro) {
		this.coupons_pro = coupons_pro;
	}
	public Integer getCoupon_type() {
		return coupon_type;
	}
	public void setCoupon_type(Integer coupon_type) {
		this.coupon_type = coupon_type;
	}
	public Double getCoupon_buy_price() {
		return coupon_buy_price;
	}
	public void setCoupon_buy_price(Double coupon_buy_price) {
		this.coupon_buy_price = coupon_buy_price;
	}
	public Double getDue() {
		return due;
	}
	public void setDue(Double due) {
		this.due = due;
	}
	public Double getTenancy_assume() {
		return tenancy_assume;
	}
	public void setTenancy_assume(Double tenancy_assume) {
		this.tenancy_assume = tenancy_assume;
	}
	public Double getThird_assume() {
		return third_assume;
	}
	public void setThird_assume(Double third_assume) {
		this.third_assume = third_assume;
	}
	public Double getThird_fee() {
		return third_fee;
	}
	public void setThird_fee(Double third_fee) {
		this.third_fee = third_fee;
	}
	public Integer getRequest_state() {
		return request_state;
	}
	public void setRequest_state(Integer request_state) {
		this.request_state = request_state;
	}
	
}
