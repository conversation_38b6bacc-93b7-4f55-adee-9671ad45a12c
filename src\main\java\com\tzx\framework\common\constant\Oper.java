package com.tzx.framework.common.constant;

public enum Oper
{
	/**
	 * 验证
	 */
	check,
	/**
	 * 全部加载
	 */
	load,
	/**
	 * 新增
	 */
	add,
	/**
	 * 修改
	 */
	update,
	/**
	 * 删除
	 */
	delete,
	/**
	 * 查询
	 */
	find,
	/**
	 * 初始化
	 */
	init,
	/**
	 * 上传
	 */
	upload,
	/**
	 * 预下单支付
	 */
	scan,
	/**
	 * 条码支付
	 */
	barcode,
	/**
	 * 
	 */
	precreate,

	/**
	 * 取消订单
	 */
	cancle,
	/**
	 * 微信查询
	 */
	query,
	/**
	 * 申请退款
	 */
	refund,
	/**
	 * 重新下单
	 */
	reorder,
	/**
	 * 查询退款
	 */
	refundquery,
	/**
	 * 撤销订单
	 */
	reverse,
	/**
	 * 投诉
	 */
	opinion,
	/**
	 * 订单完成
	 */
	complete,
	
	/* ********************scm 使用追加********************* */
	/**
	 * 插号
	 */
	insert,
	
	/**
	 * 开户
	 */
	open,
	/**
	 * 查询明细
	 */
	detail,
	/**
	 * 查询上架物品
	 */
	queryup,
	/**
	 * 更新菜品接口
	 */
	updatedish,
	/**
	 * 支付超时修改订单支付状态为已失效
	 */
	paytimeout,
	/**
	 * 打印
	 */
	print,
	/**
	 * 消费
	 */
	consume,
	/**
	 * 准备验劵
	 */
	prepare,
	/**
	 * 消息推送
	 */
	notice,
    /**
     * 订单状态查询
     */
    order_state_query,
	/**
	 * 
	 */
	feedback,
    /**
     * 反充值
     */
    recharge_back,
    /**
     * 反消费
     */
    consumption_back, 
    /**
     * 提交确认
     */
    commit,
    /**
     * 发送验证码
     */
    sendcode, 
    /**
     * 数据上传-上传账单数据
     */
    upload_bill_info, 
    /**
     * 数据上传-上传交班,交款数据
     */
    upload_changeshift_info,
	/**
	 * 新美大验券查询对账
	 */
	trade_detail,
    /**
     * 数据重传
     */
    data_reupload,
    /**
     * 重新收取订单
     */
    add_modify,
    
    /**
     * 重新取消订单
     */
    cancle_modify,
    /**
     * 更改派送方式重新下发
     */
    replace_modify,
    /**
     * 外卖异常订单改为自配送
     */
    deliver_exception,
	/**
	 * 点赞会员标签
	 */
	praise,
	/**
	 * 重试
	 */
	retry,

	/**
	 * 储值条码支付
	 */
	charge_barcode,

	/**
	 * 储值获取二维码
	 */
	charge_precreate,

	/**
	 * 储值列表
	 */
	charge_list,
	/**
	 * 储值列表
	 */
	charge_revoke,
	/**
	 * 消费列表
	 */
	consume_list,

	/**
	 * 积分规则
	 */
	credit_rule,
	/**
	 * 积分换礼
	 */
	credit_exchange
    ;
}
