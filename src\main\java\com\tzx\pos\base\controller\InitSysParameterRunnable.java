package com.tzx.pos.base.controller;

import java.util.Map;

import org.apache.log4j.Logger;

import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.pos.bo.PosBaseService;
import com.tzx.pos.bo.imp.PosBaseServiceImp;

/**
 * 读取pos_config.properties文件
 * 初始化sys_parameter表系统参数
 *
 */

public class InitSysParameterRunnable implements Runnable {
	
	private static final Logger logger = Logger.getLogger(InitSysParameterRunnable.class);
	
	@Override
	public void run() {
		// TODO Auto-generated method stub
		try{
			Map<String, String> systemMap = Constant.getSystemMap();
			
			if (!systemMap.containsKey("tenent_id") || !systemMap.containsKey("store_id") || "".equals(systemMap.get("tenent_id")) || "".equals(systemMap.get("store_id")))
			{
				logger.info("参数为空");
				return;
			}
			
			while(SpringConext.getApplicationContext() == null){
				Thread.sleep(1000);
			}
			
//			logger.info("---spring上下文对象---"+SpringConext.getApplicationContext());
			
			String tenentId = systemMap.get("tenent_id");
			String storeId = systemMap.get("store_id");
			DBContextHolder.setTenancyid(tenentId);
			
			PosBaseService baseService = (PosBaseServiceImp) SpringConext.getApplicationContext().getBean(PosBaseService.NAME);
			baseService.initSysParamterDataByProties(tenentId, Integer.parseInt(storeId));
			logger.info("新美大系统参数初始化成功！");
			
		}catch(Exception e){
			logger.error("初始化新美大系统参数失败...", e);
		}
	}

}
