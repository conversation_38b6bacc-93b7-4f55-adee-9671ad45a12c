connect_timeout = 2
network_timeout = 30
charset = UTF-8
http.tracker_http_port = 8888
http.anti_steal_token = no
http.secret_key = FastDFS1234567890

tracker_server = ${tracker_server.url}:${tracker_server.port}

#kb
section_size = 1 
#\u4E0A\u4F20\u5931\u8D25\u5C1D\u8BD5\u4E0A\u4F20\u7684\u6B21\u6570
try_upload_times = 3
#\u6BCF\u6B21\u5931\u8D25\u540E\u95F4\u9694\u7B49\u5F85\u65F6\u95F4 s
try_interval_time= 5	


#s more than storage conf connect_time
ingore_diff_time=10
