package com.tzx.clientorder.wlifeprogram.common.util;

import java.beans.PropertyDescriptor;
import java.util.Map;

import org.springframework.beans.BeanUtils;

import com.tzx.clientorder.wlifeprogram.common.entity.MicroLifeOrder;

import net.sf.json.JsonConfig;
import net.sf.json.util.PropertyFilter;

public class WlifeProgramUtil
{
	public static JsonConfig getJsonConfig(Class<?> rootClass, Map<String, Class<?>> classMap)
	{
		JsonConfig config = new JsonConfig();
		config.setClassMap(classMap);
		config.setRootClass(rootClass);
		config.setJavaPropertyFilter(new PropertyFilter()
		{
			@Override
			public boolean apply(Object source, String name, Object value)
			{
				PropertyDescriptor propertyDescriptor = BeanUtils.getPropertyDescriptor(source.getClass(), name);
				return propertyDescriptor == null;
			}
		});
		return config;
	}
}
