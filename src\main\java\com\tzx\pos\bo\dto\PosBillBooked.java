package com.tzx.pos.bo.dto;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> 
 * @日期：2015年5月18日-下午7:02:25
 */

public class PosBillBooked implements Serializable
{
	/**
	 * 
	 */
	private static final long	serialVersionUID	= 1L;
	
	private String id;
	private String bill_booked_num;
	private String serial_num;
	private String batch_num;
	private String report_date;
	private String shift_id;
	private String chanel;

	private String opentable_time;
	private String open_pos_num;
	private String open_opt;
	private String payment_time;
	private String pos_num;
	private String cashier_num;
	private String waiter_num;
	
	private String service_id;
	private String service_name;
	private String service_amount;
	private String discount_case_id;
	private String discount_case_name;
	private String discount_rate;
	private String discount_mode_id;
	private String discountk_amount;
	private String discountr_amount;
	private String discount_amount;
	private String subtotal;
	private String subtotal2;//不包含奉送金额的小计
	private String payment_amount;
	private String maling_amount;
	private String difference;
	
	private String givi_amount;
	private String guest;
	private String average_amount;
	private String guest_msg;
	private String remark;
	private String print_count;
	private String bill_state;
	private String bill_property;
	private String payment_state;
	private String copy_bill_num;
	private String bill_taste;
	private String order_num;
	private String fictitious_table;
	
	private String none_discount_amount;//不打折金额
	
	private List<?> detaillist; //账单明细
	private List<?> paymentlist; //支付方式
	private List<?> memberlist;//会员积分
	private List<?> invoicelist;//发票
	private List<?> servicelist;//(附加服务费)
	private List<?> batchlist;//pos_bill_batchs
	
	private List<?> mesList;
	private String load_state;
	public String getId()
	{
		return id;
	}
	public void setId(String id)
	{
		this.id = id;
	}
	
	public String getSerial_num()
	{
		return serial_num;
	}
	public void setSerial_num(String serial_num)
	{
		this.serial_num = serial_num;
	}
	public String getBatch_num()
	{
		return batch_num;
	}
	public void setBatch_num(String batch_num)
	{
		this.batch_num = batch_num;
	}
	
	public String getReport_date()
	{
		return report_date;
	}
	public void setReport_date(String report_date)
	{
		this.report_date = report_date;
	}
	public String getShift_id()
	{
		return shift_id;
	}
	public void setShift_id(String shift_id)
	{
		this.shift_id = shift_id;
	}
	public String getOpentable_time()
	{
		return opentable_time;
	}
	public void setOpentable_time(String opentable_time)
	{
		this.opentable_time = opentable_time;
	}
	public String getOpen_pos_num()
	{
		return open_pos_num;
	}
	public void setOpen_pos_num(String open_pos_num)
	{
		this.open_pos_num = open_pos_num;
	}
	public String getOpen_opt()
	{
		return open_opt;
	}
	public void setOpen_opt(String open_opt)
	{
		this.open_opt = open_opt;
	}
	public String getPayment_time()
	{
		return payment_time;
	}
	public void setPayment_time(String payment_time)
	{
		this.payment_time = payment_time;
	}
	public String getPos_num()
	{
		return pos_num;
	}
	public void setPos_num(String pos_num)
	{
		this.pos_num = pos_num;
	}
	public String getCashier_num()
	{
		return cashier_num;
	}
	public void setCashier_num(String cashier_num)
	{
		this.cashier_num = cashier_num;
	}
	public String getWaiter_num()
	{
		return waiter_num;
	}
	public void setWaiter_num(String waiter_num)
	{
		this.waiter_num = waiter_num;
	}
	public String getService_id()
	{
		return service_id;
	}
	public void setService_id(String service_id)
	{
		this.service_id = service_id;
	}
	public String getService_name()
	{
		return service_name;
	}
	public void setService_name(String service_name)
	{
		this.service_name = service_name;
	}
	public String getService_amount()
	{
		return service_amount;
	}
	public void setService_amount(String service_amount)
	{
		this.service_amount = service_amount;
	}
	public String getDiscount_case_id()
	{
		return discount_case_id;
	}
	public void setDiscount_case_id(String discount_case_id)
	{
		this.discount_case_id = discount_case_id;
	}
	public String getDiscount_case_name()
	{
		return discount_case_name;
	}
	public void setDiscount_case_name(String discount_case_name)
	{
		this.discount_case_name = discount_case_name;
	}
	public String getDiscount_rate()
	{
		return discount_rate;
	}
	public void setDiscount_rate(String discount_rate)
	{
		this.discount_rate = discount_rate;
	}
	public String getDiscount_mode_id()
	{
		return discount_mode_id;
	}
	public void setDiscount_mode_id(String discount_mode_id)
	{
		this.discount_mode_id = discount_mode_id;
	}
	public String getDiscountk_amount()
	{
		return discountk_amount;
	}
	public void setDiscountk_amount(String discountk_amount)
	{
		this.discountk_amount = discountk_amount;
	}
	public String getDiscountr_amount()
	{
		return discountr_amount;
	}
	public void setDiscountr_amount(String discountr_amount)
	{
		this.discountr_amount = discountr_amount;
	}
	public String getDiscount_amount()
	{
		return discount_amount;
	}
	public void setDiscount_amount(String discount_amount)
	{
		this.discount_amount = discount_amount;
	}
	public String getSubtotal()
	{
		return subtotal;
	}
	public void setSubtotal(String subtotal)
	{
		this.subtotal = subtotal;
	}
	public String getPayment_amount()
	{
		return payment_amount;
	}
	public void setPayment_amount(String payment_amount)
	{
		this.payment_amount = payment_amount;
	}
	public String getMaling_amount()
	{
		return maling_amount;
	}
	public void setMaling_amount(String maling_amount)
	{
		this.maling_amount = maling_amount;
	}
	public String getDifference()
	{
		return difference;
	}
	public void setDifference(String difference)
	{
		this.difference = difference;
	}
	public String getGivi_amount()
	{
		return givi_amount;
	}
	public void setGivi_amount(String givi_amount)
	{
		this.givi_amount = givi_amount;
	}
	public String getGuest()
	{
		return guest;
	}
	public void setGuest(String guest)
	{
		this.guest = guest;
	}
	public String getAverage_amount()
	{
		return average_amount;
	}
	public void setAverage_amount(String average_amount)
	{
		this.average_amount = average_amount;
	}
	public String getGuest_msg()
	{
		return guest_msg;
	}
	public void setGuest_msg(String guest_msg)
	{
		this.guest_msg = guest_msg;
	}
	public String getRemark()
	{
		return remark;
	}
	public void setRemark(String remark)
	{
		this.remark = remark;
	}
	public String getPrint_count()
	{
		return print_count;
	}
	public void setPrint_count(String print_count)
	{
		this.print_count = print_count;
	}
	public String getBill_state()
	{
		return bill_state;
	}
	public void setBill_state(String bill_state)
	{
		this.bill_state = bill_state;
	}
	public String getBill_property()
	{
		return bill_property;
	}
	public void setBill_property(String bill_property)
	{
		this.bill_property = bill_property;
	}
	public String getPayment_state()
	{
		return payment_state;
	}
	public void setPayment_state(String payment_state)
	{
		this.payment_state = payment_state;
	}
	public String getCopy_bill_num() {
		return copy_bill_num;
	}
	public void setCopy_bill_num(String copy_bill_num) {
		this.copy_bill_num = copy_bill_num;
	}
	public List<?> getDetaillist()
	{
		return detaillist;
	}
	public void setDetaillist(List<?> detaillist)
	{
		this.detaillist = detaillist;
	}
	public List<?> getPaymentlist()
	{
		return paymentlist;
	}
	public void setPaymentlist(List<?> paymentlist)
	{
		this.paymentlist = paymentlist;
	}
	public List<?> getMemberlist()
	{
		return memberlist;
	}
	public void setMemberlist(List<?> memberlist)
	{
		this.memberlist = memberlist;
	}
	public List<?> getInvoicelist()
	{
		return invoicelist;
	}
	public void setInvoicelist(List<?> invoicelist)
	{
		this.invoicelist = invoicelist;
	}
	
	public List<?> getServicelist()
	{
		return servicelist;
	}
	
	public void setServicelist(List<?> serviceList)
	{
		this.servicelist = serviceList;
	}
	
	public List<?> getBatchList()
	{
		return batchlist;
	}
	public void setBatchList(List<?> batchList)
	{
		this.batchlist = batchList;
	}
	
	public String getChanel()
	{
		return chanel;
	}
	public void setChanel(String chanel)
	{
		this.chanel = chanel;
	}
	public String getSubtotal2()
	{
		return subtotal2;
	}
	public void setSubtotal2(String subtotal2)
	{
		this.subtotal2 = subtotal2;
	}
	
	public String getBill_taste() {
		return bill_taste;
	}
	public void setBill_taste(String bill_taste) {
		this.bill_taste = bill_taste;
	}
	public String getOrder_num()
	{
		return order_num;
	}
	public void setOrder_num(String order_num)
	{
		this.order_num = order_num;
	}
	public String getFictitious_table() {
		return fictitious_table;
	}
	public void setFictitious_table(String fictitious_table) {
		this.fictitious_table = fictitious_table;
	}
	public String getNone_discount_amount()
	{
		return none_discount_amount;
	}
	public void setNone_discount_amount(String none_discount_amount)
	{
		this.none_discount_amount = none_discount_amount;
	}
	public String getBill_booked_num() {
		return bill_booked_num;
	}
	public void setBill_booked_num(String bill_booked_num) {
		this.bill_booked_num = bill_booked_num;
	}
	public List<?> getBatchlist() {
		return batchlist;
	}
	public void setBatchlist(List<?> batchlist) {
		this.batchlist = batchlist;
	}
	public List<?> getMesList() {
		return mesList;
	}
	public void setMesList(List<?> mesList) {
		this.mesList = mesList;
	}
	public String getLoad_state() {
		return load_state;
	}
	public void setLoad_state(String load_state) {
		this.load_state = load_state;
	}
}
