package com.tzx.pos.bo;

import java.util.Date;
import java.util.List;

import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.entity.Pagination;

import net.sf.json.JSONObject;

/**
 * 交款相关
 * 
 * <AUTHOR>
 *
 */
public interface PosCashierReceiveService extends PosBaseService
{
	String NAME = "com.tzx.pos.bo.imp.PosCashierReceiveServiceImp";

	/**
	 * 交款统计
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param param
	 * @param pagination
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> totalCashierReceive(String tenancyId, int storeId, List<?> param, Pagination pagination) throws Exception;

	/**
	 * 交款详细统计
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param param
	 * @param pagination
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> cashierReceiveDetails(String tenancyId, int storeId, List<?> param, Pagination pagination) throws Exception;

	/**
	 * 交款/抽大钞
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param param
	 * @throws Exception
	 */
	void addCashierReceive(String tenancyId, int storeId, List<?> param, JSONObject printJson) throws Exception;

	/**
	 * 交款/抽大钞
	 *
	 * @param tenancyId
	 * @param storeId
	 * @param param
	 * @throws Exception
	 */
	public void addCashierReceiveBatch(String tenancyId, int storeId, Data param, List<JSONObject> printJsonList) throws Exception;

	/**
	 * 交款流水查询
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param param
	 * @param pagination
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> findCashierReceive(String tenancyId, int storeId, List<?> param, Pagination pagination) throws Exception;

	/**
	 * 校验是否有未交款
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param reportDate
	 * @throws Exception
	 */
	public boolean checkCashierReceive(String tenantId, Integer storeId, Date reportDate) throws Exception;
	
	/**
	 * 签退时检查交款差额
	 * 
	 * @param param
	 * @param result
	 * @return
	 * @throws Exception
	 */
	void checkReceive(Data param, Data result) throws Exception;
	
	/**
	 * 交款限额验证
	 * 
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public Data cashierLimitVlidate(Data param) throws Exception;
}
