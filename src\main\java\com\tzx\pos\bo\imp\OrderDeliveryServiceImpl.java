package com.tzx.pos.bo.imp;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.log4j.Logger;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;

import com.alipay.api.internal.util.StringUtils;
import com.tzx.base.common.util.PosPropertyUtil;
import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.util.Comet4jUtil;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.HttpUtil;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.orders.po.springjdbc.dao.OrderDeliveryDao;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.controller.CometDataNoticeClientRunnable;
import com.tzx.pos.base.dao.imp.BaseDaoImp;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.base.util.SendUdpToPos;
import com.tzx.pos.bo.OrderDeliveryService;
/**
 *  订单配送
 * <AUTHOR>
 *
 */
@Service(OrderDeliveryService.NAME)
public class OrderDeliveryServiceImpl extends BaseDaoImp implements OrderDeliveryService{

	protected static final Logger		logger				= Logger.getLogger(OrderDeliveryServiceImpl.class);

	@Resource(name=OrderDeliveryDao.NAME)
	private OrderDeliveryDao	dao;
	
	private final String   FIND_ORDER_DELIVERY = "/findDeliveryAddressInfo";//查询配送订单地址

	private final String   ADD_ORDER_DELIVERY = "/createDeliverOrder";//新增配送订单
	
	private final String   FIND_EXCEPTION_ORDER_DELIVERY = "/checkIsAllowToSelfDeliver";//查询异常订单是否允许配送

	private final String   QUERY_ORDER_DELIVERY = "/queryDeliverOrder";//查询配送订单信息
	
	private final String   ADD_ORDER_addTips = "/addTips";//添加小费
	
	
	private final String DELIVER_CHANNEL_WM ="WM";
	private final String DELIVER_CHANNEL_WX ="WX";
	private final String DELIVER_CHANNEL_TS ="TS";
	
	public String getPequestUrl(String url)
	{

		//String saas_url = PosPropertyUtil.getMsg("saas.url");
        String saas_url = PosPropertyUtil.getMsg("waimai.orderdelivery.url");
	    logger.debug("总部地址："+saas_url);
		return saas_url + url;
	}
	
	@Override
	public void findOrderDeliveryAddress(Data data, Data result) {
		String url = getPequestUrl(FIND_ORDER_DELIVERY); 
		//String url = "http://localhost:8091/tzxsaas/deliverRest/test/findDeliveryAddressInfo";
		try
		{
			long t = System.currentTimeMillis();
			JSONObject paramJson = JSONObject.fromObject(data.getData().get(0));
			logger.info(String.valueOf(t)+"<查询地址发送接口请求体==>" + paramJson);
			String findResult = HttpUtil.sendPostRequest(url, paramJson.toString(), 3000, 5000);
			logger.info(String.valueOf(t)+"<查询地址发送接口返回体==>" + findResult);
			if (StringUtils.isEmpty(findResult))
			{
				result.setCode(Constant.CODE_CONN_EXCEPTION);
				result.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
				result.setSuccess(false);
			}
			else
			{
//				try
//				{
					JSONObject resultJo = JSONObject.fromObject(findResult);
					if(resultJo.containsKey("error")){
						int error = resultJo.optInt("error");
						String msg = resultJo.optString("msg");
						if(error==1){
							result.setCode(Constant.CODE_CONN_EXCEPTION);
							result.setMsg(msg);
							result.setSuccess(false);
						}
					}else{
						List<JSONObject> listJson = new ArrayList<JSONObject>();
						listJson.add(resultJo);
						result.setData(listJson);
					}
					
//				}
//				catch (Exception se)
//				{
//					logger.info("查询配送订单地址信息错误：" + ExceptionMessage.getExceptionMessage(se));
//					se.printStackTrace();
//				}
			}
		}
		catch (Exception se)
		{
			logger.info("查询配送订单地址错误，连接超时，请检查网络,请求体为：" + data);
			result.setCode(5);
			result.setMsg("无法查询配送地址");
			result.setSuccess(false);
			se.printStackTrace();
		}
		
	}

	@Override
	public void addOrderDelivery(Data param, Data result) {
		String url = getPequestUrl(ADD_ORDER_DELIVERY); 
		//String url = "http://localhost:8091/tzxsaas/deliverRest/createDeliverOrder";
		try
		{
			JSONObject paramJson = JSONObject.fromObject(param.getData().get(0));
				
			String channel = paramJson.optString("channel");
			JSONArray ja = new JSONArray();
			if(null == channel||"".equals(channel)){
				return;
			}
			if(DELIVER_CHANNEL_WM.equals(channel)){
				String wmSql = "select bi.item_name as name,bi.number as quantity,bi.product_fee as price,bi.net_income_amount as actualPrice from  cc_order_item bi where bi.order_code= '"+paramJson.optString("orderCode")+"' ";
				List<JSONObject> wmListJo = dao.queryString4Json(paramJson.optString("tanancyId"), wmSql);
				if(wmListJo!=null&&wmListJo.size()>0){
					for (int i = 0; i < wmListJo.size(); i++) {
						JSONObject wmJofind = wmListJo.get(i);
						JSONObject wmJo = new JSONObject();
						wmJo.put("name", wmJofind.optString("name"));
						wmJo.put("quantity", wmJofind.optInt("quantity"));
						wmJo.put("price", wmJofind.optDouble("price"));
						wmJo.put("actualPrice", ParamUtil.getDoubleValueByObject(wmJofind, "actualPrice"));
						ja.add(wmJo);
					}
				}
			}else if(DELIVER_CHANNEL_TS.equals(channel)){
				String tsSql = "select bi.item_name as name,bi.item_count as quantity,bi.item_price as price,bi.real_amount as actualPrice from pos_bill_item bi where (bi.item_remark is null or (bi.item_remark<>'TC01' and bi.item_remark<>'CJ05')) and bi.bill_num= '"+paramJson.optString("orderCode")+"' order by bi.item_serial,bi.assist_item_id";
				List<JSONObject> tsListJo = dao.queryString4Json(paramJson.optString("tanancyId"), tsSql);
				if(tsListJo!=null&&tsListJo.size()>0){
					for (int i = 0; i < tsListJo.size(); i++) {
						JSONObject tsJofind = tsListJo.get(i);
						JSONObject tsJo = new JSONObject();
						tsJo.put("name", tsJofind.optString("name"));
						tsJo.put("quantity", tsJofind.optInt("quantity"));
						tsJo.put("price", tsJofind.optDouble("price"));
						tsJo.put("actualPrice", ParamUtil.getDoubleValueByObject(tsJofind, "actualPrice"));
						ja.add(tsJo);
					}
				}
			}
			
			paramJson.put("item", ja);
			
			long t = System.currentTimeMillis();
			logger.info(String.valueOf(t)+"<发送接口请求体==>" + paramJson.toString());
			String findResult = HttpUtil.sendPostRequest(url, paramJson.toString(), 3000, 5000);
			logger.info(String.valueOf(t)+"<发送接口返回体==>" + findResult);
			if (StringUtils.isEmpty(findResult))
			{
				result.setCode(Constant.CODE_CONN_EXCEPTION);
				result.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
				result.setSuccess(false);
			}
			else
			{
				try
				{
					JSONObject resultJo = JSONObject.fromObject(findResult);
					if(resultJo.containsKey("error")){
					int code = resultJo.optInt("error");
					String msg = resultJo.optString("msg");
					logger.info("发送配送订单返回信息:code:"+code+"-----"+"msg:"+msg);
					if(code==0||code==23){//0 成功 23配送单已存在，不允许重复下单
						String deliverySuccesssql = "update cc_deliver_list  set order_state = ? , order_state_reason = ? where order_code = ? and shop_id = ? ";
						dao.update(deliverySuccesssql, new Object[] {'1',msg,paramJson.optString("orderCode"),paramJson.optString("shopId")});
					    
						
					    String orderSuccessSql  = "update cc_order_list set find_state = 1  where tenancy_id = ? and store_id = ? and order_code = ?";
					    dao.update(orderSuccessSql,new Object[]{paramJson.optString("tanancyId"),Integer.valueOf(paramJson.optString("shopId")),paramJson.optString("orderCode")});
					    
//					    String orderSuccessSql  = "update cc_order_list set find_state = 1 ,delivery_state = '01' where tenancy_id = ? and store_id = ? and order_code = ?";
//					    dao.update(orderSuccessSql,new Object[]{paramJson.optString("tanancyId"),Integer.valueOf(paramJson.optString("shopId")),paramJson.optString("orderCode")});
					}
//					else if(code==16||code==18||code==22||code==23){
//						String orderSuccessSql  = "update cc_order_list set find_state = 0 ,delivery_state = '01' where tenancy_id = ? and store_id = ? and order_code = ?";
//					    dao.update(orderSuccessSql,new Object[]{paramJson.optString("tanancyId"),Integer.valueOf(paramJson.optString("shopId")),paramJson.optString("orderCode")});
//					}else if(code==15){
//						String orderSuccessSql  = "update cc_order_list set find_state = 1 ,delivery_state = '06' where tenancy_id = ? and store_id = ? and order_code = ?";
//					    dao.update(orderSuccessSql,new Object[]{paramJson.optString("tanancyId"),Integer.valueOf(paramJson.optString("shopId")),paramJson.optString("orderCode")});
//					}else if(code==25){
//						String orderSuccessSql  = "update cc_order_list set find_state = 1 ,delivery_state = '07' where tenancy_id = ? and store_id = ? and order_code = ?";
//					    dao.update(orderSuccessSql,new Object[]{paramJson.optString("tanancyId"),Integer.valueOf(paramJson.optString("shopId")),paramJson.optString("orderCode")});
//					}
					else{
						String deliverySuccesssql = "update cc_deliver_list  set order_state = ? , order_state_reason = ? where order_code = ? and shop_id = ? ";
						dao.update(deliverySuccesssql, new Object[] {'0',msg,paramJson.optString("orderCode"),paramJson.optString("shopId")});
					}
					}
					List<JSONObject> listJson = new ArrayList<JSONObject>();
					listJson.add(resultJo);
					result.setData(listJson);
				}
				catch (Exception se)
				{
					logger.info("配送订单添加本地错误：" + ExceptionMessage.getExceptionMessage(se));
					se.printStackTrace();
				}
			}
		}
		catch (Exception se)
		{
			logger.info("添加订单错误，连接超时，请检查网络,请求体为：" + param);
			result.setCode(5);
			result.setMsg("无法添加订单");
			result.setSuccess(false);
			se.printStackTrace();
		}
		
	}

	@Override
	public void addBohOrderDelivery(Data param, Data result) {
		try {
			if(param.getData()!=null&&param.getData().size()>0){
				JSONObject paramJson = JSONObject.fromObject(param.getData().get(0));

				Double tips = 0.0;
				if(paramJson.containsKey("tips")){
					tips = paramJson.getDouble("tips");
				}
				Timestamp time = DateUtil.getNowTimestamp();
				//String time = DateUtil.format(new Timestamp(System.currentTimeMillis()));
				int orderdeliveryId = 0;
				String orderdeliverySql = "select id from cc_deliver_list where order_code = ? and shop_id = ?";
				SqlRowSet orderdeliveryRowSet = dao.query4SqlRowSet(orderdeliverySql, new Object[]{paramJson.optString("orderCode"),paramJson.optString("shopId")});
				if(orderdeliveryRowSet.next()){
					orderdeliveryId = orderdeliveryRowSet.getInt("id");
				}
				if(orderdeliveryId<=0){
					String deliverysql = "insert into cc_deliver_list(order_code,type,shop_id,channel,remark,receivername,receiveraddress,actual_price,receiverphone,receiverlat,receiverlng,tips,create_time) values (?,?,?,?,?,?,?,?,?,?,?,?,?)";
					dao.update(deliverysql, new Object[] {paramJson.optString("orderCode"),paramJson.optString("type"),paramJson.optString("shopId"),paramJson.optString("channel"),paramJson.optString("remark"),paramJson.optString("receiverName"),paramJson.optString("receiverAddress"),paramJson.optDouble("cargoPrice"),paramJson.optString("receiverPhone"),paramJson.optString("receiverLat"),paramJson.optString("receiverLng"),tips,time});
				    
				    result.setCode(0);
					result.setMsg(Constant.ADD_ORDER_DELIVERY_SUCCESS);	
				}else{
					result.setCode(2);
					result.setMsg("此订单已存在");
				}
			}else{
				result.setCode(1);
				result.setMsg("订单参数为空");
			}
			
		} catch (Exception e) {
			e.printStackTrace();
			result.setCode(1);
			result.setMsg(Constant.ADD_ORDER_DELIVERY_FAILURE);
		}
		
	}

	@Override
	public void findExceptionDeliveryOrder(Data param, Data result) {
        try {
        	JSONObject resultJo = new JSONObject();
        	List<JSONObject> resultListJo = new ArrayList<JSONObject>();
        	JSONObject obj = JSONObject.fromObject(param.getData().get(0));
        	String tenentid = param.getTenancy_id();
        	int storeId = param.getStore_id();
        	StringBuilder sql =  new StringBuilder();//COALESCE(hdt.order_num,'0')
        	sql.append("SELECT cd.id,cd.order_code,cd.actual_price,cd.type,sd.class_item as type_name,cd.shop_id,cd.remark,cd.channel,sdy.class_item as channel_name,cd.receivername,cd.receiverlat,cd.receiverlng,cd.receiveraddress,cd.receiverphone,COALESCE (cd.tips, '0') as tips,cd.order_state,COALESCE (cd.order_state_reason, '') as order_state_reason from cc_deliver_list cd join sys_dictionary sd on cd.type = sd.class_item_code join sys_dictionary sdy on cd.channel = sdy.class_item_code where cd.shop_id = '"+storeId+"' and cd.order_state = '0'");
        	
        	int pagenum = obj.containsKey("page") ? (obj.getInt("page") == 0 ? 1 : obj.getInt("page")) : 1;
//    		Set<String> keys = obj.keySet();
//    		for(String s:keys)
//    		{
//    			if("order_code".equals(s)){
//    				sql.append("and order_code like '%"+obj.get(s)+"%'");
//    			}
//    			
//    		}
    		
    		long total = this.dao.countSql(tenentid, sql.toString());
    		List<JSONObject> list = this.dao.query4Json(tenentid, this.dao.buildPageSql(obj,sql.toString()));
    		resultJo.put("page", pagenum);
    		resultJo.put("total", total);
    		resultJo.put("rows", list);
    		
    		resultListJo.add(resultJo);
    		
    		result.setData(resultListJo);
    		result.setMsg(Constant.FIND_EXCEPTION_DELIVERY_ORDER_SUCCESS);
    		
        } catch (Exception e) {
			e.printStackTrace();
    		result.setMsg(Constant.FIND_EXCEPTION_DELIVERY_ORDER_FAILURE);
		}		
	}

	@Override
	public List<JSONObject> findWmOrderException(String tenancyId, int storeId) {
		Timestamp nowTime = DateUtil.getNowTimestamp();
		List<JSONObject> orderExcptionListJo = new ArrayList<JSONObject>();
		try {
			String sql = "select tenancy_id,store_id,chanel,order_code,meituan_id,third_order_code,receive_time,order_state,find_num,find_state,find_updatetime,delivery_state from cc_order_list where tenancy_id = '"+tenancyId+"' and store_id = "+storeId+"  and (find_state = 0 or find_state is null) and order_state in ('10','12') ";
			
			List<JSONObject> wmOrderListJo = this.dao.query4Json(tenancyId, sql);
			for(JSONObject jo:wmOrderListJo){
				String tanancyId = jo.optString("tenancy_id");
				int shopId = jo.optInt("store_id");
				String channel = jo.optString("chanel");
				String orderCode = jo.optString("order_code");
				String third_order_id = jo.optString("meituan_id");
				String third_order_code = jo.optString("third_order_code");
				String orderState = jo.optString("order_state");
				String singTimeString = jo.optString("receive_time");
				if(singTimeString!=null && !"null".equals(singTimeString)){
					Timestamp singTime = Timestamp.valueOf(singTimeString);
					int find_num = jo.optInt("find_num");
					int find_state = jo.optInt("find_state");
					String delivery_state = jo.optString("delivery_state");
					String find_updatetimeStirng = jo.optString("receive_time");
					Timestamp find_updatetime  = null;
					if(find_updatetimeStirng!=null && !"null".equals(find_updatetimeStirng)){
						find_updatetime= Timestamp.valueOf(find_updatetimeStirng);
					}
					long minutes = (nowTime.getTime() - singTime.getTime())/(1000*60);
					if(minutes>=16){
						JSONObject json = new JSONObject();
						json.put("tanancyId", tanancyId);
						json.put("shopId", String.valueOf(shopId));
						json.put("channel", channel);
						json.put("orderCode", orderCode);
						json.put("third_order_id", third_order_id);
						json.put("third_order_code", third_order_code);
						json.put("order_state", orderState);
						json.put("find_num", find_num);
						json.put("find_state", find_state);
						json.put("find_updatetime", find_updatetime);
						json.put("delivery_state", delivery_state);
						orderExcptionListJo.add(json);
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return orderExcptionListJo;
	}

	@Override
	public void findExceptionOrderState(String tenancyId, JSONObject jo) {
		this.findsaasExcepetionOrder(jo);
//		Timestamp time = DateUtil.getNowTimestamp();
//		String findResult = this.findsaasExcepetionOrder(jo);
//		try
//		{
//			if(!StringUtils.isEmpty(findResult)){
//			//返回 {error:0,orderCode:订单号,isAllow:0｜1（0不允许，1允许）,deliver_state:配送状态}
//			JSONObject resultJo = JSONObject.fromObject(findResult);
//		    if (resultJo.containsKey("error"))
//			{
//				int error = resultJo.optInt("error");
//				int isAllow = resultJo.optInt("isAllow");
//				String orderCode = resultJo.optString("orderCode");
//				String deliver_state = resultJo.optString("deliver_state");
//				if(error==0){
//					if(isAllow==1){
//						
//						
//						// 发送消息
//	                    List<String> msgList = new ArrayList<String>();
//	                    msgList.add(orderCode);
//	                    JSONObject msg = new JSONObject();
//	                    msg.put("channel", jo.optString("channel"));
//	                    msg.put("order_code", msgList);
//	                    // msg.put("chanel", orderJson.optString("chanel"));
//	                    // msg.put("is_online_payment",
//	                    // orderJson.optString("is_online_payment"));
//	                    // Comet4jUtil.sendMessage2All(Comet4jUtil.CALLCENTER_ORDER,
//	                    // msg.toString());
//
//	                    // 响铃提醒
//	                    List<JSONObject> cometList = new ArrayList<JSONObject>();
//	                    cometList.add(msg);
//	                    Data cometData = new Data();
//	                    cometData.setTenancy_id(tenancyId);
//	                    cometData.setStore_id(Integer.valueOf(jo.optInt("shopId")));
//	                    cometData.setType(Type.DELIVERY);
//	                    cometData.setOper(Oper.add);
//	                    cometData.setSuccess(true);
//	                    cometData.setData(cometList);
//					    /*Comet4jUtil.sendMessage2All(Comet4jUtil.POS_PC, JSONObject
//							.fromObject(cometData).toString());*/
//
//	                    //POS用到UDP
//	                    String port = new CometDataNoticeClientRunnable(cometData).getPort();
//	                    SendUdpToPos.sendMessagePos(JSONObject.fromObject(cometData).toString(), port);
//	                    logger.debug("----推送消息：" + JSONObject.fromObject(cometData).toString() + ", 端口号：" + port);
//	                    
//					}else{
//						logger.info("---订单号"+orderCode+"不允许配送,订单状态为"+deliver_state);
//						this.updateExceptionOrder(jo.optString("tanancyId"), Integer.valueOf(jo.optString("shopId")),jo.optString("orderCode"), 1, 1, time, "01");
//					}	
//				}
//			}else{
//				logger.info("外卖异常订单查询是否允许配送返回值为空");
//			}
//		}else{
//			logger.info("外卖异常订单查询是否允许配送返回值为空");
//		}
//		}
//		catch (Exception se)
//		{
//			logger.info("外卖异常订单查询是否允许配送错误，连接超时，请检查网络,请求体为：" + jo);
//			se.printStackTrace();
//			
//		}

	}
	
	/**
	 * 从外卖总部查询异常订单
	 * @param jo
	 * @return
	 */
	public void findsaasExcepetionOrder(JSONObject jo){
		String url = getPequestUrl(FIND_EXCEPTION_ORDER_DELIVERY);
		Timestamp nowTime = DateUtil.getNowTimestamp();
		DBContextHolder.setTenancyid(jo.optString("tanancyId"));
		long t = System.currentTimeMillis();
		String findResult = "";
		Timestamp find_updatetime = null;
		JSONObject findJo = this.findExceptionOrder(jo.optString("tanancyId"), Integer.valueOf(jo.optString("shopId")),
				jo.optString("orderCode"));
		int find_state = findJo.optInt("find_state");
		int find_num = findJo.optInt("find_num");
		// String delivery_state = findJo.optString("delivery_state");
		String updatetime = findJo.optString("find_updatetime");
		if (updatetime == null || "".equals(updatetime) || "null".equals(updatetime)) {
			find_updatetime = nowTime;
		} else {
			find_updatetime = Timestamp.valueOf(updatetime);
		}

		jo.remove("find_num");
		jo.remove("find_state");
		jo.remove("find_updatetime");
		jo.remove("delivery_state");
		try {
			if (find_state == 0 || "".equals(String.valueOf(find_state)) || "null".equals(String.valueOf(find_state))) {
				if ("".equals(String.valueOf(find_num)) || "null".equals(String.valueOf(find_num)) || find_num <= 1) {
					logger.debug("异常订单第" + 1 + "次查询");
					logger.debug(String.valueOf(t) + "<外卖异常订单查询是否允许配送请求体==>" + jo);
					findResult = HttpUtil.sendPostRequest(url, jo.toString(), 3000, 5000);
					logger.debug(String.valueOf(t) + "<外卖异常订单查询是否允许配送返回体==>" + JSONObject.fromObject(findResult));
				} else {
					if (find_num <= 6) {
						if ((nowTime.getTime() - find_updatetime.getTime()) / (1000 * 60) >= 1) {
							logger.debug("异常订单第" + (find_num + 1) + "次查询");
							logger.debug(String.valueOf(t) + "<外卖异常订单查询是否允许配送请求体==>" + jo);
							findResult = HttpUtil.sendPostRequest(url, jo.toString(), 3000, 5000);
							logger.debug(String.valueOf(t) + "<外卖异常订单查询是否允许配送返回体==>" + JSONObject.fromObject(findResult));
						}
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			logger.debug("异常订单查询错误,异常订单第" + find_num + "次查询");
			logger.debug(String.valueOf(t) + "<外卖异常订单查询是否允许配送请求体==>" + jo);
			findResult = HttpUtil.sendPostRequest(url, jo.toString(), 3000, 5000);
			logger.debug(String.valueOf(t) + "<外卖异常订单查询是否允许配送返回体==>" + JSONObject.fromObject(findResult));
		} finally {
			Timestamp deliverytime = DateUtil.getNowTimestamp();
			if (!StringUtils.isEmpty(findResult)) {
				JSONObject reJo = JSONObject.fromObject(findResult);
				if (reJo.containsKey("error")) {
					if (reJo.optInt("error") == 0) {
						// int error = reJo.optInt("error");
						int isAllow = reJo.optInt("isAllow");
						String orderCode = reJo.optString("orderCode");
						String deliver_state = reJo.optString("deliver_state");
						if(isAllow==1){
							// 发送消息
		                    List<String> msgList = new ArrayList<String>();
		                    msgList.add(orderCode);
		                    JSONObject msg = new JSONObject();
		                    msg.put("channel", jo.optString("channel"));
		                    msg.put("order_code", msgList);
							msg.put("order_state",jo.optString("order_state"));
							msg.put("delivery_state","07");
		                    Comet4jUtil.sendMessage2All(Comet4jUtil.CALLCENTER_ORDER,
		                            msg.toString());

		                    // 响铃提醒
		                    List<JSONObject> cometList = new ArrayList<JSONObject>();
		                    cometList.add(msg);
		                    Data cometData = new Data();
		                    cometData.setTenancy_id(jo.optString("tanancyId"));
		                    cometData.setStore_id(Integer.valueOf(jo.optInt("shopId")));
		                    cometData.setType(Type.DELIVERY);
		                    cometData.setOper(Oper.add);
		                    cometData.setSuccess(true);
		                    cometData.setData(cometList);
						    /*Comet4jUtil.sendMessage2All(Comet4jUtil.POS_PC, JSONObject
								.fromObject(cometData).toString());*/

		                    //POS用到UDP
		                    String port = new CometDataNoticeClientRunnable(cometData).getPort();
		                    SendUdpToPos.sendMessagePos(JSONObject.fromObject(cometData).toString(), port);
		                    logger.debug("----推送消息：" + JSONObject.fromObject(cometData).toString() + ", 端口号：" + port);

							this.updateExceptionOrder(jo.optString("tanancyId"),
									Integer.valueOf(jo.optString("shopId")), jo.optString("orderCode"), (find_num + 1),
									1, deliverytime, "07");
						} else {
							logger.info("---订单号" + orderCode + "不允许配送,订单状态为" + deliver_state);
							this.updateExceptionOrder(jo.optString("tanancyId"),
									Integer.valueOf(jo.optString("shopId")), jo.optString("orderCode"), (find_num + 1),
									1, deliverytime, deliver_state);
						}
					}  else {
						this.updateExceptionOrder(jo.optString("tanancyId"), Integer.valueOf(jo.optString("shopId")),
								jo.optString("orderCode"), (find_num + 1), 0, deliverytime, "0");
					}
				}
			} else {
				if (find_num > 6) {
					this.updateExceptionOrder(jo.optString("tanancyId"), Integer.valueOf(jo.optString("shopId")),
							jo.optString("orderCode"), find_num, 1, find_updatetime, "0");
				} else {
					this.updateExceptionOrder(jo.optString("tanancyId"), Integer.valueOf(jo.optString("shopId")),
							jo.optString("orderCode"), (find_num + 1), 0, deliverytime, "0");
				}
			}
		}
		//return findResult;
	}

	public JSONObject findExceptionOrder(String tenancyId,int storeid,String ordreCode){
		DBContextHolder.setTenancyid(tenancyId);
		JSONObject orderJo = new JSONObject();
		try {
			String sql = "select order_code,find_num,find_state,find_updatetime,delivery_state from cc_order_list where tenancy_id = '"+tenancyId+"' and store_id = "+storeid+" and order_code = '"+ordreCode+"'";
			List<JSONObject> orderListJo = this.dao.query4Json(tenancyId, sql);
			if(orderListJo!=null&&orderListJo.size()>0){
				orderJo = orderListJo.get(0);
			}
			return orderJo;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
	
	
	public void updateExceptionOrder(String tenancyId,int storeid,String ordreCode,int findNum,int findState,Timestamp time , String deliveryState ){
		DBContextHolder.setTenancyid(tenancyId);
		try {
			String sql = "update cc_order_list set find_num = "+findNum+",find_state = "+findState+",find_updatetime = '"+time+"',delivery_state = '"+deliveryState+"' where  tenancy_id = '"+tenancyId+"' and store_id = "+storeid+" and order_code = '"+ordreCode+"' ";
		    dao.execute(tenancyId, sql);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@Override
	public JSONObject addTips(String tenancyId, JSONObject obj) {
		String url = getPequestUrl(ADD_ORDER_addTips);
		// String url =
		// "http://localhost:8091/tzxsaas/deliverRest/createDeliverOrder";
		JSONObject resultJo = new JSONObject();
		try {
			long t = System.currentTimeMillis();
			logger.debug(String.valueOf(t) + "<发送接口请求体==>" + obj.toString());
			String findResult = HttpUtil.sendPostRequest(url, obj.toString(), 3000, 5000);
			logger.debug(String.valueOf(t) + "<发送接口返回体==>" + JSONObject.fromObject(findResult).toString());
			if (StringUtils.isEmpty(findResult)) {
				resultJo.put("error", 1);
				resultJo.put("msg", "配送订单添加小费返回信息为空");
			} else {
				try {
					JSONObject saasresultJo = JSONObject.fromObject(findResult);
					if (saasresultJo.containsKey("error")) {
						int error = saasresultJo.optInt("error");
						String msg = saasresultJo.optString("msg");
						logger.debug("发送添加小费返回信息:error:" + error + "-----" + "msg:" + msg);
						resultJo.put("error", error);
						resultJo.put("msg", msg);
					} else {
						resultJo.put("error", 1);
						resultJo.put("msg", saasresultJo.optString("msg"));
						logger.debug("配送订单添加小费返回信息:"+saasresultJo.toString());
					}
				} catch (Exception se) {
					logger.debug("配送订单添加小费错误：" + ExceptionMessage.getExceptionMessage(se));
					se.printStackTrace();
					resultJo.put("error", 1);
					resultJo.put("msg", "配送订单添加小费错误");
					;
				}
			}
		} catch (Exception se) {
			logger.debug("配送订单添加小费错误，连接超时，请检查网络,请求体为：" + obj);
			se.printStackTrace();
			resultJo.put("error", 1);
			resultJo.put("msg", "配送订单添加小费错误");
		}
		return resultJo;
	}

	@Override
	public JSONObject distributionChannel(String tenancyId, JSONObject obj) {
		// TODO Auto-generated method stub
		DBContextHolder.setTenancyid(tenancyId);
        JSONObject jo = new JSONObject();
        List<JSONObject> resultListJo = new ArrayList<JSONObject>();
		try {
			String sql = "select id,class_item,class_item_code,class_identifier,class_identifier_code from  sys_dictionary where class_identifier_code = 'deliver_type' and is_sys = 'Y' and valid_state = '1' and tenancy_id = '"+tenancyId+"'";
			resultListJo = dao.query4Json(tenancyId, sql);
			jo.put("error", 0);
			jo.put("msg", "配送渠道查询成功");
			jo.put("data", resultListJo);
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
			jo.put("error", 1);
			jo.put("msg", "配送渠道查询错误");
			jo.put("data", resultListJo);
		}
		return jo;
	}

	@Override
	public void queryOrderDelivery(Data param, Data result) {
		String url = getPequestUrl(QUERY_ORDER_DELIVERY); 
		//String url = "http://localhost:8091/tzxsaas/deliverRest/createDeliverOrder";
		try{
			long t = System.currentTimeMillis();
			JSONObject paramJson = JSONObject.fromObject(param.getData().get(0));
			logger.debug(String.valueOf(t) + "<发送接口请求体==>" + paramJson.toString());
			String findResult = HttpUtil.sendPostRequest(url, paramJson.toString(), 3000, 5000);
			logger.debug(String.valueOf(t) + "<发送接口返回体==>" + JSONObject.fromObject(findResult).toString());
			if (StringUtils.isEmpty(findResult)) {
				result.setCode(Constant.CODE_CONN_EXCEPTION);
				result.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
				result.setSuccess(false);
			} else {
				try {
					List<JSONObject> listJson = new ArrayList<JSONObject>();
					JSONObject resultJo = JSONObject.fromObject(findResult);
					if (resultJo.containsKey("rows")) {
						JSONArray ja = resultJo.getJSONArray("rows");
						if (ja == null || ja.size() == 0) {
							StringBuilder sbSql = new StringBuilder();
							sbSql.append(
									"SELECT null as create_time,null as accept_time,null as reach_time,null as fetch_time,null as finish_time,null as cancel_time,null as exception_time,null as deliver_name,null as deliver_phone,");
							sbSql.append(
									"cd.tenancy_id AS tenancy_id,cd.store_id AS shop_id,cd. ID,cd.order_code AS order_code,cd.actual_pay AS actual_price,cd.order_name AS receivername,cd.order_phone AS receiverphone,cd.address AS receiveraddress,cd.longitude AS receiverlng,cd.latitude AS receiverlat,cd.order_state as order_state,cd.delivery_party,COALESCE(cd.delivery_state,'0') as delivery_state");
							sbSql.append(" FROM cc_order_list cd where cd.order_code = '"
									+ paramJson.optString("orderCode") + "' and cd.tenancy_id = '"
									+ paramJson.optString("tanancyId") + "' and cd.store_id = "
									+ Integer.valueOf(paramJson.optString("shopId")) + "");
							List<JSONObject> orderlistJo = dao.query4Json(paramJson.optString("tanancyId"),
									sbSql.toString());
							if (orderlistJo != null && orderlistJo.size() > 0) {
								JSONObject resultOrderJo = orderlistJo.get(0);
								if (resultOrderJo.optString("delivery_state").equals("07")) {
									resultOrderJo.put("state", "07");
									resultOrderJo.put("state_desc", "平台配送异常");
								} else {
									resultOrderJo.put("state", "0");// 未超时、未允许配送的
									resultOrderJo.put("state_desc", "未接单");
								}
								List<JSONObject> listJsonOrder = new ArrayList<JSONObject>();
								listJsonOrder.add(resultOrderJo);
								resultJo.put("page", 1);
								resultJo.put("total", 1);
								resultJo.put("rows", listJsonOrder);
							}
						} else {
							if (ja.size() > 1) {
								JSONArray wmptRow = new JSONArray();
								JSONArray rows = new JSONArray();
								for (int i = 0; i < ja.size(); i++) {
									JSONObject object = (JSONObject) ja.get(i);
									String type = object.optString("type");
									if (!type.equals("wmpt")) {
										rows.add(object);
									} else {
										wmptRow.add(object);
									}
								}
								if (!wmptRow.isEmpty()) {
									for (int i = 0; i < wmptRow.size(); i++) {
										rows.add(wmptRow.get(i));
									}
								}
								resultJo.put("rows", rows);
							}
							logger.info(resultJo);
						}
					}
					listJson.add(resultJo);
					result.setData(listJson);
				} catch (Exception se) {
					logger.info("查询配送订单添加本地错误：" + ExceptionMessage.getExceptionMessage(se));
					se.printStackTrace();
				}
			}
		}
		catch (Exception se)
		{
			logger.info("查询订单错误，连接超时，请检查网络,请求体为：" + param);
			result.setCode(5);
			result.setMsg("无法查询订单");
			result.setSuccess(false);
			se.printStackTrace();
		}
	}
}
