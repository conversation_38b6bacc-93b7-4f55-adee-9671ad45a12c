package com.tzx.pos.po.springjdbc.dao;

import com.tzx.framework.common.entity.Data;
import com.tzx.pos.base.dao.BaseDao;
import com.tzx.pos.bo.dto.PosBill;
import com.tzx.pos.bo.dto.PosBillItem;

import net.sf.json.JSONObject;

import java.util.Date;
import java.util.List;

/**
 * Created by kevin on 2017-11-06.
 */
public interface ComboSetMealDao extends BaseDao
{

	String	NAME	= "com.tzx.pos.po.springjdbc.dao.imp.ComboSetMealDaoImp";

	/**
	 * 查询组合套餐主项菜品
	 * 
	 * @param rwid
	 * @throws Exception
	 */
	public PosBillItem getPosBillItemByRWID(Integer rwid) throws Exception;

	/**
	 * @param tenancyId
	 * @param stroeId
	 * @param billNum
	 * @param rwid
	 * @return
	 * @throws Exception
	 */
	public PosBillItem getPosBillItemByRWID(String tenancyId, Integer stroeId, String billNum, Integer rwid) throws Exception;

	/**
	 * 查询组合套餐明细集合
	 * 
	 * @param rwid
	 * @return
	 * @throws Exception
	 */
	public List<PosBillItem> getPosBillItemListByRWIDS(String rwid) throws Exception;

	/**
	 * @param tenancyId
	 * @param stroeId
	 * @param billNum
	 * @param rwid
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getPosBillItemListByRWIDS(String tenancyId, Integer stroeId, String billNum, String rwid) throws Exception;

	/**
	 * 确认组合套餐，更新pos_bill_item
	 * 
	 * @param setmealItemList
	 * @throws Exception
	 */
	public void updatePosBillItem4Setmeal(List<PosBillItem> setmealItemList) throws Exception;

	/**
	 * @param tenancyId
	 * @param stroeId
	 * @param billNum
	 * @param setmealItemList
	 * @throws Exception
	 */
	public void updatePosBillItem4Setmeal(String tenancyId, Integer stroeId, String billNum,List<PosBillItem> setmealItemList) throws Exception;
	
	/**
	 * 组合套餐主项做法金额
	 * 
	 * @param tenancyId
	 * @param stroeId
	 * @param billNum
	 * @return
	 * @throws Exception
	 */
	public double getMethodAmountByRWID(String tenancyId, Integer stroeId, String billNum) throws Exception;

	/**
	 * 更新pos_bill表
	 * 
	 * @param posBillItem
	 * @throws Exception
	 */
	public void updatePosBill(PosBillItem posBillItem) throws Exception;

	/**
	 * 还原修改过的pos_bill_item表的记录
	 * 
	 * @param rwid
	 * @param comboSetId
	 * @throws Exception
	 */
	public void revertPosBillItemByRWIDS(String rwid, Integer comboSetId) throws Exception;

	/**
	 * 需要添加到组合套餐的菜品信息
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param rwid
	 * @return
	 * @throws Exception
	 */
	public PosBillItem getPosBillItemByRWID(String tenancyId, int storeId, int rwid) throws Exception;

	/**
	 *
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getPosBillItemByBillNum(String tenancyId, int storeId, String billNum) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getPosBillItemByBillnum(String tenancyId, int storeId, String billNum) throws Exception;

	/**
	 *
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @return
	 * @throws Exception
	 */
	public double getBillDiscountrByBillNum(String tenancyId, Integer storeId, String billNum) throws Exception;

	/**
	 *
	 * @param tenancyId
	 * @param itemUnitId
	 * @param itemId
	 * @return
	 * @throws Exception
	 */
	public JSONObject getHqItemUnit(String tenancyId, Integer itemUnitId, Integer itemId) throws Exception;

	/**
	 *
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getAllPosBillItemByBillNum(String tenancyId, int storeId, String billNum) throws Exception;

	/**
	 *
	 * @param tenancyId
	 * @param storeId
	 * @param rwid
	 * @throws Exception
	 */
	public void deletePosKwzfItemByRWID(String tenancyId, int storeId, int rwid) throws Exception;

	/**
	 *
	 * @param billNum
	 * @param storeId
	 * @param tenancyId
	 * @return
	 * @throws Exception
	 */
	public int selectMaxItemSerial(String billNum, int storeId, String tenancyId) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param itemId
	 * @param itemSerial
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getPosBillItemComboByItemList(String tenancyId, int storeId, List<Integer> rwidList) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param stroeId
	 * @param billNum
	 * @param setmealItemList
	 * @throws Exception
	 */
	public void updatePosBillItemForSingle(String tenancyId, Integer stroeId, String billNum,List<PosBillItem> setmealItemList) throws Exception;


}
