package com.tzx.pos.bo.dto;

import java.io.Serializable;

/**
 * TODO
 * <AUTHOR> 
 * @日期：2015年5月18日-下午5:26:21
 */

public class BasicDataServiceFeeType implements Serializable
{
	/**
	 * 
	 */
	private static final long	serialVersionUID	= 143085931280616770L;
	
	private String id;
	private String service_type_code;
	private String name;
	private String guding_jj;
	private String zuidi_xfe;
	private String fwfl;
	private String taken_mode;
	private String ismodify;
	private String fee_type;
	private String valid_state;
	
	public String getId()
	{
		return id;
	}
	public void setId(String id)
	{
		this.id = id;
	}
	public String getService_type_code()
	{
		return service_type_code;
	}
	public void setService_type_code(String service_type_code)
	{
		this.service_type_code = service_type_code;
	}
	public String getName()
	{
		return name;
	}
	public void setName(String name)
	{
		this.name = name;
	}
	public String getGuding_jj()
	{
		return guding_jj;
	}
	public void setGuding_jj(String guding_jj)
	{
		this.guding_jj = guding_jj;
	}
	public String getZuidi_xfe()
	{
		return zuidi_xfe;
	}
	public void setZuidi_xfe(String zuidi_xfe)
	{
		this.zuidi_xfe = zuidi_xfe;
	}
	public String getFwfl()
	{
		return fwfl;
	}
	public void setFwfl(String fwfl)
	{
		this.fwfl = fwfl;
	}
	public String getTaken_mode()
	{
		return taken_mode;
	}
	public void setTaken_mode(String taken_mode)
	{
		this.taken_mode = taken_mode;
	}
	public String getIsmodify()
	{
		return ismodify;
	}
	public void setIsmodify(String ismodify)
	{
		this.ismodify = ismodify;
	}
	public String getFee_type()
	{
		return fee_type;
	}
	public void setFee_type(String fee_type)
	{
		this.fee_type = fee_type;
	}
	public String getValid_state()
	{
		return valid_state;
	}
	public void setValid_state(String valid_state)
	{
		this.valid_state = valid_state;
	}
}
