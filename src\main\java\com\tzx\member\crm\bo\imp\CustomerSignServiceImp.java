package com.tzx.member.crm.bo.imp;

import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.pos.base.Constant;
import com.tzx.member.crm.bo.CustomerSignService;
import net.sf.json.JSONObject;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description 会员标签业务处理
 * <AUTHOR>
 * @Date 2018-09-25 10:21
 */
@Service(CustomerSignService.NAME)
public class CustomerSignServiceImp extends CustomerServiceImp implements CustomerSignService{

    private static final Logger logger = Logger.getLogger(CustomerSignServiceImp.class);

    @Override
    public Data addCustomerSign(String tenancyId, Data param) throws Exception {
        Data resultData = new Data();
        Data resData = Data.get(tenancyId, param.getStore_id(), Constant.CODE_SUCCESS);
        resData.setType(Type.CUSTOMER_SIGN);
        resData.setOper(Oper.add);
        resData.setTenancy_id(tenancyId);
        resData.setStore_id(param.getStore_id());
        resData.setMsg("添加会员标签");
        resData.setT(System.currentTimeMillis());

        List<Map<String, String>> list = (List<Map<String, String>>) param.getData();
        resData.setData(list);

        //调用crm的接口
        this.commonPost(JSONObject.fromObject(resData).toString(), resultData);
        return resultData;
    }

    @Override
    public Data getCustomerSign(String tenancyId, Data param) throws Exception {
        //返回前端的数据
        Data resultData = new Data();
        Map<String,String> map = (Map<String,String>) param.getData().get(0);
        int storeId = param.getStore_id();
        Data resData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
        resData.setType(Type.CUSTOMER_SIGN);
        resData.setOper(Oper.find);
        resData.setT(System.currentTimeMillis());
        resData.setMsg("会员标签查询");
        List<Map<String, String>> list = new ArrayList<Map<String, String>>();
        Map<String, String> paramMap = new HashMap<String, String>();
        paramMap.put("mobil", map.get("mobil"));
        list.add(paramMap);
        resData.setData(list);

        //调用crm的接口
        this.commonPost(JSONObject.fromObject(resData).toString(), resultData);
        return resultData;
    }

    @Override
    public Data praiseSign(String tenancyId, Data param) throws Exception {
        Data resultData = new Data();
        Data resData = Data.get(tenancyId, param.getStore_id(), Constant.CODE_SUCCESS);
        resData.setType(Type.CUSTOMER_SIGN);
        resData.setOper(Oper.praise);
        resData.setTenancy_id(tenancyId);
        resData.setStore_id(param.getStore_id());
        resData.setMsg("会员标签点赞");
        resData.setT(System.currentTimeMillis());

        List<Map<String, String>> list = (List<Map<String, String>>) param.getData();
        resData.setData(list);

        //调用crm的接口
        this.commonPost(JSONObject.fromObject(resData).toString(), resultData);
        return resultData;
    }
}
