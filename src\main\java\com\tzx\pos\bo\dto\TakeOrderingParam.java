package com.tzx.pos.bo.dto;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Map;

import net.sf.json.JSONObject;

public class TakeOrderingParam {
	private String tenantId;
	private Integer organId;
	private String chanel = null;
	private String billCode;
	private String optNum;
	private String posNum;
	private String mode;
	private String tableCode;
	private List<Map<String, Object>> items;
	private List<Map<String, Object>> paymentList;
	private String saleMode;
	private Date reportDate;
	private String billNum;
	private String serialNum;
	private Integer serviceId;
    private Integer shiftId;
    private String billProperty = null;
    private JSONObject printJson= null;
    private Double serviceAmount;
    private Double paymentAmount;
    private Timestamp createTime;
    
    private Double discountrAmount;
    private Double discountkAmount;
    private JSONObject paymentPrintJson;
    private List<JSONObject> discountList;
    private Map<String,JSONObject> discountMap;
	public String getChanel() {
		return chanel;
	}

	public void setChanel(String chanel) {
		this.chanel = chanel;
	}

	public String getBillCode() {
		return billCode;
	}

	public void setBillCode(String billCode) {
		this.billCode = billCode;
	}

	public String getOptNum() {
		return optNum;
	}

	public void setOptNum(String optNum) {
		this.optNum = optNum;
	}

	public String getPosNum() {
		return posNum;
	}

	public void setPosNum(String posNum) {
		this.posNum = posNum;
	}

	public String getMode() {
		return mode;
	}

	public void setMode(String mode) {
		this.mode = mode;
	}

	public String getTableCode() {
		return tableCode;
	}

	public void setTableCode(String tableCode) {
		this.tableCode = tableCode;
	}

	public List<Map<String, Object>> getItems() {
		return items;
	}

	public void setItems(List<Map<String, Object>> items) {
		this.items = items;
	}

	public List<Map<String, Object>> getPaymentList() {
		return paymentList;
	}

	public void setPaymentList(List<Map<String, Object>> paymentList) {
		this.paymentList = paymentList;
	}

	public String getSaleMode() {
		return saleMode;
	}

	public void setSaleMode(String saleMode) {
		this.saleMode = saleMode;
	}

	public Date getReportDate() {
		return reportDate;
	}

	public void setReportDate(Date reportDate) {
		this.reportDate = reportDate;
	}

	public String getBillNum() {
		return billNum;
	}

	public void setBillNum(String billNum) {
		this.billNum = billNum;
	}

	public String getSerialNum() {
		return serialNum;
	}

	public void setSerialNum(String serialNum) {
		this.serialNum = serialNum;
	}

	public Integer getServiceId() {
		return serviceId;
	}

	public void setServiceId(Integer serviceId) {
		this.serviceId = serviceId;
	}

	public String getTenantId() {
		return tenantId;
	}

	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}

	public Integer getOrganId() {
		return organId;
	}

	public void setOrganId(Integer organId) {
		this.organId = organId;
	}

	public Integer getShiftId() {
		return shiftId;
	}

	public void setShiftId(Integer shiftId) {
		this.shiftId = shiftId;
	}

	public String getBillProperty() {
		return billProperty;
	}

	public void setBillProperty(String billProperty) {
		this.billProperty = billProperty;
	}
    
	public JSONObject getPrintJson() {
		return printJson;
	}

	public void setPrintJson(JSONObject printJson) {
		this.printJson = printJson;
	}
    
	public Double getServiceAmount() {
		return serviceAmount;
	}

	public void setServiceAmount(Double serviceAmount) {
		this.serviceAmount = serviceAmount;
	}
    
	public Double getPaymentAmount() {
		return paymentAmount;
	}

	public void setPaymentAmount(Double paymentAmount) {
		this.paymentAmount = paymentAmount;
	}
    
	public Double getDiscountrAmount() {
		return discountrAmount;
	}

	public void setDiscountrAmount(Double discountrAmount) {
		this.discountrAmount = discountrAmount;
	}

	@Override
	public String toString() {
		return "TakeOrderingParam [tenantId=" + tenantId + ", organId="
				+ organId + ", chanel=" + chanel + ", billCode=" + billCode
				+ ", optNum=" + optNum + ", posNum=" + posNum + ", mode="
				+ mode + ", tableCode=" + tableCode + ", items=" + items
				+ ", paymentList=" + paymentList + ", saleMode=" + saleMode
				+ ", reportDate=" + reportDate + ", billNum=" + billNum
				+ ", serialNum=" + serialNum + ", serviceId=" + serviceId
				+ ", shiftId=" + shiftId + ", billProperty=" + billProperty
				+ ", printJson=" + printJson + ", serviceAmount="
				+ serviceAmount + ", paymentAmount=" + paymentAmount + "]";
	}

	public JSONObject getPaymentPrintJson() {
		return paymentPrintJson;
	}

	public void setPaymentPrintJson(JSONObject paymentPrintJson) {
		this.paymentPrintJson = paymentPrintJson;
	}

	public Timestamp getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Timestamp createTime) {
		this.createTime = createTime;
	}

	public Double getDiscountkAmount() {
		return discountkAmount;
	}

	public void setDiscountkAmount(Double discountkAmount) {
		this.discountkAmount = discountkAmount;
	}

	public List<JSONObject> getDiscountList() {
		return discountList;
	}

	public void setDiscountList(List<JSONObject> discountList) {
		this.discountList = discountList;
	}

	public Map<String, JSONObject> getDiscountMap() {
		return discountMap;
	}

	public void setDiscountMap(Map<String, JSONObject> discountMap) {
		this.discountMap = discountMap;
	}
	
}
