package com.tzx.framework.common.util;

import java.io.Serializable;
import java.util.List;
import java.util.Map;


public class Page implements Serializable {
	
	

	/**
	 * 
	 */
	private static final long	serialVersionUID	= 1L;

	private int numPerPage = 500;

	private int totalRows;

	private int totalPages;

	private int currentPage = 1;

	private int startIndex;


	private List<Map<String, Object>> resultListMap;

	public int getNumPerPage()
	{
		return numPerPage;
	}


	public void setNumPerPage(int numPerPage)
	{
		this.numPerPage = numPerPage;
	}


	public int getTotalRows()
	{
		return totalRows;
	}


	public void setTotalRows(int totalRows)
	{
		this.totalRows = totalRows;
	}


	public int getTotalPages()
	{
		return totalPages;
	}


	public void setTotalPages(int totalPages)
	{
		this.totalPages = totalPages;
	}
	
	public void setTotalPages() {
		if (totalRows % numPerPage == 0) {
			this.totalPages = totalRows / numPerPage;
		} else {
			this.totalPages = (totalRows / numPerPage) + 1;
		}
	}


	public int getCurrentPage()
	{
		return currentPage;
	}


	public void setCurrentPage(int currentPage)
	{
		this.currentPage = currentPage;
	}


	public int getStartIndex()
	{
		return startIndex;
	}
	
	public void setStartIndex() {
		this.startIndex = (currentPage - 1) * numPerPage;
	}


	public List<Map<String, Object>> getResultListMap()
	{
		return resultListMap;
	}


	public void setResultListMap(List<Map<String, Object>> resultListMap)
	{
		this.resultListMap = resultListMap;
	}
}