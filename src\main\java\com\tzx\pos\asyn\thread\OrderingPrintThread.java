/** 
 * @(#)CodeFormat.java    1.0   2017-06-19
 * 
 * Copyright 北京普照天星科技有限公司. All rights reserved. 
 * PROPRIETARY/CONFIDENTIAL. Use is subject to license terms. 
 */
package com.tzx.pos.asyn.thread;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;

import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.pos.asyn.service.PosPrintNewThreadService;
import com.tzx.pos.asyn.service.PosPrintThreadService;
import com.tzx.pos.base.constant.FunctionCode;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.SpringContextUtils;

import net.sf.json.JSONObject;

/**
 * 下单接口打印功能线程处理类（下单操作）.
 *
 * <AUTHOR> email:<EMAIL>
 * @version 1.0  2017-06-19
 * @see     
 * @since   JDK7.0
 * @update  
 */
public class OrderingPrintThread implements Runnable {
	
	private static final Logger	logger	= Logger.getLogger(OrderingPrintThread.class);
	
 	private JSONObject json = null;                                     // request请求参数
	private Data param = null;                                          // 参数对象
 	private PosPrintThreadService	posPrintThreadService = null;       // pos打印服务
 	private PosPrintNewThreadService posPrintNewThreadService = null;   // pos新打印服务  
	

 	public OrderingPrintThread(JSONObject json,Data param){
 		this.json = json;
 		this.param = param;
 	}

	@Override
	public void run() {
		try
		{
			long t1 = System.currentTimeMillis();
			if (json != null)
			{
				// 获取Spring容器中的bean对象。				
				posPrintNewThreadService = (PosPrintNewThreadService)SpringContextUtils.getBean("com.tzx.pos.asyn.service.PosPrintNewThreadServiceImp");				
				posPrintThreadService = (PosPrintThreadService)SpringContextUtils.getBean("com.tzx.pos.asyn.service.PosPrintThreadServiceImp");
				
				String source = param.getSource();
				if (SysDictionary.SOURCE_MOVE.contains(source)||SysDictionary.SOURCE_ORDER_DEVICE.equals(source))
				{
					posPrintThreadService.getBindOptNum(json.optString("tenancy_id"), json.optInt("store_id"), json);
				}
                json.put("source", source);

				if(posPrintNewThreadService.isNONewPrint(json.optString("tenancy_id"), json.optInt("store_id")))
				{   // 如果启用新的打印模式
					if (json.optString("mode").equalsIgnoreCase("0") && json.optString("is_print").equalsIgnoreCase("Y") )
					{
						if(json.containsKey("rwids") && json.getString("rwids").length() > 0)
						{
							posPrintNewThreadService.posPrintByFunction(json.optString("tenancy_id"), json.optInt("store_id"), FunctionCode.ORDERING, json);
						}
					}
				}
				else
				{
					//打印点菜单
					if(json.containsKey("is_print_order") && "Y".equalsIgnoreCase(json.optString("is_print_order")))
					{
						List<JSONObject> printParaList = new ArrayList<JSONObject>();
						printParaList.add(json);						
						Data printData = Data.get(param);
						printData.setOper(Oper.check);
						printData.setType(Type.PRINT_BILL);
						printData.setData(printParaList);						
						posPrintThreadService.printPosBill(printData, null);
					}
					
					//厨房打印
					if (json.optString("mode").equalsIgnoreCase("0"))
					{
						if (json.optString("is_print").equalsIgnoreCase("Y"))
						{
							List<Integer> list = posPrintThreadService.orderChef(json.optString("tenancy_id"), json.optString("bill_num"), json.optInt("store_id"), "0");
							posPrintThreadService.orderPrint(json.optString("tenancy_id"), json.optString("bill_num"), json.optInt("store_id"), list);
						}
					}
				}
				long t2 = System.currentTimeMillis();
				String runTime = String.valueOf(t2 - t1);
			}
		}catch(Exception e1){
			logger.error(ExceptionMessage.getExceptionMessage(e1));
		}	
	}

}
