package com.tzx.framework.common.newprint;

import javax.imageio.ImageIO;
import javax.print.Doc;
import javax.print.DocFlavor;
import javax.print.DocPrintJob;
import javax.print.PrintService;
import javax.print.PrintServiceLookup;
import javax.print.SimpleDoc;
import javax.print.attribute.DocAttributeSet;
import javax.print.attribute.HashDocAttributeSet;
import javax.print.attribute.HashPrintRequestAttributeSet;
import javax.print.attribute.PrintRequestAttributeSet;
import javax.print.attribute.standard.MediaPrintableArea;
import javax.swing.JTextPane;
import javax.swing.SizeRequirements;
import javax.swing.text.View;
import javax.swing.text.ViewFactory;
import javax.swing.text.html.HTMLEditorKit;
import javax.swing.text.html.InlineView;
import javax.swing.text.html.ParagraphView;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import com.tzx.framework.common.print.PrintHelperNew;
import com.tzx.framework.common.util.DoubleHelper;

import net.sf.json.JSONObject;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.awt.image.RenderedImage;
import java.awt.print.Book;
import java.awt.print.PageFormat;
import java.awt.print.Paper;
import java.awt.print.PrinterJob;
import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.InputStream;
import java.util.Iterator;
import java.util.Map;

import gui.ava.html.image.util.SynchronousHTMLEditorKit;

public class HtmlImageGenerators
{
	public static int			DEFAULT_IMAGE_WIDTH		= 730;
	public static int			DEFAULT_IMAGE_HEIGHT	= 700;
	static final Dimension		DEFAULT_SIZE			= new Dimension(800, 800);
	public static final double	SIZE_PX					= 6.12d;
	public static final int		PAPER_CORRECT_SIZE		= 8;

	@SuppressWarnings("serial")
	public static BufferedImage html2bi(String html) throws Exception
	{
		//HtmlImageGenerator
		JTextPane tp = new JTextPane();

		tp.setSize(DEFAULT_IMAGE_WIDTH, DEFAULT_IMAGE_HEIGHT);
		tp.setBackground(Color.white);
		tp.setSize(DEFAULT_SIZE);
		tp.setEditable(false);
		final SynchronousHTMLEditorKit kit = new SynchronousHTMLEditorKit();
		tp.setEditorKitForContentType("text/html", kit);
		tp.setContentType("text/html");
		tp.setEditorKit(new HTMLEditorKit()
		{
			@Override
			public ViewFactory getViewFactory()
			{
				return new HTMLFactory()
				{
					public View create(javax.swing.text.Element e)
					{
						View v = super.create(e);
						if (v instanceof InlineView)
						{
							return new InlineView(e)
							{
								public int getBreakWeight(int axis, float pos, float len)
								{
									return GoodBreakWeight;
								}

								public View breakView(int axis, int p0, float pos, float len)
								{
									if (axis == View.X_AXIS)
									{
										checkPainter();
										int p1 = getGlyphPainter().getBoundedPosition(this, p0, pos, len);
										if (p0 == getStartOffset() && p1 == getEndOffset())
										{
											return this;
										}
										return createFragment(p0, p1);
									}
									return this;
								}
							};
						}
						else if (v instanceof ParagraphView)
						{
							return new ParagraphView(e)
							{
								protected SizeRequirements calculateMinorAxisRequirements(int axis, SizeRequirements r)
								{
									if (r == null)
									{
										r = new SizeRequirements();
									}
									float pref = layoutPool.getPreferredSpan(axis);
									float min = layoutPool.getMinimumSpan(axis);
									// Don't include insets, Box.getXXXSpan will
									// include them.
									r.minimum = (int) min;
									r.preferred = Math.max(r.minimum, (int) pref);
									r.maximum = Integer.MAX_VALUE;
									r.alignment = 0.5f;
									return r;
								}

							};
						}
						return v;
					}
				};
			}
		});
		tp.addPropertyChangeListener(new PropertyChangeListener()
		{
			public void propertyChange(PropertyChangeEvent evt)
			{
				if (evt.getPropertyName().equals("page"))
				{
					// onDocumentLoad();
				}
			}
		});
		tp.setText(html);
		Dimension prefSize = tp.getPreferredSize();
		BufferedImage image = new java.awt.image.BufferedImage(prefSize.width, prefSize.height, java.awt.image.BufferedImage.TYPE_4BYTE_ABGR);
		Graphics graphics = image.getGraphics();
		tp.setSize(prefSize);
		tp.paint(graphics);

		return image;
	}
	
	@SuppressWarnings("serial")
	public static BufferedImage html3bi(String html) throws Exception
	{
		//HtmlImageGenerator
		JTextPane tp = new JTextPane();
		
		tp.setSize(DEFAULT_IMAGE_WIDTH, DEFAULT_IMAGE_HEIGHT);
		tp.setBackground(Color.white);
		tp.setSize(DEFAULT_SIZE);
		tp.setEditable(false);
		final SynchronousHTMLEditorKit kit = new SynchronousHTMLEditorKit();
		tp.setEditorKitForContentType("text/html", kit);
		tp.setContentType("text/html");
		tp.setEditorKit(new HTMLEditorKit()
		{
			@Override
			public ViewFactory getViewFactory()
			{
				return new HTMLFactory()
				{
					public View create(javax.swing.text.Element e)
					{
						View v = super.create(e);
						if (v instanceof InlineView)
						{
							return new InlineView(e)
							{
								public int getBreakWeight(int axis, float pos, float len)
								{
									return GoodBreakWeight;
								}

								public View breakView(int axis, int p0, float pos, float len)
								{
									if (axis == View.X_AXIS)
									{
										checkPainter();
										int p1 = getGlyphPainter().getBoundedPosition(this, p0, pos, len);
										if (p0 == getStartOffset() && p1 == getEndOffset())
										{
											return this;
										}
										return createFragment(p0, p1);
									}
									return this;
								}
							};
						}
						else if (v instanceof ParagraphView)
						{
							return new ParagraphView(e)
							{
								protected SizeRequirements calculateMinorAxisRequirements(int axis, SizeRequirements r)
								{
									if (r == null)
									{
										r = new SizeRequirements();
									}
									float pref = layoutPool.getPreferredSpan(axis);
									float min = layoutPool.getMinimumSpan(axis);
									// Don't include insets, Box.getXXXSpan will
									// include them.
									r.minimum = (int) min;
									r.preferred = Math.max(r.minimum, (int) pref);
									r.maximum = Integer.MAX_VALUE;
									r.alignment = 0.5f;
									return r;
								}

							};
						}
						return v;
					}
				};
			}
		});
		tp.addPropertyChangeListener(new PropertyChangeListener()
		{
			public void propertyChange(PropertyChangeEvent evt)
			{
				if (evt.getPropertyName().equals("page"))
				{
					// onDocumentLoad();
				}
			}
		});
		
		tp.setText(html);
		Dimension prefSize = tp.getPreferredSize();
		BufferedImage image = new java.awt.image.BufferedImage(prefSize.width, prefSize.height, java.awt.image.BufferedImage.TYPE_4BYTE_ABGR);
		
		Graphics2D graphics = (Graphics2D)image.getGraphics();
		graphics.setStroke(new BasicStroke(0f, BasicStroke.CAP_BUTT, BasicStroke.JOIN_MITER)); 
		//g2d.setRenderingHint(hintKey, hintValue);
		graphics.setBackground(Color.WHITE);
		graphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING,RenderingHints.VALUE_ANTIALIAS_ON);    
		graphics.setRenderingHint(RenderingHints.KEY_STROKE_CONTROL,RenderingHints.VALUE_STROKE_DEFAULT);    
		tp.setSize(prefSize);
		tp.paint(graphics);
//		int toWidth = new Double(prefSize.width/2.8).intValue();
//		int toHeight = new Double(prefSize.height/2.8).intValue()	;	
//		BufferedImage result = new BufferedImage(toWidth,toHeight , BufferedImage.TYPE_INT_RGB); 
//		
//		Graphics2D graphics2 =  (Graphics2D)result.getGraphics();
//		graphics2.setStroke(new BasicStroke(0f, BasicStroke.CAP_BUTT, BasicStroke.JOIN_MITER)); 
//		//g2d.setRenderingHint(hintKey, hintValue);
//		graphics2.setBackground(Color.WHITE);
////		graphics2.setRenderingHint(RenderingHints.KEY_ANTIALIASING,RenderingHints.VALUE_ANTIALIAS_ON);    
////		graphics2.setRenderingHint(RenderingHints.KEY_STROKE_CONTROL,RenderingHints.VALUE_STROKE_DEFAULT);    
////	    
//		result.getGraphics().drawImage( image.getScaledInstance(toWidth, toHeight, java.awt.Image.SCALE_SMOOTH), 0, 0, null);

		return image;
	}
	
	@SuppressWarnings("serial")
	public static int[] html2bif(String html,String fliename) throws Exception
	{

		JTextPane tp = new JTextPane();

		tp.setSize(DEFAULT_IMAGE_WIDTH, DEFAULT_IMAGE_HEIGHT);
		tp.setBackground(Color.white);
		tp.setSize(DEFAULT_SIZE);
		tp.setEditable(false);
		final SynchronousHTMLEditorKit kit = new SynchronousHTMLEditorKit();
		tp.setEditorKitForContentType("text/html", kit);
		tp.setContentType("text/html");
		tp.setEditorKit(new HTMLEditorKit()
		{
			@Override
			public ViewFactory getViewFactory()
			{
				return new HTMLFactory()
				{
					public View create(javax.swing.text.Element e)
					{
						View v = super.create(e);
						if (v instanceof InlineView)
						{
							return new InlineView(e)
							{
								public int getBreakWeight(int axis, float pos, float len)
								{
									return GoodBreakWeight;
								}

								public View breakView(int axis, int p0, float pos, float len)
								{
									if (axis == View.X_AXIS)
									{
										checkPainter();
										int p1 = getGlyphPainter().getBoundedPosition(this, p0, pos, len);
										if (p0 == getStartOffset() && p1 == getEndOffset())
										{
											return this;
										}
										return createFragment(p0, p1);
									}
									return this;
								}
							};
						}
						else if (v instanceof ParagraphView)
						{
							return new ParagraphView(e)
							{
								protected SizeRequirements calculateMinorAxisRequirements(int axis, SizeRequirements r)
								{
									if (r == null)
									{
										r = new SizeRequirements();
									}
									float pref = layoutPool.getPreferredSpan(axis);
									float min = layoutPool.getMinimumSpan(axis);
									// Don't include insets, Box.getXXXSpan will
									// include them.
									r.minimum = (int) min;
									r.preferred = Math.max(r.minimum, (int) pref);
									r.maximum = Integer.MAX_VALUE;
									r.alignment = 0.5f;
									return r;
								}

							};
						}
						return v;
					}
				};
			}
		});
		tp.addPropertyChangeListener(new PropertyChangeListener()
		{
			public void propertyChange(PropertyChangeEvent evt)
			{
				if (evt.getPropertyName().equals("page"))
				{
					// onDocumentLoad();
				}
			}
		});
		tp.setText(html);
		Dimension prefSize = tp.getPreferredSize();
		BufferedImage image = new java.awt.image.BufferedImage(prefSize.width, prefSize.height, java.awt.image.BufferedImage.TYPE_4BYTE_ABGR);
		int [] aa = {image.getWidth(),image.getHeight()};
//		System.out.println(width);
//		System.out.println(height);
		Graphics2D graphics = (Graphics2D)image.getGraphics();
		graphics.setStroke(new BasicStroke(0f, BasicStroke.CAP_BUTT, BasicStroke.JOIN_MITER)); 
		//g2d.setRenderingHint(hintKey, hintValue);
		graphics.setBackground(Color.WHITE);
		graphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING,RenderingHints.VALUE_ANTIALIAS_ON);    
		graphics.setRenderingHint(RenderingHints.KEY_STROKE_CONTROL,RenderingHints.VALUE_STROKE_DEFAULT);    
	    
		tp.setSize(prefSize);
		tp.paint(graphics);
		int toWidth = new Double(prefSize.width/2.8).intValue();
		int toHeight = new Double(prefSize.height/2.8).intValue()	;	
		BufferedImage result = new BufferedImage(toWidth,toHeight , BufferedImage.TYPE_INT_RGB); 
		
		Graphics2D graphics2 =  (Graphics2D)result.getGraphics();
		graphics2.setStroke(new BasicStroke(0f, BasicStroke.CAP_BUTT, BasicStroke.JOIN_MITER)); 
		//g2d.setRenderingHint(hintKey, hintValue);
		graphics2.setBackground(Color.WHITE);
		graphics2.setRenderingHint(RenderingHints.KEY_ANTIALIASING,RenderingHints.VALUE_ANTIALIAS_ON);    
		graphics2.setRenderingHint(RenderingHints.KEY_STROKE_CONTROL,RenderingHints.VALUE_STROKE_DEFAULT);    
	    
		result.getGraphics().drawImage( image.getScaledInstance(toWidth, toHeight, java.awt.Image.SCALE_SMOOTH), 0, 0, null);
		
		
		File file = new File(fliename);
		ImageIO.write((RenderedImage)result, "png", file);  
		return aa;
	}
	
	@SuppressWarnings("serial")
	public static void html2in(String html,InputStream in) throws Exception
	{

		JTextPane tp = new JTextPane();

		tp.setSize(DEFAULT_IMAGE_WIDTH, DEFAULT_IMAGE_HEIGHT);
		tp.setBackground(Color.white);
		tp.setSize(DEFAULT_SIZE);
		tp.setEditable(false);
		final SynchronousHTMLEditorKit kit = new SynchronousHTMLEditorKit();
		tp.setEditorKitForContentType("text/html", kit);
		tp.setContentType("text/html");
		tp.setEditorKit(new HTMLEditorKit()
		{
			@Override
			public ViewFactory getViewFactory()
			{
				return new HTMLFactory()
				{
					public View create(javax.swing.text.Element e)
					{
						View v = super.create(e);
						if (v instanceof InlineView)
						{
							return new InlineView(e)
							{
								public int getBreakWeight(int axis, float pos, float len)
								{
									return GoodBreakWeight;
								}

								public View breakView(int axis, int p0, float pos, float len)
								{
									if (axis == View.X_AXIS)
									{
										checkPainter();
										int p1 = getGlyphPainter().getBoundedPosition(this, p0, pos, len);
										if (p0 == getStartOffset() && p1 == getEndOffset())
										{
											return this;
										}
										return createFragment(p0, p1);
									}
									return this;
								}
							};
						}
						else if (v instanceof ParagraphView)
						{
							return new ParagraphView(e)
							{
								protected SizeRequirements calculateMinorAxisRequirements(int axis, SizeRequirements r)
								{
									if (r == null)
									{
										r = new SizeRequirements();
									}
									float pref = layoutPool.getPreferredSpan(axis);
									float min = layoutPool.getMinimumSpan(axis);
									// Don't include insets, Box.getXXXSpan will
									// include them.
									r.minimum = (int) min;
									r.preferred = Math.max(r.minimum, (int) pref);
									r.maximum = Integer.MAX_VALUE;
									r.alignment = 0.5f;
									return r;
								}

							};
						}
						return v;
					}
				};
			}
		});
		tp.addPropertyChangeListener(new PropertyChangeListener()
		{
			public void propertyChange(PropertyChangeEvent evt)
			{
				if (evt.getPropertyName().equals("page"))
				{
					// onDocumentLoad();
				}
			}
		});
		tp.setText(html);
		Dimension prefSize = tp.getPreferredSize();
		BufferedImage image = new java.awt.image.BufferedImage(prefSize.width, prefSize.height, java.awt.image.BufferedImage.TYPE_4BYTE_ABGR);
		//int [] aa = {image.getWidth(),image.getHeight()};
//		System.out.println(width);
//		System.out.println(height);
		ByteArrayOutputStream outstream=new ByteArrayOutputStream();
		ImageIO.write( image, "png", outstream);
	
		byte[] buf=outstream.toByteArray();
		in = new ByteArrayInputStream(buf);
	}
	
	
	public static String transformationHtmlTemplate2(int pageSize,int fontSize, String template, Map<String, java.util.List<JSONObject>> params)
	{
		if(pageSize<=PAPER_CORRECT_SIZE || template==null || params==null)
		{
			return "<p>***模板格式错误***</p>";
		}
		
		int correct_page_size =  pageSize - PAPER_CORRECT_SIZE;
		Double p_s = SIZE_PX * correct_page_size/2.8;
		p_s = DoubleHelper.round(p_s, 0);
		Document doc = Jsoup.parse(template);
		Elements imgs = doc.getElementsByTag("img");
		int imsize = imgs.size();
		for(int i =0;i<imsize;i++)
		{
			Element element = imgs.get(i);
			String nodeName = element.attr("title");
			if(nodeName!=null && nodeName.indexOf("R")==0)
			{
				element.tagName("span");
				element.removeAttr("src");
				element.removeAttr("alt");
			}
		}
		
		Elements elements = doc.getElementsByTag("table");
		int size = elements.size();
		
		for(int i =0;i<size;i++)
		{
			Element element = elements.get(i);
			String nodeName = element.attr("title");
			if(nodeName!=null && nodeName.length()>0)
			{
				Elements trs = element.getElementsByTag("tr");
				
				Elements trsclone = trs.clone();
				
				if(trsclone.size()>1)
				{
					Element trhead = trs.first();
					String displayhead = trhead.attr("displayhead");
					element.html("");
					if(displayhead!=null && "1".equals(displayhead))
					{
						trhead.attr("style", "display:none;");
					}
					else
					{
						element.append(trhead.clone().toString());
					}
					
					trsclone.remove(0);
				}
				//Elements trtrclone = trs.clone();
				
				
				if(params.containsKey(nodeName))
				{
					java.util.List<JSONObject> list = params.get(nodeName);
					if(list != null)
					{
						for(JSONObject jo : list)
						{
							Elements trsclone2 = trsclone.clone();
							//System.out.println("C2:"+trsclone2.toString());
							@SuppressWarnings("unchecked")
							Iterator<String> keys = jo.keySet().iterator();
							while(keys.hasNext())
							{
								//System.out.println(1);
								String key1 = keys.next();
								String key = nodeName+":"+key1;
								Elements elespan = trsclone2.select("span[title="+key+"]");
								int esize = elespan.size();
								for(int ii=0;ii<esize;ii++)
								{
									Element ele = elespan.get(ii);
									ele.text(jo.getString(key1));
									ele.removeAttr("title");
								}
							} 
							//System.out.println("k");
							//System.out.println(trsclone2.toString());
							element.append(trsclone2.toString());
							//System.out.println("j");
						}
					}
				}
			}
			else
			{
				Elements trs = element.getElementsByTag("tr");
				Element trhead = trs.first();
				String displayhead = trhead.attr("displayhead");
				if(displayhead!=null && "1".equals(displayhead))
				{
					trhead.attr("style", "display:none");
				}
			}
		}
		Elements elespan = doc.select("span[title]");
		int z = elespan.size();
		for(int i=0;i<z;i++)
		{
			Element span = elespan.get(0);
			String title = span.attr("title");
			if(null!=title && title.indexOf("R")==0 && title.indexOf(":")>0)
			{
				String[] titles = title.split(":");
				if(titles.length==2)
				{
					String key = titles[0];
					String pro = titles[1];
					String con = params.containsKey(key)?(params.get(key).get(0).containsKey(pro)?params.get(key).get(0).getString(pro):""):"";
					span.text(con);
				}
			}
			
		}
		String docs = doc.toString().replace("<body>","").replace("</body>","").replace("<html>", "").replace("</html>", "").replace("<head></head>", "").replace("<tbody>", "").replace("</tbody>", "");
		StringBuilder sb = new StringBuilder("<div style=\"width:"+p_s+"px;font-size:"+(fontSize-18)+"px;padding-left:5px\">"+ docs +"</div>");
		
		return sb.toString();
	}
	
	public static String transformationHtmlTemplate3(int pageSize,int fontSize, String template, Map<String, java.util.List<JSONObject>> params)
	{
		if(pageSize<=PAPER_CORRECT_SIZE || template==null || params==null)
		{
			return "<p>***模板格式错误***</p>";
		}
		
		int correct_page_size =  pageSize - PAPER_CORRECT_SIZE;
		Double p_s = SIZE_PX * correct_page_size;
		p_s = DoubleHelper.round(p_s, 0);
		Document doc = Jsoup.parse(template);
		Elements imgs = doc.getElementsByTag("a");
		int imsize = imgs.size();
		for(int i =0;i<imsize;i++)
		{
			Element element = imgs.get(i);
			String nodeName = element.attr("title");
			if(nodeName!=null && (nodeName.indexOf("R")==0 || nodeName.indexOf("L")==0 || nodeName.indexOf("R")==0 || nodeName.indexOf("P")==0))
			{
				element.tagName("span");
				element.removeAttr("src");
				element.removeAttr("alt");
				element.html("");
				if(nodeName.indexOf("PN")==0)
				{
					element.html(":page_now:");
				}
				if(nodeName.indexOf("PT")==0)
				{
					element.html(":page_total:");
				}
					
			}
		}
		
		Elements elements = doc.getElementsByTag("table");
		int size = elements.size();
		
		for(int i =0;i<size;i++)
		{
			Element element = elements.get(i);
			String nodeName = element.attr("title");
			if(nodeName!=null && nodeName.length()>0)
			{
				Elements trs = element.getElementsByTag("tr");
				
				Elements trsclone = trs.clone();
				
				if(trsclone.size()>1)
				{
					Element trhead = trs.first();
					String displayhead = trhead.attr("displayhead");
					element.html("");
					if(displayhead!=null && "1".equals(displayhead))
					{
						trhead.attr("style", "display:none;");
					}
					else
					{
						element.append(trhead.clone().toString());
					}
					
					trsclone.remove(0);
				}
				//Elements trtrclone = trs.clone();
				
				
				if(params.containsKey(nodeName))
				{
					java.util.List<JSONObject> list = params.get(nodeName);
					if(list != null)
					{
						for(JSONObject jo : list)
						{
							Elements trsclone2 = trsclone.clone();
							//System.out.println("C2:"+trsclone2.toString());
							@SuppressWarnings("unchecked")
							Iterator<String> keys = jo.keySet().iterator();
							while(keys.hasNext())
							{
								//System.out.println(1);
								String key1 = keys.next();
								String key = nodeName+":"+key1;
								Elements elespan = trsclone2.select("span[title="+key+"]");
								int esize = elespan.size();
								for(int ii=0;ii<esize;ii++)
								{
									Element ele = elespan.get(ii);
									ele.text(jo.getString(key1));
									ele.removeAttr("title");
								}
							} 
							//System.out.println("k");
							//System.out.println(trsclone2.toString());
							element.append(trsclone2.toString());
							//System.out.println("j");
						}
					}
				}
			}
			else
			{
				Elements trs = element.getElementsByTag("tr");
				Element trhead = trs.first();
				String displayhead = trhead.attr("displayhead");
				if(displayhead!=null && "1".equals(displayhead))
				{
					trhead.attr("style", "display:none");
				}
			}
		}
		Elements elespan = doc.select("span[title]");
		int z = elespan.size();
		for(int i=0;i<z;i++)
		{
			Element span = elespan.get(0);
			String title = span.attr("title");
			if(null!=title && title.indexOf("R")==0 && title.indexOf(":")>0)
			{
				String[] titles = title.split(":");
				if(titles.length==2)
				{
					String key = titles[0];
					String pro = titles[1];
					String con = params.containsKey(key)?(params.get(key).get(0).containsKey(pro)?params.get(key).get(0).getString(pro):""):"";
					span.text(con);
				}
			}
			
		}
		String docs = doc.toString().replace("<body>","").replace("</body>","").replace("<html>", "").replace("</html>", "").replace("<head></head>", "").replace("<tbody>", "").replace("</tbody>", "");
		StringBuilder sb = new StringBuilder("<div style=\"width:"+p_s+"px;font-size:"+(fontSize<18?18:(fontSize>50?50:fontSize))+"px;margin-left:25px\">"+ docs +"</div>");
		
		return sb.toString();
	}
	
	public static String transformationHtmlTemplate(int pageSize,int fontSize, String template, Map<String, java.util.List<JSONObject>> params)
	{
		if(pageSize<=PAPER_CORRECT_SIZE || template==null || params==null)
		{
			return "<p>***模板格式错误***</p>";
		}
		
		int correct_page_size =  pageSize - PAPER_CORRECT_SIZE;
		Double p_s = SIZE_PX * correct_page_size;
		p_s = DoubleHelper.round(p_s, 0);
		Document doc = Jsoup.parse(template);
		Elements imgs = doc.getElementsByTag("img");
		int imsize = imgs.size();
		for(int i =0;i<imsize;i++)
		{
			Element element = imgs.get(i);
			String nodeName = element.attr("title");
			if(nodeName!=null && nodeName.indexOf("R")==0)
			{
				element.tagName("span");
				element.removeAttr("src");
				element.removeAttr("alt");
			}
		}
		
		Elements elements = doc.getElementsByTag("table");
		int size = elements.size();
		
		for(int i =0;i<size;i++)
		{
			Element element = elements.get(i);
			String nodeName = element.attr("title");
			if(nodeName!=null && nodeName.length()>0)
			{
				Elements trs = element.getElementsByTag("tr");
				
				Elements trsclone = trs.clone();
				
				if(trsclone.size()>1)
				{
					Element trhead = trs.first();
					String displayhead = trhead.attr("displayhead");
					element.html("");
					if(displayhead!=null && "1".equals(displayhead))
					{
						trhead.attr("style", "display:none;");
					}
					else
					{
						element.append(trhead.clone().toString());
					}
					
					trsclone.remove(0);
				}
				//Elements trtrclone = trs.clone();
				
				
				if(params.containsKey(nodeName))
				{
					java.util.List<JSONObject> list = params.get(nodeName);
					if(list != null)
					{
						for(JSONObject jo : list)
						{
							Elements trsclone2 = trsclone.clone();
							//System.out.println("C2:"+trsclone2.toString());
							@SuppressWarnings("unchecked")
							Iterator<String> keys = jo.keySet().iterator();
							while(keys.hasNext())
							{
								//System.out.println(1);
								String key1 = keys.next();
								String key = nodeName+":"+key1;
								Elements elespan = trsclone2.select("span[title="+key+"]");
								int esize = elespan.size();
								for(int ii=0;ii<esize;ii++)
								{
									Element ele = elespan.get(ii);
									ele.text(jo.getString(key1));
									ele.removeAttr("title");
								}
							} 
							//System.out.println("k");
							//System.out.println(trsclone2.toString());
							element.append(trsclone2.toString());
							//System.out.println("j");
						}
					}
				}
			}
			else
			{
				Elements trs = element.getElementsByTag("tr");
				Element trhead = trs.first();
				String displayhead = trhead.attr("displayhead");
				if(displayhead!=null && "1".equals(displayhead))
				{
					trhead.attr("style", "display:none");
				}
			}
		}
		Elements elespan = doc.select("span[title]");
		int z = elespan.size();
		for(int i=0;i<z;i++)
		{
			Element span = elespan.get(0);
			String title = span.attr("title");
			if(null!=title && title.indexOf("R")==0 && title.indexOf(":")>0)
			{
				String[] titles = title.split(":");
				if(titles.length==2)
				{
					String key = titles[0];
					String pro = titles[1];
					String con = params.containsKey(key)?(params.get(key).get(0).containsKey(pro)?params.get(key).get(0).getString(pro):""):"";
					span.text(con);
				}
			}
			
		}
		String docs = doc.toString().replace("<body>","").replace("</body>","").replace("<html>", "").replace("</html>", "").replace("<head></head>", "").replace("<tbody>", "").replace("</tbody>", "");
		StringBuilder sb = new StringBuilder("<div style=\"width:"+p_s+"px;font-size:"+(fontSize<18?18:(fontSize>50?50:fontSize))+"px\">"+ docs +"</div>");
		
		return sb.toString();
	}
	
	public static void printImage(int cpoy,int page_size,String printer, String template) throws Exception
	{
		
		PrintService ps = PrintHelperNew.getPrintService(printer);
		BufferedImage image = html2bi(template);
		ByteArrayOutputStream os = new ByteArrayOutputStream();
		ImageIO.write(image, "png", os);
		InputStream is = new ByteArrayInputStream(os.toByteArray());
		PrintRequestAttributeSet pras = new HashPrintRequestAttributeSet();  
		DocAttributeSet das = new HashDocAttributeSet(); 
//		float h = image.getHeight()*page_size/image.getWidth();
//		float w = page_size/1f;
//		PageFormat pf = new PageFormat();
//		Paper p = new Paper();
//		p.setSize(w, h); 
//	    p.setImageableArea(0,0, w, h);  
//		pf.setPaper(p);
//		das.add(pf);

        // 设置纸张大小,也可以新建MediaSize类来自定义大小
		
		das.add(new MediaPrintableArea(200,200,image.getWidth()+image.getHeight(),image.getHeight(), MediaPrintableArea.MM)); 
		Doc doc = new SimpleDoc(is,DocFlavor.INPUT_STREAM.PNG ,das);
		//pras.add(MediaSizeName.INVOICE);
		//pras.add(ms.getMediaSizeName());
		DocPrintJob docPrintJob = ps.createPrintJob();
		docPrintJob.print(doc, pras);
		//docPrintJob.print(doc, attributes);

	}

	
	
	public static void printImagef3(int cpoy,int page_size,String printer, String template) throws Exception
	{
		BufferedImage bi  = html2bi(template);
		DocAttributeSet attr = new HashDocAttributeSet();
		attr.add(new MediaPrintableArea(0, 0, 80, bi.getHeight(), MediaPrintableArea.MM));
		Doc doc = new SimpleDoc(new NewPrintable(bi), DocFlavor.SERVICE_FORMATTED.PRINTABLE, attr);
		PrintRequestAttributeSet pras = new HashPrintRequestAttributeSet();  
		PrintService[] ss = PrintServiceLookup.lookupPrintServices(DocFlavor.INPUT_STREAM.PNG, pras);
		for(int i=0;i<ss.length;i++)
		{
			PrintService ps1 = ss[i];
			if(printer.equals(ps1.getName()))
			{
				DocPrintJob printJob = ps1.createPrintJob();
				printJob.print(doc, pras);
			}
		}
	}
	
	public static void cf(BufferedImage bi,Book book)
	{
		int height = bi.getHeight();
		int width = bi.getWidth();
		if(height<=800)
		{
			PageFormat pf = new PageFormat();  
		    //pf.setOrientation(PageFormat.PORTRAIT); // LANDSCAPE表示竖打;PORTRAIT表示横打;REVERSE_LANDSCAPE表示打印空白  
		    // 通过Paper设置页面的空白边距和可打印区域。必须与实际打印纸张大小相符。  
		    Paper p = new Paper();  
		    p.setSize(300,800);
		    p.setImageableArea(0, 0, bi.getWidth(),  bi.getHeight());
		    pf.setPaper(p);
		    book.append(new NewPrintable(bi), pf); 
		}
		else
		{
			int count = 0;
			if(height%800==0)
			{
				count = height/800;
			}
			else
			{
				count = height/800 +1;
			}

			for(int now=0;now<count;now++)
			{
				int begin = 800*now;
				int cut = 800;
				if(800*(now+1)>height)
				{
					cut =  height- 800*(now);
				}
				//System.out.println(count);
				BufferedImage bImage  = bi.getSubimage(0,begin, width,cut);
				PageFormat pf = new PageFormat();  
			    //pf.setOrientation(PageFormat.PORTRAIT); // LANDSCAPE表示竖打;PORTRAIT表示横打;REVERSE_LANDSCAPE表示打印空白  
			    // 通过Paper设置页面的空白边距和可打印区域。必须与实际打印纸张大小相符。  
			    Paper p = new Paper();  
			    p.setSize(300,800);
			    p.setImageableArea(0, 0, bImage.getWidth(), bi.getHeight());
			    pf.setPaper(p);
			    book.append(new NewPrintable(bImage), pf);  	 
			}
		}
	}
	
	public static void print(String printer,int copy,String html) throws Exception
	{
		for(int i=0;i<copy;i++)
		{
			String htmln = html.replaceAll(":page_now:", i+1+"");
			htmln = htmln.replaceAll(":page_total:",copy+"");
			
			BufferedImage bi = HtmlImageGenerators.html2bi(htmln);
			Book book = new Book();  
			//System.out.println(bi.getHeight());
			cf(bi,book);
//			HashAttributeSet hs = new HashAttributeSet();   
//			hs.add(new PrinterName(printer,null)); 
//			hs.add(PrintQuality.HIGH);
//			// 获取打印服务对象  
//			PrintService[] printService =PrintServiceLookup.lookupPrintServices(null, hs);    
//			PrinterJob job = PrinterJob.getPrinterJob();  
//			if(printService.length>0){  
//				job.setPrintService(printService[0]);
//			    job.setPageable(book);  
//				job.print(); 
//			} 
			PrinterJob job = PrinterJob.getPrinterJob();  
			PrintRequestAttributeSet pras = new HashPrintRequestAttributeSet();  
			PrintService[] ss = PrintServiceLookup.lookupPrintServices(DocFlavor.INPUT_STREAM.PNG, pras);
			for(int i22=0;i22<ss.length;i22++)
			{
				PrintService ps1 = ss[i22];		
				if(printer.equals(ps1.getName()))
				{
					job.setPrintService(ps1);
				    job.setPageable(book);  
					job.print(); 
				}
			}
			
		}
	}
	
	public static int print2(String printer,int copy,String html,int printer_id) throws Exception
	{
		int ydycs = Constant.getTimes(printer_id);
		int pn = 0;
		for(int i=0;i<(copy-ydycs);i++)
		{
			String htmln = html.replaceAll(":page_now:", (i+1+ydycs)+"");
			htmln = htmln.replaceAll(":page_total:",copy+"");
			
			BufferedImage bi = HtmlImageGenerators.html3bi(htmln);
			Book book = new Book();  
			//System.out.println(bi.getHeight());
			cf(bi,book);
//			HashAttributeSet hs = new HashAttributeSet();   
//			hs.add(new PrinterName(printer,null)); 
//			hs.add(PrintQuality.HIGH);
//			// 获取打印服务对象  
//			PrintService[] printService =PrintServiceLookup.lookupPrintServices(null, hs);    
//			PrinterJob job = PrinterJob.getPrinterJob();  
//			if(printService.length>0){  
//				job.setPrintService(printService[0]);
//			    job.setPageable(book);  
//				job.print(); 
//			} 
			PrinterJob job = PrinterJob.getPrinterJob();  
			PrintRequestAttributeSet pras = new HashPrintRequestAttributeSet();  
			PrintService[] ss = PrintServiceLookup.lookupPrintServices(DocFlavor.INPUT_STREAM.PNG, pras);
			
			for(int i22=0;i22<ss.length;i22++)
			{
				PrintService ps1 = ss[i22];		
				if(printer.equals(ps1.getName()))
				{
					pn ++;
					job.setPrintService(ps1);
				    job.setPageable(book);  
					job.print(); 
				}
			}
			
		}
		
		return pn;
	}
}