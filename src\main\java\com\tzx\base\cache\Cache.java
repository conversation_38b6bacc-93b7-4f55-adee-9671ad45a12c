package com.tzx.base.cache;

/**
 * Created by kevin on 2017-06-15.
 * 缓存类
 */
public class Cache {

    /**
     * 缓存键值id
     */
    private String key;

    /**
     * 缓存数据对象
     */
    private Object value;

    /**
     * 缓存更新时间
     */
    private long timeOut;

    /**
     * 缓存是否终止
     */
    private boolean expired;

    public Cache() {
        super();
    }

    public Cache(String key, Object value, long timeOut, boolean expired) {
        this.key = key;
        this.value = value;
        this.timeOut = timeOut;
        this.expired = expired;
    }

    public String getKey() {
        return key;
    }

    public long getTimeOut() {
        return timeOut;
    }

    public Object getValue() {
        return value;
    }

    public void setKey(String string) {
        key = string;
    }

    public void setTimeOut(long l) {
        timeOut = l;
    }

    public void setValue(Object object) {
        value = object;
    }

    public boolean isExpired() {
        return expired;
    }

    public void setExpired(boolean b) {
        expired = b;
    }
}
