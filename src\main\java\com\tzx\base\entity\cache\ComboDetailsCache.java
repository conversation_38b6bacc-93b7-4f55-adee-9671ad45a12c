package com.tzx.base.entity.cache;

public class ComboDetailsCache
{
	public static String getSql()
	{
		String sql = new String("select distinct hicd.id as combo_details_id,hicd.iitem_id,hicd.is_itemgroup,hicd.details_id,hicd.item_unit_id,hicd.combo_num,hicd.standardprice,hicd.combo_order,hicp.price_system,hicp.chanel,hicp.price from hq_item_combo_details hicd left join hq_item_combo_pricesystem hicp on hicd.tenancy_id=hicp.tenancy_id and hicd.id=hicp.combo_details_id left join organ org on org.tenancy_id=hicp.tenancy_id and org.price_system=hicp.price_system||'' where hicd.valid_state='1' and org.tenancy_id=::tenancy_id and org.id=::store_id");
		return sql;
	}
	
	public static String getKey()
	{
		return "iitem_id,combo_details_id,chanel";
	}
}
