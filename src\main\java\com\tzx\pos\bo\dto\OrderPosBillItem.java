package com.tzx.pos.bo.dto;

import java.io.Serializable;

/**
 * 
 * <AUTHOR> 
 * @日期：2015年5月22日-上午11:40:16
 */

public class OrderPosBillItem implements Serializable
{

	/**
	 * 
	 */
	private static final long	serialVersionUID	= 1L;
	
	private String tenancy_id;
	private String organ_id;
	private String bill_num;
	private String details_id;
	private String item_id;
	private String item_num;
	private String item_name;
	private String item_english;
	private String report_date;
	private String item_unit_id;
	private String item_unit_name;
	private String table_code;
	private String pushmoney_way;
	private String proportion;
	private String assist_num;
	private String assist_money;
	private String waiter_num;
	private String item_price;
	private String item_count;
	private String discount_rate;
	private String item_amount;
	private String real_amount;
	private String discount_amount;
	private String single_discount_amount;
	private String discountr_amount;
	private String discount_state;
	private String discount_mode_id;
	private String item_class_id;
	private String item_property;
	private String item_remark;
	private String print_tag;
	private String waitcall_tag;
	private String setmeal_id;
	private String setmeal_rwid;
	private String is_setmeal_changitem;
	private String item_time;
	private String item_serial;
	private String item_shift_id;
	private String item_mac_id;
	private String order_remark;
	private String seat_num;
	private String sale_mode;
	private String item_taste;
	private String assist_item_id;
	private String method_money;
	
	public String getMethod_money()
	{
		return method_money;
	}
	public void setMethod_money(String method_money)
	{
		this.method_money = method_money;
	}
	public String getAssist_item_id()
	{
		return assist_item_id;
	}
	public void setAssist_item_id(String assist_item_id)
	{
		this.assist_item_id = assist_item_id;
	}
	public String getWaitcall_tag()
	{
		return waitcall_tag;
	}
	public void setWaitcall_tag(String waitcall_tag)
	{
		this.waitcall_tag = waitcall_tag;
	}
	public String getItem_taste()
	{
		return item_taste;
	}
	public void setItem_taste(String item_taste)
	{
		this.item_taste = item_taste;
	}
	public void setPrint_tag(String print_tag)
	{
		this.print_tag = print_tag;
	}
	public String getTenancy_id()
	{
		return tenancy_id;
	}
	public void setTenancy_id(String tenancy_id)
	{
		this.tenancy_id = tenancy_id;
	}
	public String getOrgan_id()
	{
		return organ_id;
	}
	public void setOrgan_id(String organ_id)
	{
		this.organ_id = organ_id;
	}
	public String getBill_num()
	{
		return bill_num;
	}
	public void setBill_num(String bill_num)
	{
		this.bill_num = bill_num;
	}
	public String getDetails_id()
	{
		return details_id;
	}
	public void setDetails_id(String details_id)
	{
		this.details_id = details_id;
	}
	public String getItem_id()
	{
		return item_id;
	}
	public void setItem_id(String item_id)
	{
		this.item_id = item_id;
	}
	public String getItem_num()
	{
		return item_num;
	}
	public void setItem_num(String item_num)
	{
		this.item_num = item_num;
	}
	public String getItem_name()
	{
		return item_name;
	}
	public void setItem_name(String item_name)
	{
		this.item_name = item_name;
	}
	public String getItem_english()
	{
		return item_english;
	}
	public void setItem_english(String item_english)
	{
		this.item_english = item_english;
	}
	public String getReport_date()
	{
		return report_date;
	}
	public void setReport_date(String report_date)
	{
		this.report_date = report_date;
	}
	public String getItem_unit_id()
	{
		return item_unit_id;
	}
	public void setItem_unit_id(String item_unit_id)
	{
		this.item_unit_id = item_unit_id;
	}
	public String getItem_unit_name()
	{
		return item_unit_name;
	}
	public void setItem_unit_name(String item_unit_name)
	{
		this.item_unit_name = item_unit_name;
	}
	public String getTable_code()
	{
		return table_code;
	}
	public void setTable_code(String table_code)
	{
		this.table_code = table_code;
	}
	public String getPushmoney_way()
	{
		return pushmoney_way;
	}
	public void setPushmoney_way(String pushmoney_way)
	{
		this.pushmoney_way = pushmoney_way;
	}
	public String getProportion()
	{
		return proportion;
	}
	public void setProportion(String proportion)
	{
		this.proportion = proportion;
	}
	public String getAssist_num()
	{
		return assist_num;
	}
	public void setAssist_num(String assist_num)
	{
		this.assist_num = assist_num;
	}
	public String getAssist_money()
	{
		return assist_money;
	}
	public void setAssist_money(String assist_money)
	{
		this.assist_money = assist_money;
	}
	public String getWaiter_num()
	{
		return waiter_num;
	}
	public void setWaiter_num(String waiter_num)
	{
		this.waiter_num = waiter_num;
	}
	public String getItem_price()
	{
		return item_price;
	}
	public void setItem_price(String item_price)
	{
		this.item_price = item_price;
	}
	public String getItem_count()
	{
		return item_count;
	}
	public void setItem_count(String item_count)
	{
		this.item_count = item_count;
	}
	public String getDiscount_rate()
	{
		return discount_rate;
	}
	public void setDiscount_rate(String discount_rate)
	{
		this.discount_rate = discount_rate;
	}
	public String getItem_amount()
	{
		return item_amount;
	}
	public void setItem_amount(String item_amount)
	{
		this.item_amount = item_amount;
	}
	public String getReal_amount()
	{
		return real_amount;
	}
	public void setReal_amount(String real_amount)
	{
		this.real_amount = real_amount;
	}
	public String getDiscount_amount()
	{
		return discount_amount;
	}
	public void setDiscount_amount(String discount_amount)
	{
		this.discount_amount = discount_amount;
	}
	public String getSingle_discount_amount()
	{
		return single_discount_amount;
	}
	public void setSingle_discount_amount(String single_discount_amount)
	{
		this.single_discount_amount = single_discount_amount;
	}
	public String getDiscountr_amount()
	{
		return discountr_amount;
	}
	public void setDiscountr_amount(String discountr_amount)
	{
		this.discountr_amount = discountr_amount;
	}
	public String getDiscount_state()
	{
		return discount_state;
	}
	public void setDiscount_state(String discount_state)
	{
		this.discount_state = discount_state;
	}
	public String getDiscount_mode_id()
	{
		return discount_mode_id;
	}
	public void setDiscount_mode_id(String discount_mode_id)
	{
		this.discount_mode_id = discount_mode_id;
	}
	public String getItem_class_id()
	{
		return item_class_id;
	}
	public void setItem_class_id(String item_class_id)
	{
		this.item_class_id = item_class_id;
	}
	public String getItem_property()
	{
		return item_property;
	}
	public void setItem_property(String item_property)
	{
		this.item_property = item_property;
	}
	public String getItem_remark()
	{
		return item_remark;
	}
	public void setItem_remark(String item_remark)
	{
		this.item_remark = item_remark;
	}
	public String getPrint_tag()
	{
		return print_tag;
	}
	public void setPrint_tg(String print_tag)
	{
		this.print_tag = print_tag;
	}
	public String getWait_calltag()
	{
		return waitcall_tag;
	}
	public void setWait_calltag(String waitcall_tag)
	{
		this.waitcall_tag = waitcall_tag;
	}
	public String getSetmeal_id()
	{
		return setmeal_id;
	}
	public void setSetmeal_id(String setmeal_id)
	{
		this.setmeal_id = setmeal_id;
	}
	public String getSetmeal_rwid()
	{
		return setmeal_rwid;
	}
	public void setSetmeal_rwid(String setmeal_rwid)
	{
		this.setmeal_rwid = setmeal_rwid;
	}
	public String getIs_setmeal_changitem()
	{
		return is_setmeal_changitem;
	}
	public void setIs_setmeal_changitem(String is_setmeal_changitem)
	{
		this.is_setmeal_changitem = is_setmeal_changitem;
	}
	public String getItem_time()
	{
		return item_time;
	}
	public void setItem_time(String item_time)
	{
		this.item_time = item_time;
	}
	public String getItem_serial()
	{
		return item_serial;
	}
	public void setItem_serial(String item_serial)
	{
		this.item_serial = item_serial;
	}
	public String getItem_shift_id()
	{
		return item_shift_id;
	}
	public void setItem_shift_id(String item_shift_id)
	{
		this.item_shift_id = item_shift_id;
	}
	public String getItem_mac_id()
	{
		return item_mac_id;
	}
	public void setItem_mac_id(String item_mac_id)
	{
		this.item_mac_id = item_mac_id;
	}
	public String getOrder_remark()
	{
		return order_remark;
	}
	public void setOrder_remark(String order_remark)
	{
		this.order_remark = order_remark;
	}
	public String getSeat_num()
	{
		return seat_num;
	}
	public void setSeat_num(String seat_num)
	{
		this.seat_num = seat_num;
	}
	public String getSale_mode()
	{
		return sale_mode;
	}
	public void setSale_mode(String sale_mode)
	{
		this.sale_mode = sale_mode;
	}
}
