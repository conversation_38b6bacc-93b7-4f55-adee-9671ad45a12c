package com.tzx.clientorder.wlifeprogram.bo.impl;

import com.tzx.base.common.util.StringUtil;
import com.tzx.clientorder.wlifeprogram.bo.GeneralService;
import com.tzx.clientorder.wlifeprogram.bo.WlifeService;
import com.tzx.clientorder.wlifeprogram.common.constant.WlifePromptMessage;
import com.tzx.clientorder.wlifeprogram.dao.PosGenericDao;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.DoubleHelper;
import com.tzx.framework.common.util.Tools;
import com.tzx.pos.base.constant.WLifeConstant;
import com.tzx.clientorder.acewillwechat.po.springjdbc.dao.OrderDao;
import com.tzx.clientorder.acewillwechat.po.springjdbc.dao.WshPosEntranceDao;
import com.tzx.pos.bo.PosDishService;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by qingui on 2018-06-26.
 */
@Service(GeneralService.name)
public class GeneralServiceImp implements GeneralService{

    private Logger logger = Logger.getLogger(GeneralServiceImp.class);

    @Resource(name = PosGenericDao.name)
    private PosGenericDao posGenericDao;
    @Resource(name = OrderDao.NAME)
    private OrderDao orderDao;
    @Resource(name = WshPosEntranceDao.NAME)
    private WshPosEntranceDao wshPosEntranceDao;
    @Resource(name = WlifeService.NAME)
    private WlifeService wlifeProgramService;
    @Resource(name = PosDishService.NAME)
    private PosDishService posDishService;

    @Override
    public void getOrganStatus(JSONObject responseJson,String tenancyId, int organId) throws Exception {
//        logger.info("微生活小程序查询门店是否营业");
        //判断门店是否启用微生活小程序
		String orderType = null;
		String businessId = null;
		String brandId = null;
		String shopId = null;
        List<JSONObject> paramList = posGenericDao.getParam(tenancyId);
        if (null != paramList && paramList.size() > 0) {
        	for (int i = 0; i < paramList.size(); i++) {
        		String code = paramList.get(i).optString("para_code");
        		if (WLifeConstant.USER_WLIFE_ORDER_TYPE_KEY.equals(code)) {
        			orderType = paramList.get(i).optString("para_value");
        		} else if (WLifeConstant.BUSSINESS_ID.equals(code)) {
        			businessId = paramList.get(i).optString("para_value");
        		} else if (WLifeConstant.BRAND_ID.equals(code)) {
        			brandId = paramList.get(i).optString("para_value");
        		} else if (WLifeConstant.SHOP_ID.equals(code)) {
        			shopId = paramList.get(i).optString("para_value");
        		}
        	}
        }
        
        if(!WLifeConstant.USER_WLIFE_ORDER_TYPE_PROGRAM.equals(orderType)){
            responseJson.put("success", 0);
            responseJson.put("msg", "门店未启用微生活小程序点餐");
        }
        else if(Tools.isNullOrEmpty(businessId) || Tools.isNullOrEmpty(brandId) || Tools.isNullOrEmpty(shopId)){
        	responseJson.put("success", 0);
            responseJson.put("msg", "门店未配置微生活小程序点餐相关参数");
        }
        else {
            JSONObject organ = posGenericDao.getOrganStatus(tenancyId, organId);
            //未日始的门店服务不可用
            if (null == organ){
                responseJson.put("success", 0);
                responseJson.put("msg", "门店服务不可用，查看是否日始");
            }else {
                responseJson.put("success", 1);
                responseJson.put("msg", "门店服务可用");
            }
        }
//        logger.info("微生活小程序查询门店是否营业的返回结果" + responseJson);
    }

    @Override
    public void getTableOrder(JSONObject responseJson, JSONObject jsobj) throws Exception {
        String tenancyId = jsobj.optString("tenancyId");
        int storeId = jsobj.optInt("storeId");
        String tableCode = jsobj.optString("tableno");
        String out_order_id = jsobj.optString("out_order_id");
        //传入的收银订单号
        String oid = jsobj.optString("oid");
        logger.info("小程序查询桌台状态参数有：桌台是" + tableCode + ",传入的平台订单号是:" +  out_order_id);
        if (StringUtils.isEmpty(tableCode)){
            throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
        }
        //判断门店是否启用微生活小程序
        String orderType = posGenericDao.getSysParameter(tenancyId, storeId, WLifeConstant.USER_WLIFE_ORDER_TYPE_KEY);
        if(!WLifeConstant.USER_WLIFE_ORDER_TYPE_PROGRAM.equals(orderType) &&
                !WLifeConstant.USER_WLIFE_ORDER_TYPE_MEIWEI_PROGRAM.equals(orderType)){
            responseJson.put("success", 0);
            responseJson.put("msg", "门店未启用小程序点餐");
            responseJson.put("data", new JSONObject());
        }else {
            //查询桌台，判断是否需要通知微生活平台清台、转台或并台
            if (WLifeConstant.USER_WLIFE_ORDER_TYPE_PROGRAM.equals(orderType)){
                notifyWlifeProgram(tenancyId, out_order_id, tableCode);
            }

            //查询桌台是否有账单
            JSONObject billObject = posGenericDao.getBillInfo(tenancyId, tableCode, storeId);
            logger.info("billObject "+billObject);

            //查询账单 pos是否合并账单
            String sbill_num = null;
            if(billObject!=null){
                logger.info("查询账单是否为空");
                logger.info("校验账单 "+billObject.optString("oid")+" 是否存在并台");
                JSONObject bill = posDishService.isCombineTableOfBillNum(tenancyId,storeId, billObject.optString("oid"));
                if(null!=bill){ //存在并台
                    logger.info("账单 "+billObject.optString("oid")+" 存在并台");
                    String bill_num = bill.optString("bill_num");
                    tableCode = bill.optString("table_code");
                    sbill_num= bill.optString("sbill_num");
                    logger.info("目标账单 "+bill_num+" 桌号 "+tableCode +" 原桌账单 "+sbill_num);
                    billObject = posGenericDao.getBillInfo(tenancyId, tableCode, storeId);
                    logger.info("目标账单信息 "+billObject);
                }
            }

            JSONObject data = new JSONObject();
            //门店的该桌台没有OPEN状态的订单
            if (Tools.isNullOrEmpty(billObject)){
                data.put("status", "0");
                //如果只是pos收银为空台，网络通信导致平台订单未关闭，调用结账通知
                if (StringUtil.hasText(oid) && StringUtil.hasText(out_order_id) && WLifeConstant.USER_WLIFE_ORDER_TYPE_PROGRAM.equals(orderType)){
                    //调用微生活小程序的清台,通知微生活清台
                    wlifeProgramService.completeOrder(tenancyId, storeId, oid);
                    if(Tools.hv(sbill_num)) { //并台账单零结账
                        String[] bill_nums = sbill_num.split(",");
                        if (bill_nums.length > 0) {
                            for (int i = 0; i < bill_nums.length; i++) {
                                String bill_no = bill_nums[i];
                                if (!Tools.hv(bill_no)) {
                                    continue;
                                } else {
                                    //调用微生活小程序的清台,通知微生活清台  清台并台桌
                                    wlifeProgramService.completeOrder(tenancyId, storeId, bill_no);
                                }
                            }
                        }
                    }
                }
            }else {
                if (!StringUtil.hasText(out_order_id)){
                    out_order_id = billObject.optString("out_order_id");
                }
                logger.info("小程序查询桌台状态，平台订单号是：" + out_order_id);
                //修改pos_bill表该账单的order_source订单来源为微生活小程序
                if (StringUtil.hasText(billObject.optString("out_order_id")) && !Tools.hv(sbill_num)){
                    posGenericDao.updateOrderSource(tenancyId, billObject.optString("oid"), out_order_id);
                }
                data.put("status", "1");
                logger.info("输出账单号 "+billObject.optString("oid"));
                data.put("oid", billObject.optString("oid"));
                data.put("tableno", billObject.optString("table_code"));
                //data.put("tableno", jsobj.optString("tableno"));
                //平台订单号
                data.put("out_order_id", out_order_id);

                //订单详细信息
                JSONObject orderInfo = new JSONObject();
                //设置订单的会员信息
                JSONObject member = orderDao.getWLifeMember(tenancyId, tableCode, storeId);

//                String[] paraCodes = new String[]{
//                        WLifeConstant.BUSSINESS_ID,	//商户id
//                        WLifeConstant.BRAND_ID,		//品牌id
//                        WLifeConstant.SHOP_ID			//门店id
//                };
//
//                List<JSONObject> values = wshPosEntranceDao.selectStoreParams(tenancyId, storeId, paraCodes);
                List<JSONObject> values = posGenericDao.getParam(tenancyId);
                JSONObject param = new JSONObject();
                for(JSONObject json : values){
                    param.put(json.optString("para_code"), json.optString("para_value", json.optString("para_defaut")));
                }
                if (!Tools.isNullOrEmpty(param)){
                    orderInfo.put("business_id", param.optString(WLifeConstant.BUSSINESS_ID));
                    orderInfo.put("brand_id", param.optString(WLifeConstant.BRAND_ID));
                    orderInfo.put("shop_id", param.optString(WLifeConstant.SHOP_ID));
                }
                orderInfo.put("shop_name", billObject.optString("shop_name"));

                setOrderInfo(orderInfo, billObject, tableCode, member);


                setOrderDiscountInfo(tenancyId,billObject,orderInfo);

                //设置订单的菜品信息
                setDish(tenancyId, orderInfo);


                data.put("order_info", orderInfo);
            }

            responseJson.put("success", 1);
            responseJson.put("msg", WlifePromptMessage.WLIFE_ORDER_SUCCESS);
            responseJson.put("data", data);
        }

        logger.info("微生活小程序查询桌台状态返回结果" + responseJson);
    }

    /**
     * 设置可折扣金额
     * @param tenancyId
     * @param billObject
     * @param orderInfo
     * @throws Exception
     */
    private void setOrderDiscountInfo(String tenancyId,JSONObject billObject,JSONObject orderInfo) throws Exception {
        Double discountAmountPay = this.posGenericDao.getBillItemAmountByDis(tenancyId, billObject.optString("oid"), "Y");
        Double total = billObject.optDouble("total");
        Double nonDiscountAmountPay = DoubleHelper.sub(total, discountAmountPay, 2);
        orderInfo.put("discountAmountPay", discountAmountPay);
        orderInfo.put("nonDiscountAmountPay", nonDiscountAmountPay);
    }
    /**
     * 查询平台订单的桌位等信息，处理结账，转台，并台未通知的情况
     * @param tenancyId
     * @param out_order_id
     * @param tableCode
     * @throws Exception
     */
    private void notifyWlifeProgram(String tenancyId, String out_order_id, String tableCode) throws Exception{
        if (!StringUtil.hasText(out_order_id)){
            //平台订单号为空，暂时不处理
        }else {
            JSONObject wlifeBill = posGenericDao.getWlifeBill(tenancyId, out_order_id);
            if (Tools.isNullOrEmpty(wlifeBill)){

            }else {
                String table = wlifeBill.optString("fictitious_table");
                int storeId = wlifeBill.optInt("store_id");
                String billNum = wlifeBill.optString("bill_num");
                //桌号一致，并且桌位已关台，调用结账通知
                if (tableCode.equals(table) && "CLOSED".equals(wlifeBill.optString("bill_property"))){
                    wlifeProgramService.completeOrder(tenancyId, storeId, billNum);
                }
                //桌号不一致，并且平台订单号查询的订单还未关台，调用转台通知
                if (!tableCode.equals(table) && "OPEN".equals(wlifeBill.optString("bill_property"))){
                    wlifeProgramService.changeTable(tenancyId, storeId, billNum, table);
                }
            }
        }
    }

    /**
     * 订单信息
     * @param orderInfo
     * @param billObject
     * @param tableCode
     */
    private void setOrderInfo(JSONObject orderInfo, JSONObject billObject, String tableCode, JSONObject member){
        orderInfo.put("oid", billObject.optString("oid"));
        orderInfo.put("identify", billObject.optString("out_order_id"));
        orderInfo.put("out_order_id", billObject.optString("out_order_id"));
        orderInfo.put("tableno", tableCode);
        orderInfo.put("ordermemo", new JSONObject());
        //orderInfo.put("ordermemo", billObject.optString("text"));
        //应收
        orderInfo.put("total", billObject.optDouble("total"));
        //实收
        orderInfo.put("cost", billObject.optDouble("cost"));
        orderInfo.put("people", billObject.optString("people"));
        orderInfo.put("mealfee", billObject.optString("mealfee"));
        //会员信息
        if (!Tools.isNullOrEmpty(member)){
            orderInfo.put("openid", member.optString("openid"));
            orderInfo.put("name", member.optString("name"));
            orderInfo.put("mobile", member.optString("mobile"));
            orderInfo.put("credit", member.optString("credit"));
            orderInfo.put("balance", member.optString("balance"));
            //会员等级
            JSONObject upgrade = new JSONObject();
            upgrade.put("cardnum", member.optString("cno"));
            upgrade.put("orginlevel", member.optString("grade"));
            orderInfo.put("upgrade", upgrade);
        }else {
            orderInfo.put("openid", "");
            orderInfo.put("name", "");
            orderInfo.put("mobile", "");
            orderInfo.put("credit", 0);
            orderInfo.put("balance", 0);
            orderInfo.put("upgrade", new JSONObject());
        }

        orderInfo.put("discount_money", billObject.optDouble("discount_money"));
        orderInfo.put("memberPrice", billObject.optDouble("memberprice",0));
        orderInfo.put("weiXinPay", billObject.optDouble("weixinpay", 0));

        //活动信息
        orderInfo.put("discount_info", new JSONObject());
    }

    /**
     * 设置订单中的菜品信息
     * @param orderObject
     */
    private void setDish(String tenancyId,JSONObject orderObject) throws Exception{
        //桌台的未结账的账单号
        String billNum = orderObject.optString("oid");
        //查询套餐菜品信息(可能一笔订单，多个套餐)
        List<JSONObject> setmealList = posGenericDao.getSetmeal(tenancyId, billNum);

        if(null == setmealList)
        {
            setmealList = new ArrayList<JSONObject>();
        }
        //查询套餐中的主菜和必选菜
        if (setmealList.size() > 0){
            for (int i=0; i < setmealList.size(); i++){

                //setmealList.get(i).put("bbuySno", "");
                //setmealList.get(i).put("bgiftSno", "");
                //可使用会员价的会员等级
                String [] membergid = (String[]) com.tzx.clientorder.wlifeprogram.common.constant.Constant.membergidMap.get(com.tzx.clientorder.wlifeprogram.common.constant.Constant.MEMBERGID);
                setmealList.get(i).put("membergid", membergid);

                //查询菜品做法
                List<JSONObject> cookList = posGenericDao.getCooks(tenancyId, setmealList.get(i).optInt("id"));
                setmealList.get(i).put("cooks", cookList);
                //查询套餐里的主菜信息
                //主菜里的数量，要除以套餐的数量。
                int stemealCount = setmealList.get(i).optInt("number", 1);
                //套餐id
                String setmealId = setmealList.get(i).optString("did");
                String setmeal_rwid = setmealList.get(i).optString("setmeal_rwid");
                List<JSONObject> mainList = posGenericDao.getSetmealDish(tenancyId, billNum, "main", stemealCount, setmealId, setmeal_rwid);
                if(null == mainList)
                {
                    mainList = new ArrayList<JSONObject>();
                }
                //查询套餐中的必选菜品
                List<JSONObject> mandatoryList = posGenericDao.getSetmealDish(tenancyId, billNum, "mandatory", stemealCount, setmealId, setmeal_rwid);
                if(null == mandatoryList)
                {
                    mandatoryList = new ArrayList<JSONObject>();
                }
                setmealList.get(i).put("maindish", mainList);
                setmealList.get(i).put("mandatory", mandatoryList);
                setmealList.get(i).put("optional", new JSONArray());

                setmealList.get(i).put("isWeigh", setmealList.get(i).optString("is_weigh"));
                setmealList.get(i).remove("is_weigh");
            }
        }
        //设置订单中的套餐信息
        orderObject.put("setmeal", setmealList);

        //非套餐菜品的查询
        List<JSONObject> normalList = posGenericDao.getNormalitems(tenancyId, billNum);
        if (null != normalList && normalList.size() > 0){
            for (int i=0; i < normalList.size(); i++){
                normalList.get(i).put("bbuySno", "");
                normalList.get(i).put("bgiftSno", "");
                //可使用会员价的会员等级
                String [] membergid = (String[]) com.tzx.clientorder.wlifeprogram.common.constant.Constant.membergidMap.get(com.tzx.clientorder.wlifeprogram.common.constant.Constant.MEMBERGID);
                normalList.get(i).put("membergid", membergid);
                //查询菜品做法
                List<JSONObject> cookList = posGenericDao.getCooks(tenancyId, normalList.get(i).optInt("id"));
                normalList.get(i).put("cooks", cookList);

                normalList.get(i).put("centPrice", normalList.get(i).optDouble("price",0) * 100);

                normalList.get(i).put("isWeigh", normalList.get(i).optString("is_weigh"));
                normalList.get(i).remove("is_weigh");

                //菜品是否可使用菜品券
                normalList.get(i).put("cpqflag", false);
            }
            orderObject.put("normalitems", normalList);
        }else {
            orderObject.put("normalitems", new JSONArray());
        }

    }
}
