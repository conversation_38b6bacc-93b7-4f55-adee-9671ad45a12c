package com.tzx.pos.base.constant;

/**
 * 
 * 操作编号常量
 * 
 * <AUTHOR>
 * 
 *
 */
public class FunctionCode
{
	/**
	 * 下单function编号
	 */
	public static final String	ORDERING				= "ORDERING";

	/**
	 * 结账function编号
	 */
	public static final String	BILL_PAYMENT			= "BILL_PAYMENT";

	/**
	 * 退菜
	 */
	public static final String	RETREAT_FOOD_SELF		= "RETREAT_FOOD";

	/**
	 * 整单取消
	 */
	public static final String	CANCBILL				= "CANCBILL";

	/**
	 * 单品退菜
	 */
	public static final String	RETREAT_ITEM			= "RETREAT_ITEM";

	/**
	 * 催菜
	 */
	public static final String	REMINDER_FOOD			= "REMINDER_FOOD";

	/**
	 * 转台
	 */
	public static final String	CHANGE_TABLE			= "CHANGE_TABLE";

	/**
	 * 单品转台
	 */
	public static final String	SINGLE_CHANGE_TABLE		= "SINGLE_CHANGE_TABLE";

	/**
	 * 起菜
	 */
	public static final String	CALLUP_ITEM				= "CALLUP_ITEM";

    /**
     * 零找金
     */
	public static final String CHANGET                  = "CHANGET";

	/**
	 * 交班
	 */
	public static final String	CHANG_SHIFT				= "CHANG_SHIFT";

	/**
	 * 交款
	 */
	public static final String	POS_CASHIER_RECEIVE		= "CASHIER_RECEIVE";

	/**
	 * 会员发卡
	 */
	public static final String	CARD_ACTIVATION			= "CARD_ACTIVATION";
	
	/**
	 * 会员充值
	 */
	public static final String	CARD_RECHARGE			= "CARD_RECHARGE";
	
	/**
	 * 会员反充值
	 */
	public static final String	CARD_RECHARGE_CANCEL	= "CARD_RECHARGE_CANCEL";
	
	/**
	 * 会员消费
	 */
	public static final String	CARD_CONSUME			= "CARD_CONSUME";
	
	/**
	 * 会员反消费
	 */
	public static final String	CARD_CONSUME_CANCEL		= "CARD_CONSUME_CANCEL";
	
	/**
	 * 会员退卡
	 */
	public static final String	CARD_BACK_OUT			= "CARD_BACK_OUT";
	
	/**
	 * 会员补卡
	 */
	public static final String	CUSTOMER_CARD_FILL_CARD	  = "CUSTOMER_CARD_FILL_CARD";
	
	/**
	 * 外卖单
	 */
	public static final String	TAKEAWAY_ORDER			= "TAKEAWAY_ORDER";
	
	/**
	 * 取消外卖单
	 */
	public static final String	TAKEAWAY_ORDER_CANCEL		= "TAKEAWAY_ORDER_CANCEL";
}
