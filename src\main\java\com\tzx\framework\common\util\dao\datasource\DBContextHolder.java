package com.tzx.framework.common.util.dao.datasource;

/**
 * <AUTHOR>
 * @descrice 多个登录用户可能需要同时切换数据源，所以这里需要写一个线程安全的ThreadLocal
 * @more 用户切换数据源只要在程序中使用 DBContextHolder.setDBType("1") 即可完成数据源切换
 */
public class DBContextHolder
{
	private static final ThreadLocal<String>	contextHolder	= new ThreadLocal<String>();
	
	private static final ThreadLocal<String>	truetenancyid	= new ThreadLocal<String>();

	
	public static void setTenancyid(String tenancyid)
	{
		contextHolder.set(tenancyid);
	}

	public static String getTenancyid()
	{
		return contextHolder.get();
	}

	public static void clearDBType()
	{
		contextHolder.remove();
	}
	public static String getTTenancyid()
	{
		return truetenancyid.get()+"";
	}
	public static void setTTenancyid(String vg)
	{
		truetenancyid.set(vg);
	}

}
