package com.tzx.estate.base.dao;

import java.util.List;

import net.sf.json.JSONObject;

import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.support.rowset.SqlRowSet;

import com.tzx.framework.common.util.dao.GenericDao;

/**
 * 
 * <AUTHOR>
 * @日期：2015年5月26日-下午5:32:41
 */

public interface BaseDao extends GenericDao
{
//
//	/**
//	 * 加这个方法的原是：两次转JSONObject，后台没有错，返回给andriod或delphi那边是404
//	 * 
//	 * @param tenantId
//	 * @param sql
//	 * @return
//	 * @throws Exception
//	 */
//	public List<JSONObject> queryString4Json(String tenantId, String sql, Object[] objs) throws Exception;
//	
//	public SqlRowSet query4SqlRowSet(String sql, Object[] obj);
//
//	public SqlRowSet query4SqlRowSet(String sql);
//
//	public int queryForInt(String sql, Object[] obj);
//
//	public int update(String sql, Object[] obj);
//
//	public List<?> query(String sql, Object[] obj, BeanPropertyRowMapper<?> className);
//	
//	public int[] batchUpdate(String sql,List<Object[]> batchArgs) throws Exception;
}
