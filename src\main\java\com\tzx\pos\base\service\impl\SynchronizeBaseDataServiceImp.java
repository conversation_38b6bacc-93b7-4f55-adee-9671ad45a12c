package com.tzx.pos.base.service.impl;

import com.tzx.base.common.util.PosPropertyUtil;
import com.tzx.framework.common.exception.HttpRestException;
import com.tzx.framework.common.util.HttpUtil;
import com.tzx.orders.base.util.OrderUtil;
import com.tzx.pos.base.service.SynchronizeBaseDataService;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import java.io.File;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> on 2017-11-23
 */
public class SynchronizeBaseDataServiceImp implements SynchronizeBaseDataService {

    private final String SAASPOST_URL = "/rest/hq/post";
    private  Logger tip= Logger.getLogger("tip");
    private final int maxRetryCount =10;

    /**
     * 请求下发终端设备相关资料
     *
     * @param type
     * @return
     */
    @Override
    public boolean syncData(Type type) {
        return  syncData(type,PosPropertyUtil.getMsg("saas.url"));
    }

    @Override
    public void syncData(Type organ, String saasUrl, String autoSynchronize) {
        String lockPath = System.getProperty("catalina.home") + "/sync.lock";
        File file= FileUtils.getFile(lockPath);

        String tenent_id = PosPropertyUtil.loadPosSystemProperties("tenent_id");
        String store_id = PosPropertyUtil.loadPosSystemProperties("store_id");



        /*if("yes".equals(autoSynchronize)||file.exists()){
            syncData(organ,saasUrl) ;
        }*/

        if("1".equals(OrderUtil.getSysPara(tenent_id,Integer.valueOf(store_id),"auto_synchronize_on_start"))||file.exists()){
            syncData(organ,saasUrl) ;
        }

        if(file.exists()) {
            try {
                FileUtils.forceDelete(file);
            } catch (IOException e) {
                tip.warn("请手动删除"+lockPath+"文件",e);
            }
        }
    }

    @Override
    public boolean syncData(Type type, String saasUrl) {

        if(StringUtils.isEmpty(saasUrl)){
            saasUrl="http://www.e7e6.net";
        }

        String tenent_id = PosPropertyUtil.loadPosSystemProperties("tenent_id");
        String store_id = PosPropertyUtil.loadPosSystemProperties("store_id");

        JSONObject postParam = new JSONObject();
        postParam.put("type", "DATA_DELIVERY");
        postParam.put("oper", "data_delivery_by_service_type");
        postParam.put("store_id", store_id);
        postParam.put("tenancy_id", tenent_id);
        JSONArray datalist = new JSONArray();
        JSONObject data = new JSONObject();
        //下发所有初始化资料organ_management   下发终端设备相关资料terminalequipment_management
        String moduleCode = "terminalequipment_management";
        switch (type) {
            case organ:
                moduleCode = "organ_management";
                break;
            case terminal:
                moduleCode = "terminalequipment_management";
                break;
            default:

        }
        data.put("module_code", moduleCode);
        datalist.add(data);
        postParam.put("data", datalist);

        int retry=0;
        while (retry<maxRetryCount){
            Exception e=null;
            if(null==(e=sendPostRequest(saasUrl + SAASPOST_URL,postParam))){
                tip.info("请求下发资料成功");
                return true;
            }else{
                retry++;
                tip.warn("请求下发资料第" + retry + "次失败" + "[" + e.getMessage() + "]" + (retry == maxRetryCount ? ". 请尝试手动下发!" : "..."));
                try {
                    TimeUnit.SECONDS.sleep(retry*3);
                } catch (InterruptedException e1) {
                    e1.printStackTrace();
                }
            }
        }
        return false;
    }




    private Exception sendPostRequest(String url,JSONObject param){
        try {
            JSONObject result=   HttpUtil.post(url, param,1,true);
            if(result.optBoolean("success")){
                return null;
            }else {
                throw new Exception(result.optString("msg"));
            }

        } catch (HttpRestException httpExce) {
            return httpExce;
        } catch (Exception e1) {
            return e1;
        }
    }

}
