/** 
 * @(#)CodeFormat.java    1.0   2017-10-08
 * 
 * Copyright 北京普照天星科技有限公司. All rights reserved. 
 * PROPRIETARY/CONFIDENTIAL. Use is subject to license terms. 
 */
package com.tzx.base.scoket;

import java.net.URI;
import java.util.Map;
import java.util.UUID;

import org.java_websocket.drafts.Draft_6455;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.tzx.base.common.util.PosPropertyUtil;
import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.util.Tools;

/**
 * webSocket连接线程类，包括重连和心跳。
 * 
 * <AUTHOR> email:she<PERSON><PERSON><PERSON>@tzx.com.cn
 * @version 1.0 2017-10-09
 * @see
 * @since JDK7.0
 * @update
 */
public class WebSocketConnectionThread implements Runnable{
	private final static String MSG_PING = "PING";
	private final static String MSG_RETURN = "success";
	
	private final static String SECRET_KEY = "pYjACwhTwFPj661m";
	private static final Logger LOGGER = LoggerFactory.getLogger(WebSocketConnectionThread.class);

	@Override
	public void run() {
		Map<String, String> systemMap = Constant.getSystemMap();
		if (!systemMap.containsKey("tenent_id") || !systemMap.containsKey("store_id")
				|| "".equals(systemMap.get("tenent_id")) || "".equals(systemMap.get("store_id"))) {
			LOGGER.info("参数为空");
			return;
		}
		String tenentId = systemMap.get("tenent_id");
		String storeId = systemMap.get("store_id");
		String url = PosPropertyUtil.getMsg("websocket.url"); // 连接地址
		String waitTime = PosPropertyUtil.getMsg("websocket.wait"); // 等待时间
		String heartbeatTime = PosPropertyUtil.getMsg("websocket.heartbeattime");// 心跳间隔时间
		
		if(Tools.isNullOrEmpty(url))
		{
			LOGGER.info("参数websocket.url为空");
			return;
		}
		
		if(Tools.isNullOrEmpty(heartbeatTime))
		{
			heartbeatTime = "3000";
		}

		WebSocketClientHandler c = null;
		try {
			c = new WebSocketClientHandler(
					new URI("ws://" + url + "?deviceId=" + tenentId + "_" + storeId + "&secretKey="+SECRET_KEY),
					new Draft_6455());
			LOGGER.info("ws://" + url + "?deviceId=" + tenentId + "_" + storeId + "&secretKey="+SECRET_KEY);
			c.connect();
			LOGGER.info("创建websocket连接...");
			Thread.sleep(Integer.parseInt(waitTime)); // 休眠指定秒数，防止创建多个websocket连接。
		} catch (Exception e) {
			LOGGER.info("创建websocket连接失败...");
			LOGGER.error(e.getMessage(), e);
		}

		while (true) {
			try {
				/*
				 * 向消息推送平台发送ping指令，如果发送3次都失败，则重新创建websocket连接。
				 */
				boolean retryBo = true;
				if (c == null) {
					LOGGER.info("WebSocketClientHandler对象为null");
					retryBo = true;
				} else {
					// ping重试次数
					int pingNum = 3;
					for (int i = 0; i < pingNum; i++) {
						try {
							String uuid = UUID.randomUUID().toString();
							c.send(MSG_PING + "_" + uuid);
							String pingReturn = getMsgResult(uuid);
							LOGGER.info("PING返回结果："+pingReturn + ",uuid:"+uuid);
							if (pingReturn.equals(MSG_RETURN)) {
								retryBo = false;
								break;
							}
						} catch (Exception ec) {
							LOGGER.debug(ec.getMessage());
						}
					}
				}

				/*
				 * 多次ping都不能返回结果，则启动重新创建websocket连接。
				 */
				Thread.sleep(Integer.parseInt(heartbeatTime));
				if (retryBo) {
					LOGGER.info("重新创建websocket连接...");
                    try
                    {
                        if(c != null)
                        {
                            c.close();
                            LOGGER.info("websocket连接关闭");
                        }
                    }
                    catch (Exception e1)
                    {
                        LOGGER.error("关闭websocket连接异常.....", e1);
                    }

					c = new WebSocketClientHandler(new URI(
							"ws://" + url + "?deviceId=" + tenentId + "_" + storeId + "&secretKey="+SECRET_KEY),
							new Draft_6455());
					LOGGER.info("ws://" + url + "?deviceId=" + tenentId + "_" + storeId + "&secretKey="+SECRET_KEY);
					c.connect();
					Thread.sleep(Integer.parseInt(heartbeatTime));
				}				
			} catch (Exception e) {
				LOGGER.debug(e.getMessage(),e);
			}
		}
	}
	
	/**
	 * @Description 检测消息推送返回结果
	 * 过期时间：4秒
	 * 检测频率：50毫秒
	 * @param  msgKey   
	 * @return 
	 * <AUTHOR> email:<EMAIL>
	 * @version 1.0  2017-09-13
	 * @see
	 */
	private String getMsgResult(String msgKey){	
		Object obj = null;
		for(int i =0;i<48;i++){
			obj = Constant.msgMap.get(msgKey);
			if(obj != null){
				Constant.msgMap.remove(msgKey);
				return obj.toString();
			}
			try {
				// 休眠50毫秒
				Thread.sleep(50);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
		}
		return "";
	}
}
