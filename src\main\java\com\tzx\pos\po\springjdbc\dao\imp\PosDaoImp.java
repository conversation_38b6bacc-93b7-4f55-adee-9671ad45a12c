package com.tzx.pos.po.springjdbc.dao.imp;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.tzx.framework.common.constant.Oper;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Repository;

import com.tzx.base.bo.PosCodeService;
import com.tzx.base.cache.CacheManager;
import com.tzx.base.constant.CacheTableConstant;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.entity.Pagination;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.EhcacheUtil;
import com.tzx.framework.common.util.JavaMd5;
import com.tzx.framework.common.util.PropertiesLoader;
import com.tzx.framework.common.util.Tools;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.BasicData;
import com.tzx.pos.base.constant.FunctionCode;
import com.tzx.pos.base.constant.PrinterSerialNumber;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.dao.imp.BaseDaoImp;
import com.tzx.pos.base.util.CacheTableUtil;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.base.util.ReqDataUtil;
import com.tzx.pos.bo.dto.BasicDataDiscountCase;
import com.tzx.pos.bo.dto.BasicDataDish;
import com.tzx.pos.bo.dto.BasicDataServiceFeeType;
import com.tzx.pos.bo.dto.BasicDataUnusualReason;
import com.tzx.pos.bo.dto.BasicDataUserDiscountAuthority;
import com.tzx.pos.bo.dto.BasicDataUserDiscountCase;
import com.tzx.pos.bo.dto.BasicDevice;
import com.tzx.pos.bo.dto.ItemGroup;
import com.tzx.pos.bo.dto.PosBillMembers;
import com.tzx.pos.bo.dto.PosChangShift;
import com.tzx.pos.bo.dto.PosItemSort;
import com.tzx.pos.bo.dto.PosLog;
import com.tzx.pos.bo.dto.PosSysParam;
import com.tzx.pos.bo.dto.PosTableFind;
import com.tzx.pos.bo.dto.PosTableInfo;
import com.tzx.pos.po.springjdbc.dao.PosDao;


@Repository(PosDao.NAME)
public class PosDaoImp extends BaseDaoImp implements PosDao {
	private static final Logger logger = Logger.getLogger(PosDaoImp.class);

	@Override
	public List<JSONObject> getDutyOrderByOrganId(String tenantId, Integer organId) throws Exception {
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject(); // data数组里的对象

		StringBuilder sql = new StringBuilder(" select dor.id, sd.class_item as name,dor.start_time,dor.end_time,dor.start_property,dor.end_property from duty_order as dor ");
		sql.append(" left JOIN duty_order_of_ogran as doo on dor.id = doo.duty_order_id left join sys_dictionary sd on dor.name = sd.class_item_code and sd.class_identifier_code = 'duty' ");
		sql.append(" where dor.valid_state = '1' and doo.organ_id = " + organId);
		sql.append(" group by dor.id,sd.class_item,dor.start_time,dor.end_time order by dor.id asc ");
		logger.debug("getDutyOrderByOrganId:::" + sql.toString());

		List<JSONObject> dutyOrders = this.queryString4Json(tenantId, sql.toString());
		if (dutyOrders != null) {
			JSONArray dutyOrderArray = JSONArray.fromObject(dutyOrders);
			object.element("duty_order", dutyOrderArray);
		}
		list.add(object);
		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return list;
	}

	//	private static final ReentrantLock lock = new ReentrantLock(); //同步类 用lock()加锁，用unlock()解锁
	@Override
	public String getDutyOrderByOrganIdEx(String tenantId, Integer organId) throws Exception {
		StringBuilder sql = new StringBuilder(" select dor.id, sd.class_item as name,dor.start_time,dor.end_time,dor.start_property,dor.end_property from duty_order as dor ");
		sql.append(" left JOIN duty_order_of_ogran as doo on dor.id = doo.duty_order_id left join sys_dictionary sd on dor.name = sd.class_item_code and sd.class_identifier_code = 'duty' ");
		sql.append(" where dor.valid_state = '1' and doo.organ_id = " + organId);
		sql.append(" group by dor.id,sd.class_item,dor.start_time,dor.end_time order by dor.id asc ");
		logger.debug("getDutyOrderByOrganId:::" + sql.toString());

		List<Map<String, Object>> dishes = this.jdbcTemplate.queryForList(sql.toString());
		String tmp = org.apache.commons.lang.StringUtils.join(dishes.toArray(), "|");
		return JavaMd5.toMd5B32(tmp);
	}

	@Override
	public List<JSONObject> getTablePropertyByOrganId(String tenantId, Integer organId) throws Exception {
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject(); // data数组里的对象

		StringBuilder sql = new StringBuilder("select ti.table_property_id,sd.class_item_code,sd.class_item as table_property_name from tables_info as ti ");
		sql.append(" left join sys_dictionary as sd on ti.table_property_id = sd.id and sd.class_identifier_code = 'table_property' where ti.valid_state = '1' and ti.organ_id = ");
		sql.append(organId + " group by table_property_id,class_item_code,table_property_name ");
		sql.append(" order by ti.table_property_id asc ");

		logger.debug("getTablePropertyByOrganId:::" + sql.toString());

		List<JSONObject> tableProperties = this.queryString4Json(tenantId, sql.toString());
		if (tableProperties != null) {
			JSONArray tablePropertyArray = JSONArray.fromObject(tableProperties);
			object.element("table_property", tablePropertyArray);
		}
		list.add(object);

		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return list;
	}

	@Override
	public String getTablePropertyByOrganIdEx(String tenantId, Integer organId) throws Exception {
		StringBuilder sql = new StringBuilder("select ti.table_property_id,sd.class_item_code,sd.class_item as table_property_name from tables_info as ti ");
		sql.append(" left join sys_dictionary as sd on ti.table_property_id = sd.id and sd.class_identifier_code = 'table_property' where ti.valid_state = '1' and ti.organ_id = ");
		sql.append(organId + " group by table_property_id,class_item_code,table_property_name ");
		sql.append(" order by ti.table_property_id asc ");
		logger.debug("getTablePropertyByOrganId:::" + sql.toString());

		List<Map<String, Object>> dishes = this.jdbcTemplate.queryForList(sql.toString());
		String tmp = org.apache.commons.lang.StringUtils.join(dishes.toArray(), "|");
		return JavaMd5.toMd5B32(tmp);
	}

	@Override
	public List<JSONObject> getBussinessArea(String tenantId, Integer organId) throws Exception {
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject(); // data数组里的对象

		StringBuilder sql = new StringBuilder("select ti.business_area_id,sd.class_item_code,sd.class_item as business_area_name from tables_info as ti ");
		sql.append(" left join sys_dictionary as sd on ti.business_area_id = sd.id and sd.class_identifier_code='business_area' where ti.valid_state = '1' and ti.organ_id = ");
		sql.append(organId + " group by business_area_id,class_item_code,business_area_name");
		sql.append(" order by ti.business_area_id asc ");
		logger.debug("getBussinessArea:::" + sql.toString());

		List<JSONObject> businessAreas = this.queryString4Json(tenantId, sql.toString());
		if (businessAreas != null) {
			JSONArray busiAreaArray = JSONArray.fromObject(businessAreas);
			object.element("business_area", busiAreaArray);
		}
		list.add(object);

		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return list;
	}

	@Override
	public String getBussinessAreaEx(String tenantId, Integer organId) throws Exception {
		StringBuilder sql = new StringBuilder("select ti.business_area_id,sd.class_item_code,sd.class_item as business_area_name from tables_info as ti ");
		sql.append(" left join sys_dictionary as sd on ti.business_area_id = sd.id and sd.class_identifier_code='business_area' where ti.valid_state = '1' and ti.organ_id = ");
		sql.append(organId + " group by business_area_id,class_item_code,business_area_name");
		sql.append(" order by ti.business_area_id asc ");
		logger.debug("getBussinessArea:::" + sql.toString());

		List<Map<String, Object>> dishes = this.jdbcTemplate.queryForList(sql.toString());
		String tmp = org.apache.commons.lang.StringUtils.join(dishes.toArray(), "|");
		return JavaMd5.toMd5B32(tmp);
	}

	@Override
	public List<JSONObject> getItemClassByTenantId(String tenantId, Integer organId, String channel) throws Exception {
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject(); // data数组里的对象

		StringBuilder sql1 = new StringBuilder("select distinct hic.id,hic.chanel,hic.father_id,trim(hic.five_code) as five_code,trim(hic.phonetic_code) as phonetic_code,hic.itemclass_code,hic.itemclass_name, coalesce(himco.classorder,0) as classorder,hic.hotpot_property,hic.is_must_dish from hq_item_menu_class himc ");
		sql1.append(" left join hq_item_menu_details as himd on himc.details_id = himd.id left join hq_item_menu_organ himo on himd.item_menu_id = himo.item_menu_id left join hq_item_class hic on himc.class = hic.id  ");
		sql1.append(" left join hq_item_menu_classorder himco on himco.menu_id = himo.item_menu_id and himco.class_id = hic.id ");
		sql1.append(" where hic.valid_state = '1' and himd.valid_state='1' and himo.store_id = " + organId);
		if (Tools.hv(channel)) {
			sql1.append(" and hic.chanel = '" + channel + "' ");
		}

		List<JSONObject> list1 = this.queryString4Json(tenantId, sql1.toString());
		if (list1.size() > 0) {
			StringBuilder sql2 = new StringBuilder("select hic.id,hic.chanel,hic.father_id,trim(hic.five_code) as five_code,trim(hic.phonetic_code) as phonetic_code,hic.itemclass_code,hic.itemclass_name,0 as classorder,hic.hotpot_property,hic.is_must_dish from hq_item_class hic where hic.valid_state='1' and hic.id in (");
			for (int i = 0; i < list1.size(); i++) {
				JSONObject obj = list1.get(i);
				sql2.append(obj.optInt("father_id"));
				if (i != list1.size() - 1) {
					sql2.append(",");
				}
			}
			sql2.append(")");
			//去重，由于客户石碧东那边的序列重新计算，新增的大类和以前的数据有重
			List<JSONObject> list2 = this.queryString4Json(tenantId, sql2.toString());
			for (int j = 0; j < list2.size(); j++) {
				JSONObject obj2 = list2.get(j);
				for (int m = 0; m < list1.size(); m++) {
					if (obj2.optInt("id") == list1.get(m).optInt("id")) {
						break;
					} else {
						continue;
					}
				}
				list1.add(list2.get(j));
			}

			logger.debug("getItemClassByTenantId:::" + sql1.toString());
		}

		JSONArray itemClassArray = JSONArray.fromObject(list1);
		object.element("item_class", itemClassArray);
		list.add(object);

		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return list;
	}

	@Override
	public String getItemClassByTenantIdEx(String tenantId, Integer organId) throws Exception {
		/*List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject(); // data数组里的对象
		
		StringBuilder sql1 = new StringBuilder("select distinct hic.id,hic.chanel,hic.father_id,trim(hic.five_code) as five_code,trim(hic.phonetic_code) as phonetic_code,hic.itemclass_code,hic.itemclass_name, coalesce(himco.classorder,0) as classorder from hq_item_menu_class himc ");
		sql1.append(" left join hq_item_menu_details as himd on himc.details_id = himd.id left join hq_item_menu_organ himo on himd.item_menu_id = himo.item_menu_id left join hq_item_class hic on himc.class = hic.id  ");
		sql1.append(" left join hq_item_menu_classorder himco on himco.menu_id = himo.item_menu_id and himco.class_id = hic.id ");
		sql1.append(" where hic.valid_state = '1' and himd.valid_state='1' and himo.store_id = " + organId + " ");
		
		List<JSONObject> list1 = this.queryString4Json(tenantId, sql1.toString());
		if (list1.size() > 0)
		{
			StringBuilder sql2 = new StringBuilder("select hic.id,hic.chanel,hic.father_id,trim(hic.five_code) as five_code,trim(hic.phonetic_code) as phonetic_code,hic.itemclass_code,hic.itemclass_name,0 as classorder from hq_item_class hic where hic.valid_state='1' and hic.id in (");
			for (int i=0; i<list1.size(); i++)
			{
				JSONObject obj = list1.get(i);
				sql2.append(obj.optInt("father_id"));
				if (i != list1.size() - 1)
				{
					sql2.append(",");
				}
			}
			sql2.append(")");
			List<JSONObject> list2 = this.queryString4Json(tenantId, sql2.toString());
			for (int j=0; j<list2.size(); j++)
			{
				JSONObject obj2 = list2.get(j);
				for (int m=0; m<list1.size(); m++)
				{
					if (obj2.optInt("id")== list1.get(m).optInt("id"))
					{
						break;
					}else
					{
						continue;
					}
				}
				list1.add(list2.get(j));
			}
			
			logger.debug("getItemClassByTenantId:::" + sql1.toString());
		}
		
		JSONArray itemClassArray = JSONArray.fromObject(list1);
		object.element("item_class", itemClassArray);
		list.add(object);*/

		List<JSONObject> list = getItemClassByTenantId(tenantId, organId, null);

//		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return JavaMd5.toMd5B32(list.toString());
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<JSONObject> getServiceFeeTypeByOrganId(String tenantId, Integer organId) throws Exception {
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject(); // data数组里的对象

		StringBuilder sql = new StringBuilder("select hsft.id,hsft.service_type_code,hsft.name,hsft.guding_jj,hsft.zuidi_xfe,hsft.fwfl,hsft.taken_mode,hsft.ismodify,hsft.fee_type,hsft.valid_state ");
		sql.append(" from hq_service_fee_type as hsft ");
		sql.append(" left join hq_service_fee_of_organ as hsfoo on hsft.id = hsfoo.service_fee_id ");
		sql.append(" where hsft.valid_state = '1' and hsfoo.store_id = " + organId);
		sql.append(" group by hsft.id,hsft.service_type_code,hsft.name,hsft.guding_jj,hsft.zuidi_xfe,hsft.fwfl,hsft.taken_mode,hsft.ismodify,hsft.fee_type,hsft.valid_state order by hsft.id asc ");
		logger.debug("getServiceFeeTypeByOrganId:::" + sql.toString());

		List<JSONObject> serviceFeeTypes = (List<JSONObject>) this.query(tenantId, sql.toString(), BasicDataServiceFeeType.class);
		if (serviceFeeTypes != null) {
			JSONArray serviceFeeArray = JSONArray.fromObject(serviceFeeTypes);
			object.element("service_type", serviceFeeArray);
		}
		list.add(object);

		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return list;
	}

	@Override
	public String getServiceFeeTypeByOrganIdEx(String tenantId, Integer organId) throws Exception {
		StringBuilder sql = new StringBuilder("select hsft.id,hsft.service_type_code,hsft.name,hsft.guding_jj,hsft.zuidi_xfe,hsft.fwfl,hsft.taken_mode,hsft.ismodify,hsft.fee_type,hsft.valid_state ");
		sql.append(" from hq_service_fee_type as hsft ");
		sql.append(" left join hq_service_fee_of_organ as hsfoo on hsft.id = hsfoo.service_fee_id ");
		sql.append(" where hsft.valid_state = '1' and hsfoo.store_id = " + organId);
		sql.append(" group by hsft.id,hsft.service_type_code,hsft.name,hsft.guding_jj,hsft.zuidi_xfe,hsft.fwfl,hsft.taken_mode,hsft.ismodify,hsft.fee_type,hsft.valid_state order by hsft.id asc ");
		logger.debug("getServiceFeeTypeByOrganId:::" + sql.toString());

		List<Map<String, Object>> dishes = this.jdbcTemplate.queryForList(sql.toString());
		String tmp = org.apache.commons.lang.StringUtils.join(dishes.toArray(), "|");
		return JavaMd5.toMd5B32(tmp);
	}

	@Override
	public List<JSONObject> getItemTastes(String tenantId, Integer organId) throws Exception {
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject(); // data数组里的对象

		StringBuilder sql = new StringBuilder("select distinct(it.id) id,it.code,it.name,it.pinyin_sy,it.father_id,it.third_code from item_taste it left join item_taste_org ito on it.id = ito.teste_id");
		sql.append(" where (it.valid_state = '1' and ito.store_id =?) or (it.father_id = 0 and it.valid_state = '1') order by it.father_id");

		logger.debug("getItemTastes:::" + sql.toString());

		List<JSONObject> itemTastes = this.queryString4Json(tenantId, sql.toString(), new Object[]{organId});
		if (itemTastes != null) {
			JSONArray itemTasteArray = JSONArray.fromObject(itemTastes);
			object.element("taste", itemTasteArray);
		}
		list.add(object);

		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return list;
	}

	@Override
	public String getItemTastesEx(String tenantId, Integer organId) throws Exception {
		StringBuilder sql = new StringBuilder("select distinct(it.id) id,it.code,it.name,it.pinyin_sy,it.father_id,it.third_code from item_taste it left join item_taste_org ito on it.id = ito.teste_id");
		sql.append(" where (it.valid_state = '1' and ito.store_id =?) or (it.father_id = 0 and it.valid_state = '1') order by it.father_id");

		List<Map<String, Object>> dishes = this.jdbcTemplate.queryForList(sql.toString(), new Object[]{organId});
		String tmp = org.apache.commons.lang.StringUtils.join(dishes.toArray(), "|");
		return JavaMd5.toMd5B32(tmp);
	}

	@Override
	public List<JSONObject> getItemMethodsByTenantId(String tenantId, Integer organId) throws Exception {
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject(); // data数组里的对象

		StringBuilder sql = new StringBuilder("select distinct hime.id,hime.item_id,sd.class_item as method_name,hime.makeup_way,hime.proportion_money,sd.class_item_code as third_code");
		sql.append(" from hq_item_method hime");
		sql.append(" left join sys_dictionary as sd on hime.method_name_id=sd.id");
		sql.append(" left join hq_item_menu_details himd on hime.item_id=himd.item_id");
		sql.append(" left join hq_item_menu him on himd.item_menu_id = him.id");
		sql.append(" left join hq_item_menu_organ himo on him.id = himo.item_menu_id");
		sql.append(" where himo.tenancy_id=? and himo.store_id=? and him.valid_state='1' and hime.valid_state='1' and sd.class_identifier_code='method' order by hime.id asc ");

		logger.debug("getItemMethodsByTenantId:::" + sql.toString());

		List<JSONObject> itemMethods = this.queryString4Json(tenantId, sql.toString(), new Object[]{tenantId, organId});
		if (itemMethods != null) {
			JSONArray itemMethodArray = JSONArray.fromObject(itemMethods);
			object.element("method", itemMethodArray);
		}
		list.add(object);

		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return list;
	}

	@Override
	public String getItemMethodsByTenantIdEx(String tenantId, Integer organId) throws Exception {
		StringBuilder sql = new StringBuilder("select distinct hime.id,hime.item_id,sd.class_item as method_name,hime.makeup_way,hime.proportion_money,sd.class_item_code as third_code");
		sql.append(" from hq_item_method hime");
		sql.append(" left join sys_dictionary as sd on hime.method_name_id=sd.id");
		sql.append(" left join hq_item_menu_details himd on hime.item_id=himd.item_id");
		sql.append(" left join hq_item_menu him on himd.item_menu_id = him.id");
		sql.append(" left join hq_item_menu_organ himo on him.id = himo.item_menu_id");
		sql.append(" where himo.tenancy_id=? and himo.store_id=? and him.valid_state='1' and hime.valid_state='1' and sd.class_identifier_code='method' order by hime.id asc ");

		logger.debug("getItemMethodsByTenantId:::" + sql.toString());

		List<Map<String, Object>> dishes = this.jdbcTemplate.queryForList(sql.toString(), new Object[]{tenantId, organId});
		String tmp = org.apache.commons.lang.StringUtils.join(dishes.toArray(), "|");
		return JavaMd5.toMd5B32(tmp);
	}

//	@SuppressWarnings("unchecked")
//	@Override
//	public List<JSONObject> getUnusualReasonByTenantId(String tenantId,Integer organId) throws Exception
//	{
//		List<JSONObject> list = new ArrayList<JSONObject>();
//		JSONObject object = new JSONObject(); // data数组里的对象
//		
//		StringBuilder sql = new StringBuilder("select k.id,k.unusual_type,k.father_id, k.reason_name, k.phonetic_code, k.five_code, k.remark,k.valid_state from ");
//		sql.append(" (SELECT huur.id,huur.unusual_type,huur.father_id,huur.reason_name,huur.phonetic_code,huur.five_code,huur.remark,huur.valid_state FROM hq_unusual_reason as huur where huur.father_id = 0 union ");
//		sql.append(" SELECT DISTINCT(hur.id) id,hur.unusual_type,hur.father_id,hur.reason_name,hur.phonetic_code,hur.five_code,hur.remark,hur.valid_state FROM hq_unusual_reason as hur left join hq_unusual_reason_org as huro on huro.unusual_reason_id = hur.id ");
//		sql.append(" where hur.valid_state = '1' and huro.store_id = " + organId + " ) k order by k.father_id asc ");
//		
//		logger.debug("getUnusualReasonByTenantId:::" + sql.toString());
//		
//		List<JSONObject> reasons = (List<JSONObject>) this.query(tenantId, sql.toString(),BasicDataUnusualReason.class);
//		if (reasons != null)
//		{
//			JSONArray reasonArray = JSONArray.fromObject(reasons);
//			object.element("reason", reasonArray);
//		}
//		list.add(object);
//		
//		logger.debug("同步数据，查询返回数据:---" + object.toString());
//		return list;
//	}

	@SuppressWarnings("unchecked")
	@Override
	public List<JSONObject> getUnusualReasonByTenantId(String tenantId, Integer organId) throws Exception {
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject(); // data数组里的对象

		StringBuilder sql = new StringBuilder("select k.id,k.reason_code,k.unusual_type,k.father_id, k.reason_name, k.phonetic_code, k.five_code, k.remark,k.valid_state,k.third_code from ");
		sql.append(" (SELECT huur.id,huur.reason_code,huur.unusual_type,huur.father_id,huur.reason_name,huur.phonetic_code,huur.five_code,huur.remark,huur.valid_state,huur.third_code FROM hq_unusual_reason as huur where huur.father_id = 0 union ");
		sql.append(" SELECT DISTINCT(hur.id) id,hur.reason_code,hur.unusual_type,hur.father_id,hur.reason_name,hur.phonetic_code,hur.five_code,hur.remark,hur.valid_state,hur.third_code FROM hq_unusual_reason as hur left join hq_unusual_reason_org as huro on huro.unusual_reason_id = hur.id ");
		sql.append(" where hur.valid_state = '1' and huro.store_id = " + organId + " ) k order by k.father_id asc ");

		logger.debug("getUnusualReasonByTenantId:::" + sql.toString());

		List<JSONObject> reasons = (List<JSONObject>) this.query(tenantId, sql.toString(), BasicDataUnusualReason.class);
		if (reasons != null) {
			JSONArray reasonArray = JSONArray.fromObject(reasons);
			object.element("reason", reasonArray);
		}
		list.add(object);

		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return list;
//		List<JSONObject> list = new ArrayList<JSONObject>();
//		JSONObject object = new JSONObject(); // data数组里的对象
//		
//		StringBuilder sql = new StringBuilder("select k.id,k.unusual_type,k.father_id, k.reason_name, k.phonetic_code, k.five_code, k.remark,k.valid_state from ");
//		sql.append(" (SELECT huur.id,huur.unusual_type,huur.father_id,huur.reason_name,huur.phonetic_code,huur.five_code,huur.remark,huur.valid_state FROM hq_unusual_reason as huur where huur.father_id = 0 union ");
//		sql.append(" SELECT DISTINCT(hur.id) id,hur.unusual_type,hur.father_id,hur.reason_name,hur.phonetic_code,hur.five_code,hur.remark,hur.valid_state FROM hq_unusual_reason as hur left join hq_unusual_reason_org as huro on huro.unusual_reason_id = hur.id ");
//		sql.append(" where hur.valid_state = '1' and huro.store_id = " + organId + " ) k order by k.father_id asc ");
//		
//		logger.debug("getUnusualReasonByTenantId:::" + sql.toString());
//		
//		List<JSONObject> reasons = (List<JSONObject>) this.query(tenantId, sql.toString(),BasicDataUnusualReason.class);
//		if (reasons != null)
//		{
//			JSONArray reasonArray = JSONArray.fromObject(reasons);
//			object.element("reason", reasonArray);
//		}
//		list.add(object);
//		
//		logger.debug("同步数据，查询返回数据:---" + object.toString());
//		return list;
	}

	@Override
	public String getUnusualReasonByTenantIdEx(String tenantId, Integer organId) throws Exception {
		StringBuilder sql = new StringBuilder("select k.id,k.unusual_type,k.father_id, k.reason_name, k.phonetic_code, k.five_code, k.remark,k.valid_state from ");
		sql.append(" (SELECT huur.id,huur.unusual_type,huur.father_id,huur.reason_name,huur.phonetic_code,huur.five_code,huur.remark,huur.valid_state FROM hq_unusual_reason as huur where huur.father_id = 0 union ");
		sql.append(" SELECT DISTINCT(hur.id) id,hur.unusual_type,hur.father_id,hur.reason_name,hur.phonetic_code,hur.five_code,hur.remark,hur.valid_state FROM hq_unusual_reason as hur left join hq_unusual_reason_org as huro on huro.unusual_reason_id = hur.id ");
		sql.append(" where hur.valid_state = '1' and huro.store_id = " + organId + " ) k order by k.father_id asc ");

		logger.debug("getUnusualReasonByTenantId:::" + sql.toString());

		List<Map<String, Object>> dishes = this.jdbcTemplate.queryForList(sql.toString());
		String tmp = org.apache.commons.lang.StringUtils.join(dishes.toArray(), "|");
		return JavaMd5.toMd5B32(tmp);
	}

	@Override
	public List<JSONObject> getDiscountCaseByTenantId(String tenantId, Integer organId) throws Exception {
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject(); // data数组里的对象

		String nowDate = DateUtil.getNowDateYYDDMM();

		StringBuilder sql = new StringBuilder("select distinct hdc.id,discount_case_name,discount_case_type,rate_renate,startdate,sarttime,enddate,endtime,running_cycle,day_running_cycle,zero_norm,discount_case_code,hdc.is_vipprice");
		sql.append(" from hq_discount_case as hdc LEFT JOIN hq_discount_case_org as hdco on hdco.discount_case_id = hdc.id ");
		sql.append(" where hdc.valid_state='1' and hdc.startdate<='" + nowDate + "' and hdc.enddate>='" + nowDate + "' and hdco.store_id = " + organId + " order by hdc.id asc ");

		SqlRowSet rs = this.query(tenantId, sql.toString());

		String week = "0" + String.valueOf(DateUtil.dayForWeek(nowDate));

		String dayOfMonth = StringUtils.leftPad("" + Calendar.getInstance().get(Calendar.DAY_OF_MONTH), 2, "0");

		List<BasicDataDiscountCase> discountCaseList = new ArrayList<BasicDataDiscountCase>();

		String runningCycle = null;
		String dayRunnintCycle = null;

		List<String> weeks = null;
		List<String> days = null;

		while (rs.next()) {
			runningCycle = rs.getString("running_cycle");
			dayRunnintCycle = rs.getString("day_running_cycle");
			BasicDataDiscountCase discountCase = new BasicDataDiscountCase();
			if (StringUtils.isNotEmpty(runningCycle)) {
				weeks = Arrays.asList(runningCycle.split(","));
			}
			if (StringUtils.isNotEmpty(dayRunnintCycle)) {
				days = Arrays.asList(dayRunnintCycle.split(","));
			}

			if ((null != weeks && weeks.contains(week)) || (null != days && days.contains(dayOfMonth))) {
				discountCase.setId(rs.getInt("id"));
				discountCase.setDiscount_case_name(rs.getString("discount_case_name"));
				discountCase.setDiscount_case_type(rs.getString("discount_case_type"));
				discountCase.setRate_renate(rs.getDouble("rate_renate"));
				discountCase.setStartdate(rs.getString("startdate"));
				discountCase.setSarttime(rs.getString("sarttime"));
				discountCase.setEnddate(rs.getString("enddate"));
				discountCase.setEndtime(rs.getString("endtime"));
				discountCase.setRunning_cycle(runningCycle);
				discountCase.setDay_running_cycle(dayRunnintCycle);
				discountCase.setZero_norm(rs.getDouble("zero_norm"));
				discountCase.setDiscount_case_code(rs.getString("discount_case_code"));
				discountCase.setIs_vipprice(rs.getString("is_vipprice"));
				discountCaseList.add(discountCase);
			}
		}
		object.element("discountcase", discountCaseList);
		list.add(object);

		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return list;
	}

	@Override
	public String getDiscountCaseByTenantIdEx(String tenantId, Integer organId) throws Exception {
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject(); // data数组里的对象

		String nowDate = DateUtil.getNowDateYYDDMM();

		StringBuilder sql = new StringBuilder("select distinct hdc.id,discount_case_name,discount_case_type,rate_renate,startdate,sarttime,enddate,endtime,running_cycle,zero_norm,discount_case_code,hdc.is_vipprice ");
		sql.append(" from hq_discount_case as hdc LEFT JOIN hq_discount_case_org as hdco on hdco.discount_case_id = hdc.id ");
		sql.append(" where hdc.valid_state='1' and hdc.startdate<='" + nowDate + "' and hdc.enddate>='" + nowDate + "' and hdco.store_id = " + organId + " order by hdc.id asc ");

		SqlRowSet rs = this.query(tenantId, sql.toString());

		String week = "0" + String.valueOf(DateUtil.dayForWeek(nowDate));

		List<BasicDataDiscountCase> discountCaseList = new ArrayList<BasicDataDiscountCase>();
		String runningCycle = null;
		while (rs.next()) {
			runningCycle = rs.getString("running_cycle");

			BasicDataDiscountCase discountCase = new BasicDataDiscountCase();
			List<String> weeks = Arrays.asList(runningCycle.split(","));
			// 不在执行周期内
			if (StringUtils.isEmpty(runningCycle) || weeks.contains(week)) {
				discountCase.setId(rs.getInt("id"));
				discountCase.setDiscount_case_name(rs.getString("discount_case_name"));
				discountCase.setDiscount_case_type(rs.getString("discount_case_type"));
				discountCase.setRate_renate(rs.getDouble("rate_renate"));
				discountCase.setStartdate(rs.getString("startdate"));
				discountCase.setSarttime(rs.getString("sarttime"));
				discountCase.setEnddate(rs.getString("enddate"));
				discountCase.setEndtime(rs.getString("endtime"));
				discountCase.setRunning_cycle(runningCycle);
				discountCase.setZero_norm(rs.getDouble("zero_norm"));
				discountCase.setDiscount_case_code(rs.getString("discount_case_code"));
				discountCase.setIs_vipprice(rs.getString("is_vipprice"));
				discountCaseList.add(discountCase);
			}
		}
		object.element("discountcase", discountCaseList);
		list.add(object);

		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return JavaMd5.toMd5B32(list.toString());
	}

	@Override
	public Data getDiscountCaseDetailsByTenantId(String tenantId, Integer organId, Pagination pagination) throws Exception {
		Data data = new Data();
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject(); // data数组里的对象

		if (pagination == null) {
			pagination = new Pagination();
		}


		StringBuilder sql = new StringBuilder("select hdcd.discount_case_id,hdcd.id,hdcd.item_id,hdcd.unit,rate,hdcd.derate,hdcd.item_unit_id  from hq_discount_case_details as hdcd ");
		sql.append(" LEFT JOIN hq_discount_case_org as hdco on hdco.discount_case_id = hdcd.discount_case_id  ");
		sql.append(" where hdco.store_id = " + organId + " group by hdcd.discount_case_id,hdcd.id,hdcd.item_id,hdcd.unit,rate,hdcd.derate order by hdcd.id asc ");

		int totalCount = 0;
		SqlRowSet rs = this.jdbcTemplate.queryForRowSet("select count(*) as count from (" + sql.toString() + ") t");
		while (rs.next()) {
			totalCount = rs.getInt("count");
		}

		if (pagination != null && pagination.getPagesize() > 0) {
			if ((pagination.getPageno() - 1) < 1) {
				sql.append(" limit " + pagination.getPagesize() + " OFFSET " + 0);
			} else {
				sql.append(" limit " + pagination.getPagesize() + " OFFSET " + (pagination.getPageno() - 1) * pagination.getPagesize());
			}
		}

		logger.debug("getDiscountCaseDetailsByTenantId:::" + sql.toString());
		List<JSONObject> discountCaseDetails = this.queryString4Json(tenantId, sql.toString());
		if (discountCaseDetails != null) {
			JSONArray discountDetailArray = JSONArray.fromObject(discountCaseDetails);
			object.element("discount_detail", discountDetailArray);
		}
		pagination.setPageno(pagination.getPageno() > 0 ? pagination.getPageno() : 1);
		pagination.setTotalcount(totalCount);

		if (pagination.getPagesize() == 0) {
			pagination.setPagesize(discountCaseDetails.size());
		}

		list.add(object);

		data.setPagination(pagination);
		data.setData(list);

		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return data;
	}

	@Override
	public String getDiscountCaseDetailsByTenantIdEx(String tenantId, Integer organId) throws Exception {
		StringBuilder sql = new StringBuilder("select hdcd.discount_case_id,hdcd.id,hdcd.item_id,hdcd.unit,rate,hdcd.derate from hq_discount_case_details as hdcd ");
		sql.append(" LEFT JOIN hq_discount_case_org as hdco on hdco.discount_case_id = hdcd.id ");
		sql.append(" where hdco.store_id = " + organId + " group by hdcd.discount_case_id,hdcd.id,hdcd.item_id,hdcd.unit,rate,hdcd.derate order by hdcd.id asc ");

		logger.debug("getDiscountCaseDetailsByTenantId:::" + sql.toString());

		List<Map<String, Object>> dishes = this.jdbcTemplate.queryForList(sql.toString());
		String tmp = org.apache.commons.lang.StringUtils.join(dishes.toArray(), "|");
		return JavaMd5.toMd5B32(tmp);
	}

	@Override
	public List<JSONObject> getPaymentWayByOrganId(String tenantId, Integer organId) throws Exception {
		StringBuilder sql = new StringBuilder("select pw2.id,coalesce(sd.class_item,pw2.payment_name1) payment_name1,pw2.payment_name2,pw2.is_standard_money,pw2.payment_class,pw2.if_prepay,pw2.is_check,pw2.is_recharge,pw2.rate,pw2.third_code, coalesce(pw2.is_open_cashbox, 1) as is_open_cashbox,'1' as is_show ,pw2.pay_type from payment_way as pw2");
		sql.append(" left join payment_way_of_ogran as pwoo2 on pw2.id = pwoo2.payment_id left join sys_dictionary sd on pw2.payment_name1 = sd.class_item_code and sd.class_identifier_code='currency' and pw2.payment_class='cash'");
		sql.append(" where pw2.status = '1' and pwoo2.organ_id = ").append(organId);

		logger.debug("getPaymentWayByOrganId:::" + sql.toString());
		List<JSONObject> paymentWays = this.queryString4Json(tenantId, sql.toString());

		JSONObject object = new JSONObject(); // data数组里的对象
		if (paymentWays != null) {
			JSONArray paymentWayArray = JSONArray.fromObject(paymentWays);
			object.element("payment_way", paymentWayArray);
		}

		List<JSONObject> list = new ArrayList<JSONObject>();
		list.add(object);

		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return list;
	}

	@Override
	public List<JSONObject> getPaymentWayByOrganId(String tenantId, Integer organId, String devicesCode) throws Exception {
		StringBuilder sql = new StringBuilder("select pw2.id,coalesce(sd.class_item,pw2.payment_name1) payment_name1,pw2.payment_name2,pw2.is_standard_money,pw2.payment_class,pw2.if_prepay,pw2.is_check,pw2.is_recharge,pw2.rate,pw2.third_code, coalesce(pw2.is_open_cashbox, 1) as is_open_cashbox ");
		sql.append(",(case when sh.payment_id is not null then '1' else '0' end) as is_show ,pw2.pay_type  ");
		sql.append("from payment_way as pw2 left join payment_way_of_ogran as pwoo2 on pw2.id = pwoo2.payment_id left join sys_dictionary sd on pw2.payment_name1 = sd.class_item_code and sd.class_identifier_code='currency' and pw2.payment_class=? ");
//		sql.append("left join ( ");
//		sql.append("  select ps.tenancy_id,ps.store_id,count(*) as counts from hq_payment_showtype ps left join hq_devices de on ps.tenancy_id=de.tenancy_id and ps.store_id=de.store_id and ps.showtype_code=de.show_type ");
//		sql.append("  left join (select pw.*,po.organ_id from payment_way pw left join payment_way_of_ogran as po on pw.tenancy_id=po.tenancy_id and pw.id=po.payment_id) pw on ps.tenancy_id=pw.tenancy_id and ps.store_id=pw.organ_id and ps.payment_id=pw.id ");
//		sql.append("  where pw.status='1' and devices_code=? group by ps.tenancy_id,ps.store_id ");
//		sql.append(") sc on pwoo2.tenancy_id=sc.tenancy_id and pwoo2.organ_id=sc.store_id ");
		sql.append("left join ( ");
		sql.append("  select ps.tenancy_id,ps.store_id,ps.payment_id from hq_payment_showtype ps left join hq_devices de on ps.tenancy_id=de.tenancy_id and ps.store_id=de.store_id and ps.showtype_code=de.show_type ");
		sql.append("  where devices_code=? ");
		sql.append(") sh on pwoo2.tenancy_id=sh.tenancy_id and pwoo2.organ_id=sh.store_id and pw2.id = sh.payment_id ");
		sql.append("where pw2.status = '1' and pwoo2.organ_id = ? and pwoo2.tenancy_id = ?");

		logger.debug("getPaymentWayByOrganId:::" + sql.toString());
		List<JSONObject> paymentWays = this.queryString4Json(tenantId, sql.toString(), new Object[]
				{SysDictionary.PAYMENT_CLASS_CASH, devicesCode, organId, tenantId});

		//如果终端设备类型未配置付款方式显示,则显示所有付款方式
		boolean isShowAll = true;
		if (null != paymentWays && 0 < paymentWays.size()) {
			for (JSONObject payJson : paymentWays) {
				if ("1".equals(payJson.optString("is_show"))) {
					isShowAll = false;
					break;
				}
			}

			if (isShowAll) {
				for (JSONObject payJson : paymentWays) {
					payJson.put("is_show", "1");
				}
			}
		}

		JSONObject object = new JSONObject(); // data数组里的对象
		if (paymentWays != null) {
			JSONArray paymentWayArray = JSONArray.fromObject(paymentWays);
			object.element("payment_way", paymentWayArray);
		}
		List<JSONObject> list = new ArrayList<JSONObject>();
		list.add(object);

		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return list;
	}

	@Override
	public String getPaymentWayByOrganIdEx(String tenantId, Integer organId) throws Exception {
		StringBuilder sql = new StringBuilder("select pw2.id,coalesce(sd.class_item,pw2.payment_name1) payment_name1,pw2.payment_name2,pw2.is_standard_money,pw2.payment_class,pw2.if_prepay,pw2.is_check,pw2.is_recharge,pw2.rate,pw2.third_code, coalesce(pw2.is_open_cashbox, 1) as is_open_cashbox,ps.showtype_code,de.devices_code from payment_way as pw2");
		sql.append(" left join payment_way_of_ogran as pwoo2 on pw2.id = pwoo2.payment_id left join sys_dictionary sd on pw2.payment_name1 = sd.class_item_code and sd.class_identifier_code='currency' and pw2.payment_class='cash'");
		sql.append(" left join hq_payment_showtype as ps on pw2.id = ps.payment_id and pwoo2.organ_id=ps.store_id");
		sql.append(" left join hq_devices de on ps.tenancy_id=de.tenancy_id and ps.store_id=de.store_id and ps.showtype_code=de.show_type");
		sql.append(" where pw2.status = '1' and pwoo2.organ_id = ").append(organId);

		logger.debug("getPaymentWayByOrganId:::" + sql.toString());

		List<Map<String, Object>> dishes = this.jdbcTemplate.queryForList(sql.toString());
		String tmp = org.apache.commons.lang.StringUtils.join(dishes.toArray(), "|");
		return JavaMd5.toMd5B32(tmp);
	}


	@Override
	public List<JSONObject> getPaymentShowtypeByOrganId(String tenantId, Integer organId) throws Exception {
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject(); // data数组里的对象

		StringBuilder sql = new StringBuilder("select ps.* from hq_payment_showtype as ps left join payment_way_of_ogran as po on ps.tenancy_id=po.tenancy_id and ps.store_id=po.organ_id and ps.payment_id=po.payment_id left join payment_way as pw on  po.payment_id=pw.id");
		sql.append(" where po.tenancy_id='").append(tenantId).append("' and po.organ_id=").append(organId).append(" and pw.status='1'");
		logger.debug("getPaymentWayByOrganId:::" + sql.toString());
		List<JSONObject> paymentWays = this.queryString4Json(tenantId, sql.toString());
		if (paymentWays != null) {
			JSONArray paymentWayArray = JSONArray.fromObject(paymentWays);
			object.element("hq_payment_showtype", paymentWayArray);
		}
		list.add(object);

		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return list;
	}

	@Override
	public String getPaymentShowtypeByOrganIdEx(String tenantId, Integer organId) throws Exception {
		StringBuilder sql = new StringBuilder("select ps.* from hq_payment_showtype as ps left join payment_way_of_ogran as po on ps.tenancy_id=po.tenancy_id and ps.store_id=po.organ_id and ps.payment_id=po.payment_id left join payment_way as pw on  po.payment_id=pw.id");
		sql.append(" where po.tenancy_id='").append(tenantId).append("' and po.organ_id=").append(organId).append(" and pw.status='1'");
		logger.debug("getPaymentWayByOrganId:::" + sql.toString());
		List<Map<String, Object>> dishes = this.jdbcTemplate.queryForList(sql.toString());
		String tmp = org.apache.commons.lang.StringUtils.join(dishes.toArray(), "|");
		return JavaMd5.toMd5B32(tmp);
	}

	@Override
	public Data getDishByTenantId(String tenantId, Integer organId, String channel, Pagination pagination) throws Exception {
		Data data = new Data();
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject(); // data数组里的对象

		if (pagination == null) {
			pagination = new Pagination();
		}

//		StringBuilder str = new StringBuilder("select distinct himd.item_menu_id,hpix.print_id,hpix.print_name,hpix.print_ip_com,himd.id as details_id,himd.item_id,(case when himd.menu_item_rank is null then 0 when himd.menu_item_rank = '' then 0 else cast(himd.menu_item_rank as int) end) as menu_item_rank,hii.item_code as item_no,himc.item_name,himc.item_english, himc.phonetic_code, himc.five_code,");
//		str.append("coalesce(hip.price,hiu.standard_price) as price,hiu.id as unit_id,hiu.unit_name,himd.starttime,himd.endtime,coalesce(himd.is_show,1) as is_show,himd.valid_state,hic.id as item_class_id,");
//		str.append("hic.itemclass_code,hii.item_barcode,hii.is_discount,hii.is_pushmoney,hii.pushmoney_way,hii.proportion_money as proportion,hii.is_modifyquantity,hii.third_code,hii.is_turnover,hii.is_new,");
//		str.append("hii.is_modifyname,hii.is_runningprice,hii.is_staffmeal,hii.is_throwaway,hii.is_seafood,hii.is_staplefood,hii.is_combo,hii.is_characteristic,hii.remark,hii.entry_person,hii.entry_time,");
//		str.append("hii.is_recommendation,hii.to_offer,hii.combo_type,hii.nutrition,hii.suitable_crowds,hii.unsuitable_crowds,hii.processing_technic,hii.photo1,hii.photo2,hii.photo3,hii.photo4,hii.photo5,hii.photo6,hii.summary,hii.spicy,hii.is_points,hii.item_description,coalesce(civs.vip_price, civ.vip_price) as vip_price,hii.is_assemble_combo,hii.is_assist_num ");
//		str.append(" from hq_item_info as hii ");
//		str.append(" LEFT JOIN (SELECT hpi.item_id, string_agg (hpi.print_id :: VARCHAR, ',') print_id, string_agg (hpn.name :: VARCHAR, ',') print_name, string_agg (hpn.ip_com :: VARCHAR, ',') print_ip_com FROM hq_printer_new hpn LEFT JOIN hq_printer_item hpi ON hpi.print_id = hpn. ID LEFT JOIN hq_printer_model hpm ON hpi.print_format_id = hpm. ID WHERE hpm.format_type IN ('1107', '1113', '1106') AND hpm.is_enables = '1' AND hpn.valid_state = '1' GROUP BY hpi.item_id) hpix ON hpix.item_id = hii. ID ");
//		str.append(" left join hq_item_menu_details as himd on hii.id = himd.item_id");
//		str.append(" left join hq_item_menu him on himd.item_menu_id = him.id");
//		str.append(" left join hq_item_menu_organ as himo on him.id = himo.item_menu_id");
//		str.append(" left join organ o on himo.store_id = o.id");
//		str.append(" left join hq_item_menu_class as himc on himc.details_id = himd.id");
//		str.append(" left join hq_item_class as hic on himc.class = hic.id");
//		str.append(" left join hq_item_unit as hiu on hii.id = hiu.item_id  and hiu.is_default = 'Y' and hiu.valid_state = '1'");
//		str.append(" left join crm_item_vip civ on hiu.item_id = civ.item_id and hiu.id = civ.unit_id");
//		str.append(" left join crm_item_vip_sysprice civs on civ.unit_id = civs.unit_id and civs.chanel = himc.chanel and civs.price_system::text = o.price_system");
//		str.append(" left join hq_item_pricesystem hip on hiu.id=hip.item_unit_id and hip.chanel=himc.chanel and hip.price_system::text =o.price_system");
//		str.append(" where hiu.id is not null and hii.valid_state = '1' and him.valid_state = '1' and himc.chanel='" + channel + "' and himo.store_id = " + organId);
//
//		int totalCount = 0;
//		SqlRowSet rs = this.jdbcTemplate.queryForRowSet("select count(*) as count from (" + str.toString() + ") t");
//		while (rs.next()) {
//			totalCount = rs.getInt("count");
//		}
//
//		str.append(" order by himd.item_id asc ");

		StringBuilder str=new StringBuilder();
		str.append("select * from pos_dish_cache where chanel='"+channel+"' order by item_id asc");

		int totalCount = 0;
		if (pagination != null && pagination.getPagesize() > 0)
		{
			SqlRowSet rs = this.jdbcTemplate.queryForRowSet("select count(1) as count from ("+str.toString()+") t");
			while (rs.next())
			{
				totalCount = rs.getInt("count");
			}

			if ((pagination.getPageno()-1) < 1)
			{
				str.append(" limit " + pagination.getPagesize() + " OFFSET " + 0);
			}else
			{
				str.append(" limit " + pagination.getPagesize() + " OFFSET " + (pagination.getPageno()-1)*pagination.getPagesize());
			}
		}

		List<JSONObject> dishes=this.query4Json(tenantId,str.toString());
		object.element("dish", dishes);
		pagination.setPageno(pagination.getPageno() > 0 ? pagination.getPageno() : 1);
		pagination.setTotalcount(totalCount);

		if (pagination.getPagesize() == 0)
		{
			pagination.setPagesize(dishes.size());
		}

		list.add(object);

		data.setPagination(pagination);
		data.setData(list);

		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return data;
	}

	@Override
	public String getDishByTenantIdEx(String tenantId, Integer organId) throws Exception {
		StringBuilder str = new StringBuilder("select distinct himc.chanel,himd.item_menu_id,/*hpix.print_id,hpix.print_name,hpix.print_ip_com,*/himd.id as details_id,himd.item_id,(case when himd.menu_item_rank is null then 0 when himd.menu_item_rank = '' then 0 else cast(himd.menu_item_rank as int) end) as menu_item_rank,hii.item_code as item_no,himc.item_name,himc.item_english, himc.phonetic_code, himc.five_code,");
		str.append("coalesce(hip.price,hiu.standard_price) as price,hiu.id as unit_id,hiu.unit_name,himd.starttime,himd.endtime,coalesce(himd.is_show,1) as is_show,himd.valid_state,hic.id as item_class_id,");
		str.append("hic.itemclass_code,hii.item_barcode,hii.is_discount,hii.is_pushmoney,hii.pushmoney_way,hii.proportion_money as proportion,hii.is_modifyquantity,hii.third_code,hii.is_turnover,hii.is_new,");
		str.append("hii.is_modifyname,hii.is_runningprice,hii.is_staffmeal,hii.is_throwaway,hii.is_seafood,hii.is_staplefood,hii.is_combo,hii.is_characteristic,hii.remark,hii.entry_person,hii.entry_time,");
		str.append("hii.is_recommendation,hii.to_offer,hii.combo_type,hii.nutrition,hii.suitable_crowds,hii.unsuitable_crowds,hii.processing_technic,hii.photo1,hii.photo2,hii.photo3,hii.photo4,hii.photo5,hii.photo6,hii.summary,hii.spicy,hii.is_points,hii.item_description,coalesce(civs.vip_price, civ.vip_price) as vip_price,hii.is_assemble_combo,hii.is_assist_num ");
		str.append(" from hq_item_info as hii ");
//		str.append(" LEFT JOIN (SELECT hpi.item_id, string_agg (hpi.print_id :: VARCHAR, ',') print_id, string_agg (hpn.name :: VARCHAR, ',') print_name, string_agg (hpn.ip_com :: VARCHAR, ',') print_ip_com FROM hq_printer_new hpn LEFT JOIN hq_printer_item hpi ON hpi.print_id = hpn. ID LEFT JOIN hq_printer_model hpm ON hpi.print_format_id = hpm. ID WHERE hpm.format_type IN ('1107', '1113', '1106') AND hpm.is_enables = '1' AND hpn.valid_state = '1' GROUP BY hpi.item_id) hpix ON hpix.item_id = hii. ID ");
		str.append(" left join hq_item_menu_details as himd on hii.id = himd.item_id");
		str.append(" left join hq_item_menu him on himd.item_menu_id = him.id");
		str.append(" left join hq_item_menu_organ as himo on him.id = himo.item_menu_id");
		str.append(" left join organ o on himo.store_id = o.id");
		str.append(" left join hq_item_menu_class as himc on himc.details_id = himd.id");
		str.append(" left join hq_item_class as hic on himc.class = hic.id");
		str.append(" left join hq_item_unit as hiu on hii.id = hiu.item_id  and hiu.is_default = 'Y' and hiu.valid_state = '1'");
		str.append(" left join crm_item_vip civ on hiu.item_id = civ.item_id and hiu.id = civ.unit_id");
		str.append(" left join crm_item_vip_sysprice civs on civ.unit_id = civs.unit_id and civs.chanel = himc.chanel and civs.price_system::text = o.price_system");
		str.append(" left join hq_item_pricesystem hip on hiu.id=hip.item_unit_id and hip.chanel=himc.chanel and hip.price_system::text =o.price_system");
		str.append(" where hiu.id is not null and hii.valid_state = '1' and him.valid_state = '1' ");
		str.append(" and himo.store_id = " + organId);
		str.append(" order by himd.item_id asc ");

		List<JSONObject> dishes=this.query4Json(tenantId,str.toString());
		this.execute(tenantId,"delete from pos_dish_cache");
		this.insertBatchIgnorCase(tenantId,"pos_dish_cache",dishes);
		return JavaMd5.toMd5B32(dishes.toString());

	}

//		@Override
//		public Data getDishByTenantId(String tenantId,Integer organId) throws Exception
//		{
//		Data data = new Data();
//		List<JSONObject> list = new ArrayList<JSONObject>();
//		JSONObject object = new JSONObject(); // data数组里的对象
//		
//		StringBuilder str = new StringBuilder("select distinct himd.item_menu_id,himd.id as details_id,himd.item_id,hii.item_code as item_no,himc.item_name,himc.item_english, himc.phonetic_code, himc.five_code,");
//		str.append("coalesce(hip.price,hiu.standard_price) as price,hiu.id as unit_id,hiu.unit_name,himd.starttime,himd.endtime,himd.valid_state,hic.id as item_class_id,");
//		str.append("hic.itemclass_code,hii.item_barcode,hii.is_discount,hii.is_pushmoney,hii.pushmoney_way,hii.proportion_money as proportion,hii.is_modifyquantity,hii.third_code,hii.is_turnover,hii.is_new,");
//		str.append("hii.is_modifyname,hii.is_runningprice,hii.is_staffmeal,hii.is_throwaway,hii.is_seafood,hii.is_staplefood,hii.is_combo,hii.is_characteristic,hii.remark,hii.entry_person,hii.entry_time,");
//		str.append("hii.is_recommendation,hii.to_offer,hii.combo_type,hii.nutrition,hii.suitable_crowds,hii.unsuitable_crowds,hii.processing_technic,hii.photo1,hii.photo2,hii.photo3,hii.photo4,hii.photo5,hii.photo6,hii.summary,hii.spicy,hii.is_points,hii.item_description ");
//		str.append(" from hq_item_info as hii ");
//		str.append(" left join hq_item_menu_details as himd on hii.id = himd.item_id ");
//		str.append(" left join hq_item_menu_organ as himo on himd.item_menu_id = himo.item_menu_id ");
//		str.append(" left join hq_item_menu him on himo.item_menu_id = him.id ");
//		str.append(" left join hq_item_menu_class as himc on himc.details_id = himd.id ");
//		str.append(" left join hq_item_class as hic on himc.class = hic.id ");
//		str.append(" left join hq_item_unit as hiu on hii.id = hiu.item_id ");
//		str.append(" left join organ o on himo.store_id = o.id ");
//		str.append(" left join hq_item_pricesystem hip on hiu.id=hip.item_unit_id and hip.chanel=himc.chanel and hip.price_system||'' =o.price_system ");
//		str.append(" where hiu.is_default = 'Y' and him.valid_state = '1' and hiu.valid_state = '1' and hii.valid_state = '1' and himo.store_id = " + organId);
//		str.append(" order by himd.item_id asc ");
//		
//		logger.debug("getDishByTenantId:::" + str.toString());
//		List<BasicDataDish> dishes = this.jdbcTemplate.query(str.toString(), BeanPropertyRowMapper.newInstance(BasicDataDish.class));	
//		if (dishes != null)
//		{
//			JSONArray itemMenuDetailArray = JSONArray.fromObject(dishes);
//			object.element("dish", itemMenuDetailArray);
//		}
//		list.add(object);
//		data.setData(list);
//		
//		logger.debug("同步数据，查询返回数据:---" + object.toString());
//		return data;
//	}

	@Override
	public Data getItemUnitsByTenantId(String tenantId, Integer organId, String channel, Pagination pagination) throws Exception {
		Data data = new Data();
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject();
		if (pagination == null)
		{
			pagination = new Pagination();
		}
		/*StringBuilder str = new StringBuilder("select distinct hiu.id,hiu.item_id,hiu.unit_name,hiu.is_default,coalesce(hip.price,hiu.standard_price) as standard_price,hiu.combo_display_state,hiu.count_rate");
		str.append(",(case when iv.vip_price is not null and iv.vip_price<coalesce(hip.price,hiu.standard_price)then iv.vip_price else coalesce(hip.price,hiu.standard_price) end) vip_price");
		str.append(" from hq_item_unit as hiu");
		str.append(" left join hq_item_menu_details as himd on himd.item_id = hiu.item_id");
		str.append(" left join hq_item_menu_class as himc on himc.details_id = himd.id");
		str.append(" left join hq_item_menu_organ as himo on himo.item_menu_id = himd.item_menu_id");
		str.append(" left join organ o on himo.store_id = o.id");
		str.append(" left join hq_item_pricesystem hip on hiu.id=hip.item_unit_id and hip.chanel=himc.chanel and hip.price_system||'' =o.price_system");
		str.append(" left join crm_item_vip_sysprice iv on iv.unit_id=hiu.id and iv.chanel=himc.chanel and iv.price_system||'' =o.price_system");
		str.append(" where hiu.valid_state='1' and himo.store_id = " + organId + " and himc.chanel= '" + channel + "' ");
        str.append(" order by hiu.id asc ");*/

		StringBuilder str=new StringBuilder();
		str.append("SELECT DISTINCT unit_id as id,unit_name,is_default,standard_price,combo_display_state,count_rate,vip_price,item_id FROM pos_unit_cache where chanel='"+channel+"' order by unit_id asc");

		int totalCount = 0;
		if (pagination != null && pagination.getPagesize() > 0)
		{
			SqlRowSet rs = this.jdbcTemplate.queryForRowSet("select count(1) as count from ("+str.toString()+") t");
			while (rs.next())
			{
				totalCount = rs.getInt("count");
			}

			if ((pagination.getPageno()-1) < 1)
			{
				str.append(" limit " + pagination.getPagesize() + " OFFSET " + 0);
			}else
			{
				str.append(" limit " + pagination.getPagesize() + " OFFSET " + (pagination.getPageno()-1)*pagination.getPagesize());
			}
		}

		List<JSONObject> itemUnits=this.query4Json(tenantId,str.toString());
		object.element("unit", itemUnits);
		pagination.setPageno(pagination.getPageno() > 0 ? pagination.getPageno() : 1);
		pagination.setTotalcount(totalCount);

		if (pagination.getPagesize() == 0)
		{
			pagination.setPagesize(itemUnits.size());
		}
		list.add(object);

		data.setPagination(pagination);
		data.setData(list);

		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return data;
	}

	@Override
	public String getItemUnitsByTenantIdEx(String tenantId, Integer organId) throws Exception {
		StringBuilder str = new StringBuilder("select distinct himc.chanel,hiu.id as unit_id,hiu.item_id,hiu.unit_name,hiu.is_default,coalesce(hip.price,hiu.standard_price) as standard_price,hiu.combo_display_state,hiu.count_rate");
		str.append(",(case when iv.vip_price is not null and iv.vip_price<coalesce(hip.price,hiu.standard_price)then iv.vip_price else coalesce(hip.price,hiu.standard_price) end) vip_price");
		str.append(" from hq_item_unit as hiu");
		str.append(" left join hq_item_menu_details as himd on himd.item_id = hiu.item_id");
		str.append(" left join hq_item_menu_class as himc on himc.details_id = himd.id");
		str.append(" left join hq_item_menu_organ as himo on himo.item_menu_id = himd.item_menu_id");
		str.append(" left join organ o on himo.store_id = o.id");
		str.append(" left join hq_item_pricesystem hip on hiu.id=hip.item_unit_id and hip.chanel=himc.chanel and hip.price_system||'' =o.price_system");
		str.append(" left join crm_item_vip_sysprice iv on iv.unit_id=hiu.id and iv.chanel=himc.chanel and iv.price_system||'' =o.price_system");
		str.append(" where hiu.valid_state='1' and himo.store_id = " + organId );
		str.append(" order by hiu.id asc ");

		List<JSONObject> units=this.query4Json(tenantId,str.toString());
		this.execute(tenantId,"delete from pos_unit_cache");
		this.insertBatchIgnorCase(tenantId,"pos_unit_cache",units);
		return JavaMd5.toMd5B32(units.toString());
	}

	@Override
	public List<JSONObject> getTablesByTenantIdAndOrganId(String tenantId, Integer organId) throws Exception {
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject();

		StringBuilder sql = new StringBuilder("select id,business_area_id,table_code,table_name,table_property_id,seat_counts,floor,fwfz_id,table_fw,is_cyfq,free_icon,use_icon,danzhuo_yy from tables_info ");
		sql.append(" where valid_state='1' and organ_id = " + organId + " order by id asc ");
		List<PosTableInfo> tableInfos = this.jdbcTemplate.query(sql.toString(), BeanPropertyRowMapper.newInstance(PosTableInfo.class));

		logger.debug("getTablesByTenantIdAndOrganId:::" + sql.toString());

		if (tableInfos != null) {
			JSONArray tablesArray = JSONArray.fromObject(tableInfos);
			object.element("table", tablesArray);
		}
		list.add(object);

		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return list;
	}

	@Override
	public String getTablesByTenantIdAndOrganIdEx(String tenantId, Integer organId) throws Exception {
		StringBuilder sql = new StringBuilder("select id,business_area_id,table_code,table_name,table_property_id,seat_counts,floor,fwfz_id,table_fw,is_cyfq,free_icon,use_icon,danzhuo_yy from tables_info ");
		sql.append(" where valid_state='1' and organ_id = " + organId + " order by id asc ");
//		List<PosTableInfo> tableInfos = this.jdbcTemplate.query(sql.toString(), BeanPropertyRowMapper.newInstance(PosTableInfo.class));

		logger.debug("getTablesByTenantIdAndOrganId:::" + sql.toString());

		List<Map<String, Object>> dishes = this.jdbcTemplate.queryForList(sql.toString());
		String tmp = org.apache.commons.lang.StringUtils.join(dishes.toArray(), "|");
		return JavaMd5.toMd5B32(tmp);
	}

	@Override
	public List<JSONObject> getTimePriceByOrganId(String tenantId, Integer organId, String channel) throws Exception {
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject();

		StringBuilder sql = new StringBuilder("select htp.id,htp.time_price_name,htp.remark,htp.startdate,htp.sarttime,htp.enddate,htp.endtime,htp.running_cycle,htp.zero_norm,htp.running_status,htp.valid_state,htp.last_updatetime,htp.stop_person,htp.stop_time from hq_time_price as htp ");
		sql.append(" left JOIN hq_time_price_org as htpo ON htpo.time_price_id = htp.id where htp.valid_state='1' and htpo.store_id = " + organId);
		sql.append(" group by htp.id,htp.time_price_name,htp.remark,htp.startdate,htp.sarttime,htp.enddate,htp.endtime,htp.running_cycle,htp.zero_norm,htp.running_status,htp.valid_state,htp.last_updatetime,htp.stop_person,htp.stop_time order by htp.id asc ");

		logger.debug("getTimePriceByOrganId:::" + sql.toString());
		List<JSONObject> timePrices = this.queryString4Json(tenantId, sql.toString());

		if (timePrices != null) {
			JSONArray pricesArray = JSONArray.fromObject(timePrices);
			object.element("timeprice", pricesArray);
		}

		list.add(object);

		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return list;
	}

//	@Override
//	public List<JSONObject> getTimePriceByOrganId(String tenantId,Integer organId) throws Exception
//	{
//		List<JSONObject> list = new ArrayList<JSONObject>();
//		JSONObject object = new JSONObject();
//		
//		StringBuilder sql = new StringBuilder("select htp.id,htp.time_price_name,htp.remark,htp.startdate,htp.sarttime,htp.enddate,htp.endtime,htp.running_cycle,htp.zero_norm,htp.running_status,htp.valid_state,htp.last_updatetime,htp.stop_person,htp.stop_time from hq_time_price as htp ");
//		sql.append(" left JOIN hq_time_price_org as htpo ON htpo.time_price_id = htp.id where htp.valid_state='1' and htpo.store_id = " + organId);
//		sql.append(" group by htp.id,htp.time_price_name,htp.remark,htp.startdate,htp.sarttime,htp.enddate,htp.endtime,htp.running_cycle,htp.zero_norm,htp.running_status,htp.valid_state,htp.last_updatetime,htp.stop_person,htp.stop_time order by htp.id asc ");
//		
//		logger.debug("getTimePriceByOrganId:::" + sql.toString());
//		List<JSONObject> timePrices = this.queryString4Json(tenantId, sql.toString());
//		
//		if (timePrices != null)
//		{
//			JSONArray pricesArray = JSONArray.fromObject(timePrices);
//			object.element("timeprice", pricesArray);
//		}
//		
//		list.add(object);
//		
//		logger.debug("同步数据，查询返回数据:---" + object.toString());
//		return list;
//	}

	@Override
	public String getTimePriceByOrganIdEx(String tenantId, Integer organId) throws Exception {
		StringBuilder sql = new StringBuilder("select htp.id,htp.time_price_name,htp.remark,htp.startdate,htp.sarttime,htp.enddate,htp.endtime,htp.running_cycle,htp.zero_norm,htp.running_status,htp.valid_state,htp.last_updatetime,htp.stop_person,htp.stop_time from hq_time_price as htp ");
		sql.append(" left JOIN hq_time_price_org as htpo ON htpo.time_price_id = htp.id where htp.valid_state='1' and htpo.store_id = " + organId);
		sql.append(" group by htp.id,htp.time_price_name,htp.remark,htp.startdate,htp.sarttime,htp.enddate,htp.endtime,htp.running_cycle,htp.zero_norm,htp.running_status,htp.valid_state,htp.last_updatetime,htp.stop_person,htp.stop_time order by htp.id asc ");

		logger.debug("getTimePriceByOrganId:::" + sql.toString());
		List<Map<String, Object>> dishes = this.jdbcTemplate.queryForList(sql.toString());
		String tmp = org.apache.commons.lang.StringUtils.join(dishes.toArray(), "|");
		return JavaMd5.toMd5B32(tmp);
	}

	@Override
	public List<JSONObject> getTimePriceItemByOrganId(String tenantId, Integer organId, String channel) throws Exception {
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject();

		StringBuilder sql = new StringBuilder();
		sql.append(" select distinct htpd.id,htpd.time_price_id,htpd.item_id,coalesce(htpp.price,htpd.afterprice) as afterprice,htpd.item_unit_id,htpd.remark");
		sql.append(" from hq_time_price_details as htpd");
		sql.append(" left join hq_time_price_org as htpo on htpo.time_price_id = htpd.time_price_id");
		sql.append(" left join organ o on o.id=htpo.store_id");
		sql.append(" left join hq_time_price_pricesystem htpp on htpp.time_price_details_iid=htpd.id and htpp.price_system||''=o.price_system");
		sql.append(" where htpd.valid_state='1' and htpo.store_id = ").append(organId).append(" and htpp.chanel='").append(channel).append("' order by htpd.id asc");

		logger.debug("getTimePriceItemByOrganId:::" + sql.toString());

		List<JSONObject> timePriceItems = this.queryString4Json(tenantId, sql.toString());
		if (timePriceItems != null) {
			JSONArray priceItemArray = JSONArray.fromObject(timePriceItems);
			object.element("timeprice_item", priceItemArray);
		}

		list.add(object);

		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return list;
	}

//	@Override
//	public List<JSONObject> getTimePriceItemByOrganId(String tenantId, Integer organId) throws Exception
//	{
//		List<JSONObject> list = new ArrayList<JSONObject>();
//		JSONObject object = new JSONObject();
//		
//		StringBuilder sql = new StringBuilder();
//		sql.append(" select distinct htpd.id,htpd.time_price_id,htpd.item_id,coalesce(htpp.price,htpd.afterprice) as afterprice,htpd.item_unit_id,htpd.remark,htpp.chanel");
//		sql.append(" from hq_time_price_details as htpd");
//		sql.append(" left join hq_time_price_org as htpo on htpo.time_price_id = htpd.time_price_id");
//		sql.append(" left join organ o on o.id=htpo.store_id");
//		sql.append(" left join hq_time_price_pricesystem htpp on htpp.time_price_details_iid=htpd.id and htpp.price_system||''=o.price_system");
//		sql.append(" where htpd.valid_state='1' and htpo.store_id = ").append(organId).append(" order by htpd.id asc");
//		
//		logger.debug("getTimePriceItemByOrganId:::" + sql.toString());
//		
//		List<JSONObject> timePriceItems = this.queryString4Json(tenantId, sql.toString());
//		if (timePriceItems != null)
//		{
//			JSONArray priceItemArray = JSONArray.fromObject(timePriceItems);
//			object.element("timeprice_item", priceItemArray);
//		}
//		
//		list.add(object);
//		
//		logger.debug("同步数据，查询返回数据:---" + object.toString());
//		return list;
//	}

	@Override
	public String getTimePriceItemByOrganIdEx(String tenantId, Integer organId) throws Exception {
		StringBuilder sql = new StringBuilder();
		sql.append(" select distinct htpd.id,htpd.time_price_id,htpd.item_id,coalesce(htpp.price,htpd.afterprice) as afterprice,htpd.item_unit_id,htpd.remark,htpp.chanel");
		sql.append(" from hq_time_price_details as htpd");
		sql.append(" left join hq_time_price_org as htpo on htpo.time_price_id = htpd.time_price_id");
		sql.append(" left join organ o on o.id=htpo.store_id");
		sql.append(" left join hq_time_price_pricesystem htpp on htpp.time_price_details_iid=htpd.id and htpp.price_system||''=o.price_system");
		sql.append(" where htpd.valid_state='1' and htpo.store_id = ").append(organId).append(" order by htpd.id asc");

		logger.debug("getTimePriceItemByOrganId:::" + sql.toString());

		List<Map<String, Object>> dishes = this.jdbcTemplate.queryForList(sql.toString());
		String tmp = org.apache.commons.lang.StringUtils.join(dishes.toArray(), "|");
		return JavaMd5.toMd5B32(tmp);
	}

	@Override
	public List<JSONObject> getItemComboDetailsByOrganId(String tenantId, Integer organId, String channel) throws Exception {
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject();

		StringBuilder sql = new StringBuilder();
		sql.append(" select distinct hicd.id,hicd.iitem_id,hicd.is_itemgroup,hicd.details_id,hicd.combo_num,coalesce(hicp.price,hicd.standardprice) as standardprice,coalesce(hicd.combo_order,0) as order,hicd.valid_state,hicd.item_unit_id,coalesce(hicd.change_num_state,'N') as change_num_state ");
		sql.append(" from hq_item_combo_details as hicd");
		sql.append(" left join hq_item_combo_pricesystem as hicp on hicp.combo_details_id = hicd.id");
		sql.append(" left join organ o on o.price_system=hicp.price_system||''");
		sql.append(" where hicd.valid_state='1' and hicp.chanel = '").append(channel).append("' and o.id=").append(organId).append(" order by hicd.id asc ");

		logger.debug("getItemComboDetailsByOrganId:::" + sql.toString());

		List<JSONObject> combo_details = this.queryString4Json(tenantId, sql.toString());
		if (combo_details != null) {
			JSONArray comboDetailsArray = JSONArray.fromObject(combo_details);
			object.element("combo_details", comboDetailsArray);
		}

		list.add(object);

		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return list;
	}

	@Override
	public String getItemComboDetailsByOrganIdEx(String tenantId, Integer organId) throws Exception {
		StringBuilder sql = new StringBuilder();
		sql.append(" select distinct hicd.id,hicd.iitem_id,hicd.is_itemgroup,hicd.details_id,hicd.combo_num,coalesce(hicp.price,hicd.standardprice) as standardprice,hicd.combo_order as order,hicd.valid_state,hicd.item_unit_id,hicp.chanel");
		sql.append(" from hq_item_combo_details as hicd");
		sql.append(" left join hq_item_combo_pricesystem as hicp on hicp.combo_details_id = hicd.id");
		sql.append(" left join organ o on o.price_system=hicp.price_system||''");
		sql.append(" where hicd.valid_state='1' and o.id=").append(organId).append(" order by hicd.id asc");

		logger.debug("getItemComboDetailsByOrganId:::" + sql.toString());
		List<Map<String, Object>> dishes = this.jdbcTemplate.queryForList(sql.toString());
		String tmp = org.apache.commons.lang.StringUtils.join(dishes.toArray(), "|");
		return JavaMd5.toMd5B32(tmp);
	}

	@Override
	public List<JSONObject> getItemGroupDetailsByOrganId(String tenantId, Integer organId, String channel) throws Exception {
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject();

		StringBuilder sql = new StringBuilder("select higd.id,higd.item_group_id,higd.item_id,higd.isdefault,higd.makeup_money,higd.quantity_limit,higd.item_unit_id,coalesce(higd.details_order,0) as details_order from hq_item_group_details as higd ");
		sql.append(" left join hq_item_group_pricesystem as higp on higp.item_group_iid = higd.item_group_id ");
		sql.append(" where higp.chanel = '" + channel + "' group by higd.id,higd.item_group_id,higd.item_id,higd.isdefault,higd.makeup_money,higd.quantity_limit,higd.item_unit_id order by higd.id asc ");

		logger.debug("getItemGroupDetailsByOrganId:::" + sql.toString());
		List<JSONObject> item_group_details = this.queryString4Json(tenantId, sql.toString());
		if (item_group_details != null) {
			JSONArray groupDetailsArray = JSONArray.fromObject(item_group_details);
			object.element("item_group_details", groupDetailsArray);
		}

		list.add(object);

		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return list;
	}

	@Override
	public String getItemGroupDetailsByOrganIdEx(String tenantId, Integer organId) throws Exception {
		StringBuilder sql = new StringBuilder("select higd.id,higd.item_group_id,higd.item_id,higd.isdefault,higd.makeup_money,higd.quantity_limit,higd.item_unit_id from hq_item_group_details as higd ");
		sql.append(" left join hq_item_group_pricesystem as higp on higp.item_group_iid = higd.item_group_id ");
		sql.append(" group by higd.id,higd.item_group_id,higd.item_id,higd.isdefault,higd.makeup_money,higd.quantity_limit,higd.item_unit_id order by higd.id asc ");
		logger.debug("getItemGroupDetailsByOrganId:::" + sql.toString());
		List<Map<String, Object>> dishes = this.jdbcTemplate.queryForList(sql.toString());
		String tmp = org.apache.commons.lang.StringUtils.join(dishes.toArray(), "|");
		return JavaMd5.toMd5B32(tmp);
	}

	// 会员卡种
	@Override
	public List<JSONObject> getCrmCardClassByOrganId(String tenantId, Integer organId) throws Exception {
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject();

		StringBuilder sql = new StringBuilder("select tenancy_id,id,code,name,is_only,is_back,is_lostreport,is_namereport,valid_state,remark,last_operator,last_updatetime,card_sale_price,card_deposit,is_physical_card,limit_prestore_per from crm_card_class where tenancy_id='").append(tenantId).append("' and valid_state='1'");

		logger.debug("getCrmCardClassByOrganId:::" + sql.toString());

		List<JSONObject> crm_card_class = this.queryString4Json(tenantId, sql.toString());
		if (crm_card_class != null) {
			JSONArray crmCardClassArray = JSONArray.fromObject(crm_card_class);
			object.element("crm_card_class", crmCardClassArray);
		}

		list.add(object);
		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return list;
	}

	// 会员卡种
	@Override
	public String getCrmCardClassByOrganIdEx(String tenantId, Integer organId) throws Exception {
		StringBuilder sql = new StringBuilder("select tenancy_id,id,code,name,is_only,is_back,is_lostreport,is_namereport,valid_state,remark,last_operator,last_updatetime,card_sale_price,card_deposit,is_physical_card,limit_prestore_per from crm_card_class where tenancy_id='").append(tenantId).append("' and valid_state='1'");

		logger.debug("getCrmCardClassByOrganId:::" + sql.toString());

		List<Map<String, Object>> dishes = this.jdbcTemplate.queryForList(sql.toString());
		String tmp = org.apache.commons.lang.StringUtils.join(dishes.toArray(), "|");
		return JavaMd5.toMd5B32(tmp);
	}

//	// 会员价
//	@Override
//	public List<JSONObject> getCrmItemVipByOrganId(String tenantId, Integer organId) throws Exception
//	{
//		List<JSONObject> list = new ArrayList<JSONObject>();
//		JSONObject object = new JSONObject();
//		
//		StringBuilder sql = new StringBuilder("select tenancy_id,id,big_class_id,big_class_code,big_class_name,small_class_id,small_class_code,small_class_name,item_id,item_code,item_name,unit_id,unit_code,unit_name,standard_price,vip_price,remark from crm_item_vip where tenancy_id='").append(tenantId).append("'");
//		
//		logger.debug("getCrmItemVipByOrganId:::" + sql.toString());
//		
//		List<JSONObject> crm_item_vip = this.queryString4Json(tenantId, sql.toString());
//		if (crm_item_vip != null)
//		{
//			JSONArray crmItemVipArray = JSONArray.fromObject(crm_item_vip);
//			object.element("crm_item_vip", crmItemVipArray);
//		}
//		
//		list.add(object);
//		
//		logger.debug("同步数据，查询返回数据:---" + object.toString());
//		return list;
//	}

	// 会员价
	@Override
	public String getCrmItemVipByOrganIdEx(String tenantId, Integer organId) throws Exception {
		StringBuilder sql = new StringBuilder("select tenancy_id,id,big_class_id,big_class_code,big_class_name,small_class_id,small_class_code,small_class_name,item_id,item_code,item_name,unit_id,unit_code,unit_name,standard_price,vip_price,remark from crm_item_vip where tenancy_id='").append(tenantId).append("'");

		logger.debug("getCrmItemVipByOrganId:::" + sql.toString());

		List<Map<String, Object>> dishes = this.jdbcTemplate.queryForList(sql.toString());
		String tmp = org.apache.commons.lang.StringUtils.join(dishes.toArray(), "|");
		return JavaMd5.toMd5B32(tmp);
	}

	@Override
	public List<JSONObject> getUsersByTenantId(String tenantId, Integer organId) throws Exception {
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject();

		StringBuilder sql = new StringBuilder("select employee_id,user_name,user_type,name as employee_name,password,remark,a.roles_id,a.pos_user_name from user_authority a left join employee b on a.employee_id=b.id and a.tenancy_id=b.tenancy_id and a.store_id=b.store_id ");
		sql.append(" where a.valid_state='1' and a.store_id=? and a.tenancy_id=? and a.user_name<>'' ");

		logger.debug("getUsersByTenantId:::" + sql.toString());

		List<JSONObject> users = this.queryString4Json(tenantId, sql.toString(), new Object[]{organId, tenantId});
		if (users != null) {
			JSONArray usersArray = JSONArray.fromObject(users);
			object.element("user", usersArray);
		}

		list.add(object);

		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return list;
	}

	@Override
	public String getUsersByTenantIdEx(String tenantId, Integer organId) throws Exception {
		StringBuilder sql = new StringBuilder("select employee_id,user_name,user_type,name as employee_name,password,remark,a.roles_id,a.pos_user_name from user_authority a left join employee b on a.employee_id=b.id and a.tenancy_id=b.tenancy_id and a.store_id=b.store_id ");
		sql.append(" where a.valid_state='1' and a.store_id=? and a.tenancy_id=? and a.user_name<>'' ");

		logger.debug("getUsersByTenantId:::" + sql.toString());

		List<Map<String, Object>> dishes = this.jdbcTemplate.queryForList(sql.toString(), new Object[]{organId, tenantId});
		String tmp = org.apache.commons.lang.StringUtils.join(dishes.toArray(), "|");
		return JavaMd5.toMd5B32(tmp);
	}

	@Override
	public List<JSONObject> getSysParameter(String tenantId, Integer organId) throws Exception {
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject obj = new JSONObject();
		StringBuilder sql = new StringBuilder("select DISTINCT para_name,para_code,para_value,system_name from sys_parameter where (system_name='CRM' or system_name='POS' or system_name='hq') and (store_id = 0 or store_id = ?) and valid_state = '1'");
		logger.debug("getSysParameter--sql:::" + sql.toString());
		List<PosSysParam> params = this.jdbcTemplate.query(sql.toString(), new Object[]{organId}, BeanPropertyRowMapper.newInstance(PosSysParam.class));
		if (params != null) {
			/** pos端建库需要,没有实际意义  */
			PosSysParam temp = new PosSysParam();
			temp.setPara_name("8ef9e8f294abb8a54c1a8396a04d52d1");
			temp.setPara_code("8ef9e8f294abb8a54c1a8396a04d52d1");
			temp.setPara_value("8ef9e8f294abb8a54c1a8396a04d52d1");
			temp.setSystem_name("8ef9e8f294abb8a54c1a8396a04d52d1");
			params.add(0, temp);
			String shiftsQuery = "select DISTINCT '交班限制次数' as para_name,'setting_shifts_times' as para_code,limit_count as para_value,'POS' as system_name from pos_setting_shifts_times where tenancy_id =? and (store_id = 0 or store_id = ?) ";
			params = this.buildPosSysParam(tenantId, organId, params, shiftsQuery);
			JSONArray paramsArray = JSONArray.fromObject(params);
			obj.element("sys_parameter", paramsArray);
		}
		list.add(obj);
		logger.debug("同步数据，查询返回数据:--getSysParameter:::" + obj.toString());
		return list;
	}

	public List<PosSysParam> buildPosSysParam(String tenantId, Integer organId, List<PosSysParam> params, String sql) throws Exception {
		List<PosSysParam> result = new ArrayList<>();
		if (sql.length() > 0) {
			result = this.jdbcTemplate.query(sql.toString(), new Object[]{tenantId, organId}, BeanPropertyRowMapper.newInstance(PosSysParam.class));
		}
		if (!result.isEmpty()) {
			params.addAll(result);
		}
		return params;
	}

	@Override
	public String getSysParameterEx(String tenantId, Integer organId) throws Exception {
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject obj = new JSONObject();
		StringBuilder sql = new StringBuilder("select DISTINCT para_name,para_code,para_value,system_name from sys_parameter where (system_name='CRM' or system_name='POS' or system_name='hq') and (store_id = 0 or store_id = ?) and valid_state = '1'");
		logger.debug("getSysParameter--sql:::" + sql.toString());
		List<PosSysParam> params = this.jdbcTemplate.query(sql.toString(), new Object[]{organId}, BeanPropertyRowMapper.newInstance(PosSysParam.class));
		if (params != null) {
			/** pos端建库需要,没有实际意义  */
			PosSysParam temp = new PosSysParam();
			temp.setPara_name("8ef9e8f294abb8a54c1a8396a04d52d1");
			temp.setPara_code("8ef9e8f294abb8a54c1a8396a04d52d1");
			temp.setPara_value("8ef9e8f294abb8a54c1a8396a04d52d1");
			temp.setSystem_name("8ef9e8f294abb8a54c1a8396a04d52d1");
			params.add(0, temp);
			String shiftsQuery = "select DISTINCT '交班限制次数' as para_name,'setting_shifts_times' as para_code,limit_count as para_value,'POS' as system_name from pos_setting_shifts_times where tenancy_id =? and (store_id = 0 or store_id = ?) ";
			params = this.buildPosSysParam(tenantId, organId, params, shiftsQuery);
			JSONArray paramsArray = JSONArray.fromObject(params);
			obj.element("sys_parameter", paramsArray);
		}
		list.add(obj);
		logger.debug("同步数据，查询返回数据:--getSysParameter:::" + obj.toString());
		return JavaMd5.toMd5B32(list.toString());
	}


	@Override
	public List<JSONObject> getDevicesByOrgan(String tenantId, Integer organId) throws Exception {
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject obj = new JSONObject();

		StringBuilder sql = new StringBuilder("select id,business_area,devices_code,devices_name,devices_ip,show_type,show_port,remark,authorize_code,devices_properties, is_site as issite, (case when is_site = '1'  and ( select max(is_open_cashbox) from hq_printer_new box where box.tenancy_id = dev.tenancy_id and box.store_id = dev.store_id and box.site_id = dev.id ) = '1' then '1' else '0' end) is_site from hq_devices AS dev where valid_state = '1' and store_id = ? ");
		List<BasicDevice> devices = this.jdbcTemplate.query(sql.toString(), new Object[]{organId}, BeanPropertyRowMapper.newInstance(BasicDevice.class));
		if (devices != null) {
			JSONArray paramsArray = JSONArray.fromObject(devices);
			obj.element("devices", paramsArray);
		}
		logger.debug("同步数据，查询返回数据:--getDevices:::" + obj.toString());
		list.add(obj);
		return list;
	}

	@Override
	public String getDevicesByOrganEx(String tenantId, Integer organId) throws Exception {
		StringBuilder sql = new StringBuilder("select id,business_area,devices_code,devices_name,devices_ip,show_type,show_port,remark,authorize_code,devices_properties, is_site as issite, (case when is_site = '1'  and ( select max(is_open_cashbox) from hq_printer_new box where box.tenancy_id = dev.tenancy_id and box.store_id = dev.store_id and box.site_id = dev.id ) = '1' then '1' else '0' end) is_site from hq_devices AS dev where valid_state = '1' and store_id = ? ");
		List<Map<String, Object>> dishes = this.jdbcTemplate.queryForList(sql.toString(), new Object[]{organId});
		String tmp = org.apache.commons.lang.StringUtils.join(dishes.toArray(), "|");
		return JavaMd5.toMd5B32(tmp);
	}

	@Override
	public List<JSONObject> getUserDiscountCaseByOrgan(String tenantId, Integer organId) throws Exception {
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject obj = new JSONObject();

		StringBuilder sql = new StringBuilder(
				"select dc.id,dc.employee_id,dc.discount_id,dc.discount_authority_id,da.discount_type from user_discount_case dc left join user_discount_authority da on dc.tenancy_id=da.tenancy_id and dc.discount_authority_id=da.id and dc.employee_id=da.employee_id where da.tenancy_id=?");
		List<BasicDataUserDiscountCase> devices = this.jdbcTemplate.query(sql.toString(), new Object[]
				{tenantId}, BeanPropertyRowMapper.newInstance(BasicDataUserDiscountCase.class));
		if (devices != null) {
			JSONArray paramsArray = JSONArray.fromObject(devices);
			obj.element("user_discount_case", paramsArray);
		}
		logger.debug("同步数据，查询返回数据:--getUserDiscountCaseByOrgan:::" + obj.toString());
		list.add(obj);
		return list;
	}

	@Override
	public String getUserDiscountCaseByOrganEx(String tenantId, Integer organId) throws Exception {
		StringBuilder sql = new StringBuilder(
				"select dc.id,dc.employee_id,dc.discount_id,dc.discount_authority_id,da.discount_type from user_discount_case dc left join user_discount_authority da on dc.tenancy_id=da.tenancy_id and dc.discount_authority_id=da.id and dc.employee_id=da.employee_id where da.tenancy_id=?");
		List<Map<String, Object>> dishes = this.jdbcTemplate.queryForList(sql.toString(), new Object[]{tenantId});
		String tmp = org.apache.commons.lang.StringUtils.join(dishes.toArray(), "|");
		return JavaMd5.toMd5B32(tmp);
	}

	@Override
	public List<JSONObject> getUserDiscountAuthorityByOrgan(String tenantId, Integer organId) throws Exception {
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject obj = new JSONObject();

		StringBuilder sql = new StringBuilder("select id,employee_id,user_id,fixed_discount_limit,fall_discount_limit,single_discount_limit,is_discount_case,discount_type from user_discount_authority where tenancy_id=? and (store_id =? or store_id is NULL)");
		List<BasicDataUserDiscountAuthority> devices = this.jdbcTemplate.query(sql.toString(), new Object[]
				{tenantId, organId}, BeanPropertyRowMapper.newInstance(BasicDataUserDiscountAuthority.class));
		if (devices != null) {
			JSONArray paramsArray = JSONArray.fromObject(devices);
			obj.element("user_discount_authority", paramsArray);
		}
		logger.debug("同步数据，查询返回数据:--getUserDiscountAuthorityByOrgan:::" + obj.toString());
		list.add(obj);
		return list;
	}

	@Override
	public String getUserDiscountAuthorityByOrganEx(String tenantId, Integer organId) throws Exception {
		StringBuilder sql = new StringBuilder("select id,employee_id,user_id,fixed_discount_limit,fall_discount_limit,single_discount_limit,is_discount_case,discount_type from user_discount_authority where tenancy_id=? and (store_id =? or store_id is NULL)");
		List<Map<String, Object>> dishes = this.jdbcTemplate.queryForList(sql.toString(), new Object[]{tenantId, organId});
		String tmp = org.apache.commons.lang.StringUtils.join(dishes.toArray(), "|");
		return JavaMd5.toMd5B32(tmp);
	}

	@Override
	public void systemConfig(String mac, Integer organId, String tableCode) throws SystemException {
		//根据mac清空所有和这个设备绑定的设备编号
		StringBuilder usql = new StringBuilder("update tables_info set pad_mac = '' where pad_mac = ? and organ_id = ? ");
		//根据table_code绑定设备
		StringBuilder u2sql = new StringBuilder("update tables_info set pad_mac = ? where table_code = ? and organ_id = ? ");

		if (StringUtils.isNotEmpty(tableCode)) {
			StringBuilder tableSql = new StringBuilder("select count(tenancy_id) from tables_info");
			tableSql.append(" where table_code = ? and organ_id = ?");

			int tableCount = this.jdbcTemplate.queryForObject(tableSql.toString(), Integer.class, new Object[]{tableCode, organId});
			if (tableCount == 0) {
				throw new SystemException(PosErrorCode.NOT_EXISTS_TABLE_CODE);
			}

			try {
				this.jdbcTemplate.update(usql.toString(), new Object[]{mac, organId});
				this.jdbcTemplate.update(u2sql.toString(), new Object[]{mac, tableCode, organId});
			} catch (Exception se) {
				se.printStackTrace();
				logger.error("系统设置失败，原因：" + ExceptionMessage.getExceptionMessage(se));
			}
		}
	}

	@Override
	public List<PosTableFind> findTable(String tenantId, Integer organId, String tableNo, Integer tablePropertyId, String tableState, Integer businessAreaId) throws SystemException {
		List<PosTableFind> tableInfos = null;

//		StringBuffer str = new StringBuffer("select * from (select (case when pt.state is null then 'FREE' else pt.state end) state,t1.valid_state,t1.table_code,b.billno,b.opentabletime,b.service_id,b.guest,b.amt,t1.seat_counts,b.printtimes,t1.business_area_id,t1.table_property_id,t1.table_fw,t1.table_name,t1.organ_id,pt.lock_pos_num,pt.lock_opt_num from tables_info t1 ");
//		str.append(" left join (select pb.table_code,pb.bill_num as billno,pb.opentable_time as opentabletime,pb.service_id,pb.guest,ROUND(pb.payment_amount,2) as amt ,pb.print_count as printtimes from pos_bill pb where pb.bill_property='OPEN' and store_id = ?) b on t1.table_code=b.table_code left join pos_tablestate pt on t1.table_code = pt.table_code and t1.organ_id = pt.store_id ) k ");
//		
		StringBuffer str = new StringBuffer("select * from (select pt.stable,pt.state as ptstate,pt.table_open_num,pt.tenancy_id,")
				.append("CASE WHEN pt.table_open_num is  NULL THEN t1.valid_state ELSE pti.valid_state END as valid_state,")
				.append("pt.table_code,")
				.append("CASE WHEN pt.table_open_num  IS NULL THEN t1.seat_counts  else pti.seat_counts  end as seat_counts,")
				.append("CASE WHEN pt.table_open_num  IS NULL THEN t1.business_area_id  else pti.business_area_id  end as business_area_id,")
				.append("CASE WHEN pt.table_open_num  IS NULL THEN t1.table_property_id  else pti.table_property_id  end as table_property_id,")
				.append("CASE WHEN pt.table_open_num  IS NULL THEN t1.table_fw  else pti.table_fw  end as table_fw,")
				.append("CASE WHEN pt.table_open_num  IS NULL THEN t1.TABLE_NAME else pti.TABLE_NAME ||'_' || pt.table_open_num end as TABLE_NAME,")
				.append("pt.store_id as organ_id,")
				.append("pb.bill_num as billno,pb.opentable_time as opentabletime,pb.service_id,pb.guest,ROUND(pb.payment_amount,2) as amt,pb.print_count as printtimes,pt.lock_pos_num,pt.lock_opt_num,COALESCE(pt.state,'" + SysDictionary.TABLE_STATE_FREE + "') as state,pb.payment_state,pt.last_updatetime as locked_time ")
				.append(" from pos_tablestate pt left join tables_info t1 on t1.organ_id = pt.store_id and t1.table_code = pt.table_code left join tables_info pti  on pt.stable =pti.table_code left join pos_bill pb on pb.store_id = pt.store_id and pt.table_code=pb.fictitious_table   and pb.bill_property='" + SysDictionary.BILL_PROPERTY_OPEN + "') k ");
		//用for循环的话还要在switch里判断是否为空
		if (StringUtils.isNotEmpty(tableNo)) {
			//str.append(" where ((K . STABLE IS NULL AND k.table_open_num is null) OR (K . STABLE IS NOT NULL  AND k.table_open_num is not null and k.ptstate = 'BUSY'))");
			str.append(" where  k.valid_state = '1' and k.table_code = ? and k.tenancy_id= ? and k.organ_id = ?" + " order by k.table_code asc");
			tableInfos = this.jdbcTemplate.query(str.toString(), new Object[]{tableNo, tenantId, organId}, BeanPropertyRowMapper.newInstance(PosTableFind.class));
			return tableInfos;
		} else if (tablePropertyId != null) {
			str.append(" where ((K . STABLE IS NULL AND k.table_open_num is null) OR (K . STABLE IS NOT NULL  AND k.table_open_num is not null and k.ptstate = '" + SysDictionary.TABLE_STATE_BUSY + "'))");
			str.append(" and  k.valid_state = '1' and k.table_property_id = ? and k.tenancy_id= ? and k.organ_id = ? order by k.table_code asc");
			tableInfos = this.jdbcTemplate.query(str.toString(), new Object[]{tablePropertyId, tenantId, organId}, BeanPropertyRowMapper.newInstance(PosTableFind.class));
			return tableInfos;
		} else if (StringUtils.isNotEmpty(tableState)) {
			str.append(" where ((K . STABLE IS NULL AND k.table_open_num is null) OR (K . STABLE IS NOT NULL  AND k.table_open_num is not null and k.ptstate = '" + SysDictionary.TABLE_STATE_BUSY + "'))");
			str.append(" and  k.valid_state = '1' and k.state = ? and k.tenancy_id= ? and k.organ_id = ? order by k.table_code asc");
			tableInfos = this.jdbcTemplate.query(str.toString(), new Object[]{tableState, tenantId, organId}, BeanPropertyRowMapper.newInstance(PosTableFind.class));
			return tableInfos;
		} else if (businessAreaId != null) {
			str.append(" where ((K . STABLE IS NULL AND k.table_open_num is null) OR (K . STABLE IS NOT NULL  AND k.table_open_num is not null and k.ptstate = '" + SysDictionary.TABLE_STATE_BUSY + "'))");
			str.append(" and  k.valid_state = '1' and k.business_area_id = ? and k.tenancy_id= ? and k.organ_id = ? order by k.table_code asc");
			tableInfos = this.jdbcTemplate.query(str.toString(), new Object[]{businessAreaId, tenantId, organId}, BeanPropertyRowMapper.newInstance(PosTableFind.class));
			return tableInfos;
		} else {
			str.append(" where ((K . STABLE IS NULL AND k.table_open_num is null) OR (K . STABLE IS NOT NULL  AND k.table_open_num is not null and k.ptstate = '" + SysDictionary.TABLE_STATE_BUSY + "'))");
			str.append(" and  k.valid_state = '1' and k.tenancy_id= ? and k.organ_id = ? order by k.table_code asc");
			tableInfos = this.jdbcTemplate.query(str.toString(), new Object[]{tenantId, organId}, BeanPropertyRowMapper.newInstance(PosTableFind.class));
			return tableInfos;
		}
	}

	@Override
	public void changeTable(String tenantId, Integer organId, String tableCode, String state) throws SystemException {
		StringBuilder csql = new StringBuilder("update pos_tablestate set state = ? where table_code = ? and store_id = ? and tenancy_id = ?");
		if (SysDictionary.TABLE_STATE_BUSY.equals(state)) {
			csql.append(" and state='").append(SysDictionary.TABLE_STATE_FREE).append("'");
		}
		this.jdbcTemplate.update(csql.toString(), new Object[]{state, tableCode, organId, tenantId});
	}

	@Override
	public synchronized void lockOrUnlockTable(String tenantId, Integer organId, String optNum, String posNum, String tableCode, String mode, Data result) throws SystemException {
		StringBuffer sql = new StringBuffer("update pos_tablestate set lock_opt_num=?,lock_pos_num=? where table_code = ? and tenancy_id=? and store_id=?");

		if (StringUtils.isNotEmpty(mode)) {
			switch (mode) {
				case "0":
					logger.debug("锁定桌位：" + sql.toString());
					this.jdbcTemplate.update(sql.toString(), new Object[]{optNum, posNum, tableCode, tenantId, organId});
					result.setCode(Constant.CODE_SUCCESS);
					result.setMsg(Constant.LOCK_TABLE_SUCCESS);
					break;
				case "1":
					logger.debug("解锁桌位：" + sql.toString());
					this.jdbcTemplate.update(sql.toString(), new Object[]{null, null, tableCode, tenantId, organId});
					result.setCode(Constant.CODE_SUCCESS);
					result.setMsg(Constant.UNLOCK_TABLE_SUCCESS);
					break;
				default:
					logger.debug("mode的值不为0或1，传入的mode值为：" + mode);
					throw new SystemException(PosErrorCode.MODE_NOT_MATCH_ERROR);
			}
		} else {
			throw new SystemException(PosErrorCode.NOT_NULL_MODE);
		}
	}

	@Override
	@Deprecated
	public Data posCopyTable(String tenantId, Integer organId, List<Map<String, Object>> listParams, PosLog log, PosCodeService codeService) throws SystemException {
		Data rData = new Data();
		return rData;
	}

	@Override
	@Deprecated
	public synchronized Data singleChangeTable(String tenantId, Integer organId, Map<String, Object> map) throws SystemException {
		Data rData = new Data();

		return rData;
	}


//	/**
//	 * gj20160819
//	 * @param reportDate
//	 * @param storeId
//	 * @param tenantId
//	 */
//	public void checkCashierReceive(Date reportDate,Integer storeId, String tenantId) throws Exception
//	{
//		String tableMode = getSysParameter(tenantId, storeId, "ZCDCMS");
//		//自助模式不需要检测交款
//		if ("02".equals(tableMode))
//		{
//			return;
//		}
////		String sql = " SELECT (coalesce(payamount,0) - coalesce(receiveamount,0)) as amount from ( "+
////	                 "            SELECT SUM (coalesce(pbp.currency_amount,0)) AS payamount,pbp.tenancy_id,pbp.store_id,"+
////				     "            pbp.report_date FROM  pos_bill_payment AS pbp "+
////	                 "            WHERE pbp.tenancy_id = ?  AND pbp.store_id = ?  AND pbp.report_date =  ? "+
////				     "                AND EXISTS ( SELECT devices_code FROM hq_devices AS hd WHERE (show_type = 'YD' OR show_type = 'PAD')"+
////	                 "                AND hd.devices_code = pbp.pos_num) "+
////				     "             GROUP BY pbp.tenancy_id,pbp.store_id,pbp.report_date"+
////				     "        ) pbp left join( "+
////				     "          SELECT SUM (coalesce(pcr.amount,0)) AS receiveamount,pcr.tenancy_id,pcr.store_id,pcr.report_date"+
////				     "          FROM pos_cashier_receive_log AS pcr WHERE pcr.tenancy_id = ? AND pcr.store_id = ? AND pcr.report_date = ?          "+				     
////				     "          GROUP BY pcr.tenancy_id,pcr.store_id,pcr.report_date"+
////				     "        )  pcrl on pbp.tenancy_id = pcrl.tenancy_id and  pbp.store_id = pcrl.store_id and pbp.report_date = pcrl.report_date";
////		SqlRowSet rs = query4SqlRowSet(sql, new Object[]
////				{ tenantId, storeId, reportDate, tenantId, storeId, reportDate });
////		if (rs.next())
////		{
////			double amount = rs.getDouble("amount");
////			if (amount > 0)
////			{
////				throw new SystemException(PosErrorCode.DAYBEGAIN_RECEIVE_ADD_FAILURE);
////			}
////	    }
//		
//		StringBuilder condition = new StringBuilder();
//		condition.append("pbp.tenancy_id = '").append(tenantId).append("' and pbp.store_id = '").append(storeId).append("' and pbp.report_date='").append(DateUtil.formatDate(reportDate)).append("'");
//		
//		StringBuilder receiveSql = new StringBuilder();
//		receiveSql.append(" select (coalesce(sum(bl.bill_amount),0)-coalesce(sum(pcrl.receive_amount),0)) as difference_amount");
//		receiveSql.append(" from (select bi.tenancy_id,bi.store_id,bi.report_date,bi.shift_id,cast(bi.cashier_num as int) as waiter_num,coalesce(sum(pbp.currency_amount),0) AS bill_amount from pos_bill_payment pbp left join pos_bill bi on pbp.tenancy_id=bi.tenancy_id and pbp.store_id=bi.store_id and pbp.bill_num=bi.bill_num left join hq_devices hd on bi.tenancy_id = hd.tenancy_id and bi.store_id = hd.store_id and bi.pos_num = hd.devices_code where ").append(condition).append(" and (hd.show_type = 'YD' or hd.show_type = 'PAD') group by bi.tenancy_id,bi.store_id,bi.report_date,bi.shift_id,bi.cashier_num) as bl");
//		receiveSql.append(" left join (select pbp.tenancy_id,pbp.store_id,pbp.report_date,pbp.pay_shift_id as shift_id,pbp.waiter_id,sum(pbp.amount) as receive_amount from pos_cashier_receive_log pbp where ").append(condition).append(" group by pbp.tenancy_id,pbp.store_id,pbp.report_date,pbp.pay_shift_id,pbp.waiter_id) as pcrl on bl.tenancy_id = pcrl.tenancy_id and bl.store_id = pcrl.store_id and bl.report_date = pcrl.report_date and bl.shift_id = pcrl.shift_id and bl.waiter_num = pcrl.waiter_id"); 
//		receiveSql.append(" left join employee em  on bl.tenancy_id = em.tenancy_id and bl.store_id = em.store_id and bl.waiter_num = em.id");
//		receiveSql.append(" left join (select doo.tenancy_id,doo.organ_id,dor.id as shift_id,sd.class_item as shift_name from duty_order as dor left join duty_order_of_ogran as doo on dor.tenancy_id = doo.tenancy_id and dor.id = doo.duty_order_id left join sys_dictionary sd on  dor.tenancy_id = sd.tenancy_id and dor.name = sd.class_item_code and sd.class_identifier_code ='duty') du on bl.tenancy_id = du.tenancy_id and bl.store_id = du.organ_id and bl.shift_id = du.shift_id");
//		
//		SqlRowSet rs = this.query4SqlRowSet(receiveSql.toString());
//		if (rs.next())
//		{
//			// 交款差额合计>0的时候不可签退
//			if (rs.getDouble("difference_amount") > 0)
//			{
//				throw new SystemException(PosErrorCode.DAYBEGAIN_RECEIVE_ADD_FAILURE);
//			}
//		}
//	}

	@Override
	public void dayReport(String tenantId, Integer organId, Map<String, Object> map, Data result) throws SystemException {
		try {
			Date reportDate = ParamUtil.getDateValue(map, "report_date", false, null);
			Integer shiftId = ParamUtil.getIntegerValue(map, "shift_id", true, PosErrorCode.NOT_NULL_SHIFT_ID);
			String posNum = ParamUtil.getStringValue(map, "pos_num", false, null);
			String optNum = ParamUtil.getStringValue(map, "opt_num", false, null);

//			checkCashierReceive(reportDate, organId, tenantId);

//			this.checkDayEnd(reportDate, posNum, null, organId);


			String daysql = new String("select count(id) as count,bill_num,open_opt,open_pos_num from pos_bill where report_date = ? and (bill_property='OPEN' or bill_property='SORT') and store_id = ? group by bill_num,open_opt,open_pos_num");
			SqlRowSet rsb = this.jdbcTemplate.queryForRowSet(daysql, new Object[]{reportDate, organId});
			if (rsb.next()) {
				int dcount = rsb.getInt("count");
				String bill_num = rsb.getString("bill_num");
				String open_opt = rsb.getString("open_opt");
				String open_pos_num = rsb.getString("open_pos_num");
				if (dcount > 0) {
					result.setCode(Constant.CODE_INNER_EXCEPTION);
					String openOptName = this.getEmpNameById(open_opt, tenantId, organId);
					result.setMsg("有未结账单，不能打烊！未结账单号：" + bill_num + ",操作员：" + openOptName + ",机台号：" + open_pos_num);
					result.setSuccess(false);
					return;
				}
			}


			//		String dayEnd = "DAYEND";
			//		Timestamp time = DateUtil.currentTimestamp();
			//		String tag = "0";

			Timestamp endTime = DateUtil.currentTimestamp();

			String qsql = new String("select count(id) from pos_opt_state where report_date = ? and content=? and store_id = ?");
			int dayCount = this.jdbcTemplate.queryForObject(qsql, Integer.class, new Object[]
					{reportDate, SysDictionary.OPT_STATE_DAYEND, organId});

			if (dayCount > 0) {
				throw new SystemException(PosErrorCode.HAD_DAY_END);
			} else {
				String qOptNum = new String("select opt_num,last_updatetime from pos_opt_state where report_date=? and content='KSSY' and tag='0' and store_id=? order by id desc");

				String qBlCount = new String("select count(id) from pos_bill where report_date=? and payment_time > ? and payment_time < ? and cashier_num=? and store_id=?");


				SqlRowSet rst = this.jdbcTemplate.queryForRowSet(qOptNum, new Object[]{reportDate, organId});
				/**
				 * 如果用户没有退出且有账单记录，就不能让打烊 
				 */
				while (rst.next()) {
					String optName = rst.getString("opt_num");
					Timestamp timestamp = rst.getTimestamp("last_updatetime");
					int billCount = this.jdbcTemplate.queryForObject(qBlCount, Integer.class, new Object[]{reportDate, timestamp, endTime, optName, organId});
					if (billCount > 0) {
						result.setCode(Constant.CODE_INNER_EXCEPTION);
						//GJ20160325
						String openOptName = this.getEmpNameById(optName, tenantId,
								organId);
						result.setMsg("操作员:" + openOptName + ",未交班，不能打烊");
						return;
					}
				}
			}

			String orderStatusSql = new String("select count(id) as count,order_code from cc_order_list where order_state not in ('08','10','12','21','22','23','24') and store_id = ? and tenancy_id = ?  and report_date= ? group by order_code");
			SqlRowSet orderStatusSb = this.jdbcTemplate.queryForRowSet(orderStatusSql, new Object[]{organId, tenantId, reportDate});
			if (orderStatusSb.next()) {
				int count = orderStatusSb.getInt("count");
				String order_code = orderStatusSb.getString("order_code");
				if (count > 0) {
//					result.setCode(Constant.CODE_INNER_EXCEPTION);
//					result.setMsg("有未完成订单，不能打烊！未完成订单号：" + order_code);
//					result.setSuccess(false);
//					return ;
					throw SystemException.getInstance(PosErrorCode.ORDER_UNFINISHED_TO_NOT_DAYEND_ERROR).set("{0}", order_code);
				}
			}

			//取数据
			JSONObject mqObj = new JSONObject();
			String dateTime = DateUtil.format(reportDate);

			StringBuilder mqsql = new StringBuilder("select 'billCount' as k, count(*) as v from pos_bill where report_date='" + dateTime + "' and bill_property='CLOSED' and store_id=" + organId + " UNION all");
			mqsql.append(" select 'billItemsCount' as k, count(*) as v from pos_bill_item where report_date='" + dateTime + "' and store_id=" + organId + " UNION all ");
			mqsql.append(" select 'paymentCount' as k, count(*) as v from pos_bill_payment where report_date='" + dateTime + "' and store_id=" + organId + " UNION all ");
			mqsql.append(" select 'paymentAmount' as k, sum(payment_amount) as v from pos_bill where report_date='" + dateTime + "' and bill_property='CLOSED' and store_id=" + organId + " UNION all");
			mqsql.append(" select 'billCount' as k, count(*) as v from pos_bill2 where report_date='" + dateTime + "' and bill_property='CLOSED' and store_id=" + organId + " UNION all");
			mqsql.append(" select 'billItemsCount' as k, count(*) as v from pos_bill_item2 where report_date='" + dateTime + "' and store_id=" + organId + " UNION all ");
			mqsql.append(" select 'paymentCount' as k, count(*) as v from pos_bill_payment2 where report_date='" + dateTime + "' and store_id=" + organId + " UNION all ");
			mqsql.append(" select 'paymentAmount' as k, sum(payment_amount) as v from pos_bill2 where report_date='" + dateTime + "' and bill_property='CLOSED' and store_id=" + organId);

			SqlRowSet mqRs = this.jdbcTemplate.queryForRowSet(mqsql.toString());
			while (mqRs.next()) {
				if (mqObj.containsKey(mqRs.getString("k"))) {
					mqObj.put(mqRs.getString("k"), mqObj.optDouble(mqRs.getString("k")) + mqRs.getDouble("v"));
				} else {
					mqObj.put(mqRs.getString("k"), mqRs.getDouble("v"));
				}
			}
			//取数据

			//写机台状态库
			//Gj20160307
//			String empName = this.getEmpNameById(optNum, tenantId, organId);
//			StringBuilder sql = new StringBuilder("insert into pos_opt_state (tenancy_id,store_id,pos_num,content,opt_num,opt_name,report_date,last_updatetime,tag,login_number,remark,upload_tag) values (?,?,?,?,?,?,?,?,?,?,?,'0')");
//			this.jdbcTemplate.update(sql.toString(),new Object[]{tenantId,organId,posNum,SysDictionary.OPT_STATE_DAYEND,optNum,empName,reportDate,time,tag,null,"$$$" + mqObj.toString()});
			this.saveOptState(tenantId, organId, reportDate, shiftId, posNum, optNum, SysDictionary.OPT_STATE_DAYEND, null, "$$$" + mqObj.toString(), null, endTime);
			//清除沽清数据
			StringBuilder soldstr = new StringBuilder("delete from pos_soldout where store_id = ?  and soldout_type = ?");
			this.jdbcTemplate.update(soldstr.toString(), new Object[]{organId, "0"});
			//清除急推菜品数据
			StringBuilder worrystr = new StringBuilder("delete from pos_item_worrysale where store_id = ?");
			this.jdbcTemplate.update(worrystr.toString(), new Object[]{organId});
//			//
//			StringBuilder uposb = new StringBuilder("insert into pos_bill2 (tenancy_id,id,store_id,bill_num,batch_num,serial_num,report_date,table_code,guest,opentable_time,payment_time,payment_num,open_pos_num,pos_num,waiter_num,open_opt,cashier_num,shift_id,item_menu_id,service_id,service_amount,service_discount,order_num,print_time,print_count,subtotal,bill_amount,payment_amount,difference,discountk_amount,discountr_amount,maling_amount,single_discount_amount,discount_amount,givi_amount,more_coupon,average_amount,discount_num,discount_case_id,discount_rate,billfree_reason_id,discount_mode_id,transfer_remark,sale_mode,bill_state,bill_property,upload_tag,deposit_count,copy_bill_num,source,opt_login_number,guest_msg,remark,free_amount,return_amount,payment_state) ");
//		    uposb.append(" select tenancy_id,id,store_id,bill_num,batch_num,serial_num,report_date,table_code,guest,opentable_time,payment_time,payment_num,open_pos_num,pos_num,waiter_num,open_opt,cashier_num,shift_id,item_menu_id,service_id,service_amount,service_discount,order_num,print_time,print_count,subtotal,bill_amount,payment_amount,difference,discountk_amount,discountr_amount,maling_amount,single_discount_amount,discount_amount,givi_amount,more_coupon,average_amount,discount_num,discount_case_id,discount_rate,billfree_reason_id,discount_mode_id,transfer_remark,sale_mode,bill_state,bill_property,upload_tag,deposit_count,copy_bill_num,source,opt_login_number,guest_msg,remark,free_amount,return_amount,payment_state from pos_bill where report_date = ? and store_id = ? and tenancy_id = ? ");
//		    this.jdbcTemplate.update(uposb.toString(),new Object[]{reportDate,organId,tenantId});
//		    //
//		    StringBuilder uposbi = new StringBuilder("insert into pos_bill_item2 (tenancy_id,store_id,rwid,yrwid,bill_num,details_id,item_id,item_num,item_name,item_english,item_unit_id,item_unit_name,stable_code,table_code,pushmoney_way,proportion,assist_num,assist_money,waiter_num,item_price,item_count,item_amount,real_amount,discount_amount,single_discount_amount,discountr_amount,discount_state,discount_rate,discount_mode_id,item_class_id,item_property,item_remark,print_tag,waitcall_tag,setmeal_id,setmeal_rwid,is_setmeal_changitem,item_time,item_serial,report_date,item_shift_id,item_mac_id,item_taste,order_remark,seat_num,ticket_num,sale_mode,gqcj_tag,kvscp_tag,discount_reason_id,is_showitem,upload_tag,remark,method_money,returngive_reason_id,manager_num,batch_num)");
//			uposbi.append(" select tenancy_id,store_id,rwid,yrwid,bill_num,details_id,item_id,item_num,item_name,item_english,item_unit_id,item_unit_name,stable_code,table_code,pushmoney_way,proportion,assist_num,assist_money,waiter_num,item_price,item_count,item_amount,real_amount,discount_amount,single_discount_amount,discountr_amount,discount_state,discount_rate,discount_mode_id,item_class_id,item_property,item_remark,print_tag,waitcall_tag,setmeal_id,setmeal_rwid,is_setmeal_changitem,item_time,item_serial,report_date,item_shift_id,item_mac_id,item_taste,order_remark,seat_num,ticket_num,sale_mode,gqcj_tag,kvscp_tag,discount_reason_id,is_showitem,upload_tag,remark,method_money,returngive_reason_id,manager_num,batch_num from pos_bill_item where report_date = ? and store_id = ? and tenancy_id = ? ");
//		    this.jdbcTemplate.update(uposbi.toString(),new Object[]{reportDate,organId,tenantId});
//		    //
//		    StringBuilder upospay = new StringBuilder("insert into pos_bill_payment2 (tenancy_id,store_id,id,bill_num,table_code,type,jzid,name,name_english,amount,count,number,phone,report_date,shift_id,pos_num,cashier_num,last_updatetime,is_ysk,rate,currency_amount,upload_tag,customer_id,bill_code,remark,batch_num,payment_state) ");
//		    upospay.append(" select tenancy_id,store_id,id,bill_num,table_code,type,jzid,name,name_english,amount,count,number,phone,report_date,shift_id,pos_num,cashier_num,last_updatetime,is_ysk,rate,currency_amount,upload_tag,customer_id,bill_code,remark,batch_num,payment_state from pos_bill_payment where report_date = ? and store_id = ? and tenancy_id = ?");
//		    this.jdbcTemplate.update(upospay.toString(),new Object[]{reportDate,organId,tenantId});

			JSONObject search = new JSONObject();
			search.put("tenancy_id", tenantId);
			search.put("store_id", organId);
			search.put("report_date", dateTime);

			copyDataForTable("pos_bill", "pos_bill2", search, false);
			copyDataForTable("pos_bill_sub", "pos_bill_sub2", search, false); // 账单副表
			copyDataForTable("pos_bill_item", "pos_bill_item2", search, false);
			copyDataForTable("pos_bill_payment", "pos_bill_payment2", search, false);

			String dpospb = new String("delete from pos_bill where report_date = ? and store_id = ? and tenancy_id = ? ");
			this.jdbcTemplate.update(dpospb.toString(), new Object[]{reportDate, organId, tenantId});

			// 删除账单副表
			String dPosBillSub = new String("delete from pos_bill_sub where report_date = ? and store_id = ? and tenancy_id = ? ");
			this.jdbcTemplate.update(dPosBillSub.toString(), new Object[]{reportDate, organId, tenantId});

			String dpospbi = new String("delete from pos_bill_item where report_date = ? and store_id = ? and tenancy_id = ? ");
			this.jdbcTemplate.update(dpospbi.toString(), new Object[]{reportDate, organId, tenantId});

			String dpospbp = new String("delete from pos_bill_payment where report_date = ? and store_id = ? and tenancy_id = ? ");
			this.jdbcTemplate.update(dpospbp.toString(), new Object[]{reportDate, organId, tenantId});

			String dpospi = new String("delete from pos_print_item where store_id=?");
			this.jdbcTemplate.update(dpospi.toString(), new Object[]{organId});

			String syscodeValuesSql = new String("delete from SYS_CODE_VALUES where code_type=?");
			this.jdbcTemplate.update(syscodeValuesSql.toString(), new Object[]{com.tzx.framework.common.constant.Code.BILL_BOOKED_NUM.getType()});
			EhcacheUtil.removeAllElment("currentValue", com.tzx.framework.common.constant.Code.BILL_BOOKED_NUM.getType());//删除预点单号缓存
			String bookedSql = new String("DELETE from pos_bill_booked");
			this.jdbcTemplate.update(bookedSql.toString(), new Object[]{});
			String bookedItemSql = new String("DELETE from pos_bill_item_booked");
			this.jdbcTemplate.update(bookedItemSql.toString(), new Object[]{});
			//当打烊时把未交班的且未有营业数据的人员更新交班状态，为了解决快餐其它人登录提示，未交班的问题
//		    String qnames = new String("select opt_num from pos_opt_state where content = 'KSSY' and tag = '0' and report_date = ? and store_id = ? group by opt_num");
//		    
//		    String uNoShifter = new String("update pos_opt_state set tag='1' where opt_num = ? and tag = '0' and content = 'KSSY' and report_date = ? and store_id = ?");
//		   
//		    SqlRowSet rst = this.jdbcTemplate.queryForRowSet(qnames,new Object[]{reportDate,organId});
//		    
//		    while (rst.next())
//		    {
//		    	String uname = rst.getString("opt_num");
//		    	
//		    	this.jdbcTemplate.update(uNoShifter,new Object[]{uname,reportDate,organId});
//		    }

			savePosLog(tenantId, organId, posNum, optNum, optNum, shiftId, reportDate, Constant.TITLE, "日结", "", "");

			result.setCode(Constant.CODE_SUCCESS);
			result.setMsg(Constant.DAY_END_SUCCESS);

		} catch (SystemException e) {
			throw e;
		} catch (Exception e) {
			logger.error(Constant.DAY_END_FAILURE, e);
			e.printStackTrace();
			result.setCode(Constant.CODE_INNER_EXCEPTION);
			result.setMsg(Constant.DAY_END_FAILURE);
		}
	}

	@Override
	@Deprecated
	public Data dataTrans(String tenantId, Integer organId, Date reportDate) throws SystemException {
		Data data = new Data();

		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject();

		StringBuilder q_bill_sql = new StringBuilder("select * from pos_bill where report_date = ? and store_id = ? ");
		StringBuilder q_bill_item_sql = new StringBuilder("select * from pos_bill_item where report_date = ? and store_id = ? ");
		StringBuilder q_bill_payment_sql = new StringBuilder("select * from pos_bill_payment where report_date = ? and store_id = ? ");
		StringBuilder q_zfkw_item_sql = new StringBuilder("select * from pos_zfkw_item where report_date = ? and store_id = ? ");
		StringBuilder q_opter_changshift_sql = new StringBuilder("select * from pos_opter_changshift where report_date = ? and store_id = ? ");
		StringBuilder q_returngive_item_sql = new StringBuilder("select * from pos_returngive_item where report_date = ? and store_id = ? ");
		try {
			List<JSONObject> bills = this.queryString4Json(tenantId, q_bill_sql.toString(), new Object[]{reportDate, organId});
			List<JSONObject> billItems = this.queryString4Json(tenantId, q_bill_item_sql.toString(), new Object[]{reportDate, organId});
			List<JSONObject> billPayments = this.queryString4Json(tenantId, q_bill_payment_sql.toString(), new Object[]{reportDate, organId});
			List<JSONObject> zfkwItems = this.queryString4Json(tenantId, q_zfkw_item_sql.toString(), new Object[]{reportDate, organId});
			List<JSONObject> changShifts = this.queryString4Json(tenantId, q_opter_changshift_sql.toString(), new Object[]{reportDate, organId});
			List<JSONObject> returnGives = this.queryString4Json(tenantId, q_returngive_item_sql.toString(), new Object[]{reportDate, organId});

			object.put("bill", bills);
			object.put("bill_item", billItems);
			object.put("bill_payment", billPayments);
			object.put("zfkw_item", zfkwItems);
			object.put("opter_changshift", changShifts);
			object.put("returngive_item", returnGives);
			list.add(object);
		} catch (Exception se) {
			se.printStackTrace();
			logger.error("上传数据失败，原因：" + ExceptionMessage.getExceptionMessage(se));
			data.setCode(Constant.CODE_INNER_EXCEPTION);
			data.setMsg(Constant.INNER_ERROR);
			return data;
		}

		data.setCode(Constant.CODE_SUCCESS);
		data.setMsg(Constant.UPLOAD_DATA_SUCCESS);
		data.setData(list);
		return data;
	}

	@Override
	public void findWaiter(String tenantId, Integer organId, String billno, Data result) throws SystemException {
		StringBuilder sql = new StringBuilder("select pb.waiter_num as user_name,pbw.waiter_num from pos_bill pb,pos_bill_waiter pbw ");
		sql.append("where pb.bill_num = pbw.bill_num and pb.bill_num = ? and pb.store_id = ?");
		try {
			List<JSONObject> list = this.queryString4Json(tenantId, sql.toString(), new Object[]{billno, organId});
			result.setCode(Constant.CODE_SUCCESS);
			result.setMsg(Constant.QUERY_WAITER_SUCCESS);
			result.setData(list);
		} catch (Exception se) {
			result.setCode(Constant.CODE_INNER_EXCEPTION);
			result.setMsg(Constant.QUERY_WAITER_FAILURE);
			logger.error("查询服务员失败，原因：" + ExceptionMessage.getExceptionMessage(se));
		}
	}

	@Override
	public Data updateOptStateForChangeShift(String tenantId, Integer organId, String optNum, String posNum, Date reportDate, String isAllPos, Integer shiftId) throws SystemException {
		Data data = new Data();
		//
		StringBuilder sq = new StringBuilder("update pos_opt_state set tag='1' ");
		if ("Y".equalsIgnoreCase(isAllPos)) {
			//sq.append("where opt_num=? and report_date=? and store_id=? and content in ('KSSY','YYDL','YYTC')");
			sq.append("where report_date=? and shift_id = ? and store_id=? and content in ('KSSY','YYDL','YYTC')");
			try {
				//this.jdbcTemplate.update(sq.toString(),new Object[]{optNum,reportDate,organId});
				this.jdbcTemplate.update(sq.toString(), new Object[]{reportDate, shiftId, organId});
			} catch (Exception se) {
				se.printStackTrace();
				data.setCode(Constant.CODE_INNER_EXCEPTION);
				data.setMsg(Constant.INNER_ERROR);
				return data;
			}
		} else {
			//sq.append("where opt_num=? and pos_num=? and report_date=? and store_id=? and content in ('KSSY','YYDL','YYTC')");
			sq.append("where  pos_num=? and  shift_id = ? and report_date=? and store_id=? and content in ('KSSY','YYDL','YYTC')");
			try {
				this.jdbcTemplate.update(sq.toString(), new Object[]{posNum, shiftId, reportDate, organId});
			} catch (Exception se) {
				se.printStackTrace();
				data.setCode(Constant.CODE_INNER_EXCEPTION);
				data.setMsg(Constant.INNER_ERROR);
				return data;
			}
		}

		data.setCode(Constant.CODE_SUCCESS);
		data.setMsg(Constant.CHANGE_SHIFT_SUCCESS);
		return data;
	}

	@Override
	@Deprecated
	public Data getShiftData(String tenantId, Integer organId, Map<String, Object> map) throws SystemException {
		Data data = new Data();

		data.setCode(0);
		data.setMsg("获取交班数据成功");
		return data;
	}

	@Override
	public Data getDishByClass(String tenantId, Integer organId, String sql) throws SystemException {
		Data data = new Data();
		try {
			List<JSONObject> list = new ArrayList<JSONObject>();
			List<JSONObject> dishes = this.queryString4Json(tenantId, sql.toString());
			StringBuilder str = new StringBuilder("select num from pos_soldout where item_id=? and store_id=?");
			for (int i = 0; i < dishes.size(); i++) {
				JSONObject obj = dishes.get(i);
				Double count = null;
				int item_id = obj.getInt("item_id");
				SqlRowSet rs = this.jdbcTemplate.queryForRowSet(str.toString(), new Object[]{item_id, organId});
				while (rs.next()) {
					count = rs.getDouble("num");
				}
				if (count == null) {
					obj.put("guqing_count", "");
				} else {
					obj.put("guqing_count", count + "");
				}

				list.add(obj);
			}
			data.setCode(Constant.CODE_SUCCESS);
			data.setMsg(Constant.GET_DISH_SUCCESS);
			data.setData(list);
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("Exception:" + ExceptionMessage.getExceptionMessage(e));
			throw new SystemException(PosErrorCode.OPER_ERROR);
		}

		return data;
	}

	@Override
	public List<JSONObject> getCrmInCorporations(String tenantId, Integer organId) throws Exception {
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject();

		StringBuilder sql = new StringBuilder("select cii.id,cii.name,cii.code,cii.phone,cii.address,cii.credit_limit,cii.discount,cii.phonetic_code,cii.five_code,cii.main_balance,cii.reward_balance,cii.arrears,cii.derate_money from crm_incorporation_info cii ");
		sql.append(" left join crm_incorporation_org cio on cii.id = cio.incorporation_id where cio.store_id = " + organId);
		sql.append(" order by cii.id desc");

		logger.debug("getCrmInCorporations:::" + sql.toString());

		List<JSONObject> corporations = this.queryString4Json(tenantId, sql.toString());
		if (corporations != null) {
			JSONArray corporationsArray = JSONArray.fromObject(corporations);
			object.element("crm_incorporation_info", corporationsArray);
		}

		list.add(object);

		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return list;
	}

	@Override
	public String getCrmInCorporationsEx(String tenantId, Integer organId) throws Exception {
		StringBuilder sql = new StringBuilder("select cii.id,cii.name,cii.code,cii.phone,cii.address,cii.credit_limit,cii.discount,cii.phonetic_code,cii.five_code,cii.main_balance,cii.reward_balance,cii.arrears,cii.derate_money from crm_incorporation_info cii ");
		sql.append(" left join crm_incorporation_org cio on cii.id = cio.incorporation_id where cio.store_id = " + organId);
		sql.append(" order by cii.id desc");

		logger.debug("getCrmInCorporations:::" + sql.toString());

		List<Map<String, Object>> dishes = this.jdbcTemplate.queryForList(sql.toString());
		String tmp = org.apache.commons.lang.StringUtils.join(dishes.toArray(), "|");
		return JavaMd5.toMd5B32(tmp);
	}

	@Override
	public List<JSONObject> getCrmInCorporationPersons(String tenantId, Integer organId) throws Exception {
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject();

		StringBuilder sql = new StringBuilder("select cip.incorporation_id,cip.autograph,cip.customer_id from crm_incorporation_person cip ");
		sql.append(" left join crm_incorporation_org cio on cip.incorporation_id = cio.incorporation_id where cio.store_id = " + organId);
		sql.append(" order by cip.incorporation_id desc ");

		logger.debug("getCrmInCorporationPersons:::" + sql.toString());

		List<JSONObject> corpPersons = this.queryString4Json(tenantId, sql.toString());
		if (corpPersons != null) {
			JSONArray corpPersonsArray = JSONArray.fromObject(corpPersons);
			object.element("crm_incorporation_person", corpPersonsArray);
		}

		list.add(object);

		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return list;
	}

	@Override
	public String getCrmInCorporationPersonsEx(String tenantId, Integer organId) throws Exception {
		StringBuilder sql = new StringBuilder("select cip.incorporation_id,cip.autograph,cip.customer_id from crm_incorporation_person cip ");
		sql.append(" left join crm_incorporation_org cio on cip.incorporation_id = cio.incorporation_id where cio.store_id = " + organId);
		sql.append(" order by cip.incorporation_id desc ");

		logger.debug("getCrmInCorporationPersons:::" + sql.toString());

		List<Map<String, Object>> dishes = this.jdbcTemplate.queryForList(sql.toString());
		String tmp = org.apache.commons.lang.StringUtils.join(dishes.toArray(), "|");
		return JavaMd5.toMd5B32(tmp);
	}

	@Cacheable(value = "tableStateCache", key = "#tableCode")
	@Override
	public List<JSONObject> findLockTable(String tenantId, String tableCode, Integer organId) throws Exception {
		StringBuilder sql = new StringBuilder("select state,lock_opt_num,lock_pos_num from pos_tablestate where table_code = ? and store_id = ? and tenancy_id = ?");
		return this.queryString4Json(tenantId, sql.toString(), new Object[]{tableCode, organId, tenantId});
	}

	@Override
	public List<JSONObject> findLockTable(String tenantId, Integer organId, String tableCode) throws Exception {
		StringBuilder sql = new StringBuilder("select t.state, t.lock_opt_num, t.lock_pos_num, t.table_code, i.table_name || '_' || t.table_open_num as table_name from pos_tablestate t left join tables_info i on i.table_code = ( case when t.stable is not null then t.stable else t.table_code end ) and i.tenancy_id = t.tenancy_id and i.organ_id = t.store_id where t.table_code = ? and t.store_id = ? and t.tenancy_id = ?");
		return this.queryString4Json(tenantId, sql.toString(), new Object[]
				{tableCode, organId, tenantId});
	}

	@Override
	@Deprecated
	public void cancGiveItem(String tenantId, Integer organId, String billno, Integer rwid, Data result) throws SystemException {

	}

	@Override
	public void checkPosLogin(String tenantId, Integer organId, Date reportDate, String posNum, String optNum, Data result) throws SystemException {
		String sql = new String("select opt_num,last_updatetime from pos_opt_state where report_date=? and pos_num=? and opt_num=? and content='KSSY' and tag='0' order by id desc limit 1");
		String tcsql = new String("select last_updatetime from pos_opt_state where report_date=? and pos_num=? and opt_num=? and content='YYTC' and tag='0' order by id desc limit 1");

		Timestamp kssyTime = null;
		Timestamp tcTime = null;
		SqlRowSet rs = this.jdbcTemplate.queryForRowSet(sql, new Object[]{reportDate, posNum, optNum});
		if (rs.next()) {
			kssyTime = rs.getTimestamp("last_updatetime");
		}
		if (kssyTime == null) {
			throw new SystemException(PosErrorCode.POS_CASHIER_NOT_SIGNIN_ERROR);
		} else {
			SqlRowSet rst = this.jdbcTemplate.queryForRowSet(tcsql, new Object[]{reportDate, posNum, optNum});
			if (rst.next()) {
				tcTime = rst.getTimestamp("last_updatetime");
			}
			if (tcTime != null) {
				if (kssyTime.getTime() < tcTime.getTime()) {
					throw new SystemException(PosErrorCode.POS_CASHIER_NOT_SIGNIN_ERROR);
				}
			}
		}

		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject obj = new JSONObject();
		obj.element("login_opt_num", optNum);
		result.setCode(Constant.CODE_SUCCESS);
		result.setMsg(Constant.CHECK_POS_LOGIN_SUCCESS);
		result.setData(list);
	}

	@Override
	@Deprecated
	public Date findPosUploadDate(String tenancyId, int storeId) throws Exception {
		String sql = "select para_value from pos_data_version where para_code='AUTOUPLOADDATETIME' and tenancy_id=? and store_id=?";

		SqlRowSet rs = jdbcTemplate.queryForRowSet(sql, new Object[]
				{tenancyId, storeId});
		if (rs.next()) {
			return DateUtil.parseDateAll(rs.getString("para_value"));
		}
		return null;
	}

	@Override
	@Deprecated
	public List<JSONObject> findNotUploadData(String tenancyId, int storeId, JSONObject param) throws Exception {
		String sql = "select trim(para_value) as para_value from sys_parameter where para_code = 'autouploaddatatable';";
		SqlRowSet rs = jdbcTemplate.queryForRowSet(sql);
		if (rs.next()) {
			StringBuffer selectSql = new StringBuffer();
			String[] tableNames = rs.getString("para_value").split(",");
			for (String tableName : tableNames) {
				switch (tableName.toLowerCase()) {
					case "pos_bill": // 打烊前-账单
						selectSql.append("select tenancy_id,store_id,id,'pos_bill' tablename from pos_bill where bill_property='CLOSED' and upload_tag='0'");
						if (param.containsKey("report_date")) {
							selectSql.append(" and report_date='").append(param.optString("report_date")).append("'");
						}
						if (param.containsKey("last_updatetime")) {
							selectSql.append(" and payment_time<'").append(param.optString("last_updatetime")).append("'");
						}
						selectSql.append(" union all ");
						break;
					case "pos_bill_item": // 打烊前-明细
						break;
					case "pos_bill_payment": // 打烊前-付款流水
						break;
					case "pos_zfkw_item": // 做法口味卧单库
						break;
					case "pos_bill_invoice": // 账单发票库
						break;
					case "pos_bill_waiter": // 账单服务员库
						break;
					case "pos_returngive_item": // 退菜奉送流水库
						break;
					case "pos_bill_regain": // 恢复--账单库
						selectSql.append("select tenancy_id,store_id,id,'pos_bill_regain' tablename from pos_bill_regain where bill_property='CLOSED' and upload_tag='0'");
						if (param.containsKey("report_date")) {
							selectSql.append(" and report_date='").append(param.optString("report_date")).append("'");
						}
						if (param.containsKey("last_updatetime")) {
							selectSql.append(" and regain_time<'").append(param.optString("last_updatetime")).append("'");
						}
						selectSql.append(" union all ");
						break;
					case "pos_bill_item_regain": // 恢复--明细
						break;
					case "pos_bill_payment_regain": // 恢复--付款流水库
						break;
					case "pos_byjdc_log": // 备用金大钞日志库
						selectSql.append("select tenancy_id,store_id,id,'pos_byjdc_log' tablename from pos_byjdc_log where upload_tag='0'");
						if (param.containsKey("report_date")) {
							selectSql.append(" and report_date='").append(param.optString("report_date")).append("'");
						}
						if (param.containsKey("last_updatetime")) {
							selectSql.append(" and last_updatetime<'").append(param.optString("last_updatetime")).append("'");
						}
						selectSql.append(" union all ");
						break;
					case "pos_opter_changshift": // 收银员交班金额库
						selectSql.append("select tenancy_id,store_id,id,'pos_opter_changshift' tablename from pos_opter_changshift where upload_tag='0'");
						if (param.containsKey("report_date")) {
							selectSql.append(" and report_date='").append(param.optString("report_date")).append("'");
						}
						if (param.containsKey("last_updatetime")) {
							selectSql.append(" and last_updatetime<'").append(param.optString("last_updatetime")).append("'");
						}
						selectSql.append(" union all ");
						break;
					default:
						break;
				}
			}

			selectSql.setLength(selectSql.length() - 10);

			return this.query4Json(tenancyId, selectSql.toString());
		}
		return null;
	}

	@Override
	public int updateGiveSuggest(String tenancyId, int storeId, JSONObject param) throws Exception {
		String sql = "select id from hq_bill_evaluate where  tenancy_id=? and store_id= ? and bill_num=?";
		SqlRowSet rs = jdbcTemplate.queryForRowSet(sql, new Object[]{tenancyId, storeId, param.optString("bill_num")});
		String updateSql = null;
		Object[] pss = null;
		if (rs.next()) {
			updateSql = "update hq_bill_evaluate set environment=?,service=?,quality=? where id=?";
			pss = new Object[]{param.optInt("environment"), param.optInt("service"), param.optInt("quality"), rs.getInt("id")};
		} else {
			updateSql = "insert into hq_bill_evaluate(tenancy_id,store_id,bill_num,environment,service,quality,report_date) select ? tenancy_id,? store_id,bill_num,? environment,? service,? quality,report_date from pos_bill where bill_num=?";
			pss = new Object[]{tenancyId, storeId, param.optInt("environment"), param.optInt("service"), param.optInt("quality"), param.optString("bill_num")};
		}
		return jdbcTemplate.update(updateSql, pss);
	}

	@Override
	public void insertDataVersion(String tenancyId, int storeId, String systemName, String modelName, String paraName, String paraCode, String paraValue, String paraDefaut, String paraType, String validState, String valuesName, String paraRemark) throws Exception {
		String inssql = "insert into pos_data_version(tenancy_id,store_id,system_name,model_name,para_name,para_code,para_value,para_defaut,para_type,valid_state,values_name,para_remark) values(?,?,?,?,?,?,?,?,?,?,?,?)";
		jdbcTemplate.update(inssql, new Object[]
				{tenancyId, storeId, systemName, modelName, paraName, paraCode, paraValue, paraDefaut, paraType, validState, valuesName, paraRemark});
	}

	@Override
	public void updateDataVersionValueByCode(String tenancyId, int storeId, String paraCode, String paraValue) throws Exception {
		StringBuilder sql = new StringBuilder("update pos_data_version set para_value=? where para_code=? ");
		this.jdbcTemplate.update(sql.toString(), new Object[]{paraValue, paraCode});
	}

	@Override
	public JSONObject getDataVersionByCode(String tenancyId, int storeId, String paraCode) throws Exception {
		StringBuilder sql = new StringBuilder("select * from pos_data_version where para_code=? and store_id=? and tenancy_id=?");
		List<JSONObject> list = this.query4Json(tenancyId, sql.toString(), new Object[]{paraCode, storeId, tenancyId});

		if (null != list && list.size() > 0) {
			return list.get(0);
		}
		return null;
	}

	@Override
	public void updatePosTableState(String tenantId, Integer organId) throws Exception {
		String qtableState = new String("insert into pos_tablestate (tenancy_id,store_id,table_code,state,last_updatetime) select ti.tenancy_id,ti.organ_id,ti.table_code,'FREE',now() from tables_info ti where table_code not in (select table_code from pos_tablestate where store_id = ? and tenancy_id = ? and table_code is not null) and ti.organ_id = ? and tenancy_id = ?");
		this.jdbcTemplate.update(qtableState, new Object[]{organId, tenantId, organId, tenantId});
	}

	@Override
	public List<JSONObject> getItemGroup(String tenancyId, int storeId) throws Exception {
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject obj = new JSONObject();

		StringBuilder sql = new StringBuilder("select id,item_group_code,item_group_name,five_code,phonetic_code,item_group_price,remark from hq_item_group where valid_state = '1' and tenancy_id = ?");
		List<ItemGroup> itemGroup = this.jdbcTemplate.query(sql.toString(), new Object[]{tenancyId}, BeanPropertyRowMapper.newInstance(ItemGroup.class));
		if (itemGroup != null) {
			JSONArray paramsArray = JSONArray.fromObject(itemGroup);
			obj.element("item_group", paramsArray);
		}
		logger.debug("同步数据，查询返回数据:--getDevices:::" + obj.toString());
		list.add(obj);
		return list;
	}

	@Override
	public String getItemGroupEx(String tenancyId, int storeId) throws Exception {
		StringBuilder sql = new StringBuilder("select id,item_group_code,item_group_name,five_code,phonetic_code,item_group_price,remark from hq_item_group where valid_state = '1' and tenancy_id = ?");
		List<Map<String, Object>> dishes = this.jdbcTemplate.queryForList(sql.toString(), new Object[]{tenancyId});
		String tmp = org.apache.commons.lang.StringUtils.join(dishes.toArray(), "|");
		return JavaMd5.toMd5B32(tmp);
	}

	@Override
	@Deprecated
	public List<PosTableFind> tableStateSelf(String tenantId, Integer organId, String tableNo, Integer tablePropertyId, String tableState, Integer businessAreaId) throws SystemException {
		List<PosTableFind> tableInfos = null;

		StringBuffer str = new StringBuffer("select t1.table_code,t1.table_name,t1.seat_counts,t1.table_fw,t1.valid_state,t1.business_area_id,t1.table_property_id,t1.organ_id,coalesce(pt.state,'FREE') as state,pt.lock_pos_num,pt.lock_opt_num,min(pb.opentable_time) as opentabletime,round(sum(pb.payment_amount),2) as amt ");
		str.append("from tables_info t1 left join pos_tablestate pt on t1.organ_id = pt.store_id and t1.table_code = pt.table_code left join pos_bill pb on pb.store_id = pt.store_id and pt.table_code=pb.table_code and pt.bill_batch_num=pb.batch_num ");
		str.append("where t1.valid_state = '1' and t1.tenancy_id = ? and t1.organ_id = ? ");
		str.append("group by t1.valid_state,t1.table_code,t1.seat_counts,t1.business_area_id,t1.table_property_id,t1.table_fw,t1.table_name,t1.organ_id,pt.lock_pos_num,pt.lock_opt_num,pt.state ");

		//用for循环的话还要在switch里判断是否为空
		if (StringUtils.isNotEmpty(tableNo)) {
			str.append(" having and t1.table_code = ? order by t1.table_code asc");
			tableInfos = this.jdbcTemplate.query(str.toString(), new Object[]{tenantId, organId, tableNo}, BeanPropertyRowMapper.newInstance(PosTableFind.class));
			return tableInfos;
		} else if (tablePropertyId != null) {
			str.append(" having and t1.table_property_id = ? order by t1.table_code asc");
			tableInfos = this.jdbcTemplate.query(str.toString(), new Object[]{tenantId, organId, tablePropertyId}, BeanPropertyRowMapper.newInstance(PosTableFind.class));
			return tableInfos;
		} else if (StringUtils.isNotEmpty(tableState)) {
			str.append(" having and t1.state = ? order by t1.table_code asc");
			tableInfos = this.jdbcTemplate.query(str.toString(), new Object[]{tenantId, organId, tableState}, BeanPropertyRowMapper.newInstance(PosTableFind.class));
			return tableInfos;
		} else if (businessAreaId != null) {
			str.append(" having and t1.business_area_id = ? order by t1.table_code asc");
			tableInfos = this.jdbcTemplate.query(str.toString(), new Object[]{tenantId, organId, businessAreaId}, BeanPropertyRowMapper.newInstance(PosTableFind.class));
			return tableInfos;
		} else {
			str.append(" order by t1.table_code asc");
			tableInfos = this.jdbcTemplate.query(str.toString(), new Object[]{tenantId, organId}, BeanPropertyRowMapper.newInstance(PosTableFind.class));
			return tableInfos;
		}
	}

	@Override
	public boolean isNotifyDayEnd(String tenancyId, Integer organId) throws Exception {
		// 打烊提醒时间
		String time = null;
		String timeState = null;
		try {
			//StringBuilder qureyParaSql = new StringBuilder("select para_value from sys_parameter where para_code='day_end_time'");
			StringBuilder qureyParaSql = new StringBuilder("select para_value,valid_state from sys_parameter where para_code='closed_time' and tenancy_id = ?");
			SqlRowSet rsp = this.jdbcTemplate.queryForRowSet(qureyParaSql.toString(), new Object[]{tenancyId});


			if (rsp.next()) {
				timeState = rsp.getString("valid_state");
				//总部规定的打烊时间
				time = rsp.getString("para_value");
				logger.info("========设定打烊提醒时间:" + time + "=======");

				if (!timeState.equals("0")) {
					//正则判断是否为hh:mm格式的
					Pattern pattern = Pattern.compile("^((20|21|22|23|[0-1]?\\d):[0-5]?\\d)$");
					Matcher matcher = pattern.matcher(time);
					if (matcher.find()) {
						String dateEndTime = time.concat(":00");

						int compareResult = DateUtil.timeCompare(DateUtil.getNowHHMMSS(), dateEndTime);


						String qsql = new String("select last_updatetime,report_date from pos_opt_state where content=? and store_id = ? and tenancy_id = ? order by report_date desc limit 1");

						SqlRowSet rsb = this.jdbcTemplate.queryForRowSet(qsql, new Object[]{"DAYBEGAIN", organId, tenancyId});
						String begainUpdatetime = null;
						String report_date = null;
						// 有日始记录
						if (rsb.next()) {
							begainUpdatetime = rsb.getString("last_updatetime");
							report_date = rsb.getString("report_date");
							int days = DateUtil.daysBetween(DateUtil.parseDate(report_date), new Date());
							SqlRowSet rse = this.jdbcTemplate.queryForRowSet(qsql, new Object[]{"DAYEND", organId, tenancyId});
							String endUpdatetime = null;
							// 有打烊记录
							if (rse.next()) {
								endUpdatetime = rse.getString("last_updatetime");
							} else {
								if (days > 0) {
									return true;
								}
							}

							if (!(StringUtils.isEmpty(endUpdatetime) || StringUtils.isEmpty(begainUpdatetime))) {
								SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
								Date begainUpdatetimeD = simpleDateFormat.parse(begainUpdatetime);
								Date endUpdatetimeD = simpleDateFormat.parse(endUpdatetime);

								// 最新次日始没有打烊记录
								if (endUpdatetimeD.getTime() < begainUpdatetimeD.getTime() && (compareResult > 0 || days > 0)) {
									return true;
								}
							} else {
								if (compareResult > 0 || days > 0) {
									return true;
								}
							}


						}
					}
				} else {
					logger.info("时间格式不通过-------您的时间格式为" + time);
					return false;
				}
			} else {
				return false;
			}
		} catch (Exception e) {
			logger.info("查询时间错误", e);
			return false;
		}


		return false;
	}


	@Override
	public List<JSONObject> findItemCheap(String tenantId, Integer organId, String reportDate) throws Exception {
		Date today = new Date();
		reportDate = reportDate.replaceAll(" ", "");
		/**
		 * 需注意：calendar的日期是：星期日 对应1  星期一 对应2，以此类推
		 */
		Calendar c = Calendar.getInstance();
		c.setTime(today);
		int weekcount = c.get(Calendar.DAY_OF_WEEK);
		String weekday = "0" + (weekcount == 1 ? weekcount : weekcount - 1);

		StringBuilder sql = new StringBuilder("select htpd.id as serial,htpd.item_id,hii.item_code as item_num,hii.item_name,htpd.afterprice as item_price,htpd.item_unit_id,hii.item_class as class_id,hic.itemclass_name as class_name from hq_time_price_details as htpd ");
		sql.append(" left join hq_time_price as htp on htpd.time_price_id = htp.id ");
		sql.append(" left join hq_time_price_org as htpo on htpo.time_price_id = htp.id ");
		sql.append(" left join hq_item_info as hii on htpd.item_id = hii.id ");
		sql.append(" left join hq_item_class as hic on hii.item_class = hic.id ");
		sql.append(" where htpo.store_id = " + organId + " and concat(htp.startdate,htp.sarttime) <= '" + reportDate + "' ");
		sql.append(" AND concat(htp.enddate,htp.endtime) >= '" + reportDate + "' AND htp.running_cycle = '" + weekday + "' and htp.valid_state = '1' order by htpd.item_id asc ");

		logger.debug("查询特价菜sql:" + sql.toString());
		List<JSONObject> list = this.queryString4Json(tenantId, sql.toString());
		return list;
	}

	@Override
	public List<PosItemSort> findItemSort(String qsDate, String tenantId, Integer organId) throws Exception {
		StringBuilder sql = new StringBuilder("select * from (select ROW_NUMBER()OVER() AS serial, a.item_id,b.item_code as item_num,b.item_name,sum(a.sales_num) as click_count,a.item_unit_id,c.unit_name,c.standard_price from hq_daycount_item a ");
		sql.append(" left join hq_item_info b on a.item_id=b.id and a.tenancy_id=b.tenancy_id left join hq_item_unit c on a.item_unit_id=c.id and a.item_id=c.item_id and a.tenancy_id=c.tenancy_id ");
		sql.append(" where a.day_count >= '" + qsDate + "' and a.tenancy_id=? and a.store_id=? ");
		sql.append(" group by a.item_id, b.item_code ,b.item_name,item_unit_id,c.unit_name,c.standard_price ) k order by click_count desc");
		System.out.println("菜品排行:" + sql.toString());
		List<PosItemSort> list = (List<PosItemSort>) this.jdbcTemplate.query(sql.toString(), new Object[]{tenantId, organId}, BeanPropertyRowMapper.newInstance(PosItemSort.class));
		return list;
	}

	@Override
	public List<JSONObject> findSoldOut(String tenantId, Integer organId, Map<String, Object> map) throws Exception {
		StringBuilder sql = new StringBuilder("select item_id,num,coalesce(item_unit_id,0) as item_unit_id,setdate,soldout_type from pos_soldout where store_id = " + organId);
		return this.queryString4Json(tenantId, sql.toString());
	}

	@Override
	public List<JSONObject> findWorryItem(String tenantId, Integer organId, Map<String, Object> map) throws Exception {
		StringBuilder sql = new StringBuilder("select item_id,num,item_unit_id from pos_item_worrysale where store_id = " + organId);
		return this.queryString4Json(tenantId, sql.toString());
	}

	@Override
	public Data getSoldOutCount(String tenantId, Integer organId, Map<String, Object> map) throws SystemException {
		Data data = new Data();
		@SuppressWarnings("unchecked")
		List<Map<String, Object>> items = (List<Map<String, Object>>) map.get("item");
		if (items == null || items.size() == 0) {
			throw new SystemException(PosErrorCode.NOT_NULL_ITEM_LIST);
		}
		Integer item_id = null;
		StringBuilder sql = new StringBuilder("select num from pos_soldout where item_id = ? and store_id = ? ");
		for (int i = 0; i < items.size(); i++) {
			Map<String, Object> item = items.get(i);
			Double count = null;
			item_id = ParamUtil.getIntegerValue(item, "item_id", true, PosErrorCode.NOT_NULL_ITEM_ID);

			SqlRowSet rs = this.jdbcTemplate.queryForRowSet(sql.toString(), new Object[]{item_id, organId});
			while (rs.next()) {
				count = rs.getDouble("num");
			}
			items.get(i).put("num", "" + count);
		}
		data.setCode(Constant.CODE_SUCCESS);
		data.setMsg(Constant.GET_GUQINGCOUNT_SUCCESS);
		data.setData(items);
		return data;
	}

	@Override
	public List<JSONObject> getComboItemByItemId(Integer itemId) throws Exception {
		StringBuffer sb = new StringBuffer("select iitem_id as item_id from hq_item_combo_details where details_id =" + itemId);
		sb.append(" and is_itemgroup='N' ");
		//"union select details_id as item_id from hq_item_combo_details where iitem_id ="+itemId );
		return this.queryString4Json("", sb.toString());
	}

	@Override
	public List<JSONObject> getSoldOutComboId(Integer itemId, String itemIdStr) throws Exception {
		StringBuffer sb = new StringBuffer("select item_id,num,item_unit_id,setdate from pos_soldout where 1=1 ");
		if (itemIdStr != null && !"".equals(itemIdStr)) {
			sb.append(" and item_id in(" + itemIdStr + ") ");
//			if("0".equals(mode)){
//				sb.append(" and num>0");
//			}
		}
		if (null != itemId && itemId > 0) {
			if (itemIdStr != null && !"".equals(itemIdStr)) {
				sb.append(" or ");
			} else {
				sb.append(" and ");
			}
			sb.append("  item_id=" + itemId);
//			if("0".equals(mode)){
//				sb.append(" and num>0");
//			}
		}
		return this.queryString4Json("", sb.toString());
	}

	@Override
	public List<JSONObject> getSoldDetailsIdComboId(Integer itemId, String str) throws Exception {
		StringBuffer sb = new StringBuffer("select details_id   from hq_item_combo_details where iitem_id =" + itemId);
		if (str != null && !"".equals(str)) {
			sb.append("  and details_id in(" + str + ")");
		}
		return this.queryString4Json("", sb.toString());
	}

	/**
	 * 查询套餐相关的Id
	 *
	 * @param itemIdStr
	 * @return
	 * @throws SystemException
	 */
	@Override
	public List<JSONObject> findComboItemId(String itemIdStr) throws Exception {
		StringBuffer sb = new StringBuffer("select iitem_id as item_id from hq_item_combo_details where details_id in(" + itemIdStr + ")" +
				"union select details_id as item_id from hq_item_combo_details where iitem_id in(" + itemIdStr + ")");
		return this.queryString4Json("", sb.toString());
	}

	@Override
	public Integer geComboMinByItemId(Integer itemId, String itemIdStr) throws Exception {
		StringBuffer sb = new StringBuffer("select item_id,num from pos_soldout where 1=1");
		if (itemIdStr != null && !"".equals(itemIdStr)) {
			sb.append("and item_id in(" + itemIdStr + ") ");
		}
		if (null != itemId && itemId > 0) {
			sb.append(" or item_id=" + itemId);
		}
		sb.append("  order by num asc LIMIT 1");
		List<JSONObject> list = this.queryString4Json("", sb.toString());
		Integer num = null;
		if (list != null && list.size() > 0) {
			JSONObject jsonObject = list.get(0);
			num = jsonObject.getInt("num");
		}
		return num;
	}


	public Integer geComboMAXByItemId(String iitem_id) throws Exception {
		StringBuffer sb = new StringBuffer("select details_id ,combo_num from hq_item_combo_details where 1=1");
		if (iitem_id != null && !"".equals(iitem_id)) {
			sb.append("and iitem_id='" + iitem_id + "'");
		}

		sb.append(" and is_itemgroup='N' and details_id in ( SELECT sold.item_id from pos_soldout sold ) ORDER BY combo_num desc LIMIT 1");
		List<JSONObject> list = this.queryString4Json("", sb.toString());
		Integer combo_num = null;
		if (list != null && list.size() > 0) {
			JSONObject jsonObject = list.get(0);
			combo_num = jsonObject.getInt("combo_num");
		}
		return combo_num;
	}

	/**
	 * 根据单品关联的套餐id 查询套餐下单品的最小沽清数量
	 *
	 * @return
	 * @throws SystemException
	 */
	@Override
	public Integer getSingleMinByItemId(String iitem_id) throws Exception {
		StringBuffer sb = new StringBuffer("select soldout.item_id,soldout.num from pos_soldout soldout where 1=1");
		if (iitem_id != null && !"".equals(iitem_id)) {
			sb.append(" and  soldout.item_id in ( select combo.details_id from hq_item_combo_details combo  where combo.iitem_id='" + iitem_id + "')");
		}
		sb.append("  order by num asc LIMIT 1");
		List<JSONObject> list = this.queryString4Json("", sb.toString());
		Integer num = null;
		if (list != null && list.size() > 0) {
			JSONObject jsonObject = list.get(0);
			num = jsonObject.getInt("num");
		}
		return num;
	}


	/**
	 * 查询是否为他餐 移动设备验证点餐
	 *
	 * @param itemId
	 * @return
	 * @throws SystemException
	 */
	@Override
	public boolean isCheckComboByItemId(String iitem_id) throws Exception {
		StringBuffer sb = new StringBuffer("select count(combo.details_id) as num from hq_item_combo_details combo  where combo.iitem_id='" + iitem_id + "'");
		List<JSONObject> list = this.queryString4Json("", sb.toString());
		Integer num = null;
		boolean result = false;
		if (list != null && list.size() > 0) {
			JSONObject jsonObject = list.get(0);
			num = jsonObject.getInt("num");
		}
		if (num != null && num > 0) {
			result = true;
		}
		return result;
	}

	public List<JSONObject> getSingleByItemId(String iitem_id) throws Exception {
		StringBuffer sb = new StringBuffer("select soldout.item_id ,soldout.num  from pos_soldout soldout where 1=1");
		if (iitem_id != null && !"".equals(iitem_id)) {
			sb.append(" and  soldout.item_id in ( select combo.details_id from hq_item_combo_details combo  where combo.iitem_id='" + iitem_id + "')");
		}
		List<JSONObject> list = this.queryString4Json("", sb.toString());

		return list;
	}

	public List<JSONObject> getComboOutBySingelItemId(Integer itemId) throws Exception {
		StringBuffer sb = new StringBuffer("SELECT soldout.item_id,soldout.num from pos_soldout soldout where  soldout.item_id in ( select combo.iitem_id from hq_item_combo_details combo where combo.details_id = " + itemId + ")");
		List<JSONObject> list = this.queryString4Json("", sb.toString());
		return list;
	}

	@Override
	public List<?> getChangshiftList(String tenancyId, int storeId, Date reportDate, String optNum, String posNum) throws Exception {
		StringBuilder msql = new StringBuilder("select concat(cm.begain_time,'~',cm.end_time) as work_datetime,cm.opt_num,cm.pos_num,cm.begain_time as begain_shift,cm.end_time as end_shift,cm.changshift_uid as changshift_id,cm.change as changet_amount,(cm.retreat_amount+cm.single_retreat_amount) as back_money, (cm.retreat_count+cm.single_retreat_count) as back_num,cm.shift_id,em.name as opt_name");
		msql.append(" from pos_opter_changshift_main cm left join employee em on cm.opt_num = em.id||''");
		msql.append(" where cm.tenancy_id=? and cm.store_id=? and cm.report_date=?");

//		if(Tools.hv(optNum))
//		{
//			msql.append(" and cm.opt_num='").append(optNum).append("'");
//		}

		if (Tools.hv(posNum)) {
			msql.append(" and cm.pos_num='").append(posNum).append("'");
		}

//		return this.query4Json(tenancyId, msql.toString(), new Object[]{tenancyId,storeId,reportDate});
		return this.query(msql.toString(), new Object[]
				{tenancyId, storeId, reportDate}, BeanPropertyRowMapper.newInstance(PosChangShift.class));
	}

	@Override
	public void savePosThirdpay(Data param, JSONObject data, int mainId) throws Exception {
		Map<String, Object> map = ReqDataUtil.getDataMap(param);
		StringBuilder insertSql = new StringBuilder("INSERT INTO pos_thirdpay")
				.append("(tenancy_id, store_id, id, pos_num, report_date, third_pos_num, third_store_num, trading_time, ")
				.append("shift_id, bill_num, pay_batch, cashier_num, card_no, card_type, trading_type, trading_type_num, amount,")
				.append("payment_id, payment_third_num, request_data, response_data, remark, old_expire_date, old_trading_num,")
				.append("old_hostserial_num, oldtracenumber, oldauth_num, old_trading_date, request_times)")
				.append("VALUES(?,?,?, ?, ?, ?, ?, ?, ? ,?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
		//.append("VALUES(?, ?,?, ?, ?, to_timestamp(COALESCE(?,null,'1900-01-01 00:00:00'),'yyyy-mm-dd hh24:mi:ss'), ?, ?, ? ,?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
		jdbcTemplate.update(insertSql.toString(), new Object[]{
				param.getTenancy_id(),
				param.getStore_id(),
				mainId,
				ParamUtil.getStringValue(map, "pos_num", false, null),
				ParamUtil.getDateValue(map, "report_date", false, null),
				ParamUtil.getStringValue(map, "third_pos_num", false, null),
				ParamUtil.getStringValue(map, "third_store_num", false, null),
				ParamUtil.getTimestampValue(map, "trading_time", false, null),
				ParamUtil.getIntegerValue(map, "shift_id", false, null),
				ParamUtil.getStringValue(map, "bill_num", false, null),
				ParamUtil.getStringValue(map, "pay_batch", false, null),
				ParamUtil.getStringValue(map, "cashier_num", false, null),
				ParamUtil.getStringValue(map, "card_no", false, null),
				ParamUtil.getStringValue(map, "card_type", false, null),
				ParamUtil.getStringValue(map, "trading_type", false, null),
				ParamUtil.getStringValue(map, "trading_type_num", false, null),
				ParamUtil.getDoubleValue(map, "amount", false, null),
				ParamUtil.getIntegerValue(map, "payment_id", false, null),
				ParamUtil.getStringValue(map, "payment_third_num", false, null),
				ParamUtil.getStringValue(map, "request_data", false, null),
				ParamUtil.getStringValue(map, "response_data", false, null),
				ParamUtil.getStringValue(map, "remark", false, null),
				ParamUtil.getStringValue(map, "old_expire_date", false, null),
				ParamUtil.getStringValue(map, "old_trading_num", false, null),
				ParamUtil.getStringValue(map, "old_hostserial_num", false, null),
				ParamUtil.getStringValue(map, "oldtracenumber", false, null),
				ParamUtil.getStringValue(map, "oldauth_num", false, null),
				ParamUtil.getDateValue(map, "old_trading_date", false, null),
				ParamUtil.getIntegerValue(map, "request_times", false, null)
		});


	}

	@SuppressWarnings("unchecked")
	@Override
	public void savePosThirdpayDetail(Data param, JSONObject thirdpayLogJson, int mainId) throws Exception {
		Map<String, Object> map = (Map<String, Object>) thirdpayLogJson;
		StringBuilder insertSql = new StringBuilder("INSERT INTO pos_thirdpay_detail")
				.append("(tenancy_id, store_id, id, father_id, thirdpay_id, oldauth_num, rejcode, trading_num, ")
				.append("trace_numr, pos_trace_num, hostserial_num, tips, total, amount, balance_amount, ")
				.append("expire_date, third_store_num, full_name, third_pos_num, iss_num, iss_name, card_no, ")
				.append("trading_date, trading_time, re_code_explain, remark)")
				.append("VALUES(?,?, nextval('pos_thirdpay_detail_id_seq'::regclass),?, ?, ?, ?, ?, ?, ?, ?,?,?,?,?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
		jdbcTemplate.update(insertSql.toString(), new Object[]{
				param.getTenancy_id(),
				param.getStore_id(),
				mainId,
				ParamUtil.getStringValue(map, "thirdpay_id", false, null),
				ParamUtil.getStringValue(map, "oldauth_num", false, null),
				ParamUtil.getStringValue(map, "rejcode", false, null),
				ParamUtil.getStringValue(map, "trading_num", false, null),
				ParamUtil.getStringValue(map, "trace_numr", false, null),
				ParamUtil.getStringValue(map, "pos_trace_num", false, null),
				ParamUtil.getStringValue(map, "hostserial_num", false, null),
				ParamUtil.getDoubleValue(map, "tips", false, null),
				ParamUtil.getDoubleValue(map, "total", false, null),
				ParamUtil.getDoubleValue(map, "amount", false, null),
				ParamUtil.getDoubleValue(map, "balance_amount", false, null),
				ParamUtil.getStringValue(map, "expire_date", false, null),
				ParamUtil.getStringValue(map, "third_store_num", false, null),
				ParamUtil.getStringValue(map, "full_name", false, null),
				ParamUtil.getStringValue(map, "third_pos_num", false, null),
				ParamUtil.getStringValue(map, "iss_num", false, null),
				ParamUtil.getStringValue(map, "iss_name", false, null),
				ParamUtil.getStringValue(map, "card_no", false, null),
				ParamUtil.getDateValue(map, "trading_date", false, null),
				ParamUtil.getStringValue(map, "trading_time", false, null),
				ParamUtil.getStringValue(map, "re_code_explain", false, null),
				ParamUtil.getStringValue(map, "remark", false, null),
		});

	}

	@Override
	public JSONObject queryPosThirdpay(JSONObject jsobj, Data param) throws Exception {
		JSONArray paramData = jsobj.optJSONArray("data");
		if (paramData == null || paramData.size() <= 0) {
			return null;
		}
		JSONObject jsonData = paramData.getJSONObject(0);
		String billNum = jsonData.optString("bill_num", "");// 账单号
		String tradingType = jsonData.optString("trading_type", "");// 交易类型
		double amount = jsonData.optDouble("amount", 0);// 金额
		List<Object> paramlst = new ArrayList<Object>();
		paramlst.add(param.getTenancy_id());
		paramlst.add(param.getStore_id());
		paramlst.add(billNum);
		StringBuilder querySql = new StringBuilder("SELECT tenancy_id,id, store_id, pos_num, report_date, third_pos_num, third_store_num, trading_time, ")
				.append("shift_id, bill_num , pay_batch, cashier_num, card_no, card_type, trading_type, trading_type_num, amount, payment_id, payment_third_num, ")
				.append("request_data, response_data, remark, old_expire_date, old_trading_num, old_hostserial_num, oldtracenumber, ")
				.append("oldauth_num, old_trading_date, request_times FROM pos_thirdpay pt ");
		querySql.append("where pt.tenancy_id=? and pt.store_id=? and pt.bill_num=? ");
		if (!tradingType.equals("")) {
			querySql.append(" and pt.trading_type=? ");
			paramlst.add(tradingType);
		}
		if (amount != 0) {
			querySql.append(" and pt.amount=? ");
			paramlst.add(amount);
		}


		List<JSONObject> list = this.queryString4Json(param.getTenancy_id(), querySql.toString(), paramlst.toArray());
		if (list != null && list.size() > 0) {
			return list.get(0);
		} else {
			return null;
		}
	}

	@Override
	public List<JSONObject> queryPosThirdpayDetail(String tenancyId, int mainId)
			throws Exception {
		StringBuilder querySql = new StringBuilder("SELECT tenancy_id, store_id,thirdpay_id, oldauth_num, rejcode, trading_num, trace_numr, pos_trace_num,")
				.append("hostserial_num, tips, total, amount, balance_amount, expire_date, third_store_num, full_name, third_pos_num, iss_num, iss_name, card_no, trading_date, ")
				.append("trading_time, re_code_explain, remark  FROM pos_thirdpay_detail ptd   ");
		querySql.append("where ptd.father_id=?");
		List<JSONObject> list = this.queryString4Json(tenancyId, querySql.toString(), new Object[]{mainId});
		return list;
	}

	@Override
	public List<JSONObject> getPrinter(String tenancyId, int storeId, String printMode) throws Exception {
		StringBuilder sql = new StringBuilder(" select coalesce(hd.is_site, '0') as is_site, coalesce(pn.is_open_cashbox, 0) as is_open_cashbox, pn.is_lobby,pn.kitchen_sign,pn.site_id,pn.type,pn.ctrl_type,pm.format_id,pm.is_area as is_begin_area,pm.print_num,pn.id as printer_id,pn.is_area,pn.name,pn.ip_com,pnbak.name as bak_name,pnbak.ip_com as bak_ip_com,pm.is_print_all from hq_printer_model pm ");
		sql.append(" left join hq_printer_new pn on pm.printer_id=pn.id ");
		sql.append(" left join hq_printer_new pnbak on pn.spare_id=pnbak.id ");

		// 关联终端设备查询
		sql.append(" left join hq_devices hd on hd.id = pm.devices_id and hd.tenancy_id = pm.tenancy_id and hd.valid_state = '1' ");

		String printType = getSysParameter(tenancyId, storeId, "new_printer_type");
		if ("1".equals(printType)) // java打印
		{
			sql.append(" left join sys_print_format_new spf on spf.id=pm.format_id and spf.class_item_code=pm.format_type ");
		} else if ("2".equals(printType)) // delphi打印
		{
			sql.append(" left join sys_print_format_delphi spf on spf.id=pm.format_id ");
		}
		sql.append(" where pm.format_type=? and pm.format_id=spf.id and pm.is_enables='1' and pm.tenancy_id=? and pn.store_id=? and pn.valid_state='1' order by pn.is_area asc ");

		return this.query4Json(tenancyId, sql.toString(), new Object[]
				{printMode, tenancyId, storeId});
//		StringBuilder sql = new StringBuilder(" select coalesce(hd.is_site, '0') as is_site, coalesce(pn.is_open_cashbox, 0) as is_open_cashbox, pn.is_lobby,pn.kitchen_sign,pn.site_id,pn.type,pn.ctrl_type,pm.format_id,pm.is_area as is_begin_area,pm.print_num,pn.id as printer_id,pn.is_area,pn.name,pn.ip_com,pnbak.name as bak_name,pnbak.ip_com as bak_ip_com from hq_printer_model pm ");
//		sql.append(" left join hq_printer_new pn on pm.printer_id=pn.id ");
//		sql.append(" left join hq_printer_new pnbak on pn.spare_id=pnbak.id ");
//
//		// 关联终端设备查询
//		sql.append(" left join hq_devices hd on hd.id = pm.devices_id and hd.tenancy_id = pm.tenancy_id and hd.valid_state = '1' ");
//
//		String printType = getSysParameter(tenancyId, storeId, "new_printer_type");
//		if("1".equals(printType)) // java打印
//		{
//			sql.append(" left join sys_print_format_new spf on spf.id=pm.format_id and spf.class_item_code=pm.format_type ");
//		}
//		else if("2".equals(printType)) // delphi打印
//		{
//			sql.append(" left join sys_print_format_delphi spf on spf.id=pm.format_id and spf.class_item_code=pm.format_type ");
//		}
//		sql.append(" where pm.format_type=? and pm.format_id=spf.id and pm.is_enables='1' and pm.tenancy_id=? and pn.store_id=? and pn.valid_state='1' order by pn.is_area asc ");
//		
//		return this.query4Json(tenancyId, sql.toString(), new Object[]
//				{ printMode, tenancyId, storeId });
	}

	@Override
	public List<JSONObject> getDevicesPrinter(String tenancyId, int storeId, String printMode, String posNum) throws Exception {
		StringBuilder sql = new StringBuilder(" select coalesce(hd.is_site, '0') as is_site, coalesce(pn.is_open_cashbox, 0) as is_open_cashbox, pn.is_lobby,pn.kitchen_sign,pn.site_id,pn.type,pn.ctrl_type,pm.format_id,pm.is_area as is_begin_area,pm.print_num,pn.id as printer_id,pn.is_area,pn.name,pn.ip_com,pnbak.name as bak_name,pnbak.ip_com as bak_ip_com,pm.is_print_all from hq_printer_model pm ");
		sql.append(" left join hq_printer_new pn on pm.printer_id=pn.id ");
		sql.append(" left join hq_printer_new pnbak on pn.spare_id=pnbak.id ");

		sql.append(" left join hq_devices hd on hd.id = pm.devices_id and hd.tenancy_id = pm.tenancy_id and hd.valid_state = '1' ");

		String printType = getSysParameter(tenancyId, storeId, "new_printer_type");
		if ("1".equals(printType)) // java打印
		{
			sql.append(" left join sys_print_format_new spf on spf.id=pm.format_id and spf.class_item_code=pm.format_type ");
		} else if ("2".equals(printType)) // delphi打印
		{
			sql.append(" left join sys_print_format_delphi spf on spf.id=pm.format_id and spf.class_item_code=pm.format_type ");
		}
		sql.append(" where pm.format_type=? and pm.format_id=spf.id and pm.is_enables='1' and pm.tenancy_id=? and pn.store_id=? and pn.valid_state='1' and hd.devices_code = ? ");

		return this.query4Json(tenancyId, sql.toString(), new Object[]
				{printMode, tenancyId, storeId, posNum});
	}

	@Override
	public List<JSONObject> getAreaPrinter(String tenancyId, int storeId, String printMode, String billNum) throws Exception {
		StringBuilder sql = new StringBuilder(" select coalesce(hd.is_site, '0') as is_site, coalesce(pn.is_open_cashbox, 0) as is_open_cashbox, tc.area_id, pn.is_lobby,pn.kitchen_sign,pn.site_id,pn.type,pn.ctrl_type,pm.format_id,pm.is_area as is_begin_area,pm.print_num,pn.id as printer_id,pn.is_area,pn.name,pn.ip_com,pnbak.name as bak_name,pnbak.ip_com as bak_ip_com,pm.is_print_all from hq_printer_model pm ");
		sql.append(" left join hq_printer_new pn on pm.printer_id=pn.id ");
		sql.append(" left join hq_printer_new pnbak on pn.spare_id=pnbak.id ");
		sql.append(" inner join (select hpt.id as area_id, hpt.printer_id from pos_bill pb left join tables_info ti on ti.table_code=pb.table_code left join hq_printer_table hpt on hpt.table_id=ti.id where pb.bill_num=? and pb.tenancy_id=?) tc on tc.printer_id = pn.id ");

		// 关联终端设备查询
		sql.append(" left join hq_devices hd on hd.id = pm.devices_id and hd.tenancy_id = pm.tenancy_id and hd.valid_state = '1' ");

		String printType = getSysParameter(tenancyId, storeId, "new_printer_type");
		if ("1".equals(printType)) // java打印
		{
			sql.append(" left join sys_print_format_new spf on spf.id=pm.format_id and spf.class_item_code=pm.format_type ");
		} else if ("2".equals(printType)) // delphi打印
		{
			sql.append(" left join sys_print_format_delphi spf on spf.id=pm.format_id and spf.class_item_code=pm.format_type ");
		}
		sql.append(" where pm.format_type=? and pm.format_id=spf.id and pm.is_enables='1' and pm.tenancy_id=? and pn.is_area = '1' and pn.store_id=? and pn.valid_state='1' ");

		return this.query4Json(tenancyId, sql.toString(), new Object[]
				{billNum, tenancyId, printMode, tenancyId, storeId});
	}

	@Override
	public JSONObject getPrinterTable(String tenancyId, int storeId, JSONObject para) throws Exception {
		StringBuilder sql = new StringBuilder(" select hpt.id as area_id,pb.table_code from pos_bill pb ");
		sql.append(" left join tables_info ti on ti.table_code=pb.table_code ");
		sql.append(" left join hq_printer_table hpt on hpt.table_id=ti.id ");
		sql.append(" where hpt.printer_id=? and pb.bill_num=? and pb.tenancy_id=? ");

		SqlRowSet rs = this.query4SqlRowSet(sql.toString(), new Object[]
				{para.optInt("printer_id"), para.optString("bill_num"), tenancyId});

		JSONObject jo = new JSONObject();
		if (rs.next()) {
			jo.put("area_id", rs.getString("area_id"));
			jo.put("table_code", rs.getString("table_code"));
		}
		return jo;
	}

	@Override
	public JSONObject getDeviceFromPrinterModel(String tenancyId, int storeId, JSONObject para, String printMode) throws Exception {
		StringBuilder sql = new StringBuilder(" select hpm.devices_id,hd.devices_code from hq_printer_model hpm ");
		sql.append(" left join hq_devices hd on hd.id=hpm.devices_id ");

		String printType = getSysParameter(tenancyId, storeId, "new_printer_type");
		if ("1".equals(printType)) // java打印
		{
			sql.append(" left join sys_print_format_new spf on spf.id=hpm.format_id and spf.class_item_code=hpm.format_type ");
		} else if ("2".equals(printType)) // delphi打印
		{
			sql.append(" left join sys_print_format_delphi spf on spf.id=hpm.format_id and spf.class_item_code=hpm.format_type ");
		}

		sql.append(" where hpm.tenancy_id=? and hpm.printer_id=? and hpm.format_type=? and hpm.is_enables='1' and spf.class_item_code=hpm.format_type ");

		SqlRowSet rs = this.query4SqlRowSet(sql.toString(), new Object[]
				{tenancyId, para.optInt("printer_id"), printMode});

		JSONObject jo = new JSONObject();
		if (rs.next()) {
			jo.put("devices_code", rs.getString("devices_code"));
		}
		return jo;
	}

	@Override
	public JSONObject checkPrinerTable(String tenancyId, int storeId, JSONObject para) throws Exception {
		StringBuilder sql = new StringBuilder(" select hpt.id as area_id,ti.table_code from hq_printer_table hpt ");
		sql.append(" left join tables_info ti on hpt.table_id=ti.id ");
		sql.append(" where ti.table_code=? and hpt.tenancy_id=? and hpt.printer_id=? ");

		SqlRowSet rs = this.query4SqlRowSet(sql.toString(), new Object[]
				{para.optString("rtable_code"), tenancyId, para.optInt("printer_id")});

		JSONObject jo = new JSONObject();
		if (rs.next()) {
			jo.put("area_id", rs.getString("area_id"));
			jo.put("table_code", rs.getString("table_code"));
		}
		return jo;
	}

	@Override
	public List<JSONObject> getPrintItemList(String tenancyId, int storeId, JSONObject para, String printMode, int itemClass) throws Exception {
		Map<?, ?> sysParameterMap = CacheManager.getCacheMap(CacheTableConstant.SYS_PARAMETER.name());
		// 厨打单是否打印历史菜品（快餐）
		String isPrintHistoryDish = CacheTableUtil.getSysParameter("isPrintHistoryDish", sysParameterMap);

		/**
		 * 一菜一单、一份一单、一类一单，设置参数套餐主项和明细全部显示时，就为每一个套餐明细分配套餐主项
		 * 主查询就排除套餐主项
		 */
		boolean sameType = false;
		if (SysDictionary.PRINT_CODE_1106.equals(printMode)
				|| SysDictionary.PRINT_CODE_1113.equals(printMode)
				|| SysDictionary.PRINT_CODE_1105.equals(printMode)) {
			sameType = true;
		}
		// 打印单套餐显示方式
		String comboPrintType = this.getSysParameter(tenancyId, storeId, "comboprint_type");

		// 下单时是否打印等叫菜品	
		String isPrintWaitCallItem = this.getSysParameter(tenancyId, storeId, "SFDYDJCP");

		StringBuilder sql = new StringBuilder(" select pbi.rwid, pbi2.rwid as tczx_rwid,pbi.item_id,pbi.item_num,hii.item_class,pbi.waitcall_tag,pbi.item_count,pbi.item_property,(case when pbi.yrwid is not null and pbi.print_count > 0 then '1' else '0' end) as is_use_allbillcopy_his, pbi.print_count from pos_bill_item pbi ");
		sql.append(" left join pos_bill pb on pbi.bill_num=pb.bill_num ");
		sql.append(" left join tables_info ti on pb.table_code=ti.table_code and pb.store_id=ti.organ_id ");
		sql.append(" left join hq_item_info hii on pbi.item_id=hii.id and pbi.tenancy_id=hii.tenancy_id ");
		if (SysDictionary.PRINT_CODE_1103.equals(printMode) && para.optString("is_print_all").equals("1")) {
			logger.info("门店:" + storeId + ",打印机：" + para.optInt("printer_id") + ",打印类型:" + printMode + ",已设置为打印全部菜品");

			//sql.append(" left join hq_printer_item hpi on pbi.item_id=hpi.item_id ");
			sql.append(" left join hq_printer_model hpm on pbi.tenancy_id=hpm.tenancy_id and hpm.is_enables='1'  and hpm.printer_id=? and hpm.format_type=? and pbi.store_id=? ");

			// 左连接是为了查询套餐明细对应的套餐主项
			sql.append(" left join pos_bill_item pbi2 on pbi.bill_num = pbi2.bill_num and pbi.store_id = pbi2.store_id and pbi.tenancy_id = pbi2.tenancy_id and pbi2.item_property = '" + SysDictionary.ITEM_PROPERTY_SETMEAL + "' and pbi.setmeal_id = pbi2.item_id and pbi.setmeal_rwid = pbi2.item_serial and (pbi2.item_remark is null or pbi2.item_remark = '" + SysDictionary.ITEM_REMARK_TC01 + "') ");

			String printType = getSysParameter(tenancyId, storeId, "new_printer_type");
			if ("1".equals(printType)) // java打印
			{
				sql.append(" left join sys_print_format_new spf on spf.id=hpm.format_id and spf.class_item_code=hpm.format_type ");
			} else if ("2".equals(printType)) // delphi打印
			{
				sql.append(" left join sys_print_format_delphi spf on spf.id=hpm.format_id ");
			}
			sql.append(" where  1=1  ");

		} else {
			sql.append(" left join hq_printer_item hpi on pbi.item_id=hpi.item_id ");
			sql.append(" left join hq_printer_model hpm on hpm.printer_id=hpi.print_id and hpm.id=hpi.print_format_id ");

			// 左连接是为了查询套餐明细对应的套餐主项
			sql.append(" left join pos_bill_item pbi2 on pbi.bill_num = pbi2.bill_num and pbi.store_id = pbi2.store_id and pbi.tenancy_id = pbi2.tenancy_id and pbi2.item_property = '" + SysDictionary.ITEM_PROPERTY_SETMEAL + "' and pbi.setmeal_id = pbi2.item_id and pbi.setmeal_rwid = pbi2.item_serial and (pbi2.item_remark is null or pbi2.item_remark = '" + SysDictionary.ITEM_REMARK_TC01 + "') ");

			String printType = getSysParameter(tenancyId, storeId, "new_printer_type");
			if ("1".equals(printType)) // java打印
			{
				sql.append(" left join sys_print_format_new spf on spf.id=hpm.format_id and spf.class_item_code=hpm.format_type ");
			} else if ("2".equals(printType)) // delphi打印
			{
				sql.append(" left join sys_print_format_delphi spf on spf.id=hpm.format_id ");
			}
			sql.append(" where hpm.is_enables='1' and hpm.printer_id=? and hpm.format_type=? and pbi.store_id=? and pbi.item_id=hpi.item_id ");
		}


		if (!"1".equals(isPrintHistoryDish)) {
			Map<?, ?> organMap = CacheManager.getCacheMap(CacheTableConstant.ORGAN.name());
			JSONObject organJson = CacheTableUtil.getOrganInfo(tenancyId, storeId, organMap);
			String formatState = organJson.optString("format_state");
			if ("2".equals(formatState)) {
				sql.append(" and (pbi.yrwid is null or pbi.yrwid is not null and pbi.print_count > 0) ");
			}
		}

		// 仅明细项打印
		if (StringUtils.equals(SysDictionary.COMBOPRINT_TYPE_MEALLIST, comboPrintType)) {
			sql.append(" and (pbi.item_property = '" + SysDictionary.ITEM_PROPERTY_SINGLE + "' or pbi.item_property = '" + SysDictionary.ITEM_PROPERTY_MEALLIST + "') ");
		} else if (StringUtils.equals(SysDictionary.COMBOPRINT_TYPE_SETMEAL, comboPrintType)) {
			sql.append(" and (pbi.item_property = '" + SysDictionary.ITEM_PROPERTY_SINGLE + "' or pbi.item_property = '" + SysDictionary.ITEM_PROPERTY_SETMEAL + "') ");
		} else {
			if (sameType) {
				sql.append(" and pbi.item_property <> '" + SysDictionary.ITEM_PROPERTY_SETMEAL + "' ");
			}
		}

		if (!Tools.isNullOrEmpty(para.optString("rwids"))) {
			sql.append(" and pbi.rwid in (" + para.optString("rwids") + ") ");
		} else if (FunctionCode.SINGLE_CHANGE_TABLE.equals(para.optString("function_id"))) {
			sql.append(" and pbi.rwid =" + para.optString("rwid"));
		} else if (FunctionCode.REMINDER_FOOD.equals(para.optString("function_id"))) { // 当rwids为null或""时，催菜时，应该没有催菜菜品
			sql.append(" and pbi.rwid = -1 ");
		}

		// 下单时不打印等叫菜品的场合
		if (FunctionCode.ORDERING.equals(para.optString("function_id")) && "0".equals(isPrintWaitCallItem)) {
			sql.append(" and pbi.waitcall_tag<>'1' ");
		}
		/**
		 *  未结账单，退菜时，判断是否打印等叫菜品。
		 *  等叫菜品waitcall_tag=1
		 */
		if (FunctionCode.RETREAT_FOOD_SELF.equals(para.optString("function_id")) && "0".equals(isPrintWaitCallItem)) {
			sql.append(" and pbi.waitcall_tag<>'1' ");
		}

		List<Object> values = new ArrayList<Object>();// 参数列表
		values.add(para.optInt("printer_id"));
		values.add(printMode);
		values.add(storeId);
		if (itemClass > 0) {
			sql.append(" and hii.item_class =?");
			values.add(itemClass);
		}
		if (!(FunctionCode.SINGLE_CHANGE_TABLE.equals(para.optString("function_id")) || FunctionCode.CHANGE_TABLE.equals(para.optString("function_id")))) {
			sql.append(" and pbi.bill_num=? ");
			values.add(para.optString("bill_num"));
		}
		Timestamp orderTime = DateUtil.parseTimestamp(para.optString("item_time"));
		if (!Tools.isNullOrEmpty(orderTime)) {
			sql.append(" and pbi.item_time =? ");
			values.add(orderTime);
		}

		sql.append(" order by rwid ");
		Object[] objs = values.toArray();

		return this.query4Json(tenancyId, sql.toString(), objs);
//        Map<?, ?> sysParameterMap = CacheManager.getCacheMap(CacheTableConstant.SYS_PARAMETER.name());
//        // 厨打单是否打印历史菜品（快餐）
//        String isPrintHistoryDish = CacheTableUtil.getSysParameter("isPrintHistoryDish", sysParameterMap);
//
//        /**
//         * 一菜一单、一份一单、一类一单，设置参数套餐主项和明细全部显示时，就为每一个套餐明细分配套餐主项
//         * 主查询就排除套餐主项
//         */
//	    boolean sameType = false;
//		if(SysDictionary.PRINT_CODE_1106.equals(printMode)
//                || SysDictionary.PRINT_CODE_1113.equals(printMode)
//                || SysDictionary.PRINT_CODE_1105.equals(printMode)){
//            sameType = true;
//        }
//	    // 打印单套餐显示方式
//		String comboPrintType = this.getSysParameter(tenancyId, storeId, "comboprint_type");
//		
//		// 下单时是否打印等叫菜品
//		String isPrintWaitCallItem = this.getSysParameter(tenancyId, storeId, "SFDYDJCP");
//					
//		StringBuilder sql = new StringBuilder(" select pbi.rwid, pbi2.rwid as tczx_rwid,pbi.item_id,pbi.item_num,hii.item_class,pbi.waitcall_tag,pbi.item_count,pbi.item_property,(case when pbi.yrwid is not null and pbi.print_count > 0 then '1' else '0' end) as is_use_allbillcopy_his, pbi.print_count from pos_bill_item pbi ");
//		sql.append(" left join pos_bill pb on pbi.bill_num=pb.bill_num ");
//		sql.append(" left join tables_info ti on pb.table_code=ti.table_code and pb.store_id=ti.organ_id ");
//		sql.append(" left join hq_item_info hii on pbi.item_id=hii.id and pbi.tenancy_id=hii.tenancy_id ");
//		sql.append(" left join hq_printer_item hpi on pbi.item_id=hpi.item_id ");
//		sql.append(" left join hq_printer_model hpm on hpm.printer_id=hpi.print_id and hpm.id=hpi.print_format_id ");
//
//        // 左连接是为了查询套餐明细对应的套餐主项
//        sql.append(" left join pos_bill_item pbi2 on pbi.bill_num = pbi2.bill_num and pbi.store_id = pbi2.store_id and pbi.tenancy_id = pbi2.tenancy_id and pbi2.item_property = '"+SysDictionary.ITEM_PROPERTY_SETMEAL+"' and pbi.setmeal_id = pbi2.item_id and pbi.setmeal_rwid = pbi2.item_serial and (pbi2.item_remark is null or pbi2.item_remark = '"+SysDictionary.ITEM_REMARK_TC01+"') ");
//
//		String printType = getSysParameter(tenancyId, storeId, "new_printer_type");
//		if("1".equals(printType)) // java打印
//		{
//			sql.append(" left join sys_print_format_new spf on spf.id=hpm.format_id and spf.class_item_code=hpm.format_type ");
//		}
//		else if("2".equals(printType)) // delphi打印
//		{
//			sql.append(" left join sys_print_format_delphi spf on spf.id=hpm.format_id and spf.class_item_code=hpm.format_type ");
//		}
//		sql.append(" where hpm.is_enables='1' and hpm.printer_id=? and hpm.format_type=? and pbi.store_id=? and pbi.item_id=hpi.item_id ");
//
//        if(!"1".equals(isPrintHistoryDish)){
//            Map<?, ?> organMap = CacheManager.getCacheMap(CacheTableConstant.ORGAN.name());
//            JSONObject organJson = CacheTableUtil.getOrganInfo(tenancyId, storeId, organMap);
//            String formatState = organJson.optString("format_state");
//            if("2".equals(formatState)){
//                sql.append(" and (pbi.yrwid is null or pbi.yrwid is not null and pbi.print_count > 0) ");
//            }
//        }
//
//		// 仅明细项打印
//		if (StringUtils.equals(SysDictionary.COMBOPRINT_TYPE_MEALLIST, comboPrintType))
//		{
//			sql.append(" and (pbi.item_property = '" + SysDictionary.ITEM_PROPERTY_SINGLE + "' or pbi.item_property = '" + SysDictionary.ITEM_PROPERTY_MEALLIST + "') ");
//		}
//		else if (StringUtils.equals(SysDictionary.COMBOPRINT_TYPE_SETMEAL, comboPrintType))
//		{
//			sql.append(" and (pbi.item_property = '" + SysDictionary.ITEM_PROPERTY_SINGLE + "' or pbi.item_property = '" + SysDictionary.ITEM_PROPERTY_SETMEAL + "') ");
//		}else{
//            if(sameType){
//                sql.append(" and pbi.item_property <> '" + SysDictionary.ITEM_PROPERTY_SETMEAL + "' ");
//            }
//        }
//		
//		if (!Tools.isNullOrEmpty(para.optString("rwids")))
//		{
//			sql.append(" and pbi.rwid in (" + para.optString("rwids") + ") ");
//		}
//		else if(FunctionCode.SINGLE_CHANGE_TABLE.equals(para.optString("function_id")))
//		{
//			sql.append(" and pbi.rwid =" + para.optString("rwid"));
//		}
//		else if(FunctionCode.REMINDER_FOOD.equals(para.optString("function_id")))
//		{ // 当rwids为null或""时，催菜时，应该没有催菜菜品
//			sql.append(" and pbi.rwid = -1 ");
//		}
//		
//		// 下单时不打印等叫菜品的场合
//		if(FunctionCode.ORDERING.equals(para.optString("function_id")) && "0".equals(isPrintWaitCallItem))
//		{
//			sql.append(" and pbi.waitcall_tag<>'1' ");
//		}
//		/**
//		 *  未结账单，退菜时，判断是否打印等叫菜品。
//		 *  等叫菜品waitcall_tag=1
//		 */
//		if(FunctionCode.RETREAT_FOOD_SELF.equals(para.optString("function_id")) && "0".equals(isPrintWaitCallItem)){
//			sql.append(" and pbi.waitcall_tag<>'1' ");
//		}
//		
//		List<Object> values = new ArrayList<Object>();// 参数列表
//		values.add(para.optInt("printer_id"));
//		values.add(printMode);
//		values.add(storeId);
//		if(itemClass > 0)
//		{
//			sql.append(" and hii.item_class =?");
//			values.add(itemClass);
//		}
//		if(!(FunctionCode.SINGLE_CHANGE_TABLE.equals(para.optString("function_id")) || FunctionCode.CHANGE_TABLE.equals(para.optString("function_id"))))
//		{
//			sql.append(" and pbi.bill_num=? ");
//			values.add(para.optString("bill_num"));
//		}
//		Timestamp orderTime = DateUtil.parseTimestamp(para.optString("item_time"));
//		if(!Tools.isNullOrEmpty(orderTime))
//		{
//			sql.append(" and pbi.item_time =? ");
//			values.add(orderTime);
//		}
//		
//		sql.append(" order by rwid ");
//		Object[] objs = values.toArray();
//		
//		return this.query4Json(tenancyId, sql.toString(), objs);
	}

	@Override
	public List<JSONObject> getPrintItemClass(String tenancyId, int storeId, String printMode, JSONObject para) throws Exception {

		StringBuilder sql = new StringBuilder(" select distinct hii.item_class from pos_bill_item pbi ");
		sql.append(" left join pos_bill pb on pbi.bill_num=pb.bill_num ");
		sql.append(" left join tables_info ti on pb.table_code=ti.table_code and pb.store_id=ti.organ_id ");
		sql.append(" left join hq_item_info hii on pbi.item_id=hii.id and pbi.tenancy_id=hii.tenancy_id ");
		sql.append(" left join hq_printer_item hpi on pbi.item_id=hpi.item_id ");
		sql.append(" left join hq_printer_model hpm on hpm.printer_id=hpi.print_id and hpm.id=hpi.print_format_id ");

		String printType = getSysParameter(tenancyId, storeId, "new_printer_type");
		if ("1".equals(printType)) // java打印
		{
			sql.append(" left join sys_print_format_new spf on spf.id=hpm.format_id and spf.class_item_code=hpm.format_type ");
		} else if ("2".equals(printType)) // delphi打印
		{
			sql.append(" left join sys_print_format_delphi spf on spf.id=hpm.format_id and spf.class_item_code=hpm.format_type ");
		}

		sql.append(" where hpm.is_enables='1' and hpm.printer_id=? and hpm.format_type=? and pbi.bill_num=? and pbi.store_id=? and pbi.item_id=hpi.item_id ");

		// 参数列表
		List<Object> values = new ArrayList<Object>();
		values.add(para.optInt("printer_id"));
		values.add(printMode);
		values.add(para.optString("bill_num"));
		values.add(storeId);

		// 只有下单时，才拼点菜时间，退菜、催菜、起菜都不需要
		if (FunctionCode.ORDERING.equals(para.optString("function_id"))) {
			String formatState = para.optString("format_state");
			String formatMode = para.optString("format_mode");
			if ("2".equals(formatState) || "02".equals(formatMode)) { // 快餐结完账, 不需要拼item_time，快餐的菜是一次性出厨打单的

			} else {
				sql.append(" and pbi.item_time=? ");
				values.add(DateUtil.parseTimestamp(para.optString("item_time")));
			}
		}

		Object[] objs = values.toArray();

		return this.query4Json(tenancyId, sql.toString(), objs);
	}

	@Override
	public List<JSONObject> getPrintParam(String tenancyId, int storeId, String printMode, JSONObject printerJo) throws Exception {
		//删掉查询条件 class_item_code
		String printType = getSysParameter(tenancyId, storeId, "new_printer_type");
		StringBuilder colSql = new StringBuilder();
		if ("1".equals(printType)) {
			colSql.append(" param_pairs3 ");
		} else if ("2".equals(printType)) {
			int is_lobby = printerJo.optInt("is_lobby"); // 是否前厅
			int kitchen_sign = printerJo.optInt("kitchen_sign"); // 是否后厨
			if (is_lobby == 1) // 前厅选中，一定是fastreport打印
			{
				colSql.append(" param_pairs2 ");
			} else if (is_lobby != 0 && kitchen_sign == 1)  // 前厅未选，后厨选中，则为esc打印
			{
				colSql.append(" param_pairs ");
			} else {
				colSql.append(" param_pairs2 ");  // 前厅后厨都没选，默认fastreport打印
			}
		} else {
			colSql.append(" param_pairs ");
		}
		StringBuilder sql = new StringBuilder(" select ");
		sql.append(colSql);
		sql.append(" as param_pairs ");

		if ("1".equals(printType)) // java打印
		{
			sql.append(" from sys_print_format_new ");
		} else if ("2".equals(printType)) // delphi打印
		{
			sql.append(" from sys_print_format_delphi ");
		}
		sql.append(" where id=? and valid_state='1' ");

		return this.query4Json(tenancyId, sql.toString(), new Object[]
				{printerJo.optInt("format_id")});
//		String printType = getSysParameter(tenancyId, storeId, "new_printer_type");
//		StringBuilder colSql = new StringBuilder();
//		if("1".equals(printType))
//		{
//			colSql.append(" param_pairs3 ");
//		}
//		else if("2".equals(printType))
//		{
//			int is_lobby = printerJo.optInt("is_lobby"); // 是否前厅
//			int kitchen_sign = printerJo.optInt("kitchen_sign"); // 是否后厨
//			if(is_lobby == 1) // 前厅选中，一定是fastreport打印
//			{
//				colSql.append(" param_pairs2 ");
//			}
//			else if(is_lobby != 0 && kitchen_sign == 1)  // 前厅未选，后厨选中，则为esc打印
//			{
//				colSql.append(" param_pairs ");
//			}
//			else
//			{
//				colSql.append(" param_pairs2 ");  // 前厅后厨都没选，默认fastreport打印
//			}
//		}
//		else
//		{
//			colSql.append(" param_pairs ");
//		}
//		StringBuilder sql = new StringBuilder(" select ");
//		sql.append(colSql);
//		sql.append(" as param_pairs ");
//		
//		if("1".equals(printType)) // java打印
//		{
//			sql.append(" from sys_print_format_new ");
//		}
//		else if("2".equals(printType)) // delphi打印
//		{
//			sql.append(" from sys_print_format_delphi ");
//		}
//		sql.append(" where id=? and class_item_code=? and valid_state='1' ");
//		
//		return this.query4Json(tenancyId, sql.toString(), new Object[]
//				{ printerJo.optInt("format_id"), printMode });
	}

	@Override
	public void insertPrintTask(String tenancyId, int storeId, JSONObject printer, JSONObject params, String printMode) throws Exception {
		//将state放到最后
		StringBuilder sql = new StringBuilder(" insert into pos_print_task(tenancy_id,store_id,site_id,area_id,print_name,print_address,print_parameter, ");
		sql.append(" templete_id,params,send_time,print_time,order_no,print_times,bak_print_name,bak_print_address,bak_print_parameter,print_num,serial_number,state) ");
		sql.append(" values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ");

		this.update(sql.toString(), new Object[]
				{tenancyId, storeId, printer.optInt("site_id"), printer.optInt("area_id"), printer.optString("name"), printer.optString("ip_com"),
						"", printer.optInt("format_id"), params.toString(), DateUtil.getNowTimestamp(), null, 0, 0, printer.optString("bak_name"), printer.optString("bak_ip_com"), "", printer.optInt("print_num"), printer.optInt("serial_number"), 0});
		//修改前
//		StringBuilder sql = new StringBuilder(" insert into pos_print_task(tenancy_id,store_id,site_id,area_id,state,print_name,print_address,print_parameter, ");
//		sql.append(" templete_id,params,send_time,print_time,order_no,print_times,bak_print_name,bak_print_address,bak_print_parameter,print_num,serial_number) ");
//		sql.append(" values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ");
//		
//		this.update(sql.toString(), new Object[]
//				{ tenancyId, storeId, printer.optInt("site_id"), printer.optInt("area_id"), 0, printer.optString("name"), printer.optString("ip_com"),
//			      "", printer.optInt("format_id"), params.toString(), DateUtil.getNowTimestamp(), null, 0, 0, printer.optString("bak_name"), printer.optString("bak_ip_com"), "", printer.optInt("print_num"), printer.optInt("serial_number")});
	}

	@Override
	public void UpdateItemPrintTag(String tenancyId, int storeId, String bill_num) throws Exception {
		StringBuilder sql = new StringBuilder("update pos_bill_item set print_tag=null where bill_num=? and store_id=? and tenancy_id=? and rwid in(select order_no from pos_print_task) ");

		this.update(sql.toString(), new Object[]
				{bill_num, storeId, tenancyId});
	}

	@Override
	public int checkKitchenModel(String tenancyId, int storeId, JSONObject para) throws Exception {
		StringBuilder sql = new StringBuilder(" select count(*) as model_num from hq_printer_model hpm ");

		String printType = getSysParameter(tenancyId, storeId, "new_printer_type");
		if ("1".equals(printType)) // java打印
		{
			sql.append(" left join sys_print_format_new spf on spf.id=hpm.format_id and spf.class_item_code=hpm.format_type ");
		} else if ("2".equals(printType)) // delphi打印
		{
			sql.append(" left join sys_print_format_delphi spf on spf.id=hpm.format_id and spf.class_item_code=hpm.format_type ");
		}

		sql.append(" where hpm.tenancy_id=? and hpm.printer_id=? and hpm.format_type in (?,?,?) and hpm.is_enables='1' and spf.class_item_code=hpm.format_type ");

		SqlRowSet rs = this.query4SqlRowSet(sql.toString(), new Object[]
				{tenancyId, para.optInt("printer_id"), SysDictionary.PRINT_CODE_1105, SysDictionary.PRINT_CODE_1106, SysDictionary.PRINT_CODE_1107});

		int ret = 0;
		if (rs.next()) {
			ret = rs.getInt("model_num");
		}
		return ret;
	}

	@Override
	public int checkKitchenItem(String tenancyId, int storeId, JSONObject para) throws Exception {
		StringBuilder sql = new StringBuilder(" select count(*) as item_num from pos_bill pb ");
		sql.append(" left join pos_bill_item pbi on pb.bill_num=pbi.bill_num ");
		sql.append(" left join hq_printer_item hpi on pbi.item_id=hpi.item_id ");
		sql.append(" left join hq_printer_model hpm on hpi.print_id=hpm.printer_id and hpi.print_format_id=hpm.id ");

		String printType = getSysParameter(tenancyId, storeId, "new_printer_type");
		if ("1".equals(printType)) // java打印
		{
			sql.append(" left join sys_print_format_new spf on spf.id=hpm.format_id and spf.class_item_code=hpm.format_type ");
		} else if ("2".equals(printType)) // delphi打印
		{
			sql.append(" left join sys_print_format_delphi spf on spf.id=hpm.format_id and spf.class_item_code=hpm.format_type ");
		}

		sql.append(" where hpm.printer_id=? and hpm.is_enables='1' and pb.tenancy_id=? and pb.store_id=? and pb.fictitious_table=? and pb.bill_property=? ");
		sql.append(" and hpm.format_type in (?,?,?) and spf.class_item_code=hpm.format_type; ");

		SqlRowSet rs = this.query4SqlRowSet(sql.toString(), new Object[]
				{para.optInt("printer_id"), tenancyId, storeId, para.optString("rtable_code"), SysDictionary.BILL_PROPERTY_OPEN, SysDictionary.PRINT_CODE_1105, SysDictionary.PRINT_CODE_1106, SysDictionary.PRINT_CODE_1107});

		int ret = 0;
		if (rs.next()) {
			ret = rs.getInt("item_num");
		}
		return ret;
	}

	@Override
	public void deletePosPrint(String tenancyId, int storeId) throws Exception {
		String deleteSql = new String("delete from pos_print where tenancy_id=? and store_id=?");
		this.update(deleteSql, new Object[]{tenancyId, storeId});
	}

	@Override
	public void deletePosPrintList(String tenancyId, int storeId) throws Exception {
		String deleteSql = new String("delete from pos_print_list where tenancy_id=? and store_id=?");
		this.update(deleteSql, new Object[]{tenancyId, storeId});
	}

	@Override
	public void checkFinanceBasedata(String tenantId, Integer organId,
									 Map<String, Object> map) throws Exception {
		Date reportDate = ParamUtil.getDateValue(map, "report_date", false, null);//报表日期
		String baseSql = "insert into pos_finance_basedata_bad_record (tenancy_id,store_id,bill_num,report_date,bad_code,bad_message,create_time,remark,upload_tag) ";

		//1、账单应收=菜品销售应收+服务费（检查）
		StringBuffer posBillAmountSql = new StringBuffer(baseSql);
		posBillAmountSql.append(" select ?,?,bill_num,?,?,?,?,?,? from pos_bill where bill_amount <> subtotal+service_amount  and tenancy_id =? and store_id=? ");
		this.update(posBillAmountSql.toString(), new Object[]{tenantId, organId, reportDate,
				Constant.POS_FINANCE_BASEDATA_BAD_CODE_BILL_AMOUNT, "账单应收 不等于（菜品销售应收+服务费）",
				DateUtil.currentTimestamp(), null, 0,
				tenantId, organId});

		//2、账单付款金额=账单应收-折扣金额-折让金额+抹零金额-奉送（检查）
		StringBuffer paymentAmountSql = new StringBuffer(baseSql);
		paymentAmountSql.append(" select ?,?,bill_num,?,?,?,?,?,? from pos_bill where payment_amount <> bill_amount-discountk_amount-discountr_amount+maling_amount-givi_amount   and tenancy_id =? and store_id=? ");
		this.update(paymentAmountSql.toString(), new Object[]{tenantId, organId, reportDate,
				Constant.POS_FINANCE_BASEDATA_BAD_CODE_PAYMENT_AMOUNT, "账单付款金额 不等于（账单应收-折扣金额-折让金额+抹零金额-奉送）",
				DateUtil.currentTimestamp(), null, 0,
				tenantId, organId});

		//3、账单实收 = 菜品实收 +服务费
		StringBuffer paymentAmountItemSql = new StringBuffer(baseSql);
		paymentAmountItemSql.append(" select ?,?,pb.bill_num ,?,?,?,?,?,? from  pos_bill pb where pb.tenancy_id=? and pb.store_id=? and pb.payment_amount <> service_amount+ ");
		paymentAmountItemSql.append(" (select sum(real_amount) from pos_bill_item pbi where pbi.bill_num=pb.bill_num and pbi.item_property<>'MEALLIST' ) ");
		this.update(paymentAmountItemSql.toString(), new Object[]{tenantId, organId, reportDate,
				Constant.POS_FINANCE_BASEDATA_BAD_CODE_PAYMENT_AMOUNT_ITEM, "账单实收 不等于 （菜品实收 +服务费）",
				DateUtil.currentTimestamp(), null, 0,
				tenantId, organId});

		//4、 付款表中付款金额 = 账单表中的（实结金额+多收礼券）
		StringBuffer paymentAmountPayMentSql = new StringBuffer(baseSql);
		paymentAmountPayMentSql.append(" select ?,?,pb.bill_num ,?,?,?,?,?,? from  pos_bill pb where pb.tenancy_id=? and pb.store_id=? and pb.payment_amount <>  ");
		paymentAmountPayMentSql.append(" (select sum(currency_amount) from pos_bill_payment pbp where pbp.bill_num=pb.bill_num) -  pb.more_coupon ");
		this.update(paymentAmountPayMentSql.toString(), new Object[]{tenantId, organId, reportDate,
				Constant.POS_FINANCE_BASEDATA_BAD_CODE_PAYMENT_AMOUNT_PAYMENT, "付款表中付款金额 不等于（ 账单表中的实结金额+多收礼券）",
				DateUtil.currentTimestamp(), null, 0,
				tenantId, organId});
	}

	/**
	 * 本地验证优惠卷
	 *
	 * @param tenancyId /商户ID
	 * @param storeId   //机构ID
	 * @param billNum   //订单ID
	 * @param //优惠卷相关集合
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getCouponsValiData(String tenancyId, Integer storeId, String billNum, String typeId) throws Exception {
		StringBuilder sql = new StringBuilder("select t.rwid,t.real_amount,t.item_id,t.item_unit_id,t.item_count,s.type_id");
		sql.append(" from pos_bill_item t left join crm_coupons_details s on t.item_id = s.item_id and t.item_unit_id=s.unit_id");
		sql.append(" where t.tenancy_id=? and t.store_id=? and t.bill_num=?");
		sql.append(" and s.type_id in(" + typeId + ") and t.item_property <> 'MEALLIST' order by t.real_amount desc");
		List<JSONObject> jsonList = this.queryString4Json(tenancyId, sql.toString(), new Object[]{tenancyId, storeId, billNum});
		return jsonList;
	}

	/**
	 * 验证优惠卷是否可以使用
	 *
	 * @param
	 * @param
	 * @param coupnosCode
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getCouponsValiCode(String tenancyId, String coupnosCode) throws Exception {
		StringBuffer sb = new StringBuffer();
		sb.append("select ss.start_coupon,end_coupon,bill_limit_money,bill_limit_num,ss.use_cycle,t.send_time");
		sb.append(" from  crm_coupons t  left join crm_coupons_type ss on t.type_id=ss.id");
		sb.append(" where  t.tenancy_id=? AND t.code in('" + coupnosCode + "')");
		List<JSONObject> jsonList = this.queryString4Json(tenancyId, sb.toString(), new Object[]{tenancyId});
		return jsonList;
	}

	@Override
	@Deprecated
	public List<JSONObject> getCcThirdOrganInfoByOrganId(String tenantId, Integer organId) throws Exception {
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject(); // data数组里的对象

		StringBuilder sql = new StringBuilder("select tenant_id,shop_id,source as developerid,secret as signkey,channel from cc_third_organ_info where tenant_id='").append(tenantId).append("' and shop_id=").append(organId);

		logger.debug("getCcThirdOrganInfoByOrganId:::" + sql.toString());

		List<JSONObject> thirdOrganInfo = this.queryString4Json(tenantId, sql.toString());
		if (thirdOrganInfo != null) {
			JSONArray thirdOrganInfoArray = JSONArray.fromObject(thirdOrganInfo);
			object.element("cc_third_organ_info", thirdOrganInfoArray);
		}
		list.add(object);
		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return list;
	}

	@Override
	public List<JSONObject> getBohImgByPcOrPad(String tenantId, Integer organId) throws Exception {
		StringBuilder sb = new StringBuilder("select id,terminal_type,img_type,img_addr,img_index,remark,last_updatetime from boh_img_padpc where tenancy_id=? and organ_id=?");
		Object[] objs = new Object[]{tenantId, organId};
		JSONArray jsonList = this.query4JSONArray(tenantId, sb.toString(), objs);
		List<JSONObject> jsonObjects = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject();
		object.put("boh_img_padpc", jsonList);
		jsonObjects.add(object);
		return jsonObjects;
	}

	@Override
	public String getBohImgByPcOrPadEx(String tenantId, Integer organId) throws Exception {
		StringBuilder sb = new StringBuilder("select id,terminal_type,img_type,img_addr,img_index,remark,last_updatetime from boh_img_padpc where tenancy_id=? and organ_id=?");
		List<Map<String, Object>> dishes = this.jdbcTemplate.queryForList(sb.toString(), new Object[]{tenantId, organId});
		String tmp = org.apache.commons.lang.StringUtils.join(dishes.toArray(), "|");
		return JavaMd5.toMd5B32(tmp);
	}

	@Override
	public List<JSONObject> getItemSoldout(String tenantId, Integer organId) throws Exception {
		StringBuilder sql = new StringBuilder(" SELECT id, item_id, num, item_unit_id from pos_soldout where tenancy_id = ? and store_id = ? ");
		Object[] obj = new Object[]{tenantId, organId};
		List<JSONObject> jsons = this.query4Json(tenantId, sql.toString(), obj);
		JSONObject json = new JSONObject();
		json.put("soldout", jsons);
		List<JSONObject> jsonsList = new ArrayList<JSONObject>();
		jsonsList.add(json);
		return jsonsList;
	}

	@Override
	public List<JSONObject> getItemWorrysale(String tenantId, Integer organId) throws Exception {
		StringBuilder sql = new StringBuilder(" SELECT id, item_id, item_unit_id, num from pos_item_worrysale where tenancy_id = ? and store_id = ?");
		Object[] obj = new Object[]{tenantId, organId};
		List<JSONObject> jsons = this.query4Json(tenantId, sql.toString(), obj);
		JSONObject json = new JSONObject();
		json.put("worrysale", jsons);
		List<JSONObject> jsonList = new ArrayList<>();
		jsonList.add(json);
		return jsonList;
	}

	@Cacheable(value = "hqDeviceStatCache", key = "#devicesCode")
	@Override
	public String getDevicesDataState(String devicesCode) throws Exception {
		String sql = "select *  from hq_devices_datastate where devices_code='" + devicesCode + "' and  refresh_flag='1' ";
		SqlRowSet rs = this.query4SqlRowSet(sql);
		if (rs.next()) {
			return "1";
		}
		return "0";
	}

	@CacheEvict(value = "tableStateCache", key = "#tableCode")
	@Override
	public int insertPosTableState(String tenantId, Integer organId, String tableCode, String optNum)
			throws Exception {
		// TODO Auto-generated method stub
		Timestamp tt = DateUtil.limit6Timestamp();
		String ipt = new String("insert into pos_tablestate (tenancy_id,store_id,table_code,state,opt_name,last_updatetime) values (?,?,?,?,?,?) ");
		String optName = this.getEmpNameById(optNum, tenantId, organId);
		EhcacheUtil.removeAllElment("tableStateCache", tableCode);
		return this.update(ipt, new Object[]
				{tenantId, organId, tableCode, SysDictionary.TABLE_STATE_FREE, optName, tt});
	}

	/**
	 * 无码优惠卷流水插入
	 *
	 * @param tenancyId
	 * @param storeId
	 * @param chanel
	 * @param businessDate
	 * @param billCode
	 * @param typeId
	 * @param itemId
	 * @param unitId
	 * @param price
	 * @param itemNum
	 * @param discountMoney
	 * @param discountNum
	 * @throws Exception
	 */
	@Deprecated
	public void insertPosCouponsUseItem(String tenancyId, Integer storeId, String chanel, Date businessDate, String billCode, Integer typeId, Integer itemId, Integer unitId, Double price, Double itemNum, Double discountMoney, Double discountNum, Integer classId, Double dealValue, String dealName, String remark, String couponsPro) throws Exception {
		Timestamp lastUpdatetime = new Timestamp(System.currentTimeMillis());
		StringBuilder insertCouponsUseItemSql = new StringBuilder();
		insertCouponsUseItemSql.append("insert into pos_bill_payment_coupons(tenancy_id,store_id,chanel,report_date,bill_num,type_id,item_id,price,item_num,discount_money,discount_num,class_id,coupon_type,is_cancel,upload_tag,last_updatetime,deal_value,deal_name,remark,coupons_pro) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
		this.update(insertCouponsUseItemSql.toString(), new Object[]{tenancyId, storeId, chanel, businessDate, billCode, typeId, itemId, price, itemNum, discountMoney, discountNum, classId, 2, "0", "0", lastUpdatetime, dealValue, dealName, remark, couponsPro});
	}

	@Override
	public void getBindOptNum(String tenancyId, int storeId, JSONObject para) throws Exception {
		String posNum = para.optString("pos_num");
		StringBuilder queryDevicesSql = new StringBuilder("select pos_num from pos_opt_state_devices where tenancy_id=? and store_id=? and devices_num=? and is_valid='1'");
		SqlRowSet rs = this.query4SqlRowSet(queryDevicesSql.toString(), new Object[]{tenancyId, storeId, posNum});
		if (rs.next()) {
			para.put("pos_num", rs.getString("pos_num"));
		}

		// 查询设备类型
        /*queryDevicesSql =new StringBuilder(" select show_type from hq_devices where devices_code = ? and valid_state = '1' and tenancy_id = ? and store_id = ? ");
        SqlRowSet sqlRowSet = this.query4SqlRowSet(queryDevicesSql.toString(), new Object[]{posNum, tenancyId, storeId});
        if(sqlRowSet.next())
        {
            para.put("show_type", sqlRowSet.getString("show_type"));
        }*/

	}

//	@Override
//	public Double getReceiveAmountByPayment(String tenancyId, int storeId, Date reportDate, Integer waiterId, Integer paymentId) throws Exception
//	{
//		// TODO Auto-generated method stub
//		StringBuilder querySql =new StringBuilder();
//		querySql.append(" select coalesce((");
//		querySql.append(" select sum(coalesce(pbp.currency_amount,0)) as payment_amount from pos_bill_payment pbp left join hq_devices hd on  pbp.store_id = hd.store_id AND pbp.pos_num = hd.devices_code");
//		querySql.append(" where pbp.tenancy_id=? and pbp.store_id=? and pbp.report_date=? and pbp.cashier_num=?||'' and pbp.jzid=? and pbp.payment_state=? and (hd.show_type = 'YD' OR  hd.show_type = 'PAD')");
//		querySql.append(" ),0)-coalesce((");
//		querySql.append(" select sum(pos_cashier_receive_log.amount) receive_amount from pos_cashier_receive_log where tenancy_id=? and store_id=? and report_date=? and waiter_id=? and payment_id=? ");
//		querySql.append(" ),0) receive_amount");
//		
//		//this.jdbcTemplate.query(querySql.toString(), new Object[]{});
//		SqlRowSet rs = this.query4SqlRowSet(querySql.toString(), new Object[]{tenancyId,storeId,reportDate,waiterId,paymentId,SysDictionary.PAYMENT_STATE_PAY_COMPLETE,tenancyId,storeId,reportDate,waiterId,paymentId});
//		Double receiveAmount =0d;
//		if(rs.next())
//		{
//			receiveAmount = rs.getDouble("receive_amount");
//			if(receiveAmount.isNaN())
//			{
//				receiveAmount =0d;
//			}
//		}
//		return receiveAmount;
//	}

	@Override
	@Deprecated
	public List<PosBillMembers> selectMembersByBillNum(String tenantId, Integer organId, String billNum)
			throws Exception {
		// TODO Auto-generated method stub

		String membersSql = "select id,type,amount,credit,card_code,mobil,last_updatetime,remark,bill_num from pos_bill_member where bill_num = ? and store_id=? and tenancy_id=?";
		List<PosBillMembers> memberList = this.jdbcTemplate.query(membersSql,
				new Object[]{billNum, organId, tenantId}, BeanPropertyRowMapper.newInstance(PosBillMembers.class));

		return memberList;
	}

	@Override
	public void otherChanelAddMeallist(String tenancyId, int storeId) throws Exception {
		// 排序字段值，默认给较大值，排到最后
		String itemRank = "99999";
		//
		Timestamp timestamp = DateUtil.currentTimestamp();

		// 查询其它渠道下有套餐但没有明细项的套餐明细
		// 套餐明细可能会有项目组或单品
		StringBuilder meallistSql = new StringBuilder();
		meallistSql.append(" select * from (select t1.id as zhux_id,t1.tenancy_id,t1.item_menu_id,t1.starttime,t1.endtime,t1.valid_state,t1.last_operator,t1.is_show,t1.chanel,t1.class as menu_class,t1.item_name,t1.item_english,t1.phonetic_code,t1.five_code,t1.details_id as item_id,t2.id as item_id2 ");
		meallistSql.append(" from (select distinct hii.id,himd.tenancy_id,himd.item_menu_id,himd.starttime,himd.endtime,himd.valid_state,himd.last_operator,himd.is_show,himc.chanel,himc.class,hicd.details_id,hii2.item_name,hii2.item_english,hii2.phonetic_code,hii2.five_code ");
		meallistSql.append(" from hq_item_info as hii ");
		meallistSql.append(" left join hq_item_menu_details as himd on hii.id = himd.item_id ");
		meallistSql.append(" left join hq_item_menu_organ as himo on himd.item_menu_id = himo.item_menu_id ");
		meallistSql.append(" left join hq_item_menu him on himo.item_menu_id = him. id ");
		meallistSql.append(" left join hq_item_menu_class as himc on himc.details_id = himd. id ");
		meallistSql.append(" left join hq_item_class as hic on himc. class = hic. id ");
		meallistSql.append(" left join organ o on himo.store_id = o. id ");
		meallistSql.append(" left join hq_item_combo_details hicd on hii.id = hicd.iitem_id ");
		meallistSql.append(" left join hq_item_info hii2 on hicd.details_id = hii2.id ");
		meallistSql.append(" where him.valid_state = '1' and hii.valid_state = '1' and hii.is_combo = 'Y' and himc.chanel = ? and himo.store_id = ? and himo.tenancy_id = ? and hii2.valid_state = '1' and hicd.is_itemgroup = 'N' ");
		meallistSql.append(" union (select distinct hii.id,himd.tenancy_id,himd.item_menu_id,himd.starttime,himd.endtime,himd.valid_state,himd.last_operator,himd.is_show,himc.chanel,himc.class,higd.item_id as details_id,hii2.item_name,hii2.item_english,hii2.phonetic_code,hii2.five_code ");
		meallistSql.append(" from hq_item_info as hii ");
		meallistSql.append(" left join hq_item_menu_details as himd on hii.id = himd.item_id ");
		meallistSql.append(" left join hq_item_menu_organ as himo on himd.item_menu_id = himo.item_menu_id ");
		meallistSql.append(" left join hq_item_menu him on himo.item_menu_id = him. id ");
		meallistSql.append(" left join hq_item_menu_class as himc on himc.details_id = himd. id ");
		meallistSql.append(" left join hq_item_class as hic on himc. class = hic. id ");
		meallistSql.append(" left join organ o on himo.store_id = o. id ");
		meallistSql.append(" left join hq_item_combo_details hicd on hii.id = hicd.iitem_id ");
		meallistSql.append(" left join hq_item_group hig on hicd.details_id = hig.id ");
		meallistSql.append(" left join hq_item_group_details higd on hig.id = higd.item_group_id ");
		meallistSql.append(" left join hq_item_info hii2 on higd.item_id = hii2.id ");
		meallistSql.append(" where him.valid_state = '1' and hii.valid_state = '1' and hii.is_combo = 'Y' and himc.chanel = ? and himo.store_id = ? and himo.tenancy_id = ? and hii2.valid_state = '1' and hicd.is_itemgroup = 'Y' and hig.valid_state = '1' ");
		meallistSql.append(" ) ) t1 ");
		meallistSql.append(" left join (select distinct hii.id,hii.item_name,himc.chanel ");
		meallistSql.append(" from hq_item_info as hii");
		meallistSql.append(" left join hq_item_menu_details as himd on hii. id = himd.item_id ");
		meallistSql.append(" left join hq_item_menu_organ as himo on himd.item_menu_id = himo.item_menu_id ");
		meallistSql.append(" left join hq_item_menu him on himo.item_menu_id = him. id ");
		meallistSql.append(" left join hq_item_menu_class as himc on himc.details_id = himd. id ");
		meallistSql.append(" left join hq_item_class as hic on himc. class = hic. id ");
		meallistSql.append(" left join organ o on himo.store_id = o. id ");
		meallistSql.append(" where him.valid_state = '1' and hii.valid_state = '1' and himc.chanel = ? and himo.store_id = ? and himo.tenancy_id = ? ");
		meallistSql.append(" ) t2 on t1.details_id = t2.id) tt where tt.item_id2 is null ");

		// 查询hq_item_menu_details当前最大id值
		String detailsIdSql = "select (max(id)+1) as id from hq_item_menu_details";
		// 餐谱明细
		String menuDetailsSql = "insert into hq_item_menu_details (tenancy_id, id, item_menu_id, item_id, starttime, endtime, valid_state, last_operator, last_updatetime, menu_item_rank, fake_id, is_show) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
		// 餐谱类别
		String menuClassSql = "insert into hq_item_menu_class (tenancy_id, id, details_id, chanel, class, item_name, item_english, phonetic_code, five_code, menu_class_rank, fake_id) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

		List<JSONObject> meallists = null;
		Object[] detailsParams = null;
		Object[] classParams = null;

		try {
			// 其它渠道
			String[] chanelArr = new String[]{SysDictionary.PD_CHANEL, SysDictionary.EZZ_CHANEL};

			// 遍历器
			Set<String> itemIds = null;

			for (String chanel : chanelArr) {
				Object[] paramArr = new Object[]{chanel, storeId, tenancyId, chanel, storeId, tenancyId, chanel, storeId, tenancyId};
				meallists = this.query4Json(tenancyId, meallistSql.toString(), paramArr);

				if (!meallists.isEmpty()) {
					itemIds = new HashSet<String>();

					for (JSONObject item : meallists) {
						// 同一个渠道下只能添加一个菜品
						if (!itemIds.add(String.valueOf(item.optInt("item_id")))) {
							continue;
						}

						// 餐谱明细最大值
						Integer maxDetailsId = this.queryForInt(detailsIdSql, new Object[]{});

						detailsParams = new Object[]{tenancyId, maxDetailsId, item.optInt("item_menu_id"), item.optInt("item_id"), DateUtil.parseTime(item.optString("starttime")), DateUtil.parseTime(item.optString("endtime")), item.optString("valid_state"), item.optString("last_operator"), timestamp, itemRank, null, item.optInt("is_show")};
						this.update(menuDetailsSql, detailsParams);

						classParams = new Object[]{tenancyId, maxDetailsId, maxDetailsId, item.optString("chanel"), item.optInt("menu_class"), item.optString("item_name"), item.optString("item_english"), item.optString("phonetic_code"), item.optString("five_code"), "", null};
						this.update(menuClassSql, classParams);
					}
				}
			}
		} catch (Exception e) {
			logger.error("其它渠道下添加套餐明细失败：" + ExceptionMessage.getExceptionMessage(e));
			e.printStackTrace();
		}

	}

	@Override
	public List<JSONObject> getDishFormat(String tenantId, Integer organId,
										  String channel) throws Exception {
		// TODO Auto-generated method stub
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject();

		StringBuilder sql = new StringBuilder();
		sql.append(" select d.item_class_id, d.page, d.show_count from hq_pad_item_show s ");
		sql.append(" inner join hq_pad_item_show_detail d on s.id = d.show_id ");
		sql.append(" inner join (select hic.id from hq_item_class hic left join hq_item_menu_class himc on himc.class = hic.id ");
		sql.append(" left join hq_item_menu_details himd on himc.details_id = himd.id ");
		sql.append(" left join hq_item_menu_organ himo on himd.item_menu_id = himo.item_menu_id ");
		sql.append(" where hic.chanel = '" + channel + "' and hic.valid_state = '1' and himc.chanel = '" + channel + "' ");
		sql.append(" and himd.valid_state = '1' and himo.store_id = " + organId + " and himo.tenancy_id = '" + tenantId + "' group by hic.id) t on d.item_class_id = t.id ");
		sql.append(" where s.valid_state = '1' ");

		logger.debug("getDishFormat:::" + sql.toString());

		List<JSONObject> dishFormats = this.queryString4Json(tenantId, sql.toString());
		if (dishFormats != null) {
			JSONArray dishFormatArray = JSONArray.fromObject(dishFormats);
			object.element("dish_format", dishFormatArray);
		}

		list.add(object);

		logger.debug("同步数据，菜品格式返回数据:---" + object.toString());
		return list;
	}

	@Override
	public List<JSONObject> getActivityRuleDetails(String tenancyId) throws Exception {
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject(); // data数组里的对象
		StringBuilder sql = new StringBuilder("SELECT car.item_unit_id,car.tenancy_id,car. ID,car.activity_id,car.reach_amount,car.discount_amount,COALESCE(car.item_id,0) as item_id,COALESCE(car.item_num,0) as item_num,ca.subject,ca.activity_type,cas.if_loop,cas.if_parallel,cas.bill_limit_times,cas.if_auto,cas.if_all_item FROM " +
				"crm_activity_rule car,crm_activity ca,crm_activity_sub_t cas WHERE	ca. ID = car.activity_id AND cas.activity_id = ca. ID AND car.tenancy_id = ?");
		logger.debug("getActivityRuleDetails:::" + sql.toString());
		List<JSONObject> brandList = this.queryString4Json(tenancyId, sql.toString(), new Object[]{tenancyId});
		if (brandList != null) {
			JSONArray activityRuleArray = JSONArray.fromObject(brandList);
			object.element("crm_activity_rule", activityRuleArray);
		}
		list.add(object);

		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return list;
	}


	@Override
	public List<JSONObject> getCrmActivityItem(String tenancyId)
			throws Exception {
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject(); // data数组里的对象
		StringBuilder sql = new StringBuilder("select cai.item_unit_id, cai.tenancy_id,cai.id,cai.activity_id,cai.item_type,cai.item_id,cai.rule_code,cai.item_num from crm_activity_item cai where tenancy_id=?");
		logger.debug("getCrmActivityItem:::" + sql.toString());
		List<JSONObject> brandList = this.queryString4Json(tenancyId, sql.toString(), new Object[]{tenancyId});
		if (brandList != null) {
			JSONArray activityRuleArray = JSONArray.fromObject(brandList);
			object.element("crm_activity_item", activityRuleArray);
		}
		list.add(object);

		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return list;
	}

	/**
	 * 同步数据字典
	 */
	public List<JSONObject> getSysDictionary(String tenancyId) throws Exception {
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject(); // data数组里的对象
		StringBuilder sql = new StringBuilder("select t.tenancy_id,t.id,t.model_name,t.application_model,t.class_identifier,t.class_identifier_code ");
		sql.append(" ,class_item,class_item_code,is_sys,remark,valid_state,last_operator,last_updatetime");
		sql.append(" from sys_dictionary t where t.tenancy_id=?  and class_identifier_code in ('pos_type','dinner_type','sales_model','deliver_type') and t.valid_state='1'");
		logger.debug("getSysDictionary:::" + sql.toString());
		List<JSONObject> authorizationList = this.queryString4Json(tenancyId, sql.toString(), new Object[]{tenancyId});
		if (authorizationList != null) {
			JSONArray sysDictionary = JSONArray.fromObject(authorizationList);
			object.element("sys_dictionary", sysDictionary);
		}
		list.add(object);
		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return list;
	}

	/**
	 * 同步数据字典
	 */
	public String getSysDictionaryEx(String tenancyId) throws Exception {
		StringBuilder sql = new StringBuilder("select t.tenancy_id,t.id,t.model_name,t.application_model,t.class_identifier,t.class_identifier_code ");
		sql.append(" ,class_item,class_item_code,is_sys,remark,valid_state,last_operator,last_updatetime");
		sql.append(" from sys_dictionary t where t.tenancy_id=?  and class_identifier_code in ('pos_type','dinner_type','sales_model')");
		logger.debug("getSysDictionary:::" + sql.toString());
		List<Map<String, Object>> dishes = this.jdbcTemplate.queryForList(sql.toString(), new Object[]{tenancyId});
		String tmp = org.apache.commons.lang.StringUtils.join(dishes.toArray(), "|");
		return JavaMd5.toMd5B32(tmp);
	}

	/**
	 * E待客同步数据字典
	 */
	public List<JSONObject> getAndroidSysDictionary(String tenancyId) throws Exception {
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject object = new JSONObject(); // data数组里的对象
		StringBuilder sql = new StringBuilder("select t.tenancy_id,t.id,t.model_name,t.application_model,t.class_identifier,t.class_identifier_code ");
		sql.append(" ,class_item,class_item_code,is_sys,remark,valid_state,last_operator,last_updatetime");
		sql.append(" from sys_dictionary t where t.tenancy_id=? and class_identifier_code = 'bank'");
		logger.debug("getAndroidSysDictionary:::" + sql.toString());
		List<JSONObject> authorizationList = this.queryString4Json(tenancyId, sql.toString(), new Object[]{tenancyId});
		if (authorizationList != null) {
			JSONArray sysDictionary = JSONArray.fromObject(authorizationList);
			object.element("android_sys_dictionary", sysDictionary);
		}
		list.add(object);
		logger.debug("同步数据，查询返回数据:---" + object.toString());
		return list;
	}

	@Override
	public void initPrinterSerialNumber(String tenancyId, int storeId, Date reportDate) throws Exception {
		String printerSql = " select distinct p.ip_com from hq_printer_new p where p.valid_state = '1' ";
		SqlRowSet rs = this.query4SqlRowSet(printerSql);

		Date date = new Date();
		int defaultSerialNum = 0; // 初始序号值

		List<Object[]> batchArgs = new ArrayList<>();

		while (rs.next()) {
			//日始初始化缓存----
			PrinterSerialNumber.addSerialNumber(rs.getString("ip_com"), 0);
			//-----------
			batchArgs.add(new Object[]{tenancyId, storeId, reportDate, rs.getString("ip_com"), defaultSerialNum, date});
		}

		String insertPrinterSql = "insert into pos_printer_serial_number (tenancy_id, store_id, report_date, ip_com, serial_number, last_update_time) values (?, ?, ?, ?, ?, ?)";

		this.batchUpdate(insertPrinterSql, batchArgs);
	}

	@Override
	public void synzPrinter(String tenancyId, int storeId) throws Exception {
		try {
			// 查询新增的物理打印机
			StringBuffer ipComSql = new StringBuffer();
			ipComSql.append(" select t.ip_com1 as ip_com from (select t1.ip_com as ip_com1, t2.ip_com as ip_com2 from ");
			ipComSql.append(" (select distinct p.ip_com from hq_printer_new p where p.valid_state = '1') t1 ");
			ipComSql.append(" left join (select distinct psn.ip_com from pos_printer_serial_number psn where psn.report_date = (select max(report_date) from pos_printer_serial_number where tenancy_id = ? and store_id = ?) and psn.tenancy_id = ? and psn.store_id = ?) t2 ");
			ipComSql.append(" on t1.ip_com = t2.ip_com) t where t.ip_com2 is null ");

			List<JSONObject> rs = this.queryString4Json(tenancyId, ipComSql.toString(), new Object[]{tenancyId, storeId, tenancyId, storeId});

			if (!rs.isEmpty()) {
				// 查询报表日期
				String reportDateSql = "select max(report_date) as report_date from pos_printer_serial_number where tenancy_id = ? and store_id = ?";
				SqlRowSet rowSet = this.query4SqlRowSet(reportDateSql, new Object[]{tenancyId, storeId});
				Date reportDate = null;
				if (rowSet.next() && rowSet.getDate("report_date") != null) {
					reportDate = rowSet.getDate("report_date");
				} else {
					reportDateSql = "select max(s.report_date) as report_date from pos_opt_state s where s.content = 'DAYBEGAIN' and s.tenancy_id = ? and s.store_id = ?";
					rowSet = this.query4SqlRowSet(reportDateSql, new Object[]{tenancyId, storeId});
					if (rowSet.next() && rowSet.getDate("report_date") != null) {
						reportDate = rowSet.getDate("report_date");
					} else {
						return;
					}
				}

				List<JSONObject> serialNums = new ArrayList<>();

				Date date = new Date();
				int defaultSerialNum = 0; // 初始序号值
				List<Object[]> batchArgs = new ArrayList<Object[]>();

				for (int i = 0; i < rs.size(); i++) {
					batchArgs.add(new Object[]{tenancyId, storeId, reportDate, rs.get(i).getString("ip_com"), defaultSerialNum, date});
					JSONObject json = new JSONObject();
					json.put("ip_com", rs.get(i).getString("ip_com"));
					json.put("serial_number", defaultSerialNum);
					serialNums.add(json);
				}
				// 插入打印序号表
				String insertPrinterSql = "insert into pos_printer_serial_number (tenancy_id, store_id, report_date, ip_com, serial_number, last_update_time) values (?, ?, ?, ?, ?, ?)";
				this.batchUpdate(insertPrinterSql, batchArgs);

				// 新增的物理打印机放入缓存中
				PrinterSerialNumber.setSerials(serialNums);
			}
		} catch (Exception e) {
			logger.error("加载厨打打印序号失败...", e);
			throw e;
		}
	}

	@Override
	public void deleteBeforePrinterSerial(String tenancyId, int storeId, Date reportDate) throws Exception {
		Date date = new Date(reportDate.getTime() - 15 * 24 * 60 * 60 * 1000);
		String deletePrinterSql = "delete from pos_printer_serial_number where report_date < ? and tenancy_id = ? and store_id = ? ";

		this.update(deletePrinterSql, new Object[]{date, tenancyId, storeId});
	}

	@Override
	public List<JSONObject> getMenuButtonConfig(String tenantId, Integer organId) throws Exception {
		String buttonConfigSql = "select c.business_id, c.type, c.display_order, (select pw.payment_class from payment_way pw where cast(pw.id as varchar) = c.business_id and c.type='3') as payment_class from hq_device_button_config c where c.tenancy_id = ? and c.store_id = ? order by c.type asc ";

		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject obj = new JSONObject();
		List<JSONObject> configList = this.query4Json(tenantId, buttonConfigSql, new Object[]{tenantId, organId});
		if (configList != null) {
			JSONArray configInfos = JSONArray.fromObject(configList);
			obj.element("menu_button_config", configInfos);
		}

		list.add(obj);
		logger.debug("----菜单按钮配置信息----" + obj.toString());

		return list;
	}

	/**
	 * @Description: 手机端退菜 根据账单编号 查询可选菜品id 沽清信息
	 * @Title:getBatchNumByBillNum
	 * @param:@param tenantId
	 * @param:@param billNum
	 * @param:@return
	 * @param:@throws Exception
	 * @return: List<JSONObject>
	 * @author: shenzhanyu
	 * @version: 1.0
	 * @Date: 2018年1月10日
	 */
	public List<JSONObject> getBatchNumByBillNum(String tenantId, String billNum, String itemId) throws Exception {

		String querySql = " select DISTINCT item.item_id,item.item_count from pos_bill_item item where item.batch_num = (SELECT batch_num from  pos_bill where bill_num='" + billNum + "') ";
		if (itemId != null && !itemId.equals("")) {
			querySql += " and item.item_id='" + itemId + "'";
		}
		querySql += " and yrwid is null and item.item_id in(SELECT sold.item_id from pos_soldout sold )";
		List<JSONObject> itemList = this.query4Json(tenantId, querySql);
		return itemList;
	}

	@Override
	public List<JSONObject> getPaymentWayButtonConfig(String tenantId, Integer organId) throws Exception {

		String buttonConfigSql = "SELECT tenancy_id, id, payment_id, organ_id, sorting_type, sorting_code, create_time FROM pos_payment_way_sorting WHERE tenancy_id=? AND organ_id=? ";

		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject obj = new JSONObject();
		List<JSONObject> configList = this.query4Json(tenantId, buttonConfigSql, new Object[]{tenantId, organId});
		if (configList != null) {
			JSONArray configInfos = JSONArray.fromObject(configList);
			obj.element("paymentway_button_config", configInfos);
		}

		list.add(obj);
		logger.debug("----支付按钮配置信息----" + obj.toString());

		return list;
	}

	@Override
	public boolean payWayIsOpenCashBox(String tenancyId, int storeId, String billNum, String printMode) throws Exception {
		if (SysDictionary.PRINT_CODE_1102.equals(printMode) && StringUtils.isNotEmpty(billNum)) {
			StringBuilder cashBoxSql = new StringBuilder();
			cashBoxSql.append(" select count(w.id) as counts from pos_bill_payment p left join payment_way w on p.jzid = w.id and p.tenancy_id = w.tenancy_id ");
			cashBoxSql.append(" where p.bill_num = '" + billNum + "' and p.store_id = " + storeId + " and p.tenancy_id = '" + tenancyId + "' and w.status = '1' and w.is_open_cashbox = 1 ");

			int counts = this.getInt(tenancyId, cashBoxSql.toString());
			if (counts > 0) {
				return true;
			}
		}

		return false;
	}

	@Override
	public void deletePosPrintTask(String tenancyId, int storeId) throws Exception {
		// 当前系统日期
		String currentDateStr = DateUtil.getNowDateYYDDMM();
		Date currentDate = DateUtil.parseDate(currentDateStr);

		// 10天前的日期
		Date tenBeforeDate = new Date(currentDate.getTime() - 10 * 24 * 60 * 60 * 1000);
		// 删除10天前的打印数据
		String deleteBakSql = "delete from pos_print_task where send_time < '" + DateUtil.format(tenBeforeDate) + " 00:00:00" + "' and tenancy_id = ? and store_id = ? ";
		this.update(deleteBakSql, new Object[]{tenancyId, storeId});
	}

	@Override
	public Boolean getBillCode(String tenantId, Integer organId, String billCode) throws Exception {
		String sql = "select order_num from pos_bill t where tenancy_id=? and store_id=?  and order_num = ?";
		SqlRowSet rs = this.jdbcTemplate.queryForRowSet(sql, new Object[]{tenantId, organId, billCode});
		if (rs.next()) {
			return true;
		}
		return false;
	}

	@Override
	public List<JSONObject> getDiningType(String tenantId, Integer organId) throws Exception {
		String diningTypeSql = "select  hdt.id,hdt.dining_code,hdt.dining_name,COALESCE(hdt.order_num,'0') order_num,hdt.remark,hdt.valid_state,hdt.is_open from hq_dining_type hdt LEFT JOIN hq_dining_type_organ hdto on hdt.tenancy_id = hdto.tenancy_id AND hdt.id = hdto.dining_type_id where hdt.tenancy_id = ? and hdto.store_id = ? and hdt.valid_state = '1' order by hdt.dining_code asc";
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject obj = new JSONObject();
		List<JSONObject> configList = this.query4Json(tenantId, diningTypeSql, new Object[]{tenantId, organId});
		if (configList != null) {
			JSONArray configInfos = JSONArray.fromObject(configList);
			obj.element("dining_type", configInfos);
		}

		list.add(obj);
		logger.debug("----就餐类型信息----" + obj.toString());

		return list;
	}

	@Override
	public List<JSONObject> getHqItemTasteDetails(String tenantId, Integer organId) throws Exception {
		String sql = "select id, id as tid,item_id,taste_id,taste_code,taste_name,last_operator,last_updatetime from hq_item_taste_details";
		List<JSONObject> jsonList = this.queryString4Json(tenantId, sql);
		JSONObject json = new JSONObject();
		json.put("item_taste", jsonList);
		return Arrays.asList(json);
	}

	@Override
	public String getDiningTypeByTenantIdEx(String tenantId, Integer organId) throws Exception {
		StringBuilder sb = new StringBuilder("select  hdt.id,hdt.dining_code,hdt.dining_name,COALESCE(hdt.order_num,'0') order_num,hdt.remark,hdt.valid_state,hdt.is_open from hq_dining_type hdt LEFT JOIN hq_dining_type_organ hdto on hdt.tenancy_id = hdto.tenancy_id AND hdt.id = hdto.dining_type_id where hdt.tenancy_id = ? and hdto.store_id = ? and hdt.valid_state = '1' order by hdt.dining_code asc");
		List<Map<String, Object>> dishes = this.jdbcTemplate.queryForList(sb.toString(), new Object[]{tenantId, organId});
		String tmp = org.apache.commons.lang.StringUtils.join(dishes.toArray(), "|");
		return JavaMd5.toMd5B32(tmp);
	}

	@Override
	public void updateKvsMopState(String tenancyId, Integer storeId, String kvsNum, Timestamp currentTime) throws Exception {
		StringBuilder sql = new StringBuilder();
		sql.append(" UPDATE pos_kvs_bill SET upload_tag=0,kvs_num = ?, mop_time = ?, meal_consume= to_timestamp(to_char(? - order_time, 'HH24:MI:SS'), 'HH24:MI:SS')");
		sql.append(" where tenancy_id = ? and store_id = ? AND mop_time IS NULL;");

		sql.append(" UPDATE pos_kvs_bill_item ki SET upload_tag=0,is_out = ?,kvs_num = ?,mop_time = ?,");
		sql.append(" meal_consume= to_timestamp(to_char(? - kl.order_time, 'HH24:MI:SS'), 'HH24:MI:SS') 	");
		sql.append(" from pos_kvs_bill kl where ki.tenancy_id=kl.tenancy_id and ki.store_id=kl.store_id and ki.bill_num=kl.bill_num");
		sql.append(" and ki.tenancy_id = ? and ki.store_id = ? and ki.mop_time is null ; ");

		this.update(sql.toString(), new Object[]
				{kvsNum, currentTime, currentTime, tenancyId, storeId, Constant.DISH_STATE_COMPLETE, kvsNum, currentTime, currentTime, tenancyId, storeId});
	}

	@Override
	public List<JSONObject> getHqPrinter(String tenantId, Integer organId) throws Exception {
		List<JSONObject> data = this.query4Json(tenantId, "SELECT DISTINCT\thpn.id print_id, hpn.name print_name,hpn.ip_com printer_address FROM hq_printer_new hpn LEFT JOIN hq_printer_item hpi ON hpi.print_id = hpn. ID LEFT JOIN hq_printer_model hpm ON hpn. ID = hpm.printer_id WHERE hpm.format_type IN ('1107', '1113', '1106') AND hpm.is_enables = '1' AND hpn.valid_state = '1' and hpn.tenancy_id='" + tenantId + "' and hpn.store_id=" + organId);
		JSONObject json = new JSONObject();
		json.put("hq_printer", data);
		return Arrays.asList(json);
	}

    @Override
	public List<JSONObject> getPosBillItemByBillNum(String tenantId, Integer organId, String bill_num) throws Exception {
		List<JSONObject> dishs = this.query4Json(tenantId, "SELECT id from pos_bill_item where tenancy_id='" + tenantId + "' and store_id=" + organId + " and bill_num='" + bill_num + "'");
		return dishs;
	}

    @Override
	public List<JSONObject> getPosForbiddenItem(String tenantId, Integer organId) throws Exception {
		String querySql = "SELECT  item_id,unit_id,opt_num,updatetime from pos_forbidden_item where tenancy_id='" + tenantId + "' and store_id=" + organId;
		List<JSONObject> jsonList = this.query4Json(tenantId, querySql);
		JSONObject json = new JSONObject();
		json.put("forbidden_item", jsonList);
		return Arrays.asList(json);
	}

    @Override
    public List<JSONObject> getPosItemComboDetailsDefault(String tenantId, Integer organId) throws Exception {
        String querySql = "SELECT  id,tenancy_id,store_id,setmeal_id,group_id,group_item_id,unit_id,pos_num,opt_num,updatetime from pos_item_combo_details_default where tenancy_id='" + tenantId + "' and store_id=" + organId;
        List<JSONObject> jsonList = this.query4Json(tenantId, querySql);
        JSONObject json = new JSONObject();
        json.put("setting_setmeal_details", jsonList);
        return Arrays.asList(json);
    }

    @Override
    public String getTableNameByTableCode(String tenantId, Integer organId, String sbill_num) throws Exception {
        String tableName = "";
        if (Tools.hv(sbill_num) && sbill_num.contains(",")) {
            String[] sbill_nums = sbill_num.split(",");
            if (sbill_nums.length > 0) {
                for (int i = 0; i < sbill_nums.length; i++) {
                    String bill_num = sbill_nums[i];
                    String querySql = "SELECT  ti.table_name from tables_info ti left join pos_bill pb on pb.table_code = ti.table_code where pb.tenancy_id=? and pb.store_id=? and pb.bill_num=?";
                    List<JSONObject> jsonList = this.query4Json(tenantId, querySql, new Object[]{tenantId, organId, bill_num});
                    if (!jsonList.isEmpty()) {
                        tableName += jsonList.get(0).optString("table_name") + ",";
                    }
                }
            }
        } else {
            String querySql = "SELECT  ti.table_name from tables_info ti left join pos_bill pb on pb.table_code = ti.table_code where pb.tenancy_id=? and pb.store_id=? and pb.bill_num=?";
            List<JSONObject> jsonList = this.query4Json(tenantId, querySql, new Object[]{tenantId, organId, sbill_num});
            if (!jsonList.isEmpty()) {
                tableName = jsonList.get(0).optString("table_name");
            }
        }
        return tableName;
    }

    @Override
    public List<JSONObject> getRoles(String tenantId) throws Exception {
        String querySql = "select id,role_name,role_level,uplevel_role_id from roles where tenancy_id='" + tenantId + "' and  state ='1'";
        List<JSONObject> jsonList = this.query4Json(tenantId, querySql);
        JSONObject json = new JSONObject();
        json.put("roles", jsonList);
        return Arrays.asList(json);
    }

    @Override
    public String findUserName(String tenantId, Integer organId, String posUserName) throws Exception {
        String username = "";
        String querySql = "select id,user_name from user_authority where tenancy_id='" + tenantId + "' and store_id = " + organId + " and valid_state ='1' and pos_user_name='" + posUserName + "'";
        List<JSONObject> jsonList = this.query4Json(tenantId, querySql);
        if (jsonList != null && jsonList.size() > 0) {
            JSONObject user = JSONObject.fromObject(jsonList.get(0));
            username = user.optString("user_name");
        }
        return username;
    }

    @Override
	public void addEmpAndAuth(String  tenantId,Integer organId,JSONObject paraJson) throws Exception {
		StringBuffer empSql = new StringBuffer();
		StringBuffer userAuthSql = new StringBuffer();
		StringBuffer userAuthRolesSql = new StringBuffer();

		empSql.append(" insert into employee (id,states, sex, paper_no, phone, email, birthday, education, id_card, is_married, pay_limit, name,employee_code,tenancy_id,store_id ) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
		this.update(empSql.toString(),new Object[]{paraJson.optInt("id"),"1",paraJson.optString("sex"),paraJson.optString("paper_no"),
				paraJson.optString("phone"),paraJson.optString("email"),DateUtil.parseDate(paraJson.optString("birthday")),paraJson.optString("education"),paraJson.optString("id_card"),
				paraJson.optString("is_married"),paraJson.optDouble("pay_limit"),paraJson.optString("name"),paraJson.optString("employee_code"),tenantId,organId});

		userAuthSql.append(" insert into user_authority (id, user_name, pos_user_name, roles_id, employee_id, valid_state, password, tenancy_id, store_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
		this.update(userAuthSql.toString(),new Object[]{paraJson.optInt("user_authority_id"),paraJson.optString("user_name"),paraJson.optString("pos_user_name"),paraJson.optInt("roles"),
				paraJson.optInt("id"),"1",paraJson.optString("password"),tenantId,organId}); //"e10adc3949ba59abbe56e057f20f883e"

		userAuthRolesSql.append("insert into user_authority_roles (tenancy_id, id, user_id, store_id, roles_id, employee_id) values (?,?,?,?,?,?)");
		this.update(userAuthRolesSql.toString(),new Object[]{tenantId,paraJson.optInt("user_authority_roles_id"),paraJson.optInt("user_authority_id"),organId,paraJson.optInt("roles"),paraJson.optInt("id")});


	}

	@Override
	public void updateEmployeeInfo(String  tenantId,Integer organId,JSONObject paraJson,Oper opertype) throws Exception{

		StringBuffer updateEmpSql = new StringBuffer();
		StringBuffer updateuserAuthSql = new StringBuffer();
		if(Oper.delete==opertype){
			updateEmpSql.append("update employee set states=? where tenancy_id=? and store_id=? and id=? ");
			this.update(updateEmpSql.toString(), new Object[]{paraJson.optString("states"), tenantId,organId,paraJson.optInt("id")});
			updateuserAuthSql.append("update user_authority set valid_state=? where tenancy_id=? and store_id=? and employee_id=? ");
			this.update(updateuserAuthSql.toString(), new Object[]{ paraJson.optString("states"),tenantId,organId,paraJson.optInt("id")});

		}
		else if(Oper.update==opertype)
		{
			updateEmpSql.append("update employee set sex = ? ,paper_no=?,phone =?,email = ?,name=?,birthday =?,education =?,id_card=?,is_married=?,pay_limit=?,states=? where tenancy_id=? and store_id=? and id=? ");
			this.update(updateEmpSql.toString(), new Object[]{paraJson.optString("sex"), paraJson.optString("paper_no"), paraJson.optString("phone"), paraJson.optString("email"),
					paraJson.optString("name"),DateUtil.parseDate(paraJson.optString("birthday")), paraJson.optString("education"),
					paraJson.optString("id_card"), paraJson.optString("is_married"),paraJson.optDouble("pay_limit") ,"1", tenantId,organId,paraJson.optInt("id") });

			updateuserAuthSql.append("update user_authority set user_name=?,pos_user_name=?,password=?,roles_id=? ,valid_state=? where tenancy_id=? and store_id=? and id=? ");
			this.update(updateuserAuthSql.toString(), new Object[]{ paraJson.optString("user_name"), paraJson.optString("pos_user_name"),paraJson.optString("password"), paraJson.optInt("roles_id"),"1",tenantId,organId,paraJson.optInt("user_authority_id")});
    	}
	}

	@Override
	public JSONObject findItemComboDetails(String tenancyId, int setmeal_id, int item_id) throws Exception {
		StringBuffer sb = new StringBuffer();
		sb.append("select * from hq_item_combo_details where iitem_id = ? and details_id = ? order by id desc");
		List<JSONObject> list = this.query4Json(tenancyId, sb.toString(), new Object[]{setmeal_id,item_id});
		return list.isEmpty() ? null : list.get(0);
	}
}

