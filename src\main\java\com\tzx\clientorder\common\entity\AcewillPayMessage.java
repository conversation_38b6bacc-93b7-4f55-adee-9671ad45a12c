package com.tzx.clientorder.common.entity;

import java.util.List;

import com.tzx.base.scoket.acewill.WebSocketMessageHeader;

public class AcewillPayMessage extends WebSocketMessageHeader{

	private String tenent_id;
	private Integer store_id;
	
	/**
	 * 桌号
	 */
	private String table;
	
	private AcewillPayData data;
	/**
	 * 支付明细
	 */
	private List<AcewillPayInfo> pay_info;
	/**
	 * 会员支付前信息
	 */
	private AcewillPayWLife wlife;

	public String getTable() {
		return table;
	}

	public void setTable(String table) {
		this.table = table;
	}

	public AcewillPayData getData() {
		return data;
	}

	public void setData(AcewillPayData data) {
		this.data = data;
	}

	public List<AcewillPayInfo> getPay_info() {
		return pay_info;
	}

	public void setPay_info(List<AcewillPayInfo> pay_info) {
		this.pay_info = pay_info;
	}

	public AcewillPayWLife getWlife() {
		return wlife;
	}

	public void setWlife(AcewillPayWLife wlife) {
		this.wlife = wlife;
	}

	public String getTenent_id() {
		return tenent_id;
	}

	public void setTenent_id(String tenent_id) {
		this.tenent_id = tenent_id;
	}

	public Integer getStore_id() {
		return store_id;
	}

	public void setStore_id(Integer store_id) {
		this.store_id = store_id;
	}
}
