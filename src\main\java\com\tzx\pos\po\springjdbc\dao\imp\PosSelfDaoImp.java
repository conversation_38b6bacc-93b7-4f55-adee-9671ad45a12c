package com.tzx.pos.po.springjdbc.dao.imp;

import java.lang.reflect.Method;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.tzx.base.entity.PosBillPayment;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.Tools;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.dao.imp.BaseDaoImp;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.bo.dto.PosBill;
import com.tzx.pos.bo.dto.PosBillBatch;
import com.tzx.pos.bo.dto.PosBillBatchs;
import com.tzx.pos.bo.dto.PosBillItem;
import com.tzx.pos.bo.dto.PosBillMember;
import com.tzx.pos.bo.dto.PosBillMembers;
import com.tzx.pos.bo.dto.PosBillPaymentWay;
import com.tzx.pos.bo.dto.PosBillPaymentWays;
import com.tzx.pos.bo.dto.PosBillService;
import com.tzx.pos.bo.dto.PosBillServices;
import com.tzx.pos.bo.dto.PosItemMethod;
import com.tzx.pos.po.springjdbc.dao.PosSelfDao;

import net.sf.json.JSONObject;

@Repository(PosSelfDao.NAME)
public class PosSelfDaoImp extends BaseDaoImp implements PosSelfDao
{
	@Override
	public void updatePosBill(String tenancyId, int storeId, JSONObject bill) throws Exception
	{
		this.updateIgnorCase(tenancyId, "pos_bill", bill);
	}

	@Override
	public List<JSONObject> queryPosBillByBillNum(String tenancyId, int storeId, String billNum) throws Exception
	{
		StringBuilder querySql = new StringBuilder("select * from pos_bill where tenancy_id=? and store_id=? and bill_num=?");
		return this.query4Json(tenancyId, querySql.toString(),new Object[]{tenancyId,storeId,billNum});
	}
	
	@Override
	public JSONObject queryPosBillForOpenByTablecode(String tenancyId, int storeId, String tableCode) throws Exception
	{
		JSONObject resultJson = new JSONObject();
		StringBuilder querySql = new StringBuilder("select * from pos_bill where tenancy_id=? and store_id=? and table_code=? and bill_property = ?");
		List<JSONObject> billList = this.query4Json(tenancyId, querySql.toString(), new Object[]
		{ tenancyId, storeId, tableCode ,SysDictionary.BILL_PROPERTY_OPEN});
		if (billList != null && billList.size() > 0)
		{
			resultJson = billList.get(0);
		}
		return resultJson;
	}

	@Override
	public Integer insertBillBatch(String tenancyId, int storeId, JSONObject billBatch) throws Exception
	{
		Object obj = this.insertIgnorCase(tenancyId, "pos_bill_batch", billBatch);
		if (Tools.hv(obj))
		{
			return Integer.parseInt(obj.toString());
		}
		else
		{
			return 0;
		}
	}

	@Override
	public JSONObject queryBillBatchByBillNum(String tenancyId, int storeId, String billNum, String batchNum) throws Exception
	{
		JSONObject resultJson = new JSONObject();
		StringBuilder querySql = new StringBuilder("select * from pos_bill_batch where tenancy_id=? and store_id=? and bill_num=? and batch_num=?");
		List<JSONObject> billList = this.query4Json(tenancyId, querySql.toString(), new Object[]
		{ tenancyId, storeId, billNum, batchNum });
		if (billList != null && billList.size() > 0)
		{
			resultJson = billList.get(0);
		}
		return resultJson;
	}

	@Override
	public void updateBillBatchForFreeAmountByBatchNum(String tenancyId, int storeId, String billNum, String batchNum, double freeAmount, int reasonId) throws Exception
	{
		String updateBillSql = new String("update pos_bill_batch set free_amount=?,billfree_reason_id=? where batch_num=? and bill_num = ? and store_id = ? and tenancy_id = ?");
		this.update(updateBillSql, new Object[]
		{ freeAmount, reasonId, batchNum, billNum, storeId, tenancyId });
	}

	@Override
	public void updateBillItemForItemRemarkByBatchNum(String tenancyId, int storeId, String billNum, String batchNum, String itemRemark) throws Exception
	{
		String updateBillItemSql = new String("update pos_bill_item set item_remark=? where tenancy_id = ? and store_id = ? and bill_num = ? and batch_num =? and item_remark is null");
		this.update(updateBillItemSql, new Object[]
		{ itemRemark, tenancyId, storeId, billNum, batchNum });
	}

	@Override
	public void deletePosBillPaymentById(String tenancyId, int storeId, int payId) throws Exception
	{
		String deletePaymentSql = new String("delete from pos_bill_payment where tenancy_id=? and store_id = ? and id=?");
		this.update(deletePaymentSql.toString(), new Object[]
		{ tenancyId, storeId, payId });
	}

	@Override
	public void updatePosBillPayment(String tenancyId, int storeId, JSONObject payment) throws Exception
	{
		this.updateIgnorCase(tenancyId, "pos_bill_payment", payment);
	}

	@Override
	public int queryPosBillPaymentForCountByBatchNum(String tenancyId, int storeId, String billNum, String batchNum) throws Exception
	{
		String sql = new String("select count(*) as count from pos_bill_payment where tenancy_id=? and store_id = ? and bill_num =? and batch_num =?");
		int count = this.queryForInt(sql.toString(), new Object[]
		{ tenancyId, storeId, billNum, batchNum });
		return count;
	}

	@Override
	public JSONObject queryPosBillPaymentById(String tenancyId, int storeId, int payId) throws Exception
	{
		JSONObject resultJson = new JSONObject();
		StringBuilder queryPaymentSql = new StringBuilder("select * from pos_bill_payment where tenancy_id=? and store_id=? and id=?");
		List<JSONObject> paymentList = this.query4Json(tenancyId, queryPaymentSql.toString(), new Object[]
		{ tenancyId, storeId, payId });
		if (paymentList != null && paymentList.size() > 0)
		{
			resultJson = paymentList.get(0);
		}
		return resultJson;
	}

	@Override
	public JSONObject queryPosBillPaymentByJzid(String tenancyId, int storeId, String billNum, String batch_num, int jzid) throws Exception
	{
		JSONObject resultJson = new JSONObject();
		StringBuilder findPaymentSql = new StringBuilder("select * from pos_bill_payment where tenancy_id=? and store_id = ? and bill_num = ? and batch_num=? and jzid = ?");
		List<JSONObject> paymentList = this.query4Json(tenancyId, findPaymentSql.toString(), new Object[]
		{ tenancyId, storeId, billNum, batch_num, jzid });

		if (paymentList != null && paymentList.size() > 0)
		{
			resultJson = paymentList.get(0);
		}
		return resultJson;
	}

	public List<JSONObject> queryPosBillPaymentForPayingByBatch(String tenancyId, int storeId, String billNum, String batch_num) throws Exception
	{
		StringBuilder querySql = new StringBuilder("select bp.*,pw.payment_class,pw.is_check from pos_bill_payment bp left join payment_way pw on bp.jzid=pw.id where bp.tenancy_id=? and bp.store_id = ? and bp.bill_num =? and bp.batch_num=? and bp.payment_state=? order by bp.last_updatetime");

		return this.query4Json(tenancyId, querySql.toString(), new Object[]
		{ tenancyId, storeId, billNum, batch_num, SysDictionary.PAYMENT_STATE_PAY });
	}
	
	@Override
	public List<JSONObject> getPosBillBatchListByBillnum(String tenancyId, Integer storeId, String billnum) throws SystemException
	{
		StringBuilder queryBillSql = new StringBuilder("select bill_num,payment_state,bill_amount,payment_amount,bill_property,batch_num from pos_bill_batch where tenancy_id =? and store_id =? and bill_num = ? and bill_property = ? ");
		List<JSONObject> lists = new ArrayList<JSONObject>();
		try 
		{
			lists = this.query4Json(tenancyId, queryBillSql.toString(), new Object[]
			{ tenancyId, storeId, billnum, SysDictionary.BILL_PROPERTY_OPEN });
		}catch (Exception se)
		{
			se.printStackTrace();
		}
		return lists;
	}
	
	@Override
	public List<JSONObject> findBatch(String tenancyId ,int storeId ,String billNum, String batchNum) throws Exception
	{
		StringBuilder sql = new StringBuilder("select bill_num,bill_amount,bill_state,payment_amount,table_code,source,order_num from pos_bill_batch where bill_num = '" + billNum + "' and batch_num = '" + batchNum + "' and store_id = " + storeId);
		return this.queryString4Json(tenancyId, sql.toString());
	}
	
	@Override
	public void updateBatchReturnAmt(String tenancyId ,int storeId ,String billNum, String batchNum) throws Exception
	{
		StringBuilder sql = new StringBuilder("update pos_bill_batch set bill_state=? ,return_amount = (select abs(sum(pbi.real_amount)) from pos_bill_item pbi where pbi.item_remark = ? and pbi.item_property <>'MEALLIST' and pbi.bill_num = ? and pbi.batch_num = ? and pbi.store_id = ?) where bill_num = ? and batch_num = ? and store_id = ?");
		this.jdbcTemplate.update(sql.toString(),new Object[]{SysDictionary.BILL_STATE_ZDQX02,SysDictionary.ITEM_REMARK_TC01,billNum,batchNum,storeId,billNum,batchNum,storeId});
	}
	
	public JSONObject getItemTime(String tenancyId, int storeId, String billNum, String batchNum) throws Exception
	{
		StringBuilder querySql = new StringBuilder(" select distinct item_time from pos_bill_item where tenancy_id=? and store_id=? and bill_num=? and batch_num=? ");
		SqlRowSet rs = this.query4SqlRowSet(querySql.toString(), new Object[]
				{ tenancyId, storeId, billNum, batchNum });
		
		JSONObject jo = new JSONObject();
		if(rs.next())
		{
			jo.put("item_time", DateUtil.format(rs.getTimestamp("item_time")));
		}
		return jo;
	}
	
	@Override
    @Transactional(propagation = Propagation.REQUIRES_NEW,rollbackFor = Exception.class) //生产环境中pos_bill_payment_log容易损坏导致正常业务无法正常完成，故新建事务
	public void insertPosBillPaymentLog(String tenancyId, int storeId, String billNum, Date reportDate, int shiftId, String posNum, String optNum, String tableCode, String type, int jzid, String name, String nameEnglish, double rate, double amount, double count, double currencyAmount,
			String number, String phone, String isYsk, Integer customerId, String billCode, String remark, Timestamp lastUpdatetime, String paymentState, String batchNum, String paramCach, double more_coupon) throws Exception
	{
		StringBuilder insertPaymentLogSql = new StringBuilder(
				"insert into pos_bill_payment_log(tenancy_id,store_id,bill_num,report_date,shift_id,pos_num,cashier_num,table_code,type,jzid,name,name_english,rate,amount,count,currency_amount,number,phone,is_ysk,customer_id,bill_code,remark,last_updatetime,upload_tag,payment_state,batch_num,param_cach,more_coupon) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,0,?,?,?,?)");
		this.update(insertPaymentLogSql.toString(), new Object[]
		{ tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, type, jzid, name, nameEnglish, rate, amount, count, currencyAmount, number, phone, isYsk, customerId, billCode, remark, lastUpdatetime, paymentState, batchNum, paramCach, more_coupon });
	}
	
	@Override
	public void closeBill(String tenancyId, Integer storeId, String billno) throws SystemException
	{
		try
		{
			StringBuffer updateBillSql = new StringBuffer("update pos_bill set payment_time=?, bill_property = ?, payment_state = ? where bill_num = ? and store_id = ? and tenancy_id = ?");
			Timestamp timef = DateUtil.currentTimestamp();
			this.update(updateBillSql.toString(), new Object[]
			{timef, SysDictionary.BILL_PROPERTY_CLOSED, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, billno, storeId, tenancyId });
		}catch (Exception se)
		{
			se.printStackTrace();
		}
	}
	
	@Override
	public void updateTableState(String tenancyId, Integer storeId, String tableCode) throws SystemException
	{
		StringBuffer updateTableState = new StringBuffer("update pos_tablestate set state = ?,lock_pos_num = ?,lock_opt_num=?,bill_batch_num=?,opt_name=? where table_code = ? and store_id = ? and tenancy_id = ?");

		try
		{
			this.update(updateTableState.toString(), new Object[]
			{ SysDictionary.TABLE_STATE_FREE, null, null, null, null, tableCode, storeId, tenancyId });
		}catch (Exception se)
		{
			se.printStackTrace();
		}
	}
	
	@Override
	public List<JSONObject> findBill(String tenancyId ,int storeId ,String billNum) throws Exception
	{
		StringBuilder sql = new StringBuilder("select bill_num,bill_amount,bill_state,payment_amount,table_code,source,order_num,bill_property,payment_state from pos_bill where bill_num = '" + billNum + "' and store_id = " + storeId);
		return this.queryString4Json(tenancyId, sql.toString());
	}
	
	@Override
	public List<JSONObject> findBillPayment(String tenancyId ,int storeId ,String billNum, String batchNum) throws Exception
	{
		StringBuilder sql = new StringBuilder("select bill_num,currency_amount,payment_state,coalesce(more_coupon,0) as more_coupon from pos_bill_payment where bill_num = '" + billNum + "' and batch_num = '" + batchNum + "' and store_id = " + storeId);
		return this.queryString4Json(tenancyId, sql.toString());
	}
	
	@Override
	public List<JSONObject> findBillItem(String tenancyId ,int storeId ,String billNum, String batchNum) throws Exception
	{
		StringBuilder sql = new StringBuilder("select item_time,item_serial,rwid,item_id,item_num,item_name,item_remark,item_property,round(discount_rate,2) as discount_rate,round(discount_amount,2) as discount_amount,round(item_price,2) as item_price,round(item_count,2) as count,round(item_amount,2) as item_amount,round(real_amount,2) as real_amount, " +
				" remark,item_unit_id as unit_id,details_id,item_taste,sale_mode,setmeal_id,setmeal_rwid from pos_bill_item where bill_num = '" + billNum + "' and batch_num = '" + batchNum + "' and store_id = " + storeId + " and item_property <> '" + SysDictionary.ITEM_PROPERTY_MEALLIST + "'  and (item_remark <> '" + SysDictionary.ITEM_REMARK_FS02 + "' or item_remark is null) " );
		return this.queryString4Json(tenancyId, sql.toString());
	}

	@Override
	public Data newOrderDish(String tenancyid, String billno, Integer organId, String mode) throws SystemException
	{
		Data data = new Data();	
		StringBuffer posbill = new StringBuffer();		
		posbill.append(" select pb.dinner_type,pb.bill_taste,pb.order_num,pb.fictitious_table, pb.table_code,pb.bill_num,pb.batch_num,pb.serial_num,pb.report_date,pb.shift_id,pb.opentable_time,pb.open_pos_num,pb.open_opt,pb.payment_time,pb.pos_num,pb.cashier_num,pb.waiter_num,round(pb.difference, 2) as difference,");
		posbill.append(" pb.service_id,hsft.name as service_name,round(pb.service_amount, 2) as service_amount,round(pb.subtotal, 2) as subtotal,round(pb.payment_amount, 2) as payment_amount,round(pb.discountk_amount, 2) as discountk_amount,");
		posbill.append(" round(pb.discountr_amount, 2) as discountr_amount,round(pb.discount_amount, 2) as discount_amount,round(pb.maling_amount, 2) as maling_amount,round(pb.average_amount, 2) as average_amount,round(pb.givi_amount, 2) as givi_amount,");
		posbill.append(" pb.discount_rate,pb.discount_case_id,hdc.discount_case_name, pb.discount_mode_id,pb.guest,pb.guest_msg,pb.print_count,pb.bill_state,pb.bill_property,pb.remark,pb.payment_state,pb.copy_bill_num,pb.source as chanel,pb.sale_mode as sale_mode");
		posbill.append(" ,(select round(coalesce(sum(bi.item_amount),0),2) from pos_bill_item bi where bi.tenancy_id=pb.tenancy_id and bi.store_id=pb.store_id and bi.bill_num=pb.bill_num and (bi.item_remark<>'"+SysDictionary.ITEM_REMARK_FS02+"' or bi.item_remark is null) and bi.item_property<>'"+SysDictionary.ITEM_PROPERTY_MEALLIST+"') as subtotal2");
		posbill.append(" ,(select round(coalesce(sum(bi.item_amount),0),2) from pos_bill_item bi where bi.tenancy_id=pb.tenancy_id and bi.store_id=pb.store_id and bi.bill_num=pb.bill_num and bi.discount_state='N' and bi.item_property<>'"+SysDictionary.ITEM_PROPERTY_MEALLIST+"') as none_discount_amount");
		posbill.append(" from pos_bill as pb left join hq_service_fee_type as hsft on hsft.id = pb.service_id left join hq_discount_case as hdc on pb.discount_case_id = hdc.id");
		posbill.append(" where pb.store_id=? and pb.bill_num=? and pb.bill_property=?");

		String billProperty = "";
		if ("0".equals(mode))
		{
			billProperty = SysDictionary.BILL_PROPERTY_OPEN;
		}
		else if ("1".equals(mode))
		{
			billProperty = SysDictionary.BILL_PROPERTY_SORT;
		}
		
		List<PosBill> list = this.jdbcTemplate.query(posbill.toString(), new Object[]
		{ organId, billno, billProperty }, BeanPropertyRowMapper.newInstance(PosBill.class));
		
		List<PosBill> posBills = this.findPosBillItem(tenancyid, organId, list);
				
		data.setData(posBills);
		data.setCode(Constant.CODE_SUCCESS);
		data.setMsg(Constant.ORDER_DISH_SUCCESS);
		return data;
	}
	
	public List<PosBill> findPosBillItem(String tenantId, Integer organId, List<PosBill> list) throws SystemException
	{
		try {
			// 账单
			List<PosBill> posBills = new ArrayList<PosBill>();
			
			int listSize = list.size();
			if(listSize == 0){	
				return posBills;
			}

			// 将bill_num字段拼接到sql的in关键字中
			StringBuilder billNumsb = new StringBuilder();
			billNumsb.append("(");
			for(int i = 0; i < listSize; i++){
				billNumsb.append("'");
				billNumsb.append(list.get(i).getBill_num());
				billNumsb.append("'");
				if(listSize - i > 1){ // 最后一位不加逗号
					billNumsb.append(",");
				}
			}
			billNumsb.append(")");
			String billNums = billNumsb.toString();

			// 查询账单明细和做法
			StringBuilder itemMethodSql = new StringBuilder();
			itemMethodSql.append(" select bi.default_state,bi.opt_num,bi.discount_reason_id,bi.discount_mode_id,zfkw.method_id, zfkw.method_name, zfkw.amount, bi.bill_num, bi.item_unit_name, bi.item_serial, bi.rwid, bi.item_id, bi.item_num, bi.item_name, bi.item_remark, bi.item_property, bi.served_state,bi.remark, bi.item_unit_id as unit_id, bi.details_id,bi.sale_mode,bi.returngive_reason_id reason_id,bi.manager_num, ");
			itemMethodSql.append(" round(bi.discount_rate, 2) as discount_rate, round(bi.discount_amount+bi.discountr_amount, 2) as discount_amount, round(bi.item_price, 2) as item_price,round(bi.item_count, 4) as item_count, round(bi.item_amount, 2) as item_amount, round(bi.real_amount, 2) as real_amount,  trim (bi.item_taste) as item_taste, ");
			itemMethodSql.append(" (case when bi.item_remark = '"+SysDictionary.ITEM_REMARK_TC01+"' then 0 else bi.waitcall_tag end) as waitcall_tag,(case when bi.item_remark = '"+SysDictionary.ITEM_REMARK_FS02+"' then '"+SysDictionary.PAYMENT_STATE_PAY_COMPLETE+"' else coalesce(p .payment_state, '03') end ) as payment_state,");
			itemMethodSql.append(" bi.setmeal_id, bi.setmeal_rwid,bi.assist_num,bi.assist_money,bi.print_tag,bi.assist_item_id,bi.item_time,bi.waiter_num as waiter_name, bi.item_mac_id, bi.item_time, bi.assist_num, bi.item_assist_num,bi.method_money, bi.batch_num,bi.activity_id,bi.activity_batch_num,bi.activity_rule_id,bi.activity_count,bi.single_amount,bi.single_discount_rate,bi.combo_prop,bi.origin_item_price,bi.original_table,bi.discount_num ");
			itemMethodSql.append(" from pos_bill_item bi inner join pos_bill b on b.tenancy_id = bi.tenancy_id and b.store_id = bi.store_id and b.bill_num = bi.bill_num");
			itemMethodSql.append(" left join (select tenancy_id,store_id,bill_num,rwid,string_agg(cast(zfkw_id as varchar), '&') method_id, string_agg(zfkw_name, '&') method_name, string_agg(cast(coalesce(amount, 0) as varchar), '&') amount from pos_zfkw_item where type = '"+SysDictionary.ZFKW_TYPE_METHOD+"' group by tenancy_id,store_id,bill_num,rwid) zfkw on zfkw.tenancy_id = bi.tenancy_id and zfkw.store_id = bi.store_id and zfkw.bill_num = bi.bill_num and zfkw.rwid = bi.rwid");
			itemMethodSql.append(" left join (select tenancy_id,store_id,bill_num,batch_num, (case when count (nullif(payment_state, '"+SysDictionary.PAYMENT_STATE_PAY_COMPLETE+"')) > 0 then '"+SysDictionary.PAYMENT_STATE_PAY+"' else '"+SysDictionary.PAYMENT_STATE_PAY_COMPLETE+"' end ) as payment_state from pos_bill_payment group by tenancy_id, store_id, bill_num, batch_num ) p on bi.tenancy_id = p .tenancy_id and bi.store_id = p .store_id and bi.bill_num = p .bill_num and bi.batch_num = p .batch_num");
			itemMethodSql.append(" where bi.bill_num in "+billNums+" and bi.store_id = ? and bi.tenancy_id = ? and (bi.item_remark is null or bi.item_remark <> '"+SysDictionary.ITEM_REMARK_CJ05+"' or b.bill_state='"+SysDictionary.BILL_STATE_CJ01+"')  order by bi.item_serial,bi.item_remark,bi.assist_item_id,bi.rwid ");
			SqlRowSet rs = this.jdbcTemplate.queryForRowSet(itemMethodSql.toString(),new Object[]{organId, tenantId});
			Map<String,List<PosBillItem>> billItemsMap = new HashMap<String, List<PosBillItem>>();
			while (rs.next())
			{
				String billNum = rs.getString("bill_num");
				PosBillItem posBillItemMethod = new PosBillItem();
				posBillItemMethod.setDefault_state(rs.getString("default_state"));
				posBillItemMethod.setOpt_num(rs.getString("opt_num"));
				posBillItemMethod.setDiscount_reason_id(rs.getString("discount_reason_id"));
				posBillItemMethod.setDiscount_mode_id(rs.getString("discount_mode_id"));				
				posBillItemMethod.setBill_num(billNum);
				posBillItemMethod.setItem_unit_name(rs.getString("item_unit_name"));
				posBillItemMethod.setItem_serial(rs.getString("item_serial"));
				posBillItemMethod.setRwid(rs.getString("rwid"));
				posBillItemMethod.setItem_id(rs.getString("item_id"));
				posBillItemMethod.setItem_num(rs.getString("item_num"));
				posBillItemMethod.setItem_name(rs.getString("item_name"));
				posBillItemMethod.setItem_remark(rs.getString("item_remark"));
				posBillItemMethod.setItem_property(rs.getString("item_property"));
				posBillItemMethod.setDiscount_rate(rs.getString("discount_rate"));
				posBillItemMethod.setDiscount_amount(rs.getString("discount_amount"));
				posBillItemMethod.setItem_price(rs.getString("item_price"));
				posBillItemMethod.setServed_state(rs.getString("served_state"));
				posBillItemMethod.setItem_count(rs.getString("item_count"));
				posBillItemMethod.setItem_amount(rs.getString("item_amount"));
				posBillItemMethod.setReal_amount(rs.getString("real_amount"));
				posBillItemMethod.setRemark(rs.getString("remark"));
				posBillItemMethod.setUnit_id(rs.getString("unit_id"));
				posBillItemMethod.setDetails_id(rs.getString("details_id"));
				posBillItemMethod.setItem_taste(rs.getString("item_taste"));
				posBillItemMethod.setSale_mode(rs.getString("sale_mode"));
				posBillItemMethod.setSetmeal_id(rs.getString("setmeal_id"));
				posBillItemMethod.setSetmeal_rwid(rs.getString("setmeal_rwid"));
				posBillItemMethod.setWaitcall_tag(rs.getString("waitcall_tag"));
				posBillItemMethod.setAssist_num(rs.getString("assist_num"));
                posBillItemMethod.setItem_assist_num(rs.getString("item_assist_num"));
				posBillItemMethod.setAssist_money(rs.getString("assist_money"));
				posBillItemMethod.setPrint_tag(rs.getString("print_tag"));
				posBillItemMethod.setAssist_item_id(rs.getString("assist_item_id"));
				posBillItemMethod.setItem_time(rs.getString("item_time"));
				posBillItemMethod.setWaiter_name(rs.getString("waiter_name"));
				posBillItemMethod.setItem_mac_id(rs.getString("item_mac_id"));
				posBillItemMethod.setMethod_money(rs.getString("method_money"));
				posBillItemMethod.setBatch_num(rs.getString("batch_num"));
				posBillItemMethod.setPayment_state(rs.getString("payment_state"));
				posBillItemMethod.setActivity_id(rs.getString("activity_id"));
				posBillItemMethod.setActivity_batch_num(rs.getString("activity_batch_num"));
				posBillItemMethod.setActivity_rule_id(rs.getString("activity_rule_id"));
				posBillItemMethod.setActivity_count(rs.getString("activity_count"));
				posBillItemMethod.setSingle_amount(rs.getString("single_amount"));
				posBillItemMethod.setSingle_discount_rate(rs.getString("single_discount_rate"));
                posBillItemMethod.setOriginal_table(rs.getString("original_table"));
                posBillItemMethod.setDiscount_num(rs.getString("discount_num"));

				// 遍历账单明细的口味做法
				List<PosItemMethod> methodList = new ArrayList<PosItemMethod>();
				String methodId = rs.getString("method_id"); // 口味做法id
				String methodName = rs.getString("method_name"); // 口味做法名称
				if(StringUtils.isNotEmpty(methodId) && StringUtils.isNotEmpty(methodName)){
					String[] methodIds = methodId.split("&");
					String[] methodNames = methodName.split("&");	// 口味做法名称
					String[] amounts = rs.getString("amount").split("&");				// 口味做法价格
					for(int j = 0; j < methodIds.length; j++){
						PosItemMethod method = new PosItemMethod();
						method.setMethod_id(methodIds[j]);
						method.setMethod_name(methodNames[j]);
						method.setAmount(amounts[j]);
						
						methodList.add(method);
					}
				}
				posBillItemMethod.setMethod(methodList);
				
				List<PosBillItem> billItems = null;
				if (billItemsMap.containsKey(billNum))
				{
					billItems = billItemsMap.get(billNum);
				}

				if (null == billItems)
				{
					billItems = new ArrayList<PosBillItem>();
				}
				billItems.add(posBillItemMethod);
				
				billItemsMap.put(billNum, billItems);
			}
//			List<PosBillItemMethod> billItems = this.jdbcTemplate.query(itemMethodSql.toString(), new Object[]{organId, tenantId}, BeanPropertyRowMapper.newInstance(PosBillItemMethod.class));			
			
			// 查询账单付款方式
			StringBuilder payment = new StringBuilder("select id,jzid,name,amount,count,number,phone,shift_id,pos_num,cashier_num,last_updatetime,customer_id,bill_code,remark,currency_amount,batch_num,bill_num from pos_bill_payment where bill_num in "+billNums+" and store_id = ? and tenancy_id=?");
			List<PosBillPaymentWays> paymentWays = this.jdbcTemplate.query(payment.toString(), 
					new Object[]{organId, tenantId}, BeanPropertyRowMapper.newInstance(PosBillPaymentWays.class));
			
			// 账单会员
			StringBuilder bMemberSql = new StringBuilder("select id,type,amount,credit,card_code,mobil,last_updatetime,'' as remark,bill_num from pos_bill_member where bill_num in "+billNums+" and store_id=? and tenancy_id=?");
			List<PosBillMembers> posMember = this.jdbcTemplate.query(bMemberSql.toString(), 
					new Object[]{organId, tenantId}, BeanPropertyRowMapper.newInstance(PosBillMembers.class));
			
			// 开发票
//			StringBuilder queryInvoiceSql = new StringBuilder("select invoice_num,invoice_count,invoice_amount,last_updatetime,remark from pos_bill_invoice where tenancy_id=? and store_id=? and (bill_num=? or copy_bill_num=?) ");
//			StringBuilder queryInvoiceSql = new StringBuilder("select sum(coalesce(invoice_count,0)) as invoice_count,sum(abs(coalesce(invoice_count,0)) * coalesce(invoice_amount,0)) as invoice_amount from pos_bill_invoice where tenancy_id=? and store_id=? and (bill_num=? or copy_bill_num=?) ");
			StringBuilder queryInvoiceSql = new StringBuilder("select bill_num, sum(invoice_count) as invoice_count, sum(invoice_amount) as invoice_amount from (select bill_num,coalesce(invoice_count, 0) as invoice_count,(abs(coalesce(invoice_count, 0)) * coalesce (invoice_amount, 0)) as invoice_amount from pos_bill_invoice where tenancy_id = ? and store_id = ? and bill_num in "+billNums);
			queryInvoiceSql.append(" union all (select copy_bill_num as bill_num,coalesce(invoice_count, 0) as invoice_count,(abs(coalesce(invoice_count, 0)) * coalesce (invoice_amount, 0)) as invoice_amount from pos_bill_invoice where tenancy_id = ? and store_id = ? and copy_bill_num in "+billNums+")) t group by t.bill_num");
			List<JSONObject> invoiceList = this.query4Json(tenantId, queryInvoiceSql.toString(), new Object[]{tenantId, organId, tenantId, organId});
			
			// 查询batch
//			StringBuilder queryBatchSql = new StringBuilder("select id,batch_num,payment_amount,bill_state,bill_property,payment_state from pos_bill_batch where bill_num = ? and tenancy_id=? and store_id=?");
			StringBuilder queryBatchSql = new StringBuilder("select id,batch_num,payment_amount,bill_state,bill_property,payment_state,bill_num from pos_bill_batch where (select coalesce(trim(para_value), '01') as para_value from sys_parameter where para_code = 'ZCDCMS' and valid_state='1') = '02' and bill_num in "+billNums+" and tenancy_id=? and store_id=?");
			List<PosBillBatchs> batchList = this.jdbcTemplate.query(queryBatchSql.toString(), 
					new Object[]{tenantId, organId}, BeanPropertyRowMapper.newInstance(PosBillBatchs.class));

			//查询附加服务费
//			StringBuilder queryService = new StringBuilder(" SELECT service_id, service_type, sum(coalesce(service_count,0)) as service_count, sum(coalesce(service_total,0)) as service_total from pos_bill_service ");
//			queryService.append(" where tenancy_id=? and store_id=? and bill_num=? group by service_id, service_type");
			StringBuilder queryService = new StringBuilder("select bill_num, service_id, service_type, sum (coalesce(service_count, 0)) as service_count, sum (coalesce(service_total, 0)) as service_total");
			queryService.append(" from (SELECT bill_num, service_id, service_type, service_count, service_total from pos_bill_service where tenancy_id =? and store_id =? and bill_num in "+billNums+" ) t group by bill_num, service_id, service_type");
			List<PosBillServices> serviceList = this.jdbcTemplate.query(queryService.toString(), 
					new Object[]{tenantId, organId}, BeanPropertyRowMapper.newInstance(PosBillServices.class)); 
			
			for (int i = 0; i < list.size(); i++)
			{
				PosBill bill = list.get(i);
				String billno = bill.getBill_num();
				List<PosBillItem> billItems = null;
				if (billItemsMap.containsKey(billno))
				{
					billItems = billItemsMap.get(billno);
				}

				if (null == billItems)
				{
					billItems = new ArrayList<PosBillItem>();
				}
				bill.setDetaillist(billItems);

				// 会员信息
				// 在会员信息集合里遍历每一个订单的会员信息，对应盛放
				if(posMember != null && posMember.size() > 0){
					// 反射
					Method[] methods = PosBillMember.class.getDeclaredMethods();
					
					List<PosBillMember> billMembers2 = new ArrayList<PosBillMember>();
					
					for(PosBillMembers member: posMember){
						if(billno.equals(member.getBill_num())){
							PosBillMember member2 = new PosBillMember();
							for(int n = 0; n < methods.length; n++){
								if(methods[n].getName().startsWith("get")){
									// 参数类型
									Class<?> paramType = methods[n].getReturnType();
									// 通过反射取值
									Object value = methods[n].invoke(member, new Object[]{});
									// set方法赋值
									Method setMethod = PosBillMember.class.getMethod("set"+methods[n].getName().substring(3), paramType); 
									setMethod.invoke(member2, value);
								}
							}
							billMembers2.add(member2);
						}
					}
					bill.setMemberlist(billMembers2);
				}

				// 账单付款方式
				// 在账单付款方式集合里遍历每一个订单的账单付款方式，对应盛放
				if(paymentWays != null && paymentWays.size() > 0){
					// 反射
					Method[] methods = PosBillPaymentWay.class.getDeclaredMethods();
					
					List<PosBillPaymentWay> paymentWays2 = new ArrayList<PosBillPaymentWay>();
					for(PosBillPaymentWays paymentWay: paymentWays){
						if(billno.equals(paymentWay.getBill_num())){	// 相等
							PosBillPaymentWay paymentWay2 = new PosBillPaymentWay();
							for(int n = 0; n < methods.length; n++){
								if(methods[n].getName().startsWith("get")){
									// 参数类型
									Class<?> paramType = methods[n].getReturnType();
									// 通过反射取值
									Object value = methods[n].invoke(paymentWay, new Object[]{});
									// set方法赋值
									Method setMethod =PosBillPaymentWay.class.getMethod("set"+methods[n].getName().substring(3), paramType); 
									setMethod.invoke(paymentWay2, value);
								}
							}
							paymentWays2.add(paymentWay2);	
						}
					}
					bill.setPaymentlist(paymentWays2);
				}
				
				// 附加服务费
				// 在附加服务费集合里遍历每一个订单的附加服务费，对应盛放
				if(serviceList != null && serviceList.size() > 0){
					// 反射
					Method[] methods = PosBillService.class.getDeclaredMethods(); 
					
					List<PosBillService> billServices2 = new ArrayList<PosBillService>();
					for(PosBillServices service: serviceList){
						if(billno.equals(service.getBill_num())){
							PosBillService service2 = new PosBillService();
							for(int n = 0; n < methods.length; n++){
								if(methods[n].getName().startsWith("get")){
									// 参数类型
									Class<?> paramType = methods[n].getReturnType();
									// 通过反射取值
									Object value = methods[n].invoke(service, new Object[]{});
									// set方法赋值
									Method setMethod = PosBillService.class.getDeclaredMethod("set"+methods[n].getName().substring(3), paramType);
									setMethod.invoke(service2, value);
								}
							}
							billServices2.add(service2);
						}
					}
					bill.setServicelist(billServices2);
				}
				
				// 开发票
				if (!SysDictionary.BILL_STATE_CJ01.equalsIgnoreCase(bill.getBill_state()))
				{
					// 在开发票集合里遍历每一个订单的发票，对应盛放
					if(invoiceList != null && invoiceList.size() > 0){
						List<JSONObject> jsonObjects = new ArrayList<JSONObject>();
						for(JSONObject invoice: invoiceList){
							if(billno.equals(invoice.optString("bill_num"))){
								invoice.remove("bill_num");		// 移除bill_num,避免返回给客户端
								jsonObjects.add(invoice);
							}
						}
						bill.setInvoicelist(jsonObjects);
					}
				}

				// 查询Batch
				// Batch根据billno进行分类
				if(batchList != null && batchList.size() > 0){
					// 反射
					Method[] methods = PosBillBatch.class.getDeclaredMethods();
					
					List<PosBillBatch> billBatch2 = new ArrayList<PosBillBatch>();
					for(PosBillBatchs batch: batchList){
						if(billno.equals(batch.getBill_num())){
							PosBillBatch batch2 = new PosBillBatch();
							for(int n = 0; n < methods.length; n++){
								if(methods[n].getName().startsWith("get")){
									// 参数类型
									Class<?> paramType = methods[n].getReturnType();
									// 通过反射取值
									Object value = methods[n].invoke(batch, new Object[]{});
									// set方法赋值
									Method setMethod = PosBillBatch.class.getDeclaredMethod("set"+methods[n].getName().substring(3), paramType);
									setMethod.invoke(batch2, value);
								}
							}
							billBatch2.add(batch2);
						}
					}
					bill.setBatchList(billBatch2);
				}

				posBills.add(bill);
			}
			
			return posBills;
		} catch (Exception e1) {
			throw SystemException.getInstance(PosErrorCode.OPER_ERROR);
		}
	}
	
	@Override
	public int insertPosBillPayment(String tenancyId, int storeId, PosBillPayment paymentEntity) throws Exception
	{
		StringBuilder insertPaymentSql = new StringBuilder(
				"insert into pos_bill_payment(tenancy_id,store_id,bill_num,table_code,type,jzid,name,name_english,amount,count,number,phone,report_date,shift_id,pos_num,cashier_num,last_updatetime,is_ysk,rate,currency_amount,upload_tag,customer_id,bill_code,remark,payment_state,param_cach,batch_num,more_coupon,fee,fee_rate,coupon_type,yjzid,coupon_buy_price,due,tenancy_assume,third_assume,third_fee,payment_uid) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
		this.update(
				insertPaymentSql.toString(),
				new Object[]
				{ paymentEntity.getTenancy_id(), paymentEntity.getStore_id(), paymentEntity.getBill_num(), paymentEntity.getTable_code(), paymentEntity.getType(), paymentEntity.getJzid(), paymentEntity.getName(), paymentEntity.getName_english(), paymentEntity.getAmount(), paymentEntity.getCount(),
						paymentEntity.getNumber(), paymentEntity.getPhone(), paymentEntity.getReport_date(), paymentEntity.getShift_id(), paymentEntity.getPos_num(), paymentEntity.getCashier_num(), paymentEntity.getLast_updatetime(), paymentEntity.getIs_ysk(), paymentEntity.getRate(),
						paymentEntity.getCurrency_amount(), paymentEntity.getUpload_tag(), paymentEntity.getCustomer_id(), paymentEntity.getBill_code(), paymentEntity.getRemark(), paymentEntity.getPayment_state(), paymentEntity.getParam_cach(), paymentEntity.getBatch_num(),
						paymentEntity.getMore_coupon(), paymentEntity.getFee(), paymentEntity.getFee_rate(), paymentEntity.getCoupon_type(), paymentEntity.getYjzid(), paymentEntity.getCoupon_buy_price(), paymentEntity.getDue(), paymentEntity.getTenancy_assume(), paymentEntity.getThird_assume(),
						paymentEntity.getThird_fee(), paymentEntity.getPayment_uid() });

		StringBuilder rwidSql = new StringBuilder("select currval('pos_bill_payment_id_seq'::regclass) ");
		return this.queryForInt(rwidSql.toString(), new Object[] {});
	}
	
	@Override
	public int insertPosBillPayment(String tenancyId, int storeId, String billNum, Date reportDate, int shiftId, String posNum, String optNum, String tableCode, String type, int jzid, String name, String nameEnglish, double rate, double amount, double count, double currencyAmount, String number,
			String phone, String isYsk, Integer customerId, String billCode, String remark, Timestamp lastUpdatetime, String paymentState, String batchNum, String paramCach, double moreCoupon) throws Exception
	{
		PosBillPayment paymentEntity = new PosBillPayment(tenancyId, storeId, reportDate, shiftId, posNum, optNum, billNum, batchNum, tableCode, type, jzid, name, nameEnglish, amount, currencyAmount, Double.valueOf(count).intValue(), number, phone, lastUpdatetime, paymentState);
		paymentEntity.setRate(rate);
		paymentEntity.setIs_ysk(isYsk);
		paymentEntity.setCustomer_id(customerId);
		paymentEntity.setBill_code(billCode);
		paymentEntity.setRemark(remark);
		paymentEntity.setParam_cach(paramCach);
		paymentEntity.setMore_coupon(moreCoupon);
		paymentEntity.setCoupon_buy_price(currencyAmount);
		paymentEntity.setDue(currencyAmount);
		return this.insertPosBillPayment(tenancyId, storeId, paymentEntity);
	}
}
