package com.tzx.pos.bo.payment.imp.ext;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import com.tzx.base.entity.PosBillMember;
import com.tzx.base.entity.PosBillPayment;
import com.tzx.base.entity.PosBillPaymentCoupons;
import com.tzx.base.entity.PosThirdPaymentRefundEntity;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.CrmErrorCode;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.util.DoubleHelper;
import com.tzx.framework.common.util.Md5Utils;
import com.tzx.framework.common.util.PropertiesLoader;
import com.tzx.framework.common.util.Tools;
import com.tzx.member.crm.bo.CustomerCouponService;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.bo.payment.PaymentWayService;
import com.tzx.pos.bo.payment.imp.PaymentWayServiceImp;
import com.tzx.pos.po.springjdbc.dao.NocodeCouponsDao;
import com.tzx.pos.po.springjdbc.dao.PosBillMemberDao;
import com.tzx.thirdpay.bo.ThirdPaymentRefundService;

/**
 * 优惠券
 *
 */
@Service("com.tzx.pos.bo.payment.imp.ext.CouponsPaymentWayServiceImp")
public class CouponsPaymentWayServiceImp extends PaymentWayServiceImp implements PaymentWayService
{
	
	public static final Integer	COUPON_TYPE_CUSTOMER	= 1;
	public static final Integer	COUPON_TYPE_NOCODE	= 2;
	
	@Resource(name = CustomerCouponService.NAME)
	private CustomerCouponService	customerService;

	@Resource(name = PosBillMemberDao.NAME)
	private PosBillMemberDao			memberDao;
	
	@Resource(name = NocodeCouponsDao.NAME)
	private NocodeCouponsDao	couponsDao;
	
	@Resource(name = ThirdPaymentRefundService.NAME)
	private ThirdPaymentRefundService		thirdPaymentRefundService;
	
	@Override
	public void payment(String tenantId, int storeId, String billNum, String batchNum,Date reportDate, int shiftId, String posNum, String optNum, String tableCode, JSONObject payment, Timestamp currentTime,String isOnlinePay) throws Exception
	{
//		// TODO Auto-generated method stub
////		String chanel = payment.optString("chanel");
//
//		double amount = payment.optDouble("amount");
//		double count = payment.optDouble("count");
//		double currencyAmount = payment.optDouble("currency_amount");
//		String number = payment.optString("number");
//		String phone = payment.optString("phone");
//		String billCode = payment.optString("bill_code");
//
//		int jzid = payment.optInt("jzid");
//		String paymentClass = payment.optString("payment_class");
//		String paymentName = payment.optString("payment_name");
//		String nameEnglish = payment.optString("payment_english_name");
//		double rate = payment.optDouble("rate");
//		String remark = payment.optString("remark");
//		String isYsk = payment.optString("is_ysk");
//		if (Tools.isNullOrEmpty(isYsk))
//		{
//			isYsk = "N";
//		}
//		
//		String mobil = ParamUtil.getStringValueByObject(payment, "mobil");
//		String customerCode = ParamUtil.getStringValueByObject(payment, "customer_code");
//		String customerName = ParamUtil.getStringValueByObject(payment, "customer_name");
//		String cardCode = ParamUtil.getStringValueByObject(payment, "card_code");
//		Double customerCredit = ParamUtil.getDoubleValueByObject(payment, "customer_credit");
//		Double mainBalance = ParamUtil.getDoubleValueByObject(payment, "main_balance");
//		Double rewardBalance = ParamUtil.getDoubleValueByObject(payment, "reward_balance");
//
//		boolean isCheck = payment.optBoolean("is_check");
//
//		if ("1".equalsIgnoreCase(isOnlinePay))
//		{
//			paymentDao.insertPosBillPayment(tenantId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, nameEnglish, rate, amount, count, currencyAmount, number, phone, isYsk, null, billCode, remark, currentTime,
//					SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);
//
//			paymentDao.insertPosBillPaymentLog(tenantId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, nameEnglish, rate, amount, count, currencyAmount, number, phone, isYsk, null, billCode, remark, currentTime,
//					SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);
//
//		}
//		else
//		{
//			if (isCheck || Tools.hv(number)){
//				//判断是否无码卷
//				if(StringUtils.isEmpty(number)){
//					//String classId = payment.getString("class_id");//无码优惠卷大类
//					paymentDao.insertPosBillPaymentNocode(tenantId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, nameEnglish, rate, amount, count, currencyAmount, number, phone, isYsk, null, billCode, remark, currentTime,SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d,2);
//					paymentDao.insertPosBillPaymentLogNocode(tenantId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, nameEnglish, rate, amount, count, currencyAmount, number, phone, isYsk, null, billCode, remark, currentTime,SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d,2);
//				}else{
//					//有码卷
//					
//					PosBillMember billMember = new PosBillMember();
//					billMember.setBill_num(billNum);
//					billMember.setReport_date(reportDate);
//					billMember.setType(SysDictionary.BILL_MEMBERCARD_YHJ04);
//					billMember.setCard_code(cardCode);
//					billMember.setMobil(mobil);
//					billMember.setCustomer_code(customerCode);
//					billMember.setCustomer_name(customerName);
//					billMember.setAmount(currencyAmount);
//					billMember.setCredit(0d);
//					billMember.setBill_code(number);
//					billMember.setConsume_after_credit(customerCredit);
//					billMember.setConsume_before_credit(customerCredit);
//					billMember.setConsume_after_main_balance(mainBalance);
//					billMember.setConsume_before_main_balance(mainBalance);
//					billMember.setConsume_after_reward_balance(rewardBalance);
//					billMember.setConsume_before_reward_balance(rewardBalance);
//					billMember.setLast_updatetime(currentTime);
//					memberDao.insertPosBillMember(tenantId, storeId, billMember);
//					
//					payment.put("pay_state", SysDictionary.PAYMENT_STATE_NOTPAY);
//					paymentDao.insertPosBillPaymentNocode(tenantId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, nameEnglish, rate, amount, count, currencyAmount, number, phone, isYsk, null, billCode, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY,
//							batchNum, payment.toString(), 0d,1);
//				}
//			}
//			else
//			{
//				paymentDao.insertPosBillPayment(tenantId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, nameEnglish, rate, amount, count, currencyAmount, number, phone, isYsk, null, billCode, remark, currentTime,
//						SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);
//
//				paymentDao.insertPosBillPaymentLog(tenantId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, nameEnglish, rate, amount, count, currencyAmount, number, phone, isYsk, null, billCode, remark, currentTime,
//						SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);
//			}
//		}

		String number = payment.optString("number");
		String isCheck = payment.optString("is_check");

		if ("1".equalsIgnoreCase(isOnlinePay))
		{
			this.paymentForOnlinePay(tenantId, storeId, billNum, batchNum, reportDate, shiftId, posNum, optNum, tableCode, payment, currentTime, isOnlinePay);
		}
		else if ("1".equals(isCheck) && Tools.hv(number))
		{
			// 有码卷
			this.paymentForCode(tenantId, storeId, billNum, batchNum, reportDate, shiftId, posNum, optNum, tableCode, payment, currentTime, isOnlinePay);
		}
		else
		{
			// 判断是否无码卷
			this.paymentForNoCode(tenantId, storeId, billNum, batchNum, reportDate, shiftId, posNum, optNum, tableCode, payment, currentTime, isOnlinePay);
		}

	}
	
	/**
	 * 线上优惠券支付
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @param batchNum
	 * @param reportDate
	 * @param shiftId
	 * @param posNum
	 * @param optNum
	 * @param tableCode
	 * @param payment
	 * @param currentTime
	 * @param isOnlinePay
	 * @throws Exception
	 */
	public void paymentForOnlinePay(String tenantId, int storeId, String billNum, String batchNum,Date reportDate, int shiftId, String posNum, String optNum, String tableCode, JSONObject payment, Timestamp currentTime,String isOnlinePay) throws Exception
	{
		double amount = payment.optDouble("amount");
		double count = payment.optDouble("count");
		double currencyAmount = payment.optDouble("currency_amount");
		String number = payment.optString("number");
		String phone = payment.optString("phone");
		String billCode = payment.optString("bill_code");

		int jzid = payment.optInt("jzid");
		String paymentClass = payment.optString("payment_class");
		String paymentName = payment.optString("payment_name");
		String nameEnglish = payment.optString("payment_english_name");
		double rate = payment.optDouble("rate");
		String remark = payment.optString("remark");
		String isYsk = payment.optString("is_ysk");
		if (Tools.isNullOrEmpty(isYsk))
		{
			isYsk = "N";
		}

		paymentDao.insertPosBillPayment(tenantId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, nameEnglish, rate, amount, count, currencyAmount, number, phone, isYsk, null, billCode, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE,
				batchNum, null, 0d);

//		paymentDao.insertPosBillPaymentLog(tenantId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, nameEnglish, rate, amount, count, currencyAmount, number, phone, isYsk, null, billCode, remark, currentTime,
//				SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);
	}
	
	/**会员券支付
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @param batchNum
	 * @param reportDate
	 * @param shiftId
	 * @param posNum
	 * @param optNum
	 * @param tableCode
	 * @param payment
	 * @param currentTime
	 * @param isOnlinePay
	 * @throws Exception
	 */
	public void paymentForCode(String tenantId, int storeId, String billNum, String batchNum,Date reportDate, int shiftId, String posNum, String optNum, String tableCode, JSONObject payment, Timestamp currentTime,String isOnlinePay) throws Exception
	{
		// 有码卷
		Double amount = payment.optDouble("amount");
		Integer count = payment.optInt("count");
		Double currencyAmount = payment.optDouble("currency_amount");
		String number = payment.optString("number");
		String phone = payment.optString("phone");
		Integer jzid = payment.optInt("jzid");
		String paymentClass = payment.optString("payment_class");
		String paymentName = payment.optString("payment_name");
		String nameEnglish = payment.optString("payment_english_name");
		String remark = payment.optString("remark");
		String isYsk = payment.optString("is_ysk");
		if (Tools.isNullOrEmpty(isYsk))
		{
			isYsk = "N";
		}
		String chanel = ParamUtil.getStringValueByObject(payment, "chanel");
		String mobil = ParamUtil.getStringValueByObject(payment, "mobil");
		String customerCode = ParamUtil.getStringValueByObject(payment, "customer_code");
		String customerName = ParamUtil.getStringValueByObject(payment, "customer_name");
		String cardCode = ParamUtil.getStringValueByObject(payment, "card_code");
		Double customerCredit = ParamUtil.getDoubleValueByObject(payment, "customer_credit");
		Double mainBalance = ParamUtil.getDoubleValueByObject(payment, "main_balance");
		Double rewardBalance = ParamUtil.getDoubleValueByObject(payment, "reward_balance");
		String couponsPro = ParamUtil.getStringValueByObject(payment, "coupons_pro");
		String className = ParamUtil.getStringValueByObject(payment, "coupons_class_name");
		Integer classId = ParamUtil.getIntegerValueByObject(payment, "coupons_class_id");
		Integer typeId = ParamUtil.getIntegerValueByObject(payment, "coupons_type_id");
		Double couponsAmount = ParamUtil.getDoubleValueByObject(payment, "currency_amount");
		if(payment.containsKey("coupons_amount"))
		{
			couponsAmount = ParamUtil.getDoubleValueByObject(payment, "coupons_amount");
		}
		Double incomeAmount = ParamUtil.getDoubleValueByObject(payment, "income_amount");

		JSONObject paramCachJson = new JSONObject();
		paramCachJson.put("pay_state", SysDictionary.PAYMENT_STATE_NOTPAY);
		paramCachJson.put("mobil", mobil);
		paramCachJson.put("customer_code", customerCode);
		paramCachJson.put("customer_name", customerName);
		paramCachJson.put("card_code", cardCode);
		paramCachJson.put("customer_credit", customerCredit);
		paramCachJson.put("main_balance", mainBalance);
		paramCachJson.put("reward_balance", rewardBalance);
		paramCachJson.put("chanel", chanel);

		String paymentUid = Md5Utils.md5(billNum + "_" + number + "_" + String.valueOf(currentTime.getTime()));

		if (Tools.hv(mobil) || Tools.hv(cardCode))
		{
			// 插入会员操作记录
			PosBillMember billMember = new PosBillMember(tenantId, storeId, billNum, reportDate, SysDictionary.BILL_MEMBERCARD_YHJ04, currencyAmount, 0d, cardCode, mobil, currentTime, null, customerCode, customerName, customerCredit, customerCredit, mainBalance, rewardBalance, mainBalance,
					rewardBalance);
			memberDao.insertPosBillMember(tenantId, storeId, billMember);
		}
		
		//插入账单优惠券记录
		PosBillPaymentCoupons paymentCoupon = new PosBillPaymentCoupons(tenantId, storeId, billNum, reportDate, paymentUid, number, currencyAmount, className, currentTime, null, classId, typeId, couponsAmount, count.doubleValue(), chanel, couponsPro, COUPON_TYPE_CUSTOMER);
		JSONArray rwids = payment.optJSONArray("item_rwids");
		Double moreCoupon = 0d;
		if (SysDictionary.COUPONS_PRO_DISH.equals(couponsPro) && null != rwids && rwids.size() > 0)
		{
			Integer rwid = rwids.optInt(0);
			JSONObject itemJson = paymentDao.getBillItemForCouponByRwid(tenantId, storeId, billNum, rwid);
			if (null != itemJson && false == itemJson.isEmpty())
			{
				paymentCoupon.setPrice(itemJson.optDouble("item_price", 0d));
				paymentCoupon.setItem_id(itemJson.optInt("item_id"));
				paymentCoupon.setItem_num(itemJson.optInt("item_count"));
				paymentCoupon.setItem_unit_id(itemJson.optInt("item_unit_id"));
				paymentCoupon.setRwid(rwid);
			}
			moreCoupon = DoubleHelper.sub(currencyAmount, couponsAmount, 4);
		}
		else 
		{
			paymentCoupon.setDiscount_money(currencyAmount);
		}
		
		paymentCoupon.setCoupon_buy_price(incomeAmount);
		paymentCoupon.setDue(incomeAmount);
		paymentCoupon.setTenancy_assume(DoubleHelper.sub(currencyAmount, incomeAmount, 4));
		paymentDao.insertPosBillPaymentCoupons(tenantId, storeId, paymentCoupon);

		//插入支付记录
		PosBillPayment paymentEntity = new PosBillPayment(tenantId, storeId, reportDate, shiftId, posNum, optNum, billNum, batchNum, tableCode, paymentClass, jzid, paymentName, nameEnglish, amount, currencyAmount, count, number, phone, currentTime, SysDictionary.PAYMENT_STATE_PAY);
		paymentEntity.setRemark(remark);
		paymentEntity.setParam_cach(paramCachJson.toString());
		paymentEntity.setMore_coupon(moreCoupon);
		paymentEntity.setCoupon_type(COUPON_TYPE_CUSTOMER);
		paymentEntity.setCoupon_buy_price(incomeAmount);
		paymentEntity.setDue(incomeAmount);
		paymentEntity.setTenancy_assume(DoubleHelper.sub(currencyAmount, incomeAmount, 4));
		paymentEntity.setPayment_uid(paymentUid);
		paymentDao.insertPosBillPayment(tenantId, storeId, paymentEntity);
	}
	
	/**无码券支付
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @param batchNum
	 * @param reportDate
	 * @param shiftId
	 * @param posNum
	 * @param optNum
	 * @param tableCode
	 * @param payment
	 * @param currentTime
	 * @param isOnlinePay
	 * @throws Exception
	 */
	public void paymentForNoCode(String tenantId, int storeId, String billNum, String batchNum,Date reportDate, int shiftId, String posNum, String optNum, String tableCode, JSONObject payment, Timestamp currentTime,String isOnlinePay) throws Exception
	{
		final int scale = 4;
		
		Double amount = payment.optDouble("amount");
		Double couponCount = payment.optDouble("count");
		Double currencyAmount = payment.optDouble("currency_amount");
		String number = payment.optString("number");
		String phone = payment.optString("phone");
//		String billCode = payment.optString("bill_code");

		Integer jzid = payment.optInt("jzid");
		String paymentClass = payment.optString("payment_class");
		String paymentName = payment.optString("payment_name");
		String nameEnglish = payment.optString("payment_english_name");
//		Double rate = payment.optDouble("rate");
		String remark = payment.optString("remark");
		String isYsk = payment.optString("is_ysk");
		if (Tools.isNullOrEmpty(isYsk))
		{
			isYsk = "N";
		}
		
		String chanel = ParamUtil.getStringValueByObject(payment, "chanel");
		String couponsPro = ParamUtil.getStringValueByObject(payment, "coupons_pro");
		String className = ParamUtil.getStringValueByObject(payment, "coupons_class_name");
		Integer classId = ParamUtil.getIntegerValueByObject(payment, "coupons_class_id");
		Integer typeId = ParamUtil.getIntegerValueByObject(payment, "coupons_type_id");
		Double couponsAmount = ParamUtil.getDoubleValueByObject(payment, "coupons_amount");
		Double incomeAmount = ParamUtil.getDoubleValueByObject(payment, "income_amount");
		Double incomeAmountTotal = DoubleHelper.mul(incomeAmount, couponCount, scale);
		
		String paymentUid = Md5Utils.md5(billNum + "_" + payment.toString() + "_" + String.valueOf(currentTime.getTime()));
		
		Double moreCoupon=0d;
		if (SysDictionary.COUPONS_PRO_DISH.equals(couponsPro) && payment.containsKey("item_rwids") && payment.optJSONArray("item_rwids").size() > 0)
		{
			List<String> couponTypeIdList = new ArrayList<String>();
			couponTypeIdList.add(String.valueOf(typeId));
			List<JSONObject> couponTypeList = couponsDao.getCrmCouponsType(tenantId, storeId, chanel,couponTypeIdList );
			String isTotal = "1";
			Double faceValue = 0d;
			if(null!=couponTypeList && couponTypeList.size()>0)
			{
				isTotal = couponTypeList.get(0).optString("is_total");
				faceValue = couponTypeList.get(0).optDouble("face_value");
			}
			
			List<PosBillPaymentCoupons> paymentCouponList = new ArrayList<PosBillPaymentCoupons>();
			PosBillPaymentCoupons paymentCoupon = null;
			List<JSONObject> itemList = paymentDao.getBillItemForCouponByRwid(tenantId, storeId, billNum, payment.optJSONArray("item_rwids"));
			if (null != itemList)
			{
				for (JSONObject itemJson : itemList)
				{
					Double itemCount = ParamUtil.getDoubleValueByObject(itemJson, "item_count");
					Double discountNum = ParamUtil.getDoubleValueByObject(itemJson, "discount_num");
					itemCount = DoubleHelper.sub(itemCount, discountNum, scale);

					Double count = itemCount;
					if (0 < itemCount && couponCount <= itemCount)
					{
						count = couponCount;
					}

					if (0 < count)
					{
						couponCount = DoubleHelper.sub(couponCount, count, scale);
						itemCount = DoubleHelper.sub(itemCount, count, scale);

						Double realPrice = DoubleHelper.div(itemJson.optDouble("real_amount"), itemJson.optDouble("item_count"), 2);
						Double itemPrice = DoubleHelper.div(itemJson.optDouble("item_amount"), itemJson.optDouble("item_count"), 2);
						Double discountMoney = 0d;
						Double couponMoney = 0d;
						if ("2".equals(isTotal))
						{
							if (faceValue <= realPrice)
							{
								discountMoney = DoubleHelper.mul(faceValue, count, scale);
							}
							else
							{
								discountMoney = DoubleHelper.mul(realPrice, count, scale);
								if (0 == itemCount)
								{
									discountMoney = DoubleHelper.add(discountMoney, DoubleHelper.sub(itemJson.optDouble("real_amount"), DoubleHelper.mul(realPrice, itemJson.optDouble("item_count"), scale), scale), scale);
								}
							}
							couponMoney = DoubleHelper.mul(faceValue, count, scale);
						}
						else
						{
							faceValue = itemPrice;
							discountMoney = DoubleHelper.mul(realPrice, count, scale);
							couponMoney = DoubleHelper.mul(itemPrice, count, scale);

							if (0 == itemCount)
							{
								discountMoney = DoubleHelper.add(discountMoney, DoubleHelper.sub(itemJson.optDouble("real_amount"), DoubleHelper.mul(realPrice, itemJson.optDouble("item_count"), scale), scale), scale);
							}
						}

						incomeAmountTotal = DoubleHelper.mul(incomeAmount, count, scale);

						paymentCoupon = new PosBillPaymentCoupons(tenantId, storeId, billNum, reportDate, paymentUid, null, faceValue, className, currentTime, null, classId, typeId, discountMoney, count, chanel, couponsPro, COUPON_TYPE_NOCODE);
						paymentCoupon.setPrice(itemJson.optDouble("item_price", 0d));
						paymentCoupon.setItem_id(itemJson.optInt("item_id"));
						paymentCoupon.setItem_num(itemJson.optInt("item_count"));
						paymentCoupon.setItem_unit_id(itemJson.optInt("item_unit_id"));
						paymentCoupon.setRwid(itemJson.optInt("rwid"));
						paymentCoupon.setCoupon_buy_price(incomeAmountTotal);
						paymentCoupon.setDue(incomeAmountTotal);
						paymentCoupon.setTenancy_assume(DoubleHelper.sub(couponMoney, incomeAmountTotal, scale));
						paymentCouponList.add(paymentCoupon);
					}

					// 优惠券抵扣完,退出循环
					if (0 == couponCount)
					{
						break;
					}
				}
			}
			paymentDao.insertPosBillPaymentCoupons(tenantId, storeId, paymentCouponList);
			
			moreCoupon = DoubleHelper.sub(currencyAmount, couponsAmount, scale);
		}
		else
		{
			PosBillPaymentCoupons paymentCoupon = new PosBillPaymentCoupons(tenantId, storeId, billNum, reportDate, paymentUid, null, DoubleHelper.div(currencyAmount, couponCount, scale), className, currentTime, null, classId, typeId, couponsAmount, couponCount, chanel, couponsPro, COUPON_TYPE_NOCODE);
			paymentCoupon.setCoupon_buy_price(incomeAmountTotal);
			paymentCoupon.setDue(incomeAmountTotal);
			paymentCoupon.setTenancy_assume(DoubleHelper.sub(currencyAmount, incomeAmountTotal, scale));
			paymentDao.insertPosBillPaymentCoupons(tenantId, storeId, paymentCoupon);
		}
		
		PosBillPayment paymentEntity = new PosBillPayment(tenantId, storeId, reportDate, shiftId, posNum, optNum, billNum, batchNum, tableCode, paymentClass, jzid, paymentName, nameEnglish, amount, currencyAmount, couponCount.intValue(), number, phone, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
		paymentEntity.setRemark(remark);
		paymentEntity.setMore_coupon(moreCoupon);
		paymentEntity.setCoupon_type(COUPON_TYPE_NOCODE);
		paymentEntity.setCoupon_buy_price(incomeAmountTotal);
		paymentEntity.setDue(incomeAmountTotal);
		paymentEntity.setTenancy_assume(DoubleHelper.sub(currencyAmount, incomeAmountTotal, scale));
		paymentEntity.setPayment_uid(paymentUid);
		paymentEntity.setCount(payment.optInt("count"));
		paymentDao.insertPosBillPayment(tenantId, storeId, paymentEntity);
	}

	@Override
	public void paymentDebit(String tenancyId, int storeId, String billNum,String orderNum,String chanel, Double paymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData) throws Exception
	{
		// TODO Auto-generated method stub
		String currentTimeStr = DateUtil.format(currentTime);

		String optNum = payment.optString("cashier_num");
		String optName = paymentDao.getEmpNameById(optNum, tenancyId, storeId);

		JSONObject paramCachJson = JSONObject.fromObject(payment.optString("param_cach"));

		int payId = payment.optInt("id");
		String isCheck = payment.optString("is_check");
		String number = payment.optString("number");

		if ("1".equals(isCheck) || !StringUtils.isEmpty(number))
		{
			JSONObject coupons = new JSONObject();
			coupons.put("coupons_code", number);

			List<JSONObject> couponsList = new ArrayList<JSONObject>();
			couponsList.add(coupons);

			JSONObject requestJson = new JSONObject();
			requestJson.put("couponslist", couponsList);
			requestJson.put("chanel", paramCachJson.optString("chanel"));
			requestJson.put("bill_money", paymentAmount);
			if((!SysDictionary.CHANEL_MD01.equals(chanel))&&Tools.hv(orderNum))
			{
				requestJson.put("bill_code", orderNum);
			}
			else
			{
				requestJson.put("bill_code", billNum);
				requestJson.put("batch_num", payment.optString("batch_num"));
			}
			requestJson.put("updatetime", currentTimeStr);
			requestJson.put("business_date", payment.optString("report_date"));// GJ20160408
			requestJson.put("shift_id", payment.optInt("shift_id"));
			requestJson.put("operator_id", optNum);
			requestJson.put("operator", optName);
			
			String billDetailsSql = " select a.item_id,a.item_unit_id,a.item_price,a.item_count,a.real_amount,a.item_property,a.discount_rate,a.discountr_amount "
					+ " from pos_bill_item a,pos_bill b where a.tenancy_id=? and a.store_id=? and a.bill_num=? and a.item_property=? and a.bill_num = b.bill_num and b.bill_property=?  and (a.item_remark is null or a.item_remark='') and a.real_amount>0 ";
			List<JSONObject> billDetailsList = paymentDao.query4Json(tenancyId, billDetailsSql, new Object[]
			{ tenancyId, storeId, billNum, SysDictionary.ITEM_PROPERTY_SINGLE, SysDictionary.BILL_PROPERTY_OPEN });
			requestJson.put("billdetails", billDetailsList);

//			List<JSONObject> requestList = new ArrayList<JSONObject>();
//			requestList.add(requestJson);
//
//			Data requestData = Data.get(tenancyId, storeId, 0);
//
//			requestData.setType(Type.NEW_COUPONS);
//			requestData.setOper(Oper.update);
//			requestData.setData(requestList);
//
//			Data responseData = Data.get(requestData);
//			customerService.commonPost(JSONObject.fromObject(requestData).toString(), responseData);
			
			Data responseData = Data.get();
			customerService.customerCouponConsume(tenancyId, storeId, requestJson, responseData, Type.BILL_PAYMENT.name());

			if (Constant.CODE_SUCCESS == responseData.getCode())
			{
				JSONObject paymentJson = new JSONObject();
				paymentJson.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
				paymentJson.put("id", payId);
				paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", paymentJson);
				
				payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
				resultData.setCode(Constant.CODE_SUCCESS);
			}
			else if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
			{
				payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY);
				resultData.setCode(Constant.CODE_CONN_EXCEPTION);
				resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
			}
			else if (Constant.PAYMENT_STATUS_PAYING_CODE == responseData.getCode())
			{
				resultData.setCode(Constant.PAYMENT_STATUS_PAYING_CODE);
				resultData.setMsg(PropertiesLoader.getProperty(String.valueOf(responseData.getCode())));
			}
			else
			{
				paymentDao.deletePosBillPaymentByID(tenancyId, storeId, billNum, payId);

				Date reportDate = DateUtil.parseDate(payment.optString("report_date"));
				Integer shiftId = payment.optInt("shift_id");
				String posNum = payment.optString("pos_num");

				int jzid = payment.optInt("jzid");
				String table_code = payment.optString("table_code");
				String paymentClass = payment.optString("type");
				String paymentName = payment.optString("name");
				String paymentEnglishName = payment.optString("name_english");
				double exchangeRate = payment.optDouble("rate");
				double pay_amount = payment.optDouble("amount");
				double pay_count = payment.optDouble("count");
				double currency_amount = payment.optDouble("currency_amount");
				String phone = payment.optString("phone");
				int customer_id = payment.optInt("customer_id");
				String remark = payment.optString("remark");
				String batch_num = payment.optString("batch_num");
				String is_ysk = Tools.hv(payment.optString("is_ysk")) ? payment.optString("is_ysk") : "N";
				paymentDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, table_code, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, customer_id, null, remark, currentTime,
						SysDictionary.PAYMENT_STATE_PAY_FAILURE, batch_num, payment.optString("param_cach"), 0d);

				memberDao.deletePosBillMember(tenancyId, storeId, billNum, SysDictionary.BILL_MEMBERCARD_YHJ04);

				payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_FAILURE);
				resultData.setCode(PosErrorCode.BILL_PAYMENT_ERROP.getNumber());
				resultData.setMsg(PosErrorCode.BILL_PAYMENT_ERROP.getMessage().replace("{0}", responseData.getMsg()));
			}
		}
	}

	@Override
	public void findPaymentDebit(String tenancyId, int storeId, String billNum,String orderNum,String chanel,Double paymentAmount,JSONObject payment,Timestamp currentTime,JSONObject resultJson,Data resultData) throws Exception
	{
		// TODO Auto-generated method stub
		double pay_amount = payment.getDouble("amount");
		String number = payment.getString("number");
		
//		StringBuilder billAmountSql = new StringBuilder("select b.source from pos_bill b where b.bill_num = ? and b.store_id = ?");
//		SqlRowSet rsb = paymentDao.query4SqlRowSet(billAmountSql.toString(), new Object[]
//		{ billNum, storeId });
//		String chanel = SysDictionary.CHANEL_MD01;
//		if (rsb.next())
//		{
//			chanel = rsb.getString("source");
//		}

		JSONObject coupons = new JSONObject();
		coupons.put("coupons_code", number);

		List<JSONObject> couponsList = new ArrayList<JSONObject>();
		couponsList.add(coupons);

		JSONObject paraJson = new JSONObject();
		paraJson.put("chanel", chanel);
		paraJson.put("bill_money", pay_amount);
		paraJson.put("couponslist", couponsList);

//		List<JSONObject> requestList = new ArrayList<JSONObject>();
//		requestList.add(paraJson);
//
//		Data requestData = Data.get(tenancyId, storeId, 0);
//		requestData.setType(Type.COUPONS);
//		requestData.setOper(Oper.check);
//		requestData.setData(requestList);
//
//		Data responseData = Data.get(requestData);
//		customerService.commonPost(JSONObject.fromObject(requestData).toString(), responseData);
		
		Data responseData = customerService.queryCustomerCouponConsume(tenancyId, storeId, paraJson);
		
		if (CrmErrorCode.COUPONS_CODE_USED.getNumber() == responseData.getCode())
		{
			payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_COMPLETE);

			JSONObject updatePaymentJson = new JSONObject();
			updatePaymentJson.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
			updatePaymentJson.put("id", payment.optInt("id"));
			paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", updatePaymentJson);

			resultData.setCode(Constant.CODE_SUCCESS);
		}
		else if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
		{
			payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY);
			resultData.setCode(Constant.CODE_CONN_EXCEPTION);
			resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
		}
		else
		{
			if (Constant.CODE_SUCCESS == responseData.getCode())
			{
				payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_FAILURE);
				
				resultData.setCode(PosErrorCode.BILL_PAYMENT_ERROP.getNumber());
				resultData.setMsg(PosErrorCode.BILL_PAYMENT_ERROP.getMessage().replace("{0}", "优惠劵使用失败"));
			}
			else
			{
				payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_FAILURE);
				
				resultData.setCode(PosErrorCode.BILL_PAYMENT_ERROP.getNumber());
				resultData.setMsg(PosErrorCode.BILL_PAYMENT_ERROP.getMessage().replace("{0}", responseData.getMsg()));
			}

			JSONObject updatePaymentJson = new JSONObject();
			updatePaymentJson.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_FAILURE);
			updatePaymentJson.put("id", payment.optInt("id"));
			paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", updatePaymentJson);
		}
	}

	@Override
	public void paymentRefund(String tenancyId, int storeId, String billNum,String oldBillNum,String orderNum,String chanel,Double billPaymentAmount,JSONObject payment,Timestamp currentTime,JSONObject resultJson,Data resultData,String operType) throws Exception
	{
		// TODO Auto-generated method stub
		String couponsCode = payment.optString("number");
		if(Tools.isNullOrEmpty(couponsCode))
		{
			//无码劵,自接返回退款成功
			resultData.setCode(Constant.CODE_SUCCESS);
			return;
		}
		
		JSONObject coupons = new JSONObject();
		coupons.put("coupons_code", couponsCode);

		List<JSONObject> couponslist = new ArrayList<JSONObject>();
		couponslist.add(coupons);

		JSONObject couponsJson = new JSONObject();
		couponsJson.put("chanel", chanel);
		couponsJson.put("couponslist", couponslist);
		if((!SysDictionary.CHANEL_MD01.equals(chanel))&&Tools.hv(orderNum))
		{
			couponsJson.put("bill_code", orderNum);
		}
		else
		{
			couponsJson.put("bill_code", oldBillNum);
		}

//		List<JSONObject> dataList = new ArrayList<JSONObject>();
//		dataList.add(couponsJson);
//
//		Data paramData = Data.get(tenancyId,storeId,Constant.CODE_SUCCESS);
//		paramData.setType(Type.COUPONS);
//		paramData.setOper(Oper.init);
//		paramData.setData(dataList);
//
//		Data responseData = new Data();
//		String param = JSONObject.fromObject(paramData).toString();
//
//		customerService.commonPost(param, responseData);
		
		Data responseData = Data.get();
		customerService.customerCouponCancelConsume(tenancyId, storeId, couponsJson, responseData, operType);

		if(Constant.CODE_SUCCESS==responseData.getCode())
		{
			resultData.setCode(Constant.CODE_SUCCESS);
			
			JSONObject paymentJson = new JSONObject();
			paymentJson.put("payment_state", SysDictionary.PAYMENT_STATE_REFUND_COMPLETE);
			paymentJson.put("id", payment.optInt("id"));
			paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", paymentJson);
		}
		else
		{
			if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
			{
				resultData.setCode(Constant.CODE_CONN_EXCEPTION);
				resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
			}
			else
			{
				JSONObject paymentJson = new JSONObject();
				paymentJson.put("payment_state", SysDictionary.PAYMENT_STATE_REFUND_FAILURE);
				paymentJson.put("id", payment.optInt("id"));
				paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", paymentJson);

				resultData.setCode(responseData.getCode());
				resultData.setMsg(responseData.getMsg());
			}
			
			// 退款失败,记录退款异常
			Date reportDate = ParamUtil.getDateValueByObject(payment, "report_date");
			Integer shiftId = ParamUtil.getIntegerValueByObject(payment, "shift_id");
			String posNum = ParamUtil.getStringValueByObject(payment, "pos_num");
			String optNum = ParamUtil.getStringValueByObject(payment, "cashier_num");
			Integer paymentId = ParamUtil.getIntegerValueByObject(payment, "jzid");
			String paymentClass = ParamUtil.getStringValueByObject(payment, "payment_class");
			String thirdBillCode = billNum + "_" + couponsCode;
			Double revokedTrading = Math.abs(ParamUtil.getDoubleValueByObject(payment, "currency_amount"));

			PosThirdPaymentRefundEntity paymentRefund = new PosThirdPaymentRefundEntity(tenancyId, storeId, reportDate, shiftId, posNum, optNum, chanel, SysDictionary.SERVICE_TYPE_CONSUME, oldBillNum, thirdBillCode, null, paymentClass, paymentId, Math.abs(billPaymentAmount), revokedTrading, currentTime,
					currentTime, optNum, String.valueOf(resultData.getCode()), resultData.getMsg(), operType, SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL, SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL);
			paymentRefund.setExtra(couponsJson.toString());

			thirdPaymentRefundService.thirdPaymentRefundFailure(tenancyId, storeId, paymentRefund);
		}
	}

	@Override
	public void paymentCancel(String tenancyId, int storeId, String billNum, String orderNum, String chanel, Double billPaymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData, String operType) throws Exception
	{
		// TODO Auto-generated method stub
		String couponsCode = payment.optString("number");
		if(Tools.isNullOrEmpty(couponsCode))
		{
			//无码劵,自接返回退款成功
			resultData.setCode(Constant.CODE_SUCCESS);
			return;
		}
		
		JSONObject coupons = new JSONObject();
		coupons.put("coupons_code", couponsCode);

		List<JSONObject> couponslist = new ArrayList<JSONObject>();
		couponslist.add(coupons);

		JSONObject couponsJson = new JSONObject();
		couponsJson.put("chanel", chanel);
		couponsJson.put("couponslist", couponslist);
		if((!SysDictionary.CHANEL_MD01.equals(chanel))&&Tools.hv(orderNum))
		{
			couponsJson.put("bill_code", orderNum);
		}
		else
		{
			couponsJson.put("bill_code", billNum);
		}
		
//		List<JSONObject> dataList = new ArrayList<JSONObject>();
//		dataList.add(couponsJson);
//
//		Data paramData = Data.get(tenancyId,storeId,Constant.CODE_SUCCESS);
//		paramData.setType(Type.COUPONS);
//		paramData.setOper(Oper.init);
//		paramData.setData(dataList);
//
//		Data responseData = new Data();
//		String param = JSONObject.fromObject(paramData).toString();
//
//		customerService.commonPost(param, responseData);
		
		Data responseData = Data.get();
		customerService.customerCouponCancelConsume(tenancyId, storeId, couponsJson, responseData, operType);

		if(Constant.CODE_SUCCESS == responseData.getCode())
		{
			resultData.setCode(Constant.CODE_SUCCESS);
		}
		else
		{
			if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
			{
				resultData.setCode(Constant.CODE_CONN_EXCEPTION);
				resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
			}
			else
			{
				resultData.setCode(responseData.getCode());
				resultData.setMsg(responseData.getMsg());
			}
				
			// 退款失败,记录退款异常
			Date reportDate = ParamUtil.getDateValueByObject(payment, "report_date");
			Integer shiftId = ParamUtil.getIntegerValueByObject(payment, "shift_id");
			String posNum = ParamUtil.getStringValueByObject(payment, "pos_num");
			String optNum = ParamUtil.getStringValueByObject(payment, "cashier_num");
			Integer paymentId = ParamUtil.getIntegerValueByObject(payment, "jzid");
			String paymentClass = ParamUtil.getStringValueByObject(payment, "payment_class");
			String thirdBillCode = billNum + "_" + couponsCode;
			Double revokedTrading = ParamUtil.getDoubleValueByObject(payment, "currency_amount");
			
			PosThirdPaymentRefundEntity paymentRefund = new PosThirdPaymentRefundEntity(tenancyId, storeId, reportDate, shiftId, posNum, optNum, chanel, SysDictionary.SERVICE_TYPE_CONSUME, billNum, thirdBillCode, null, paymentClass, paymentId, billPaymentAmount, revokedTrading, currentTime,
					currentTime, optNum, String.valueOf(resultData.getCode()), resultData.getMsg(), operType, SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL, SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL);
			paymentRefund.setExtra(couponsJson.toString());
			
			thirdPaymentRefundService.thirdPaymentRefundFailure(tenancyId, storeId, paymentRefund);
		}
	}

	@Override
	public void paymentRegain(String tenancyId, int storeId, String billNum, String orderNum, String chanel, Double billPaymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData, String operType) throws Exception
	{
		// TODO Auto-generated method stub
		String couponsCode = payment.optString("number");
		if(Tools.isNullOrEmpty(couponsCode))
		{
			//无码劵,自接返回退款成功
			resultData.setCode(Constant.CODE_SUCCESS);
			return;
		}
		JSONObject coupons = new JSONObject();
		coupons.put("coupons_code", couponsCode);

		List<JSONObject> couponslist = new ArrayList<JSONObject>();
		couponslist.add(coupons);

		JSONObject couponsJson = new JSONObject();
		couponsJson.put("chanel", chanel);
		couponsJson.put("couponslist", couponslist);
		if((!SysDictionary.CHANEL_MD01.equals(chanel))&&Tools.hv(orderNum))
		{
			couponsJson.put("bill_code", orderNum);
		}
		else
		{
			couponsJson.put("bill_code", billNum);
		}

//		List<JSONObject> dataList = new ArrayList<JSONObject>();
//		dataList.add(couponsJson);
//
//		Data paramData = Data.get(tenancyId,storeId,Constant.CODE_SUCCESS);
//		paramData.setType(Type.COUPONS);
//		paramData.setOper(Oper.init);
//		paramData.setData(dataList);
//
//		Data responseData = new Data();
//		String param = JSONObject.fromObject(paramData).toString();
//
//		customerService.commonPost(param, responseData);
		
		Data responseData = Data.get();
		customerService.customerCouponCancelConsume(tenancyId, storeId, couponsJson, responseData, operType);

		if(responseData.isSuccess())
		{
			resultData.setCode(Constant.CODE_SUCCESS);
		}
		else
		{
			if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
			{
				resultData.setCode(Constant.CODE_CONN_EXCEPTION);
				resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
			}
			else
			{
				resultData.setCode(responseData.getCode());
				resultData.setMsg(responseData.getMsg());
			}
			
			// 退款失败,记录退款异常
			Date reportDate = ParamUtil.getDateValueByObject(payment, "report_date");
			Integer shiftId = ParamUtil.getIntegerValueByObject(payment, "shift_id");
			String posNum = ParamUtil.getStringValueByObject(payment, "pos_num");
			String optNum = ParamUtil.getStringValueByObject(payment, "cashier_num");
			Integer paymentId = ParamUtil.getIntegerValueByObject(payment, "jzid");
			String paymentClass = ParamUtil.getStringValueByObject(payment, "payment_class");
			String thirdBillCode = billNum + "_" + couponsCode;
			Double revokedTrading = ParamUtil.getDoubleValueByObject(payment, "currency_amount");
			
			PosThirdPaymentRefundEntity paymentRefund = new PosThirdPaymentRefundEntity(tenancyId, storeId, reportDate, shiftId, posNum, optNum, chanel, SysDictionary.SERVICE_TYPE_CONSUME, billNum, thirdBillCode, null, paymentClass, paymentId, billPaymentAmount, revokedTrading, currentTime,
					currentTime, optNum, String.valueOf(resultData.getCode()), resultData.getMsg(), operType, SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL, SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL);
			paymentRefund.setExtra(couponsJson.toString());
			
			thirdPaymentRefundService.thirdPaymentRefundFailure(tenancyId, storeId, paymentRefund);
		}
	}

	@Override
	public void paymentDebitResult(String tenancyId, int storeId, String billNum, JSONObject paymentJson) throws Exception
	{
		// TODO 自动生成的方法存根
		JSONObject updatePaymentJson = new JSONObject();
		updatePaymentJson.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
		updatePaymentJson.put("id", paymentJson.optInt("id"));
		paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", updatePaymentJson);
	}
}
