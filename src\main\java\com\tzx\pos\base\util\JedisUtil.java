package com.tzx.pos.base.util;

import org.apache.log4j.Logger;

/**
 *
 * <AUTHOR> 2015年6月10日-下午5:19:09
 */
public class JedisUtil
{
	protected static Logger log = Logger.getLogger(JedisUtil.class);

	/**
	 * 私有构造器.
	 */
	private JedisUtil()
	{

	}

//	private static Map<String, JedisPool>	maps	= new HashMap<String, JedisPool>();
//
//	/**
//	 * 获取连接池.
//	 * 
//	 * @return 连接池实例
//	 */
//	private static JedisPool getPool(String ip, int port)
//	{
//		String key = ip + ":" + port;
//		JedisPool pool = null;
//		if (!maps.containsKey(key))
//		{
//			JedisPoolConfig config = new JedisPoolConfig();
//			config.setMaxActive(PropertiesLoader.getInteger("jedis.maxactive"));
//			config.setMaxIdle(PropertiesLoader.getInteger("jedis.maxidle"));
//			config.setMaxWait(PropertiesLoader.getInteger("jedis.maxwait"));
//			config.setTestOnBorrow(true);
//			config.setTestOnReturn(true);
//
//			try
//			{
//				/**
//				 * 如果你遇到 java.net.SocketTimeoutException: Read timed out
//				 * exception的异常信息 请尝试在构造JedisPool的时候设置自己的超时值.
//				 * JedisPool默认的超时时间是30秒(单位毫秒)
//				 */
//				pool = new JedisPool(config, ip, port, 30000);
//				maps.put(key, pool);
//			}
//			catch (Exception e)
//			{
//				e.printStackTrace();
//			}
//		}
//		else
//		{
//			pool = maps.get(key);
//		}
//		return pool;
//	}
//
//	/**
//	 * 类级的内部类，也就是静态的成员式内部类，该内部类的实例与外部类的实例 没有绑定关系，而且只有被调用到时才会装载，从而实现了延迟加载。
//	 */
//	private static class RedisUtilHolder
//	{
//		/**
//		 * 静态初始化器，由JVM来保证线程安全
//		 */
//		private static JedisUtil	instance	= new JedisUtil();
//	}
//
//	public static JedisUtil getInstance()
//	{
//		return RedisUtilHolder.instance;
//	}
//	
//	/**
//	 * 获取Redis实例.
//	 * 
//	 * @return Redis工具类实例
//	 */
//	public Jedis getJedis(String ip, int port)
//	{
//		Jedis jedis = null;
//		int count = 0;
//		do
//		{
//			try
//			{
//				jedis = getPool(ip, port).getResource();
//				// log.info("get redis master1!");
//			}
//			catch (Exception e)
//			{
//				log.error("get redis master1 failed!", e);
//				// 销毁对象
//				getPool(ip, port).returnBrokenResource(jedis);
//			}
//			count++;
//		}
//		while (jedis == null && count < 1000);
//		return jedis;
//	}
//
//	/**
//	 * 释放redis实例到连接池.
//	 * 
//	 * @param jedis
//	 *            redis实例
//	 */
//	public void closeJedis(Jedis jedis, String ip, int port)
//	{
//		if (jedis != null)
//		{
//			getPool(ip, port).returnResource(jedis);
//		}
//	}
}
