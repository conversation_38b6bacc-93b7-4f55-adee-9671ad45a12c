package com.tzx.framework.common.util;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import com.google.gson.reflect.TypeToken;

import jxl.format.Alignment;
import jxl.format.Colour;
import jxl.format.UnderlineStyle;
import jxl.format.VerticalAlignment;
import jxl.write.Label;
import jxl.write.Number;
import jxl.write.NumberFormat;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import net.sf.json.JSONObject;


/**
 * scm导出通用工具类
 * Created by Libo on 2016-10-19.
 * update by zhouqinchi on 2017-04-12
 */
public class SystemExportUtils
{
	
	public static VerticalAlignment verticalAlignment = VerticalAlignment.CENTRE;

	public static Alignment alignment = Alignment.CENTRE;

	public static void createSheetNew(WritableWorkbook wwb, JSONObject main, JSONObject params) throws Exception
	{
		WritableSheet ws = wwb.createSheet(main.optString("sheetName"), main.optInt("sheetNum"));
		// 创建表头
		createHeaderNew(wwb, ws, main, params);
		// 设置数据
		initDataNew(wwb, ws, params);

	}

	public static void createHeaderNew(WritableWorkbook wwb, WritableSheet ws, JSONObject main, JSONObject params) throws Exception
	{
		String headName = main.optString("headName");
		if ("".equals(headName))
		{
			headName = main.optString("sheetName");
		}
		WritableFont wf = new WritableFont(WritableFont.ARIAL, 12, WritableFont.BOLD, false, UnderlineStyle.NO_UNDERLINE, Colour.BLACK);
		WritableCellFormat wcf = createMyWritableCellFormat(wf,alignment);
		if(main.optInt("headlength")==0)
		{
			ws.setRowView(0, 500);
		}
		else
		{
			ws.setRowView(0, 400 * main.optInt("headlength"));
		}
		ws.addCell(new Label(0, 0, headName, wcf));
		ws.mergeCells(0, 0, params.optInt("length") - 1, 0);
		params.put("dataIndex", 1);
	}

	@SuppressWarnings("unchecked")
	public static void initDataNew(WritableWorkbook wwb, WritableSheet ws, JSONObject params) throws Exception
	{
		int dataIndex = params.optInt("dataIndex");
		//根据参数调整列宽度缩放比例
		int scaling = params.optInt("scaling");
		// 准备数据
		List<JSONObject> detail = null;
		if (params.opt("rows") instanceof String)
		{
			detail = (List<JSONObject>) GsonUtil.toT((String) params.get("rows"), new TypeToken<List<JSONObject>>(){}.getType());
			params.put("rows", detail);
		}
		else
		{
			detail = (List<JSONObject>) params.opt("rows");
		}
		//统计合计项
		if (params.containsKey("total_columns"))
		{
			detail.add(recountFooter(detail, params));
		}
		//获取列属性
		List<JSONObject> columns = (List<JSONObject>) params.opt("columns");
		
		String titles = (String) params.opt("titles");
		// 载入标题信息
		if (!"".equals(titles) && titles != null)
		{
			// 定义样式
			WritableFont wf = new WritableFont(WritableFont.ARIAL, 8, WritableFont.BOLD, false, UnderlineStyle.NO_UNDERLINE, Colour.BLACK);
			WritableCellFormat wcf = createMyWritableCellFormat(wf,alignment);
			String titleArr[] = titles.split("#");
			dataIndex += titleArr.length;
			for (int i = 0; i < titleArr.length; i++)
			{
				List<JSONObject> titleList = GsonUtil.toT(titleArr[i], new TypeToken<List<JSONObject>>(){}.getType());
				for (int j = 0; j < titleList.size(); j++)
				{
					JSONObject row = titleList.get(j);
					if (!"".equals(row.optString("title")))
					{
						ws.addCell(new Label(j, i + 1, row.optString("title"), wcf));
					}
					if (row.optBoolean("merge"))
					{
						int mergeX1 = row.optInt("mergeX1");
						int mergeX2 = row.optInt("mergeX2") + 1;
						int mergeY1 = row.optInt("mergeY1");
						int mergeY2 = row.optInt("mergeY2") + 1;
						ws.mergeCells(mergeX1, mergeX2, mergeY1, mergeY2);
					}
				}
			}
		}
		else
		{
			WritableFont wf = new WritableFont(WritableFont.ARIAL, 8, WritableFont.BOLD, false, UnderlineStyle.NO_UNDERLINE, Colour.BLACK);
			WritableCellFormat wcf = createMyWritableCellFormat(wf,alignment);
			for (int j = 0; j < columns.size(); j++)
			{
				JSONObject column = columns.get(j);
				ws.addCell(new Label(j, dataIndex, column.optString("title"), wcf));
			}
			dataIndex++;
		}
		WritableFont wf1 = new WritableFont(WritableFont.ARIAL, 8, WritableFont.NO_BOLD, false, UnderlineStyle.NO_UNDERLINE, Colour.BLACK);
		WritableCellFormat wcf1 = null;
		for (int i = 0; i < detail.size(); i++)
		{
			JSONObject row = detail.get(i);
			for (int j = 0; j < columns.size(); j++)
			{
				JSONObject column = columns.get(j);
				Alignment align = Alignment.CENTRE;
				String value = row.optString(column.optString("field"));
				if ("right".equalsIgnoreCase(column.optString("align")))
				{
					align = Alignment.RIGHT;
				}
				else if ("left".equalsIgnoreCase(column.optString("align")))
				{
					align = Alignment.LEFT;
				}
				if (column.containsKey("count") && !"".equals(column.optString("count")))
				{
					wcf1 = createMyWritableCellFormat(wf1,align,column.optInt("count"));
					// value = formatterValue(value,column.optInt("count"));
					if ("".equals(value) || "null".equals(value) || value == null)
					{
						ws.addCell(new Label(j, i + dataIndex, "", wcf1));
					}
					else
					{
						ws.addCell(new Number(j, i + dataIndex, Double.parseDouble(value), wcf1));
					}
				}
				else
				{
					wcf1 = createMyWritableCellFormat(wf1,align);
					if ("num".equals(column.optString("field")))
					{
						ws.addCell(new Label(j, i + dataIndex, ("合计".equals(value)) ? value : String.valueOf(i + 1), wcf1));
					}
					else
					{
						ws.addCell(new Label(j, i + dataIndex, (value == null || "null".equals(value)) ? "" : value, wcf1));
					}
				}
				ws.setColumnView(j, (int) (column.optInt("width") / scaling));
			}
		}
	}

	@SuppressWarnings({ "unchecked", "unused" })
	public static WritableWorkbook batchExportExcel(WritableWorkbook wwb, JSONObject params) throws Exception
	{
		List<JSONObject> main_rows = (List<JSONObject>) params.opt("main_rows");
		List<JSONObject> detail_rows = (List<JSONObject>) params.opt("detail_rows");
		List<JSONObject> columns = (List<JSONObject>) params.opt("columns");
		int index = 0;
		List<JSONObject> details = null;
		String detail_id = params.optString("detail_id");
		if ("".equals(detail_id) || detail_id == null)
		{
			detail_id = "bill_id";
		}
		for (JSONObject main : main_rows)
		{
			// 指定分页号与分页名称
			main.put("sheetNum", index++);
			main.put("sheetName", main.optString(params.optString("sheetname")));
			// 指定抬头信息（标题）
			List<JSONObject> headparams = (List<JSONObject>) params.opt("headparams");
			String headName = "";
			for (JSONObject headparam : headparams)
			{
				Set<String> set = headparam.keySet();
				for (String key : set)
				{
					if("".equals(headparam.optString(key)))
					{
						headName += "【" + main.optString(key) + "】";
					}
					else
					{
						headName += "【" + headparam.optString(key) + ":" + main.optString(key) + "】";
					}
				}
				headName += "\r\n";
			}
			main.put("headlength", headparams.size());
			main.put("headName", headName.substring(0, headName.length() - 2));
			details = new ArrayList<JSONObject>();
			for (JSONObject detail : detail_rows)
			{
				if (main.optInt("id") == 0)
				{
					if (main.optString("id").equals(detail.optString(detail_id)))
					{
						details.add(detail);
					}
				}
				else
				{
					if (main.optInt("id") == detail.optInt(detail_id))
					{
						details.add(detail);
					}
				}
			}
			params.put("rows", details);
			createSheetNew(wwb, main, params);
		}
		return wwb;
	}
	
	public static WritableCellFormat createMyWritableCellFormat(WritableFont wf,Alignment align) throws Exception
	{
		WritableCellFormat wcf = new WritableCellFormat(wf);
		wcf.setWrap(true);
		wcf.setVerticalAlignment(verticalAlignment);
		wcf.setAlignment(align);
		return wcf;
	}
	
	public static WritableCellFormat createMyWritableCellFormat(WritableFont wf,Alignment align,int count) throws Exception
	{
		WritableCellFormat wcf = null;
		if (count == 2)
		{
			wcf = new WritableCellFormat(wf, new NumberFormat("#0.00"));
		}
		else if (count == 4)
		{
			wcf = new WritableCellFormat(wf, new NumberFormat("#0.0000"));
		}
		else if (count == 6)
		{
			wcf = new WritableCellFormat(wf, new NumberFormat("#0.000000"));
		}
		wcf.setWrap(true);
		wcf.setVerticalAlignment(verticalAlignment);
		wcf.setAlignment(align);
		return wcf;
	}

	public static String formatterValue(String value, int point) throws Exception
	{
		if ("".equals(value) || value == null || "null".equals(value)) return value;
		BigDecimal b = new BigDecimal(Double.parseDouble(value));
		double num = b.setScale(point, BigDecimal.ROUND_HALF_UP).doubleValue();
		return String.format("%." + point + "f", num);
	}

	public static JSONObject recountFooter(List<JSONObject> rows, JSONObject obj) throws Exception
	{
		JSONObject foot = JSONObject.fromObject("{}");
		DecimalFormat df = new DecimalFormat("0.000000");
		String total_title = obj.optString("total_title");
		if ("".equals(total_title))
		{
			total_title = "num";
		}
		foot.put(total_title, "合计");
		if (rows != null && rows.size() > 0)
		{
			for (String str : obj.optString("total_columns").split(","))
			{
				double total = 0.00;
				for (int i = 0; i < rows.size(); i++)
				{
					total = Scm.qaddSix(DoubleHelper.round(JsonUtils.getDouble(rows.get(i), str),6), total);
				}
				foot.put(str, df.format(total));
			}
		}
		else
		{
			for (String str : obj.optString("total_columns").split(","))
			{
				double total = 0.00;
				foot.put(str, total);
			}
		}
		return foot;
	}
}
