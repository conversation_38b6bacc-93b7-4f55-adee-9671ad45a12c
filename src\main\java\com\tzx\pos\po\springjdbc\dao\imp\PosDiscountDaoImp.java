package com.tzx.pos.po.springjdbc.dao.imp;

import java.util.Date;
import java.util.List;

import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Repository;

import com.tzx.framework.common.entity.Data;
import com.tzx.pos.base.dao.imp.BaseDaoImp;
import com.tzx.pos.bo.dto.BasicDataUserDiscountAuthority;
import com.tzx.pos.po.springjdbc.dao.PosDiscountDao;

/**
 * Created by kevin on 2017-08-14.
 */
@Repository(PosDiscountDao.NAME)
public class PosDiscountDaoImp extends BaseDaoImp implements PosDiscountDao {
    /**
     *
     * @param tenancyId
     * @param storeId
     * @param optNum
     * @return
     * @throws Exception
     */
    @SuppressWarnings("unchecked")
	@Override
    public List<BasicDataUserDiscountAuthority> getUserDiscountCase(String tenancyId, int storeId, String optNum) throws Exception {
        StringBuffer sb = new StringBuffer();
        sb.append("select da.pre_reward_money,pre_discount_money,da.employee_id,single_discount_money,single_reward_money from user_discount_authority da ")
                .append(" where da.tenancy_id = ? and da.employee_id = ? ")
                .append(" and da.discount_type = '2'");
        return  (List<BasicDataUserDiscountAuthority>)this.query(sb.toString(), new Object[] {tenancyId, Integer.parseInt(optNum)}, BeanPropertyRowMapper.newInstance(BasicDataUserDiscountAuthority.class));
    }

    /**
     *
     * @param tenancyId
     * @param storeId
     * @param optNum
     * @return
     * @throws Exception
     */
    public int getRoleIdByOptNum(String tenancyId, int storeId, String optNum) throws Exception {
        //根据员工id查出角色id
        int roleId = 0;
        if (StringUtils.isNotEmpty(optNum)) {
            StringBuffer checkRole = new StringBuffer();
            checkRole.append("select ua.employee_id,ua.roles_id from user_authority as ua ")
                    .append("where ua.valid_state='1'")
                    .append(" and ua.tenancy_id = ?")
                    .append(" and ua.store_id = ?")
                    .append(" and ua.employee_id = ? ");
            SqlRowSet rs = this.query4SqlRowSet(checkRole.toString(), new Object[] {tenancyId, storeId, Integer.parseInt(optNum)});
            if (rs.next()) {
                roleId = rs.getInt("roles_id");
            }
        }
        return roleId;
    }

    /**
     *
     * @param tenancyId
     * @param roleId
     * @return
     * @throws Exception
     */
    @SuppressWarnings("unchecked")
	@Override
    public List<BasicDataUserDiscountAuthority> getRoleDiscountCase(String tenancyId, int roleId) throws Exception {
        //根据员工id查出角色id
//        int roleId = getRoleIdByOptNum(tenancyId, storeId, optNum);
//        if(roleId != 0) {
//			StringBuffer sb = new StringBuffer();
//			sb.append("select dc.id,dc.employee_id,dc.discount_id,dc.discount_authority_id,da.discount_type from user_discount_case dc ")
//					.append("left join user_discount_authority da on dc.tenancy_id = da.tenancy_id and dc.discount_authority_id = da.id")
//					.append(" and dc.employee_id = da.employee_id where da.tenancy_id = ? and da.store_id = ? and dc.employee_id = ?");
            StringBuffer sb = new StringBuffer();
            sb.append("select da.pre_reward_money,pre_discount_money,single_discount_money,single_reward_money from user_discount_authority da ")
                    .append(" where da.tenancy_id = ? ")
                    .append(" and da.employee_id = " + roleId + "and da.discount_type = '1'");
            return  (List<BasicDataUserDiscountAuthority>)this.query(sb.toString(), new Object[] {tenancyId}, BeanPropertyRowMapper.newInstance(BasicDataUserDiscountAuthority.class));
//        }
    }
    
	@SuppressWarnings("unchecked")
	@Override
	public List<BasicDataUserDiscountAuthority> getRoleDiscountAuthority(String tenancyId, int storeId, String optNum) throws Exception
	{
		StringBuffer sb = new StringBuffer();
		sb.append("select da.pre_reward_money,da.pre_discount_money,da.single_discount_money,da.single_reward_money from user_discount_authority da ");
		sb.append("left join user_authority_roles ar on da.tenancy_id=ar.tenancy_id and da.employee_id=ar.roles_id and da.discount_type='1' ");
		sb.append("where ar.tenancy_id=? and ar.store_id =? and ar.employee_id=? and da.discount_type='1' ");

		return (List<BasicDataUserDiscountAuthority>) this.query(sb.toString(), new Object[]
		{ tenancyId,storeId ,Integer.parseInt(optNum)}, BeanPropertyRowMapper.newInstance(BasicDataUserDiscountAuthority.class));
	}

    /**
     *
     * @param param
     * @param employeeId
     * @param reportDate
     * @return
     * @throws Exception
     */
    public Double getItemAmountByManagerNum(Data param, String employeeId, Date reportDate) throws Exception {
        String tenancyId = param.getTenancy_id();
        Integer storeId = param.getStore_id();
        double itemAmount = 0d;
        StringBuffer sb = new StringBuffer();
        sb.append("select coalesce(sum(pb.item_amount),0) as item_amount,pb.manager_num from pos_bill_item as pb ")
                .append(" where pb.tenancy_id = ? and pb.store_id = ? and pb.manager_num = ? and pb.report_date = ?")
                .append(" and pb.item_property in ('SETMEAL','SINGLE') group by pb.manager_num");
        SqlRowSet rs = this.query4SqlRowSet(sb.toString(), new Object[] {tenancyId, storeId, employeeId, reportDate});
        if (rs.next()) {
            itemAmount = rs.getDouble("item_amount");
        }
        return itemAmount;
    }
    
   @Override
   public Double getItemAmountByBillNum(Data param, String employeeId, String billNum) throws Exception {
       String tenancyId = param.getTenancy_id();
       Integer storeId = param.getStore_id();
       double itemAmount = 0d;
       StringBuffer sb = new StringBuffer();
       sb.append("select coalesce(sum(pb.item_amount),0) as item_amount,pb.manager_num from pos_bill_item as pb ")
               .append(" where pb.tenancy_id = ? and pb.store_id = ? and pb.manager_num = ? and pb.bill_num = ?")
               .append(" and pb.item_property in ('SETMEAL','SINGLE') group by pb.manager_num");
       SqlRowSet rs = this.query4SqlRowSet(sb.toString(), new Object[] {tenancyId, storeId, employeeId, billNum});
       if (rs.next()) {
           itemAmount = rs.getDouble("item_amount");
       }
       return itemAmount;
   }

    /**
     *
     * @param param
     * @param employeeId
     * @param reportDate
     * @return
     * @throws Exception
     */
    public Double getDiscountAmountByDiscountNum(Data param, int employeeId, Date reportDate,String billNum) throws Exception {
        String tenancyId = param.getTenancy_id();
        Integer storeId = param.getStore_id();
        double discountAmount = 0d;
        StringBuffer sb = new StringBuffer();
        sb.append("select coalesce(sum(pb.discountr_amount),0) as discount_amount,pb.discount_num from pos_bill as pb ")
                .append(" where pb.tenancy_id = ? and pb.store_id = ? and pb.discount_num = ? and pb.report_date = ? and pb.bill_num != ? ")
//                .append(" and pb.bill_property = '"  + SysDictionary.BILL_PROPERTY_CLOSED +"' group by pb.discount_num");
                .append(" group by pb.discount_num");
        SqlRowSet rs = this.query4SqlRowSet(sb.toString(), new Object[] {tenancyId, storeId, employeeId + "", reportDate,billNum});
        if (rs.next()) {
            discountAmount = rs.getDouble("discount_amount");
        }
        return discountAmount;
    }

	@Override
	public JSONObject getPosBillItemByRwid(String tenancyId, int storeId, String billNum, Integer rwid) throws Exception
	{
		String querySql = new String("select * from pos_bill_item bi where bi.tenancy_id=? and bi.store_id=? and bi.bill_num=? and bi.rwid=?");
		List<JSONObject> list = this.query4Json(tenancyId, querySql, new Object[]
		{ tenancyId, storeId, billNum, rwid });
		if (null != list && list.size() > 0)
		{
			return list.get(0);
		}
		return null;
	}

	@Override
	public void updateBillItemForDiscountByRwid(String tenancyId, int storeId, String billNum,Integer rwid,Integer discountModeId,String discountNum,Double discountRate,Integer discountReasonId) throws Exception
	{
		String updateSql = new String(
				"update pos_bill_item bi set discount_amount=0,discount_mode_id=?,discount_num=?,discount_rate=?,discount_reason_id=?,discountr_amount=0,single_amount=0,single_discount_rate=100,single_discount_amount=0 where bi.tenancy_id=? and bi.store_id=? and bi.bill_num=? and bi.rwid=?");
		this.update(updateSql, new Object[]
		{ discountModeId, discountNum, discountRate, discountReasonId, tenancyId, storeId, billNum, rwid });
	}
}
