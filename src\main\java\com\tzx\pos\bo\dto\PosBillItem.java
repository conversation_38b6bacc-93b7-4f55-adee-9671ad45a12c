package com.tzx.pos.bo.dto;

import java.io.Serializable;
import java.util.List;

/**
 * 
 * <AUTHOR> 
 * @日期：2015年5月19日-下午6:51:46
 */

public class PosBillItem implements Serializable
{

	/**
	 * 
	 */
	private static final long	serialVersionUID	= 1L;
	
	private String				tenancy_id;
	private String				store_id;
	private String				bill_num;
	private String				batch_num;
	private String				opt_num;
	private String				waiter_name;
	private String				item_mac_id;
	private String				item_serial;
	private String				combo_prop;
	private String				rwid;
	private String				item_id;
	private String				details_id;
	private String				item_num;
	private String				item_name;
	private String				unit_id;
	private String				item_unit_name;
	private String				item_price;
	private String				origin_item_price;
	private String				item_count;
	private String				item_amount;
	private String				real_amount;
	private String				method_money;
	private String				assist_num;
    private String				item_assist_num;
	private String				assist_money;
	private String				setmeal_id;
	private String				setmeal_rwid;
	private String				assist_item_id;
	private String				discount_mode_id;
	private String				discount_rate;
	private String				discount_amount;
	private String				discountr_amount;
	private String				single_amount;
	private String				single_discount_rate;
	private String				discount_num;
	private String				discount_reason_id;
	private String				item_taste;
	private String				item_time;
	private String				sale_mode;
	private String				item_remark;
	private String				reason_id;
	private String				manager_num;
	private String				item_property;
	private String				remark;
	private String				waitcall_tag;
	private String				print_tag;
	private String				payment_state;
	private List<?>				method;
	private String				activity_id;
	private String				activity_batch_num;
	private String				activity_rule_id;
	private String				activity_count;
	private String				default_state;
	private String				served_state;
	private String              original_table;
	private String price_type;
	private String sepcial_price_id;

    public String getPrice_type() {
        return price_type;
    }

    public void setPrice_type(String price_type) {
        this.price_type = price_type;
    }

    public String getSepcial_price_id() {
        return sepcial_price_id;
    }

    public void setSepcial_price_id(String sepcial_price_id) {
        this.sepcial_price_id = sepcial_price_id;
    }

    public String getOriginal_table() {
        return original_table;
    }

    public void setOriginal_table(String original_table) {
        this.original_table = original_table;
    }

    public String getTenancy_id()
	{
		return tenancy_id;
	}
	public void setTenancy_id(String tenancy_id)
	{
		this.tenancy_id = tenancy_id;
	}
	public String getStore_id()
	{
		return store_id;
	}
	public void setStore_id(String store_id)
	{
		this.store_id = store_id;
	}
	public String getBill_num()
	{
		return bill_num;
	}
	public void setBill_num(String bill_num)
	{
		this.bill_num = bill_num;
	}
	public String getBatch_num()
	{
		return batch_num;
	}
	public void setBatch_num(String batch_num)
	{
		this.batch_num = batch_num;
	}
	public String getOpt_num()
	{
		return opt_num;
	}
	public void setOpt_num(String opt_num)
	{
		this.opt_num = opt_num;
	}
	public String getWaiter_name()
	{
		return waiter_name;
	}
	public void setWaiter_name(String waiter_name)
	{
		this.waiter_name = waiter_name;
	}
	public String getItem_mac_id()
	{
		return item_mac_id;
	}
	public void setItem_mac_id(String item_mac_id)
	{
		this.item_mac_id = item_mac_id;
	}
	public String getItem_serial()
	{
		return item_serial;
	}
	public void setItem_serial(String item_serial)
	{
		this.item_serial = item_serial;
	}
	public String getCombo_prop()
	{
		return combo_prop;
	}
	public void setCombo_prop(String combo_prop)
	{
		this.combo_prop = combo_prop;
	}
	public String getRwid()
	{
		return rwid;
	}
	public void setRwid(String rwid)
	{
		this.rwid = rwid;
	}
	public String getItem_id()
	{
		return item_id;
	}
	public void setItem_id(String item_id)
	{
		this.item_id = item_id;
	}
	public String getDetails_id()
	{
		return details_id;
	}
	public void setDetails_id(String details_id)
	{
		this.details_id = details_id;
	}
	public String getItem_num()
	{
		return item_num;
	}
	public void setItem_num(String item_num)
	{
		this.item_num = item_num;
	}
	public String getItem_name()
	{
		return item_name;
	}
	public void setItem_name(String item_name)
	{
		this.item_name = item_name;
	}
	public String getUnit_id()
	{
		return unit_id;
	}
	public void setUnit_id(String unit_id)
	{
		this.unit_id = unit_id;
	}
	public String getItem_unit_name()
	{
		return item_unit_name;
	}
	public void setItem_unit_name(String item_unit_name)
	{
		this.item_unit_name = item_unit_name;
	}
	public String getItem_price()
	{
		return item_price;
	}
	public void setItem_price(String item_price)
	{
		this.item_price = item_price;
	}
	public String getOrigin_item_price()
	{
		return origin_item_price;
	}
	public void setOrigin_item_price(String origin_item_price)
	{
		this.origin_item_price = origin_item_price;
	}
	public String getItem_count()
	{
		return item_count;
	}
	public void setItem_count(String item_count)
	{
		this.item_count = item_count;
	}
	public String getItem_amount()
	{
		return item_amount;
	}
	public void setItem_amount(String item_amount)
	{
		this.item_amount = item_amount;
	}
	public String getReal_amount()
	{
		return real_amount;
	}
	public void setReal_amount(String real_amount)
	{
		this.real_amount = real_amount;
	}
	public String getMethod_money()
	{
		return method_money;
	}
	public void setMethod_money(String method_money)
	{
		this.method_money = method_money;
	}
	public String getAssist_num()
	{
		return assist_num;
	}
	public void setAssist_num(String assist_num)
	{
		this.assist_num = assist_num;
	}
	public String getAssist_money()
	{
		return assist_money;
	}
	public void setAssist_money(String assist_money)
	{
		this.assist_money = assist_money;
	}
	public String getSetmeal_id()
	{
		return setmeal_id;
	}
	public void setSetmeal_id(String setmeal_id)
	{
		this.setmeal_id = setmeal_id;
	}
	public String getSetmeal_rwid()
	{
		return setmeal_rwid;
	}
	public void setSetmeal_rwid(String setmeal_rwid)
	{
		this.setmeal_rwid = setmeal_rwid;
	}
	public String getAssist_item_id()
	{
		return assist_item_id;
	}
	public void setAssist_item_id(String assist_item_id)
	{
		this.assist_item_id = assist_item_id;
	}
	public String getDiscount_mode_id()
	{
		return discount_mode_id;
	}
	public void setDiscount_mode_id(String discount_mode_id)
	{
		this.discount_mode_id = discount_mode_id;
	}
	public String getDiscount_rate()
	{
		return discount_rate;
	}
	public void setDiscount_rate(String discount_rate)
	{
		this.discount_rate = discount_rate;
	}
	public String getDiscount_amount()
	{
		return discount_amount;
	}
	public void setDiscount_amount(String discount_amount)
	{
		this.discount_amount = discount_amount;
	}
	public String getDiscountr_amount()
	{
		return discountr_amount;
	}
	public void setDiscountr_amount(String discountr_amount)
	{
		this.discountr_amount = discountr_amount;
	}
	public String getSingle_amount()
	{
		return single_amount;
	}
	public void setSingle_amount(String single_amount)
	{
		this.single_amount = single_amount;
	}
	public String getSingle_discount_rate()
	{
		return single_discount_rate;
	}
	public void setSingle_discount_rate(String single_discount_rate)
	{
		this.single_discount_rate = single_discount_rate;
	}
	public String getDiscount_num()
	{
		return discount_num;
	}
	public void setDiscount_num(String discount_num)
	{
		this.discount_num = discount_num;
	}
	public String getDiscount_reason_id()
	{
		return discount_reason_id;
	}
	public void setDiscount_reason_id(String discount_reason_id)
	{
		this.discount_reason_id = discount_reason_id;
	}
	public String getItem_taste()
	{
		return item_taste;
	}
	public void setItem_taste(String item_taste)
	{
		this.item_taste = item_taste;
	}
	public String getItem_time()
	{
		return item_time;
	}
	public void setItem_time(String item_time)
	{
		this.item_time = item_time;
	}
	public String getSale_mode()
	{
		return sale_mode;
	}
	public void setSale_mode(String sale_mode)
	{
		this.sale_mode = sale_mode;
	}
	public String getItem_remark()
	{
		return item_remark;
	}
	public void setItem_remark(String item_remark)
	{
		this.item_remark = item_remark;
	}
	public String getReason_id()
	{
		return reason_id;
	}
	public void setReason_id(String reason_id)
	{
		this.reason_id = reason_id;
	}
	public String getManager_num()
	{
		return manager_num;
	}
	public void setManager_num(String manager_num)
	{
		this.manager_num = manager_num;
	}
	public String getItem_property()
	{
		return item_property;
	}
	public void setItem_property(String item_property)
	{
		this.item_property = item_property;
	}
	public String getRemark()
	{
		return remark;
	}
	public void setRemark(String remark)
	{
		this.remark = remark;
	}
	public String getWaitcall_tag()
	{
		return waitcall_tag;
	}
	public void setWaitcall_tag(String waitcall_tag)
	{
		this.waitcall_tag = waitcall_tag;
	}
	public String getPrint_tag()
	{
		return print_tag;
	}
	public void setPrint_tag(String print_tag)
	{
		this.print_tag = print_tag;
	}
	public String getPayment_state()
	{
		return payment_state;
	}
	public void setPayment_state(String payment_state)
	{
		this.payment_state = payment_state;
	}
	public List<?> getMethod()
	{
		return method;
	}
	public void setMethod(List<?> method)
	{
		this.method = method;
	}
	public String getActivity_id()
	{
		return activity_id;
	}
	public void setActivity_id(String activity_id)
	{
		this.activity_id = activity_id;
	}
	public String getActivity_batch_num()
	{
		return activity_batch_num;
	}
	public void setActivity_batch_num(String activity_batch_num)
	{
		this.activity_batch_num = activity_batch_num;
	}
	public String getActivity_rule_id()
	{
		return activity_rule_id;
	}
	public void setActivity_rule_id(String activity_rule_id)
	{
		this.activity_rule_id = activity_rule_id;
	}
	public String getActivity_count()
	{
		return activity_count;
	}
	public void setActivity_count(String activity_count)
	{
		this.activity_count = activity_count;
	}
	public String getDefault_state()
	{
		return default_state;
	}
	public void setDefault_state(String default_state)
	{
		this.default_state = default_state;
	}
	public String getServed_state()
	{
		return served_state;
	}
	public void setServed_state(String served_state)
	{
		this.served_state = served_state;
	}

    public String getItem_assist_num() {
        return item_assist_num;
    }

    public void setItem_assist_num(String item_assist_num) {
        this.item_assist_num = item_assist_num;
    }
}
