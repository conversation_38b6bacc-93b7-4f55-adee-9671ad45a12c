package com.tzx.pos.base.dao;

import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.util.dao.GenericDao;

/**
 * Created by Administrator on 2017-03-27.
 */
public interface DataUploadDao extends GenericDao{
    String NAME = "com.tzx.pos.base.dao.imp.DataUploadImp";

    /**
     * 门店数据上传
     * @param tenancy_id
     * @param store_id
     * @param old_table_code
     * @param new_table_code
     * @param old_bill_num
     * @param new_bill_num
     * @return
     */
    Data uploadData(String tenancy_id, String store_id,String old_table_code,String new_table_code,String old_bill_num,String new_bill_num)throws Exception;
    
    /**
	 * 门店端菜品估清数据上传
	 */
	public String getPosSoldOutData(String tenent_id);

}
