<!-- The contents of this file will be loaded for each web application -->
<Context>

    <!-- Default set of monitored resources -->
    <WatchedResource>WEB-INF/web.xml</WatchedResource>
	
    <!-- Uncomment this to disable session persistence across Tomcat restarts -->
    <!-- <Manager pathname="" /> -->
    
      <Resource
     	   name="jdbc/multi_tenancydbjndi"
     	   type="javax.sql.DataSource"
     	   driverClassName="org.postgresql.Driver"
	       url="****************************************"
	       username="tzxdbuser"
      	   password="p@ss2d"
     	   maxIdle="10"
     	   maxWait="3000"
     	   maxActive="100"/>

</Context>