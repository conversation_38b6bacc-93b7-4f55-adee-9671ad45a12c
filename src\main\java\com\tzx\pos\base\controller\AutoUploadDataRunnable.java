package com.tzx.pos.base.controller;

import java.util.Map;
import org.apache.log4j.Logger;
import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.pos.base.service.AutoUploadDataService;

/**
 * 自动上传门店数据到总部
 */
public class AutoUploadDataRunnable implements Runnable {

    private final Logger logger = Logger.getLogger(AutoUploadDataRunnable.class);

    @Override
    public void run() {
    	synchronized (AutoUploadDataRunnable.class) {		
	        try {	
	            Map<String, String> systemMap = Constant.getSystemMap();	
	            if (!systemMap.containsKey("tenent_id") || !systemMap.containsKey("store_id") || "".equals(systemMap.get("tenent_id")) || "".equals(systemMap.get("store_id"))) {
	                logger.info("门店数据自动上传: tenent_id: " + systemMap.get("tenent_id") + ", store_id: " + systemMap.get("store_id"));
	                return;
	            }	
	            String tenancyId = systemMap.get("tenent_id");
	            int storeId = Integer.parseInt(systemMap.get("store_id"));	
	            DBContextHolder.setTenancyid(tenancyId);	                
	            AutoUploadDataService autoUploadDataService = SpringConext.getApplicationContext().getBean(AutoUploadDataService.class);
	            autoUploadDataService.AutoUploadData(tenancyId, storeId);
	        } catch (Throwable e) {
	            logger.error("门店数据上传报错:", e);
	        } 
    	}
    }

}
