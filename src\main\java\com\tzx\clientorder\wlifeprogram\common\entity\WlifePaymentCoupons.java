package com.tzx.clientorder.wlifeprogram.common.entity;

import com.tzx.framework.common.util.CommonUtil;
import com.tzx.framework.common.util.DoubleHelper;

public class WlifePaymentCoupons
{
	private static final int DEFAULT_SCALE = 4;
	
	private String					id;
	private String					title;
	private Integer					money;//抵扣价
	private Integer					original_money;//券面额
	private Integer					sale_money;//售卖价格
	private WlifePaymentCouponsItem	dishItem;
	
	public String getId()
	{
		return id;
	}
	public void setId(String id)
	{
		this.id = id;
	}
	public String getTitle()
	{
		return title;
	}
	public void setTitle(String title)
	{
		this.title = title;
	}
	public Integer getMoney()
	{
		return money;
	}
	public void setMoney(Integer money)
	{
		this.money = money;
	}
	public Integer getOriginal_money()
	{
		return original_money;
	}
	public void setOriginal_money(Integer original_money)
	{
		this.original_money = original_money;
	}
	public Integer getSale_money()
	{
		return sale_money;
	}
	public void setSale_money(Integer sale_money)
	{
		this.sale_money = sale_money;
	}

	public Double getMoneyY()
	{
		Double moneyY = 0d;
		if (CommonUtil.hv(money))
		{
			moneyY = DoubleHelper.div(money.doubleValue(), 100d, DEFAULT_SCALE);
		}
		return moneyY;
	}
	public Double getOriginalMoneyY()
	{
		Double originalMoneyY = 0d;
		if (CommonUtil.hv(original_money))
		{
			originalMoneyY = DoubleHelper.div(original_money.doubleValue(), 100d, DEFAULT_SCALE);
		}
		return originalMoneyY;
	}
	public Double getSaleMoneyY()
	{
		Double saleMoneyY = 0d;
		if (CommonUtil.hv(sale_money))
		{
			saleMoneyY = DoubleHelper.div(sale_money.doubleValue(), 100d, DEFAULT_SCALE);
		}
		return saleMoneyY;
	}
	public WlifePaymentCouponsItem getDishItem()
	{
		return dishItem;
	}
	public void setDishItem(WlifePaymentCouponsItem dishItem)
	{
		this.dishItem = dishItem;
	}
}
