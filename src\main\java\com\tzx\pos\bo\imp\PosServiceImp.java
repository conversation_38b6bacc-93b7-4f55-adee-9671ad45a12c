package com.tzx.pos.bo.imp;

import com.tzx.base.bo.dto.Version;
import com.tzx.base.cache.CacheManager;
import com.tzx.base.common.util.PosPropertyUtil;
import com.tzx.base.common.util.StringUtil;
import com.tzx.base.constant.CacheTableConstant;
import com.tzx.base.service.servlet.ProcessMessage;
import com.tzx.clientorder.acewillwechat.bo.OrderService;
import com.tzx.clientorder.acewillwechat.bo.SoldOutService;
import com.tzx.clientorder.acewillwechat.bo.WshPosEntranceService;
import com.tzx.clientorder.acewillwechat.po.springjdbc.dao.OrderDao;
import com.tzx.clientorder.mtwechat.bo.MtSoldOutService;
import com.tzx.clientorder.mtwechat.po.springjdbc.dao.MtSoldOutDao;
import com.tzx.clientorder.wlifeprogram.bo.WlifeService;
import com.tzx.framework.common.constant.Code;
import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.entity.Pagination;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.*;
import com.tzx.framework.common.util.apkUtil.ApkInfo;
import com.tzx.framework.common.util.apkUtil.ApkUtil;
import com.tzx.member.crm.bo.CustomerService;
import com.tzx.orders.base.util.OrderUtil;
import com.tzx.orders.bo.OrdersManagementService;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.BasicData;
import com.tzx.pos.base.constant.PrinterSerialNumber;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.constant.WLifeConstant;
import com.tzx.pos.base.controller.CometDataNoticeClientRunnable;
import com.tzx.pos.base.controller.SellDishesDataUploadRunnable;
import com.tzx.pos.base.dao.AutoUploadDataDao;
import com.tzx.pos.base.service.SynchronizeBaseDataService;
import com.tzx.pos.base.service.impl.SynchronizeBaseDataServiceImp;
import com.tzx.pos.base.util.*;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.bo.*;
import com.tzx.pos.bo.dto.PosItemSort;
import com.tzx.pos.bo.dto.PosTableFind;
import com.tzx.pos.bo.dto.PrinterBg;
import com.tzx.pos.po.springjdbc.dao.*;
import net.sf.json.JSON;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpHeaders;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.params.CoreConnectionPNames;
import org.apache.http.params.CoreProtocolPNames;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;
import org.jsoup.helper.DataUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;

@Service(PosService.NAME)
public class PosServiceImp extends PosBaseServiceImp implements PosService {
    private final String postUrl = "/hqRest/post";
    private static final Logger logger = Logger.getLogger(PosServiceImp.class);

    @Resource(name = CustomerService.NAME)
    private CustomerService customerService;

//	@Resource(name = PaymentService.NAME)
//	private PaymentService		paymentService;

    @Resource(name = PosPrintService.NAME)
    private PosPrintService posPrintService;

    @Resource(name = PosDao.NAME)
    private PosDao posDao;

    @Resource(name = AutoUploadDataDao.NAME)
    private AutoUploadDataDao uploadDao;

    @Autowired
    OrdersManagementService ordersManagementService;
    @Autowired
    private LoginDao loginDao;

    @Resource(name = PosPrintNewService.NAME)
    private PosPrintNewService posPrintNewService;

    @Resource(name = PosDishDao.NAME)
    private PosDishDao posDishDao;
    @Autowired
    private PosPaymentDao pospaymentDao;
    @Autowired
    private PosBillDao posBillDao;
    @Resource(name = PosCodeDao.NAME)
    private PosCodeDao posCodeDao;

    @Resource(name = CloudBillDao.NAME)
    private CloudBillDao cloudBillDao;

    @Resource(name = WshPosEntranceService.NAME)
    private WshPosEntranceService wshPosEntranceService;

    @Resource(name = SoldOutService.NAME)
    private SoldOutService soldOutService;
    @Resource(name = OrderService.NAME)
    private OrderService orderService;
    @Resource(name = OrderDao.NAME)
    private OrderDao orderDao;
    @Resource(name = PosSoldOutDao.NAME)
    private PosSoldOutDao soldOutDao;
    @Resource(name = MtSoldOutDao.NAME)
    private MtSoldOutDao mtSoldOutDao;
    @Resource(name = MtSoldOutService.NAME)
    private MtSoldOutService mtSoldOutService;

    @Resource(name = WlifeService.NAME)
    private WlifeService wlifeProgramService;

    @Autowired
    private PosFinanceCheck posFinanceCheck;

    @Resource(name = PosCashierReceiveService.NAME)
    private PosCashierReceiveService cashierReceiveService;

    /**
     * @param
     * @return 返回参数：无
     * @throws Exception
     * @Description 记录版本到pos_data_version表里，用于版本控制，该方法存在内存泄露问题。
     * <AUTHOR> email:<EMAIL>
     * @version 1.0 2018-06-21
     * @see
     */
    @Override
    public void buildBasicVersion(String tenantId, Integer organId) throws Exception {
        List<Object[]> delList = new ArrayList<Object[]>();
        List<Object[]> insList = new ArrayList<Object[]>();
        String md5 = "";
        BasicData[] dataTypes = BasicData.values();
        for (BasicData basicData : dataTypes) {
            try {
                switch (basicData) {
                    case DUTY_ORDER:
                        // 班次
                        md5 = posDao.getDutyOrderByOrganIdEx(tenantId, organId);
                        break;
                    case TABLE_PROPERTY:
                        md5 = posDao.getTablePropertyByOrganIdEx(tenantId, organId);
                        break;
                    case BUSINESS_AREA:
                        md5 = posDao.getBussinessAreaEx(tenantId, organId);
                        break;
                    case TABLE:
                        md5 = posDao.getTablesByTenantIdAndOrganIdEx(tenantId, organId);
                        break;
                    case SERVICE_TYPE:
                        // 服务费种
                        md5 = posDao.getServiceFeeTypeByOrganIdEx(tenantId, organId);
                        break;
                    case ITEM_CLASS:
                        // 菜品类别
                        md5 = posDao.getItemClassByTenantIdEx(tenantId, organId);
                        break;
                    case DISH:
                        // 菜品
                        md5 = posDao.getDishByTenantIdEx(tenantId, organId);
                        break;
                    case UNIT:
                        // 规格
                        md5 = posDao.getItemUnitsByTenantIdEx(tenantId, organId);
                        break;
                    case COMBO_DETAILS:
                        // COMBO_DETAILS:
                        md5 = posDao.getItemComboDetailsByOrganIdEx(tenantId, organId);
                        break;
                    case ITEM_GROUP:
                        md5 = posDao.getItemGroupEx(tenantId, organId);
                        break;
                    case ITEM_GROUP_DETAILS:
                        // ITEM_GROUP_DETAILS:
                        md5 = posDao.getItemGroupDetailsByOrganIdEx(tenantId, organId);
                        break;
                    case TASTE:
                        // 口味备注
                        md5 = posDao.getItemTastesEx(tenantId, organId);
                        break;
                    case METHOD:
                        // 做法
                        md5 = posDao.getItemMethodsByTenantIdEx(tenantId, organId);
                        break;
                    case TIMEPRICE:
                        md5 = posDao.getTimePriceByOrganIdEx(tenantId, organId);
                        break;
                    case TIMEPRICE_ITEM:
                        // TIMEPRICE_ITEM:
                        md5 = posDao.getTimePriceItemByOrganIdEx(tenantId, organId);
                        break;
                    case REASON:
                        // 退菜、奉送、优惠、恢复账单、免单原因
                        md5 = posDao.getUnusualReasonByTenantIdEx(tenantId, organId);
                        break;
                    case DISCOUNTCASE:
                        // 折扣方案
                        md5 = posDao.getDiscountCaseByTenantIdEx(tenantId, organId);
                        break;
                    case DISCOUNT_DETAIL:
                        // 折扣方案明细
                        md5 = posDao.getDiscountCaseDetailsByTenantIdEx(tenantId, organId);
                        break;
                    case PAYMENT_WAY:
                        // 付款方式
                        md5 = posDao.getPaymentWayByOrganIdEx(tenantId, organId);
                        break;
                    case HQ_PAYMENT_SHOWTYPE:
                        // 付款方式
                        md5 = posDao.getPaymentShowtypeByOrganIdEx(tenantId, organId);
                        break;
                    case SYS_PARAMETER:
                        // SYS_PARAMETER:
                        md5 = posDao.getSysParameterEx(tenantId, organId);
                        break;
                    case CRM_INCORPORATION_INFO:
                        // CRM_INCORPORATION_INFO:
                        md5 = posDao.getCrmInCorporationsEx(tenantId, organId);
                        break;
                    case CRM_INCORPORATION_PERSON:
                        // CRM_INCORPORATION_PERSON:
                        md5 = posDao.getCrmInCorporationPersonsEx(tenantId, organId);
                        break;
                    case CRM_CARD_CLASS:
                        // 会员卡种
                        md5 = posDao.getCrmCardClassByOrganIdEx(tenantId, organId);
                        break;
                    case CRM_ITEM_VIP:
                        // 会员价
                        md5 = posDao.getCrmItemVipByOrganIdEx(tenantId, organId);
                        break;
                    case DEVICES:
                        // DEVICES:
                        md5 = posDao.getDevicesByOrganEx(tenantId, organId);
                        break;
                    case USER:
                        // USER:
                        md5 = posDao.getUsersByTenantIdEx(tenantId, organId);
                        break;
                    case USER_DISCOUNT_AUTHORITY:
                        // 人员折扣权限
                        md5 = posDao.getUserDiscountAuthorityByOrganEx(tenantId, organId);
                        break;
                    case USER_DISCOUNT_CASE:
                        // 人员折扣方案权限
                        md5 = posDao.getUserDiscountCaseByOrganEx(tenantId, organId);
                        break;
                    case BOH_IMG_PADPC:
                        // boh_img_padpc:
                        // 平板/电脑图片表
                        md5 = posDao.getBohImgByPcOrPadEx(tenantId, organId);
                        break;
                    case SYS_DICTIONARY:
                        md5 = posDao.getSysDictionaryEx(tenantId);
                        break;
                    case DINING_TYPE:
                        md5 = posDao.getDiningTypeByTenantIdEx(tenantId, organId);
                        break;
                    case HQ_PRINTER:
                        md5 = JavaMd5.toMd5B32(posDao.getHqPrinter(tenantId, organId).toString());
                        break;
                    case FORBIDDEN_ITEM:
                        md5 = JavaMd5.toMd5B32(posDao.getPosForbiddenItem(tenantId, organId).toString());
                        break;
                    case SETTING_SETMEAL_DETAILS:
                        md5 = JavaMd5.toMd5B32(posDao.getPosItemComboDetailsDefault(tenantId, organId).toString());
                        break;
                    case ROLES:
                        md5 = JavaMd5.toMd5B32(posDao.getRoles(tenantId).toString());
                        break;
                    default:
                        md5 = "";
                }
            } catch (Exception e) {
                logger.info("生成基础资料版本号失败,表名:" + basicData.name(), e);
                md5 = "";
            }

            if (!"".equals(md5)) {
                delList.add(new Object[]
                        {tenantId, organId, basicData.getType()});

                insList.add(new Object[]
                        {tenantId, organId, PropertiesLoader.getProperty(basicData.getCode()), basicData.getType(), md5});
            }
        }

        if (delList.size() > 0) {
            String delsql = "delete from pos_data_version where tenancy_id=? and store_id=? and para_code =?";
            posDao.batchUpdate(delsql, delList);

            String inssql = "insert into pos_data_version(tenancy_id,store_id,system_name,model_name,para_name,para_code,para_value,para_defaut,para_type,valid_state) values(?,?,'POS','数据同步版本',?,?,?,'1','初始化','1')";
            posDao.batchUpdate(inssql, insList);
        }

//		List<Object[]> delList = new ArrayList<Object[]>();
//		List<Object[]> insList = new ArrayList<Object[]>();
//		List<JSONObject> jsons = null;
//		BasicData[] dataTypes = BasicData.values();
//		for (BasicData basicData : dataTypes)
//		{
//			switch (basicData)
//			{
//				case DUTY_ORDER:
//					// 班次
//					jsons = getDutyOrderByOrganId(tenantId, organId);
//					break;
//
//				case TABLE_PROPERTY:
//					//
//					jsons = getTablePropertyByOrganId(tenantId, organId);
//					break;
//
//				case BUSINESS_AREA:
//					//
//					jsons = getBussinessArea(tenantId, organId);
//					break;
//
//				case TABLE:
//					//
//					jsons = getTablesByTenantIdAndOrganId(tenantId, organId);
//					break;
//
//				case SERVICE_TYPE:
//					// 服务费种
//					jsons = getServiceFeeTypeByOrganId(tenantId, organId);
//					break;
//
//				case ITEM_CLASS:
//					// 菜品类别
//					jsons = getItemClassByTenantId(tenantId, organId);
//					break;
//				case DISH:
//					// 菜品
//					jsons = (List<JSONObject>) getDishByTenantId(tenantId, organId).getData();
//					break;
//
//				case UNIT:
//					// 规格
//					jsons = (List<JSONObject>) getItemUnitsByTenantId(tenantId, organId).getData();
//					break;
//
//				case COMBO_DETAILS:
//					// COMBO_DETAILS:
//					jsons = getItemComboDetailsByOrganId(tenantId, organId);
//					break;
//
//				case ITEM_GROUP:
//					jsons = getItemGroup(tenantId, organId);
//					break;
//
//				case ITEM_GROUP_DETAILS:
//					// ITEM_GROUP_DETAILS:
//					jsons = getItemGroupDetailsByOrganId(tenantId, organId);
//					break;
//
//				case TASTE:
//					// 口味备注
//					jsons = getItemTastes(tenantId, organId);
//					break;
//
//				case METHOD:
//					// 做法
//					jsons = getItemMethodsByTenantId(tenantId, organId);
//					break;
//
//				case TIMEPRICE:
//					//
//					jsons = getTimePriceByOrganId(tenantId, organId);
//					break;
//
//				case TIMEPRICE_ITEM:
//					// TIMEPRICE_ITEM:
//					jsons = getTimePriceItemByOrganId(tenantId, organId);
//					break;
//				case REASON:
//					// 退菜、奉送、优惠、恢复账单、免单原因
//					jsons = getUnusualReasonByTenantId(tenantId, organId);
//					break;
//
//				case DISCOUNTCASE:
//					// 折扣方案
//					jsons = getDiscountCaseByTenantId(tenantId, organId);
//					break;
//
//				case DISCOUNT_DETAIL:
//					// 折扣方案明细
//					jsons = getDiscountCaseDetailsByTenantId(tenantId, organId);
//					break;
//
//				case PAYMENT_WAY:
//					// 付款方式
//					jsons = getPaymentWayByOrganId(tenantId, organId);
//					break;
//
//				case HQ_PAYMENT_SHOWTYPE:
//					// 付款方式
//					jsons = getPaymentShowtypeByOrganId(tenantId, organId);
//					break;
//
//				case SYS_PARAMETER:
//					// SYS_PARAMETER:
//					jsons = getSysParameter(tenantId, organId);
//					break;
//
//				case CRM_INCORPORATION_INFO:
//					// CRM_INCORPORATION_INFO:
//					jsons = getCrmInCorporations(tenantId, organId);
//					break;
//
//				case CRM_INCORPORATION_PERSON:
//					// CRM_INCORPORATION_PERSON:
//					jsons = getCrmInCorporationPersons(tenantId, organId);
//					break;
//
//				case CRM_CARD_CLASS:
//					// 会员卡种
//					jsons = getCrmCardClassByOrganId(tenantId, organId);
//					break;
//
//				case CRM_ITEM_VIP:
//					// 会员价
//					jsons = getCrmItemVipByOrganId(tenantId, organId);
//					break;
//
//				case DEVICES:
//					// DEVICES:
//					jsons = getDevicesByOrgan(tenantId, organId);
//					break;
//
//				case USER:
//					// USER:
//					jsons = getUsersByTenantId(tenantId, organId);
//					break;
//
//				case USER_DISCOUNT_AUTHORITY:
//					// 人员折扣权限
//					jsons = getUserDiscountAuthorityByOrgan(tenantId, organId);
//					break;
//
//				case USER_DISCOUNT_CASE:
//					// 人员折扣方案权限
//					jsons = getUserDiscountCaseByOrgan(tenantId, organId);
//					break;
//
//				case BOH_IMG_PADPC:
//					// boh_img_padpc:
//					// 平板/电脑图片表
//					jsons = getBohImgByPcOrPad(tenantId, organId);
//					break;
//
//                case SYS_DICTIONARY:
//                    jsons= getSysDictionary(tenantId);
//                    break;
//
//				default:
//					jsons = null;
//			}
//
//			if (null != jsons)
//			{
//				delList.add(new Object[]
//				{ tenantId, organId, basicData.getType() });
//
//				insList.add(new Object[]
//				{ tenantId, organId, PropertiesLoader.getProperty(basicData.getCode()), basicData.getType(), JavaMd5.toMd5B32(jsons.toString()) });
//			}
//		}
//
//		if (delList.size() > 0)
//		{
//			String delsql = "delete from pos_data_version where tenancy_id=? and store_id=? and para_code =?";
//			jdbcTemplate.batchUpdate(delsql, delList);
//
//			String inssql = "insert into pos_data_version(tenancy_id,store_id,system_name,model_name,para_name,para_code,para_value,para_defaut,para_type,valid_state) values(?,?,'POS','数据同步版本',?,?,?,'1','初始化','1')";
//			jdbcTemplate.batchUpdate(inssql, insList);
//		}
    }

    @Override
    public List<JSONObject> basicVersion(Data param) throws Exception {
        String tenantId = param.getTenancy_id();
        Integer organId = param.getStore_id();

        List<Map<String, Object>> maps = ReqDataUtil.getDataList(param);

        List<JSONObject> list = new ArrayList<JSONObject>();
        if (maps.size() > 0) {
            StringBuilder qsql = new StringBuilder("select para_value from pos_data_version where para_code = ? and store_id = ? and tenancy_id = ? ");

            for (int i = 0; i < maps.size(); i++) {
                JSONObject obj = new JSONObject();

                Map<String, Object> map = maps.get(i);
                String dataType = ParamUtil.getStringValue(map, "data_type", false, null);
                String version = ParamUtil.getStringValue(map, "version", false, null);

                String basicVersion = "";
//				String basicCode = "BASIC_VERSION_" + dataType.toUpperCase();
//				String basicCode = "BASIC_DATA_" + dataType.toUpperCase();

                String basicCode = "";
                try {
                    basicCode = BasicData.valueOf(dataType.toUpperCase()).getType();
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.error("传入的datatype不存在", e);
                }

                if (Tools.hv(basicCode) && basicCode.length() > 0) {
                    SqlRowSet rs = posDao.query4SqlRowSet(qsql.toString(), new Object[]{basicCode, organId, tenantId});
                    if (rs.next()) {
                        basicVersion = rs.getString("para_value");
                    }
                }
                obj.element("type", dataType);
                if (StringUtils.isEmpty(version)) {
                    obj.element("isnewest", false);
                    obj.element("version", basicVersion);
                } else if (version.equalsIgnoreCase(basicVersion)) {
                    obj.element("isnewest", true);
                    obj.element("version", version);
                } else {
                    obj.element("isnewest", false);
                    obj.element("version", basicVersion);
                }
                list.add(obj);
            }
        }
        return list;
    }

    @Override
    public void syncBaseData(Data param, Data result) throws SystemException {
        Data data = new Data();

        List<JSONObject> jsons = null;

        String tenantId = param.getTenancy_id();
        Integer organId = param.getStore_id();

        Map<String, Object> map = ReqDataUtil.getDataMap(param);

        String channel = ParamUtil.getStringValue(map, "channel", true, PosErrorCode.NOT_NULL_CHANNEL).toUpperCase();
        String dataType = ParamUtil.getStringValue(map, "data_type", true, PosErrorCode.NOT_NULL_DATA_TYPE);
        String posNum = ParamUtil.getStringValue(map, "pos_num", false, null);

        try {
            BasicData type = null;
            try {
                type = BasicData.valueOf(dataType.toUpperCase());
            } catch (Exception e) {
                e.printStackTrace();
                throw new SystemException(PosErrorCode.NOT_EXISTS_SYNC_DATATYPE);
            }

            switch (type) {
                case DUTY_ORDER:
                    // 班次
                    jsons = posDao.getDutyOrderByOrganId(tenantId, organId);
                    break;
                case TABLE_PROPERTY:
                    //
                    jsons = posDao.getTablePropertyByOrganId(tenantId, organId);
                    break;
                case BUSINESS_AREA:
                    //
                    jsons = posDao.getBussinessArea(tenantId, organId);
                    break;
                case TABLE:
                    jsons = posDao.getTablesByTenantIdAndOrganId(tenantId, organId);
                    break;
                case SERVICE_TYPE:
                    // 服务费种
                    jsons = posDao.getServiceFeeTypeByOrganId(tenantId, organId);
                    break;
                case ITEM_CLASS:
                    // 菜品类别
                    jsons = posDao.getItemClassByTenantId(tenantId, organId, channel);
                    break;
                case DISH:
                    // 菜品
                    data = posDao.getDishByTenantId(tenantId, organId, channel, param.getPagination());
                    param.setPagination(data.getPagination());
                    break;
                case DISH_FORMAT:
                    // 菜品格式
                    jsons = posDao.getDishFormat(tenantId, organId, channel);
                    break;
                case UNIT:
                    // 规格
                    data = posDao.getItemUnitsByTenantId(tenantId, organId, channel, param.getPagination());
                    param.setPagination(data.getPagination());
                    break;
                case COMBO_DETAILS:
                    jsons = posDao.getItemComboDetailsByOrganId(tenantId, organId, channel);
                    break;
                case ITEM_GROUP:
                    jsons = posDao.getItemGroup(tenantId, organId);
                    break;
                case ITEM_GROUP_DETAILS:
                    jsons = posDao.getItemGroupDetailsByOrganId(tenantId, organId, channel);
                    break;
                case TIMEPRICE:
                    jsons = posDao.getTimePriceByOrganId(tenantId, organId, channel);
                    break;
                case TIMEPRICE_ITEM:
                    jsons = posDao.getTimePriceItemByOrganId(tenantId, organId, channel);
                    break;
                case TASTE:
                    // 口味备注
                    jsons = posDao.getItemTastes(tenantId, organId);
                    break;
                case METHOD:
                    // 做法
                    jsons = posDao.getItemMethodsByTenantId(tenantId, organId);
                    break;
                case REASON:
                    // 退菜、奉送、优惠、恢复账单、免单原因
                    jsons = posDao.getUnusualReasonByTenantId(tenantId, organId);
                    break;
                case DISCOUNTCASE:
                    // 折扣方案
                    jsons = posDao.getDiscountCaseByTenantId(tenantId, organId);
                    break;
                case DISCOUNT_DETAIL:
                    // 折扣方案明细
                    data = posDao.getDiscountCaseDetailsByTenantId(tenantId, organId,param.getPagination());
                    param.setPagination(data.getPagination());
                    break;
                case PAYMENT_WAY:
                    // 付款方式
                    if (Tools.hv(posNum)) {
                        //根据机台判断是否显示付款方式
                        jsons = posDao.getPaymentWayByOrganId(tenantId, organId, posNum);
                    } else {
                        jsons = posDao.getPaymentWayByOrganId(tenantId, organId);
                    }
                    break;
                case USER:
                    jsons = posDao.getUsersByTenantId(tenantId, organId);
                    break;
                case SYS_PARAMETER:
                    jsons = posDao.getSysParameter(tenantId, organId);
                    break;
                case CRM_INCORPORATION_INFO:
                    jsons = posDao.getCrmInCorporations(tenantId, organId);
                    break;
                case CRM_INCORPORATION_PERSON:
                    jsons = posDao.getCrmInCorporationPersons(tenantId, organId);
                    break;
                case DEVICES:
                    jsons = posDao.getDevicesByOrgan(tenantId, organId);
                    break;
                case CRM_CARD_CLASS:
                    jsons = posDao.getCrmCardClassByOrganId(tenantId, organId);
                    break;
                case USER_DISCOUNT_AUTHORITY:
                    jsons = posDao.getUserDiscountAuthorityByOrgan(tenantId, organId);
                    break;
                case USER_DISCOUNT_CASE:
                    jsons = posDao.getUserDiscountCaseByOrgan(tenantId, organId);
                    break;
                case HQ_PAYMENT_SHOWTYPE:
                    jsons = posDao.getPaymentShowtypeByOrganId(tenantId, organId);
                    break;
//				case "cc_third_organ_info":
//					jsons = posDao.getCcThirdOrganInfoByOrganId(tenantId, organId);
//					break;
                case BOH_IMG_PADPC:
                    jsons = posDao.getBohImgByPcOrPad(tenantId, organId);
                    break;
                case SOLDOUT:
                    //沽清
                    jsons = posDao.getItemSoldout(tenantId, organId);
                    break;
                case WORRYSALE:
                    //急推
                    jsons = posDao.getItemWorrysale(tenantId, organId);
                    break;
                case CRM_ACTIVITY_RULE://同步活动规则数据
                    jsons = posDao.getActivityRuleDetails(tenantId);
                    break;
                case CRM_ACTIVITY_ITEM://同步活动商品规则数据
                    jsons = posDao.getCrmActivityItem(tenantId);
                    break;
                case SYS_DICTIONARY:
                    jsons = posDao.getSysDictionary(tenantId);
                    break;
                case ANDROID_SYS_DICTIONARY:
                    jsons = posDao.getAndroidSysDictionary(tenantId);
                    break;
                case MENU_BUTTON_CONFIG:
                    jsons = posDao.getMenuButtonConfig(tenantId, organId);
                    break;
                case PAYMENTWAY_BUTTON_CONFIG:
                    jsons = posDao.getPaymentWayButtonConfig(tenantId, organId);
                    break;
                case DINING_TYPE:
                    jsons = posDao.getDiningType(tenantId, organId);
                    break;
                case ITEM_TASTE:
                    jsons = posDao.getHqItemTasteDetails(tenantId, organId);
                    break;
                case HQ_PRINTER:
                    jsons = posDao.getHqPrinter(tenantId, organId);
                    break;
                case FORBIDDEN_ITEM:
                    jsons = posDao.getPosForbiddenItem(tenantId, organId);
                    break;
                case SETTING_SETMEAL_DETAILS:
                    jsons = posDao.getPosItemComboDetailsDefault(tenantId, organId);
                    break;
                case ROLES:
                    jsons = posDao.getRoles(tenantId);
                    break;
                default:
                    throw new SystemException(PosErrorCode.NOT_EXISTS_SYNC_DATATYPE);
            }
            result.setCode(Constant.CODE_SUCCESS);
            result.setMsg(Constant.SYNC_DATA_SUCCESS);
            if (data.getData() != null) {
                result.setData(data.getData());
            } else {
                result.setData(jsons);
            }
        } catch (SystemException se) {
            throw se;
        } catch (Exception e) {
            logger.info("sync:" + ExceptionMessage.getExceptionMessage(e));
            result.setCode(Constant.CODE_INNER_EXCEPTION);
            result.setMsg(Constant.SYNC_DATA_FAILURE);
            e.printStackTrace();
        }
    }

    @Override
    public void systemConfig(Data param, Data result) throws SystemException {
        try {
            String tenantId = param.getTenancy_id();
            Integer organId = param.getStore_id();

            Map<String, Object> map = ReqDataUtil.getDataMap(param);

            // String posNum = ParamUtil.getStringValue(map, "pos_num", false,
            // null);
            String tableCode = ParamUtil.getStringValue(map, "table_code", false, null);
            String mac = ParamUtil.getStringValue(map, "mac", false, null);
            String optNum = ParamUtil.getStringValue(map, "opt_num", false, null);

            // 这个表暂时还不存在
            StringBuilder check = new StringBuilder("select count(*) from user_authority as ua where ua.tenancy_id = '");
            check.append(tenantId + "' and ua.store_id = '" + organId + "'");
            int count = 1; // this.jdbcTemplate.queryForInt(check.toString());

            if (count == 0) {
                throw new SystemException(PosErrorCode.NOT_EXISTS_TENANT);
            }

            posDao.systemConfig(mac, organId, tableCode);

            StringBuilder sbSql = new StringBuilder();

            if (StringUtils.isNotEmpty(optNum)) {
                sbSql.append("select ua.employee_id from user_authority as ua where ua.tenancy_id = ? and ua.pos_user_name = ? and ua.store_id = ? ");
                SqlRowSet rs = posDao.query4SqlRowSet(sbSql.toString(), new Object[]
                        {tenantId, optNum, organId});

                String employeeId = "";
                if (rs.next()) {
                    employeeId = rs.getString("employee_id");
                } else {
                    throw new SystemException(PosErrorCode.NOT_EXISTS_USER);
                }

                List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
                Map<String, Object> map0 = new HashMap<String, Object>();
                map0.put("employee_id", employeeId);
                list.add(map0);
                result.setData(list);
            }
        } catch (SystemException se) {
            throw se;
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("系统设置：" + ExceptionMessage.getExceptionMessage(e));
            throw new SystemException(PosErrorCode.OPER_ERROR);
        }
    }

    @Override
    public List<PosTableFind> findTable(Data param) throws SystemException {
        String tenantId = param.getTenancy_id();
        Integer organId = param.getStore_id();

        Map<String, Object> map = ReqDataUtil.getDataMap(param); // 开台传入的参数
        String tableNo = ParamUtil.getStringValue(map, "table_code", false, null);
        Integer tablePropertyId = ParamUtil.getIntegerValue(map, "table_property_id", false, null);
        String tableState = ParamUtil.getStringValue(map, "tablestate", false, null);
        Integer businessAreaId = ParamUtil.getIntegerValue(map, "business_area_id", false, null);

        List<PosTableFind> tableList = posDao.findTable(tenantId, organId, tableNo, tablePropertyId, tableState, businessAreaId);

        List<Object[]> batchArgs = new ArrayList<Object[]>();
        for (PosTableFind table : tableList) {
            if (SysDictionary.TABLE_STATE_BUSY.equals(table.getState()) && Tools.isNullOrEmpty(table.getBillno())) {
                table.setState(SysDictionary.TABLE_STATE_FREE);
                batchArgs.add(new Object[]{SysDictionary.TABLE_STATE_FREE, table.getTable_code(), organId, tenantId});
            } else if (Tools.hv(table.getBillno()) && false == SysDictionary.TABLE_STATE_BUSY.equals(table.getState())) {
                table.setState(SysDictionary.TABLE_STATE_BUSY);
                batchArgs.add(new Object[]{SysDictionary.TABLE_STATE_BUSY, table.getTable_code(), organId, tenantId});
            }

            if(SysDictionary.TABLE_STATE_BUSY.equals(table.getState())){
                table.setLocked_time(null);
                //是否锁台 is_locked  0:未锁台 1：锁台
                table.setIs_locked("0");
            }else{
                if(Tools.hv(table.getLock_opt_num()) && Tools.hv(table.getLock_pos_num())){
                    //是否锁台 is_locked  0:未锁台 1：锁台
                    table.setIs_locked("1");
                }else{
                    table.setLocked_time(null);
                    //是否锁台 is_locked  0:未锁台 1：锁台
                    table.setIs_locked("0");
                }
            }

            table.setCombine_table_name("");
            table.setCombine_table_bill_num("");

            try{
                if(Tools.hv(table.getBillno())){

                    JSONObject billJson = posDishDao.getPosBillByBillnum(tenantId, organId, table.getBillno());//根据账单号查询账单
                    if (null != billJson)
                    {
                        String sbill_num  = billJson.optString("sbill_num"); //是否存在原账单号
                        if(Tools.hv(sbill_num)){//存在  说明当前桌台为目标桌台
                            String table_name = posDao.getTableNameByTableCode(tenantId, organId, sbill_num);
                            if(!"".equals(table_name)){
                                table.setCombine_table_name(table_name+""+table.getTable_name());
                            }
                            //存在账单  是否并台 是 1
                            table.setIs_combine_table("1");
                            table.setCombine_table_bill_num(table.getBillno());


                            List<JSONObject> dish = posDao.getPosBillItemByBillNum(tenantId, organId, table.getBillno());
                            if(dish.isEmpty()){
                                //账单是否存在菜品  0：不存在 1：存在
                                table.setIs_exist_dish("0");
                            }else{
                                //账单是否存在菜品  0：不存在 1：存在
                                table.setIs_exist_dish("1");
                            }


                        }else{//不存在  说明当前桌台为原桌台 或者没有并台桌台
                            //根据 sbill_num 查询 pos_bill 是否存在并台账单
                             billJson = posDishDao.getPosBillBySBillnum(tenantId, organId, table.getBillno());//目标账单
                             if(billJson.isEmpty()){//没有并台桌台
                                 //存在账单  是否并台 否
                                 table.setIs_combine_table("0");

                                 List<JSONObject> dish = posDao.getPosBillItemByBillNum(tenantId, organId, table.getBillno());
                                 if(dish.isEmpty()){
                                     //账单是否存在菜品  0：不存在 1：存在
                                     table.setIs_exist_dish("0");
                                 }else{
                                     //账单是否存在菜品  0：不存在 1：存在
                                     table.setIs_exist_dish("1");
                                 }

                             }else{
                                 String guest = billJson.optString("guest");
                                 String amt = billJson.optString("payment_amount");
                                 String bill_num = billJson.optString("bill_num");
                                 sbill_num  = billJson.optString("sbill_num"); //是否存在原账单号
                                 table.setAmt(amt);
                                 table.setGuest(guest);
                                 //table.setSeat_counts();

                                 String table_name = posDao.getTableNameByTableCode(tenantId, organId, sbill_num);
                                 String rtable_name = posDao.getTableNameByTableCode(tenantId, organId, bill_num);
                                 if(!"".equals(table_name)){
                                     table.setCombine_table_name(table_name+""+rtable_name);
                                 }

                                 List<JSONObject> dish = posDao.getPosBillItemByBillNum(tenantId, organId, bill_num);
                                 if(dish.isEmpty()){
                                     //账单是否存在菜品  0：不存在 1：存在
                                     table.setIs_exist_dish("0");
                                 }else{
                                     //账单是否存在菜品  0：不存在 1：存在
                                     table.setIs_exist_dish("1");
                                 }

                                 //存在账单  是否并台 是
                                 table.setIs_combine_table("1");
                                 table.setCombine_table_bill_num(bill_num);
                             }
                        }
                    }
                }
            }catch (Exception e){
                e.printStackTrace();
            }
        }
        try {
            if (batchArgs.size() > 0) {
                StringBuffer updateTableState = new StringBuffer("update pos_tablestate set state = ? where table_code = ? and store_id = ? and tenancy_id = ?");
                posDao.batchUpdate(updateTableState.toString(), batchArgs);
            }
        } catch (Exception e) {
            logger.error("修改桌位状态错误", e);
            e.printStackTrace();
        }
        return tableList;
    }




    @SuppressWarnings("unchecked")
    @Override
    public synchronized void lockOrUnlockTable(Data param, Data result) throws SystemException {
        try {
            String tenantId = param.getTenancy_id();
            Integer organId = param.getStore_id();

            Map<String, Object> map = ReqDataUtil.getDataMap(param);

            String posNum = ParamUtil.getStringValue(map, "pos_num", true, PosErrorCode.NOT_NULL_POS_NUM);// 机号

            String optNum = ParamUtil.getStringValue(map, "opt_num", true, PosErrorCode.NOT_NULL_OPT_NUM);// 操作员编号

            Date reportDate = ParamUtil.getDateValue(map, "report_date", false, null);
            String nowTimeStr = ParamUtil.getStringValue(map, "nowTime", false, null);
            Date nowTime = new Date();
            if (nowTimeStr != null && !Tools.hv(nowTimeStr)) {
                nowTime = DateUtil.parseDateAll(nowTimeStr);
            }

            Integer shiftId = ParamUtil.getIntegerValue(map, "shift_id", false, null);//
            String chanel = ParamUtil.getStringValue(map, "chanel", false, null);
            String mode = ParamUtil.getStringValue(map, "mode", true, PosErrorCode.NOT_NULL_MODE);

            List<Map<String, Object>> tables = (List<Map<String, Object>>) map.get("tables");
            if (Tools.isNullOrEmpty(tables) == true) {
                throw new SystemException(PosErrorCode.NOT_NULL_TABLES);
            }

            if (tables.size() > 0) {
                List<JSONObject>  data = new ArrayList();
                for (int i = 0; i < tables.size(); i++) {
                    String tableCode = ParamUtil.getStringValue(tables.get(i), "table_code", false, null);

                    String ptcount = new String("select state,lock_opt_num,lock_pos_num from pos_tablestate where table_code = ? and store_id = ? and tenancy_id = ?");
                    SqlRowSet rst = posDao.query4SqlRowSet(ptcount, new Object[]{tableCode, organId, tenantId});

                    String ptState = null;
                    String lockOptNum = null;
                    String lockPosNum = null;
                    if (rst.next()) {
                        ptState = rst.getString("state");
                        lockOptNum = rst.getString("lock_opt_num");
                        lockPosNum = rst.getString("lock_pos_num");
                    } else {
                        throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_TABLE_CODE);
                    }

                    if (StringUtils.isEmpty(ptState)) {
                        Timestamp tt = DateUtil.limit6Timestamp();
                        String ipt = new String("insert into pos_tablestate (tenancy_id,store_id,table_code,state,opt_name,last_updatetime) values (?,?,?,?,?,?) ");
                        String optName = "";
                        if (!"WX01".equals(posNum)) {
                            optName = posDao.getEmpNameById(optNum, tenantId, organId);
                        } else if ("WX01".equals(posNum)) {//设置锁单服务员名字
                            List<JSONObject> listTmp = this.getOptStateInfo(tenantId, organId, DateUtil.getNowDateYYDDMM());
                            if (listTmp != null && listTmp.size() > 0) {
                                JSONObject jsonObject = listTmp.get(0);
                                if (!Tools.isNullOrEmpty(jsonObject)) {
                                    String optnumTmp = jsonObject.optString("opt_num", "");
                                    optName = posDao.getEmpNameById(optnumTmp, tenantId, organId);
                                }
                            }
                            if (!Tools.hv(optName)) {
                                optName = "WX01";
                            }
                        }
                        posDao.update(ipt, new Object[]{tenantId, organId, tableCode, SysDictionary.TABLE_STATE_FREE, optName, tt});
                    }


//					if(!"WX01".equals(posNum)) {
//						if (mode.equals("0") && (Tools.hv(lockOptNum) || Tools.hv(lockPosNum)) && (!posNum.equals(lockPosNum) || !optNum.equals(lockOptNum)))
//						{
//							String optName = posDao.getEmpNameById(lockOptNum, tenantId, organId);
//							throw SystemException.getInstance(PosErrorCode.TABLE_ALREADY_LOCK_ERROR).set("{0}", optName).set("{1}", lockPosNum);
//						}
//					}
//					posDao.lockOrUnlockTable(tenantId, organId, optNum, posNum, tableCode, mode, result);
//					String bill_num=this.cloudBillDao.findBillNum(tenantId,organId,tableCode);
//					String chanel = ParamUtil.getStringValue(map, "chanel", false, null);
//					if(!Tools.hv(chanel)){
//						chanel=SysDictionary.CHANEL_MD01;
//					}

                    if (mode.equals("0"))//锁定桌位
                    {
                        if (!"WX01".equals(posNum) && ((Tools.hv(lockOptNum) && !optNum.equals(lockOptNum)) || (Tools.hv(lockPosNum) && !posNum.equals(lockPosNum)))) {
                            String optName = posDao.getEmpNameById(lockOptNum, tenantId, organId);
                            throw SystemException.getInstance(PosErrorCode.TABLE_ALREADY_LOCK_ERROR).set("{0}", optName).set("{1}", lockPosNum);
                        }

                        // 修改桌位状态
                        posDao.lockOrUnlockTable(tenantId, organId, optNum, posNum, tableCode, mode, result);
                        String bill_num = this.cloudBillDao.findBillNum(tenantId, organId, tableCode);
//						String chanel = ParamUtil.getStringValue(map, "chanel", false, null);
                        if (!Tools.hv(chanel)) {
                            chanel = SysDictionary.CHANEL_MD01;
                        }

                        if (Tools.hv(bill_num)) {// 云账单强制加锁
                            String open_id = ParamUtil.getStringValue(map, "open_id", false, null);//
                            Integer customer_id = ParamUtil.getIntegerValue(map, "customer_id", false, null);//

                            JSONObject billJson = posDishDao.getPosBillByBillnum(tenantId, organId, bill_num);
                            if(null!=billJson){
                                String sbill_num = billJson.optString("sbill_num");
                                if (Tools.hv(sbill_num)) {//并台
                                    this.combineLockOrUnlockTable( billJson, tenantId, organId, optNum, posNum, mode,  result);
                                }else{
                                    JSONObject  billJson2 = posDishDao.getPosBillBySBillnum(tenantId, organId, bill_num);
                                    if(null!=billJson2 && !billJson2.isEmpty()){
                                        tableCode = billJson2.getString("table_code");
                                        posDao.lockOrUnlockTable(tenantId, organId, optNum, posNum, tableCode, mode, result);

                                        this.combineLockOrUnlockTable( billJson2, tenantId, organId, optNum, posNum, mode,  result);
                                    }
                                }
                            }

                            JSONObject json = new JSONObject();
                            json.put("table_code",tableCode);
                            json.put("is_locked","1");
                            json.put("lock_opt_num",lockOptNum)  ;
                            json.put("locked_time",DateUtil.getNowDateYYDDMMHHMMSS());
                            try {
                                List<JSONObject> dish = posDao.getPosBillItemByBillNum(tenantId, organId, bill_num);
                                if (dish.isEmpty()) {
                                    //账单是否存在菜品  0：不存在 1：存在
                                    json.put("is_exist_dish", "0");
                                } else {
                                    //账单是否存在菜品  0：不存在 1：存在
                                    json.put("is_exist_dish", "1");
                                }
                            }catch (Exception e){
                                logger.error("查账单菜品是否存在失败", e);
                            }
                            data.add(json);



                            try {
                                if (open_id == null || open_id.isEmpty()) {
                                    open_id = "MD01";
                                }
                                if (customer_id == null) {
                                    customer_id = 0;
                                }
                                this.cloudBillDao.forcedUnlocking(bill_num, tenantId, organId, nowTime, chanel, 1, open_id, customer_id);
                            } catch (Exception e) {
                                logger.error("云账单强制加锁失败", e);
                            }
                        }
                        if (!"WX01".equals(posNum)) {
                            posDao.savePosLog(tenantId, organId, posNum, optNum, optNum, shiftId, reportDate, Constant.TITLE, "锁定桌位", "锁定桌位编号：" + tableCode, "");
                        }
                        // 门店锁单，也会上传订单，订单为锁单状态
                        // 查询是否有order_num平台订单号
                        JSONObject billObject = orderDao.getBillInfo(tenantId, tableCode, organId);
                        if (null != billObject && StringUtil.hasText(billObject.optString("out_order_id"))) {
                            logger.info("门店锁单，调用上传订单接口>>>>");
                            // 上传订单
                            orderService.getOrderInfo(tenantId, organId, tableCode, WLifeConstant.OPT_TYPE_LOCK);
                        } else {
                            logger.info("非微生活订单");
                        }
                    } else if (mode.equals("1"))//解锁桌位
                    {
                        // 修改桌位状态
                        posDao.lockOrUnlockTable(tenantId, organId, optNum, posNum, tableCode, mode, result);

                        String bill_num = this.cloudBillDao.findBillNum(tenantId, organId, tableCode);
//						String chanel = ParamUtil.getStringValue(map, "chanel", false, null);
                        if (!Tools.hv(chanel)) {
                            chanel = SysDictionary.CHANEL_MD01;
                        }

                        if (Tools.hv(bill_num)) {// 云账单强制解锁

                            JSONObject billJson = posDishDao.getPosBillByBillnum(tenantId, organId, bill_num);
                            if(null!=billJson){
                                String sbill_num = billJson.optString("sbill_num");
                                if (Tools.hv(sbill_num)) {//并台
                                    this.combineLockOrUnlockTable( billJson, tenantId, organId, optNum, posNum, mode,  result);
                                }else{
                                    JSONObject  billJson2 = posDishDao.getPosBillBySBillnum(tenantId, organId, bill_num);

                                    if(null!=billJson2 && !billJson2.isEmpty()){
                                        tableCode = billJson2.getString("table_code");
                                        posDao.lockOrUnlockTable(tenantId, organId, optNum, posNum, tableCode, mode, result);
                                        this.combineLockOrUnlockTable( billJson2, tenantId, organId, optNum, posNum, mode,  result);
                                    }
                                }
                            }


                            JSONObject json = new JSONObject();
                            json.put("table_code",tableCode);
                            json.put("is_locked","0");
                            json.put("lock_opt_num","")  ;
                            json.put("locked_time","");
                            try {
                                List<JSONObject> dish = posDao.getPosBillItemByBillNum(tenantId, organId, bill_num);
                                if (dish.isEmpty()) {
                                    //账单是否存在菜品  0：不存在 1：存在
                                    json.put("is_exist_dish", "0");
                                } else {
                                    //账单是否存在菜品  0：不存在 1：存在
                                    json.put("is_exist_dish", "1");
                                }
                            }catch (Exception e){
                                logger.error("查账单菜品是否存在失败", e);
                            }
                            data.add(json);


                            try {
                                this.cloudBillDao.forcedUnlocking(bill_num, tenantId, organId, nowTime, chanel, 0, "MD01", 0);
                            } catch (Exception e) {
                                logger.error("云账单强制解锁失败", e);
                            }
                        }
                        if (!"WX01".equals(posNum)) {
                            String content = ((Tools.hv(lockOptNum) && !optNum.equals(lockOptNum)) || (Tools.hv(lockPosNum) && !posNum.equals(lockPosNum))) ? "强制解锁桌位" : "解锁桌位";
                            posDao.savePosLog(tenantId, organId, posNum, optNum, optNum, shiftId, reportDate, Constant.TITLE, content, "解锁桌位编号：" + tableCode, "");
                        }
                        // 解锁时若判定为微生活锁定账单，则通知微生活接口
                        // 通知失败就不管了
                        try {
                            JSONObject notifyWshUnlockOrder = wshPosEntranceService.notifyWshUnlockOrder(tenantId, organId, tableCode);
                            logger.info("notifyWshUnlockOrder result:" + notifyWshUnlockOrder);

                            wlifeProgramService.unlockOrder(tenantId, organId, bill_num);
                        } catch (Exception e) {
                            e.printStackTrace();
                            logger.info("微生活解锁失败:", e);
                        }
                    }
                }
                result.setData(data);
            } else {
                throw new SystemException(PosErrorCode.NOT_NULL_TABLES);
            }
        } catch (SystemException se) {
            throw se;
        } catch (Exception e) {
            e.printStackTrace();
            logger.info(ExceptionMessage.getExceptionMessage(e));
            throw SystemException.getInstance(PosErrorCode.OPER_ERROR);
        }
    }

    @Override
    public synchronized void dayReport(Data param, Data result) throws SystemException, Exception {
        String tenantId = param.getTenancy_id();
        Integer organId = param.getStore_id();

        Map<String, Object> map = ReqDataUtil.getDataMap(param);
        String posNum = ParamUtil.getStringValue(map, "pos_num", false, null);
        Date reportDate = ParamUtil.getDateValue(map, "report_date", false, null);//报表日期
        //同步未到店订单数据
        syncMissOrder(tenantId, organId, reportDate);

        /**账务基础数据平衡 检查**/
        posDao.checkFinanceBasedata(tenantId, organId, map);

        /*
         * 外卖数据门店端与总部外卖平台之间数据验证。
         * 门店账单总应收（payment_amount）与总部账单总应收 ( localserver传值v=0 cc.shopfee； v=1 cc.shop_real_amount) 进行验证。
         */
        ordersManagementService.analysisOrderAmount(tenantId, organId, new SimpleDateFormat("yyyy-MM-dd").format(reportDate));

        //校验交款
        if (false == cashierReceiveService.checkCashierReceive(tenantId, organId, reportDate)) {
            throw new SystemException(PosErrorCode.DAYBEGAIN_RECEIVE_ADD_FAILURE);
        }

        //校验打烊
        posDao.checkDayEnd(reportDate, posNum, null, organId);

        //打烊
        posDao.dayReport(tenantId, organId, map, result);

        //上传报表平衡性检查结果
        JSONObject checkArg = new JSONObject();
        checkArg.put("report_date", new SimpleDateFormat("yyyy-MM-dd").format(reportDate));
        posFinanceCheck.uploadFinanceCheckResult(tenantId, organId, checkArg);

        //
        if (Constant.CODE_SUCCESS == result.getCode()) {
            try {
                posDao.updateKvsMopState(tenantId, organId, posNum, DateUtil.currentTimestamp());
            } catch (Exception e) {
                logger.info(ExceptionMessage.getExceptionMessage(e));
                e.printStackTrace();
            }
        }

        JSONObject json = new JSONObject();
        json.put("pos_num", posNum);

        List<JSONObject> list = new ArrayList<JSONObject>();
        list.add(json);

        Data cjData = new Data();
        cjData.setType(Type.DAY_END);
        cjData.setOper(Oper.add);
        cjData.setData(list);

        String message = JSONObject.fromObject(cjData).toString();
        Comet4jUtil.sendMessage2All(Comet4jUtil.POS_ANDROID, message);
        Comet4jUtil.sendMessage2All(Comet4jUtil.POS_PC, message);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void syncMissOrder(String tenantId, Integer organId, Date reportDate) {
        try {
            JSONObject jo = OrderUtil.validOrderData(tenantId, organId, new SimpleDateFormat("yyyy-MM-dd").format(reportDate), true);
            if (null != jo && "0".equals(jo.optString("error"))) {
                JSONArray missOrderData = jo.getJSONArray("missOrderData");
                ProcessMessage processMessage = new ProcessMessage();
                int count = 0;
                for (Object o : missOrderData) {
                    count++;
                    if (count < 20) {
                        JSONObject e = JSONObject.fromObject(o);
                        e.put("is_after_change_shift", true);
                        processMessage.handleMsg(tenantId, organId, e);
                    } else {
                        break;
                    }
                }
            } else {
                throw new Exception("与总部同步及处理订单数据时发生错误：" + jo);
            }
        } catch (Exception e) {
            logger.error(e);
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public Data dataTrans(Data param) throws SystemException {
        // String tenantId = param.getTenancy_id();
        // Integer organId = param.getStore_id();
        //
        // Map<String, Object> map = ReqDataUtil.getDataMap(param);
        //
        // Date reportDate = ParamUtil.getDateValue(map, "report_date", true,
        // PosErrorCode.NOT_NULL_REPORT_DATE);
        // Integer shiftId = ParamUtil.getIntegerValue(map, "shift_id", false,
        // null);
        // String posNum = ParamUtil.getStringValue(map, "pos_num", false,
        // null);
        // String optNum = ParamUtil.getStringValue(map, "opt_num", false,
        // null);
        //
        // System.out.println(shiftId + "" + posNum + "" + optNum);
        //
        // return posDao.dataTrans(tenantId, organId, reportDate);

        Data reData = Data.get();
        reData.setSuccess(true);
        try {
            Map<String, Object> map = ReqDataUtil.getDataMap(param);
            Date reportDate = ParamUtil.getDateValue(map, "report_date", true, PosErrorCode.NOT_NULL_REPORT_DATE);

            String tenancyId = param.getTenancy_id();
            int storeId = param.getStore_id();

            JSONObject statePara = JSONObject.fromObject("{}");
            statePara.element("report_date", DateUtil.format(reportDate));
            statePara.element("content", "DAYEND");
            statePara.element("tenancy_id", tenancyId);
            statePara.element("store_id", storeId);
            //
//			List<JSONObject> optList = uploadDao.getPosOptState(tenancyId, storeId, statePara);
//			if (optList == null || optList.size() <= 0)
//			{
//				reData.setCode(Constant.CODE_STORE_EXCEPTION);
//				reData.setMsg(Constant.NOT_DAY_END_YET);
//				reData.setSuccess(false);
//				return reData;
//			}

            JSONObject daycountPara = JSONObject.fromObject("{}");
            daycountPara.element("day_count", DateUtil.format(reportDate));
            logger.info("上传数据：<上传报表日期:" + DateUtil.format(reportDate) + ">");

            int dayCount = this.checkDaycount(tenancyId, storeId, daycountPara);
            if (0 != dayCount) {
                if (1 == dayCount) {
                    reData.setMsg(Constant.ALREADY_DAYCOUNT);
                } else if (100 == dayCount) {
                    reData.setMsg(Constant.ALREADY_DAYCOUNTING);
                }
                reData.setCode(Constant.CODE_STORE_EXCEPTION);
                reData.setSuccess(false);
                return reData;
            }

            /* 查询门店数据 */
            String[] tableNames = uploadDao.getSysParameter(tenancyId, storeId, "uploaddatatable").split(",");

            JSONObject dataJson = new JSONObject();
            dataJson.put("report_date", DateUtil.format(reportDate));

            JSONObject itemPara = JSONObject.fromObject("{}");
            itemPara.put("report_date", DateUtil.format(reportDate));
            boolean isSend = false;
            for (String tableName : tableNames) {
                List<JSONObject> jsonList = uploadDao.getPosUploadDataItem(tenancyId, storeId, tableName, itemPara);
                if (null != jsonList && jsonList.size() > 0) {
                    isSend = true;
                    dataJson.put(tableName, jsonList);
                    logger.info("上传数据：<上传表名:" + tableName + ";上传数据" + jsonList.size() + "条>");
                }
            }

            List<JSONObject> dataList = new ArrayList<JSONObject>();
            dataList.add(dataJson);

            Data data = Data.get();
            data.setType(Type.DATATRANS);
            data.setOper(Oper.upload);
            data.setTenancy_id(tenancyId);
            data.setStore_id(Integer.valueOf(storeId));
            data.setData(dataList);

            /* 发送到MQ */
            int rs = 0;
            if (isSend) {
                MessageUtils mu = new MessageUtils();
                rs = mu.sendMessage(JSONObject.fromObject(data).toString());
            } else {
                reData.setCode(Constant.CODE_INNER_EXCEPTION);
                reData.setMsg(Constant.UPLOAD_DATA_FAILURE);
                reData.setSuccess(false);
            }

            if (rs == 1) {
                /* 发送成功修改上传状态 */
                Iterator<String> it = dataJson.keys();
                while (it.hasNext()) {
                    String tableName = it.next();
                    if (!"report_date".equals(tableName)) {
                        uploadDao.updatePosItemUploadTag(tenancyId, storeId, tableName, dataJson.optJSONArray(tableName), null);
                    }
                }

                reData.setCode(Constant.CODE_SUCCESS);
                reData.setMsg(Constant.UPLOAD_DATA_SUCCESS);
            } else {
                reData.setCode(Constant.CODE_INNER_EXCEPTION);
                reData.setMsg(Constant.UPLOAD_DATA_FAILURE);
                reData.setSuccess(false);
            }

            return reData;
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("上传数据失败，原因：" + ExceptionMessage.getExceptionMessage(e));
            reData.setCode(Constant.CODE_INNER_EXCEPTION);
            reData.setMsg(Constant.UPLOAD_DATA_FAILURE);
            reData.setSuccess(false);
            return reData;
        }
    }

    @Override
    public void findWaiter(Data param, Data result) throws SystemException {
        String tenantId = param.getTenancy_id();
        Integer organId = param.getStore_id();

        Map<String, Object> map = ReqDataUtil.getDataMap(param);

        Date reportDate = ParamUtil.getDateValue(map, "report_date", false, null);
        Integer shiftId = ParamUtil.getIntegerValue(map, "shift_id", false, null);
        String billno = ParamUtil.getStringValue(map, "bill_num", true, PosErrorCode.NOT_NULL_BILL_NUM);
        String posNum = ParamUtil.getStringValue(map, "pos_num", false, null);
        String optNum = ParamUtil.getStringValue(map, "opt_num", false, null);

        posDao.findWaiter(tenantId, organId, billno, result);

        posDao.savePosLog(tenantId, organId, posNum, optNum, optNum, shiftId, reportDate, Constant.TITLE, "查询服务员", "账单编号:" + billno, "");
    }

    @Deprecated
    @SuppressWarnings("unchecked")
    @Override
    public Data changeShift(Data param) throws SystemException {
        try {
            String tenantId = param.getTenancy_id();
            Integer organId = param.getStore_id();

            Map<String, Object> maps = ReqDataUtil.getDataMap(param);

            Date reportDate = ParamUtil.getDateValue(maps, "report_date", false, null);
            Integer shiftId = ParamUtil.getIntegerValue(maps, "shift_id", false, null);
            Integer loginCount = 0;
            if (Tools.isNullOrEmpty(maps.get("opt_login_number")) == false) {
                loginCount = Integer.parseInt(maps.get("opt_login_number").toString());
            }
            String posNum = ParamUtil.getStringValue(maps, "pos_num", false, null);
            String optNum = ParamUtil.getStringValue(maps, "opt_num", false, null);
            // 1正餐 2快餐
            String busiType = ParamUtil.getStringValue(maps, "business_type", true, PosErrorCode.NOT_NULL_BUSSINESS_TYPE);

            String isAllPos = ParamUtil.getStringValue(maps, "isallpos", false, null);
            if (StringUtils.isEmpty(isAllPos)) {
                isAllPos = "N";
            }

            String optName;

            posDao.checkSign(reportDate, optNum, posNum, busiType, organId, tenantId);

            optName = posDao.getEmpNameById(optNum, tenantId, organId);

            Timestamp startTime = null;

            Timestamp endTime = null;

            String workTime = ParamUtil.getStringValue(maps, "work_datetime", false, null);

            if (StringUtils.isNotEmpty(workTime)) {
                String[] array = workTime.split("~");

                if (array.length > 0) {
                    startTime = DateUtil.formatTimestamp(array[0]);
                    endTime = DateUtil.formatTimestamp(array[1]);
                }
            }
            String remark = ParamUtil.getStringValue(maps, "remark", false, null);

            Double paid_money = ParamUtil.getDoubleValue(maps, "paid_money", false, null);

            Double refund_money = ParamUtil.getDoubleValue(maps, "refund_money", false, null);

            if ("1".equals(busiType)) {
                List<Map<String, Object>> list = (List<Map<String, Object>>) maps.get("item");
                // if (list.size() == 0)
                // {
                // throw new
                // SystemException(PosErrorCode.NULL_CHANGE_SHIFT_COUNT);
                // }
                Timestamp time = DateUtil.currentTimestamp();
                for (int k = 0; k < list.size(); k++) {
                    Map<String, Object> map = list.get(k);

                    double payment_count = ParamUtil.getDoubleValue(map, "payment_count", false, null);
                    double payment_amount = ParamUtil.getDoubleValue(map, "payment_amount", false, null);
                    double differ_amount = ParamUtil.getDoubleValue(map, "differ_amount", false, null);
                    double give_count = ParamUtil.getDoubleValue(map, "give_count", false, null);
                    double give_amount = ParamUtil.getDoubleValue(map, "give_amount", false, null);

                    Integer jzid = ParamUtil.getIntegerValue(map, "jzid", false, null);
                    String payment_name = ParamUtil.getStringValue(map, "payment_name", false, null);

                    // 收银员交班金额库
                    StringBuilder sql = new StringBuilder(
                            "insert into pos_opter_changshift (tenancy_id,store_id,cashier_num,cashier_name,payment_count,payment_amount,give_count,give_amount,opt_login_number,pos_num,jzid,payment_name,differ_amount,report_date,shift_id,opt_pos_num,opt_num,last_updatetime,begain_shift,end_shift) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");

                    posDao.update(sql.toString(), new Object[]
                            {tenantId, organId, optNum, optName, payment_count, payment_amount, give_count, give_amount, loginCount, posNum, jzid, payment_name, differ_amount, reportDate, shiftId, posNum, optNum, time, startTime, endTime});

                }

                return posDao.updateOptStateForChangeShift(tenantId, organId, optNum, posNum, reportDate, isAllPos,shiftId);
            } else if ("2".equalsIgnoreCase(busiType)) {
                // 只有快餐去查
                String daysql = new String("select count(id) as count,bill_num,open_opt,open_pos_num from pos_bill where open_pos_num = ? and open_opt = ? and report_date = ? and (bill_property='OPEN' or bill_property='SORT') and store_id = ? group by bill_num,open_opt,open_pos_num");
                SqlRowSet rsb = posDao.query4SqlRowSet(daysql, new Object[]
                        {posNum, optNum, reportDate, organId});
                if (rsb.next()) {
                    int dcount = rsb.getInt("count");
                    String bill_num = rsb.getString("bill_num");
                    String open_opt = rsb.getString("open_opt");
                    String open_pos_num = rsb.getString("open_pos_num");
                    if (dcount > 0) {
                        Data data = new Data();
                        data.setCode(Constant.CODE_INNER_EXCEPTION);
                        String openOptName = posDao.getEmpNameById(open_opt, tenantId, organId);
                        data.setMsg("有未结账单，不能交班！未结账单号：" + bill_num + ",操作员：" + openOptName + ",机台号：" + open_pos_num);
                        data.setSuccess(false);
                        return data;
                    }
                }

                List<Map<String, Object>> list = (List<Map<String, Object>>) maps.get("item");
                // if (list.size() == 0)
                // {
                // throw new
                // SystemException(PosErrorCode.NULL_CHANGE_SHIFT_COUNT);
                // }
                Timestamp time = DateUtil.currentTimestamp();
                for (int i = 0; i < list.size(); i++) {
                    Map<String, Object> map = list.get(i);

                    double payment_count = ParamUtil.getDoubleValue(map, "payment_count", false, null);
                    double payment_amount = ParamUtil.getDoubleValue(map, "payment_amount", false, null);
                    double differ_amount = ParamUtil.getDoubleValue(map, "differ_amount", false, null);
                    double give_count = ParamUtil.getDoubleValue(map, "give_count", false, null);
                    double give_amount = ParamUtil.getDoubleValue(map, "give_amount", false, null);

                    Integer jzid = ParamUtil.getIntegerValue(map, "jzid", false, null);
                    String payment_name = ParamUtil.getStringValue(map, "payment_name", false, null);

                    // 收银员交班金额库
                    StringBuilder sql = new StringBuilder(
                            "insert into pos_opter_changshift (tenancy_id,store_id,cashier_num,cashier_name,payment_count,payment_amount,give_count,give_amount,opt_login_number,pos_num,jzid,payment_name,differ_amount,report_date,shift_id,opt_pos_num,opt_num,last_updatetime,begain_shift,end_shift) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");

                    posDao.update(sql.toString(), new Object[]
                            {tenantId, organId, optNum, optName, payment_count, payment_amount, give_count, give_amount, loginCount, posNum, jzid, payment_name, differ_amount, reportDate, shiftId, posNum, optNum, time, startTime, endTime});

                }
                // 用来获取退款数量和退款金额
                String qpaidMoney = new String("select count(id) as back_num,sum(payment_amount) as back_money from pos_bill where bill_property='CLOSED' and bill_state='ZDQX02' and payment_time>? and payment_time<? and store_id = ?");
                double back_num = 0;
                double back_money = 0;

                SqlRowSet qrst = posDao.query4SqlRowSet(qpaidMoney, new Object[]
                        {startTime, endTime, organId});
                while (qrst.next()) {
                    back_num = qrst.getDouble("back_num");
                    back_money = qrst.getDouble("back_money");
                }

                String insertSql = new String(
                        "insert into pos_opter_paidrefund (tenancy_id,store_id,changshift_id,start_time,end_time,shift_id,report_date,pos_num,paid_money,refund_money,cashier_num,cashier_name,oprater_time,upload_tag,back_num,back_money,remark) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ");

                posDao.update(insertSql, new Object[]
                        {tenantId, organId, shiftId, startTime, endTime, shiftId, reportDate, posNum, paid_money, refund_money, optNum, optName, time, 0, back_num, back_money, remark});

                return posDao.updateOptStateForChangeShift(tenantId, organId, optNum, posNum, reportDate, isAllPos,shiftId);
            }

            return null;
        } catch (SystemException se) {
            throw se;
        } catch (Exception e) {
            e.printStackTrace();
            logger.info(ExceptionMessage.getExceptionMessage(e));
            throw SystemException.getInstance(PosErrorCode.OPER_ERROR);
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    @Deprecated
    public synchronized void signOffAndChangeShift(String tenancyId, int storeId, List<?> param, JSONObject printJson) throws Exception {
        if (param == null || param.size() <= 0 || param.isEmpty()) {
            throw new SystemException(PosErrorCode.PARAM_ERROR);
        }

        JSONObject paraJson = JSONObject.fromObject(param.get(0));

        String reportDateStr = ParamUtil.getStringValueByObject(paraJson, "report_date", true, PosErrorCode.NOT_NULL_REPORT_DATE);
        Date reportDate = DateUtil.parseDate(reportDateStr);
        String optNum = ParamUtil.getStringValueByObject(paraJson, "opt_num", true, PosErrorCode.NOT_NULL_OPT_NUM);
        String posNum = ParamUtil.getStringValueByObject(paraJson, "pos_num", true, PosErrorCode.NOT_NULL_POS_NUM);
        Integer shiftId = ParamUtil.getIntegerValueByObject(paraJson, "shift_id");
        String reason = ParamUtil.getStringValueByObject(paraJson, "reason"); // 溢缺原因备注
        String busiType = ParamUtil.getStringValueByObject(paraJson, "business_type", true, PosErrorCode.NOT_NULL_BUSSINESS_TYPE);

        Integer loginCount = 0;
        if (Tools.isNullOrEmpty(paraJson.opt("opt_login_number")) == false) {
            loginCount = paraJson.optInt("opt_login_number");
        }

        String isAllPos = paraJson.optString("isallpos");
        if (StringUtils.isEmpty(isAllPos)) {
            isAllPos = "N";
        }

        String empName = posDao.getEmpNameById(optNum, tenancyId, storeId);
//		String optName = posDao.getEmpNameById(optNum, tenancyId, storeId);

        Timestamp timestamp = DateUtil.currentTimestamp();

        Timestamp startTime = null;
        Timestamp endTime = null;

        String workTime = paraJson.optString("work_datetime");

        if (StringUtils.isNotEmpty(workTime)) {
            String[] array = workTime.split("~");
            if (array.length > 0) {
                startTime = DateUtil.formatTimestamp(array[0]);
                endTime = DateUtil.formatTimestamp(array[1]);
            }
        }

        posDao.checkReportDate(tenancyId, storeId, reportDate);

//		if ("2".equalsIgnoreCase(busiType))
//		{
        // 只有快餐去查

        //数据清理，删除无效数据
        StringBuilder cleanSql = new StringBuilder();
        cleanSql.append("delete from pos_bill_item i where not EXISTS (select 1 from pos_bill b where i.bill_num=b.bill_num) and report_date='" + reportDate.toString() + "';");
        cleanSql.append("delete from pos_bill_payment p where not EXISTS (select 1 from pos_bill b where p.bill_num=b.bill_num) and report_date='" + reportDate.toString() + "';");
        cleanSql.append("update pos_bill set bill_property='CLOSED' where order_num is not null and bill_property<>'CLOSED' and report_date='" + reportDate.toString() + "';");
        posDao.getJdbcTemplate(tenancyId).execute(cleanSql.toString());

        // 交班是否验证有未结账单   1,是;0,否
        String isHasUnpaid = posDao.getSysParameter(tenancyId, storeId, "JBSFYZYWJZD");
        if (Tools.isNullOrEmpty(isHasUnpaid)) {
            isHasUnpaid = "1";
        }
        if ("1".equals(isHasUnpaid)) {
            String daysql = new String("select count(id) as count,bill_num,open_opt,open_pos_num from pos_bill where tenancy_id=? and store_id = ? and report_date = ? and ((open_pos_num = ? and open_opt = ?) or open_pos_num in (select pd.devices_num from pos_opt_state_devices pd where pd.tenancy_id = ? and pd.store_id = ? and pd.pos_num = ? and pd.is_valid = '1')) and bill_property<>? group by bill_num,open_opt,open_pos_num");
            SqlRowSet rsb = posDao.query4SqlRowSet(daysql, new Object[]
                    {tenancyId, storeId, reportDate, posNum, optNum, tenancyId, storeId, posNum, SysDictionary.BILL_PROPERTY_CLOSED});
            if (rsb.next()) {
                int dcount = rsb.getInt("count");
                String bill_num = rsb.getString("bill_num");
                String open_opt = rsb.getString("open_opt");
                String open_pos_num = rsb.getString("open_pos_num");
                if (dcount > 0) {
                    String openOptName = posDao.getEmpNameById(open_opt, tenancyId, storeId);
                    throw SystemException.getInstance(PosErrorCode.CHANGESHIFT_HAS_BILL_NOT_CLOSED_ERROR).set("{0}", bill_num).set("{1}", openOptName).set("{2}", open_pos_num);
                }
            }
        }
//		}

        StringBuilder qureyOptStateSql = new StringBuilder("select id,pos_num from pos_opt_state where tag='0' and content=? and opt_num=? and report_date=? and store_id = ? and tenancy_id=?");

        SqlRowSet rs = posDao.query4SqlRowSet(qureyOptStateSql.toString(), new Object[]
                {SysDictionary.OPT_STATE_KSSY, optNum, reportDate, storeId, tenancyId});
        int beginId = 0;
        String pos_num = "";
        if (rs.next()) {
            beginId = rs.getInt("id");
            pos_num = rs.getString("pos_num");
        }

        if (beginId == 0) {
            throw SystemException.getInstance(PosErrorCode.POS_CASHIER_NOT_SIGNIN_ERROR);
        }

        rs = posDao.query4SqlRowSet(qureyOptStateSql.toString(), new Object[]
                {SysDictionary.OPT_STATE_YYTC, optNum, reportDate, storeId, tenancyId});
        int endId = 0;
        if (rs.next()) {
            endId = rs.getInt("id");
        }

        if (endId > 0 && endId > beginId) {
            throw SystemException.getInstance(PosErrorCode.POS_CASHIER_ALREADY_SIGNOUT_ERROR);
        }

        if (!posNum.equals(pos_num)) {
            throw SystemException.getInstance(PosErrorCode.POS_OPEN_CLOSE_POSNUM_NOT_EQUALS).set("{0}", pos_num);
        }

        // 机台状态库

        List<Map<String, Object>> list = (List<Map<String, Object>>) paraJson.get("pay_item");

        // 验证交班溢缺
        StringBuffer paraSql = new StringBuffer("SELECT para_value, valid_state from sys_parameter WHERE para_code=?");
        SqlRowSet paraSet = posDao.query4SqlRowSet(paraSql.toString(), new Object[]{"shift_limitmoney"});
        double paraValue = 0.0;        // 临时存放溢缺金额
        String validState = "";        // 是否开启溢缺金额验证	0:不验证  其他:验证
        if (paraSet.next()) {
            paraValue = Double.parseDouble(paraSet.getString("para_value"));
            validState = paraSet.getString("valid_state");
            if (!validState.equals("0")) {

                if (list != null && list.size() > 0) {
                    for (int k = 0; k < list.size(); k++) {
                        Map<String, Object> map = list.get(k);
                        double differ_amount = ParamUtil.getDoubleValue(map, "differ_amount", false, null);
                        if (Math.abs(differ_amount) > paraValue) {
                            throw SystemException.getInstance(PosErrorCode.CHANGE_SHIFT_LIMITMONEY_FAIL);
                        }
                    }
                }
            }
        }

//		StringBuilder instr = new StringBuilder("insert into pos_opt_state (tenancy_id,store_id,pos_num,content,opt_num,opt_name,report_date,last_updatetime,tag,login_number) values (?,?,?,?,?,?,?,?,?,?)");
//		posDao.update(instr.toString(), new Object[]
//		{ tenancyId, storeId, posNum, SysDictionary.OPT_STATE_YYTC, optNum, empName, reportDate, timestamp, "0", loginCount });
        posDao.saveOptState(tenancyId, storeId, reportDate, shiftId, posNum, optNum, SysDictionary.OPT_STATE_YYTC, null, null, loginCount, endTime);

        posDao.savePosLog(tenancyId, storeId, posNum, optNum, optNum, shiftId, reportDate, Constant.TITLE, "收银员签退", "", empName + "签退");

        String remark = paraJson.optString("remark");

        Double paid_money = ParamUtil.getDoubleValueByObject(paraJson, "paid_money");
        Double refund_money = ParamUtil.getDoubleValueByObject(paraJson, "refund_money");
        Double businessAmount = ParamUtil.getDoubleValueByObject(paraJson, "business_amount");
        Double change = ParamUtil.getDoubleValueByObject(paraJson, "change");
        Double discountkAmount = ParamUtil.getDoubleValueByObject(paraJson, "discountk_amount");
        Double discountrAmount = ParamUtil.getDoubleValueByObject(paraJson, "discountr_amount");
        Double malingAmount = ParamUtil.getDoubleValueByObject(paraJson, "maling_amount");
        Double moreCoupon = ParamUtil.getDoubleValueByObject(paraJson, "more_coupon");
        Double giviAmount = ParamUtil.getDoubleValueByObject(paraJson, "givi_amount");
        Double retreatCount = ParamUtil.getDoubleValueByObject(paraJson, "retreat_count");
        Double retreatAmount = ParamUtil.getDoubleValueByObject(paraJson, "retreat_amount");
        Double singleRetreatCount = ParamUtil.getDoubleValueByObject(paraJson, "single_retreat_count");
        Double singleRetreatAmount = ParamUtil.getDoubleValueByObject(paraJson, "single_retreat_amount");
        Double bills = ParamUtil.getDoubleValueByObject(paraJson, "bills");
        Double customerRecharge = ParamUtil.getDoubleValueByObject(paraJson, "customer_recharge");
        Double customerConsume = ParamUtil.getDoubleValueByObject(paraJson, "customer_consume");
        Double backCard = ParamUtil.getDoubleValueByObject(paraJson, "back_card");
        Double customerDeposit = ParamUtil.getDoubleValueByObject(paraJson, "customer_deposit");
        Double indemnityAmount = ParamUtil.getDoubleValueByObject(paraJson, "paid_money");
        Double refundAmount = ParamUtil.getDoubleValueByObject(paraJson, "refund_money");
        Double saleCardAmount = ParamUtil.getDoubleValueByObject(paraJson, "sale_card_amount");
        Double customerReward = ParamUtil.getDoubleValueByObject(paraJson, "customer_reward");
        Double shopDealAmount = ParamUtil.getDoubleValueByObject(paraJson, "shop_real_amount");
        Double platformChargeAmount = ParamUtil.getDoubleValueByObject(paraJson, "platform_charge_amount");

        StringBuffer plainText = new StringBuffer();
        plainText.append(tenancyId).append("_").append(storeId).append("_").append(reportDateStr).append("_");
        plainText.append(shiftId).append("_").append(optNum).append("_").append(posNum).append("_");
        plainText.append(startTime.getTime()).append("_").append(endTime.getTime()).append("_").append(timestamp.getTime());

        String changshiftUid = JavaMd5.toMd5B16(plainText.toString());

        String insertMainSql = new String(
                "insert into pos_opter_changshift_main (tenancy_id,store_id,report_date,shift_id,opt_num,pos_num,is_allpos,begain_time,end_time,business_amount,change,discountk_amount,discountr_amount,maling_amount,more_coupon,givi_amount,retreat_count,retreat_amount,single_retreat_count,single_retreat_amount,bills,customer_recharge,customer_consume,back_card,customer_deposit,indemnity_amount,refund_amount,last_updatetime,remark,upload_tag,sale_card_amount,customer_reward,shop_real_amount,platform_charge_amount,reason,changshift_uid) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ");

        posDao.update(insertMainSql, new Object[]
                {tenancyId, storeId, reportDate, shiftId, optNum, posNum, isAllPos, startTime, endTime, businessAmount, change, discountkAmount, discountrAmount, malingAmount, moreCoupon, giviAmount, retreatCount, retreatAmount, singleRetreatCount, singleRetreatAmount, bills, customerRecharge, customerConsume, backCard, customerDeposit, indemnityAmount, refundAmount, timestamp, remark, 0, saleCardAmount, customerReward, shopDealAmount, platformChargeAmount, reason, changshiftUid});

//		/**
//		 * 获取当前的序列
//		 */
//		String qseq = new String("select currval('pos_opter_changshift_main_id_seq'::regclass) ");
//		Integer pid = posDao.queryForInt(qseq, new Object[] {});

        if (list != null && list.size() > 0) {
            for (int k = 0; k < list.size(); k++) {
                Map<String, Object> map = list.get(k);

                double payment_count = ParamUtil.getDoubleValue(map, "payment_count", false, null);
                double payment_amount = ParamUtil.getDoubleValue(map, "payment_amount", false, null);
                double differ_amount = ParamUtil.getDoubleValue(map, "differ_amount", false, null);
                double give_count = ParamUtil.getDoubleValue(map, "give_count", false, null);
                double give_amount = ParamUtil.getDoubleValue(map, "give_amount", false, null);
                Integer jzid = ParamUtil.getIntegerValue(map, "jzid", false, null);
                String payment_name = ParamUtil.getStringValue(map, "payment_name", false, null);

                try {
                    // 收银员交班金额库
                    StringBuilder sql = new StringBuilder(
                            "insert into pos_opter_changshift (tenancy_id,store_id,cashier_num,cashier_name,payment_count,payment_amount,give_count,give_amount,opt_login_number,pos_num,jzid,payment_name,differ_amount,report_date,shift_id,opt_pos_num,opt_num,last_updatetime,begain_shift,end_shift,changshift_id) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");

                    posDao.update(sql.toString(), new Object[]
                            {tenancyId, storeId, optNum, empName, payment_count, payment_amount, give_count, give_amount, loginCount, posNum, jzid, payment_name, differ_amount, reportDate, shiftId, posNum, optNum, timestamp, startTime, endTime, changshiftUid});

                } catch (Exception se) {
                    logger.info("收银员交班写库错误：" + ExceptionMessage.getExceptionMessage(se));
                    throw new SystemException(PosErrorCode.OPER_ERROR);
                }
            }
        }

        if ("2".equalsIgnoreCase(busiType)) {
            // 用来获取退款数量和退款金额
            String qpaidMoney = new String("select count(id) as back_num,sum(payment_amount) as back_money from pos_bill where bill_property='CLOSED' and bill_state='ZDQX02' and payment_time>? and payment_time<? and store_id = ?");
            double back_num = 0;
            double back_money = 0;

            SqlRowSet qrst = posDao.query4SqlRowSet(qpaidMoney, new Object[]
                    {startTime, endTime, storeId});
            while (qrst.next()) {
                back_num = qrst.getDouble("back_num");
                back_money = qrst.getDouble("back_money");
            }

            String insertSql = new String(
                    "insert into pos_opter_paidrefund (tenancy_id,store_id,changshift_id,start_time,end_time,shift_id,report_date,pos_num,paid_money,refund_money,cashier_num,cashier_name,oprater_time,upload_tag,back_num,back_money,remark) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ");

            posDao.update(insertSql, new Object[]
                    {tenancyId, storeId, changshiftUid, startTime, endTime, shiftId, reportDate, posNum, paid_money, refund_money, optNum, empName, timestamp, 0, back_num, back_money, remark});
        }

        // 会员结算数据
        List<Map<String, Object>> listActivation = (List<Map<String, Object>>) paraJson.get("card_item");

        if (listActivation != null && listActivation.size() > 0) {
            for (int k = 0; k < listActivation.size(); k++) {
                Map<String, Object> map = listActivation.get(k);

                String operatType = ParamUtil.getStringValue(map, "operat_type_code", false, null);
                String operatDetails = ParamUtil.getStringValue(map, "jzid", false, null);
                double tradeAmount = ParamUtil.getDoubleValue(map, "payment_amount", false, null);
                double realAmount = ParamUtil.getDoubleValue(map, "give_amount", false, null);
                double differAmount = ParamUtil.getDoubleValue(map, "differ_amount", false, null);

                try {
                    // 收银员交班会员结算表
                    StringBuilder sql = new StringBuilder(
                            "insert into pos_opter_changshift_customer (tenancy_id,store_id,changshift_id,report_date,shift_id,opt_num,pos_num,operat_type,operat_details,trade_amount,real_amount,differ_amount,last_updatetime,remark,upload_tag) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");

                    posDao.update(sql.toString(), new Object[]
                            {tenancyId, storeId, changshiftUid, reportDate, shiftId, optNum, posNum, operatType, operatDetails, tradeAmount, realAmount, differAmount, timestamp, remark, 0});
                } catch (Exception se) {
                    logger.info("收银员交班写库错误：" + ExceptionMessage.getExceptionMessage(se));
                    throw new SystemException(PosErrorCode.OPER_ERROR);
                }
            }
        }

        posDao.updateOptStateForChangeShift(tenancyId, storeId, optNum, posNum, reportDate, isAllPos,shiftId);

        //交班单打印
        printJson.put("tenancy_id", tenancyId);
        printJson.put("store_id", storeId);
        printJson.put("print_code", SysDictionary.PRINT_CODE_1301);
        printJson.put("mode", "0");
        printJson.put("print_type", "1");

        printJson.put("report_date", reportDateStr);
        printJson.put("shift_id", shiftId);
        printJson.put("pos_num", posNum);
        printJson.put("opt_num", optNum);
        printJson.put("date_remark", DateUtil.format(startTime) + "~" + DateUtil.format(endTime));

    }

//	@Override
//	@Deprecated
//	public void getShiftData(Data param, Data result) throws SystemException
//	{
//		try
//		{
//			String tenantId = param.getTenancy_id();
//			Integer organId = param.getStore_id();
//
//			Map<String, Object> map = ReqDataUtil.getDataMap(param);
//
//			String mode = ParamUtil.getStringValue(map, "mode", true, PosErrorCode.NOT_NULL_MODE);
//			Date reportDate = ParamUtil.getDateValue(map, "report_date", true, PosErrorCode.NOT_NULL_REPORT_DATE);
//			String posNum = ParamUtil.getStringValue(map, "pos_num", true, PosErrorCode.NOT_NULL_POS_NUM);
//			String optNum = ParamUtil.getStringValue(map, "opt_num", true, PosErrorCode.NOT_NULL_OPT_NUM);
//			String isAllPos = ParamUtil.getStringValue(map, "isallpos", false, null);
//			if (StringUtils.isEmpty(isAllPos))
//			{
//				isAllPos = "N";
//			}
//
//			double back_num = 0;
//			double back_money = 0;
//			double retreat_num = 0;
//			double retreat_money = 0;
//			double recharge_money = 0;
//			double reward_money = 0;
//			double consume_money = 0;
//			double backout_money = 0;
//
//			Timestamp startTimestamp = null;
//			Timestamp endTimestamp = DateUtil.currentTimestamp();
//
//			int scale =4;
//
//			String lastUpdateTime = "";
//			String time = DateUtil.format(endTimestamp);
//
//			List<JSONObject> list = new ArrayList<JSONObject>();
//			JSONObject obj = new JSONObject();
//			// 获取当前机台当前人员的交班数据
//			if ("0".equalsIgnoreCase(mode))
//			{
//				// String optName = posDao.getEmpNameById(optNum, tenantId,
//				// organId);
//				String formatState = "";
//				String formatMode = posDao.getSysParameter(tenantId, organId, "ZCDCMS");
//
//				StringBuilder querySql = new StringBuilder("select format_state, org_short_name from organ where tenancy_id=? and id=?");
//				SqlRowSet rsOrgan = posDao.query4SqlRowSet(querySql.toString(), new Object[]
//				{ tenantId, organId });
//				if (rsOrgan.next())
//				{
//					formatState = rsOrgan.getString("format_state");
//					obj.put("org_short_name", rsOrgan.getString("org_short_name"));
//				}
//				obj.put("report_date",DateUtil.format(reportDate, "yyyy-MM-dd"));
//				JSONArray arr = new JSONArray();
//				if ("Y".equalsIgnoreCase(isAllPos))
//				{
//					// 查询last_updatetime
//					StringBuilder str = new StringBuilder("select sd.class_item as name ,pos.last_updatetime,pos.shift_id from pos_opt_state pos left join duty_order dor on dor.id = pos.shift_id ");
//					str.append(" left join sys_dictionary sd on sd.class_item_code= dor.name and sd.class_identifier_code = 'duty' ");
//					str.append(	" where pos.tenancy_id=? and pos.store_id=? and pos.report_date=? and pos.opt_num=? and  pos.content=? and pos.tag='0' order by pos.id asc");
//					SqlRowSet rs = posDao.query4SqlRowSet(str.toString(), new Object[]
//					{ tenantId, organId, reportDate, optNum, SysDictionary.OPT_STATE_KSSY });
//
//					if (rs.next())
//					{
//						startTimestamp = rs.getTimestamp("last_updatetime");
//						lastUpdateTime = rs.getString("last_updatetime");
//						obj.put("duty_order_name", rs.getString("name"));
//					}
//
//					if (StringUtils.isEmpty(lastUpdateTime))
//					{
//						throw new SystemException(PosErrorCode.POS_CASHIER_NOT_SIGNIN_ERROR);
//					}
//
//					obj.put("work_datetime", lastUpdateTime.substring(0, 19) + "~" + time);
//
//					StringBuilder sql = new StringBuilder("select pd.*,pd2.give_amount,pd2.give_count,pd2.differ_amount from (");
//					sql.append(" select pa.jzid,(case when pw.payment_class='").append(SysDictionary.PAYMENT_CLASS_CASH).append("' then sd.class_item else pw.payment_name1 end) as payment_name,sum(pa.currency_amount) as amount,");
//					sql.append(" count(case when pa.name_english='").append(SysDictionary.PAYMENT_CHANGE_ENGLISH_NAME).append("' or bi.bill_state='").append(SysDictionary.BILL_STATE_CJ01).append("' or bi.bill_state='").append(SysDictionary.BILL_STATE_ZDQX02).append("' then null else 1 end) as count");
//					sql.append(" from pos_bill_payment pa left join payment_way pw on pa.jzid=pw.id and pa.tenancy_id=pw.tenancy_id and pw.status='1'");
//					sql.append(" left join sys_dictionary sd on pw.payment_name1 = sd.class_item_code left join pos_bill bi on bi.tenancy_id=pa.tenancy_id and bi.store_id=pa.store_id and bi.bill_num=pa.bill_num");
//					sql.append(" where bi.store_id = ? and bi.report_date = ?  and bi.payment_time > ? and bi.payment_time < ? and bi.cashier_num = ? group by pa.jzid,payment_name) pd");
//					sql.append(" left join (select jzid,coalesce(sum(give_amount),0) as give_amount,coalesce(sum(give_count),0) as give_count,coalesce(sum(differ_amount),0) as differ_amount from pos_opter_changshift where store_id = ? and report_date=?   group by jzid) pd2");
//					sql.append(" on pd.jzid=pd2.jzid order by pd.jzid desc");
//
//					rs = posDao.query4SqlRowSet(sql.toString(), new Object[]
//					{ organId, reportDate, startTimestamp, endTimestamp,optNum, organId, reportDate });
//
//					while (rs.next())
//					{
//						JSONObject json = new JSONObject();
//						json.put("jzid", rs.getInt("jzid"));
//						json.put("payment_name", rs.getString("payment_name"));
//						json.put("payment_amount", rs.getDouble("amount"));
//						json.put("payment_count", rs.getInt("count"));
//						json.put("give_amount", 0);
//						json.put("give_count", 0);
//						json.put("differ_amount", 0);
//						json.put("can_modify", "Y");
//						arr.add(json);
//					}
//					obj.put("pay_item", arr);
//					double bill_amount = 0d;// 营业应收
//					double more_coupon = 0d; //多收礼券
//					double sale_person_num = 0d;//消费客数
//					double sale_billnum = 0d;//账单数
//
//					double coupon_buy_price = 0d; // 美团票券用户实付
//					double due = 0d; // 美团票券账单净收
//					double tenancy_assume = 0d; // 美团票券商户承担
//					double third_assume = 0d;	// 美团票券第三方承担
//					double third_fee = 0d; 	// 美团票券第三方服务费
//
////					StringBuilder sqlAmount = new StringBuilder("select sum(pb.discountk_amount) as discountk_amount,sum(pb.discountr_amount) as discountr_amount,sum(pb.maling_amount) as maling_amount,sum(pb.givi_amount) as givi_amount,sum(pb.more_coupon) as more_coupon,sum(coalesce(pb.shop_real_amount,0)) as shop_real_amount,sum(coalesce(pb.platform_charge_amount,0)) as platform_charge_amount,sum(coalesce(service_amount,0)) as service_amount,");
//					StringBuilder sqlAmount = new StringBuilder("select sum(pb.discountk_amount) as discountk_amount,sum(pb.discountr_amount) as discountr_amount,sum(pb.maling_amount) as maling_amount,sum(pb.givi_amount) as givi_amount,sum(pb.more_coupon) as more_coupon,sum(coalesce(pb.shop_real_amount,0)) as shop_real_amount,sum(coalesce(pb.platform_charge_amount,0)) as platform_charge_amount,sum(coalesce(service_amount,0)) as service_amount,sum(coalesce(pb.coupon_buy_price,0)) as coupon_buy_price, sum(coalesce(pb.due,0)) as due, sum(coalesce(pb.tenancy_assume,0)) as tenancy_assume, sum(coalesce(pb.third_assume,0)) as third_assume, sum(coalesce(pb.third_fee,0)) as third_fee, ");
//
//					sqlAmount.append("coalesce(sum(guest), 0) as sale_person_num,");
//					sqlAmount.append("count(case when coalesce(pb.bill_state, 'zc') not in ('CJ01', 'ZDQX02') then pb.bill_num end) as sale_billnum,");
//					sqlAmount.append("sum(pb.bill_amount) as bill_amount from pos_bill pb ");
//					sqlAmount.append(" where pb.store_id = ? and pb.report_date = ? and pb.payment_time > ? and pb.payment_time < ? and pb.bill_property = ? and  pb.cashier_num = ? ");
//					SqlRowSet rsAmount = posDao.query4SqlRowSet(sqlAmount.toString(), new Object[]
//					{ organId, reportDate, startTimestamp, endTimestamp ,SysDictionary.BILL_PROPERTY_CLOSED,optNum});
//					while (rsAmount.next())
//					{
//						bill_amount =  rsAmount.getDouble("bill_amount");
//						sale_person_num = rsAmount.getDouble("sale_person_num");
//						sale_billnum =  rsAmount.getDouble("sale_billnum");
//						more_coupon =  rsAmount.getDouble("more_coupon");
//						// 折扣
//						obj.put("discountk_amount", rsAmount.getDouble("discountk_amount"));
//						// 折让
//						obj.put("discountr_amount", rsAmount.getDouble("discountr_amount"));
//						// 抹零
//						obj.put("maling_amount", rsAmount.getDouble("maling_amount"));
//						// 奉送
//						obj.put("givi_amount", rsAmount.getDouble("givi_amount"));
//						// 多收礼券
//						obj.put("more_coupon", more_coupon);
//						// 商家实收
//						obj.put("shop_real_amount", rsAmount.getDouble("shop_real_amount"));
//						// 外卖平台收取金额
//						obj.put("platform_charge_amount", rsAmount.getDouble("platform_charge_amount"));
//						// 服务费
//						obj.put("service_amount", rsAmount.getDouble("service_amount"));
//						// 营业应收
//						obj.put("bill_amount", bill_amount);
//						//消费客数
//						obj.put("sale_person_num", sale_person_num);
//						//账单数
//						obj.put("sale_billnum", sale_billnum);
//						//人均均值
//						if(sale_person_num != 0){
//							obj.put("sale_person_average", DoubleHelper.div(bill_amount+more_coupon,sale_person_num,2));
//						}else{
//							obj.put("sale_person_average",0);
//						}
//						//账单均值
//						if(sale_person_num != 0){
//							obj.put("sale_billaverage", DoubleHelper.div(bill_amount+more_coupon,sale_billnum,2));
//						}else{
//							obj.put("sale_billaverage",0);
//						}
//
//						coupon_buy_price = rsAmount.getDouble("coupon_buy_price");
//						due = rsAmount.getDouble("due");
//						tenancy_assume = rsAmount.getDouble("tenancy_assume");
//						third_assume = rsAmount.getDouble("third_assume");
//						third_fee = rsAmount.getDouble("third_fee");
//
////						// 美团票券商户承担
////						obj.put("tenancy_assume", tenancy_assume);
////						// 美团票券第三方承担
////						obj.put("third_assume", third_assume);
//
//						// 美团票券虚收  =  商家承担 + 第三方服务费
//						obj.put("tenancy_assume", DoubleHelper.round(tenancy_assume + third_fee, 2));
//						// 美团票券第三方承担
//						obj.put("third_assume", DoubleHelper.round(third_assume, 2));
//
//						// 顾客实付= 账单应收-第三方支付优惠(商家承担 + 第三方承担)
//						double customer_real_pay = DoubleHelper.round(bill_amount - tenancy_assume - third_assume, 2);
//						// 账单净收 = 账单应收 - 商家承担优惠 - 票券服务费
//						double bill_due = DoubleHelper.round(bill_amount - tenancy_assume - third_fee, 2) ;
//						double service_amount = rsAmount.getDouble("service_amount");
//						// 菜品净收 = 账单净收- 服务费
//						double item_actual = DoubleHelper.round(bill_due - service_amount, 2);
//
//						// 顾客实付
//						obj.put("customer_real_pay", customer_real_pay);
//						// 账单净收
//						obj.put("bill_due", bill_due);
//						// 菜品净收
//						obj.put("item_actual", item_actual);
//
//					}
//					double  payment_total = 0d;//营业实收  = 收款方式合计
//					StringBuffer paymentTotalSql = new StringBuffer(" select coalesce(sum(p.currency_amount), 0) as payment_total  from pos_bill_payment p LEFT JOIN pos_bill pb on  p.bill_num = pb.bill_num ");
//					paymentTotalSql.append(" where pb.store_id = ? and pb.report_date = ? and pb.payment_time > ? and pb.payment_time < ? and pb.bill_property = ? and  pb.cashier_num = ?");
//					SqlRowSet paymentTotalRs = posDao.query4SqlRowSet(paymentTotalSql.toString(), new Object[]
//							{ organId, reportDate, startTimestamp, endTimestamp ,SysDictionary.BILL_PROPERTY_CLOSED,optNum});
//					if(paymentTotalRs.next()){
//						payment_total = paymentTotalRs.getDouble("payment_total");
//					}
//					obj.put("payment_total",payment_total);
//					obj.put("real_amount",DoubleHelper.sub(payment_total, more_coupon, scale));
//
//					double  no_income_money = 0d;//不计收入付款方式金额
//					StringBuffer noIncomeMoneySql = new StringBuffer(" select sum(case when w.if_income='0' then coalesce(p.currency_amount, 0) else 0 end)  no_income_money  from pos_bill_payment p LEFT JOIN pos_bill pb on  p.bill_num = pb.bill_num ");
//					noIncomeMoneySql.append(" left join (select id,payment_class,if_income from payment_way where status='1') w on p.jzid = w.id");
//					noIncomeMoneySql.append(" where pb.store_id = ? and pb.report_date = ? and pb.payment_time > ? and pb.payment_time < ? and pb.bill_property = ? and  pb.cashier_num = ?");
//					SqlRowSet noIncomeMoneyRs = posDao.query4SqlRowSet(noIncomeMoneySql.toString(), new Object[]
//							{ organId, reportDate, startTimestamp, endTimestamp ,SysDictionary.BILL_PROPERTY_CLOSED,optNum});
//					if(noIncomeMoneyRs.next()){
//						no_income_money = noIncomeMoneyRs.getDouble("no_income_money");
//					}
//					obj.put("no_income_money",no_income_money);
//					obj.put("real_income",payment_total-no_income_money);//付款实际收入
////					StringBuffer tableDataSql = new StringBuffer("select  max(t.seat_num) as seat_num , max(t.tables_num) as tables_num");
////					tableDataSql.append("  from pos_bill pb ");
////					tableDataSql.append("left join  (select organ_id, coalesce(count(t.id),0)  as tables_num, coalesce(sum(t.seat_counts), 0) as seat_num ");
////					tableDataSql.append(" from tables_info t where t.valid_state = '1' group by organ_id ) t on pb.store_id=t.organ_id");
////					tableDataSql.append(" where pb.store_id = ? and pb.report_date = ? and pb.payment_time > ? and pb.payment_time < ? and pb.bill_property = ? and  pb.cashier_num = ?");
////					StringBuffer tableDataSql = new StringBuffer("select organ_id, coalesce(count(t.id),0)  as tables_num, coalesce(sum(t.seat_counts), 0) as seat_num ");
////					tableDataSql.append(" from tables_info t INNER JOIN pos_bill pb ON t.organ_id = pb.store_id");
////					tableDataSql.append(" where t.valid_state = '1' AND pb.bill_amount = 0 group by organ_id");
//
//					StringBuffer tableDataSql = new StringBuffer("select coalesce(count(t.id),0) as tables_num, coalesce(sum(t.seat_counts), 0) as seat_num from tables_info t where t.tenancy_id=? and t.organ_id=? and t.valid_state = '1' ");
//					SqlRowSet tableDataRs = posDao.query4SqlRowSet(tableDataSql.toString(), new Object[]{tenantId,organId});
//					if(tableDataRs.next()){
//						double tables_num =  tableDataRs.getDouble("tables_num");//桌台数
//						double seat_num =  tableDataRs.getDouble("seat_num");//座位数
//						obj.put("tables_num", tables_num);
//						obj.put("seat_num", seat_num);
//						//翻台率 =【消费账单】/【桌台数】
//						if(tables_num != 0 ){
//							obj.put("num_ft", DoubleHelper.div(sale_billnum,tables_num,2));//翻台率
//						}else{
//							obj.put("num_ft", 0d);//翻台率
//						}
//						// 上座率 = 【消费客数】/【座位数】
//						if(seat_num != 0 ){
//							obj.put("num_sz", DoubleHelper.div(sale_person_num,seat_num,2));//上座率
//						}else{
//							obj.put("num_sz", 0d);//上座率
//						}
//					}
//					if ("1".equals(formatState) && "02".equals(formatMode))
//					{// 自助
//						// 用来获取退款数量和退款金额
//						// StringBuilder qpaidMoney = new
//						// StringBuilder("select count(bi.id) as back_num,sum(coalesce(bi.return_amount,0)) as back_money from pos_bill_batch bi");
//						// qpaidMoney.append(" where bi.tenancy_id=? and bi.store_id =? and bi.cashier_num =? and bi.payment_time>? and bi.payment_time<? and bi.bill_state='ZDQX02'");
//						// SqlRowSet qrst =
//						// posDao.query4SqlRowSet(qpaidMoney.toString(), new
//						// Object[]
//						// { tenantId, organId, optNum, startTimestamp,
//						// endTimestamp });
//						// if (qrst.next())
//						// {
//						// back_num = qrst.getDouble("back_num");
//						// back_money = qrst.getDouble("back_money");
//						// }
//						obj.put("back_num", back_num);
//						obj.put("back_money", back_money);
//
//						// 查询单品退菜数量和金额
//						StringBuilder singleRetreatSb = new StringBuilder(
//								"select coalesce(count(b.id),'0') as single_retreat_count,coalesce(sum(bi.real_amount),'0') as single_retreat_amount from pos_bill_batch b inner join pos_bill_item bi on b.tenancy_id=bi.tenancy_id and b.store_id=bi.store_id and b.bill_num=bi.bill_num and b.batch_num=bi.batch_num");
//						singleRetreatSb.append(" where b.tenancy_id=? and b.store_id =? and b.payment_time >? and b.payment_time <? and b.cashier_num =? and bi.item_remark='TC01' and bi.item_property in ('SINGLE','SETMEAL') ");
//						// StringBuilder singleRetreatSb = new
//						// StringBuilder("select coalesce(count(p.bill_num),'0') as single_retreat_count,coalesce(round(sum (p.payment_amount),2),0) as single_retreat_amount from pos_bill p inner join pos_bill pb on p.copy_bill_num = pb.bill_num and (pb.bill_state is null or pb.bill_state <> 'ZDQX02')");
//						// singleRetreatSb.append(" where p.payment_time >? and p .payment_time <? and p.cashier_num =? and p .tenancy_id =? and p .store_id =? and p.bill_state = 'CJ01'");
//						SqlRowSet singleRetreatRs = posDao.query4SqlRowSet(singleRetreatSb.toString(), new Object[]
//						{ tenantId, organId, startTimestamp, endTimestamp, optNum });
//						if (singleRetreatRs.next())
//						{
//							retreat_num = singleRetreatRs.getDouble("single_retreat_count");
//							retreat_money = singleRetreatRs.getDouble("single_retreat_amount");
//						}
//						obj.put("single_retreat_count", retreat_num);
//						obj.put("single_retreat_amount", retreat_money);
//					}
//					else
//					{
//						// 用来获取退款数量和退款金额
//						StringBuilder qpaidMoney = new StringBuilder("select count(bi.id) as back_num,sum(bi.payment_amount) as back_money from pos_bill bi");
//						qpaidMoney.append(" inner join pos_bill bi2 on bi.tenancy_id=bi2.tenancy_id and bi.store_id=bi2.store_id and bi.bill_num=bi2.copy_bill_num and bi.bill_state='ZDQX02' and bi.bill_property='CLOSED' and bi2.bill_state='CJ01' and bi2.bill_property='CLOSED'");
//						qpaidMoney.append(" where bi2.tenancy_id=? and bi2.store_id =? and bi2.cashier_num =? and bi2.payment_time>? and bi2.payment_time<?");
//						SqlRowSet qrst = posDao.query4SqlRowSet(qpaidMoney.toString(), new Object[]
//						{ tenantId, organId, optNum, startTimestamp, endTimestamp });
//						if (qrst.next())
//						{
//							back_num = qrst.getDouble("back_num");
//							back_money = qrst.getDouble("back_money");
//						}
//
//						obj.put("back_num", back_num);
//						obj.put("back_money", back_money);
//
//						// 查询单品退菜数量和金额
//						StringBuilder singleRetreatSb = new StringBuilder(
//								"select coalesce(count(p.bill_num),'0') as single_retreat_count,coalesce(round(sum (p.payment_amount),2),0) as single_retreat_amount from pos_bill p inner join pos_bill pb on p.copy_bill_num = pb.bill_num and (pb.bill_state is null or pb.bill_state <> 'ZDQX02')");
//						singleRetreatSb.append(" where p .payment_time >? and p .payment_time <? and p.cashier_num =? and p .tenancy_id =? and p .store_id =? and p.bill_state = 'CJ01'");
//						SqlRowSet singleRetreatRs = posDao.query4SqlRowSet(singleRetreatSb.toString(), new Object[]
//						{ startTimestamp, endTimestamp, optNum, tenantId, organId });
//						if (singleRetreatRs.next())
//						{
//							retreat_num = singleRetreatRs.getDouble("single_retreat_count");
//							retreat_money = singleRetreatRs.getDouble("single_retreat_amount");
//						}
//						obj.put("single_retreat_count", retreat_num);
//						obj.put("single_retreat_amount", retreat_money);
//					}
//
//					String qamount = new String("select coalesce(sum(amount),'0') as amount from pos_byjdc_log where opt_num = ? and last_updatetime > ? and last_updatetime < ? and report_date = ? and store_id = ? and opt_type = ?");
//					double amount = 0;
//					SqlRowSet rsChangetAmount = posDao.query4SqlRowSet(qamount, new Object[]
//					{ optNum, startTimestamp, endTimestamp, reportDate, organId, "changet" });
//					if (rsChangetAmount.next())
//					{
//						amount = rsChangetAmount.getDouble("amount");
//					}
//					obj.put("changet_amount", amount);
//
//					// 抽大钞
//					double bills_money = 0;
//					SqlRowSet billsRs = posDao.query4SqlRowSet(qamount, new Object[]
//					{ optNum, startTimestamp, endTimestamp, reportDate, organId, "bills" });
//					if (billsRs.next())
//					{
//						bills_money = billsRs.getDouble("amount");
//					}
//					obj.put("bills", bills_money);
//					getBillItemReport(tenantId, organId, optNum,null, startTimestamp, endTimestamp, obj, reportDate);
//				}
//				if ("N".equalsIgnoreCase(isAllPos))
//				{
//					// 查询last_updatetime
//					StringBuilder str = new StringBuilder("select sd.class_item as name,pos.pos_num,pos.last_updatetime,pos.shift_id from pos_opt_state pos left join duty_order dor on dor.id = pos.shift_id ");
//					str.append(" left join sys_dictionary sd on sd.class_item_code= dor.name and sd.class_identifier_code = 'duty' ");
//					str.append(	" where pos.tenancy_id=? and pos.store_id=? and pos.report_date=? and pos.opt_num=? and  pos.content=? and pos.tag='0' order by pos.id asc");
//					SqlRowSet rs = posDao.query4SqlRowSet(str.toString(), new Object[]
//					{ tenantId, organId, reportDate, optNum, SysDictionary.OPT_STATE_KSSY });
//
//					String kssyPosNum = "";
//					if (rs.next())
//					{
//						startTimestamp = rs.getTimestamp("last_updatetime");
//						lastUpdateTime = rs.getString("last_updatetime");
//						obj.put("duty_order_name", rs.getString("name"));
//						kssyPosNum = rs.getString("pos_num");
//					}
//
//					if (StringUtils.isEmpty(lastUpdateTime))
//					{
//						throw SystemException.getInstance(PosErrorCode.POS_CASHIER_NOT_SIGNIN_ERROR);
//					}
//
//					if (!posNum.equals(kssyPosNum))
//					{
//						throw SystemException.getInstance(PosErrorCode.POSNUM_NOT_EQUALS_CURRENT_ERROR).set("{0}", kssyPosNum);
//					}
//
//					obj.put("work_datetime", lastUpdateTime.substring(0, 19) + "~" + time);
//
//
//					StringBuilder sql = new StringBuilder("select pd.*,pd2.give_amount,pd2.give_count,pd2.differ_amount from (");
//					sql.append(" select pa.jzid,(case when pw.payment_class='").append(SysDictionary.PAYMENT_CLASS_CASH).append("' then sd.class_item else pw.payment_name1 end) as payment_name,sum(pa.currency_amount) as amount,");
//					sql.append(" count(case when pa.name_english='").append(SysDictionary.PAYMENT_CHANGE_ENGLISH_NAME).append("' or bi.bill_state='").append(SysDictionary.BILL_STATE_CJ01).append("' or bi.bill_state='").append(SysDictionary.BILL_STATE_ZDQX02).append("' then null else 1 end) as count");
//					sql.append(" from pos_bill_payment pa left join payment_way pw on pa.jzid=pw.id and pa.tenancy_id=pw.tenancy_id and pw.status='1'");
//					sql.append(" left join sys_dictionary sd on pw.payment_name1 = sd.class_item_code left join pos_bill bi on bi.tenancy_id=pa.tenancy_id and bi.store_id=pa.store_id and bi.bill_num=pa.bill_num");
//					sql.append(" where bi.store_id = ? and bi.report_date = ? and bi.cashier_num=? and bi.pos_num=? and bi.payment_time > ? and bi.payment_time < ? group by pa.jzid,payment_name) pd");
//					sql.append(" left join (select jzid,coalesce(sum(give_amount),0) as give_amount,coalesce(sum(give_count),0) as give_count,coalesce(sum(differ_amount),0) as differ_amount from pos_opter_changshift");
//					sql.append(" where store_id = ? and report_date=? and cashier_num=? and pos_num = ? group by jzid) pd2");
//					sql.append(" on pd.jzid=pd2.jzid order by pd.jzid desc");
//
//					rs = posDao.query4SqlRowSet(sql.toString(), new Object[]
//					{ organId, reportDate, optNum, posNum, startTimestamp, endTimestamp, organId, reportDate, optNum, posNum });
//					while (rs.next())
//					{
//						JSONObject json = new JSONObject();
//						json.put("jzid", rs.getInt("jzid"));
//						json.put("payment_name", rs.getString("payment_name"));
//						json.put("payment_amount", rs.getDouble("amount"));
//						json.put("payment_count", rs.getInt("count"));
//						json.put("give_amount", 0);
//						json.put("give_count", 0);
//						json.put("differ_amount", 0);
//						json.put("can_modify", "Y");
//						arr.add(json);
//					}
//					obj.put("pay_item", arr);
//					double bill_amount = 0d;// 营业应收
//					double more_coupon = 0d; //多收礼券
//					double sale_person_num = 0d;//消费客数
//					double sale_billnum = 0d;//账单数
//
//					double coupon_buy_price = 0d; // 美团票券用户实付
//					double due = 0d; // 美团票券账单净收
//					double tenancy_assume = 0d; // 美团票券商户承担
//					double third_assume = 0d;	// 美团票券第三方承担
//					double third_fee = 0d; 	// 美团票券第三方服务费
//
//					StringBuilder sqlAmount = new StringBuilder("select sum(pb.discountk_amount) as discountk_amount,sum(pb.discountr_amount) as discountr_amount,sum(pb.maling_amount) as maling_amount,sum(pb.givi_amount) as givi_amount,sum(pb.more_coupon) as more_coupon,sum(coalesce(pb.shop_real_amount,0)) as shop_real_amount,sum(coalesce(pb.platform_charge_amount,0)) as platform_charge_amount,sum(coalesce(service_amount,0)) as service_amount,sum(coalesce(pb.coupon_buy_price,0)) as coupon_buy_price, sum(coalesce(pb.due,0)) as due, sum(coalesce(pb.tenancy_assume,0)) as tenancy_assume, sum(coalesce(pb.third_assume,0)) as third_assume, sum(coalesce(pb.third_fee,0)) as third_fee, ");
//					sqlAmount.append("coalesce(sum(guest), 0) as sale_person_num,");
//					sqlAmount.append("count(case when coalesce(pb.bill_state, 'zc') not in ('CJ01', 'ZDQX02') then pb.bill_num end) as sale_billnum,");
//					sqlAmount.append("sum(pb.bill_amount) as bill_amount from pos_bill pb ");
//					sqlAmount.append(" where pb.store_id = ? and pb.report_date = ? and pb.payment_time > ? and pb.payment_time < ? and pb.cashier_num = ? and pb.pos_num =? and pb.bill_property = ?");
//					SqlRowSet rsAmount = posDao.query4SqlRowSet(sqlAmount.toString(), new Object[]
//					{ organId, reportDate, startTimestamp, endTimestamp, optNum, posNum,SysDictionary.BILL_PROPERTY_CLOSED });
//					if (rsAmount.next())
//					{
//						bill_amount =  rsAmount.getDouble("bill_amount");
//						sale_person_num = rsAmount.getDouble("sale_person_num");
//						sale_billnum =  rsAmount.getDouble("sale_billnum");
//						more_coupon =  rsAmount.getDouble("more_coupon");
//
//						// 折扣
//						obj.put("discountk_amount", rsAmount.getDouble("discountk_amount"));
//						// 折让
//						obj.put("discountr_amount", rsAmount.getDouble("discountr_amount"));
//						// 抹零
//						obj.put("maling_amount", rsAmount.getDouble("maling_amount"));
//						// 奉送
//						obj.put("givi_amount", rsAmount.getDouble("givi_amount"));
//						// 多收礼券
//						obj.put("more_coupon", more_coupon);
//						// 商家实收
//						obj.put("shop_real_amount", rsAmount.getDouble("shop_real_amount"));
//						// 外卖平台收取金额
//						obj.put("platform_charge_amount", rsAmount.getDouble("platform_charge_amount"));
//						// 服务费
//						obj.put("service_amount", rsAmount.getDouble("service_amount"));
//						// 营业应收
//						obj.put("bill_amount", bill_amount);
//						//消费客数
//						obj.put("sale_person_num", sale_person_num);
//						//账单数
//						obj.put("sale_billnum", sale_billnum);
//						//人均均值
//						if(sale_person_num!=0){
//							obj.put("sale_person_average", DoubleHelper.div(bill_amount+more_coupon,sale_person_num,2));
//						}else{
//							obj.put("sale_person_average",0);
//						}
//						//账单均值
//						if(sale_person_num!=0){
//							obj.put("sale_billaverage", DoubleHelper.div(bill_amount+more_coupon,sale_billnum,2));
//						}else{
//							obj.put("sale_billaverage",0);
//						}
//
//						coupon_buy_price = rsAmount.getDouble("coupon_buy_price");
//						due = rsAmount.getDouble("due");
//						tenancy_assume = rsAmount.getDouble("tenancy_assume");
//						third_assume = rsAmount.getDouble("third_assume");
//						third_fee = rsAmount.getDouble("third_fee");
//
//						// 美团票券虚收  =  商家承担 + 第三方服务费
//						obj.put("tenancy_assume", DoubleHelper.round(tenancy_assume + third_fee, 2));
//						// 美团票券第三方承担
//						obj.put("third_assume", DoubleHelper.round(third_assume, 2));
//
//						// 顾客实付= 账单应收-第三方支付优惠(商家承担 + 第三方承担)
//						double customer_real_pay = DoubleHelper.round(bill_amount - tenancy_assume - third_assume, 2);
//						// 账单净收 = 账单应收 - 商家承担优惠 - 票券服务费
//						double bill_due = DoubleHelper.round(bill_amount - tenancy_assume - third_fee, 2) ;
//						double service_amount = rsAmount.getDouble("service_amount");
//						// 菜品净收 = 账单净收- 服务费
//						double item_actual = DoubleHelper.round(bill_due - service_amount, 2);
//
//						// 顾客实付
//						obj.put("customer_real_pay", customer_real_pay);
//						// 账单净收
//						obj.put("bill_due", bill_due);
//						// 菜品净收
//						obj.put("item_actual", item_actual);
//					}
//					double  payment_total = 0d;//营业实收  = 收款方式合计
//					StringBuffer paymentTotalSql = new StringBuffer(" select coalesce(sum(p.currency_amount), 0) as payment_total  from pos_bill_payment p LEFT JOIN pos_bill pb on  p.bill_num = pb.bill_num ");
//					paymentTotalSql.append(" where pb.store_id = ? and pb.report_date = ? and pb.payment_time > ? and pb.payment_time < ? and pb.bill_property = ?  and pb.cashier_num = ? and pb.pos_num =?");
//					SqlRowSet paymentTotalRs = posDao.query4SqlRowSet(paymentTotalSql.toString(), new Object[]
//							{ organId, reportDate, startTimestamp, endTimestamp ,SysDictionary.BILL_PROPERTY_CLOSED,optNum, posNum});
//					if(paymentTotalRs.next()){
//						payment_total = paymentTotalRs.getDouble("payment_total");
//					}
//					obj.put("payment_total",payment_total);
//					obj.put("real_amount",DoubleHelper.sub(payment_total, more_coupon, scale));
//
//					double  no_income_money = 0d;//不计收入付款方式金额
//					StringBuffer noIncomeMoneySql = new StringBuffer(" select sum(case when w.if_income='0' then coalesce(p.currency_amount, 0) else 0 end)  no_income_money  from pos_bill_payment p LEFT JOIN pos_bill pb on  p.bill_num = pb.bill_num ");
//					noIncomeMoneySql.append(" left join (select id,payment_class,if_income from payment_way where status='1') w on p.jzid = w.id");
//					noIncomeMoneySql.append(" where pb.store_id = ? and pb.report_date = ? and pb.payment_time > ? and pb.payment_time < ? and pb.bill_property = ?  and pb.cashier_num = ? and pb.pos_num =?");
//					SqlRowSet noIncomeMoneyRs = posDao.query4SqlRowSet(noIncomeMoneySql.toString(), new Object[]
//							{ organId, reportDate, startTimestamp, endTimestamp ,SysDictionary.BILL_PROPERTY_CLOSED,optNum, posNum});
//					if(noIncomeMoneyRs.next()){
//						no_income_money = noIncomeMoneyRs.getDouble("no_income_money");
//					}
//					obj.put("no_income_money",no_income_money);
//					obj.put("real_income",payment_total-no_income_money);//付款实际收入
////					StringBuffer tableDataSql = new StringBuffer("select  max(t.seat_num) as seat_num , max(t.tables_num) as tables_num");
////					tableDataSql.append("  from pos_bill pb ");
////					tableDataSql.append("left join  (select organ_id, coalesce(count(t.id),0)  as tables_num, coalesce(sum(t.seat_counts), 0) as seat_num ");
////					tableDataSql.append(" from tables_info t where t.valid_state = '1' group by organ_id ) t on pb.store_id=t.organ_id");
////					tableDataSql.append(" where pb.store_id = ? and pb.report_date = ? and pb.cashier_num = ? and pb.pos_num =? and pb.payment_time > ? and pb.payment_time < ? and pb.bill_property = ?");
////					StringBuffer tableDataSql = new StringBuffer("select organ_id, coalesce(count(t.id),0)  as tables_num, coalesce(sum(t.seat_counts), 0) as seat_num ");
////					tableDataSql.append(" from tables_info t INNER JOIN pos_bill pb ON t.organ_id = pb.store_id");
////					tableDataSql.append(" where t.valid_state = '1' AND pb.bill_amount = 0 group by organ_id");
////					tableDataSql.append(" where pb.store_id = ? and pb.report_date = ? and pb.cashier_num = ? and pb.pos_num =? and pb.payment_time > ? and pb.payment_time < ? and pb.bill_property = ?");
//
//					StringBuffer tableDataSql = new StringBuffer("select coalesce(count(t.id),0)  as tables_num, coalesce(sum(t.seat_counts), 0) as seat_num from tables_info t where t.tenancy_id=? and t.organ_id=? and t.valid_state = '1' ");
//					SqlRowSet tableDataRs = posDao.query4SqlRowSet(tableDataSql.toString(), new Object[]
//					{ tenantId, organId });
//
//					if(tableDataRs.next()){
//						double tables_num =  tableDataRs.getDouble("tables_num");//桌台数
//						double seat_num =  tableDataRs.getDouble("seat_num");//座位数
//						obj.put("tables_num", tables_num);
//						obj.put("seat_num", seat_num);
//						//翻台率 =【消费账单】/【桌台数】
//						if(tables_num != 0 ){
//							obj.put("num_ft", DoubleHelper.div(sale_billnum,tables_num,2));//翻台率
//						}else{
//							obj.put("num_ft", 0d);//翻台率
//						}
//						// 上座率 = 【消费客数】/【座位数】
//						if(seat_num != 0 ){
//							obj.put("num_sz", DoubleHelper.div(sale_person_num,seat_num,2));//上座率
//						}else{
//							obj.put("num_sz", 0d);//上座率
//						}
//					}
//					if ("1".equals(formatState) && "02".equals(formatMode))
//					{// 自助
//						obj.put("back_num", back_num);
//						obj.put("back_money", back_money);
//
//						// 单品退菜数量和金额
//						StringBuilder singleRetreatSb = new StringBuilder(
//								"select coalesce(count(b.id),'0') as single_retreat_count,coalesce(sum(bi.real_amount),'0') as single_retreat_amount from pos_bill_batch b inner join pos_bill_item bi on b.tenancy_id=bi.tenancy_id and b.store_id=bi.store_id and b.bill_num=bi.bill_num and b.batch_num=bi.batch_num");
//						singleRetreatSb.append(" where b.tenancy_id=? and b.store_id =? and b.payment_time >? and b.payment_time <? and b.cashier_num =? and b.pos_num=? and bi.item_remark='TC01' and bi.item_property in ('SINGLE','SETMEAL')");
//						// StringBuilder singleRetreatSb = new
//						// StringBuilder("select coalesce(count(p.bill_num),'0') as single_retreat_count,sum(p.payment_amount) as single_retreat_amount from pos_bill p inner join pos_bill pb on p.copy_bill_num = pb.bill_num and (pb.bill_state is null or pb.bill_state <> 'ZDQX02')");
//						// singleRetreatSb.append(" where p .payment_time >? and p .payment_time <? and p.cashier_num =? and p.pos_num=? and p .tenancy_id =? and p .store_id =? and p.bill_state = 'CJ01'");
//						SqlRowSet singleRetreatRs = posDao.query4SqlRowSet(singleRetreatSb.toString(), new Object[]
//						{ tenantId, organId, startTimestamp, endTimestamp, optNum, posNum });
//						if (singleRetreatRs.next())
//						{
//							retreat_num = singleRetreatRs.getDouble("single_retreat_count");
//							retreat_money = singleRetreatRs.getDouble("single_retreat_amount");
//						}
//						obj.put("single_retreat_count", retreat_num);
//						obj.put("single_retreat_amount", retreat_money);
//					}
//					else
//					{
//						// 用来获取退款数量和退款金额
//						StringBuilder qpaidMoney = new StringBuilder("select count(bi.id) as back_num,sum(bi.payment_amount) as back_money from pos_bill bi");
//						qpaidMoney.append(" inner join pos_bill bi2 on bi.bill_num=bi2.copy_bill_num and bi.store_id=bi2.store_id and bi.bill_state='ZDQX02' and bi.bill_property='CLOSED' and bi2.bill_state='CJ01' and bi2.bill_property='CLOSED'");
//						qpaidMoney.append(" where bi2.payment_time>? and bi2.payment_time<? and bi2.store_id = ?  ");
//						qpaidMoney.append(" and bi2.cashier_num =? and bi2.pos_num =?");
//						SqlRowSet qrst = posDao.query4SqlRowSet(qpaidMoney.toString(), new Object[]
//						{ startTimestamp, endTimestamp, organId, optNum, posNum });
//						if (qrst.next())
//						{
//							back_num = qrst.getDouble("back_num");
//							back_money = qrst.getDouble("back_money");
//						}
//						obj.put("back_num", back_num);
//						obj.put("back_money", back_money);
//
//						// 单品退菜数量和金额
//						StringBuilder singleRetreatSb = new StringBuilder(
//								"select coalesce(count(p.bill_num),'0') as single_retreat_count,sum(p.payment_amount) as single_retreat_amount from pos_bill p inner join pos_bill pb on p.copy_bill_num = pb.bill_num and (pb.bill_state is null or pb.bill_state <> 'ZDQX02')");
//						singleRetreatSb.append(" where p .payment_time >? and p .payment_time <? and p.cashier_num =? and p.pos_num=? and p .tenancy_id =? and p .store_id =? and p.bill_state = 'CJ01'");
//						SqlRowSet singleRetreatRs = posDao.query4SqlRowSet(singleRetreatSb.toString(), new Object[]
//						{ startTimestamp, endTimestamp, optNum, posNum, tenantId, organId });
//						if (singleRetreatRs.next())
//						{
//							retreat_num = singleRetreatRs.getDouble("single_retreat_count");
//							retreat_money = singleRetreatRs.getDouble("single_retreat_amount");
//						}
//						obj.put("single_retreat_count", retreat_num);
//						obj.put("single_retreat_amount", retreat_money);
//					}
//
//					// 零找金
//					String qamount = new String("select coalesce(sum(amount),'0') as amount from pos_byjdc_log where pos_num = ? and opt_num = ? and last_updatetime > ? and last_updatetime < ? and report_date = ? and store_id = ? and opt_type = ?");
//					double amount = 0;
//					SqlRowSet rsChangetAmount = posDao.query4SqlRowSet(qamount, new Object[]
//					{ posNum, optNum, startTimestamp, endTimestamp, reportDate, organId, "changet" });
//					if (rsChangetAmount.next())
//					{
//						amount = rsChangetAmount.getDouble("amount");
//					}
//					obj.put("changet_amount", amount);
//
//					// 抽大钞
//					double bills_amount = 0;
//					SqlRowSet billsRs = posDao.query4SqlRowSet(qamount, new Object[]
//					{ posNum, optNum, startTimestamp, endTimestamp, reportDate, organId, "bills" });
//					if (billsRs.next())
//					{
//						bills_amount = billsRs.getDouble("amount");
//					}
//					obj.put("bills", bills_amount);
//					getBillItemReport(tenantId, organId, null,posNum, startTimestamp, endTimestamp, obj, reportDate);
//				}
//
//				// 交款数据
//				StringBuilder cashierReceive = new StringBuilder();
//				cashierReceive.append(" select emp.name as opt_name,");
//				cashierReceive.append(" (case when pw.payment_class = 'cash' and d.id is not null then d.class_item else pw.payment_name1 end) as payment_name,");
//				cashierReceive.append(" coalesce (pcr.amount, '0') as receive_amount,coalesce (pbp.amount, '0') as payment_amount ");
//				cashierReceive.append(" from(  ");
//				cashierReceive.append("       select pcr.waiter_id,pcr.payment_id,sum(pcr.amount) as amount from pos_cashier_receive_log AS pcr");
//				cashierReceive.append("       where pcr.tenancy_id = ? and pcr.store_id = ? and pcr.report_date = ? and cast (pcr.cashier_id as varchar) = ? and pcr.last_updatetime > ? and pcr.last_updatetime < ? ");
//				cashierReceive.append("       group by pcr.waiter_id,pcr.payment_id ");
//				cashierReceive.append("    ) pcr left join(  ");
//				cashierReceive.append("       select pbp.cashier_num,pbp.jzid,sum(pbp.amount) as amount from pos_bill_payment as pbp");
//				cashierReceive.append("       inner join pos_opt_state_devices as osd on osd.devices_num = pbp.pos_num");
//				cashierReceive.append("       where pbp.tenancy_id = ? and pbp.store_id = ? and pbp.report_date = ? and osd.pos_num = ? and pbp.last_updatetime > ? and pbp.last_updatetime < ? ");
//				cashierReceive.append("       and exists ( select devices_code from hq_devices as hd where (show_type = 'YD' or show_type = 'PAD') and hd.devices_code = osd.devices_num ) ");
//				cashierReceive.append("       group by pbp.cashier_num,pbp.jzid ");
//				cashierReceive.append("    ) as pbp on cast (pbp.cashier_num as varchar) = cast (pcr.waiter_id as varchar) and pcr.payment_id = pbp.jzid ");
//				cashierReceive.append(" left join employee emp on pcr.waiter_id = emp.id ");
//				cashierReceive.append(" left join payment_way pw on pcr.payment_id = pw.id ");
//				cashierReceive.append(" left join payment_way_of_ogran o on pw.id = o.payment_id ");
//				cashierReceive.append(" left join sys_dictionary d on d.class_item_code = pw.payment_name1 ");
//				cashierReceive.append(" group by emp.name,payment_name,pcr.amount,pbp.amount");
//
//				JSONArray arrCashierReceive = new JSONArray();
//				SqlRowSet rsCashierReceive = posDao.query4SqlRowSet(cashierReceive.toString(), new Object[]
//				{ tenantId, organId, reportDate, optNum, startTimestamp, endTimestamp, tenantId, organId, reportDate, posNum, startTimestamp, endTimestamp });
//
//				while (rsCashierReceive.next())
//				{
//					JSONObject json = new JSONObject();
//					json.put("opt_name", rsCashierReceive.getString("opt_name"));
//					json.put("payment_name", rsCashierReceive.getString("payment_name"));
//					json.put("payment_amount", rsCashierReceive.getDouble("payment_amount"));
//					json.put("receive_amount", rsCashierReceive.getDouble("receive_amount"));
//					arrCashierReceive.add(json);
//				}
//				obj.put("receive_item", arrCashierReceive);
//
//				// 交款
//
//				StringBuilder cashierReceiveSum = new StringBuilder();
//				cashierReceiveSum.append("select payment_class,payment_name,sum(amount) as amount from (");
//				cashierReceiveSum.append("select pw.payment_class,(case when pw.payment_class = 'cash' and d.id is not null then d.class_item else pw.payment_name1 end) as payment_name,coalesce(sum(pcrl.amount),'0') as amount");
//				cashierReceiveSum.append(" from pos_cashier_receive_log pcrl left join payment_way pw on pcrl.payment_id = pw.id left join sys_dictionary d on d.class_item_code = pw.payment_name1");
//				cashierReceiveSum.append(" where pcrl.tenancy_id = ? and pcrl.store_id = ? and pcrl.last_updatetime > ? and pcrl.last_updatetime < ? and cast(pcrl.cashier_id as varchar) = ? and pcrl.pos_num = ? group by pw.payment_class,payment_name");
//				cashierReceiveSum.append(" union all select pw.payment_class,(case when pw.payment_class = 'cash' and d.id is not null then d.class_item else pw.payment_name1 end) as payment_name,0 as amount from payment_way pw left join sys_dictionary d on d.class_item_code = pw.payment_name1");
//				cashierReceiveSum.append(" where (payment_class in('card','ali_pay','wechat_pay') or (payment_class ='cash' and is_standard_money='1' )) and status='1') t group by payment_class,payment_name");
//
//				List<JSONObject> receiveSumList = posDao.query4Json(tenantId, cashierReceiveSum.toString(), new Object[]
//				{ tenantId, organId, startTimestamp, endTimestamp, optNum, posNum });
//
//				obj.put("receive_sum_list", receiveSumList);
//
//				JSONArray arrCard = new JSONArray();
//
//				//TODO 发卡
////				StringBuilder cardActivation = new StringBuilder("select coalesce(sum(deposit),'0') as deposit,coalesce(sum(sales_price),'0') as sales_price,count(id) as count_num from crm_activation_lost_list");
////				cardActivation.append(" where operat_type = ? and operator_id = ? and updatetime > ? and updatetime < ? and tenancy_id = ? and store_id = ?");
////
////				SqlRowSet rsCardActivation = posDao.query4SqlRowSet(cardActivation.toString(), new Object[]
////				{ SysDictionary.OPERAT_TYPE_FK, Integer.parseInt(optNum), startTimestamp, endTimestamp, tenantId, organId });
////
////
////				if (rsCardActivation.next())
////				{
////					int count_num = rsCardActivation.getInt("count_num");
////					if (count_num > 0)
////					{
////						customerSell = rsCardActivation.getDouble("sales_price");
////						depositActivation = rsCardActivation.getDouble("deposit");
////
////						JSONObject jsonMoney = new JSONObject();
////						jsonMoney.put("operat_type_code", SysDictionary.OPERAT_TYPE_FK);
////						jsonMoney.put("operat_type", "发卡");
////						jsonMoney.put("jzid", SysDictionary.CUSTOMER_SALECARD_MONEY);
////						jsonMoney.put("payment_name", "售卡金额");
////						jsonMoney.put("payment_amount", customerSell);
////						jsonMoney.put("give_amount", 0);
////						jsonMoney.put("differ_amount", 0);
////						jsonMoney.put("can_modify", "Y");
////						arrCard.add(jsonMoney);
////
////						JSONObject jsonDeposit = new JSONObject();
////						jsonDeposit.put("operat_type_code", SysDictionary.OPERAT_TYPE_FK);
////						jsonDeposit.put("operat_type", "发卡");
////						jsonDeposit.put("jzid", SysDictionary.CUSTOMER_DEPOSIT_MONEY);
////						jsonDeposit.put("payment_name", "押金金额");
////						jsonDeposit.put("payment_amount", depositActivation);
////						jsonDeposit.put("give_amount", 0);
////						jsonDeposit.put("differ_amount", 0);
////						jsonDeposit.put("can_modify", "Y");
////						arrCard.add(jsonDeposit);
////					}
////				}
//
//				StringBuilder cardActivation = new StringBuilder("");
//				cardActivation.append(" select pl.payment_id,pl.payment_name,sum(pl.deposit) as deposit,sum(pl.sales_price) as sales_price from (");
//				cardActivation.append(" select pl.payment_id,(case when pw.payment_class = 'cash' and sd.id is not null then sd.class_item else pw.payment_name1 end) payment_name,coalesce(tl.deposit,0) as deposit,(coalesce(pl.pay_money,0)-coalesce(tl.deposit,0)) as sales_price");
//				cardActivation.append(" from crm_card_trading_list tl left join crm_card_payment_list pl on tl.tenancy_id=pl.tenancy_id and tl.card_id=pl.card_id and tl.bill_code=pl.bill_code");
//				cardActivation.append(" left join payment_way pw on pl.tenancy_id=pw.tenancy_id and pl.payment_id=pw.id left join sys_dictionary sd on pw.tenancy_id=sd.tenancy_id and pw.payment_name1=sd.class_item_code and sd.class_identifier_code='currency'");
//				cardActivation.append(" where tl.operat_type=? and pl.id is not null and tl.tenancy_id=? and tl.store_id=? and tl.operator_id=? and tl.store_updatetime>? and tl.store_updatetime<?) pl group by pl.payment_id,pl.payment_name");
//
//				List<JSONObject> cardActivationList = posDao.query4Json(tenantId, cardActivation.toString(), new Object[]{SysDictionary.OPERAT_TYPE_FK,tenantId,organId,Integer.parseInt(optNum),startTimestamp, endTimestamp});
//
//				double customerSell = 0d;
//				for(JSONObject cardActivationJson :cardActivationList)
//				{
//					Double salesPrice = ParamUtil.getDoubleValueByObject(cardActivationJson, "sales_price");
//					customerSell = DoubleHelper.add(customerSell, salesPrice, 4);
//
//					JSONObject jsonMoney = new JSONObject();
//					jsonMoney.put("operat_type_code", SysDictionary.CUSTOMER_SALECARD_MONEY);
//					jsonMoney.put("operat_type", "发卡售卡金额");
//					jsonMoney.put("jzid", cardActivationJson.optInt("payment_id"));
//					jsonMoney.put("payment_name", cardActivationJson.optString("payment_name"));
//					jsonMoney.put("payment_amount", salesPrice);
//					jsonMoney.put("give_amount", 0);
//					jsonMoney.put("differ_amount", 0);
//					jsonMoney.put("can_modify", "Y");
//					arrCard.add(jsonMoney);
//				}
//
//				double depositActivation = 0d;
//				for(JSONObject cardActivationJson :cardActivationList)
//				{
//					Double deposit = ParamUtil.getDoubleValueByObject(cardActivationJson, "deposit");
//					depositActivation = DoubleHelper.add(depositActivation, deposit, 4);
//
//					JSONObject jsonMoney = new JSONObject();
//					jsonMoney.put("operat_type_code", SysDictionary.CUSTOMER_DEPOSIT_MONEY);
//					jsonMoney.put("operat_type", "发卡押金金额");
//					jsonMoney.put("jzid", cardActivationJson.optInt("payment_id"));
//					jsonMoney.put("payment_name", cardActivationJson.optString("payment_name"));
//					jsonMoney.put("payment_amount", deposit);
//					jsonMoney.put("give_amount", 0);
//					jsonMoney.put("differ_amount", 0);
//					jsonMoney.put("can_modify", "Y");
//					arrCard.add(jsonMoney);
//				}
//
//				// 充值
//				StringBuilder cardRecharge = new StringBuilder("select m.payment_id,m.operat_type_name,coalesce(sum(m.pay_money),'0') as money from (");
//				cardRecharge.append(" select ccpl.payment_id,(case when pw.payment_class = 'cash' and d.id is not null then d.class_item else pw.payment_name1 end) as operat_type_name,ccpl.pay_money from crm_card_trading_list cctl");
//				cardRecharge.append(" left join crm_card_payment_list ccpl on cctl.bill_code = ccpl.bill_code left join payment_way pw on ccpl.payment_id = pw.id left join payment_way_of_ogran o on pw.id = o.payment_id left join sys_dictionary d on d.class_item_code = pw.payment_name1");
//				cardRecharge.append(" where cctl.operat_type in ('02','04') and cctl.operator_id = ? and cctl.operate_time > ? and cctl.operate_time < ? and cctl.tenancy_id = ? and cctl.store_id = ?) m group by m.payment_id,m.operat_type_name");
//
//				SqlRowSet rsCardRecharge = posDao.query4SqlRowSet(cardRecharge.toString(), new Object[]
//				{ Integer.parseInt(optNum), startTimestamp, endTimestamp, tenantId, organId });
//				while (rsCardRecharge.next())
//				{
//					JSONObject json = new JSONObject();
//					json.put("operat_type_code", SysDictionary.OPERAT_TYPE_CZ);
//					json.put("operat_type", "充值");
//					json.put("jzid", rsCardRecharge.getString("payment_id"));
//					json.put("payment_name", rsCardRecharge.getString("operat_type_name"));
//					json.put("payment_amount", rsCardRecharge.getDouble("money"));
//					json.put("give_amount", 0);
//					json.put("differ_amount", 0);
//					json.put("can_modify", "Y");
//					arrCard.add(json);
//				}
//
//				// 消费
//				StringBuilder cardConsumeCount = new StringBuilder("select count(cctl.id) as count_num from crm_card_trading_list cctl");
//				cardConsumeCount.append(" where cctl.operat_type in ('03','05') and cctl.operator_id = ? and cctl.operate_time > ? and cctl.operate_time < ? and cctl.tenancy_id = ? and cctl.store_id = ?");
//				int consumeCount = posDao.queryForInt(cardConsumeCount.toString(), new Object[]
//				{ Integer.parseInt(optNum), startTimestamp, endTimestamp, tenantId, organId });
//				if (consumeCount > 0)
//				{
//					StringBuilder cardConsumeHaveBill = new StringBuilder("select coalesce(sum(cctl.main_trading+cctl.reward_trading),'0') as money from crm_card_trading_list cctl");
//					cardConsumeHaveBill.append(" left join pos_bill bi on cctl.tenancy_id=bi.tenancy_id and cctl.store_id=bi.store_id and (cctl.third_bill_code=bi.bill_num or cctl.third_bill_code=bi.order_num)");
//					cardConsumeHaveBill.append(" where cctl.operat_type in ('03','05') and cctl.operator_id = ? and cctl.operate_time > ? and cctl.operate_time < ? and bi.bill_num is not null and cctl.tenancy_id = ? and cctl.store_id = ?");
//					SqlRowSet rsCardConsumeHaveBill = posDao.query4SqlRowSet(cardConsumeHaveBill.toString(), new Object[]
//					{ Integer.parseInt(optNum), startTimestamp, endTimestamp, tenantId, organId });
//					if (rsCardConsumeHaveBill.next())
//					{
//						JSONObject json = new JSONObject();
//						json.put("operat_type_code", SysDictionary.OPERAT_TYPE_XF);
//						json.put("operat_type", "消费");
//						json.put("jzid", SysDictionary.CUSTOMER_DETAIL_HAVE_BILL);
//						json.put("payment_name", "有账单");
//						json.put("payment_amount", rsCardConsumeHaveBill.getDouble("money"));
//						json.put("give_amount", 0);
//						json.put("differ_amount", 0);
//						json.put("can_modify", "Y");
//						arrCard.add(json);
//					}
//
//					StringBuilder cardConsumeNoBill = new StringBuilder("select coalesce(sum(cctl.main_trading+cctl.reward_trading),'0') as money from crm_card_trading_list cctl");
//					cardConsumeNoBill.append(" left join pos_bill bi on cctl.tenancy_id=bi.tenancy_id and cctl.store_id=bi.store_id and (cctl.third_bill_code=bi.bill_num or cctl.third_bill_code=bi.order_num)");
//					cardConsumeNoBill.append(" where cctl.operat_type in ('03','05') and cctl.operator_id = ? and cctl.operate_time > ? and cctl.operate_time < ? and bi.bill_num is null and cctl.tenancy_id = ? and cctl.store_id = ?");
//					SqlRowSet rsCardConsumeNoBill = posDao.query4SqlRowSet(cardConsumeNoBill.toString(), new Object[]
//					{ Integer.parseInt(optNum), startTimestamp, endTimestamp, tenantId, organId });
//					if (rsCardConsumeNoBill.next())
//					{
//						JSONObject json = new JSONObject();
//						json.put("operat_type_code", SysDictionary.OPERAT_TYPE_XF);
//						json.put("operat_type", "消费");
//						json.put("jzid", SysDictionary.CUSTOMER_DETAIL_NO_BILL);
//						json.put("payment_name", "无账单");
//						json.put("payment_amount", rsCardConsumeNoBill.getDouble("money"));
//						json.put("give_amount", 0);
//						json.put("differ_amount", 0);
//						json.put("can_modify", "Y");
//						arrCard.add(json);
//					}
//				}
//
//				// 退卡
//				StringBuilder cardBack = new StringBuilder("select coalesce(sum(cctl.main_trading),'0') as money,coalesce(sum(cctl.deposit),'0') as deposit,count(cctl.id) as count_num from crm_card_trading_list cctl");
//				cardBack.append(" where cctl.operat_type = '12' and cctl.operator_id = ? and cctl.operate_time > ? and cctl.operate_time < ? and cctl.tenancy_id = ? and cctl.store_id = ?");
//				SqlRowSet rsCardBack = posDao.query4SqlRowSet(cardBack.toString(), new Object[]
//				{ Integer.parseInt(optNum), startTimestamp, endTimestamp, tenantId, organId });
//				double depositBack = 0d;
//				double moneyBack = 0d;
//				if (rsCardBack.next())
//				{
//					depositBack = rsCardBack.getDouble("deposit");
//					moneyBack = rsCardBack.getDouble("money");
//					if (rsCardBack.getDouble("count_num") > 0)
//					{
//						JSONObject jsonMoney = new JSONObject();
//						jsonMoney.put("operat_type_code", SysDictionary.OPERAT_TYPE_TK);
//						jsonMoney.put("operat_type", "退卡");
//						jsonMoney.put("jzid", SysDictionary.CUSTOMER_BACKCARD_MONEY);
//						jsonMoney.put("payment_name", "退卡金额");
//						jsonMoney.put("payment_amount", moneyBack);
//						jsonMoney.put("give_amount", 0);
//						jsonMoney.put("differ_amount", 0);
//						jsonMoney.put("can_modify", "Y");
//						arrCard.add(jsonMoney);
//
//						JSONObject jsonDeposit = new JSONObject();
//						jsonDeposit.put("operat_type_code", SysDictionary.OPERAT_TYPE_TK);
//						jsonDeposit.put("operat_type", "退卡");
//						jsonDeposit.put("jzid", SysDictionary.CUSTOMER_BACKDEPOSIT_MONEY);
//						jsonDeposit.put("payment_name", "退押金金额");
//						jsonDeposit.put("payment_amount", depositBack);
//						jsonDeposit.put("give_amount", 0);
//						jsonDeposit.put("differ_amount", 0);
//						jsonDeposit.put("can_modify", "Y");
//						arrCard.add(jsonDeposit);
//					}
//				}
//				// 购买会籍
//				StringBuilder gmhjSql = new StringBuilder("select  way.id as way_id, CASE WHEN  way.payment_class ='cash' THEN (SELECT sd.class_item FROM sys_dictionary sd WHERE sd.class_item_code=way.payment_name1 ) ");
//				gmhjSql.append("  ELSE way.payment_name1 END payment_name, SUM(ls.pay_money) as pay_money from crm_activity_payvip_list ls  left  JOIN payment_way way on ls.pay_id = way.id ");
//				gmhjSql.append(" where ls.pay_time>=? and ls.pay_time<=?  and ls.tenancy_id=? and ls.store_id=? ");
//				gmhjSql.append("  group by way.payment_name1,way.id,payment_class ");
//				SqlRowSet rsgmhj = posDao.query4SqlRowSet(gmhjSql.toString(), new Object[]
//						{ startTimestamp, endTimestamp, tenantId, organId });
//				while (rsgmhj.next())
//				{
//					JSONObject json = new JSONObject();
//					json.put("operat_type_code", SysDictionary.OPERAT_TYPE_GMHJ);
//					json.put("operat_type", "购买会籍");
//					json.put("jzid", rsgmhj.getString("way_id"));
//					json.put("payment_name", rsgmhj.getString("payment_name"));
//					json.put("payment_amount", rsgmhj.getDouble("pay_money"));
//					json.put("give_amount", 0);
//					json.put("differ_amount", 0);
//					json.put("can_modify", "Y");
//					arrCard.add(json);
//				}
//				obj.put("card_item", arrCard);
//
//				// 售卡金额
//				obj.put("customer_sell", customerSell);
//
//				// 充值合计，赠送合计
//				StringBuilder rechargeSb = new StringBuilder(
//						"select coalesce(sum(cctl.main_trading),'0') as recharge_money, coalesce(sum(cctl.reward_trading),'0') as reward_money from crm_card_trading_list cctl where cctl.operat_type in ('02','04') and cctl.operator_id =? and cctl.operate_time >? and cctl.operate_time <? and cctl.tenancy_id=? and cctl.store_id=?");
//				SqlRowSet rechargeRs = posDao.query4SqlRowSet(rechargeSb.toString(), new Object[]
//				{ Integer.parseInt(optNum), startTimestamp, endTimestamp, tenantId, organId });
//				if (rechargeRs.next())
//				{
//					recharge_money = rechargeRs.getDouble("recharge_money");
//					reward_money = rechargeRs.getDouble("reward_money");
//				}
//				obj.put("customer_recharge", recharge_money);
//				obj.put("customer_reward", reward_money);
//
//				// 消费合计
//				StringBuilder consumeSb = new StringBuilder(
//						"select coalesce(sum(cctl.main_trading + cctl.reward_trading),'0') as consume_money from crm_card_trading_list cctl where cctl.operat_type in ('03','05') and cctl.operator_id =? and cctl.operate_time >? and cctl.operate_time <? and cctl.tenancy_id=? and cctl.store_id=?");
//				SqlRowSet consumeRs = posDao.query4SqlRowSet(consumeSb.toString(), new Object[]
//				{ Integer.parseInt(optNum), startTimestamp, endTimestamp, tenantId, organId });
//				if (consumeRs.next())
//				{
//					consume_money = consumeRs.getDouble("consume_money");
//				}
//				obj.put("customer_consume", consume_money);
//
//				// 退卡金额
//				StringBuilder backoutSb = new StringBuilder("select coalesce(sum(cctl.main_trading),'0') as backout_money from crm_card_trading_list cctl where cctl.operat_type='12' and cctl.operator_id =? and cctl.operate_time >? and cctl.operate_time <? and cctl.tenancy_id=? and cctl.store_id=?");
//				SqlRowSet backoutRs = posDao.query4SqlRowSet(backoutSb.toString(), new Object[]
//				{ Integer.parseInt(optNum), startTimestamp, endTimestamp, tenantId, organId });
//				if (backoutRs.next())
//				{
//					backout_money = Math.abs(backoutRs.getDouble("backout_money"));
//				}
//				obj.put("back_card", backout_money);
//
//				// 金额沉淀
//				double precipitate_amount = DoubleHelper.sub(DoubleHelper.sub(recharge_money, consume_money, 2), backout_money, 2);
//				obj.put("precipitate_amount", precipitate_amount);
//
//				// 未退押金金额
//				double customerDeposit = DoubleHelper.add(depositActivation, depositBack, 2);
//				obj.put("customer_deposit", customerDeposit);
//				//会员卡付款统计
//				//本系统卡
//				double total_card = 0d ;
//				//前台消费卡主帐户金额
//				double qt_xf_main_trading = 0d;
//				//前台消费卡赠送账户金额
//				double qt_xf_reward_trading =0d;
//				//【后台消费卡主帐户金额】
//				double ht_xf_main_trading = 0d;
//				//【后台消费卡赠送账户金额】
//				double ht_xf_reward_trading =0d;
////				StringBuffer loadcardSql = new StringBuffer("select coalesce(sum(case when t.third_bill_code <> '' then coalesce(main_trading, 0)end),0) as qt_xf_main_trading, ");
////				loadcardSql.append(" coalesce(sum(case when t.third_bill_code <> '' then coalesce(reward_trading, 0)end),0) as qt_xf_reward_trading ,");
////				loadcardSql.append(" coalesce(sum(case when t.third_bill_code <> '' then  coalesce(main_trading, 0)end),0) as qt_xf_main_trading,");
////				loadcardSql.append(" coalesce(sum(case when t.third_bill_code <> '' then coalesce(reward_trading, 0)end),0) as qt_xf_reward,");
////				loadcardSql.append(" coalesce(sum(case when t.third_bill_code = '' then coalesce(main_trading, 0)end),0) as ht_xf_main_trading,");
////				loadcardSql.append(" coalesce(sum(case when t.third_bill_code = '' then coalesce(reward_trading, 0)end),0) as ht_xf_reward_trading");
////				loadcardSql.append(" from crm_card_trading_list t left join crm_card_payment_list p on t.bill_code = p.bill_code and t.operat_type in ('03', '05')");
////				loadcardSql.append(" left join (select id,payment_class from payment_way where status='1') w on p.payment_id = w.id");
////				loadcardSql.append(" left join pos_bill as t3 on t.third_bill_code = t3.bill_num ");
////				loadcardSql.append(" where t.tenancy_id=? and t.store_id=? and t.operate_time>=? and t.operate_time<=?");
//
//				StringBuffer loadcardSql = new StringBuffer("select coalesce(sum(case when bi.bill_num is not null then coalesce(main_trading, 0)end),0) as qt_xf_main_trading,");
//				loadcardSql.append(" coalesce(sum(case when bi.bill_num is not null then coalesce(reward_trading, 0)end),0) as qt_xf_reward_trading,");
//				loadcardSql.append(" coalesce(sum(case when bi.bill_num is null then coalesce(main_trading, 0)end),0) as ht_xf_main_trading,");
//				loadcardSql.append(" coalesce(sum(case when bi.bill_num is null then coalesce(reward_trading, 0)end),0) as ht_xf_reward_trading");
//				loadcardSql.append(" from crm_card_trading_list tl left join pos_bill bi on tl.third_bill_code=bi.bill_num");
//				loadcardSql.append(" where tl.tenancy_id=? and tl.store_id=? and tl.operate_time>=? and tl.operate_time<=? and tl.operat_type in (?, ?) and tl.operator_id=?");
//
//				SqlRowSet loadcardRs = posDao.query4SqlRowSet(loadcardSql.toString(), new Object[]
//				{ tenantId, organId, startTimestamp, endTimestamp, SysDictionary.OPERAT_TYPE_XF, SysDictionary.OPERAT_TYPE_FXF, Integer.parseInt(optNum) });
//
//				if (loadcardRs.next())
//				{
//					qt_xf_main_trading = loadcardRs.getDouble("qt_xf_main_trading");
//					qt_xf_reward_trading = loadcardRs.getDouble("qt_xf_reward_trading");
//					total_card = DoubleHelper.add(qt_xf_main_trading, qt_xf_reward_trading, 4);
//
//					ht_xf_main_trading = loadcardRs.getDouble("ht_xf_main_trading");
//					ht_xf_reward_trading = loadcardRs.getDouble("ht_xf_reward_trading");
//				}
//
//				obj.put("total_card", total_card);
//				obj.put("qt_xf_main_trading", qt_xf_main_trading);
//				obj.put("qt_xf_reward_trading", qt_xf_reward_trading);
//				obj.put("ht_xf_main_trading", ht_xf_main_trading);
//				obj.put("ht_xf_reward_trading", ht_xf_reward_trading);
//
//				// 大类名称、大类数量、大类金额
//				StringBuffer classSql = new StringBuffer();
//				classSql.append("select hicc.itemclass_name, count(hicc.id) as numbers, round(sum(pbi.real_amount), 2) as real_amount from pos_bill_item pbi inner join (select bill_num, store_id, report_date from pos_bill where payment_time >= ? and payment_time <= ? ");
//				classSql.append(" and report_date = ? and store_id = ? and tenancy_id = ? and cashier_num = ? and pos_num = ? and bill_property = 'CLOSED') as pb on pbi.bill_num = pb.bill_num ");
//				classSql.append(" and pbi.report_date=pb.report_date and pbi.store_id =pb.store_id left join hq_item_menu_details himd on pbi.details_id = himd.id and pbi.item_id = himd.item_id left join hq_item_menu_organ himo on himd.item_menu_id = himo.item_menu_id left join ");
//				classSql.append(" hq_item_menu_class himc on himd. id = himc.details_id left join hq_item_class hic on himc. class = hic.id left join hq_item_class hicc on hic.father_id = hicc.id where hicc.valid_state = '1' and hic.valid_state = '1' and himd.valid_state = '1' and himo.store_id = ? group by hicc. id, hicc.itemclass_name ");
//
//				SqlRowSet classRows = posDao.query4SqlRowSet(classSql.toString(),
//						new Object[]{startTimestamp, endTimestamp, reportDate, organId, tenantId, optNum, posNum, organId});
//
//				JSONArray classList = new JSONArray();
//				while(classRows.next()){
//					JSONObject classItem = new JSONObject();
//					classItem.put("itemclass_name", classRows.getString("itemclass_name"));
//					classItem.put("numbers", classRows.getInt("numbers"));
//					classItem.put("real_amount", classRows.getDouble("real_amount"));
//
//					classList.add(classItem);
//				}
//				obj.put("class_list", classList);
//
//				//外卖统计
//                obj.put("meituanAmount",0);
//                obj.put("baiduAmount",0);
//                obj.put("eleAmount",0);
//                String orderSumSql="select type,sum(amount) from pos_bill_payment where type in('meituan_pay','baidu_pay','ele_pay') and store_id=? and tenancy_id=? and report_date = ? GROUP BY type";
//                SqlRowSet orderSum=posDao.query4SqlRowSet(orderSumSql,new Object[]{organId,tenantId,reportDate});
//                while (orderSum.next()){
//                    if(orderSum.getString("type").equals("meituan_pay")){
//                        obj.put("meituanAmount",orderSum.getDouble("sum"));
//                    }else if(orderSum.getString("type").equals("baidu_pay")){
//                        obj.put("baiduAmount",orderSum.getDouble("sum"));
//                    }else if(orderSum.getString("type").equals("ele_pay")){
//                        obj.put("eleAmount",orderSum.getDouble("sum"));
//                    }
//                }
//
//                obj.put("thirdOrderAmount",obj.optDouble("meituanAmount")+obj.optDouble("baiduAmount")+obj.optDouble("eleAmount"));
//
//                obj.put("meituanExceNum",0);
//                obj.put("baiduExceNum",0);
//                obj.put("eleExceNum",0);
//                String orderExceSum="select chanel,count(*) from cc_order_list where order_state='11' and store_id=? and tenancy_id=? GROUP BY chanel";
//                SqlRowSet orderExce=posDao.query4SqlRowSet(orderExceSum,new Object[]{organId,tenantId});
//                while (orderExce.next()){
//                    if(orderExce.getString("chanel").equals("MT08")){
//                        obj.put("meituanExceNum",orderExce.getInt("count"));
//                    }else if(orderExce.getString("chanel").equals("BD06")){
//                        obj.put("baiduExceNum",orderExce.getInt("count"));
//                    }else if(orderExce.getString("chanel").equals("EL09")){
//                        obj.put("eleExceNum",orderExce.getInt("count"));
//                    }
//                }
//                obj.put("thirdOrderExceNum",obj.optInt("meituanExceNum")+obj.optInt("baiduExceNum")+obj.optInt("eleExceNum"));
//
//				list.add(obj);
//				result.setData(list);
//			}
//			result.setCode(Constant.CODE_SUCCESS);
//			result.setMsg(Constant.GET_SHIFT_DATA_SUCCESS);
//		}
//		catch (SystemException se)
//		{
//			throw se;
//		}
//		catch (Exception e)
//		{
//			e.printStackTrace();
//			logger.info("获取交班数据：" + ExceptionMessage.getExceptionMessage(e));
//			throw new SystemException(PosErrorCode.OPER_ERROR);
//		}
//	}

    @Override
    public Data getDishByClass(Data param) throws SystemException {
        String tenantId = param.getTenancy_id();
        Integer organId = param.getStore_id();

        Map<String, Object> map = ReqDataUtil.getDataMap(param);

        Integer item_class_id = ParamUtil.getIntegerValue(map, "item_class_id", false, null);
        Integer item_id = ParamUtil.getIntegerValue(map, "item_id", false, null);
        Integer item_unit_id = ParamUtil.getIntegerValue(map, "item_unit_id", false, null);

        StringBuilder sql = new StringBuilder();
        sql = new StringBuilder("select distinct himd.id as details_id,himd.item_id,hii.item_code as item_num,hii.item_name,hiu.id as item_unit_id,coalesce(hip.price,hiu.standard_price) as item_price,");
        sql.append("hii.item_english, hii.phonetic_code, hii.five_code,hii.item_class as item_class_id ");
        sql.append(" from hq_item_info as hii ");
        sql.append(" left join hq_item_menu_details as himd on hii.id = himd.item_id ");
        sql.append(" left join hq_item_menu_organ as himo on himd.item_menu_id = himo.item_menu_id ");
        sql.append(" left join hq_item_menu him on himo.item_menu_id = him.id ");
        sql.append(" left join hq_item_menu_class as himc on himc.details_id = himd.id ");
        sql.append(" left join hq_item_class as hic on himc.class = hic.id ");
        sql.append(" left join hq_item_unit as hiu on hii.id = hiu.item_id and hiu.is_default = 'Y' and hiu.valid_state = '1' ");
        sql.append(" left join organ o on himo.store_id = o.id ");
        sql.append(" left join hq_item_pricesystem hip on hiu.id=hip.item_unit_id and hip.chanel=himc.chanel and hip.price_system||'' =o.price_system ");
        sql.append(" where hii.valid_state = '1' and him.valid_state = '1' and himc.chanel='MD01' and himo.store_id = ").append(organId);

        if (Tools.isNullOrEmpty(item_class_id) == false) {
            // sql = new
            // StringBuilder("SELECT himd.id as details_id,himd.item_id,hii.item_code as item_num,hii.item_name,hiu.id as item_unit_id,hiu.standard_price as item_price,");
            // sql.append("hii.item_english, hii.phonetic_code, hii.five_code,hii.item_class as item_class_id ");
            // sql.append(" FROM hq_item_info AS hii ");
            // sql.append(" left join hq_item_menu_details AS himd on hii.id = himd.item_id ");
            // sql.append(" left join hq_item_menu_organ as himo on himd.item_menu_id = himo.item_menu_id ");
            // sql.append(" left join hq_item_menu him on himo.item_menu_id = him.id ");
            // sql.append(" left join hq_item_menu_class as himc on himc.details_id = himd.id ");
            // sql.append(" left join hq_item_class as hic on himc.class = hic.id ");
            // sql.append(" LEFT JOIN hq_item_unit AS hiu ON hii.id = hiu.item_id and hiu.is_default = 'Y' and hiu.valid_state = '1' ");
            // sql.append(" WHERE hii.valid_state = '1' and hic.id=" +
            // item_class_id +" and himo.store_id = " + organId +
            // " and him.valid_state = '1' order by himd.item_id asc ");
            sql.append(" and hic.id=" + item_class_id);
        } else {
            // sql = new
            // StringBuilder("SELECT himd.id as details_id,himd.item_id,hii.item_code as item_num,hii.item_name,hiu.id as item_unit_id,hiu.standard_price as item_price,");
            // sql.append("hii.item_english, hii.phonetic_code, hii.five_code,hii.item_class as item_class_id ");
            // sql.append(" FROM hq_item_info AS hii ");
            // sql.append(" left join hq_item_menu_details AS himd on hii.id = himd.item_id ");
            // sql.append(" left join hq_item_menu_organ as himo on himd.item_menu_id = himo.item_menu_id ");
            // sql.append(" left join hq_item_menu him on himo.item_menu_id = him.id ");
            // sql.append(" left join hq_item_menu_class as himc on himc.details_id = himd.id ");
            // sql.append(" left join hq_item_class as hic on himc.class = hic.id ");
            // sql.append(" LEFT JOIN hq_item_unit AS hiu ON hii.id = hiu.item_id and hiu.is_default = 'Y' and hiu.valid_state = '1' ");
            // sql.append(" WHERE hii.valid_state = '1' and hii.id=" + item_id
            // +" and hiu.id=" + item_unit_id + " and himo.store_id = " +
            // organId +
            // " and him.valid_state = '1' order by himd.item_id asc ");
            sql.append(" and hii.id=" + item_id + " and hiu.id=" + item_unit_id);
        }

        sql.append(" order by himd.item_id asc ");

        logger.debug("getDishByClass:::" + sql.toString());

        return posDao.getDishByClass(tenantId, organId, sql.toString());
    }

    @Override
    public List<JSONObject> findLockTable(Data param) throws Exception {
        String tenantId = param.getTenancy_id();
        Integer organId = param.getStore_id();

        Map<String, Object> map = ReqDataUtil.getDataMap(param);
        // String optNum = ParamUtil.getStringValue(map, "opt_num", false,
        // null);
        String tableCode = ParamUtil.getStringValue(map, "table_code", false, null);

//		StringBuilder sql = new StringBuilder("select pt.lock_opt_num,pt.lock_pos_num from pos_tablestate pt where pt.table_code = ? and pt.store_id = ?");
//		return posDao.findLockTable(tenantId, tableCode, organId, sql.toString());
        return posDao.findLockTable(tenantId, organId, tableCode);
    }

    @Override
    public void checkPosLogin(Data param, Data result) throws SystemException {
        String tenantId = param.getTenancy_id();
        Integer organId = param.getStore_id();

        Map<String, Object> map = ReqDataUtil.getDataMap(param);
        String posNum = ParamUtil.getStringValue(map, "pos_num", true, PosErrorCode.NOT_NULL_POS_NUM);// 机号

        String optNum = ParamUtil.getStringValue(map, "opt_num", true, PosErrorCode.NOT_NULL_OPT_NUM);

        Date reportDate = ParamUtil.getDateValue(map, "report_date", false, null);

        posDao.checkPosLogin(tenantId, organId, reportDate, posNum, optNum, result);
    }

    @Override
    public void cancDayEnd(Data param, Data result) throws SystemException {
        try {
            String tenantId = param.getTenancy_id();
            Integer organId = param.getStore_id();

            Map<String, Object> map = ReqDataUtil.getDataMap(param);

            Integer shiftId = ParamUtil.getIntegerValue(map, "shift_id", false, null);

            String posNum = ParamUtil.getStringValue(map, "pos_num", false, null);// 机号

            String optNum = ParamUtil.getStringValue(map, "opt_num", false, null);

            Date reportDate = ParamUtil.getDateValue(map, "report_date", false, null);
            // 查询最大的日始时间和传入的时间做比对，如果日始时间大于打烊时间就不允许取消打烊
            String qdayBeginDate = new String("select report_date from pos_opt_state where tenancy_id=? and store_id=? and content = ? order by last_updatetime desc limit 1 ");
            Date qreportDate = null;
            SqlRowSet rst = posDao.query4SqlRowSet(qdayBeginDate, new Object[]
                    {tenantId, organId, SysDictionary.OPT_STATE_DAYBEGAIN});
            if (rst.next()) {
                qreportDate = rst.getDate("report_date");
            }

            if (Tools.isNullOrEmpty(qreportDate) == false) {
                int compCount = reportDate.compareTo(qreportDate);
                if (compCount < 0) {
                    result.setCode(Constant.CODE_INNER_EXCEPTION);
                    result.setMsg("已经做过日始，不能取消打烊");
                    return;
                }
            }

            JSONObject oob = new JSONObject();
            oob.put("day_count", DateUtil.format(reportDate));

            int dayCount = this.checkDaycount(tenantId, organId, oob);
            logger.debug("查询是否做过日结返回值为：" + dayCount);
            if (1 == dayCount) {
                result.setCode(Constant.CODE_INNER_EXCEPTION);
                result.setMsg("已经做过日结");
                return;
            }
            if (100 == dayCount) {
                result.setCode(Constant.CODE_INNER_EXCEPTION);
                result.setMsg("正在做日结");
                return;
            }

            String desql = new String("select count(id) from pos_opt_state where tenancy_id=? and store_id=? and report_date = ? and content = ?");
            int dEndCount = posDao.queryForInt(desql, new Object[]
                    {tenantId, organId, reportDate, SysDictionary.OPT_STATE_DAYEND});
            if (dEndCount == 0) {
                throw new SystemException(PosErrorCode.NOT_DAY_END_YET);
            }

            String dsql = new String("delete from pos_opt_state where tenancy_id=? and store_id=? and report_date = ? and content = ?");
            posDao.update(dsql, new Object[]
                    {tenantId, organId, reportDate, SysDictionary.OPT_STATE_DAYEND});

            JSONObject search = new JSONObject();
            search.put("tenancy_id", tenantId);
            search.put("store_id", organId);
            search.put("report_date", DateUtil.format(reportDate));
            posDao.copyDataForTable("pos_bill2", "pos_bill", search, false);
            posDao.copyDataForTable("pos_bill_item2", "pos_bill_item", search, false);
            posDao.copyDataForTable("pos_bill_payment2", "pos_bill_payment", search, false);

            String dpospb = new String("delete from pos_bill2 where report_date = ? and store_id = ? and tenancy_id = ? ");
            posDao.update(dpospb.toString(), new Object[]
                    {reportDate, organId, tenantId});

            String dpospbi = new String("delete from pos_bill_item2 where report_date = ? and store_id = ? and tenancy_id = ? ");
            posDao.update(dpospbi.toString(), new Object[]
                    {reportDate, organId, tenantId});

            String dpospbp = new String("delete from pos_bill_payment2 where report_date = ? and store_id = ? and tenancy_id = ? ");
            posDao.update(dpospbp.toString(), new Object[]
                    {reportDate, organId, tenantId});

            // write log
            posDao.savePosLog(tenantId, organId, posNum, optNum, optNum, shiftId, reportDate, Constant.TITLE, "取消打烊", "", "");

            result.setCode(Constant.CODE_SUCCESS);
            result.setMsg(Constant.CANC_DAY_END_SUCCESS);

            //bug4545门店取消打烊后进行日结问题
//			String postUrl = "/hqRest/post";
//			Map<String, String> systemMap = com.tzx.framework.common.constant.Constant.getSystemMap();
            JSONObject json = new JSONObject();
            json.put("report_date", DateUtil.format(reportDate));
            List<JSONObject> jsonList = new ArrayList<JSONObject>();
            jsonList.add(json);
            Data data = Data.get();
            data.setType(Type.CANC_DAY_END);
            data.setSecret("0");
            data.setSource("pos");
            data.setTenancy_id(tenantId);
            data.setStore_id(organId);
            data.setData(jsonList);
            logger.info("请求地址: " + PosPropertyUtil.getMsg("saas.url") + postUrl + "      请求参数: " + JSONObject.fromObject(data).toString());
            String responseResult = HttpUtil.sendPostRequest(PosPropertyUtil.getMsg("saas.url") + postUrl, JSONObject.fromObject(data).toString());
            if (responseResult != null) {
                JSONObject jsonObject = JSONObject.fromObject(responseResult);
                boolean success = jsonObject.optBoolean("success");
                if (!success) {
                    logger.error("调用总部取消打烊接口");
                    result.setCode(Constant.CODE_INNER_EXCEPTION);
                    result.setMsg(Constant.CANC_DAY_END_FAILURE);
                    return;
                }
            }
        } catch (Exception se) {
            logger.info("查询是否做过日结：" + ExceptionMessage.getExceptionMessage(se));
            se.printStackTrace();
            result.setCode(Constant.CODE_INNER_EXCEPTION);
            result.setMsg(Constant.CANC_DAY_END_FAILURE);
            return;
        }
    }

    @Override
    public int checkDaycount(String tenancyId, int storeId, JSONObject json) throws Exception {
//		String postUrl = "/hqRest/post";
//		Map<String, String> systemMap = com.tzx.framework.common.constant.Constant.getSystemMap();

        List<JSONObject> jsonList = new ArrayList<JSONObject>();
        jsonList.add(json);

        Data data = Data.get();
        data.setType(Type.DAYCOUNT_INFO);
        data.setOper(Oper.find);
        data.setTenancy_id(tenancyId);
        data.setStore_id(storeId);
        data.setData(jsonList);

        JSONObject postReq = HttpUtil.post(PosPropertyUtil.getMsg("saas.url") + postUrl, JSONObject.fromObject(data), 1, true);
        ObjectMapper objectMapper = new ObjectMapper();
        Data reqData = objectMapper.readValue(postReq.toString(), Data.class);
        List<?> reqList = reqData.getData();
        if (reqList != null && reqList.size() > 0) {
            JSONObject reqJson = JSONObject.fromObject(reqList.get(0));
            return reqJson.optInt("hq_sign");
        } else {
            return 0;
        }
    }

    @Override
    public boolean updateGiveSuggest(String tenancyId, int storeId, List<?> param) throws Exception {
        JSONObject para = JSONObject.fromObject(param.get(0));
        return posDao.updateGiveSuggest(tenancyId, storeId, para) > 0;
    }

    @Override
    public List<Version> findAppVersion(HttpServletRequest request, List<?> param) throws Exception {
        Map<String, String> systemMap = com.tzx.framework.common.constant.Constant.getSystemMap();

        Version v = new Version();
        v.setIsnewest(false);

        String result = request.getHeader("User-Agent");

        if (null != result && result.toLowerCase().contains("android")) {
            String apkPath = com.tzx.framework.common.constant.Constant.CONTEXT_PATH + com.tzx.framework.common.constant.Constant.DOWNLOAD_APP_PATH + systemMap.get("app_name").toString();

            ApkUtil apkUtil = new ApkUtil();
            apkUtil.setmAaptPath(com.tzx.framework.common.constant.Constant.CONTEXT_PATH + "WEB-INF/lib/aapt");

            ApkInfo apkInfo = apkUtil.getApkInfo(apkPath);
            v.setVersionno(apkInfo.getVersionCode());
            v.setUrl(request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + request.getContextPath() + "/" + com.tzx.framework.common.constant.Constant.DOWNLOAD_APP_PATH + systemMap.get("app_name").toString());

            if (param != null && !param.isEmpty()) {
                JSONObject paraJson = JSONObject.fromObject(param.get(0));

                if (v.getVersionno().equals(paraJson.optString("apkno"))) {
                    v.setIsnewest(true);
                }
            }
        } else {
            String filePath = com.tzx.framework.common.constant.Constant.CONTEXT_PATH + "config/version.properties";
            logger.info("***************************************************************" + filePath);

            v.setVersionno(PropertiesUtil.readProperties(filePath).getProperty("app.version", ""));
            v.setUrl(request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + request.getContextPath() + "/download/TZXPOS.zip");

            if (param != null && !param.isEmpty()) {
                JSONObject paraJson = JSONObject.fromObject(param.get(0));

                if (v.getVersionno().equals(paraJson.optString("apkno"))) {
                    v.setIsnewest(true);
                }
            }
        }

        List<Version> list = new ArrayList<Version>();
        list.add(v);
        return list;
    }

    @Override
    public synchronized List<JSONObject> checkDayBegain(String tenancyId, int storeId, List<?> param) throws Exception {
        if (param == null || param.isEmpty()) {
            throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
        }

        JSONObject para = JSONObject.fromObject(param.get(0));
        Date reportDate = DateUtil.parseDate(ParamUtil.getDateStringValue(para, "report_date"));
        Date beginDate = DateUtil.parseDate(para.optString("begain_date"));// 跨天营业时间

        String posNum = para.optString("pos_num");
        String optNum = para.optString("opt_num");


        if (Tools.isNullOrEmpty(reportDate)) {
            throw SystemException.getInstance(PosErrorCode.NOT_NULL_REPORT_DATE);
        }

        StringBuilder sSql = new StringBuilder("select report_date,last_updatetime from pos_opt_state where content = ? and store_id = ? and tenancy_id = ? order by report_date desc limit 1");
        SqlRowSet rs = posDao.query4SqlRowSet(sSql.toString(), new Object[]
                {SysDictionary.OPT_STATE_DAYBEGAIN, storeId, tenancyId});

        Date oldReportDate = DateUtil.parseDate(DateUtil.getNowDateYYDDMM());
        String lastUpdatetime = null;
        if (rs.next()) {
            lastUpdatetime = rs.getString("last_updatetime");
            oldReportDate = rs.getDate("report_date");
        }

        if (Tools.hv(oldReportDate)) {
            if (oldReportDate.getTime() != reportDate.getTime()) {
                throw SystemException.getInstance(PosErrorCode.CHECK_REPORT_DATE_ERROR);
            }
        }

        if (Tools.hv(lastUpdatetime)) {
            // 两次日始最小间隔小时数
            int betweenHour = 0;
            try {
                betweenHour = Integer.valueOf(posDao.getSysParameter(tenancyId, storeId, "daybegain_between"));
            } catch (Exception e) {
                throw SystemException.getInstance(PosErrorCode.DAYBEGAIN_INT_ERROR);
            }

            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Calendar c = Calendar.getInstance();
            c.add(Calendar.HOUR, -betweenHour);
            Date mergeDate = DateUtil.parseDateAll(simpleDateFormat.format(c.getTime()));


            //--------------------
            if (beginDate == null) {
                // 距离上次日始的时间间隔小于两次日始最小间隔小时数
                if (betweenHour != 0 && lastUpdatetime != null && mergeDate.getTime() < DateUtil.parseDateAll(lastUpdatetime).getTime()) {
                    throw SystemException.getInstance(PosErrorCode.DAYBEGAIN_BETWEEN_ERROR).set("{0}", betweenHour);
                }
            }
            //---------------------
        }

        Date report_date = reportDate;
        int beginCount = 0;
        int endCount = 0;

        StringBuilder queryDayStateSql = new StringBuilder("select count(id) from pos_opt_state where content = ? and report_date=? and store_id = ? and tenancy_id = ?");

        beginCount = posDao.queryForInt(queryDayStateSql.toString(), new Object[]
                {SysDictionary.OPT_STATE_DAYBEGAIN, reportDate, storeId, tenancyId});

        if (beginCount > 0) {
            endCount = posDao.queryForInt(queryDayStateSql.toString(), new Object[]
                    {SysDictionary.OPT_STATE_DAYEND, reportDate, storeId, tenancyId});
            if (endCount > 0) {
                if (Tools.hv(beginDate) && reportDate.before(beginDate)) {
                    report_date = beginDate;
                } else {
                    report_date = new Date(reportDate.getTime() + 24 * 60 * 60 * 1000);
                }

                beginCount = posDao.queryForInt(queryDayStateSql.toString(), new Object[]
                        {SysDictionary.OPT_STATE_DAYBEGAIN, report_date, storeId, tenancyId});
                if (beginCount > 0) {
                    throw SystemException.getInstance(PosErrorCode.ALREADY_EXIST_DAYBEGAIN_ERROR);
                }
            } else {
                throw SystemException.getInstance(PosErrorCode.ALREADY_EXIST_DAYBEGAIN_ERROR);
            }
        } else {
            if (Tools.hv(beginDate) && reportDate.before(beginDate)) {
                beginCount = posDao.queryForInt(queryDayStateSql.toString(), new Object[]
                        {SysDictionary.OPT_STATE_DAYBEGAIN, beginDate, storeId, tenancyId});
                if (beginCount > 0) {
                    throw SystemException.getInstance(PosErrorCode.ALREADY_EXIST_DAYBEGAIN_ERROR);
                } else {
                    report_date = beginDate;
                }
            }
        }

        StringBuilder daystr = new StringBuilder("select day_count from hq_daycount_info where tenancy_id=? and store_id=? and finish_sign='1' order by day_count desc limit 1;");
        SqlRowSet dayrs = posDao.query4SqlRowSet(daystr.toString(), new Object[]
                {tenancyId, storeId});
        Date dayCount = report_date;
        if (dayrs.next()) {
            dayCount = dayrs.getDate("day_count");
        }
        String periodstr = new String("select para_value from sys_parameter where para_code='daybegin_period' and valid_state='1'");
        SqlRowSet periodrs = posDao.query4SqlRowSet(periodstr);
        String period = "1";
        if (periodrs.next()) {
            period = periodrs.getString("para_value");
        }
        Date lastDate = new Date(dayCount.getTime() + 24 * 60 * 60 * 1000 * Integer.parseInt(period));
        if (lastDate.before(report_date)) {
            throw SystemException.getInstance(PosErrorCode.OVER_MAX_DAY_CAN_NOT_DAYBEGIN);
        }

        // GJ20150307
        String empName = posDao.getEmpNameById(optNum, tenancyId, storeId);

        // 写状态库（pos_opt_state）
        Timestamp timeStamp = DateUtil.currentTimestamp();
        StringBuilder instr = new StringBuilder("insert into pos_opt_state (tenancy_id,store_id,pos_num,content,opt_num,opt_name,report_date,last_updatetime,tag,login_number,remark) values (?,?,?,?,?,?,?,?,?,?,?)");
        List<Object[]> batchArgs = new ArrayList<Object[]>();

        StringBuilder qureySql = new StringBuilder("select count(id) from pos_opt_state where content=? and store_id = ? and tenancy_id = ?");
        int count = posDao.queryForInt(qureySql.toString(), new Object[]
                {SysDictionary.OPT_STATE_DAYBEGAIN, storeId, tenancyId});
        if (count > 0) {
            Date date = new Date(reportDate.getTime() + 24 * 60 * 60 * 1000);
            JSONObject dateJson = new JSONObject();
            dateJson.put("daybegin", DateUtil.format(date));
            dateJson.put("dayend", DateUtil.format(new Date(report_date.getTime() - 24 * 60 * 60 * 1000)));

            while (date.before(report_date)) {
                batchArgs.add(new Object[]
                        {tenancyId, storeId, posNum, SysDictionary.OPT_STATE_DAYBEGAIN, optNum, empName, date, timeStamp, "1", null, "跨天营业" + dateJson.toString()});
                batchArgs.add(new Object[]
                        {tenancyId, storeId, posNum, SysDictionary.OPT_STATE_DAYEND, optNum, empName, date, timeStamp, "1", null, "$$${\"billCount\":0,\"billItemsCount\":0,\"paymentCount\":0,\"paymentAmount\":0}"});

                date = new Date(date.getTime() + 24 * 60 * 60 * 1000);
            }
        }

        batchArgs.add(new Object[]
                {tenancyId, storeId, posNum, SysDictionary.OPT_STATE_DAYBEGAIN, optNum, empName, report_date, DateUtil.currentTimestamp(), "0", null, null});

        posDao.batchUpdate(instr.toString(), batchArgs);

        // 写日志
        posDao.savePosLog(tenancyId, storeId, posNum, optNum, empName, null, report_date, Constant.TITLE, "日始", null, null);

        try {
            //清除打印任务
            posDao.deletePosPrint(tenancyId, storeId);
            posDao.deletePosPrintList(tenancyId, storeId);

            // 清除10天前pos_print_task表中的数据
            posDao.deletePosPrintTask(tenancyId, storeId);

            // 日始时，初始化物理打印机打印序号
            posDao.initPrinterSerialNumber(tenancyId, storeId, report_date);

            // 清理15天前的打印序号数据
            posDao.deleteBeforePrinterSerial(tenancyId, storeId, report_date);

            // 删除临时的沽清
            StringBuilder dsql = new StringBuilder("delete from pos_soldout where store_id = ? and soldout_type = ?");
            posDao.update(dsql.toString(), new Object[]{storeId, "0"});

            //删除支付记录和打印任务
            posDao.execute(tenancyId, "TRUNCATE pos_bill_payment_log;TRUNCATE pos_kvs_bill;TRUNCATE pos_kvs_bill_item");

            // 删除拆台产生的虚拟桌位
            posDao.update("delete from pos_tablestate where table_open_num<> null", null);

            String syscodeValuesSql = new String("delete from SYS_CODE_VALUES where code_type=?");
            posDao.update(syscodeValuesSql.toString(), new Object[]{com.tzx.framework.common.constant.Code.BILL_BOOKED_NUM.getType()});

            EhcacheUtil.removeAllElment("currentValue", com.tzx.framework.common.constant.Code.BILL_BOOKED_NUM.getType());//删除预点单号缓存

            String bookedSql = new String("DELETE from pos_bill_booked");
            posDao.update(bookedSql.toString(), new Object[]{});

            String bookedItemSql = new String("DELETE from pos_bill_item_booked");
            posDao.update(bookedItemSql.toString(), new Object[]{});
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("删除历史数据错误", e);
        }

        List<JSONObject> ret = new ArrayList<JSONObject>();
        JSONObject json = new JSONObject();
        json.put("report_date", DateUtil.format(report_date));
        json.put("pos_num", posNum);
        ret.add(json);

        Data cjData = new Data();
        cjData.setType(Type.DAY_BEGAIN);
        cjData.setOper(Oper.add);
        cjData.setData(ret);

        String message = JSONObject.fromObject(cjData).toString();
        Comet4jUtil.sendMessage2All(Comet4jUtil.POS_ANDROID, message);
        Comet4jUtil.sendMessage2All(Comet4jUtil.POS_PC, message);

        return ret;
    }

    @Override
    public void deviceApply(String tenancyId, int storeId, List<?> param, Data result) throws Exception {
        if (param == null || param.isEmpty()) {
            throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
        }

        String loginMethod = "/framework/systemUserRest/userlogin";
        String checkMethod = "/hq/termianlContraller/storeSaveTerminal";

        HttpClient httpClient = new DefaultHttpClient();
        try {
            // 请求总部下发终端资料(2017-11-23新注册流程)
            boolean isNewRegister = StringUtils.isNotEmpty(PosPropertyUtil.loadPosSystemProperties("license"));

            JSONObject para = JSONObject.fromObject(param.get(0));
            para.put("tenentid", tenancyId);
            para.put("store_id", storeId);

            //根据pos_user_name 查找 user_name
            String  loginUserName = posDao.findUserName(tenancyId,storeId,para.optString("loginusername"));
            para.put("loginUserName", loginUserName);
            para.remove("loginusername");

            if (isNewRegister) {
                para.put("device_type", "OTHER_TERMINAL");
            } else {
                para.remove("show_type");
                para.remove("devices_properties");
            }

            httpClient.getParams().setParameter(CoreConnectionPNames.CONNECTION_TIMEOUT, 3000);
            httpClient.getParams().setParameter(CoreProtocolPNames.HTTP_CONTENT_CHARSET, "UTF-8");
            httpClient.getParams().setParameter(HttpHeaders.CONTENT_TYPE, "application/x-www-form-urlencoded");
            HttpPost httpPost = new HttpPost(PosPropertyUtil.getMsg("saas.url") + loginMethod);

            List<NameValuePair> formParams = new ArrayList<NameValuePair>(); // 创建参数队列

            for (Object obj : para.keySet()) {
                String key = String.valueOf(obj);
                formParams.add(new BasicNameValuePair(key, para.optString(key)));
            }

            httpPost.setEntity(new UrlEncodedFormEntity(formParams, "UTF-8"));

            HttpResponse r = httpClient.execute(httpPost);

            HttpEntity entity = r.getEntity();
            String responseContent = EntityUtils.toString(entity, "UTF-8");
            EntityUtils.consume(entity);

            JSONObject login = null;
            if (Tools.hv(responseContent)) {
                login = JSONObject.fromObject(responseContent);
            }
            if (null != login && login.optBoolean("success")) {
                httpPost = new HttpPost(PosPropertyUtil.getMsg("saas.url") + checkMethod);
                httpPost.setEntity(new UrlEncodedFormEntity(formParams, "UTF-8"));

                r = httpClient.execute(httpPost);

                entity = r.getEntity();
                responseContent = EntityUtils.toString(entity, "UTF-8");
                EntityUtils.consume(entity);

                logger.debug("请求返回：" + responseContent);

                JSONObject apply = JSONObject.fromObject(responseContent);

                if (null != apply && apply.optBoolean("success")) {
                    result.setMsg(Constant.DEVICE_APPLY_SUCCESS);
                    result.setCode(Constant.CODE_SUCCESS);

                    if (isNewRegister) {
                        SynchronizeBaseDataService synchronizeBaseDataService = new SynchronizeBaseDataServiceImp();
                        if (!synchronizeBaseDataService.syncData(SynchronizeBaseDataService.Type.terminal)) {
                            result.setMsg("请求下发数据失败");
                            result.setCode(Constant.CODE_AUTH_FAILURE);
                        }
                    }
                } else {
                    result.setMsg(apply.optString("msg"));
                    result.setCode(Constant.CODE_AUTH_FAILURE);
                }
            } else {
                throw SystemException.getInstance(PosErrorCode.USER_LOGIN_ERROR);
            }
        } catch (SystemException se) {
            throw se;
        } catch (Exception e) {
            e.printStackTrace();
            throw SystemException.getInstance(PosErrorCode.POS_NETWORK_CONNECTION_ERROR);
        } finally {
            httpClient.getConnectionManager().shutdown();
        }
    }

    @Override
    public JSONObject findDevice(String tenancyId, int storeId, List<?> param) throws Exception {
        JSONObject obj = new JSONObject();
        if (param == null || param.isEmpty()) {
            throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
        }

        JSONObject para = JSONObject.fromObject(param.get(0));

        int count = 0;
        String posNum = null;
        String posName = null;
        String kvsType = null;
        String kvsShowMode = null;
        String kvsTimeOut = null;

        String ip_device = new String("select count(devices_code) as dcount,devices_code,devices_name,kvs_type,show_mode,mealpreparetime from hq_devices where valid_state = '1' and devices_ip = ? and store_id = ? and tenancy_id = ? group by devices_code,devices_name,kvs_type,show_mode,mealpreparetime");
        SqlRowSet rs = posDao.query4SqlRowSet(ip_device, new Object[]
                {para.optString("devices_ip"), storeId, tenancyId});
        //判断结果集是否为空，此处产生空指针异常20170524 by kevin
        if (null != rs) {
            if (rs.next()) {
                count = rs.getInt("dcount");
                posNum = rs.getString("devices_code");
                posName = rs.getString("devices_name");
                kvsType = rs.getString("kvs_type");
                kvsShowMode = rs.getString("show_mode");
                kvsTimeOut = rs.getString("mealpreparetime");
            }
        } else {
            count = 0;
            posNum = "";
            posName = "";
            kvsType = "";
            kvsShowMode = "";
            kvsTimeOut = "";
        }
        obj.put("count", count);
        obj.put("pos_num", posNum);
        obj.put("pos_name", posName);
        obj.put("kvs_type", kvsType);
        obj.put("kvs_time_out", kvsTimeOut);
        obj.put("show_mode", kvsShowMode);
        obj.put("system_time", DateUtil.currentTimestamp().toString());

        String organSql = new String("SELECT org_short_name,third_organ_code_fin  from organ where id=? and tenancy_id=?");
        SqlRowSet orgrs = posDao.query4SqlRowSet(organSql, new Object[]{ storeId, tenancyId});
        if(orgrs.next()){
            obj.put("org_short_name", orgrs.getString("org_short_name"));
            obj.put("third_organ_code_fin", orgrs.getString("third_organ_code_fin"));
        }
        return obj;
        // int count = posDao.queryForInt(ip_device, new Object[]
        // { para.optString("devices_ip") , storeId,tenancyId });
        // return count>0;
    }

    /**
     * 校验设备
     *
     * @param param
     * @throws Exception
     */
    public void vaildDevice(Data param, Data result) throws Exception {

        JSONObject para = JSONObject.fromObject(param.getData().get(0));

        final String showType = "PC";
        final String devicesProperties = "CLIENT";

        String deviceUniqueCode = para.optString("device_unique_code");
        String deviceIp = para.optString("devices_ip");
        String deviceName = para.optString("devices_name");
        String deviceRemark = para.optString("remark");

        String tenancyId = PosPropertyUtil.loadPosSystemProperties("tenent_id");
        Integer storeId = Integer.valueOf(PosPropertyUtil.loadPosSystemProperties("store_id"));

        List<JSONObject> devices = posDao.query4Json(tenancyId, "SELECT * FROM hq_devices where device_unique_code='" + deviceUniqueCode + "'");
        if (null == devices || devices.isEmpty()) {

            //请求总部注册或更新设备信息并下发
            Data data = Data.get();
            data.setTenancy_id(tenancyId);
            data.setStore_id(storeId);
            data.setType(Type.DEVICE_APPLY);
            data.setOper(Oper.add);

            JSONObject object = new JSONObject();
            object.put("show_type", showType);
            object.put("devices_properties", devicesProperties);
            object.put("device_unique_code", deviceUniqueCode);
            object.put("devices_ip", deviceIp);
            object.put("devices_name", deviceName);
            object.put("remark", deviceRemark);

            data.setData(Arrays.asList(object));
            String reqURL = PosPropertyUtil.getMsg("saas.url") + "/hqRest/post";

            Data resData = JsonUtil.JsonToData(HttpUtil.post(reqURL, object, 3));
            if (Constant.CODE_SUCCESS != resData.getCode()) {
                result.setMsg("设备申请失败:" + resData.getMsg());
                result.setCode(500);
                return;
            }

            SynchronizeBaseDataService synchronizeBaseDataService = new SynchronizeBaseDataServiceImp();
            if (!synchronizeBaseDataService.syncData(SynchronizeBaseDataService.Type.terminal)) {
                result.setMsg("请求下发数据失败，请尝试手动下发");
                result.setCode(400);
                return;
            }
            result.setMsg("设备申请成功，请等待下发完成");
            result.setCode(100);
            return;
        } else {

        }


    }


//	@Override
//	public void checkChangShift(Data param, Data result) throws SystemException
//	{
//		String tenantId = param.getTenancy_id();
//		Integer organId = param.getStore_id();
//
//		Map<String, Object> map = ReqDataUtil.getDataMap(param);
//
//		String posNum = ParamUtil.getStringValue(map, "pos_num", false, null);// 机号
//
//		String optNum = ParamUtil.getStringValue(map, "opt_num", true, PosErrorCode.NOT_NULL_OPT_NUM);
//
//		Date reportDate = ParamUtil.getDateValue(map, "report_date", true, PosErrorCode.NOT_NULL_REPORT_DATE);
//
//		try
//		{
////			StringBuilder msql = new StringBuilder("select concat(begain_shift,'~',end_shift) as work_datetime,opt_num,pos_num,begain_shift,end_shift,changshift_id from pos_opter_changshift ");
////
//////			StringBuilder qsql = new StringBuilder("select jzid,payment_name,payment_count,payment_amount,give_count,give_amount,differ_amount,begain_shift,end_shift from pos_opter_changshift ");
////
////			SqlRowSet rs = null;
////
////			List<PosChangShift> list = new ArrayList<PosChangShift>();
//
////			if (StringUtils.isEmpty(posNum))
////			{
////				msql.append(" where opt_num=? and report_date=? and store_id = ? and tenancy_id = ? group by begain_shift,end_shift,pos_num,opt_num,work_datetime,changshift_id order by end_shift desc");
////
//////				qsql.append(" where opt_num = ? and report_date=? and begain_shift = ? and end_shift = ? and store_id = ? and tenancy_id = ? ");
////
////				rs = posDao.query4SqlRowSet(msql.toString(), new Object[]
////				{ optNum, reportDate, organId, tenantId });
////
////				while (rs.next())
////				{
////					PosChangShift shift = new PosChangShift();
////					shift.setWork_datetime(rs.getString("work_datetime"));
////					shift.setOpt_num(rs.getString("opt_num"));
////					String optName = posDao.getEmpNameById(rs.getString("opt_num"), tenantId, organId);
////					shift.setOpt_name(optName);
////					shift.setPos_num(rs.getString("pos_num"));
////
////					String startTime = rs.getTimestamp("begain_shift").toLocaleString();
////					String endTime = rs.getTimestamp("end_shift").toLocaleString();
////
////					shift.setBegain_shift(startTime);
////					shift.setEnd_shift(endTime);
////					shift.setChangshift_id(rs.getInt("changshift_id"));
////
////					List<PosPaymentWay> arr = new ArrayList<PosPaymentWay>();
////
////					Timestamp startTimestamp = null;
////					if (StringUtils.isNotEmpty(startTime))
////					{
////						startTimestamp = DateUtil.parseTimestamp(startTime);
////					}
////					Timestamp endTimestamp = null;
////					if (StringUtils.isNotEmpty(endTime))
////					{
////						endTimestamp = DateUtil.parseTimestamp(endTime);
////					}
////
//////					SqlRowSet rst = posDao.query4SqlRowSet(qsql.toString(), new Object[]
//////					{ optNum, reportDate, startTimestamp, endTimestamp, organId, tenantId });
//////
//////					while (rst.next())
//////					{
//////						PosPaymentWay way = new PosPaymentWay();
//////						way.setJzid(rst.getInt("jzid"));
//////						way.setPayment_name(rst.getString("payment_name"));
//////						way.setPayment_count(rst.getInt("payment_count"));
//////						way.setPayment_amount(rst.getDouble("payment_amount"));
//////						way.setGive_count(rst.getInt("give_count"));
//////						way.setGive_amount(rst.getDouble("give_amount"));
//////						way.setDiffer_amount(rst.getDouble("differ_amount"));
//////						way.setBegain_shift(rst.getTimestamp("begain_shift").toLocaleString());
//////						way.setEnd_shift(rst.getTimestamp("end_shift").toLocaleString());
//////
//////						arr.add(way);
//////					}
////
////					// 用来获取退款数量和退款金额
////					String qpaidMoney = new String("select count(id) as back_num,sum(payment_amount) as back_money from pos_bill where bill_property='CLOSED' and bill_state='ZDQX02' and payment_time>? and payment_time<? and cashier_num = ? and store_id = ?");
////					double back_num = 0;
////					double back_money = 0;
////
////					SqlRowSet qrst = posDao.query4SqlRowSet(qpaidMoney, new Object[]
////					{ startTimestamp, endTimestamp, optNum, organId });
////					if (qrst.next())
////					{
////						back_num = qrst.getDouble("back_num");
////						back_money = qrst.getDouble("back_money");
////					}
////
////					shift.setBack_num(back_num);
////					shift.setBack_money(back_money);
////
////					String qamount = new String("select sum(amount) as amount from pos_byjdc_log where opt_num = ? and last_updatetime > ? and last_updatetime < ? and report_date = ? and store_id = ?");
////					double amount = 0;
////					SqlRowSet rsAmount = posDao.query4SqlRowSet(qamount, new Object[]
////					{ optNum, startTimestamp, endTimestamp, reportDate, organId });
////					if (rsAmount.next())
////					{
////						amount = rsAmount.getDouble("amount");
////					}
////					shift.setChanget_amount(amount);
////					shift.setItem(arr);
////
////					list.add(shift);
////				}
////
////			}
////			else
////			{
////				msql.append(" where opt_num=? and pos_num = ? and report_date=? and store_id = ? and tenancy_id = ? group by begain_shift,end_shift,pos_num,opt_num,work_datetime,changshift_id order by end_shift desc");
////
//////				qsql.append(" where opt_num = ? and pos_num = ? and report_date=? and begain_shift = ? and end_shift = ? and store_id = ? and tenancy_id = ? ");
////
////				rs = posDao.query4SqlRowSet(msql.toString(), new Object[]
////				{ optNum, posNum, reportDate, organId, tenantId });
////
////				while (rs.next())
////				{
////					PosChangShift shift = new PosChangShift();
////					shift.setWork_datetime(rs.getString("work_datetime"));
////					shift.setOpt_num(rs.getString("opt_num"));
////					String optName = posDao.getEmpNameById(rs.getString("opt_num"), tenantId, organId);
////					shift.setOpt_name(optName);
////					shift.setPos_num(rs.getString("pos_num"));
////
////					String startTime = rs.getTimestamp("begain_shift").toLocaleString();
////					String endTime = rs.getTimestamp("end_shift").toLocaleString();
////
////					shift.setBegain_shift(startTime);
////					shift.setEnd_shift(endTime);
////					shift.setChangshift_id(rs.getInt("changshift_id"));
////
////					List<PosPaymentWay> arr = new ArrayList<PosPaymentWay>();
////
////					Timestamp startTimestamp = null;
////					if (StringUtils.isNotEmpty(startTime))
////					{
////						startTimestamp = DateUtil.parseTimestamp(startTime);
////					}
////					Timestamp endTimestamp = null;
////					if (StringUtils.isNotEmpty(endTime))
////					{
////						endTimestamp = DateUtil.parseTimestamp(endTime);
////					}
////
//////					SqlRowSet rst = posDao.query4SqlRowSet(qsql.toString(), new Object[]
//////					{ optNum, posNum, reportDate, startTimestamp, endTimestamp, organId, tenantId });
//////
//////					while (rst.next())
//////					{
//////						PosPaymentWay way = new PosPaymentWay();
//////						way.setJzid(rst.getInt("jzid"));
//////						way.setPayment_name(rst.getString("payment_name"));
//////						way.setPayment_count(rst.getInt("payment_count"));
//////						way.setPayment_amount(rst.getDouble("payment_amount"));
//////						way.setGive_count(rst.getInt("give_count"));
//////						way.setGive_amount(rst.getDouble("give_amount"));
//////						way.setDiffer_amount(rst.getDouble("differ_amount"));
//////						way.setBegain_shift(rst.getTimestamp("begain_shift").toLocaleString());
//////						way.setEnd_shift(rst.getTimestamp("end_shift").toLocaleString());
//////
//////						arr.add(way);
//////					}
////
////					// 用来获取退款数量和退款金额
////					String qpaidMoney = new String("select count(id) as back_num,sum(payment_amount) as back_money from pos_bill where pos_num = ? and bill_property='CLOSED' and bill_state='ZDQX02' and payment_time>? and payment_time<? and cashier_num = ? and store_id = ?");
////					double back_num = 0;
////					double back_money = 0;
////
////					SqlRowSet qrst = posDao.query4SqlRowSet(qpaidMoney, new Object[]
////					{ posNum, startTimestamp, endTimestamp, optNum, organId });
////					if (qrst.next())
////					{
////						back_num = qrst.getDouble("back_num");
////						back_money = qrst.getDouble("back_money");
////					}
////
////					shift.setBack_num(back_num);
////					shift.setBack_money(back_money);
////
////					String qamount = new String("select sum(amount) as amount from pos_byjdc_log where pos_num = ? and opt_num = ? and last_updatetime > ? and last_updatetime < ? and report_date = ? and store_id = ?");
////					double amount = 0;
////					SqlRowSet rsAmount = posDao.query4SqlRowSet(qamount, new Object[]
////					{ posNum, optNum, startTimestamp, endTimestamp, reportDate, organId });
////					if (rsAmount.next())
////					{
////						amount = rsAmount.getDouble("amount");
////					}
////					shift.setChanget_amount(amount);
////					shift.setItem(arr);
////
////					list.add(shift);
////				}
////			}
//			List<?> dataList = posDao.getChangshiftList(tenantId, organId, reportDate, optNum, posNum);
//
//			result.setCode(Constant.CODE_SUCCESS);
//			result.setMsg(Constant.CHECK_CHANGSHIFT_SUCCESS);
//			result.setData(dataList);
//		}
//		catch (Exception se)
//		{
//			result.setCode(Constant.CODE_INNER_EXCEPTION);
//			result.setMsg(Constant.CHECK_CHANGSHIFT_FAILURE);
//			logger.info("交班查询失败，原因：" + ExceptionMessage.getExceptionMessage(se));
//			se.printStackTrace();
//		}
//	}

//	@Override
//	public void getDetailChangShift(Data param, Data result) throws SystemException
//	{
//		String tenantId = param.getTenancy_id();
//		Integer organId = param.getStore_id();
//
//		int scale = 4;
//
//		Map<String, Object> map = ReqDataUtil.getDataMap(param);
//
//		String posNum = ParamUtil.getStringValue(map, "pos_num", false, null);// 机号
//
//		String optNum = ParamUtil.getStringValue(map, "opt_num", true, PosErrorCode.NOT_NULL_OPT_NUM);
//
//		Date reportDate = ParamUtil.getDateValue(map, "report_date", true, PosErrorCode.NOT_NULL_REPORT_DATE);
//
//		String startTime = ParamUtil.getStringValue(map, "start_time", false, null);
//
//		String endTime = ParamUtil.getStringValue(map, "end_time", false, null);
//
//		String changShiftId = ParamUtil.getStringValue(map, "changshift_id", false, null);
//
//		SqlRowSet rs = null;
//
//		List<JSONObject> list = new ArrayList<JSONObject>();
//		JSONObject obj = new JSONObject();
//		//obj.put("work_datetime",reportDate);
//		obj.put("report_date",DateUtil.format(reportDate, "yyyy-MM-dd"));
//		try
//		{
//			StringBuilder querySql = new StringBuilder("select format_state, org_short_name from organ where tenancy_id=? and id=?");
//			SqlRowSet rsOrgan = posDao.query4SqlRowSet(querySql.toString(), new Object[]
//			{ tenantId, organId });
//			if (rsOrgan.next())
//			{
//				obj.put("org_short_name", rsOrgan.getString("org_short_name"));
//			}
//
//			Timestamp startTimestamp = null;
//			if (StringUtils.isNotEmpty(startTime))
//			{
//				startTimestamp = DateUtil.parseTimestamp(startTime);
//			}
//			Timestamp endTimestamp = null;
//			if (StringUtils.isNotEmpty(endTime))
//			{
//				endTimestamp = DateUtil.parseTimestamp(endTime);
//			}
//
////			StringBuilder msql = new StringBuilder("select * from pos_opter_changshift_main where tenancy_id = ? and store_id = ? and changshift_uid = ? ");
//			StringBuilder msql = new StringBuilder("select pm.*,sd.class_item as name from pos_opter_changshift_main pm ");
//			msql.append(" left join duty_order dor on dor.id = pm.shift_id left join sys_dictionary sd on sd.class_item_code= dor.name and sd.class_identifier_code = 'duty' ");
//			msql.append(" where pm.tenancy_id=? and pm.store_id=? and pm.report_date=? and pm.changshift_uid=? ");
//			rs = posDao.query4SqlRowSet(msql.toString(), new Object[]
//			{ tenantId, organId, reportDate, changShiftId });
//
//			if (rs.next())
//			{
//				// 营业数据
//				obj.put("bills", rs.getDouble("bills"));
//				obj.put("changet_amount", rs.getDouble("change"));
//				obj.put("paid_money", rs.getDouble("indemnity_amount"));
//				obj.put("refund_amount", rs.getDouble("refund_amount"));
//				obj.put("business_amount", rs.getDouble("business_amount"));
//				obj.put("discountk_amount", rs.getDouble("discountk_amount"));
//				obj.put("discountr_amount", rs.getDouble("discountr_amount"));
//				obj.put("maling_amount", rs.getDouble("maling_amount"));
//				obj.put("more_coupon", rs.getDouble("more_coupon"));
//				obj.put("givi_amount", rs.getDouble("givi_amount"));
//				obj.put("back_num", rs.getDouble("retreat_count"));
//				obj.put("back_money", rs.getDouble("retreat_amount"));
//				obj.put("single_retreat_count", rs.getDouble("single_retreat_count"));
//				obj.put("single_retreat_amount", rs.getDouble("single_retreat_amount"));
//				obj.put("shop_real_amount", rs.getDouble("shop_real_amount"));
//				obj.put("platform_charge_amount", rs.getDouble("platform_charge_amount"));
//				// 会员结算数据
//				obj.put("customer_sell", rs.getDouble("sale_card_amount"));
//				obj.put("customer_recharge", rs.getDouble("customer_recharge"));
//				obj.put("customer_consume", rs.getDouble("customer_consume"));
//				obj.put("back_card", rs.getDouble("back_card"));
//				double precipitate_amount = DoubleHelper.sub(DoubleHelper.sub(rs.getDouble("customer_recharge"), rs.getDouble("customer_consume"), 2), rs.getDouble("back_card"), 2);
//				obj.put("precipitate_amount", precipitate_amount);
//				obj.put("customer_deposit", rs.getDouble("customer_deposit"));
//				//溢缺原因备注
//				obj.put("reason", rs.getString("reason"));
//				obj.put("duty_order_name", rs.getString("name"));
//			}
//
////			StringBuilder str2 = new StringBuilder("SELECT sd.class_item AS NAME FROM pos_opter_changshift_main pm ");
////			str2.append("  LEFT JOIN duty_order dor ON dor. ID = pm.shift_id ");
////			str2.append("  left join sys_dictionary sd on sd.class_item_code= dor.name and sd.class_identifier_code = 'duty' ");
////			str2.append("  where pm.tenancy_id=? and pm.store_id=? and pm.report_date=? and pm.changshift_uid=? ");
////			SqlRowSet rs2 = posDao.query4SqlRowSet(str2.toString(), new Object[]
////			{ tenantId, organId, reportDate, changShiftId });
////			if (rs2.next())
////			{
////				obj.put("duty_order_name", rs2.getString("name"));
////			}
//
//			// 营业数据
//			StringBuilder changshiftsql = new StringBuilder("select payment_name,payment_count,payment_amount,give_count,give_amount,differ_amount,coalesce(sort_num,'0') sort_num,coalesce(parent_sort_num,'0') parent_sort_num from pos_opter_changshift where tenancy_id = ? and store_id = ? and changshift_id = ? order by sort_num ");
//			rs = posDao.query4SqlRowSet(changshiftsql.toString(), new Object[]{ tenantId, organId, changShiftId });
//
//			JSONArray arr = new JSONArray();
//			while (rs.next())
//			{
//				JSONObject json = new JSONObject();
//				json.put("payment_name", rs.getString("payment_name"));
//				json.put("payment_count", rs.getInt("payment_count"));
//				json.put("payment_amount", rs.getDouble("payment_amount"));
//				json.put("give_count", rs.getInt("give_count"));
//				json.put("give_amount", rs.getDouble("give_amount"));
//				json.put("differ_amount", rs.getDouble("differ_amount"));
//				json.put("can_modify", "Y");
//				json.put("sort_num", rs.getString("sort_num"));
//				json.put("parent_sort_num", rs.getString("parent_sort_num"));
//				arr.add(json);
//			}
//			obj.put("pay_item", arr);
//
//			//TODO 会员结算数据
//			StringBuilder changshifCustsql = new StringBuilder("select case when pocc.operat_type='SK01' then '发卡售卡金额' when pocc.operat_type='YJ02' then '发卡押金金额' when pocc.operat_type='02' then '充值' when pocc.operat_type='03' then '消费' when pocc.operat_type='12' then '退卡' when pocc.operat_type='13' then '购买会籍' else '' end as operat_type,case when pocc.operat_details='YZD03' then '有账单' when pocc.operat_details='WZD04' then '无账单' when pocc.operat_details='TK05' then '退卡金额' when pocc.operat_details='TYJ06' then '退押金金额' else (case when pw.payment_class = 'cash' and d.id is not null then d.class_item else pw.payment_name1 end) end as payment_name,pocc.trade_amount,pocc.real_amount,pocc.differ_amount");
//			changshifCustsql.append(" from pos_opter_changshift_customer pocc left join payment_way pw on pocc.operat_details = cast(pw.id as varchar) left join payment_way_of_ogran o on pw.id = o.payment_id left join sys_dictionary d on d.class_item_code = pw.payment_name1 where pocc.tenancy_id = ? and pocc.store_id = ? and pocc.changshift_id = ? order by operat_type,pocc.operat_details");
//			rs = posDao.query4SqlRowSet(changshifCustsql.toString(), new Object[]{ tenantId, organId, changShiftId });
//
//			JSONArray arrCustomer = new JSONArray();
//			while (rs.next())
//			{
//				JSONObject json = new JSONObject();
//				json.put("operat_type", rs.getString("operat_type"));
//				json.put("payment_name", rs.getString("payment_name"));
//				json.put("payment_amount", rs.getDouble("trade_amount"));
//				json.put("give_amount", rs.getDouble("real_amount"));
//				json.put("differ_amount", rs.getDouble("differ_amount"));
//				json.put("can_modify", "Y");
//				arrCustomer.add(json);
//			}
//			obj.put("card_item", arrCustomer);
//
//			// 交款数据
//			StringBuilder cashierReceive = new StringBuilder();
//			cashierReceive.append(" select emp.name as opt_name,");
//			cashierReceive.append(" (case when pw.payment_class = 'cash' and d.id is not null then d.class_item else pw.payment_name1 end) as payment_name,");
//			cashierReceive.append(" coalesce (pcr.amount, '0') as receive_amount,coalesce (pcr.amount, '0') as payment_amount ");
//			cashierReceive.append(" from (  ");
//			cashierReceive.append("       select pbp.cashier_num,pbp.jzid,sum (pbp.amount) as amount from v_pos_bill_payment as pbp");
//			cashierReceive.append("       inner join pos_opt_state_devices as osd on osd.devices_num = pbp.pos_num");
//			cashierReceive.append("       where pbp.tenancy_id = ? and pbp.store_id = ? and pbp.report_date = ? and osd.pos_num = ? and pbp.last_updatetime > ? and pbp.last_updatetime < ? ");
//			cashierReceive.append("       and exists ( select devices_code from hq_devices as hd where (show_type = 'YD' or show_type = 'PAD') and hd.devices_code = osd.devices_num ) ");
//			cashierReceive.append("       group by pbp.cashier_num,pbp.jzid ");
//			cashierReceive.append("    ) pbp join(  ");
//			cashierReceive.append("       select pcr.waiter_id,pcr.payment_id,sum (pcr.amount) as amount from pos_cashier_receive_log as pcr       ");
//			cashierReceive.append("       where pcr.tenancy_id = ? and pcr.store_id = ? and pcr.report_date = ? and cast (pcr.cashier_id as varchar) = ? and pcr.last_updatetime > ? and pcr.last_updatetime < ? ");
//			cashierReceive.append("       group by pcr.waiter_id,pcr.payment_id ");
//			cashierReceive.append("    ) as pcr on cast (pbp.cashier_num as varchar) = cast (pcr.waiter_id as varchar) and pcr.payment_id = pbp.jzid ");
//			cashierReceive.append(" left join employee emp on pbp.cashier_num = cast (emp.id as varchar) ");
//			cashierReceive.append(" left join payment_way pw on pbp.jzid = pw.id ");
//			cashierReceive.append(" left join payment_way_of_ogran o on pw.id = o.payment_id ");
//			cashierReceive.append(" left join sys_dictionary d on d.class_item_code = pw.payment_name1 ");
//			cashierReceive.append(" group by emp.name,payment_name,pcr.amount,pcr.amount; ");
//
//			JSONArray arrCashierReceive = new JSONArray();
//			SqlRowSet rsCashierReceive = posDao.query4SqlRowSet(cashierReceive.toString(),
//					new Object[]{tenantId,organId,reportDate,posNum,startTimestamp,endTimestamp,
//							tenantId,organId,reportDate,optNum,startTimestamp,endTimestamp});
//
//
//			while (rsCashierReceive.next())
//			{
//				JSONObject json = new JSONObject();
//				json.put("opt_name", rsCashierReceive.getString("opt_name"));
//				json.put("payment_name", rsCashierReceive.getString("payment_name"));
//				json.put("payment_amount", rsCashierReceive.getDouble("payment_amount"));
//				json.put("receive_amount", rsCashierReceive.getDouble("receive_amount"));
//				arrCashierReceive.add(json);
//			}
//			obj.put("receive_item", arrCashierReceive);
//
//			// 交款
//			StringBuilder cashierReceiveSum = new StringBuilder("select pw.payment_class,(case when pw.payment_class = 'cash' and d.id is not null then d.class_item else pw.payment_name1 end) as payment_name,coalesce(sum(pcrl.amount),'0') as amount");
//			cashierReceiveSum.append(" from pos_cashier_receive_log pcrl left join payment_way pw on pcrl.payment_id = pw.id left join sys_dictionary d on d.class_item_code = pw.payment_name1");
//			cashierReceiveSum.append(" where pcrl.tenancy_id = ? and pcrl.store_id = ? and pcrl.last_updatetime > ? and pcrl.last_updatetime < ? and cast(pcrl.cashier_id as varchar) = ? and pcrl.pos_num = ? group by pw.payment_class,payment_name");
//			List<JSONObject> receiveSumList = posDao.query4Json(tenantId, cashierReceiveSum.toString(), new Object[]{tenantId, organId, startTimestamp, endTimestamp, optNum, posNum});
//			obj.put("receive_sum_list", receiveSumList);
//
//			/**交班详细 **/
//			double bill_amount = 0d;// 营业应收
//			double more_coupon = 0d; //多收礼券
//			double sale_person_num = 0d;//消费客数
//			double sale_billnum = 0d;//账单数
//
//			double coupon_buy_price = 0d; // 美团票券用户实付
//			double due = 0d; // 美团票券账单净收
//			double tenancy_assume = 0d; // 美团票券商户承担
//			double third_assume = 0d;	// 美团票券第三方承担
//			double third_fee = 0d; 	// 美团票券第三方服务费
//
////			StringBuilder sqlAmount = new StringBuilder("select sum(pb.discountk_amount) as discountk_amount,sum(pb.discountr_amount) as discountr_amount,sum(pb.maling_amount) as maling_amount,sum(pb.givi_amount) as givi_amount,sum(pb.more_coupon) as more_coupon,sum(coalesce(pb.shop_real_amount,0)) as shop_real_amount,sum(coalesce(pb.platform_charge_amount,0)) as platform_charge_amount,sum(coalesce(service_amount,0)) as service_amount,");
//			StringBuilder sqlAmount = new StringBuilder("select sum(pb.discountk_amount) as discountk_amount,sum(pb.discountr_amount) as discountr_amount,sum(pb.maling_amount) as maling_amount,sum(pb.givi_amount) as givi_amount,sum(pb.more_coupon) as more_coupon,sum(coalesce(pb.shop_real_amount,0)) as shop_real_amount,sum(coalesce(pb.platform_charge_amount,0)) as platform_charge_amount,sum(coalesce(service_amount,0)) as service_amount,sum(coalesce(pb.coupon_buy_price,0)) as coupon_buy_price, sum(coalesce(pb.due,0)) as due, sum(coalesce(pb.tenancy_assume,0)) as tenancy_assume, sum(coalesce(pb.third_assume,0)) as third_assume, sum(coalesce(pb.third_fee,0)) as third_fee, ");
//
//			sqlAmount.append("coalesce(sum(guest), 0) as sale_person_num,");
//			sqlAmount.append("count(case when coalesce(pb.bill_state, 'zc') not in ('CJ01', 'ZDQX02') then pb.bill_num end) as sale_billnum,");
//			sqlAmount.append("sum(pb.bill_amount) as bill_amount from pos_bill pb ");
//
//			sqlAmount.append(" where pb.store_id = ? and pb.report_date = ? and pb.payment_time > ? and pb.payment_time < ? and pb.bill_property = ? and  pb.cashier_num = ? and pb.pos_num=?");
//			SqlRowSet rsAmount = posDao.query4SqlRowSet(sqlAmount.toString(), new Object[]
//			{ organId, reportDate, startTimestamp, endTimestamp ,SysDictionary.BILL_PROPERTY_CLOSED,optNum,posNum});
//			while (rsAmount.next())
//			{
//				bill_amount =  rsAmount.getDouble("bill_amount");
//				sale_person_num = rsAmount.getDouble("sale_person_num");
//				sale_billnum =  rsAmount.getDouble("sale_billnum");
//				more_coupon =  rsAmount.getDouble("more_coupon");
//				// 折扣
//				obj.put("discountk_amount", rsAmount.getDouble("discountk_amount"));
//				// 折让
//				obj.put("discountr_amount", rsAmount.getDouble("discountr_amount"));
//				// 抹零
//				obj.put("maling_amount", rsAmount.getDouble("maling_amount"));
//				// 奉送
//				obj.put("givi_amount", rsAmount.getDouble("givi_amount"));
//				// 多收礼券
//				obj.put("more_coupon", more_coupon);
//				// 商家实收
//				obj.put("shop_real_amount", rsAmount.getDouble("shop_real_amount"));
//				// 外卖平台收取金额
//				obj.put("platform_charge_amount", rsAmount.getDouble("platform_charge_amount"));
//				// 服务费
//				obj.put("service_amount", rsAmount.getDouble("service_amount"));
//				// 营业应收
//				obj.put("bill_amount", bill_amount);
//				//消费客数
//				obj.put("sale_person_num", sale_person_num);
//				//账单数
//				obj.put("sale_billnum", sale_billnum);
//				//人均均值
//				if(sale_person_num != 0){
//					obj.put("sale_person_average", DoubleHelper.div(bill_amount+more_coupon,sale_person_num,2));
//				}else{
//					obj.put("sale_person_average",0);
//				}
//				//账单均值
//				if(sale_person_num != 0){
//					obj.put("sale_billaverage", DoubleHelper.div(bill_amount+more_coupon,sale_billnum,2));
//				}else{
//					obj.put("sale_billaverage",0);
//				}
//
//				coupon_buy_price = rsAmount.getDouble("coupon_buy_price");
//				due = rsAmount.getDouble("due");
//				tenancy_assume = rsAmount.getDouble("tenancy_assume");
//				third_assume = rsAmount.getDouble("third_assume");
//				third_fee = rsAmount.getDouble("third_fee");
//
//				// 美团票券虚收  =  商家承担 + 第三方服务费
//				obj.put("tenancy_assume", DoubleHelper.round(tenancy_assume + third_fee, 2));
//				// 美团票券第三方承担
//				obj.put("third_assume", DoubleHelper.round(third_assume, 2));
//
//				// 顾客实付= 账单应收-第三方支付优惠(商家承担 + 第三方承担)
//				double customer_real_pay = DoubleHelper.round(bill_amount - tenancy_assume - third_assume, 2);
//				// 账单净收 = 账单应收 - 商家承担优惠 - 票券服务费
//				double bill_due = DoubleHelper.round(bill_amount - tenancy_assume - third_fee, 2) ;
//				double service_amount = rsAmount.getDouble("service_amount");
//				// 菜品净收 = 账单净收- 服务费
//				double item_actual = DoubleHelper.round(bill_due - service_amount, 2);
//
//				// 顾客实付
//				obj.put("customer_real_pay", customer_real_pay);
//				// 账单净收
//				obj.put("bill_due", bill_due);
//				// 菜品净收
//				obj.put("item_actual", item_actual);
//
//			}
//			double  payment_total = 0d;//营业实收  = 收款方式合计
//			StringBuffer paymentTotalSql = new StringBuffer(" select coalesce(sum(p.currency_amount), 0) as payment_total  from pos_bill_payment p LEFT JOIN pos_bill pb on  p.bill_num = pb.bill_num ");
//			paymentTotalSql.append(" where pb.store_id = ? and pb.report_date = ? and pb.payment_time > ? and pb.payment_time < ? and pb.bill_property = ? and  pb.cashier_num = ? and pb.pos_num=?");
//			SqlRowSet paymentTotalRs = posDao.query4SqlRowSet(paymentTotalSql.toString(), new Object[]
//					{ organId, reportDate, startTimestamp, endTimestamp ,SysDictionary.BILL_PROPERTY_CLOSED,optNum,posNum});
//			if(paymentTotalRs.next()){
//				payment_total = paymentTotalRs.getDouble("payment_total");
//			}
//			obj.put("payment_total",payment_total);
//			obj.put("real_amount",DoubleHelper.sub(payment_total, more_coupon, scale));
//
//			double  no_income_money = 0d;//不计收入付款方式金额
//			StringBuffer noIncomeMoneySql = new StringBuffer(" select sum(case when w.if_income='0' then coalesce(p.currency_amount, 0) else 0 end)  no_income_money  from pos_bill_payment p LEFT JOIN pos_bill pb on  p.bill_num = pb.bill_num ");
//			noIncomeMoneySql.append(" left join (select id,payment_class,if_income from payment_way where status='1') w on p.jzid = w.id");
//			noIncomeMoneySql.append(" where pb.store_id = ? and pb.report_date = ? and pb.payment_time > ? and pb.payment_time < ? and pb.bill_property = ? and  pb.cashier_num = ? and pb.pos_num =?");
//			SqlRowSet noIncomeMoneyRs = posDao.query4SqlRowSet(noIncomeMoneySql.toString(), new Object[]
//					{ organId, reportDate, startTimestamp, endTimestamp ,SysDictionary.BILL_PROPERTY_CLOSED,optNum,posNum});
//			if(noIncomeMoneyRs.next()){
//				no_income_money = noIncomeMoneyRs.getDouble("no_income_money");
//			}
//			obj.put("no_income_money",no_income_money);
//			obj.put("real_income",payment_total-no_income_money);//付款实际收入
////			StringBuffer tableDataSql = new StringBuffer("select  max(t.seat_num) as seat_num , max(t.tables_num) as tables_num");
////			tableDataSql.append("  from pos_bill pb ");
////			tableDataSql.append("left join  (select organ_id, coalesce(count(t.id),0)  as tables_num, coalesce(sum(t.seat_counts), 0) as seat_num ");
////			tableDataSql.append(" from tables_info t where t.valid_state = '1' group by organ_id ) t on pb.store_id=t.organ_id");
////			tableDataSql.append(" where pb.store_id = ? and pb.report_date = ? and pb.payment_time > ? and pb.payment_time < ? and pb.bill_property = ? and  pb.cashier_num = ? and pb.pos_num = ?");
////			StringBuffer tableDataSql = new StringBuffer("select organ_id, coalesce(count(t.id),0)  as tables_num, coalesce(sum(t.seat_counts), 0) as seat_num ");
////			tableDataSql.append(" from tables_info t INNER JOIN pos_bill pb ON t.organ_id = pb.store_id");
////			tableDataSql.append(" where t.valid_state = '1' AND pb.bill_amount = 0 group by organ_id");
//
//			StringBuffer tableDataSql = new StringBuffer("select coalesce(count(t.id),0) as tables_num, coalesce(sum(t.seat_counts), 0) as seat_num from tables_info t where t.tenancy_id=? and t.organ_id=? and t.valid_state = '1' ");
//			SqlRowSet tableDataRs = posDao.query4SqlRowSet(tableDataSql.toString(), new Object[]
//			{ tenantId, organId });
//
//			if(tableDataRs.next()){
//				double tables_num =  tableDataRs.getDouble("tables_num");//桌台数
//				double seat_num =  tableDataRs.getDouble("seat_num");//座位数
//				obj.put("tables_num", tables_num);
//				obj.put("seat_num", seat_num);
//				//翻台率 =【消费账单】/【桌台数】
//				if(tables_num != 0 ){
//					obj.put("num_ft", DoubleHelper.div(sale_billnum, tables_num, 2));//翻台率
//				}else{
//					obj.put("num_ft", 0d);//翻台率
//				}
//				// 上座率 = 【消费客数】/【座位数】
//				if(seat_num != 0 ){
//					obj.put("num_sz", DoubleHelper.div(sale_person_num,seat_num,2));//上座率
//				}else{
//					obj.put("num_sz", 0d);//上座率
//				}
//			}
//
//			double back_num = 0;
//			double back_money = 0;
//			double retreat_num = 0;
//			double retreat_money = 0;
//			double recharge_money = 0;
//			double reward_money = 0;
//			double consume_money = 0;
//			double backout_money = 0;
//			// 用来获取退款数量和退款金额
//			StringBuilder qpaidMoney = new StringBuilder("select count(bi.id) as back_num,sum(bi.payment_amount) as back_money from pos_bill bi");
//			qpaidMoney.append(" inner join pos_bill bi2 on bi.tenancy_id=bi2.tenancy_id and bi.store_id=bi2.store_id and bi.bill_num=bi2.copy_bill_num and bi.bill_state='ZDQX02' and bi.bill_property='CLOSED' and bi2.bill_state='CJ01' and bi2.bill_property='CLOSED'");
//			qpaidMoney.append(" where bi2.tenancy_id=? and bi2.store_id =? and bi2.cashier_num =? and bi2.payment_time>? and bi2.payment_time<? and bi2.pos_num = ?");
//			SqlRowSet qrst = posDao.query4SqlRowSet(qpaidMoney.toString(), new Object[]
//			{ tenantId, organId, optNum, startTimestamp, endTimestamp ,posNum});
//			if (qrst.next())
//			{
//				back_num = qrst.getDouble("back_num");
//				back_money = qrst.getDouble("back_money");
//			}
//
//			obj.put("back_num", back_num);
//			obj.put("back_money", back_money);
//
//			// 查询单品退菜数量和金额
//			StringBuilder singleRetreatSb = new StringBuilder(
//					"select coalesce(count(p.bill_num),'0') as single_retreat_count,coalesce(round(sum (p.payment_amount),2),0) as single_retreat_amount from pos_bill p inner join pos_bill pb on p.copy_bill_num = pb.bill_num and (pb.bill_state is null or pb.bill_state <> 'ZDQX02')");
//			singleRetreatSb.append(" where p .payment_time >? and p .payment_time <? and p.cashier_num =? and p .tenancy_id =? and p .store_id =? and p.bill_state = 'CJ01' and p.pos_num=?");
//			SqlRowSet singleRetreatRs = posDao.query4SqlRowSet(singleRetreatSb.toString(), new Object[]
//			{ startTimestamp, endTimestamp, optNum, tenantId, organId,posNum });
//			if (singleRetreatRs.next())
//			{
//				retreat_num = singleRetreatRs.getDouble("single_retreat_count");
//				retreat_money = singleRetreatRs.getDouble("single_retreat_amount");
//			}
//			obj.put("single_retreat_count", retreat_num);
//			obj.put("single_retreat_amount", retreat_money);
//
//			String qamount = new String("select coalesce(sum(amount),'0') as amount from pos_byjdc_log where opt_num = ? and last_updatetime > ? and last_updatetime < ? and report_date = ? and store_id = ? and opt_type = ?");
//			double amount = 0;
//			SqlRowSet rsChangetAmount = posDao.query4SqlRowSet(qamount, new Object[]
//			{ optNum, startTimestamp, endTimestamp, reportDate, organId, "changet" });
//			if (rsChangetAmount.next())
//			{
//				amount = rsChangetAmount.getDouble("amount");
//			}
//			obj.put("changet_amount", amount);
//
//			// 抽大钞
//			double bills_money = 0;
//			SqlRowSet billsRs = posDao.query4SqlRowSet(qamount, new Object[]
//			{ optNum, startTimestamp, endTimestamp, reportDate, organId, "bills" });
//			if (billsRs.next())
//			{
//				bills_money = billsRs.getDouble("amount");
//			}
//			obj.put("bills", bills_money);
//			getBillItemReport(tenantId, organId, optNum,posNum, startTimestamp, endTimestamp, obj, reportDate);
//
//			// 充值合计，赠送合计
//			StringBuilder rechargeSb = new StringBuilder(
//					"select coalesce(sum(cctl.main_trading),'0') as recharge_money, coalesce(sum(cctl.reward_trading),'0') as reward_money from crm_card_trading_list cctl where cctl.operat_type in ('02','04') and cctl.operator_id =? and cctl.operate_time >? and cctl.operate_time <? and cctl.tenancy_id=? and cctl.store_id=?");
//			SqlRowSet rechargeRs = posDao.query4SqlRowSet(rechargeSb.toString(), new Object[]
//			{ Integer.parseInt(optNum), startTimestamp, endTimestamp, tenantId, organId });
//			if (rechargeRs.next())
//			{
//				recharge_money = rechargeRs.getDouble("recharge_money");
//				reward_money = rechargeRs.getDouble("reward_money");
//			}
//			obj.put("customer_recharge", recharge_money);
//			obj.put("customer_reward", reward_money);
//
//			// 消费合计
//			StringBuilder consumeSb = new StringBuilder(
//					"select coalesce(sum(cctl.main_trading + cctl.reward_trading),'0') as consume_money from crm_card_trading_list cctl where cctl.operat_type in ('03','05') and cctl.operator_id =? and cctl.operate_time >? and cctl.operate_time <? and cctl.tenancy_id=? and cctl.store_id=?");
//			SqlRowSet consumeRs = posDao.query4SqlRowSet(consumeSb.toString(), new Object[]
//			{ Integer.parseInt(optNum), startTimestamp, endTimestamp, tenantId, organId });
//			if (consumeRs.next())
//			{
//				consume_money = consumeRs.getDouble("consume_money");
//			}
//			obj.put("customer_consume", consume_money);
//
//			// 退卡金额
//			StringBuilder backoutSb = new StringBuilder("select coalesce(sum(cctl.main_trading),'0') as backout_money from crm_card_trading_list cctl where cctl.operat_type='12' and cctl.operator_id =? and cctl.operate_time >? and cctl.operate_time <? and cctl.tenancy_id=? and cctl.store_id=?");
//			SqlRowSet backoutRs = posDao.query4SqlRowSet(backoutSb.toString(), new Object[]
//			{ Integer.parseInt(optNum), startTimestamp, endTimestamp, tenantId, organId });
//			if (backoutRs.next())
//			{
//				backout_money = Math.abs(backoutRs.getDouble("backout_money"));
//			}
//			obj.put("back_card", backout_money);
//
//			// 金额沉淀
//			double precipitate_amount = DoubleHelper.sub(DoubleHelper.sub(recharge_money, consume_money, 2), backout_money, 2);
//			obj.put("precipitate_amount", precipitate_amount);
//
//
//			//会员卡付款统计
//			//本系统卡
//			double total_card = 0d ;
//			//前台消费卡主帐户金额
//			double qt_xf_main_trading = 0d;
//			//前台消费卡赠送账户金额
//			double qt_xf_reward_trading =0d;
//			//【后台消费卡主帐户金额】
//			double ht_xf_main_trading = 0d;
//			//【后台消费卡赠送账户金额】
//			double ht_xf_reward_trading =0d;
////			StringBuffer loadcardSql = new StringBuffer("select coalesce(sum(case when t.third_bill_code <> '' then coalesce(main_trading, 0)end),0) as qt_xf_main_trading, ");
////			loadcardSql.append(" coalesce(sum(case when t.third_bill_code <> '' then coalesce(reward_trading, 0)end),0) as qt_xf_reward_trading ,");
////			loadcardSql.append(" coalesce(sum(case when  t.third_bill_code <> '' then  coalesce(main_trading, 0)end),0) as qt_xf_main_trading,");
////			loadcardSql.append(" coalesce(sum(case when t.third_bill_code <> '' then coalesce(reward_trading, 0)end),0) as qt_xf_reward,");
////			loadcardSql.append(" coalesce(sum(case when t.third_bill_code = '' then coalesce(main_trading, 0)end),0) as ht_xf_main_trading,");
////			loadcardSql.append(" coalesce(sum(case when t.third_bill_code = '' then coalesce(reward_trading, 0)end),0) as ht_xf_reward_trading");
////			loadcardSql.append(" from crm_card_trading_list t left join crm_card_payment_list p on t.bill_code = p.bill_code and t.operat_type in ('03', '05') ");
////			loadcardSql.append(" left join (select id,payment_class from payment_way where status='1') w on p.payment_id = w.id ");
////			loadcardSql.append(" left join pos_bill as t3   on t.third_bill_code = t3.bill_num ");
////			loadcardSql.append(" where t.tenancy_id=? and t.store_id=? and t.operate_time>=? and t.operate_time<=?");
////
////			SqlRowSet loadcardRs = posDao.query4SqlRowSet(loadcardSql.toString(), new Object[]
////			{ tenantId, organId, startTimestamp, endTimestamp});
//
//			StringBuffer loadcardSql = new StringBuffer("select coalesce(sum(case when bi.bill_num is not null then coalesce(main_trading, 0)end),0) as qt_xf_main_trading,");
//			loadcardSql.append(" coalesce(sum(case when bi.bill_num is not null then coalesce(reward_trading, 0)end),0) as qt_xf_reward_trading,");
//			loadcardSql.append(" coalesce(sum(case when bi.bill_num is null then coalesce(main_trading, 0)end),0) as ht_xf_main_trading,");
//			loadcardSql.append(" coalesce(sum(case when bi.bill_num is null then coalesce(reward_trading, 0)end),0) as ht_xf_reward_trading");
//			loadcardSql.append(" from crm_card_trading_list tl left join pos_bill bi on tl.third_bill_code=bi.bill_num");
//			loadcardSql.append(" where tl.tenancy_id=? and tl.store_id=? and tl.operate_time>=? and tl.operate_time<=? and tl.operat_type in (?, ?) and tl.operator_id=?");
//
//			SqlRowSet loadcardRs = posDao.query4SqlRowSet(loadcardSql.toString(), new Object[]
//			{ tenantId, organId, startTimestamp, endTimestamp, SysDictionary.OPERAT_TYPE_XF, SysDictionary.OPERAT_TYPE_FXF, Integer.parseInt(optNum) });
//
//			if (loadcardRs.next())
//			{
//				qt_xf_main_trading = loadcardRs.getDouble("qt_xf_main_trading");
//				qt_xf_reward_trading = loadcardRs.getDouble("qt_xf_reward_trading");
//				total_card = DoubleHelper.add(qt_xf_main_trading, qt_xf_reward_trading, 4);
//
//				ht_xf_main_trading = loadcardRs.getDouble("ht_xf_main_trading");
//				ht_xf_reward_trading = loadcardRs.getDouble("ht_xf_reward_trading");
//			}
//
//			obj.put("total_card", total_card);
//			obj.put("qt_xf_main_trading", qt_xf_main_trading);
//			obj.put("qt_xf_reward_trading", qt_xf_reward_trading);
//			obj.put("ht_xf_main_trading", ht_xf_main_trading);
//			obj.put("ht_xf_reward_trading", ht_xf_reward_trading);
//
//			// 大类名称、大类数量、大类金额
//			StringBuffer classSql = new StringBuffer();
//			classSql.append("select hicc.itemclass_name, count(hicc.id) as numbers, round(sum(pbi.real_amount), 2) as real_amount from pos_bill_item pbi inner join (select bill_num, store_id, report_date from pos_bill where payment_time >= ? and payment_time <= ? ");
//			classSql.append(" and report_date = ? and store_id = ? and tenancy_id = ? and cashier_num = ? and pos_num = ? and bill_property = 'CLOSED') as pb on pbi.bill_num = pb.bill_num ");
//			classSql.append(" and pbi.report_date=pb.report_date and pbi.store_id =pb.store_id left join hq_item_menu_details himd on pbi.details_id = himd.id and pbi.item_id = himd.item_id left join hq_item_menu_organ himo on himd.item_menu_id = himo.item_menu_id left join ");
//			classSql.append(" hq_item_menu_class himc on himd. id = himc.details_id left join hq_item_class hic on himc. class = hic.id left join hq_item_class hicc on hic.father_id = hicc.id where hicc.valid_state = '1' and hic.valid_state = '1' and himd.valid_state = '1' and himo.store_id = ? group by hicc. id, hicc.itemclass_name ");
//
//			SqlRowSet classRows = posDao.query4SqlRowSet(classSql.toString(),
//					new Object[]{startTimestamp, endTimestamp, reportDate, organId, tenantId, optNum, posNum, organId});
//
//			JSONArray classList = new JSONArray();
//			while(classRows.next()){
//				JSONObject classItem = new JSONObject();
//				classItem.put("itemclass_name", classRows.getString("itemclass_name"));
//				classItem.put("numbers", classRows.getInt("numbers"));
//				classItem.put("real_amount", classRows.getDouble("real_amount"));
//
//				classList.add(classItem);
//			}
//			obj.put("class_list", classList);
//
//			list.add(obj);
//
//			result.setData(list);
//			result.setCode(Constant.CODE_SUCCESS);
//			result.setMsg(Constant.GET_DETAIL_CHANGSHIFT_SUCCESS);
//		}
//		catch (Exception se)
//		{
//			result.setCode(Constant.CODE_INNER_EXCEPTION);
//			result.setMsg(Constant.GET_DETAIL_CHANGSHIFT_FAILURE);
//			logger.info("历史交班明细查询失败，原因：" + ExceptionMessage.getExceptionMessage(se));
//			se.printStackTrace();
//		}
//	}

    @Override
    public void openSyy(Data param, Data result) throws SystemException {
        try {
            String tenantId = param.getTenancy_id();
            Integer organId = param.getStore_id();

            Map<String, Object> map = ReqDataUtil.getDataMap(param);

            Date report_date = ParamUtil.getDateValue(map, "report_date", true, PosErrorCode.NOT_NULL_REPORT_DATE);

            String optNum = ParamUtil.getStringValue(map, "opt_num", true, PosErrorCode.NOT_NULL_OPT_NUM);

            String posNum = ParamUtil.getStringValue(map, "pos_num", true, PosErrorCode.NOT_NULL_POS_NUM);

            Integer shiftId = ParamUtil.getIntegerValue(map, "shift_id", false, null);

            Double changet = ParamUtil.getDoubleValue(map, "changet", false, null);

            String qDayBegin = new String("select count(id) from pos_opt_state where content='DAYBEGAIN' and report_date = ? and store_id=? and tenancy_id=?");
            int bcount = posDao.queryForInt(qDayBegin, new Object[]
                    {report_date, organId, tenantId});
            if (bcount == 0) {
                throw new SystemException(PosErrorCode.NOT_EXIST_DAYBEGAIN_ERROR);
            }

            String qDayEnd = new String("select count(id) from pos_opt_state where content='DAYEND' and report_date = ? and store_id=? and tenancy_id=?");
            int ecount = posDao.queryForInt(qDayEnd, new Object[]
                    {report_date, organId, tenantId});
            if (bcount > 0 && ecount > 0) {
                throw new SystemException(PosErrorCode.NOT_EXIST_DAYBEGAIN_ERROR);
            }

            StringBuilder qureyOptStateSql = new StringBuilder("select id from pos_opt_state where tag='0' and content=? and shift_id=? and pos_num=? and report_date=? and store_id = ? and tenancy_id=?");

            SqlRowSet rs = posDao.query4SqlRowSet(qureyOptStateSql.toString(), new Object[]
                    {SysDictionary.OPT_STATE_KSSY, shiftId,posNum, report_date, organId, tenantId});
            int beginId = 0;
            if (rs.next()) {
                beginId = rs.getInt("id");
            }

            rs = posDao.query4SqlRowSet(qureyOptStateSql.toString(), new Object[]
                    {SysDictionary.OPT_STATE_YYTC, shiftId,posNum, report_date, organId, tenantId});
            int endId = 0;
            if (rs.next()) {
                endId = rs.getInt("id");
            }

            if (beginId > 0) {
                if (endId < beginId) {
                    throw new SystemException(PosErrorCode.SHIFT_ALREADY_SIGNOUT_ERROR);
                } else {
                    throw new SystemException(PosErrorCode.POS_NOT_EXIST_CHANGSHIFT_ERROR);
                }
            }

            qureyOptStateSql = new StringBuilder("select id,opt_num from pos_opt_state where tag='0' and content=? and shift_id=? and pos_num=? and report_date=? and store_id = ? and tenancy_id=?");
            rs = posDao.query4SqlRowSet(qureyOptStateSql.toString(), new Object[]{"KSSY", shiftId,posNum, report_date, organId, tenantId});
            int posBeginId = 0;
            String opt_num = "";
            if (rs.next()) {
                posBeginId = rs.getInt("id");
                opt_num = rs.getString("opt_num");
            }

            rs = posDao.query4SqlRowSet(qureyOptStateSql.toString(), new Object[]{"YYTC", shiftId,posNum, report_date, organId, tenantId});
            int posEndId = 0;
            if (rs.next()) {
                posEndId = rs.getInt("id");
            }

            if (posBeginId > 0) {
                if (posEndId < posBeginId) {
                    String optName = posDao.getEmpNameById(opt_num, tenantId, organId);
                    //throw new SystemException(PosErrorCode.POS_POSNUM_ALREADY_SIGNIN_ERROR).set("{0}", optName);
                    throw new SystemException(PosErrorCode.SHIFT_ALREADY_SIGNOUT_ERROR);
                } else {
                    throw new SystemException(PosErrorCode.POS_NOT_EXIST_CHANGSHIFT_ERROR);
                }
            }

            Timestamp timestamp = DateUtil.currentTimestamp();
            // 机台状态库
            String empName = posDao.getEmpNameById(optNum, tenantId, organId);
            // StringBuilder instr = new
            // StringBuilder("insert into pos_opt_state (tenancy_id,store_id,pos_num,content,opt_num,opt_name,report_date,last_updatetime,tag,login_number) values (?,?,?,?,?,?,?,?,?,?)");
            // posDao.update(instr.toString(), new Object[]
            // { tenantId, organId, posNum, "KSSY", optNum, empName,
            // report_date, timestamp, "0", 0 });

            posDao.saveOptState(tenantId, organId, report_date, shiftId, posNum, optNum, SysDictionary.OPT_STATE_KSSY, null, null, 1, timestamp);

            if (Tools.isNullOrEmpty(changet) == true) {
                changet = 0d;
            }
            int loginNum = 0;

            if (changet > 0d) {
                String optName = null;
                // 查询最大登录次数
                StringBuilder fcsql = new StringBuilder("select count(id) as max_num,opt_name from pos_opt_state where tag = '0' and content = '" + SysDictionary.OPT_STATE_YYDL + "' and opt_num = ? and pos_num=? and report_date = ? and store_id = ? group by opt_name");
                SqlRowSet rss = posDao.query4SqlRowSet(fcsql.toString(), new Object[]
                        {optNum, posNum, report_date, organId});
                if (rss.next()) {
                    optName = rss.getString("opt_name");
                    loginNum = rss.getInt("max_num");
                }
                String insertSql = new String("insert into pos_byjdc_log (tenancy_id,store_id,pos_num,opt_num,opt_name,opt_type,report_date,shift_id,last_updatetime,amount,opt_login_number,upload_tag) values (?,?,?,?,?,?,?,?,?,?,?,?) ");

                posDao.update(insertSql, new Object[]
                        {tenantId, organId, posNum, optNum, optName, "changet", report_date, shiftId, timestamp, changet, loginNum, 0});

            }

            try {
                //TODO 签到后调用拉单接口进行拉单，用于防止交班时的订单没有处理
                (new Thread(new Runnable() {
                    Map<String, String> systemMap = com.tzx.framework.common.constant.Constant.getSystemMap();
                    String tenancyId = systemMap.get("tenent_id");
                    int storeId = Integer.parseInt(systemMap.get("store_id"));

                    @Override
                    public void run() {
                        try {
                            ordersManagementService.batchTakeOrder(tenancyId, storeId);
                        } catch (Exception e) {
                            // TODO Auto-generated catch block
                            e.printStackTrace();
                            logger.info(e);
                            logger.info("签到后拉单接口异常>>>>>" + e);
                        }
                    }
                })).start();
            } catch (Exception e) {
                logger.info(e);
                logger.info("签到后拉单接口异常>>>>>" + e);
            }

            posDao.savePosLog(tenantId, organId, posNum, optNum, optNum, shiftId, report_date, Constant.TITLE, "收银员签到", "", empName + "签到,备用金:" + changet);
            result.setCode(Constant.CODE_SUCCESS);
            result.setMsg(Constant.OPEN_SYY_SUCCESS);
        } catch (SystemException se) {
            throw se;
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("签到：" + ExceptionMessage.getExceptionMessage(e));
            throw new SystemException(PosErrorCode.OPER_ERROR);
        }
    }

    @Override
    public void closeSyy(Data param, Data result) throws SystemException {
        try {
            String tenantId = param.getTenancy_id();
            Integer organId = param.getStore_id();

            Map<String, Object> map = ReqDataUtil.getDataMap(param);

            Date report_date = ParamUtil.getDateValue(map, "report_date", true, PosErrorCode.NOT_NULL_REPORT_DATE);

            String optNum = ParamUtil.getStringValue(map, "opt_num", true, PosErrorCode.NOT_NULL_OPT_NUM);

            String posNum = ParamUtil.getStringValue(map, "pos_num", true, PosErrorCode.NOT_NULL_POS_NUM);

            Integer shiftId = ParamUtil.getIntegerValue(map, "shift_id", false, null);

            String qDayBegin = new String("select count(id) from pos_opt_state where content=? and report_date = ? and store_id=? and tenancy_id=?");
            int bcount = posDao.queryForInt(qDayBegin, new Object[]
                    {SysDictionary.OPT_STATE_DAYBEGAIN, report_date, organId, tenantId});
            if (bcount == 0) {
                throw new SystemException(PosErrorCode.NOT_EXIST_DAYBEGAIN_ERROR);
            }

            String qDayEnd = new String("select count(id) from pos_opt_state where content=? and report_date = ? and store_id=? and tenancy_id=?");
            int ecount = posDao.queryForInt(qDayEnd, new Object[]
                    {SysDictionary.OPT_STATE_DAYEND, report_date, organId, tenantId});
            if (bcount > 0 && ecount > 0) {
                throw new SystemException(PosErrorCode.NOT_EXIST_DAYBEGAIN_ERROR);
            }

            // 获取机构业态
            String qOrgan = new String("select format_state from organ where id = ?");
            SqlRowSet rsts = posDao.query4SqlRowSet(qOrgan, new Object[]
                    {organId});
            String organCode = null;
            if (rsts.next()) {
                organCode = rsts.getString("format_state");
            }

            String format = posDao.getSysParameter(tenantId, organId, "ZCDCMS");

            if ("1".equals(organCode) && "02".equals(format)) {
                StringBuilder queryTableSql = new StringBuilder("select sd.devices_num from pos_opt_state_devices sd inner join pos_tablestate t on sd.tenancy_id=t.tenancy_id and sd.store_id=t.store_id and sd.devices_num=t.lock_pos_num");
                queryTableSql.append(" where sd.tenancy_id=? and sd.store_id=? and sd.pos_num=? and sd.is_valid='1'");
                SqlRowSet rs = posDao.query4SqlRowSet(queryTableSql.toString(), new Object[]
                        {tenantId, organId, posNum});
                if (rs.next()) {
                    throw SystemException.getInstance(PosErrorCode.POS_LOCK_NOT_ALLOWED_SIGN_OFF_ERROR).set("{0}", rs.getString("devices_num"));
                }
            }

            if ("2".equalsIgnoreCase(organCode) || "02".equals(format)) {
                // 只有快餐去查
                String daysql = new String("select count(id) as count,bill_num,open_opt,open_pos_num from pos_bill where open_pos_num = ? and open_opt = ? and report_date = ? and (bill_property='OPEN' or bill_property='SORT') and store_id = ? group by bill_num,open_opt,open_pos_num");
                SqlRowSet rsb = posDao.query4SqlRowSet(daysql, new Object[]
                        {posNum, optNum, report_date, organId});
                if (rsb.next()) {
                    int dcount = rsb.getInt("count");
                    String bill_num = rsb.getString("bill_num");
                    String open_opt = rsb.getString("open_opt");
                    String open_pos_num = rsb.getString("open_pos_num");
                    if (dcount > 0) {
                        result.setCode(Constant.CODE_INNER_EXCEPTION);
                        String openOptName = posDao.getEmpNameById(open_opt, tenantId, organId);
                        result.setMsg("有未结账单，不能签退！未结账单号：" + bill_num + ",操作员：" + openOptName + ",机台号：" + open_pos_num);
                        result.setSuccess(false);
                        return;
                    }
                }
            }

            StringBuilder qureyOptStateSql = new StringBuilder("select id,pos_num from pos_opt_state where tag='0' and content=? and shift_id=? and pos_num=? and report_date=? and store_id = ? and tenancy_id=?");

            SqlRowSet rs = posDao.query4SqlRowSet(qureyOptStateSql.toString(), new Object[]
                    {SysDictionary.OPT_STATE_KSSY, shiftId,posNum, report_date, organId, tenantId});
            int beginId = 0;
            String pos_num = "";
            if (rs.next()) {
                beginId = rs.getInt("id");
                pos_num = rs.getString("pos_num");
            }

            if (beginId == 0) {
                throw SystemException.getInstance(PosErrorCode.SHIFT_NOT_SIGNIN_ERROR);
            }

            rs = posDao.query4SqlRowSet(qureyOptStateSql.toString(), new Object[]
                    {"YYTC", shiftId, posNum,report_date, organId, tenantId});
            int endId = 0;
            if (rs.next()) {
                endId = rs.getInt("id");
            }

            if (endId > 0 && endId > beginId) {
                throw SystemException.getInstance(PosErrorCode.POS_CASHIER_ALREADY_SIGNOUT_ERROR);
            }

            if (!posNum.equals(pos_num)) {
                throw SystemException.getInstance(PosErrorCode.POS_OPEN_CLOSE_POSNUM_NOT_EQUALS).set("{0}", pos_num);
            }

            int loginNum = 0;

            // 查询最大登录次数
            StringBuilder fcsql = new StringBuilder("select count(id) as max_num,opt_name from pos_opt_state where tag = '0' and content = '" + SysDictionary.OPT_STATE_YYDL + "' and opt_num = ? and pos_num=? and report_date = ? and store_id = ? group by opt_name");
            SqlRowSet rss = posDao.query4SqlRowSet(fcsql.toString(), new Object[]
                    {optNum, posNum, report_date, organId});
            if (rss.next()) {
                loginNum = rss.getInt("max_num");
            }

            Timestamp timestamp = DateUtil.currentTimestamp();
            // 机台状态库
            String empName = posDao.getEmpNameById(optNum, tenantId, organId);

            posDao.saveOptState(tenantId, organId, report_date, shiftId, posNum, optNum, SysDictionary.OPT_STATE_YYTC, null, null, loginNum, timestamp);

            posDao.savePosLog(tenantId, organId, posNum, optNum, optNum, shiftId, report_date, Constant.TITLE, "收银员签退", "", empName + "签退");
            result.setCode(Constant.CODE_SUCCESS);
            result.setMsg(Constant.CLOSE_SYY_SUCCESS);
        } catch (SystemException se) {
            throw se;
        } catch (Exception e) {
            e.printStackTrace();
            logger.info(ExceptionMessage.getExceptionMessage(e));
            throw SystemException.getInstance(PosErrorCode.OPER_ERROR);
        }
    }

    @Override
    public void querySyy(Data param, Data result) throws Exception {
        String tenantId = param.getTenancy_id();
        Integer organId = param.getStore_id();

        Map<String, Object> map = ReqDataUtil.getDataMap(param);

        Date report_date = ParamUtil.getDateValue(map, "report_date", false, null);

        String optNum = ParamUtil.getStringValue(map, "opt_num", false, null);
        String posNum = ParamUtil.getStringValue(map, "pos_num", false, null);
        Integer shift_id = ParamUtil.getIntegerValue(map, "shift_id", false, null);//班次

        String qDayBegin = new String("select count(id) from pos_opt_state where content='DAYBEGAIN' and report_date = ? and store_id=? and tenancy_id=?");
        int bcount = posDao.queryForInt(qDayBegin, new Object[]
                {report_date, organId, tenantId});
        if (bcount == 0) {
            throw new SystemException(PosErrorCode.NOT_EXIST_DAYBEGAIN_ERROR);
        }

        String qDayEnd = new String("select count(id) from pos_opt_state where content='DAYEND' and report_date = ? and store_id=? and tenancy_id=?");
        int ecount = posDao.queryForInt(qDayEnd, new Object[]
                {report_date, organId, tenantId});
        if (bcount > 0 && ecount > 0) {
            throw new SystemException(PosErrorCode.NOT_EXIST_DAYBEGAIN_ERROR);
        }


        //豪享来修改为验证班次
        if (SysDictionary.SOURCE_MOVE.contains(param.getSource()) || SysDictionary.SOURCE_ORDER_DEVICE.equals(param.getSource())) {
            posDao.checkBindOptnum(tenantId, organId, report_date, posNum);
        } else {
           // StringBuilder qureyOptStateSql = new StringBuilder("select id,pos_num from pos_opt_state where tag='0' and content=? and opt_num=? and report_date=? and store_id = ? and tenancy_id=?");
            StringBuilder qureyOptStateSql = new StringBuilder("select id,pos_num from pos_opt_state where tag='0' and content=? and shift_id=? and pos_num=? and report_date=? and store_id = ? and tenancy_id=?");

            SqlRowSet rs = posDao.query4SqlRowSet(qureyOptStateSql.toString(), new Object[]
                    //{SysDictionary.OPT_STATE_KSSY, optNum, report_date, organId, tenantId});
                    {SysDictionary.OPT_STATE_KSSY, shift_id,posNum, report_date, organId, tenantId});
            int beginId = 0;
            String pos_num = "";
            if (rs.next()) {
                beginId = rs.getInt("id");
                pos_num = rs.getString("pos_num");
            }

            if (beginId == 0) {
                throw SystemException.getInstance(PosErrorCode.SHIFT_NOT_SIGNIN_ERROR);
            }

            rs = posDao.query4SqlRowSet(qureyOptStateSql.toString(), new Object[]
                    //{SysDictionary.OPT_STATE_YYTC, optNum, report_date, organId, tenantId});
                    {SysDictionary.OPT_STATE_YYTC, shift_id,posNum, report_date, organId, tenantId});
            int endId = 0;
            if (rs.next()) {
                endId = rs.getInt("id");
            }

            if (endId > 0 && endId > beginId) {
                throw SystemException.getInstance(PosErrorCode.SHIFT_ALREADY_SIGNOUT_ERROR);
            }

//            if (!posNum.equals(pos_num)) {
//                throw SystemException.getInstance(PosErrorCode.POSNUM_NOT_EQUALS_CURRENT_ERROR).set("{0}", pos_num);
//            }
        }
    }

    @Override
    public List<?> getStoreOptState(String tenancyId, int storeId, List<?> param) throws Exception {

        if (param == null || param.size() <= 0 || param.isEmpty()) {
            throw new SystemException(PosErrorCode.PARAM_ERROR);
        }

        JSONObject paraJson = JSONObject.fromObject(param.get(0));
        if (Tools.isNullOrEmpty(paraJson.opt("report_date"))) {
            throw new SystemException(PosErrorCode.NOT_NULL_REPORT_DATE);
        }

        Date reportDate =DateUtil.parseDate(ParamUtil.getDateStringValue(paraJson, "report_date"));

        JSONObject obj = new JSONObject();

        String qDayState = new String("select os.content,os.opt_num,os.opt_name,em.name as employee_name,os.last_updatetime from pos_opt_state os left join employee em on os.opt_num=em.id||'' where (os.content = ? or os.content = ?) and os.report_date = ? and os.store_id=? and os.tenancy_id=?");
        SqlRowSet rs = posDao.query4SqlRowSet(qDayState, new Object[]{SysDictionary.OPT_STATE_DAYBEGAIN, SysDictionary.OPT_STATE_DAYEND, reportDate, storeId, tenancyId});

        String begain_opt_num = "";
        String begain_opt_name = "";
        String end_opt_num = "";
        String end_opt_name = "";
        String begain_updatetime = "";
        String end_updatetime = "";
        while (rs.next()) {
            if (SysDictionary.OPT_STATE_DAYBEGAIN.equals(rs.getString("content"))) {
                begain_opt_num = rs.getString("opt_num");
                if (Tools.isNullOrEmpty(begain_opt_num)) {
                    begain_opt_num = "";
                }
                begain_opt_name = rs.getString("employee_name");
                if (Tools.isNullOrEmpty(begain_opt_name)) {
                    begain_opt_name = rs.getString("opt_name");
                    if (Tools.isNullOrEmpty(begain_opt_name)) {
                        begain_opt_name = "";
                    }
                }
                begain_updatetime = rs.getString("last_updatetime");
            } else if (SysDictionary.OPT_STATE_DAYEND.equals(rs.getString("content"))) {
                end_opt_num = rs.getString("opt_num");
                if (Tools.isNullOrEmpty(end_opt_num)) {
                    end_opt_num = "";
                }
                end_opt_name = rs.getString("employee_name");
                if (Tools.isNullOrEmpty(end_opt_name)) {
                    end_opt_name = rs.getString("opt_name");
                    if (Tools.isNullOrEmpty(end_opt_name)) {
                        end_opt_name = "";
                    }
                }
                end_updatetime = rs.getString("last_updatetime");
            }
        }

        obj.put("begain_opt_num", begain_opt_num);
        obj.put("begain_opt_name", begain_opt_name);
        obj.put("end_opt_num", end_opt_num);
        obj.put("end_opt_name", end_opt_name);
        obj.put("begain_updatetime", begain_updatetime);
        obj.put("end_updatetime", end_updatetime);

        JSONArray arr = new JSONArray();



        StringBuilder queryKssySql = new StringBuilder("select os.shift_id,ds.class_item shift_name,os.pos_num,os.opt_num,os.opt_name,os.last_updatetime,os.tag,em.name as employee_name,(select count(*) is_yytc from pos_opt_state where os.tenancy_id=tenancy_id and os.store_id = store_id and os.report_date=report_date  and os.pos_num=pos_num and content =? and  id>os.id) from pos_opt_state os left join employee em on os.opt_num=em.id||'' LEFT JOIN (select s.class_item,d.\"id\" from duty_order d LEFT JOIN sys_dictionary s on d.\"name\"=s.class_item_code ) ds on os.shift_id=ds.\"id\" where os.content =? and os.report_date=? and os.store_id = ? and os.tenancy_id=? order by os.last_updatetime");
        rs = posDao.query4SqlRowSet(queryKssySql.toString(), new Object[]{SysDictionary.OPT_STATE_YYTC, SysDictionary.OPT_STATE_KSSY, reportDate, storeId, tenancyId});

//		StringBuilder queryYytcSql = new StringBuilder("select os.pos_num,os.opt_num,os.opt_name,os.last_updatetime,os.tag,em.name as employee_name from pos_opt_state os left join employee em on os.opt_num=em.id||'' where os.tenancy_id=? and os.store_id = ? and os.report_date=? and os.content =? and os.pos_num=? and os.opt_num=? and os.last_updatetime >? order by os.last_updatetime");
        while (rs.next()) {
            JSONObject item = new JSONObject();

//			String posNum = rs.getString("pos_num");
            String optNum = rs.getString("opt_num");
            String userName = rs.getString("employee_name");
            String shiftId = rs.getString("shift_id");
            String shiftName = rs.getString("shift_name");

            if (Tools.isNullOrEmpty(userName)) {
                userName = rs.getString("opt_name");
                if (Tools.isNullOrEmpty(userName)) {
                    userName = "";
                }
            }

            item.put("userid", optNum);
            item.put("username", userName);
            item.put("ischeckin", "Y");
            item.put("ischeckout", "N");
            item.put("shift_id", shiftId);
            item.put("shift_name", shiftName);

            String posNum ="";

            StringBuilder qureyOptStateSql = new StringBuilder("select pos_num from pos_opt_state where  content=? and shift_id=? and report_date=? and store_id = ? and tenancy_id=?");
            SqlRowSet rowSet = posDao.query4SqlRowSet(qureyOptStateSql.toString(), new Object[]
                    { SysDictionary.OPT_STATE_KSSY, Integer.parseInt(shiftId), reportDate, storeId, tenancyId });
            while (rowSet.next()) {
                posNum  = rs.getString("pos_num");
            }
            item.put("pos_num", posNum);

            if (0 < rs.getInt("is_yytc")) {
                item.put("ischeckout", "Y");
            }

//			SqlRowSet kRs = posDao.query4SqlRowSet(queryYytcSql.toString(), new Object[]{tenancyId,storeId,reportDate,SysDictionary.OPT_STATE_YYTC,posNum ,optNum,rs.getTimestamp("last_updatetime")});
//			if(kRs.next())
//			{
//				item.put("ischeckout", "Y");
//			}
            arr.add(item);
        }
        obj.put("item", arr);

        //获取上一次日始的时间
        String lastbegintime = null;
        String lastBeginTimeSql = "select last_updatetime from pos_opt_state where store_id = " + storeId + " and tenancy_id = '" + tenancyId + "' and content = '" + SysDictionary.OPT_STATE_DAYBEGAIN + "' order by last_updatetime desc";
        List<JSONObject> timelist = posDao.query4Json(tenancyId, lastBeginTimeSql);
        if (timelist != null && timelist.size() > 0) {
            JSONObject timejo = timelist.get(0);
            lastbegintime = timejo.getString("last_updatetime");
        }
        obj.put("last_begin_time", lastbegintime);
        //-----

        List<JSONObject> list = new ArrayList<JSONObject>();
        list.add(obj);
        return list;
    }

    @Override
    public void findInvoice(Data param, Data result) throws SystemException {
        String tenantId = param.getTenancy_id();
        Integer organId = param.getStore_id();

        Map<String, Object> map = ReqDataUtil.getDataMap(param);

        Date report_date = ParamUtil.getDateValue(map, "report_date", true, PosErrorCode.NOT_NULL_REPORT_DATE);
        // String opt_num = ParamUtil.getStringValue(map, "opt_num", true,
        // PosErrorCode.NOT_NULL_OPT_NUM);
        // String pos_num = ParamUtil.getStringValue(map, "pos_num", true,
        // PosErrorCode.NOT_NULL_POS_NUM);
        // Integer shift_id = ParamUtil.getIntegerValue(map, "shift_id", false,
        // null);
        String bill_num = ParamUtil.getStringValue(map, "bill_num", true, PosErrorCode.NOT_NULL_BILL_NUM);

        //总计可开发票金额
        String totalInvoiceSql = "SELECT round( SUM (COALESCE(bb.amount, 0)), 2) AS total_invoice_amount FROM pos_bill_payment bb LEFT JOIN payment_way pw ON bb.jzid = pw. ID LEFT JOIN payment_way_of_ogran pwoo ON pw. ID = pwoo.payment_id WHERE bb.bill_num ='" + bill_num + "' AND bb.tenancy_id ='" + tenantId + "' AND bb.store_id ='" + organId + "' AND pw.if_invoicing = '1' AND bb.tenancy_id = pw.tenancy_id";

        StringBuilder sql = new StringBuilder("select invoice_num,invoice_count,invoice_amount from pos_bill_invoice where (bill_num = ? or copy_bill_num = ?) and report_date = ? and store_id = ? and bill_state is null ");
        try {
            String totalInvoice = "0";
            List<JSONObject> totalInvoices = posDao.query4Json(tenantId, totalInvoiceSql);
            if (null != totalInvoices && !totalInvoices.isEmpty()) {
                totalInvoice = totalInvoices.get(0).optString("total_invoice_amount");
            }


            List<JSONObject> list = posDao.query4Json(tenantId, sql.toString(), new Object[]
                    {bill_num, bill_num, report_date, organId});

            // 取得可以开电子发票的金额
            JSONObject billJo = posDao.getPosBillByBillnum(tenantId, organId, bill_num);

            if (SysDictionary.BILL_STATE_DPQX03.equals(billJo.optString("bill_state"))) {
                billJo.put("type", "RETREAT_FOOD");
            }

            // 可开发票金额
            double invoiceAmount = calcAmountForInvoice(tenantId, organId, billJo);
            if (Double.isNaN(invoiceAmount)) {
                invoiceAmount = 0;
            }
            if (invoiceAmount < 0) {
                invoiceAmount = 0;
            }

            Map<String, Object> dataMap = new HashMap<String, Object>();
            dataMap.put("list", list);
            dataMap.put("total_invoice_amount", totalInvoice);
            dataMap.put("real_invoice_amount", invoiceAmount);

            List<Map<String, Object>> joList = new ArrayList<Map<String, Object>>();
            joList.add(dataMap);

            result.setCode(Constant.CODE_SUCCESS);
            result.setMsg(Constant.QUERY_INVOICE_SUCCESS);
            result.setData(joList);
        } catch (Exception e) {
            e.printStackTrace();
            result.setCode(Constant.CODE_INNER_EXCEPTION);
            result.setMsg(Constant.QUERY_INVOICE_FAILURE);
        }
    }

    @Override
    public void findItemSort(Data param, Data result) throws SystemException {
        try {
            String tenantId = param.getTenancy_id();
            Integer organId = param.getStore_id();

            String qsTime = "";
            int days = 0;

            String qtime = new String("select para_value from sys_parameter where para_code='CPPHCXSD'");
            SqlRowSet rs = posDao.query4SqlRowSet(qtime);

            while (rs.next()) {
                qsTime = rs.getString("para_value");
            }
            if (StringUtils.isEmpty(qsTime)) {
                days = 30;
            } else {
                days = Integer.parseInt(qsTime);
            }

            String qsDate = "";

            String qDate = new String("select CURRENT_DATE-cast(? as int) as qsdate");
            SqlRowSet rs2 = posDao.query4SqlRowSet(qDate, new Object[]
                    {days});

            while (rs2.next()) {
                qsDate = rs2.getString("qsdate");
            }

            List<PosItemSort> list = posDao.findItemSort(qsDate, tenantId, organId);
            result.setData(list);
            result.setCode(Constant.CODE_SUCCESS);
            result.setMsg(Constant.QUERY_ITEM_SORT_SUCCESS);
        } catch (SystemException se) {
            result.setCode(Constant.CODE_INNER_EXCEPTION);
            result.setMsg(Constant.QUERY_ITEM_SORT_FAILURE);
            logger.info(ExceptionMessage.getExceptionMessage(se));
            se.printStackTrace();
        } catch (Exception e) {
            result.setCode(Constant.CODE_INNER_EXCEPTION);
            result.setMsg(Constant.QUERY_ITEM_SORT_FAILURE);
            logger.info(ExceptionMessage.getExceptionMessage(e));
            e.printStackTrace();
        }
    }

    @Override
    public void setInvoice(Data param, Data result) throws SystemException {
        try {
            String tenantId = param.getTenancy_id();
            Integer organId = param.getStore_id();

            Map<String, Object> map = ReqDataUtil.getDataMap(param);

            Date report_date = ParamUtil.getDateValue(map, "report_date", true, PosErrorCode.NOT_NULL_REPORT_DATE);
            String opt_num = ParamUtil.getStringValue(map, "opt_num", true, PosErrorCode.NOT_NULL_OPT_NUM);
            String pos_num = ParamUtil.getStringValue(map, "pos_num", true, PosErrorCode.NOT_NULL_POS_NUM);
            // Integer shift_id = ParamUtil.getIntegerValue(map, "shift_id",
            // false,
            // null);
            String bill_num = ParamUtil.getStringValue(map, "bill_num", true, PosErrorCode.NOT_NULL_BILL_NUM);
            String invoice_num = ParamUtil.getStringValue(map, "invoice_num", true, PosErrorCode.NOT_NULL_INVOICE_NUM);
            Integer invoice_count = ParamUtil.getIntegerValue(map, "invoice_count", true, PosErrorCode.NOT_NULL_INVOICE_COUNT);
            Double invoice_amount = ParamUtil.getDoubleValue(map, "invoice_amount", false, null);
            String isReopenInvoice = ParamUtil.getStringValue(map, "is_reopen_invoice", false, null);

            if (StringUtils.isNotEmpty(isReopenInvoice) && "Y".equals(isReopenInvoice)) {
                posDao.execute(tenantId, "delete from pos_bill_invoice where bill_num='" + bill_num + "'");
            }

            Timestamp time = DateUtil.currentTimestamp();
            // StringBuilder dsql = new
            // StringBuilder("delete from pos_bill_invoice where bill_num = ? and report_date = ? and store_id = ?");
            // posDao.update(dsql.toString(), new Object[]
            // { bill_num, report_date, organId });

            StringBuilder qureyBillSql = new StringBuilder("select (coalesce(payment_amount,0)-coalesce(return_amount,0)) as payment_amount,bill_property,source from pos_bill where tenancy_id=? and store_id=? and bill_num=?");
            SqlRowSet rs = posDao.query4SqlRowSet(qureyBillSql.toString(), new Object[]
                    {tenantId, organId, bill_num});
//			double paymentAmount = 0d;
            String billProperty = "";
            String channel = SysDictionary.CHANEL_MD01;
            if (rs.next()) {
//				paymentAmount = rs.getDouble("payment_amount");
                billProperty = rs.getString("bill_property");
                channel = rs.getString("source");
            }
            if (!SysDictionary.BILL_PROPERTY_CLOSED.equals(billProperty)) {
                throw SystemException.getInstance(PosErrorCode.BILL_NOT_CLOSED_ERROR);
            }

            double invoiceAmount = 0d;
            StringBuilder queryInvoiceSql = new StringBuilder(" select sum(abs(coalesce(invoice_count,0)) * coalesce(invoice_amount,0)) as invoice_amount from pos_bill_invoice where tenancy_id=? and store_id=? and (bill_num=? or copy_bill_num=?) ");
            rs = posDao.query4SqlRowSet(queryInvoiceSql.toString(), new Object[]
                    {tenantId, organId, bill_num, bill_num});
            if (rs.next()) {
                invoiceAmount = rs.getDouble("invoice_amount");
            }

            String isInvoice = ParamUtil.getStringValue(map, "is_invoice", false, null);
            if ("1".equals(isInvoice)) // 开电子发票
            {
                // 开电子发票的时候，只要该账单有开过发票金额，就不许再开电子发票
                if (invoiceAmount > 0) {
                    throw SystemException.getInstance(PosErrorCode.REPEAT_INVOICE_ERROR);
                }
            }

//			if (invoiceAmount == paymentAmount)  // 已开发票金额=账单金额时
//			{
//				throw SystemException.getInstance(PosErrorCode.REPEAT_INVOICE_ERROR);
//			}
//
//			invoiceAmount = DoubleHelper.add(invoiceAmount, DoubleHelper.mul(invoice_count.doubleValue(), invoice_amount, 4), 4);

            // 取得可以开电子发票的金额
            JSONObject jo = posDao.getPosBillByBillnum(tenantId, organId, bill_num);
            if (SysDictionary.BILL_STATE_DPQX03.equals(jo.optString("bill_state"))) {
                jo.put("type", "RETREAT_FOOD");
            }

            // 可开发票金额
            double real_invoice_amount = calcAmountForInvoice(tenantId, organId, jo);
            if (Double.isNaN(invoiceAmount)) {
                real_invoice_amount = 0;
            }

            // 要开发票金额 > 可开发票金额时 不可再开
            if (invoice_amount > real_invoice_amount) {
                throw SystemException.getInstance(PosErrorCode.INVOICE_AMOUNT_MORE_PAYMENT_AMOUNT_ERROR);
            }

            // 已开发票金额 = 可开发票金额时 不可再开
            if (invoiceAmount == real_invoice_amount) {
                throw SystemException.getInstance(PosErrorCode.REPEAT_INVOICE_ERROR);
            }

            StringBuilder sql = new StringBuilder("insert into pos_bill_invoice (tenancy_id,store_id,pos_num,opt_num,report_date,bill_num,invoice_num,invoice_count,invoice_amount,last_updatetime,invoice_type) values (?,?,?,?,?,?,?,?,?,?,?)");

            // 打印补打结账单
            JSONObject printJson = new JSONObject();
            printJson.put("print_code", "1110");
            printJson.put("print_type", "0");
            printJson.put("mode", "0");
            printJson.put("bill_num", bill_num);
            printJson.put("report_date", DateUtil.format(report_date));
            printJson.put("pos_num", pos_num);
            printJson.put("opt_num", opt_num);
            printJson.put("invoice_num", invoice_num);
            printJson.put("invoice_count", invoice_count);
            printJson.put("invoice_amount", invoice_amount);
            printJson.put("last_updatetime", time);
            printJson.put("channel", channel);

            if ("1".equals(isInvoice) && invoiceAmount == 0) // 开电子发票
            {
                logger.info("电子发票二维码生成开始!");
                JSONObject printInvJo = new JSONObject();
                // 获取法人信息
                JSONObject legalPerInfoJo = posDao.getLegalPerInfo(tenantId, organId, printJson);

                if (!(Tools.isNullOrEmpty(legalPerInfoJo) || Double.isNaN(legalPerInfoJo.optDouble("tax_rate")))) {
//					// 计算开发票金额
//					double invAmt = calcAmountForInvoice(tenantId, organId, printJson);
//					if(Double.isNaN(invAmt))
//					{
//						invAmt = 0;
//					}
//					if(invAmt > 0)
//					{
                    JSONObject billJo = posDao.getPosBillByBillnum(tenantId, organId, bill_num);
                    String orderNum = billJo.optString("order_num");

                    // 第三方账单的场合，用订单号生成电子发票
                    if (!Tools.isNullOrEmpty(orderNum)) {
                        printJson.put("bill_num", orderNum);
                    }
                    printJson.put("invoice_amount", real_invoice_amount);
                    printJson.put("tax_rate", legalPerInfoJo.optDouble("tax_rate"));
                    printJson.put("invoice_num", legalPerInfoJo.optString("invoice_num"));
                    printJson.put("invoice_repeat", "yes_invoice");
                    printJson.put("recover_count", billJo.optString("recover_count"));

                    // 生成电子发票，获得电子发票的打印信息
                    logger.info("电子发票二维码生成开始：传入参数：" + printJson.toString());
                    try {
                        printInvJo = getPrintInvoiceInfo(tenantId, organId, report_date, printJson);
                    } catch (Exception e) {
                        e.printStackTrace();
                        logger.error("电子发票二维码生成失败:", e);
                    }
                    logger.info("加密前的二维码URL:" + printInvJo.optString("before_encode_url"));
                    logger.info("生成的二维码URL: " + printInvJo.optString("url_content"));
//					}
//					else
//					{
//						logger.info("电子发票金额为0!");
//					}
                } else {
                    logger.info("法人信息设置错误!");
                }
                logger.info("电子发票二维码生成结束!");

                printJson.put("url_path", printInvJo.optString("url_path"));

                if (posPrintNewService.isNONewPrint(tenantId, organId)) {//如果启用新的打印模式
                    posPrintNewService.posPrintByMode(tenantId, organId, SysDictionary.PRINT_CODE_1110, printJson);
                } else {
                    List<JSONObject> printList = new ArrayList<JSONObject>();
                    printList.add(printJson);

                    Data printData = new Data();
                    printData.setTenancy_id(tenantId);
                    printData.setStore_id(organId);
                    printData.setType(Type.PRINT_BILL);
                    printData.setData(printList);

                    Data resultData = new Data();

                    posPrintService.printPosBill(printData, resultData);
                }
            } else {
                posDao.update(sql.toString(), new Object[]
                        {tenantId, organId, pos_num, opt_num, report_date, bill_num, invoice_num, invoice_count, invoice_amount, time, 1});
            }

            result.setCode(Constant.CODE_SUCCESS);
            result.setMsg(Constant.SET_INVOICE_SUCCESS);
        } catch (SystemException se) {
            throw se;
        } catch (Exception e) {
            e.printStackTrace();
            logger.info(ExceptionMessage.getExceptionMessage(e));
            throw SystemException.getInstance(PosErrorCode.OPER_ERROR);
        }
    }

    @Override
    public List<JSONObject> findItemCheap(Data param) throws Exception {
        String tenantId = param.getTenancy_id();
        Integer organId = param.getStore_id();

        Map<String, Object> map = (Map<String, Object>) ReqDataUtil.getDataMap(param);
        String reportDate = ParamUtil.getStringValue(map, "report_date", false, PosErrorCode.NOT_NULL_REPORT_DATE);

        return posDao.findItemCheap(tenantId, organId, reportDate);
    }

    @Override
    public List<JSONObject> findWorryItem(Data param) throws Exception {
        String tenantId = param.getTenancy_id();
        Integer organId = param.getStore_id();

        Map<String, Object> map = ReqDataUtil.getDataMap(param);

        return posDao.findWorryItem(tenantId, organId, map);
    }

    @Override
    @Deprecated
    public void setSoldOut(Data param, Data result, List<JSONObject> soldList) throws Exception {
        String tenantId = param.getTenancy_id();
        Integer organId = param.getStore_id();

        Map<String, Object> map = ReqDataUtil.getDataMap(param);

        /**
         * 0：设置沽清 1:取消沽清
         */
        String mode = ParamUtil.getStringValue(map, "mode", true, PosErrorCode.NOT_NULL_MODE);

        Date reportDate = ParamUtil.getDateValue(map, "report_date", false, null);

        String optNum = ParamUtil.getStringValue(map, "opt_num", false, null);

        String posNum = ParamUtil.getStringValue(map, "pos_num", false, null);

        Integer shiftId = ParamUtil.getIntegerValue(map, "shift_id", false, null);
        //沽清类型 0为临时沽清1为长期沽清
        String soldout_type = ParamUtil.getStringValue(map, "soldout_type", false, null);
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> list = (List<Map<String, Object>>) map.get("item");
        list = this.sortToMap(list);
        if (null == soldout_type || "".equals(soldout_type)) {
            soldout_type = "0";
        }
        Oper oper = Oper.add;
        StringBuilder sql = null;

        //查询现有的沽清为0的菜品
        List<JSONObject> dishs = soldOutDao.getItemUnits(tenantId, organId, reportDate);

        List<JSONObject> mtDishs = mtSoldOutDao.getItemUnits(tenantId, organId, reportDate);

        if ("0".equals(mode)) {
            try {
                //先删除后插入
                this.deleteSoldOut(soldout_type, param, soldList, tenantId, organId, oper);
                // 插入沽清，包含以前已经存在的沽清
                sql = new StringBuilder(" insert into pos_soldout (tenancy_id,store_id,item_id,num,setdate,soldout_type) values (?,?,?,?,?,?)");

                String newstate = "";
                if (list.size() > 0) {
                    for (Iterator<Map<String, Object>> iterator = list.iterator(); iterator.hasNext(); ) {
                        Map<String, Object> item = iterator.next();
                        Integer itemId = null;
                        Double count = null;
                        if (Tools.isNullOrEmpty(item.get("item_id")) == false) {
                            itemId = Integer.parseInt(item.get("item_id").toString());
                        }
                        if (Tools.isNullOrEmpty(item.get("num")) == false) {
                            count = Double.parseDouble(item.get("num").toString());
                        }

                        List<JSONObject> ifExitLst = posDao.getSoldOutComboId(null, itemId + "");
                        if (ifExitLst != null && ifExitLst.size() > 0) {
                            iterator.remove();
                        }
//						//判断是否有菜品已经设置沽清
//						if(iitemId!=null&&iitemId.size()>0){
//							Integer num=this.getNum
//						}

                        if (!Tools.isNullOrEmpty(count) && count >= 0) {
                            //长传总部
                            JSONObject soldJson = new JSONObject();
                            soldJson.put("item_id", itemId + "");
                            soldJson.put("num", count + "");
                            soldJson.put("soldout_type", soldout_type);
                            soldList.add(soldJson);
                        }
                        posDao.update(sql.toString(), new Object[]
                                {tenantId, organId, itemId, count, reportDate, soldout_type});
                        newstate += itemId + ":" + count + ",";
                    }
                    boolean isExist = false;
                    /**
                     * 更新沽清数据,只能放在这里处理，放在前面。因为还没把数据插入所以不能查到数据。
                     */
                    for (Map<String, Object> map1 : list) {
                        // 循环沽清数据
                        Integer item_id = Integer.parseInt(map1.get("item_id") + "");
                        // 根据itemid取出对应套餐关联的itemId
                        List<JSONObject> iitemIdLst = posDao.getComboItemByItemId(item_id);
                        Double minNum = null;
                        if (iitemIdLst != null && iitemIdLst.size() > 0) {
                            String itemIdstr = this.listToString(iitemIdLst);

                            // 取出最小数量
                            Integer minNumInt = posDao.geComboMinByItemId(item_id, itemIdstr);
                            minNum = Double.parseDouble(minNumInt + "");


                            // 取出关联的没有沽清的itemid
                            List<String> listOut = this.getItemIdSoldOut(list, itemIdstr);

                            // 插入数据库并上传
                            for (String str : listOut) {
                                Integer maxNumInt = posDao.geComboMAXByItemId(str);
                                if (maxNumInt != null && maxNumInt > 1) {
                                    if (maxNumInt <= minNumInt) {
                                        minNumInt = minNumInt / maxNumInt;
                                        minNum = Double.parseDouble(minNumInt + "");
                                    }
                                }
                                List<JSONObject> list1 = posDao.getSoldOutComboId(null, str);
                                if (list1 == null || list1.size() < 1) {
                                    posDao.update(sql.toString(), new Object[]
                                            {tenantId, organId, Integer.parseInt(str), Double.parseDouble(minNum + ""), reportDate, soldout_type});
                                } else {
                                    isExist = true;
                                    // 更新数量
                                    StringBuilder usql = new StringBuilder("update pos_soldout set num = ? where item_id = ? ");
                                    posDao.update(usql.toString(), new Object[]
                                            {minNum, Integer.parseInt(str)});
                                }
                                // 长传总部
                                JSONObject soldJson = new JSONObject();
                                soldJson.put("item_id", str);
                                soldJson.put("num", minNum + "");
                                soldJson.put("soldout_type", soldout_type);
                                soldList.add(soldJson);
                            }
                        }
/*						else
						{
							minNum = Double.parseDouble(map1.get("num") + "");
						}

						// 更新数量
						StringBuilder usql = new StringBuilder("update pos_soldout set num = ? where item_id = ? ");
						for (JSONObject jsonObject3 : iitemIdLst)
						{
							Integer item_id1 = Integer.parseInt(jsonObject3.get("item_id") + "");
							if (isExist)
							{
								minNum = Double.parseDouble(map1.get("num") + "");
							}
							posDao.update(usql.toString(), new Object[]
							{ minNum, item_id1 });

						}*/
                    }
                    //当设置估清，且估清菜品不为空时，调用微生活的估清
                    soldOutService.wLifeSoldOut(tenantId, organId, reportDate);
                    //调用美团沽清
                    mtSoldOutService.soldOutAll(tenantId, organId, reportDate, mtDishs);
                } else {
                    //微生活取消沽清
                    soldOutService.cancelSoldOut(tenantId, organId, dishs);
                    //美团取消沽清
                    mtSoldOutService.cancelSoldOut(tenantId, organId, mtDishs);
                }

                result.setCode(Constant.CODE_SUCCESS);
                result.setMsg(Constant.SET_GUQING_SUCCESS);
                posDao.savePosLog(tenantId, organId, posNum, optNum, optNum, shiftId, reportDate, Constant.TITLE, "设置沽清", "pos_num=" + posNum + ",shift_id=" + shiftId, newstate.toString());
            } catch (Exception e) {
                logger.info("设置沽清失败：" + ExceptionMessage.getExceptionMessage(e));
                result.setCode(Constant.CODE_INNER_EXCEPTION);
                result.setMsg(Constant.SET_GUQING_FAILURE);
                e.printStackTrace();
            }
        } else if ("1".equals(mode)) {
            sql = new StringBuilder("delete from pos_soldout where item_id = ? and store_id = ?");
            String itemIds = "";
            try {
                for (int i = 0; i < list.size(); i++) {
                    Map<String, Object> item = list.get(i);
                    String itemId = ParamUtil.getStringValue(item, "item_id", false, null);
                    //上传总部
                    JSONObject soldJson = new JSONObject();
                    soldJson.put("item_id", itemId);
                    soldList.add(soldJson);

                    posDao.update(sql.toString(), new Object[]
                            {itemId, organId});

                    itemIds += itemId + ",";
                }
                oper = Oper.delete;
                result.setCode(Constant.CODE_SUCCESS);
                result.setMsg(Constant.DEL_GUQING_SUCCESS);
                posDao.savePosLog(tenantId, organId, posNum, optNum, optNum, shiftId, reportDate, Constant.TITLE, "删除沽清", "pos_num=" + posNum + ",shift_id=" + shiftId, itemIds.toString());

                //TYPE: DISH_SOLD_OUT
                //oper: delete

                //取消沽清，会调用微生活的取消沽清
                itemIds = itemIds.substring(0, itemIds.length() - 1);
                List<JSONObject> itemList = soldOutDao.getUnits(tenantId, itemIds);
                List<JSONObject> mtItemList = mtSoldOutDao.getUnits(tenantId, itemIds);
                soldOutService.cancelSoldOut(tenantId, organId, itemList);
                //调用美团的取消沽清
                mtSoldOutService.cancelSoldOut(tenantId, organId, mtItemList);

            } catch (Exception e) {
                logger.info("删除沽清失败：" + ExceptionMessage.getExceptionMessage(e));
                result.setCode(Constant.CODE_INNER_EXCEPTION);
                result.setMsg(Constant.DEL_GUQING_FAILURE);
                e.printStackTrace();
            }
        }

        try {
            soldList = posDao.findSoldOut(tenantId, organId, map);
            if (soldList.size() > 0) {
                //上传到总部
                Data subParam = new Data();
                subParam = param.clone();
                subParam.setOper(oper);
//				customerService.dishSoldOut(subParam, soldList);
                SellDishesDataUploadRunnable sellDishesDataUpload = new SellDishesDataUploadRunnable(subParam, soldList);
                Thread sellDishT = new Thread(sellDishesDataUpload);
                sellDishT.start();
            }

            List<JSONObject> soldOutList = posDao.findSoldOut(tenantId, organId, map);

            Data cjData = new Data();
            cjData.setType(Type.SOLD_OUT);
            cjData.setOper(Oper.init);
            cjData.setData(soldOutList);

//			Comet4jUtil.sendMessage2All(Comet4jUtil.POS_ANDROID, JSONObject.fromObject(cjData).toString());
//			Comet4jUtil.sendMessage2All(Comet4jUtil.POS_PC, JSONObject.fromObject(cjData).toString());
            CometDataNoticeClientRunnable noticeClientRunnable = new CometDataNoticeClientRunnable(cjData);
            Thread noticeThread = new Thread(noticeClientRunnable);
            noticeThread.start();

        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public List<Map<String, Object>> sortToMap(List<Map<String, Object>> mapList) {
        Collections.sort(mapList, new Comparator<Map<String, Object>>() {
            public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                String s1 = "0d";
                if (null != o1 && Tools.hv(o1.get("num").toString())) {
                    s1 = o1.get("num").toString();
                }
                String s2 = "0d";
                if (null != o2 && Tools.hv(o2.get("num").toString())) {
                    s2 = o2.get("num").toString();
                }
                Double map1value = Double.parseDouble(s1);
                Double map2value = Double.parseDouble(s2);
//                return map1value - map2value > 0 ? -1 : 1;
                return map2value.compareTo(map1value);
            }
        });
        return mapList;
    }

//	/**
//	 * List to String
//	 * @param list
//	 * @return
//	 */
//	public String listToStringSoldOut(List<Map<String, Object>> list){
//		String itemStr="";
//		for(Map map:list){
//			 String item_id=map.get("item_id").toString();
//			itemStr+=item_id+",";
//		}
//	     if(list!=null&&list.size()>0){
//			 itemStr=itemStr.substring(0,itemStr.length()-1);
//		 }
//		return itemStr;
//	}

    /**
     * 删除沽清数据
     */
    public void deleteSoldOut(String soldout_type, Data param, List<JSONObject> soldList, String tenantId, Integer organId, Oper oper) throws Exception {
        soldList = posDao.query4Json(tenantId, "select item_id from pos_soldout where store_id = ? and soldout_type = ? ",
                new Object[]{organId, soldout_type});
        if (soldList.size() > 0) {
            //先把总部数据都删了
            oper = Oper.delete;
            //上传到总部
            Data subParam = new Data();
            subParam = param.clone();
            subParam.setOper(oper);
            customerService.dishSoldOut(subParam, soldList);
            soldList.clear();
        }

        // 删除以前的沽清
        StringBuilder dsql = new StringBuilder("delete from pos_soldout where store_id = ? and soldout_type = ?");
        posDao.update(dsql.toString(), new Object[]
                {organId, soldout_type});
    }

    /**
     * 取出多余不重复的数据
     *
     * @return
     * @throws Exception
     * @throws NumberFormatException
     */
    @Override
    public List<String> getItemIdSoldOut(List<Map<String, Object>> list, String itemStr) throws NumberFormatException, Exception {
        String[] itemStrlst = itemStr.split(",");
        List list1 = new ArrayList();
        for (int i = 0; i < itemStrlst.length; i++) {
            String itemId = itemStrlst[i];
            if (this.getItemByList(list, itemId)) {
                list1.add(itemId);
            }
        }
        return list1;
    }

    /**
     * 判断List是否存在对应ITEMiD
     *
     * @param list
     * @param itemId
     * @return
     * @throws Exception
     * @throws NumberFormatException
     */
    public boolean getItemByList(List<Map<String, Object>> list, String itemId) throws NumberFormatException, Exception {
        boolean isflag = true;
        for (Map<String, Object> map : list) {
            if (itemId.equals(map.get("item_id").toString())) {
                // 根据itemid取出对应套餐关联的itemId
                List<JSONObject> iitemIdLst = posDao.getComboItemByItemId(Integer.parseInt(itemId));
                // 根据itemid取出对应套餐关联的itemId
                String itemIdstr = this.listToString(iitemIdLst);
                // 取出最小数量
                Integer minNumInt = posDao.geComboMinByItemId(Integer.parseInt(itemId), itemIdstr);
                if (Double.parseDouble(minNumInt.toString()) >= Double.parseDouble(map.get("num").toString())) {
                    isflag = false;
                    break;
                }
            }
        }
        return isflag;
    }

    public String listToString(List<JSONObject> list) {
        boolean flag = false;
        StringBuilder result = new StringBuilder();
        for (JSONObject jsonObject : list) {
            String itemIdStr = jsonObject.get("item_id").toString();
            if (flag) {
                result.append(",");
            } else {
                flag = true;
            }
            result.append(itemIdStr);
        }
        return result.toString();
    }

    @Override
    @Deprecated
    public List<JSONObject> findSoldOut(Data param) throws Exception {
        String tenantId = param.getTenancy_id();
        Integer organId = param.getStore_id();

        Map<String, Object> map = ReqDataUtil.getDataMap(param);

        return posDao.findSoldOut(tenantId, organId, map);
    }

    @SuppressWarnings("unchecked")
    @Override
    public void setWorryItem(Data param, Data result) throws SystemException {
        String tenantId = param.getTenancy_id();
        Integer organId = param.getStore_id();

        Map<String, Object> map = ReqDataUtil.getDataMap(param);
        String mode = ParamUtil.getStringValue(map, "mode", true, PosErrorCode.NOT_NULL_MODE);

        String optNum = ParamUtil.getStringValue(map, "opt_num", false, null);

        String posNum = ParamUtil.getStringValue(map, "pos_num", true, PosErrorCode.NOT_NULL_POS_NUM);

        Timestamp reportDate = ParamUtil.getTimestampValue(map, "report_date", false, null);

        Integer shiftId = ParamUtil.getIntegerValue(map, "shift_id", false, null);

        List<Map<String, Object>> list = (List<Map<String, Object>>) map.get("item");

        StringBuilder sql = null;
        if ("0".equals(mode)) {
            try {
                // 删除以前的急推菜品
                StringBuilder dsql = new StringBuilder(" delete from pos_item_worrysale where store_id = ?");
                posDao.update(dsql.toString(), new Object[]
                        {organId});

                sql = new StringBuilder(" insert into pos_item_worrysale (tenancy_id,store_id,item_id,num,item_unit_id,setdate) values (?,?,?,?,?,?)");

                for (int i = 0; i < list.size(); i++) {
                    Map<String, Object> map1 = list.get(i);
                    Integer itemId = ParamUtil.getIntegerValue(map1, "item_id", false, null);
                    Double count = ParamUtil.getDoubleValue(map1, "num", false, null);
                    Integer itemUnitId = ParamUtil.getIntegerValue(map1, "item_unit_id", false, null);

                    posDao.update(sql.toString(), new Object[]
                            {tenantId, organId, itemId, count, itemUnitId, reportDate});
                }
                result.setCode(Constant.CODE_SUCCESS);
                result.setMsg(Constant.SET_WORRY_DISH_SUCCESS);
                posDao.savePosLog(tenantId, organId, posNum, optNum, optNum, shiftId, reportDate, Constant.TITLE, "设置急推菜品", "pos_num=" + posNum + ",shift_id=" + shiftId, "");
            } catch (Exception e) {
                logger.info("设置急推菜品失败：" + ExceptionMessage.getExceptionMessage(e));
                result.setCode(Constant.CODE_INNER_EXCEPTION);
                result.setMsg(Constant.DEL_WORRY_DISH_FAILURE);
                e.printStackTrace();
            }
        }
        if ("1".equals(mode)) {
            try {
                sql = new StringBuilder("delete from pos_item_worrysale where item_id = ? and store_id = ?");
                for (int i = 0; i < list.size(); i++) {
                    Map<String, Object> map1 = list.get(i);
                    String itemId = ParamUtil.getStringValue(map1, "item_id", false, null);

                    posDao.update(sql.toString(), new Object[]
                            {itemId, organId});
                }
                result.setCode(Constant.CODE_SUCCESS);
                result.setMsg(Constant.DEL_WORRY_DISH_SUCCESS);
                posDao.savePosLog(tenantId, organId, posNum, optNum, optNum, shiftId, reportDate, Constant.TITLE, "删除急推菜品成功", "pos_num=" + posNum + ",shift_id=" + shiftId, "");
            } catch (Exception e) {
                logger.info("删除急推菜品失败：" + ExceptionMessage.getExceptionMessage(e));
                result.setCode(Constant.CODE_INNER_EXCEPTION);
                result.setMsg(Constant.DEL_WORRY_DISH_FAILURE);
                e.printStackTrace();
            }
        }

        try {
            List<JSONObject> worryItemList = posDao.findWorryItem(tenantId, organId, map);
            Data cjData = new Data();
            cjData.setType(Type.SET_SOLD_OUT);
            cjData.setOper(Oper.init);
            cjData.setData(worryItemList);
            CometDataNoticeClientRunnable noticeClientRunnable = new CometDataNoticeClientRunnable(cjData);
            Thread noticeThread = new Thread(noticeClientRunnable);
            noticeThread.start();
        } catch (Exception e) {
            logger.info("查询急推菜品失败：" + ExceptionMessage.getExceptionMessage(e));
            e.printStackTrace();
        }

    }

    // 零找金
    @Override
    public void blindSuccession(Data param, Data result) throws SystemException {
        try {
            String tenantId = param.getTenancy_id();
            Integer organId = param.getStore_id();

            Map<String, Object> map = ReqDataUtil.getDataMap(param);

            String posNum = ParamUtil.getStringValue(map, "pos_num", false, null);// 收款机号

            String optNum = ParamUtil.getStringValue(map, "opt_num", false, null);// 操作员编号

            Date reportDate = ParamUtil.getDateValue(map, "report_date", false, null);// 报表日期

            Integer shiftId = ParamUtil.getIntegerValue(map, "shift_id", false, null);// 班次id

            Double amount = ParamUtil.getDoubleValue(map, "amount", false, null);

            String optName = null;

            String insertSql = new String("insert into pos_byjdc_log (tenancy_id,store_id,pos_num,opt_num,opt_name,opt_type,report_date,shift_id,last_updatetime,amount,opt_login_number,upload_tag) values (?,?,?,?,?,?,?,?,?,?,?,?) ");

            String qName = new String("select em.name from employee em join user_authority ua on em.id = ua.employee_id where ua.employee_id = ? and ua.valid_state='1'");
            SqlRowSet rst = posDao.query4SqlRowSet(qName, new Object[]
                    {Integer.valueOf(optNum)});
            if (rst.next()) {
                optName = rst.getString("name");
            }
            Timestamp time = DateUtil.currentTimestamp();

            String qloginCount = new String("select count(id) from pos_opt_state where content='KSSY' and tag='1' and report_date = ? and opt_num= ?");
            int loginCount = posDao.queryForInt(qloginCount, new Object[]
                    {reportDate, optNum});

            posDao.update(insertSql, new Object[]
                    {tenantId, organId, posNum, optNum, optName, "changet", reportDate, shiftId, time, amount, loginCount, 0});
            result.setCode(Constant.CODE_SUCCESS);
            result.setMsg(Constant.CHANGET_SUCCESS);
            result.setSuccess(true);
            return;
        } catch (Exception se) {
            logger.info("零找金失败：" + ExceptionMessage.getExceptionMessage(se));
            se.printStackTrace();
            result.setCode(Constant.CODE_INNER_EXCEPTION);
            result.setMsg(Constant.CHANGET_FAILURE);
            result.setSuccess(false);
            return;
        }
    }

    @Override
    public void getOptName(Data param, Data result) throws Exception {
        String tenancy_id = param.getTenancy_id();
        Integer store_id = param.getStore_id();

        if (null == store_id || "".equals(store_id.toString())) {
            throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_ORGAN);
        }
        String optName = "SELECT id,user_name FROM user_authority where tenancy_id = ? and store_id = ? ";
        List<JSONObject> userList = posDao.queryString4Json(tenancy_id, optName, new Object[]{tenancy_id, store_id});
        if (userList != null && userList.size() > 0) {
            result.setData(userList);
        }
        result.setMsg(Constant.GET_OPT_NAME_SUCCESS);
    }

    @Override
    public Data getSoldOutCount(Data param) throws SystemException {
        String tenantId = param.getTenancy_id();
        Integer organId = param.getStore_id();

        Map<String, Object> map = ReqDataUtil.getDataMap(param);

        return posDao.getSoldOutCount(tenantId, organId, map);
    }

    @Override
    public void addOptBindDevices(String tenancyId, int storeId, List<?> param) throws Exception {
        if (param == null || param.size() <= 0 || param.isEmpty()) {
            throw new SystemException(PosErrorCode.PARAM_ERROR);
        }
        JSONObject paraJson = JSONObject.fromObject(param.get(0));
        if (Tools.isNullOrEmpty(paraJson.opt("report_date"))) {
            throw new SystemException(PosErrorCode.NOT_NULL_REPORT_DATE);
        }
        if (Tools.isNullOrEmpty(paraJson.optString("pos_num"))) {
            throw new SystemException(PosErrorCode.NOT_NULL_POS_NUM);
        }
        if (Tools.isNullOrEmpty(paraJson.optString("opt_num"))) {
            throw new SystemException(PosErrorCode.NOT_NULL_OPT_NUM);
        }

        Date reportDate = DateUtil.parseDate(ParamUtil.getDateStringValue(paraJson, "report_date"));
        String optNum = paraJson.optString("opt_num");
        String posNum = paraJson.optString("pos_num");

        Timestamp currentTime = DateUtil.currentTimestamp();

        List<?> devicesList = paraJson.optJSONArray("devices_list");

        if (null != devicesList && devicesList.size() > 0) {
            StringBuilder qureyOtherDevicesSql = new StringBuilder("select * from pos_opt_state_devices where tenancy_id=? and store_id=? and pos_num<>? and is_valid='1'");
            StringBuilder devicesIdStr = new StringBuilder();
            for (Object obj : devicesList) {
                JSONObject devices = JSONObject.fromObject(obj);
                devicesIdStr.append(devices.optInt("devices_id")).append(",");
            }
            if (devicesIdStr.length() > 0) {
                devicesIdStr.setLength(devicesIdStr.length() - 1);
                qureyOtherDevicesSql.append(" and devices_id in (").append(devicesIdStr).append(")");

                SqlRowSet rs = posDao.query4SqlRowSet(qureyOtherDevicesSql.toString(), new Object[]{tenancyId, storeId, posNum});
                StringBuilder devicesNumStr = new StringBuilder();
                while (rs.next()) {
                    devicesNumStr.append(rs.getString("devices_num")).append(",");
                }
                if (devicesNumStr.length() > 0) {
                    devicesNumStr.setLength(devicesNumStr.length() - 1);
                    throw SystemException.getInstance(PosErrorCode.DEVICES_ALREADY_BIND_ERROR).set("{0}", devicesNumStr.toString());
                }

            }
        }

        StringBuilder updateDevicesSql = new StringBuilder("update pos_opt_state_devices set is_valid='0',last_updatetime=? where tenancy_id=? and store_id=? and pos_num=? and is_valid='1' and is_changshift='0'");
        posDao.update(updateDevicesSql.toString(), new Object[]{currentTime, tenancyId, storeId, posNum});

        StringBuilder qureyDevicesSql = new StringBuilder("select * from pos_opt_state_devices where tenancy_id=? and store_id=? and pos_num=? and is_changshift='0'");
        List<JSONObject> hasDevicesList = posDao.query4Json(tenancyId, qureyDevicesSql.toString(), new Object[]{tenancyId, storeId, posNum});

        if (null != devicesList) {
            List<Object[]> insertDevicesList = new ArrayList<Object[]>();
            List<Object[]> updateDevicesList = new ArrayList<Object[]>();
            for (Object obj : devicesList) {
                JSONObject devices = JSONObject.fromObject(obj);
                boolean isNotHas = true;
                if (null != devicesList) {
                    for (JSONObject hasDevices : hasDevicesList) {
                        if (devices.optInt("devices_id") == hasDevices.optInt("devices_id")) {
                            isNotHas = false;
                            updateDevicesList.add(new Object[]{currentTime, tenancyId, storeId, posNum, devices.optInt("devices_id")});
                            break;
                        }
                    }
                }

                if (isNotHas) {
                    insertDevicesList.add(new Object[]{tenancyId, storeId, reportDate, posNum, optNum, devices.optInt("devices_id"), devices.optString("devices_num"), currentTime});
                }
            }

            if (insertDevicesList.size() > 0) {
                String insertSql = "insert into pos_opt_state_devices(tenancy_id,store_id,report_date,pos_num,opt_num,devices_id,devices_num,is_valid,is_changshift,last_updatetime,upload_tag) values(?,?,?,?,?,?,?,'1','0',?,'0')";
                posDao.batchUpdate(insertSql, insertDevicesList);
            }
            if (updateDevicesList.size() > 0) {
                String updateSql = "update pos_opt_state_devices set is_valid='1',last_updatetime=? where tenancy_id=? and store_id=? and pos_num=? and devices_id=?";
                posDao.batchUpdate(updateSql, updateDevicesList);
            }
        }

        String optName = posDao.getEmpNameById(optNum, tenancyId, storeId);
        posDao.savePosLog(tenancyId, storeId, posNum, optNum, optName, paraJson.optInt("shift_id"), reportDate, Constant.TITLE, "绑定机台", "操作人:" + optName + ",操作机台:" + posNum, "绑定设备数量:" + devicesList.size());
    }

    @Override
    public List<JSONObject> findOptBindDevices(String tenancyId, int storeId, List<?> param, Pagination pagination) throws Exception {
        if (param == null || param.size() <= 0 || param.isEmpty()) {
            throw new SystemException(PosErrorCode.PARAM_ERROR);
        }
        JSONObject paraJson = JSONObject.fromObject(param.get(0));
        if (Tools.isNullOrEmpty(paraJson.opt("report_date"))) {
            throw new SystemException(PosErrorCode.NOT_NULL_REPORT_DATE);
        }
        if (Tools.isNullOrEmpty(paraJson.optString("pos_num"))) {
            throw new SystemException(PosErrorCode.NOT_NULL_POS_NUM);
        }
        if (Tools.isNullOrEmpty(paraJson.optString("opt_num"))) {
            throw new SystemException(PosErrorCode.NOT_NULL_OPT_NUM);
        }

//		Date reportDate = DateUtil.parseDate(ParamUtil.getDateStringValue(paraJson,"report_date"));
//		String optNum = paraJson.optString("opt_num");
        String posNum = paraJson.optString("pos_num");

        StringBuilder qureyDevicesSql = new StringBuilder("select coalesce(CAST(devices_id as VARCHAR(20)), '') as devices_id,coalesce(devices_num,'') as devices_num from pos_opt_state_devices where tenancy_id=? and store_id=? and pos_num=? and is_valid='1' order by devices_num desc");

        List<JSONObject> devicesList = posDao.query4Json(tenancyId, qureyDevicesSql.toString(), new Object[]{tenancyId, storeId, posNum});

        JSONObject result = new JSONObject();
        result.put("devices_list", devicesList);

        List<JSONObject> resultList = new ArrayList<JSONObject>();
        resultList.add(result);
        return resultList;
    }

    @Override
    public void cancleOptBindDevices(String tenancyId, int storeId, List<?> param) throws Exception {
        if (param == null || param.size() <= 0 || param.isEmpty()) {
            throw new SystemException(PosErrorCode.PARAM_ERROR);
        }
        JSONObject paraJson = JSONObject.fromObject(param.get(0));
        if (Tools.isNullOrEmpty(paraJson.opt("report_date"))) {
            throw new SystemException(PosErrorCode.NOT_NULL_REPORT_DATE);
        }
        if (Tools.isNullOrEmpty(paraJson.optString("pos_num"))) {
            throw new SystemException(PosErrorCode.NOT_NULL_POS_NUM);
        }
        if (Tools.isNullOrEmpty(paraJson.optString("opt_num"))) {
            throw new SystemException(PosErrorCode.NOT_NULL_OPT_NUM);
        }

        Date reportDate = DateUtil.parseDate(ParamUtil.getDateStringValue(paraJson, "report_date"));
        String optNum = paraJson.optString("opt_num");
        String posNum = paraJson.optString("pos_num");

//		Timestamp currentTime = DateUtil.currentTimestamp();

        StringBuilder updateDevicesSql = new StringBuilder("update pos_opt_state_devices set is_valid='0',is_changshift='1' where tenancy_id=? and store_id=? and pos_num=? and is_changshift='0'");
        posDao.update(updateDevicesSql.toString(), new Object[]{tenancyId, storeId, posNum});

        String optName = posDao.getEmpNameById(optNum, tenancyId, storeId);
        posDao.savePosLog(tenancyId, storeId, posNum, optNum, optName, paraJson.optInt("shift_id"), reportDate, Constant.TITLE, "取消绑定机台", "操作人:" + optName + ",操作机台:" + posNum, "");
    }

    @Override
    public List<JSONObject> findOptPosData(String tenancyId, int storeId, List<?> param, Pagination pagination) throws Exception {
        if (param == null || param.size() <= 0 || param.isEmpty()) {
            throw new SystemException(PosErrorCode.PARAM_ERROR);
        }
        JSONObject paraJson = JSONObject.fromObject(param.get(0));
        if (Tools.isNullOrEmpty(paraJson.opt("report_date"))) {
            throw new SystemException(PosErrorCode.NOT_NULL_REPORT_DATE);
        }

        Date reportDate = DateUtil.parseDate(ParamUtil.getDateStringValue(paraJson, "report_date"));

        StringBuilder qureyPosNumSql = new StringBuilder("select distinct pb.pos_num,hd.devices_name as pos_name from pos_bill pb,hq_devices hd where pb.tenancy_id=? and pb.store_id=? and pb.report_date=? and pb.bill_property='CLOSED' " +
                " and pb.store_id=hd.store_id and pb.pos_num=hd.devices_code and hd.valid_state='1' and hd.show_type='YD' order by pb.pos_num ");
        List<JSONObject> posList = posDao.query4Json(tenancyId, qureyPosNumSql.toString(), new Object[]{tenancyId, storeId, reportDate});

        StringBuilder OptNumSql = new StringBuilder();
        OptNumSql.append(" SELECT DISTINCT pb.cashier_num AS opt_num,emp.name as opt_name  ");
        OptNumSql.append(" FROM pos_bill AS pb LEFT JOIN hq_devices AS hd ON pb.pos_num = hd.devices_code ");
        OptNumSql.append(" LEFT JOIN employee AS emp on CAST(emp.id as VARCHAR(20)) = pb.cashier_num ");
        OptNumSql.append(" WHERE pb.bill_property = ? and pb.tenancy_id=? and pb.store_id=? and pb.report_date=? ");
        OptNumSql.append(" and hd.valid_state=? and hd.show_type=? order by pb.cashier_num ");

        List<JSONObject> optList = posDao.query4Json(tenancyId, OptNumSql.toString(),
                new Object[]{"CLOSED", tenancyId, storeId, reportDate, "1", "YD"});

        JSONObject result = new JSONObject();
        result.put("poslist", posList);
        result.put("optlist", optList);

        List<JSONObject> resultList = new ArrayList<JSONObject>();
        resultList.add(result);
        return resultList;
    }

//	@Override
//	public List<JSONObject> totalCashierReceive(String tenancyId, int storeId, List<?> param, Pagination pagination) throws Exception
//	{
//		if (param == null || param.size() <= 0 || param.isEmpty())
//		{
//			throw new SystemException(PosErrorCode.PARAM_ERROR);
//		}
//		JSONObject paraJson = JSONObject.fromObject(param.get(0));
//		Date reportDate = DateUtil.parseDate(ParamUtil.getDateStringValue(paraJson,"report_date"));
//
////		String bill_condition = "";
////		String pcrl_condition = "";
////		if(Tools.hv(reportDate))
////		{
////			bill_condition = " and pbp.report_date='" + reportDate + "' ";
////			pcrl_condition = " and report_date='" + reportDate + "' ";
////		}
////
////		StringBuilder cashierRcvSql = new StringBuilder(" with t as(select bl.report_date,bl.shift_id,bl.waiter_num as waiter_id,bl.waiter_name as waiter_name,bl.shift_name,bl.bill_amount,pcrl.receive_amount from (" +
////				" (select store_id,report_date,shift_id,waiter_id,payment_id,sum(amount) as receive_amount from pos_cashier_receive_log where tenancy_id = '" + tenancyId + "' and store_id = '" + storeId + "' " + pcrl_condition + " group by store_id,report_date,shift_id,waiter_id,payment_id) as pcrl " +
////				" right join (select bill.store_id,bill.report_date,bill.shift_id,bill.waiter_num,bill.bill_amount,bill.payment_state,bill.jzid, " +
////				" (select em.name from employee em where bill.tenancy_id = em.tenancy_id and bill.store_id = em.store_id and cast (bill.waiter_num as int) = em. id) as waiter_name, " +
////				" (select sd.class_item as name from duty_order as dor left join duty_order_of_ogran as doo on dor.id = doo.duty_order_id left join sys_dictionary sd on dor.name = sd.class_item_code and sd.class_identifier_code ='duty' where dor.id=bill.shift_id and doo.organ_id = bill.store_id) as shift_name from ( " +
////				" select pbp.tenancy_id,pbp.store_id,pbp.report_date,pbp.shift_id,pbp.cashier_num as waiter_num,coalesce(sum(pbp.currency_amount),0) AS bill_amount,pbp.payment_state,pbp.jzid from pos_bill_payment pbp,hq_devices hd " +
////				" where pbp.tenancy_id = '" + tenancyId + "' and pbp.store_id = '" + storeId + "' " + bill_condition + " and pbp.store_id = hd.store_id and pbp.pos_num = hd.devices_code and hd.valid_state = '1' and (hd.show_type = 'YD' or hd.show_type = 'PAD') " +
////				" group by pbp.tenancy_id,pbp.store_id,pbp.report_date,pbp.shift_id,pbp.cashier_num,pbp.payment_state,pbp.jzid) as bill) as bl " +
////				" on bl.store_id = pcrl.store_id and bl.report_date = pcrl.report_date and bl.shift_id = pcrl.shift_id and cast(bl.waiter_num as int) = pcrl.waiter_id and bl.jzid = pcrl.payment_id and bl.payment_state = '" + SysDictionary.PAYMENT_STATE_PAY_COMPLETE + "')) " +
////				" select report_date,shift_id,waiter_id,waiter_name,shift_name,coalesce(sum(bill_amount),0) as bill_amount,coalesce(sum(receive_amount),0) as receive_amount,coalesce(coalesce(sum(bill_amount),0)-coalesce(sum(receive_amount),0),0) as difference_amount " +
////				" from t group by report_date,shift_id,waiter_id,waiter_name,shift_name order by waiter_id,shift_id ");
//
//		StringBuilder condition = new StringBuilder();
//		condition.append("pbp.tenancy_id = '").append(tenancyId).append("' and pbp.store_id = '").append(storeId).append("' and pbp.report_date='").append(reportDate).append("'");
//
//		StringBuilder cashierRcvSql = new StringBuilder();
//		cashierRcvSql.append(" select bl.report_date,bl.shift_id,bl.waiter_num as waiter_id,em.name as waiter_name,du.shift_name,bl.bill_amount,coalesce(pcrl.receive_amount,0) as receive_amount,coalesce(coalesce(bl.bill_amount,0)-coalesce(pcrl.receive_amount,0),0) as difference_amount");
//		cashierRcvSql.append(" from (select bi.tenancy_id,bi.store_id,bi.report_date,bi.shift_id,bi.cashier_num as waiter_num,coalesce(sum(pbp.currency_amount),0) AS bill_amount from pos_bill_payment pbp left join pos_bill bi on pbp.tenancy_id=bi.tenancy_id and pbp.store_id=bi.store_id and pbp.bill_num=bi.bill_num left join hq_devices hd on bi.tenancy_id = hd.tenancy_id and bi.store_id = hd.store_id and bi.pos_num = hd.devices_code where ").append(condition).append(" and (hd.show_type = 'YD' or hd.show_type = 'PAD') group by bi.tenancy_id,bi.store_id,bi.report_date,bi.shift_id,bi.cashier_num) as bl");
//		cashierRcvSql.append(" left join (select pbp.tenancy_id,pbp.store_id,pbp.report_date,pbp.pay_shift_id as shift_id,pbp.waiter_id,sum(pbp.amount) as receive_amount from pos_cashier_receive_log pbp where ").append(condition).append(" group by pbp.tenancy_id,pbp.store_id,pbp.report_date,pbp.pay_shift_id,pbp.waiter_id) as pcrl on bl.tenancy_id = pcrl.tenancy_id and bl.store_id = pcrl.store_id and bl.report_date = pcrl.report_date and bl.shift_id = pcrl.shift_id and cast(bl.waiter_num as int) = pcrl.waiter_id");
//		cashierRcvSql.append(" left join employee em  on bl.tenancy_id = em.tenancy_id and bl.store_id = em.store_id and cast(bl.waiter_num as int) = em.id");
//		cashierRcvSql.append(" left join (select doo.tenancy_id,doo.organ_id,dor.id as shift_id,sd.class_item as shift_name from duty_order as dor left join duty_order_of_ogran as doo on dor.tenancy_id = doo.tenancy_id and dor.id = doo.duty_order_id left join sys_dictionary sd on  dor.tenancy_id = sd.tenancy_id and dor.name = sd.class_item_code and sd.class_identifier_code ='duty') du on bl.tenancy_id = du.tenancy_id and bl.store_id = du.organ_id and bl.shift_id = du.shift_id");
//
//		List<JSONObject> receivelist = posDao.query4Json(tenancyId, cashierRcvSql.toString());
//		JSONObject result = new JSONObject();
//		result.put("receivelist", receivelist);
//
//		List<JSONObject> resultList = new ArrayList<JSONObject>();
//		resultList.add(result);
//
//		return resultList;
//	}
//
//	@Override
//	public List<JSONObject> cashierReceiveDetails(String tenancyId, int storeId, List<?> param, Pagination pagination) throws Exception
//	{
//		if (param == null || param.size() <= 0 || param.isEmpty())
//		{
//			throw new SystemException(PosErrorCode.PARAM_ERROR);
//		}
//		JSONObject paraJson = JSONObject.fromObject(param.get(0));
//		Date reportDate = DateUtil.parseDate(ParamUtil.getDateStringValue(paraJson,"report_date"));
//		String optNum = paraJson.optString("opt_num");
//		Integer shiftId = paraJson.optInt("shift_id");
//
////		String bill_condition = "";
////		String pcrl_condition = "";
////		if(Tools.hv(reportDate))
////		{
////			bill_condition = " and pbp.report_date='" + reportDate + "' ";
////			pcrl_condition = " and report_date='" + reportDate + "' ";
////		}
////
////		StringBuilder cashierRcvSql = new StringBuilder(" with t as(select bl.report_date,bl.shift_id,bl.waiter_num as waiter_id,bl.waiter_name as waiter_name,bl.shift_name,bl.payment_state,bl.jzid,bl.payment_name,bl.bill_amount,pcrl.receive_amount from (" +
////				" (select store_id,report_date,shift_id,waiter_id,payment_id,sum(amount) as receive_amount from pos_cashier_receive_log where tenancy_id = '" + tenancyId + "' and store_id = '" + storeId + "' " + pcrl_condition + " group by store_id,report_date,shift_id,waiter_id,payment_id) as pcrl " +
////				" right join (select bill.store_id,bill.report_date,bill.shift_id,bill.waiter_num,bill.bill_amount,bill.payment_state,bill.jzid,bill.payment_name, " +
////				" (select em.name from employee em where bill.tenancy_id = em.tenancy_id and bill.store_id = em.store_id and cast (bill.waiter_num as int) = em. id) as waiter_name, " +
////				" (select sd.class_item as name from duty_order as dor left join duty_order_of_ogran as doo on dor.id = doo.duty_order_id left join sys_dictionary sd on dor.name = sd.class_item_code and sd.class_identifier_code ='duty' where dor.id=bill.shift_id and doo.organ_id = bill.store_id) as shift_name from ( " +
////				" select pbp.tenancy_id,pbp.store_id,pbp.report_date,pbp.shift_id,pbp.cashier_num as waiter_num,coalesce(sum(pbp.currency_amount),0) AS bill_amount,pbp.payment_state,pbp.jzid,(case when pbp.name='找零' then '人民币' else pbp.name end) AS payment_name from pos_bill_payment pbp,hq_devices hd " +
////				" where pbp.tenancy_id = '" + tenancyId + "' and pbp.store_id = '" + storeId + "' " + bill_condition + "and pbp.cashier_num='" + optNum + "' and pbp.shift_id='" + shiftId + "' and pbp.store_id = hd.store_id and pbp.pos_num = hd.devices_code and hd.valid_state = '1' and (hd.show_type = 'YD' or hd.show_type = 'PAD') " +
////				" group by pbp.tenancy_id,pbp.store_id,pbp.report_date,pbp.shift_id,pbp.cashier_num,pbp.payment_state,pbp.jzid,payment_name) as bill) as bl " +
////				" on bl.store_id = pcrl.store_id and bl.report_date = pcrl.report_date and bl.shift_id = pcrl.shift_id and cast(bl.waiter_num as int) = pcrl.waiter_id and bl.jzid = pcrl.payment_id and bl.payment_state = '" + SysDictionary.PAYMENT_STATE_PAY_COMPLETE + "')) " +
////				" select t1.report_date,t1.shift_id,t1.waiter_id,t1.waiter_name,t1.shift_name,t1.jzid,t1.payment_name,t1.bill_amount,t1.receive_amount,t1.difference_amount,COALESCE (t2.unpaid_amount,0) unpaid_amount,COALESCE ((t1.difference_amount-COALESCE (t2.unpaid_amount,0)),0) as paid_amount from (" +
////				" select report_date,shift_id,waiter_id,waiter_name,shift_name,jzid,payment_name,coalesce (sum(bill_amount), 0) as bill_amount,coalesce (sum(receive_amount), 0) as receive_amount,coalesce (coalesce (sum(bill_amount), 0) - coalesce (sum(receive_amount), 0),0) as difference_amount " +
////				" from t group by report_date,shift_id,waiter_id,waiter_name,shift_name,jzid,payment_name) t1 " +
////				" left join (select jzid,coalesce (sum(bill_amount), 0) as unpaid_amount from t where payment_state = '" + SysDictionary.PAYMENT_STATE_PAY + "' group by payment_state,jzid) t2 " +
////				" on t1.jzid = t2.jzid order by t1.waiter_id,t1.shift_id,t1.jzid ");
//
//		StringBuilder condition = new StringBuilder();
//		condition.append(" and bi.tenancy_id= '").append(tenancyId).append("' and bi.store_id = '").append(storeId).append("' and bi.report_date ='").append(reportDate).append("'");
//
//		StringBuilder cashierRcvSql = new StringBuilder();
//		cashierRcvSql.append(" select *,(coalesce(difference_amount, 0)-coalesce(unpaid_amount, 0)) as paid_amount from (");
//		cashierRcvSql.append(" select bl.report_date,bl.shift_id,du.shift_name,bl.cashier_num as waiter_id,em.name as waiter_name,bl.jzid,pw.payment_name,bl.bill_amount,coalesce(pcrl.receive_amount,0) receive_amount,(coalesce(bl.bill_amount,0) - coalesce(pcrl.receive_amount, 0)) as difference_amount,bl.unpaid_amount");
//		cashierRcvSql.append(" from (select bi.tenancy_id,bi.store_id,bi.report_date,bi.shift_id,bi.cashier_num,py.jzid,sum(py.currency_amount)as bill_amount,sum(case when bi.bill_property='CLOSED' then 0 else py.currency_amount end) as unpaid_amount from public.pos_bill_payment py left join pos_bill bi on py.tenancy_id=bi.tenancy_id and py.store_id=bi.store_id and py.bill_num=bi.bill_num left join hq_devices hd on bi.tenancy_id = hd.tenancy_id and bi.store_id = hd.store_id and bi.pos_num = hd.devices_code where (hd.show_type = 'YD' or hd.show_type = 'PAD') ").append(condition).append(" group by bi.tenancy_id,bi.store_id,bi.report_date,bi.shift_id,bi.cashier_num,py.jzid) bl");
//		cashierRcvSql.append(" left join (select tenancy_id,store_id,report_date,pay_shift_id as shift_id,waiter_id,payment_id,sum(amount) as receive_amount from pos_cashier_receive_log bi where 1=1 ").append(condition).append(" group by tenancy_id,store_id,report_date,pay_shift_id,waiter_id,payment_id) as pcrl on bl.tenancy_id = pcrl.tenancy_id and bl.store_id = pcrl.store_id and bl.report_date = pcrl.report_date and bl.shift_id = pcrl.shift_id and cast(bl.cashier_num as int) = pcrl.waiter_id and bl.jzid = pcrl.payment_id");
//		cashierRcvSql.append(" left join employee em  on bl.tenancy_id = em.tenancy_id and bl.store_id = em.store_id and cast(bl.cashier_num as int) = em.id");
//		cashierRcvSql.append(" left join (select doo.tenancy_id,doo.organ_id,dor.id as shift_id,sd.class_item as shift_name from duty_order as dor left join duty_order_of_ogran as doo on dor.tenancy_id = doo.tenancy_id and dor.id = doo.duty_order_id left join sys_dictionary sd on  dor.tenancy_id = sd.tenancy_id and dor.name = sd.class_item_code and sd.class_identifier_code ='duty') du on bl.tenancy_id = du.tenancy_id and bl.store_id = du.organ_id and bl.shift_id = du.shift_id");
//		cashierRcvSql.append(" left join (select pw.tenancy_id,pw.id,coalesce(sd.class_item,pw.payment_name1) as payment_name  from payment_way pw left join sys_dictionary sd on pw.payment_name1=sd.class_item_code and sd.class_identifier_code='currency') pw on bl.tenancy_id=pw.tenancy_id and bl.jzid=pw.id");
//		cashierRcvSql.append(" where bl.cashier_num='").append(optNum).append("' and bl.shift_id='").append(shiftId).append("') t");
//
//		List<JSONObject> receivelist = posDao.query4Json(tenancyId, cashierRcvSql.toString());
//		JSONObject result = new JSONObject();
//		result.put("receivelist", receivelist);
//
//		List<JSONObject> resultList = new ArrayList<JSONObject>();
//		resultList.add(result);
//
//		return resultList;
//	}
//
//	@Override
//	public synchronized void addCashierReceive(String tenancyId, int storeId, List<?> param,JSONObject printJson) throws Exception
//	{
//		if (param == null || param.size() <= 0 || param.isEmpty())
//		{
//			throw new SystemException(PosErrorCode.PARAM_ERROR);
//		}
//		JSONObject paraJson = JSONObject.fromObject(param.get(0));
//		if (Tools.isNullOrEmpty(paraJson.opt("report_date")))
//		{
//			throw new SystemException(PosErrorCode.NOT_NULL_REPORT_DATE);
//		}
//
//		String report_date = ParamUtil.getDateStringValue(paraJson,"report_date");
//		Date reportDate = DateUtil.parseDate(report_date);
//		Integer shiftId = paraJson.optInt("shift_id");
//		String operType = paraJson.optString("oper_type");
//		Integer waiterId = paraJson.optInt("waiter_id");
//		String waiterName = paraJson.optString("waiter_name");
//		String deviceNum = paraJson.optString("device_num");
//		Integer cashierId = paraJson.optInt("cashier_id");
//		String cashierName = paraJson.optString("cashier_name");
//		String posNum = paraJson.optString("pos_num");
//		String opt_num = paraJson.optString("opt_num");
//		double amount = paraJson.optDouble("amount");
//		Integer jzid = paraJson.optInt("jzid");
//		Integer payShiftId = paraJson.optInt("pay_shift_id");
//		//验证是否签到
//		posDao.checkKssy(reportDate, opt_num, storeId);
//
//		if(amount<=0)
//		{
//			throw SystemException.getInstance(PosErrorCode.AMOUNT_NOT_MORE_ZERO_ERROR);
//		}
//
//		if(Tools.isNullOrEmpty(waiterName))
//		{
//			waiterName = posDao.getEmpNameById(waiterId.toString(), tenancyId, storeId);
//		}
//		if(Tools.isNullOrEmpty(cashierName))
//		{
//			cashierName = posDao.getEmpNameById(cashierId.toString(), tenancyId, storeId);
//		}
//		Timestamp currentTime = DateUtil.currentTimestamp();
//
//		Double receiveAmount = posDao.getReceiveAmountByPayment(tenancyId, storeId, reportDate, waiterId, jzid);
//
//		if (amount > receiveAmount)
//		{
//			throw SystemException.getInstance(PosErrorCode.RECEIVE_AMOUNT_MORE_PAYMENT_AMOUNT_ERROR);
//		}
//
//		Object[] objs = new Object[]{tenancyId,storeId,reportDate,shiftId,operType,waiterId,waiterName,deviceNum,amount,cashierId,cashierName,posNum,DateUtil.currentTimestamp(),"0",jzid,payShiftId};
//		StringBuilder updateCashierRcvSql = new StringBuilder(" insert into pos_cashier_receive_log (tenancy_id,store_id,report_date,shift_id,oper_type,waiter_id,waiter_name,device_num,amount,cashier_id,cashier_name,pos_num,last_updatetime,upload_tag,payment_id,pay_shift_id) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ");
//		posDao.update(updateCashierRcvSql.toString(), objs);
//
//		StringBuilder queryIdSql = new StringBuilder(" select id from pos_cashier_receive_log order by id desc limit 1");
//		SqlRowSet rs = posDao.query4SqlRowSet(queryIdSql.toString(), new Object[]{});
//		Integer receive_id = 0;
//		if (rs.next())
//		{
//			receive_id = rs.getInt("id");
//		}
//
//		StringBuilder updateCashierRcvDetailsSql = new StringBuilder(" insert into pos_cashier_receive_log_details (tenancy_id,store_id,receive_id,cashier_id,pos_num,waiter_id,bill_num,last_updatetime,upload_tag) " +
//				" select ?,?,?,?,?,?,a.bill_num,?,0 from (select bill_num from pos_bill_payment " +
//				" where tenancy_id=? and store_id=? and report_date=? and shift_id=? and jzid=? and payment_state='" +SysDictionary.PAYMENT_STATE_PAY_COMPLETE + "' " +
//				 "and bill_num not in (select bill_num from pos_cashier_receive_log_details)) a ");
//		posDao.update(updateCashierRcvDetailsSql.toString(), new Object[]
//		{ tenancyId,storeId,receive_id,cashierId,deviceNum,waiterId,currentTime,tenancyId,storeId,reportDate,payShiftId,jzid });
//
//		JSONObject paymentJson =posDao.getPaymentWayByID(tenancyId, storeId, jzid);
//
//		if(null ==printJson)
//		{
//			printJson = new JSONObject();
//		}
//		printJson.put("cashier_name", cashierName);
//		printJson.put("amount", amount);
//		printJson.put("waiter_name", waiterName);
//		printJson.put("operator", cashierName);
//		printJson.put("payment_name", paymentJson.optString("payment_name"));
//
//		printJson.put("print_code", SysDictionary.PRINT_CODE_1303);
//		printJson.put("mode", "1");
//		printJson.put("pos_num", posNum);
//		printJson.put("opt_num",opt_num);
//		printJson.put("report_date", report_date);
//		printJson.put("printCount", 2);
//
////		posDao.customerPrint(tenancyId, storeId, posNum, "1014", "0", printJson, 2);
//		//记录POS日志
//		posDao.savePosLog(tenancyId, storeId, posNum, cashierId.toString(), cashierName, shiftId, reportDate, Constant.TITLE, "交款/抽大钞", "收款人：" + cashierId + "，收款机台：" + posNum, "交款人：" + cashierId + "，交款机台：" + posNum + "，交款金额：" + String.format("%.2f", amount));
//	}
//
//	@Override
//	public List<JSONObject> findCashierReceive(String tenancyId, int storeId, List<?> param, Pagination pagination) throws Exception
//	{
//		if (param == null || param.size() <= 0 || param.isEmpty())
//		{
//			throw new SystemException(PosErrorCode.PARAM_ERROR);
//		}
//
//		JSONObject paraJson = JSONObject.fromObject(param.get(0));
//		Integer shiftId = paraJson.optInt("shift_id");
//		String optNum = paraJson.optString("opt_num");
//		String startDate = paraJson.optString("start_date");
//		String endDate = paraJson.optString("end_date");
//
//		StringBuilder condition = new StringBuilder(" where tenancy_id='" + tenancyId + "' and store_id='" + storeId + "' ");
//		if (Tools.hv(shiftId))
//		{
//			condition.append(" and shift_id='" + shiftId + "' ");
//		}
//		if (Tools.hv(optNum))
//		{
//			condition.append(" and cashier_id='" + optNum + "' ");
//		}
//		if (Tools.hv(startDate))
//		{
//			condition.append(" and to_char(report_date,'yyyy-MM-dd')>='" + startDate + "' ");
//		}
//		if (Tools.hv(endDate))
//		{
//			condition.append(" and to_char(report_date,'yyyy-MM-dd')<='" + endDate + "' ");
//		}
//
//		StringBuilder querySql = new StringBuilder(" select report_date,shift_id,oper_type,waiter_id,waiter_name,device_num,amount,cashier_id,cashier_name,pos_num,last_updatetime from pos_cashier_receive_log ");
//		querySql.append(condition);
//
//		int totalCount = posDao.queryForInt("select count(1) as count from (" + querySql.toString() + ") t", new Object[] {});
//		if (pagination != null && pagination.getPagesize() > 0)
//		{
//			String orderBy = "waiter_id";
//			String ascDesc = "desc";
//			int pageNo = 1;
//			int limit = pagination.getPagesize();
//			if ((pagination.getPageno() - 1) > 0)
//			{
//				pageNo = pagination.getPageno();
//			}
//			if (!"".equals(pagination.getOrderby())) {
//				orderBy = pagination.getOrderby();
//			}
//
//			if (pagination.isAsc() == true)
//			{
//				ascDesc = "asc";
//			}
//
//			querySql.append(" order by " + orderBy + " " + ascDesc+ " limit " + limit + " offset " + (pageNo - 1) * pagination.getPagesize());
//		}
//		else
//		{
//			querySql.append(" order by waiter_id desc ");
//		}
//
//		List<JSONObject> receivelist = posDao.query4Json(tenancyId, querySql.toString(), new Object[]{});
//
//		pagination.setPageno(pagination.getPageno() > 0 ? pagination.getPageno() : 1);
//		pagination.setTotalcount(totalCount);
//
//		if (pagination.getPagesize() == 0)
//		{
//			pagination.setPagesize(receivelist.size());
//		}
//
//		StringBuilder totalAmountSql = new StringBuilder(" select coalesce(sum(amount),0) as total_amount from pos_cashier_receive_log ");
//		totalAmountSql.append(condition);
//		SqlRowSet rst = posDao.query4SqlRowSet(totalAmountSql.toString(), new Object[]{});
//
//		JSONObject result =  new JSONObject();
//		result.put("receivelist", receivelist);
//		if (rst.next())
//		{
//			result.put("total_amount", rst.getDouble("total_amount"));//交款金额合计
//		}
//
//		List<JSONObject> resultList = new ArrayList<JSONObject>();
//		resultList.add(result);
//		return resultList;
//	}
//
//	@Override
//	public void checkReceive(Data param, Data result)  throws Exception
//	{
//		String tenancyId = param.getTenancy_id();
//		Integer storeId = param.getStore_id();
//
//		Map<String, Object> map = ReqDataUtil.getDataMap(param);
//
//		Date reportDate = ParamUtil.getDateValue(map, "report_date", false, null);
//		String optNum = ParamUtil.getStringValue(map, "opt_num", false, null);
//
//		//查询签到时间
//
//		String sql = new String("select opt_num,last_updatetime from pos_opt_state where opt_num=? and report_date=? and content=? and tag=? and store_id=? and tenancy_id=? order by id desc limit 1");
//		//查询签退时间
//		String tcsql = new String("select last_updatetime from pos_opt_state where opt_num=? and report_date=? and content=? and tag=? and store_id=? and tenancy_id=?  order by id desc limit 1");
//
//		Timestamp kssyTime = null;
//		Timestamp tcTime = null;
//		SqlRowSet rs3 = posDao.query4SqlRowSet(sql, new Object[]
//		{ optNum, reportDate,SysDictionary.OPT_STATE_KSSY,'0', storeId,tenancyId });
//		if (rs3.next())
//		{
//			kssyTime = rs3.getTimestamp("last_updatetime");
//		}
//
//		if (kssyTime == null)
//		{
//			throw new SystemException(PosErrorCode.POS_CASHIER_NOT_SIGNIN_ERROR);
//		}
//		else
//		{
//			SqlRowSet rst = posDao.query4SqlRowSet(tcsql, new Object[]
//			{ optNum, reportDate, SysDictionary.OPT_STATE_YYTC, '0', storeId,tenancyId });
//			if (rst.next())
//			{
//				tcTime = rst.getTimestamp("last_updatetime");
//			}
//
//			if (tcTime != null)
//			{
//				throw new SystemException(PosErrorCode.POS_CASHIER_ALREADY_SIGNOUT_ERROR);
//			}
//		}
//		//现在时间
////		Timestamp currentTime = DateUtil.currentTimestamp();
//
////		String receiveSql = new String("select COALESCE((COALESCE(sum(bill.bill_amount),0)-COALESCE(sum(pcrl.receive_amount),0)),0) AS difference_amount from " +
////				" (SELECT pbp.tenancy_id,pbp.store_id,pbp.report_date,pbp.shift_id,pbp.cashier_num AS waiter_num,COALESCE (SUM(pbp.currency_amount),0) AS bill_amount FROM pos_bill_payment pbp,hq_devices hd " +
////				" WHERE pbp.tenancy_id = '" + tenancyId + "' AND pbp.store_id = '" + storeId + "' AND pbp.report_date = '" + reportDate + "' AND pbp.payment_state = '" + SysDictionary.PAYMENT_STATE_PAY_COMPLETE + "' AND pbp. TYPE = '" + SysDictionary.PAYMENT_CLASS_CASH + "' AND pbp.last_updatetime>='" + kssyTime + "' AND pbp.last_updatetime<='" + currentTime + "' AND pbp.store_id = hd.store_id AND pbp.pos_num = hd.devices_code AND hd.valid_state = '1' AND hd.show_type = 'YD' " +
////				" GROUP BY pbp.tenancy_id, pbp.store_id, pbp.report_date, pbp.shift_id, pbp.cashier_num) as bill " +
////				" LEFT JOIN(SELECT store_id, report_date, shift_id, waiter_id, SUM (amount) AS receive_amount FROM pos_cashier_receive_log " +
////				" WHERE tenancy_id = '" + tenancyId + "' AND store_id = '" + storeId + "' AND report_date = '" + reportDate + "' AND last_updatetime>='" + kssyTime + "' AND last_updatetime<='" + currentTime + "' " +
////				" GROUP BY store_id, report_date, shift_id, waiter_id ) AS pcrl " +
////				" ON bill.store_id = pcrl.store_id AND bill.report_date = pcrl.report_date AND bill.shift_id = pcrl.shift_id AND CAST (bill.waiter_num AS INT) = pcrl.waiter_id");
//
////		StringBuffer receiveSql = new StringBuffer();
////		receiveSql.append(" select (coalesce(sum(bill.bill_amount),0)-coalesce(sum(pcrl.receive_amount),0)) as difference_amount from ");
////		receiveSql
////				.append(" (select pbp.tenancy_id,pbp.store_id,pbp.report_date,pbp.shift_id,pbp.cashier_num as waiter_num,coalesce (sum(pbp.currency_amount),0) as bill_amount from pos_bill_payment pbp inner join hq_devices hd on pbp.tenancy_id = hd.tenancy_id and pbp.store_id = hd.store_id and pbp.pos_num = hd.devices_code");
////		receiveSql.append(" where pbp.tenancy_id = '").append(tenancyId).append("' and pbp.store_id = '").append(storeId).append("' and pbp.report_date = '").append(reportDate).append("' and pbp.payment_state = '").append(SysDictionary.PAYMENT_STATE_PAY_COMPLETE)
////				.append("' and hd.valid_state = '1' and hd.show_type in ('YD','PAD')");
////		receiveSql.append(" group by pbp.tenancy_id, pbp.store_id, pbp.report_date, pbp.shift_id, pbp.cashier_num) as bill");
////		receiveSql.append(" left join(select tenancy_id,store_id,report_date,pay_shift_id,waiter_id,sum(amount) as receive_amount from pos_cashier_receive_log where tenancy_id = '").append(tenancyId).append("' and store_id = '").append(storeId).append("' and report_date = '").append(reportDate)
////				.append("' group by tenancy_id,store_id,report_date,pay_shift_id,waiter_id ) as pcrl");
////		receiveSql.append(" on bill.tenancy_id = pcrl.tenancy_id and bill.store_id = pcrl.store_id and bill.report_date = pcrl.report_date and bill.shift_id = pcrl.pay_shift_id and cast (bill.waiter_num as int) = pcrl.waiter_id");
//
//		StringBuilder condition = new StringBuilder();
//		condition.append("pbp.tenancy_id = '").append(tenancyId).append("' and pbp.store_id = '").append(storeId).append("' and pbp.report_date='").append(reportDate).append("'");
//
//		StringBuilder receiveSql = new StringBuilder();
//		receiveSql.append(" select (coalesce(sum(bl.bill_amount),0)-coalesce(sum(pcrl.receive_amount),0)) as difference_amount");
//		receiveSql.append(" from (select bi.tenancy_id,bi.store_id,bi.report_date,bi.shift_id,cast(bi.cashier_num as int) as waiter_num,coalesce(sum(pbp.currency_amount),0) AS bill_amount from pos_bill_payment pbp left join pos_bill bi on pbp.tenancy_id=bi.tenancy_id and pbp.store_id=bi.store_id and pbp.bill_num=bi.bill_num left join hq_devices hd on bi.tenancy_id = hd.tenancy_id and bi.store_id = hd.store_id and bi.pos_num = hd.devices_code where ").append(condition).append(" and (hd.show_type = 'YD' or hd.show_type = 'PAD') group by bi.tenancy_id,bi.store_id,bi.report_date,bi.shift_id,bi.cashier_num) as bl");
//		receiveSql.append(" left join (select pbp.tenancy_id,pbp.store_id,pbp.report_date,pbp.pay_shift_id as shift_id,pbp.waiter_id,sum(pbp.amount) as receive_amount from pos_cashier_receive_log pbp where ").append(condition).append(" group by pbp.tenancy_id,pbp.store_id,pbp.report_date,pbp.pay_shift_id,pbp.waiter_id) as pcrl on bl.tenancy_id = pcrl.tenancy_id and bl.store_id = pcrl.store_id and bl.report_date = pcrl.report_date and bl.shift_id = pcrl.shift_id and bl.waiter_num = pcrl.waiter_id");
//		receiveSql.append(" left join employee em  on bl.tenancy_id = em.tenancy_id and bl.store_id = em.store_id and bl.waiter_num = em.id");
//		receiveSql.append(" left join (select doo.tenancy_id,doo.organ_id,dor.id as shift_id,sd.class_item as shift_name from duty_order as dor left join duty_order_of_ogran as doo on dor.tenancy_id = doo.tenancy_id and dor.id = doo.duty_order_id left join sys_dictionary sd on  dor.tenancy_id = sd.tenancy_id and dor.name = sd.class_item_code and sd.class_identifier_code ='duty') du on bl.tenancy_id = du.tenancy_id and bl.store_id = du.organ_id and bl.shift_id = du.shift_id");
//
//		SqlRowSet rs = posDao.query4SqlRowSet(receiveSql.toString());
//		if (rs.next())
//		{
//			//交款差额合计>0的时候不可签退
//			if( rs.getDouble("difference_amount") > 0){
//				throw new SystemException(PosErrorCode.CASHIER_RECEIVE_ADD_FAILURE);
//			}
//		}
//		result.setCode(Constant.CODE_SUCCESS);
//		result.setMsg(Constant.CHECK_RECEIVE_SUCCESS);
//	}

    @Override
    public void extractBills(Data param, Data result) throws SystemException {
        try {
            String tenantId = param.getTenancy_id();
            Integer organId = param.getStore_id();

            Map<String, Object> map = ReqDataUtil.getDataMap(param);

            String posNum = ParamUtil.getStringValue(map, "pos_num", false, null);//收款机号

            String optNum = ParamUtil.getStringValue(map, "opt_num", false, null);//操作员编号

            Date reportDate = ParamUtil.getDateValue(map, "report_date", false, null);//报表日期

            Integer shiftId = ParamUtil.getIntegerValue(map, "shift_id", false, null);//班次id

            String authorizeId = ParamUtil.getStringValue(map, "authorize_id", false, null);// 授权人ID

            String insertSql = new String("insert into pos_byjdc_log (tenancy_id,store_id,pos_num,opt_num,opt_name,opt_type,report_date,shift_id,last_updatetime,amount,opt_login_number,upload_tag,face_value,count,authorize_id) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ");

            String optName = posDao.getEmpNameById(optNum, tenantId, organId);

            Timestamp time = DateUtil.currentTimestamp();

            String qloginCount = new String("select count(id) from pos_opt_state where content='KSSY' and tag='1' and report_date = ? and opt_num= ?");
            int loginCount = posDao.queryForInt(qloginCount, new Object[]{reportDate, optNum});

            @SuppressWarnings("unchecked")
            List<Map<String, Object>> list = (List<Map<String, Object>>) map.get("item");

            for (int k = 0; k < list.size(); k++) {
                Map<String, Object> mapInfo = list.get(k);
                String faceValue = ParamUtil.getStringValue(mapInfo, "face_value", false, null);
                Integer count = ParamUtil.getIntegerValue(mapInfo, "count", false, null);
                double amount = DoubleHelper.mul(Double.valueOf(faceValue), Double.valueOf(String.valueOf(count)), 2);

                posDao.update(insertSql, new Object[]{tenantId, organId, posNum, optNum, optName, "bills", reportDate, shiftId, time, amount, loginCount, 0, faceValue, count, authorizeId});
            }

            result.setCode(Constant.CODE_SUCCESS);
            result.setMsg(Constant.EXTRACT_BILLS_SUCCESS);
            return;
        } catch (Exception se) {
            logger.info("抽大钞失败：" + ExceptionMessage.getExceptionMessage(se));
            se.printStackTrace();
            result.setCode(Constant.CODE_INNER_EXCEPTION);
            result.setMsg(Constant.EXTRACT_BILLS_FAILURE);
            return;
        }
    }

    @Override
    public void saveOpenBoxLog(Data param, Data result) throws SystemException {
        String tenantId = param.getTenancy_id();
        Integer organId = param.getStore_id();

        Map<String, Object> map = ReqDataUtil.getDataMap(param);
        String posNum = ParamUtil.getStringValue(map, "pos_num", false, null);//收款机号
        String optNum = ParamUtil.getStringValue(map, "opt_num", false, null);//操作员编号
        Date reportDate = ParamUtil.getDateValue(map, "report_date", false, null);//报表日期
        Integer shiftId = ParamUtil.getIntegerValue(map, "shift_id", false, null);//班次id
        String managerNum = ParamUtil.getStringValue(map, "manage_num", false, null);
        String operatType = ParamUtil.getStringValue(map, "operat_type", false, null);
        String remark = ParamUtil.getStringValue(map, "remark", false, null);
        Timestamp time = DateUtil.currentTimestamp();

        try {
            //记录开钱箱日志
            StringBuilder sql = new StringBuilder(" insert into pos_open_cashbox_log (tenancy_id,store_id,report_date,shift_id,opt_num,pos_num,manage_num,operat_type,last_updatetime,remark,upload_tag) values (?,?,?,?,?,?,?,?,?,?,?)");

            posDao.update(sql.toString(), new Object[]
                    {tenantId, organId, reportDate, shiftId, optNum, posNum, managerNum, operatType, time, remark, '0'});
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("Exception:" + ExceptionMessage.getExceptionMessage(e));
            throw SystemException.getInstance(PosErrorCode.OPER_ERROR);
        }

    }

    @Override
    public void getExtractBillsHistory(Data param, Data result) throws SystemException {
        String tenancyId = param.getTenancy_id();
        Integer storeId = param.getStore_id();

        Map<String, Object> map = ReqDataUtil.getDataMap(param);
        Date reportDate = ParamUtil.getDateValue(map, "report_date", false, null);
        Integer shiftId = ParamUtil.getIntegerValue(map, "shift_id", false, null);
        String posNum = ParamUtil.getStringValue(map, "pos_num", false, null);
        String optNum = ParamUtil.getStringValue(map, "opt_num", false, null);

        StringBuilder querySql = new StringBuilder(" select opt_num,opt_name,last_updatetime,amount,face_value,count from pos_byjdc_log " +
                " where tenancy_id='" + tenancyId + "' and store_id='" + storeId + "' and report_date='" + reportDate + "' and shift_id='" + shiftId + "' and opt_type='bills' ");

        if (Tools.hv(posNum)) {
            querySql.append(" and pos_num='" + posNum + "' ");
        }
        if (Tools.hv(optNum)) {
            querySql.append(" and opt_num='" + optNum + "' ");
        }
        querySql.append(" order by opt_num,last_updatetime desc ");

        try {
            List<JSONObject> lists = posDao.query4Json(tenancyId, querySql.toString());
            result.setData(lists);
            result.setCode(Constant.CODE_SUCCESS);
            result.setMsg(Constant.GET_EXTRACT_BILLS_SUCCESS);
        } catch (Exception e) {
            logger.info("查询抽大钞历史失败：" + ExceptionMessage.getExceptionMessage(e));
            e.printStackTrace();
            result.setCode(Constant.CODE_INNER_EXCEPTION);
            result.setMsg(Constant.GET_EXTRACT_BILLS_FAILURE);
            return;
        }
    }

    @Override
    public void saveBankTrandingLog(Data param, Data result)
            throws SystemException {
        try {
            List<?> paramList = param.getData();
            // 初始化三方交易表中主键ID
            int mainId = posDao.queryForInt("select nextval('pos_thirdpay_id_seq'::regclass) ", null);
            JSONObject data = JSONObject.fromObject(paramList.get(0));

            // 添加三方交易信息
            posDao.savePosThirdpay(param, data, mainId);
            JSONArray thirdpayLogs = data.getJSONArray("thirdpay_log");
            if (thirdpayLogs != null && thirdpayLogs.size() > 0) {
                for (int i = 0; i < thirdpayLogs.size(); i++) {
                    JSONObject thirdpayLogJson = thirdpayLogs.getJSONObject(i);
                    posDao.savePosThirdpayDetail(param, thirdpayLogJson, mainId);
                }
            }
            result.setCode(Constant.CODE_SUCCESS);
            result.setMsg(Constant.SAVE_BANK_TRANDING_LOG_SUCCESS);
        } catch (Exception e) {
            logger.info("银行支付接口保存记录失败：" + ExceptionMessage.getExceptionMessage(e));
            e.printStackTrace();
            result.setCode(Constant.CODE_INNER_EXCEPTION);
            result.setMsg(Constant.SAVE_BANK_TRANDING_LOG_FAILURE);
            return;
        }

    }

    @Override
    public void queryBankTrandingLog(JSONObject jsobj, Data param, Data result)
            throws SystemException {
        try {

            JSONObject posThirdpay = posDao.queryPosThirdpay(jsobj, param);

            if (posThirdpay != null) {
                int mainId = posThirdpay.getInt("id");// 三方交易表中主键ID
                List<JSONObject> posThirdpayDetail = posDao.queryPosThirdpayDetail(param.getTenancy_id(), mainId);
                if (posThirdpayDetail != null && posThirdpayDetail.size() > 0) {
                    posThirdpay.put("thirdpay_log", posThirdpayDetail);// 三方交易原始数据记录表
                }
                posThirdpay.remove("id");//返回时无需主键ID
                List<JSONObject> posThirdpayLst = new ArrayList<JSONObject>();
                posThirdpayLst.add(posThirdpay);
                result.setData(posThirdpayLst);
                result.setCode(Constant.CODE_SUCCESS);
                result.setMsg(Constant.QUERY_BANK_TRANDING_LOG_SUCCESS);
            } else {
                result.setCode(Constant.CODE_NULL_DATASET);
                result.setMsg(Constant.CODE_NULL_LOG);
            }
        } catch (Exception e) {
            logger.info("银行支付接口查询记录失败：" + ExceptionMessage.getExceptionMessage(e));
            e.printStackTrace();
            result.setCode(Constant.CODE_INNER_EXCEPTION);
            result.setMsg(Constant.QUERY_BANK_TRANDING_LOG_FAILURE);
            return;
        }

    }

    @Override
    public void uploadPrintFormat(Data prarm) throws Exception {
//		String postUrl = "/hqRest/post";
//		Map<String, String> systemMap = com.tzx.framework.common.constant.Constant.getSystemMap();

        logger.info("请求地址: " + PosPropertyUtil.getMsg("saas.url") + postUrl + "      请求参数: " + JSONObject.fromObject(prarm).toString());
        String responseResult = HttpUtil.sendPostRequest(PosPropertyUtil.getMsg("saas.url") + postUrl, JSONObject.fromObject(prarm).toString());
        if (responseResult != null) {
            JSONObject json = JSONObject.fromObject(responseResult);
            boolean success = json.optBoolean("success");
            if (!success) {
                logger.error("打印模板上传失败");
                throw SystemException.getInstance(PosErrorCode.UPLOAD_PRINTFORMAT_FAIL);
            }
        }
    }

    @Override
    public void changTaste(Data param, Data result) throws SystemException {
        try {
            String tenantId = param.getTenancy_id();
            Integer organId = param.getStore_id();

            Map<String, Object> map = ReqDataUtil.getDataMap(param);

            Date report_date = ParamUtil.getDateValue(map, "report_date", true, PosErrorCode.NOT_NULL_REPORT_DATE);

            String optNum = ParamUtil.getStringValue(map, "opt_num", true, PosErrorCode.NOT_NULL_OPT_NUM);

            String posNum = ParamUtil.getStringValue(map, "pos_num", true, PosErrorCode.NOT_NULL_POS_NUM);

            Integer shiftId = ParamUtil.getIntegerValue(map, "shift_id", true, PosErrorCode.NOT_NULL_SHIFT_ID);
            String billNum = ParamUtil.getStringValue(map, "bill_num", true, PosErrorCode.NOT_NULL_BILL_NUM);//账单号
            String mode = ParamUtil.getStringValue(map, "mode", true, PosErrorCode.NOT_NULL_MODE);// 模式 1: 整单备注;2:单品备注;
            Integer rwid = 0;
            String taste = ParamUtil.getStringValue(map, "taste", false, null);

            JSONObject billJson = posDishDao.getPosBillByBillnum(tenantId, organId, billNum);

            String billProperty = null;
            String paymentState = null;
            if (null != billJson) {
                billProperty = billJson.optString("bill_property");
                paymentState = billJson.optString("payment_state");
            }

            if (SysDictionary.BILL_PROPERTY_CLOSED.equals(billProperty)) {
                throw SystemException.getInstance(PosErrorCode.BILL_CLOSED);
            }
            if (SysDictionary.PAYMENT_STATE_PAY.equals(paymentState)) {
                throw SystemException.getInstance(PosErrorCode.BILL_PAYMENT_STATE_PAYING_ERROR);
            }

            //判断日始
            String qDayBegin = new String("select count(id) from pos_opt_state where content='DAYBEGAIN' and report_date = ? and store_id=? and tenancy_id=?");
            int bcount = posDao.queryForInt(qDayBegin, new Object[]
                    {report_date, organId, tenantId});
            if (bcount == 0) {
                throw new SystemException(PosErrorCode.NOT_EXIST_DAYBEGAIN_ERROR);
            }
            if (mode.equals(Constant.MODE_ALL)) {//整单模式
                String updateBillSql = "update pos_bill set bill_taste =? where tenancy_id=? and store_id =? and bill_num = ?";
                posDao.update(updateBillSql, new Object[]{taste, tenantId, organId, billNum});//修改账单表的整单备注
            } else if (mode.equals(Constant.MODE_SINGLETON)) {// 单品模式
                String billitemSql = "select item_property ,id ,item_id,item_serial,setmeal_rwid,setmeal_id from pos_bill_item where  tenancy_id =? and store_id=?  and rwid=? and bill_num=? ";
                rwid = ParamUtil.getIntegerValue(map, "rwid", true, PosErrorCode.NOT_NULL_RWID);
                List<JSONObject> billItemJsons = posDao.query4Json(tenantId, billitemSql, new Object[]{tenantId, organId, rwid, billNum});
                if (billItemJsons != null && billItemJsons.size() > 0) {
                    JSONObject billItemJson = billItemJsons.get(0);
                    String item_property = billItemJson.optString("item_property");
                    if (!item_property.isEmpty() && item_property.equals(SysDictionary.ITEM_PROPERTY_SINGLE)) {//如果是单品
                        String updateSql = "update  pos_bill_item set item_taste =? where    id=? ";
                        posDao.update(updateSql, new Object[]{taste, billItemJson.optInt("id")});//修改账单明细的口味备注
                    } else if (!item_property.isEmpty() && item_property.equals(SysDictionary.ITEM_PROPERTY_SETMEAL)) {//如果是套餐
                        String updateSql = "update  pos_bill_item set item_taste =? where  setmeal_rwid=? and setmeal_id =? and  and bill_num=?";
                        posDao.update(updateSql, new Object[]{taste, billItemJson.optInt("setmeal_rwid"), billItemJson.optInt("setmeal_id"), billItemJson.optInt("bill_num"),});//修改套餐明细项和主项的口味备注
                    }
                }
            }
            //记录POS日志
            String optName = posDao.getEmpNameById(optNum, tenantId, organId);
            posDao.savePosLog(tenantId, organId, posNum, optNum, optName, shiftId, report_date, Constant.TITLE, "修改口味成功", null, null);
            result.setCode(Constant.CODE_SUCCESS);
            result.setMsg(Constant.CHANG_TASTE_SUCCESS);
        } catch (Exception e) {
            logger.info("修改口味失败：" + ExceptionMessage.getExceptionMessage(e));
            e.printStackTrace();
            result.setCode(Constant.CODE_INNER_EXCEPTION);
            result.setMsg(Constant.CHANG_TASTE_FAILURE);
            return;
        }

    }

    @Override
    public void updateDevicesDataState() throws Exception {
        try {
//			String deleteSql = "delete from hq_devices_datastate ";
//			posDao.update(deleteSql, null);
//			String insertSql =  "insert into hq_devices_datastate (devices_code,refresh_flag) select distinct devices_code,'0' from hq_devices where devices_code is not null and devices_code <> ''";
//			posDao.update(insertSql, null);

            String insertSql = "insert into hq_devices_datastate (devices_code,refresh_flag) select distinct hd.devices_code,'0' from hq_devices hd left join hq_devices_datastate hdd on hd.devices_code=hdd.devices_code where hd.valid_state='1' and hd.devices_code is not null and hd.devices_code <> '' and hdd.devices_code is null";
            posDishDao.update(insertSql, null);
            String updateSql = "update hq_devices_datastate set refresh_flag='0' where refresh_flag<>'0'";
            posDishDao.update(updateSql, null);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
            logger.error("更新机台状态失败", e);
        }
    }

    @Override
    public void getDevicesDataState(String devicesCode, Data result)
            throws Exception {
        String sql = "select *  from hq_devices_datastate where devices_code='" + devicesCode + "' and  refresh_flag='1' ";
        SqlRowSet rs = posDao.query4SqlRowSet(sql);
        List<JSONObject> datas = new ArrayList<JSONObject>();
        JSONObject data = new JSONObject();
        data.put("refresh_flag", "0");
        if (rs.next()) {
            data.put("refresh_flag", "1");
        }
        datas.add(data);
        result.setData(datas);
    }

    @Override
    public void updateDevicesByCode(String devicesCode, String state)
            throws Exception {
        String update = "update  hq_devices_datastate set refresh_flag = ? where devices_code=? ";
        posDao.update(update, new Object[]{state, devicesCode});
    }


    @SuppressWarnings("unchecked")
    @Override
    public void pushPrintState(Data param, Data result) {
        try {
            getPrinterState(param, result);
            List<JSONObject> list = (List<JSONObject>) result.getData();
            boolean flag = true;
            Data cjData = new Data();
            String printName = "";
            if (null != list && list.size() > 0) {
                for (JSONObject obj : list) {
                    if (!"0".equals(obj.opt("print_state"))) {
                        flag = false;
                        printName = printName + obj.optString("print_name") + ",";
                    }
                }
                if (flag) {
                    result.setMsg("打印机全部正常");
                } else {
                    result.setMsg("打印机" + printName + "异常");
                }
            } else {
                result.setMsg("没有找到打印机状态信息");
            }
            cjData.setType(Type.PRINT);
            cjData.setOper(Oper.notice);
            cjData.setData(result.getData());
            Comet4jUtil.sendMessage2All(Comet4jUtil.POS_PC, JSONObject.fromObject(cjData).toString());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void getPrinterState(Data param, Data result) {

        String tenancyId = param.getTenancy_id();
        Integer storeId = param.getStore_id();
        List<JSONObject> dataList = new ArrayList<JSONObject>();
        try {
            if (StringUtils.isNotEmpty(tenancyId) && StringUtils.isNotEmpty(storeId + "")) {
                String sql = new String("select name,statedesc,printstate from hq_printer_new  where valid_state = ? and tenancy_id = ? and store_id = ?");
                SqlRowSet rs = posDao.query4SqlRowSet(sql, new Object[]{"1", tenancyId, storeId});
                while (rs.next()) {
                    JSONObject obj = new JSONObject();
                    obj.put("print_name", rs.getString("name"));
                    obj.put("error_reason", rs.getString("statedesc"));
                    String printState = rs.getString("printstate");
                    obj.put("print_state", printState);
                    if ("0".equals(printState)) {
                        obj.put("print_state_name", SysDictionary.PRINT_STATE_OK);
                    } else if ("1".equals(printState)) {
                        //obj.put("print_state_name", SysDictionary.PRINT_STATE_RESTART);
                        obj.put("print_state_name", SysDictionary.PRINT_STATE_EXCEPTION);
                    } else if ("2".equals(printState)) {
                        obj.put("print_state_name", SysDictionary.PRINT_SERVICE_UNNSUAL);
                    } else {
                        obj.put("print_state_name", SysDictionary.PRINT_STATE_UNUSUAL);
                    }
                    dataList.add(obj);
                }
            }
            result.setMsg(Constant.QUERY_PRINTER_STATE_SUCCESS);
            result.setCode(Constant.CODE_SUCCESS);
            result.setSuccess(true);
            result.setData(dataList);
        } catch (Exception e) {
            logger.info("查询打印机状态失败:" + ExceptionMessage.getExceptionMessage(e));
            result.setMsg(Constant.QUERY_PRINTER_STATE_FAILURE);
            result.setCode(Constant.CODE_INNER_EXCEPTION);
            result.setSuccess(false);
            result.setData(dataList);
            e.printStackTrace();
        }
    }

    /**
     * 获得菜品销售数据和售卖模式数据
     *
     * @param tenancyId
     * @param storeId
     * @param optNum
     * @param posNum
     * @param startTimestamp
     * @param endTimestamp
     * @param obj
     * @param reportDate
     * @throws Exception
     */
    public void getBillItemReport(String tenancyId, int storeId, String optNum, String posNum, Timestamp startTimestamp, Timestamp endTimestamp, JSONObject obj, Date reportDate) throws Exception {
        // 菜品销售实收
        double item_actual_money = 0d;
        //菜品优惠金额
        double item_yh_money = 0d;
        //菜品奉送金额
        double free_money_item = 0d;
        //菜品退菜金额
        double back_money_item = 0d;
        // 菜品销售流水
        double item_list_money = 0d;

        //售卖模式数据  堂食
        double ts_actual_money = 0d;
        //售卖模式数据 外带
        double wd_actual_money = 0d;
        //售卖模式数据 外送
        double ws_actual_num = 0d;
        //售卖模式数据 外送
        double ws_actual_amount = 0d;
        // 售卖模式数据 合计
        double countsell = 0d;
        SqlRowSet rs = null;
        StringBuffer itemSql = new StringBuffer("select coalesce(sum(case when bi.item_remark in ('CJ05', 'TC01') then 0 else coalesce(bi.real_amount, 0) end ),0) as item_actual_money,  ");
        itemSql.append(" coalesce(sum((bi.discount_amount + bi.discountr_amount - bi.single_discount_amount) ), 0) as item_yh_money ,  ");
        itemSql.append(" coalesce(sum(case when bi.item_remark = 'FS02' then coalesce(bi.item_amount, 0) end),0) as free_money_item,");
        itemSql.append(" coalesce( sum(case when bi.item_remark in ('TC01', 'QX04') then coalesce(bi.item_amount, 0) end),0) as back_money_item, ");
        itemSql.append(" coalesce( sum(case when bi.item_remark in ('CJ05') then 0 else coalesce(bi.item_amount, 0)end),0) as item_list_money,");
        itemSql.append(" coalesce(sum(case when bi.sale_mode = 'TS01'  then coalesce(bi.real_amount, 0)end),0) as ts_actual_money, ");
        itemSql.append("  coalesce(sum(case when bi.sale_mode = 'WD02'  then coalesce(bi.real_amount, 0) end),0) as wd_actual_money,");
        itemSql.append("  coalesce(sum(case when bi.sale_mode = 'WS03' then bi.item_count else 0 end),0) as ws_actual_num, ");
        itemSql.append("  coalesce(sum(case when bi.sale_mode = 'WS03' then coalesce(bi.real_amount, 0) end),0)  as ws_actual_amount");
        itemSql.append(" from pos_bill_item bi inner join pos_bill pb on  pb.bill_num = bi.bill_num and  pb.bill_property = 'CLOSED'and (bi.item_property ='SINGLE'or  bi.item_property ='SETMEAL') and bi.report_date=? and pb.payment_time>=? and pb.payment_time<=? where pb.tenancy_id=? and pb.store_id=? ");
        if (StringUtils.isEmpty(optNum)) {
            if (StringUtils.isEmpty(posNum)) {
                rs = posDao.query4SqlRowSet(itemSql.toString(), new Object[]{reportDate, startTimestamp, endTimestamp, tenancyId, storeId});
            } else {
                itemSql.append(" and pb.pos_num = ? ");
                rs = posDao.query4SqlRowSet(itemSql.toString(), new Object[]{reportDate, startTimestamp, endTimestamp, tenancyId, storeId, posNum});
            }

        } else {
            if (StringUtils.isEmpty(posNum)) {
                itemSql.append(" and pb.cashier_num = ? ");
                rs = posDao.query4SqlRowSet(itemSql.toString(), new Object[]{reportDate, startTimestamp, endTimestamp, tenancyId, storeId, optNum});
            } else {
                itemSql.append(" and pb.cashier_num = ? ");
                itemSql.append(" and pb.pos_num = ? ");
                rs = posDao.query4SqlRowSet(itemSql.toString(), new Object[]{reportDate, startTimestamp, endTimestamp, tenancyId, storeId, optNum, posNum});
            }

        }


        if (rs.next()) {
            item_actual_money = rs.getDouble("item_actual_money");
            item_yh_money = rs.getDouble("item_yh_money");
            free_money_item = rs.getDouble("free_money_item");
            back_money_item = rs.getDouble("back_money_item");
            item_list_money = rs.getDouble("item_list_money");

            ts_actual_money = rs.getDouble("ts_actual_money");
            wd_actual_money = rs.getDouble("wd_actual_money");
            ws_actual_num = rs.getDouble("ws_actual_num");
            ws_actual_amount = rs.getDouble("ws_actual_amount");
            countsell = ts_actual_money + wd_actual_money + ws_actual_amount;
        }
        obj.put("item_actual_money", item_actual_money);
        obj.put("item_yh_money", item_yh_money);
        obj.put("free_money_item", free_money_item);
        obj.put("back_money_item", back_money_item);
        obj.put("item_list_money", item_list_money);
        obj.put("ts_actual_money", ts_actual_money);
        obj.put("wd_actual_money", wd_actual_money);
        obj.put("ws_actual_num", ws_actual_num);
        obj.put("ws_actual_amount", ws_actual_amount);
        obj.put("countsell", countsell);

    }

    /**
     * 本地验证优惠卷
     *
     * @param param
     * @param result
     */
    @SuppressWarnings({"unchecked"})
    public void getCouponsValiData(Data param, Data result) {

        String tenancyId = param.getTenancy_id();//门店id
        Integer storeId = param.getStore_id();//机构ID
        Map<String, Object> map = ReqDataUtil.getDataMap(param);
        String billNum = ParamUtil.getStringValue(map, "bill_num", true, PosErrorCode.NOT_NULL_BILL_NUM);//账单号
        String billMoney = (String) map.get("bill_money");

        List<JSONArray> couponsList = (List<JSONArray>) map.get("coupons_list");//获取list集合
        List<JSONObject> obList = new ArrayList<JSONObject>();
        //订单金额不能是0
        if (billMoney.equals("0")) {
            result.setCode(PosErrorCode.LOCAL_COUPONS_BILL_MONEY_ERROR.getNumber());
            result.setMsg(PosErrorCode.LOCAL_COUPONS_BILL_MONEY_ERROR.getMessage());
        }

        String typeId = ""; //菜品劵类型
//		String couponsCode ="";//优惠卷编号
        for (int i = 0; i < couponsList.size(); i++) {
            JSONArray array = JSONArray.fromObject(couponsList.get(i));
            JSONObject entity = (JSONObject) array.toArray()[0];
            typeId += entity.getInt("coupons_class") + ",";
//			couponsCode = entity.getString("coupons_code")+",";//优惠卷编号
            obList.add(entity);
        }
//		//优惠卷编号
//		if(!StringUtils.isEmpty(couponsCode)){
//			couponsCode = couponsCode.substring(0,couponsCode.length()-1);
//		}
        //获取优惠卷类型
        if (!StringUtils.isEmpty(typeId)) {
            typeId = typeId.substring(0, typeId.length() - 1);
        }
		/*//验证优惠卷是否可用
		List<JSONObject> couponsCodeList = null;
		try {
			couponsCodeList = posDao.getCouponsValiCode(tenancyId,couponsCode);
		} catch (Exception e1) {
			e1.printStackTrace();
		}
		for (JSONObject json : couponsCodeList) {
			String startCoupon = json.getString("start_coupon");
			String endDate = json.getString("end_coupon");
			String useCycle = json.getString("use_cycle");//使用周日
			String createDate = json.getString("send_time");//创建日期
			String sysDateTime =DateUtil.format(DateUtil.currentTimestamp(), "yyyy-MM-dd");
    		long millionSeconds= DateUtil.getMillionSeconds(sysDateTime);
		    if(!StringUtils.isEmpty(startCoupon) && !StringUtils.isEmpty(endDate)){
	    		long startCouponSeconds = DateUtil.getMillionSeconds(startCoupon);
	    		long strEndSeconds = DateUtil.getMillionSeconds(endDate);
	    		//判断优惠卷使用时间是否开始
	    		if(startCouponSeconds > millionSeconds){
	    			result.setCode(Constant.LOCAL_COUPONS_START_TIME_CODE);
	    			result.setMsg(Constant.LOCAL_COUPONS_START_TIME);
	    		}else if(strEndSeconds < millionSeconds){
	    			result.setCode(Constant.LOCAL_COUPONS_BB_OVERDUE_CODE);
	    			result.setMsg(Constant.LOCAL_COUPONS_BB_OVERDUE_EMS);
	    		}
		    }else if(!StringUtils.isEmpty(useCycle)){
		    	//使用周期判断
		    	long createMillionSeconds= DateUtil.getMillionSeconds(createDate);
	    		if((millionSeconds -createMillionSeconds) < Long.parseLong(useCycle)){
	    			result.setCode(Constant.LOCAL_COUPONS_DAY_EXPIRED_CODE);
	    			result.setMsg(Constant.LOCAL_COUPONS_DAY_EXPIRED_EMS);
	    		}
		    }*/
        //账单金额大于预定金额才能使用
		   /* String billLimitMoney = json.getString("bill_limit_money");
		    if(!StringUtils.isEmpty(billLimitMoney)){
		    	if(Double.parseDouble(billMoney) < Double.parseDouble(billLimitMoney)){
		    		result.setCode(Constant.LOCAL_COUPONS_NOT_ENOUGH_CODE);
	    			result.setMsg(Constant.LOCAL_COUPONS_NOT_ENOUGH_EMS);
		    	}
		    }
		}*/

        Collections.sort(obList, new Comparator<JSONObject>() {
            public int compare(JSONObject o1, JSONObject o2) {
                //排序优惠卷是不是全低
                int order = o2.getString("is_total").compareTo(o1.getString("is_total"));
                if (order != 0) {
                    return order > 0 ? -1 : 1;
                }
                //排序部分低价格
                order = o1.getString("face_value").compareTo(o2.getString("face_value"));
                if (order != 0) {
                    return order > 0 ? -1 : 1;
                }
                return 0;
            }
        });

        List<JSONObject> arrayList = new ArrayList<JSONObject>();
        List<JSONObject> jsonList = new ArrayList<JSONObject>();
        JSONObject jsonAll = new JSONObject();
        Double sumAmount = 0.0D;//全部抵制金额
        try {
//			if(StringUtils.isNotEmpty(tenancyId) && StringUtils.isNotEmpty(storeId+"") && StringUtils.isNotEmpty(billNum)){
            List<JSONObject> dataList = posDao.getCouponsValiData(tenancyId, storeId, billNum, typeId);
            Map<String, Double> itemCountMap = new HashMap<String, Double>();
            for (JSONObject itemJson : dataList) {
                String rwid = ParamUtil.getStringValueByObject(itemJson, "rwid");
                if (false == itemCountMap.containsKey(rwid)) {
                    itemCountMap.put(rwid, ParamUtil.getDoubleValueByObject(itemJson, "item_count"));
                }
            }

            for (int i = 0; i < obList.size(); i++) {
                Boolean flag = false;
                JSONObject entity = obList.get(i);
                String isTotal = entity.getString("is_total");//全低和部分低的表示
                String couponsClass = entity.getString("coupons_class");
                Double faceValue = entity.getDouble("face_value");

                JSONObject objectJson = new JSONObject();
                objectJson.put("coupons_code", entity.getString("coupons_code"));
                objectJson.put("face_value", faceValue);

                Iterator<JSONObject> it = dataList.iterator();
                while (it.hasNext()) {
                    JSONObject stu = (JSONObject) it.next();
                    String rwid = stu.getString("rwid");
                    Double itemCount = itemCountMap.get(rwid);
                    //判断该优惠卷是否能兑换
                    if (stu.getString("type_id").equals(couponsClass) && 0 < itemCount) {
                        Double realAmount = stu.getDouble("real_amount");//获得菜的实际金额
                        Double realPrice = DoubleHelper.roundUp(DoubleHelper.div(realAmount, stu.getDouble("item_count"), 4), 2);
                        Double discountAmount = realPrice;//每一道菜的金额

                        if (isTotal.equals("2")) {
                            // 部分低处理
                            discountAmount = (faceValue > realPrice) ? realPrice : faceValue;
                            sumAmount += discountAmount;
                            objectJson.put("currency_amount", discountAmount);
                        } else {
                            //判断是否是全低的卷
                            sumAmount += discountAmount;
                            objectJson.put("currency_amount", discountAmount);
                        }

                        flag = true;
                        itemCount = DoubleHelper.sub(itemCount, 1d, 4);
                        itemCountMap.put(rwid, itemCount);
                        if (0 >= itemCount) {
                            it.remove();//删除已兑换过的菜
                        }
                        break;
                    }
                }
                if (flag) {
                    result.setCode(Constant.CODE_SUCCESS);
                    arrayList.add(objectJson);
                } else {
                    result.setCode(PosErrorCode.LOCAL_COUPONS_NO_ITEM_ERROR.getNumber());
                    result.setMsg(PosErrorCode.LOCAL_COUPONS_NO_ITEM_ERROR.getMessage());
                }
            }
            //抵制金额不能大于订单金额
            if (sumAmount > Double.parseDouble(billMoney)) {
                result.setCode(PosErrorCode.LOCAL_COUPONS_AMOUNT_ERROR.getNumber());
                result.setMsg(PosErrorCode.LOCAL_COUPONS_AMOUNT_ERROR.getMessage());
            }
            jsonAll.put("coupons_list", arrayList);
            jsonAll.put("sumamount", sumAmount);
            jsonList.add(jsonAll);
            result.setData(jsonList);
//			}else{
//				result.setSuccess(false);
//				result.setCode(Constant.CODE_PARAM_FAILURE);//参数验证失败
//			}
        } catch (Exception e) {
            logger.info(ExceptionMessage.getExceptionMessage(e));
            result.setSuccess(false);
            result.setData(arrayList);
            e.printStackTrace();
        }
    }


    @Override
    public void setTakeMealNum(String tenancyId, int storeId, String type, String reportDate) throws SystemException {
        try {
            Integer result = 0;
            List<JSONObject> kk = posDao.query4Json(tenancyId, "select current_value from sys_code_values where code_type='" + type + "' and store_id=" + storeId + " and prefix ='" + reportDate + "' LIMIT 1");
            if (kk.size() > 0) {
                result = kk.get(0).optInt("current_value");
                int i = result.intValue();
                //获取POS快餐最大餐牌号
                String maxNum = posDishDao.getSysParameter(tenancyId, storeId, "POSKCZDCPH");
                if ("".equals(maxNum)) {
                    maxNum = "0";
                }
                //如果取餐号等于设置最大值，重置为1重新开始排号
//				if(i >= Integer.parseInt(maxNum) && i != 1) {
                if (Integer.parseInt(maxNum) > 0 && i >= Integer.parseInt(maxNum)) {
                    String update = "update sys_code_values set current_value = ? where code_type= ? and store_id= ? and prefix = ?";
                    if (!"".equals(reportDate)) {
                        posDao.update(update, new Object[]{0, type, storeId, reportDate});
                        //清除缓存数据
                        posCodeDao.delete(Code.POS_BILL_DISH_CODE.getType());
                    }
                }
            }
        } catch (Exception e) {
            logger.info(ExceptionMessage.getExceptionMessage(e));
            e.printStackTrace();
        }
    }

//	/**
//	 * 无码优惠卷核单
//	 * @param param
//	 * @param result
//	 * @throws Exception
//	 * @throws SystemException
//	 */
//	@Deprecated
//	public void getNocodeCoupons(Data param, Data result) throws Exception, SystemException {
//		String tenancyId = param.getTenancy_id();//门店id
//		Integer storeId = param.getStore_id();//机构ID
//		Map<String, Object> map = ReqDataUtil.getDataMap(param);
//		String billNum = ParamUtil.getStringValue(map, "bill_code", true, PosErrorCode.NOT_NULL_BILL_NUM);//账单号
//		String chanel = ParamUtil.getStringValue(map, "chanel", false, null);//账单号
//		String businessDate = ParamUtil.getStringValue(map, "report_date", false, null);//业务日期
//		List<JSONArray> couponsList = (List<JSONArray>) map.get("couponslist");//获取list集合
//		try{
//			for (int i=0;i<couponsList.size();i++) {
//				JSONArray array = JSONArray.fromObject(couponsList.get(i));
//				JSONObject json = (JSONObject) array.toArray()[0];
//				String typeId = json.getString("type_id");//优惠券类型ID
//				String itemId = json.getString("item_id");//菜品ID
//				String unitId = json.getString("unit_id");//规格id
//				String price = json.getString("price");//规格id
//				String itemNum = json.getString("item_num");//菜品数量id
//				String discountMoney =  json.getString("discount_money");//优惠金额
//				String discountNum = json.getString("discount_num")	;
//				String classId     = json.getString("class_id");
//				String dealValue = json.getString("deal_value");//面值
//				String dealName = json.getString("deal_name");//券类型名称
//				String remark = json.getString("remark");//券说明
//				String couponsPro = json.getString("coupons_pro");//优惠券/菜品/菜品卷
//				posDao.insertPosCouponsUseItem(tenancyId, storeId, chanel,  DateUtil.parseDate(businessDate), billNum, Integer.parseInt(typeId), Integer.parseInt(itemId), Integer.parseInt(unitId), Double.valueOf(price), Double.valueOf(itemNum), Double.valueOf(discountMoney),  Double.valueOf(discountNum),Integer.valueOf(classId),Double.parseDouble(dealValue), dealName,remark, couponsPro);
//
//
//			}
//			result.setSuccess(true);
//			result.setCode(Constant.CODE_SUCCESS);
//		}catch(Exception e){
//			logger.info(ExceptionMessage.getExceptionMessage(e));
//			result.setSuccess(false);
//			e.printStackTrace();
//		}
//	}
//
//
//	/**
//	 * 无码优惠券查询
//	 * @param param
//	 * @param result
//	 * @throws Exception
//	 * @throws SystemException
//	 */
//	@Override
//	public void getNocodeCouponsValiData(Data param, Data result) throws Exception, SystemException {
//		try{
//			String tenancyId = param.getTenancy_id();//门店id
//			Integer storeId = param.getStore_id();//机构ID
//			Map<String, Object> map = ReqDataUtil.getDataMap(param);
//			Map<String, Double> item_unit_realprice = new HashMap<String, Double>();//规格id：金额
//			Map<String, Integer> item_unit = new HashMap<String, Integer>();//规格id:菜品ID
//			Map<String, Integer> item_unit_count = new HashMap<String, Integer>();//菜品规格id:菜品数量
//			String billNum = ParamUtil.getStringValue(map, "bill_code", true, PosErrorCode.NOT_NULL_BILL_NUM);//账单号
//			Double billMoney = ParamUtil.getDoubleValue(map, "bill_money", true, PosErrorCode.NOT_NULL_PAYMENT_AMOUNT);//账单金额
//			String chanel = ParamUtil.getStringValue(map, "chanel", true, PosErrorCode.CHANEL_NOTEXIST_ERROE);//消费渠道
//			String reportDateString = ParamUtil.getStringValue(map, "report_date", false, null);//报表日期
//			Date reportDate = ParamUtil.getDateValue(map, "report_date", false, null);//报表日期
//			//订单金额不能是0
//			if(billMoney.equals("0")){
//				result.setCode(PosErrorCode.LOCAL_COUPONS_BILL_MONEY_ERROR.getNumber());
//				result.setMsg(PosErrorCode.LOCAL_COUPONS_BILL_MONEY_ERROR.getMessage());
//			}
//			StringBuffer sb = new StringBuffer();//组织sql
//			sb.append("select cct.with_discount,cct.use_cycle,ccc.class_name,cct.activity_subject,"+
//			            "ccc.coupons_pro,ccc.help_memory_code,cct.start_coupon,cct.end_coupon,cct.remark,cct.face_value,"+
//					    "cct.id as type_id,cct.father_id,cct.used_other,cct.used_main,cct.bill_limit_money,cct.bill_limit_num,"+
//			            "cct.class_id,cct.is_appoin_item,cct.is_income,ccc.begintime,ccc.endtime,ccc.coupons_pro,ccc.is_total,"+
//					    "(select array_to_string(array(SELECT ccd.unit_id FROM crm_coupons_details ccd WHERE ccd.type_id=cct.class_id),',')) as details,"+
//			            "(SELECT array_to_string(ARRAY (SELECT concat(cctt.begintime,'_',cctt.endtime) FROM crm_coupons_type_times cctt "+
//					    "WHERE cctt.coupons_type = cct. ID),',')) AS times,(case cct.validity_type when 1 then  now()< concat(to_char((ccc.create_time + COALESCE (cct.validity_days, 0) * INTERVAL '1 day'),'YYYY-MM-DD'),' 23:59:59')::timestamp else true end) as crt   " +
//					 	"from crm_coupons_type cct ,crm_coupons_class ccc,crm_coupons_org cco,crm_coupons_chanel cc2 " +
//						"WHERE cct.class_id = ccc. ID "+
//						"AND cco.type_id = cct. ID AND cco.store_id = "+storeId+
//						" AND cc2.type_id = cct. ID AND cc2.chanel = '"+chanel+
//					    "' AND ccc.valid_state='1' and ccc.create_time is not null ORDER BY ccc.coupons_pro desc");//1为启用中的优惠券0为停用
//			List<JSONObject> couponsList = posDao.query4Json(tenancyId,sb.toString());////查出的优惠券list集合
//			List<JSONObject> listresult = new ArrayList<JSONObject>();;////过滤的优惠券list集合
//
//			List<JSONObject> billList = posDishDao.findBill(tenancyId, storeId, billNum);
//
//			firstListFor : for(JSONObject json: couponsList)
//			{
//				String startCoupon = json.getString("start_coupon");
//				String endDate = json.getString("end_coupon");
//				String billLimitMoney = json.optString("bill_limit_money");
//				String className = json.optString("class_name");
//				String classid = json.optString("class_id");
//				String typeid = json.optString("type_id");
//				int isTotal = json.optInt("is_total");//是否可抵菜品总价；1、抵菜品总价2、按照面值抵菜品价格
//				double faceValue = Scm.pround(json.optDouble("face_value",0.0));//面值
//				String useCycle = json.getString("use_cycle");//使用周期
//				int used_other = json.optInt("used_other");//1 为不与其他券同时使用
//				int used_main = json.optInt("used_main");//1 为不与主券同时使用
//				String father_id = json.optString("father_id");//主券种id
//				String times = json.optString("times");//09:00:00_15:20:59,16:00:00_18:00:59
//				int billlimitNum = json.optInt("bill_limit_num");//账单限制数量
//				int withDiscount = json.optInt("with_discount");//是否与可以其他优惠同时使用
//				int isWithDiscount = 0;//是否与其他优惠同时使用了？
//				String[] details = json.optString("details").split(",");//菜品券unit_id规格id
//				String couponsPro = json.optString("coupons_pro");//券编码菜品券编码coupons_dish代金券编码coupons_deduct
//				String crt = json.optString("crt");//是否过期validity_type=1过期时间x天，validity_type=0有效时间段
//				String nowweek = DateUtil.dayForWeek(reportDateString)+"";//返回当天是星期几
//				if(nowweek.equals("7")){//为了适应总部存的星期数据
//					nowweek = "1";
//				}else {
//					nowweek = (Integer.parseInt(nowweek) + 1)+"";
//				}
//				String sysDateTime =DateUtil.format(DateUtil.currentTimestamp(), "yyyy-MM-dd");
//	    		long millionSeconds= DateUtil.getMillionSeconds(sysDateTime);
//
//	    		//判断优惠卷开始时间和结束时间
//				if(!StringUtils.isEmpty(startCoupon) && !StringUtils.isEmpty(endDate) && !"null".equals(startCoupon)){
//		    		long startCouponSeconds = DateUtil.getMillionSeconds(startCoupon);
//		    		long strEndSeconds = DateUtil.getMillionSeconds(endDate);
//		    		if(startCouponSeconds > millionSeconds){
//		    			//result.setCode(Constant.LOCAL_COUPONS_START_TIME_CODE);
//		    			//result.setMsg(Constant.LOCAL_COUPONS_START_TIME);
//		    			continue;
//		    		}else if(strEndSeconds < millionSeconds){
//		    			//result.setCode(Constant.LOCAL_COUPONS_BB_OVERDUE_CODE);
//		    			//result.setMsg(Constant.LOCAL_COUPONS_BB_OVERDUE_EMS);
//		    			continue;
//		    		}
//			    }
//
//				//账单金额大于预定金额才能使用
//			    if(!StringUtils.isEmpty(billLimitMoney)){
//			    	if(billMoney < Double.parseDouble(billLimitMoney)){
//			    		//result.setCode(Constant.LOCAL_COUPONS_NOT_ENOUGH_CODE);
//		    			//result.setMsg(Constant.LOCAL_COUPONS_NOT_ENOUGH_EMS);
//			    		continue;
//			    	}
//			    }
//
//			    //判断useCycle使用周期
//			    if(useCycle.indexOf(nowweek)<0){
//			    	continue;
//			    }
//
//			    //判断使用时段
//			    Time now1 = DateUtil.getNowHHMMSSDate();
//			    if(times != null && !"".equals(times) && !"null".equals(times))
//				{
//					String[] timesarr = times.split(",");
//					int ff = 0;//不在有效时间内变量
//					for(String tbe:timesarr)
//					{
//						String[] tbearr = tbe.split("_");
//						if(tbearr.length==2 && tbearr[0].length()==8 &&tbearr[1].length()==8)
//						{
//							Time b1 = DateUtil.parseTime(tbearr[0]);
//							Time e1 = DateUtil.parseTime(tbearr[1]);
//							if(now1.after(b1) && now1.before(e1))
//							{
//								ff = 1;
//								continue;//跳出这for循环
//							}
//						}
//					}
//					if(ff == 0)
//					{
//						continue;
//					}
//				}
//
//			    //优惠券有效期判断
//			    if("false".equals(crt)){
//			    	continue;
//			    }
//
//			    //判断with_discount是否与其他优惠一起使用
//			    for(JSONObject bill: billList){
//			    	if(withDiscount > 0 && bill.optDouble("discount_amount") > 0.0){
//			    		//一起使用了
//			    		isWithDiscount = 1;
//			    	}else{
//			    		isWithDiscount = 0;
//			    	}
//			    }
//			    if(isWithDiscount > 0){
//			    	continue;
//			    }
//
//			    //账单限制数量=账单限制总数量-账单使用券数量
//			    //根据账单iD查询优惠劵付款记录
//			    List<JSONObject> paymentList = pospaymentDao.getPaymentCouponsByBillNum(tenancyId, storeId, billNum);
//			    String usedClassId = "";
//			    String usedTypeId = "";
//			    if(paymentList !=null && paymentList.size() > 0){//已经付过款有记录
//			    	for(JSONObject paymentjson : paymentList){
//			    		String paytypeid = paymentjson.optString("type_id");
//			    		String payclassid = paymentjson.optString("class_id");
//			    		Integer new_used_other = paymentjson.optInt("used_other");
//			    		if(new_used_other != null && new_used_other == 1){
//			    			break firstListFor;//支付过一次之后跳过剩下循环
//			    		}
//			    		if(payclassid.equals(json.optString("class_id")) && paytypeid.equals(json.optString("type_id")) && paymentjson.optInt("coupon_type")==2){
//			    			int discountNum = paymentjson.optInt("discount_num");//抵用数量
//			    			billlimitNum = billlimitNum - discountNum;
//			    		}
//			    		usedClassId = usedClassId + payclassid;
//			    		usedTypeId = usedTypeId + paytypeid;
//			    	}
//			    }
//
//			    //判断是否与其他券同时使用，根据支付订单typeid和classid判断
//			    if(used_other == 1){//不与其他券同时使用
//			    	if(usedClassId.length() > 0 && usedTypeId.length() > 0){
//			    		continue;
//			    	}
//			    }
//
//			    logger.info("优惠券大类名称："+className+"-----账单限制数量："+billlimitNum);
//			    if(billlimitNum >0){
//			    	json.put("bill_limit_num", billlimitNum);
//			    }else{
//			    	continue;//过了订单最大使用数量，不显示
//			    }
//
//			    StringBuilder bill_item_sql = new StringBuilder("select * from pos_bill_item where bill_num ='"+billNum+"'");
//				List<JSONObject> billItems = posDao.query4Json(tenancyId, bill_item_sql.toString());//订单菜品集合
//			    //菜品券与代金券逻辑稍有不同
//				if("coupons_dish".equals(couponsPro))
//				{
//					for(JSONObject itemjson: billItems)
//					{
//						double real_amount = itemjson.optDouble("real_amount",0.0);//总金额
//						double item_count = itemjson.optDouble("item_count",0.0);//菜品数量
//						String item_unit_id = itemjson.optInt("item_unit_id")+"";//菜品规格id
//						int item_count2 = itemjson.optInt("item_count");
//						if(item_count2==0)
//						{
//							continue;
//						}
//						if(real_amount>0 && item_count>0)
//						{
//							double jsdj = Scm.pdiv(real_amount, item_count);//菜品单价
//							item_unit_realprice.put(item_unit_id, jsdj);
//							item_unit.put(item_unit_id, itemjson.optInt("item_id"));
//
//							if(item_unit_count.containsKey(item_unit_id))
//							{
//								item_unit_count.put(item_unit_id,item_unit_count.get(item_unit_id)+item_count2);
//							}
//							else
//							{
//								item_unit_count.put(item_unit_id, item_count2);
//							}
//						}
//					}
//
//					String used_item_unit_id = "";
//					double dkje = 0.0;//抵扣金额
//					double lsjg = 0.0;//菜品券优惠菜品金额
//
//					for(String udid : details)
//					{
//						if(item_unit_realprice.containsKey(udid))
//						{
//							double up = item_unit_realprice.get(udid);
//							if(up>lsjg)
//							{
//								used_item_unit_id = udid;
//								lsjg = up;
//							}
//						}
//
//					}
//
//					if(used_item_unit_id.length() == 0)
//					{
//						//没有符合条件的菜品或者已经使用完
//						continue;
//					}
//					double cosjjg = item_unit_realprice.get(used_item_unit_id);//菜品单价
//					//抵扣的金额
//					dkje = cosjjg;
//					//菜品券 is_total 1全价 、2 按面值
//					if(isTotal==2 && faceValue < cosjjg)
//					{
//						dkje = faceValue;
//					}
//					json.put("discount", dkje);
//					json.put("unit_id",Integer.parseInt(used_item_unit_id));
//					json.put("item_id", item_unit.get(used_item_unit_id));
//					json.put("item_amount", cosjjg);//菜品单价
//					json.put("item_count", item_unit_count.get(used_item_unit_id));//菜品数量
//				}
//				else
//				{
//					double djqdkje = faceValue;//代金券抵扣金额
//					if(djqdkje<0)
//					{
//						djqdkje = 0;
//					}
//
//					json.put("discount", djqdkje);//抵扣金额
//					json.put("unit_id",0);
//					json.put("item_id", 0);
//					json.put("item_amount", 0);//菜品单价
//					json.put("item_count", 0);//菜品数量
//				}
//				json.put("father_id", father_id);//father_id传整数前端会报错，和刘工商量，这里传给他字符类型的
//				json.remove("with_discount");//前端用不到的参数，删不删的吧
//				json.put("activity_subject", className);//无码券涉及不到活动，所以这里手动赋值，为了activity_subject不为空
//				listresult.add(json);
//			}
//			logger.info("返回有效无码券："+listresult.toString());
//			result.setData(listresult);
//			result.setCode(Constant.CODE_SUCCESS);
//	    	result.setMsg(Constant.LOCAL_COUPONS_SUCCESS);
//
//		}catch(Exception e){
//			logger.info(ExceptionMessage.getExceptionMessage(e));
//			result.setSuccess(false);
//			e.printStackTrace();
//		}
//
//	}

    @Override
    public void findTotalAmount(Data param, Data result) throws Exception, SystemException {
        try {
//			logger.info("开始查询账单金额");
            String tenancyId = param.getTenancy_id();//门店id
            Integer storeId = param.getStore_id();//机构ID
            Map<String, Object> map = ReqDataUtil.getDataMap(param);
            Date reportDate = ParamUtil.getDateValue(map, "report_date", false, PosErrorCode.NOT_NULL_REPORT_DATE);
            String posNum = ParamUtil.getStringValue(map, "pos_num", false, PosErrorCode.NOT_NULL_POS_NUM);//  收款机号
            String open_pos_num = ParamUtil.getStringValue(map, "open_pos_num", false, PosErrorCode.NOT_NULL_POS_NUM);//  开台机器号
//			String order_num = ParamUtil.getStringValue(map, "order_num", false, PosErrorCode.NOT_NULL_POS_NUM);//订单号
            String optNum = ParamUtil.getStringValue(map, "opt_num", false, PosErrorCode.NOT_NULL_OPT_NUM);//开台人
            String open_opt = ParamUtil.getStringValue(map, "open_opt", false, PosErrorCode.NOT_NULL_OPT_NUM);//开台人
//			String table_code = ParamUtil.getStringValue(map, "table_code", false, PosErrorCode.NOT_NULL_OPT_NUM);//桌位
//			String table_property_id  = ParamUtil.getStringValue(map, "table_property_id ", false, PosErrorCode.NOT_NULL_OPT_NUM);//桌类
//			String bill_num  = ParamUtil.getStringValue(map, "bill_num", false, PosErrorCode.NOT_NULL_OPT_NUM);//账单编号
//			String shift_id  = ParamUtil.getStringValue(map, "shift_id", false, PosErrorCode.NOT_NULL_OPT_NUM);// 班次
//			String cashier_num   = ParamUtil.getStringValue(map, "cashier_num ", false, PosErrorCode.NOT_NULL_OPT_NUM);// 收款员
            //查询账单状态1为已结账单，2为未结账单
            String billState = ParamUtil.getStringValue(map, "bill_state", false, PosErrorCode.NOT_NULL_BILL_NUM);
//			String billStr=SysDictionary.BILL_PROPERTY_CLOSED;
//			Timestamp endTimestamp = DateUtil.currentTimestamp();
//			Timestamp startTimestamp=null;
//			// 查询last_updatetime
//			StringBuilder str = new StringBuilder("select sd.class_item as name,pos.pos_num,pos.last_updatetime,pos.shift_id from pos_opt_state pos left join duty_order dor on dor.id = pos.shift_id ");
//			str.append(" left join sys_dictionary sd on sd.class_item_code= dor.name and sd.class_identifier_code = 'duty' ");
//			str.append(	" where pos.tenancy_id=? and pos.store_id=? and pos.report_date=? and pos.opt_num=? and  pos.content=? and pos.tag='0' order by pos.id asc");
//			SqlRowSet rs = posDao.query4SqlRowSet(str.toString(), new Object[]
//					{ tenancyId, storeId, reportDate, optNum, SysDictionary.OPT_STATE_KSSY });
//
//			String kssyPosNum = "";
//			if (rs.next())
//			{
//				startTimestamp = rs.getTimestamp("last_updatetime");
//
//			}
            if (Tools.isNullOrEmpty(reportDate)) {
                reportDate = posDishDao.getReportDate(tenancyId, storeId);
            }

            if (optNum == null || optNum.isEmpty()) {
                optNum = open_opt;
            }
            if (posNum == null || posNum.isEmpty()) {
                posNum = open_pos_num;
            }
//			double bill_amount = 0d;// 营业应收
//			double more_coupon = 0d; //多收礼券
//			double sale_person_num = 0d;//消费客数
//			double sale_billnum = 0d;//账单数
//			double payment_total = 0d;//营业实收  = 收款方式合计
//
//			JSONObject jsonObject=new JSONObject();
//			jsonObject=this.getCommon(map,tenancyId,storeId,reportDate,startTimestamp,endTimestamp,optNum,posNum,bill_amount,more_coupon,sale_person_num,sale_billnum,payment_total,jsonObject,4,billState);

            JSONObject jsonObject = getCommon(tenancyId, storeId, map, 4);

            //统计发票金额
            if ("1".equals(billState)) {//如果已结账才能查发票，未结账是没有发票信息的
                List<JSONObject> list1 = posBillDao.findPosBillInvoice(tenancyId, storeId, reportDate, optNum, posNum, map);
                if (null != list1 && list1.size() > 0) {
                    JSONObject jsonObject1 = list1.get(0);
                    String invoice_num = jsonObject1.getString("invoice_num");
                    String invoice_total = jsonObject1.getString("invoice_total");
                    jsonObject.put("invoice_num", invoice_num);
                    jsonObject.put("invoice_total", DoubleHelper.roundDown(Double.parseDouble(invoice_total != null && !"null".equals(invoice_total) ? invoice_total : "0"), 2));
                }
            } else {
                jsonObject.put("invoice_num", "0");
                jsonObject.put("invoice_total", "0");
            }

//			String stateStr=SysDictionary.BILL_PROPERTY_CLOSED;
//			if("2".equals(billState)){
//                stateStr=SysDictionary.BILL_PROPERTY_OPEN;
//            }

            //项目小计,打印次数
            List<JSONObject> list3 = posBillDao.findPosBillSumTotal(tenancyId, storeId, reportDate, optNum, posNum, map);
            if (null != list3 && list3.size() > 0) {
                JSONObject jsonObject2 = list3.get(0);
                String subtotal = jsonObject2.getString("subtotal");//项目小计
                String print_count = jsonObject2.getString("print_count");//打印次数
                if (subtotal == null || "".equals(subtotal) || "null".equals(subtotal)) {
                    subtotal = "0";
                }
                jsonObject.put("subtotal", DoubleHelper.roundDown(Double.parseDouble(subtotal), 2));
                jsonObject.put("print_count", print_count);
            }

            List<JSONObject> list = new ArrayList<JSONObject>();
            list.add(jsonObject);
            logger.info("list>>>>" + list.toString());
            result.setData(list);
            result.setSuccess(true);
            result.setCode(Constant.CODE_SUCCESS);
            result.setMsg(Constant.FIND_TOTAL_SUCCESS);
        } catch (Exception e) {
            logger.info(ExceptionMessage.getExceptionMessage(e));
            result.setSuccess(false);
            e.printStackTrace();
        }

    }


    public JSONObject getCommon(String tenancyId, Integer organId, Map<String, Object> map, int scale) throws Exception {
        JSONObject resultJson = new JSONObject();
        Date reportDate = ParamUtil.getDateValue(map, "report_date", false, PosErrorCode.NOT_NULL_REPORT_DATE);
        Integer shiftId = ParamUtil.getIntegerValue(map, "shift_id", false, null);// 班次
        String orderNum = ParamUtil.getStringValue(map, "order_num", false, null);// 订单号
        String billNum = ParamUtil.getStringValue(map, "bill_num", false, null);// 账单编号
        String batchNum = ParamUtil.getStringValue(map, "batch_num", false, null);// 批次号
        String paymentState = ParamUtil.getStringValue(map, "payment_state", false, null);// 支付状态
        String billState = ParamUtil.getStringValue(map, "bill_state", false, null);
        String tableCode = ParamUtil.getStringValue(map, "table_code", false, null);// 桌位
        String tablePropertyId = ParamUtil.getStringValue(map, "table_property_id ", false, null);// 桌类
        String openOpt = ParamUtil.getStringValue(map, "open_opt", false, null);// 开台人
        String openPosNum = ParamUtil.getStringValue(map, "open_pos_num", false, null);// 开台机器号
        String cashierNum = ParamUtil.getStringValue(map, "cashier_num", false, null);// 收款员
        String posNum = ParamUtil.getStringValue(map, "pos_num", false, null);// 结账机台
        String waiterNum = ParamUtil.getStringValue(map, "waiter_num", false, null);// 开台机器号

        StringBuilder sqlAmount = new StringBuilder("select ");
        sqlAmount.append("sum(pb.discountk_amount) as discountk_amount,");
        sqlAmount.append("sum(pb.discountr_amount) as discountr_amount,");
        sqlAmount.append("sum(pb.maling_amount) as maling_amount,");
        sqlAmount.append("sum(pb.givi_amount) as givi_amount,");
        sqlAmount.append("sum(pb.more_coupon) as more_coupon,");
        sqlAmount.append("sum(coalesce(pb.shop_real_amount,0)) as shop_real_amount,");
        sqlAmount.append("sum(coalesce(pb.platform_charge_amount,0)) as platform_charge_amount,");
        sqlAmount.append("sum(coalesce(service_amount,0)) as service_amount,");
        sqlAmount.append("coalesce(sum(guest), 0) as sale_person_num,");
        sqlAmount.append("count(case when coalesce(pb.bill_state, 'zc') not in ('").append(SysDictionary.BILL_STATE_CJ01).append("', '").append(SysDictionary.BILL_STATE_ZDQX02).append("') then pb.bill_num end) as sale_billnum,");
        sqlAmount.append("sum(pb.bill_amount) as bill_amount,");
        sqlAmount.append("coalesce(sum(pb.payment_amount), 0) as payment_total ");
        sqlAmount.append("from pos_bill pb left join tables_info ti on pb.table_code=ti.table_code ");
        sqlAmount.append("where pb.tenancy_id= ? and pb.store_id = ? ");

        String billProperty = SysDictionary.BILL_PROPERTY_CLOSED;
        if ("2".equals(billState)) {
            billProperty = SysDictionary.BILL_PROPERTY_OPEN;
        }
//		if("1".equals(OrderUtil.getSysPara(tenancyId,organId,"enable_wm_shift"))){//是否启用外卖班次 0:关闭;1:启用;
//			sqlAmount.append(" and pb.shift_id <>").append(com.tzx.orders.base.constant.Constant.WM_SHIFT_ID); //shift_id =-100 外卖班次过滤外卖数据
//		}
        sqlAmount.append(" and pb.shift_id <>").append(com.tzx.orders.base.constant.Constant.WM_SHIFT_ID); //shift_id =-100 外卖班次过滤外卖数据

        if (Tools.hv(billNum)) {
            sqlAmount.append(" and pb.bill_num like '%").append(billNum).append("%'");
        }
        if (Tools.hv(orderNum)) {
            sqlAmount.append(" and pb.order_num like '%").append(orderNum).append("%'");
        }
        if (Tools.hv(batchNum)) {
            sqlAmount.append(" and pb.batch_num like '%").append(batchNum).append("%'");
        }
        if (Tools.hv(reportDate)) {
            sqlAmount.append(" and pb.report_date='").append(reportDate).append("'");
        }
        if (Tools.hv(shiftId) && shiftId > 0) {
            sqlAmount.append(" and pb.shift_id='").append(shiftId).append("'");
        }
        if (Tools.hv(openPosNum)) {
            sqlAmount.append(" and pb.open_pos_num='").append(openPosNum).append("'");
        }
        if (Tools.hv(openOpt)) {
            sqlAmount.append(" and pb.open_opt='").append(openOpt).append("'");
        }
        if (Tools.hv(posNum)) {
            sqlAmount.append(" and pb.pos_num='").append(posNum).append("'");
        }
        if (Tools.hv(cashierNum)) {
            sqlAmount.append(" and pb.cashier_num='").append(cashierNum).append("'");
        }
        if (Tools.hv(waiterNum)) {
            sqlAmount.append(" and pb.waiter_num='").append(waiterNum).append("'");
        }
        if (Tools.hv(tableCode)) {
            sqlAmount.append(" and pb.table_code='").append(tableCode).append("'");
        }
        if (Tools.hv(billProperty)) {
            sqlAmount.append(" and pb.bill_property='").append(billProperty).append("'");
        }
        if (Tools.hv(paymentState)) {
            sqlAmount.append(" and pb.payment_state='").append(paymentState).append("'");
            if (SysDictionary.PAYMENT_STATE_PAY.equals(paymentState)) {
                sqlAmount.append(" and pb.bill_property<>'").append(SysDictionary.BILL_PROPERTY_CLOSED).append("'");
            }
        }
        if (Tools.hv(tablePropertyId) && !"0".equals(tablePropertyId)) {
            sqlAmount.append(" and ti.table_property_id='").append(tablePropertyId).append("' ");
        }

        SqlRowSet rsAmount = posDao.query4SqlRowSet(sqlAmount.toString(), new Object[]
                {tenancyId, organId});

        double bill_amount = 0d;// 营业应收
        double more_coupon = 0d; // 多收礼券
        double sale_person_num = 0d;// 消费客数
        double sale_billnum = 0d;// 账单数
        double payment_total = 0d;// 营业实收 = 收款方式合计
        if (rsAmount.next()) {
            bill_amount = rsAmount.getDouble("bill_amount");
            sale_person_num = rsAmount.getDouble("sale_person_num");
            sale_billnum = rsAmount.getDouble("sale_billnum");
            more_coupon = rsAmount.getDouble("more_coupon");
            payment_total = rsAmount.getDouble("payment_total");
            // 折扣
            resultJson.put("discountk_amount", DoubleHelper.roundDown(rsAmount.getDouble("discountk_amount"), 2));
            // 折让
            resultJson.put("discountr_amount", DoubleHelper.roundDown(rsAmount.getDouble("discountr_amount"), 2));
            // 抹零
            resultJson.put("maling_amount", DoubleHelper.roundDown(rsAmount.getDouble("maling_amount"), 2));
            // 奉送
            resultJson.put("givi_amount", DoubleHelper.roundDown(rsAmount.getDouble("givi_amount"), 2));
            // 多收礼券
            resultJson.put("more_coupon", more_coupon);
            // 商家实收
            resultJson.put("shop_real_amount", DoubleHelper.roundDown(rsAmount.getDouble("shop_real_amount"), 2));
            // 外卖平台收取金额
            resultJson.put("platform_charge_amount", DoubleHelper.roundDown(rsAmount.getDouble("platform_charge_amount"), 2));
            // 服务费
            resultJson.put("service_amount", DoubleHelper.roundDown(rsAmount.getDouble("service_amount"), 2));
            // 营业应收
            resultJson.put("bill_amount", DoubleHelper.roundDown(bill_amount, 2));
            // 消费客数
            resultJson.put("sale_person_num", sale_person_num);
            // 账单数
            resultJson.put("sale_billnum", sale_billnum);

            resultJson.put("payment_total", DoubleHelper.roundDown(payment_total, 2));
            resultJson.put("real_amount", DoubleHelper.sub(payment_total, 0d, scale));

            // 人均均值
            if (sale_person_num != 0) {
                resultJson.put("sale_person_average", DoubleHelper.div(payment_total, sale_person_num, 2));
            } else {
                resultJson.put("sale_person_average", 0);
            }
            // 账单均值
            if (sale_person_num != 0) {
                resultJson.put("sale_billaverage", DoubleHelper.div(bill_amount, sale_billnum, 2));
            } else {
                resultJson.put("sale_billaverage", 0);
            }
        }

        return resultJson;
    }

    @Deprecated
    public JSONObject getCommon(Map<String, Object> map, String tenancyId, Integer organId, Date reportDate, Timestamp startTimestamp, Timestamp endTimestamp, String optNum, String posNum, Double bill_amount, Double more_coupon, Double sale_person_num, Double sale_billnum, Double payment_total, JSONObject obj, int scale, String billState) throws Exception {
        String order_num = ParamUtil.getStringValue(map, "order_num", false, PosErrorCode.NOT_NULL_POS_NUM);//订单号
        String bill_num = ParamUtil.getStringValue(map, "bill_num", false, PosErrorCode.NOT_NULL_OPT_NUM);//账单编号
        String table_code = ParamUtil.getStringValue(map, "table_code", false, PosErrorCode.NOT_NULL_OPT_NUM);//桌位
        String table_property_id = ParamUtil.getStringValue(map, "table_property_id ", false, PosErrorCode.NOT_NULL_OPT_NUM);//桌类
        Integer shift_id = ParamUtil.getIntegerValue(map, "shift_id", false, PosErrorCode.NOT_NULL_OPT_NUM);// 班次
        String cashier_num = ParamUtil.getStringValue(map, "cashier_num ", false, PosErrorCode.NOT_NULL_OPT_NUM);// 收款员

        StringBuilder sqlAmount = new StringBuilder("select ");
        sqlAmount.append("sum(pb.discountk_amount) as discountk_amount,");
        sqlAmount.append("sum(pb.discountr_amount) as discountr_amount,");
        sqlAmount.append("sum(pb.maling_amount) as maling_amount,");
        sqlAmount.append("sum(pb.givi_amount) as givi_amount,");
        sqlAmount.append("sum(pb.more_coupon) as more_coupon,");
        sqlAmount.append("sum(coalesce(pb.shop_real_amount,0)) as shop_real_amount,");
        sqlAmount.append("sum(coalesce(pb.platform_charge_amount,0)) as platform_charge_amount,");
        sqlAmount.append("sum(coalesce(service_amount,0)) as service_amount,");
        sqlAmount.append("coalesce(sum(guest), 0) as sale_person_num,");
        sqlAmount.append("count(case when coalesce(pb.bill_state, 'zc') not in ('CJ01', 'ZDQX02') then pb.bill_num end) as sale_billnum,");
        sqlAmount.append("sum(pb.bill_amount) as bill_amount,");
        sqlAmount.append("coalesce(sum(pb.payment_amount), 0) as payment_total ");
        sqlAmount.append("from pos_bill pb ");
        sqlAmount.append("where pb.tenancy_id= ? and pb.store_id = ? and pb.report_date = ? ");

        List<Object> list1 = new ArrayList<Object>();
        list1.add(tenancyId);
        list1.add(organId);
        list1.add(reportDate);

        String billStateStr = SysDictionary.BILL_PROPERTY_CLOSED;
        if ("2".equals(billState)) {
            billStateStr = SysDictionary.BILL_PROPERTY_OPEN;
        }

        if (billStateStr != null && !"".equals(billStateStr)) {
            sqlAmount.append("  and pb.bill_property = ? ");
            list1.add(billStateStr);
        }
        if (startTimestamp != null) {
            sqlAmount.append(" and pb.payment_time > ? and pb.payment_time < ?");

            list1.add(startTimestamp);
            list1.add(endTimestamp);
        }
        if (order_num != null && !"".equals(order_num)) {
            sqlAmount.append("  and pb.order_num=? ");
            list1.add(order_num);
        }
        if (bill_num != null && !"".equals(bill_num)) {
            sqlAmount.append("  and pb.bill_num=? ");
            list1.add(bill_num);
        }
        if (table_property_id != null && !"".equals(table_property_id)) {
            sqlAmount.append("  and pb.table_code IN (select ti.table_code from tables_info ti where ti.table_property_id=" + table_property_id + ") ");
        }
        if (table_code != null && !"".equals(table_code)) {
            sqlAmount.append("  and pb.table_code=? ");
            list1.add(table_code);
        }
        if (shift_id != null && !"".equals(shift_id.toString())) {
            sqlAmount.append("  and pb.shift_id=? ");
            list1.add(shift_id);
        }
        if (cashier_num != null && !"".equals(cashier_num)) {
            sqlAmount.append("  and pb.cashier_num=? ");
            list1.add(cashier_num);
        }
        if (null != optNum && !"".equals(optNum)) {
            sqlAmount.append(" and pb.open_opt = ? ");
            list1.add(optNum);
        }
        if (null != posNum && !"".equals(posNum)) {
            sqlAmount.append(" and pb.pos_num =? ");
            list1.add(posNum);
        }

        Object[] o = list1.toArray();
        SqlRowSet rsAmount = posDao.query4SqlRowSet(sqlAmount.toString(), o);
        if (rsAmount.next()) {
            bill_amount = rsAmount.getDouble("bill_amount");
            sale_person_num = rsAmount.getDouble("sale_person_num");
            sale_billnum = rsAmount.getDouble("sale_billnum");
            more_coupon = rsAmount.getDouble("more_coupon");
            payment_total = rsAmount.getDouble("payment_total");
            // 折扣
            obj.put("discountk_amount", DoubleHelper.roundDown(rsAmount.getDouble("discountk_amount"), 2));
            // 折让
            obj.put("discountr_amount", DoubleHelper.roundDown(rsAmount.getDouble("discountr_amount"), 2));
            // 抹零
            obj.put("maling_amount", DoubleHelper.roundDown(rsAmount.getDouble("maling_amount"), 2));
            // 奉送
            obj.put("givi_amount", DoubleHelper.roundDown(rsAmount.getDouble("givi_amount"), 2));
            // 多收礼券
            obj.put("more_coupon", more_coupon);
            // 商家实收
            obj.put("shop_real_amount", DoubleHelper.roundDown(rsAmount.getDouble("shop_real_amount"), 2));
            // 外卖平台收取金额
            obj.put("platform_charge_amount", DoubleHelper.roundDown(rsAmount.getDouble("platform_charge_amount"), 2));
            // 服务费
            obj.put("service_amount", DoubleHelper.roundDown(rsAmount.getDouble("service_amount"), 2));
            // 营业应收
            obj.put("bill_amount", DoubleHelper.roundDown(bill_amount, 2));
            //消费客数
            obj.put("sale_person_num", sale_person_num);
            //账单数
            obj.put("sale_billnum", sale_billnum);

            obj.put("payment_total", DoubleHelper.roundDown(payment_total, 2));
            obj.put("real_amount", DoubleHelper.sub(payment_total, 0d, scale));

            //人均均值
            if (sale_person_num != 0) {
//				obj.put("sale_person_average", DoubleHelper.div(bill_amount+more_coupon,sale_person_num,2));
                obj.put("sale_person_average", DoubleHelper.div(payment_total, sale_person_num, 2));
            } else {
                obj.put("sale_person_average", 0);
            }
            //账单均值
            if (sale_person_num != 0) {
                obj.put("sale_billaverage", DoubleHelper.div(bill_amount, sale_billnum, 2));
            } else {
                obj.put("sale_billaverage", 0);
            }
        }
//		StringBuffer paymentTotalSql = new StringBuffer();
////		if("1".equals(billState)){
////			paymentTotalSql.append(" select coalesce(sum(p.currency_amount), 0) as payment_total  from pos_bill_payment p LEFT JOIN pos_bill pb on  p.bill_num = pb.bill_num ");
////		}
////		if("2".equals(billState)){
//			paymentTotalSql.append(" select coalesce(sum(pb.payment_amount), 0) as payment_total from pos_bill pb ");
////		}
//		paymentTotalSql.append(" where pb.store_id = ? and pb.report_date = ? ");
//		List<Object> list2=new ArrayList<Object>();
//		list2.add(organId);
//		list2.add(reportDate);
//		if(startTimestamp!=null){
//			paymentTotalSql.append(" and pb.payment_time > ? and pb.payment_time < ? ");
//			list2.add(startTimestamp);
//			list2.add(endTimestamp);
//		}
//		if(order_num!=null&&!"".equals(order_num)){
//			paymentTotalSql.append("  and pb.order_num=? ");
//			list2.add(order_num);
//		}
//		if(bill_num!=null&&!"".equals(bill_num)){
//			paymentTotalSql.append("  and pb.bill_num=? ");
//			list2.add(bill_num);
//		}
//		if(table_property_id!=null&&!"".equals(table_property_id)){
//			paymentTotalSql.append("  and pb.table_code IN (select ti.table_code from tables_info ti where ti.table_property_id="+table_property_id+") ");
//		}
//		if(table_code!=null&&!"".equals(table_code)){
//			paymentTotalSql.append("  and pb.table_code=? ");
//			list2.add(table_code);
//		}
//		if(shift_id!=null&&!"".equals(shift_id)){
//			paymentTotalSql.append("  and pb.shift_id=? ");
//			list2.add(shift_id);
//		}
//		if(cashier_num!=null&&!"".equals(cashier_num)){
//			paymentTotalSql.append("  and pb.cashier_num=? ");
//			list2.add(cashier_num);
//		}
//		if(null!=optNum&&!"".equals(optNum)){
//			paymentTotalSql.append(" and pb.open_opt = ? ");
//			list2.add(optNum);
//		}
//
//		if(null!=posNum&&!"".equals(posNum)){
//			paymentTotalSql.append(" and pb.pos_num =? ");
//			list2.add(posNum);
//		}
//		paymentTotalSql.append("and pb.bill_property = ?");
//
//		list2.add(billStateStr);
//		Object[] o2=list2.toArray();
//
//		SqlRowSet paymentTotalRs = posDao.query4SqlRowSet(paymentTotalSql.toString(), o2);
//		if(paymentTotalRs.next()){
//			payment_total = paymentTotalRs.getDouble("payment_total");
//		}
//		obj.put("payment_total",DoubleHelper.roundDown(payment_total,2));
//		obj.put("real_amount",DoubleHelper.sub(payment_total, more_coupon, scale));

        return obj;
    }

    @Override
    public String getSaleMode(String tenancyId, Integer storeId, String billNum) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" select sale_mode from pos_bill ");
        sql.append(" where tenancy_id=? and store_id=? and bill_num=?");
        SqlRowSet rs = posDao.query4SqlRowSet(sql.toString(), new Object[]{tenancyId, storeId, billNum});
        if (rs.next()) {
            return rs.getString("sale_mode");
        }
        return null;
    }

    /**
     * 查询总部前一次日结数据状态
     * 未日结不允许次日营业
     * 总部后台需要增加参数控制，控制该功能是否开启
     * 参数名称：日结是否影响次日POS营业
     * 参数说明：0：不影响1：未日结不允许次日POS开启日始
     *
     * @param tenancyId
     * @param storeId
     * @param param
     * @return
     * @throws Exception
     */
    public int getDayCountState(String tenancyId, int storeId, List<?> param) throws Exception {
        JSONObject para = JSONObject.fromObject(param.get(0));
        Date reportDate = DateUtil.parseDate(ParamUtil.getDateStringValue(para, "report_date"));
        //从缓存中获取日结是否影响次日POS营业的控制参数
        Map<?, ?> sysParameterMap = CacheManager.getCacheMap(CacheTableConstant.SYS_PARAMETER.name());
        try {
            int dayCountEffect = Tools.parseInt(CacheTableUtil.getSysParameter("day_count_effect", sysParameterMap), 2);
            if (dayCountEffect == 1) {
                //检测是否收到总部前一次日结数据
//				Date before_date = new Date(reportDate.getTime() - 24 * 60 * 60 * 1000);
                Date before_date = new Date(reportDate.getTime());
                JSONObject daycountPara = JSONObject.fromObject("{}");
                daycountPara.element("day_count", DateUtil.format(before_date));
                int dayCount = this.checkDaycount(tenancyId, storeId, daycountPara);
                return dayCount;
            }
        } catch (Exception e) {
            return -2;
        }
        return -2;
    }

    //	@Override
//	public synchronized void addCashierReceiveBatch(String tenancyId, int storeId, Data param,List<JSONObject> printJsonList) throws Exception {
//		if (param == null)
//		{
//			throw new SystemException(PosErrorCode.PARAM_ERROR);
//		}
//		JSONObject paraJson = JSONObject.fromObject(param.getData().get(0));
////		if (Tools.isNullOrEmpty(paraJson.opt("report_date")))
////		{
////			throw new SystemException(PosErrorCode.NOT_NULL_REPORT_DATE);
////		}
//		Map<String, Object> map = ReqDataUtil.getDataMap(param);
//		Integer cashierId = ParamUtil.getIntegerValue(map, "cashier_id", false,null);
//		String cashierName = ParamUtil.getStringValue(map, "cashier_name", false,null);
//		if(Tools.isNullOrEmpty(cashierName))
//		{
//			cashierName = posDao.getEmpNameById(cashierId.toString(), tenancyId, storeId);
//		}
//		String deviceNum = ParamUtil.getStringValue(map, "device_num", false,null);
//		Integer shiftId = ParamUtil.getIntegerValue(map, "shift_id", false,null);
//		String posNum = ParamUtil.getStringValue(map, "pos_num", false,null);
//		String optNum = ParamUtil.getStringValue(map, "opt_num", false,null);
//		String operType = ParamUtil.getStringValue(map, "oper_type", false,null);
//		String report_date = ParamUtil.getStringValue(map,"report_date", false, null);
//		Date reportDate = DateUtil.parseDate(report_date);
//		List<Map<String, Object>> items = (List<Map<String, Object>>) map.get("item");
//
////		Integer shiftId = paraJson.optInt("shift_id");
////		String operType = paraJson.optString("oper_type");
////		Integer waiterId = paraJson.optInt("waiter_id");
////		String waiterName = paraJson.optString("waiter_name");
////		String deviceNum = paraJson.optString("device_num");
////		Integer cashierId = paraJson.optInt("cashier_id");
////		String cashierName = paraJson.optString("cashier_name");
////		String posNum = paraJson.optString("pos_num");
////		String opt_num = paraJson.optString("opt_num");
////		double amount = paraJson.optDouble("amount");
////		Integer jzid = paraJson.optInt("jzid");
////		Integer payShiftId = paraJson.optInt("pay_shift_id");
//		//验证是否签到
//		posDao.checkKssy(reportDate, optNum, storeId);
//		List<Object[]> itemList = new ArrayList<Object[]>();
//		Map<String, Map<String, Object>> itemMap = new HashMap<String, Map<String, Object>>();
//		for (int i = 0; i < items.size(); i++) {
//			int payNum = i + 1;
//			Map<String, Object> detail = (Map<String, Object>) items.get(i);
//			itemMap.put(String.valueOf(payNum), detail);
//			double amount = ParamUtil.getDoubleValue(detail, "amount", false, null);
//			if(amount<=0)
//			{
//				throw SystemException.getInstance(PosErrorCode.AMOUNT_NOT_MORE_ZERO_ERROR);
//			}
//			Integer jzid = ParamUtil.getIntegerValue(detail, "jzid", false, null);
//			Integer payShiftId = ParamUtil.getIntegerValue(detail, "pay_shift_id", false, null);
//			String report_date_new = ParamUtil.getStringValue(detail,"report_date", false, null);
//			Date reportDateNew = DateUtil.parseDate(report_date_new);
//			Integer waiterId = ParamUtil.getIntegerValue(detail, "waiter_id", false, null);
//			String waiterName = ParamUtil.getStringValue(detail,"waiter_name", false, null);
//			if(Tools.isNullOrEmpty(waiterName))
//			{
//				waiterName = posDao.getEmpNameById(waiterId.toString(), tenancyId, storeId);
//			}
//			Double receiveAmount = posDao.getReceiveAmountByPayment(tenancyId, storeId, reportDateNew, waiterId, jzid);
//			if (amount > receiveAmount)
//			{
//				throw SystemException.getInstance(PosErrorCode.RECEIVE_AMOUNT_MORE_PAYMENT_AMOUNT_ERROR);
//			}
//			Timestamp currentTime = DateUtil.currentTimestamp();
//			Object[] objs = new Object[]{tenancyId,storeId,reportDate,shiftId,operType,waiterId,waiterName,deviceNum,amount,cashierId,
//					cashierName,posNum,DateUtil.currentTimestamp(),"0",jzid,payShiftId};
//			StringBuilder updateCashierRcvSql = new StringBuilder(" insert into pos_cashier_receive_log (tenancy_id,store_id,report_date,shift_id,oper_type,waiter_id,waiter_name,device_num,amount,cashier_id,cashier_name,pos_num,last_updatetime,upload_tag,payment_id,pay_shift_id) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ");
//			posDao.update(updateCashierRcvSql.toString(), objs);
//			StringBuilder queryIdSql = new StringBuilder(" select id from pos_cashier_receive_log order by id desc limit 1");
//			SqlRowSet rs = posDao.query4SqlRowSet(queryIdSql.toString(), new Object[]{});
//			Integer receive_id = 0;
//			if (rs.next())
//			{
//				receive_id = rs.getInt("id");
//			}
//			Object[] obj = new Object[]{tenancyId,storeId,receive_id,cashierId,deviceNum,waiterId,currentTime,tenancyId,storeId,reportDateNew,payShiftId,jzid};
//			itemList.add(obj);
//			JSONObject paymentJson =posDao.getPaymentWayByID(tenancyId, storeId, jzid);
//
//			JSONObject  printJson = new JSONObject();
//			printJson.put("cashier_name", cashierName);
//			printJson.put("amount", amount);
//			printJson.put("waiter_name", waiterName);
//			printJson.put("operator", cashierName);
//			printJson.put("payment_name", paymentJson.optString("payment_name"));
//
//			printJson.put("print_code", SysDictionary.PRINT_CODE_1303);
//			printJson.put("mode", "1");
//			printJson.put("pos_num", posNum);
//			printJson.put("opt_num",optNum);
//			printJson.put("report_date", report_date);
//			printJson.put("printCount", 2);
//			printJsonList.add(printJson);
//			//记录POS日志
//			posDao.savePosLog(tenancyId, storeId, posNum, cashierId.toString(), cashierName, shiftId, reportDate, Constant.TITLE, "交款/抽大钞", "收款人：" + cashierId + "，收款机台：" + posNum, "交款人：" + cashierId + "，交款机台：" + posNum + "，交款金额：" + String.format("%.2f", amount));
//		}
//		if (itemList.size() > 0) {
//			StringBuilder updateCashierRcvDetailsSql = new StringBuilder(" insert into pos_cashier_receive_log_details (tenancy_id,store_id,receive_id,cashier_id,pos_num,waiter_id,bill_num,last_updatetime,upload_tag) " +
//					" select ?,?,?,?,?,?,a.bill_num,?,0 from (select bill_num from pos_bill_payment " +
//					" where tenancy_id=? and store_id=? and report_date=? and shift_id=? and jzid=? and payment_state='" + SysDictionary.PAYMENT_STATE_PAY_COMPLETE + "' " +
//					"and bill_num not in (select bill_num from pos_cashier_receive_log_details)) a ");
//			posDao.batchUpdate(updateCashierRcvDetailsSql.toString(), itemList);
//		}
////		posDao.update(updateCashierRcvDetailsSql.toString(), new Object[]
////				{ tenancyId,storeId,receive_id,cashierId,deviceNum,waiterId,currentTime,tenancyId,storeId,reportDate,payShiftId,jzid });
////		posDao.customerPrint(tenancyId, storeId, posNum, "1014", "0", printJson, 2);
//
//	}
//
//    @Override
//    public Data cashierLimitVlidate(Data param) throws Exception {
//        String tenantId = param.getTenancy_id();
//        Integer organId = param.getStore_id();
//
//        Map<String, Object> map = ReqDataUtil.getDataMap(param);
//
//        String reportDate = ParamUtil.getStringValue(map, "report_date", true, PosErrorCode.NOT_NULL_REPORT_DATE);// 报表日期
//
//        String cashierNum = ParamUtil.getStringValue(map, "cashier_num", true, PosErrorCode.NOT_NULL_CASHIER_NUM);
//
//        StringBuilder condition = new StringBuilder();
//        condition.append("pbp.tenancy_id = '").append(tenantId).append("' and pbp.store_id = ").append(organId).append(" and pbp.report_date='").append(reportDate).append("' ");
//
//        StringBuilder vlidateSql = new StringBuilder();
//        vlidateSql.append(" select em.is_limit_pay,em.pay_limit,coalesce(coalesce(bl.bill_amount,0)-coalesce(pcrl.receive_amount,0),0) as difference_amount");
//        vlidateSql.append(" from (select bi.tenancy_id,bi.store_id,bi.report_date,bi.shift_id,bi.cashier_num as waiter_num,coalesce(sum(pbp.currency_amount),0) AS bill_amount from pos_bill_payment pbp left join pos_bill bi on pbp.tenancy_id=bi.tenancy_id and pbp.store_id=bi.store_id and pbp.bill_num=bi.bill_num left join hq_devices hd on bi.tenancy_id = hd.tenancy_id and bi.store_id = hd.store_id and bi.pos_num = hd.devices_code where ").append(condition).append(" and bi.cashier_num =  '"+cashierNum+"'").append(" and (hd.show_type = 'YD' or hd.show_type = 'PAD') group by bi.tenancy_id,bi.store_id,bi.report_date,bi.shift_id,bi.cashier_num) as bl");
//        vlidateSql.append(" left join (select pbp.tenancy_id,pbp.store_id,pbp.report_date,pbp.pay_shift_id as shift_id,pbp.waiter_id,sum(pbp.amount) as receive_amount from pos_cashier_receive_log pbp where ").append(condition).append(" and pbp.waiter_id = '"+cashierNum+"'").append(" group by pbp.tenancy_id,pbp.store_id,pbp.report_date,pbp.pay_shift_id,pbp.waiter_id) as pcrl on bl.tenancy_id = pcrl.tenancy_id and bl.store_id = pcrl.store_id and bl.report_date = pcrl.report_date and bl.shift_id = pcrl.shift_id and cast(bl.waiter_num as int) = pcrl.waiter_id");
//        vlidateSql.append(" left join employee em on bl.tenancy_id = em.tenancy_id and bl.store_id = em.store_id and cast(bl.waiter_num as int) = em.id");
//        vlidateSql.append(" left join (select doo.tenancy_id,doo.organ_id,dor.id as shift_id,sd.class_item as shift_name from duty_order as dor left join duty_order_of_ogran as doo on dor.tenancy_id = doo.tenancy_id and dor.id = doo.duty_order_id left join sys_dictionary sd on  dor.tenancy_id = sd.tenancy_id and dor.name = sd.class_item_code and sd.class_identifier_code ='duty') du on bl.tenancy_id = du.tenancy_id and bl.store_id = du.organ_id and bl.shift_id = du.shift_id");
//
//        List<JSONObject> list = posDao.query4Json(tenantId, vlidateSql.toString());
//
//        Data result = param.clone();
//        result.setData(null);
//
//        result.setData(list);
//        result.setCode(Constant.CODE_SUCCESS);
//        result.setMsg(Constant.GET_CASHIER_LIMIT_VLIDATE_SUCCESS);
//
//        return result;
//    }
    @Override
    public Data updateItemServedState(Data param) throws Exception {
        Map<String, Object> map = ReqDataUtil.getDataMap(param);
        String bill_num = ParamUtil.getStringValue(map, "bill_num", false, null);
        List<Map<String, Object>> list = (List<Map<String, Object>>) map.get("item");
        for (Map<String, Object> itemMap : list) {
            Integer rwid = ParamUtil.getIntegerValue(itemMap, "rwid", false, null);
            Integer servedState = ParamUtil.getIntegerValue(itemMap, "served_state", false, null);
            String sql = "update  pos_bill_item set served_state=? where  bill_num=? and rwid=? ";
            posDao.update(sql, new Object[]{servedState, bill_num, rwid});

        }
        Data result = param.clone();
        result.setData(null);
        result.setMsg(Constant.UPDATE_ITEM_SERVED_SUCCESS);
        result.setCode(Constant.CODE_SUCCESS);
        return result;
    }

    @Override
    public void loadPrintSerialNumber(String tenancyId, int storeId) throws Exception {
        // 是否新增物理打印机
        posDao.synzPrinter(tenancyId, storeId);

        // 从打印任务表中查询最大序号，更新到缓存中
        StringBuffer serialSql = new StringBuffer();
        serialSql.append(" select t2.print_address as ip_com, t2.serial_number from ");
        serialSql.append(" (select distinct psn.ip_com from pos_printer_serial_number psn where psn.report_date =(select max(report_date) from pos_printer_serial_number where tenancy_id = ? and store_id = ?) and psn.tenancy_id = ? and psn.store_id = ?) t1 inner join ");
        serialSql.append(" (select pt.print_address, coalesce(max(pt.serial_number), 0) serial_number from pos_print_task pt group by pt.print_address) t2 on t1.ip_com = t2.print_address ");

        List<JSONObject> serialNumbers = posDao.query4Json(tenancyId, serialSql.toString(), new Object[]{tenancyId, storeId, tenancyId, storeId});

        PrinterSerialNumber.setSerials(serialNumbers);
    }

    @Override
    public void updatePrintSerialByMap(String tenancyId, int storeId) throws Exception {
        // 查询报表日期
        String reportDateSql = "select max(report_date) as report_date from pos_printer_serial_number where tenancy_id = ? and store_id = ?";
        SqlRowSet rowSet = posDao.query4SqlRowSet(reportDateSql, new Object[]{tenancyId, storeId});
        Date reportDate = null;
        if (rowSet.next() && rowSet.getDate("report_date") != null) {
            reportDate = rowSet.getDate("report_date");

            List<Object[]> batchArgs = new ArrayList<>();

            // 缓存数据
            Map<String, Integer> serials = PrinterSerialNumber.getSerials();
            Set<Map.Entry<String, Integer>> set = serials.entrySet();

            for (Map.Entry<String, Integer> item : set) {
                batchArgs.add(new Object[]{item.getValue(), item.getKey(), reportDate, storeId, tenancyId});
            }

            String updatePrinterSql = "update pos_printer_serial_number set serial_number = ? where ip_com = ? and report_date = ? and store_id = ? and tenancy_id = ? ";
            posDao.batchUpdate(updatePrinterSql, batchArgs);
        }
    }

    @Override
    public void openCashBox(Data param, Data result) throws Exception {
        String tenancyId = param.getTenancy_id();
        Integer storeId = param.getStore_id();

        List<?> paramList = param.getData();
        if (paramList == null || paramList.isEmpty()) {
            throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
        }
        JSONObject para = JSONObject.fromObject(paramList.get(0));

        int shiftId = para.optInt("shift_id");
        String posNum = para.optString("pos_num");
        String optNum = para.optString("opt_num");
        String chanel = para.optString("chanel");

        String devicesSql = "select d.id, d.is_site from hq_devices d where d.devices_code = ? and d.valid_state = '1' and d.is_site = '1' and d.store_id = ? and d.tenancy_id = ?";
        List<JSONObject> devicesList = posDao.query4Json(tenancyId, devicesSql, new Object[]{posNum, storeId, tenancyId});

        if (devicesList == null || devicesList.isEmpty()) {
            throw SystemException.getInstance(PosErrorCode.DEVICES_NO_OPEN_PRINT_SERVICE);
        }

        JSONObject devicesJson = devicesList.get(0);
        int devicesId = devicesJson.optInt("id");

        String printSql = "select p.name, p.ip_com, p.site_id, p.is_area from hq_printer_new p where p.site_id = ? and p.is_open_cashbox = 1 and p.valid_state = '1' and p.store_id = ? and p.tenancy_id = ?";
        List<JSONObject> printList = posDao.query4Json(tenancyId, printSql, new Object[]{devicesId, storeId, tenancyId});

        if (printList == null || printList.isEmpty()) {
            throw SystemException.getInstance(PosErrorCode.PRINTER_NO_SET_CASHBOX);
        }

        StringBuilder sql = new StringBuilder(" insert into pos_print_task(tenancy_id,store_id,site_id,area_id,state,print_name,print_address,print_parameter, ");
        sql.append(" templete_id,params,send_time,print_time,order_no,print_times,bak_print_name,bak_print_address,bak_print_parameter,print_num,serial_number) ");
        sql.append(" values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ");

        JSONObject params = new JSONObject();
        params.put("is_open_cashbox", 1);

        for (JSONObject printer : printList) {
            posDao.update(sql.toString(), new Object[]
                    {tenancyId, storeId, printer.optInt("site_id"), 0, 0, printer.optString("name"), printer.optString("ip_com"),
                            "", 0, params.toString(), DateUtil.getNowTimestamp(), null, 0, 0, printer.optString("bak_name"), printer.optString("bak_ip_com"), "", 1, 0});
        }

        try {
            // 发出打印通知
            Data cjData = new Data();
            cjData.setType(Type.PRINT);
            cjData.setOper(Oper.print);
            Comet4jUtil.sendMessage2All(Comet4jUtil.POS_PRINTER, JSONObject.fromObject(cjData).toString());
        } catch (Exception e) {
            logger.error("发出打印通知异常：", e);
        }

        result.setCode(Constant.CODE_SUCCESS);
        result.setMsg(Constant.OPEN_CASHBOX_SUCCESS);
    }

    @Override
    public void checkUnclosedBill(Data param, Data result) throws Exception {
        String tenancyId = param.getTenancy_id();
        Integer storeId = param.getStore_id();

        Map<String, Object> map = ReqDataUtil.getDataMap(param);

        Date reportDate = ParamUtil.getDateValue(map, "report_date", false, null);
        String openPosNum = ParamUtil.getStringValue(map, "open_pos_num", false, null);

        String selectBill = "select bill_num,open_pos_num,bill_property,payment_state FROM pos_bill where bill_property<>'CLOSED' and report_date='" + reportDate + "' and store_id='" + storeId + "' ";
        if (StringUtils.isNotEmpty(openPosNum)) {
            selectBill += " and open_pos_num='" + openPosNum + "'";
        }
        String selectOrder = "select order_code,order_state from cc_order_list where order_state not in ('08','10','12','21','22','23','24') and store_id = '" + storeId + "' and tenancy_id = '" + tenancyId + "'  and report_date='" + reportDate + "'";

        JSONObject res = new JSONObject();
        res.put("unclosed_bill", posDao.query4Json(tenancyId, selectBill));
        List<JSONObject> unClosedOrder = posDao.query4Json(tenancyId, selectOrder);

        if (StringUtils.isEmpty(openPosNum)) {//打烊
            String noShiftOrder = "SELECT order_num order_code, '0' order_state  FROM pos_bill where shift_id='0'  and store_id='" + storeId + "' and tenancy_id='" + tenancyId + "' and report_date='" + reportDate + "'";
            List<JSONObject> noShiftOrders = posDao.query4Json(tenancyId, noShiftOrder);
            unClosedOrder.addAll(noShiftOrders);
        }

        res.put("unclosed_order", unClosedOrder);

        result.setData(Arrays.asList(res));
        result.setCode(Constant.CODE_SUCCESS);
        result.setMsg(Constant.CHECK_UNCLOSED_BILL_SUCCESS);
    }


    @Override
    public boolean isNewInstall(String tenancy_id, int store_id, List<?> data) {
        String queryDayEndSql = "select count(1) from pos_opt_state where content='DAYEND' and store_id='" + store_id + "' and tenancy_id='" + tenancy_id + "'";
        try {
            List<JSONObject> res = posDao.query4Json(tenancy_id, queryDayEndSql);
            if (null != res && !res.isEmpty()) {
                int count = res.get(0).optInt("count", 0);
                if (count > 0) {
                    return false;
                }
            }
        } catch (Exception e) {
            logger.error(e);
        }
        return true;
    }


    @Override
    public void progressMonitor(Data param, Data result) {


        Map<String, Object> map = ReqDataUtil.getDataMap(param);

        String name = ParamUtil.getStringValue(map, "name", false, null);

        Progress p = ProgressMonitor.getPhantomProgress(name);

        result.setData(Arrays.asList(JSONObject.fromObject(p.toString())));
        result.setCode(Constant.CODE_SUCCESS);
    }

    @Override
    public void getItemStampList(Data param, Data result) {

        String tenancyId = param.getTenancy_id();
        Integer storeId = param.getStore_id();

        Map<String, Object> map = ReqDataUtil.getDataMap(param);

//        Date reportDate = ParamUtil.getDateValue(map, "report_date", false, null);


        StringBuilder sql = new StringBuilder("SELECT P .* FROM pos_bill_item_stamp P RIGHT JOIN ( SELECT MAX (pbs. ID) ID, pbs.item_rwid FROM pos_bill_item_stamp pbs LEFT JOIN ( SELECT item_rwid, SUM (stamp_type) stamp_status FROM pos_bill_item_stamp GROUP BY item_rwid) pss ON pbs.item_rwid = pss.item_rwid WHERE pbs.stamp_type = 1 AND pss.stamp_status = 1 GROUP BY pbs.item_rwid) s ON P . ID = s. ID");

        try {
            Date reportDate = posDao.getReportDate(tenancyId, storeId);

            sql.append(" where p.report_date='" + reportDate + "'");
            sql.append(" and p.tenancy_id='" + tenancyId + "'");
            sql.append(" and p.store_id=" + storeId);

            Pagination pagination = param.getPagination();
            if (pagination != null && pagination.getPagesize() > 0) {

                buildPaginationSql(sql, pagination, "stamp_time");
            }

            result.setPagination(pagination);
            result.setData(posDao.query4Json(tenancyId, sql.toString()));
            result.setCode(Constant.CODE_SUCCESS);

        } catch (Exception e) {
            result.setCode(9000);
            result.setMsg("查询失败");
            logger.error(e);
        }
    }

    public static int COUNTDOWN_TIME = 0;

    private int getCountdownTime(String tenancyId, Integer storeId) {
        if (0 == COUNTDOWN_TIME) {
            try {
                COUNTDOWN_TIME = Integer.valueOf(OrderUtil.getSysPara(tenancyId, storeId, "kvs_serving_time"));
            } catch (Exception e) {
                logger.error("划菜--菜品上齐菜时间获取失败", e);
                COUNTDOWN_TIME = 30;
            }
        }
        return COUNTDOWN_TIME;
    }

    @Override
    public void getItemStampByBusinessArea(Data param, Data result) {
        String tenancyId = param.getTenancy_id();
        Integer storeId = param.getStore_id();

        Map<String, Object> map = ReqDataUtil.getDataMap(param);

        Date reportDate = ParamUtil.getDateValue(map, "report_date", false, null);
        String businessAreaId = ParamUtil.getStringValue(map, "business_area_id", false, null);
        String tablePropertyId = ParamUtil.getStringValue(map, "table_property_id", false, null);
        StringBuilder sql = new StringBuilder();
        sql.append("select * from (");
        sql.append("SELECT C .bill_num, C . TABLE_NAME, C .table_code, e.guest, MIN (C .countdown_time) countdown_time, SUM (C .cooking) cooking_count, d.sum_count, C .business_area_id FROM (")
                .append(buildItemStampDetailSql(getCountdownTime(tenancyId, storeId), null))
                .append(") C LEFT JOIN ( SELECT bill_num, COUNT (1) sum_count FROM pos_bill_item  b INNER JOIN ( SELECT hpi.item_id, hpn.\"id\" print_id, hpn.\"name\" print_name FROM hq_printer_new hpn LEFT JOIN hq_printer_item hpi ON hpi.print_id = hpn. ID LEFT JOIN hq_printer_model hpm ON hpi.print_format_id = hpm. ID WHERE hpm.format_type IN ('1107', '1113', '1106') AND hpm.is_enables = '1' AND hpn.valid_state = '1') P ON b.item_id = P .item_id WHERE b.item_property IN ('SINGLE', 'MEALLIST') AND ( b.item_remark IS NULL OR b.item_remark IN ('TC01', 'FS02')) GROUP BY b.bill_num) d ON C .bill_num = d.bill_num LEFT JOIN pos_bill e ON d.bill_num = e.bill_num");
        if (StringUtils.isNotEmpty(businessAreaId)) {
            sql.append(" where business_area_id='" + businessAreaId + "'");
        }
        if (StringUtils.isNotEmpty(tablePropertyId)) {
            sql.append(" where table_property_id='" + tablePropertyId + "'");
        }
        sql.append(" GROUP BY C .bill_num, d.sum_count, C . TABLE_NAME, C .table_code, e.guest, C .business_area_id , C.table_property_id");
        sql.append(") rs where rs.cooking_count>0");
        try {
            Pagination pagination = param.getPagination();
            buildPaginationSql(sql, param.getPagination(), "countdown_time");

            result.setPagination(pagination);
            result.setData(posDao.query4Json(tenancyId, sql.toString()));
            result.setCode(Constant.CODE_SUCCESS);

        } catch (Exception e) {
            result.setCode(9000);
            result.setMsg("查询失败");
            logger.error(e);
        }
    }

    @Override
    public void getItemStampDetailByTableCode(Data param, Data result) {
        String tenancyId = param.getTenancy_id();
        Integer storeId = param.getStore_id();

        Map<String, Object> map = ReqDataUtil.getDataMap(param);

        Date reportDate = ParamUtil.getDateValue(map, "report_date", false, null);
        String tableCode = ParamUtil.getStringValue(map, "table_code", false, null);
        String itemId = ParamUtil.getStringValue(map, "item_id", false, null);
        String itemUnitId = ParamUtil.getStringValue(map, "item_unit_id", false, null);
        String cooking = ParamUtil.getStringValue(map, "cooking", false, null);

        Map<String, String> conditions = new HashMap<>();
        if (StringUtils.isNotEmpty(tableCode)) {
            conditions.put("table_code", tableCode);
        }
        if (StringUtils.isNotEmpty(itemId)) {
            conditions.put("item_id", itemId);
        }
        if (StringUtils.isNotEmpty(itemUnitId)) {
            conditions.put("item_unit_id", itemUnitId);
        }
        conditions.put("cooking", StringUtils.isEmpty(cooking) ? "1" : cooking);
        try {
            result.setData(posDao.query4Json(tenancyId, buildItemStampDetailSql(getCountdownTime(tenancyId, storeId), conditions)));
            result.setCode(Constant.CODE_SUCCESS);

        } catch (Exception e) {
            result.setCode(9000);
            result.setMsg("查询失败");
            logger.error(e);
        }

    }

    @Override
    public void getItemStampByPrintId(Data param, Data result) {
        String tenancyId = param.getTenancy_id();
        Integer storeId = param.getStore_id();

        Map<String, Object> map = ReqDataUtil.getDataMap(param);

        Date reportDate = ParamUtil.getDateValue(map, "report_date", false, null);
        String printId = ParamUtil.getStringValue(map, "print_id", false, null);
        StringBuilder sql = new StringBuilder();
        sql.append("select * from (");
        sql.append("SELECT c.item_id,C.item_code, c.item_name, c.item_unit_name,c.item_unit_id, c.print_id,  MIN (C .countdown_time) countdown_time, SUM (C .cooking) cooking_count FROM (")
                .append(buildItemStampDetailSql(getCountdownTime(tenancyId, storeId), null))
                .append(") C ");
        if (StringUtils.isNotEmpty(printId)) {
            sql.append(" where C.print_id='" + printId + "'");
        }
        sql.append(" GROUP BY c.item_id,C.item_code ,c.item_name,c.item_unit_id, c.item_unit_name, c.print_id ");
        sql.append(") rs where rs.cooking_count>0");
        try {
            Pagination pagination = param.getPagination();
            buildPaginationSql(sql, pagination, "countdown_time");

            result.setPagination(pagination);
            result.setData(posDao.query4Json(tenancyId, sql.toString()));
            result.setCode(Constant.CODE_SUCCESS);

        } catch (Exception e) {
            result.setCode(9000);
            result.setMsg("查询失败");
            logger.error(e);
        }
    }

    private void buildPaginationSql(StringBuilder sql, Pagination pagination, String orderByCondition) throws Exception {

        if (pagination != null && pagination.getPagesize() > 0) {

            int totalCount = posDao.queryForInt("select count(1) as count from (" + sql.toString() + ") t", new Object[]{});
            pagination.setTotalcount(totalCount);

            String orderBy = StringUtils.isEmpty(pagination.getOrderby()) ? orderByCondition : pagination.getOrderby();
            String ascDesc = pagination.isAsc() ? "asc" : "desc";
            int pageNo = pagination.getPageno() > 0 ? pagination.getPageno() : 1;
            int limit = pagination.getPagesize() > 0 ? pagination.getPagesize() : Integer.MAX_VALUE;

            sql.append(" order by " + orderBy + " " + ascDesc + " limit " + limit + " offset " + (pageNo - 1) * pagination.getPagesize());
        }
    }

    @Override
    public void getItemStampDetailByItem(Data param, Data result) {
        getItemStampDetailByTableCode(param, result);
    }

    @Override
    public void retreatItemStamp(Data param, JSONObject json) {
        JSONObject para = JSONObject.fromObject(param.getData().get(0));
        String posNum = para.optString("pos_num");
        String optNum = para.optString("opt_num");
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("stamp_source", "retreat");
        jsonObject.put("stamp_opt", optNum);
        jsonObject.put("stamp_type", "1");
        jsonObject.put("stamp_kvs", posNum);
        Data d = Data.get();
        d.setTenancy_id(param.getTenancy_id());
        d.setStore_id(param.getStore_id());
        for (String rwid : json.optString("rwids").split(",")) {
            jsonObject.put("item_rwid", rwid);
            d.setData(Arrays.asList(jsonObject));
            itemStamp(d, d);
        }
    }

    @Override
    public void itemStamp(Data param, Data result) {
        String tenancyId = param.getTenancy_id();
        Integer storeId = param.getStore_id();

        Map<String, Object> map = ReqDataUtil.getDataMap(param);

        try {
            Date reportDate = ParamUtil.getDateValue(map, "report_date", false, PosErrorCode.NOT_NULL_REPORT_DATE);
            final String itemRwid = ParamUtil.getStringValue(map, "item_rwid", true, PosErrorCode.PARAM_ERROR);
            String stampSource = ParamUtil.getStringValue(map, "stamp_source", true, PosErrorCode.PARAM_ERROR);
            String stampOpt = ParamUtil.getStringValue(map, "stamp_opt", true, PosErrorCode.PARAM_ERROR);
            String stampType = ParamUtil.getStringValue(map, "stamp_type", true, PosErrorCode.PARAM_ERROR);
            String stampKvs = ParamUtil.getStringValue(map, "stamp_kvs", true, PosErrorCode.PARAM_ERROR);
            String billNum = ParamUtil.getStringValue(map, "bill_num", false, PosErrorCode.PARAM_ERROR);
            String tableCode = ParamUtil.getStringValue(map, "table_code", false, PosErrorCode.PARAM_ERROR);
            String tableName = ParamUtil.getStringValue(map, "table_name", false, PosErrorCode.PARAM_ERROR);
            String itemId = ParamUtil.getStringValue(map, "item_id", false, PosErrorCode.PARAM_ERROR);
            String itemName = ParamUtil.getStringValue(map, "item_name", false, PosErrorCode.PARAM_ERROR);
            String unitName = ParamUtil.getStringValue(map, "unit_name", false, PosErrorCode.PARAM_ERROR);
            String itemCount = ParamUtil.getStringValue(map, "item_count", false, PosErrorCode.PARAM_ERROR);
            String optName = ParamUtil.getStringValue(map, "opt_name", false, PosErrorCode.PARAM_ERROR);
            String itemTime = ParamUtil.getStringValue(map, "item_time", false, PosErrorCode.PARAM_ERROR);

            List<JSONObject> rwList = posDao.query4Json(tenancyId, buildItemStampDetailSql(getCountdownTime(tenancyId, storeId), new HashMap<String, String>(1) {{
                put("rwid", itemRwid);
            }}));
            if (!StringUtils.equals(stampSource, "retreat") && (null == rwList || rwList.isEmpty())) {
                throw new SystemException(PosErrorCode.INVALID_ITEM_RWID);
            } else {
                JSONObject data = rwList.get(0);

                if (!StringUtils.equals(stampSource, "retreat") && "1".equals(data.optString("waitcall_tag"))) {
                    throw new SystemException(PosErrorCode.ITEM_WAITING_CALL);
                }

                if (StringUtils.isNotEmpty(data.getString("stamp_time")) && !"null".equals(data.getString("stamp_time"))) {
                    throw new SystemException(PosErrorCode.ITEM_ALEADY_STAMP);
                }

                data.put("stamp_source", stampSource);
                data.put("stamp_opt", stampOpt);
                data.put("stamp_type", stampType);
                data.put("stamp_kvs", stampKvs);
                data.put("stamp_time", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
                data.put("item_rwid", itemRwid);
                posDao.insertIgnorCase(tenancyId, "pos_bill_item_stamp", data);
                result.setData(Arrays.asList(data));
            }

        } catch (SystemException se) {
            result.setCode(se.getErrorCode().getNumber());
            result.setMsg(se.getErrorMsg());
        } catch (Exception e) {
            result.setCode(9000);
            result.setMsg("操作失败");
            logger.error(e);
        }


    }

    @Override
    public void getHqPrinter(Data param, Data result) {
        String tenancyId = param.getTenancy_id();
        Integer storeId = param.getStore_id();
        try {
            JSONObject data=posDao.getHqPrinter(tenancyId,storeId).get(0);
            result.setData(data.optJSONArray("hq_printer"));
            result.setCode(Constant.CODE_SUCCESS);

        } catch (Exception e) {
            result.setCode(9000);
            result.setMsg("查询失败:"+e.getMessage());
            logger.error(e);
        }

    }

    @Override
    public void queryPrinterBg(Data param, Data result) {
        String tenancyId = param.getTenancy_id();
        Integer storeId = param.getStore_id();

        pullHqPrinterBgData(tenancyId,storeId);

        try {

            Integer printerId=ParamUtil.getDataValue(param,"printer_id");
            String printerAddress=ParamUtil.getDataValue(param,"printer_address");
            String printerName=ParamUtil.getDataValue(param,"printer_name");

            StringBuilder querySql=new StringBuilder("SELECT pb. ID, COALESCE (pb.bgcode, '') bgcode, pb.bgname, pb.bgorder, pb.printer_id, pb.printer_address, pb.printer_name, pbi.item_id, pbi.item_name, pbi.item_order FROM pos_print_bgclass pb LEFT JOIN pos_print_bgclass_item pbi ON pb. ID = pbi.bgclassid where pb.tenancy_id='"+tenancyId+"' and pb.store_id="+storeId+"");

            if(Tools.hv(printerId)){
               querySql.append(" and pb.printer_id='"+printerId+"' ");
            }
            if(Tools.hv(printerAddress)){
                querySql.append(" and pb.printer_address='"+printerAddress+"' ");
            }
            if(Tools.hv(printerName)){
                querySql.append(" and pb.printer_name='"+printerName+"' ");
            }
            querySql.append(" ORDER BY pb.bgorder, pbi.item_order ");
            SqlRowSet rowSet=posDao.getJdbcTemplate(tenancyId).queryForRowSet(querySql.toString());

            Map<String,List<PrinterBg.Item>> printerBgItems=new HashMap<>();
            Map<String,PrinterBg> printerBgs=new TreeMap<>();


            while (rowSet.next()){
                String printerBgId=rowSet.getString("id");
                PrinterBg printerBg=printerBgs.get(printerBgId);
                if(null==printerBg){

                    printerBg=new PrinterBg();
                    printerBg.setId(printerBgId);
                    printerBg.setBgcode(rowSet.getString("bgcode"));
                    printerBg.setBgname(rowSet.getString("bgname"));
                    printerBg.setBgorder(rowSet.getInt("bgorder"));
                    printerBg.setPrinter_id(rowSet.getInt("printer_id"));
                    printerBg.setPrinter_address(rowSet.getString("printer_address"));
                    printerBg.setPrinter_name(rowSet.getString("printer_name"));

                    printerBgs.put(printerBgId,printerBg);
                    printerBgItems.put(printerBgId,new ArrayList<PrinterBg.Item>());
                }
                   Integer itemId=rowSet.getInt("item_id");

                if(!rowSet.wasNull()){
                    List<PrinterBg.Item> items=printerBgItems.get(printerBgId);
                    PrinterBg.Item item=new PrinterBg.Item();
                    item.setItem_id(itemId);
                    item.setItem_name(rowSet.getString("item_name"));
                    item.setItem_order(rowSet.getInt("item_order"));
                    items.add(item);
                }

            }

            List<PrinterBg> data = new ArrayList<>();
            for (PrinterBg bg : printerBgs.values()) {
                bg.setItem(printerBgItems.get(bg.getId()));
                data.add(bg);
            }

            result.setData(data);
            result.setCode(Constant.CODE_SUCCESS);

        } catch (Exception e) {
            result.setCode(9000);
            result.setMsg("查询失败:"+e.getMessage());
            logger.error(e);
        }

    }

    @Override
    public void savePrinterBg(Data param, Data result) {
        String tenancyId = param.getTenancy_id();
        Integer storeId = param.getStore_id();



        try {

            List data=param.getData();
            Integer printerId=0;
            String printAddress=null;
            String printName=null;
            StringBuilder sql=new StringBuilder();
            for(Object o:data){
                JSONObject bg=JSONObject.fromObject(o);
                //delete
                printAddress=bg.optString("printer_address");
                printerId=bg.optInt("printer_id");
                if(printName!=bg.optString("printer_name")){

                    printName=bg.optString("printer_name");

                    sql.append("delete from pos_print_bgclass where printer_name='"+printName+"' and tenancy_id='"+tenancyId+"' and store_id="+storeId);
                    sql.append(";delete from pos_print_bgclass_item where printer_name='"+printName+"' and tenancy_id='"+tenancyId+"' and store_id="+storeId+";");
                }
                //insert
                JSONArray shelfs=bg.optJSONArray("printer_bg");
                if(null!=shelfs&&!shelfs.isEmpty()){
                    for(Object _shelf:shelfs){
                        String id=UUID.randomUUID().toString().replace("-","");

                       JSONObject shelf=JSONObject.fromObject(_shelf) ;

                        sql.append("INSERT INTO \"public\".\"pos_print_bgclass\" (\"id\", \"bgcode\", \"bgname\", \"bgorder\", \"tenancy_id\", \"store_id\", \"printer_id\", \"printer_address\",printer_name) VALUES ");
                        sql.append("('"+id+"',");
                        sql.append(" '"+shelf.optString("bgcode")+"',");
                        sql.append(" '"+shelf.optString("bgname")+"',");
                        sql.append(" '"+shelf.optString("bgorder")+"',");
                        sql.append(" '"+tenancyId+"',");
                        sql.append(" '"+storeId+"',");
                        sql.append(" '"+printerId+"',");
                        sql.append(" '"+printAddress+"',");
                        sql.append(" '"+printName+"');");

                        JSONArray bgItem=shelf.optJSONArray("item");
                        List<JSONObject> items=new ArrayList<>();
                        for(Object i:bgItem){
                            JSONObject item=JSONObject.fromObject(i) ;
                            item.put("bgclassid",id);
                            item.put("store_id",storeId);
                            item.put("printer_id",printerId);
                            items.add(item);

                            sql.append("INSERT INTO \"public\".\"pos_print_bgclass_item\" (\"id\", \"bgclassid\", \"item_id\", \"tenancy_id\", \"store_id\", \"item_order\", \"item_name\", \"printer_id\",printer_name) VALUES");
                            sql.append("('"+UUID.randomUUID().toString().replace("-","")+"',");
                            sql.append("'"+id+"',");
                            sql.append("'"+item.optString("item_id")+"',");
                            sql.append("'"+tenancyId+"',");
                            sql.append("'"+storeId+"',");
                            sql.append("'"+item.optString("item_order")+"',");
                            sql.append("'"+item.optString("item_name")+"',");
                            sql.append("'"+item.optString("printer_id")+"',");
                            sql.append("'"+printName+"');");
                        }
                    }
                }

            }
            posDao.execute(tenancyId,sql.toString());

            uploadPrinterBgData(tenancyId, storeId, sql.toString());

            result.setMsg("保存成功");
            result.setCode(Constant.CODE_SUCCESS);

        } catch (Exception e) {
            result.setCode(9000);
            result.setMsg("保存失败:"+e.getMessage());
            logger.error(e);
        }

    }

    private void uploadPrinterBgData(final String tenancyId, final Integer storeId,final String sql){

        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    JSONObject hqRequest=new JSONObject();
                    hqRequest.put("execSql",sql.toString());

                    Data uploadData = Data.get();
                    uploadData.setTenancy_id(tenancyId);
                    uploadData.setStore_id(storeId);
                    uploadData.setType(Type.HQ_PRINGTER_BG);
                    uploadData.setOper(Oper.upload);
                    uploadData.setData(Arrays.asList(hqRequest));

                    HttpUtil.post(PosPropertyUtil.getMsg("saas.url") +"/hqRest/post" , JSONObject.fromObject(uploadData), 3,10000,60000,true);
                }catch (Exception e){
                    logger.error("冰柜信息上传失败",e);
                }
            }
        }).start();

    }

    private void pullHqPrinterBgData(String tenancyId,Integer storeId){

        try {
           SqlRowSet rs = posDao.query(tenancyId,"SELECT count(1) FROM \"public\".\"pos_print_bgclass\"");
           int count=0;
           while (rs.next()){
               count=rs.getInt("count");
           }

            rs = posDao.query(tenancyId,"SELECT count(1) FROM \"public\".\"pos_print_bgclass_item\"");
           while(rs.next()){
              count+=rs.getInt("count");
           }

           if(0==count){

               Data uploadData = Data.get();
               uploadData.setTenancy_id(tenancyId);
               uploadData.setStore_id(storeId);
               uploadData.setType(Type.HQ_PRINGTER_BG);
               uploadData.setOper(Oper.find);

               JSONObject result= HttpUtil.post(PosPropertyUtil.getMsg("saas.url") +"/hqRest/post" , JSONObject.fromObject(uploadData), 1,10000,60000,true);
               Data data=JsonUtil.JsonToData(result);
               List<?> list=data.getData();


               if(null!=list&&!list.isEmpty()){
                   StringBuilder sql=new StringBuilder();

                   JSONObject res=JSONObject.fromObject(list.get(0));
                   JSONArray bgclass=res.optJSONArray("pos_print_bgclass");
                   JSONArray bgclassItem=res.optJSONArray("pos_print_bgclass_item");

                   for(Object c:bgclass){
                       JSONObject shelf=JSONObject.fromObject(c) ;

                       sql.append("INSERT INTO \"public\".\"pos_print_bgclass\" (\"id\", \"bgcode\", \"bgname\", \"bgorder\", \"tenancy_id\", \"store_id\", \"printer_id\", \"printer_address\",printer_name) VALUES ");
                       sql.append("('"+shelf.optString("id")+"',");
                       sql.append(" '"+shelf.optString("bgcode")+"',");
                       sql.append(" '"+shelf.optString("bgname")+"',");
                       sql.append(" '"+shelf.optString("bgorder")+"',");
                       sql.append(" '"+tenancyId+"',");
                       sql.append(" '"+storeId+"',");
                       sql.append(" '"+shelf.optString("printer_id")+"',");
                       sql.append(" '"+shelf.optString("printer_address")+"',");
                       sql.append(" '"+shelf.optString("printer_name")+"');");
                   }

                   for(Object c:bgclassItem){
                       JSONObject item=JSONObject.fromObject(c) ;

                       sql.append("INSERT INTO \"public\".\"pos_print_bgclass_item\" (\"id\", \"bgclassid\", \"item_id\", \"tenancy_id\", \"store_id\", \"item_order\", \"item_name\", \"printer_id\",printer_name) VALUES");
                       sql.append("('"+item.optString("id")+"',");
                       sql.append("'"+item.optString("bgclassid")+"',");
                       sql.append("'"+item.optString("item_id")+"',");
                       sql.append("'"+tenancyId+"',");
                       sql.append("'"+storeId+"',");
                       sql.append("'"+item.optString("item_order")+"',");
                       sql.append("'"+item.optString("item_name")+"',");
                       sql.append("'"+item.optString("printer_id")+"',");
                       sql.append("'"+item.optString("printer_name")+"');");
                   }

                  posDao.execute(tenancyId,sql.toString());

               }
           }


        } catch (Exception e) {
            logger.error("冰柜信息拉取失败",e);
        }


    }

    @Override
    public void calcHotpotNum(String tenancyId, int storeId,String billNum) throws Exception {
        calcHotpotNum (tenancyId,storeId,billNum,false);
    }

    @Override
    public void calcHotpotNum(String tenancyId, int storeId,String billNum,boolean negative) throws Exception {
        int bigHotpotTableNum=0,smallHotpotTableNum=0,hotPotGuestNum=0;

        SqlRowSet row=posDao.query(tenancyId,"SELECT sum(item_count), c.hotpot_property FROM pos_bill_item pbi LEFT JOIN ( SELECT DISTINCT himd.item_id, hic.chanel, hic.hotpot_property, hic.is_must_dish FROM hq_item_menu_class himc LEFT JOIN hq_item_menu_details AS himd ON himc.details_id = himd. ID LEFT JOIN hq_item_menu_organ himo ON himd.item_menu_id = himo.item_menu_id LEFT JOIN hq_item_class hic ON himc. CLASS = hic. ID LEFT JOIN hq_item_menu_classorder himco ON himco.menu_id = himo.item_menu_id AND himco.class_id = hic. ID WHERE himc.chanel = 'MD01' AND hic.valid_state = '1' AND himd.valid_state = '1' AND himo.store_id = "+storeId+") C ON pbi.item_id = C .item_id where pbi.bill_num='"+billNum+"' GROUP BY c.hotpot_property");
        while (row.next()){
            if("bigpot".equals(row.getString("hotpot_property"))){
                bigHotpotTableNum=(int)row.getDouble("sum");
            }
            if("smallpot".equals(row.getString("hotpot_property"))){
                if(negative){
                    smallHotpotTableNum = row.getDouble("sum") < 0 ? -1 : 0;
                }else {
                    smallHotpotTableNum = row.getDouble("sum") > 0 ? 1 : 0;
                }
            }
            if("condiment".equals(row.getString("hotpot_property"))){
                hotPotGuestNum=(int)row.getDouble("sum");
            }

        }

        //修改账单锅底桌数和小料人数
        posDao.execute(tenancyId,"update pos_bill set hotpot_table_num="+(bigHotpotTableNum+smallHotpotTableNum)+", hotpot_guest_num="+hotPotGuestNum+" where bill_num='"+billNum+"'");
    }

    @Override
    public void baseDataPull(Data param,Data result){
        SynchronizeBaseDataService synchronizeBaseDataService = new SynchronizeBaseDataServiceImp();
        if (!synchronizeBaseDataService.syncData(SynchronizeBaseDataService.Type.organ)) {
            result.setMsg("请求下发数据失败");
            result.setCode(Constant.CODE_AUTH_FAILURE);
            result.setSuccess(false);
        }else {
            result.setMsg("请求下发数据成功");
            result.setCode(Constant.CODE_SUCCESS);
            result.setSuccess(true);
        }
    }
    @Override
    public void getFreeSpace(Data result){
        try {
            File file=new File(this.getClass().getClassLoader().getResource("").toURI());
            String driver=file.getAbsolutePath().substring(0,1);
            long freeSpace=file.getFreeSpace()/(1024*1024);

            JSONObject res=new JSONObject();
            res.put("driver",driver);
            res.put("free_space",freeSpace);

            result.setData(Collections.singletonList(res));
            result.setCode(0);
            result.setSuccess(true);

        } catch (Exception e) {
            result.setCode(500);
            result.setSuccess(false);
            result.setMsg(e.getMessage());
        }
    }

    private String buildItemStampDetailSql(int time, Map<String, String> conditions) {
        StringBuilder sql = new StringBuilder("SELECT * FROM ( SELECT ( CASE WHEN b.stamp_time IS NOT NULL THEN '100 years' ELSE ( b.item_time + INTERVAL '" + time + " minute' - now()) END) countdown_time, ( CASE WHEN b.stamp_time IS NULL THEN 1 ELSE 0 END) cooking, b.*, e. NAME stamp_opt_name, P .print_id, P .print_name FROM ( SELECT A .*, ti.\"table_name\", ti.business_area_id,ti.table_property_id FROM ( SELECT T .*, h.phonetic_code, h.item_code, five_code FROM ( SELECT pbi.rwid, pbi.waitcall_tag, pbi.bill_num, pbi.item_id, replace(pbi.item_name,'　', '') item_name, pbi.item_count, pbi.item_unit_name,pbi.item_property, pbi.item_unit_id, pbi.item_taste,string_agg (pzi.zfkw_name, ' ') zfkw_name, pbi.item_remark, pbi.item_time, pbi.table_code, pbi.report_date, pbi.store_id,pbis.stamp_time, pbis.stamp_source, pbis.stamp_opt, pbis.stamp_kvs FROM pos_bill_item pbi LEFT JOIN pos_zfkw_item pzi ON pbi.rwid=pzi.rwid LEFT JOIN ( SELECT P .* FROM pos_bill_item_stamp P RIGHT JOIN ( SELECT MAX (pbs. ID) ID, pbs.item_rwid FROM pos_bill_item_stamp pbs LEFT JOIN ( SELECT item_rwid, SUM (stamp_type) stamp_status FROM pos_bill_item_stamp GROUP BY item_rwid) pss ON pbs.item_rwid = pss.item_rwid WHERE pbs.stamp_type = 1 AND pss.stamp_status = 1 GROUP BY pbs.item_rwid) s ON P . ID = s. ID) pbis ON pbi.rwid = pbis.item_rwid WHERE EXISTS ( SELECT 1 FROM pos_bill pb WHERE pb.bill_property = 'OPEN' AND pb.bill_num = pbi.bill_num) AND ( pbi.item_remark IS NULL OR pbi.item_remark IN( 'TC01','FS02')) AND pbi.item_property IN ('SINGLE', 'MEALLIST') GROUP BY pbi.rwid, pbi.waitcall_tag, pbi.bill_num, pbi.item_id, pbi.item_name, pbi.item_count, pbi.item_unit_name,pbi.item_property,pbi.item_unit_id, pbi.item_taste, pbi.item_remark, pbi.item_time, pbi.table_code, pbi.report_date, pbi.store_id, pbis.stamp_time, pbis.stamp_source, pbis.stamp_opt, pbis.stamp_kvs ) T LEFT JOIN hq_item_info h ON T .item_id = h. ID) A LEFT JOIN tables_info ti ON A .table_code = ti.table_code) b LEFT JOIN employee e ON b.stamp_opt = e. ID INNER JOIN (SELECT hpi.item_id, hpn.\"id\" print_id, hpn.\"name\" print_name FROM hq_printer_new hpn LEFT JOIN hq_printer_item hpi ON hpi.print_id = hpn. ID LEFT JOIN hq_printer_model hpm ON  hpi.print_format_id = hpm.id WHERE hpm.format_type IN ('1107', '1113', '1106') AND hpm.is_enables = '1' AND hpn.valid_state = '1') P ON b.item_id = P .item_id) r ");

        boolean isProduced = false;
        if (null != conditions) {
            Iterator<String> keys = conditions.keySet().iterator();
            StringBuilder conditionSql = new StringBuilder();
            conditionSql.append(" where 1=1 ");
            while (keys.hasNext()) {
                String key = keys.next();
                if (StringUtils.isNotEmpty(conditions.get(key))) {
                    conditionSql.append(" and ");
                    conditionSql.append(key);

                    if ("cooking".equals(key) && "0".equals(conditions.get(key))) {
                        isProduced = true;
                    }
                    if ("phonetic_code".equals(key)) {
                        conditionSql.append("like '" + conditions.get(key) + "%'");
                    } else {
                        conditionSql.append("='" + conditions.get(key) + "'");
                    }
                }
            }
            sql.append(conditionSql.toString());
        }
        if (isProduced) {
            //已出品菜品按划菜时间倒序排序
            sql.append(" ORDER BY stamp_time desc ");
        } else {
            sql.append(" ORDER BY countdown_time ");
        }


        return sql.toString();
    }


    /**
     *保存禁用菜品
     * @param param
     * @param result
     */
    public void addForbiddenItem(Data param,Data result){
        try {
            String tenantId = param.getTenancy_id();
            Integer organId = param.getStore_id();

            List dataList=param.getData();
            StringBuilder sql=new StringBuilder();
            if(dataList!=null && dataList.size()>0){
                for(Object o:dataList){
                    JSONObject data=JSONObject.fromObject(o);
                    sql.append("INSERT INTO pos_forbidden_item (tenancy_id, store_id, item_id,unit_id,opt_num,updatetime ) VALUES ");
                    sql.append("('"+tenantId+"',");
                    sql.append(organId+",");
                    sql.append(data.optInt("item_id")+",");
                    //sql.append(" '"+data.optString("item_num")+"',");
                    //sql.append(" '"+data.optString("item_name")+"',");
                    sql.append(data.optInt("unit_id")+",");
                    //sql.append(" '"+data.optString("unit_name")+"',");
                    sql.append(" '"+data.optString("opt_num")+"',");
                    sql.append(" '"+ com.tzx.framework.common.util.DateUtil.getNowDateYYDDMMHHMMSS()+"');");
                }
            }
            posDao.execute(tenantId,sql.toString());
            result.setMsg(Constant.ADD_FORBIDDEN_ITEM_SUCCESS);
            result.setSuccess(true);
            result.setCode(Constant.CODE_SUCCESS);
        }  catch (Exception e) {
            logger.info(e);
            result.setCode(Constant.CODE_INNER_EXCEPTION);
            result.setSuccess(false);
            result.setMsg(Constant.ADD_FORBIDDEN_ITEM_FAILURE);
            logger.error(e);
        }
    }

    /**
     * 删除禁用菜品
     * @param param
     * @param result
     */
    public void delForbiddenItem(Data param,Data result) {
        try {
            String tenantId = param.getTenancy_id();
            List dataList=param.getData();
            StringBuilder sql = new StringBuilder();
            if (dataList != null && dataList.size() > 0) {
                for(Object o:dataList){
                    JSONObject data=JSONObject.fromObject(o);
                    String item_id = data.optString("item_id");
                    if(Tools.hv(item_id))
                        if(item_id.contains(",")){
                            String[] itemIds = item_id.split(",");
                            for(int i=0;i<itemIds.length;i++){
                                String querySql = "select count(1) from pos_forbidden_item where id=" + Integer.parseInt(itemIds[i]);
                                SqlRowSet row = posDao.query(tenantId, querySql);
                                while (row.next()) {
                                    //刪除禁用记录
                                    sql.append("DELETE FROM pos_forbidden_item WHERE item_id =").append(Integer.parseInt(itemIds[i])).append(";");
                                }
                            }
                        }else{
                            String querySql = "select count(1) from pos_forbidden_item where id=" + Integer.parseInt(item_id);
                            SqlRowSet row = posDao.query(tenantId, querySql);
                            while (row.next()) {
                                //刪除禁用记录
                                sql.append("DELETE FROM pos_forbidden_item WHERE item_id =").append(Integer.parseInt(item_id)).append(";");
                            }
                        }
                }
            }
            posDao.execute(tenantId, sql.toString());
            result.setMsg(Constant.DEL_FORBIDDEN_ITEM_SUCCESS);
            result.setCode(Constant.CODE_SUCCESS);
            result.setSuccess(true);
        } catch (Exception e) {
            result.setCode(Constant.CODE_INNER_EXCEPTION);
            result.setMsg(Constant.DEL_FORBIDDEN_ITEM_FAILURE);
            result.setSuccess(false);
            logger.error(e);
        }
    }

    /**
     * 判断ForbiddenItem 是否变动，发送UDP消息到pos进行登录，同步pos_forbidden_item
     * @param param
     * @param result
     */
    public void checkForbiddenItem(Data param,Data result){
        try{

            String tenancyId = param.getTenancy_id();
            Integer storeId = param.getStore_id();

            //微生活小程序
            WlifeService wlifeProgramService = SpringConext.getApplicationContext().getBean(WlifeService.class);
            wlifeProgramService.synchrodataDish(tenancyId, storeId);

            //创建禁用菜品表版本
            this.buildBasicVersion4SingleTable(tenancyId, storeId,BasicData.FORBIDDEN_ITEM);
            //发送消息通知重新登录，获取禁用菜品
            this.sendNotice("禁用菜品",Type.CHANGE_DATA);
            result.setMsg(Constant.NOTIFY_FORBIDDEN_ITEM_SUCCESS);
            result.setCode(Constant.CODE_SUCCESS);
            result.setSuccess(true);
        }catch(Exception e){
            result.setCode(Constant.CODE_INNER_EXCEPTION);
            result.setMsg(Constant.NOTIFY_FORBIDDEN_ITEM_FAILURE);
            result.setSuccess(false);
            logger.error(e);
        }
    }


    /**
     *
     * 修改默认套餐项
     * @param param
     * @param result
     */
    public void updateItemComboDetailsDefault(Data param, Data result){
        try{
            String tenantId = param.getTenancy_id();
            Integer organId = param.getStore_id();

            StringBuilder addSql=new StringBuilder();
            StringBuilder updateSql=new StringBuilder();
            StringBuilder deleteSql=new StringBuilder();

            List dataList=param.getData();
            for(Object o:dataList){
                JSONObject data=JSONObject.fromObject(o);
                int setmeal_id = data.optInt("setmeal_id"); //套餐id
                int group_id  = data.optInt("group_id");  //套餐分组id
                int group_item_id = data.optInt("group_item_id");//套餐分组菜品id
                int unit_id =data.optInt("unit_id");//菜品规格id
                String pos_num = data.optString("pos_num");
                String opt_num = data.optString("opt_num");
                //根据  setmeal_id  group_id 查询 pos_item_combo_details_default 中是否存在

                addSql.append("INSERT INTO pos_item_combo_details_default (tenancy_id,store_id,setmeal_id,group_id,group_item_id,unit_id,pos_num,opt_num,updatetime ) VALUES ");
                addSql.append("('"+tenantId+"',");
                addSql.append(organId+",");
                addSql.append(setmeal_id+",");
                addSql.append(group_id+",");
                addSql.append(unit_id+",");
                addSql.append(group_item_id+",");
                addSql.append(" '"+data.optString("pos_num")+"',");
                addSql.append(" '"+data.optString("opt_num")+"',");
                addSql.append(" '"+ com.tzx.framework.common.util.DateUtil.getNowDateYYDDMMHHMMSS()+"');");




                //如果存在修改 不存在新增
                String querySql ="SELECT  id,tenancy_id,store_id,setmeal_id,group_id,group_item_id,unit_id,pos_num,opt_num,updatetime from pos_item_combo_details_default where tenancy_id=? and store_id=? and setmeal_id=? and group_id=?" ;
                List<JSONObject>  objList = posDao.query4Json(tenantId,querySql, new Object[] {tenantId,organId,setmeal_id,group_id});

                if(objList!=null && !objList.isEmpty()){
                    for(JSONObject obj:objList){
                        int id = obj.optInt("id");
                        deleteSql.append("delete from  pos_item_combo_details_default where id ="+id+";");
                    }
                }



                //                if(objList.isEmpty()){//新增数据
//                    addSql.append("INSERT INTO pos_item_combo_details_default (tenancy_id,store_id,setmeal_id,group_id,group_item_id,unit_id,pos_num,opt_num,updatetime from pos_item_combo_details_default) VALUES ");
//                    addSql.append("('"+tenantId+"',");
//                    addSql.append(organId+",");
//                    addSql.append(setmeal_id+",");
//                    addSql.append(group_id+",");
//                    addSql.append(unit_id+",");
//                    addSql.append(group_item_id+",");
//                    addSql.append(" '"+data.optString("pos_num")+"',");
//                    addSql.append(" '"+data.optString("opt_num")+"',");
//                    addSql.append(" '"+ com.tzx.framework.common.util.DateUtil.getNowDateYYDDMMHHMMSS()+"');");
//                }else{//修改数据
//                    for(JSONObject obj:objList){
//                        int id = obj.optInt("id");
//                        updateSql.append("update  pos_item_combo_details_default set ");
//                        updateSql.append(" group_item_id=").append(group_item_id);
//                        updateSql.append(" ,unit_id=").append(unit_id);
//                        updateSql.append(" ,pos_num='").append(pos_num).append("'");
//                        updateSql.append(" ,opt_num='").append(opt_num).append("'");
//                        updateSql.append(" ,updatetime='").append(com.tzx.framework.common.util.DateUtil.getNowDateYYDDMMHHMMSS()).append("'");
//                        updateSql.append(" where id =").append(id).append(";");
//                    }
//                }
            }

            if(deleteSql!=null && !"".equals(deleteSql.toString())){
                posDao.execute(tenantId,deleteSql.toString());//批量执行删除
            }
            posDao.execute(tenantId,addSql.toString());//批量执行新增
            //posDao.execute(tenantId,updateSql.toString());//批量执行修改

            //创建 basic_data_version;
            //this.buildBasicVersion4SingleTable(tenantId,organId,BasicData.SETTING_SETMEAL_DETAILS);

            //发送消息通知重新登录，获取设置套餐明细默认菜品
            //this.sendNotice("默认套餐项",Type.CHANGE_DATA);

            result.setMsg(Constant.SETTING_SETMEAL_DETAILS_SUCCESS);
            result.setCode(Constant.CODE_SUCCESS);
            result.setSuccess(true);
        }catch(Exception e){
            result.setCode(Constant.CODE_INNER_EXCEPTION);
            result.setMsg(Constant.SETTING_SETMEAL_DETAILS_FAILURE);
            result.setSuccess(false);
            e.printStackTrace();
            logger.info(e.getMessage());
        }
    }


    /**
     * 恢复默认套餐项
     * @param param
     * @param result
     */
    public void recoverItemComboDetailsDefault(Data param, Data result){
        try{
            String tenantId = param.getTenancy_id();
            Integer organId = param.getStore_id();

            StringBuilder delSql=new StringBuilder();

            List dataList=param.getData();
            for(Object o:dataList){
                JSONObject data=JSONObject.fromObject(o);
                int setmeal_id = data.optInt("setmeal_id"); //套餐id
                String querySql ="SELECT  id,tenancy_id,store_id,setmeal_id,group_id,group_item_id,unit_id,pos_num,opt_num,updatetime from pos_item_combo_details_default where tenancy_id=? and store_id=? and setmeal_id=? " ;
                List<JSONObject>  objList = posDao.query4Json(tenantId,querySql, new Object[] {tenantId,organId,setmeal_id});
                if(!objList.isEmpty()){//删除数据
                    for(JSONObject obj:objList){
                        int id = obj.optInt("id");
                        delSql.append("delete from  pos_item_combo_details_default ");
                        delSql.append("where id =").append(id).append(";");
                    }
                }
                posDao.execute(tenantId,delSql.toString());//批量执行删除

                //创建 basic_data_version;
                //this.buildBasicVersion4SingleTable(tenantId,organId,BasicData.SETTING_SETMEAL_DETAILS);

                //发送消息通知重新登录，获取设置套餐明细默认菜品
                //this.sendNotice("默认套餐项",Type.CHANGE_DATA);

                result.setMsg(Constant.RECOVER_SETMEAL_DETAILS_SUCCESS);
                result.setCode(Constant.CODE_SUCCESS);
                result.setSuccess(true);
            }
        }catch(Exception e){
            result.setCode(Constant.CODE_INNER_EXCEPTION);
            result.setMsg(Constant.RECOVER_SETMEAL_DETAILS_FAILURE);
            result.setSuccess(false);
            logger.error(e);
        }
    }


    public void checkItemComboDetailsDefault(Data param,Data result){
        try{
            String tenancyId = param.getTenancy_id();
            Integer storeId = param.getStore_id();
            //创建 basic_data_version;
            this.buildBasicVersion4SingleTable(tenancyId,storeId,BasicData.SETTING_SETMEAL_DETAILS);

            //发送消息通知重新登录，获取禁用菜品
            this.sendNotice("默认套餐项",Type.CHANGE_DATA);
            result.setMsg(Constant.NOTIFY_SETMEAL_DETAILS_SUCCESS);
            result.setCode(Constant.CODE_SUCCESS);
            result.setSuccess(true);
        }catch(Exception e){
            result.setCode(Constant.CODE_INNER_EXCEPTION);
            result.setMsg(Constant.NOTIFY_SETMEAL_DETAILS_FAILURE);
            result.setSuccess(false);
            logger.error(e);
        }
    }


    public void updateSettingShiftsTimes(Data param,Data result){
        try{
            String tenantId = param.getTenancy_id();
            Integer organId = param.getStore_id();

            StringBuilder addSql=new StringBuilder();
            StringBuilder updateSql=new StringBuilder();

            List dataList=param.getData();
            for(Object o:dataList){
                JSONObject data=JSONObject.fromObject(o);
                int limit_count = data.optInt("limit_count"); //限制次数
                String pos_num = data.optString("pos_num");
                String opt_num = data.optString("opt_num");

                String querySql ="SELECT id,tenancy_id,store_id,limit_count,pos_num,opt_num,updatetime from pos_setting_shifts_times where tenancy_id=? and (store_id=? or store_id=0)" ;
                List<JSONObject>  objList = posDao.query4Json(tenantId,querySql, new Object[] {tenantId,organId});
                if(objList.isEmpty()){//新增数据
                    addSql.append("INSERT INTO pos_setting_shifts_times (tenancy_id,store_id,limit_count,pos_num,opt_num,updatetime ) VALUES ");
                    addSql.append("('"+tenantId+"',");
                    addSql.append(organId+",");
                    addSql.append(limit_count+",");
                    addSql.append(" '"+data.optString("pos_num")+"',");
                    addSql.append(" '"+data.optString("opt_num")+"',");
                    addSql.append(" '"+ com.tzx.framework.common.util.DateUtil.getNowDateYYDDMMHHMMSS()+"');");
                }else{//修改数据
                    for(JSONObject obj:objList){
                        int id = obj.optInt("id");
                        updateSql.append("update  pos_setting_shifts_times set ");
                        updateSql.append(" limit_count=").append(limit_count);
                        updateSql.append(" , pos_num='").append(pos_num).append("'");
                        updateSql.append(" , opt_num='").append(opt_num).append("'");
                        updateSql.append(" , updatetime='").append(com.tzx.framework.common.util.DateUtil.getNowDateYYDDMMHHMMSS()).append("'");
                        updateSql.append(" where id =").append(id).append(";");
                    }
                }

                posDao.execute(tenantId,addSql.toString());//批量执行新增
                posDao.execute(tenantId,updateSql.toString());//批量执行修改

                //创建 basic_data_version;
                this.buildBasicVersion4SingleTable(tenantId,organId,BasicData.SYS_PARAMETER);

                //发送消息通知重新登录，获取设置套餐明细默认菜品
                this.sendNotice("交班限制次数",Type.CHANGE_DATA);

                result.setMsg(Constant.SETTING_SHIFTS_TIMES_SUCCESS);
                result.setCode(Constant.CODE_SUCCESS);
                result.setSuccess(true);
            }
        }catch(Exception e){
            result.setCode(Constant.CODE_INNER_EXCEPTION);
            result.setMsg(Constant.SETTING_SHIFTS_TIMES_FAILURE);
            result.setSuccess(false);
            logger.info(e);
        }
    }


    /**
     *单表生成版本号
     * @param tenantId
     * @param organId
     * @param dataType
     * @throws Exception
     */
    public void buildBasicVersion4SingleTable(String tenantId, Integer organId ,BasicData dataType) throws Exception {
        List<Object[]> delList = new ArrayList<Object[]>();
        List<Object[]> insList = new ArrayList<Object[]>();
        String md5 = "";
        try {
            switch (dataType) {
                case FORBIDDEN_ITEM:
                    md5 = JavaMd5.toMd5B32(posDao.getPosForbiddenItem(tenantId, organId).toString());
                    break;
                case SETTING_SETMEAL_DETAILS:
                    md5 = JavaMd5.toMd5B32(posDao.getPosItemComboDetailsDefault(tenantId, organId).toString());
                    break;
                case SYS_PARAMETER:
                    md5 = posDao.getSysParameterEx(tenantId, organId);
                    break;
                default:
                    md5 = "";
            }
        } catch (Exception e) {
            logger.info("生成版本号失败,表名:" + dataType.name(), e);
            md5 = "";
        }

        if (!"".equals(md5)) {
            delList.add(new Object[]{tenantId, organId, dataType.getType()});
            insList.add(new Object[]{tenantId, organId, PropertiesLoader.getProperty(dataType.getCode()), dataType.getType(), md5});
        }

        if (delList.size() > 0) {
            String delsql = "delete from pos_data_version where tenancy_id=? and store_id=? and para_code =?";
            posDao.batchUpdate(delsql, delList);

            String inssql = "insert into pos_data_version(tenancy_id,store_id,system_name,model_name,para_name,para_code,para_value,para_defaut,para_type,valid_state) values(?,?,'POS','数据同步版本',?,?,?,'1','初始化','1')";
            posDao.batchUpdate(inssql, insList);
        }
    }


    public void sendNotice(String message,Type type){
        List<JSONObject> noticeList = new ArrayList<JSONObject>();
        JSONObject noticeJson = new JSONObject();
        noticeJson.put("message", message+"数据更改。请重新登录获取数据！");
        noticeList.add(noticeJson);
        Comet4jUtil.comet4J(noticeList, type, Oper.notice);
    }

    public void combineLockOrUnlockTable(JSONObject billJson,String tenantId,Integer organId,String optNum,String posNum,String mode, Data result) throws Exception{
        if(null!= billJson) {
            String sbill_num = billJson.optString("sbill_num");
            if (Tools.hv(sbill_num)) {//并台
                if (sbill_num.contains(",")) {
                    String[] sbillNums = sbill_num.split(",");
                    for (String sbillNum : sbillNums) {
                        JSONObject bill = posDishDao.getPosBillByBillnum(tenantId, organId, sbillNum);
                        if (null != bill && Tools.hv(bill.optString("table_code"))) {
                            posDao.lockOrUnlockTable(tenantId, organId, optNum, posNum, bill.optString("table_code"), mode, result);
                        }
                    }
                } else {
                    JSONObject bill = posDishDao.getPosBillByBillnum(tenantId, organId, sbill_num);
                    if (null != bill && Tools.hv(bill.optString("table_code"))) {
                        posDao.lockOrUnlockTable(tenantId, organId, optNum, posNum, bill.optString("table_code"), mode, result);
                    }
                }
            }
        }
    }



    public void updateTableState(String tenantId,Integer organId){
        try{
            String stateSql =" update pos_tablestate set state='"+SysDictionary.TABLE_STATE_FREE+"',lock_pos_num=NULL,opt_name=NULL,lock_opt_num=NULL,bill_batch_num=NULL,table_open_num=NULL,stable=NULL where tenancy_id='"+tenantId+"' and store_id="+organId;
            posDao.execute(tenantId,stateSql);

            String infoSql=" update tables_info set state='"+SysDictionary.TABLE_STATE_FREE+"' where tenancy_id='"+tenantId+"' and organ_id="+organId;
            posDao.execute(tenantId,infoSql);
        }catch (Exception e){
            e.printStackTrace();
            logger.info("清空桌台状态失败");
        }
    }

    @Override
    public void findEmployeeInfo(Data param, Data result) throws Exception{
        String tenantId = param.getTenancy_id();
        Integer storeId = param.getStore_id();
        JSONObject org = posDao.getOrganById(tenantId,  storeId);
        if (null != org && !org.isEmpty())
        {
            String organ_code = org.optString("organ_code");
            List<JSONObject> jsonList = new ArrayList<JSONObject>();
            JSONObject json = new JSONObject();
            json.put("organ_code",organ_code);
            jsonList.add(json);
            Data data = Data.get();
            data.setType(Type.HQ_EMPLOYEE_INFO);
            data.setOper(Oper.find);
            data.setTenancy_id(tenantId);
            data.setStore_id(storeId);
            data.setData(jsonList);

            String responseResult = HttpUtil.sendPostRequest(PosPropertyUtil.getMsg("saas.url") + postUrl, JSONObject.fromObject(data).toString(), 30000, 10000);

            if (null != responseResult && !"".equals(responseResult))
            {
                Data responseData = ParamUtil.stringToData(responseResult);
                List<?> responseList = responseData.getData();
                if (responseList == null || responseList.isEmpty())
                {
                    throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
                }
                JSONObject responseJson = JSONObject.fromObject(responseList.get(0));
                if (Constant.CODE_SUCCESS == responseData.getCode())
                {
                    responseData.setCode(Constant.CODE_SUCCESS);
                    List<JSONObject> resultList = new ArrayList<JSONObject>();
                    result.setData(responseList);
                }
                else
                {
                    logger.debug("查询人员失败");
                    boolean is_succeed = false;
                    String  failure_code = responseJson.optString("failure_code");
                    String failure_msg = responseJson.optString("failure_msg");
                    result.setSuccess(is_succeed);
                    result.setCode(Integer.parseInt(failure_code));
                    result.setMsg(failure_msg);
                }
            }
            else
            {// 返回为空
                boolean is_succeed = false;
                result.setCode(Constant.CODE_NULL_DATASET);
                result.setMsg(Constant.GET_PAYMENT_WAY_MESSAGE_IS_NULL);
                result.setSuccess(is_succeed);
            }
        }
    }


    public void addEmployeeInfo(Data param, Data result) throws Exception{
        String tenantId = param.getTenancy_id();
        Integer storeId = param.getStore_id();
        List<?> paramList = param.getData();

        //验证参数
        if (null == paramList || paramList.isEmpty())
        {
            throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
        }

        JSONObject paraJson = JSONObject.fromObject(paramList.get(0));
        String opt_num = paraJson.optString("opt_num");
        String operator = posDao.getEmpNameById(opt_num, tenantId, storeId);
        paraJson.put("create_name",operator);
        List list = new ArrayList();
        list.add(paraJson);
        param.setData(list);
        String responseResult = HttpUtil.sendPostRequest(PosPropertyUtil.getMsg("saas.url") + postUrl, JSONObject.fromObject(param).toString());

        if (null != responseResult && !"".equals(responseResult))
        {
            Data responseData = ParamUtil.stringToData(responseResult);

            if (Constant.CODE_SUCCESS == responseData.getCode())
            {
                List<?> responseList = responseData.getData();
                JSONObject responseJson = JSONObject.fromObject(responseList.get(0));

                //总部新增成功 本地数据增加记录
                this.addEmpAndAuth(tenantId,storeId,responseJson);
                responseData.setCode(Constant.CODE_SUCCESS);
                List<JSONObject> resultList = new ArrayList<JSONObject>();
                resultList.add(responseJson);
                result.setData(resultList);
            }
            else
            {
                logger.debug("新增人员失败");
                boolean is_succeed = false;
                Integer  failure_code = responseData.getCode();
                String failure_msg = responseData.getMsg();
                result.setSuccess(is_succeed);
                result.setCode(failure_code);
                result.setMsg(failure_msg);
            }
        }
        else
        {// 返回为空
            boolean is_succeed = false;
            result.setCode(Constant.CODE_NULL_DATASET);
            result.setMsg(Constant.GET_PAYMENT_WAY_MESSAGE_IS_NULL);
            result.setSuccess(is_succeed);
        }
    }

    public void updateEmployeeInfo(Data param, Data result) throws Exception{
        String tenantId = param.getTenancy_id();
        Integer storeId = param.getStore_id();
        List<?> paramList = param.getData();

        //验证参数
        if (null == paramList || paramList.isEmpty())
        {
            throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
        }

        String responseResult = HttpUtil.sendPostRequest(PosPropertyUtil.getMsg("saas.url") + postUrl, JSONObject.fromObject(param).toString());

        if (null != responseResult && !"".equals(responseResult))
        {
            Data responseData = ParamUtil.stringToData(responseResult);

            if (Constant.CODE_SUCCESS == responseData.getCode())
            {
                List<?> responseList = responseData.getData();
                JSONObject responseJson = JSONObject.fromObject(responseList.get(0));
                JSONObject paraData = JSONObject.fromObject(param.getData().get(0));
                responseJson.put("roles_id",paraData.optInt("roles"));
                this.updateEmployeeInfo(tenantId,storeId, responseJson,Oper.update);
                responseData.setCode(Constant.CODE_SUCCESS);
                List<JSONObject> resultList = new ArrayList<JSONObject>();
                resultList.add(responseJson);
                result.setData(resultList);
            }
            else
            {
                logger.debug("修复人员信息失败");
                boolean is_succeed = false;
                Integer  failure_code = responseData.getCode();
                String failure_msg = responseData.getMsg();
                result.setSuccess(is_succeed);
                result.setCode(failure_code);
                result.setMsg(failure_msg);
            }
        }
        else
        {// 返回为空
            boolean is_succeed = false;
            result.setCode(Constant.CODE_NULL_DATASET);
            result.setMsg(Constant.GET_PAYMENT_WAY_MESSAGE_IS_NULL);
            result.setSuccess(is_succeed);
        }
    }

    public void delEmployeeById(Data param, Data result) throws Exception{
        String tenantId = param.getTenancy_id();
        Integer storeId = param.getStore_id();
        List<?> paramList = param.getData();

        //验证参数
        if (null == paramList || paramList.isEmpty())
        {
            throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
        }

        String responseResult = HttpUtil.sendPostRequest(PosPropertyUtil.getMsg("saas.url") + postUrl, JSONObject.fromObject(param).toString());

        if (null != responseResult && !"".equals(responseResult))
        {
            Data responseData = ParamUtil.stringToData(responseResult);
            if (Constant.CODE_SUCCESS == responseData.getCode())
            {

                List<?> responseList = responseData.getData();
                JSONObject responseJson = JSONObject.fromObject(responseList.get(0));
                JSONObject jsonPara = JSONObject.fromObject(paramList.get(0));
                //总部删除成功 本地修改人员状态
                this.updateEmployeeInfo(tenantId,storeId, jsonPara,Oper.delete);

                responseData.setCode(Constant.CODE_SUCCESS);
                List<JSONObject> resultList = new ArrayList<JSONObject>();
                resultList.add(responseJson);
                result.setData(resultList);
            }
            else
            {
                logger.debug("删除人员失败");
                boolean is_succeed = false;
                Integer failure_code = responseData.getCode();
                String failure_msg = responseData.getMsg();
                result.setSuccess(is_succeed);
                result.setCode(failure_code);
                result.setMsg(failure_msg);
            }
        }
        else
        {// 返回为空
            boolean is_succeed = false;
            result.setCode(Constant.CODE_NULL_DATASET);
            result.setMsg(Constant.GET_PAYMENT_WAY_MESSAGE_IS_NULL);
            result.setSuccess(is_succeed);
        }
    }


    public void addEmpAndAuth(String  tenantId,Integer organId,JSONObject paraJson) throws Exception{
        posDao.addEmpAndAuth(tenantId, organId,paraJson);
    }

    public void updateEmployeeInfo(String  tenantId,Integer organId,JSONObject paraJson,Oper opertype) throws Exception {
        posDao.updateEmployeeInfo(  tenantId, organId,paraJson, opertype);
    }

    //--------------- 自动日结-----------------
    private final String dailyUrl = "/boh/autoDailySettlementRest/dailySettlement";
    public JSONObject autoDailySettlement(String tenancyId, int storeId, JSONObject json) throws Exception {
        JSONObject reqJson = new JSONObject();
        List<JSONObject> jsonList = new ArrayList<JSONObject>();
        jsonList.add(json);

        Data data = Data.get();
        data.setType(Type.DAYCOUNT_INFO);
        data.setOper(Oper.upload);
        data.setTenancy_id(tenancyId);
        data.setStore_id(storeId);
        data.setData(jsonList);

        JSONObject postReq = HttpUtil.post(PosPropertyUtil.getMsg("saas.url") + dailyUrl, JSONObject.fromObject(data), 1, true);
        return postReq;
    }

    public JSONObject findAutoDailyInfoByParam(String tenancyId, int storeId, JSONObject json) throws Exception {
        JSONObject obj = new JSONObject();
        String querySql = "SELECT  * from hq_auto_daycount_info where tenancy_id=? and store_id=? and day_count=? and daily_count_state=? ";
        List<JSONObject> objList = posDao.query4Json(tenancyId, querySql, new Object[]{tenancyId, storeId, json.optString("day_count"), "0"});
        if (objList != null && !objList.isEmpty()) {
            obj = JSONObject.fromObject(objList.get(0));
        }
        return obj;
    }

    public void updateAutoDailyInfo(String tenancyId, int storeId, JSONObject json) throws Exception {
        String querySql = "SELECT  * from hq_auto_daycount_info where tenancy_id=? and store_id=? and day_count=?";
        List<JSONObject> objList = posDao.query4Json(tenancyId, querySql, new Object[]{tenancyId, storeId, json.optString("day_count")});
        if (objList == null || objList.isEmpty()) {
            StringBuffer sb = new StringBuffer();
            sb.append(" insert into hq_auto_daycount_info (tenancy_id, store_id, day_count, day_end_state, upload_state, daily_count_state, upload_num, daily_count_num, updatetime)  values(?,?,?,?,?,?,?,?,?)");
            posDao.update(sb.toString(), new Object[]{tenancyId, storeId, json.optString("day_count"), json.optString("day_end_state"),
                    json.optString("upload_state"), json.optString("daily_count_state"), json.optInt("upload_num"), json.optInt("daily_count_num"), com.tzx.framework.common.util.DateUtil.getNowDateYYDDMMHHMMSS()});
        } else {
            JSONObject obj = JSONObject.fromObject(objList.get(0));
            int id = obj.optInt("id");
            StringBuffer sb = new StringBuffer();
            sb.append("update hq_auto_daycount_info set upload_state = ? ,daily_count_state=?,upload_num =?,daily_count_num = ?,updatetime=? where tenancy_id=? and store_id=? and id=? ");
            posDao.update(sb.toString(), new Object[]{json.optString("upload_state"), json.optString("daily_count_state"), json.optInt("upload_num"), json.optInt("daily_count_num"), com.tzx.framework.common.util.DateUtil.getNowDateYYDDMMHHMMSS(),
                    tenancyId, storeId, id});

        }
    }

    public List<JSONObject> findAutoDailyInfo(String tenancyId, int storeId, int daily_count_num) throws Exception {
        String querySql = "SELECT  * from hq_auto_daycount_info where tenancy_id=? and store_id=?  and daily_count_state=? and daily_count_num <? order by id asc";
        List<JSONObject> objList = posDao.query4Json(tenancyId, querySql, new Object[]{tenancyId, storeId, "0", daily_count_num});
        return objList;
    }
}
