package com.tzx.member.crm.bo.imp;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.springframework.stereotype.Service;

import com.tzx.framework.common.constant.Code;
import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.HttpUtil;
import com.tzx.framework.common.util.Tools;
import com.tzx.member.common.entity.CrmCardPaymentListEntity;
import com.tzx.member.common.entity.CrmCardTradingListEntity;
import com.tzx.member.common.entity.PosCustomerOperateListEntity;
import com.tzx.member.crm.bo.CustomerCardRechargeService;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.base.util.ParamUtil;


@Service(CustomerCardRechargeService.NAME)
public class CustomerCardRechargeServiceImp extends CustomerServiceImp implements CustomerCardRechargeService
{
	/**
	 * 撤销充值参数Model,2:用third_bill_code撤销
	 */
	private final String	CARD_RECHARGE_CANCEL_MODEL	= "2";

	/**
	 * 撤销充值参数Model,1:用old_bill_code撤销
	 */
	private final String	CARD_RECHARGE_REVOKE_MODEL	= "1";

	@Override
	public String createThirdBillCode(String tenancyId, Integer storeId, JSONObject paramJson) throws Exception
	{
		String reportDateStr = ParamUtil.getStringValueByObject(paramJson, "report_date", true, PosErrorCode.NOT_NULL_REPORT_DATE);
		String billCode = "";
		try
		{
			// 生成新的账单号和流水单号
			JSONObject object = new JSONObject();
			object.element("store_id", storeId);
			object.element("busi_date", reportDateStr);
			billCode = codeService.getCode(tenancyId, Code.POS_THIRD_PAYMENT_ORDER_NUM, object);// 调用统一接口来实现
			if (Tools.isNullOrEmpty(billCode))
			{
				throw new SystemException(PosErrorCode.GET_BILLNUM_SERVICE_ERROR);
			}
		}
		catch (Exception e)
		{
			logger.error("会员充值：" + ExceptionMessage.getExceptionMessage(e));
			throw new SystemException(PosErrorCode.GET_BILLNUM_SERVICE_ERROR);
		}
		return billCode;
	}

	@Override
	public void printForCustomerCardRecharge(String tenancyId, Integer storeId, String printCode, JSONObject paramJson) throws Exception
	{
		// TODO Auto-generated method stub
		try
		{
			String posNum = ParamUtil.getStringValueByObject(paramJson, "pos_num");
			String strUrlContent = ParamUtil.getStringValueByObject(paramJson, "url_content");
			String strUrlPath = ParamUtil.getStringValueByObject(paramJson, "url_path");
			Double payMoney = ParamUtil.getDoubleValueByObject(paramJson, "income");
			String billCode = ParamUtil.getStringValueByObject(paramJson, "bill_code");
			Integer paymentId = ParamUtil.getIntegerValueByObject(paramJson, "payment_id");

			String isInvoice = "0";
			Double invoiceAmount = 0d;

			if (Tools.hv(strUrlContent))
			{
				isInvoice = "1";
				invoiceAmount = payMoney;
			}
			String paymentName = "";
			if (null != paymentId && paymentId > 0)
			{
				JSONObject paymentWayJson = customerDao.getPaymentWayByID(tenancyId, storeId, paymentId);
				paymentName = ParamUtil.getStringValueByObject(paymentWayJson, "payment_name");
			}

			// // 打印入参
			JSONObject printJson = new JSONObject();
			printJson.put("print_code", printCode);
			printJson.put("mode", "1");

			printJson.put("pos_num", posNum);
			printJson.put("bill_code", billCode);
			printJson.put("income", payMoney);
			printJson.put("bill_money", payMoney);
			printJson.put("pay_money", payMoney);
			printJson.put("paymenttypename", paymentName);
			printJson.put("level_name", ParamUtil.getStringValueByObject(paramJson, "level_name"));
			printJson.put("card_class_name", ParamUtil.getStringValueByObject(paramJson, "card_class_name"));
			printJson.put("card_code", ParamUtil.getStringValueByObject(paramJson, "card_code"));
			printJson.put("name", ParamUtil.getStringValueByObject(paramJson, "name"));
			printJson.put("mobil", ParamUtil.getStringValueByObject(paramJson, "mobil"));
			printJson.put("main_trading", ParamUtil.getDoubleValueByObject(paramJson, "main_trading"));
			printJson.put("reward_trading", ParamUtil.getDoubleValueByObject(paramJson, "reward_trading"));
			printJson.put("main_balance", ParamUtil.getDoubleValueByObject(paramJson, "main_balance")); //充值前金额
			printJson.put("before_balance", ParamUtil.getDoubleValueByObject(paramJson, "before_balance"));
			printJson.put("reward_balance", ParamUtil.getDoubleValueByObject(paramJson, "reward_balance"));
			printJson.put("reward_credit", ParamUtil.getDoubleValueByObject(paramJson, "reward_credit"));
			printJson.put("useful_credit", ParamUtil.getDoubleValueByObject(paramJson, "useful_credit"));

			printJson.put("before_credit",ParamUtil.getDoubleValueByObject(paramJson, "before_credit"));//充值后前积分

			printJson.put("total_main", 0d);
			printJson.put("total_reward", 0d);
			printJson.put("credit", 0d);
			printJson.put("operator", ParamUtil.getStringValueByObject(paramJson, "operator"));
			printJson.put("channel", ParamUtil.getStringValueByObject(paramJson, "chanel"));
			printJson.put("updatetime", ParamUtil.getStringValueByObject(paramJson, "updatetime"));
			printJson.put("reward_coupon", ParamUtil.getStringValueByObject(paramJson, "reward_coupon"));
			printJson.put("url_content", strUrlContent);
			printJson.put("url_path", strUrlPath);
			printJson.put("is_invoice", isInvoice);
			printJson.put("invoice_amount", invoiceAmount);

			if (posPrintNewService.isNONewPrint(tenancyId, storeId))
			{// 如果启用新的打印模式
				posPrintNewService.posPrintByMode(tenancyId, storeId, printCode, printJson);
			}
			else
			{
				List<JSONObject> printList = new ArrayList<>();
				printList.add(printJson);

				Data paramData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
				paramData.setData(printList);

				Data result = new Data();

				String printCountStr = customerDao.getSysParameter(tenancyId, storeId, "MemberReceiptCount");
				if (Tools.isNullOrEmpty(printCountStr))
				{
					printCountStr = "0";
				}
				Integer printCount = Integer.parseInt(printCountStr);
				if (printCount <= 0)
				{
					printCount = 1;
				}

				for (int i = 0; i < printCount; i++)
				{
					posPrintService.printPosBill(paramData, result);
				}
			}
		}
		catch (Exception e)
		{
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

	@Override
	public void printForCustomerCardRecharge(String tenancyId, Integer storeId, JSONObject paramJson) throws Exception
	{
		// TODO Auto-generated method stub
		String isInvoice = ParamUtil.getStringValueByObject(paramJson, "is_invoice");
		if ("1".equals(isInvoice))
		{
			// 生成电子发票
			JSONObject invoiceParamJson = new JSONObject();
			invoiceParamJson.put("is_invoice", isInvoice);
			invoiceParamJson.put("bill_code", ParamUtil.getStringValueByObject(paramJson, "bill_code"));
			invoiceParamJson.put("business_date", ParamUtil.getStringValueByObject(paramJson, "report_date"));
			JSONObject invoiceJson = this.createElectricInvoice(tenancyId, storeId, invoiceParamJson);

			paramJson.put("url_content", invoiceJson.optString("url_content"));
			paramJson.put("url_path", invoiceJson.optString("url_path"));
		}
		// 打印小票
		this.printForCustomerCardRecharge(tenancyId, storeId, SysDictionary.PRINT_CODE_1008, paramJson);
	}

	@Override
	public String addCustomerOperateListForRecharge(String tenancyId, Integer storeId, String OperatType, JSONObject paramJson) throws Exception
	{
		// TODO Auto-generated method stub
		String reportDateStr = ParamUtil.getStringValueByObject(paramJson, "report_date", false, null);
		Integer paymentId = ParamUtil.getIntegerValueByObject(paramJson, "payment", false, null);
		Double tradeAmount = ParamUtil.getDoubleValueByObject(paramJson, "income", false, null);
		Date reportDate = DateUtil.parseDate(reportDateStr);

		String isInvoice = ParamUtil.getStringValueByObject(paramJson, "is_invoice");
		String authcode = ParamUtil.getStringValueByObject(paramJson, "credential");
		if (Tools.isNullOrEmpty(isInvoice))
		{
			isInvoice = "0";
		}

		Timestamp currentTime = DateUtil.currentTimestamp();

		String billCode = this.createThirdBillCode(tenancyId, storeId, paramJson);
		String paymentClass = null;
		if(null !=paymentId &&paymentId>0)
		{
			JSONObject paymentWayJson = customerDao.getPaymentWayByID(tenancyId, storeId, paymentId);
			paymentClass = ParamUtil.getStringValueByObject(paymentWayJson, "payment_class");
			
			if(SysDictionary.PAYMENT_CLASS_SCAN_CODE_PAY.equals(paymentClass) && Tools.hv(authcode))
			{
				paymentWayJson = thirdPaymentService.getPaymentClassByuAuthcode(tenancyId, storeId, authcode);
				paymentId = ParamUtil.getIntegerValueByObject(paymentWayJson, "payment_id");
				paymentClass = ParamUtil.getStringValueByObject(paymentWayJson, "payment_class");
				
				paramJson.put("payment", paymentId);
			}
		}

		PosCustomerOperateListEntity cardRecharge = new PosCustomerOperateListEntity();
		cardRecharge.setTenancy_id(tenancyId);
		cardRecharge.setStore_id(storeId);
		cardRecharge.setBusiness_date(reportDate);
		cardRecharge.setOperator_id(ParamUtil.getIntegerValueByObject(paramJson, "opt_num"));
		cardRecharge.setPos_num(ParamUtil.getStringValueByObject(paramJson, "pos_num"));
		cardRecharge.setShift_id(ParamUtil.getIntegerValueByObject(paramJson, "shift_id"));
		cardRecharge.setOperate_time(currentTime);
		cardRecharge.setChanel(ParamUtil.getStringValueByObject(paramJson, "chanel"));
		cardRecharge.setService_type(SysDictionary.SERVICE_TYPE_RECHARGE);
		cardRecharge.setOperat_type(OperatType);
		cardRecharge.setCard_code(ParamUtil.getStringValueByObject(paramJson, "card_code"));
		cardRecharge.setThird_code(ParamUtil.getStringValueByObject(paramJson, "third_code"));
		cardRecharge.setThird_bill_code(billCode);
		cardRecharge.setBill_code_original(ParamUtil.getStringValueByObject(paramJson, "bill_code_original"));
		cardRecharge.setTrade_amount(tradeAmount);
		cardRecharge.setTrade_credit(0d);
		cardRecharge.setDeposit(0d);
		cardRecharge.setSales_price(0d);
		cardRecharge.setSales_person(ParamUtil.getStringValueByObject(paramJson, "salesman"));
		cardRecharge.setPayment_id(paymentId);
		cardRecharge.setPayment_class(paymentClass);
		cardRecharge.setIs_invoice(isInvoice);
		cardRecharge.setPayment_state(SysDictionary.THIRD_PAY_STATUS_PAYING);
		cardRecharge.setOperate_state(SysDictionary.CUSTOMER_OPERATE_STATE_WAIT);
		cardRecharge.setCancel_state(SysDictionary.CUSTOMER_OPERATE_CANCEL_STATE_N);
		cardRecharge.setRequest_status(SysDictionary.REQUEST_STATUS_ING);
		cardRecharge.setLast_query_time(currentTime);
		cardRecharge.setQuery_count(0);
		cardRecharge.setAction_type(Type.CUSTOMER_CARD_RECHARGE.name());

		JSONObject extJson = new JSONObject();
		extJson.put("reward_money", ParamUtil.getDoubleValueByObject(paramJson, "reward_money"));
		extJson.put("auto_reward_money", ParamUtil.getStringValueByObject(paramJson, "auto_reward_money"));
		cardRecharge.setExtend_param(extJson.toString());
		
		customerDao.insertPosCustomerOperateList(tenancyId, storeId, cardRecharge);

		return billCode;
	}

	@Override
	public Data precreatePaymentForRecharge(String tenantId, Integer storeId, String billCode, JSONObject paramJson, String path) throws Exception
	{
		// TODO Auto-generated method stub
		Double settleAmount = ParamUtil.getDoubleValueByObject(paramJson, "income", false, null);

		JSONObject thirdParamJson = new JSONObject();
		thirdParamJson.put("order_no", billCode);
		thirdParamJson.put("order_num", billCode);
		thirdParamJson.put("bill_num", billCode);
		thirdParamJson.put("service_type", SysDictionary.SERVICE_TYPE_RECHARGE);
		thirdParamJson.put("report_date", ParamUtil.getStringValueByObject(paramJson, "report_date"));
		thirdParamJson.put("shift_id", ParamUtil.getIntegerValueByObject(paramJson, "shift_id"));
		thirdParamJson.put("channel", ParamUtil.getStringValueByObject(paramJson, "chanel"));
		thirdParamJson.put("pos_num", ParamUtil.getStringValueByObject(paramJson, "pos_num"));
		thirdParamJson.put("opt_num", ParamUtil.getStringValueByObject(paramJson, "opt_num"));
		thirdParamJson.put("settle_amount", settleAmount);
		thirdParamJson.put("total_amount", settleAmount);
		thirdParamJson.put("payment_id", ParamUtil.getIntegerValueByObject(paramJson, "payment"));
		thirdParamJson.put("subject", "会员卡充值");
		thirdParamJson.put("currency_name", ParamUtil.getStringValueByObject(paramJson, "currency_name"));
		thirdParamJson.put("body", ParamUtil.getStringValueByObject(paramJson, "body"));
		thirdParamJson.put("client_ip", ParamUtil.getStringValueByObject(paramJson, "client_ip"));
		thirdParamJson.put("description", ParamUtil.getStringValueByObject(paramJson, "description"));
		thirdParamJson.put("extra", ParamUtil.getStringValueByObject(paramJson, "extra"));
		thirdParamJson.put("oper_type", Type.CUSTOMER_CARD_RECHARGE.name());

		Data resultData = thirdPaymentService.precreatePayment(tenantId, storeId, thirdParamJson, path);

		JSONObject resultJson = null;
		String paymentState = null;
		String operateState = null;
		String requestStatus = null;
		Integer requestCode = null;
		String requestMsg = null;
		Timestamp finishTime = null;
		if (Constant.CODE_SUCCESS == resultData.getCode())
		{
			// 获取二维码成功
			paymentState = SysDictionary.THIRD_PAY_STATUS_PAYING;
			operateState = SysDictionary.CUSTOMER_OPERATE_STATE_WAIT;
			requestStatus = SysDictionary.REQUEST_STATUS_COMPLETE;

			resultJson = JSONObject.fromObject(resultData.getData().get(0));
			resultJson.put("bill_code", billCode);
			resultJson.put("payment_state", paymentState);
			resultJson.put("operate_state", operateState);
		}
		else if (Constant.CODE_CONN_EXCEPTION == resultData.getCode())
		{
			// 网络异常,获取二维码网络异常后,已自动取消交易
			paymentState = SysDictionary.THIRD_PAY_STATUS_NOOK;
			operateState = SysDictionary.CUSTOMER_OPERATE_STATE_FAIL;
			requestStatus = SysDictionary.REQUEST_STATUS_FAILURE;
			requestCode = resultData.getCode();
			requestMsg = resultData.getMsg();
			finishTime = DateUtil.currentTimestamp();

			resultJson = new JSONObject();
			resultJson.put("bill_code", billCode);
			resultJson.put("payment_state", paymentState);
			resultJson.put("operate_state", operateState);
		}
		else
		{
			// 获取二维码失败
			paymentState = SysDictionary.THIRD_PAY_STATUS_FAIL;
			operateState = SysDictionary.CUSTOMER_OPERATE_STATE_FAIL;
			requestStatus = SysDictionary.REQUEST_STATUS_COMPLETE;
			requestCode = resultData.getCode();
			requestMsg = resultData.getMsg();
			finishTime = DateUtil.currentTimestamp();

			resultJson = new JSONObject();
			resultJson.put("bill_code", billCode);
			resultJson.put("payment_state", paymentState);
			resultJson.put("operate_state", operateState);
		}

		// 修改会员操作状态
		customerDao.updatePosCustomerOperateList(tenantId, storeId, billCode, paymentState, operateState, requestStatus, requestCode, requestMsg, finishTime, SysDictionary.OPERAT_TYPE_CZ);

		List<JSONObject> resultList = new ArrayList<JSONObject>();
		resultList.add(resultJson);

		resultData.setType(Type.CUSTOMER_CARD_RECHARGE);
		resultData.setOper(Oper.precreate);
		resultData.setData(resultList);
		return resultData;
	}

	@Override
	public Data barcodePaymentForRecharge(String tenantId, Integer storeId, String billCode, JSONObject paramJson) throws Exception
	{
		// TODO Auto-generated method stub
		Double settleAmount = ParamUtil.getDoubleValueByObject(paramJson, "income", false, null);

		JSONObject thirdParamJson = new JSONObject();
		thirdParamJson.put("order_no", billCode);
		thirdParamJson.put("order_num", billCode);
		thirdParamJson.put("bill_num", billCode);
		thirdParamJson.put("service_type", SysDictionary.SERVICE_TYPE_RECHARGE);
		thirdParamJson.put("report_date", ParamUtil.getStringValueByObject(paramJson, "report_date"));
		thirdParamJson.put("shift_id", ParamUtil.getIntegerValueByObject(paramJson, "shift_id"));
		thirdParamJson.put("pos_num", ParamUtil.getStringValueByObject(paramJson, "pos_num"));
		thirdParamJson.put("opt_num", ParamUtil.getStringValueByObject(paramJson, "opt_num"));
		thirdParamJson.put("channel", ParamUtil.getStringValueByObject(paramJson, "chanel"));
		thirdParamJson.put("payment_id", ParamUtil.getIntegerValueByObject(paramJson, "payment"));
		thirdParamJson.put("total_amount", settleAmount);
		thirdParamJson.put("settle_amount", settleAmount);
		thirdParamJson.put("currency_name", ParamUtil.getStringValueByObject(paramJson, "currency_name"));
		thirdParamJson.put("client_ip", ParamUtil.getStringValueByObject(paramJson, "client_ip"));
		thirdParamJson.put("body", ParamUtil.getStringValueByObject(paramJson, "body"));
		thirdParamJson.put("description", ParamUtil.getStringValueByObject(paramJson, "description"));
		thirdParamJson.put("extra", ParamUtil.getStringValueByObject(paramJson, "extra"));
		thirdParamJson.put("credential", ParamUtil.getStringValueByObject(paramJson, "credential"));
		thirdParamJson.put("oper_type", Type.CUSTOMER_CARD_RECHARGE.name());
		thirdParamJson.put("subject", "会员卡充值");

		Data resultData = thirdPaymentService.barcodePayment(tenantId, storeId, thirdParamJson);

		JSONObject resultJson = null;
		String paymentState = null;
		String operateState = null;
		String requestStatus = null;
		Integer requestCode = null;
		String requestMsg = null;
		Timestamp finishTime = null;
		if (Constant.CODE_SUCCESS == resultData.getCode())
		{
			// 支付成功
			resultJson = JSONObject.fromObject(resultData.getData().get(0));

			if (SysDictionary.THIRD_PAY_STATUS_SUCCESS.equals(resultJson.optString("payment_state")))
			{
				paymentState = SysDictionary.THIRD_PAY_STATUS_SUCCESS;
				operateState = SysDictionary.CUSTOMER_OPERATE_STATE_PROCESS;
				requestStatus = SysDictionary.REQUEST_STATUS_COMPLETE;
			}
			else if(SysDictionary.THIRD_PAY_STATUS_FAIL.equals(resultJson.optString("payment_state")))
			{
				paymentState = SysDictionary.THIRD_PAY_STATUS_FAIL;
				operateState = SysDictionary.CUSTOMER_OPERATE_STATE_FAIL;
				requestStatus = SysDictionary.REQUEST_STATUS_COMPLETE;
				requestMsg = resultJson.optString("failure_msg");
				requestCode = PosErrorCode.THIRD_PAYMENT_FAILURE.getNumber();
				
				resultData.setCode(requestCode);
				resultData.setMsg(requestMsg);
			}
			else
			{
				paymentState = SysDictionary.THIRD_PAY_STATUS_PAYING;
				operateState = SysDictionary.CUSTOMER_OPERATE_STATE_WAIT;
				requestStatus = SysDictionary.REQUEST_STATUS_COMPLETE;
				requestMsg = resultJson.optString("failure_msg");
			}

			resultJson.put("bill_code", billCode);
			resultJson.put("payment_state", paymentState);
			resultJson.put("operate_state", operateState);
		}
		else if (Constant.CODE_CONN_EXCEPTION == resultData.getCode())
		{
			// 网络异常,支付网络异常后,已自动取消交易
			paymentState = SysDictionary.THIRD_PAY_STATUS_NOOK;
			operateState = SysDictionary.CUSTOMER_OPERATE_STATE_WAIT;
			requestStatus = SysDictionary.REQUEST_STATUS_FAILURE;
			requestCode = resultData.getCode();
			requestMsg = resultData.getMsg();

			resultJson = new JSONObject();
			resultJson.put("bill_code", billCode);
			resultJson.put("payment_state", paymentState);
			resultJson.put("operate_state", operateState);
		}
		else
		{
			// 支付失败
			paymentState = SysDictionary.THIRD_PAY_STATUS_FAIL;
			operateState = SysDictionary.CUSTOMER_OPERATE_STATE_FAIL;
			requestStatus = SysDictionary.REQUEST_STATUS_COMPLETE;
			requestCode = resultData.getCode();
			requestMsg = resultData.getMsg();
			finishTime = DateUtil.currentTimestamp();

			resultJson = new JSONObject();
			resultJson.put("bill_code", billCode);
			resultJson.put("payment_state", paymentState);
			resultJson.put("operate_state", operateState);
		}

		// 修改会员操作状态
		customerDao.updatePosCustomerOperateList(tenantId, storeId, billCode, paymentState, operateState, requestStatus, requestCode, requestMsg, finishTime, SysDictionary.OPERAT_TYPE_CZ);

		List<JSONObject> resultList = new ArrayList<JSONObject>();
		resultList.add(resultJson);

		resultData.setType(Type.CUSTOMER_CARD_RECHARGE);
		resultData.setOper(Oper.barcode);
		resultData.setData(resultList);

		return resultData;
	}

	@Override
	public Data customerCardRecharge(String tenantId, Integer storeId, String thirdBillCode, JSONObject paramJson) throws Exception
	{
		// TODO Auto-generated method stub
		Double income = ParamUtil.getDoubleValueByObject(paramJson, "income", false, null);
		String isInvoice = ParamUtil.getStringValueByObject(paramJson, "is_invoice", false, null);
		Integer operatorId = ParamUtil.getIntegerValueByObject(paramJson, "opt_num", false, null);
		String operator = customerDao.getEmpNameById(String.valueOf(operatorId), tenantId, storeId);
		Integer paymentId = ParamUtil.getIntegerValueByObject(paramJson, "payment");

		synchronized (thirdBillCode.intern())
		{
			Timestamp currentTime = DateUtil.currentTimestamp();
			
			PosCustomerOperateListEntity  customerOperate = customerDao.queryPosCustomerOperateListByBillcode(tenantId, storeId, null, null, null, null, null, null, thirdBillCode, null, SysDictionary.OPERAT_TYPE_CZ);

			if(SysDictionary.CUSTOMER_OPERATE_CANCEL_STATE_Y.equals(customerOperate.getCancel_state()))
			{
				Data responseData = Data.get();
				responseData.setCode(Constant.CODE_INNER_EXCEPTION);
				responseData.setMsg("充值已取消");
				return responseData;
			}
			
			JSONObject requestJson = new JSONObject();
			requestJson.put("card_code", ParamUtil.getStringValueByObject(paramJson, "card_code"));
			requestJson.put("third_code", ParamUtil.getStringValueByObject(paramJson, "third_code"));
			requestJson.put("bill_code", thirdBillCode);
			requestJson.put("batch_num", "");
			requestJson.put("business_date", ParamUtil.getStringValueByObject(paramJson, "report_date"));
			requestJson.put("shift_id", ParamUtil.getIntegerValueByObject(paramJson, "shift_id"));
			requestJson.put("chanel", ParamUtil.getStringValueByObject(paramJson, "chanel"));
			requestJson.put("payment", paymentId);
			requestJson.put("income", income);
			requestJson.put("pay_no", ParamUtil.getStringValueByObject(paramJson, "pay_no"));
			requestJson.put("salesman", ParamUtil.getStringValueByObject(paramJson, "salesman"));
			requestJson.put("reward_money", ParamUtil.getDoubleValueByObject(paramJson, "reward_money"));
			requestJson.put("auto_reward_money", ParamUtil.getStringValueByObject(paramJson, "auto_reward_money"));
			requestJson.put("operator", operator);
			requestJson.put("operator_id", operatorId);
			requestJson.put("updatetime", DateUtil.format(currentTime));
	
			List<JSONObject> requestList = new ArrayList<JSONObject>();
			requestList.add(requestJson);
	
			Data requestData = Data.get(tenantId, storeId, Constant.CODE_SUCCESS);
			requestData.setType(Type.CUSTOMER_CARD_RECHARGE);
			requestData.setOper(Oper.add);
			requestData.setData(requestList);
			JSONObject jsobj = JSONObject.fromObject(requestData);
	
			Data responseData = Data.get();
			this.commonPost(jsobj.toString(), responseData);
	
			String paymentState = SysDictionary.THIRD_PAY_STATUS_SUCCESS;
			String operateState = SysDictionary.REQUEST_STATUS_ING;
			String requestStatus = SysDictionary.REQUEST_STATUS_ING;
			Integer requestCode = null;
			String requestMsg = null;
			Timestamp finishTime = null;
			String billCode = null;
			
			JSONObject resultObj = new JSONObject();
			JSONObject extendParamJson = null;
			if(Tools.hv(customerOperate.getExtend_param()))
			{
				extendParamJson = JSONObject.fromObject(customerOperate.getExtend_param());
			}
			if (responseData.isSuccess())
			{
				operateState = SysDictionary.CUSTOMER_OPERATE_STATE_SUCCESS;
				requestStatus = SysDictionary.REQUEST_STATUS_COMPLETE;
				finishTime = currentTime;
				Double invoiceAmount = 0d;
				if ("1".equals(isInvoice))
				{
					JSONObject paymentWayJson = customerDao.getPaymentWayByID(tenantId, storeId, paymentId);
					if ("1".equals(paymentWayJson.optString("if_invoicing")))
					{
						invoiceAmount = income;
					}
				}
	
				resultObj = JSONObject.fromObject(responseData.getData().get(0));
	
				JSONObject cardTradingJson = new JSONObject();
				JSONArray resultCardTradingList = resultObj.optJSONArray("crm_card_trading_list");
				if (null != resultCardTradingList && resultCardTradingList.size() > 0)
				{
					cardTradingJson = resultCardTradingList.getJSONObject(0);
				}
	
				billCode = ParamUtil.getStringValueByObject(resultObj, "bill_code");
				Integer cardId = ParamUtil.getIntegerValueByObject(cardTradingJson, "card_id");
				Timestamp lastUpdatetime = ParamUtil.getTimestampValueByObject(cardTradingJson, "last_updatetime");
				Timestamp storeUpdatetime = ParamUtil.getTimestampValueByObject(cardTradingJson, "store_updatetime");
	
				CrmCardTradingListEntity cardTradingList = new CrmCardTradingListEntity();
	
				cardTradingList.setThird_bill_code(thirdBillCode);
				cardTradingList.setBill_code(billCode);
				cardTradingList.setCard_id(cardId);
				cardTradingList.setCard_code(ParamUtil.getStringValueByObject(resultObj, "card_code"));
				cardTradingList.setCard_class_id(ParamUtil.getIntegerValueByObject(resultObj, "card_class_id"));
				cardTradingList.setName(ParamUtil.getStringValueByObject(resultObj, "name"));
				cardTradingList.setMobil(ParamUtil.getStringValueByObject(resultObj, "mobil"));
				cardTradingList.setBusiness_date(ParamUtil.getDateValueByObject(cardTradingJson, "business_date"));
				cardTradingList.setShift_id(ParamUtil.getIntegerValueByObject(cardTradingJson, "shift_id"));
				cardTradingList.setChanel(ParamUtil.getStringValueByObject(cardTradingJson, "chanel"));
				cardTradingList.setOperat_type(ParamUtil.getStringValueByObject(cardTradingJson, "operat_type"));
				cardTradingList.setBill_money(ParamUtil.getDoubleValueByObject(cardTradingJson, "bill_money"));
				cardTradingList.setMain_trading(ParamUtil.getDoubleValueByObject(cardTradingJson, "main_trading"));
				cardTradingList.setReward_trading(ParamUtil.getDoubleValueByObject(cardTradingJson, "reward_trading"));
				cardTradingList.setRevoked_trading(ParamUtil.getDoubleValueByObject(cardTradingJson, "revoked_trading"));
				cardTradingList.setMain_original(ParamUtil.getDoubleValueByObject(cardTradingJson, "main_original"));
				cardTradingList.setReward_original(ParamUtil.getDoubleValueByObject(cardTradingJson, "reward_original"));
				cardTradingList.setTotal_balance(ParamUtil.getDoubleValueByObject(cardTradingJson, "total_balance"));
				cardTradingList.setReward_balance(ParamUtil.getDoubleValueByObject(cardTradingJson, "reward_balance"));
				cardTradingList.setMain_balance(ParamUtil.getDoubleValueByObject(cardTradingJson, "main_balance"));
				cardTradingList.setDeposit(ParamUtil.getDoubleValueByObject(cardTradingJson, "deposit"));
				cardTradingList.setActivity_id(ParamUtil.getIntegerValueByObject(cardTradingJson, "activity_id"));
				cardTradingList.setCustomer_id(ParamUtil.getIntegerValueByObject(cardTradingJson, "customer_id"));
				cardTradingList.setSalesman(ParamUtil.getStringValueByObject(cardTradingJson, "salesman"));
				cardTradingList.setOperate_time(ParamUtil.getTimestampValueByObject(cardTradingJson, "operate_time"));
				cardTradingList.setLast_updatetime(lastUpdatetime);
				cardTradingList.setStore_updatetime(storeUpdatetime);
				cardTradingList.setOperator_id(operatorId);
				cardTradingList.setOperator(operator);
				cardTradingList.setInvoice_balance(invoiceAmount);
				cardTradingList.setIs_invoice(isInvoice);
				cardTradingList.setPayment_state(paymentState);
				cardTradingList.setRecharge_state(operateState);
				cardTradingList.setRequest_status(requestStatus);
				cardTradingList.setBill_code_original(null);
				cardTradingList.setBatch_num(null);
				cardTradingList.setCommission_saler_money(0d);
				cardTradingList.setCommission_store_money(0d);
				cardTradingList.setPay_type(null);
				cardTradingList.setPosNum(ParamUtil.getStringValueByObject(cardTradingJson, "pos_num"));
	
				customerDao.insertCrmCardTradingList(tenantId, storeId, cardTradingList);
	
				CrmCardPaymentListEntity cardPaymentList = new CrmCardPaymentListEntity();
				cardPaymentList.setBill_code(billCode);
				cardPaymentList.setThird_bill_code(thirdBillCode);
				cardPaymentList.setCard_id(cardId);
				cardPaymentList.setPayment_id(ParamUtil.getIntegerValueByObject(cardTradingJson, "payment_id"));
				cardPaymentList.setPay_money(ParamUtil.getDoubleValueByObject(cardTradingJson, "pay_money"));
				cardPaymentList.setLocal_currency(ParamUtil.getDoubleValueByObject(cardTradingJson, "local_currency"));
				cardPaymentList.setPay_no(ParamUtil.getStringValueByObject(resultObj, "pay_no"));
				cardPaymentList.setRexchange_rate(ParamUtil.getDoubleValueByObject(cardTradingJson, "rexchange_rate"));
				cardPaymentList.setStore_updatetime(storeUpdatetime);
				cardPaymentList.setLast_updatetime(lastUpdatetime);
	
				customerDao.insertCrmCardPaymentList(tenantId, storeId, cardPaymentList);
				
				if(null ==extendParamJson)
				{
					extendParamJson = new JSONObject();
				}
				extendParamJson.put("level_id", ParamUtil.getStringValueByObject(resultObj, "level_id"));
				extendParamJson.put("level_name", ParamUtil.getStringValueByObject(resultObj, "level_name"));
				extendParamJson.put("card_class_name", ParamUtil.getStringValueByObject(resultObj, "card_class_name"));
				extendParamJson.put("useful_credit", ParamUtil.getDoubleValueByObject(resultObj, "useful_credit"));
				extendParamJson.put("reward_credit", ParamUtil.getDoubleValueByObject(resultObj, "reward_credit"));
			}
			else if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
			{
				operateState = SysDictionary.CUSTOMER_OPERATE_STATE_PROCESS;
				requestStatus = SysDictionary.REQUEST_STATUS_FAILURE;
				requestCode = responseData.getCode();
				requestMsg = responseData.getMsg();
			}
			else
			{
				operateState = SysDictionary.CUSTOMER_OPERATE_STATE_FAIL;
				requestStatus = SysDictionary.REQUEST_STATUS_COMPLETE;
				requestCode = responseData.getCode();
				requestMsg = responseData.getMsg();
				finishTime = currentTime;
			}
	
			customerDao.updatePosCustomerOperateList(tenantId, storeId, thirdBillCode, paymentState, operateState, requestStatus, requestCode, requestMsg, finishTime, SysDictionary.OPERAT_TYPE_CZ, billCode, (null != extendParamJson ? extendParamJson.toString() : null));
	
			resultObj.put("operate_state", operateState);
			resultObj.put("payment_state", paymentState);
			resultObj.put("payment_id", paymentId);
	
			List<JSONObject> resultList = new ArrayList<JSONObject>();
			resultList.add(resultObj);
	
			responseData.setType(Type.CUSTOMER_CARD_RECHARGE);
			responseData.setOper(Oper.add);
			responseData.setData(resultList);
	
			return responseData;
		}
	}

	@Override
	public PosCustomerOperateListEntity queryCustomerCardRecharge(String tenantId, Integer storeId, JSONObject paramJson) throws Exception
	{
		// TODO Auto-generated method stub
		return customerDao.queryPosCustomerOperateListByBillcode(tenantId, storeId, paramJson);
	}

	@Override
	public JSONObject queryCustomerCardRechargeForDetails(String tenantId, Integer storeId, PosCustomerOperateListEntity cardRecharge) throws Exception
	{
		// TODO Auto-generated method stub
		// 根据bill_code查询crm_card_trading_list,crm_card_payment_list数据,组织数据返回
		// 交易单号
		String billCode = cardRecharge.getThird_bill_code();

		StringBuffer detailsSql = new StringBuffer();
		detailsSql.append(" select pcol.card_code, pcol.bill_code, coalesce(pcol.customer_name,cctl.name) as name, coalesce(pcol.mobil,cctl.mobil) as mobil, coalesce(pcol.card_class_id,cctl.card_class_id) as card_class_id, pcol.operate_state, cctl.main_trading, cctl.reward_trading, cctl.main_balance, cctl.reward_balance, cctl.total_balance, cctl.chanel, ccpl.payment_id, ccpl.pay_money as income,ccpl.last_updatetime as updatetime,pcol.operator_id,cctl.operator,cctl.shift_id,ccpl.pay_no,pcol.extend_param ");
		detailsSql.append(" from pos_customer_operate_list pcol left join crm_card_trading_list cctl on pcol.third_bill_code = cctl.third_bill_code and pcol.tenancy_id = cctl.tenancy_id and pcol.store_id = cctl.store_id ");
		detailsSql.append(" left join crm_card_payment_list ccpl on pcol.bill_code = ccpl.bill_code and pcol.tenancy_id = ccpl.tenancy_id ");
		detailsSql.append(" where pcol.third_bill_code = ? and pcol.operate_state = '" + SysDictionary.CUSTOMER_OPERATE_STATE_SUCCESS + "' and pcol.tenancy_id = ? and pcol.store_id = ? ");

		List<JSONObject> resultList = customerDao.query4Json(tenantId, detailsSql.toString(), new Object[]
		{ billCode, tenantId, storeId });

		JSONObject resultJson = new JSONObject();
		if (resultList.size() > 0)
		{
			resultJson = resultList.get(0);
			JSONObject extendParamJson = ParamUtil.getJSONObject(resultJson, "extend_param");
			resultJson.remove("extend_param");
			
			resultJson.put("useful_credit", ParamUtil.getDoubleValueByObject(extendParamJson, "useful_credit")); // 可用积分
			resultJson.put("reward_credit", ParamUtil.getDoubleValueByObject(extendParamJson, "reward_credit")); // 奖励积分
			resultJson.put("level_id", extendParamJson.optString("level_id")); // 会员等级id
			resultJson.put("level_name", extendParamJson.optString("level_name")); // 会员等级名称
			resultJson.put("card_class_name", extendParamJson.optString("card_class_name")); // 卡类别
			// 赠送优惠券
			List<JSONObject> coupons = new ArrayList<>();
//			JSONObject coupon = new JSONObject();
//			coupon.put("coupons_code", ""); // 优惠券券号
//			coupon.put("subject", ""); // 活动名称
//			coupon.put("face_value", 0d); // 面值
//			coupons.add(coupon);
			resultJson.put("reward_coupon", coupons);
		}
		return resultJson;
	}

	@Override
	public Data cancelThirdPaymentForCardRecharge(String tenantId, Integer storeId,String thirdBillCode, PosCustomerOperateListEntity cardRecharge) throws Exception
	{
		if (SysDictionary.THIRD_PAY_STATUS_SUCCESS.equals(cardRecharge.getPayment_state()) || SysDictionary.THIRD_PAY_STATUS_PAYING.equals(cardRecharge.getPayment_state()) || SysDictionary.THIRD_PAY_STATUS_NOOK.equals(cardRecharge.getPayment_state()))
		{
			JSONObject paramJson = new JSONObject();
			paramJson.put("order_no", thirdBillCode);
			paramJson.put("order_num", thirdBillCode);
			paramJson.put("settle_amount", cardRecharge.getTrade_amount());
			paramJson.put("total_amount", cardRecharge.getTrade_amount());
			paramJson.put("payment_id", cardRecharge.getPayment_id());
			paramJson.put("service_type", SysDictionary.SERVICE_TYPE_RECHARGE);
			paramJson.put("channel", cardRecharge.getChanel());
			paramJson.put("report_date", DateUtil.formatDate(cardRecharge.getBusiness_date()));
			paramJson.put("shift_id", cardRecharge.getShift_id());
			paramJson.put("pos_num", cardRecharge.getPos_num());
			paramJson.put("opt_num", cardRecharge.getOperator_id());
			paramJson.put("client_ip", "");
			paramJson.put("currency_name", "");
			paramJson.put("oper_type", Type.CUSTOMER_CARD_RECHARGE.name());
			paramJson.put("refunds_order", "");

			Data resultData = thirdPaymentService.cancelPayment(tenantId, storeId, paramJson);
			return resultData;
		}
		else
		{
			return Data.get(tenantId, storeId, Constant.CODE_SUCCESS);
		}
	}

	@Override
	public Data cancelCustomerCardRecharge(String tenantId, Integer storeId, PosCustomerOperateListEntity cardRecharge) throws Exception
	{
		cardRecharge = customerDao.queryPosCustomerOperateListByBillcode(tenantId, storeId, null, null, null, null, null, null, cardRecharge.getThird_bill_code(), null, SysDictionary.OPERAT_TYPE_CZ);
		
		if (SysDictionary.CUSTOMER_OPERATE_STATE_SUCCESS.equals(cardRecharge.getOperate_state()))
		{
			// 充值成功,返回前端取消充值失败,
			Data resultData = Data.get();
			resultData.setCode(PosErrorCode.CARD_RECHARGE_COMPLETE_NOT_PERMIT_CANCEL_ERROR.getNumber());
			resultData.setMsg(PosErrorCode.CARD_RECHARGE_COMPLETE_NOT_PERMIT_CANCEL_ERROR.getMessage());
			return resultData;
		}
		
		String operator = customerDao.getEmpNameById(String.valueOf(cardRecharge.getOperator_id()), tenantId, storeId);

		Timestamp currentTime = DateUtil.currentTimestamp();

		cardRecharge.setCancel_state(SysDictionary.CUSTOMER_OPERATE_CANCEL_STATE_Y);
		cardRecharge.setFinish_time(currentTime);
		
		customerDao.updatePosCustomerOperateList(tenantId, storeId, cardRecharge);
		
		Data responseData = Data.get(tenantId,storeId,Constant.CODE_SUCCESS);
		if (SysDictionary.CUSTOMER_OPERATE_STATE_PROCESS.equals(cardRecharge.getOperate_state()))
		{
			JSONObject requestJson = new JSONObject();
			requestJson.put("card_code", cardRecharge.getCard_code());
			requestJson.put("third_code", cardRecharge.getThird_code());
			requestJson.put("old_bill_code", cardRecharge.getBill_code_original());
			requestJson.put("bill_code", cardRecharge.getThird_bill_code());
			requestJson.put("model", CARD_RECHARGE_CANCEL_MODEL);
			requestJson.put("business_date", DateUtil.formatDate(cardRecharge.getBusiness_date()));
			requestJson.put("shift_id", cardRecharge.getShift_id());
			requestJson.put("chanel", cardRecharge.getChanel());
			requestJson.put("pos_num", cardRecharge.getPos_num());
			requestJson.put("operator_id", cardRecharge.getOperator_id());
			requestJson.put("operator", operator);
			requestJson.put("updatetime", DateUtil.format(currentTime));

			List<JSONObject> requestList = new ArrayList<JSONObject>();
			requestList.add(requestJson);

			Data requestData = Data.get(tenantId, storeId, Constant.CODE_SUCCESS);

			requestData.setType(Type.CUSTOMER_CARD_RECHARGE);
			requestData.setOper(Oper.update);
			requestData.setData(requestList);

			JSONObject jsobj = JSONObject.fromObject(requestData);
			
			this.commonPost(jsobj.toString(), responseData);

			if (Constant.CODE_SUCCESS != responseData.getCode())
			{
				throw SystemException.getInstance(PosErrorCode.POS_REQUEST_CRMSERVICE_ERROR).set("{0}", "取消充值失败");
			}
		}
		
		return responseData;
	}

	@Override
	public Data revokeCustomerCardRecharge(String tenantId, Integer storeId, String thirdBillCode, JSONObject paramJson) throws Exception
	{
		// TODO Auto-generated method stub
		String cardCode = ParamUtil.getStringValueByObject(paramJson, "card_code", false, null);
		String thirdCode = ParamUtil.getStringValueByObject(paramJson, "third_code", false, null);
		String billCodeOriginal = ParamUtil.getStringValueByObject(paramJson, "old_bill_code", false, null);

		Integer operatorId = ParamUtil.getIntegerValueByObject(paramJson, "opt_num", false, null);

		String operator = customerDao.getEmpNameById(String.valueOf(operatorId), tenantId, storeId);

		Timestamp currentTime = DateUtil.currentTimestamp();

		JSONObject requestJson = new JSONObject();
		requestJson.put("card_code", cardCode);
		requestJson.put("third_code", thirdCode);
		requestJson.put("old_bill_code", billCodeOriginal);
		requestJson.put("bill_code", thirdBillCode);
		requestJson.put("model", CARD_RECHARGE_REVOKE_MODEL);
		requestJson.put("business_date", ParamUtil.getStringValueByObject(paramJson, "report_date"));
		requestJson.put("shift_id", ParamUtil.getIntegerValueByObject(paramJson, "shift_id"));
		requestJson.put("chanel", ParamUtil.getStringValueByObject(paramJson, "chanel"));
		requestJson.put("pos_num", ParamUtil.getStringValueByObject(paramJson, "pos_num"));
		requestJson.put("operator_id", operatorId);
		requestJson.put("operator", operator);
		requestJson.put("updatetime", DateUtil.format(currentTime));

		List<JSONObject> requestList = new ArrayList<JSONObject>();
		requestList.add(requestJson);

		Data requestData = Data.get(tenantId, storeId, Constant.CODE_SUCCESS);

		requestData.setType(Type.CUSTOMER_CARD_RECHARGE);
		requestData.setOper(Oper.update);
		requestData.setData(requestList);

		JSONObject jsobj = JSONObject.fromObject(requestData);
		Data responseData = Data.get();
		this.commonPost(jsobj.toString(), responseData);

		String paymentState = SysDictionary.THIRD_PAY_STATUS_SUCCESS;
		String operateState = SysDictionary.REQUEST_STATUS_ING;
		String requestStatus = SysDictionary.REQUEST_STATUS_ING;
		Integer requestCode = null;
		String requestMsg = null;
		Timestamp finishTime = null;
		Integer paymentId=null;
		String paymentClass =null;

		JSONObject resultObj = new JSONObject();
		if (responseData.isSuccess())
		{
			operateState = SysDictionary.CUSTOMER_OPERATE_STATE_SUCCESS;
			requestStatus = SysDictionary.REQUEST_STATUS_COMPLETE;
			finishTime = currentTime;

			resultObj = JSONObject.fromObject(responseData.getData().get(0));

			JSONObject cardTradingJson = new JSONObject();
			JSONArray resultCardTradingList = resultObj.optJSONArray("crm_card_trading_list");
			if (null != resultCardTradingList && resultCardTradingList.size() > 0)
			{
				cardTradingJson = resultCardTradingList.getJSONObject(0);
			}

			paymentId = ParamUtil.getIntegerValueByObject(cardTradingJson, "payment_id");
			if(null !=paymentId &&paymentId>0)
			{
				JSONObject paymentWayJson = customerDao.getPaymentWayByID(tenantId, storeId, paymentId);
				paymentClass = ParamUtil.getStringValueByObject(paymentWayJson, "payment_class");
			}
			
			String billCode = ParamUtil.getStringValueByObject(resultObj, "bill_code");
			Integer cardId = ParamUtil.getIntegerValueByObject(cardTradingJson, "card_id");
			Timestamp lastUpdatetime = ParamUtil.getTimestampValueByObject(cardTradingJson, "last_updatetime");
			Timestamp storeUpdatetime = ParamUtil.getTimestampValueByObject(cardTradingJson, "store_updatetime");

			CrmCardTradingListEntity cardTradingList = new CrmCardTradingListEntity();

			cardTradingList.setThird_bill_code(thirdBillCode);
			cardTradingList.setBill_code(billCode);
			cardTradingList.setCard_id(cardId);
			cardTradingList.setCard_code(ParamUtil.getStringValueByObject(resultObj, "card_code"));
			cardTradingList.setCard_class_id(ParamUtil.getIntegerValueByObject(resultObj, "card_class_id"));
			cardTradingList.setName(ParamUtil.getStringValueByObject(resultObj, "name"));
			cardTradingList.setMobil(ParamUtil.getStringValueByObject(resultObj, "mobil"));
			cardTradingList.setBusiness_date(ParamUtil.getDateValueByObject(cardTradingJson, "business_date"));
			cardTradingList.setShift_id(ParamUtil.getIntegerValueByObject(cardTradingJson, "shift_id"));
			cardTradingList.setChanel(ParamUtil.getStringValueByObject(cardTradingJson, "chanel"));
			cardTradingList.setOperat_type(ParamUtil.getStringValueByObject(cardTradingJson, "operat_type"));
			cardTradingList.setBill_money(ParamUtil.getDoubleValueByObject(cardTradingJson, "bill_money"));
			cardTradingList.setMain_trading(ParamUtil.getDoubleValueByObject(cardTradingJson, "main_trading"));
			cardTradingList.setReward_trading(ParamUtil.getDoubleValueByObject(cardTradingJson, "reward_trading"));
			cardTradingList.setRevoked_trading(ParamUtil.getDoubleValueByObject(cardTradingJson, "revoked_trading"));
			cardTradingList.setMain_original(ParamUtil.getDoubleValueByObject(cardTradingJson, "main_original"));
			cardTradingList.setReward_original(ParamUtil.getDoubleValueByObject(cardTradingJson, "reward_original"));
			cardTradingList.setTotal_balance(ParamUtil.getDoubleValueByObject(cardTradingJson, "total_balance"));
			cardTradingList.setReward_balance(ParamUtil.getDoubleValueByObject(cardTradingJson, "reward_balance"));
			cardTradingList.setMain_balance(ParamUtil.getDoubleValueByObject(cardTradingJson, "main_balance"));
			cardTradingList.setDeposit(ParamUtil.getDoubleValueByObject(cardTradingJson, "deposit"));
			cardTradingList.setActivity_id(ParamUtil.getIntegerValueByObject(cardTradingJson, "activity_id"));
			cardTradingList.setCustomer_id(ParamUtil.getIntegerValueByObject(cardTradingJson, "customer_id"));
			cardTradingList.setSalesman(ParamUtil.getStringValueByObject(cardTradingJson, "salesman"));
			cardTradingList.setOperate_time(ParamUtil.getTimestampValueByObject(cardTradingJson, "operate_time"));
			cardTradingList.setBill_code_original(ParamUtil.getStringValueByObject(cardTradingJson, "bill_code_original"));
			cardTradingList.setLast_updatetime(lastUpdatetime);
			cardTradingList.setStore_updatetime(storeUpdatetime);
			cardTradingList.setOperator_id(operatorId);
			cardTradingList.setOperator(operator);
			cardTradingList.setInvoice_balance(0d);
			cardTradingList.setIs_invoice("0");
			cardTradingList.setPayment_state(paymentState);
			cardTradingList.setRecharge_state(operateState);
			cardTradingList.setRequest_status(requestStatus);
			cardTradingList.setBill_code_original(null);
			cardTradingList.setBatch_num(null);
			cardTradingList.setCommission_saler_money(0d);
			cardTradingList.setCommission_store_money(0d);
			cardTradingList.setPay_type(paymentClass);
			cardTradingList.setPosNum(ParamUtil.getStringValueByObject(paramJson, "pos_num"));

			customerDao.insertCrmCardTradingList(tenantId, storeId, cardTradingList);

			CrmCardPaymentListEntity cardPaymentList = new CrmCardPaymentListEntity();
			cardPaymentList.setBill_code(billCode);
			cardPaymentList.setThird_bill_code(thirdBillCode);
			cardPaymentList.setCard_id(cardId);
			cardPaymentList.setPayment_id(paymentId);
			cardPaymentList.setPay_money(ParamUtil.getDoubleValueByObject(cardTradingJson, "pay_money"));
			cardPaymentList.setLocal_currency(ParamUtil.getDoubleValueByObject(cardTradingJson, "local_currency"));
			cardPaymentList.setPay_no(ParamUtil.getStringValueByObject(resultObj, "pay_no"));
			cardPaymentList.setRexchange_rate(ParamUtil.getDoubleValueByObject(cardTradingJson, "rexchange_rate"));
			cardPaymentList.setStore_updatetime(storeUpdatetime);
			cardPaymentList.setLast_updatetime(lastUpdatetime);
			cardPaymentList.setLast_updatetime(lastUpdatetime);

			customerDao.insertCrmCardPaymentList(tenantId, storeId, cardPaymentList);

			resultObj.put("income", ParamUtil.getDoubleValueByObject(cardTradingJson, "pay_money"));
		}
		else if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
		{
			operateState = SysDictionary.CUSTOMER_OPERATE_STATE_PROCESS;
			requestStatus = SysDictionary.REQUEST_STATUS_FAILURE;
			requestCode = responseData.getCode();
			requestMsg = responseData.getMsg();
		}
		else
		{
			operateState = SysDictionary.CUSTOMER_OPERATE_STATE_FAIL;
			requestStatus = SysDictionary.REQUEST_STATUS_COMPLETE;
			requestCode = responseData.getCode();
			requestMsg = responseData.getMsg();
			finishTime = currentTime;
		}

		customerDao.updatePosCustomerOperateList(tenantId, storeId, thirdBillCode, paymentState, operateState, requestStatus, requestCode, requestMsg, finishTime, SysDictionary.OPERAT_TYPE_FCZ,paymentId,paymentClass);

		resultObj.put("operate_state", operateState);
		resultObj.put("payment_state", paymentState);
		resultObj.put("payment_id", paymentId);

		List<JSONObject> resultList = new ArrayList<JSONObject>();
		resultList.add(resultObj);

		responseData.setType(Type.CUSTOMER_CARD_RECHARGE);
		responseData.setOper(Oper.update);
		responseData.setData(resultList);

		return responseData;
	}

	@Override
	public JSONObject createElectricInvoice(String tenantId, Integer storeId, JSONObject paramJson) throws Exception
	{
		// TODO Auto-generated method stub
		logger.info("电子发票二维码生成开始!");
		String isInvoice = ParamUtil.getStringValueByObject(paramJson, "is_invoice");
		String billCode = ParamUtil.getStringValueByObject(paramJson, "bill_code");
		String businessDate = ParamUtil.getStringValueByObject(paramJson, "business_date");
		Double invoiceAmount = 0d;

		String operateTime = DateUtil.getNowDateYYDDMMHHMMSS();

		// 再次判断开具发票状态 1：开电子发票 1以外：不开电子发票
		JSONObject printInvJo = new JSONObject();
		if ("1".equals(isInvoice)) // 开具电子发票
		{
			// 获取法人信息
			JSONObject legalPerInfoJo = customerDao.getCrmLegalPerInfo(tenantId, storeId, null);

			Double taxRate = 0d;
			if (!(Tools.isNullOrEmpty(legalPerInfoJo)))
			{
				taxRate = ParamUtil.getDoubleValueByObject(legalPerInfoJo, "tax_rate");
			}

			if (taxRate > 0)
			{
				// 计算开票金额
				JSONObject invoiceAmountParamJson = new JSONObject();
				invoiceAmountParamJson.put("bill_code", billCode);
				List<JSONObject> invoicePaymentList = customerDao.getCrmInvoicePayment(tenantId, storeId, invoiceAmountParamJson);
				invoiceAmount = invoicePaymentList.get(0).optDouble("pay_money");
				if (Double.isNaN(invoiceAmount))
				{
					invoiceAmount = 0d;
				}

				if (invoiceAmount > 0)
				{
					// 生成电子发票，获得电子发票的打印信息
					try
					{
						JSONObject invoiceParamJson = new JSONObject();
						invoiceParamJson.put("business_date", businessDate);
						invoiceParamJson.put("bill_code", billCode);
						invoiceParamJson.put("invoice_amount", invoiceAmount);
						invoiceParamJson.put("tax_rate", taxRate);
						invoiceParamJson.put("operateTime", operateTime);

						logger.info("电子发票二维码生成开始：传入参数：" + invoiceParamJson.toString());
						printInvJo = this.getPrintInvoiceInfo(tenantId, storeId, invoiceParamJson);
					}
					catch (Exception e)
					{
						e.printStackTrace();
						logger.error("电子发票二维码生成失败:", e);
					}
				}
				else
				{
					isInvoice = "0";
					logger.info("电子发票金额为0!");
				}
			}
			else
			{
				isInvoice = "0";
				logger.info("法人信息设置错误!");
			}
		}
		logger.info("电子发票二维码生成结束!");
		return printInvJo;
	}

	@Override
	public Data cancelElectricInvoice(String tenantId, Integer storeId, JSONObject paramJson) throws Exception
	{
		// TODO Auto-generated method stub
		String billCodeOriginal = paramJson.optString("bill_code_original");

		JSONObject invoiceJo = new JSONObject();
		invoiceJo.put("bill_code", billCodeOriginal);
		int invoiceRecord = customerDao.getCrmInvoice(tenantId, storeId, invoiceJo);
		if (invoiceRecord > 0) // 开具过电子发票
		{
			logger.info("取消电子发票开始");
			// 取消电子发票CANCLE_LECTRONIC_INVOICE
			String postUrl = getPequestUrl(CustomerServiceImp.INVOICE_POST_URL);

			JSONObject obj = new JSONObject();
			obj.put("DH", billCodeOriginal);
			List<JSONObject> jsonList = new ArrayList<JSONObject>();
			jsonList.add(obj);

			Data data = Data.get();
			data.setType(Type.CANCLE_LECTRONIC_INVOICE);
			data.setTenancy_id(tenantId);
			data.setStore_id(storeId);
			data.setData(jsonList);

			logger.info("取消电子发票  请求地址: " + postUrl + "      请求参数: " + JSONObject.fromObject(data).toString());
			String responseResult = HttpUtil.sendPostRequest(postUrl, JSONObject.fromObject(data).toString());
			if (responseResult != null)
			{
				JSONObject json = JSONObject.fromObject(responseResult);
				boolean success = json.optBoolean("success");
				if (!success)
				{
					logger.info("取消电子发票失败");
					throw SystemException.getInstance(PosErrorCode.UPDATE_PASSWORD_ERROR);
				}
			}
			logger.info("取消电子发票成功！");
		}

		return null;
	}

	@Override
	public Data queryCustomerCardRechargeToCrm(String tenantId, Integer storeId, String operType, PosCustomerOperateListEntity customerOperate, JSONObject printJson) throws Exception
	{
		Data resultData = Data.get(tenantId,storeId,Constant.CODE_SUCCESS);
		
		//Timestamp currentTime = DateUtil.currentTimestamp();
		JSONObject requestJson = new JSONObject();
		requestJson.put("card_code", customerOperate.getCard_code());
		requestJson.put("third_code", customerOperate.getThird_code());
		requestJson.put("third_bill_code", customerOperate.getThird_bill_code());
		requestJson.put("operat_type", operType);
		requestJson.put("batch_num", "");
		
		List<JSONObject> requestList = new ArrayList<JSONObject>();
		requestList.add(requestJson);
		
		Data requestData = Data.get(tenantId, storeId, Constant.CODE_SUCCESS);
		requestData.setType(Type.CUSTOMER_BILL);
		requestData.setOper(Oper.load);
		requestData.setData(requestList);
		
		Data responseData = new Data();
		this.commonPost(JSONObject.fromObject(requestData).toString(), responseData);
		
		String paymentState = customerOperate.getPayment_state();
		String operateState = customerOperate.getOperate_state();
		String requestStatus = SysDictionary.REQUEST_STATUS_ING;
		Integer requestCode = null;
		String requestMsg = null;
		Timestamp finishTime = null;
		String billCode = null;
		JSONObject extendParamJson = null;
		if (Tools.hv(customerOperate.getExtend_param()))
		{
			extendParamJson = JSONObject.fromObject(customerOperate.getExtend_param());
		}

		JSONObject cardTradingJson = null;
		JSONObject cardPaymentJson = null;
		
		if(Constant.CODE_SUCCESS==responseData.getCode())
		{
			requestStatus = SysDictionary.REQUEST_STATUS_COMPLETE;
			
			JSONObject resultObj = JSONObject.fromObject(responseData.getData().get(0));
			JSONArray resultCardTradingList = resultObj.optJSONArray("crm_card_trading_list");
			if (null != resultCardTradingList && resultCardTradingList.size() > 0)
			{
				cardTradingJson = resultCardTradingList.getJSONObject(0);
				if (!Tools.hv(cardTradingJson.optString("name")))
				{
					cardTradingJson.put("name", resultObj.optString("name"));
				}
				if (!Tools.hv(cardTradingJson.optString("mobil")))
				{
					cardTradingJson.put("mobil", resultObj.optString("mobil"));
				}
				if (!Tools.hv(cardTradingJson.optString("card_class_id")))
				{
					cardTradingJson.put("card_class_id", resultObj.optString("card_class_id"));
				}
			}
			JSONArray resultCardPymentList = resultObj.optJSONArray("crm_card_payment_list");
			if (null != resultCardPymentList && resultCardPymentList.size() > 0)
			{
				cardPaymentJson = resultCardPymentList.getJSONObject(0);
			}
			
			if(null ==extendParamJson)
			{
				extendParamJson = new JSONObject();
			}
			extendParamJson.put("level_id", ParamUtil.getStringValueByObject(resultObj, "level_id"));
			extendParamJson.put("level_name", ParamUtil.getStringValueByObject(resultObj, "level_name"));
			extendParamJson.put("card_class_name", ParamUtil.getStringValueByObject(resultObj, "card_class_name"));
			extendParamJson.put("useful_credit", ParamUtil.getDoubleValueByObject(resultObj, "useful_credit"));
			extendParamJson.put("reward_credit", ParamUtil.getDoubleValueByObject(resultObj, "reward_credit"));
		}
		else
		{
			requestStatus = SysDictionary.REQUEST_STATUS_FAILURE;
			requestCode = responseData.getCode();
			requestMsg = responseData.getMsg();
		}
		
		if (Tools.hv(cardTradingJson)&&Tools.hv(cardPaymentJson))
		{
			operateState = SysDictionary.CUSTOMER_OPERATE_STATE_SUCCESS;
			finishTime = DateUtil.currentTimestamp();

			billCode = ParamUtil.getStringValueByObject(cardTradingJson, "bill_code");
			Integer cardId = ParamUtil.getIntegerValueByObject(cardTradingJson, "card_id");
			Timestamp lastUpdatetime = ParamUtil.getTimestampValueByObject(cardTradingJson, "last_updatetime");
			Timestamp storeUpdatetime = ParamUtil.getTimestampValueByObject(cardTradingJson, "store_updatetime");

			CrmCardTradingListEntity cardTradingList = new CrmCardTradingListEntity();

			cardTradingList.setThird_bill_code(customerOperate.getThird_bill_code());
			cardTradingList.setBill_code(billCode);
			cardTradingList.setCard_id(cardId);
			cardTradingList.setCard_code(ParamUtil.getStringValueByObject(cardTradingJson, "card_code"));
			cardTradingList.setCard_class_id(ParamUtil.getIntegerValueByObject(cardTradingJson, "card_class_id"));
			cardTradingList.setName(ParamUtil.getStringValueByObject(cardTradingJson, "name"));
			cardTradingList.setMobil(ParamUtil.getStringValueByObject(cardTradingJson, "mobil"));
			cardTradingList.setBusiness_date(ParamUtil.getDateValueByObject(cardTradingJson, "business_date"));
			cardTradingList.setShift_id(ParamUtil.getIntegerValueByObject(cardTradingJson, "shift_id"));
			cardTradingList.setChanel(ParamUtil.getStringValueByObject(cardTradingJson, "chanel"));
			cardTradingList.setOperat_type(ParamUtil.getStringValueByObject(cardTradingJson, "operat_type"));
			cardTradingList.setBill_money(ParamUtil.getDoubleValueByObject(cardTradingJson, "bill_money"));
			cardTradingList.setMain_trading(ParamUtil.getDoubleValueByObject(cardTradingJson, "main_trading"));
			cardTradingList.setReward_trading(ParamUtil.getDoubleValueByObject(cardTradingJson, "reward_trading"));
			cardTradingList.setRevoked_trading(ParamUtil.getDoubleValueByObject(cardTradingJson, "revoked_trading"));
			cardTradingList.setMain_original(ParamUtil.getDoubleValueByObject(cardTradingJson, "main_original"));
			cardTradingList.setReward_original(ParamUtil.getDoubleValueByObject(cardTradingJson, "reward_original"));
			cardTradingList.setTotal_balance(ParamUtil.getDoubleValueByObject(cardTradingJson, "total_balance"));
			cardTradingList.setReward_balance(ParamUtil.getDoubleValueByObject(cardTradingJson, "reward_balance"));
			cardTradingList.setMain_balance(ParamUtil.getDoubleValueByObject(cardTradingJson, "main_balance"));
			cardTradingList.setDeposit(ParamUtil.getDoubleValueByObject(cardTradingJson, "deposit"));
			cardTradingList.setActivity_id(ParamUtil.getIntegerValueByObject(cardTradingJson, "activity_id"));
			cardTradingList.setCustomer_id(ParamUtil.getIntegerValueByObject(cardTradingJson, "customer_id"));
			cardTradingList.setSalesman(ParamUtil.getStringValueByObject(cardTradingJson, "salesman"));
			cardTradingList.setOperate_time(ParamUtil.getTimestampValueByObject(cardTradingJson, "operate_time"));
			cardTradingList.setBill_code_original(ParamUtil.getStringValueByObject(cardTradingJson, "bill_code_original"));
			cardTradingList.setLast_updatetime(lastUpdatetime);
			cardTradingList.setStore_updatetime(storeUpdatetime);
			cardTradingList.setOperator_id(ParamUtil.getIntegerValueByObject(cardTradingJson, "operator_id"));
			cardTradingList.setOperator(ParamUtil.getStringValueByObject(cardTradingJson, "operator"));
			cardTradingList.setInvoice_balance(0d);
			cardTradingList.setIs_invoice(customerOperate.getIs_invoice());
			cardTradingList.setPayment_state(paymentState);
			cardTradingList.setRecharge_state(operateState);
			cardTradingList.setRequest_status(requestStatus);
			cardTradingList.setBill_code_original(null);
			cardTradingList.setBatch_num(null);
			cardTradingList.setCommission_saler_money(0d);
			cardTradingList.setCommission_store_money(0d);
			cardTradingList.setPay_type(null);
			cardTradingList.setPosNum(ParamUtil.getStringValueByObject(cardTradingJson, "pos_num"));


			customerDao.insertCrmCardTradingList(tenantId, storeId, cardTradingList);

			CrmCardPaymentListEntity cardPaymentList = new CrmCardPaymentListEntity();
			cardPaymentList.setBill_code(billCode);
			cardPaymentList.setThird_bill_code(customerOperate.getThird_bill_code());
			cardPaymentList.setCard_id(cardId);
			cardPaymentList.setPayment_id(ParamUtil.getIntegerValueByObject(cardPaymentJson, "payment_id"));
			cardPaymentList.setPay_money(ParamUtil.getDoubleValueByObject(cardPaymentJson, "pay_money"));
			cardPaymentList.setLocal_currency(ParamUtil.getDoubleValueByObject(cardPaymentJson, "local_currency"));
			cardPaymentList.setPay_no(ParamUtil.getStringValueByObject(cardPaymentJson, "pay_no"));
			cardPaymentList.setRexchange_rate(ParamUtil.getDoubleValueByObject(cardPaymentJson, "rexchange_rate"));
			cardPaymentList.setStore_updatetime(storeUpdatetime);
			cardPaymentList.setLast_updatetime(lastUpdatetime);

			customerDao.insertCrmCardPaymentList(tenantId, storeId, cardPaymentList);
			
			JSONObject resultJson = new JSONObject();
			resultJson.put("is_invoice", customerOperate.getIs_invoice());
			resultJson.put("report_date", ParamUtil.getDateValueByObject(cardTradingJson, "business_date"));
			resultJson.put("pos_num",customerOperate.getPos_num());
			resultJson.put("income", ParamUtil.getDoubleValueByObject(cardPaymentJson, "pay_money"));
			resultJson.put("bill_code", billCode);
			resultJson.put("payment_id", ParamUtil.getIntegerValueByObject(cardPaymentJson, "payment_id"));
			resultJson.put("level_name", ParamUtil.getStringValueByObject(cardTradingJson, "level_name"));
			resultJson.put("card_class_name", ParamUtil.getStringValueByObject(cardTradingJson, "card_class_name"));
			resultJson.put("card_code", ParamUtil.getStringValueByObject(cardTradingJson, "card_code"));
			resultJson.put("name", ParamUtil.getStringValueByObject(cardTradingJson, "name"));
			resultJson.put("mobil", ParamUtil.getStringValueByObject(cardTradingJson, "mobil"));
			resultJson.put("main_trading", ParamUtil.getDoubleValueByObject(cardTradingJson, "main_trading"));
			resultJson.put("reward_trading", ParamUtil.getDoubleValueByObject(cardTradingJson, "reward_trading"));
			resultJson.put("main_balance", ParamUtil.getDoubleValueByObject(cardTradingJson, "main_balance"));
			resultJson.put("reward_balance", ParamUtil.getDoubleValueByObject(cardTradingJson, "reward_balance"));
			resultJson.put("reward_credit", ParamUtil.getDoubleValueByObject(cardTradingJson, "reward_credit"));
			resultJson.put("useful_credit", ParamUtil.getDoubleValueByObject(cardTradingJson, "useful_credit"));
			resultJson.put("operator", ParamUtil.getStringValueByObject(cardTradingJson, "operator"));
			resultJson.put("chanel", ParamUtil.getStringValueByObject(cardTradingJson, "chanel"));
			resultJson.put("updatetime", DateUtil.format(lastUpdatetime));
			resultJson.put("reward_coupon", "");
			
			List<JSONObject> resultList = new ArrayList<JSONObject>();
			resultList.add(resultJson);
			
			resultData.setCode(Constant.CODE_SUCCESS);
			resultData.setMsg(Constant.GET_CRM_CZ_SUCCESS);
			resultData.setData(resultList);
		}
		else
		{
			operateState = SysDictionary.CUSTOMER_OPERATE_STATE_FAIL;
			requestStatus = SysDictionary.REQUEST_STATUS_COMPLETE;
			requestCode = responseData.getCode();
			requestMsg = responseData.getMsg();
			finishTime = DateUtil.currentTimestamp();
			
			resultData.setCode(Constant.CODE_NULL_DATASET);
			resultData.setMsg(Constant.GET_CRM_CZ_FAILURE);
		}

		customerDao.updatePosCustomerOperateList(tenantId, storeId, customerOperate.getThird_bill_code(), paymentState, operateState, requestStatus, requestCode, requestMsg, finishTime, operType,billCode,(null != extendParamJson ? extendParamJson.toString() : null));
		
		return resultData;
	}

	@Override
	public void updateCustomerOperateListStateForCardRecharge(String tenancyId, int storeId, JSONObject paramJson) throws Exception
	{
		// TODO Auto-generated method stub
		String billCode = ParamUtil.getStringValueByObject(paramJson, "bill_code");
		String paymentState = ParamUtil.getStringValueByObject(paramJson, "payment_state");
		String operateState = ParamUtil.getStringValueByObject(paramJson, "operate_state");
		String requestStatus = ParamUtil.getStringValueByObject(paramJson, "request_status");
		Integer requestCode = ParamUtil.getIntegerValueByObject(paramJson, "request_code");
		String requestMsg = ParamUtil.getStringValueByObject(paramJson, "request_msg");
		Timestamp finishTime = ParamUtil.getTimestampValueByObject(paramJson, "finish_time");
		String operatType = ParamUtil.getStringValueByObject(paramJson, "operat_type");

		customerDao.updatePosCustomerOperateList(tenancyId, storeId, billCode, paymentState, operateState, requestStatus, requestCode, requestMsg, finishTime, operatType);
	}
	
	@Override
	public Data customerCardRechargeManage(String tenantId, Integer storeId, String thirdBillCode, JSONObject paramJson, PosCustomerOperateListEntity cardRecharge, Oper oper) throws Exception
	{
		synchronized (thirdBillCode.intern())
		{
			switch (oper)
			{
				case add:
					return this.customerCardRecharge(tenantId, storeId, thirdBillCode, paramJson);

				case cancle:

					return this.cancelCustomerCardRecharge(tenantId, storeId, cardRecharge);
				default:
					break;
			}
			return null;
		}
	}
}
