package com.tzx.clientorder.common.entity;

import java.util.List;

public class AcewillOrderSetmeal {

	private String did;
	private String dishsno;
	private String name;
	private String duid;
	private String duName;
	private Integer number;
	private Double price;
	private Double orgprice;
	private Double memberprice;
	private String dishimg;
	private Object cooks;
	private String type;
	private List<String> membergid;
	private Integer bgift;
	private Integer isWeigh;
	private String bbuySno;
	private String bgiftSno;
	private Double bmemberprice;
	private Double bargainprice;
	private Double aprice;
	private String pkid;
	private String remark;
	private Double realprice;
	private List<AcewillOrderSetmealItem> maindish;
	private List<AcewillOrderSetmealItem> mandatory;
	private List<AcewillOrderSetmealItem> optional;

	//
	/*private  Integer is_gift;
	private  Integer is_weigh;
	private  Integer is_memberprice;
	private  Integer is_bargainprice;
	private  String memos;
	private  String bgiftsno;
	private  String bbuysno;*/

	public String getDid() {
		return did;
	}
	public void setDid(String did) {
		this.did = did;
	}
	public String getDishsno() {
		return dishsno;
	}
	public void setDishsno(String dishsno) {
		this.dishsno = dishsno;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getDuid() {
		return duid;
	}
	public void setDuid(String duid) {
		this.duid = duid;
	}
	public Integer getNumber() {
		return number;
	}
	public void setNumber(Integer number) {
		this.number = number;
	}
	public Double getPrice() {
		return price;
	}
	public void setPrice(Double price) {
		this.price = price;
	}
	public Double getOrgprice() {
		return orgprice;
	}
	public void setOrgprice(Double orgprice) {
		this.orgprice = orgprice;
	}
	public Double getMemberprice() {
		return memberprice;
	}
	public void setMemberprice(Double memberprice) {
		this.memberprice = memberprice;
	}
	public String getDishimg() {
		return dishimg;
	}
	public void setDishimg(String dishimg) {
		this.dishimg = dishimg;
	}
	public Object getCooks() {
		return cooks;
	}
	public void setCooks(Object cooks) {
		this.cooks = cooks;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public List<String> getMembergid() {
		return membergid;
	}
	public void setMembergid(List<String> membergid) {
		this.membergid = membergid;
	}
	public Integer getBgift() {
		return bgift;
	}
	public void setBgift(Integer bgift) {
		this.bgift = bgift;
	}
	public Integer getIsWeigh() {
		return isWeigh;
	}
	public void setIsWeigh(Integer isWeigh) {
		this.isWeigh = isWeigh;
	}
	public String getBbuySno() {
		return bbuySno;
	}
	public void setBbuySno(String bbuySno) {
		this.bbuySno = bbuySno;
	}
	public String getBgiftSno() {
		return bgiftSno;
	}
	public void setBgiftSno(String bgiftSno) {
		this.bgiftSno = bgiftSno;
	}
	public Double getBmemberprice() {
		return bmemberprice;
	}
	public void setBmemberprice(Double bmemberprice) {
		this.bmemberprice = bmemberprice;
	}
	public Double getBargainprice() {
		return bargainprice;
	}
	public void setBargainprice(Double bargainprice) {
		this.bargainprice = bargainprice;
	}
	public Double getAprice() {
		return aprice;
	}
	public void setAprice(Double aprice) {
		this.aprice = aprice;
	}
	public String getPkid() {
		return pkid;
	}
	public void setPkid(String pkid) {
		this.pkid = pkid;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public Double getRealprice() {
		return realprice;
	}
	public void setRealprice(Double realprice) {
		this.realprice = realprice;
	}
	public List<AcewillOrderSetmealItem> getMaindish() {
		return maindish;
	}
	public void setMaindish(List<AcewillOrderSetmealItem> maindish) {
		this.maindish = maindish;
	}
	public List<AcewillOrderSetmealItem> getMandatory() {
		return mandatory;
	}
	public void setMandatory(List<AcewillOrderSetmealItem> mandatory) {
		this.mandatory = mandatory;
	}
	public List<AcewillOrderSetmealItem> getOptional() {
		return optional;
	}
	public void setOptional(List<AcewillOrderSetmealItem> optional) {
		this.optional = optional;
	}
	public String getDuName() {
		return duName;
	}
	public void setDuName(String duName) {
		this.duName = duName;
	}
}