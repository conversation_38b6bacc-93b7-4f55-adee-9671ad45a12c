package com.tzx.clientorder.wlifeprogram.common.entity;

/**
 * Created by zds on 2018-07-19.
 */
public class MicroLifePayOrderData {

    private Integer shop_key;
    private String openid;
    private String name;
    private String order_id;
    private String oid;
    private String source;
    //账单金额
    private Double amount;
    //发票抬头
    private String invoice;
    //发票价格
    private Double invoice_price;
    //会员价
    private Double memberPrice;

    public Integer getShop_key() {
        return shop_key;
    }

    public void setShop_key(Integer shop_key) {
        this.shop_key = shop_key;
    }

    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getOrder_id() {
        return order_id;
    }

    public void setOrder_id(String order_id) {
        this.order_id = order_id;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public String getInvoice() {
        return invoice;
    }

    public void setInvoice(String invoice) {
        this.invoice = invoice;
    }

    public Double getInvoice_price() {
        return invoice_price;
    }

    public void setInvoice_price(Double invoice_price) {
        this.invoice_price = invoice_price;
    }

    public Double getMemberPrice() {
        return memberPrice;
    }

    public void setMemberPrice(Double memberPrice) {
        this.memberPrice = memberPrice;
    }
}
