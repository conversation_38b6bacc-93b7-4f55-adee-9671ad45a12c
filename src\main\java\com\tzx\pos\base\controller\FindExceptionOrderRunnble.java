package com.tzx.pos.base.controller;

import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;

import com.tzx.base.common.util.PosPropertyUtil;
import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.pos.bo.OrderDeliveryService;

import net.sf.json.JSONObject;

public class FindExceptionOrderRunnble implements Runnable{

	private static final Logger	logger	= Logger.getLogger(FindExceptionOrderRunnble.class);

	private List<JSONObject> list;
	
	public FindExceptionOrderRunnble(List<JSONObject> list){
		this.list = list;
		this.run();
	}
	
	@Override
	public void run() {
		try {
			Map<String, String> systemMap = Constant.getSystemMap();

			String tenancyId = systemMap.get("tenent_id");
			Integer storeId = Integer.parseInt(systemMap.get("store_id"));
			OrderDeliveryService orderDeliveryService = SpringConext.getApplicationContext().getBean(OrderDeliveryService.class);

			for(JSONObject jo : list){
				orderDeliveryService.findExceptionOrderState(tenancyId,jo);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

}
