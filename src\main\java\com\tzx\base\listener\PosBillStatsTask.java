package com.tzx.base.listener;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Map;

import javax.servlet.ServletContext;

import com.tzx.pos.po.springjdbc.dao.PosPaymentDao;
import com.tzx.pos.po.springjdbc.dao.imp.PosPaymentDaoImp;
import net.sf.json.JSONObject;

import org.apache.log4j.Logger;
import org.springframework.context.ApplicationContext;

import com.tzx.base.service.servlet.PosQuickPassMessage;
import com.tzx.base.service.servlet.ProcessMessage;
import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.bo.payment.PosPaymentService;
import com.tzx.pos.bo.QuickPassService;
import com.tzx.pos.bo.payment.imp.PosPaymentServiceImp;
import com.tzx.pos.bo.imp.QuickPassServiceImp;
import com.tzx.pos.po.springjdbc.dao.PosBillDao;
import com.tzx.pos.po.springjdbc.dao.imp.PosBillDaoImp;

/**
 * 定时任务查询账单状态
 */
public class PosBillStatsTask extends Thread{

    private ServletContext		context;
    private ApplicationContext contextspring	= SpringConext.getApplicationContext();
    private Logger logger		= Logger.getLogger(ProcessMessage.class);
    public PosBillStatsTask()
    {
        // this.context = context;
    }

    public PosBillStatsTask(ServletContext context)
    {
        this.context = context;
    }


    public void run()
    {
        try
        {
            Map<String, String> systemMap = Constant.getSystemMap();
            String tenent_id= systemMap.get("tenent_id");
            String store_id= systemMap.get("store_id");
            DBContextHolder.setTenancyid(tenent_id);
            PosPaymentDaoImp posPaymentServiceImp = (PosPaymentDaoImp) SpringConext.getApplicationContext().getBean(PosPaymentDao.NAME);
            String para_value=posPaymentServiceImp.getSysParameter(tenent_id,Integer.parseInt(store_id),Constant.IFUP_QUICK_PAY);
            if("0".equals(para_value)||"".equals(para_value)){
                return ;
            }
            this.findBillState();
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

    }

    /**
     * 业务处理方法
     */
    public synchronized  void findBillState() throws Exception {
        List <JSONObject>list=this.findPosBill();
        for(JSONObject map:list){
            logger.debug("查询一个半小时到4个小时之间未结账单"+map.toString());
            String tenancy_id = ParamUtil.getStringValueByObject(map,"tenancy_id",false,null);
            int organId = ParamUtil.getIntegerValueByObject(map,"store_id",false,null);
            String  opt_num=ParamUtil.getStringValueByObject(map,"open_opt",false,null);
            String  pos_num=ParamUtil.getStringValueByObject(map,"open_pos_num",false,null);
            String bill_num=ParamUtil.getStringValueByObject(map,"bill_num",false,null);
            String table_code=ParamUtil.getStringValueByObject(map,"table_code",false,null);


            PosQuickPassRunable posQuickPassRunable=new PosQuickPassRunable(tenancy_id,organId+"",bill_num, SysDictionary.SERVICE_TYPE_CONSUME,pos_num,opt_num,SysDictionary.PAYMENT_CLASS_SECONDS_PAY);
            JSONObject jsonObject2=posQuickPassRunable.getPaymentState();
            String  res="";
            Integer jzid=0;
            Integer jzid_credit=0;
            String mem="";
            String billCode="";
            Double jifen=null;
            String mobile="";
            Double dixian=null;
            Integer customer_id=0;
            if(jsonObject2!=null){
                res=ParamUtil.getStringValueByObject(jsonObject2,"payment_state",false,null);
                jzid=ParamUtil.getIntegerValueByObject(jsonObject2,"jzid",false,null);
                jzid_credit=ParamUtil.getIntegerValueByObject(jsonObject2,"jzid_credit",false,null);
                billCode=ParamUtil.getStringValueByObject(jsonObject2,"transaction_no",false,null);
                mem=ParamUtil.getStringValueByObject(jsonObject2,"mem",false,null);
                 jifen=ParamUtil.getDoubleValueByObject(jsonObject2,"jifen",false,null);//积分
                 mobile=ParamUtil.getStringValueByObject(jsonObject2,"mobile",false,null);//会员手机号
                 dixian=ParamUtil.getDoubleValueByObject(jsonObject2,"dixian",false,null);//抵现金额
                 customer_id=ParamUtil.getIntegerValueByObject(jsonObject2,"customer_id",false,null);//会员id

            }

            if("1".equals(res)) {
                List <JSONObject> list4=findExistBill(bill_num,tenancy_id);
                if(list4!=null&&list4.size()>0){
                    JSONObject jsonObject4=list4.get(0);
                    //支付成功更新状态。
                    DBContextHolder.setTenancyid(tenancy_id);
                    QuickPassServiceImp q = (QuickPassServiceImp) SpringConext.getApplicationContext().getBean(QuickPassService.NAME);
                    PosPaymentServiceImp paymentWayService = (PosPaymentServiceImp) SpringConext.getApplicationContext().getBean(PosPaymentService.NAME);
                    JSONObject jsonObject=new JSONObject();
                    jsonObject.put("tenancy_id",jsonObject4.optString("tenancy_id"));
                    jsonObject.put("store_id",jsonObject4.optInt("store_id"));
                    JSONObject data=new JSONObject();
                    data.put("opt_num",opt_num);
                    data.put("pos_num",pos_num);
                    data.put("bill_num",bill_num);
                    data.put("table_code",table_code);
                    data.put("bill_code",billCode);
                    data.put("bill_amount",jsonObject4.optDouble("bill_amount")*100);
                    data.put("shift_id",jsonObject4.optString("shift_id"));
                    JSONObject jsonObject1=new JSONObject();
                    jsonObject1.put("jzid",jzid);
                    jsonObject1.put("mem",mem);
                    jsonObject1.put("jzid_credit",jzid_credit);
                    jsonObject1.put("bill_code",billCode);
                    jsonObject1.put("dixian",dixian);
                    jsonObject1.put("jifen",jifen);
                    jsonObject1.put("mobile",mobile);
                    jsonObject1.put("customer_id",customer_id);
                    data.put("item",jsonObject1);
                    List list1=new ArrayList();
                    list1.add(data);
                    jsonObject.put("data",list1);
                    PosQuickPassMessage posQuickPassMessage=new PosQuickPassMessage();
                    posQuickPassMessage.quickPayMent(q, paymentWayService, jsonObject);
                }

            }
        }
    }
    public  List<JSONObject> findExistBill(String billNum,String tenent_id) throws Exception {
        logger.info("查询是否存在未结账账单");
        DBContextHolder.setTenancyid(tenent_id);
        PosBillDaoImp posBillDaoImp = (PosBillDaoImp) SpringConext.getApplicationContext().getBean(PosBillDao.NAME);
        List<JSONObject> list= posBillDaoImp.findPosPayment(billNum);
        logger.info("未结账账单"+list.toString());
        return list;
    }
    /**
     * 查询所有未付款且开台时大于1个半小时的订单。
     * @return
     */
    public List<JSONObject> findPosBill() throws Exception {
        Calendar calendar = Calendar.getInstance();
        Calendar calendar1 = Calendar.getInstance();
        /* HOUR_OF_DAY 指示一天中的小时 */
        calendar.set(Calendar.HOUR_OF_DAY, calendar.get(Calendar.HOUR_OF_DAY) - 1);
        calendar.set(Calendar.MINUTE, calendar.get(Calendar.MINUTE) - 30);
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Timestamp timestamp=DateUtil.formatTimestamp(df.format(calendar.getTime()));
       // logger.debug("一个半小时前时间为"+timestamp.toString());
        calendar1.set(Calendar.HOUR_OF_DAY, calendar1.get(Calendar.HOUR_OF_DAY) - 4);
        Timestamp timestamp1=DateUtil.formatTimestamp(df.format(calendar1.getTime()));
       // logger.debug("四小时前时间为"+timestamp1.toString());
        Map<String, String> systemMap = Constant.getSystemMap();
        String tenent_id= systemMap.get("tenent_id");
        DBContextHolder.setTenancyid(tenent_id);
        PosBillDaoImp posBillDaoImp = (PosBillDaoImp) SpringConext.getApplicationContext().getBean(PosBillDao.NAME);
        List<JSONObject> list= posBillDaoImp.findPosBill(timestamp1,timestamp);
        return list;
    }
}
