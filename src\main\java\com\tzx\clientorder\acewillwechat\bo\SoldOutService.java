package com.tzx.clientorder.acewillwechat.bo;

import java.util.Date;
import java.util.List;

import net.sf.json.JSONObject;

/**
 * 估清（微生活会员） Created by qin-gui on 2018-03-16.
 */
public interface SoldOutService
{
	String	NAME	= "com.tzx.clientorder.acewillwechat.bo.imp.SoldOutServiceImp";

	/**
	 * 微生活的估清(估清调用，包含删除微生活门店的全部估清)
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @throws Exception
	 */
	void wLifeSoldOut(String tenancyId, int storeId, Date reportDate) throws Exception;

	/**
	 * 微生活的估清（只调用估清接口）
	 * 
	 * @param tenancyId
	 * @throws Exception
	 */
	void soldOut(String tenancyId, int storeId, Date reportDate) throws Exception;

	/**
	 * 取消沽清（门店退沽清时，调用微生活平台的取消沽清）
	 * 
	 * @param tenancyId
	 * @param dishs
	 * @throws Exception
	 */
	void cancelSoldOut(String tenancyId, int storeId, List<JSONObject> dishs) throws Exception;
	
	/** 取消沽清（门店退沽清时，调用微生活平台的取消沽清）
	 * @param tenancyId
	 * @param storeId
	 * @param itemIds
	 * @throws Exception
	 */
	void cancelSoldOut(String tenancyId, int storeId,String itemIds) throws Exception;
}
