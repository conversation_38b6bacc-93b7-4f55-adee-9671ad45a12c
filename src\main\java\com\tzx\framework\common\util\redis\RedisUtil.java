package com.tzx.framework.common.util.redis;

import com.thoughtworks.xstream.XStream;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.ShardedJedis;
import redis.clients.jedis.ShardedJedisPool;

import java.util.*;

/**
 * Created by jzq1999 on 2017/4/6.
 */
public class RedisUtil extends BaseRedisUtil {
    private static final Logger log = LoggerFactory.getLogger(RedisUtil.class);
    private static XStream xstream = new XStream();
    private ShardedJedisPool shardedJedisPool;

    public RedisUtil() {
    }

    public void rpush(String key, String... val) {
        ShardedJedis shardedJedis = null;

        try {
            shardedJedis = (ShardedJedis)this.shardedJedisPool.getResource();
            shardedJedis.rpush(key, val);
            return;
        } catch (Exception var8) {
            log.error("redis操作发生异常", var8);
        } finally {
            if(shardedJedis != null) {
                shardedJedis.close();
            }
        }
    }

    public void lpush(String key, String... val) {
        ShardedJedis shardedJedis = null;

        try {
            shardedJedis = (ShardedJedis)this.shardedJedisPool.getResource();
            shardedJedis.lpush(key, val);
            return;
        } catch (Exception var8) {
            log.error("redis操作发生异常", var8);
        } finally {
            if(shardedJedis != null) {
                shardedJedis.close();
            }

        }

    }

    public Long llen(String key) {
        if(!this.useCache) {
            return null;
        } else {
            ShardedJedis shardedJedis = null;

            Long var4;
            try {
                shardedJedis = (ShardedJedis)this.shardedJedisPool.getResource();
                Long e = shardedJedis.llen(key);
                var4 = e;
                return var4;
            } catch (Exception var8) {
                log.error("redis操作发生异常", var8);
                var4 = null;
            } finally {
                if(shardedJedis != null) {
                    shardedJedis.close();
                }

            }

            return var4;
        }
    }

    public List lrange(String key, int start, int end) {
        if(!this.useCache) {
            return null;
        } else {
            ShardedJedis shardedJedis = null;

            List var6;
            try {
                shardedJedis = (ShardedJedis)this.shardedJedisPool.getResource();
                List e = shardedJedis.lrange(key, (long)start, (long)end);
                var6 = e;
                return var6;
            } catch (Exception var10) {
                log.error("redis操作发生异常", var10);
                var6 = null;
            } finally {
                if(shardedJedis != null) {
                    shardedJedis.close();
                }

            }

            return var6;
        }
    }

    public void lset(String key, int index, String value) {
        ShardedJedis shardedJedis = null;

        try {
            shardedJedis = (ShardedJedis)this.shardedJedisPool.getResource();
            shardedJedis.lset(key, (long)index, value);
            return;
        } catch (Exception var9) {
            log.error("redis操作发生异常", var9);
        } finally {
            if(shardedJedis != null) {
                shardedJedis.close();
            }

        }

    }

    public String lpop(String key) {
        if(!this.useCache) {
            return null;
        } else {
            ShardedJedis shardedJedis = null;

            String var4;
            try {
                shardedJedis = (ShardedJedis)this.shardedJedisPool.getResource();
                String e = shardedJedis.lpop(key);
                var4 = e;
                return var4;
            } catch (Exception var8) {
                log.error("redis操作发生异常", var8);
                var4 = null;
            } finally {
                if(shardedJedis != null) {
                    shardedJedis.close();
                }

            }

            return var4;
        }
    }

    public String rpop(String key) {
        if(!this.useCache) {
            return null;
        } else {
            ShardedJedis shardedJedis = null;

            String var4;
            try {
                shardedJedis = (ShardedJedis)this.shardedJedisPool.getResource();
                String e = shardedJedis.rpop(key);
                var4 = e;
                return var4;
            } catch (Exception var8) {
                log.error("redis操作发生异常", var8);
                var4 = null;
            } finally {
                if(shardedJedis != null) {
                    shardedJedis.close();
                }

            }

            return var4;
        }
    }

    public boolean exists(String key) {
        ShardedJedis shardedJedis = null;

        boolean var4;
        try {
            shardedJedis = (ShardedJedis)this.shardedJedisPool.getResource();
            Boolean e = shardedJedis.exists(key);
            var4 = e.booleanValue();
            return var4;
        } catch (Exception var8) {
            log.error("redis操作发生异常", var8);
            var4 = false;
        } finally {
            if(shardedJedis != null) {
                shardedJedis.close();
            }

        }

        return var4;
    }

    public Long del(String key) {
        ShardedJedis shardedJedis = null;

        Long var4;
        try {
            shardedJedis = (ShardedJedis)this.shardedJedisPool.getResource();
            Long e = shardedJedis.del(key);
            return e;
        } catch (Exception var8) {
            log.error("redis操作发生异常", var8);
            var4 = Long.valueOf(0L);
        } finally {
            if(shardedJedis != null) {
                shardedJedis.close();
            }

        }

        return var4;
    }

    public void set(String key, String val) {
        ShardedJedis shardedJedis = null;

        try {
            shardedJedis = (ShardedJedis)this.shardedJedisPool.getResource();
            shardedJedis.setex(key, this.defaultExpireTime, val);
            return;
        } catch (Exception var8) {
            log.error("redis操作发生异常", var8);
        } finally {
            if(shardedJedis != null) {
                shardedJedis.close();
            }

        }

    }

    public void set(String key, String val, boolean expire) {
        ShardedJedis shardedJedis = null;

        try {
            shardedJedis = (ShardedJedis)this.shardedJedisPool.getResource();
            if(expire) {
                shardedJedis.setex(key, this.defaultExpireTime, val);
            } else {
                shardedJedis.set(key, val);
            }

            return;
        } catch (Exception var9) {
            log.error("redis操作发生异常", var9);
        } finally {
            if(shardedJedis != null) {
                shardedJedis.close();
            }

        }

    }

    public String get(String key) {
        if(!this.useCache) {
            return null;
        } else {
            ShardedJedis shardedJedis = null;

            String var4;
            try {
                shardedJedis = (ShardedJedis)this.shardedJedisPool.getResource();
                String e = shardedJedis.get(key);
                if(StringUtils.isNotBlank(e) && e.startsWith("<") && e.endsWith(">")) {
                    e = null;
                    this.del(key);
                }

                var4 = e;
                return var4;
            } catch (Exception var8) {
                log.error("redis操作发生异常", var8);
                var4 = null;
            } finally {
                if(shardedJedis != null) {
                    shardedJedis.close();
                }

            }

            return var4;
        }
    }

    public String getSet(String key, String value) {
        if(!this.useCache) {
            return null;
        } else {
            ShardedJedis shardedJedis = null;

            String var5;
            try {
                shardedJedis = (ShardedJedis)this.shardedJedisPool.getResource();
                String e = shardedJedis.getSet(key, value);
                var5 = e;
                return var5;
            } catch (Exception var9) {
                log.error("redis操作发生异常", var9);
                var5 = null;
            } finally {
                if(shardedJedis != null) {
                    shardedJedis.close();
                }

            }

            return var5;
        }
    }

    public void lrem(String key, Integer count, String val) {
        ShardedJedis shardedJedis = null;

        try {
            shardedJedis = (ShardedJedis)this.shardedJedisPool.getResource();
            shardedJedis.lrem(key, (long)count.intValue(), val);
            return;
        } catch (Exception var9) {
            log.error("redis操作发生异常", var9);
        } finally {
            if(shardedJedis != null) {
                shardedJedis.close();
            }

        }

    }

    public void sadd(String key, String val) {
        ShardedJedis shardedJedis = null;

        try {
            shardedJedis = (ShardedJedis)this.shardedJedisPool.getResource();
            shardedJedis.sadd(key, new String[]{val});
            return;
        } catch (Exception var8) {
            log.error("redis操作发生异常", var8);
        } finally {
            if(shardedJedis != null) {
                shardedJedis.close();
            }

        }

    }

    public String spop(String key) {
        if(!this.useCache) {
            return null;
        } else {
            ShardedJedis shardedJedis = null;

            String var4;
            try {
                shardedJedis = (ShardedJedis)this.shardedJedisPool.getResource();
                String e = shardedJedis.spop(key);
                var4 = e;
                return var4;
            } catch (Exception var8) {
                log.error("redis操作发生异常", var8);
                var4 = null;
            } finally {
                if(shardedJedis != null) {
                    shardedJedis.close();
                }

            }

            return var4;
        }
    }

    public Set<String> smembers(String key) {
        if(!this.useCache) {
            return null;
        } else {
            ShardedJedis shardedJedis = null;

            Set var4;
            try {
                shardedJedis = (ShardedJedis)this.shardedJedisPool.getResource();
                Set e = shardedJedis.smembers(key);
                var4 = e;
                return var4;
            } catch (Exception var8) {
                log.error("redis操作发生异常", var8);
                var4 = null;
            } finally {
                if(shardedJedis != null) {
                    shardedJedis.close();
                }

            }

            return var4;
        }
    }

    public int scard(String key) {
        if(!this.useCache) {
            return 0;
        } else {
            ShardedJedis shardedJedis = null;

            byte var4;
            try {
                shardedJedis = (ShardedJedis)this.shardedJedisPool.getResource();
                Long e = shardedJedis.scard(key);
                int var10 = e.intValue();
                return var10;
            } catch (Exception var8) {
                log.error("redis操作发生异常", var8);
                var4 = 0;
            } finally {
                if(shardedJedis != null) {
                    shardedJedis.close();
                }

            }

            return var4;
        }
    }

    public void expire(String key, int num) {
        ShardedJedis shardedJedis = null;

        try {
            shardedJedis = (ShardedJedis)this.shardedJedisPool.getResource();
            shardedJedis.expire(key, num);
            return;
        } catch (Exception var8) {
            log.error("redis操作发生异常", var8);
        } finally {
            if(shardedJedis != null) {
                shardedJedis.close();
            }

        }

    }

    public void setex(String key, int seconds, String value) {
        ShardedJedis shardedJedis = null;

        try {
            shardedJedis = (ShardedJedis)this.shardedJedisPool.getResource();
            shardedJedis.setex(key, seconds, value);
            return;
        } catch (Exception var9) {
            log.error("redis操作发生异常", var9);
        } finally {
            if(shardedJedis != null) {
                shardedJedis.close();
            }

        }

    }

    public void srem(String key, String val) {
        ShardedJedis shardedJedis = null;

        try {
            shardedJedis = (ShardedJedis)this.shardedJedisPool.getResource();
            shardedJedis.srem(key, new String[]{val});
            return;
        } catch (Exception var8) {
            log.error("redis操作发生异常", var8);
        } finally {
            if(shardedJedis != null) {
                shardedJedis.close();
            }

        }

    }

    public boolean sismember(String key, String member) {
        if(!this.useCache) {
            return false;
        } else {
            ShardedJedis shardedJedis = null;

            boolean var5;
            try {
                shardedJedis = (ShardedJedis)this.shardedJedisPool.getResource();
                boolean e = shardedJedis.sismember(key, member).booleanValue();
                return e;
            } catch (Exception var9) {
                log.error("redis操作发生异常", var9);
                var5 = false;
            } finally {
                if(shardedJedis != null) {
                    shardedJedis.close();
                }

            }

            return var5;
        }
    }

    public Long incr(String key) {
        if(!this.useCache) {
            return null;
        } else {
            ShardedJedis shardedJedis = null;

            Object var4;
            try {
                shardedJedis = (ShardedJedis)this.shardedJedisPool.getResource();
                Long e = shardedJedis.incr(key);
                return e;
            } catch (Exception var8) {
                log.error("redis操作发生异常", var8);
                var4 = null;
            } finally {
                if(shardedJedis != null) {
                    shardedJedis.close();
                }

            }

            return (Long)var4;
        }
    }

    public Long decr(String key) {
        if(!this.useCache) {
            return null;
        } else {
            ShardedJedis shardedJedis = null;

            Object var4;
            try {
                shardedJedis = (ShardedJedis)this.shardedJedisPool.getResource();
                Long e = shardedJedis.decr(key);
                return e;
            } catch (Exception var8) {
                log.error("redis操作发生异常", var8);
                var4 = null;
            } finally {
                if(shardedJedis != null) {
                    shardedJedis.close();
                }

            }

            return (Long)var4;
        }
    }

    public Long hincrBy(String key, String field, Long value) {
        if(!this.useCache) {
            return null;
        } else {
            ShardedJedis shardedJedis = null;

            Object var7;
            try {
                shardedJedis = (ShardedJedis)this.shardedJedisPool.getResource();
                Long e = shardedJedis.hincrBy(key, field, value);
                return e;
            } catch (Exception var11) {
                log.error("redis操作发生异常", var11);
                var7 = null;
            } finally {
                if(shardedJedis != null) {
                    shardedJedis.close();
                }

            }

            return (Long)var7;
        }
    }

    public boolean hmexite(String key, String field) {
        if(!this.useCache) {
            return false;
        } else {
            ShardedJedis shardedJedis = null;

            boolean var5;
            try {
                shardedJedis = (ShardedJedis)this.shardedJedisPool.getResource();
                boolean e = shardedJedis.hexists(key, field).booleanValue();
                return e;
            } catch (Exception var9) {
                log.error("redis操作发生异常", var9);
                var5 = false;
            } finally {
                if(shardedJedis != null) {
                    shardedJedis.close();
                }

            }

            return var5;
        }
    }

    public Long hgetNum(String key, String field) {
        if(!this.useCache) {
            return null;
        } else {
            ShardedJedis shardedJedis = null;

            Long var5;
            try {
                shardedJedis = (ShardedJedis)this.shardedJedisPool.getResource();
                String e = shardedJedis.hget(key, field);
                if(StringUtils.isNotBlank(e)) {
                    var5 = Long.valueOf(e);
                    return var5;
                }

                var5 = Long.valueOf(0L);
            } catch (Exception var9) {
                log.error("redis操作发生异常", var9);
                var5 = null;
                return var5;
            } finally {
                if(shardedJedis != null) {
                    shardedJedis.close();
                }

            }

            return var5;
        }
    }

    public Long hsetnx(String key, String field, String value) {
        if(!this.useCache) {
            return null;
        } else {
            ShardedJedis shardedJedis = null;

            Object var6;
            try {
                shardedJedis = (ShardedJedis)this.shardedJedisPool.getResource();
                Long e = shardedJedis.hsetnx(key, field, value);
                return e;
            } catch (Exception var10) {
                log.error("redis操作发生异常", var10);
                var6 = null;
            } finally {
                if(shardedJedis != null) {
                    shardedJedis.close();
                }

            }

            return (Long)var6;
        }
    }

    public Map<String, String> hgetAll(String key) {
        if(!this.useCache) {
            return null;
        } else {
            ShardedJedis shardedJedis = null;

            Object var4;
            try {
                shardedJedis = (ShardedJedis)this.shardedJedisPool.getResource();
                Map e = shardedJedis.hgetAll(key);
                return e;
            } catch (Exception var8) {
                log.error("redis操作发生异常", var8);
                var4 = null;
            } finally {
                if(shardedJedis != null) {
                    shardedJedis.close();
                }

            }

            return (Map)var4;
        }
    }

    public Object getBySerialize(String key) {
        if(!this.useCache) {
            return null;
        } else {
            ShardedJedis shardedJedis = null;

            Object var4;
            try {
                shardedJedis = (ShardedJedis)this.shardedJedisPool.getResource();
                String e = shardedJedis.get(key);
                if(e == null) {
                    var4 = null;
                    return var4;
                }

                var4 = xstream.fromXML(e);
                return var4;
            } catch (Exception var8) {
                log.error("redis操作发生异常", var8);
                var4 = null;
            } finally {
                if(shardedJedis != null) {
                    shardedJedis.close();
                }

            }

            return var4;
        }
    }

    public void setBySerialize(String key, Object val) {
        ShardedJedis shardedJedis = null;

        try {
            if(val == null) {
                return;
            }

            shardedJedis = (ShardedJedis)this.shardedJedisPool.getResource();
            shardedJedis.setex(key, this.defaultExpireTime, xstream.toXML(val));
            return;
        } catch (Exception var8) {
            log.error("redis操作发生异常", var8);
        } finally {
            if(shardedJedis != null) {
                shardedJedis.close();
            }

        }

    }

    public void setexBySerialize(String key, int seconds, Object val) {
        ShardedJedis shardedJedis = null;

        try {
            shardedJedis = (ShardedJedis)this.shardedJedisPool.getResource();
            shardedJedis.setex(key, seconds, val == null?null:xstream.toXML(val));
            return;
        } catch (Exception var9) {
            log.error("redis操作发生异常", var9);
        } finally {
            if(shardedJedis != null) {
                shardedJedis.close();
            }

        }

    }

    public void zadd(String key, double score, String member) {
        if(this.useCache) {
            if(!StringUtils.isBlank(key)) {
                ShardedJedis shardedJedis = null;

                try {
                    shardedJedis = (ShardedJedis)this.shardedJedisPool.getResource();
                    shardedJedis.zadd(key, score, member);
                    return;
                } catch (Exception var10) {
                    log.error("redis操作发生异常", var10);
                } finally {
                    if(shardedJedis != null) {
                        shardedJedis.close();
                    }

                }

            }
        }
    }

    public void zrem(String key, String member) {
        if(this.useCache) {
            if(!StringUtils.isBlank(key)) {
                ShardedJedis shardedJedis = null;

                try {
                    shardedJedis = (ShardedJedis)this.shardedJedisPool.getResource();
                    shardedJedis.zrem(key, new String[]{member});
                    return;
                } catch (Exception var8) {
                    log.error("redis操作发生异常", var8);
                } finally {
                    if(shardedJedis != null) {
                        shardedJedis.close();
                    }

                }

            }
        }
    }

    public Double zscore(String key, String member) {
        Double result = null;
        if(!this.useCache) {
            return null;
        } else if(StringUtils.isBlank(key)) {
            return null;
        } else {
            ShardedJedis shardedJedis = null;

            Object var6;
            try {
                shardedJedis = (ShardedJedis)this.shardedJedisPool.getResource();
                result = shardedJedis.zscore(key, member);
                return result;
            } catch (Exception var10) {
                log.error("redis操作发生异常", var10);
                var6 = null;
            } finally {
                if(shardedJedis != null) {
                    shardedJedis.close();
                }

            }

            return (Double)var6;
        }
    }

    public List<String> zquery(String key, Long start, Long end, boolean isAsc) {
        if(!this.useCache) {
            return null;
        } else if(StringUtils.isBlank(key)) {
            return null;
        } else {
            ShardedJedis shardedJedis = null;

            ArrayList list;
            try {
                shardedJedis = (ShardedJedis)this.shardedJedisPool.getResource();
                Set e = null;
                if(isAsc) {
                    e = shardedJedis.zrange(key, start, end);
                } else {
                    e = shardedJedis.zrevrange(key, start, end);
                }

                if(null != e && e.size() > 0) {
                    list = new ArrayList();
                    Iterator var10 = e.iterator();

                    while(var10.hasNext()) {
                        String s = (String)var10.next();
                        list.add(s);
                    }

                    ArrayList var17 = list;
                    return var17;
                }

                list = null;
            } catch (Exception var15) {
                log.error("redis操作发生异常", var15);
                list = null;
                return list;
            } finally {
                if(shardedJedis != null) {
                    shardedJedis.close();
                }

            }

            return list;
        }
    }

    public Long zcard(String key) {
        if(!this.useCache) {
            return 0L;
        } else if(StringUtils.isBlank(key)) {
            return 0L;
        } else {
            ShardedJedis shardedJedis = null;

            long var4;
            try {
                shardedJedis = (ShardedJedis)this.shardedJedisPool.getResource();
                long e = shardedJedis.zcard(key).longValue();
                return e;
            } catch (Exception var9) {
                log.error("redis操作发生异常", var9);
                var4 = 0L;
            } finally {
                if(shardedJedis != null) {
                    shardedJedis.close();
                }

            }

            return var4;
        }
    }



    public void setShardedJedisPool(ShardedJedisPool shardedJedisPool) {
        this.shardedJedisPool = shardedJedisPool;
    }

    public String hget(String key, String field) {
        if(!this.useCache) {
            return null;
        } else if(!StringUtils.isBlank(key) && !StringUtils.isBlank(field)) {
            ShardedJedis shardedJedis = null;

            Object var5;
            try {
                shardedJedis = (ShardedJedis)this.shardedJedisPool.getResource();
                String e = shardedJedis.hget(key, field);
                return e;
            } catch (Exception var9) {
                log.error("redis操作发生异常", var9);
                var5 = null;
            } finally {
                if(shardedJedis != null) {
                    shardedJedis.close();
                }

            }

            return (String)var5;
        } else {
            return null;
        }
    }

    public Long hdel(String key, String... fields) {
        if(!this.useCache) {
            return null;
        } else if(!StringUtils.isBlank(key) && fields != null && fields.length > 0) {
            ShardedJedis shardedJedis = null;

            Object var5;
            try {
                shardedJedis = (ShardedJedis)this.shardedJedisPool.getResource();
                Long e = shardedJedis.hdel(key, fields);
                return e;
            } catch (Exception var9) {
                log.error("redis操作发生异常", var9);
                var5 = null;
            } finally {
                if(shardedJedis != null) {
                    shardedJedis.close();
                }

            }

            return (Long)var5;
        } else {
            return null;
        }
    }

    public Long hset(String key, String field, String value) {
        if(!this.useCache) {
            return Long.valueOf(0L);
        } else if(!StringUtils.isBlank(key) && !StringUtils.isBlank(field) && !StringUtils.isBlank(value)) {
            ShardedJedis shardedJedis = null;
            Long result = Long.valueOf(0L);

            Long var7;
            try {
                shardedJedis = (ShardedJedis)this.shardedJedisPool.getResource();
                result = shardedJedis.hset(key, field, value);
                shardedJedis.expire(key, this.defaultExpireTime);
                return result;
            } catch (Exception var11) {
                log.error("redis操作发生异常", var11);
                var7 = Long.valueOf(0L);
            } finally {
                if(shardedJedis != null) {
                    shardedJedis.close();
                }

            }

            return var7;
        } else {
            return Long.valueOf(0L);
        }
    }

    public void sadd(String key, String... member) {
        ShardedJedis shardedJedis = null;

        try {
            shardedJedis = (ShardedJedis)this.shardedJedisPool.getResource();
            shardedJedis.sadd(key, member);
            return;
        } catch (Exception var8) {
            log.error("redis操作发生异常", var8);
        } finally {
            if(shardedJedis != null) {
                shardedJedis.close();
            }
        }
    }

}


