package com.tzx.clientorder.wlifeprogram.bo;

import net.sf.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

/**
 * 微生活后付业务层
 */
public interface AfterPaymentService {
    Map<String,String> paymentWayMapping = new HashMap<>();

    Map<String,String> posBillMemberTyoeMapping = new HashMap<>();

    Map<String,String> posBillCouponTypeMapping = new HashMap<>();

    Map<String,String> posBillCouponTypeNameMapping = new HashMap<>();
    int DEFAULT_SCALE = 4;
    String NAME = "com.tzx.clientorder.wlifeprogram.bo.impl.AfterPaymentServiceImpl";

    @Deprecated
    void orderDish(JSONObject param, JSONObject printJson, JSONObject responseJson) throws Exception;

    void billPayment(JSONObject param, JSONObject printJson, JSONObject responseJson) throws Exception;

    void saveOrUpdateOrder(JSONObject param, JSONObject printJson, JSONObject responseJson) throws Exception;

    void payUploadBill(JSONObject param,JSONObject responseJson) throws Exception;

    void paymentClose(JSONObject param,JSONObject responseJson) throws Exception;

    JSONObject getOrderDetail(String tenancyId, int storeId, String tableCode, String opType) throws Exception;

    /** 锁单
     * @param param
     * @param responseJson
     * @throws Exception
     */
	void lockOrder(String tenancyId, Integer storeId, JSONObject param, JSONObject responseJson) throws Exception;
    
    /** 解锁
     * @param param
     * @param responseJson
     * @throws Exception
     */
    void unlockOrder(String tenancyId, Integer storeId, JSONObject param,JSONObject responseJson) throws Exception;
}
