package com.tzx.pos.bo.acewill.imp;

import java.sql.Timestamp;
import java.util.*;

import com.google.common.base.Objects;
import com.google.common.base.Optional;
import com.tzx.base.common.util.StringUtil;
import com.tzx.base.entity.PosBillPaymentCoupons;
import com.tzx.framework.common.util.CommonUtil;
import com.tzx.framework.common.util.Md5Utils;
import com.tzx.member.common.constant.WLifeCrmConstant;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.tzx.base.common.util.PosPropertyUtil;
import com.tzx.base.entity.PosBillMember;
import com.tzx.base.entity.PosBillPayment;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.DoubleHelper;
import com.tzx.framework.common.util.Tools;
import com.tzx.member.common.constant.PaymentWayEnum;
import com.tzx.member.common.entity.PosCustomerOperateListEntity;
import com.tzx.member.common.util.AcewillRequestUtil;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.bo.acewill.AcewillPaymentService;
import com.tzx.pos.bo.payment.imp.PosPaymentServiceImp;

@Service(AcewillPaymentService.NAME)
public class AcewillPaymentServiceImp extends PosPaymentServiceImp implements AcewillPaymentService
{
	private static final Logger		logger				= Logger.getLogger(AcewillPaymentService.class);

//	@Resource(name = AcewillCustomerService.NAME)
//	private AcewillCustomerService	customerService;

//	@Resource(name = PosPaymentDao.NAME)
//	private PosPaymentDao			paymentDao;

//	@Resource(name = PosBillMemberDao.NAME)
//	private PosBillMemberDao			memberDao;
	
	/**积分兑换金额比例  */
	private final double			INTEGRAL_CASH_RATE	= 1d;

//	/** 短信验证码为空 */
//	private final int				SMS_NULL_ERROR_CODE					= 3029;
//	/** 短信验证码错误 */
//	private final int				SMS_WRONG_ERROR_CODE				= 3030;
//	/** 短信验证码过期 */
//	private final int				SMS_OVERDUE_ERROR_CODE				= 3031;
//	/** 交易密码仅支持6位数字 */
//	private final int				PASSWORD_LENGTH_ERROR_CODE			= 3055;
//	/** 输入的交易密码不正确 */
//	private final int				PASSWORD_WRONG_ERROR_CODE			= 3056;
//	/** 不允许使用默认密码交易 */
//	private final int				PASSWORD_DEFAULT_ERROR_CODE			= 3135;
	
	private final int				DEFAULT_SCALE						= 4;
	private final int				AMOUNT_SCALE						= 2;

/*	@Value("${activity_exclude_payment_type}")
	private String[] activityExcludePaymentType;*/

	@Override
	public String addPosBillPaymentForAcewillPay(String tenancyId, int storeId, String devicesType, JSONObject paramJson) throws Exception
	{
		boolean isCheckKssy = true;

		String reportDateStr = ParamUtil.getStringValueByObject(paramJson, "report_date", true, PosErrorCode.NOT_NULL_REPORT_DATE);
		Date reportDate = DateUtil.parseDate(reportDateStr);
		Integer shiftId = ParamUtil.getIntegerValueByObject(paramJson, "shift_id");
		String posNum = ParamUtil.getStringValueByObject(paramJson, "pos_num");
		String optNum = ParamUtil.getStringValueByObject(paramJson, "opt_num");
		String tableCode = ParamUtil.getStringValueByObject(paramJson, "table_code");
		String billNum = ParamUtil.getStringValueByObject(paramJson, "bill_num", true, PosErrorCode.NOT_NULL_BILL_NUM);
		String cardCode = ParamUtil.getStringValueByObject(paramJson, "card_code", true, PosErrorCode.NOT_NULL_CARD_CODE);
		String mobil = ParamUtil.getStringValueByObject(paramJson, "mobil");
		String customerName = ParamUtil.getStringValueByObject(paramJson, "customer_name");
		String levelName = ParamUtil.getStringValueByObject(paramJson, "level_name");
		Double subBalance = ParamUtil.getDoubleValueByObject(paramJson, "sub_balance");
		Double subCredit = ParamUtil.getDoubleValueByObject(paramJson, "sub_credit");
		Double subCreditAmount = ParamUtil.getDoubleValueByObject(paramJson, "sub_credit_amount");
		Double creditDeduct = ParamUtil.getDoubleValueByObject(paramJson, "credit_deduct");//商家设置的多少积分抵1元 例：20 即20积分抵1元
		String chargeReturnCredit = ParamUtil.getStringValueByObject(paramJson, "charge_return_credit");// 消费使用积分抵现
		String isPrintBill = ParamUtil.getStringValueByObject(paramJson, "isprint_bill");
		String isInvoice = ParamUtil.getStringValueByObject(paramJson, "is_invoice");
		JSONArray coupons = null;
		if (paramJson.containsKey("coupon_ids"))
		{
			coupons = paramJson.getJSONArray("coupon_ids");
		}
		String remark = null;

		paymentDao.checkReportDate(tenancyId, storeId, reportDate);
		if (isCheckKssy)
		{
			if (SysDictionary.SOURCE_MOVE.contains(devicesType)||SysDictionary.SOURCE_ORDER_DEVICE.equals(devicesType))
			{
				paymentDao.checkBindOptnum(tenancyId, storeId, reportDate, posNum);
				try
				{
					shiftId = paymentDao.getShiftId(tenancyId, storeId, reportDate, optNum, posNum);
				}
				catch (Exception e)
				{
					e.printStackTrace();
					throw SystemException.getInstance(PosErrorCode.GET_SHIFT_ID_ERROR);
				}
			}
			else
			{
				paymentDao.checkKssy(reportDate, optNum, storeId,shiftId,posNum);
			}
		}

		String paymentState = SysDictionary.PAYMENT_STATE_PAY_PRESERVE;

		Double paymentAmount = 0d;
		String billProperty = "";
		String batchNum = "";
		Double difference = 0d;
		String outTradeNo = null;
		JSONObject billJson = paymentDao.getPosBillByBillnum(tenancyId, storeId, billNum);
		if (null != billJson)
		{
			paymentAmount = ParamUtil.getDoubleValueByObject(billJson, "payment_amount");
			difference = ParamUtil.getDoubleValueByObject(billJson, "difference");
			billProperty = billJson.optString("bill_property");
			batchNum = billJson.optString("batch_num");
			outTradeNo = ParamUtil.getStringValueByObject(billJson, "pay_no");

			if (Tools.isNullOrEmpty(tableCode) && Tools.hv(billJson.optString("table_code")))
				{
					tableCode = billJson.optString("table_code");
			}
		}

		if (SysDictionary.BILL_PROPERTY_CLOSED.equalsIgnoreCase(billProperty))
		{
			throw SystemException.getInstance(PosErrorCode.BILL_CLOSED);
		}

		if (paymentAmount <= 0)
		{
			throw SystemException.getInstance(PosErrorCode.BILL_AMOUNT_ZERO);
		}

		if (difference <= 0)
		{
			throw SystemException.getInstance(PosErrorCode.BILL_DIFFERENCE_MORE_ZERO_ERROR);
		}
		
		if(Tools.hv(outTradeNo))
		{
			PosCustomerOperateListEntity customerOperate = acewillCustomerService.getCustomerOperateListByAcewillXF(tenancyId, storeId, cardCode, billNum, outTradeNo);
			if (null != customerOperate && SysDictionary.CUSTOMER_OPERATE_STATE_PROCESS.equals(customerOperate.getOperate_state()))
			{
				throw SystemException.getInstance(PosErrorCode.BILL_PAYMENT_STATE_PAYING_ERROR);
			}
			else if (null != customerOperate && SysDictionary.CUSTOMER_OPERATE_STATE_SUCCESS.equals(customerOperate.getOperate_state()) && cardCode.equals(customerOperate.getCard_code()))
			{
				throw SystemException.getInstance(PosErrorCode.ONLY_CARD_PAYMENT);
			}
		}
		
		Timestamp currentTime = DateUtil.currentTimestamp();
//		String payNo = String.valueOf(currentTime.getTime());
		outTradeNo = ParamUtil.getOutTradeNoForAcewill(billNum, currentTime);
		
//		List<JSONObject> oldPaymentList = paymentDao.getBillPaymentByBillnum(tenancyId, storeId, billNum);
//		for(JSONObject paymentJson:oldPaymentList)
//		{
//			if(SysDictionary.PAYMENT_CLASS_WLIFE_BALANCE.equals(paymentJson.optString("payment_class"))||SysDictionary.PAYMENT_CLASS_WLIFE_CREDIT.equals(paymentJson.optString("payment_class"))||SysDictionary.PAYMENT_CLASS_WLIFE_COUPON.equals(paymentJson.optString("payment_class")))
//			{
//				throw SystemException.getInstance(PosErrorCode.ONLY_CARD_PAYMENT);
//			}
//		}
		
		List<PosBillPayment> paymentList = new ArrayList<PosBillPayment>();
		List<PosBillPaymentCoupons> paymentCouponList = new ArrayList<PosBillPaymentCoupons>();
		if (null != subBalance && subBalance > 0)
		{
			JSONObject paymentWayJson = paymentDao.getPaymentWayByPaymentClass(tenancyId, storeId, SysDictionary.PAYMENT_CLASS_WLIFE_BALANCE);
			if (null != paymentWayJson && !paymentWayJson.isEmpty())
			{
				String type = paymentWayJson.optString("payment_class");
				Integer jzid = paymentWayJson.optInt("payment_id");
				String name = paymentWayJson.optString("payment_name");
				String nameEnglish = paymentWayJson.optString("name_english");
				Double rate = paymentWayJson.optDouble("rate");

				PosBillPayment cardPayment = new PosBillPayment();
				cardPayment.setReport_date(reportDate);
				cardPayment.setShift_id(shiftId);
				cardPayment.setPos_num(posNum);
				cardPayment.setCashier_num(optNum);
				cardPayment.setTable_code(tableCode);
				cardPayment.setBill_num(billNum);
				cardPayment.setBatch_num(batchNum);
				cardPayment.setType(type);
				cardPayment.setJzid(jzid);
				cardPayment.setName(name);
				cardPayment.setName_english(nameEnglish);
				cardPayment.setRate(rate);
				cardPayment.setIs_ysk("Y");
				cardPayment.setAmount(subBalance);
				cardPayment.setCount(1);
				cardPayment.setCurrency_amount(subBalance);
				cardPayment.setNumber(cardCode);
				cardPayment.setMore_coupon(0d);
				cardPayment.setLast_updatetime(currentTime);
				cardPayment.setRemark(remark);
				cardPayment.setPayment_state(paymentState);
				cardPayment.setPhone(mobil);
				cardPayment.setOut_trade_no(outTradeNo);
				JSONObject paramCachJson = new JSONObject();
				paramCachJson.put("card_code", cardCode);
				paramCachJson.put("mobil", mobil);
				paramCachJson.put("customer_name", customerName);
				paramCachJson.put("level_name", levelName);
				paramCachJson.put("charge_return_credit", chargeReturnCredit);
				cardPayment.setParam_cach(paramCachJson.toString());
				paymentList.add(cardPayment);
			}
			else
			{
				throw SystemException.getInstance(PosErrorCode.NOT_EXIST_JZID);
			}
			
			difference = DoubleHelper.sub(difference, subBalance, DEFAULT_SCALE);
			if (difference < 0)
			{
				throw SystemException.getInstance(PosErrorCode.BILL_DIFFERENCE_LESS_ZERO_ERROR);
			}
		}

		if (null != subCredit && subCredit > 0)
		{
			JSONObject paymentWayJson = paymentDao.getPaymentWayByPaymentClass(tenancyId, storeId, SysDictionary.PAYMENT_CLASS_WLIFE_CREDIT);
			if (null != paymentWayJson && !paymentWayJson.isEmpty())
			{
				String type = paymentWayJson.optString("payment_class");
				Integer jzid = paymentWayJson.optInt("payment_id");
				String name = paymentWayJson.optString("payment_name");
				String nameEnglish = paymentWayJson.optString("name_english");
				Double rate = paymentWayJson.optDouble("rate");
				
				if (null == subCreditAmount || subCreditAmount <= 0d)
				{
					if (null == creditDeduct || creditDeduct <= 0d)
					{
						creditDeduct = INTEGRAL_CASH_RATE;
					}
					// '积分÷商家设置的多少积分抵1元' ='抵现付款金额'
					subCreditAmount = DoubleHelper.div(subCredit, creditDeduct, AMOUNT_SCALE);
				}

				PosBillPayment creditPayment = new PosBillPayment();
				creditPayment.setReport_date(reportDate);
				creditPayment.setShift_id(shiftId);
				creditPayment.setPos_num(posNum);
				creditPayment.setCashier_num(optNum);
				creditPayment.setTable_code(tableCode);
				creditPayment.setBill_num(billNum);
				creditPayment.setBatch_num(batchNum);
				creditPayment.setType(type);
				creditPayment.setJzid(jzid);
				creditPayment.setName(name);
				creditPayment.setName_english(nameEnglish);
				creditPayment.setRate(rate);
				creditPayment.setIs_ysk("Y");
				creditPayment.setAmount(subCreditAmount);
				creditPayment.setCount(1);
				creditPayment.setCurrency_amount(subCreditAmount);
				creditPayment.setNumber(cardCode);
				creditPayment.setMore_coupon(0d);
				creditPayment.setLast_updatetime(currentTime);
				creditPayment.setRemark(remark);
				creditPayment.setPayment_state(paymentState);
				creditPayment.setPhone(mobil);
				creditPayment.setOut_trade_no(outTradeNo);
				JSONObject paramCachJson = new JSONObject();
				paramCachJson.put("card_code", cardCode);
				paramCachJson.put("mobil", mobil);
				paramCachJson.put("customer_name", customerName);
				paramCachJson.put("level_name", levelName);
				paramCachJson.put("credit_deduct",creditDeduct);
				creditPayment.setParam_cach(paramCachJson.toString());
				paymentList.add(creditPayment);
			}
			else
			{
				throw SystemException.getInstance(PosErrorCode.NOT_EXIST_JZID);
			}
			
			difference = DoubleHelper.sub(difference, subCreditAmount, DEFAULT_SCALE);
			if (difference < 0)
			{
				throw SystemException.getInstance(PosErrorCode.BILL_DIFFERENCE_LESS_ZERO_ERROR);
			}
		}

		if (null != coupons && coupons.size() > 0)
		{
			String type = null;
			Integer jzid = null;
			String name = null;
			String nameEnglish = null;
			Double rate = null;
			JSONObject paymentWayJson = paymentDao.getPaymentWayByPaymentClass(tenancyId, storeId, SysDictionary.PAYMENT_CLASS_WLIFE_COUPON);
			if (null != paymentWayJson && !paymentWayJson.isEmpty())
			{
				type = paymentWayJson.optString("payment_class");
				jzid = paymentWayJson.optInt("payment_id");
				name = paymentWayJson.optString("payment_name");
				nameEnglish = paymentWayJson.optString("name_english");
				rate = paymentWayJson.optDouble("rate");
			}
			else
			{
				throw SystemException.getInstance(PosErrorCode.NOT_EXIST_JZID);
			}
			
			//查询账单菜品
			List<JSONObject> itemList = paymentDao.getPosBillItemForSingleByBillNum(tenancyId, storeId, billNum);
			Map<String, JSONObject> itemMap = new HashMap<String, JSONObject>();
			if(null!= itemList)
			{
				for(JSONObject itemJson:itemList)
				{
					itemMap.put(itemJson.optString("rwid"), itemJson);
				}
			}

			JSONObject couponJson = null;
			Double currencyAmount = 0d;
			String couponCode = null;
			Integer couponType = null;
			Double couponDeno = 0d;
			Integer templateId = null;
			String couponName = null;
			String couponsPro = null;
			Integer couponRwid = null;
			PosBillPayment couponPayment = null;
			for (Object couponObj : coupons)
			{
//				if (difference < 0)
//				{
//					throw SystemException.getInstance(PosErrorCode.BILL_DIFFERENCE_LESS_ZERO_ERROR);
//				}

				couponJson = JSONObject.fromObject(couponObj);
				currencyAmount = ParamUtil.getDoubleValueByObject(couponJson, "coupon_amount");
				couponCode = ParamUtil.getStringValueByObject(couponJson, "coupon_code");
				couponType = ParamUtil.getIntegerValueByObject(couponJson, "coupon_type");
				couponDeno = ParamUtil.getDoubleValueByObject(couponJson, "coupon_deno");
				templateId = ParamUtil.getIntegerValueByObject(couponJson, "template_id");
				couponName = ParamUtil.getStringValueByObject(couponJson, "coupon_name");
				couponRwid = ParamUtil.getIntegerValueByObject(couponJson, "rwid");
				if(Tools.hv(couponType)){
					if (SysDictionary.CUSTOMER_COUPON_TYPE_CASH.equals(couponType.toString()))
					{
						couponsPro= SysDictionary.COUPONS_PRO_DEDUCT;
					}
					else  if (SysDictionary.CUSTOMER_COUPON_TYPE_DISH.equals(couponType.toString()))
					{
						couponsPro= SysDictionary.COUPONS_PRO_DISH;
					}
				}

				couponPayment = new PosBillPayment();
				couponPayment.setReport_date(reportDate);
				couponPayment.setShift_id(shiftId);
				couponPayment.setPos_num(posNum);
				couponPayment.setCashier_num(optNum);
				couponPayment.setTable_code(tableCode);
				couponPayment.setBill_num(billNum);
				couponPayment.setBatch_num(batchNum);
				couponPayment.setType(type);
				couponPayment.setJzid(jzid);
				couponPayment.setName(name);
				couponPayment.setName_english(nameEnglish);
				couponPayment.setRate(rate);
				couponPayment.setIs_ysk("Y");
				couponPayment.setAmount(currencyAmount);
				couponPayment.setCount(1);
				couponPayment.setCurrency_amount(currencyAmount);
				couponPayment.setNumber(couponCode);
				couponPayment.setCoupon_type(SysDictionary.COUPON_TYPE_CODE);
				couponPayment.setMore_coupon(0d);
				couponPayment.setLast_updatetime(currentTime);
				couponPayment.setRemark(remark);
				couponPayment.setPayment_state(paymentState);
				couponPayment.setPhone(mobil);
				couponPayment.setOut_trade_no(outTradeNo);
				
				JSONObject paramCachJson = new JSONObject();
				paramCachJson.put("card_code", cardCode);
				paramCachJson.put("mobil", mobil);
				paramCachJson.put("customer_name", customerName);
				paramCachJson.put("level_name", levelName);
				paramCachJson.putAll(couponJson);
				couponPayment.setParam_cach(paramCachJson.toString());
				
				String paymentUid = Md5Utils.md5(JSONObject.fromObject(couponPayment).toString());
				couponPayment.setPayment_uid(paymentUid);

				paymentList.add(couponPayment);

				PosBillPaymentCoupons paymentCoupon = new PosBillPaymentCoupons(tenancyId, storeId, billNum, reportDate, paymentUid, couponCode, couponDeno, couponName, currentTime, null,templateId, templateId, currencyAmount, 1.0, SysDictionary.CHANEL_MD01, couponsPro, SysDictionary.COUPON_TYPE_CODE);
				paymentCoupon.setTenancy_assume(currencyAmount);
				if(SysDictionary.CUSTOMER_COUPON_TYPE_DISH.equals(couponType.toString()) && itemMap.containsKey(String.valueOf(couponRwid))){
					JSONObject itemInfo = itemMap.get(String.valueOf(couponRwid));
					paymentCoupon.setPrice(ParamUtil.getDoubleValueByObject(itemInfo, "item_price"));
					paymentCoupon.setItem_id(ParamUtil.getIntegerValueByObject(itemInfo, "item_id"));
					paymentCoupon.setItem_num(ParamUtil.getIntegerValueByObject(itemInfo, "item_count"));
					paymentCoupon.setItem_unit_id(ParamUtil.getIntegerValueByObject(itemInfo, "item_unit_id"));
					paymentCoupon.setRwid(couponRwid);
				}
				paymentCouponList.add(paymentCoupon);
				
				difference = DoubleHelper.sub(difference, currencyAmount, DEFAULT_SCALE);
			}
		}

//		//判断付款差额,
//		if (difference > 0)
//		{
//			throw SystemException.getInstance(PosErrorCode.BILL_DIFFERENCE_MORE_ZERO_ERROR);
//		}
		
		if (paymentList.size() > 0)
		{
			paymentDao.batchInsertPodBillPayment(tenancyId, storeId, paymentList);
		}

		if (paymentCouponList.size() > 0)
		{
			paymentDao.insertPosBillPaymentCoupons(tenancyId, storeId, paymentCouponList);
		}

		//修改账单付款状态 transfer_remark
		JSONObject remarkJson = new JSONObject();
		remarkJson.put("isprint_bill", isPrintBill);
		remarkJson.put("is_invoice", isInvoice);

		paymentDao.updatePosBillForStateByBillnum(tenancyId, storeId, billNum, optNum, posNum, SysDictionary.PAYMENT_STATE_PAY, difference, currentTime, remarkJson.toString(), outTradeNo);
	
		return outTradeNo;
	}

	/** 删除付款记录
	 * @param tenancyId
	 * @param storeId
	 * @param paramJson
	 * @throws Exception
	 */
	public void deletePosBillPaymentForAcewillPay(String tenancyId, int storeId, JSONObject paramJson) throws Exception
	{
		String billNum = ParamUtil.getStringValueByObject(paramJson, "bill_num");

		String payNo = null;
		Double difference = 0d;
		Double paymentAmount = 0d;
		String transferRemark = null;
		JSONObject billJson = paymentDao.getPosBillByBillnum(tenancyId, storeId, billNum);
		if (null != billJson)
		{
			payNo = ParamUtil.getStringValueByObject(billJson, "pay_no");
			transferRemark = ParamUtil.getStringValueByObject(billJson, "transfer_remark");
			difference = ParamUtil.getDoubleValueByObject(billJson, "difference");
			paymentAmount = ParamUtil.getDoubleValueByObject(billJson, "payment_amount");
		}

		List<JSONObject> billPaymentList = paymentDao.getPosBillPaymentByBillnum(tenancyId, storeId, billNum);
		List<PosBillPayment> paymentList = new ArrayList<PosBillPayment>();
		Double amount = 0d;
		String optNum = null;
		String posNum = null;
		String paymentState = SysDictionary.PAYMENT_STATE_NOTPAY;
		Timestamp paymentTime = null;
		Timestamp lastUpdatetime = null;
		for (JSONObject paymentJson : billPaymentList)
		{
			if (payNo.equals(paymentJson.optString("out_trade_no")))
			{
				paymentJson.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_FAILURE);
				// PosBillPayment payment = (PosBillPayment)
				// JSONObject.toBean(paymentJson, PosBillPayment.class);
				PosBillPayment payment = PosBillPayment.getBean(paymentJson);
				paymentList.add(payment);
			}
			else
			{
				// 计算付款金额
				amount = DoubleHelper.add(amount, ParamUtil.getDoubleValueByObject(paymentJson, "currency_amount"), DEFAULT_SCALE);

				if (Tools.hv(paymentState) && SysDictionary.PAYMENT_STATE_NOTPAY.equals(paymentState) == false)
				{
					if (SysDictionary.PAYMENT_STATE_PAY.equals(paymentJson.optString("payment_state")))
					{
						paymentState = SysDictionary.PAYMENT_STATE_PAY;
					}
					else if (SysDictionary.PAYMENT_STATE_PAY_COMPLETE.equals(paymentJson.optString("payment_state")) && SysDictionary.PAYMENT_STATE_PAY.equals(paymentState) == false)
					{
						paymentState = SysDictionary.PAYMENT_STATE_PAY_COMPLETE;
					}
				}
				else
				{
					paymentState = paymentJson.optString("payment_state");
					if (SysDictionary.PAYMENT_STATE_NOTPAY.equals(paymentState))
					{
						paymentState = SysDictionary.PAYMENT_STATE_PAY;
					}
				}

				lastUpdatetime = DateUtil.formatTimestamp(paymentJson.optString("last_updatetime"));
				if (Tools.isNullOrEmpty(paymentTime) || lastUpdatetime.after(paymentTime))
				{
					posNum = paymentJson.optString("pos_num");
					optNum = paymentJson.optString("cashier_num");
					paymentTime = lastUpdatetime;
				}
			}
		}

		paymentDao.insertPosBillPaymentLogByBillmum(tenancyId, storeId, billNum, payNo, SysDictionary.PAYMENT_STATE_PAY_FAILURE, null);
		
		// 删除付款优惠券明细
		paymentDao.deleteBillPaymentCouponsByBillNum(tenancyId, storeId, billNum, payNo);
		
		// 删除付款
		paymentDao.deletePosBillPaymentByBillmum(tenancyId, storeId, billNum, payNo);

//		// 新增历史记录
//		paymentDao.batchInsertPosBillPaymentLog(tenancyId, storeId, paymentList);

		// 修改账单
		difference = DoubleHelper.sub(paymentAmount, amount, DEFAULT_SCALE);
		if (SysDictionary.PAYMENT_STATE_PAY_COMPLETE.equals(paymentState) && difference > 0)
		{
			paymentState = SysDictionary.PAYMENT_STATE_NOTPAY;
		}
		paymentDao.updatePosBillForStateByBillnum(tenancyId, storeId, billNum, optNum, posNum, paymentState, difference, paymentTime, transferRemark, null);
	}

	@Override
	public Data requestAcewillCustomerDealPreview(String tenancyId, int storeId, JSONObject paramJson) throws Exception
	{
		String billNum = ParamUtil.getStringValueByObject(paramJson, "bill_num", true, PosErrorCode.NOT_NULL_BILL_NUM);
		String outTradeNo = ParamUtil.getStringValueByObject(paramJson, "out_trade_no", true, PosErrorCode.NOT_NULL_BILL_NUM);
		String reportDate = ParamUtil.getStringValueByObject(paramJson, "report_date");
		Integer shiftId = ParamUtil.getIntegerValueByObject(paramJson, "shift_id");
		String posNum = ParamUtil.getStringValueByObject(paramJson, "pos_num");
		String optNum = ParamUtil.getStringValueByObject(paramJson, "opt_num");
//		String cardCode = ParamUtil.getStringValueByObject(paramJson, "card_code");
//		String mobil = ParamUtil.getStringValueByObject(paramJson, "mobil");
//		String customerName = ParamUtil.getStringValueByObject(paramJson, "customer_name");
//		String levelName = ParamUtil.getStringValueByObject(paramJson, "level_name");
		Double subBalance = ParamUtil.getDoubleValueByObject(paramJson, "sub_balance");// 消费使用储值金额
		Double subCredit = ParamUtil.getDoubleValueByObject(paramJson, "sub_credit");// 消费使用积分抵现的积分数
//		Double creditDeduct = ParamUtil.getDoubleValueByObject(paramJson, "credit_deduct");// 商家设置的多少积分抵1元 20 即20积分抵1元
//		Double subCreditAmount = ParamUtil.getDoubleValueByObject(paramJson, "sub_credit_amount");// 消费使用积分抵现的积分金额 单位元
//		String chargeReturnCredit = ParamUtil.getStringValueByObject(paramJson, "charge_return_credit");// 消费使用积分抵现
//		JSONArray coupons = null;
//		if (paramJson.containsKey("coupon_ids"))
//		{
//			coupons = paramJson.getJSONArray("coupon_ids");
//		}
//		String remark = ParamUtil.getStringValueByObject(paramJson, "remark");

		Double billPaymentAmount = 0d;// 账单总金额
		String batchNum = null;
//		String orderNum = null;
		String chanel = null;
		Double difference = 0d;
		String tableCode = null;
		Double discountAmount = 0d;
		//服务员
		String waiterNum = null;
		String payNo = null;
		JSONObject billJson = paymentDao.getPosBillByBillnum(tenancyId, storeId, billNum);
		if (null != billJson)
		{
			billPaymentAmount = billJson.optDouble("payment_amount");
			batchNum = billJson.optString("batch_num");
			chanel = billJson.optString("source");
			batchNum = billJson.optString("batch_num");
//			orderNum = billJson.optString("order_num");
			tableCode = billJson.optString("table_code");
			discountAmount = billJson.optDouble("discount_amount");
			waiterNum = billJson.optString("waiter_num");
			payNo = billJson.optString("pay_no");
		}

		//查询付款明细
		List<JSONObject> billPaymentList = paymentDao.getPosBillPaymentByBillnum(tenancyId, storeId, billNum);

		//获取请求参数
		JSONObject requestJson = this.getPreviewDealParamJson(tenancyId, storeId, billNum, outTradeNo, chanel, billPaymentAmount, billPaymentList);
		
		List<JSONObject> requestList = new ArrayList<JSONObject>();
		requestList.add(requestJson);
		Data requestData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
		requestData.setData(requestList);

		// 请求消费预览
		Data responseData = acewillCustomerService.previewAcewillCustomerDeal(requestData);

		JSONObject resultJson = null;
		if (Constant.CODE_SUCCESS == responseData.getCode())
		{
			// 成功返回付款信息
			resultJson = JSONObject.fromObject(responseData.getData().get(0));
			String billCode = ParamUtil.getStringValueByObject(resultJson, "tcid");
			paymentDao.updatePosBillPaymentForPaymentState(tenancyId, storeId, billNum, payNo, SysDictionary.PAYMENT_STATE_PAY, billCode);
			
			resultJson.put("is_commit", "1");
		}
		else
		{
			resultJson = new JSONObject();
			this.deletePosBillPaymentForAcewillPay(tenancyId, storeId, paramJson);
		}

		Double creditAmount = ParamUtil.getDoubleValueByObject(requestJson, "credit_amount");
		String optName = paymentDao.getEmpNameById(optNum, tenancyId, storeId);
		StringBuffer newstate = new  StringBuffer();
		newstate.append("账单金额:").append(billPaymentAmount).append(";");
		newstate.append("储值消费金额:").append(subBalance).append(";");
		newstate.append("积分消费:").append(subCredit).append(";");
		newstate.append("消费可积分金额:").append(creditAmount).append(";");
		paymentDao.savePosLog(tenancyId, storeId, posNum, optNum, optName, shiftId, DateUtil.parseDate(reportDate), Constant.TITLE, "微生活预消费", "账单编号:"+billNum, newstate.toString());
		
		resultJson.putAll(this.getPosBillPaymentByBillNum(tenancyId, storeId, billNum, tableCode, billPaymentAmount, discountAmount, difference, ""));
		
		List<JSONObject> resultList = new ArrayList<JSONObject>();
		resultList.add(resultJson);
		responseData.setData(resultList);
		return responseData;
	}
	
	@Override
	public JSONObject getPreviewDealParamJson(String tenancyId, int storeId, String billNum, String outTradeNo, String chanel, Double billPaymentAmount, List<JSONObject> paymentList) throws Exception
	{
		String reportDate = null;
		Integer shiftId = null;
		String posNum = null;
		String optNum = null;
		String batchNum = null;
		String tableCode = null;

		String cardCode = null;
		String thirdCode = null;
		String mobil = null;
		String customerName = null;
		String levelName = null;
		Double subBalance = 0d;
		Double subCredit = 0d;
		Double subCreditAmount = 0d;
		Double creditDeduct = INTEGRAL_CASH_RATE;

		// 使用的代金券列表
		List<String> denoCouponList = new ArrayList<String>();
		// 使用的礼品券列表
		List<String> giftCouponsList = new ArrayList<String>();
		// 使用的可自定义金额的礼品券列表
		List<JSONObject> diyGiftCouponsList = new ArrayList<JSONObject>();

		Double couponsAmount = 0d;// 优惠劵金额
		Double paymentAmount = 0d;
		Integer paymentMode = null;
		List<JSONObject> productsList = null;
		Double paymentAmountTotal = 0d;
		Double cashAmount = 0d;//现金金额
		if (null != paymentList)
		{
			Map<String, Double> itemNumPriceMap = new HashMap<String, Double>();
			Map<String, Double> itemNumCountMap = new HashMap<String, Double>();
			Map<String, List<String>> itemNumCouponsMap = new HashMap<String, List<String>>();
			
			for (JSONObject paymentJson : paymentList)
			{
				String paymentClass = ParamUtil.getStringValueByObject(paymentJson, "payment_class");
				Double currencyAmount = ParamUtil.getDoubleValueByObject(paymentJson, "currency_amount");
				paymentAmountTotal = DoubleHelper.padd(paymentAmountTotal, currencyAmount);

				if (SysDictionary.PAYMENT_CLASS_WLIFE_BALANCE.equals(paymentClass) || SysDictionary.PAYMENT_CLASS_WLIFE_CREDIT.equals(paymentClass) || SysDictionary.PAYMENT_CLASS_WLIFE_COUPON.equals(paymentClass))
				{
					if (outTradeNo.equals(paymentJson.optString("out_trade_no")))
					{
						reportDate = ParamUtil.getStringValueByObject(paymentJson, "report_date");
						shiftId = ParamUtil.getIntegerValueByObject(paymentJson, "shift_id");
						posNum = ParamUtil.getStringValueByObject(paymentJson, "pos_num");
						optNum = ParamUtil.getStringValueByObject(paymentJson, "cashier_num");
						batchNum = ParamUtil.getStringValueByObject(paymentJson, "batch_num");
						tableCode = ParamUtil.getStringValueByObject(paymentJson, "table_code");

						JSONObject paramCashJson = ParamUtil.getJSONObject(paymentJson, "param_cach");
						cardCode = ParamUtil.getStringValueByObject(paramCashJson, "card_code");
						thirdCode = ParamUtil.getStringValueByObject(paramCashJson, "third_code");
						mobil = ParamUtil.getStringValueByObject(paramCashJson, "mobil");
						customerName = ParamUtil.getStringValueByObject(paramCashJson, "customer_name");
						levelName = ParamUtil.getStringValueByObject(paramCashJson, "level_name");

						if (SysDictionary.PAYMENT_CLASS_WLIFE_BALANCE.equals(paymentClass))
						{
							subBalance = ParamUtil.getDoubleValueByObject(paymentJson, "currency_amount");
						}
						else if (SysDictionary.PAYMENT_CLASS_WLIFE_CREDIT.equals(paymentClass))
						{
							subCredit = ParamUtil.getDoubleValueByObject(paymentJson, "amount");
							subCreditAmount = ParamUtil.getDoubleValueByObject(paymentJson, "currency_amount");
							creditDeduct = ParamUtil.getDoubleValueByObject(paramCashJson, "credit_deduct");
						}
						else if (SysDictionary.PAYMENT_CLASS_WLIFE_COUPON.equals(paymentClass))
						{
							Double couponAmount = paramCashJson.optDouble("coupon_amount");
							String itemNum = paramCashJson.optString("item_num");
							String couponCode = paramCashJson.optString("coupon_code");
							String couponType = paramCashJson.optString("coupon_type");
							String isDiyDeno = paramCashJson.optString("is_diy_deno");

							if (SysDictionary.CUSTOMER_COUPON_TYPE_CASH.equals(couponType))
							{
								denoCouponList.add(couponCode);
								couponsAmount = DoubleHelper.add(couponsAmount, couponAmount, DEFAULT_SCALE);
							}
							else
							{
								if (Tools.hv(itemNum) && couponAmount > (itemNumPriceMap.containsKey(itemNum) ? itemNumPriceMap.get(itemNum) : 0d))
								{
									itemNumPriceMap.put(itemNum, couponAmount);
								}

								if (Tools.hv(itemNum))
								{
									Double itemCount = 1d;
									if (itemNumCountMap.containsKey(itemNum))
									{
										itemCount = itemNumCountMap.get(itemNum) + 1d;
									}
									itemNumCountMap.put(itemNum, itemCount);
								}
								else
								{
									couponsAmount = DoubleHelper.add(couponsAmount, couponAmount, DEFAULT_SCALE);
								}

								// 菜品抵扣的菜品券
								List<String> couponsList = null;
								if (itemNumCouponsMap.containsKey(itemNum))
								{
									couponsList = itemNumCouponsMap.get(itemNum);
									if (false == couponsList.contains(couponCode))
									{
										couponsList.add(couponCode);
										itemNumCouponsMap.put(itemNum, couponsList);
									}
								}
								else
								{
									couponsList = new ArrayList<String>();
									couponsList.add(couponCode);
									itemNumCouponsMap.put(itemNum, couponsList);
								}

								// 是否自定义金额的礼品券
								if ("1".equals(isDiyDeno))
								{
									JSONObject diyGiftCoupon = new JSONObject();
									diyGiftCoupon.put("user_coupon_id", couponCode);
									diyGiftCoupon.put("deno", couponAmount);
									diyGiftCouponsList.add(diyGiftCoupon);
								}
								else
								{
									giftCouponsList.add(couponCode);
								}
							}
						}
					}else 
					{
						paymentAmount = DoubleHelper.padd(paymentAmount, currencyAmount);
						paymentMode = Integer.valueOf(PaymentWayEnum.OTHER.getLabel());
					}
				}
				else
				{
					paymentAmount = DoubleHelper.padd(paymentAmount, currencyAmount);
					if (null == paymentMode)
					{
						paymentMode = this.getPaymentMode(paymentClass);
					}
					else if (paymentMode != this.getPaymentMode(paymentClass))
					{
						paymentMode = Integer.valueOf(PaymentWayEnum.OTHER.getLabel());
					}
					
					if (SysDictionary.PAYMENT_CLASS_CASH.equals(paymentClass))
					{
						cashAmount = DoubleHelper.add(cashAmount, currencyAmount, DEFAULT_SCALE);
					}
				}
			}
			
			//
			for (String key : itemNumCountMap.keySet())
			{
				if (itemNumPriceMap.containsKey(key))
				{
					Double couponAmount = DoubleHelper.mul(itemNumCountMap.get(key), itemNumPriceMap.get(key), DEFAULT_SCALE);
					couponsAmount = DoubleHelper.add(couponsAmount, couponAmount, DEFAULT_SCALE);
				}
			}

			productsList = this.getProductsList(tenancyId, storeId, billNum, itemNumPriceMap, itemNumCouponsMap);
		}

		Double difference = DoubleHelper.psub(billPaymentAmount, paymentAmountTotal);
		if (0d > difference)
		{
			//找零金额
			Double changeAmount = this.getChangeAmount(difference, cashAmount);
			paymentAmount = DoubleHelper.sub(paymentAmount, Math.abs(changeAmount), DEFAULT_SCALE);
		}else if (0d < difference)
		{
			paymentAmount = DoubleHelper.add(paymentAmount, difference, DEFAULT_SCALE);
			paymentMode = Integer.valueOf(PaymentWayEnum.OTHER.getLabel());
		}
		
		if (null == paymentMode)
		{
			paymentMode = Integer.valueOf(PaymentWayEnum.OTHER.getLabel());
		}
		
		Double consumeAmount = DoubleHelper.add(paymentAmount, subBalance, DEFAULT_SCALE);
		consumeAmount = DoubleHelper.add(consumeAmount, subCreditAmount, DEFAULT_SCALE);
		consumeAmount = DoubleHelper.add(consumeAmount, couponsAmount, DEFAULT_SCALE);

		Double creditAmount = this.getCreditAmount(outTradeNo, billPaymentAmount, paymentList);
//		Double activityAmount = paymentAmount;//creditAmount
		Double activityAmount = billPaymentAmount;

		Double activityExcludeAmount=0d;

		/*List<String> activityExcludePaymentTypeList = getExcludePaymentTypeList(tenancyId, storeId);

		String activityExcludePaymentStr="";

		if(activityExcludePaymentType!=null&&activityExcludePaymentType.length>0){
			for(String activityExcludePayment:activityExcludePaymentType){
				activityExcludePaymentStr+=",'"+activityExcludePayment+"'";
			}
		}

		if(CollectionUtils.isNotEmpty(activityExcludePaymentTypeList)){
			for(String activityExcludePayment:activityExcludePaymentTypeList){
				activityExcludePaymentStr+=",'"+activityExcludePayment+"'";
			}
		}

		if (StringUtils.isNotEmpty(activityExcludePaymentStr)) {

			activityExcludePaymentStr = activityExcludePaymentStr.replaceFirst(",", "");

			StringBuilder activityExcludeSql = new StringBuilder();

			activityExcludeSql.append("SELECT COALESCE(SUM(currency_amount), 0) AS no_active_amount ");
			activityExcludeSql.append("FROM pos_bill_payment P ");
			activityExcludeSql.append("WHERE EXISTS ( ");
			activityExcludeSql.append("SELECT T.payment_class ");
			activityExcludeSql.append("FROM (SELECT pw.payment_class FROM payment_way_of_ogran pwo LEFT JOIN payment_way pw ON pwo.payment_id = pw.ID WHERE pwo.is_rebate_coupons = '0') T ");
			activityExcludeSql.append("WHERE P.TYPE = T.payment_class ");
			activityExcludeSql.append(") ");
			activityExcludeSql.append("AND P.bill_num = '35520220901000001'");

			List<JSONObject> jsonObjects = paymentDao.query4Json(null, activityExcludeSql.toString());
			if (CollectionUtils.isNotEmpty(jsonObjects)) {
				activityExcludeAmount = jsonObjects.get(0).optDouble("no_active_amount");
			}

			//不参与返券的金额
			activityAmount -= activityExcludeAmount;
		}*/

		StringBuilder activityExcludeSql = new StringBuilder();

		activityExcludeSql.append("SELECT COALESCE(SUM(currency_amount), 0) AS no_active_amount ");
		activityExcludeSql.append("FROM pos_bill_payment P ");
		activityExcludeSql.append("WHERE EXISTS ( ");
		activityExcludeSql.append("SELECT T.payment_class ");
		activityExcludeSql.append("FROM (SELECT pw.payment_class FROM payment_way_of_ogran pwo LEFT JOIN payment_way pw ON pwo.payment_id = pw.ID WHERE pwo.is_rebate_coupons = '0') T ");
		activityExcludeSql.append("WHERE P.TYPE = T.payment_class ");
		activityExcludeSql.append(") ");
		activityExcludeSql.append("AND P.bill_num = '"+billNum+"'");

		List<JSONObject> jsonObjects = paymentDao.query4Json(null, activityExcludeSql.toString());
		if (CollectionUtils.isNotEmpty(jsonObjects)) {
			activityExcludeAmount = jsonObjects.get(0).optDouble("no_active_amount");
		}

		//不参与返券的金额
		activityAmount -= activityExcludeAmount;



		// 查询服务员信息
		JSONObject employee = paymentDao.getEmployees(tenancyId, optNum);
		if (null != employee)
		{
			employee.put("emp_avatar", PosPropertyUtil.getMsg(com.tzx.clientorder.wlifeprogram.common.constant.Constant.EMPLOYEE_IMG_URL));
		}
		List<JSONObject> employees = new ArrayList<JSONObject>();
		if (Tools.hv(employee))
		{
			employees.add(employee);
		}

		JSONObject requestJson = new JSONObject();
		requestJson.put("third_bill_code", billNum);
		requestJson.put("out_trade_no", outTradeNo);
		requestJson.put("report_date", reportDate);
		requestJson.put("shift_id", shiftId);
		requestJson.put("pos_num", posNum);
		requestJson.put("opt_num", optNum);
		requestJson.put("chanel", chanel);
		requestJson.put("batch_num", batchNum);
		requestJson.put("card_code", cardCode);
		requestJson.put("third_code", thirdCode);
		requestJson.put("mobil", mobil);
		requestJson.put("customer_name", customerName);
		requestJson.put("level_name", levelName);
		requestJson.put("consume_amount", consumeAmount);
		requestJson.put("payment_amount", paymentAmount); //subBalance
		requestJson.put("payment_mode", paymentMode);
		requestJson.put("sub_balance", subBalance);
		requestJson.put("sub_credit", subCredit);
		requestJson.put("sub_credit_amount", subCreditAmount);
		requestJson.put("credit_deduct", creditDeduct);
		requestJson.put("credit_amount", creditAmount);
		requestJson.put("activity_amount", activityAmount);
		requestJson.put("deno_coupon_ids", denoCouponList);
		requestJson.put("gift_coupons_ids", giftCouponsList);
		requestJson.put("diy_gift_coupon_pay", diyGiftCouponsList);
		requestJson.put("products", productsList);
		requestJson.put("activity_ids", "[]");
		requestJson.put("count_num", "0");
		requestJson.put("remark", "");
		requestJson.put("employees", employees);
		requestJson.put("table_id", tableCode);

		return requestJson;
	}

	/**
	 * 获取活动排除支付方式
	 * @param tenancyId
	 * @param storeId
	 * @return
	 * @throws Exception
	 */
	private List<String> getExcludePaymentTypeList(String tenancyId, int storeId) throws Exception {
		//活动排除支付方式
		//付款方式是否支持消费返券，1：是，0：否，默认值：1
		StringBuilder activityExcludePaymentSql=new StringBuilder();

		activityExcludePaymentSql.append("SELECT\n")
				.append("	pw.payment_class \n")
				.append("FROM\n")
				.append("	payment_way_of_ogran pwo\n")
				.append("	LEFT JOIN payment_way pw ON pwo.payment_id = pw.ID \n")
				.append("WHERE\n")
				.append("	pwo.is_rebate_coupons = '0' \n")
				.append("	AND pwo.organ_id = '"+ storeId +"' \n")
				.append("	AND pwo.tenancy_id = '"+ tenancyId +"' \n")
				.append("	AND pw.status = '1'");


		List<String> activityExcludePaymentTypeList=paymentDao.getJdbcTemplate(tenancyId)
				.queryForList(activityExcludePaymentSql.toString(),String.class);

		return activityExcludePaymentTypeList;
	}

	private Integer getPaymentMode(String paymentClass)
	{
		return Integer.valueOf(PaymentWayEnum.findLabel(paymentClass));
	}
	
	private List<JSONObject> getProductsList(String tenancyId, int storeId, String billNum, Map<String, Double> itemNumPriceMap, Map<String, List<String>> itemNumCouponsMap) throws Exception
	{
		List<JSONObject> productsList = new ArrayList<JSONObject>();
		Map<String,Integer> itemNumIndexMap = new HashMap<String, Integer>();
		List<JSONObject> itemList = paymentDao.getPosBillItemForSingleByBillNum(tenancyId, storeId, billNum);
		if (null != itemList && itemList.size() > 0)
		{
			for (JSONObject itemJson : itemList)
			{
				String itemName = ParamUtil.getStringValueByObject(itemJson, "item_name");
				String itemNum = ParamUtil.getStringValueByObject(itemJson, "item_num");
//				String itemRwid = ParamUtil.getStringValueByObject(itemJson, "rwid");
				Double itemCount = ParamUtil.getDoubleValueByObject(itemJson, "item_count");
				Double realAmount = ParamUtil.getDoubleValueByObject(itemJson, "real_amount");
				Double itemAmount =  ParamUtil.getDoubleValueByObject(itemJson, "item_amount");
				if(realAmount > itemAmount)
				{
					realAmount = itemAmount;
				}
				
				Double itemPrice = DoubleHelper.div(realAmount, itemCount, AMOUNT_SCALE);
				if (itemPrice.isNaN())
				{
					itemPrice = 0d;
				}
				
				if (itemNumPriceMap.containsKey(itemNum))
				{
					itemPrice = itemNumPriceMap.get(itemNum);
				}

				if (itemNumIndexMap.containsKey(itemNum))
				{
					JSONObject productJson = productsList.get(itemNumIndexMap.get(itemNum));
					Double itemCountT = DoubleHelper.add(itemCount, productJson.optDouble("num", 0), 4);
					productJson.put("num", itemCountT);
					productJson.put("price", itemPrice);
					productsList.set(itemNumIndexMap.get(itemNum), productJson);
				}
				else
				{
					itemNumIndexMap.put(itemNum, productsList.size());
					List<String> couponsIds = itemNumCouponsMap.get(itemNum);
					JSONObject productJson = new JSONObject();
					productJson.put("name", itemName);
					productJson.put("no", itemNum);
					productJson.put("num", itemCount);
					productJson.put("price", itemPrice);
					productJson.put("is_activity", "2");
					productJson.put("coupons_ids", couponsIds);
					productsList.add(productJson);
				}
			}
		}
		return productsList;
	}
	
	@Override
	public Data requestAcewillCustomerDealCommit(String tenancyId, int storeId, JSONObject paramJson, JSONObject printJson) throws Exception
	{
		String reportDateStr = ParamUtil.getStringValueByObject(paramJson, "report_date", true, PosErrorCode.NOT_NULL_REPORT_DATE);
		Date reportDate = DateUtil.parseDate(reportDateStr);
		Integer shiftId = ParamUtil.getIntegerValueByObject(paramJson, "shift_id");
		String posNum = ParamUtil.getStringValueByObject(paramJson, "pos_num");
		String optNum = ParamUtil.getStringValueByObject(paramJson, "opt_num");
		String billNum = ParamUtil.getStringValueByObject(paramJson, "bill_num", true, PosErrorCode.NOT_NULL_BILL_NUM);
		String verifyCode = ParamUtil.getStringValueByObject(paramJson, "verify_code");

		String transferRemark = null;
		String payNo = null;
		Double difference = 0d;
		String tableCode = null;
		Double paymentAmount=0d;
		Double discountAmount =0d;
		String paymentState = null;
//		String chanel = null;
//		String orderNum=null;
		String saleMode=null;

        String sbill_num = null;
		
		JSONObject billJson = paymentDao.getPosBillByBillnum(tenancyId, storeId, billNum);
		if (null != billJson)
		{
			transferRemark = ParamUtil.getStringValueByObject(billJson, "transfer_remark");
			payNo = ParamUtil.getStringValueByObject(billJson, "pay_no");
			tableCode = ParamUtil.getStringValueByObject(billJson, "fictitious_table");
			difference = ParamUtil.getDoubleValueByObject(billJson, "difference");
			paymentAmount = ParamUtil.getDoubleValueByObject(billJson, "payment_amount");
			discountAmount = ParamUtil.getDoubleValueByObject(billJson, "discount_amount");
			paymentState = ParamUtil.getStringValueByObject(billJson, "payment_state");
//			chanel =  ParamUtil.getStringValueByObject(billJson, "source");
//			orderNum =  ParamUtil.getStringValueByObject(billJson, "order_num");
			saleMode =  ParamUtil.getStringValueByObject(billJson, "sale_mode");
            sbill_num = billJson.optString("sbill_num");
		}
		
		JSONObject remarkJson = JSONObject.fromObject(transferRemark);
		String isPrint = remarkJson.optString("isprint_bill");
		String isInvoice = remarkJson.optString("is_invoice");

		Timestamp currentTime = DateUtil.currentTimestamp();

		JSONObject requestJson = new JSONObject();
		requestJson.put("out_trade_no", payNo);
		requestJson.put("third_bill_code", billNum);
		requestJson.put("verify_code", verifyCode);

		List<JSONObject> requestList = new ArrayList<JSONObject>();
		requestList.add(requestJson);

		Data requestData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
		requestData.setData(requestList);

		Data responseData = acewillCustomerService.commitAcewillCustomerDeal(requestData);

//		JSONObject resultJson = null;
		
		if (Constant.CODE_SUCCESS == responseData.getCode())
		{
			String billCode=null;
			String cardCode=null;
			String customerName=null;
			String mobil=null;
			String levelName=null;
			Double consumeAmount=null;
			Double consumeAfterBalance=null;
			Double receiveAfterCredit=null;
			Double receiveCredit =null;
			try
			{
				JSONObject responseJson = JSONObject.fromObject(responseData.getData().get(0));
				billCode = ParamUtil.getStringValueByObject(responseJson, "deal_id");
				cardCode = ParamUtil.getStringValueByObject(responseJson, "card_code");
				customerName = ParamUtil.getStringValueByObject(responseJson, "customer_name");
				mobil = ParamUtil.getStringValueByObject(responseJson, "mobil");
				levelName = ParamUtil.getStringValueByObject(responseJson, "level_name");

				consumeAmount = ParamUtil.getDoubleValueByObject(responseJson, "sub_balance");
				consumeAfterBalance = ParamUtil.getDoubleValueByObject(responseJson, "balance");
				receiveCredit = ParamUtil.getDoubleValueByObject(responseJson, "receive_credit");
				Double receiveBeforeCredit =ParamUtil.getDoubleValueByObject(responseJson, "credit");
				receiveAfterCredit = DoubleHelper.add(receiveBeforeCredit, receiveCredit, DEFAULT_SCALE);

				this.requestDealCommitSuccess(tenancyId, storeId, reportDate, billNum, payNo, paymentAmount, responseJson);

				// 检查付款差额,小于等于0,关闭账单
				if (difference <= 0)
				{
//					if (null == resultJson)
//					{
//						resultJson = new JSONObject();
//					}
					this.closedPosBill(tenancyId, storeId, billNum, reportDateStr, shiftId, posNum, optNum, isPrint, isInvoice,saleMode, billJson, printJson, "0", currentTime);

                    if(Tools.hv(sbill_num)){ //并台账单零结账
                        this.combineTableZeroPayment(tenancyId, storeId, sbill_num, reportDate, shiftId, posNum, optNum,currentTime);
                    }
				}

				if (null == printJson)
				{
					printJson = new JSONObject();
					printJson.put("isprint", "N");
				}
			}
			catch (Exception e)
			{
				logger.error("微生活消费提交报错", e);
				throw e;
			}
			
			String operator=null;
			try
			{
				operator = paymentDao.getEmpNameById(optNum, tenancyId, storeId);
			}
			catch (Exception e)
			{
				logger.error("微生活消费提交报错", e);
			}
			printJson.put("pos_num", posNum);
			printJson.put("print_time", DateUtil.format(currentTime));
			printJson.put("bill_code", billCode);
			printJson.put("card_code", cardCode);
			printJson.put("level_name", levelName);
			printJson.put("card_class_name", "");
			printJson.put("name", customerName);
			printJson.put("mobil", mobil);
			printJson.put("consume_cardmoney", consumeAmount);
			printJson.put("main_balance", consumeAfterBalance);
			printJson.put("reward_balance", 0);
			printJson.put("credit", receiveCredit); //本次积分
			printJson.put("useful_credit", receiveAfterCredit);
			printJson.put("operator", operator);
			printJson.put("updatetime", DateUtil.format(currentTime));
		}
		else if ((Constant.CODE_CONN_EXCEPTION != responseData.getCode())
				&& (AcewillRequestUtil.SMS_NULL_ERROR_CODE != responseData.getCode() && AcewillRequestUtil.SMS_WRONG_ERROR_CODE != responseData.getCode() && AcewillRequestUtil.SMS_OVERDUE_ERROR_CODE != responseData.getCode() && AcewillRequestUtil.PASSWORD_LENGTH_ERROR_CODE != responseData.getCode() && AcewillRequestUtil.PASSWORD_WRONG_ERROR_CODE != responseData.getCode()&& AcewillRequestUtil.PASSWORD_DEFAULT_ERROR_CODE != responseData.getCode()))
		{
			//支付失败，除密码错误错误提示外，取消付款
			this.deletePosBillPaymentForAcewillPay(tenancyId, storeId, paramJson);
		}

		paymentDao.savePosLog(tenancyId, storeId, posNum, optNum, optNum, shiftId, reportDate, Constant.TITLE, "微生活消费确认", "账单编号:"+billNum, responseData.getMsg());
		
		JSONObject resultJson = this.getPosBillPaymentByBillNum(tenancyId, storeId, billNum, tableCode, paymentAmount, discountAmount, difference, paymentState);
		List<JSONObject> resultList = new ArrayList<JSONObject>();
		resultList.add(resultJson);
		responseData.setData(resultList);
		return responseData;
	}
	
	@Override
	public void requestDealCommitSuccess(String tenancyId, int storeId, Date reportDate, String billNum, String outTradeNo, Double billPaymentAmount, JSONObject responseJson) throws Exception
	{
		Timestamp currentTime = DateUtil.currentTimestamp();

		String billCode = ParamUtil.getStringValueByObject(responseJson, "deal_id");
		String cardCode = ParamUtil.getStringValueByObject(responseJson, "card_code");
		String customerName = ParamUtil.getStringValueByObject(responseJson, "customer_name");
		String mobil = ParamUtil.getStringValueByObject(responseJson, "mobil");

		Double consumeAmount = ParamUtil.getDoubleValueByObject(responseJson, "sub_balance");
		Double consumeCredit = ParamUtil.getDoubleValueByObject(responseJson, "sub_credit");
		Double receiveCredit = ParamUtil.getDoubleValueByObject(responseJson, "receive_credit");

		Double consumeAfterBalance = ParamUtil.getDoubleValueByObject(responseJson, "balance");
		Double consumeBeforeBalance = DoubleHelper.add(consumeAfterBalance, consumeAmount, DEFAULT_SCALE);

		Double consumeAfterCredit = ParamUtil.getDoubleValueByObject(responseJson, "credit");
		Double consumeBeforeCredit = DoubleHelper.add(consumeAfterCredit, consumeCredit, DEFAULT_SCALE);

		Double receiveBeforeCredit = ParamUtil.getDoubleValueByObject(responseJson, "credit");
		Double receiveAfterCredit = DoubleHelper.add(receiveBeforeCredit, receiveCredit, DEFAULT_SCALE);

		List<JSONObject> billPaymentList = paymentDao.getPosBillPaymentByBillnum(tenancyId, storeId, billNum);
		PosBillMember billMember = null;
		List<PosBillPayment> updateBillPaymentList = new ArrayList<PosBillPayment>();
		for (JSONObject paymentJson : billPaymentList)
		{
			if (outTradeNo.equals(paymentJson.optString("out_trade_no")))
			{
				if (SysDictionary.PAYMENT_CLASS_WLIFE_BALANCE.equals(paymentJson.optString("payment_class")))
				{
					billMember = new PosBillMember();
					billMember.setBill_num(billNum);
					billMember.setReport_date(reportDate);
					billMember.setType(SysDictionary.BILL_MEMBERCARD_CZXF03);
					billMember.setCard_code(cardCode);
					billMember.setMobil(mobil);
					billMember.setCustomer_code(cardCode);
					billMember.setCustomer_name(customerName);
					billMember.setAmount(consumeAmount);
					billMember.setCredit(0d);
					billMember.setConsume_before_credit(consumeBeforeCredit);
					billMember.setConsume_before_main_balance(consumeBeforeBalance);
					billMember.setConsume_before_reward_balance(0d);
					billMember.setConsume_after_credit(consumeAfterCredit);
					billMember.setConsume_after_main_balance(consumeAfterBalance);
					billMember.setConsume_after_reward_balance(0d);
					billMember.setLast_updatetime(currentTime);
					billMember.setBill_code(billCode);
					billMember.setRequest_state(SysDictionary.REQUEST_STATUS_COMPLETE);
					memberDao.insertPosBillMember(tenancyId, storeId, billMember);

					Double storedSalePay = ParamUtil.getDoubleValueByObject(responseJson, "stored_sale_pay");
					Double storedPay = ParamUtil.getDoubleValueByObject(responseJson, "stored_pay");

					PosBillPayment billPayment = new PosBillPayment();
					billPayment.setId(paymentJson.optInt("id"));
					billPayment.setBill_code(billCode);
					billPayment.setPayment_state(SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
					billPayment.setCoupon_buy_price(storedSalePay);
					billPayment.setDue(storedSalePay);
					billPayment.setTenancy_assume(DoubleHelper.sub(storedPay, storedSalePay, 4));
					billPayment.setThird_assume(0d);
					billPayment.setThird_fee(0d);
					updateBillPaymentList.add(billPayment);
				}
				else if (SysDictionary.PAYMENT_CLASS_WLIFE_CREDIT.equals(paymentJson.optString("payment_class")))
				{
					billMember = new PosBillMember();
					billMember.setBill_num(billNum);
					billMember.setReport_date(reportDate);
					billMember.setType(SysDictionary.BILL_MEMBERCARD_JFDX05);
					billMember.setCard_code(cardCode);
					billMember.setMobil(mobil);
					billMember.setCustomer_code(cardCode);
					billMember.setCustomer_name(customerName);
					billMember.setAmount(consumeCredit);
					billMember.setCredit(consumeCredit);
					billMember.setConsume_before_credit(consumeBeforeCredit);
					billMember.setConsume_before_main_balance(consumeBeforeBalance);
					billMember.setConsume_before_reward_balance(0d);
					billMember.setConsume_after_credit(consumeAfterCredit);
					billMember.setConsume_after_main_balance(consumeAfterBalance);
					billMember.setConsume_after_reward_balance(0d);
					billMember.setLast_updatetime(currentTime);
					billMember.setBill_code(billCode);
					billMember.setRequest_state(SysDictionary.REQUEST_STATUS_COMPLETE);
					memberDao.insertPosBillMember(tenancyId, storeId, billMember);

					PosBillPayment billPayment = new PosBillPayment();
					billPayment.setId(paymentJson.optInt("id"));
					billPayment.setBill_code(billCode);
					billPayment.setPayment_state(SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
					billPayment.setCoupon_buy_price(0d);
					billPayment.setDue(0d);
					billPayment.setTenancy_assume(consumeCredit);
					billPayment.setThird_assume(0d);
					billPayment.setThird_fee(0d);
					updateBillPaymentList.add(billPayment);
				}
				else if (SysDictionary.PAYMENT_CLASS_WLIFE_COUPON.equals(paymentJson.optString("payment_class")))
				{
					billMember = new PosBillMember();
					billMember.setBill_num(billNum);
					billMember.setReport_date(reportDate);
					billMember.setType(SysDictionary.BILL_MEMBERCARD_YHJ04);
					billMember.setCard_code(cardCode);
					billMember.setMobil(mobil);
					billMember.setCustomer_code(cardCode);
					billMember.setCustomer_name(customerName);
					billMember.setAmount(ParamUtil.getDoubleValueByObject(paymentJson, "currency_amount"));
					billMember.setCredit(0d);
					billMember.setBill_code(paymentJson.optString("number"));
					billMember.setConsume_before_credit(consumeBeforeCredit);
					billMember.setConsume_before_main_balance(consumeBeforeBalance);
					billMember.setConsume_before_reward_balance(0d);
					billMember.setConsume_after_credit(consumeAfterCredit);
					billMember.setConsume_after_main_balance(consumeAfterBalance);
					billMember.setConsume_after_reward_balance(0d);
					billMember.setLast_updatetime(currentTime);
					billMember.setBill_code(billCode);
					billMember.setRequest_state(SysDictionary.REQUEST_STATUS_COMPLETE);
					billMember.setRemark(paymentJson.optString("number"));
					memberDao.insertPosBillMember(tenancyId, storeId, billMember);

					PosBillPayment billPayment = new PosBillPayment();
					billPayment.setId(paymentJson.optInt("id"));
					billPayment.setBill_code(billCode);
					billPayment.setPayment_state(SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
					billPayment.setCoupon_buy_price(0d);
					billPayment.setDue(0d);
					billPayment.setTenancy_assume(ParamUtil.getDoubleValueByObject(paymentJson, "currency_amount"));
					billPayment.setThird_assume(0d);
					billPayment.setThird_fee(0d);
					updateBillPaymentList.add(billPayment);
				}
			}
		}

		if (updateBillPaymentList.size() > 0)
		{
			paymentDao.updatePosBillPaymentForPaymentState(tenancyId, storeId, billNum, updateBillPaymentList);
		}

		if (0 < receiveCredit)
		{
			List<JSONObject> memberList = memberDao.queryPosBillMember(tenancyId, storeId, billNum, cardCode, SysDictionary.BILL_MEMBERCARD_JFZS06);

			if (null != memberList && memberList.size() > 0)
			{
				memberDao.updatePosBillMember(tenancyId, storeId, billNum, SysDictionary.BILL_MEMBERCARD_JFZS06, billPaymentAmount, receiveCredit, billCode, SysDictionary.REQUEST_STATUS_COMPLETE, customerName, receiveBeforeCredit, receiveAfterCredit);
			}
			else
			{
				billMember = new PosBillMember();
				billMember.setBill_num(billNum);
				billMember.setReport_date(reportDate);
				billMember.setType(SysDictionary.BILL_MEMBERCARD_JFZS06);
				billMember.setCard_code(cardCode);
				billMember.setMobil(mobil);
				billMember.setCustomer_code(cardCode);
				billMember.setCustomer_name(customerName);
				billMember.setAmount(billPaymentAmount);
				billMember.setCredit(receiveCredit);
				billMember.setConsume_before_credit(receiveBeforeCredit);
				billMember.setConsume_before_main_balance(consumeAfterBalance);
				billMember.setConsume_before_reward_balance(0d);
				billMember.setConsume_after_credit(receiveAfterCredit);
				billMember.setConsume_after_main_balance(consumeAfterBalance);
				billMember.setConsume_after_reward_balance(0d);
				billMember.setLast_updatetime(currentTime);
				billMember.setBill_code(billCode);
				billMember.setRequest_state(SysDictionary.REQUEST_STATUS_COMPLETE);
				memberDao.insertPosBillMember(tenancyId, storeId, billMember);
			}
		}
		else 
		{
			List<JSONObject> memberList = memberDao.queryPosBillMember(tenancyId, storeId, billNum, SysDictionary.BILL_MEMBERCARD_JFZS06);
			if (null == memberList || 0 == memberList.size())
			{
				billMember = new PosBillMember();
				billMember.setBill_num(billNum);
				billMember.setReport_date(reportDate);
				billMember.setType(SysDictionary.BILL_MEMBERCARD_JFZS06);
				billMember.setCard_code(cardCode);
				billMember.setMobil(mobil);
				billMember.setCustomer_code(cardCode);
				billMember.setCustomer_name(customerName);
				billMember.setAmount(billPaymentAmount);
				billMember.setCredit(0d);
				billMember.setConsume_before_credit(receiveBeforeCredit);
				billMember.setConsume_before_main_balance(consumeAfterBalance);
				billMember.setConsume_before_reward_balance(0d);
				billMember.setConsume_after_credit(receiveAfterCredit);
				billMember.setConsume_after_main_balance(consumeAfterBalance);
				billMember.setConsume_after_reward_balance(0d);
				billMember.setLast_updatetime(currentTime);
				billMember.setBill_code(billCode);
				memberDao.insertPosBillMember(tenancyId, storeId, billMember);
			}
		}
	}
	
	public void printPosBillForPayment (String tenantId, Integer storeId, String source,JSONObject printJson) throws Exception
	{
		try
		{
			if (SysDictionary.SOURCE_MOVE.contains(source) || SysDictionary.SOURCE_ORDER_DEVICE.equals(source))
			{
				posPrintService.getBindOptNum(printJson.optString("tenancy_id"), printJson.optInt("store_id"), printJson);
			}
			printJson.put("source", source);

			// 打印会员小票
			acewillCustomerService.printForCustomer(tenantId, storeId, SysDictionary.PRINT_CODE_1010, printJson);

			posPrintService.printPosBillForPayment(printJson, posPrintNewService);
		}
		catch (Exception e)
		{
			logger.info("打印小票失败:",e);
		}
	}

	@Override
	public List<JSONObject> checkAcewillCoupons(String tenancyId, int storeId, JSONObject paramJson) throws Exception
    {
        String billNum = ParamUtil.getStringValueByObject(paramJson, "bill_num", true, PosErrorCode.NOT_NULL_BILL_NUM);
        JSONArray coupons = null;
        if (paramJson.containsKey("coupons"))
        {
            coupons = paramJson.optJSONArray("coupons");
        }

        if (null == coupons || coupons.size() <= 0)
        {
            throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
        }

        Timestamp currentTime = com.tzx.framework.common.util.DateUtil.currentTimestamp();

        //计算菜品
        List<JSONObject> itemList = this.getCouponDeductibleBillItem(tenancyId, storeId, billNum);

        Map<String, JSONObject> rwidItemJsonMap = new HashMap<String, JSONObject>();//<RWID,菜品信息>
        Map<String, List<String>> itemidRwidMap = new HashMap<String, List<String>>();//<菜品ID,RWID列表>
        for (JSONObject itemJson : itemList)
        {
            String rwid = ParamUtil.getStringValueByObject(itemJson, "rwid");
            String itemNum = ParamUtil.getStringValueByObject(itemJson, "item_num");
            Double itemCount = ParamUtil.getDoubleValueByObject(itemJson, "item_count");
            Double realAmount = ParamUtil.getDoubleValueByObject(itemJson, "real_amount");
            Double itemPrice = ParamUtil.getDoubleValueByObject(itemJson, "item_price");
            Double itemAmount =  ParamUtil.getDoubleValueByObject(itemJson, "item_amount");
            if(realAmount > itemAmount)
            {
                realAmount = itemAmount;
            }
            Double realPrice = DoubleHelper.div(realAmount, itemCount, AMOUNT_SCALE);
            if (realPrice.isNaN())
            {
                realPrice = 0d;
            }
            //做法加价,或者套餐可选项目加价,不做优惠券抵扣
            if (realPrice > itemPrice)
            {
                realPrice = itemPrice;
            }
            itemJson.put("real_price", realPrice);

            //RWID对应菜品信息
            rwidItemJsonMap.put(rwid, itemJson);

            //item_id对应的RWID
            List<String> rwids = itemidRwidMap.get(itemNum);
            if (null == rwids)
            {
                rwids = new ArrayList<String>();
            }
            rwids.add(rwid);
            itemidRwidMap.put(itemNum, rwids);
        }

        //根据已抵扣菜品的优惠劵,计算可抵扣菜品劵的菜品数量
        for (Object obj : coupons)
        {
            JSONObject couponJson = JSONObject.fromObject(obj);
            String rwid = ParamUtil.getStringValueByObject(couponJson, "rwid");
            if (CommonUtil.hv(rwid) && rwidItemJsonMap.containsKey(rwid))
            {
                JSONObject itemJson = rwidItemJsonMap.get(rwid);
                Double itemCount = ParamUtil.getDoubleValueByObject(itemJson, "item_count");
                //修改菜品剩余可抵扣数量
                itemJson.put("item_count", DoubleHelper.sub(itemCount, 1d, DEFAULT_SCALE));
                rwidItemJsonMap.put(rwid, itemJson);
            }
        }

        JSONObject billJson = paymentDao.getPosBillByBillnum(tenancyId, storeId, billNum);

        //查询账单同一模板已使用的优惠券数量
        List<JSONObject> temlateUsed=paymentDao.query4Json(tenancyId,"SELECT\n" +
                "	pbpc.class_id,\n" +
                "	SUM (pbpc.discount_num) num\n" +
                "FROM\n" +
                "	pos_bill_payment pbp\n" +
                "LEFT JOIN pos_bill_payment_coupons pbpc ON pbp.payment_uid = pbpc.payment_id\n" +
                "WHERE\n" +
                "	pbp. TYPE = 'wlife_coupon'\n" +
                "AND pbp.bill_num = ?\n" +
                "GROUP BY\n" +
                "	pbpc.class_id",new Object[]{billNum});
        Map<String,Integer> temlateUsedNumMap=null;
        if(CollectionUtils.isNotEmpty(temlateUsed)){
            temlateUsedNumMap=new HashMap<>();
            for(JSONObject json:temlateUsed){
                String temlateId=json.optString("class_id");
                temlateUsedNumMap.put(temlateId,json.optInt("num"));
            }
            logger.info(billNum+"====模板已使用的优惠券数量:"+temlateUsedNumMap);
        }

        //查询账单

        Double difference = ParamUtil.getDoubleValueByObject(billJson, "difference");
        Double paymentAmount = ParamUtil.getDoubleValueByObject(billJson, "payment_amount");

        Map<String, Double> templateCountMap = new HashMap<String, Double>();
        List<JSONObject> resultList = new ArrayList<JSONObject>();
        Double couponsAmount = 0d;
        for (Object obj : coupons)
        {
            JSONObject couponJson = JSONObject.fromObject(obj);
            String couponCode = ParamUtil.getStringValueByObject(couponJson, "coupon_code");
            Timestamp effectiveTime = com.tzx.framework.common.util.DateUtil.formatTimestamp(couponJson.optString("effective_time"));// 生效时间
            Timestamp failureTime = com.tzx.framework.common.util.DateUtil.formatTimestamp(couponJson.optString("failure_time"));// 失效时间
            String itemNum = ParamUtil.getStringValueByObject(couponJson, "item_num");
            String rwid = ParamUtil.getStringValueByObject(couponJson, "rwid");
            Double couponDeno = ParamUtil.getDoubleValueByObject(couponJson, "coupon_deno");
            String limitType = ParamUtil.getStringValueByObject(couponJson, "limit_type");
            Double enableAmount = ParamUtil.getDoubleValueByObject(couponJson, "enable_amount");
            String templateId = ParamUtil.getStringValueByObject(couponJson, "template_id");
            Integer maxUse = ParamUtil.getIntegerValueByObject(couponJson,"max_use"); //同一种券一次最多可以使用几张
            Integer mixUseValue = ParamUtil.getIntegerValueByObject(couponJson,"mix_use_value"); //混用类型
            JSONArray  limitMixCoupon = ParamUtil.getJSONArray(couponJson,"limit_mix_coupon"); //可以混用的券
            JSONArray products = null;
            if(couponJson.containsKey("products"))
            {
                products = couponJson.optJSONArray("products");
            }
            String couponType = ParamUtil.getStringValueByObject(couponJson, "coupon_type");
            String isDiyDeno = "1";
            if (couponJson.containsKey("is_diy_deno"))
            {
                isDiyDeno = ParamUtil.getStringValueByObject(couponJson, "is_diy_deno");
            }
            String title = ParamUtil.getStringValueByObject(couponJson, "title");
            if(CommonUtil.isNullOrEmpty(title))
            {
                title = String.valueOf(couponCode);
            }


            Double couponCountTotal = 1d;
            if (templateCountMap.containsKey(templateId))
            {
                couponCountTotal = DoubleHelper.padd(templateCountMap.get(templateId), 1d);
            }
            templateCountMap.put(templateId, couponCountTotal);

            //同一模板优惠券使用数量合计
            logger.info("优惠券"+couponCode+"限制使用张数："+maxUse);
            if(null!=maxUse&&maxUse>0){
                Integer temlateUsedNum=0;
                if(MapUtils.isNotEmpty(temlateUsedNumMap)&&temlateUsedNumMap.containsKey(templateId)){
                    temlateUsedNum=temlateUsedNumMap.get(templateId);
                }
                if((couponCountTotal+temlateUsedNum)>maxUse){
                    throw SystemException.getInstance(PosErrorCode.COUPONS_BILL_LIMIT_OVERLOAD_ERROR).set("{0}", templateId).set("{1}", maxUse);
                }
            }

            //验证混用
            if(MapUtils.isNotEmpty(temlateUsedNumMap)) {
                List usedTemlates = new ArrayList(temlateUsedNumMap.keySet());

                //不可以
                if(1==mixUseValue){
                    throw SystemException.getInstance(PosErrorCode.COUPON_MIXED_USE_ERROR).set("{0}", title).set("{1}", usedTemlates.get(0));
                }

                //部分可以
                if (2 == mixUseValue) {
                    if (CollectionUtils.isNotEmpty(limitMixCoupon)) {
                        usedTemlates.removeAll(limitMixCoupon);
                    }
                    if (CollectionUtils.isNotEmpty(usedTemlates)) {
                        throw SystemException.getInstance(PosErrorCode.COUPON_MIXED_USE_ERROR).set("{0}", title).set("{1}", usedTemlates.get(0));
                    }
                }
            }

            // 校验劵生效时间
            if (currentTime.before(effectiveTime))
            {
                throw SystemException.getInstance(PosErrorCode.COUPON_NOT_EFFECTIVE_ERROR).set("{0}", couponCode);
            }

            //不可用菜品或菜品类别编码
            List<String> extList=null;
            String productsExt=ParamUtil.getStringValueByObject(couponJson, "products_ext");
            if(WLifeCrmConstant.CUSTOMER_COUPON_TYPE_CASH.equals(couponType)&&StringUtils.isNotBlank(productsExt)){
                String[] extIds=productsExt.split(",");
                extList= Arrays.asList(extIds);
            }

            //查询可以用劵的菜品总金额
            Double unAvailableAmount=0d;
            if(null!=extList&&!extList.isEmpty()){

                String extIds="";
                for(String id:extList){
                    extIds+=",'"+id+"'";
                }

                extIds=extIds.replaceFirst(",","");

                String queryAvail="SELECT\n" +
                        "	COALESCE (SUM(real_amount), 0)\n" +
                        "FROM\n" +
                        "	pos_bill_item pbi\n" +
                        "LEFT JOIN hq_item_class hic ON pbi.item_class_id = hic. ID\n" +
                        "WHERE\n" +
                        "	bill_num = '"+billNum+"'\n" +
                        "AND item_property IN ('SETMEAL', 'SINGLE')\n" +
                        "AND (pbi.item_num IN ("+extIds+")\n" +
                        "OR hic.itemclass_code IN ("+extIds+"))";
                unAvailableAmount = paymentDao.getJdbcTemplate(tenancyId).queryForObject(queryAvail,Double.class);
            }

            logger.info(couponCode+"不可用的菜品或类别编码为："+productsExt+",不可用券的菜品金额为:"+unAvailableAmount);

            Double availableAmount=DoubleHelper.psub(DoubleHelper.psub(paymentAmount,unAvailableAmount),couponCountTotal>1?0:couponsAmount); //(208-0)-50=158
            //启用金额限制
            if (0d < enableAmount)
            {

                if ("1".equals(limitType))
                {
                    //1:每满多少金额可用
                    Double enableAmountTotal = DoubleHelper.pmui(enableAmount, couponCountTotal);//100*2 =200
                    if (availableAmount < enableAmountTotal)
                    {
                        throw SystemException.getInstance(PosErrorCode.COUPONS_NOT_EVERY_FULL_BILL_LIMIT_MONEY_ERROR).set("{0}", enableAmount).set("{1}", title).set("{2}",availableAmount);
                    }
                }
                else if (availableAmount < enableAmount)
                {
                    throw SystemException.getInstance(PosErrorCode.COUPONS_NOT_FULL_BILL_LIMIT_MONEY_ERROR).set("{0}", enableAmount).set("{1}", title).set("{2}",availableAmount);
                }
            }

            //未启用金额限制但是账单菜品都是不可用券菜品
            if(0==enableAmount&&availableAmount<=0){
                throw SystemException.getInstance(PosErrorCode.COUPONS_NOT_FULL_BILL_LIMIT_MONEY_ERROR).set("{0}", enableAmount).set("{1}", title).set("{2}",availableAmount);
            }


            // 校验失效时间
            if (currentTime.after(failureTime))
            {
                throw SystemException.getInstance(PosErrorCode.COUPON_ALREADY_INVALID_ERROR).set("{0}", couponCode);
            }

            if (WLifeCrmConstant.CUSTOMER_COUPON_TYPE_DISH.equals(couponType)&&null != products && products.size() > 0)
            {
                // 计算礼品劵抵扣金额
                if (CommonUtil.hv(rwid) && rwidItemJsonMap.containsKey(rwid))
                {
                    // 已经抵扣菜品的优惠劵,抵扣金额为菜品单价
                    JSONObject itemJson = rwidItemJsonMap.get(rwid);
                    Double couponAmount = this.getDishCouponAmount(itemJson, couponDeno, isDiyDeno);

                    //组织返回数据
                    couponJson.put("coupon_amount", couponAmount);

                    //优惠券抵扣金额合计
                    couponsAmount = DoubleHelper.add(couponsAmount, couponAmount, DEFAULT_SCALE);
                }
                else
                {
                    // 根据可抵扣菜品列表,获取账明细的RWID列表
                    List<String> rwids = new ArrayList<String>();
                    for (Object productObj : products)
                    {
                        String product = String.valueOf(productObj);
                        if (CommonUtil.hv(product) && itemidRwidMap.containsKey(product))
                        {
                            rwids.addAll(itemidRwidMap.get(product));
                        }
                    }

                    // 查找抵扣金额最大的可抵扣菜品
                    JSONObject itemJson = null;// 可抵扣菜品
                    for (String rwidKey : rwids)
                    {
                        JSONObject productJson = rwidItemJsonMap.get(rwidKey);
                        Double itemCount = ParamUtil.getDoubleValueByObject(productJson, "item_count");
                        Double realPrice = ParamUtil.getDoubleValueByObject(productJson, "real_price");

                        if (1d <= itemCount)
                        {
                            if (CommonUtil.isNullOrEmpty(itemJson) || (realPrice > ParamUtil.getDoubleValueByObject(itemJson, "real_price")))
                            {
                                itemJson = productJson;
                            }
                        }
                    }

                    // 获取抵扣金额
                    if (CommonUtil.hv(itemJson) && itemJson.isEmpty() == false)
                    {
                        Double itemCount = ParamUtil.getDoubleValueByObject(itemJson, "item_count");
                        itemNum = ParamUtil.getStringValueByObject(itemJson, "item_num");
                        rwid = ParamUtil.getStringValueByObject(itemJson, "rwid");
                        Double couponAmount = this.getDishCouponAmount(itemJson, couponDeno, isDiyDeno);

                        //组织返回数据
                        couponJson.put("item_num", itemNum);
                        couponJson.put("rwid", rwid);
                        couponJson.put("coupon_amount", couponAmount);
                        couponJson.put("coupon_deno", couponAmount);

                        //优惠券抵扣金额合计
                        couponsAmount = DoubleHelper.add(couponsAmount, couponAmount, DEFAULT_SCALE);

                        //修改菜品剩余可抵扣数量
                        itemJson.put("item_count", DoubleHelper.sub(itemCount, 1d, DEFAULT_SCALE));
                        rwidItemJsonMap.put(rwid, itemJson);
                    }
                    else
                    {
                        // 礼品劵没有可抵扣的菜品
                        throw SystemException.getInstance(PosErrorCode.COUPON_NOT_OFFSET_DISH_ERROR).set("{0}", couponCode);
                    }
                }
            }
            else if (null != couponDeno && 0d < couponDeno.doubleValue())
            {
                // 代金券抵金额
                couponJson.put("coupon_amount", couponDeno);
                couponsAmount = DoubleHelper.add(couponsAmount, couponDeno, DEFAULT_SCALE);
            }
            else
            {
                // 无优惠劵面值,优惠劵无效
                throw SystemException.getInstance(PosErrorCode.COUPON_DISABLED_ERROR).set("{0}", couponCode);
            }

            resultList.add(couponJson);
        }

        Double couponDifference = DoubleHelper.sub(difference, couponsAmount, AMOUNT_SCALE);
        if (couponDifference < 0)
        {
            for (JSONObject couponJson : resultList)
            {
                if (Math.abs(couponDifference) >= couponJson.optDouble("coupon_amount"))
                {
                    throw SystemException.getInstance(PosErrorCode.COUPON_AMOUNT_MORE_DIFFERENCE_ERROR);
                }
            }
        }
        return resultList;
    }

    /**获取优惠券可抵扣菜品
     * @param tenancyId
     * @param storeId
     * @param billNum
     * @return
     * @throws Exception
     */
    private List<JSONObject> getCouponDeductibleBillItem(String tenancyId, Integer storeId, String billNum) throws Exception
    {
        List<JSONObject> itemList = paymentDao.getPosBillItemForSingleByBillNum(tenancyId, storeId, billNum);
        if (null != itemList)
        {
            // 查询付款优惠明细
            List<JSONObject> paymentCouponList = paymentDao.getPaymentCouponsByBillNum(tenancyId, storeId, billNum);
            if (null != paymentCouponList && 0 < paymentCouponList.size())
            {
                Map<String, JSONObject> rwidItemJsonMap = new HashMap<String, JSONObject>();// <RWID,菜品信息>
                for (JSONObject itemJson : itemList)
                {
                    String rwid = ParamUtil.getStringValueByObject(itemJson, "rwid");
                    rwidItemJsonMap.put(rwid, itemJson);
                }

                for (JSONObject couponJson : paymentCouponList)
                {
                    String rwid = ParamUtil.getStringValueByObject(couponJson, "rwid");
                    Double count = ParamUtil.getDoubleValueByObject(couponJson, "discount_num");
                    if (CommonUtil.hv(rwid) && rwidItemJsonMap.containsKey(rwid))
                    {
                        JSONObject itemJson = rwidItemJsonMap.get(rwid);
                        Double itemCount = DoubleHelper.sub(ParamUtil.getDoubleValueByObject(itemJson, "item_count"), count, DEFAULT_SCALE);
                        if (0d < itemCount.doubleValue())
                        {
                            // 修改菜品剩余可抵扣数量
                            itemJson.put("item_count", itemCount);
                            rwidItemJsonMap.put(rwid, itemJson);
                        }
                        else
                        {
                            //数量为0,移除菜品
                            rwidItemJsonMap.remove(rwid);
                        }

                    }
                }
                //
                itemList = new ArrayList<JSONObject>(rwidItemJsonMap.values());
            }
        }
        return itemList;
    }


    /** 计算菜品券抵扣金额
     * @param itemJson
     * @param couponDeno
     * @param isDiyDeno
     * @return
     * @throws Exception
     */
    private Double getDishCouponAmount(JSONObject itemJson, Double couponDeno, String isDiyDeno) throws Exception
    {
        Double itemPrice = ParamUtil.getDoubleValueByObject(itemJson, "real_price");
        Double couponAmount = itemPrice;
        if ("0".equals(isDiyDeno) && 0d < couponDeno.doubleValue() && itemPrice.doubleValue() > couponDeno.doubleValue())
        {
            // 优惠券为固定价值,且优惠券面值大于0,菜品金额大于优惠券面值,抵扣金额为优惠券面值;
            couponAmount = couponDeno;
        }
        return couponAmount;
    }
	
//	/** 获取账单支付结果
//	 * @param tenancyId
//	 * @param storeId
//	 * @param billNum
//	 * @param paymentAmount
//	 * @param tableCode
//	 * @param discountAmount
//	 * @param resultJson
//	 * @throws Exception
//	 */
//	private void getBillPaymentResult(String tenancyId, int storeId, String billNum, Double paymentAmount, String tableCode, Double discountAmount, JSONObject resultJson) throws Exception
//	{
//		List<JSONObject> paymentList = paymentDao.getBillPaymentByBillnum(tenancyId, storeId, billNum);
//		double amount = 0d;
//		String paymentState = SysDictionary.PAYMENT_STATE_NOTPAY;
//		for (JSONObject pay : paymentList)
//		{
//			amount = DoubleHelper.add(amount, pay.optDouble("currency_amount"), DEFAULT_SCALE);
//			if (SysDictionary.PAYMENT_STATE_PAY.equals(pay.optString("payment_state")))
//			{
//				paymentState = SysDictionary.PAYMENT_STATE_PAY;
//			}
//			else if (SysDictionary.PAYMENT_STATE_PAY_COMPLETE.equals(pay.optString("payment_state")) && !SysDictionary.PAYMENT_STATE_PAY.equals(paymentState))
//			{
//				paymentState = SysDictionary.PAYMENT_STATE_PAY_COMPLETE;
//			}
//		}
//
//		Double difference = DoubleHelper.sub(paymentAmount, amount, DEFAULT_SCALE);
//
//		resultJson.put("bill_num", billNum);
//		resultJson.put("table_code", tableCode);
//		resultJson.put("discount_amount", discountAmount);
//		resultJson.put("payment_amount", paymentAmount);
//		resultJson.put("payment_state", paymentState);
//		resultJson.put("payment_state_text", SysDictionary.PAYMENT_STATE_TEXT_MAP.get(paymentState));
//		resultJson.put("difference", difference > 0 ? difference : 0d);
//		resultJson.put("change", difference < 0 ? difference : 0d);
//		resultJson.put("paymentlist", paymentList);
//	}
}
