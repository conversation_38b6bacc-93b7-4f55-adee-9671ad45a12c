package com.tzx.pos.service.notify;

import com.tzx.framework.common.constant.Type;

import java.util.Observable;

/**
 * <AUTHOR> on 2018-06-30
 */
public class BohStateObservable extends Observable {

    private BohStateObservable() {
    }

    private static BohStateObservable instance;

    public static void doNotify(Type p) {

        try {
            synchronized (BohStateObservable.class) {
                if (null == instance) {

                    instance = new BohStateObservable();

                    //TODO 如果需要支持多终端推送，此处需要动态添加
                    instance.addObserver(new PosNotifyOberver());
                    instance.addObserver(new SystemParameterUpdateNotifyOberver());

                }
            }

            instance.setChanged();
            instance.notifyObservers(p);

        } catch (Exception e) {
        }
    }

}