package com.tzx.clientorder.wlifeprogram.common.entity;

import java.util.List;

import com.tzx.clientorder.common.entity.AcewillPayInfo;
import com.tzx.clientorder.common.entity.AcewillPayWLife;

/**
 * Created by zds on 2018-07-06.
 */
public class MicroLifePayOrder {

    private String business_id;
    private String brand_id;
    private Integer shop_id;

    private String tableno;

    private AcewillPayWLife wlife;

    private List<AcewillPayInfo> pay_info;

    private MicroLifePayOrderData data;

//    private  MicroLifeInvoice invoice;

    private List<WlifePaymentCoupons> deno_gift_couponMoney;

    public String getTableno() {
        return tableno;
    }

    public void setTableno(String tableno) {
        this.tableno = tableno;
    }

    public AcewillPayWLife getWlife() {
        return wlife;
    }

    public void setWlife(AcewillPayWLife wlife) {
        this.wlife = wlife;
    }

    public List<AcewillPayInfo> getPay_info() {
        return pay_info;
    }

    public void setPay_info(List<AcewillPayInfo> pay_info) {
        this.pay_info = pay_info;
    }

    public MicroLifePayOrderData getData() {
        return data;
    }

    public void setData(MicroLifePayOrderData data) {
        this.data = data;
    }

    public String getBusiness_id() {
        return business_id;
    }

    public void setBusiness_id(String business_id) {
        this.business_id = business_id;
    }

    public String getBrand_id() {
        return brand_id;
    }

    public void setBrand_id(String brand_id) {
        this.brand_id = brand_id;
    }

    public Integer getShop_id() {
        return shop_id;
    }

    public void setShop_id(Integer shop_id) {
        this.shop_id = shop_id;
    }

	public List<WlifePaymentCoupons> getDeno_gift_couponMoney()
	{
		return deno_gift_couponMoney;
	}

	public void setDeno_gift_couponMoney(List<WlifePaymentCoupons> deno_gift_couponMoney)
	{
		this.deno_gift_couponMoney = deno_gift_couponMoney;
	}
}
