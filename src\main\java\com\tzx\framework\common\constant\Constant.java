package com.tzx.framework.common.constant;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import javax.servlet.ServletContext;

public class Constant
{

	public static Map<String, String>	systemMap	= new HashMap<String, String>();

	public static Map<String, String> getSystemMap()
	{
		return systemMap;
	}

	public static void setSystemMap(Map<String, String> systemMap, ServletContext sc)
	{
//		Constant.systemMap = systemMap;
        Constant.systemMap.putAll(systemMap);
	}

	public static String		IDENTIFICATION				= "saasapp";
	public static String		TASKRESTURL					= "http://192.168.40.15/taskRest/post";
	public static String		PASS_WORD1_6				= "e10adc3949ba59abbe56e057f20f883e";
	
	public static String		CONTEXT_PATH				= "";

	public static Integer		DEFAULT_PRINT_TYPE			= 0;
	public static Boolean		DEFAULT_PRINT_TYPE_CHECK	= true;

	/**
	 * 初始化下发状态
	 */
	public static String		INIT_BASIC_DATA_STATE		= "0";

	/**
	 * 菜品图片下载目录
	 */
	public static final String	ITEM_PHOTO_PATH				= "itemImage";

	/**
	 * 副屏图片下载目录
	 */
	public static final String	VICE_SCREEN_PHOTO_PATH		= "viceScreenImage";
	
	public static final String	DOWNLOAD_APP_PATH			= "download/";

	public static final String	BOOL_TRUE					= "1";
	public static final String	BOOL_FASLE					= "0";

	public static final String	VALID_STATE_TRUE			= "1";
	public static final String	VALID_STATE_FALSE			= "0";

	public static final String TOKEN = "token";
	public static final String SPRING_SESSION_PARAM = "_s";

	
	public static final String MQ_TRANSFER_4UPLOAD = "MQUPLOAD";
	
	//系统参数，是否秒付上传数据
	public static final String IFUP_QUICK_PAY="ifup_quick_pay";
	
	//系统参数，是否开启秒付功能
	public static final String IS_START_CLOUDPOS_PAY = "is_start_cloudPos_pay";
	
//	//判断是否启用微生活点餐平台对接
//	public static final String IS_USER_WLIFE = "IS_USER_WLIFE";
	
	/**
	 * 短信平台 漫道
	 */
	public static final String	SMS_PLATFORM_MD = "MD";		
	/**
	 * 短信平台 移动时空
	 */
	public static final String	SMS_PLATFORM_YDSK = "YDSK";		
	/**
	 * 短信平台 云信留客
	 */
	public static final String	SMS_PLATFORM_YXLK = "YXLK";	
	
	// 保存推送消息的结果
	public static ConcurrentHashMap msgMap = new ConcurrentHashMap<String,String>();

}
