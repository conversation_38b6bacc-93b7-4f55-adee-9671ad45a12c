package com.tzx.pos.task;

import com.tzx.pos.po.springjdbc.dao.PosDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 历史数据定时清理任务
 *
 * <AUTHOR> on 2018/6/26
 */
@EnableScheduling
@Component
public class HistoryCleanTask implements IScheduleder {

    Logger logger = LoggerFactory.getLogger(HistoryCleanTask.class);

    @Resource(name = PosDao.NAME)
    private PosDao dao;

    @Value("${history.keep.days}")
    private String keepDays;

    @Value("${history.clean.tables}")
    private String[] cleanTables;

//    @Scheduled(cron = "0 0 15,20 * * ? ")
    @Scheduled(cron = "${history.clean.cron}")
    @Override
    public void scheduledExecute() {
        try {
            StringBuilder sql = new StringBuilder();
            for (String table : cleanTables) {
                try {
                    sql.setLength(0);
                    sql.append("delete from " + table + " where report_date < (select MAX(report_date)-" + keepDays + "  from " + table + ");");
                    //此处不采取批量提交，防止表名配置错误时导致所有表无法清理
                    dao.execute(null, sql.toString());
                    logger.info("定时清理任务执行成功:" + sql.toString());
                } catch (Exception e) {
                    logger.error("定时清理任务执行失败", e);
                }
            }
        } catch (Exception e) {
            logger.error("定时清理任务执行失败", e);
        }
    }

    @Scheduled(cron = "0 30 15 * * 1")
    public void scheduledvacuum() {
        try {
            dao.execute(null, "vacuum full;analyze;");
            logger.error("定时整理数据任务执行成功");
        } catch (Exception e) {
            logger.error("定时整理数据任务执行失败", e);
        }
    }
}
