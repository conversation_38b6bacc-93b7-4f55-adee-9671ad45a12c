package com.tzx.base.entity;

import java.sql.Timestamp;
import java.util.Date;

/**
 * 交款表射实体
 * 
 * <AUTHOR>
 *
 */
public class PosCashierReceiveLogEntity
{
	private String		tenancy_id;
	private Integer		store_id;
	private Integer		id;
	private Date		report_date;
	private Integer		shift_id;
	private String		oper_type;
	private Integer		waiter_id;
	private String		waiter_name;
	private String		device_num;
	private Double		amount;
	private Integer		cashier_id;
	private String		cashier_name;
	private String		pos_num;
	private Timestamp	last_updatetime;
	private String		upload_tag	= "0";
	private Integer		payment_id;
	private Integer		pay_shift_id;
	private String		service_type;

	public PosCashierReceiveLogEntity()
	{
		super();
	};

	public PosCashierReceiveLogEntity(String tenancy_id, Integer store_id, Date report_date, Integer shift_id, String oper_type, Integer waiter_id, String waiter_name, String device_num, Double amount, Integer cashier_id, String cashier_name, String pos_num, Timestamp last_updatetime,
			Integer payment_id, Integer pay_shift_id, String service_type)
	{
		super();
		this.tenancy_id = tenancy_id;
		this.store_id = store_id;
		this.report_date = report_date;
		this.shift_id = shift_id;
		this.oper_type = oper_type;
		this.waiter_id = waiter_id;
		this.waiter_name = waiter_name;
		this.device_num = device_num;
		this.amount = amount;
		this.cashier_id = cashier_id;
		this.cashier_name = cashier_name;
		this.pos_num = pos_num;
		this.last_updatetime = last_updatetime;
		this.payment_id = payment_id;
		this.pay_shift_id = pay_shift_id;
		this.service_type = service_type;
	}

	public String getTenancy_id()
	{
		return tenancy_id;
	}

	public void setTenancy_id(String tenancy_id)
	{
		this.tenancy_id = tenancy_id;
	}

	public Integer getStore_id()
	{
		return store_id;
	}

	public void setStore_id(Integer store_id)
	{
		this.store_id = store_id;
	}

	public Integer getId()
	{
		return id;
	}

	public void setId(Integer id)
	{
		this.id = id;
	}

	public Date getReport_date()
	{
		return report_date;
	}

	public void setReport_date(Date report_date)
	{
		this.report_date = report_date;
	}

	public Integer getShift_id()
	{
		return shift_id;
	}

	public void setShift_id(Integer shift_id)
	{
		this.shift_id = shift_id;
	}

	public String getOper_type()
	{
		return oper_type;
	}

	public void setOper_type(String oper_type)
	{
		this.oper_type = oper_type;
	}

	public Integer getWaiter_id()
	{
		return waiter_id;
	}

	public void setWaiter_id(Integer waiter_id)
	{
		this.waiter_id = waiter_id;
	}

	public String getWaiter_name()
	{
		return waiter_name;
	}

	public void setWaiter_name(String waiter_name)
	{
		this.waiter_name = waiter_name;
	}

	public String getDevice_num()
	{
		return device_num;
	}

	public void setDevice_num(String device_num)
	{
		this.device_num = device_num;
	}

	public Double getAmount()
	{
		return amount;
	}

	public void setAmount(Double amount)
	{
		this.amount = amount;
	}

	public Integer getCashier_id()
	{
		return cashier_id;
	}

	public void setCashier_id(Integer cashier_id)
	{
		this.cashier_id = cashier_id;
	}

	public String getCashier_name()
	{
		return cashier_name;
	}

	public void setCashier_name(String cashier_name)
	{
		this.cashier_name = cashier_name;
	}

	public String getPos_num()
	{
		return pos_num;
	}

	public void setPos_num(String pos_num)
	{
		this.pos_num = pos_num;
	}

	public Timestamp getLast_updatetime()
	{
		return last_updatetime;
	}

	public void setLast_updatetime(Timestamp last_updatetime)
	{
		this.last_updatetime = last_updatetime;
	}

	public String getUpload_tag()
	{
		return upload_tag;
	}

	public void setUpload_tag(String upload_tag)
	{
		this.upload_tag = upload_tag;
	}

	public Integer getPayment_id()
	{
		return payment_id;
	}

	public void setPayment_id(Integer payment_id)
	{
		this.payment_id = payment_id;
	}

	public Integer getPay_shift_id()
	{
		return pay_shift_id;
	}

	public void setPay_shift_id(Integer pay_shift_id)
	{
		this.pay_shift_id = pay_shift_id;
	}

	public String getService_type()
	{
		return service_type;
	}

	public void setService_type(String service_type)
	{
		this.service_type = service_type;
	}
}
