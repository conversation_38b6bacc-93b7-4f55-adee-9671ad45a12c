package com.tzx.pos.bo.imp;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import com.tzx.framework.common.exception.HttpRestException;
import net.sf.json.JSONObject;

import org.apache.log4j.Logger;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;

import com.alipay.api.internal.util.StringUtils;
import com.tzx.base.common.util.PosPropertyUtil;
import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.util.DoubleHelper;
import com.tzx.framework.common.util.HttpUtil;
import com.tzx.framework.common.util.JavaZipUtils;
import com.tzx.framework.common.util.MessageUtils;
import com.tzx.framework.common.util.Tools;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.base.util.JsonUtil;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.base.util.Progress;
import com.tzx.pos.base.util.ProgressMonitor;
import com.tzx.pos.base.util.ReqDataUtil;
import com.tzx.pos.bo.PosService;
import com.tzx.pos.bo.UploadDataService;
import com.tzx.pos.po.springjdbc.dao.UploadDataDao;

@Service(UploadDataService.NAME)
@EnableAsync
public class UploadDataServiceImp implements UploadDataService {
    private static Logger logger = Logger.getLogger(UploadDataService.class);

    private static final String UPLOAD_DATA_URL = "/rest/hq/post";

    @Resource(name = PosService.NAME)
    private PosService posService;

    @Resource(name = UploadDataDao.NAME)
    private UploadDataDao uploadDao;

    @SuppressWarnings("unchecked")
    @Override
    public void AutoUploadDataByMQ(String tenancyId, int storeId, String[] tableNames) throws Exception {
        // TODO Auto-generated method stub
        JSONObject dataJson = new JSONObject();

        String isUploadOpenBill = uploadDao.getSysParameter(tenancyId, storeId, "is_upload_open_bill");
        if (Tools.isNullOrEmpty(isUploadOpenBill)) {
            isUploadOpenBill = "0";
        }

        /* 查询门店数据 */
        boolean isSend = false;
        for (String tableName : tableNames) {
            List<JSONObject> jsonList = uploadDao.getPosAutoUploadDataItem(tenancyId, storeId, tableName, JSONObject.fromObject("{}"), isUploadOpenBill);
            if (null != jsonList && jsonList.size() > 0) {
                isSend = true;
                dataJson.put(tableName, jsonList);
            }
        }

        /* 发送到MQ */
        int rs = 0;
        if (isSend) {
            List<JSONObject> dataList = new ArrayList<JSONObject>();
            dataList.add(dataJson);

            Data data = Data.get();
            data.setType(Type.AUTOUPLOADDATA);
            data.setOper(Oper.upload);
            data.setTenancy_id(tenancyId);
            data.setStore_id(storeId);
            data.setData(dataList);

            MessageUtils mu = new MessageUtils();
            rs = mu.sendMessage(JSONObject.fromObject(data).toString());
            // rs = 1;
        }

        if (rs == 1) {
            StringBuffer cond = new StringBuffer();
            if (dataJson.containsKey("pos_bill")) {
                Iterator<JSONObject> jsonIt = dataJson.optJSONArray("pos_bill").iterator();
                while (jsonIt.hasNext()) {
                    JSONObject json = jsonIt.next();
                    if (SysDictionary.BILL_PROPERTY_OPEN.equalsIgnoreCase(json.optString("bill_property"))) {
                        cond.append("'").append(json.optString("bill_num")).append("',");
                    }
                }
                if (cond.length() > 0) {
                    cond.setLength(cond.length() - 1);
                    cond.insert(0, " and bill_num not in(").append(")");
                }
            }

            /* 发送成功修改上传状态 */
            Iterator<String> it = dataJson.keys();
            while (it.hasNext()) {
                String tableName = it.next();
                switch (tableName) {
                    case "pos_bill":
                    case "pos_bill_service":
                    case "pos_bill_member":
                    case "pos_bill_invoice":
                    case "pos_bill_waiter":
                    case "hq_bill_evaluate":
                    case "pos_bill_item":
                    case "pos_kvs_bill":
                    case "pos_kvs_bill_item":
                    case "pos_zfkw_item":
                    case "pos_returngive_item":
                    case "pos_bill_payment":
                    case "pos_bill_payment_coupons":
                    case "pos_item_discount_list":

                        uploadDao.updatePosItemUploadTag(tenancyId, storeId, tableName, dataJson.optJSONArray(tableName), cond.toString());
                        break;
                    default:
                        uploadDao.updatePosItemUploadTag(tenancyId, storeId, tableName, dataJson.optJSONArray(tableName), null);
                        break;
                }
            }
            uploadDao.updatePosUploadDate(tenancyId, storeId);
        }

    }

    @SuppressWarnings("unchecked")
    @Override
    public Data UploadDataByMQ(String tenancyId, int storeId, String[] tableNames, Date reportDate) throws Exception {
        Data reData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);

        JSONObject dataJson = new JSONObject();
        dataJson.put("report_date", DateUtil.format(reportDate));
        logger.info("上传数据：<上传报表日期:" + DateUtil.format(reportDate) + ">");

        JSONObject itemPara = JSONObject.fromObject("{}");
        itemPara.put("report_date", DateUtil.format(reportDate));
        boolean isSend = false;
        for (String tableName : tableNames) {
            List<JSONObject> jsonList = uploadDao.getPosUploadDataItem(tenancyId, storeId, tableName, itemPara);
            if (null != jsonList && jsonList.size() > 0) {
                isSend = true;
                dataJson.put(tableName, jsonList);
                logger.info("上传数据：<上传表名:" + tableName + ";上传数据" + jsonList.size() + "条>");
            }
        }

        List<JSONObject> dataList = new ArrayList<JSONObject>();
        dataList.add(dataJson);

        Data data = Data.get();
        data.setType(Type.DATATRANS);
        data.setOper(Oper.upload);
        data.setTenancy_id(tenancyId);
        data.setStore_id(Integer.valueOf(storeId));
        data.setData(dataList);

        /* 发送到MQ */
        if (isSend) {
            MessageUtils mu = new MessageUtils();
            int rs = mu.sendMessage(JSONObject.fromObject(data).toString());

            if (1 == rs) {
                /* 发送成功修改上传状态 */
                Iterator<String> it = dataJson.keys();
                while (it.hasNext()) {
                    String tableName = it.next();
                    if (!"report_date".equals(tableName)) {
                        uploadDao.updatePosItemUploadTag(tenancyId, storeId, tableName, dataJson.optJSONArray(tableName), null);
                    }
                }

                reData.setCode(Constant.CODE_SUCCESS);
                reData.setMsg(Constant.UPLOAD_DATA_SUCCESS);
            } else {
                reData.setCode(Constant.CODE_INNER_EXCEPTION);
                reData.setMsg(Constant.UPLOAD_DATA_FAILURE);
                reData.setSuccess(false);
            }
        } else {
            reData.setCode(Constant.CODE_UPLOAD_DATA_NULL);
            reData.setMsg(Constant.UPLOAD_DATA_NULL);
            reData.setSuccess(false);
        }

        return reData;
    }

    @Override
    public Data UploadDataByHttp(String tenancyId, int storeId, String[] tableNames, Date reportDate, String uploadMode) throws Exception {
        synchronized (UploadDataServiceImp.class) {
            logger.info("进入UploadDataByHttp方法");
            Long t = System.currentTimeMillis();
            Data reData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);

            //手动上传，通知总部并重置上传标志位
            if (MODE_UPLOAD_DATA.equals(uploadMode)) {

                Data d = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
                d.setType(Type.DATATRANS);
                d.setOper(Oper.data_reupload);
                JSONObject j = new JSONObject();
                j.put("report_date", DateUtil.formatDate(reportDate));
                j.put("time_stamp", System.currentTimeMillis());
                j.put("new_delete_table_name", "pos_opt_state");   // 多个表用逗号分隔
                d.setData(Arrays.asList(j));
                commonPost(d);

                logger.info("进入resetUploadTagByReportDate方法");
                uploadDao.resetUploadTagByReportDate(tenancyId, storeId, reportDate, new String[]{"pos_bill", "pos_bill2", "pos_bill_item", "pos_bill_item2", "pos_bill_payment", "pos_bill_payment2","pos_opt_state"});
            }
            logger.info("UploadDataByHttp方法：通知总部并重置上传标志位耗时：" + (System.currentTimeMillis() - t) + "毫秒");

            String isOpenBill = uploadDao.getSysParameter(tenancyId, storeId, "is_upload_open_bill");
            if (Tools.isNullOrEmpty(isOpenBill)) {
                isOpenBill = "0";
            }

            List<String> billTableNameList = new ArrayList<String>();
            List<String> orderTableNameList = new ArrayList<String>();
            List<String> otherTableNameList = new ArrayList<String>();
            for (String tableName : tableNames) {
                switch (tableName) {
                    case "pos_bill":
                    case "pos_bill_member":
                    case "pos_bill_service":
                    case "pos_bill_waiter":
                    case "pos_bill_item":
                    case "pos_zfkw_item":
                    case "pos_item_discount_list":
                    case "pos_bill_payment":
                    case "pos_bill_payment_coupons":
                    case "pos_bill2":
                    case "pos_bill_item2":
                    case "pos_bill_payment2":
                        billTableNameList.add(tableName);
                        break;
                    case "cc_order_list":
                    case "cc_order_item":
                    case "cc_order_item_retain":
                    case "cc_order_item_details":
                    case "cc_order_item_details_retain":
                    case "cc_order_repayment":
                        orderTableNameList.add(tableName);
                        break;

                    default:
                        otherTableNameList.add(tableName);
                        break;
                }
            }
            logger.info("UploadDataByHttp方法：上传数据前耗时：" + (System.currentTimeMillis() - t) + "毫秒");

            long t1 = System.currentTimeMillis();

            Data billResultData = this.UploadDataForBillByHttp(tenancyId, storeId, billTableNameList, reportDate, isOpenBill, uploadMode);
            long t2 = System.currentTimeMillis();
            logger.info("上传账单业务耗时 " + (t2 - t1) + " ms");

            Data otherResultData = this.UploadDataForOtherByHttp(tenancyId, storeId, otherTableNameList, reportDate, uploadMode);
            long t3 = System.currentTimeMillis();
            logger.info("上传其他表业务耗时 " + (t3 - t2) + " ms");

            Data orderResultData = this.UploadDataForOrder(tenancyId, storeId, orderTableNameList, reportDate, uploadMode);
            long t4 = System.currentTimeMillis();
            logger.info("上传外卖业务耗时 " + (t4 - t3) + " ms");

            logger.info("上传数据耗时:" + (t4 - t1) + " ms");

            if (Constant.CODE_UPLOAD_DATA_NULL == billResultData.getCode() && Constant.CODE_UPLOAD_DATA_NULL == otherResultData.getCode() && Constant.CODE_UPLOAD_DATA_NULL == orderResultData.getCode()) {
                reData.setCode(Constant.CODE_UPLOAD_DATA_NULL);
                reData.setMsg(Constant.UPLOAD_DATA_NULL);
                reData.setSuccess(false);
            } else if (Constant.CODE_CONN_EXCEPTION == billResultData.getCode() || Constant.CODE_CONN_EXCEPTION == otherResultData.getCode() || Constant.CODE_CONN_EXCEPTION == orderResultData.getCode()) {
                reData.setCode(Constant.CODE_CONN_EXCEPTION);
                reData.setMsg(Constant.UPLOAD_DATA_FAILURE);
                reData.setSuccess(false);
            } else {
                reData.setCode(Constant.CODE_SUCCESS);
                reData.setMsg(Constant.UPLOAD_DATA_SUCCESS);
                reData.setSuccess(true);
            }
            uploadDao.updatePosUploadDate(tenancyId, storeId);

            return reData;
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public Data UploadDataForBillByHttp(String tenancyId, int storeId, List<String> tableNames, Date reportDate, String isOpenBill, String uploadMode) throws Exception {
    	logger.info("进入UploadDataForBillByHttp方法。");
    	Data reData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
        // 获取未上传的账单数量
        Integer billCount = uploadDao.getPosBillDataForCountByReportDate(tenancyId, storeId, reportDate, isOpenBill);
        logger.info("执行getPosBillDataForCountByReportDate方法，获取账单数量："+billCount);
        if (0 == billCount) {
            reData.setCode(Constant.CODE_UPLOAD_DATA_NULL);
            reData.setMsg(Constant.UPLOAD_DATA_NULL);
            reData.setSuccess(false);
            return reData;

        }

        Integer pageNo = DoubleHelper.roundUp(billCount.doubleValue() / UPLOAD_DATA_SIZE, 0).intValue();
        List<JSONObject> tableList = null;
        List<JSONObject> billList = null;
        List<JSONObject> bill2List = null;
        List<JSONObject> billAllList = null;

        List<JSONObject> billItemList = null;
        List<JSONObject> billItem2List = null;
        List<JSONObject> billItemAllList = null;

        List<JSONObject> billPaymentList = null;
        List<JSONObject> billPayment2List = null;
        List<JSONObject> billPaymentAllList = null;

        logger.info("执行分页获取账单表数据");
        for (; pageNo > 0; pageNo--) {
            tableList = new ArrayList<JSONObject>();

            // 分页获取未上传的账单,包括pos_bill2
            billList = uploadDao.getPosBillDataByReportDate(tenancyId, storeId, reportDate, isOpenBill, UPLOAD_DATA_SIZE);
            logger.info("执行分页获取账单表数据，数据行数："+billList.size());
            int count = UPLOAD_DATA_SIZE;
            if (null != billList && billList.size() > 0) {
                count = UPLOAD_DATA_SIZE - billList.size();
            }
            if (0 < count) {
                bill2List = uploadDao.getPosBill2DataByReportDate(tenancyId, storeId, reportDate, isOpenBill, count);
            }
            logger.info("执行getPosBill2DataByReportDate返回数据集大小："+bill2List.size());

            billAllList = new ArrayList<JSONObject>();
            if (null != billList && billList.size() > 0) {
                billAllList.addAll(billList);
            }
            if (null != bill2List && bill2List.size() > 0) {
                billAllList.addAll(bill2List);
            }
            logger.info("执行分页获取账单表数据结束");
            JSONObject tableDataJson = null;
            if (null != billAllList && billAllList.size() > 0) {
                tableDataJson = new JSONObject();
                tableDataJson.put("tableName", "pos_bill");

                //压缩 并记录日志
//				String n1=JavaZipUtils.getStringSize(billAllList.toString().getBytes().length);
//				String zipdata = JavaZipUtils.zipJsonList(billAllList);
//				String n2=JavaZipUtils.getStringSize(zipdata.getBytes().length);
//				logger.info("压缩前 pos_bill 数据集大小是 "+n1);
//				logger.info("压缩后 pos_bill 数据集大小是 "+n2);

                tableDataJson.put("dataList", billAllList);
                tableList.add(tableDataJson);
                logger.info("上传数据：<上传表名:" + "pos_bill" + ";上传数据" + billAllList.size() + "条>");

                billItemList = uploadDao.getPosBillLinkDataByBillList(tenancyId, storeId, "pos_bill_item", billAllList, reportDate);
                logger.info("billItemList数据集大小："+billItemList.size());
                billItem2List = uploadDao.getPosBillLinkDataByBillList(tenancyId, storeId, "pos_bill_item", "pos_bill_item2", billAllList, reportDate);
                logger.info("billItem2List数据集大小："+billItem2List.size());
                billItemAllList = new ArrayList<JSONObject>();
                if (null != billItemList && billItemList.size() > 0) {
                    billItemAllList.addAll(billItemList);
                }
                if (null != billItem2List && billItem2List.size() > 0) {
                    billItemAllList.addAll(billItem2List);
                }

                if (null != billItemAllList && billItemAllList.size() > 0) {
                    tableDataJson = new JSONObject();
                    tableDataJson.put("tableName", "pos_bill_item");

                    //压缩 并记录日志
//					String n11=JavaZipUtils.getStringSize(billItemAllList.toString().getBytes().length);
//					String zipdata01 = JavaZipUtils.zipJsonList(billItemAllList);
//					String n21=JavaZipUtils.getStringSize(zipdata01.getBytes().length);
//					logger.info("压缩前 pos_bill_item 数据集大小是 "+n11);
//					logger.info("压缩后 pos_bill_item 数据集大小是 "+n21);

                    tableDataJson.put("dataList", billItemAllList);
                    tableList.add(tableDataJson);
                    logger.info("上传数据：<上传表名:" + "pos_bill_item" + ";上传数据" + billItemAllList.size() + "条>");
                }

                billPaymentList = uploadDao.getPosBillLinkDataByBillList(tenancyId, storeId, "pos_bill_payment", billAllList, reportDate);
                logger.info("billPaymentList数据集大小："+billPaymentList.size());
                billPayment2List = uploadDao.getPosBillLinkDataByBillList(tenancyId, storeId, "pos_bill_payment", "pos_bill_payment2", billAllList, reportDate);
                logger.info("billPayment2List数据集大小："+billPayment2List.size());
                billPaymentAllList = new ArrayList<JSONObject>();
                if (null != billPaymentList && billPaymentList.size() > 0) {
                    billPaymentAllList.addAll(billPaymentList);
                }
                if (null != billPayment2List && billPayment2List.size() > 0) {
                    billPaymentAllList.addAll(billPayment2List);
                }
                if (null != billPaymentAllList && billPaymentAllList.size() > 0) {
                    tableDataJson = new JSONObject();
                    tableDataJson.put("tableName", "pos_bill_payment");

                    //压缩 并记录日志
//					String n11=JavaZipUtils.getStringSize(billPaymentAllList.toString().getBytes().length);
//					String zipdata02 = JavaZipUtils.zipJsonList(billPaymentAllList);
//					String n21=JavaZipUtils.getStringSize(zipdata02.getBytes().length);
//					logger.info("压缩前 pos_bill_payment 数据集大小是 "+n11);
//					logger.info("压缩后 pos_bill_payment 数据集大小是 "+n21);

                    tableDataJson.put("dataList", billPaymentAllList);
                    tableList.add(tableDataJson);
                    logger.info("上传数据：<上传表名:" + "pos_bill_payment" + ";上传数据" + billPaymentAllList.size() + "条>");
                }

                List<JSONObject> tableDataList = null;
                for (String tableName : tableNames) {
                    tableDataList = null;
                    if ("pos_bill".equals(tableName) || "pos_bill2".equals(tableName) || "pos_bill_item".equals(tableName) || "pos_bill_item2".equals(tableName) || "pos_bill_payment".equals(tableName) || "pos_bill_payment2".equals(tableName)) {
                        continue;
                    } else {
                        tableDataList = uploadDao.getPosBillLinkDataByBillList(tenancyId, storeId, tableName, billAllList, reportDate);
                    }

                    logger.info("billPaymentList数据集大小："+billPaymentList.size());
                    if (null != tableDataList && tableDataList.size() > 0) {
                        tableDataJson = new JSONObject();
                        tableDataJson.put("tableName", tableName);

                        //压缩 并记录日志
//						String n11=JavaZipUtils.getStringSize(tableDataList.toString().getBytes().length);
//						String zipdata03 = JavaZipUtils.zipJsonList(tableDataList);
//						String n21=JavaZipUtils.getStringSize(zipdata03.getBytes().length);
//						logger.info("压缩前 "+tableName+" 数据集大小是 "+n11);
//						logger.info("压缩后 "+tableName+" 数据集大小是 "+n21);

                        tableDataJson.put("dataList", tableDataList);
                        tableList.add(tableDataJson);
                        logger.info("上传数据：<上传表名:" + tableName + ";上传数据" + tableDataList.size() + "条>");
                    }
                }
            }

            if (null != tableList && tableList.size() > 0) {
                JSONObject requestJson = new JSONObject();
                requestJson.put("report_date", DateUtil.formatDate(reportDate));

                //对 tableList 进行二次压缩 缩小体积
                String n1 = JavaZipUtils.getStringSize(tableList.toString().getBytes().length);
                String tableListZip = JavaZipUtils.zip(tableList.toString());
                String n2 = JavaZipUtils.getStringSize(tableListZip.getBytes().length);
                logger.info("对Data[tableList]进行二次压缩前数据集大小是 " + n1);
                logger.info("对Data[tableList]进行二次压缩后数据集大小是 " + n2);

                requestJson.put("tableList", tableListZip);
                requestJson.put("compress", "yes");

                List<JSONObject> requestList = new ArrayList<JSONObject>();
                requestList.add(requestJson);

                Data requestData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
                requestData.setData(requestList);
                requestData.setType(Type.DATATRANS);
                requestData.setOper(Oper.upload_bill_info);

                long t1 = System.currentTimeMillis();
                Data resultData = this.commonPost(requestData);
                logger.info("commonPost上传账单耗时 " + (System.currentTimeMillis() - t1) + " ms");

                if (resultData.isSuccess()) {
                    for (JSONObject dataJson : tableList) {
                        if (MODE_UPLOAD_DATA.equals(uploadMode)) {
                            // 全量上传
                            if ("pos_bill".equals(dataJson.optString("tableName"))) {
                                uploadDao.setUploadTagByReportDate(tenancyId, storeId, reportDate, "pos_bill");
                                uploadDao.setUploadTagByReportDate(tenancyId, storeId, reportDate, "pos_bill2");
                            } else if ("pos_bill_item".equals(dataJson.optString("tableName"))) {
                                uploadDao.setUploadTagByReportDate(tenancyId, storeId, reportDate, "pos_bill_item");
                                uploadDao.setUploadTagByReportDate(tenancyId, storeId, reportDate, "pos_bill_item2");
                            } else if ("pos_bill_payment".equals(dataJson.optString("tableName"))) {
                                uploadDao.setUploadTagByReportDate(tenancyId, storeId, reportDate, "pos_bill_payment");
                                uploadDao.setUploadTagByReportDate(tenancyId, storeId, reportDate, "pos_bill_payment2");
                            } else {
                                uploadDao.setUploadTagByReportDate(tenancyId, storeId, reportDate, dataJson.optString("tableName"));
                            }
                            continue;
                        }

                        // 增量上传
                        if ("pos_bill".equals(dataJson.optString("tableName"))) {
                            uploadDao.updatePosItemUploadTag(tenancyId, storeId, "pos_bill", billList, null);
                            uploadDao.updatePosItemUploadTag(tenancyId, storeId, "pos_bill2", bill2List, null);
                        } else if ("pos_bill_item".equals(dataJson.optString("tableName"))) {
                            uploadDao.updatePosItemUploadTag(tenancyId, storeId, "pos_bill_item", billItemList, null);
                            uploadDao.updatePosItemUploadTag(tenancyId, storeId, "pos_bill_item2", billItem2List, null);
                        } else if ("pos_bill_payment".equals(dataJson.optString("tableName"))) {
                            uploadDao.updatePosItemUploadTag(tenancyId, storeId, "pos_bill_payment", billPaymentList, null);
                            uploadDao.updatePosItemUploadTag(tenancyId, storeId, "pos_bill_payment2", billPayment2List, null);
                        } else {
                            uploadDao.updatePosItemUploadTag(tenancyId, storeId, dataJson.optString("tableName"), dataJson.optJSONArray("dataList"), null);
                        }
                    }
                    reData.setCode(Constant.CODE_SUCCESS);
                    reData.setMsg(Constant.UPLOAD_DATA_SUCCESS);
                    reData.setSuccess(true);
                } else {
                    reData.setCode(Constant.CODE_CONN_EXCEPTION);
                    reData.setMsg(Constant.UPLOAD_DATA_FAILURE);
                    reData.setSuccess(false);
                }
            } else {
                reData.setCode(Constant.CODE_UPLOAD_DATA_NULL);
                reData.setMsg(Constant.UPLOAD_DATA_NULL);
                reData.setSuccess(false);
            }
        }
        return reData;
    }

    @SuppressWarnings("unchecked")
    @Override
    public Data UploadDataForOtherByHttp(String tenancyId, int storeId, List<String> tableNames, Date reportDate, String uploadMode) throws Exception {
        Data reData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);

        List<JSONObject> tableList = new ArrayList<JSONObject>();

        JSONObject tableDataJson = null;
        List<JSONObject> tableDataList = null;
        for (String tableName : tableNames) {
            tableDataList = uploadDao.getPosUploadDataByReportDate(tenancyId, storeId, reportDate, tableName);

            if (null != tableDataList && tableDataList.size() > 0) {
                tableDataJson = new JSONObject();
                tableDataJson.put("tableName", tableName);

                //压缩 并记录日志
//				String n11=JavaZipUtils.getStringSize(tableDataList.toString().getBytes().length);
//				String zipdata04 = JavaZipUtils.zipJsonList(tableDataList);
//				String n21=JavaZipUtils.getStringSize(zipdata04.getBytes().length);
//				logger.info("压缩前 "+tableName+" 数据集大小是 "+n11);
//				logger.info("压缩后 "+tableName+" 数据集大小是 "+n21);

                tableDataJson.put("dataList", tableDataList);
                tableList.add(tableDataJson);
                logger.info("上传数据：<上传表名:" + tableName + ";上传数据" + tableDataList.size() + "条>");
            }
        }

        if (null != tableList && tableList.size() > 0) {
            JSONObject requestJson = new JSONObject();
            requestJson.put("report_date", DateUtil.formatDate(reportDate));

            //对 tableList 进行二次压缩 缩小体积
            String n1 = JavaZipUtils.getStringSize(tableList.toString().getBytes().length);
            String tableListZip = JavaZipUtils.zip(tableList.toString());
            String n2 = JavaZipUtils.getStringSize(tableListZip.getBytes().length);
            logger.info("对Data[tableList]进行二次压缩前数据集大小是 " + n1);
            logger.info("对Data[tableList]进行二次压缩后数据集大小是 " + n2);

            requestJson.put("tableList", tableListZip);
            requestJson.put("compress", "yes");

            List<JSONObject> requestList = new ArrayList<JSONObject>();
            requestList.add(requestJson);

            Data requestData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
            requestData.setData(requestList);
            requestData.setType(Type.DATATRANS);
            requestData.setOper(Oper.upload_changeshift_info);

            long t1 = System.currentTimeMillis();
            Data resultData = this.commonPost(requestData);
            logger.info("commonPost上传其他表耗时 " + (System.currentTimeMillis() - t1) + " ms");

            if (Constant.CODE_SUCCESS == resultData.getCode()) {
                for (JSONObject dataJson : tableList) {
                    if (MODE_UPLOAD_DATA.equals(uploadMode)) {
                        // 全量上传
                        uploadDao.setUploadTagByReportDate(tenancyId, storeId, reportDate, dataJson.optString("tableName"));
                        continue;
                    }
                    // 增量上传
                    uploadDao.updatePosItemUploadTag(tenancyId, storeId, dataJson.optString("tableName"), dataJson.optJSONArray("dataList"), null);
                }
                reData.setCode(Constant.CODE_SUCCESS);
                reData.setMsg(Constant.UPLOAD_DATA_SUCCESS);
                reData.setSuccess(true);
            } else {
                reData.setCode(Constant.CODE_CONN_EXCEPTION);
                reData.setMsg(Constant.UPLOAD_DATA_FAILURE);
                reData.setSuccess(false);
            }
        } else {
            reData.setCode(Constant.CODE_UPLOAD_DATA_NULL);
            reData.setMsg(Constant.UPLOAD_DATA_NULL);
            reData.setSuccess(false);
        }

        return reData;
    }

    @Override
    public Data UploadDataForOrder(String tenancyId, int storeId, List<String> tableNames, Date reportDate, String uploadMode) throws Exception {
        Data reData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);

        String[] table = tableNames.toArray(new String[tableNames.size()]);
        // TODO Auto-generated method stub
		/*if (MODE_AUTO_UPLOAD_DATA.equals(uploadMode))
		{
			this.AutoUploadDataByMQ(tenancyId, storeId, table);
		}
		else
		{
			reData = this.UploadDataByMQ(tenancyId, storeId, table, reportDate);
		}*/
        //数据重传亦采用增量上传方式已减少数据传输里
        this.AutoUploadDataByMQ(tenancyId, storeId, table);

        return reData;
    }

    @Override
    @Async
    public Data UploadData(Data param) throws Exception {
        ProgressMonitor.setProgress(Progress.DATATRANS, Progress.READY);
        logger.info("数据重传：进入数据重传。");
        Data reData = param.clone();
        try {
            long t1 = System.currentTimeMillis();
            Map<String, Object> map = ReqDataUtil.getDataMap(param);
            Date reportDate = ParamUtil.getDateValue(map, "report_date", true, PosErrorCode.NOT_NULL_REPORT_DATE);

            String tenancyId = param.getTenancy_id();
            int storeId = param.getStore_id();

            JSONObject daycountPara = JSONObject.fromObject("{}");
            daycountPara.element("day_count", DateUtil.format(reportDate));
            logger.info("数据重传：checkDaycount方法执行前耗时" + (System.currentTimeMillis() - t1) + "毫秒。");
            int dayCount = posService.checkDaycount(tenancyId, storeId, daycountPara);
            logger.info("数据重传：checkDaycount方法执行后耗时" + (System.currentTimeMillis() - t1) + "毫秒。");
            if (0 != dayCount) {
                if (1 == dayCount) {
                    reData.setMsg(Constant.ALREADY_DAYCOUNT);
                } else if (100 == dayCount) {
                    reData.setMsg(Constant.ALREADY_DAYCOUNTING);
                }
                reData.setCode(Constant.CODE_STORE_EXCEPTION);
                reData.setSuccess(false);
            }else{
                /* 查询门店数据 */
                String[] tableNames = uploadDao.getSysParameter(tenancyId, storeId, "uploaddatatable").split(",");

                String uploadDataWay = uploadDao.getSysParameter(tenancyId, storeId, "upload_data_way");

                if (UPLOAD_DATA_WAY_HTTP.equals(uploadDataWay)) {
                    logger.info("数据重传：UploadDataByHttp方法执行前耗时" + (System.currentTimeMillis() - t1) + "毫秒。");
                    reData = this.UploadDataByHttp(tenancyId, storeId, tableNames, reportDate, MODE_UPLOAD_DATA);
                    logger.info("数据重传：UploadDataByHttp方法执行后耗时" + (System.currentTimeMillis() - t1) + "毫秒。");
                } else {
                    logger.info("数据重传：UploadDataByMQ方法执行前耗时" + (System.currentTimeMillis() - t1) + "毫秒。");
                    reData = this.UploadDataByMQ(tenancyId, storeId, tableNames, reportDate);
                    logger.info("数据重传：UploadDataByMQ方法执行后耗时" + (System.currentTimeMillis() - t1) + "毫秒。");
                }
                logger.info("上传接口合计耗时 " + (System.currentTimeMillis() - t1) + " ms,上传结果:"+reData);
            }

           String is_auto_daily_count = uploadDao.getSysParameter(tenancyId, storeId, "is_auto_daily_count");
            if (reData.getCode() == Constant.CODE_SUCCESS) {
                ProgressMonitor.setProgress(Progress.DATATRANS, Progress.DONE);
                //打烊自动自动日结
                if ("1".equals(is_auto_daily_count) && !Type.DATATRANS.equals(param.getType())) //0:不自动日结；1：自动日结
                {
                    //1.调用自动日结接口
                    daycountPara = new JSONObject();
                    daycountPara.element("dstype", "hq");
                    daycountPara.element("day_count", com.tzx.framework.common.util.DateUtil.format(reportDate));

                    try {
                        JSONObject result = posService.autoDailySettlement(tenancyId, storeId, daycountPara);
                        if (result == null || !result.optBoolean("success")) {
                            int i = 0;
                            while (i < 3) {
                                result = posService.autoDailySettlement(tenancyId, storeId, daycountPara);
                                if (result == null || !result.optBoolean("success")) {
                                    Thread.sleep(3000);// 暂停3秒
                                    i = i + 1;
                                    logger.debug("开始重试第" + i + "次");
                                } else {
                                    break;
                                }
                            }
                        }
                        JSONObject jsobj = posService.findAutoDailyInfoByParam(tenancyId, storeId, daycountPara);
                        if (jsobj == null || jsobj.isEmpty()) {
                            if (result == null || !result.getBoolean("success")) { //日结失败
                                //插入记录   day_end_state 1 -成功  upload_state 1--成功 daily_count_state 0--失败  daily_count_num 1次
                                jsobj = new JSONObject();
                                jsobj.put("day_end_state", "1");
                                jsobj.put("upload_state", "1");
                                jsobj.put("daily_count_state", "0");
                                jsobj.put("day_count", com.tzx.framework.common.util.DateUtil.format(reportDate));
                                jsobj.put("upload_num", "1");
                                jsobj.put("daily_count_num", "1");
                                posService.updateAutoDailyInfo(tenancyId, storeId, jsobj);
                            }
                        } else {
                            if (result == null || !result.optBoolean("success")) { //日结失败
                                //修改记录   day_end_state 1 -成功  upload_state  1--成功 daily_count_state 0--失败  daily_count_num 1次
                                jsobj.put("day_end_state", "1");
                                jsobj.put("upload_state", "1");
                                jsobj.put("daily_count_state", "0");
                                jsobj.put("upload_num", jsobj.optInt("upload_num") + 1);
                                jsobj.put("daily_count_num", jsobj.optInt("daily_count_num") + 1);
                                posService.updateAutoDailyInfo(tenancyId, storeId, jsobj);
                                reData.setCode(Constant.CODE_INNER_EXCEPTION);
                                reData.setMsg(Constant.AUTO_DAILY_FAILURE);
                                reData.setSuccess(false);

                            } else {
                                //修改记录   day_end_state 1 -成功  upload_state 1--成功 daily_count_state 1--成功  daily_count_num  = daily_count_num+1次
                                jsobj.put("day_end_state", "1");
                                jsobj.put("upload_state", "1");
                                jsobj.put("daily_count_state", "1");
                                jsobj.put("upload_num", jsobj.optInt("upload_num") + 1);
                                jsobj.put("daily_count_num", jsobj.optInt("daily_count_num") + 1);
                                posService.updateAutoDailyInfo(tenancyId, storeId, jsobj);

                                reData.setCode(Constant.CODE_SUCCESS);
                                reData.setMsg(Constant.AUTO_DAILY_SUCCESS);
                                reData.setSuccess(true);
                            }
                        }
                    } catch (Exception e) {
                        //不要使用 //e.printStackTrace();，如果有必要，用logger.error替代;
                        logger.info("上传数据失败，原因：" + ExceptionMessage.getExceptionMessage(e));
                        reData.setCode(Constant.CODE_INNER_EXCEPTION);
                        reData.setMsg(Constant.AUTO_DAILY_FAILURE);
                        reData.setSuccess(false);
                    }
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
            logger.info("上传数据失败，原因：" + ExceptionMessage.getExceptionMessage(e));
            reData.setCode(Constant.CODE_INNER_EXCEPTION);
            reData.setMsg(Constant.UPLOAD_DATA_FAILURE);
            reData.setSuccess(false);
        }
        if (reData.getCode() == Constant.CODE_SUCCESS) {
            ProgressMonitor.setProgress(Progress.DATATRANS, Progress.DONE);
        } else {
            ProgressMonitor.setProgress(Progress.DATATRANS, Progress.EXCEPTION,reData.getMsg()==null?Constant.UPLOAD_DATA_FAILURE:reData.getMsg());
        }
        return reData;
    }

    private Data commonPost(Data param) throws HttpRestException {
    	int conntimeOut = 5000;    // 连接超时时间 5秒
    	int soTimeOut   = (int)TimeUnit.MINUTES.toMillis(10);   // 读取数据超时  120秒
        String requestUrl = PosPropertyUtil.getMsg("saas.url") + UPLOAD_DATA_URL;
        Data resData = param.clone();
        JSONObject paramJson = JSONObject.fromObject(param);
        //JSONObject result = HttpUtil.post(requestUrl, paramJson,1,true );
        JSONObject result = HttpUtil.post(requestUrl,paramJson,1,conntimeOut,soTimeOut,true);
        Data data = JsonUtil.JsonToData(result);
        if (1 == data.getCode()) {
            resData.setCode(Constant.CODE_CONN_EXCEPTION);
            resData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
            resData.setSuccess(false);
        } else {
            resData.setCode(data.getCode());
            resData.setMsg(data.getMsg());
            resData.setData(data.getData());
            resData.setSuccess(data.isSuccess());
        }

        return resData;
    }
}
