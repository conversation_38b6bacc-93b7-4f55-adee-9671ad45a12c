package com.tzx.orders.service.rest;

import static com.tzx.orders.base.constant.Constant.THIRD_CHANNEL;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.tzx.orders.bo.dto.OrderState;
import com.tzx.orders.bo.dto.PosBill;
import com.tzx.orders.po.springjdbc.dao.OrdersManagementDao;

import org.apache.commons.io.IOUtils;
import org.apache.log4j.Logger;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.tzx.base.bo.ProcessMessageService;
import com.tzx.base.common.util.PosPropertyUtil;
import com.tzx.clientorder.wxorder.bo.WxInsertOrderService;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.Tools;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.orders.base.constant.Constant;
import com.tzx.orders.base.util.OrderUtil;
import com.tzx.orders.bo.OrdersManagementService;
import com.tzx.pos.base.util.PathUtil;
import com.tzx.pos.bo.OrderDeliveryService;

import net.sf.json.JSONObject;

@Controller("OrdersManagementRest")
@RequestMapping("/pos/ordersManagementRest")
public class OrdersManagementRest
{
	private static final Logger		logger	= Logger.getLogger(OrdersManagementRest.class);

	// @Resource(name = OrdersManagementService.NAME)
	private OrdersManagementService	ordersService;
	private ApplicationContext		context	= SpringConext.getApplicationContext();
	@Resource(name =ProcessMessageService.NAME)
	private ProcessMessageService processMessageService;

	@Resource(name =OrderDeliveryService.NAME)
	private OrderDeliveryService orderDeliveryService;

	@Resource(name = WxInsertOrderService.NAME)
	private WxInsertOrderService wxInsertOrderService;

	@Resource(name = OrdersManagementDao.NAME)
	private OrdersManagementDao ordersDao;

    /**
     * @param request
     * @param response
     */
    @RequestMapping(value = "/newOrder")
    public void newOrder(HttpServletRequest request, HttpServletResponse response) throws IOException {
        InputStream inputStream=null;
        OutputStream outputStream=null;
        JSONObject result=new JSONObject();
        try {
            inputStream = request.getInputStream();
            outputStream = response.getOutputStream();
            JSONObject data = JSONObject.fromObject(IOUtils.toString(inputStream));
            String tenancyId = data.optString("tenancy_id");
            int storeId = data.optInt("store_id");
            DBContextHolder.setTenancyid(tenancyId);
            processMessageService.ordersManagement(tenancyId,storeId,data);
            result.put("code", 0);
            result.put("msg", "ok");
        } catch (SystemException e) {
            result.put("code", e.getErrorCode().getNumber());
            result.put("msg", getMessage(e, e.getMessage()));
        } catch (Exception e) {
            e.printStackTrace();
            result.put("code", 1);
            result.put("msg", e.getMessage());
        }finally {
            IOUtils.write(result.toString(),outputStream);
            IOUtils.closeQuietly(inputStream);
            IOUtils.closeQuietly(outputStream);
        }
    }


	/**
	 * 获取订单列表
	 * 
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/getOrdersList")
	public void getOrdersList(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		JSONObject resultJson = new JSONObject();
		try
		{
			Map<String, String[]> map = request.getParameterMap();
			
			JSONObject obj = JSONObject.fromObject("{}");
			
			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			
			DBContextHolder.setTenancyid(obj.optString("tenancy_id"));
			ordersService = context.getBean(OrdersManagementService.NAME, OrdersManagementService.class);

			resultJson = ordersService.getOrdersList(obj.optString("tenancy_id"), obj.optInt("store_id"), obj);
		}
		catch (Exception e)
		{
			e.printStackTrace();
			resultJson.put("success", false);
			resultJson.put("msg", "查询订单失败!");
			logger.error("查询订单失败", e);
		}
		finally
		{
			try
			{
				out = response.getWriter();
				out.print(resultJson.toString());
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
				logger.error("查询订单失败", e);
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
	/**
	  * @Description: 查询订单明细菜品
	  * @Title:getOrdersItemDetails
	  * @param:@param request
	  * @param:@param response
	  * @return: void
	  * @author: shenzhanyu
	  * @version: 1.0
	  * @Date: 2018年10月10日
	 */
	@RequestMapping(value = "/getOrdersItemDetails")
	public void getOrdersItemDetails(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		JSONObject resultJson = new JSONObject();
		try
		{
			Map<String, String[]> map = request.getParameterMap();
			
			JSONObject obj = JSONObject.fromObject("{}");
			
			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			
			DBContextHolder.setTenancyid(obj.optString("tenancy_id"));
			ordersService = context.getBean(OrdersManagementService.NAME, OrdersManagementService.class);

			resultJson = ordersService.getOrdersItemDetails(obj.optString("tenancy_id"), obj.optInt("store_id"), obj);
		}
		catch (Exception e)
		{
			e.printStackTrace();
			resultJson.put("success", false);
			resultJson.put("msg", "查询订单失败!");
			logger.error("查询订单失败", e);
		}
		finally
		{
			try
			{
				out = response.getWriter();
				out.print(resultJson.toString());
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
				logger.error("查询订单失败", e);
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}

	/**
	 * 获取订单列表
	 * 
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/getUnusualReason")
	public void getUnusualReason(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		JSONObject resultJson = new JSONObject();
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			
			DBContextHolder.setTenancyid(obj.optString("tenancy_id"));
			ordersService = context.getBean(OrdersManagementService.NAME, OrdersManagementService.class);

			resultJson = ordersService.getReasonDetail(obj.optString("tenancy_id"), obj.optInt("store_id"), obj);
		}
		catch (Exception e)
		{
			e.printStackTrace();
			resultJson.put("success", false);
			resultJson.put("msg", "查询异常原因失败!");
			logger.error("查询异常原因失败", e);
		}
		finally
		{
			try
			{
				out = response.getWriter();
				out.print(resultJson.toString());
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
				logger.error("查询订单失败", e);
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
	/**
	 * 获取支付方式
	 * 
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/getPaymentWayList")
	public void getPaymentWayList(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		JSONObject resultJson = new JSONObject();
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
			DBContextHolder.setTenancyid(obj.optString("tenancy_id"));
			ordersService = context.getBean(OrdersManagementService.class);

			resultJson = ordersService.getPaymentWay(obj.optString("tenancy_id"), obj.optInt("store_id"), obj);
		}
		catch (Exception e)
		{
			e.printStackTrace();
			resultJson.put("success", false);
			resultJson.put("msg", "查询支付方式失败!");
			logger.error("查询支付方式失败", e);
		}
		finally
		{
			try
			{
				out = response.getWriter();
				out.print(resultJson.toString());
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
				logger.error("查询支付方式失败", e);
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}

	/**
	 * 获取取消类型
	 * 
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/getReasonType")
	public void getReasonType(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		JSONObject resultJson = new JSONObject();
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}

			DBContextHolder.setTenancyid(obj.optString("tenancy_id"));
			ordersService = context.getBean(OrdersManagementService.NAME, OrdersManagementService.class);

			resultJson = ordersService.getUnusualReason(obj.optString("tenancy_id"), obj.optInt("store_id"), obj);
		}
		catch (SystemException se)
		{
			resultJson.put("success", false);
			resultJson.put("msg", getMessage(se,"查询订单异常原因失败"));
			logger.error("查询订单异常原因失败", se);
		}
		catch (Exception e)
		{
			e.printStackTrace();
			resultJson.put("success", false);
			resultJson.put("msg", "查询订单异常原因失败!");
			logger.error("查询订单异常原因失败", e);
		}
		finally
		{
			try
			{
				out = response.getWriter();
				out.print(resultJson.toString());
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
				logger.error("查询订单异常原因失败", e);
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}

	/**
	  * @Description: 取单、入账
	  * @Title:takeOrders
	  * @param:@param request
	  * @param:@param response
	  * @return: void
	  * @author: shenzhanyu
	  * @version: 1.0
	 */
	@RequestMapping(value = "/takeOrders")
	public void takeOrders(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		JSONObject resultJson = JSONObject.fromObject("{'success':true,'msg':'取单成功!'}");
        String tenancyId = null;
        int storeId=0;
        JSONObject obj=null;
        JSONObject print=null;
		String  single_time ="";
		String chanel =null;
		String orderState=null;
		try
		{
			obj = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				if(key.equals("single_time")){
					single_time = map.get(key)[0];
				}
				obj.put(key, map.get(key)[0]);
			}

			tenancyId=obj.optString("tenancy_id");
			storeId=obj.optInt("store_id");
			chanel = obj.optString("channel");
			orderState = obj.optString("order_state");
			
			DBContextHolder.setTenancyid(tenancyId);
			ordersService = context.getBean(OrdersManagementService.NAME, OrdersManagementService.class);
			
			JSONObject para = ordersService.getPosOptState(tenancyId, storeId);
			if(!THIRD_CHANNEL.contains(chanel)){ // 微信取单
				para = ordersService.getPosOptState4WX(tenancyId, storeId);
			}
			obj.put("opt_num", para.optString("opt_num"));
			obj.put("pos_num", para.optString("pos_num"));
			obj.put("report_date", para.optString("report_date"));
			obj.put("shift_id", para.optInt("shift_id"));
			
			// 获取订单信息
			String orderCode = obj.optString("order_code").intern();

			logger.info("操作手动取单 ："+para.optString("opt_num") +" 操作时间： "+DateUtil.getNowDateYYDDMMHHMMSS() +"订单信息：渠道"+obj.optString("channel") +"订单号 ："+orderCode);

			boolean result= ordersDao.getTakeOrderFlag(tenancyId, storeId, orderCode);
			if(result){
				resultJson.put("success", false);
				resultJson.put("msg","订单号："+orderCode+"的订单正在自动取单，暂不能手动取单。");
				return;
			}

			PosBill order = ordersDao.getOrderByOrdercode(tenancyId, storeId, orderCode);
			//是微信渠道且is_online_payment不为1,1是结账
			if (Constant.WX02_CHANNEL.equals(obj.optString("channel")) && null != order
					&& !"1".equals(order.getIs_online_payment())){
				//&& !"1".equals(order.getIs_online_payment())
				// 查询门店业态,判断业态
				JSONObject organ = ordersDao.getOrganInfo(tenancyId, storeId);
				if (organ == null || organ.isEmpty()) {
					throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_ORGAN);
				}
				if (!ordersDao.checkReportDate(tenancyId, storeId, para.optString("report_date"))) {
					throw SystemException.getInstance(PosErrorCode.CHECK_REPORT_DATE_ERROR);
				}
				if(order == null){
					throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_BILL);
				}

				order.setIsprint("Y");
				order.setReport_date(para.optString("report_date"));
				order.setShift_id(para.optString("shift_id"));
				order.setItem_menu_id(organ.optString("item_menu_id"));
				order.setOpt_num(para.optString("opt_num"));
				order.setWaiter_num(para.optString("waiter_num"));
				order.setPos_num(para.optString("pos_num"));
				order.setItem(ordersDao.getOrderItemByOrdercode(tenancyId, storeId, orderCode, obj.optString("channel")));
				logger.info("***************微信后付下单：" + order.toString());
				String consigner_phone = order.getConsigner_phone(); // 会员手机号

				// 获取订单状态
				OrderState orderState2 = ordersDao.getOrderStateByOrderCode(tenancyId, storeId, orderCode);
				if (null==orderState2) {
					throw SystemException.getInstance(PosErrorCode.ORDER_NOT_EXISTS_ERROR);
				}
				//发送接收订单状态
				OrderUtil.changeOrderState(tenancyId,storeId,orderState2,Constant.ORDER_STATE_RECEIVE,Constant.NOTIFY_SAAS);

				OrderUtil.hook(obj.optString("channel"));
				//微信渠道的订单，后付有加菜(没有openId,暂存手机号)
				wxInsertOrderService.saveOrUpdateOrder(tenancyId, storeId, order.getTable_code(), consigner_phone, orderCode, order);
				//重置同步订单状态和结算精度
				OrderUtil.unhook();

			}
			else
			{
				print = ordersService.takeOrders(tenancyId, storeId, obj);
				if (print != null && !print.isEmpty())
				{
					if(print.containsKey("existsNotMappingItmes") && print.optBoolean("existsNotMappingItmes")){
						resultJson.put("success", false);
						resultJson.put("msg", "菜品【" + print.getJSONObject("orderPrint").optString("not_exits_mapping_items") + "】映射有误。请修复映射关系后，重新入账");
					}else if(print.containsKey("existsNotItmes") && print.optBoolean("existsNotItmes")){
						resultJson.put("success", false);
						resultJson.put("msg", "菜品【" + print.getJSONObject("orderPrint").optString("not_exits_items") + "】不存在，请下发再重新入账");
					}
				}
			}
		}
		catch (SystemException se)
		{
			resultJson.put("success", false);
			resultJson.put("msg",getMessage(se,null));
			logger.error("取单失败", se);
            print=ordersService.takeExceptionOrders(tenancyId,storeId,obj,resultJson);
		}
		catch (Exception e)
		{
			e.printStackTrace();
			resultJson.put("success", false);
			resultJson.put("msg", "取单失败!");
			logger.error("取单失败", e);
            print=ordersService.takeExceptionOrders(tenancyId,storeId,obj,resultJson);
		}
		finally
		{
            if (null != print && !print.containsKey("existsNotMappingItmes") && !orderState.equals(Constant.ORDER_STATE_EXCEPTION)) {
                //打印
                try {
					// 下单时间与当前时间10分钟之内进行打印
					Long currentTime = System.currentTimeMillis();
					Date singleTime = DateUtil.parseDateAll(single_time);
					String printTime = "10";//默认外卖打印范围时间内
					if(PosPropertyUtil.getMsg("waimai.print.time")!=null && !"".equals(PosPropertyUtil.getMsg("waimai.print.time"))){
						printTime = PosPropertyUtil.getMsg("waimai.print.time");
					}
					if((currentTime-singleTime.getTime())<1000*60*Integer.parseInt(printTime)){
						ordersService.printOrderBuffer(tenancyId, storeId, print);
					}
                } catch (Exception e) {
                    logger.error("外卖订单打印失败", e);
                }
            }
            try {
                out = response.getWriter();
                out.print(resultJson.toString());
                out.flush();
                out.close();
            } catch (Exception e) {
                logger.error("取单失败", e);
            } finally {
                if (out != null) out.close();
            }
		}
	}

	/**
	 * 付款
	 * 
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/paymentOrders")
	public void paymentOrders(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		JSONObject resultJson = JSONObject.fromObject("{'success':true,'msg':'付款成功!'}");
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}

			DBContextHolder.setTenancyid(obj.optString("tenancy_id"));
			ordersService = context.getBean(OrdersManagementService.NAME, OrdersManagementService.class);

			String tenancyId=obj.optString("tenancy_id");
			int storeId=obj.optInt("store_id");
			JSONObject para = ordersService.getPosOptState(tenancyId, storeId);
			obj.put("opt_num", para.optString("opt_num"));
			obj.put("pos_num", para.optString("pos_num"));
			obj.put("report_date", para.optString("report_date"));
			obj.put("shift_id", para.optInt("shift_id"));

			JSONObject print=ordersService.paymentOrders(tenancyId, storeId, obj);
            //打印
            ordersService.printOrderBuffer(tenancyId,storeId,print);
		}
		catch (SystemException se)
		{
			resultJson.put("success", false);
			resultJson.put("msg", getMessage(se,"付款失败"));
			logger.error("付款失败", se);
		}
		catch (Exception e)
		{
			e.printStackTrace();
			resultJson.put("success", false);
			resultJson.put("msg", "付款失败!");
			logger.error("付款失败", e);
		}
		finally
		{
			try
			{
				out = response.getWriter();
				out.print(resultJson.toString());
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
				logger.error("付款失败", e);
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}

	@RequestMapping(value = "/getQRCode")
	public void getQRCodeForPayment(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		JSONObject resultJson = new JSONObject();
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}

			DBContextHolder.setTenancyid(obj.optString("tenancy_id"));
			ordersService = context.getBean(OrdersManagementService.NAME, OrdersManagementService.class);
			JSONObject para = ordersService.getPosOptState(obj.optString("tenancy_id"), obj.optInt("store_id"));
			obj.put("opt_num", para.optString("opt_num"));
			obj.put("pos_num", para.optString("pos_num"));
			obj.put("report_date", para.optString("report_date"));
			obj.put("shift_id", para.optInt("shift_id"));
			
			resultJson = ordersService.getQRCodeForPayment(obj.optString("tenancy_id"), obj.optInt("store_id"), obj, PathUtil.getWebRoot(request));
		}
		catch (SystemException se)
		{
			resultJson.put("success", false);
			resultJson.put("msg", getMessage(se,"付款失败"));
			logger.error("付款失败", se);
		}
		catch (Exception e)
		{
			e.printStackTrace();
			resultJson.put("success", false);
			resultJson.put("msg", "付款失败!");
			logger.error("付款失败", e);
		}
		finally
		{
			try
			{
				out = response.getWriter();
				out.print(resultJson.toString());
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
				logger.error("付款失败", e);
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}

	@RequestMapping(value = "/queryThirdPayment")
	public void queryThirdPayment(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		JSONObject resultJson = new JSONObject();
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}

			DBContextHolder.setTenancyid(obj.optString("tenancy_id"));
			ordersService = context.getBean(OrdersManagementService.NAME, OrdersManagementService.class);

			resultJson = ordersService.queryThirdPayment(obj.optString("tenancy_id"), obj.optInt("store_id"), obj, PathUtil.getWebRoot(request));
		}
		catch (SystemException se)
		{
			resultJson.put("success", false);
			resultJson.put("msg", getMessage(se,"付款失败"));
			logger.error("付款失败", se);
		}
		catch (Exception e)
		{
			e.printStackTrace();
			resultJson.put("success", false);
			resultJson.put("msg", "付款失败!");
			logger.error("付款失败", e);
		}
		finally
		{
			try
			{
				out = response.getWriter();
				out.print(resultJson.toString());
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
				logger.error("付款失败", e);
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}

	/**
	 * 补打印
	 * 
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/printOrders")
	public void printOrders(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		JSONObject resultJson = JSONObject.fromObject("{'success':true,'msg':'订单打印成功!'}");
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}

			DBContextHolder.setTenancyid(obj.optString("tenancy_id"));
			ordersService = context.getBean(OrdersManagementService.NAME, OrdersManagementService.class);

			ordersService.printOrders(obj.optString("tenancy_id"), obj.optInt("store_id"), obj);
		}
		catch (SystemException se)
		{
			resultJson.put("success", false);
			resultJson.put("msg", getMessage(se,"订单打印失败"));
			logger.error("订单打印失败", se);
		}
		catch (Exception e)
		{
			e.printStackTrace();
			resultJson.put("success", false);
			resultJson.put("msg", "订单打印失败!");
			logger.error("订单打印失败", e);
		}
		finally
		{
			try
			{
				out = response.getWriter();
				out.print(resultJson.toString());
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
				logger.error("订单打印失败", e);
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}

	/**
	 * 门店取消
	 * 
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/cancelOrders")
	public void cancelOrders(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		JSONObject resultJson = JSONObject.fromObject("{'success':true,'msg':'取消订单成功!'}");
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}

			DBContextHolder.setTenancyid(obj.optString("tenancy_id"));
			ordersService = context.getBean(OrdersManagementService.NAME, OrdersManagementService.class);
			JSONObject para = ordersService.getPosOptState(obj.optString("tenancy_id"), obj.optInt("store_id"));
			obj.put("opt_num", para.optString("opt_num"));
			obj.put("pos_num", para.optString("pos_num"));
			obj.put("report_date", para.optString("report_date"));
			obj.put("shift_id", para.optInt("shift_id"));

			ordersService.cancelOrders(obj.optString("tenancy_id"), obj.optInt("store_id"), obj);
		}
		catch (SystemException se)
		{
			resultJson.put("success", false);
			resultJson.put("msg", getMessage(se,"取消订单失败"));
			logger.error("取消订单失败", se);
		}
		catch (Exception e)
		{
			e.printStackTrace();
			resultJson.put("success", false);
			resultJson.put("msg", "取消订单失败!");
			logger.error("取消订单失败", e);
		}
		finally
		{
			try
			{
				out = response.getWriter();
				out.print(resultJson.toString());
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
				logger.error("取消订单失败", e);
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}

    /**
     * 门店取消微信后付异常订单
     *
     * @param request
     * @param response
     */
    @RequestMapping(value = "/cancelWxOrders")
    public void cancelWxOrders(HttpServletRequest request, HttpServletResponse response)
    {
        response.setContentType("text/html; charset=UTF-8");
        response.setContentType("text/html");
        response.setCharacterEncoding("UTF-8");
        PrintWriter out = null;
        JSONObject resultJson = JSONObject.fromObject("{'success':true,'msg':'取消订单成功!'}");
        try
        {
            JSONObject obj = JSONObject.fromObject("{}");

            Map<String, String[]> map = request.getParameterMap();

            for (String key : map.keySet())
            {
                obj.put(key, map.get(key)[0]);
            }

            DBContextHolder.setTenancyid(obj.optString("tenancy_id"));
            ordersService = context.getBean(OrdersManagementService.NAME, OrdersManagementService.class);
            JSONObject para = ordersService.getPosOptState(obj.optString("tenancy_id"), obj.optInt("store_id"));
            obj.put("opt_num", para.optString("opt_num"));
            obj.put("pos_num", para.optString("pos_num"));
            obj.put("report_date", para.optString("report_date"));
            obj.put("shift_id", para.optInt("shift_id"));

            ordersService.cancelWxOrders(obj.optString("tenancy_id"), obj.optInt("store_id"), obj);
        }
        catch (SystemException se)
        {
            resultJson.put("success", false);
            resultJson.put("msg", getMessage(se,"取消订单失败"));
            logger.error("取消订单失败", se);
        }
        catch (Exception e)
        {
            e.printStackTrace();
            resultJson.put("success", false);
            resultJson.put("msg", "取消订单失败!");
            logger.error("取消订单失败", e);
        }
        finally
        {
            try
            {
                out = response.getWriter();
                out.print(resultJson.toString());
                out.flush();
                out.close();
            }
            catch (Exception e)
            {
                logger.error("取消订单失败", e);
            }
            finally
            {
                if (out != null) out.close();
            }
        }
    }
	
	/**
	  * @Description: 订单配送
	  * @Title:deliveryOrders
	  * @param:@param request
	  * @param:@param response
	  * @return: void
	  * @author: shenzhanyu
	  * @version: 1.0
	 */
	@RequestMapping(value = "/deliveryOrders")
	public void deliveryOrders(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		JSONObject resultJson = JSONObject.fromObject("{'success':true,'msg':'配送成功!'}");
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}

			DBContextHolder.setTenancyid(obj.optString("tenancy_id"));
			ordersService = context.getBean(OrdersManagementService.NAME, OrdersManagementService.class);

			ordersService.deliveryOrders(obj.optString("tenancy_id"), obj.optInt("store_id"), obj);
		}
		catch (SystemException se)
		{
			resultJson.put("success", false);
			resultJson.put("msg", getMessage(se,"配送失败"));
			logger.error("配送失败", se);
		}
		catch (Exception e)
		{
			e.printStackTrace();
			resultJson.put("success", false);
			resultJson.put("msg", "订单配送失败!");
			logger.error("订单配送失败", e);
		}
		finally
		{
			try
			{
				out = response.getWriter();
				out.print(resultJson.toString());
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
				logger.error("订单配送失败", e);
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
	/**
	  * @Description: 订单完成
	  * @Title:completeOrders
	  * @param:@param request
	  * @param:@param response
	  * @return: void
	  * @author: shenzhanyu
	  * @version: 1.0
	 */
	@RequestMapping(value = "/completeOrders")
	public void completeOrders(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		JSONObject resultJson = JSONObject.fromObject("{'success':true,'msg':'订单完成!'}");
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}

			DBContextHolder.setTenancyid(obj.optString("tenancy_id"));
			ordersService = context.getBean(OrdersManagementService.NAME, OrdersManagementService.class);
			JSONObject para = ordersService.getPosOptState(obj.optString("tenancy_id"), obj.optInt("store_id"));
			obj.put("opt_num", para.optString("opt_num"));
			obj.put("pos_num", para.optString("pos_num"));
			obj.put("report_date", para.optString("report_date"));
			obj.put("shift_id", para.optInt("shift_id"));
			ordersService.completeOrders(obj.optString("tenancy_id"), obj.optInt("store_id"), obj);
		}
		catch (SystemException se)
		{
			resultJson.put("success", false);
			resultJson.put("msg", getMessage(se,"订单完成失败"));
			logger.error("订单完成失败", se);
		}
		catch (Exception e)
		{
			e.printStackTrace();
			resultJson.put("success", false);
			resultJson.put("msg", "订单未完成!");
			logger.error("订单完成失败", e);
		}
		finally
		{
			try
			{
				out = response.getWriter();
				out.print(resultJson.toString());
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
				logger.error("订单完成失败", e);
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
	/**
	  * @Description: 获取自动设置配置
	  * @Title:getConfig
	  * @param:@param request
	  * @param:@param response
	  * @return: void
	  * @author: shenzhanyu
	  * @version: 1.0
	 */
	@RequestMapping(value = "/getConfig")
	public void getConfig(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		JSONObject resultJson = JSONObject.fromObject("{'success':true}");
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}

			ordersService = context.getBean(OrdersManagementService.NAME, OrdersManagementService.class);

			resultJson.put("config", ordersService.getConfig());
		}
		catch (Exception e)
		{
			e.printStackTrace();
			resultJson.put("success", false);
			resultJson.put("msg", "自动取单设置失败,请重试!");
		}
		finally
		{
			try
			{
				out = response.getWriter();
				out.print(resultJson.toString());
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
				logger.error("获取参数失败!", e);
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
	/**
	  * @Description: 外卖自动取单设置
	  * @Title:setConfig
	  * @param:@param request
	  * @param:@param response
	  * @return: void
	  * @author: shenzhanyu
	  * @version: 1.0
	 */
	@RequestMapping(value = "/setConfig")
	public void setConfig(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		JSONObject resultJson = JSONObject.fromObject("{'success':true}");
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}

			ordersService = context.getBean(OrdersManagementService.NAME, OrdersManagementService.class);

			ordersService.setConfig(obj);
		}
		catch (Exception e)
		{
			e.printStackTrace();
			resultJson.put("success", false);
			resultJson.put("msg", "自动取单设置失败,请重试!");
		}
		finally
		{
			try
			{
				out = response.getWriter();
				out.print(resultJson.toString());
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
				logger.error("设置参数失败!", e);
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
	/**
	  * @Description: 外卖补打印
	  * @Title:makePrint
	  * @param:@param request
	  * @param:@param response
	  * @return: void
	  * @author: shenzhanyu
	  * @version: 1.0
	 */
	@RequestMapping(value = "/makePrint")
	public void makePrint(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		JSONObject resultJson = JSONObject.fromObject("{'success':true}");
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}
            DBContextHolder.setTenancyid(obj.optString("tenancy_id"));

			ordersService = context.getBean(OrdersManagementService.NAME, OrdersManagementService.class);
            String tenancyId=obj.optString("tenancy_id");
            int storeId=obj.optInt("store_id");
			JSONObject para = ordersService.getPosOptState(tenancyId, storeId);
			obj.put("opt_num", para.optString("opt_num"));
			obj.put("pos_num", para.optString("pos_num"));
			obj.put("report_date", para.optString("report_date"));
			obj.put("shift_id", para.optInt("shift_id"));
			ordersService.makePrintOrder(tenancyId,storeId,obj);
		}
		catch (SystemException se)
		{
			resultJson.put("success", false);
			resultJson.put("msg", getMessage(se,"订单打印失败"));
			logger.error("订单打印失败", se);
		}
		catch (Exception e)
		{
			e.printStackTrace();
			resultJson.put("success", false);
			resultJson.put("msg", "订单打印失败!");
			logger.error("订单打印失败", e);
		}
		finally
		{
			try
			{
				out = response.getWriter();
				out.print(resultJson.toString());
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
				logger.error("订单打印失败", e);
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
	private static String getMessage(SystemException se,String msg)
	{

	    if(Tools.hv(msg)){
	       return msg;
        }

		String message =null;
		try
		{
			message = se.getErrorMsg();
//			message =  PropertiesLoader.getProperty(String.valueOf(se.getErrorCode().getNumber()));
//			Map<String, Object> map = se.getProperties();
//			if(Tools.hv(map))
//			{
//				for(String key : map.keySet())
//				{
//					message = message.replace(key, String.valueOf(map.get(key)));
//				}
//			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
			message = "未知异常，请联系管理员!";
		}
		return message;
	}
	
	/**
	  * @Description: 订单详情中获取退款信息
	  * @Title:getRefundOrders
	  * @param:@param request
	  * @param:@param response
	  * @return: void
	  * @author: shenzhanyu
	  * @version: 1.0
	  * @Date: 2018年8月30日
	 */
	@RequestMapping(value = "/getRefundOrders")
	public void getRefundOrders(HttpServletRequest request, HttpServletResponse response) {
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		JSONObject resultJson = JSONObject.fromObject("{'success':true}");
		try {
			JSONObject obj = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet()) {
				obj.put(key, map.get(key)[0]);
			}

			ordersService = context.getBean(OrdersManagementService.NAME, OrdersManagementService.class);
			String tenancyId = obj.optString("tenancy_id");
			int storeId = obj.optInt("store_id");
			resultJson.put("refundOrders", ordersService.getRefundOrders(tenancyId, storeId, obj));
		} catch (Exception e) {
			e.printStackTrace();
			resultJson.put("success", false);
		} finally {
			try {
				out = response.getWriter();
				out.print(resultJson.toString());
				out.flush();
				out.close();
			} catch (Exception e) {
				logger.error("获取参数失败!", e);
			} finally {
				if (out != null)
					out.close();
			}
		}
	}
	
	
	/**
	  * @Description: boh操作退款信息--同意退款
	  * @Title:getRefundOrders
	  * @param:@param request
	  * @param:@param response
	  * @return: void
	  * @author: shenzhanyu
	  * @version: 1.0
	  * @Date: 2018年6月27日
	 */
	@RequestMapping(value = "/agreeRefundOrders")
	public void agreeRefundOrders(HttpServletRequest request, HttpServletResponse response) {
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		JSONObject resultJson = JSONObject.fromObject("{'success':true}");
		try {
			JSONObject obj = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet()) {
				obj.put(key, map.get(key)[0]);
			}

			ordersService = context.getBean(OrdersManagementService.NAME, OrdersManagementService.class);
			String tenancyId = obj.optString("tenancy_id");
			int storeId = obj.optInt("store_id");
			resultJson.put("refundOrders", ordersService.agreeRefundOrders(tenancyId, storeId, obj));
		} catch (Exception e) {
			e.printStackTrace();
			resultJson.put("success", false);
			resultJson.put("msg", "退款失败，请检查网络或者通过平台客户端操作");
		} finally {
			try {
				out = response.getWriter();
				out.print(resultJson.toString());
				out.flush();
				out.close();
			} catch (Exception e) {
				logger.error("获取参数失败!", e);
			} finally {
				if (out != null)
					out.close();
			}
		}
	}
	
	
	/**
	  * boh处理退款信息--拒绝退款
	  * @Title:getRefundOrders
	  * @param:@param request
	  * @param:@param response
	  * @return: void
	  * @author: shenzhanyu
	  * @version: 1.0
	  * @Date: 2018年6月27日
	 */
	@RequestMapping(value = "/disagreeRefundOrders")
	public void disagreeRefundOrders(HttpServletRequest request, HttpServletResponse response) {
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		JSONObject resultJson = JSONObject.fromObject("{'success':true}");
		try {
			JSONObject obj = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet()) {
				obj.put(key, map.get(key)[0]);
			}

			ordersService = context.getBean(OrdersManagementService.NAME, OrdersManagementService.class);
			String tenancyId = obj.optString("tenancy_id");
			int storeId = obj.optInt("store_id");
			ordersService.disagreeRefundOrders(tenancyId, storeId, obj);
		} catch (Exception e) {
			e.printStackTrace();
			resultJson.put("success", false);
			resultJson.put("msg", "拒绝失败，请检查网络或者通过平台客户端操作");
		} finally {
			try {
				out = response.getWriter();
				out.print(resultJson.toString());
				out.flush();
				out.close();
			} catch (Exception e) {
				logger.error("获取参数失败!", e);
			} finally {
				if (out != null)
					out.close();
			}
		}
	}
	
	/**
	  * @Description: 获取申请退款订单个数
	  * @Title:getNumOfRefundOrders
	  * @param:@param request
	  * @param:@param response
	  * @return: void
	  * @author: shenzhanyu
	  * @version: 1.0
	 */
	@RequestMapping(value = "/getNumOfRefundOrders")
	public void getNumOfRefundOrders(HttpServletRequest request, HttpServletResponse response){
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		JSONObject resultJson = JSONObject.fromObject("{'success':true}");
		try {
			JSONObject obj = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet()) {
				obj.put(key, map.get(key)[0]);
			}

			ordersService = context.getBean(OrdersManagementService.NAME, OrdersManagementService.class);
			String tenancyId = obj.optString("tenancy_id");
			int storeId = obj.optInt("store_id");
			List<JSONObject> orders = ordersService.getNumOfRefundOrders(tenancyId, storeId);
			resultJson.put("orders", orders);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				out = response.getWriter();
				out.print(resultJson.toString());
				out.flush();
				out.close();
			} catch (Exception e) {
				logger.error("获取参数失败!", e);
			} finally {
				if (out != null)
					out.close();
			}
		}
	}
	
	/**
	  * @Description: 查询订单未映射的菜品
	  * @Title:getNotMappedDishes
	  * @param:@param request
	  * @param:@param response
	  * @return: void
	  * @author: shenzhanyu
	  * @version: 1.0
	 */
	@RequestMapping(value = "/getNotMappedDishes")
	public void getNotMappedDishes(HttpServletRequest request, HttpServletResponse response) {
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		JSONObject resultJson = new JSONObject();
		try {
			Map<String, String[]> map = request.getParameterMap();

			JSONObject obj = JSONObject.fromObject("{}");

			for (String key : map.keySet()) {
				obj.put(key, map.get(key)[0]);
			}

			DBContextHolder.setTenancyid(obj.optString("tenancy_id"));
			ordersService = context.getBean(OrdersManagementService.NAME, OrdersManagementService.class);

			resultJson = ordersService.getNotMappedDishes(obj.optString("tenancy_id"), obj.optInt("store_id"),obj);
		} catch (Exception e) {
			e.printStackTrace();
			resultJson.put("success", false);
			resultJson.put("msg", "查询异常订单缺失菜品失败!");
			logger.error("查询异常订单缺失菜品失败", e);
		} finally {
			try {
				out = response.getWriter();
				out.print(resultJson.toString());
				out.flush();
				out.close();
			} catch (Exception e) {
				logger.error("查询订单失败", e);
			} finally {
				if (out != null)
					out.close();
			}
		}
	}
	
	
	/**
	 * 获取本地菜品类别
	 * 
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/getLocalItemCate")
	public void getLocalItemCate(HttpServletRequest request, HttpServletResponse response)
	{
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		JSONObject resultJson = new JSONObject();
		try
		{
			JSONObject obj = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet())
			{
				obj.put(key, map.get(key)[0]);
			}

			DBContextHolder.setTenancyid(obj.optString("tenancy_id"));
			ordersService = context.getBean(OrdersManagementService.NAME, OrdersManagementService.class);

			resultJson = ordersService.getLocalItemCate(obj.optString("tenancy_id"), obj.optInt("store_id"), obj);
		}
		catch (SystemException se)
		{
			resultJson.put("success", false);
			resultJson.put("msg", getMessage(se,"查询本地菜品类别失败"));
			logger.error("查询本地菜品类别失败", se);
		}
		catch (Exception e)
		{
			e.printStackTrace();
			resultJson.put("success", false);
			resultJson.put("msg", "查询本地菜品类别失败!");
			logger.error("查询本地菜品类别失败", e);
		}
		finally
		{
			try
			{
				out = response.getWriter();
				out.print(resultJson.toString());
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
				logger.error("查询本地菜品类别失败", e);
			}
			finally
			{
				if (out != null) out.close();
			}
		}
	}
	
	/**
	  * @Description: 查询localserver菜品
	  * @Title:getLocalItems
	  * @param:@param request
	  * @param:@param response
	  * @return: void
	  * @author: shenzhanyu
	  * @version: 1.0
	 */
	@RequestMapping(value = "/getLocalItems")
	public void getLocalItems(HttpServletRequest request, HttpServletResponse response) {
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		JSONObject resultJson = new JSONObject();
		try {
			Map<String, String[]> map = request.getParameterMap();

			JSONObject obj = JSONObject.fromObject("{}");

			for (String key : map.keySet()) {
				obj.put(key, map.get(key)[0]);
			}

			DBContextHolder.setTenancyid(obj.optString("tenancy_id"));
			ordersService = context.getBean(OrdersManagementService.NAME, OrdersManagementService.class);

			resultJson = ordersService.getLocalItems(obj.optString("tenancy_id"), obj.optInt("store_id"),obj);
		} catch (Exception e) {
			e.printStackTrace();
			resultJson.put("success", false);
			resultJson.put("msg", "查询本地菜品失败!");
			logger.error("查询本地菜品失败", e);
		} finally {
			try {
				out = response.getWriter();
				out.print(resultJson.toString());
				out.flush();
				out.close();
			} catch (Exception e) {
				logger.error("查询订单失败", e);
			} finally {
				if (out != null)
					out.close();
			}
		}
	}
	
	/**
	  * @Description: 修复菜品 异常菜品和本地菜品绑定
	  * @Title:bindingItem
	  * @param:@param request
	  * @param:@param response
	  * @return: void
	  * @author: shenzhanyu
	  * @version: 1.0
	  * @Date: 2018年7月22日
	 */
	
	@RequestMapping(value = "/bindingItem")
	public void bindingItem(HttpServletRequest request, HttpServletResponse response) {
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		JSONObject resultJson = JSONObject.fromObject("{'success':true}");
		try {
			Map<String, String[]> map = request.getParameterMap();

			JSONObject obj = JSONObject.fromObject("{}");

			for (String key : map.keySet()) {
				obj.put(key, map.get(key)[0]);
			}

			DBContextHolder.setTenancyid(obj.optString("tenancy_id"));
			ordersService = context.getBean(OrdersManagementService.NAME, OrdersManagementService.class);
			
			JSONObject bindingItem = ordersService.validateBindingItem(obj.optString("tenancy_id"), obj.optInt("store_id"),obj);
			if(bindingItem!=null){
				resultJson.put("success", false);
				resultJson.put("msg", "该异常菜品已经存在绑定");
			}else{
				ordersService.bindingItem(obj.optString("tenancy_id"), obj.optInt("store_id"),obj);
			}
		} catch (Exception e) {
			e.printStackTrace();
			resultJson.put("success", false);
			resultJson.put("msg", "菜品绑定失败");
			logger.error("菜品绑定失败", e);
		} finally {
			try {
				out = response.getWriter();
				out.print(resultJson.toString());
				out.flush();
				out.close();
			} catch (Exception e) {
				logger.error("菜品绑定失败", e);
			} finally {
				if (out != null)
					out.close();
			}
		}
	}
	
	/**
	  * @Description: 解除菜品绑定
	  * @Title:removeBindingItem
	  * @param:@param request
	  * @param:@param response
	  * @return: void
	  * @author: shenzhanyu
	  * @version: 1.0
	  * @Date: 2018年7月22日
	 */
	@RequestMapping(value = "/removeBindingItem")
	public void removeBindingItem(HttpServletRequest request, HttpServletResponse response) {
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		JSONObject resultJson = JSONObject.fromObject("{'success':true}");
		try {
			Map<String, String[]> map = request.getParameterMap();

			JSONObject obj = JSONObject.fromObject("{}");

			for (String key : map.keySet()) {
				obj.put(key, map.get(key)[0]);
			}

			DBContextHolder.setTenancyid(obj.optString("tenancy_id"));
			ordersService = context.getBean(OrdersManagementService.NAME, OrdersManagementService.class);
			JSONObject bindingItem = ordersService.validateBindingItem(obj.optString("tenancy_id"), obj.optInt("store_id"),obj);
			if(bindingItem==null){
				resultJson.put("success", false);
				resultJson.put("msg", "该异常菜品不存在绑定菜品");
			}else{
				ordersService.removeBindingItem(obj.optString("tenancy_id"), obj.optInt("store_id"),obj);
			}
		} catch (Exception e) {
			e.printStackTrace();
			resultJson.put("success", false);
			resultJson.put("msg", "解除菜品绑定失败");
			logger.error("解除菜品绑定失败", e);
		} finally {
			try {
				out = response.getWriter();
				out.print(resultJson.toString());
				out.flush();
				out.close();
			} catch (Exception e) {
				logger.error("解除菜品绑定失败", e);
			} finally {
				if (out != null)
					out.close();
			}
		}
	}
	
	/**
	  * @Description: 修复异常菜品数据
	  * @Title:repairItemInfo
	  * @param:@param request
	  * @param:@param response
	  * @return: void
	  * @author: shenzhanyu
	  * @version: 1.0
	  * @Date: 2018年7月22日
	 */
	@RequestMapping(value = "/repairItemInfo")
	public void repairItemInfo(HttpServletRequest request, HttpServletResponse response) {
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		JSONObject resultJson = JSONObject.fromObject("{'success':true}");
		try {
			Map<String, String[]> map = request.getParameterMap();

			JSONObject obj = JSONObject.fromObject("{}");

			for (String key : map.keySet()) {
				obj.put(key, map.get(key)[0]);
			}

			DBContextHolder.setTenancyid(obj.optString("tenancy_id"));
			ordersService = context.getBean(OrdersManagementService.NAME, OrdersManagementService.class);
			List<JSONObject> bindingItmes = ordersService.getBindingItems(obj.optString("tenancy_id"), obj.optInt("store_id"));
			if(bindingItmes!=null){
				ordersService.repairItemInfo(obj.optString("tenancy_id"), obj.optInt("store_id"),obj);
			}else{
				resultJson.put("success", false);
				resultJson.put("msg", "绑定关系不存在，请先对异常菜品进行绑定");
			}
		} catch (Exception e) {
			e.printStackTrace();
			resultJson.put("success", false);
			resultJson.put("msg", "修复异常菜品失败");
			logger.error("修复异常菜品失败", e);
		} finally {
			try {
				out = response.getWriter();
				out.print(resultJson.toString());
				out.flush();
				out.close();
			} catch (Exception e) {
				logger.error("修复异常菜品失败", e);
			} finally {
				if (out != null)
					out.close();
			}
		}
	}

	/**
	 * 获取当前报表日志各渠道订单个数
	 * @param request
	 * @param response
	 */
/*	@RequestMapping(value = "/getNumOfOrdersByChanel")
	public void getNumOfOrdersByChanel(HttpServletRequest request, HttpServletResponse response){
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		JSONObject resultJson = JSONObject.fromObject("{'success':true}");
		try {
			JSONObject obj = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet()) {
				obj.put(key, map.get(key)[0]);
			}

			ordersService = context.getBean(OrdersManagementService.NAME, OrdersManagementService.class);
			String tenancyId = obj.optString("tenancy_id");
			int storeId = obj.optInt("store_id");

			List<JSONObject> orders = ordersService.getNumOfOrdersByChanel(tenancyId, storeId,obj);
			resultJson.put("orders", orders);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				out = response.getWriter();
				out.print(resultJson.toString());
				out.flush();
				out.close();
			} catch (Exception e) {
				logger.error("获取参数失败!", e);
			} finally {
				if (out != null)
					out.close();
			}
		}
	}*/
	
	
	/**
	 * 获取当前报表日期各状态订单个数
	 * @param request
	 * @param response
	 */
/*	@RequestMapping(value = "/getNumOfOrdersByChanel")
	public void getNumOfOrdersByState(HttpServletRequest request, HttpServletResponse response){
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		JSONObject resultJson = JSONObject.fromObject("{'success':true}");
		try {
			JSONObject obj = JSONObject.fromObject("{}");

			Map<String, String[]> map = request.getParameterMap();

			for (String key : map.keySet()) {
				obj.put(key, map.get(key)[0]);
			}

			ordersService = context.getBean(OrdersManagementService.NAME, OrdersManagementService.class);
			String tenancyId = obj.optString("tenancy_id");
			int storeId = obj.optInt("store_id");

			List<JSONObject> orders = ordersService.getNumOfOrdersByChanel(tenancyId, storeId,obj);
			resultJson.put("orders", orders);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			try {
				out = response.getWriter();
				out.print(resultJson.toString());
				out.flush();
				out.close();
			} catch (Exception e) {
				logger.error("获取参数失败!", e);
			} finally {
				if (out != null)
					out.close();
			}
		}
	}*/
	
	/**
	  * @Description: 配送异常 添加小费
	  * @Title:addTips
	  * @param:@param request
	  * @param:@param requestParams
	  * @param:@return
	  * @return: JSONObject
	  * @author: shenzhanyu
	  * @version: 1.0
	 */
	@RequestMapping(value = "/addTips",method = RequestMethod.POST)
	@ResponseBody
	public JSONObject addTips(HttpServletRequest request,  @RequestBody JSONObject requestParams)
	{
		try {
			DBContextHolder.setTenancyid(requestParams.optString("tanancyId"));

			String tenancyId=requestParams.optString("tanancyId");
			int storeId=requestParams.optInt("shopId");
			JSONObject resultJo = orderDeliveryService.addTips(tenancyId,requestParams);
			return resultJo;
		} catch (Exception e) {
			e.printStackTrace();
			JSONObject exceptionJo = new JSONObject();
			exceptionJo.put("error", 1);
			exceptionJo.put("msg", "添加小费错误");
			return exceptionJo;
			
		}

	}
	
	/**
	  * @Description: 配送异常-更改配送
	  * @Title:changeDistribution
	  * @param:@param request
	  * @param:@param requestParams
	  * @param:@return
	  * @return: JSONObject
	  * @author: shenzhanyu
	  * @version: 1.0
	 */
	@RequestMapping(value = "/changeDistribution",method = RequestMethod.POST)
	@ResponseBody
	public JSONObject changeDistribution(HttpServletRequest request, @RequestBody JSONObject requestParams)
	{
		try {
			  DBContextHolder.setTenancyid(requestParams.optString("tanancyId"));		
			  Data param  = new Data();
		      Data result = new Data();
		      List<JSONObject> listJson = new ArrayList<>();
		      listJson.add(requestParams);
		      param.setData(listJson);
		      orderDeliveryService.addOrderDelivery(param, result);
		      JSONObject resultJo = new JSONObject();
		      if(result.getData()!=null&&result.getData().size()>0){
		    	  resultJo = JSONObject.fromObject(result.getData().get(0)); 
		      }else{
		    	  resultJo.put("error", 701);
		    	  resultJo.put("msg", "网络请求超时");
		      }			  
		      return resultJo;  
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("更改自配送添加订单错误"+ExceptionMessage.getExceptionMessage(e));
			JSONObject exceptionJo = new JSONObject();
			exceptionJo.put("error", 702);
			exceptionJo.put("msg", "请联系内部管理员");
			return exceptionJo;
		}
	  		
	}
	
	
	@RequestMapping(value = "/distributionChannel",method = RequestMethod.POST)
	@ResponseBody
	public JSONObject distributionChannel(HttpServletRequest request, @RequestBody JSONObject requestParams)
	{
		try {
			DBContextHolder.setTenancyid(requestParams.optString("tanancyId"));
			String tenancyId=requestParams.optString("tanancyId");
			JSONObject resultJo = orderDeliveryService.distributionChannel(tenancyId, requestParams);
			return resultJo;
		} catch (Exception e) {
			e.printStackTrace();
			JSONObject exceptionJo = new JSONObject();
			exceptionJo.put("error", 1);
			exceptionJo.put("msg", "查询自配送渠道错误");
			return exceptionJo;
		}
		
	}
	
	@RequestMapping(value = "/queryDeliveryInfo",method = RequestMethod.POST)
	@ResponseBody
	public JSONObject queryDeliveryInfo(HttpServletRequest request, @RequestBody JSONObject requestParams)
	{
		try {
			DBContextHolder.setTenancyid(requestParams.optString("tanancyId"));
			Data param  = new Data();
            Data result = new Data();
            List<JSONObject> listJson = new ArrayList<>();
            listJson.add(requestParams);
            param.setData(listJson);
            orderDeliveryService.queryOrderDelivery(param, result);
		    JSONObject resultJo = new JSONObject();
            if(result.getData()!=null&&result.getData().size()>0){
		    	  resultJo = JSONObject.fromObject(result.getData().get(0)); 
		      }else{
		    	  resultJo.put("error", 701);
		    	  resultJo.put("msg", "网络请求超时");
		      }			
            return resultJo;
		} catch (Exception e) {
			e.printStackTrace();
			JSONObject exceptionJo = new JSONObject();
			exceptionJo.put("error", 702);
			exceptionJo.put("msg", "请联系内部管理员");
			return exceptionJo;
		}
	}



	@RequestMapping(value = "/removeRedundantData")
	public void removeRedundantData(HttpServletRequest request, HttpServletResponse response) {
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
		PrintWriter out = null;
		JSONObject resultJson = JSONObject.fromObject("{'success':true}");
		Map<String, String> systemMap = com.tzx.framework.common.constant.Constant.getSystemMap();
		String tenentId = null;
		Integer storeId = null;
		try {
			tenentId = systemMap.get("tenent_id");
			storeId = Integer.parseInt(systemMap.get("store_id"));

			Map<String, String[]> map = request.getParameterMap();
			JSONObject obj = JSONObject.fromObject("{}");
			for (String key : map.keySet()) {
				obj.put(key, map.get(key)[0]);
			}

			DBContextHolder.setTenancyid(tenentId);
			ordersService = context.getBean(OrdersManagementService.NAME, OrdersManagementService.class);
			ordersService.removeRedundantData(tenentId, storeId,obj);
		} catch (Exception e) {
			e.printStackTrace();
			resultJson.put("success", false);
			resultJson.put("msg", "清除外卖数据失败");
			logger.error("清除外卖数据失败", e);
		} finally {
			try {
				out = response.getWriter();
				out.print(resultJson.toString());
				out.flush();
				out.close();
			} catch (Exception e) {
				logger.error("清除外卖数据失败", e);
			} finally {
				if (out != null)
					out.close();
			}
		}
	}
}
