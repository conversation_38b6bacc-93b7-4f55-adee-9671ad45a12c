package com.tzx.clientorder.wlifeprogram.bo.impl;

import com.alibaba.fastjson.JSON;
import com.tzx.base.bo.PosCodeService;
import com.tzx.base.common.util.IdempotentUtil;
import com.tzx.base.common.util.StringUtil;
import com.tzx.base.entity.PosBillPayment;
import com.tzx.clientorder.acewillwechat.po.springjdbc.dao.AcewillInsertOrderDao;
import com.tzx.clientorder.acewillwechat.po.springjdbc.dao.AcewillPaySucessDao;
import com.tzx.clientorder.common.entity.*;
import com.tzx.clientorder.wlifeprogram.bo.FirstPaymentCalAmountService;
import com.tzx.clientorder.wlifeprogram.bo.FirstPaymentOrderService;
import com.tzx.clientorder.wlifeprogram.bo.FirstPaymentService;
import com.tzx.clientorder.wlifeprogram.common.entity.*;
import com.tzx.clientorder.wlifeprogram.dao.FirstPaymentDao;
import com.tzx.framework.common.constant.Code;
import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.exception.OtherSystemException;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.DoubleHelper;
import com.tzx.framework.common.util.Tools;
import com.tzx.framework.common.util.UUIDUtil;
import com.tzx.member.acewill.bo.AcewillCustomerService;
import com.tzx.member.common.entity.CrmCardTradingListEntity;
import com.tzx.member.crm.bo.CustomerCardConsumeService;
import com.tzx.pos.asyn.pool.ThreadPool;
import com.tzx.pos.asyn.thread.OrderingPrintThread;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.constant.WLifeConstant;
import com.tzx.pos.base.util.CacheTableUtil;
import com.tzx.pos.base.util.JsonUtil;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.bo.*;
import com.tzx.pos.bo.payment.PosPaymentService;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import net.sf.json.JsonConfig;
import net.sf.json.util.PropertyFilter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.beans.PropertyDescriptor;
import java.sql.Timestamp;
import java.util.*;

import static com.tzx.pos.base.constant.SysDictionary.PAYMENT_CLASS_DOUYIN_COUPONS_PHANTOM_PAY;

/**
 * Created by zds on 2018-08-14.
 */
@Service(FirstPaymentService.NAME)
public class FirstPaymentServiceImpl implements FirstPaymentService {
    private static final Logger LOG = LoggerFactory.getLogger(FirstPaymentServiceImpl.class);


    private static final String MSG = "msg";
    private static final String DATA = "data";

    private static final String SET_MEAL_TYPE = "2";


    @Resource(name = PosBaseService.NAME)
    private PosBaseService posBaseService;

    @Resource(name = PosDishService.NAME)
    private PosDishService posDishService;

    @Resource(name = PosPaymentService.NAME)
    private PosPaymentService posPaymentService;

    @Resource(name = AcewillInsertOrderDao.NAME)
    AcewillInsertOrderDao insertOrderDao;

    @Resource(name = PosDiscountService.NAME)
    private PosDiscountService posDiscountService;

    @Resource(name = AcewillCustomerService.NAME)
    private AcewillCustomerService acewillCustomerService;

    @Resource(name = AcewillPaySucessDao.NAME)
    private AcewillPaySucessDao paySucessDao;
    @Resource(name = PosPrintNewService.NAME)
    private PosPrintNewService posPrintNewService;

    @Resource(name = PosPrintService.NAME)
    private PosPrintService posPrintService;

    @Resource(name = CustomerCardConsumeService.NAME)
    private CustomerCardConsumeService customerService;
    @Resource(name = FirstPaymentDao.NAME)
    private FirstPaymentDao firstPaymentDao;
    @Resource
    private FirstPaymentOrderService firstPaymentOrderService;
    @Resource(name = PosOpenTableService.NAME)
    private PosOpenTableService	openTableService;
    @Resource(name = PosCodeService.NAME)
    PosCodeService	codeService;
    @Resource(name =FirstPaymentCalAmountService.NAME )
    FirstPaymentCalAmountService firstPaymentCalAmountService;

    public static List<String> WLIFE_THIRD_COUPONS=Arrays.asList(WLifeConstant.PAY_SOURCE_MEITUAN,
            WLifeConstant.PAY_SOURCE_DOUYIN,
            WLifeConstant.PAY_SOURCE_ALIPAYCOUPON,
            WLifeConstant.PAY_SOURCE_LITTLE_RED_BOOK
            );

    private static final Map<String,JSONObject> paymentJzidMap=new HashMap<>();

    @Override
    public JSONObject orderPrecheck(JSONObject param, JSONObject responseJson) throws Exception{

        String tenancyId = param.optString("business_id");
        int storeId = param.optInt("shop_id");
        JSONObject order = param.getJSONObject("order_info"); // 订单信息
        String out_order_id = param.optString("out_order_id");

        if(IdempotentUtil.isDuplicate(out_order_id,AfterPaymentServiceImpl.class)){
            LOG.info(out_order_id+"=============重复请求，不处理...");
            responseJson.put("success",0);
            responseJson.put(MSG,"该订单已处理");
            return  responseJson;
        }

        if (StringUtils.isEmpty(tenancyId) || storeId == 0
                || order.isEmpty()) {
            throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
        }

        Object setmeal = order.get("setmeal"); // 菜品明细
        Object normalitems = order.get("normalitems"); // 菜品明细

        JSONArray dishArray = JSONArray.fromObject(setmeal);
        JSONArray normalArray = JSONArray.fromObject(normalitems);
        if (dishArray.isEmpty() && normalArray.isEmpty()){
            throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
        }
        MicroLifeOrder microLifeOrder = (MicroLifeOrder) JSONObject.toBean(param,getJsonConfig());
        microLifeOrder.setShop_id(storeId);
        microLifeOrder.setBusiness_id(tenancyId);
        microLifeOrder.setBrand_id(null);

        AcewillOrderAssist orderAssist = createOrderAssist(microLifeOrder);
        //开台
        String billNum = openTable(microLifeOrder,true);
        orderAssist.setBill_num(billNum);
        // 拼装下单所需参数

        Data orderParam = createOrderDishData(microLifeOrder, orderAssist);

//        Data newestOrderDish = posDishService.newestOrderDish(orderParam, new JSONObject());
        Data newestOrderDish  = firstPaymentOrderService.saveTempOrder(orderParam, new JSONObject());

        if (newestOrderDish.getCode() == 0) {
            // 会员信息
            String sysParameter = CacheTableUtil.getSysParameter("CustomerType");
            if (SysDictionary.CUSTOMER_TYPE_ACEWILL.equals(sysParameter)) {
                JSONObject member = order.getJSONObject("member");
                if(null != member&&!member.isEmpty()){
                savePosBillMember(microLifeOrder, orderAssist);
                //处理折扣
                //查询账单pos_bill，判断账单有没折扣
                JSONObject billObject = firstPaymentDao.getBillDiscount(tenancyId, storeId, billNum);
                if(null == billObject ){
                    LOG.error("进行折扣的门店为{}，账单号为:{}的账单不存在", storeId,billNum);
                }
                    setMemberDiscount(member, tenancyId,microLifeOrder.getShop_id(), billNum, billObject,true);
                }
            }


            JSONObject orderData = firstPaymentOrderService.getOrderDetail(microLifeOrder.getBusiness_id(), orderAssist.getBill_num());
            LOG.info(" firstPayment getOrderDetail data:" + orderData.toString());

            buildSuccessData(orderData, responseJson, out_order_id,true);
        } else {
            throw new OtherSystemException(newestOrderDish.getCode(), newestOrderDish.getMsg(), null);
        }

        return responseJson;
    }

    @Override
    public JSONObject firstPayClose(JSONObject param, JSONObject responseJson) throws Exception{
        String tenancyId = param.optString("business_id");
        int storeId = param.optInt("shop_id");
        String outOrderId = param.optString("out_order_id");
        JSONObject order = param.getJSONObject("order_info"); // 订单信息
        JSONObject payInfo = param.getJSONObject("pay_info"); // 支付信息
        String mobil = param.optString("mobile"); // 支付信息
        
        if(StringUtils.isEmpty(tenancyId) || storeId == 0 || !StringUtil.hasText(outOrderId)){
            throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
        }
        if (order.isEmpty() || payInfo.isEmpty()) {
            throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
        }
        
        MicroLifeOrder microLifeOrder = new MicroLifeOrder();
        microLifeOrder.setShop_id(storeId);
        microLifeOrder.setBusiness_id(tenancyId);

        MicroLifeOrderInfo microLifeOrderInfo = (MicroLifeOrderInfo) JSONObject.toBean(order,getOrderInfoJsonConfig());
        MicroLifePayOrder microLifePayOrder = (MicroLifePayOrder) JSONObject.toBean(payInfo, getPayJsonConfig());
        microLifePayOrder.setBusiness_id(tenancyId);
        microLifePayOrder.setShop_id(storeId);
        MicroLifePayOrderData payOrderData = microLifePayOrder.getData();

        microLifeOrder.setOrder_info(microLifeOrderInfo);
        microLifeOrder.setOut_order_id(outOrderId);

        AcewillPayWLife wlife = microLifePayOrder.getWlife();
        if(null==wlife){
           wlife=new AcewillPayWLife();
        }
        wlife.setMobil(mobil);
        wlife.setBalance(null!=wlife.getBalance()?DoubleHelper.div(wlife.getBalance(), 100d, DEFAULT_SCALE):0d);
        if(StringUtils.isEmpty(wlife.getCno())&&microLifeOrderInfo.getMember()!=null){
            wlife.setCno(microLifeOrderInfo.getMember().getCno());
        }

        boolean isWlifeCrmPay=StringUtils.isNotEmpty(wlife.getCno());
        
        //获取消费的储值和积分
        Double consumeAmount = 0.0;
        Double consumeCredit = 0.0;
        for (AcewillPayInfo acewillPayInfo : microLifePayOrder.getPay_info())
		{
			if (WLifeConstant.PAY_SOURCE_CREDIT.equals(acewillPayInfo.getSource()))
			{
				consumeCredit = acewillPayInfo.getAmount();
			}
			else if (WLifeConstant.PAY_SOURCE_BALANCE.equals(acewillPayInfo.getSource()))
			{
				consumeAmount = acewillPayInfo.getAmount();
			}
		}
        
        //validate
        AcewillPosBill posBill =null;
        String oid = firstPaymentDao.getBillNumByOrderId(outOrderId);
        if(null != oid){
            payOrderData.setOid(oid);
            JSONObject posBillObj = paySucessDao.getPosBillByBillNum(tenancyId, payOrderData.getOid());
            if (posBillObj == null) {
                // 账单不存在
                LOG.error("账单号为:{}的账单不存在", payOrderData.getOid());
                throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_BILL);
            }
             posBill = (AcewillPosBill) JSONObject.toBean(posBillObj, AcewillPosBill.class);
            // 判断账单状态
            String billProperty = posBill.getBill_property();
            if (!SysDictionary.BILL_PROPERTY_OPEN.equals(billProperty)) {
                LOG.error("账单号为:{}的账单已关闭", payOrderData.getOid());
                throw SystemException.getInstance("账单已关闭", PosErrorCode.BILL_CLOSED);
            }
            String paymentState = posBill.getPayment_state();
            if (!SysDictionary.PAYMENT_STATE_NOTPAY.equals(paymentState)) {
                LOG.error("账单号为:{}的账单处于支付中", payOrderData.getOid());
                throw SystemException.getInstance("账单支付中", PosErrorCode.BILL_PAYMENT_STATE_PAYING_ERROR);
            }
        }
        
        //开台
        AcewillOrderAssist orderAssist = createOrderAssist(microLifeOrder);
        String billNum = openTable(microLifeOrder,false);
        orderAssist.setBill_num(billNum);
        //下单
        JSONObject orderPrintObj = new JSONObject();
        Data orderParam = createOrderDishData(microLifeOrder, orderAssist);
        Data newestOrderDish = posDishService.newestOrderDish(orderParam,orderPrintObj);
        if (newestOrderDish.getCode() == 0) {
            //打印
//            printOrderInfo(tenancyId,storeId,orderPrintObj);
            // 会员信息
            String sysParameter = CacheTableUtil.getSysParameter("CustomerType");
            if (SysDictionary.CUSTOMER_TYPE_ACEWILL.equals(sysParameter)) {

                JSONObject mem = order.getJSONObject("member");
                if(null != mem&&!mem.isEmpty()) {

                    setOrderMemberInfo(microLifeOrder.getOrder_info(), order);
                    savePosBillMember(microLifeOrder, orderAssist);

                    JSONObject billObject = firstPaymentDao.getBillDiscountForPay(tenancyId, storeId, billNum);
                    if (null == billObject) {
                        LOG.error("进行折扣的门店为{}，账单号为:{}的账单不存在", storeId, billNum);
                    }
                    if (isWlifeCrmPay) {
//                    JSONObject member = new JSONObject();
//                    member.put("cno",microLifePayOrder.getWlife().getCno());
                        JSONObject member = JSONObject.fromObject(wlife);
                        member.put("balance", DoubleHelper.add(wlife.getBalance(), consumeAmount, DEFAULT_SCALE));
                        member.put("credit", DoubleHelper.add(NumberUtils.toDouble(String.valueOf(wlife.getCredit()),NumberUtils.DOUBLE_ZERO), consumeCredit, DEFAULT_SCALE));
                        setMemberDiscount(member, tenancyId, microLifeOrder.getShop_id(), billNum, billObject, false);
                    }
                }
            }
            //更新posbill
            firstPaymentDao.updatePosBillSource(orderAssist.getBill_num(),SysDictionary.CHANEL_WSP17,true);

        } else {
            throw new OtherSystemException(newestOrderDish.getCode(), newestOrderDish.getMsg(), null);
        }

        //结账
        if(null == posBill){
            LOG.info(" outOrderId="+outOrderId+" is new order");
            payOrderData.setOid(billNum);
            JSONObject posBillObj = paySucessDao.getPosBillByBillNum(tenancyId, payOrderData.getOid());
            if (posBillObj == null) {
                // 账单不存在
                LOG.error("账单号为:{}的账单不存在", payOrderData.getOid());
                throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_BILL);
            }
            posBill = (AcewillPosBill) JSONObject.toBean(posBillObj, AcewillPosBill.class);
        }
        List<JSONObject> optStateInfoList = posPaymentService.getOptStateInfo(tenancyId, storeId,
                posBill.getReport_date());
        if (optStateInfoList.size() > 0) {
            JSONObject optStateInfo = optStateInfoList.get(0);
            posBill.setPos_num(optStateInfo.getString("pos_num"));
            posBill.setCashier_num(optStateInfo.getString("opt_num"));
            posBill.setShift_id(optStateInfo.optInt("shift_id",0));
        }else 
        {
        	//没有找到签到,取开台人和开台机台
        	posBill.setPos_num(posBill.getOpen_pos_num());
            posBill.setCashier_num(posBill.getOpen_opt());
        }

        // 判断账单金额
        Double paymentAmount = posBill.getPayment_amount();
        payOrderData.setAmount(paymentAmount);

        Map<String, AcewillPaymentWay> paymentWayMap = getPaymentWayMap(tenancyId, storeId, microLifePayOrder);
        List<AcewillPosBillPayment> posBillPaymentList = new ArrayList<>();
        List<AcewillPosBillMember> posBillMemberList = new ArrayList<>();
        List<CrmCardTradingListEntity> crmCardTradingListEntityList = new ArrayList<>();
        List<AcewillPosBillPaymentCoupons> posBillPaymentCouponsList = new ArrayList<>();
        Double sumPaymentAmount = 0d;
        boolean isInclueBalance = false;

//        dealCreditCash(microLifePayOrder.getPay_info());
        
        for (AcewillPayInfo acewillPayInfo : microLifePayOrder.getPay_info()) {

            String paymentId= UUIDUtil.generateGUID();
            sumPaymentAmount = DoubleHelper.add(sumPaymentAmount, acewillPayInfo.getAmount(), DEFAULT_SCALE);
            // 支付来源：微生活 不生成PosBillPayment
            if (!WLifeConstant.PAY_SOURCE_WLIFE.equals(acewillPayInfo.getSource())) {
                AcewillPaymentWay paymentWay = getPaymentWay(paymentWayMap, acewillPayInfo);
                if (paymentWay != null) {
                    if(WLifeConstant.PAY_SOURCE_WEIXIN.equals(acewillPayInfo.getSource())){
                        Double amount = acewillPayInfo.getAmount();
                        if(null !=amount && 0==amount){
                            continue;
                        }
                    }
                    AcewillPosBillPayment createPosBillPayment = createPosBillPayment(tenancyId,storeId,wlife, posBill, paymentWay,
                            acewillPayInfo);
                    createPosBillPayment.setPayment_uid(paymentId);
                    posBillPaymentList.add(createPosBillPayment);
                }else {
                    throw SystemException.getInstance(PosErrorCode.NOT_EXIST_JZID);
                }
            }
            // 如果有微生活会员信息
            if (microLifePayOrder.getWlife() != null) {
                // 判断微生活会员是否启用
                String sysParameter = CacheTableUtil.getSysParameter("CustomerType");
                if (sysParameter != null&&isWlifeCrmPay) {
                    AcewillPosBillMember createPosBillMember = null;
                    try {
                        createPosBillMember = createPosBillMember(tenancyId, storeId, wlife, posBill, acewillPayInfo, consumeAmount, consumeCredit);
                        posBillMemberList.add(createPosBillMember);
                    } catch (Exception e) {
                        LOG.error("生成微生活会员信息异常",e);
                    }
                    if (WLifeConstant.PAY_SOURCE_BALANCE.equals(acewillPayInfo.getSource())) {
                        if(acewillPayInfo.getAmount()!= null && acewillPayInfo.getAmount() > 0){
							CrmCardTradingListEntity crmCardTradingList = createCrmCardTradingList(tenancyId, storeId, wlife, posBill, acewillPayInfo);
							crmCardTradingListEntityList.add(crmCardTradingList);
                            isInclueBalance = true;
                            consumeAmount = acewillPayInfo.getAmount();
                        }

                    }
                    if (WLifeConstant.PAY_SOURCE_COUPON.equals(acewillPayInfo.getSource())
                            || WLifeConstant.PAY_SOURCE_PRODUCT.equals(acewillPayInfo.getSource())) {
                        AcewillPosBillPaymentCoupons posBillPaymentCoupons = createPosBillPaymentCoupons(microLifePayOrder,
                                posBill, acewillPayInfo);
                        posBillPaymentCoupons.setPayment_id(paymentId);
                        posBillPaymentCouponsList.add(posBillPaymentCoupons);
                    }
                }
            }
        }


        if (DoubleHelper.sub(paymentAmount, sumPaymentAmount, DEFAULT_SCALE) <= 0) {
        	if (null != posBillMemberList && 0 < posBillMemberList.size())
			{
        		paySucessDao.deletePosBillMember(tenancyId, storeId, posBill.getBill_num(), SysDictionary.BILL_MEMBERCARD_JFZS06);
			}
            paySucessDao.savePosBillPayment(posBillPaymentList);
            paySucessDao.savePosBillMember(posBillMemberList);
            paySucessDao.saveCrmCardTradingList(crmCardTradingListEntityList);
            paySucessDao.savePosBillPaymentCouponsList(posBillPaymentCouponsList);
            firstPaymentDao.updatePosBillSource(posBill.getBill_num(),SysDictionary.CHANEL_WSP17,false);
            JSONObject printJson = new JSONObject();
            JSONObject resultJson = new JSONObject();
            String isInvoice = StringUtils.isEmpty(payOrderData.getInvoice()) ? "0" : "1";
            posPaymentService.closedAcewillPosBill(tenancyId, storeId, posBill.getBill_num(),
                    posBill.getReport_date(), posBill.getShift_id(), posBill.getPos_num(),
                    posBill.getCashier_num(), "Y", isInvoice, resultJson, printJson, "1", com.tzx.pos.base.util.DateUtil.currentTimestamp());
            LOG.info("pay.success:单号："+posBill.getBill_num()+"=======================================================门店清台成功");

            for(AcewillPosBillPayment payment: posBillPaymentList){
                //拆分实收虚收
                splitPayment(tenancyId,billNum,payment.getPayment_uid(),payment.getCoupon_buy_price(),payment.getDue(),payment.getTenancy_assume(),payment.getType());
            }

            // 打印
            try {
                LOG.info("######printJson=" + printJson.toString());
                printJson.put("format_state", "2");
                posPrintService.printPosBillForPayment(printJson, posPrintNewService);
                LOG.info("pay.success:单号："+posBill.getBill_num()+"=======================================================门店清台打印完成");
                if(isInclueBalance){
                    LOG.info("=========================isInclueBalance True===============");
                    printMemberBalance(microLifePayOrder, posBill, consumeAmount);
                }

            }catch (Exception e) {
                LOG.error("print failed... bill_number:"+posBill.getBill_num());
            }
        }else {
            throw SystemException.getInstance(PosErrorCode.BILL_DIFFERENCE_MORE_ZERO_ERROR);
        }

        buildSuccessData(null, responseJson, outOrderId,false);

        return responseJson;
    }

    private void dealCreditCash(List<AcewillPayInfo> acewillPayInfos){
        double amount = 0;
        for (AcewillPayInfo acewillPayInfo : acewillPayInfos) {
            if (WLifeConstant.PAY_SOURCE_CREDIT.equals(acewillPayInfo.getSource())) {
                amount = acewillPayInfo.getAmount();
            }
        }
        for (AcewillPayInfo acewillPayInfo : acewillPayInfos) {
            if (WLifeConstant.PAY_SOURCE_WLIFE.equals(acewillPayInfo.getSource())) {
                acewillPayInfo.setCreditCash(amount);
            }
        }
    }

    private void printOrderInfo(String tenancyId,Integer storeId,JSONObject printJson){
        // 异步执行分单打印
        Data data = new Data();
        data.setTenancy_id(tenancyId);
        data.setStore_id(storeId);
        OrderingPrintThread orderingPrintThread = new OrderingPrintThread(printJson, data);
        ThreadPool.getPrintThreadPool().execute(orderingPrintThread);
    }

    private void  setOrderMemberInfo(MicroLifeOrderInfo microLifeOrderInfo,JSONObject orderInfo){
        String name = orderInfo.optString("name");
        String mobile = orderInfo.optString("mobile");
        String openid = orderInfo.optString("openid");
        Double balance = orderInfo.optDouble("balance",0);
        Double credit = orderInfo.optDouble("credit",0);
        AcewillOrderMember member = new AcewillOrderMember();
        member.setName(name);
        member.setOpenid(openid);
        member.setMobile(mobile);
        member.setCredit(credit);
        member.setBalance(balance);
        microLifeOrderInfo.setMember(member);

    }
    private void printMemberBalance(MicroLifePayOrder microLifePayOrder, AcewillPosBill posBill, Double consumeAmount) throws Exception {
        //balance print
        AcewillPayWLife wlife = microLifePayOrder.getWlife();
        JSONObject printMemberBlance = new JSONObject();

        String cardCode = wlife.getCno();
        if(StringUtil.hasText(cardCode)){
            JSONObject memberInfo = firstPaymentDao.getBillMemberInfo(posBill.getBill_num(), SysDictionary.BILL_MEMBERCARD_JFZS06, cardCode);
            if(null != memberInfo){
                if(StringUtil.hasText(memberInfo.optString("mobil"))){
                    printMemberBlance.put("mobil",memberInfo.optString("mobil"));
                }else {
                    printMemberBlance.put("mobil","");
                }
                printMemberBlance.put("useful_credit", memberInfo.optDouble("consume_before_credit"));
            }else {
                printMemberBlance.put("mobil", "");
                printMemberBlance.put("useful_credit",0);
            }

            printMemberBlance.put("isprint", "Y");
            printMemberBlance.put("pos_num", posBill.getPos_num());
            printMemberBlance.put("print_time", DateUtil.format(DateUtil.currentTimestamp()));
            printMemberBlance.put("bill_code", posBill.getBill_num());
            printMemberBlance.put("card_code", cardCode);
            printMemberBlance.put("level_name", wlife.getGrade_name());
            printMemberBlance.put("card_class_name", "");
            printMemberBlance.put("name", wlife.getName());
            printMemberBlance.put("consume_cardmoney", consumeAmount);
            printMemberBlance.put("main_balance", wlife.getBalance());
            printMemberBlance.put("reward_balance", 0);

            String operator = firstPaymentDao.getEmpNameById(posBill.getOpen_opt(), microLifePayOrder.getBusiness_id(), microLifePayOrder.getShop_id());
            printMemberBlance.put("operator", operator);
            printMemberBlance.put("updatetime", DateUtil.format(DateUtil.currentTimestamp()));
            // 会员卡打印
            LOG.info(" print member balance start pararms ="+printMemberBlance.toString());
            customerService.customerCardConsumePrint(microLifePayOrder.getBusiness_id(), microLifePayOrder.getShop_id(), posBill.getPos_num(), printMemberBlance,
                    SysDictionary.PRINT_CODE_1010);
            LOG.info(" print member balance end tenancyId ="+microLifePayOrder.getBusiness_id()+",storeId="+microLifePayOrder.getShop_id()+",posNum="+posBill.getPos_num());
        }
    }
    private Map<String, AcewillPaymentWay> getPaymentWayMap(String tenantId, Integer storeId,
                                                            MicroLifePayOrder microLifePayOrder) throws Exception {
        List<String> paymentClassList = new ArrayList<>();
        for (AcewillPayInfo payInfo : microLifePayOrder.getPay_info()) {
            String paymentClass = paymentWayMapping.get(payInfo.getSource());
            if (!org.apache.commons.lang.StringUtils.isEmpty(paymentClass)) {
                paymentClassList.add(paymentClass);
            }
        }
        List<JSONObject> paymentWayList = paySucessDao.findPaymentWay(tenantId, storeId, paymentClassList);
        Map<String, AcewillPaymentWay> paymentWayMap = new HashMap<>();
        if (paymentWayList != null) {
            for (JSONObject jsonObject : paymentWayList) {
                AcewillPaymentWay bean = JsonUtil.jsonToBean(jsonObject, AcewillPaymentWay.class);
                paymentWayMap.put(bean.getPayment_class(), bean);
            }
        }
        return paymentWayMap;
    }

    private AcewillPaymentWay getPaymentWay(Map<String, AcewillPaymentWay> paymentWayMap, AcewillPayInfo payInfo)
            throws Exception {
        String paymentClass = paymentWayMapping.get(payInfo.getSource());
        if (paymentClass == null) {
            LOG.error("未知的支付方式：{}", payInfo.getSource());
            return null;
        }
        AcewillPaymentWay paymentWay = paymentWayMap.get(paymentClass);
        if (paymentWay == null) {
            LOG.error("门店支付方式{}未启用", payInfo.getSource());
            return null;
        }
        return paymentWay;
    }

    private AcewillPosBillPayment createPosBillPayment(String tenancyId, Integer storeId, AcewillPayWLife wlife, AcewillPosBill posBill,
                                                       AcewillPaymentWay paymentWay, AcewillPayInfo payInfo) throws Exception {

        AcewillPosBillPayment posBillPayment = new AcewillPosBillPayment();
        posBillPayment.setTenancy_id(tenancyId);
        posBillPayment.setStore_id(storeId);
        posBillPayment.setId(null);
        posBillPayment.setBill_num(posBill.getBill_num());
        posBillPayment.setTable_code(posBill.getTable_code());
        posBillPayment.setType(paymentWay.getPayment_class());
        posBillPayment.setJzid(paymentWay.getId());
        posBillPayment.setName(paymentWay.getPayment_name1());
        posBillPayment.setName_english(paymentWay.getPayment_name2());
        posBillPayment.setAmount(payInfo.getAmount());
        posBillPayment.setCount(1);
        // 付款号码
        if (wlife != null && (SysDictionary.PAYMENT_CLASS_WLIFE_BALANCE.equals(paymentWay.getPayment_class()) || SysDictionary.PAYMENT_CLASS_WLIFE_CREDIT.equals(paymentWay.getPayment_class()))) {
            posBillPayment.setNumber(wlife.getCno());
            posBillPayment.setPhone(wlife.getMobil());
        }
        posBillPayment.setReport_date(com.tzx.pos.base.util.DateUtil.parseDate(posBill.getReport_date()));
        posBillPayment.setShift_id(posBill.getShift_id());
        posBillPayment.setPos_num(posBill.getPos_num());
        // 收款员号
        posBillPayment.setCashier_num(posBill.getCashier_num());
        posBillPayment.setLast_updatetime(new Date());
        posBillPayment.setIs_ysk("N");
        posBillPayment.setRate(1d);
        posBillPayment.setCurrency_amount(payInfo.getAmount());
        // 上传标记
        posBillPayment.setUpload_tag(0);
        // 挂账人id
        posBillPayment.setCustomer_id(null);
        // 付款流水号
        posBillPayment.setBill_code(payInfo.getSerilNo());
        posBillPayment.setRemark(null);
        posBillPayment.setPayment_state(SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
        // 与总部通讯的缓存
        posBillPayment.setParam_cach(null);
        // 批次编号
        posBillPayment.setBatch_num(posBill.getBatch_num());
        // 多收礼卷
        posBillPayment.setMore_coupon(0d);
        posBillPayment.setFee(0d);
        posBillPayment.setFee_rate(null);
        // 优惠券类型
        String coupon_type = posBillCouponTypeMapping.get(payInfo.getSource());
        posBillPayment.setCoupon_type(coupon_type != null ? Integer.parseInt(coupon_type) : null);
        // 原付款方式ID
        posBillPayment.setYjzid(null);

        Double couponBuyPrice = (payInfo.getStorepay() != null ? payInfo.getStorepay() : 0.00);
        if (WLifeConstant.PAY_SOURCE_ALIPAY.equals(payInfo.getSource())) {
            couponBuyPrice = (payInfo.getStorepay() != null ? payInfo.getStorepay() / 100 : 0.00);
        }
        if (WLifeConstant.PAY_SOURCE_BALANCE.equals(payInfo.getSource())) {
            couponBuyPrice = (payInfo.getStorepay() != null ? payInfo.getStorepay() / 100 : 0.00);
        } else if (WLifeConstant.PAY_SOURCE_CREDIT.equals(payInfo.getSource()) || WLifeConstant.PAY_SOURCE_COUPON.equals(payInfo.getSource()) || WLifeConstant.PAY_SOURCE_PRODUCT.equals(payInfo.getSource())) {
            couponBuyPrice = 0d;
        }else if(WLIFE_THIRD_COUPONS.contains(payInfo.getSource())){
            couponBuyPrice=payInfo.getBuyer_pay_amount();
        }
        Double due = couponBuyPrice;
        Double tenancyAssume = payInfo.getAmount() - couponBuyPrice;
        Double thirdAssume = 0d;
        Double thirdFee = 0d;

        // 用户实付
        posBillPayment.setCoupon_buy_price(couponBuyPrice);
        // 券账单净收
        posBillPayment.setDue(due);
        // 商家优惠承担
        posBillPayment.setTenancy_assume(tenancyAssume);
        // 第三方优惠承担
        posBillPayment.setThird_assume(thirdAssume);
        // 第三方票券服务费
        posBillPayment.setThird_fee(thirdFee);
        return posBillPayment;
    }
    private AcewillPosBillMember createPosBillMember(String tenancyId,Integer storeId,AcewillPayWLife wlife, AcewillPosBill posBill,
                                                     AcewillPayInfo payInfo,Double consumeAmount,Double consumeCredit) throws Exception {
//        AcewillPayWLife wlife = microLifePayOrder.getWlife();
//        Double balance = wlife.getBalance();
//        wlife.setBalance(DoubleHelper.div(wlife.getBalance(), 100d, DEFAULT_SCALE));
    	
        AcewillPosBillMember posBillMember = new AcewillPosBillMember();
        posBillMember.setTenancy_id(tenancyId);
        posBillMember.setStore_id(storeId);
        posBillMember.setId(null);
        posBillMember.setBill_num(posBill.getBill_num());
        posBillMember.setReport_date(com.tzx.pos.base.util.DateUtil.parseDate(posBill.getReport_date()));
        // 类型
        posBillMember.setType(posBillMemberTyoeMapping.get(payInfo.getSource()));
        posBillMember.setAmount(payInfo.getAmount());
        if(WLifeConstant.PAY_SOURCE_WLIFE.equals(payInfo.getSource())){
            posBillMember.setCredit(wlife.getReceive_credit());
        }else {
            posBillMember.setCredit(0.0);
        }

        posBillMember.setCard_code(wlife.getCno());
//        JSONObject memberInfo = firstPaymentDao.getBillMemberInfo(posBill.getBill_num(), SysDictionary.BILL_MEMBERCARD_JFZS06, wlife.getCno());
//        if(null != memberInfo){
//            if(StringUtil.hasText(memberInfo.optString("mobil"))){
//                posBillMember.setMobil(memberInfo.optString("mobil"));
//            }
//
//        }
        posBillMember.setMobil(wlife.getMobil());

        posBillMember.setLast_updatetime(new Date());
        // 上传标记
        posBillMember.setUpload_tag(0);
        posBillMember.setRemark(null);
        posBillMember.setBill_code(payInfo.getSerilNo());
        // 请求状态
        posBillMember.setRequest_state(null);
        posBillMember.setCustomer_code(wlife.getCno());
        posBillMember.setCustomer_name(wlife.getName());
        // 交易前积分
//        if (WLifeConstant.PAY_SOURCE_CREDIT.equals(payInfo.getSource())) {
//            posBillMember.setConsume_before_credit(wlife.getCredit() + payInfo.getAmount());
//        }else {
//            Double consumeBeforeCredit = DoubleHelper.add(wlife.getCredit().doubleValue(),payInfo.getCreditCash(),DEFAULT_SCALE);
//            posBillMember.setConsume_before_credit(consumeBeforeCredit);
//        }
        Double consumeBeforeCredit = DoubleHelper.add(wlife.getCredit().doubleValue(),consumeCredit,DEFAULT_SCALE);
        posBillMember.setConsume_before_credit(consumeBeforeCredit);
        // 交易后积分
        posBillMember.setConsume_after_credit(wlife.getCredit() == null ? null : wlife.getCredit().doubleValue());
        if (WLifeConstant.PAY_SOURCE_WLIFE.equals(payInfo.getSource())) 
        {
        	posBillMember.setConsume_after_credit(DoubleHelper.add(wlife.getCredit().doubleValue(), wlife.getReceive_credit(), DEFAULT_SCALE));
        }
        
        // 交易前主账户余额
        Double consumeBeforeBalance = DoubleHelper.add(wlife.getBalance().doubleValue(),consumeAmount,DEFAULT_SCALE);
        posBillMember.setConsume_before_main_balance(consumeBeforeBalance);
//        if (WLifeConstant.PAY_SOURCE_BALANCE.equals(payInfo.getSource())) {
//            posBillMember.setConsume_before_main_balance(wlife.getBalance() + payInfo.getAmount());
//        }
        // 交易前赠送账户余额
        posBillMember.setConsume_before_reward_balance(0d);
        // 交易后主账户余额
        posBillMember.setConsume_after_main_balance(wlife.getBalance());
        // 交易后赠送账户余额
        posBillMember.setConsume_after_reward_balance(0d);
//        wlife.setBalance(balance);
        return posBillMember;
    }
    private JsonConfig getJsonConfig() {
        JsonConfig config = new JsonConfig();
        config.setClassMap(getInsertOrderClassMap());
        config.setRootClass(MicroLifeOrder.class);
        config.setJavaPropertyFilter(new PropertyFilter() {
            @Override
            public boolean apply(Object source, String name, Object value) {
                PropertyDescriptor propertyDescriptor = BeanUtils.getPropertyDescriptor(source.getClass(), name);
                return propertyDescriptor==null;
            }
        });
        return config;
    }
    private JsonConfig getOrderInfoJsonConfig() {
        JsonConfig config = new JsonConfig();
        config.setClassMap(getInsertOrderClassMap());
        config.setRootClass(MicroLifeOrderInfo.class);
        config.setJavaPropertyFilter(new PropertyFilter() {
            @Override
            public boolean apply(Object source, String name, Object value) {
                PropertyDescriptor propertyDescriptor = BeanUtils.getPropertyDescriptor(source.getClass(), name);
                return propertyDescriptor==null;
            }
        });
        return config;
    }
    private JsonConfig getPayJsonConfig() {
        JsonConfig config = new JsonConfig();
        config.setClassMap(getPayMessageClassMap());
        config.setRootClass(MicroLifePayOrder.class);
        config.setJavaPropertyFilter(new PropertyFilter() {
            @Override
            public boolean apply(Object source, String name, Object value) {
                PropertyDescriptor propertyDescriptor = BeanUtils.getPropertyDescriptor(source.getClass(), name);
                return propertyDescriptor == null;
            }
        });
        return config;
    }

    private Map<String, Class<?>> getPayMessageClassMap() {
        Map<String, Class<?>> classMap = new HashMap<>();
        classMap.put("pay_info", AcewillPayInfo.class);
        classMap.put("wlife", AcewillPayWLife.class);
        classMap.put("receive_coupons", MicroLifeCoupons.class);
        classMap.put("data", MicroLifePayOrderData.class);
        return classMap;
    }


    private Map<String,Class<?>> getInsertOrderClassMap() {
        Map<String, Class<?>> classMap = new HashMap<>();
        classMap.put("order_info", AcewillOrderInfo.class);
        classMap.put("ordermemo", AcewillOrderMemo.class);
        classMap.put("member", AcewillOrderMember.class);
        classMap.put("upgrade", MicroLifeGrade.class);
        classMap.put("setmeal", AcewillOrderSetmeal.class);
        classMap.put("normalitems", AcewillOrderNormalitem.class);
        classMap.put("maindish", AcewillOrderSetmealItem.class);
        classMap.put("mandatory", AcewillOrderSetmealItem.class);
        classMap.put("optional", AcewillOrderSetmealItem.class);
        return classMap;
    }
    private AcewillPosBillPaymentCoupons createPosBillPaymentCoupons(MicroLifePayOrder microLifePayOrder,
                                                                     AcewillPosBill posBill, AcewillPayInfo payInfo) {
        AcewillPosBillPaymentCoupons posBillPaymentCoupons = new AcewillPosBillPaymentCoupons();

        // 商户id
        posBillPaymentCoupons.setTenancy_id(microLifePayOrder.getBusiness_id());
        // 机构id
        posBillPaymentCoupons.setStore_id(microLifePayOrder.getShop_id());
        // ID-自增
        posBillPaymentCoupons.setId(null);
        // 报表日期
        posBillPaymentCoupons.setBill_num(posBill.getBill_num());
        // 账单编号
        posBillPaymentCoupons.setReport_date(com.tzx.pos.base.util.DateUtil.parseDate(posBill.getReport_date()));
        // 付款方式ID
//        posBillPaymentCoupons.setPayment_id(posBill.getPayment_id());
        // 优惠劵号
        posBillPaymentCoupons.setCoupons_code(payInfo.getSerilNo());
        // 面值
        posBillPaymentCoupons.setDeal_value(payInfo.getAmount());
        // 劵类型名称
        posBillPaymentCoupons.setDeal_name(posBillCouponTypeNameMapping.get(payInfo.getSource()));
        // 操作时间
        posBillPaymentCoupons.setLast_updatetime(new Date());
        // 备注
        posBillPaymentCoupons.setRemark(null);
        // 上传标记
        posBillPaymentCoupons.setUpload_tag("0");
        // 是否撤销
        posBillPaymentCoupons.setIs_cancel("0");
        // 优惠券大类ID
        posBillPaymentCoupons.setClass_id(null);
        // 优惠券类型ID
        posBillPaymentCoupons.setType_id(null);
        // 优惠金额
        posBillPaymentCoupons.setDiscount_money(payInfo.getAmount());
        // 抵用数量
        posBillPaymentCoupons.setDiscount_num(null);
        // 渠道
        posBillPaymentCoupons.setChanel(posBill.getSource());
        // 菜品单价
        posBillPaymentCoupons.setPrice(null);
        // 抵扣菜品
        posBillPaymentCoupons.setItem_id(null);
        // 菜品数量
        posBillPaymentCoupons.setItem_num(null);
        // 优惠劵编码
        posBillPaymentCoupons.setCoupons_pro(payInfo.getSerilNo());
        // 优惠劵类型
        posBillPaymentCoupons.setCoupon_type(Integer.parseInt(posBillCouponTypeMapping.get(payInfo.getSource())));
        posBillPaymentCoupons.setCoupon_buy_price(null);
        posBillPaymentCoupons.setDue(null);
        posBillPaymentCoupons.setTenancy_assume(null);
        posBillPaymentCoupons.setThird_assume(null);
        posBillPaymentCoupons.setThird_fee(null);
        posBillPaymentCoupons.setRequest_state(2);

        return posBillPaymentCoupons;
    }

    private CrmCardTradingListEntity createCrmCardTradingList(String tenancyId,Integer storeId,AcewillPayWLife wlife, AcewillPosBill posBill,AcewillPayInfo acewillPayInfo) {
        CrmCardTradingListEntity crmCardTradingList = new CrmCardTradingListEntity();
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
//        AcewillPayWLife wlife = microLifePayOrder.getWlife();
        // 商户id
        crmCardTradingList.setTenancy_id(tenancyId);
        // Id
        crmCardTradingList.setId(null);
        // 卡id
        crmCardTradingList.setCard_id(null);
        // 卡号 
        crmCardTradingList.setCard_code(wlife.getCno());
        // 交易单号
        crmCardTradingList.setBill_code(acewillPayInfo.getSerilNo());
        // 交易渠道
        crmCardTradingList.setChanel(posBill.getSource());
        // 交易门店ID
        crmCardTradingList.setStore_id(storeId);
        // 交易日期
        crmCardTradingList.setBusiness_date(com.tzx.pos.base.util.DateUtil.parseDate(posBill.getReport_date()));
        // 主账户交易金额
        crmCardTradingList.setMain_trading(acewillPayInfo.getAmount());
        // 赠送账户交易金额
        crmCardTradingList.setReward_trading(0d);
        // 交易类型
        crmCardTradingList.setOperat_type(SysDictionary.OPERAT_TYPE_XF);

        // 原主账户金额
        crmCardTradingList.setMain_original(
                DoubleHelper.add(wlife.getBalance(), acewillPayInfo.getAmount(), DEFAULT_SCALE));
        // 原赠送账户金额
        crmCardTradingList.setReward_original(0d);
        // 押金金额
        crmCardTradingList.setDeposit(0d);
        // 操作员
        crmCardTradingList.setOperator(null);
        // 操作时间
        crmCardTradingList.setOperate_time(timestamp);
        // 账单金额
        crmCardTradingList.setBill_money(posBill.getBill_amount());
        // 第三方账单号
        crmCardTradingList.setThird_bill_code(posBill.getBill_num());
        // 原账单号
        crmCardTradingList.setBill_code_original(null);
        // 活动ID
        crmCardTradingList.setActivity_id(null);
        // 会员ID
        crmCardTradingList.setCustomer_id(null);
        // 已撤销金额
        crmCardTradingList.setRevoked_trading(0d);
        //
        crmCardTradingList.setBatch_num(posBill.getBatch_num());
        // 最后修改时间
        crmCardTradingList.setLast_updatetime(timestamp);
        // 门店修改时间
        crmCardTradingList.setStore_updatetime(timestamp);
        // 卡类ID
        crmCardTradingList.setCard_class_id(null);
        // 会员name
        crmCardTradingList.setName(wlife.getName());
        // 会员电话
        crmCardTradingList.setMobil(wlife.getMobil());
        // 操作员ID
        crmCardTradingList.setOperator_id(null);
        // 班次ID
        crmCardTradingList.setShift_id(posBill.getShift_id());
        // 卡余额
        crmCardTradingList.setTotal_balance(wlife.getBalance());
        // 卡赠送账户余额
        crmCardTradingList.setReward_balance(0d);
        // 卡主账户余额
        crmCardTradingList.setMain_balance(wlife.getBalance());
        // 付款方式
        crmCardTradingList.setPay_type(null);

        // 销售人员ID
        crmCardTradingList.setSalesman(null);
        // 人员提成金额
        crmCardTradingList.setCommission_saler_money(0d);
        // 机构提成金额
        crmCardTradingList.setCommission_store_money(0d);
        // 可开票金额
        crmCardTradingList.setInvoice_balance(0d);
        //pos机台号
        crmCardTradingList.setPosNum(posBill.getPos_num());
        crmCardTradingList.setIs_invoice("0");
        crmCardTradingList.setPayment_state("1");
        crmCardTradingList.setRecharge_state("1");
        crmCardTradingList.setRequest_status("02");
        crmCardTradingList.setRequest_code(null);
        crmCardTradingList.setRequest_msg(null);

        return crmCardTradingList;
    }




    static {
        paymentWayMapping.put(WLifeConstant.PAY_SOURCE_ALIPAY, "ali_pay_wlife");
        paymentWayMapping.put(WLifeConstant.PAY_SOURCE_WEIXIN, "wechat_pay_wlife");
       /* paymentWayMapping.put(WLifeConstant.PAY_SOURCE_BALANCE, SysDictionary.PAYMENT_CLASS_CARD);
        paymentWayMapping.put(WLifeConstant.PAY_SOURCE_CREDIT, SysDictionary.PAYMENT_CLASS_CARD_CREDIT);
        paymentWayMapping.put(WLifeConstant.PAY_SOURCE_COUPON, SysDictionary.PAYMENT_CLASS_COUPONS);
        paymentWayMapping.put(WLifeConstant.PAY_SOURCE_PRODUCT, SysDictionary.PAYMENT_CLASS_COUPONS);*/
        paymentWayMapping.put(WLifeConstant.PAY_SOURCE_BALANCE, SysDictionary.PAYMENT_CLASS_WLIFE_BALANCE);
        paymentWayMapping.put(WLifeConstant.PAY_SOURCE_CREDIT, SysDictionary.PAYMENT_CLASS_WLIFE_CREDIT);
        paymentWayMapping.put(WLifeConstant.PAY_SOURCE_COUPON, SysDictionary.PAYMENT_CLASS_WLIFE_COUPON);
        paymentWayMapping.put(WLifeConstant.PAY_SOURCE_PRODUCT, SysDictionary.PAYMENT_CLASS_WLIFE_COUPON);
        paymentWayMapping.put(WLifeConstant.PAY_SOURCE_WLIFE, "wlife");

        paymentWayMapping.put(WLifeConstant.PAY_SOURCE_MEITUAN,SysDictionary.PAYMENT_CLASS_WLIFE_MEITUAN);
        paymentWayMapping.put(WLifeConstant.PAY_SOURCE_DOUYIN, SysDictionary.PAYMENT_CLASS_WLIFE_DOUYIN);
        paymentWayMapping.put(WLifeConstant.PAY_SOURCE_ALIPAYCOUPON, SysDictionary.PAYMENT_CLASS_WLIFE_ALIPAYCOUPON);
        paymentWayMapping.put(WLifeConstant.PAY_SOURCE_LITTLE_RED_BOOK, SysDictionary.PAYMENT_CLASS_WLIFE_LITTLE_RED_BOOK);

        posBillMemberTyoeMapping.put(WLifeConstant.PAY_SOURCE_ALIPAY, SysDictionary.BILL_MEMBERCARD_HYZK02);
        posBillMemberTyoeMapping.put(WLifeConstant.PAY_SOURCE_WEIXIN, SysDictionary.BILL_MEMBERCARD_HYZK02);
        posBillMemberTyoeMapping.put(WLifeConstant.PAY_SOURCE_BALANCE, SysDictionary.BILL_MEMBERCARD_CZXF03);
        posBillMemberTyoeMapping.put(WLifeConstant.PAY_SOURCE_CREDIT, SysDictionary.BILL_MEMBERCARD_JFDX05);
        posBillMemberTyoeMapping.put(WLifeConstant.PAY_SOURCE_COUPON, SysDictionary.BILL_MEMBERCARD_YHJ04);
        posBillMemberTyoeMapping.put(WLifeConstant.PAY_SOURCE_PRODUCT, SysDictionary.BILL_MEMBERCARD_YHJ04);
        posBillMemberTyoeMapping.put(WLifeConstant.PAY_SOURCE_WLIFE, SysDictionary.BILL_MEMBERCARD_JFZS06);

        posBillCouponTypeMapping.put(WLifeConstant.PAY_SOURCE_COUPON, SysDictionary.CUSTOMER_COUPON_TYPE_CASH);
        posBillCouponTypeMapping.put(WLifeConstant.PAY_SOURCE_PRODUCT, SysDictionary.CUSTOMER_COUPON_TYPE_DISH);
        posBillCouponTypeNameMapping.put(WLifeConstant.PAY_SOURCE_COUPON, "优惠券");
        posBillCouponTypeNameMapping.put(WLifeConstant.PAY_SOURCE_PRODUCT, "菜品劵");
    }

    private Data createOrderDishData(MicroLifeOrder microLifeOrder, AcewillOrderAssist orderAssist) throws Exception {
    	//组织下单参数
    	AcewillOrder order = createOrder(microLifeOrder, orderAssist);
		JSONObject orderJsonObject = JSONObject.fromObject(order, getAcewillOrderJsonConfig());
    	
		List<JSONObject> orderList = new ArrayList<>();
		orderList.add(orderJsonObject);
    	
    	Data data = new Data();
		data.setTenancy_id(microLifeOrder.getBusiness_id());
		data.setStore_id(microLifeOrder.getShop_id());
		data.setType(Type.ORDERING);
		data.setOper(Oper.add);
		data.setSource("cc_order");
		
		data.setData(orderList);
		return data;
    }
    private JsonConfig getAcewillOrderJsonConfig() {
        Map<String, Class<?>> classMap = new HashMap<>();
        classMap.put("item", AcewillOrderItem.class);
        classMap.put("method", AcewillOrderItemMethod.class);
        JsonConfig jsonConfig = new JsonConfig();
        jsonConfig.setClassMap(classMap);
        jsonConfig.setRootClass(AcewillOrder.class);
        return jsonConfig;
    }
    private AcewillOrder createOrder(MicroLifeOrder microLifeOrder, AcewillOrderAssist orderAssist) throws Exception {

        MicroLifeOrderInfo microLifeOrderInfo = microLifeOrder.getOrder_info();

        AcewillOrder acewillOrder = new AcewillOrder();
        // 账单号
        acewillOrder.setBill_num(orderAssist.getBill_num());
        // 整单备注
        //String billTaste = insertOrderDao.getBillTaste(insertOrder);
        AcewillOrderMemo ordermemo = microLifeOrderInfo.getOrdermemo();

//        acewillOrder.setBill_taste(ordermemo.getText());

        // 是否厨打
        acewillOrder.setIsprint("Y");
        // 0:下单 1:
        acewillOrder.setMode(0);
        acewillOrder.setChanel(SysDictionary.CHANEL_WX02);
        // 操作员编号
        acewillOrder.setOpt_num(orderAssist.getOpt_num());
        // 收款机编号
        acewillOrder.setPos_num(orderAssist.getPos_num());
        // 备注
        if(StringUtil.hasText(ordermemo.getText())){
            acewillOrder.setRemark(ordermemo.getText());
        }

        // 报表日期
        acewillOrder.setReport_date(orderAssist.getReport_date());
        // 销售模式
        // acewillOrder.setSale_mode(sale_mode);
        // 班次id
        acewillOrder.setShift_id(orderAssist.getShift_id());
        //桌号
        acewillOrder.setTable_code(microLifeOrderInfo.getTableno());
        // 服务员号
        acewillOrder.setWaiter_num(null);
        //菜品明细
        List<AcewillOrderItem> orderItems = new ArrayList<>();
        acewillOrder.setItem(orderItems);
        // 点餐序号
        Integer item_serial = insertOrderDao.getLastItemSerial(microLifeOrder.getBusiness_id(), orderAssist.getBill_num());
        
        List<AcewillOrderSetmeal> setmealList = microLifeOrderInfo.getSetmeal();
        
      //获取套餐档案
//        Map<String, JSONObject> itemComboDetailMap = itemComboDetailMap(microLifeOrder);
        Map<String, Map<String, JSONObject>> itemComboMap = this.getComboDetailMap(microLifeOrder.getBusiness_id(), setmealList); 
        
        //套餐规格id不需要设置
		getSetmealUnitMap(microLifeOrder.getBusiness_id(), setmealList);
        
        Map<String, String> unitNameMap = firstPaymentDao.getUnitNameMap(microLifeOrder);
        if(setmealList!=null) {
            for (AcewillOrderSetmeal setmeal : setmealList) {
                item_serial++;
 
                String duName = unitNameMap.get(setmeal.getDuid());
				setmeal.setDuName(duName);
				orderItems.add(createOrderItem(microLifeOrderInfo, setmeal, item_serial, null));
				
				List<AcewillOrderSetmealItem> setmealItemList = new ArrayList<>();
				if (null != setmeal.getMaindish()){
					setmealItemList.addAll(setmeal.getMaindish());
				}
				if (null != setmeal.getMandatory()){
					setmealItemList.addAll(setmeal.getMandatory());
				}
				if (null != setmeal.getOptional()){
					setmealItemList.addAll(setmeal.getOptional());
				}

				//获取套餐明细信息
				Map<String, JSONObject> itemComboDetailMap = itemComboMap.get(setmeal.getDid());
				List<AcewillOrderItem> setmealOrderItemList = new ArrayList<AcewillOrderItem>();
				for (AcewillOrderSetmealItem item : setmealItemList)
				{
					item.setNumber(setmeal.getNumber() * item.getNumber());
					String duItemName = unitNameMap.get(item.getDuid());
					item.setDuName(duItemName);
					item.setMallListName();// 设置套餐明细菜品名称
					JSONObject itemComboDetail = this.getComboDetailByDetailsId(itemComboDetailMap, item);
					AcewillOrderItem setmealOrderItem = createOrderItem(microLifeOrderInfo, setmeal, item, item_serial, itemComboDetail);
					setmealOrderItemList.add(setmealOrderItem);
				}
				orderItems.addAll(this.sortOrderSetmealItem(setmealOrderItemList));
            }
        }
        List<AcewillOrderNormalitem> normalitemList = microLifeOrderInfo.getNormalitems();
        if(normalitemList!=null) {
            for (AcewillOrderNormalitem item : normalitemList) {
                item_serial++;
                String duName = unitNameMap.get(item.getDuid());
                item.setDuName(duName);
//                JSONObject itemComboDetail = itemComboDetailMap.get(item.getDid());
                AcewillOrderItem setmealOrderItem = createOrderItem(item, item_serial, null);
                orderItems.add(setmealOrderItem);
            }
        }

        return acewillOrder;
    }
    
    /** 套餐明细排序
	 * @param setmealItemList
	 * @return
	 * @throws Exception
	 */
	private List<AcewillOrderItem> sortOrderSetmealItem(List<AcewillOrderItem> setmealItemList) throws Exception
	{
		if (null == setmealItemList)
		{
			return null;
		}
		Collections.sort(setmealItemList);
		return setmealItemList;
	}
	
    private Map<String, String> getSetmealUnitMap(String tenentId,List<AcewillOrderSetmeal> setmealList) throws Exception {
        List<String> itemIdList = new ArrayList<>();
        if(setmealList!=null) {
            for (AcewillOrderSetmeal setmeal : setmealList) {
                itemIdList.add(setmeal.getDid());
            }
        }
        Map<String,String> setmealUnitMap = new HashMap<>();
        List<JSONObject> findItemUnit = insertOrderDao.findItemUnit(tenentId, itemIdList);
        if(findItemUnit!=null) {
            for (JSONObject jsonObject : findItemUnit) {
                setmealUnitMap.put(jsonObject.getString("item_id"), jsonObject.getString("id"));
            }
        }
        
        //菜品规格
        if(setmealList!=null) {
            for (AcewillOrderSetmeal setmeal : setmealList) {
                String setmealUnitId = setmealUnitMap.get(setmeal.getDid());
                if(!org.apache.commons.lang.StringUtils.isEmpty(setmealUnitId)) {
                    setmeal.setDuid(setmealUnitId);
                }
            }
        }
        return setmealUnitMap;
    }

    //套餐菜品id
    private Map<String, JSONObject> itemComboDetailMap(MicroLifeOrder microLifeOrder)
            throws Exception {
        String TenentId = microLifeOrder.getBusiness_id();
        MicroLifeOrderInfo microLifeOrderInfo = microLifeOrder.getOrder_info();
        List<Integer> itemIdList = new ArrayList<>();
        List<AcewillOrderSetmeal> setmealList = microLifeOrderInfo.getSetmeal();
        if (setmealList!=null) {
            for (AcewillOrderSetmeal setmeal : setmealList) {
                List<AcewillOrderSetmealItem> setmealItemList = new ArrayList<>();
                if(null != setmeal.getMaindish()){
                    setmealItemList.addAll(setmeal.getMaindish());
                }
                if(null != setmeal.getMandatory()){
                    setmealItemList.addAll(setmeal.getMandatory());
                }
                if(null !=setmeal.getOptional()){
                    setmealItemList.addAll(setmeal.getOptional());
                }

                for (AcewillOrderSetmealItem item : setmealItemList) {
                    itemIdList.add(Integer.parseInt(item.getId()));
                }
                itemIdList.add(Integer.parseInt(setmeal.getDid()));
            }
        }
        List<AcewillOrderNormalitem> normalitemList = microLifeOrderInfo.getNormalitems();
        if(normalitemList!=null) {
            for (AcewillOrderNormalitem item : normalitemList) {
                itemIdList.add(Integer.parseInt(item.getDid()));
            }
        }
        List<JSONObject> itemComboDetails = insertOrderDao.getItemComboDetails(TenentId, itemIdList);
        Map<String,JSONObject> itemComboDetailMap = new HashMap<>();
        if(itemComboDetails!=null) {
            for (JSONObject jsonObject : itemComboDetails) {
                //itemComboDetailMap.put(jsonObject.getString("details_id"), jsonObject);
                itemComboDetailMap.put(jsonObject.getString("iitem_id"), jsonObject);
            }
        }
        return itemComboDetailMap;
    }
    
	/** 查询套餐明细
	 * @param tenantId
	 * @param setmealList
	 * @return
	 * @throws Exception
	 */
	private Map<String, Map<String, JSONObject>> getComboDetailMap(String tenantId,List<AcewillOrderSetmeal> setmealList) throws Exception
	{
		List<Integer> itemIdList = new ArrayList<>();
		if (setmealList != null)
		{
			for (AcewillOrderSetmeal setmeal : setmealList)
			{
				itemIdList.add(Integer.parseInt(setmeal.getDid()));
			}
		}

		//查询套餐明细
		List<JSONObject> omboDetailsList = firstPaymentDao.getItemComboDetails(tenantId, itemIdList);

		Map<String, Map<String, JSONObject>> comboMap = new HashMap<String, Map<String, JSONObject>>();
		if (null != omboDetailsList)
		{
			for (JSONObject comboDetail : omboDetailsList)
			{
				String iItemId = comboDetail.optString("iitem_id");//套餐主项ID
				String isItemGroup = comboDetail.optString("is_itemgroup");
				String detailsId = comboDetail.optString("details_id");
				String itemUnitId = comboDetail.optString("item_unit_id");

				Map<String, JSONObject> comboDetailMap = null;
				if (comboMap.containsKey(iItemId))
				{
					comboDetailMap = comboMap.get(iItemId);
				}
				else
				{
					comboDetailMap = new HashMap<String, JSONObject>();
				}

				if ("Y".equals(isItemGroup))
				{
					comboDetailMap.put(detailsId, comboDetail);
				}
				else
				{
					comboDetailMap.put(detailsId + "_" + itemUnitId, comboDetail);
				}

				comboMap.put(iItemId, comboDetailMap);
			}
		}
		return comboMap;
	}
	
	private JSONObject getComboDetailByDetailsId(Map<String, JSONObject> itemComboDetailMap,AcewillOrderSetmealItem item) throws Exception
	{
		JSONObject itemComboDetail = null;
		if (null != itemComboDetailMap)
		{
			if (Tools.hv(item.getRpdid()))
			{
				itemComboDetail = itemComboDetailMap.get(item.getRpdid());
			}
			if (null == itemComboDetail)
			{
				if (itemComboDetailMap.containsKey(item.getId() + "_" + item.getDuid()))
				{
					itemComboDetail = itemComboDetailMap.get(item.getId() + "_" + item.getDuid());
				}
				else
				{
					itemComboDetail = itemComboDetailMap.get(item.getId());
				}
			}
		}
		return itemComboDetail;
	}
	
    private AcewillOrderItem createOrderItem(AcewillOrderNormalitem item,
                                             Integer item_serial, JSONObject itemComboDetail) {
        AcewillOrderItem orderItem = new AcewillOrderItem();
        // 辅助Assist
        if(itemComboDetail!=null) {
            orderItem.setAssist_item_id(itemComboDetail.getString("id"));
            orderItem.setAssist_num(itemComboDetail.getString("combo_num"));
        }
        // orderItem.setAssist_money(assist_money);
        // 餐谱明细id orderItem.setDetails_id(details_id);

        orderItem.setItem_count(item.getNumber());

        orderItem.setItem_id(item.getDid());
        orderItem.setItem_name(item.getName());
        orderItem.setItem_num(item.getDishsno());
        orderItem.setItem_price(item.getPrice());
        orderItem.setItem_property(SysDictionary.ITEM_PROPERTY_SINGLE);
        orderItem.setItem_remark(null);
        // 点菜序号
        orderItem.setItem_serial(item_serial);
        orderItem.setItem_taste(item.getRemark());
        // 规格名称
        orderItem.setItem_unit_name(item.getDuName());

        // orderItem.setSale_mode(sale_mode);

        orderItem.setMethod(createItemMethod(item.getCooks()));
        // 座位号
        orderItem.setSeat_num(null);
        orderItem.setSetmeal_id(null);
        // 套菜点菜号
        orderItem.setSetmeal_rwid(null);
        // 规格id
        orderItem.setUnit_id(item.getDuid());
        // 等叫标记
        // orderItem.setWaitcall_tag(waitcall_tag);
        return orderItem;
    }
    private AcewillOrderItem createOrderItem(MicroLifeOrderInfo orderInfo, AcewillOrderSetmeal setmeal,
                                             Integer item_serial, JSONObject itemComboDetail) {
        AcewillOrderItem orderItem = new AcewillOrderItem();
        // 辅助Assist
        if(itemComboDetail!=null) {
            orderItem.setAssist_item_id(itemComboDetail.getString("id"));
            orderItem.setAssist_num(itemComboDetail.getString("combo_num"));
        }else {
            orderItem.setAssist_item_id("0");
            orderItem.setAssist_num("0");
        }
        // orderItem.setAssist_money(assist_money);
        // 餐谱明细id orderItem.setDetails_id(details_id);
        orderItem.setItem_count(setmeal.getNumber());

        orderItem.setItem_id(setmeal.getDid());
        orderItem.setItem_name(setmeal.getName());
        orderItem.setItem_num(setmeal.getDishsno());
        orderItem.setItem_price(setmeal.getPrice());
        orderItem.setItem_property(SysDictionary.ITEM_PROPERTY_SETMEAL);

        //orderItem.setItem_remark(setmeal.getRemark());
        // 点菜序号
        orderItem.setItem_serial(item_serial);
        orderItem.setItem_taste(setmeal.getRemark());

        // 规格名称
        orderItem.setItem_unit_name(setmeal.getDuName());
        // orderItem.setSale_mode(sale_mode);
        orderItem.setMethod(createItemMethod(setmeal.getCooks()));
        // 座位号
        orderItem.setSeat_num(null);
        orderItem.setSetmeal_id(setmeal.getDid());
        // 套菜点菜号
        orderItem.setSetmeal_rwid(item_serial);
        // 规格id
        orderItem.setUnit_id(setmeal.getDuid());
        // 等叫标记
        // orderItem.setWaitcall_tag(waitcall_tag);
        return orderItem;
    }
    private AcewillOrderItem createOrderItem(AcewillOrderInfo orderInfo, AcewillOrderNormalitem item,
                                             Integer item_serial, JSONObject itemComboDetail) {
        AcewillOrderItem orderItem = new AcewillOrderItem();
        // 辅助Assist
        if(itemComboDetail!=null) {
            orderItem.setAssist_item_id(itemComboDetail.getString("id"));
            orderItem.setAssist_num(itemComboDetail.getString("combo_num"));
        }
        // orderItem.setAssist_money(assist_money);
        // 餐谱明细id orderItem.setDetails_id(details_id);

        orderItem.setItem_count(item.getNumber());

        orderItem.setItem_id(item.getDid());
        orderItem.setItem_name(item.getName());
        orderItem.setItem_num(item.getDishsno());
        orderItem.setItem_price(item.getPrice());
        orderItem.setItem_property(SysDictionary.ITEM_PROPERTY_SINGLE);

        //orderItem.setItem_remark(item.getRemark());
        // 点菜序号
        orderItem.setItem_serial(item_serial);
        orderItem.setItem_taste(item.getRemark());
        // 规格名称
        orderItem.setItem_unit_name(item.getDuName());

        // orderItem.setSale_mode(sale_mode);

        orderItem.setMethod(createItemMethod(item.getCooks()));
        // 座位号
        orderItem.setSeat_num(null);
        orderItem.setSetmeal_id(null);
        // 套菜点菜号
        orderItem.setSetmeal_rwid(null);
        // 规格id
        orderItem.setUnit_id(item.getDuid());
        // 等叫标记
        // orderItem.setWaitcall_tag(waitcall_tag);
        return orderItem;
    }
    private AcewillOrderItem createOrderItem(MicroLifeOrderInfo orderInfo, AcewillOrderSetmeal setmeal,
                                             AcewillOrderSetmealItem item, Integer item_serial, JSONObject itemComboDetail) {
        AcewillOrderItem orderItem = new AcewillOrderItem();
        // 辅助Assist
        if(itemComboDetail!=null) {
            orderItem.setAssist_item_id(itemComboDetail.getString("id"));
            orderItem.setAssist_num(itemComboDetail.getString("combo_num"));
            orderItem.setOrder_number(itemComboDetail.optInt("combo_order"));
        }
        // orderItem.setAssist_money(assist_money);
        // 餐谱明细id orderItem.setDetails_id(details_id);

        orderItem.setItem_count(item.getNumber());

        orderItem.setItem_id(item.getId());
        orderItem.setItem_name(item.getName());
        orderItem.setItem_num(item.getDishsno());
        //orderItem.setItem_price(item.getAprice());
        orderItem.setItem_property(SysDictionary.ITEM_PROPERTY_MEALLIST);
        //口味备注
        orderItem.setItem_taste(setmeal.getRemark());
        // 点菜序号
        orderItem.setItem_serial(item_serial);

        // orderItem.setItem_taste(null);
        // 规格名称
        orderItem.setItem_unit_name(item.getDuName());

        // 做法 orderItem.setMethod(method);

        // orderItem.setSale_mode(sale_mode);
        orderItem.setMethod(createItemMethod(item.getCooks()));
        // 座位号
        orderItem.setSeat_num(null);
        //套餐id
        orderItem.setSetmeal_id(setmeal.getDid());
        // 套菜点菜号
        orderItem.setSetmeal_rwid(item_serial);
        // 规格id
        orderItem.setUnit_id(item.getDuid());
        // 等叫标记
        // orderItem.setWaitcall_tag(waitcall_tag);
        return orderItem;
    }

    private AcewillOrderItem createOrderItem(AcewillOrderSetmeal item,
                                             Integer item_serial, JSONObject itemComboDetail) {
        AcewillOrderItem orderItem = new AcewillOrderItem();
        // 辅助Assist
        if(itemComboDetail!=null) {
            orderItem.setAssist_item_id(itemComboDetail.getString("id"));
            orderItem.setAssist_num(itemComboDetail.getString("combo_num"));
        }
        // orderItem.setAssist_money(assist_money);
        // 餐谱明细id orderItem.setDetails_id(details_id);

        orderItem.setItem_count(item.getNumber());

        orderItem.setItem_id(item.getDid());
        orderItem.setItem_name(item.getName());
        orderItem.setItem_num(item.getDishsno());
        orderItem.setItem_price(item.getPrice());
        orderItem.setItem_property(SysDictionary.ITEM_PROPERTY_SINGLE);
        orderItem.setItem_remark(null);
        // 点菜序号
        orderItem.setItem_serial(item_serial);
        orderItem.setItem_taste(item.getRemark());
        // 规格名称
        orderItem.setItem_unit_name(item.getDuName());

        // orderItem.setSale_mode(sale_mode);

        orderItem.setMethod(createItemMethod(item.getCooks()));
        // 座位号
        orderItem.setSeat_num(null);
        orderItem.setSetmeal_id(null);
        // 套菜点菜号
        orderItem.setSetmeal_rwid(null);
        // 规格id
        orderItem.setUnit_id(item.getDuid());
        // 等叫标记
        // orderItem.setWaitcall_tag(waitcall_tag);
        return orderItem;
    }
    private List<AcewillOrderItemMethod> createItemMethod(Object cooks)  {
        List<AcewillOrderItemMethod> methods = new ArrayList<>();
        if(null==cooks){
            return methods;
        }
        JSONArray cookArray = JSONArray.fromObject(cooks);
        if(cookArray!=null) {
            for (int i = 0; i < cookArray.size(); i++) {
                JSONObject jsonObject = cookArray.getJSONObject(i);
                AcewillOrderItemMethod method = new AcewillOrderItemMethod();
                String id = jsonObject.optString("id");
                if(org.apache.commons.lang.StringUtils.isBlank(id)||"null".equals(id)){
                    continue;
                }
                method.setMethod_id(jsonObject.getString("id"));
                method.setMethod_name(jsonObject.getString("cook"));
                methods.add(method);
            }
        }
        return methods;
    }
    private Data createOpenTableData(MicroLifeOrder microLifeOrder, AcewillOrderAssist orderAssist) throws Exception {
        MicroLifeOrderInfo orderInfo = microLifeOrder.getOrder_info();
        Data data = new Data();
        data.setTenancy_id(microLifeOrder.getBusiness_id());
        data.setStore_id(microLifeOrder.getShop_id());
        data.setType(Type.ORDERING);
        data.setOper(Oper.add);
        List<JSONObject> list = new ArrayList<>();
        JSONObject object = new JSONObject();
        if(StringUtil.hasText(orderInfo.getTableno())){
            object.put("table_code", orderInfo.getTableno());
            object.put("mode", 0);// 正餐
        }else {
            object.put("mode", 1);
            object.put("table_code", "99");
        }

        object.put("shift_id", orderAssist.getShift_id());
        object.put("report_date", orderAssist.getReport_date());
        object.put("table_code", orderInfo.getTableno());
        object.put("pos_num", orderAssist.getPos_num());
        object.put("opt_num", orderAssist.getOpt_num());
        object.put("waiter_num", orderAssist.getOpt_num());
        object.put("item_menu_id", 0);
        object.put("sale_mode", SysDictionary.SALE_MODE_TS01);
        object.put("chanel", SysDictionary.CHANEL_WX02);
        object.put("guest", orderInfo.getPeople());
        object.put("preorderno", microLifeOrder.getOut_order_id());
        object.put("copy_bill_num", "");
        object.put("remark", "");
        object.put("shop_real_amount", 0);
        object.put("platform_charge_amount", 0);
        object.put("settlement_type", "");
        object.put("discount_mode_id", 0);
        object.put("discountk_amount", 0);
        object.put("discount_rate", 0);
        list.add(object);
        data.setData(list);
        return data;
    }

    private AcewillOrderAssist createOrderAssist(JSONObject posBill) {
        AcewillOrderAssist orderAssist = new AcewillOrderAssist();
        orderAssist.setBill_num(posBill.getString("bill_num"));
        orderAssist.setOpt_num(posBill.getString("open_opt"));
        orderAssist.setPos_num(posBill.getString("pos_num"));
        orderAssist.setReport_date(posBill.getString("report_date"));
        orderAssist.setShift_id(posBill.getInt("shift_id"));
        return orderAssist;
    }
    private AcewillOrderAssist createOrderAssist(MicroLifeOrder microLifeOrder) throws Exception {
        AcewillOrderAssist orderAssist = new AcewillOrderAssist();
        String tenantId = microLifeOrder.getBusiness_id();
        Integer storeId = microLifeOrder.getShop_id();
        Date reportDate = insertOrderDao.getReportDate(tenantId, storeId);
        orderAssist.setReport_date(DateUtil.format(reportDate));
        List<JSONObject> optStateInfoList = posPaymentService.getOptStateInfo(tenantId, storeId,
                orderAssist.getReport_date());
        if (optStateInfoList == null || optStateInfoList.size() == 0) {
            throw new OtherSystemException(Constant.CODE_NULL_DATASET, Constant.CHECK_POS_LOGIN_FAILURE, null);
        }
        JSONObject optStateInfo = optStateInfoList.get(0);
        orderAssist.setPos_num(optStateInfo.getString("pos_num"));
        orderAssist.setOpt_num(optStateInfo.getString("opt_num"));
        int shiftId = insertOrderDao.getShiftId(tenantId, storeId, reportDate, orderAssist.getOpt_num(), orderAssist.getPos_num());
        orderAssist.setShift_id(shiftId);
        return orderAssist;
    }
    private AcewillPosBillMember savePosBillMember(MicroLifeOrder microLifeOrder, AcewillOrderAssist orderAssist)
            throws Exception {
        MicroLifeOrderInfo orderInfo = microLifeOrder.getOrder_info();
        AcewillOrderMember member = orderInfo.getMember();
        AcewillPosBillMember posBillMember = new AcewillPosBillMember();
        posBillMember.setTenancy_id(microLifeOrder.getBusiness_id());
        posBillMember.setStore_id(microLifeOrder.getShop_id());
        posBillMember.setBill_num(orderAssist.getBill_num());
        posBillMember.setBill_code(microLifeOrder.getOut_order_id());
        posBillMember.setReport_date(com.tzx.pos.base.util.DateUtil.parseDate(orderAssist.getReport_date()));
        posBillMember.setType(SysDictionary.BILL_MEMBERCARD_JFZS06);
        posBillMember.setAmount(null);

        posBillMember.setCredit(0.0);
        posBillMember.setCard_code(member.getCno());
        posBillMember.setMobil(member.getMobile());
        posBillMember.setLast_updatetime(new Date());
        posBillMember.setUpload_tag(0);
        posBillMember.setRemark(null);
        posBillMember.setBill_code(null);
        posBillMember.setRequest_state(null);
        posBillMember.setCustomer_code(member.getCno());
        posBillMember.setCustomer_name(member.getName());
        posBillMember.setConsume_before_credit(member.getCredit());
        posBillMember.setConsume_after_credit(member.getCredit());
        posBillMember.setConsume_before_main_balance(member.getBalance());
        //交易前赠送账户余额
        posBillMember.setConsume_before_reward_balance(0d);
        //交易后主账户余额
        posBillMember.setConsume_after_main_balance(member.getBalance());
        //交易后赠送账户余额
        posBillMember.setConsume_after_reward_balance(0d);
        insertOrderDao.savePosBillMember(posBillMember);
        return posBillMember;
    }

    private  void buildSuccessData(JSONObject data, JSONObject responseData, String oid, boolean isOrdering){
        if(null == data){
            data = new JSONObject();
        }
        if(isOrdering){
            data.put("oid", oid);
            data.put("djqflag", true);
            data.put("cpqflag", true);
        }else {
            data.put("identify", oid);//平台订单号
            data.put("meal_number", "");//
            data.put("ordermode", WLifeConstant.PRE_WLIFE_ORDER_MODE);
        }

        responseData.put("success",1);
        responseData.put(MSG,"SUCCESS");
        responseData.put(DATA, data);
    }

    private String openTable(MicroLifeOrder microLifeOrder,boolean isOrdering) throws Exception {
        AcewillOrderAssist orderAssist = createOrderAssist(microLifeOrder);

        Data param = createOpenTableData(microLifeOrder, orderAssist);

        String tenantId = param.getTenancy_id();
        Integer storeId = param.getStore_id();
        String source = param.getSource();

        List<?> paramList = param.getData();

        if (null == paramList || paramList.isEmpty())
        {
            throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
        }
        JSONObject paramJson = JSONObject.fromObject(paramList.get(0));

//        String mode = ParamUtil.getStringValueByObject(paramJson, "mode", true, PosErrorCode.NOT_NULL_MODE);
        Integer shiftId = ParamUtil.getIntegerValueByObject(paramJson, "shift_id", true, PosErrorCode.NOT_NULL_SHIFT_ID);
        Date reportDate = ParamUtil.getDateValueByObject(paramJson, "report_date", true, PosErrorCode.NOT_NULL_REPORT_DATE);
        String optNum = ParamUtil.getStringValueByObject(paramJson, "opt_num", true, PosErrorCode.NOT_NULL_OPT_NUM);
        String posNum = ParamUtil.getStringValueByObject(paramJson, "pos_num", true, PosErrorCode.NOT_EXISTS_POSNUM);
        String tableCode = ParamUtil.getStringValueByObject(paramJson, "table_code", false, null);
        String chanel = ParamUtil.getStringValueByObject(paramJson, "chanel", false, null);
//		String splitFlag = ParamUtil.getStringValueByObject(paramJson, "split_flag", false, null);
//		synchronized (tableCode){

        if (Tools.isNullOrEmpty(chanel))
        {
            chanel = SysDictionary.CHANEL_MD01;
        }


        // 报表日期校验
        openTableService.checkReportDate(tenantId, storeId, reportDate);

        // 班次ID校验
        shiftId = openTableService.checkShiftId(tenantId, storeId, reportDate, shiftId, optNum, posNum, source);
        String waiterNum = ParamUtil.getStringValueByObject(paramJson, "waiter_num", false, null);
        Integer guest = ParamUtil.getIntegerValueByObject(paramJson, "guest", false, null);
        Integer itemMenuId = ParamUtil.getIntegerValueByObject(paramJson, "item_menu_id", false, null);
        String preorderno = ParamUtil.getStringValueByObject(paramJson, "preorderno", false, null);
        String copyBillNum = ParamUtil.getStringValueByObject(paramJson, "copy_bill_num", false, null);
        String remark = ParamUtil.getStringValueByObject(paramJson, "remark", false, null);
        Double shopRealAmount = ParamUtil.getDoubleValueByObject(paramJson, "shop_real_amount", false, null);
        Double platformChargeAmount = ParamUtil.getDoubleValueByObject(paramJson, "platform_charge_amount", false, null);
        String settlementType = ParamUtil.getStringValueByObject(paramJson, "settlement_type", false, null);
        // 是否拆台
//        String splitFlag = ParamUtil.getStringValueByObject(paramJson, "split_flag", false, null);
        // 就餐类型
//        String dinnerType = ParamUtil.getStringValueByObject(paramJson, "dinner_type", false, null);
        if(!StringUtil.hasText(remark)){
            remark ="";
        }

        int discountModeId = 0;
        if (paramJson.containsKey("discount_mode_id"))
        {
            discountModeId = ParamUtil.getIntegerValueByObject(paramJson, "discount_mode_id", false, null);
        }

        double discountkAmount = 0d;
        if (paramJson.containsKey("discountk_amount"))
        {
            discountkAmount = ParamUtil.getDoubleValueByObject(paramJson, "discountk_amount", false, null);
        }

        double discountRate = 100d;
        if (paramJson.containsKey("discount_rate"))
        {
            discountRate = ParamUtil.getDoubleValueByObject(paramJson, "discount_rate", false, null);
        }

        String saleMode = ParamUtil.getStringValueByObject(paramJson, "sale_mode", false, null);
        if (Tools.isNullOrEmpty(saleMode))
        {
            saleMode = SysDictionary.SALE_MODE_TS01;
        }

        if (Tools.isNullOrEmpty(chanel))
        {
            chanel = SysDictionary.CHANEL_MD01;
        }

        if (Tools.isNullOrEmpty(itemMenuId))
        {
            itemMenuId = 0;
        }

        if (Tools.isNullOrEmpty(waiterNum))
        {
            waiterNum = optNum;
        }

        double serviceAmount = 0d;
        if (paramJson.containsKey("service_amount"))
        {
            serviceAmount = ParamUtil.getDoubleValueByObject(paramJson, "service_amount", false, null);
        }

        Integer serviceId = 0;
        if (paramJson.containsKey("service_id"))
        {
            serviceId = ParamUtil.getIntegerValueByObject(paramJson, "service_id", false, null);
        }

        // POS账单编号
        String billNum = "";
        // POS流水单号
        String serialNum = "";

        Timestamp time = com.tzx.pos.base.util.DateUtil.currentTimestamp();
        String bill_property = SysDictionary.BILL_PROPERTY_OPEN;

        //根据小程序out_order_id 与pos_bill 中order_num 查询账单号---小程序先付跳单问题
        JSONObject jsonObject = firstPaymentDao.getBillNumByOutOrderId(tenantId, storeId,microLifeOrder.getOut_order_id());
        if(jsonObject!=null){
            billNum = jsonObject.optString("bill_num");
            serialNum =jsonObject.optString("serial_num");
        }else{
            try
            {
                JSONObject object = new JSONObject();
                object.put("store_id", storeId);
                object.put("busi_date", reportDate);
                billNum = codeService.getCode(tenantId, Code.POS_BILL_CODE, object);// 调用统一接口来实现
                object.put("pos_num", posNum);
                serialNum = codeService.getCode(tenantId, Code.POS_RECORD_CODE, object); // 调用统一接口来实现
            }
            catch (Exception e)
            {
                LOG.info("开台：" + ExceptionMessage.getExceptionMessage(e));
                throw new SystemException(PosErrorCode.GET_BILLNUM_SERVICE_ERROR);
            }
        }
        serviceId = firstPaymentDao.getServiceIdByTablCode(tenantId, storeId,tableCode);
        if(isOrdering){
            firstPaymentDao.savePosBillBooked(tenantId, storeId, reportDate, shiftId, billNum, null, serialNum, preorderno, time, posNum, optNum, waiterNum, guest, itemMenuId, saleMode, chanel, serviceId, serviceAmount, copyBillNum, remark, bill_property, SysDictionary.PAYMENT_STATE_NOTPAY,
                    discountModeId, discountkAmount, shopRealAmount, platformChargeAmount, settlementType, discountRate,tableCode);
        }else {
            firstPaymentDao.savePosBill(tenantId, storeId, reportDate, shiftId, billNum, null, serialNum, preorderno, time, posNum, optNum, waiterNum, guest, itemMenuId, saleMode, chanel, serviceId, serviceAmount, copyBillNum, remark, bill_property, SysDictionary.PAYMENT_STATE_NOTPAY,
                    discountModeId, discountkAmount, shopRealAmount, platformChargeAmount, settlementType, discountRate,tableCode);
        }




        String str0 = new String("insert into pos_bill_waiter (tenancy_id,store_id,bill_num,waiter_num,report_date) values (?,?,?,?,?)");
        firstPaymentDao.update(str0, new Object[]
                { tenantId, storeId, billNum, waiterNum, reportDate });

        firstPaymentDao.savePosLog(tenantId, storeId, posNum, optNum, optNum, shiftId, reportDate, Constant.TITLE, "开台", "桌位编号:" + tableCode, "账单编号:" + billNum);
        return  billNum;
    }
    private void setMemberDiscount(JSONObject member, String tenancyId, int storeId, String billNum, JSONObject billObject,boolean isOrdering) throws Exception {

        if(!"0".equals(billObject.optString("discount_mode_id"))  ){
            //如果账单有折扣，直接上传dingd
            LOG.info("账单有折扣，直接上传预结订单>>>>");
        }else {
            //查询是否启用微生活会员
            String memberType = firstPaymentDao.getMemberType(tenancyId);
            if (null != memberType && SysDictionary.CUSTOMER_TYPE_ACEWILL.equals(memberType)){
                //调用查询会员接口,如果有会员折扣或者会员价，调用账单折扣，否则直接上传
                String cno = null;
                if (null != member)
                {
                    // 会员卡编号
                    cno = member.optString("cno");
                }

                if (Tools.hv(cno))
                {
                    JSONObject obj = new JSONObject();
                    obj.put("card_code", cno);
                    List<JSONObject> dataList = new ArrayList<>();
                    dataList.add(obj);

                    Data requestData = new Data();
                    requestData.setStore_id(storeId);
                    requestData.setTenancy_id(tenancyId);
                    requestData.setData(dataList);

                    Data customerUserInfo = acewillCustomerService.getAcewillCustomerUserInfo(requestData);
                    LOG.info("查询会员返回信息：" + JSON.toJSONString(customerUserInfo));
                    List<JSONObject> userList = (List<JSONObject>) customerUserInfo.getData();
                    if (null != userList && userList.size() > 0){
                        //折扣率，0-100
                        Integer rate = userList.get(0).optInt("rate");
                        //是否会员价，0为否，1为是
                        String is_vipprice = userList.get(0).optString("is_vipprice");
                        //账单折扣是走会员折扣
                        if (rate > 0 && rate < 100){
                            billDiscount(tenancyId, storeId, rate,billNum,member,isOrdering);
                        }else if (StringUtil.hasText(is_vipprice) && "1".equals(is_vipprice)){
                            //账单折扣走会员价
                            billDiscount(tenancyId, storeId,null,billNum,member,isOrdering);
                        }else {
                            LOG.info("微生活会员既没折扣也不走会员价，直接上传预结订单>>>>");
                            billDiscount(tenancyId, storeId,100,billNum,member,isOrdering);
                        }
                    }else {
                        billDiscount(tenancyId, storeId,100,billNum,member,isOrdering);
                    }
                }
            }else {
                LOG.info("未开启微生活会员，直接上传预结订单>>>>");
                billDiscount(tenancyId, storeId,100,billNum,member,isOrdering);
            }
        }
    }

    private void billDiscount(String tenancyId, int storeId, Integer rate,String billNum,JSONObject memberJson,boolean isOrdering) throws Exception {
        //调用账单折扣
        Data data= new Data();
        data.setType(Type.BILL_DISCOUNT);
        data.setOper(Oper.add);
        data.setTenancy_id(tenancyId);
        data.setStore_id(storeId);
        List<JSONObject> list = getDiscountParam(tenancyId, storeId, rate,billNum,memberJson,isOrdering);
        data.setData(list);
        if(isOrdering){
            firstPaymentCalAmountService.calBillDiscount(data);
        }else {
            posDiscountService.newBillDiscount(data);
        }


    }

    //查询会员折扣需要的参数
	private List<JSONObject> getDiscountParam(String tenancyId, int storeId, Integer rate, String billNum, JSONObject memberJson, boolean isOrdering) throws Exception {
        List<JSONObject> list = new ArrayList<>();
        JSONObject object;
        if(isOrdering){
            object = firstPaymentDao.getBillDiscount(tenancyId, storeId, billNum);
        }else {
            object = firstPaymentDao.getBillDiscountForPay(tenancyId, storeId, billNum);
        }

        if (null == object)
            return null;
        //0是取消折扣，1是账单折扣
        object.put("mode", 1);
        //操作员号
        object.put("opt_num", "");
        //批准人编号
        object.put("manager_num", "");
        //折让金额
        object.put("discountr_amount", 0);
        //折让方式
        if (null != rate){
            //会员折扣
            object.put("discount_mode", SysDictionary.DISCOUNT_MODE_5);
            object.put("discount_rate", rate);
        }else {
            //会员价
            object.put("discount_mode", SysDictionary.DISCOUNT_MODE_6);
        }
        object.put( "discount_reason_id", "0");
        
        object.put( "card_code", memberJson.optString("cno"));
		// 手机号
        object.put( "mobil", memberJson.optString("mobil"));
		// 会员编号
        object.put( "customer_code", memberJson.optString("cno"));
		// 会员名称
        object.put( "customer_name", memberJson.optString("name"));
		// 本次消费前积分
        object.put( "customer_credit", memberJson.optDouble("credit",0d));
        object.put( "main_balance", memberJson.optDouble("balance",0d));
        object.put( "reward_balance", 0d);
        list.add(object);
        return list;
    }

    private AcewillPosBillPayment createPosPayment(String tenancyId, Integer storeId, AcewillPayWLife wlife, AcewillPosBill posBill,
                                                       AcewillPaymentWay paymentWay, AcewillPayInfo payInfo) throws Exception {

        AcewillPosBillPayment posBillPayment = new AcewillPosBillPayment();
        posBillPayment.setTenancy_id(tenancyId);
        posBillPayment.setStore_id(storeId);
        posBillPayment.setId(null);
        posBillPayment.setBill_num(posBill.getBill_num());
        posBillPayment.setTable_code(posBill.getTable_code());
        posBillPayment.setType(paymentWay.getPayment_class());
        posBillPayment.setJzid(paymentWay.getId());
        posBillPayment.setName(paymentWay.getPayment_name1());
        posBillPayment.setName_english(paymentWay.getPayment_name2());
        posBillPayment.setAmount(payInfo.getAmount());
        posBillPayment.setCount(1);
        // 付款号码
        if (wlife != null && (SysDictionary.PAYMENT_CLASS_WLIFE_BALANCE.equals(paymentWay.getPayment_class()) || SysDictionary.PAYMENT_CLASS_WLIFE_CREDIT.equals(paymentWay.getPayment_class()))) {
            posBillPayment.setNumber(wlife.getCno());
            posBillPayment.setPhone(wlife.getMobil());
        }
        posBillPayment.setReport_date(com.tzx.pos.base.util.DateUtil.parseDate(posBill.getReport_date()));
        posBillPayment.setShift_id(posBill.getShift_id());
        posBillPayment.setPos_num(posBill.getPos_num());
        // 收款员号
        posBillPayment.setCashier_num(posBill.getCashier_num());
        posBillPayment.setLast_updatetime(new Date());
        posBillPayment.setIs_ysk("N");
        posBillPayment.setRate(1d);
        posBillPayment.setCurrency_amount(payInfo.getAmount());
        // 上传标记
        posBillPayment.setUpload_tag(0);
        // 挂账人id
        posBillPayment.setCustomer_id(null);
        // 付款流水号
        posBillPayment.setBill_code(payInfo.getSerilNo());
        posBillPayment.setRemark(null);
        posBillPayment.setPayment_state(SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
        // 与总部通讯的缓存
        posBillPayment.setParam_cach(null);
        // 批次编号
        posBillPayment.setBatch_num(posBill.getBatch_num());
        // 多收礼卷
        posBillPayment.setMore_coupon(0d);
        posBillPayment.setFee(0d);
        posBillPayment.setFee_rate(null);
        // 优惠券类型
        String coupon_type = posBillCouponTypeMapping.get(payInfo.getSource());
        posBillPayment.setCoupon_type(coupon_type != null ? Integer.parseInt(coupon_type) : null);
        // 原付款方式ID
        posBillPayment.setYjzid(null);

        Double couponBuyPrice = (payInfo.getStorepay() != null ? payInfo.getStorepay() : 0.00);
        if (WLifeConstant.PAY_SOURCE_ALIPAY.equals(payInfo.getSource())) {
            couponBuyPrice = (payInfo.getStorepay() != null ? payInfo.getStorepay() / 100 : 0.00);
        }
        if (WLifeConstant.PAY_SOURCE_BALANCE.equals(payInfo.getSource())) {
            couponBuyPrice = (payInfo.getStorepay() != null ? payInfo.getStorepay() / 100 : 0.00);
        } else if (WLifeConstant.PAY_SOURCE_CREDIT.equals(payInfo.getSource()) || WLifeConstant.PAY_SOURCE_COUPON.equals(payInfo.getSource()) || WLifeConstant.PAY_SOURCE_PRODUCT.equals(payInfo.getSource())) {
            couponBuyPrice = 0d;
        }
        Double due = couponBuyPrice;
        Double tenancyAssume = payInfo.getAmount() - couponBuyPrice;
        Double thirdAssume = 0d;
        Double thirdFee = 0d;

        // 用户实付
        posBillPayment.setCoupon_buy_price(couponBuyPrice);
        // 券账单净收
        posBillPayment.setDue(due);
        // 商家优惠承担
        posBillPayment.setTenancy_assume(tenancyAssume);
        // 第三方优惠承担
        posBillPayment.setThird_assume(thirdAssume);
        // 第三方票券服务费
        posBillPayment.setThird_fee(thirdFee);
        return posBillPayment;
    }

    private void splitPayment(String tenancyId,  String billNum,String paymentUid, Double couponBuyPrice,Double due,Double tenancyAssume,String type) throws Exception {

        String phantom = "";
        switch (type){
            case SysDictionary.PAYMENT_CLASS_WLIFE_MEITUAN:
                phantom=SysDictionary.PAYMENT_CLASS_WLIFE_PHANTOM_MEITUAN;
                break;
            case SysDictionary.PAYMENT_CLASS_WLIFE_DOUYIN:
                phantom=SysDictionary.PAYMENT_CLASS_WLIFE_PHANTOM_DOUYIN;
                break;
            case SysDictionary.PAYMENT_CLASS_WLIFE_ALIPAYCOUPON:
                phantom=SysDictionary.PAYMENT_CLASS_WLIFE_PHANTOM_ALIPAYCOUPON;
                break;
            case SysDictionary.PAYMENT_CLASS_WLIFE_LITTLE_RED_BOOK:
                phantom=SysDictionary.PAYMENT_CLASS_WLIFE_PHANTOM_LITTLE_RED_BOOK;
                break;
            default:
                return;
        }

        List<JSONObject> payments = firstPaymentDao.query4Json(tenancyId, "select * from pos_bill_payment where payment_uid ='" + paymentUid + "' and bill_num='" + billNum + "'");

        if (CollectionUtils.isNotEmpty(payments)) {

            if (paymentJzidMap.isEmpty()||null==paymentJzidMap.get(phantom)) {
                List<JSONObject> paymentWays = firstPaymentDao.query4Json(tenancyId, "select id,payment_class,payment_name1 from payment_way where payment_class = '" + phantom + "'");
                if (CollectionUtils.isEmpty(paymentWays)) {
                    LOG.warn(type+"虚收付款款方式不存在");
                    throw SystemException.getInstance(PosErrorCode.NOT_EXIST_JZID);
                }

                for (JSONObject paymentWay : paymentWays) {
                    paymentJzidMap.put(paymentWay.optString("payment_class"), paymentWay);
                }
            }

            List<JSONObject> paymentsPhantoms = new ArrayList<JSONObject>();

            for (JSONObject payment : payments) {

                JSONObject paymentPhantom = null;

                String paymentType = payment.optString("type");

                double oldTenancyAssume = payment.optDouble("tenancy_assume", 0);

                if (oldTenancyAssume == 0) {
                    continue;
                }
                paymentPhantom = JSONObject.fromObject(payment);

                paymentPhantom.put("type", type+"_phantom");
                paymentPhantom.put("jzid", paymentJzidMap.get(phantom).optInt("id"));
                paymentPhantom.put("name", paymentJzidMap.get(phantom).optString("payment_name1"));
                paymentPhantom.put("name_english", paymentJzidMap.get(phantom).optString("payment_name1"));
                paymentPhantom.put("amount", tenancyAssume);
                paymentPhantom.put("currency_amount", tenancyAssume);
                paymentPhantom.put("coupon_buy_price", 0);
                paymentPhantom.put("due", 0);
                paymentPhantom.put("tenancy_assume", tenancyAssume);

                paymentsPhantoms.add(paymentPhantom);

                payment.put("amount", due);
                payment.put("currency_amount", due);
                payment.put("coupon_buy_price", couponBuyPrice);
                payment.put("due", due);
                payment.put("tenancy_assume", 0);


            }
            firstPaymentDao.insertBatchIgnorCase(tenancyId, "pos_bill_payment", paymentsPhantoms);
            firstPaymentDao.updateBatchIgnorCase(tenancyId, "pos_bill_payment", payments);
        }
    }
}
