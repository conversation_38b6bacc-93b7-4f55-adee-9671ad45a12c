package com.tzx.clientorder.wlifeprogram.service.rest;

import com.tzx.clientorder.wlifeprogram.bo.BasicService;
import com.tzx.clientorder.wlifeprogram.bo.WlifeService;
import com.tzx.clientorder.wlifeprogram.common.constant.WlifePromptMessage;
import com.tzx.clientorder.wlifeprogram.common.util.ExceptionPrintUtil;
import com.tzx.framework.common.exception.OtherSystemException;
import com.tzx.framework.common.exception.SystemException;
import net.sf.json.JSONObject;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 提供微生活小程序的接口(基础资料、沽清信息)
 * Created by qingui on 2018-06-27.
 */
@Controller("WlifeBasicRest")
@RequestMapping("/wlife/basicRest")
public class WlifeBasicRest {

    private static final Logger logger = Logger.getLogger(WlifeBasicRest.class);
    @Resource(name = BasicService.NAME)
    private BasicService basicService;
    @Resource(name = WlifeService.NAME)
    private WlifeService wlifeService;

    /**
     * 查询基础资料
     * @param request
     */
    @RequestMapping(value = "/getBaseData",method = RequestMethod.POST)
    @ResponseBody
    public JSONObject getBaseData(HttpServletRequest request, @RequestBody JSONObject jsobj){

    	long t1 = System.currentTimeMillis();
    	logger.info("小程序调用基础资料接口请求=="+String.valueOf(t1)+">>"+jsobj.toString());

        String tenancyId = null;
        if(com.tzx.framework.common.constant.Constant.systemMap.containsKey("tenent_id"))
        {
            tenancyId = com.tzx.framework.common.constant.Constant.systemMap.get("tenent_id");
        }

        Integer storeId = Integer.valueOf(com.tzx.framework.common.constant.Constant.systemMap.get("store_id"));
        if(com.tzx.framework.common.constant.Constant.systemMap.containsKey("store_id"))
        {
            storeId = Integer.valueOf(com.tzx.framework.common.constant.Constant.systemMap.get("store_id"));
        }

        jsobj.put("tenancyId", tenancyId);
        jsobj.put("storeId", storeId);

        //拉取基础资料信息
        JSONObject responseJson = new JSONObject();
        try
        {
            basicService.getBasicInfo(responseJson, jsobj,String.valueOf(t1));
        }
        catch (OtherSystemException se)
        {
            ExceptionPrintUtil.buildOtherSysExceptionData(logger, se, responseJson, WlifePromptMessage.WLIFE_BASIC_FAILURE);
        }
        catch (SystemException se)
        {
            ExceptionPrintUtil.buildSysExceptionData(logger, se, responseJson, WlifePromptMessage.WLIFE_BASIC_FAILURE);
        }
        catch (Exception e)
        {
            ExceptionPrintUtil.buildExceptionData(logger, e, responseJson, WlifePromptMessage.WLIFE_BASIC_FAILURE);
        }
        String logStr = responseJson.toString();
        logStr = logStr.length() > 1000 ? logStr.substring(0, 1000) : logStr;
        logger.info("查询基础资料响应出参==耗时"+(System.currentTimeMillis()-t1)+">>" + logStr);
        return responseJson;
    }

    /**
     * 查询沽清信息
     * @param request
     */
    @RequestMapping(value = "/getSoldOut")
    @ResponseBody
    public JSONObject getSoldOut(HttpServletRequest request){

        JSONObject responseJson = new JSONObject();

        String tenancyId = null;
        if(com.tzx.framework.common.constant.Constant.systemMap.containsKey("tenent_id"))
        {
            tenancyId = com.tzx.framework.common.constant.Constant.systemMap.get("tenent_id");
        }

        Integer storeId = Integer.valueOf(com.tzx.framework.common.constant.Constant.systemMap.get("store_id"));
        if(com.tzx.framework.common.constant.Constant.systemMap.containsKey("store_id"))
        {
            storeId = Integer.valueOf(com.tzx.framework.common.constant.Constant.systemMap.get("store_id"));
        }
        JSONObject jsobj = new JSONObject();
        jsobj.put("tenancyId", tenancyId);
        jsobj.put("storeId", storeId);

        logger.info("小程序调用沽清信息接口请求");

        //查询门店菜品的沽清信息
        try
        {
            basicService.getSoldOutInfo(responseJson, jsobj);
        }
        catch (OtherSystemException se)
        {
            ExceptionPrintUtil.buildOtherSysExceptionData(logger, se, responseJson, WlifePromptMessage.WLIFE_SOLD_OUT_FAILURE);
        }
        catch (SystemException se)
        {
            ExceptionPrintUtil.buildSysExceptionData(logger, se, responseJson, WlifePromptMessage.WLIFE_SOLD_OUT_FAILURE);
        }
        catch (Exception e)
        {
            ExceptionPrintUtil.buildExceptionData(logger, e, responseJson, WlifePromptMessage.WLIFE_SOLD_OUT_FAILURE);
        }
        logger.info("查询沽清信息响应出参===" + responseJson.toString());
        return responseJson;
    }

    /**
     * 查询沽清信息
     * @param request
     * @param jsobj
     */
    /*@RequestMapping(value = "/test", method = RequestMethod.POST)
    @ResponseBody
    public JSONObject test(HttpServletRequest request, @RequestBody JSONObject jsobj){

        JSONObject responseJson = new JSONObject();

        logger.info("请求入参===" + jsobj.toString());

        String tenancyId = null;
        if(com.tzx.framework.common.constant.Constant.systemMap.containsKey("tenent_id"))
        {
            tenancyId = com.tzx.framework.common.constant.Constant.systemMap.get("tenent_id");
        }

        Integer storeId = Integer.valueOf(com.tzx.framework.common.constant.Constant.systemMap.get("store_id"));
        if(com.tzx.framework.common.constant.Constant.systemMap.containsKey("store_id"))
        {
            storeId = Integer.valueOf(com.tzx.framework.common.constant.Constant.systemMap.get("store_id"));
        }
        jsobj.put("tenancyId", tenancyId);
        jsobj.put("storeId", storeId);

        //查询门店菜品的沽清信息
        try
        {
            wlifeService.completeOrder(tenancyId, storeId, jsobj.optString("out_order_id"));
        }
        catch (OtherSystemException se)
        {
            ExceptionPrintUtil.buildOtherSysExceptionData(logger, se, responseJson, Constant.WLIFE_SOLD_OUT_FAILURE);
        }
        catch (SystemException se)
        {
            ExceptionPrintUtil.buildSysExceptionData(logger, se, responseJson, Constant.WLIFE_SOLD_OUT_FAILURE);
        }
        catch (Exception e)
        {
            ExceptionPrintUtil.buildExceptionData(logger, e, responseJson, Constant.WLIFE_SOLD_OUT_FAILURE);
        }
        logger.info("响应出参===" + responseJson.toString());
        return responseJson;
    }*/
}
