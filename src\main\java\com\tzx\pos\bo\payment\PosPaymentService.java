package com.tzx.pos.bo.payment;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import net.sf.json.JSONObject;

import com.tzx.framework.common.entity.Data;
import com.tzx.pos.bo.PosBaseService;

/**
 * 支付方式
 */
public interface PosPaymentService extends PosBaseService
{
	String	NAME	= "com.tzx.pos.bo.payment.imp.PosPaymentServiceImp";

	/**
	 * 统一结账接口
	 * 
	 * @param param
	 * @param result
	 * @param printJson
	 *            打印参数
	 * @param isCheckKssy
	 * @throws Exception
	 */
	public void posBillPayment(Data param, Data result, JSONObject printJson, boolean isCheckKssy) throws Exception;

	/**
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void getPosBillPaymentByBillNum(Data param, Data result) throws Exception;
	
	/**
	 * 取消付款
	 * 
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void cancelPayment(Data param, Data result) throws Exception;

	/**
	 * 强制付款完成
	 * 
	 * @param param
	 * @param result
	 * @param printJson
	 * @throws Exception
	 */
	@Deprecated
	public void paymentComplete(Data param, Data result, JSONObject printJson) throws Exception;

	/**
	 * 账单付款状态查询
	 * 
	 * @param param
	 * @param result
	 * @param printJson
	 * @throws Exception
	 */
	public void paymentStateQuery(Data param, Data result, JSONObject printJson) throws Exception;

	/**
	 * 付款重试
	 * 
	 * @param param
	 * @param result
	 * @param printJson
	 * @throws Exception
	 */
	@Deprecated
	public void paymentRepeat(Data param, Data result, JSONObject printJson) throws Exception;

	/**
	 * 退款
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param paramJson
	 * @throws Exception
	 */
	public void paymentRefund(String tenancyId, int storeId, JSONObject paramJson, Data result, String operType) throws Exception;

	/** 恢复账单退款
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param paymentAmount
	 * @param tableCode
	 * @param reportDate
	 * @param shiftId
	 * @param posNum
	 * @param optNum
	 * @param optName
	 * @param chanel
	 * @param orderNum
	 * @param regainCount
	 * @throws Exception
	 */
	public Data paymentRegain(String tenancyId, int storeId, String billNum, double paymentAmount, String tableCode, Date reportDate, int shiftId, String posNum, String optNum, String optName,String chanel,String orderNum,int regainCount) throws Exception;
	
	/**
	 * 预付款
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param paramList
	 * @throws Exception
	 */
	public void beforehandBillPayment(String tenancyId, int storeId, List<?> paramList, String source, Data result) throws Exception;

	/**
	 * 获取二维码
	 * 
	 * @param params
	 * @param path
	 * @return
	 * @throws Exception
	 */
	public Data precreateThirdPayment(Data params, String path) throws Exception;

	/**
	 * 扫码支付
	 * 
	 * @throws Exception
	 */
	public Data barcodeThirdPayment(Data params, JSONObject jsobj) throws Exception;

	/**
	 * 第三方支付状态查询
	 * 
	 * @throws Exception
	 */
	@Deprecated
	public Data queryThirdPayment(Data params, JSONObject printJson) throws Exception;

	/**
	 * 取消第三方支付
	 * 
	 * @throws Exception
	 */
	@Deprecated
	public Data cancelThirdPayment(Data params) throws Exception;

	/**
	 * 第三方支付退款
	 * 
	 * @throws Exception
	 */
	@Deprecated
	public Data refundThirdPayment(Data params) throws Exception;

	/**
	 * 第三方支付退款状态查询
	 * 
	 * @throws Exception
	 */
	@Deprecated
	public Data queryRefundPayment(Data params) throws Exception;

	/**
	 * 第三方优惠劵验证
	 * 
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void prepareThirdCoupons(Data param, Data result) throws Exception;


    /***
     * 本地验券
     * @param param
     * @param result
     * @throws Exception
     */
    void checkCoupons(Data param, Data result) throws Exception;


	/**
	 * 异常支付流水查询
	 * 
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void wrongPayDetailQuery(Data param, Data result) throws Exception;

//	/**
//	 * @param param
//	 * @param result
//	 * @param printJson
//	 * @throws Exception
//	 */
//	public void thirdPaymentSuccess(Data param, Data result, JSONObject printJson) throws Exception;

//	/**
//	 * @param tenancyId
//	 * @param storeId
//	 * @param paramJson
//	 * @throws Exception
//	 */
//	public void thirdPaymentFailure(String tenancyId, int storeId, JSONObject paramJson) throws Exception;

	/** 修改账单税价分离
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param saleMode
	 * @throws Exception
	 */
	public void updatePosBillTaxPriceSeparation(String tenancyId, int storeId, JSONObject paramJson) throws Exception;
	
	/**
	 * 预打单打印
	 * 
	 * @param paraData
	 * @param resultData
	 * @throws Exception
	 */
	public void pretrPrintBill(Data paraData, String webPath, Data resultData) throws Exception;

	/**
	 * 更新 pos_bill_payment, pos_bill 表中的 净收
	 * @param billNum
	 * @param tenancyId
	 * @param storeId
	 * @throws Exception
	 */
	public void updateBillDue(String billNum, String tenancyId, Integer storeId) throws Exception;

	/**
	 * 关闭账单
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param reportDate
	 * @param shiftId
	 * @param posNum
	 * @param optNum
	 * @param isPrint
	 * @param isInvoice
	 * @param resultJson
	 * @param printJson
	 * @param isOnlinePay
	 * @param currentTime
	 * @throws Exception
	 */
	void closedAcewillPosBill(String tenancyId, int storeId, String billNum, String reportDate, int shiftId,
			String posNum, String optNum, String isPrint, String isInvoice, JSONObject resultJson, JSONObject printJson,
			String isOnlinePay, Timestamp currentTime) throws Exception;

	/** 轮询结果处理
	 * @param tenancyId
	 * @param storeId
	 * @param para
	 * @param printJson
	 * @throws Exception
	 */
	void paymentResultByQuery(String tenancyId, int storeId, JSONObject para, JSONObject printJson) throws Exception;
	
	public void billPaymentService(Data param, Data result, JSONObject printJson) throws Exception;

	/** 生成KVS记录
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param reportDate
	 * @throws Exception
	 */
	public void writeKvsBill(String tenancyId, int storeId,String billNum, String reportDate) throws Exception;


	/**
	 * 手动关闭账单
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param reportDate
	 * @param shiftId
	 * @param posNum
	 * @param optNum
	 * @param isPrint
	 * @param isInvoice
	 * @param saleMode
	 * @param printJson
	 * @param isOnlinePay
	 * @throws Exception
	 */
	public void manualClosedPosBill(Data param, Data result, JSONObject printJson) throws Exception;


	/**
	 * 并台原账单零结
	 * @param tenancyId
	 * @param storeId
	 * @param sbill_num
	 * @param reportDate
	 * @param shiftId
	 * @param posNum
	 * @param optNum
	 * @param currentTime
	 * @throws Exception
	 */
	public void combineTableZeroPayment(String tenancyId, int storeId, String sbill_num, Date reportDate, int shiftId, String posNum, String optNum,Timestamp currentTime) throws Exception;
}
