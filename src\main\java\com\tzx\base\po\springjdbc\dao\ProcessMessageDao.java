package com.tzx.base.po.springjdbc.dao;

import java.util.List;
import java.util.Map;

import net.sf.json.JSONObject;

import com.tzx.framework.common.util.dao.GenericDao;

/**
 * 
 * <AUTHOR> 2015年8月3日-上午10:07:42
 */
public interface ProcessMessageDao extends GenericDao
{
	String	NAME	= "com.tzx.base.po.springjdbc.dao.imp.ProcessMessageDaoImp";

	/**
	 * 初始化下发
	 * 
	 * @param sql
	 * @return
	 */
	public int[] batchInsert(String sql[]) throws Exception;

	/**
	 * 批量更新版本
	 * 
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public int[] batchUpdateVersion(String tenancyId, int storeId, List<JSONObject> param) throws Exception;

	/**
	 * 获取门店参数
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param para
	 * @return
	 * @throws Exception
	 */
	public String getSysParameter(String tenancyId, int storeId, String para) throws Exception;

	/**
	 * 获取菜品图片
	 * 
	 * @param tenancyId
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getItemPhoto(String tenancyId) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @throws Exception
	 */
	public void updateEmployeeForStoreId(String tenancyId,int storeId) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @throws Exception
	 */
	public void updateUserForStoreId(String tenancyId,int storeId) throws Exception;
	
	/**
	 * 获取菜品图片
	 * 
	 * @param tenancyId
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getViceScreenPhoto(String tenancyId,int storeid) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param parameter
	 * @throws Exception
	 */
	public void insertSysParameter(String tenancyId,int storeId,List<Map<String,Object>> parameterList) throws Exception;

	/**
	  * @Description: 查询本地DB是否存在对应表的版本号
	  * @Title:findTableNameId
	  * @param:@param tableName
	  * @param:@return
	  * @return: int
	  * @author: zhouquan
	  * @version: 1.0
	  * @Date: 2018年4月11日
	*/
	public List<JSONObject> findTableNameId(String tableName);

	/**
	  * @Description: 将下发的表和表的版本号存入本地DB
	  * @Title:insertTableNameAndVersion
	  * @param:@param tableName
	  * @param:@param tableVersion
	  * @return: void
	  * @author: zhouquan
	  * @version: 1.0
	 * @param time 
	 * @param tableVersion2 
	 * @param storeId 
	  * @Date: 2018年4月11日
	*/
	public String insertTableNameAndVersion(String tenancyId ,int storeId, String tableName,  String tableVersion,String time,String recordCount);

	/**
	  * @Description: 在本地DB修改下发的表和表的版本号
	  * @Title:updateTableNameAndVersion
	  * @param:@param tableName
	  * @param:@param tableVersion
	  * @return: void
	  * @author: zhouquan
	  * @version: 1.0
	  * @Date: 2018年4月11日
	*/
	public String updateTableNameAndVersion(String tenancyId ,int storeId, String tableName,  String tableVersion,String time,String recordCount);
	
	/*
	 * 查询序列是否存在
	 */
	public String ifExistSequenceOrTable(String sequenceName) throws Exception;
	
	/*
	 * 执行查询（初始化序列值）
	 */
	public String initSeqValue(String sql) throws Exception;
}
