package com.tzx.pos.base.util;

import java.sql.Time;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

import net.sf.json.JSONNull;
import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;

public class DateUtil {
//    private static SimpleDateFormat (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")) = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//    private static SimpleDateFormat (new SimpleDateFormat("HH:mm:ss")) = new SimpleDateFormat("HH:mm:ss");
//    public static SimpleDateFormat (new SimpleDateFormat("yyyy-MM-dd")) = new SimpleDateFormat("yyyy-MM-dd");
//    private static SimpleDateFormat (new SimpleDateFormat("HH:mm")) = new SimpleDateFormat("HH:mm");
//    private static SimpleDateFormat (new SimpleDateFormat("yyyy-MM")) = new SimpleDateFormat("yyyy-MM");
//    private static SimpleDateFormat (new SimpleDateFormat("yyyy年MM月")) = new SimpleDateFormat("yyyy年MM月");
//    private static SimpleDateFormat (new SimpleDateFormat("yyyy")) = new SimpleDateFormat("yyyy");
//    private static SimpleDateFormat (new SimpleDateFormat("yyyyMM-dd")) = new SimpleDateFormat("yyyyMM-dd");
//    private static SimpleDateFormat (new SimpleDateFormat("HHmmss")) = new SimpleDateFormat("HHmmss");
//    private static SimpleDateFormat (new SimpleDateFormat("yyyyMMddHHmmss")) = new SimpleDateFormat("yyyyMMddHHmmss");
    
    /**
     * format date just only for MQ generate insert SQL sentences。
     *
     * @param
     * @return
     */
    public static String formatForMQ(Object date) {
        if (null == date) {
            return "null";
        } else {
            return String.format("'%s'", format(date));
        }
    }

    /** 格式化日期为字符串
     * @param d
     * @return
     */
    public static String format(Object d) {
        if (d == null || d instanceof JSONNull) {
            return "";
        }

        if (d instanceof Timestamp) {
            return (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).format((Timestamp) d);
        } else if (d instanceof Time) {
            return (new SimpleDateFormat("HH:mm:ss")).format((Time) d);
        } else if (d instanceof Date) {
            return (new SimpleDateFormat("yyyy-MM-dd")).format((Date) d);
        }

        return d.toString();
    }
    
    /** 格式化日期 返回 yyyy-MM-dd
	 * @param d
	 * @return
	 */
	public static String formatDate(Object d)
	{
		if (d == null || d instanceof JSONNull)
		{
			return "";
		}

		return (new SimpleDateFormat("yyyy-MM-dd")).format((Date) d);
	}

    /** 格式化日期为字符串
     * @param d
     * @param partten 日期格式
     * @return
     */
    public static String format(Object d, String partten) {
        if (d == null || d instanceof JSONNull) {
            return "";
        }

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(partten);

        return simpleDateFormat.format(d);
    }

    /**
     * 格式化日期为字符串,返回 HH:mm:ss
     *
     * @param d
     * @return
     */
    public static String formatTime(Object d) {
        if (d == null || d instanceof JSONNull) {
            return "";
        }

        return (new SimpleDateFormat("HH:mm:ss")).format(d);
    }

    /** 字符串转换为日期,格式:yyyy-MM-dd
     * @param s
     * @return
     */
    public static Date parseDate(String s) {
        if (StringUtils.isEmpty(s)) {
            return null;
        }

        try {
            return (new SimpleDateFormat("yyyy-MM-dd")).parse(s);
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 获取当前日期 yyyy-MM-dd HH:mm:ss
     *
     * @return Timestamp
     */
    public static Timestamp currentTimestamp() {
        String str = (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).format(new Date());
        try {
            return new Timestamp((new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).parse(str).getTime());
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }
    
    /** 
     * 获取当前日期 yyyy-MM-dd HH:mm:ss
     *
     * @return Timestamp
     */
    public static Timestamp getNowTimestamp() {
    	
    	SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String str = dateFormat.format(new Date());
        try {
            return new Timestamp(dateFormat.parse(str).getTime());
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 获取当前日期 yyyy-MM-dd
     *
     * @return Timestamp
     */
    public static Timestamp limit6Timestamp() {
        String str = (new SimpleDateFormat("yyyy-MM-dd")).format(new Date());
        try {
            return new Timestamp((new SimpleDateFormat("yyyy-MM-dd")).parse(str).getTime());
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 字符串转换为日期,根据格式返回Timestamp,如果格式为空返回yyyy-MM-dd HH:mm:ss
     *
     * @param s
     * @param format 日期格式
     * @return Timestamp
     */
    public static Timestamp formatTimestamp(String s, String format) {
        if (s == null) {
            return null;
        }

        try {
            if (StringUtils.isEmpty(format)) {
                return new Timestamp((new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).parse(s).getTime());
            } else if ("yyyy-MM-dd HH:mm:ss".equalsIgnoreCase(format)) {
                return new Timestamp((new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).parse(s).getTime());
            } else if ("yyyy-MM-dd".equalsIgnoreCase(format)) {
                return new Timestamp((new SimpleDateFormat("yyyy-MM-dd")).parse(s).getTime());
            } else {
                return new Timestamp(System.currentTimeMillis());
            }
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 字符串转换为日期,返回带格式的Timestamp，格式为：yyyy-MM-dd HH:mm:ss
     *
     * @param s
     * @return Timestamp
     */
    public static Timestamp formatTimestamp(String s) {
        if (s == null) {
            return null;
        }

        try {
            return new Timestamp((new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).parse(s).getTime());
        } catch (ParseException e) {
            return null;
        }
    }

    /** 字符串转换为日期,返回带格式的Timestamp，格式为：yyyy-MM-dd HH:mm:ss
     * @param s
     * @return
     */
    public static Timestamp parseTimestamp(String s) {
        if (s == null) {
            return null;
        }

        try {
            return new Timestamp((new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).parse(s).getTime());
        } catch (ParseException e) {
            return null;
        }
    }

    /** 字符串转换为日期,返回带格式的Time，格式为：HH:mm:ss
     * @param s
     * @return
     */
    public static Time parseTime(String s) {
        if (s == null) {
            return null;
        }

        try {
            return new Time((new SimpleDateFormat("HH:mm:ss")).parse(s).getTime());
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 字符串转换为日期,返回带格式的date，格式为：yyyy-MM-dd HH:mm:ss
     *
     * @param s
     * @return
     */
    public static Date parseDateAll(String s) {
        if (s == null) {
            return null;
        }

        try {
            return (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).parse(s);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 比较两个时间点的大小
     *
     * @param t1
     * @param t2
     * @return
     */
    public static int timeCompare(String t1, String t2) {
        SimpleDateFormat formatter = new SimpleDateFormat("HH:mm:ss");
        Calendar c1 = Calendar.getInstance();
        Calendar c2 = Calendar.getInstance();
        try {
            c1.setTime(formatter.parse(t1));
            c2.setTime(formatter.parse(t2));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        int result = c1.compareTo(c2);
        return result;
    }
    
    /**
     * 比较两个时间点的大小
     *
     * @param t1
     * @param t2
     * @return
     *  0  t1等于t2
     * -1  t1小于t2
     *  1  t1大于t2
     */
    public static int timestampCompare(String t1, String t2) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar c1 = Calendar.getInstance();
        Calendar c2 = Calendar.getInstance();
        try {
            c1.setTime(formatter.parse(t1));
            c2.setTime(formatter.parse(t2));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        int result = c1.compareTo(c2);
        return result;
    }

    /**
     * 比较两个时间的时间差(打印)
     *
     * @param date1
     * @param date2
     * @param notes 注释
     * @return 打印时间差(n天n小时n分n秒n毫秒), 返回大的时间Date格式
     * <p>
     * <br>
     * 主要用于测试方法执行时间,会分别输出每个代码段的执行时间;<br>
     * 例:<br>
     * <br>
     * 方法开始<br>
     * Date startTime = new Date();<br>
     * 代码段一<br>
     * startTime = DateUtil.timeCompare(new Date(), startTime, "代码段一");<br>
     * 代码段二<br>
     * startTime = DateUtil.timeCompare(new Date(), startTime, "代码段二");<br>
     * 代码段三<br>
     * startTime = DateUtil.timeCompare(new Date(), startTime, "代码段三");<br>
     * <p>
     */
    public static Date timeCompare(Date date1, Date date2, String notes) {
        Date returnTime;
        long timeDifference = 0;
        if (date2.getTime() > date1.getTime()) {
            timeDifference = date2.getTime() - date1.getTime();
            returnTime = date2;
        } else {
            timeDifference = date1.getTime() - date2.getTime();
            returnTime = date1;
        }
        long day = timeDifference / (24 * 60 * 60 * 1000);
        long hour = (timeDifference / (60 * 60 * 1000) - day * 24);
        long min = ((timeDifference / (60 * 1000)) - day * 24 * 60 - hour * 60);
        long s = (timeDifference / 1000 - day * 24 * 60 * 60 - hour * 60 * 60 - min * 60);
        long ms = (timeDifference % 1000 - day * 24 * 60 * 60 - hour * 60 * 60 - min * 60);
        System.out.println("【" + notes + "】:" + day + "天" + hour + "小时" + min + "分" + s + "秒" + ms + "毫秒");
        return returnTime;
    }

    /**
     * 返回本周的开始和结束时间
     *
     * @return
     */
    public static Date[] getCurrentWeek() {
        Calendar ca = Calendar.getInstance();
        int dayOfWeek = ca.get(Calendar.DAY_OF_WEEK);
        // 中国习惯：周一是一周的开始
        if (dayOfWeek == 1) {
            dayOfWeek = 7;
        } else {
            dayOfWeek--;
        }
        Calendar cal = (Calendar) ca.clone();

        cal.add(Calendar.DATE, 1 - dayOfWeek);
        Date date1 = cal.getTime();
        cal = (Calendar) ca.clone();
        cal.add(Calendar.DATE, 7 - dayOfWeek);
        Date date2 = cal.getTime();
        return new Date[]
                {date1, date2};
    }

    /**
     * 判断当前日期是星期几<br>
     * <br>
     *
     * @param pTime 修要判断的时间<br>
     * @return dayForWeek 判断结果<br>
     * @Exception 发生异常<br>
     */
    public static int dayForWeek(String pTime) throws Exception {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Calendar c = Calendar.getInstance();
        c.setTime(format.parse(pTime));
        int dayForWeek = 0;
        if (c.get(Calendar.DAY_OF_WEEK) == 1) {
            dayForWeek = 7;
        } else {
            dayForWeek = c.get(Calendar.DAY_OF_WEEK) - 1;
        }
        return dayForWeek;
    }

    /**
     * 获取当前日期字符串 yyyy-MM-dd
     */
    /**
     * @return
     */
    public static String getNowDateYYDDMM() {
        return (new SimpleDateFormat("yyyy-MM-dd")).format(new Date());

    }
    
    /**
     * 得到当前年份 YYYY
     */
    /**
     * @return
     */
    public static String getNowDateY() {
        return (new SimpleDateFormat("yyyy")).format(new Date());

    }

    /**
     * 得到下一年 YYYY
     */
    public static int getNextY() {
        Calendar cal = Calendar.getInstance();
        int year = cal.get(Calendar.YEAR);//获取年份
        return year + 1;
    }

    /**
     * 获取当前日期字符串  YYYY-MM-DD HH:MM:SS
     */
    public static String getNowDateYYDDMMHHMMSS() {
        return (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).format(new Date());
    }

    /**
     * 获取当前时间字符串 HH:mm
     */
    public static String getNowDateHHMM() {
        return (new SimpleDateFormat("HH:mm")).format(new Date());
    }

    /**
     * 得到传入的date得到HH:mm
     */
    public static String getDateHHMM(Date date) {
        return (new SimpleDateFormat("HH:mm")).format(date);
    }
    
    /**
     * 根据传入的date得到YYYYMMDD
     */
    public static String getYYYYMMDDFromDate(Date date) {
        return (new SimpleDateFormat("yyyyMMdd")).format(date);
    }
    
    /**
     * 根据传入的date得到YYYY-MM-DD
     */
    public static String getYYYYMMDDFromDate2(Date date) {
        return (new SimpleDateFormat("yyyy-MM-dd")).format(date);
    }

    /**
     * 转换HHmm字符串
     */
    public static Date parseDateHHMM(String hhmm) throws Exception {
        return (new SimpleDateFormat("HH:mm")).parse(hhmm);
    }

    public static String getNowHHMMSS() throws Exception {
        return (new SimpleDateFormat("HH:mm:ss")).format(new Date());
    }

    public static Time getNowHHMMSSDate() throws Exception {
        return new Time((new SimpleDateFormat("HH:mm:ss")).parse((new SimpleDateFormat("HH:mm:ss")).format(new Date())).getTime());
    }

    public static Date parseHHMMSSDate(String hhmmss) throws Exception {
        return (new SimpleDateFormat("HH:mm:ss")).parse(hhmmss);
    }

    public static String formatDateHHMM(Time time) throws Exception {
        return (new SimpleDateFormat("HH:mm")).format(time);
    }

    public static Time addTime(Time time, int minutes) throws Exception {
        return null;
    }

    /**
     * 判断字符串是否是日期格式
     *
     * @param date
     * @return
     */
    public static boolean isDate(String date) {
        try {
            return java.text.DateFormat.getDateInstance().parse(date) != null;
        } catch (java.text.ParseException e) {
            return false;
        }
    }

    /**
     * 返回当前日期0点的毫秒数
     *
     * @return Long
     */
    public static Long getTodayTime() {
        Date date1 = new Date();

        @SuppressWarnings("deprecation")
        Date date2 = new Date(date1.getYear(), date1.getMonth(), date1.getDate());

        return date2.getTime();
    }

    /**
     * 返回昨天日期0点的毫秒数
     *
     * @return Long
     */
    public static Long getYesterdayTime() {
        Date date1 = new Date();

        @SuppressWarnings("deprecation")
        Date date2 = new Date(date1.getYear(), date1.getMonth(), date1.getDate());

        return date2.getTime() - (24 * 60 * 60 * 1000);
    }

    public static Time parseTimeBystr(String param) {
        if (param == null) {
            return null;
        }

        try {
            return new Time((new SimpleDateFormat("HH:mm")).parse(param).getTime());
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 判断两个日期是否是不同年份的同一天
     *
     * @param date1 date1
     * @param date2 date2
     * @return
     */
    public static boolean isSameDate(Date date1, Date date2) {
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date1);

        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(date2);
        boolean isSameMonth = cal1.get(Calendar.MONTH) == cal2.get(Calendar.MONTH);
        boolean isSameDate = isSameMonth && cal1.get(Calendar.DAY_OF_MONTH) == cal2.get(Calendar.DAY_OF_MONTH);

        return isSameDate;
    }

    /**
     * 将分钟时间格式化为**天**小时**分钟
     *
     * @return
     */
    public static String formatTime(int paramMinute) {
        long days = paramMinute / (60 * 24);
        long hours = (paramMinute % (60 * 24)) / 60;
        long minutes = paramMinute % 60;
        String result = "";
        if (days > 0) {
            result += days + "天";
        }
        if (hours > 0) {
            result += hours + "小时";
        }
        if (minutes > 0) {
            result += minutes + "分";
        }
        return result;
    }

    /**
     * 格式化日期类型的参数
     *
     * @param time
     * @return
     */
    public static String formatTime(String time) {
        try {
            Date date = DateUtil.parseDateAll(time);
            return (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).format(date);
        } catch (Exception e) {
        }
        return null;
    }

 /*   public static void main(String[] args) throws Exception {
        // Time t = new Time(System.currentTimeMillis());
        //
        // System.out.println(format(t));
        // int a = timeCampare("","");
        System.out.println(DateUtil.getCurrentDateByDiff(600));
    }*/

    /**
     * 格式化日期类型的参数
     *
     * @return month年月 month_in传入 无则返回本月 month_in_next下月
     */
    @SuppressWarnings("deprecation")
    public static JSONObject getNextMonth(String yyyyMM) {
        JSONObject result = new JSONObject();
        Date now1 = new Date();
        try {
            Date now = new Date();
            String nowds = (new SimpleDateFormat("yyyy-MM")).format(now);
            now1 = (new SimpleDateFormat("yyyy-MM")).parse(nowds);
            if (yyyyMM != null && yyyyMM.length() > 0) {
                Date indate = (new SimpleDateFormat("yyyy-MM")).parse(yyyyMM);

                result.put("month", (new SimpleDateFormat("yyyy年MM月")).format(indate));
                result.put("month_in", (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).format(indate));
                indate.setMonth(indate.getMonth() + 1);
                result.put("month_in_next", (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).format(indate));
                return result;

            }
        } catch (ParseException e) {

            result.put("month", (new SimpleDateFormat("yyyy年MM月")).format(now1));
            result.put("month_in", (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).format(now1));
            now1.setMonth(now1.getMonth() + 1);
            result.put("month_in_next", (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).format(now1));
            return result;
        }

        result.put("month", (new SimpleDateFormat("yyyy年MM月")).format(now1));
        result.put("month_in", (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).format(now1));
        now1.setMonth(now1.getMonth() + 1);
        result.put("month_in_next", (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).format(now1));
        return result;
    }

    @SuppressWarnings("deprecation")
    public static JSONObject getThisMonth(String yyyyMM) {
        JSONObject result = new JSONObject();
        Date now1 = new Date();
        try {
            Date now = new Date();
            String nowds = (new SimpleDateFormat("yyyy-MM")).format(now);
            now1 = (new SimpleDateFormat("yyyy-MM")).parse(nowds);
            if (yyyyMM != null && yyyyMM.length() > 0) {
                Date indate = (new SimpleDateFormat("yyyy-MM")).parse(yyyyMM);

                result.put("month", (new SimpleDateFormat("yyyy年MM月")).format(indate));
                result.put("month_c", yyyyMM);
                result.put("month_first", yyyyMM + "-01");
                Calendar a = Calendar.getInstance();
                a.set(Calendar.YEAR, indate.getYear());
                a.set(Calendar.MONTH, indate.getMonth());
                a.set(Calendar.DATE, 1);// 把日期设置为当月第一天
                a.roll(Calendar.DATE, -1);// 日期回滚一天，也就是最后一天
                result.put("month_last", yyyyMM + "-" + a.get(Calendar.DATE));
                result.put("days", a.get(Calendar.DATE));
                return result;

            }
        } catch (ParseException e) {
            String nd = (new SimpleDateFormat("yyyy-MM")).format(now1);
            result.put("month", (new SimpleDateFormat("yyyy年MM月")).format(now1));
            result.put("month_c", nd);
            result.put("month_first", nd + "-01");
            Calendar a = Calendar.getInstance();
            a.set(Calendar.YEAR, now1.getYear());
            a.set(Calendar.MONTH, now1.getMonth());
            a.set(Calendar.DATE, 1);// 把日期设置为当月第一天
            a.roll(Calendar.DATE, -1);// 日期回滚一天，也就是最后一天
            result.put("month_last", nd + "-" + a.get(Calendar.DATE));
            result.put("days", a.get(Calendar.DATE));
            return result;
        }

        String nd = (new SimpleDateFormat("yyyy-MM")).format(now1);
        result.put("month", (new SimpleDateFormat("yyyy年MM月")).format(now1));
        result.put("month_c", nd);
        result.put("month_first", nd + "-01");
        Calendar a = Calendar.getInstance();
        a.set(Calendar.YEAR, now1.getYear());
        a.set(Calendar.MONTH, now1.getMonth());
        a.set(Calendar.DATE, 1);// 把日期设置为当月第一天
        a.roll(Calendar.DATE, -1);// 日期回滚一天，也就是最后一天
        result.put("month_last", nd + "-" + a.get(Calendar.DATE));
        result.put("days", a.get(Calendar.DATE));
        return result;
    }

    /**
     * 得到这个月的第一天和今天
     *
     * @return month年月 month_in传入 无则返回本月 month_in_next下月
     */
    public static JSONObject getMonthDate() {
        JSONObject result = new JSONObject();
        Date now1 = new Date();
        String from = (new SimpleDateFormat("yyyy-MM")).format(now1) + "-01";
        String to = (new SimpleDateFormat("yyyy-MM-dd")).format(now1);
        result.put("month", from + " -- " + to);
        result.put("from", from);
        result.put("to", to);
        return result;
    }
    
    public static JSONObject getDateBeforeMonth(int num){
    	 JSONObject result = new JSONObject();
         Date b = new Date();
         Calendar c = Calendar.getInstance();
         c.add(Calendar.MONTH, -num);
   	 	 String from = (new SimpleDateFormat("yyyy-MM-dd")).format(c.getTime());
   	 	 String to = (new SimpleDateFormat("yyyy-MM-dd")).format(b);
   	 	 result.put("from", from);
   	 	 result.put("to", to);
         return result;
    }
    /**
     * 计算两个日期相差天数
     *
     * @param date
     * @param date2
     * @return
     */
    public static int daysBetween(Date date, Date date2) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        long time1 = cal.getTimeInMillis();
        cal.setTime(date2);
        long time2 = cal.getTimeInMillis();
        long between_days = (time2 - time1) / (1000 * 3600 * 24);
        return Integer.parseInt(String.valueOf(between_days));
    }

    public static String getCurrentDateByDiff(int seconds) {
        Date now = new Date();
        long times = now.getTime() - seconds * 1000;
        Date result = new Date(times);
        return DateUtil.format(result, "yyyy-MM-dd HH:mm:ss");
    }
    
    public static String getyyyymmdd()
    {
    	 return (new SimpleDateFormat("yyyyMM-dd")).format(new Date());
    }
    
    public static String gethhmmss()
    {
    	 return (new SimpleDateFormat("HHmmss")).format(new Date());
    }
    
    public static String getSubSecYYYYMMDDHHMMSS(long sbS)
    {
    	return (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).format(new Date(System.currentTimeMillis()-(sbS*1000)));
    }
    public static String getYYYYMMDDHHMMSS(Date date)
    {
    	 return (new SimpleDateFormat("yyyyMMddHHmmss")).format(date);
    }
    /**
     * 日期转毫秒
     * @param date
     * @return
     */
    public static long getMillionSeconds(String date){
    	try{
    		 Calendar calendar = Calendar.getInstance();
    		 calendar.setTime(new SimpleDateFormat("yyyy-MM-dd").parse(date));
    		 return calendar.getTimeInMillis();
    	}catch(Exception e){
    		e.printStackTrace();
    	}
		return 0;
    	
    }
    public static void main(String[] args) {
    	
//    	JSONObject result = new JSONObject();
//        Date b = new Date();
//        Calendar c = Calendar.getInstance();
//        c.add(Calendar.MONTH, -2);
//  	 	 String from = (new SimpleDateFormat("yyyy-MM-dd")).format(c.getTime());
//  	 	 String to = (new SimpleDateFormat("yyyy-MM-dd")).format(b);
//  	 	 result.put("from", from);
//  	 	 result.put("to", to);
//  	 	 System.out.println(result);
//    	System.out.println(getNowDateYYDDMMHHMMSS());
//    	System.out.println(getSubSecYYYYMMDDHHMMSS(30L));
	}
}
