package com.tzx.pos.bo.imp;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import com.tzx.base.common.util.PosPropertyUtil;
import com.tzx.base.entity.PosOptState;
import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.file.MqFileDownload;
import com.tzx.framework.common.util.HttpUtil;
import com.tzx.framework.common.util.Tools;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.base.util.JsonUtil;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.bo.PosRegisterService;
import com.tzx.pos.po.springjdbc.dao.PosRegisterDao;

@Service(PosRegisterService.NAME)
public class PosRegisterServiceImp implements PosRegisterService
{
	private final String		postUrl						= "/rest/hq/post";

	private final String		codeType					= "pos_bill_code";

	/** 日始状态:未日始 */
	private final int			REPORT_DATE_STATE_NOT		= 0;
	/** 日始状态:已日始 */
	private final int			REPORT_DATE_STATE_BEGAIN	= 1;
	/** 日始状态:已打烊 */
	private final int			REPORT_DATE_STATE_END		= 2;

	private static final Logger	logger						= Logger.getLogger(PosDishServiceImp.class);

	@Resource(name = PosRegisterDao.NAME)
	private PosRegisterDao		registerDao;

	public String getPequestUrl(String url)
	{
        String saas_url = PosPropertyUtil.getMsg("saas.url");
	    logger.info("总部地址："+saas_url);
		return saas_url + url;
	}
	
	public void initDataForinstallStore() throws Exception
	{
		Map<String, String> systemMap = com.tzx.framework.common.constant.Constant.getSystemMap();
		if (!systemMap.containsKey("tenent_id") || !systemMap.containsKey("store_id") || "".equals(systemMap.get("tenent_id")) || "".equals(systemMap.get("store_id")))
		{
			logger.info("参数为空");
			return;
		}

		String tenancyId = systemMap.get("tenent_id");
		Integer storeId = Integer.parseInt(systemMap.get("store_id"));

		int dayBegainCount = registerDao.getPosOptStateForDayBegainCount(tenancyId, storeId);

		if (0 == dayBegainCount)
		{
			this.initDataForinstallStore(tenancyId, storeId);
		}
	}

	@Override
	public void initDataForinstallStore(String tenancyId, int storeId) throws Exception
	{
		// TODO Auto-generated method stub
		// 请求总部,获取信息
		String reqURL = this.getPequestUrl(postUrl) ;
		long t = System.currentTimeMillis();

		Data paramData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
		paramData.setType(Type.STORE_INSTALL);
		paramData.setOper(Oper.init);

		JSONObject param = JSONObject.fromObject(paramData);

		logger.info(String.valueOf(t) + "<发送接口请求地址==>" + reqURL);
		logger.info(String.valueOf(t) + "<发送接口请求体==>" + param.toString());
		String result = HttpUtil.sendPostRequest(reqURL, param.toString(), 20 * 1000, 20 * 1000);
		logger.info(String.valueOf(t) + "<发送接口返回体==>" + result);

		if (Tools.isNullOrEmpty(result))
		{
			return;
		}

		Data resData = JsonUtil.JsonToData(JSONObject.fromObject(result));
		
		JSONObject resultObj = null;
		if(Constant.CODE_SUCCESS == resData.getCode())
		{
			resultObj = JSONObject.fromObject(resData.getData().get(0));
		}
		
		if(Tools.isNullOrEmpty(resultObj))
		{
			return ;
		}

		Date reportDate = ParamUtil.getDateValueByObject(resultObj, "report_date");
		if (Tools.isNullOrEmpty(reportDate))
		{
			return;
		}

		// 插入日始,以及签到操作记录
		JSONArray optStateArray = resultObj.optJSONArray("pos_opt_state");
		int reportDateState = this.addPosOptStsteByReportDate(tenancyId, storeId, reportDate, optStateArray);

		if (REPORT_DATE_STATE_BEGAIN == reportDateState)
		{
			// 根据最大账单号设置
			String billNum = resultObj.optString("bill_num");
			this.updateSysCodeValues(tenancyId, storeId, billNum);

			// 获取门店详细数据
			JSONArray fetchDataArray = resultObj.optJSONArray("fetch_data");
			this.updateDataByReportDate(tenancyId, storeId, reportDate, fetchDataArray);
		}
	}

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @throws Exception
	 */
	private void updateSysCodeValues(String tenancyId, int storeId, String billNum) throws Exception
	{
		if (Tools.isNullOrEmpty(billNum))
		{
			return;
		}

		Integer len = 6;
		JSONObject billCodeRuleJson = registerDao.getSysBillCodeRule(tenancyId, codeType);
		if (Tools.hv(billCodeRuleJson))
		{
			len = ParamUtil.getIntegerValueByObject(billCodeRuleJson, "serial_length");
		}

		String perfix = null;
		Integer serial = null;
		if (Tools.hv(billNum) && billNum.length() > len)
		{
			int beginIndex = billNum.length() - len;
			perfix = billNum.substring(0, beginIndex);
			serial = Integer.valueOf(billNum.substring(beginIndex, billNum.length()));
		}

		if (Tools.hv(perfix) && 0 < serial)
		{
			registerDao.updateSysCodeValues(tenancyId, storeId, codeType, perfix, serial);
		}
	}

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param reportDate
	 * @param optStateArray
	 * @throws Exception
	 */
	private Integer addPosOptStsteByReportDate(String tenancyId, int storeId, Date reportDate, JSONArray optStateArray) throws Exception
	{
		boolean isDayBegain = false;
		boolean isDayEnd = false;
		if (null != optStateArray && optStateArray.size() > 0)
		{
			List<PosOptState> optStateList = new ArrayList<PosOptState>();

			JSONObject optStateJson = null;
			Date itemReportDate = null;
			for (Object obj : optStateArray)
			{
				optStateJson = JSONObject.fromObject(obj);
				itemReportDate = ParamUtil.getDateValueByObject(optStateJson, "report_date");
				if (0 == reportDate.compareTo(itemReportDate))
				{
					String optContent = ParamUtil.getStringValueByObject(optStateJson, "content");
					if (SysDictionary.OPT_STATE_DAYBEGAIN.equals(optContent))
					{
						isDayBegain = true;
					}
					if (SysDictionary.OPT_STATE_DAYEND.equals(optContent))
					{
						isDayEnd = true;
					}
					PosOptState optState = new PosOptState();
					optState.setTenancy_id(tenancyId);
					optState.setStore_id(storeId);
					optState.setReport_date(itemReportDate);
					optState.setShift_id(ParamUtil.getIntegerValueByObject(optStateJson, "shift_id"));
					optState.setContent(optContent);
					optState.setPos_num(ParamUtil.getStringValueByObject(optStateJson, "pos_num"));
					optState.setOpt_num(ParamUtil.getStringValueByObject(optStateJson, "opt_num"));
					optState.setOpt_name(ParamUtil.getStringValueByObject(optStateJson, "opt_name"));
					optState.setManager_num(ParamUtil.getStringValueByObject(optStateJson, "manager_num"));
					optState.setLast_updatetime(ParamUtil.getTimestampValueByObject(optStateJson, "last_updatetime"));
					optState.setTag(ParamUtil.getStringValueByObject(optStateJson, "tag"));
					optState.setLogin_number(ParamUtil.getIntegerValueByObject(optStateJson, "login_number"));
					optState.setRemark(ParamUtil.getStringValueByObject(optStateJson, "remark"));
					optState.setUpload_tag(1);
					optStateList.add(optState);
				}
			}
			registerDao.batchInsertPosOptState(tenancyId, storeId, optStateList);
		}

		if (isDayBegain && isDayEnd)
		{
			return REPORT_DATE_STATE_END;
		}
		else if (isDayBegain)
		{
			return REPORT_DATE_STATE_BEGAIN;
		}
		else
		{
			return REPORT_DATE_STATE_NOT;
		}
	}
	
	@SuppressWarnings("unchecked")
	private void updateDataByReportDate(String tenancyId, int storeId, Date reportDate, JSONArray fetchDataArray) throws Exception
	{
		if (null != fetchDataArray && fetchDataArray.size() > 0)
		{
			List<String> tableList = new ArrayList<String>();
			for (Object obj : fetchDataArray)
			{
				JSONObject fetchDataJson = JSONObject.fromObject(obj);
				boolean exceptionState = fetchDataJson.optBoolean("exception_state");
				Integer tableSize = fetchDataJson.optInt("table_size");
				String tableName = fetchDataJson.optString("table_name");
				if (true == exceptionState)
				{
					logger.error("总部表异常:" + tableName);
					continue;
				}
				
				if (0 >= tableSize)
				{
					continue;
				}

				JSONObject dataJson = null;
				if (Tools.hv(fetchDataJson.optString("fetch_url")))
				{
					String dataMsg = null;
					try
					{
						dataMsg = MqFileDownload.download(fetchDataJson.optString("fetch_url"),true);
						dataJson = JSONObject.fromObject(dataMsg);
					}
					catch (Exception e)
					{
						logger.error("下载数据错误,下载地址===>" + fetchDataJson.optString("fetch_url"));
						logger.error("下载数据错误,下载数据===>" + dataMsg);
					}
				}

				if (null != dataJson && tableName.equals(dataJson.optString("table_name")))
				{
					JSONArray dataArray = dataJson.getJSONArray("value_list");
					registerDao.insertBatchIgnorCase(tenancyId, tableName, dataArray);
				}
				else
				{
					tableList.add(tableName);
				}
			}
			
			if(tableList.size()>0)
			{
				this.requestData(tenancyId, storeId, reportDate, tableList);
			}
		}
	}
	
	private void requestData(String tenancyId, int storeId, Date reportDate, List<String> tableList) throws Exception
	{
		String reqURL = this.getPequestUrl(postUrl);
		long t = System.currentTimeMillis();

		JSONObject paramJson = new JSONObject();
		paramJson.put("table_names", StringUtils.join(tableList, ","));
		paramJson.put("report_date", DateUtil.formatDate(reportDate));
		
		List<JSONObject> paramList = new ArrayList<JSONObject>();
		paramList.add(paramJson);
		
		Data paramData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
		paramData.setType(Type.STORE_INSTALL);
		paramData.setOper(Oper.find);
		paramData.setData(paramList);

		JSONObject param = JSONObject.fromObject(paramData);

		logger.info(String.valueOf(t) + "<发送接口请求地址==>" + reqURL);
		logger.info(String.valueOf(t) + "<发送接口请求体==>" + param.toString());
		String result = HttpUtil.sendPostRequest(reqURL, param.toString(), 20 * 1000, 20 * 1000);
		logger.info(String.valueOf(t) + "<发送接口返回体==>" + result);

		if (Tools.isNullOrEmpty(result))
		{
			return;
		}

		Data resData = JsonUtil.JsonToData(JSONObject.fromObject(result));
		if (Constant.CODE_SUCCESS == resData.getCode())
		{
			JSONObject resultObj = JSONObject.fromObject(resData.getData().get(0));
			JSONArray fetchDataArray = resultObj.optJSONArray("fetch_data");
			this.updateDataByReportDate(tenancyId, storeId, reportDate, fetchDataArray);
		}
	}

}
