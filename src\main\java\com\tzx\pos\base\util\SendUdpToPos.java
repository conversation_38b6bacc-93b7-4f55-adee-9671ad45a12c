package com.tzx.pos.base.util;

import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.SocketAddress;

public class SendUdpToPos 
{
	public static void sendMessagePos(String message,  String sPort)
	{
		DatagramSocket ds = null;
		DatagramPacket dgp = null;
		try
		{
			Integer iport = Integer.parseInt(sPort);
			byte[] buffer = message.getBytes();
			//InetAddress addr = InetAddress.getLocalHost();
			String ip = "***************";
			//String ip = addr.getHostAddress();
			//ip = ip.substring(0, ip.lastIndexOf(".")) + ".255";
			SocketAddress sendAddress = new InetSocketAddress(ip, iport);
			dgp = new DatagramPacket(buffer, buffer.length, sendAddress);
			ds = new DatagramSocket();//发送对象
			ds.send(dgp);
		}
		catch(Throwable e)
		{
			e.printStackTrace();
		}
		finally
		{
			if (null != ds)
				ds.close();
		}
	}
}
