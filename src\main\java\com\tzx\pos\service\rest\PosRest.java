package com.tzx.pos.service.rest;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import com.tzx.clientorder.wlifeprogram.bo.WlifeService;
import com.tzx.clientorder.wlifeprogram.bo.impl.BasicServiceImp;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import com.tzx.base.bo.CacheManagerService;
import com.tzx.base.bo.PosCodeService;
import com.tzx.base.controller.PosBaseController;
import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.entity.Pagination;
import com.tzx.framework.common.exception.ErrorCode;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.Comet4jUtil;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.Tools;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.ios.bo.BaseDataIOSService;
import com.tzx.ios.bo.PosSoldOutIOSService;
import com.tzx.member.crm.bo.CustomerCardConsumeService;
import com.tzx.member.crm.bo.CustomerCouponService;
import com.tzx.member.crm.bo.CustomerInvoiceService;
import com.tzx.member.crm.bo.CustomerService;
import com.tzx.member.crm.bo.CustomerSignService;
import com.tzx.member.meituan.bo.PosNewCrmService;
import com.tzx.orders.base.util.OrderUtil;
import com.tzx.orders.bo.OrdersManagementService;
import com.tzx.orders.bo.OrdersSoldoutService;
import com.tzx.pos.asyn.pool.ThreadPool;
import com.tzx.pos.asyn.thread.BillForPaymentPrintThread;
import com.tzx.pos.asyn.thread.OpenTablePrintThread;
import com.tzx.pos.asyn.thread.OrderingPrintThread;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.FunctionCode;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.service.PosDataSyncService;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.base.util.JsonUtil;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.base.util.PathUtil;
import com.tzx.pos.base.util.ReqDataUtil;
import com.tzx.pos.bo.CloudBillService;
import com.tzx.pos.bo.ComboSetMealService;
import com.tzx.pos.bo.LoginService;
import com.tzx.pos.bo.OrderDeliveryService;
import com.tzx.pos.bo.PaymentService;
import com.tzx.pos.bo.PosActivityService;
import com.tzx.pos.bo.PosCashierReceiveService;
import com.tzx.pos.bo.PosChangeShiftService;
import com.tzx.pos.bo.PosDiscountService;
import com.tzx.pos.bo.PosDishGiveService;
import com.tzx.pos.bo.PosDishRetreatFoodService;
import com.tzx.pos.bo.PosDishService;
import com.tzx.pos.bo.PosPrintNewService;
import com.tzx.pos.bo.PosPrintService;
import com.tzx.pos.bo.PosService;
import com.tzx.pos.bo.PosServiceSelf;
import com.tzx.pos.bo.PosSoldOutService;
import com.tzx.pos.bo.QuickPassService;
import com.tzx.pos.bo.TakeOrderingService;
import com.tzx.pos.bo.UploadDataService;
import com.tzx.pos.bo.dto.LockTable;
import com.tzx.pos.bo.dto.PosTableFind;
import com.tzx.pos.bo.dto.SysModule;
import com.tzx.pos.bo.payment.NocodeCouponsService;
import com.tzx.pos.bo.payment.PosPaymentService;
import com.tzx.pos.service.notify.PosNotifyOberver;
import com.tzx.pos.service.servlet.AcewillCustomerServlet;
import com.tzx.pos.service.servlet.CustomerCardActivationServlet;
import com.tzx.pos.service.servlet.CustomerCardRechargeServlet;
import com.tzx.pos.service.servlet.PosDishServlet;
import com.tzx.pos.version.BohUpgradeConstant;
import com.tzx.pos.version.BohUpgradeState;
import com.tzx.pos.version.NebulaBoh;
import com.tzx.thirdpay.bo.ThirdPaymentRefundService;
import com.tzx.thirdpay.bo.imp.ThirdPaymentRefundServiceImp;
import net.sf.json.JSONObject;

@Controller("PosRest")
@RequestMapping("/posRest")
public class PosRest extends PosBaseController
{
	private static final Logger	logger	= Logger.getLogger(PosRest.class);

	@Resource(name = PosService.NAME)
	private PosService			posService;

	@Resource(name = LoginService.NAME)
	private LoginService		loginService;

	@Resource(name = PosDishService.NAME)
	private PosDishService		posDishService;

	@Resource(name = CustomerService.NAME)
	private CustomerService		customerService;

	@Resource(name = CustomerCardConsumeService.NAME)
	private CustomerCardConsumeService		customerCardService;

	@Resource(name = PosDishRetreatFoodService.NAME)
	private PosDishRetreatFoodService		retreatFoodService;

	@Resource(name=PosCodeService.NAME)
	PosCodeService					codeService;

	@Resource(name = PaymentService.NAME)
	private PaymentService		paymentService;

	@Resource(name = PosPrintService.NAME)
	private PosPrintService		posPrintService;

	@Resource(name = PosServiceSelf.NAME)
	private PosServiceSelf		posServiceSelf;

	@Resource(name = PosPrintNewService.NAME)
	private PosPrintNewService posPrintNewService;

	@Resource(name = PosPaymentService.NAME)
	private PosPaymentService		posPaymentService;


	@Resource(name = PosNewCrmService.NAME)
	private PosNewCrmService posNewCrmService;

	@Resource(name = QuickPassService.NAME)
	private QuickPassService quickPassService;

	@Resource(name = CacheManagerService.NAME)
	private CacheManagerService cacheManagerService;

	@Autowired
	private PosDataSyncService posDataSyncService;

	@Resource(name = PosDishGiveService.NAME)
	private PosDishGiveService posDishGiveService;

	@Resource(name = PosDiscountService.NAME)
	private PosDiscountService posDiscountService;

	@Resource(name = PosActivityService.NAME)
	private PosActivityService posActivityService;


	@Resource(name = CustomerCardRechargeServlet.NAME)
	private CustomerCardRechargeServlet cardRechargeServlet;

	@Resource(name = CustomerCardActivationServlet.NAME)
	private CustomerCardActivationServlet customerCardActivationServlet;

	@Resource(name = AcewillCustomerServlet.NAME)
	private AcewillCustomerServlet acewillCustomerServlet;

	@Resource(name = ComboSetMealService.NAME)
	private ComboSetMealService comboSetMealService;

	@Resource(name = CloudBillService.NAME)
	private CloudBillService cloudBillService;

	@Resource(name = UploadDataService.NAME)
	private UploadDataService			uploadDataService;

	@Resource(name = PosDishServlet.NAME)
	private PosDishServlet posDishServlet;

	@Resource(name = CustomerCouponService.NAME)
	private CustomerCouponService	customerCouponService;

	@Resource(name = CustomerInvoiceService.name)
	private CustomerInvoiceService customerInvoiceService;

	@Autowired
    private TakeOrderingService takeOrderingService;

	@Resource(name = OrderDeliveryService.NAME)
	private OrderDeliveryService orderDeliveryService;

	@Resource(name = OrdersManagementService.NAME)
	private OrdersManagementService ordersManagementService;

	@Resource(name = CustomerSignService.NAME)
	private CustomerSignService customerSignService;

	@Resource(name = BaseDataIOSService.NAME)
	private BaseDataIOSService baseDataIOSService;
	
	@Resource(name = PosCashierReceiveService.NAME)
	private PosCashierReceiveService cashierReceiveService;

	public static boolean storeStateOpen = false;    // 门店状态（true ： 营业中   false : 非营业）

//	@Autowired
//	private PosPaymentDao			paymentDao;
	/**
	 * 请求入口
	 *
	 * @return JSONObject
	 */
	@RequestMapping(value = "post", method = RequestMethod.POST)
	@ResponseBody
	public JSONObject post(HttpServletRequest request, @RequestBody JSONObject jsobj)
	{
//		response.setContentType("text/html; charset=UTF-8");
//		PrintWriter out = null;
		JSONObject responseJson = null;

//		ObjectMapper objectMapper = new ObjectMapper();
		Data param = null;
		long bt = System.currentTimeMillis();

		try{
			param = JsonUtil.JsonToData(jsobj,request.getHeader("User-Agent"));
		}catch(Exception se){
			se.printStackTrace();
			logger.error("JSON转data对象错误" + ExceptionMessage.getExceptionMessage(se));
			responseJson = buildErrorResult(Constant.CODE_PARAM_FAILURE, "json转data类型错误，请查看传入参数");
		}

		// 对账单号进行同步处理
		String sync = UUID.randomUUID().toString();     // 同步标识
		if(param.getData() != null && param.getData().size()>0){
			try{
				Map<String, Object> maps =(Map<String, Object>) param.getData().get(0);
				if(maps.containsKey("bill_num")){
					sync = maps.get("bill_num").toString().intern();
				}
			}catch(Exception e1){
				logger.error("data转Map<String, Object>对象错误："+e1);
			}
		}
		synchronized(sync){

			/*if (Tools.isNullOrEmpty(param.getTenancy_id()) || param.getData() == null || param.getData().isEmpty())
			{
				throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
			}
			// 检验是否通过认证
			if (!TokenUtil.checkToken(param.getT(), param.getTenancy_id(), param.getSecret()))
			{
				// System.out.println(request.getLocalAddr() + "==" +
				// request.getRemoteHost());
				// System.out.println("验证信息：" + TokenUtil.checkToken(param.getT(),
				// param.getTenancy_id(), param.getSecret()));
			}*/

	//		String reqJson = JSONObject.fromObject(param).toString();

			boolean isPrintLog = (!Type.DEVICES_REFRESH.equals(param.getType()) && !(Type.KVS_INFO.equals(param.getType()) && Oper.find.equals(param.getOper())));
			if (isPrintLog)
			{
				logger.info(String.valueOf(bt)+"<==T，接收请求：==>" + jsobj.toString());
			}

			try
			{
				DBContextHolder.setTenancyid(param.getTenancy_id());
			}
			catch (Exception e)
			{
				logger.error("切换数据源错误：" + ExceptionMessage.getExceptionMessage(e));
				e.printStackTrace();
				// return buildErrorResult(Constant.CODE_CHANGE_DATASOURCE_FAILURE,
				// Constant.POS_CHANGE_DATASOURCE_FAILURE);
				responseJson = buildErrorResult(Constant.CODE_CHANGE_DATASOURCE_FAILURE, Constant.POS_CHANGE_DATASOURCE_FAILURE);
			}
			Data result = null; // 以前用Data.get(param),现在换成clone
			// try
			// {
			if (param.getPagination() == null)
			{
				param.setPagination(new Pagination());
			}
			result = param.clone();
			result.setData(null);
			// }
			// catch (CloneNotSupportedException e1)
			// {
			// logger.info(Constant.CLONE_DATA_FAILURE + "：" + e1);
			// e1.printStackTrace();
			// return buildErrorResult(Constant.CODE_INNER_EXCEPTION,
			// Constant.POS_CHANGE_DATASOURCE_FAILURE);
			// }

			// 判断门店状态是否正常，关店或装修暂停营业
			// JSONObject oobj = null;
			// try
			// {
			// oobj = this.isStoreNormal(param.getTenancy_id(),
			// param.getStore_id());
			// }catch (Exception se)
			// {
			// logger.info("查询门店状态：" + ExceptionMessage.getExceptionMessage(se));
			// result.setCode(Constant.CODE_INNER_EXCEPTION);
			// result.setMsg("查询门店状态失败");
			// JSONObject postData = JSONObject.fromObject(result);
			// return postData;
			// }
			//
			// if (oobj.optInt("code") != 0)
			// {
			// return oobj;
			// }

			Data data = null;

			JSONObject json = new JSONObject();

			String message = "";

			switch (param.getType())
			{
	            case AVAILABILITY_CHECK:
	                try
	                {
	                    if (Oper.check == param.getOper())
	                    {
	                        result.setCode(Constant.CODE_SUCCESS);
	                        result.setSuccess(true);
	                    }
	                }
	                catch (Exception e)
	                {
	                    buildExceptionData(e, result, Constant.INNER_ERROR);
	                }
	                break;
				case VERSION:
					try
					{
						if (Oper.check == param.getOper())
						{
							result.setData(posService.findAppVersion(request, param.getData()));
							result.setCode(Constant.CODE_SUCCESS);
							result.setMsg(Constant.APP_VERSION_SUCCESS);
							result.setSuccess(true);
						}
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.APP_VERSION_FAILURE);
					}
					break;
				case BASIC_VERSION:
					try
					{
						result.setData(posService.basicVersion(param));
						result.setCode(Constant.CODE_SUCCESS);
						result.setMsg(Constant.BASIC_VERSION_SUCCESS);
					}
					catch (Exception se)
					{
						buildExceptionData(se, result, Constant.BASIC_VERSION_FAILURE);
					}
					break;

				case BASIC_DATA:
					try
					{
						if (SysDictionary.SOURCE_ANDROID_PAD.equals(param.getSource()))
						{
							baseDataIOSService.syncBaseData(param, result);
						}
						else
						{
							posService.syncBaseData(param, result);
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.SYNC_DATA_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.SYNC_DATA_FAILURE);
					}
					break;

				case LOGIN:
					try
					{						
						List<JSONObject> logins = loginService.login(param);
						if (logins.size() == 0)
						{
							buildDataResult(Constant.CODE_NULL_DATASET, Constant.LOGIN_FAILURE, logins, result);
						}
						else
						{
							buildDataResult(Constant.CODE_SUCCESS, Constant.LOGIN_SUCCESS, logins, result);
						}
						PathUtil.delSaasLog();//删除日志						
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.LOGIN_ERROR);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.LOGIN_ERROR);
					}
					break;
				case CHANGE_PASSWORD:
					try
					{
						loginService.updatePasword(param, result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.MODIFY_PASSWORD_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.MODIFY_PASSWORD_FAILURE);
					}
					break;

				case TABLE_STATE:
					try
					{
						List<PosTableFind> ftables = posService.findTable(param);
						JSONObject dataParam = JSONObject.fromObject(param.getData().get(0));
						try
						{
							posService.updateDevicesByCode(dataParam.getString("pos_num"), "1");
						}
						catch (Exception e)
						{
							e.printStackTrace();
						}

						if (ftables.size() == 0)
						{
							buildDataResult(Constant.CODE_NULL_DATASET, Constant.FIND_TABLE_NOT_EXISTS, ftables, result);
						}
						else
						{
							buildDataResult(Constant.CODE_SUCCESS, Constant.FIND_TABLE_SUCCESS, ftables, result);
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.FIND_TABLE_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.FIND_TABLE_FAILURE);
					}
					break;

				case OPEN_TABLE:
				case OPEN_TABLENEW:
				case NEW_OPEN_TABLE:
                    openTable(param, result, json);
                    break;

				case ORDERING:
					try
					{
//						String header = request.getHeader("User-Agent");
//
//						if (Tools.hv(header) && header.toLowerCase().contains("android"))
//						{
//							param.setSource(SysDictionary.SOURCE_ANDROID_PAD);
//						}

						data = posDishService.newOrderDish(param, json);
//						String tenantId = param.getTenancy_id();
//						Integer organId = param.getStore_id();
//						Map<String, Object> maps = ReqDataUtil.getDataMap(param);
//						String billno = ParamUtil.getStringValue(maps, "bill_num", true, PosErrorCode.NOT_NULL_BILL_NUM);
//						// 数据上传
//						posDishService.upload(tenantId,organId+"","","","",billno);

						posDishService.upload(param);
						try
						{
							if (json != null)
							{
								String source = param.getSource();
								if (SysDictionary.SOURCE_MOVE.contains(source)||SysDictionary.SOURCE_ORDER_DEVICE.equals(source))
								{
									posPrintService.getBindOptNum(json.optString("tenancy_id"), json.optInt("store_id"), json);
	                            }
	                            json.put("source", source);
	                            if(posPrintNewService.isNONewPrint(json.optString("tenancy_id"), json.optInt("store_id")))
								{   // 如果启用新的打印模式
									if (json.optString("mode").equalsIgnoreCase("0") && json.optString("is_print").equalsIgnoreCase("Y") )
									{
										if(json.containsKey("rwids") && json.getString("rwids").length() > 0)
										{
											posPrintNewService.posPrintByFunction(json.optString("tenancy_id"), json.optInt("store_id"), FunctionCode.ORDERING, json);
										}
									}
								}
								else
								{
									//打印点菜单
									if(json.containsKey("is_print_order") && "Y".equalsIgnoreCase(json.optString("is_print_order")))
									{
										List<JSONObject> printParaList = new ArrayList<JSONObject>();
										printParaList.add(json);

										Data printData = Data.get(param);
										printData.setOper(Oper.check);
										printData.setType(Type.PRINT_BILL);
										printData.setData(printParaList);

										posPrintService.printPosBill(printData, null);
									}

									//厨房打印
									if (json.optString("mode").equalsIgnoreCase("0"))
									{
										if (json.optString("is_print").equalsIgnoreCase("Y"))
										{
											List<Integer> list = posPrintService.orderChef(json.optString("tenancy_id"), json.optString("bill_num"), json.optInt("store_id"), "0");
											posPrintService.orderPrint(json.optString("tenancy_id"), json.optString("bill_num"), json.optInt("store_id"), list);
										}
									}
								}
							}
						}catch(Exception e1){
							logger.info(ExceptionMessage.getExceptionMessage(e1));
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.ORDER_DISH_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.ORDER_DISH_FAILURE);
					}
					break;

				case ORDERINGNEW:
					try
					{
//						String header = request.getHeader("User-Agent");
//
//						if (Tools.hv(header) && header.toLowerCase().contains("android"))
//						{
//							param.setSource(SysDictionary.SOURCE_ANDROID_PAD);
//						}
						data = posDishService.newestOrderDish(param, json);
	//					data = posDishService.newOrderDish(param, json);

//						// 数据上传
//						String tenantId = param.getTenancy_id();
//						Integer organId = param.getStore_id();
//						Map<String, Object> maps = ReqDataUtil.getDataMap(param);
//						String billno = ParamUtil.getStringValue(maps, "bill_num", true, PosErrorCode.NOT_NULL_BILL_NUM);
//						posDishService.upload(tenantId,organId+"","","","",billno);

						posDishService.upload(param);

						// 异步执行分单打印
						OrderingPrintThread orderingPrintThread = new OrderingPrintThread(json, param);
						ThreadPool.getPrintThreadPool().execute(orderingPrintThread);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.ORDER_DISH_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.ORDER_DISH_FAILURE);
					}
					break;

				case PAYMENT:
					try
					{
						paymentService.posPayment(param, result, json);
						if (json != null)
						{
							if ("Y".equalsIgnoreCase(json.optString("is_print")))
							{
								List<Integer> list = posPrintService.orderChef(json.optString("tenancy_id"), json.optString("bill_num"), json.optInt("store_id"), "0");
								posPrintService.orderPrint(json.optString("tenancy_id"), json.optString("bill_num"), json.optInt("store_id"), list);
							}
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.PAYMENT_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.PAYMENT_FAILURE);
					}
					break;
				case CLEAR_PAYMENT:
					try
					{
	//					paymentService.clearPayment(param, result);
						posPaymentService.billPaymentService(param, result,null);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.CLEAR_PAYMENT_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.CLEAR_PAYMENT_FAILURE);
					}
					break;
				case ORDER_PAYMENT:
					try
					{
						paymentService.posPaymentOrder(param, result, json);
						if (json != null)
						{
							if ("Y".equalsIgnoreCase(json.optString("is_print")))
							{
								List<Integer> list = posPrintService.orderChef(json.optString("tenancy_id"), json.optString("bill_num"), json.optInt("store_id"), "0");
								posPrintService.orderPrint(json.optString("tenancy_id"), json.optString("bill_num"), json.optInt("store_id"), list);
							}
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.ORDER_PAYMENT_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.ORDER_PAYMENT_FAILURE);
					}
					break;
				case PAYMENT_PRECREATE:
					try
					{
						String path = PathUtil.getWebRoot(request);
						List<JSONObject> list = paymentService.precreate(param, path);
						result.setData(list);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.PAYMENT_ALIPAY_PRECREATE);
					}
					break;
				case PAYMENT_BARCODE:
					try
					{
						String bar_code_path = PathUtil.getWebRoot(request);
						List<JSONObject> list = paymentService.barcode(param, bar_code_path);
						result.setData(list);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.PAYMENT_ALIPAY_BARCODE);
					}
					break;
				case PAYMENT_QUERY:
					try
					{
						String path_query = PathUtil.getWebRoot(request);
						List<JSONObject> list = paymentService.query(param, path_query);
						result.setData(list);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.PAYMENT_ALIPAY_QUERY);
					}
					break;
				case PAYMENT_CANCLE:
					try
					{
						String path_cancle = PathUtil.getWebRoot(request);
						List<JSONObject> list = paymentService.cancle(param, path_cancle);
						result.setData(list);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.PAYMENT_ALIPAY_CANCLE);
					}
					break;
				case PAYMENT_REFUND:
					try
					{
						String path_refund = PathUtil.getWebRoot(request);
						List<JSONObject> list = paymentService.refund(param, path_refund);
						result.setData(list);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.PAYMENT_ALIPAY_REFUND);
					}
					break;
				case PAYMENT_UNIFIEDORDER:
					try
					{
						String path_unifiedorder = PathUtil.getWebRoot(request);
						List<JSONObject> list = paymentService.unifiedorder(param, path_unifiedorder);
						result.setData(list);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.PAYMENT_WECHAT_UNIFIEDORDER);
					}
					break;
				case PAYMENT_MICROPAY:
					try
					{
						String path_mircopay = PathUtil.getWebRoot(request);
						List<JSONObject> list = paymentService.micropay(param, path_mircopay);
						result.setData(list);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.PAYMENT_WECHAT_MICROPAY);
					}
					break;
				case PAYMENT_ORDERQUERY:
					try
					{
						String path_orderquery = PathUtil.getWebRoot(request);
						List<JSONObject> list = paymentService.orderquery(param, path_orderquery);
						result.setData(list);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.PAYMENT_WECHAT_ORDERQUERY);
					}
					break;
				case PAYMENT_CLOSEORDER:
					try
					{
						String path_closeorder = PathUtil.getWebRoot(request);
						List<JSONObject> list = paymentService.closeorder(param, path_closeorder);
						result.setData(list);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.PAYMENT_WECHAT_CLOSEORDER);
					}
					break;
				case PAYMENT_REFUNDORDER:
					try
					{
						String path_refundorder = PathUtil.getWebRoot(request);
						List<JSONObject> list = paymentService.refundorder(param, path_refundorder);
						result.setData(list);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.PAYMENT_WECHAT_REFUNDORDER);
					}
					break;
				case PAYMENT_REFUNDQUERY:
					try
					{
						String path_refundquery = PathUtil.getWebRoot(request);
						List<JSONObject> list = paymentService.refundquery(param, path_refundquery);
						result.setData(list);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.PAYMENT_WECHAT_REFUNDQUERY);
					}
					break;
				case QUERY_PAY_STATE:
					try{
						JSONObject printJson =new JSONObject();
						result = posPaymentService.queryThirdPayment(param,printJson);

						try
						{
							posPrintService.printPosBillForPayment(printJson, posPrintNewService);
						}
						catch (Exception e)
						{
							e.printStackTrace();
							logger.error(e );
						}
					}catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.QUERY_PAY_STATE_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.QUERY_PAY_STATE_FAILURE);
					}
					break;
				case QUERY_PAY_REFUND:
					try{
						result = posPaymentService.queryRefundPayment(param);
					}catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.QUERY_PAY_REFUND_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.QUERY_PAY_REFUND_FAILURE);
					}
					break;
				case REFUND_PAY_ORDER:
					try{
						result = posPaymentService.refundThirdPayment(param);
					}catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.REFUND_PAY_ORDER_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.REFUND_PAY_ORDER_FAILURE);
					}
					break;
				case CANCEL_PAY_ORDER:
					try{
						result = posPaymentService.cancelThirdPayment(param);
					}catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.CANCEL_PAY_ORDER_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.CANCEL_PAY_ORDER_FAILURE);
					}
					break;
				case PAYMENT_FAILURE:
					try
					{
	//					paymentService.updatePaymentStateForFailure(param, result);
						posPaymentService.billPaymentService(param, result,null);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.PAYMENT_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.PAYMENT_FAILURE);
					}
					break;
				case GET_PREPAY_BARCODE:
					try
					{
						String webPath = PathUtil.getWebRoot(request);
						result = posPaymentService.precreateThirdPayment(param, webPath);
						JSONObject paramJson = JSONObject.fromObject(param.getData().get(0));
	//					String saleMode =posService.getSaleMode(param.getTenancy_id(), param.getStore_id(),paramJson.getString("order_no"));
	//					paymentDao.updatePosBillTaxPriceSeparation(param.getTenancy_id(), param.getStore_id(),paramJson.getString("order_no"), saleMode);
						posPaymentService.updatePosBillTaxPriceSeparation(param.getTenancy_id(), param.getStore_id(), paramJson);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.GET_PREPAY_BARCODE_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.GET_PREPAY_BARCODE_FAILURE);
					}
					break;
				case PAY_ORDER_BY_CUSTOMER:
					try{
					    String source = param.getSource();
						JSONObject printJson =new JSONObject();
						result = posPaymentService.barcodeThirdPayment(param,printJson);
						JSONObject paramJson = JSONObject.fromObject(param.getData().get(0));
	//					String saleMode =posService.getSaleMode(param.getTenancy_id(), param.getStore_id(),paramJson.getString("order_no"));
	//					paymentDao.updatePosBillTaxPriceSeparation(param.getTenancy_id(), param.getStore_id(),paramJson.getString("order_no"), saleMode);
						posPaymentService.updatePosBillTaxPriceSeparation(param.getTenancy_id(), param.getStore_id(), paramJson);
						try
						{
	                        printJson.put("source", source);
							posPrintService.printPosBillForPayment(printJson, posPrintNewService);
						}
						catch (Exception e)
						{
							e.printStackTrace();
							logger.error(e );
						}
					}catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.PAY_ORDER_BY_CUSTOMER_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.PAY_ORDER_BY_CUSTOMER_FAILURE);
					}

					break;
				case BILL_PAYMENT:
					try
					{
						List<?> dataList = param.getData();
						if (null != dataList && dataList.size() > 0)
						{
							json = JSONObject.fromObject(dataList.get(0));
							if (null != json)
							{
								String tableCode = json.getString("table_code");
								if (Tools.isNullOrEmpty(tableCode))
								{
									tableCode = posDishService.getTakeMealNum(param, result);
									if (Tools.hv(tableCode) && tableCode.length() > 0)
									{
										// 截取日期，舍去日期
										tableCode = tableCode.substring((tableCode.length() > 3 ? tableCode.length() - 3 : 0), tableCode.length());
										json.put("table_code", tableCode);
										List<JSONObject> list = new ArrayList<JSONObject>();
										list.add(json);
										param.setData(list);
									}
								}
							}
						}
						JSONObject printJson = new JSONObject();

						// paymentService.posBillPayment(param, result, printJson);

						if (Oper.check == param.getOper())
						{
							posPaymentService.posBillPayment(param, result, printJson, true);
						}
						else if (Oper.update == param.getOper())//手动关闭账单
						{
							posPaymentService.manualClosedPosBill(param, result, printJson);
						}


//						String tenantId = param.getTenancy_id();
//						Integer organId = param.getStore_id();
//						Map<String, Object> maps = ReqDataUtil.getDataMap(param);
//						String billno = ParamUtil.getStringValue(maps, "bill_num", true, PosErrorCode.NOT_NULL_BILL_NUM);
//						// 数据上传
//						posDishService.upload(tenantId, organId + "", "", "", "", billno);

						posDishService.upload(param);

						try
						{
							String source = param.getSource();
							if (SysDictionary.SOURCE_MOVE.contains(source)||SysDictionary.SOURCE_ORDER_DEVICE.equals(source))
							{
								posPrintService.getBindOptNum(printJson.optString("tenancy_id"), printJson.optInt("store_id"), printJson);
							}
							printJson.put("source", source);

							BillForPaymentPrintThread billForPaymentPrintThread = new BillForPaymentPrintThread(printJson, posPrintNewService, posPrintService);
							BillForPaymentPrintThread.billForPaymentThreadPool.execute(billForPaymentPrintThread);
						}
						catch (Exception e1)
						{
							logger.error(e1);
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.ORDER_PAYMENT_FAILURE);
						try
						{
							posPaymentService.getPosBillPaymentByBillNum(param, result);
						}
						catch (Exception e)
						{
							logger.info("查询帐单失败", e);
						}
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.ORDER_PAYMENT_FAILURE);
					}
					break;

				case BEFOREHAND_BILL_PAYMENT:
					try
					{
						List<?> dataList = param.getData();
						if(null != dataList && dataList.size() > 0) {
							json = JSONObject.fromObject(dataList.get(0));
							if (null != json) {
								String tableCode = json.optString("table_code");
								if(Tools.isNullOrEmpty(tableCode)) {
									tableCode = posDishService.getTakeMealNum(param, result);
									if (Tools.hv(tableCode) && tableCode.length() > 0) {
										//截取日期，舍去日期
										tableCode = tableCode.substring((tableCode.length() > 3 ? tableCode.length() - 3 : 0), tableCode.length());
										json.put("table_code", tableCode);
										List<JSONObject> list = new ArrayList<JSONObject>();
										list.add(json);
										param.setData(list);
									}
								}
	//							}
							}
						}
						result.setCode(Constant.CODE_SUCCESS);
						result.setMsg(Constant.ORDER_PAYMENT_SUCCESS);
						posPaymentService.beforehandBillPayment(param.getTenancy_id(), param.getStore_id(), param.getData(),param.getSource(),result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.ORDER_PAYMENT_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.ORDER_PAYMENT_FAILURE);
					}
					break;
				case QUERY_PAYMENT:
					try
					{
						JSONObject printJson = new JSONObject();
	//					paymentService.queryBillPayment(param, result,printJson);
						posPaymentService.paymentStateQuery(param, result, printJson);

						try
						{
							if(printJson != null && !printJson.isEmpty()){
								posPrintService.printPosBillForPayment(printJson, posPrintNewService);
							}
						}
						catch (Exception e1)
						{
							logger.error(ExceptionMessage.getExceptionMessage(e1));
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.ORDER_PAYMENT_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.ORDER_PAYMENT_FAILURE);
					}
					break;
				case BILL_PAYMENT_REPEAT:
					try
					{
						JSONObject printJson = new JSONObject();
	//					paymentService.billPaymentRepeat(param, result, printJson);
						posPaymentService.paymentRepeat(param, result, printJson);

					try{
						posPrintService.printPosBillForPayment(printJson, posPrintNewService);
					}catch(Exception e1){
						logger.error(e1);
					}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.ORDER_PAYMENT_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.ORDER_PAYMENT_FAILURE);
					}
					break;

				case ENFORCE_CLOSE_BILL:
					try
					{
						JSONObject printJson = new JSONObject();
	//					paymentService.updatePaymentStateForSuccess(param, result,printJson);
						posPaymentService.paymentComplete(param, result, printJson);

					try{
						posPrintService.printPosBillForPayment(printJson, posPrintNewService);
						}catch(Exception e1){
							logger.error(e1);
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.ORDER_PAYMENT_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.ORDER_PAYMENT_FAILURE);
					}
					break;
				case CHANGE_TABLE:
					try
					{
						posDishService.changeTable(param, result, json);

						try
						{
							posService.updateDevicesDataState();
						}
						catch (Exception e)
						{
							// TODO Auto-generated catch block
							e.printStackTrace();
							logger.info("Exception:" + ExceptionMessage.getExceptionMessage(e));
						}

//						if (json != null)
//						{
//							String bill_num = json.optString("bill_num");
//							String tenantId = param.getTenancy_id();
//							Integer organId = param.getStore_id();
//	//						Map<String, Object> maps = ReqDataUtil.getDataMap(param);
//							// 数据上传
//							posDishService.upload(tenantId, String.valueOf(organId), "", "", "", bill_num);
//						}

						try
						{
							if (json != null)
							{
								JSONObject changeJSON = json.getJSONObject("changeJSON");
								JSONObject orderJSON = json.getJSONObject("orderJSON");
								if (posPrintNewService.isNONewPrint(changeJSON.optString("tenancy_id"), changeJSON.optInt("store_id")))
								{// 如果启用新的打印模式
									posPrintNewService.posPrintByFunction(changeJSON.optString("tenancy_id"), changeJSON.optInt("store_id"), FunctionCode.CHANGE_TABLE, changeJSON);

									//打印清单
									posPrintNewService.posPrintByMode(orderJSON.optString("tenancy_id"), orderJSON.optInt("store_id"), com.tzx.orders.base.constant.Constant.ORDER_PRINT_CODE_1003, orderJSON);
								}
								else
								{
									List<Integer> lids = posPrintService.orderWholeChgChef(changeJSON.optString("tenancy_id"), changeJSON.optString("bill_num"), changeJSON.optInt("store_id"), changeJSON.optString("oper"), changeJSON.optString("oper_type"), changeJSON.optString("table_tag"), changeJSON.optString("table_name_tag"));
									posPrintService.orderPrint(orderJSON.optString("tenancy_id"), changeJSON.optString("bill_num"), json.optInt("store_id"), lids);

									//打印清单
									Data printData = Data.get();
									printData.setTenancy_id(json.optString("tenancy_id"));
									printData.setStore_id(json.optInt("store_id"));
									printData.setType(Type.PRINT_BILL);
									printData.setData(Arrays.asList(orderJSON));
									Data resultData = Data.get();
									posPrintService.printPosBill(printData, resultData);
								}


							}
						}
						catch (Exception e1)
						{
							logger.error(e1);
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.CHANGE_TABLE_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.CHANGE_TABLE_FAILURE);
					}
					break;
				case BILL:
					try
					{
						posDishService.newFindPosBill(param, result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.FIND_BILL_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.FIND_BILL_FAILURE);
					}
					break;
				case POS_BILL:
					try
					{
						posDishService.findPosBillList(param, result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.FIND_BILL_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.FIND_BILL_FAILURE);
					}
					break;
				case GUQING:
					try
					{
//						List<JSONObject> soldList = new ArrayList<JSONObject>();
//						posService.setSoldOut(param, result,soldList);

//						if (soldList.size() > 0)
//						{
//							Oper oper = Oper.add;
//							//上传到总部
//							Data subParam = new Data();
//							subParam =  param.clone();
//							subParam.setOper(oper);
//							SellDishesDataUploadRunnable sellDishesDataUpload = new SellDishesDataUploadRunnable(subParam, soldList);
//							Thread sellDishT = new Thread(sellDishesDataUpload);
//							sellDishT.start();
//						}
//						//沽清上传到redis
//						String tenantId = param.getTenancy_id();
//						Integer organId = param.getStore_id();
//						posDataSyncService.dataSyncForPosSoldOut(tenantId,organId+"");

						if (Oper.check == param.getOper())
						{
							message = Constant.SET_GUQING_FAILURE;
							DBContextHolder.setTenancyid(param.getTenancy_id());
							PosSoldOutService poldOutService = (PosSoldOutService)SpringConext.getApplicationContext().getBean(PosSoldOutService.NAME);
							poldOutService.setSoldOut(param, result);

                            //清空微生活缓存
                            String tenantId = param.getTenancy_id();
                            Integer organId = param.getStore_id();
                            WlifeService wlifeService = (WlifeService) SpringConext.getApplicationContext().getBean(WlifeService.NAME);
                            wlifeService.synchrodataDish(tenantId, organId);
						}else if (Oper.find == param.getOper())
						{
							message = "查询外卖估清失败";
							DBContextHolder.setTenancyid(param.getTenancy_id());
							OrdersSoldoutService soldOutService = (OrdersSoldoutService)SpringConext.getApplicationContext().getBean(OrdersSoldoutService.NAME);
							List<JSONObject> dataList = soldOutService.findOrderSoldOut(param);
							result.setData(dataList);
						}
						else 
						{
							result.setCode(Constant.CODE_PARAM_FAILURE);
							result.setMsg(Constant.NOT_EXISTS_OPER);
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, message);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, message);
					}
					break;
				case VIEW_GUQING:
					try
					{
						List<JSONObject> objs =null;
						if (SysDictionary.SOURCE_ANDROID_PAD.equals(param.getSource()))
						{
							DBContextHolder.setTenancyid(param.getTenancy_id());
							PosSoldOutIOSService poldOutService = (PosSoldOutIOSService)SpringConext.getApplicationContext().getBean(PosSoldOutIOSService.NAME);
							objs = poldOutService.findSoldOut(param);
						}
						else
						{
							DBContextHolder.setTenancyid(param.getTenancy_id());
							PosSoldOutService poldOutService = (PosSoldOutService)SpringConext.getApplicationContext().getBean(PosSoldOutService.NAME);
							objs = poldOutService.findSoldOut(param);
						}
						
//						List<JSONObject> objs = posService.findSoldOut(param);
						if (null == objs || 0 == objs.size())
						{
							buildDataResult(Constant.CODE_NULL_DATASET, Constant.QUERY_GUQING_NOT_EXIST, objs, result);
						}
						else
						{
							buildDataResult(Constant.CODE_SUCCESS, Constant.QUERY_GUQING_SUCCESS, objs, result);
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.QUERY_GUQING_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.QUERY_GUQING_FAILURE);
					}
					break;
				case SOLD_OUT:
					try
					{
						posService.setWorryItem(param, result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.SET_WORRY_DISH_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.SET_WORRY_DISH_FAILURE);
					}
					break;
				case VIEW_SOLD_OUT:
					try
					{
						List<JSONObject> worries = posService.findWorryItem(param);
						if (worries.size() == 0)
						{
							buildDataResult(Constant.CODE_NULL_DATASET, Constant.QUERY_WORRY_DISH_NOT_EXIST, worries, result);
						}
						else
						{
							buildDataResult(Constant.CODE_SUCCESS, Constant.QUERY_WORRY_DISH_SUCCESS, worries, result);
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.QUERY_WORRY_DISH_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.QUERY_WORRY_DISH_FAILURE);
					}
					break;

				case SAVE_CONFIG:
					try
					{
						posService.systemConfig(param, result);
						result.setCode(Constant.CODE_SUCCESS);
						result.setMsg(Constant.SYS_CONFIG_SUCCESS);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.SYS_CONFIG_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.SYS_CONFIG_FAILURE);
					}
					break;
				case CLOCK_TABLE:
					try
					{
						posService.lockOrUnlockTable(param, result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.LOCK_UNLOCK_TABLE_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.LOCK_UNLOCK_TABLE_FAILURE);
					}
					break;
				case COPY_TABLE:
					try
					{
						List<JSONObject> list = new ArrayList<JSONObject>();
						List<JSONObject> uplist = new ArrayList<JSONObject>();
						posDishService.posCopyTable(param, codeService, result, list,uplist);
						for (JSONObject up:uplist){
							posDishService.upload(up.optString("tenancy_id"),up.optString("store_id"),up.optString("fromTableCode"),up.optString("toTableCode"),up.optString("fromBill"),up.optString("toBill"));
	//						new Thread(new DataUploadRunnable(up.optString("tenancy_id"),up.optString("store_id"),up.optString("fromTableCode"),up.optString("toTableCode"),up.optString("fromBill"),up.optString("toBill"))).start();
						}
						if (list.size() > 0)
						{
							for (int n = 0; n < list.size(); n++)
							{
								JSONObject objs = list.get(n);
							try{
								if(posPrintNewService.isNONewPrint(objs.optString("tenancy_id"),objs.optInt("store_id"))){//如果启用新的打印模式
									posPrintNewService.posPrintByFunction(objs.optString("tenancy_id"),
											objs.optInt("store_id"),
											FunctionCode.ORDERING,
											objs);
								}else{
									List<Integer> ids = posPrintService.orderChef(objs.optString("tenancy_id"), objs.optString("bill_num"), objs.optInt("store_id"), objs.optString("print_type"));
									posPrintService.orderPrint(objs.optString("tenancy_id"), objs.optString("bill_num"), objs.optInt("store_id"), ids);
								}
							}catch(Exception e1){
								logger.error(e1);
							}
								}
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.COPY_TABLE_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.COPY_TABLE_FAILURE);
					}
					break;
				case ITEM_SERVED:
					try
					{
						data = posService.updateItemServedState(param);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.GET_CASHIER_LIMIT_VLIDATE_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.GET_CASHIER_LIMIT_VLIDATE_FAILURE);
					}
					break;
				case SINGLE_CHANGE_TABLE:
					try
					{
						posDishService.singleChangeTable(param, result, json);
						try
						{
							if (json != null)
							{
//								if (posPrintNewService.isNONewPrint(json.optString("tenancy_id"), json.optInt("store_id")))
//								{// 如果启用新的打印模式
//									posPrintNewService.posPrintByFunction(json.optString("tenancy_id"), json.optInt("store_id"), FunctionCode.SINGLE_CHANGE_TABLE, json);
//								}
//								else
//								{
//									List<Integer> lids = posPrintService.orderSingleChgChef(json.optString("tenancy_id"), json.optString("bill_num"), json.optInt("store_id"), json.optString("oper"), json.optString("oper_type"), json.optString("table_code"), json.optString("table_tag"),
//											json.optString("rwid"));
//									posPrintService.orderPrint(json.optString("tenancy_id"), json.optString("bill_num"), json.optInt("store_id"), lids);
//								}


								JSONObject changeJSON = json.getJSONObject("changeJSON");
								JSONObject orderJSON = json.getJSONObject("orderJSON");
								if (posPrintNewService.isNONewPrint(changeJSON.optString("tenancy_id"), changeJSON.optInt("store_id")))
								{// 如果启用新的打印模式
									posPrintNewService.posPrintByFunction(changeJSON.optString("tenancy_id"), changeJSON.optInt("store_id"), FunctionCode.CHANGE_TABLE, changeJSON);

								}
								else
								{
									List<Integer> lids = posPrintService.orderWholeChgChef(changeJSON.optString("tenancy_id"), changeJSON.optString("bill_num"), changeJSON.optInt("store_id"), changeJSON.optString("oper"), changeJSON.optString("oper_type"), changeJSON.optString("table_tag"), changeJSON.optString("table_name_tag"));
									posPrintService.orderPrint(orderJSON.optString("tenancy_id"), changeJSON.optString("bill_num"), json.optInt("store_id"), lids);
								}

							}

						}
						catch (Exception e1)
						{
							logger.error(e1);
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.SINGLE_CHANGE_TABLE_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.SINGLE_CHANGE_TABLE_FAILURE);
					}
					break;
				case VIEW_ORDERDISH:
					try
					{
						result.setData(posDishService.findOderDish(param));
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.QUERY_ORDERED_DISH_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.QUERY_ORDERED_DISH_FAILURE);
					}
					break;
				case ITEM_CHEAP:
					try
					{
						result.setData(posService.findItemCheap(param));
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.QUERY_CHEAP_DISH_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.QUERY_CHEAP_DISH_FAILURE);
					}
					break;
				case CHANG_SERVICE:
					try
					{
						posDishService.modifyServiceFee(param, result);
//						String tenantId = param.getTenancy_id();
//						Integer organId = param.getStore_id();
//						Map<String, Object> maps = ReqDataUtil.getDataMap(param);
//						String billno = ParamUtil.getStringValue(maps, "bill_num", true, PosErrorCode.NOT_NULL_BILL_NUM);
//						// 数据上传
//						posDishService.upload(tenantId,organId+"","","","",billno);

						posDishService.upload(param);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.MODIFY_SERVICE_FEE_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.MODIFY_SERVICE_FEE_FAILURE);
					}
					break;
				case CHANG_GUEST:
					try
					{
						posDishService.modifyGuest(param, result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.MODIFY_CUSTOMER_COUNT_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.MODIFY_CUSTOMER_COUNT_FAILURE);
					}
					break;
				case SINGLE_DISCOUNT:
					try
					{
						data = posDishService.singleDiscount(param);
					}
					catch(SystemException se)
					{
						buildSysExceptionData(se, result, Constant.BILL_SINGLE_DISCOUNT_FAILURE);
					}
					catch(Exception e)
					{
						buildExceptionData(e, result, Constant.BILL_SINGLE_DISCOUNT_FAILURE);
					}
					break;

				case BILL_DISCOUNT:
					try
					{
						data = posDiscountService.newBillDiscount(param);
//						String tenantId = param.getTenancy_id();
//						Integer organId = param.getStore_id();
//						Map<String, Object> maps = ReqDataUtil.getDataMap(param);
//						String billno = ParamUtil.getStringValue(maps, "bill_num", true, PosErrorCode.NOT_NULL_BILL_NUM);
//						// 数据上传
//						posDishService.upload(tenantId,organId+"","","","",billno);
						posDishService.upload(param);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.BILL_DISCOUNT_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.BILL_DISCOUNT_FAILURE);
					}
					break;
				case CANC_BILL_TASTE:
					try
					{
						posDishService.cancBillTaste(param, result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.CANC_BILL_REMARK_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.CANC_BILL_REMARK_FAILURE);
					}
					break;
				case GUEST_MSG:
					try
					{
						posDishService.guestMsg(param, result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.GUEST_MSG_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.GUEST_MSG_FAILURE);
					}
					break;
				case CHANG_ITEM_ZFKW:
					try
					{
						posDishService.changeRemarkAndZfkw(param, result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.DISH_REMARK_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.DISH_REMARK_FAILURE);
					}
					break;
				case CHANG_WAITER:
					try
					{
						posDishService.changeWaiter(param, result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.CHANGE_WAITER_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.CHANGE_WAITER_FAILURE);
					}
					break;
				case CHANG_ITEM_UNIT:
					try
					{
						posDishService.changeItemUnit(param, result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.CHANGE_DISH_UNIT_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.CHANGE_DISH_UNIT_FAILURE);
					}
					break;
				case GIVE:
					try
					{
						if (Oper.check == param.getOper())
						{
							posDishGiveService.checkGiveItem(param, result);
						}
						else if (Oper.add == param.getOper())
						{
							posDishGiveService.giveItem(param, result);
//							String tenantId = param.getTenancy_id();
//							Integer organId = param.getStore_id();
//							Map<String, Object> maps = ReqDataUtil.getDataMap(param);
//							String billno = ParamUtil.getStringValue(maps, "bill_num", true, PosErrorCode.NOT_NULL_BILL_NUM);
//							// 数据上传
//							posDishService.upload(tenantId,organId+"","","","",billno);
							posDishService.upload(param);
						}
						else
						{
							result.setCode(Constant.CODE_PARAM_FAILURE);
							result.setMsg(Constant.NOT_EXISTS_OPER);
						}

					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.GIVE_DISH_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.GIVE_DISH_FAILURE);
					}
					break;
				case RETREAT_FOOD:
					try
					{
						if (Oper.check == param.getOper())
						{
							retreatFoodService.checkRetreatFood(param.getTenancy_id(), param.getStore_id(), param.getData(), result);
						}
						else if (Oper.add == param.getOper())
						{
//							String header = request.getHeader("User-Agent");
//							if (Tools.hv(header) && header.toLowerCase().contains("android"))
//							{
//								param.setSource(SysDictionary.SOURCE_ANDROID_PAD);
//							}
							retreatFoodService.retreatFood(param.getTenancy_id(), param.getStore_id(), param.getData(), result, json, param.getSource());
//							String tenantId = param.getTenancy_id();
//							Integer organId = param.getStore_id();
//							Map<String, Object> maps = ReqDataUtil.getDataMap(param);
//							String billno = ParamUtil.getStringValue(maps, "bill_num", true, PosErrorCode.NOT_NULL_BILL_NUM);
//							// 数据上传
//							retreatFoodService.upload(tenantId, organId + "", "", "", "", billno);
							retreatFoodService.upload(param);

							try
							{
								if (json != null)
								{
									if (posPrintNewService.isNONewPrint(json.optString("tenancy_id"), json.optInt("store_id")))
									{// 如果启用新的打印模式
										//后厨档口打印机是否打印退菜单
										if("0".equals(OrderUtil.getSysPara(json.optString("tenancy_id"), json.optInt("store_id"), "HCDKDYJSFDYTCD"))){
											logger.info("已设置后厨档口打印机不打印退菜单");
										}else{
											posPrintNewService.posPrintByFunction(json.optString("tenancy_id"), json.optInt("store_id"), FunctionCode.RETREAT_FOOD_SELF, json);
										}
										posPrintNewService.posPrintByMode(json.optString("tenancy_id"), json.optInt("store_id"), SysDictionary.PRINT_CODE_1114, json);
									}
									else
									{
										List<Integer> ids = posPrintService.orderRetreat(json.optString("tenancy_id"), json.optString("bill_num"), json.optInt("store_id"), json.optString("oper"), json.optString("oper_type"), json.optString("rwids"));
										posPrintService.orderPrint(json.optString("tenancy_id"), json.optString("bill_num"), json.optInt("store_id"), ids);
									}
								}
							}
							catch (Exception e)
							{
								e.printStackTrace();
								logger.info("退菜打印失败:", e);
							}
                            //正餐自动划菜
                            try {
                                posService.retreatItemStamp(param,json);
                            }catch (Exception ex){
                                logger.error(ex);
                            }

						}
						else
						{
							result.setCode(Constant.CODE_PARAM_FAILURE);
							result.setMsg(Constant.NOT_EXISTS_OPER);
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.RETREAT_DISH_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.RETREAT_DISH_FAILURE);
					}
					break;
				case CHANG_ITEM_COUNT:
					try
					{
						posDishService.changeDishCount(param, result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.CHANGE_DISH_COUNT_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.CHANGE_DISH_COUNT_FAILURE);
					}
					break;
                case CHANG_ITEM_ASSIST_NUM:
                    try
                    {
                        posDishService.changeItemAssistNum(param, result);
                    }
                    catch (SystemException se)
                    {
                        buildSysExceptionData(se, result, Constant.CHANGE_ITEM_ASSIST_NUM_FAILURE);
                    }
                    catch (Exception e)
                    {
                        buildExceptionData(e, result, Constant.CHANGE_ITEM_ASSIST_NUM_FAILURE);
                    }
                    break;
				case CHANG_ITEM_NAME:
					try
					{
						posDishService.changeDishName(param, result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.CHANGE_DISH_NAME_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.CHANGE_DISH_NAME_FAILURE);
					}
					break;
				case REMINDER_FOOD:
					try
					{
						posDishService.pushFood(param, result, json);
						try
						{
							if (json != null)
							{
								if (posPrintNewService.isNONewPrint(json.optString("tenancy_id"), json.optInt("store_id")))
								{// 如果启用新的打印模式
									posPrintNewService.posPrintByFunction(json.optString("tenancy_id"), json.optInt("store_id"), FunctionCode.REMINDER_FOOD, json);
								}
								else
								{
									List<Integer> ids = posPrintService.orderOperChef(json.optString("tenancy_id"), json.optString("bill_num"), json.optInt("store_id"), json.optString("oper"), json.optString("oper_type"), json.optString("rwids"));
									posPrintService.orderPrint(json.optString("tenancy_id"), json.optString("bill_num"), json.optInt("store_id"), ids);
								}
							}
						}
						catch (Exception e1)
						{
							logger.error(e1);
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.REMINDER_FOOD_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.REMINDER_FOOD_FAILURE);
					}
					break;
				case WAIT_CALL:
					try
					{
						posDishService.waitCall(param, result, json);
						if (json != null)
						{
							List<Integer> ids = posPrintService.orderOperChef(json.optString("tenancy_id"), json.optString("bill_num"), json.optInt("store_id"), json.optString("oper"), json.optString("oper_type"), json.optString("rwids"));
							posPrintService.orderPrint(json.optString("tenancy_id"), json.optString("bill_num"), json.optInt("store_id"), ids);
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.WAIT_CALL_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.WAIT_CALL_FAILURE);
					}
					break;
				case DAY_BEGAIN:
					try
					{
						if (Oper.check == param.getOper())
						{
                            boolean isNewInstall = posService.isNewInstall(param.getTenancy_id(), param.getStore_id(), null);
                            int dayCount = 0;
                            if (!isNewInstall) {
                                dayCount = posService.getDayCountState(param.getTenancy_id(), param.getStore_id(), param.getData());
                            }else{
                                dayCount = 1;
                            }
						/*	//未日结不允许次日营业
							int dayCount = posService.getDayCountState(param.getTenancy_id(), param.getStore_id(), param.getData());*/

							//当天已经做过日结
							 if(dayCount == 1 || dayCount == -2) {
								 result.setData(posService.checkDayBegain(param.getTenancy_id(), param.getStore_id(), param.getData()));
								 result.setCode(Constant.CODE_SUCCESS);
								 result.setMsg(Constant.CHECK_DAY_BEGAIN_SUCCESS);
								// 设置门店状态为营业中
								storeStateOpen = true;
								// 清空桌台
								 posService.updateTableState(param.getTenancy_id(), param.getStore_id());
							 }else {
								 JSONObject para = JSONObject.fromObject(param.getData().get(0));
								 Date reportDate = DateUtil.parseDate(ParamUtil.getDateStringValue(para,"report_date"));
	//							 Date before_date = new Date(reportDate.getTime() - 24 * 60 * 60 * 1000);
								 Date before_date = new Date(reportDate.getTime());
								 result.setCode(Constant.CODE_STORE_EXCEPTION);
								 result.setMsg(Constant.STORE_HEAD + DateUtil.format(before_date, "yyyy-MM-dd") + Constant.NOT_DAY_COUNT + Constant.SAAS_DAY_COUNT);
							 }
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.CHECK_DAY_BEGAIN_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.CHECK_DAY_BEGAIN_FAILURE);
					}
					break;
				case DAY_END:
					try
					{
						posService.dayReport(param, result);
						/*
						 * 打烊成功后，取消调用数据增量上传，直接进行数据重传。
						 */
						logger.info("打烊后进行数据重传...");
						final Data paramUpload = param.clone();
						Runnable r = new Runnable() {							
							@Override
							public void run() {
								try {
									uploadDataService.UploadData(paramUpload);        // 数据重传
									posDataSyncService.reIndex();                     // 打烊成功后，进行数据库索引重建操作，时间间隔为一周一次。
								} catch (Exception e) {
									logger.error("打烊后执行数据重传出现异常："+e.getMessage());
								}								
							}
						};						
						Thread t = new Thread(r);
						t.start();						
						
//						//new AutoUploadDataRunnable().run();
//						Thread t = new Thread(new AutoUploadDataRunnable());
//						t.start();
	                    // 设置门店状态为非营业
	                    storeStateOpen = false;
                        //检查是否需要升级
                        checkBohUpgrade();
	                }
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.DAY_END_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.DAY_END_FAILURE);
					}
					break;
				case CANC_DAY_END:
					try
					{
						posService.cancDayEnd(param, result);
						if(result!=null && (result.getCode()==Constant.CODE_SUCCESS)){//取消打烊成功
							// 设置门店状态为营业中
							storeStateOpen = true;
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.CANC_DAY_END_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.CANC_DAY_END_FAILURE);
					}
					break;
				case DATATRANS:
					try
					{
	//					data = posService.dataTrans(param);
						data = uploadDataService.UploadData(param);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.DATATRANS_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.DATATRANS_FAILURE);
					}
					break;
				case GIVE_WAITER:
					try
					{
						posService.findWaiter(param, result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.QUERY_WAITER_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.QUERY_WAITER_FAILURE);
					}
					break;
				case CHANG_SHIFT:
					try
					{
	//					data = posService.changeShift(param);
						JSONObject printJson = new JSONObject();
//						posService.signOffAndChangeShift(param.getTenancy_id(), param.getStore_id(), param.getData(),printJson);

						DBContextHolder.setTenancyid(param.getTenancy_id());
						PosChangeShiftService shiftDataService = SpringConext.getApplicationContext().getBean(PosChangeShiftService.class);
						shiftDataService.signOffAndChangeShift(param.getTenancy_id(), param.getStore_id(), param.getData(),printJson);
//						try
//						{
//							if (posPrintNewService.isNONewPrint(param.getTenancy_id(), param.getStore_id()))
//							{// 如果启用新的打印模式
//								posPrintNewService.posPrintByFunction(param.getTenancy_id(), param.getStore_id(), FunctionCode.CHANG_SHIFT, printJson);
//							}
//							else
//							{
//								Data printData = Data.get(param);
//								List<JSONObject> PrintList = new ArrayList<JSONObject>();
//								PrintList.add(printJson);
//								printData.setData(PrintList);
//								posPrintService.printPosBill(printData, result);
//							}
//						}
//						catch (Exception e1)
//						{
//							logger.error(e1);
//						}
						result.setCode(Constant.CODE_SUCCESS);
						result.setMsg(Constant.CHANGE_SHIFT_SUCCESS);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.CHANGE_SHIFT_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.CHANGE_SHIFT_FAILURE);
					}
					break;
				case GET_SHIFTDATA:
					try
					{
//						posService.getShiftData(param, result);
						DBContextHolder.setTenancyid(param.getTenancy_id());
						PosChangeShiftService shiftDataService = SpringConext.getApplicationContext().getBean(PosChangeShiftService.class);
						shiftDataService.getShiftData(param, result);

					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.GET_SHIFT_DATA_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.GET_SHIFT_DATA_FAILURE);
					}
					break;

				case CANCBILL:
					try
					{
						JSONObject printJson = new JSONObject();
						posDishService.cancelBill(param, codeService, result, printJson);
						try{
						if (printJson != null)
						{
							try
							{
								if(posPrintNewService.isNONewPrint(printJson.optString("tenancy_id"),printJson.optInt("store_id"))){//如果启用新的打印模式
									posPrintNewService.posPrintByFunction(printJson.optString("tenancy_id"),printJson.optInt("store_id"),FunctionCode.CANCBILL,printJson);
								}else{
									List<Integer> ids = posPrintService.orderRetreat(printJson.optString("tenancy_id"), printJson.optString("bill_num"), printJson.optInt("store_id"), printJson.optString("oper"), printJson.optString("oper_type"), printJson.optString("rwids"));
									posPrintService.orderPrint(printJson.optString("tenancy_id"), printJson.optString("bill_num"), printJson.optInt("store_id"), ids);
								}
							}
							catch (Exception e)
							{
								result.setCode(Constant.CODE_SUCCESS);
								result.setMsg(Constant.CANC_BILL_SUCCESS_PRING_FAILURE);
							}
						}
					}catch(Exception e1){
						logger.error(e1);
					}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.CANC_BILL_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.CANC_BILL_FAILURE);
					}
					break;
				case REGAIN_BILL:
					try
					{
						JSONObject printJson = new JSONObject();

						posDishService.recoveryBill(param, result,printJson);
//						String tenantId = param.getTenancy_id();
//						Integer organId = param.getStore_id();
//						Map<String, Object> maps = ReqDataUtil.getDataMap(param);
//						String billno = ParamUtil.getStringValue(maps, "bill_num", true, PosErrorCode.NOT_NULL_BILL_NUM);
//						// 数据上传
//						posDishService.upload(tenantId,organId+"","","","",billno);
						posDishService.upload(param);


						try
						{
							String source = param.getSource();
							if (SysDictionary.SOURCE_MOVE.contains(source)||SysDictionary.SOURCE_ORDER_DEVICE.equals(source))
							{
								posPrintService.getBindOptNum(printJson.optString("tenancy_id"), printJson.optInt("store_id"), printJson);
							}
							printJson.put("source", source);

							BillForPaymentPrintThread billForPaymentPrintThread = new BillForPaymentPrintThread(printJson, posPrintNewService, posPrintService);
							BillForPaymentPrintThread.billForPaymentThreadPool.execute(billForPaymentPrintThread);
						}
						catch (Exception e1)
						{
							logger.error(e1);
						}

					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.RECOVERY_BILL_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.RECOVERY_BILL_FAILURE);
					}
					break;
				case GET_ITEM_GUQINGCOUNT:
					try
					{
						data = posService.getSoldOutCount(param);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.GET_GUQINGCOUNT_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.GET_GUQINGCOUNT_FAILURE);
					}
					break;
				case CHANG_GUQINGCOUNT:
					try
					{
//						data = posDishService.modSoldOutCount(param);
						
						DBContextHolder.setTenancyid(param.getTenancy_id());
						PosSoldOutService poldOutService = (PosSoldOutService)SpringConext.getApplicationContext().getBean(PosSoldOutService.NAME);
						data = poldOutService.modSoldOutCount(param);
						
						//沽清上传到redis
//						String tenantId = param.getTenancy_id();
//						Integer organId = param.getStore_id();
						posDataSyncService.dataSyncForPosSoldOut(param.getTenancy_id(), String.valueOf(param.getStore_id()));
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.CHANG_GUQINGCOUNT_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.CHANG_GUQINGCOUNT_FAILURE);
					}
					break;
				case GET_DISH:
					try
					{
						data = posService.getDishByClass(param);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.GET_DISH_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.GET_DISH_FAILURE);
					}
					break;
				case CALLUP_ITEM:
					try
					{
						posDishService.callupFood(param, result, json);

						try
						{
							if (json != null && !json.isEmpty())
							{
								if (posPrintNewService.isNONewPrint(json.optString("tenancy_id"), json.optInt("store_id")))
								{// 如果启用新的打印模式
									String isPrintWaitCallItem = OrderUtil.getSysPara(json.optString("tenancy_id"), json.optInt("store_id"), "SFDYDJCP");
                                    if("1".equals(isPrintWaitCallItem)){
                                    	posPrintNewService.posPrintByMode(json.optString("tenancy_id"), json.optInt("store_id"), SysDictionary.PRINT_CODE_1115, json);
                                    }else{
                                    	json.remove("model");
    									posPrintNewService.posPrintByFunction(json.optString("tenancy_id"), json.optInt("store_id"), FunctionCode.CALLUP_ITEM, json);
                                    }
								}
								else
								{
									List<Integer> ids = posPrintService.orderOperChef(json.optString("tenancy_id"), json.optString("bill_num"), json.optInt("store_id"), json.optString("oper"), json.optString("oper_type"), json.optString("rwids"));
									posPrintService.orderPrint(json.optString("tenancy_id"), json.optString("bill_num"), json.optInt("store_id"), ids);
								}
							}
						}
						catch (Exception e1)
						{
							logger.error(e1);
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.CALL_UP_DISH_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.CALL_UP_DISH_FAILURE);
					}
					break;
				case CARD_PASSWORD:
				case CUSTOMER_INFO:
				case CUSTOMERINFO:
				case CUSTOMER_CREDIT:
				case CUSTOMER_LOGIN:
				case CUSTOMER_CARD_REPORT_LOST:
				case LOST_CARD:
				case CUSTOMER_CARD_ARTIFICIAL:
				case CUSTOMERTRANS:
				case COUPONS:
				case ACTIVITY:
				case INCORPORATION_INFO:
				case INCORPORATION_GZ:
				case CUSTOMER_BILL:
				case DYNAMIC_CODE:
				case CUSTOMER_CREDIT_CASH:
				case ACTIVITY_CHZ:
				case NEW_CUSTOMER_INFO:
					try
					{
						if (customerService.commonPostCheck(param))
						{
							customerService.formatParam(param, jsobj);
							customerService.commonPost(jsobj.toString(), result);
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.REQUEST_MEMBER_SERVICE_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.REQUEST_MEMBER_SERVICE_FAILURE);
					}
					break;
                case CARD_TRANSFER_HXL:
                    try {
                        customerService.commonPost(jsobj.toString(), result);
                    } catch (SystemException se) {
                        buildSysExceptionData(se, result, Constant.REQUEST_MEMBER_SERVICE_FAILURE);
                    } catch (Exception e) {
                        buildExceptionData(e, result, Constant.REQUEST_MEMBER_SERVICE_FAILURE);
                    }
                    break;
				case CUSTOMER_CARD_ACTIVATION:
					try
					{
						if (customerService.commonPostCheck(param))
						{
	/*						if(Oper.add  == param.getOper())
							{
								customerService.formatParam(param, jsobj);
								customerService.activateCustomerCardPost(jsobj.toString(), result);
							}*/
							if(Oper.add==param.getOper()){//会员发卡
								result = customerCardActivationServlet.customerCardActivation(param);
								System.out.println("result "+JsonUtil.DataToJson(result));
							}else if(Oper.find==param.getOper()){//发卡状态查询
								result = customerCardActivationServlet.customerCardActivationStatus(param);
								System.out.println("result "+JsonUtil.DataToJson(result));
							}else if(Oper.precreate==param.getOper()){//会员发卡获取二维码
								String webPath = PathUtil.getWebRoot(request);
								result = customerCardActivationServlet.customerCardThirdPaymentPrecreate(param, webPath);
								System.out.println("result "+JsonUtil.DataToJson(result));
							}else if(Oper.cancle==param.getOper()){//会员发卡取消付款
								result = customerCardActivationServlet.customerCardCancelThirdPayment(param);
								System.out.println("result "+JsonUtil.DataToJson(result));
							}else if(Oper.barcode==param.getOper()){//会员发卡扫码支付（B扫C）
								result = customerCardActivationServlet.customerCardThirdPaymentBarcode(param);
								System.out.println("result "+JsonUtil.DataToJson(result));
							}
						}
					}catch (SystemException se){
						buildSysExceptionData(se, result, Constant.REQUEST_MEMBER_SERVICE_FAILURE);
					}catch (Exception e){
						buildExceptionData(e, result, Constant.REQUEST_MEMBER_SERVICE_FAILURE);
					}
					break;
				case BUY_VIPLEVEL:
					try
					{
						if (Oper.find == param.getOper())
			 			{
							customerService.commonPost(jsobj.toString(), result);
						}
						else if (Oper.add == param.getOper())
						{
							customerService.buyVipLevel(param, result);
						}
						else if (Oper.delete == param.getOper())
						{
							customerService.deleteBuyVipLevel(param, result);
						}else if (Oper.check == param.getOper())
						{
							customerService.commonPost(jsobj.toString(), result);
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.REQUEST_MEMBER_SERVICE_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.REQUEST_MEMBER_SERVICE_FAILURE);
					}
					break;
				case NEW_COUPONS:
					try
					{
						if (Oper.check == param.getOper())
						{
							if (customerService.commonPostCheck(param))
							{
//								customerService.verifyCoupon(param, result);
								customerCouponService.checkCustomerCoupon(param, result);
							}
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.REQUEST_MEMBER_SERVICE_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.REQUEST_MEMBER_SERVICE_FAILURE);
					}
					break;
				case CUSTOMER_CARD:
					try
					{
						if (Oper.find == param.getOper())
						{
							customerService.commonPost(jsobj.toString(), result);
						}
						else
						{
							if (customerService.commonPostCheck(param))
							{
								customerService.formatParam(param, jsobj);
								customerService.commonPost(jsobj.toString(), result);
							}
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.REQUEST_MEMBER_SERVICE_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.REQUEST_MEMBER_SERVICE_FAILURE);
					}
					break;

				case DEBIT_CARD:
					try
					{
						if (Oper.find == param.getOper()){

							customerService.commonDebitPay(jsobj.toString(), result);
						}

					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.REQUEST_MEMBER_SERVICE_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.REQUEST_MEMBER_SERVICE_FAILURE);
					}
					break;

				case CUSTOMER_CARD_CONSUME:
					try
					{
	//					if (customerService.commonPostCheck(param))
	//					{
	//						customerService.formatParam(param, jsobj);
							if(Oper.add == param.getOper())
							{
								customerCardService.customerCardConsumePost(param, result);
							}
							else if(Oper.update == param.getOper())
							{
								customerCardService.customerCardCancelConsumePost(param, result,"Y");
							}
	//					}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.REQUEST_MEMBER_SERVICE_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.REQUEST_MEMBER_SERVICE_FAILURE);
					}
					break;
				case CUSTOMER_CARD_RECHARGE:
					try
					{
	//					if (customerService.commonPostCheck(jsobj.toString()))
	//					{
	//						customerService.formatParam(param, jsobj);
	//						customerService.rechargePost(jsobj.toString(), result);
	//					}

	//					if(Oper.prepare==param.getOper())
	//					{
	//						customerService.rechargePreparePost(param,codeService, result);
	//					}
	//					else if(Oper.add==param.getOper())
	//					{
	//						customerService.rechargePost(param, result);
	//					}
	//					else if(Oper.update==param.getOper())
	//					{
	//						customerService.rechargeCancelPost(param, result);
	//					}
	//					else
	//					{
	//						result.setCode(Constant.CODE_PARAM_FAILURE);
	//						result.setMsg(Constant.NOT_EXISTS_OPER);
	//					}

						if (Oper.precreate == param.getOper())
						{
							String path = PathUtil.getWebRoot(request);
							result = cardRechargeServlet.precreatePaymentForRecharge(param, path);
						}
						else if (Oper.barcode == param.getOper())
						{
							result = cardRechargeServlet.barcodePaymentForRecharge(param);
						}
						else if (Oper.find == param.getOper())
						{
							result = cardRechargeServlet.queryCustomerCardRechargeForState(param);
						}
						else if (Oper.add == param.getOper())
						{
							result = cardRechargeServlet.customerCardRecharge(param);
						}
						else if (Oper.cancle == param.getOper())
						{
							result = cardRechargeServlet.cancelThirdPaymentForCustomerCardRecharge(param);
						}
						else if (Oper.update == param.getOper())
						{
							result = cardRechargeServlet.cancelCustomerCardRecharge(param);
						}
						else
						{
							result.setCode(Constant.CODE_PARAM_FAILURE);
							result.setMsg(Constant.NOT_EXISTS_OPER);
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.REQUEST_MEMBER_SERVICE_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.REQUEST_MEMBER_SERVICE_FAILURE);
					}
					break;
				case CUSTOMER_CARD_FILL_CARD:
					try
					{
						if (customerService.commonPostCheck(param))
						{
							customerService.formatParam(param, jsobj);
							customerService.fillCustomerCardPost(jsobj.toString(), result);
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.REQUEST_MEMBER_SERVICE_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.REQUEST_MEMBER_SERVICE_FAILURE);
					}
					break;
				case CUSTOMER_CARD_BACK_OUT:
					try
					{
						if (customerService.commonPostCheck(param))
						{
							customerService.formatParam(param, jsobj);
							customerService.backOutCustomerCardPost(jsobj.toString(), result);
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.REQUEST_MEMBER_SERVICE_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.REQUEST_MEMBER_SERVICE_FAILURE);
					}
					break;
				case CUSTOMER_CARD_MERGE:
					try
					{
						if (customerService.commonPostCheck(param))
						{
							customerService.formatParam(param, jsobj);
							customerService.mergeCustomerCardPost(jsobj.toString(), result);
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.REQUEST_MEMBER_SERVICE_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.REQUEST_MEMBER_SERVICE_FAILURE);
					}
					break;
				case CUSTOMER_VIPPRICE:
					try
					{
						posDishService.customerVipPrice(jsobj, result);
	//					customerService.customerVipPrice(jsobj, result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.VIP_PRICE_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.VIP_PRICE_FAILURE);
					}
					break;
				case VIEW_INVOICE:
					try
					{
						posService.findInvoice(param, result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.QUERY_INVOICE_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.QUERY_INVOICE_FAILURE);
					}
					break;
				case SET_INVOICE:
					try
					{
						posService.setInvoice(param, result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.SET_INVOICE_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.SET_INVOICE_FAILURE);
					}
					break;
				case CHECK_TABLE_LOCK:
					try
					{
						List<JSONObject> lockTables = posService.findLockTable(param);
						if (lockTables.size() == 0)
						{
							List<LockTable> list = new ArrayList<LockTable>();
							LockTable lock = new LockTable();
							list.add(lock);
							buildDataResult(Constant.CODE_SUCCESS, Constant.QUERY_LOCK_TABLE_SUCCESS, list, result);
						}
						else
						{
							buildDataResult(Constant.CODE_SUCCESS, Constant.QUERY_LOCK_TABLE_SUCCESS, lockTables, result);
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.QUERY_LOCK_TABLE_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.QUERY_LOCK_TABLE_FAILURE);
					}
					break;
				case CANC_GIVE:
					try
					{
						posDishGiveService.cancGiveItem(param, result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.CANC_GIVE_ITEM_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.CANC_GIVE_ITEM_FAILURE);
					}
					break;
				case CHECK_POS_LOGIN:
					try
					{
						posService.checkPosLogin(param, result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.CHECK_POS_LOGIN_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.CHECK_POS_LOGIN_FAILURE);
					}
					break;
				case CLEAR_SERVICE:
					try
					{
						posDishService.cancServiceFee(param, result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.CANC_SERVICE_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.CANC_SERVICE_FAILURE);
					}
					break;
				case PRINT_BILL:
					try
					{
						json = JSONObject.fromObject(param.getData().get(0));
						String source = param.getSource();
						if (SysDictionary.SOURCE_MOVE.contains(source)||SysDictionary.SOURCE_ORDER_DEVICE.equals(source))
						{
							posPrintService.getBindOptNum(param.getTenancy_id(), param.getStore_id(), json);
						}
	                    json.put("source", source);
	                    List<JSONObject> list = new ArrayList<JSONObject>();
						list.add(json);
						param.setData(list);

						if (posPrintNewService.isNONewPrint(param.getTenancy_id(), param.getStore_id()))
						{// 如果启用新的打印模式
							posPrintNewService.posPrintByMode(param.getTenancy_id(), param.getStore_id(), json.optString("print_code"), json);
						}
						else
						{
							posPrintService.printPosBill(param, result);
						}

						try
						{
							posService.updateDevicesDataState();
						}
						catch (Exception e)
						{
							e.printStackTrace();
						}

					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.PRINT_BILL_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.PRINT_BILL_FAILURE);
					}
					break;
				case PRETR_PRINT_BILL:
					try
					{
						String webPath = PathUtil.getWebRoot(request);
						posPaymentService.pretrPrintBill(param,webPath, result);

					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.PRINT_BILL_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.PRINT_BILL_FAILURE);
					}
					break;
				case ITEM_SORT:
					try
					{
						posService.findItemSort(param, result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.PRINT_BILL_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.PRINT_BILL_FAILURE);
					}
					break;
				case CHECK_AUTHORITY:
					if (Oper.check == param.getOper())
					{
						try
						{
							loginService.checkAuth(param, result);
						}
						catch (Exception e)
						{
							buildExceptionData(e, result, Constant.CHECK_AUTHRITORY_FAILURE);
						}
					}
					else if (Oper.find == param.getOper())
					{
						try
						{
							List<SysModule> sysModule = loginService.getSysModule(param);

							buildDataResult(Constant.CODE_SUCCESS, Constant.GET_SYSMODULE_SUCCESS, sysModule, result);
						}
						catch (Exception e)
						{
							buildExceptionData(e, result, Constant.GET_SYSMODULE_FAILURE);
						}
					}
					else
					{
						result.setCode(Constant.CODE_PARAM_FAILURE);
						result.setMsg(Constant.NOT_EXISTS_OPER);
					}
					break;
				case GIVE_SUGGEST:
					try
					{
						if (Oper.check == param.getOper())
						{
							boolean success = posService.updateGiveSuggest(param.getTenancy_id(), param.getStore_id(), param.getData());
							if (success)
							{
								result.setCode(Constant.CODE_SUCCESS);
								result.setMsg(Constant.GIVE_SUGGEST_SUCCESS);
								result.setSuccess(true);
							}
							else
							{
								result.setCode(Constant.CODE_PARAM_FAILURE);
								result.setMsg(Constant.GIVE_SUGGEST_FAILURE);
								result.setSuccess(false);
							}
						}
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.GIVE_SUGGEST_FAILURE);
					}
					break;
				case GET_TABLEMAXSERIAL:
					try
					{
						posDishService.getMaxSerial(param, result);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.GET_MAX_SERIAL_FAILURE);
					}
					break;
				case GET_ORGANID:
					try
					{
						loginService.getOrganId(param, result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.GET_ORGAN_ID_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.GET_ORGAN_ID_FAILURE);
					}
					break;
				case LOADBILL:
					try
					{
						posDishService.loadPauseBill(param, result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.LOAD_BILL_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.LOAD_BILL_FAILURE);
					}
					break;
				case CLEAR_BILLITEM:
					try
					{
						posDishService.clearBillItem(param, result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.CLEAR_BILL_ITEM_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.CLEAR_BILL_ITEM_FAILURE);
					}
					break;
				case TEMPORARY:
					try
					{
						posDishService.temporary(param, result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.PAUSE_ORDER_DISH_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.PAUSE_ORDER_DISH_FAILURE);
					}
					break;

				case DEVICE_APPLY:
					if (Oper.add == param.getOper())
					{
						try
						{
							posService.deviceApply(param.getTenancy_id(), param.getStore_id(), param.getData(), result);
						}
						catch (SystemException se)
						{
							buildSysExceptionData(se, result, Constant.DEVICE_APPLY_FAILURE);
						}
						catch (Exception e)
						{
							buildExceptionData(e, result, Constant.DEVICE_APPLY_FAILURE);
						}
					}
					else if (Oper.find == param.getOper())
					{
						try
						{
							JSONObject obj = posService.findDevice(param.getTenancy_id(), param.getStore_id(), param.getData());

							if (obj.optInt("count") > 0)
							{
								List<JSONObject> objList = new ArrayList<JSONObject>();
								obj.remove("count");
								objList.add(obj);

								result.setMsg(Constant.DEVICE_SELECT_SUCCESS);
								result.setCode(Constant.CODE_SUCCESS);
								result.setData(objList);
							}
							else
							{
								result.setMsg(Constant.DEVICE_SELECT_FAILURE);
								result.setCode(Constant.CODE_NULL_DATASET);
							}
						}
						catch (SystemException se)
						{
							buildSysExceptionData(se, result, Constant.DEVICE_APPLY_FAILURE);
						}
						catch (Exception e)
						{
							buildExceptionData(e, result, Constant.DEVICE_APPLY_FAILURE);
						}
					}
					else
					{
						result.setCode(Constant.CODE_PARAM_FAILURE);
						result.setMsg(Constant.NOT_EXISTS_OPER);
					}
					break;
				case CHANGET:
					try
					{
						posService.blindSuccession(param, result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.CHANGET_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.CHANGET_FAILURE);
					}
					break;
				case CHANG_SHIFT_CHECK:
					try
					{
						DBContextHolder.setTenancyid(param.getTenancy_id());
						PosChangeShiftService shiftDataService = SpringConext.getApplicationContext().getBean(PosChangeShiftService.class);
						
						shiftDataService.checkChangShift(param, result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.CHECK_CHANGSHIFT_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.CHECK_CHANGSHIFT_FAILURE);
					}
					break;
				case CHECK_ITEM:
					try
					{
						posDishService.findBillItem(param, result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.QUERY_CHECK_ITEM_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.QUERY_CHECK_ITEM_FAILURE);
					}
					break;
				case RETREAT_ITEM:
					try
					{
						posDishService.singleRetreatItem(param, codeService, result, json);
						try
						{
							if (json != null)
							{
								if (posPrintNewService.isNONewPrint(json.optString("tenancy_id"), json.optInt("store_id")))
								{// 如果启用新的打印模式
									posPrintNewService.posPrintByFunction(json.optString("tenancy_id"), json.optInt("store_id"), FunctionCode.RETREAT_ITEM, json);
								}
								else
								{
									List<Integer> ids = posPrintService.orderRetreat(json.optString("tenancy_id"), json.optString("bill_num"), json.optInt("store_id"), json.optString("oper"), json.optString("oper_type"), json.optString("rwids"));
									posPrintService.orderPrint(json.optString("tenancy_id"), json.optString("bill_num"), json.optInt("store_id"), ids);
								}
							}
						}
						catch (Exception e1)
						{
							logger.error(e1);
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.SINGLE_RETREAT_ITEM_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.SINGLE_RETREAT_ITEM_FAILURE);
					}
					break;
				case OPENSYY:
					try
					{
						posService.openSyy(param, result);
						// 设置门店状态为营业中
						storeStateOpen = true;
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.OPEN_SYY_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.OPEN_SYY_FAILURE);
					}
					try {//修复日始后开班前的订单数据
						JSONObject para = JSONObject.fromObject(param.getData().get(0));
						ordersManagementService.repairOrdersBeforeOpenSyy(param.getTenancy_id(),param.getStore_id(),para.optString("report_date"));
					} catch (Exception e2) {
						e2.printStackTrace();
					}
					break;
				case CLOSESYY:
					try
					{
						posService.closeSyy(param, result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.CLOSE_SYY_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.CLOSE_SYY_FAILURE);
					}
					break;
				case DELETE_VIPPRICE:
					try
					{
						customerService.deleteVipPrice(param, result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.DELETE_VIP_PRICE_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.DELETE_VIP_PRICE_FAILURE);
					}
					break;
				case QUERY_KSSY:
					try
					{
						posService.querySyy(param, result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.QUERY_KSSY_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.QUERY_KSSY_FAILURE);
					}
					break;

				case CLOSE_TABLE:
					try
					{
						posServiceSelf.closeTable(param, result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.CLOSE_TABLE_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.CLOSE_TABLE_FAILURE);
					}
					break;
				case OPENTABLE_ORDERING:
					try
					{
						data = posServiceSelf.openTableOrderDish(param);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.OPEN_TABLE_ORDER_DISH_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.OPEN_TABLE_ORDER_DISH_FAILURE);
					}
					break;
				case BILL_PAYMENT_SELF:
					try
					{
						JSONObject printJson = new JSONObject();
						posServiceSelf.posBillPaymentSelf(param, result,printJson);

						if(null !=printJson)
						{
							String tenancyId=printJson.optString("tenancy_id");
							int storeId =printJson.optInt("store_id");
							String billNum = printJson.optString("bill_num");

							String isPrint = printJson.optString("isprint");
							if (posPrintNewService.isNONewPrint(tenancyId, storeId))
							{// 如果启用新的打印模式
								posPrintNewService.posPrintByFunction(tenancyId, storeId, FunctionCode.ORDERING, printJson);

								if ("Y".equalsIgnoreCase(isPrint))
								{
									posPrintNewService.posPrintByFunction(tenancyId, storeId, FunctionCode.BILL_PAYMENT, printJson);
								}
							}
							else
							{
								List<Integer> list = posPrintService.orderChef(tenancyId, billNum, storeId, "0");
								posPrintService.orderPrint(tenancyId, billNum, storeId, list);

								if ("Y".equalsIgnoreCase(isPrint))
								{
									printJson.put("print_code", "1003");
									printJson.put("print_type", "0");
									printJson.put("mode", "0");

									List<JSONObject> printList = new ArrayList<JSONObject>();
									printList.add(printJson);

									Data printData = new Data();
									printData.setTenancy_id(tenancyId);
									printData.setStore_id(storeId);
									printData.setType(Type.PRINT_BILL);
									printData.setData(printList);

									Data resultData = new Data();

									int printCount = printJson.optInt("print_count");
									if (printCount < 1)
									{
										printCount = 1;
									}
									for (int i = 0; i < printCount; i++)
									{
										posPrintService.printPosBill(printData, resultData);
									}
								}
							}
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.ORDER_PAYMENT_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.ORDER_PAYMENT_FAILURE);
					}
					break;
				case CLEAR_BILLITEM_SELF:
					try
					{
						posServiceSelf.clearBillItemSelf(param, result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.CLEAR_BILL_ITEM_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.CLEAR_BILL_ITEM_FAILURE);
					}
					break;
				case CLEAR_PAYMENT_SELF:
					try
					{
						posServiceSelf.clearPaymentSelf(param, result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.CLEAR_BILL_ITEM_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.CLEAR_BILL_ITEM_FAILURE);
					}
					break;
				case TABLE_STATE_SELF:
					try
					{
						List<PosTableFind> ftables = posServiceSelf.tableStateSelf(param);
						if (ftables.size() == 0)
						{
							buildDataResult(Constant.CODE_NULL_DATASET, Constant.FIND_TABLE_NOT_EXISTS, ftables, result);
						}
						else
						{
							buildDataResult(Constant.CODE_SUCCESS, Constant.FIND_TABLE_SUCCESS, ftables, result);
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.FIND_TABLE_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.FIND_TABLE_FAILURE);
					}
					break;
				case RETREAT_FOOD_SELF:
					try
					{
						posServiceSelf.retreatFood(param.getTenancy_id(), param.getStore_id(), param.getData(), result, json, param.getSource());
						try{
							if (json != null && json.containsKey("rwids"))
							{
								if(posPrintNewService.isNONewPrint(json.optString("tenancy_id"),json.optInt("store_id"))){//如果启用新的打印模式
									posPrintNewService.posPrintByFunction(json.optString("tenancy_id"),
											json.optInt("store_id"),
											FunctionCode.RETREAT_FOOD_SELF,
											json);
								}else{
									List<Integer> ids = posPrintService.orderRetreat(json.optString("tenancy_id"), json.optString("bill_num"), json.optInt("store_id"), json.optString("oper"), json.optString("oper_type"), json.optString("rwids"));
									posPrintService.orderPrint(json.optString("tenancy_id"), json.optString("bill_num"), json.optInt("store_id"), ids);
								}

							}
						}catch(Exception e1){
							logger.error(e1);
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.RETREAT_DISH_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.RETREAT_DISH_FAILURE);
					}
					break;

				case QUERY_PAYMENT_SELF:
					try
					{
						JSONObject printJson = new JSONObject();
						posServiceSelf.queryBillPayment(param, result,printJson);

						if(null !=printJson)
						{
							String tenancyId=printJson.optString("tenancy_id");
							int storeId =printJson.optInt("store_id");
							String billNum = printJson.optString("bill_num");

							String isPrint = printJson.optString("isprint");
							if (posPrintNewService.isNONewPrint(tenancyId, storeId))
							{// 如果启用新的打印模式
								posPrintNewService.posPrintByFunction(tenancyId, storeId, FunctionCode.ORDERING, printJson);

								if ("Y".equalsIgnoreCase(isPrint))
								{
									posPrintNewService.posPrintByFunction(tenancyId, storeId, FunctionCode.BILL_PAYMENT, printJson);
								}
							}
							else
							{
								List<Integer> list = posPrintService.orderChef(tenancyId, billNum, storeId, "0");
								posPrintService.orderPrint(tenancyId, billNum, storeId, list);

								if ("Y".equalsIgnoreCase(isPrint))
								{
									printJson.put("print_code", "1003");
									printJson.put("print_type", "0");
									printJson.put("mode", "0");

									List<JSONObject> printList = new ArrayList<JSONObject>();
									printList.add(printJson);

									Data printData = new Data();
									printData.setTenancy_id(tenancyId);
									printData.setStore_id(storeId);
									printData.setType(Type.PRINT_BILL);
									printData.setData(printList);

									Data resultData = new Data();

									int printCount = printJson.optInt("print_count");
									if (printCount < 1)
									{
										printCount = 1;
									}
									for (int i = 0; i < printCount; i++)
									{
										posPrintService.printPosBill(printData, resultData);
									}
								}
							}
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.ORDER_PAYMENT_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.ORDER_PAYMENT_FAILURE);
					}
					break;
				case UPDATE_PAYMENT_SELF:
					try
					{
						JSONObject printJson = new JSONObject();
						posServiceSelf.updateBillPaymentState(param, result,printJson);
						if(null !=printJson)
						{
							String tenancyId=printJson.optString("tenancy_id");
							int storeId =printJson.optInt("store_id");
							String billNum = printJson.optString("bill_num");

							String isPrint = printJson.optString("isprint");
							if (posPrintNewService.isNONewPrint(tenancyId, storeId))
							{// 如果启用新的打印模式
								posPrintNewService.posPrintByFunction(tenancyId, storeId, FunctionCode.ORDERING, printJson);

								if ("Y".equalsIgnoreCase(isPrint))
								{
									posPrintNewService.posPrintByFunction(tenancyId, storeId, FunctionCode.BILL_PAYMENT, printJson);
								}
							}
							else
							{
								List<Integer> list = posPrintService.orderChef(tenancyId, billNum, storeId, "0");
								posPrintService.orderPrint(tenancyId, billNum, storeId, list);

								if ("Y".equalsIgnoreCase(isPrint))
								{
									printJson.put("print_code", "1003");
									printJson.put("print_type", "0");
									printJson.put("mode", "0");

									List<JSONObject> printList = new ArrayList<JSONObject>();
									printList.add(printJson);

									Data printData = new Data();
									printData.setTenancy_id(tenancyId);
									printData.setStore_id(storeId);
									printData.setType(Type.PRINT_BILL);
									printData.setData(printList);

									Data resultData = new Data();

									int printCount = printJson.optInt("print_count");
									if (printCount < 1)
									{
										printCount = 1;
									}
									for (int i = 0; i < printCount; i++)
									{
										posPrintService.printPosBill(printData, resultData);
									}
								}
							}
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.ORDER_PAYMENT_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.ORDER_PAYMENT_FAILURE);
					}
					break;
				case CUSTOMER_CREDIT_PLUS:
					try
					{
						posDishService.customerCreditPlus(param, result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.CUSTOMER_CREDIT_PLUS_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.CUSTOMER_CREDIT_PLUS_FAILURE);
					}
					break;
				case BILL_PAYMENT_RECORD:
					try
					{
						result.setData(posServiceSelf.getBillPaymentRecord(param.getTenancy_id(), param.getStore_id(), param.getData()));
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.BILL_PAYMENT_RECORD_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.BILL_PAYMENT_RECORD_FAILURE);
					}
					break;
				case CARD_RECHARGE_RECORD:
					try
					{
						result.setData(customerService.getCardRechargeRecord(param.getTenancy_id(), param.getStore_id(), param.getData(), param.getPagination()));
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.CARD_RECHARGE_RECORD_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.CARD_RECHARGE_RECORD_FAILURE);
					}
					break;
				case CLOCK_TABLE_SELF:
					try
					{
						posServiceSelf.lockOrUnlockTable(param, result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.LOCK_UNLOCK_TABLE_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.LOCK_UNLOCK_TABLE_FAILURE);
					}
					break;
				case OPT_BIND_DEVICES:
					try
					{
						if (Oper.add == param.getOper())
						{
							message = Constant.OPT_BIND_DEVICES_ADD_FAILURE;
							posService.addOptBindDevices(param.getTenancy_id(), param.getStore_id(), param.getData());
							result.setCode(Constant.CODE_SUCCESS);
							result.setMsg(Constant.OPT_BIND_DEVICES_ADD_SUCCESS);
						}
						else if (Oper.find == param.getOper())
						{
							message = Constant.OPT_BIND_DEVICES_FIND_FAILURE;
							result.setData(posService.findOptBindDevices(param.getTenancy_id(), param.getStore_id(), param.getData(), param.getPagination()));
						}
						else if (Oper.cancle == param.getOper())
						{
							message = Constant.OPT_BIND_DEVICES_CANCLE_FAILURE;
							posService.cancleOptBindDevices(param.getTenancy_id(), param.getStore_id(), param.getData());
							result.setCode(Constant.CODE_SUCCESS);
							result.setMsg(Constant.OPT_BIND_DEVICES_CANCLE_SUCCESS);
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, message);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, message);
					}
					break;
				case Get_POS_OPT:
					try
					{
						message = Constant.Get_POS_OPT_FAILURE;
						result.setData(posService.findOptPosData(param.getTenancy_id(), param.getStore_id(), param.getData(), param.getPagination()));
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, message);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, message);
					}
					break;

				case CASHIER_RECEIVE_TOTAL:
					try
					{
						message = Constant.CASHIER_RECEIVE_TOTAL_FAILURE;
						result.setData(cashierReceiveService.totalCashierReceive(param.getTenancy_id(), param.getStore_id(), param.getData(), param.getPagination()));
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, message);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, message);
					}
					break;

				case CASHIER_RECEIVE_DETAILS:
					try
					{
						message = Constant.CASHIER_RECEIVE_TOTAL_FAILURE;
						result.setData(cashierReceiveService.cashierReceiveDetails(param.getTenancy_id(), param.getStore_id(), param.getData(), param.getPagination()));
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, message);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, message);
					}
					break;

				case POS_CASHIER_RECEIVE:
					try
					{
						if (Oper.add == param.getOper())
						{
							message = Constant.POS_CASHIER_RECEIVE_ADD_FAILURE;

							String tenancyId = param.getTenancy_id();
							int storeId = param.getStore_id();
							JSONObject printJson = new JSONObject();
							cashierReceiveService.addCashierReceive(tenancyId, storeId, param.getData(), printJson);
							try
							{
								if (posPrintNewService.isNONewPrint(tenancyId, storeId))
								{// 如果启用新的打印模式
									posPrintNewService.posPrintByFunction(tenancyId, storeId, FunctionCode.POS_CASHIER_RECEIVE, printJson);
								}
								else
								{
									posPrintService.customerPrint(tenancyId, storeId, printJson);
								}
							}
							catch (Exception e1)
							{
								logger.error(e1);
							}
							result.setCode(Constant.CODE_SUCCESS);
							result.setMsg(Constant.POS_CASHIER_RECEIVE_ADD_SUCCESS);
						}
						else if (Oper.find == param.getOper())
						{
							message = Constant.POS_CASHIER_RECEIVE_FIND_FAILURE;
							result.setData(cashierReceiveService.findCashierReceive(param.getTenancy_id(), param.getStore_id(), param.getData(), param.getPagination()));
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, message);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, message);
					}
					break;

				case CHECK_RECEIVE:
					try
					{
						cashierReceiveService.checkReceive(param, result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.CHECK_RECEIVE_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.CHECK_RECEIVE_FAILURE);
					}
					break;
				case GET_STORE_OPT_STATE:
					try
					{
						result.setData(posService.getStoreOptState(param.getTenancy_id(), param.getStore_id(), param.getData()));
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.GET_STORE_OPT_STATE_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.GET_STORE_OPT_STATE_FAILURE);
					}
					break;
				case EXTRACT_BILLS:
					try
					{
						posService.extractBills(param, result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.EXTRACT_BILLS_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.EXTRACT_BILLS_FAILURE);
					}
					break;
				case GET_DETAIL_CHANG_SHIFT:
					try
					{
						DBContextHolder.setTenancyid(param.getTenancy_id());
						PosChangeShiftService shiftDataService = SpringConext.getApplicationContext().getBean(PosChangeShiftService.class);
						
						shiftDataService.getDetailChangShift(param, result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.GET_DETAIL_CHANGSHIFT_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.GET_DETAIL_CHANGSHIFT_FAILURE);
					}
					break;
				case OPEN_CASHBOX_LOG:
					try
					{
						posService.saveOpenBoxLog(param, result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.EXTRACT_BILLS_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.EXTRACT_BILLS_FAILURE);
					}
					break;
				case QUERY_EXTRACT_BILLS:
					try
					{
						posService.getExtractBillsHistory(param, result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.GET_EXTRACT_BILLS_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.GET_EXTRACT_BILLS_FAILURE);
					}
					break;
				case CANCBILL_SELF:
					try
					{
						JSONObject printJson = new JSONObject();
						posServiceSelf.cancelBillSelf(param, codeService, result, printJson);
						if (printJson != null)
						{
							try
							{
								if(posPrintNewService.isNONewPrint(printJson.optString("tenancy_id"),printJson.optInt("store_id"))){//如果启用新的打印模式
									posPrintNewService.posPrintByFunction(printJson.optString("tenancy_id"),
											printJson.optInt("store_id"),
											FunctionCode.CANCBILL,
											printJson);
								}else {
									List<Integer> ids = posPrintService.orderRetreat(
													printJson.optString("tenancy_id"),
													printJson.optString("bill_num"),
													printJson.optInt("store_id"),
													printJson.optString("oper"),
													printJson.optString("oper_type"),
													printJson.optString("rwids"));
									posPrintService.orderPrint(
											printJson.optString("tenancy_id"),
											printJson.optString("bill_num"),
											printJson.optInt("store_id"), ids);
								}
							}
							catch (Exception e)
							{
								logger.error(e);
								result.setCode(Constant.CODE_SUCCESS);
								result.setMsg(Constant.CANC_BILL_SUCCESS_PRING_FAILURE);
							}
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.CANC_BILL_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.CANC_BILL_FAILURE);
					}
					break;
				case PAYMENT_REPEAT:
					try
					{
						JSONObject printJson = new JSONObject();
						posServiceSelf.billPaymentRepeat(param, result, printJson);

						try
						{
							posPrintService.printPosBillForPayment(printJson, posPrintNewService);
						}
						catch (Exception e)
						{
							e.printStackTrace();
							logger.error(e );
						}
	//					if ( null != printJson ){
	//						String tenancyId = printJson.optString("tenancy_id");
	//						int storeId = printJson.optInt("store_id");
	//						String formatState = printJson.optString("format_state");
	//						String formatMode = printJson.optString("format_mode");
	//						String billNum = printJson.optString("bill_num");
	//						String isPrint = printJson.optString("isprint");
	//						if ("Y".equalsIgnoreCase(isPrint)){//
	//							printJson.put("print_code", "1002");
	//							printJson.put("print_type", "0");
	//							printJson.put("mode", "0");
	//							List<JSONObject> printList = new ArrayList<JSONObject>();
	//							printList.add(printJson);
	//
	//							Data printData = new Data();
	//							printData.setTenancy_id(tenancyId);
	//							printData.setStore_id(storeId);
	//							printData.setType(Type.PRINT_BILL);
	//							printData.setData(printList);
	//
	//							Data resultData = new Data();
	//							int printCount = printJson.optInt("print_count");
	//
	//							for (int i = 0; i < printCount; i++)
	//							{
	//								posPrintService.printPosBill(printData, resultData);
	//							}
	//
	//							if ("2".equals(formatState) || "02".equals(formatMode))
	//							{
	//								List<Integer> list = posPrintService.orderChef(tenancyId, billNum, storeId, "0");
	//								posPrintService.orderPrint(tenancyId, billNum, storeId, list);
	//							}
	//						}
	//					}
					}catch(SystemException se)
					{
						buildSysExceptionData(se, result, Constant.SELF_RETRY_PAYMENT_REPEAT_FAILURE);
					}catch(Exception e){
						buildExceptionData(e, result, Constant.SELF_RETRY_PAYMENT_REPEAT_FAILURE);
					}
					break;
				case TOTAL_BILL_INFO:
					try {
						if (Oper.find == param.getOper()) {
							posDishService.totalBillInfo(param, result);
						}
					}
					catch(SystemException se) {
						buildSysExceptionData(se, result, Constant.TOTAL_BILL_INFO_FAILURE);
					}
					catch (Exception e) {
						buildExceptionData(e, result, Constant.TOTAL_BILL_INFO_FAILURE);
					}
					break;
				case KVS_INFO:
					try
					{
						if (Oper.find == param.getOper())
						{
							posDishService.getKvsInfo(param, result);
						}
						if (Oper.update == param.getOper())
						{
							posDishService.updateKvsMopState(param, result);
						}
						if (Oper.check == param.getOper()) {  //查询等待列表
							posDishService.findAllBill(param, result);
						}
						if (Oper.query == param.getOper()) {
							posDishService.checkIsMop(param, result);
						}
						if(Oper.notice == param.getOper()){  //单独叫号推送
							Comet4jUtil.sendMessage2All(Comet4jUtil.POS_ANDROID, JSONObject.fromObject(param).toString());
						}
						if (Oper.load == param.getOper()) {  //查询已叫号数据
							posDishService.getmopedData(param, result);
						}
						if (Oper.complete  == param.getOper()){  //出餐员出菜状态修改
							posDishService.dishOut(param,result);
						}
					}
					catch(SystemException se)
					{
						buildSysExceptionData(se, result, Constant.FIND_KVS_BILL_FAILURE);
					}
					catch(Exception e)
					{
						buildExceptionData(e, result, Constant.FIND_KVS_BILL_FAILURE);
					}
					break;
				case KVS_TIME:
					try
					{
						posDishService.getMealConsume(param, result);
					}catch(SystemException se)
					{
						buildSysExceptionData(se, result, Constant.FIND_KVS_MEAL_CONSUME_FAILURE);
					}catch(Exception e){
						buildExceptionData(e, result, Constant.FIND_KVS_MEAL_CONSUME_FAILURE);
					}
					break;
				case SAVE_BANK_TRANDING_LOG:
					if(Oper.add  == param.getOper()){
						try
						{
							posService.saveBankTrandingLog(param, result);
						}
						catch (SystemException se)
						{
							buildSysExceptionData(se, result, Constant.SAVE_BANK_TRANDING_LOG_FAILURE);
						}
						catch (Exception e)
						{
							buildExceptionData(e, result, Constant.SAVE_BANK_TRANDING_LOG_FAILURE);
						}
					}else if(Oper.find  == param.getOper()){
						try
						{
							posService.queryBankTrandingLog(jsobj,param, result);
						}
						catch (SystemException se)
						{
							buildSysExceptionData(se, result, Constant.QUERY_BANK_TRANDING_LOG_FAILURE);
						}
						catch (Exception e)
						{
							buildExceptionData(e, result, Constant.QUERY_BANK_TRANDING_LOG_FAILURE);
						}
					}
					break;
				case SURPLUS_DISCOUNT_AMOUNT:
					try
					{
						if (Oper.find  == param.getOper())
						{
							result = posDishService.getSurplusDiscountAmount(param);
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.SURPLUS_DISCOUNT_AMOUNT_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.SURPLUS_DISCOUNT_AMOUNT_FAILURE);
					}
					break;

				case THIRD_COUPONS:
					try
					{
						if (Oper.prepare  == param.getOper())
						{
							posPaymentService.prepareThirdCoupons(param, result);
						}else if (Oper.check  == param.getOper())
                        {
                            posPaymentService.checkCoupons(param, result);
                        }
						else
						{
							result.setCode(Constant.CODE_PARAM_FAILURE);
							result.setMsg(Constant.NOT_EXISTS_OPER);
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.THIRD_COUPONS_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.THIRD_COUPONS_FAILURE);
					}
					break;

				case BILL_PAYMENT_CANCEL:
					try
					{
						posPaymentService.billPaymentService(param, result,null);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.BILL_PAYMENT_CANCEL_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.BILL_PAYMENT_CANCEL_FAILURE);
					}
					break;
				case COPY_POS_BILL:
					try{
						JSONObject obj = new JSONObject();
						posDishService.copyPostBill(param,result,obj);
						if (obj.size()>0){
							posDishService.upload(obj.optString("tenancy_id"),obj.optString("store_id"),"",obj.optString("tableCode"),"",obj.optString("billNum"));
	//						new  Thread(new DataUploadRunnable(obj.optString("tenancy_id"),obj.optString("store_id"),"",obj.optString("tableCode"),"",obj.optString("billNum"))).start();
						}
					}catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.COPY_POS_BILL_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.COPY_POS_BILL_FAILURE);
					}
					break;

				case PRINT_TEMPLATE: //delphi 打印模板接收
					try
					{
						posService.uploadPrintFormat(param);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.UPLOAD_PRINTFORMAT_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.UPLOAD_PRINTFORMAT_FAILURE);
					}
					break;
				case NEW_PRINT_TEMPLATE: //delphi 新打印模板接收
					try
					{
						posService.uploadPrintFormat(param);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.UPLOAD_PRINTFORMAT_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.UPLOAD_PRINTFORMAT_FAILURE);
					}
					break;
				case CHANG_TASTE: //修改口味
					try
					{
						posService.changTaste(param,result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.CHANG_TASTE_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.CHANG_TASTE_FAILURE);
					}
					break;
				case DISCOUNT:
					break;
				case PRINT:
					try
					{
						if (Oper.notice == param.getOper())
						{
							posService.pushPrintState(param, result);
						}
						else if (Oper.query == param.getOper()) {
							posService.getPrinterState(param, result);
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.QUERY_PRINTER_STATE_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.QUERY_PRINTER_STATE_FAILURE);
					}
					break;

				/**
				 * 获取CRM Token
				 */
				case XMD_CRM_TOKEN:
				{
					try
					{
						data = posNewCrmService.getToken(param);
					}
					catch (Exception e)
					{

						buildExceptionData(e, result, Constant.GET_CRM_TOKEN_FAILURE);
					}
					break;
				}
				/**
				 * 发卡
				 */
				case XMD_CRM_ACTIVATION:
				{
					try
					{
						data = posNewCrmService.creditCard(param);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.GET_CRM_FK_FAILURE);
					}
					break;
				}
				/**
				 * 充值
				 */
				case XMD_CRM_RECHARGE:
				{
					try
					{
						if (Oper.add == param.getOper())
						{
							data = posNewCrmService.recharge(param);
						}
						else if (Oper.cancle == param.getOper())
						{
							data = posNewCrmService.cancelRecharge(param);
						}

					}
					catch (Exception e)
					{
						if (Oper.add == param.getOper())
						{
							buildExceptionData(e, result, Constant.GET_CRM_CZ_FAILURE);
						}
						else if (Oper.cancle == param.getOper())
						{
							buildExceptionData(e, result, Constant.GET_CRM_CANCEL_FAILURE);
						}
					}
					break;
				}
				/**
				 * 会员查询
				 */
				case XMD_CRM_MEMBER:
				{
					try
					{
						data = posNewCrmService.findUserInfo(param);

					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.CRM_MEMBER_FAILURE);
					}
					break;
				}
				case COUPONS_DISH:
					try
					{
						if (Oper.check == param.getOper())
						{
							posService.getCouponsValiData(param, result);
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.LOCAL_COUPONS_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.LOCAL_COUPONS_FAILURE);
					}
					break;
				case NOCODE_COUPONSINFO:
					try
					{
//						posService.getNocodeCouponsValiData(param, result);
						DBContextHolder.setTenancyid(param.getTenancy_id());
						NocodeCouponsService couponsService = SpringConext.getApplicationContext().getBean(NocodeCouponsService.class);
						couponsService.getNocodeCouponsValiData(param, result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.LOCAL_COUPONS_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.LOCAL_COUPONS_FAILURE);
					}
					break;
				case NOCODE_COUPONS:
					try
					{
						if (Oper.add == param.getOper())
						{
//							posService.getNocodeCoupons(param, result);
							DBContextHolder.setTenancyid(param.getTenancy_id());
							NocodeCouponsService couponsService = SpringConext.getApplicationContext().getBean(NocodeCouponsService.class);
							couponsService.checkNocodeCoupons(param, result);
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.LOCAL_COUPONS_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.LOCAL_COUPONS_FAILURE);
					}
					break;
				case DEVICES_REFRESH:
					try
					{
						if (Oper.check == param.getOper())
						{
							JSONObject paramJson = JSONObject.fromObject(param.getData().get(0));
							posService.getDevicesDataState(paramJson.getString("devices_code"), result);
						}
						else if (Oper.update == param.getOper())
						{
							JSONObject paramJson = JSONObject.fromObject(param.getData().get(0));
							posService.updateDevicesByCode(paramJson.getString("devices_code"), "1");
							result.setCode(Constant.CODE_SUCCESS);
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.DEVICES_REFRESH_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.DEVICES_REFRESH_FAILURE);
					}
					break;
				case POS_TOTAL_AMOUNT:
					try{
						posService.findTotalAmount(param,result);
					}catch (Exception e){
						buildExceptionData(e, result, Constant.FIND_TOTAL_FAILURE);
					}
				case QUICK_PASS_FIND:
					try
					{
						if (Oper.find  == param.getOper())
						{
							data=quickPassService.findList(param);
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.QUICK_PASS_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.QUICK_PASS_FAILURE);
					}
					break;
				case QUICK_PASS_DEL:
					try
					{
						if (Oper.delete  == param.getOper())
						{
							quickPassService.deleteData(param);
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.QUICK_PASS_DEL_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.QUICK_PASS_DEL_FAILURE);
					}
					break;
				case WRONG_THIRD_PAYMENT:
					try
					{
						posPaymentService.wrongPayDetailQuery(param, result);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.WRONG_THIRD_PAYMENT_FAILURE);
					}
					break;
				case HISTORY_BILL:
					if (Oper.query == param.getOper())
					{
						try
						{
							posDishService.queryPosBillHistory(param, result);
						}
						catch (SystemException se)
						{
							buildSysExceptionData(se, result, Constant.FIND_BILL_FAILURE);
						}
						catch (Exception e)
						{
							buildExceptionData(e, result, Constant.FIND_BILL_FAILURE);
						}
					}else{
						result.setCode(Constant.CODE_PARAM_FAILURE);
						result.setMsg(Constant.NOT_EXISTS_OPER);
					}
					break;

				case BILL_BOOKED:
					try
					{
						if(Oper.add == param.getOper() ){
							posDishService.billBookedOrder(param, result,json);
						}else{
							posDishService.newFindPosBillBooked(param, result);
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.BILL_BOOKED_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.BILL_BOOKED_FAILURE);
					}
					break;
				//奉送认证
				case GIVE_AUTH:
					try
					{
						posDishGiveService.giveAuth(param, result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.GIVE_DISH_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.GIVE_DISH_FAILURE);
					}
					break;
				//折让认证
				case DISCOUNT_AUTH:
					try
					{
						posDiscountService.discountAuth(param, result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.BILL_DISCOUNT_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.BILL_DISCOUNT_FAILURE);
					}
					break;
				//查询有效活动
				case ACTIVITY_INFO:
					try
					{
						posActivityService.getValidActivity(param,result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.FIND_ACTIVITY_INFO_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.FIND_ACTIVITY_INFO_FAILURE);
					}
					break;
				//取消活动
				case ACTIVITY_INFO_CANCEL:
					try
					{
						posActivityService.cancelActivity(param,result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.FIND_ACTIVITY_INFO_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.FIND_ACTIVITY_INFO_FAILURE);
					}
					break;
				//参加活动
				case JOIN_IN_ACTIVITY:
					try
					{
						posActivityService.joinInActivity(param,result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.JOIN_IN_ACTIVITY_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.JOIN_IN_ACTIVITY_FAILURE);
					}
					break;
				//批量交款
				case POS_CASHIER_RECEIVE_BATCH:
					try
					{
						if (Oper.add == param.getOper())
						{
							message = Constant.POS_CASHIER_RECEIVE_ADD_FAILURE;

							String tenancyId = param.getTenancy_id();
							int storeId = param.getStore_id();
							List<JSONObject> printJsonList = new ArrayList<JSONObject>();
	//						JSONObject printJson = new JSONObject();
							cashierReceiveService.addCashierReceiveBatch(tenancyId, storeId, param, printJsonList);
							try
							{
								if(printJsonList.size() > 0) {
									for(int i = 0 ; i < printJsonList.size(); i++) {
										JSONObject printJson = printJsonList.get(i);
										if (posPrintNewService.isNONewPrint(tenancyId, storeId)) {// 如果启用新的打印模式
											posPrintNewService.posPrintByFunction(tenancyId, storeId, FunctionCode.POS_CASHIER_RECEIVE, printJson);
										} else {
											posPrintService.customerPrint(tenancyId, storeId, printJson);
										}
									}
								}
							}
							catch (Exception e1)
							{
								logger.error(e1);
							}
							result.setCode(Constant.CODE_SUCCESS);
							result.setMsg(Constant.POS_CASHIER_RECEIVE_ADD_SUCCESS);
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, message);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, message);
					}
					break;
	            case CASHIER_LIMIT_VLIDATE:
	                try
	                {
	                    data = cashierReceiveService.cashierLimitVlidate(param);
	                }
	                catch (SystemException se)
	                {
	                    buildSysExceptionData(se, result, Constant.GET_CASHIER_LIMIT_VLIDATE_FAILURE);
	                }
	                catch (Exception e)
	                {
	                    buildExceptionData(e, result, Constant.GET_CASHIER_LIMIT_VLIDATE_FAILURE);
	                }
	                break;
	            case ACEWILL_CUSTOMER:
	            	//TODO 微生活
					try
					{
						if (Oper.find == param.getOper()) //过滤不可用券
						{
							result = acewillCustomerServlet.queryAcewillCustomerInfo(param);
						}
						else if (Oper.precreate == param.getOper())
						{
							result = acewillCustomerServlet.billPaymentForAcewillPreview(param);
						}
						else if (Oper.commit == param.getOper())
						{
							result = acewillCustomerServlet.billPaymentForAcewillCommit(param);
						}
						else if (Oper.sendcode == param.getOper())
						{
							result = acewillCustomerServlet.sendCodeAcewillCustomer(param);
						}
						else if (Oper.update == param.getOper())//修改会员信息
						{
							result = acewillCustomerServlet.editAcewillCustomerUserInfo(param);
						}
						else if (Oper.query == param.getOper())//收银员、门店储值规则
						{
							result = acewillCustomerServlet.queryAcewillBasicData(param);
						}
						else if (Oper.scan == param.getOper())//储值预览
						{
							result = acewillCustomerServlet.previewAcewillCharge(param);
						}
						else if (Oper.add == param.getOper())//储值提交
						{
							result = acewillCustomerServlet.commitAcewillCharge(param);
						}
						else if(Oper.charge_precreate==param.getOper()) //储值获取二维码
						{
							String path = PathUtil.getWebRoot(request);
							result = acewillCustomerServlet.precreatePaymentForRecharge(param, path);
						}
						else if(Oper.charge_barcode==param.getOper()) //储值扫码支付
						{
							result = acewillCustomerServlet.barcodePaymentForRecharge(param);
						}
						else if(Oper.order_state_query==param.getOper()) //储值支付状态查询
						{
							result = acewillCustomerServlet.queryCustomerCardRechargeForState(param);
						}
						else if(Oper.charge_list==param.getOper()) //储值记录列表
						{
							result = acewillCustomerServlet.findAcewillChargeUser(param);
						}
						else if(Oper.cancle==param.getOper()) //储值取消
						{
							result = acewillCustomerServlet.cancelAcewillCharge(param);
						}
						else if(Oper.charge_revoke==param.getOper()) //储值撤销
						{
							result = acewillCustomerServlet.revokeAcewillCharge(param);
						}
						else if(Oper.consume_list==param.getOper()) //指定会员消费记录列表
						{
							result = acewillCustomerServlet.findAcewillConsumeUser(param);
						}
						else if(Oper.credit_rule==param.getOper()) //商家积分换礼规则
						{
							result = acewillCustomerServlet.findAcewillCreditRule(param);
						}
						else if(Oper.credit_exchange==param.getOper()) //积分换礼
						{
							result = acewillCustomerServlet.creditExchangeGifts(param);
						}
						else if(Oper.load==param.getOper())//查询商户等级
						{
							result = acewillCustomerServlet.findAcewillCustomerGrade(param);
						}
						else if(Oper.init==param.getOper()) //实体卡 制卡 、验卡
						{
							result = acewillCustomerServlet.makeAcewillCustomerCard(param);
						}
						else if(Oper.open==param.getOper()) //激活实体卡 开卡
						{
							result = acewillCustomerServlet.activatedAcewillCustomerCard(param);
						}
						else if(Oper.print==param.getOper()) //补打微生活消费小票
						{
							result = acewillCustomerServlet.makeUpCustomerCardConsumePrint(param);
						}
						else
						{
							result.setCode(Constant.CODE_PARAM_FAILURE);
							result.setMsg(Constant.NOT_EXISTS_OPER);
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.REQUEST_MEMBER_SERVICE_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.REQUEST_MEMBER_SERVICE_FAILURE);
					}
					break;
	            case ACEWILL_CUSTOMER_COUPONS:
	            	//TODO 微生活
					try
					{
						if (Oper.check == param.getOper())
						{
							result = acewillCustomerServlet.checkAcewillCoupons(param);
						}
						else
						{
							result.setCode(Constant.CODE_PARAM_FAILURE);
							result.setMsg(Constant.NOT_EXISTS_OPER);
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.REQUEST_MEMBER_SERVICE_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.REQUEST_MEMBER_SERVICE_FAILURE);
					}
					break;
				//组合套餐
				case COMBO_SET_MEAL:
					try
					{
	//					comboSetMealService.setMeal(param,result);
						comboSetMealService.comboSetMeal(param,result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.COMBO_SET_MEAL_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.COMBO_SET_MEAL_FAILURE);
					}
					break;
				case LOCKING:
					try
					{
						cloudBillService.locking(param, result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.POS_CLOUD_LOCK_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.POS_CLOUD_LOCK_FAILURE);
					}
					break;
				case FORCED_UNLOCKING:
					try
					{
						cloudBillService.forcedUnlocking(param, result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.POS_CLOUD_LOCK_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.POS_CLOUD_LOCK_FAILURE);
					}
					break;
				case CLOSE_BILL_LOCK:
					try
					{
						cloudBillService.closeBillLock(param, result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.POS_CLOUD_CLOSED_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.POS_CLOUD_CLOSED_FAILURE);
					}
					break;
				case BILL_LOCK_STATE:
					try
					{
						cloudBillService.findBillLockState(param, result);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.POS_CLOUD_CLOSED_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.POS_CLOUD_CLOSED_FAILURE);
					}
					break;
	            case CHANGE_DINNER_TYPE:
	                try
	                {
	                    posDishService.changeDinnerType(param, result);
	                }
	                catch (SystemException se)
	                {
	                    buildSysExceptionData(se, result, Constant.CHANGE_DINNER_TYPE_FAILURE);
	                }
	                catch (Exception e)
	                {
	                    buildExceptionData(e, result, Constant.CHANGE_DINNER_TYPE_FAILURE);
	                }
	                break;
	            case QUERY_ITEM_LIST:
	                try
	                {
	                    posDishService.queryItemList(param, result);
	                }
	                catch (SystemException se)
	                {
	                    buildSysExceptionData(se, result, Constant.QUERY_ITEM_LIST_FAILURE);
	                }
	                catch (Exception e)
	                {
	                    buildExceptionData(e, result, Constant.QUERY_ITEM_LIST_FAILURE);
	                }
	                break;
	            case SINGLE_CANCEL_DISCOUNT:
					try
					{
						result = posDiscountService.singleCancelDiscount(param);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.SINGLE_CANCEL_DISCOUNT_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.SINGLE_CANCEL_DISCOUNT_FAILURE);
					}
	            	break;
	            case OPEN_CASHBOX:
	                try
	                {
	                      posService.openCashBox(param, result);
	                }
	                catch (SystemException se)
	                {
	                    buildSysExceptionData(se, result, Constant.OPEN_CASHBOX_FAILURE);
	                }
	                catch (Exception e)
	                {
	                    buildExceptionData(e, result, Constant.OPEN_CASHBOX_FAILURE);
	                }
	                break;
	            case CHANGE_BILL_HEADER:
	                try
	                {
	                    posDishService.changeBillHeader(param, result);
	                }
	                catch (SystemException se)
	                {
	                    buildSysExceptionData(se, result, Constant.MODIFY_BILL_HEADER_FAILURE);
	                }
	                catch (Exception e)
	                {
	                    buildExceptionData(e, result, Constant.MODIFY_BILL_HEADER_FAILURE);
	                }
	                break;
	            case CHANGE_ITEM_WAITER:
	                try
	                {
	                    posDishService.changeItemWaiter(param, result);
	                }
	                catch (SystemException se)
	                {
	                    buildSysExceptionData(se, result, Constant.MODIFY_ITEM_WAITER_FAILURE);
	                }
	                catch (Exception e)
	                {
	                    buildExceptionData(e, result, Constant.MODIFY_ITEM_WAITER_FAILURE);
	                }
	                break;
	             case OPENTABLE_AND_BINGMEMBER:  //开台并绑定会员
	            	 openTable(param, result, json);
	            	 List<JSONObject> openTables = (List<JSONObject>) result.getData();
	            	 try
	                 {
	                     if (openTables == null || openTables.size() == 0)
	                     {
	                         result.setCode(Constant.CODE_NULL_DATASET);
	                         result.setMsg(Constant.OPEN_TABLE_FAILURE);
	                     }
	                     else
	                     {
	                    	 posDishServlet.optableAndCustomerCreditPlus(param,result,openTables);
	                         result.setData(openTables);
	                         result.setCode(Constant.CODE_SUCCESS);
	                         result.setMsg(Constant.OPEN_TABLE_SUCCESS);
	                     }
	                 }
	                 catch (SystemException se)
	                 {
	                     buildSysExceptionData(se, result, Constant.OPEN_TABLE_FAILURE);
	                 }
	                 catch (Exception e)
	                 {
	                     buildExceptionData(e, result, Constant.OPEN_TABLE_FAILURE);
	                 }
	            	 break;
	             case 	QUERY_MEMBER_HISTORYBILL: //查询会员历史账单
	            	 try {
	            		 customerService.queryMemberHistoryBill(param,result);
					 } catch (SystemException se)
	                 {
	                     buildSysExceptionData(se, result, Constant.QUERY_MEMBER_HISTORYBILL_SUCCESS);
	                 }
	                 catch (Exception e)
	                 {
	                     buildExceptionData(e, result, Constant.QUERY_MEMBER_HISTORYBILL_FAILURE);
	                 }
	            	 break;
	             case QUERY_MEMBER_HISTORYBILLDETAIL:  //查询会员历史账单明细
	            	 try {
	            		 customerService.queryMemberHistoryBillDetail(param,result);
					 } catch (SystemException se)
	                 {
	                     buildSysExceptionData(se, result, Constant.QUERY_MEMBER_HISTORYBILLDETAIL_SUCCESS);
	                 }
	                 catch (Exception e)
	                 {
	                     buildExceptionData(e, result, Constant.QUERY_MEMBER_HISTORYBILLDETAIL_FAILURE);
	                 }
	            	 break;
	             case TABLESTATE_AND_MEMBERSTATE: //查询桌台会员
	            	 try {
	            		 customerService.queryTableStateAndMemberState(param,result);
					 } catch (SystemException se)
	                 {
	                     buildSysExceptionData(se, result, Constant.QUERY_TABLESTATE_AND_MEMBERSTATE_SUCCESS);
	                 }
	                 catch (Exception e)
	                 {
	                     buildExceptionData(e, result, Constant.QUERY_TABLESTATE_AND_MEMBERSTATE_FAILURE);
	                 }
	            	 break;
				case CRM_INVOICE:
					try
					{
						if (Oper.find == param.getOper())
						{
							Map<String,String> map = (Map<String,String>) param.getData().get(0);
							//查询会员充值可开票金额
							result = customerInvoiceService.getInvoiceBalancePost(param.getTenancy_id(), param.getStore_id(), map.get("customer_id"));
						}
						else if (Oper.add == param.getOper())
						{
							result = customerInvoiceService.insertInvoicePost(param.getTenancy_id(), param);
						}
						else if (Oper.load == param.getOper())
						{
							result = customerInvoiceService.getInvoiceListPost(param.getTenancy_id(), param);
						}
						else
						{
							result.setCode(Constant.CODE_PARAM_FAILURE);
							result.setMsg(Constant.NOT_EXISTS_OPER);
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.SET_INVOICE_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.SET_INVOICE_FAILURE);
					}
                break;
            case TAKE_ORDERING: //D5厨房订单核销(接单)
				try{
					takeOrderingService.takeOrderingEntry(param, result, json);
				}
				catch (SystemException se)
				{
					buildSysExceptionData(se, result, Constant.TAKE_ORDERING_FAILURE);
				}
				catch (Exception e)
				{
					buildExceptionData(e, result, Constant.TAKE_ORDERING_FAILURE);
				}
				break;
            case CANCAL_DISCOUNT_DELETE_MEMBER: //取消折扣和会员记录
            	try
				{
            		data = posDiscountService.cancalDiscountDeleteMeber(param);
//					String tenantId = param.getTenancy_id();
//					Integer organId = param.getStore_id();
//					Map<String, Object> maps = ReqDataUtil.getDataMap(param);
//					String billno = ParamUtil.getStringValue(maps, "bill_num", true, PosErrorCode.NOT_NULL_BILL_NUM);
//					// 数据上传
//					posDishService.upload(tenantId,organId+"","","","",billno);
            		posDishService.upload(param);
				}
				catch (SystemException se)
				{
					buildSysExceptionData(se, result, Constant.BILL_DISCOUNT_FAILURE);
				}
				catch (Exception e)
				{
					buildExceptionData(e, result, Constant.BILL_DISCOUNT_FAILURE);
				}
				break;
            case ORDER_DELIVERY: //查询配送订单地址
            	try
				{
            		orderDeliveryService.findOrderDeliveryAddress(param,result);
				}
				catch (SystemException se)
				{
					buildSysExceptionData(se, result, Constant.FIND_ORDER_DELIVERY_ADDRESS_FAILURE);
				}
				catch (Exception e)
				{
					buildExceptionData(e, result, Constant.FIND_ORDER_DELIVERY_ADDRESS_FAILURE);
				}
				break;
            case ADD_ORDER_DELIVERY: //新增配送订单
            	try
				{
            		orderDeliveryService.addOrderDelivery(param,result);
				}
				catch (SystemException se)
				{
					buildSysExceptionData(se, result, Constant.ADD_ORDER_DELIVERY_FAILURE);
				}
				catch (Exception e)
				{
					buildExceptionData(e, result, Constant.ADD_ORDER_DELIVERY_FAILURE);
				}
				break;
            case ADD_BOH_ORDER_DELIVERY: //新增BOH配送订单
            	try
				{
            		orderDeliveryService.addBohOrderDelivery(param,result);
				}
				catch (SystemException se)
				{
					buildSysExceptionData(se, result, Constant.ADD_ORDER_DELIVERY_FAILURE);
				}
				catch (Exception e)
				{
					buildExceptionData(e, result, Constant.ADD_ORDER_DELIVERY_FAILURE);
				}
				break;
            case FIND_EXCEPTION_DELIVERY_ORDER: //异常配送订单查询
            	try
				{
            		orderDeliveryService.findExceptionDeliveryOrder(param,result);
				}
				catch (Exception e)
				{
					buildExceptionData(e, result, Constant.FIND_EXCEPTION_DELIVERY_ORDER_FAILURE);
				}
				break;
            case BOH_STATE_CHECK:
                try
                {
                    if (Oper.check == param.getOper())
                    {
                        JSONObject state=new JSONObject();
                        state.put("current_state", PosNotifyOberver.CURRENT_STATE);
                        result.setData(Arrays.asList(state));
                        result.setCode(Constant.CODE_SUCCESS);
                        result.setMsg(Constant.APP_VERSION_SUCCESS);
                        result.setSuccess(true);
                    }
                }
                catch (Exception e)
                {
                    buildExceptionData(e, result, Constant.INNER_ERROR);
                }
                break;
            case CHECK_UNCLOSED_BILL:
                try
                {
                    posService.checkUnclosedBill(param, result);
                }
                catch (SystemException se)
                {
                    buildSysExceptionData(se, result, Constant.CHECK_UNCLOSED_BILL_FAILURE);
                }
                catch (Exception e)
                {
                    buildExceptionData(e, result, Constant.CHECK_UNCLOSED_BILL_FAILURE);
                }
                break;
			case CUSTOMER_SIGN:
				try
				{
					if (Oper.find == param.getOper())
					{
						result = customerSignService.getCustomerSign(param.getTenancy_id(), param);
					}
					else if (Oper.add == param.getOper())
					{
						result = customerSignService.addCustomerSign(param.getTenancy_id(), param);
					}
					else if (Oper.praise == param.getOper())
					{
						result = customerSignService.praiseSign(param.getTenancy_id(), param);
					}
					else
					{
						result.setCode(Constant.CODE_PARAM_FAILURE);
						result.setMsg(Constant.NOT_EXISTS_OPER);
					}
				}
				catch (SystemException se)
				{
					buildSysExceptionData(se, result, Constant.FIND_CUSTOMER_SIGN_FAILURE);
				}
				catch (Exception e)
				{
					buildExceptionData(e, result, Constant.FIND_CUSTOMER_SIGN_FAILURE);
				}
				break;
				
                case PROGRESS_MONITOR:
                    posService.progressMonitor(param, result);
                    break;
                    
				case THIRD_PAYMENT_REFUND:
					try
					{
						if (Oper.find == param.getOper())
						{
							message = Constant.FIND_THIRD_PAYMENT_REFUND;
							DBContextHolder.setTenancyid(param.getTenancy_id());
							ThirdPaymentRefundService couponsService = SpringConext.getApplicationContext().getBean(ThirdPaymentRefundServiceImp.class);
							result = couponsService.getThirdPaymentRefund(param);
						}
						else if (Oper.retry == param.getOper())
						{
							message = Constant.THIRD_PAYMENT_REFUND_RETRY;
							DBContextHolder.setTenancyid(param.getTenancy_id());
							ThirdPaymentRefundService couponsService = SpringConext.getApplicationContext().getBean(ThirdPaymentRefundServiceImp.class);
							result = couponsService.thirdPaymentRefundRepeat(param);
						}
						else
						{
							result.setCode(Constant.CODE_PARAM_FAILURE);
							result.setMsg(Constant.NOT_EXISTS_OPER);
						}

					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, message.concat(Constant.CODE_FAILURE_MSG));
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, message.concat(Constant.CODE_FAILURE_MSG));
					}
					break;
                case GET_ITEM_STAMP_LIST:
                    posService.getItemStampList(param, result);
                    break;
                case GET_ITEM_STAMP_BY_BUSINESS_AREA:
                    posService.getItemStampByBusinessArea(param, result);
                    break;
                case GET_ITEM_STAMP_DETAIL_BY_TABLE_CODE:
                    posService.getItemStampDetailByTableCode(param, result);
                    break;
                case GET_ITEM_STAMP_DETAIL_BY_ITEM:
                    posService.getItemStampDetailByItem(param, result);
                    break;
                case GET_ITEM_STAMP_BY_PRINT_ID:
                    posService.getItemStampByPrintId(param, result);
                    break;
                case ITEM_STAMP:
                    posService.itemStamp(param, result);
                    break;
                case NEW_PRINT_PUBLICDB:
                	try
					{
						posService.uploadPrintFormat(param);
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, Constant.UPLOAD_PRINTFORMAT_FAILURE);
					}
					catch (Exception e)
					{
						buildExceptionData(e, result, Constant.UPLOAD_PRINTFORMAT_FAILURE);
					}
					break;
                case HQ_PRINTER:
                    posService.getHqPrinter(param,result);
                    break;

                case PRINTER_BG:
                    if (Oper.find == param.getOper()){
                        posService.queryPrinterBg(param,result);
                    }
                    if(Oper.add==param.getOper()){
                        posService.savePrinterBg(param,result);
                    }
                    break;
                case HQ_BASEDATA_PULL:
                    posService.baseDataPull(param,result);
                    break;

                case GET_FREE_SPACE:
                    posService.getFreeSpace(result);
                    break;
				case FORBIDDEN_ITEM:
					if (Oper.add == param.getOper()) {
						posService.addForbiddenItem(param, result);
					} else if (Oper.delete == param.getOper()) {
						posService.delForbiddenItem(param, result);
					} else if (Oper.check == param.getOper()) {
						posService.checkForbiddenItem(param, result);
					}
					break;
				case SETTING_SETMEAL_DETAILS:
					if (Oper.update == param.getOper()) {
						posService.updateItemComboDetailsDefault(param, result);
					} else if (Oper.delete == param.getOper()) {
						posService.recoverItemComboDetailsDefault(param, result);
					}else if (Oper.check == param.getOper()){
						posService.checkItemComboDetailsDefault(param, result);
					}
					break;
				case SETTING_SHIFTS_TIMES:
					if (Oper.update == param.getOper()) {
						posService.updateSettingShiftsTimes(param, result);
					}
					break;
				case COMBINE_TABLE:
					if (Oper.check == param.getOper()) {
						try {
							posDishService.combineTable(param, result, json);
							try {
								posService.updateDevicesDataState();
							} catch (Exception e) {
								e.printStackTrace();
								logger.info("Exception:" + ExceptionMessage.getExceptionMessage(e));
							}

							try {
								if (json != null && !json.isEmpty()) {
									if (posPrintNewService.isNONewPrint(json.optString("tenancy_id"), json.optInt("store_id"))) {// 如果启用新的打印模式
										posPrintNewService.posPrintByFunction(json.optString("tenancy_id"), json.optInt("store_id"), FunctionCode.CHANGE_TABLE, json);
									} else {
										List<Integer> lids = posPrintService.orderWholeChgChef(json.optString("tenancy_id"), json.optString("bill_num"), json.optInt("store_id"), json.optString("oper"), json.optString("oper_type"), json.optString("table_tag"), json.optString("table_name_tag"));
										posPrintService.orderPrint(json.optString("tenancy_id"), json.optString("bill_num"), json.optInt("store_id"), lids);
									}

								}
							} catch (Exception e1) {
								logger.error(e1);
							}
						} catch (SystemException se) {
							buildSysExceptionData(se, result, Constant.COMBINE_TABLE_FAILURE);
						} catch (Exception e) {
							buildExceptionData(e, result, Constant.COMBINE_TABLE_FAILURE);
						}
					}else if(Oper.cancle == param.getOper()){ //取消并台
						try {
							posDishService.combineTableCancle(param, result, json);
							try {
								posService.updateDevicesDataState();
							} catch (Exception e) {
								e.printStackTrace();
								logger.info("Exception:" + ExceptionMessage.getExceptionMessage(e));
							}

							try {
								if (json != null && !json.isEmpty()) {
									if (posPrintNewService.isNONewPrint(json.optString("tenancy_id"), json.optInt("store_id"))) {// 如果启用新的打印模式
										posPrintNewService.posPrintByFunction(json.optString("tenancy_id"), json.optInt("store_id"), FunctionCode.CHANGE_TABLE, json);
									} else {
										List<Integer> lids = posPrintService.orderWholeChgChef(json.optString("tenancy_id"), json.optString("bill_num"), json.optInt("store_id"), json.optString("oper"), json.optString("oper_type"), json.optString("table_tag"), json.optString("table_name_tag"));
										posPrintService.orderPrint(json.optString("tenancy_id"), json.optString("bill_num"), json.optInt("store_id"), lids);
									}

								}
							} catch (Exception e1) {
								logger.error(e1);
							}

						}
						catch (SystemException se)
						{
							buildSysExceptionData(se, result, Constant.COMBINE_TABLE_CANCLE_SUCCESS);
						}
						catch (Exception e)
						{
							buildExceptionData(e, result, Constant.COMBINE_TABLE_CANCLE_FAILURE);
						}
				    }
					break;
				case HQ_EMPLOYEE_INFO:
					try{
						if (Oper.find == param.getOper())
						{//查询人员
							posService.findEmployeeInfo(param, result);
						}
						else  if(Oper.add == param.getOper())
						{
							posService.addEmployeeInfo(param,result);
						}
						else if(Oper.update == param.getOper())
						{
							posService.updateEmployeeInfo(param,result);
						}
						else if(Oper.delete == param.getOper())
						{
							posService.delEmployeeById(param,result);
						}
					}
					catch (SystemException se)
					{
						buildSysExceptionData(se, result, message.concat(Constant.CODE_FAILURE_MSG));
					}
							catch (Exception e)
					{
						buildExceptionData(e, result, message.concat(Constant.CODE_FAILURE_MSG));
					}
					break;

                case ECO_STATE_QUERY:
                    try {
                        // 接收POS传入的ECO配置url 及账号信息
                        ordersManagementService.ecoStateQuery(param);
                        result.setCode(Constant.CODE_SUCCESS);
                        result.setMsg("调用成功");
                    } catch (Exception e) {
                        buildExceptionData(e, result, Constant.CODE_FAILURE_MSG);
                    }

                    break;


                default:
					result.setCode(Constant.CODE_PARAM_FAILURE);
					result.setMsg(Constant.NOT_EXISTS_TYPE);
					break;
			}

			if (data != null)
			{
				result.setCode(data.getCode());
				result.setMsg(data.getMsg());
				result.setData(data.getData());
			}

			if (data != null && data.getPagination() == null)
			{
				Pagination page = result.getPagination();
				page.setPageno(1);
				List<?> lists = result.getData();
				if (lists == null)
				{
					lists = new ArrayList<Object>();
				}
				page.setPagesize(lists.size());
				page.setTotalcount(lists.size());
			}
			else if (data != null)
			{
				result.setPagination(data.getPagination());
			}

			if (result.getCode() == 0)
			{
				result.setSuccess(true);
			}
			else
			{
				result.setSuccess(false);
			}

			// JSONObject postData = JSONObject.fromObject(result);
			// logger.info(postData.toString().length() + "<==长度，POS查询结果==>" +
			// postData.toString());
			// return postData;

			responseJson = JSONObject.fromObject(result);

	//		logger.info(responseJson.toString().length() + "<==长度，POS查询结果==>" + responseJson.toString());
			// 相应日志加到队列里异步执行

			if (isPrintLog)
			{
				long et =System.currentTimeMillis();
				//OutputLogToFileRunnable.queue.add(String.valueOf(bt)+"<==T，用时：==>" +String.valueOf((et-bt)/1000d)+"秒 <返回结果:==>"+responseJson.toString());
				logger.info(String.valueOf(bt)+"<==T，用时：==>" +String.valueOf((et-bt)/1000d)+"秒 <返回结果:==>"+responseJson.toString());
			}
/*
			try
			{
				out = response.getWriter();
				out.print(responseJson.toString());
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
			}
			finally
			{
				if (out != null) out.close();
			}*/
			return responseJson;
		}
	}

    private void openTable(Data param, Data result, JSONObject json) {
        try
        {
//			List<JSONObject> openTables = posDishService.newestOpenTable(param, codeService, json);

			boolean isExsist = posDishServlet.checkCombineTableOpenTable(param);
			if(isExsist){
				result.setCode(Constant.CODE_INNER_EXCEPTION);
				result.setMsg("桌台存在并台账单不能拆台");
				return ;
			}



			long t = System.currentTimeMillis();
			List<JSONObject> openTables = posDishServlet.newestOpenTable(param, json);

            System.out.println("开台服务接口newestOpenTable方法耗时："+(System.currentTimeMillis()-t)+"毫秒。");
            if (openTables.size() == 0)
            {
                result.setCode(Constant.CODE_NULL_DATASET);
                result.setMsg(Constant.OPEN_TABLE_FAILURE);
            }
            else
            {
                result.setData(openTables);
                result.setCode(Constant.CODE_SUCCESS);
                result.setMsg(Constant.OPEN_TABLE_SUCCESS);
                
                //数据上传
                String tenantId = param.getTenancy_id();
    			Integer organId = param.getStore_id();
    			Map<String, Object> maps = ReqDataUtil.getDataMap(result);
    			String billno = ParamUtil.getStringValue(maps, "bill_num", true, PosErrorCode.NOT_NULL_BILL_NUM);
    			posDishService.upload(tenantId,organId+"","","","",billno);
            }
            OpenTablePrintThread openTablePrintThread = new OpenTablePrintThread(json);
            ThreadPool.getPrintThreadPool().execute(openTablePrintThread);
            System.out.println("开台服务接口耗时："+(System.currentTimeMillis()-t)+"毫秒。");
        }
        catch (SystemException se)
        {
            buildSysExceptionData(se, result, Constant.OPEN_TABLE_FAILURE);
        }
        catch (Exception e)
        {
            buildExceptionData(e, result, Constant.OPEN_TABLE_FAILURE);
        }
    }

    public void buildSysExceptionData(SystemException se, Data result, String message)
	{
		ErrorCode error = se.getErrorCode();
		String msg = se.getErrorMsg();
//		String msg = PropertiesLoader.getProperty(String.valueOf(error.getNumber()));
//		Map<String, Object> map = se.getProperties();
//		for (String key : map.keySet())
//		{
//			msg = msg.replace(key, String.valueOf(map.get(key)));
//		}

		result.setCode(error.getNumber());
		result.setMsg(msg);
		logger.error(message + ",原因：" + msg + ",错误码：" + error.getNumber());
		logger.error(ExceptionMessage.getExceptionMessage(se));
	}

	public void buildExceptionData(Exception e, Data result, String message)
	{
		result.setCode(Constant.CODE_INNER_EXCEPTION);
		result.setMsg(message);
		logger.info("系统内部错误：" + ExceptionMessage.getExceptionMessage(e));
		e.printStackTrace();
	}

	public void buildDataResult(int code, String msg, List<?> list, Data result)
	{
		result.setCode(code);
		result.setMsg(msg);

		if (list != null && list.size() != 0)
		{
			result.setData(list);
		}
	}

	public JSONObject buildErrorResult(int code, String msg)
	{
		JSONObject obj = new JSONObject();
		obj.put("code", code);
		obj.put("msg", msg);
		return obj;
	}

    /**
     * 检查BOH是否需要升级
     */
    private void checkBohUpgrade() {
        try {
            String upgradeState = NebulaBoh.loadConsoleProperties(BohUpgradeConstant.BOH_UPGRADE_PROPERTY, true);
            if (!StringUtils.isEmpty(upgradeState) && BohUpgradeState.FILE_DOWNLOADED.getCode().equals(upgradeState)) {
                logger.info("BOH需要进行升级");

                NebulaBoh.saveConsoleProperties(BohUpgradeConstant.BOH_UPGRADE_PROPERTY, BohUpgradeState.SHOP_CLOSED.getCode());

                Data data = new Data();
                data.setType(Type.UPGRADE);
                data.setOper(Oper.update);
                data.setData(null);

                Comet4jUtil.sendMessage2All(Comet4jUtil.POS_PC, JSONObject.fromObject(data).toString());
                logger.info("通知POS进行升级");
            }
        }catch (Throwable e){
            logger.error("检查打烊后是否自动升级异常:",e);
        }
    }

}
