package com.tzx.member.meituan.po.springjdbc.dao.imp;


import java.util.List;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Repository;

import com.tzx.pos.base.dao.imp.BaseDaoImp;
import com.tzx.member.meituan.po.springjdbc.dao.PosNewCrmDao;

@Repository(PosNewCrmDao.NAME)
public class PosNewCrmDaoImp extends BaseDaoImp implements PosNewCrmDao {

	
	/**
	 * 获取TOKEN
	 * @throws Exception 
	 */
	public String getTokenByThirdLink(String tenancyId,Integer storId,Integer employeeId) throws Exception
	{
		StringBuilder sBuilder=new StringBuilder("select token from employee_third_link where tenancy_id =? and store_id=? and employee_id=?");
		List<JSONObject>list=this.query4Json(tenancyId, sBuilder.toString(),new Object[]{tenancyId,storId,employeeId});
		String token="";
		if(null!=list&&list.size()>0){
			token=(String) list.get(0).get("token");
		}
		return token;
	}
	/**
	 * 获取signKey
	 * @param tenancy_id
	 * @param stor_id
	 * @return
	 * @throws Exception
	 */
	@Deprecated
	public String getSignKen(String tenancy_id,Integer stor_id) throws Exception{
		StringBuilder sBuilder=new StringBuilder("select secret as signkey from cc_third_organ_info where  cc_third_organ_info.shop_id=? and cc_third_organ_info.channel='XMD' ");
		Object[] objects=new Object[]{stor_id};
		List<JSONObject>list=this.query4Json(tenancy_id, sBuilder.toString(),objects);
		String signkey="";
		if(null!=list&&list.size()>0){
			signkey=(String) list.get(0).get("signkey");
		}
		return signkey;
	}
	
	/**
	 * 查出最后一个会员卡Id和会员Id
	 * @return
	 */
	@Override
	public List<JSONObject> findLastData(String tenantId) throws Exception{
		StringBuilder sql = new StringBuilder("select card_id,customer_id from crm_activation_lost_list order by id desc limit 1");
		return this.query4Json(tenantId, sql.toString());
		
	}
	
	/**
	 * 根据会员卡号取会员信息
	 * @return
	 */
	@Override
	public List<JSONObject> findDataByCode(String tenantId,String cardCode) throws Exception{
		StringBuilder sql = new StringBuilder("select card_id,customer_id,deposit from crm_activation_lost_list where card_code = ? ");
		return this.query4Json(tenantId, sql.toString(),new Object[]{cardCode});
		
	}
	
	/**
	 * 根据操作员Id取操作员名字
	 * @param operatorId
	 * @return
	 * @throws Exception
	 */
	@Override
	public String  findOperator(String tenantId,Integer operatorId) throws Exception{
		StringBuilder sql = new StringBuilder("select name from employee where id=?");
		Object[] objects=new Object[]{operatorId};
		List<JSONObject>list=this.query4Json(tenantId, sql.toString(),objects);
		String name="";
		if(null!=list&&list.size()>0){
			name=(String) list.get(0).get("name");
		}
		return name;
	}
	
	/**
	 *  付款方式
	 * @return
	 * @throws Exception 
	 */
	@Override
	public int paymentType(String type,Object[]objs) throws Exception{
		StringBuffer sql = new StringBuffer();
		sql.append("insert into crm_card_payment_list(tenancy_id,card_id,payment_id,bill_code,pay_money,pay_no,rexchange_rate,local_currency,");
		if("1".equals(type)){
			sql.append("third_bill_code)values(?,?,?,?,?,?,?,?,?)");
		}else if("2".equals(type)){
			sql.append("third_bill_code,store_updatetime,last_updatetime) values(?,?,?,?,?,?,?,?,?,?,?)");
		}
		
		return this.update(sql.toString(), objs);
	}
}
