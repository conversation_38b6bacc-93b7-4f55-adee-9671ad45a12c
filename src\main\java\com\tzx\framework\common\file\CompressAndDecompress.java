/** 
 * @(#)CodeFormat.java    1.0   2017-04-25 
 * 
 * Copyright 北京普照天星科技有限公司. All rights reserved. 
 * PROPRIETARY/CONFIDENTIAL. Use is subject to license terms. 
 */
package com.tzx.framework.common.file;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.UUID;
import java.util.zip.Deflater;
import java.util.zip.DeflaterOutputStream;
import java.util.zip.Inflater;
import java.util.zip.InflaterInputStream;
import java.util.zip.ZipException;
import net.sf.json.JSONObject;

/**
 * 文件压缩和解压缩工具类
 * 
 * <AUTHOR> email:<EMAIL>
 * @version 1.0  2017-04-25 
 * @see     
 * @since   JDK7.0
 * @update  
 */
public class CompressAndDecompress {
	
	/**
	 * @Description 数据压缩
	 * @param  byteStr ：被压缩数据字节数组
	 * @return 返回参数：压缩后数据字节数组
	 * @exception
	 * <AUTHOR> email:<EMAIL>
	 * @version 1.0  2017-04-25
	 * @see
	 */
	public static byte[] compressBytes(byte[] byteStr) {
		ByteArrayInputStream bis = new ByteArrayInputStream(byteStr);
		ByteArrayOutputStream bos = new ByteArrayOutputStream();
		try {
			// 此处的第二个参数必须要设置成true，这样才能成功与服务器端对应压缩与解压缩。
			Deflater def = new Deflater(Deflater.BEST_COMPRESSION, true);
			DeflaterOutputStream dos = new DeflaterOutputStream(bos, def);
			byte[] buf = new byte[1024];
			int readCount = 0;
			while ((readCount = bis.read(buf, 0, buf.length)) > 0) {
				dos.write(buf, 0, readCount);
			}
			dos.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		byte[] res = bos.toByteArray();
		return res;
	}

	/**
	 * @Description 数据解压缩
	 * @param  byteEncrypto ：压缩数据字节数组
	 * @return 返回参数：解压缩后数据字节数组
	 * @exception
	 * <AUTHOR> email:<EMAIL>
	 * @version 1.0  2017-04-25
	 * @see
	 */
	public static byte[] decompressBytes(byte[] byteEncrypto) {
		ByteArrayInputStream bis = new ByteArrayInputStream(byteEncrypto);
		ByteArrayOutputStream bos = new ByteArrayOutputStream();

		// 相对应的解压缩的参数也要设置未true
		Inflater inf = new Inflater(true);
		InflaterInputStream iis = new InflaterInputStream(bis, inf);
		int readCount = 0;
		byte[] buf = new byte[1024];
		try {
			while ((readCount = iis.read(buf, 0, buf.length)) > 0) {
				bos.write(buf, 0, readCount);
			}
			iis.close();
			return bos.toByteArray();
		} catch (ZipException e) {
		} catch (IOException e) {
			e.printStackTrace();
		}
		return null;
	}
	
	/*
	 * 测试用例
	 */
	public static void main(String[] args){ 
		HashMap<String,String> map = new HashMap<String, String>();
		map.put("logId", UUID.randomUUID().toString().replaceAll("-",""));
		map.put("logType", "pos");
		map.put("logDate", "2017-03-07");
		map.put("tenancyId", "hdl");
		map.put("storeId", "1212");
		JSONObject jsonObject = new JSONObject();
		String str = jsonObject.fromObject(map).toString();		
		StringBuffer sb = new StringBuffer();
		for(int i =0;i<1000;i++){
			sb.append(str);
		}
		String dataStr = sb.toString();
		
		// 字符串转byte[]数组
		byte[] srtbyte = dataStr.getBytes();
		// 数据字节数组长度
		System.out.println("压缩前大小："+srtbyte.length+"");
		// 进行数据压缩
		byte[] compressByte = compressBytes(srtbyte);	
		System.out.println("压缩后大小："+compressByte.length+"");
		// 对数据进行解压缩
		byte[] decompressByte = decompressBytes(compressByte);
		System.out.println("解压后大小："+decompressByte.length+"");
		// byte[]数组转字符串
		String tmpStr = new String(decompressByte);
		// 判断字符串压缩前后是否相等
		System.out.println("压缩前后字符串是否相等："+tmpStr.equals(dataStr));
	}

}
