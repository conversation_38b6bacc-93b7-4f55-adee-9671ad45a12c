package com.tzx.pos.version;


import com.twmacinta.util.MD5;
import org.apache.commons.io.FileUtils;
import org.apache.log4j.Logger;

import javax.swing.*;
import java.io.File;
import java.io.IOException;

/**
 * <AUTHOR> on 2017-03-18
 */
public class UpgradeConsole implements Runnable {

    Logger logger = Logger.getLogger("tip");

    private String docBase;

    public UpgradeConsole(String docBase) {
        this.docBase = docBase;
    }

    @Override
    public void run() {
    	
        try {            
            File newConsole = new File(docBase + "/download/Console.jar");
            if(!newConsole.exists()){
                return;
            }
            
            File file = new File(docBase);
            File console = new File(file.getParentFile().getParent() + "/lib/Console.jar");

            String oldHash = MD5.asHex(MD5.getHash(console));
            String newHash = MD5.asHex(MD5.getHash(newConsole));
            if (oldHash.equals(newHash)) {
                FileUtils.forceDelete(newConsole);
                return;
            }
            logger.info("正在升级更新组件……");
            Process process = Runtime.getRuntime().exec("taskkill /F /IM javawBOH.exe");
            process.waitFor();

            FileUtils.copyFile(newConsole, console);

            process = Runtime.getRuntime().exec("cmd /c start /d " + file.getParentFile().getParentFile().getParent() + " startServer.bat");
            process.waitFor();

            if(process.exitValue()==0){
                logger.info("成功升级更新组件");
            }else{
                logger.error("更新组件升级后启动失败");
            }

        } catch (IOException | InterruptedException e) {
            logger.error("升级更新组件时发生错误!" + e.getMessage());
        }
    }
}
