package com.tzx.orders.service.rest;

import com.tzx.framework.common.exception.CcErrorCode;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.CommonUtil;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.PathUtil;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.orders.base.constant.Constant;
import com.tzx.orders.bo.OrdersManagementService;
import com.tzx.orders.po.springjdbc.dao.OrdersManagementDao;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.base.util.QrCodeUtils;
import net.sf.json.JSONObject;
import org.apache.log4j.Logger;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.File;
import java.io.PrintWriter;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

/**
 * Eco 外卖接口
 * <AUTHOR>
 */
@Controller("EcoOrdersRest")
public class EcoOrdersRest
{
	private static final Logger logger	= Logger.getLogger(EcoOrdersRest.class);

	private OrdersManagementService ordersService;
	private ApplicationContext context	= SpringConext.getApplicationContext();

	@Resource(name = OrdersManagementDao.NAME)
	private OrdersManagementDao ordersDao;

	/**
	 * 统一设置response 的header
	 * @param request
	 * @param response
	 */
	private void setHeader(HttpServletRequest request, HttpServletResponse response) {
		String name = request.getHeader("Origin");
		response.setHeader("Access-Control-Allow-Origin", name);
		response.setHeader("Access-Control-Allow-Credentials", "true");
		response.setHeader("Cache-Control","no-cache");
		response.setContentType("text/html; charset=UTF-8");
		response.setContentType("text/html");
		response.setCharacterEncoding("UTF-8");
	}

	/**
	 * 获取系统参数Map
	 * @return
	 */
	private Map<String, String> getSystemMap(){
		return com.tzx.framework.common.constant.Constant.getSystemMap();
	}

	/**
	 * ECO对接 接收并转账单
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/receive",method = RequestMethod.POST)
	public void receive(HttpServletRequest request, HttpServletResponse response){
		this.setHeader(request, response);

		String tenantId  = getSystemMap().get("tenent_id");
		int storeId= Integer.parseInt(getSystemMap().get("store_id"));

		PrintWriter out = null;
		JSONObject resultJson = new JSONObject();
		String orderCode = null;
		String orderIndex = null;
		String conTactMem = null;
		JSONObject requestParams = new JSONObject();
		try {
			String strParam = this.getRequestParam(request);
			logger.info("调用接单接口传递参数："+request.getRequestURL()+"##### "+strParam);

			requestParams = JSONObject.fromObject(strParam);
			orderCode = requestParams.getString("orderid");
			String sourceName = requestParams.getString("sourcename");
			orderIndex = requestParams.getString("order_index");
			conTactMem = sourceName +"第"+orderIndex+"单";

			DBContextHolder.setTenancyid(tenantId);
			ordersService = context.getBean(OrdersManagementService.NAME, OrdersManagementService.class);

			// eco 转账单
			JSONObject printInfo = ordersService.createBill(tenantId,storeId,requestParams);

			// 打印信息
			if (null != printInfo && !printInfo.isEmpty()){
				// 判断是否异常单变为正常单(是则不再重复打印)
				String printStatus = ParamUtil.getStringValueByObject(printInfo, "printStatus");
				if (null == printStatus) {
					// 分单
					logger.info("ECO对接打印信息 ======"+printInfo);
					ordersService.printOrderBufferOfECO(tenantId,storeId,printInfo);
				}

				JSONObject orderPrint = printInfo.getJSONObject("orderPrint");
				String billNum = orderPrint.optString("bill_num");
				String orderId = orderPrint.optString("order_num");

				resultJson.put("success", "true");
				resultJson.put("msg", "接单成功");
				resultJson.put("code", 0);
				resultJson.put("check_id", billNum);
				resultJson.put("orderid", orderId);
			}
			else {
				resultJson.put("success", "true");
				resultJson.put("msg", "同步数据成功");
				resultJson.put("code", 0);
			}
		}
		catch (SystemException se)
		{
			if (CcErrorCode.ORDER_ALREADY_EXISTS_ERROR.equals(se.getErrorCode()))
			{
				String billNum = String.valueOf(se.getProperties().get("bill_num"));

				resultJson.put("success", "true");
				resultJson.put("msg", se.getErrorMsg());
				resultJson.put("code", 0);
				resultJson.put("check_id", billNum);
				resultJson.put("orderid", orderCode);
			}
			else
			{
				ordersService.exceptionPrint(tenantId, storeId, requestParams, SysDictionary.PRINT_CODE_1206);
				// 发送异常声音提醒
				ordersService.sendMsg(tenantId, storeId, orderCode, Constant.ORDER_STATE_EXCEPTION,false);

				String msg = getMessage(se, null);
				ordersService.sendMsgForWindow(msg, conTactMem);

				resultJson.put("success", "false");
				resultJson.put("code", se.getErrorCode().getNumber());
				resultJson.put("msg", msg);
				logger.error("接单失败", se);
			}
		}
		catch (Exception e)
		{
			ordersService.exceptionPrint(tenantId, storeId, requestParams, SysDictionary.PRINT_CODE_1206);
			// 发送异常声音提醒
			ordersService.sendMsg(tenantId,storeId,orderCode, Constant.ORDER_STATE_EXCEPTION,false);
			ordersService.sendMsgForWindow("接单失败!", conTactMem);
			logger.error("接单失败", e);
			resultJson.put("success", "false");
			resultJson.put("msg", "接单失败!");

		} finally {
			try {
				logger.info("调用接单接口响应参数：" + request.getRequestURL() + " ##### " + resultJson.toString());

				out = response.getWriter();
				out.print(resultJson.toString());
				out.flush();
				out.close();
			} catch (Exception e) {
				e.printStackTrace();
				logger.error("接单失败", e);
			} finally {
				if (out != null){
					out.close();
				}

			}
		}
	}

	/**
	 * ECO对接 退单
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/orderback",method = RequestMethod.POST)
	public void orderBack(HttpServletRequest request, HttpServletResponse response) {
		this.setHeader(request, response);

		JSONObject resultJson = new JSONObject();
		PrintWriter out = null;
		String tenantId = "";
		int storeId = -1;
		JSONObject requestParams = new JSONObject();
		try {
			String strParam = this.getRequestParam(request);
			logger.info("调用退单传递参数："+request.getRequestURL()+"##### "+strParam);

			requestParams = JSONObject.fromObject(strParam);
			Map<String, String> systemMap = com.tzx.framework.common.constant.Constant.getSystemMap();
			tenantId  = systemMap.get("tenent_id");
			storeId= Integer.parseInt(systemMap.get("store_id"));

			DBContextHolder.setTenancyid(tenantId);
			ordersService = context.getBean(OrdersManagementService.NAME, OrdersManagementService.class);

			// eco退单
			ordersService.orderBack(tenantId, storeId, requestParams);

			resultJson.put("success", "true");
			resultJson.put("msg", "退单成功");

		} catch (SystemException se) {
			if (PosErrorCode.NOT_PERMIT_CANCBILL_STATE.equals(se.getErrorCode())) {
				resultJson.put("success", "true");
				resultJson.put("msg", "退单成功");
			} else if (PosErrorCode.NOT_EXISTS_BILL.equals(se.getErrorCode())) {
				// 整单退
				if (requestParams.containsKey("orderPart") && requestParams.getJSONArray("orderPart").size()==0) {
					ordersService.exceptionPrint(tenantId, storeId, requestParams, SysDictionary.PRINT_CODE_1207);
					resultJson.put("success", "true");
					resultJson.put("msg", "退单成功，原单未入账");
				} else {
					resultJson.put("success", "false");
					resultJson.put("code", se.getErrorCode().getNumber());
					resultJson.put("msg", getMessage(se, null));
				}

			} else {
				String msg = getMessage(se, null);
				resultJson.put("success", "false");
				resultJson.put("code", se.getErrorCode().getNumber());
				resultJson.put("msg", msg);
			}

		} catch (Exception e) {
			logger.error("退单失败", e);
			resultJson.put("success", "false");
			resultJson.put("msg", "退单失败");
		} finally {
			logger.info("调用退单响应参数："+request.getRequestURL()+"##### "+resultJson);
			try {
				out = response.getWriter();
				out.print(resultJson.toString());
				out.flush();
				out.close();
			} catch (Exception e) {
				e.printStackTrace();
				logger.error("接单失败", e);
			} finally {
				if (out != null){
					out.close();
				}

			}
		}
	}

	/**
	 * 解析系统定义异常
	 * @param se
	 * @param msg
	 * @return
	 */
	private static String getMessage(SystemException se, String msg) {
		if(CommonUtil.hv(msg)){
			return msg;
		} else {
			String message = se.getErrorMsg();
			return message;
		}
	}

	/**
	 * 订单提醒 1-新订单 2-申请退单 3-催单
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/orderRemind",method = RequestMethod.POST)
	public void orderRemind(HttpServletRequest request, HttpServletResponse response){
		this.setHeader(request, response);

		JSONObject resultJson = new JSONObject();
		PrintWriter out = null;
		try {
			String strParam = this.getRequestParam(request);
			logger.info("调用订单提醒接口传递参数："+request.getRequestURL()+" ##### "+strParam);

			String tenantId  = getSystemMap().get("tenent_id");
			int storeId= Integer.parseInt(getSystemMap().get("store_id"));
			DBContextHolder.setTenancyid(tenantId);

			JSONObject requestParams = JSONObject.fromObject(strParam);

			ordersService = context.getBean(OrdersManagementService.NAME, OrdersManagementService.class);
			ordersService.orderRemind(tenantId, storeId, requestParams);
			resultJson.put("success", "true");
			resultJson.put("msg", "订单提醒成功");
		}
		catch (Exception e)
		{
			logger.error("订单提醒orderRemind失败:" + e.getMessage(), e);
			resultJson.put("success", "false");
			resultJson.put("msg", "订单提醒失败");
		}
		finally {
			try {
				out = response.getWriter();
				out.print(resultJson.toString());
				out.flush();
				out.close();
			} catch (Exception e) {
				e.printStackTrace();
				logger.error("订单提醒失败", e);
			} finally {
				if (out != null){
					out.close();
				}

			}
		}
	}

	/**
	 * 推送订单数量
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/ordernum",method = RequestMethod.POST)
	public void orderNum(HttpServletRequest request, HttpServletResponse response){
		this.setHeader(request, response);

		JSONObject resultJson = new JSONObject();
		PrintWriter out = null;
		try {
			Map<String, String[]> map = request.getParameterMap();

			JSONObject requestParams = JSONObject.fromObject("{}");

			for (String key : map.keySet())
			{
				requestParams.put(key, map.get(key)[0]);
			}

			if(null==requestParams || requestParams.isEmpty())
			{
				String strParam = this.getRequestParam(request);
				requestParams = JSONObject.fromObject(strParam);
			}

			logger.info("调用推送订单数量接口传递参数："+request.getRequestURL()+"#####"+requestParams);

			String tenantId  = getSystemMap().get("tenent_id");
			int storeId= Integer.parseInt(getSystemMap().get("store_id"));
			DBContextHolder.setTenancyid(tenantId);

			ordersService = context.getBean(OrdersManagementService.NAME, OrdersManagementService.class);
			ordersService.orderNum(tenantId, storeId, requestParams);

			resultJson.put("success", "true");
			resultJson.put("msg", "推送订单数量成功");
		}
		catch (Exception e)
		{
			resultJson.put("success", "false");
			resultJson.put("msg", "推送订单数量失败");
		}
		finally {
			try {
				out = response.getWriter();
				out.print(resultJson.toString());
				out.flush();
				out.close();
			} catch (Exception e) {
				e.printStackTrace();
				logger.error("推送订单数量失败", e);
			} finally {
				if (out != null){
					out.close();
				}

			}
		}
	}

	/** 获取请求参数
	 * @param request
	 * @return
	 * @throws Exception
	 */
	private String getRequestParam(HttpServletRequest request) throws Exception
	{
		String strParam = null;
		try
		{
			BufferedReader bufferedReader = request.getReader();
			StringBuffer params = new StringBuffer();
			String thisLine = null;
			while ((thisLine = bufferedReader.readLine()) != null)
			{
				params.append(thisLine);
			}
			strParam = params.toString();

			if (null == strParam || "".equals(strParam))
			{
				throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
			}
		}
		catch (Exception e)
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}

		return strParam;
	}

	/**
	 * @Description: 打印外卖随餐单
	 * @Title:printorder
	 * @param:@param request
	 * @param:@param response
	 */
	@RequestMapping(value = "/printorder",method = RequestMethod.POST)
	public void printOrder(HttpServletRequest request, HttpServletResponse response)
	{
		this.setHeader(request, response);
		response.setHeader("Access-Control-Allow-Methods", "*");
		response.setHeader("Access-Control-Allow-Headers", "x-requested-with,content-type");

		PrintWriter out = null;
		JSONObject resultJson = new JSONObject();
		try
		{
			String strParam = this.getRequestParam(request);
			logger.info("调用打印外卖随餐单接口传递参数："+request.getRequestURL()+"================"+strParam);

			JSONObject obj = JSONObject.fromObject(strParam) ;

			Map<String, String> systemMap = com.tzx.framework.common.constant.Constant.getSystemMap();
			String tenantId  = systemMap.get("tenent_id");
			int storeId= Integer.parseInt(systemMap.get("store_id"));
			DBContextHolder.setTenancyid(tenantId);

			ordersService = context.getBean(OrdersManagementService.NAME, OrdersManagementService.class);

			// 根据账单号查询原账单(日结前后账单)
			JSONObject posBill = null;
			String queryBillSql = "SELECT * FROM v_pos_bill WHERE coalesce(bill_state,'') in ('','"+ SysDictionary.BILL_STATE_ZDQX02 +"') and order_num = ? ";
			List<JSONObject> posBillList = ordersDao.query4Json(tenantId, queryBillSql, new Object[]{ obj.optString("orderid") });
			if (posBillList != null && posBillList.size() > 0) {
				posBill = posBillList.get(0);
			}

			if (posBill != null) {
				// 调用预打印
				JSONObject printInfo = prePrinted(tenantId, storeId, obj, posBill);

				// 打印信息
				if (null != printInfo && !printInfo.isEmpty()){
					// 分单
					logger.info("ECO对接预打印成功信息 ======" + printInfo.toString());
					// 整理完参数调用eco打印随餐单接口
					ordersService.printOrderBufferOfECO(tenantId,storeId,printInfo);
					resultJson.put("success", "true");
					resultJson.put("msg", "打印成功");
				}

			} else {
				obj.put("is_repeat_eco", true);
				ordersService.exceptionPrint(tenantId, storeId, obj, SysDictionary.PRINT_CODE_1206);
				resultJson.put("success", "true");
				resultJson.put("msg", "打印成功");
			}

		}
		catch (Exception e)
		{
			e.printStackTrace();
			resultJson.put("success", "false");
			resultJson.put("msg", "订单打印失败!");
			logger.error("订单打印失败", e);
		}
		finally
		{
			try
			{
				out = response.getWriter();
				out.print(resultJson.toString());
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
				logger.error("订单打印失败out", e);
			}
			finally
			{
				if (out != null){
					out.close();
				}
			}
		}
	}

	/**
	 * 组织预打印参数
	 * @param tenancyId
	 * @param storeId
	 * @param para
	 * @param posBillInfo
	 * @return
	 * @throws Exception
	 */
	private synchronized JSONObject prePrinted(String tenancyId, int storeId, JSONObject para, JSONObject posBillInfo) throws Exception {

		logger.info("随餐单接到参数=============== " + para.toString());
		ordersService = context.getBean(OrdersManagementService.NAME, OrdersManagementService.class);

		Map<String, Object> map = (Map) para;

		// 营业日期、班次数据
		JSONObject jsonObject = ordersService.getPosOptState(tenancyId, storeId, false);
		String reportDate = ParamUtil.getStringValueByObject(jsonObject, "report_date", false, null);
		Integer shiftId = ParamUtil.getIntegerValueByObject(jsonObject, "shift_id", false, null);
		String optNum = ParamUtil.getStringValueByObject(jsonObject, "opt_num", false, null);
		String posNum = ParamUtil.getStringValueByObject(jsonObject, "pos_num", false, null);

		// 订单编号
		String orderid = ParamUtil.getStringValue(map, "orderid", false, null);
		// 外部订单编号
		String outorderid = ParamUtil.getStringValue(map, "outorderid", false, null);
		// 订餐人
		String contact = ParamUtil.getStringValue(map, "contact", false, null);
		// 订餐人电话
		String mobile = ParamUtil.getStringValue(map, "mobile", false, null);
		// 订餐人地址
		String address = ParamUtil.getStringValue(map, "address", false, null);
		// 发票信息
		String invoiceinfo = ParamUtil.getStringValue(map, "invoiceinfo", false, null);
		// 下单时间
		String createtime = ParamUtil.getStringValue(map, "createtime", false, null);
		// 预约时间
		String send_time = ParamUtil.getStringValue(map, "diningtime", false, null);
		// 平台第几号单 int 类型
		String order_index = ParamUtil.getStringValue(map, "order_index", false, null);
		String lin = ParamUtil.getStringValue(map, "lin", false, null);
		String piaoTongQrCode = ParamUtil.getStringValue(map, "piaoTongQrCode", false, null);
		// 是否已经支付  1已支付0未支付
		Integer isOnline = ParamUtil.getIntegerValue(map, "ispaid", false, null);

		// 9-美团 10-饿了么 3-微信 2-话务 11-到家  渠道类型 ;
		// WX02微信扫码点餐 、EL09 饿了么外卖、MT11新美大外卖、MT08美团外卖 、DJ01到家、CC04电话
		String source = ParamUtil.getStringValue(map, "source", false, null);
		String chanel_name = ParamUtil.getStringValue(map, "sourcename", false, null);
		String onlineName = ParamUtil.getStringValue(map, "payTypeName", false, null);

		String chanel = "MD01";
		if (null != source && !"".equals(source))
		{
			if (source.equals("9"))
			{
				chanel = "MT08";
			}
			else if (source.equals("10"))
			{
				chanel = "EL09";
			}
			else if (source.equals("3"))
			{
				chanel = "WX02";
			}
			else if (source.equals("2"))
			{
				chanel = "CC04";
			}
			else if (source.equals("1"))
			{
				chanel = "DJ01";
			}
		}


		String billNum = posBillInfo.optString("bill_num");
		// 缓冲打印
		JSONObject printBuffer = new JSONObject();
		// 打印通用参数
		JSONObject orderJSON = new JSONObject();
		//v   报表日期
		orderJSON.put("report_date", reportDate);
		//  结账班次ID
		orderJSON.put("shift_id", shiftId);
		//    结账收款机号
		orderJSON.put("pos_num", posNum);
		//  操作人编号
		orderJSON.put("opt_num", optNum);
		// 账单编号
		orderJSON.put("bill_num", billNum);
		// 0:下单
		orderJSON.put("mode", "0");

		// 打印电子发票参数
		orderJSON.put("order_num", orderid);

		orderJSON.put("chanel_serial_number", order_index);
		orderJSON.put("third_order_code", outorderid);
		String need_invoice = "0";
		String url_path = null;
		if(null!=piaoTongQrCode && !"".equals(piaoTongQrCode)){
			need_invoice="1";
			// 生成电子发票二维码图片
			Timestamp timestamp = DateUtil.currentTimestamp();
			// 生成新的二维码文件名
			String fileName = billNum + "@" + DateUtil.getYYYYMMDDHHMMSS(timestamp) + ".png";
			String strPhysicalPath = PathUtil.getWebRootPath() + File.separator;
			String strimgPath = "img/qr_code/invoice/" ;
			strPhysicalPath = strPhysicalPath + strimgPath;
			boolean result = QrCodeUtils.GenerateImage(piaoTongQrCode,strPhysicalPath,fileName);
			if(result){
				String webPathConst = com.tzx.framework.common.constant.Constant.getSystemMap().get("WEB_PATH");
				url_path = webPathConst + strimgPath+ fileName;
			}
		}

		//就餐人数
		Integer guest = ParamUtil.getIntegerValue(map, "dinners_number", false, null);
		//商家实收
		Double shop_real_amount = ParamUtil.getDoubleValue(map, "shopfee", false, null);
		//订单总价
		Double totalprice = ParamUtil.getDoubleValue(map, "totalprice", false, null);
		//优惠后总金额
		Double total = ParamUtil.getDoubleValue(map, "total", false, null);
		//顾客实付金额
		Double actualPay = ParamUtil.getDoubleValue(map, "actual_amount", false, null);
		if(actualPay==0d)
		{
			actualPay=shop_real_amount;
			if(para.containsKey("Print_info") && para.get("Print_info")!=null)
			{
				JSONObject printInfojson = para.optJSONObject("Print_info");
				if(null!=printInfojson && !printInfojson.isEmpty() && printInfojson.containsKey("Actual_amount"))
				{
					actualPay = printInfojson.optDouble("Actual_amount");
				}
			}
		}

		orderJSON.put("need_invoice", need_invoice);
		orderJSON.put("url_path", url_path);
		//渠道 source 渠道来源
		orderJSON.put("channel", chanel);
		//渠道名称
		orderJSON.put("chanel_name", chanel_name);
		//第三方平台优惠金额 = actualPay 顾客支付 -  shop_real_amount商家实收
		orderJSON.put("invoice_amount", actualPay);
		//外部订单号
		orderJSON.put("third_order_code", outorderid);
		//订餐人contact
		orderJSON.put("consigner", contact);
		//订餐人电话
		orderJSON.put("consigner_phone", mobile);
		//订餐人地址
		orderJSON.put("address", address);
		if(invoiceinfo!=null && !"".equals(invoiceinfo) && invoiceinfo.contains("\n"))
		{
			invoiceinfo = invoiceinfo.replace("\n","");
		}
		//发票信息
		orderJSON.put("invoice_title", invoiceinfo);
		//下单时间
		orderJSON.put("single_time", createtime);
		//送达时间
		orderJSON.put("send_time", send_time);
		//在线支付/ 货到付款
		orderJSON.put("onlineName", onlineName);
		// 结账单/随餐单
		orderJSON.put("is_printbill", true);
		orderJSON.put("order_source", "ECO");
		//取餐一维码
		ordersService.buildOrderOneDimCode(orderJSON,tenancyId,storeId);
		// 缓冲打印
		printBuffer.put("orderPrint", orderJSON);
		// 是否补打印
		printBuffer.put("is_repeat_eco", true);
		logger.info("随餐单设置的参数=============== " + orderJSON.toString());
		return printBuffer;

	}

	/**
	 *  配送完成
	 * @param request
	 * @param response
	 */
	@RequestMapping(value = "/sendover",method = RequestMethod.POST)
	public void sendOver(HttpServletRequest request, HttpServletResponse response)
	{
		this.setHeader(request, response);
		PrintWriter out = null;
		JSONObject resultJson = new JSONObject();
		try
		{
			String strParam = this.getRequestParam(request);
			logger.info("调用配送完成接口传递参数："+request.getRequestURL()+"================" + strParam);

			JSONObject obj = JSONObject.fromObject(strParam) ;

			Map<String, String> systemMap = com.tzx.framework.common.constant.Constant.getSystemMap();
			String tenantId  = systemMap.get("tenent_id");
			DBContextHolder.setTenancyid(tenantId);
			int storeId = Integer.parseInt(systemMap.get("store_id"));

			logger.info("门店: "+ storeId + " 配送完成订单信息:-> 内部订单id: " + obj.optString("amoid") + " 收银单号:" + obj.optString("check_id"));
			resultJson.put("success", "true");
			resultJson.put("msg", "配送完成");
		}
		catch (Exception e)
		{
			e.printStackTrace();
			resultJson.put("success", "false");
			resultJson.put("msg", "配送完成状态处理失败");
			logger.error("配送完成状态处理失败", e);
		}
		finally
		{
			try
			{
				out = response.getWriter();
				out.print(resultJson.toString());
				out.flush();
				out.close();
			}
			catch (Exception e)
			{
				logger.error("配送完成处理失败out", e);
			}
			finally
			{
				if (out != null){
					out.close();
				}
			}
		}
	}


}
