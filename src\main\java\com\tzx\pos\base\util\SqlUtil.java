package com.tzx.pos.base.util;

public class SqlUtil {

	public static String getInnerStr(String[] contents){
		if(contents == null || contents.length == 0) return "";
		StringBuilder sb = new StringBuilder();
		for(int i = 0; i < contents.length; i++){
			if(sb.length() > 0) sb.append(",");
			sb.append("'").append(contents[i]).append("'");
		}
		return sb.toString();
	}
	
	public static String getInnerStr(int[] contents){
		if(contents == null || contents.length == 0) return "";
		StringBuilder sb = new StringBuilder();
		for(int i = 0; i < contents.length; i++){
			if(sb.length() > 0) sb.append(",");
			sb.append(contents[i]);
		}
		return sb.toString();
	}
	
}
