package com.tzx.ios.bo.imp;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.ios.bo.BaseDataIOSService;
import com.tzx.ios.po.springjdbc.dao.BaseDataSyncDao;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.BasicData;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.base.util.ReqDataUtil;
import com.tzx.pos.bo.imp.PosBaseServiceImp;
import com.tzx.pos.bo.imp.PosServiceImp;

@Service(BaseDataIOSService.NAME)
public class BaseDataIOSServiceImp extends PosBaseServiceImp implements BaseDataIOSService
{
	private static final Logger	logger	= Logger.getLogger(PosServiceImp.class);

	@Resource(name = BaseDataSyncDao.NAME)
	private BaseDataSyncDao		baseDataSyncDao;

	@Override
	public void syncBaseData(Data param, Data result) throws SystemException
	{
		Data data = new Data();

		List<JSONObject> jsons = null;

		String tenantId = param.getTenancy_id();
		Integer organId = param.getStore_id();

		Map<String, Object> map = ReqDataUtil.getDataMap(param);

		String channel = ParamUtil.getStringValue(map, "channel", true, PosErrorCode.NOT_NULL_CHANNEL).toUpperCase();
		String dataType = ParamUtil.getStringValue(map, "data_type", true, PosErrorCode.NOT_NULL_DATA_TYPE);

		try
		{
			BasicData type = null;
			try
			{
				type = BasicData.valueOf(dataType.toUpperCase());
			}
			catch (Exception e)
			{
				e.printStackTrace();
				throw new SystemException(PosErrorCode.NOT_EXISTS_SYNC_DATATYPE);
			}

			switch (type)
			{
				case DUTY_ORDER:
					// 班次
					jsons = baseDataSyncDao.getDutyOrderByOrganId(tenantId, organId);
					break;

				case TABLE_PROPERTY:
					//
					jsons = baseDataSyncDao.getTablePropertyByOrganId(tenantId, organId);
					break;

				case BUSINESS_AREA:
					//
					jsons = baseDataSyncDao.getBussinessArea(tenantId, organId);
					break;

				case TABLE:
					jsons = baseDataSyncDao.getTablesByTenantIdAndOrganId(tenantId, organId);
					break;

				case SERVICE_TYPE:
					// 服务费种
					jsons = baseDataSyncDao.getServiceFeeTypeByOrganId(tenantId, organId);
					break;

				case ITEM_CLASS:
					// 菜品类别
					jsons = baseDataSyncDao.getItemClassByTenantId(tenantId, organId, channel);
					break;

				case DISH:
					// 菜品
					data = baseDataSyncDao.getDishByTenantId(tenantId, organId, channel, param.getPagination());
					param.setPagination(data.getPagination());
					break;

				case DISH_FORMAT:
					// 菜品格式
					jsons = baseDataSyncDao.getDishFormat(tenantId, organId, channel);
					break;

				case UNIT:
					// 规格
					data = baseDataSyncDao.getItemUnitsByTenantId(tenantId, organId, channel, param.getPagination());
					param.setPagination(data.getPagination());
					break;

				case COMBO_DETAILS:
					jsons = baseDataSyncDao.getItemComboDetailsByOrganId(tenantId, organId, channel);
					break;

				case ITEM_GROUP:
					jsons = baseDataSyncDao.getItemGroup(tenantId, organId);
					break;

				case ITEM_GROUP_DETAILS:
					jsons = baseDataSyncDao.getItemGroupDetailsByOrganId(tenantId, organId, channel);
					break;

				case TIMEPRICE:
					jsons = baseDataSyncDao.getTimePriceByOrganId(tenantId, organId, channel);
					break;

				case TIMEPRICE_ITEM:
					jsons = baseDataSyncDao.getTimePriceItemByOrganId(tenantId, organId, channel);
					break;

				case TASTE:
					// 口味备注
					jsons = baseDataSyncDao.getItemTastes(tenantId, organId);
					break;

				case METHOD:
					// 做法
					jsons = baseDataSyncDao.getItemMethodsByTenantId(tenantId, organId);
					break;

				case REASON:
					// 退菜、奉送、优惠、恢复账单、免单原因
					jsons = baseDataSyncDao.getUnusualReasonByTenantId(tenantId, organId);
					break;

				case DISCOUNTCASE:
					// 折扣方案
					jsons = baseDataSyncDao.getDiscountCaseByTenantId(tenantId, organId);
					break;

				case DISCOUNT_DETAIL:
					// 折扣方案明细
					data = baseDataSyncDao.getDiscountCaseDetailsByTenantId(tenantId, organId,param.getPagination());
					param.setPagination(data.getPagination());
					break;

				case PAYMENT_WAY:
					// 付款方式
					jsons = baseDataSyncDao.getPaymentWayByOrganId(tenantId, organId);
					break;

				case USER:
					jsons = baseDataSyncDao.getUsersByTenantId(tenantId, organId);
					break;

				case SYS_PARAMETER:
					jsons = baseDataSyncDao.getSysParameter(tenantId, organId);
					break;

				case CRM_INCORPORATION_INFO:
					jsons = baseDataSyncDao.getCrmInCorporations(tenantId, organId);
					break;

				case CRM_INCORPORATION_PERSON:
					jsons = baseDataSyncDao.getCrmInCorporationPersons(tenantId, organId);
					break;

				case DEVICES:
					jsons = baseDataSyncDao.getDevicesByOrgan(tenantId, organId);
					break;

				case CRM_CARD_CLASS:
					jsons = baseDataSyncDao.getCrmCardClassByOrganId(tenantId, organId);
					break;

				case USER_DISCOUNT_AUTHORITY:
					jsons = baseDataSyncDao.getUserDiscountAuthorityByOrgan(tenantId, organId);
					break;

				case USER_DISCOUNT_CASE:
					jsons = baseDataSyncDao.getUserDiscountCaseByOrgan(tenantId, organId);
					break;

				case HQ_PAYMENT_SHOWTYPE:
					jsons = baseDataSyncDao.getPaymentShowtypeByOrganId(tenantId, organId);
					break;

				case BOH_IMG_PADPC:
					jsons = baseDataSyncDao.getBohImgByPcOrPad(tenantId, organId);
					break;

				case SOLDOUT:
					// 沽清
					jsons = baseDataSyncDao.getItemSoldout(tenantId, organId);
					break;

				case WORRYSALE:
					// 急推
					jsons = baseDataSyncDao.getItemWorrysale(tenantId, organId);
					break;

				case CRM_ACTIVITY_RULE:// 同步活动规则数据
					jsons = baseDataSyncDao.getActivityRuleDetails(tenantId);
					break;

				case CRM_ACTIVITY_ITEM:// 同步活动商品规则数据
					jsons = baseDataSyncDao.getCrmActivityItem(tenantId);
					break;

				case SYS_DICTIONARY:
					jsons = baseDataSyncDao.getSysDictionary(tenantId);
					break;

				case ANDROID_SYS_DICTIONARY:
					jsons = baseDataSyncDao.getAndroidSysDictionary(tenantId);
					break;

				case MENU_BUTTON_CONFIG:
					jsons = baseDataSyncDao.getMenuButtonConfig(tenantId, organId);
					break;

				case PAYMENTWAY_BUTTON_CONFIG:
					jsons = baseDataSyncDao.getPaymentWayButtonConfig(tenantId, organId);
					break;

				case DINING_TYPE:
					jsons = baseDataSyncDao.getDiningType(tenantId, organId);
					break;

				case ITEM_TASTE:
					jsons = baseDataSyncDao.getHqItemTasteDetails(tenantId, organId);
					break;
//				case FORBIDDEN_ITEM:
//					jsons = baseDataSyncDao.getPosForbiddenItem(tenantId, organId);
//					break;
//				case SETTING_SETMEAL_DETAILS:
//					jsons = baseDataSyncDao.getPosItemComboDetailsDefault(tenantId, organId);
//					break;
//				case ROLES:
//					jsons = baseDataSyncDao.getRoles(tenantId);
//					break;
				default:
					throw new SystemException(PosErrorCode.NOT_EXISTS_SYNC_DATATYPE);
			}
			result.setCode(Constant.CODE_SUCCESS);
			result.setMsg(Constant.SYNC_DATA_SUCCESS);
			if (data.getData() != null)
			{
				result.setData(data.getData());
			}
			else
			{
				result.setData(jsons);
			}
		}
		catch (SystemException se)
		{
			throw se;
		}
		catch (Exception e)
		{
			logger.info("sync:" + ExceptionMessage.getExceptionMessage(e));
			result.setCode(Constant.CODE_INNER_EXCEPTION);
			result.setMsg(Constant.SYNC_DATA_FAILURE);
			e.printStackTrace();
		}
	}

}
