package com.tzx.framework.common.util.io;

import java.io.*;

/**
 * 文件操作类
* <p>Title: ControlFile</p>
* <p>Description: </p>
* <p>Company: tzx</p> 
* <AUTHOR>
* @date       Sep 27, 2009
 */
public class ControlFile {


	/**
	 * copy文件
	* <p>Title: bakFile</p>
	* <p>Description: </p>
	* <AUTHOR>
	* @param sourcePath
	* @param targetPath
	* @return
	 */  
	public static boolean bakFile(String sourcePath, String targetPath) {
		try {
			//检测源文件是否存在
			File sourceFile = new File(sourcePath);
			if(!sourceFile.exists()){
				//System.out.println("源文件不存在！");
				return false;
			}
			//创建目标文件
			File targetFile = new File(targetPath);
			if(!targetFile.exists()){
				targetFile.createNewFile();
			}else {
				targetFile.delete();
				targetFile.createNewFile();
			}
			
			//读文件     
			FileReader raf = new FileReader(sourcePath);
			String detail = "";
			BufferedReader buff = new BufferedReader(raf);
			String temp = buff.readLine();
			while (temp != null) {
				detail += temp + "\n";
				temp = buff.readLine();
			}
			raf.close();
			//System.out.println(detail);
			//写文件   
			File file = new File(targetPath);
			PrintWriter out = new PrintWriter(new FileWriter(file));
			out.print(detail);
			out.close();

		} catch (FileNotFoundException e) {
			//System.out.println("文件没有找到");
		} catch (IOException e) {
			//System.out.println("copyFile   出错");
		}
		return true;
	}

	/**
	 * 创建文件
	* <p>Title: createFile</p>
	* <p>Description: </p>
	* <AUTHOR>
	* @param filePath
	 */
	public static File createFile(String filePath) {
		File file = null;
		try {
			 file = new File(filePath);
			if(!file.exists()){
				file.createNewFile();
			}else {
				file.delete();
				file.createNewFile();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return file;
	}
	public static void main(String[] args) {
		ControlFile.bakFile("D://struts-2.0.dtd", "D://struts-2.1.dtd");
	}
}
