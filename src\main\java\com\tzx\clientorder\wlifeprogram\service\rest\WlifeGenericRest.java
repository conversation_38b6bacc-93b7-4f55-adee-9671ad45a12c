package com.tzx.clientorder.wlifeprogram.service.rest;

import com.tzx.clientorder.wlifeprogram.bo.GeneralService;
import com.tzx.clientorder.wlifeprogram.common.constant.WlifePromptMessage;
import com.tzx.clientorder.wlifeprogram.common.util.ExceptionPrintUtil;
import com.tzx.framework.common.exception.OtherSystemException;
import com.tzx.framework.common.exception.SystemException;
import net.sf.json.JSONObject;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 通用业务（门店营业状态、桌台订单查询）
 * Created by qingui on 2018-06-26.
 */
@Controller("WlifeGenericRest")
@RequestMapping("/wlife/genericRest")
public class WlifeGenericRest {

    private static final Logger logger = Logger.getLogger(WlifeGenericRest.class);

    @Resource(name = GeneralService.name)
    private GeneralService generalService;

    /**
     * 查询门店是否营业
     * @param request
     */
    @RequestMapping(value = "/getOrganStatus")
    @ResponseBody
    public JSONObject getOrganStatus(HttpServletRequest request){
        JSONObject responseJson = new JSONObject();

        String tenancyId = null;
        if(com.tzx.framework.common.constant.Constant.systemMap.containsKey("tenent_id"))
        {
            tenancyId = com.tzx.framework.common.constant.Constant.systemMap.get("tenent_id");
        }

        Integer storeId = Integer.valueOf(com.tzx.framework.common.constant.Constant.systemMap.get("store_id"));
        if(com.tzx.framework.common.constant.Constant.systemMap.containsKey("store_id"))
        {
            storeId = Integer.valueOf(com.tzx.framework.common.constant.Constant.systemMap.get("store_id"));
        }

        //拉取桌台的订单信息
        try
        {
            generalService.getOrganStatus(responseJson, tenancyId, storeId);
        }
        catch (OtherSystemException se)
        {
            ExceptionPrintUtil.buildOtherSysExceptionData(logger, se, responseJson, WlifePromptMessage.WLIFE_ORGAN_FAILURE);
        }
        catch (SystemException se)
        {
            ExceptionPrintUtil.buildSysExceptionData(logger, se, responseJson, WlifePromptMessage.WLIFE_ORGAN_FAILURE);
        }
        catch (Exception e)
        {
            ExceptionPrintUtil.buildExceptionData(logger, e, responseJson, WlifePromptMessage.WLIFE_ORGAN_FAILURE);
        }
        logger.debug("查询门店响应出参===" + responseJson.toString());
        return responseJson;

    }

    /**
     * 查询桌台是否有订单
     * @param request
     * @param jsobj
     */
    @RequestMapping(value = "/getTableOrder", method = RequestMethod.POST)
    @ResponseBody
    public JSONObject getTableOrder(HttpServletRequest request, @RequestBody JSONObject jsobj){

        JSONObject responseJson = new JSONObject();

        logger.info("查询桌台请求入参===" + jsobj.toString());

        String tenancyId = null;
        if(com.tzx.framework.common.constant.Constant.systemMap.containsKey("tenent_id"))
        {
            tenancyId = com.tzx.framework.common.constant.Constant.systemMap.get("tenent_id");
        }

        Integer storeId = Integer.valueOf(com.tzx.framework.common.constant.Constant.systemMap.get("store_id"));
        if(com.tzx.framework.common.constant.Constant.systemMap.containsKey("store_id"))
        {
            storeId = Integer.valueOf(com.tzx.framework.common.constant.Constant.systemMap.get("store_id"));
        }
        jsobj.put("tenancyId", tenancyId);
        jsobj.put("storeId", storeId);

        //String tableCode = jsobj.optString("tableno");
        //拉取桌台的订单信息
        try
        {
            generalService.getTableOrder(responseJson, jsobj);
        }
        catch (OtherSystemException se)
        {
            ExceptionPrintUtil.buildOtherSysExceptionData(logger, se, responseJson, WlifePromptMessage.WLIFE_ORDER_FAILURE);
        }
        catch (SystemException se)
        {
            ExceptionPrintUtil.buildSysExceptionData(logger, se, responseJson, WlifePromptMessage.WLIFE_ORDER_FAILURE);
        }
        catch (Exception e)
        {
            ExceptionPrintUtil.buildExceptionData(logger, e, responseJson, WlifePromptMessage.WLIFE_ORDER_FAILURE);
        }
        logger.info("查询桌台响应出参===" + responseJson.toString());
        return responseJson;

    }
}
