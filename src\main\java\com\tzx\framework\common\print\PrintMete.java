package com.tzx.framework.common.print;

import java.awt.Font;

public class PrintMete
{
	public static final int	content_style_STR				= 1;	// 文字
	public static final int	content_style_STR_CENTER		= 2;	// 文字
	public static final int	content_style_LINE				= 3;	// 线
	public static final int	content_style_LINE_With_String	= 4;	// 线
	/**
	 * 图片
	 */
	public static final int	CONTENT_STYLE_IMAGE	= 5;
	
	private int				content_style;
	private Float			point;
	/**
	 * content_style = content_style_STR;
	 */
	// float line_position_x;
	Font					font;
	String					content;
	private Float			width;
	private Float			height;

	public PrintMete()
	{
	}

	/**
	 * 
	 * @param content_style
	 * @param font
	 * @param content
	 * @param point
	 */
	public PrintMete(int content_style, Font font, String content, Float point)
	{
		this.content_style = content_style;
		this.font = font;
		this.content = content;
		this.point = point;
	}
	
	public PrintMete(int content_style, Font font, String content, Float point,Float width,Float height)
	{
		this.content_style = content_style;
		this.font = font;
		this.content = content;
		this.point = point;
		this.width=width;
		this.height=height;
	}

	public String getContent()
	{
		return content;
	}

	public void setContent(String content)
	{
		this.content = content;
	}

	public Font getFont()
	{
		return font;
	}

	public void setFont(Font font)
	{
		this.font = font;
	}

	public int getContent_style()
	{
		return content_style;
	}

	public void setContent_style(int content_style)
	{
		this.content_style = content_style;
	}

	public Float getPoint()
	{
		return point;
	}

	public void setPoint(Float point)
	{
		this.point = point;
	}

	public Float getWidth()
	{
		return width;
	}

	public void setWidth(Float width)
	{
		this.width = width;
	}

	public Float getHeight()
	{
		return height;
	}

	public void setHeight(Float height)
	{
		this.height = height;
	}
	
}
