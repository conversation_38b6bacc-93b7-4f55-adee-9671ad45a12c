package com.tzx.orders.bo.imp;

import com.tzx.base.common.util.PosPropertyUtil;
import com.tzx.framework.common.exception.HttpRestException;
import com.tzx.framework.common.util.HttpUtil;
import net.sf.json.JSONObject;
import org.apache.log4j.LogManager;
import org.apache.log4j.Logger;

/**
 * 外卖订单数据验证（打烊）
 *
 * <AUTHOR> email:<EMAIL>
 * @version 1.0  2018-07-18
 * @see     
 * @since   JDK7.0
 * @update  
 */
public class OrderAmountAnalysis implements Runnable{
	
	private static final Logger logger = LogManager.getLogger(OrderAmountAnalysis.class);
	
	private String tenantId;           // 商户号
	private int shopId;                // 门店号
	private String startDate;          // 开始时间
	private String endDate;            // 结束时间
	private String reportDate;         // 报表日期
	private String billTotalShopfee;   // 总实收
	private int v;                  //1.20版本开始 新订单转账单  账单 payment_amount 为cc.shop_real_amount ,下单模式 为cc.shop_fee
	
	public OrderAmountAnalysis(String tenantId, int shopId, String startDate, String endDate, String reportDate, String billTotalShopfee,int v){
		this.tenantId = tenantId;
		this.shopId = shopId;
		this.startDate = startDate;
		this.endDate = endDate;
		this.reportDate = reportDate;
		this.billTotalShopfee = billTotalShopfee;
		this.v = v;
	}

	@Override
	public void run() {
		JSONObject json = new JSONObject();
		json.put("tenantId",tenantId);
		json.put("shopId",String.valueOf(shopId));
		json.put("startDate",startDate);
		json.put("endDate",endDate);
		json.put("reportDate",reportDate);
		json.put("bill_total_shopfee",billTotalShopfee);
		json.put("v", v);
		// 调用外卖平台服务接口
		String reqURL = PosPropertyUtil.getMsg("saas.url") + "/orderRest/analysisOrderAmount";
		logger.info("外卖平台服务接口地址："+reqURL);
        try {
        	logger.info("外卖平台服务接口analysisOrderAmount输入参数："+json.toString());
			JSONObject returnJson = HttpUtil.post(reqURL,json,5);
			logger.info("外卖平台服务接口analysisOrderAmount返回："+returnJson.toString());
		} catch (HttpRestException e) {
			logger.error(e.getMessage());
		}
	}

}
