package com.tzx.thirdpay.bo;

import com.tzx.base.entity.PosThirdPaymentRefundEntity;
import com.tzx.framework.common.entity.Data;

/**
 * <AUTHOR>
 *
 */
public interface ThirdPaymentRefundService
{
	String NAME = "com.tzx.thirdpay.bo.imp.ThirdPaymentRefundServiceImp";

	/** 退款失败,添加退款异常记录
	 * @param tenancyId
	 * @param storeId
	 * @param paymentRefund
	 * @return
	 * @throws Exception
	 */
	public boolean thirdPaymentRefundFailure(String tenancyId, int storeId, PosThirdPaymentRefundEntity paymentRefund) throws Exception;
	
	/** 查询异常退款
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public Data getThirdPaymentRefund(Data param) throws Exception;
	
	/** 退款重试
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public Data thirdPaymentRefundRepeat(Data param) throws Exception;
}
