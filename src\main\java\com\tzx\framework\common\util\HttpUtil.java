/** 
 * @(#)CodeFormat.java    1.0   2013-06-13
 * Copyright 北京普照天星科技有限公司. All rights reserved. 
 * PROPRIETARY/CONFIDENTIAL. Use is subject to license terms. 
 */
package com.tzx.framework.common.util;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.protocol.HTTP;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;
import com.tzx.base.bo.HttpConnectionManagerService;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.HttpRestException;
import net.sf.json.JSONException;
import net.sf.json.JSONObject;

/**
 * Http访问工具类（测试类：com.tzx.framework.comon.util.test.HttpUtilTest.java）
 * 
 * <AUTHOR> email:<EMAIL>
 * @version 1.0 2018-10-16
 * @see
 * @since JDK7.0
 * @update
 */
public class HttpUtil {
	
	public static Logger	logger	= Logger.getLogger(HttpUtil.class);
	
	public static final int DEFAULT_GETCONNECT  = 3000;    // 从连接池获取连接默认超时时间
	public static final int DEFAULT_CONNTIMEOUT = 5000;    // 默认连接超时时间
	public static final int DEFAULT_SOTIMEOUT   = 10000;   // 默认读取数据超时
	public static final int cache = 10 * 1024;             // 读取数据缓冲区大小

	/**
	 * @Description 日志输出。
	 * @param  message 日志信息  
	 * @param  e 异常信息
	 * @return null
	 * @exception 
	 * <AUTHOR> email:<EMAIL>
	 * @version 1.0  2018-10-16
	 * @see
	 */
	public static void log(String message, Exception e) {
		Logger.getLogger(HttpUtil.class).info(message, e);
	}
	
	/**
	 * @Description GET方式HTTP请求。
	 * @param  reqURL 请求地址
	 * @param  decodeCharset 字符编码格式
	 * @param  map 请求参数集合
	 * @return 响应内容
	 * @exception 无
	 * <AUTHOR> email:<EMAIL>
	 * @version 1.0  2018-10-16
	 * @see
	 */
	public static String sendGetRequest(String reqURL, String decodeCharset, Map<String, String> map) {
		String responseContent = null;
		CloseableHttpResponse response = null;
		CloseableHttpClient httpClient = null;
		try {
			// 等待Bean初始化完成
			while (SpringConext.getApplicationContext() == null) {
				Thread.sleep(100);
			}
			HttpConnectionManagerService httpConnectionManagerService = (HttpConnectionManagerService) SpringConext
					.getApplicationContext().getBean(HttpConnectionManagerService.NAME);
			httpClient = httpConnectionManagerService.getHttpClient();
			RequestConfig requestConfig = RequestConfig.custom()
					.setConnectionRequestTimeout(DEFAULT_GETCONNECT) // 连接池获取连接超时
					.setConnectTimeout(DEFAULT_CONNTIMEOUT)          // 连接超时
					.setSocketTimeout(DEFAULT_SOTIMEOUT).build();    // 读取数据超时
			HttpGet httpGet = new HttpGet(reqURL);
			if (map != null && map.size() > 0) {
				for (Map.Entry<String, String> entry : map.entrySet()) {
					httpGet.addHeader(entry.getKey(), entry.getValue());
				}
			}
			httpGet.setHeader(HTTP.CONTENT_TYPE, "text/xml; charset=utf-8");
			httpGet.setConfig(requestConfig);
			response = httpClient.execute(httpGet);
			int state = response.getStatusLine().getStatusCode();
			if (200 == state) {
				HttpEntity entity = response.getEntity();
				if (null != entity) {
					responseContent = EntityUtils.toString(entity, decodeCharset == null ? "UTF-8" : decodeCharset);
					EntityUtils.consume(entity);
				}
			} else if (404 == state) {
				Data data = new Data();
				data.setCode(404);
				data.setMsg("找不到服务器，请检查服务器地址是否正确!");
				responseContent = JSONObject.fromObject(data).toString();
			}
		} catch (UnknownHostException e) {
			Data data = new Data();
			data.setCode(404);
			data.setMsg("找不到服务器，请检查您的网络连接是否正常!");
			responseContent = JSONObject.fromObject(data).toString();
			log("与[" + reqURL + "]通信过程中发生异常,堆栈信息如下", e);
		} catch (Exception e) {
			log("与[" + reqURL + "]通信过程中发生异常,堆栈信息如下", e);
		} finally {
			if (response != null) {
				try {
					response.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		return responseContent;
	}
	
	/**
	 * @Description 发送POST请求。
	 * @param reqURL : 请求地址
	 * @param sendData : 请求参数   
	 * @param connTimeout : 连接超时
	 * @param soTimeout ： socket读取数据超时
	 * @return 返回请求数据
	 * @exception 
	 * <AUTHOR> email:<EMAIL>
	 * @version 1.0  2018-10-12
	 * @see
	 */
	public static String sendPostRequest(String reqURL, String sendData, int connTimeout, int soTimeout){
		String responseContent = null;
		CloseableHttpResponse  response = null;		
		CloseableHttpClient httpClient = null;
		try{
			// 等待Bean初始化完成
			while(SpringConext.getApplicationContext() == null){
				Thread.sleep(100);
			}  
			HttpConnectionManagerService httpConnectionManagerService = (HttpConnectionManagerService)SpringConext.getApplicationContext().getBean(HttpConnectionManagerService.NAME);
			httpClient = httpConnectionManagerService.getHttpClient();	
			RequestConfig requestConfig = RequestConfig.custom()
					.setConnectionRequestTimeout(DEFAULT_GETCONNECT)       // 连接池获取连接超时
	                .setConnectTimeout(connTimeout)                        // 连接超时
	                .setSocketTimeout(soTimeout).build();                  // 读取数据超时			
			HttpPost httpPost = new HttpPost(reqURL+"?uuid="+ UUID.randomUUID().toString().replace("-",""));
			httpPost.setHeader(HTTP.CONTENT_TYPE, "application/json");
			httpPost.setEntity(new StringEntity(sendData.toString(), "UTF-8"));// 解决中文乱码问题
			httpPost.setConfig(requestConfig);
			response = httpClient.execute(httpPost);
			int state = response.getStatusLine().getStatusCode();
			if (200 == state) {
				HttpEntity entity = response.getEntity();
				if (null != entity) {
					responseContent = EntityUtils.toString(entity, "UTF-8");
					EntityUtils.consume(entity);
				}
			} else if (404 == state) {
//				Data data = new Data();
//				data.setCode(404);
//				data.setMsg("找不到服务器，请检查服务器地址是否正确!");
//				responseContent = JSONObject.fromObject(data).toString();
				JSONObject sendDataJson = JSONObject.fromObject(sendData);
				sendDataJson.put("code", 404);
				sendDataJson.put("success",false);
				sendDataJson.put("msg", "找不到服务器，请检查您的网络连接是否正常!");
				responseContent = sendDataJson.toString();
			}			
		}catch (UnknownHostException e){
			JSONObject sendDataJson = JSONObject.fromObject(sendData);
//			Data data = new Data();
//			data.setType(Type.valueOf(sendDataJson.optString("type")));
//			data.setOper(Oper.valueOf(sendDataJson.optString("oper")));
//			data.setCode(404);
//			data.setMsg("找不到服务器，请检查您的网络连接是否正常!");
//			responseContent = JSONObject.fromObject(data).toString();
			sendDataJson.put("code", 404);
			sendDataJson.put("success",false);
			sendDataJson.put("msg", "找不到服务器，请检查您的网络连接是否正常!");
			responseContent = sendDataJson.toString();
			log("与[" + reqURL + "]通信过程中发生异常,堆栈信息如下", e);
		}catch(Exception e){
			log("与[" + reqURL + "]通信过程中发生异常,堆栈信息如下", e);
		}finally {
			if (response != null) {
				try {
					response.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		return responseContent;
	}	
	
	/**
	 * @Description 发送POST请求。
	 * @param reqURL : 请求地址
	 * @param params : 请求参数   
	 * @param connTimeout : 连接超时
	 * @param soTimeout ： socket读取数据超时
	 * @return 返回请求数据
	 * @exception 
	 * <AUTHOR> email:<EMAIL>
	 * @version 1.0  2018-10-12
	 * @see
	 */
	public static String sendPostRequest(String reqURL, Map<String, String> params, int connTimeout, int soTimeout){
		String responseContent = null;
		CloseableHttpResponse  response = null;		
		CloseableHttpClient httpClient = null;
		try{
			// 等待Bean初始化完成
			while(SpringConext.getApplicationContext() == null){
				Thread.sleep(100);
			}  
			HttpConnectionManagerService httpConnectionManagerService = (HttpConnectionManagerService)SpringConext.getApplicationContext().getBean(HttpConnectionManagerService.NAME);
			httpClient = httpConnectionManagerService.getHttpClient();	
			RequestConfig requestConfig = RequestConfig.custom()
					.setConnectionRequestTimeout(DEFAULT_GETCONNECT)       // 连接池获取连接超时
	                .setConnectTimeout(connTimeout)                        // 连接超时
	                .setSocketTimeout(soTimeout).build();                  // 读取数据超时			
			HttpPost httpPost = new HttpPost(reqURL+"?uuid="+ UUID.randomUUID().toString().replace("-",""));
			httpPost.setHeader(HTTP.CONTENT_TYPE, "application/x-www-form-urlencoded");
			List<NameValuePair> formParams = new ArrayList<NameValuePair>(); // 创建参数队列
			for (Map.Entry<String, String> entry : params.entrySet()){
				formParams.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
			}
			httpPost.setEntity(new UrlEncodedFormEntity(formParams, "UTF-8"));
			httpPost.setConfig(requestConfig);
			response = httpClient.execute(httpPost);
			int state = response.getStatusLine().getStatusCode();
			if (200 == state) {
				HttpEntity entity = response.getEntity();
				if (null != entity) {
					responseContent = EntityUtils.toString(entity, "UTF-8");
					EntityUtils.consume(entity);
				}
			} else if (404 == state) {
				Data data = new Data();
				data.setCode(404);
				data.setMsg("找不到服务器，请检查服务器地址是否正确!");
				responseContent = JSONObject.fromObject(data).toString();
			}			
		}catch (UnknownHostException e){
			Data data = new Data();
			data.setCode(404);
			data.setMsg("找不到服务器，请检查您的网络连接是否正常!");
			responseContent = JSONObject.fromObject(data).toString();			
			log("与[" + reqURL + "]通信过程中发生异常,堆栈信息如下", e);
		}catch(Exception e){
			log("与[" + reqURL + "]通信过程中发生异常,堆栈信息如下", e);
		}finally {
			if (response != null) {
				try {
					response.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		return responseContent;
	}	
	
	/**
	 * @Description 下载基础资料数据。
	 * @param url        : 请求地址
	 * @param filePath   : 下载数据保存文件地址
	 * @return 
	 * @exception 无
	 * <AUTHOR> email:<EMAIL>
	 * @version 1.0  2018-04-08
	 * @see
	 */
	public static void getHttpInputStream(String url,String filePath){			
		CloseableHttpResponse response = null;
		CloseableHttpClient httpClient = null;
		try {
			// 等待Bean初始化完成
			while (SpringConext.getApplicationContext() == null) {
				Thread.sleep(100);
			}
			HttpConnectionManagerService httpConnectionManagerService = (HttpConnectionManagerService) SpringConext
					.getApplicationContext().getBean(HttpConnectionManagerService.NAME);
			httpClient = httpConnectionManagerService.getHttpClient();
			RequestConfig requestConfig = RequestConfig.custom()
					.setConnectionRequestTimeout(DEFAULT_GETCONNECT) // 连接池获取连接超时
					.setConnectTimeout(DEFAULT_CONNTIMEOUT)          // 连接超时
					.setSocketTimeout(600000).build();    // 读取数据超时
			HttpGet httpGet = new HttpGet(url);
			httpGet.setHeader(HTTP.CONTENT_TYPE, "text/plain; charset=utf-8");
			httpGet.setConfig(requestConfig);
			response = httpClient.execute(httpGet);
			HttpEntity entity = response.getEntity();  
            InputStream is = entity.getContent(); 			             
            File file = new File(filePath);  
            FileOutputStream fileout = new FileOutputStream(file);    
            byte[] buffer=new byte[cache];  
            int ch = 0;  
            while ((ch = is.read(buffer)) != -1) {  
                fileout.write(buffer,0,ch);  
            }  
            is.close();  
            fileout.flush();  
            fileout.close();
		}catch(Exception e){
			log("与[" + url + "]通信过程中发生异常,堆栈信息如下", e);
		}finally {
			if (response != null) {
				try {
					response.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}			
	}
	
	/**
	 * @Description 发送Http Get请求。
	 * @param reqURL : 请求地址
	 * @param decodeCharset : 返回数据字符集
	 * @return 返回请求数据
	 * @exception 无
	 * <AUTHOR> email:<EMAIL>
	 * @version 1.0  2018-10-16
	 * @see
	 */
	public static String sendGetRequest(String reqURL, String decodeCharset){
		return HttpUtil.sendGetRequest(reqURL, decodeCharset,null);
	}	

	/**
	 * @Description 发送Http Post请求。
	 * @param reqURL   : 请求地址
	 * @param sendData : 请求输入参数
	 * @return 请求返回数据
	 * @exception 无
	 * <AUTHOR> email:<EMAIL>
	 * @version 1.0  2018-10-16
	 * @see
	 */
	public static String sendPostRequest(String reqURL, String sendData){
		return HttpUtil.sendPostRequest(reqURL, sendData, DEFAULT_CONNTIMEOUT, DEFAULT_SOTIMEOUT);
	}
	
	/**
	 * @Description 发送Http Post请求。
	 * @param reqURL   : 请求地址
	 * @param params   : 请求输入参数（表单数据）
	 * @return 请求返回数据
	 * @exception 无
	 * <AUTHOR> email:<EMAIL>
	 * @version 1.0  2018-10-16
	 * @see
	 */
	public static String sendPostRequest(String reqURL, Map<String, String> params){
		return sendPostRequest(reqURL, params, 3000, 5000);
	}
	
	/**
	 * @Description 发送Http Post请求。
	 * @param reqURL   : 请求地址
	 * @param sendData : 请求输入参数
	 * @param connTimeout : 连接超时
	 * @param soTimeout   ： socket读取数据超时
	 * @return 请求返回数据
	 * @exception 无
	 * <AUTHOR> email:<EMAIL>
	 * @version 1.0  2018-10-16
	 * @see
	 */
	@Deprecated
	public static String sendHttpPostRequest(String reqURL, String sendData,int connTimeout,int soTimeout)
	{	
		String responseContent = HttpUtil.sendPostRequest(reqURL, sendData, connTimeout, soTimeout);
		if (null == responseContent){
			JSONObject obj = new JSONObject();
			obj.put("code", 99);
			obj.put("msg", "连接服务器异常，请联系管理员~");
			responseContent = obj.toString();
		}
		return responseContent;
	}
	
	/**
	 * @Description 发送Http Post请求。
	 * @param reqURL   : 请求地址
	 * @param params   : 请求输入参数（表单数据）
	 * @return 请求返回数据
	 * @exception 无
	 * <AUTHOR> email:<EMAIL>
	 * @version 1.0  2018-10-16
	 * @see
	 */
	@Deprecated
	public static String sendHttpPostRequest(String reqURL, Map<String, String> params)
	{
		String responseContent = HttpUtil.sendPostRequest(reqURL, params, 3000, 5000);
		if (null == responseContent){
			JSONObject obj = new JSONObject();
			obj.put("code", 99);
			obj.put("msg", "连接服务器异常，请联系管理员~");
			responseContent = obj.toString();
		}
		return responseContent;
	}	

	/**
	 * @Description 发送Http Post请求，用于外卖功能模块。
	 * @param url      : 请求地址
	 * @param params   : 请求输入参数
	 * @param rerty    : 重试次数
	 * @param connTimeout : 连接超时
	 * @param soTimeout   ： socket读取数据超时
	 * @param trace       : 是否记录日志
	 * @return 请求返回数据
	 * @exception HttpRestException
	 * <AUTHOR> email:<EMAIL>
	 * @version 1.0  2018-10-16
	 * @see
	 */
    public static JSONObject post(String url, JSONObject param, int rerty, int connTimeout, int soTimeout, boolean trace) throws HttpRestException {
    	JSONObject returnObj = null;
    	if(rerty<=1) rerty = 1;
        String result = null;
        for (int i =0;i<rerty;i++) {
            try {
            	if(i > 0) Thread.sleep(3000);
                long startTime = System.currentTimeMillis();
                if (trace || logger.isDebugEnabled()) {
                    logger.info(">>>发送请求:[id=" + startTime + ",url=" + url + "],参数:" + param + ",第" + (i+1) + "次重试");
                }                
                result = sendPostRequest(url,param.toString(),connTimeout,soTimeout);                
                long endTime = System.currentTimeMillis();
                if (trace || logger.isDebugEnabled()) {
                    logger.info(url + "<<<请求[id=" + startTime + "]耗时:" + (endTime - startTime) + "ms,返回内容:" + result);
                }
                if(result == null) continue;
                returnObj = JSONObject.fromObject(result);
                if(returnObj.containsKey("code") && returnObj.optInt("code") == 404 ){
                	continue;
                }
                return returnObj;
            } catch (JSONException e) {
                throw new HttpRestException("服务器返回数据不符合JSON规范:" + result, e);
            } catch (Exception e) {
                throw new HttpRestException("请求服务器时发生异常", e);
            }            
        }
        throw new HttpRestException("请求服务器时发生异常" + result,null);
    }
    
    public static JSONObject post(String url, JSONObject param) throws HttpRestException {
        return post(url, param, 1,false);
    }

    public static JSONObject post(String url, JSONObject param,int rerty) throws HttpRestException {
        return post(url, param, rerty,false);
    }

    public static JSONObject post(String url, JSONObject param,int rerty,int connetTimeout,int readTimeout) throws HttpRestException {
        return post(url, param, rerty,connetTimeout,readTimeout,false);
    }

    public static JSONObject post(String url, JSONObject param,int rerty,boolean trace) throws HttpRestException {
        return post(url, param, rerty,DEFAULT_CONNTIMEOUT,DEFAULT_SOTIMEOUT,trace);
    }
	
}
