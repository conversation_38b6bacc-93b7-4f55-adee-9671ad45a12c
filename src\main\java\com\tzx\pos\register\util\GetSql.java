package com.tzx.pos.register.util;

import java.sql.Date;
import java.sql.Timestamp;
import java.sql.Types;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.springframework.jdbc.support.rowset.SqlRowSet;

import com.tzx.pos.register.dao.RegisterDao;

import net.sf.json.JSONObject;

public class GetSql {

	/**
	 * 拼接查询语句
	 * 
	 * @param jsonParam
	 * @param tableName
	 * @param where
	 * @param columns
	 * @return
	 */
	public static String returnSelectSql(JSONObject jsonParam, String tableName, List<String> where,
			List<String> columns) {
		StringBuffer sb = new StringBuffer();
		sb.append("select   ");
		if (columns.size() == 0 || columns == null) {
			sb.append("*");
		} else {
			for (int i = 0; i < columns.size(); i++) {
				if (i == columns.size() - 1) {
					sb.append(columns.get(i));
				} else {
					sb.append(columns.get(i)).append(",");
				}

			}
		}
		sb.append(" from  " + tableName + " ");
		sb.append("where  1=1 ");
		for (int i = 0; i < where.size(); i++) {
			if (jsonParam.has(where.get(i)) && jsonParam.getString(where.get(i)) != null
					&& !jsonParam.getString(where.get(i)).equals("")) {
				sb.append(" and  " + where.get(i) + "='" + jsonParam.getString(where.get(i)) + "'");
			}
		}

		return sb.toString();
	}

	/**
	 * 根据json数据编辑insert sql
	 * 
	 * @param jsonParam
	 * @param tableName
	 * @return
	 */
	public static String returnInsertSql(JSONObject jsonParam, String tableName) {
		StringBuffer sb = new StringBuffer();
		List listkey = new ArrayList();
		for (Iterator i = jsonParam.keySet().iterator(); i.hasNext();) {
			Object obj = i.next();
			listkey.add(obj);
		}
		sb.append("insert  into  " + tableName + "(");
		// 列名
		for (int i = 0; i < listkey.size(); i++) {
			if (i == listkey.size() - 1) {
				sb.append(listkey.get(i).toString());
			} else {
				sb.append(listkey.get(i).toString()).append(",");
			}

		}

		sb.append(")");
		// 值
		sb.append("values(");
		for (int i = 0; i < listkey.size(); i++) {
			if (i == listkey.size() - 1) {
				sb.append("'" + jsonParam.get(listkey.get(i).toString()).toString() + "'");
			} else {
				sb.append("'" + jsonParam.get(listkey.get(i).toString()).toString() + "'").append(",");
			}
		}

		sb.append(")");
		return sb.toString();
	}

	/**
	 * 根据json数据编辑 预编译insert sql
	 * 
	 * @param jsonParam
	 * @param tableName
	 * @return
	 */
	public static Map<String, Object> returnPreparedInsertSql(JSONObject jsonParam, String tableName,
			RegisterDao registerDao) {
		StringBuffer sb = new StringBuffer();
		SqlRowSet sqlRowSet = registerDao.queryColumnByTableName(tableName);
		Map<String, Object> map = new HashMap<String, Object>();
		List listkey = new ArrayList();
		for (Iterator i = jsonParam.keySet().iterator(); i.hasNext();) {
			Object obj = i.next();
			listkey.add(obj);
		}
		Object[] value = new Object[listkey.size()];
		sb.append("insert  into  " + tableName + "(");
		// 列名
		for (int i = 0; i < listkey.size(); i++) {
			if (i == listkey.size() - 1) {
				sb.append(listkey.get(i).toString());
			} else {
				sb.append(listkey.get(i).toString()).append(",");
			}

		}

		sb.append(")");
		// 值
		sb.append("values(");
		for (int i = 0; i < listkey.size(); i++) {
			if (i == listkey.size() - 1) {
				sb.append("?");
			} else {
				sb.append("?").append(",");
			}
			for (int c = 1; c <= sqlRowSet.getMetaData().getColumnCount(); c++) {
				if (listkey.get(i).equals(sqlRowSet.getMetaData().getColumnName(c))) {
					if (sqlRowSet.getMetaData().getColumnType(c) == Types.INTEGER) {

						value[i] = Integer.parseInt(jsonParam.get(sqlRowSet.getMetaData().getColumnName(c)).toString());
					} else if (sqlRowSet.getMetaData().getColumnType(c) == Types.VARCHAR) {

						value[i] = String.valueOf(jsonParam.get(sqlRowSet.getMetaData().getColumnName(c)));
					} else if (sqlRowSet.getMetaData().getColumnType(c) == Types.TIMESTAMP) {

						value[i] = Timestamp
								.valueOf(jsonParam.get(sqlRowSet.getMetaData().getColumnName(c)).toString());
					} else if (sqlRowSet.getMetaData().getColumnType(c) == Types.DATE) {

						value[i] = Date.valueOf(jsonParam.get(sqlRowSet.getMetaData().getColumnName(c)).toString());
					} else {
						value[i] = jsonParam.get(sqlRowSet.getMetaData().getColumnName(c)).toString();
					}

				}

			}
		}
		sb.append(")");
		map.put("sql", sb.toString());

		map.put("param", value);
		return map;
	}

	/**
	 * 根据json数据编辑update sql
	 * 
	 * @param jsonParam
	 * @param tableName
	 * @param where
	 * @return sql
	 * 
	 */
	public static String returnUpdateSql(JSONObject jsonParam, String tableName, List<String> where) {
		StringBuffer sb = new StringBuffer();
		sb.append("update   " + tableName + "  set  ");
		List listkey = new ArrayList();
		for (Iterator i = jsonParam.keySet().iterator(); i.hasNext();) {
			Object obj = i.next();
			listkey.add(obj);
		}

		for (int i = 0; i < listkey.size(); i++) {
			if (i == listkey.size() - 1) {
				sb.append(listkey.get(i).toString()).append("=")
						.append("'" + jsonParam.get(listkey.get(i).toString()).toString() + "'");
			} else {
				sb.append(listkey.get(i).toString() + "=" + "'" + jsonParam.get(listkey.get(i).toString()).toString()
						+ "'").append(",");
			}

		}

		sb.append("  where  1=1 ");
		for (int i = 0; i < where.size(); i++) {
			if (jsonParam.has(where.get(i)) && jsonParam.getString(where.get(i)) != null
					&& !jsonParam.getString(where.get(i)).equals("")) {
				sb.append("  and  " + where.get(i) + "='" + jsonParam.getString(where.get(i)) + "'");
			}
		}
		return sb.toString();
	}

	/**
	 * 拼接删除语句
	 * 
	 * @param jsonParam
	 * @param tableName
	 * @param where
	 * @return
	 */
	public static String returnDeleteSql(JSONObject jsonParam, String tableName, List<String> where) {
		StringBuffer sb = new StringBuffer();
		sb.append("delete from    " + tableName + " where  1=1 ");
		for (int i = 0; i < where.size(); i++) {
			if (jsonParam.has(where.get(i)) && jsonParam.getString(where.get(i)) != null
					&& !jsonParam.getString(where.get(i)).equals("")) {
				sb.append("and  " + where.get(i) + "='" + jsonParam.getString(where.get(i)) + "'");
			}
		}

		return sb.toString();
	}

}
