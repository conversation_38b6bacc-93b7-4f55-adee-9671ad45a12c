package com.tzx.base.bo;

import com.tzx.base.constant.CacheTableConstant;
import com.tzx.framework.common.entity.Data;

import java.util.List;
import java.util.Map;

/**
 * Created by kevin on 2017-06-24.
 * 缓存业务数据接口
 */
public interface CacheManagerService {

    String	NAME	= "com.tzx.base.bo.imp.CacheManagerServiceImpl";

    /**
     * 设置需要缓存的业务数据
     * @param tenancyId
     * @param storeId
     * @throws Exception
     */
    public void loadBusinessData(String tenancyId, int storeId) throws Exception;

    /**
     * 获取需要缓存的业务数据
     * @param param
     * @param type
     * @return
     * @throws Exception
     */
    public List<Map<String, Object>> getBusinessData(Data param, CacheTableConstant type) throws Exception;
}
