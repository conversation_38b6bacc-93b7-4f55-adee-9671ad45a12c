package com.tzx.framework.common.util;


import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;
import org.springframework.jdbc.support.rowset.SqlRowSet;

import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.pos.service.rest.PosRest;

import net.sf.json.JSONObject;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

public class Tools
{
	private static Logger logger = Logger.getLogger(Tools.class);
	
	public static boolean hv(String s)
	{
		return s == null || s.trim().isEmpty() || "null".equals(s.toLowerCase()) ? false : true;
	}
	
	public static boolean hv(List<?> l)
	{
		return l == null || l.isEmpty() ? false : true;
	}
	
	public static boolean hv(Object o)
	{
		return o == null ? false : true;
	}
	
	public static Object fromJsonToJava(JSONObject json,Class pojo) throws Exception{
		         // 首先得到pojo所定义的字段
		          Field [] fields = pojo.getDeclaredFields();
		          // 根据传入的Class动态生成pojo对象
		          Object obj = pojo.newInstance();
		          for(Field field: fields){
		              // 设置字段可访问（必须，否则报错）
		              field.setAccessible(true);
		             // 得到字段的属性名
		             String name = field.getName();
		             // 这一段的作用是如果字段在JSONObject中不存在会抛出异常，如果出异常，则跳过。
		             try{
		                     json.get(name);
		             }catch(Exception ex){
		                 continue;
		             }
		             if(json.get(name) != null && !"".equals(json.getString(name))){
	                 // 根据字段的类型将值转化为相应的类型，并设置到生成的对象中。
		                 if(field.getType().equals(Long.class) || field.getType().equals(long.class)){
		                     field.set(obj, Long.parseLong(json.getString(name)));
		                 }else if(field.getType().equals(String.class)){
		                     field.set(obj, json.getString(name));
	                 } else if(field.getType().equals(Double.class) || field.getType().equals(double.class)){
		                     field.set(obj, Double.parseDouble(json.getString(name)));
		                } else if(field.getType().equals(Integer.class) || field.getType().equals(int.class)){
		                    field.set(obj, Integer.parseInt(json.getString(name)));
		                 } else if(field.getType().equals(java.util.Date.class)){
		                     field.set(obj, Date.parse(json.getString(name)));
		                 }else{
		                     continue;
		                 }
		             }
		         }
		         return obj;
		     }
	
	public static boolean isUpdateSuccess(Object obj) {
		try {
			if ( Integer.valueOf(obj.toString()).intValue() > 0 ) {
				return true;
			}
		} catch (Exception e) {
			//ignore exception
		}
		return false;
	}
	/**
	 * 判断Object对象是NUll或empty
	 * @param obj
	 * @return
	 */
	public static boolean isNullOrEmpty(Object obj) 
	{  
        if (obj == null)  
            return true;  
        
		if ("null".equals(obj))
		{
			return true;
		}
  
        if (obj instanceof CharSequence)
        {
        	if (((CharSequence) obj).length() == 0)
            {
            	return true;
            }
        }
            
        if (obj instanceof Collection)  
            return ((Collection) obj).isEmpty();  
  
        if (obj instanceof Map)  
            return ((Map) obj).isEmpty();  
  
        if (obj instanceof Object[]) {  
            Object[] object = (Object[]) obj;  
            if (object.length == 0) {  
                return true;  
            }  
            boolean empty = true;  
            for (int i = 0; i < object.length; i++) {  
                if (!isNullOrEmpty(object[i])) {  
                    empty = false;  
                    break;  
                }  
            }  
            return empty;  
        }
		return false;
    }
	
	public static Integer parseInt(String obj,Integer defaultInt) throws Exception
	{
		if(isNullOrEmpty(obj))
		{
			return defaultInt;
		}
		else
		{
			return Integer.parseInt(obj);
		}
	}
	
	/**
	 * @Description 输入流内存拷贝至输出流。
	 * @param  input  ：输入流
	 * @param  output : 输出流
	 * @return 返回参数：拷贝数据大小
	 * @exception
	 * <AUTHOR> email:<EMAIL>
	 * @version 1.0  2017-12-08
	 * @see
	 */
	public static int copy(InputStream input, OutputStream output) throws IOException {
	    long count = copyLarge(input, output);
	    if (count > Integer.MAX_VALUE) {
	        return -1;
	    }
	    return (int) count;
	}
	public static long copyLarge(InputStream input, OutputStream output)
	        throws IOException {
	    return copyLarge(input, output, new byte[4096]);//大小为4096
	}

	public static long copyLarge(InputStream input, OutputStream output, byte[] buffer)
	        throws IOException {
	    long count = 0;
	    int n = 0;
	    while (-1 != (n = input.read(buffer))) {
	        output.write(buffer, 0, n);
	        count += n;
	    }
	    return count;
	}
	
	/*
	 * 从txt文件读取数据到数据中
	 */
	public static List<String> getListFromFile(String filePath) {
		List<String> sqlList = null;
		try {
			sqlList = new ArrayList<String>();
			File file = new File(filePath);
			if (file.isFile() && file.exists()) { // 判断文件是否存在
				InputStreamReader read = new InputStreamReader(new FileInputStream(file));
				BufferedReader bufferedReader = new BufferedReader(read);
				String lineTxt = null;
				while ((lineTxt = bufferedReader.readLine()) != null) {
					sqlList.add(lineTxt);
				}
				read.close();
			} else {
				logger.info("找不到指定的文件");
			}
		} catch (Exception e) {
			logger.info("读取文件内容出错");
			e.printStackTrace();
		}
		return sqlList;
	}
	
	/**
     * delet 指定目录下的文件夹以及下面的所有文件
     * @param dir
     * @return
     */
    public static boolean deleteDir(File dir) {
        if(dir.isDirectory()) {
            String [] children=dir.list();
            for (int i = 0; i < children.length; i++) {
                boolean success =deleteDir(new File(dir, children[i]));//如果有文件将一直调用deleteDir()方法
                if(!success){
                    return false;
                }
                System.out.println(children[i]);//文件夹下面的文件
            }
        }
        System.out.println("The directory is deleted.");
        return dir.delete();
    }
    
	/*
	 * 判断门店是否营业状态（营业中、非营业）
	 */
	public static boolean judgeStoreStateOpen(String tenantId, Integer storeId) {
		boolean bo = true;
		try {
			if (PosRest.storeStateOpen == true) {
				// 营业中
				bo = true;
				return bo;
			} else {
				// 非营业
				GenericDao dao= (GenericDao) SpringConext.getBean("genericDaoImpl");
				String sql = "select count(id) as num from pos_opt_state where report_date in (select max(report_date) as report_date from pos_opt_state where content = 'DAYBEGAIN' and store_id = '"
						+ storeId + "' and tenancy_id = '" + tenantId + "') and content = 'DAYEND'";
				SqlRowSet rs = dao.getJdbcTemplate(tenantId).queryForRowSet(sql);
				int num = 0;
				while (rs.next()) {
					num = rs.getInt("num");
				}
				if (num > 0) {
					bo = false;
					return bo;
				} else {
					PosRest.storeStateOpen = true;
					bo = true;
					return bo;
				}
			}
		} catch (Exception e) {
			logger.error(e.getMessage());
		}
		return bo;
	}
	
	public static String[] getJsonKey(JSONObject json) throws Exception
	{
		if (null == json)
		{
			return null;
		}
		String[] keys = new String[json.keySet().size()];
		int i = 0;
		for (Iterator<?> it = json.keySet().iterator(); it.hasNext();)
		{
			keys[i] = (String) it.next();
			i++;
		}
		return keys;
	}

	@SuppressWarnings("restriction")
	public static String encodeBASE64(byte[] content)
	{
		BASE64Encoder base64en = new BASE64Encoder();
		return base64en.encodeBuffer(content);
	}

	@SuppressWarnings("restriction")
	public static byte[] decodeBASE64(String content) throws IOException
	{
		BASE64Decoder decoder = new BASE64Decoder();
		return decoder.decodeBuffer(content);
	}
}
