package com.tzx.base.po.springjdbc.dao.imp;

import java.util.Date;

import org.springframework.stereotype.Repository;

import com.tzx.framework.common.util.dao.impl.GenericDaoImpl;
import com.tzx.base.po.springjdbc.dao.MqTtransferLogDao;

import net.sf.json.JSONObject;


@Repository(MqTtransferLogDao.NAME)
public class MqTtransferLogDaoImpl extends GenericDaoImpl implements MqTtransferLogDao {

	@Override
	public Integer addDownLogs(String tenancyId,Integer storeId,String url,Integer fileSize,Integer blockSize,String remark){
		JSONObject param = new JSONObject();
		try {
			param.put("tenancy_id", tenancyId);
			param.put("store_id", storeId);
			param.put("transfer_type", "mq_download");
			param.put("url", url);
			param.put("file_size", fileSize);
			param.put("block_size", blockSize);
			param.put("remark", remark);
			param.put("last_updatetime", new Date());
			return (Integer) this.insertIgnorCase(tenancyId, "mq_transfer_logs", param);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
}
