package com.tzx.clientorder.wlifeprogram.bo;

import net.sf.json.JSONObject;

/**
 * Created by qing<PERSON> on 2018-06-27.
 */
public interface BasicService {
    String NAME = "com.tzx.clientorder.wlifeprogram.bo.BasicServiceImp";

    /**
     * 查询门店的基础资料信息
     * @param responseJson
     * @param jsobj
     * @throws Exception
     */
    void getBasicInfo(JSONObject responseJson, JSONObject jsobj,String secret) throws Exception;

    /**
     * 查询门店的菜品沽清信息
     * @param responseJson
     * @param jsobj
     * @throws Exception
     */
    void getSoldOutInfo(JSONObject responseJson, JSONObject jsobj) throws Exception;
}
