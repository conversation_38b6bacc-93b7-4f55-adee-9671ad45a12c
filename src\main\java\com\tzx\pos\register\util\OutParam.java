package com.tzx.pos.register.util;

import java.util.ArrayList;
import java.util.Arrays;

public enum OutParam {

	/**
	 * 根据设备唯一码判断是否注册
	 */
	registerByCode(new String[] { "device_unique_code" ,"devices_ip"}),
	/**
	 * 校验注册key并保存信息
	 */
	checkCode(new String[] { "device_unique_code", "devices_ip", "store_id", "show_type" }),
	/**
	 * 查询kvs类别
	 */
	getHqKvs(new String[] { "store_id" }),
	/**
	 * 查询显示方式
	 */
	getSysDictionary(new String[] { "class_identifier_code" });
	
	private OutParam(String[] param) {
		this.param = param;
	}

	private OutParam() {
		
	}
	public String[] param;

	public String[] getParam() {
		return param;
	}

	public void setParam(String[] param) {
		
		String[] p=new String[param.length+(this.param==null?0:this.param.length)];
		ArrayList<String> list=new ArrayList<String>();
		for(int i=0;i<(this.param==null?0:this.param.length);i++){
			list.add(this.param[i]);
		}
		for(int i=0;i<param.length;i++){
			list.add(param[i]);
		}
		for(int i=0;i<p.length;i++){
			p[i]=list.get(i);
		}
			
		this.param = p;
	}
	
	
	public static void main(String[] args) {
		String[] str=new String[]{"nan"};
		String[] st=new String[]{"xue"};
		String[] s=new String[2];
	}
	

}
