package com.tzx.pos.bo.imp;

import com.tzx.base.cache.CacheManager;
import com.tzx.base.constant.CacheTableConstant;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.Tools;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.CacheTableUtil;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.bo.PosPrintModeService;
import com.tzx.pos.bo.PosPrintNewService;
import com.tzx.pos.bo.PosPrintService;
import com.tzx.pos.bo.imp.print.*;
import com.tzx.pos.po.springjdbc.dao.PosDao;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

@Service(PosPrintNewService.NAME)
public  class PosPrintNewServiceImp extends PosBaseServiceImp implements PosPrintNewService
{
	@Resource(name = PosDao.NAME)
	private PosDao posDao;
	
	@Resource(name = PosPrintService.NAME)
	private PosPrintService		posPrintService;
	
	private static final Logger	logger	= Logger.getLogger(PosPrintNewServiceImp.class);
	
	/**
	 * 根据操作找打印模板
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param functionId
	 * @param para
	 * 
	 * @return
	 */
	@Override
	public void posPrintByFunction(String tenancyId, int storeId, String functionId, JSONObject para) throws Exception
	{
		
		para.put("function_id", functionId);
		logger.info(" ============>根据操作找打印模板处理开始：传入参数为->tenancyId:" + tenancyId + ",storeId:" + storeId + "," + para.toString());
		
		String takeOutOrWeChat = null;
		Iterator  keys = para.keys();
		while(keys.hasNext()){
			String key = keys.next().toString();
			if(key.equals("order_type")){
				takeOutOrWeChat = para.getString("order_type");
				break;
			}
		}
		
		List<String> printModeList = posDao.getSysPrintModeList(tenancyId, storeId, functionId,takeOutOrWeChat);// 这应该返回一List

		logger.debug(" 操作【" + functionId + "】对应的打印模板：" + printModeList.toString());
		if(printModeList.size() > 0)
		{
			for(String printMode : printModeList)
			{
				posPrintByMode(tenancyId, storeId, printMode, para);
			}
			// 更新菜品打印状态
//			String billNum = para.optString("bill_num");
//			posDao.UpdateItemPrintTag(tenancyId, storeId, billNum);
		}
		else
		{
			logger.info(" 分单失败：未找到打印模板");
		}
		logger.info(" ============>根据操作找打印模板处理结束");
	}
	
	/**
	 * 根据打印模板调用打印服务
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param printMode
	 * @param para
	 * 
	 * @return
	 */
	@Override
	public void posPrintByMode(String tenancyId, int storeId, String printMode, JSONObject para) throws Exception
	{
		if(!para.containsKey("tenancy_id") || "".endsWith(para.optString("tenancy_id")))
		{
			para.put("tenancy_id", tenancyId);
		}
		if(!para.containsKey("store_id") || 0 == para.optInt("store_id"))
		{
			para.put("store_id", storeId);
		}
		
		// 补打结账单(不是开发票的场合) or 补打随餐单 打印电子发票二维码
		if ((SysDictionary.PRINT_CODE_1110.equals(printMode) && !"invoice".equals(para.optString("invoice_repeat"))) 
				|| (SysDictionary.PRINT_CODE_1201.equals(printMode) && "1".equals(para.optString("is_repeat_1201"))))
		{
			JSONObject invJo = posDao.getInvoiceInfo(tenancyId, storeId, para);
			// 打印过电子发票才可以补打带电子发票的结账单
			if (!Tools.isNullOrEmpty(invJo) && "2".equals(invJo.optString("invoice_type")) && Tools.hv(invJo.optString("url_content")) &&invJo.optString("url_content").length()>0)
			{
				Timestamp last_updatetime = DateUtil.parseTimestamp(invJo.optString("last_updatetime"));
				String billNum = invJo.optString("bill_num");
				
				JSONObject billJo = posDao.getPosBillByBillnum(tenancyId, storeId, billNum);
				String orderNum = billJo.optString("order_num");
				int recoverCnt = billJo.optInt("recover_count");
				String fileName = "";
				if(Tools.isNullOrEmpty(orderNum))
				{
					fileName = billNum + "@" + recoverCnt;
				}
				else
				{
					fileName = orderNum + "@" + recoverCnt;
				}
				fileName += DateUtil.getYYYYMMDDHHMMSS(last_updatetime) + ".png";
				String webPathConst = com.tzx.framework.common.constant.Constant.getSystemMap().get("WEB_PATH");
				String strUrlPath = webPathConst + "img/qr_code/invoice/" + fileName;
				
				para.put("url_path", strUrlPath);
				para.put("is_invoice", "1");
			}
		}
		logger.info(" =========>根据打印模板分单处理开始：打印参数为->tenancyId:" + tenancyId + ",storeId:" + storeId + ",printMode：" + printMode + "," + para.toString());
		
		PosPrintModeService	posPrintModeService = null;
		try
		{
			String isLocalPrinter = posDao.getSysParameter(tenancyId, storeId, "IsLocalPrinter");

            Map<?, ?> sysParameterMap = CacheManager.getCacheMap(CacheTableConstant.SYS_PARAMETER.name());
            // 新打印分单模式
            String isNewPrintLogic = CacheTableUtil.getSysParameter("isNewPrintLogic", sysParameterMap);
            // 厨打是否启用序号打印
            String isPrintSerialNumber = CacheTableUtil.getSysParameter("isPrintSerialNumber", sysParameterMap);
            para.put("isPrintSerialNumber", isPrintSerialNumber);

			if("1".equals(isNewPrintLogic)){
                switch(printMode)
                {
                    case SysDictionary.PRINT_CODE_1106://单切单
                    case SysDictionary.PRINT_CODE_1104://标签切单
                        posPrintModeService = SpringConext.getApplicationContext().getBean(SingleModePrintServiceImp.class);
                        break;
                    case SysDictionary.PRINT_CODE_1113: // 一份一单
                        posPrintModeService = SpringConext.getApplicationContext().getBean(ACopyOfModePrintServiceImp.class);
                        break;
                    case SysDictionary.PRINT_CODE_1107://整台单
                        posPrintModeService = SpringConext.getApplicationContext().getBean(WholeModePrintServiceImp.class);
                        break;
                    case SysDictionary.PRINT_CODE_1105://大类
                        posPrintModeService = SpringConext.getApplicationContext().getBean(ClassModePrintServiceImp.class);
                        break;
                    case SysDictionary.PRINT_CODE_1109://退菜单
                        posPrintModeService = SpringConext.getApplicationContext().getBean(RetreatModePrintServiceImp.class);
                        break;
                    case SysDictionary.PRINT_CODE_1112://单品转台单
                        posPrintModeService = SpringConext.getApplicationContext().getBean(SingleChangeTableModePrintServiceImp.class);
                        break;
                    case SysDictionary.PRINT_CODE_1108://转台
                        posPrintModeService = SpringConext.getApplicationContext().getBean(WholeChangeTableModePrintServiceImp.class);
                        break;
                    case SysDictionary.PRINT_CODE_1103://POS点菜单
                    case SysDictionary.PRINT_CODE_1101://POS预打单
                    case SysDictionary.PRINT_CODE_1102://POS结账单
                    case SysDictionary.PRINT_CODE_1110://补打结账单
                        posPrintModeService = SpringConext.getApplicationContext().getBean(NewServiceModePrintServiceImp.class);
                        break;
                    case SysDictionary.PRINT_CODE_1301://POS交班单
                    case SysDictionary.PRINT_CODE_1302://补打交班单
                        String printType = para.optString("print_type");
                        String posNum = para.optString("pos_num");
                        String optNum = para.optString("opt_num");
                        String dateRemark = para.optString("date_remark");

                        if(!"1".equals(isLocalPrinter))
                        {
                            if ("1".equalsIgnoreCase(printType))
                            {
                                para.put("opt_pos_num", posNum);
                            }
                            else if ("2".equalsIgnoreCase(printType))
                            {
                                para.put("opt_pos_num", "");
                            }
                            if (StringUtils.isNotEmpty(dateRemark) && dateRemark.contains("~"))
                            {
                                String[] arr = dateRemark.split("~");
                                if (arr.length > 0)
                                {
                                    para.put("start_date", arr[0]);
                                    para.put("end_date", arr[1]);
                                }
                            }
                            para.put("cashier_num", optNum);
                            posPrintModeService = SpringConext.getApplicationContext().getBean(MemberModeServiceImp.class);
                        }
                        break;
                    case SysDictionary.PRINT_CODE_1013://会员卡发卡
                    case SysDictionary.PRINT_CODE_1008://会员卡充值
                    case SysDictionary.PRINT_CODE_1009://会员卡反充值
                    case SysDictionary.PRINT_CODE_1010://会员卡消费
                    case SysDictionary.PRINT_CODE_1011://会员卡反消费
                    case SysDictionary.PRINT_CODE_1012://会员卡退卡
                    case SysDictionary.PRINT_CODE_1015://会员卡补卡
                    case SysDictionary.PRINT_CODE_1201://外卖随餐单
                    case SysDictionary.PRINT_CODE_1202://外卖取消订单
                    case SysDictionary.PRINT_CODE_1203://外卖随餐单（异常单据）
                    case SysDictionary.PRINT_CODE_1204://外卖取消订单（异常单据）
                    case SysDictionary.PRINT_CODE_1303://交款凭证
                    case SysDictionary.PRINT_CODE_1304://零找金
                    case SysDictionary.PRINT_CODE_1016://会籍购买
                        if(!"1".equals(isLocalPrinter))
                        {
                            posPrintModeService = SpringConext.getApplicationContext().getBean(MemberModeServiceImp.class);
                        }
                        break;
                    case SysDictionary.PRINT_CODE_1205://外卖点餐清单
                    	posPrintModeService = SpringConext.getApplicationContext().getBean(TakeOutOrderPrintServiceImpl.class);
                        break;
                    case SysDictionary.PRINT_CODE_1401://微信点餐清单
                    	posPrintModeService = SpringConext.getApplicationContext().getBean(WeChatOrderPrintServiceImpl.class);
                        break;
                    case SysDictionary.PRINT_CODE_1114://打印所有档口退菜单
                    	posPrintModeService = SpringConext.getApplicationContext().getBean(AllRetreatFoodPrintServiceImp.class);
                        break;
                    case SysDictionary.PRINT_CODE_1115://打印起菜通知单
                    	posPrintModeService = SpringConext.getApplicationContext().getBean(CallupItemRequisitionPrintServiceImp.class);
                        break;
                    default:
                        logger.info("分单失败：未找到打印模板1：" + printMode);
                        break;
                }
            }else{
                switch(printMode)
                {
                    case SysDictionary.PRINT_CODE_1106://单切单
                    case SysDictionary.PRINT_CODE_1104://标签切单
                        posPrintModeService = SpringConext.getApplicationContext().getBean(SingleModePrintServiceImp.class);
                        break;
                    case SysDictionary.PRINT_CODE_1113: // 一份一单
                        posPrintModeService = SpringConext.getApplicationContext().getBean(ACopyOfModePrintServiceImp.class);
                        break;
                    case SysDictionary.PRINT_CODE_1107://整台单
                        posPrintModeService = SpringConext.getApplicationContext().getBean(WholeModePrintServiceImp.class);
                        break;
                    case SysDictionary.PRINT_CODE_1105://大类
                        posPrintModeService = SpringConext.getApplicationContext().getBean(ClassModePrintServiceImp.class);
                        break;
                    case SysDictionary.PRINT_CODE_1109://退菜单
                        posPrintModeService = SpringConext.getApplicationContext().getBean(RetreatModePrintServiceImp.class);
                        break;
                    case SysDictionary.PRINT_CODE_1103://POS点菜单
                        posPrintModeService = SpringConext.getApplicationContext().getBean(OrderModePrintServiceImp.class);
                        break;
                    case SysDictionary.PRINT_CODE_1112://单品转台单
                        posPrintModeService = SpringConext.getApplicationContext().getBean(SingleChangeTableModePrintServiceImp.class);
                        break;
                    case SysDictionary.PRINT_CODE_1108://转台
                        posPrintModeService = SpringConext.getApplicationContext().getBean(WholeChangeTableModePrintServiceImp.class);
                        break;
                    case SysDictionary.PRINT_CODE_1101://POS预打单
                    case SysDictionary.PRINT_CODE_1102://POS结账单
                        if(!"1".equals(isLocalPrinter))
                        {
                            posPrintModeService = SpringConext.getApplicationContext().getBean(PaymentModePrintServiceImp.class);
                        }
                        break;
                    case SysDictionary.PRINT_CODE_1301://POS交班单
                    case SysDictionary.PRINT_CODE_1302://补打交班单
                        String printType = para.optString("print_type");
                        String posNum = para.optString("pos_num");
                        String optNum = para.optString("opt_num");
                        String dateRemark = para.optString("date_remark");

                        if(!"1".equals(isLocalPrinter))
                        {
                            if ("1".equalsIgnoreCase(printType))
                            {
                                para.put("opt_pos_num", posNum);
                            }
                            else if ("2".equalsIgnoreCase(printType))
                            {
                                para.put("opt_pos_num", "");
                            }
                            if (StringUtils.isNotEmpty(dateRemark) && dateRemark.contains("~"))
                            {
                                String[] arr = dateRemark.split("~");
                                if (arr.length > 0)
                                {
                                    para.put("start_date", arr[0]);
                                    para.put("end_date", arr[1]);
                                }
                            }
                            para.put("cashier_num", optNum);
                            posPrintModeService = SpringConext.getApplicationContext().getBean(PrintModeServiceImp.class);
                        }
                        break;
                    case SysDictionary.PRINT_CODE_1110://补打结账单
                    case SysDictionary.PRINT_CODE_1013://会员卡发卡
                    case SysDictionary.PRINT_CODE_1008://会员卡充值
                    case SysDictionary.PRINT_CODE_1009://会员卡反充值
                    case SysDictionary.PRINT_CODE_1010://会员卡消费
                    case SysDictionary.PRINT_CODE_1011://会员卡反消费
                    case SysDictionary.PRINT_CODE_1012://会员卡退卡
                    case SysDictionary.PRINT_CODE_1015://会员卡补卡
                    case SysDictionary.PRINT_CODE_1201://外卖随餐单
                    case SysDictionary.PRINT_CODE_1202://外卖取消订单
                    case SysDictionary.PRINT_CODE_1203://外卖随餐单（异常单据）
                    case SysDictionary.PRINT_CODE_1204://外卖取消订单（异常单据）
                    case SysDictionary.PRINT_CODE_1303://交款凭证
                    case SysDictionary.PRINT_CODE_1304://零找金
                    case SysDictionary.PRINT_CODE_1016://会籍购买
                        if(!"1".equals(isLocalPrinter))
                        {
                            posPrintModeService = SpringConext.getApplicationContext().getBean(PrintModeServiceImp.class);
                        }
                        break;
                    case SysDictionary.PRINT_CODE_1205://外卖点餐清单
                    	posPrintModeService = SpringConext.getApplicationContext().getBean(TakeOutOrderPrintServiceImpl.class);
                        break;
                    case SysDictionary.PRINT_CODE_1401://微信点餐清单
                    	posPrintModeService = SpringConext.getApplicationContext().getBean(WeChatOrderPrintServiceImpl.class);
                        break;
                    case SysDictionary.PRINT_CODE_1114://打印所有档口退菜单
                    	posPrintModeService = SpringConext.getApplicationContext().getBean(AllRetreatFoodPrintServiceImp.class);
                        break;
                    case SysDictionary.PRINT_CODE_1115://打印起菜通知单
                    	posPrintModeService = SpringConext.getApplicationContext().getBean(CallupItemRequisitionPrintServiceImp.class);
                        break;
                    default:
                        logger.info("分单失败：未找到打印模板1：" + printMode);
                        break;
                }
            }

			if(posPrintModeService != null){
				posPrintModeService.posPrint(tenancyId, storeId, para, printMode);
			}
		}
		catch(Exception e)
		{
			logger.error("分单失败：分单处理异常：" + printMode , e);
		}
	}
	
//	@Override
	public boolean isNONewPrint(String tenantId,Integer storeId) throws SystemException {
		String ret ="";
		try {
			ret = posDao.getSysParameter(tenantId,storeId,"new_printer_type");
		} catch (Exception e) {
			ret="";
		}
		if(StringUtils.isNotEmpty(ret) && !ret.equals("0")){
			return true;
		}else{
			return false;
		}
		
	}

    @Override
    public String getPosNumByMoveDevicesNum(String tenancyId, int storeId, String devicesNum) throws Exception {
        StringBuilder queryDevicesSql =new StringBuilder("select pos_num from pos_opt_state_devices where tenancy_id=? and store_id=? and devices_num=? and is_valid='1'");
        SqlRowSet rs = posDao.query4SqlRowSet(queryDevicesSql.toString(), new Object[]{tenancyId, storeId, devicesNum});

        if(rs.next()){
            return rs.getString("pos_num");
        }

        return "";
    }
}
