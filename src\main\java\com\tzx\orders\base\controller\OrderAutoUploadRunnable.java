package com.tzx.orders.base.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import net.sf.json.JSONObject;

import org.apache.log4j.Logger;

import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.orders.base.util.OrderUtil;
import com.tzx.orders.bo.dto.OrderState;
import com.tzx.orders.po.springjdbc.dao.OrdersManagementDao;
import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;

/**
 * 门店订单状态自动上传
 * 
 * 
 */
public class OrderAutoUploadRunnable implements Runnable
{

	private static final Logger	logger	= Logger.getLogger(OrderAutoUploadRunnable.class);

	@Override
	public void run()
	{
		try
		{
			Map<String, String> systemMap = Constant.getSystemMap();

			if (!systemMap.containsKey("tenent_id") || !systemMap.containsKey("store_id") || "".equals(systemMap.get("tenent_id")) || "".equals(systemMap.get("store_id")))
			{
				logger.error("门店订单状态自动上传报错:租户ID为空,或机构ID为空");
				return;
			}

			String tenancyId = systemMap.get("tenent_id");
			int storeId = Integer.parseInt(systemMap.get("store_id"));

			DBContextHolder.setTenancyid(tenancyId);
			OrdersManagementDao uploadDao = SpringConext.getApplicationContext().getBean(OrdersManagementDao.class);

			List<OrderState> orderStateList = uploadDao.getOrderStateForNotUpload(tenancyId, storeId);
			for (OrderState orderState : orderStateList)
			{
				List<OrderState> paraList = new ArrayList<OrderState>();
				paraList.add(orderState);

				JSONObject resultJson = OrderUtil.sendOrderState(tenancyId, storeId, Type.ORDER, Oper.update, paraList);
				if (0 == resultJson.optInt("code"))
				{
					JSONObject json = new JSONObject();
					json.put("order_code", orderState.getOrder_code());
					json.put("upload_tag", "1");
					uploadDao.updateOrderInfo(tenancyId, storeId, json);
				}
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
			logger.error("门店订单状态自动上传报错:", e);
		}
	}

}
