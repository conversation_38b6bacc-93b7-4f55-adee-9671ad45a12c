package com.tzx.base.bo;

import java.util.List;

import com.tzx.framework.common.exception.SystemException;

import net.sf.json.JSONObject;

public interface PrintFormatService
{

	String	NAME	= "com.tzx.base.bo.imp.PrintFormatServiceImpl";

	public List<JSONObject> testTempSql(String tenancyID,JSONObject condition)throws Exception;
	
	public JSONObject queryPrintFormat(String tenancyID,JSONObject condition)throws Exception;
	
	public Object savePrintFormat(String tenancyID,JSONObject condition)throws Exception,SystemException;
	
	public List<JSONObject> findMainDataColumn(String tenancyID,JSONObject condition)throws Exception;
	
	public List<JSONObject> findDetailDataColumn(String tenancyID,JSONObject condition)throws Exception;
	
	
	public void print(String tenancyID,JSONObject condition) throws Exception;
	/**
	 * 打印指定模板的账单
	 * @param tenancyID
	 * @param condition
	 * @throws Exception
	 */
	public void printBill(String tenancyID,JSONObject condition) throws Exception;
	/**
	 * 打印客户单（充值，消费等）
	 * @param tenancyID
	 * @param condition
	 * @throws Exception
	 */
	public void printCusBill(String tenancyID, JSONObject condition) throws Exception;
	/**
	 * 新打印接口
	 * @param tenancyID
	 * @param condition
	 * @throws Exception
	 */
	public void newPrint(String tenantId,JSONObject condition) throws Exception;
}
