package com.tzx.pos.bo;

import java.util.List;

import com.tzx.framework.common.entity.Data;

import net.sf.json.JSONObject;

public interface OrderDeliveryService {
	
	String NAME = "com.tzx.pos.bo.imp.OrderDeliveryServiceImpl";

	/**
	 * 查询订单配送地址
	 * @param data
	 * @param result
	 */
	public void findOrderDeliveryAddress(Data data, Data result);

	/**
	 * 外卖新增订单配送
	 * @param data
	 * @param result
	 */
	public void addOrderDelivery(Data param, Data result);

	/**
	 * BOH新增订单配送
	 * @param data
	 * @param result
	 */
	public void addBohOrderDelivery(Data param, Data result);

	/**
	 * 异常订单配送查询
	 * @param data
	 * @param result
	 */
	public void findExceptionDeliveryOrder(Data param, Data result);

	/**
	 * 超时未接单订单查询
	 * @param data
	 * @param result
	 */
	public List<JSONObject> findWmOrderException(String tenancyId, int storeId);

	/**
	 * 超时未接单订单是否允许自配送
	 * @param data
	 * @param result
	 */
	public void findExceptionOrderState(String tenancyId, JSONObject jo);

	/**
	 * 配送订单添加小费
	 * @param tenancyId
	 * @param obj
	 */
	public JSONObject addTips(String tenancyId, JSONObject obj);

	/**
	 * 配送订单选择配送渠道
	 * @param tenancyId
	 * @param obj
	 */
	public JSONObject distributionChannel(String tenancyId, JSONObject obj);

	/**
	 * 查询配送订单信息
	 * @param tenancyId
	 * @param obj
	 */
	public void queryOrderDelivery(Data param, Data result);

	
}
