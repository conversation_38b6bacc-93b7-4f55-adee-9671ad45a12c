package com.tzx.framework.common.util;

import static java.lang.System.out;

import java.awt.geom.GeneralPath;
import java.awt.geom.Point2D;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.httpclient.DefaultHttpMethodRetryHandler;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.HttpStatus;
import org.apache.commons.httpclient.methods.GetMethod;
import org.apache.commons.httpclient.params.HttpMethodParams;
import org.codehaus.jackson.JsonNode;
import org.codehaus.jackson.map.ObjectMapper;

import com.tzx.framework.common.entity.Gps;

/**
 * 各地图API坐标系统比较与转换;
 * WGS84坐标系：即地球坐标系，国际上通用的坐标系。设备一般包含GPS芯片或者北斗芯片获取的经纬度为WGS84地理坐标系,
 * 谷歌地图采用的是WGS84地理坐标系（中国范围除外）;
 * GCJ02坐标系：即火星坐标系，是由中国国家测绘局制订的地理信息系统的坐标系统。由WGS84坐标系经加密后的坐标系。
 * 谷歌中国地图和搜搜中国地图采用的是GCJ02地理坐标系; BD09坐标系：即百度坐标系，GCJ02坐标系经加密后的坐标系;
 * 搜狗坐标系、图吧坐标系等，估计也是在GCJ02基础上加密而成的。 chenhua
 */
public class MapUtil
{

	public static final String	BAIDU_LBS_TYPE	= "bd09ll";

	public static double		pi				= 3.1415926535897932384626;
	public static double		a				= 6378245.0;
	public static double		ee				= 0.00669342162296594323;
	
	/**
	 * 地球半径
	 */
	public static final double			EARTHR						= 6371229;
	
	/**
	 * 百度地图ak
	 */
	public static final String			MAPAK						= "de4ZwcG285Dc9fsGCndi17pj";

	/**
	 * 84 to 火星坐标系 (GCJ-02) World Geodetic System ==> Mars Geodetic System
	 * 
	 * @param lat
	 * @param lon
	 * @return
	 */
	public static Gps gps84_To_Gcj02(double lat, double lon)
	{
		if (outOfChina(lat, lon))
		{
			return null;
		}
		double dLat = transformLat(lon - 105.0, lat - 35.0);
		double dLon = transformLon(lon - 105.0, lat - 35.0);
		double radLat = lat / 180.0 * pi;
		double magic = Math.sin(radLat);
		magic = 1 - ee * magic * magic;
		double sqrtMagic = Math.sqrt(magic);
		dLat = (dLat * 180.0) / ((a * (1 - ee)) / (magic * sqrtMagic) * pi);
		dLon = (dLon * 180.0) / (a / sqrtMagic * Math.cos(radLat) * pi);
		double mgLat = lat + dLat;
		double mgLon = lon + dLon;
		return new Gps(mgLat, mgLon);
	}

	/**
	 * * 火星坐标系 (GCJ-02) to 84 * * @param lon * @param lat * @return
	 * */
	public static Gps gcj_To_Gps84(double lat, double lon)
	{
		Gps gps = transform(lat, lon);
		double lontitude = lon * 2 - gps.getWgLon();
		double latitude = lat * 2 - gps.getWgLat();
		return new Gps(latitude, lontitude);
	}

	/**
	 * 火星坐标系 (GCJ-02) 与百度坐标系 (BD-09) 的转换算法 将 GCJ-02 坐标转换成 BD-09 坐标
	 * 
	 * @param gg_lat
	 * @param gg_lon
	 */
	public static Gps gcj02_To_Bd09(double gg_lat, double gg_lon)
	{
		double x = gg_lon, y = gg_lat;
		double z = Math.sqrt(x * x + y * y) + 0.00002 * Math.sin(y * pi);
		double theta = Math.atan2(y, x) + 0.000003 * Math.cos(x * pi);
		double bd_lon = z * Math.cos(theta) + 0.0065;
		double bd_lat = z * Math.sin(theta) + 0.006;
		return new Gps(bd_lat, bd_lon);
	}

	/**
	 * * 火星坐标系 (GCJ-02) 与百度坐标系 (BD-09) 的转换算法 * * 将 BD-09 坐标转换成GCJ-02 坐标 * * @param
	 * bd_lat * @param bd_lon * @return
	 */
	public static Gps bd09_To_Gcj02(double bd_lat, double bd_lon)
	{
		double x = bd_lon - 0.0065, y = bd_lat - 0.006;
		double z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * pi);
		double theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * pi);
		double gg_lon = z * Math.cos(theta);
		double gg_lat = z * Math.sin(theta);
		return new Gps(gg_lat, gg_lon);
	}

	/**
	 * (BD-09)-->84
	 * 
	 * @param bd_lat
	 * @param bd_lon
	 * @return
	 */
	public static Gps bd09_To_Gps84(double bd_lat, double bd_lon)
	{

		Gps gcj02 = MapUtil.bd09_To_Gcj02(bd_lat, bd_lon);
		Gps map84 = MapUtil.gcj_To_Gps84(gcj02.getWgLat(), gcj02.getWgLon());
		return map84;

	}

	public static boolean outOfChina(double lat, double lon)
	{
		if (lon < 72.004 || lon > 137.8347) return true;
		if (lat < 0.8293 || lat > 55.8271) return true;
		return false;
	}

	public static Gps transform(double lat, double lon)
	{
		if (outOfChina(lat, lon))
		{
			return new Gps(lat, lon);
		}
		double dLat = transformLat(lon - 105.0, lat - 35.0);
		double dLon = transformLon(lon - 105.0, lat - 35.0);
		double radLat = lat / 180.0 * pi;
		double magic = Math.sin(radLat);
		magic = 1 - ee * magic * magic;
		double sqrtMagic = Math.sqrt(magic);
		dLat = (dLat * 180.0) / ((a * (1 - ee)) / (magic * sqrtMagic) * pi);
		dLon = (dLon * 180.0) / (a / sqrtMagic * Math.cos(radLat) * pi);
		double mgLat = lat + dLat;
		double mgLon = lon + dLon;
		return new Gps(mgLat, mgLon);
	}

	public static double transformLat(double x, double y)
	{
		double ret = -100.0 + 2.0 * x + 3.0 * y + 0.2 * y * y + 0.1 * x * y + 0.2 * Math.sqrt(Math.abs(x));
		ret += (20.0 * Math.sin(6.0 * x * pi) + 20.0 * Math.sin(2.0 * x * pi)) * 2.0 / 3.0;
		ret += (20.0 * Math.sin(y * pi) + 40.0 * Math.sin(y / 3.0 * pi)) * 2.0 / 3.0;
		ret += (160.0 * Math.sin(y / 12.0 * pi) + 320 * Math.sin(y * pi / 30.0)) * 2.0 / 3.0;
		return ret;
	}

	public static double transformLon(double x, double y)
	{
		double ret = 300.0 + x + 2.0 * y + 0.1 * x * x + 0.1 * x * y + 0.1 * Math.sqrt(Math.abs(x));
		ret += (20.0 * Math.sin(6.0 * x * pi) + 20.0 * Math.sin(2.0 * x * pi)) * 2.0 / 3.0;
		ret += (20.0 * Math.sin(x * pi) + 40.0 * Math.sin(x / 3.0 * pi)) * 2.0 / 3.0;
		ret += (150.0 * Math.sin(x / 12.0 * pi) + 300.0 * Math.sin(x / 30.0 * pi)) * 2.0 / 3.0;
		return ret;
	}

	/**
	 * 根据经纬度算出两点距离
	 * 
	 * @param longt1
	 * @param lat1
	 * @param longt2
	 * @param lat2
	 * @return
	 */
	public static double getDistance(double longt1, double lat1, double longt2, double lat2)
	{
//		double pi = Constant.PI;
//		double r = Constant.EARTHR;
		double x, y, distance;
		x = (longt2 - longt1) * pi * EARTHR * Math.cos(((lat1 + lat2) / 2) * pi / 180) / 180;
		y = (lat2 - lat1) * pi * EARTHR / 180;
		distance = Math.hypot(x, y);
		return distance;
	}

	/**
	 * 将google地图、soso地图、aliyun地图、mapabc地图和amap地图所用坐标转换为百度米坐标，手机浏览器使用
	 * 
	 * @param nb_x
	 * @param nb_y
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	public static List<Map> getBaiduLocation(String param)
	{
		List<Map> list = new ArrayList<Map>();
		Map<String, String> paramMap = new HashMap<String, String>();
		paramMap.put("coords", param);
		paramMap.put("from", "5");
		paramMap.put("to", "6");
		paramMap.put("ak", MAPAK);
		String url = "http://api.map.baidu.com/geoconv/v1/";

		JsonNode rootNode = sendHttpRequest(url, paramMap);
		JsonNode result = rootNode.findValue("result");
		for (JsonNode j : result)
		{
			Map<String, String> map = new HashMap<String, String>();
			double x = j.get("x").getValueAsDouble();
			double y = j.get("y").getValueAsDouble();
			// 精度转换
			DecimalFormat a = new DecimalFormat("###0.000000");
			map.put("nb_x", a.format(x));
			map.put("nb_y", a.format(y));
			list.add(map);
		}

		return list;
	}

	/**
	 * 根据经纬度获取城市
	 * 
	 * @param tenantId
	 * @return
	 */
	public static String getCityForLocation(double lat, double lng) throws Exception
	{
		HttpClient client = new HttpClient();
		JsonNode rootNode = null;
		GetMethod method = null;
		String city = "";

		// 测试地址
		String url = "http://api.map.baidu.com/geocoder/v2/?ak=" + MAPAK + "&location=" + lat + "," + lng + "&output=json&pois=1";
		method = new GetMethod(url);
		method.getParams().setParameter(HttpMethodParams.RETRY_HANDLER, new DefaultHttpMethodRetryHandler(3, false));

		// Execute the method.
		int statusCode = client.executeMethod(method);
		if (statusCode != HttpStatus.SC_OK)
		{
			method.releaseConnection();
			throw new Exception("http请求异常：" + method.getStatusLine());
		}

		String respStr = method.getResponseBodyAsString();

		ObjectMapper objectMapper = new ObjectMapper();
		rootNode = objectMapper.readTree(respStr);
		JsonNode node1 = rootNode.get("result");
		JsonNode node2 = node1.get("addressComponent");
		city = node2.get("city").getTextValue();

		return city;
	}
	
	
	/**
	 * 根据经纬度获取城市和地区
	 * @param lat
	 * @param lng
	 * @return
	 * @throws Exception
	 * 备注：kencery 2016年6月8日  添加返回省的标识
	 * 
	 */
	public static JSONObject getCityandtown(double lat, double lng) throws Exception
	{
		HttpClient client = new HttpClient();
		JsonNode rootNode = null;
		GetMethod method = null;
		JSONObject obj = new JSONObject();
		String province="";
		String city = "";
		String town = "";
		String reg_no = "";
		
		// 测试地址
		String url = "http://api.map.baidu.com/geocoder/v2/?ak=" + MAPAK + "&location=" + lat + "," + lng + "&output=json&pois=1";
		method = new GetMethod(url);
		method.getParams().setParameter(HttpMethodParams.RETRY_HANDLER, new DefaultHttpMethodRetryHandler(3, false));

		// Execute the method.
		int statusCode = client.executeMethod(method);
		if (statusCode != HttpStatus.SC_OK)
		{
			method.releaseConnection();
			throw new Exception("http请求异常：" + method.getStatusLine());
		}

		String respStr = method.getResponseBodyAsString();

		ObjectMapper objectMapper = new ObjectMapper();
		rootNode = objectMapper.readTree(respStr);
		JsonNode node1 = rootNode.get("result");
		JsonNode node2 = node1.get("addressComponent");
		province=node2.get("province").getTextValue();
		city = node2.get("city").getTextValue();
		town = node2.get("district").getTextValue();
		reg_no = node2.get("adcode").getTextValue();
		String formatted_address = node1.get("formatted_address").getTextValue();

		obj.put("province", province);
		obj.put("city", city);
		obj.put("town", town);
		obj.put("reg_no", reg_no);
		obj.put("formatted_address",formatted_address);
		//adcode
		return obj;
	}
	
	
	
	/**
	 * 获取搜索的地址
	 * @param city
	 * @param place
	 * @return
	 * @throws Exception
	 */
	public static JSONArray getPlaceSearch(String city,String place) throws Exception{
		city = checkEmpty(city)?city:"全国";
		String url = "http://api.map.baidu.com/place/v2/suggestion?q="+URLEncoder.encode(place,"UTF-8")+"&region="
				+URLEncoder.encode(city,"UTF-8")+"&output=json&ak="+MAPAK;
		String result = HttpUtil.sendGetRequest(url, "UTF-8");
		if(result!=null){
			JSONObject json = JSONObject.fromObject(result);
			if(json.optJSONArray("result")!=null && json.optJSONArray("result").size()>0){
				return json.optJSONArray("result");
			}
		}
		return null;
	}
	/**
	 * 计算坐标是否在多边形内
	 * @param point
	 * @param polygon
	 * @return
	 */
	public static boolean checkWithJdkGeneralPath(Point2D.Double point, List<Point2D.Double> polygon) {
		GeneralPath p = new GeneralPath();
		Point2D.Double first = polygon.get(0);
		p.moveTo(first.x, first.y);
		for (Point2D.Double d : polygon) {
	      p.lineTo(d.x, d.y);
		}
		p.lineTo(first.x, first.y);
		p.closePath();
		return p.contains(point);
	}
	/**
	 * 根据坐标获取该坐标的省市区信息
	 * @param lat
	 * @param lng
	 * @return
	 */
	public static JSONObject getProvinceDetail(double lat, double lng){
		String url = "http://api.map.baidu.com/geocoder/v2/?output=json&ak="+MAPAK+"&location="+lat+","+lng;
		String result = HttpUtil.sendGetRequest(url, "UTF-8");
		if(result!=null){
			JSONObject json = JSONObject.fromObject(result);
			if("0".equals(json.optString("status"))){
				JSONObject resultJson = json.optJSONObject("result");
				JSONObject p = new JSONObject();
				p.put("province", resultJson.optJSONObject("addressComponent").optString("province"));
				p.put("city", resultJson.optJSONObject("addressComponent").optString("city"));
				p.put("district", resultJson.optJSONObject("addressComponent").optString("district"));
				return p;
			}
		}
		return null;
	}
	
	public static void main(String[] args)
	{

		out.println(MapUtil.bd09_To_Gcj02(39.998931,116.343321));
	}

	/**
	 * 判断坐标在给定范围之内
	 * @param gps
	 * @param polygonCoordinate 多边形坐标值，分号分隔的多个经纬度坐标
     * @return
     */
	public static boolean inBounds(Gps gps, String polygonCoordinate) {
		Point2D.Double point =new Point2D.Double(gps.getWgLon(),gps.getWgLat());
		List<Point2D.Double> polygon = new ArrayList<Point2D.Double>();
		String[] temp = polygonCoordinate.split(";");
		for(int i=0;i<temp.length;i++){
			if(temp[i]!=null && temp[i].length()>0){
				String[] pointArray = temp[i].split(",");
				Point2D.Double pointElement =new Point2D.Double(Double.parseDouble(pointArray[0]), Double.parseDouble(pointArray[1]));
				polygon.add(pointElement);
			}
		}
		return MapUtil.checkWithJdkGeneralPath(point, polygon);
	}
	
	/**
	 * 判断字符串不为空
	 * 
	 * @param str
	 * @return
	 */
	public static boolean checkEmpty(String str)
	{
		if (null != str && !"".equals(str))
		{
			return true;
		}
		return false;
	}
	
	/**
	 * 微信http请求接口，用于公众平台交互，获取数据或推送数据
	 * 
	 * @param url
	 *            要请求的url
	 * @param paramMap
	 *            请求的参数，map
	 * @return
	 */
	public static JsonNode sendHttpRequest(String url, Map<String, String> map)
	{
		HttpClient client = new HttpClient();
		JsonNode rootNode = null;
		GetMethod method = null;

		StringBuilder sb = new StringBuilder(url + "?");
		Iterator<Map.Entry<String, String>> it = map.entrySet().iterator();
		while (it.hasNext())
		{
			Map.Entry<String, String> entry = it.next();
			sb.append(entry.getKey() + "=" + entry.getValue() + "&");
		}
		String requestUrl = sb.toString().substring(0, sb.toString().length() - 1);

		try
		{
			method = new GetMethod(requestUrl);
			method.getParams().setParameter(HttpMethodParams.RETRY_HANDLER, new DefaultHttpMethodRetryHandler(3, false));

			// Execute the method.
			int statusCode = client.executeMethod(method);
			if (statusCode != HttpStatus.SC_OK)
			{
				method.releaseConnection();
				throw new Exception("http请求异常：" + method.getStatusLine());
			}

			String respStr = new String(method.getResponseBody(), "utf-8");

			ObjectMapper objectMapper = new ObjectMapper();
			rootNode = objectMapper.readTree(respStr);
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		finally
		{
			// Release the connection.
			method.releaseConnection();
		}
		return rootNode;
	}
}