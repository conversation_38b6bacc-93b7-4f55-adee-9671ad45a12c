package com.tzx.orders.bo.dto;

import java.io.Serializable;

public class PosBillPayment implements Serializable
{
	/**
	 * 
	 */
	private static final long	serialVersionUID	= 6614741495501043539L;

	private String				jzid;
	private String				amount;
	private String				count;
	private String				number;
	private String				phone;
	private String				currency_amount;
	private String				reason_id;
	private String				customer_id;
	private String				bill_code;
	private String				remark;

	public String getJzid()
	{
		return jzid;
	}

	public void setJzid(String jzid)
	{
		this.jzid = jzid;
	}

	public String getAmount()
	{
		return amount;
	}

	public void setAmount(String amount)
	{
		this.amount = amount;
	}

	public String getCount()
	{
		return count;
	}

	public void setCount(String count)
	{
		this.count = count;
	}

	public String getNumber()
	{
		return number;
	}

	public void setNumber(String number)
	{
		this.number = number;
	}

	public String getPhone()
	{
		return phone;
	}

	public void setPhone(String phone)
	{
		this.phone = phone;
	}

	public String getCurrency_amount()
	{
		return currency_amount;
	}

	public void setCurrency_amount(String currency_amount)
	{
		this.currency_amount = currency_amount;
	}

	public String getReason_id()
	{
		return reason_id;
	}

	public void setReason_id(String reason_id)
	{
		this.reason_id = reason_id;
	}

	public String getCustomer_id()
	{
		return customer_id;
	}

	public void setCustomer_id(String customer_id)
	{
		this.customer_id = customer_id;
	}

	public String getBill_code()
	{
		return bill_code;
	}

	public void setBill_code(String bill_code)
	{
		this.bill_code = bill_code;
	}

	public String getRemark()
	{
		return remark;
	}

	public void setRemark(String remark)
	{
		this.remark = remark;
	}
}
