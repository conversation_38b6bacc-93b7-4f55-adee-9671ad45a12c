package com.tzx.estate.base;

import java.util.HashMap;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 **/

public class Constant {
	//code的值
	public static final int CODE_SUCCESS = 0; //成功
	public static final int CODE_PARAM_FAILURE = 1; //参数失败
	public static final int CODE_AUTH_FAILURE = 2; //认证失败
	public static final int CODE_NULL_DATASET = 3; //数据集为空
	public static final int CODE_CHANGE_DATASOURCE_FAILURE = 4;//切换数据源失败
	public static final int CODE_INNER_EXCEPTION = 5; //内部错误
	public static final int CODE_STORE_EXCEPTION = 6; //门店状态异常
	public static final int CODE_CONN_EXCEPTION = 99; //连接异常
	public static final String CHANGE_DATASOURCE_FAILURE = "切换数据源失败";

	public static final String NOT_EXISTS_TYPE = "未找到对应的type";
	public static final String NOT_EXISTS_OPER = "未找到对应的oper";
		
	public static final String ESTATE_POST_SALES_FAILURE = "销售数据上传失败";
	public static final String ESTATE_POST_SALES_SUCCESS = "销售数据上传成功";
	
	public static final String ESTATE_POST_SALES_FIND_FAILURE = "请检查是否有数据需要上传";
	
	public static final String ESTATE_POST_SALES_PARAMNULL = "后台参数配置错误";
	
	public static final String ESTATE_POST_SALES_PART_FAILURE = "部分销售数据上传失败,请重新上传";
	
	public static Map<String, String> constantMap	= new HashMap<String, String>();
	
	public static Map<String, String> getConstantMap()
	{
		return constantMap;
	}

	public static void setSystemMap(Map<String, String> constantMap)
	{
		Constant.constantMap = constantMap;
	}
}
