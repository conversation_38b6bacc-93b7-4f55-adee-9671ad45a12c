package com.tzx.pos.bo;

import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.SystemException;
import net.sf.json.JSONObject;

import java.util.List;

/**
 * Created by kevin on 2017-11-03.
 */
public interface ComboSetMealService extends PosBaseService {

    String	NAME	= "com.tzx.pos.bo.imp.ComboSetMealServiceImp";

    /**
     *  设置组合套餐
     * @param param
     * @param result
     * @return
     * @throws SystemException
     * @throws Exception
     */
    public Data setMeal(Data param, Data result) throws SystemException;

    /**
     * 删除pos_bill_item_combo表
     * @param tenancyId
     * @param posBillItems
     * @throws Exception
     */
    public void deletePosBillItem(String tenancyId, List<JSONObject> posBillItems) throws  Exception;


    /**
     * @param param
     * @param result
     * @throws SystemException
     */
    public void comboSetMeal(Data param, Data result) throws Exception;

}
