package com.tzx.pos.bo.dto;

import java.io.Serializable;

/**
 * TODO
 * <AUTHOR> 
 * @日期：2015年5月18日-下午1:49:32
 */

public class PosTableInfo implements Serializable
{
	/**
	 * 
	 */
	private static final long	serialVersionUID	= 1L;
	
	private Integer id;
	private String tenancy_id;
	private Integer organ_id;
	private String table_code;
	private String table_name;
	private Integer table_property_id;
	private Integer seat_counts;
	private String floor;
	private Integer fwfz_id; 
	private String table_fw;
	private String is_cyfq;
	private String free_icon;
	private String use_icon;
	private String danzhuo_yy;
	private String business_area_id;
	
	public Integer getId()
	{
		return id;
	}
	public void setId(Integer id)
	{
		this.id = id;
	}
	public String getTenancy_id()
	{
		return tenancy_id;
	}
	public void setTenancy_id(String tenancy_id)
	{
		this.tenancy_id = tenancy_id;
	}
	public Integer getOrgan_id()
	{
		return organ_id;
	}
	public void setOrgan_id(Integer organ_id)
	{
		this.organ_id = organ_id;
	}
	public String getTable_code()
	{
		return table_code;
	}
	public void setTable_code(String table_code)
	{
		this.table_code = table_code;
	}
	public String getTable_name()
	{
		return table_name;
	}
	public void setTable_name(String table_name)
	{
		this.table_name = table_name;
	}
	public Integer getTable_property_id()
	{
		return table_property_id;
	}
	public void setTable_property_id(Integer table_property_id)
	{
		this.table_property_id = table_property_id;
	}
	public Integer getSeat_counts()
	{
		return seat_counts;
	}
	public void setSeat_counts(Integer seat_counts)
	{
		this.seat_counts = seat_counts;
	}
	public String getFloor()
	{
		return floor;
	}
	public void setFloor(String floor)
	{
		this.floor = floor;
	}
	public Integer getFwfz_id()
	{
		return fwfz_id;
	}
	public void setFwfz_id(Integer fwfz_id)
	{
		this.fwfz_id = fwfz_id;
	}
	public String getTable_fw()
	{
		return table_fw;
	}
	public void setTable_fw(String table_fw)
	{
		this.table_fw = table_fw;
	}
	public String getIs_cyfq()
	{
		return is_cyfq;
	}
	public void setIs_cyfq(String is_cyfq)
	{
		this.is_cyfq = is_cyfq;
	}
	public String getFree_icon()
	{
		return free_icon;
	}
	public void setFree_icon(String free_icon)
	{
		this.free_icon = free_icon;
	}
	public String getUse_icon()
	{
		return use_icon;
	}
	public void setUse_icon(String use_icon)
	{
		this.use_icon = use_icon;
	}
	public String getDanzhuo_yy()
	{
		return danzhuo_yy;
	}
	public void setDanzhuo_yy(String danzhuo_yy)
	{
		this.danzhuo_yy = danzhuo_yy;
	}
	public String getBusiness_area_id()
	{
		return business_area_id;
	}
	public void setBusiness_area_id(String business_area_id)
	{
		this.business_area_id = business_area_id;
	}
}
