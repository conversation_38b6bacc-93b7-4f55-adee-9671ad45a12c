package com.tzx.estate.po.springjdbc.dao.imp;

import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Repository;

import com.tzx.estate.po.springjdbc.dao.EstateDao;
import com.tzx.estate.base.dao.imp.BaseDaoImp;

@Repository(EstateDao.NAME)
public class EstateDaoImp extends BaseDaoImp implements EstateDao
{

	@Override
	public String getParamCodeByName(String paraCode) {
		// TODO Auto-generated method stub
		String sqlParam = "select trim(para_value) as para_value from sys_parameter where para_code = ? and valid_state='1'";
		SqlRowSet rsParam = query4SqlRowSet(sqlParam,new Object[] { paraCode});
		String postUrl = "";
		if (rsParam.next()) {
			postUrl = rsParam.getString("para_value");
		}
		return postUrl;
	}

}
