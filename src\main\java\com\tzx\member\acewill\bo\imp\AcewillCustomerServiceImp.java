package com.tzx.member.acewill.bo.imp;

import com.tzx.base.entity.CustomerOperationLogs;
import com.tzx.base.entity.PosThirdPaymentRefundEntity;
import com.tzx.framework.common.constant.CustomerOperationEnum;
import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.CrmErrorCode;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.CommonUtil;
import com.tzx.framework.common.util.DoubleHelper;
import com.tzx.framework.common.util.Tools;
import com.tzx.member.acewill.bo.AcewillCustomerService;
import com.tzx.member.common.entity.CrmCardTradingListEntity;
import com.tzx.member.common.entity.PosCustomerOperateListEntity;
import com.tzx.member.common.util.AcewillRequestUtil;
import com.tzx.member.common.util.AcewillUtil;
import com.tzx.member.crm.po.springjdbc.dao.CustomerDao;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.base.util.JsonUtil;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.base.util.ReqDataUtil;
import com.tzx.pos.bo.PosPrintNewService;
import com.tzx.pos.bo.PosPrintService;
import com.tzx.thirdpay.bo.ThirdPaymentRefundService;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.log4j.Logger;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;

/**
 * 微生活会员
 * 
 * <AUTHOR>
 *
 */
@Service(AcewillCustomerService.NAME)
public class AcewillCustomerServiceImp implements AcewillCustomerService {

	private static final Logger logger = Logger.getLogger(AcewillCustomerService.class);
	
	@Resource(name = CustomerDao.NAME)
	protected CustomerDao customerDao;
	
	@Resource(name = PosPrintNewService.NAME)
	protected PosPrintNewService		posPrintNewService;

	@Resource(name = PosPrintService.NAME)
	protected PosPrintService			posPrintService;
	
	@Resource(name = ThirdPaymentRefundService.NAME)
	private ThirdPaymentRefundService		thirdPaymentRefundService;
	
	@Resource(name = "transactionManager")
	private DataSourceTransactionManager transactionManager;
	
	@Override
	public Data commonPost(Data requestData, String requestUrl) throws Exception
	{
		// 传递参数Data 请求调用url 将返回参数 转换成Data类型
		String tenancyId = requestData.getTenancy_id();
		Integer storeId = requestData.getStore_id();

		String appId = customerDao.getSysParameter(tenancyId, storeId, "wlife_appid");
		String appKey = customerDao.getSysParameter(tenancyId, storeId, "wlife_appkey");

		return AcewillRequestUtil.commonPost(requestData, requestUrl, appId, appKey);
	}
	
	@Override
	public boolean checkIsAcewillCustomer(String tenantId, int storeId) throws Exception {
		String customerType = customerDao.getCustomerType(tenantId, storeId);
		return SysDictionary.CUSTOMER_TYPE_ACEWILL.equals(customerType);
	}
	
	public String getCashierId(String tenancyId,Integer storeId,String optNum) throws Exception
	{
		String cashierId = "-1";
		try
		{
			String sbSql = new String("select em.id,em.weishenghuo_id from employee as em where em.tenancy_id = ? and em.store_id = ? and em.id = ?");
			SqlRowSet rs = customerDao.query4SqlRowSet(sbSql.toString(), new Object[]
			{ tenancyId, storeId, Integer.parseInt(optNum) });

			if (rs.next())
			{
				cashierId = rs.getString("weishenghuo_id");
			}

			if (CommonUtil.isNullOrEmpty(cashierId))
			{
				cashierId = "-1";
			}
		}
		catch (Exception e)
		{
			logger.info("获取微生活收银员失败:", e);
		}
		return cashierId;
	}

	@Override
	public Data getAcewillCustomerUserInfo(String tenancyId, Integer storeId, String cardCode) throws Exception
	{
		JSONObject obj = new JSONObject();
		obj.put("card_code", cardCode);
		List<JSONObject> dataList = new ArrayList<JSONObject>();
		dataList.add(obj);

		Data requestData = new Data();
		requestData.setStore_id(storeId);
		requestData.setTenancy_id(tenancyId);
		requestData.setData(dataList);

		return this.getAcewillCustomerUserInfo(requestData);

	}

	/**
	 * 获取用户账户信息
	 */
	@Override
	public Data getAcewillCustomerUserInfo(Data requestData) throws Exception {
//		String requestUrl = getPequestUrl(CUSTOMER_USER_QUERY_URL);
		String tenancyId = requestData.getTenancy_id();
		Integer storeId = requestData.getStore_id();
		Map<String, Object> map = ReqDataUtil.getDataMap(requestData);
		Data resData = new Data();

		JSONObject jsonO = JSONObject.fromObject(requestData.getData().get(0));

		String shopId = this.getShopId(tenancyId, storeId);

		JSONObject jsonParam = new JSONObject();
		jsonParam.put("cno", jsonO.optString("card_code"));
		jsonParam.put("shop_id", Tools.hv(shopId) ? shopId : "");
		List<JSONObject> data = new ArrayList<JSONObject>();
		data.add(jsonParam);
		requestData.setData(data);
		logger.info("获取用户账户信息请求参数"+JsonUtil.DataToJson(requestData));
		try {
			//查询会员信息
			resData = this.commonPost(requestData, AcewillRequestUtil.CUSTOMER_USER_QUERY_URL);
		} catch (Exception e) {
			e.printStackTrace();
		}

		//会员信息封装
		List<JSONObject> dataList = new ArrayList<JSONObject>();
		if (Constant.CODE_SUCCESS == resData.getCode()) 
		{
			JSONArray jsonArray = JSONArray.fromObject(resData.getData());
			JSONObject jsonObj = null;
			JSONObject jsonCon = null;
			for (int i = 0; i < jsonArray.size(); i++) {
				try {
					jsonCon = JSONObject.fromObject(jsonArray.get(i));
				} catch (Exception e) {
					e.printStackTrace();
				}
				if (jsonCon != null) {
					jsonObj = new JSONObject();
					jsonObj.put("card_code", ParamUtil.getStringValueForNullByObject(jsonCon, "cno")); // 会员卡号
					jsonObj.put("third_code", ParamUtil.getStringValueForNullByObject(jsonCon, "acno")); // 实体卡卡号
					jsonObj.put("type", ParamUtil.getStringValueForNullByObject(jsonCon, "type")); // 用户类型
					jsonObj.put("type_name",
							AcewillUtil.getTypeName(ParamUtil.getStringValueForNullByObject(jsonCon, "type"))); // 用户类型名称
					jsonObj.put("name", ParamUtil.getStringValueForNullByObject(jsonCon, "name")); // 用户姓名
					jsonObj.put("mobil", ParamUtil.getStringValueForNullByObject(jsonCon, "phone")); // 用户电话
					jsonObj.put("birthday", ParamUtil.getStringValueForNullByObject(jsonCon, "birthday"));
					// 1:男 -- man 2:女 --woman
					jsonObj.put("sex", AcewillUtil.getSex(ParamUtil.getStringValueForNullByObject(jsonCon, "gender"))); // 性别
					jsonObj.put("registered", ParamUtil.getStringValueForNullByObject(jsonCon, "registered")); // 注册时间
					jsonObj.put("openid", ParamUtil.getStringValueForNullByObject(jsonCon, "openid"));// 微信openid
					// 会员是否关注公众号 true:关注 false:未关注
					Integer follow = AcewillUtil.getBoolean(jsonCon, "follow");
					jsonObj.put("follow", follow);
					jsonObj.put("qcno", ParamUtil.getStringValueForNullByObject(jsonCon, "qcno"));// 微信卡面的二维码卡号

					jsonObj.put("level", ParamUtil.getIntegerValueByObject(jsonCon, "grade"));// 等级编号
					jsonObj.put("level_name", ParamUtil.getStringValueForNullByObject(jsonCon, "grade_name"));// 等级名称

					jsonObj.put("rate", 100d);
					jsonObj.put("is_vipprice", "0");
					if (jsonCon.containsKey("member_discount_shops")) {
						JSONObject discountShops = jsonCon.optJSONObject("member_discount_shops");
						if ("1".equals(discountShops.optString("all"))
								|| discountShops.optJSONArray("list").contains(shopId)) {
							jsonObj.put("rate", ParamUtil.getIntegerValueByObject(jsonCon, "member_discount"));
							jsonObj.put("is_vipprice", "1");
						}
					}

					// is_vipprice 是否会员价 1:是 0:否
					Double balance = 0d;
					if (ParamUtil.getIntegerValueByObject(jsonCon, "balance") != null
							&& ParamUtil.getIntegerValueByObject(jsonCon, "balance") > 0) {
						balance = AcewillUtil.changeF2Y(jsonCon.optInt("balance"));
					}

					jsonObj.put("balance", balance);// 储值余额
					jsonObj.put("credit", ParamUtil.getIntegerValueByObject(jsonCon, "credit"));// 用户积分

					// 是否可以使用积分消费
					Integer use_credit = AcewillUtil.getBoolean(jsonCon, "use_credit");
					jsonObj.put("use_credit", use_credit);// 是否可以使用积分消费
					//  积分可抵扣的最大金额
					jsonObj.put("use_max_credit_money",
							ParamUtil.getIntegerValueByObject(jsonCon, "use_max_credit_money"));
					//  商家设置的多少积分抵1元
					jsonObj.put("credit_deduct",
							ParamUtil.getIntegerValueByObject(jsonCon, "credit_deduct"));

					// 是否可以使用积分消费 true:可以 false:不可以
					Integer charge_return_credit = AcewillUtil.getBoolean(jsonCon, "charge_return_credit");
					jsonObj.put("charge_return_credit", charge_return_credit);

					// 用户是否有效 true:有效 false:无效
					Integer in_effect = AcewillUtil.getBoolean(jsonCon, "in_effect");
					jsonObj.put("in_effect", in_effect);

					// 卡状态
					if (in_effect == 1) {
						jsonObj.put("card_state", 1);// 正常
					} else {
						jsonObj.put("card_state", 0);// 未激活
					}

					Double maxUseSaving = 0d;
					if (ParamUtil.getIntegerValueByObject(jsonCon, "max_use_saving") != null
							&& ParamUtil.getIntegerValueByObject(jsonCon, "max_use_saving") > 0) {
						maxUseSaving = AcewillUtil.changeF2Y(jsonCon.optInt("max_use_saving"));
					}
					jsonObj.put("max_use_saving", maxUseSaving);// 可以使用的储值最大值

					Double maxShopUseSaving = 0d;
//					if (ParamUtil.getIntegerValueByObject(jsonCon, "max_shop_use_saving") != null
//							&& ParamUtil.getIntegerValueByObject(jsonCon, "max_shop_use_saving") > 0) {
//						maxShopUseSaving = changeF2Y(jsonCon.optInt("max_shop_use_saving"));
//					}
					Integer maxShopUseSavingF = ParamUtil.getIntegerValueByObject(jsonCon, "max_shop_use_saving");
					if (null != maxShopUseSavingF)
					{
						maxShopUseSaving = AcewillUtil.changeF2Y(maxShopUseSavingF);
					}
					else
					{
						maxShopUseSaving = balance;
					}
					
					jsonObj.put("max_shop_use_saving", maxShopUseSaving);// 门店可用最大储值余额

					jsonObj.put("open_source", ParamUtil.getStringValueForNullByObject(jsonCon, "open_source"));// 会员开卡来源

					// 会员开卡来源的门店id
					jsonObj.put("open_source_shop_id",
							ParamUtil.getIntegerValueByObject(jsonCon, "open_source_shop_id"));
					//	最后消费的门店名称
					jsonObj.put("last_consume_shop_name", ParamUtil.getStringValueForNullByObject(jsonCon, "last_consume_shop_name"));
					//  最后消费的门店id
					jsonObj.put("last_consume_shop",
							ParamUtil.getIntegerValueByObject(jsonCon, "last_consume_shop"));


					// 会员交易验证方式 //短信：sms 密码：password
					jsonObj.put("trade_verify_type",
							ParamUtil.getStringValueForNullByObject(jsonCon, "trade_verify_type"));
					JSONArray couponsArrays = jsonCon.getJSONArray("coupons");
					List<JSONObject> couponsList = new ArrayList<JSONObject>();
					JSONObject couponsObj = null;
					for (int j = 0; j < couponsArrays.size(); j++) {

						couponsObj = new JSONObject();
						JSONObject couponsObject = JSONObject.fromObject(couponsArrays.get(j));
						if ((ParamUtil.getIntegerValueByObject(couponsObject, "deno") == null
								|| ParamUtil.getIntegerValueByObject(couponsObject, "deno") == 0)
								&& couponsObject.optJSONArray("products").isEmpty()) {
							continue;
						}
						couponsObj.put("template_id",
								ParamUtil.getStringValueForNullByObject(couponsObject, "template_id"));
						couponsObj.put("coupon_ids", couponsObject.optJSONArray("coupon_ids"));
						couponsObj.put("title", ParamUtil.getStringValueForNullByObject(couponsObject, "title"));
						couponsObj.put("deno", ParamUtil.getDoubleValueByObject(couponsObject, "deno"));
						String strType = null;
						// 1:代金券 2:礼品券
						if (ParamUtil.getIntegerValueByObject(couponsObject, "type") != null) {
							if (ParamUtil.getIntegerValueByObject(couponsObject, "type") == 1) {
								strType = SysDictionary.CUSTOMER_COUPON_TYPE_CASH;
							} else if (ParamUtil.getIntegerValueByObject(couponsObject, "type") == 2) {
								strType = SysDictionary.CUSTOMER_COUPON_TYPE_DISH;
							}
						}

						Integer is_user_shop = 0;
						if (couponsObject.containsKey("sids")) {
							JSONArray sids = couponsObject.getJSONArray("sids");
							if(!sids.isEmpty()){
								if (sids.toString().contains(shopId)) {
									is_user_shop = 1;
								} else {
									is_user_shop = 0;
								}
							}
						}
						couponsObj.put("is_user_shop", is_user_shop);
						couponsObj.put("type", strType);

						couponsObj.put("products", couponsObject.optJSONArray("products"));
						couponsObj.put("effective_time",
								ParamUtil.getStringValueForNullByObject(couponsObject, "effective_time"));
						couponsObj.put("failure_time",
								ParamUtil.getStringValueForNullByObject(couponsObject, "failure_time"));
						couponsObj.put("limitations", couponsObject.optJSONArray("limitations"));
						couponsObj.put("enable_amount",
								ParamUtil.getDoubleValueByObject(couponsObject, "enable_amount"));
						couponsObj.put("max_use", ParamUtil.getIntegerValueByObject(couponsObject, "max_use"));

                        //是否支持与其他券混合使用 (1:可以，0：不可以)
                        couponsObj.put("mix_use", AcewillUtil.getBoolean(couponsObject, "mix_use"));
						//是否支持与其他券混合使用 (0:可以,1:不可以,2:部分可以)
						couponsObj.put("mix_use_value", ParamUtil.getIntegerValueByObject(couponsObject, "mix_use_value"));
                        // 限制可以混用的券
                        couponsObj.put("limit_mix_coupon",couponsObject.optJSONArray("limit_mix_coupon"));


                        Integer is_diy_deno = AcewillUtil.getBoolean(couponsObject, "is_diy_deno");
						couponsObj.put("is_diy_deno", is_diy_deno);

                        couponsObj.put("sale_money", ParamUtil.getStringValueForNullByObject(couponsObject, "sale_money"));
                        couponsObj.put("limit_type",ParamUtil.getStringValueForNullByObject(couponsObject, "limit_type"));



                        // 菜品券扩展信息(用来保存代金券不可用菜品或类别编码)
                        couponsObj.put("products_ext",ParamUtil.getStringValueForNullByObject(couponsObject, "products_ext"));

						couponsList.add(couponsObj);
					}

					jsonObj.put("coupons", couponsList);
					dataList.add(jsonObj);
				}
			}
		}
		try {
			String posNum = ParamUtil.getStringValue(map, "pos_num", false, null);
			String optNum = ParamUtil.getStringValue(map, "opt_num", false, null);
			String idCard = ParamUtil.getStringValue(map, "card_code", false, null);
			String reportDate = ParamUtil.getStringValue(map, "report_date", false, null);
			String shiftId = ParamUtil.getStringValue(map, "shift_id", false, PosErrorCode.NOT_NULL_IPDZ);

			CustomerOperationLogs operationLogs = new CustomerOperationLogs();
			operationLogs.setOperation_id(optNum);
			operationLogs.setPos_num(posNum);
			operationLogs.setShift_id(shiftId);
			operationLogs.setTenancy_id(tenancyId);
			operationLogs.setStore_id(storeId);
			operationLogs.setCard_code(idCard);
			operationLogs.setReport_date(reportDate);
			operationLogs.setOperation_type(CustomerOperationEnum.FIND.getType());
			operationLogs.setOperation_type_name(CustomerOperationEnum.FIND.getValue());
			operationLogs.setTitle(CustomerOperationEnum.FIND.getTitle());
			operationLogs.setContent("查询会员:" + idCard + "," + (dataList.size()>0?"已查到":"未查到"));
			this.saveCustomerOperationLog(tenancyId,operationLogs);
		}catch (Exception e){
			e.printStackTrace();
		}


		resData.setData(dataList);
		logger.info("获取用户账户信息返回数据"+JsonUtil.DataToJson(resData));
		return resData;
	}

	
	/**
	 * 消费预览
	 */
	@Override
	public Data previewAcewillCustomerDeal(Data requestData) throws Exception {
		TransactionStatus status = null;
		try
		{
			status = getTransctionStatus(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
				
//			String requestUrl = getPequestUrl(AcewillRequestUtil.CUSTOMER_DEAL_PREVIEW_URL);
			String tenancyId = requestData.getTenancy_id();
			int storeId = requestData.getStore_id();
	
			JSONObject jsonO = JSONObject.fromObject(requestData.getData().get(0));
			String cardCode = ParamUtil.getStringValueForNullByObject(jsonO, "card_code");
			String thirdBillCode = ParamUtil.getStringValueForNullByObject(jsonO, "third_bill_code");
			String outTradeNo = ParamUtil.getStringValueForNullByObject(jsonO, "out_trade_no");
			if(Tools.isNullOrEmpty(outTradeNo))
			{
				outTradeNo = ParamUtil.getStringValueForNullByObject(jsonO, "third_bill_code");
			}
	
//			// 组织接口参数
//			JSONObject jsonParam = new JSONObject();
//			if (jsonO.containsKey("third_bill_code")) {
//				jsonParam.put("biz_id", jsonO.optString("third_bill_code")+"_"+ts);
//			}
//			jsonParam.put("cno", jsonO.optString("card_code"));
//			jsonParam.put("shop_id", getShopId(tenancyId, storeId));
//			jsonParam.put("cashier_id", -1);
//			jsonParam.put("consume_amount", changeY2F(ParamUtil.getDoubleValueByObject(jsonO, "consume_amount"))); // boh单位为元acewill为分
//			jsonParam.put("payment_amount", changeY2F(ParamUtil.getDoubleValueByObject(jsonO, "payment_amount"))); // boh单位为元acewill为分实际支付金额
//	
//			// 数字 1:现金 2:银行卡 3:店内微信 4:支付宝 6:线上微信 7:百度糯米 8:美团 9:点评 10:其他 11:支票
//			// 12:集团消费
//			jsonParam.put("payment_mode", jsonO.optString("payment_mode")); // 支付方式
//			jsonParam.put("sub_balance", changeY2F(ParamUtil.getDoubleValueByObject(jsonO, "sub_balance")));
//	
//			if (jsonO.containsKey("sub_credit")) {
//				jsonParam.put("sub_credit", ParamUtil.getIntegerValueByObject(jsonO, "sub_credit"));
//			}
//	
//			// 数字 -1:不送积分 ; 0:按照实际消费金额赠送;
//			// 其他数字：指定消费金额赠送，注意：根据查询用户的消费返积分规则charge_return_credit，不可以超过payment_amount实际消费金额
//			// 或者 不可以超过payment_amount+sub_balance 实际消费金额+使用储值金额
//			if (jsonO.containsKey("credit_amount")) {
//				Double creditAmount = ParamUtil.getDoubleValueByObject(jsonO, "credit_amount");
//				Integer creditAmountInt = -1;
//				if (0 <= creditAmount)
//				{
//					creditAmountInt = changeY2F(creditAmount);
//				}
//				jsonParam.put("credit_amount", creditAmountInt);
//			}
//			if (jsonO.containsKey("deno_coupon_ids")) {
//				jsonParam.put("deno_coupon_ids", jsonO.optJSONArray("deno_coupon_ids")); // 使用的代金券列表
//			}
//			if (jsonO.containsKey("gift_coupons_ids")) {
//				jsonParam.put("gift_coupons_ids", jsonO.optJSONArray("gift_coupons_ids")); // 使用的礼品券列表
//			}
//			JSONArray coupon_pay = null;
//			if (jsonO.containsKey("diy_gift_coupon_pay")) {
//				coupon_pay = jsonO.optJSONArray("diy_gift_coupon_pay");
//				for (int i = 0; i < coupon_pay.size(); i++) {
//					JSONObject p = coupon_pay.getJSONObject(i);
//					int deno = changeY2F(ParamUtil.getDoubleValueByObject(p, "deno"));
//					p.put("deno", deno);
//				}
//			}
//			jsonParam.put("diy_gift_coupon_pay", coupon_pay);
//	
//			JSONArray products = null;
//			if (jsonO.containsKey("products")) {
//				products = jsonO.optJSONArray("products");
//				for (int i = 0; i < products.size(); i++) {
//					JSONObject p = products.getJSONObject(i);
//					int price = changeY2F(ParamUtil.getDoubleValueByObject(p, "price"));
//					p.put("price", price);
//					Double num = ParamUtil.getDoubleValueByObject(p, "num");
//					p.put("num", num);
//					if (0 == (num % 1))
//					{
//						p.put("num", num.intValue());
//					}
//				}
//			}
//			jsonParam.put("products", products);
//	
//			if (jsonO.containsKey("activity_ids")) {
//				jsonParam.put("activity_ids", jsonO.optJSONArray("activity_ids"));
//			}
//	
//			if (jsonO.containsKey("count_num")) {
//				jsonParam.put("count_num", jsonO.optInt("count_num"));
//			}
//	
//			if (jsonO.containsKey("remark")) {
//				jsonParam.put("remark", jsonO.optString("remark"));
//			}
//			
//			//补充服务员信息
//			if (jsonO.containsKey("employees"))
//				jsonParam.put("employees", jsonO.optJSONArray("employees"));
//			if (jsonO.containsKey("table_id"))
//				jsonParam.put("table_id", jsonO.optString("table_id"));
			
			// 查询有效的消费记录
			PosCustomerOperateListEntity cusOperate = customerDao.queryCustomerOperateListByAcewillXF(tenancyId, storeId, cardCode, thirdBillCode, outTradeNo, SysDictionary.OPERAT_TYPE_XF);
			
			Data resData = null;
			if (null != cusOperate)
			{
				// 存在有效的消费记录
				resData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
				resData.setMsg("交易号重复");
				resData.setCode(Constant.CODE_INNER_EXCEPTION);
				resData.setSuccess(false);
			}
			else
			{// 不存在有效的消费记录
				// 记录操作记录
				cusOperate = insertPosCustomerOperateList(tenancyId, storeId, jsonO);
				
				// 请求acewill接口
				JSONObject jsonParam = this.getPreviewParamJson(tenancyId, storeId, jsonO);
				
				List<JSONObject> data = new ArrayList<JSONObject>();
				data.add(jsonParam);
				Data paramData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
				paramData.setData(data);
				paramData.setType(requestData.getType());
				paramData.setT(requestData.getT());
				paramData.setOper(requestData.getOper());
				
				logger.info("消费预览请求参数"+JsonUtil.DataToJson(paramData));
				try
				{
 					resData = this.commonPost(paramData, AcewillRequestUtil.CUSTOMER_DEAL_PREVIEW_URL);
				}
				catch (Exception e)
				{
					logger.info("消费预览请求失败"+e.getLocalizedMessage());
					resData = Data.get();
					resData.setMsg("消费预览请求失败");
					resData.setCode(Constant.CODE_INNER_EXCEPTION);
					resData.setSuccess(false);
				}
				
				if (Constant.CODE_SUCCESS == resData.getCode())
				{
					JSONObject resultData = JSONObject.fromObject(resData.getData().get(0));
					String tcid = resultData.optString("tcid");
					Integer verify_sms = AcewillUtil.getBoolean(resultData, "verify_sms");
					Integer verify_password = AcewillUtil.getBoolean(resultData, "verify_password");
					Double balance = 0d;
					if (ParamUtil.getIntegerValueByObject(resultData, "balance") != null && ParamUtil.getIntegerValueByObject(resultData, "balance") > 0)
					{
						balance = AcewillUtil.changeF2Y(resultData.optInt("balance"));
					}
					
					JSONObject jsonObject = new JSONObject();
					jsonObject.put("tcid", tcid);
					jsonObject.put("verify_sms", verify_sms);
					jsonObject.put("verify_password", verify_password);
					jsonObject.put("grade", resultData.optJSONArray("grade"));
					jsonObject.put("balance", balance); // 分转换成元
					jsonObject.put("credit", resultData.optInt("credit"));
					jsonObject.put("receive_credit", resultData.optInt("receive_credit"));
					jsonObject.put("coupons", resultData.optJSONArray("coupons"));
					
					List<JSONObject> dataList = new ArrayList<JSONObject>();
					dataList.add(jsonObject);
					resData.setData(dataList);
					
					// 修改operate_state
					String extend_param = cusOperate.getExtend_param();
					JSONObject extendJson = JSONObject.fromObject(extend_param);
					extendJson.put("grade", resultData.optJSONArray("grade"));
					extendJson.put("balance", balance); // 分转换成元
					extendJson.put("credit", resultData.optInt("credit"));
					extendJson.put("receive_credit", resultData.optInt("receive_credit"));
//					extendJson.put("coupons", resultData.optJSONArray("coupons"));

					cusOperate.setBill_code(tcid);
					cusOperate.setExtend_param(extendJson.toString());
					cusOperate.setOperate_state(SysDictionary.CUSTOMER_OPERATE_STATE_PROCESS);
					cusOperate.setCancel_state(SysDictionary.CUSTOMER_OPERATE_CANCEL_STATE_N);
					cusOperate.setRequest_status(SysDictionary.REQUEST_STATUS_COMPLETE);

					customerDao.updatePosCustomerOperateList(tenancyId, storeId, cusOperate);
				}
				else if (Constant.CODE_CONN_EXCEPTION == resData.getCode() || Constant.CODE_INNER_EXCEPTION == resData.getCode())
				{
					// 调用请求冲正接口
					cusOperate.setOperate_state(SysDictionary.CUSTOMER_OPERATE_STATE_FAIL);
					cusOperate.setRequest_status(SysDictionary.REQUEST_STATUS_FAILURE);
					this.rollbackAcewillCustomerDeal(tenancyId, storeId, cusOperate);
				}
				else
				{
					cusOperate.setOperate_state(SysDictionary.CUSTOMER_OPERATE_STATE_FAIL);// 失败
					cusOperate.setCancel_state(SysDictionary.CUSTOMER_OPERATE_CANCEL_STATE_N);
					cusOperate.setRequest_status(SysDictionary.REQUEST_STATUS_FAILURE);
					customerDao.updatePosCustomerOperateList(tenancyId, storeId, cusOperate);
				}
			}
			logger.info("消费预览返回数据"+JsonUtil.DataToJson(resData));
	
			transactionManager.commit(status);

			return resData;
		}
		catch (Exception e)
		{
			transactionManager.rollback(status);
			throw e;
		}
	}

	/** 获取交易预览请求参数
	 * @param tenancyId
	 * @param storeId
	 * @param jsonO
	 * @return
	 * @throws Exception
	 */
	private JSONObject getPreviewParamJson(String tenancyId, int storeId, JSONObject jsonO) throws Exception
	{
		String optNum = ParamUtil.getStringValueForNullByObject(jsonO, "opt_num");
		
		// 组织接口参数
		JSONObject jsonParam = new JSONObject();
		jsonParam.put("biz_id", ParamUtil.getStringValueForNullByObject(jsonO, "out_trade_no"));
		jsonParam.put("cno", ParamUtil.getStringValueForNullByObject(jsonO, "card_code"));
		jsonParam.put("shop_id", getShopId(tenancyId, storeId));
		jsonParam.put("cashier_id", this.getCashierId(tenancyId, storeId, optNum));
		jsonParam.put("consume_amount", AcewillUtil.changeY2F(ParamUtil.getDoubleValueByObject(jsonO, "consume_amount"))); // boh单位为元acewill为分
		jsonParam.put("payment_amount", AcewillUtil.changeY2F(ParamUtil.getDoubleValueByObject(jsonO, "payment_amount"))); // boh单位为元acewill为分实际支付金额

		// 数字 1:现金 2:银行卡 3:店内微信 4:支付宝 6:线上微信 7:百度糯米 8:美团 9:点评 10:其他 11:支票
		// 12:集团消费
		jsonParam.put("payment_mode", ParamUtil.getStringValueByObject(jsonO, "payment_mode")); // 支付方式
		jsonParam.put("sub_balance", AcewillUtil.changeY2F(ParamUtil.getDoubleValueByObject(jsonO, "sub_balance")));
		if (jsonO.containsKey("sub_credit_amount"))
		{
			jsonParam.put("sub_credit", ParamUtil.getIntegerValueByObject(jsonO, "sub_credit_amount"));
		}

		// 数字 -1:不送积分 ; 0:按照实际消费金额赠送;
		// 其他数字：指定消费金额赠送，注意：根据查询用户的消费返积分规则charge_return_credit，不可以超过payment_amount实际消费金额
		// 或者 不可以超过payment_amount+sub_balance 实际消费金额+使用储值金额
		Integer creditAmountInt = -1;
		if (jsonO.containsKey("credit_amount"))
		{
			Double creditAmount = ParamUtil.getDoubleValueByObject(jsonO, "credit_amount");

			if (0 <= creditAmount)
			{
				creditAmountInt = AcewillUtil.changeY2F(creditAmount);
			}
			jsonParam.put("credit_amount", creditAmountInt);
		}
		if (jsonO.containsKey("activity_amount"))
		{
			Double activityamount = ParamUtil.getDoubleValueByObject(jsonO, "activity_amount");
			Integer activityamountint = -1;
			if (0 <= activityamount)
			{
				activityamountint = AcewillUtil.changeY2F(activityamount);
			}
			jsonParam.put("activity_amount", activityamountint);
		}
		else {
			jsonParam.put("activity_amount", creditAmountInt);
		}
		if (jsonO.containsKey("deno_coupon_ids"))
		{
			jsonParam.put("deno_coupon_ids", jsonO.optJSONArray("deno_coupon_ids")); // 使用的代金券列表
		}
		if (jsonO.containsKey("gift_coupons_ids"))
		{
			jsonParam.put("gift_coupons_ids", jsonO.optJSONArray("gift_coupons_ids")); // 使用的礼品券列表
		}
		JSONArray coupon_pay = null;
		if (jsonO.containsKey("diy_gift_coupon_pay"))
		{
			coupon_pay = jsonO.optJSONArray("diy_gift_coupon_pay");
			for (int i = 0; i < coupon_pay.size(); i++)
			{
				JSONObject p = coupon_pay.getJSONObject(i);
				int deno = AcewillUtil.changeY2F(ParamUtil.getDoubleValueByObject(p, "deno"));
				p.put("deno", deno);
			}
		}
		jsonParam.put("diy_gift_coupon_pay", coupon_pay);

		JSONArray products = null;
		if (jsonO.containsKey("products"))
		{
			products = jsonO.optJSONArray("products");
			for (int i = 0; i < products.size(); i++)
			{
				JSONObject p = products.getJSONObject(i);
				int price = AcewillUtil.changeY2F(ParamUtil.getDoubleValueByObject(p, "price"));
				p.put("price", price);
				Double num = ParamUtil.getDoubleValueByObject(p, "num");
				p.put("num", num);
				if (0 == (num % 1))
				{
					p.put("num", num.intValue());
				}
			}
		}
		jsonParam.put("products", products);

		if (jsonO.containsKey("activity_ids"))
		{
			jsonParam.put("activity_ids", jsonO.optJSONArray("activity_ids"));
		}

		if (jsonO.containsKey("count_num"))
		{
			jsonParam.put("count_num", jsonO.optInt("count_num"));
		}

		if (jsonO.containsKey("remark"))
		{
			jsonParam.put("remark", jsonO.optString("remark"));
		}

		// 补充服务员信息
		if (jsonO.containsKey("employees"))
		{
			jsonParam.put("employees", jsonO.optJSONArray("employees"));
		}
		if (jsonO.containsKey("table_id"))
		{
			jsonParam.put("table_id", jsonO.optString("table_id"));
		}
		if (jsonO.containsKey("from"))
		{
			// 预消费时加参数from 值为16时不发送交易验证码
			jsonParam.put("from", jsonO.optString("from"));
		}
		return jsonParam;
	}
	
	
	private PosCustomerOperateListEntity insertPosCustomerOperateList(String tenancyId,int storeId,JSONObject jsonO) {
//		String tenancyId = requestData.getTenancy_id();
//		int storeId = requestData.getStore_id();
//		JSONObject jsonO = JSONObject.fromObject(requestData.getData().get(0));
		Timestamp currentTime = DateUtil.currentTimestamp();
		String cardCode = ParamUtil.getStringValueForNullByObject(jsonO, "card_code");
		String thirdBillCode = ParamUtil.getStringValueForNullByObject(jsonO, "third_bill_code");
		String outTradeNo = ParamUtil.getStringValueForNullByObject(jsonO, "out_trade_no");
		// 记录pos_customer_operate_list 数据库表不存在字段 放入extends
		PosCustomerOperateListEntity customerOperate = new PosCustomerOperateListEntity();
		customerOperate.setShift_id(ParamUtil.getIntegerValueByObject(jsonO, "shift_id"));
		customerOperate.setPos_num(ParamUtil.getStringValueForNullByObject(jsonO, "pos_num"));
		customerOperate.setOperator_id(ParamUtil.getIntegerValueByObject(jsonO, "opt_num"));
		customerOperate.setChanel(ParamUtil.getStringValueForNullByObject(jsonO, "chanel"));
		customerOperate.setThird_bill_code(thirdBillCode);
		customerOperate.setThird_bill_code_timestamp(outTradeNo);
		customerOperate.setBatch_num(ParamUtil.getStringValueForNullByObject(jsonO, "batch_num"));
		customerOperate.setCard_code(cardCode);
		customerOperate.setTenancy_id(tenancyId);
		customerOperate.setStore_id(storeId);
		String reportDateStr = ParamUtil.getStringValueByObject(jsonO, "report_date", true,
				PosErrorCode.NOT_NULL_REPORT_DATE);// 业务日期
		customerOperate.setBusiness_date(DateUtil.parseDate(reportDateStr));
		customerOperate.setOperate_time(currentTime);
		customerOperate.setService_type(SysDictionary.SERVICE_TYPE_CONSUME); // pos点餐消费
		customerOperate.setOperat_type(SysDictionary.OPERAT_TYPE_XF); // 卡消费
		customerOperate.setTrade_amount(ParamUtil.getDoubleValueByObject(jsonO, "sub_balance"));
		customerOperate.setTrade_credit(ParamUtil.getDoubleValueByObject(jsonO, "sub_credit"));
		customerOperate.setPayment_state(SysDictionary.THIRD_PAY_STATUS_SUCCESS);
		customerOperate.setOperate_state(SysDictionary.CUSTOMER_OPERATE_STATE_WAIT);// 待处理
		customerOperate.setCancel_state(SysDictionary.CUSTOMER_OPERATE_CANCEL_STATE_N);
		customerOperate.setRequest_status(SysDictionary.REQUEST_STATUS_ING);
		customerOperate.setLast_query_time(currentTime);
		customerOperate.setQuery_count(0);
		customerOperate.setIs_invoice(ParamUtil.getStringValueForNullByObject(jsonO, "is_invoice"));
		customerOperate.setMobil(ParamUtil.getStringValueForNullByObject(jsonO, "mobil"));
		customerOperate.setCustomer_name(ParamUtil.getStringValueForNullByObject(jsonO, "customer_name"));
		
		JSONObject extJson = new JSONObject();
		extJson.put("payment_mode", ParamUtil.getStringValueForNullByObject(jsonO, "payment_mode"));
		extJson.put("credit_amount", ParamUtil.getStringValueForNullByObject(jsonO, "credit_amount"));
//		extJson.put("deno_coupon_ids", jsonO.optJSONArray("deno_coupon_ids"));
//		extJson.put("gift_coupons_ids", jsonO.optJSONArray("gift_coupons_ids"));
//		extJson.put("diy_gift_coupon_pay", jsonO.optJSONArray("diy_gift_coupon_pay"));
//		extJson.put("products", jsonO.optJSONArray("products"));
		extJson.put("activity_ids", jsonO.optJSONArray("activity_ids"));
		extJson.put("count_num", ParamUtil.getIntegerValueByObject(jsonO, "count_num"));
		extJson.put("remark", ParamUtil.getStringValueForNullByObject(jsonO, "remark"));
		extJson.put("isprint_bill", ParamUtil.getStringValueForNullByObject(jsonO, "isprint_bill"));
		extJson.put("level_name",ParamUtil.getStringValueForNullByObject(jsonO, "level_name"));
		extJson.put("is_commit","0");
		customerOperate.setExtend_param(extJson.toString());
		
		PosCustomerOperateListEntity cusOperate = null;
		try {
			customerDao.insertPosCustomerOperateList(tenancyId, storeId, customerOperate);
			
			// 修改operate_state
			cusOperate = customerDao.queryCustomerOperateListByAcewillXF(tenancyId, storeId, cardCode, thirdBillCode,outTradeNo, SysDictionary.OPERAT_TYPE_XF);
		} catch (Exception e) {
			logger.info("记录会员操作记录失败");
			e.printStackTrace();
		}
		
		return cusOperate;
	}

	
	private TransactionStatus getTransctionStatus(int transactionDefinition) {
		DefaultTransactionDefinition def = new DefaultTransactionDefinition();
		def.setPropagationBehavior(transactionDefinition);
		TransactionStatus status = transactionManager.getTransaction(def);
		return status;
	}

	/**
	 * 交易提交
	 * 
	 * @param requestData
	 * @return
	 * @throws Exception
	 */
	@Override
	public Data commitAcewillCustomerDeal(Data requestData) throws Exception {
		TransactionStatus status = null;
		try
		{
			status = getTransctionStatus(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
			
//			String requestUrl = getPequestUrl(AcewillRequestUtil.CUSTOMER_DEAL_COMMIT_URL);
			String tenancyId = requestData.getTenancy_id();
			int storeId = requestData.getStore_id();
			Data resData = new Data();
			JSONObject data = null;
			try {
				data = JSONObject.fromObject(requestData.getData().get(0));
			} catch (Exception e) {
				e.printStackTrace();
				logger.info("交易提交:请求参数转换json格式失败");
				resData.setCode(Constant.CODE_INNER_EXCEPTION);
				resData.setMsg("交易提交:请求参数转换json格式失败");
				resData.setSuccess(false);
				return resData;
			}
			String outTradeNo = ParamUtil.getStringValueForNullByObject(data, "out_trade_no");
			String thirdBillCode = ParamUtil.getStringValueForNullByObject(data, "third_bill_code");
			String verify_code = ParamUtil.getStringValueForNullByObject(data, "verify_code");

			// 查询有效的消费记录
			PosCustomerOperateListEntity customerOperate = customerDao.queryCustomerOperateListByAcewillXF(tenancyId,storeId, null, thirdBillCode, outTradeNo, SysDictionary.OPERAT_TYPE_XF);
			
			if (customerOperate != null)
			{
				JSONObject extendJson = JSONObject.fromObject(customerOperate.getExtend_param());
//				extendJson.put("is_commit","1");
//				customerOperate.setExtend_param(extendJson.toString());
				
				JSONObject param = new JSONObject();
				param.put("biz_id", customerOperate.getThird_bill_code_timestamp());
				param.put("verify_code", verify_code);
				
				ArrayList<JSONObject> dataList = new ArrayList<JSONObject>();
				dataList.add(param);
				
				requestData.setData(dataList);
				
				try
				{
					logger.info("交易提交请求参数" + JsonUtil.DataToJson(requestData));
					resData = this.commonPost(requestData, AcewillRequestUtil.CUSTOMER_DEAL_COMMIT_URL);
				}
				catch (Exception e)
				{
					e.printStackTrace();
				}

				if (Constant.CODE_SUCCESS == resData.getCode())
				{
					JSONObject jsonData = JSONObject.fromObject(resData.getData().get(0));

					// 使用储值支付金额(单位:分)
					Double stored_pay = 0d;
					if (jsonData.containsKey("stored_pay") && ParamUtil.getIntegerValueByObject(jsonData, "stored_pay") > 0)
					{
						stored_pay = AcewillUtil.changeF2Y(jsonData.optInt("stored_pay"));
					}

					// 使用实际储值支付金额(单位:分)
					Double stored_sale_pay = 0d;
					if (jsonData.containsKey("stored_sale_pay") && ParamUtil.getIntegerValueByObject(jsonData, "stored_sale_pay") > 0)
					{
						stored_sale_pay = AcewillUtil.changeF2Y(jsonData.optInt("stored_sale_pay"));
					}
					
					jsonData.put("stored_pay", stored_pay);
					jsonData.put("stored_sale_pay", stored_sale_pay);
					jsonData.put("card_code", customerOperate.getCard_code());// 数据库获取会员卡号
					jsonData.put("sub_balance", customerOperate.getTrade_amount());// 数据库获取会消费使用储值金额
					jsonData.put("sub_credit", customerOperate.getTrade_credit());// 数据库获取会消费使用积分抵现

					jsonData.put("mobil", customerOperate.getMobil());// 数据库获取手机号
					jsonData.put("customer_name", customerOperate.getCustomer_name());// 数据库获取客户名称

					jsonData.put("level_name", ParamUtil.getStringValueForNullByObject(extendJson, "level_name"));
					jsonData.put("grade", extendJson.optJSONArray("grade"));
					jsonData.put("balance", extendJson.optDouble("balance"));
					jsonData.put("credit", extendJson.optDouble("credit"));
					jsonData.put("receive_credit", extendJson.optDouble("receive_credit"));

					List<JSONObject> list = new ArrayList<JSONObject>();
					list.add(jsonData);
					resData.setData(list);

					extendJson.put("is_commit","1");
					extendJson.put("stored_pay", stored_pay);
					extendJson.put("stored_sale_pay", stored_sale_pay);
					customerOperate.setExtend_param(extendJson.toString());
					customerOperate.setOperate_state(SysDictionary.CUSTOMER_OPERATE_STATE_SUCCESS);// 成功
					customerOperate.setCancel_state(SysDictionary.CUSTOMER_OPERATE_CANCEL_STATE_N);// 未撤销
					customerOperate.setRequest_status(SysDictionary.REQUEST_STATUS_COMPLETE);// 请求成功
					customerDao.updatePosCustomerOperateList(tenancyId, storeId, customerOperate);
					// 新增crm_card_trading_list表记录
					insertCrmCardTradingList(customerOperate);
				}
				else if (AcewillRequestUtil.SMS_NULL_ERROR_CODE == resData.getCode() || AcewillRequestUtil.SMS_WRONG_ERROR_CODE == resData.getCode() || AcewillRequestUtil.SMS_OVERDUE_ERROR_CODE == resData.getCode() || AcewillRequestUtil.PASSWORD_LENGTH_ERROR_CODE == resData.getCode()
						|| AcewillRequestUtil.PASSWORD_WRONG_ERROR_CODE == resData.getCode()|| AcewillRequestUtil.PASSWORD_DEFAULT_ERROR_CODE == resData.getCode())
				{
					customerOperate.setOperate_state(SysDictionary.CUSTOMER_OPERATE_STATE_PROCESS);// 成功
					customerOperate.setCancel_state(SysDictionary.CUSTOMER_OPERATE_CANCEL_STATE_N);// 未撤销
					customerOperate.setRequest_status(SysDictionary.REQUEST_STATUS_FAILURE);// 请求成功
					customerOperate.setRequest_code(String.valueOf(resData.getCode()));
					customerOperate.setRequest_msg(resData.getMsg());
					customerDao.updatePosCustomerOperateList(tenancyId, storeId, customerOperate);
				}
				else if(Constant.CODE_CONN_EXCEPTION == resData.getCode())
				{
					extendJson.put("is_commit","1");
					customerOperate.setExtend_param(extendJson.toString());
					
					customerOperate.setOperate_state(SysDictionary.CUSTOMER_OPERATE_STATE_PROCESS);// 成功
					customerOperate.setCancel_state(SysDictionary.CUSTOMER_OPERATE_CANCEL_STATE_N);// 未撤销
					customerOperate.setRequest_status(SysDictionary.REQUEST_STATUS_FAILURE);// 请求成功
					customerOperate.setRequest_code(String.valueOf(resData.getCode()));
					customerOperate.setRequest_msg(resData.getMsg());
					customerDao.updatePosCustomerOperateList(tenancyId, storeId, customerOperate);
				}
				else
				{
					customerOperate.setOperate_state(SysDictionary.CUSTOMER_OPERATE_STATE_FAIL);// 成功
					customerOperate.setCancel_state(SysDictionary.CUSTOMER_OPERATE_CANCEL_STATE_N);// 未撤销
					customerOperate.setRequest_status(SysDictionary.REQUEST_STATUS_FAILURE);// 请求成功
					customerOperate.setRequest_code(String.valueOf(resData.getCode()));
					customerOperate.setRequest_msg(resData.getMsg());
					customerDao.updatePosCustomerOperateList(tenancyId, storeId, customerOperate);
				}
			} else {
				logger.info("消费提交：交易记录不存在");
				resData.setCode(Constant.CODE_INNER_EXCEPTION);
				resData.setMsg("消费提交：交易记录不存在");
				resData.setSuccess(false);
			}
			logger.info("交易提交返回数据"+JsonUtil.DataToJson(resData));
			
			transactionManager.commit(status);
			
			return resData;
		}
		catch (Exception e)
		{
			transactionManager.rollback(status);
			throw e;
		}
	}
	
	/** 交易冲正
	 * @param tenancyId
	 * @param storeId
	 * @param customerOperate
	 * @return
	 * @throws Exception
	 */
	public Data rollbackAcewillCustomerDeal(String tenancyId,Integer storeId,PosCustomerOperateListEntity customerOperate) throws Exception
	{
//		String requestUrl = getPequestUrl(AcewillRequestUtil.CUSTOMER_DEAL_ROLLBACK_URL);
		Data resData = null;
		if (null != customerOperate)
		{
			JSONObject param = new JSONObject();
			param.put("biz_id", customerOperate.getThird_bill_code_timestamp());
			ArrayList<JSONObject> dataList = new ArrayList<JSONObject>();
			dataList.add(param);
			Data requestData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
			requestData.setData(dataList);

			try
			{
				logger.info("交易冲正请求参数" + JsonUtil.DataToJson(requestData));
				resData = this.commonPost(requestData, AcewillRequestUtil.CUSTOMER_DEAL_ROLLBACK_URL);
			}
			catch (Exception e)
			{
				logger.info("交易冲正请求失败" + e.getMessage());
				resData = Data.get();
				resData.setCode(Constant.CODE_INNER_EXCEPTION);
				resData.setMsg("交易冲正请求失败");
				resData.setSuccess(false);
			}

			customerOperate.setOperate_state(SysDictionary.CUSTOMER_OPERATE_STATE_SUCCESS);
			customerOperate.setCancel_state(SysDictionary.CUSTOMER_OPERATE_CANCEL_STATE_Y);
			customerDao.updatePosCustomerOperateList(tenancyId, storeId, customerOperate);

		}
		else
		{
			resData = Data.get();
			resData.setCode(Constant.CODE_PARAM_FAILURE);
			resData.setMsg("交易冲正记录为空");
			resData.setSuccess(false);
		}

		return resData;
	}
	
	public Data cancelAcewillCustomerDeal(String tenancyId, Integer storeId, PosCustomerOperateListEntity customerOperate, String actionType) throws Exception
	{
//		String requestUrl = getPequestUrl(AcewillRequestUtil.CUSTOMER_DEAL_CANCEL_URL);
		Data resData = null;
		if (null != customerOperate)
		{
			JSONObject param = new JSONObject();
			param.put("biz_id", customerOperate.getThird_bill_code_timestamp());
			param.put("cashier_id", this.getCashierId(tenancyId, storeId, String.valueOf(customerOperate.getOperator_id())));

			ArrayList<JSONObject> dataList = new ArrayList<JSONObject>();
			dataList.add(param);

			Data requestData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
			requestData.setData(dataList);

			try
			{
				logger.info("交易撤销请求参数" + JsonUtil.DataToJson(requestData));
				resData = this.commonPost(requestData, AcewillRequestUtil.CUSTOMER_DEAL_CANCEL_URL);
			}
			catch (Exception e)
			{
				logger.info("交易撤销请求失败" + e.getMessage());
				resData = Data.get();
				resData.setCode(Constant.CODE_INNER_EXCEPTION);
				resData.setMsg("交易撤销请求失败");
				resData.setSuccess(false);
			}

		}
		else
		{
			resData = Data.get();
			resData.setCode(Constant.CODE_PARAM_FAILURE);
			resData.setMsg("交易撤销记录为空");
			resData.setSuccess(false);
		}

		return resData;
	}
	
	
	/**
	 * 交易撤销
	 */
	@Override
	public Data cancelAcewillCustomerDeal(String tenancyId, Integer storeId, JSONObject paramJson, String actionType) throws Exception
	{
		Date businessDate = ParamUtil.getDateValueByObject(paramJson, "business_date");
		Integer shiftId = ParamUtil.getIntegerValueByObject(paramJson, "shift_id");
		Integer operatorId = ParamUtil.getIntegerValueByObject(paramJson, "operator_id");
		String posNum = ParamUtil.getStringValueByObject(paramJson, "pos_num");
		String chanel = ParamUtil.getStringValueByObject(paramJson, "chanel");
//		String operatType = ParamUtil.getStringValueByObject(paramJson, "operat_type");
		String serviceType = ParamUtil.getStringValueByObject(paramJson, "service_type");
		String cardCode = ParamUtil.getStringValueByObject(paramJson, "card_code");
		Double tradeAmount = ParamUtil.getDoubleValueByObject(paramJson, "trade_amount");
		Double tradeCredit = ParamUtil.getDoubleValueByObject(paramJson, "trade_credit");
		Double totalAmount=ParamUtil.getDoubleValueByObject(paramJson, "total_amount");
		String thirdBillCode = ParamUtil.getStringValueForNullByObject(paramJson, "third_bill_code");
		String billCode = ParamUtil.getStringValueForNullByObject(paramJson, "bill_code");
		
		Timestamp currentTime = DateUtil.currentTimestamp();

		//查询是否有撤销记录
		PosCustomerOperateListEntity customerOperateFXF = customerDao.queryAcewillCustomerDeal(tenancyId, storeId, null, thirdBillCode, billCode, null, null, SysDictionary.OPERAT_TYPE_FXF);
		boolean isInsert = false;
		boolean isCommit = true;
		if (null == customerOperateFXF)
		{
			isInsert = true;
			PosCustomerOperateListEntity customerOperateXF = customerDao.queryAcewillCustomerDeal(tenancyId, storeId, null, thirdBillCode, billCode, null, null, SysDictionary.OPERAT_TYPE_XF);

			if (null != customerOperateXF)
			{
				JSONObject extendJson = JSONObject.fromObject(customerOperateXF.getExtend_param());
				isCommit = ("1".equals(extendJson.optString("is_commit")));
				
				// 修改消费记录
//				customerOperateXF.setOperate_state(SysDictionary.CUSTOMER_OPERATE_STATE_SUCCESS);// 成功
//				customerOperateXF.setRequest_status(SysDictionary.REQUEST_STATUS_COMPLETE);
				customerOperateXF.setCancel_state(SysDictionary.CUSTOMER_OPERATE_CANCEL_STATE_Y);
				customerDao.updatePosCustomerOperateList(tenancyId, storeId, customerOperateXF);

				// 记录操作撤销记录
				customerOperateFXF = customerOperateXF;
				customerOperateFXF.setLast_query_time(currentTime);
				customerOperateFXF.setOperate_time(currentTime);
				customerOperateFXF.setQuery_count(0);
				customerOperateFXF.setService_type(serviceType); // pos点餐消费
				customerOperateFXF.setOperat_type(SysDictionary.OPERAT_TYPE_FXF);// 反消费-撤销
				customerOperateFXF.setOperate_state(SysDictionary.CUSTOMER_OPERATE_STATE_WAIT);// 成功
				customerOperateFXF.setRequest_status(SysDictionary.REQUEST_STATUS_COMPLETE);
				customerOperateFXF.setCancel_state(SysDictionary.CUSTOMER_OPERATE_CANCEL_STATE_N);
				customerOperateFXF.setPayment_state(SysDictionary.THIRD_PAY_STATUS_SUCCESS);
			}
			else
			{
				JSONObject viewJson = null;
				try
				{
					// 不存在有效的消费记录,请求查询接口
					JSONObject paramjson = new JSONObject();
					paramjson.put("bill_code", billCode);
					Data viewData = this.getAcewillCustomerView(tenancyId, storeId, paramjson);
					List<?> viewList = viewData.getData();
					if (Constant.CODE_SUCCESS == viewData.getCode() && null != viewList && viewList.size() > 0)
					{
						viewJson = JSONObject.fromObject(viewList.get(0));
					}
				}
				catch (Exception e)
				{
					e.printStackTrace();
				}

				// 新增撤销操作记录
				customerOperateFXF = new PosCustomerOperateListEntity();
				customerOperateFXF.setTenancy_id(tenancyId);
				customerOperateFXF.setShift_id(storeId);
				customerOperateFXF.setBusiness_date(businessDate);
				customerOperateFXF.setShift_id(shiftId);
				customerOperateFXF.setOperator_id(operatorId);
				customerOperateFXF.setPos_num(posNum);
				customerOperateFXF.setChanel(chanel);
				customerOperateFXF.setLast_query_time(currentTime);
				customerOperateFXF.setOperate_time(currentTime);
				customerOperateFXF.setQuery_count(0);
				customerOperateFXF.setService_type(serviceType); // pos点餐消费
				customerOperateFXF.setOperat_type(SysDictionary.OPERAT_TYPE_FXF);// 反消费-撤销
				customerOperateFXF.setOperate_state(SysDictionary.CUSTOMER_OPERATE_STATE_WAIT);// 成功
				customerOperateFXF.setRequest_status(SysDictionary.REQUEST_STATUS_COMPLETE);
				customerOperateFXF.setCancel_state(SysDictionary.CUSTOMER_OPERATE_CANCEL_STATE_N);
				customerOperateFXF.setPayment_state(SysDictionary.THIRD_PAY_STATUS_SUCCESS);
				
				if (Tools.hv(viewJson))
				{
					isCommit = ("2".equals(viewJson.optString("status")));

					customerOperateFXF.setThird_bill_code(thirdBillCode);
					customerOperateFXF.setThird_bill_code_timestamp(ParamUtil.getStringValueForNullByObject(viewJson, "third_bill_code"));
					customerOperateFXF.setCard_code(ParamUtil.getStringValueForNullByObject(viewJson, "card_code"));
					customerOperateFXF.setBill_code(ParamUtil.getStringValueForNullByObject(viewJson, "tcid"));
					customerOperateFXF.setTrade_amount(ParamUtil.getDoubleValueByObject(viewJson, "stored_pay"));
					customerOperateFXF.setTrade_credit(ParamUtil.getDoubleValueByObject(viewJson, "credit_num"));
					customerOperateFXF.setOperator_id(ParamUtil.getIntegerValueByObject(viewJson, "cashier_id"));
				}
				else
				{
					customerOperateFXF.setThird_bill_code(thirdBillCode);
					customerOperateFXF.setThird_bill_code_timestamp(thirdBillCode);
					customerOperateFXF.setCard_code(cardCode);
					customerOperateFXF.setBill_code(billCode);
					customerOperateFXF.setTrade_amount(tradeAmount);
					customerOperateFXF.setTrade_credit(tradeCredit);
					customerOperateFXF.setOperator_id(operatorId);
				}
			}
		}
		else 
		{
			JSONObject extendJson = JSONObject.fromObject(customerOperateFXF.getExtend_param());
			isCommit = ("1".equals(extendJson.optString("is_commit")));
		}

		Data resData = null;
		if (customerOperateFXF.getOperate_state() != null && SysDictionary.CUSTOMER_OPERATE_STATE_SUCCESS.equals(customerOperateFXF.getOperate_state()))
		{
			JSONObject dataJson = new JSONObject();
			dataJson.put("deal_id", customerOperateFXF.getBill_code());
			
			List<JSONObject> dataLit = new ArrayList<JSONObject>();
			dataLit.add(dataJson);
			
			resData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
			resData.setData(dataLit);
			resData.setCode(Constant.CODE_SUCCESS);
			resData.setSuccess(true);
			
		}
		else
		{
//			if (Tools.isNullOrEmpty(bizId))
//			{
//				bizId = customerOperateFXF.getThird_bill_code_timestamp();
//			}

			if (false == isCommit)
			{
				resData = this.rollbackAcewillCustomerDeal(tenancyId, storeId, customerOperateFXF);
				
				if (isInsert)
				{
					customerDao.insertPosCustomerOperateList(tenancyId, storeId, customerOperateFXF);
				}
				
				resData.setCode(Constant.CODE_SUCCESS);
			}
			else
			{
				resData = this.cancelAcewillCustomerDeal(tenancyId, storeId, customerOperateFXF, actionType);

				if (Constant.CODE_SUCCESS == resData.getCode())
				{
					customerOperateFXF.setOperat_type(SysDictionary.OPERAT_TYPE_FXF);// 反消费-撤销
					customerOperateFXF.setOperate_state(SysDictionary.CUSTOMER_OPERATE_STATE_SUCCESS);// 成功
					customerOperateFXF.setRequest_status(SysDictionary.REQUEST_STATUS_COMPLETE);

					// 新增crm_card_trading_list表记录
					insertCrmCardTradingList(customerOperateFXF);
					
					this.memberCancelConsumePrint(tenancyId, storeId, posNum, String.valueOf(operatorId), cardCode, billCode, tradeAmount);
				}
				else
				{
					customerOperateFXF.setOperat_type(SysDictionary.OPERAT_TYPE_FXF);// 反消费-撤销
					customerOperateFXF.setOperate_state(SysDictionary.CUSTOMER_OPERATE_STATE_FAIL);// 成功
					customerOperateFXF.setRequest_status(SysDictionary.REQUEST_STATUS_FAILURE);
					customerOperateFXF.setRequest_code(String.valueOf(resData.getCode()));
					customerOperateFXF.setRequest_msg(resData.getMsg());

					// 退款失败,记录退款异常
					String paymentClass = SysDictionary.PAYMENT_CLASS_WLIFE_BALANCE;
					Integer paymentId = 0;
					JSONObject paymentWayJson = customerDao.getPaymentWayByPaymentClass(tenancyId, storeId, paymentClass);
					if (null != paymentWayJson && !paymentWayJson.isEmpty())
					{
						paymentId = ParamUtil.getIntegerValueByObject(paymentWayJson, "payment_id");
					}
					String bizId = customerOperateFXF.getThird_bill_code_timestamp();
					
					PosThirdPaymentRefundEntity paymentRefund = new PosThirdPaymentRefundEntity(tenancyId, storeId, businessDate, shiftId, posNum, String.valueOf(operatorId), chanel, serviceType, thirdBillCode, bizId, billCode, paymentClass, paymentId, totalAmount, Math.abs(tradeAmount), currentTime,
							currentTime, String.valueOf(operatorId), String.valueOf(resData.getCode()), resData.getMsg(), actionType, SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL, SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL);

					thirdPaymentRefundService.thirdPaymentRefundFailure(tenancyId, storeId, paymentRefund);
				}

				// 记录操作记录
				if (isInsert)
				{
					customerDao.insertPosCustomerOperateList(tenancyId, storeId, customerOperateFXF);
				}
				else
				{
					customerDao.updatePosCustomerOperateList(tenancyId, storeId, customerOperateFXF);
				}
			}
		}

		logger.info("交易撤销返回数据" + JsonUtil.DataToJson(resData));
		return resData;
	}

	/** 撤销消费打印小票
	 * @param tenancyId
	 * @param storeId
	 * @param posNum
	 * @param optNum
	 * @param cardCode
	 * @param billCode
	 * @param payAmount
	 */
	private void memberCancelConsumePrint(String tenancyId, Integer storeId, String posNum, String optNum, String cardCode, String billCode, Double payAmount)
	{
		try
		{
			String cardClassName = "";
			String levelName = "";
			String memberName = "";
			String mobil = "";
			Double usefulCredit = 0d;
			Double balance = 0d;

			Data resultData = this.getAcewillCustomerUserInfo(tenancyId, storeId, cardCode);
			if (Constant.CODE_SUCCESS == resultData.getCode())
			{
				JSONObject userInfo = JSONObject.fromObject(resultData.getData().get(0));
				cardClassName = userInfo.optString("type_name");
				levelName = userInfo.optString("level_name");
				memberName = userInfo.optString("name");
				mobil = userInfo.optString("mobil");
				usefulCredit = userInfo.optDouble("credit");
				balance = userInfo.optDouble("balance");
			}

			String operator = customerDao.getEmpNameById(optNum, tenancyId, storeId);

			JSONObject printJson = new JSONObject();
			printJson.put("pos_num", "posNum");

			printJson.put("bill_code", billCode);
			printJson.put("card_code", cardCode);
			printJson.put("card_class_name", cardClassName);
			printJson.put("level_name", levelName);
			printJson.put("name", memberName);
			printJson.put("mobil", mobil);
			printJson.put("credit", 0d);
			printJson.put("useful_credit", usefulCredit);
			printJson.put("main_balance", balance);
			printJson.put("reward_balance", 0d);
			printJson.put("total_main", 0d);
			printJson.put("total_reward", 0d);
			printJson.put("bill_money", Math.abs(payAmount));
			printJson.put("consume_cardmoney", payAmount);
			printJson.put("main_trading", payAmount);
			printJson.put("reward_trading", 0d);
			printJson.put("operator", operator);
			printJson.put("updatetime", DateUtil.format(DateUtil.currentTimestamp()));

			this.customerCardConsumePrint(tenancyId, storeId, posNum, printJson, SysDictionary.PRINT_CODE_1011);
		}
		catch (Exception e)
		{
			logger.info("会员小票打印失败", e);
		}
	}
	/**
	 * @param customerOperate
	 */
	public void insertCrmCardTradingList(PosCustomerOperateListEntity customerOperate){
		JSONObject tradingJson = null;
		try {
			tradingJson = customerDao.getCrmCardTradingListByBillCode(customerOperate.getTenancy_id(), customerOperate.getShift_id(), customerOperate.getBill_code());
		} catch (Exception e) {
			e.printStackTrace();
		}
		if(null ==tradingJson){
			Timestamp storeUpdatetime = DateUtil.currentTimestamp();
			Timestamp lastUpdatetime = DateUtil.currentTimestamp();
			Timestamp operateTime = DateUtil.currentTimestamp();
			
			//-----------------插入crm_card_trading_list表记录 start-------------------
			CrmCardTradingListEntity cardTradingList = new CrmCardTradingListEntity();
			cardTradingList.setThird_bill_code(customerOperate.getThird_bill_code());
			cardTradingList.setBill_code(customerOperate.getBill_code());
			//cardTradingList.setCard_id(cardId);
			cardTradingList.setCard_code(customerOperate.getCard_code());
			//cardTradingList.setCard_class_id("card_class_id");
			cardTradingList.setName(customerOperate.getCustomer_name());
			cardTradingList.setMobil(customerOperate.getMobil());
			cardTradingList.setBusiness_date(customerOperate.getBusiness_date());
			cardTradingList.setShift_id(customerOperate.getShift_id());
			cardTradingList.setChanel(customerOperate.getChanel());
			cardTradingList.setOperat_type(customerOperate.getOperat_type());
			//账单金额
			cardTradingList.setBill_money(customerOperate.getTrade_amount());
			
			cardTradingList.setMain_trading(customerOperate.getTrade_amount());//交易
			cardTradingList.setReward_trading(0d); //0
			cardTradingList.setRevoked_trading(0d);//0
			
			JSONObject extendJson = JSONObject.fromObject(customerOperate.getExtend_param());
			Double balance = 0d;
			if(extendJson.containsKey("balance") && ParamUtil.getDoubleValueByObject(extendJson, "balance")>0){
				balance = extendJson.optDouble("balance");
			}
			
			cardTradingList.setMain_original(balance+customerOperate.getTrade_amount()); //原主账户金额
			cardTradingList.setReward_original(0d); //原赠送账户金额
			
			//交易余额
			cardTradingList.setMain_balance(balance); //主账户
			cardTradingList.setReward_balance(0d); //副账户
			cardTradingList.setTotal_balance(0d);//合计 
			
			cardTradingList.setDeposit(0d);
			cardTradingList.setOperate_time(operateTime);
			cardTradingList.setLast_updatetime(lastUpdatetime);
			cardTradingList.setStore_updatetime(storeUpdatetime);
			cardTradingList.setOperator_id(customerOperate.getOperator_id());
			if(customerOperate.getOperator_id()!=null){
				String operator=null;;
				try {
					operator = customerDao.getEmpNameById(customerOperate.getOperator_id().toString(), customerOperate.getTenancy_id(), customerOperate.getStore_id());
				} catch (Exception e) {
					e.printStackTrace();
				}
				cardTradingList.setOperator(operator);
			}
			cardTradingList.setInvoice_balance(0d);
			cardTradingList.setIs_invoice("0");
			cardTradingList.setPayment_state(SysDictionary.THIRD_PAY_STATUS_SUCCESS);
			cardTradingList.setRecharge_state(SysDictionary.CUSTOMER_OPERATE_STATE_SUCCESS);
			cardTradingList.setRequest_status(SysDictionary.REQUEST_STATUS_COMPLETE);
			cardTradingList.setBill_code_original(customerOperate.getBill_code());
			cardTradingList.setBatch_num(null);
			cardTradingList.setCommission_saler_money(0d);
			cardTradingList.setCommission_store_money(0d);
			cardTradingList.setPay_type(null);
			cardTradingList.setPosNum(customerOperate.getPos_num());
			try {
				customerDao.insertCrmCardTradingList(customerOperate.getTenancy_id(), customerOperate.getStore_id(),cardTradingList);
			} catch (Exception e) {
				e.printStackTrace();
			}
			//-----------------插入crm_card_trading_list表记录 end-------------------
		}	
	}
	
	/**
	 * 消费记录详情
	 */
	@Override
	public Data getAcewillCustomerView(String tenancyId, Integer storeId, JSONObject data) throws Exception {
//		String requestUrl = getPequestUrl(AcewillRequestUtil.CUSTOMER_DEAL_VIEW_URL);
		Data resData = null;

		String deal_id = ParamUtil.getStringValueByObject(data, "bill_code");
		if(Tools.isNullOrEmpty(deal_id))
		{
			resData = Data.get();
			resData.setCode(Constant.CODE_INNER_EXCEPTION);
			resData.setMsg("消费记录详情：消费流水号为空");
			resData.setSuccess(false);
			return resData;
		}

		JSONObject param = new JSONObject();
		param.put("deal_id", deal_id);
		ArrayList<JSONObject> dataList = new ArrayList<JSONObject>();
		dataList.add(param);
		Data requestData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
		requestData.setData(dataList);

		try {
			resData = this.commonPost(requestData, AcewillRequestUtil.CUSTOMER_DEAL_VIEW_URL);
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		if (resData.isSuccess()) {
			JSONObject resultData = JSONObject.fromObject(resData.getData().get(0));
			
			JSONObject jsonObject = new JSONObject();
			jsonObject.put("tcid", ParamUtil.getStringValueForNullByObject(resultData, "deal_id"));
			jsonObject.put("card_code", ParamUtil.getStringValueForNullByObject(resultData, "cno"));
			jsonObject.put("shopid", ParamUtil.getStringValueForNullByObject(resultData, "sid"));
			Double total_fee = 0d;
			if (resultData.containsKey("total_fee") && ParamUtil.getIntegerValueByObject(resultData, "total_fee") > 0) {
				total_fee = AcewillUtil.changeF2Y(resultData.optInt("total_fee"));
			}
			jsonObject.put("total_fee", total_fee);

			Double fee = 0d;
			if (resultData.containsKey("fee") && ParamUtil.getIntegerValueByObject(resultData, "fee") > 0) {
				fee = AcewillUtil.changeF2Y(resultData.optInt("fee"));
			}
			jsonObject.put("fee", fee);

			Double stored_pay = 0d;
			if (resultData.containsKey("stored_pay")
					&& ParamUtil.getIntegerValueByObject(resultData, "stored_pay") > 0) {
				stored_pay = AcewillUtil.changeF2Y(resultData.optInt("stored_pay"));
			}
			jsonObject.put("stored_pay", stored_pay);

			Double stored_sale_pay = 0d;
			if (resultData.containsKey("stored_sale_pay") && ParamUtil.getIntegerValueByObject(resultData, "stored_sale_pay") > 0)
			{
				stored_sale_pay = AcewillUtil.changeF2Y(ParamUtil.getIntegerValueByObject(resultData, "stored_sale_pay"));
			}
			jsonObject.put("stored_sale_pay", stored_sale_pay);

			Double cash_coupon_pay = 0d;
			if (resultData.containsKey("cash_coupon_pay")
					&& ParamUtil.getIntegerValueByObject(resultData, "cash_coupon_pay") > 0) {
				cash_coupon_pay = AcewillUtil.changeF2Y(resultData.optInt("cash_coupon_pay"));
			}
			jsonObject.put("cash_coupon_pay", cash_coupon_pay);

			Double gift_counpon_pay = 0d;
			if (resultData.containsKey("gift_counpon_pay") && ParamUtil.getIntegerValueByObject(resultData, "gift_counpon_pay") > 0)
			{
				gift_counpon_pay = AcewillUtil.changeF2Y(resultData.optInt("gift_counpon_pay"));
			}
			jsonObject.put("gift_counpon_pay", gift_counpon_pay);

			jsonObject.put("credit_num", ParamUtil.getIntegerValueByObject(resultData, "credit_num"));

			jsonObject.put("credit_pay", ParamUtil.getIntegerValueByObject(resultData, "credit_pay"));

			jsonObject.put("credit_award", ParamUtil.getIntegerValueByObject(resultData, "credit_award"));
			// 1:充值 2:消费 3:撤销消费 4:被撤销消费 5:系统回收 6:撤销充值 7:被撤销充值 8:手工调账减少充值
			// 9:手工调账减少积分 10:积分换礼
			jsonObject.put("type", ParamUtil.getIntegerValueByObject(resultData, "type"));
			// 1:现金 2:银行卡 3:微信 4:支付宝 6:手工调整
			jsonObject.put("pay_type", ParamUtil.getIntegerValueByObject(resultData, "pay_type"));
			jsonObject.put("pay_time", ParamUtil.getStringValueForNullByObject(resultData, "pay_time"));
			jsonObject.put("cashier_id", ParamUtil.getStringValueForNullByObject(resultData, "cashier_id"));
			jsonObject.put("remark", ParamUtil.getStringValueForNullByObject(resultData, "remark"));
			jsonObject.put("third_bill_code", ParamUtil.getStringValueForNullByObject(resultData, "biz_id"));
			// 交易状态 1：预消费 2：成功消费
			jsonObject.put("status", ParamUtil.getIntegerValueByObject(resultData, "status"));
			
			List<JSONObject> list = new ArrayList<JSONObject>();
			list.add(jsonObject);
			resData.setData(list);
		}
		return resData;
	}
	
	/**
	 * 获取门店id
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @return
	 * @throws Exception
	 */
	public String getShopId(String tenancyId, int storeId) throws Exception
	{
		JSONObject organJson = customerDao.getOrganById(tenancyId, storeId);
		if (null != organJson && organJson.containsKey("third_organ_code"))
		{
			return organJson.optString("third_organ_code");
		}
		return null;
	}

	/**
	 * 发送验证码
	 */
	@Override
	public Data sendCodeAcewillCustomer(Data requestData) throws Exception {
//		String requestUrl = getPequestUrl(AcewillRequestUtil.CUSTOMER_USER_SEND_CODE);
		String tenancyId = requestData.getTenancy_id();
		int storeId = requestData.getStore_id();
		Data resData = new Data();
		resData.setType(requestData.getType());// ACEWILL_CUSTOMER
		resData.setOper(Oper.sendcode);
		resData.setTenancy_id(tenancyId);
		resData.setStore_id(storeId);

		JSONObject data = null;
		try {
			data = JSONObject.fromObject(requestData.getData().get(0));
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("请求参数转换json格式失败");
			resData.setCode(Constant.CODE_INNER_EXCEPTION);
			resData.setMsg("请求参数转换json格式失败");
			resData.setSuccess(false);
			return resData;
		}

		// 请求参数
		String cno = data.optString("card_code");
		List<JSONObject> list = new ArrayList<JSONObject>();
		JSONObject param = new JSONObject();
		param.put("cno", cno);
		list.add(param);
		requestData.setData(list);
		try {
			resData = this.commonPost(requestData, AcewillRequestUtil.CUSTOMER_USER_SEND_CODE);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return resData;
	}

	@Override
	public void printForCustomer(String tenancyId, Integer storeId, String printCode, JSONObject paramJson) throws Exception
	{
		String posNum = ParamUtil.getStringValueByObject(paramJson, "pos_num");
		try
		{
			// 会员卡打印
			this.customerCardConsumePrint(tenancyId, storeId, posNum, paramJson, printCode);
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
	}
	
	public void customerCardConsumePrint(String tenantId, int storeId,String posNum,JSONObject para,String printCode) throws Exception
	{
		try
		{
			if (posPrintNewService.isNONewPrint(tenantId, storeId))
			{// 如果启用新的打印模式
			    para.put("pos_num", posNum);
				posPrintNewService.posPrintByMode(tenantId, storeId, printCode, para);
			}
			else
			{
				double main_balance = para.optDouble("main_balance");
				double reward_balance = para.optDouble("reward_balance");

				JSONObject param = new JSONObject();
				param.put("bill_code", para.optString("bill_code"));
				param.put("card_code", para.optString("card_code"));
				param.put("card_class_name", para.optString("card_class_name"));
				param.put("name", para.optString("name"));
				param.put("mobil", para.optString("mobil"));
				param.put("credit", para.optDouble("credit"));
				param.put("useful_credit", para.optDouble("useful_credit"));
				param.put("main_balance", main_balance);
				param.put("reward_balance", reward_balance);
				param.put("total_balance", DoubleHelper.add(main_balance, reward_balance, 4));
				param.put("total_main", para.optDouble("total_main"));
				param.put("total_reward", para.optDouble("total_reward"));
				param.put("consume_cardmoney", para.optDouble("consume_cardmoney"));
				param.put("operator", para.optString("operator"));
				param.put("updatetime", para.optString("updatetime"));
				param.put("main_trading", para.optDouble("main_trading"));
				param.put("reward_trading", para.optDouble("reward_trading"));
				param.put("payment_name1", "");
				param.put("income", 0d);
				param.put("deposit", 0d);
				param.put("sales_price", 0d);

				String printCountStr = customerDao.getSysParameter(tenantId, storeId, "MemberReceiptCount");
				int printCount = 1;
				if (Tools.hv(printCountStr))
				{
					printCount = Integer.parseInt(printCountStr);
				}

				customerDao.customerPrint(tenantId, storeId, posNum, printCode, "1", param, printCount);
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
			logger.error("会员消费小票打印失败:", e);
		}
	}

	@Override
	public JSONObject queryAcewillCustomerDeal(String tenancyId, Integer storeId, JSONObject paramJson) throws Exception
	{
		String thirdBillCode = ParamUtil.getStringValueForNullByObject(paramJson, "third_bill_code");
		String billCode = ParamUtil.getStringValueForNullByObject(paramJson, "bill_code");
		PosCustomerOperateListEntity customerOperate =null;
		if (thirdBillCode != null && billCode != null)
		{
			//查询记录
			customerOperate = customerDao.queryAcewillCustomerDeal(tenancyId, storeId, null, thirdBillCode, billCode, null, null, SysDictionary.OPERAT_TYPE_XF);
			if (null == customerOperate)
			{
				customerOperate = new PosCustomerOperateListEntity();
				customerOperate.setOperate_state(SysDictionary.CUSTOMER_OPERATE_STATE_PROCESS);
				customerOperate.setBill_code(billCode);
				customerOperate.setThird_bill_code(thirdBillCode);
			}

			if (SysDictionary.CUSTOMER_OPERATE_STATE_PROCESS.equals(customerOperate.getOperate_state()))
			{
				Data queryData = this.getAcewillCustomerView(tenancyId, storeId, paramJson);

				if (Constant.CODE_SUCCESS == queryData.getCode())
				{
					JSONObject queryJson = JSONObject.fromObject(queryData.getData().get(0));
					String type = ParamUtil.getStringValueByObject(queryJson, "type");
					String status = ParamUtil.getStringValueByObject(queryJson, "status");

					if ("2".equals(type) && "2".equals(status))
					{
						customerOperate.setOperate_state(SysDictionary.CUSTOMER_OPERATE_STATE_SUCCESS);
						
						JSONObject extendJson = null;
						if(Tools.hv(customerOperate.getExtend_param()))
						{
							extendJson = JSONObject.fromObject(customerOperate.getExtend_param());
						}else {
							extendJson  = new JSONObject();
						}
						
						Double stored_pay = ParamUtil.getDoubleValueByObject(queryJson, "stored_pay");
						// 使用实际储值支付金额(单位:分)
						Double stored_sale_pay = ParamUtil.getDoubleValueByObject(queryJson, "stored_sale_pay");
						
						extendJson.put("stored_pay", stored_pay);
						extendJson.put("stored_sale_pay", stored_sale_pay);
						extendJson.put("receive_credit", queryJson.optInt("credit_award"));
						customerOperate.setExtend_param(extendJson.toString());
					}
					else if ("2".equals(type) && "1".equals(status))
					{
						customerOperate.setOperate_state(SysDictionary.CUSTOMER_OPERATE_STATE_PROCESS);
					}
					else
					{
						customerOperate.setOperate_state(SysDictionary.CUSTOMER_OPERATE_STATE_FAIL);
					}
					
					if (null != customerOperate.getId() && 0 != customerOperate.getId())
					{
						customerDao.updatePosCustomerOperateList(tenancyId, storeId, customerOperate);
					}
				}
			}
		}
		else
		{
			logger.info("微生活查询交易参数thirdBillCode或billCode为空");
		}

		JSONObject jsonObject = null;
		if (null != customerOperate)
		{
			jsonObject = new JSONObject();
			jsonObject.put("operate_state", customerOperate.getOperate_state());
			if (Tools.hv(customerOperate.getExtend_param()))
			{
				JSONObject extendJson = JSONObject.fromObject(customerOperate.getExtend_param());
				jsonObject.put("stored_pay", ParamUtil.getDoubleValueByObject(extendJson, "stored_pay"));
				jsonObject.put("stored_sale_pay", ParamUtil.getDoubleValueByObject(extendJson, "stored_sale_pay"));
				jsonObject.put("receive_credit", ParamUtil.getDoubleValueByObject(extendJson, "receive_credit"));
			}
		}
		
		return jsonObject;
	}

	@Override
	public JSONObject queryCancelAcewillCustomerDeal(String tenancyId, Integer storeId, JSONObject paramJson) throws Exception
	{
		String thirdBillCode = ParamUtil.getStringValueForNullByObject(paramJson, "third_bill_code");
		String billCode = ParamUtil.getStringValueForNullByObject(paramJson, "bill_code");
		if (thirdBillCode != null && !"".equals(thirdBillCode))
		{
			PosCustomerOperateListEntity customerOperate = customerDao.queryAcewillCustomerDeal(tenancyId, storeId, null, thirdBillCode, billCode, null, null, SysDictionary.OPERAT_TYPE_FXF);

			if (customerOperate != null)
			{
				JSONObject jsonObject = JSONObject.fromObject(customerOperate);
				return jsonObject;
			}
			else
			{
				return null;
			}
		}
		else
		{
			logger.info("微生活查询撤销状态参数thirdBillCode为空");
			return null;
		}
	}

	@Override
	public Data getAcewillGradeRule(String tenancyId, Integer storeId) throws Exception
	{
//		String url = getPequestUrl(AcewillRequestUtil.CUSTOMER_GRADE_RULE);

		List<JSONObject> list = new ArrayList<JSONObject>();
		list.add(new JSONObject());

		Data requestData = new Data();
		requestData.setData(list);
		requestData.setTenancy_id(tenancyId);
		requestData.setStore_id(storeId);
		logger.info("查询微生活可使用会员价的会员等级url是：" + AcewillRequestUtil.CUSTOMER_GRADE_RULE);
		Data resData = this.commonPost(requestData, AcewillRequestUtil.CUSTOMER_GRADE_RULE);
		
		return resData;
	}

	@Override
	public void saveCustomerOperationLog(String tenancyId,CustomerOperationLogs operationLogs) throws Exception {
		JSONObject dataObj = null;
		if(operationLogs!=null){
			dataObj = JSONObject.fromObject(operationLogs);
		}
		logger.info("开始记录会员操作日志，商户："+ tenancyId +"，参数："  + dataObj);
		try {

			String optNum = operationLogs.getOperation_id();
			if(!Tools.isNullOrEmpty(optNum)){
				String sql = "SELECT name FROM employee WHERE id="+optNum;
				SqlRowSet rsEmp = customerDao.query4SqlRowSet(sql);
				if(rsEmp.next()){
					String name = rsEmp.getString("name");
					if(!Tools.isNullOrEmpty(name)){
						dataObj.put("operation_name",name);
					}
				}
			}
			dataObj.put("operation_time",DateUtil.getNowDateYYDDMMHHMMSS());
			dataObj.put("upload_tag","0");
			customerDao.insertIgnorCase(tenancyId,"customer_operation_logs",dataObj);
		}catch (Exception e){
			e.printStackTrace();
			logger.error("记录会员操作日志出错，" + e.getMessage());
		}
		logger.info("开始记录会员操作日志结束。");
	}

	@Override
	public PosCustomerOperateListEntity getCustomerOperateListByAcewillXF(String tenancyId, Integer storeId, String cardCode, String thirdBillCode, String outTradeNo) throws Exception
	{
		return customerDao.queryCustomerOperateListByAcewillXF(tenancyId, storeId, cardCode, thirdBillCode, outTradeNo, SysDictionary.OPERAT_TYPE_XF);
	}

	@Override
	public Data lockAcewillCustomerDeal(String tenancyId, Integer storeId, String optNum, String cardCode, String lockTradeNo, String outTradeNo, Double subBalance, Double subCredit, List<String> couponsList) throws Exception
	{
		JSONObject param = new JSONObject();
		param.put("biz_id", outTradeNo);
		param.put("lock_biz_id", lockTradeNo);
		param.put("cno", cardCode);
		param.put("deno_coupon_ids", couponsList);
		param.put("sub_balance", AcewillUtil.changeY2F(subBalance));
		param.put("sub_credit", subCredit);
		param.put("shop_id", this.getShopId(tenancyId, storeId));
		param.put("cashier_id", this.getCashierId(tenancyId, storeId, optNum));
		param.put("is_mall", "0");

		List<JSONObject> list = new ArrayList<JSONObject>();
		list.add(param);

		Data requestData = Data.get();
		requestData.setData(list);

		Data resData = null;
		try
		{
//			String requestUrl = this.getPequestUrl(AcewillRequestUtil.LOCK_CUSTOMER_DEAL_URL);
			resData = this.commonPost(requestData, AcewillRequestUtil.LOCK_CUSTOMER_DEAL_URL);
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		return resData;
	}

	@Override
	public Data unlockAcewillCustomerDeal(String tenancyId, Integer storeId, String optNum, String cardCode, String lockTradeNo, String outTradeNo) throws Exception
	{
		Data queryData = this.lockqueryAcewillCustomerDeal(tenancyId, storeId, cardCode);

		if (Constant.CODE_SUCCESS == queryData.getCode())
		{
			JSONObject queryResultJson = ParamUtil.getJSONObjectForData(queryData);
			String status = ParamUtil.getStringValueByObject(queryResultJson, "status");
			String bizId = ParamUtil.getStringValueByObject(queryResultJson, "biz_id");
			if (!"1".equals(status))
			{
				Data resutData = Data.get();
				resutData.setCode(Constant.CODE_SUCCESS);
				resutData.setMsg(Constant.CODE_SUCCESS_MSG);
				return resutData;
			}

			// 会员卡锁定单号与当前不一致
			if (null != outTradeNo && !outTradeNo.equals(bizId))
			{
				SystemException se = SystemException.getInstance(CrmErrorCode.DEAL_LOCK_NUMBER_DISCORD_ERROR);
				Data resutData = Data.get();
				resutData.setCode(se.getErrorCode().getNumber());
				resutData.setMsg(se.getErrorMsg());
				return resutData;
			}
		}
		return this.unlockAcewillCustomerDeal(tenancyId, storeId, optNum, cardCode);
	}
	
	/** 解锁资产占用
	 * @param tenancyId
	 * @param storeId
	 * @param cardCode
	 * @return
	 * @throws Exception
	 */
	private Data unlockAcewillCustomerDeal(String tenancyId, int storeId, String optNum, String cardCode) throws Exception
	{
		JSONObject param = new JSONObject();
		param.put("cno", cardCode);
		param.put("shop_id", this.getShopId(tenancyId, storeId));
		param.put("cashier_id", this.getCashierId(tenancyId, storeId, optNum));
		param.put("is_mall", "0");

		List<JSONObject> list = new ArrayList<JSONObject>();
		list.add(param);

		Data requestData = Data.get();
		requestData.setData(list);

		Data resData = null;
		try
		{
//			String requestUrl = this.getPequestUrl(AcewillRequestUtil.UNLOCK_CUSTOMER_DEAL_URL);
			resData = this.commonPost(requestData, AcewillRequestUtil.UNLOCK_CUSTOMER_DEAL_URL);
			
			if(AcewillRequestUtil.ALREADY_UNLOCK_ERROR_CODE==resData.getCode())
			{
				resData.setCode(Constant.CODE_SUCCESS);
				resData.setMsg(Constant.CODE_SUCCESS_MSG);
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		return resData;
	}
	
	/**查询资产占用
	 * @param tenancyId
	 * @param storeId
	 * @param cardCode
	 * @return
	 * @throws Exception
	 */
	private Data lockqueryAcewillCustomerDeal(String tenancyId, int storeId, String cardCode) throws Exception
	{
		JSONObject param = new JSONObject();
		param.put("cno", cardCode);
		param.put("biz_id", "");

		List<JSONObject> list = new ArrayList<JSONObject>();
		list.add(param);

		Data requestData = Data.get();
		requestData.setData(list);

		Data resData = null;
		try
		{
//			String requestUrl = this.getPequestUrl(AcewillRequestUtil.LOCKQUERY_CUSTOMER_DEAL_URL);
			resData = this.commonPost(requestData, AcewillRequestUtil.LOCKQUERY_CUSTOMER_DEAL_URL);
			
			if(AcewillRequestUtil.ALREADY_UNLOCK_ERROR_CODE==resData.getCode())
			{
				resData.setCode(Constant.CODE_SUCCESS);
				resData.setMsg(Constant.CODE_SUCCESS_MSG);
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		return resData;
	}
}
