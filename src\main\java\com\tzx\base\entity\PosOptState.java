package com.tzx.base.entity;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

/**
 * 人员签到操作表映射实体
 * 
 * <AUTHOR>
 * <AUTHOR>
 */
public class PosOptState implements Serializable
{
	/**
	 * 
	 */
	private static final long	serialVersionUID	= -4249637304589788461L;

	private String				tenancy_id;
	private Integer				store_id;
	private Integer				id;
	private Date				report_date;
	private Integer				shift_id;
	private String				content;
	private String				pos_num;
	private String				opt_num;
	private String				opt_name;
	private String				manager_num;
	private String				tag;
	private Timestamp			last_updatetime;
	private Integer				login_number;
	private String				remark;
	private Integer				upload_tag;

	public String getTenancy_id()
	{
		return tenancy_id;
	}

	public void setTenancy_id(String tenancy_id)
	{
		this.tenancy_id = tenancy_id;
	}

	public Integer getStore_id()
	{
		return store_id;
	}

	public void setStore_id(Integer store_id)
	{
		this.store_id = store_id;
	}

	public Integer getId()
	{
		return id;
	}

	public void setId(Integer id)
	{
		this.id = id;
	}

	public Date getReport_date()
	{
		return report_date;
	}

	public void setReport_date(Date report_date)
	{
		this.report_date = report_date;
	}

	public Integer getShift_id()
	{
		return shift_id;
	}

	public void setShift_id(Integer shift_id)
	{
		this.shift_id = shift_id;
	}

	public String getContent()
	{
		return content;
	}

	public void setContent(String content)
	{
		this.content = content;
	}

	public String getPos_num()
	{
		return pos_num;
	}

	public void setPos_num(String pos_num)
	{
		this.pos_num = pos_num;
	}

	public String getOpt_num()
	{
		return opt_num;
	}

	public void setOpt_num(String opt_num)
	{
		this.opt_num = opt_num;
	}

	public String getOpt_name()
	{
		return opt_name;
	}

	public void setOpt_name(String opt_name)
	{
		this.opt_name = opt_name;
	}

	public String getManager_num()
	{
		return manager_num;
	}

	public void setManager_num(String manager_num)
	{
		this.manager_num = manager_num;
	}

	public String getTag()
	{
		return tag;
	}

	public void setTag(String tag)
	{
		this.tag = tag;
	}

	public Timestamp getLast_updatetime()
	{
		return last_updatetime;
	}

	public void setLast_updatetime(Timestamp last_updatetime)
	{
		this.last_updatetime = last_updatetime;
	}

	public Integer getLogin_number()
	{
		return login_number;
	}

	public void setLogin_number(Integer login_number)
	{
		this.login_number = login_number;
	}

	public String getRemark()
	{
		return remark;
	}

	public void setRemark(String remark)
	{
		this.remark = remark;
	}

	public Integer getUpload_tag()
	{
		return upload_tag;
	}

	public void setUpload_tag(Integer upload_tag)
	{
		this.upload_tag = upload_tag;
	}
}
