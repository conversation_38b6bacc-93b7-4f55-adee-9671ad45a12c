package com.tzx.clientorder.acewillwechat.bo;

import com.tzx.pos.asyn.thread.OrderingPrintThread;

import net.sf.json.JSONObject;

/**
 * Created by 郑鹏飞 on 2018/3/10.
 */
public interface InsertOrderService{

    String	NAME	= "com.tzx.clientorder.acewillwechat.bo.imp.InsertOrderServiceImpl";

	void orderingPrint(OrderingPrintThread orderingPrintThread);

	OrderingPrintThread insertOrderRequiresNew(String tenancyId, Integer storeId, JSONObject jsonObject);

}
