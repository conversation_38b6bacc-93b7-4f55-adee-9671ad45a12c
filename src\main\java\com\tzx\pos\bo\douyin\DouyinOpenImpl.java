package com.tzx.pos.bo.douyin;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.Unirest;
import com.mashape.unirest.http.exceptions.UnirestException;
import com.tzx.base.common.util.PosPropertyUtil;
import com.tzx.framework.common.util.UUIDUtil;
import com.tzx.orders.base.util.OrderUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR> at 2021-12-14
 */
@Service
public class DouyinOpenImpl implements IDouyinOpen {


    private final static Logger logger = LoggerFactory.getLogger(DouyinOpenImpl.class);
    private final static ObjectMapper objectMapper = new ObjectMapper();

    private static String ACCESS_TOKEN = null;

    private static AtomicBoolean initialized = new AtomicBoolean(false);

    @Value("${douyin.api.url:}")
    private String baseUrl;

    @Value("${douyin.client.key:}")
    private String clientKey;

    @Value("${douyin.client.secret:}")
    private String clientSecret;

    private String poiId;

    static {
        System.setProperty("https.protocols", "TLSv1,TLSv1.1,TLSv1.2");
    }

    private void initSysParam() {
        if (!initialized.get()) {
            String sbaseUrl = OrderUtil.getSysPara(null, 0, "DOUYIN_API_URL");
            if (StringUtils.isNotEmpty(sbaseUrl)) {
                baseUrl = sbaseUrl;
            }
            String sclientKey = OrderUtil.getSysPara(null, 0, "DOUYIN_CLIENT_KEY");
            if (StringUtils.isNotEmpty(sclientKey)) {
                clientKey = sclientKey;
            }
            String sclientSecret = OrderUtil.getSysPara(null, 0, "DOUYIN_CLIENT_SECRET");
            if (StringUtils.isNotEmpty(sclientSecret)) {
                clientSecret = sclientSecret;
            }
            poiId = OrderUtil.getSysPara(null, Integer.valueOf(PosPropertyUtil.loadPosSystemProperties("store_id")), "DOUYIN_STORE_ID");

            initialized.set(true);
        }
    }

    @Override
    public String getAccessToken() throws UnirestException, IOException {
        initSysParam();

        HttpResponse<String> response = Unirest.post(baseUrl + GET_ACCESS_TOKEN_URL).field("client_key", clientKey).field("client_secret", clientSecret).field("grant_type", grantType).asString();
        checkResponseStateOk(response);
        String token = objectMapper.readTree(response.getBody()).path("data").path("access_token").asText();
        checkResponseFieldValueIsValid(response, token, "access_token为空");
        ACCESS_TOKEN = token;
        return token;
    }

    @Override
    public JsonNode prepare(String code) throws UnirestException, IOException {
        return prepare(code, true);
    }

    private JsonNode prepare(String code, boolean checkToken) throws UnirestException, IOException {
        String requestId = UUIDUtil.generateGUID();

        StringBuilder url = new StringBuilder(baseUrl);
        url.append(PREPARE_URL);
       /* url.append("?client_token=");
        url.append(StringUtils.isEmpty(ACCESS_TOKEN) ? getAccessToken() : ACCESS_TOKEN);*/
        String assessToken= doGetAssessToken();

        if (code.startsWith("https://")) {
            url.append("?encrypted_data=");
            url.append(DouyinOpenUtil.getEncryptedDataByQRCode(code));
        } else {
            url.append("?code=");
            url.append(code);
        }


        logger.info("douyin[" + requestId + "] url= " + url);
        HttpResponse<String> response = Unirest.get(url.toString())
                .header("Content-Type","application/json")
                .header("access-token",assessToken)
                .asString();
        checkResponseStateOk(response);

        logger.info("douyin[" + requestId + "] response body:" + response.getBody());
        JsonNode node = objectMapper.readTree(response.getBody());
        //刷新token，重新请求
        if (checkToken && checkIfRefreshToken(node)) {
            getAccessToken();
            return prepare(code, false);
        }
        checkResponseCode(node);

        return node.path("data");
    }



    @Override
    public JsonNode verify(String verifyToken, List<String> encryptedCodes, List<String> codes) throws UnirestException, IOException {
        return verify(verifyToken, encryptedCodes, codes, true);
    }

    private JsonNode verify(String verifyToken, List<String> encryptedCodes, List<String> codes, boolean checkToken) throws UnirestException, IOException {

        String requestId = UUIDUtil.generateGUID();

        StringBuilder url = new StringBuilder(baseUrl);
        url.append(VERIFY_URL);
       /* url.append("?client_token=");
        url.append(ACCESS_TOKEN == null ? getAccessToken() : ACCESS_TOKEN);*/
        String assessToken= doGetAssessToken();

        HttpResponse<String> response = null;

        StringBuilder body = new StringBuilder();
        body.append("{\"verify_token\": \"" + verifyToken + "\",");

        if(StringUtils.isNotEmpty(poiId)){
            body.append("\"poi_id\": \"" + poiId + "\",");
        }

        String codesStr = "";
        if (CollectionUtils.isNotEmpty(encryptedCodes)) {
            for (String encryptedCode : encryptedCodes) {
                codesStr += ",\"" + encryptedCode + "\"";
            }
        }
        if (CollectionUtils.isNotEmpty(codes)) {
            for (String code : codes) {
                codesStr += ",\"" + code + "\"";
            }
        }

        body.append("\"encrypted_codes\": [" + codesStr.replaceFirst(",", "") + "]}");
        logger.info("douyin[" + requestId + "] url= " + url + " ,body : " + body);
        response = Unirest.post(url.toString())
                .header("Content-Type","application/json")
                .header("access-token",assessToken)
                .body(body.toString()).asString();
        checkResponseStateOk(response);
        logger.info("douyin[" + requestId + "] response body:" + response.getBody());
        JsonNode node = objectMapper.readTree(response.getBody());
        //刷新token，重新请求
        if (checkToken && checkIfRefreshToken(node)) {
            getAccessToken();
            return verify(verifyToken, encryptedCodes, codes, false);
        }
        checkResponseCode(node);
        return node.path("data");
    }

    @Override
    public JsonNode cancel(String verifyId, String certificateId,int timesCardCancelCount,String cancelToken) throws UnirestException, IOException {
        return cancel(verifyId, certificateId, timesCardCancelCount,cancelToken,true);
    }

    private JsonNode cancel(String verifyId, String certificateId,int timeCardCancelCount,String cancelToken, boolean checkToken) throws UnirestException, IOException {
        String requestId = UUIDUtil.generateGUID();

        StringBuilder url = new StringBuilder(baseUrl);
        url.append(CANCEL_URL);
        /*url.append("?client_token=");
        url.append(ACCESS_TOKEN == null ? getAccessToken() : ACCESS_TOKEN);*/
        String assessToken= doGetAssessToken();

        String body ="";

        if(timeCardCancelCount>1){

            //撤销多张verify_id传0
             body = "{\"verify_id\": \"0\",\"certificate_id\": \"" + certificateId +
                    "\",\"times_card_cancel_count\": " + timeCardCancelCount + ",\"cancel_token\": \"" + cancelToken + "\"}";
        }else {

            body = "{\"verify_id\": \"" + verifyId + "\",\"certificate_id\": \"" + certificateId + "\"}";
        }


        logger.info("douyin[" + requestId + "] url= " + url + ", body: " + body);
        HttpResponse<String> response = Unirest.post(url.toString())
                .header("Content-Type","application/json")
                .header("access-token",assessToken)
                .body(body)
                .asString();
        logger.info("douyin[" + requestId + "] response body:" + response.getBody());

        checkResponseStateOk(response);

        JsonNode node = objectMapper.readTree(response.getBody());
        //刷新token，重新请求
        if (checkToken && checkIfRefreshToken(node)) {
            getAccessToken();
            return cancel(verifyId, certificateId,timeCardCancelCount,cancelToken, false);
        }
        checkResponseCode(node);
        return node.path("data");
    }

    @Override
    public JsonNode queryCertificate() {
        return null;
    }

    private void checkResponseStateOk(HttpResponse<String> response) {
        if (200 != response.getStatus()) {
            throw new DouyinOpenInvokeException(String.format("[statusCode: %d %s]", response.getStatus(), response.getStatusText()));
        }
    }

    private void checkResponseFieldValueIsValid(HttpResponse response, String fieldValue, String errorMsg) {
        if (StringUtils.isEmpty(fieldValue)) {
            throw new DouyinOpenInvokeException(String.format("[field_%s]", errorMsg));
        }
    }

    private boolean checkIfRefreshToken(JsonNode node) throws UnirestException, IOException {
        if ("2190008".equals(node.path("extra").path("error_code").asText())) {
            return true;
        }
        return false;
    }

    private void checkResponseCode(JsonNode node) {
        if (!"0".equals(node.path("extra").path("error_code").asText())) {
            throw new DouyinOpenInvokeException(String.format("[%s, %s]", node.path("extra").path("error_code").asText(), node.path("extra").path("description").asText()));
        }
       /* if (!"0".equals(node.path("base").path("biz_code").asText())) {
            throw new DouyinOpenInvokeException(String.format("[biz_%s, %s]", node.path("base").path("biz_code").asText(), node.path("base").path("biz_msg").asText()));
        }*/
    }

    private String doGetAssessToken() throws UnirestException, IOException {
        return StringUtils.isEmpty(ACCESS_TOKEN) ? getAccessToken() : ACCESS_TOKEN;
    }


}