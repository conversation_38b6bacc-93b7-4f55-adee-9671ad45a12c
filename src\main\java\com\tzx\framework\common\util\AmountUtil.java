package com.tzx.framework.common.util;

import java.math.BigDecimal;

/**
 * <AUTHOR> at 2021-12-31
 */
public class AmountUtil {
    /**

     * 分转元(除以100)四舍五入，保留2位小数

     * @param amount 金额 元

     * @return

     */

    public static Double changeF2Y(Double amount){

        return new BigDecimal(amount).divide(new BigDecimal(100)).setScale(2).doubleValue();

    }

    /**

     * 元转分(乘以100)

     * @param amount 金额 分(支持大数字，16位以上)

     * @return

     */

    public static Double changeY2F(Double amount){

        return new BigDecimal(amount).multiply(new BigDecimal(100)).doubleValue();

    }

}
