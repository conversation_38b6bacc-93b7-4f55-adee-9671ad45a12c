package com.tzx.pos.bo.payment.imp;

import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.bo.imp.PosBaseServiceImp;
import com.tzx.pos.bo.payment.PaymentWayService;
import com.tzx.pos.bo.payment.imp.ext.*;
import com.tzx.pos.po.springjdbc.dao.PosPaymentDao;
import net.sf.json.JSONObject;
import org.apache.log4j.Logger;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.Date;


public abstract class PaymentWayServiceImp extends PosBaseServiceImp implements PaymentWayService {
    public static final int PAYMENT_SCALE = 2;
    public static final int DEFAULT_SCALE = 4;

    @Resource(name = PosPaymentDao.NAME)
    protected PosPaymentDao paymentDao;

    protected static final Logger logger = Logger.getLogger(PaymentWayServiceImp.class);

    public static PaymentWayService getPaymentServiceBeanByPaymentClass(String tenancyId, int storeId, String paymentClass, String customerType, boolean isCheck) throws Exception {
        DBContextHolder.setTenancyid(tenancyId);
        PaymentWayService posPaymentService = null;
        switch (paymentClass) {
            case SysDictionary.PAYMENT_CLASS_FREESINGLE:
                posPaymentService = SpringConext.getApplicationContext().getBean(FreesinglePaymentWayServiceImp.class);
                break;
            case SysDictionary.PAYMENT_CLASS_CASH:
                posPaymentService = SpringConext.getApplicationContext().getBean(CashPaymentWayServiceImp.class);
                break;
            case SysDictionary.PAYMENT_CLASS_BANKCARD:
                posPaymentService = SpringConext.getApplicationContext().getBean(BankcardPaymentWayServiceImp.class);
                break;
            case SysDictionary.PAYMENT_CLASS_CARD:
                posPaymentService = SpringConext.getApplicationContext().getBean(CardPaymentWayServiceImp.class);
                break;
            case SysDictionary.PAYMENT_CLASS_CARD_CREDIT:
                posPaymentService = SpringConext.getApplicationContext().getBean(CreditPaymentWayServiceImp.class);
                break;
            case SysDictionary.PAYMENT_CLASS_COUPONS:
                posPaymentService = SpringConext.getApplicationContext().getBean(CouponsPaymentWayServiceImp.class);
                break;
            case SysDictionary.PAYMENT_CLASS_WLIFE_BALANCE:
            case SysDictionary.PAYMENT_CLASS_WLIFE_CREDIT:
            case SysDictionary.PAYMENT_CLASS_WLIFE_COUPON:
                posPaymentService = SpringConext.getApplicationContext().getBean(AcewillPaymentWayServiceImp.class);
                break;
            case SysDictionary.PAYMENT_CLASS_INCORPORATION:
                posPaymentService = SpringConext.getApplicationContext().getBean(IncorporationPaymentWayServiceImp.class);
                break;
            case SysDictionary.PAYMENT_CLASS_MEIDA_CARD_PAY:
                posPaymentService = SpringConext.getApplicationContext().getBean(MeiDaCardPaymentWayServiceImp.class);
                break;
            case SysDictionary.PAYMENT_CLASS_ALI_PAY:
            case SysDictionary.PAYMENT_DDM_QUICK_PAY:
            case SysDictionary.PAYMENT_CLASS_WECHAT_PAY:
            case SysDictionary.PAYMENT_CLASS_JD_PAY:
            case SysDictionary.PAYMENT_CLASS_FLASH_PAY:
            case SysDictionary.PAYMENT_CLASS_SCAN_CODE_PAY:
                posPaymentService = SpringConext.getApplicationContext().getBean(ThirdPayPaymentWayServiceImp.class);
                break;
            case SysDictionary.PAYMENT_CLASS_MEIDA_COUPONS_PAY:
                posPaymentService = SpringConext.getApplicationContext().getBean(MeituanCouponsPaymentWayServiceImp.class);
                break;
            case SysDictionary.PAYMENT_CLASS_DOUYIN_COUPONS_PAY:
                posPaymentService = SpringConext.getApplicationContext().getBean(DouyinCouponsPaymentWayServiceImp.class);
                break;
            case SysDictionary.PAYMENT_CLASS_SECONDS_PAY:
                posPaymentService = SpringConext.getApplicationContext().getBean(SecondsPaymentWayServiceImp.class);
                break;
            case SysDictionary.PAYMENT_CLASS_SECONDS_PAY_CREDIT:
                posPaymentService = SpringConext.getApplicationContext().getBean(SecondsCreditPaymentWayServiceImp.class);
                break;
            case SysDictionary.PAYMENT_CLASS_DEBIT_PAY:
                posPaymentService = SpringConext.getApplicationContext().getBean(DebitCardPaymentWayServiceImp.class);
                break;
            case SysDictionary.PAYMENT_CLASS_SYNCRETIC_PAY:
                posPaymentService = SpringConext.getApplicationContext().getBean(SyncreticPayPaymentWayServiceImp.class);
                break;
            default:
                posPaymentService = SpringConext.getApplicationContext().getBean(OtherPaymentWayServiceImp.class);
                break;
        }
        return posPaymentService;
    }

    @Override
    public abstract void payment(String tenantId, int storeId, String billNum, String batchNum, Date reportDate, int shiftId, String posNum, String optNum, String tableCode, JSONObject payment, Timestamp currentTime, String isOnlinePay) throws Exception;

    @Override
    public abstract void paymentDebit(String tenancyId, int storeId, String billNum, String orderNum, String chanel, Double paymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData) throws Exception;

    @Override
    public abstract void findPaymentDebit(String tenancyId, int storeId, String billNum, String orderNum, String chanel, Double paymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData) throws Exception;

    @Override
    public abstract void paymentDebitResult(String tenancyId, int storeId, String billNum, JSONObject paymentJson) throws Exception;

    @Override
    public abstract void paymentRefund(String tenancyId, int storeId, String billNum, String oldBillNum, String orderNum, String chanel, Double billPaymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData, String operType) throws Exception;

    @Override
    public abstract void paymentCancel(String tenancyId, int storeId, String billNum, String orderNum, String chanel, Double billPaymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData, String operType) throws Exception;

    @Override
    public abstract void paymentRegain(String tenancyId, int storeId, String billNum, String orderNum, String chanel, Double billPaymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData, String operType) throws Exception;
}
