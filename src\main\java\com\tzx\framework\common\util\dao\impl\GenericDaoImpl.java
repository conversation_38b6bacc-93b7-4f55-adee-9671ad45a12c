package com.tzx.framework.common.util.dao.impl;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.log4j.Logger;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.CallableStatementCallback;
import org.springframework.jdbc.core.CallableStatementCreator;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Repository;

import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.JsonUtils;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.framework.common.util.dao.datasource.MultiDataSourceManager;
import com.tzx.framework.common.util.dao.pojo.GenericDaoTableInfo;

import net.sf.json.JSONArray;
import net.sf.json.JSONNull;
import net.sf.json.JSONObject;

/**
 * 通用Dao实现类 所有的Dao继承它
 * 
 */
@Repository("genericDaoImpl")
public class GenericDaoImpl implements GenericDao
{
	private static Logger		logger	= Logger.getLogger(GenericDaoImpl.class);
	
	@Resource
	public JdbcTemplate		jdbcTemplate;

	private NumberFormat	numberFormat	= NumberFormat.getInstance();

	public JdbcTemplate getJdbcTemplate(String tenantId) throws Exception
	{
		// DBContextHolder.setTenancyid(tenantId);

		return this.jdbcTemplate;
	}

	public boolean execute(String tenantId, String sql) throws Exception
	{
		// DBContextHolder.setTenancyid(tenantId);

		this.jdbcTemplate.execute(sql);
		return true;
	}

	public SqlRowSet query(String tenantId, String sql) throws Exception
	{
		// DBContextHolder.setTenancyid(tenantId);

		return this.jdbcTemplate.queryForRowSet(sql);
	}

	public List<?> query(String tenantId, String sql, Class<?> cls) throws Exception
	{
		// DBContextHolder.setTenancyid(tenantId);

		return this.jdbcTemplate.query(sql, BeanPropertyRowMapper.newInstance(cls));
	}
	
	@Override
	public <T> List<T> query(String sql, Object[] obj, BeanPropertyRowMapper<T> className) throws Exception
	{
		return this.jdbcTemplate.query(sql, obj, className);
	}
	
	@Override
	public SqlRowSet query4SqlRowSet(String sql) throws Exception
	{
		return this.jdbcTemplate.queryForRowSet(sql);
	}
	
	@Override
	public SqlRowSet query4SqlRowSet(String sql, Object[] obj) throws Exception
	{
		return this.jdbcTemplate.queryForRowSet(sql, obj);
	}
	
	@Override
	public int queryForInt(String sql, Object[] obj) throws Exception
	{
		return this.jdbcTemplate.queryForObject(sql, Integer.class, obj);
	}
	
	@Override
	public <T> T queryForObject(String sql, Object[] obj, BeanPropertyRowMapper<T> className) throws Exception
	{
		List<T> list = this.query(sql, obj, className);
		if (null != list && 0 < list.size())
		{
			return list.get(0);
		}
		return null;
	}
	
	/**
	 * 加这个方法的原是：两次转JSONObject，后台没有错，返回给andriod或delphi那边是404
	 * 
	 * @param tenantId
	 * @param sql
	 * @return
	 * @throws Exception
	 */
	@Override
	public List<JSONObject> queryString4Json(String tenantId, String sql) throws Exception
	{
		List<JSONObject> list = new ArrayList<JSONObject>();

		List<Map<String, Object>> listMap = this.jdbcTemplate.queryForList(sql);

		List<Map<String, String>> listStrMap = new ArrayList<Map<String, String>>();

		for (Map<String, Object> map : listMap)
		{
			Map<String, String> strMap = new HashMap<String, String>();
			for (Map.Entry<String, Object> entry : map.entrySet())
			{
				if (entry.getValue() == null || entry.getValue() == "")
				{
					strMap.put(entry.getKey(), "");
				}
				else
				{
					strMap.put(entry.getKey(), "" + entry.getValue());
				}

			}

			listStrMap.add(strMap);
		}
		for (Map<String, String> map : listStrMap)
		{
			JSONObject json = JSONObject.fromObject(map, JsonUtils.gxhkJsonConfig);
			list.add(json);
		}

		return list;
	}

	/**
	 * 加这个方法的原是：两次转JSONObject，后台没有错，返回给andriod或delphi那边是404
	 * 
	 * @param tenantId
	 * @param sql
	 * @return
	 * @throws Exception
	 */
	@Override
	public List<JSONObject> queryString4Json(String tenantId, String sql, Object[] objs) throws Exception
	{
		List<JSONObject> list = new ArrayList<JSONObject>();

		List<Map<String, Object>> listMap = this.jdbcTemplate.queryForList(sql, objs);

		List<Map<String, String>> listStrMap = new ArrayList<Map<String, String>>();

		for (Map<String, Object> map : listMap)
		{
			Map<String, String> strMap = new HashMap<String, String>();
			for (Map.Entry<String, Object> entry : map.entrySet())
			{
				if (entry.getValue() == null || entry.getValue() == "")
				{
					strMap.put(entry.getKey(), "");
				}
				else
				{
					strMap.put(entry.getKey(), "" + entry.getValue());
				}
			}

			listStrMap.add(strMap);
		}
		for (Map<String, String> map : listStrMap)
		{
			JSONObject json = JSONObject.fromObject(map, JsonUtils.gxhkJsonConfig);
			list.add(json);
		}

		return list;
	}

	public List<JSONObject> query4Json(String tenantId, String sql) throws Exception
	{
		// DBContextHolder.setTenancyid(tenantId);
//		logger.info("进入query4Json方法");
		List<JSONObject> list = new ArrayList<JSONObject>();

		List<Map<String, Object>> listMap = this.jdbcTemplate.queryForList(sql);
//		logger.info("执行sql："+sql);
//		logger.info("返回结果集大小："+listMap.size());
		for (Map<String, Object> map : listMap)
		{
			JSONObject json = JSONObject.fromObject(map, JsonUtils.gxhkJsonConfig);
			list.add(json);
		}
//		logger.info("数据集转JSON列表，列表大小：："+list.size());
		return list;
	}

	public List<JSONObject> query4Json(String tenantId, String sql, Object[] param) throws Exception
	{
		// DBContextHolder.setTenancyid(tenantId);

		List<JSONObject> list = new ArrayList<JSONObject>();

		List<Map<String, Object>> listMap = this.jdbcTemplate.queryForList(sql, param);

		for (Map<String, Object> map : listMap)
		{
			JSONObject json = JSONObject.fromObject(map, JsonUtils.gxhkJsonConfig);
			list.add(json);
		}

		return list;
	}

	public JSONArray query4JSONArray(String tenantId, String sql) throws Exception
	{
		// DBContextHolder.setTenancyid(tenantId);

		JSONArray array = new JSONArray();

		List<Map<String, Object>> listMap = this.jdbcTemplate.queryForList(sql);

		for (Map<String, Object> map : listMap)
		{
			JSONObject json = JSONObject.fromObject(map, JsonUtils.gxhkJsonConfig);
			array.add(json);
		}

		return array;
	}
	/**
	 * 把原来拼sql改成？传参的形式
	 */
	public JSONArray query4JSONArray(String tenantId, String sql,Object[] objs) throws Exception
	{
		// DBContextHolder.setTenancyid(tenantId);

		JSONArray array = new JSONArray();

		List<Map<String, Object>> listMap = this.jdbcTemplate.queryForList(sql,objs);

		for (Map<String, Object> map : listMap)
		{
			JSONObject json = JSONObject.fromObject(map, JsonUtils.gxhkJsonConfig);
			array.add(json);
		}
		
		return array;
	}

	public int getInt(String tenantId, String sql) throws Exception
	{
		// DBContextHolder.setTenancyid(tenantId);

		return jdbcTemplate.queryForObject(sql, Integer.class);
	}

	public Double getDouble(String tenantId, String sql) throws Exception
	{
		// DBContextHolder.setTenancyid(tenantId);

		List<Double> list = jdbcTemplate.query(sql, new RowMapper<Double>()
		{
			public Double mapRow(ResultSet rs, int index) throws SQLException
			{
				return rs.getDouble(1);
			}
		});
		double d = 0;
		if (list != null && list.size() > 0)
		{
			d = list.get(0);
		}
		return d;
	}

	public String getString(String tenantId, String sql) throws Exception
	{
		// DBContextHolder.setTenancyid(tenantId);

		final StringBuffer str = new StringBuffer();
		str.append("");
		jdbcTemplate.query(sql, new RowMapper<Object>()
		{
			public Object mapRow(ResultSet rs, int index) throws SQLException
			{
				str.append(rs.getString(1));
				return null;
			}
		});
		return str.toString();
		// return jdbcTemplate.queryForObject(sql, String.class);
	}
	
	@Override
	public int update(String sql, Object[] obj) throws Exception
	{
		return this.jdbcTemplate.update(sql, obj);
	}

	@Override
	public int[] batchUpdate(String sql, List<Object[]> batchArgs) throws Exception
	{
		return this.jdbcTemplate.batchUpdate(sql, batchArgs);
	}

	public Object insertIgnorCase(String tenantId, String tableName, final JSONObject json) throws Exception
	{
		String sql =GenericDaoTableInfo.getInsert(tenantId, tableName, this, json);
		Object  result = null;
		if(sql.indexOf("RETURNING id")> -1){
			List<JSONObject> list = query4Json(tenantId, sql);
			result = list.get(0).optInt("id");
		}else{
			execute(tenantId, sql);
			result = 0;
		}
		return result;
		
//		DatabaseMetaData dbmd = null;
//
//		String idColumn = null;
//
//		Connection conn = this.jdbcTemplate.getDataSource().getConnection();
//
//		dbmd = conn.getMetaData();
//		ResultSet rs = dbmd.getPrimaryKeys(null, null, tableName);
//		if (rs.next())
//		{
//			idColumn = rs.getString(4);
//		}
//
//		MultiDataSourceManager.close(null, null, rs);
//
//		rs = dbmd.getColumns(null, null, tableName, null);
//
//		if (rs != null)
//		{
//			StringBuilder sql = new StringBuilder("INSERT INTO " + tableName + " (");
//
//			StringBuilder column = new StringBuilder();
//			StringBuilder value = new StringBuilder();
//
//			final Map<String, String> order = new HashMap<String, String>();
//			// order.put(1 + "@", idColumn);
//
//			while (rs.next())
//			{
//				String colname = rs.getString("COLUMN_NAME");
//
//				if (colname.equals(idColumn))
//				{
//					// order.remove("1@");
//					// order.put("1@" + rs.getInt("DATA_TYPE"), colname);
//				}
//				else
//				{
//					column.append("," + colname);
//					value.append(",?");
//
//					order.put((order.size() + 1) + "@" + rs.getInt("DATA_TYPE") + "@" + rs.getString("TYPE_NAME"), colname);
//				}
//			}
//
//			MultiDataSourceManager.close(conn, null, rs);
//
//			PreparedStatementSetter preparedStatementSetter = new PreparedStatementSetter()
//			{
//
//				@Override
//				public void setValues(PreparedStatement arg0) throws SQLException
//				{
//
//					for (String ii : order.keySet())
//					{
//						String columnName = order.get(ii);
//
//						Integer index = Integer.parseInt(ii.split("@")[0]);
//						Integer dataType = Integer.parseInt(ii.split("@")[1]);
//
//						Object obj = json.get(columnName);
//
//						if (obj == null)
//						{
//							obj = json.get(columnName.replace("_", ""));
//						}
//
//						if (obj instanceof JSONObject)
//						{
//							if (((JSONObject) obj).isEmpty() || ((JSONObject) obj).isNullObject())
//							{
//								obj = null;
//							}
//						}
//						else if (obj instanceof JSONNull)
//						{
//							obj = null;
//						}
//
//						if (obj != null)
//						{
//							switch (dataType)
//							{
//								case Types.TIMESTAMP:
//									obj = DateUtil.parseTimestamp(obj.toString());
//									break;
//								case Types.DATE:
//									Date d = DateUtil.parseDate(obj.toString());
//
//									if (d != null)
//									{
//										obj = new java.sql.Date(d.getTime());
//									}
//									else
//									{
//										obj = null;
//									}
//									break;
//								case Types.TIME:
//									obj = DateUtil.parseTime(obj.toString());
//									break;
//								case Types.BIT:
//									if ("true".equalsIgnoreCase(obj.toString()) || "1".equals(obj.toString()))
//									{
//										obj = true;
//									}
//									else
//									{
//										obj = false;
//									}
//									break;
//								case Types.NUMERIC:
//								case Types.INTEGER:
//								case Types.DOUBLE:
//								case Types.REAL:
//								case Types.FLOAT:
//								case Types.BIGINT:
//								case Types.SMALLINT:
//									if (obj.toString().isEmpty())
//									{
//										obj = null;
//									}
//									else
//									{
//										try
//										{
//											obj = numberFormat.parse(obj.toString());
//										}
//										catch (Exception e)
//										{
//											obj = null;
//										}
//									}
//									break;
//								case Types.DISTINCT:
//									if ("datetime".equals(ii.split("@")[2]))
//									{
//										obj = DateUtil.parseTimestamp(obj.toString());
//									}
//									break;
//								default:
//									break;
//							}
//
//						}
//
//						arg0.setObject(index, obj);
//					}
//				}
//
//			};
//
//			sql.append(column.substring(1) + " ) VALUES (" + value.substring(1) + ")");
//			
//			this.jdbcTemplate.update(sql.toString(), preparedStatementSetter);
//
//			Integer key = this.jdbcTemplate.queryForInt("SELECT currval('" + tableName + "_id_seq')");
//
//			return key;
//		}
//
//		return null;
	}

	

	public Object[] insertBatchIgnorCase(String tenantId, String tableName, final List<JSONObject> jsonList) throws Exception
	{
		// DBContextHolder.setTenancyid(tenantId);

//		final List<Object> ids = new ArrayList<Object>();
//
//		DatabaseMetaData dbmd = null;
//
//		String idColumn = null;
//
//		Connection conn = this.jdbcTemplate.getDataSource().getConnection();
//
//		dbmd = conn.getMetaData();
//		ResultSet rs = dbmd.getPrimaryKeys(null, null, tableName);
//		if (rs.next())
//		{
//			idColumn = rs.getString(4);
//		}
//
//		MultiDataSourceManager.close(null, null, rs);
//
//		rs = dbmd.getColumns(null, null, tableName, null);
//
//		if (rs != null)
//		{
//			StringBuilder sql = new StringBuilder("INSERT INTO " + tableName + " (");
//
//			StringBuilder column = new StringBuilder();
//			StringBuilder value = new StringBuilder();
//
//			final Map<String, String> order = new HashMap<String, String>();
//			// order.put(1 + "@", idColumn);
//
//			while (rs.next())
//			{
//				String colname = rs.getString("COLUMN_NAME");
//
//				if (colname.equals(idColumn))
//				{
//					// order.remove("1@");
//					// order.put("1@" + rs.getInt("DATA_TYPE"), colname);
//				}
//				else
//				{
//					column.append("," + colname);
//					value.append(",?");
//
//					order.put((order.size() + 1) + "@" + rs.getInt("DATA_TYPE") + "@" + rs.getString("TYPE_NAME"), colname);
//				}
//			}
//
//			MultiDataSourceManager.close(conn, null, rs);
//
//			BatchPreparedStatementSetter batchPreparedStatementSetter = new BatchPreparedStatementSetter()
//			{
//
//				@Override
//				public void setValues(PreparedStatement arg0, int arg1) throws SQLException
//				{
//					JSONObject json = jsonList.get(arg1);
//
//					for (String ii : order.keySet())
//					{
//						String columnName = order.get(ii);
//
//						Integer index = Integer.parseInt(ii.split("@")[0]);
//						Integer dataType = Integer.parseInt(ii.split("@")[1]);
//
//						Object obj = json.get(columnName);
//
//						if (obj == null)
//						{
//							obj = json.get(columnName.replace("_", ""));
//						}
//
//						if (obj instanceof JSONObject)
//						{
//							if (((JSONObject) obj).isEmpty() || ((JSONObject) obj).isNullObject())
//							{
//								obj = null;
//							}
//						}
//						else if (obj instanceof JSONNull)
//						{
//							obj = null;
//						}
//
//						if (obj != null)
//						{
//							switch (dataType)
//							{
//								case Types.TIMESTAMP:
//									obj = DateUtil.parseTimestamp(obj.toString());
//									break;
//								case Types.DATE:
//									Date d = DateUtil.parseDate(obj.toString());
//
//									if (d != null)
//									{
//										obj = new java.sql.Date(d.getTime());
//									}
//									else
//									{
//										obj = null;
//									}
//									break;
//								case Types.TIME:
//									obj = DateUtil.parseTime(obj.toString());
//									break;
//								case Types.BIT:
//									if ("true".equalsIgnoreCase(obj.toString()) || "1".equals(obj.toString()))
//									{
//										obj = true;
//									}
//									else
//									{
//										obj = false;
//									}
//									break;
//								case Types.NUMERIC:
//								case Types.INTEGER:
//								case Types.DOUBLE:
//								case Types.REAL:
//								case Types.FLOAT:
//								case Types.BIGINT:
//								case Types.SMALLINT:
//									if (obj.toString().isEmpty())
//									{
//										obj = null;
//									}
//									else
//									{
//										try
//										{
//											obj = numberFormat.parse(obj.toString());
//										}
//										catch (Exception e)
//										{
//											obj = null;
//										}
//									}
//									break;
//								case Types.DISTINCT:
//									if ("datetime".equals(ii.split("@")[2]))
//									{
//										obj = DateUtil.parseTimestamp(obj.toString());
//									}
//									break;
//								default:
//									break;
//							}
//						}
//
//						arg0.setObject(index, obj);
//					}
//				}
//
//				@Override
//				public int getBatchSize()
//				{
//					return jsonList.size();
//				}
//			};
//
//			sql.append(column.substring(1) + " ) VALUES (" + value.substring(1) + ")");
//
//			this.jdbcTemplate.batchUpdate(sql.toString(), batchPreparedStatementSetter);
//
//			return ids.toArray();
//		}
//
//		return null;
		
		String sql =GenericDaoTableInfo.getBatchInsert(tenantId, tableName, this, jsonList);
		Object[] result = null;
		if(sql.indexOf("RETURNING id")> -1){
			List<JSONObject> list = query4Json(tenantId, sql);
			result = new Object[list.size()];
			int listsize = list.size();
			for(int i=0;i<listsize;i++)
			{
				result[i] = list.get(i).optInt("id");
			}	
		}else{
			execute(tenantId, sql);
			result = new Object[1];
			result[0] = 0;
		}
			
		return result;
	}

	public int updateIgnorCase(String tenantId, String tableName, final JSONObject json) throws Exception
	{
		// DBContextHolder.setTenancyid(tenantId);

//		DatabaseMetaData dbmd = null;
//
//		String idColumn = null;
//
//		Connection conn = this.jdbcTemplate.getDataSource().getConnection();
//
//		dbmd = conn.getMetaData();
//		ResultSet rs = dbmd.getPrimaryKeys(null, null, tableName);
//		if (rs.next())
//		{
//			idColumn = rs.getString(4);
//		}
//
//		MultiDataSourceManager.close(null, null, rs);
//
//		rs = dbmd.getColumns(null, null, tableName, null);
//
//		if (rs != null)
//		{
//			StringBuilder sql = new StringBuilder("UPDATE " + tableName + " SET ");
//
//			StringBuilder column = new StringBuilder();
//			StringBuilder where = new StringBuilder();
//
//			final Map<String, String> order = new HashMap<String, String>();
//
//			String idkey = "@";
//
//			while (rs.next())
//			{
//				String colname = rs.getString("COLUMN_NAME");
//				if (!(json.containsKey(colname)))
//				{
//					continue;
//				}
//
//				if (colname.equals(idColumn))
//				{
//					where.append(colname + "=?");
//					idkey = idkey + rs.getInt("DATA_TYPE");
//				}
//				else
//				{
//					column.append(colname + "=?, ");
//					order.put((order.size() + 1) + "@" + rs.getInt("DATA_TYPE") + "@" + rs.getString("TYPE_NAME"), colname);
//				}
//			}
//
//			idkey = (order.size() + 1) + idkey;
//
//			order.put(idkey, idColumn);
//
//			MultiDataSourceManager.close(conn, null, rs);
//
//			PreparedStatementSetter preparedStatementSetter = new PreparedStatementSetter()
//			{
//
//				@Override
//				public void setValues(PreparedStatement arg0) throws SQLException
//				{
//
//					for (String ii : order.keySet())
//					{
//						String columnName = order.get(ii);
//
//						Integer index = Integer.parseInt(ii.split("@")[0]);
//						Integer dataType = Integer.parseInt(ii.split("@")[1]);
//
//						Object obj = json.get(columnName);
//
//						if (obj == null)
//						{
//							obj = json.get(columnName.replace("_", ""));
//						}
//
//						if (obj instanceof JSONObject)
//						{
//							if (((JSONObject) obj).isEmpty() || ((JSONObject) obj).isNullObject())
//							{
//								obj = null;
//							}
//						}
//						else if (obj instanceof JSONNull)
//						{
//							obj = null;
//						}
//
//						if (obj != null)
//						{
//							switch (dataType)
//							{
//								case Types.TIMESTAMP:
//									obj = DateUtil.parseTimestamp(obj.toString());
//									break;
//								case Types.DATE:
//									Date d = DateUtil.parseDate(obj.toString());
//
//									if (d != null)
//									{
//										obj = new java.sql.Date(d.getTime());
//									}
//									else
//									{
//										obj = null;
//									}
//									break;
//								case Types.TIME:
//									obj = DateUtil.parseTime(obj.toString());
//									break;
//								case Types.BIT:
//									if ("true".equalsIgnoreCase(obj.toString()) || "1".equals(obj.toString()))
//									{
//										obj = true;
//									}
//									else
//									{
//										obj = false;
//									}
//									break;
//								case Types.NUMERIC:
//								case Types.INTEGER:
//								case Types.DOUBLE:
//								case Types.REAL:
//								case Types.FLOAT:
//								case Types.BIGINT:
//								case Types.SMALLINT:
//									if (obj.toString().isEmpty())
//									{
//										obj = null;
//									}
//									else
//									{
//										try
//										{
//											obj = numberFormat.parse(obj.toString());
//										}
//										catch (Exception e)
//										{
//											obj = null;
//										}
//									}
//									break;
//								case Types.DISTINCT:
//									if ("datetime".equals(ii.split("@")[2]))
//									{
//										obj = DateUtil.parseTimestamp(obj.toString());
//									}
//									break;
//								default:
//									break;
//							}
//						}
//
//						arg0.setObject(index, obj);
//					}
//				}
//			};
//
//			sql.append(column.toString().substring(0, column.toString().lastIndexOf(", ")) + " WHERE 1=1 AND " + where.toString());
//			return this.jdbcTemplate.update(sql.toString(), preparedStatementSetter);
//
//		}
//
//		return 0;
		String sql =GenericDaoTableInfo.updateById(tenantId, tableName, this, json);
		execute(tenantId, sql);
		return 1;
	}
	
	public int[] updateBatchIgnorCase(String tenantId, String tableName, final List<JSONObject> jsonList) throws Exception
	{
		// DBContextHolder.setTenancyid(tenantId);

//		DatabaseMetaData dbmd = null;
//
//		String idColumn = null;
//
//		Connection conn = this.jdbcTemplate.getDataSource().getConnection();
//
//		dbmd = conn.getMetaData();
//		ResultSet rs = dbmd.getPrimaryKeys(null, null, tableName);
//		if (rs.next())
//		{
//			idColumn = rs.getString(4);
//		}
//
//		MultiDataSourceManager.close(null, null, rs);
//
//		rs = dbmd.getColumns(null, null, tableName, null);
//
//		if (rs != null)
//		{
//			StringBuilder sql = new StringBuilder("UPDATE " + tableName + " SET ");
//
//			StringBuilder column = new StringBuilder();
//			StringBuilder where = new StringBuilder();
//
//			final Map<String, String> order = new HashMap<String, String>();
//
//			String idkey = "@";
//
//			while (rs.next())
//			{
//				String colname = rs.getString("COLUMN_NAME");
//				if (!(jsonList.get(0).containsKey(colname)))
//				{
//					continue;
//				}
//
//				if (colname.equals(idColumn))
//				{
//					where.append(colname + "=?");
//					idkey = idkey + rs.getInt("DATA_TYPE");
//				}
//				else
//				{
//					column.append(colname + "=?, ");
//					order.put((order.size() + 1) + "@" + rs.getInt("DATA_TYPE") + "@" + rs.getString("TYPE_NAME"), colname);
//				}
//			}
//
//			idkey = (order.size() + 1) + idkey;
//
//			order.put(idkey, idColumn);
//
//			MultiDataSourceManager.close(conn, null, rs);
//
//			BatchPreparedStatementSetter batchPreparedStatementSetter = new BatchPreparedStatementSetter()
//			{
//
//				@Override
//				public void setValues(PreparedStatement arg0, int arg1) throws SQLException
//				{
//					JSONObject json = jsonList.get(arg1);
//
//					for (String ii : order.keySet())
//					{
//						String columnName = order.get(ii);
//
//						Integer index = Integer.parseInt(ii.split("@")[0]);
//						Integer dataType = Integer.parseInt(ii.split("@")[1]);
//
//						Object obj = json.get(columnName);
//
//						if (obj == null)
//						{
//							obj = json.get(columnName.replace("_", ""));
//						}
//
//						if (obj instanceof JSONObject)
//						{
//							if (((JSONObject) obj).isEmpty() || ((JSONObject) obj).isNullObject())
//							{
//								obj = null;
//							}
//						}
//						else if (obj instanceof JSONNull)
//						{
//							obj = null;
//						}
//
//						if (obj != null)
//						{
//							switch (dataType)
//							{
//								case Types.TIMESTAMP:
//									obj = DateUtil.parseTimestamp(obj.toString());
//									break;
//								case Types.DATE:
//									Date d = DateUtil.parseDate(obj.toString());
//
//									if (d != null)
//									{
//										obj = new java.sql.Date(d.getTime());
//									}
//									else
//									{
//										obj = null;
//									}
//									break;
//								case Types.TIME:
//									obj = DateUtil.parseTime(obj.toString());
//									break;
//								case Types.BIT:
//									if ("true".equalsIgnoreCase(obj.toString()) || "1".equals(obj.toString()))
//									{
//										obj = true;
//									}
//									else
//									{
//										obj = false;
//									}
//									break;
//								case Types.NUMERIC:
//								case Types.INTEGER:
//								case Types.DOUBLE:
//								case Types.REAL:
//								case Types.FLOAT:
//								case Types.BIGINT:
//								case Types.SMALLINT:
//									if (obj.toString().isEmpty())
//									{
//										obj = null;
//									}
//									else
//									{
//										try
//										{
//											obj = numberFormat.parse(obj.toString());
//										}
//										catch (Exception e)
//										{
//											obj = null;
//										}
//									}
//									break;
//								case Types.DISTINCT:
//									if ("datetime".equals(ii.split("@")[2]))
//									{
//										obj = DateUtil.parseTimestamp(obj.toString());
//									}
//									break;
//								default:
//									break;
//							}
//						}
//
//						arg0.setObject(index, obj);
//					}
//				}
//
//				@Override
//				public int getBatchSize()
//				{
//					return jsonList.size();
//				}
//			};
//
//			sql.append(column.toString().substring(0, column.toString().lastIndexOf(", ")) + " WHERE 1=1 AND " + where.toString());
//			
//			return this.jdbcTemplate.batchUpdate(sql.toString(), batchPreparedStatementSetter);
//		}
//
//		return null;
		
		int[] result = new int[jsonList.size()];
		String sql =GenericDaoTableInfo.updateBatchById(tenantId, tableName, this, jsonList);
		execute(tenantId, sql);
		for(int i=0;i<jsonList.size();i++)
		{
			result[i]=1;
		}
		return result;
	}

	/**
	 * delete
	 * 
	 */
	public boolean delete(String tenantId, String tableName, final Object id) throws Exception
	{
		// DBContextHolder.setTenancyid(tenantId);

//		DatabaseMetaData dbmd = null;
//
//		String idColumn = null;
//
//		Connection conn = this.jdbcTemplate.getDataSource().getConnection();
//
//		dbmd = conn.getMetaData();
//		ResultSet rs = dbmd.getPrimaryKeys(null, null, tableName);
//		if (rs.next())
//		{
//			idColumn = rs.getString(4);
//		}
//
//		MultiDataSourceManager.close(conn, null, rs);
//
//		if (id != null)
//		{
//			String sql = "DELETE FROM " + tableName + " WHERE " + idColumn + "=?";
//
//			PreparedStatementSetter p = new PreparedStatementSetter()
//			{
//				@Override
//				public void setValues(PreparedStatement arg0) throws SQLException
//				{
//					arg0.setObject(1, id);
//				}
//			};
//
//			this.jdbcTemplate.update(sql, p);
//		}
//		else
//		{
//			String sql = "DELETE FROM " + tableName;
//
//			this.jdbcTemplate.update(sql);
//		}
		String idColumn = GenericDaoTableInfo.getPkKey(tenantId, tableName, this);
		String sql = "DELETE FROM " + tableName + " WHERE " + idColumn + "="+id;
		execute(tenantId, sql);
		return true;
	}
	
	public int[] deleteBatchIgnorCase(String tenantId, String tableName, final List<JSONObject> jsonList) throws Exception
	{
		// DBContextHolder.setTenancyid(tenantId);

		DatabaseMetaData dbmd = null;

		Connection conn = this.jdbcTemplate.getDataSource().getConnection();

		dbmd = conn.getMetaData();

		ResultSet rs = dbmd.getColumns(null, null, tableName, null);

		if (rs != null)
		{
			StringBuilder sql = new StringBuilder("DELETE FROM " + tableName);

			StringBuilder column = new StringBuilder();

			final Map<String, String> order = new HashMap<String, String>();
			
			
			JSONObject temp = jsonList.get(0);
			while (rs.next())
			{
				String colname = rs.getString("COLUMN_NAME");

				if (!temp.containsKey(colname))
				{
					continue;
				}

				column.append(" AND " + colname + "=? ");
				order.put((order.size() + 1) + "@" + rs.getInt("DATA_TYPE") + "@" + rs.getString("TYPE_NAME"), colname);
			}

			MultiDataSourceManager.close(conn, null, rs);

			BatchPreparedStatementSetter batchPreparedStatementSetter = new BatchPreparedStatementSetter()
			{

				@Override
				public void setValues(PreparedStatement arg0, int arg1) throws SQLException
				{
					JSONObject json = jsonList.get(arg1);

					for (String ii : order.keySet())
					{
						String columnName = order.get(ii);

						Integer index = Integer.parseInt(ii.split("@")[0]);
						Integer dataType = Integer.parseInt(ii.split("@")[1]);

						Object obj = json.get(columnName);

						if (obj == null)
						{
							obj = json.get(columnName.replace("_", ""));
						}

						if (obj instanceof JSONObject)
						{
							if (((JSONObject) obj).isEmpty() || ((JSONObject) obj).isNullObject())
							{
								obj = null;
							}
						}
						else if (obj instanceof JSONNull)
						{
							obj = null;
						}

						if (obj != null)
						{
							switch (dataType)
							{
								case Types.TIMESTAMP:
									obj = DateUtil.parseTimestamp(obj.toString());
									break;
								case Types.DATE:
									Date d = DateUtil.parseDate(obj.toString());

									if (d != null)
									{
										obj = new java.sql.Date(d.getTime());
									}
									else
									{
										obj = null;
									}
									break;
								case Types.TIME:
									obj = DateUtil.parseTime(obj.toString());
									break;
								case Types.BIT:
									if ("true".equalsIgnoreCase(obj.toString()) || "1".equals(obj.toString()))
									{
										obj = true;
									}
									else
									{
										obj = false;
									}
									break;
								case Types.NUMERIC:
								case Types.INTEGER:
								case Types.DOUBLE:
								case Types.REAL:
								case Types.FLOAT:
								case Types.BIGINT:
								case Types.SMALLINT:
									if (obj.toString().isEmpty())
									{
										obj = null;
									}
									else
									{
										try
										{
											obj = numberFormat.parse(obj.toString());
										}
										catch (Exception e)
										{
											obj = null;
										}
									}
									break;
								case Types.DISTINCT:
									if ("datetime".equals(ii.split("@")[2]))
									{
										obj = DateUtil.parseTimestamp(obj.toString());
									}
									break;
								default:
									break;
							}
						}

						arg0.setObject(index, obj);
					}
				}

				@Override
				public int getBatchSize()
				{
					return jsonList.size();
				}
			};

			if (column.length() > 0)
			{
				sql.append(" WHERE 1=1 " + column.toString());
				return this.jdbcTemplate.batchUpdate(sql.toString(), batchPreparedStatementSetter);
			}
		}

		return null;

	}

	@SuppressWarnings(
	{ "unchecked", "rawtypes" })
	public Object callProduce(String tenantId, final String procString, final List<Object> inParams, final List<String> outParamTypes) throws Exception
	{
		// DBContextHolder.setTenancyid(tenantId);

		return this.jdbcTemplate.execute(new CallableStatementCreator()
		{
			public CallableStatement createCallableStatement(Connection con) throws SQLException
			{
				CallableStatement cstmt = con.prepareCall(procString);
				Object obj = null;
				int j = 0;
				if ((inParams != null) && (inParams.size() > 0))
				{// 设置输入参数的值
					for (int i = 0; i < inParams.size(); ++i)
					{
						obj = inParams.get(i);
						if (obj instanceof Integer)
						{
							cstmt.setInt(i + 1, ((Integer) obj).intValue());
						}
						else if (obj instanceof String)
						{
							cstmt.setString(i + 1, (String) obj);
						}
						else if (obj instanceof Double)
						{
							cstmt.setDouble(i + 1, ((Double) obj).doubleValue());
						}
						else if (obj instanceof Float)
						{
							cstmt.setFloat(i + 1, ((Float) obj).floatValue());
						}
						else if (obj instanceof Long)
						{
							cstmt.setLong(i + 1, ((Long) obj).longValue());
						}
						else if (obj instanceof java.sql.Date)
						{
							cstmt.setDate(i + 1, (java.sql.Date) obj);
						}
					}

					j = inParams.size();
				}
				if ((outParamTypes != null) && (outParamTypes.size() > 0))
				{ // 注册输出参数的类型
					for (int i = 0; i < outParamTypes.size(); i++)
					{
						String outParamType = (String) outParamTypes.get(i);
						if (outParamType.indexOf("int") != -1)
						{
							cstmt.registerOutParameter(j + 1, 4);
						}
						else if (outParamType.indexOf("string") != -1)
						{
							cstmt.registerOutParameter(j + 1, 12);
						}
						else if (outParamType.indexOf("double") != -1)
						{
							cstmt.registerOutParameter(j + 1, 8);
						}
						else if (outParamType.indexOf("float") != -1)
						{
							cstmt.registerOutParameter(j + 1, 6);
						}
						else if (outParamType.indexOf("long") != -1)
						{
							cstmt.registerOutParameter(j + 1, -1);
						}
						j++;
					}
				}
				return cstmt;
			}
		}, new CallableStatementCallback()
		{
			public Object doInCallableStatement(CallableStatement cstmt) throws SQLException, DataAccessException
			{
				cstmt.execute();
				Object obj = null;
				if (outParamTypes != null && (outParamTypes.size() > 0))
				{
					int index = 0;
					if (inParams != null)
					{
						index = inParams.size() + outParamTypes.size();
					}
					String outParamType = (String) outParamTypes.get(outParamTypes.size() - 1);
					if (outParamType.indexOf("int") != -1)
					{
						obj = cstmt.getInt(index);
					}
					else if (outParamType.indexOf("string") != -1)
					{
						obj = cstmt.getString(index);
					}
					else if (outParamType.indexOf("double") != -1)
					{
						obj = cstmt.getDouble(index);
					}
					else if (outParamType.indexOf("float") != -1)
					{
						obj = cstmt.getFloat(index);
					}
					else if (outParamType.indexOf("long") != -1)
					{
						obj = cstmt.getLong(index);
					}
				}
				return obj;
			}
		});
	}

	public long countSql(String tenantId, String sql) throws Exception
	{
		// DBContextHolder.setTenancyid(tenantId);

		String count = "SELECT COUNT(C.*) FROM (" + sql + ") C";
		SqlRowSet srs = this.jdbcTemplate.queryForRowSet(count);
		// SqlRowSet srs = this.jdbcTemplate.queryForRowSet(sql);

		if (srs != null && srs.next())
		{
			return srs.getLong(1);
		}

		return 0l;
	}

	@Override
	public String buildPageSql(JSONObject condition, String sql) throws Exception
	{
		int pagenum = condition.containsKey("page") ? (condition.optInt("page") == 0 ? 1 : condition.optInt("page")) : 1;
		int limit = condition.containsKey("rows") ? condition.optInt("rows") : 10;
		String sort = condition.optString("sort");
		String order = condition.optString("order");
		if(!"".equals(condition.optString("sort")))
		{
			if("desc".equalsIgnoreCase(order))
			{
				return "select * from (" + sql + " order by " + sort + " desc) datasql limit " + limit + " offset " + (limit * (pagenum - 1));
			}
			else
			{
				return "select * from (" + sql + " order by " + sort + ") datasql limit " + limit + " offset " + (limit * (pagenum - 1));
			}
		}
		else
		{
			//return "select * from (" + sql + ") datasql limit " + limit + " offset " + (limit * (pagenum - 1));
			return "select * from (" + sql + ") datasql order by datasql.id desc limit " + limit + " offset " + (limit * (pagenum - 1));
		}
		
	}
	
	@Override
	public String multiSortPageSql(JSONObject condition, String sql) throws Exception
	{
		int pagenum = condition.containsKey("page") ? (condition.optInt("page") == 0 ? 1 : condition.optInt("page")) : 1;
		int limit = condition.containsKey("rows") ? condition.optInt("rows") : 10;
		String sort = condition.optString("sort");
		String order = condition.optString("order");
		//是否【分页】  不传值为false 即默认分页
		boolean nonpage = condition.optBoolean("nonpage");
		StringBuilder resultSql = new StringBuilder("select * from (" + sql );
		if(!"".equals(condition.optString("sort")))
		{
			String[] mulSort = sort.split(",");
			String[] mulOrder = order.split(",");
			resultSql.append(" order by ");
			for(int i=0; i<mulSort.length; i++)
			{
				if("desc".equalsIgnoreCase(order))
				{
					//单/多列【倒序】
					resultSql.append(mulSort[i] + " desc ");
				}
				else if("asc".equalsIgnoreCase(order) || "".equalsIgnoreCase(order))
				{
					//单/多列【正序】
					resultSql.append(mulSort[i] + " asc ");
				}
				else
				{
					//多列【多序】
					if(mulSort.length==mulOrder.length)
					{
						resultSql.append(mulSort[i] + " " + mulOrder[i] + ",");
					}
					else
					{
						resultSql.append(mulSort[i] + " " + mulOrder[0] + ",");
					}
				}
			}
			resultSql.setLength(resultSql.length()-1);
			resultSql.append(") datasql ");
		}
		else
		{
			//默认按第一列正序排列
			resultSql.append(") datasql order by 1 asc ");
		}
		if(!nonpage)
		{
			resultSql.append(" limit " + limit + " offset " + (limit * (pagenum - 1)));
		}
		return resultSql.toString();
	}

}
