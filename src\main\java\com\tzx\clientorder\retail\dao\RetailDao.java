package com.tzx.clientorder.retail.dao;

import java.util.List;

import net.sf.json.JSONObject;

public abstract interface RetailDao
{
	public static final String NAME = "com.tzx.clientorder.retail.dao.impl.RetailDaoImpl";

	/**
	 * @param pageParams
	 * @param tenancyId
	 * @param storeId
	 * @param channel
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getItemInfos(JSONObject pageParams, String tenancyId, String storeId, String channel) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param channel
	 * @return
	 * @throws Exception
	 */
	public long getItemInfoCount(String tenancyId, String storeId, String channel) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getShops(String tenancyId, String storeId) throws Exception;

	/**
	 * @param tenancy_id
	 * @param item_ids
	 * @param price_system
	 * @param channel
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getUnitInfos(String tenancy_id, int[] item_ids, String price_system, String channel) throws Exception;

	/**
	 * @param tenancy_id
	 * @param item_ids
	 * @param storeId
	 * @param channel
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getMethodInfos(String tenancy_id, int[] item_ids, String storeId, String channel) throws Exception;

	/**
	 * @param tenancy_id
	 * @param combo_item_ids
	 * @param price_system
	 * @param channel
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getComboBaseInfo(String tenancy_id, int[] combo_item_ids, String price_system, String channel) throws Exception;

	/**
	 * @param tenancy_id
	 * @param combo_item_ids
	 * @param channel
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> selectDetailsInfo(String tenancy_id, int[] combo_item_ids, String channel) throws Exception;

	/**
	 * @param teancyId
	 * @param deliverInfo
	 * @throws Exception
	 */
	public void saveDeliveInfo(String teancyId, JSONObject deliverInfo) throws Exception;

}