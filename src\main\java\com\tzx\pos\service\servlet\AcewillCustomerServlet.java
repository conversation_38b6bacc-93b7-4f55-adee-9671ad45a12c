package com.tzx.pos.service.servlet;

import com.tzx.framework.common.entity.Data;

public interface AcewillCustomerServlet
{
	String	NAME	= "com.tzx.pos.service.servlet.imp.AcewillCustomerServletImp";
	
	/**会员查询
	 * @param paramData
	 * @return
	 * @throws Exception
	 */
	public Data queryAcewillCustomerInfo(Data paramData) throws Exception;

	
	/**会员消费预览
	 * @param paramData
	 * @return
	 * @throws Exception
	 */
	public Data billPaymentForAcewillPreview(Data paramData) throws Exception;
	
	/**会员消费提交
	 * @param paramData
	 * @return
	 * @throws Exception
	 */
	public Data billPaymentForAcewillCommit(Data paramData) throws Exception;
	
	/** 优惠劵验证
	 * @param paramData
	 * @return
	 * @throws Exception
	 */
	public Data checkAcewillCoupons(Data paramData) throws Exception;
	
	/**
	 * @param requestData
	 * @return
	 * @throws Exception
	 */
	public Data sendCodeAcewillCustomer(Data requestData) throws Exception;


	/**
	 * ========豪享来对接微生活会员======
	 */
	/**
	 * 修改会员信息
	 *
	 * @param requestData
	 * @return
	 * @throws Exception
	 */
	public Data editAcewillCustomerUserInfo(Data requestData) throws Exception;


	/**
	 * 查询微生活基础数据
	 *
	 * @param requestData
	 * @return
	 * @throws Exception
	 */
	public Data queryAcewillBasicData(Data requestData) throws Exception;


	/**
	 * 储值预览
	 *
	 * @param requestData
	 * @return
	 * @throws Exception
	 */
	public Data previewAcewillCharge(Data requestData) throws Exception;


	/**
	 * 微生活会员储值提交
	 *
	 * @param requestData
	 * @return
	 * @throws Exception
	 */
	public Data commitAcewillCharge(Data requestData) throws Exception;

	/**
	 * 获取二维码
	 *
	 * @param tenantId
	 * @param storeId
	 * @param paramJson
	 * @param path
	 * @param resultData
	 * @throws Exception
	 */
	public Data precreatePaymentForRecharge(Data paramData, String path) throws Exception;


	/**
	 * 扫码支付
	 *
	 * @param tenantId
	 * @param storeId
	 * @param paramJson
	 * @param resultData
	 * @throws Exception
	 */
	public Data barcodePaymentForRecharge(Data paramData) throws Exception;


	/**
	 * 查询充值状态
	 *
	 * @param tenantId
	 * @param storeId
	 * @param paramList
	 * @param resultData
	 * @throws Exception
	 */
	public Data queryCustomerCardRechargeForState(Data paramData) throws Exception;


	/**
	 * 取消付款
	 *
	 * @param paramData
	 * @return
	 * @throws Exception
	 */
	public Data cancelAcewillCharge(Data paramData) throws Exception;


	/**
	 * 查询会员储值列表
	 *
	 * @param paramData
	 * @return
	 * @throws Exception
	 */
	public Data findAcewillChargeUser(Data paramData) throws Exception;


	/**
	 * 查询会员消费列表
	 *
	 * @param paramData
	 * @return
	 * @throws Exception
	 */
	public Data findAcewillConsumeUser(Data paramData) throws Exception;


	/**
	 * 微生活储值成功后 撤销充值
	 *
	 * @param paramData
	 * @return
	 * @throws Exception
	 */
	public Data revokeAcewillCharge(Data paramData) throws Exception;


	/**
	 * 微生活积分兑换规则
	 *
	 * @param requestData
	 * @return
	 * @throws Exception
	 */
	public Data findAcewillCreditRule(Data requestData) throws Exception;


	/**
	 * 微生活积分换礼
	 *
	 * @param requestData
	 * @return
	 * @throws Exception
	 */
	public Data creditExchangeGifts(Data requestData) throws Exception;


	/**
	 * 微生活会员等级
	 *
	 * @param requestData
	 * @return
	 * @throws Exception
	 */
	public Data findAcewillCustomerGrade(Data requestData) throws Exception;


	/**
	 * 微生活制卡
	 * @param requestData
	 * @return
	 * @throws Exception
	 */
	public Data makeAcewillCustomerCard(Data requestData) throws Exception;


	/**
	 * 微生活验证实体卡
	 * @param requestData
	 * @return
	 * @throws Exception
	 */
	public Data checkAcewillCustomerCard(Data requestData) throws Exception;


	/**
	 * 会员卡激活（开卡）
	 * @param requestData
	 * @return
	 * @throws Exception
	 */
	public Data activatedAcewillCustomerCard(Data requestData) throws Exception;


	/**
	 * 补打消费小票
	 */
	public Data makeUpCustomerCardConsumePrint(Data requestData) throws Exception;

}
