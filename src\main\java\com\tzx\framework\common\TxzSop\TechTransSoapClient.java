package com.tzx.framework.common.TxzSop;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.List;
import net.sf.json.JSONObject;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.methods.InputStreamRequestEntity;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.methods.RequestEntity;
import org.apache.log4j.Logger;
import com.tzx.framework.common.util.DateUtil;


public class TechTransSoapClient {
	private static final Logger				logger	= Logger.getLogger(TechTransSoapClient.class);

	private static String HEADER = "<?xml version=\"1.0\" encoding=\"utf-8\"?>	<soap12:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soap12=\"http://www.w3.org/2003/05/soap-envelope\"><soap12:Body>";

//	public static void main(String[] args) throws Exception
//	{
//		
//		String licensekey = "";
//		String username = "010201";
//		String password = "010201";
//		String store_code = "A00001";
//		String mallid = "5012"; 
//		StringBuilder sb = new StringBuilder();
//		String organ_code ="123";
//		JSONObject bill_info = new JSONObject();
//		
//		
//		bill_info.put("bill_num","00005");
//		bill_info.put("serial_num","123");
//		bill_info.put("report_date","2016-09-20");
//		bill_info.put("service_amount",1);
//		bill_info.put("subtotal",0.1);
//		bill_info.put("bill_amount",0.1);
//		bill_info.put("yyyymmdd","20160920");
//		bill_info.put("hhmmss","184611");
//		bill_info.put("salestype","SA");
//		List<JSONObject> item_list = new ArrayList<JSONObject>();
//		JSONObject item = new JSONObject();
//		item.put("item_amount",0.1);
//		item.put("item_count",1);
//		item.put("code","A000011");
//		item_list.add(item);
//		List<JSONObject> payment_list  = new ArrayList<JSONObject>();
//		JSONObject pay = new JSONObject();
//		pay.put("pay_code", "CH");
//		pay.put("amount", 0.1);
//		payment_list.add(pay);
//		int code = postdailysalesestimatecreate2("http://180.166.29.82:8080/TTPOS/sales.asmx",licensekey,username, password,store_code,mallid, sb, organ_code,bill_info,item_list, payment_list);
//		//404 url
//		System.out.println(code);
//	}
	
	public static int postdailysalesestimatecreate(String url,String licensekey,String username,String password,String store_code,String mallid,StringBuilder sb,String organ_code,JSONObject bill_info,List<JSONObject> item_list,List<JSONObject> payment_list) throws Exception
	{
		return postdailysalesestimatecreate2(url, licensekey, username, password, store_code, mallid, sb, organ_code, bill_info, item_list, payment_list);
//		sb.setLength(0);
//		sb.append(HEADER);
//		sb.append("<postsalescreate xmlns=\"http://tempurl.org\">");
//		sb.append("<astr_request>");
//		sb.append("<header>");
//      	//--表头信息--
//		//许可证
//		sb.append("<licensekey>"+licensekey+"</licensekey>");
//		sb.append("<username>"+username+"</username>");
//        sb.append("<password>"+password+"</password>");
//        sb.append("<lang></lang>");
//        sb.append("<pagerecords>100</pagerecords>");
//        sb.append("<pageno>1</pageno>");
//        sb.append("<updatecount>0</updatecount>");
//        sb.append("<messagetype>SALESDATA</messagetype>");
//        sb.append("<messageid>332</messageid>");
//        sb.append("<version>V332M</version>");
//        sb.append("</header>");
//        //select bill_num,serial_num,report_date,service_amount,subtotal,bill_amount,to_char(payment_time,'yyyymmdd') as yyyymmdd,to_char(payment_time,'HH24mmss') as hhmmss,(CASE when bill_amount<0 then 'SR' else 'SA' end) as salestype from pos_bill2 where bill_num='12920151209000121'
//	   
//        Double bill_amount = bill_info.optDouble("service_amount",0.0);
//        //double service_amount = 
//        //System.out.println(payment_list.size());
//        for(JSONObject jo:payment_list)
//        {
//        	bill_amount += jo.optDouble("amount",0.0);
//        	//System.out.println(jo.optDouble("amount",0.0));
//        }
//        //System.out.println(bill_amount);
//        //销售单主表
//	    sb.append("<salestotal>");
//	    sb.append("<localstorecode>"+organ_code+"</localstorecode>");
//	    sb.append("<reservedocno>"+bill_info.optString("bill_num")+"</reservedocno>");
//        sb.append("<txdate_yyyymmdd>"+bill_info.optString("yyyymmdd")+"</txdate_yyyymmdd>");
//        sb.append("<txtime_hhmmss>"+bill_info.optString("hhmmss")+"</txtime_hhmmss>");
//        sb.append("<mallid>"+mallid+"</mallid>");
//        sb.append("<storecode>"+store_code+"</storecode>");
//        sb.append("<tillid>01</tillid>");
//        //SA SR
//        sb.append("<salestype>"+bill_info.optString("salestype")+"</salestype>");
//        sb.append("<txdocno>"+bill_info.optString("bill_num")+"</txdocno>");
//        sb.append("<orgtxdate_yyyymmdd></orgtxdate_yyyymmdd>");
//        sb.append("<orgstorecode></orgstorecode>");
//        sb.append("<orgtillid></orgtillid>");
//        sb.append("<txorgdocno></txorgdocno>");
//        sb.append("<mallitemcode>"+bill_info.optString("bill_num")+"</mallitemcode>");
//        sb.append("<cashier>"+bill_info.optString("serial_num")+"</cashier>");
//        sb.append("<vipcode></vipcode>");
//        sb.append("<salesman></salesman>");
//        sb.append("<demographiccode></demographiccode>");
//        sb.append("<demographicdata></demographicdata>");
//        sb.append("<netqty>"+bill_info.optInt("num")+"</netqty>");
//        //原始金额
//        sb.append("<originalamount>"+bill_info.optDouble("subtotal",0.0)+"</originalamount>");
//        //销售金额
//        sb.append("<sellingamount>"+bill_amount+"</sellingamount>");
//
//        sb.append("<couponnumber></couponnumber>");
//        sb.append("<coupongroup></coupongroup>");
//        sb.append("<coupontype></coupontype>");  
//        sb.append("<couponqty>0</couponqty>");
//        sb.append("<totaldiscount>");
//        sb.append("<salesdiscount xsi:nil=\"true\" />");
//        sb.append("</totaldiscount>");
//        sb.append("<ttltaxamount1>0</ttltaxamount1>");
//        sb.append("<ttltaxamount2>0</ttltaxamount2>");
//       
//        //净金额
//        sb.append("<netamount>"+bill_amount+"</netamount>"); 
//        sb.append("<paidamount>"+bill_amount+"</paidamount>");
//        sb.append("<changeamount>0</changeamount>");
//        sb.append("<priceincludetax></priceincludetax>");
//        sb.append("<shoptaxgroup></shoptaxgroup>");
//        sb.append("<extendparam></extendparam>");
//        sb.append("<invoicetitle></invoicetitle>");
//        sb.append("<invoicecontent></invoicecontent>");
//        
//        sb.append("<issueby>admin</issueby>");
//        sb.append("<issuedate_yyyymmdd>"+DateUtil.getyyyymmdd()+"</issuedate_yyyymmdd>");
//        sb.append("<issuetime_hhmmss>"+DateUtil.gethhmmss()+"</issuetime_hhmmss>");
//        //DateUtil.getNowDateYYDDMMHHMMSS()
//        sb.append("<ecorderno></ecorderno>");
//        sb.append("<buyerremark></buyerremark>");
//        sb.append("<orderremark></orderremark>");
//        sb.append("<status></status>");
//        sb.append("<ttpossalesdocno></ttpossalesdocno>");
//        sb.append("</salestotal>");
//
//        //SalesItem:销售单货品明细表
//        sb.append("<salesitems>");
//        int lineno2 = 0;
//        double service_amount = bill_info.optDouble("service_amount",0.0);
//        if(service_amount!=0)
//        {
//        	lineno2 ++;
//        	sb.append("<salesitem>");
//    		sb.append("<iscounteritemcode>1</iscounteritemcode>");
//            sb.append("<lineno>"+lineno2+"</lineno>");
//            sb.append("<storecode>"+store_code+"</storecode>");
//            sb.append("<mallitemcode>A000011</mallitemcode>");
//            sb.append("<counteritemcode>A000011</counteritemcode>");
//            sb.append("<itemcode>A000011</itemcode>");
//            sb.append("<plucode>A000011</plucode>");
//            sb.append("<colorcode></colorcode>");
//            sb.append("<sizecode></sizecode>");
//            sb.append("<itemlotnum></itemlotnum>");
//            sb.append("<serialnum></serialnum>");
//            sb.append("<isdeposit></isdeposit>");
//            sb.append("<iswholesale></iswholesale>");
//            sb.append("<invttype>1</invttype>");
//            sb.append("<qty>1</qty>");
//            sb.append("<exstk2sales>1</exstk2sales>");
//            sb.append("<originalprice>0</originalprice>");
//            sb.append("<sellingprice>0</sellingprice>");
//            sb.append("<pricemode></pricemode>");
//            sb.append("<priceapprove></priceapprove>");
//            sb.append("<couponnumber></couponnumber>");
//            sb.append("<coupongroup></coupongroup>");
//            sb.append("<coupontype></coupontype>");
//            sb.append("<itemdiscount xsi:nil=\"true\" />");
//            sb.append("<vipdiscountpercent>0</vipdiscountpercent>");
//            sb.append("<vipdiscountless>0</vipdiscountless>");
//            sb.append("<promotion xsi:nil=\"true\" />");
//            sb.append("<totaldiscountless1>0</totaldiscountless1>");
//            sb.append("<totaldiscountless2>0</totaldiscountless2>");
//            sb.append("<totaldiscountless>0</totaldiscountless>");
//            sb.append("<tax xsi:nil=\"true\" />");
//            sb.append("<netamount>"+service_amount+"</netamount>");
//            sb.append("<bonusearn>0</bonusearn>");
//            sb.append("<salesitemremark></salesitemremark>");
//            sb.append("<refundreasoncode></refundreasoncode>");
//            sb.append("<extendparam></extendparam>");
//            sb.append("</salesitem>");        	
//        	
//        }
//
//        for(JSONObject jo : item_list)
//        {
//        	lineno2 ++;
//        	sb.append("<salesitem>");
//    		sb.append("<iscounteritemcode>1</iscounteritemcode>");
//            sb.append("<lineno>"+lineno2+"</lineno>");
//            sb.append("<storecode>"+store_code+"</storecode>");
//            sb.append("<mallitemcode>A000011</mallitemcode>");
//            sb.append("<counteritemcode>A000011</counteritemcode>");
//            sb.append("<itemcode>A000011</itemcode>");
//            sb.append("<plucode>A000011</plucode>");
//            sb.append("<colorcode></colorcode>");
//            sb.append("<sizecode></sizecode>");
//            sb.append("<itemlotnum></itemlotnum>");
//            sb.append("<serialnum></serialnum>");
//            sb.append("<isdeposit></isdeposit>");
//            sb.append("<iswholesale></iswholesale>");
//            sb.append("<invttype>1</invttype>");
//            sb.append("<qty>"+jo.optInt("item_count")+"</qty>");
//            sb.append("<exstk2sales>1</exstk2sales>");
//            sb.append("<originalprice>0</originalprice>");
//            sb.append("<sellingprice>0</sellingprice>");
//            sb.append("<pricemode></pricemode>");
//            sb.append("<priceapprove></priceapprove>");
//            sb.append("<couponnumber></couponnumber>");
//            sb.append("<coupongroup></coupongroup>");
//            sb.append("<coupontype></coupontype>");
//            sb.append("<itemdiscount xsi:nil=\"true\" />");
//            sb.append("<vipdiscountpercent>0</vipdiscountpercent>");
//            sb.append("<vipdiscountless>0</vipdiscountless>");
//            sb.append("<promotion xsi:nil=\"true\" />");
//            sb.append("<totaldiscountless1>0</totaldiscountless1>");
//            sb.append("<totaldiscountless2>0</totaldiscountless2>");
//            sb.append("<totaldiscountless>0</totaldiscountless>");
//            sb.append("<tax xsi:nil=\"true\" />");
//            sb.append("<netamount>"+jo.optDouble("item_amount",0.0)+"</netamount>");
//            sb.append("<bonusearn>0</bonusearn>");
//            sb.append("<salesitemremark></salesitemremark>");
//            sb.append("<refundreasoncode></refundreasoncode>");
//            sb.append("<extendparam></extendparam>");
//            sb.append("</salesitem>");        	
//        }
//       
//        System.out.println(sb.toString());
//        
//        if(item_list.size()==0 && service_amount==0)
//        {
//        	lineno2 ++;
//        	sb.append("<salesitem>");
//    		sb.append("<iscounteritemcode>1</iscounteritemcode>");
//            sb.append("<lineno>"+lineno2+"</lineno>");
//            sb.append("<storecode>"+store_code+"</storecode>");
//            sb.append("<mallitemcode>A000011</mallitemcode>");
//            sb.append("<counteritemcode>A000011</counteritemcode>");
//            sb.append("<itemcode>A000011</itemcode>");
//            sb.append("<plucode>A000011</plucode>");
//            sb.append("<colorcode></colorcode>");
//            sb.append("<sizecode></sizecode>");
//            sb.append("<itemlotnum></itemlotnum>");
//            sb.append("<serialnum></serialnum>");
//            sb.append("<isdeposit></isdeposit>");
//            sb.append("<iswholesale></iswholesale>");
//            sb.append("<invttype>1</invttype>");
//            sb.append("<qty>1</qty>");
//            sb.append("<exstk2sales>1</exstk2sales>");
//            sb.append("<originalprice>0</originalprice>");
//            sb.append("<sellingprice>0</sellingprice>");
//            sb.append("<pricemode></pricemode>");
//            sb.append("<priceapprove></priceapprove>");
//            sb.append("<couponnumber></couponnumber>");
//            sb.append("<coupongroup></coupongroup>");
//            sb.append("<coupontype></coupontype>");
//            sb.append("<itemdiscount xsi:nil=\"true\" />");
//            sb.append("<vipdiscountpercent>0</vipdiscountpercent>");
//            sb.append("<vipdiscountless>0</vipdiscountless>");
//            sb.append("<promotion xsi:nil=\"true\" />");
//            sb.append("<totaldiscountless1>0</totaldiscountless1>");
//            sb.append("<totaldiscountless2>0</totaldiscountless2>");
//            sb.append("<totaldiscountless>0</totaldiscountless>");
//            sb.append("<tax xsi:nil=\"true\" />");
//            sb.append("<netamount>"+ bill_amount+"</netamount>");
//            sb.append("<bonusearn>0</bonusearn>");
//            sb.append("<salesitemremark></salesitemremark>");
//            sb.append("<refundreasoncode></refundreasoncode>");
//            sb.append("<extendparam></extendparam>");
//            sb.append("</salesitem>");    
//        }
//
//        sb.append("</salesitems>");
//        
//        // -- 销售单付款明细表 --   
//        sb.append("<salestenders>");
//        int lineno = 0;
//        Double paymentam = 0.0;
//       
//        
//        for(JSONObject jo:payment_list)
//        {
//            sb.append("<salestender>");
//          	//行号
//            lineno ++;
//            sb.append("<lineno>"+lineno+"</lineno>");
//            //付款代码 CH----现金 CI----国内银行卡 CO----国外银行卡 OT-----其他付款方式
//            sb.append("<tendercode>"+jo.optString("pay_code")+"</tendercode>");
//            //付款类型 *保留
//            sb.append("<tendertype>0</tendertype>");
//            //付款种类  *保留 0
//            sb.append("<tendercategory>0</tendercategory>");
//            Double am = jo.optDouble("amount",0.0);
//            paymentam += am;
//            //付款金额
//            sb.append("<payamount>"+am+"</payamount>");
//            //本位币金额 同上
//            sb.append("<baseamount>"+am+"</baseamount>");
//            //超额金额  *保留 0
//            sb.append("<excessamount>0</excessamount>");
//            //扩展参数 *保留
//            sb.append("<extendparam></extendparam>");
//            //备注 *保留
//            sb.append("<remark></remark>");
//            sb.append("</salestender>");	
//        }
//        if(payment_list.size()==0)
//        {
//            sb.append("<salestender>");
//          	//行号
//            lineno ++;
//            sb.append("<lineno>"+lineno+"</lineno>");
//            //付款代码 CH----现金 CI----国内银行卡 CO----国外银行卡 OT-----其他付款方式
//            sb.append("<tendercode>OT</tendercode>");
//            //付款类型 *保留
//            sb.append("<tendertype>0</tendertype>");
//            //付款种类  *保留 0
//            sb.append("<tendercategory>0</tendercategory>");
//            //付款金额
//            paymentam += bill_amount;
//            sb.append("<payamount>"+bill_amount+"</payamount>");
//            //本位币金额 同上
//            sb.append("<baseamount>"+bill_amount+"</baseamount>");
//            //超额金额  *保留 0
//            sb.append("<excessamount>0</excessamount>");
//            //扩展参数 *保留
//            sb.append("<extendparam></extendparam>");
//            //备注 *保留
//            sb.append("<remark></remark>");
//            sb.append("</salestender>");	
//        }
////        System.out.println(paymentam);
////        System.out.println(bill_amount);
//        if(paymentam!=bill_amount)
//        {
//        	 sb.append("<salestender>");
//           	//行号
//             lineno ++;
//             sb.append("<lineno>"+lineno+"</lineno>");
//             //付款代码 CH----现金 CI----国内银行卡 CO----国外银行卡 OT-----其他付款方式
//             sb.append("<tendercode>OT</tendercode>");
//             //付款类型 *保留
//             sb.append("<tendertype>0</tendertype>");
//             //付款种类  *保留 0
//             sb.append("<tendercategory>0</tendercategory>");
//             //付款金额
//             sb.append("<payamount>"+(bill_amount-paymentam)+"</payamount>");
//             //本位币金额 同上
//             sb.append("<baseamount>"+(bill_amount-paymentam)+"</baseamount>");
//             //超额金额  *保留 0
//             sb.append("<excessamount>0</excessamount>");
//             //扩展参数 *保留
//             sb.append("<extendparam></extendparam>");
//             //备注 *保留
//             sb.append("<remark></remark>");
//             sb.append("</salestender>");	
//        }
//        
//        sb.append("</salestenders>");
//
//        //--配送相关 --  *保留
//        sb.append("<salesdelivery>");
//        //收货人姓名*保留
//        sb.append("<receiver_name></receiver_name>");
//        //收货人所在国家*保留
//        sb.append("<receiver_country></receiver_country>");
//        //收货人所在省份*保留
//        sb.append("<receiver_province></receiver_province>");
//        //收货人所在市*保留
//        sb.append("<receiver_city></receiver_city>");
//        //收货人所在区*保留
//        sb.append("<receiver_district></receiver_district>");
//        //收货人地址1*保留
//        sb.append("<receiver_address1></receiver_address1>");
//        //收货人地址2*保留
//        sb.append("<receiver_address2></receiver_address2>");
//        //收货人地址3*保留
//        sb.append("<receiver_address3></receiver_address3>");
//        //收货人地址4*保留
//        sb.append("<receiver_address4></receiver_address4>");
//        //收货人邮编*保留
//        sb.append("<receiver_postal></receiver_postal>");
//        //收货人手机号码*保留
//        sb.append("<receiver_mobile></receiver_mobile>");
//        //收货人电话*保留
//        sb.append("<receiver_phone></receiver_phone>");
//        //物流公司*保留
//        sb.append("<logisticscompany></logisticscompany>");
//        //物流单号*保留
//        sb.append("<logisticsdocno></logisticsdocno>");
//        //期望送货日期*保留
//        sb.append("<expectdeliverydate_yyyymmdd></expectdeliverydate_yyyymmdd>");
//        sb.append("<deliveryremarks></deliveryremarks>");
//        sb.append("</salesdelivery>");
//        sb.append("</astr_request>");
//        sb.append("</postsalescreate>");
//        sb.append("</soap12:Body></soap12:Envelope>");
//        //System.out.println(sb.toString());
//        PostMethod postMethod = new PostMethod(url);
//        	
//        byte[] b = sb.toString().getBytes("utf-8");
//        InputStream is = new ByteArrayInputStream(b, 0, b.length);
//        RequestEntity re = new InputStreamRequestEntity(is, b.length,"application/soap+xml; charset=utf-8");
//        postMethod.setRequestEntity(re);
//       
//        HttpClient httpClient = new HttpClient();
//        int statusCode = httpClient.executeMethod(postMethod);
//        String soapResponseData = postMethod.getResponseBodyAsString();
//        if(statusCode == 200) 
//        {
//	       
//	       //System.out.println(soapResponseData);
//	       if(soapResponseData.indexOf("<responsecode>0")>-1 || soapResponseData.indexOf("<responsecode>1000")>-1)
//	       {
//	    	   return  200;
//	       }
//	       else
//	       {
//	    	   logger.info("kc调用失败！"+soapResponseData);
//	    	   if(soapResponseData.indexOf("Transaction Date is not allowed")>-1)
//	    	   {
//	    		   return  403;
//	    	   }
//	    	   //System.out.println(soapResponseData);
//	    	  
//	    	   return  402;
//	       }
//        }
//        else 
//        {
//        	logger.info("kc调用失败！错误码：" + statusCode);
//        	//String soapResponseData = postMethod.getResponseBodyAsString();
//	       // System.out.println(soapResponseData);
//        }
//        return  statusCode;
	}
	
	public static int postdailysalesestimatecreate2(String url,String licensekey,String username,String password,String store_code,String mallid,StringBuilder sb,String organ_code,JSONObject bill_info,List<JSONObject> item_list,List<JSONObject> payment_list) throws Exception
	{
		String remark = bill_info.optString("remark");
		sb.setLength(0);
		sb.append(HEADER);
		sb.append("<postsalescreate xmlns=\"http://tempurl.org\">");
		sb.append("<astr_request>");
		sb.append("<header>");
      	//--表头信息--
		//许可证
		sb.append("<licensekey>"+licensekey+"</licensekey>");
		sb.append("<username>"+username+"</username>");
        sb.append("<password>"+password+"</password>");
        sb.append("<lang></lang>");
        sb.append("<pagerecords>100</pagerecords>");
        sb.append("<pageno>1</pageno>");
        sb.append("<updatecount>0</updatecount>");
        sb.append("<messagetype>SALESDATA</messagetype>");
        sb.append("<messageid>332</messageid>");
        sb.append("<version>V332M</version>");
        sb.append("</header>");
        //select bill_num,serial_num,report_date,service_amount,subtotal,bill_amount,to_char(payment_time,'yyyymmdd') as yyyymmdd,to_char(payment_time,'HH24mmss') as hhmmss,(CASE when bill_amount<0 then 'SR' else 'SA' end) as salestype from pos_bill2 where bill_num='12920151209000121'
	   
        Double bill_amount = bill_info.optDouble("service_amount",0.0) + bill_info.optDouble("bill_amount",0.0);
        if(bill_amount==0)
        {
        	return 200;
        }
        //double service_amount = 
        //System.out.println(payment_list.size());
//        for(JSONObject jo:payment_list)
//        {
//        	bill_amount += jo.optDouble("amount",0.0);
//        	//System.out.println(jo.optDouble("amount",0.0));
//        }
        //System.out.println(bill_amount);
        //销售单主表
	    sb.append("<salestotal>");
	    sb.append("<localstorecode>"+organ_code+"</localstorecode>");
	    sb.append("<reservedocno>"+bill_info.optString("bill_num")+"</reservedocno>");
        sb.append("<txdate_yyyymmdd>"+bill_info.optString("yyyymmdd")+"</txdate_yyyymmdd>");
        sb.append("<txtime_hhmmss>"+bill_info.optString("hhmmss")+"</txtime_hhmmss>");
        sb.append("<mallid>"+mallid+"</mallid>");
        sb.append("<storecode>"+store_code+"</storecode>");
        sb.append("<tillid>01</tillid>");
        //SA SR
        sb.append("<salestype>"+bill_info.optString("salestype")+"</salestype>");
        sb.append("<txdocno>"+bill_info.optString("bill_num")+"</txdocno>");
        sb.append("<orgtxdate_yyyymmdd></orgtxdate_yyyymmdd>");
        sb.append("<orgstorecode></orgstorecode>");
        sb.append("<orgtillid></orgtillid>");
        sb.append("<txorgdocno></txorgdocno>");
        sb.append("<mallitemcode>"+bill_info.optString("bill_num")+"</mallitemcode>");
        sb.append("<cashier>"+bill_info.optString("serial_num")+"</cashier>");
        sb.append("<vipcode></vipcode>");
        sb.append("<salesman></salesman>");
        sb.append("<demographiccode></demographiccode>");
        sb.append("<demographicdata></demographicdata>");
        sb.append("<netqty>"+bill_info.optInt("num")+"</netqty>");
        //原始金额
        sb.append("<originalamount>"+bill_info.optDouble("subtotal",0.0)+"</originalamount>");
        //销售金额
        sb.append("<sellingamount>"+bill_amount+"</sellingamount>");

        sb.append("<couponnumber></couponnumber>");
        sb.append("<coupongroup></coupongroup>");
        sb.append("<coupontype></coupontype>");  
        sb.append("<couponqty>0</couponqty>");
        sb.append("<totaldiscount>");
        sb.append("<salesdiscount xsi:nil=\"true\" />");
        sb.append("</totaldiscount>");
        sb.append("<ttltaxamount1>0</ttltaxamount1>");
        sb.append("<ttltaxamount2>0</ttltaxamount2>");
       
        //净金额
        sb.append("<netamount>"+bill_amount+"</netamount>"); 
        sb.append("<paidamount>"+bill_amount+"</paidamount>");
        sb.append("<changeamount>0</changeamount>");
        sb.append("<priceincludetax></priceincludetax>");
        sb.append("<shoptaxgroup></shoptaxgroup>");
        sb.append("<extendparam></extendparam>");
        sb.append("<invoicetitle></invoicetitle>");
        sb.append("<invoicecontent></invoicecontent>");
        
        sb.append("<issueby>admin</issueby>");
        sb.append("<issuedate_yyyymmdd>"+DateUtil.getyyyymmdd()+"</issuedate_yyyymmdd>");
        sb.append("<issuetime_hhmmss>"+DateUtil.gethhmmss()+"</issuetime_hhmmss>");
        //DateUtil.getNowDateYYDDMMHHMMSS()
        sb.append("<ecorderno></ecorderno>");
        sb.append("<buyerremark></buyerremark>");
        sb.append("<orderremark></orderremark>");
        sb.append("<status></status>");
        sb.append("<ttpossalesdocno></ttpossalesdocno>");
        sb.append("</salestotal>");

        //SalesItem:销售单货品明细表
        sb.append("<salesitems>");
        int item_count = 0;
        for(JSONObject jo : item_list)
        {
        	item_count += jo.optInt("item_count");
        	
        }
        if(item_count==0)
        {
        	item_count =1;
        }
    	sb.append("<salesitem>");
		sb.append("<iscounteritemcode>1</iscounteritemcode>");
        sb.append("<lineno>1</lineno>");
        sb.append("<storecode>"+store_code+"</storecode>");
        sb.append("<mallitemcode>"+remark+"</mallitemcode>");
        sb.append("<counteritemcode>"+remark+"</counteritemcode>");
        sb.append("<itemcode>"+remark+"</itemcode>");
        sb.append("<plucode>"+remark+"</plucode>");
        sb.append("<colorcode></colorcode>");
        sb.append("<sizecode></sizecode>");
        sb.append("<itemlotnum></itemlotnum>");
        sb.append("<serialnum></serialnum>");
        sb.append("<isdeposit></isdeposit>");
        sb.append("<iswholesale></iswholesale>");
        sb.append("<invttype>1</invttype>");
        sb.append("<qty>"+item_count+"</qty>");
        sb.append("<exstk2sales>1</exstk2sales>");
        sb.append("<originalprice>0</originalprice>");
        sb.append("<sellingprice>0</sellingprice>");
        sb.append("<pricemode></pricemode>");
        sb.append("<priceapprove></priceapprove>");
        sb.append("<couponnumber></couponnumber>");
        sb.append("<coupongroup></coupongroup>");
        sb.append("<coupontype></coupontype>");
        sb.append("<itemdiscount xsi:nil=\"true\" />");
        sb.append("<vipdiscountpercent>0</vipdiscountpercent>");
        sb.append("<vipdiscountless>0</vipdiscountless>");
        sb.append("<promotion xsi:nil=\"true\" />");
        sb.append("<totaldiscountless1>0</totaldiscountless1>");
        sb.append("<totaldiscountless2>0</totaldiscountless2>");
        sb.append("<totaldiscountless>0</totaldiscountless>");
        sb.append("<tax xsi:nil=\"true\" />");
        sb.append("<netamount>"+bill_amount+"</netamount>");
        sb.append("<bonusearn>0</bonusearn>");
        sb.append("<salesitemremark></salesitemremark>");
        sb.append("<refundreasoncode></refundreasoncode>");
        sb.append("<extendparam></extendparam>");
        sb.append("</salesitem>");        	
       
        sb.append("</salesitems>");
        // -- 销售单付款明细表 --   
        sb.append("<salestenders>");
        int lineno = 0;
        String tendercode = "OT";
        if(payment_list.size()>0)
        {
        	
        	tendercode = payment_list.get(0).optString("pay_code");
        }

        sb.append("<salestender>");
      	//行号
        lineno ++;
        sb.append("<lineno>"+lineno+"</lineno>");
        //付款代码 CH----现金 CI----国内银行卡 CO----国外银行卡 OT-----其他付款方式
        sb.append("<tendercode>"+tendercode+"</tendercode>");
        //付款类型 *保留
        sb.append("<tendertype>0</tendertype>");
        //付款种类  *保留 0
        sb.append("<tendercategory>0</tendercategory>");
        //付款金额
        sb.append("<payamount>"+bill_amount+"</payamount>");
        //本位币金额 同上
        sb.append("<baseamount>"+bill_amount+"</baseamount>");
        //超额金额  *保留 0
        sb.append("<excessamount>0</excessamount>");
        //扩展参数 *保留
        sb.append("<extendparam></extendparam>");
        //备注 *保留
        sb.append("<remark></remark>");
        sb.append("</salestender>");	
        sb.append("</salestenders>");

        //--配送相关 --  *保留
        sb.append("<salesdelivery>");
        //收货人姓名*保留
        sb.append("<receiver_name></receiver_name>");
        //收货人所在国家*保留
        sb.append("<receiver_country></receiver_country>");
        //收货人所在省份*保留
        sb.append("<receiver_province></receiver_province>");
        //收货人所在市*保留
        sb.append("<receiver_city></receiver_city>");
        //收货人所在区*保留
        sb.append("<receiver_district></receiver_district>");
        //收货人地址1*保留
        sb.append("<receiver_address1></receiver_address1>");
        //收货人地址2*保留
        sb.append("<receiver_address2></receiver_address2>");
        //收货人地址3*保留
        sb.append("<receiver_address3></receiver_address3>");
        //收货人地址4*保留
        sb.append("<receiver_address4></receiver_address4>");
        //收货人邮编*保留
        sb.append("<receiver_postal></receiver_postal>");
        //收货人手机号码*保留
        sb.append("<receiver_mobile></receiver_mobile>");
        //收货人电话*保留
        sb.append("<receiver_phone></receiver_phone>");
        //物流公司*保留
        sb.append("<logisticscompany></logisticscompany>");
        //物流单号*保留
        sb.append("<logisticsdocno></logisticsdocno>");
        //期望送货日期*保留
        sb.append("<expectdeliverydate_yyyymmdd></expectdeliverydate_yyyymmdd>");
        sb.append("<deliveryremarks></deliveryremarks>");
        sb.append("</salesdelivery>");
        sb.append("</astr_request>");
        sb.append("</postsalescreate>");
        sb.append("</soap12:Body></soap12:Envelope>");
        //System.out.println(sb.toString());
        PostMethod postMethod = new PostMethod(url);
        	
        byte[] b = sb.toString().getBytes("utf-8");
        InputStream is = new ByteArrayInputStream(b, 0, b.length);
        RequestEntity re = new InputStreamRequestEntity(is, b.length,"application/soap+xml; charset=utf-8");
        postMethod.setRequestEntity(re);
        int statusCode = 0;
        HttpClient httpClient = new HttpClient();
		try
		{
			statusCode = httpClient.executeMethod(postMethod);
		}
		catch (Exception e)
		{
			return 404;
		}
        String soapResponseData = postMethod.getResponseBodyAsString();
        if(statusCode == 200) 
        {
	       
	       //System.out.println(soapResponseData);
	       if(soapResponseData.indexOf("<responsecode>0")>-1 || soapResponseData.indexOf("<responsecode>1000")>-1)
	       {
	    	   return  200;
	       }
	       else
	       {
	    	   System.out.println(soapResponseData);
	    	   logger.info("kc调用失败！"+soapResponseData);
	    	   if(soapResponseData.indexOf("Transaction Date is not allowed")>-1)
	    	   {
	    		   return  403;
	    	   }
	    	   
	    	  
	    	   return  402;
	       }
        }
        else 
        {
        	System.out.println(soapResponseData);
        	logger.info("kc调用失败！错误码：" + statusCode);
        	//String soapResponseData = postMethod.getResponseBodyAsString();
	       // System.out.println(soapResponseData);
        }
        return  statusCode;
	}
}





