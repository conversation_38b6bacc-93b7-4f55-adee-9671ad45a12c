package com.tzx.clientorder.acewillwechat.bo;

import net.sf.json.JSONObject;

import com.tzx.clientorder.acewillwechat.service.servlet.SocketMessageProcessor;

/**
 * Created by qin-gui on 2018/3/10.
 */
public interface OrderService extends SocketMessageProcessor{
    String	NAME	= SocketMessageProcessor.UPLOAD_ORDER;

    /**
     * 上传订单，查询订单信息
     * @param tenancyId
     * @param storeId
     * @param tableCode
     * @param opType
     * @return
     * @throws Exception
     */
	JSONObject getOrderInfo(String tenancyId, int storeId, String tableCode, String opType) throws Exception;
}
