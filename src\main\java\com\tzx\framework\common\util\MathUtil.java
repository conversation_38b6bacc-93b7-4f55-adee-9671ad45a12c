package com.tzx.framework.common.util;

import java.math.BigDecimal;

public class MathUtil
{
	public static Double	defDouble	= 0.0;

	/**
	 * 舍掉小数
	 */
	public static Double floor(Double result)
	{

		try
		{
			return Math.floor(result);
		}
		catch (Exception e)
		{

			return defDouble;

		}

	}

	/**
	 * 四舍五入
	 */
	public static Double halfup(Double result)
	{

		try
		{
			return Double.parseDouble(new BigDecimal(result.toString()).setScale(0, BigDecimal.ROUND_HALF_UP).toString());
		}
		catch (Exception e)
		{
			return defDouble;
		}

	}

	/**
	 * 凑整
	 */
	public static Double ceil(Double result)
	{

		try
		{
			return Math.ceil(result);
		}
		catch (Exception e)
		{
			return defDouble;
		}

	}

}
