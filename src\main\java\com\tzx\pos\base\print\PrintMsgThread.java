package com.tzx.pos.base.print;

import net.sf.json.JSONObject;

import org.apache.log4j.Logger;

import com.tzx.base.bo.PrintFormatService;
import com.tzx.base.bo.imp.PrintFormatServiceImpl;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;

/**
 *
 * <AUTHOR>
 * 2015年7月16日-下午9:03:49
 */
public class PrintMsgThread implements Runnable
{
	private static final Logger logger = Logger.getLogger(PrintMsgThread.class);
	
	private JSONObject msg;
	
	public JSONObject getMsg()
	{
		return msg;
	}

	public void setMsg(JSONObject msg )
	{
		this.msg = msg;
	}

	public PrintMsgThread()
	{
		super();
	}

	public PrintMsgThread(JSONObject msg)
	{
		this.msg = msg;
	}

	public void run()
	{
		try
		{
			String tenancy_id = msg.getString("tenancy_id");
			String mode = msg.getString("mode");
			logger.info("Print the message: " + msg.toString());
			DBContextHolder.setTenancyid(tenancy_id);
			PrintFormatServiceImpl printFormatService = (PrintFormatServiceImpl) SpringConext.getApplicationContext().getBean(PrintFormatService.NAME);
			
			if ("1".equals(mode))
			{
				printFormatService.printCusBill(tenancy_id, msg);
			}else
			{
				printFormatService.printBill(tenancy_id, msg);
			}
		}
		catch (Exception e)
		{
			logger.info("调用打印线程失败：" + ExceptionMessage.getExceptionMessage(e));
			e.printStackTrace();
		}
	}
}