package com.tzx.framework.common.umengpush.android;

import java.util.HashMap;
import java.util.Map;

public class PushInfo {

	private String title = ""; 	// 必填 通知标题
	private String text = "";	// 必填 通知文字描述 
	private String ticker = "";	// 必填 通知栏提示文字

	private Map<String, String> extraFields = new HashMap<String, String>();

	public PushInfo(){}
	
	public PushInfo(String ticker, String title, String text){
		this.ticker = ticker;
		this.title = title;
		this.text = text;
	}
	
	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getText() {
		return text;
	}

	public void setText(String text) {
		this.text = text;
	}

	public String getTicker() {
		return ticker;
	}

	public void setTicker(String ticker) {
		this.ticker = ticker;
	}

	public Map<String, String> getExtraFields() {
		return extraFields;
	}

	public void setExtraFields(Map<String, String> extraFields) {
		this.extraFields = extraFields;
	}
	
}
