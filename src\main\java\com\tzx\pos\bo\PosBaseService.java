package com.tzx.pos.bo;

import java.util.Date;
import java.util.List;

import com.tzx.framework.common.entity.Data;

import net.sf.json.JSONObject;

public interface PosBaseService
{
	String	NAME	= "com.tzx.pos.bo.imp.PosBaseServiceImp";

	/**
	 * 金额计算
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @throws Exception
	 */
	public void calcAmount(String tenantId, Integer storeId, String billNum) throws Exception;

	/** 计算发票金额
	 * @param tenantId
	 * @param storeId
	 * @param param
	 * @throws Exception
	 */
	public double calcAmountForInvoice(String tenantId, Integer storeId, JSONObject param) throws Exception;

	/**
	 * 生成电子发票
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public JSONObject getPrintInvoiceInfo(String tenancyId, Integer storeId, Date reportDate, JSONObject param) throws Exception;

	/**
	 * 读取pos_config.properties文件 初始化sys_parameter表系统参数
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @throws Exception
	 */
	public void initSysParamterDataByProties(String tenancyId, int storeId) throws Exception;

	/**账单数据上传
	 * @param paramData
	 */
	public void upload(Data paramData);
	
	/** 账单数据上传
	 * @param tenantId
	 * @param store_id
	 * @param old_table_code
	 * @param new_table_code
	 * @param old_bill_num
	 * @param new_bill_num
	 */
	public void upload(String tenantId, String store_id, String old_table_code, String new_table_code, String old_bill_num, String new_bill_num);

	/** 设置菜品会员价
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param discountMode
	 * @param rwidList
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> SetCustomerVipPriceByItem(String tenancyId,int storeId,String billNum,int discountMode,List<Integer> rwidList) throws Exception;
	
	/**修改账单上传状态
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @throws Exception
	 */
	public void updatePosBillForUpload(String tenancyId, int storeId,String billNum) throws Exception;
	
	/**修改账单上传状态
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @throws Exception
	 */
	public void updatePosBillForUpload(String tenancyId, int storeId,String billNum,String[] tableNames) throws Exception;

    /**
     * 获取签到表，最后一个签到人的信息（签到未签退）
     * @param tenancyId
     * @param storeId
     * @param reportDate
     * @return
     * @throws Exception
     */
    List<JSONObject> getOptStateInfo(String tenancyId, int storeId, String reportDate) throws Exception;

	/**
	 * 生成开电子发票二维码文件
	 *
	 * @return
	 */
	String generateInvoiceQrCodeFile(String tenancyId, int storeId, JSONObject invoiceInfo);
}
