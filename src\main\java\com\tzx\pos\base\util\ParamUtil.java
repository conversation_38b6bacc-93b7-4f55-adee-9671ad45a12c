package com.tzx.pos.base.util;

import com.tzx.estate.common.util.dao.datasource.DBContextHolder;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.ErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.ObjectMapper;
import com.tzx.framework.common.util.Scm;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.Tools;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.log4j.Logger;
import org.springframework.jdbc.support.rowset.SqlRowSet;

import java.sql.Timestamp;
import java.util.Date;
import java.util.Map;

/**
 *
 * <AUTHOR> 2015年7月21日-上午11:30:34
 */
public class ParamUtil
{

    private static Logger logger	= Logger.getLogger(ParamUtil.class);
    //private static GenericDao dao= (GenericDao) SpringConext.getBean("genericDaoImpl");

    @SuppressWarnings("unchecked")
	public static <T> T  getDataValue(Data param,String key){
        if(null!=param&&null!=param.getData()&&!param.getData().isEmpty()){
            Map<String,T> map= (Map<String, T>) param.getData().get(0);
            return map.get(key);
        }
        return null;
    }

    /**
	 * 获取map里的参数值，如果isExce为true，则抛出自定义异常
	 * 
	 * @param map
	 *            Map<String,Object>
	 * @param paramName
	 *            参数名称，如：bill_num
	 * @param isExce
	 *            boolena true：抛异常，false：不抛异常
	 * @param errorCode
	 *            自定义的异常 PosErrorCode.NOT_NULL_BILL_NUM(账单编号不能为空)
	 * @return 返回字符串类型
	 */
    public static String getStringValue(Map<String, Object> map, String paramName, boolean isExce, ErrorCode errorCode)
    {
    	return getStringValue(map, paramName, isExce, errorCode, true);
    }
    
	/**
	 * @param map
	 * @param paramName
	 * @param isExce
	 * @param errorCode
	 * @param isTrim 是否去除空格
	 * @return
	 */
	public static String getStringValue(Map<String, Object> map, String paramName, boolean isExce, ErrorCode errorCode,boolean isTrim)
	{
		String result = null;
		if (Tools.isNullOrEmpty(map.get(paramName)) == false)
		{
			result = (map.get(paramName).toString());
			if(isTrim)
			{
				result = result.trim();
			}
		}
		else
		{
			if (isExce)
			{
				throw new SystemException(paramName,errorCode);
			}
		}
		return result;
	}

	/**
	 * @param object
	 * @param paramName
	 * @param isExce
	 * @param errorCode
	 * @return
	 */
	public static String getStringValueByObject(JSONObject object, String paramName, boolean isExce, ErrorCode errorCode)
	{
		String result = null;
		if (Tools.isNullOrEmpty(object.get(paramName)) == false)
		{
			result = (object.get(paramName).toString()).trim();
			if("null".equals(result))
			{
				result = null;
			}
		}
		else
		{
			if (isExce)
			{
				throw new SystemException(errorCode);
			}
		}
		return result;
	}
	
	
	public static String getStringValueForNullByObject(JSONObject object, String paramName)
	{
		String result = "";
		if (Tools.isNullOrEmpty(object.get(paramName)) == false)
		{
			result = (object.get(paramName).toString()).trim();
			if("null".equals(result))
			{
				result = "";
			}
		}

		return result;
	}

	/**
	 * @param object
	 * @param paramName
	 * @return
	 */
	public static String getStringValueByObject(JSONObject object, String paramName)
	{
		return getStringValueByObject(object, paramName, false, null);
	}

	/**
	 * 获取map里的参数值，如果isExce为true，则抛出自定义异常
	 * 
	 * @param map
	 *            Map<String,Object>
	 * @param paramName
	 *            参数名称，如：shift_id
	 * @param isExce
	 *            boolena true：抛异常，false：不抛异常
	 * @param errorCode
	 *            自定义的异常 PosErrorCode.NOT_NULL_SHIFT_ID(班次不能为空)
	 * @return 返回Integer类型
	 */
	public static Integer getIntegerValue(Map<String, Object> map, String paramName, boolean isExce, ErrorCode errorCode)
	{
		Integer result = null;
		if (Tools.isNullOrEmpty(map.get(paramName)) == false)
		{
			result = Integer.parseInt(map.get(paramName).toString());
		}
		else
		{
			if (isExce)
			{
				throw new SystemException(errorCode);
			}
		}
		return result;
	}

	/**
	 * @param jsonObject
	 * @param paramName
	 * @param isExce
	 * @param errorCode
	 * @return
	 */
	public static Integer getIntegerValueByObject(JSONObject jsonObject, String paramName, boolean isExce, ErrorCode errorCode)
	{
		Integer result = null;
		if (jsonObject.containsKey(paramName) && Tools.isNullOrEmpty(jsonObject.get(paramName)) == false && "null".equals(jsonObject.get(paramName).toString()) == false)
		{
//			result = Integer.parseInt(jsonObject.get(paramName).toString());
			result = jsonObject.optInt(paramName);
		}
		else
		{
			if (isExce)
			{
				throw new SystemException(errorCode);
			}
		}
		return result;
	}

	/**
	 * @param jsonObject
	 * @param paramName
	 * @return
	 */
	public static Integer getIntegerValueByObject(JSONObject jsonObject, String paramName)
	{
		return getIntegerValueByObject(jsonObject, paramName, false, null);
	}

	/**
	 * 获取map里的参数值，如果isExce为true，则抛出自定义异常
	 * 
	 * @param map
	 *            Map<String,Object>
	 * @param paramName
	 *            参数名称，如：price_amount
	 * @param isExce
	 *            boolena true：抛异常，false：不抛异常
	 * @param errorCode
	 *            自定义的异常 PosErrorCode.NOT_NULL_PRICE_AMOUNT(商品价格不能为空)
	 * @return 返回Double类型
	 */
	public static Double getDoubleValue(Map<String, Object> map, String paramName, boolean isExce, ErrorCode errorCode)
	{
		Double result = 0d;
		if (map.containsKey(paramName) && Tools.isNullOrEmpty(map.get(paramName)) == false)
		{
			result = Double.parseDouble(map.get(paramName).toString());
			if (null == result || result.isNaN())
			{
				result = 0d;
			}
		}
		else
		{
			if (isExce)
			{
				throw new SystemException(errorCode);
			}
		}
		return result;
	}

	/**
	 * @param object
	 * @param paramName
	 * @param isExce
	 * @param errorCode
	 * @return
	 */
	public static Double getDoubleValueByObject(JSONObject object, String paramName, boolean isExce, ErrorCode errorCode)
	{
		Double result = 0d;
		if (object.containsKey(paramName) && Tools.isNullOrEmpty(object.get(paramName)) == false && "null".equals(object.opt(paramName).toString()) == false)
		{
			result = Double.parseDouble(object.get(paramName).toString());
			if (null == result || result.isNaN())
			{
				result = 0d;
			}
		}
		else
		{
			if (isExce)
			{
				throw new SystemException(errorCode);
			}
		}
		return result;
	}

	public static Double getDoubleValueByObject(JSONObject object, String paramName)
	{
		return getDoubleValueByObject(object, paramName, false, null);
	}

	/**
	 * @param map
	 * @param paramName
	 * @param isExce
	 * @param errorCode
	 * @return
	 */
	public static Date getDateValue(Map<String, Object> map, String paramName, boolean isExce, ErrorCode errorCode)
	{
		Object obj = map.get(paramName);
		Date result = null;
		if (Tools.isNullOrEmpty(obj) == false)
		{
			String strDate = obj.toString();
			String[] date = null;
			if (strDate.contains("/"))
			{
				date = strDate.split("/");
			}
			else if (strDate.contains("-"))
			{
				date = strDate.split("-");
			}
			if (date[0].length() < 4)
			{
				date[0] = ("20" + date[0]).substring(0, 4);
			}
			if (date[1].length() < 2)
			{
				date[1] = ("0" + date[1]).substring(0, 2);
			}
			if (date[2].length() < 2)
			{
				date[2] = ("0" + date[2]).substring(0, 2);
			}
			String ndate = date[0] + "-" + date[1] + "-" + date[2];
			result = DateUtil.parseDate(ndate);
		}
		else
		{
			if (isExce)
			{
				throw new SystemException(errorCode);
			}
		}

		return result;
	}
	
	public static Date getDateValueByObject(JSONObject object, String paramName, boolean isExce, ErrorCode errorCode)
	{
		Date result = null;
		if (object.containsKey(paramName) && Tools.isNullOrEmpty(object.get(paramName)) == false)
		{
			result = DateUtil.parseDate(object.get(paramName).toString());
		}
		else
		{
			if (isExce)
			{
				throw new SystemException(errorCode);
			}
		}
		return result;
	}
	
	public static Date getDateValueByObject(JSONObject object, String paramName)
	{
		return getDateValueByObject(object, paramName, false, null);
	}

	/**
	 * @param map
	 * @param paramName
	 * @param isExce
	 * @param errorCode
	 * @return
	 */
	public static Timestamp getTimestampValue(Map<String, Object> map, String paramName, boolean isExce, ErrorCode errorCode)
	{
		Timestamp result = null;
		if (Tools.isNullOrEmpty(map.get(paramName)) == false)
		{
			result = DateUtil.parseTimestamp(map.get(paramName).toString());
		}
		else
		{
			if (isExce)
			{
				throw new SystemException(errorCode);
			}
		}

		return result;
	}
	
	public static Timestamp getTimestampValueByObject(JSONObject object, String paramName, boolean isExce, ErrorCode errorCode)
	{
		Timestamp result = null;
		if (object.containsKey(paramName) && Tools.isNullOrEmpty(object.get(paramName)) == false)
		{
			result = DateUtil.parseTimestamp(object.get(paramName).toString());
		}
		else
		{
			if (isExce)
			{
				throw new SystemException(errorCode);
			}
		}
		return result;
	}
	
	public static Timestamp getTimestampValueByObject(JSONObject object, String paramName)
	{
		return getTimestampValueByObject(object, paramName, false, null);
	}

	/**
	 * @param param
	 * @param paramName
	 * @return
	 */
	public static String getDateStringValue(JSONObject param, String paramName)
	{
		String result = null;
		String strDate = param.optString(paramName);
		if (Tools.hv(strDate))
		{
			String[] date = null;
			if (strDate.contains("/"))
			{
				date = strDate.split("/");
			}
			else if (strDate.contains("-"))
			{
				date = strDate.split("-");
			}

			if (date[0].length() < 4)
			{
				String year2 = String.valueOf(((new Date()).getYear() + 1900) / 100);
				date[0] = (year2 + date[0]).substring(0, 4);
			}

			if (date[1].length() < 2)
			{
				date[1] = ("0" + date[1]).substring(0, 2);
			}

			if (date[2].length() < 2)
			{
				date[2] = ("0" + date[2]).substring(0, 2);
			}

			result = date[0] + "-" + date[1] + "-" + date[2];
		}
		return result;
	}
	
	public static JSONObject getJSONObject(JSONObject object, String paramName)
	{
		JSONObject json = null;
		try
		{
			if (null != object && object.containsKey(paramName))
			{
				json = JSONObject.fromObject(object.optString(paramName));
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		return json;
	}

	/**
	 * 为打印设置参数
	 * 
	 * @param tenancyid
	 * @param billno
	 * @param print_format
	 * @param rwid
	 * @param printer_id
	 * @param organId
	 * @return
	 */
	public static JSONObject setDishParam(String tenancyid, String billno, String printCode, String printFormat, Integer rwid, Integer printer_id, Integer organId, String print_property)
	{
		JSONObject obj = new JSONObject();
		obj.put("printer_id", printer_id);
		obj.put("print_code", printCode);
		obj.put("print_format", printFormat);
		obj.put("rwid", rwid);
		obj.put("bill_num", billno);
		obj.put("tenancy_id", tenancyid);
		obj.put("store_id", organId);
		obj.put("print_property", print_property);
		return obj;
	}

	/**
	 * 为模板打印设置参数
	 * 
	 * @param tenancyid
	 * @param billno
	 * @param print_code
	 * @param printer_id
	 * @param organId
	 * @return
	 */
	public static JSONObject setTempParam(String mode, String tenancyid, String billno, String print_code, Integer printer_id, Integer organId, String paymentUrl)
	{
		JSONObject obj = new JSONObject();
		obj.put("mode", mode);
		obj.put("printer_id", printer_id);
		obj.put("print_code", print_code); // 模板对应的id
		obj.put("bill_num", billno);
		obj.put("tenancy_id", tenancyid);
		obj.put("store_id", organId);
		obj.put("payment_url", paymentUrl);
		return obj;
	}

	/**
	 * 为打印设置参数
	 * 
	 * @param tenancyid
	 * @param card_code
	 * @param bill_code
	 * @param print_code
	 * @param printer_id
	 * @param organId
	 * @return
	 */
	public static JSONObject setCusParam(String mode, String tenancyid, String cardCode, String billCode, Object print_code, Object printer_id, Integer organId, double credit, double useful_credit, double main_balance, double reward_balance, double total_main, double total_reward, String operator,
			String updatetime, double income, double consume_cardmoney, double deposit, double sales_price, double main_trading, double reward_trading, String payment_name, String card_class_name, String name, String mobil, String strUrlPath, String strUrlContent, String operatedate,
			String level_name, String orignal_card)
	{
		double total_balance = Scm.padd(main_balance, reward_balance);

		JSONObject obj = new JSONObject();
		obj.put("tenancy_id", tenancyid);
		obj.put("store_id", organId);
		obj.put("mode", mode);
		obj.put("print_time", DateUtil.getNowDateYYDDMMHHMMSS());
		obj.put("printer_id", printer_id);
		obj.put("print_code", print_code); // 模板对应的id

		obj.put("bill_code", billCode);
		obj.put("card_code", cardCode);
		obj.put("credit", credit);
		obj.put("useful_credit", useful_credit);
		obj.put("main_balance", main_balance);
		obj.put("reward_balance", reward_balance);
		obj.put("total_main", total_main);
		obj.put("total_reward", total_reward);
		obj.put("income", income);
		obj.put("consume_cardmoney", consume_cardmoney);
		obj.put("deposit", deposit);
		obj.put("sales_price", sales_price);
		obj.put("operator", operator);
		obj.put("updatetime", updatetime);
		obj.put("main_trading", main_trading);
		obj.put("reward_trading", reward_trading);
		obj.put("total_balance", total_balance);
		obj.put("payment_name1", payment_name);
		obj.put("card_class_name", card_class_name);
		obj.put("name", name);
		obj.put("mobil", mobil);
		obj.put("operatedate", operatedate);
		obj.put("level_name", level_name);
		obj.put("card_code_original", orignal_card);
		obj.put("url_path", strUrlPath);
		obj.put("url_content", strUrlContent);
		if (Tools.hv(strUrlPath))
		{
			obj.put("content1", "开发票");
			obj.put("content2", "请使用支付宝或微信扫码开发票");
		}
		else
		{
			obj.put("content1", "");
			obj.put("content2", "");
		}
		return obj;
	}

	/**
	 * @param str
	 * @return
	 * @throws Exception
	 */
	public static Data stringToData(String str) throws Exception
	{
		Data retData = new Data();
		try
		{
			ObjectMapper objectMapper = new ObjectMapper();
			retData = objectMapper.readValue(str, Data.class);
		}
		catch (Exception e)
		{
			e.printStackTrace();
			retData.setCode(Constant.CODE_CONN_EXCEPTION);
			retData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
		}
		return retData;
	}

    /** 获取系统参表系统参数编码获取参数值
     * @param tenancyId
     * @param storeId
     * @param code
     * @return
     */
    public static String getSysPara(String tenancyId, int storeId, String code){

        String value = "";

        String sql = "select store_id,trim(para_value) as para_value from sys_parameter where para_code = ? and valid_state='1'";

        SqlRowSet rs = null;
        try {
            DBContextHolder.setTenancyid(tenancyId);
            GenericDao dao= (GenericDao) SpringConext.getBean("genericDaoImpl");
            rs = dao.getJdbcTemplate(tenancyId).queryForRowSet(sql, new Object[]{code});
            while (rs.next()) {
                int _storeId = rs.getInt("store_id");
                if (0 == _storeId || storeId == _storeId) {
                    value = rs.getString("para_value");
                    if (storeId == _storeId) {
                        break;
                    }
                }
            }
        } catch (Exception e) {
            logger.error("获取系统参数错误！", e);
        }

        return value;
    }
    
	/**
	 * 获取请求单号
	 * 
	 * @param billNum
	 * @param batchNum
	 * @return
	 * @throws Exception
	 */
	public static String getOutTradeNo(String billNum, String batchNum, Timestamp paymenTime) throws Exception
	{
		String outTradeNo = billNum;
		if (Tools.hv(batchNum) && !"null".equals(batchNum))
		{
			outTradeNo = billNum + "_" + batchNum;
		}
		outTradeNo = outTradeNo + "@" + String.valueOf(paymenTime.getTime());

		return outTradeNo;
	}

	/**
	 * 获取请求单号
	 * 
	 * @param billNum
	 * @param batchNum
	 * @param orderCode
	 * @param channel
	 * @param paymenTime
	 * @return
	 * @throws Exception
	 */
	public static String getOutTradeNo(String billNum, String batchNum, String orderCode, String channel, Timestamp paymenTime) throws Exception
	{
		String outTradeNo = null;
		if ((!SysDictionary.CHANEL_MD01.equals(channel)) && Tools.hv(orderCode))
		{
			outTradeNo = orderCode;
		}
		else
		{
			outTradeNo = billNum;
			if (Tools.hv(batchNum) && !"null".equals(batchNum))
			{
				outTradeNo = billNum + "_" + batchNum;
			}
			outTradeNo = outTradeNo + "@" + String.valueOf(paymenTime.getTime());
		}

		return outTradeNo;
	}

	/**
	 * 获取微生活请求单号
	 * 
	 * @param billNum
	 * @param paymenTime
	 * @return
	 * @throws Exception
	 */
	public static String getOutTradeNoForAcewill(String billNum, Timestamp paymenTime) throws Exception
	{
		String outTradeNo = billNum + "_" + String.valueOf(paymenTime.getTime());
		return outTradeNo;
	}

	/**
	 * 获取微生活请求单号
	 * 
	 * @param billNum
	 * @param orderCode
	 * @param channel
	 * @param paymenTime
	 * @return
	 * @throws Exception
	 */
	public static String getOutTradeNoForAcewillCredit(String billNum, Timestamp paymenTime) throws Exception
	{
		String outTradeNo = billNum + "_" + SysDictionary.BILL_MEMBERCARD_JFZS06 + "_" + String.valueOf(paymenTime.getTime());
		return outTradeNo;
	}

	/** 获取JSONArray
	 * @param object
	 * @param paramName
	 * @return
	 */
	public static JSONArray getJSONArray(JSONObject object, String paramName)
	{
		return getJSONArray(object, paramName, null);
	}

	/** 获取JSONArray
	 * @param object
	 * @param paramName
	 * @return
	 */
	public static JSONArray getJSONArray(JSONObject object, String paramName,JSONArray defaultVal)
	{
		JSONArray array = null;
		try
		{
			if (null != object && object.containsKey(paramName))
			{
				array = object.optJSONArray(paramName);
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}

		if (null != defaultVal && null == array)
		{
			array = defaultVal;
		}
		return array;
	}
	
	/** 获取Data对象参数
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public static JSONObject getJSONObjectForData(Data param) throws Exception
	{
		if (null != param && null != param.getData() && !param.getData().isEmpty() && 0 < param.getData().size())
		{
			return JSONObject.fromObject(param.getData().get(0));
		}
		return null;
	}
}
