package com.tzx.pos.bo.imp;

import com.tzx.base.common.util.PosPropertyUtil;
import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.HttpRestException;
import com.tzx.framework.common.util.HttpUtil;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.pos.bo.PosFinanceCheck;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.BadSqlGrammarException;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR> on 2018-07-23
 */
@Service
@EnableAsync
public class PosFinanceCheckImpl implements PosFinanceCheck {

    private Logger logger = LoggerFactory.getLogger(PosFinanceCheckImpl.class);
    private static final String DAY_PATTER = "d";
    private static final String MONTH_PATTER = "M";
    private static final String[] FORBID_KEY = {"DELETE", "INSERT", "UPDATE", "DROP", "TRUNCATE"};
    private static final String START_TIME_APPEND=" 00:00:00";
    private static final String END_TIME_APPEND=" 23:59:59";

    private static final String TENANCY_ID_PATTEN="?tenancy_id?";
    private static final String STORE_ID_PATTEN="?store_id?";
    private static final String START_TIME_PATTEN="?start_time?";
    private static final String END_TIME_PATTEN="?end_time?";

    @Resource(name = "genericDaoImpl")
    private GenericDao dao;

    @Async
    @Override
    public void uploadFinanceCheckResult(String tenentId, Integer storeId, JSONObject arg) {
        JSONObject result = new JSONObject();
        try {
            String reportDate = arg.optString("report_date");
            List<JSONObject> metas = dao.query4Json(tenentId, "SELECT * FROM pos_unequal_meta where valid_state='1'");
            String periodPatter = null;
            String startTime = null;
            String endTime = null;
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");

            if (null != metas) {
                rawSqlLoop:
                for (JSONObject meta : metas) {
                    periodPatter = meta.optString("period");
                    endTime = reportDate + END_TIME_APPEND;
                    if (DAY_PATTER.equals(periodPatter)) {
                        startTime = reportDate + START_TIME_APPEND;
                    } else if (MONTH_PATTER.equals(periodPatter)) {
                        Calendar calendar = GregorianCalendar.getInstance();
                        calendar.setTime(simpleDateFormat.parse(reportDate));
                        calendar.set(Calendar.DAY_OF_MONTH, 1);
                        startTime = simpleDateFormat.format(calendar.getTime()) + START_TIME_APPEND;

                    } else {
                        logger.warn("period不合法,目前仅支持d,M");
                        continue;
                    }
                    String rawSql = meta.optString("sql");
                    for (String fs : FORBID_KEY) {
                        if (StringUtils.containsIgnoreCase(rawSql, fs)) {
                            logger.warn("数据检验sql[" + rawSql + "]中有非法关键字:" + fs);
                            continue rawSqlLoop;
                        }
                    }

                    if(StringUtils.contains(rawSql,TENANCY_ID_PATTEN)){
                       rawSql=rawSql.replace(TENANCY_ID_PATTEN,"'" + tenentId + "'");
                    }
                    if(StringUtils.contains(rawSql,STORE_ID_PATTEN)){
                       rawSql=rawSql.replace(STORE_ID_PATTEN,"'" + storeId + "'");
                    }
                    if(StringUtils.contains(rawSql,START_TIME_PATTEN)){
                        rawSql=rawSql.replace(START_TIME_PATTEN,"'" + startTime + "'");
                    }
                    if(StringUtils.contains(rawSql,END_TIME_PATTEN)){
                        rawSql=rawSql.replace(END_TIME_PATTEN,"'" + endTime + "'");
                    }

                    JSONObject  subResult=new JSONObject();
                    try {
                        logger.info("执行数据校验sql:"+rawSql);
                        List<JSONObject> list=dao.query4Json(tenentId,rawSql);
                        if(null!=list&&!list.isEmpty()){
                            subResult.put("result",list);
                            subResult.put("code", 0);
                        }else {
                            subResult.put("code",200);
                            subResult.put("msg","查询结果为空");
                        }
                    } catch (BadSqlGrammarException sqle) {
                        logger.warn("数据校验sql执行失败:"+rawSql,sqle);
                        subResult.put("code",100);
                        subResult.put("msg",sqle.getMessage());
                    }
                    result.put(meta.optString("code"), subResult);
                }
            }


            Data data = Data.get();
            data.setTenancy_id(tenentId);
            data.setStore_id(storeId);
            data.setOper(Oper.add);
            data.setType(Type.CHECK_ERROR_INFO_UPLOAD);

            JSONObject resj = new JSONObject();
            resj.put("report_date", reportDate);
            resj.put("check_result", result);
            resj.put("upload_time",new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            data.setData(Arrays.asList(resj));
            try {
                logger.info(">>>数据校验结果上传内容:"+data);
                JSONObject upresult= HttpUtil.post(PosPropertyUtil.getMsg("saas.url") + "/hqRest/post", JSONObject.fromObject(data), 3);
                logger.info(">>>数据校验结果上传结果:"+upresult);
            } catch (HttpRestException httpe) {
                logger.error(">>>数据校验结果上传失败", httpe);
            }
        } catch (Exception e) {
            logger.error("数据校验失败", e);
        }

    }

}
