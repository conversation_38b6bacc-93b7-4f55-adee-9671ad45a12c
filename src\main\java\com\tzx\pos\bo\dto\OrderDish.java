package com.tzx.pos.bo.dto;

import java.io.Serializable;
import java.util.List;

/**
 * 
 * <AUTHOR> 
 * @日期：2015年5月21日-下午4:24:39
 */

public class OrderDish implements Serializable
{

	/**
	 * 
	 */
	private static final long	serialVersionUID	= 1L;
	
	private String details_id;
	private String assist_num;
	private String seat_num;
	private String unit_id;
	private String item_count;
	private String item_remark;
	private String sale_mode;
	private String item_taste;
	private List<?> taste;
	private List<?> method;
	
	public String getSale_mode()
	{
		return sale_mode;
	}
	public void setSale_mode(String sale_mode)
	{
		this.sale_mode = sale_mode;
	}
	public String getItem_taste()
	{
		return item_taste;
	}
	public void setItem_taste(String item_taste)
	{
		this.item_taste = item_taste;
	}
	public String getDetails_id()
	{
		return details_id;
	}
	public void setDetails_id(String details_id)
	{
		this.details_id = details_id;
	}
	public String getAssist_num()
	{
		return assist_num;
	}
	public void setAssist_num(String assist_num)
	{
		this.assist_num = assist_num;
	}
	public String getSeat_num()
	{
		return seat_num;
	}
	public void setSeat_num(String seat_num)
	{
		this.seat_num = seat_num;
	}
	public String getUnit_id()
	{
		return unit_id;
	}
	public void setUnit_id(String unit_id)
	{
		this.unit_id = unit_id;
	}
	public String getItem_count()
	{
		return item_count;
	}
	public void setItem_count(String item_count)
	{
		this.item_count = item_count;
	}
	public String getItem_remark()
	{
		return item_remark;
	}
	public void setItem_remark(String item_remark)
	{
		this.item_remark = item_remark;
	}
	public List<?> getTaste()
	{
		return taste;
	}
	public void setTaste(List<?> taste)
	{
		this.taste = taste;
	}
	public List<?> getMethod()
	{
		return method;
	}
	public void setMethod(List<?> method)
	{
		this.method = method;
	}
}
