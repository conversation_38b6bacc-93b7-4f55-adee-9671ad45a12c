package com.tzx.base.listener;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import javax.jms.BytesMessage;
import javax.jms.Message;
import javax.jms.MessageListener;
import javax.jms.StreamMessage;
import javax.jms.TextMessage;

import net.sf.json.JSONObject;

import org.apache.log4j.Logger;

import com.tzx.base.common.util.ConsumerThreadPoolUtil;
import com.tzx.base.service.servlet.ProcessMessage;
import com.tzx.framework.common.constant.Constant;

/**
 * 门店端MQ消息消费者监听类.
 *
 * <AUTHOR> email:<EMAIL>
 * @version 1.0  2017-04-28
 * @see     
 * @since   JDK7.0
 * @update  
 */
public class PosConsumerMessageStoreListener implements MessageListener{
	
	// 定义MQ消息消费者线程池
	private static volatile ThreadPoolExecutor consumerThreadPool = null;
	
	private static Logger logger = Logger.getLogger(PosConsumerMessageStoreListener.class);

	/**
	 * @Description 获取线程池对象（通过双重检查锁实现）。
	 * @param
	 * @return ThreadPoolExecutor 线程池对象
	 * @exception <AUTHOR>                zhehong.qiu email:<EMAIL>
	 * @version 1.0 2017-04-28
	 * @see
	 */
	public static ThreadPoolExecutor getConsumerThreadPool() {
		if(consumerThreadPool == null){
			synchronized (ThreadPoolExecutor.class) {
				if(consumerThreadPool == null){
					/*
					 * 创建固定大小为1的线程池，使用ArrayBlockingQueue阻塞队列，队列大小为1万，线程数超过队列大小时的策略为重试。
					 */
					consumerThreadPool = new ThreadPoolExecutor(1,1,3,TimeUnit.SECONDS, new ArrayBlockingQueue<Runnable>(10000),
							new ThreadPoolExecutor.CallerRunsPolicy());
				}
			}
		}
		return consumerThreadPool;
	}

	/**
	 * @Description 获取消息队列中对象。
	 * @param message : 队列中的消息文本
	 * @return 
	 * @exception
	 * <AUTHOR> email:<EMAIL>
	 * @version 1.0  2017-04-28
	 * @see
	 */
	@Override
	public void onMessage(Message message) {
		logger.info("收到一条MQ消息");
		long startTime=System.currentTimeMillis();
		try
		{
			String msgBody = "";
			if (message instanceof TextMessage)
			{
				TextMessage textMsg = (TextMessage) message;
				msgBody = textMsg.getText();
			}
			else if (message instanceof StreamMessage)
			{
				StreamMessage streamMsg = (StreamMessage) message;
				msgBody = new String(streamMsg.readString().getBytes()); // 目前用的默认编码，所以要处理好操作系统的语言环境
			}
			else if (message instanceof BytesMessage)
			{
				BytesMessage bytesMsg = (BytesMessage) message;
				msgBody = bytesMsg.readUTF();
			}
			else
			{
				logger.error("不支持的MQ消息类型 ------>> " + message);
			}

			if ((Constant.getSystemMap().get("sotreorhq")).equals("boh"))
			{
				handleMsg(msgBody);
				long endTime=System.currentTimeMillis();
				logger.info("MQ消息处理完毕,耗时:"+(endTime-startTime)+"ms");
			}
		}
		catch (Exception e)
		{
			logger.error("处理MQ消息异常", e);
		}
	}
	
	/**
	 * 处理消息
	 * @param data
	 * @return
	 */
	public void handleMsg(String data) throws Exception {
		boolean newVersion = false;
		JSONObject getJson = JSONObject.fromObject(data);
		if (getJson.isEmpty() || !getJson.containsKey("tenancy_id") || "".equals(getJson.optString("tenancy_id")))
		{
			newVersion = true;
		}

		if(newVersion){	//新版本下发 mq 通过文件下发内容
			//mq 新版本接收消息，通过文件获取消息
			String sy = getJson.optString("sy");
			if(sy != null && "1".equals(sy)){//异步处理
				logger.info("异步处理新版本下发消息");
				ConsumerThreadPoolUtil.getConsumerThreadPool().execute(new PosConsumerThreadPoolTask(data));
			} else {//同步处理消息
				logger.info("同步处理新版本下发消息");
				new ProcessMessage().doExecute(data);
			}
		} else {//原来版本下发
			logger.info("同步处理老版本下发消息");
			new ProcessMessage().doExecute(data);
		}
	}

}
