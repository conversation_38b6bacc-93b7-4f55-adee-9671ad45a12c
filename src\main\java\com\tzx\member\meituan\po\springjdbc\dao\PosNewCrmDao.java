package com.tzx.member.meituan.po.springjdbc.dao;


import java.util.List;

import com.tzx.pos.base.dao.BaseDao;

import net.sf.json.JSONObject;

public interface PosNewCrmDao extends BaseDao {
	
	
	String NAME = "com.tzx.member.meituan.po.springjdbc.dao.imp.PosNewCrmDaoImp";
	/**
	 * 获取SignKey
	 * @param tenancy_id
	 * @param stor_id
	 * @return
	 * @throws Exception
	 */
	@Deprecated
	public String getSignKen(String tenancy_id,Integer stor_id) throws Exception;
	
	/** 获取TOken
	 * @param tenancyId
	 * @param storId
	 * @param employeeId
	 * @return
	 * @throws Exception
	 */
	public String getTokenByThirdLink(String tenancyId,Integer storId,Integer employeeId) throws Exception;

	/** 查出最后一个会员卡Id和会员Id
	 * @param tenantId
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> findLastData(String tenantId) throws Exception;

	/** 根据会员卡号取会员信息
	 * @param tenantId
	 * @param cardCode
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> findDataByCode(String tenantId, String cardCode) throws Exception;

	/** 根据操作员Id取操作员名字
	 * @param tenantId
	 * @param operatorId
	 * @return
	 * @throws Exception
	 */
	public String findOperator(String tenantId, Integer operatorId) throws Exception;

	/** 付款方式
	 * @param type
	 * @param objs
	 * @return
	 * @throws Exception
	 */
	public int paymentType(String type, Object[] objs) throws Exception;

}
