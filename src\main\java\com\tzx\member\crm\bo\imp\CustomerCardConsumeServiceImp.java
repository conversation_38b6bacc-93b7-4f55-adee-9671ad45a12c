package com.tzx.member.crm.bo.imp;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.tzx.pos.base.util.ParamUtil;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.springframework.stereotype.Service;

import com.tzx.framework.common.constant.Code;
import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.Tools;
import com.tzx.member.common.entity.CrmCardTradingListEntity;
import com.tzx.member.common.entity.PosCustomerOperateListEntity;
import com.tzx.member.crm.bo.CustomerCardConsumeService;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.DateUtil;

@Service(CustomerCardConsumeService.NAME)
public class CustomerCardConsumeServiceImp extends CustomerServiceImp implements CustomerCardConsumeService
{

	public final String	CUSTOMER_CARD_CONSUME_XF	= "0";
	public final String	CUSTOMER_CARD_CONSUME_FXF	= "1";

	@SuppressWarnings("unchecked")
	@Override
	public void customerCardConsumePost(Data param, Data resData) throws Exception
	{
		List<JSONObject> listMaps = (List<JSONObject>) param.getData();

		if (listMaps == null || listMaps.size() <= 0 || listMaps.isEmpty())
		{
			throw new SystemException(PosErrorCode.PARAM_ERROR);
		}
		this.customerCardConsumePost(param.getTenancy_id(), param.getStore_id(), JSONObject.fromObject(listMaps.get(0)), resData, Type.CUSTOMER_CARD_CONSUME.name());
	}

	@Override
	public void customerCardConsumePost(String tenancyId, int storeId, JSONObject param, Data resData, String actionType) throws Exception
	{
		String reportDateStr = param.optString("report_date");
		Integer shiftId = param.optInt("shift_id");
		String optNum = param.optString("opt_num");
		String posNum = param.optString("pos_num");
		String thirdBillCode = param.optString("bill_code");
		String batchNum = param.optString("batch_num");
		String chanel = param.optString("chanel");
		String cardCode = param.optString("card_code");
		String thirdCode = param.optString("third_code");
		String dynamicCode = param.optString("dynamic_code");
		String cardPassword = param.optString("cardpassword");
		Double consumeTotalmoney = param.optDouble("consume_totalmoney");
		Double consumeCardmoney = param.optDouble("consume_cardmoney");
		Double consumeCredit = param.optDouble("consume_credit");
		Double consumeCreditmoney = param.optDouble("consume_creditmoney");
		String isCheck = param.optString("ischeck");

		Date reportDate = null;
		if (Tools.hv(reportDateStr))
		{
			reportDate = DateUtil.parseDate(reportDateStr);
		}
		else
		{
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_REPORT_DATE);
		}

		if (Tools.isNullOrEmpty(optNum))
		{
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_OPT_NUM);
		}

		if ("Y".equals(isCheck))
		{
			customerDao.checkKssy(reportDate, optNum, storeId,shiftId,posNum);
		}

		String optName = customerDao.getEmpNameById(optNum, tenancyId, storeId);

		Timestamp currentTime = DateUtil.currentTimestamp();

		if (Tools.isNullOrEmpty(thirdBillCode))
		{
			try
			{
				// 生成新的账单号和流水单号
				JSONObject object = new JSONObject();
				object.element("store_id", storeId);
				object.element("busi_date", reportDateStr);
				thirdBillCode = codeService.getCode(tenancyId, Code.POS_THIRD_PAYMENT_ORDER_NUM, object);// 调用统一接口来实现
				if (Tools.isNullOrEmpty(thirdBillCode))
				{
					throw new SystemException(PosErrorCode.GET_BILLNUM_SERVICE_ERROR);
				}
			}
			catch (Exception e)
			{
				logger.info("会员操作：" + ExceptionMessage.getExceptionMessage(e));
				throw new SystemException(PosErrorCode.GET_BILLNUM_SERVICE_ERROR);
			}
		}

		PosCustomerOperateListEntity customerOperate = new PosCustomerOperateListEntity();
		customerOperate.setTenancy_id(tenancyId);
		customerOperate.setStore_id(storeId);
		customerOperate.setBusiness_date(reportDate);
		customerOperate.setOperator_id(Integer.parseInt(optNum));
		customerOperate.setPos_num(posNum);
		customerOperate.setShift_id(shiftId);
		customerOperate.setOperate_time(currentTime);
		customerOperate.setChanel(chanel);
		customerOperate.setService_type(SysDictionary.SERVICE_TYPE_CONSUME);
		customerOperate.setOperat_type(SysDictionary.OPERAT_TYPE_XF);
		customerOperate.setCard_code(cardCode);
		customerOperate.setThird_bill_code(thirdBillCode);
		customerOperate.setBatch_num(batchNum);
		customerOperate.setTrade_amount(consumeCardmoney);
		customerOperate.setTrade_credit(0d);
		customerOperate.setDeposit(0d);
		customerOperate.setSales_price(0d);
		customerOperate.setOperate_state(SysDictionary.THIRD_PAY_STATUS_PAYING);
		customerOperate.setCancel_state(SysDictionary.CUSTOMER_OPERATE_CANCEL_STATE_N);
		customerOperate.setRequest_status(SysDictionary.REQUEST_STATUS_ING);
		customerOperate.setLast_query_time(currentTime);
		customerOperate.setQuery_count(0);
		customerOperate.setAction_type(actionType);

		try
		{
			JSONObject requestJson = new JSONObject();
			requestJson.put("card_code", cardCode);
			requestJson.put("third_code", thirdCode);
			requestJson.put("dynamic_code", dynamicCode);
			requestJson.put("cardpassword", cardPassword);
			requestJson.put("consume_totalmoney", consumeTotalmoney);
			requestJson.put("consume_cardmoney", consumeCardmoney);
			requestJson.put("consume_credit", consumeCredit);
			requestJson.put("consume_creditmoney", consumeCreditmoney);
			requestJson.put("bill_code", thirdBillCode);
			requestJson.put("batch_num", batchNum);
			requestJson.put("chanel", chanel);
			requestJson.put("business_date", reportDateStr);
			requestJson.put("shift_id", shiftId);
			requestJson.put("pos_num", posNum);
			requestJson.put("operator", optName);
			requestJson.put("operator_id", optNum);
			requestJson.put("updatetime", DateUtil.format(currentTime));

			List<JSONObject> requestList = new ArrayList<JSONObject>();
			requestList.add(requestJson);

			Data requestData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
			requestData.setType(Type.CUSTOMER_CARD_CONSUME);
			requestData.setOper(Oper.add);
			requestData.setData(requestList);

			this.commonPost(JSONObject.fromObject(requestData).toString(), resData);

			this.customerCardConsumeResult(tenancyId, storeId, customerOperate, resData, currentTime);
		}
		catch (Exception e)
		{
			// TODO Auto-generated catch block
			e.printStackTrace();

			resData.setCode(Constant.CODE_CONN_EXCEPTION);
			resData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
		}

		customerDao.insertPosCustomerOperateList(tenancyId, storeId, customerOperate);
	}

	@Override
	public Data queryCustomerCardConsume(String tenancyId, int storeId, JSONObject paramJson) throws Exception
	{
		String card_code = paramJson.optString("card_code");
		String billCode = paramJson.optString("third_bill_code");
		String batchNum = paramJson.optString("batch_num");

		PosCustomerOperateListEntity customerOperate = customerDao.queryPosCustomerOperateListByCustomerCard(tenancyId, storeId,card_code, billCode, batchNum,SysDictionary.OPERAT_TYPE_XF);

		return this.queryCustomerCardConsume(tenancyId, storeId, customerOperate);
	}

	@Override
	public Data queryCustomerCardConsume(String tenancyId, int storeId, PosCustomerOperateListEntity customerOperate) throws Exception
	{
		Timestamp currentTime = DateUtil.currentTimestamp();

		Data resData = Data.get();

		JSONObject requestJson = new JSONObject();
		requestJson.put("card_code", customerOperate.getCard_code());
		requestJson.put("third_code", customerOperate.getCard_code());
		requestJson.put("third_bill_code", customerOperate.getThird_bill_code());
		requestJson.put("batch_num", customerOperate.getBatch_num());
		requestJson.put("type", this.CUSTOMER_CARD_CONSUME_XF);

		List<JSONObject> requestList = new ArrayList<JSONObject>();
		requestList.add(requestJson);

		Data requestData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
		requestData.setType(Type.CUSTOMER_BILL);
		requestData.setOper(Oper.find);
		requestData.setData(requestList);

		this.commonPost(JSONObject.fromObject(requestData).toString(), resData);

		this.customerCardConsumeResult(tenancyId, storeId, customerOperate, resData, currentTime);

		try
		{
			customerDao.updatePosCustomerOperateList(tenancyId, storeId, customerOperate);
		}
		catch (Exception e)
		{
			e.printStackTrace();
			logger.error("会员卡消费记录pos_customer_operate_list报错:", e);
		}
		return resData;
	}

	private void customerCardConsumeResult(String tenancyId, int storeId, PosCustomerOperateListEntity customerOperate, Data resData, Timestamp currentTime) throws Exception
	{
		currentTime = DateUtil.currentTimestamp();
		if (null != resData && Constant.CODE_SUCCESS == resData.getCode() && resData.isSuccess() && resData.getData().size() > 0)
		{
			CrmCardTradingListEntity cardTrading = new CrmCardTradingListEntity();

			JSONObject resultObj = JSONObject.fromObject(resData.getData().get(0));
			JSONArray list = resultObj.optJSONArray("crm_card_trading_list");

			String cardCode = resultObj.optString("card_code");
			String mobil = resultObj.optString("mobil");
			String customerName = resultObj.optString("name");
			Integer cardClassId = resultObj.optInt("card_class_id");
			String billCode = resultObj.optString("bill_code");
			Integer customerId = null;
			Integer cardId = null;
			Date businessDate = customerOperate.getBusiness_date();
			Integer shiftId = customerOperate.getShift_id();
			String chanel = customerOperate.getChanel();
			String posNum = customerOperate.getPos_num();
			String operatType = customerOperate.getOperat_type();
			String batchNum = customerOperate.getBatch_num();
			Integer operatorId = customerOperate.getOperator_id();
			Double billMoney = customerOperate.getTrade_amount();
			String thirdBillCode = resultObj.optString("third_bill_code");
			Double mainTrading = resultObj.optDouble("main_trading");
			Double rewardTrading = resultObj.optDouble("reward_trading");
			Double mainBalance = resultObj.optDouble("main_balance");
			Double rewardBalance = resultObj.optDouble("reward_balance");
			Double totalBalance = 0d;
			Double mainOriginal = 0d;
			Double rewardOriginal = 0d;
			Double deposit = 0d;
			String operator = null;
			String billCodeOriginal = null;

			Timestamp operateTime = DateUtil.parseTimestamp(resultObj.optString("operate_time"));
			Timestamp lastUpdatetime = DateUtil.parseTimestamp(resultObj.optString("operate_time"));

			if (null != list && list.size() > 0)
			{
				JSONObject trading = list.getJSONObject(0);

				customerId = trading.optInt("customer_id");
				cardId = trading.optInt("card_id");

				businessDate = DateUtil.parseDate(trading.optString("business_date"));
				shiftId = trading.optInt("shift_id");
				chanel = trading.optString("chanel");
				operatType = trading.optString("operat_type");
				batchNum = trading.optString("batch_num");

				thirdBillCode = trading.optString("third_bill_code");
				billCodeOriginal = trading.optString("bill_code_original");
				operatorId = trading.optInt("operator_id");
				operator = trading.optString("operator");

				billMoney = trading.optDouble("bill_money");
				mainTrading = trading.optDouble("main_trading");
				rewardTrading = trading.optDouble("reward_trading");
				mainOriginal = trading.optDouble("main_original");
				rewardOriginal = trading.optDouble("reward_original");
				totalBalance = trading.optDouble("total_balance");
				mainBalance = trading.optDouble("main_balance");
				rewardBalance = trading.optDouble("reward_balance");
				deposit = trading.optDouble("deposit");

				operateTime = DateUtil.parseTimestamp(trading.optString("operate_time"));
				lastUpdatetime = DateUtil.parseTimestamp(trading.optString("last_updatetime"));
			}

			cardTrading.setTenancy_id(tenancyId);
			cardTrading.setStore_id(storeId);
			cardTrading.setBusiness_date(businessDate);
			cardTrading.setShift_id(shiftId);
			cardTrading.setChanel(chanel);
			cardTrading.setOperat_type(operatType);
			cardTrading.setCustomer_id(customerId);
			cardTrading.setCard_id(cardId);
			cardTrading.setCard_code(cardCode);
			cardTrading.setName(customerName);
			cardTrading.setMobil(mobil);
			cardTrading.setCard_class_id(cardClassId);
			cardTrading.setBill_code(billCode);
			cardTrading.setThird_bill_code(thirdBillCode);
			cardTrading.setBill_code_original(billCodeOriginal);
			cardTrading.setBatch_num(batchNum);
			cardTrading.setBill_money(billMoney);
			cardTrading.setMain_trading(mainTrading);
			cardTrading.setReward_trading(rewardTrading);
			cardTrading.setMain_original(mainOriginal);
			cardTrading.setReward_original(rewardOriginal);
			cardTrading.setTotal_balance(totalBalance);
			cardTrading.setMain_balance(mainBalance);
			cardTrading.setReward_balance(rewardBalance);
			cardTrading.setDeposit(deposit);
			cardTrading.setOperator_id(operatorId);
			cardTrading.setOperator(operator);
			cardTrading.setOperate_time(operateTime);
			cardTrading.setLast_updatetime(lastUpdatetime);
			cardTrading.setStore_updatetime(currentTime);
			cardTrading.setPosNum(posNum);

			try
			{
				customerDao.insertCrmCardTradingList(tenancyId, storeId, cardTrading);
			}
			catch (Exception e)
			{
				e.printStackTrace();
				logger.error("会员卡消费记录crm_card_trading_list报错:", e);
			}

			customerOperate.setCustomer_id(customerId);
			customerOperate.setCard_id(cardId);
			customerOperate.setMobil(mobil);
			customerOperate.setCustomer_name(customerName);
			customerOperate.setCard_class_id(cardClassId);
			customerOperate.setBill_code(billCode);
			customerOperate.setFinish_time(currentTime);
			customerOperate.setOperate_state(SysDictionary.THIRD_PAY_STATUS_SUCCESS);
			customerOperate.setRequest_status(SysDictionary.REQUEST_STATUS_COMPLETE);
		}
		else if (Constant.CODE_CONN_EXCEPTION == resData.getCode())
		{
			customerOperate.setOperate_state(SysDictionary.THIRD_PAY_STATUS_PAYING);
			customerOperate.setRequest_status(SysDictionary.REQUEST_STATUS_FAILURE);
			customerOperate.setRequest_code(String.valueOf(resData.getCode()));
			customerOperate.setRequest_msg(resData.getMsg());
		}
		else
		{
			customerOperate.setFinish_time(currentTime);
			customerOperate.setOperate_state(SysDictionary.THIRD_PAY_STATUS_FAIL);
			customerOperate.setRequest_status(SysDictionary.REQUEST_STATUS_COMPLETE);
			customerOperate.setRequest_code(String.valueOf(resData.getCode()));
			customerOperate.setRequest_msg(resData.getMsg());
		}
	}

	@SuppressWarnings("unchecked")
	@Override
	public void customerCardCancelConsumePost(Data param, Data resData, String isCheck) throws Exception
	{
		List<JSONObject> listMaps = (List<JSONObject>) param.getData();

		if (listMaps == null || listMaps.size() <= 0 || listMaps.isEmpty())
		{
			throw new SystemException(PosErrorCode.PARAM_ERROR);
		}
		this.customerCardCancelConsumePost(param.getTenancy_id(), param.getStore_id(), JSONObject.fromObject(listMaps.get(0)), resData, isCheck, Type.CUSTOMER_CARD_CONSUME.name());
	}

	@Override
	public void customerCardCancelConsumePost(String tenancyId, int storeId, JSONObject param, Data resData, String isCheckBillConsume, String actionType) throws Exception
	{
		String cardCode = param.optString("card_code");
		String thirdCode = param.optString("third_code");
		String cardPassword = param.optString("cardpassword");
		String oldBillCode = param.optString("old_bill_code");
		String thirdBillCode = param.optString("bill_code");
		String batchNum = param.optString("batch_num");
		String reportDateStr = param.optString("report_date");
		Integer shiftId = param.optInt("shift_id");
		String optNum = param.optString("opt_num");
		String posNum = param.optString("pos_num");
		String chanel = param.optString("chanel");
		String isCheck = param.optString("ischeck");
		Double revokedTrading = param.optDouble("revoked_trading");

		Date reportDate = null;
		if (Tools.hv(reportDateStr))
		{
			reportDate = DateUtil.parseDate(reportDateStr);
		}
		else
		{
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_REPORT_DATE);
		}

		if (Tools.isNullOrEmpty(optNum))
		{
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_OPT_NUM);
		}

		if (Tools.isNullOrEmpty(cardCode) && Tools.isNullOrEmpty(thirdCode))
		{
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_CARD_CODE);
		}

		if (Tools.isNullOrEmpty(oldBillCode) && Tools.isNullOrEmpty(thirdBillCode))
		{
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_OLD_BILL_CODE);
		}

		Timestamp currentTime = DateUtil.currentTimestamp();

		if ("Y".equals(isCheck))
		{
			customerDao.checkKssy(reportDate, optNum, storeId,shiftId,posNum);
		}

		if ("Y".equals(isCheckBillConsume))
		{
			// 账单消费不允许在会员操作界面进行撤销
			StringBuilder queryBillCodeSql = new StringBuilder("select count(1) from pos_bill_payment where tenancy_id=? and store_id=? and report_date=? and shift_id=? and number=? and bill_code=? ");
			int totalCount = customerDao.queryForInt(queryBillCodeSql.toString(), new Object[]
			{ tenancyId, storeId, reportDate, shiftId, cardCode, oldBillCode });

			if (totalCount > 0)
			{
				throw new SystemException(PosErrorCode.HAS_PAYMENTED_CAN_NOT_CANCEL);
			}
		}

		String optName = customerDao.getEmpNameById(optNum, tenancyId, storeId);

		//新增会员操作记录
		PosCustomerOperateListEntity customerOperate = new PosCustomerOperateListEntity();
		customerOperate.setTenancy_id(tenancyId);
		customerOperate.setStore_id(storeId);
		customerOperate.setBusiness_date(reportDate);
		customerOperate.setOperator_id(Integer.parseInt(optNum));
		customerOperate.setPos_num(posNum);
		customerOperate.setShift_id(shiftId);
		customerOperate.setOperate_time(currentTime);
		customerOperate.setChanel(chanel);
		customerOperate.setService_type(SysDictionary.SERVICE_TYPE_CONSUME);
		customerOperate.setOperat_type(SysDictionary.OPERAT_TYPE_FXF);
		customerOperate.setCard_code(cardCode);
		customerOperate.setThird_bill_code(thirdBillCode);
		customerOperate.setBatch_num(batchNum);
		customerOperate.setBill_code_original(oldBillCode);
		customerOperate.setTrade_amount(0d);
		customerOperate.setTrade_credit(0d);
		customerOperate.setDeposit(0d);
		customerOperate.setSales_price(0d);
		customerOperate.setOperate_state(SysDictionary.THIRD_PAY_STATUS_REFUND);
		customerOperate.setCancel_state(SysDictionary.CUSTOMER_OPERATE_CANCEL_STATE_N);
		customerOperate.setRequest_status(SysDictionary.REQUEST_STATUS_ING);
		customerOperate.setLast_query_time(currentTime);
		customerOperate.setQuery_count(0);
		customerOperate.setAction_type(actionType);
		if (!revokedTrading.isNaN() && Math.abs(revokedTrading) > 0)
		{
			customerOperate.setTrade_amount(Math.abs(revokedTrading));
		}

		//组织参数,请求撤销消费
		JSONObject requestJson = new JSONObject();
		requestJson.put("card_code", cardCode);
		requestJson.put("third_code", thirdCode);
		requestJson.put("cardpassword", cardPassword);
		requestJson.put("bill_code", thirdBillCode);
		requestJson.put("batch_num", batchNum);
		requestJson.put("old_bill_code", oldBillCode);
		requestJson.put("chanel", chanel);
		requestJson.put("business_date", reportDateStr);
		requestJson.put("shift_id", shiftId);
		requestJson.put("operator_id", optNum);
		requestJson.put("operator", optName);
		requestJson.put("pos_num", posNum);
		requestJson.put("updatetime", DateUtil.format(currentTime));
		if (!revokedTrading.isNaN() && Math.abs(revokedTrading) > 0)
		{
			requestJson.put("revoked_trading", Math.abs(revokedTrading));
		}

		List<JSONObject> requestList = new ArrayList<JSONObject>();
		requestList.add(requestJson);

		Data requestData = Data.get(tenancyId, storeId, 0);
		requestData.setType(Type.CUSTOMER_CARD_CONSUME);
		requestData.setOper(Oper.update);
		requestData.setData(requestList);

		this.commonPost(JSONObject.fromObject(requestData).toString(), resData);

		this.customerCardCancelConsumeResult(tenancyId, storeId, customerOperate, resData, currentTime);
		
		try
		{
			customerDao.insertPosCustomerOperateList(tenancyId, storeId, customerOperate);
		}
		catch (Exception e)
		{
			e.printStackTrace();
			logger.error("会员卡撤销消费记录pos_customer_operate_list报错:", e);
		}
	}

	@Override
	public Data queryCustomerCardCancelConsume(String tenancyId, int storeId, PosCustomerOperateListEntity customerOperate) throws Exception
	{
		Timestamp currentTime = DateUtil.currentTimestamp();

		Data resData = Data.get();

		JSONObject requestJson = new JSONObject();
		requestJson.put("card_code", customerOperate.getCard_code());
		requestJson.put("third_code", customerOperate.getCard_code());
		requestJson.put("third_bill_code", customerOperate.getThird_bill_code());
		requestJson.put("batch_num", customerOperate.getBatch_num());
		requestJson.put("type", this.CUSTOMER_CARD_CONSUME_FXF);

		List<JSONObject> requestList = new ArrayList<JSONObject>();
		requestList.add(requestJson);

		Data requestData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
		requestData.setType(Type.CUSTOMER_BILL);
		requestData.setOper(Oper.find);
		requestData.setData(requestList);

		this.commonPost(JSONObject.fromObject(requestData).toString(), resData);

//		this.customerCardCancelConsumeResult(tenancyId, storeId, customerOperate, resData, currentTime);

		if (null != resData && Constant.CODE_SUCCESS == resData.getCode() && resData.getData().size() > 0)
		{
			JSONObject resultObj = JSONObject.fromObject(resData.getData().get(0));
			JSONArray list = resultObj.optJSONArray("crm_card_trading_list");

			String mobil = resultObj.optString("mobil");
			String customerName = resultObj.optString("name");
			Integer cardClassId = resultObj.optInt("card_class_id");
			String billCode = resultObj.optString("bill_code");
			
			customerOperate.setMobil(mobil);
			customerOperate.setCustomer_name(customerName);
			customerOperate.setCard_class_id(cardClassId);
			customerOperate.setBill_code(billCode);
			customerOperate.setFinish_time(currentTime);
			customerOperate.setOperate_state(SysDictionary.THIRD_PAY_STATUS_REFUND_SUCCESS);
			customerOperate.setRequest_status(SysDictionary.REQUEST_STATUS_COMPLETE);
			
			if (null != list && list.size() > 0)
			{
				JSONObject trading = list.getJSONObject(0);

				CrmCardTradingListEntity cardTrading = new CrmCardTradingListEntity();
				cardTrading.setTenancy_id(tenancyId);
				cardTrading.setStore_id(storeId);
				cardTrading.setBusiness_date(DateUtil.parseDate(trading.optString("business_date")));
				cardTrading.setShift_id(trading.optInt("shift_id"));
				cardTrading.setChanel(trading.optString("chanel"));
				cardTrading.setOperat_type(trading.optString("operat_type"));
				cardTrading.setCustomer_id(trading.optInt("customer_id"));
				cardTrading.setCard_id(trading.optInt("card_id"));
				cardTrading.setCard_code(trading.optString("card_code"));
				cardTrading.setName(customerName);
				cardTrading.setMobil(mobil);
				cardTrading.setCard_class_id(cardClassId);
				cardTrading.setBill_code(billCode);
				cardTrading.setThird_bill_code(trading.optString("third_bill_code"));
				cardTrading.setBill_code_original(trading.optString("bill_code_original"));
				cardTrading.setBatch_num(trading.optString("batch_num"));
				cardTrading.setBill_money(trading.optDouble("bill_money"));
				cardTrading.setMain_trading(trading.optDouble("main_trading"));
				cardTrading.setReward_trading(trading.optDouble("reward_trading"));
				cardTrading.setMain_original(trading.optDouble("main_original"));
				cardTrading.setReward_original(trading.optDouble("reward_original"));
				cardTrading.setTotal_balance(trading.optDouble("total_balance"));
				cardTrading.setMain_balance(trading.optDouble("main_balance"));
				cardTrading.setReward_balance(trading.optDouble("reward_balance"));
				cardTrading.setDeposit(trading.optDouble("deposit"));
				cardTrading.setOperator_id(trading.optInt("operator_id"));
				cardTrading.setOperator(trading.optString("operator"));
				cardTrading.setOperate_time(DateUtil.parseTimestamp(trading.optString("operate_time")));
				cardTrading.setLast_updatetime(DateUtil.parseTimestamp(trading.optString("last_updatetime")));
				cardTrading.setStore_updatetime(currentTime);
				cardTrading.setPosNum(ParamUtil.getStringValueByObject(trading, "pos_num"));

				try
				{
					customerDao.insertCrmCardTradingList(tenancyId, storeId, cardTrading);
				}
				catch (Exception e)
				{
					e.printStackTrace();
					logger.error("会员卡撤销消费记录crm_card_trading_list报错:", e);
				}
			}
		}
		else if (Constant.CODE_CONN_EXCEPTION == resData.getCode())
		{
			customerOperate.setOperate_state(SysDictionary.THIRD_PAY_STATUS_PAYING);
			customerOperate.setRequest_status(SysDictionary.REQUEST_STATUS_FAILURE);
			customerOperate.setRequest_code(String.valueOf(resData.getCode()));
			customerOperate.setRequest_msg(resData.getMsg());
		}
		else
		{
			customerOperate.setFinish_time(currentTime);
			customerOperate.setOperate_state(SysDictionary.THIRD_PAY_STATUS_FAIL);
			customerOperate.setRequest_status(SysDictionary.REQUEST_STATUS_COMPLETE);
			customerOperate.setRequest_code(String.valueOf(resData.getCode()));
			customerOperate.setRequest_msg(resData.getMsg());
		}
		
		try
		{
			customerDao.updatePosCustomerOperateList(tenancyId, storeId, customerOperate);
		}
		catch (Exception e)
		{
			e.printStackTrace();
			logger.error("会员卡撤销消费记录pos_customer_operate_list报错:", e);
		}
		return resData;
	}

	private void customerCardCancelConsumeResult(String tenancyId, int storeId, PosCustomerOperateListEntity customerOperate, Data resData, Timestamp currentTime) throws Exception
	{
		if (Constant.CODE_SUCCESS == resData.getCode() && resData.isSuccess())
		{
			CrmCardTradingListEntity cardTrading = new CrmCardTradingListEntity();

			if (null != resData && resData.getData().size() > 0)
			{
				JSONObject resultObj = JSONObject.fromObject(resData.getData().get(0));
				JSONArray list = resultObj.optJSONArray("crm_card_trading_list");

				if (null != list && list.size() > 0)
				{
					JSONObject trading = list.getJSONObject(0);

					Integer customerId = trading.optInt("customer_id");
					String mobil = resultObj.optString("mobil");
					String customerName = resultObj.optString("name");
					Integer cardClassId = resultObj.optInt("card_class_id");
					Integer cardId = trading.optInt("card_id");
					String billCode = trading.optString("bill_code");

					customerOperate.setCustomer_id(customerId);
					// customerOperate.setCustomer_code(customer_code);
					customerOperate.setMobil(mobil);
					customerOperate.setCustomer_name(customerName);
					customerOperate.setCard_class_id(cardClassId);
					customerOperate.setCard_id(cardId);
					customerOperate.setBill_code(billCode);
					customerOperate.setFinish_time(currentTime);
					customerOperate.setOperate_state(SysDictionary.THIRD_PAY_STATUS_REFUND_SUCCESS);
					customerOperate.setRequest_status(SysDictionary.REQUEST_STATUS_COMPLETE);
					// customerOperate.setRequest_code(request_code);
					// customerOperate.setRequest_msg(request_msg);

					cardTrading.setTenancy_id(tenancyId);
					cardTrading.setStore_id(storeId);
					cardTrading.setBusiness_date(DateUtil.parseDate(trading.optString("business_date")));
					cardTrading.setShift_id(trading.optInt("shift_id"));
					cardTrading.setChanel(trading.optString("chanel"));
					cardTrading.setOperat_type(trading.optString("operat_type"));
					cardTrading.setCustomer_id(customerId);
					cardTrading.setCard_id(cardId);
					cardTrading.setCard_code(trading.optString("card_code"));
					cardTrading.setName(customerName);
					cardTrading.setMobil(mobil);
					cardTrading.setCard_class_id(cardClassId);
					cardTrading.setBill_code(billCode);
					cardTrading.setThird_bill_code(trading.optString("third_bill_code"));
					cardTrading.setBill_code_original(trading.optString("bill_code_original"));
					cardTrading.setBatch_num(trading.optString("batch_num"));
					cardTrading.setBill_money(trading.optDouble("bill_money"));
					cardTrading.setMain_trading(trading.optDouble("main_trading"));
					cardTrading.setReward_trading(trading.optDouble("reward_trading"));
					cardTrading.setMain_original(trading.optDouble("main_original"));
					cardTrading.setReward_original(trading.optDouble("reward_original"));
					cardTrading.setTotal_balance(trading.optDouble("total_balance"));
					cardTrading.setMain_balance(trading.optDouble("main_balance"));
					cardTrading.setReward_balance(trading.optDouble("reward_balance"));
					cardTrading.setDeposit(trading.optDouble("deposit"));
					cardTrading.setOperator_id(trading.optInt("operator_id"));
					cardTrading.setOperator(trading.optString("operator"));
					cardTrading.setOperate_time(DateUtil.parseTimestamp(trading.optString("operate_time")));
					cardTrading.setLast_updatetime(DateUtil.parseTimestamp(trading.optString("last_updatetime")));
					cardTrading.setStore_updatetime(currentTime);
					cardTrading.setPosNum(trading.optString("pos_num"));

					try
					{
						customerDao.insertCrmCardTradingList(tenancyId, storeId, cardTrading);
					}
					catch (Exception e)
					{
						e.printStackTrace();
						logger.error("会员卡撤销消费记录crm_card_trading_list报错:", e);
					}
				}
			}
		}
		else if (Constant.CODE_CONN_EXCEPTION == resData.getCode())
		{
			customerOperate.setOperate_state(SysDictionary.THIRD_PAY_STATUS_PAYING);
			customerOperate.setRequest_status(SysDictionary.REQUEST_STATUS_FAILURE);
			customerOperate.setRequest_code(String.valueOf(resData.getCode()));
			customerOperate.setRequest_msg(resData.getMsg());
		}
		else
		{
			customerOperate.setFinish_time(currentTime);
			customerOperate.setOperate_state(SysDictionary.THIRD_PAY_STATUS_FAIL);
			customerOperate.setRequest_status(SysDictionary.REQUEST_STATUS_COMPLETE);
			customerOperate.setRequest_code(String.valueOf(resData.getCode()));
			customerOperate.setRequest_msg(resData.getMsg());
		}
	}

}
