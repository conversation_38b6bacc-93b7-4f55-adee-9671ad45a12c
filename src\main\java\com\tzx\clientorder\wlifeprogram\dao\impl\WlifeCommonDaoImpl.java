package com.tzx.clientorder.wlifeprogram.dao.impl;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.jdbc.support.rowset.SqlRowSet;

import com.tzx.clientorder.wlifeprogram.dao.WlifeCommonDao;
import com.tzx.framework.common.util.Tools;
import com.tzx.framework.common.util.dao.impl.GenericDaoImpl;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.DateUtil;

import net.sf.json.JSONObject;

public class WlifeCommonDaoImpl extends GenericDaoImpl implements WlifeCommonDao
{
	private static final Logger	logger	= Logger.getLogger(WlifeCommonDao.class);
	
	@Override
	public String getSysParameter(String tenancyId, int storeId, String para) throws Exception
	{
		String sql = "select trim(para_value) as para_value from sys_parameter where para_code = ? and valid_state='1'";

		SqlRowSet rs = jdbcTemplate.queryForRowSet(sql, new Object[]
		{ para });

		if (rs.next())
		{
			return rs.getString("para_value");
		}
		return "";
	}
	
    @Override
    public List<JSONObject> getParam(String tenancyId) throws Exception {
        StringBuffer sql = new StringBuffer();
        sql.append(" select para_code,para_value,para_defaut from sys_parameter where model_name = '微生活点餐平台对接' and valid_state = '1'");
        List<JSONObject> list = this.query4Json(tenancyId, sql.toString());
        return list;
    }
    
    @Override
    public String getMemberType(String tenancyId) throws Exception {
        StringBuffer sql = new StringBuffer();
        sql.append(" select para_value from sys_parameter where para_code = 'CustomerType' limit 1");
        return this.getString(tenancyId, sql.toString());
    }
	
	@Override
	public Date getReportDate(String tenantId, Integer storeId) throws Exception
	{
		String qReportDate = new String("select max(report_date) as report_date from pos_opt_state where content = ? and store_id = ? and tenancy_id = ?");
		SqlRowSet rst = this.query4SqlRowSet(qReportDate, new Object[]
		{ SysDictionary.OPT_STATE_DAYBEGAIN,storeId, tenantId });

		if (rst.next())
		{
			// 这段要判断取出的日期是否是null
			String date = rst.getString("report_date");
			if (StringUtils.isEmpty(date))
			{
				return new Date();
			}
			return DateUtil.parseDate(date);
		}
		else
		{
			return new Date();
		}
	}
	
	@Override
	public String getEmpNameById(String optNum, String tenancyId, Integer storeId)
	{
		if (false == StringUtils.isNumeric(optNum))
		{
			return optNum;
		}

		StringBuilder sbSql = new StringBuilder();
		sbSql.append(" select em.id,em.name from employee as em left join user_authority as ua on ua.tenancy_id=em.tenancy_id and ua.store_id=em.store_id and ua.employee_id=em.id where em.tenancy_id = ? and (em.id = ? or ua.pos_user_name = ?) and em.store_id = ? ");
		SqlRowSet rs = this.jdbcTemplate.queryForRowSet(sbSql.toString(), new Object[]
		{ tenancyId, Integer.parseInt(optNum), optNum, storeId });

		String empName = "";
		if (rs.next())
		{
			empName = rs.getString("name").trim();
		}
		else
		{
			empName = optNum;
		}
		return empName;
	}
	
	@Override
	public void savePosLog(String tenantId, Integer organId, String posNum, String optNum, String optName, Integer shiftId, Date reportDate, String title, String content, String oldstate, String newstate)
	{
		try
		{
			Timestamp lastUpdateTime = DateUtil.currentTimestamp();
			if (Tools.isNullOrEmpty(optName) && !com.tzx.orders.base.constant.Constant.BETWEEN_SHIFT_OPT_NUM.equals(optNum) && !com.tzx.orders.base.constant.Constant.WM_OPT_NUM.equals(optNum))
			{
				optName = this.getEmpNameById(optNum, tenantId, organId);
			}

			if (Tools.isNullOrEmpty(lastUpdateTime))
			{
				lastUpdateTime = DateUtil.currentTimestamp();
			}

			if (null != oldstate && oldstate.length() > 500)
			{
				oldstate = oldstate.substring(0, 497) + "...";
			}

			if (null != newstate && newstate.length() > 500)
			{
				newstate = newstate.substring(0, 497) + "...";
			}

			StringBuilder insertSql = new StringBuilder("INSERT INTO pos_log (tenancy_id,store_id,pos_num,opt_num,opt_name,title,content,oldstate,newstate,shift_id,report_date,last_updatetime) ");
			insertSql.append("values (?,?,?,?,?,?,?,?,?,?,?,?)");

			this.jdbcTemplate.update(insertSql.toString(), new Object[]
			{ tenantId, organId, posNum, optNum, optName, title, content, oldstate, newstate, shiftId, reportDate, lastUpdateTime });
		}
		catch (Exception e)
		{
			logger.info("保存Pos日志错误", e);
			e.printStackTrace();
		}
	}
}
