package com.tzx.pos.bo.imp;

import java.io.File;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;

import com.google.gson.JsonObject;
import com.tzx.base.common.util.PosPropertyUtil;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.DoubleHelper;
import com.tzx.framework.common.util.JavaMd5;
import com.tzx.framework.common.util.PropertiesLoader;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.Tools;
import com.tzx.pos.base.constant.BasicData;
import com.tzx.pos.base.constant.InvoiceChannel;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.constant.XmdCrmParameterEnum;
import com.tzx.pos.base.controller.DataUploadRunnable;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.base.util.KeyUtils;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.base.util.PathUtil;
import com.tzx.pos.base.util.QrCodeUtils;
import com.tzx.pos.base.util.ReqDataUtil;
import com.tzx.pos.bo.PosBaseService;
import com.tzx.pos.bo.dto.TakeOrderingParam;
import com.tzx.pos.bo.imp.calcamount.PosCalcAmountSelfServiceImp;
import com.tzx.pos.bo.imp.calcamount.PosCalcAmountServiceImp;
import com.tzx.pos.bo.imp.calcamount.TakePosCalcAmountServiceImp;
import com.tzx.pos.po.springjdbc.dao.PosBaseDao;
import com.tzx.pos.po.springjdbc.dao.PosDao;
import com.tzx.pos.po.springjdbc.dao.imp.PosDaoImp;
import net.sf.json.JSONObject;
import sun.misc.BASE64Encoder;

@Service(PosBaseService.NAME)
public class PosBaseServiceImp implements PosBaseService
{
	@Resource(name = PosBaseDao.NAME)
	private PosBaseDao	baseDao;
	
	private static final Logger	logger	= Logger.getLogger(PosBaseServiceImp.class);

	@Override
	public void calcAmount(String tenantId, Integer storeId, String billNum) throws Exception
	{
		String mode = "";
		String orgFormatState = "";
		StringBuilder querySql = new StringBuilder("select format_state from organ where tenancy_id=? and id=?");
		SqlRowSet rs = baseDao.query4SqlRowSet(querySql.toString(), new Object[]
		{ tenantId, storeId });
		if (rs.next())
		{
			orgFormatState = rs.getString("format_state");
			if ("1".equals(orgFormatState))
			{
				mode = baseDao.getSysParameter(tenantId, storeId, "ZCDCMS");
			}
		}

		if ("1".equals(orgFormatState) && "02".equals(mode))
		{
			// 正餐自助模式
			calcAmountForSelf(tenantId, storeId, billNum);
		}
		else
		{
			calcAmountForStandard(tenantId, storeId, billNum);
		}
	}
	
	public void calcAmountTake(TakeOrderingParam param) throws Exception
	{
		SpringConext.getApplicationContext().getBean(TakePosCalcAmountServiceImp.class).calcAmount(param.getTenantId(), param.getOrganId(), param.getBillNum(), param);
	}

	/**金额计算
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @throws Exception
	 */
	public void calcAmountForStandard(String tenantId, Integer storeId, String billNum) throws Exception
	{
		SpringConext.getApplicationContext().getBean(PosCalcAmountServiceImp.class).calcAmount(tenantId, storeId, billNum);
	}
	
//	/**金额计算
//	 * @param tenantId
//	 * @param storeId
//	 * @param billNum
//	 * @throws Exception
//	 */
//	public void calcAmountForStandardTake(String tenantId, Integer storeId, String billNum,TakeOrderingParam param) throws Exception
//	{
//		SpringConext.getApplicationContext().getBean(TakePosCalcAmountServiceImp.class).calcAmount(tenantId, storeId, billNum, param);
//	}
	
	/**自助模式金额计算
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @throws Exception
	 */
	public void calcAmountForSelf(String tenantId, Integer storeId, String billNum) throws Exception
	{
		SpringConext.getApplicationContext().getBean(PosCalcAmountSelfServiceImp.class).calcAmount(tenantId, storeId, billNum);
	}

//	public void calcAmountForSelfTake(String tenantId, Integer storeId, String billNum,TakeOrderingParam param) throws Exception
//	{
//		SpringConext.getApplicationContext().getBean(TakePosCalcAmountServiceImp.class).calcAmount(tenantId, storeId, billNum,param);
//	}
	
//	/**金额计算优化
//	 * @param tenantId
//	 * @param storeId
//	 * @param billNum
//	 * @throws Exception
//	 */
//	public void calcAmountForStandard2(String tenantId, Integer storeId, String billNum) throws Exception
//	{
//		SpringConext.getApplicationContext().getBean(PosCalcAmountStandardServiceImp.class).calcAmount(tenantId, storeId, billNum);
//	}
	
//	/**金额计算优化
//	 * @param tenantId
//	 * @param storeId
//	 * @param billNum
//	 * @throws Exception
//	 */
//	public void calcAmountForStandard2Take(String tenantId, Integer storeId, String billNum,TakeOrderingParam param) throws Exception
//	{
//		SpringConext.getApplicationContext().getBean(TakePosCalcAmountServiceImp.class).calcAmount(tenantId, storeId, billNum,param);
//	}

	@Override
	public double calcAmountForInvoice(String tenantId, Integer storeId, JSONObject param) throws Exception
	{
		// 计算开票金额
		String type = param.optString("type");
		List<JSONObject> invoicePaymentList = new ArrayList<JSONObject>();
		if ("RETREAT_FOOD".equals(type))
		{
			invoicePaymentList = baseDao.getInvoicePaymentRetreat(tenantId, storeId, param);
		}
		else
		{
			invoicePaymentList = baseDao.getInvoicePayment(tenantId, storeId, param);
		}

		double amt = 0d;
		if (invoicePaymentList != null && invoicePaymentList.size() > 0)
		{
			amt = invoicePaymentList.get(0).optDouble("invoice_amount");
		}
		return amt;
	}

	@Override
	public JSONObject getPrintInvoiceInfo(String tenancyId, Integer storeId, Date reportDate, JSONObject param) throws Exception
	{
		int recoverCnt = param.optInt("recover_count"); // 取得恢复账单次数
		double invoiceAmount = param.optDouble("invoice_amount");
		double taxRate = param.optDouble("tax_rate");
		String type = param.optString("type");
		String orderNum = param.optString("order_num");
		String billNum = "";
		if ("RETREAT_FOOD".equals(type))
		{
			billNum = param.optString("copy_bill_num");
		}
		else
		{
			if (Tools.hv(orderNum))
			{
				billNum = orderNum;
			}
			else
			{
				billNum = param.optString("bill_num");
			}
		}
		
		if (Tools.isNullOrEmpty(billNum))
		{
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_BILL_NUM);
		}
		
		String channel = param.optString("channel");// 注意：开发票时，字段值为channnel
		if ("".equals(channel))
		{
			channel = param.optString("source");
		}

		String serviceType = InvoiceChannel.find(channel).getLabel();

		if (Tools.isNullOrEmpty(serviceType))
		{
			throw new Exception("获取serviceType失败。请检查配置文件是否设置正确！");
		}
		// 生成校验密码
		String strNum = "";
		String strReportDate = DateUtil.getYYYYMMDDFromDate(reportDate);
		String strOrganId = String.valueOf(storeId);
		
		//截取单据流水号:渠道编码(外卖订单或微信点餐订单) + 机构号 + 报表日期 + 流水号,例如:EL09 10 20180905 113325  0010
		int beginIndex = strOrganId.length() + strReportDate.length();
		if (billNum.contains(channel))
		{
			beginIndex += channel.length();
		}
		strNum = billNum.substring(beginIndex, billNum.length());

//		if ("1".equals(serviceType)) // pos结账时 MD01
//		{
//			strNum = billNum.substring(billNum.length() - 6, billNum.length());
//			strReportDate = billNum.substring(billNum.length() - 14, billNum.length() - 6);
//			strOrganId = billNum.substring(0, billNum.length() - 14);
//		}
//		else if ("2".equals(serviceType) || "4".equals(serviceType) || "10".equals(serviceType)) // WX02
//																									// CC04
//																									// WM10
//		{
//			strNum = billNum.substring(billNum.length() - 6, billNum.length());
//			strReportDate = billNum.substring(billNum.length() - 14, billNum.length() - 6);
//			strOrganId = billNum.substring(4, billNum.length() - 14);
//		}
//		else if ("6".equals(serviceType) || "8".equals(serviceType)||"11".equals(serviceType)) // BD06,MT08,MT11
//
//		{
//			strNum = billNum.substring(billNum.length() - 8, billNum.length());
//			strReportDate = billNum.substring(billNum.length() - 16, billNum.length() - 8);
//			strOrganId = billNum.substring(4, billNum.length() - 16);
//		}
//		else if ("9".equals(serviceType)) // EL09
//		{
//			strNum = billNum.substring(billNum.length() - 10, billNum.length());
//			strReportDate = billNum.substring(billNum.length() - 18, billNum.length() - 10);
//			strOrganId = billNum.substring(4, billNum.length() - 18);
//		}

		// 生成新的电子发票单号
		// tenancy_id#store_id#DDRQ#DH#SERVICE_TYPE#SL#JE#key
		String num = strNum;
		if (recoverCnt > 0)
		{
			num = strNum + "@" + recoverCnt;
		}

		String content = tenancyId + "#" + strOrganId + "#" + strReportDate + "#" + num + "#" + serviceType + "#" + taxRate + "#" + invoiceAmount;
		String password = baseDao.getSysParameter(tenancyId, storeId, "dzfp_ewmdyfpmy");
		String para = KeyUtils.encryptASE_MD5(content, password);
		content = content + "#" + para.substring(0, 4);
		
		// 获得二维码URL
		// Map<String, String> systemMap =
		// com.tzx.framework.common.constant.Constant.getSystemMap();
		String requestUrl = PosPropertyUtil.getMsg("saas.url") + "/invoice/elec/get?para=";
		// 加密前URL
//		String strBeforeEncodeUrl = requestUrl + content + "#" + password;
//		content = content + "#" + para.substring(0, 4);
		String strBeforeEncodeUrl = requestUrl + content;
		
		// 加密URL
		BASE64Encoder base64en = new BASE64Encoder();
		String newContent = base64en.encode(content.getBytes());
		newContent = newContent.replace("\r\n", "");
		String strUrlContent = requestUrl + newContent;

		// 生成电子发票二维码图片
		Timestamp timestamp = DateUtil.currentTimestamp();
		String strPhysicalPath = PathUtil.getWebRootPath() + File.separator;
		// 生成新的二维码文件名
		String fileName = billNum + "@" + recoverCnt + DateUtil.getYYYYMMDDHHMMSS(timestamp) + ".png";
		String strimgPath = "img/qr_code/invoice/" + fileName;
		strPhysicalPath = strPhysicalPath + strimgPath;
		QrCodeUtils.encode(strUrlContent, strPhysicalPath);

		String webPathConst = com.tzx.framework.common.constant.Constant.getSystemMap().get("WEB_PATH");
		String strUrlPath = webPathConst + strimgPath;

		param.put("before_encode_url", strBeforeEncodeUrl);
		param.put("url_content", strUrlContent);
		param.put("url_path", strUrlPath);
		param.put("invoice_amount", invoiceAmount);
		param.put("last_updatetime", DateUtil.format(timestamp));

		// 插入发票表
        if(!param.optBoolean("is_exception_print")){
            baseDao.insertToInvoice(tenancyId, storeId, param);
        }

		return param;
	}

	@Override
	public void initSysParamterDataByProties(String tenancyId, int storeId) throws Exception
	{
		List<Object[]> insertBatchArgs = new ArrayList<Object[]>();
		List<Object[]> delBatchArgs = new ArrayList<Object[]>();
		String systemName = "POS";
		String modelName = "系统参数";
		String paraType = "初始化";
		String validState = "1";

//		Iterator<Map.Entry<String, String>> entries = Constant.XMD_CRM_PARAMETER_KEYS.entrySet().iterator();
//		while (entries.hasNext())
//		{
//			Map.Entry<String, String> entry = entries.next();
//			delBatchArgs.add(new Object[]
//			{ entry.getKey(), systemName, tenancyId, storeId });
//			insertBatchArgs.add(new Object[]
//			{ tenancyId, storeId, systemName, modelName, Constant.XMD_CRM_PARAMETER_NAME.get(entry.getKey()), entry.getKey(), PosPropertyUtil.getMsg(entry.getValue()), paraType, validState });
//		}
		
		for(XmdCrmParameterEnum para: XmdCrmParameterEnum.values())
		{
			delBatchArgs.add(new Object[]
			{ para.getCode(), systemName, tenancyId, storeId });
			insertBatchArgs.add(new Object[]
			{ tenancyId, storeId, systemName, modelName, para.getName(), para.getCode(), PosPropertyUtil.getMsg(para.getVaue()), paraType, validState });
		}

		// 更新美大指定值
		String sql = new String("select setval('sys_parameter_id_seq', (select max(id) from sys_parameter)+1)");
		baseDao.query4SqlRowSet(sql);

		String deleteXMDSql = "delete from sys_parameter where para_code = ? and system_name = ? and tenancy_id = ? and store_id = ?";
		baseDao.batchUpdate(deleteXMDSql, delBatchArgs);

		String insertXMDSql = "insert into sys_parameter (tenancy_id, store_id, system_name, model_name, para_name, para_code, para_value, para_type, valid_state) values (?,?,?,?,?,?,?,?,?)";
		baseDao.batchUpdate(insertXMDSql, insertBatchArgs);

		this.updateDataVersion(tenancyId, storeId, BasicData.SYS_PARAMETER);
	}

	public void updateDataVersion(String tenancyId, int storeId, BasicData dataParaCode) throws Exception
	{
		String delsql = "delete from pos_data_version where tenancy_id=? and store_id=? and para_code =?";
		String inssql = "insert into pos_data_version(tenancy_id,store_id,system_name,model_name,para_name,para_code,para_value,para_defaut,para_type,valid_state) values(?,?,'POS','数据同步版本',?,?,?,'1','初始化','1')";

		PosDaoImp posDaoImp = (PosDaoImp) SpringConext.getBean(PosDao.NAME);
		List<JSONObject> jsons = posDaoImp.getSysParameter(tenancyId, storeId);

		baseDao.update(delsql, new Object[]
		{ tenancyId, storeId, dataParaCode.toString() });
		baseDao.update(inssql, new Object[]
		{ tenancyId, storeId, PropertiesLoader.getProperty(dataParaCode.getCode()), dataParaCode.toString(), JavaMd5.toMd5B32(jsons.toString()) });
	}

	@Override
	public void upload(Data paramData)
	{
		try
		{
			String tenantId = paramData.getTenancy_id();
			Integer organId = paramData.getStore_id();
			Map<String, Object> maps = ReqDataUtil.getDataMap(paramData);
			String billno = ParamUtil.getStringValue(maps, "bill_num", true, PosErrorCode.NOT_NULL_BILL_NUM);
			// 数据上传
			this.upload(tenantId, String.valueOf(organId), "", "", "", billno);
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
	}
	
	/**
	 * 数据上传
	 * @param tenantId
	 * @param store_id
	 * @param old_table_code
	 * @param new_table_code
	 * @param old_bill_num
	 * @param new_bill_num
	 */
	@Override
	public void upload(String tenantId, String store_id, String old_table_code, String new_table_code, String old_bill_num, String new_bill_num)
	{
		DataUploadRunnable r = new DataUploadRunnable(tenantId, store_id, old_table_code, new_table_code, old_bill_num, new_bill_num);
		Thread thread = new Thread(r);
		thread.start();
	}
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param discountMode
	 * @param rwidList
	 * @return
	 * @throws Exception
	 */
	@Override
	public List<JSONObject> SetCustomerVipPriceByItem(String tenancyId,int storeId,String billNum,int discountMode,List<Integer> rwidList) throws Exception
	{
		final int defaultScale = 4;
		final int scale = 2;

		StringBuilder rwids = new StringBuilder();
		for(Integer rwid : rwidList)
		{
			if(rwid >0 )
			{
				rwids.append("," + rwid);
			}
		}

		StringBuilder conditon = new StringBuilder();
		if(rwids.length()>0)
		{
			conditon.append(" and bi.rwid in(" + rwids.delete(0, 1) + ") ");
		}

		StringBuilder querySql = new StringBuilder();
		querySql.append(" select bi.id,bi.item_id,bi.setmeal_id,bi.item_serial,bi.setmeal_rwid,bi.item_property,coalesce(civs.vip_price,civ.vip_price) as vip_price,bi.item_price,bi.item_num,bi.item_name,bi.item_unit_id,bi.item_unit_name,bi.discount_mode_id,hi.is_runningprice");
		querySql.append(" from pos_bill_item bi left join pos_bill b on bi.tenancy_id = b.tenancy_id and bi.store_id=b.store_id and bi.bill_num = b.bill_num");
		querySql.append(" left join hq_item_info hi on bi.item_id=hi.id");
		querySql.append(" left join crm_item_vip civ on bi.item_id=civ.item_id and bi.item_unit_id=civ.unit_id");
		querySql.append(" left join crm_item_vip_sysprice civs on civ.unit_id=civs.unit_id and civs.chanel=b.source and civs.price_system||'' = (select price_system from organ where id=bi.store_id)");
		querySql.append(" where bi.tenancy_id =? and bi.store_id=? and bi.bill_num=? and (bi.discount_mode_id <> ? or bi.discount_mode_id is null) ").append(conditon);

		List<JSONObject> itemList = baseDao.query4Json(tenancyId, querySql.toString(), new Object[]{ tenancyId,storeId,billNum,SysDictionary.DISCOUNT_MODE_10});

		List<JSONObject> returnList = new ArrayList<JSONObject>();
		List<Object[]> batchArgs = new ArrayList<Object[]>();
		for (JSONObject itemJson : itemList)
		{
			String itemProperty = itemJson.optString("item_property");
			Double vipPrice = itemJson.optDouble("vip_price");
			Double itemPrice = itemJson.optDouble("item_price");

			// 菜品未设置会员价时，默认赋值为单价
			if (null == vipPrice || vipPrice.isNaN())
			{
				vipPrice = 0d;
			}
			
			//时价菜会员价为0
			if("Y".equals(itemJson.optString("is_runningprice")))
			{
				vipPrice = 0d;
			}

			if(SysDictionary.DISCOUNT_MODE_10 == itemJson.optInt("discount_mode_id"))
			{
				continue;
			}

			if (vipPrice > 0 && vipPrice < itemPrice)
			{
				if (SysDictionary.ITEM_PROPERTY_SINGLE.equals(itemProperty))
				{
					batchArgs.add(new Object[]
					{ vipPrice, discountMode, itemJson.optInt("id"), billNum, storeId, tenancyId });

					JSONObject item = new JSONObject();
					item.put("item_id", itemJson.optInt("item_id"));
					item.put("item_code", itemJson.optString("item_num"));
					item.put("item_name", itemJson.optString("item_name"));
					item.put("unit_id", itemJson.optInt("item_unit_id"));
					item.put("unit_name", itemJson.optString("item_unit_name"));
					item.put("item_price", itemPrice);
					item.put("vip_price", vipPrice);
					returnList.add(item);
				}
				else if (SysDictionary.ITEM_PROPERTY_SETMEAL.equals(itemProperty))
				{
					Integer itemId = itemJson.optInt("item_id");
					Integer itemSerial = itemJson.optInt("item_serial");

					batchArgs.add(new Object[]
					{ vipPrice, discountMode, itemJson.optInt("id"), billNum, storeId, tenancyId });

					JSONObject item = new JSONObject();
					item.put("item_id", itemId);
					item.put("item_code", itemJson.optString("item_num"));
					item.put("item_name", itemJson.optString("item_name"));
					item.put("unit_id", itemJson.optInt("item_unit_id"));
					item.put("unit_name", itemJson.optString("item_unit_name"));
					item.put("item_price", itemPrice);
					item.put("vip_price", vipPrice);
					returnList.add(item);

					Integer dMaxId = null;
					Double dmaxAmount = 0d;
					Double sumAmount = 0d;

					List<JSONObject> detailList = new ArrayList<JSONObject>();
					for (JSONObject detailItemJson : itemList)
					{
						String item_property = detailItemJson.optString("item_property");
						if (SysDictionary.ITEM_PROPERTY_MEALLIST.equals(item_property) && itemId == detailItemJson.optInt("setmeal_id") && itemSerial == detailItemJson.optInt("setmeal_rwid"))
						{
							Double detailItemPrice = detailItemJson.optDouble("item_price");
							Double detailVipPrice = DoubleHelper.mul(DoubleHelper.div(vipPrice, itemPrice, defaultScale), detailItemPrice, scale);
							detailItemJson.put("vip_price", detailVipPrice);
							detailList.add(detailItemJson);
							sumAmount = DoubleHelper.add(sumAmount, detailVipPrice, defaultScale);

							if (null == dMaxId || dmaxAmount < detailItemPrice)
							{
								dMaxId = detailItemJson.optInt("id");
								dmaxAmount = detailItemPrice;
							}
						}
					}

					for (JSONObject detail : detailList)
					{
						Double detailVipPrice = detail.optDouble("vip_price");
						if (dMaxId == detail.optInt("id"))
						{
							detailVipPrice = DoubleHelper.add(detailVipPrice, DoubleHelper.sub(vipPrice, sumAmount, defaultScale), scale);
						}
						batchArgs.add(new Object[]
						{ detailVipPrice, discountMode, detail.optInt("id"), billNum, storeId, tenancyId });

						JSONObject detailItem = new JSONObject();
						detailItem.put("item_id", detail.optInt("item_id"));
						detailItem.put("item_code", detail.optString("item_num"));
						detailItem.put("item_name", detail.optString("item_name"));
						detailItem.put("unit_id", detail.optInt("item_unit_id"));
						detailItem.put("unit_name", detail.optString("item_unit_name"));
						detailItem.put("item_price", detail.optDouble("item_price"));
						detailItem.put("vip_price", detailVipPrice);
						returnList.add(detailItem);
					}
				}
			}
			else
			{
				if (SysDictionary.ITEM_PROPERTY_SINGLE.equals(itemProperty))
				{
					batchArgs.add(new Object[]
					{ 0d, 0d, itemJson.optInt("id"), billNum, storeId, tenancyId });
				}
				else if (SysDictionary.ITEM_PROPERTY_SETMEAL.equals(itemProperty))
				{
					Integer itemId = itemJson.optInt("item_id");
					Integer itemSerial = itemJson.optInt("item_serial");
					batchArgs.add(new Object[]
					{ 0d, 0d, itemJson.optInt("id"), billNum, storeId, tenancyId });

					for (JSONObject detailItemJson : itemList)
					{
						String item_property = detailItemJson.optString("item_property");
						if (SysDictionary.ITEM_PROPERTY_MEALLIST.equals(item_property) && itemId == detailItemJson.optInt("setmeal_id") && itemSerial == detailItemJson.optInt("setmeal_rwid"))
						{
							batchArgs.add(new Object[]
							{ 0d, 0d, detailItemJson.optInt("id"), billNum, storeId, tenancyId });
						}
					}
				}
			}
		}
		if (batchArgs.size() > 0)
		{
			baseDao.batchUpdate("update pos_bill_item set third_price=?,discount_mode_id=? where id=? and bill_num=? and store_id=? and tenancy_id=?", batchArgs);
		}
		return returnList;
	}
	
	@Override
	public void updatePosBillForUpload(String tenancyId, int storeId, String billNum) throws Exception
	{
		String[] tableNames = new String[]
		{ "pos_bill", "pos_bill_invoice", "pos_bill_member", "pos_bill_service", "pos_bill_waiter", "hq_bill_evaluate", "pos_bill_item", "pos_zfkw_item", "pos_item_discount_list", "pos_bill_payment", "pos_bill_payment_coupons" };

		this.updatePosBillForUpload(tenancyId, storeId, billNum, tableNames);
	}

	@Override
	public void updatePosBillForUpload(String tenancyId, int storeId, String billNum, String[] tableNames) throws Exception
	{
		for (String tableName : tableNames)
		{
			baseDao.updatePosBillForUploadByBillNum(tenancyId, storeId, tableName, billNum, "0");
		}
	}

    @Override
    public List<JSONObject> getOptStateInfo(String tenancyId, int storeId, String reportDate) throws Exception
    {
    	// 首先签到或最后签到机台信息
		String paramValue = baseDao.getSysParameter(tenancyId, storeId, "first_or_last_opt_state");

        StringBuilder queryOptSql = new StringBuilder();
        queryOptSql.append(" select os1.id,os1.tenancy_id,os1.store_id,os1.opt_num,os1.pos_num,os1.report_date,os1.shift_id from pos_opt_state os1 ");
        queryOptSql.append(" where os1.tenancy_id=? and os1.store_id=? and os1.report_date=? and os1.content=? ");

        if (StringUtils.isNotEmpty(paramValue) && "first".equals(paramValue)){ // 首先签到
			queryOptSql.append(" order by os1.id asc ");
		} else { // 最后签到
			queryOptSql.append(" order by os1.id desc ");
		}

        List<JSONObject> optList = baseDao.query4Json(tenancyId, queryOptSql.toString(), new Object[]
                {tenancyId, storeId, com.tzx.pos.base.util.DateUtil.parseDate(reportDate), SysDictionary.OPT_STATE_KSSY});

        return optList;
    }

	@Override
	public String generateInvoiceQrCodeFile(String tenancyId, int storeId, JSONObject invoiceInfo) {
		String strPhysicalPath = PathUtil.getWebRootPath() + File.separator;

		String billNum = invoiceInfo.optString("bill_num");
		String copyBillNum = invoiceInfo.optString("copy_bill_num");
		if (Tools.hv(copyBillNum)) {
			billNum = copyBillNum;
		}
		int recoverCnt = invoiceInfo.optInt("recover_count");
		String strUrlContent = invoiceInfo.optString("url_content");
		Timestamp last_updatetime = DateUtil.parseTimestamp(invoiceInfo.optString("last_updatetime"));
		String fileName = billNum + "@" + recoverCnt + DateUtil.getYYYYMMDDHHMMSS(last_updatetime) + ".png";
		String strimgPath = "img/qr_code/invoice/" + fileName;
		strPhysicalPath = strPhysicalPath + strimgPath;
		try {
			QrCodeUtils.encode(strUrlContent, strPhysicalPath);
			String webPathConst = com.tzx.framework.common.constant.Constant.getSystemMap().get("WEB_PATH");
			String strUrlPath = webPathConst + strimgPath;

			return strUrlPath;
		} catch (Exception e) {
			logger.error("生成二维码图片失败:" + e.getMessage(), e);
		}

		return null;
	}
}
