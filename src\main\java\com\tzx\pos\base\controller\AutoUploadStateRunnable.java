package com.tzx.pos.base.controller;

import java.io.File;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tzx.framework.common.util.Comet4jUtil;
import com.tzx.pos.base.util.ParamUtil;
import org.apache.log4j.Logger;

import net.sf.json.JSONObject;

import com.tzx.base.common.util.PosPropertyUtil;
import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.util.HttpUtil;
import com.tzx.framework.common.util.PropertiesUtil;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.pos.base.dao.AutoUploadDataDao;
import com.tzx.pos.base.util.DateUtil;

public class AutoUploadStateRunnable implements Runnable
{
	private final String	postUrl	= "/hqRest/post";
	private static final Logger	logger	= Logger.getLogger(AutoUploadStateRunnable.class);
	@Override
	public void run()
	{
		try
		{
			Map<String, String> systemMap = Constant.getSystemMap();

			if (!systemMap.containsKey("tenent_id") || !systemMap.containsKey("store_id") || "".equals(systemMap.get("tenent_id")) || "".equals(systemMap.get("store_id")) || "".equals(PosPropertyUtil.getMsg("saas.url")))
			{
				logger.info("参数为空");
				return;
			}
			String tenancyId = systemMap.get("tenent_id");
			Integer storeId = Integer.parseInt(systemMap.get("store_id"));
			
			String url= PosPropertyUtil.getMsg("saas.url") + postUrl;

			DBContextHolder.setTenancyid(tenancyId);
			AutoUploadDataDao uploadDao = SpringConext.getApplicationContext().getBean(AutoUploadDataDao.class);

			JSONObject obj = new JSONObject();
			// Map<String,String> obj = new HashMap<String, String>();
			obj.put("send_time", DateUtil.format(new Timestamp(System.currentTimeMillis())));
//			obj.put("db_version", uploadDao.getPosParameter(systemMap.get("tenent_id"), Integer.parseInt(systemMap.get("store_id")), "DBVERSION"));
			// 2016-07-21 由于下发时没有指定门店的ID,所以在上传数据库版本时查询门店ID为0的就是当前门店的数据库版本  
			obj.put("db_version", uploadDao.getPosParameter("", 0, "DBVERSION"));
			String filePath = System.getProperty("catalina.home") + System.getProperty("file.separator") + "webapps/ROOT/config/version.properties";
			
			Map<String, String> propsMap = new HashMap<String, String>();
			if ((new File(filePath)).exists())
			{
				propsMap = PropertiesUtil.setPropertiesFileToMap(filePath);
			}
			obj.put("pos_version",propsMap.get("app.version"));
            obj.put("cc_version", propsMap.get("app.release") +  propsMap.get("app.revision"));

			// obj.put("tenent_id", systemMap.get("tenent_id"));
			// obj.put("store_id", systemMap.get("store_id"));
			//
			// HttpUtil.sendPostRequest(systemMap.get("saas_url")+postUrl, obj);

			List<JSONObject> jsonList = new ArrayList<JSONObject>();
			jsonList.add(obj);

			Data data = Data.get();
			data.setType(Type.STORE_STATE);
			data.setOper(Oper.upload);
			data.setTenancy_id(tenancyId);
			data.setStore_id(storeId);
			data.setData(jsonList);

			//logger.info("请求地址: "+url+"      请求参数: "+JSONObject.fromObject(data).toString());
			String result=HttpUtil.sendPostRequest(url, JSONObject.fromObject(data).toString());
            if ("1".equals(ParamUtil.getSysPara(tenancyId,storeId,"network_disconnet_notify"))&&null == result) {
                List<JSONObject> noticeList = new ArrayList<JSONObject>();
                JSONObject noticeJson = new JSONObject();
                noticeJson.put("message", "网络连接延迟或中断");
                noticeList.add(noticeJson);
                Comet4jUtil.comet4J(noticeList, Type.NETWORK_DICONNECT, Oper.notice);
            }
            
            //异常打印提醒
            String printDesc = uploadDao.getPrintNewData(tenancyId, storeId);
            if(printDesc!=null && !"".equals(printDesc)){
                List<JSONObject> noticeList = new ArrayList<JSONObject>();
                JSONObject noticeJson = new JSONObject();
                noticeJson.put("message", printDesc);
                noticeList.add(noticeJson);
                Comet4jUtil.comet4J(noticeList, Type.PRINT_EXCEPTION_WARN, Oper.notice);
            }
        }
		catch (Exception e)
		{
			e.printStackTrace();
			logger.error("门店状态自动上传报错:", e);
		}

	}
}
