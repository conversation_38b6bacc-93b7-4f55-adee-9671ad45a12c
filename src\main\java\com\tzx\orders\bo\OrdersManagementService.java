package com.tzx.orders.bo;

import java.util.List;

import com.tzx.framework.common.entity.Data;
import com.tzx.orders.bo.dto.OrderState;
import com.tzx.pos.bo.PosBaseService;

import net.sf.json.JSONObject;

public interface OrdersManagementService extends PosBaseService
{
	String	NAME	= "com.tzx.orders.bo.imp.OrdersManagementServiceImp";

	/**
	 * 获取订单列表
	 * 
	 * @param tenentid
	 * @param storeid
	 * @param para
	 * @return
	 * @throws Exception
	 */
	JSONObject getOrdersList(String tenentid, int storeid, JSONObject para) throws Exception;
	
	/**
	 * 查询订单明细菜品
	 * 
	 * @param tenentid
	 * @param storeid
	 * @param para
	 * @return
	 * @throws Exception
	 */
	JSONObject getOrdersItemDetails(String tenentid, int storeid, JSONObject para) throws Exception;


	/** 查询订单异常原因
	 * @param tenentid
	 * @param storeid
	 * @param para
	 * @return
	 * @throws Exception
	 */
	JSONObject getReasonDetail(String tenentid, int storeid, JSONObject para) throws Exception;
	/**
	 * 获取支付方式
	 * 
	 * @param tenentid
	 * @param storeid
	 * @param para
	 * @return
	 * @throws Exception
	 */
	JSONObject getPaymentWay(String tenentid, int storeid, JSONObject para) throws Exception;

	/**
	 * 获取订单取消原因类型
	 * 
	 * @param tenentid
	 * @param storeid
	 * @param para
	 * @return
	 * @throws Exception
	 */
	JSONObject getUnusualReason(String tenentid, int storeid, JSONObject para) throws Exception;

    JSONObject batchTakeOrder(String tenancyId, int storeId) throws Exception;

    /**
	 * 取单
	 * 
	 * @param tenentid
	 * @param storeid
	 * @param para
	 * @throws Exception
	 */
	JSONObject takeOrders(String tenentid, int storeid, JSONObject para) throws Exception;

    /**
     * 异常单取单
     *
     * @param tenentid
     * @param storeid
     * @param para
     * @throws Exception
     */
    JSONObject takeExceptionOrders(String tenentid, int storeid, JSONObject para,JSONObject resultJson) ;

	/**
	 * 付款
	 * 
	 * @param tenentid
	 * @param storeid
	 * @param para
	 * @throws Exception
	 */
	JSONObject paymentOrders(String tenentid, int storeid, JSONObject para) throws Exception;

	
	/** 获取支付二维码
	 * @param tenentid
	 * @param storeid
	 * @param para
	 * @return
	 * @throws Exception
	 */
	JSONObject getQRCodeForPayment(String tenentid, int storeid, JSONObject para,String webRootPath) throws Exception;
	
	/**
	 * @param tenentid
	 * @param storeid
	 * @param para
	 * @param webRootPath
	 * @return
	 * @throws Exception
	 */
	JSONObject queryThirdPayment(String tenentid, int storeid, JSONObject para,String webRootPath) throws Exception;

	/**
	 * 取消订单
	 * 
	 * @param tenentid
	 * @param storeid
	 * @param para
	 * @throws Exception
	 */
	void cancelOrders(String tenentid, int storeid, JSONObject para) throws Exception;

	/**
	 * 取消微信后付异常订单
	 *
	 * @param tenentid
	 * @param storeid
	 * @param para
	 * @throws Exception
	 */
	void cancelWxOrders(String tenentid, int storeid, JSONObject para) throws Exception;

	/**
	 * @param tenentid
	 * @param storeid
	 * @param para
	 * @throws Exception
	 */
	void printOrders(String tenentid, int storeid, JSONObject para) throws Exception;
	
	/** 订单配送
	 * @param tenentid
	 * @param storeid
	 * @param para
	 * @throws Exception
	 */
	void deliveryOrders(String tenentid, int storeid, JSONObject para) throws Exception;
	
	/** 订单完成
	 * @param tenentid
	 * @param storeid
	 * @param para
	 * @throws Exception
	 */
	void completeOrders(String tenentid, int storeid, JSONObject para) throws Exception;

    /** 打印订单（厨打、结账单、随餐单等）
     * @param tenentId
     * @param storeId
     * @param json
     * @throws Exception
     */
    void printOrderBuffer(String tenentId,int storeId,JSONObject json) throws Exception;

	JSONObject getConfig();

	void setConfig(JSONObject config);

	JSONObject getPosOptState(String tenancyId, int storeId) throws Exception;
	
    JSONObject getPosOptState(String tenancyId, int storeId,boolean isAfterChangedShift) throws Exception;

	/**
	 * 微信转账单获取班次信息
	 * @param tenancyId
	 * @param storeId
	 * @return
	 * @throws Exception
	 */
	public JSONObject getPosOptState4WX(String tenancyId, int storeId) throws Exception;

	/** 补打印
	 * @param json
	 * @return
	 * @throws Exception
	 */
	JSONObject makePrintOrder(String tenancyId, int storeId,JSONObject json) throws Exception;

	/**
	 * 订单转账单
	 * @param tenentid
	 * @param storeid
	 * @param para
	 * @return
	 * @throws Exception
	 */
	JSONObject createPosBillByOrder(String tenentid, int storeid, JSONObject para) throws Exception;
	
	
	/**
	  * 根据原单号查询退款订单
	  * @Title:getRefundOrders
	  * @param:@param tenancyId
	  * @param:@param storeId
	  * @param:@param obj
	  * @param:@return
	  * @param:@throws Exception
	  * @return: List<JSONObject>
	  * @author: shenzhanyu
	  * @version: 1.0
	  * @Date: 2018年5月25日
	 */
	List<JSONObject> getRefundOrders(String tenancyId, int storeId,JSONObject obj) throws Exception;
	
	/**
	  * boh操作退款信息--同意退款
	  * @Title:agreeRefundOrders
	  * @param:@param tenancyId
	  * @param:@param storeId
	  * @param:@param obj
	  * @param:@return
	  * @param:@throws Exception
	  * @return: List<JSONObject>
	  * @author: shenzhanyu
	  * @version: 1.0
	  * @Date: 2018年06月27日
	 */
	JSONObject agreeRefundOrders(String tenancyId, int storeId,JSONObject json) throws Exception;
	
	/**
	  * boh操作退款信息--拒绝退款
	  * @Title:agreeRefundOrders
	  * @param:@param tenancyId
	  * @param:@param storeId
	  * @param:@param obj
	  * @param:@return
	  * @param:@throws Exception
	  * @return: List<JSONObject>
	  * @author: shenzhanyu
	  * @version: 1.0
	  * @Date: 2018年06月27日
	 */
	void disagreeRefundOrders(String tenancyId, int storeId,JSONObject json) throws Exception;
	
	
	
	/** 外卖退款
	 * @param json
	 * @return
	 * @throws Exception
	 */
	JSONObject refundOrders(String tenancyId, int storeId,JSONObject json) throws Exception;
	
	
	/** 对外卖部分退款冲减
	 * @param json
	 * @return
	 * @throws Exception
	 */
	void writeDownsRefundOrders(String tenancyId, int storeId,OrderState orderState) throws Exception;
	
    /**
     * 
      * @Description: 退款退菜单
      * @Title:printOrderBuffer
      * @param:@param tenentId
      * @param:@param storeId
      * @param:@param json
      * @param:@throws Exception
      * @return: void
      * @author: shenzhanyu
      * @version: 1.0
      * @Date: 2018年6月7日
     */
    void refundOrderBuffer(String tenentId,int storeId,JSONObject json) throws Exception;
    
	 /**
     * 查询退款订单个数
     * @Title:getNumOfRefundOrdes
     * @param:@param tenentid
     * @param:@param storeid
     * @param:@return
     * @param:@throws Exception
     * @return: long
     * @author: shenzhanyu
     * @version: 1.0
     * @Date: 2018年7月19日
    */
	List<JSONObject> getNumOfRefundOrders(String tenentid,int storeid) throws Exception; 
	
	/**
	  * 查询异常订单缺失菜品
	  * @Title:getNotMappedDishes
	  * @param:@param tenentid
	  * @param:@param storeid
	  * @param:@return
	  * @param:@throws Exception
	  * @return: List<JSONObject>
	  * @author: shenzhanyu
	  * @version: 1.0
	  * @Date: 2018年7月20日
	 */
	JSONObject getNotMappedDishes(String tenentid,int storeid,JSONObject obj) throws Exception; 
	
	/**
	  * 获取本地菜品类别 
	  * @Title:getLocalItemCate
	  * @param:@param tenentid
	  * @param:@param storeid
	  * @param:@param para
	  * @param:@return
	  * @param:@throws Exception
	  * @return: JSONObject
	  * @author: shenzhanyu
	  * @version: 1.0
	  * @Date: 2018年7月24日
	 */
	JSONObject getLocalItemCate(String tenentid, int storeid, JSONObject para) throws Exception;
	
	  /**
	    * 获取本地菜品
	    * @Title:getLocalItems
	    * @param:@param tenentid
	    * @param:@param storeid
	    * @param:@return
	    * @param:@throws Exception
	    * @return: JSONObject
	    * @author: shenzhanyu
	    * @version: 1.0
	    * @Date: 2018年7月22日
	   */
	 JSONObject getLocalItems(String tenentid,int storeid,JSONObject obj) throws Exception ;
	 
	 
	  /**
	    * 菜品修复 异常菜品与本地菜品绑定
	    * @Title:bindingItem
	    * @param:@param tenentid
	    * @param:@param storeid
	    * @param:@param obj
	    * @param:@return
	    * @param:@throws Exception
	    * @return: Object
	    * @author: shenzhanyu
	    * @version: 1.0
	    * @Date: 2018年7月22日
	   */
	   Object bindingItem (String tenentid,int storeid,JSONObject obj) throws Exception ;
	   
	   
	   /**
	    * 菜品修复 解除异常菜品与本地菜品绑定
	    * @Title:removeBindingItem
	    * @param:@param tenentid
	    * @param:@param storeid
	    * @param:@param obj
	    * @param:@throws Exception
	    * @return: void
	    * @author: shenzhanyu
	    * @version: 1.0
	    * @Date: 2018年7月22日
	   */
	   void  removeBindingItem (String tenentid,int storeid,JSONObject obj) throws Exception ;
	   
	   
	   /**
	    * 验证异常菜品是否绑定
	    * @Title:validateBindingItem
	    * @param:@param tenentid
	    * @param:@param storeid
	    * @param:@param obj
	    * @param:@return
	    * @param:@throws Exception
	    * @return: JSONObject
	    * @author: shenzhanyu
	    * @version: 1.0
	    * @Date: 2018年7月22日
	   */
	   JSONObject validateBindingItem (String tenentid,int storeid,JSONObject obj) throws Exception ;
	   
	   /**
	     * 
	     * @Description: 查询异常菜品绑定信息
	     * @Title:getBindingItems
	     * @param:@param tenentid
	     * @param:@param storeid
	     * @param:@return
	     * @param:@throws Exception
	     * @return: List<JSONObject>
	     * @author: shenzhanyu
	     * @version: 1.0
	     * @Date: 2018年7月25日
	    */
	    List<JSONObject> getBindingItems(String tenentid, int storeid) throws Exception;
	   
	   /**
	    * @Description: 修復綁定菜品
	    * @Title:repairItemInfo
	    * @param:@param tenentid
	    * @param:@param storeid
	    * @param:@param obj
	    * @param:@return
	    * @return: boolean
	    * @author: shenzhanyu
	    * @version: 1.0
	    * @Date: 2018年7月22日
	   */
	  public void repairItemInfo(String tenentid,int storeid,JSONObject obj) throws Exception ;
	  
	  /**
	    * @Description: 修复开班前订单数据
	    * @Title:orderDataBeforeOpenSyyRepair
	    * @param:@param tenancyId
	    * @param:@param storeId
	    * @param:@param reportDate
	    * @return: void
	    * @author: shenzhanyu
	    * @version: 1.0
	    * @Date: 2018年8月1日
	   */
	  void repairOrdersBeforeOpenSyy(String tenancyId, int storeId,String reportDate) throws Exception;

    /**
     * @Description: 打烊时门店外卖订单数据与总部外卖平台核对。
     * 注：内部处理异常，不抛出异常，不影响调用方流程。
     * @param: tenancyId 商户号
     * @param: storeId	门店号
     * @param: reportDate 报表日期
     * @return: boolean 核对结果
     * @author: zhehong.qiu email:<EMAIL>
     * @version: 1.0
     * @Date: 2018年07月18日
     */
    void analysisOrderAmount(String tenancyId,int storeId,String reportDate);

    /**
     * @Description:已有订单数据修复（用于交班时外卖订单数据修复）
     * 注：内部处理异常，不抛出异常，不影响调用方流程。
     * existOrderDataRepair
     * @param:@param tenancyId
     * @param:@param storeId
     * @return: void
     * @author: zhehong.qiu email:<EMAIL>
     * @version: 1.0
     * @Date: 2018年07月12日
     */
    void existOrderDataRepair(String tenancyId, int storeId,String reportDate);

	/**
	 * 删除外卖冗余数据
	 * @param tenancyId
	 * @param storeId
	 * @param para
	 */
	void removeRedundantData(String tenancyId, int storeId,JSONObject para) throws Exception;

	/**
	 * @Description: 查询预订单
	 * @Title:findAdvanceOrders
	 * @param:@param tenancyId
	 * @param:@param storeId
	 * @param:@param reportDate
	 * @param:@return
	 * @param:@throws Exception
	 * @return: List<JSONObject>
	 * @author: shenzhanyu
	 * @version: 1.0
	 * @Date: 2018年11月28日
	 */
	List<JSONObject> findAdvanceOrders(String tenancyId, int storeId,String single_time1,String single_time2) throws Exception;

	/**
	 * @Description: 预订单打印提醒
	 * @Title:advanceOrdersOfPrintRemind
	 * @param:@param tenancyId
	 * @param:@param storeId
	 * @param:@param json
	 * @return: void
	 * @author: shenzhanyu
	 * @version: 1.0
	 * @Date: 2018年11月28日
	 */
	void advanceOrdersOfPrintRemind(String tenancyId, int storeId,JSONObject json) throws Exception;


	/**
	 * 正在取单的订单标识置空
	 * @param tenancyId
	 * @param storeId
	 * @param orderCode
	 * @return
	 * @throws Exception
	 */
	boolean settingTakeOrderFlag(String tenancyId, int storeId, String orderCode) throws  Exception;

	/**
	 * eco 对接创建账单
	 * @param tenentid
	 * @param storeid
	 * @param para
	 * @return
	 * @throws Exception
	 */
	JSONObject createBill(String tenentid, int storeid, JSONObject para) throws Exception;


	/**
	 * eco对接退单
	 * @param tenentid
	 * @param storeid
	 * @param para
	 * @return
	 * @throws Exception
	 */
	void orderBack(String tenentid, int storeid, JSONObject para) throws Exception;

	/**
	 * 订单提醒
	 * @param tenentid
	 * @param storeid
	 * @param para
	 * @return
	 * @throws Exception
	 */
	void orderRemind(String tenentid, int storeid, JSONObject para) throws Exception;


	void orderNum(String tenentid, int storeid, JSONObject para) throws Exception;



    /**
     *  eco发送声音提示消息
     * @param tenancyId
     * @param storeId
     * @param orderCode
     * @param orderState
     * @param isRefresh
     */
    void sendMsg(String tenancyId,Integer storeId, String orderCode,String orderState, boolean isRefresh);

	/**
	 * eco发送弹窗提示消息
	 * @param msg
	 * @param conTactMem
	 */
	void sendMsgForWindow(String msg, String conTactMem);

	/**
	 * eco 打印随餐单 、厨打
	 * @param tenentId
	 * @param storeId
	 * @param json
	 * @throws Exception
	 */
	void printOrderBufferOfECO(String tenentId, int storeId, JSONObject json) throws Exception;

	void exceptionPrint(String tenancyId, int storeId, JSONObject para, String printCode);

    /**
     * eco 状态查询
     * @param param
     */
    void ecoStateQuery(Data param) throws Exception;

	/**
	 * 生成存餐条形码
	 * @param orderJSON
	 * @param tenancyId
	 * @param storeId
	 * @throws Exception
	 */
	public void buildOrderOneDimCode(JSONObject orderJSON, String tenancyId, Integer storeId) throws Exception ;

}
