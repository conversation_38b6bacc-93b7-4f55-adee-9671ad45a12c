package com.tzx.log.preserve;

import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.log.config.LogConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;

import javax.annotation.PostConstruct;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> on 2018-10-27
 */
public abstract class AbstractLogSave implements LogSave{

    private static final Logger LOG = LoggerFactory.getLogger(AbstractLogSave.class);

    @Autowired
    private LogConfig config;

    protected String defaultEnable;
    protected  List<String> logStrategy;
    protected  List<String> blackList;
    protected  List<String> whiteList;
    protected String segmentPattern;
    protected String keepMonth;




    @PostConstruct
    public void init(){
        initConfig();
    }

    private void initConfig(){
        this.defaultEnable=config.getDefaultEnable();
        this.logStrategy=Arrays.asList(config.getLogStrategy());
        this.blackList=Arrays.asList(config.getBlackList());
        this.whiteList=Arrays.asList(config.getWhiteList());
        this.segmentPattern=config.getSegmentPattern();
        this.keepMonth =config.getKeepMonth();
    }

    @Override
    @Async
    public void save(OperateLogger.Entry entry)throws SQLException {
        Data data=entry.getParam();
        Type type=data.getType();
        if(logEnable(type)){
            doSave(entry);
        }
    }

    protected abstract void doSave(OperateLogger.Entry entry) throws SQLException;

    protected boolean logEnable(Type type) {
        for(String strategy:logStrategy){

           if(LogConfig.WHITE_LIST.equals(strategy)&&whiteList.contains(type.toString())){
               return true;
           }

           if(LogConfig.BLACK_LIST.equals(strategy)&&blackList.contains(type.toString())){
               return false;
           }
        }

        return Boolean.valueOf(defaultEnable);

    }


}
