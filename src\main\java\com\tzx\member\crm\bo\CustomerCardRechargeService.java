package com.tzx.member.crm.bo;

import net.sf.json.JSONObject;

import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.entity.Data;
import com.tzx.member.common.entity.PosCustomerOperateListEntity;

/**
 * 会员卡充值
 * 
 * <AUTHOR>
 * @version 1.10.0
 *
 */
public interface CustomerCardRechargeService extends CustomerService
{
	String	NAME	= "com.tzx.member.crm.bo.imp.CustomerCardRechargeServiceImp";

	/**
	 * 生成订单号
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param paramJson
	 * @return
	 * @throws Exception
	 */
	public String createThirdBillCode(String tenancyId, Integer storeId, JSONObject paramJson) throws Exception;

	/**
	 * 打印小票
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param paramJson
	 * @throws Exception
	 */
	public void printForCustomerCardRecharge(String tenancyId, Integer storeId, String printCode, JSONObject paramJson) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param printCode
	 * @param paramJson
	 * @param resultJson
	 * @throws Exception
	 */
	public void printForCustomerCardRecharge(String tenancyId, Integer storeId,JSONObject paramJson) throws Exception;
	
	/**
	 * 充值插入操作流水库
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param paramJson
	 * @throws Exception
	 */
	public String addCustomerOperateListForRecharge(String tenancyId, Integer storeId, String OperatType, JSONObject paramJson) throws Exception;

	/**
	 * 获取二维码
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param paramJson
	 * @param path
	 * @throws Exception
	 */
	public Data precreatePaymentForRecharge(String tenantId, Integer storeId, String thirdBillCode, JSONObject paramJson, String path) throws Exception;

	/**
	 * 扫码支付
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param paramJson
	 * @return
	 * @throws Exception
	 */
	public Data barcodePaymentForRecharge(String tenantId, Integer storeId, String thirdBillCode, JSONObject paramJson) throws Exception;

	/**
	 * 会员卡充值
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param paramJson
	 * @return
	 * @throws Exception
	 */
	public Data customerCardRecharge(String tenantId, Integer storeId, String thirdBillCode, JSONObject paramJson) throws Exception;

	/**
	 * 查询充值状态
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param paramJson
	 * @return
	 * @throws Exception
	 */
	public PosCustomerOperateListEntity queryCustomerCardRecharge(String tenantId, Integer storeId, JSONObject paramJson) throws Exception;

	/**
	 * 查询充值详情
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param paramJson
	 * @return
	 * @throws Exception
	 */
	public JSONObject queryCustomerCardRechargeForDetails(String tenantId, Integer storeId, PosCustomerOperateListEntity cardRecharge) throws Exception;

	/**
	 * 取消第三方付款
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param paramJson
	 * @return
	 * @throws Exception
	 */
	public Data cancelThirdPaymentForCardRecharge(String tenantId, Integer storeId,String thirdBillCode, PosCustomerOperateListEntity cardRecharge) throws Exception;

	/**
	 * 取消充值
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param paramJson
	 * @return
	 * @throws Exception
	 */
	public Data cancelCustomerCardRecharge(String tenantId, Integer storeId, PosCustomerOperateListEntity cardRecharge) throws Exception;

	/** 撤销充值
	 * @param tenantId
	 * @param storeId
	 * @param cardRecharge
	 * @return
	 * @throws Exception
	 */
	public Data revokeCustomerCardRecharge(String tenantId, Integer storeId, String thirdBillCode,JSONObject paramJson) throws Exception;

	/**
	 * 生成电子发票
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param paramJson
	 * @return
	 * @throws Exception
	 */
	public JSONObject createElectricInvoice(String tenantId, Integer storeId, JSONObject paramJson) throws Exception;

	/**
	 * 取消电子发票
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param paramJson
	 * @return
	 * @throws Exception
	 */
	public Data cancelElectricInvoice(String tenantId, Integer storeId, JSONObject paramJson) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param paramJson
	 * @throws Exception
	 */
	public void updateCustomerOperateListStateForCardRecharge(String tenancyId, int storeId, JSONObject paramJson) throws Exception;
	
	/**请求CRM总部查询充值
	 * @param tenantId
	 * @param storeId
	 * @param operType
	 * @param customerOperate
	 * @throws Exception
	 */
	public Data queryCustomerCardRechargeToCrm(String tenantId, Integer storeId,String operType, PosCustomerOperateListEntity customerOperate,JSONObject printJson) throws Exception;

	/**
	 * @param tenantId
	 * @param storeId
	 * @param thirdBillCode
	 * @param paramJson
	 * @param cardRecharge
	 * @param oper
	 * @return
	 * @throws Exception
	 */
	public Data customerCardRechargeManage(String tenantId, Integer storeId, String thirdBillCode, JSONObject paramJson, PosCustomerOperateListEntity cardRecharge, Oper oper) throws Exception;
	
}
