package com.tzx.thirdpay.bo;

import net.sf.json.JSONObject;

import com.tzx.framework.common.entity.Data;
import com.tzx.pos.bo.dto.PosThirdPaymentOrderEntity;

public interface ThirdPaymentService
{
	String	NAME	= "com.tzx.thirdpay.bo.imp.ThirdPaymentServiceImp";

	/**
	 * 获取请求地址
	 * 
	 * @return
	 * @throws Exception
	 */
	public String getRequestPath() throws Exception;

	/**
	 * 扫码支付
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param paramJson
	 *            { "order_num" : "13620170726000004_00096201707260003",
	 *            "order_no" :
	 *            "13620170726000004_00096201707260003@1501052752000",
	 *            "total_amount" : 11, "amount" : "11", "subject" : "门店交易dc",
	 *            "body" : "微信支付餐费，订单号：13620170726000004，金额：11", "chanel" :
	 *            "MD01", "channel" : "MD01", "service_type" : "pos04",
	 *            "shift_id" : 91, "client_ip" : "**********", "report_date" :
	 *            "2017-07-26", "pos_num" : "00096", "is_invoice" : "0",
	 *            "pay_id" : "221", "opt_num" : "218",
	 * 
	 *            }
	 * @param path
	 * @return
	 * @throws Exception
	 */
	public Data precreatePayment(String tenantId, int storeId, JSONObject paramJson, String path) throws Exception;

	/**
	 * 被扫支付
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param paramJson
	 * @return
	 * @throws Exception
	 */
	public Data barcodePayment(String tenantId, int storeId, JSONObject paramJson) throws Exception;

	/**
	 * 支付状态查询
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param aidOrderNum
	 * @return
	 * @throws Exception
	 */
	public Data queryPayment(String tenantId, int storeId, String aidOrderNum) throws Exception;

	/**
	 * 支付状态查询
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param paymentOrder
	 * @return
	 * @throws Exception
	 */
	public Data queryPayment(String tenantId, int storeId, PosThirdPaymentOrderEntity paymentOrder) throws Exception;

	/**
	 * @param tenantId
	 * @param storeId
	 * @param result
	 * @param thirdPaymentJson
	 * @param responseData
	 * @throws Exception
	 */
	public void queryPaymentResult(String tenantId, int storeId, Data responseData, PosThirdPaymentOrderEntity thirdPaymentJson) throws Exception;

	/**
	 * 取消支付
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param paramJson
	 *            {
	 * @param "order_no" : "13620170726000004_00096201707260003@1501052752000",
	 * @param "settle_amount" : "11",
	 * @param "payment_id" : "",
	 * @param "service_type" : "pos04",
	 * @param "channel" : "MD01",
	 * @param "report_date" : "2017-07-26",
	 * @param "shift_id" : 91,
	 * @param "pos_num" : "00096",
	 * @param "opt_num" : "218",
	 * @param "currency_name" : "",
	 * @param
	 * @return
	 * @throws Exception
	 */
	public Data cancelPayment(String tenancyId, Integer storeId, JSONObject paramJson) throws Exception;

	/**
	 * 交易退款
	 * 
	 * @throws Exception
	 */
	@Deprecated
	public Data refundPayment(String tenantId, int storeId, JSONObject paramJson) throws Exception;

	/**
	 * 退款状态查询
	 * 
	 * @throws Exception
	 */
	@Deprecated
	public Data queryRefundPayment(String tenantId, int storeId, JSONObject paramJson) throws Exception;
	
	/** 获取第三方记录
	 * @param tenantId
	 * @param storeId
	 * @param paramJson
	 * @return
	 * @throws Exception
	 */
	public PosThirdPaymentOrderEntity getThirdPaymentOrder(String tenantId, int storeId, JSONObject paramJson) throws Exception;

	/** 根据授权码解析付款方式
	 * @param authcode
	 * @return
	 * @throws Exception
	 */
	public JSONObject getPaymentClassByuAuthcode(String tenantId, Integer storeId, String authcode) throws Exception;
}
