package com.tzx.pos.bo.payment.imp.ext;

import java.sql.Timestamp;
import java.util.Date;

import net.sf.json.JSONObject;

import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;

import com.tzx.base.entity.PosBillPayment;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.util.DoubleHelper;
import com.tzx.framework.common.util.Tools;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.bo.payment.PaymentWayService;
import com.tzx.pos.bo.payment.imp.PaymentWayServiceImp;

/**
 * 现金
 *
 */
@Service("com.tzx.pos.bo.payment.imp.ext.CashPaymentWayServiceImp")
public class CashPaymentWayServiceImp extends PaymentWayServiceImp implements PaymentWayService
{
	@Override
	public void payment(String tenantId, int storeId, String billNum, String batchNum,Date reportDate, int shiftId, String posNum, String optNum, String tableCode, JSONObject payment, Timestamp currentTime,String isOnlinePay) throws Exception
	{
		// TODO Auto-generated method stub
		double amount = payment.optDouble("amount");
		double count = payment.optDouble("count");
		int jzid = payment.optInt("jzid");
		String paymentClass = payment.optString("payment_class");
		String paymentName = payment.optString("payment_name");
		String nameEnglish = payment.optString("payment_english_name");
		double exchangeRate = payment.optDouble("rate");
		String remark = payment.optString("remark");
		String isYsk = payment.optString("is_ysk");
		if (Tools.isNullOrEmpty(isYsk))
		{
			isYsk = "N";
		}

		double cashAmount = 0d;
		StringBuilder findPaymentSql = new StringBuilder("select amount from pos_bill_payment where bill_num = ? and jzid = ? and store_id = ?");
		SqlRowSet rs = paymentDao.query4SqlRowSet(findPaymentSql.toString(), new Object[]
		{ billNum, jzid, storeId });
		if (rs.next())
		{
			cashAmount = rs.getDouble("amount");
		}

		if (cashAmount > 0)
		{
			StringBuilder updateBillPaymentSql = new StringBuilder("update pos_bill_payment set amount=?,count=?,report_date=?,shift_id=?,pos_num=?,cashier_num=?,last_updatetime=?,rate=?,currency_amount=?,coupon_buy_price=?,due=?,tenancy_assume=? where store_id = ? and bill_num = ? and jzid = ?");
			cashAmount = DoubleHelper.add(cashAmount, amount, PAYMENT_SCALE);
			double currencyAmount = DoubleHelper.mul(cashAmount, exchangeRate, PAYMENT_SCALE);
			Double due = currencyAmount;
			Double tenancyAssume = 0d;
			if ("0".equals(payment.optString("if_income")))
			{
				due = 0d;
				tenancyAssume = currencyAmount;
			}
			paymentDao.update(updateBillPaymentSql.toString(), new Object[]
			{ cashAmount, 1, reportDate, shiftId, posNum, optNum, currentTime, exchangeRate, currencyAmount, due, due, tenancyAssume, storeId, billNum, jzid });
		}
		else
		{
			double currencyAmount = DoubleHelper.mul(amount, exchangeRate, PAYMENT_SCALE);
//			paymentDao.insertPosBillPayment(tenantId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, nameEnglish, exchangeRate, amount, count, currencyAmount, null, null, isYsk, null, null, remark, currentTime,
//					SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);
			
			Double due = currencyAmount;
			Double tenancyAssume = 0d;
			if ("0".equals(payment.optString("if_income")))
			{
				due = 0d;
				tenancyAssume = currencyAmount;
			}
			
			PosBillPayment paymentEntity = new PosBillPayment(tenantId, storeId, reportDate, shiftId, posNum, optNum, billNum, batchNum, tableCode, paymentClass, jzid, paymentName, nameEnglish, amount, currencyAmount, Double.valueOf(count).intValue(), null, null, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
			paymentEntity.setRate(exchangeRate);
			paymentEntity.setIs_ysk(isYsk);
			paymentEntity.setRemark(remark);
			paymentEntity.setCoupon_buy_price(due);
			paymentEntity.setDue(due);
			paymentEntity.setTenancy_assume(tenancyAssume);
			paymentDao.insertPosBillPayment(tenantId, storeId, paymentEntity);
		}

//		double currencyAmount = DoubleHelper.mul(amount, exchangeRate, PAYMENT_SCALE);
//		paymentDao.insertPosBillPaymentLog(tenantId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, nameEnglish, exchangeRate, amount, count, currencyAmount, null, null, isYsk, null, null, remark, currentTime,
//				SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);
	}

	@Override
	public void paymentDebit(String tenancyId, int storeId, String billNum,String orderNum,String chanel, Double paymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData) throws Exception
	{
		// TODO Auto-generated method stub

	}

	@Override
	public void findPaymentDebit(String tenancyId, int storeId, String billNum,String orderNum,String chanel,Double paymentAmount,JSONObject payment,Timestamp currentTime,JSONObject resultJson,Data resultData) throws Exception
	{
		// TODO Auto-generated method stub

	}

	@Override
	public void paymentRefund(String tenancyId, int storeId, String billNum,String oldBillNum,String orderNum,String chanel,Double billPaymentAmount,JSONObject payment,Timestamp currentTime,JSONObject resultJson,Data resultData,String operType) throws Exception
	{
		// TODO Auto-generated method stub

	}

	@Override
	public void paymentCancel(String tenancyId, int storeId, String billNum, String orderNum, String chanel, Double billPaymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData, String operType) throws Exception
	{
		// TODO Auto-generated method stub
		
	}

	@Override
	public void paymentRegain(String tenancyId, int storeId, String billNum, String orderNum, String chanel, Double billPaymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData, String operType) throws Exception
	{
		// TODO Auto-generated method stub
		
	}

	@Override
	public void paymentDebitResult(String tenancyId, int storeId, String billNum, JSONObject paymentJson) throws Exception
	{
		// TODO 自动生成的方法存根
		
	}
}
