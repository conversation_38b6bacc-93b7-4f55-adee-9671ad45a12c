package com.tzx.base.common.util;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
@Component
public class ApplicationContextHolder implements ApplicationContextAware{
	private static ApplicationContext applicationContext;  
	  
    @Override  
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {  
    	ApplicationContextHolder.applicationContext = applicationContext;  
    }  
  
  
    public static ApplicationContext getApplicationContext() {  
        return applicationContext;  
    }
    
    public static Object getBean(String beanName) {
    	if(applicationContext.containsBean(beanName)) {
    		return applicationContext.getBean(beanName);  
    	}
    	return null;
    }  
      
    public static <T>T getBean(String beanName , Class<T>clazz) {  
    	if(applicationContext.containsBean(beanName)) {
    		return applicationContext.getBean(beanName , clazz);  
    	}
    	return null;
    }  
}
