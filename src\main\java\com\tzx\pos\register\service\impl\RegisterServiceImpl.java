package com.tzx.pos.register.service.impl;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.tzx.pos.register.dao.RegisterDao;
import com.tzx.pos.register.service.RegisterService;
import com.tzx.pos.register.util.DeviceCodeQueen;
import com.tzx.pos.register.util.GetSql;

import net.sf.json.JSONObject;

@Service(RegisterService.NAME)
public class RegisterServiceImpl implements RegisterService {
	// final static String localCodeIndex = "lc";
	// final static String localCodeMiddle = "_";
	// final static String localCodelast = "(\\d){5}";
	final static String stringFormat = "%1s%03d";
	// final static String pattern = localCodeIndex + localCodeMiddle +
	// localCodelast;
	@Resource(name = RegisterDao.NAME)
	private RegisterDao registerDao;

	@Autowired
	private DeviceCodeQueen deviceCodeQueen;

	@Override
	@Transactional
	public boolean saveDevices(JSONObject jsonParam) throws Exception {

		JSONObject json = deviceCodeQueen.take();
		if (json == null) {
			return false;
		} else {
			List<JSONObject> maxDevicesCode = queryHqLocalDeviceMaxId();
			String device_code = getDevicesCode(maxDevicesCode.get(0).getString("id"),
					jsonParam.getString("store_id").toString());

			jsonParam.put("devices_name", jsonParam.getString("show_type") + device_code);

			jsonParam.put("devices_code", device_code);
			String hqDevicesSql = GetSql.returnInsertSql(jsonParam, "hq_devices");
			// Map<String, Object> map =
			// GetSql.returnPreparedInsertSql(jsonParam, "hq_devices",
			// registerDao);
			JSONObject hqLocalDevicesParam = jsonParam;
			hqLocalDevicesParam.put("status", 1);
			String hqLocalDevicesSql = GetSql.returnInsertSql(hqLocalDevicesParam, "hq_local_devices");

			// int i = registerDao.update(map.get("sql").toString(), (Object[])
			// map.get("param"));
			registerDao.execute(null, hqDevicesSql);
			return registerDao.execute(null, hqLocalDevicesSql);

		}

	}

	@Override
	public List<JSONObject> registerByCode(JSONObject jsobj) throws Exception {
		String device_unique_code = null;
		if (jsobj.getString("device_unique_code").equals("")) {
			device_unique_code = jsobj.getString("devices_ip");
		} else {
			device_unique_code = jsobj.getString("device_unique_code");
		}

		String localSql = "select * from  hq_local_devices " + "where  devices_properties='CLIENT' "
				+ "and  device_type='OTHER_TERMINAL'  " + "and  device_unique_code='" + device_unique_code
				+ "'  order  by  valid_state desc  limit  1  offset 0";
		String saasSql = "select * from  hq_devices " + "where devices_properties='CLIENT' "
				+ "and device_type='OTHER_TERMINAL' " + "and	  device_unique_code='" + device_unique_code
				+ "'  order  by  valid_state desc  limit  1  offset 0";
		List<JSONObject> listJson = null;

		listJson = registerDao.query4Json(null, saasSql);
		if (listJson.size() == 0) {
			listJson = registerDao.query4Json(null, localSql);
		}

		// TODO Auto-generated catch block

		return listJson;
	}

	@Override
	public List<JSONObject> registerByDeviceIp(JSONObject jsobj) throws Exception {

		String saasSql = "select * from  hq_devices where  devices_ip='" + jsobj.getString("devices_ip")
				+ "' order  by  valid_state desc  limit  1  offset 0";
		List<JSONObject> listJson = registerDao.query4Json(null, saasSql);

		return listJson;
	}

	@Override
	public List<JSONObject> queryOrgan() throws Exception {
		String sql = "select tenancy_id,id,org_full_name,org_short_name,"
				+ "org_type,top_org_id,phone,fax,email,website,longitude,"
				+ "avgprice,label_value,address,post_code,bussiness_area,"
				+ "setup_date,book_food_phone,complain_phone,charge_person,"
				+ "contract_close_date,org_create_person,organ_code,tenant_state,"
				+ "org_uuid,district_name,store_type,father_store,brand,price_system,"
				+ "store_manager_no,store_manager_name,alipay_id,alipsy,wechat,operating_status,"
				+ "air_cindition,is_webbook,is_phonebook,is_delivery,is_24time,is_interface,"
				+ "financial_system_code,cuisine,bus_route,remark,summary,image1,image2,"
				+ "last_updatetime,last_operator,third_code,wechat_region,latitude,level,"
				+ "regionalism,qr_codes,is_wechatydzw,is_wechatdddc,is_wechatwmzt,is_wechatpddh,"
				+ "close_date,organ_brief_code,line_up_time1,line_up_time2,line_up_time3,legal_per,"
				+ "manage_type,storelevel,arealevel,format_state,fake_id,poi_id,image3,"
				+ "third_organ_code,taxpayer_type_code,register_key from  organ ";
		List<JSONObject> listJson = registerDao.query4Json(null, sql);

		return listJson;

	}

	@Override
	public List<JSONObject> queryOrganByRegisterKey(JSONObject jsobj) throws Exception {
		String sql = "select * from  organ where  register_key like '%'||'" + jsobj.getString("devices_key") + "'";
		List<JSONObject> listJson = registerDao.query4Json(null, sql);

		return listJson;

	}

	@Override
	public List<JSONObject> queryHqLocalDeviceMaxId() throws Exception {
		String sql = "SELECT COALESCE "
				+ "(MAX (SUBSTRING (devices_code  FROM  CHAR_LENGTH (devices_code) - 2 FOR CHAR_LENGTH (devices_code))),"
				+ "'0',"
				+ "MAX (SUBSTRING (devices_code  FROM  CHAR_LENGTH (devices_code) - 2 FOR CHAR_LENGTH (devices_code)))) ID"
				+ " FROM   hq_devices";
		List<JSONObject> listJson = registerDao.query4Json(null, sql);

		return listJson;

	}

	@Override
	public List<JSONObject> getKvs(JSONObject jsobj) throws Exception {
		String sql = "select  id kvs_id ,kvs_name  from hq_kvs where valid_state='1' and  store_id = '"
				+ jsobj.getString("store_id") + "'";
		List<JSONObject> listJson = registerDao.query4Json(null, sql);

		return listJson;
	}

	@Override
	public List<JSONObject> getSysDictionary(JSONObject jsobj) throws Exception {
		String sql = "select class_item_code ,class_item  from sys_dictionary where class_identifier_code = '"
				+ jsobj.getString("class_identifier_code") + "' and is_sys='Y' and valid_state='1' ";
		List<JSONObject> listJson = registerDao.query4Json(null, sql);

		return listJson;

	}

	@Override
	public List<JSONObject> queryHqLocalDeviceByStatus() throws Exception {
		JSONObject json = new JSONObject();
		String hq_local_devices_sql = "select  *   from  hq_local_devices  l   where l.status=1";
		String hq_devices_kvs_ref_sql = "select * from  hq_devices_kvs_ref k where devices_code in (select  devices_code   from  hq_local_devices  l   where l.status=1) ";
		List<JSONObject> listJson = new ArrayList<JSONObject>();

		json.put("devices", registerDao.query4Json(null, hq_local_devices_sql));
		json.put("kvsref", registerDao.query4Json(null, hq_devices_kvs_ref_sql));
		json.put("tenancy_id", queryOrgan().size() == 0 ? "" : queryOrgan().get(0).getString("tenancy_id"));
		json.put("store_id", queryOrgan().size() == 0 ? "" : queryOrgan().get(0).getString("id"));
		listJson.add(json);

		return listJson;
	}

	@Override
	@Transactional
	public int updateHqLocalDevices(String devicesCode) throws Exception {
		String sql = "update  hq_local_devices  set status=0 where  devices_code='" + devicesCode + "'";

		return registerDao.update(sql);

	}

	@Override
	@Transactional(rollbackFor = { RuntimeException.class, Exception.class })
	public Integer updateRegister(JSONObject jsobj, List<String> localWhereList, List<String> saasWhereList)
			throws Exception {

		JSONObject hq_local_devices_jsonParam = new JSONObject();
		JSONObject hq_devices_jsonParam = new JSONObject();
		for (Iterator i = jsobj.keySet().iterator(); i.hasNext();) {
			Object obj = i.next();
			hq_local_devices_jsonParam.put(obj, jsobj.get(obj));
			hq_devices_jsonParam.put(obj, jsobj.get(obj));
		}
		hq_devices_jsonParam.remove("kvs_id");
		hq_local_devices_jsonParam.remove("kvs_id");
		// where 条件 list
		// localWhereList.add("status");// 本地表的条件
		hq_local_devices_jsonParam.put("status", 1);
		String update_hq_local_devices_sql = GetSql.returnUpdateSql(hq_local_devices_jsonParam, "hq_local_devices",
				localWhereList);
		String update_hq_devices_sql = GetSql.returnUpdateSql(hq_devices_jsonParam, "hq_devices", saasWhereList);
		// 如果是kvs 注册 需要保存信息
		if (jsobj.has("show_type") && jsobj.getString("show_type").equals("KVS")) {
			JSONObject hqDevicesKvsRef = new JSONObject();
			hqDevicesKvsRef.put("tenancy_id", jsobj.getString("tenancy_id").toString());
			hqDevicesKvsRef.put("kvs_id", jsobj.getString("kvs_id"));
			hqDevicesKvsRef.put("devices_code", jsobj.getString("devices_code"));
			List<String> whereList = new ArrayList<String>();
			whereList.add("devices_code");
			String deleteHqDevicesKvsRefSql = GetSql.returnDeleteSql(hqDevicesKvsRef, "hq_devices_kvs_ref", whereList);

			String insertHqDevicesKvsRefSql = GetSql.returnInsertSql(hqDevicesKvsRef, "hq_devices_kvs_ref");

			registerDao.execute(null, deleteHqDevicesKvsRefSql);
			registerDao.execute(null, insertHqDevicesKvsRefSql);

		}

		Integer hqDevices = registerDao.update_hq_devices_sql(update_hq_devices_sql);

		Integer hqLocalsDevices = registerDao.update_hq_local_devices_sql(update_hq_local_devices_sql);
		if (hqLocalsDevices == 0) {
			throw new RuntimeException("更新hqLocalsDevices失败 sql：" + update_hq_local_devices_sql);
		}
		return hqLocalsDevices;
	}

	/**
	 * 获得设备标识
	 * 
	 * @param list
	 * @param storeId
	 * @return
	 */
	public String getDevicesCode(String maxDevicesCode, String storeId) {
		if (maxDevicesCode == null || maxDevicesCode.equals("")) {
			maxDevicesCode = "0";
		}
		if (storeId.length() < 2) {
			storeId = "0" + storeId;
		}
		String devices_code = String.format(stringFormat, "0", Integer.parseInt(maxDevicesCode) + 1);
		return devices_code;
	}

	@Override
	public Long queryHqDevicesCount() throws Exception {
		String sql = "select  count(*)  from  hq_devices";
		Long l = registerDao.countSql(null, sql);

		return l;
	}

	// public static void main(String[] args) {
	// String stringFormat = "%1s%03d";
	// System.out.println(String.format(stringFormat, "2", 2 + 1));
	// }

	@Override
	@Transactional
	public Integer updatePosDataVersion(JSONObject jsobj) throws Exception {
		String sql = "update  pos_data_version  set   para_value='0'  where    para_code='BASIC_DATA_DEVICES'  and  store_id='"
				+ jsobj.getString("store_id") + "'  and  tenancy_id='" + jsobj.getString("tenancy_id") + "'";
		Integer count = registerDao.update(sql);

		return count;

	}

}
