package com.tzx.pos.bo.payment.imp.ext;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.util.Tools;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.bo.dto.PosThirdPaymentOrderEntity;
import com.tzx.pos.bo.payment.PaymentWayService;
import com.tzx.pos.bo.payment.imp.PaymentWayServiceImp;
import com.tzx.thirdpay.bo.ThirdPaymentService;

import net.sf.json.JSONObject;

/**
 * 第三方支付 
 *
 */
@Service("com.tzx.pos.bo.payment.imp.ext.SyncreticPayPaymentWayServiceImp")
public class SyncreticPayPaymentWayServiceImp extends PaymentWayServiceImp implements PaymentWayService
{
	@Resource(name = ThirdPaymentService.NAME)
	private ThirdPaymentService		thirdPaymentWayService;

//	@Resource(name = PosPaymentDao.NAME)
//	private PosPaymentDao posPaymentDao;

	@Override
	public void payment(String tenantId, int storeId, String billNum,String batchNum, Date reportDate, int shiftId, String posNum, String optNum, String tableCode, JSONObject payment, Timestamp currentTime,String isOnlinePay) throws Exception
	{
		double amount = payment.optDouble("amount");
		double count = payment.optDouble("count");
		double currencyAmount = payment.optDouble("currency_amount");
		String number = payment.optString("number");
		String phone = payment.optString("phone");
		String billCode = payment.optString("bill_code");

		int jzid = payment.optInt("jzid");
		String paymentClass = payment.optString("payment_class");
		String paymentName = payment.optString("payment_name");
		String nameEnglish = payment.optString("payment_english_name");
		double rate = payment.optDouble("rate");
		String remark = payment.optString("remark");
		String isYsk = payment.optString("is_ysk");
		String payType = payment.optString("pay_type");
		if (Tools.isNullOrEmpty(isYsk))
		{
			isYsk = "N";
		}

		if ("1".equalsIgnoreCase(isOnlinePay))
		{
			paymentDao.insertPosBillPayment(tenantId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, nameEnglish, rate, amount, count, currencyAmount, number, phone, isYsk, null, billCode, remark, currentTime,
					SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);

//			paymentDao.insertPosBillPaymentLog(tenantId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, nameEnglish, rate, amount, count, currencyAmount, number, phone, isYsk, null, billCode, remark, currentTime,
//					SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);
		}
		else
		{
			JSONObject paymentParam = paymentDao.getBillPaymentByJzid(tenantId, storeId, billNum, jzid);
			paymentParam.put("number", number);
			paymentParam.put("phone", phone);
			paymentParam.put("bill_code", billCode);
			paymentParam.put("pay_type", payType);

			int payId = paymentParam.optInt("id");
			
			if (paymentParam.optDouble("currency_amount") == currencyAmount)
			{
				JSONObject updatePayment = new JSONObject();
				updatePayment.put("number", number);
				updatePayment.put("bill_code", billCode);
				updatePayment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
				updatePayment.put("id", payId);
				paymentDao.updateIgnorCase(tenantId, "pos_bill_payment", updatePayment);
			}
			else
			{
				JSONObject updatePayment = new JSONObject();
				updatePayment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_FAILURE);
				updatePayment.put("id", payId);
				paymentDao.updateIgnorCase(tenantId, "pos_bill_payment", updatePayment);
			}
		}
	}
	
	@Override
	public void paymentDebit(String tenancyId, int storeId, String billNum,String orderNum,String chanel, Double paymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData) throws Exception
	{
		this.findPaymentDebit(tenancyId, storeId, billNum, orderNum, chanel, paymentAmount, payment, currentTime, resultJson, resultData);

		switch (payment.optString("payment_state"))
		{
			case SysDictionary.PAYMENT_STATE_PAY_COMPLETE:
				resultData.setCode(Constant.CODE_SUCCESS);
				break;
			case SysDictionary.PAYMENT_STATE_PAY:
				resultData.setCode(Constant.PAYMENT_STATUS_PAYING_CODE);
				break;
		}
	}

	@Override
	public void findPaymentDebit(String tenancyId, int storeId, String billNum,String orderNum,String chanel,Double paymentAmount,JSONObject payment,Timestamp currentTime,JSONObject resultJson,Data resultData) throws Exception
	{
		// 查询第三方支付表记录
		String orderNo = billNum;
		if ((!SysDictionary.CHANEL_MD01.equals(chanel)) && Tools.hv(orderNum))
		{
			orderNo = orderNum;
		}
		else
		{
			if (Tools.hv(payment.optString("batch_num"))&&!"null".equals(payment.optString("batch_num")))
			{
				orderNo = orderNo + "_" + payment.optString("batch_num");
			}
			Timestamp paymenTime = DateUtil.parseTimestamp(payment.optString("last_updatetime"));
			orderNo = orderNo + "@" + String.valueOf(paymenTime.getTime());
		}
		// JSONObject json = paymentDao.getPayThirdByOrderNum(tenancyId,storeId,orderNo, SysDictionary.SERVICE_TYPE_CONSUME,payment.optString("payment_class"), SysDictionary.THIRD_PAY_CHARGE);

		PosThirdPaymentOrderEntity thirdPaymentOrder = paymentDao.getThirdPaymentByAidOrderNum(tenancyId, storeId, orderNo, SysDictionary.THIRD_PAY_CHARGE);
		if (null != thirdPaymentOrder)
		{
			resultData.setCode(Constant.CODE_SUCCESS);
			if (SysDictionary.REQUEST_STATUS_COMPLETE.equals(thirdPaymentOrder.getRequest_status()))
			{
				String status = thirdPaymentOrder.getStatus();
				if (SysDictionary.THIRD_PAY_STATUS_SUCCESS.equals(status))
				{
					payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
					payment.put("bill_code", thirdPaymentOrder.getTransaction_no());
					payment.put("pay_type", thirdPaymentOrder.getPay_type());
				}
				else if (SysDictionary.THIRD_PAY_STATUS_FAIL.equals(status))
				{
					payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_FAILURE);
					resultData.setMsg(thirdPaymentOrder.getFailure_msg());
				}
				else if ((SysDictionary.THIRD_PAY_STATUS_PAYING.equals(status) || SysDictionary.THIRD_PAY_STATUS_NOOK.equals(status)) && Constant.THIRD_PAYMENT_MAX_QUERY_COUNT <= thirdPaymentOrder.getQuery_count())
				{
					Data resultData1 = thirdPaymentWayService.queryPayment(tenancyId, storeId, orderNo);

					List<?> returnDataList = resultData1.getData();
					String paymentState = SysDictionary.PAYMENT_STATE_PAY;
					JSONObject returnJson = new JSONObject();
					if (null != returnDataList && returnDataList.size() > 0)
					{
						returnJson = JSONObject.fromObject(returnDataList.get(0));
						paymentState = returnJson.optString("payment_state");
					}

					switch (paymentState)
					{
						case SysDictionary.THIRD_PAY_STATUS_SUCCESS:
							payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
							payment.put("bill_code", returnJson.optString("transaction_no"));
							break;
						case SysDictionary.THIRD_PAY_STATUS_PAYING:
						case SysDictionary.THIRD_PAY_STATUS_NOOK:
							payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY);
							resultData.setMsg(returnJson.optString("failure_msg"));
							break;
						default:
							payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_FAILURE);
							resultData.setMsg(returnJson.optString("failure_msg"));
							break;
					}
				}
				else
				{
					payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY);
					resultData.setMsg(thirdPaymentOrder.getFailure_msg());
				}
			}
			else
			{
				payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY);
				resultData.setMsg(thirdPaymentOrder.getRequest_msg());
			}
		}
		else
		{
			payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_FAILURE);
			resultData.setCode(PosErrorCode.THIRD_PAYMENT_FAILURE.getNumber());
			resultData.setMsg(PosErrorCode.THIRD_PAYMENT_FAILURE.getMessage());
		}

		if (Constant.CODE_SUCCESS == resultData.getCode())
		{
			if (SysDictionary.PAYMENT_STATE_PAY_COMPLETE.equals(payment.optString("payment_state")))
			{
				// 修改付款状态
				int payId = payment.optInt("id");
				String number = payment.optString("number");
				String billCode = payment.optString("bill_code");

				String paymentType = payment.optString("pay_type");

				JSONObject updatePaymentJson = new JSONObject();

				if (Tools.hv(paymentType) && false == payment.optString("type").equals(paymentType))
				{
					JSONObject paymentWayJson = paymentDao.getPaymentWayByPaymentClass(tenancyId, storeId, paymentType);
					if (null != paymentWayJson && !paymentWayJson.isEmpty())
					{
						updatePaymentJson.put("type", paymentWayJson.optString("payment_class"));
						updatePaymentJson.put("jzid", paymentWayJson.optInt("payment_id"));
						updatePaymentJson.put("name", paymentWayJson.optString("payment_name"));
						updatePaymentJson.put("name_english", paymentWayJson.optString("name_english"));
						updatePaymentJson.put("rate", paymentWayJson.optDouble("rate"));
						updatePaymentJson.put("yjzid", payment.optInt("jzid"));
					}
				}

				updatePaymentJson.put("number", number);
				updatePaymentJson.put("bill_code", billCode);
				updatePaymentJson.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
				updatePaymentJson.put("id", payId);
				paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", updatePaymentJson);
			}
			else if (SysDictionary.PAYMENT_STATE_PAY_FAILURE.equals(payment.optString("payment_state")))
			{
				// 修改付款状态
				JSONObject updatePaymentJson = new JSONObject();
				updatePaymentJson.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_FAILURE);
				updatePaymentJson.put("id", payment.optInt("id"));
				paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", updatePaymentJson);

				resultData.setCode(PosErrorCode.BILL_PAYMENT_ERROP.getNumber());
				resultData.setMsg(PosErrorCode.BILL_PAYMENT_ERROP.getMessage().replace("{0}", resultData.getMsg()));
			}
		}
		else if (Constant.CODE_CONN_EXCEPTION == resultData.getCode())
		{
			resultData.setCode(Constant.CODE_CONN_EXCEPTION);
			resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
		}
		else
		{
			// 修改付款状态
			JSONObject updatePaymentJson = new JSONObject();
			updatePaymentJson.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_FAILURE);
			updatePaymentJson.put("id", payment.optInt("id"));
			paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", updatePaymentJson);

			resultData.setCode(PosErrorCode.BILL_PAYMENT_ERROP.getNumber());
			resultData.setMsg(PosErrorCode.BILL_PAYMENT_ERROP.getMessage().replace("{0}", resultData.getMsg()));
		}
	}
	
	@Override
	public void paymentDebitResult(String tenancyId, int storeId, String billNum, JSONObject paymentJson) throws Exception
	{
		// TODO 自动生成的方法存根
		this.paymentDebitSuccess(tenancyId, storeId, billNum, paymentJson);
	}

	@Override
	public void paymentRefund(String tenancyId, int storeId, String billNum,String oldBillNum,String orderNum,String chanel,Double billPaymentAmount,JSONObject payment,Timestamp currentTime,JSONObject resultJson,Data resultData,String operType) throws Exception
	{
		String orderNo = oldBillNum;
//		if (Type.CANCBILL.name().equals(operType))
//		{
//			payment = paymentDao.getBillPaymentByJzid(tenancyId, storeId, oldBillNum, payment.optInt("jzid"));
//		}
		payment = paymentDao.getBillPaymentByJzid(tenancyId, storeId, oldBillNum, payment.optInt("jzid"));
		
		if ((!SysDictionary.CHANEL_MD01.equals(chanel)) && Tools.hv(orderNum))
		{
			orderNo = orderNum;
		}
		else
		{
			if(Tools.hv(payment.optString("batch_num"))&&!"null".equals(payment.optString("batch_num")))
			{
				orderNo = orderNo + "_" + payment.optString("batch_num");
			}
			Timestamp paymenTime = DateUtil.parseTimestamp(payment.optString("last_updatetime"));
			orderNo =  orderNo+ "@" + String.valueOf(paymenTime.getTime());
		}
		/**
		 * 	 第三方支付，取pos_third_payment_order中的air_order_num字段
		 *	 程序自动提取已支付成功的单号，把时间戳过滤，根据过滤后的单号，
		 *	 去做账单关联，关联上后在进行退款；
		 */
		JSONObject jsonObject = paymentDao.getPayThirdByOrderNum(tenancyId, storeId, oldBillNum,orderNo, SysDictionary.THIRD_PAY_CHARGE);
		if(!Tools.isNullOrEmpty(jsonObject)) {
			orderNo = jsonObject.optString("aid_order_num");
		}
		String serviceType = SysDictionary.SERVICE_TYPE_CONSUME;
		if (SysDictionary.CHANEL_WX02.equals(chanel)) {
			serviceType=SysDictionary.SERVICE_TYPE_ORDER;
		}
		
		Integer paymentId = payment.optInt("jzid");
		if(payment.containsKey("yjzid")&&Tools.hv(payment.optInt("yjzid")))
		{
			paymentId = payment.optInt("yjzid");
		}
		
		JSONObject requestJson = new JSONObject();
		requestJson.put("order_num", oldBillNum);
		requestJson.put("total_amount", billPaymentAmount);
		requestJson.put("client_ip", "");
		requestJson.put("refunds_order", payment.optString("bill_code"));
		requestJson.put("order_no", orderNo);
		requestJson.put("settle_amount", Math.abs(payment.optDouble("currency_amount")));
		requestJson.put("payment_id",  paymentId);
		requestJson.put("service_type", serviceType);
		requestJson.put("channel",  chanel);
		requestJson.put("report_date",  payment.optString("report_date"));
		requestJson.put("shift_id",  payment.optInt("shift_id"));
		requestJson.put("pos_num",  payment.optString("pos_num"));
		requestJson.put("opt_num",  payment.optString("cashier_num"));
		requestJson.put("currency_name",  "");
		requestJson.put("oper_type",  operType);

		Data responseData = thirdPaymentWayService.cancelPayment(tenancyId, storeId, requestJson);
		
		if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
		{
			responseData = thirdPaymentWayService.cancelPayment(tenancyId, storeId, requestJson);
		}
		
		if (Constant.CODE_SUCCESS == responseData.getCode())
		{
			resultData.setCode(Constant.CODE_SUCCESS);
			
			JSONObject responseJson = JSONObject.fromObject(responseData.getData().get(0));

			JSONObject paymentJson = new JSONObject();
			paymentJson.put("bill_code", responseJson.optString("transaction_no"));
			paymentJson.put("payment_state", SysDictionary.PAYMENT_STATE_REFUND_COMPLETE);
			paymentJson.put("id", payment.optInt("id"));
			paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", paymentJson);
			
		}
		else if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
		{
			resultData.setCode(Constant.CODE_CONN_EXCEPTION);
			resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
		}
		else
		{
			JSONObject paymentJson = new JSONObject();
			paymentJson.put("payment_state", SysDictionary.PAYMENT_STATE_REFUND_FAILURE);
			paymentJson.put("id", payment.optInt("id"));
			paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", paymentJson);
			
			resultData.setCode(responseData.getCode());
			resultData.setMsg(responseData.getMsg());
		}
	}

	@Override
	public void paymentCancel(String tenancyId, int storeId, String billNum, String orderNum, String chanel, Double billPaymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData, String operType) throws Exception
	{
//		String orderNo = billNum;
//		if (Type.CANCBILL.name().equals(operType))
//		{
//			payment = paymentDao.getBillPaymentByJzid(tenancyId, storeId, billNum, payment.optInt("jzid"));
//		}
		
		String orderNo = billNum;
		if ((!SysDictionary.CHANEL_MD01.equals(chanel)) && Tools.hv(orderNum))
		{
			orderNo = orderNum;
		}
		else
		{
			if (Tools.hv(payment.optString("batch_num")) && !"null".equals(payment.optString("batch_num")))
			{
				orderNo = orderNo + "_" + payment.optString("batch_num");
			}
			Timestamp paymenTime = DateUtil.parseTimestamp(payment.optString("last_updatetime"));
			orderNo = orderNo + "@" + String.valueOf(paymenTime.getTime());
		}
		/**
		 * 	 第三方支付，取pos_third_payment_order中的air_order_num字段
		 *	 程序自动提取已支付成功的单号，把时间戳过滤，根据过滤后的单号，
		 *	 去做账单关联，关联上后在进行退款；
		 */
		JSONObject jsonObject = paymentDao.getPayThirdByOrderNum(tenancyId, storeId, billNum, orderNo, SysDictionary.THIRD_PAY_CHARGE);
		if (!Tools.isNullOrEmpty(jsonObject))
		{
			orderNo = jsonObject.optString("aid_order_num");
		}
		String serviceType = SysDictionary.SERVICE_TYPE_CONSUME;
		if (SysDictionary.CHANEL_WX02.equals(chanel)) {
			serviceType=SysDictionary.SERVICE_TYPE_ORDER;
		}
		
		Integer paymentId = payment.optInt("jzid");
		if(payment.containsKey("yjzid")&&Tools.hv(payment.optInt("yjzid")))
		{
			paymentId = payment.optInt("yjzid");
		}
		
		JSONObject requestJson = new JSONObject();
		requestJson.put("order_num", billNum);
		requestJson.put("total_amount", billPaymentAmount);
		requestJson.put("client_ip", "");
		requestJson.put("refunds_order", payment.optString("bill_code"));
		requestJson.put("order_no", orderNo);
		requestJson.put("settle_amount", Math.abs(payment.optDouble("currency_amount")));
		requestJson.put("payment_id",  paymentId);
		requestJson.put("service_type", serviceType);
		requestJson.put("channel",  chanel);
		requestJson.put("report_date",  payment.optString("report_date"));
		requestJson.put("shift_id",  payment.optInt("shift_id"));
		requestJson.put("pos_num",  payment.optString("pos_num"));
		requestJson.put("opt_num",  payment.optString("cashier_num"));
		requestJson.put("currency_name",  "");
		requestJson.put("oper_type",  operType);

		Data responseData = thirdPaymentWayService.cancelPayment(tenancyId, storeId, requestJson);
		if (Constant.CODE_SUCCESS == responseData.getCode())
		{
			JSONObject responseJson = JSONObject.fromObject(responseData.getData().get(0));
			payment.put("bill_code", responseJson.optString("transaction_no"));

			resultData.setCode(Constant.CODE_SUCCESS);
		}
		else
		{
			if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
			{
				resultData.setCode(Constant.CODE_CONN_EXCEPTION);
				resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
			}
			else if(Constant.THIRD_PAYMENT_ORDER_NOT_EXIST_CODE == responseData.getCode())
			{
				payment.put("bill_code", "");
				resultData.setCode(Constant.CODE_SUCCESS);
				resultData.setMsg(responseData.getMsg());
			}
			else
			{
				resultData.setCode(responseData.getCode());
				resultData.setMsg(responseData.getMsg());
			}
		}
	}

	@Override
	public void paymentRegain(String tenancyId, int storeId, String billNum, String orderNum, String chanel, Double billPaymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData, String operType) throws Exception
	{
		String orderNo = billNum;
//		if (Type.CANCBILL.name().equals(operType))
//		{
//			payment = paymentDao.getBillPaymentByJzid(tenancyId, storeId, billNum, payment.optInt("jzid"));
//		}
		if ((!SysDictionary.CHANEL_MD01.equals(chanel)) && Tools.hv(orderNum))
		{
			orderNo = orderNum;
		}
		else
		{
			if (Tools.hv(payment.optString("batch_num")) && !"null".equals(payment.optString("batch_num")))
			{
				orderNo = orderNo + "_" + payment.optString("batch_num");
			}
			Timestamp paymenTime = DateUtil.parseTimestamp(payment.optString("last_updatetime"));
			orderNo = orderNo + "@" + String.valueOf(paymenTime.getTime());
		}
		/**
		 * 	 第三方支付，取pos_third_payment_order中的air_order_num字段
		 *	 程序自动提取已支付成功的单号，把时间戳过滤，根据过滤后的单号，
		 *	 去做账单关联，关联上后在进行退款；
		 */
		JSONObject jsonObject = paymentDao.getPayThirdByOrderNum(tenancyId, storeId, billNum,orderNo, SysDictionary.THIRD_PAY_CHARGE);
		if(!Tools.isNullOrEmpty(jsonObject)) {
			orderNo = jsonObject.optString("aid_order_num");
		}
		String serviceType = SysDictionary.SERVICE_TYPE_CONSUME;
		if (SysDictionary.CHANEL_WX02.equals(chanel)) {
			serviceType=SysDictionary.SERVICE_TYPE_ORDER;
		}
		Integer paymentId = payment.optInt("jzid");
		if(payment.containsKey("yjzid")&&Tools.hv(payment.optInt("yjzid")))
		{
			paymentId = payment.optInt("yjzid");
		}
		
		JSONObject requestJson = new JSONObject();
		requestJson.put("order_num", billNum);
		requestJson.put("total_amount", billPaymentAmount);
		requestJson.put("client_ip", "");
		requestJson.put("refunds_order", payment.optString("bill_code"));
		requestJson.put("order_no", orderNo);
		requestJson.put("settle_amount", Math.abs(payment.optDouble("currency_amount")));
		requestJson.put("payment_id",  paymentId);
		requestJson.put("service_type", serviceType);
		requestJson.put("channel",  chanel);
		requestJson.put("report_date",  payment.optString("report_date"));
		requestJson.put("shift_id",  payment.optInt("shift_id"));
		requestJson.put("pos_num",  payment.optString("pos_num"));
		requestJson.put("opt_num",  payment.optString("cashier_num"));
		requestJson.put("currency_name",  "");
		requestJson.put("oper_type",  operType);

		Data responseData = thirdPaymentWayService.cancelPayment(tenancyId, storeId, requestJson);
		
		if (Constant.CODE_SUCCESS == responseData.getCode())
		{
			String transactionNo = "";
			if (null != responseData.getData() && 0 <= responseData.getData().size())
			{
				JSONObject responseJson = JSONObject.fromObject(responseData.getData().get(0));
				transactionNo = responseJson.optString("transaction_no");
			}
			payment.put("bill_code", transactionNo);

			resultData.setCode(Constant.CODE_SUCCESS);
		}
		else if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
		{
			resultData.setCode(Constant.CODE_CONN_EXCEPTION);
			resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
		}
		else
		{
			resultData.setCode(responseData.getCode());
			resultData.setMsg(responseData.getMsg());
		}
	}
	
	private void paymentDebitSuccess(String tenancyId, int storeId, String billNum, JSONObject paymentParam) throws Exception
	{
		// TODO Auto-generated method stub
		int payId = paymentParam.optInt("id");
		String number = paymentParam.optString("number");
		String billCode = paymentParam.optString("bill_code");
		
		String paymentType = paymentParam.optString("pay_type");
		
		JSONObject payment = new JSONObject();
		
		if(Tools.hv(paymentType))
		{
			JSONObject paymentWayJson = paymentDao.getPaymentWayByPaymentClass(tenancyId, storeId, paymentType);
			if (null != paymentWayJson && !paymentWayJson.isEmpty())
			{
				payment.put("type", paymentWayJson.optString("payment_class"));
				payment.put("jzid", paymentWayJson.optInt("payment_id"));
				payment.put("name", paymentWayJson.optString("payment_name"));
				payment.put("name_english", paymentWayJson.optString("name_english"));
				payment.put("rate", paymentWayJson.optDouble("rate"));
				payment.put("yjzid", paymentParam.optInt("jzid"));
			}
		}

		payment.put("number", number);
		payment.put("bill_code", billCode);
		payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
		payment.put("id", payId);
		paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", payment);
	}
}
