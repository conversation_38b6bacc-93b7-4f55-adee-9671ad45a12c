package com.tzx.member.common.util;

import java.math.BigDecimal;

import com.tzx.framework.common.util.DoubleHelper;

import net.sf.json.JSONObject;

public class AcewillUtil
{
	/**
	 * @param paramJson
	 * @param key
	 * @return
	 * @throws Exception
	 */
	public static Integer getBoolean(JSONObject paramJson, String key) throws Exception {
		Integer result = 0;
		if (paramJson.containsKey(key)) {
			if (paramJson.getBoolean(key)) {
				result = 1; // 可以
			} else {
				result = 0; // 不可以
			}
		}
		return result;
	}

	/**
	 * 支付方式名称
	 * @param type wx:微信卡 dp:点评卡 ph:实体卡 actual:实体卡
	 * @return
	 */
	public static String getTypeName(String type) {
		String typeName = null;
		switch (type) {
		case "wx":
			typeName = "微信卡";
			break;
		case "dp":
			typeName = "点评卡";
			break;
		case "ph":
			typeName = "实体卡";
			break;
		case "actual":
			typeName = "实体卡";
			break;
		default:
			break;
		}
		return typeName;
	}

	/**
	 * 性别转换
	 * @param :男 -- man 2:女 --woman
	 * @return
	 */
	public static String getSex(String gender) {
		String sex = null;
		switch (gender) {
		case "1":
			sex = "man";
			break;
		case "2":
			sex = "woman";
			break;
		default:
			break;
		}
		return sex;
	}
	
	   /**
     * 	交易类型
     * @param 	1:储值 6:撤销储值 7:被撤销储值 8:手工调整减少储值
     * @return
     */
    public static String getChargeType(int type) {
        String typeName = null;
        switch (type) {
            case 1:
                typeName = "储值";
                break;
            case 6:
                typeName = "撤销储值";
                break;
            case 7:
                typeName = "被撤销储值";
                break;
            case 8:
                typeName = "手工调整减少储值";
                break;
            default:
                break;
        }
        return typeName;
    }


    /**
     *消费交易类型
     * @param 	1:充值 2:消费 3:撤销消费 4:被撤销消费 5:系统回收 6:撤销充值 7:被撤销充值 8:手工调账减少充值 9:手工调账减少积分 10:积分换礼
     * @return
     */
    public static String getConsumeType(int type) {
        String typeName = null;
        switch (type) {
            case 1:
                typeName = "充值";
                break;
            case 2:
                typeName = "消费";
                break;
            case 3:
                typeName = "撤销消费";
                break;
            case 4:
                typeName = "被撤销消费";
                break;
            case 5:
                typeName = "系统回收";
                break;
            case 6:
                typeName = "撤销充值";
                break;
            case 7:
                typeName = "被撤销充值";
                break;
            case 8:
                typeName = "手工调账减少充值";
                break;
            case 9:
                typeName = "手工调账减少积分";
                break;
            case 10:
                typeName = "积分换礼";
                break;
            default:
                break;
        }
        return typeName;
    }

    /**
     * 	储值支付类型
     * @param 	 1:现金 2:银行卡 3:微信 4:支付宝 5:店内微信支付 6:手工调整
     * @return
     */
    public static String getPayType(int type) {
        String typeName = null;
        switch (type) {
            case 1:
                typeName = "现金";
                break;
            case 2:
                typeName = "银行卡";
                break;
            case 3:
                typeName = "微信";
                break;
            case 4:
                typeName = "支付宝";
                break;
            case 6:
                typeName = "店内微信支付";
                break;
            case 11:
                typeName = "百度糯米";
                break;
            case 12:
                typeName = "美团";
                break;
            case 13:
                typeName = "大众点评";
                break;
            case 14:
                typeName = "其他";
                break;
            default:
            	typeName = "其他";
                break;
        }
        return typeName;
    }

    /**
     * 消费类型名称
     * @param type
     * @return
     */
    public static String getCoumerPayType(int type) {
        String typeName = null;
        switch (type) {
            case 1:
                typeName = "现金支付";
                break;
            case 2:
                typeName = "银行卡支付";
                break;
            case 3:
                typeName = "店内微信支付";
                break;
            case 4:
                typeName = "支付宝支付";
                break;
            case 6:
                typeName = "线上微信支付";
                break;
            case 7:
                typeName = "百度糯米";
                break;
            case 8:
                typeName = "美团";
                break;
            case 9:
                typeName = "大众点评";
                break;
            case 10:
                typeName = "其他";
                break;
            case 11:
                typeName = "支票";
                break;
            case 12:
                typeName = "集团消费";
                break;
            default:
                break;
        }
        return typeName;
    }

	/**
	 * 人民币 单位分转单位元
	 * @param amount
	 * @return
	 * @throws Exception
	 */
	public static Double changeF2Y(int amount) throws Exception
	{
		return BigDecimal.valueOf(Long.valueOf(amount)).divide(new BigDecimal(100)).doubleValue();
	}

	/** 人民币 单位元转单位分
	 * @param amount
	 * @return
	 * @throws Exception
	 */
	public static int changeY2F(Double amount) throws Exception
	{
		return DoubleHelper.mul(amount, 100d, 0).intValue();
	}
}
