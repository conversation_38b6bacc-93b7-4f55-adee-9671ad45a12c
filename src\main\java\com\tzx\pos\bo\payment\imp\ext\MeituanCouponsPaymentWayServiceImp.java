package com.tzx.pos.bo.payment.imp.ext;

import com.tzx.base.entity.PosBillPayment;
import com.tzx.base.entity.PosThirdPaymentRefundEntity;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.DoubleHelper;
import com.tzx.framework.common.util.Md5Utils;
import com.tzx.framework.common.util.PropertiesLoader;
import com.tzx.framework.common.util.Tools;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.bo.payment.PaymentWayService;
import com.tzx.pos.bo.payment.imp.PaymentWayServiceImp;
import com.tzx.thirdpay.bo.MeituanCouponsService;
import com.tzx.thirdpay.bo.ThirdPaymentRefundService;
import com.tzx.thirdpay.bo.imp.MeituanCouponsServiceImp;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Service("com.tzx.pos.bo.payment.imp.ext.MeituanCouponsPaymentWayServiceImp")
public class MeituanCouponsPaymentWayServiceImp extends PaymentWayServiceImp implements PaymentWayService
{
	@Resource(name = MeituanCouponsService.NAME)
	private MeituanCouponsService		meituanCouponsService;
	
	@Resource(name = ThirdPaymentRefundService.NAME)
	private ThirdPaymentRefundService		thirdPaymentRefundService;

	@Override
	public void payment(String tenantId, int storeId, String billNum,String batchNum, Date reportDate, int shiftId, String posNum, String optNum, String tableCode, JSONObject payment, Timestamp currentTime,String isOnlinePay) throws Exception
	{
		// TODO Auto-generated method stub
//		String chanel = payment.optString("chanel");

		Double amount = payment.optDouble("amount");
		Double count = payment.optDouble("count");
		Double currencyAmount = payment.optDouble("currency_amount");
		String number = payment.optString("number");
		String phone = payment.optString("phone");
		String billCode = payment.optString("bill_code");

		int jzid = payment.optInt("jzid");
		String paymentClass = payment.optString("payment_class");
		String paymentName = payment.optString("payment_name");
		String nameEnglish = payment.optString("payment_english_name");
		double rate = payment.optDouble("rate");
		String remark = payment.optString("remark");
		String isYsk = payment.optString("is_ysk");

		String paymentUid = UUID.randomUUID().toString().replace("-","");

		if (Tools.isNullOrEmpty(isYsk))
		{
			isYsk = "N";
		}

		if ("1".equalsIgnoreCase(isOnlinePay))
		{
			paymentDao.insertPosBillPayment(tenantId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, nameEnglish, rate, amount, count, currencyAmount, number, phone, isYsk, null, billCode, remark, currentTime,
					SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);

//			paymentDao.insertPosBillPaymentLog(tenantId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, nameEnglish, rate, amount, count, currencyAmount, number, phone, isYsk, null, billCode, remark, currentTime,
//					SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);
		}
		else
		{
//			paymentDao.insertPosBillPayment(tenantId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, nameEnglish, rate, amount, count, currencyAmount, number, phone, isYsk, null, billCode, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY,
//					batchNum, payment.toString(), 0d);

			//查询未使用优惠菜品
			List<JSONObject> billItem=paymentDao.query4Json(null,"select item_num item_code,sum(item_count) item_count_sum,item_price from pos_bill_item where bill_num='"+billNum+"' and item_property in ('SINGLE','SETMEAL') and (discount_amount is null or discount_amount =0) GROUP BY item_num,item_price");

			Map<String,JSONObject> itemMap= new HashMap<>();
			if(!billItem.isEmpty()){
				for(JSONObject item:billItem){
					JSONObject info = new JSONObject();
					info.put("item_count_sum",item.getDouble("item_count_sum"));
					info.put("item_price",item.getDouble("item_price"));

					itemMap.put(item.getString("item_code"),info);

				}
			}

			Map<String,JSONObject> coupousInfoMap = MeituanCouponsServiceImp.coupousInfoMap.get(number);
			Double realAmount=0d;
			if(null!=coupousInfoMap&&!coupousInfoMap.isEmpty()){
				Iterator iterator = coupousInfoMap.keySet().iterator();


				while (iterator.hasNext()){
					String itemCode= (String) iterator.next();

					JSONObject singleCoupon = coupousInfoMap.get(itemCode);
					String coupouItemName=singleCoupon.optString("item_name");
					Double singleCouponCount=singleCoupon.optDouble("count");

					if(itemMap.containsKey(itemCode)) {


						JSONObject item = itemMap.get(itemCode);

						Integer hisDisCount = paymentDao.getJdbcTemplate(null).queryForObject("SELECT COALESCE(sum(count),0) FROM \"pos_bill_payment_coupons_mt\" where bill_num='"+billNum+"' and item_code='"+itemCode+"' and state='1'", Integer.class);

						Double sumCount= item.getDouble("item_count_sum");

						Double realCount=sumCount-hisDisCount;

						Double discountCount = singleCouponCount * count;

						logger.info("账单："+billNum+" 中菜品："+itemCode+"数量："+sumCount+", 已抵扣数量："+hisDisCount+"，预期抵扣数量："+discountCount+" ，剩余可抵扣数量："+realCount);


						if (discountCount > realCount) {
							throw SystemException.getInstance(PosErrorCode.DOUYIN_COUPOUS_ERROR)
									.set("{0}", "").set("{1}", "账单中可抵扣菜品" + coupouItemName + "数量小于" + discountCount);
						}

						realAmount = DoubleHelper.padd(realAmount, DoubleHelper.pmui(discountCount, item.getDouble("item_price")));

						paymentDao.getJdbcTemplate(tenantId).execute("INSERT INTO \"public\".\"pos_bill_payment_coupons_mt\" ( \"tenancy_id\", \"store_id\", \"bill_num\", \"item_code\", \"count\", \"pay_uuid\", \"state\", \"create_time\", \"coupon_code\" )" +
								"VALUES ( '"+tenantId+"', "+storeId+", '"+billNum+"', '"+itemCode+"', '"+discountCount.intValue()+"', '"+paymentUid+"', 0 , 'now()', '"+number+"' )");


					}else {
						throw SystemException.getInstance(PosErrorCode.DOUYIN_COUPOUS_ERROR).set("{0}", "美团验券失败").set("{1}", "账单中没有菜品" + coupouItemName);
					}
				}
			}

			if(realAmount>0){
				logger.info("美团验券成功，预期抵扣金额："+amount);
				amount=realAmount;
				currencyAmount=realAmount;
				logger.info("美团验券成功，实际抵扣金额："+amount);
			}

			PosBillPayment paymentEntity = new PosBillPayment(tenantId, storeId, reportDate, shiftId, posNum, optNum, billNum, batchNum, tableCode, paymentClass, jzid, paymentName, nameEnglish, amount, currencyAmount, count.intValue(), number, phone, currentTime, SysDictionary.PAYMENT_STATE_PAY);
			paymentEntity.setRemark(remark);
			paymentEntity.setPayment_uid(paymentUid);
			paymentDao.insertPosBillPayment(tenantId, storeId, paymentEntity);
		}
	}

	@Override
	public void paymentDebit(String tenancyId, int storeId, String billNum,String orderNum,String chanel, Double paymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData) throws Exception
	{
		// TODO Auto-generated method stub
		int payId = payment.optInt("id");
		String optNum = payment.optString("cashier_num");
//		String batchNum = payment.optString("batch_num");
		
		JSONObject paramJson = new JSONObject();
		paramJson.put("couponCode", payment.optString("number"));
		paramJson.put("count", payment.optDouble("count"));
		if ((!SysDictionary.CHANEL_MD01.equals(chanel)) && Tools.hv(orderNum))
		{
			paramJson.put("eOrderId", orderNum);
		}
		else
		{
			paramJson.put("eOrderId", billNum);
		}
		paramJson.put("couponType", SysDictionary.PAYMENT_CLASS_MEIDA_COUPONS_PAY);
		
		Data responseData = meituanCouponsService.couponsConsume(tenancyId, storeId, paramJson);

		if (responseData.isSuccess())
		{
			payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
			resultData.setCode(Constant.CODE_SUCCESS);
			// 插入优惠劵记录表
			Date reportDate = DateUtil.parseDate(payment.optString("report_date"));
			String paymentId = payment.optString("payment_uid");
			Double dealValue = DoubleHelper.div(payment.optDouble("currency_amount"), payment.optDouble("count"), 4);

			JSONObject responseJson = JSONObject.fromObject(responseData.getData().get(0));

			String dealName = responseJson.optString("dealTitle");
			JSONArray couponCodes = responseJson.optJSONArray("couponCodes");

			List<Object[]> couponList = new ArrayList<Object[]>();
			for (Object obj : couponCodes)
			{
				couponList.add(new Object[]
				{ tenancyId, storeId, billNum, reportDate, paymentId, String.valueOf(obj), dealValue, dealName, currentTime, null });
			}

			if (couponList.size() > 0)
			{
				paymentDao.insertPaymentCoupons(tenancyId, storeId, couponList);
			}

			JSONObject paymentJson = new JSONObject();
			paymentJson.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
			paymentJson.put("id", payId);
			paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", paymentJson);

			paymentDao.getJdbcTemplate(null).execute("update pos_bill_payment_coupons_mt set state=1 where pay_uuid='"+paymentId+"'");
		}
		else if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
		{
			payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY);
			resultData.setCode(Constant.CODE_CONN_EXCEPTION);
			resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
		}
		else if (Constant.PAYMENT_STATUS_PAYING_CODE == responseData.getCode())
		{
			resultData.setCode(Constant.PAYMENT_STATUS_PAYING_CODE);
			resultData.setMsg(PropertiesLoader.getProperty(String.valueOf(responseData.getCode())));
		}
		else
		{
			paymentDao.deletePosBillPaymentByID(tenancyId, storeId, billNum, payId);

			Date reportDate = DateUtil.parseDate(payment.optString("report_date"));
			Integer shiftId = payment.optInt("shift_id");
			String posNum = payment.optString("pos_num");

			int jzid = payment.optInt("jzid");
			String number = payment.optString("number");
			String table_code = payment.optString("table_code");
			String paymentClass = payment.optString("type");
			String paymentName = payment.optString("name");
			String paymentEnglishName = payment.optString("name_english");
			double exchangeRate = payment.optDouble("rate");
			double pay_amount = payment.optDouble("amount");
			double pay_count = payment.optDouble("count");
			double currency_amount = payment.optDouble("currency_amount");
			String phone = payment.optString("phone");
			int customer_id = payment.optInt("customer_id");
			String remark = payment.optString("remark");
			String batch_num = payment.optString("batch_num");
			String is_ysk = Tools.hv(payment.optString("is_ysk")) ? payment.optString("is_ysk") : "N";
			paymentDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, table_code, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, customer_id,
					paramJson.optString("bill_code"), remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_FAILURE, batch_num, paramJson.optString("param_cach"), 0d);

			payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_FAILURE);
			resultData.setCode(PosErrorCode.BILL_PAYMENT_ERROP.getNumber());
			resultData.setMsg(PosErrorCode.BILL_PAYMENT_ERROP.getMessage().replace("{0}", responseData.getMsg()));
		}
	}

	@Override
	public void findPaymentDebit(String tenancyId, int storeId, String billNum, String orderNum,String chanel,Double paymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData) throws Exception
	{
		// TODO Auto-generated method stub
		JSONObject paramJson = new JSONObject();
		paramJson.put("couponCode", payment.optString("number"));
		paramJson.put("couponType", SysDictionary.PAYMENT_CLASS_MEIDA_COUPONS_PAY);
		
		Data responseData = meituanCouponsService.couponsQuery(tenancyId, storeId, paramJson);
		
		if (responseData.isSuccess())
		{
			JSONObject responseJson = JSONObject.fromObject(responseData.getData().get(0));
			if ("已使用".equals(responseJson.optString("couponStatusDesc"))||"已验证".equals(responseJson.optString("couponStatusDesc")))
			{
				payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
				
				// 修改付款状态
				JSONObject updatePaymentJson = new JSONObject();
				updatePaymentJson.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
				updatePaymentJson.put("id", payment.optInt("id"));
				paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", updatePaymentJson);
			
				resultData.setCode(Constant.CODE_SUCCESS);
				// 插入优惠劵记录表
				
				Date reportDate = DateUtil.parseDate(payment.optString("report_date"));
				String paymentId = payment.optString("payment_uid");
				
				String dealName = responseJson.optString("dealTitle");
				String couponCode = responseJson.optString("couponCode");
				Double dealValue = responseJson.optDouble("dealValue");
				
				List<Object[]> couponList = new ArrayList<Object[]>();
				couponList.add(new Object[]{tenancyId,storeId,billNum,reportDate,paymentId,couponCode,dealValue,dealName,currentTime,null});
				
				if(couponList.size()>0)
				{
					paymentDao.insertPaymentCoupons(tenancyId, storeId, couponList);
				}
			}
			else
			{
				payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_FAILURE);
				resultData.setCode(Constant.CODE_PARAM_FAILURE);
				resultData.setMsg(Constant.THIRD_COUPONS_FAILURE);
			}
		}
		else if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
		{
			payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY);
			resultData.setCode(Constant.CODE_CONN_EXCEPTION);
			resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
		}
		else
		{
			payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_FAILURE);
			resultData.setCode(responseData.getCode());
			resultData.setMsg(responseData.getMsg());
		}
		
		if (Constant.CODE_SUCCESS != resultData.getCode() && Constant.CODE_CONN_EXCEPTION != resultData.getCode())
		{
			// 修改付款状态
			JSONObject updatePaymentJson = new JSONObject();
			updatePaymentJson.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_FAILURE);
			updatePaymentJson.put("id", payment.optInt("id"));
			paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", updatePaymentJson);

			resultData.setCode(PosErrorCode.BILL_PAYMENT_ERROP.getNumber());
			resultData.setMsg(PosErrorCode.BILL_PAYMENT_ERROP.getMessage().replace("{0}", resultData.getMsg()));
		}
	}

	@Override
	public void paymentRefund(String tenancyId, int storeId, String billNum, String oldBillNum, String orderNum, String chanel, Double billPaymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData,String operType) throws Exception
	{
		// TODO Auto-generated method stub
		String paymentUid = ParamUtil.getStringValueByObject(payment,"payment_uid");
		String paymentClass = ParamUtil.getStringValueByObject(payment,"payment_class");
		
		Date reportDate = ParamUtil.getDateValueByObject(payment, "report_date");
		Integer shiftId = ParamUtil.getIntegerValueByObject(payment, "shift_id");
		String posNum = ParamUtil.getStringValueByObject(payment, "pos_num");
		String optNum = ParamUtil.getStringValueByObject(payment, "cashier_num");
		Integer paymentWayId = ParamUtil.getIntegerValueByObject(payment, "jzid");
		String oldBillCode = ParamUtil.getStringValueByObject(payment, "bill_code");
		
		List<JSONObject> couponsList = paymentDao.getPaymentCouponsByPaymentId(tenancyId, storeId, billNum, paymentUid);

		if (null != couponsList && couponsList.size() > 0)
		{
			for (JSONObject couponJson : couponsList)
			{
				if ("0".equals(couponJson.optString("is_cancel")))
				{
					String couponsCode = couponJson.optString("coupons_code");
					JSONObject paramJson = new JSONObject();
					paramJson.put("couponCode", couponsCode);
					paramJson.put("type", "1");// 0=是否可撤销;1=执行撤销;
					paramJson.put("couponType", paymentClass);

					Data responseData = meituanCouponsService.couponsCancel(tenancyId, storeId, paramJson);

					if (Constant.CODE_SUCCESS == responseData.getCode())
					{
						paymentDao.updatePaymentCouponsCancelStateById(tenancyId, storeId, billNum, couponsCode);
					}
					else
					{
						if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
						{
							resultData.setCode(Constant.CODE_CONN_EXCEPTION);
							resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
						}
						else
						{
							resultData.setCode(responseData.getCode());
							resultData.setMsg(responseData.getMsg());
						}

						// 退款失败,记录退款异常
						String thirdBillCode = oldBillNum + "_" + couponsCode;
						Double revokedTrading = Math.abs(ParamUtil.getDoubleValueByObject(couponJson, "deal_value"));

						PosThirdPaymentRefundEntity paymentRefund = new PosThirdPaymentRefundEntity(tenancyId, storeId, reportDate, shiftId, posNum, optNum, chanel, SysDictionary.SERVICE_TYPE_CONSUME, oldBillNum, thirdBillCode, oldBillCode, paymentClass, paymentWayId, Math.abs(billPaymentAmount),
								revokedTrading, currentTime, currentTime, optNum, String.valueOf(resultData.getCode()), resultData.getMsg(), operType, SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL, SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL);
						paymentRefund.setExtra(paramJson.toString());

						thirdPaymentRefundService.thirdPaymentRefundFailure(tenancyId, storeId, paymentRefund);
					}
				}
			}
		}
		else
		{
			resultData.setCode(PosErrorCode.NOT_FIND_PAYMENT_COUPONS_ERROR.getNumber());
			resultData.setMsg(PosErrorCode.NOT_FIND_PAYMENT_COUPONS_ERROR.getMessage());
		}
		
		if (Constant.CODE_SUCCESS == resultData.getCode())
		{
			JSONObject paymentJson = new JSONObject();
			paymentJson.put("payment_state", SysDictionary.PAYMENT_STATE_REFUND_COMPLETE);
			paymentJson.put("id", payment.optInt("id"));
			paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", paymentJson);
		}
		else if (Constant.CODE_CONN_EXCEPTION == resultData.getCode())
		{
			resultData.setCode(Constant.CODE_CONN_EXCEPTION);
			resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
		}
		else
		{
			JSONObject paymentJson = new JSONObject();
			paymentJson.put("payment_state", SysDictionary.PAYMENT_STATE_REFUND_FAILURE);
			paymentJson.put("id", payment.optInt("id"));
			paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", paymentJson);
		}
	}

	@Override
	public void paymentCancel(String tenancyId, int storeId, String billNum, String orderNum, String chanel, Double billPaymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData, String operType) throws Exception
	{
		// TODO Auto-generated method stub
		String paymentId = payment.optString("payment_uid");
		String paymentClass = payment.optString("payment_class");
		
		Date reportDate = ParamUtil.getDateValueByObject(payment, "report_date");
		Integer shiftId = ParamUtil.getIntegerValueByObject(payment, "shift_id");
		String posNum = ParamUtil.getStringValueByObject(payment, "pos_num");
		String optNum = ParamUtil.getStringValueByObject(payment, "cashier_num");
		Integer paymentWayId = ParamUtil.getIntegerValueByObject(payment, "jzid");
		String oldBillCode = ParamUtil.getStringValueByObject(payment, "bill_code");
		
		List<JSONObject> couponsList = paymentDao.getPaymentCouponsByPaymentId(tenancyId, storeId, billNum, paymentId);

		if (null != couponsList && couponsList.size() > 0)
		{
			for (JSONObject couponJson : couponsList)
			{
				if ("0".equals(couponJson.optString("is_cancel")))
				{
					String couponsCode = couponJson.optString("coupons_code");
					JSONObject paramJson = new JSONObject();
					paramJson.put("couponCode", couponsCode);
					paramJson.put("type", "1");// 0=是否可撤销;1=执行撤销;
					paramJson.put("couponType", paymentClass);

					Data responseData = meituanCouponsService.couponsCancel(tenancyId, storeId, paramJson);

					if (responseData.isSuccess())
					{
						paymentDao.updatePaymentCouponsCancelStateById(tenancyId, storeId, billNum, couponsCode);
					}
					else
					{
						if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
						{
							resultData.setCode(Constant.CODE_CONN_EXCEPTION);
							resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
						}
						else
						{
							resultData.setCode(responseData.getCode());
							resultData.setMsg(responseData.getMsg());
						}
						
						// 退款失败,记录退款异常
						String thirdBillCode = billNum + "_" + couponsCode;
						Double revokedTrading = Math.abs(ParamUtil.getDoubleValueByObject(couponJson, "deal_value"));

						PosThirdPaymentRefundEntity paymentRefund = new PosThirdPaymentRefundEntity(tenancyId, storeId, reportDate, shiftId, posNum, optNum, chanel, SysDictionary.SERVICE_TYPE_CONSUME, billNum, thirdBillCode, oldBillCode, paymentClass, paymentWayId, Math.abs(billPaymentAmount),
								revokedTrading, currentTime, currentTime, optNum, String.valueOf(resultData.getCode()), resultData.getMsg(), operType, SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL, SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL);
						paymentRefund.setExtra(paramJson.toString());

						thirdPaymentRefundService.thirdPaymentRefundFailure(tenancyId, storeId, paymentRefund);
					}
				}
			}
		}
		else
		{
//			int code = PosErrorCode.NOT_FIND_PAYMENT_COUPONS_ERROR.getNumber();
//			resultData.setCode(code);
//			resultData.setMsg(PropertiesLoader.getProperty(String.valueOf(code)));
			
			resultData.setCode(Constant.CODE_SUCCESS);
		}
	}

	@Override
	public void paymentRegain(String tenancyId, int storeId, String billNum, String orderNum, String chanel, Double billPaymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData, String operType) throws Exception
	{
		// TODO Auto-generated method stub
		String paymentId = payment.optString("payment_uid");
		String paymentClass = payment.optString("payment_class");
		
		Date reportDate = ParamUtil.getDateValueByObject(payment, "report_date");
		Integer shiftId = ParamUtil.getIntegerValueByObject(payment, "shift_id");
		String posNum = ParamUtil.getStringValueByObject(payment, "pos_num");
		String optNum = ParamUtil.getStringValueByObject(payment, "cashier_num");
		Integer paymentWayId = ParamUtil.getIntegerValueByObject(payment, "jzid");
		String oldBillCode = ParamUtil.getStringValueByObject(payment, "bill_code");
		
		List<JSONObject> couponsList = paymentDao.getPaymentCouponsByPaymentId(tenancyId, storeId, billNum, paymentId);

		if (null != couponsList && couponsList.size() > 0)
		{
			for (JSONObject couponJson : couponsList)
			{
				if ("0".equals(couponJson.optString("is_cancel")))
				{
					String couponsCode = couponJson.optString("coupons_code");
					JSONObject paramJson = new JSONObject();
					paramJson.put("couponCode", couponsCode);
					paramJson.put("type", "1");// 0=是否可撤销;1=执行撤销;
					paramJson.put("couponType", paymentClass);

					Data responseData = meituanCouponsService.couponsCancel(tenancyId, storeId, paramJson);

					if (responseData.isSuccess())
					{
						paymentDao.updatePaymentCouponsCancelStateById(tenancyId, storeId, billNum, couponsCode);
					}
					else
					{
						if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
						{
							resultData.setCode(Constant.CODE_CONN_EXCEPTION);
							resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
						}
						else
						{
							resultData.setCode(responseData.getCode());
							resultData.setMsg(responseData.getMsg());
						}
						
						// 退款失败,记录退款异常
						String thirdBillCode = billNum + "_" + couponsCode;
						Double revokedTrading = Math.abs(ParamUtil.getDoubleValueByObject(couponJson, "deal_value"));

						PosThirdPaymentRefundEntity paymentRefund = new PosThirdPaymentRefundEntity(tenancyId, storeId, reportDate, shiftId, posNum, optNum, chanel, SysDictionary.SERVICE_TYPE_CONSUME, billNum, thirdBillCode, oldBillCode, paymentClass, paymentWayId, Math.abs(billPaymentAmount),
								revokedTrading, currentTime, currentTime, optNum, String.valueOf(resultData.getCode()), resultData.getMsg(), operType, SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL, SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL);
						paymentRefund.setExtra(paramJson.toString());

						thirdPaymentRefundService.thirdPaymentRefundFailure(tenancyId, storeId, paymentRefund);
					}
				}
			}
		}
		else
		{
			resultData.setCode(PosErrorCode.NOT_FIND_PAYMENT_COUPONS_ERROR.getNumber());
			resultData.setMsg(PosErrorCode.NOT_FIND_PAYMENT_COUPONS_ERROR.getMessage());
		}
	}

	@Override
	public void paymentDebitResult(String tenancyId, int storeId, String billNum, JSONObject paymentJson) throws Exception
	{
		// TODO 自动生成的方法存根
		JSONObject updatePaymentJson = new JSONObject();
		updatePaymentJson.put("bill_code", paymentJson.optString("bill_code"));
		updatePaymentJson.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
		updatePaymentJson.put("id", paymentJson.optInt("id"));
		paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", updatePaymentJson);
	}
	
}
