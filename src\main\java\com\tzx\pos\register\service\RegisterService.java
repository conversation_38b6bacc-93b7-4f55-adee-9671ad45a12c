package com.tzx.pos.register.service;

import java.util.List;

import net.sf.json.JSONObject;

public interface RegisterService {
	String NAME = "com.tzx.pos.register.service.impl.RegisterServiceImpl";

	boolean saveDevices(JSONObject jsonParam) throws Exception;

	List<JSONObject> registerByCode(JSONObject jsobj) throws Exception;

	List<JSONObject> queryOrgan() throws Exception;

	List<JSONObject> queryOrganByRegisterKey(JSONObject jsobj) throws Exception;

	List<JSONObject> queryHqLocalDeviceMaxId() throws Exception;

	List<JSONObject> getKvs(JSONObject jsobj) throws Exception;

	List<JSONObject> getSysDictionary(JSONObject jsobj) throws Exception;

	List<JSONObject> queryHqLocalDeviceByStatus() throws Exception;

	int updateHqLocalDevices(String devicesCode) throws Exception;

	Integer updateRegister(JSONObject jsobj, List<String> localList, List<String> saasList) throws Exception;

	List<JSONObject> registerByDeviceIp(JSONObject jsobj) throws Exception;

	Long queryHqDevicesCount() throws Exception;

	Integer updatePosDataVersion(JSONObject jsobj) throws Exception;

}
