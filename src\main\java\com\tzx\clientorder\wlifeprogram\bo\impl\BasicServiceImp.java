package com.tzx.clientorder.wlifeprogram.bo.impl;

import com.tzx.base.cache.SoftCache;
import com.tzx.clientorder.wlifeprogram.bo.BasicService;
import com.tzx.clientorder.wlifeprogram.common.constant.Constant;
import com.tzx.clientorder.wlifeprogram.common.constant.WlifePromptMessage;
import com.tzx.clientorder.wlifeprogram.dao.BasicDao;
import com.tzx.framework.common.constant.SysParameterCode;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.OtherSystemException;
import com.tzx.framework.common.util.Tools;
import com.tzx.pos.base.constant.SelectTypeEnum;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.constant.WLifeConstant;
import com.tzx.pos.base.util.JsonUtil;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.member.acewill.bo.AcewillCustomerService;
import com.tzx.clientorder.acewillwechat.po.springjdbc.dao.WshPosEntranceDao;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;

/**
 * Created by qingui on 2018-06-27.
 */
@Service(BasicService.NAME)
public class BasicServiceImp implements BasicService{

    private static final Logger logger = Logger.getLogger(BasicServiceImp.class);
    
    @Resource(name = BasicDao.NAME)
    private BasicDao basicDao;
    @Resource(name = WshPosEntranceDao.NAME)
    private WshPosEntranceDao	wshPosEntranceDao;
    
    @Resource(name = AcewillCustomerService.NAME)
    private AcewillCustomerService acewillCustomerService;

    public static final String WLIFE_BASIC_DATA="WLIFE_BASIC_DATA";

    // 缓存过期时间（30分钟）
    private static final long CACHE_EXPIRE_TIME = 30 * 60 * 1000;
    
    // 缓存数据
    private static final Map<String, CacheEntry> cacheMap = new ConcurrentHashMap<>();
    
    // 缓存锁
    private final ReentrantLock lock = new ReentrantLock();
    
    /**
     * 缓存条目类
     */
    private static class CacheEntry {
        private final JSONObject data;
        private final long expireTime;
        
        public CacheEntry(JSONObject data) {
            this.data = data;
            this.expireTime = System.currentTimeMillis() + CACHE_EXPIRE_TIME;
        }
        
        public boolean isExpired() {
            return System.currentTimeMillis() > expireTime;
        }
        
        public JSONObject getData() {
            return data;
        }
    }

    @Override
    public void getBasicInfo(JSONObject responseJson, JSONObject jsobj,String secret) throws Exception {

        int code=1;
        String msg=WlifePromptMessage.WLIFE_BASIC_SUCCESS;

        // 从缓存获取数据
        CacheEntry entry = cacheMap.get(WLIFE_BASIC_DATA);
        JSONObject data = null;
        
        if (entry != null && !entry.isExpired()) {
            data = entry.getData();
            logger.info("getBasicInfo : data read from cache");
        } else {
            String tenancyId = jsobj.optString("tenancyId");
            int storeId = jsobj.optInt("storeId");
            
            try{
                if(lock.tryLock(10, TimeUnit.SECONDS)){
                    data = this.fillData(tenancyId, String.valueOf(storeId), jsobj, secret);
                    logger.info("getBasicInfo : data read from db");
                    
                    // 更新缓存
                    cacheMap.put(WLIFE_BASIC_DATA, new CacheEntry(data));
                }else {
                    code=0;
                    msg="服务繁忙，请稍候再试！";
                }
            }finally {
                if(lock.isHeldByCurrentThread()){
                    lock.unlock();
                }
            }
        }

        responseJson.put("success", code);
        responseJson.put("msg",msg);
        responseJson.put("data", data);
    }

    @Override
    public void getSoldOutInfo(JSONObject responseJson, JSONObject jsobj) throws Exception {
        String tenancyId = jsobj.optString("tenancyId");
        int storeId = jsobj.optInt("storeId");

        // 获取门店信息
        JSONObject organ = wshPosEntranceDao.selectOrganInfo(tenancyId, storeId+"");
        if(organ == null){
            throw new OtherSystemException(WlifePromptMessage.CODE_NULL_DATASET, WlifePromptMessage.NOT_EXIST_ORGAN_ERROR, null);
        }
        List<JSONObject> dishs = basicDao.getSoldOutInfo(tenancyId, storeId);
        logger.info("微生活小程序查询沽清菜品信息:" + dishs);
        responseJson.put("success", 1);
        responseJson.put("msg", WlifePromptMessage.WLIFE_SOLD_OUT_SUCCESS);
        if (null == dishs || dishs.size() < 1)
            responseJson.put("data", new JSONArray());
        else
            responseJson.put("data", dishs);
    }

    /**
     * 获取基础信息
     * @param tenancy_id
     * @param store_id
     * @param data
     * @throws Exception
     */
    private JSONObject fillData(String tenancy_id, String store_id, JSONObject paramJson,String secret) throws Exception
    {
    	String tableCode = ParamUtil.getStringValueByObject(paramJson, "tableno");
    	long t1 = System.currentTimeMillis();
        List<JSONObject> sysParamList = wshPosEntranceDao.selectStoreParams(tenancy_id, Integer.parseInt(store_id), new String[]
                { WLifeConstant.WLIFE_DISH_KINDS_MODE,SysParameterCode.WLIFE_ORDERMODE_KEY,SysParameterCode.WLIFE_SYNC_MEMO_TYPE_KEY });
        String mode = "2";// mode=1 大类 mode=2小类;默认按小类
        String orderMode = Constant.ORDERMODE_AFTERPAY;// 微生活点餐订单模式, 2:先付 3:后付
		String memoType = Constant.WLIFE_SYNC_MEMO_TYPE_ALL;
		for (JSONObject sysParaJson : sysParamList)
		{
			if (WLifeConstant.WLIFE_DISH_KINDS_MODE.equals(sysParaJson.optString("para_code")))
			{
				mode = sysParaJson.optString("para_value");
			}
			else if (SysParameterCode.WLIFE_ORDERMODE_KEY.equals(sysParaJson.optString("para_code")))
			{
				orderMode = sysParaJson.optString("para_value");
			}
			else if (SysParameterCode.WLIFE_SYNC_MEMO_TYPE_KEY.equals(sysParaJson.optString("para_code")))
			{
				memoType = sysParaJson.optString("para_value");
			}
		}

		long t2 = System.currentTimeMillis();
		logger.debug(secret+"<==获取系统参数，用时：==>" +String.valueOf((t2-t1)/1000d)+"秒");
		
        this.getAcewillGradeRule(tenancy_id, Integer.valueOf(store_id));
        
        long t3 = System.currentTimeMillis();
		logger.debug(secret+"<==获取会员等级，用时：==>" +String.valueOf((t3-t2)/1000d)+"秒");
        
        JSONObject organJson = wshPosEntranceDao.selectOrganInfo(tenancy_id, store_id);
        
        long t4 = System.currentTimeMillis();
		logger.debug(secret+"<==获取机构信息，用时：==>" +String.valueOf((t4-t3)/1000d)+"秒");
        
        JSONObject tableJson = basicDao.getTableInfoByTableCode(tenancy_id, Integer.valueOf(store_id), tableCode);
        String areaId = null;
		if (null != tableJson && Tools.hv(tableJson.optString("table_property_id")))
		{
			areaId = tableJson.optString("table_property_id");
		}
		
		long t5 = System.currentTimeMillis();
		logger.debug(secret+"<==获取当前桌位信息，用时：==>" +String.valueOf((t5-t4)/1000d)+"秒");
		
        JSONObject data = new JSONObject();
		JSONObject dishs = selectDishs(tenancy_id, store_id, mode, organJson.optString("price_system"), areaId,memoType,secret);
		data.put(SelectTypeEnum.DISHS.name, dishs);
		
		long t6 = System.currentTimeMillis();
		logger.debug(secret+"<==获取菜品信息，用时：==>" +String.valueOf((t6-t5)/1000d)+"秒");
		
		selectKindMemos(tenancy_id, store_id, data,memoType);
		
		long t7 = System.currentTimeMillis();
		logger.debug(secret+"<==获取菜品备注，用时：==>" +String.valueOf((t7-t6)/1000d)+"秒");
		
		selectMemo(tenancy_id, store_id, data,memoType);
		
		long t8 = System.currentTimeMillis();
		logger.debug(secret+"<==获取备注，用时：==>" +String.valueOf((t8-t7)/1000d)+"秒");
		
		selectDishKinds(tenancy_id, store_id, data, SysDictionary.CHANEL_WX02, mode);
		
		long t9 = System.currentTimeMillis();
		logger.debug(secret+"<==获取菜品类别，用时：==>" +String.valueOf((t9-t8)/1000d)+"秒");
		
		selectTables(tenancy_id, store_id, data);
		
		long t10 = System.currentTimeMillis();
		logger.debug(secret+"<==获取桌位信息，用时：==>" +String.valueOf((t10-t9)/1000d)+"秒");
		
		selectHeadActivity(tenancy_id, store_id, data);
		selectMarketing(tenancy_id, store_id, data);
//		selectShops(tenancy_id, store_id, data);
		data.put(SelectTypeEnum.SHOPS.name, this.setShopInfo(organJson, orderMode, areaId));
		
		long t11 = System.currentTimeMillis();
		logger.debug(secret+"<==组织机构信息，用时：==>" +String.valueOf((t11-t10)/1000d)+"秒");
		
		return data;
    }

    /** 获取会员等级
     * @param tenancyId
     * @param storeId
     * @throws Exception
     */
    private void getAcewillGradeRule(String tenancyId, Integer storeId) throws Exception
    {
    	Data resData = acewillCustomerService.getAcewillGradeRule(tenancyId, storeId);
        
        StringBuilder memberGrade = new StringBuilder();
        if (!Tools.isNullOrEmpty(resData) && "OK".equals(resData.getMsg())){
            JSONObject obj = (JSONObject) resData.getData().get(0);
            JSONArray jsonArray = obj.optJSONArray("grades");
            Iterator<?> it = jsonArray.iterator();
            while (it.hasNext()){
                JSONObject object = (JSONObject) it.next();
                if (null != memberGrade && memberGrade.length() > 0){
                    memberGrade.append(",");
                }
                memberGrade.append(object.optString("id"));
            }
        }
        logger.info("查询微生活可用会员价，返回的会员等级：" + memberGrade.toString());
        String[] membergid = memberGrade.toString().split(",");

        Constant.membergidMap.put(Constant.MEMBERGID, membergid);
    }
    
    /**
     * 查询菜品信息
     * @param tenancy_id
     * @param store_id
     * @return
     * @throws Exception
     */
    private JSONObject selectDishs(String tenancy_id, String store_id, String classMode,String priceSystem,String areaId,String memoType,String secret) throws Exception{
//         获取门店信息
//        JSONObject organ = wshPosEntranceDao.selectOrganInfo(tenancy_id, store_id);
//        String priceSystem = organ.optString("price_system", "1");
        
        String[] membergid = Constant.membergidMap.get(Constant.MEMBERGID);
        long t1 = System.currentTimeMillis();
        //查询出门店餐谱的菜品信息
        List<JSONObject> item_infos = basicDao.getItemInfos(tenancy_id, store_id);
        long t2 = System.currentTimeMillis();
		logger.debug(secret+"<selectDishs==获取菜品数据，用时：==>" +String.valueOf((t2-t1)/1000d)+"秒");
        
        //获取套餐菜品ID集合
        int[] combo_item_ids = getComboItemIds(item_infos);

        //查询不在餐谱的套餐明细菜品信息
		List<JSONObject> comboItemList = basicDao.getItemInfosForCombo(tenancy_id, store_id, combo_item_ids, getItemIds(item_infos));
		if (null != comboItemList && 0 < comboItemList.size())
		{
			item_infos.addAll(comboItemList);
		}
        //替换一下返回的key值
        replaceName(item_infos);
        long t3 = System.currentTimeMillis();
		logger.debug(secret+"<selectDishs==获取菜品套餐数据，用时：==>" +String.valueOf((t3-t2)/1000d)+"秒");
        
        JSONObject dishs = new JSONObject();
        for(int i = 0; i < item_infos.size(); i++){
            JSONObject json = item_infos.get(i);
            String dishkind =json.optString("dishkind");
            // mode=1 大类 mode=2小类
//            if(2==mode)
            if("2".equals(classMode))
            {
                dishkind = json.optString("pkid");
            }

            if(dishkind == null || "null".equals(dishkind)){
                json.put("dishkind", new String[0]);	//特殊处理
            }else{
                String[] arr = new String[]{dishkind};
                json.put("dishkind", arr);	//特殊处理
            }

            String dishsno = json.optString("dishsno");	//速记码
            dishs.put(dishsno, json);	// 菜品信息以速记码做key
        }

        //dishs.put("dishs", item_infos);	// 菜品信息

        int[] item_ids = getItemIds(item_infos);
        long t4 = System.currentTimeMillis();
		logger.debug(secret+"<selectDishs==菜品类别数据，用时：==>" +String.valueOf((t4-t3)/1000d)+"秒");
		
        //查询时段特价方案
        List<JSONObject> timePriceList = basicDao.getTimePrice(tenancy_id, Integer.parseInt(store_id));
        List<Integer> timePriceIdList = new ArrayList<Integer>();
        for(JSONObject timePriceJson:timePriceList )
        {
        	timePriceIdList.add(ParamUtil.getIntegerValueByObject(timePriceJson, "id"));
        }
        int[] timePriceIds = this.transferIntegerArray(timePriceIdList);
        long t5 = System.currentTimeMillis();
		logger.debug(secret+"<selectDishs==获取时价菜品数据，用时：==>" +String.valueOf((t5-t4)/1000d)+"秒");
        
        List<String> tableItemUnitList = new ArrayList<String>();
        if(Tools.hv(areaId))
        {
        	List<JSONObject> tableItemList = basicDao.getTableItemForPropertyId(tenancy_id, Integer.parseInt(store_id), areaId);
        	for(JSONObject tableItem : tableItemList)
        	{
        		tableItemUnitList.add(tableItem.optString("unit_id"));
        	}
        }
        long t6 = System.currentTimeMillis();
		logger.debug(secret+"<selectDishs==获取桌位菜品数据，用时：==>" +String.valueOf((t6-t5)/1000d)+"秒");
        
        //根据item_id查询unit信息
//        List<JSONObject> unit_infos = basicDao.getUnitInfos(tenancy_id, item_ids, price_system);

        Map<Integer,Double> itemPriceMap = new HashMap<>();

		List<JSONObject> unit_infos = basicDao.getUnitInfos(tenancy_id, item_ids, timePriceIds, priceSystem);
//        for (JSONObject unit : unit_infos){
//            unit.put("bmemberprice", 1);//
//            unit.put("membergid", membergid);//会员等级
//        }
        Iterator<JSONObject> unitIt = unit_infos.iterator();
        while(unitIt.hasNext()){
        	JSONObject unit = unitIt.next();

            //设置菜品默认价格
            if("Y".equals(unit.optString("is_default"))){
               itemPriceMap.put(unit.optInt("item_id"), unit.optDouble("price"));
            }


        	unit.put("bmemberprice", 1);//
            unit.put("membergid", membergid);//会员等级
            
			if (0 < tableItemUnitList.size() && tableItemUnitList.contains(unit.optString("duid"))){
            	unitIt.remove();
            }
        }
        
        replaceName(unit_infos);
        Map<Integer, List<JSONObject>> unitMap = buildMap(unit_infos, "item_id");
        long t7 = System.currentTimeMillis();
		logger.debug(secret+"<selectDishs==获取规格数据，用时：==>" +String.valueOf((t7-t6)/1000d)+"秒");

        // 查询做法信息
        List<JSONObject> cooks_infos = basicDao.getMethodInfos(tenancy_id, item_ids, store_id);
        Map<Integer, List<JSONObject>> cooksMap = buildMap(cooks_infos, "item_id");
        long t8 = System.currentTimeMillis();
		logger.debug(secret+"<selectDishs==获取做法数据，用时：==>" +String.valueOf((t8-t7)/1000d)+"秒");
		
        // 查询套餐信息
        List<JSONObject> selectCombInfos = selectCombInfos(tenancy_id, combo_item_ids,timePriceIds, priceSystem);
        for (JSONObject comb : selectCombInfos){
            comb.put("membergid", membergid);
            comb.put("bmemberprice", 1);
        }
        replaceName(selectCombInfos);
        Map<Integer, List<JSONObject>> combMap = buildMap(selectCombInfos, "item_id");

		Map<Integer, List<JSONObject>> memoMap = null;
        if(Constant.WLIFE_SYNC_MEMO_TYPE_DISH.equals(memoType))
        {
        	memoMap = this.getDishMemos(tenancy_id, Integer.valueOf(store_id));
        }
        long t9 = System.currentTimeMillis();
		logger.debug(secret+"<selectDishs==设置菜品套餐数据，用时：==>" +String.valueOf((t9-t8)/1000d)+"秒");
		
        // 组装
        String[] keys = Tools.getJsonKey(dishs);
        for(String key :keys ) {
        	JSONObject json = dishs.getJSONObject(key);
//        Iterator<?> iterator = dishs.values().iterator();
//        while(iterator.hasNext()){
//            JSONObject json = (JSONObject)iterator.next();
            int item_id = json.optInt("id");

            if(itemPriceMap.containsKey(item_id)){
                json.put("price", itemPriceMap.get(item_id));
            }

            if(unitMap.containsKey(item_id)){
                json.put("norms", unitMap.get(item_id));
                if (Tools.isNullOrEmpty(json.optString("priceName"))){
                    json.put("priceName", unitMap.get(item_id).get(0).optString("name"));
                }
            }else
            {
//                json.put("norms", new JSONArray());
            	dishs.remove(key);
            	continue;
            }

            if(cooksMap.containsKey(item_id)){
                json.put("cooks", cooksMap.get(item_id));
            }else{
                json.put("cooks", new JSONArray());
            }

            if(combMap.containsKey(item_id)){
                json.put("setmeals", combMap.get(item_id).get(0));
            }else{
                json.put("setmeals", new JSONObject());
            }
            
			if (null != memoMap && memoMap.containsKey(item_id))
			{
				json.put("memo", memoMap.get(item_id));
			}else 
			{
				json.put("memo", new JSONArray());
			}
        }
        long t10 = System.currentTimeMillis();
		logger.debug(secret+"<selectDishs==返回值组装，用时：==>" +String.valueOf((t10-t9)/1000d)+"秒");
        return dishs;
    }

    private List<JSONObject> selectCombInfos(String tenancy_id, int[] combo_item_ids, int[] timePriceIds,String price_system) throws Exception{
        //查询套餐信息
//        List<JSONObject> default_unit_infos = basicDao.getComboBaseInfo(tenancy_id, combo_item_ids, price_system);
        List<JSONObject> default_unit_infos = basicDao.getComboBaseInfo(tenancy_id, combo_item_ids, timePriceIds,price_system);

        //套餐的主菜和辅菜都查询出来
        List<JSONObject> detailsInfo = basicDao.selectDetailsInfo(tenancy_id, combo_item_ids);
        // 查询主菜和辅菜
        List<JSONObject> maindish = new ArrayList<JSONObject>();
        Map<Integer, Integer> isGroup = new HashMap<Integer, Integer>();
        for(JSONObject detail : detailsInfo){
            int item_id = detail.optInt("item_id");
            if("N".equals(detail.optString("is_itemgroup"))){
                // 套餐主菜
                maindish.add(detail);
            }else if("Y".equals(detail.optString("is_itemgroup"))){
                // 辅菜
                isGroup.put(detail.optInt("hicd_id"), item_id);
            }
        }
        // 查询主菜信息
        Map<Integer, List<JSONObject>> map0 = buildMap(maindish, "item_id");	// id为菜品信息id

        // 查询辅菜信息
        int[] hicdIds = transferIntegerArray(isGroup.keySet());
        List<JSONObject> mandatoryInfos = getMandatoryInfos(tenancy_id, hicdIds);
        for(JSONObject json : mandatoryInfos){
            Integer hicd_id = json.optInt("hicd_id");
            json.put("item_id", isGroup.get(hicd_id));
        }
        Map<Integer, List<JSONObject>> map1 = buildMap(mandatoryInfos, "item_id");

        // 将主菜和辅菜挂到对应菜品信息下
        for(JSONObject json : default_unit_infos){
            Integer item_id = json.optInt("item_id");
            if(map0.containsKey(item_id)){
                json.put("maindish", map0.get(item_id));
            }else{
                json.put("maindish", new JSONArray());
            }
            if(map1.containsKey(item_id)){
                json.put("mandatory", map1.get(item_id));
            }else{
                json.put("mandatory", new JSONArray());
            }
            json.put("optional", new JSONArray());
            json.put("membergid", new JSONArray());

        }
        return default_unit_infos;
    }

    private List<JSONObject> getMandatoryInfos(String tenancy_id, int[] hicdIds) throws Exception {
        //查询套餐辅菜
        List<JSONObject> list = basicDao.getMandatoryBaseInfo(tenancy_id, hicdIds);
        Set<Integer> groupIdSet = new HashSet<Integer>();
        for(JSONObject json : list){
            Integer id =  json.optInt("mid");
            groupIdSet.add(id);
        }
        int[] groupIds = transferIntegerArray(groupIdSet);
        //查询套餐辅菜的菜品组详细
        List<JSONObject> groupDetails = basicDao.getGroupDetails(tenancy_id, groupIds);
        Map<Integer, List<JSONObject>> groupDetailMap = buildMap(groupDetails, "rpdid");
        for(JSONObject json : list){
            Integer group_id =  json.optInt("mid");
            if(groupDetailMap.containsKey(group_id)){
                json.put("items", groupDetailMap.get(group_id));
            }
        }
        return list;
    }

    private int[] getComboItemIds(List<JSONObject> item_infos) {
        Set<Integer> itemIdSet = new HashSet<Integer>();
        for(JSONObject json : item_infos){
            String isComb = json.optString("type", "1");
            if("2".equals(isComb)){
                Integer item_id = json.optInt("id");
                itemIdSet.add(item_id);
            }
        }
        int[] ids = transferIntegerArray(itemIdSet);
        return ids;
    }

    private int[] getItemIds(List<JSONObject> item_infos) {
        Set<Integer> itemIdSet = new HashSet<Integer>();
        for(JSONObject json : item_infos){
            Integer item_id = json.optInt("id");
            itemIdSet.add(item_id);
        }
        int[] ids = transferIntegerArray(itemIdSet);
        return ids;
    }

    private int[] transferIntegerArray(Collection<Integer> collection){
        Integer[] array = collection.toArray(new Integer[0]);
        int[] ids = new int[array.length];
        for(int i = 0; i < ids.length; i++){
            ids[i] = array[i];
        }
        return ids;
    }

    private <T> Map<T, List<JSONObject>> buildMap(List<JSONObject> list, String attr){
        Map<T, List<JSONObject>> map = new HashMap<T, List<JSONObject>>();
        for(JSONObject json : list){
            T key = (T)json.opt(attr);
            if(!map.containsKey(key)){
                map.put(key, new ArrayList<JSONObject>());
            }
            map.get(key).add(json);
        }
        return map;
    }

    /**
     * 菜品备注数据
     * @param tenancy_id
     * @param store_id
     * @param context
     * @throws Exception
     */
	private void selectKindMemos(String tenancy_id, String store_id, JSONObject context, String memoType) throws Exception
	{
		List<JSONObject> datas = null;
		if (!Constant.WLIFE_SYNC_MEMO_TYPE_DISH.equals(memoType))
		{
			datas = basicDao.getMemos(tenancy_id, store_id);
		}
		else
		{
			datas = new ArrayList<JSONObject>();
		}
        JSONObject data = new JSONObject();
        data.put("all", datas);
        context.put(SelectTypeEnum.KINDS_MEMO.name, data);
    }

	private void selectMemo(String tenancy_id, String store_id, JSONObject context, String memoType) throws Exception
	{
        List<JSONObject> datas = null;
		if (!Constant.WLIFE_SYNC_MEMO_TYPE_DISH.equals(memoType))
		{
			datas = basicDao.getMemos(tenancy_id, store_id);
		}
		else
		{
			datas = new ArrayList<JSONObject>();
		}
        context.put(SelectTypeEnum.MEMO.name, datas);
    }

    /** 查询菜品类别
     * @param tenancy_id
     * @param store_id
     * @param context
     * @param chanel
     * @param classMode
     * @throws Exception
     */
    private void selectDishKinds(String tenancy_id, String store_id, JSONObject context, String chanel, String classMode) throws Exception
    {
        // 查询菜品类别
        List<JSONObject> itemClassList = basicDao.getDishKinds(tenancy_id, Integer.valueOf(store_id), chanel);

        // mode=1 大类 mode=2小类;默认按小类
        List<JSONObject> retList = new ArrayList<JSONObject>();
        for (JSONObject itemClassJson : itemClassList)
        {
        	itemClassJson.put("dkOrder", itemClassJson.optString("seq"));

            String pdkid = itemClassJson.optString("pdkid");
            if (Tools.isNullOrEmpty(pdkid))
            {
                pdkid = "0";
            }

            if ("1".equals(classMode) && "0".equals(pdkid))
            {
                String classId = itemClassJson.optString("id");
                itemClassJson.put("pkid", "0");
                itemClassJson.put("must", "0");
                itemClassJson.put("must_seq", "0");
                itemClassJson.put("suggest", "0");
                itemClassJson.put("icon", "");
                List<String> children = new ArrayList<String>();
                Integer dishCount =0;
                for (JSONObject childrenJson : itemClassList)
                {
                    String childrenId = childrenJson.optString("id");
                    String fatherId = childrenJson.optString("pdkid");
                    if (classId.equals(fatherId) && false == children.contains(childrenId))
                    {
                        children.add(childrenId);
                        dishCount = dishCount+childrenJson.optInt("dish_count");
                    }
                }
                itemClassJson.put("children", children);
                itemClassJson.put("dish_count", dishCount);
                retList.add(itemClassJson);
            }
            else if (!"0".equals(pdkid))
            {
                itemClassJson.put("pkid", pdkid);
                itemClassJson.put("must", "0");
                itemClassJson.put("must_seq", "0");
                itemClassJson.put("suggest", "0");
                itemClassJson.put("icon", "");
                itemClassJson.put("children", "[]");
                retList.add(itemClassJson);
            }
        }
        context.put(SelectTypeEnum.DISH_KINDS.name, retList);
    }

    /**
     * 桌台信息
     * @param tenancy_id
     * @param store_id
     * @param context
     * @throws Exception
     */
    private void selectTables(String tenancy_id, String store_id, JSONObject context) throws Exception{
        List<JSONObject> datas = basicDao.getTables(tenancy_id, store_id);
        context.put(SelectTypeEnum.TABLES.name, datas);
    }

    /**
     * 公告信息
     * @param tenancy_id
     * @param store_id
     * @param context
     * @throws Exception
     */
    private void selectHeadActivity(String tenancy_id, String store_id, JSONObject context) throws Exception{
        // 目前当前系统没有公告信息
        context.put(SelectTypeEnum.HEAD_ACTIVITY.name, new JSONObject());
    }

    /**
     * 营销活动信息
     * @param tenancy_id
     * @param store_id
     * @param context
     * @throws Exception
     */
    private void selectMarketing(String tenancy_id, String store_id, JSONObject context) throws Exception{
        // 目前当前系统没有营销活动
        context.put(SelectTypeEnum.MARKETING.name, new JSONArray());
    }

    /**
     * 门店信息
     * @param tenancy_id
     * @param store_id
     * @param context
     * @throws Exception
     */
    private void selectShops(String tenancy_id, String store_id, JSONObject context) throws Exception{
        List<JSONObject> datas = basicDao.getShops(tenancy_id, store_id);
        JSONObject shopJson = new JSONObject();
        if(datas.size() > 0){
            shopJson = datas.get(0);
        }
        
        if(Tools.isNullOrEmpty(shopJson.optString("ordermode")))
        {
        	shopJson.put("ordermode", Constant.ORDERMODE_AFTERPAY);
        }
        context.put(SelectTypeEnum.SHOPS.name, shopJson);
    }

    /**
     * @param context
     * @param orderMode
     * @param areaId
     * @return
     * @throws Exception
     */
    private JSONObject setShopInfo(JSONObject context,String orderMode,String areaId) throws Exception
    {
    	JSONObject shopJson = new JSONObject();
    	shopJson.put("sid", context.optString("id"));
    	shopJson.put("bid", "");//品牌id
    	shopJson.put("shopname", context.optString("org_full_name"));
    	shopJson.put("shopadd", context.optString("address"));
    	shopJson.put("shoplocation", "");//位置
    	shopJson.put("lng", context.optString("longitude"));
    	shopJson.put("lat", context.optString("latitude"));
    	shopJson.put("is_bind_user", "0");//是否需要用户绑定手机号, 0:否 1:是, 默认:0
    	shopJson.put("ordermode", orderMode);
    	if(Tools.hv(areaId))
    	{
    		shopJson.put("areaid", areaId);
    	}
    	return shopJson;
    }
    
	private static String[]				keys	=
	{ "wxDishs", "priceName", "isWeigh", "limitCount", "dHide", "dOrder" };

    private static Map<String, String> map;
    static{
        map = new ConcurrentHashMap<String, String>();
        for(String key : keys){
            map.put(key.toLowerCase(), key);
        }
    }

    private static void replaceName(List<JSONObject> list){
        for(JSONObject json : list){
            replaceName(json);
        }
    }
    private static void replaceName(JSONObject json){
        String[][] temp = new String[2][1];
        Set<String> set = new HashSet<String>(json.keySet());
        Iterator<String> iterator = set.iterator();
        while(iterator.hasNext()){
            String next = iterator.next();
            if(map.containsKey(next)){
                temp[0][0] = next;
                temp[1][0] = map.get(next);
                JsonUtil.renameKey(json, temp[0] , temp[1]);
            }
        }
    }
    
    /** 查询单品备注
     * @param tenancyId
     * @param storeId
     * @return
     * @throws Exception
     */
    private Map<Integer, List<JSONObject>> getDishMemos(String tenancyId, Integer storeId) throws Exception
	{
		List<JSONObject> itemTasteList = basicDao.getDishMemos(tenancyId, storeId);

		// 根据菜品分组
		Map<Integer, List<JSONObject>> itemTasteMap = this.buildMap(itemTasteList, "item_id");
		
		Map<Integer, List<JSONObject>> dishMemoMap = new HashMap<Integer, List<JSONObject>>();
		for (Integer itemId : itemTasteMap.keySet())
		{
			// 根据上级分类id分组
			Map<Integer, List<JSONObject>> tasteFatherMap = this.buildMap(itemTasteMap.get(itemId), "father_id");
			
			List<JSONObject> memoFatherList = new ArrayList<JSONObject>();
			for (Integer fatherId : tasteFatherMap.keySet())
			{
				//备注明细
				List<JSONObject> memoList = new ArrayList<JSONObject>();
				String fatherName = null;
				for(JSONObject tasteJson:tasteFatherMap.get(fatherId))
				{
					if(null==fatherName)
					{
						fatherName = tasteJson.optString("father_name");
					}
					
					JSONObject memoJson = new JSONObject();
					memoJson.put("omid", tasteJson.optInt("id"));
					memoJson.put("omkid", fatherId);
					memoJson.put("ordermemo", tasteJson.optString("name"));
					memoJson.put("soldout", "1");
					memoJson.put("aprice", 0);
					memoList.add(memoJson);
				}
				
				//备注分组
				JSONObject memoFatherJson = new JSONObject();
				memoFatherJson.put("omkid", fatherId);
				memoFatherJson.put("title", fatherName);
				memoFatherJson.put("is_must", "2");
				memoFatherJson.put("is_radio", "2");
				memoFatherJson.put("min", "0");
				memoFatherJson.put("max", "0");
				memoFatherJson.put("items", memoList);
				memoFatherList.add(memoFatherJson);
			}
			dishMemoMap.put(Integer.valueOf(itemId), memoFatherList);
		}
		return dishMemoMap;
	}
}
