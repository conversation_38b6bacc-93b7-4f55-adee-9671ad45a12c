package com.tzx.clientorder.acewillwechat.bo.imp;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.TreeSet;
import java.util.concurrent.ConcurrentHashMap;

import javax.annotation.Resource;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import com.tzx.base.common.util.PosPropertyUtil;
import com.tzx.framework.common.util.HttpUtil;
import com.tzx.framework.common.util.Tools;
import com.tzx.pos.base.constant.SelectTypeEnum;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.constant.WLifeConstant;
import com.tzx.pos.base.util.JsonUtil;
import com.tzx.clientorder.acewillwechat.bo.WshPosEntranceService;
import com.tzx.clientorder.acewillwechat.po.springjdbc.dao.WshPosEntranceDao;

@Service(WshPosEntranceService.NAME)
public class WshPosEntranceServiceImp implements WshPosEntranceService
{
	private static final Logger logger = Logger.getLogger(WshPosEntranceServiceImp.class);
	
	private static final String ACEWILL_URL_KEY = "acewill.request.url";
	private static final String SHOP_INFO_URI = "/api/shop_info";
	private static final String UNLOCK_ORDER_URI = "/api/Order/unlockOrder";
	private static final String ORDER_QUIT_URI = "/api/Order/orderQuit";
	
	private static final String SUCCESS_KEY = "success";
	private static final String MSG_KEY = "msg";
	
	@Resource(name = WshPosEntranceDao.NAME)
	private WshPosEntranceDao	wshPosEntranceDao;

	@Override
	public JSONObject uploadBaseDataToWsh(String tenancyID, int storeId, JSONObject params)
			throws Exception {
		JSONObject result = new JSONObject();
		result.put(SUCCESS_KEY, false);
		
		String[] paraCodes = new String[]{
				WLifeConstant.BUSSINESS_ID,	//商户id
				WLifeConstant.BRAND_ID,		//品牌id
				WLifeConstant.SHOP_ID			//门店id
		};
		
		List<JSONObject> values = wshPosEntranceDao.selectStoreParams(tenancyID, storeId, paraCodes);
		JSONObject param = new JSONObject();
		for(JSONObject json : values){
			param.put(json.optString("para_code"), json.optString("para_value", json.optString("para_defaut")));
		}
		
//		String isUserWlife = param.optString(WLifeConstant.IS_USER_WLIFE);
//		String userWlifeOrderType = param.optString(WLifeConstant.USER_WLIFE_ORDER_TYPE_KEY);
		// 开始查询基础数据
//		JSONObject storeInfo = wshPosEntranceDao.selectStoreInfo(tenancyID);
//		if(storeInfo == null || storeInfo.optInt("id") == 0){
//			result.put(SUCCESS_KEY, false);
//			result.put(MSG_KEY, "当前门店信息获取失败");
//			return result;
//		}
//		String store_id = storeInfo.optString("id");
//		if("1".equals(isUserWlife))
		if (this.isWlifeOrderH5(tenancyID, storeId))
		{
			try{
				JSONObject data = new JSONObject();
				fillData(tenancyID, String.valueOf(storeId), data);
				logger.info("查询到的基础信息数据为：" + data.toString());
				
				String business_id = param.optString(WLifeConstant.BUSSINESS_ID);
				String brand_id = param.optString(WLifeConstant.BRAND_ID);
				String shop_id = param.optString(WLifeConstant.SHOP_ID);
				
				Map<String, String> postData = new HashMap<String, String>();
				postData.put("business_id", business_id);
				postData.put("brand_id", brand_id);
				postData.put("shop_id", shop_id);
				postData.put("datas", data.toString());
				boolean sendPostToWsh = sendPostToWsh(postData);
				if(sendPostToWsh){
					result.put(SUCCESS_KEY, true);
					result.put(MSG_KEY, "数据上传成功");
				}else{
					result.put(SUCCESS_KEY, true);
					result.put(MSG_KEY, "数据上传失败");
				}
			}catch(Exception e){
				e.printStackTrace();
				result.put(SUCCESS_KEY, false);
				result.put(MSG_KEY, "查询数据失败");
			}
		}
		return result;
	}
	
	/**
	 * 获取基础信息
	 * @param tenancy_id
	 * @param store_id
	 * @param data
	 * @throws Exception 
	 */
	private void fillData(String tenancy_id, String store_id, JSONObject data) throws Exception
	{
		List<JSONObject> uploadClassType = wshPosEntranceDao.selectStoreParams(tenancy_id, Integer.parseInt(store_id), new String[]
		{ WLifeConstant.WLIFE_DISH_KINDS_MODE });
		String mode = "2";// mode=1 大类 mode=2小类;默认按小类
		if (null != uploadClassType && uploadClassType.size() > 0)
		{
			mode = uploadClassType.get(0).optString("para_value");
		}

		selectDishKinds(tenancy_id, store_id, data,SysDictionary.CHANEL_WX02,mode);
		selectTables(tenancy_id, store_id, data);
		selectShops(tenancy_id, store_id, data);
		selectMarketing(tenancy_id, store_id, data);
		selectHeadActivity(tenancy_id, store_id, data);
		selectMemo(tenancy_id, store_id, data);
		selectKindsMemo(tenancy_id, store_id, data);
		selectDishs(tenancy_id, store_id, data);
	}
	
	/**
	 * @param tenancy_id
	 * @param store_id
	 * @param context
	 * @param chanel
	 * @param classMode
	 * @throws Exception
	 */
	private void selectDishKinds(String tenancy_id, String store_id, JSONObject context, String chanel, String classMode) throws Exception
	{
		// 查询菜品类别
		List<JSONObject> itemClassList = wshPosEntranceDao.selectDishKinds(tenancy_id, Integer.valueOf(store_id), chanel);

		// mode=1 大类 mode=2小类;默认按小类
		List<JSONObject> retList = new ArrayList<JSONObject>();
		for (JSONObject itemClassJson : itemClassList)
		{
			String pdkid = itemClassJson.optString("pdkid");
			if (Tools.isNullOrEmpty(pdkid))
			{
				pdkid = "0";
			}

			if ("1".equals(classMode) && "0".equals(pdkid))
			{
				String classId = itemClassJson.optString("id");
				itemClassJson.put("pkid", "0");
				itemClassJson.put("must", "0");
				itemClassJson.put("must_seq", "0");
				itemClassJson.put("suggest", "0");
				itemClassJson.put("suggest", "0");
				itemClassJson.put("icon", "");
				List<String> children = new ArrayList<String>();
				Integer dishCount =0;
				for (JSONObject childrenJson : itemClassList)
				{
					String childrenId = childrenJson.optString("id");
					String fatherId = childrenJson.optString("pdkid");
					if (classId.equals(fatherId) && false == children.contains(childrenId))
					{
						children.add(childrenId);
						dishCount = dishCount+childrenJson.optInt("dish_count");
					}
				}
				itemClassJson.put("children", children);
				itemClassJson.put("dish_count", dishCount);
				retList.add(itemClassJson);
			}
			else if (false == "0".equals(pdkid))
			{
				itemClassJson.put("pkid", pdkid);
				itemClassJson.put("must", "0");
				itemClassJson.put("must_seq", "0");
				itemClassJson.put("suggest", "0");
				itemClassJson.put("suggest", "0");
				itemClassJson.put("icon", "");
				itemClassJson.put("children", "[]");
				retList.add(itemClassJson);
			}
		}
		context.put(SelectTypeEnum.DISH_KINDS.name, retList);
	}

	/**
	 * 查询菜品分类
	 * @param tenancy_id
	 * @param store_id
	 * @param json
	 * @throws Exception
	 */
	@SuppressWarnings("unused")
	private void selectDishKinds(String tenancy_id, String store_id, JSONObject context) throws Exception{
		List<JSONObject> datas = wshPosEntranceDao.selectDishKinds(tenancy_id, store_id);
		convertDishKinds(datas);
		List<JSONObject> uploadClassType = wshPosEntranceDao.selectStoreParams(tenancy_id, Integer.parseInt(store_id), new String[]{WLifeConstant.WLIFE_DISH_KINDS_MODE});
		int mode = uploadClassType.size() > 0 ? uploadClassType.get(0).optInt("para_value", 2) : 2; // 默认按小类
		// mode=1 大类 mode=2小类
		if(mode == 1){
			List<JSONObject> classParents = getClassParents(tenancy_id, store_id, datas);
			datas.clear();
			datas.addAll(classParents);
		}
		context.put(SelectTypeEnum.DISH_KINDS.name, datas);
	}
	
	private List<JSONObject> getClassParents(String tenancy_id, String store_id,
			List<JSONObject> datas) throws Exception {
		Set<Integer> pidSet = new HashSet<Integer>();
		for(JSONObject json : datas){
			Integer pid = json.optInt("pdkid");
			pidSet.add(pid);
		}
		
		int[] pids = transferIntegerArray(pidSet);
		List<JSONObject> pClassData = wshPosEntranceDao.selectDishKindParents(tenancy_id, store_id, pids);
		
		Map<Integer, List<JSONObject>> pClassMap = buildMap(pClassData, "id");
		List<JSONObject> pJsons = new ArrayList<JSONObject>();
		// 遍历子类，填充父类children,dish_count信息
		for(JSONObject json : datas){
			String class_id = json.optString("id");
			int chd_dish_count = json.optInt("dish_count");
			Integer pid = json.optInt("pdkid");
			JSONObject pClass = pClassMap.get(pid).get(0);
			if(!pClass.containsKey("children")){
				pClass.put("children", new JSONArray());
			}
			if(!pClass.containsKey("dish_count")){
				pClass.put("dish_count", 0);
			}
			// 填充子id
			JSONArray kids = (JSONArray)pClass.get("children");
			kids.add(class_id);
			
			// 计算子id的菜品数量和
			int dish_count = pClass.optInt("dish_count");
			dish_count += chd_dish_count;
			pClass.put("dish_count", dish_count);
//			pJsons.add(pClass);
		}
		
//		for(int key:pids)
//		{
//			if(pClassMap.containsKey(key))
//			{
//				pJsons.add(pClassMap.get(key).get(0));
//			}
//		}
		return pClassData;
	}

	private void convertDishKinds(List<JSONObject> datas) {
		String[] needKeys = new String[]{
			"id", "name", "seq", "dishkindsno", "pdkid", "pkid",
			"must", "must_seq", "suggest", "border", "icon", 
			"children", "dish_count"
		};
		Map<String, JSONObject> map = new HashMap<String, JSONObject>();
		for(JSONObject json : datas){
			String class_id = json.optString("id");
			if(!map.containsKey(class_id)){
				JSONObject newJson = new JSONObject();
				JsonUtil.attrCopy(json, newJson, needKeys);
				map.put(class_id, newJson);
			}
		}
		
		Iterator<Entry<String, JSONObject>> iterator = map.entrySet().iterator();
		while(iterator.hasNext()){
			Entry<String, JSONObject> next = iterator.next();
			JSONObject data = next.getValue();
			String class_id = data.optString("id");
			data.put("children", new TreeSet<String>());
			data.put("dish_count", 0);
			for(JSONObject json : datas){
				String pdkid = json.optString("pdkid");
				if(pdkid.equals(class_id)){
					Set<String> children = (TreeSet<String>)data.get("children");
					String chd_id = json.optString("id");
					children.add(chd_id);
				}
				String cid = json.optString("id");
				// 每一条数据对应一条菜品信息，所以用这个统计分类下菜品数
				if(cid.equals(class_id)){
					int dish_count = data.optInt("dish_count");
					dish_count++;
					data.put("dish_count", dish_count);
				}
			}
		}
		datas.clear();
		datas.addAll(map.values());
	}
	
	private void selectTables(String tenancy_id, String store_id, JSONObject context) throws Exception{
		List<JSONObject> datas = wshPosEntranceDao.selectTables(tenancy_id, store_id);
		context.put(SelectTypeEnum.TABLES.name, datas);
	}
	
	private void selectShops(String tenancy_id, String store_id, JSONObject context) throws Exception{
		List<JSONObject> datas = wshPosEntranceDao.selectShops(tenancy_id, store_id);
		JSONObject shopJson = new JSONObject();
		if(datas.size() > 0){
			shopJson = datas.get(0); 
			shopJson.put("ordermode", WLifeConstant.WLIFE_ORDER_MODE);
			shopJson.put("is_bind_user", "0");
			shopJson.put("version", WLifeConstant.WLIFE_SHOP_VERSION);
		}
		context.put(SelectTypeEnum.SHOPS.name, shopJson);
	}
	
	private void selectMarketing(String tenancy_id, String store_id, JSONObject context) throws Exception{
		// 目前当前系统没有营销活动
		context.put(SelectTypeEnum.MARKETING.name, new JSONObject());
	}
	
	private void selectHeadActivity(String tenancy_id, String store_id, JSONObject context) throws Exception{
		// 目前当前系统没有公告信息
		JSONObject json = new JSONObject();
		json.put("image", "");
		json.put("activities", new JSONArray());
		context.put(SelectTypeEnum.HEAD_ACTIVITY.name, json);
	}
	
	private void selectMemo(String tenancy_id, String store_id, JSONObject context) throws Exception{
		List<JSONObject> datas = wshPosEntranceDao.selectMemo(tenancy_id, store_id);
		context.put(SelectTypeEnum.MEMO.name, datas);
	}
	
	private void selectKindsMemo(String tenancy_id, String store_id, JSONObject context) throws Exception{
		List<JSONObject> datas = wshPosEntranceDao.selectMemo(tenancy_id, store_id);
		JSONObject content = new JSONObject();
		content.put("all", datas);
		context.put(SelectTypeEnum.KINDS_MEMO.name, content);
	}
	/**
	 * 查询菜品信息
	 * @param tenancy_id
	 * @param store_id
	 * @return
	 * @throws Exception
	 */
	private void selectDishs(String tenancy_id, String store_id, JSONObject context) throws Exception{
		// 获取门店信息
		JSONObject organ = wshPosEntranceDao.selectOrganInfo(tenancy_id, store_id);
		if(organ == null){
			throw new Exception("门店信息不存在");
		}
		String price_system = organ.optString("price_system", "1");
		
		List<JSONObject> uploadClassType = wshPosEntranceDao.selectStoreParams(tenancy_id, Integer.parseInt(store_id), new String[]{WLifeConstant.WLIFE_DISH_KINDS_MODE});
		int mode = uploadClassType.size() > 0 ? uploadClassType.get(0).optInt("para_value", 2) : 2; // 默认按小类
		
		JSONObject dishs = new JSONObject();
		
		//查询出门店餐谱的菜品信息
		List<JSONObject> item_infos = wshPosEntranceDao.selectItemInfos(tenancy_id, store_id);
		replaceName(item_infos);
		for(int i = 0; i < item_infos.size(); i++){
			JSONObject json = item_infos.get(i);
			String dishkind =json.optString("dishkind");
			// mode=1 大类 mode=2小类
			if(2==mode)
			{
				dishkind = json.optString("pkid");
			}
			
			if(dishkind == null || "null".equals(dishkind)){
				json.put("dishkind", new String[0]);	//特殊处理
			}else{
				String[] arr = new String[]{dishkind};
				json.put("dishkind", arr);	//特殊处理
			}
			String dishsno = json.optString("dishsno");	//速记码
			dishs.put(dishsno, json);	// 菜品信息以速记码做key
		}
		
		int[] item_ids = getItemIds(item_infos);
		
		//根据item_id查询unit信息
		List<JSONObject> unit_infos = wshPosEntranceDao.selectUnitInfos(tenancy_id, item_ids, price_system);
		replaceName(unit_infos);
		Map<Integer, List<JSONObject>> unitMap = buildMap(unit_infos, "item_id");
		
		// 查询做法信息
		List<JSONObject> cooks_infos = wshPosEntranceDao.selectMethodInfos(tenancy_id, item_ids);
		Map<Integer, List<JSONObject>> cooksMap = buildMap(cooks_infos, "item_id");
		
		// 查询套餐信息
		int[] combo_item_ids = getComboItemIds(item_infos);
		List<JSONObject> selectCombInfos = selectCombInfos(tenancy_id, combo_item_ids, price_system);
		replaceName(unit_infos);
		Map<Integer, List<JSONObject>> combMap = buildMap(selectCombInfos, "item_id");
		
		// 组装
		Iterator iterator = dishs.values().iterator();
		while(iterator.hasNext()){
			JSONObject json = (JSONObject)iterator.next();
			int item_id = json.optInt("id");
			if(unitMap.containsKey(item_id)){
				json.put("norms", unitMap.get(item_id));
			}else
			{
				json.put("norms", new JSONArray());
			}

			if(cooksMap.containsKey(item_id)){
				json.put("cooks", cooksMap.get(item_id));
			}else{
				json.put("cooks", new JSONArray());
			}

			if(combMap.containsKey(item_id)){
				json.put("setmeals", combMap.get(item_id).get(0));
			}else{
				json.put("setmeals", new JSONObject());
			}
		}
		context.put(SelectTypeEnum.DISHS.name, dishs);
	}
	
	private List<JSONObject> selectCombInfos(String tenancy_id, int[] combo_item_ids, String price_system) throws Exception{
		List<JSONObject> default_unit_infos = wshPosEntranceDao.selectComboBaseInfo(tenancy_id, combo_item_ids, price_system);
		
		List<JSONObject> detailsInfo = wshPosEntranceDao.selectDetailsInfo(tenancy_id, combo_item_ids);
		// 查询主菜和辅菜
		List<JSONObject> maindish = new ArrayList<JSONObject>();
		Map<Integer, Integer> isGroup = new HashMap<Integer, Integer>();
		for(JSONObject detail : detailsInfo){
			int item_id = detail.optInt("item_id");
			if("N".equals(detail.optString("is_itemgroup"))){
				// 套餐主菜
				maindish.add(detail);	
			}else if("Y".equals(detail.optString("is_itemgroup"))){
				// 辅菜
				isGroup.put(detail.optInt("hicd_id"), item_id);
			}
		}
		// 查询主菜信息
		Map<Integer, List<JSONObject>> map0 = buildMap(maindish, "item_id");	// id为菜品信息id
		
		// 查询辅菜信息
		int[] hicdIds = transferIntegerArray(isGroup.keySet());
		List<JSONObject> mandatoryInfos = getMandatoryInfos(tenancy_id, hicdIds);
		for(JSONObject json : mandatoryInfos){
			Integer hicd_id = json.optInt("hicd_id");
			json.put("item_id", isGroup.get(hicd_id));
		}
		Map<Integer, List<JSONObject>> map1 = buildMap(mandatoryInfos, "item_id");
		
		// 将主菜和辅菜挂到对应菜品信息下
		for(JSONObject json : default_unit_infos){
			Integer item_id = json.optInt("item_id");
			if(map0.containsKey(item_id)){
				json.put("maindish", map0.get(item_id));
			}else{
				json.put("maindish", new JSONArray());
			}
			if(map1.containsKey(item_id)){
				json.put("mandatory", map1.get(item_id));
			}else{
				json.put("mandatory", new JSONArray());
			}
			json.put("optional", new JSONArray());
			json.put("membergid", new JSONArray());
		}
		return default_unit_infos;
	}

	private List<JSONObject> getMandatoryInfos(String tenancy_id, int[] hicdIds) throws Exception {
		List<JSONObject> list = wshPosEntranceDao.selectMandatoryBaseInfo(tenancy_id, hicdIds);
		Set<Integer> groupIdSet = new HashSet<Integer>();
		for(JSONObject json : list){
			Integer id =  json.optInt("hig_id");
			groupIdSet.add(id);
		}
		int[] groupIds = transferIntegerArray(groupIdSet);
		List<JSONObject> groupDetails = wshPosEntranceDao.selectGroupDetails(tenancy_id, groupIds);
		Map<Integer, List<JSONObject>> groupDetailMap = buildMap(groupDetails, "group_id");
		for(JSONObject json : list){
			Integer group_id =  json.optInt("hig_id");
			if(groupDetailMap.containsKey(group_id)){
				json.put("items", groupDetailMap.get(group_id));
			}
		}
		return list;
	}

	private int[] getComboItemIds(List<JSONObject> item_infos) {
		Set<Integer> itemIdSet = new HashSet<Integer>();
		for(JSONObject json : item_infos){
			String isComb = json.optString("type", "1");
			if("2".equals(isComb)){
				Integer item_id = json.optInt("id");
				itemIdSet.add(item_id);
			}
		}
		int[] ids = transferIntegerArray(itemIdSet);
		return ids;
	}

	private int[] getItemIds(List<JSONObject> item_infos) {
		Set<Integer> itemIdSet = new HashSet<Integer>();
		for(JSONObject json : item_infos){
			Integer item_id = json.optInt("id");
			itemIdSet.add(item_id);
		}
		int[] ids = transferIntegerArray(itemIdSet);
		return ids;
	}
	
	private int[] transferIntegerArray(Collection<Integer> collection){
		Integer[] array = collection.toArray(new Integer[0]);
		int[] ids = new int[array.length];
		for(int i = 0; i < ids.length; i++){
			ids[i] = array[i];
		}
		return ids;
	}
	
	private <T> Map<T, List<JSONObject>> buildMap(List<JSONObject> list, String attr){
		Map<T, List<JSONObject>> map = new HashMap<T, List<JSONObject>>();
		for(JSONObject json : list){
			T key = (T)json.opt(attr);
			if(!map.containsKey(key)){
				map.put(key, new ArrayList<JSONObject>());
			}
			map.get(key).add(json);
		}
		return map;
	}

	/**
	 * 向微生活微信推送基础数据
	 * @param business_id
	 * @param brand_id
	 * @param shop_id
	 * @param data
	 * @return
	 */
	private boolean sendPostToWsh(Map<String, String> postData) {
		String reqURL = PosPropertyUtil.getMsg(ACEWILL_URL_KEY, new String[0]);
		reqURL = reqURL + WLifeConstant.UPLOAD_SHOP_INFO_URL;
		logger.info("向微生活推送基础信息参数: " + postData);
		logger.info("向微生活推送请求的url: " + reqURL);
		String postResult = HttpUtil.sendPostRequest(reqURL, postData);
		logger.info("向微生活推送基础信息的返回值: " + postResult);
		JSONObject postResultJson = JSONObject.fromObject(postResult);
		if(postResultJson.optInt("ok", -100) == 1){
			return true;
		}else{
			return false;
		}
	}
	
	private static String[] keys = {
		"wxDishs", "priceName", "isWeigh", "limitCount", "limitCount"
	};
	
	private static Map<String, String> map;
	static{
		map = new ConcurrentHashMap<String, String>();
		for(String key : keys){
			map.put(key.toLowerCase(), key);
		}
	}
	private static void replaceName(List<JSONObject> list){
		for(JSONObject json : list){
			replaceName(json);
		}
	}
	private static void replaceName(JSONObject json){
		String[][] temp = new String[2][1];
		Set<String> set = new HashSet<String>(json.keySet());
		Iterator<String> iterator = set.iterator();
		while(iterator.hasNext()){
			String next = iterator.next();
			if(map.containsKey(next)){
				temp[0][0] = next;
				temp[1][0] = map.get(next);
				JsonUtil.renameKey(json, temp[0] , temp[1]);
			}
		}
	}

	@Override
	public JSONObject notifyWshUnlockOrder(String tenantId, int store_id, String tableCode) throws Exception{
		JSONObject result = new JSONObject();
		result.put(SUCCESS_KEY, false);
		
		if(false == this.isWlifeOrderH5(tenantId, store_id))
		{
			result.put(MSG_KEY, "未启用微生活H5点餐");
			return result;
		}
		
		// 先取到订单信息，需要使用order_num（微生活端订单号）
		JSONObject pos_bill = wshPosEntranceDao.selectUnclosedBill(tenantId, store_id, tableCode);
		if(pos_bill == null){
			result.put(MSG_KEY, "未找到未关台订单");
			return result;
		}
		logger.info("找到未关台订单(pos_bill):" + pos_bill.toString());
		String bill_num = pos_bill.optString("bill_num");
		// 找到锁单信息，判断是否为微生活锁单
		JSONObject lockInfo = wshPosEntranceDao.findLockInfoByBillNum(tenantId, store_id, bill_num);
		if(lockInfo == null){
			result.put(MSG_KEY, "未找到锁单信息");
			return result;
		}
//		String lock_type = lockInfo.optString("lock_type");
		// 若不是微生活端锁的单则不做通知
//		if(!SysDictionary.CHANEL_WX02.equals(lock_type)){
//			result.put(MSG_KEY, "非微生活锁单");
//			return result;
//		}
		// 通知微生活解锁订单
		String reqURL = PosPropertyUtil.getMsg(ACEWILL_URL_KEY, new String[0]);
		reqURL = reqURL + UNLOCK_ORDER_URI;
		//shop_id	门店ID
		//out_order_id	外部订单号
		//error		0:门店主动解锁 1:异常解锁 (此处固定为0)
		String order_num = pos_bill.optString("order_num");
		if(StringUtils.isBlank(order_num) || StringUtils.equals("null", order_num)){
			result.put(MSG_KEY, "未获取微生活订单号");
			return result;
		}
		Map<String, String> reqParam = new HashMap<String, String>();
		reqParam.put("shop_id", String.valueOf(store_id));
		reqParam.put("out_order_id", String.valueOf(order_num));
		reqParam.put("error", "0");
		String postResult = HttpUtil.sendPostRequest(reqURL, reqParam);
		logger.info("微生活解锁接口返回值：" + postResult);
		if(Tools.hv(postResult))
		{
			JSONObject postResJson = JSONObject.fromObject(postResult);
			if("1".equals(postResJson.optString("ok"))){
				result.put(SUCCESS_KEY, true);
				result.put(MSG_KEY, "微生活解锁接口调用成功");
			}else{
				result.put(MSG_KEY, postResJson.optString("data"));
			}
		}
		else
		{
			result.put(SUCCESS_KEY, false);
			result.put(MSG_KEY, "微生活解锁接口调用超时");
		}
		return result;
	}
	
	@Override
	public JSONObject preCancelBill(String tenantId, int store_id,
			String billno) throws Exception {
		// 查询看是否订单为微生活订单
		JSONObject bill = wshPosEntranceDao.selectBillByNo(tenantId, store_id, billno);
		// 订单不存在直接返回
		if(bill == null){
			return null;
		}
		// 根据order_num是否有值和渠道判断是否微生活订单
		String order_num = bill.optString("order_num");
		String source = bill.optString("source");
		if((StringUtils.isNotBlank(order_num) && StringUtils.equals("null", order_num))
				&& StringUtils.equals(SysDictionary.CHANEL_WX02, source)){
			JSONObject json = new JSONObject();
			json.put("store_id", store_id);
			json.put("order_num", order_num);
			return json;
		}
		return null;
	}

	@Override
	public JSONObject cancelBill(String tenancyID, int store_id, String order_num)
			throws Exception {
		if(!this.isWlifeOrderH5(tenancyID, store_id))
		{
			return null;
		}

		String reqURL = PosPropertyUtil.getMsg(ACEWILL_URL_KEY, new String[0]);
		reqURL = reqURL + ORDER_QUIT_URI;
		Map<String, String> reqParam = new HashMap<String, String>();
		reqParam.put("shop_id", String.valueOf(store_id));
		reqParam.put("out_order_id", order_num);
		logger.info("请求退单的参数为(cancelBill):" + reqParam);
		String sendPostRequest = HttpUtil.sendPostRequest(reqURL, reqParam);
		logger.info("请求退单的返回结果为:" + sendPostRequest);
		JSONObject resJson = JSONObject.fromObject(sendPostRequest);
		JSONObject result = new JSONObject();
		if("1".equals(resJson.optString("ok"))){
			result.put(SUCCESS_KEY, true);
		}else{
			result.put(SUCCESS_KEY, false);
		}
		result.put(MSG_KEY, resJson.optString("data"));
		return result;
	}
	
	@Override
	public boolean isWlifeOrderH5(String tenancyID, int storeId)
	{
		try
		{
			List<JSONObject> values = wshPosEntranceDao.selectStoreParams(tenancyID, storeId, new String[]
			{ WLifeConstant.IS_USER_WLIFE, WLifeConstant.USER_WLIFE_ORDER_TYPE_KEY });

			String isUserWlife = null;
			String userWlifeOrderType = null;
			for (JSONObject json : values)
			{

				if (WLifeConstant.IS_USER_WLIFE.equals(json.optString("para_code")))
				{
					isUserWlife = json.optString("para_value", json.optString("para_defaut"));
				}

				else if (WLifeConstant.USER_WLIFE_ORDER_TYPE_KEY.equals(json.optString("para_code")))
				{
					userWlifeOrderType = json.optString("para_value", json.optString("para_defaut"));
				}

			}
			return (WLifeConstant.USER_WLIFE_ORDER_TYPE_H5.equals(userWlifeOrderType) || ("0".equals(userWlifeOrderType) && "1".equals(isUserWlife)));
		}
		catch (Exception e)
		{
			logger.info("微生活点餐平台类型校验失败:",e);
		}
		return false;
	}
}
