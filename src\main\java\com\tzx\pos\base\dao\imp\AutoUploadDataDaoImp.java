package com.tzx.pos.base.dao.imp;

import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

import net.sf.json.JSONObject;

import org.apache.log4j.Logger;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Repository;

import com.tzx.pos.base.util.DateUtil;
import com.tzx.framework.common.util.Tools;
import com.tzx.framework.common.util.dao.datasource.MultiDataSourceManager;
import com.tzx.framework.common.util.dao.impl.GenericDaoImpl;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.dao.AutoUploadDataDao;

@Repository(AutoUploadDataDao.NAME)
public class AutoUploadDataDaoImp extends GenericDaoImpl implements AutoUploadDataDao
{
	private Logger	logger	= Logger.getLogger(this.getClass());

	@Override
	public String getSysParameter(String tenancyId, int storeId, String para) throws Exception
	{
		String sql = "select trim(para_value) as para_value from sys_parameter where para_code = ?";

		SqlRowSet rs = jdbcTemplate.queryForRowSet(sql, new Object[]
		{ para });

		if (rs.next())
		{
			return rs.getString("para_value");
		}
		return "";
	}

	@Override
	public String getPosParameter(String tenancyId, int storeId, String para) throws Exception
	{
		String sql = "select trim(para_value) as para_value from pos_data_version where para_code = ? and tenancy_id = ? and store_id = ?";

		SqlRowSet rs = jdbcTemplate.queryForRowSet(sql, new Object[]
		{ para, tenancyId, storeId });

		if (rs.next())
		{
			return rs.getString("para_value");
		}
		return "";
	}

	@Override
	public List<JSONObject> getPosUploadDataItem(String tenancyId, int storeId, String tableName, JSONObject param) throws Exception
	{
		if (null == tableName || "".equals(tableName))
		{
			logger.warn("表名不能为空!");
			return null;
		}

		Connection conn = this.jdbcTemplate.getDataSource().getConnection();
		DatabaseMetaData dbmd = conn.getMetaData();
		ResultSet rs = null;
		// rs = dbmd.getPrimaryKeys(null, null, tableName);
		// String idColumn = null;
		// if (rs.next())
		// {
		// idColumn = rs.getString("COLUMN_NAME");
		// }
		//
		// MultiDataSourceManager.close(null, null, rs);

		rs = dbmd.getColumns(null, null, tableName, null);

		StringBuilder column = new StringBuilder();
		while (rs.next())
		{
			String colname = rs.getString("COLUMN_NAME");
			if ("id".equalsIgnoreCase(colname))
			{
				column.append(",id as uid");
			}
			else if ("upload_tag".equalsIgnoreCase(colname))
			{
				column.append(",'1' as upload_tag");
			}
			else
			{
				column.append(",").append(colname);
			}
		}
		MultiDataSourceManager.close(conn, null, rs);

		if (column.length() == 0)
		{
			return null;
		}

		StringBuffer sqlSb = new StringBuffer("select ");

		sqlSb.append(column.substring(1)).append(" from ").append(tableName);
		sqlSb.append(" where tenancy_id='").append(tenancyId).append("' and store_id='").append(storeId).append("'");

		if (param != null && param.containsKey("upload_tag"))
		{
			sqlSb.append(" and upload_tag='").append(param.opt("upload_tag")).append("' ");
		}
		else if (param != null && param.containsKey("report_date"))
		{
			sqlSb.append(" and to_char(report_date,'yyyy-MM-dd')='").append(param.opt("report_date")).append("' ");
		}
		return query4Json(tenancyId, sqlSb.toString());
	}

	public List<JSONObject> getPosAutoUploadDataItem(String tenancyId, int storeId, String tableName, JSONObject param,String isUploadOpenBill) throws Exception
	{
		if (null == tableName || "".equals(tableName))
		{
			logger.warn("表名不能为空!");
			return null;
		}

		Connection conn = this.jdbcTemplate.getDataSource().getConnection();
		DatabaseMetaData dbmd = conn.getMetaData();
		ResultSet rs = null;
		rs = dbmd.getColumns(null, null, tableName, null);

		StringBuilder column = new StringBuilder();
		while (rs.next())
		{
			String colname = rs.getString("COLUMN_NAME");
			if ("id".equalsIgnoreCase(colname))
			{
				column.append(",a.id as uid");
			}
			else if ("upload_tag".equalsIgnoreCase(colname))
			{
				column.append(",'1' as upload_tag");
			}
			else
			{
				column.append(",a.").append(colname);
			}
		}
		MultiDataSourceManager.close(conn, null, rs);

		if (column.length() == 0)
		{
			return null;
		}

		StringBuffer sqlSb = new StringBuffer("select ");
		sqlSb.append(column.substring(1)).append(" from ").append(tableName).append(" as a");
		
/*
		sqlSb.append(" where a.tenancy_id='").append(tenancyId).append("' and a.store_id='").append(storeId).append("'").append(" and a.upload_tag='0'");

		if (param != null && param.containsKey("report_date"))
		{
			sqlSb.append(" and to_char(a.report_date,'yyyy-MM-dd')='").append(param.opt("report_date")).append("' ");
		}

		if ("0".equals(isUploadOpenBill))
		{
			switch (tableName.toLowerCase())
			{
				case "pos_bill":
					sqlSb.append(" and a.bill_property='CLOSED'");
					break;
				case "pos_bill_item":
				case "pos_bill_payment":
					sqlSb.append(" and a.bill_num in(select bill_num from pos_bill where tenancy_id='").append(tenancyId).append("' and store_id=").append(storeId).append(" and bill_property='CLOSED' and upload_tag='0')");
					break;
				case "pos_zfkw_item":
				case "pos_bill_invoice":
				case "pos_bill_waiter":
				case "pos_returngive_item":
				case "pos_bill_member":
				case "pos_bill_service":
				case "pos_bill_payment_coupons":
				case "hq_bill_evaluate":
					sqlSb.append(" and a.bill_num in(select bill_num from pos_bill where tenancy_id='").append(tenancyId).append("' and store_id=").append(storeId).append(" and bill_property='CLOSED' and upload_tag='0' union all select bill_num from pos_bill2 where tenancy_id='").append(tenancyId)
							.append("' and store_id=").append(storeId).append(" and bill_property='CLOSED' and upload_tag='0')");
					break;
				default:
					break;
			}
		}
*/
		
		switch (tableName.toLowerCase())
		{
			case "pos_bill":
				sqlSb.append(" where a.tenancy_id='").append(tenancyId).append("' and a.store_id='").append(storeId).append("'").append(" and a.upload_tag='0'");

				if (param != null && param.containsKey("report_date"))
				{
					sqlSb.append(" and to_char(a.report_date,'yyyy-MM-dd')='").append(param.opt("report_date")).append("' ");
				}
				if ("0".equals(isUploadOpenBill))
				{
					sqlSb.append(" and a.bill_property='").append(SysDictionary.BILL_PROPERTY_CLOSED).append("'");
				}
				break;
			case "pos_bill_item":
			case "pos_bill_payment":
				sqlSb.append(" left join pos_bill b on a.tenancy_id=b.tenancy_id and a.store_id=b.store_id and a.bill_num=b.bill_num");
				sqlSb.append(" where b.tenancy_id='").append(tenancyId).append("' and b.store_id='").append(storeId).append("'").append(" and b.upload_tag='0'");

				if (param != null && param.containsKey("report_date"))
				{
					sqlSb.append(" and to_char(b.report_date,'yyyy-MM-dd')='").append(param.opt("report_date")).append("' ");
				}
				if ("0".equals(isUploadOpenBill))
				{
					sqlSb.append(" and b.bill_property='").append(SysDictionary.BILL_PROPERTY_CLOSED).append("'");
				}
				break;
			case "pos_zfkw_item":
			case "pos_bill_invoice":
			case "pos_bill_waiter":
			case "pos_returngive_item":
			case "pos_bill_member":
			case "pos_table_log":
				sqlSb.append(" where a.tenancy_id='").append(tenancyId).append("' and a.store_id='").append(storeId).append("'").append(" and a.upload_tag='0'");

				if (param != null && param.containsKey("report_date"))
				{
					sqlSb.append(" and to_char(a.report_date,'yyyy-MM-dd')='").append(param.opt("report_date")).append("' ");
				}
				break;
			case "pos_kvs_bill_item":
			case "pos_kvs_bill":
			case "pos_bill_service":
			case "pos_bill_payment_coupons":
			case "hq_bill_evaluate":
            case "pos_item_discount_list":
				sqlSb.append(" left join (select tenancy_id,store_id,report_date,bill_num,bill_property,upload_tag from pos_bill union all select tenancy_id,store_id,report_date,bill_num,bill_property,upload_tag from pos_bill2) b");
				sqlSb.append(" on a.tenancy_id=b.tenancy_id and a.store_id=b.store_id and a.bill_num=b.bill_num");
				sqlSb.append(" where b.tenancy_id='").append(tenancyId).append("' and b.store_id='").append(storeId).append("'").append(" and b.upload_tag='0'");

				if (param != null && param.containsKey("report_date"))
				{
					sqlSb.append(" and to_char(b.report_date,'yyyy-MM-dd')='").append(param.opt("report_date")).append("' ");
				}
				if ("0".equals(isUploadOpenBill))
				{
					sqlSb.append(" and b.bill_property='").append(SysDictionary.BILL_PROPERTY_CLOSED).append("'");
				}
				break;
			default:
				sqlSb.append(" where a.tenancy_id='").append(tenancyId).append("' and a.store_id='").append(storeId).append("'").append(" and a.upload_tag='0'");

				if (param != null && param.containsKey("report_date"))
				{
					sqlSb.append(" and to_char(a.report_date,'yyyy-MM-dd')='").append(param.opt("report_date")).append("' ");
				}
				break;
		}
		
		return query4Json(tenancyId, sqlSb.toString());
	}

	@Override
	public int[] updatePosItemUploadTag(final String tenancyId, final int storeId, String tableName, final List<JSONObject> param, String cond) throws Exception
	{
		if (null == tableName || "".equals(tableName) || null == param || param.size() <= 0)
		{
			logger.warn("参数为空!");
			return null;
		}
		StringBuffer sql = new StringBuffer();
		sql.append("update ").append(tableName).append(" set upload_tag='1' where id=? and tenancy_id=? and store_id=?");

		if (null != cond && !"".equals(cond))
		{
			sql.append(" ").append(cond);
		}
		return jdbcTemplate.batchUpdate(sql.toString(), new BatchPreparedStatementSetter()
		{
			@Override
			public void setValues(PreparedStatement arg0, int arg1) throws SQLException
			{
				JSONObject json = param.get(arg1);
				arg0.setInt(1, json.optInt("uid"));
				arg0.setString(2, tenancyId);
				arg0.setInt(3, storeId);
			}

			@Override
			public int getBatchSize()
			{
				return param.size();
			}
		});
	}

	@Override
	public boolean updatePosUploadDate(String tenancyId, int storeId) throws Exception
	{
		String paraValue = DateUtil.format(new Timestamp(System.currentTimeMillis()));

		String sql = "select id,para_remark from pos_data_version where para_code='AUTOUPLOADDATETIME' and tenancy_id=? and store_id=?";

		SqlRowSet rs = jdbcTemplate.queryForRowSet(sql, new Object[]
		{ tenancyId, storeId });

		if (rs.next())
		{
			String updateSql = "update pos_data_version set para_value=?,para_remark=? where para_code='AUTOUPLOADDATETIME' and tenancy_id=? and store_id=?";
			int ret = jdbcTemplate.update(updateSql, new Object[]
			{ paraValue,rs.getInt("para_remark")+1, tenancyId, storeId });

			return ret > 0;
		}
		else
		{
			String insertSql = "insert into pos_data_version(tenancy_id,store_id,system_name,model_name,para_name,para_code,para_value,para_type,valid_state,para_remark) values(?,?,'POS','系统参数','自动上传时间','AUTOUPLOADDATETIME',?,'常规','1',1)";

			int ret = jdbcTemplate.update(insertSql, new Object[]
			{ tenancyId, storeId, paraValue });

			return ret > 0;
		}
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<JSONObject> getPosOptState(String tenancyId, int storeId, JSONObject param) throws Exception
	{
		// TODO Auto-generated method stub
		StringBuffer sb = new StringBuffer("select tenancy_id,store_id,id,pos_num,content,opt_num,report_date,remark from pos_opt_state where 1=1");

		Iterator<String> it = param.keys();
		while (it.hasNext())
		{
			String key = it.next();
			sb.append(" and ").append(key).append(" = '").append(param.getString(key)).append("'");
		}
		return this.query4Json(tenancyId, sb.toString());
	}

	@Override
	public Date getCurrentReportDate(String tenancyId, int storeId) throws Exception
	{
		// TODO Auto-generated method stub
		StringBuffer sb = new StringBuffer("select max(st.report_date) as report_date from pos_opt_state st where st.tenancy_id=? and st.store_id=? and st.content=?");
		List<JSONObject> re = this.query4Json(tenancyId, sb.toString(), new Object[]
		{ tenancyId, storeId, SysDictionary.OPT_STATE_DAYBEGAIN });
		String reportDate = null;
		if (null != re && re.size() > 0)
		{
			reportDate = re.get(0).optString("report_date");
		}
		if(Tools.hv(reportDate))
		{
			return DateUtil.parseDate(reportDate);
		}
		return null;
	}

	@Override
	public String getPrintNewData(String tenancyId, int storeId) throws Exception {
		StringBuffer sb = new StringBuffer("select hpn.statedesc from hq_printer_new hpn where ");
		sb.append(" hpn.tenancy_id='").append(tenancyId).append("' and hpn.store_id= ").append(storeId).append(" and hpn.printstate=1 LIMIT 4");
		List<JSONObject> printList= this.query4Json(tenancyId, sb.toString());
		StringBuffer printDesc=new StringBuffer();
		if(printList!=null && !printList.isEmpty()){
			for(int i=0;i<printList.size();i++){
				JSONObject print = printList.get(i);
				String statedesc = print.optString("statedesc");
				if(statedesc!=null && !"".equals(statedesc)){
					printDesc.append(statedesc).append("\r\n");
				}
			}
		}
		return printDesc.toString();
	}
}
