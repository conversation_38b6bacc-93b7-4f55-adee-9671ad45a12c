package com.tzx.pos.base.controller;

import java.util.List;

import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.member.crm.bo.CustomerService;
import com.tzx.member.crm.bo.imp.CustomerServiceImp;

/**
 * 沽清菜品上传到总部
 */
@Component
public class SellDishesDataUploadRunnable implements  Runnable{

    private static final Logger logger = Logger.getLogger(SellDishesDataUploadRunnable.class);

    private Data subParam;
    private List<JSONObject> subSoldList;
   
    public SellDishesDataUploadRunnable(){}

    public SellDishesDataUploadRunnable(Data subParam, List<JSONObject> subSoldList) {
    	this.subParam = subParam;
    	this.subSoldList = subSoldList;
    }

    @Override
    public void run() {
        try {
        	String tenantId = subParam.getTenancy_id();
			if (StringUtils.isEmpty(tenantId))
			{
				logger.info("参数为空");
				return;
			}
			
            DBContextHolder.setTenancyid(tenantId);
            CustomerServiceImp customerServiceImp = (CustomerServiceImp)SpringConext.getApplicationContext().getBean(CustomerService.NAME);
            customerServiceImp.dishSoldOut(subParam, subSoldList);
            
        }catch (Exception e){
        	logger.info("上传到总部沽清菜品失败......");
            e.printStackTrace();
        }
    }

	public Data getSubParam() {
		return subParam;
	}

	public void setSubParam(Data subParam) {
		this.subParam = subParam;
	}

	public List<JSONObject> getSubSoldList() {
		return subSoldList;
	}

	public void setSubSoldList(List<JSONObject> subSoldList) {
		this.subSoldList = subSoldList;
	}

}
