package com.tzx.orders.bo.dto;

import java.io.Serializable;
import java.util.List;

public class PosBillItem implements Serializable
{
	/**
	 * 
	 */
	private static final long	serialVersionUID	= -7079566940535393897L;

	private String				item_serial;
	private String				details_id;
	private String				item_id;
	private String				item_num;
	private String				item_name;
	private String				item_unit_name;
	private String				assist_num;
	private String				assist_money;
	private String				item_taste;
	private String				seat_num;
	private String				unit_id;
	private String				item_count;
	private String				item_price;
	private String				item_remark;
	private String				sale_mode;
	private String				setmeal_id;
	private String				setmeal_rwid;
	private String				item_property;
	private String				assist_item_id;
	private String				waitcall_tag;
	private String method_money;
	private List<?>				taste;
	private List<?>				method;
	private Integer 			activity_id;
	private Integer 			activity_rule_id;
	private Integer 			activity_count;
	private Double 				vip_price;
	private String            cart_name;//口袋

	public String getItem_serial()
	{
		return item_serial;
	}

	public void setItem_serial(String item_serial)
	{
		this.item_serial = item_serial;
	}

	public String getDetails_id()
	{
		return details_id;
	}

	public void setDetails_id(String details_id)
	{
		this.details_id = details_id;
	}

	public String getItem_id()
	{
		return item_id;
	}

	public void setItem_id(String item_id)
	{
		this.item_id = item_id;
	}

	public String getItem_num()
	{
		return item_num;
	}

	public void setItem_num(String item_num)
	{
		this.item_num = item_num;
	}

	public String getItem_name()
	{
		return item_name;
	}

	public void setItem_name(String item_name)
	{
		this.item_name = item_name;
	}

	public String getItem_unit_name()
	{
		return item_unit_name;
	}

	public void setItem_unit_name(String item_unit_name)
	{
		this.item_unit_name = item_unit_name;
	}

	public String getAssist_num()
	{
		return assist_num;
	}

	public void setAssist_num(String assist_num)
	{
		this.assist_num = assist_num;
	}

	public String getAssist_money()
	{
		return assist_money;
	}

	public void setAssist_money(String assist_money)
	{
		this.assist_money = assist_money;
	}

	public String getItem_taste()
	{
		return item_taste;
	}

	public void setItem_taste(String item_taste)
	{
		this.item_taste = item_taste;
	}

	public String getSeat_num()
	{
		return seat_num;
	}

	public void setSeat_num(String seat_num)
	{
		this.seat_num = seat_num;
	}

	public String getUnit_id()
	{
		return unit_id;
	}

	public void setUnit_id(String unit_id)
	{
		this.unit_id = unit_id;
	}

	public String getItem_count()
	{
		return item_count;
	}

	public void setItem_count(String item_count)
	{
		this.item_count = item_count;
	}

	public String getItem_price()
	{
		return item_price;
	}

	public void setItem_price(String item_price)
	{
		this.item_price = item_price;
	}

	public String getItem_remark()
	{
		return item_remark;
	}

	public void setItem_remark(String item_remark)
	{
		this.item_remark = item_remark;
	}

	public String getSale_mode()
	{
		return sale_mode;
	}

	public void setSale_mode(String sale_mode)
	{
		this.sale_mode = sale_mode;
	}

	public String getSetmeal_id()
	{
		return setmeal_id;
	}

	public void setSetmeal_id(String setmeal_id)
	{
		this.setmeal_id = setmeal_id;
	}

	public String getSetmeal_rwid()
	{
		return setmeal_rwid;
	}

	public void setSetmeal_rwid(String setmeal_rwid)
	{
		this.setmeal_rwid = setmeal_rwid;
	}

	public String getItem_property()
	{
		return item_property;
	}

	public void setItem_property(String item_property)
	{
		this.item_property = item_property;
	}

	public String getAssist_item_id()
	{
		return assist_item_id;
	}

	public void setAssist_item_id(String assist_item_id)
	{
		this.assist_item_id = assist_item_id;
	}

	public String getWaitcall_tag()
	{
		return waitcall_tag;
	}

	public void setWaitcall_tag(String waitcall_tag)
	{
		this.waitcall_tag = waitcall_tag;
	}

	public List<?> getTaste()
	{
		return taste;
	}

	public void setTaste(List<?> taste)
	{
		this.taste = taste;
	}

	public List<?> getMethod()
	{
		return method;
	}

	public void setMethod(List<?> method)
	{
		this.method = method;
	}

	public String getMethod_money()
	{
		return method_money;
	}

	public void setMethod_money(String method_money)
	{
		this.method_money = method_money;
	}

	public Integer getActivity_id() {
		return activity_id;
	}

	public void setActivity_id(Integer activity_id) {
		this.activity_id = activity_id;
	}

	public Integer getActivity_rule_id() {
		return activity_rule_id;
	}

	public void setActivity_rule_id(Integer activity_rule_id) {
		this.activity_rule_id = activity_rule_id;
	}

	public Integer getActivity_count() {
		return activity_count;
	}

	public Double getVip_price() {
		return vip_price;
	}

	public void setVip_price(Double vip_price) {
		this.vip_price = vip_price;
	}

	public void setActivity_count(Integer activity_count) {
		this.activity_count = activity_count;
	}

	public String getCart_name() {
		return cart_name;
	}

	public void setCart_name(String cart_name) {
		this.cart_name = cart_name;
	}
}
