package com.tzx.clientorder.wlifeprogram.dao;

import net.sf.json.JSONObject;

import java.util.List;

/**
 * Created by qingui on 2018-06-26.
 */
public interface PosGenericDao extends WlifeCommonDao{

    String name = "com.tzx.clientorder.wlifeprogram.dao.impl.PosGeneralDaoImp";

    /**
     * 查询门店的营业状态
     * @param tenancyId
     * @param organId
     * @return
     * @throws Exception
     */
    JSONObject getOrganStatus(String tenancyId, int organId) throws Exception;

    /**
     * 查询账单中套餐的信息
     * @param tenancyId
     * @param billNum
     * @return
     * @throws Exception
     */
    List<JSONObject> getSetmeal(String tenancyId, String billNum) throws Exception;

    /**
     * 查询套餐的主菜或套餐必选菜的菜品
     * @param tenancyId
     * @param billNum
     * @param type 套餐主菜或是套餐必选菜查询
     * @return
     * @throws Exception
     */
    List<JSONObject> getSetmealDish(String tenancyId, String billNum, String type, int stemealCount, String setmealId, String setmeal_rwid) throws Exception;

    /**
     * 查询单品信息
     * @param tenancyId
     * @param billNum
     * @return
     * @throws Exception
     */
    List<JSONObject> getNormalitems(String tenancyId, String billNum) throws Exception;

    /**
     * 查询账单信息
     * @param tenancyId
     * @param tableCode
     * @param storeId
     * @return
     * @throws Exception
     */
    JSONObject getBillInfo(String tenancyId, String tableCode, int storeId) throws Exception;

    /**
     * 查询菜品的做法信息
     * @param tenancyId
     * @param id
     * @return
     * @throws Exception
     */
    List<JSONObject> getCooks(String tenancyId, Integer id) throws Exception;

    /**
     * 根据账单编号查询账单信息
     * @param tenancyId
     * @param billNum
     * @return
     * @throws Exception
     */
    JSONObject getOrderInfo(String tenancyId, String billNum) throws Exception;

    /**
     * 修改订单来源为微生活小程序
     * @param tenancyId
     * @param billNum
     * @throws Exception
     */
    void updateOrderSource(String tenancyId, String billNum, String orderNum) throws Exception;

    /**
     * 根据微生活平台订单号，查询平台订单信息
     * @param tenancyId
     * @param out_order_id
     * @return
     * @throws Exception
     */
    JSONObject getWlifeBill(String tenancyId, String out_order_id) throws Exception;

    /**
     * 获取账单的支付名称
     * @param tenancyId
     * @param billNum
     * @return
     * @throws Exception
     */
    String getBillPayNames(String tenancyId, String billNum)  throws Exception;


    Double getBillItemAmountByDis(String tenancyId,String billNum,String isDiscount) throws Exception;
}
