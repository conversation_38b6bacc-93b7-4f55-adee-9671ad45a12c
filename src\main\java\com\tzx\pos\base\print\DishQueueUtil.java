package com.tzx.pos.base.print;

/**
 *
 * <AUTHOR>
 * 2015年7月27日-下午4:00:21
 */
public class DishQueueUtil
{
	private static final DishQueue dishQueue = new DishQueue();
	
	public static DishQueue getQueue()
	{
		return dishQueue;
	}
	
	public static void push(Object obj)
	{
		dishQueue.push(obj);
	}
	
	public static Object pull()
	{
		return dishQueue.pull();
	}
	
	public static int size()
	{
		return dishQueue.size();
	}
}
