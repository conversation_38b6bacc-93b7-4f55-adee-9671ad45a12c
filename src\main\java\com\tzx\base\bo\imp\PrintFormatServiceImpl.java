package com.tzx.base.bo.imp;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.jsoup.Jsoup;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.jdbc.support.rowset.SqlRowSetMetaData;
import org.springframework.stereotype.Service;

import com.tzx.base.bo.PrintFormatService;
import com.tzx.framework.common.exception.HqErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.print.PrintHelper;
import com.tzx.framework.common.print.template.PrintTemplate_HTML;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.Tools;
import com.tzx.framework.common.util.dao.GenericDao;

@Service(PrintFormatService.NAME)
public class PrintFormatServiceImpl implements PrintFormatService
{
	private Logger logger = Logger.getLogger(PrintFormatServiceImpl.class);
	
	@Resource(name = "genericDaoImpl")
	private GenericDao	dao;

	@Override
	public JSONObject queryPrintFormat(String tenantId, JSONObject condition) throws Exception
	{
		if (condition.containsKey("id"))
		{
			JSONObject result = new JSONObject();

			String sql = "select trim(f.param_pairs) as param_pairs,f.content from sys_print_format f where f.id=" + condition.optInt("id");
			SqlRowSet rs = this.dao.query(tenantId, sql);
			if (rs.next())
			{
				result.put("param_pairs", rs.getString("param_pairs"));
				result.put("content", rs.getString("content"));
			}
			else
			{
				result.put("content", "");
			}

			sql = "select result_set_title,sql,is_test from sys_print_format_detail where parent_id=" + condition.optInt("id");
			List<JSONObject> list = this.dao.query4Json(tenantId, sql);
			result.put("detail", list);

			return result;
		}
		else
		{
			int pagenum = condition.containsKey("page") ? (condition.getInt("page") == 0 ? 1 : condition.getInt("page")) : 1;

			StringBuilder sb = new StringBuilder();
			//sb.append("select f.id,f.class_item_code,d.class_item,f.format_name,f.page_size,f.valid_state,f.last_updatetime,f.last_operator from sys_print_format f join sys_dictionary d on f.class_item_code=d.class_item_code");
			sb.append("  select    ");
			sb.append("     xx. ID,xx.class_item_code,xx.class_item,xx.format_name,xx.page_size,xx.valid_state,    ");
			sb.append("     xx.last_updatetime,xx.last_operator,xx.store_id       ");
			sb.append("  from  (    ");
			sb.append("  SELECT    ");
			sb.append("  	f. ID,f.class_item_code,d.class_item,f.format_name,f.page_size,f.valid_state,    ");
			sb.append("     f.last_updatetime,f.last_operator,f.store_id    ");
			sb.append("  FROM    ");
			sb.append("  	sys_print_format f    ");
			sb.append("  JOIN sys_dictionary d ON f.class_item_code = d.class_item_code    ");
			sb.append("  where store_id = '0'    ");
			sb.append("  AND NOT EXISTS (select 1 from sys_print_format ss where ss.store_id = '"+condition.optInt("store_id")+"' and ss. class_item_code = f.class_item_code )    ");
			sb.append("  UNION    ");
			sb.append("  SELECT    ");
			sb.append("  	ff. ID,ff.class_item_code,dd.class_item,ff.format_name,ff.page_size,ff.valid_state,    ");
			sb.append("  	ff.last_updatetime,ff.last_operator,ff.store_id    ");
			sb.append("  FROM    ");
			sb.append("  	sys_print_format ff    ");
			sb.append("  JOIN sys_dictionary dd ON ff.class_item_code = dd.class_item_code    ");
			sb.append("  where store_id = '"+condition.optInt("store_id")+"' ) xx    ");
			
			if (condition.containsKey("class_item_code") && !condition.optString("class_item_code").isEmpty())
			{
				sb.append(" where xx.class_item like '%" + condition.optString("class_item_code") + "%'");
			}
			sb.append("  order by xx.class_item_code ,xx.last_updatetime desc     ");

			long total = this.dao.countSql(tenantId, sb.toString());
			List<JSONObject> list = this.dao.query4Json(tenantId, dao.buildPageSql(condition, sb.toString()));

			JSONObject result = new JSONObject();

			result.put("page", pagenum);
			result.put("total", total);
			result.put("rows", list);

			return result;
		}
	}

	@Override
	public Object savePrintFormat(String tenantId, JSONObject condition) throws Exception, SystemException
	{
		if (!condition.optString("id").isEmpty())
		{
			Integer id = condition.getInt("id");
			
			int preStore_id = condition.optInt("preStore_id");
			int store_id = condition.optInt("store_id");
			condition.remove("preStore_id");
			String param_pairs = condition.optJSONObject("param_pairs").toString();
			condition.element("param_pairs", " " + param_pairs);
			
			if(preStore_id == store_id){
				this.dao.updateIgnorCase(tenantId, "sys_print_format", condition);
			}else{
				List<JSONObject> list11 = this.dao.query4Json(tenantId, "select id from sys_print_format where class_item_code = '" + condition.optString("class_item_code") + "' and store_id = '"+condition.optInt("store_id")+"'  and valid_state='1'");

				if (list11 != null && list11.size() > 0){
					throw new SystemException(HqErrorCode.ONLY_ONE_FORMAT_ERROR);
				}
				
				id = (Integer) this.dao.insertIgnorCase(tenantId, "sys_print_format", condition);
			}

			JSONArray detail = condition.optJSONArray("detail");

			if (detail != null)
			{
				List<JSONObject> list = new ArrayList<JSONObject>();

				JSONObject j = new JSONObject();
				j.element("parent_id", id);
				list.add(j);

				this.dao.deleteBatchIgnorCase(tenantId, "sys_print_format_detail", list);

				list.clear();

				for (Object o : detail)
				{
					JSONObject json = JSONObject.fromObject(o);
					json.element("parent_id", id);

					list.add(json);
				}

				this.dao.insertBatchIgnorCase(tenantId, "sys_print_format_detail", list);
			}

			return id;
		}
		else
		{
			
			
			condition.remove("id");
			
			List<JSONObject> list11 = this.dao.query4Json(tenantId, "select id from sys_print_format where class_item_code = '" + condition.optString("class_item_code") + "' and store_id = '"+condition.optInt("store_id")+"'  and valid_state='1'");

			if (list11 != null && list11.size() > 0)
			{
				throw new SystemException(HqErrorCode.ONLY_ONE_FORMAT_ERROR);
			}

			String param_pairs = condition.optJSONObject("param_pairs").toString();
			condition.element("param_pairs", " " + param_pairs);

			Object id = this.dao.insertIgnorCase(tenantId, "sys_print_format", condition);

			JSONArray detail = condition.optJSONArray("detail");

			if (detail != null)
			{
				List<JSONObject> list = new ArrayList<JSONObject>();

				for (Object o : detail)
				{
					JSONObject j = JSONObject.fromObject(o);
					j.element("parent_id", id);

					list.add(j);
				}

				this.dao.insertBatchIgnorCase(tenantId, "sys_print_format_detail", list);
			}

			return id;
		}

	}

	@Override
	public List<JSONObject> findMainDataColumn(String tenancyID, JSONObject condition) throws Exception
	{
		return null;
	}

	@Override
	public List<JSONObject> findDetailDataColumn(String tenancyID, JSONObject condition) throws Exception
	{
		return null;
	}

	@Override
	public List<JSONObject> testTempSql(String tenancyId, JSONObject condition) throws Exception
	{
		String testSql = condition.optString("sql").trim();
		JSONObject param_pairs = condition.optJSONObject("param_pairs");
		JSONArray rows = param_pairs.optJSONArray("rows");

		if (testSql.substring(0, 6).equalsIgnoreCase("select"))
		{
			StringBuilder sb = new StringBuilder(testSql);

			int i = 0;
			String temp = "";
			List<String> paraName = new ArrayList<String>();
			while (true)
			{
				i = sb.indexOf(":",i);
				int j = 0;

				if (i > 0)
				{
					temp = sb.substring(i, sb.length());

					j = temp.indexOf(" ");

					if (j < 0)
					{
						j = temp.indexOf(")");
					}

					if (j < 0)
					{
						j = temp.length();
					}

					paraName.add(temp.substring(1, j));
					i++;
					
//					String t = temp.substring(1, j);
//					
//					String value = "";
//					for (Object row : rows)
//					{
//						JSONObject r = JSONObject.fromObject(row);
//
//						if (t.equals(r.optString("paramName")))
//						{
//							value = r.optString("value");
//							break;
//						}
//					}
//
//					sb.replace(i, i + j, value);
				}
				else
				{
					break;
				}
			}
			
			for(String para :paraName){
				String value = "";
				for (Object row : rows)
				{
					JSONObject r = JSONObject.fromObject(row);

					if (para.equals(r.optString("paramName")))
					{
						value = r.optString("value");
						break;
					}
				}

				sb.replace(sb.indexOf(":"+para), sb.indexOf(":"+para)+para.length()+1, value);
			}

			List<JSONObject> result = this.dao.query4Json(tenancyId, "select * from (" + sb.toString() + ") datasql limit 10 offset 0");
			
			
			if(result.isEmpty())
			{
				SqlRowSet sqlrowset = this.dao.query(tenancyId, "select * from (" + sb.toString() + ") datasql limit 10 offset 0");

				SqlRowSetMetaData metaData = sqlrowset.getMetaData();
					
				JSONObject j = new JSONObject();
				
				for(int c = 0; c < metaData.getColumnCount(); c++)
				{
					j.element(metaData.getColumnName(c + 1), "");
				}
				
				result.add(j);
			}
			
			return result;
		}

		return null;
	}

	@Override
	public void printBill(String tenancyID, JSONObject condition) throws Exception
	{
		logger.info("打印账单，打印参数为：" + condition.toString());
		/**
		 * 根据printer_id查询class_item_code即是打印模板 和打印机名称
		 */
		Integer printer_id = condition.optInt("printer_id");
		if (printer_id == 0)
		{
			throw new Exception("未找到指定的打印机：");
		}
		String classItemCode = condition.optString("print_code");
		String printName = "";

		String qPname = new String("select name from hq_print_printer where id = ?");
		SqlRowSet rs = this.dao.getJdbcTemplate(tenancyID).queryForRowSet(qPname, new Object[]
		{ printer_id });
		while (rs.next())
		{
			printName = rs.getString("name");
		}

		String sql = "select id,page_size,trim(param_pairs) as param_pairs,content from sys_print_format where class_item_code='" + classItemCode + "' and valid_state='1'";

		List<JSONObject> list = this.dao.query4Json(tenancyID, sql);

		if (list.size() > 0)
		{
			int id = list.get(0).optInt("id");
			int pageSize = list.get(0).optInt("page_size");
			JSONObject paramPairs = JSONObject.fromObject(list.get(0).get("param_pairs"));
			JSONArray rows = paramPairs.optJSONArray("rows");
			String content = list.get(0).optString("content");

			sql = "select result_set_title,sql from sys_print_format_detail where parent_id=" + id;
			list = this.dao.query4Json(tenancyID, sql);

			List<JSONObject> param = new ArrayList<JSONObject>();

			for (JSONObject json : list)
			{
				JSONObject p = new JSONObject();
				p.put("result_set_title", json.optString("result_set_title"));

				String testSql = json.optString("sql").trim();

				List<Object> values = new ArrayList<Object>();// 参数列表

				if (testSql.substring(0, 6).equalsIgnoreCase("select"))
				{
					StringBuilder sb = new StringBuilder(testSql);

					int i = 0;
					String temp = "";

					while (true)
					{
						i = sb.indexOf(":");
						int j = 0;

						if (i > 0)
						{
							temp = sb.substring(i, sb.length()).replace(")", "");

							j = temp.indexOf(" ");

							if (j < 0)
							{
								j = temp.length();
							}

							String t = temp.substring(1, j);

							Object value = null;
							for (Object row : rows)
							{
								JSONObject r = JSONObject.fromObject(row);

								if (t.equals(r.optString("paramName")))
								{

									if ("test".equals(condition.optString("printmode")))
									{
										value = r.optString("value");
									}
									else
									{
										value = condition.get(t); // 返回object类型
									}

									if (Tools.isNullOrEmpty(value) == false && DateUtil.isDate(value.toString()))
									{
										Date vDate = null;
										if (value.toString().length() > 10)
										{
											vDate = DateUtil.parseDateAll(value.toString());
										}else
										{
											vDate = DateUtil.parseDate(value.toString());
										}
										values.add(vDate);
									}
									else
									{
										values.add(value);
									}
									break;
								}
							}

							sb.replace(i, i + j, "?");
						}
						else
						{
							break;
						}
					}
					Object[] objs = values.toArray();
					p.put("result", this.dao.query4JSONArray(tenancyID, sb.toString(), objs));
				}

				param.add(p);
			}
			
			PrintTemplate_HTML html = new PrintTemplate_HTML();

			String doc = html.parse(pageSize, content, param);
			logger.debug("打印单子的打印机名称：" + printName);
			logger.debug("打印账单内容：" + doc);
			PrintHelper printHelper = PrintHelper.newInstance(pageSize, printName); // "Tzx Printer"测试用的

			printHelper.print(html, Jsoup.parse(doc));
		}
	}

	/**
	 * condition {'tenancyid':'hdl','printer_id':'1001'}
	 * class_item_code这个是模板的class_code
	 */
	@Override
	public void print(String tenantId, JSONObject condition) throws Exception
	{
		logger.info(tenantId + "进入打印print方法，打印内容为：" + condition.toString());
		// 预览模式
		if ("test".equals(condition.optString("printmode")))
		{

			int pageSize = condition.optInt("page_size");
			JSONArray rows = condition.optJSONObject("param_pairs").optJSONArray("rows");
			String content = condition.optString("content");

			List<JSONObject> param = new ArrayList<JSONObject>();

			JSONArray list = condition.optJSONArray("detail");

			for (Object o : list)
			{
				JSONObject json = JSONObject.fromObject(o);

				JSONObject p = new JSONObject();
				p.put("result_set_title", json.optString("result_set_title"));

				String testSql = json.optString("sql").trim();

				if (testSql.substring(0, 6).equalsIgnoreCase("select"))
				{
					StringBuilder sb = new StringBuilder(testSql);

					int i = 0;
					String temp = "";
					List<String> paraName = new ArrayList<String>();
					while (true)
					{
						i = sb.indexOf(":",i);
						int j = 0;

						if (i > 0)
						{
							temp = sb.substring(i, sb.length());

							j = temp.indexOf(" ");

							if (j < 0)
							{
								j = temp.indexOf(")");
							}

							if (j < 0)
							{
								j = temp.length();
							}

							paraName.add(temp.substring(1, j));
							i++;
						}
						else
						{
							break;
						}
					}
					
					for(String para :paraName){
						String value = "";
						for (Object row : rows)
						{
							JSONObject r = JSONObject.fromObject(row);

							if (para.equals(r.optString("paramName")))
							{
								value = r.optString("value");
								break;
							}
						}

						sb.replace(sb.indexOf(":"+para), sb.indexOf(":"+para)+para.length()+1, value);
					}

					p.put("result", this.dao.query4JSONArray(tenantId, sb.toString()));
				}

				param.add(p);
			}

			PrintTemplate_HTML html = new PrintTemplate_HTML();

			String doc = html.parse(pageSize, content, param);

			PrintHelper printHelper = PrintHelper.newInstance(pageSize, "Tzx Printer");

			printHelper.print(html, Jsoup.parse(doc));

		}
		else
		{
			/**
			 * 根据printer_id查询class_item_code即是打印模板 和打印机名称
			 */
			Integer printer_id = condition.optInt("printer_id");
			String classItemCode = "";
			String printName = "";

			String qPname = new String("select name from hq_print_printer where id = ?");
			SqlRowSet rs = this.dao.getJdbcTemplate(tenantId).queryForRowSet(qPname, new Object[]
			{ printer_id });
			while (rs.next())
			{
				printName = rs.getString("name");
			}
			
			// 一个打印机对应多个模板id就会打印多个
			String qPitemCode = new String("select class_item_code from hq_print_printer_format where print_printer_id = ?");
			rs = this.dao.getJdbcTemplate(tenantId).queryForRowSet(qPitemCode, new Object[]
			{ printer_id });
			while (rs.next())
			{
				// 这个是字段是厨打类型
				classItemCode = rs.getString("class_item_code");
				if (StringUtils.isNotEmpty(classItemCode))
				{
					String sql = "select id,page_size,trim(param_pairs) as param_pairs,content from sys_print_format where class_item_code='" + classItemCode + "' and valid_state='1'";

					List<JSONObject> list = this.dao.query4Json(tenantId, sql);

					if (list.size() > 0)
					{
						int id = list.get(0).optInt("id");
						int pageSize = list.get(0).optInt("page_size");
						JSONObject paramPairs = JSONObject.fromObject(list.get(0).get("param_pairs"));
						JSONArray rows = paramPairs.optJSONArray("rows");
						String content = list.get(0).optString("content");

						sql = "select result_set_title,sql from sys_print_format_detail where parent_id=" + id;
						list = this.dao.query4Json(tenantId, sql);

						List<JSONObject> param = new ArrayList<JSONObject>();

						for (JSONObject json : list)
						{
							JSONObject p = new JSONObject();
							p.put("result_set_title", json.optString("result_set_title"));

							String testSql = json.optString("sql").trim();

							List<Object> values = new ArrayList<Object>();// 参数列表

							if (testSql.substring(0, 6).equalsIgnoreCase("select"))
							{
								StringBuilder sb = new StringBuilder(testSql);

								int i = 0;
								String temp = "";

								while (true)
								{
									i = sb.indexOf(":");
									int j = 0;

									if (i > 0)
									{
										temp = sb.substring(i, sb.length());

										j = temp.indexOf(" ");

										if (j < 0)
										{
											j = temp.length();
										}

										String t = temp.substring(1, j);

										Object value = null;
										for (Object row : rows)
										{
											JSONObject r = JSONObject.fromObject(row);

											if (t.equals(r.optString("paramName")))
											{

												if ("test".equals(condition.optString("printmode")))
												{
													value = r.optString("value");
												}
												else
												{
													value = condition.get(t); // 返回object类型
												}
												// 这块判断日期格式，不加的话，有日期参数就查不出来内容，报类型错误
												if (Tools.isNullOrEmpty(value) == false && DateUtil.isDate(value.toString()))
												{
													Date vDate = DateUtil.parseDate(value.toString());
													values.add(vDate);
												}
												else
												{
													values.add(value);
												}
												break;
											}
										}

										sb.replace(i, i + j, "?");
									}
									else
									{
										break;
									}
								}
								Object[] objs = values.toArray();
								p.put("result", this.dao.query4JSONArray(tenantId, sb.toString(), objs));
							}

							param.add(p);
						}

						PrintTemplate_HTML html = new PrintTemplate_HTML();

						String doc = html.parse(pageSize, content, param);

						PrintHelper printHelper = PrintHelper.newInstance(pageSize, printName); // "Tzx Printer"测试用的

						printHelper.print(html, Jsoup.parse(doc));
						logger.info(printer_id + "<=id,打印机名称：" + printName);
						// 下面用来更新打印过的打印类型
						String billNum = condition.optString("bill_num");
						Integer printCode = condition.optInt("print_code");
						Integer organId = condition.optInt("store_id");
						Integer rwid = condition.optInt("rwid");
						String printFormat = condition.optString("print_format");
						String printProperty = condition.optString("print_property");
						
						//print_format打印格式 1106 单切   1105大类  1107整台
						if (StringUtils.isNotEmpty(printFormat))
						{
							logger.info(printFormat + "<=模板,打印类型名称：" + printProperty);
							if ("1107".equals(printFormat))
							{
								String uPitem = new String("update pos_print_item set print_tag = ? where print_property = ? and printer_id = ? and print_format = ? and bill_num = ? and store_id = ? and tenancy_id = ?");
								this.dao.getJdbcTemplate(tenantId).update(uPitem,new Object[]{null,printProperty,printer_id,printFormat,billNum,organId,tenantId});
							}
							if ("1105".equals(printFormat))
							{
								String uPitem = new String("update pos_print_item set print_tag = ? where print_property = ? and printer_id = ? and print_format = ? and bill_num = ? and store_id = ? and tenancy_id = ?");
								this.dao.getJdbcTemplate(tenantId).update(uPitem,new Object[]{null,printProperty,printer_id,printFormat,billNum,organId,tenantId});
							}
							if ("1108".equals(printFormat))
							{
								String upitem = new String("update pos_print_item set print_tag = ? where print_property = ? and print_format = ? and bill_num = ? and store_id = ? and tenancy_id = ?");
								this.dao.getJdbcTemplate(tenantId).update(upitem, new Object[]{null,printProperty,printFormat,billNum,organId,tenantId});
							}
							if ("1106".equals(printFormat))
							{
								String uPitem = new String("update pos_print_item set print_tag = ? where print_property = ? and rwid = ? and print_format = ? and bill_num = ? and store_id = ? and tenancy_id = ?");
								this.dao.getJdbcTemplate(tenantId).update(uPitem,new Object[]{null,printProperty,rwid,printFormat,billNum,organId,tenantId});
							}
						}else
						{
							logger.info(printFormat + "<=printFormat是空值，直接更新pos_bill_item和pos_print_item");
							String uBitem = new String("update pos_bill_item set print_tag = ? where print_property = ? and rwid = ? and bill_num = ? and store_id = ? and tenancy_id = ?");
							this.dao.getJdbcTemplate(tenantId).update(uBitem, new Object[]{null,printProperty,printCode,billNum,organId,tenantId});
							
							String uPitem = new String("update pos_print_item set print_tag = ? where print_property = ? and rwid = ? and print_format = ? and bill_num = ? and store_id = ? and tenancy_id = ?");
							this.dao.getJdbcTemplate(tenantId).update(uPitem,new Object[]{null,printProperty,rwid,condition.optString("print_code"),billNum,organId,tenantId});
						}
						// 原来想统一处理，现在放到各种类别里面处理
						// String uPitem = new
						// String("update pos_print_item set print_tag = ? where rwid = ? and print_code = ? and bill_num = ? and store_id = ? and tenancy_id = ?");
						// this.dao.getJdbcTemplate(tenantId).update(uPitem,new
						// Object[]{null,rwid,condition.optString("print_code"),billNum,organId,tenantId});
					}
				}
			}
		}
	}
	
	@Override
	public void printCusBill(String tenancyID, JSONObject condition) throws Exception
	{
		logger.info("打印客户小票，打印参数为：" + condition.toString());
		/**
		 * 根据printer_id查询class_item_code即是打印模板 和打印机名称
		 */
		Integer printer_id = condition.optInt("printer_id");
		String classItemCode = condition.optString("print_code");
		String printName = "";

		String qPname = new String("select name from hq_print_printer where id = ?");
		SqlRowSet rs = this.dao.getJdbcTemplate(tenancyID).queryForRowSet(qPname, new Object[]
		{ printer_id });
		while (rs.next())
		{
			printName = rs.getString("name");
		}

		String sql = "select id,page_size,trim(param_pairs) as param_pairs,content from sys_print_format where class_item_code='" + classItemCode + "' and valid_state='1'";

		List<JSONObject> list = this.dao.query4Json(tenancyID, sql);

		if (list.size() > 0)
		{
			int id = list.get(0).optInt("id");
			int pageSize = list.get(0).optInt("page_size");
			
			String content = list.get(0).optString("content");

			sql = "select result_set_title,sql from sys_print_format_detail where parent_id=" + id;
			list = this.dao.query4Json(tenancyID, sql);

			List<JSONObject> param = new ArrayList<JSONObject>();
			param.add(condition);

			PrintTemplate_HTML html = new PrintTemplate_HTML();

			String doc = html.parseCus(pageSize, content, param);
			logger.debug("打印单子的打印机名称：" + printName);
			logger.debug("打印账单内容：" + doc);
			PrintHelper printHelper = PrintHelper.newInstance(pageSize, printName); // "Tzx Printer"测试用的

			printHelper.print(html, Jsoup.parse(doc));
		}
	}
	
	@Override
	public void newPrint(String tenantId, JSONObject condition) throws Exception
	{
		logger.info(tenantId + "进入newPrint打印方法，打印参数为：" + condition.toString());
		String printFormat = condition.optString("print_format");
		String printName = condition.optString("printer_name");
		
		// 根据打印类型查询模板信息
		String sql = "select id,page_size,trim(param_pairs) as param_pairs,content from sys_print_format where class_item_code='" + printFormat + "' and valid_state='1'";

		List<JSONObject> list = this.dao.query4Json(tenantId, sql);

		if (list.size() > 0)
		{
			int id = list.get(0).optInt("id");
			int pageSize = list.get(0).optInt("page_size");
			JSONObject paramPairs = JSONObject.fromObject(list.get(0).get("param_pairs"));
			JSONArray rows = paramPairs.optJSONArray("rows");
			String content = list.get(0).optString("content");

			sql = "select result_set_title,sql from sys_print_format_detail where parent_id=" + id;
			list = this.dao.query4Json(tenantId, sql);

			List<JSONObject> param = new ArrayList<JSONObject>();

			for (JSONObject json : list)
			{
				JSONObject p = new JSONObject();
				p.put("result_set_title", json.optString("result_set_title"));

				String testSql = json.optString("sql").trim();

				List<Object> values = new ArrayList<Object>();// 参数列表

				if (testSql.substring(0, 6).equalsIgnoreCase("select"))
				{
					StringBuilder sb = new StringBuilder(testSql);

					int i = 0;
					String temp = "";

					while (true)
					{
						i = sb.indexOf(":");
						int j = 0;

						if (i > 0)
						{
							temp = sb.substring(i, sb.length());

							j = temp.indexOf(" ");

							if (j < 0)
							{
								j = temp.length();
							}

							String t = temp.substring(1, j);

							Object value = null;
							for (Object row : rows)
							{
								JSONObject r = JSONObject.fromObject(row);

								if (t.equals(r.optString("paramName")))
								{

									if ("test".equals(condition.optString("printmode")))
									{
										value = r.optString("value");
									}
									else
									{
										value = condition.get(t); // 返回object类型
									}
									// 这块判断日期格式，不加的话，有日期参数就查不出来内容，报类型错误
									if (Tools.isNullOrEmpty(value) == false && DateUtil.isDate(value.toString()))
									{
										Date vDate = DateUtil.parseDate(value.toString());
										values.add(vDate);
									}
									else
									{
										values.add(value);
									}
									break;
								}
							}

							sb.replace(i, i + j, "?");
						}
						else
						{
							break;
						}
					}
					Object[] objs = values.toArray();
					p.put("result", this.dao.query4JSONArray(tenantId, sb.toString(), objs));
				}

				param.add(p);
			}

			PrintTemplate_HTML html = new PrintTemplate_HTML();

			String doc = html.parse(pageSize, content, param);

			PrintHelper printHelper = PrintHelper.newInstance(pageSize, printName); // "Tzx Printer"测试用的

			printHelper.print(html, Jsoup.parse(doc));
			// 下面用来更新打印过的打印类型
			String billNum = condition.optString("bill_num");
			Integer organId = condition.optInt("store_id");
			Integer printId = condition.optInt("id");
			//把打印表pos_print表里的打印状态更为null，默认是*未打印
			String uPitem = new String("update pos_print set print_tag = ? where id = ? and bill_num = ? and store_id = ? and tenancy_id = ?");
			this.dao.getJdbcTemplate(tenantId).update(uPitem,new Object[]{null,printId,billNum,organId,tenantId});
		}
	}
	
	public void invokePrintFile() throws Exception
	{
		
	}

	public static void main(String args[]) throws Exception
	{
		PrintFormatServiceImpl p = new PrintFormatServiceImpl();
		p.print(null, null);

	}
}
