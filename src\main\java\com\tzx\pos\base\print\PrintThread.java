package com.tzx.pos.base.print;

import net.sf.json.JSONObject;

import org.apache.log4j.Logger;

import com.alipay.api.internal.util.StringUtils;
import com.tzx.base.bo.PrintFormatService;
import com.tzx.base.bo.imp.PrintFormatServiceImpl;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;

/**
 *
 * <AUTHOR> 2015年7月27日-下午3:43:50
 */
public class PrintThread extends Thread // implements Runnable
{
	private static final Logger logger = Logger.getLogger(PrintThread.class);
	// private static PrintThread printThread = new PrintThread();

	NewQueue newQueue = NewQueueUtil.getQueue();
	private JSONObject print;

	public JSONObject getPrint() {
		return print;
	}

	public void setPrint(JSONObject print) {
		this.print = print;
	}

	public PrintThread() {
		super();
	}

	public PrintThread(JSONObject json) {
		this.print = json;
	}

	private boolean hasMoreAcquire() {
		return !newQueue.isEmpty();
	}

	@Override
	public void run() {
		while (true) {
			try {
//				if (hasMoreAcquire()) {
					JSONObject msg = (JSONObject) newQueue.pull();
					print = msg;

					String tenantId = print.getString("tenancy_id");

					String mode = print.optString("mode");

					DBContextHolder.setTenancyid(tenantId);

					PrintFormatServiceImpl printFormatService = (PrintFormatServiceImpl) SpringConext
							.getApplicationContext().getBean(PrintFormatService.NAME);

					if (StringUtils.isEmpty(mode)) {
						printFormatService.newPrint(tenantId, print);
					} else {
						if ("1".equals(mode)) {
							printFormatService.printCusBill(tenantId, print);
						} else {
							printFormatService.printBill(tenantId, print);
						}
					}

//				} else {/// if queue is not null
//					Thread.sleep(1000);
//				}
			} catch (Exception e) {
				logger.info("调用厨打线程失败：" + ExceptionMessage.getExceptionMessage(e));
				e.printStackTrace();
			} catch (java.lang.Throwable es) {
				es.printStackTrace();
			}

		} // end while true
	}
}
