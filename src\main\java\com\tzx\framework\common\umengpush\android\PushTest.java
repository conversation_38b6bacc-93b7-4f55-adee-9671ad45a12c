package com.tzx.framework.common.umengpush.android;

import org.json.JSONArray;
import org.json.JSONObject;

import com.tzx.framework.common.umengpush.push.android.AndroidBroadcast;
import com.tzx.framework.common.umengpush.push.android.AndroidGroupcast;
import com.tzx.framework.common.umengpush.push.android.AndroidUnicast;

public class PushTest {

	public static void main(String[] args) throws Exception {
		PushInfo info = new PushInfo("测试ticker", "测试title","测试text");
		UMPusher pusher = UMPusher.build(info);
		AndroidUnicast cast = sendUnicast(pusher);
		cast.setTestMode();
		pusher.send();
	}
	//广播
	public static AndroidBroadcast sendBroadcast(UMPusher pusher) throws Exception{
		return pusher.initAndroidBroadcast();
	}
	//单播
	public static AndroidUnicast sendUnicast(UMPusher pusher) throws Exception{
		String device_token = "AiZznag3IFkm4LJHkbavTT1Tu4A52C3EMJZuL5vAopgN";
		return pusher.initAndroidUnicast(device_token);
	}
	//条件播
	public static AndroidGroupcast sendGroupcast(UMPusher pusher) throws Exception{
		JSONArray array = new JSONArray();
		JSONObject json = new JSONObject();
		json.put(FilterKeyEnum.NOT_LAUNCH_FROM.keyName, "2017-03-25");
		array.put(json);
		pusher.addFilterCondition(array);
		return pusher.initAndroidGroupcast();
	}
	
}
