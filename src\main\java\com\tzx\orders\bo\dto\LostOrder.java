package com.tzx.orders.bo.dto;

import java.io.Serializable;

public class LostOrder implements Serializable{
	
	private static final long serialVersionUID = -5499299650051600568L;
	private String single_date;       //下单日期
	private String storeId;          //门店id
	private String tenancyId;        //商户编号
	private String total;            //总数
	private String loss;             //丢单数
	private String channel;          //渠道 
	private String serial_number_agg; //编号
	public String getSingle_date() {
		return single_date;
	}
	public void setSingle_date(String single_date) {
		this.single_date = single_date;
	}
	public String getStoreId() {
		return storeId;
	}
	public void setStoreId(String storeId) {
		this.storeId = storeId;
	}
	public String getTenancyId() {
		return tenancyId;
	}
	public void setTenancyId(String tenancyId) {
		this.tenancyId = tenancyId;
	}
	public String getTotal() {
		return total;
	}
	public void setTotal(String total) {
		this.total = total;
	}
	public String getLoss() {
		return loss;
	}
	public void setLoss(String loss) {
		this.loss = loss;
	}
	public String getChannel() {
		return channel;
	}
	public void setChannel(String channel) {
		this.channel = channel;
	}
	
	public String getSerial_number_agg() {
		return serial_number_agg;
	}
	public void setSerial_number_agg(String serial_number_agg) {
		this.serial_number_agg = serial_number_agg;
	}
}
