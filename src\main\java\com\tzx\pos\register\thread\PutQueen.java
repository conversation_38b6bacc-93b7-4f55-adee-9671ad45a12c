package com.tzx.pos.register.thread;

import java.util.List;

import com.tzx.pos.register.service.RegisterService;

import net.sf.json.JSONObject;

public class PutQueen extends Thread {

	private RegisterService registerService;
	private UploadQueen queen;
	boolean flag = true;

	public PutQueen(UploadQueen queen, RegisterService registerService) {
		this.queen = queen;
		this.registerService = registerService;
	}

	@Override
	public void run() {
		while (true) {
			try {
				synchronized (queen) {
					List<JSONObject> listJson = null;
					try {
						listJson = registerService.queryHqLocalDeviceByStatus();
					} catch (Exception e1) {
						// TODO Auto-generated catch block
						e1.printStackTrace();
					}

					if (null == listJson || listJson.size() == 0
							|| listJson.get(0).getJSONArray("devices").size() == 0) {
						try {
							queen.wait();
						} catch (Exception e) {
							// TODO Auto-generated catch block
							e.printStackTrace();
						}
					} else {
						for (int i = 0; i < listJson.size(); i++) {

							queen.put(listJson.get(i));
						}
					}
					Thread.sleep(10000);
				}

			} catch (InterruptedException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
	}

}
