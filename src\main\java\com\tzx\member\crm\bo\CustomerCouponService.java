package com.tzx.member.crm.bo;

import net.sf.json.JSONObject;

import com.tzx.framework.common.entity.Data;

public interface CustomerCouponService extends CustomerService
{
	String	NAME	= "com.tzx.member.crm.bo.imp.CustomerCouponServiceImp";

	/**
	 * 积分消费
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param param
	 * @param resData
	 * @param operType
	 * @throws Exception
	 */
	public void customerCouponConsume(String tenancyId, int storeId, JSONObject param, Data resData, String operType) throws Exception;

	/**
	 * 积分消费查询
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param customerOperate
	 * @return
	 * @throws Exception
	 */
	public Data queryCustomerCouponConsume(String tenancyId, int storeId, JSONObject paramJson) throws Exception;

	/**
	 * 撤销积分消费
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param param
	 * @param resData
	 * @param operType
	 * @throws Exception
	 */
	public void customerCouponCancelConsume(String tenancyId, int storeId, JSONObject param, Data resData, String operType) throws Exception;

	/**
	 * 撤销积分消费查询
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param customerOperate
	 * @return
	 * @throws Exception
	 */
	public Data queryCustomerCouponCancelConsume(String tenancyId, int storeId, JSONObject paramJson) throws Exception;
	
	/** 优惠券验证
	 * @param param
	 * @param resData
	 * @throws Exception
	 */
	public void checkCustomerCoupon(Data param, Data resData) throws Exception;
}
