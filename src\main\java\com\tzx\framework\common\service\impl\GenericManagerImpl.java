package com.tzx.framework.common.service.impl;


import java.util.List;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;

import com.tzx.framework.common.service.GenericManager;
import com.tzx.framework.common.util.dao.GenericDao;




@Service("GenericManager")
public class GenericManagerImpl implements GenericManager {
	//	protected GenericDao genericDao;
	//	@Resource
	//	public void setDao(DAO genericDao) {
	//		this.genericDao = genericDao;
	//	}


	@Resource(name="genericDaoImpl")
	private GenericDao genericDao;
	
	
	public List<JSONObject> findBasicConfig(String tenantId) throws Exception
	{
		String sql = "SELECT C.KEYNAME, C.KEYVALUE FROM BASICCONFIG C";
		
		return this.genericDao.query4Json(tenantId, sql);
	}

	
	public String findBasicConfig(String tenantId, String keyName) throws Exception
	{
		String sql = "SELECT C.KEYVALUE FROM BASICCONFIG C WHERE C.KEYNAME='" + keyName + "'";
		
		SqlRowSet srs = this.genericDao.query(tenantId, sql);
		
		if(srs != null && srs.next())
		{
			return srs.getString(1);
		}
		
		return null;
	}


}