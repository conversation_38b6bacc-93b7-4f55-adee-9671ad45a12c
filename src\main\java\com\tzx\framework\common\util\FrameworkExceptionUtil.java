package com.tzx.framework.common.util;

import java.util.List;

import net.sf.json.JSONObject;

import com.tzx.framework.common.exception.FrameworkErrorCode;
import com.tzx.framework.common.exception.SystemException;

public class FrameworkExceptionUtil
{
	
	

	public static void checkListSizeZero(List<JSONObject> list, FrameworkErrorCode ecode) throws SystemException
	{
		if (list == null || list.size() == 0)
		{
			throw new SystemException(ecode);
		}
	}
	
	
	public static void checkListSizeNotZero(List<JSONObject> list, FrameworkErrorCode ecode) throws SystemException
	{
		if (list != null && list.size() > 0)
		{
			throw new SystemException(ecode);
		}
	}

	

}
