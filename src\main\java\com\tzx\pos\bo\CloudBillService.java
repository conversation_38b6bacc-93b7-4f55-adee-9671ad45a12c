package com.tzx.pos.bo;

import com.tzx.framework.common.entity.Data;
import net.sf.json.JSONObject;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017-12-27.
 * BOH 云账单类
 */
public interface CloudBillService extends PosBaseService {
    String	NAME	= "com.tzx.pos.bo.imp.CloudBillServiceImp";
    /**
     *
     * @param param
     * @throws Exception
     */
    public String findCloudBill(String param) throws Exception;

    /**
     *
     * @param param
     * @throws Exception
     */
    public String validateCloudBill(String param) throws Exception;

    /**
     * 锁台和解锁
     * @param param
     * @param result
     * @return
     * @throws Exception
     */
    public Data locking(Data param, Data result)throws  Exception;

    /**
     * 强制解锁
     * @param param
     * @param result
     * @return
     * @throws Exception
     */
    public Data forcedUnlocking(Data param, Data result)throws  Exception;

    /**
     * 关闭锁单表的账单
     * @param param
     * @param result
     * @return
     * @throws Exception
     */
    public Data closeBillLock(Data param, Data result)throws Exception;

    /**
     * 查询账单锁定状态
     * @param param
     * @param result
     * @return
     * @throws Exception
     */
    public Data findBillLockState(Data param, Data result) throws  Exception;

    /**
     * 同步云POS已结账单
     * @param tenancyId
     * @param storeId
     * @param data
     * @throws Exception
     */
    void syncCloudPaymentBill(String tenancyId, int storeId, JSONObject data) throws Exception;

    /**
     * 门店已结账单通知云端
     * @param tenancyId
     * @param storeId
     * @param billNum
     * @throws Exception
     */
    void syncPosBillToCloudBill(String tenancyId, int storeId, String billNum) throws Exception;

    /**
     * 查询已结账，未通知云端账单
     * @param tenancyId
     * @param storeId
     * @return
     * @throws Exception
     */
    List<JSONObject> queryCompleteNoSyncBill(String tenancyId, int storeId) throws Exception;

}
