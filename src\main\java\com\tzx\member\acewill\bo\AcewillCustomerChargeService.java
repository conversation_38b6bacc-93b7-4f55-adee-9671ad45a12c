package com.tzx.member.acewill.bo;

import java.util.Date;

import com.tzx.framework.common.entity.Data;
import com.tzx.member.common.entity.PosCustomerOperateListEntity;

import net.sf.json.JSONObject;

public interface AcewillCustomerChargeService
{
	String NAME = "com.tzx.member.acewill.bo.imp.AcewillCustomerChargeServiceImp";

	/**
	 * 微生活会员储值预览
	 *
	 * @param requestData
	 * @return
	 * @throws Exception
	 */
	public Data previewAcewillCharge(Data requestData) throws Exception;

	/**
	 * 微生活会员储值提交
	 *
	 * @param requestData
	 * @return
	 * @throws Exception
	 */
	public Data commitAcewillCharge(String tenancyId, Integer storeId, String thirdBillCode, JSONObject jsonObject) throws Exception;

	/**
	 * 插入操作流水表
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param oper_type
	 * @param paramJson
	 * @param resultJson
	 * @return
	 * @throws Exception
	 */
	public String addCustomerOperateListForRecharge(String tenantId, Integer storeId, String oper_type, JSONObject paramJson) throws Exception;

	/**
	 * 获取二维码
	 *
	 * @param tenantId
	 * @param storeId
	 * @param paramJson
	 * @param path
	 * @throws Exception
	 */
	public Data precreatePaymentForRecharge(String tenantId, Integer storeId, JSONObject paramJson, String path) throws Exception;

	/**
	 * 扫码支付
	 *
	 * @param tenantId
	 * @param storeId
	 * @param paramJson
	 * @return
	 * @throws Exception
	 */
	public Data barcodePaymentForRecharge(String tenantId, Integer storeId, JSONObject paramJson) throws Exception;

	/**
	 * 查询充值状态
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param paramJson
	 * @return
	 * @throws Exception
	 */
	public PosCustomerOperateListEntity queryCustomerCardRecharge(String tenantId, Integer storeId, String thirdBillCode, String cardCode, String operatType) throws Exception;
	
	/**
	 * 查询充值详情
	 *
	 * @param tenantId
	 * @param storeId
	 * @param paramJson
	 * @return
	 * @throws Exception
	 */
	public JSONObject queryCustomerCardRechargeForDetails(String tenantId, Integer storeId, PosCustomerOperateListEntity cardRecharge) throws Exception;

	/** 根据业务号查储值详情
	 * @param tenantId
	 * @param storeId
	 * @param customer
	 * @return
	 * @throws Exception
	 */
	public Data findCustomerCardChargeDetails(String tenantId, Integer storeId,PosCustomerOperateListEntity customer,JSONObject printJson) throws Exception;
	
	/**
	 * 撤销充值
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param cardRecharge
	 * @return
	 * @throws Exception
	 */
	public Data revokeAcewillCharge(String tenantId, Integer storeId, String thirdBillCode, Data paramData) throws Exception;

	/**
	 * @param tenantId
	 * @param storeId
	 * @param reportDate
	 * @param shiftId
	 * @param posNum
	 * @param optNum
	 * @param thirdBillCode
	 * @param cardRecharge
	 * @return
	 * @throws Exception
	 */
	public Data revokeAcewillCharge(String tenantId, Integer storeId, Date reportDate, Integer shiftId, String posNum, String optNum, String thirdBillCode, PosCustomerOperateListEntity cardRecharge) throws Exception;
	
	/**
	 * 取消第三方付款
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param paramJson
	 * @return
	 * @throws Exception
	 */
	public Data cancelThirdPaymentForCardRecharge(String tenantId, Integer storeId, String thirdBillCode, PosCustomerOperateListEntity cardRecharge) throws Exception;

	/**
	 * 打印小票
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param paramJson
	 * @throws Exception
	 */
	public void printForCustomerCardRecharge(String tenancyId, Integer storeId, String printCode, JSONObject paramJson) throws Exception;

	/** 打印小票
	 * @param tenancyId
	 * @param storeId
	 * @param printCode
	 * @param paramJson
	 * @param resultJson
	 * @throws Exception
	 */
	public void printForCustomerCardRecharge(String tenancyId, Integer storeId, JSONObject paramJson) throws Exception;
}
