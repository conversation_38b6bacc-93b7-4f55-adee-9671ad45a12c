package com.tzx.pos.base.service.impl;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import com.tzx.member.crm.po.springjdbc.dao.CustomerDao;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.util.Tools;
import com.tzx.member.acewill.bo.AcewillCustomerChargeService;
import com.tzx.member.common.entity.PosCustomerOperateListEntity;
import com.tzx.member.crm.bo.CustomerCardActivationService;
import com.tzx.member.crm.bo.CustomerCardRechargeService;
import com.tzx.member.crm.bo.CustomerService;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.service.ThirdPaymentOrderService;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.base.util.JsonUtil;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.bo.PosPrintNewService;
import com.tzx.pos.bo.PosPrintService;
import com.tzx.pos.bo.dto.PosThirdPaymentOrderEntity;
import com.tzx.pos.bo.payment.PosPaymentService;
import com.tzx.thirdpay.bo.ThirdPaymentService;
import com.tzx.thirdpay.po.springjdbc.dao.ThirdPaymentDao;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

@Service(ThirdPaymentOrderService.NAME)
public class ThirdPaymentOrderServiceImp implements ThirdPaymentOrderService
{
	private static final Logger				logger	= Logger.getLogger(ThirdPaymentOrderServiceImp.class);

	@Resource(name = ThirdPaymentService.NAME)
	public ThirdPaymentService			thirdPaymentService;

	@Resource(name = CustomerService.NAME)
	private CustomerService					customerService;

	@Resource(name = PosPaymentService.NAME)
	public PosPaymentService				paymentService;

	@Resource(name = CustomerCardActivationService.NAME)
	public CustomerCardActivationService	customerCardActivationService;
	
	@Resource(name = CustomerCardRechargeService.NAME)
	public CustomerCardRechargeService	cardRechargeService;

	@Resource(name = PosPrintNewService.NAME)
	private PosPrintNewService				posPrintNewService;

	@Resource(name = PosPrintService.NAME)
	private PosPrintService					posPrintService;

	@Resource(name = ThirdPaymentDao.NAME)
	private ThirdPaymentDao					paymentDao;


	@Resource(name = com.tzx.member.acewill.bo.CustomerService.NAME)
	private com.tzx.member.acewill.bo.CustomerService alCustomerService;

	@Resource(name = AcewillCustomerChargeService.NAME)
	private AcewillCustomerChargeService aChargeService;

	@Resource(name = com.tzx.member.acewill.bo.AcewillCustomerService.NAME)
	private com.tzx.member.acewill.bo.AcewillCustomerService acewillService;

	@Resource(name = CustomerDao.NAME)
	protected CustomerDao customerDao;

	@Override
	public List<PosThirdPaymentOrderEntity> getThirdPaymentOrderList(String tenancyId, int storeId, Double queryCount) throws Exception
	{
		Timestamp currentTime = DateUtil.currentTimestamp();
		return paymentDao.getThirdPaymentOrderForNotComplete(tenancyId, storeId, currentTime, queryCount);
	}

//	@Override
//	public void updateThirdPaymentOrderListState(String tenancyId, int storeId, int paymentId, int queryCount) throws Exception
//	{
//		Timestamp currentTime = DateUtil.currentTimestamp();
//		queryCount = queryCount + 1;
//		paymentDao.updateThirdPaymentOrderForQuery(tenancyId, storeId, paymentId, currentTime, queryCount);
//	}

	@Override
	public void queryThirdPaymentForRunnable(String tenancyId, int storeId, PosThirdPaymentOrderEntity thirdPayment) throws Exception
	{
		//调用第三方支付接口,查询支付状态
		Data resultData = thirdPaymentService.queryPayment(tenancyId, storeId, thirdPayment);

		//查询查询结果
		this.queryThirdPaymentResult(tenancyId, storeId, thirdPayment, resultData);

	}

	@Override
	public void thirdPaymentCallback(String tenancyId, int storeId, JSONObject data) throws Exception
	{
		JSONObject dataJson = data.optJSONArray("data").getJSONObject(0);
		String orderNo = dataJson.optString("order_no");

		// 根据账单号查询第三方支付记录
		PosThirdPaymentOrderEntity thirdPaymentOrder = paymentDao.getThirdPaymentByAidOrderNum(tenancyId, storeId, orderNo, SysDictionary.THIRD_PAY_CHARGE);
		int thirdPaymentId = 0;
		if (null != thirdPaymentOrder)
		{
			thirdPaymentId = thirdPaymentOrder.getId();
		}
		if (0 == thirdPaymentId)
		{
			thirdPaymentOrder = paymentDao.getThirdPaymentByAidOrderNum(tenancyId, storeId, orderNo, SysDictionary.THIRD_PAY_REFUND);
		}

		if (null == thirdPaymentOrder || SysDictionary.THIRD_PAY_STATUS_SUCCESS.equals(thirdPaymentOrder.getStatus()) || SysDictionary.THIRD_PAY_STATUS_FAIL.equals(thirdPaymentOrder.getStatus()) || SysDictionary.THIRD_PAY_STATUS_REFUND_SUCCESS.equals(thirdPaymentOrder.getStatus())
				|| SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL.equals(thirdPaymentOrder.getStatus()) || SysDictionary.THIRD_PAY_STATUS_REVOKED_SUCCESS.equals(thirdPaymentOrder.getStatus()) || SysDictionary.THIRD_PAY_STATUS_REVOKED_FAIL.equals(thirdPaymentOrder.getStatus()))
		{
			return;
		}
		// 修改第三方支付记录
		Data responseData = null;
		if (null != data && false ==data.isEmpty())
		{
			responseData = JsonUtil.JsonToData(data);
			if (Constant.CODE_SUCCESS == responseData.getCode())
			{
				List<?> responseList = responseData.getData();
				if (null == responseList || responseList.isEmpty() || 0 == responseList.size())
				{
					responseData.setCode(PosErrorCode.THIRD_PAYMENT_FAILURE.getNumber());
					responseData.setMsg(PosErrorCode.THIRD_PAYMENT_FAILURE.getMessage());
				}
			}
		}
		else
		{// 返回为空
			responseData = Data.get();
			responseData.setCode(Constant.CODE_CONN_EXCEPTION);
			responseData.setMsg(Constant.GET_PAYMENT_WAY_MESSAGE_IS_NULL);
		}
		thirdPaymentService.queryPaymentResult(tenancyId, storeId, responseData, thirdPaymentOrder);

		// 根据第三方支付结果判断,返回结果
		this.queryThirdPaymentResult(tenancyId, storeId, thirdPaymentOrder, responseData);
	}

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param thirdPayment
	 * @param resultData
	 * @throws Exception
	 */
	private void queryThirdPaymentResult(String tenancyId, int storeId, PosThirdPaymentOrderEntity thirdPayment, Data resultData) throws Exception
	{
		switch (thirdPayment.getService_type())
		{
			case SysDictionary.SERVICE_TYPE_CONSUME:
			case SysDictionary.SERVICE_TYPE_ORDER:
				this.queryThirdPaymentResultForBill(tenancyId, storeId, thirdPayment, resultData);
				break;

			case SysDictionary.SERVICE_TYPE_RECHARGE:
				this.queryThirdPaymentResultForCardRecharge(tenancyId, storeId, thirdPayment, resultData);
				break;

			case SysDictionary.SERVICE_TYPE_ACTIVATION:
				this.queryThirdPaymentResultForCardActivation(tenancyId, storeId, thirdPayment, resultData);
				break;

			default:
				break;
		}
	}

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param params
	 * @param resultData
	 * @throws Exception
	 */
	private void queryThirdPaymentResultForBill(String tenancyId, int storeId, PosThirdPaymentOrderEntity params, Data resultData) throws Exception
	{
		synchronized (params.getAid_order_num())
		{
			if (SysDictionary.THIRD_PAY_CHARGE.equals(params.getObject_name()))
			{
				// 支付记录结果处理
				JSONObject printJson = new JSONObject();
				this.queryThirdPaymentResultForCharge(tenancyId, storeId, params, resultData, printJson);

				// 打印结账单
				if (null != printJson && !printJson.isEmpty())
				{
					try
					{
						posPrintService.printPosBillForPayment(printJson, posPrintNewService);
					}
					catch (Exception e)
					{
						logger.info(e);
					}
				}
			}
			else
			{
				// 退款结果处理
				JSONObject printJson = new JSONObject();
				// 判断是取消付款,或整单取消,或恢复账单
				if (Type.BILL_PAYMENT_CANCEL.name().equals(params.getOper_type()))
				{
					this.queryThirdPaymentResultForCancelPayment(tenancyId, storeId, params, resultData, printJson);
				}
				else if (Type.CANCBILL.name().equals(params.getOper_type()))
				{
					this.queryThirdPaymentResultForCancBill(tenancyId, storeId, params, resultData, printJson);
				}
				else if (Type.REGAIN_BILL.name().equals(params.getOper_type()))
				{
					this.queryThirdPaymentResultForRegainBill(tenancyId, storeId, params, resultData, printJson);
				}
			}
		}
	}

	private void queryThirdPaymentResultForCharge(String tenancyId, int storeId, PosThirdPaymentOrderEntity params,Data resultData, JSONObject printJson) throws Exception
	{
		if (Constant.CODE_SUCCESS == resultData.getCode())
		{
			List<?> returnDataList = resultData.getData();
			JSONObject returnJson = new JSONObject();
			if (null != returnDataList && returnDataList.size() > 0)
			{
				returnJson = JSONObject.fromObject(returnDataList.get(0));
			}

			String paymentState = returnJson.optString("payment_state");
			String transactionNo = returnJson.optString("transaction_no");
			String paymentType = returnJson.optString("pay_type");

//			Data returnData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);

			Date reportDate = params.getReport_date();
			String report_Date = DateUtil.formatDate(reportDate);
			String orderNo = params.getOrder_num();
			int shiftId = params.getShift_id();
			String posNum = params.getPos_num();
			String optNum = params.getOpt_num();
			Integer paymentId = params.getPayment_id();

			String billNum = paymentDao.getBillNumByOrderNo(tenancyId, storeId, orderNo);

			if (SysDictionary.THIRD_PAY_STATUS_SUCCESS.equals(paymentState))
			{
				Data payData = this.getPayParam(tenancyId, storeId, report_Date, shiftId, posNum, optNum, billNum, paymentId, transactionNo, paymentType, "Y", returnJson);
				paymentService.billPaymentService(payData, (new Data()), printJson);
			}
			else if (false == SysDictionary.THIRD_PAY_STATUS_PAYING.equals(paymentState))
			{
				// 付款失败,修改账单付款状态
				JSONObject item = paymentDao.getBillPaymentByJzid(tenancyId, storeId, billNum, paymentId);
				JSONObject payment = new JSONObject();
				payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_FAILURE);
				payment.put("id", item.optInt("id"));
				paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", payment);
			}
		}
	}
	
	/**取消付款结果处理
	 * @param tenancyId
	 * @param storeId
	 * @param params
	 * @param resultData2
	 * @param printJson
	 * @throws Exception
	 */
	private void queryThirdPaymentResultForCancelPayment(String tenancyId, int storeId, PosThirdPaymentOrderEntity params,Data resultData2, JSONObject printJson) throws Exception
	{
		List<?> returnDataList = resultData2.getData();
		JSONObject returnJson = new JSONObject();
		if (null != returnDataList && returnDataList.size() > 0)
		{
			returnJson = JSONObject.fromObject(returnDataList.get(0));
		}

		String paymentState = returnJson.optString("payment_state");
		String orderNo = params.getOrder_num();
		Integer paymentId = params.getPayment_id();
		String aidOrderNum = params.getAid_order_num();

		String billNum = paymentDao.getBillNumByOrderNo(tenancyId, storeId, orderNo);

		switch (paymentState)
		{
			case SysDictionary.THIRD_PAY_STATUS_SUCCESS:
			case SysDictionary.THIRD_PAY_STATUS_PAYING:
				paymentState = SysDictionary.THIRD_PAY_STATUS_REVOKED_FAIL;
				break;

			case SysDictionary.THIRD_PAY_STATUS_FAIL:
				paymentState = SysDictionary.THIRD_PAY_STATUS_REVOKED_SUCCESS;
				break;

			case SysDictionary.THIRD_PAY_STATUS_REFUND_SUCCESS:
			case SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL:
			case SysDictionary.THIRD_PAY_STATUS_REVOKED_SUCCESS:
			case SysDictionary.THIRD_PAY_STATUS_REVOKED_FAIL:
				break;
		}
		
		// 取消付款,取消成功删除付款,判断付款时间
		JSONObject paymentJson = paymentDao.getPosBillPaymentByBillnum(tenancyId, storeId, billNum, paymentId);

		if (null != paymentJson && !paymentJson.isEmpty())
		{
			String timeStr = aidOrderNum.substring(aidOrderNum.indexOf("@") + 1);
			String paymentTimeStr = String.valueOf(DateUtil.parseTimestamp(paymentJson.optString("last_updatetime")).getTime());
			if (timeStr.equals(paymentTimeStr))
			{
				JSONObject paymentCancBill = new JSONObject();
				paymentCancBill.put("payment_state", paymentState);
				paymentCancBill.put("id", paymentJson.optInt("id"));
				paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", paymentCancBill);
			}
		}
	}
	
	/** 整单取消结果处理
	 * @param tenancyId
	 * @param storeId
	 * @param params
	 * @param resultData2
	 * @param printJson
	 * @throws Exception
	 */
	private void queryThirdPaymentResultForCancBill(String tenancyId, int storeId, PosThirdPaymentOrderEntity params,Data resultData2, JSONObject printJson) throws Exception
	{
		List<?> returnDataList = resultData2.getData();
		JSONObject returnJson = new JSONObject();
		if (null != returnDataList && returnDataList.size() > 0)
		{
			returnJson = JSONObject.fromObject(returnDataList.get(0));
		}

		String paymentState = returnJson.optString("payment_state");
		String orderNo = params.getOrder_num();
		Integer paymentId = params.getPayment_id();

		String billNum = paymentDao.getBillNumByOrderNo(tenancyId, storeId, orderNo);

		switch (paymentState)
		{
			case SysDictionary.THIRD_PAY_STATUS_SUCCESS:
			case SysDictionary.THIRD_PAY_STATUS_PAYING:
				paymentState = SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL;
				break;

			case SysDictionary.THIRD_PAY_STATUS_FAIL:
				paymentState = SysDictionary.THIRD_PAY_STATUS_REFUND_SUCCESS;
				break;

			case SysDictionary.THIRD_PAY_STATUS_REFUND_SUCCESS:
			case SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL:
			case SysDictionary.THIRD_PAY_STATUS_REVOKED_SUCCESS:
			case SysDictionary.THIRD_PAY_STATUS_REVOKED_FAIL:
				break;
		}

		List<JSONObject> paymentList = paymentDao.getPosBillPaymentByCancBill(tenancyId, storeId, billNum, paymentId);
		if (null != paymentList)
		{
			for (JSONObject paymentItemJson : paymentList)
			{
				if (params.getSettle_amount() == Math.abs(paymentItemJson.optDouble("currency_amount")))
				{
					JSONObject paymentCancBill = new JSONObject();
					paymentCancBill.put("payment_state", paymentState);
					paymentCancBill.put("id", paymentItemJson.optInt("id"));
					paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", paymentCancBill);
				}
			}
		}
	}
	
	
	/** 恢复账单结果处理
	 * @param tenancyId
	 * @param storeId
	 * @param params
	 * @param resultData2
	 * @param printJson
	 * @throws Exception
	 */
	private void queryThirdPaymentResultForRegainBill(String tenancyId, int storeId, PosThirdPaymentOrderEntity params,Data resultData2, JSONObject printJson) throws Exception
	{
		List<?> returnDataList = resultData2.getData();
		JSONObject returnJson = new JSONObject();
		if (null != returnDataList && returnDataList.size() > 0)
		{
			returnJson = JSONObject.fromObject(returnDataList.get(0));
		}

		String paymentState = returnJson.optString("payment_state");
		String orderNo = params.getOrder_num();
		Integer paymentId = params.getPayment_id();
		String aidOrderNum = params.getAid_order_num();

		String billNum = paymentDao.getBillNumByOrderNo(tenancyId, storeId, orderNo);

		switch (paymentState)
		{
			case SysDictionary.THIRD_PAY_STATUS_SUCCESS:
			case SysDictionary.THIRD_PAY_STATUS_PAYING:
				paymentState = SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL;
				break;

			case SysDictionary.THIRD_PAY_STATUS_FAIL:
				paymentState = SysDictionary.THIRD_PAY_STATUS_REFUND_SUCCESS;
				break;

			case SysDictionary.THIRD_PAY_STATUS_REFUND_SUCCESS:
			case SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL:
			case SysDictionary.THIRD_PAY_STATUS_REVOKED_SUCCESS:
			case SysDictionary.THIRD_PAY_STATUS_REVOKED_FAIL:
				break;
		}

		List<JSONObject> paymentList = paymentDao.getPosBillPaymentByRegainBill(tenancyId, storeId, billNum, paymentId);
		if (null != paymentList)
		{
			String timeStr = aidOrderNum.substring(aidOrderNum.indexOf("@") + 1);
			for (JSONObject paymentItemJson : paymentList)
			{
				String paymentTimeStr = String.valueOf(DateUtil.parseTimestamp(paymentItemJson.optString("last_updatetime")).getTime());
				if (params.getSettle_amount() == Math.abs(paymentItemJson.optDouble("currency_amount")) && timeStr.equals(paymentTimeStr))
				{
					JSONObject paymentCancBill = new JSONObject();
					paymentCancBill.put("payment_state", paymentState);
					paymentCancBill.put("id", paymentItemJson.optInt("id"));
					paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment_regain", paymentCancBill);
				}
			}
		}
	}
	
	/**
	 * 封装消费接口
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param reportDate
	 * @param shiftId
	 * @param posNum
	 * @param optNum
	 * @param billNum
	 * @param paymentId
	 * @param transactionNo
	 * @param isPrintBill
	 * @return
	 * @throws Exception
	 */
	private Data getPayParam(String tenancyId, Integer storeId, String reportDate, Integer shiftId, String posNum, String optNum, String billNum, Integer paymentId, String transactionNo, String paymentType, String isPrintBill,JSONObject queryResultJson) throws Exception
	{
		JSONObject postBill = paymentDao.getPosBillByBillnum(tenancyId, storeId, billNum);

		JSONObject data = new JSONObject();
		data.put("report_date", reportDate);
		data.put("shift_id", shiftId);
		data.put("pos_num", posNum);
		data.put("opt_num", optNum);
		data.put("bill_num", billNum);
		data.put("table_code", postBill.optString("table_code"));
		data.put("payment_amount", postBill.opt("payment_amount"));
		data.put("difference", postBill.opt("difference"));
		data.put("sale_mode", postBill.optString("sale_mode"));
		// data.put("isprint_bill", isPrintBill);
		// item

		JSONObject item = paymentDao.getBillPaymentByJzid(tenancyId, storeId, billNum, paymentId);
		
		Double buyerPayAmount = 0d;
		if (queryResultJson.containsKey("buyer_pay_amount") && Tools.hv(queryResultJson.optString("buyer_pay_amount")))
		{
			buyerPayAmount = ParamUtil.getDoubleValueByObject(queryResultJson, "buyer_pay_amount");
		}else 
		{
			buyerPayAmount = ParamUtil.getDoubleValueByObject(item, "amount");
		}

		Double receiptAmount = 0d;
		if (queryResultJson.containsKey("receipt_amount") && Tools.hv(queryResultJson.optString("receipt_amount")))
		{
			receiptAmount = ParamUtil.getDoubleValueByObject(queryResultJson, "receipt_amount");
		}else 
		{
			receiptAmount = ParamUtil.getDoubleValueByObject(item, "amount");
		}
		
		item.put("pay_type", paymentType);
		item.put("bill_code", transactionNo);
		item.put("consume_credit", 0);
		item.put("consume_creditmoney", 0);
		item.put("integraloffset", 0);
		item.put("buyer_pay_amount", buyerPayAmount);
		item.put("receipt_amount", receiptAmount);
		
		JSONArray items = new JSONArray();
		items.add(item);
		data.put("item", items);

		// 是否打印结账单
		JSONObject paymentParamJson = JSONObject.fromObject(item.optString("param_cach"));
		if (null != paymentParamJson && paymentParamJson.containsKey("isprint_bill"))
		{
			isPrintBill = paymentParamJson.optString("isprint_bill");
		}
		data.put("isprint_bill", isPrintBill);

		// 设备类型
        String source = "";
        if (null != paymentParamJson && paymentParamJson.containsKey("source"))
        {
            source = paymentParamJson.optString("source");
        }
        data.put("source", source);

		List<JSONObject> datas = new ArrayList<JSONObject>();
		datas.add(data);

		Data retData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
		retData.setType(Type.BILL_PAYMENT);
		retData.setData(datas);

		return retData;
	}

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param thirdPayment
	 * @param resultData
	 * @throws Exception
	 */
	private void queryThirdPaymentResultForCardRecharge(String tenancyId, int storeId, PosThirdPaymentOrderEntity thirdPayment, Data resultData) throws Exception
	{
		// 查询会员操作流水表
		String thirdBillCode = thirdPayment.getOrder_num();
		synchronized (thirdBillCode.intern())
		{
			JSONObject queryParamJson = new JSONObject();
			queryParamJson.put("third_bill_code", thirdBillCode);
			PosCustomerOperateListEntity cardRecharge = cardRechargeService.queryCustomerCardRecharge(tenancyId, storeId, queryParamJson);
	
			if (null != cardRecharge)
			{
				if (SysDictionary.CUSTOMER_OPERATE_STATE_SUCCESS.equals(cardRecharge.getOperate_state()))
				{
					// 充值已经完成;
					return;
				}
	
				String paymentState = SysDictionary.THIRD_PAY_STATUS_NOOK;
				String operateState = SysDictionary.CUSTOMER_OPERATE_STATE_WAIT;
				String requestStatus = null;
				Integer requestCode = null;
				String requestMsg = null;
				Timestamp finishTime = null;
	
				if (Constant.CODE_SUCCESS == resultData.getCode())
				{
					requestStatus = SysDictionary.REQUEST_STATUS_COMPLETE;
	
					List<?> returnDataList = resultData.getData();
					JSONObject returnJson = new JSONObject();
					if (null != returnDataList && returnDataList.size() > 0)
					{
						returnJson = JSONObject.fromObject(returnDataList.get(0));
						paymentState = returnJson.optString("payment_state");
					}
					// 判断充值或反充值
					if (SysDictionary.OPERAT_TYPE_CZ.equals(cardRecharge.getOperat_type()))
					{
						// 充值,判断付款状态
						switch (paymentState)
						{
							case SysDictionary.THIRD_PAY_STATUS_SUCCESS:
								String customerType = customerDao.getCustomerType(tenancyId, storeId);
								// 成功,调用充值接口
								operateState = SysDictionary.CUSTOMER_OPERATE_STATE_PROCESS;
								
								JSONObject paramJson = new JSONObject();
								paramJson.put("card_code", cardRecharge.getCard_code());
								paramJson.put("third_code", cardRecharge.getThird_code());
								paramJson.put("income", thirdPayment.getSettle_amount());
								paramJson.put("payment", thirdPayment.getPayment_id());
								paramJson.put("salesman", cardRecharge.getSales_person());
								paramJson.put("pay_no", thirdPayment.getTransaction_no());
								paramJson.put("report_date", DateUtil.formatDate(cardRecharge.getBusiness_date()));
								paramJson.put("shift_id", cardRecharge.getShift_id());
								paramJson.put("chanel", cardRecharge.getChanel());
								paramJson.put("opt_num", cardRecharge.getOperator_id());
								paramJson.put("pos_num", cardRecharge.getPos_num());
								paramJson.put("is_invoice", cardRecharge.getIs_invoice());
								
								JSONObject extJson = JSONObject.fromObject(cardRecharge.getExtend_param());
								paramJson.put("reward_money", ParamUtil.getDoubleValueByObject(extJson, "reward_money"));
								paramJson.put("auto_reward_money", ParamUtil.getStringValueByObject(extJson, "auto_reward_money"));
								Data rechargeData = new Data();
								if(SysDictionary.CUSTOMER_TYPE_ACEWILL.equals(customerType)){
									//微生活提交储值
									rechargeData = aChargeService.commitAcewillCharge(tenancyId, storeId, thirdBillCode, paramJson);
									
									if (Constant.CODE_SUCCESS == rechargeData.getCode())
									{
										operateState = SysDictionary.CUSTOMER_OPERATE_STATE_SUCCESS;
										finishTime = DateUtil.currentTimestamp();

										// 打印小票
										JSONObject rechargeResultJson = JSONObject.fromObject(rechargeData.getData().get(0));
										if (Tools.hv(rechargeResultJson)) {
											Data customerUserInfo = acewillService.getAcewillCustomerUserInfo(rechargeData);
											if(Constant.CODE_SUCCESS == customerUserInfo.getCode()){
												JSONObject userInfo = JSONObject.fromObject(customerUserInfo.getData().get(0));
												rechargeResultJson.put("useful_credit",userInfo.optInt("credit")); //充值后积分
												rechargeResultJson.put("before_credit",(userInfo.optInt("credit") - rechargeResultJson.optInt("reward_credit")));//充值后前积分
												rechargeResultJson.put("main_balance",userInfo.optDouble("balance"));//充值后金额
												rechargeResultJson.put("before_balance",userInfo.optDouble("balance")  - rechargeResultJson.optDouble("charge_total"));//充值前金额
												rechargeResultJson.put("level_name",userInfo.optString("level_name"));
												rechargeResultJson.put("card_class_name",userInfo.optString("type_name"));

											}
										}

										rechargeResultJson.put("is_invoice", ParamUtil.getStringValueByObject(paramJson, "is_invoice"));
										rechargeResultJson.put("report_date", ParamUtil.getStringValueByObject(paramJson, "report_date"));
										aChargeService.printForCustomerCardRecharge(tenancyId, storeId, rechargeResultJson);
									}
								}
								else
								{
									rechargeData= cardRechargeService.customerCardRechargeManage(tenancyId, storeId, thirdBillCode, paramJson,cardRecharge,Oper.add);
									
									if (Constant.CODE_SUCCESS == rechargeData.getCode())
									{
										operateState = SysDictionary.CUSTOMER_OPERATE_STATE_SUCCESS;
										finishTime = DateUtil.currentTimestamp();

										// 打印小票
										JSONObject rechargeResultJson = JSONObject.fromObject(rechargeData.getData().get(0));
										rechargeResultJson.put("is_invoice", ParamUtil.getStringValueByObject(paramJson, "is_invoice"));
										rechargeResultJson.put("report_date", ParamUtil.getStringValueByObject(paramJson, "report_date"));
										cardRechargeService.printForCustomerCardRecharge(tenancyId, storeId, rechargeResultJson);
									}
								}

								break;
							case SysDictionary.THIRD_PAY_STATUS_FAIL:
								// 失败,修改状态
								operateState = SysDictionary.CUSTOMER_OPERATE_STATE_FAIL;
								break;
	
							case SysDictionary.THIRD_PAY_STATUS_PAYING:
								// 支付中,修改状态
								operateState = SysDictionary.CUSTOMER_OPERATE_STATE_WAIT;
								break;
	
							case SysDictionary.THIRD_PAY_STATUS_NOOK:
								// 网络异常,修改状态
								operateState = SysDictionary.CUSTOMER_OPERATE_STATE_WAIT;
								break;
	
							case SysDictionary.THIRD_PAY_STATUS_REFUND_SUCCESS:
							case SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL:
							case SysDictionary.THIRD_PAY_STATUS_REVOKED_SUCCESS:
							case SysDictionary.THIRD_PAY_STATUS_REVOKED_FAIL:
							case SysDictionary.THIRD_PAY_STATUS_REFUND:
							case SysDictionary.THIRD_PAY_STATUS_REVOKED:
								operateState = SysDictionary.CUSTOMER_OPERATE_STATE_FAIL;
								break;
	
							default:
								break;
						}
					}
					else if (SysDictionary.OPERAT_TYPE_FCZ.equals(cardRecharge.getOperat_type()))
					{
						// 反充值,修改状态
						operateState = cardRecharge.getOperate_state();
					}
				}
				else
				{
					requestStatus = SysDictionary.REQUEST_STATUS_FAILURE;
					requestCode = resultData.getCode();
					requestMsg = resultData.getMsg();
				}
	
				// 修改状态
				JSONObject updateJson = new JSONObject();
				updateJson.put("bill_code", thirdBillCode);
				updateJson.put("payment_state", paymentState);
				updateJson.put("operate_state", operateState);
				updateJson.put("request_status", requestStatus);
				updateJson.put("request_code", requestCode);
				updateJson.put("request_msg", requestMsg);
				updateJson.put("finish_time", DateUtil.format(finishTime));
				updateJson.put("operat_type", cardRecharge.getOperat_type());
	
				cardRechargeService.updateCustomerOperateListStateForCardRecharge(tenancyId,storeId, updateJson);
			}
		}
	}

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param thirdPayment
	 * @param resultData
	 */
	private void queryThirdPaymentResultForCardActivation(String tenancyId, int storeId, PosThirdPaymentOrderEntity thirdPayment, Data resultData)
	{
		try
		{
			JSONObject queryParamJson = new JSONObject();
			queryParamJson.put("bill_code", thirdPayment.getOrder_num());
			PosCustomerOperateListEntity customerOperate = customerCardActivationService.queryCustomerOperateListForCardActivation(tenancyId, storeId, queryParamJson);

			if (null != customerOperate)
			{
				String paymentState = SysDictionary.THIRD_PAY_STATUS_NOOK;
				String operateState = SysDictionary.CUSTOMER_OPERATE_STATE_WAIT;
				String requestStatus = null;
				Integer requestCode = null;
				String requestMsg = null;
				Timestamp finishTime = null;

				if (SysDictionary.CUSTOMER_OPERATE_STATE_SUCCESS.equals(customerOperate.getOperate_state()))
				{
					// 发卡已经完成;
					return;
				}

				if (Constant.CODE_SUCCESS == resultData.getCode())
				{
					requestStatus = SysDictionary.REQUEST_STATUS_COMPLETE;

					List<?> returnDataList = resultData.getData();
					JSONObject returnJson = new JSONObject();
					if (null != returnDataList && returnDataList.size() > 0)
					{
						returnJson = JSONObject.fromObject(returnDataList.get(0));
						paymentState = returnJson.optString("payment_state");
					}

					if (SysDictionary.THIRD_PAY_CHARGE.equals(thirdPayment.getObject_name()))
					{
						switch (paymentState)
						{
							case SysDictionary.THIRD_PAY_STATUS_SUCCESS:

								operateState = SysDictionary.CUSTOMER_OPERATE_STATE_PROCESS;

								JSONObject paymentJson = new JSONObject();
								paymentJson.put("payment_id", thirdPayment.getPayment_id().toString());
								paymentJson.put("amount", thirdPayment.getSettle_amount().toString());
								paymentJson.put("cashier_num", thirdPayment.getOpt_num().toString());
								paymentJson.put("rate", "1");

								paymentJson.put("bill_code", thirdPayment.getOrder_num());

								List<JSONObject> paymentList = new ArrayList<JSONObject>();
								paymentList.add(paymentJson);

								JSONObject extJson = JSONObject.fromObject(customerOperate.getExtend_param());

								JSONObject paramJson = new JSONObject();
								paramJson.put("card_code", customerOperate.getCard_code());
								paramJson.put("third_code", customerOperate.getThird_code());
								paramJson.put("customer_code", customerOperate.getCustomer_code());
								paramJson.put("card_class_id", customerOperate.getCard_class_id());
								paramJson.put("pay_password", extJson.optString("pay_password"));
								paramJson.put("salesman", customerOperate.getSales_person());
								paramJson.put("deposit", customerOperate.getDeposit());
								paramJson.put("sales_price", customerOperate.getSales_price());
								paramJson.put("is_only", extJson.optString("is_only"));
								paramJson.put("is_physical_card", extJson.optString("is_physical_card"));
								paramJson.put("report_date", DateUtil.formatDate(customerOperate.getBusiness_date()));
								paramJson.put("shift_id", customerOperate.getShift_id());
								paramJson.put("chanel", customerOperate.getChanel());
								paramJson.put("opt_num", customerOperate.getOperator_id());
								paramJson.put("pos_num", customerOperate.getPos_num());
								paramJson.put("payment", paymentList);

								Data returnData = customerCardActivationService.customerCardActivationPost(tenancyId, storeId, paramJson);

								if (Constant.CODE_SUCCESS == returnData.getCode())
								{
									operateState = SysDictionary.CUSTOMER_OPERATE_STATE_SUCCESS;
									finishTime = DateUtil.currentTimestamp();
								}else if(returnData.getCode()==5024){
									requestStatus = SysDictionary.REQUEST_STATUS_FAILURE;
									operateState = SysDictionary.CUSTOMER_OPERATE_STATE_FAIL;
									finishTime = DateUtil.currentTimestamp();
									requestMsg = returnData.getMsg();
								}

								break;
							case SysDictionary.THIRD_PAY_STATUS_FAIL:
								//
								operateState = SysDictionary.CUSTOMER_OPERATE_STATE_FAIL;

								break;
							case SysDictionary.THIRD_PAY_STATUS_PAYING:
								// 返回待支付
								operateState = SysDictionary.CUSTOMER_OPERATE_STATE_WAIT;
								break;
							case SysDictionary.THIRD_PAY_STATUS_REFUND_SUCCESS:
							case SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL:
							case SysDictionary.THIRD_PAY_STATUS_REVOKED_SUCCESS:
							case SysDictionary.THIRD_PAY_STATUS_REVOKED_FAIL:
							case SysDictionary.THIRD_PAY_STATUS_REFUND:
							case SysDictionary.THIRD_PAY_STATUS_REVOKED:
								operateState = SysDictionary.CUSTOMER_OPERATE_STATE_FAIL;
								break;
							default:

								break;
						}
					}
				}
				else
				{
					requestStatus = SysDictionary.REQUEST_STATUS_FAILURE;
					requestCode = resultData.getCode();
					
					requestMsg = resultData.getMsg();
				}

				JSONObject updateJson = new JSONObject();
				updateJson.put("bill_code", customerOperate.getThird_bill_code());
				updateJson.put("payment_state", paymentState);
				updateJson.put("operate_state", operateState);
				updateJson.put("request_status", requestStatus);
				updateJson.put("request_code", requestCode);
				updateJson.put("request_msg", requestMsg);
				updateJson.put("finish_time", DateUtil.format(finishTime));

				customerCardActivationService.updateCustomerOperateListStateForCardActivation(tenancyId, storeId, updateJson);
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
	}
}
