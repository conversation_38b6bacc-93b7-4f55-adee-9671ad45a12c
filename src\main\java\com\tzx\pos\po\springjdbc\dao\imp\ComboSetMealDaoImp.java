package com.tzx.pos.po.springjdbc.dao.imp;

import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.dao.imp.BaseDaoImp;
import com.tzx.pos.bo.dto.PosBill;
import com.tzx.pos.bo.dto.PosBillItem;
import com.tzx.pos.po.springjdbc.dao.ComboSetMealDao;
import net.sf.json.JSONArray;

import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by kevin on 2017-11-06.
 */
@Repository(ComboSetMealDao.NAME)
public class ComboSetMealDaoImp extends BaseDaoImp implements ComboSetMealDao {

    private static final Logger logger = Logger.getLogger(ComboSetMealDaoImp.class);

    /** 查询组合套餐主项菜品
     * @param rwid
     * @throws Exception
     */
    @Override
    public PosBillItem getPosBillItemByRWID(Integer rwid) throws Exception {
        StringBuffer setmealStringBuffer = new StringBuffer("select item_id,item_price,item_count,item_amount,item_serial,combo_prop,discount_mode_id, ");
        setmealStringBuffer.append(" store_id,tenancy_id, bill_num,bill_num,discount_rate,discount_amount,discountr_amount ");
        setmealStringBuffer.append(" from pos_bill_item where rwid = ? ");
        List<PosBillItem> posBillItemList =  (List<PosBillItem>)this.query(setmealStringBuffer.toString(), new Object[] {rwid}, BeanPropertyRowMapper.newInstance(PosBillItem.class));
        if(!posBillItemList.isEmpty()) {
            return posBillItemList.get(0);
        }
        return null;
    }

    @SuppressWarnings("unchecked")
    @Override
    public PosBillItem getPosBillItemByRWID(String tenancyId, Integer stroeId, String billNum, Integer rwid) throws Exception
    {
        StringBuffer setmealStringBuffer = new StringBuffer("");
        setmealStringBuffer.append(" select * from pos_bill_item where tenancy_id = ? and store_id = ? and bill_num = ? and rwid = ?");
        List<PosBillItem> posBillItemList = (List<PosBillItem>) this.query(setmealStringBuffer.toString(), new Object[]
                { tenancyId, stroeId, billNum, rwid }, BeanPropertyRowMapper.newInstance(PosBillItem.class));
        if (!posBillItemList.isEmpty())
        {
            return posBillItemList.get(0);
        }
        return null;
    }

    /**
     * 查询组合套餐明细集合
     * @param rwid
     * @return
     * @throws Exception
     */
    @Override
    public List<PosBillItem> getPosBillItemListByRWIDS(String rwid) throws Exception {
        StringBuffer meallistStringBuffer = new StringBuffer("select rwid,item_id,item_price,item_count,item_amount,item_serial,combo_prop,discount_mode_id,method_money, ");
        meallistStringBuffer.append(" discount_amount,discountr_amount from pos_bill_item where rwid in (" + rwid +  ") and item_property = '"  + SysDictionary.ITEM_PROPERTY_SINGLE +  "'");
        return (List<PosBillItem>)this.query(meallistStringBuffer.toString(), new Object[] {}, BeanPropertyRowMapper.newInstance(PosBillItem.class));
    }

    @Override
    public List<JSONObject> getPosBillItemListByRWIDS(String tenancyId, Integer stroeId, String billNum, String rwid) throws Exception
    {
        StringBuffer meallistStringBuffer = new StringBuffer("");
        meallistStringBuffer.append(" select * from pos_bill_item where tenancy_id = ? and store_id = ? and bill_num = ? and rwid in (" + rwid + ") and item_property = '" + SysDictionary.ITEM_PROPERTY_SINGLE + "'");
        return this.query4Json(tenancyId, meallistStringBuffer.toString(), new Object[]
                { tenancyId, stroeId, billNum });
    }

    @Override
    public void updatePosBillItem4Setmeal(List<PosBillItem> setmealItemList) throws Exception {
        if(!setmealItemList.isEmpty()) {
            List<Object[]> singlebatchArgs = new ArrayList<Object[]>();
            List<Object[]> batchArgs = new ArrayList<Object[]>();
            for (PosBillItem posBillItem: setmealItemList) {
                //处理单品折扣明细项不可以打折，加入组合套餐后， 去折扣、折让，变为正常的菜品加入组全套餐
                int discountModeId = Integer.parseInt(posBillItem.getDiscount_mode_id() == null ? "0" : posBillItem.getDiscount_mode_id());
                if (discountModeId == SysDictionary.DISCOUNT_MODE_10) {
                    Object[] obj = new Object[]{Double.parseDouble(posBillItem.getItem_price()), Double.parseDouble(posBillItem.getItem_amount()),
                            posBillItem.getItem_property(), Integer.parseInt(posBillItem.getSetmeal_id()), Integer.parseInt(posBillItem.getItem_serial()),
                            Double.parseDouble(posBillItem.getMethod_money()),
                            Integer.parseInt(posBillItem.getCombo_prop()), 0, "Y", 100, null, null, Integer.parseInt(posBillItem.getRwid())};
                    singlebatchArgs.add(obj);
                } else {
                    Object[] objs = new Object[]{Double.parseDouble(posBillItem.getItem_price()), Double.parseDouble(posBillItem.getItem_amount()),
                            posBillItem.getItem_property(), Integer.parseInt(posBillItem.getSetmeal_id()), Integer.parseInt(posBillItem.getItem_serial()),
                            Integer.parseInt(posBillItem.getCombo_prop()), Double.parseDouble(posBillItem.getDiscount_amount()),
                            Double.parseDouble(posBillItem.getDiscountr_amount()), Double.parseDouble(posBillItem.getMethod_money()), Integer.parseInt(posBillItem.getRwid())};
                    batchArgs.add(objs);
                }
            }
            if(singlebatchArgs.size() > 0) {
                StringBuffer updateItemSingleSql = new StringBuffer("update pos_bill_item set item_price = ?, item_amount= ?, item_property = ?,");
                updateItemSingleSql.append("setmeal_id = ?, item_serial = ?, method_money = ?, combo_prop = ?,discount_amount = ?, discount_state = ?, ");
                updateItemSingleSql.append("discount_rate = ?, discount_mode_id = ?, discount_reason_id = ? where rwid = ?");
                this.batchUpdate(updateItemSingleSql.toString(), singlebatchArgs);
            }
            if(batchArgs.size() > 0) {
                StringBuffer updateItemSql = new StringBuffer("update pos_bill_item set item_price = ?, item_amount= ?, item_property = ?,");
                updateItemSql.append("setmeal_id = ?, item_serial = ?, combo_prop = ?,discount_amount = ?, discountr_amount = ?,method_money = ?,");
                updateItemSql.append("discount_rate = 100  where rwid = ?");
                this.batchUpdate(updateItemSql.toString(), batchArgs);
            }
            logger.info("updatePosBillItem:success");
        }
    }

    @Override
    public void updatePosBillItem4Setmeal(String tenancyId, Integer stroeId, String billNum,List<PosBillItem> setmealItemList) throws Exception
	{
		List<Object[]> batchArgs = new ArrayList<Object[]>();
		if (!setmealItemList.isEmpty())
		{
			for (PosBillItem posBillItem : setmealItemList)
			{
				Double originItemPrice = Double.parseDouble(posBillItem.getOrigin_item_price());
				if (originItemPrice.isNaN())
				{
					originItemPrice = 0d;
				}
				batchArgs.add(new Object[]
				{ posBillItem.getItem_property(), Integer.parseInt(posBillItem.getItem_serial()), Integer.parseInt(posBillItem.getSetmeal_rwid()), Integer.parseInt(posBillItem.getSetmeal_id()), Integer.parseInt(posBillItem.getAssist_item_id()), Integer.parseInt(posBillItem.getCombo_prop()),
						originItemPrice, posBillItem.getItem_name(), tenancyId, stroeId, billNum, Integer.parseInt(posBillItem.getRwid()) });
			}
		}
		if (batchArgs.size() > 0)
		{
			StringBuffer updateItemSql = new StringBuffer("update pos_bill_item set item_property=?,item_serial=?,setmeal_rwid=?,setmeal_id=?,assist_item_id=?,combo_prop = ?,origin_item_price = ?,item_name = ? ");
			updateItemSql.append(" where tenancy_id=? and store_id=? and bill_num=? and rwid=?");
			this.batchUpdate(updateItemSql.toString(), batchArgs);
		}
	}

    @Override
    public double getMethodAmountByRWID(String tenancyId, Integer stroeId, String billNum) throws Exception {
        double methodAmount = 0d;
        StringBuilder methodAmountSql = new StringBuilder("select coalesce(sum(pzi.amount),0) as method_amount,bill_num from pos_zfkw_item pzi ");
                            methodAmountSql.append("where pzi.bill_num = ? and pzi.tenancy_id=? and pzi.store_id = ? group by bill_num");
        SqlRowSet rs = jdbcTemplate.queryForRowSet(methodAmountSql.toString(), new Object[]
                { billNum, tenancyId, stroeId});
        if (rs.next())
        {
            methodAmount = rs.getDouble("method_amount");
        }
        return methodAmount;
    }

    @Override
    public void updatePosBill(PosBillItem posBillItem) throws Exception {
        StringBuffer updatePosBillSql = new StringBuffer("update pos_bill set discountr_amount = ?, discount_rate = ? where tenancy_id = ? and store_id = ? and bill_num = ?");
        Object[] object = new Object[] {Double.parseDouble(posBillItem.getDiscountr_amount()), Double.parseDouble(posBillItem.getDiscount_rate()),
                posBillItem.getTenancy_id(), Integer.parseInt(posBillItem.getStore_id()), posBillItem.getBill_num()};
        this.update(updatePosBillSql.toString(), object);
    }

    @Override
    public void revertPosBillItemByRWIDS(String rwid, Integer comboSetId) throws Exception {
        //组合套餐明细的数量
        int meallistCount = 0;
        StringBuffer meallistSql = new StringBuffer("select count(a.id) as meallist_count from pos_bill_item a, ");
                                                meallistSql.append("(select item_id,bill_num from pos_bill_item where rwid = " + comboSetId);
                                                meallistSql.append(" and combo_prop > 0 ) b where a.setmeal_id = b.item_id and a.bill_num = b.bill_num");
                                                meallistSql.append(" and a.item_property = '"  + SysDictionary.ITEM_PROPERTY_MEALLIST +  "'");
        SqlRowSet rs = jdbcTemplate.queryForRowSet(meallistSql.toString());
        if (rs.next())
        {
            meallistCount = rs.getInt("meallist_count");
        }
        if(rwid.indexOf(",") != -1) {
            String[] rwids = rwid.split(",");
            //删除所有组合套餐
            if(meallistCount == rwids.length) {

            }else {
                StringBuffer meallistStringBuffer = new StringBuffer("select rwid,item_id,item_price,item_count,item_amount,item_serial,combo_prop,discount_mode_id, ");
                meallistStringBuffer.append(" discount_amount,discountr_amount from pos_bill_item where rwid in (" + rwid +  ") and item_property = '"  + SysDictionary.ITEM_PROPERTY_MEALLIST +  "'");

            }
        }else {

        }
    }

    @Override
    public PosBillItem getPosBillItemByRWID(String tenancyId, int storeId, int rwid)throws Exception {
        String sql =" select * from pos_bill_item_combo where tenancy_id=? and store_id=? and rwid =? ";
        List<PosBillItem> posBillItemList =  (List<PosBillItem>)this.query(sql, new Object[] {tenancyId,storeId,rwid}, BeanPropertyRowMapper.newInstance(PosBillItem.class));
        if(!posBillItemList.isEmpty()) {
            return posBillItemList.get(0);
        }
        return null;
    }

    @Override
    public List<JSONObject> getPosBillItemByBillNum(String tenancyId, int storeId, String billNum)throws Exception {
        String sql =" select * from pos_bill_item_combo where tenancy_id=? and store_id=? and bill_num=?  and default_state = 'Y'  order by item_serial,rwid";
        return this.query4Json(tenancyId, sql, new Object[]{tenancyId,storeId,billNum});
    }

    @Override
    public List<JSONObject> getPosBillItemByBillnum(String tenancyId, int storeId, String billNum) throws Exception
    {
        String sql = " select * from pos_bill_item where tenancy_id=? and store_id=? and bill_num=? order by item_serial,rwid";
        return this.query4Json(tenancyId, sql, new Object[]
                { tenancyId, storeId, billNum });
    }

    @Override
    public List<JSONObject> getAllPosBillItemByBillNum(String tenancyId, int storeId, String billNum)throws Exception {
        String sql =" select * from pos_bill_item_combo where tenancy_id=? and store_id=? and bill_num=? order by item_serial,rwid";
        return this.query4Json(tenancyId, sql, new Object[]{tenancyId,storeId,billNum});
    }

    @Override
    public void deletePosKwzfItemByRWID(String tenancyId, int storeId, int rwid) throws Exception {
        StringBuilder deletePosKwzfItemSql = new StringBuilder("delete from pos_zfkw_item pzi ");
        deletePosKwzfItemSql.append("where pzi.rwid = ? and pzi.tenancy_id=? and pzi.store_id = ?");
        this.update(deletePosKwzfItemSql.toString(), new Object[]{rwid, tenancyId, storeId});
    }

    @Override
    public double getBillDiscountrByBillNum(String tenancyId, Integer storeId, String billNum) throws Exception {
        double discountrAmount = 0d;
        StringBuilder discountrAmountSql = new StringBuilder("select coalesce(pb.discountr_amount,0) as discountr_amount from pos_bill pb ");
        discountrAmountSql.append("where pb.bill_num = ? and pb.tenancy_id=? and pb.store_id = ?");
        SqlRowSet rs = jdbcTemplate.queryForRowSet(discountrAmountSql.toString(), new Object[]
                { billNum, tenancyId, storeId});
        if (rs.next())
        {
            discountrAmount = rs.getDouble("discountr_amount");
        }
        return discountrAmount;
    }

    @Override
    public JSONObject getHqItemUnit(String tenancyId, Integer itemUnitId, Integer itemId) throws Exception {
        String sql =" select * from hq_item_unit where tenancy_id=? and id=? and item_id = ? ";
        List<JSONObject> jsonObjectList = this.query4Json(tenancyId, sql, new Object[]{tenancyId,itemUnitId, itemId});
        if(jsonObjectList.size() > 0) {
            return jsonObjectList.get(0);
        }
        return null;
    }

    @Override
    public int selectMaxItemSerial(String billNum, int storeId, String tenancyId)
            throws Exception {
        StringBuilder maxSerialSql = new StringBuilder(
                "select coalesce(max(combo_prop), 0) from pos_bill_item i where i.bill_num = ? and i.store_id = ? and i.tenancy_id = ?");

        return this.queryForInt(maxSerialSql.toString(), new Object[]{billNum, storeId, tenancyId});
    }



	@Override
	public List<JSONObject> getPosBillItemComboByItemList(String tenancyId, int storeId, List<Integer> rwidList) throws Exception
	{
		StringBuffer querySql = new StringBuffer("select * from pos_bill_item_combo where tenancy_id=? and store_id=?");
		if (null != rwidList && rwidList.size() > 0)
		{
			querySql.append(" and rwid in(").append(StringUtils.join(rwidList, ",")).append(")");

			return this.query4Json(tenancyId, querySql.toString(), new Object[]
			{ tenancyId, storeId });
		}
		return null;
	}

	@Override
	public void updatePosBillItemForSingle(String tenancyId, Integer stroeId, String billNum, List<PosBillItem> setmealItemList) throws Exception
	{
		List<Object[]> batchArgs = new ArrayList<Object[]>();
		if (!setmealItemList.isEmpty())
		{
			for (PosBillItem posBillItem : setmealItemList)
			{
				batchArgs.add(new Object[]
				{ posBillItem.getItem_property(), Integer.parseInt(posBillItem.getItem_serial()), Integer.parseInt(posBillItem.getSetmeal_rwid()), Integer.parseInt(posBillItem.getSetmeal_id()), Integer.parseInt(posBillItem.getAssist_item_id()), Integer.parseInt(posBillItem.getCombo_prop()),
						posBillItem.getItem_name(), Double.parseDouble(posBillItem.getItem_price()),Double.parseDouble(posBillItem.getSingle_amount()), tenancyId, stroeId, billNum, Integer.parseInt(posBillItem.getRwid()) });
			}
		}
		if (batchArgs.size() > 0)
		{
			StringBuffer updateItemSql = new StringBuffer("update pos_bill_item set item_property=?,item_serial=?,setmeal_rwid=?,setmeal_id=?,assist_item_id=?,combo_prop = ?,item_name = ?,item_price=?,single_amount=? ");
			updateItemSql.append(" where tenancy_id=? and store_id=? and bill_num=? and rwid=?");
			this.batchUpdate(updateItemSql.toString(), batchArgs);
		}
	}



}
