package com.tzx.pos.bo.payment.imp.ext;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Service;

import com.tzx.base.entity.PosBillMember;
import com.tzx.base.entity.PosThirdPaymentRefundEntity;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.CrmErrorCode;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.PropertiesLoader;
import com.tzx.framework.common.util.Tools;
import com.tzx.member.crm.bo.CustomerCardConsumeService;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.bo.payment.PaymentWayService;
import com.tzx.pos.bo.payment.imp.PaymentWayServiceImp;
import com.tzx.pos.po.springjdbc.dao.PosBillMemberDao;
import com.tzx.thirdpay.bo.ThirdPaymentRefundService;

/**
 * 本系统卡
 *
 */
@Service("com.tzx.pos.bo.payment.imp.ext.CardPaymentWayServiceImp")
public class CardPaymentWayServiceImp extends PaymentWayServiceImp implements PaymentWayService
{
	@Resource(name = CustomerCardConsumeService.NAME)
	private CustomerCardConsumeService	customerService;
	
	@Resource(name = ThirdPaymentRefundService.NAME)
	private ThirdPaymentRefundService		thirdPaymentRefundService;
	
	@Resource(name = PosBillMemberDao.NAME)
	private PosBillMemberDao			memberDao;
	
//	@Resource(name = PosPrintNewService.NAME)
//	private PosPrintNewService posPrintNewService;

	@Override
	public void payment(String tenantId, int storeId, String billNum,String batchNum, Date reportDate, int shiftId, String posNum, String optNum, String tableCode, JSONObject payment, Timestamp currentTime,String isOnlinePay) throws Exception
	{
//		String chanel = payment.optString("chanel");

		double amount = payment.optDouble("amount");
		double count = payment.optDouble("count");
		double currencyAmount = payment.optDouble("currency_amount");
		String number = payment.optString("number");
		String phone = payment.optString("phone");
		String billCode = payment.optString("bill_code");

		int jzid = payment.optInt("jzid");
		String paymentClass = payment.optString("payment_class");
		String paymentName = payment.optString("payment_name");
		String nameEnglish = payment.optString("payment_english_name");
		double rate = payment.optDouble("rate");
		String remark = payment.optString("remark");
		String isYsk = payment.optString("is_ysk");
		if (Tools.isNullOrEmpty(isYsk))
		{
			isYsk = "N";
		}
		
		String mobil = ParamUtil.getStringValueByObject(payment, "mobil");
		String customerCode = ParamUtil.getStringValueByObject(payment, "customer_code");
		String customerName = ParamUtil.getStringValueByObject(payment, "customer_name");
		String cardCode = ParamUtil.getStringValueByObject(payment, "card_code");
		Double customerCredit = ParamUtil.getDoubleValueByObject(payment, "customer_credit");
		Double mainBalance = ParamUtil.getDoubleValueByObject(payment, "main_balance");
		Double rewardBalance = ParamUtil.getDoubleValueByObject(payment, "reward_balance");

		if(Tools.isNullOrEmpty(mobil))
		{
			mobil = phone;
		}
		if(Tools.isNullOrEmpty(cardCode))
		{
			cardCode = number;
		}
		if(Tools.isNullOrEmpty(customerCode))
		{
			customerCode = remark;
		}
		
		 // 只能使用一次会员卡结账，请使用其它付款方式结账
		List<JSONObject> paymentWays = paymentDao.getPosBillPaymentByIDAndClass(tenantId, storeId, jzid, paymentClass, billNum);
		if (paymentWays.size() > 0)
		{
			throw SystemException.getInstance(PosErrorCode.ONLY_CARD_PAYMENT);
		}
		
//		List<JSONObject> memberList = memberDao.queryPosBillMember(tenantId, storeId, billNum, SysDictionary.BILL_MEMBERCARD_CZXF03);
//		if(null == memberList || memberList.size()==0)
//		{
		memberDao.deletePosBillMember(tenantId, storeId, billNum, SysDictionary.BILL_MEMBERCARD_CZXF03);
		
		PosBillMember billMember = new PosBillMember();
		billMember.setBill_num(billNum);
		billMember.setReport_date(reportDate);
		billMember.setType(SysDictionary.BILL_MEMBERCARD_CZXF03);
		billMember.setCard_code(cardCode);
		billMember.setMobil(mobil);
		billMember.setCustomer_code(customerCode);
		billMember.setCustomer_name(customerName);
		billMember.setAmount(currencyAmount);
		billMember.setCredit(0d);
		billMember.setConsume_after_credit(customerCredit);
		billMember.setConsume_before_credit(customerCredit);
		billMember.setConsume_after_main_balance(mainBalance);
		billMember.setConsume_before_main_balance(mainBalance);
		billMember.setConsume_after_reward_balance(rewardBalance);
		billMember.setConsume_before_reward_balance(rewardBalance);
		billMember.setLast_updatetime(currentTime);
		memberDao.insertPosBillMember(tenantId, storeId, billMember);
//		}
		
		if ("1".equalsIgnoreCase(isOnlinePay))
		{
			paymentDao.insertPosBillPayment(tenantId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, nameEnglish, rate, amount, count, currencyAmount, number, phone, isYsk, null, billCode, remark, currentTime,
					SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);

//			paymentDao.insertPosBillPaymentLog(tenantId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, nameEnglish, rate, amount, count, currencyAmount, number, phone, isYsk, null, billCode, remark, currentTime,
//					SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);
			
//			if(null == memberList || memberList.size()==0)
//			{
			memberDao.updatePosBillMember(tenantId, storeId, billNum, SysDictionary.BILL_MEMBERCARD_CZXF03, currencyAmount, 0d, billCode, SysDictionary.REQUEST_STATUS_COMPLETE, customerName, customerCredit, customerCredit);
//			}
		}
		else
		{
			payment.put("pay_state", SysDictionary.PAYMENT_STATE_NOTPAY);
			paymentDao.insertPosBillPayment(tenantId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, nameEnglish, rate, amount, count, currencyAmount, number, phone, isYsk, null, billCode, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY,
					batchNum, payment.toString(), 0d);
		}
	}

	@Override
	public void paymentDebit(String tenancyId, int storeId, String billNum,String orderNum,String chanel, Double paymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData) throws Exception
	{
//		String currentTimeStr = DateUtil.format(currentTime);

		JSONObject paramCachJson = JSONObject.fromObject(payment.optString("param_cach"));

		int payId = payment.optInt("id");
		String posNum = payment.optString("pos_num");
		String optNum = payment.optString("cashier_num");
//		String optName = paymentDao.getEmpNameById(optNum, tenancyId, storeId);
		
		String reportDate = payment.optString("report_date");
		Integer shiftId = payment.optInt("shift_id");
		String number = payment.optString("number");
//		String phone = payment.optString("phone");
//		String remark = payment.optString("remark");

		JSONObject requestJson = new JSONObject();
		requestJson.put("card_code", number);
		requestJson.put("third_code", paramCachJson.optString("third_code"));
		requestJson.put("cardpassword", paramCachJson.optString("password"));
		if (Tools.hv(number) && number.length() > 3 && "+++".equals(number.substring(0, 3)))
		{
			requestJson.put("dynamic_code", number);
		}
		
		if((!SysDictionary.CHANEL_MD01.equals(chanel))&&Tools.hv(orderNum))
		{
			requestJson.put("bill_code", orderNum);
		}
		else
		{
			requestJson.put("bill_code", billNum);
			requestJson.put("batch_num", payment.optString("batch_num"));
		}
		requestJson.put("consume_cardmoney", payment.optDouble("currency_amount"));
		requestJson.put("consume_credit", 0d);
		requestJson.put("consume_creditmoney", 0d);
		requestJson.put("consume_totalmoney", paymentAmount);
//		requestJson.put("operator", optName);
		requestJson.put("report_date", reportDate);
		requestJson.put("shift_id", shiftId);
		requestJson.put("opt_num", optNum);
		requestJson.put("pos_num", posNum);
		requestJson.put("chanel", chanel);

//		List<JSONObject> requestList = new ArrayList<JSONObject>();
//		requestList.add(requestJson);
//
//		Data requestData = Data.get(tenancyId, storeId, 0);
//		requestData.setType(Type.CUSTOMER_CARD_CONSUME);
//		requestData.setOper(Oper.add);
//		requestData.setData(requestList);

		Data responseData = Data.get();
		customerService.customerCardConsumePost(tenancyId, storeId, requestJson, responseData,Type.BILL_PAYMENT.name());

		if (Constant.CODE_SUCCESS == responseData.getCode())
		{
			JSONObject responseJson = JSONObject.fromObject(responseData.getData().get(0));
			
			String billCode = responseJson.optString("bill_code");
			Double usefulCredit =ParamUtil.getDoubleValueByObject(responseJson,"useful_credit");
			Double mainBalance =ParamUtil.getDoubleValueByObject(responseJson,"main_balance");
			Double rewardBalance =ParamUtil.getDoubleValueByObject(responseJson,"reward_balance");
			// 返回余额,pad显示
			resultJson.put("credit", usefulCredit);
			resultJson.put("main_balance", mainBalance);
			resultJson.put("reward_balance", rewardBalance);

//			payment.put("number", responseJson.optString("card_code"));
//			payment.put("bill_code", billCode);

			payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
			resultData.setCode(Constant.CODE_SUCCESS);
			
			List<JSONObject> memberList = memberDao.queryPosBillMember(tenancyId, storeId, billNum, SysDictionary.BILL_MEMBERCARD_CZXF03);
			
			for(JSONObject memberJson: memberList)
			{
				if(SysDictionary.BILL_MEMBERCARD_CZXF03.equals(memberJson.optString("type")))
				{
					memberDao.updatePosBillMember(tenancyId, storeId, billNum, SysDictionary.BILL_MEMBERCARD_CZXF03, payment.optDouble("currency_amount"), 0d, billCode, SysDictionary.REQUEST_STATUS_COMPLETE, responseJson.optString("name"), usefulCredit, mainBalance,rewardBalance);
				}
			}
			
			JSONObject paymentJson = new JSONObject();
			paymentJson.put("number", responseJson.optString("card_code"));
			paymentJson.put("bill_code", billCode);
			paymentJson.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
			paymentJson.put("id", payId);
			paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", paymentJson);
			
			try
			{
				// 会员卡打印
				customerService.customerCardConsumePrint(tenancyId, storeId, posNum, responseJson, SysDictionary.PRINT_CODE_1010);
			}
			catch (Exception e)
			{
				e.printStackTrace();
			}
		}
		else if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
		{
			payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY);
			resultData.setCode(Constant.CODE_CONN_EXCEPTION);
			resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
		}
		else if (Constant.PAYMENT_STATUS_PAYING_CODE == responseData.getCode())
		{
			resultData.setCode(Constant.PAYMENT_STATUS_PAYING_CODE);
			resultData.setMsg(PropertiesLoader.getProperty(String.valueOf(responseData.getCode())));
		}
		else
		{
			paymentDao.deletePosBillPaymentByID(tenancyId, storeId, billNum, payId);

			int jzid = payment.optInt("jzid");
			String table_code = payment.optString("table_code");
			String paymentClass = payment.optString("type");
			String paymentName = payment.optString("name");
			String paymentEnglishName = payment.optString("name_english");
			double exchangeRate = payment.optDouble("rate");
			double pay_amount = payment.optDouble("amount");
			double pay_count = payment.optDouble("count");
			double currency_amount = payment.optDouble("currency_amount");
			String phone = payment.optString("phone");
			int customer_id = payment.optInt("customer_id");
			String remark = payment.optString("remark");
			String batch_num = payment.optString("batch_num");
			String is_ysk = Tools.hv(payment.optString("is_ysk")) ? payment.optString("is_ysk") : "N";

			paymentDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, DateUtil.parseDate(reportDate), shiftId, posNum, optNum, table_code, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, customer_id, null,
					remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_FAILURE, batch_num, payment.optString("param_cach"), 0d);

			memberDao.deletePosBillMember(tenancyId, storeId, billNum, SysDictionary.BILL_MEMBERCARD_CZXF03);

			if (CrmErrorCode.BALANCE_NOTENOUGH_ERROE.getNumber() == responseData.getCode())
			{
				resultData.setCode(resultData.getCode());
				resultData.setMsg(PropertiesLoader.getProperty(String.valueOf(responseData.getCode())));
			}
			else
			{
				resultData.setCode(PosErrorCode.BILL_PAYMENT_ERROP.getNumber());
				resultData.setMsg(PosErrorCode.BILL_PAYMENT_ERROP.getMessage().replace("{0}", responseData.getMsg()));
			}
		}
	}

	@Override
	public void findPaymentDebit(String tenancyId, int storeId, String billNum,String orderNum,String chanel,Double paymentAmount,JSONObject payment,Timestamp currentTime,JSONObject resultJson,Data resultData) throws Exception
	{
		String number = payment.getString("number");
		String posNum = payment.optString("pos_num");
		// String reportDate = payment.optString("report_date");

		JSONObject cachParamJson = JSONObject.fromObject(payment.getString("param_cach"));

		// String phone = cachParamJson.optString("phone");
		// String remark = cachParamJson.optString("remark");

		JSONObject requestJson = new JSONObject();
		if (Tools.hv(number))
		{
			requestJson.put("card_code", number);
		}
		else
		{
			requestJson.put("card_code", cachParamJson.optString("third_code"));
		}
		requestJson.put("third_code", cachParamJson.optString("third_code"));
		if ((!SysDictionary.CHANEL_MD01.equals(chanel)) && Tools.hv(orderNum))
		{
			requestJson.put("third_bill_code", orderNum);
		}
		else
		{
			requestJson.put("third_bill_code", billNum);
			requestJson.put("batch_num", payment.optString("batch_num"));
		}

		// List<JSONObject> requestList = new ArrayList<JSONObject>();
		// requestList.add(requestJson);
		//
		// Data requestData = Data.get(tenancyId, storeId, 0);
		// requestData.setType(Type.CUSTOMER_BILL);
		// requestData.setOper(Oper.find);
		// requestData.setData(requestList);
		//
		// Data responseData = Data.get(requestData);
		// customerService.commonPost(JSONObject.fromObject(requestData).toString(),
		// responseData);

		Data responseData = customerService.queryCustomerCardConsume(tenancyId, storeId, requestJson);

		if (responseData.isSuccess())
		{
			JSONObject responseJson = JSONObject.fromObject(responseData.getData().get(0));
			String billCode = responseJson.optString("bill_code");
			Double usefulCredit = responseJson.optDouble("useful_credit");

			resultJson.put("credit", usefulCredit);
			resultJson.put("main_balance", responseJson.optDouble("main_balance"));
			resultJson.put("reward_balance", responseJson.optDouble("reward_balance"));

			payment.put("number", responseJson.optString("card_code"));
			payment.put("bill_code", billCode);
			payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_COMPLETE);

			JSONObject updatePaymentJson = new JSONObject();
			updatePaymentJson.put("number", responseJson.optString("card_code"));
			updatePaymentJson.put("bill_code", billCode);
			updatePaymentJson.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
			updatePaymentJson.put("id", payment.optInt("id"));
			paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", updatePaymentJson);
			
			resultData.setCode(Constant.CODE_SUCCESS);

			List<JSONObject> memberList = memberDao.queryPosBillMember(tenancyId, storeId, billNum, SysDictionary.BILL_MEMBERCARD_CZXF03);
			for (JSONObject memberJson : memberList)
			{
				if (SysDictionary.BILL_MEMBERCARD_CZXF03.equals(memberJson.optString("type")))
				{
					memberDao.updatePosBillMember(tenancyId, storeId, billNum, SysDictionary.BILL_MEMBERCARD_CZXF03, payment.optDouble("currency_amount"), 0d, billCode, SysDictionary.REQUEST_STATUS_COMPLETE, responseJson.optString("name"), usefulCredit, usefulCredit);
				}
			}

			try
			{
				customerService.customerCardConsumePrint(tenancyId, storeId, posNum, responseJson, SysDictionary.PRINT_CODE_1010);
			}
			catch (Exception e)
			{
				e.printStackTrace();
			}
		}
		else if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
		{
			payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY);
			resultData.setCode(Constant.CODE_CONN_EXCEPTION);
			resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
		}
		else
		{
			payment.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_FAILURE);

			JSONObject updatePaymentJson = new JSONObject();
			updatePaymentJson.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_FAILURE);
			updatePaymentJson.put("id", payment.optInt("id"));
			paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", updatePaymentJson);

			resultData.setCode(PosErrorCode.BILL_PAYMENT_ERROP.getNumber());
			resultData.setMsg(PosErrorCode.BILL_PAYMENT_ERROP.getMessage().replace("{0}", responseData.getMsg()));
		}
	}

	@Override
	public void paymentRefund(String tenancyId, int storeId, String billNum,String oldBillNum,String orderNum,String chanel,Double billPaymentAmount,JSONObject payment,Timestamp currentTime,JSONObject resultJson,Data resultData,String operType) throws Exception
	{
//		String currentTimeStr = DateUtil.format(currentTime);

		String reportDateStr = ParamUtil.getStringValueByObject(payment,"report_date");
		Integer shiftId = ParamUtil.getIntegerValueByObject(payment,"shift_id");
		String posNum = ParamUtil.getStringValueByObject(payment,"pos_num");
		String optNum = ParamUtil.getStringValueByObject(payment,"cashier_num");
		String oldBillCode = ParamUtil.getStringValueByObject(payment,"bill_code");
		Double currencyAmount = Math.abs(ParamUtil.getDoubleValueByObject(payment,"currency_amount"));
//		String optName = paymentDao.getEmpNameById(optNum, tenancyId, storeId);
		
		JSONObject cardJson = new JSONObject();
		cardJson.put("card_code", payment.optString("number"));
		cardJson.put("old_bill_code", oldBillCode);
		if ((!SysDictionary.CHANEL_MD01.equals(chanel)) && Tools.hv(orderNum))
		{
			cardJson.put("bill_code", orderNum);
		}
		else
		{
			cardJson.put("bill_code", oldBillNum);
			cardJson.put("batch_num", payment.optString("batch_num"));
		}
		cardJson.put("revoked_trading", currencyAmount);
		cardJson.put("chanel", chanel);
		cardJson.put("report_date", reportDateStr);
		cardJson.put("shift_id", shiftId);
		cardJson.put("opt_num", optNum);
		cardJson.put("pos_num", posNum);
		
//		List<JSONObject> dataList = new ArrayList<JSONObject>();
//		dataList.add(cardJson);
//
//		Data paramData = Data.get(tenancyId,storeId,Constant.CODE_SUCCESS);
//		paramData.setType(Type.CUSTOMER_CARD_CONSUME);
//		paramData.setOper(Oper.update);
//		paramData.setData(dataList);

		Data responseData = new Data();
		customerService.customerCardCancelConsumePost(tenancyId, storeId, cardJson, responseData, "N", operType);

		if (Constant.CODE_SUCCESS == responseData.getCode())
		{
			resultData.setCode(Constant.CODE_SUCCESS);
			
			JSONObject responseJson = JSONObject.fromObject(responseData.getData().get(0));
			// 返回余额,pad显示
			resultJson.put("credit", responseJson.optDouble("credit"));
			resultJson.put("main_balance", responseJson.optDouble("main_balance"));
			resultJson.put("reward_balance", responseJson.optDouble("reward_balance"));

			JSONObject paymentJson = new JSONObject();
			paymentJson.put("number", responseJson.optString("card_code"));
			paymentJson.put("bill_code", responseJson.optString("bill_code"));
			paymentJson.put("payment_state", SysDictionary.PAYMENT_STATE_REFUND_COMPLETE);
			paymentJson.put("id", payment.optInt("id"));
			paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", paymentJson);

			// 会员卡打印
			try
			{
				responseJson.put("consume_cardmoney", 0d);
				responseJson.put("total_main", 0d);
				responseJson.put("total_reward", 0d);
				responseJson.put("operator", responseJson.optString("last_operator"));
				responseJson.put("updatetime", responseJson.optString("last_updatetime"));
				
				customerService.customerCardConsumePrint(tenancyId, storeId, posNum, responseJson, SysDictionary.PRINT_CODE_1011);
			}
			catch (Exception e)
			{
				e.printStackTrace();
			}
		}
		else
		{
			if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
			{
				resultData.setCode(Constant.CODE_CONN_EXCEPTION);
				resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
			}
			else
			{
				JSONObject paymentJson = new JSONObject();
				paymentJson.put("payment_state", SysDictionary.PAYMENT_STATE_REFUND_FAILURE);
				paymentJson.put("id", payment.optInt("id"));
				paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", paymentJson);

				resultData.setCode(responseData.getCode());
				resultData.setMsg(responseData.getMsg());
			}

			// 退款失败,记录异常
			Integer paymentId = ParamUtil.getIntegerValueByObject(payment, "jzid");
			String paymentClass = ParamUtil.getStringValueByObject(payment, "payment_class");
			String thirdBillCode = ParamUtil.getStringValueByObject(cardJson, "bill_code");

			PosThirdPaymentRefundEntity paymentRefund = new PosThirdPaymentRefundEntity(tenancyId, storeId, DateUtil.parseDate(reportDateStr), shiftId, posNum, optNum, chanel, SysDictionary.SERVICE_TYPE_CONSUME, oldBillNum, thirdBillCode, oldBillCode, paymentClass, paymentId,
					Math.abs(billPaymentAmount), currencyAmount, currentTime, currentTime, optNum, String.valueOf(resultData.getCode()), resultData.getMsg(), operType, SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL, SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL);
			paymentRefund.setExtra(cardJson.toString());
			thirdPaymentRefundService.thirdPaymentRefundFailure(tenancyId, storeId, paymentRefund);
		}
	}

	@Override
	public void paymentCancel(String tenancyId, int storeId, String billNum, String orderNum, String chanel, Double billPaymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData, String operType) throws Exception
	{
//		String currentTimeStr = DateUtil.format(currentTime);

		String reportDateStr = ParamUtil.getStringValueByObject(payment,"report_date");
		Integer shiftId = ParamUtil.getIntegerValueByObject(payment,"shift_id");
		String posNum = ParamUtil.getStringValueByObject(payment,"pos_num");
		String optNum = ParamUtil.getStringValueByObject(payment,"cashier_num");
		String oldBillCode = ParamUtil.getStringValueByObject(payment,"bill_code");
		Double currencyAmount = Math.abs(ParamUtil.getDoubleValueByObject(payment,"currency_amount"));
//		String optName = paymentDao.getEmpNameById(optNum, tenancyId, storeId);
		
		JSONObject cardJson = new JSONObject();
		cardJson.put("card_code", payment.optString("number"));
		cardJson.put("old_bill_code", oldBillCode);
		if((!SysDictionary.CHANEL_MD01.equals(chanel))&&Tools.hv(orderNum))
		{
			cardJson.put("bill_code", orderNum);
		}
		else
		{
			cardJson.put("bill_code", billNum);
			cardJson.put("batch_num", payment.optString("batch_num"));
		}
		cardJson.put("revoked_trading", currencyAmount);
		cardJson.put("chanel", chanel);
//		cardJson.put("updatetime", currentTimeStr);
		cardJson.put("report_date", reportDateStr);
		cardJson.put("shift_id", shiftId);
		cardJson.put("opt_num", optNum);
		cardJson.put("pos_num", posNum);
        
		Data responseData = new Data();
		customerService.customerCardCancelConsumePost(tenancyId, storeId, cardJson, responseData, "N", operType);

		if (responseData.isSuccess())
		{
			JSONObject responseJson = JSONObject.fromObject(responseData.getData().get(0));
			// 返回余额,pad显示
			resultJson.put("credit", responseJson.optDouble("credit"));
			resultJson.put("main_balance", responseJson.optDouble("main_balance"));
			resultJson.put("reward_balance", responseJson.optDouble("reward_balance"));

			payment.put("number", responseJson.optString("card_code"));
			payment.put("bill_code", responseJson.optString("bill_code"));

			resultData.setCode(Constant.CODE_SUCCESS);

			// 会员卡打印
			try
			{
				responseJson.put("consume_cardmoney", 0d);
				responseJson.put("total_main", 0d);
				responseJson.put("total_reward", 0d);
				responseJson.put("operator", responseJson.optString("last_operator"));
				responseJson.put("updatetime", responseJson.optString("last_updatetime"));
				
				customerService.customerCardConsumePrint(tenancyId, storeId, posNum, responseJson, SysDictionary.PRINT_CODE_1011);
			}
			catch (Exception e)
			{
				e.printStackTrace();
			}
		}
		else if (Constant.CARD_PAYMENT_ORDER_NOT_EXIST_CODE == responseData.getCode())
		{
			resultData.setCode(Constant.CODE_SUCCESS);
		}
		else
		{
			if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
			{
				resultData.setCode(Constant.CODE_CONN_EXCEPTION);
				resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
			}
			else
			{
				resultData.setCode(responseData.getCode());
				resultData.setMsg(responseData.getMsg());
			}

			// 退款失败,记录异常
			Integer paymentId = ParamUtil.getIntegerValueByObject(payment, "jzid");
			String paymentClass = ParamUtil.getStringValueByObject(payment, "payment_class");
			String thirdBillCode = ParamUtil.getStringValueByObject(cardJson, "bill_code");

			PosThirdPaymentRefundEntity paymentRefund = new PosThirdPaymentRefundEntity(tenancyId, storeId, DateUtil.parseDate(reportDateStr), shiftId, posNum, optNum, chanel, SysDictionary.SERVICE_TYPE_CONSUME, billNum, thirdBillCode, oldBillCode, paymentClass, paymentId, billPaymentAmount,
					currencyAmount, currentTime, currentTime, optNum, String.valueOf(resultData.getCode()), resultData.getMsg(), operType, SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL, SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL);
			paymentRefund.setExtra(cardJson.toString());
			thirdPaymentRefundService.thirdPaymentRefundFailure(tenancyId, storeId, paymentRefund);

		}
	}

	@Override
	public void paymentRegain(String tenancyId, int storeId, String billNum, String orderNum, String chanel, Double billPaymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData, String operType) throws Exception
	{
//		String currentTimeStr = DateUtil.format(currentTime);

		String reportDateStr = ParamUtil.getStringValueByObject(payment,"report_date");
		Integer shiftId = ParamUtil.getIntegerValueByObject(payment,"shift_id");
		String posNum = ParamUtil.getStringValueByObject(payment,"pos_num");
		String optNum = ParamUtil.getStringValueByObject(payment,"cashier_num");
		String oldBillCode = ParamUtil.getStringValueByObject(payment,"bill_code");
		Double currencyAmount = Math.abs(ParamUtil.getDoubleValueByObject(payment,"currency_amount"));
//		String optName = paymentDao.getEmpNameById(optNum, tenancyId, storeId);
		
		JSONObject cardJson = new JSONObject();
		cardJson.put("card_code", payment.optString("number"));
		cardJson.put("old_bill_code", oldBillCode);
		if((!SysDictionary.CHANEL_MD01.equals(chanel))&&Tools.hv(orderNum))
		{
			cardJson.put("bill_code", orderNum);
		}
		else
		{
			cardJson.put("bill_code", billNum);
			cardJson.put("batch_num", payment.optString("batch_num"));
		}
		cardJson.put("revoked_trading", currencyAmount);
		cardJson.put("chanel", chanel);
//		cardJson.put("updatetime", currentTimeStr);
		cardJson.put("report_date", reportDateStr);
		cardJson.put("shift_id", shiftId);
		cardJson.put("opt_num", optNum);
		cardJson.put("pos_num", posNum);
		
//		List<JSONObject> dataList = new ArrayList<JSONObject>();
//		dataList.add(cardJson);
//
//		Data paramData = Data.get(tenancyId,storeId,Constant.CODE_SUCCESS);
//		paramData.setType(Type.CUSTOMER_CARD_CONSUME);
//		paramData.setOper(Oper.update);
//		paramData.setData(dataList);

		Data responseData = new Data();
		customerService.customerCardCancelConsumePost(tenancyId, storeId,cardJson, responseData,"N",operType);

		if (responseData.isSuccess())
		{
			JSONObject responseJson = JSONObject.fromObject(responseData.getData().get(0));
			// 返回余额,pad显示
			resultJson.put("credit", responseJson.optDouble("credit"));
			resultJson.put("main_balance", responseJson.optDouble("main_balance"));
			resultJson.put("reward_balance", responseJson.optDouble("reward_balance"));

			payment.put("number", responseJson.optString("card_code"));
			payment.put("bill_code", responseJson.optString("bill_code"));

			resultData.setCode(Constant.CODE_SUCCESS);

			// 会员卡打印
			try
			{
				responseJson.put("consume_cardmoney", 0d);
				responseJson.put("total_main", 0d);
				responseJson.put("total_reward", 0d);
				responseJson.put("operator", responseJson.optString("last_operator"));
				responseJson.put("updatetime", responseJson.optString("last_updatetime"));
				
				customerService.customerCardConsumePrint(tenancyId, storeId, posNum, responseJson, SysDictionary.PRINT_CODE_1011);
			}
			catch (Exception e)
			{
				e.printStackTrace();
			}
		}
		else
		{
			if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
			{
				resultData.setCode(Constant.CODE_CONN_EXCEPTION);
				resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
			}
			else
			{
				resultData.setCode(responseData.getCode());
				resultData.setMsg(responseData.getMsg());
			}

			// 退款失败,记录异常
			Integer paymentId = ParamUtil.getIntegerValueByObject(payment, "jzid");
			String paymentClass = ParamUtil.getStringValueByObject(payment, "payment_class");
			String thirdBillCode = ParamUtil.getStringValueByObject(cardJson, "bill_code");

			PosThirdPaymentRefundEntity paymentRefund = new PosThirdPaymentRefundEntity(tenancyId, storeId, DateUtil.parseDate(reportDateStr), shiftId, posNum, optNum, chanel, SysDictionary.SERVICE_TYPE_CONSUME, billNum, thirdBillCode, oldBillCode, paymentClass, paymentId, billPaymentAmount,
					currencyAmount, currentTime, currentTime, optNum, String.valueOf(resultData.getCode()), resultData.getMsg(), operType, SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL, SysDictionary.THIRD_PAY_STATUS_REFUND_FAIL);
			paymentRefund.setExtra(cardJson.toString());
			thirdPaymentRefundService.thirdPaymentRefundFailure(tenancyId, storeId, paymentRefund);

		}
	}

	@Override
	public void paymentDebitResult(String tenancyId, int storeId, String billNum, JSONObject paymentJson) throws Exception
	{
		// TODO 自动生成的方法存根
		JSONObject updatePaymentJson = new JSONObject();
		updatePaymentJson.put("number", paymentJson.optString("number"));
		updatePaymentJson.put("bill_code", paymentJson.optString("bill_code"));
		updatePaymentJson.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
		updatePaymentJson.put("id", paymentJson.optInt("id"));
		paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", updatePaymentJson);
	}
}
