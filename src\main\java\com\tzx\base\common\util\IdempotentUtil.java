package com.tzx.base.common.util;

import org.apache.commons.collections.map.LRUMap;

import java.util.Map;

/**
 * <AUTHOR> on 2020-11-02
 */
public class IdempotentUtil {

    private static final Map reqCache = new LRUMap(100);

    /**
     * 幂等性判断
     *
     * @return
     */
    public static boolean isDuplicate(String id, Object mutex) {
        synchronized (mutex) {
            if (reqCache.containsKey(id)) {
                return true;
            }
            reqCache.put(id, null);
        }
        return false;
    }

}
