package com.tzx.base.po.springjdbc.dao.imp;

import java.util.List;

import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Repository;

import com.tzx.base.po.springjdbc.dao.OrganDao;
import com.tzx.framework.common.util.dao.impl.GenericDaoImpl;

@Repository(OrganDao.NAME)
public class OrganDaoImp extends GenericDaoImpl implements OrganDao
{
	private static final Logger logger = Logger.getLogger(OrganDaoImp.class);
	@Override
	public List<JSONObject> getOrgan(String tenantId, String employeeOrgIds) throws Exception
	{

		StringBuilder sql = new StringBuilder("select * from organ ");
		/*
		 * sql.append("login_account='"+longinJsonObject.getString("loginUserName"
		 * ));
		 * sql.append("' and passwd='"+longinJsonObject.getString("password")
		 * +"'");
		 */

		return this.query4Json(tenantId, sql.toString().replaceAll("'null'", "null"));
	}

	@Override
	public List<JSONObject> findOrgans(String tenantId, String employeeOrgIds) throws Exception
	{
		String sql = "select id, org_short_name as text, top_org_id as parent from organ where org_type!='门店' order by top_org_id";

		return this.query4Json(tenantId, sql);
	}

	@Override
	public String getOrganOperingStatus(String tenantId,Integer organId) throws Exception
	{
//		this.jdbcTemplate.setDataSource(MultiDataSourceManager.getDataSource(tenantId));
		String sql = "select operating_status from organ where id = ? and tenancy_id = ?";
		
		String operating_status = null;
		SqlRowSet rs = this.jdbcTemplate.queryForRowSet(sql.toString(), new Object[]{organId,tenantId});
		while (rs.next())
		{
			operating_status = rs.getString("operating_status");
		}
//		logger.info("getOrganOperingStatus的sql" + sql + ",参数:" + organId);
		return operating_status;
	}
	
	public String getOperatingStatusName(String tenantId,String operating_status) throws Exception
	{
//		this.jdbcTemplate.setDataSource(MultiDataSourceManager.getDataSource(tenantId));
		
		String str = "select class_item from sys_dictionary where class_identifier_code = ? and class_item_code = ? ";
		String class_item = "";
		if (StringUtils.isNotEmpty(operating_status))
		{
			SqlRowSet rs = this.jdbcTemplate.queryForRowSet(str.toString(),new Object[]{"operating_status",operating_status});
			while (rs.next()) 
			{
				class_item = rs.getString("class_item");
			}
		}
//		logger.info("getOperatingStatusName的sql" + str + ",参数:" + operating_status);
		return class_item;
	}
	
	public String getShopId(String tenancyId,Integer organId){
		String sql = "select third_organ_code from organ where id = ? and tenancy_id = ?";
		
		String third_organ_code = null;
		SqlRowSet rs = this.jdbcTemplate.queryForRowSet(sql.toString(), new Object[]{organId,tenancyId});
		while (rs.next())
		{
			third_organ_code = rs.getString("third_organ_code");
		}
//		logger.info("getShopId的sql" + sql + ",参数:" + organId);
		return third_organ_code;
	}
}
