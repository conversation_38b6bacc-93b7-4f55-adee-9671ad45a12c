package com.tzx.pos.po.springjdbc.dao;


import java.util.List;
import java.util.Map;

import com.tzx.framework.common.entity.Data;

import com.tzx.pos.base.dao.BaseDao;
import net.sf.json.JSONObject;


public interface QuickPassDao extends BaseDao{
	String	NAME	= "com.tzx.pos.bo.imp.QuickPassDaoImp";
	
	/**
	 * 插入数据
	 * 从MQ里取出数据 插入数据库中
	 * @return
	 */
	public int insert(Object[] objs) throws Exception;
	/**
	 * 查询数据
	 * @return
	 */
	public List<JSONObject>  findList(Map<String, Object> map) throws Exception;
	/**
	 * 删除数据
	 * 根据流水号bill_code更改state状态
	 * 1 正常，0删除
	 * @return
	 */
	public int deleteData(String bill_code,int quick_id) throws Exception;

	/**
	 * 根据账单号查询数据
	 * @param tenancy_id
	 * @param bill_num
	 * @return
	 */
	public JSONObject getDataByBillNum(String tenancy_id,String bill_num)throws  Exception;
}
