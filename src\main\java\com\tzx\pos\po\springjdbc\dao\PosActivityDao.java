package com.tzx.pos.po.springjdbc.dao;

import com.tzx.framework.common.entity.Data;
import com.tzx.pos.base.dao.BaseDao;
import com.tzx.pos.bo.dto.PosBill;
import net.sf.json.JSONObject;

import java.util.List;

public interface PosActivityDao extends BaseDao
{
	String	NAME	= "com.tzx.pos.po.springjdbc.dao.imp.PosActivityDaoImp";

	/**
	 * 查询有效活动
	 * */
	public List<JSONObject> getValidActivity(String tenancyId, Integer storeId, String chanel) throws Exception;
	/**
	 * 添加菜品折扣明细表pos_item_discount_list数据
	 * */
	public void insertItemDiscountList(String tenancyId, Integer storeId, String billNum) throws Exception;
	/**
	 * 查询菜品折扣明细表总的折扣金额
	 * sum(discount_amount)
	 * */
	public Double getSumDiscountAmount(String tenancyId, Integer storeId, String billNum) throws Exception;
	/**
	 * 根据活动id删除菜品折扣明细表相关数据
	 * tenancy_id,store_id,bill_num,activity_id,batch_num
	 * */
	public void deleteItemDiscountInfo(List<Object[]> objs) throws Exception;
	/**
	 * 查询使用过的活动数据
	 * id,subject,batch_num,if_parallel
	 * */
	public List<JSONObject> getUsedActivityInfo(String tenancyId, Integer storeId, String billNum) throws Exception;
	/**
	 * 删除加价购，满额赠菜，第二杯半价，赠品
	 *
	 * */
	public void deleteActivityGift(String tenancyId, Integer storeId, String billNum, String batchNum) throws Exception;
	/**
	 * 查询使用活动又取消活动的菜品明细
	 * 没有使用活动
	 * */
	public List<?> getcancelActivityItemInfo(String tenancyId, Integer storeId, String billNum) throws Exception;
	/**
	 * 查询商品限制规则明细
	 * crm_activity_item
	 * */
	public List<JSONObject> getActivityItemRules(String tenancyId, Integer activityId) throws Exception;
	/**
	 * 查询已经使用活动相关明细
	 * 落单计算金额
	 * crm_activity_rule
	 * */
	public List<JSONObject> getusedActivityItemRules(String tenancyId, Integer storeId, String billNum) throws Exception;
	/**
	 * 账单查询
	 *
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public List<PosBill> newFindPosBill(Data param, Data result) throws Exception;

	/**
	 * 根据菜品折扣明细表pos_item_discount_list更新pos_bill_item数据
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param activityBatchNum
	 * @throws Exception
	 */
	public void updatePosBillItem(String tenancyId, Integer storeId, String billNum, String activityBatchNum) throws Exception;
}
