package com.tzx.clientorder.wlifeprogram.dao;

import com.tzx.clientorder.wlifeprogram.common.entity.MicroLifeOrder;
import net.sf.json.JSONObject;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *
 */
public interface FirstPaymentDao extends WlifeCommonDao {
    String NAME = "com.tzx.clientorder.wlifeprogram.dao.impl.FirstPaymentDaoImpl";

    /**
     * 获取桌台状态
     *
     * @param tenancyId
     * @param storeId
     * @param tableCode
     * @return
     * @throws Exception
     */
    String getTableState(String tenancyId, int storeId, String tableCode) throws Exception;

    /**
     * 通过订单号获取账单信息
     *
     * @param tenancyId
     * @param storeId
     * @param orderNum
     * @return
     * @throws Exception
     */
    List<JSONObject> getBillInfoByOrderNum(String tenancyId, int storeId, String orderNum) throws Exception;

    /**
     * 查询菜品规格
     *
     * @param microLifeOrder
     * @return
     * @throws Exception
     */
    Map<String, String> getUnitNameMap(MicroLifeOrder microLifeOrder) throws Exception;

    /**
     * 查询微生活会员信息
     *
     * @param tenancyId
     * @param tableCode
     * @param storeId
     * @return
     */
    JSONObject getWLifeMember(String tenancyId, String tableCode, int storeId) throws Exception;

    JSONObject getWLifeMember(String tenancyId, String billNum) throws Exception;

    /**
     * 查询订单的信息
     *
     * @param tenancyId
     * @param tableCode
     * @param storeId
     * @return
     * @throws Exception
     */
    JSONObject getBillInfo(String tenancyId, String tableCode, int storeId) throws Exception;

    JSONObject getBillInfo(String tenancyId, String billNum) throws Exception;

    /**
     * 查询套餐菜品信息
     *
     * @param tenancyId
     * @param billNum
     * @return
     * @throws Exception
     */
    List<JSONObject> getSetmeal(String tenancyId, String billNum, String itemProperty) throws Exception;

    /**
     * 查询套餐的主菜或套餐必选菜的菜品
     *
     * @param tenancyId
     * @param billNum
     * @param type      套餐主菜或是套餐必选菜查询
     * @return
     * @throws Exception
     */
    List<JSONObject> getSetmealDish(String tenancyId, String billNum, String type, int stemealCount, String setmealId, String setmeal_rwid) throws Exception;

    /**
     * 根据pos_bill_item表的菜品id，查询做法
     *
     * @param tenancyId
     * @param id
     * @return
     * @throws Exception
     */
    List<JSONObject> getCooks(String tenancyId, int id) throws Exception;

    /**
     * 查询订单的信息
     *
     * @param tenancyId
     * @param tableCode
     * @param storeId
     * @return
     * @throws Exception
     */
    JSONObject getBillOrder(String tenancyId, String tableCode, int storeId) throws Exception;

    int selectComboMaxItemSerial(String billNum, int storeId, String tenancyId) throws Exception;

    void savePosBillBooked(String tenancyId, int storeId, Date reportDate, int shiftId, String billNum, String batchNum,
                           String serialNum, String orderNum, Timestamp opentableTime, String openPosNum, String openOpt, String waiterNum, int guest, int itemMenuId, String saleMode,
                           String source, int serviceId, double serviceAmount, String copyBillNum, String remark, String billProperty, String paymentState, int discountModeId, double discountkAmount,
                           double shopRealAmount, double platformChargeAmount, String settlementType, double discountRate,String tableCode) throws Exception;

    void savePosBill(String tenancyId, int storeId, Date reportDate, int shiftId, String billNum, String batchNum,
                           String serialNum, String orderNum, Timestamp opentableTime, String openPosNum, String openOpt, String waiterNum, int guest, int itemMenuId, String saleMode,
                           String source, int serviceId, double serviceAmount, String copyBillNum, String remark, String billProperty, String paymentState, int discountModeId, double discountkAmount,
                           double shopRealAmount, double platformChargeAmount, String settlementType, double discountRate,String tableCode) throws Exception;

    List<JSONObject> getRwidsWithoutWaitCallItem(String tenancyId, int storeId, String billNum, String orderRemark) throws Exception;

    JSONObject getBillDiscount(String tenancyId, int storeId, String billNum) throws Exception;
    JSONObject getBillDiscountForPay(String tenancyId, int storeId, String billNum) throws Exception;

    List<JSONObject> getPosBillItemByBillNum(String tenantId,int store_id,String oldBillNum) throws Exception;
    List<JSONObject> getPosBillItemForDiscountCaseByBillnum(String tenantId, Integer storeId, String billNum,Integer discountCaseId) throws Exception;
    /**
     * 获取菜品做法
     *
     * @param methodId
     * @return
     */
    JSONObject getCookMethodObj(Integer methodId) throws Exception;

    String getBillNumByOrderId(String orderId) throws Exception;

    void updatePosBillSource(String billNum, String sourceVal, boolean isOrdering) throws Exception;

    void updatePosBillOrderNum(String orderNum, String billNum) throws Exception;

    JSONObject getTableStatus(String tenantId, Integer storeId, String tableNo) throws Exception;

    void lockTableStatus(Integer storeId, String tableNo, String lockOptNum, String lockPosNum, String optName) throws Exception;

    JSONObject getBillMemberInfo(String billNum, String type, String cardCode) throws Exception;

    JSONObject getBillNumByOutOrderId(String tenantId, Integer storeId, String outOrderId) throws Exception;

    Integer getServiceIdByTablCode(String tenantId, int storeId, String  tableCode) throws Exception;


    /** 查询套餐明细
     * @param tenantId
     * @param itemIdList
     * @return
     * @throws Exception
     */
    public List<JSONObject> getItemComboDetails(String tenantId, List<Integer> itemIdList) throws Exception;
    
	/** 根据账单编号获取账单信息
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @return
	 * @throws Exception
	 */
	public JSONObject getPosBillByBillnum(String tenancyId,int storeId,String billNum) throws Exception;
}
