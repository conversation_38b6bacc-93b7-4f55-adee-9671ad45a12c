package com.tzx.framework.common.util;

import java.io.UnsupportedEncodingException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Random;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.KeyGenerator;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;

public class JavaAES
{
	public static String	PASS	= "TZXAES";
	public static Random	random2	= new Random();
	public static int		len		= 13;
	public static String 	reg     = "[^0-9]";
	public static String numJia(String id,String card_code)
	{
		
		return JavaDynamicCode.encryptDynamicCode(id, card_code);
		/*StringBuilder result = new StringBuilder(id + "");
		result.append(result.length() < 10 ? "0" + result.length() : result.length());
		if (result.length() < len)
		{
			int zz = len - result.length();
			for (int i = 0; i < zz; i++)
			{
				result.insert(0, random2.nextInt(9));
			}
		}
		
		while(card_code!=null && card_code.equals(result.toString()))
		{
			result.setLength(0);
			result.append(id + "");
			result.append(result.length() < 10 ? "0" + result.length() : result.length());
			if (result.length() < len)
			{
				int zz = len - result.length();
				for (int i = 0; i < zz; i++)
				{
					result.insert(0, random2.nextInt(9));
				}
			}
		}
		
		return result.toString();*/
	}
	
	
	
	
	public static int numJie(String cc) throws Exception
	{
		return Integer.valueOf(JavaDynamicCode.unEncryptDynamicCode(cc));
		/*cc = cc.replaceAll(reg,"");
		int len = cc.length();
		String cd = cc.substring(len - 2);
		if (cd.startsWith("0"))
		{
			cd = cc.substring(len - 1);
		}
		int l = Integer.parseInt(cd);
		return Integer.parseInt(cc.substring(cc.length() - l - 2, cc.length() - 2));*/

	}

	/**
	 * 加密
	 * 
	 * @param content
	 *            需要加密的内容
	 * @param password
	 *            加密密码
	 * @return
	 */
	public static byte[] encrypt(String content, String password)
	{
		try
		{
			KeyGenerator kgen = KeyGenerator.getInstance("AES");
			kgen.init(128, new SecureRandom(password.getBytes()));
			SecretKey secretKey = kgen.generateKey();
			byte[] enCodeFormat = secretKey.getEncoded();
			SecretKeySpec key = new SecretKeySpec(enCodeFormat, "AES");
			Cipher cipher = Cipher.getInstance("AES");// 创建密码器
			byte[] byteContent = content.getBytes("utf-8");
			cipher.init(Cipher.ENCRYPT_MODE, key);// 初始化
			byte[] result = cipher.doFinal(byteContent);
			return result; // 加密
		}
		catch (NoSuchAlgorithmException e)
		{
			e.printStackTrace();
		}
		catch (NoSuchPaddingException e)
		{
			e.printStackTrace();
		}
		catch (InvalidKeyException e)
		{
			e.printStackTrace();
		}
		catch (UnsupportedEncodingException e)
		{
			e.printStackTrace();
		}
		catch (IllegalBlockSizeException e)
		{
			e.printStackTrace();
		}
		catch (BadPaddingException e)
		{
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * 解密
	 * 
	 * @param content
	 *            待解密内容
	 * @param password
	 *            解密密钥
	 * @return
	 */
	public static byte[] decrypt(byte[] content, String password)
	{
		try
		{
			KeyGenerator kgen = KeyGenerator.getInstance("AES");
			kgen.init(128, new SecureRandom(password.getBytes()));
			SecretKey secretKey = kgen.generateKey();
			byte[] enCodeFormat = secretKey.getEncoded();
			SecretKeySpec key = new SecretKeySpec(enCodeFormat, "AES");
			Cipher cipher = Cipher.getInstance("AES");// 创建密码器
			cipher.init(Cipher.DECRYPT_MODE, key);// 初始化
			byte[] result = cipher.doFinal(content);
			return result; // 加密
		}
		catch (NoSuchAlgorithmException e)
		{
			e.printStackTrace();
		}
		catch (NoSuchPaddingException e)
		{
			e.printStackTrace();
		}
		catch (InvalidKeyException e)
		{
			e.printStackTrace();
		}
		catch (IllegalBlockSizeException e)
		{
			e.printStackTrace();
		}
		catch (BadPaddingException e)
		{
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * 将二进制转换成16进制
	 * 
	 * @param buf
	 * @return
	 */
	public static String parseByte2HexStr(byte buf[])
	{
		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < buf.length; i++)
		{
			String hex = Integer.toHexString(buf[i] & 0xFF);
			if (hex.length() == 1)
			{
				hex = '0' + hex;
			}
			sb.append(hex.toUpperCase());
		}
		return sb.toString();
	}

	/**
	 * 将16进制转换为二进制
	 * 
	 * @param hexStr
	 * @return
	 */
	public static byte[] parseHexStr2Byte(String hexStr)
	{
		if (hexStr.length() < 1) return null;
		byte[] result = new byte[hexStr.length() / 2];
		for (int i = 0; i < hexStr.length() / 2; i++)
		{
			int high = Integer.parseInt(hexStr.substring(i * 2, i * 2 + 1), 16);
			int low = Integer.parseInt(hexStr.substring(i * 2 + 1, i * 2 + 2), 16);
			result[i] = (byte) (high * 16 + low);
		}
		return result;
	}

	public static String jia(String con)
	{
		return parseByte2HexStr(encrypt(con, PASS));
	}

	public static String jie(String con)
	{
		return new String(decrypt(parseHexStr2Byte(con), PASS));
	}
	
	
//	public static void main(String[] args)
//	{
//		String aa = "hdl_136";
//		String bb = JavaAES.jia(aa);
//		String cc = JavaAES.jie(bb);
//		System.out.println(bb);
//		System.out.println(cc);
//	}

}
