package com.tzx.clientorder.common.entity;

public class AcewillPosBill {

	/**
	 * 商户ID
	 */
	private String tenancy_id;
	/**
	 * 机构ID
	 */
	private Integer store_id;
	/**
	 * 唯一标识
	 */
	private Integer id;
	/**
	 * 账单编号
	 */
	private String bill_num;
	/**
	 * 批次编号
	 */
	private String batch_num;
	/**
	 * 流水单号
	 */
	private String serial_num;
	/**
	 * 报表日期
	 */
	private String report_date;
	/**
	 * 桌位编号
	 */
	private String table_code;
	/**
	 * 消费客数
	 */
	private Integer guest;
	/**
	 * 开台时间
	 */
	private String opentable_time;
	/**
	 * 结账时间
	 */
	private String payment_time;
	/**
	 * 结账次数
	 */
	private Integer payment_num;
	/**
	 * 开台收款机号
	 */
	private String open_pos_num;
	/**
	 * 结账收款机号
	 */
	private String pos_num;
	/**
	 * 服务员号
	 */
	private String waiter_num;
	/**
	 * 开台操作员
	 */
	private String open_opt;
	/**
	 * 收款员号
	 */
	private String cashier_num;
	/**
	 * 结账班次ID
	 */
	private Integer shift_id;
	/**
	 * 餐谱ID
	 */
	private Integer item_menu_id;
	/**
	 * 服务费种
	 */
	private Integer service_id;
	/**
	 * 服务费金额
	 */
	private Double service_amount;
	/**
	 * 服务费折扣率
	 */
	private Integer service_discount;
	/**
	 * 预订单号
	 */
	private String order_num;
	/**
	 * 预打时间
	 */
	private String print_time;
	/**
	 * 预打账单次数
	 */
	private Integer print_count;
	/**
	 * 项目小计金额
	 */
	private Double subtotal;
	/**
	 * 账单金额
	 */
	private Double bill_amount;
	/**
	 * 付款金额
	 */
	private Double payment_amount;
	/**
	 * 付款差额
	 */
	private Double difference;
	/**
	 * 折扣金额
	 */
	private Double discountk_amount;
	/**
	 * 折让金额
	 */
	private Double discountr_amount;
	/**
	 * 抹零金额
	 */
	private Double maling_amount;
	/**
	 * 单品折扣金额
	 */
	private Double single_discount_amount;
	/**
	 * 优惠金额
	 */
	private Double discount_amount;
	/**
	 * 免单金额
	 */
	private Double free_amount;
	/**
	 * 奉送金额
	 */
	private Double givi_amount;
	/**
	 * 多收礼券
	 */
	private Double more_coupon;
	/**
	 * 人均消费
	 */
	private Double average_amount;
	/**
	 * 优惠人员编号
	 */
	private String discount_num;
	/**
	 * 折扣方案ID折扣
	 */
	private Integer discount_case_id;
	/**
	 * 固定折扣率
	 */
	private Integer discount_rate;
	/**
	 * 免单原因ID
	 */
	private Integer billfree_reason_id;
	/**
	 * 优惠方式ID
	 */
	private Integer discount_mode_id;
	/**
	 * 第三方转账备注
	 */
	private String transfer_remark;
	/**
	 * 销售模式
	 */
	private String sale_mode;
	/**
	 * 账单状态
	 */
	private String bill_state;
	/**
	 * 结账属性/账单属性
	 */
	private String bill_property;
	/**
	 * 上传标记
	 */
	private Integer upload_tag;
	/**
	 * 暂存次数
	 */
	private Integer deposit_count;
	/**
	 * 复制账单编号
	 */
	private String copy_bill_num;
	/**
	 * 渠道来源
	 */
	private String source;
	/**
	 * 员工登陆次数
	 */
	private Integer opt_login_number;
	/**
	 * 客人留言
	 */
	private String guest_msg;
	/**
	 * 积分抵现金额
	 */
	private Double integraloffset;
	/**
	 * 账单备注
	 */
	private String remark;
	/**
	 * 退菜金额
	 */
	private Double return_amount;
	/**
	 * 预付款金额
	 */
	private Double advance_payment_amt;
	/**
	 * 已退预付款金额
	 */
	private Double advance_refund_amt;
	/**
	 * 预付款是否退款标志
	 */
	private String is_refund;
	/**
	 * 预付款方式ID
	 */
	private Integer payment_id;
	/**
	 * 付款号码
	 */
	private String pay_no;
	/**
	 * 付款交易流水号
	 */
	private String third_bill_code;
	/**
	 * 支付状态
	 */
	private String payment_state;
	/**
	 * 强制关单授权人
	 */
	private String payment_manager_num;
	/**
	 * 商家实收
	 */
	private Double shop_real_amount;
	/**
	 * 手续费合计
	 */
	private Double total_fees;
	/**
	 * 第三方平台优惠金额
	 */
	private Double platform_charge_amount;
	/**
	 * 线上|线下付款
	 */
	private String settlement_type;
	/**
	 * 整单备注
	 */
	private String bill_taste;
	/**
	 * 辅助桌位号
	 */
	private String fictitious_table;
	/**
	 * 恢复账单次数
	 */
	private Integer recover_count;
	/**
	 * 税率值
	 */
	private Double tax_rate;
	/**
	 * 菜品计税-税额
	 */
	private Double tax_money;
	/**
	 * 菜品计税-含税金额
	 */
	private Double tax_amount;
	/**
	 * 菜品计税-未税金额
	 */
	private Double no_tax_amount;
	/**
	 * 菜品付款-税额
	 */
	private Double payment_tax_money;
	/**
	 * 菜品付款-未税金额
	 */
	private Double payment_notax;
	/**
	 * 菜品应收-税额
	 */
	private Double bill_tax_money;
	/**
	 * 菜品应收-未税金额
	 */
	private Double bill_notax;
	/**
	 * 服务费-税率值
	 */
	private Double service_tax_rate;
	/**
	 * 服务费-税额
	 */
	private Double service_tax_money;
	/**
	 * 服务费-未税金额
	 */
	private Double service_notax;
	/**
	 * 结算价
	 */
	private Double settlement_price;
	/**
	 * 折扣原因
	 */
	private Integer discount_reason_id;
	/**
	 * 就餐类型
	 */
	private String dinner_type;
	/**
	 * 用户实付
	 */
	private Double coupon_buy_price;
	/**
	 * 券账单净收
	 */
	private Double due;
	/**
	 * 商家优惠承担
	 */
	private Double tenancy_assume;
	/**
	 * 第三方优惠承担
	 */
	private Double third_assume;
	/**
	 * 第三方票券服务费
	 */
	private Double third_fee;

	private String order_source;

	private String payment_source;

	public String getTenancy_id() {
		return tenancy_id;
	}
	public Integer getStore_id() {
		return store_id;
	}
	public Integer getId() {
		return id;
	}
	public String getBill_num() {
		return bill_num;
	}
	public String getBatch_num() {
		return batch_num;
	}
	public String getSerial_num() {
		return serial_num;
	}
	public String getReport_date() {
		return report_date;
	}
	public String getTable_code() {
		return table_code;
	}
	public Integer getGuest() {
		return guest;
	}
	public String getOpentable_time() {
		return opentable_time;
	}
	public String getPayment_time() {
		return payment_time;
	}
	public Integer getPayment_num() {
		return payment_num;
	}
	public String getOpen_pos_num() {
		return open_pos_num;
	}
	public String getPos_num() {
		return pos_num;
	}
	public String getWaiter_num() {
		return waiter_num;
	}
	public String getOpen_opt() {
		return open_opt;
	}
	public String getCashier_num() {
		return cashier_num;
	}
	public Integer getShift_id() {
		return shift_id;
	}
	public Integer getItem_menu_id() {
		return item_menu_id;
	}
	public Integer getService_id() {
		return service_id;
	}
	public Double getService_amount() {
		return service_amount;
	}
	public Integer getService_discount() {
		return service_discount;
	}
	public String getOrder_num() {
		return order_num;
	}
	public String getPrint_time() {
		return print_time;
	}
	public Integer getPrint_count() {
		return print_count;
	}
	public Double getSubtotal() {
		return subtotal;
	}
	public Double getBill_amount() {
		return bill_amount;
	}
	public Double getPayment_amount() {
		return payment_amount;
	}
	public Double getDifference() {
		return difference;
	}
	public Double getDiscountk_amount() {
		return discountk_amount;
	}
	public Double getDiscountr_amount() {
		return discountr_amount;
	}
	public Double getMaling_amount() {
		return maling_amount;
	}
	public Double getSingle_discount_amount() {
		return single_discount_amount;
	}
	public Double getDiscount_amount() {
		return discount_amount;
	}
	public Double getFree_amount() {
		return free_amount;
	}
	public Double getGivi_amount() {
		return givi_amount;
	}
	public Double getMore_coupon() {
		return more_coupon;
	}
	public Double getAverage_amount() {
		return average_amount;
	}
	public String getDiscount_num() {
		return discount_num;
	}
	public Integer getDiscount_case_id() {
		return discount_case_id;
	}
	public Integer getDiscount_rate() {
		return discount_rate;
	}
	public Integer getBillfree_reason_id() {
		return billfree_reason_id;
	}
	public Integer getDiscount_mode_id() {
		return discount_mode_id;
	}
	public String getTransfer_remark() {
		return transfer_remark;
	}
	public String getSale_mode() {
		return sale_mode;
	}
	public String getBill_state() {
		return bill_state;
	}
	public String getBill_property() {
		return bill_property;
	}
	public Integer getUpload_tag() {
		return upload_tag;
	}
	public Integer getDeposit_count() {
		return deposit_count;
	}
	public String getCopy_bill_num() {
		return copy_bill_num;
	}
	public String getSource() {
		return source;
	}
	public Integer getOpt_login_number() {
		return opt_login_number;
	}
	public String getGuest_msg() {
		return guest_msg;
	}
	public Double getIntegraloffset() {
		return integraloffset;
	}
	public String getRemark() {
		return remark;
	}
	public Double getReturn_amount() {
		return return_amount;
	}
	public Double getAdvance_payment_amt() {
		return advance_payment_amt;
	}
	public Double getAdvance_refund_amt() {
		return advance_refund_amt;
	}
	public String getIs_refund() {
		return is_refund;
	}
	public Integer getPayment_id() {
		return payment_id;
	}
	public String getPay_no() {
		return pay_no;
	}
	public String getThird_bill_code() {
		return third_bill_code;
	}
	public String getPayment_state() {
		return payment_state;
	}
	public String getPayment_manager_num() {
		return payment_manager_num;
	}
	public Double getShop_real_amount() {
		return shop_real_amount;
	}
	public Double getTotal_fees() {
		return total_fees;
	}
	public Double getPlatform_charge_amount() {
		return platform_charge_amount;
	}
	public String getSettlement_type() {
		return settlement_type;
	}
	public String getBill_taste() {
		return bill_taste;
	}
	public String getFictitious_table() {
		return fictitious_table;
	}
	public Integer getRecover_count() {
		return recover_count;
	}
	public Double getTax_rate() {
		return tax_rate;
	}
	public Double getTax_money() {
		return tax_money;
	}
	public Double getTax_amount() {
		return tax_amount;
	}
	public Double getNo_tax_amount() {
		return no_tax_amount;
	}
	public Double getPayment_tax_money() {
		return payment_tax_money;
	}
	public Double getPayment_notax() {
		return payment_notax;
	}
	public Double getBill_tax_money() {
		return bill_tax_money;
	}
	public Double getBill_notax() {
		return bill_notax;
	}
	public Double getService_tax_rate() {
		return service_tax_rate;
	}
	public Double getService_tax_money() {
		return service_tax_money;
	}
	public Double getService_notax() {
		return service_notax;
	}
	public Double getSettlement_price() {
		return settlement_price;
	}
	public Integer getDiscount_reason_id() {
		return discount_reason_id;
	}
	public String getDinner_type() {
		return dinner_type;
	}
	public Double getCoupon_buy_price() {
		return coupon_buy_price;
	}
	public Double getDue() {
		return due;
	}
	public Double getTenancy_assume() {
		return tenancy_assume;
	}
	public Double getThird_assume() {
		return third_assume;
	}
	public Double getThird_fee() {
		return third_fee;
	}
	public void setTenancy_id(String tenancy_id) {
		this.tenancy_id = tenancy_id;
	}
	public void setStore_id(Integer store_id) {
		this.store_id = store_id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public void setBill_num(String bill_num) {
		this.bill_num = bill_num;
	}
	public void setBatch_num(String batch_num) {
		this.batch_num = batch_num;
	}
	public void setSerial_num(String serial_num) {
		this.serial_num = serial_num;
	}
	public void setReport_date(String report_date) {
		this.report_date = report_date;
	}
	public void setTable_code(String table_code) {
		this.table_code = table_code;
	}
	public void setGuest(Integer guest) {
		this.guest = guest;
	}
	public void setOpentable_time(String opentable_time) {
		this.opentable_time = opentable_time;
	}
	public void setPayment_time(String payment_time) {
		this.payment_time = payment_time;
	}
	public void setPayment_num(Integer payment_num) {
		this.payment_num = payment_num;
	}
	public void setOpen_pos_num(String open_pos_num) {
		this.open_pos_num = open_pos_num;
	}
	public void setPos_num(String pos_num) {
		this.pos_num = pos_num;
	}
	public void setWaiter_num(String waiter_num) {
		this.waiter_num = waiter_num;
	}
	public void setOpen_opt(String open_opt) {
		this.open_opt = open_opt;
	}
	public void setCashier_num(String cashier_num) {
		this.cashier_num = cashier_num;
	}
	public void setShift_id(Integer shift_id) {
		this.shift_id = shift_id;
	}
	public void setItem_menu_id(Integer item_menu_id) {
		this.item_menu_id = item_menu_id;
	}
	public void setService_id(Integer service_id) {
		this.service_id = service_id;
	}
	public void setService_amount(Double service_amount) {
		this.service_amount = service_amount;
	}
	public void setService_discount(Integer service_discount) {
		this.service_discount = service_discount;
	}
	public void setOrder_num(String order_num) {
		this.order_num = order_num;
	}
	public void setPrint_time(String print_time) {
		this.print_time = print_time;
	}
	public void setPrint_count(Integer print_count) {
		this.print_count = print_count;
	}
	public void setSubtotal(Double subtotal) {
		this.subtotal = subtotal;
	}
	public void setBill_amount(Double bill_amount) {
		this.bill_amount = bill_amount;
	}
	public void setPayment_amount(Double payment_amount) {
		this.payment_amount = payment_amount;
	}
	public void setDifference(Double difference) {
		this.difference = difference;
	}
	public void setDiscountk_amount(Double discountk_amount) {
		this.discountk_amount = discountk_amount;
	}
	public void setDiscountr_amount(Double discountr_amount) {
		this.discountr_amount = discountr_amount;
	}
	public void setMaling_amount(Double maling_amount) {
		this.maling_amount = maling_amount;
	}
	public void setSingle_discount_amount(Double single_discount_amount) {
		this.single_discount_amount = single_discount_amount;
	}
	public void setDiscount_amount(Double discount_amount) {
		this.discount_amount = discount_amount;
	}
	public void setFree_amount(Double free_amount) {
		this.free_amount = free_amount;
	}
	public void setGivi_amount(Double givi_amount) {
		this.givi_amount = givi_amount;
	}
	public void setMore_coupon(Double more_coupon) {
		this.more_coupon = more_coupon;
	}
	public void setAverage_amount(Double average_amount) {
		this.average_amount = average_amount;
	}
	public void setDiscount_num(String discount_num) {
		this.discount_num = discount_num;
	}
	public void setDiscount_case_id(Integer discount_case_id) {
		this.discount_case_id = discount_case_id;
	}
	public void setDiscount_rate(Integer discount_rate) {
		this.discount_rate = discount_rate;
	}
	public void setBillfree_reason_id(Integer billfree_reason_id) {
		this.billfree_reason_id = billfree_reason_id;
	}
	public void setDiscount_mode_id(Integer discount_mode_id) {
		this.discount_mode_id = discount_mode_id;
	}
	public void setTransfer_remark(String transfer_remark) {
		this.transfer_remark = transfer_remark;
	}
	public void setSale_mode(String sale_mode) {
		this.sale_mode = sale_mode;
	}
	public void setBill_state(String bill_state) {
		this.bill_state = bill_state;
	}
	public void setBill_property(String bill_property) {
		this.bill_property = bill_property;
	}
	public void setUpload_tag(Integer upload_tag) {
		this.upload_tag = upload_tag;
	}
	public void setDeposit_count(Integer deposit_count) {
		this.deposit_count = deposit_count;
	}
	public void setCopy_bill_num(String copy_bill_num) {
		this.copy_bill_num = copy_bill_num;
	}
	public void setSource(String source) {
		this.source = source;
	}
	public void setOpt_login_number(Integer opt_login_number) {
		this.opt_login_number = opt_login_number;
	}
	public void setGuest_msg(String guest_msg) {
		this.guest_msg = guest_msg;
	}
	public void setIntegraloffset(Double integraloffset) {
		this.integraloffset = integraloffset;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public void setReturn_amount(Double return_amount) {
		this.return_amount = return_amount;
	}
	public void setAdvance_payment_amt(Double advance_payment_amt) {
		this.advance_payment_amt = advance_payment_amt;
	}
	public void setAdvance_refund_amt(Double advance_refund_amt) {
		this.advance_refund_amt = advance_refund_amt;
	}
	public void setIs_refund(String is_refund) {
		this.is_refund = is_refund;
	}
	public void setPayment_id(Integer payment_id) {
		this.payment_id = payment_id;
	}
	public void setPay_no(String pay_no) {
		this.pay_no = pay_no;
	}
	public void setThird_bill_code(String third_bill_code) {
		this.third_bill_code = third_bill_code;
	}
	public void setPayment_state(String payment_state) {
		this.payment_state = payment_state;
	}
	public void setPayment_manager_num(String payment_manager_num) {
		this.payment_manager_num = payment_manager_num;
	}
	public void setShop_real_amount(Double shop_real_amount) {
		this.shop_real_amount = shop_real_amount;
	}
	public void setTotal_fees(Double total_fees) {
		this.total_fees = total_fees;
	}
	public void setPlatform_charge_amount(Double platform_charge_amount) {
		this.platform_charge_amount = platform_charge_amount;
	}
	public void setSettlement_type(String settlement_type) {
		this.settlement_type = settlement_type;
	}
	public void setBill_taste(String bill_taste) {
		this.bill_taste = bill_taste;
	}
	public void setFictitious_table(String fictitious_table) {
		this.fictitious_table = fictitious_table;
	}
	public void setRecover_count(Integer recover_count) {
		this.recover_count = recover_count;
	}
	public void setTax_rate(Double tax_rate) {
		this.tax_rate = tax_rate;
	}
	public void setTax_money(Double tax_money) {
		this.tax_money = tax_money;
	}
	public void setTax_amount(Double tax_amount) {
		this.tax_amount = tax_amount;
	}
	public void setNo_tax_amount(Double no_tax_amount) {
		this.no_tax_amount = no_tax_amount;
	}
	public void setPayment_tax_money(Double payment_tax_money) {
		this.payment_tax_money = payment_tax_money;
	}
	public void setPayment_notax(Double payment_notax) {
		this.payment_notax = payment_notax;
	}
	public void setBill_tax_money(Double bill_tax_money) {
		this.bill_tax_money = bill_tax_money;
	}
	public void setBill_notax(Double bill_notax) {
		this.bill_notax = bill_notax;
	}
	public void setService_tax_rate(Double service_tax_rate) {
		this.service_tax_rate = service_tax_rate;
	}
	public void setService_tax_money(Double service_tax_money) {
		this.service_tax_money = service_tax_money;
	}
	public void setService_notax(Double service_notax) {
		this.service_notax = service_notax;
	}
	public void setSettlement_price(Double settlement_price) {
		this.settlement_price = settlement_price;
	}
	public void setDiscount_reason_id(Integer discount_reason_id) {
		this.discount_reason_id = discount_reason_id;
	}
	public void setDinner_type(String dinner_type) {
		this.dinner_type = dinner_type;
	}
	public void setCoupon_buy_price(Double coupon_buy_price) {
		this.coupon_buy_price = coupon_buy_price;
	}
	public void setDue(Double due) {
		this.due = due;
	}
	public void setTenancy_assume(Double tenancy_assume) {
		this.tenancy_assume = tenancy_assume;
	}
	public void setThird_assume(Double third_assume) {
		this.third_assume = third_assume;
	}
	public void setThird_fee(Double third_fee) {
		this.third_fee = third_fee;
	}

	public String getOrder_source() {
		return order_source;
	}

	public void setOrder_source(String order_source) {
		this.order_source = order_source;
	}

	public String getPayment_source() {
		return payment_source;
	}

	public void setPayment_source(String payment_source) {
		this.payment_source = payment_source;
	}
}
