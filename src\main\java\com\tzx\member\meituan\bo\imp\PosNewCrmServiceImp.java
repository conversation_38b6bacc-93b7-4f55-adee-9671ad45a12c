package com.tzx.member.meituan.bo.imp;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.tzx.base.common.util.PosPropertyUtil;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.entity.PrintBean;
import com.tzx.framework.common.util.HttpUtil;
import com.tzx.framework.common.util.Tools;
import com.tzx.member.meituan.bo.PosNewCrmService;
import com.tzx.member.meituan.po.springjdbc.dao.PosNewCrmDao;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.base.util.MeiDaSignUtils;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.base.util.ReqDataUtil;
import com.tzx.pos.bo.PosPrintNewService;


@Service(PosNewCrmService.NAME)
public class PosNewCrmServiceImp implements PosNewCrmService {
	
	private static final Logger	logger	= Logger.getLogger("pos_to_crm");
	
	@Autowired
	private PosNewCrmDao posNewCrmDao;
	
	@Resource(name = PosPrintNewService.NAME)
	private PosPrintNewService posPrintNewService;
	
//	@Resource(name = PosDao.NAME)
//	private PosDao				posDao;	
	
	
	private static final String	CHARSET	= "UTF-8";

	private static final String	VERSION	= "1";
	

	/**
	 * 获取TOken
	 */
	@Override
	public Data getToken(Data param) throws Exception {
		// TODO Auto-generated method stub
		String tenantId = param.getTenancy_id();
		Integer storeId = param.getStore_id();
		Map<String, Object> map = ReqDataUtil.getDataMap(param);
		Integer optNum = ParamUtil.getIntegerValue(map, "opt_num", false, null);//登录人员id
		String thirdEmployeeId = ParamUtil.getStringValue(map, "third_employee_id", false, null);//第三方人员id
		String token = ParamUtil.getStringValue(map, "token", false, null);//第三方token
		
		// 把获取的TOken存入数据库中
		StringBuilder sql = new StringBuilder("insert into employee_third_link(tenancy_id,store_id,employee_id,third_employee_id,token) values(?,?,?,?,?)");
		int res = posNewCrmDao.update(sql.toString(), new Object[]
		{ tenantId, storeId, optNum, thirdEmployeeId, token });
		
		Data result=new Data();
		if(res<0){
			result.setCode(Constant.CODE_INNER_EXCEPTION);
			result.setMsg(Constant.GET_CRM_TOKEN_FAILURE);
		}else{
			result.setCode(Constant.CODE_SUCCESS);
			result.setMsg(Constant.GET_CRM_TOKEN_SUCCESS);
		}
		return result;
	}
	
	/**
	 * 发卡
	 */
	@Override
	public Data creditCard(Data param) throws Exception {
		// TODO Auto-generated method stub
		String tenantId = param.getTenancy_id();
		Integer storeId = param.getStore_id();
		Map<String, Object> map = ReqDataUtil.getDataMap(param);
		String cardCode = ParamUtil.getStringValue(map, "card_code", false, null);// 会员卡号
		String pos_num = ParamUtil.getStringValue(map, "pos_num", false, null);// 机具号
		Double deposit = ParamUtil.getDoubleValue(map, "deposit", false, null);// 押金金额
		Double salesPric = ParamUtil.getDoubleValue(map, "sales_pric", false, null);// 售卡金额
		Integer operatorId = ParamUtil.getIntegerValue(map, "operator_id", false, null);// 操作人
		Integer shiftId = ParamUtil.getIntegerValue(map, "shift_id", false, null);// 操作班次
		String businessDateStr = ParamUtil.getStringValue(map, "business_date", false, null);// 操作日期
		String chanel = ParamUtil.getStringValue(map, "chanel", false, null);// 渠道

		Date businessDate = DateUtil.parseDateAll(businessDateStr);
		List<JSONObject> list = posNewCrmDao.findLastData(tenantId);
		Integer cardId = 0;
		Integer customerId = 0;
		if (list != null && list.size() > 0)
		{
			cardId = (Integer) list.get(0).get("card_id"); // 会员卡Id
			customerId = (Integer) list.get(0).get("customer_id"); // 会员Id
		}

		String operatorName = posNewCrmDao.findOperator(tenantId, operatorId);
	
		// 把会员发数据存入数据库中
		StringBuilder sql = new StringBuilder("insert into crm_activation_lost_list(tenancy_id,customer_id,card_id,card_code,operat_type,chanel,store_id,operator,updatetime,deposit,sales_price,shift_id,operator_id) values(?,?,?,?,?,?,?,?,?,?,?,?,?)");
		int res = posNewCrmDao.update(sql.toString(), new Object[]
		{ tenantId, customerId + 1, cardId + 1, cardCode, SysDictionary.OPERAT_TYPE_FK, chanel, storeId, operatorName, businessDate, deposit, salesPric, shiftId, operatorId });

		/** 打印start ***/
		PrintBean pb = new PrintBean();
		pb.setTenancy_id(param.getTenancy_id());
		pb.setStore_id(param.getStore_id());
		pb.setMode("1");
		pb.setPos_num(pos_num);
		pb.setPrint_code(SysDictionary.PRINT_CODE_1013);
		pb.setCard_code(cardCode);
		pb.setOperator(operatorName);
		pb.setUpdatetime(DateUtil.getNowDateYYDDMMHHMMSS());
		pb.setDeposit(deposit == null ? "" : deposit + "");
		pb.setSales_price(salesPric == null ? "" : salesPric + "");

		setPrint(pb, SysDictionary.OPERAT_TYPE_FK);
		/*** 打印 end **/
		
		Data result = new Data();
		if (res < 0)
		{
			result.setCode(Constant.CODE_INNER_EXCEPTION);
			result.setMsg(Constant.GET_CRM_FK_FAILURE);
		}
		else
		{
			result.setCode(Constant.CODE_SUCCESS);
			result.setMsg(Constant.GET_CRM_FK_SUCCESS);
		}
		return result;
	}
	
	/**
	 *  充值
	 */
	@Override
	public Data recharge(Data param) throws Exception {
		// TODO Auto-generated method stub
		String tenantId = param.getTenancy_id();
		Integer storeId = param.getStore_id();
		Map<String, Object> map = ReqDataUtil.getDataMap(param);
		String bill_code = ParamUtil.getStringValue(map, "bill_code", false, null);//交易流水号
		String pos_num = ParamUtil.getStringValue(map, "pos_num", false, null);//交易流水号
		String card_code = ParamUtil.getStringValue(map, "card_code", false, null);//会员卡号
		String customer_name = ParamUtil.getStringValue(map, "customer_name", false, null);//会员姓名
		String mobil = ParamUtil.getStringValue(map, "mobil", false, null);//会员手机号
		Double pay_money = ParamUtil.getDoubleValue(map, "pay_money", false, null);//充值金额
		Double gift_money = ParamUtil.getDoubleValue(map, "gift_money", false, null);//赠送金额
		Integer payment_id = ParamUtil.getIntegerValue(map, "payment_id", false, null);//充值方式
		String pay_no = ParamUtil.getStringValue(map, "pay_no", false, null);//支付流水号
		String total_balance = ParamUtil.getStringValue(map, "total_balance", false, null);//卡内余额
		Double main_balance = ParamUtil.getDoubleValue(map, "main_balance", false, null);//主账户金额
		Double reward_balance = ParamUtil.getDoubleValue(map, "reward_balance", false, null);//赠送账户金额
		String member_credit = ParamUtil.getStringValue(map, "member_credit", false, null);//会员积分
		Integer operator_id = ParamUtil.getIntegerValue(map, "operator_id", false, null);//操作人
		Integer shift_id = ParamUtil.getIntegerValue(map, "shift_id", false, null);//操作班次
		String business_dateStr = ParamUtil.getStringValue(map, "business_date", false, null);//操作日期
		String chanel = ParamUtil.getStringValue(map, "chanel", false, null);//渠道
		Date business_date=DateUtil.parseDateAll(business_dateStr);
		
		List<JSONObject> list=posNewCrmDao.findDataByCode(tenantId,card_code);
		Integer cardId=null;//会员卡ID	
		Double deposit=null;//押金金额
		Integer customer_id=null; //会员Id
		if(list!=null&&list.size()>0){
			cardId=list.get(0).getInt("card_id");
			deposit=list.get(0).getDouble("deposit");
			customer_id=list.get(0).getInt("customer_id"); 
		}		
		
		String operator=posNewCrmDao.findOperator(tenantId,operator_id);			
		String third_bill_code=pay_no;	//三方账单号
		int activity_id=0;	//活动Id
	
		Integer card_class_id=3;//卡类型Id
		String is_invoice="0"; //是否要发票
		Object[] objs =new Object[]{tenantId,cardId,card_code,bill_code,chanel,storeId,business_date,pay_money,gift_money,SysDictionary.OPERAT_TYPE_CZ,main_balance,reward_balance,deposit,operator,new Date(),pay_money,third_bill_code,activity_id,customer_id,card_class_id,customer_name,mobil,operator_id,shift_id,pay_money,is_invoice,pos_num};
		double rexchange_rate=1;
		Object[] payMentObjs=new Object[]{
				tenantId,cardId,payment_id,bill_code,pay_money,pay_no,rexchange_rate,pay_money,bill_code
		};
		int re=posNewCrmDao.paymentType("1", payMentObjs);
		int res=-1;
		if(re>=0){
			StringBuilder sql = new StringBuilder();
			sql.append("insert into crm_card_trading_list(tenancy_id,card_id,card_code,bill_code,chanel,store_id,business_date,main_trading,reward_trading,operat_type,main_original,reward_original,deposit,operator,operate_time,bill_money,third_bill_code,activity_id,customer_id,card_class_id,name,mobil,operator_id,shift_id,invoice_balance,is_invoice,pos_num) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
			res=posNewCrmDao.update(sql.toString(), objs);
		
		}
		
		
		/**打印start***/
		PrintBean pb=new PrintBean();
		pb.setTenancy_id(param.getTenancy_id());
		pb.setStore_id(param.getStore_id());
		pb.setMode("1");
		pb.setPos_num(pos_num);
		pb.setPrint_code(SysDictionary.PRINT_CODE_1008);
		pb.setCard_code(card_code);
		pb.setOperator(operator);
		pb.setUpdatetime(DateUtil.getNowDateYYDDMMHHMMSS());
		pb.setBill_code(bill_code);
		pb.setName(customer_name);
		pb.setMobil(mobil);
		pb.setTotal_balance(total_balance==null?"":total_balance+"");
		pb.setMain_balance(main_balance==null?"":main_balance+"");
		pb.setReward_balance(reward_balance==null?"":reward_balance+"");
		pb.setUseful_credit(member_credit);
		pb.setIncome(pay_money==null?"":pay_money+"");
		pb.setPayment_name1(payment_id==null?"":getPayName(payment_id));
		
		setPrint(pb,SysDictionary.OPERAT_TYPE_CZ);
		/***打印 end **/
		Data result=new Data();
		if(res>=0){
			result.setCode(Constant.CODE_SUCCESS);
			result.setMsg(Constant.GET_CRM_CZ_SUCCESS);
		}else{
			result.setCode(Constant.CODE_INNER_EXCEPTION);
			result.setMsg(Constant.GET_CRM_CZ_FAILURE);
		}
	
		return result;
	}
		
	
	
	
	/**
	 * 充值撤销
	 */
	@Override
	public Data cancelRecharge(Data param) throws Exception {
		// TODO Auto-generated method stub
		String tenantId = param.getTenancy_id();
		Integer storeId = param.getStore_id();
		Map<String, Object> map = ReqDataUtil.getDataMap(param);
		String bill_code_original=ParamUtil.getStringValue(map, "bill_code_original", false, null);//原交易流水号
		String pos_num = ParamUtil.getStringValue(map, "pos_num", false, null);//机具号
		String bill_code = ParamUtil.getStringValue(map, "bill_code", false, null);//交易流水号
		String card_code = ParamUtil.getStringValue(map, "card_code", false, null);//会员卡号
		String customer_name = ParamUtil.getStringValue(map, "customer_name", false, null);//会员姓名
		String mobil = ParamUtil.getStringValue(map, "mobil", false, null);//会员手机号
		Double pay_money = ParamUtil.getDoubleValue(map, "pay_money", false, null);//充值金额
		Double gift_money = ParamUtil.getDoubleValue(map, "gift_money", false, null);//赠送金额
		Integer payment_id = ParamUtil.getIntegerValue(map, "payment_id", false, null);//充值方式
		String pay_no = ParamUtil.getStringValue(map, "pay_no", false, null);//支付流水号
		String total_balance = ParamUtil.getStringValue(map, "total_balance", false, null);//卡内余额
		Double main_balance = ParamUtil.getDoubleValue(map, "main_balance", false, null);//主账户金额
		Double reward_balance = ParamUtil.getDoubleValue(map, "reward_balance", false, null);//赠送账户金额
		String member_credit = ParamUtil.getStringValue(map, "member_credit", false, null);//会员积分
		Integer operator_id = ParamUtil.getIntegerValue(map, "operator_id", false, null);//操作人
		Integer shift_id = ParamUtil.getIntegerValue(map, "shift_id", false, null);//操作班次
		String business_dateStr = ParamUtil.getStringValue(map, "business_date", false, null);//操作日期
		String chanel = ParamUtil.getStringValue(map, "chanel", false, null);//渠道
		Date business_date=DateUtil.parseDateAll(business_dateStr);
		List<JSONObject> list=posNewCrmDao.findDataByCode(tenantId,card_code);
		Integer cardId=null;//会员卡ID	
		Double deposit=null;//押金金额
		Integer customer_id=null; //会员Id
		if(list!=null&&list.size()>0){
			cardId=list.get(0).getInt("card_id");
			deposit=list.get(0).getDouble("deposit");
			customer_id=list.get(0).getInt("customer_id"); 
		}		
		
		String operator=posNewCrmDao.findOperator(tenantId,operator_id);				
		String third_bill_code="";	//三方账单号
		Integer activity_id=0;	//活动Id
		Integer card_class_id=3;//卡类型Id
		
		double rexchange_rate=1;
		Object[] objs =new Object[]{tenantId,cardId,card_code,bill_code,chanel,storeId,business_date,pay_money,gift_money,SysDictionary.OPERAT_TYPE_FCZ,main_balance,reward_balance,deposit,operator,new Date(),pay_money,third_bill_code,bill_code_original,activity_id,customer_id,new Date(),new Date(),card_class_id,customer_name,mobil,operator_id,shift_id,null,null,"4","02","02",pos_num};
		Object[] payMentObjs=new Object[]{
				tenantId,cardId,payment_id,bill_code,pay_money,pay_no,rexchange_rate,pay_money,"",new Date(),new Date()
		};
		int re=posNewCrmDao.paymentType("2", payMentObjs);
		int res=-1;
		if(re>=0){
			StringBuilder sql = new StringBuilder();
			sql.append("insert into crm_card_trading_list(tenancy_id,card_id,card_code,bill_code,chanel,store_id,business_date,main_trading,reward_trading,operat_type,main_original,reward_original,deposit,operator,operate_time,bill_money,third_bill_code,bill_code_original,activity_id,customer_id,last_updatetime,store_updatetime,card_class_id,name,mobil,operator_id,shift_id,invoice_balance,is_invoice,payment_state,recharge_state,request_status,pos_num) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
			 res=posNewCrmDao.update(sql.toString(), objs);
		
		}
		
		/**打印start***/
		PrintBean pb=new PrintBean();
		pb.setTenancy_id(param.getTenancy_id());
		pb.setStore_id(param.getStore_id());
		pb.setMode("1");
		pb.setPos_num(pos_num);
		pb.setPrint_code(SysDictionary.PRINT_CODE_1009);
		pb.setCard_code(card_code);
		pb.setOperator(operator);
		pb.setUpdatetime(DateUtil.getNowDateYYDDMMHHMMSS());
		pb.setBill_code(bill_code);
		pb.setName(customer_name);
		pb.setMobil(mobil);
		pb.setTotal_balance(total_balance==null?"":total_balance+"");
		pb.setMain_balance(main_balance==null?"":main_balance+"");
		pb.setReward_balance(reward_balance==null?"":reward_balance+"");
		pb.setUseful_credit(member_credit);
		pb.setIncome(pay_money==null?"":pay_money+"");
		pb.setPayment_name1(payment_id==null?"":getPayName(payment_id));
		setPrint(pb,SysDictionary.OPERAT_TYPE_FCZ);
		/***打印 end **/
		
		Data result=new Data();
		if(res>=0){
			result.setCode(Constant.CODE_SUCCESS);
			result.setMsg(Constant.GET_CRM_CANCEL_SUCCESS);
		}else{
			result.setCode(Constant.CODE_INNER_EXCEPTION);
			result.setMsg(Constant.GET_CRM_CANCEL_FAILURE);
		}
	
		
		return result;
	}

	@Override
	public Data findUserInfo(Data param) throws Exception {
		// TODO Auto-generated method stub
		Data result=new Data();
		
		Timestamp currentTime = DateUtil.currentTimestamp();
		
		String tenancyId = param.getTenancy_id();
		int storeId = param.getStore_id();		
				
		Map<String, Object> map = ReqDataUtil.getDataMap(param);
		String cardCode=ParamUtil.getStringValue(map, "card_code", false, null);
		Integer operator_id=ParamUtil.getIntegerValue(map, "opt_num", false, null);
		
		if(operator_id==null||"".equals(operator_id.toString())){
			result.setCode(Constant.CODE_PARAM_FAILURE);
			result.setMsg("operator_id  不能为空");
			return result;
		}
				
//		String appAuthToken=posNewCrmDao.getTokenByThirdLink(tenancyId,storeId,operator_id);
//		if(appAuthToken==null||"".equals(appAuthToken)){
//			result.setCode(Constant.CODE_PARAM_FAILURE);
//			result.setMsg("appAuthToken  不能为空");
//			return result;
//		}
//		
//		String timeStr = Long.toString(currentTime.getTime());
//		String storeIdStr = Integer.toString(storeId);
		
		Map<String, String> params=new HashMap<>();
		params.put("memberNo", cardCode);
//		params.put("appAuthToken", appAuthToken);
//		params.put("version", VERSION);
//		params.put("charset", CHARSET);
//		params.put("timestamp", timeStr);
//		params.put("ePoiId", storeIdStr+tenancyId);
//		
//		String sign=MeiDaSignUtils.createSign(PosPropertyUtil.getMsg("xmdcrm.signkey"), params);//获取SIgn
//		
//		
//		String url=PosPropertyUtil.getMsg("xmdcrm.request.url")+MeiDaSignUtils.MEIDA_GET_USERINFO_URL+"?memberNo="+cardCode+"&sign="+sign+"&appAuthToken="+appAuthToken+"&version=1&charset=UTF-8&timestamp="+timeStr+"&ePoiId="+storeIdStr+tenancyId;
//		
//		String reString=HttpUtil.sendGetRequest(url, "UTF-8");
//		// 请求美大会员支付做异常处理
//		if(StringUtils.isEmpty(reString)){
//			result.setCode(Constant.CODE_CONN_EXCEPTION);
//			result.setMsg("美大会员支付请求异常");
//			return result;
//		}
//		JSONObject jsonObject = null;
//		try{
//			jsonObject = JSONObject.fromObject(reString);
//		}catch(Exception e){
//			result.setCode(Constant.CODE_PARAM_FAILURE);
//			result.setMsg("解析参数异常");
//			return result;
//		}
		
		JSONObject jsonObject = this.sendGetRequest(MeiDaSignUtils.MEIDA_GET_USERINFO_URL, tenancyId, storeId, String.valueOf(operator_id), currentTime, params);
		
		if (!jsonObject.has("data"))
		{// 失败
			JSONObject errorJson = (JSONObject) jsonObject.get("error");
			String message = errorJson.getString("message");
			result.setCode(Constant.CODE_PARAM_FAILURE);
			result.setMsg(message);
		}
		else
		{// 成功
			JSONObject dataJson = (JSONObject) jsonObject.get("data");
			String mobile = dataJson.getString("mobile");

			String name = getReturnStr(dataJson, "name");
			String memberNo = getReturnStr(dataJson, "memberNo");
			String customerNo = getReturnStr(dataJson, "customerNo");
			String memberLevel = getReturnStr(dataJson, "memberLevel");
			boolean userMemberPrice = dataJson.getBoolean("useMemberPrice");
			String discount_rate = getReturnStr(dataJson, "discount");
			String cardNumber = memberNo;
			if (cardNumber == null || "".equals(cardNumber))
			{
				cardNumber = customerNo;
			}
			
			JSONObject returnObject = new JSONObject();
			returnObject.put("mobil", mobile);
			returnObject.put("customer_name", name);
			returnObject.put("customer_code", memberNo);
			returnObject.put("card_code", cardNumber);
			returnObject.put("customer_leve", memberLevel);
			returnObject.put("discount_rate", discount_rate);
			returnObject.put("customer_price", userMemberPrice);
			
			List<JSONObject> ls = new ArrayList<JSONObject>();
			ls.add(returnObject);

			result.setData(ls);
			result.setCode(Constant.CODE_SUCCESS);
			result.setMsg(Constant.CRM_MEMBER_SUCCESS);
		}
		return result;
	}
	
	public String getReturnStr(JSONObject jsonObject,String name){
		 if(!jsonObject.has(name)){
			return "";
		 }
		 return jsonObject.getString(name);
	}
	/**
	 * 付款
	 */
	@Override
	public Data paymentCRM(String tenantId,Integer storeId,Map<String, Object> map) throws Exception {

		// TODO Auto-generated method stub
//		String tenantId = param.getTenancy_id();
//		Integer storeId = param.getStore_id();
//		Map<String, Object> map = ReqDataUtil.getDataMap(param);
		String bill_code = ParamUtil.getStringValue(map, "bill_code", false, null);//交易流水号
		String pos_num = ParamUtil.getStringValue(map, "pos_num", false, null);//机具号
		String card_code = ParamUtil.getStringValue(map, "card_code", false, null);//会员卡号
		String customer_name = ParamUtil.getStringValue(map, "customer_name", false, null);//会员姓名
		String mobil = ParamUtil.getStringValue(map, "mobil", false, null);//会员手机号
		Double pay_money = ParamUtil.getDoubleValue(map, "pay_money", false, null);//充值金额
		Double gift_money = ParamUtil.getDoubleValue(map, "gift_money", false, null);//赠送金额
		Integer payment_id = ParamUtil.getIntegerValue(map, "payment_id", false, null);//充值方式
		String pay_no = ParamUtil.getStringValue(map, "pay_no", false, null);//支付流水号
		String total_balance = ParamUtil.getStringValue(map, "total_balance", false, null);//卡内余额
		Double main_balance = ParamUtil.getDoubleValue(map, "main_balance", false, null);//主账户金额
		Double reward_balance = ParamUtil.getDoubleValue(map, "reward_balance", false, null);//赠送账户金额
		String member_credit = ParamUtil.getStringValue(map, "member_credit", false, null);//会员积分
		Integer operator_id = ParamUtil.getIntegerValue(map, "operator_id", false, null);//操作人
		Integer shift_id = ParamUtil.getIntegerValue(map, "shift_id", false, null);//操作班次
		String business_dateStr = ParamUtil.getStringValue(map, "business_date", false, null);//操作日期
		String chanel = ParamUtil.getStringValue(map, "chanel", false, null);//渠道
		Date business_date=DateUtil.parseDateAll(business_dateStr);
		
		List<JSONObject> list=posNewCrmDao.findDataByCode(tenantId,card_code);
		Integer cardId=null;//会员卡ID	
		Double deposit=null;//押金金额
		Integer customer_id=null; //会员Id
		if(list!=null&&list.size()>0){
			cardId=list.get(0).getInt("card_id");
			deposit=list.get(0).getDouble("deposit");
			customer_id=list.get(0).getInt("customer_id"); 
		}	
		String operator=posNewCrmDao.findOperator(tenantId,operator_id);			
		String third_bill_code=pay_no;	//三方账单号
		int activity_id=0;	//活动Id
//		Integer customer_id=list.get(0).getInt("customer_id");  //会员Id
		Integer card_class_id=3;//卡类型Id
		String is_invoice="0"; //是否要发票
		Object[] objs =new Object[]{tenantId,cardId,card_code,bill_code,chanel,storeId,business_date,pay_money,gift_money,SysDictionary.OPERAT_TYPE_XF,main_balance,reward_balance,deposit,operator,new Date(),pay_money,third_bill_code,activity_id,customer_id,card_class_id,customer_name,mobil,operator_id,shift_id,pay_money,is_invoice,pos_num};
		double rexchange_rate=1;
		Object[] payMentObjs=new Object[]{
				tenantId,cardId,payment_id,bill_code,pay_money,pay_no,rexchange_rate,pay_money,bill_code
		};
		int re=posNewCrmDao.paymentType("1", payMentObjs);
		int res=-1;
		if(re>=0){
			StringBuilder sql = new StringBuilder();
			sql.append("insert into crm_card_trading_list(tenancy_id,card_id,card_code,bill_code,chanel,store_id,business_date,main_trading,reward_trading,operat_type,main_original,reward_original,deposit,operator,operate_time,bill_money,third_bill_code,activity_id,customer_id,card_class_id,name,mobil,operator_id,shift_id,invoice_balance,is_invoice,pos_num) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
			res=posNewCrmDao.update(sql.toString(), objs);
		
		}
		
		/**打印start***/
		PrintBean pb=new PrintBean();
		pb.setTenancy_id(tenantId);
		pb.setStore_id(storeId);
		pb.setMode("1");
		pb.setPos_num(pos_num);
		pb.setPrint_code(SysDictionary.PRINT_CODE_1010);
		pb.setCard_code(card_code);
		pb.setOperator(operator);
		pb.setUpdatetime(DateUtil.getNowDateYYDDMMHHMMSS());
		pb.setBill_code(bill_code);
		pb.setName(customer_name);
		pb.setMobil(mobil);
		pb.setTotal_balance(total_balance==null?"":total_balance+"");
		pb.setMain_balance(main_balance==null?"":main_balance+"");
		pb.setReward_balance(reward_balance==null?"":reward_balance+"");
		pb.setUseful_credit(member_credit);
		pb.setCard_class_name("会员卡");
		pb.setConsume_cardmoney(pay_money==null?"":pay_money+"");
		setPrint(pb,SysDictionary.OPERAT_TYPE_XF);
		/***打印 end **/
		
		Data result=new Data();
		if(res>=0){
			result.setCode(Constant.CODE_SUCCESS);
			result.setMsg(Constant.PAY_MENT_CRM_SUCCESS);
		}else{
			result.setCode(Constant.CODE_INNER_EXCEPTION);
			result.setMsg(Constant.PAY_MENT_CRM_FAILURE);
		}
	
		return result;
	
	}
	/**
	 * 付款取消
	 */
	@Override
	public void cancelPayMent(String tenantId,Integer storeId,JSONObject para,Data resultData) throws Exception {
		// TODO Auto-generated method stub
		//通知美大会员退款
//		long timestamp = System.currentTimeMillis();
		Timestamp currentTime = DateUtil.currentTimestamp();
		
//		String tenancyId = param.getTenancy_id();
//		int storeId = param.getStore_id();
		
		String optNum = para.optString("cashier_num");
		String billId = para.optString("bill_code");
		Double amount = para.optDouble("currency_amount");
		
		if(amount.isNaN())
		{
			amount = 0d;
		}

//		String appAuthToken = posNewCrmDao.getTokenByThirdLink(tenantId, storeId, Integer.parseInt(optNum));
		
//		String xmdCrmUrl = paymentDao.getSysParameter(tenancyId, storeId, "XMDCRMMKQQJKURL");//美大CRM模块请求接口URL
//		//查询token;查询订单
//		String tokenSql = new String(
//				"select third_employee_id,token from employee_third_link where tenancy_id=? and store_id = ? and employee_id =?");
//		JSONObject json = paymentDao.query4Json(tenancyId, tokenSql, new Object[]
//		{ tenancyId, storeId, Integer.parseInt(optNum) }).get(0);
//		String appAuthToken = json.optString("token");
//		String charset = "UTF-8";
//		long timestamp = new Date().getTime();
		//查询第三方支付表记录
//		if(!Tools.hv(payment.optString("bill_code")) || !"null".equals(payment.optString("bill_code")))//美大会员第三方流水号
//		{
//			billId = payment.optString("bill_code");
//		}else{
//			String billsql = new String("select bill_code from pos_bill_payment where tenancy_id=? and store_id=? and  bill_num=?");
//			JSONObject jsonb = paymentDao.query4Json(tenancyId, billsql, new Object[]
//					{ tenancyId, storeId, billNum }).get(0);
//			billId = jsonb.optString("bill_code");
//		}
//		
//		if(StringUtils.isEmpty(billId)|| "null".equals(billId)){
//			//还未支付，取消支付，不用退款
//		}else{
//			String signKey = PosPropertyUtil.getMsg("xmdcrm.signkey");//posNewCrmDao.getSignKen(tenancyId,storeId);
//			if(StringUtils.isEmpty(signKey)){
//				//signKey = "s4p6di49nbbkt2ej";
//				throw new Exception("美大signKey不存在！");
//			}
		
		Map<String, String> params = new HashMap<String, String>();// 组织参数
//		params.put("appAuthToken", appAuthToken);
//		params.put("charset", CHARSET);
//		params.put("version", VERSION);
//		params.put("timestamp", Long.toString(timestamp));
		params.put("billId", billId);
//		params.put("ePoiId", String.valueOf(storeId)+tenantId);
//		String sign = MeiDaSignUtils.createSign(PosPropertyUtil.getMsg("xmdcrm.signkey"), params);
//		params.put("sign", sign);

//		String reqURL = PosPropertyUtil.getMsg("xmdcrm.request.url") + MeiDaSignUtils.MEIDA_DEAL_CANCEL_URL;
//		JSONObject paramJson = JSONObject.fromObject(params);
//		logger.info(String.valueOf(timestamp) + "支付流水号：" + billId + "<美大退款发送接口参数==>" + paramJson);
//		String result = HttpUtil.sendHttpPostRequest(reqURL, params);
//		logger.info(String.valueOf(timestamp) + "支付流水号：" + billId + "<发送美大退款接口返回体==>" + result);

		JSONObject jsonresult = this.sendPostRequest(MeiDaSignUtils.MEIDA_DEAL_CANCEL_URL, tenantId, storeId, optNum, currentTime, params); 
		
		if (null != jsonresult && jsonresult.containsKey("error"))
		{
//			JSONObject jsonresult = JSONObject.fromObject(result);
			String error = jsonresult.optString("error");
			JSONObject jsonerror = JSONObject.fromObject(error);
			resultData.setCode(Integer.parseInt(jsonerror.optString("code")));
			resultData.setMsg(jsonerror.optString("message"));
			throw new Exception(jsonerror.optString("message"));
		}
		else
		{
			resultData.setCode(Constant.CODE_SUCCESS);
		}
		
		JSONObject resultJson = new JSONObject();
		if (null != jsonresult && jsonresult.containsKey("data")){
			resultJson = jsonresult.optJSONObject("data");
//			JSONArray resultJsonArray = JSONObject.fromObject(result).optJSONArray("data");
//			if(null != resultJsonArray && resultJsonArray.size()>0)
//			{
//				resultJson = resultJsonArray.getJSONObject(0);
//			}
		}
		// 通知会员明细退款
		Map<String, Object> paramJsonCrm = new HashMap<String, Object>();
			
		paramJsonCrm.put("bill_code", resultJson.optString("billId"));// 交易流水号
		paramJsonCrm.put("card_code", StringUtils.isEmpty(resultJson.optString("memberNo")) ? para.optString("phone") : resultJson.optString("memberNo"));// 会员卡号
		paramJsonCrm.put("customer_name", null);// 会员姓名
		paramJsonCrm.put("mobil", para.optString("phone"));// 会员手机号
		
		paramJsonCrm.put("gift_money", 0d);// 赠送金额
		paramJsonCrm.put("total_balance", 0d);// 卡内余额
		paramJsonCrm.put("main_balance", 0d);// 主账户金额
		paramJsonCrm.put("reward_balance", 0d);// 赠送账户金额
		paramJsonCrm.put("member_credit", 0d);// 会员积分
		
		paramJsonCrm.put("bill_code_original", billId);// 原交易流水号
		paramJsonCrm.put("operator_id", optNum);// 操作人
		paramJsonCrm.put("pay_money",amount);// 充值金额
		
		paramJsonCrm.put("pos_num", para.optString("pos_num"));// 机具号
		paramJsonCrm.put("shift_id", para.optString("shift_id"));// 操作班次
		paramJsonCrm.put("business_date", para.optString("report_date"));// 操作日期
		paramJsonCrm.put("chanel", para.optString("chanel"));// 渠道
		paramJsonCrm.put("third_bill_code", para.optString("bill_num"));// 渠道
		
		paramJsonCrm.put("payment_id", "");// 充值方式
		paramJsonCrm.put("pay_no", "");// 支付流水号
			
		this.cancelXMDCRMPayment(tenantId, storeId, paramJsonCrm);
		
//			JSONObject paramJsonCrm = new JSONObject();
//			paramJsonCrm.put("bill_code", billId);
//			paramJsonCrm.put("card_code", "");
//			paramJsonCrm.put("customer_name", "");
//			paramJsonCrm.put("pay_money", amount);
//			List<JSONObject> requestList = new ArrayList<JSONObject>();
//			requestList.add(paramJsonCrm);
//			
//			Data requestData = Data.get(tenantId, storeId, 0);
//			requestData.setType(Type.CANCEL_PAYMENT);
//			requestData.setOper(Oper.cancle);
//			requestData.setData(requestList);
//			this.cancelXMDCRMPayment(requestData);
//			
//			logger.info(String.valueOf(t)+"<退款通知会员明细发送接口参数==>" + JSONObject.fromObject(requestData));
//			Data resultDatas = posNewCrmService.cancelPayMent(requestData);
//			logger.info(String.valueOf(t)+"<退款通知会员明细接口返回体==>" + JSONObject.fromObject(resultDatas));
//		}

	}
	
	public Data cancelXMDCRMPayment(String tenantId,Integer storeId,Map<String, Object> map) throws Exception {
		// TODO Auto-generated method stub
		String bill_code_original=ParamUtil.getStringValue(map, "bill_code_original", false, null);//原交易流水号
		String pos_num = ParamUtil.getStringValue(map, "pos_num", false, null);//机具号
		String bill_code = ParamUtil.getStringValue(map, "bill_code", false, null);//交易流水号
		String card_code = ParamUtil.getStringValue(map, "card_code", false, null);//会员卡号
		String customer_name = ParamUtil.getStringValue(map, "customer_name", false, null);//会员姓名
		String mobil = ParamUtil.getStringValue(map, "mobil", false, null);//会员手机号
		Double pay_money = ParamUtil.getDoubleValue(map, "pay_money", false, null);//充值金额
		Double gift_money = ParamUtil.getDoubleValue(map, "gift_money", false, null);//赠送金额
		Integer payment_id = ParamUtil.getIntegerValue(map, "payment_id", false, null);//充值方式
		String pay_no = ParamUtil.getStringValue(map, "pay_no", false, null);//支付流水号
		String total_balance = ParamUtil.getStringValue(map, "total_balance", false, null);//卡内余额
		Double main_balance = ParamUtil.getDoubleValue(map, "main_balance", false, null);//主账户金额
		Double reward_balance = ParamUtil.getDoubleValue(map, "reward_balance", false, null);//赠送账户金额
		String member_credit = ParamUtil.getStringValue(map, "member_credit", false, null);//会员积分
		Integer operator_id = ParamUtil.getIntegerValue(map, "operator_id", false, null);//操作人
		Integer shift_id = ParamUtil.getIntegerValue(map, "shift_id", false, null);//操作班次
		String business_dateStr = ParamUtil.getStringValue(map, "business_date", false, null);//操作日期
		String chanel = ParamUtil.getStringValue(map, "chanel", false, null);//渠道
		String third_bill_code=ParamUtil.getStringValue(map, "third_bill_code", false, null);	//三方账单号
		Date business_date=DateUtil.parseDateAll(business_dateStr);
		List<JSONObject> list=posNewCrmDao.findDataByCode(tenantId,card_code);
		Integer cardId=null;//会员卡ID	
		Double deposit=null;//押金金额
		Integer customer_id=null; //会员Id
		if(list!=null&&list.size()>0){
			cardId=list.get(0).getInt("card_id");
			deposit=list.get(0).getDouble("deposit");
			customer_id=list.get(0).getInt("customer_id"); 
		}	
		String operator=posNewCrmDao.findOperator(tenantId,operator_id);				
		
		Integer activity_id=0;	//活动Id
//		Integer customer_id=list.get(0).getInt("customer_id");
		Integer card_class_id=3;//卡类型Id
		
		double rexchange_rate=1;
		Object[] objs =new Object[]{tenantId,cardId,card_code,bill_code,chanel,storeId,business_date,pay_money,gift_money,SysDictionary.OPERAT_TYPE_FXF,main_balance,reward_balance,deposit,operator,new Date(),pay_money,third_bill_code,bill_code_original,activity_id,customer_id,new Date(),new Date(),card_class_id,customer_name,mobil,operator_id,shift_id,null,null,"4","02","02",pos_num};
		Object[] payMentObjs=new Object[]{
				tenantId,cardId,payment_id,bill_code,pay_money,pay_no,rexchange_rate,pay_money,"",new Date(),new Date()
		};
		int re=posNewCrmDao.paymentType("2", payMentObjs);
		int res=-1;
		if(re>=0){
			StringBuilder sql = new StringBuilder();
			sql.append("insert into crm_card_trading_list(tenancy_id,card_id,card_code,bill_code,chanel,store_id,business_date,main_trading,reward_trading,operat_type,main_original,reward_original,deposit,operator,operate_time,bill_money,third_bill_code,bill_code_original,activity_id,customer_id,last_updatetime,store_updatetime,card_class_id,name,mobil,operator_id,shift_id,invoice_balance,is_invoice,payment_state,recharge_state,request_status,pos_num) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
			 res=posNewCrmDao.update(sql.toString(), objs);
		
		}
		/**打印start***/
		PrintBean pb=new PrintBean();
		pb.setTenancy_id(tenantId);
		pb.setStore_id(storeId);
		pb.setMode("1");
		pb.setPos_num(pos_num);
		pb.setPrint_code(SysDictionary.PRINT_CODE_1011);
		pb.setCard_code(card_code);
		pb.setOperator(operator);
		pb.setUpdatetime(DateUtil.getNowDateYYDDMMHHMMSS());
		pb.setBill_code(bill_code);
		pb.setName(customer_name);
		pb.setMobil(mobil);
		pb.setTotal_balance(total_balance==null?"":total_balance+"");
		pb.setMain_balance(main_balance==null?"":main_balance+"");
		pb.setReward_balance(reward_balance==null?"":reward_balance+"");
		pb.setUseful_credit(member_credit);
		pb.setCard_class_name("会员卡");
		pb.setMain_trading(pay_money==null?"":pay_money+"");
		this.setPrint(pb,SysDictionary.OPERAT_TYPE_FXF);
		/***打印 end **/
		Data result=new Data();
		if(res>=0){
			result.setCode(Constant.CODE_SUCCESS);
			result.setMsg(Constant.CANCEL_PAYMENT_SUCCESS);
		}else{
			result.setCode(Constant.CODE_INNER_EXCEPTION);
			result.setMsg(Constant.CANCEL_PAYMENT_FAILURE);
		}
		return result;
	}
	
	/**
	 * 打印小票
	 * @param printJson
	 * @param param
	 * @throws Exception
	 */
	public void setPrint(PrintBean param,String type) throws Exception{
		JSONObject printJson=new JSONObject();
//		printJson.put("mode", "1");
//		printJson.put("pos_num", "00001");
//		printJson.put("print_code", "1008");
		
		printJson.put("card_code", param.getCard_code());
		printJson.put("operator", param.getOperator());
		printJson.put("updatetime", param.getUpdatetime());
		
		if(type.equals(SysDictionary.OPERAT_TYPE_FK)){//发卡
			printJson.put("deposit", param.getDeposit());
			printJson.put("sales_price", param.getSales_price());
		}else{
			printJson.put("bill_code", param.getBill_code());
			printJson.put("name", param.getName());
			printJson.put("mobil", param.getMobil());
			printJson.put("total_balance", param.getTotal_balance()==null?"":param.getTotal_balance());
			printJson.put("main_balance", param.getMain_balance()==null?"":param.getMain_balance());
			printJson.put("reward_balance", param.getReward_balance()==null?"":param.getReward_balance());
			printJson.put("useful_credit", param.getUseful_credit()==null?"":param.getUseful_credit());
			if(SysDictionary.OPERAT_TYPE_CZ.equals(type)||SysDictionary.OPERAT_TYPE_FCZ.equals(type)){
				printJson.put("income", param.getIncome());
				printJson.put("payment_name1", param.getPayment_name1());
			}else if(SysDictionary.OPERAT_TYPE_XF.equals(type)){
				printJson.put("card_class_name", param.getCard_class_name());
				printJson.put("consume_cardmoney", param.getConsume_cardmoney());//消费金额
			}else if(SysDictionary.OPERAT_TYPE_FXF.equals(type)){
				printJson.put("card_class_name", param.getCard_class_name());
				printJson.put("main_trading", param.getMain_trading());//反消费金额
			}
			
		}
		if(posPrintNewService.isNONewPrint(param.getTenancy_id(), param.getStore_id())){ // 启用新打印
			
			posPrintNewService.posPrintByMode(param.getTenancy_id(), param.getStore_id(), param.getPrint_code(), printJson);
			
		}else{
			posNewCrmDao.customerPrint(param.getTenancy_id(), param.getStore_id(), param.getPos_num(), param.getPrint_code(), param.getMode(), printJson, 1);
		}
	}
	
	/**
	 * 获取付款名称 
	 * @param payType
	 * @return
	 */
	public String getPayName(Integer payType){
		String name="";
		switch (payType) {
			case 0:
				name="混结";
				break;
			case 1:
				name="现金";
				break;
			case 2:
				name="银行卡";		
				break;
			case 3:
				name="微信付款";		
				break;
			case 4:
				name="支付宝";		
				break;
			case 5:
				name="储值";		
				break;
			case 6:
				name="积分";		
				break;
			case 7:
				name="优惠券";		
				break;
			default:
				name="其他";	
				break;
			}
		return name;
			
	}

	@Override
	public void queryXMDCRMPayment(String tenantId, Integer storeId, JSONObject para,Data resultData) throws Exception
	{
		// TODO Auto-generated method stub
		Timestamp currentTime = DateUtil.currentTimestamp();
		
		String optNum = para.optString("cashier_num");
		String billId = para.optString("bill_code");
		
		//查询token;查询订单
//		Data param = new Data();
//		param.setTenancy_id(tenantId);
//		param.setStore_id(storeId);
		
//		String appAuthToken = posNewCrmDao.getTokenByThirdLink(tenantId,storeId,Integer.parseInt(optNum));
//		String charset = "UTF-8";
//		String timestamp = new Timestamp(new Date().getTime()).toString();
		//查询第三方支付表记录
//		String billId;
//		if ((!SysDictionary.CHANEL_MD01.equals(chanel)) && Tools.hv(orderNum))
//		{
//			billId = orderNum;
//		}
//		else
//		{
//			if(Tools.hv(payment.optString("bill_code")))
//			{
//				billId = payment.optString("bill_code");
//			}else{
//				String billsql = new String("select bill_code from pos_bill_payment where tenancy_id=? and store_id=? and  bill_num=?");
//				JSONObject jsonb = paymentDao.query4Json(tenancyId, billsql, new Object[]
//						{ tenancyId, storeId, billNum }).get(0);
//				billId = jsonb.optString("bill_code");
//			}
//		}
				
//		String signKey = PosPropertyUtil.getMsg("xmdcrm.signkey");//posNewCrmDao.getSignKen(tenancyId,storeId);
//		if(StringUtils.isEmpty(signKey)){
//			//signKey = "s4p6di49nbbkt2ej";
//			throw new Exception("美大signKey不存在！");
//		}
		
		Map<String, String> params = new HashMap<String,String>();//组织参数
//		params.put("appAuthToken", appAuthToken);
//		params.put("charset", CHARSET);
//		params.put("version", VERSION);
//		params.put("timestamp", timestamp);
		params.put("billId", billId);
//		String sign = MeiDaSignUtils.createSign(PosPropertyUtil.getMsg("xmdcrm.signkey"), params);
//		params.put("sign", sign);
		
//		String reqURL = PosPropertyUtil.getMsg("xmdcrm.request.url")+MeiDaSignUtils.MEIDA_DEAL_DETAIL_URL;
//		JSONObject paramJson = JSONObject.fromObject(params);
//		logger.info(String.valueOf(t)+"<美大确认支付发送接口参数==>" + paramJson);
//		String result = HttpUtil.sendHttpPostRequest(reqURL, params);
//		logger.info(String.valueOf(t)+"<发送美大确认支付接口返回体==>" + result);
		
		logger.info(String.valueOf(currentTime.getTime())+"<美大查询发送接口参数==>" + JSONObject.fromObject(params).toString());
		JSONObject jsonresult = this.sendPostRequest(MeiDaSignUtils.MEIDA_DEAL_DETAIL_URL, tenantId, storeId, optNum, currentTime, params);
		
		if(null!=jsonresult && jsonresult.containsKey("error")){
//			JSONObject jsonresult =JSONObject.fromObject(result);
			String error = jsonresult.optString("error");
			JSONObject jsonerror = JSONObject.fromObject(error);
			resultData.setCode(Integer.parseInt(jsonerror.optString("code")));
			resultData.setMsg(jsonerror.optString("message"));
		}else{
			resultData.setCode(Constant.CODE_SUCCESS);
		}
	}

	@Override
	public void completeXMDCRMPayment(String tenantId, Integer storeId, JSONObject para,Timestamp currentTime) throws Exception
	{
		// TODO Auto-generated method stub
		String optNum=para.optString("opt_num");
		String orderId = para.optString("bill_num");
		String billId = para.optString("bill_code");
		Double paymentAmount = para.optDouble("payment_amount");
		
		if("".equals(billId) || billId == null || "null".equals(billId)){
			throw new Exception("美大流水号不存在！");
		}
		
		if(paymentAmount.isNaN())
		{
			paymentAmount = 0d;
		}
		
		//查询token;确认订单成功, 并通知发送积分 
//		Data param = new Data();
//		param.setTenancy_id(tenantId);
//		param.setStore_id(storeId);
		
//		String appAuthToken = posNewCrmDao.getTokenByThirdLink(tenantId,storeId,Integer.parseInt(optNum));
//		long timestamp = currentTime.getTime();
		long orderMoney = (long)(paymentAmount*100);
		
		Map<String, String> params = new TreeMap<String,String>();//组织参数
//		params.put("appAuthToken", appAuthToken);
//		params.put("ePoiId", String.valueOf(storeId)+tenantId);
//		params.put("charset", CHARSET);
//		params.put("version", VERSION);
		params.put("billId", billId);
		params.put("orderId", orderId);
		params.put("orderMoney", Long.toString(orderMoney));
//		params.put("timestamp", Long.toString(timestamp));
//		String sign = MeiDaSignUtils.createSign(PosPropertyUtil.getMsg("xmdcrm.signkey"), params);
//		params.put("sign", sign);
		
//		String reqURL = PosPropertyUtil.getMsg("xmdcrm.request.url") + MeiDaSignUtils.MEIDA_POST_URL;
//		logger.info(String.valueOf(currentTime) + "<美大确认支付发送接口参数==>" + JSONObject.fromObject(params));
//		String result = HttpUtil.sendHttpPostRequest(reqURL, params);
//		logger.info(String.valueOf(currentTime) + "<发送美大确认支付接口返回体==>" + result);
		
		logger.info(String.valueOf(currentTime.getTime()) + "<美大确认支付发送接口参数==>" + JSONObject.fromObject(params));
		JSONObject jsonresult = this.sendPostRequest(MeiDaSignUtils.MEIDA_POST_URL, tenantId, storeId, optNum, currentTime, params);
		
		if(null != jsonresult && jsonresult.containsKey("error")){
//			JSONObject jsonresult =JSONObject.fromObject(result);
			String error = jsonresult.optString("error");
			JSONObject jsonerror = JSONObject.fromObject(error);
			throw new Exception(jsonerror.optString("code")+jsonerror.optString("message")); 
		}
	}
	
	private JSONObject sendPostRequest(String url,String tenantId,Integer storeId,String optNum ,Timestamp currentTime,Map<String, String> params) throws Exception
	{
		String appAuthToken = posNewCrmDao.getTokenByThirdLink(tenantId, storeId, Integer.parseInt(optNum));
		if (null == appAuthToken || "".equals(appAuthToken))
		{
			JSONObject errorJson = new JSONObject();
			errorJson.put("code", Constant.CODE_PARAM_FAILURE);
			errorJson.put("message", "appAuthToken不能为空");
			JSONObject resultJson = new JSONObject();
			resultJson.put("error", errorJson);
			return resultJson;
		}

		params.put("appAuthToken", appAuthToken);
		params.put("ePoiId", String.valueOf(storeId) + tenantId);
		params.put("charset", CHARSET);
		params.put("version", VERSION);
		params.put("timestamp", Long.toString(currentTime.getTime()));
		String sign = MeiDaSignUtils.createSign(PosPropertyUtil.getMsg("xmdcrm.signkey"), params);
		params.put("sign", sign);

		String reqURL = PosPropertyUtil.getMsg("xmdcrm.request.url") + url;
		logger.info(String.valueOf(currentTime) + "<美大接口请求参数==>" + JSONObject.fromObject(params));
		String result = HttpUtil.sendPostRequest(reqURL, params, 3000, 5000);
		logger.info(String.valueOf(currentTime) + "<美大接口返回体==>" + result);

		JSONObject resultJson = null;
		if (Tools.hv(result))
		{
			try
			{
				resultJson = JSONObject.fromObject(result);
			}
			catch (Exception e)
			{
				JSONObject errorJson = new JSONObject();
				errorJson.put("code", Constant.CODE_INNER_EXCEPTION);
				errorJson.put("message", "请求返回参数解析失败");
				resultJson = new JSONObject();
				resultJson.put("error", errorJson);
			}
		}
		else
		{
			JSONObject errorJson = new JSONObject();
			errorJson.put("code", Constant.CODE_CONN_EXCEPTION);
			errorJson.put("message", Constant.CODE_CONN_EXCEPTION_MSG);
			resultJson = new JSONObject();
			resultJson.put("error", errorJson);
		}
		return resultJson;
	}
	
	private JSONObject sendGetRequest(String url,String tenantId,Integer storeId,String optNum ,Timestamp currentTime,Map<String, String> params) throws Exception
	{
		String appAuthToken = posNewCrmDao.getTokenByThirdLink(tenantId, storeId, Integer.parseInt(optNum));
		if (null == appAuthToken || "".equals(appAuthToken))
		{
			JSONObject errorJson = new JSONObject();
			errorJson.put("code", Constant.CODE_PARAM_FAILURE);
			errorJson.put("message", "appAuthToken不能为空");
			JSONObject resultJson = new JSONObject();
			resultJson.put("error", errorJson);
			return resultJson;
		}

		params.put("appAuthToken", appAuthToken);
		params.put("ePoiId", String.valueOf(storeId) + tenantId);
		params.put("charset", CHARSET);
		params.put("version", VERSION);
		params.put("timestamp", Long.toString(currentTime.getTime()));
		String sign = MeiDaSignUtils.createSign(PosPropertyUtil.getMsg("xmdcrm.signkey"), params);
		params.put("sign", sign);

		StringBuilder reqURL = new StringBuilder(PosPropertyUtil.getMsg("xmdcrm.request.url") + url);
		if (!params.isEmpty())
		{
			reqURL.append("?");
			for (String key : params.keySet())
			{
				reqURL.append(key).append("=").append(params.get(key)).append("&");
			}
			reqURL.setLength(reqURL.length() - 1);
		}

		logger.info(String.valueOf(currentTime) + "<美大接口请求参数==>" + JSONObject.fromObject(params));
		String result = HttpUtil.sendGetRequest(reqURL.toString(), "UTF-8");
		logger.info(String.valueOf(currentTime) + "<美大接口返回体==>" + result);

		JSONObject resultJson = null;
		if (Tools.hv(result))
		{
			try
			{
				resultJson = JSONObject.fromObject(result);
			}
			catch (Exception e)
			{
				JSONObject errorJson = new JSONObject();
				errorJson.put("code", Constant.CODE_INNER_EXCEPTION);
				errorJson.put("message", "请求返回参数解析失败");
				resultJson = new JSONObject();
				resultJson.put("error", errorJson);
			}
		}
		else
		{
			JSONObject errorJson = new JSONObject();
			errorJson.put("code", Constant.CODE_CONN_EXCEPTION);
			errorJson.put("message", Constant.CODE_CONN_EXCEPTION_MSG);
			resultJson = new JSONObject();
			resultJson.put("error", errorJson);
		}
		return resultJson;
	}
}
