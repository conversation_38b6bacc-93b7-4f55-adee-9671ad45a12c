package com.tzx.pos.base.controller;

import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.Tools;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.pos.bo.CloudBillService;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import java.sql.Timestamp;
import java.util.List;

/**
 * 门店已结账单同步到云端
 */
public class PosBillToCloudBillRunnable implements Runnable{

    private static final Logger logger = Logger.getLogger(PosBillToCloudBillRunnable.class);

    private static final int[] sleepTime = {1, 10, 20, 20, 30, 30, 30, 60, 60, 60, 60, 600, 600, 1800, 1800};

    private static final int sleepLen = sleepTime.length;

    private String tenancyId;
    private Integer storeId;
    private String billNum;

    public PosBillToCloudBillRunnable(){

    }

    public PosBillToCloudBillRunnable(String tenancyId, int storeId, String billNum){
        this.tenancyId = tenancyId;
        this.storeId = storeId;
        this.billNum = billNum;
    }

    @Override
    public void run() {
        try
        {
        	// 等待Bean初始化完成
			while(SpringConext.getApplicationContext() == null){
				Thread.sleep(1000);
			}  
        				
            String tenancyId = null;
            if(Constant.systemMap.containsKey("tenent_id"))
            {
                tenancyId = Constant.systemMap.get("tenent_id");
            }

            Integer storeId = null;
            if(Constant.systemMap.containsKey("store_id"))
            {
                storeId = Integer.valueOf(Constant.systemMap.get("store_id"));
            }

            if (Tools.isNullOrEmpty(tenancyId) || Tools.isNullOrEmpty(storeId))
            {
                logger.info("参数为空");
                return;
            }

            DBContextHolder.setTenancyid(tenancyId);
            CloudBillService cloudBillService = SpringConext.getApplicationContext().getBean(CloudBillService.class);

            if(this.billNum == null)
            {
                List<JSONObject> objectList = cloudBillService.queryCompleteNoSyncBill(tenancyId, storeId);

                for(JSONObject billJson: objectList)
                {
                    Timestamp currentTime = DateUtil.currentTimestamp();
                    String billNum = billJson.optString("bill_num");
                    String payTimeStr = billJson.optString("payment_time");
                    Timestamp payTime = DateUtil.formatTimestamp(payTimeStr);

                    String queryTimeStr = billJson.optString("query_time");
                    Timestamp queryTime = DateUtil.formatTimestamp(queryTimeStr);

                    long differ = DateUtil.timestampBetween(queryTime, payTime, DateUtil.TIME_TYPE_MINUTE);
                    int diff = (new Long(differ).intValue())/3; // 3分钟一个梯度

                    if(diff >= sleepLen || diff < 0)
                    {
                        diff = sleepLen - 2;
                    }
                    if(currentTime.getTime() >= (queryTime.getTime() + (sleepTime[diff]) * 1000) )
                    {
                        cloudBillService.syncPosBillToCloudBill(tenancyId, storeId, billNum);
                    }
                }
            }
            else
            {
                cloudBillService.syncPosBillToCloudBill(tenancyId, storeId, this.billNum);
            }

        }
        catch (Exception e)
        {
            e.printStackTrace();
            logger.error("门店已结账单同步到云端异常："+this.billNum, e);
        }
    }

    public String getTenancyId() {
        return tenancyId;
    }

    public void setTenancyId(String tenancyId) {
        this.tenancyId = tenancyId;
    }

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public String getBillNum() {
        return billNum;
    }

    public void setBillNum(String billNum) {
        this.billNum = billNum;
    }
}
