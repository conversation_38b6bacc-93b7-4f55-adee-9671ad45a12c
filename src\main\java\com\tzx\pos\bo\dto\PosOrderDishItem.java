package com.tzx.pos.bo.dto;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * 2015年6月1日-上午11:45:39
 */
public class PosOrderDishItem  implements Serializable
{

	/**
	 * 
	 */
	private static final long	serialVersionUID	= 1L;
	
	private String class_id;
	private String class_name;
	private String item_id;
	private String item_num;
	private String item_name;
	private String item_price;
	private String item_count;
	private String zfkw;
	private String item_property;
	private String setmeal_id;
	private String setmeal_rwid;
	private String item_serial;
	private String assist_num;
	private String assist_money;
	private String method_money;
	
	public String getAssist_num()
	{
		return assist_num;
	}
	public void setAssist_num(String assist_num)
	{
		this.assist_num = assist_num;
	}
	public String getAssist_money()
	{
		return assist_money;
	}
	public void setAssist_money(String assist_money)
	{
		this.assist_money = assist_money;
	}
	public String getItem_serial()
	{
		return item_serial;
	}
	public void setItem_serial(String item_serial)
	{
		this.item_serial = item_serial;
	}
	public String getItem_property()
	{
		return item_property;
	}
	public void setItem_property(String item_property)
	{
		this.item_property = item_property;
	}
	public String getSetmeal_id()
	{
		return setmeal_id;
	}
	public void setSetmeal_id(String setmeal_id)
	{
		this.setmeal_id = setmeal_id;
	}
	public String getSetmeal_rwid()
	{
		return setmeal_rwid;
	}
	public void setSetmeal_rwid(String setmeal_rwid)
	{
		this.setmeal_rwid = setmeal_rwid;
	}
	public String getClass_id()
	{
		return class_id;
	}
	public void setClass_id(String class_id)
	{
		this.class_id = class_id;
	}
	public String getClass_name()
	{
		return class_name;
	}
	public void setClass_name(String class_name)
	{
		this.class_name = class_name;
	}
	public String getItem_id()
	{
		return item_id;
	}
	public void setItem_id(String item_id)
	{
		this.item_id = item_id;
	}
	public String getItem_num()
	{
		return item_num;
	}
	public void setItem_num(String item_num)
	{
		this.item_num = item_num;
	}
	public String getItem_name()
	{
		return item_name;
	}
	public void setItem_name(String item_name)
	{
		this.item_name = item_name;
	}
	public String getItem_price()
	{
		return item_price;
	}
	public void setItem_price(String item_price)
	{
		this.item_price = item_price;
	}
	public String getItem_count()
	{
		return item_count;
	}
	public void setItem_count(String item_count)
	{
		this.item_count = item_count;
	}
	public String getZfkw()
	{
		return zfkw;
	}
	public void setZfkw(String zfkw)
	{
		this.zfkw = zfkw;
	}
	public String getMethod_money()
	{
		return method_money;
	}
	public void setMethod_money(String method_money)
	{
		this.method_money = method_money;
	}
}
