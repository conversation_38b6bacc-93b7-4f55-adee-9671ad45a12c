package com.tzx.base.bo.imp;

import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.tzx.base.bo.OrganService;
import com.tzx.base.po.springjdbc.dao.OrganDao;

@Service(OrganService.NAME)
public class OrganServiceImp implements OrganService
{
	private Logger			logger	= Logger.getLogger(OrganServiceImp.class);

	@Autowired
	private OrganDao		organDao;

	/**
	 * @param String
	 *            tenantId
	 * @param Integer
	 *            organId
	 * @return obj {"status":"1","result":"正常营业"} 1非正常营业，0正常
	 */
	@Override
	public JSONObject getOrganOperatingStatus(String tenantId, Integer organId) throws Exception
	{
		JSONObject obj = new JSONObject();
		String operating_status = organDao.getOrganOperingStatus(tenantId, organId);
		if (StringUtils.isNotEmpty(operating_status))
		{
			String result = "";
			if ("suspension_business".equalsIgnoreCase(operating_status))
			{
				result = organDao.getOperatingStatusName(tenantId, operating_status);
				obj.element("status", 1);
				obj.element("result", result);
			}
			else if ("closed".equalsIgnoreCase(operating_status))
			{
				result = organDao.getOperatingStatusName(tenantId, operating_status);
				obj.element("status", 1);
				obj.element("result", result);
			}
			else if ("normal_business".equalsIgnoreCase(operating_status))
			{
				result = organDao.getOperatingStatusName(tenantId, operating_status);
				obj.element("status", 0);
				obj.element("result", result);
			}
			else
			{
				obj.element("status", 1);
				obj.element("result", "非正常营业");
			}
		}
		else
		{
			obj.element("status", 1);
			obj.element("result", "非正常营业");
		}
		return obj;
	}
}
