package com.tzx.pos.bo.dto;

import java.io.Serializable;

/**
 * TODO
 * <AUTHOR> 
 * @日期：2015年5月18日-下午5:43:32
 */

public class BasicDataDiscountCase implements Serializable
{
	/**
	 * 
	 */
	private static final long	serialVersionUID	= 1L;
	
	private Integer id;
	private String discount_case_name;
	private String discount_case_type;
	private Double rate_renate;
	private String startdate;
	private String sarttime;
	private String enddate;
	private String endtime;



    private String day_running_cycle;
	private String running_cycle;
	private Double zero_norm;
	private String discount_case_code;
	private String is_vipprice;
	
	public Integer getId()
	{
		return id;
	}
	public void setId(Integer id)
	{
		this.id = id;
	}
	public String getDiscount_case_name()
	{
		return discount_case_name;
	}
	public void setDiscount_case_name(String discount_case_name)
	{
		this.discount_case_name = discount_case_name;
	}
	public String getDiscount_case_type()
	{
		return discount_case_type;
	}
	public void setDiscount_case_type(String discount_case_type)
	{
		this.discount_case_type = discount_case_type;
	}
	public Double getRate_renate()
	{
		return rate_renate;
	}
	public void setRate_renate(Double rate_renate)
	{
		this.rate_renate = rate_renate;
	}
	public String getStartdate()
	{
		return startdate;
	}
	public void setStartdate(String startdate)
	{
		this.startdate = startdate;
	}
	public String getSarttime()
	{
		return sarttime;
	}
	public void setSarttime(String sarttime)
	{
		this.sarttime = sarttime;
	}
	public String getEnddate()
	{
		return enddate;
	}
	public void setEnddate(String enddate)
	{
		this.enddate = enddate;
	}
	public String getEndtime()
	{
		return endtime;
	}
	public void setEndtime(String endtime)
	{
		this.endtime = endtime;
	}
	public String getRunning_cycle()
	{
		return running_cycle;
	}
	public void setRunning_cycle(String running_cycle)
	{
		this.running_cycle = running_cycle;
	}
	public Double getZero_norm()
	{
		return zero_norm;
	}
	public void setZero_norm(Double zero_norm)
	{
		this.zero_norm = zero_norm;
	}
	public String getDiscount_case_code()
	{
		return discount_case_code;
	}
	public void setDiscount_case_code(String discount_case_code)
	{
		this.discount_case_code = discount_case_code;
	}
	public String getIs_vipprice()
	{
		return is_vipprice;
	}
	public void setIs_vipprice(String is_vipprice)
	{
		this.is_vipprice = is_vipprice;
	}
    public String getDay_running_cycle() {
        return day_running_cycle;
    }

    public void setDay_running_cycle(String day_running_cycle) {
        this.day_running_cycle = day_running_cycle;
    }
}
