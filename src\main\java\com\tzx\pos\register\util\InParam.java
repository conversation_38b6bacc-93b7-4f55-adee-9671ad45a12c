package com.tzx.pos.register.util;

import java.util.HashMap;
import java.util.Map;

/**
 * 接口必须传入的参数名称，参数值是否可以为空（true 可以为空，false 不可以为空）
 * 
 * <AUTHOR>
 *
 */
public enum InParam {

	/**
	 * 根据设备唯一码判断是否注册
	 */
	registerByCode(new HashMap<String, Object>() {
		{
			put("device_unique_code", false);
			put("devices_ip", false);
		}
	}),
	/**
	 * 校验注册key并保存信息
	 */
	checkCode(new HashMap<String, Object>() {
		{
			put("device_unique_code", false);
			put("devices_ip", false);
			put("store_id", false);
			put("show_type", false);
			put("devices_key", false);
			put("tenancy_id", false);
		}
	}),
	/**
	 * 查询kvs类别
	 */
	getHqKvs(new HashMap<String, Object>() {
		{
			put("store_id", false);
		}
	}),
	/**
	 * 更新注册信息
	 */
	updateRegister(new HashMap<String, Object>() {
		{
			put("show_type", false);
			put("devices_code", false);
			put("kvs_id", true);
		}
	}),
	/**
	 * 查询显示方式
	 */
	getSysDictionary(new HashMap<String, Object>() {
		{
			put("class_identifier_code", false);
		}
	});

	private InParam(Map<String, Object> param) {
		this.param = param;
	}

	Map<String, Object> param;

	public Map<String, Object> getParam() {
		return param;
	}

	public void setParam(Map<String, Object> param) {
		this.param = param;
	}

}
