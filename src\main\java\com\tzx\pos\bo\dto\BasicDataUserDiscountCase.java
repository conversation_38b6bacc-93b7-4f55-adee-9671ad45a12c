package com.tzx.pos.bo.dto;

import java.io.Serializable;

/**
 * 人员折扣方案权限
 * <AUTHOR>
 *
 */
public class BasicDataUserDiscountCase implements Serializable
{

	/**
	 * 
	 */
	private static final long	serialVersionUID	= 2545682026194123191L;
	
	
	private Integer id;
	private Integer employee_id;
	private Integer discount_id;
	private Integer discount_authority_id;
	private String discount_type;
	
	public Integer getId()
	{
		return id;
	}
	public void setId(Integer id)
	{
		this.id = id;
	}
	public Integer getEmployee_id()
	{
		return employee_id;
	}
	public void setEmployee_id(Integer employee_id)
	{
		this.employee_id = employee_id;
	}
	public Integer getDiscount_id()
	{
		return discount_id;
	}
	public void setDiscount_id(Integer discount_id)
	{
		this.discount_id = discount_id;
	}
	public Integer getDiscount_authority_id()
	{
		return discount_authority_id;
	}
	public void setDiscount_authority_id(Integer discount_authority_id)
	{
		this.discount_authority_id = discount_authority_id;
	}

	public String getDiscount_type() {
		return discount_type;
	}

	public void setDiscount_type(String discount_type) {
		this.discount_type = discount_type;
	}
}
