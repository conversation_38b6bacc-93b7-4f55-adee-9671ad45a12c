package com.tzx.clientorder.wxorder.po.springjdbc.dao;

import net.sf.json.JSONObject;

import java.util.List;

/**
 * @Description 微信后付下单
 * <AUTHOR>
 * @Date 2018-11-09 15:35
 */
public interface WxInsertOrderDao {
    String NAME = "com.tzx.clientorder.wxorder.po.springjdbc.dao.WxInsertOrderDaoImp";

    /**
     * 修改pos_bill表的order_num为WX+bill_num,不管是门店开台还是微信开台，source都为WX02
     * @param tenancyId
     * @param billNum
     * @throws Exception
     */
    void updatePosBill(String tenancyId, String billNum) throws Exception;

    /**
     * 查询账单是否绑定了会员信息
     * @param tenancyId
     * @param billNum
     * @return
     * @throws Exception
     */
    List<JSONObject> getBillMember(String tenancyId, String billNum) throws Exception;

    /**
     * 查询会员信息
     * @param tenancyId
     * @param billNum
     * @return
     * @throws Exception
     */
    String getBillInfo(String tenancyId, String billNum) throws Exception;

    /**
     * 修改订单的状态为10已完成，同时保存上账单编号
     * @param tenancyId
     * @param orderCode
     * @param billNum
     * @throws Exception
     */
    void updateCCOrder(String tenancyId, String orderCode, String billNum) throws Exception;

    /**
     * 查询签到的收银员信息
     * @param tenancyId
     * @param storeId
     * @return
     * @throws Exception
     */
    JSONObject getOptInfo(String tenancyId, int storeId) throws Exception;

    /**
     * 根据订单编号查询订单明细
     * @param tenancyId
     * @param orderCode
     * @return
     * @throws Exception
     */
    List<JSONObject> getOrderItems(String tenancyId, String orderCode) throws Exception;

    /**
     * 修改订单菜品，茶味费的修改状态
     * @param tenancyId
     * @param billNum
     * @param itemIds
     * @throws Exception
     */
    void updateBillItem(String tenancyId, String billNum, List<String> itemIds) throws Exception;
}
