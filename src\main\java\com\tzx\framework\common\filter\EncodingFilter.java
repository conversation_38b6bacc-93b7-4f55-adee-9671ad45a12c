package com.tzx.framework.common.filter;

import java.io.IOException;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;

// import com.gxhk.common.web.CnRequestWrapper;
// @WebFilter("*")
public class EncodingFilter implements Filter
{
	protected FilterConfig	filterConfig;
	private String			targetEncoding	= "UTF-8";

	public void init(FilterConfig config) throws ServletException
	{
		this.filterConfig = config;
		// as commen servlet filter can get init parameters
		this.targetEncoding = config.getInitParameter("encoding");

	}

	public void doFilter(ServletRequest req, ServletResponse res, Filter<PERSON>hain chain) throws IOException, ServletException
	{
		// System.out.println("use the follow methed to encoding the request: encoding="+
		// targetEncoding);
		// req.setCharacterEncoding(this.targetEncoding);
		// res.setCharacterEncoding(this.targetEncoding);

		HttpServletRequest request = (HttpServletRequest) req;
		request.setCharacterEncoding(this.targetEncoding);
		// translter the request to next;
		chain.doFilter(req, res);

		/*
		 * HttpServletRequest request = (HttpServletRequest) req;
		 * chain.doFilter(new CnRequestWrapper(request), res);
		 */
	}

	public void setFilterConfig(final FilterConfig filterConfig)
	{
		this.filterConfig = filterConfig;
	}

	public void destroy()
	{
		// destroy the filter
		this.filterConfig = null;

	}

}