package com.tzx.pos.po.springjdbc.dao;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import com.tzx.base.entity.PosBillPayment;
import com.tzx.base.entity.PosBillPaymentCoupons;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.pos.base.dao.BaseDao;
import com.tzx.pos.bo.dto.PosThirdPaymentOrderEntity;

public interface PosPaymentDao extends BaseDao
{
	String	NAME	= "com.tzx.pos.po.springjdbc.dao.imp.PosPaymentDaoImp";

	/**
	 * 获取已付款金额
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @return
	 * @throws Exception
	 */
	public double getBillPaymentAmount(String tenancyId, int storeId, String billNum) throws Exception;

	/**
	 * 修改账单付款状态
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param paymentState
	 * @throws Exception
	 */
	public void updatePosBillForStateByBillnum(String tenancyId, int storeId, String billNum, String paymentState) throws Exception;

	
	/** 修改账单销售模式
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param Salemode
	 * @throws Exception
	 */
	public void updatePosBillForSalemodeByBillnum(String tenancyId, int storeId, String billNum,String Salemode,String tableCode,Integer shiftId) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param paymentState
	 * @param difference
	 * @throws Exception
	 */
	public int updatePosBillForStateByBillnum(String tenancyId, int storeId, String billNum, String paymentState, Double difference) throws Exception;

	/**
	 * 修改账单销售模式
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param Salemode
	 * @throws Exception
	 */
	public void updatePosBillForStateByBillnum(String tenancyId, int storeId, String billNum, String paymentState, Double difference, String saleMode, String tableCode, Integer shiftId) throws Exception;

	/**
	 * 修改账单销售模式
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param paymentState
	 * @param difference
	 * @param saleMode
	 * @param tableCode
	 * @param shiftId
	 * @param transferRemark
	 * @throws Exception
	 */
	public void updatePosBillForStateByBillnum(String tenancyId, int storeId, String billNum, String paymentState, Double difference, String saleMode, String tableCode, Integer shiftId,String transferRemark) throws Exception;

	/**
	 * 修改账单明细销售模式
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param Salemode
	 * @throws Exception
	 */
	public void updatePosBillItemForSalemodeByBillnum(String tenancyId, int storeId, String billNum, String Salemode, Integer shiftId) throws Exception;

	/**
	 * 获取付款中记录
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @throws Exception
	 */
	public List<JSONObject> getBillPaymentUnfinishByBillnum(String tenancyId, int storeId, String billNum) throws Exception;

	/**
	 * 获取付款记录
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @throws Exception
	 */
	public List<JSONObject> getBillPaymentByPaymentState(String tenancyId, int storeId, String billNum, String paymentState) throws Exception;

	/**
	 * 获取付款记录信息
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getBillPaymentByBillnum(String tenancyId, int storeId, String billNum) throws Exception;

	/**
	 * 根据结账ID获取结账信息
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @return
	 * @throws Exception
	 */
	public JSONObject getBillPaymentByJzid(String tenancyId, int storeId, String billNum, int jzid) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param paymentClass
	 * @return
	 * @throws Exception
	 */
	public JSONObject getBillPaymentByPaymentClass(String tenancyId, int storeId, String billNum, String paymentClass) throws Exception;

	

	/**
	 * 记录优惠劵劵号
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param coupons
	 * @throws Exception
	 */
	public void insertPaymentCoupons(String tenancyId, int storeId, List<Object[]> coupons) throws Exception;

	/**
	 * 查询优惠劵付款记录
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param paymentId
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getPaymentCouponsByPaymentId(String tenancyId, int storeId, String billNum, String paymentId) throws Exception;

	/**
	 * 修改优惠劵的取消状态
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param coupon
	 * @throws Exception
	 */
	public void updatePaymentCouponsCancelStateById(String tenancyId, int storeId, String billNum, String coupon) throws Exception;

	/**删除账单付款优惠券明细
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @return
	 * @throws Exception
	 */
	public int deleteBillPaymentCouponsByBillNum(String tenancyId, int storeId, String billNum) throws Exception;
	
	/**删除账单付款优惠券明细
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param paymentUid
	 * @return
	 * @throws Exception
	 */
	public int deleteBillPaymentCouponsByPaymentUid(String tenancyId, int storeId, String billNum,String paymentUid) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param payNo
	 * @return
	 * @throws Exception
	 */
	public int deleteBillPaymentCouponsByBillNum(String tenancyId, int storeId, String billNum,String payNo) throws Exception;
	
	/**
	 * 根据订单号查询第三方支付记录
	 * 
	 * @param tenancy_id
	 * @param order_no
	 * @param service_type
	 * @param pay_type
	 * @param thirdPayCharge
	 * @return
	 * @throws Exception
	 */
	public JSONObject getPayThirdByOrderNum(String tenancyId, int storeId, String orderNo,String aidOrderNum, String thirdPayCharge) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param aidOrderNo
	 * @param objectName
	 * @return
	 * @throws Exception
	 */
	public PosThirdPaymentOrderEntity getThirdPaymentByAidOrderNum(String tenancyId, int storeId, String aidOrderNo, String objectName) throws Exception;

	/**
	 * 使用美大会员卡支付，查询付款记录
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param para
	 * @return
	 * @throws Exception
	 * <AUTHOR>
	 */
	public List<JSONObject> getMeiDaBillPaymentByBillnum(String tenancyId, int storeId, String billNum) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param Salemode
	 * @throws Exception
	 */
	public void updatePosBillTaxPriceSeparation(String tenancyId, int storeId, String billNum, String saleMode) throws Exception;

	/**
	 * 查询账单模式
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @return
	 * @throws Exception
	 */
	public String getSaleMode(String tenancyId, Integer storeId, String billNum) throws Exception;

	/**
	 * 查询签账卡
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param para
	 * @return
	 * @throws Exception
	 * <AUTHOR>
	 */
	public List<JSONObject> getDebitCardPaymentByBillnum(String tenancyId, int storeId, String billNum) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param paymentState
	 * @param difference
	 * @param paymentAmonut
	 * @throws Exception
	 */
	public void updatePosBillForStateByBillnum(String tenancyId, int storeId, String billNum, String paymentState, Double difference, Double paymentAmonut) throws Exception;

	/**
	 * 批量添加付款方式
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param paymentList
	 * @throws Exception
	 */
	public void batchInsertPodBillPayment(String tenancyId, int storeId, List<PosBillPayment> paymentList) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getPosBillPaymentByBillnum(String tenancyId, int storeId, String billNum) throws Exception;

	/**
	 * 获取账单明细,不包含套餐明细菜品
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getPosBillItemForSingleByBillNum(String tenancyId, int storeId, String billNum) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param optNum
	 * @param posNum
	 * @param paymentState
	 * @param difference
	 * @param paymentTime
	 * @param remark
	 * @param payNo
	 * @throws Exception
	 */
	public void updatePosBillForStateByBillnum(String tenancyId, int storeId, String billNum, String optNum, String posNum, String paymentState, Double difference, Timestamp paymentTime, String remark, String payNo) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @throws Exception
	 */
	public void deletePosBillPaymentByBillmum(String tenancyId, int storeId, String billNum) throws Exception;
	
	/**
	 * 删除付款
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param phone
	 * @throws Exception
	 */
	public void deletePosBillPaymentByBillmum(String tenancyId, int storeId, String billNum, String phone) throws Exception;

	/** 根据ID删除付款方式
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param paymentID
	 * @throws Exception
	 */
	public void deletePosBillPaymentByID(String tenancyId, int storeId, String billNum,Integer paymentID) throws Exception;
	
	/** 修改付款状态
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param billPaymentList
	 * @throws Exception
	 */
	public void updatePosBillPaymentForPaymentState(String tenancyId, int storeId, String billNum, String phone,String paymentState,String billCode) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param paymentState
	 * @param billCode
	 * @throws Exception
	 */
	public void updatePosBillPaymentForPaymentState(String tenancyId, int storeId, String billNum, String paymentState,String billCode) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param phone
	 * @param paymentState
	 * @param billCode
	 * @throws Exception
	 */
	public void insertPosBillPaymentLogByBillmum(String tenancyId, int storeId, String billNum, String outTradeNo,String paymentState,String billCode) throws Exception;

	/** 插入付款历史记录
	 * @param tenancyId
	 * @param storeId
	 * @param bill
	 * @throws Exception
	 */
	public void insertPosBillPaymentLog(String tenancyId, int storeId, String billNum, Date reportDate, int shiftId, String posNum, String optNum, String tableCode, String type, int jzid, String name, String nameEnglish, double rate, double amount, double count, double currencyAmount,
			String number, String phone, String isYsk, Integer customerId, String billCode, String remark, Timestamp lastUpdatetime, String paymentState, String batchNum, String paramCach, double more_coupon) throws Exception;

	/** 批量修改付款记录
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param billPaymentList
	 * @throws Exception
	 */
	public void updatePosBillPaymentForPaymentState(String tenancyId, int storeId, String billNum, List<PosBillPayment> billPaymentList) throws Exception;
	
	/**
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @param batchNum
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getBillPaymentForCoupon(String tenantId, int storeId ,String billNum, String batchNum) throws Exception;
	
	/**
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @param batchNum
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getBillItemForCoupon(String tenantId, int storeId ,String billNum, String batchNum) throws Exception;
	
	/**
	 * @param tenantId
	 * @param storeId
	 * @param billPaymentCoupons
	 * @return
	 * @throws Exception
	 */
	public int insertPosBillPaymentCoupons(String tenantId, Integer storeId,PosBillPaymentCoupons billPaymentCoupons) throws Exception;
	
	/**
	 * @param tenantId
	 * @param storeId
	 * @param billPaymentCoupons
	 * @return
	 * @throws Exception
	 */
	public int[] insertPosBillPaymentCoupons(String tenantId, Integer storeId,List<PosBillPaymentCoupons> billPaymentCoupons) throws Exception;
	
	/** 删除优惠券明细
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param paymentUid
	 * @return
	 * @throws Exception
	 */
	public int deletePosBillPaymentCoupons(String tenancyId, int storeId, String billNum,String paymentUid) throws Exception;
	
	/** 根据RWID获取账单明细
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param rwid
	 * @return
	 * @throws Exception
	 */
	public JSONObject getBillItemForCouponByRwid(String tenancyId, int storeId, String billNum,Integer rwid) throws Exception;
	
	/**根据RWID获取账单明细
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param rwids
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getBillItemForCouponByRwid(String tenancyId, int storeId, String billNum, JSONArray rwids) throws Exception;

	/**
	 * 根据账单中的waiter_num,查询服务员的信息
	 * @param tenancyId
	 * @param waiterNum
	 * @return
	 * @throws Exception
	 */
	JSONObject getEmployees(String tenancyId, String waiterNum) throws Exception;
	
	/** 查询结账
	 * @param billno
	 * @param organId
	 * @param object
	 * @return
	 * @throws SystemException
	 */
	public List<JSONObject> posPayment(String billno, Integer organId, JSONObject object) throws SystemException;
	
	/** 查询账单表
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> findBill(String tenancyId, int storeId, String billNum) throws Exception;
	
	/** 查询账单已付金额
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @return
	 * @throws Exception
	 */
	public double getBillPaymentForSumAmount(String tenancyId, int storeId, String billNum) throws Exception;
	
	/** 修改支付记录表
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	
	 */
	public void updatePosBillPayment(String tenancyId, int storeId, String billNum, Date reportDate, int shiftId, String posNum, String optNum, String tableCode, String type, int jzid, String name, String nameEnglish, double rate, double amount, double count, double currencyAmount, String number,
			String phone, String isYsk, Integer customerId, String billCode, String remark, Timestamp lastUpdatetime, int uploadtag,String paymentState, String batchNum,String paramCach,double more_coupon) throws Exception;
	
	public int insertPosBillPayment(String tenancyId, int storeId, String billNum, Date reportDate, int shiftId, String posNum, String optNum, String tableCode, String type, int jzid, String name, String nameEnglish, double rate, double amount, double count, double currencyAmount, String number,
			String phone, String isYsk, Integer customerId, String billCode, String remark, Timestamp lastUpdatetime, String paymentState, String batchNum, String paramCach, double more_coupon, Integer yjzid) throws Exception;


	public int insertPosBillPayment(String tenancyId, int storeId, String billNum, Date reportDate, int shiftId, String posNum, String optNum, String tableCode, String type, int jzid, String name, String nameEnglish, double rate, double amount, double count, double currencyAmount, String number,
			String phone, String isYsk, Integer customerId, String billCode, String remark, Timestamp lastUpdatetime, String paymentState, String batchNum, String paramCach, double moreCoupon) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param paymentEntity
	 * @return
	 * @throws Exception
	 */
	public int insertPosBillPayment(String tenancyId, int storeId, PosBillPayment paymentEntity) throws Exception;
	
	/** 根据账单编号获取付款信息
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @return
	 * @throws Exception
	 */
	public JSONObject getPosBillPaymentByBillnum(String tenancyId,int storeId,String billNum,Integer paymentId) throws Exception;
	
    /**
     * 在同一账单中查询会员付款使用次数
     * @param tenancyId
     * @param storeId
     * @param jzid
     * @param paymentClass
     * @throws Exception
     */
	public List<JSONObject> getPosBillPaymentByIDAndClass(String tenancyId, int storeId, int jzid, String paymentClass, String billNum) throws Exception;

	/**
	 *
	 * @param tenancyId
	 * @param storeId
	 * @param jzid
	 * @param paymentClass
	 * @param billNum
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getItemInfoByBillNumAndRwid(String tenancyId, int storeId, String billNum,Integer rwid) throws Exception;
	
	/** 查询恢复账单付款明细
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param regainCount
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getPosBillPaymentRegainByBillnum(String tenancyId, int storeId, String billNum, Integer regainCount) throws Exception;

    List<JSONObject> getPaymentCouponsByBillNum(String tenancyId, Integer storeId, String billNum) throws Exception;
}
