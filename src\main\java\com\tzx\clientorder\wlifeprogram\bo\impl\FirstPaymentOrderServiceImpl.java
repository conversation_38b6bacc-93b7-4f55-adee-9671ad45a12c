package com.tzx.clientorder.wlifeprogram.bo.impl;

import com.tzx.base.bo.PosCodeService;
import com.tzx.base.cache.CacheManager;
import com.tzx.base.constant.CacheTableConstant;
import com.tzx.clientorder.acewillwechat.bo.SoldOutService;
import com.tzx.clientorder.mtwechat.bo.MtSoldOutService;
import com.tzx.clientorder.mtwechat.po.springjdbc.dao.MtSoldOutDao;
import com.tzx.clientorder.wlifeprogram.bo.FirstPaymentCalAmountService;
import com.tzx.clientorder.wlifeprogram.bo.FirstPaymentOrderService;
import com.tzx.clientorder.wlifeprogram.dao.FirstPaymentDao;
import com.tzx.framework.common.constant.Code;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.DoubleHelper;
import com.tzx.framework.common.util.Tools;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.constant.WLifeConstant;
import com.tzx.pos.base.util.CacheTableUtil;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.base.util.ReqDataUtil;
import com.tzx.pos.bo.imp.PosBaseServiceImp;
import com.tzx.pos.po.springjdbc.dao.PosBillMemberDao;
import com.tzx.pos.po.springjdbc.dao.PosDao;
import com.tzx.pos.po.springjdbc.dao.PosDishDao;
import com.tzx.pos.po.springjdbc.dao.PosSoldOutDao;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;

/**
 * Created by zds on 2018-08-15.
 */
@Service(FirstPaymentOrderService.NAME)
public class FirstPaymentOrderServiceImpl extends PosBaseServiceImp implements FirstPaymentOrderService {

    private static Logger LOG = Logger.getLogger(FirstPaymentOrderServiceImpl.class);

    @Resource(name = FirstPaymentDao.NAME)
    private FirstPaymentDao firstPaymentDao;
    @Resource(name = PosDishDao.NAME)
    private PosDishDao posDishDao;
    @Resource(name = PosDao.NAME)
    private PosDao posDao;
    @Resource(name = PosSoldOutDao.NAME)
    private PosSoldOutDao soldOutDao;
    @Resource(name = MtSoldOutService.NAME)
    private MtSoldOutService mtSoldOutService;
    @Resource(name = MtSoldOutDao.NAME)
    private MtSoldOutDao mtSoldOutDao;
    @Resource(name = PosCodeService.NAME)
    PosCodeService codeService;
    @Resource(name = PosBillMemberDao.NAME)
    private PosBillMemberDao memberDao;

    @Resource(name = SoldOutService.NAME)
    private SoldOutService soldOutService;

    @Resource(name = FirstPaymentCalAmountService.NAME)
    FirstPaymentCalAmountService firstPaymentCalAmountService;

    @Override
    public Data saveTempOrder(Data param, JSONObject json) throws Exception {
        Data data = new Data();
        try {
            long t = System.currentTimeMillis();
            String tenantId = param.getTenancy_id();
            Integer organId = param.getStore_id();
            String source = param.getSource();

            Map<String, Object> map = ReqDataUtil.getDataMap(param);

            Date reportDate = ParamUtil.getDateValue(map, "report_date", true, PosErrorCode.NOT_NULL_REPORT_DATE);// 报表日期

            Integer shiftId = ParamUtil.getIntegerValue(map, "shift_id", false, null);// 班次id

            String posNum = ParamUtil.getStringValue(map, "pos_num", true, PosErrorCode.NOT_NULL_POS_NUM);// 收款机号

            String optNum = ParamUtil.getStringValue(map, "opt_num", false, null);// 操作员编号

            String tableCode = ParamUtil.getStringValue(map, "table_code", false, null);// 桌位编号

            String mode = ParamUtil.getStringValue(map, "mode", true, PosErrorCode.NOT_NULL_MODE);// 0:下单
            // 1：暂存
            String waiterNum = ParamUtil.getStringValue(map, "waiter_num", false, null);

            String billno = ParamUtil.getStringValue(map, "bill_num", true, PosErrorCode.NOT_NULL_BILL_NUM); // 账单编号

            String remark = ParamUtil.getStringValue(map, "remark", false, null);

            String billTaste = ParamUtil.getStringValue(map, "bill_taste", false, null);

            String isPrint = ParamUtil.getStringValue(map, "isprint", false, null);

            String chanel = ParamUtil.getStringValue(map, "chanel", false, null);

            String saleMode = ParamUtil.getStringValue(map, "sale_mode", false, null);

            Integer dishScale = ParamUtil.getIntegerValue(map, "dish_scale", false, null);
            Integer billScale = ParamUtil.getIntegerValue(map, "bill_scale", false, null);

            Map<?, ?> sysParameterMap = CacheManager.getCacheMap(CacheTableConstant.SYS_PARAMETER.name());
            // 厨打单是否打印历史菜品（快餐）
            String isPrintHistoryDish = CacheTableUtil.getSysParameter("isPrintHistoryDish", sysParameterMap);

            if ("null".equals(remark)) {
                remark = null;
            }

            if (StringUtils.isEmpty(isPrint)) {
                isPrint = "Y";
            }

            Timestamp currentTime = DateUtil.currentTimestamp();
            String orderRemark = String.valueOf(currentTime.getTime());

            JSONObject billJson = posDishDao.getPosBillByBillnum(tenantId, organId, billno);

            String bill_property = null;
            String batch_num = null;
            String payment_state = null;
            String discountNum = null;
            Integer discountModeId = 0;
            Integer discountReasonId = 0;
            Double discountRate = 0d;

            // Double payment_amount = 0d;
            if (!billJson.isEmpty()) {
                payment_state = billJson.optString("payment_state");
                bill_property = billJson.optString("bill_property");
                batch_num = billJson.optString("batch_num");
                discountModeId = billJson.optInt("discount_mode_id");
                discountReasonId = billJson.optInt("discount_reason_id");
                discountRate = billJson.optDouble("discount_rate");
                discountNum = billJson.optString("discount_num");
                // payment_amount = rspb.getDouble("payment_amount");
                if (Tools.isNullOrEmpty(saleMode)) {
                    saleMode = billJson.optString("sale_mode");
                }

                if (Tools.isNullOrEmpty(saleMode)) {
                    saleMode = SysDictionary.SALE_MODE_TS01;
                }

                if (Tools.isNullOrEmpty(chanel)) {
                    chanel = billJson.optString("source");
                }

                if (Tools.isNullOrEmpty(remark)) {
                    remark = billJson.optString("remark", null);
                }

                if (discountRate == 0d || discountRate == 0) {
                    discountRate = 100d;
                }
                // 如果waiterNum为null，取pos_bill表中的waiter_num字段值
                if (StringUtils.isEmpty(waiterNum)) {
                    waiterNum = billJson.optString("waiter_num");
                }
                if (Tools.isNullOrEmpty(discountModeId)) {
                    discountModeId = 0;
                }
            }

            if (SysDictionary.BILL_PROPERTY_CLOSED.equals(bill_property)) {
                throw new SystemException(PosErrorCode.BILL_CLOSED);
            }

            if (SysDictionary.PAYMENT_STATE_PAY.equals(payment_state)) {
                throw SystemException.getInstance(PosErrorCode.BILL_PAYMENT_STATE_PAYING_ERROR);
            }

            List<Map<String, Object>> items = (List<Map<String, Object>>) map.get("item"); //
            if (items.size() == 0) {
                throw SystemException.getInstance(PosErrorCode.NOT_NULL_ITEM_LIST);
            }

            // 查询已经存在的最大的item_serial值
            int eMaxItemSerial = posDishDao.selectMaxItemSerial(billno, organId, tenantId);
            //组合套餐最大的item_serial值
            int comboSerial = firstPaymentDao.selectComboMaxItemSerial(billno, organId, tenantId);
            if (comboSerial > 1) {
                eMaxItemSerial = comboSerial;
            }
            /**
             *  下单前确认套餐的item_serial不能相等
             *  套餐主项和套餐明细的item_serial=setmeal_rwid
             */
            if (eMaxItemSerial > 0) {
                List<Integer> serialList = new ArrayList<Integer>();
                for (Map<String, Object> oDish : items) {
                    String itemProperty = ParamUtil.getStringValue(oDish, "item_property", true, PosErrorCode.NOT_NULL_ITEM_PROPERTY);
                    Integer itemSerial = ParamUtil.getIntegerValue(oDish, "item_serial", false, null);
                    itemSerial = itemSerial == null ? 1 : itemSerial;

                    if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equals(itemProperty)) { // 单品、套餐
                        serialList.add(itemSerial);
                    }
                }
                Integer[] serialArr = new Integer[serialList.size()];
                serialList.toArray(serialArr);
                int len = serialArr.length;
                // 新下单菜品的最大item_serial值
                int maxItem = serialArr[0];
                for (int i = 1; i < len; i++) {
                    if (serialArr[i] > maxItem) {
                        maxItem = serialArr[i];
                    }
                }
                if ((eMaxItemSerial + len) != maxItem) { // 已下单菜品大于新下单菜品item_serial时，将新单的item_serial调整
                    for (Map<String, Object> oDish : items) {
                        String itemProperty = ParamUtil.getStringValue(oDish, "item_property", true, PosErrorCode.NOT_NULL_ITEM_PROPERTY);
                        Integer itemId = ParamUtil.getIntegerValue(oDish, "item_id", true, PosErrorCode.NOT_NULL_ITEM_ID);
                        Integer itemSerial = ParamUtil.getIntegerValue(oDish, "item_serial", false, null);

                        if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equals(itemProperty)) { // 单品、套餐
                            eMaxItemSerial++;
                            if (SysDictionary.ITEM_PROPERTY_SETMEAL.equals(itemProperty)) { // 套餐
                                for (Map<String, Object> meallistItem : items) {
                                    String detailsItemProperty = ParamUtil.getStringValue(meallistItem, "item_property", true, PosErrorCode.NOT_NULL_ITEM_PROPERTY);
                                    Integer detailsSetmealId = ParamUtil.getIntegerValue(meallistItem, "setmeal_id", false, null);
                                    Integer detailsSetmealRwid = ParamUtil.getIntegerValue(meallistItem, "setmeal_rwid", false, null);
                                    // 套餐明细
                                    if (SysDictionary.ITEM_PROPERTY_MEALLIST.equals(detailsItemProperty) && itemId.equals(detailsSetmealId) && itemSerial.equals(detailsSetmealRwid)) {
                                        meallistItem.put("item_serial", eMaxItemSerial);
                                        meallistItem.put("setmeal_rwid", eMaxItemSerial);
                                    }
                                }

                                oDish.put("setmeal_rwid", eMaxItemSerial);
                            }
                            oDish.put("item_serial", eMaxItemSerial);
                        }
                    }
                }
            }

            //校验估清
            this.checkItemSoldOut(tenantId, organId, reportDate, items);

            String formatState = "";
            String formatmode = "";
            try {
                JSONObject org = posDishDao.getOrganById(tenantId, organId);

                if (null != org && !org.isEmpty()) {
                    formatState = org.optString("format_state");
                }

                formatmode = posDishDao.getSysParameter(tenantId, organId, "ZCDCMS");
                if ("1".equals(formatState) && !"02".equals(formatmode)) {
                    JSONObject object = new JSONObject();
                    object.element("store_id", organId);
                    object.element("busi_date", DateUtil.format(reportDate));
                    object.put("pos_num", posNum);
                    batch_num = codeService.getCode(tenantId, Code.POS_BATCH_NUM, object);// 调用统一接口来实现
                }
            } catch (Exception e) {
                LOG.info("点菜：" + ExceptionMessage.getExceptionMessage(e));
                throw new SystemException(PosErrorCode.GET_BILLNUM_SERVICE_ERROR);
            }

            // 获取菜品信息
            List<JSONObject> itemInfoList = posDishDao.getHqItemInfoByItem(tenantId, organId, chanel, items);
            Map<String, JSONObject> itemInfoMap = new HashMap<String, JSONObject>();
            for (JSONObject itemInfoJson : itemInfoList) {
                itemInfoMap.put(itemInfoJson.optString("item_id"), itemInfoJson);
            }

            // 获取菜品信息
            List<Map<String, Object>> meallistItems = new ArrayList<Map<String, Object>>();
            List<Integer> setMeallist = new ArrayList<Integer>();
            for (int i = 0; i < items.size(); i++) {
                Map<String, Object> oDish = (Map<String, Object>) items.get(i);
                String itemProperty = ParamUtil.getStringValue(oDish, "item_property", true, PosErrorCode.NOT_NULL_ITEM_PROPERTY);
                if (SysDictionary.ITEM_PROPERTY_SETMEAL.equals(itemProperty)) {
                    setMeallist.add(ParamUtil.getIntegerValue(oDish, "item_id", false, null));
                } else if (SysDictionary.ITEM_PROPERTY_MEALLIST.equals(itemProperty)) {
                    meallistItems.add(oDish);
                }
            }

            Map<String, JSONObject> itemInfoMealMap = new HashMap<String, JSONObject>();
            if (meallistItems.size() > 0) {
                List<JSONObject> itemInfoMealList = posDishDao.getHqItemInfoByItem(tenantId, organId, SysDictionary.CHANEL_MD01, meallistItems);
                if (null != itemInfoMealList && itemInfoMealList.size() > 0) {
                    for (JSONObject itemInfoJson : itemInfoMealList) {
                        itemInfoMealMap.put(itemInfoJson.optString("item_id"), itemInfoJson);
                    }
                }
            }

            Map<String, JSONObject> comboDetailsMap = new HashMap<String, JSONObject>();
            if (meallistItems.size() > 0) {
                List<JSONObject> itemInfoMealList = posDishDao.getHqItemComboDetailsByItem(tenantId, organId, SysDictionary.CHANEL_MD01, setMeallist);
                if (null != itemInfoMealList && itemInfoMealList.size() > 0) {
                    for (JSONObject itemInfoJson : itemInfoMealList) {
                        comboDetailsMap.put(itemInfoJson.optString("iitem_id") + "_" + itemInfoJson.optString("id"), itemInfoJson);
                    }
                }
            }

            // 获取菜品规格信息
            List<JSONObject> itemUnitList = posDishDao.getHqItemUnitByItem(tenantId, organId, items);
            Map<String, JSONObject> itemUnitMap = new HashMap<String, JSONObject>();
            for (JSONObject itemUnitJson : itemUnitList) {
                itemUnitMap.put(itemUnitJson.optString("unit_id"), itemUnitJson);
            }

            // 验证菜品,规格信息,组织账单明细数据
            List<Object[]> billItemList = new ArrayList<Object[]>();
            Map<String, Map<String, Object>> itemMap = new HashMap<String, Map<String, Object>>();
            for (int k = 0; k < items.size(); k++) {
                int orderNumber = k + 1;

                Map<String, Object> detail = (Map<String, Object>) items.get(k);

                itemMap.put(String.valueOf(orderNumber), detail);

                Integer itemSerial = ParamUtil.getIntegerValue(detail, "item_serial", false, null);

                Integer itemId = ParamUtil.getIntegerValue(detail, "item_id", false, null);

                String item_num = ParamUtil.getStringValue(detail, "item_num", false, null);

                String item_name = ParamUtil.getStringValue(detail, "item_name", false, null, false);

                String item_unit_name = ParamUtil.getStringValue(detail, "item_unit_name", false, null);

                Integer itemUnitId = ParamUtil.getIntegerValue(detail, "unit_id", true, PosErrorCode.NOT_NULL_DISH_UNITID);

                Integer reason_id = ParamUtil.getIntegerValue(detail, "reason_id", false, null);

                Integer assist_item_id = ParamUtil.getIntegerValue(detail, "assist_item_id", false, null);

                String item_remark = ParamUtil.getStringValue(detail, "item_remark", false, null);
                // 奉送的批准人员manager_num字段
                String managerNum = ParamUtil.getStringValue(detail, "manager_num", false, null);
                // 备注
                String itemTaste = ParamUtil.getStringValue(detail, "item_taste", false, null);

                String item_property = ParamUtil.getStringValue(detail, "item_property", true, PosErrorCode.NOT_NULL_ITEM_PROPERTY);
                // 子菜品不用填
                String sale_mode = ParamUtil.getStringValue(detail, "sale_mode", false, null);

                Integer comboProp = ParamUtil.getIntegerValue(detail, "combo_prop", false, null);
                if (Tools.isNullOrEmpty(comboProp)) {
                    comboProp = itemSerial;
                }

                Double originItemPrice = ParamUtil.getDoubleValue(detail, "origin_item_price", false, null);
                if (Tools.isNullOrEmpty(originItemPrice) || originItemPrice.isNaN()) {
                    originItemPrice = ParamUtil.getDoubleValue(detail, "item_price", false, null);
                }

                // 折扣率
                Double itemDiscountRate = ParamUtil.getDoubleValue(detail, "single_discount_rate", false, null);
                if (Tools.isNullOrEmpty(itemDiscountRate) || itemDiscountRate.isNaN()) {
                    itemDiscountRate = 100d;
                }
                // 折扣原因id
                Integer itemDiscountReasonId = ParamUtil.getIntegerValue(detail, "discount_reason_id", false, null);
                if (Tools.isNullOrEmpty(itemDiscountReasonId)) {
                    itemDiscountReasonId = 0;
                }
                // 折扣方式id
                Integer itemDiscountModeId = ParamUtil.getIntegerValue(detail, "discount_mode_id", false, null);
                if (Tools.isNullOrEmpty(itemDiscountModeId)) {
                    itemDiscountModeId = 0;
                }
                String itemDiscountNum = ParamUtil.getStringValue(detail, "discount_num", false, null);
                if (Tools.isNullOrEmpty(itemDiscountNum)) {
                    itemDiscountNum = optNum;
                }
                // 单品折扣金额
                Double singleAmount = ParamUtil.getDoubleValue(detail, "single_amount", false, null);
                if (Tools.isNullOrEmpty(singleAmount)) {
                    singleAmount = 0d;
                }

                if (SysDictionary.DISCOUNT_MODE_10 == itemDiscountModeId && 0 <= discountModeId && singleAmount > 0) {
                    discountModeId = SysDictionary.DISCOUNT_MODE_10;
                }

                // 营销活动
                Integer activity_id = ParamUtil.getIntegerValue(detail, "activity_id", false, null);
                Integer activity_rule_id = ParamUtil.getIntegerValue(detail, "activity_rule_id", false, null);
                Integer activity_count = ParamUtil.getIntegerValue(detail, "activity_count", false, null);
                String activity_batch_num = ParamUtil.getStringValue(detail, "activity_batch_num", false, null);

                //默认菜品标识
                String default_state = ParamUtil.getStringValue(detail, "default_state", false, null);

                //处理微信营销活动不传参数activity_batch_num的情况，将activity_batch_num置为账单号
                if (null == activity_id) {
                    activity_id = 0;
                }
                if (null == activity_rule_id) {
                    activity_rule_id = 0;
                }
                if (null == activity_count) {
                    activity_count = 0;
                }
                //POs 营销活动activity_batch_num必须为空。
                if (SysDictionary.SOURCE_CC_ORDER.equals(source) && null == activity_batch_num) {
                    activity_batch_num = billno;
                }
                if (null == managerNum) {
                    managerNum = "";
                }
                if (null == default_state) {
                    default_state = "N";
                }
                if ("1".equals(formatState) && Tools.isNullOrEmpty(sale_mode)) {
                    sale_mode = saleMode;
                }

                Integer waitcall_tag = ParamUtil.getIntegerValue(detail, "waitcall_tag", false, null);

                if (Tools.isNullOrEmpty(waitcall_tag)) {
                    waitcall_tag = 0;
                }

                Integer setmeal_rwid = null;
                if (Tools.isNullOrEmpty(detail.get("setmeal_rwid")) == false) {
                    setmeal_rwid = Integer.parseInt(detail.get("setmeal_rwid").toString());
                }

                Integer setmeal_id = null;
                if (SysDictionary.ITEM_PROPERTY_SETMEAL.equalsIgnoreCase(item_property) || SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item_property)) {
                    setmeal_id = ParamUtil.getIntegerValue(detail, "setmeal_id", false, null);
                }

                double itemPrice = 0d;
                if (SysDictionary.ITEM_PROPERTY_MEALLIST.equals(item_property)) {
                    if (comboDetailsMap.containsKey(setmeal_id + "_" + assist_item_id)) {
                        JSONObject comboDetailJson = comboDetailsMap.get(setmeal_id + "_" + assist_item_id);
                        itemPrice = ParamUtil.getDoubleValueByObject(comboDetailJson, "price");
                    } else {
                        itemPrice = Double.parseDouble(detail.get("item_price").toString());
                    }

                } else if (Tools.isNullOrEmpty(detail.get("item_price")) == false) {


                    itemPrice = Double.parseDouble(detail.get("item_price").toString());
                } else {
                    throw new SystemException(PosErrorCode.NOT_NULL_DISH_ITEM_PRICE);
                }

                // 菜目数量默认显示为1，防止总部套餐明细有时设置为0的情况
                double item_count = 1;
                if (Tools.isNullOrEmpty(detail.get("item_count")) == false) {
                    item_count = Double.parseDouble(detail.get("item_count").toString());
                    item_count = item_count == 0 ? 1 : item_count;
                } else {
                    throw new SystemException(PosErrorCode.NOT_NULL_DISH_ITEM_COUNT);
                }

                // 原任务id，yrwid
                Integer yrwid = null;
                // 打印次数
                Double printCount = null;

                if ("2".equals(formatState) && "0".equals(isPrintHistoryDish)) { // 快餐且不打印"退单并复制"的菜品
                    yrwid = ParamUtil.getIntegerValue(detail, "yrwid", false, null);

                    // 新点的菜品数量为NULL，原始菜品数量由客户端传入
                    if (Tools.isNullOrEmpty(detail.get("print_count")) == false) {
                        printCount = Double.parseDouble(detail.get("print_count").toString());
                    }

                    if (yrwid == null && printCount == null) { // 新点的菜品数量
                        printCount = item_count;
                    }
                }

                Double assist_num = 0d;
                if (Tools.isNullOrEmpty(detail.get("assist_num")) == false) {
                    assist_num = Double.parseDouble(detail.get("assist_num").toString());
                }

                int seat_num = 0;
                if (Tools.isNullOrEmpty(detail.get("seat_num")) == false) {
                    seat_num = Integer.parseInt(detail.get("seat_num").toString());
                }

                JSONObject itemInfoJson = null;
                if (itemInfoMap.containsKey(String.valueOf(itemId))) {
                    itemInfoJson = itemInfoMap.get(String.valueOf(itemId));
                }

                String discount_state = null;
                Double proportion = 0d;
                Integer item_class_id = 0;
                String item_english = null;
                String pushmoney_way = null;
                Integer details_id = 0;

                double count_rate = 1d;

                if (null != itemInfoJson) {
                    details_id = itemInfoJson.optInt("details_id");

                    if ((null == details_id || details_id <= 0) && SysDictionary.ITEM_PROPERTY_MEALLIST.equals(item_property)) {
                        if (itemInfoMealMap.containsKey(String.valueOf(itemId))) {
                            itemInfoJson = itemInfoMealMap.get(String.valueOf(itemId));
                            details_id = itemInfoJson.optInt("details_id");
                        }
                    }

                    item_english = itemInfoJson.optString("item_english");
                    if ("Y".equals(itemInfoJson.optString("is_pushmoney"))) {
                        pushmoney_way = itemInfoJson.optString("pushmoney_way");
                        proportion = itemInfoJson.optDouble("proportion_money");
                    }
                    discount_state = itemInfoJson.optString("is_discount");// 是否可以折扣
                    item_class_id = itemInfoJson.optInt("item_class");
                    item_num = itemInfoJson.optString("item_code");


                    if (proportion.isNaN()) {
                        proportion = 0d;
                    }
                } else {
                    throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_DISH_ITEM);
                }

                if (null == details_id || details_id <= 0) {
                    throw new SystemException(PosErrorCode.NOT_EXISTS_DISH_MENU);
                }

                JSONObject itemUnitJson = null;
                if (itemUnitMap.containsKey(String.valueOf(itemUnitId))) {
                    itemUnitJson = itemUnitMap.get(String.valueOf(itemUnitId));
                    if (itemId != itemUnitJson.optInt("item_id")) {
                        throw new SystemException(PosErrorCode.NOT_EXISTS_DISH_UNIT);
                    }
                    count_rate = itemInfoJson.optDouble("count_rate", 1d);
                }

                if (null == itemUnitJson || itemUnitJson.isEmpty()) {
                    throw new SystemException(PosErrorCode.NOT_EXISTS_DISH_UNIT);
                }
                /**
                 * 保存账单明细
                 */

                if (SysDictionary.DISCOUNT_MODE_10 == discountModeId && SysDictionary.DISCOUNT_MODE_10 != itemDiscountModeId.intValue()) {
                    // 账单的折扣方式为单品折扣时,如果明细不是指定为单品折扣,折扣方式为空
                    itemDiscountRate = 100d;
                    itemDiscountModeId = 0;
                    itemDiscountReasonId = 0;
                    itemDiscountNum = null;
                } else if (SysDictionary.DISCOUNT_MODE_10 != itemDiscountModeId.intValue()) {
                    itemDiscountRate = 100d;
                    itemDiscountModeId = discountModeId;
                    itemDiscountReasonId = discountReasonId;
                    itemDiscountNum = discountNum;
                }


                billItemList.add(new Object[]
                        {tenantId, organId, billno, details_id, itemId, item_num, item_name, item_english, reportDate, itemUnitId, item_unit_name, pushmoney_way, proportion, assist_num, waiterNum, itemPrice, item_count, discountRate, discount_state, itemDiscountModeId, item_class_id,
                                item_property, item_remark, "*", setmeal_id, setmeal_rwid, null, currentTime, itemSerial, shiftId, posNum, orderRemark, seat_num, sale_mode, itemTaste, assist_item_id, waitcall_tag, reason_id, batch_num, orderNumber, managerNum, itemDiscountReasonId,
                                yrwid, comboProp, count_rate});
            }

            if (billItemList.size() > 0) {
                /**
                 * 插入账单明细表,增加折扣原因id
                 */
                StringBuilder itemSql = new StringBuilder(
                        "insert into pos_bill_item_booked (tenancy_id,store_id,bill_num,details_id,item_id,item_num,item_name,item_english,report_date,item_unit_id,item_unit_name,pushmoney_way,proportion,assist_num,waiter_num,item_price,item_count,discount_rate,discount_state,discount_mode_id,item_class_id,item_property,item_remark,print_tag,setmeal_id,setmeal_rwid,is_setmeal_changitem,item_time,item_serial,item_shift_id,item_mac_id,order_remark,seat_num,sale_mode,item_taste,assist_item_id,waitcall_tag,returngive_reason_id,batch_num,order_number,manager_num,discount_reason_id,yrwid,combo_prop,count_rate) ");
                itemSql.append(" values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
                posDishDao.batchUpdate(itemSql.toString(), billItemList);
            }

            // 查询账单明细,组织打印参数
            List<JSONObject> rwidList = firstPaymentDao.getRwidsWithoutWaitCallItem(tenantId, organId, billno, orderRemark);
            String rwids = "";
            List<Integer> rwidSet = new ArrayList<Integer>();
            for (JSONObject rwidJson : rwidList) {
                Integer rwid = rwidJson.optInt("rwid");

//				if ("0".equals(rwidJson.optString("waitcall_tag")))
//				{
//				}
                // 等叫和不等叫菜品全部拼接，在新打印中有是否等叫菜品打印的判断
                if (!Tools.isNullOrEmpty(rwids)) {
                    rwids += ",";
                }
                rwids += String.valueOf(rwid);

                rwidSet.add(rwid);
            }

            //
            List<Integer> methodParamList = new ArrayList<Integer>();
            for (Map<String, Object> item : items) {
                if (Tools.isNullOrEmpty(item.get("method")) == false) {
                    List<Map<String, String>> methods = (List<Map<String, String>>) item.get("method");
                    for (Map<String, String> method : methods) {
//					for (int j = 0; j < methods.size(); j++)
//					{
//						Map<String, String> method = (Map<String, String>) methods.get(j);
                        if (StringUtils.isNotEmpty(method.get("method_id"))) {
                            methodParamList.add(Integer.parseInt(method.get("method_id")));
                        }
                    }
                }
            }

            if (methodParamList.size() > 0) {
                // 查询做法信息
                List<JSONObject> methodList = posDishDao.getHqItemMethodByID(tenantId, organId, methodParamList);
                Map<String, JSONObject> methodInfoMap = new HashMap<String, JSONObject>();
                if (null != methodList && methodList.size() > 0) {
                    for (JSONObject methodInfoJson : methodList) {
                        methodInfoMap.put(methodInfoJson.optString("method_id"), methodInfoJson);
                    }
                }

                // 验证做法信息,组织做法
                List<Object[]> methodItemList = new ArrayList<Object[]>();
                for (JSONObject rwidJson : rwidList) {
                    String rwid = rwidJson.optString("rwid");
                    Integer methodId = null;
                    Map<String, Object> detail = itemMap.get(rwidJson.optString("order_number"));

                    String item_property = ParamUtil.getStringValue(detail, "item_property", false, null);
                    Double itemPrice = ParamUtil.getDoubleValue(detail, "item_price", false, null);
                    Integer item_id = ParamUtil.getIntegerValue(detail, "item_id", false, null);

                    /*if (SysDictionary.ITEM_PROPERTY_SINGLE.equalsIgnoreCase(item_property) || SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item_property))
                    {*/
                    if (Tools.isNullOrEmpty(detail.get("method")) == false) {
                        List<Map<String, String>> methods = (List<Map<String, String>>) detail.get("method");
                        for (Map<String, String> method : methods) {
//							for (int j = 0; j < methods.size(); j++)
//							{
//								Map<String, String> method = (Map<String, String>) methods.get(j);
                            if (StringUtils.isNotEmpty(method.get("method_id"))) {
                                methodId = Integer.parseInt(method.get("method_id"));
                            } else {
                                methodId = 0;
                            }
                            String methodName = method.get("method_name");

                            JSONObject methodJson = null;

                            if (methodInfoMap.containsKey(String.valueOf(methodId))) {
                                methodJson = methodInfoMap.get(String.valueOf(methodId));
                            }

                            if (null == methodJson || methodJson.isEmpty()) {
                                throw new SystemException(PosErrorCode.NOT_EXISTS_METHOD_DISH);
                            }

                            String makeupWay = methodJson.optString("makeup_way");
                            Double pro_money = methodJson.optDouble("proportion_money"); // 有可能是数字也有可能会是小数
                            double zfamount = 0;

                            if (StringUtils.isNotEmpty(makeupWay)) {
                                if ("ADD".equalsIgnoreCase(makeupWay)) {
                                    zfamount = pro_money;
                                }
                                if ("MULTI".equalsIgnoreCase(makeupWay)) {
                                    zfamount = DoubleHelper.mul(itemPrice, pro_money, 4);
                                }
                            }

                            methodItemList.add(new Object[]
                                    {tenantId, organId, billno, reportDate, Integer.valueOf(rwid), item_id, posNum, "METHOD", currentTime, methodId, methodName, zfamount, 0});
                        }
                    }
//                    }
                }

                if (methodItemList.size() > 0) {
                    /**
                     * 插入做法表
                     */
                    StringBuilder insertMethod = new StringBuilder("insert into pos_zfkw_item_booked(tenancy_id,store_id,bill_num,report_date,rwid,item_id,pos_num,type,last_updatetime,zfkw_id,zfkw_name,amount,print_id) values (?,?,?,?,?,?,?,?,?,?,?,?,?)");

                    posDishDao.batchUpdate(insertMethod.toString(), methodItemList);
                }
            }

            if (SysDictionary.DISCOUNT_MODE_6 == discountModeId || SysDictionary.DISCOUNT_MODE_8 == discountModeId) {
                // 会员价

                String mobil = null;
                String cardCode = null;
                String customerCode = null;

                List<JSONObject> memberList = memberDao.queryPosBillMember(tenantId, organId, billno, SysDictionary.BILL_MEMBERCARD_HYJ01);
                if (null != memberList && memberList.size() > 0) {
                    JSONObject memberJson = memberList.get(0);
                    mobil = ParamUtil.getStringValueByObject(memberJson, "mobil");
                    cardCode = ParamUtil.getStringValueByObject(memberJson, "card_code");
                    customerCode = ParamUtil.getStringValueByObject(memberJson, "customer_code");
                }

                if (Tools.hv(mobil) || Tools.hv(cardCode) || Tools.hv(customerCode)) {
                    //TODO 会员价 计算折扣计算
                    firstPaymentCalAmountService.calCustomerVipPriceByItem(tenantId, organId, billno, discountModeId, rwidSet);
//                    this.SetCustomerVipPriceByItem(tenantId, organId, billno, discountModeId, rwidSet);
                }
            }

            String updateBillSql = "update pos_bill_booked set discount_mode_id = ? where tenancy_id=? and store_id =? and bill_num = ?";
            posDishDao.update(updateBillSql, new Object[]
                    {discountModeId, tenantId, organId, billno});

            //TODO 计算优惠 计算折扣计算
            //计算金额
            // firstPaymentCalAmountService.calcAmount(tenantId,organId,billno,dishScale,billScale);

            firstPaymentCalAmountService.calcAmount(tenantId, organId, billno, 2, 2);

            if ("1".equals(mode)) {
                // 更新账单状态为暂存
                String updateSql = "update pos_bill_booked set sale_mode = ?, bill_taste =?,remark = ?,batch_num=?, payment_state=?,bill_property = ? where tenancy_id=? and store_id =? and bill_num = ?";
                posDishDao.update(updateSql, new Object[]
                        {saleMode, billTaste, remark, batch_num, payment_state, SysDictionary.BILL_PROPERTY_SORT, tenantId, organId, billno});

                posDishDao.savePosLog(tenantId, organId, posNum, optNum, optNum, shiftId, reportDate, Constant.TITLE, "暂存", "账单编号:" + billno, "改写账单的属性改为卧单！");
            } else {
                String updateSql = "update pos_bill_booked set sale_mode = ?, bill_taste =?,remark = ?,batch_num=?, payment_state=? where tenancy_id=? and store_id =? and bill_num = ?";
                posDishDao.update(updateSql, new Object[]
                        {saleMode, billTaste, remark, batch_num, payment_state, tenantId, organId, billno});

                // 厨打
                json.put("tenancy_id", tenantId);
                json.put("store_id", organId);
                json.put("mode", mode);
                json.put("bill_num", billno);
                json.put("is_print", isPrint);
                json.put("item_time", DateUtil.format(currentTime));
                json.put("rwids", rwids);
                json.put("pos_num", posNum);
                json.put("opt_num", optNum);
                json.put("report_date", DateUtil.format(reportDate));
                json.put("shift_id", shiftId);

                if ("1".equals(formatState) && !"02".equals(formatmode)) {
                    String isPrintMenu = posDishDao.getSysParameter(tenantId, organId, "is_print_menu");

                    if ("1".equals(isPrintMenu)) {
                        json.put("is_print_order", "Y");

//						json.put("pos_num", posNum);
//						json.put("opt_num", optNum);
//						json.put("report_date", DateUtil.format(reportDate));
//						json.put("shift_id", shiftId);
                        json.put("print_type", "");
                        json.put("order_remark", orderRemark);
                        json.put("mode", "0");
                        json.put("print_code", SysDictionary.PRINT_CODE_1103);
                    }
                } else {
                    json.put("is_print_order", "N");
                }

                posDishDao.savePosLog(tenantId, organId, posNum, optNum, optNum, shiftId, reportDate, Constant.TITLE, "先付下单", "账单编号:" + billno, "");
            }


//            this.updatePosBillForUpload(tenantId, organId, billno);

            LOG.debug("接口耗时===============================>" + (System.currentTimeMillis() - t));
            data.setCode(Constant.CODE_SUCCESS);
            data.setMsg(Constant.ORDER_DISH_SUCCESS);
            return data;
        } catch (SystemException se) {
            LOG.error("下单异常：", se);
            throw se;
        } catch (Exception e) {
            e.printStackTrace();
            LOG.info("下单：" + ExceptionMessage.getExceptionMessage(e));
            throw new SystemException(PosErrorCode.OPER_ERROR);
        }

    }

    /**
     * 校验点菜菜品的估清
     *
     * @param tenantId
     * @param organId
     * @param reportDate
     * @param itemList
     * @throws Exception
     */
    private void checkItemSoldOut(String tenantId, int organId, Date reportDate, List<Map<String, Object>> itemList) throws Exception {
        //获取估清菜品
        Map<String, Object> soldOutMap = posDishDao.getPosSoldOutByItem(tenantId, organId, reportDate, itemList);
        //合计菜品的点菜数量
        Map<String, Double> itemCountMap = new HashMap<String, Double>();
        Map<String, String> itemNameMap = new HashMap<String, String>();
        for (Map<String, Object> itemMap : itemList) {
            String itemId = String.valueOf(itemMap.get("item_id"));
            Double itemCount = Double.parseDouble(String.valueOf(itemMap.get("item_count")));
            if (itemCountMap.containsKey(itemId)) {
                itemCount = DoubleHelper.add(itemCount, itemCountMap.get(itemId), 4);
            }
            itemCountMap.put(itemId, itemCount);
            itemNameMap.put(itemId, String.valueOf(itemMap.get("item_name")).trim());
        }
        //校验估清
        Iterator<String> it = itemCountMap.keySet().iterator();
        while (it.hasNext()) {
            String itemId = it.next();
            if (soldOutMap.containsKey(itemId)) {
                Double soldOutCount = Double.parseDouble(String.valueOf(soldOutMap.get(itemId)));
                if (soldOutCount.doubleValue() < itemCountMap.get(itemId).doubleValue()) {
                    throw SystemException.getInstance(itemNameMap.get(itemId) + "点菜数量大于估清数量", PosErrorCode.GREAT_THAN_SOLD_OUT_COUNT);
                }
            }
        }
    }

    @Override
    public JSONObject getOrderDetail(String tenancyId, String billNum) throws Exception {
        return getOrderInfoObject(tenancyId, billNum);
    }

    private JSONObject getOrderInfoObject(String tenancyId, String billNum) throws Exception {

        JSONObject billObject = firstPaymentDao.getBillInfo(tenancyId, billNum);
        if (null == billObject) {
            LOG.error("getOrderInfoMap is null tenancyId=" + tenancyId + ",billNum=" + billNum);
            return new JSONObject();
        }

        JSONObject orderObject = getOrder(tenancyId, billObject);
        String businessId = null;
        String brandId = null;
        String shopId = null;
        List<JSONObject> paramList = firstPaymentDao.getParam(tenancyId);
        if (null != paramList && paramList.size() > 0) {
            for (int i = 0; i < paramList.size(); i++) {
                String code = paramList.get(i).optString("para_code");
                if (WLifeConstant.BUSSINESS_ID.equals(code)) {
                    businessId = paramList.get(i).optString("para_value");
                } else if (WLifeConstant.BRAND_ID.equals(code)) {
                    brandId = paramList.get(i).optString("para_value");
                } else if (WLifeConstant.SHOP_ID.equals(code)) {
                    shopId = paramList.get(i).optString("para_value");
                }
            }
        }

        if (Tools.isNullOrEmpty(businessId) || Tools.isNullOrEmpty(brandId) || Tools.isNullOrEmpty(shopId)) {
            LOG.error("wLife System Params is null");
            return null;
        }


        if (null != orderObject) {
            orderObject.remove("id");
            //
            orderObject.put("business_id", businessId);
            orderObject.put("brand_id", brandId);
            orderObject.put("shop_id", shopId);
            JSONObject member = firstPaymentDao.getWLifeMember(tenancyId, billNum);
            if (null != member) {
//                orderObject.put("name", member.optString("name"));
//                orderObject.put("mobile", member.optString("mobile"));
//                orderObject.put("balance", member.optString("balance"));
//                orderObject.put("credit", member.optString("credit"));
//                orderObject.put("openid", member.optString("openid"));

                JSONObject grade = new JSONObject();
                grade.put("cardnum", member.optString("cno"));
                grade.put("orginlevel", "");
                member.put("upgrade", grade);

                orderObject.put("member",member);
            }

        }
        return orderObject;
    }

    private JSONObject getOrder(String tenancyId, JSONObject billObject) throws Exception {
        //订单信息
        JSONObject orderObject = new JSONObject();

        //设置订单的优惠信息
//        setBillDiscount(billObject, orderObject);
        if (null != billObject) {

            JSONObject ordermemo = new JSONObject();
            ordermemo.put("id", null);
            ordermemo.put("text", billObject.optString("ordermemo"));
            orderObject.put("ordermemo", ordermemo);

            //升级   gradeamount 升级所需金额
//            orderObject.put("is_locked", billObject.optInt("is_locked"));
            orderObject.put("total", billObject.optDouble("total"));
            orderObject.put("cost", billObject.optDouble("cost"));
//            orderObject.put("discountable", billObject.optDouble("discountable"));
//            orderObject.put("membercouponsprice", billObject.optDouble("membercouponsprice"));
            orderObject.put("people", billObject.optString("people"));
            orderObject.put("mealfee", billObject.optDouble("mealfee"));
            orderObject.put("oid", billObject.optString("oid"));
            orderObject.put("out_order_id", billObject.optString("order_id"));
            orderObject.put("identify", billObject.optString("order_id"));

        }
        orderObject.put("tableno", billObject.optString("table_code"));
        //====
        int discountModeId = billObject.optInt("discount_mode_id");
        if (discountModeId == SysDictionary.DISCOUNT_MODE_6) {
            orderObject.put("memberPrice", billObject.optDouble("discountk_amount"));
        } else {
            orderObject.put("memberPrice", 0);
        }
        orderObject.put("discount_money", billObject.optDouble("discount_amount"));


        orderObject.put("weiXinPay", 0);

        orderObject.put("discount_info", new Object());


//        orderObject.put("ordermode", WLifeConstant.WLIFE_ORDER_MODE);
        //设置代金券是否可用
//        orderObject.put("djqflag", true);
//        orderObject.put("djqRules", "");

        //设置订单中的菜品信息
        setDish(tenancyId, orderObject);
        return orderObject;
    }

    private void setDish(String tenancyId, JSONObject orderObject) throws Exception {
        //桌台的未结账的账单号
        String billNum = orderObject.optString("oid");
        //查询套餐菜品信息(可能一笔订单，多个套餐)
        List<JSONObject> setmealList = firstPaymentDao.getSetmeal(tenancyId, billNum, SysDictionary.ITEM_PROPERTY_SETMEAL);


        if (null == setmealList) {
            setmealList = new ArrayList<>();
        }
        //查询套餐中的主菜和必选菜
        if (setmealList.size() > 0) {
            for (JSONObject aSetmealList : setmealList) {
                //套餐id
                //String setmealId = setmealList.get(i).optString("did");
                //设置菜品可用券的情况
//                setmealList.get(i).put("cpqflag", true);
//                //菜品券互斥规则提示语
//                setmealList.get(i).put("cpqRules", "");
//                //菜品券抵扣数量
//                setmealList.get(i).put("cpqhasbeen", 0);
//                //抵扣菜品券面值
//                setmealList.get(i).put("cpqDeno", "");

                aSetmealList.put("bbuysno", "");
                aSetmealList.put("bgiftsno", "");
                //可使用会员价的会员等级
                String[] membergid = (String[]) com.tzx.clientorder.wlifeprogram.common.constant.Constant.membergidMap.get(com.tzx.clientorder.wlifeprogram.common.constant.Constant.MEMBERGID);
                aSetmealList.put("membergid", membergid);
//                setmealList.get(i).put("isWeigh", setmealList.get(i).optInt("is_weigh"));
                //查询菜品做饭
                List<JSONObject> cookList = firstPaymentDao.getCooks(tenancyId, aSetmealList.optInt("id"));
                if (null != cookList && cookList.size() > 0) {
                    aSetmealList.put("cooks", cookList.get(0));
                }

                //查询套餐里的主菜信息
                //主菜里的数量，要除以套餐的数量。
                //套餐id
                String setmealId = aSetmealList.optString("did");
                String setmeal_rwid = aSetmealList.optString("setmeal_rwid");
                int stemealCount = aSetmealList.optInt("number", 1);
                List<JSONObject> mainList = firstPaymentDao.getSetmealDish(tenancyId, billNum, "main", stemealCount, setmealId, setmeal_rwid);
                if (null == mainList) {
                    mainList = new ArrayList<>();
                }
                //查询套餐中的必选菜品
                List<JSONObject> mandatoryList = firstPaymentDao.getSetmealDish(tenancyId, billNum, "mandatory", stemealCount, setmealId, setmeal_rwid);
                if (null == mandatoryList) {
                    mandatoryList = new ArrayList<>();
                }
                aSetmealList.put("maindish", mainList);
                aSetmealList.put("mandatory", mandatoryList);
                aSetmealList.put("optional", new JSONArray());

                aSetmealList.put("centPrice", aSetmealList.optDouble("price", 0) * 100);
                aSetmealList.put("isWeigh", aSetmealList.optString("isweigh"));
                aSetmealList.remove("isweigh");
            }
            //设置订单中的套餐信息
            orderObject.put("setmeal", setmealList);
        }
        //设置订单中的套餐信息
//        orderObject.put("setmeal", setmealList);
        //非套餐菜品的查询
        List<JSONObject> normalList = firstPaymentDao.getSetmeal(tenancyId, billNum, SysDictionary.ITEM_PROPERTY_SINGLE);
        if (null != normalList && normalList.size() > 0) {
            for (JSONObject aNormalList : normalList) {
                aNormalList.put("bbuySno", "");
                aNormalList.put("bgiftSno", "");
                //可使用会员价的会员等级
                String[] membergid = (String[]) com.tzx.clientorder.wlifeprogram.common.constant.Constant.membergidMap.get(com.tzx.clientorder.wlifeprogram.common.constant.Constant.MEMBERGID);
                aNormalList.put("membergid", membergid);
                aNormalList.put("cpqflag", true);
                aNormalList.put("type", "1");
                //查询菜品做饭
                List<JSONObject> cookList = firstPaymentDao.getCooks(tenancyId, aNormalList.optInt("id"));
                if (null != cookList && cookList.size() > 0) {
                    aNormalList.put("cooks", cookList);
                }

                aNormalList.put("centPrice", aNormalList.optDouble("price", 0) * 100);
                aNormalList.put("isWeigh", aNormalList.optString("isweigh"));
                aNormalList.remove("isweigh");

            }
        }
        if (null != normalList) {
            orderObject.put("normalitems", normalList);
        }

    }
}