package com.tzx.member.crm.bo.imp;

import java.io.File;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import sun.misc.BASE64Encoder;

import com.alipay.api.internal.util.StringUtils;
import com.tzx.base.bo.PosCodeService;
import com.tzx.base.common.util.PosPropertyUtil;
import com.tzx.framework.common.constant.Code;
import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.entity.Pagination;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.DoubleHelper;
import com.tzx.framework.common.util.HttpUtil;
import com.tzx.framework.common.util.JavaMd5;
import com.tzx.framework.common.util.ObjectMapper;
import com.tzx.framework.common.util.PropertiesLoader;
import com.tzx.framework.common.util.Tools;
import com.tzx.member.crm.bo.CustomerService;
import com.tzx.member.crm.po.springjdbc.dao.CustomerDao;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.base.util.JsonUtil;
import com.tzx.pos.base.util.KeyUtils;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.base.util.PathUtil;
import com.tzx.pos.base.util.QrCodeUtils;
import com.tzx.pos.base.util.ReqDataUtil;
import com.tzx.pos.bo.PosDebitImageService;
import com.tzx.pos.bo.PosPrintNewService;
import com.tzx.pos.bo.PosPrintService;
import com.tzx.pos.bo.dto.PosThirdPaymentOrderEntity;
import com.tzx.pos.po.springjdbc.dao.PosDishDao;
import com.tzx.thirdpay.bo.ThirdPaymentService;

/**
 * 会员相关
 * 
 * <AUTHOR> 2015年7月10日-下午3:41:15
 */
@Service(CustomerService.NAME)
public class CustomerServiceImp implements CustomerService
{
	protected static final Logger		logger				= Logger.getLogger(CustomerServiceImp.class);

	protected static final Logger		posToCrmLogger		= Logger.getLogger("pos_to_crm");

	@Resource(name = PosDishDao.NAME)
	private PosDishDao				posDishDao;

	@Resource(name = CustomerDao.NAME)
	protected CustomerDao				customerDao;

//	@Resource(name = PosPaymentDao.NAME)
//	private PosPaymentDao			paymentDao1;

//	@Resource(name = PosDao.NAME)
//	private PosDao					posDao1;

	@Resource(name = ThirdPaymentService.NAME)
	protected ThirdPaymentService	thirdPaymentService;

	@Resource(name = PosPrintNewService.NAME)
	protected PosPrintNewService		posPrintNewService;

	@Resource(name = PosPrintService.NAME)
	protected PosPrintService			posPrintService;

	@Resource(name = PosCodeService.NAME)
	protected PosCodeService			codeService;
	
	@Autowired
	protected PosDebitImageService	imageService;
	
	
	public static final String			INVOICE_POST_URL	= "/invoice/elec/post";

	private final String			INVOICE_GET_URL		= "/invoice/elec/get";

	private final String			CRM_POST_URL		= "/crmRest/post";
	
	private final String   CUSTOMER_HISTORY_BILL = "/rest/hq/memberBillRest/queryMemberBill/";//会员查询历史账单
	
	private final String   CUSTOMER_HISTORY_BILL_DETAIL = "/rest/hq/memberBillRest/queryMemberBillDetail/";//会员查询历史账单明细

	public String getPequestUrl(String url)
	{
        String saas_url = PosPropertyUtil.getMsg("saas.url");
	    logger.info("总部地址："+saas_url);
		return saas_url + url;
	}
	
	@Override
	public void commonPost(String param, Data resData)
	{
		String reqURL = getPequestUrl(CRM_POST_URL); //com.tzx.framework.common.constant.Constant.systemMap.get("crmUrl");
		try
		{
			long t = System.currentTimeMillis();
			
			JSONObject paramJson = JSONObject.fromObject(param);
			if (Tools.isNullOrEmpty(paramJson.optLong("t")))
			{
				paramJson.put("t", t);
			}
			if (Tools.isNullOrEmpty(paramJson.optString("secret")))
			{
				paramJson.put("secret", Data.buildSecret(paramJson.optString("tenancy_id"), paramJson.optLong("t")));
			}
			
			posToCrmLogger.info(String.valueOf(t)+"<发送接口请求体==>type: " + paramJson.optString("type"));
			posToCrmLogger.info(String.valueOf(t)+"<发送接口请求体==>oper: " + paramJson.optString("oper"));
			posToCrmLogger.info(String.valueOf(t)+"<发送接口请求体==>" + param);
			logger.info(String.valueOf(t)+"<发送接口请求体==>" + param);
			String result = HttpUtil.sendPostRequest(reqURL, param, 3000, 10000);
			logger.info(String.valueOf(t)+"<发送接口返回体==>" + result);
			if (StringUtils.isEmpty(result))
			{
				resData.setCode(Constant.CODE_CONN_EXCEPTION);
				resData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
				resData.setSuccess(false);
				logger.info("连接超时，请检查网络,请求体为：" + param);
			}
			else
			{
//				ObjectMapper objectMapper = new ObjectMapper();
				try
				{
//					Data data = objectMapper.readValue(result, Data.class);
					Data data = JsonUtil.JsonToData(JSONObject.fromObject(result));
					if (1 == data.getCode())
					{
						resData.setCode(Constant.CODE_CONN_EXCEPTION);
						resData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
						logger.info("连接异常,请求体为：" + param);
					}
					else
					{
						resData.setPagination(data.getPagination());
						resData.setCode(data.getCode());
						resData.setMsg(data.getMsg());
					}
					resData.setData(data.getData());
					resData.setSuccess(data.isSuccess());
				}
				catch (Exception se)
				{
					logger.info("转查会员信息错误：" + ExceptionMessage.getExceptionMessage(se));
					se.printStackTrace();
				}
			}
			
			posToCrmLogger.info(String.valueOf(t)+"<调用接口返回体==>code: " + resData.getCode());
			posToCrmLogger.info(String.valueOf(t)+"<调用接口返回体==>msg: " + resData.getMsg());
			posToCrmLogger.info(String.valueOf(t)+"<调用接口返回体==>" + result);
		}
		catch (Exception se)
		{
			logger.info("连接超时，请检查网络,请求体为：" + param);
			resData.setCode(5);
			resData.setMsg("连接超时，请检查网络");
			resData.setSuccess(false);
		}
	}
	
	@Override
	public void formatParam(Data param,JSONObject jsobj) throws Exception
	{
		if(Oper.find  != param.getOper() && Oper.check  != param.getOper())
		{
			String tenancyId = param.getTenancy_id();
			int storeId = param.getStore_id();
			List<?> dataListOld = param.getData();
	
			if (null != dataListOld && dataListOld.size() > 0)
			{
				JSONObject dataJson = JSONObject.fromObject(dataListOld.get(0));
	
				String optNum = dataJson.optString("opt_num");
				String optName = customerDao.getEmpNameById(optNum, tenancyId, storeId);
				//添加手动reward_money输入营销活动金额
				dataJson.put("reward_money", ParamUtil.getStringValueByObject(dataJson, "reward_money ",false,null));
				dataJson.put("business_date", ParamUtil.getDateStringValue(dataJson, "report_date"));
				dataJson.put("updatetime", DateUtil.getNowDateYYDDMMHHMMSS());
				dataJson.put("operator", optName);
				dataJson.put("operator_id", optNum);
	
				List<JSONObject> dataList = new ArrayList<JSONObject>();
				dataList.add(dataJson);
	
				param.setData(dataList);
				jsobj.put("data", dataList);
			}
		}
	}
	
	@Override
	public boolean commonPostCheck(Data paramData)
	{
		JSONObject paramObj = JSONObject.fromObject(paramData.getData().get(0));

		return this.commonPostCheck(paramData.getTenancy_id(), paramData.getStore_id(), paramObj);
	}
	
	@Override
	public boolean commonPostCheck(String tenantId, Integer storeId, JSONObject paramData)
	{
		try
		{
			if ("Y".equals(paramData.optString("ischeck")))
			{
				Date reportDate = null;
				if (paramData.containsKey("report_date") && Tools.hv(paramData.opt("report_date")))
				{
					reportDate = DateUtil.parseDate(ParamUtil.getDateStringValue(paramData, "report_date"));
				}
				else
				{
					throw SystemException.getInstance(PosErrorCode.NOT_NULL_REPORT_DATE);
				}
				String optNum = paramData.optString("opt_num");
				Integer shiftId = 0;
				if(paramData.containsKey("shift_id")){
					shiftId= paramData.optInt("shift_id");
				}
				if (Tools.isNullOrEmpty(optNum))
				{
					throw SystemException.getInstance(PosErrorCode.NOT_NULL_OPT_NUM);
				}
				if(shiftId!=0 && paramData.containsKey("pos_num")){
					String posNum = paramData.optString("pos_num");
					customerDao.checkKssy(reportDate, optNum, storeId,shiftId,posNum);
				}
			}
			// this.commonPost(param, resData);
			return true;
		}
		catch (SystemException se)
		{
			throw se;
		}
		catch (Exception e)
		{
			logger.info("Exception:" + ExceptionMessage.getExceptionMessage(e));
			throw SystemException.getInstance(PosErrorCode.OPER_ERROR);
		}
	}
	
	@Override
	public boolean commonPostCheck(String tenantId, Integer storeId, String source, JSONObject paramData)
	{
		try
		{
			if ("Y".equals(paramData.optString("ischeck")))
			{
				Date reportDate = null;
				if (paramData.containsKey("report_date") && Tools.hv(paramData.opt("report_date")))
				{
					reportDate = DateUtil.parseDate(ParamUtil.getDateStringValue(paramData, "report_date"));
				}
				else
				{
					throw SystemException.getInstance(PosErrorCode.NOT_NULL_REPORT_DATE);
				}
				
				String optNum = ParamUtil.getStringValueByObject(paramData, "opt_num", true, PosErrorCode.NOT_NULL_OPT_NUM);
				String posNum = ParamUtil.getStringValueByObject(paramData, "pos_num", true, PosErrorCode.NOT_NULL_POS_NUM);

				if (SysDictionary.SOURCE_MOVE.contains(source)||SysDictionary.SOURCE_ORDER_DEVICE.equals(source))
				{
					customerDao.checkBindOptnum(tenantId, storeId, reportDate, posNum);
				}
				else
				{
					Integer shiftId = 0;
					if(paramData.containsKey("shift_id")){
						shiftId= paramData.optInt("shift_id");
					}
					if(shiftId!=0){
						customerDao.checkKssy(reportDate, optNum, storeId,shiftId,posNum);
					}
				}
			}
			// this.commonPost(param, resData);
			return true;
		}
		catch (SystemException se)
		{
			throw se;
		}
		catch (Exception e)
		{
			logger.info("Exception:" + ExceptionMessage.getExceptionMessage(e));
			throw SystemException.getInstance(PosErrorCode.OPER_ERROR);
		}
	}
	

	

	@Override
	public void initCustomerCardPost(String param, Data resData)
	{
		// TODO Auto-generated method stub
		
	}
	
	@Override
	public void activateCustomerCardPost(String param, Data resData)
	{
		this.commonPost(param, resData);

		if (resData.isSuccess())
		{
			ObjectMapper objectMapper = new ObjectMapper();
			Data pData = null;
			try
			{
				pData = objectMapper.readValue(param, Data.class);
			}
			catch (Exception se)
			{
				se.printStackTrace();
				logger.error("Fastjson 类型解析错误" + ExceptionMessage.getExceptionMessage(se));
				resData.setCode(Constant.CODE_PARAM_FAILURE);
				resData.setMsg("json转data类型错误，请查看传入参数");
				return;
			}
			
			try
			{
				String tenantId = pData.getTenancy_id();
				Integer storeId = pData.getStore_id();

				// 插入卡片激活/挂失表
				StringBuilder sqlActivation = new StringBuilder("insert into crm_activation_lost_list (tenancy_id,customer_id,card_id,card_code,operat_type,chanel,store_id,operator,updatetime,remark,deposit,sales_price,shift_id,operator_id) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?)");

				JSONObject resultObj = JSONObject.fromObject(resData.getData().get(0));

				Integer customerId = null;
				Integer cardId = null;
				String cardCode = null;
				String operatType = null;
				String chanel = null;
				String operator = null;
				Timestamp updatetime = null;
				String remark = null;
				double deposit = 0d;
				double salesPrice = 0d;
				Integer shift_id = null;
				Integer empId = null;

				Object[] objs = null;
				JSONArray list = resultObj.optJSONArray("crm_activation_lost_list");

				if (list == null || list.size() == 0)
				{
					pData.setType(Type.CUSTOMER_CARD);
					pData.setOper(Oper.find);
					String reqParam = JSONObject.fromObject(pData).toString();

					this.commonPost(reqParam, resData);

					resultObj = JSONObject.fromObject(resData.getData().get(0));

					customerId = resultObj.optInt("id");
					cardId = resultObj.optInt("card_id");
					cardCode = resultObj.optString("card_code");
					operatType = resultObj.optString("operat_type");
					if (Tools.isNullOrEmpty(operatType))
					{
						operatType = "01";
					}
					chanel = resultObj.optString("add_chanel");
					operator = resultObj.optString("last_operator");
					empId = Integer.parseInt(customerDao.getEmpIdByName(operator, tenantId, storeId));
					remark = resultObj.optString("remark");
					updatetime = DateUtil.currentTimestamp();
					deposit = resultObj.optDouble("deposit");
					if (Double.isNaN(deposit))
					{
						deposit = 0d;
					}
					salesPrice = resultObj.optDouble("sales_price");
					if (Double.isNaN(salesPrice))
					{
						salesPrice = 0d;
					}
					shift_id = JSONObject.fromObject(pData.getData().get(0)).optInt("shift_id");

					objs = new Object[]
					{ tenantId, customerId, cardId, cardCode, operatType, chanel, storeId, operator, updatetime, remark, deposit, salesPrice, shift_id, empId };
					customerDao.update(sqlActivation.toString(), objs);
					
				}
				else
				{
					for (int i = 0; i < list.size(); i++)
					{
						JSONObject activation = list.getJSONObject(i);

						customerId = activation.optInt("customer_id");
						cardId = activation.optInt("card_id");
						cardCode = activation.optString("card_code");
						operatType = activation.optString("operat_type");
						chanel = activation.optString("chanel");
						operator = activation.optString("operator");
						empId = activation.optInt("operator_id");

						if (Tools.isNullOrEmpty(operatType))
						{
							operatType = "01";
						}
						remark = activation.optString("remark");
						updatetime = DateUtil.currentTimestamp();
						deposit = activation.optDouble("deposit");
						if (Double.isNaN(deposit))
						{
							deposit = 0d;
						}
						salesPrice = activation.optDouble("sales_price");
						if (Double.isNaN(salesPrice))
						{
							salesPrice = 0d;
						}
						shift_id = activation.optInt("shift_id");

						objs = new Object[]
						{ tenantId, customerId, cardId, cardCode, operatType, chanel, storeId, operator, updatetime, remark, deposit, salesPrice, shift_id, empId };
						customerDao.update(sqlActivation.toString(), objs);
					}
				}
			}
			catch (Exception e)
			{
				e.printStackTrace();
				logger.info("Exception:" + ExceptionMessage.getExceptionMessage(e));
				throw SystemException.getInstance(PosErrorCode.OPER_ERROR);
			}
			
		}
	}
	
	@Override
	@Deprecated
	public void lossCustomerCardPost(String param, Data resData)
	{
		// TODO Auto-generated method stub
		
	}
	
	@Override
	public void fillCustomerCardPost(String param, Data resData)
	{
		this.commonPost(param, resData);

		if (resData.isSuccess())
		{
			// 插入卡片交易流水表
			StringBuilder sqlTrading = new StringBuilder(
					"insert into crm_card_trading_list (tenancy_id,card_id,card_code,bill_code,chanel,store_id,business_date,main_trading,reward_trading,operat_type,main_original,reward_original,deposit,operator,operate_time,bill_money,third_bill_code,bill_code_original,activity_id,customer_id,card_class_id,name,mobil,shift_id,operator_id,pos_num) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");

			String tenantId = resData.getTenancy_id();
			Integer storeId = resData.getStore_id();
			JSONObject resultObj = JSONObject.fromObject(resData.getData().get(0));

			Integer cardId = null;
			String cardCode = null;
			String billCode = null;
			String chanel = null;
			Date businessDate = null;
			double mainTrading = 0d;
			double rewardTrading = 0d;
			String operatType = null;
			double mainOriginal = 0d;
			double rewardOriginal = 0d;
			double deposit = 0d;
			String operator = null;
			Timestamp operateTime = null;
			double billMoney = 0d;
			String thirdBillCode = null;
			String billCodeOriginal = null;
			Integer activityId = null;
			Integer customerId = null;
			Integer shift_id = null;
			Integer empId = null;
			
			//挂失卡号
			String card_code_original = "";
			card_code_original = resultObj.optString("card_code_original");
			Double main_balance = resultObj.optDouble("main_balance");
			Double reward_balance = resultObj.optDouble("reward_balance");
			Double total_balance = resultObj.optDouble("total_balance");
			Double useful_credit = resultObj.optDouble("useful_credit");
			operator = resultObj.optString("operator");
			String card_code = resultObj.optString("card_code"); 
			String name = resultObj.optString("name");
			String mobil = resultObj.optString("mobil");
			String level_name = resultObj.optString("level_name");
			String pos_num = JSONObject.fromObject(param).optJSONArray("data").getJSONObject(0).get("pos_num").toString();
			
			
			Object[] objs = null;
			JSONArray list = resultObj.optJSONArray("crm_card_trading_list");
			
			Integer cardClassId = resultObj.optInt("card_class_id");

			for (int i = 0; i < list.size(); i++)
			{
				JSONObject trading = list.getJSONObject(i);

				cardId = trading.optInt("card_id");
				cardCode = trading.optString("card_code");  //补卡卡号
				billCode = trading.optString("bill_code");
				chanel = trading.optString("chanel");
				businessDate = DateUtil.parseDate(trading.optString("business_date"));
				mainTrading = trading.optDouble("main_trading");
				rewardTrading = trading.optDouble("reward_trading");
				operatType = trading.optString("operat_type");
				mainOriginal = trading.optDouble("main_original");
				rewardOriginal = trading.optDouble("reward_original");
				deposit = trading.optDouble("deposit");
				operator = trading.optString("operator");  
				operateTime = DateUtil.currentTimestamp();
				billMoney = trading.optDouble("bill_money");
				customerId = trading.optInt("customer_id");
				thirdBillCode = trading.optString("third_bill_code");
				billCodeOriginal = trading.optString("bill_code_original");
				shift_id = trading.optInt("shift_id");
				empId = trading.optInt("operator_id");
				

				objs = new Object[]
				{ tenantId, cardId, cardCode, billCode, chanel, storeId, businessDate, mainTrading, rewardTrading, operatType, mainOriginal, rewardOriginal, deposit, operator, operateTime, billMoney, thirdBillCode, billCodeOriginal, activityId, customerId, cardClassId, name, mobil, shift_id, empId,pos_num };
				try
				{
					customerDao.update(sqlTrading.toString(), objs);
				}
				catch (Exception e)
				{
					e.printStackTrace();
					logger.info("Exception:" + ExceptionMessage.getExceptionMessage(e));
					throw SystemException.getInstance(PosErrorCode.OPER_ERROR);
				}
				
			}
			
			//打印内容
			try
			{
				JSONObject printJson = new JSONObject();
				printJson.put("print_code",SysDictionary.PRINT_CODE_1015);
				printJson.put("mode","1");
				
				printJson.put("bill_code",billCode);
				printJson.put("card_class_name",resultObj.optString("card_class_name"));
				printJson.put("card_code",card_code);  //补卡卡号
				printJson.put("name",name);
				printJson.put("mobil",mobil);
				printJson.put("main_trading",mainTrading);
				printJson.put("reward_trading",rewardTrading);
				printJson.put("total_main","");
				printJson.put("total_reward","");
				printJson.put("credit","");
				printJson.put("reward_credit","");
				printJson.put("reward_coupon","");
				printJson.put("end_date","");
				printJson.put("operator",operator);  //操作人员
				printJson.put("updatetime",DateUtil.format(operateTime)); //补卡日期
				printJson.put("channel", chanel);
				
				printJson.put("card_code_original", card_code_original);  //挂失卡号
				printJson.put("level_name",level_name);  //会员等级
				printJson.put("useful_credit", useful_credit);  //卡内积分
				printJson.put("print_time", DateUtil.format(DateUtil.currentTimestamp()));  //打印时间
				printJson.put("main_balance", main_balance);  //主账户余额
				printJson.put("reward_balance", reward_balance);  //赠送账户金额
				printJson.put("total_balance", total_balance);  //卡内余额
				printJson.put("operatedate",DateUtil.format(new Date()));  //补卡日期
				printJson.put("pos_num", pos_num); 
				
				
				if (posPrintNewService.isNONewPrint(tenantId, storeId))
				{// 如果启用新的打印模式
					posPrintNewService.posPrintByMode(tenantId, storeId, SysDictionary.PRINT_CODE_1015, printJson);
				}
				else
				{
					List<JSONObject> printList = new ArrayList<JSONObject>();
					printList.add(printJson);
					
					Data paramData = Data.get(tenantId, storeId, Constant.CODE_SUCCESS);
					paramData.setData(printList);
					
					Data result = new Data();
					
					String printCountStr =customerDao.getSysParameter(tenantId, storeId, "MemberReceiptCount");
					if(Tools.isNullOrEmpty(printCountStr))
					{
						printCountStr = "0";
					}
					Integer printCount = Integer.parseInt(printCountStr);
					if (printCount <= 0)
					{
						printCount = 1;
					}
					
					for (int j = 0; j < printCount; j++)
					{
						posPrintService.printPosBill(paramData, result);
					}
				}
			}
			catch (Exception e)
			{
				e.printStackTrace();
			}
			
			//数据结束
		}
	}
	
	@Override
	public void mergeCustomerCardPost(String param, Data resData)
	{
		this.commonPost(param, resData);

		if (resData.isSuccess())
		{
			// 插入卡片交易流水表
			StringBuilder sqlTrading = new StringBuilder(
					"insert into crm_card_trading_list (tenancy_id,card_id,card_code,bill_code,chanel,store_id,business_date,main_trading,reward_trading,operat_type,main_original,reward_original,deposit,operator,operate_time,bill_money,third_bill_code,bill_code_original,activity_id,customer_id,card_class_id,name,mobil,shift_id, operator_id,pos_num) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");

			String tenantId = resData.getTenancy_id();
			Integer storeId = resData.getStore_id();
			JSONObject resultObj = JSONObject.fromObject(resData.getData().get(0));

			Integer cardId = null;
			String cardCode = null;
			String billCode = null;
			String chanel = null;
			Date businessDate = null;
			double mainTrading = 0d;
			double rewardTrading = 0d;
			String operatType = null;
			double mainOriginal = 0d;
			double rewardOriginal = 0d;
			double deposit = 0d;
			String operator = null;
			Timestamp operateTime = null;
			double billMoney = 0d;
			String thirdBillCode = null;
			String billCodeOriginal = null;
			Integer activityId = null;
			Integer customerId = null;
			Integer shift_id = null;
			Integer empId = null;

			Object[] objs = null;
			JSONArray list = resultObj.optJSONArray("crm_card_trading_list");
			
			Integer cardClassId = resultObj.optInt("card_class_id");
			String name = resultObj.optString("name");
			String mobil = resultObj.optString("mobil");
			String pos_num = JSONObject.fromObject(param).optJSONArray("data").getJSONObject(0).get("pos_num").toString();

			for (int i = 0; i < list.size(); i++)
			{
				JSONObject trading = list.getJSONObject(i);

				cardId = trading.optInt("card_id");
				cardCode = trading.optString("card_code");
				billCode = trading.optString("bill_code");
				chanel = trading.optString("chanel");
				businessDate = DateUtil.parseDate(trading.optString("business_date"));
				mainTrading = trading.optDouble("main_trading");
				rewardTrading = trading.optDouble("reward_trading");
				operatType = trading.optString("operat_type");
				mainOriginal = trading.optDouble("main_original");
				rewardOriginal = trading.optDouble("reward_original");
				deposit = trading.optDouble("deposit");
				operator = trading.optString("operator");
				operateTime = DateUtil.currentTimestamp();
				billMoney = trading.optDouble("bill_money");
				customerId = trading.optInt("customer_id");
				shift_id = trading.optInt("shift_id");
				empId = trading.optInt("operator_id");

				objs = new Object[]
				{ tenantId, cardId, cardCode, billCode, chanel, storeId, businessDate, mainTrading, rewardTrading, operatType, mainOriginal, rewardOriginal, deposit, operator, operateTime, billMoney, thirdBillCode, billCodeOriginal, activityId, customerId, cardClassId, name, mobil, shift_id, empId,pos_num };
				try
				{
					customerDao.update(sqlTrading.toString(), objs);
				}
				catch (Exception e)
				{
					e.printStackTrace();
					logger.info("Exception:" + ExceptionMessage.getExceptionMessage(e));
					throw SystemException.getInstance(PosErrorCode.OPER_ERROR);
				}
			}
			
		}
	}

	@Override
	public void backOutCustomerCardPost(String param, Data resData)
	{
		this.commonPost(param, resData);
		if (resData.isSuccess())
		{
			// 插入卡片交易流水表
			StringBuilder sqlTrading = new StringBuilder(
					"insert into crm_card_trading_list (tenancy_id,card_id,card_code,bill_code,chanel,store_id,business_date,main_trading,reward_trading,operat_type,main_original,reward_original,deposit,operator,operate_time,bill_money,third_bill_code,bill_code_original,activity_id,customer_id,card_class_id,name,mobil,shift_id,operator_id,pos_num) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");

			String tenantId = resData.getTenancy_id();
			Integer storeId = resData.getStore_id();
			JSONObject resultObj = JSONObject.fromObject(resData.getData().get(0));

			Integer cardId = null;
			String cardCode = null;
			String billCode = null;
			String chanel = null;
			Date businessDate = null;
			double mainTrading = 0d;
			double rewardTrading = 0d;
			String operatType = null;
			double mainOriginal = 0d;
			double rewardOriginal = 0d;
			double deposit = 0d;
			String operator = null;
			Timestamp operateTime = null;
			double billMoney = 0d;
			String thirdBillCode = null;
			String billCodeOriginal = null;
			Integer activityId = null;
			Integer customerId = null;
			Integer shift_id = null;
			Integer empId = null;

			Object[] objs = null;
			JSONArray list = resultObj.optJSONArray("crm_card_trading_list");
			
			Integer cardClassId = resultObj.optInt("card_class_id");
			String name = resultObj.optString("name");
			String mobil = resultObj.optString("mobil");
			String pos_num = JSONObject.fromObject(param).optJSONArray("data").getJSONObject(0).get("pos_num").toString();

			for (int i = 0; i < list.size(); i++)
			{
				JSONObject trading = list.getJSONObject(i);

				cardId = trading.optInt("card_id");
				cardCode = trading.optString("card_code");
				billCode = trading.optString("bill_code");
				chanel = trading.optString("chanel");
				businessDate = DateUtil.parseDate(trading.optString("business_date"));
				mainTrading = trading.optDouble("main_trading");
				rewardTrading = trading.optDouble("reward_trading");
				operatType = trading.optString("operat_type");
				mainOriginal = trading.optDouble("main_original");
				rewardOriginal = trading.optDouble("reward_original");
				deposit = trading.optDouble("deposit");
				operator = trading.optString("operator");
				operateTime = DateUtil.currentTimestamp();
				billMoney = trading.optDouble("bill_money");
				customerId = trading.optInt("customer_id");
				thirdBillCode = trading.optString("third_bill_code");
				shift_id = trading.optInt("shift_id");
				empId = trading.optInt("operator_id");

				objs = new Object[]
				{ tenantId, cardId, cardCode, billCode, chanel, storeId, businessDate, mainTrading, rewardTrading, operatType, mainOriginal, rewardOriginal, deposit, operator, operateTime, billMoney, thirdBillCode, billCodeOriginal, activityId, customerId, cardClassId, name, mobil, shift_id, empId,pos_num };
				try
				{
					customerDao.update(sqlTrading.toString(), objs);
				}
				catch (Exception e)
				{
					e.printStackTrace();
					logger.info("Exception:" + ExceptionMessage.getExceptionMessage(e));
					throw SystemException.getInstance(PosErrorCode.OPER_ERROR);
				}
			}
		}
	}
	
	@Override
	@Deprecated
	public void rechargePost(Data param, Data resData,String posNum)
	{
		try
		{
			String tenantId = param.getTenancy_id();
			Integer storeId = param.getStore_id();
			
			if (this.commonPostCheck(param))
			{
				JSONObject jsobj = JSONObject.fromObject(param);
				this.formatParam(param, jsobj);
				
				JSONObject paramJson = JSONObject.fromObject(param.getData().get(0));
				String thirdBillCode = paramJson.optString("bill_code");
				String isInvoice = paramJson.optString("is_invoice");
				
				List<JSONObject> tradingList = null;
				
				if (Tools.hv(thirdBillCode))
				{
					tradingList = customerDao.getCrmCardTradingListByThirdBillCode(tenantId, storeId, thirdBillCode);
				}

				this.commonPost(jsobj.toString(), resData);

				String paymentState = SysDictionary.THIRD_PAY_STATUS_SUCCESS;
				String rechargeState = SysDictionary.REQUEST_STATUS_ING;
				String requestStatus = SysDictionary.REQUEST_STATUS_ING;
				String requestCode = null;
				String requestMsg = null;
				
				if (resData.isSuccess())
				{
					rechargeState = SysDictionary.REQUEST_STATUS_COMPLETE;
					requestStatus = SysDictionary.REQUEST_STATUS_COMPLETE;
					
					if(null != tradingList && tradingList.size()>0)
					{
						JSONObject tradingJo = tradingList.get(0);
						if("1".equals(tradingJo.optString("is_invoice")))
						{
							isInvoice = "1"; // 第三方支付时开电子发票
						}
						customerDao.deleteCrmCardTradingListByThirdBillCode(tenantId, storeId, thirdBillCode);
						customerDao.deleteCrmCardPaymentListByThirdBillCode(tenantId, thirdBillCode);
					}
					
					// 插入卡片交易流水表
					StringBuilder sqlTrading = new StringBuilder(
							"insert into crm_card_trading_list (tenancy_id,card_id,card_code,bill_code,chanel,store_id,business_date,main_trading,reward_trading,operat_type,main_original,reward_original,deposit,operator,operate_time,bill_money,third_bill_code,bill_code_original,activity_id,customer_id,card_class_id,name,mobil,shift_id,operator_id,is_invoice,invoice_balance,pos_num) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
					// 插入卡片交易付款方式流水
					StringBuilder sqlPayment = new StringBuilder("insert into crm_card_payment_list (tenancy_id,card_id,payment_id,bill_code,pay_money,pay_no,rexchange_rate,local_currency,third_bill_code) VALUES (?,?,?,?,?,?,?,?,?)");

					JSONObject resultObj = JSONObject.fromObject(resData.getData().get(0));

					Integer cardId = null;
					String cardCode = null;
					Integer cardClassId = resultObj.optInt("card_class_id");
					String name = resultObj.optString("name");
					String mobil = resultObj.optString("mobil");
					String levelName = resultObj.optString("level_name");
					double mainTrading = 0d;
					double rewardTrading = 0d;
					double mainOriginal = 0d;
					double rewardOriginal = 0d;
					double deposit = 0d;
					
					Date businessDate = null;
					Integer shift_id = null;
					Integer empId = null;
					String chanel = null;
					String operatType = null;
					String operator = null;
					Timestamp operateTime = null;
					
					double billMoney = 0d;
					String billCode=null;
					String billCodeOriginal = null;
					Integer activityId = null;
					Integer customerId = null;
					Integer paymentId = null;
					double payMoney = 0d;
					String payNo = null;
					double rexchangeRate = 0d;
					double localCurrency = 0d;
					String strUrlContent = null;
					String strUrlPath = null;
					double invoiceAmount = 0d;
					
					Object[] objs = null;
					JSONArray list = resultObj.optJSONArray("crm_card_trading_list");
					for (int i = 0; i < list.size(); i++)
					{
						JSONObject trading = list.getJSONObject(i);

						cardId = trading.optInt("card_id");
						cardCode = trading.optString("card_code");
						billCode = trading.optString("bill_code");
						chanel = trading.optString("chanel");
						businessDate = DateUtil.parseDate(trading.optString("business_date"));
						mainTrading = trading.optDouble("main_trading");
						rewardTrading = trading.optDouble("reward_trading");
						operatType = trading.optString("operat_type");
						mainOriginal = trading.optDouble("main_original");
						rewardOriginal = trading.optDouble("reward_original");
						deposit = trading.optDouble("deposit");
						operator = trading.optString("operator");
						operateTime = DateUtil.currentTimestamp();
						billMoney = trading.optDouble("bill_money");
						customerId = trading.optInt("customer_id");
						shift_id = trading.optInt("shift_id");
						thirdBillCode = trading.optString("third_bill_code");
						activityId = trading.optInt("activity_id");
						paymentId = trading.optInt("payment_id");
						payMoney = trading.optDouble("pay_money");
						payNo = trading.optString("pay_no");
						rexchangeRate = trading.optDouble("rexchange_rate");
						localCurrency = trading.optDouble("local_currency");
						empId = trading.optInt("operator_id");

						objs = new Object[]
						{ tenantId, cardId, paymentId, billCode, payMoney, payNo, rexchangeRate, localCurrency, thirdBillCode };

						customerDao.update(sqlPayment.toString(), objs);

						logger.info("电子发票二维码生成开始!");
						// 再次判断开具发票状态 1：开电子发票  1以外：不开电子发票
						JSONObject printInvJo = new JSONObject();
						if("1".equals(isInvoice)) // 开具电子发票
						{
							// 获取法人信息
							JSONObject legalPerInfoJo = new JSONObject();
							legalPerInfoJo = customerDao.getCrmLegalPerInfo(tenantId, storeId ,trading);
							if(!(Tools.isNullOrEmpty(legalPerInfoJo) || Double.isNaN(legalPerInfoJo.optDouble("tax_rate"))))
							{
								// 计算开票金额
								List<JSONObject> invoicePaymentList = customerDao.getCrmInvoicePayment(tenantId, storeId, trading);
								invoiceAmount = invoicePaymentList.get(0).optDouble("pay_money");
								if(Double.isNaN(invoiceAmount))
								{
									invoiceAmount = 0;
								}
								if(invoiceAmount > 0)
								{
									// 生成电子发票，获得电子发票的打印信息
									logger.info("电子发票二维码生成开始：传入参数：" + trading.toString());
									try
									{
										trading.put("invoice_amount", invoiceAmount);
										double taxRate = legalPerInfoJo.optDouble("tax_rate");
										if(Double.isNaN(taxRate))
										{
											taxRate = 0d;
										}
										trading.put("tax_rate", taxRate);
										trading.put("operateTime",DateUtil.format(operateTime));
										printInvJo = getPrintInvoiceInfo(tenantId, storeId, trading);
										strUrlPath = printInvJo.optString("url_path");
										strUrlContent = printInvJo.optString("url_content");
									}
									catch(Exception e)
									{
										e.printStackTrace();
										logger.error("电子发票二维码生成失败:", e);
									}
								}
								else
								{
									isInvoice = "0";
									logger.info("电子发票金额为0!");
								}
							}
							else
							{
								isInvoice = "0";
								logger.info("法人信息设置错误!");
							}
						}
						logger.info("电子发票二维码生成结束!");
						
						objs = new Object[]
						{ tenantId, cardId, cardCode, billCode, chanel, storeId, businessDate, mainTrading, rewardTrading, operatType, mainOriginal, rewardOriginal, deposit, operator, operateTime, billMoney, thirdBillCode, billCodeOriginal, activityId, customerId, cardClassId, name, mobil,
								shift_id, empId, isInvoice, payMoney,posNum };

						customerDao.update(sqlTrading.toString(), objs);
					}
					
					JSONObject payWayJson = customerDao.getPaymentWayByID(tenantId, storeId, paymentId);
					
					try
					{
						JSONObject printJson = new JSONObject();
						printJson.put("print_code",SysDictionary.PRINT_CODE_1008);
						printJson.put("mode","1");
						printJson.put("pos_num",paramJson.optString("pos_num"));
						printJson.put("level_name",levelName);
						printJson.put("bill_code",billCode);
						printJson.put("bill_money",payMoney);
						printJson.put("pay_money",payMoney);
						printJson.put("income",payMoney);
						printJson.put("paymenttypename",payWayJson.optString("payment_name"));
						printJson.put("card_class_name",resultObj.optString("card_class_name"));
						printJson.put("card_code",cardCode);
						printJson.put("name",name);
						printJson.put("mobil",mobil);
						printJson.put("main_trading",mainTrading);
						printJson.put("reward_trading",rewardTrading);
						printJson.put("main_balance",resultObj.optDouble("main_balance"));
						printJson.put("reward_balance",resultObj.optDouble("reward_balance"));
						printJson.put("total_main","");
						printJson.put("total_reward","");
						printJson.put("credit","");
						printJson.put("reward_credit",resultObj.optDouble("reward_credit"));
						printJson.put("useful_credit",resultObj.optDouble("useful_credit"));
						printJson.put("reward_coupon","");
						printJson.put("end_date","");
						printJson.put("operator",operator);
						printJson.put("updatetime",DateUtil.format(operateTime));
						printJson.put("url_content", strUrlContent);
						printJson.put("url_path", strUrlPath);
						printJson.put("is_invoice", isInvoice);
						printJson.put("invoice_amount", invoiceAmount);
						printJson.put("channel", chanel);
						
						if (posPrintNewService.isNONewPrint(tenantId, storeId))
						{// 如果启用新的打印模式
							posPrintNewService.posPrintByMode(tenantId, storeId, SysDictionary.PRINT_CODE_1008, printJson);
						}
						else
						{
							List<JSONObject> printList = new ArrayList<JSONObject>();
							printList.add(printJson);
							
							Data paramData = Data.get(tenantId, storeId, Constant.CODE_SUCCESS);
							paramData.setData(printList);
							
							Data result = new Data();
							
							String printCountStr =customerDao.getSysParameter(tenantId, storeId, "MemberReceiptCount");
							if(Tools.isNullOrEmpty(printCountStr))
							{
								printCountStr = "0";
							}
							Integer printCount = Integer.parseInt(printCountStr);
							if (printCount <= 0)
							{
								printCount = 1;
							}
							
							for (int i = 0; i < printCount; i++)
							{
								posPrintService.printPosBill(paramData, result);
							}
						}
					}
					catch (Exception e)
					{
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
				}
				else
				{
					rechargeState = SysDictionary.REQUEST_STATUS_FAILURE;
					requestStatus = SysDictionary.REQUEST_STATUS_FAILURE;
					requestCode = String.valueOf(resData.getCode());
					requestMsg = resData.getMsg();
				}
				
				if(null != tradingList && tradingList.size()>0)
				{
					customerDao.updateCrmCardTradingListForState(tenantId, storeId, thirdBillCode, paymentState, rechargeState, requestStatus, requestCode, requestMsg);
				}
			}
		}
		catch (SystemException se)
		{
			throw se;
		}
		catch (Exception e)
		{
			e.printStackTrace();
			logger.info("Exception:" + ExceptionMessage.getExceptionMessage(e));
			throw SystemException.getInstance(PosErrorCode.OPER_ERROR);
		}
	}

	@Override
	@Deprecated
	public void rechargePreparePost(Data param,PosCodeService codeService, Data resData)
	{
		try
		{
			if (this.commonPostCheck(param))
			{
				String tenantId=param.getTenancy_id();
				int storeId = param.getStore_id();
				
				List<?> paramList = param.getData();
				
				if (paramList == null || paramList.isEmpty())
				{
					throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
				}
				
				JSONObject dataJson = JSONObject.fromObject(paramList.get(0));
				
				Date reportDate = DateUtil.parseDate(dataJson.optString("report_date"));
				String optNum = dataJson.optString("opt_num");
				Integer shiftId =dataJson.optInt("shift_id");
				String chanel = dataJson.optString("chanel");
				String cardCode = dataJson.optString("card_code");
				String isInvoice = dataJson.optString("is_invoice");
				if(Tools.isNullOrEmpty(cardCode))
				{
					cardCode = dataJson.optString("third_code");
				}
//				String thirdCode = dataJson.optString("third_code");
				Double income = dataJson.optDouble("income");
				Integer payment =dataJson.optInt("payment");
				
				Timestamp currentTime = DateUtil.currentTimestamp();
				
				String billCode = "";
				
				try
				{
					// 生成新的账单号和流水单号
					JSONObject object = new JSONObject();
					object.element("store_id", storeId);
					object.element("busi_date", DateUtil.format(reportDate));
					billCode = codeService.getCode(tenantId, Code.POS_THIRD_PAYMENT_ORDER_NUM, object);// 调用统一接口来实现
					if(Tools.isNullOrEmpty(billCode))
					{
						throw new SystemException(PosErrorCode.GET_BILLNUM_SERVICE_ERROR);
					}
				}
				catch (Exception e)
				{
					logger.info("开台：" + ExceptionMessage.getExceptionMessage(e));
					throw new SystemException(PosErrorCode.GET_BILLNUM_SERVICE_ERROR);
				}
				
				customerDao.insertCrmCardTradingList(tenantId, storeId, reportDate, shiftId, Integer.valueOf(optNum), null, chanel, null, cardCode, null, null, null, null, null, null, SysDictionary.OPERAT_TYPE_CZ, 0d, billCode, null, null, null, income, 0d, 0d, 0d, null, null, currentTime, null,
						null, null, null, null, null, null, null, null, isInvoice, SysDictionary.REQUEST_STATUS_ING,SysDictionary.THIRD_PAY_STATUS_PAYING,SysDictionary.REQUEST_STATUS_ING);
				
				customerDao.insertCrmCardPaymentList(tenantId, null, null, billCode, payment, null, income, income, null, currentTime, null);
				
				JSONObject resultJson = new JSONObject();
				resultJson.put("bill_code", billCode);
				
				List<JSONObject> resultList = new ArrayList<JSONObject>();
				resultList.add(resultJson);
				
				resData.setData(resultList);
			}
		}
		catch (SystemException se)
		{
			throw se;
		}
		catch (Exception e)
		{
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
	@Override
	@Deprecated
	public Data precreateThirdPayment(Data params, String path) throws Exception
	{
		String tenancyId = params.getTenancy_id();
		Integer storeId = params.getStore_id();

		JSONObject paramJson = JSONObject.fromObject(params.getData().get(0));

		Data returnData = null;
//		Timestamp currentTimes = DateUtil.currentTimestamp();
		String orderNum = paramJson.optString("order_no");
//		String orderNo =  orderNum+ "@" + String.valueOf(currentTimes.getTime());
		paramJson.put("order_no", orderNum);
		paramJson.put("order_num", orderNum);
		
		returnData = thirdPaymentService.precreatePayment(tenancyId, storeId, paramJson, path);
		
		if (Constant.CODE_SUCCESS == returnData.getCode())
		{
			JSONObject responseJson = JSONObject.fromObject(returnData.getData().get(0));
			String status = responseJson.optString("payment_state");
			String qrCode = responseJson.optString("qrcode");
			if (!SysDictionary.THIRD_PAY_STATUS_PAYING.equals(status) || Tools.isNullOrEmpty(qrCode))
			{
				// 获取二维码失败,删除付款
				String updateSql = new String("update crm_card_trading_list set payment_state=? where tenancy_id=? and store_id=? and third_bill_code=?");
				customerDao.update(updateSql, new Object[]
				{ SysDictionary.THIRD_PAY_STATUS_FAIL, tenancyId, storeId });
				
				JSONObject data = new JSONObject();
				data.put("qrcode", "");
				data.put("qrcode_url", "");
				data.put("transaction_no", "");
				data.put("failure_code", "");
				data.put("failure_msg", "");
				data.put("payment_state", SysDictionary.THIRD_PAY_STATUS_FAIL);
				List<JSONObject> dataList = new ArrayList<JSONObject>();
				dataList.add(data);
				returnData.setCode(PosErrorCode.CREATE_QRCODE_ERROR.getNumber());
				returnData.setMsg(PosErrorCode.CREATE_QRCODE_ERROR.getMessage());
			}
		}
		else
		{
			// 获取二维码失败,删除记录
			String updateSql = new String("update crm_card_trading_list set payment_state=? where tenancy_id=? and store_id=? and third_bill_code=?");
			customerDao.update(updateSql, new Object[]
			{ SysDictionary.THIRD_PAY_STATUS_FAIL, tenancyId, storeId });
			
			JSONObject data = new JSONObject();
			data.put("qrcode", "");
			data.put("qrcode_url", "");
			data.put("transaction_no", "");
			data.put("failure_code", "");
			data.put("failure_msg", "");
			data.put("payment_state", SysDictionary.THIRD_PAY_STATUS_FAIL);
			List<JSONObject> dataList = new ArrayList<JSONObject>();
			dataList.add(data);
			returnData.setData(dataList);
		}
		return returnData;
	}

	@Override
	@Deprecated
	public Data barcodeThirdPayment(Data params, JSONObject printJson) throws Exception
	{

		String tenancyId = params.getTenancy_id();
		Integer storeId = params.getStore_id();

		Data returnData = null;
		JSONObject paramJson = JSONObject.fromObject(params.getData().get(0));

		String report_Date = ParamUtil.getDateStringValue(paramJson, "report_date");
		int shiftId = paramJson.optInt("shift_id");
		String posNum = paramJson.optString("pos_num");
		String optNum = paramJson.optString("opt_num");
		String orderNum = paramJson.optString("order_no");
		
//		Timestamp currentTimes = DateUtil.currentTimestamp();
//		String orderNo =  orderNum+ "@" + String.valueOf(currentTimes.getTime());
		paramJson.put("order_no", orderNum);
		paramJson.put("order_num", orderNum);
		
		returnData = thirdPaymentService.barcodePayment(tenancyId, storeId, paramJson);

		if (Constant.CODE_SUCCESS == returnData.getCode())
		{
			List<?> returnDataList = returnData.getData();
			String paymentState = SysDictionary.THIRD_PAY_STATUS_PAYING;
			JSONObject returnJson = new JSONObject();
			if (null != returnDataList && returnDataList.size() > 0)
			{
				returnJson = JSONObject.fromObject(returnDataList.get(0));
				paymentState = returnJson.optString("payment_state");
			}

			switch (paymentState)
			{
				case SysDictionary.THIRD_PAY_STATUS_SUCCESS:
					
					this.rechargePost(this.getHyparam(params, returnJson.optString("transaction_no")), returnData,posNum);
					break;
				case SysDictionary.THIRD_PAY_STATUS_FAIL:
					
					String updateSql = new String("update crm_card_trading_list set payment_state=? where tenancy_id=? and store_id=? and third_bill_code=?");
					customerDao.update(updateSql, new Object[]
					{ SysDictionary.THIRD_PAY_STATUS_FAIL, tenancyId, storeId });
					
					returnData.setCode(PosErrorCode.THIRD_PAYMENT_FAILURE.getNumber());
					returnData.setMsg(returnJson.optString("failure_msg"));
					break;
				default:
					// 返回账单付款中
					returnData.setCode(Constant.CODE_SUCCESS);
					returnData.setMsg(returnJson.optString("failure_msg"));
					break;
			}
		}
		else if (Constant.CODE_CONN_EXCEPTION != returnData.getCode())
		{
			// 删除付款
			String updateSql = new String("update crm_card_trading_list set payment_state=? where tenancy_id=? and store_id=? and third_bill_code=?");
			customerDao.update(updateSql, new Object[]
			{ SysDictionary.THIRD_PAY_STATUS_FAIL, tenancyId, storeId });
			
			returnData.setCode(PosErrorCode.THIRD_PAYMENT_FAILURE.getNumber());
		}
		
		JSONObject queryJson = new JSONObject();
		queryJson.put("report_date", report_Date);
		queryJson.put("shift_id", shiftId);
		queryJson.put("pos_num", posNum);
		queryJson.put("opt_num", optNum);
		queryJson.put("bill_num", orderNum);
		queryJson.put("pay_id", 0);
		queryJson.put("isprint_bill", "Y");
		List<JSONObject> queryList = new ArrayList<JSONObject>();
		queryList.add(queryJson);
		Data queryData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
		queryData.setData(queryList);
		this.queryCardRecharge(queryData, returnData);
		
		customerDao.savePosLog(tenancyId, storeId, posNum, optNum, optNum, shiftId, DateUtil.parseDate(report_Date), Constant.TITLE, "扫码支付", "账单编号:" + orderNum, "Result:" + returnData.getMsg());
		return returnData;
	}
	
	/**
	 * 封装会员充值接口
	 * 
	 * @param param
	 * @return
	 */
	@Deprecated
	private Data getHyparam(Data param,String transactionNo) throws Exception
	{
		String tenancyId = param.getTenancy_id();
		Integer storeId = param.getStore_id();

		JSONObject paramData = JSONObject.fromObject(param.getData().get(0));
		String opt_num = paramData.optString("opt_num");
		String operator = customerDao.getEmpNameById(opt_num, tenancyId, storeId);
		String billCode = paramData.optString("order_no");

		List<JSONObject> cardTradingList = customerDao.qureyCrmCardTradingListByThirdBillCode(tenancyId, storeId, billCode);
		JSONObject cardTradingJson = JSONObject.fromObject(cardTradingList.get(0));
		
		JSONObject data = new JSONObject();

		data.put("card_code", cardTradingJson.optString("card_code"));
		data.put("third_code", cardTradingJson.optString("card_code"));
		data.put("income", cardTradingJson.optString("local_currency"));
		data.put("chanel", cardTradingJson.optString("chanel"));
		data.put("payment", cardTradingJson.optString("payment_id"));
		data.put("bill_code", billCode);
		data.put("pay_no", transactionNo);
		data.put("business_date", paramData.optString("report_date"));
		data.put("report_date", paramData.optString("report_date"));
		data.put("shift_id", paramData.optString("shift_id"));
		data.put("pos_num", paramData.optString("pos_num"));
		data.put("opt_num", opt_num);
		data.put("operator_id", opt_num);
		data.put("operator", operator);
		data.put("updatetime", DateUtil.getNowDateYYDDMMHHMMSS());

		List<JSONObject> dataList = new ArrayList<JSONObject>();
		dataList.add(data);

		Data returnData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
		returnData.setType(Type.CUSTOMER_CARD_RECHARGE);
		returnData.setOper(Oper.add);
		returnData.setData(dataList);

		return returnData;
	}

	@Override
	@Deprecated
	public void queryCardRecharge(Data param,Data returnData) throws Exception
	{
		// TODO Auto-generated method stub
		String tenancyId = param.getTenancy_id();
		int storeId = param.getStore_id();
		
		List<?> paramList = param.getData();
		if (paramList == null || paramList.isEmpty())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}
		
		JSONObject para = JSONObject.fromObject(paramList.get(0));
//		String report_date = ParamUtil.getDateStringValue(para, "report_date");
		String thirdBillCode = para.optString("bill_num");
		int shiftId = para.optInt("shift_id");
		String posNum = para.optString("pos_num");
//		String optNum = para.optString("opt_num");
		
		if (Tools.isNullOrEmpty(thirdBillCode))
		{
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_BILL_NUM);
		}

//		Timestamp currentTime = DateUtil.currentTimestamp();

		List<JSONObject> tradingList = customerDao.getCrmCardTradingListByThirdBillCode(tenancyId, storeId, thirdBillCode);
		
		JSONObject resultJson = new JSONObject(); 
		if(null !=tradingList && tradingList.size()>0)
		{
			JSONObject tradingJson = tradingList.get(0);
			
			String paymentState = tradingJson.optString("payment_state");
			String rechargeState = tradingJson.optString("recharge_state");
			
			List<JSONObject> paymentList = customerDao.getCrmCardPaymentListByThirdBillCode(tenancyId, thirdBillCode);
			JSONObject payment = paymentList.get(0);
			
			if(SysDictionary.THIRD_PAY_STATUS_SUCCESS.equals(paymentState) && SysDictionary.REQUEST_STATUS_ING.equals(rechargeState))
			{//判断对否完成充值
//				JSONObject paramJson = new JSONObject();
//				paramJson.put("business_date", report_date);
//				paramJson.put("shift_id", shiftId);
//				paramJson.put("chanel", tradingJson.optString("chanel"));
//				paramJson.put("pos_num", posNum);
//				paramJson.put("opt_num", optNum);
//				paramJson.put("operator", tradingJson.optString("operator"));
//				paramJson.put("operator_id", tradingJson.optInt("operator_id"));
//				paramJson.put("card_code", tradingJson.optString("card_code"));
//				paramJson.put("third_code", tradingJson.optString("card_code"));
//				paramJson.put("bill_code", tradingJson.optString("third_bill_code"));
//				paramJson.put("income", payment.optString("pay_money"));
//				paramJson.put("payment", payment.optString("payment_id"));
//				paramJson.put("pay_no", payment.optString("pay_no"));
//				paramJson.put("updatetime", DateUtil.format(currentTime));
//				
//				Data resData = new Data();
//				this.rechargePost(param, resData);
			}
			
			String state = SysDictionary.PAYMENT_STATE_PAY_FAILURE;
			
			if(SysDictionary.THIRD_PAY_STATUS_SUCCESS.equals(paymentState)&&SysDictionary.REQUEST_STATUS_COMPLETE.equals(rechargeState))
			{
				state  = SysDictionary.PAYMENT_STATE_PAY_COMPLETE;
			}
			else if(SysDictionary.THIRD_PAY_STATUS_PAYING.equals(paymentState)||SysDictionary.REQUEST_STATUS_ING.equals(rechargeState))
			{
				state  = SysDictionary.PAYMENT_STATE_PAY;
			}
			
			JSONObject resultPayment = new JSONObject();
			resultPayment.put("id", payment.optInt("id"));
			resultPayment.put("jzid", payment.optInt("payment_id"));
			resultPayment.put("amount", payment.optDouble("pay_money"));
			resultPayment.put("currency_amount", payment.optString("pay_money"));
			resultPayment.put("count", 1);
			resultPayment.put("name", "");
			resultPayment.put("number", "");
			resultPayment.put("phone", "");
			resultPayment.put("shift_id", shiftId);
			resultPayment.put("pos_num",posNum);
			resultPayment.put("cashier_num", "");
			resultPayment.put("last_updatetime", "");
			resultPayment.put("customer_id", "");
			resultPayment.put("bill_code",tradingJson.optString("bill_code"));
			resultPayment.put("remark", "");

			List<JSONObject> resultPaymentList = new ArrayList<JSONObject>();
			resultPaymentList.add(resultPayment);
			
			resultJson.put("bill_num", thirdBillCode);
			resultJson.put("table_code", "");
			resultJson.put("payment_amount", 0d);
			resultJson.put("discount_amount", 0d);
			
			resultJson.put("difference", 0d);
			resultJson.put("change", 0d);
			resultJson.put("payment_state",state);
			resultJson.put("paymentlist", resultPaymentList);

			List<JSONObject> resultList = new ArrayList<JSONObject>();
			resultList.add(resultJson);

			returnData.setData(resultList);
		}
		else
		{
			throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_BILL);
		}
	}

	@Override
	@Deprecated
	public Data queryThirdPaymentForRunnable(String tenancyId, int storeId,PosThirdPaymentOrderEntity params, JSONObject printJson) throws Exception
	{
		Data returnData = null;

		// String report_Date = ParamUtil.getDateStringValue(para,
		// "report_date");
		Date reportDate = params.getReport_date();
		String report_Date = DateUtil.format(reportDate,"yyyy-MM-dd");
		String orderNo = params.getAid_order_num();
		int shiftId = params.getShift_id();
		String posNum = params.getPos_num();
		String optNum = params.getOpt_num();
//		Integer paymentId = params.getPayment_id();

		// JSONObject para = new JSONObject();
		returnData = thirdPaymentService.queryPayment(tenancyId, storeId, params);
		/**
		 * * 若请求成功,根据service_type判断,
		 * service_type=充值(hy01),调用总部充值接口(CUSTOMER_CARD_RECHARGE),返回充值结果;
		 * service_type=消费(pos04),调用结账接口(BILL_PAYMENT),返回结账结果;
		 **/
//		String serviceType = params.getService_type();

		List<?> returnDataList = returnData.getData();
		String paymentState = SysDictionary.THIRD_PAY_STATUS_PAYING;
		JSONObject returnJson = new JSONObject();
		if (null != returnDataList && returnDataList.size() > 0)
		{
			returnJson = JSONObject.fromObject(returnDataList.get(0));
			paymentState = returnJson.optString("payment_state");
		}

		switch (paymentState)
		{
			case SysDictionary.THIRD_PAY_STATUS_SUCCESS: 
				
				JSONObject paramJson = new JSONObject();
				paramJson.put("report_date", report_Date);
				paramJson.put("shift_id", shiftId);
				paramJson.put("pos_num", posNum);
				paramJson.put("opt_num", optNum);
				paramJson.put("order_no", orderNo);
				List<JSONObject> paramList = new ArrayList<JSONObject>();
				paramList.add(paramJson);
				Data param = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
				param.setData(paramList);
				
				Data cardParam = this.getHyparam(param, returnJson.optString("transaction_no"));
				this.rechargePost(cardParam, returnData,posNum);
				break;
			case SysDictionary.THIRD_PAY_STATUS_FAIL:

				String updateSql = new String("update crm_card_trading_list set payment_state=? where tenancy_id=? and store_id=? and third_bill_code=?");
				customerDao.update(updateSql, new Object[]
				{ SysDictionary.THIRD_PAY_STATUS_FAIL, tenancyId, storeId });

				break;
			default:
				JSONObject queryJson = new JSONObject();
				queryJson.put("report_date", report_Date);
				queryJson.put("shift_id", shiftId);
				queryJson.put("pos_num", posNum);
				queryJson.put("opt_num", optNum);
				queryJson.put("bill_num", orderNo);
				queryJson.put("pay_id", 0);
				queryJson.put("isprint_bill", "Y");
				List<JSONObject> queryList = new ArrayList<JSONObject>();
				queryList.add(queryJson);
				Data queryData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
				queryData.setData(queryList);
				this.queryCardRecharge(queryData, returnData);
				break;
		}
		return returnData;

	}
	
	@Override
	@Deprecated
	public void cancelCardRecharge(Data param,Data returnData) throws Exception
	{
		// TODO Auto-generated method stub
		String tenancyId = param.getTenancy_id();
		int storeId = param.getStore_id();
		String secret = param.getSecret();
		Map<String, Object> map = ReqDataUtil.getDataMap(param);
		//账单编号
		String billCode = ParamUtil.getStringValue(map, "bill_code", true, PosErrorCode.NOT_NULL_BILL_NUM);
		Date reportDate = ParamUtil.getDateValue(map, "report_date", true, PosErrorCode.NOT_NULL_REPORT_DATE);
		
		//是否退款
		String isRefund = ParamUtil.getStringValue(map, "is_refund", false, null);	
		int shiftId = ParamUtil.getIntegerValue(map, "shift_id", false, null);
		String posNum = ParamUtil.getStringValue(map, "pos_num", false, null);
		String optNum = ParamUtil.getStringValue(map, "opt_num", false, null);
		String chanel = ParamUtil.getStringValue(map, "chanel", false, null);
		
		if ("".equals(isRefund))
		{
			isRefund = "N";
		}
		if ("Y".equals(isRefund))
		{
			List<JSONObject> payMentList = customerDao.getCrmPayMentWayByThirdBillCode(tenancyId, billCode);
			if (null != payMentList && payMentList.size() > 0)
			{
				JSONObject para = JSONObject.fromObject(payMentList.get(0));
				para.put("report_date", DateUtil.format(reportDate));
				para.put("shift_id", shiftId);
				para.put("pos_num", posNum);
				para.put("opt_num", optNum);
				para.put("chanel", chanel);
				para.put("service_type", SysDictionary.SERVICE_TYPE_RECHARGE);
				
				String aidOrderNum=billCode;
				
				JSONObject cancelParamJson = new JSONObject();
				cancelParamJson.put("order_num", billCode);
				cancelParamJson.put("total_amount", para.optDouble("amount"));
				cancelParamJson.put("client_ip", "");
				cancelParamJson.put("refunds_order", "");
				cancelParamJson.put("order_no", aidOrderNum);
				cancelParamJson.put("settle_amount", para.optDouble("amount"));
				cancelParamJson.put("payment_id",  para.optString("pay_id"));
				cancelParamJson.put("service_type", SysDictionary.SERVICE_TYPE_RECHARGE);
				cancelParamJson.put("channel",  chanel);
				cancelParamJson.put("report_date",  DateUtil.format(reportDate));
				cancelParamJson.put("shift_id",  shiftId);
				cancelParamJson.put("pos_num",  posNum);
				cancelParamJson.put("opt_num",  optNum);
				cancelParamJson.put("currency_name",  "");
				
//				returnData = thirdPaymentService.refundPayment(tenancyId, storeId, para);
						
				returnData = thirdPaymentService.cancelPayment(tenancyId, storeId, cancelParamJson);
			}
		}
		
		List<JSONObject> listTrading =  customerDao.getCrmCardTradingListByThirdBillCode(tenancyId, storeId, billCode);
		
		if ( null != listTrading && listTrading.size() > 0)
		{
			JSONObject Trading = listTrading.get(0);
			if (SysDictionary.REQUEST_STATUS_COMPLETE.equals(Trading.getString("recharge_state")))
			{
				//调用撤销充值
				JSONObject joCard = new JSONObject();
				joCard.put("ischeck", "Y");
				joCard.put("updatetime", DateUtil.getNowDateYYDDMMHHMMSS());
				joCard.put("operator", optNum);
				joCard.put("report_date", DateUtil.format(reportDate));
				joCard.put("shift_id", shiftId);
				joCard.put("pos_num", posNum);
				joCard.put("opt_num", optNum);
				joCard.put("chanel", chanel);
				joCard.put("card_code", Trading.getString("card_code"));
				joCard.put("old_bill_code", Trading.getString("third_bill_code"));
				joCard.put("third_code", Trading.getString("third_bill_code"));
				
				List<JSONObject> list = new ArrayList<JSONObject>();
				list.add(joCard);
				
				Data recharge = new Data();
				recharge.setTenancy_id(tenancyId);
				recharge.setStore_id(storeId);
				recharge.setSecret(secret);
				recharge.setType(Type.CUSTOMER_CARD_RECHARGE);
				recharge.setOper(Oper.update);
				recharge.setData(list);
				
				rechargeCancelPost(recharge, returnData);
			}
		}
		else
		{
			throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_BILL);
		}
		customerDao.updateCrmCardTradingPayState(tenancyId, storeId, billCode, 
				SysDictionary.THIRD_PAY_STATUS_REVOKED, 
				SysDictionary.REQUEST_STATUS_FAILURE);
	    returnData.setCode(Constant.CODE_SUCCESS);
	    returnData.setMsg(Constant.CUSTOMER_RECHARGE_CANCEL_SUCCESS);
	}
	
	@Override
	@Deprecated
	public void completeCardRecharge(Data param,Data returnData) throws Exception
	{
		// TODO Auto-generated method stub
		String tenancyId = param.getTenancy_id();
		int storeId = param.getStore_id();
		
		List<?> paramList = param.getData();
		if (paramList == null || paramList.isEmpty())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}
		
		JSONObject para = JSONObject.fromObject(paramList.get(0));
		String thirdBillCode = para.optString("bill_num");
		int shiftId = para.optInt("shift_id");
		String posNum = para.optString("pos_num");
		
		if (Tools.isNullOrEmpty(thirdBillCode))
		{
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_BILL_NUM);
		}

//		Timestamp currentTime = DateUtil.currentTimestamp();

		List<JSONObject> tradingList = customerDao.getCrmCardTradingListByThirdBillCode(tenancyId, storeId, thirdBillCode);
		
		JSONObject resultJson = new JSONObject(); 
		if(null !=tradingList && tradingList.size()>0)
		{
			JSONObject tradingJson = tradingList.get(0);
			
			String paymentState = tradingJson.optString("payment_state");
			String rechargeState = SysDictionary.REQUEST_STATUS_COMPLETE;
			String requestStatus = tradingJson.optString("request_status");
			String requestCode = tradingJson.optString("request_code");
			String requestMsg = tradingJson.optString("request_msg");
			
			customerDao.updateCrmCardTradingListForState(tenancyId, storeId, thirdBillCode, paymentState, rechargeState, requestStatus, requestCode, requestMsg);
			
			List<JSONObject> paymentList = customerDao.getCrmCardPaymentListByThirdBillCode(tenancyId, thirdBillCode);
			JSONObject payment = paymentList.get(0);
			
			String state = SysDictionary.THIRD_PAY_STATUS_FAIL;
			
			if(SysDictionary.THIRD_PAY_STATUS_SUCCESS.equals(paymentState)&&SysDictionary.REQUEST_STATUS_COMPLETE.equals(rechargeState))
			{
				state  = SysDictionary.THIRD_PAY_STATUS_SUCCESS;
			}
			else if(SysDictionary.THIRD_PAY_STATUS_PAYING.equals(paymentState)||SysDictionary.REQUEST_STATUS_ING.equals(rechargeState))
			{
				state  = SysDictionary.THIRD_PAY_STATUS_PAYING;
			}
			
			JSONObject resultPayment = new JSONObject();
			resultPayment.put("id", payment.optInt("id"));
			resultPayment.put("jzid", payment.optInt("payment_id"));
			resultPayment.put("amount", payment.optDouble("pay_money"));
			resultPayment.put("currency_amount", payment.optString("pay_money"));
			resultPayment.put("count", 1);
			resultPayment.put("name", "");
			resultPayment.put("number", "");
			resultPayment.put("phone", "");
			resultPayment.put("shift_id", shiftId);
			resultPayment.put("pos_num",posNum);
			resultPayment.put("cashier_num", "");
			resultPayment.put("last_updatetime", "");
			resultPayment.put("customer_id", "");
			resultPayment.put("bill_code", tradingJson.optString("bill_code"));
			resultPayment.put("remark", "");

			List<JSONObject> resultPaymentList = new ArrayList<JSONObject>();
			resultPaymentList.add(resultPayment);
			
			resultJson.put("bill_num", thirdBillCode);
			resultJson.put("table_code", "");
			resultJson.put("payment_amount", 0d);
			resultJson.put("discount_amount", 0d);
			
			resultJson.put("difference", 0d);
			resultJson.put("change", 0d);
			resultJson.put("payment_state",state);
			resultJson.put("paymentlist", resultPaymentList);

			List<JSONObject> resultList = new ArrayList<JSONObject>();
			resultList.add(resultJson);

			returnData.setData(resultList);
		}
		else
		{
			throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_BILL);
		}
	}
	
	@Override
	@Deprecated
	public void rechargeCancelPost(Data param, Data resData)
	{
		try
		{
			if (this.commonPostCheck(param))
			{
				JSONObject jsobj = JSONObject.fromObject(param);
				this.formatParam(param, jsobj);
				this.commonPost(jsobj.toString(), resData);

				if (resData.isSuccess())
				{
					String tenantId = resData.getTenancy_id();
					Integer storeId = resData.getStore_id();
					JSONObject resultObj = JSONObject.fromObject(resData.getData().get(0));

					Integer cardId = null;
					String cardCode = null;
					String billCode = null;
					String chanel = null;
					Date businessDate = null;
					double mainTrading = 0d;
					double rewardTrading = 0d;
					String operatType = null;
					double mainOriginal = 0d;
					double rewardOriginal = 0d;
					double deposit = 0d;
					String operator = null;
					Timestamp operateTime = null;
					double billMoney = 0d;
					String thirdBillCode = null;
					String billCodeOriginal = null;
					Integer activityId = null;
					Integer customerId = null;
					Integer shift_id = null;
					Integer empId = null;

					Integer paymentId = null;
					double payMoney = 0d;
					String payNo = null;
					double rexchangeRate = 0d;
					double localCurrency = 0d;

					JSONArray list = resultObj.optJSONArray("crm_card_trading_list");

					Integer cardClassId = resultObj.optInt("card_class_id");
					String name = resultObj.optString("name");
					String mobil = resultObj.optString("mobil");

					for (int i = 0; i < list.size(); i++)
					{
						JSONObject trading = list.getJSONObject(i);

						cardId = trading.optInt("card_id");
						cardCode = trading.optString("card_code");
						billCode = trading.optString("bill_code");
						chanel = trading.optString("chanel");
						businessDate = DateUtil.parseDate(trading.optString("business_date"));
						mainTrading = trading.optDouble("main_trading");
						rewardTrading = trading.optDouble("reward_trading");
						operatType = trading.optString("operat_type");
						mainOriginal = trading.optDouble("main_original");
						rewardOriginal = trading.optDouble("reward_original");
						deposit = trading.optDouble("deposit");
						operator = trading.optString("operator");
						operateTime = DateUtil.currentTimestamp();
						billMoney = trading.optDouble("bill_money");
						customerId = trading.optInt("customer_id");
						shift_id = trading.optInt("shift_id");
						thirdBillCode = trading.optString("third_bill_code");
						billCodeOriginal = trading.optString("bill_code_original");
						paymentId = trading.optInt("payment_id");
						payMoney = trading.optDouble("pay_money");
						payNo = trading.optString("pay_no");
						rexchangeRate = trading.optDouble("rexchange_rate");
						localCurrency = trading.optDouble("local_currency");
						empId = trading.optInt("operator_id");

						customerDao.insertCrmCardTradingList(tenantId, storeId, businessDate, shift_id, empId, operator, chanel, cardId, cardCode, cardClassId, name, mobil, deposit, customerId, billCode, operatType, billMoney, thirdBillCode, billCodeOriginal, null, activityId, mainTrading,
										rewardTrading, mainOriginal, rewardOriginal, null, operateTime, operateTime, operateTime, null, null, null, null, null, null, null, null, null, SysDictionary.REQUEST_STATUS_COMPLETE, SysDictionary.THIRD_PAY_STATUS_REFUND_SUCCESS,
										SysDictionary.REQUEST_STATUS_COMPLETE);

						customerDao.insertCrmCardPaymentList(tenantId, cardId, billCode, thirdBillCode, paymentId, rexchangeRate, payMoney, localCurrency, payNo, operateTime, operateTime);
					}

					JSONObject payWayJson = customerDao.getPaymentWayByID(tenantId, storeId, paymentId);
					String paymentClass = payWayJson.optString("payment_class");
					if (SysDictionary.PAYMENT_CLASS_WECHAT_PAY.equals(paymentClass) || SysDictionary.PAYMENT_DDM_QUICK_PAY.equals(paymentClass) || SysDictionary.PAYMENT_CLASS_ALI_PAY.equals(paymentClass))
					{
						JSONObject paramObj = JSONObject.fromObject(param.getData().get(0));
						String posNum = paramObj.optString("pos_num");
						String yBillCode = paramObj.optString("old_bill_code");
						
						JSONObject tradingJson = customerDao.getCrmCardTradingListByBillCode(tenantId, storeId, yBillCode);
						
						if(null !=tradingJson)
						{
							thirdBillCode = tradingJson.optString("third_bill_code");
						}

//						JSONObject paramJson = new JSONObject();
//						paramJson.put("order_no", thirdBillCode);
//						paramJson.put("service_type", SysDictionary.SERVICE_TYPE_RECHARGE);
//						paramJson.put("pos_num", posNum);
//						paramJson.put("opt_num", empId);
//						paramJson.put("amount", Math.abs(payMoney));
//						paramJson.put("pay_type", paymentClass);
//						paramJson.put("currency", "CNY");
//						paramJson.put("report_date", DateUtil.format(businessDate));
//						paramJson.put("shift", shift_id);
//						paramJson.put("channel", chanel);
						
//						Timestamp paymenTime = DateUtil.parseTimestamp(tradingJson.optString("store_updatetime"));
//						String aidOrderNum =  thirdBillCode+ "@" + String.valueOf(paymenTime.getTime());
//						
						JSONObject cancelParamJson = new JSONObject();
						cancelParamJson.put("order_no", thirdBillCode);
						cancelParamJson.put("order_num", thirdBillCode);
						cancelParamJson.put("settle_amount", tradingJson.optString("bill_money"));
						cancelParamJson.put("total_amount", tradingJson.optString("bill_money"));
						cancelParamJson.put("payment_id",  tradingJson.optInt("payment_id"));
						cancelParamJson.put("service_type", SysDictionary.SERVICE_TYPE_RECHARGE);
						cancelParamJson.put("channel",  chanel);
						cancelParamJson.put("report_date",  DateUtil.format(businessDate));
						cancelParamJson.put("shift_id",  shift_id);
						cancelParamJson.put("pos_num",  posNum);
						cancelParamJson.put("opt_num",  empId);
						cancelParamJson.put("currency_name",  "CNY");
						cancelParamJson.put("client_ip", "");
						cancelParamJson.put("refunds_order", tradingJson.optString(""));
						cancelParamJson.put("oper_type",  Type.CUSTOMER_CARD_RECHARGE.name());

						thirdPaymentService.cancelPayment(tenantId, storeId, cancelParamJson);
					}

					JSONObject invoiceJo = new JSONObject();
					invoiceJo.put("bill_code", billCodeOriginal);
					int invoiceRecord = customerDao.getCrmInvoice(tenantId, storeId, invoiceJo);
					if (invoiceRecord > 0) // 开具过电子发票
					{
						logger.info("取消电子发票开始");
						// 取消电子发票CANCLE_LECTRONIC_INVOICE
						String postUrl = getPequestUrl(INVOICE_POST_URL);

						JSONObject obj = new JSONObject();
						obj.put("DH", billCodeOriginal);
						List<JSONObject> jsonList = new ArrayList<JSONObject>();
						jsonList.add(obj);

						Data data = Data.get();
						data.setType(Type.CANCLE_LECTRONIC_INVOICE);
						data.setTenancy_id(tenantId);
						data.setStore_id(storeId);
						data.setData(jsonList);

						logger.info("取消电子发票  请求地址: " + postUrl + "      请求参数: " + JSONObject.fromObject(data).toString());
						String responseResult = HttpUtil.sendPostRequest(postUrl, JSONObject.fromObject(data).toString());
						if (responseResult != null)
						{
							JSONObject json = JSONObject.fromObject(responseResult);
							boolean success = json.optBoolean("success");
							if (!success)
							{
								logger.info("取消电子发票失败");
								throw SystemException.getInstance(PosErrorCode.UPDATE_PASSWORD_ERROR);
							}
						}
						logger.info("取消电子发票成功！");
					}
				}
			}
		}
		catch (SystemException se)
		{
			throw se;
		}
		catch (Exception e)
		{
			e.printStackTrace();
			logger.info("Exception:" + ExceptionMessage.getExceptionMessage(e));
			throw SystemException.getInstance(PosErrorCode.OPER_ERROR);
		}
	}
	
	@Override
	@Deprecated
	public void customerConsumePost(String param, Data resData)
	{
		this.commonPost(param, resData);

		if (resData.isSuccess())
		{
			// 插入卡片交易流水表
			StringBuilder sqlTrading = new StringBuilder(
					"insert into crm_card_trading_list (tenancy_id,card_id,card_code,bill_code,chanel,store_id,business_date,main_trading,reward_trading,operat_type,main_original,reward_original,deposit,operator,operate_time,bill_money,third_bill_code,bill_code_original,activity_id,customer_id,card_class_id,name,mobil,shift_id,operator_id,pos_num) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");

			String tenantId = resData.getTenancy_id();
			Integer storeId = resData.getStore_id();
			Oper oper = resData.getOper();
			JSONObject resultObj = JSONObject.fromObject(resData.getData().get(0));

			Integer cardId = null;
			String cardCode = null;
			String billCode = null;
			String chanel = null;
			String posNum = null;
			Date businessDate = null;
			double mainTrading = 0d;
			double rewardTrading = 0d;
			String operatType = null;
			double mainOriginal = 0d;
			double rewardOriginal = 0d;
			double deposit = 0d;
			String operator = null;
			Timestamp operateTime = null;
			double billMoney = 0d;
			String thirdBillCode = null;
			String billCodeOriginal = null;
			Integer activityId = null;
			Integer customerId = null;
			Integer shift_id = null;
			Integer empId = null;

			Object[] objs = null;
			JSONArray list = resultObj.optJSONArray("crm_card_trading_list");
			
			Integer cardClassId = resultObj.optInt("card_class_id");
			String name = resultObj.optString("name");
			String mobil = resultObj.optString("mobil");

			for (int i = 0; i < list.size(); i++)
			{
				JSONObject trading = list.getJSONObject(i);

				storeId = trading.optInt("store_id");
				tenantId = trading.optString("tenancy_id");
				
				cardId = trading.optInt("card_id");
				cardCode = trading.optString("card_code");
				billCode = trading.optString("bill_code");
				chanel = trading.optString("chanel");
				businessDate = DateUtil.parseDate(trading.optString("business_date"));
				mainTrading = trading.optDouble("main_trading");
				rewardTrading = trading.optDouble("reward_trading");
				operatType = trading.optString("operat_type");
				mainOriginal = trading.optDouble("main_original");
				rewardOriginal = trading.optDouble("reward_original");
				deposit = trading.optDouble("deposit");
				operator = trading.optString("operator");
				operateTime = DateUtil.currentTimestamp();
				billMoney = trading.optDouble("bill_money");
				customerId = trading.optInt("customer_id");
				shift_id = trading.optInt("shift_id");
				thirdBillCode = trading.optString("third_bill_code");
				posNum = trading.optString("pos_num");

				if (Oper.update.equals(oper))
				{
					billCodeOriginal = trading.optString("bill_code_original");
				}
				empId = trading.optInt("operator_id");

				objs = new Object[]
				{ tenantId, cardId, cardCode, billCode, chanel, storeId, businessDate, mainTrading, rewardTrading, operatType, mainOriginal, rewardOriginal, deposit, operator, operateTime, billMoney, thirdBillCode, billCodeOriginal, activityId, customerId, cardClassId, name, mobil, shift_id, empId,posNum };
				try
				{
					customerDao.update(sqlTrading.toString(), objs);
				}
				catch (Exception e)
				{
					e.printStackTrace();
					throw SystemException.getInstance(PosErrorCode.OPER_ERROR);
				}
			}
		}
	}
	
	@Override
	@Deprecated
	public void cancelCardConsume(Data param, Data resData,String isCheck) throws Exception
	{
		String tenantIdReq = param.getTenancy_id();
		Integer organIdReq = param.getStore_id();

		Map<String, Object> map = ReqDataUtil.getDataMap(param);
		String oldBillCode = ParamUtil.getStringValue(map, "old_bill_code", true, PosErrorCode.NOT_NULL_OLD_BILL_CODE);
		Date reportDate = ParamUtil.getDateValue(map, "business_date", true, PosErrorCode.NOT_NULL_REPORT_DATE);
		Integer shiftId = ParamUtil.getIntegerValue(map, "shift_id", true, PosErrorCode.NOT_NULL_SHIFT_ID);
		String cardCodeReq = ParamUtil.getStringValue(map, "card_code", true, PosErrorCode.NOT_NULL_CARD_CODE);
		String posNum = ParamUtil.getStringValue(map, "pos_num", true, PosErrorCode.NOT_NULL_CARD_CODE);
		
		if("Y".equals(isCheck))
		{
			//账单消费不允许在会员操作界面进行撤销
			StringBuilder queryBillCodeSql = new StringBuilder("select count(1) from pos_bill_payment where tenancy_id=? and store_id=? and report_date=? and shift_id=? and number=? and bill_code=? ");
			int totalCount = customerDao.queryForInt(queryBillCodeSql.toString(), new Object[]
					{ tenantIdReq, organIdReq, reportDate, shiftId, cardCodeReq, oldBillCode });
			
			if (totalCount > 0)
			{
				throw new SystemException(PosErrorCode.HAS_PAYMENTED_CAN_NOT_CANCEL);
			}
		}
		this.commonPost(JSONObject.fromObject(param).toString(), resData);

		if (resData.isSuccess())
		{
			// 插入卡片交易流水表
			StringBuilder sqlTrading = new StringBuilder(
					"insert into crm_card_trading_list (tenancy_id,card_id,card_code,bill_code,chanel,store_id,business_date,main_trading,reward_trading,operat_type,main_original,reward_original,deposit,operator,operate_time,bill_money,third_bill_code,bill_code_original,activity_id,customer_id,card_class_id,name,mobil,shift_id,operator_id,pos_num) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");

			String tenantId = resData.getTenancy_id();
			Integer storeId = resData.getStore_id();
			Oper oper = resData.getOper();
			JSONObject resultObj = JSONObject.fromObject(resData.getData().get(0));

			Integer cardId = null;
			String cardCode = null;
			String billCode = null;
			String chanel = null;
			Date businessDate = null;
			double mainTrading = 0d;
			double rewardTrading = 0d;
			String operatType = null;
			double mainOriginal = 0d;
			double rewardOriginal = 0d;
			double deposit = 0d;
			String operator = null;
			Timestamp operateTime = null;
			double billMoney = 0d;
			String thirdBillCode = null;
			String billCodeOriginal = null;
			Integer activityId = null;
			Integer customerId = null;
			Integer shift_id = null;
			Integer empId = null;

			Object[] objs = null;
			JSONArray list = resultObj.optJSONArray("crm_card_trading_list");
			
			Integer cardClassId = resultObj.optInt("card_class_id");
			String name = resultObj.optString("name");
			String mobil = resultObj.optString("mobil");

			for (int i = 0; i < list.size(); i++)
			{
				JSONObject trading = list.getJSONObject(i);

				cardId = trading.optInt("card_id");
				cardCode = trading.optString("card_code");
				billCode = trading.optString("bill_code");
				chanel = trading.optString("chanel");
				businessDate = DateUtil.parseDate(trading.optString("business_date"));
				mainTrading = trading.optDouble("main_trading");
				rewardTrading = trading.optDouble("reward_trading");
				operatType = trading.optString("operat_type");
				mainOriginal = trading.optDouble("main_original");
				rewardOriginal = trading.optDouble("reward_original");
				deposit = trading.optDouble("deposit");
				operator = trading.optString("operator");
				operateTime = DateUtil.currentTimestamp();
				billMoney = trading.optDouble("bill_money");
				customerId = trading.optInt("customer_id");
				thirdBillCode = trading.optString("third_bill_code");
				shift_id = trading.optInt("shift_id");

				if (Oper.update.equals(oper))
				{
					billCodeOriginal = trading.optString("bill_code_original");
				}
				empId = trading.optInt("operator_id");

				objs = new Object[]
				{ tenantId, cardId, cardCode, billCode, chanel, storeId, businessDate, mainTrading, rewardTrading, operatType, mainOriginal, rewardOriginal, deposit, operator, operateTime, billMoney, thirdBillCode, billCodeOriginal, activityId, customerId, cardClassId, name, mobil, shift_id, empId,posNum };
				customerDao.update(sqlTrading.toString(), objs);
			}
		}
	}
	
	@Override
	@Deprecated
	public void deleteVipPrice(Data param, Data result) throws SystemException
	{
		try
		{
			String tenantId = param.getTenancy_id();
			Integer organId = param.getStore_id();

			Map<String, Object> map = ReqDataUtil.getDataMap(param);

			String billNum = ParamUtil.getStringValue(map, "bill_num", true, PosErrorCode.NOT_NULL_BILL_NUM);
			// Date reportDate = ParamUtil.getDateValue(map, "report_date",
			// false,
			// null);
			// String cardCode = ParamUtil.getStringValue(map, "card_code",
			// false,
			// null);

			StringBuilder qcount = new StringBuilder("select count(id) from pos_bill where bill_num=? and store_id=? and tenancy_id=? ");
			int count = customerDao.queryForInt(qcount.toString(), new Object[]
			{ billNum, organId, tenantId });

			if (count == 0)
			{
				throw new SystemException(PosErrorCode.NOT_EXISTS_BILL);
			}

			qcount.append(" and bill_property='CLOSED'");
			int secount = customerDao.queryForInt(qcount.toString(), new Object[]
			{ billNum, organId, tenantId });
			if (secount > 0)
			{
				throw new SystemException(PosErrorCode.BILL_CLOSED);
			}
			else
			{
				String uItem = new String("update pos_bill_item set third_price=null,discount_mode_id=null where bill_num=? and store_id=? and tenancy_id=?");

				customerDao.update(uItem, new Object[]
				{ billNum, organId, tenantId });

				String upb = new String("update pos_bill set discount_mode_id=null where bill_num=? and store_id=?");
				customerDao.update(upb, new Object[]
				{ billNum, organId });
			}

			result.setCode(Constant.CODE_SUCCESS);
			result.setMsg(Constant.DELETE_VIP_PRICE_SUCCESS);
		}
		catch (SystemException se)
		{
			throw se;
		}
		catch (Exception e)
		{
			e.printStackTrace();
			logger.info("购买会籍：" + ExceptionMessage.getExceptionMessage(e));
			throw new SystemException(PosErrorCode.OPER_ERROR);
		}
	}

	@Override
	@Deprecated
	public void customerVipPrice(JSONObject param, Data result) throws SystemException
	{
		String tenantId = param.optString("tenancy_id");
		Integer organId = param.optInt("store_id");
		try
		{
			//JSONArray arr = param.optJSONArray("data");
			JSONObject para = param.optJSONArray("data").getJSONObject(0);

//			Date reportDate = DateUtil.parseDate(ParamUtil.getDateStringValue(para,"report_date"));// 报表日期
//			String card_code = para.optString("card_code");// 会员卡号
			String mobil = para.optString("mobil");// 手机号
			String billno = para.optString("bill_num"); // 账单编号
			if (StringUtils.isEmpty(mobil)) 
			{
				throw new SystemException(PosErrorCode.BILL_MEMBERCARD_NULL);
			}

			String reqURL = com.tzx.framework.common.constant.Constant.systemMap.get("crmUrl");

			String resResult = HttpUtil.sendPostRequest(reqURL, param.toString());

			logger.info("调用接口返回体：" + resResult);

			if (StringUtils.isEmpty(resResult))
			{
				result.setCode(5);
				result.setMsg("查询会员价连接超时，请检查网络");
				result.setSuccess(false);
				return;
			}
			else
			{
				ObjectMapper objectMapper = new ObjectMapper();
				try
				{
					Data data = objectMapper.readValue(resResult, Data.class);
					result.setCode(data.getCode());
					if (data.getCode() == 99)
					{
						result.setCode(5);
						result.setMsg("查询会员价连接异常");
						return;
					}

					if (data.getCode() == 0)
					{
						if (Tools.hv(billno))
						{
							String upbi = new String("update pos_bill_item set third_price=?,discount_mode_id=6 where item_num=? and item_unit_id=? and bill_num=? and store_id=?");

							String upb = new String("update pos_bill set discount_mode_id=6 where bill_num=? and store_id=?");

							customerDao.update(upb, new Object[]
							{ billno, organId });
							// GJ20160406 账单会员库
//							customerDao.operPosBillMember(tenantId, organId, reportDate, billno, SysDictionary.BILL_MEMBERCARD_ZK02, card_code, mobil);

							HashMap<String, Object> map = (HashMap<String, Object>) ReqDataUtil.getDataMap(data);
							@SuppressWarnings("unchecked")
							List<HashMap<String, Object>> list = (List<HashMap<String, Object>>) map.get("itemlist");
							if (list.size() > 0)
							{
								for (int i = 0; i < list.size(); i++)
								{
									HashMap<String, Object> obj = list.get(i);
									String item_code = (String) obj.get("item_code");
									Double vip_price = (Double) obj.get("vip_price");
									Integer unit_id = (Integer) obj.get("unit_id");
									if (Tools.isNullOrEmpty(vip_price) == false)
									{
										customerDao.update(upbi, new Object[]
										{ vip_price, item_code, unit_id, billno, organId });
									}
								}
							}
							customerDao.calcAmount(tenantId, organId, billno);
						}

						result.setData(data.getData());
						result.setMsg(Constant.VIP_PRICE_SUCCESS);
						result.setCode(data.getCode());
						return;
					}
					else
					{
						result.setMsg(data.getMsg());
						result.setCode(data.getCode());
						return;
					}
				}
				catch (Exception se)
				{
					logger.info("转查会员价信息错误：" + ExceptionMessage.getExceptionMessage(se));
					result.setCode(5);
					result.setMsg(Constant.VIP_PRICE_FAILURE);
					result.setSuccess(false);
					se.printStackTrace();
					return;
				}
			}
		}
		catch (Exception se)
		{
			result.setCode(5);
			result.setMsg("查询会员价连接超时，请检查网络");
			result.setSuccess(false);
			return;
		}
	}
	
	/**
	 * 账单菜品详细查询
	 */
	@Override
	@Deprecated
	public void verifyCoupon(Data pData, Data resData) throws SystemException
	{
		String tenantId = pData.getTenancy_id();//jobj.optString("tenancy_id");
		int storeId = pData.getStore_id();//jobj.optInt("store_id");
		try{
//			ObjectMapper objectMapper = new ObjectMapper();
//			Data pData = null;
//			try
//			{
//				pData = objectMapper.readValue(jobj.toString(), Data.class);
//			}
//			catch (Exception se)
//			{
//				se.printStackTrace();
//				logger.error("Fastjson 类型解析错误" + ExceptionMessage.getExceptionMessage(se));
//				resData.setCode(Constant.CODE_PARAM_FAILURE);
//				resData.setMsg("json转data类型错误，请查看传入参数");
//				return;
//			}
			JSONObject dataJson = JSONObject.fromObject(pData.getData().get(0));
			
			String reportDate = ParamUtil.getDateStringValue(dataJson, "report_date");
			String optNum = dataJson.optString("opt_num");
			String optName = customerDao.getEmpNameById(optNum, tenantId, storeId);
			
			String billNum = dataJson.optString("bill_code");
			if (Tools.isNullOrEmpty(billNum))
			{
				throw new SystemException(PosErrorCode.NOT_NULL_BILL_NUM);
			}
			
			String batchNum = posDishDao.getBatchNum(tenantId, storeId, billNum);
			
			JSONArray coupons = dataJson.getJSONArray("couponslist");
			Data cloneData = pData.clone();
			JSONObject cloneJo = JSONObject.fromObject(cloneData.getData().get(0));
			JSONArray oldCoupons = cloneJo.getJSONArray("couponslist");
			
			List<JSONObject> couponCodeList = posDishDao.getCouponCode(tenantId, storeId, billNum, batchNum);
			if(couponCodeList.size() > 0)
			{
				for(JSONObject couponCode : couponCodeList)
				{
					JSONObject jo = new JSONObject();
					jo.put("coupons_code", couponCode.optString("number"));
					coupons.add(jo);
				}
				dataJson.put("couponslist", coupons);
			}
			
			List<JSONObject> billDetailsList = posDishDao.getBillDetails4Coupon(tenantId, storeId, billNum, batchNum);
			
			double disMoney = 0;
			if(billDetailsList.size() > 0)
			{
				disMoney = billDetailsList.get(0).optDouble("dismoney");
			}
			
			dataJson.put("billdetails", billDetailsList);
			dataJson.put("discount_amount", disMoney);
			dataJson.put("batch_num", batchNum);
			dataJson.put("business_date", reportDate);
			dataJson.put("operator", optName);
			dataJson.put("operator_id", optNum);
			dataJson.put("updatetime", DateUtil.getNowDateYYDDMMHHMMSS());
			
			List<JSONObject> dataList = new ArrayList<JSONObject>();
			dataList.add(dataJson);
			
			pData.setData(dataList);
			
			//券验证
			Data cData = new Data();
			String cParam = JSONObject.fromObject(pData).toString();
			this.commonPost(cParam, cData);
			
			if(cData.getCode() != 0)
			{
				resData.setCode(cData.getCode());
				resData.setMsg(cData.getMsg());
				return;
			}
			Map<String, Object> rsMap = ReqDataUtil.getDataMap(cData);
			
			@SuppressWarnings("unchecked")
			List<Map<String, Object>> couponsdetails = (List<Map<String, Object>>) rsMap.get("couponsdetails"); //
			if (couponsdetails.size() == 0)
			{
				throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
			}
			double dbFaceVal = 0;
			List<JSONObject> couponsList = new ArrayList<JSONObject>();
			for(int i = 0; i < couponsdetails.size(); i++)
			{
				Map<String, Object> cp = couponsdetails.get(i);
				String couponsCode = cp.get("coupons_code").toString();
				String couponsPro = cp.get("coupons_pro").toString();
				String faceValue = cp.get("face_value").toString();
				String itemUnitId = cp.get("item_unit_id").toString();
				String discount = cp.get("discount").toString();
				
				String checkCouponsCode = null;
				for(int j = 0; j < oldCoupons.size(); j++)
				{
					checkCouponsCode = ((JSONObject)oldCoupons.get(j)).optString("coupons_code");
					if (checkCouponsCode.equals(couponsCode))
					{
						JSONObject couponsJson = new JSONObject();
						if("coupons_dish".equals(couponsPro))
						{
							//菜品券
							couponsJson.put("amount", discount);//抵扣金额
							couponsJson.put("currency_amount", discount);
							dbFaceVal += Double.parseDouble(discount);
						}
						else
						{
							//优惠券
							couponsJson.put("amount", faceValue);//面值
							couponsJson.put("currency_amount", faceValue);
							dbFaceVal += Double.parseDouble(faceValue);
						}
						couponsJson.put("number", couponsCode);//券号
						couponsJson.put("item_unit_id ", itemUnitId);//菜品规格
						couponsJson.put("remark", couponsPro);//券类型
						couponsJson.put("count", "1");//券数量
						couponsList.add(couponsJson);
						
						break;
					}
				}
			}
			
			Map<String, Object> rstMap = new HashMap<String, Object>();
			rstMap.put("face_value", dbFaceVal);
			rstMap.put("couponslist", couponsList);
			
			List<Object> rstList = new ArrayList<Object>();
			rstList.add(rstMap);
			
			resData.setData(rstList);
			resData.setCode(Constant.CODE_SUCCESS);
			resData.setMsg(Constant.QUERY_ITEM_DETAILS_SUCCESS);
		}
		catch(SystemException se)
		{
			throw se;
		}
		catch(Exception e)
		{
			e.printStackTrace();
			resData.setCode(Constant.CODE_NULL_DATASET);
			resData.setMsg(Constant.QUERY_ITEM_DETAILS_FAILURE);
		}
	}

	@Override
	public void buyVipLevel(Data param, Data result) throws Exception
	{
		// TODO Auto-generated method stub
		String tenancyId = param.getTenancy_id();
		int storeId = param.getStore_id();

		List<?> paramList = param.getData();
		if (paramList == null || paramList.isEmpty())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}
		JSONObject para = JSONObject.fromObject(paramList.get(0));

		String report_Date = ParamUtil.getDateStringValue(para, "report_date");
		Date reportDate = DateUtil.parseDate(report_Date);
		if (Tools.isNullOrEmpty(reportDate))
		{
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_REPORT_DATE);
		}
		int shiftId = para.optInt("shift_id");
//		String posNum = para.optString("pos_num");
		String optNum = para.optString("opt_num");
		String optName = customerDao.getEmpNameById(optNum, tenancyId, storeId);
		String chanel = para.optString("chanel");
		String mobil = para.optString("mobil");
		int level = para.optInt("level");
		int payId = para.optInt("pay_id");
		double payMoney = para.optDouble("pay_money");
		String payNo = para.optString("pay_no");
		int activityId = para.optInt("activity_id");
		
		Timestamp currentTime = DateUtil.currentTimestamp();

		if("Y".equals(para.optString("ischeck")) && para.containsKey("pos_num"))
		{
			String posNum = para.optString("pos_num");
			customerDao.checkKssy(reportDate, optNum, storeId,shiftId,posNum);
		}
		
		//
		StringBuilder findPaymentWaySql = new StringBuilder("select count(p.payment_class) from payment_way p inner join payment_way_of_ogran o on p.id = o.payment_id and p.status='1' where o.tenancy_id = ? and o.organ_id = ? and o.payment_id = ?");
		int payCount =  customerDao.queryForInt(findPaymentWaySql.toString(), new Object[]
		{ tenancyId, storeId, payId });
		
		if (0 > payCount)
		{
			throw SystemException.getInstance(PosErrorCode.NOT_EXIST_JZID);
		}
		
//		SqlRowSet rs = customerDao.query4SqlRowSet(findPaymentWaySql.toString(), new Object[]
//		{ tenancyId, storeId, payId });
//		String paymentClass = "";
//		if (rs.next())
//		{
//			paymentClass = rs.getString("payment_class");
//		}
//		else
//		{
//			throw SystemException.getInstance(PosErrorCode.NOT_EXIST_JZID);
//		}
		
		//
		String thirdCode =JavaMd5.toMd5B16("BUY_VIPLEVEL"+optNum+mobil+level+payId+currentTime.getTime());
		
		//
		/*StringBuilder insertSql = new StringBuilder("insert into crm_activity_payvip_list(tenancy_id,store_id,order_num,third_code,chanel,activity_id,customer_id,customer_name,mobil,old_level,new_level,request_time,business_date,pay_id,pay_money,pay_no,pay_state,pay_time,remark,last_updatetime,last_operator,request_status) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
		customerDao.update(insertSql.toString(), new Object[]{tenancyId,storeId,null,thirdCode,chanel,activityId,null,null,mobil,null,level,currentTime,reportDate,payId,payMoney,payNo,"1",currentTime,null,currentTime,optNum,SysDictionary.REQUEST_STATUS_ING});
		
		StringBuilder seelctIdSql = new StringBuilder("select currval('crm_activity_payvip_list_id_seq'::regclass) ");
		Integer listId = customerDao.queryForInt(seelctIdSql.toString(), new Object[] {});*/
		
		JSONObject requestJson = new JSONObject();
		requestJson.put("mobil", mobil);
		requestJson.put("level", level);
		requestJson.put("chanel", chanel);
		requestJson.put("payment", payId);
		requestJson.put("bill_code", thirdCode);
		requestJson.put("pay_money", payMoney);
		requestJson.put("pay_no", payNo);
		requestJson.put("business_date", report_Date);
		requestJson.put("shift_id", shiftId);
		requestJson.put("operator_id", optNum);
		requestJson.put("operator", optName);
		requestJson.put("updatetime", DateUtil.getNowDateYYDDMMHHMMSS());
		
		List<JSONObject> requestList = new ArrayList<JSONObject>();
		requestList.add(requestJson);
		
		Data requestData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
		requestData.setType(Type.BUY_VIPLEVEL);
		requestData.setOper(Oper.add);
		requestData.setData(requestList);
		
		Data responseData = new Data();
		this.commonPost(JSONObject.fromObject(requestData).toString(), responseData);
		
		//StringBuilder updateSql = new StringBuilder("update crm_activity_payvip_list set request_status=? where tenancy_id=? and store_id=? and id=?");
		if(responseData.isSuccess())
		{
			JSONObject data = JSONObject.fromObject(responseData.getData().get(0));
			JSONObject payvipList = data.getJSONArray("crm_activity_payvip_list").getJSONObject(0);
			String orderNum = payvipList.optString("order_num");
			String customerName = payvipList.optString("customer_name");
			int customerId = payvipList.optInt("customer_id");
			int oldLevel = payvipList.optInt("old_level");
			int newlevel = payvipList.optInt("new_level");
			activityId = payvipList.optInt("activity_id");
			//customerDao.update(updateSql.toString(), new Object[]{SysDictionary.REQUEST_STATUS_COMPLETE,tenancyId,storeId,listId});
			StringBuilder insertSql = new StringBuilder("insert into crm_activity_payvip_list(tenancy_id,store_id,order_num,third_code,chanel,activity_id,customer_id,customer_name,mobil,old_level,new_level,request_time,business_date,pay_id,pay_money,pay_no,pay_state,pay_time,remark,last_updatetime,last_operator,request_status) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
			customerDao.update(insertSql.toString(), new Object[]{tenancyId,storeId,orderNum,thirdCode,chanel,
				activityId,customerId,customerName,mobil,oldLevel,newlevel,currentTime,reportDate,payId,payMoney,payNo,"1",
				currentTime,null,currentTime,optNum,SysDictionary.REQUEST_STATUS_COMPLETE});
			
			//----会籍购买打印
			try {
				JSONObject findPrintParam = customerDao.findPrintParam(tenancyId,storeId,payId);
				JSONObject printJo = new JSONObject();
				printJo.put("customer_name", customerName);
				printJo.put("mobil", mobil);
				printJo.put("level", data.optString("level_name"));
				printJo.put("pay_money", payMoney);
				printJo.put("pay_way", findPrintParam.optString("payment_name1"));
				printJo.put("organ_name", findPrintParam.optString("organ_name"));
				printJo.put("opterator", optName);
				printJo.put("last_updatetime", DateUtil.getNowDateYYDDMM());
				
				posPrintNewService.posPrintByMode(tenancyId,storeId,SysDictionary.PRINT_CODE_1016,printJo);
			} catch (Exception e) {
				// TODO: handle exception
				e.printStackTrace();
				logger.info("购买会籍打印错误");
			}
			//------
			
			result.setCode(responseData.getCode());
			result.setMsg(responseData.getMsg());
		}
		else
		{
			if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
			{
				result.setCode(Constant.CODE_CONN_EXCEPTION);
				result.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
			}
			else
			{
				//customerDao.update(updateSql.toString(), new Object[]{SysDictionary.REQUEST_STATUS_FAILURE,tenancyId,storeId,listId});
				throw SystemException.getInstance(PosErrorCode.BUY_VIP_LEVEL_ERROR).set("{0}", responseData.getMsg());
			}
		}
	}

	@Override
	public void deleteBuyVipLevel(Data param, Data result) throws Exception
	{
		// TODO Auto-generated method stub
		String tenancyId = param.getTenancy_id();
		int storeId = param.getStore_id();

		List<?> paramList = param.getData();
		if (paramList == null || paramList.isEmpty())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}
		JSONObject para = JSONObject.fromObject(paramList.get(0));

		String report_Date = ParamUtil.getDateStringValue(para, "report_date");
		Date reportDate = DateUtil.parseDate(report_Date);
		if (Tools.isNullOrEmpty(reportDate))
		{
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_REPORT_DATE);
		}
		String optNum = para.optString("opt_num");
		String posNum = para.optString("pos_num");
		String optName = customerDao.getEmpNameById(optNum, tenancyId, storeId);
		String chanel = para.optString("chanel");
		String mobil = para.optString("mobil");
		
		String billCode = para.optString("bill_code");
		
		int shiftId = para.optInt("shift_id");
//		int level = para.optInt("level");
//		int payId = para.optInt("pay_id");
//		double payMoney = para.optDouble("pay_money");
//		String payNo = para.optString("pay_no");
//		int activityId = para.optInt("activity_id");
		
		Timestamp currentTime = DateUtil.currentTimestamp();

		if("Y".equals(para.optString("ischeck")))
		{
			customerDao.checkKssy(reportDate, optNum, storeId,shiftId,posNum);
		}
		
		//
		/*StringBuilder querySql = new StringBuilder("select * from crm_activity_payvip_list where tenancy_id=? and store_id=? and mobil=?");
		SqlRowSet rs = customerDao.query4SqlRowSet(querySql.toString(), new Object[]{tenancyId,storeId,mobil});
		
//		String thirdCode = null;
		int listId = 0;
		if(rs.next())
		{
//			thirdCode = rs.getString("third_code");
			listId = rs.getInt("id");
		}*/
		
	/*	StringBuilder updateSql = new StringBuilder("update crm_activity_payvip_list set pay_state=?,request_status=? where tenancy_id=? and store_id=? and id=?");
		customerDao.update(updateSql.toString(), new Object[]{"-1",SysDictionary.REQUEST_STATUS_ING,tenancyId,storeId,listId});*/
		
		JSONObject requestJson = new JSONObject();
		requestJson.put("mobil", mobil);
		requestJson.put("chanel", chanel);
		requestJson.put("bill_code", billCode);
		requestJson.put("business_date", report_Date);
		requestJson.put("shift_id", shiftId);
		requestJson.put("operator", optName);
		requestJson.put("operator_id", optNum);
		requestJson.put("updatetime", DateUtil.getNowDateYYDDMMHHMMSS());
		
		List<JSONObject> requestList = new ArrayList<JSONObject>();
		requestList.add(requestJson);
		
		Data requestData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
		requestData.setType(Type.BUY_VIPLEVEL);
		requestData.setOper(Oper.delete);
		requestData.setData(requestList);
		
		Data responseData = new Data();
		this.commonPost(JSONObject.fromObject(requestData).toString(), responseData);
//		
		//StringBuilder updateStatusSql = new StringBuilder("update crm_activity_payvip_list set request_status=? where tenancy_id=? and store_id=? and id=?");
		if(responseData.isSuccess())
		{
			//customerDao.update(updateStatusSql.toString(), new Object[]{SysDictionary.REQUEST_STATUS_COMPLETE,tenancyId,storeId,listId});
			JSONObject data = JSONObject.fromObject(responseData.getData().get(0));
			JSONObject payvipList = data.getJSONArray("crm_activity_payvip_list").getJSONObject(0);
			String orderNum = payvipList.optString("order_num");
			String thirdCode = payvipList.optString("third_code");
			int activityId = payvipList.optInt("activity_id");
			String customerName = payvipList.optString("customer_name");
			int customerId = payvipList.optInt("customer_id");
			int oldLevel = payvipList.optInt("old_level");
			int newlevel = payvipList.optInt("new_level");
			int payId = payvipList.optInt("pay_id");
			double payMoney = 0d - payvipList.optDouble("pay_money");
			String payNo = payvipList.optString("pay_no");
			Timestamp requestTime = DateUtil.formatTimestamp(payvipList.optString("request_time"));
			
			StringBuilder insertSql = new StringBuilder("insert into crm_activity_payvip_list(tenancy_id,store_id,order_num,third_code,chanel,activity_id,customer_id,");
			insertSql.append("customer_name,mobil,old_level,new_level,request_time,business_date,pay_id,pay_money,pay_no,pay_state,pay_time,remark,last_updatetime,last_operator,request_status) " );
			insertSql.append("values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
			customerDao.update(insertSql.toString(), new Object[] { tenancyId,
					storeId, orderNum, thirdCode, chanel, activityId,
					customerId, customerName, mobil, oldLevel, newlevel,
					requestTime, reportDate, payId, payMoney, payNo, "-1",
					currentTime, null, currentTime, optNum,
					SysDictionary.REQUEST_STATUS_COMPLETE });
			result.setCode(responseData.getCode());
			result.setMsg(responseData.getMsg());
		}
		else
		{
			if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
			{
				result.setCode(Constant.CODE_CONN_EXCEPTION);
				result.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
			}
			else
			{
				throw SystemException.getInstance(PosErrorCode.DELETE_BUY_VIP_LEVEL_ERROR).set("{0}", responseData.getMsg());
			}
		}
	}
	
	@Override
	public void customerCardConsumePrint(String tenantId, int storeId,String posNum,JSONObject para,String printCode) throws Exception
	{
		try
		{
			if (posPrintNewService.isNONewPrint(tenantId, storeId))
			{// 如果启用新的打印模式
			    para.put("pos_num", posNum);
				posPrintNewService.posPrintByMode(tenantId, storeId, printCode, para);
			}
			else
			{
				double main_balance = para.optDouble("main_balance");
				double reward_balance = para.optDouble("reward_balance");

				JSONObject param = new JSONObject();
				param.put("bill_code", para.optString("bill_code"));
				param.put("card_code", para.optString("card_code"));
				param.put("card_class_name", para.optString("card_class_name"));
				param.put("name", para.optString("name"));
				param.put("mobil", para.optString("mobil"));
				param.put("credit", para.optDouble("credit"));
				param.put("useful_credit", para.optDouble("useful_credit"));
				param.put("main_balance", main_balance);
				param.put("reward_balance", reward_balance);
				param.put("total_balance", DoubleHelper.add(main_balance, reward_balance, 4));
				param.put("total_main", para.optDouble("total_main"));
				param.put("total_reward", para.optDouble("total_reward"));
				param.put("consume_cardmoney", para.optDouble("consume_cardmoney"));
				param.put("operator", para.optString("operator"));
				param.put("updatetime", para.optString("updatetime"));
				param.put("main_trading", para.optDouble("main_trading"));
				param.put("reward_trading", para.optDouble("reward_trading"));
				param.put("payment_name1", "");
				param.put("income", 0d);
				param.put("deposit", 0d);
				param.put("sales_price", 0d);

				String printCountStr = customerDao.getSysParameter(tenantId, storeId, "MemberReceiptCount");
				int printCount = 1;
				if (Tools.hv(printCountStr))
				{
					printCount = Integer.parseInt(printCountStr);
				}

				customerDao.customerPrint(tenantId, storeId, posNum, printCode, "1", param, printCount);
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
			logger.error("会员消费小票打印失败:", e);
		}
	}
	
	@Override
	public List<JSONObject> getCardRechargeRecord(String tenancyId, int storeId, List<?> param, Pagination pagination) throws Exception
	{
		if (param == null || param.isEmpty())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}
		JSONObject para = JSONObject.fromObject(param.get(0));
		String cardCode = para.optString("card_code");
		String thirdCode = para.optString("third_code");
		if (Tools.isNullOrEmpty(cardCode) && Tools.isNullOrEmpty(thirdCode))
		{
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_CARD_CODE);
		}
		
		Data paramData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
		paramData.setType(Type.CUSTOMER_CARD_RECHARGE);
		paramData.setOper(Oper.find);
		paramData.setData(param);
		
		
		Data resData = new Data();
		this.commonPost(JSONObject.fromObject(paramData).toString(), resData);
		
		JSONObject resultObj = JSONObject.fromObject(resData.getData().get(0));
		JSONArray list = resultObj.optJSONArray("crm_card_payment_list");
		
		JSONObject cardJson = new JSONObject();
		cardJson.put("card_code", cardCode);
		cardJson.put("item", list);

		List<JSONObject> resultList = new ArrayList<JSONObject>();
		resultList.add(cardJson);
		return resultList;
	}

	@Override
	public void dishSoldOut(Data param, List<?> jsParam) throws Exception {
		// TODO Auto-generated method stub
		String tenantId = param.getTenancy_id();
		Integer organId = param.getStore_id();

		JSONObject orgJson = customerDao.getOrganById(tenantId, organId);
//		String isWechatdddc = (null != orgJson && orgJson.containsKey("is_wechatdddc")) ? orgJson.optString("is_wechatdddc") : "0";
//		String isWechatwmzt = (null != orgJson && orgJson.containsKey("is_wechatwmzt")) ? orgJson.optString("is_wechatwmzt") : "0";
//		if (false == "1".equals(isWechatwmzt) && false == "1".equals(isWechatdddc))
//		{
//			//未开启微信点餐,不上传估清
//			return;
//		}
		
		Map<String, Object> map = ReqDataUtil.getDataMap(param);
		
		Date reportDate = ParamUtil.getDateValue(map, "report_date", false, null);
		
		String optNum = ParamUtil.getStringValue(map, "opt_num", false, null);
		
		JSONObject postJson = new JSONObject();
		List<JSONObject> postList = new ArrayList<JSONObject>();
		postJson.put("operator_id", optNum);
		postJson.put("operator", customerDao.getEmpNameById(optNum, tenantId, organId));
		postJson.put("business_date", DateUtil.format(reportDate));
		postJson.put("operator_time", DateUtil.getNowDateYYDDMMHHMMSS());
		postJson.put("item_list", jsParam);
		postList.add(postJson);
		
		Data paramSubmit = new Data();
		paramSubmit.setSecret(param.getSecret());
		paramSubmit.setT(param.getT());
		paramSubmit.setPagination(param.getPagination());
		paramSubmit.setTenancy_id(tenantId);
		paramSubmit.setStore_id(organId);
		paramSubmit.setData(postList);
		paramSubmit.setOper(param.getOper());
		paramSubmit.setType(Type.DISH_SOLD_OUT);
		paramSubmit.setSuccess(false);
		Data postResult = new Data();
		
		commonPost(JSONObject.fromObject(paramSubmit).toString(), postResult);
	}
	
	@Override
	@Deprecated
	public void customerIncorporationPost(String param, Data resData)
	{
		this.commonPost(param, resData);

		if (resData.isSuccess())
		{
			// 插入管体挂账流水表
			StringBuilder sqlGz = new StringBuilder(
					"insert into crm_incorporation_gzlist (tenancy_id,incorporation_id,business_date,bill_code,gz_money,gz_person,store_id,remark,is_cz,cz_money,cz_person,cz_time,operate_time,operator,mz_money,wcz_money,third_bill_code,valid_state,name,customer_name) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");

			String tenantId = resData.getTenancy_id();
			Integer storeId = resData.getStore_id();
			JSONObject resultObj = JSONObject.fromObject(resData.getData().get(0));

			Integer incorporation_id = null;
			Date business_date = null;
			String bill_code = null;
			double gz_money = 0d;
			String gz_person = null;
			String remark = null;
			String is_cz = null;
			double cz_money = 0d;
			String cz_person = null;
			Date cz_time = null;
			Timestamp operate_time = null;
			String operator = null;
			double mz_money = 0d;
			double wcz_money = 0d;
			String third_bill_code = null;
			String valid_state = null;
			String name = null;
			String customer_name = null;

			Object[] objs = null;
			JSONObject gzJo = resultObj.optJSONObject("gzlist");
			
			tenantId = gzJo.optString("tenancy_id");
			storeId = gzJo.optInt("store_id");
			
			incorporation_id = gzJo.optInt("incorporation_id");
			business_date = DateUtil.parseDate(gzJo.optString("business_date"));
			bill_code = gzJo.optString("bill_code");
			gz_money = gzJo.optDouble("gz_money");
			if(Double.isNaN(gz_money))
			{
				gz_money = 0d;
			}
			gz_person = gzJo.optString("gz_person");
			remark = gzJo.optString("remark");
			is_cz = gzJo.optString("is_cz");
			cz_money = gzJo.optDouble("cz_money");
			if(Double.isNaN(cz_money))
			{
				cz_money = 0d;
			}
			cz_person = gzJo.optString("cz_person");
			cz_time = DateUtil.parseDate(gzJo.optString("cz_time"));
			operate_time = DateUtil.currentTimestamp();
			operator = gzJo.optString("operator");
			mz_money = gzJo.optDouble("mz_money");
			if(Double.isNaN(mz_money))
			{
				mz_money = 0d;
			}
			wcz_money = gzJo.optDouble("wcz_money");
			if(Double.isNaN(wcz_money))
			{
				wcz_money = 0d;
			}
			third_bill_code = gzJo.optString("third_bill_code");
			valid_state = gzJo.optString("valid_state");
			name = gzJo.optString("name");
			customer_name = gzJo.optString("customer_name");

			objs = new Object[]
					{ tenantId, incorporation_id, business_date, bill_code, gz_money, gz_person, storeId, remark, is_cz, cz_money, cz_person, cz_time, operate_time, operator, mz_money, wcz_money, third_bill_code, valid_state, name, customer_name };
			try
			{
				customerDao.update(sqlGz.toString(), objs);
			}
			catch (Exception e)
			{
				e.printStackTrace();
				logger.info("团体挂账失败：" + e);
				throw SystemException.getInstance(PosErrorCode.OPER_ERROR);
			}
		}
	}
	
	protected JSONObject getPrintInvoiceInfo(String tenantId, Integer storeId, JSONObject param) throws Exception
	{
		String billCode = param.optString("bill_code");
		Date businessDate = DateUtil.parseDate(param.optString("business_date"));
		Timestamp operateTime = DateUtil.parseTimestamp(param.optString("operateTime"));
		double invoiceAmount = param.optDouble("invoice_amount");
		double taxRate = param.optDouble("tax_rate");
		String strUrlContent = "";
		String strimgPath = "";
		String strUrlPath = "";
		
		// 生成校验密码
		// tenancy_id#store_id#DDRQ#DH#SERVICE_TYPE#SL#JE#key
		String serviceType = "21";
		String content = tenantId + "#" + storeId + "#" + DateUtil.getYYYYMMDDFromDate(businessDate) + "#" + billCode.substring(2, billCode.length()) + "#" + serviceType + "#" + taxRate + "#" + invoiceAmount;
		
		String password = customerDao.getSysParameter(tenantId, storeId, "dzfp_ewmdyfpmy");
		String para = KeyUtils.encryptASE_MD5(content, password);
		
		// 获得二维码URL
		strUrlContent = getPequestUrl(INVOICE_GET_URL)+"?para="; 
		
		// 加密前URL
		logger.info("加密前的二维码URL: " + strUrlContent + content + "#" + password);
		content = content + "#" + para.substring(0, 4);
		// 加密URL
		BASE64Encoder base64en = new BASE64Encoder();
        String newContent = base64en.encode(content.getBytes());
        newContent = newContent.replace("\r\n", "");
		strUrlContent = strUrlContent + newContent;
		
		// 生成电子发票二维码图片
		String strPhysicalPath = PathUtil.getWebRootPath() +  File.separator;
		String fileName = billCode + DateUtil.getYYYYMMDDHHMMSS(operateTime) + ".png";
		strimgPath = "img/qr_code/invoice/" + fileName;
		strPhysicalPath = strPhysicalPath + strimgPath;
		QrCodeUtils.encode(strUrlContent, strPhysicalPath);
		logger.info("生成的二维码URL: " + strUrlContent);
		
		String webPathConst = com.tzx.framework.common.constant.Constant.getSystemMap().get("WEB_PATH");
		strUrlPath = webPathConst + strimgPath;
		
		param.put("url_content", strUrlContent);
		param.put("url_path", strUrlPath);
		param.put("last_updatetime", DateUtil.format(operateTime));
		
		return param;
	}

	@Override
	public void commonDebitPay(String param, Data resData) {
		
//		String reqURL = "http://172.18.95.35:8080:8080/tzxsaas/crmRest/post"; //
		//String reqURL = com.tzx.framework.common.constant.Constant.systemMap.get("crmUrl");
		String reqURL = getPequestUrl(CRM_POST_URL);
		try{
			long t = System.currentTimeMillis();
			JSONObject paramJson = JSONObject.fromObject(param);
			posToCrmLogger.info(String.valueOf(t)+"<发送接口请求体==>type: " + paramJson.optString("type"));
			posToCrmLogger.info(String.valueOf(t)+"<发送接口请求体==>oper: " + paramJson.optString("oper"));
			posToCrmLogger.info(String.valueOf(t)+"<发送接口请求体==>" + param);
			logger.info(String.valueOf(t)+"<发送接口请求体==>" + param);
			String result = HttpUtil.sendPostRequest(reqURL, param);
			logger.info(String.valueOf(t)+"<发送接口返回体==>" + result);
			if (StringUtils.isEmpty(result))
			{
				resData.setCode(99);
				resData.setMsg("连接超时，请检查网络");
				resData.setSuccess(false);
				logger.info("连接超时，请检查网络,请求体为：" + param);
			}
			else
			{
//				ObjectMapper objectMapper = new ObjectMapper();
				try
				{
//					Data data = objectMapper.readValue(result, Data.class);
					JSONObject jsons = JSONObject.fromObject(result);
					if(jsons.toString() != "{}" && jsons.getBoolean("success") == true){
						JSONArray array = jsons.getJSONArray("data");
						JSONObject js = array.getJSONObject(0);
						if(!StringUtils.isEmpty(js.getString("photo_url"))){
							String debitUserImage =imageService.downloadDebitImage(js.getString("photo_url"));
							js.put("photo_url", debitUserImage);
							jsons.put("data", JSONArray.fromObject(js));
						}
					}
					Data data = JsonUtil.JsonToData(jsons);
					if (1 == data.getCode())
					{
						resData.setCode(99);
						resData.setMsg("网络连接失败");
						logger.info("连接异常,请求体为：" + param);
					}
					else
					{
						resData.setCode(data.getCode());
						resData.setMsg(data.getMsg());
					}
					resData.setData(data.getData());
					resData.setSuccess(data.isSuccess());
				}
				catch (Exception se)
				{
					logger.info("签账卡查询错误：" + ExceptionMessage.getExceptionMessage(se));
					se.printStackTrace();
				}
			}
			posToCrmLogger.info(String.valueOf(t)+"<调用接口返回体==>code: " + resData.getCode());
			posToCrmLogger.info(String.valueOf(t)+"<调用接口返回体==>msg: " + resData.getMsg());
			posToCrmLogger.info(String.valueOf(t)+"<调用接口返回体==>" + result);
		}
		catch (Exception se)
		{
			logger.info("连接超时，请检查网络,请求体为：" + param);
			resData.setCode(5);
			resData.setMsg("连接超时，请检查网络");
			resData.setSuccess(false);
		}
		
	}

	@Override
	public void queryMemberHistoryBill(Data param, Data posResult) {
		//String url = "http://127.0.0.1:8081/rest/hq/memberBillRest/queryMemberBill/";
		String url = getPequestUrl(CUSTOMER_HISTORY_BILL); 
		try
		{
			long t = System.currentTimeMillis();
			
			JSONObject paramJson = JSONObject.fromObject(param.getData().get(0));
			
			logger.info(String.valueOf(t)+"<发送接口请求体==>" + paramJson);
			String result = HttpUtil.sendPostRequest(url, paramJson.toString());
			logger.info(String.valueOf(t)+"<发送接口返回体==>" + result);
			if (StringUtils.isEmpty(result))
			{
				posResult.setCode(Constant.CODE_CONN_EXCEPTION);
				posResult.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
				posResult.setSuccess(false);
				logger.info("连接超时，请检查网络,请求体为：" + param);
			}
			else
			{
				try
				{
					JSONObject resultJo = JSONObject.fromObject(result);
					List<JSONObject> listJson = new ArrayList<JSONObject>();
					listJson.add(resultJo);
					posResult.setData(listJson);
				}
				catch (Exception se)
				{
					logger.info("转查会员信息错误：" + ExceptionMessage.getExceptionMessage(se));
					se.printStackTrace();
				}
			}
		}
		catch (Exception se)
		{
			logger.info("连接超时，请检查网络,请求体为：" + param);
			posResult.setCode(5);
			posResult.setMsg("连接超时，请检查网络");
			posResult.setSuccess(false);
		}
		
	}

	@Override
	public void queryMemberHistoryBillDetail(Data param, Data posResult) {
		String url = getPequestUrl(CUSTOMER_HISTORY_BILL_DETAIL); 
		//String url = "http://127.0.0.1:8081/rest/hq/memberBillRest/queryMemberBillDetail/";
		try
		{
			long t = System.currentTimeMillis();
			JSONObject paramJson = JSONObject.fromObject(param.getData().get(0));
			logger.info(String.valueOf(t)+"<发送接口请求体==>" + param);
			String result = HttpUtil.sendPostRequest(url, paramJson.toString());
			logger.info(String.valueOf(t)+"<发送接口返回体==>" + result);
			if (StringUtils.isEmpty(result))
			{
				posResult.setCode(Constant.CODE_CONN_EXCEPTION);
				posResult.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
				posResult.setSuccess(false);
				logger.info("连接超时，请检查网络,请求体为：" + param);
			}
			else
			{
				try
				{
					JSONObject resultJo = JSONObject.fromObject(result);
					List<JSONObject> listJson = new ArrayList<JSONObject>();
					listJson.add(resultJo);
					posResult.setData(listJson);
				}
				catch (Exception se)
				{
					logger.info("转查会员信息错误：" + ExceptionMessage.getExceptionMessage(se));
					se.printStackTrace();
				}
			}
		}
		catch (Exception se)
		{
			logger.info("连接超时，请检查网络,请求体为：" + param);
			posResult.setCode(5);
			posResult.setMsg("连接超时，请检查网络");
			posResult.setSuccess(false);
		}
		
	}

	@Override
	public void queryTableStateAndMemberState(Data param, Data result) {
		String tenantId = param.getTenancy_id();
		Integer organId = param.getStore_id();
		
		Map<String, Object> map = ReqDataUtil.getDataMap(param);
		Date reportDate = ParamUtil.getDateValue(map, "report_date", false, null);
		
		try {
			String billNumsSql = "select bill_num,table_code from pos_bill where report_date = '"+reportDate+"' and tenancy_id = '"+tenantId+"' and store_id = "+organId+" and bill_property = 'OPEN' and table_code in (select table_code from pos_tablestate where tenancy_id = '"+tenantId+"' and store_id = "+organId+" and state = 'BUSY')"; 
			List<JSONObject> listJson = customerDao.query4Json(tenantId, billNumsSql);
			JSONObject memberJo = null;
			JSONObject memberInfoJo = null;
			String cardCode = "";
			String mobil = "";
			List<JSONObject> memberListJson = new ArrayList<JSONObject>();
			if(listJson!=null&&listJson.size()>0){
				for (int i = 0; i < listJson.size(); i++) {
					memberJo = listJson.get(i);
					JSONObject jo = new JSONObject();
					String billNum = memberJo.getString("bill_num");
					String tableCode = memberJo.getString("table_code");//generic_field 通用字段   customer_rank 会员级别
					String memberSql = "select card_code,mobil,customer_name,generic_field from pos_bill_member where type = 'JF01' and tenancy_id = '"+tenantId+"' and store_id = "+organId+" and report_date = '"+reportDate+"' and bill_num = '"+billNum+"'";
					List<JSONObject> memberInfotableStateListJson = customerDao.query4Json(tenantId, memberSql);
					if(memberInfotableStateListJson!=null&&memberInfotableStateListJson.size()>0){
							memberInfoJo = memberInfotableStateListJson.get(0);
							cardCode = memberInfoJo.getString("card_code");
							mobil = memberInfoJo.getString("mobil");
						    String customerName = memberInfoJo.getString("customer_name");
						    String customerRank = "";
						    String genericfield = memberInfoJo.getString("generic_field");
						    if(genericfield!=null&&!"".equals(genericfield)&&!genericfield.equals("null")){
						    	JSONObject genericfieldJo = JSONObject.fromObject(genericfield);
						    	if(genericfieldJo.containsKey("customer_rank")){
						    		customerRank = genericfieldJo.getString("customer_rank");
						    	}
						    	
						    }
						    if(customerName==null||"".equals(customerName)){
						    	customerName = "";
						    }
						    	jo.put("card_code", cardCode);
					    	    jo.put("mobil", mobil);
						    	jo.put("customer_name", customerName);
                                jo.put("customer_rank", customerRank);
                                jo.put("table_code", tableCode);
                                jo.put("bill_num", billNum);
                                jo.put("is_or_not", "Y");
                                memberListJson.add(jo);
						}else{
							jo.put("card_code", cardCode);
				    	    jo.put("mobil", mobil);
							jo.put("customer_name", "");
                            jo.put("customer_rank", "");
                            jo.put("table_code", tableCode);
                            jo.put("bill_num", billNum);
                            jo.put("is_or_not", "N");
                            memberListJson.add(jo);
						}
					}
				}
			result.setData(memberListJson);
			result.setSuccess(true);
		} catch (Exception e) {
			e.printStackTrace();
			result.setData(null);
			result.setSuccess(false);
		}
		
	}
	


	public String getCustomerMobile(String tenancyId,int storeId,String bill_num,String card_code,String customer_code) throws Exception{
		String mobile = null;
		StringBuffer query = new StringBuffer();
		query.append("SELECT  mobil from pos_bill_member where tenancy_id=? and store_id =? and bill_num=?  and card_code=? and customer_code=?  and mobil is not null and mobil<>''");
		List<JSONObject> jsonObjects = customerDao.query4Json(tenancyId,query.toString(),new Object[]{tenancyId,storeId,bill_num,card_code,customer_code});
		if(jsonObjects!=null && jsonObjects.size()>0){
			JSONObject json = jsonObjects.get(0);
			mobile = json.optString("mobil");
		}
		return mobile;
	}

}
