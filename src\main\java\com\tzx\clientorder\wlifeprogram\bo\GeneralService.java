package com.tzx.clientorder.wlifeprogram.bo;

import net.sf.json.JSONObject;

/**
 * 通用业务（查询门店营业状态、桌台的订单）
 * Created by qingui on 2018-06-26.
 */
public interface GeneralService {

    String name = "com.tzx.clientorder.wlifeprogram.bo.GeneralServiceImp";

    /**
     * 查询门店的营业状态
     * @param tenancyId
     * @param organId
     * @return
     * @throws Exception
     */
    void getOrganStatus(JSONObject responseJson,String tenancyId, int organId) throws Exception;

    /**
     * 查询门店桌台的订单信息
     * @param jsobj
     * @return
     * @throws Exception
     */
    void getTableOrder(JSONObject responseJson, JSONObject jsobj) throws Exception;
}
