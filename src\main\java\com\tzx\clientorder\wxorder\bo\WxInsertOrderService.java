package com.tzx.clientorder.wxorder.bo;

import com.tzx.orders.bo.dto.PosBill;
import net.sf.json.JSONObject;

/**
 * @Description 微信后付下单接口
 * <AUTHOR>
 * @Date 2018-11-08 13:52
 */
public interface WxInsertOrderService {
    String NAME = "com.tzx.clientorder.wxorder.bo.WxInsertOrderServiceImp";

    /** 订单管理
     * @param tenancyId
     * @param storeId
     * @param data
     * @return
     * @throws Exception
     */
    public String ordersManagement(String tenancyId,int storeId,JSONObject data) throws Exception;

    /**
     * @param tenancyId
     * @param storeId
     * @param data
     * @return
     * @throws Exception
     */
    public void addOrders(String tenancyId,int storeId,JSONObject data) throws Exception;

    /**
     * 下单/加菜
     * @param tenancyId
     * @param storeId
     * @param tableCode
     * @param openId
     * @param order
     * @throws Exception
     */
    void saveOrUpdateOrder(String tenancyId, int storeId, String tableCode, String openId, String orderCode, PosBill order) throws Exception;
}
