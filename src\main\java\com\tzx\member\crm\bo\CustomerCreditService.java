package com.tzx.member.crm.bo;

import net.sf.json.JSONObject;

import com.tzx.framework.common.entity.Data;
import com.tzx.member.common.entity.PosCustomerOperateListEntity;

public interface CustomerCreditService extends CustomerService
{
	String	NAME	= "com.tzx.member.crm.bo.imp.CustomerCreditServiceImp";

	/** 积分消费
	 * @param tenancyId
	 * @param storeId
	 * @param param
	 * @param resData
	 * @param operType
	 * @throws Exception
	 */
	public void customerCreditConsume(String tenancyId, int storeId, JSONObject param, Data resData, String operType) throws Exception;

	/**积分消费查询
	 * @param tenancyId
	 * @param storeId
	 * @param customerOperate
	 * @return
	 * @throws Exception
	 */
	public Data queryCustomerCreditConsume(String tenancyId, int storeId, JSONObject paramJson) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param customerOperate
	 * @return
	 * @throws Exception
	 */
	public Data queryCustomerCreditConsume(String tenancyId, int storeId, PosCustomerOperateListEntity customerOperate)throws Exception;
	
	/**撤销积分消费
	 * @param tenancyId
	 * @param storeId
	 * @param param
	 * @param resData
	 * @param operType
	 * @throws Exception
	 */
	public void customerCreditCancelConsume(String tenancyId, int storeId, JSONObject param, Data resData, String operType) throws Exception;

	/**撤销积分消费查询
	 * @param tenancyId
	 * @param storeId
	 * @param customerOperate
	 * @return
	 * @throws Exception
	 */
	public Data queryCustomerCreditCancelConsume(String tenancyId, int storeId, PosCustomerOperateListEntity customerOperate) throws Exception;
	
	/** 积分增加
	 * @param tenancyId
	 * @param storeId
	 * @param param
	 * @param resData
	 * @param operType
	 * @throws Exception
	 */
	public void customerCreditAdd(String tenancyId, int storeId, JSONObject param, Data resData, String operType) throws Exception;

	/**积分增加查询
	 * @param tenancyId
	 * @param storeId
	 * @param customerOperate
	 * @return
	 * @throws Exception
	 */
	public Data queryCustomerCreditAdd(String tenancyId, int storeId, PosCustomerOperateListEntity customerOperate) throws Exception;

	/**撤销积分增加
	 * @param tenancyId
	 * @param storeId
	 * @param param
	 * @param resData
	 * @param operType
	 * @throws Exception
	 */
	public void customerCreditCancelAdd(String tenancyId, int storeId, JSONObject param, Data resData, String operType) throws Exception;

	/**撤销积分增加查询
	 * @param tenancyId
	 * @param storeId
	 * @param customerOperate
	 * @return
	 * @throws Exception
	 */
	public Data queryCustomerCreditCancelAdd(String tenancyId, int storeId, PosCustomerOperateListEntity customerOperate) throws Exception;
}
