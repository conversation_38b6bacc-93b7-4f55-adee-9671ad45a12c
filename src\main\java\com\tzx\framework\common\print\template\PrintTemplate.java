package com.tzx.framework.common.print.template;

import java.awt.Font;
import java.text.DecimalFormat;
import java.util.List;

import com.tzx.framework.common.print.FontHeightUtil;
import com.tzx.framework.common.print.PrintLine;
import com.tzx.framework.common.print.PrintMete;

/**
 * 通过业务对象创建打印对象，封装打印行和打印元素
 * 
 * <AUTHOR>
 * 
 */
public abstract class PrintTemplate
{
	Font	font_nomal;

	float	f005	= 0.05f;
	int		nomal	= 10;
	int		h1		= 22;
	int		h2		= 19;
	int		h3		= 16;
	int		h4		= 13;
	int		h5		= 10;
	int		h6		= 9;

	public PrintTemplate()
	{
		font_nomal = new Font("宋体", Font.PLAIN, nomal);
	}

	/**
	 * 计算打印高度
	 * 
	 * @param printLineList
	 * @return
	 * @throws Exception
	 */
	public Float computePrintHeight(List<PrintLine> printLineList) throws Exception
	{
		if (printLineList.size() == 0)
		{
			return 0f;
		}

		float currentHeigth = 0;
		for (PrintLine printline : printLineList)
		{
			currentHeigth += printline.getMarginTop();

			float imgHeight = 0;
			for (PrintMete printmete : printline.getPrintMeteList())
			{
				if (PrintMete.CONTENT_STYLE_IMAGE == printmete.getContent_style())
				{
					if (imgHeight < printmete.getHeight())
					{
						imgHeight = printmete.getHeight();
					}
				}
			}

			if (imgHeight > 0)
			{
				currentHeigth += imgHeight;
			}
			else
			{
				Font font = printline.getFont();
				if (font != null)
				{
					currentHeigth += FontHeightUtil.getfontHeights(font.getSize2D());
				}
			}
			currentHeigth += printline.getMarginBottom();
		}
		return new Float(currentHeigth);
	}

	public abstract List<PrintLine> setupPrintLineList(Object business) throws Exception;

	public String doubleFormat(Double d)
	{
		DecimalFormat format = new DecimalFormat("#.#");
		return format.format(d).toString();
	}

	public int getSize(String tagName)
	{
		if ("h1".equals(tagName))
		{
			return h1;
		}
		else if ("h2".equals(tagName))
		{
			return h2;
		}
		else if ("h3".equals(tagName))
		{
			return h3;
		}
		else if ("h4".equals(tagName))
		{
			return h4;
		}
		else if ("h5".equals(tagName))
		{
			return h5;
		}
		else if ("h6".equals(tagName))
		{
			return h6;
		}
		else
		{
			return nomal;
		}
	}
}
