package com.tzx.ios.po.springjdbc.dao;

import java.util.Arrays;
import java.util.List;

import net.sf.json.JSONObject;

import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.entity.Pagination;
import com.tzx.pos.base.dao.BaseDao;

public interface BaseDataSyncDao extends BaseDao
{
	String	NAME	= "com.tzx.ios.po.springjdbc.dao.imp.BaseDataSyncDaoImp";

	// 根据机构id获得班次
	public List<JSONObject> getDutyOrderByOrganId(String tenantId, Integer organId) throws Exception;

	// 桌类属性
	public List<JSONObject> getTablePropertyByOrganId(String tenantId, Integer organId) throws Exception;

	// 区域
	public List<JSONObject> getBussinessArea(String tenantId, Integer organId) throws Exception;

	// 菜品类别
	public List<JSONObject> getItemClassByTenantId(String tenantId, Integer organId, String channel) throws Exception;

	// 菜品格式
	public List<JSONObject> getDishFormat(String tenantId, Integer organId, String channel) throws Exception;

	// 服务费种
	public List<JSONObject> getServiceFeeTypeByOrganId(String tenantId, Integer organId) throws Exception;

	// 口味备注
	public List<JSONObject> getItemTastes(String tenantId, Integer organId) throws Exception;

	// 做法
	public List<JSONObject> getItemMethodsByTenantId(String tenantId, Integer organId) throws Exception;

	// 退菜、奉送、优惠、恢复账单、免单原因
	public List<JSONObject> getUnusualReasonByTenantId(String tenantId, Integer organId) throws Exception;

	// 折扣方案
	public List<JSONObject> getDiscountCaseByTenantId(String tenantId, Integer organId) throws Exception;

	// 折扣方案明细
	public Data getDiscountCaseDetailsByTenantId(String tenantId, Integer organId,Pagination pagination) throws Exception;

	// 付款方式
	public List<JSONObject> getPaymentWayByOrganId(String tenantId, Integer organId) throws Exception;

	// 付款方式显示类型表
	public List<JSONObject> getPaymentShowtypeByOrganId(String tenantId, Integer organId) throws Exception;

	/** 用户PAd和电脑图片 **/
	public List<JSONObject> getBohImgByPcOrPad(String tenantId, Integer organId) throws Exception;

	/** 沽清菜品 **/
	public List<JSONObject> getItemSoldout(String tenantId, Integer organId) throws Exception;

	/** 急推菜品 **/
	public List<JSONObject> getItemWorrysale(String tenantId, Integer organId) throws Exception;

	// 菜品
	public Data getDishByTenantId(String tenantId, Integer organId, String channel, Pagination pagination) throws Exception;

	// 规格
	public Data getItemUnitsByTenantId(String tenantId, Integer organId, String channel, Pagination pagination) throws Exception;

	// 桌位
	public List<JSONObject> getTablesByTenantIdAndOrganId(String tenantId, Integer organId) throws Exception;

	// 时段价格
	public List<JSONObject> getTimePriceByOrganId(String tenantId, Integer organId, String channel) throws Exception;

	// 时段价格明细
	public List<JSONObject> getTimePriceItemByOrganId(String tenantId, Integer organId, String channel) throws Exception;

	// 套餐明细
	public List<JSONObject> getItemComboDetailsByOrganId(String tenantId, Integer organId, String channel) throws Exception;

	// 套餐项目组明细

	public List<JSONObject> getItemGroupDetailsByOrganId(String tenantId, Integer organId, String channel) throws Exception;

	// 会员卡种
	public List<JSONObject> getCrmCardClassByOrganId(String tenantId, Integer organId) throws Exception;

	// 人员同步
	public List<JSONObject> getUsersByTenantId(String tenantId, Integer organId) throws Exception;

	// 系统参数同步
	public List<JSONObject> getSysParameter(String tenantId, Integer organId) throws Exception;

	// 获取团体会员
	public List<JSONObject> getCrmInCorporations(String tenantId, Integer organId) throws Exception;

	// 获取团体会员挂账人
	public List<JSONObject> getCrmInCorporationPersons(String tenantId, Integer organId) throws Exception;

	// 套餐项目组同步
	public List<JSONObject> getItemGroup(String tenancyId, int storeId) throws Exception;

	// 设备同步
	public List<JSONObject> getDevicesByOrgan(String tenantId, Integer organId) throws Exception;

	/** 用户折扣方案权限 */
	public List<JSONObject> getUserDiscountCaseByOrgan(String tenantId, Integer organId) throws Exception;

	/** 用户折扣权限 */
	public List<JSONObject> getUserDiscountAuthorityByOrgan(String tenantId, Integer organId) throws Exception;

	/**
	 * (同步活动规则)
	 * 
	 * @param tenancyId
	 * @return
	 */
	public List<JSONObject> getActivityRuleDetails(String tenancyId) throws Exception;

	/**
	 * (同步活动商品限制规则)
	 * 
	 * @param tenancyId
	 * @return
	 */
	public List<JSONObject> getCrmActivityItem(String tenancyId) throws Exception;

	/**
	 * @param tenancyId
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getSysDictionary(String tenancyId) throws Exception;

	/**
	 * @param tenancyId
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getAndroidSysDictionary(String tenancyId) throws Exception;

	/**
	 * 菜单按钮配置信息
	 * 
	 * @param tenantId
	 * @param organId
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getMenuButtonConfig(String tenantId, Integer organId) throws Exception;

	/**
	 * 支付方式按钮排序配置信息
	 * 
	 * @param tenantId
	 * @param organId
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getPaymentWayButtonConfig(String tenantId, Integer organId) throws Exception;

	/**
	 * 就餐类型信息
	 * 
	 * @param tenantId
	 * @param organId
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getDiningType(String tenantId, Integer organId) throws Exception;

	/**
	 * @param tenantId
	 * @param organId
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getHqItemTasteDetails(String tenantId, Integer organId) throws Exception;


	/**
	 * 获取pos设置的禁用菜品
	 * @param tenantId
	 * @param organId
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getPosForbiddenItem(String tenantId, Integer organId) throws Exception;

	public List<JSONObject> getPosItemComboDetailsDefault(String tenantId, Integer organId) throws Exception;


	public List<JSONObject> getRoles(String tenantId) throws Exception;
}
