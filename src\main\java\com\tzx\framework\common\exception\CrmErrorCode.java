package com.tzx.framework.common.exception;

public enum CrmErrorCode implements ErrorCode
{

	/**
	 * 名字不能为空
	 */
	NAME_EMPTY_ERROR(5001),
	/**
	 * 手机号不能为空
	 */
	MOBIL_EMPTY_ERROR(5002),
	/**
	 * 该手机号已经注册
	 */
	MOBIL_EXIST_ERROR(5003),
	/**
	 * 密码不能为空
	 */
	PASSWORD_EMPTY_ERROR(5004),
	/**
	 * 登录名不能为空
	 */
	LOGINNAME_EMPTY_ERROR(5005),
	/**
	 * 微信号已存在,请更换
	 */
	WECHAT_EXIST_ERROR(5006),
	/**
	 * QQ号已存在,请更换
	 */
	QQ_EXIST_ERROR(5007),
	/**
	 * 邮箱已存在,请更换
	 */
	EMAIL_EXIST_ERROR(5008),
	/**
	 * 登录名已存在,请更换
	 */
	WEBNAME_EXIST_ERROR(5009),
	/**
	 * 手机号不正确
	 */
	MOBIL_FORMAT_ERROR(5010),
	/**
	 * 登录失败
	 */
	LOGIN_ERROR(5011),
	/**
	 * 修改参数体有误
	 */
	UPDATE_DATA_ERROR(5012),
	/**
	 * 查询失败
	 */
	FIND_ERROR(5013),
	/**
	 * 开始日期不能为空
	 */
	START_DATE_EMPTY_ERROR(5014),
	/**
	 * 结束日期不能为空
	 */
	END_DATE_EMPTY_ERROR(5015),
	/**
	 * 查询条件不能为空
	 */
	FIND_CONDITION_EMPTY_ERROR(5016),
	/**
	 * 日期格式错误
	 */
	DATE_FORMAT_ERROR(5017),
	/**
	 * 卡编号不能为空
	 */
	CODE_EMPTY_ERROE(5018),
	/**
	 * 修改送餐地址信息不全
	 */
	ADDRESS_EDITDATA_ERROE(5019),
	/**
	 * 修改会员卡信息不全
	 */
	CARD_EDITDATA_ERROE(5020),
	/**
	 * 修改会员信息不存在,确认编码正确
	 */
	NONEMEMBER_ERROE(5021),
	/**
	 * 激活卡号不能为空
	 */
	CARD_CODE_EMPTY_ERROE(5022),
	/**
	 * 激活卡号不存在
	 */
	CARD_NOTEXIST_ERROE(5023),
	/**
	 * 该卡片已经激活
	 */
	CARD_ACTIVATED_ERROE(5024),
	/**
	 * 该卡片已经作废
	 */
	CARD_VOID_ERROE(5025),
	/**
	 * 该卡片已经挂失
	 */
	CARD_LOSS_ERROE(5026),
	/**
	 * 卡片状态异常
	 */
	CARD_STATE_ERROE(5027),
	/**
	 * 此卡信息不存在
	 */
	CONSUMECARD_NOTEXIST_ERROE(5028),
	/**
	 * 支付密码不能为空
	 */
	CARDPASSWORD_NOTEXIST_ERROE(5029),
	/**
	 * 交易总金额不能为空
	 */
	TOTALMONEY_NOTEXIST_ERROE(5030),
	/**
	 * 使用预存额不能空
	 */
	CARDMONEY_NOTEXIST_ERROE(5031),
	/**
	 * 使用积分额不能空
	 */
	CREDIT_NOTEXIST_ERROE(5032),
	/**
	 * 可积分金额不能空
	 */
	CREDITMONEY_NOTEXIST_ERROE(5033),
	/**
	 * 消费渠道不能为空
	 */
	CHANEL_NOTEXIST_ERROE(5034),
	/**
	 * 交易单号不能为空
	 */
	BILLCODE_NOTEXIST_ERROE(5035),
	/**
	 * 交易单号必须唯一
	 */
	BILLCODE_EXIST_ERROE(5036),
	/**
	 * 操作人不能为空
	 */
	OPERATOR_EMPTY_ERROE(5037),
	/**
	 * 操作时间不能为空
	 */
	UPDATETIME_EMPTY_ERROE(5038),
	/**
	 * 该卡片未激活
	 */
	CARD_NOTACTIVATED_ERROE(5039),
	/**
	 * 余额不足
	 */
	BALANCE_NOTENOUGH_ERROE(5040),
	/**
	 * 积分不足
	 */
	CREDIT_NOTENOUGH_ERROE(5041),
	/**
	 * 卡片信息已经修改,请刷新后重新操作
	 */
	CARD_UUID_ERROE(5042),
	/**
	 * 撤销单号不能为空
	 */
	OLDBILLCODE_NOTEXIST_ERROE(5043),
	/**
	 * 该卡不存在此账单
	 */
	TRADING_NOTEXIST_ERROE(5044),
	/**
	 * 实收金额不能为空
	 */
	INCOME_EMPTY_ERROE(5045),
	/**
	 * 付款方式id不能为空
	 */
	PAYMENT_EMPTY_ERROE(5046),
	/**
	 * 支付密码错误
	 */
	CARDPASSWORD_ERROE(5047),
	/**
	 * 机构ID不能为空
	 */
	STORE_ID_EMPTY_ERROE(5048),
	/**
	 * 会员编号不能为空
	 */
	CUSTOMER_CODE_EMPTY_ERROR(5049),
	/**
	 * 原卡号不能为空
	 */
	CARD_CODE_ORIGINAL_EMPTY_ERROR(5050),
	/**
	 * 原卡不是挂失状态，不能补卡
	 */
	CARD_CODE_ORIGINAL_STATE_ERROR(5051),
	/**
	 * 原卡号不存在，请确认卡号
	 */
	CARD_CODE_ORIGINAL_NOTEXIST_ERROE(5052),
	/**
	 * 并出卡号不能为空
	 */
	CARD_CODE_OUT_EMPTY_ERROR(5053),
	/**
	 * 并出卡号不存在
	 */
	CARD_CODE_OUT_NOTEXIST_ERROR(5054),
	/**
	 * 并入卡号不能为空
	 */
	CARD_CODE_IN_EMPTY_ERROR(5055),
	/**
	 * 并入卡号不存在
	 */
	CARD_CODE_IN_NOTEXIST_ERROR(5056),
	/**
	 * 并入主账户金额不能为空
	 */
	MAIN_MERGE_EMPTY_ERROR(5057),
	/**
	 * 并入主账户金额超过余额
	 */
	MAIN_MERGE_LOW_ERROR(5058),
	/**
	 * 并入赠送账户金额不能为空
	 */
	REWARD_MERGE_EMPTY_ERROR(5059),
	/**
	 * 并入赠送账户金额超过余额
	 */
	REWARD_MERGE_LOW_ERROR(5060),
	/**
	 * 会员编号不存在
	 */
	CUSTOMER_CODE_NOTEXIST_ERROR(5061),
	/**
	 * 交易查询条件不能为空
	 */
	CUSTOMERTRANS_FIND_EMPTY_ERROR(5062),
	/**
	 * 卡号生成类型不能为空
	 */
	NO_TYPE_EMPTY_ERROR(5063),
	/**
	 * 卡号生成类型不匹配
	 */
	NO_TYPE_WRONG_ERROR(5064),
	/**
	 * 卡号已存在，不能重复
	 */
	CARDCODE_EXIST_ERROR(5065),
	/**
	 * 卡号不能为空
	 */
	CARDCODE_EMPTY_ERROR(5066),
	/**
	 * 账单金额不能为空
	 */
	BILLMONEY_EMPTY_ERROR(5067),
	/**
	 * 账单金额不正确
	 */
	BILLMONEY_ZERO_ERROR(5068),
	/**
	 * 券号不能为空
	 */
	COUPONS_CODE_EMPTY_ERROR(5069),
	/**
	 * 优惠券号{0}不存在
	 */
	COUPONS_CODE_NOTEXIST_ERROR(5070),
	/**
	 * 优惠券号{0}已使用
	 */
	COUPONS_CODE_USED(5071),
	/**
	 * 优惠券号{0}过期
	 */
	COUPONS_CODE_TIME_LIMIT(5072),
	/**
	 * 优惠券{0}不允许在该渠道使用
	 */
	COUPONS_CODE_CHANEL_LIMIT(5073),
	/**
	 * 优惠券{0}不允许在该店使用
	 */
	COUPONS_CODE_STORE_LIMIT(5074),
	/**
	 * 使用该优惠券{0}，最低账单限额为{1}元
	 */
	COUPONS_CODE_LIMIT_MONEY(5075),
	/**
	 * 一个账单仅允许使用{0}张{1}优惠券
	 */
	BILL_LIMIT_NUM(5076),
	/**
	 * 会员ID不能为空
	 */
	CUSTOMER_ID_NOTEXIST_ERROR(5077),
	/**
	 * 优惠券种id不能为空
	 */
	COUPONS_TYPE_ID_NOTEXIST_ERROR(5078),
	/**
	 * 优惠券数量不能为空
	 */
	COUPONS_COUNT_NOTEXIST_ERROR(5079),
	/**
	 * 门店ID不能为空
	 */
	STORE_ID_NOTEXIST_ERROR(5080),
	/**
	 * 团体会员ID不能为空
	 */
	INCORPORATION_ID_NOTEXIST_ERROR(5081),
	/**
	 * 挂账金额不能为空
	 */
	GZ_MONEY_NOTEXIST_ERROR(5082),
	/**
	 * 账单编号不能为空
	 */
	BILL_CODE_NOTEXIST_ERROR(5083),
	/**
	 * 密码错误
	 */
	PASSWORD_ERROR(5084),
	/**
	 * 超出团体会员的信用额度范围
	 */
	BILL_MONEY_BEYOND_T_ERROE(5085),
	/**
	 * 超出挂账人的最大挂账金额范围
	 */
	BILL_MONEY_BEYOND_P_ERROE(5086),
	/**
	 * 团体会员信息不存在
	 */
	INCORPORATION_NOTEXIST_ERROR(5087),
	/**
	 * 挂账人信息不存在
	 */
	CUSTOMER_NOTEXIST_ERROR(5088),
	/**
	 * 会员信息不存在
	 */
	NO_CUSTOMER_ERROR(5089),
	/**
	 * 多倍积分的积分基数不能为空
	 */
	CREDIT_NOTEXIST_ERROR(5090),
	/**
	 * 活动id不能为空
	 */
	ACTIVITY_ID_NOTEXIST_ERROR(5091),
	/**
	 * 卡号不能为空
	 */
	CARD_CODE_NOTEXIST_ERROR(5092),
	/**
	 * 操作人不能为空
	 */
	OPERATOR_NOTEXIST_ERROR(5093),
	/**
	 * 卡号或会员号其中一项不能为空
	 */
	CARDCODE_CUSTOMERCODE_NOTEXIST_ERROR(5094),
	/**
	 * 等级信息不存在
	 */
	LEVEL_MES_NOTEXIST_ERROR(5095),
	/**
	 * 余额不足,不能撤销充值
	 */
	MAIN_BALANCE_NOENOUGH_ERROR(5096),
	/**
	 * 该充值记录已经撤销
	 */
	FCZ_BEFOR_ERROR(5097),
	/**
	 * 该消费记录已经撤销
	 */
	FXF_BEFOR_ERROR(5098),
	/**
	 * 该卡已过期
	 */
	CARD_END_DTAE_ERROR(5099),
	/**
	 * 该账单编号不存在,请核实
	 */
	INCORPORATION_GZLIST_NOTEXIST_ERROR(5100),
	/**
	 * 该账单编号已经撤销
	 */
	INCORPORATION_GZLIST_EXIST_ERROR(5101),
	/**
	 * 该卡为一次性充值卡，不允许重复充值
	 */
	CARD_IS_ONLY_CZ_ERROR(5102),
	/**
	 * 该卡{0}为不记名卡，不允许补卡
	 */
	CARD_IS_RECORDNAME_B_ERROR(5103),
	/**
	 * 该卡{0}为一次性充值卡，不允许补卡
	 */
	CARD_IS_ONLY_B_ERROR(5104),
	/**
	 * 该卡{0}没有实体卡，不允许补卡
	 */
	CARD_IS_PHYSICAL_B_ERROR(5105),
	/**
	 * 该卡{0}为不记名卡，不允许并卡
	 */
	CARD_IS_RECORDNAME_BK_ERROR(5106),
	/**
	 * 该卡{0}为一次性充值卡，不允许并卡
	 */
	CARD_IS_ONLY_BK_ERROR(5107),
	/**
	 * 该卡{0}为不记名卡，不允许挂失
	 */
	CARD_IS_RECORDNAME_GS_ERROR(5108),
	/**
	 * 该卡{0}为一次性充值卡，不允许挂失
	 */
	CARD_IS_ONLY_GS_ERROR(5109),
	/**
	 * 该卡{0}没有实体卡，不允许挂失
	 */
	CARD_IS_PHYSICAL_GS_ERROR(5110),
	/**
	 * 该卡{0}没有实体卡，不允许退卡
	 */
	CARD_IS_PHYSICAL_TK_ERROR(5111),
	/**
	 * 注册渠道类型不存在
	 */
	ADD_CHANEL_WRONG_ERROR(5112),
	/**
	 * 注册渠道不能为空
	 */
	ADD_CHANEL_NOTEXIST_ERROR(5113),
	/**
	 * 优惠券{0},该类优惠券已经停用
	 */
	COUPONS_TYPE_STOP_ERROR(5114),
	/**
	 * 优惠券号{0}还未到使用日期
	 */
	COUPONS_CODE_TIME_LESSLIMIT(5115),
	/**
	 *加盟商储值账户关闭，请及时与总部联系开启账户
	 */
	ORGAN_FRANCHISEES_CLOSED(5116),
	/**
	 * 超过加盟商储值限额，请及时于总部结算
	 */
	ORGAN_FRANCHISEES_LIMIT_MONEY(5117),
	/**
	 * 未达到最低变现积分数,变现规则:{0}积分变现 {1} 
	 */
	CREDIT_CASH_ROLE_ERROR(5118),
	/**
	 * 该账单非本店账单,不能进行撤销操作
	 */
	BILL_STORE_NOT_NOW(5119),
	/**
	 * 增加积分不能为空
	 */
	ADD_CREDIT_NOTEXIST_ERROE(5120),
	/**
	 * 增加积分必须大于0
	 */
	ADD_CREDIT_NOTENOUGH_ERROE(5121),
	/**
	 * 增加积分原因不能为空
	 */
	ADD_CREDIT_REASON_ERROE(5122),
	/**
	 * 会员不存在
	 */
	CUSTOMER_NOTEXIST_ERROE(5123),
	/**
	 * 查询信息为空，请确认参数
	 */
	VIPPRICE_LIST_NOTEXIST_ERROE(5124),
	/**
	 * 卡号有误，查询不到相关卡
	 */
	UPDATE_CARD_NOTEXIST_ERROE(5125),
	/**
	 * 传入卡号已绑定
	 */
	UPDATE_CARD_CANTBD_ERROE(5126),
	/**
	 * 传入卡号的状态不是正常状态，不能进行绑定
	 */
	UPDATE_CARD_STATE_ERROE(5127),
	/**
	 * 传入卡号已与其他账号绑定
	 */
	UPDATE_CARD_CANTBDQT_ERROE(5128),
	/**
	 * 该类型优惠券已停用
	 */
	COUPONS_CLASS_STOP_ERROE(5129),
	/**
	 * 该类型优惠券不适用当前时段
	 */
	COUPONS_CLASS_TIME_ERROE(5130),
	/**
	 * 该卡消费过于频繁，请确认没有重复操作
	 */
	CARD_XF_25S_ERROE(5131),
	/**
	 * 该卡充值过于频繁，请确认没有重复操作
	 */
	CARD_CZ_25S_ERROE(5132),
	/**
	 * 无效动态卡号
	 */
	DYNAMIC_CODE_ERROE(5133),
	/**
	 * 动态卡号空
	 */
	DYNAMIC_CODE_EMPTY_ERROE(5134),
	/**
	 * 赠送积分已使用，是否继续撤销？
	 */
	CREDIT_NOT_ENOUGH_ERROE(5135),
	/**
	 * 赠送优惠券已使用，无法撤销
	 */
	COUPONS_USED_ERROE(5136),
	/**
	 * 并出卡密码不能为空
	 */
	BK_CARD_PASSWORD_EMPTY_ERROR(5137),
	/**
	 * 并出卡密码错误
	 */
	BK_CARD_PASSWORD_WORNG_ERROR(5138),
	/**
	 * 退卡密码不能为空
	 */
	TK_CARD_PASSWORD_EMPTY_ERROR(5139),
	/**
	 * 退卡密码错误
	 */
	TK_CARD_PASSWORD_WORNG_ERROR(5140),
	/**
	 * 挂失卡密码不能为空
	 */
	GS_CARD_PASSWORD_EMPTY_ERROR(5141),
	/**
	 * 挂失卡密码错误
	 */
	GS_CARD_PASSWORD_WORNG_ERROR(5142),
	/**
	 * 查询卡密码不能为空
	 */
	CX_CARD_PASSWORD_EMPTY_ERROR(5143),
	/**
	 * 查询卡密码错误
	 */
	CX_CARD_PASSWORD_WORNG_ERROR(5144),
	/**
	 * 查询卡号不存在
	 */
	CX_CARD_CODE_NOTEXIST_ERROR(5145),
	/**
	 * 该卡已绑定卡号{0}
	 */
	THIRD_CARD_CODE_EXIST_ERROR(5146),
	/**
	 * 不存在此账单
	 */
	THIRDBILLCODE_NOTEXIST_ERROR(5147),
	/**
	 * 撤销金额超过可取消金额
	 */
	REVOKED_TRADING_ERROR(5148),
	/**
	 * 优惠券相关菜品明细不能为空
	 */
	COUPONS_DETAIL_ERROR(5149),
	/**
	 * 优惠券相关菜品明细参数错误
	 */
	COUPONS_DETAIL_CODE_ERROR(5150),
	/**
	 * 没有菜品券{0}可以抵扣的菜品  
	 */
	COUPONS_DISH_NOTFOUND_ERROR(5151),
	/**
	 * 券{0}在此时段不可用  
	 */
	COUPONS_TIMES_ERROR(5152),
	/**
	 * 券{0}不能与其他券同时使用 
	 */
	COUPONS_USEDOTHER_ERROR(5153),
	/**
	 * 券{0}不能与主券同时使用
	 */
	COUPONS_UESDMAIN_ERROR(5154),
	/**
	 * 不是会员,不能享受会员价
	 */
	VIP_NOTEXISIT_ERROR(5155),
	/**
	 * 该等级会员不享受会员价
	 */
	VIP_LEVEL_ERROR(5156),
	/**
	 * 该卡不对应此门店分组,无法消费
	 */
	ORGAN_GROUP_ERROR(5157),
	/**
	 * 该不记名卡只能在办理门店使用
	 */
	CARD_RECORDNAME_ERROR(5158),
	/**
	 * 该等级会员卡最低充值金额：{0}
	 */
	LIMIT_PRESTORE_PER(5159),
	/**
	 * 该类型卡不能退卡
	 */
	CARD_CLASS_TK_ERROR(5160),
	/**
	 * 该类型卡不能挂失
	 */
	CARD_CLASS_GS_ERROR(5161),
	/**
	 * 该类型卡不能充值
	 */
	CARD_CLASS_CZ_ERROR(5162),
	/**
	 * 指定卡类型不存在 
	 */
	CARD_CLASS_NOTEXISTS_ERROR(5163),
	/**
	 * 指定卡类型无效
	 */
	CARD_CLASS_WRONG_ERROR(5164),
	/**
	 * 查询类型不能为空
	 */
	FIND_TPYE_EMPTY_ERROR(5165),
	/**
	 * 付款金额不能为空
	 */
	PAY_MONEY_EMPTY_ERROR(5166),
	/**
	 * 购买等级不存在或错误
	 */
	BUY_LEVEL_NOTEXISTS_ERROR(5167),
	/**
	 * 购买金额与活动不符
	 */
	BUY_MONEY_ERROR(5168),
	/**
	 * 该会员不存在此账单
	 */
	MEMBER_TRADING_NOTEXIST_ERROE(5169),
	/**
	 * 会员等级与购买等级不一致，无法撤销
	 */
	MEMBER_LEVEL_WRONG_ERROE(5170),
	/**
	 * 该账单未付款，无法撤销
	 */
	MEMBER_TRADING_NOTPAY_ERROE(5171),
	/**
	 * 该账单已撤销
	 */
	MEMBER_TRADING_HADREVOKE_ERROE(5172),
	/**
	 * 没有相关购买活动
	 */
	BUY_MEMBER_ACT01_ERROE(5173),
	/**
	 * 没有符合该会员的购买活动
	 */
	BUY_MEMBER_ACT02_ERROE(5174),
	/**
	 * 优惠券{0}不能与其他优惠同时使用
	 */
	COUPONS_WITH_DISCOUNT_ERROR(5175),
	/**
	 * 该卡不属于指定会员
	 */
	CARD_NOT_THISMEMBER(5176),
	
	/**
	 * 会员信息不完整
	 */
	MEMBER_INFO_INCOMPLETE(5177),
	
	/**
	 * 积分抵现金额不能为空
	 */
	CREDIT4CASH_NOTEXIST_ERROE(5178),
	
	/**
	 * 抵现金额{0}与实际抵现金额{1}不符
	 */
	CASHMONEY_NOT_EQUALS(5179),
	
	/**
	 * 扣减积分不满足抵扣规则
	 */
	NEEDTOMINUS4CREDIT_ERROR(5180),
	
	/**
	 * 会员积分不足,剩余积分为{0}
	 */
	BONUSPOINT_NOT_ENOUGH(5181), 
	
	/**
	 * 会员积分已修改,请刷新后重新操作
	 */
	BONUSPOINT_UUID_ERROE(5182),
	
	/**
	 * 该积分信息已撤销
	 */
	BONUSPOINT_ALREADY_REVOKE(5183),
	
	
	/**
	 * 该单号已使用,请勿重复操作
	 */
	BILLCODE_ALREADY_CONSUMED(5184),
	
	/**
	 * 批次不能为空
	 */
	BATCHNO_NOTEXIST_ERROE(5185),
	
	/**
	 * 查询类型不能为空  type=1 查询撤销积分流水  type=0查询积分消费流水
	 */
	QUERYTYPE_NOTEXIST_ERROE(5186),
	
	/**
	 * 该撤销单号无对应记录
	 */
	OLDBILLCODE_HASNO_RECORDS(5187),
	
	/**
	 * 该积分已使用,不能撤销
	 */
	BONUSPOINT_ALREADY_USED(5188),
	
	/**
	 * 查询对应充赠营销活动结果为空
	 */
	ACTIVITY_CHZ_NOTEXIST(5189),
	
	/**
	 * 此次消费会员可积分数为0
	 */
	BONUSPOINT_IS_ZERO(5190),
	/**
	 * 发票流水查询条件不能为空
	 */
	INVOICE_BILL_FIND_EMPTY_ERROR(5191),
	
	/**
	 * 该会员的可开票金额不足
	 */
	INVOICE_MAX_ERROR(5192),
	/**
	 * 该会员没有充值记录
	 */
	NO_RECHARGE_ERROR(5193),
	/**
	 * 开票金额不能为空
	 */
	INVOICE_MONEY_NOTEXIST_ERROE(5194),
	/**
	 * 发票抬头不能为空
	 */
	INVOICE_HEAD_NOTEXIST_ERROE(5195),
	
	/**
	 * 本账单可抵的最大金额值（账单金额*积分最多可抵账单金额百分比）对应扣减的积分值（根据抵现金额计算使用的积分额）
	 *  本账单可抵的最大金额值{0}对应扣减的积分值{1}
	 */
	MAXMONEY_FOR_CREDIT(5196),
	
	/**
	 * 销售人员id不能为空
	 */
	SALESMAN_NOTEXIST_ERROE(5197),
	/**
	 * 操作过于频繁，请稍后再试
	 */
	REDIS_3S_ERROE(5198),
	/**
	 * 自定义异常
	 */
	MSG_ERROE(5199),
	/**第一次充值后第一次储值消费上限为该卡账户余额的{0}%*/
	CARD_FIRST_CONSUME_LIMIT(5200),
	/**不允许使用积分抵现*/
	CREDIT_CASH_NOT_ALLOW(5201),
	/**第一次消费不允许使用赠送金额*/
	CARD_FIRST_USE_REWARD_NOT_ALLOW(5202),
	/**不允许使用优惠券*/
	COUPONS_NOT_ALLOW_USE(5203),
	/**动态码已经绑定交易单号，请重新刷新动态码!*/
	DYNAMIC_CODE_HAS_BIND_BILLCODE(5204),
	/**
	 * 交易锁单单号与当前单号不一致
	 */
	DEAL_LOCK_NUMBER_DISCORD_ERROR(5219),
	;
	
	
	


	private final int	number;
	
	private String err_msg;	
	
	private CrmErrorCode(int number)
	{
		this.number = number;
	}

	public int getNumber()
	{
		return number;
	}

	@Override
	public String getMessage()
	{
		return err_msg;
	}

	public String getErr_msg()
	{
		return err_msg;
	}

	public void setErr_msg(String err_msg)
	{
		this.err_msg = err_msg;
	}

}
