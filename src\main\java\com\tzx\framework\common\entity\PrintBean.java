package com.tzx.framework.common.entity;

public class PrintBean {
	
	private String mode;
	private String pos_num;
	private String print_code;
	private String card_code;
	private String operator;
	private String updatetime;
	private String deposit;
	private String sales_price;
	private String bill_code;
	private String name;
	private String mobil;
	private String total_balance;
	private String main_balance;
	private String reward_balance;
	private String useful_credit;
	private String income;
	private String payment_name1;
	private String card_class_name;
	private String consume_cardmoney;
	private String main_trading;
	private String tenancy_id;
	private Integer store_id;
	
	
	public String getMode() {
		return mode;
	}
	public void setMode(String mode) {
		this.mode = mode;
	}
	public String getPos_num() {
		return pos_num;
	}
	public void setPos_num(String pos_num) {
		this.pos_num = pos_num;
	}
	public String getPrint_code() {
		return print_code;
	}
	public void setPrint_code(String print_code) {
		this.print_code = print_code;
	}
	public String getCard_code() {
		return card_code;
	}
	public void setCard_code(String card_code) {
		this.card_code = card_code;
	}
	public String getOperator() {
		return operator;
	}
	public void setOperator(String operator) {
		this.operator = operator;
	}
	public String getUpdatetime() {
		return updatetime;
	}
	public void setUpdatetime(String updatetime) {
		this.updatetime = updatetime;
	}
	public String getDeposit() {
		return deposit;
	}
	public void setDeposit(String deposit) {
		this.deposit = deposit;
	}
	public String getSales_price() {
		return sales_price;
	}
	public void setSales_price(String sales_price) {
		this.sales_price = sales_price;
	}
	public String getBill_code() {
		return bill_code;
	}
	public void setBill_code(String bill_code) {
		this.bill_code = bill_code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getMobil() {
		return mobil;
	}
	public void setMobil(String mobil) {
		this.mobil = mobil;
	}
	public String getTotal_balance() {
		return total_balance;
	}
	public void setTotal_balance(String total_balance) {
		this.total_balance = total_balance;
	}
	public String getMain_balance() {
		return main_balance;
	}
	public void setMain_balance(String main_balance) {
		this.main_balance = main_balance;
	}
	public String getReward_balance() {
		return reward_balance;
	}
	public void setReward_balance(String reward_balance) {
		this.reward_balance = reward_balance;
	}
	public String getUseful_credit() {
		return useful_credit;
	}
	public void setUseful_credit(String useful_credit) {
		this.useful_credit = useful_credit;
	}
	public String getIncome() {
		return income;
	}
	public void setIncome(String income) {
		this.income = income;
	}
	public String getPayment_name1() {
		return payment_name1;
	}
	public void setPayment_name1(String payment_name1) {
		this.payment_name1 = payment_name1;
	}
	public String getCard_class_name() {
		return card_class_name;
	}
	public void setCard_class_name(String card_class_name) {
		this.card_class_name = card_class_name;
	}
	public String getConsume_cardmoney() {
		return consume_cardmoney;
	}
	public void setConsume_cardmoney(String consume_cardmoney) {
		this.consume_cardmoney = consume_cardmoney;
	}
	public String getMain_trading() {
		return main_trading;
	}
	public void setMain_trading(String main_trading) {
		this.main_trading = main_trading;
	}
	public String getTenancy_id() {
		return tenancy_id;
	}
	public void setTenancy_id(String tenancy_id) {
		this.tenancy_id = tenancy_id;
	}
	public Integer getStore_id() {
		return store_id;
	}
	public void setStore_id(Integer store_id) {
		this.store_id = store_id;
	}
	
	

}
