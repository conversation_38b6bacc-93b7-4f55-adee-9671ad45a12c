package com.tzx.pos.base.dao;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.support.rowset.SqlRowSet;

import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.dao.GenericDao;

import net.sf.json.JSONObject;

/**
 * 
 * <AUTHOR>
 * @日期：2015年5月26日-下午5:32:41
 */

public interface BaseDao extends GenericDao
{
	/** 记录日志
	 * @param tenantId
	 * @param organId
	 * @param posNum
	 * @param optNum
	 * @param optName
	 * @param shiftId
	 * @param reportDate
	 * @param title
	 * @param content
	 * @param oldstate
	 * @param newstate
	 */
	public void savePosLog(String tenantId, Integer organId, String posNum, String optNum, String optName, Integer shiftId, Date reportDate, String title, String content, String oldstate, String newstate);

	/**
	 * @param tenantId
	 * @param organId
	 * @param posNum
	 * @param optNum
	 * @param optName
	 * @param shiftId
	 * @param reportDate
	 * @param title
	 * @param content
	 * @param oldstate
	 * @param newstate
	 * @param lastUpdateTime
	 * @throws Exception
	 */
	public void savePosLog(String tenantId, Integer organId, String posNum, String optNum, String optName, Integer shiftId, Date reportDate, String title, String content, String oldstate, String newstate, Timestamp lastUpdateTime) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param reportDate
	 * @param shiftId
	 * @param posNum
	 * @param optNum
	 * @param content
	 * @param managerNum
	 * @param remark
	 * @param loginNumber
	 * @throws Exception
	 */
	public void saveOptState(String tenancyId, int storeId, Date reportDate, int shiftId, String posNum, String optNum, String content, String managerNum, String remark, Integer loginNumber,Timestamp lastUpdateTime)throws Exception;
	
	/**
	 * @param tenantId
	 * @param organId
	 * @param billNum
	 * @param tableCode
	 * @throws SystemException
	 */
	@Deprecated
	public void calcAmount(String tenantId, Integer organId, String billNum, String tableCode) throws SystemException;

	/**
	 * 金额计算
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @throws Exception
	 */
	@Deprecated
	public void calcAmount(String tenantId, Integer storeId, String billNum) throws Exception;
	
	/**
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @throws Exception
	 */
	@Deprecated
	public void calcAmountForStandard(String tenantId, Integer storeId, String billNum) throws Exception;
	
	/**
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @throws Exception
	 */
	@Deprecated
	public void calcAmountForSelf(String tenantId, Integer storeId, String billNum) throws Exception;

	/** 账单打印
	 * @param mode 
	 * @param tenantId 机构id
	 * @param organId 门店id
	 * @param billno 账单编号
	 * @param posNum 机台编号
	 * @param optNum 人员编号
	 * @param reportDate 报表日期
	 * @param shiftId 班次ID
	 * @param printCode 打印模板
	 * @param printType 1表示本人本机 2表示本人所有机号3表示所有人所有机号
	 * @param dateRemark 这个主要用来查询交班单，模板里需要开始时间和结束时间
	 * @param paymentUrl
	 * @param isInvoice 是否打印电子发票
	 * @param strUrlPath 电子发票二维码存放路径
	 * @param strUrlContent 电子发票二维码URL
	 * @param orderRemark
	 * @param qrcode 付款二维码
	 * @param qrcodeUrl 付款二维码存放路径
	 */
	public void billPrint(String mode, String tenantId, Integer organId, String billno, String posNum, String optNum, Date reportDate, Integer shiftId, String printCode, String printType, String dateRemark, String paymentUrl, String isInvoice, String strUrlPath, String strUrlContent,String orderRemark,String qrcode,String qrcodeUrl);

	/**
	 * 会员打印
	 * 
	 * @param mode
	 * @param tenancyid
	 *            机构id
	 * @param cardcode
	 *            会员编号
	 * @param billcode
	 * @param posNum
	 *            机台编号
	 * @param printCode
	 *            打印模板
	 * @param 门店id
	 * 
	 * @param strUrlPath
	 *            电子发票二维码存放路径
	 *            
	 * @param strUrlContent
	 *            电子发票二维码URL   
	 */
	public void customerPrint(String mode, String tenantId, String cardCode, String billCode, String posNum, String printCode, Integer organId, double credit, double useful_credit, double main_balance, double reward_balance, double total_main, double total_reward, String operator,
			String updatetime, double income, double consume_cardmoney, double deposit, double sales_price, double main_trading, double reward_trading,String payment_name,String card_class_name,String name,String mobil, String strUrlPath, String strUrlContent,String operatedate,String level_name,String original_card);
	
	/**
	 * @param tenantId
	 * @param storeid
	 * @param posNum
	 * @param print_code
	 * @param mode 0 打印菜品，1打印会员服务
	 * @param param
	 * @param printCount
	 * @throws Exception
	 */
	public void customerPrint(String tenantId,int storeid,String posNum,String print_code,String mode,JSONObject param,int printCount)throws Exception;
	
	/**
	 * 获取报表日期
	 * 
	 * @param tenantId
	 * @param storeId
	 * @return
	 * @throws Exception
	 */
	public Date getReportDate(String tenantId, Integer storeId) throws Exception;

	/**
	 * 验证报表日期
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param reportDate
	 * @return
	 * @throws Exception
	 */
	public boolean checkReportDate(String tenantId, Integer storeId, Date reportDate) throws Exception;

	/**
	 * @param reportDate
	 * @param posNum
	 * @param busiType
	 * @param storeId
	 */
	public void checkDayEnd(Date reportDate, String posNum, String busiType, Integer storeId) throws Exception;

	/**
	 * @param reportDate
	 * @param optNum
	 * @param posNum
	 * @param busiType
	 * @param storeId
	 * @param tenantId
	 */
	@Deprecated
	public void checkSign(Date reportDate, String optNum, String posNum, String busiType, Integer storeId, String tenantId) throws Exception;

	/**
	 * 获取班次
	 * 
	 * @param tenantId
	 * @param storeId
	 * @return
	 * @throws Exception
	 */
	public int getShiftId(String tenantId, Integer storeId) throws Exception;

	/** 获取班次
	 * @param tenantId
	 * @param storeId
	 * @param reportDate
	 * @param optNum
	 * @param posNum
	 * @return
	 * @throws Exception
	 */
	public int getShiftId(String tenantId, Integer storeId, Date reportDate, String optNum, String posNum) throws Exception;
	/**
	 * 根据人员ID获取人员姓名
	 * 
	 * @param optNum
	 * @param tenancyId
	 * @param storeId
	 * @return
	 */
	public String getEmpNameById(String optNum, String tenancyId, Integer storeId) throws Exception;

	/**
	 * 根据人员姓名获取人员ID
	 * 
	 * @param name
	 * @param tenancyId
	 * @param storeId
	 * @return
	 */
	public String getEmpIdByName(String name, String tenancyId, Integer storeId) throws Exception;

	/**
	 * @param reportDate
	 * @param optNum
	 * @param organId
	 */
	public void checkKssy(Date reportDate, String optNum, Integer organId,Integer shiftId,String psoNum) throws Exception;

	/**
	 * 查询系统参数
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param para
	 * @return
	 * @throws Exception
	 */
	String getSysParameter(String tenancyId, int storeId, String para) throws Exception;
	
	/** 查询系统参数
	 * @param tenancyId
	 * @param storeId
	 * @param para
	 * @return
	 * @throws Exception
	 */
	public Map<String,String> getSysParameter(String tenancyId, int storeId, String... para) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @return
	 * @throws Exception
	 */
	String getCustomerType(String tenancyId, int storeId) throws Exception;

	@Deprecated
	public int getShiftId();

	@Deprecated
	public SqlRowSet getPosBillItemAmount(String tenantId, Integer organId, String billNum, Integer rwid);

	/**
	 * 新厨打方法 点菜
	 * 
	 * @param tenancyId
	 * @param billno
	 * @param organId
	 * @param printType
	 *            0:厨打，1：账单，2：会员
	 * @return
	 */
	@Deprecated
	public List<Integer> orderChef(String tenantId, String billno, Integer organId, String printType);

	/**
	 * 新厨打打印
	 * 
	 * @param tenancyId
	 * @param billno
	 * @param organId
	 */
	@Deprecated
	public void orderPrint(String tenantId, String billno, Integer organId, List<Integer> list);

	/**
	 * 新厨打退菜，转台用（包含地理打印机）
	 * 
	 * @param tenancyId
	 * @param billno
	 * @param organId
	 * @param oper
	 * @param rwids
	 */
	@Deprecated
	public List<Integer> orderOperChef(String tenantId, String billno, Integer organId, String oper, String operType, String rwids);

	/**
	 * 厨打
	 * 
	 * @param tenancyid
	 *            机构id
	 * @param billno
	 *            账单编号
	 * @param organId
	 *            门店id
	 */
	@Deprecated
	public void chefPrint(String tenantId, String billno, String print_format, Integer organId) throws Exception;
	
	@Deprecated
	public List<Integer> orderRetreat(String tenancyId, String billno, Integer organId, String oper, String operType, String rwids);

	@Deprecated
	public List<Integer> orderSingleChgChef(String tenancyId, String billno, Integer organId, String oper, String operType, String tableTag, String rwids);

	@Deprecated
	public List<Integer> orderWholeChgChef(String tenancyId, String billno, Integer organId, String oper, String operType, String tableTag);

	@Deprecated
	public void sendChef(String tenantId, String billno, Integer organId);

	@Deprecated
	public void operChef(String tenantId, String billno, Integer organId, String oper, String rwids);

	@Deprecated
	public void pushChef(String tenantId, String billno, Integer organId, String oper, String rwids);

	@Deprecated
	public void averageMaling(String billno, Integer organId, double maLingAmount);

	public String getBindOptNum(String tenancyId, Integer storeId, Date reportDate, String deviceNum) throws Exception;
	
	public void checkBindOptnum(String tenancyId, Integer storeId, Date reportDate, String deviceNum) throws Exception;
	
	public boolean copyDataForTable(String fromTableName, String toTableName, JSONObject json,boolean isCopyId) throws Exception;
	
	public boolean copyDataForTable(String fromTableName, String toTableName, JSONObject json,boolean isCopyId,boolean isCopyRwid,boolean isCopyUploadTag) throws Exception;
	/**
	 * @param tenancyId
	 * @param storeId
	 * @return
	 * @throws Exception
	 */
	public JSONObject getOrganById(String tenancyId, Integer storeId) throws Exception;



	/**
	 * 根据id查询付款方式
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param payId
	 * @return
	 * @throws Exception
	 */
	public JSONObject getPaymentWayByID(String tenancyId, int storeId, int payId) throws Exception;

	/**根据id查询付款方式,关联机构
	 * @param tenancyId
	 * @param storeId
	 * @param payId
	 * @return
	 * @throws Exception
	 */
	public JSONObject getPaymentWayForOrganByID(String tenancyId, int storeId, int payId) throws Exception;
	/**
	 * 获取本币付款方式
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @return
	 * @throws Exception
	 */
	public JSONObject getPaymentWayForStandard(String tenancyId, int storeId) throws Exception;
	
	/**根据付款类型查询付款方式,关联机构
	 * @param tenancyId
	 * @param storeId
	 * @param PaymentClass
	 * @return
	 * @throws Exception
	 */
	public JSONObject getPaymentWayByPaymentClass(String tenancyId, int storeId, String paymentClass) throws Exception;
	
	/**根据付款类型查询付款方式,关联机构
	 * @param tenancyId
	 * @param storeId
	 * @param paymentClass
	 * @return
	 * @throws Exception
	 */
	public JSONObject getPaymentWayForOrganByPaymentClass(String tenancyId, int storeId, String paymentClass) throws Exception;
	
	/**
	 * 查询打印模板ID
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param functionId
	 * @return
	 * @throws Exception
	 */
	public List<String> getSysPrintModeList(String tenancyId, int storeId, String functionId, String takeOutOrWeChat) throws Exception;
	
	/**
	 * 操作账单会员库表插入数据
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param report_date
	 * @param billno
	 * @param type
	 * @param cardCode
	 * @param mobil
	 * @param customer_name
	 * @param customer_credit
	 */
	@Deprecated
	public void insertPosBillMember(String tenancyId,int storeId,String billNum,Date reportDate,String type,String customerCode,String cardCode,String mobil,Timestamp lastUpdatetime,String remark,String customer_name,double customer_credit)throws Exception;
	
	@Deprecated
	public void insertPosBillMember(String tenancyId,int storeId,String billNum,Date reportDate,String type,String customerCode,String cardCode,String mobil,Timestamp lastUpdatetime,String remark,String customer_name,double customer_credit,double consume_after_credit,double credit,double amount,String bill_code,String request_state)throws Exception;
	
	/** 修改会员操作金额
	 * @param tenantId
	 * @param storeId
	 * @param reportDate
	 * @param billno
	 * @param type
	 * @param cardCode
	 * @param mobil
	 */
	@Deprecated
	public void updatePosBillMember(String tenantId, int storeId, String billno, String type, double amount,double credit,String billCode,String requestState,String customerName,Double consumeBeforeCredit,Double consumeAfterCredit)throws Exception;

	/** 修改会员操作金额
	 * @param tenantId
	 * @param storeId
	 * @param reportDate
	 * @param billno
	 * @param type
	 * @param cardCode
	 * @param mobil
	 */
	@Deprecated
	public void updatePosBillMemberRegain(String tenantId, int storeId, String billno, String type, double amount,double credit,String billCode,String requestState,String customerName,Double consumeBeforeCredit,Double consumeAfterCredit,Integer regainCount)throws Exception;

	/** 删除账单会员操作记录
	 * @param tenantId
	 * @param storeId
	 * @param billno
	 * @param type
	 * @throws Exception
	 */
	@Deprecated
	public void deletePosBillMember(String tenantId, int storeId, String billno, String type)throws Exception;
	
	/** 查询
	 * @param tenantId
	 * @param storeId
	 * @param billno
	 * @param type 若为空,则查询所有
	 * @return
	 * @throws Exception
	 */
	@Deprecated
	public List<JSONObject> queryPosBillMember(String tenantId, int storeId, String billno, String type) throws Exception;
	
	/** 根据账单编号获取账单信息
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @return
	 * @throws Exception
	 */
	public JSONObject getPosBillByBillnum(String tenancyId,int storeId,String billNum) throws Exception;


	/** 根据原账单编号获取账单信息
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @return
	 * @throws Exception
	 */
	public JSONObject getPosBillBySBillnum(String tenancyId,int storeId,String billNum) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param para
	 * @return
	 * @throws Exception
	 */
	public JSONObject getInvoiceInfo(String tenancyId, int storeId, JSONObject para) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param para
	 * @throws Exception
	 */
	public void insertToInvoice (String tenancyId, int storeId, JSONObject para) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param para
	 * @return
	 * @throws Exception
	 */
	public void deleteInvoiceFromInvoice(String tenancyId, int storeId, JSONObject para) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param invoiceId
	 * @throws Exception
	 */
	public void deleteInvoiceByInvoiceId(String tenancyId, int storeId,String billnum , Integer invoiceId) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param para
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getInvoicePaymentRetreat(String tenancyId, int storeId, JSONObject para) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param para
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getInvoicePayment(String tenancyId, int storeId, JSONObject para) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param para
	 * @return
	 * @throws Exception
	 */
	public JSONObject getLegalPerInfo(String tenancyId, int storeId, JSONObject para) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param para
	 * @return
	 * @throws Exception
	 */
	public void updateInvoice(String tenancyId, int storeId, JSONObject para) throws Exception;
	
	/**
	 * 更新套餐明细单价
	 * @param billNum
	 * @param itemProperty
	 * @param defaultScale
	 * @param count
	 * @param storeId
	 * @param tenancyId
	 * @throws Exception
	 */
	public void updateMeallistItemPrice(String billNum, String itemProperty, int defaultScale, int count, int storeId, String tenancyId) throws Exception;

	}
