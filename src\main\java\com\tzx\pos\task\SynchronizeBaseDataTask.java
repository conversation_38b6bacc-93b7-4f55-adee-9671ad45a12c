package com.tzx.pos.task;

import com.tzx.pos.base.service.SynchronizeBaseDataService;
import com.tzx.pos.base.service.impl.SynchronizeBaseDataServiceImp;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR> on 2018-07-16
 */
@EnableScheduling
@Component
public class SynchronizeBaseDataTask implements IScheduleder {

    private Logger logger=LoggerFactory.getLogger(SynchronizeBaseDataTask.class);
    private final long period=1000*60*60*24;//24h

    @Value("${saas.url}")
    private String saasUrl;

    @Value("${auto.synchronize.onStart}")
    private String autoSynchronize;

    @Scheduled(initialDelay = 1000 * 5 ,fixedDelay =period )
    @Override
    public void scheduledExecute() {
        new SynchronizeBaseDataServiceImp().syncData(SynchronizeBaseDataService.Type.organ,saasUrl,autoSynchronize);
    }
}
