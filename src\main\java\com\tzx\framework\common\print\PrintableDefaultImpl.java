package com.tzx.framework.common.print;

import java.awt.Font;
import java.awt.FontMetrics;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.print.PageFormat;
import java.awt.print.Printable;
import java.awt.print.PrinterException;

class PrintableDefaultImpl implements Printable
{

	public int print(Graphics graphics2d, PageFormat pageformat, int page) throws PrinterException
	{
		// 当打印页号大于需要打印的总页数时，打印工作结束
		if (page != 0)
		{
			return Printable.NO_SUCH_PAGE;
		}

		Graphics2D g2d = (Graphics2D) graphics2d;
		g2d.translate(pageformat.getImageableX(), pageformat.getImageableY());// 转换坐标，确定打印边界
		FontMetrics fontMetrics = null;
		Font font = null;

		for (int i = 8; i < 70; i++)
		{
			font = new Font("宋体", Font.PLAIN, i);
			g2d.setFont(font);
			fontMetrics = g2d.getFontMetrics();
			FontHeightUtil.getFontHeightCache().put(new Float(i), fontMetrics.getHeight());
		}

		return Printable.PAGE_EXISTS; // 存在打印页时，继续打印工作
	}

}