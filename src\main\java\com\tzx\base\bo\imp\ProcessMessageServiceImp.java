package com.tzx.base.bo.imp;

import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;

import javax.annotation.Resource;

import com.tzx.base.cache.SoftCache;
import com.tzx.clientorder.wlifeprogram.bo.impl.BasicServiceImp;
import net.sf.json.JSON;
import org.apache.log4j.Logger;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import com.tzx.base.bo.CacheManagerService;
import com.tzx.base.bo.PosCodeService;
import com.tzx.base.bo.ProcessMessageService;
import com.tzx.base.cache.CacheManager;
import com.tzx.base.common.util.PosPropertyUtil;
import com.tzx.base.po.springjdbc.dao.ProcessMessageDao;
import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.exception.HttpRestException;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.Comet4jUtil;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.DoubleHelper;
import com.tzx.framework.common.util.GsonUtil;
import com.tzx.framework.common.util.HttpUtil;
import com.tzx.framework.common.util.JsonUtils;
import com.tzx.framework.common.util.Scm;
import com.tzx.framework.common.util.Tools;
import com.tzx.orders.base.util.OrderUtil;
import com.tzx.orders.bo.OrdersManagementService;
import com.tzx.orders.bo.dto.OrderState;
import com.tzx.orders.bo.dto.PosBill;
import com.tzx.orders.bo.dto.PosBillItem;
import com.tzx.orders.bo.imp.OrderSyncServiceImp;
import com.tzx.orders.po.springjdbc.dao.OrdersManagementDao;
import com.tzx.pos.base.constant.FunctionCode;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.constant.XmdCrmParameterEnum;
import com.tzx.pos.base.controller.CometDataNoticeClientRunnable;
import com.tzx.pos.base.util.JsonUtil;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.base.util.SendUdpToPos;
import com.tzx.pos.bo.PaymentService;
import com.tzx.pos.bo.PosDishService;
import com.tzx.pos.bo.PosPrintNewService;
import com.tzx.pos.bo.PosPrintService;
import com.tzx.pos.bo.PosService;
import com.tzx.pos.bo.dto.PosBillMembers;
import com.tzx.pos.po.springjdbc.dao.PosBillMemberDao;
import com.tzx.pos.po.springjdbc.dao.PosDao;
import com.tzx.pos.po.springjdbc.dao.PosDishDao;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import static com.tzx.orders.base.constant.Constant.*;
import static com.tzx.orders.base.constant.Constant.ORDER_STATE_COMPLETE;

/**
 * 
 * <AUTHOR> 2015年8月3日-上午10:21:53
 */
@Service(ProcessMessageService.NAME)
public class ProcessMessageServiceImp implements ProcessMessageService {
	private static final Logger logger = Logger
			.getLogger(ProcessMessageServiceImp.class);

	@Resource(name = ProcessMessageDao.NAME)
	private ProcessMessageDao processMessageDao;

	@Resource(name = PosDao.NAME)
	private PosDao posDao;

	@Resource(name = OrdersManagementDao.NAME)
	private OrdersManagementDao ordersDao;

	// @Resource(name = "genericDaoImpl")
	// private GenericDao dao;

	// @Resource(name = PosService.NAME)
	// private PosService posService;

	@Resource(name = PosDishService.NAME)
	private PosDishService posDishService;

	@Resource(name = PaymentService.NAME)
	private PaymentService paymentService;

	@Resource(name = PosPrintService.NAME)
	private PosPrintService posPrintService;

	@Resource(name = PosCodeService.NAME)
	private PosCodeService codeService;

	@Resource(name = OrdersManagementService.NAME)
	private OrdersManagementService ordersManagementService;

	@Resource(name = "transactionManager")
	private DataSourceTransactionManager transactionManager;

	@Resource(name = CacheManagerService.NAME)
	private CacheManagerService cacheManagerService;

	@Resource(name = PosDishDao.NAME)
	private PosDishDao posDishDao;
	
    @Resource(name = PosBillMemberDao.NAME)
	private PosBillMemberDao			memberDao;

	@Resource(name = PosPrintNewService.NAME)
	private PosPrintNewService posPrintNewService;
	
	@Resource(name = PosService.NAME)
	private PosService posService;

	@Override
	@Deprecated
	public boolean batchInsert(String[] data) {
		try {
			int[] result = processMessageDao.batchInsert(data);
			if (result.length > 0) {
				return true;
			}
		} catch (Exception e) {
			logger.info("下发失败，原因：" + ExceptionMessage.getExceptionMessage(e));
			return false;
		}
		return false;
	}

	@Override
	public boolean updateBasicVersion(String tenancyId, int storeId) throws Exception
	{
		this.setXMDCRMConfigToSysParameter(tenancyId, storeId);

		processMessageDao.updateEmployeeForStoreId(tenancyId, storeId);
		processMessageDao.updateUserForStoreId(tenancyId, storeId);

		// 下面要记录版本到pos_data_version表里，用于版本控制
		posService.buildBasicVersion(tenancyId, storeId);
		posDao.updatePosTableState(tenancyId, storeId);
		// 其它渠道分配套餐明细
		posDao.otherChanelAddMeallist(tenancyId, storeId);
		// 同步物理打印机（新打印）
		posDao.synzPrinter(tenancyId, storeId);
		// 更新缓存
		cacheManagerService.loadBusinessData(tenancyId, storeId);
		return true;
	}

	@Override
	public void updateDataVersion(String tenancyId, int storeId, String state)
			throws Exception {
		if ("1".equals(state)) {
			JSONObject para = posDao.getDataVersionByCode(tenancyId, storeId,
					"INIT_BASIC_DATA_STATE");

			if (null != para && para.containsKey("id") && para.getInt("id") > 0) {
				posDao.updateDataVersionValueByCode(tenancyId, storeId,
						"INIT_BASIC_DATA_STATE", "1");
			} else {
				posDao.insertDataVersion(tenancyId, storeId, "POS", "系统参数",
						"初始化下发状态", "INIT_BASIC_DATA_STATE", "1", "0", "常规",
						"1", null, null);
			}
			Constant.INIT_BASIC_DATA_STATE = "1";
		} else {
			posDao.updateDataVersionValueByCode(tenancyId, storeId,
					"INIT_BASIC_DATA_STATE", "0");
			Constant.INIT_BASIC_DATA_STATE = "0";
		}
	}

	@Override
    public boolean initBasicData(String tenancyId, int storeId, JSONObject param) throws Exception {

        String[] sqls = GsonUtil.toT(param.opt("data").toString(), String[].class);

        processMessageDao.batchInsert(sqls);

//        int capacity = 128;
//        int count = (sqls.length % capacity == 0) ? (sqls.length / capacity) : (sqls.length / capacity + 1);
//        String[] exeSql;
//
//        Exception exception=null;
//
//        for (int i = 1; i <= count; i++) {
//            int point = i * capacity;
//            if (point >= sqls.length) {
//                exeSql = Arrays.copyOfRange(sqls, (i - 1) * capacity, sqls.length);
//            } else {
//                exeSql = Arrays.copyOfRange(sqls, (i - 1) * capacity, point);
//            }
//            TransactionStatus status = getTransctionStatus(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
//            try {
//                processMessageDao.batchInsert(exeSql);
//                transactionManager.commit(status);
//            } catch (Exception e) {
//                transactionManager.rollback(status);
//                exception=e;
//                break;
//            }
//        }
//
//        if (null==exception) {
//            this.setXMDCRMConfigToSysParameter(tenancyId, storeId);
//
//            processMessageDao.updateEmployeeForStoreId(tenancyId,
//                    storeId);
//            processMessageDao.updateUserForStoreId(tenancyId, storeId);
//
//            // 下面要记录版本到pos_data_version表里，用于版本控制
//            posDao.buildBasicVersion(tenancyId, storeId);
//            posDao.updatePosTableState(tenancyId, storeId);
//            // 其它渠道分配套餐明细
//            posDao.otherChanelAddMeallist(tenancyId, storeId);
//            // 同步物理打印机（新打印）
//            posDao.synzPrinter(tenancyId, storeId);
//            //更新缓存
//            cacheManagerService.loadBusinessData(tenancyId, storeId);
        
        this.updateBasicVersion(tenancyId, storeId);
//        } else {
//            throw exception;
//        }
        return true;
	}

	/**
	 * 将新美大CRM对接相关参数配置写入系统参数表
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @throws Exception
	 */
	public void setXMDCRMConfigToSysParameter(String tenancyId, int storeId)
			throws Exception {

		List<Map<String, Object>> parameterList = new ArrayList<Map<String, Object>>();

//		Iterator<Map.Entry<String, String>> entries = com.tzx.pos.base.Constant.XMD_CRM_PARAMETER_KEYS.entrySet()
//				.iterator();
//		while (entries.hasNext()) {
//			Map.Entry<String, String> entry = entries.next();
//
//			Map<String, Object> map = new HashMap<String, Object>();
//			map.put("tenancy_id", tenancyId);
//			map.put("store_id", storeId);
//			map.put("system_name", "POS");
//			map.put("model_name", "系统参数");
//			map.put("para_name", com.tzx.pos.base.Constant.XMD_CRM_PARAMETER_NAME.get(entry.getKey()));
//			map.put("para_code", entry.getKey());
//			map.put("para_value", PosPropertyUtil.getMsg(entry.getValue()));
//			map.put("para_defaut", null);
//			map.put("para_type", "初始化");
//			map.put("valid_state", "1");
//			map.put("values_name", null);
//			map.put("para_remark", null);
//			parameterList.add(map);
//		}
		
		for(XmdCrmParameterEnum para: XmdCrmParameterEnum.values())
		{
			Map<String, Object> map = new HashMap<String, Object>();
			map.put("tenancy_id", tenancyId);
			map.put("store_id", storeId);
			map.put("system_name", "POS");
			map.put("model_name", "系统参数");
			map.put("para_name", para.getName());
			map.put("para_code", para.getCode());
			map.put("para_value", PosPropertyUtil.getMsg(para.getVaue()));
			map.put("para_defaut", null);
			map.put("para_type", "初始化");
			map.put("valid_state", "1");
			map.put("values_name", null);
			map.put("para_remark", null);
			parameterList.add(map);
		}

		processMessageDao.insertSysParameter(tenancyId, storeId, parameterList);
	}

	@Override
	public boolean updateProductPhoto(String tenancyId, int organId)
			throws Exception {
		String[] columns = { "photo3", "photo4", "photo5", "photo6" };

//		String itemImagePath = System.getProperty("contextPath")
//				+ Constant.ITEM_PHOTO_PATH;
		
		String itemImagePath = Constant.CONTEXT_PATH + Constant.ITEM_PHOTO_PATH;

		// 查询图片服务器地址
		// String imgServiceUrl =
		// processMessageDao.getSysParameter(tenancyId,organId,"IMGSERVICEURL");
		// String imgServiceUrl = Constant.getSystemMap().get("upload_img_ip");
		// 查询菜品图片
		List<JSONObject> photoList = processMessageDao.getItemPhoto(tenancyId);
		Iterator<JSONObject> it = photoList.iterator();
		while (it.hasNext()) {
			JSONObject photoJson = it.next();
			for (String column : columns) {
				if (photoJson.containsKey(column)
						&& !"".equals(photoJson.optString(column))) {
					// String fileName =
					// photoJson.optString(column).replace("\\", "/");
					// String filePath = itemImagePath +
					// fileName.substring(fileName.lastIndexOf("/") + 1);

					URL url = new URL(photoJson.optString(column));
					String filePath = itemImagePath + url.getPath();

					File photFile = new File(filePath);
					if (!photFile.exists()) {
						if (!photFile.getParentFile().exists()) {
							photFile.getParentFile().mkdirs();
						}
						DataInputStream in = null;
						DataOutputStream out = null;
						try {
							// URL url = new URL(photoJson.optString(column));
							// URLConnection conn = url.openConnection();
							HttpURLConnection conn = (HttpURLConnection) url
									.openConnection();
							conn.connect();
							if (conn.getResponseCode() == 200) {
								in = new DataInputStream(conn.getInputStream());
								out = new DataOutputStream(
										new FileOutputStream(filePath));

								byte[] buffer = new byte[4096];
								int count = 0;
								while ((count = in.read(buffer)) > 0) {
									out.write(buffer, 0, count);
								}
							}
						} finally {
							try {
								if (in != null) {
									in.close();
								}
								if (out != null) {
									out.close();
								}
							} catch (IOException e) {
								e.printStackTrace();
							}
						}
					}
				}
			}
		}
		return true;
	}

	/**
	 * 外卖接单方法
	 * @param tenancyId
	 * @param storeId
	 * @param data
	 * @throws Exception
	 * <AUTHOR>
	 */
	@Override
	public  void addOrders(String tenancyId, int storeId, JSONObject data)
			throws Exception {
		logger.info("接收订单addOrders方法 data 参数：：：： "+data);
		if(Tools.judgeStoreStateOpen(tenancyId,storeId) == false){
			logger.info("非营业时段不处理外卖订单接收消息，消息内容："+data.toString());
			return; 
		}
		
        JSONArray dataList = data.optJSONArray("data");
        boolean isAfterChangeShift=data.optBoolean("is_after_change_shift");
        for (Object obj : dataList) {
            List<OrderState> sendList = new ArrayList<OrderState>();

            JSONObject json = JSONObject.fromObject(obj);
            JSONObject orderJson = json.optJSONObject("order_list");
            // 查询订单是否是新订单
            String orderCode = orderJson.optString("order_code").intern();
			//下单时间
			String single_time = orderJson.optString("single_time");
			//订单状态
			String currentOrderState=orderJson.optString("order_state");
			//送达时间
			String send_time = orderJson.optString("send_time");
			//订单渠道
            String chanel = orderJson.optString("chanel");
//            if(currentOrderState.equals("12")){
//            	currentOrderState =com.tzx.orders.base.constant.Constant.ORDER_STATE_NEW;
//            }
            //账单编号
            String billNum=orderJson.optString("bill_num");
            //第三方订单编号
            String thirdOrderCode=orderJson.optString("third_order_code");
          //业务报表数据
            JSONObject para = ordersManagementService.getPosOptState(tenancyId, storeId,isAfterChangeShift);
			if(!THIRD_CHANNEL.contains(chanel)){
				para = ordersManagementService.getPosOptState4WX(tenancyId, storeId);
			}
            para.put("order_code", orderCode);
            para.put("channel", chanel);
            para.put("single_time", single_time);
            para.put("bill_num", billNum);
            para.put("third_order_code",thirdOrderCode);
            if(orderJson.containsKey("oper")){
                para.put("oper",orderJson.optString("oper"));
            }

            OrderState orderState = ordersDao.getOrderStateByOrderCode(tenancyId, storeId, orderCode);

            // 无:插入订单
            if (null == orderState) {

				String upload_tag="0";
				if(!"".equals(billNum) && !"null".equals(billNum) && !orderJson.containsKey("oper")){
					upload_tag ="1";
				}
                if(com.tzx.orders.base.constant.Constant.ORDER_STATE_NEW.equals(currentOrderState)){
                    currentOrderState=com.tzx.orders.base.constant.Constant.ORDER_STATE_TOSHOP;
                }
				if("yes".equals(ordersManagementService.getConfig().optString( "auto_take_order"))){//设置是自动接单
					orderJson.put("is_taking_order","taking");
				}
                orderJson.put("order_state",currentOrderState);
                orderJson.put("receive_time", DateUtil.getNowDateYYDDMMHHMMSS());
                orderJson.put("report_date",para.optString("report_date"));
                orderJson.put("upload_tag", upload_tag);
				if(!send_time.equals("")){
					if(!send_time.equals("立即配送") && send_time.contains("T")){
						send_time = send_time.replace("T"," ");
					}
				}
				orderJson.put("send_time",send_time);
                json.put("order_list", orderJson);

                if (json.containsKey("order_item") && !json.optJSONArray("order_item").isEmpty()) {
                    List<JSONObject> items = json.optJSONArray("order_item");
                    List<JSONObject> itemArray = new ArrayList<JSONObject>();
                    for (JSONObject item : items) {
                        item.put("report_date", para.optString("report_date"));
                        item.put("upload_tag", upload_tag);
                        itemArray.add(item);
                    }
                    json.put("order_item", itemArray);
                }


                if (json.containsKey("order_repayment") && !json.optJSONArray("order_repayment").isEmpty()) {

                    List<JSONObject> payments = json.optJSONArray("order_repayment");
                    List<JSONObject> paymentArray = new ArrayList<JSONObject>();
                    for (JSONObject payment : payments) {
                        payment.put("report_date", para.optString("report_date"));
                        payment.put("upload_tag", upload_tag);
                        paymentArray.add(payment);
                    }
                    json.put("order_repayment", paymentArray);
                }

                TransactionStatus status = null;
                try {
                    status = getTransctionStatus(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
                    ordersDao.addOrderInfo(tenancyId, storeId, json);
                    transactionManager.commit(status);
                } catch (Exception e1) {
                    transactionManager.rollback(status);
                    throw e1;
                }

                orderState = OrderState.get(orderJson);
                orderState.setChanel(chanel);

				//更新总部状态
				JSONObject result = null;
				if(com.tzx.orders.base.constant.Constant.ORDER_STATE_TOSHOP.equals(currentOrderState)){
					sendList.add(orderState);
					result = OrderUtil.sendOrderState(tenancyId, storeId, Type.ORDER,Oper.update, sendList);
				}

				if(result!=null && !result.isEmpty() && result.containsKey("msg") && result.get("msg").toString().contains("已取消订单")){
					orderState.setOrder_state(com.tzx.orders.base.constant.Constant.ORDER_STATE_CANCEL);
					orderState.setPayment_state(PAYMENT_STATE_REFUND);
					orderState.setReceive_time_cancellation(DateUtil.getNowDateYYDDMMHHMMSS());
					ordersDao.updateOrderInfo(tenancyId, storeId, JSONObject.fromObject(orderState));
					return;
				}


				if(!"".equals(billNum) && !"null".equals(billNum)  && !orderJson.containsKey("oper")){//修改订单
					orderState.setBill_num(billNum);
					orderState.setSingle_time(single_time);
					orderState.setActual_pay(orderJson.optDouble("actual_pay",0));
					if(result!=null && result.containsKey("msg")){
						if(result.get("msg").toString().contains("已取消订单")){
							orderState.setOrder_state(com.tzx.orders.base.constant.Constant.ORDER_STATE_CANCEL);
							orderState.setPayment_state(PAYMENT_STATE_REFUND);
							orderState.setReceive_time_cancellation(DateUtil.getNowDateYYDDMMHHMMSS());
							ordersDao.updateOrderInfo(tenancyId, storeId, JSONObject.fromObject(orderState));
						}else{
							//发送接收订单状态
							OrderUtil.changeOrderState(tenancyId,storeId,orderState,ORDER_STATE_RECEIVE,NOTIFY_NONE);
							orderState.setPayment_state(com.tzx.orders.base.constant.Constant.PAYMENT_STATE_COMPLETE);
							//更新订单状态
							OrderUtil.changeOrderState(tenancyId, storeId, orderState, ORDER_STATE_COMPLETE, NOTIFY_NONE);
						}
					}
					//根据订单号查询账单数据 删除账单数据
//					JSONObject bill = ordersDao.getBillByOrderCode(tenancyId, storeId, orderCode);
//					if (bill != null && (!bill.isEmpty())) {
//						// 删除账单信息
//						ordersDao.deletePosBillByBillNum(tenancyId, storeId, bill);
//					}
				}else {//总部不存在账单
					// 发送消息
					String printTime = "10";//默认外卖打印范围时间内
					if(PosPropertyUtil.getMsg("waimai.print.time")!=null && !"".equals(PosPropertyUtil.getMsg("waimai.print.time"))){
						printTime = PosPropertyUtil.getMsg("waimai.print.time");
					}
					Long currentTime =DateUtil.getWebsitCurrentTimeMillis();
					Date singleTime = DateUtil.parseDateAll(single_time);
					List<String> msgList = new ArrayList<String>();
					msgList.add(orderCode);
					JSONObject msg = new JSONObject();
					msg.put("order_state", currentOrderState);
					msg.put("order_code", msgList);
					// msg.put("chanel", orderJson.optString("chanel"));
					// msg.put("is_online_payment",
					// orderJson.optString("is_online_payment"));
					// Comet4jUtil.sendMessage2All(Comet4jUtil.CALLCENTER_ORDER,
					// msg.toString());

					// 响铃提醒
					List<JSONObject> cometList = new ArrayList<JSONObject>();
					cometList.add(msg);
					Data cometData = new Data();
					cometData.setTenancy_id(tenancyId);
					cometData.setStore_id(storeId);
					cometData.setType(Type.ORDER);
					cometData.setOper(Oper.add);
					cometData.setSuccess(true);
					cometData.setData(cometList);
			    /*Comet4jUtil.sendMessage2All(Comet4jUtil.POS_PC, JSONObject
					.fromObject(cometData).toString());*/

					//POS用到UDP
					//下单时间与当前时间10分钟之内
					if((currentTime-singleTime.getTime())<1000*60*Integer.parseInt(printTime)){
						String port = new CometDataNoticeClientRunnable(cometData).getPort();
						SendUdpToPos.sendMessagePos(JSONObject.fromObject(cometData).toString(), port);
						logger.debug("----推送消息：" + JSONObject.fromObject(cometData).toString() + ", 端口号：" + port);
					}
					// 自动取单
					logger.info("自动取单设置  ：：：：： "+ordersManagementService.getConfig().optString( "auto_take_order")+"  是否包含add_modify oper "+orderJson.containsKey("oper"));
					if (com.tzx.orders.base.constant.Constant.ORDER_STATE_TOSHOP.equals(currentOrderState)
							&& ("yes".equals(ordersManagementService.getConfig().optString("auto_take_order")) ||  orderJson.containsKey("oper"))//菜品映射修复完成自动转账单
							&& ("1".equals(orderJson.optString("is_online_payment"))// 先付
							|| "CC04".equals(chanel)// 电话外卖
							|| ("WX02".equals(chanel) && "1".equals(OrderUtil.getSysPara(tenancyId, storeId, "wechat_postpay_autotake")))// 微信店内点餐且设置后付支持自动取单
					)) {

						TransactionStatus takeTs = getTransctionStatus(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
						JSONObject printBuffer = null;
						try {
							//logger.info("订单转账单模式:" + ("1".equals(OrderUtil.getSysPara(tenancyId, storeId, "order_translate_bill_mode")) ? "直接转存(beta)" : "调用下单接口"));
							//第三方  订单转账单
							if (THIRD_CHANNEL.contains(chanel) ) {//&& "1".equals(OrderUtil.getSysPara(tenancyId, storeId, "order_translate_bill_mode"))
								printBuffer = ordersManagementService.createPosBillByOrder(tenancyId, storeId, para);
							} else {
								printBuffer = ordersManagementService.takeOrders(
										tenancyId, storeId, para);
							}

							transactionManager.commit(takeTs);
						} catch (SystemException e) {
							logger.error("自动取单失败!", e);
							transactionManager.rollback(takeTs);
							if (e.getErrorCode() != PosErrorCode.POS_CASHIER_NOT_SIGNIN_ERROR && e.getErrorCode() != PosErrorCode.GREAT_THAN_SOLD_OUT_COUNT) {
								//异常订单取单
								printBuffer = ordersManagementService.takeExceptionOrders(tenancyId, storeId, para, null);
							}

							//正在取单的订单标识置空
							ordersManagementService.settingTakeOrderFlag(tenancyId, storeId,orderCode);
						} catch (Exception e) {
							logger.error("自动取单失败!", e);
							transactionManager.rollback(takeTs);
							//异常订单取单
							printBuffer = ordersManagementService.takeExceptionOrders(tenancyId, storeId, para, null);
							//正在取单的订单标识置空
							ordersManagementService.settingTakeOrderFlag(tenancyId, storeId,orderCode);
						}

						Boolean isPrint = true;
						Boolean print_remind = false; //是否需要修改提醒标识

						if(THIRD_CHANNEL.contains(chanel)
								&& orderState.getSend_time()!=null
								&& !"null".equals(orderState.getSend_time())
								&& !orderState.getSend_time().trim().equals("立即配送")){//送达时间不等于立即配送的订单为预订单
							String paraValue = OrderUtil.getSysPara(tenancyId, storeId, "waimai_bookorder_switch"); //查询提醒参数
							if (Tools.hv(paraValue))
							{
								String[] paras = paraValue.split(";");
								String printType = paras[2];//提醒时打印
								Date sendTime = DateUtil.parseDateAll(orderState.getSend_time());
								if(sendTime.getTime()>currentTime){//送达时间是否大于当前时间
									if(paras[0].equals("1")){
										print_remind = true;
										if(30<((sendTime.getTime()-currentTime)/60000)){
											if(printType.equals("2")){//提醒时打印
												isPrint  = false;
												print_remind = false;
											}else if(printType.equals("3")){//接单提醒时打印
												print_remind = false;
											}
										}
									}
								}
							}else{
								logger.info("未设置、下发预订单提醒参数");
							}
						}

						if (null != printBuffer && isPrint) {
							// 下单时间与当前时间10分钟之内进行打印
							if((currentTime-singleTime.getTime())<1000*60*Integer.parseInt(printTime)){
								ordersManagementService.printOrderBuffer(tenancyId,storeId, printBuffer);
								if(print_remind && orderCode!=null && !"".equals(orderCode) ){
									logger.info("送达时间与当前时间小于30分钟不需要提醒的订单号：：： "+orderCode);
									ordersDao.updateOrderPrintRemind(tenancyId, storeId,orderCode);//修改已经提示  ： 只提醒一次
								}
							}
						}
						//正在取单的订单标识置空
						ordersManagementService.settingTakeOrderFlag(tenancyId, storeId,orderCode);
					} else {
						Comet4jUtil.sendMessage2All(Comet4jUtil.CALLCENTER_ORDER,msg.toString());
					}
				}
            }else{
            	logger.info("订单已存在:"+orderState.getOrder_code());
            }
        }

    }
	
	private void addDish(String tenancyId, int storeId, JSONObject orderJson,
			List<PosBillItem> orderItem) throws Exception {
		// 获取订单信息
		String orderCode = orderJson.optString("order_code");
		String sql = "SELECT\n" + "	a.bill_num, a.report_date,\n"
				+ "	shift_id,\n" + "	item_menu_id,\n" + "	open_opt opt_num,\n"
				+ "	waiter_num,\n" + "	pos_num\n" + "FROM\n" + "	pos_bill A,\n"
				+ "	cc_order_list b\n" + "WHERE\n"
				+ "	A .order_num = b.order_code\n" + "AND A .order_num = '"
				+ orderCode + "';";
		JSONObject para = ordersDao.query4Json(tenancyId, sql).get(0);
		PosBill order = ordersDao.getOrderByOrdercode(tenancyId, storeId,
				orderCode);
		order.setBill_num(para.optString("bill_num"));
		order.setIsprint("Y");
		order.setReport_date(para.optString("report_date"));
		order.setShift_id(para.optString("shift_id"));
		order.setItem_menu_id(para.optString("item_menu_id"));
		order.setOpt_num(para.optString("opt_num"));
		order.setWaiter_num(para.optString("waiter_num"));
		order.setPos_num(para.optString("pos_num"));
		
		//订单渠道
        String channel = order.getSource();
		
		String discount_mode_id = order.getDiscount_mode_id(); // 折扣方式
        String consigner_phone = order.getConsigner_phone(); // 会员手机号
        String customer_id = order.getCustomer_id(); // 会员卡号
        /**
         *  微信渠道下单，并且折扣方式=6时，判断会员手机号和卡号要不为空
         *  不为空时，在下单时判断是否要绑定会员关系pos_bill_member
         */
        if(com.tzx.orders.base.constant.Constant.WX02_CHANNEL.equals(channel) && discount_mode_id != null && String.valueOf(SysDictionary.DISCOUNT_MODE_6).equals(discount_mode_id)){
        	if(Tools.isNullOrEmpty(consigner_phone) || Tools.isNullOrEmpty(customer_id)){
        		throw SystemException.getInstance(PosErrorCode.BILL_MEMBERCARD_NULL);
        	}else{
        		// 判断是否绑定会员信息
        		List<PosBillMembers> memberList = memberDao.selectMembersByBillNum(tenancyId, storeId, order.getBill_num());
        		if(memberList == null || memberList.isEmpty()){
        			// 绑定会员折扣信息
                	JSONObject memberJson = new JSONObject();
                	memberJson.put("report_date", order.getReport_date());
                	memberJson.put("bill_num", order.getBill_num());
                	memberJson.put("mobil", order.getConsigner_phone());
                	memberJson.put("card_code", order.getCustomer_id());
                	memberJson.put("customer_code", null);
                	
                	logger.info(">>>>>>>>>绑定会员折扣信息，会员参数："+memberJson.toString());
                	memberDao.insertPosBillMember(tenancyId, storeId, order.getBill_num(), DateUtil.parseDate(order.getReport_date()), SysDictionary.BILL_MEMBERCARD_HYJ01, null, order.getCustomer_id(), order.getConsigner_phone(),DateUtil.currentTimestamp(),null,null,0d);
        		}
        	}
        }
		
		/*
		 * // 获得计算用的小数点位数 String cmj = new String(
		 * "select para_code,para_value from sys_parameter where para_code ='CMJEWS' or para_code = 'ZDJEWS'"
		 * ); SqlRowSet srs = ordersDao.query(tenancyId, cmj); while
		 * (srs.next()) {
		 * com.tzx.pos.base.Constant.constantMap.put(srs.getString("para_code"),
		 * srs.getString("para_value")); }
		 * 
		 * for (PosBillItem item : orderItem) { if
		 * (Tools.isNullOrEmpty(item.getItem_id())) { throw
		 * SystemException.getInstance(PosErrorCode.NOT_NULL_ITEM_ID); } if
		 * (Tools.isNullOrEmpty(item.getUnit_id())) { throw
		 * SystemException.getInstance(PosErrorCode.NOT_NULL_DISH_UNITID); } if
		 * (Tools.isNullOrEmpty(item.getItem_count())) { throw
		 * SystemException.getInstance(PosErrorCode.NOT_NULL_DISH_ITEM_COUNT); }
		 * if (Tools.isNullOrEmpty(item.getItem_property())) { throw
		 * SystemException.getInstance(PosErrorCode.NOT_NULL_ITEM_PROPERTY); }
		 * if (Tools.isNullOrEmpty(item.getDetails_id())) { throw
		 * SystemException.getInstance(PosErrorCode.NOT_EXISTS_DISH_MENU); } if
		 * (Tools.isNullOrEmpty(item.getItem_num())) { throw
		 * SystemException.getInstance(PosErrorCode.NOT_EXISTS_DISH_ITEM); } if
		 * (Tools.isNullOrEmpty(item.getItem_unit_name())) { throw
		 * SystemException.getInstance(PosErrorCode.NOT_EXISTS_DISH_UNIT); } }
		 */

		order.setItem(orderItem);
		// 下单
		JSONObject orderDishJson = new JSONObject();

		orderDishJson.put("mode", "0");
		orderDishJson.put("isprint", order.getIsprint());
		orderDishJson.put("report_date", order.getReport_date());
		orderDishJson.put("shift_id", order.getShift_id());
		orderDishJson.put("pos_num", order.getPos_num());
		orderDishJson.put("opt_num", order.getOpt_num());
		orderDishJson.put("bill_num", order.getBill_num());
		orderDishJson.put("sale_mode", order.getSale_mode());
		orderDishJson.put("table_code", order.getTable_code());
		orderDishJson.put("waiter_num", order.getWaiter_num());
		orderDishJson.put("bill_taste", order.getRemark());// 整单备注
		orderDishJson.put("item", order.getItem());

		List<JSONObject> orderDishList = new ArrayList<JSONObject>();
		orderDishList.add(orderDishJson);

		Data orderDishData = Data.get();
		orderDishData.setTenancy_id(tenancyId);
		orderDishData.setStore_id(storeId);
		orderDishData.setType(Type.ORDERING);
		orderDishData.setData(orderDishList);

		JSONObject printJson = new JSONObject();
		posDishService.newOrderDish(orderDishData, printJson);

		JSONObject printBuffer = new JSONObject();
		
    	//add by shenzhanyu 根据渠道区分第三方外卖下单（order_type ：WM1205） 、微信下单（order_type ：WX1401）
		//标记第三方外卖
		if(THIRD_CHANNEL.contains(channel)){
			printJson.put("order_type", "WM1205");
		}else{
			//标记微信订单
			printJson.put("order_type", "WX1401");
		}
		// 缓冲厨打
		printBuffer.put("chefPrint", printJson);

		// 打印通用参数
		JSONObject orderJSON = new JSONObject();
		orderJSON.put("report_date", order.getReport_date());
		orderJSON.put("shift_id", Integer.parseInt(order.getShift_id()));
		orderJSON.put("pos_num", order.getPos_num());
		orderJSON.put("opt_num", order.getOpt_num());
		orderJSON.put("bill_num", order.getBill_num());
		orderJSON.put("mode", "0");
		// 打印电子发票参数
		orderJSON.put("order_num", order.getOrder_num());
		orderJSON.put("need_invoice", order.getNeed_invoice());
		orderJSON.put("channel", order.getSource());
		// 缓冲打印
		printBuffer.put("orderPrint", orderJSON);

		ordersManagementService.printOrderBuffer(tenancyId, storeId,
				printBuffer);

		// 发送消息
		List<String> msgList = new ArrayList<String>();
		msgList.add(orderJson.optString("order_code"));
		JSONObject msg = new JSONObject();
		msg.put("order_state",
				com.tzx.orders.base.constant.Constant.ORDER_STATE_TOSHOP);
		msg.put("order_code", msgList);
		// msg.put("chanel", orderJson.optString("chanel"));
		// msg.put("is_online_payment",
		// orderJson.optString("is_online_payment"));
		// Comet4jUtil.sendMessage2All(Comet4jUtil.CALLCENTER_ORDER,
		// msg.toString());

		// 响铃提醒
		List<JSONObject> cometList = new ArrayList<JSONObject>();
		cometList.add(msg);
		Data cometData = new Data();
		cometData.setType(Type.ORDER);
		cometData.setOper(Oper.add);
		cometData.setData(cometList);
		Comet4jUtil.sendMessage2All(Comet4jUtil.POS_PC,
				JSONObject.fromObject(cometData).toString());

	}

	@SuppressWarnings("unchecked")
	@Override
	public synchronized void cancleOrders(String tenancyId, int storeId, JSONObject data)
			throws Exception {
		
		boolean isAfterChangeShift=data.optBoolean("is_after_change_shift");
		JSONArray dataList = data.optJSONArray("data");
		List<OrderState> sendList = new ArrayList<OrderState>();
		JSONObject reportDate = null;
		//JSONObject dutyOrder = null;
		if (null == reportDate) {
			reportDate = ordersDao.getCurrentReportDate(tenancyId, storeId);
		}
/*		if (null == dutyOrder) {
			dutyOrder = ordersDao.getCurrentDutyOrder(tenancyId, storeId);
		}*/

		for (Object obj : dataList) {
			JSONObject json = JSONObject.fromObject(obj);
			//查询是否已经生成账单
			Thread.sleep(5000);
			JSONObject billJson = ordersDao.getBillByOrderCode(tenancyId, storeId, json.optString("order_code"),null);
			if(billJson!=null){//订单已经存在
				JSONObject billStateJson = ordersDao.getBillByOrderCode(tenancyId, storeId, json.optString("order_code"),SysDictionary.BILL_STATE_CJ01);
				if(billStateJson==null){
					// 查询订单是否是新订单
					OrderState orderState = ordersDao.getOrderStateByOrderCode(
							tenancyId, storeId, json.optString("order_code"));
					//查询是否有整单退款-整单退款后取消不声音提醒
					OrderState refundOrderState = ordersDao.getOrderStateByOrderCode(
							tenancyId, storeId, json.optString("order_code")+"T2");
					if (orderState == null) {
						throw SystemException
								.getInstance(PosErrorCode.ORDER_NOT_EXISTS_ERROR);
					}
					// 不需要同步订单数据
					OrderSyncServiceImp.NO_NEED_SYNC = true;
					// 获得计算用的小数点位数
					String cmjews = com.tzx.pos.base.Constant.constantMap.get("CMJEWS");
					String zdjews = com.tzx.pos.base.Constant.constantMap.get("ZDJEWS");
					if (com.tzx.orders.base.constant.Constant.THIRD_CHANNEL
							.contains(orderState.getChanel())) {
						if (!NUMBER_EXACT.equals(cmjews)) {
							com.tzx.pos.base.Constant.constantMap.put("CMJEWS",
									NUMBER_EXACT);
						}
						if (!NUMBER_EXACT.equals(zdjews)) {
							com.tzx.pos.base.Constant.constantMap.put("ZDJEWS",
									NUMBER_EXACT);
						}
					}

					if (com.tzx.orders.base.constant.Constant.ORDER_STATE_CANCEL.equals(orderState.getOrder_state())) {
						// 订单已取消
						continue;
					} else {
						try {
						    //判断打印方式
		                    boolean isNewPrint = posPrintNewService.isNONewPrint(tenancyId, storeId);
							// 订单状态是否为新订单,非新订单需要取消账单
							String posNum = "";
							String optNum = "";
							Integer shift_id=null;
							if (!com.tzx.orders.base.constant.Constant.ORDER_STATE_TOSHOP
									.equals(orderState.getOrder_state())
									&& Scm.hv(orderState.getBill_num())) {
								/*StringBuilder sql = new StringBuilder(
								"select b.open_pos_num pos_num,b.sale_mode,coalesce(b.table_code,'') as table_code,b.bill_property,coalesce(b.bill_state,'') as bill_state,b.bill_amount,b.payment_amount,b.difference,sum(p.amount)as amount,b.shift_id from pos_bill b left join pos_bill_payment p on b.bill_num=p.bill_num and b.store_id=p.store_id where b.store_id=? and b.bill_num=? group by b.id");

		*/						StringBuilder sql = new StringBuilder(
								"SELECT A .*, b.amount FROM ( SELECT b.open_pos_num pos_num, b.sale_mode, COALESCE (b.table_code, '') AS table_code, b.bill_property, COALESCE (b.bill_state, '') AS bill_state, b.bill_amount, b.payment_amount, b.difference, b.bill_num, b.shift_id FROM v_pos_bill b WHERE b.store_id = ? AND b.bill_num = ? ) A LEFT JOIN ( SELECT SUM (P .amount) AS amount, P .bill_num FROM v_pos_bill_payment P GROUP BY P .bill_num ) b ON A .bill_num = b.bill_num");
								List<JSONObject> billPaymentList = ordersDao.query4Json(tenancyId,sql.toString(),new Object[] { storeId,orderState.getBill_num() });
								
								if (null == billPaymentList || billPaymentList.isEmpty()) {
									throw SystemException .getInstance(PosErrorCode.NOT_EXISTS_BILL);
								}
		
								if (Tools.hv(billPaymentList.get(0).optString("bill_state"))) {
									throw SystemException.getInstance(PosErrorCode.HAVING_CANC_FOOD);
								}

								double paymentAmount = JsonUtils.getDouble(
										billPaymentList.get(0), "payment_amount");
								double amount = JsonUtils.getDouble(
										billPaymentList.get(0), "amount");
								double difference = JsonUtils.getDouble(
										billPaymentList.get(0), "difference");
								JSONObject rso = ordersManagementService.getPosOptState(tenancyId, storeId,isAfterChangeShift);
								if(!THIRD_CHANNEL.contains(orderState.getChanel())){ //微信取消
									rso = ordersManagementService.getPosOptState4WX(tenancyId, storeId);
								}
								
								/*posNum = billPaymentList.get(0).optString("pos_num");

								StringBuilder queryOptSql = new StringBuilder();
								queryOptSql
										.append(" select os1.opt_num from pos_opt_state os1 left join pos_opt_state os2 on os1.store_id=os2.store_id and os1.report_date=os2.report_date");
								queryOptSql
										.append(" and os1.pos_num=os2.pos_num and os1.opt_num=os2.opt_num and os1.content=? and os2.content=? and os1.last_updatetime<os2.last_updatetime");
								queryOptSql
										.append(" where os1.tenancy_id=? and os1.store_id=? and os1.report_date=? and os1.pos_num=? and os1.content=? and os1.tag='0' and os2.id is null");

								List<JSONObject> rso = ordersDao
										.query4Json(
												tenancyId,
												queryOptSql.toString(),
												new Object[] {
														SysDictionary.OPT_STATE_KSSY,
														SysDictionary.OPT_STATE_YYTC,
														tenancyId,
														storeId,
														new SimpleDateFormat(
																"yyyy-MM-dd").parse(reportDate
																.optString("report_date")),
														posNum,
														SysDictionary.OPT_STATE_KSSY });
*/
								if (null != rso && !rso.isEmpty()) {
									optNum = rso.optString("opt_num");
									posNum = rso.getString("pos_num");
									shift_id = rso.optInt("shift_id");
								} else {
									throw SystemException.getInstance(PosErrorCode.POS_CASHIER_NOT_SIGNIN_ERROR);
								}

								int reasonId = 0;
								if (null != json.optJSONArray("order_reason")
										&& json.optJSONArray("order_reason").size() > 0) {
									reasonId = JSONObject.fromObject(
											json.optJSONArray("order_reason")
													.getJSONObject(0)).optInt(
											"reason_type");
								}



								// 是否已经付款,未付款先结账
								if (!"CLOSED".equals(billPaymentList.get(0).optString("bill_property"))) {

									StringBuilder paymentWaySql = new StringBuilder(
											"select w.id as jzid from public.payment_way w inner join public.payment_way_of_ogran o on w.id=o.payment_id where is_standard_money='1' and o.organ_id=?");

									List<JSONObject> paymentWayList = ordersDao
											.query4Json(tenancyId,
													paymentWaySql.toString(),
													new Object[] { storeId });

									JSONObject payItemJson = new JSONObject();
									payItemJson.put("jzid", paymentWayList.get(0).optString("jzid"));
									payItemJson.put("amount", String.valueOf(DoubleHelper.sub(paymentAmount,amount, 4)));
									payItemJson.put("currency_amount", String.valueOf(DoubleHelper.sub(paymentAmount,amount, 4)));
									payItemJson.put("reason_id",String.valueOf(reasonId));
									payItemJson.put("count", "1");
									payItemJson.put("number", "");
									payItemJson.put("phone", "");
									payItemJson.put("customer_id", "");
									payItemJson.put("bill_code", "");
									payItemJson.put("remark", "");

									List<JSONObject> payItemList = new ArrayList<JSONObject>();
									payItemList.add(payItemJson);

									JSONObject paymentJson = new JSONObject();
									paymentJson.put("report_date",reportDate.optString("report_date"));
									paymentJson.put("shift_id",shift_id);
									paymentJson.put("pos_num", posNum);
									paymentJson.put("opt_num", optNum);
									paymentJson.put("bill_num",orderState.getBill_num());
									paymentJson.put("table_code", billPaymentList.get(0).optString("table_code"));
									paymentJson.put("payment_amount", paymentAmount);
									paymentJson.put("difference", difference);
									paymentJson.put("sale_mode", billPaymentList.get(0).optString("sale_mode"));
									paymentJson.put("isprint_bill", "N");
									paymentJson.put("item", payItemList);

									List<JSONObject> paymentList = new ArrayList<JSONObject>();
									paymentList.add(paymentJson);

									Data paymentData = Data.get();
									paymentData.setTenancy_id(tenancyId);
									paymentData.setStore_id(storeId);
									paymentData.setType(Type.PAYMENT);
									paymentData.setData(paymentList);

									Data paymentResultData = Data.get();
									JSONObject resultJson = new JSONObject();
									// paymentService.posPayment(paymentData,
									// paymentResultData,resultJson);
									// 新统一结账接口
									paymentService.posBillPayment(paymentData,
											paymentResultData, resultJson);
									/*if (resultJson != null) {
										if ("Y".equalsIgnoreCase(resultJson
												.optString("is_print"))) {
										}
									}*/

								}

		                        JSONObject retreatJson = new JSONObject();
		                        retreatJson.put("tenancy_id", tenancyId);
		                        retreatJson.put("store_id", storeId);
		                        retreatJson.put("bill_num", orderState.getBill_num());
		                        retreatJson.put("oper", "退菜");
		                        retreatJson.put("oper_type", SysDictionary.ITEM_REMARK_TC01);

		                        if(isNewPrint) {
		                            posPrintNewService.posPrintByFunction(tenancyId, storeId, FunctionCode.RETREAT_FOOD_SELF, retreatJson);
		                        }
		                        else
		                        {
		                            List<Integer> printList = posPrintService.orderChef(tenancyId, orderState.getBill_num(), storeId, "0");
		                        }

								/*
								 * else { StringBuilder queryOrderSql = new
								 * StringBuilder(
								 * "select l.is_online_payment from cc_order_list l where l.store_id =? and l.bill_num=?"
								 * ); List<JSONObject> orderList =
								 * ordersDao.query4Json(tenancyId,
								 * queryOrderSql.toString(), new Object[] { storeId,
								 * orderState.getBill_num() });
								 * 
								 * if (null != orderList && orderList.size() > 0 &&
								 * "0".equals
								 * (orderList.get(0).optString("is_online_payment"))) {
								 * StringBuilder paymentWaySql = new StringBuilder();
								 * paymentWaySql.append(
								 * " select b.payment_amount,p.jzid,p.amount,w.payment_class from pos_bill b"
								 * ); paymentWaySql.append(
								 * " left join pos_bill_payment p on b.bill_num=p.bill_num and b.store_id=p.store_id left join payment_way w on w.id=p.jzid"
								 * ); paymentWaySql.append(
								 * " where b.store_id=? and b.bill_num=?");
								 * List<JSONObject> paymentWayList =
								 * ordersDao.query4Json(tenancyId,
								 * paymentWaySql.toString(), new Object[] { storeId,
								 * orderState.getBill_num() }); // 是否门店付款,门店付款,需要退款 for
								 * (JSONObject paymentWayJson : paymentWayList) { double
								 * paymentWayAmount =
								 * JsonUtils.getDouble(paymentWayJson, "amount");
								 * 
								 * JSONObject paymentJson = new JSONObject();
								 * paymentJson.put("bill_num",
								 * orderState.getBill_num());
								 * paymentJson.put("refund_amount",
								 * DoubleHelper.round(paymentWayAmount, 4));
								 * 
								 * List<JSONObject> paymentList = new
								 * ArrayList<JSONObject>();
								 * paymentList.add(paymentJson);
								 * 
								 * Data paymentData = Data.get();
								 * paymentData.setTenancy_id(tenancyId);
								 * paymentData.setStore_id(storeId);
								 * paymentData.setData(paymentList); if
								 * ("wechat_pay".equals
								 * (paymentWayJson.optString("payment_class"))) {// 微信支付
								 * paymentData.setType(Type.PAYMENT_REFUNDORDER);
								 * paymentData.setOper(Oper.refund);
								 * paymentService.refundorder(paymentData, ""); } else
								 * if ("ali_pay".equals(paymentWayJson.optString(
								 * "payment_class"))) {// 支付宝支付
								 * paymentData.setType(Type.PAYMENT_REFUND);
								 * paymentData.setOper(Oper.refund);
								 * paymentService.refund(paymentData, ""); }
								 * 
								 * } // 修改订单退款状态 JSONObject para = new JSONObject();
								 * para.put("payment_state",
								 * com.tzx.orders.base.constant
								 * .Constant.PAYMENT_STATE_REFUND);
								 * para.put("order_code", orderState.getOrder_code());
								 * ordersDao.updateOrderInfo(tenancyId, storeId, para);
								 * } }
								 */

								// 账单取消
								JSONObject cancelJson = new JSONObject();
								cancelJson.put("report_date",
										reportDate.optString("report_date"));
								cancelJson.put("opt_num", optNum);
								cancelJson.put("pos_num", posNum);
								cancelJson.put("shift_id",shift_id);
								cancelJson.put("bill_num", orderState.getBill_num());
								cancelJson.put("reason_id", reasonId);
								cancelJson.put("manager_id", "0");
								cancelJson.put("opt_login_number", "0");
								cancelJson.put("is_online_payment",orderState.getIs_online_payment());

								List<JSONObject> cancelList = new ArrayList<JSONObject>();
								cancelList.add(cancelJson);

								Data cancelData = Data.get();
								cancelData.setTenancy_id(tenancyId);
								cancelData.setStore_id(storeId);
								cancelData.setType(Type.CANCBILL);
								cancelData.setData(cancelList);

								Data cancelResultData = Data.get();
								JSONObject printJson = new JSONObject();
								
								JSONObject posBill=ordersDao.getBillByBillNum(tenancyId, storeId,orderState.getBill_num());

								if(posBill!=null && !posBill.isEmpty() ){ 
									posDishService.cancelBill(cancelData, codeService,cancelResultData, printJson);
								}else{//隔日退款冲减操作
									if(THIRD_CHANNEL.contains(orderState.getChanel())){
										logger.info("外卖隔日退款："+json.toString());
										posDishService.refundOrderCancelBill(cancelData, codeService,cancelResultData, printJson);
									}else{
										logger.info("已经做过打烊，订单号为："+json.optString("order_code")+"不能进行隔日退款");
										throw SystemException.getInstance(PosErrorCode.ALREADY_EXIST_DAYEND_ERROR);
									}
								}

								if (printJson != null) {
									printJson.put("refound_bill_num", "");
									printJson.put("refound_num", "");
									if(isNewPrint) {//如果启用新的打印模式
										//if ("1".equals(OrderUtil.getSysPara(tenancyId, storeId, "order_translate_bill_mode"))) {
											boolean isPrintReturn = true;//是否打印退菜单
											boolean isPrintWithMeal = false;//是否打印随餐单取消单
											boolean isPrintKitchen = true;//是否打印厨打取消单
											String printMode = SysDictionary.PRINT_CODE_1202;//异常随餐单打印参数，默认正常取消 1202
											//第三方  订单转账单
											if (THIRD_CHANNEL.contains(orderState.getChanel())) {
												isPrintWithMeal = true;//打印随餐单
												if (orderState.getOrder_state().equals(com.tzx.orders.base.constant.Constant.ORDER_STATE_EXCEPTION)) {//异常单取消
													isPrintWithMeal = false;//打印随餐单
													isPrintReturn = false;//不打印退菜单
													isPrintKitchen = false;//不打印厨打取消单
													printMode = SysDictionary.PRINT_CODE_1204;//异常单取消 1204
												}
											}
											//是否打印退菜单
											if (isPrintReturn) {
												posPrintNewService.posPrintByFunction(printJson.optString("tenancy_id"),
														printJson.optInt("store_id"),
														FunctionCode.CANCBILL,
														printJson);
											}
											//是否打印随餐单
											if (isPrintWithMeal && THIRD_CHANNEL.contains(orderState.getChanel())) {
												String is_invoice = printJson.optString("is_invoice");
												printJson.put("is_invoice", "0");

												if (SysDictionary.PRINT_CODE_1202.equals(printMode)) {
													JSONObject newPrint = new JSONObject();
													newPrint.putAll(printJson);
													newPrint.put("order_num", orderState.getOrder_code());
													posPrintNewService.posPrintByMode(printJson.optString("tenancy_id"),
															printJson.optInt("store_id"),
															printMode, newPrint
													);
												} else {
													posPrintNewService.posPrintByMode(printJson.optString("tenancy_id"),
															printJson.optInt("store_id"),
															printMode,
															printJson);
												}

												printJson.put("is_invoice", is_invoice);
												JSONObject org = posDishDao.getOrganById(tenancyId, storeId);
												if (isPrintKitchen && org.optString("format_state").equals("2")) { //非异常单 并且是快餐进行打印
													printJson.put("oper_type", SysDictionary.ITEM_REMARK_TC01);//添加退菜标记
													//循环打印厨打退菜单 1113 一份一单       1106 一菜一单       1107 整单
													//String[] printModes = {SysDictionary.PRINT_CODE_1113, SysDictionary.PRINT_CODE_1106, SysDictionary.PRINT_CODE_1107};
													String[] printModes = {SysDictionary.PRINT_CODE_1106, SysDictionary.PRINT_CODE_1107};

													for (String mode : printModes) {
														posPrintNewService.posPrintByMode(printJson.optString("tenancy_id"),printJson.optInt("store_id"),mode,printJson);
													}
												}
											}
//										} else {
//											posPrintNewService.posPrintByFunction(printJson.optString("tenancy_id"),
//													printJson.optInt("store_id"), FunctionCode.CANCBILL, printJson);
//
//											JSONObject newPrint = new JSONObject();
//											newPrint.putAll(printJson);
//											newPrint.put("order_num", orderState.getOrder_code());
//											posPrintNewService.posPrintByMode(printJson.optString("tenancy_id"),printJson.optInt("store_id"), SysDictionary.PRINT_CODE_1202, newPrint);
//										}

									} else {
										List<Integer> ids = posPrintService.orderRetreat(
												printJson.optString("tenancy_id"),
												printJson.optString("bill_num"),
												printJson.optInt("store_id"),
												printJson.optString("oper"),
												printJson.optString("oper_type"),
												printJson.optString("rwids"));
										posPrintService.orderPrint(
												printJson.optString("tenancy_id"),
												printJson.optString("bill_num"),
												printJson.optInt("store_id"), ids);
									}
								}
							}

		                    logger.info("-------isNewPrint:"+isNewPrint+"-------------getOrder_state:"+orderState.getOrder_state());
		                    if(isNewPrint) {
		                    	//com.tzx.orders.base.constant.Constant.ORDER_STATE_EXCEPTION_COMPLETE.equals(orderState.getOrder_state())
		                        if (com.tzx.orders.base.constant.Constant.ORDER_STATE_EXCEPTION.equals(orderState.getOrder_state())) {

		                            JSONObject printJson = new JSONObject();
		                            printJson.put("refound_bill_num", "");
		                            printJson.put("refound_num","");
		                            printJson.put("tenancy_id", tenancyId);
		                            printJson.put("store_id", storeId);
		                            printJson.put("order_num", orderState.getOrder_code());
		                            printJson.put("report_date", reportDate.optString("report_date"));
		                            printJson.put("pos_num", posNum);
		                            posPrintNewService.posPrintByMode(tenancyId, storeId, SysDictionary.PRINT_CODE_1204, printJson);
		                        }
		                    }

							orderState.setOrder_state(com.tzx.orders.base.constant.Constant.ORDER_STATE_CANCEL);
							orderState.setPayment_state(PAYMENT_STATE_REFUND);
							orderState.setReceive_time_cancellation(DateUtil.getNowDateYYDDMMHHMMSS());

							ordersDao.updateOrderInfo(tenancyId, storeId,JSONObject.fromObject(orderState));

							ordersDao.addOrderReasonDetail(tenancyId, storeId,json.optString("order_code"),json.optJSONArray("order_reason"));

							// 打印订单取消
							JSONObject printJson = new JSONObject();
							printJson.put("print_code",com.tzx.orders.base.constant.Constant.ORDER_PRINT_CODE_1202);
							printJson.put("mode", "0");
							printJson.put("report_date",reportDate.optString("report_date"));
							printJson.put("shift_id", shift_id);
							printJson.put("pos_num", posNum);
							printJson.put("opt_num", optNum);
							printJson.put("bill_num", orderState.getBill_num());

							List<JSONObject> printList = new ArrayList<JSONObject>();
							printList.add(printJson);

							Data printData = Data.get();
							printData.setTenancy_id(tenancyId);
							printData.setStore_id(storeId);
							printData.setType(Type.PRINT_BILL);
							printData.setData(printList);

//							 Data resultData = Data.get();
//							 posPrintService.printPosBill(printData,resultData);//暂时不打印取消订单
						} catch (Exception e) {
							orderState.setOrder_state(com.tzx.orders.base.constant.Constant.ORDER_STATE_CANCEL_FAILURE);
							logger.error("=========================订单取消失败=================\n",e);
							throw e;
						}
						/*
						 * finally { sendList.add(orderState);
						 * OrderUtil.sendOrderState(tenancyId, storeId, Type.ORDER,
						 * Oper.update, sendList);
						 * 
						 * JSONObject orderStateJson =
						 * JSONObject.fromObject(orderState);
						 * orderStateJson.put("upload_tag", "1");
						 * orderStateJson.put("order_code",
						 * json.optString("order_code"));
						 * ordersDao.updateOrderInfo(tenancyId, storeId,
						 * orderStateJson); }
						 */
					}

					// 重置同步订单状态和结算精度
					OrderSyncServiceImp.NO_NEED_SYNC = false;
					if (!NUMBER_EXACT.equals(cmjews)) {
						com.tzx.pos.base.Constant.constantMap.put("CMJEWS", cmjews);
					}
					if (!NUMBER_EXACT.equals(zdjews)) {
						com.tzx.pos.base.Constant.constantMap.put("ZDJEWS", zdjews);
					}

					//外卖退款订单不存在整单退，取消声音提醒
					if(refundOrderState==null){
			            List<String> msgList = new ArrayList<String>();
			            msgList.add(orderState.getOrder_code());
			            JSONObject msg = new JSONObject();
			            msg.put("order_state", orderState.getOrder_state());
			            msg.put("order_code", msgList);
			            // 响铃提醒
			            List<JSONObject> cometList = new ArrayList<JSONObject>();
			            cometList.add(msg);
			            Data cometData = new Data();
			            cometData.setTenancy_id(tenancyId);
			            cometData.setStore_id(storeId);
			            cometData.setType(Type.ORDER);
			            cometData.setOper(Oper.add);
			            cometData.setSuccess(true);
			            cometData.setData(cometList);
			            //POS用到UDP
			            String port = new CometDataNoticeClientRunnable(cometData).getPort();
			            SendUdpToPos.sendMessagePos(JSONObject.fromObject(cometData).toString(), port);
					}


					List<String> msgList = new ArrayList<String>();
					msgList.add(orderState.getOrder_code());

					JSONObject msg = new JSONObject();
					msg.put("order_state", orderState.getOrder_state());
					msg.put("order_code", msgList);

					Comet4jUtil.sendMessage2All(Comet4jUtil.CALLCENTER_ORDER, msg.toString());
				}
			}else{
				OrderState existsOrderState = ordersDao.getOrderStateByOrderCode(tenancyId, storeId, json.optString("order_code"));
				if(existsOrderState!=null){
					if (com.tzx.orders.base.constant.Constant.ORDER_STATE_CANCEL.equals(existsOrderState.getOrder_state())) {
						continue;
					}else{
						Long currentTime = System.currentTimeMillis();
						Date reveiveTime = DateUtil.parseDateAll(existsOrderState.getReceive_time());
						if((currentTime-reveiveTime.getTime())>1000*60*30){//30分钟未转成账单 收到取消消息修改订单为08
							existsOrderState.setOrder_state(com.tzx.orders.base.constant.Constant.ORDER_STATE_CANCEL);
							existsOrderState.setPayment_state(PAYMENT_STATE_REFUND);
							existsOrderState.setReceive_time_cancellation(DateUtil.getNowDateYYDDMMHHMMSS());
							ordersDao.updateOrderInfo(tenancyId, storeId, JSONObject.fromObject(existsOrderState));

							List<String> msgList = new ArrayList<String>();
							msgList.add(existsOrderState.getOrder_code());

							JSONObject msg = new JSONObject();
							msg.put("order_state", com.tzx.orders.base.constant.Constant.ORDER_STATE_CANCEL);
							msg.put("order_code", msgList);
							Comet4jUtil.sendMessage2All(Comet4jUtil.CALLCENTER_ORDER, msg.toString());
						}
					}
				}else{//订单不存在（未能接收到该订单的新增信息--先接收到取消信息）
					logger.info("订单号为："+json.optString("order_code")+" 原账单还未生成。");
					throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_BILL);
				}
			}
		}
	}

	@SuppressWarnings("unchecked")
	@Override
	public void opinionOrders(String tenancyId, int storeId, JSONObject data)
			throws Exception {
		JSONArray dataList = data.optJSONArray("data");
		for (Object obj : dataList) {
			JSONObject json = JSONObject.fromObject(obj);
			ordersDao.addOrderReasonDetail(tenancyId, storeId,
					json.optString("order_code"),
					json.optJSONArray("order_reason"));
		}
	}

	@Override
	public void completeOrders(String tenancyId, int storeId, JSONObject data)
			throws Exception {
		// 获取订单状态
		OrderState orderState = ordersDao.getOrderStateByOrderCode(tenancyId,
				storeId, data.optString("order_code"));
		// 付款未完成

		if (orderState == null
				|| com.tzx.orders.base.constant.Constant.ORDER_STATE_CANCEL
						.equals(orderState.getOrder_state())) {
			throw SystemException
					.getInstance(PosErrorCode.ORDER_STATE_ALREADY_CANCEL_ERROR);
		}
		if (orderState == null
				|| com.tzx.orders.base.constant.Constant.ORDER_STATE_COMPLETE
						.equals(orderState.getOrder_state())) {
			throw SystemException
					.getInstance(PosErrorCode.ORDER_STATE_ALREADY_COMPLETE_ERROR);
		}

		if ("WM02".equalsIgnoreCase(orderState.getOrder_type())) {
			if (orderState == null
					|| !com.tzx.orders.base.constant.Constant.ORDER_STATE_DELIVERY
							.equals(orderState.getOrder_state())) {
				throw SystemException
						.getInstance(PosErrorCode.ORDER_STATE_NOT_DELIVERY_ERROR);
			}
		} else if ("ZT01".equalsIgnoreCase(orderState.getOrder_type())) {
			if (orderState == null
					|| !com.tzx.orders.base.constant.Constant.ORDER_STATE_COMPLETE_PAYMENT
							.equals(orderState.getOrder_state())) {
				throw SystemException
						.getInstance(PosErrorCode.ORDER_STATE_NOT_PAYMENT_ERROR);
			}
		}

		orderState
				.setOrder_state(com.tzx.orders.base.constant.Constant.ORDER_STATE_COMPLETE);
		String currentTime = DateUtil.getNowDateYYDDMMHHMMSS();
		orderState.setFinish_time(currentTime);
		orderState.setReceive_time_finish(currentTime);

		// 修改订单状态,并返回总部
		if (ordersDao.updateOrderInfo(tenancyId, storeId,
				JSONObject.fromObject(orderState))) {
			List<OrderState> orderStateList = new ArrayList<OrderState>();
			orderStateList.add(orderState);
			OrderUtil.sendOrderState(tenancyId, storeId, Type.ORDER,
					Oper.update, orderStateList);
		}

	}

	@Override
	public void updateDish(String tenancyId, int storeId, JSONObject data)
			throws Exception {

		JSONArray dataList = data.optJSONArray("data");

		for (Object obj : dataList) {
			// List<OrderState> sendList = new ArrayList<OrderState>();

			JSONObject json = JSONObject.fromObject(obj);
			JSONObject orderJson = json.optJSONObject("order_list");
			// 查询订单状态
			OrderState orderState = ordersDao.getOrderStateByOrderCode(
					tenancyId, storeId, orderJson.optString("order_code"));
			// 订单不存在
			if (orderState == null) {
				throw SystemException
						.getInstance(PosErrorCode.ORDER_NOT_EXISTS_ERROR);
			}
			// 订单已完成
			else if (com.tzx.orders.base.constant.Constant.ORDER_STATE_COMPLETE
					.equals(orderState.getOrder_state())) {
				throw SystemException
						.getInstance(PosErrorCode.ORDER_STATE_ALREADY_COMPLETE_ERROR);
			} else {

				// 原订单已点菜品
				String oldItemSql = "select * from cc_order_item where order_code='"
						+ orderJson.optString("order_code") + "'";
				String oldTasteSql = "select * from cc_order_item_taste where order_code='"
						+ orderJson.optString("order_code") + "'";
				String oldDetailsSql = "select * from cc_order_item_details where order_code='"
						+ orderJson.optString("order_code") + "'";
				List<JSONObject> oldItem = ordersDao.query4Json(tenancyId,
						oldItemSql);
				List<JSONObject> oldTaste = ordersDao.query4Json(tenancyId,
						oldTasteSql);
				List<JSONObject> oldDetails = ordersDao.query4Json(tenancyId,
						oldDetailsSql);
				// 更新订单信息
				ordersDao.deleteOrderByOrderCode(tenancyId, storeId,
						orderJson.optString("order_code"));

				orderJson.put("order_state", orderState.getOrder_state());
				orderJson
						.put("receive_time", DateUtil.getNowDateYYDDMMHHMMSS());
				ordersDao.addOrderInfo(tenancyId, storeId, json);

				if (com.tzx.orders.base.constant.Constant.ORDER_STATE_RECEIVE
						.equals(orderState.getOrder_state())) {
					List<PosBillItem> orderItem = ordersDao
							.getOrderItemByOrdercode(tenancyId, storeId,
									orderJson.optString("order_code"), "MD01");
					if (null == orderItem || orderItem.size() == 0) {
						throw SystemException
								.getInstance(PosErrorCode.NOT_EXISTS_DISH_ITEM);
					}
					addDish(tenancyId, storeId, orderJson, orderItem);
				}

				ordersDao.insertBatchIgnorCase(tenancyId, "cc_order_item",
						oldItem);
				ordersDao.insertBatchIgnorCase(tenancyId,
						"cc_order_item_taste", oldTaste);
				ordersDao.insertBatchIgnorCase(tenancyId,
						"cc_order_item_details", oldDetails);

			}

		}
		// //到店未取单
		// if(com.tzx.orders.base.constant.Constant.ORDER_STATE_TOSHOP.equals(orderState.getOrder_state())){
		//
		// }

		// if(null==orderState){ // 无:插入订单
		//
		// // 返回订单状态
		// orderJson.put("order_state",
		// com.tzx.orders.base.constant.Constant.ORDER_STATE_TOSHOP);
		// orderJson.put("receive_time", DateUtil.getNowDateYYDDMMHHMMSS());
		//
		// ordersDao.addOrderInfo(tenancyId, storeId, json);
		//
		// orderState = OrderState.get(orderJson);
		// orderState.setChanel(orderJson.optString("chanel"));
		// sendList.add(orderState);
		// OrderUtil.sendOrderState(tenancyId, storeId, Type.ORDER, Oper.update,
		// sendList);
		//
		// //向前台发送消息
		// msgList.add(orderJson.optString("order_code"));
		// JSONObject msg = new JSONObject();
		// msg.put("order_state",
		// com.tzx.orders.base.constant.Constant.ORDER_STATE_TOSHOP);
		// msg.put("order_code", msgList);
		// msg.put("chanel", orderJson.optString("chanel"));
		// msg.put("is_online_payment",
		// orderJson.optString("is_online_payment"));
		// // 支付方式　
		// Comet4jUtil.sendMessage2All(Comet4jUtil.CALLCENTER_ORDER,
		// msg.toString());
		//
		// //响铃提醒
		// List<JSONObject> cometList = new ArrayList<JSONObject>();
		// cometList.add(msg);
		// Data cometData = new Data();
		// cometData.setType(Type.ORDER);
		// cometData.setOper(Oper.add);
		// cometData.setData(cometList);
		// Comet4jUtil.sendMessage2All(Comet4jUtil.POS_PC,
		// JSONObject.fromObject(cometData).toString());
		//
		// }else{
		// //已到店但未取单
		// if(com.tzx.orders.base.constant.Constant.ORDER_STATE_TOSHOP.equals(orderState.getOrder_state())){
		// //先付(重复推送)
		// if("1".equals(orderJson.optString("is_online_payment"))){
		// continue;
		// }
		//
		// }
		//
		// }
		//
		// //后付未取单加菜
		// if (json.containsKey("order_item") &&
		// !json.optJSONArray("order_item").isEmpty())
		// {
		// oldItems = ordersDao.getOrderItemByOrdercode(tenancyId, storeId ,
		// orderJson.optString("order_code"), "MD01");
		// // ordersDao.insertBatchIgnorCase(tenancyId, "cc_order_item",
		// orderJson.optJSONArray("order_item"));
		// String
		// delSql="DELETE FROM cc_order_list where order_code='"+orderJson.optString("order_code")+"';DELETE FROM cc_order_repayment where order_code='"+orderJson.optString("order_code")+"'";
		// ordersDao.execute(tenancyId, delSql);
		// ordersDao.addOrderInfo(tenancyId, storeId, json);
		// }
		// // 后付已取单加菜 (不开台只加菜)
		// if(com.tzx.orders.base.constant.Constant.ORDER_STATE_RECEIVE.equals(orderState.getOrder_state())){
		// addDish( tenancyId, storeId,orderJson,oldItems);
		// }

	}

	private TransactionStatus getTransctionStatus(int transactionDefinition) {
		DefaultTransactionDefinition def = new DefaultTransactionDefinition();
		def.setPropagationBehavior(transactionDefinition);
		TransactionStatus status = transactionManager.getTransaction(def);
		return status;
	}

	@Override
	public boolean downloadViceScreenPhoto(String tenancyId, int organId)
			throws Exception {
		String column = "img_addr";

//		String itemImagePath = System.getProperty("contextPath")
//				+ Constant.VICE_SCREEN_PHOTO_PATH;
		
		String itemImagePath = Constant.CONTEXT_PATH + Constant.VICE_SCREEN_PHOTO_PATH;

		// 查询菜品图片
		List<JSONObject> photoList = processMessageDao.getViceScreenPhoto(
				tenancyId, organId);
		Iterator<JSONObject> it = photoList.iterator();
		while (it.hasNext()) {
			JSONObject photoJson = it.next();

			if (photoJson.containsKey(column)
					&& !"".equals(photoJson.optString(column))) {
				URL url = new URL(photoJson.optString(column));
				String filePath = itemImagePath + url.getPath();

				File photFile = new File(filePath);
				if (!photFile.exists()) {
					if (!photFile.getParentFile().exists()) {
						photFile.getParentFile().mkdirs();
					}
					DataInputStream in = null;
					DataOutputStream out = null;
					try {
						HttpURLConnection conn = (HttpURLConnection) url
								.openConnection();
						conn.connect();
						if (conn.getResponseCode() == 200) {
							in = new DataInputStream(conn.getInputStream());
							out = new DataOutputStream(new FileOutputStream(
									filePath));

							byte[] buffer = new byte[4096];
							int count = 0;
							while ((count = in.read(buffer)) > 0) {
								out.write(buffer, 0, count);
							}
						}
					} finally {
						try {
							if (in != null) {
								in.close();
							}
							if (out != null) {
								out.close();
							}
						} catch (IOException e) {
							e.printStackTrace();
						}
					}
				}
			}
		}
		return true;
	}

	/**
	 * 清除业务缓存数据并更新
	 * 
	 * @param tenancyId
	 * @param organId
	 * @return
	 * @throws Exception
	 */
	@Override
	public boolean removeAndUpdateCache(String tenancyId, int organId)
			throws Exception {
		boolean isFinish = true;
		try {
			if (isFinish) {
				// 清除业务缓存
				CacheManager.clearAll();
				// 重新设置缓存
				if (CacheManager.getCacheSize() == 0) {
					cacheManagerService.loadBusinessData(tenancyId, organId);
				}
			}
		} catch (Exception e) {
			isFinish = false;
			logger.info("删除业务缓存数据并更新失败：" + e.getMessage());
			throw e;
		}
		return isFinish;
	}

	 @Override
	    public void modifyOrder(String tenancyId,int storeId,JSONObject data) throws Exception {
	    	//1.删除原有订单数据及账单
	        // 订单cc_order_list,cc_order_item,cc_order_item_detail,cc_order_repayment 条件:order_code
	        // 账单pos_bill,pos_bill_item,pos_bill_payment 条件:pos_bill.bill_num,pos_bill.order.num
	    	
	    	//2.调用 addOrder()重新接收订单;
	        JSONArray dataList = data.optJSONArray("data");
			List<JSONObject> datas = new ArrayList<JSONObject>();
			
			 TransactionStatus status = null;
	         try {
	             status = getTransctionStatus(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
	             
	             for (Object obj : dataList) {
	     			JSONObject json = JSONObject.fromObject(obj);
	     			JSONObject orderJson = json.optJSONObject("order_list");
	     			
	     			json.optJSONObject("order_list").put("oper", "add_modify");//异常订单标记
	     			//orderJson.put("oper", "add_modify");
	     			//获取订单编号
	     			String orderCode = orderJson.optString("order_code").intern();
//	     			synchronized (orderCode) {
	     				// 删除订单信息
	     				boolean result = ordersDao.deleteOrderByOrderCode(tenancyId, storeId, orderCode);
	     				if (result) {
	     					// 根据订单号获取pos_bill
	     					JSONObject bill = ordersDao.getBillByOrderCode(tenancyId, storeId, orderCode);
	     					if (bill != null && (!bill.isEmpty())) {
	     					    //记录原始账单号
                                //String billNum = bill.optString("bill_num");
                                //orderJson.put("bill_num",billNum);
	     						// 删除账单信息
	     						ordersDao.deletePosBillByBillNum(tenancyId, storeId, bill);
	     					}
	     				}
					 datas.add(json);
//	     			}
	     		}
	     		logger.info("重新接收data :::"+datas);
	            data.put("data", datas);
	            transactionManager.commit(status);
	         } catch (Exception e1) {
	             transactionManager.rollback(status);
	             throw e1;
	         }
	         
	         
	        //调用 addOrder()重新接收订单
	        this.addOrders(tenancyId, storeId,data);
	    }

    
    
    @Override
    public void cancleModifyOrders(String tenancyId,int storeId,JSONObject data) throws Exception {
    	//1.删除原有订单数据及账单
        // 订单cc_order_list,cc_order_item,cc_order_item_detail,cc_order_repayment 条件:order_code
        // 账单pos_bill,pos_bill_item,pos_bill_payment 条件:pos_bill.bill_num,pos_bill.order.num
        JSONArray dataList = data.optJSONArray("data");
		
		
		TransactionStatus status = null;
		try {
			status = getTransctionStatus(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
			for (Object obj : dataList) {
				JSONObject json = JSONObject.fromObject(obj);
				JSONObject orderJson = json.optJSONObject("order_list");
				// 获取订单编号
				String orderCode = orderJson.optString("order_code").intern();
				// 删除订单信息
				boolean result = ordersDao.deleteOrderByOrderCode(tenancyId, storeId, orderCode);
				if (result) {
					// 根据订单号获取pos_bill
					JSONObject bill = ordersDao.getBillByOrderCode(tenancyId, storeId, orderCode);
					if (bill != null && (!bill.isEmpty())) {
						// 删除账单信息
						ordersDao.deletePosBillByBillNum(tenancyId, storeId, bill);
					}
				}
			}
			transactionManager.commit(status);
		} catch (Exception e1) {
			transactionManager.rollback(status);
			throw e1;
		}
         
		//2.重新转账单; 重新取消订单中转账单
        this.addOrdersOfCancleModify(tenancyId, storeId,data);
        
        //3.冲减（取消）取消订单
        this.cancleOrdersOfCancleModify(tenancyId, storeId,data);
        
		//POS提示"菜品映射已完成，异常订单已成功入账"
		logger.info("重新取消订单###cancleModifyOrders####start");
    	List<JSONObject> noticeList = new ArrayList<JSONObject>();
        JSONObject noticeJson = new JSONObject();
        noticeJson.put("message", "菜品映射已完成，异常订单已成功入账");
        noticeList.add(noticeJson);
        Comet4jUtil.comet4J(noticeList, Type.DISH_MODIFY_NOTIFY, Oper.notice);
        logger.info("重新取消订单###cancleModifyOrders####end");
    }
    
    /**
     * 
      * @Description: 重新取消订单中转账单
      * @Title:addOrdersOfCancleModify
      * @param:@param tenancyId
      * @param:@param storeId
      * @param:@param data
      * @param:@throws Exception
      * @return: void
      * @author: shenzhanyu
      * @version: 1.0
      * @Date: 2018年4月15日
     */
    public void addOrdersOfCancleModify(String tenancyId, int storeId, JSONObject data)throws Exception {
        JSONArray dataList = data.optJSONArray("data");
        for (Object obj : dataList) {
            JSONObject json = JSONObject.fromObject(obj);
            JSONObject orderJson = json.optJSONObject("order_list");
            // 查询订单是否是新订单
            String orderCode = orderJson.optString("order_code").intern();

            OrderState orderState = ordersDao.getOrderStateByOrderCode(tenancyId, storeId, orderCode);
            // 无:插入订单
            if (null == orderState) {
                TransactionStatus status = null;
                try {
                    status = getTransctionStatus(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
                    ordersDao.addOrderInfo(tenancyId, storeId, json);
                    transactionManager.commit(status);
                } catch (Throwable t) {
                    if (status != null && !status.isCompleted()) {
                        transactionManager.rollback(status);
                        throw t;
                    }
                }

                orderState = OrderState.get(orderJson);
                String chanel = orderJson.optString("chanel");


                TransactionStatus takeTs = getTransctionStatus(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
                JSONObject para = null;
                try {
                    para = ordersManagementService.getPosOptState( tenancyId, storeId);
                    para.put("order_code", orderCode);
                    para.put("channel", chanel);
                   
                    //第三方  订单转账单
                    if(THIRD_CHANNEL.contains(chanel)){//&&"1".equals(OrderUtil.getSysPara(tenancyId,storeId,"order_translate_bill_mode"))
                    	ordersManagementService.createPosBillByOrder(tenancyId, storeId, para);
                    }	
                    transactionManager.commit(takeTs);
                }  catch (Exception e) {
                    logger.error("重新取消订单，转账单失败!", e);
                    transactionManager.rollback(takeTs);
                }
   
            }
        }
    }
    
    /**
      * @Description: 生成取消订单数据结构
      * @Title:createCancleData
      * @param:@param tenancyId
      * @param:@param storeId
      * @param:@param data
      * @param:@return
      * @param:@throws Exception
      * @return: JSONObject
      * @author: shenzhanyu
      * @version: 1.0
      * @Date: 2018年4月15日
     */
    public JSONObject createCancleData(String tenancyId, int storeId, String orderCode)throws Exception {
    	Data cancleData = Data.get(tenancyId,storeId,0);
    	cancleData.setType(Type.ORDER);//类型：订单
    	cancleData.setOper(Oper.cancle);//操作：取消
    	JSONObject json = new JSONObject();
    	List<JSONObject> jsonList = new ArrayList<JSONObject>();
    	JSONObject reasonJson = new JSONObject();
    	List<JSONObject> reasonList = new ArrayList<JSONObject>();
    	json.put("order_code", orderCode);
    	String currentTime = DateUtil.currentTimestamp().toString();
    	json.put("cancellation_time", currentTime);//当前时间
    	
    	reasonJson.put("reason_type",0);
    	reasonJson.put("type", "MD03");
    	reasonJson.put("complaint_content", "");
    	reasonJson.put("complaints_time", currentTime);//当前时间
    	reasonJson.put("order_state", "08");//当前时间
    	reasonList.add(reasonJson);
    	
    	json.put("order_reason", reasonList);
    	jsonList.add(json);
    	cancleData.setData(jsonList);
		String pdata = JsonUtil.DataToJson(cancleData);
    	JSONObject cancleJson = JSONObject.fromObject(pdata);
    	return cancleJson;
    }
    
    /**
      * @Description: 重新取消
      * @Title:cancleOrdersOfCancleModify
      * @param:@param tenancyId
      * @param:@param storeId
      * @param:@param data
      * @param:@throws Exception
      * @return: void
      * @author: shenzhanyu
      * @version: 1.0
      * @Date: 2018年4月15日
     */
	public void cancleOrdersOfCancleModify(String tenancyId, int storeId, JSONObject data)
			throws Exception {
        JSONArray dataList = data.optJSONArray("data");
        for (Object obj : dataList) {
            JSONObject json = JSONObject.fromObject(obj);
            JSONObject orderJson = json.optJSONObject("order_list");
            // 查询订单是否是新订单
            String orderCode = orderJson.optString("order_code").intern();
            
            //生成取消订单数据结构
    		JSONObject cancleJson = this.createCancleData(tenancyId, storeId, orderCode);
    		
    		JSONArray cancleList = cancleJson.optJSONArray("data");
    		JSONObject reportDate = null;
    		JSONObject dutyOrder = null;
    		if (null == reportDate) {
    			reportDate = ordersDao.getCurrentReportDate(tenancyId, storeId);
    		}
    		if (null == dutyOrder) {
    			dutyOrder = ordersDao.getCurrentDutyOrder(tenancyId, storeId);
    		}

			for (Object cancleObj : cancleList) {
				JSONObject jsonobject = JSONObject.fromObject(cancleObj);
				// 查询订单是否是新订单
				OrderState orderState = ordersDao.getOrderStateByOrderCode(tenancyId, storeId,
						jsonobject.optString("order_code"));
				JSONObject para = ordersManagementService.getPosOptState(tenancyId, storeId);
				if (com.tzx.orders.base.constant.Constant.ORDER_STATE_CANCEL.equals(orderState.getOrder_state())) {
					// 订单已取消
					continue;
				} else {
					try {
						// 订单状态是否为新订单,非新订单需要取消账单
						String posNum = para.optString("pos_num");
						String optNum = para.optString("opt_num");
						if (!com.tzx.orders.base.constant.Constant.ORDER_STATE_TOSHOP
								.equals(orderState.getOrder_state()) && Scm.hv(orderState.getBill_num())) {
                        	JSONObject billJson = ordersDao.getBillByOrderCode(tenancyId, storeId, orderCode);
							if (null != billJson && false == billJson.isEmpty())
							{
								if (billJson.containsKey("bill_num") && Tools.hv(billJson.optString("bill_num")) && billJson.optString("bill_num").equals(orderState.getBill_num()))
								{
									orderState.setBill_num(billJson.optString("bill_num"));
								}
							// 账单取消
							JSONObject cancelJson = new JSONObject();
							cancelJson.put("report_date", reportDate.optString("report_date"));
							cancelJson.put("opt_num", optNum);
							cancelJson.put("pos_num", posNum);
							cancelJson.put("shift_id", dutyOrder.optString("shift_id"));
							cancelJson.put("bill_num", orderState.getBill_num());
							cancelJson.put("reason_id", 0);
							cancelJson.put("manager_id", "0");
							cancelJson.put("opt_login_number", "0");
							cancelJson.put("isprint", "N");
							cancelJson.put("is_online_payment", "1");

							List<JSONObject> cancelList = new ArrayList<JSONObject>();
							cancelList.add(cancelJson);

							Data cancelData = Data.get();
							cancelData.setTenancy_id(tenancyId);
							cancelData.setStore_id(storeId);
							cancelData.setType(Type.CANCBILL);
							cancelData.setData(cancelList);

							Data cancelResultData = Data.get();
							JSONObject printJson = new JSONObject();
							posDishService.cancelBill(cancelData, codeService, cancelResultData, printJson);
						}
							else
							{
								logger.info("============>订单取消冲减失败: 未找到订单["+orderCode+"]关联的账单,未生成冲减单");
							}
                        }
						orderState.setOrder_state(com.tzx.orders.base.constant.Constant.ORDER_STATE_CANCEL);
						orderState.setPayment_state(PAYMENT_STATE_REFUND);
						orderState.setReceive_time_cancellation(DateUtil
								.getNowDateYYDDMMHHMMSS());

						ordersDao.updateOrderInfo(tenancyId, storeId,
								JSONObject.fromObject(orderState));
					} catch (Exception e) {
						logger.error("======订单重新取消失败======\n", e);
						throw e;
					}
				}
			}
        }
	}
	
		
	public void createBillOfReplaceModify(String tenancyId,int storeId, JSONObject data){
        JSONArray dataList = data.optJSONArray("data");
        for (Object obj : dataList) {
            JSONObject json = JSONObject.fromObject(obj);
            JSONObject orderJson = json.optJSONObject("order_list");
            // 查询订单是否是新订单
            String orderCode = orderJson.optString("order_code").intern();
            String chanel = orderJson.optString("chanel");


            TransactionStatus takeTs = getTransctionStatus(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
            JSONObject para = null;
            JSONObject printBuffer = null;
            try {
                para = ordersManagementService.getPosOptState( tenancyId, storeId);
                para.put("order_code", orderCode);
                para.put("channel", chanel);
                para.put("single_time", orderJson.optString("single_time"));
                if (orderJson.containsKey("oper")) {
                    para.put("oper", orderJson.optString("oper"));
                }
                //第三方  订单转账单
                if (THIRD_CHANNEL.contains(chanel) ) {//&& "1".equals(OrderUtil.getSysPara(tenancyId, storeId, "order_translate_bill_mode"))
                    printBuffer = ordersManagementService.createPosBillByOrder(tenancyId, storeId, para);
                } else {
                    printBuffer = ordersManagementService.takeOrders(tenancyId, storeId, para);
                }
                transactionManager.commit(takeTs);

/*                if (null != printBuffer) {
                    ordersManagementService.printOrderBuffer(tenancyId, storeId, printBuffer);
                }*/
            } catch (Exception e) {
                logger.error("修改订单配送失败!", e);
                transactionManager.rollback(takeTs);
            }
      }
	}
	
    /**
	 * 组装沽清数据
	 *
	 * @param dataLst
	 * @return
	 */
	private List<Data> getGuQingData(JSONArray dataLst) {
		List list = new ArrayList();
		for (int i = 0; i < dataLst.size(); i++) {
			JSONObject jsonObject = dataLst.optJSONObject(i);
			int storeId = jsonObject.getInt("store_id");
			Data data = new Data();
			data.setStore_id(storeId);
			List list1 = new ArrayList();
			List list2 = new ArrayList();
			Map<String, Object> map = new HashMap<String, Object>();
			Map<String, Object> map2 = new HashMap<String, Object>();
			map2.put("item_id", jsonObject.getString("item_id"));
			map2.put("num", "1");
			list2.add(map2);
			map.put("mode", "0");
			map.put("item", list2);
			list1.add(map);
			data.setData(list1);
			list.add(data);
		}
		return list;
	}
	/**
	  * @Description: 查询本地DB是否存在对应表的版本号
	  * @Title:findTableNameId
	  * @param:@param tableName
	  * @param:@return
	  * @return: int
	  * @author: zhouquan
	  * @version: 1.0
	  * @Date: 2018年4月11日
	*/
	public List<JSONObject> findTableNameId(String tableName){
		List<JSONObject> list = processMessageDao.findTableNameId(tableName);
		return list;
	}
	
	/**
	  * @Description: 将下发的表和表的版本号存入本地DB
	  * @Title:insertTableNameAndVersion
	  * @param:@param tableName
	  * @param:@param tableVersion
	  * @return: void
	  * @author: zhouquan
	  * @version: 1.0
	  * @Date: 2018年4月11日
	*/
	public String insertTableNameAndVersion(String tenancyId ,int storeId, String tableName,  String tableVersion,String time,String recordCount){
		return processMessageDao.insertTableNameAndVersion(tenancyId ,storeId,tableName,tableVersion,time,recordCount);
	}

	/**
	  * @Description: 在本地DB修改下发的表和表的版本号
	  * @Title:updateTableNameAndVersion
	  * @param:@param tableName
	  * @param:@param tableVersion
	  * @return: void
	  * @author: zhouquan
	  * @version: 1.0
	  * @Date: 2018年4月11日
	*/
	public String updateTableNameAndVersion(String tenancyId ,int storeId, String tableName,  String tableVersion,String time,String recordCount){
		return processMessageDao.updateTableNameAndVersion(tenancyId ,storeId,tableName,tableVersion,time,recordCount);
	}

	@Override
	public String ifExistSequenceOrTable(String sequenceName) throws Exception {
		return processMessageDao.ifExistSequenceOrTable(sequenceName);
	}

	@Override
	public String initSeqValue(String sql) throws Exception {
		return processMessageDao.initSeqValue(sql);
	}

	@Override
	public void modifySendModeForOrders(String tenancyId, int storeId, JSONObject data) throws Exception
	{
		JSONArray dataList = data.optJSONArray("data");
        List<JSONObject> datas =new ArrayList<JSONObject>();
        for (Object obj : dataList) {
            List<OrderState> sendList = new ArrayList<OrderState>();

            JSONObject json = JSONObject.fromObject(obj);
            JSONObject orderJson = json.optJSONObject("order_list");
            // 查询订单是否是新订单
            String orderCode = orderJson.optString("order_code").intern();
            String chanel = orderJson.optString("chanel");
            //根据订单号查询订单
            List<JSONObject>  orders =  ordersDao.getIdByOrderCode(tenancyId, storeId, "cc_order_list",orderCode);
            if(orders!=null){
        		TransactionStatus status = null;
        		try {
        			status = getTransctionStatus(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        			
        			JSONObject order = orders.get(0);
        			orderJson.put("id",order.optInt("id"));
        			orderJson.put("order_state", order.optString("order_state"));
        			orderJson.put("bill_num", order.optString("bill_num"));
        			orderJson.put("upload_tag", 0);
        			ordersDao.updateIgnorCase(tenancyId, "cc_order_list", orderJson);
        			
                    if (json.containsKey("order_item") && !json.optJSONArray("order_item").isEmpty()) {
                        List<JSONObject> items = json.optJSONArray("order_item");
                        List<JSONObject> itemList =  ordersDao.getIdByOrderCode(tenancyId, storeId, "cc_order_item",orderCode);
                        for (JSONObject item : items) {
                        	for(JSONObject it:itemList){
                        		if(it.optInt("item_id") == item.optInt("item_id") && it.optInt("unit_id") == item.optInt("unit_id") ){
                                    item.put("id", it.optInt("id"));
                                    item.put("upload_tag", "0");
                                    ordersDao.updateIgnorCase(tenancyId, "cc_order_item",item);
                        		}
                        	}
                        }
                    }

                    if (json.containsKey("order_item_details") && !json.optJSONArray("order_item_details").isEmpty()) {
                        List<JSONObject> details = json.optJSONArray("order_item_details");
                        List<JSONObject> detailList =  ordersDao.getIdByOrderCode(tenancyId, storeId, "cc_order_item_details",orderCode);
                        for (JSONObject detail : details) {
                        	for(JSONObject it:detailList){
                        		if(it.optInt("item_id") == detail.optInt("item_id") && it.optInt("unit_id") == detail.optInt("unit_id") ){
                        			detail.put("id", it.optInt("id"));
                        			detail.put("upload_tag", "0");
                                    ordersDao.updateIgnorCase(tenancyId, "cc_order_item_details",detail);
                        		}
                        	}
                        }
                    }
        			
        			if (json.containsKey("order_repayment") && !json.optJSONArray("order_repayment").isEmpty())
        			{
        				List<JSONObject> pays =  ordersDao.getIdByOrderCode(tenancyId, storeId, "cc_order_repayment",orderCode);
        				if(pays!=null){
        					JSONObject pay = pays.get(0);
            				JSONObject repayment = json.optJSONArray("order_repayment").getJSONObject(0);
            				repayment.put("id", pay.optInt("id"));
            				repayment.put("upload_tag", 0);
            				ordersDao.updateIgnorCase(tenancyId, "cc_order_repayment",repayment);
        				}
        			}
                    if("12".equals(order.optString("order_state"))){ //如果原定但是异常修复完成订单
            			json.optJSONObject("order_list").put("oper", "add_modify");//异常订单标记 
                    }
                    datas.add(json);
                    data.put("data", datas);
        			transactionManager.commit(status);
        		} catch (Exception e1) {
        			transactionManager.rollback(status);
        			throw e1;
        		}
            }

            
            TransactionStatus takeTs = getTransctionStatus(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
            try {
            	this.cancleOrdersOfCancleModify(tenancyId, storeId,data);
                transactionManager.commit(takeTs);
            }  catch (Exception e) {
                transactionManager.rollback(takeTs);
            }
            
            this.createBillOfReplaceModify(tenancyId, storeId,data);

        }
		
	}
	
	
	/**
	  * @Description: 处理外卖订单退款
	  * @Title:refundOrders
	  * @param:@param tenancyId
	  * @param:@param storeId
	  * @param:@param data
	  * @param:@throws Exception
	  * @return: void
	  * @author: shenzhanyu
	  * @version: 1.0
	  * @Date: 2018年5月21日
	 */
	public  void refundOrders(String tenancyId,int storeId,JSONObject data) throws Exception {
		JSONArray dataList = data.optJSONArray("data");
		boolean isAfterChangeShift = data.optBoolean("is_after_change_shift");
		for (Object obj : dataList) {
			try {
				List<JSONObject> sendList = new ArrayList<JSONObject>();
				JSONObject json = JSONObject.fromObject(obj);
				JSONObject orderJson = json.optJSONObject("order_list");
				// 查询订单是否是新订单
				String orderCode = orderJson.optString("order_code").intern();
				//订单状态
				String currentOrderState = orderJson.optString("order_state");
				//订单渠道
				String chanel = orderJson.optString("chanel");
				//退款类型
				String refund_type = orderJson.optString("refund_type");
				//下单时间
				String single_time = orderJson.optString("single_time");

				//业务报表数据
				JSONObject para = ordersManagementService.getPosOptState(tenancyId, storeId, isAfterChangeShift);
				para.put("order_code", orderCode);
				para.put("channel", chanel);
				para.put("single_time", single_time);
				JSONObject printBuffer = null;
				TransactionStatus status = null;
				OrderState orderState = ordersDao.getOrderStateByOrderCode(tenancyId, storeId, orderCode);
				if (null == orderState) {//申请退款，POS接收到订单
					logger.info("orderState不存在，接收外卖退款申请信息 " + json);
					if (com.tzx.orders.base.constant.Constant.ORDER_STATE_REFUND_APPLY.equals(currentOrderState) || com.tzx.orders.base.constant.Constant.ORDER_STATE_REFUND_AGREE.equals(currentOrderState) || com.tzx.orders.base.constant.Constant.ORDER_STATE_REFUND_DISAGREE.equals(currentOrderState) || com.tzx.orders.base.constant.Constant.ORDER_STATE_REFUND_CANCEL.equals(currentOrderState)) {
						if (com.tzx.orders.base.constant.Constant.ORDER_STATE_REFUND_APPLY.equals(currentOrderState)) {
							currentOrderState = com.tzx.orders.base.constant.Constant.ORDER_STATE_REFUND_RECEIVED;
						} else if (com.tzx.orders.base.constant.Constant.ORDER_STATE_REFUND_CANCEL.equals(currentOrderState)) {
							currentOrderState = com.tzx.orders.base.constant.Constant.ORDER_STATE_REFUND_CANCEL;
						} else if (com.tzx.orders.base.constant.Constant.ORDER_STATE_REFUND_DISAGREE.equals(currentOrderState)) {
							currentOrderState = com.tzx.orders.base.constant.Constant.ORDER_STATE_REFUND_DISAGREE;
						} else if (com.tzx.orders.base.constant.Constant.ORDER_STATE_REFUND_AGREE.equals(currentOrderState)) {
							currentOrderState = com.tzx.orders.base.constant.Constant.ORDER_STATE_REFUND_AGREE;
						}

						orderJson.put("order_state", currentOrderState);
						orderJson.put("report_date", para.optString("report_date"));
						orderJson.put("upload_tag", "0");
						json.put("order_list", orderJson);

						if (json.containsKey("order_item") && !json.optJSONArray("order_item").isEmpty()) {
							List<JSONObject> items = json.optJSONArray("order_item");
							List<JSONObject> itemArray = new ArrayList<JSONObject>();
							for (JSONObject item : items) {
								item.put("report_date", para.optString("report_date"));
								item.put("upload_tag", "0");
								itemArray.add(item);
							}
							json.put("order_item", itemArray);
						}

						if (json.containsKey("order_repayment") && !json.optJSONArray("order_repayment").isEmpty()) {
							List<JSONObject> payments = json.optJSONArray("order_repayment");
							List<JSONObject> paymentArray = new ArrayList<JSONObject>();
							for (JSONObject payment : payments) {
								payment.put("report_date", para.optString("report_date"));
								payment.put("upload_tag", "0");
								paymentArray.add(payment);
							}
							json.put("order_repayment", paymentArray);
						}

						try {
							status = getTransctionStatus(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
							ordersDao.addOrderInfo(tenancyId, storeId, json);//保存cc_表信息
							transactionManager.commit(status);
						} catch (Exception e1) {
							transactionManager.rollback(status);
							throw e1;
						}

						JSONObject jsonOb = new JSONObject();
						jsonOb.put("chanel", chanel);
						jsonOb.put("order_state", currentOrderState);
						jsonOb.put("order_code", orderCode);
						String operator = "";
						if (!"null".equals(orderJson.optString("refund_operator")) && orderJson.optString("refund_operator") != null) {
							operator = orderJson.optString("refund_operator");
						}
						jsonOb.put("operator", operator);
						jsonOb.put("reason", orderJson.optString("orderJson"));

						//更新总部状态
						OrderState originalOrderState = ordersDao.getOrderStateByOrderCode(tenancyId, storeId, orderCode.substring(0, orderCode.length() - 2));
						if (com.tzx.orders.base.constant.Constant.ORDER_STATE_REFUND_RECEIVED.equals(currentOrderState)) {
							orderState = ordersDao.getOrderStateByOrderCode(tenancyId, storeId, orderCode);
							int notifyTarget = NOTIFY_POS;
							if (originalOrderState != null && (originalOrderState.getOrder_state().equals("10") || originalOrderState.getOrder_state().equals("11") || originalOrderState.getOrder_state().equals("12"))) {
								notifyTarget = NOTIFY_ALL;
							}
							OrderUtil.changeOrderState(tenancyId, storeId, orderState, currentOrderState, notifyTarget);
							sendList.add(jsonOb);
							OrderUtil.sendOrderState(tenancyId, storeId, Type.ORDER, Oper.update, sendList);
						}

						// 发送消息 提醒退款  提醒去平台处理
						if (originalOrderState != null && (originalOrderState.getOrder_state().equals("10") || originalOrderState.getOrder_state().equals("11") || originalOrderState.getOrder_state().equals("12"))) {
							List<String> msgList = new ArrayList<String>();
							msgList.add(orderCode);
							JSONObject msg = new JSONObject();
							msg.put("order_state", currentOrderState);
							msg.put("order_code", msgList);

							// 响铃提醒
							List<JSONObject> cometList = new ArrayList<JSONObject>();
							cometList.add(msg);
							Data cometData = new Data();
							cometData.setTenancy_id(tenancyId);
							cometData.setStore_id(storeId);
							cometData.setType(Type.ORDER);
							cometData.setOper(Oper.add);
							cometData.setSuccess(true);
							cometData.setData(cometList);

							//POS用到UDP
							String port = new CometDataNoticeClientRunnable(cometData).getPort();
							SendUdpToPos.sendMessagePos(JSONObject.fromObject(cometData).toString(), port);
							logger.debug("----外卖退款推送消息：" + JSONObject.fromObject(cometData).toString() + ", 端口号：" + port);
						}

						if (com.tzx.orders.base.constant.Constant.ORDER_STATE_REFUND_CANCEL.equals(currentOrderState)) {
							try {
								status = getTransctionStatus(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
								String refund_oper_time = orderJson.optString("refund_oper_time");
								if (refund_oper_time == null || "null".equals(refund_oper_time) || "".equals(refund_oper_time)) {
									refund_oper_time = DateUtil.getNowDateYYDDMMHHMMSS();
								}
								orderState = ordersDao.getOrderStateByOrderCode(tenancyId, storeId, orderCode);
								orderState.setRefund_oper_time(refund_oper_time);//操作是否同意、时间
								OrderUtil.changeOrderState(tenancyId, storeId, orderState, currentOrderState, NOTIFY_NONE);
								transactionManager.commit(status);
							} catch (Exception e1) {
								transactionManager.rollback(status);
								throw e1;
							}
						}

						if ("1".equals(refund_type)) {//部分退款
							if (com.tzx.orders.base.constant.Constant.ORDER_STATE_REFUND_AGREE.equals(currentOrderState)) {//同意部分退款
								logger.info("同意部分退款：" + json);
								//订单装账单
								boolean result = this.isExitsBFTCJ(tenancyId, storeId, SysDictionary.BILL_STATE_BFT04, orderCode);
								if (result) {
									printBuffer = ordersManagementService.refundOrders(tenancyId, storeId, para); //生产部分退整单
								}
								if (null != printBuffer) {
									logger.info("同意部分退款打印信息  ：：： " + printBuffer);
									ordersManagementService.refundOrderBuffer(tenancyId, storeId, printBuffer);
								}
							} else if (com.tzx.orders.base.constant.Constant.ORDER_STATE_REFUND_CANCEL.equals(currentOrderState)) {//用户取消部分退申请
								logger.info("用户取消部分退款申请：" + json);
								orderState = ordersDao.getOrderStateByOrderCode(tenancyId, storeId, orderCode);//查询部分退订单
								if (orderState != null) {//部分退订单是否存在
									JSONObject billJson = ordersDao.getBillByOrderCode(tenancyId, storeId, orderCode, SysDictionary.BILL_STATE_BFT04);
									if (null != billJson && !billJson.isEmpty()) {
										boolean result = this.isExitsBFTCJ(tenancyId, storeId, SysDictionary.BILL_STATE_BFTCJ01, orderCode);
										if (result) {
											ordersManagementService.writeDownsRefundOrders(tenancyId, storeId, orderState);
										}
									}
								}
							}
						} else if ("2".equals(refund_type)) {//整单退款
							logger.info("接收外卖整单退信息：：：" + json);
							//查询是否存在部分退款,若存在对部分退款进行冲减
							if (com.tzx.orders.base.constant.Constant.ORDER_STATE_REFUND_AGREE.equals(currentOrderState) || com.tzx.orders.base.constant.Constant.ORDER_STATE_REFUND_CANCEL.equals(currentOrderState)) {
								if (orderCode.substring(orderCode.length() - 2, orderCode.length()).equals("T2")) {
									orderCode = orderCode.substring(0, orderCode.length() - 1) + "1";
									logger.info("整单退时对部分退冲减,部分退订单号 ：：：" + orderCode);
									orderState = ordersDao.getOrderStateByOrderCode(tenancyId, storeId, orderCode);
									if (orderState != null) {
										logger.info("整单退时对部分退冲减：：：" + orderState);
										//判断是否存在部分退款，如果存在进行冲减
										//1.对部分退款冲减
										//部分退是否存在冲减单
										boolean result = this.isExitsBFTCJ(tenancyId, storeId, SysDictionary.BILL_STATE_BFTCJ01, orderCode);
										if (result) {//不存在部分退冲减
											JSONObject billJson = ordersDao.getBillByOrderCode(tenancyId, storeId, orderCode, SysDictionary.BILL_STATE_BFT04);
											if (billJson == null) {//部分退账单不存在
												para.put("order_code", orderCode);
												ordersManagementService.refundOrders(tenancyId, storeId, para);//部分退转账单
												ordersManagementService.writeDownsRefundOrders(tenancyId, storeId, orderState);//部分退冲减
												logger.info("部分退款账单不存在，先部分退转账单，再冲减");
											} else {
												ordersManagementService.writeDownsRefundOrders(tenancyId, storeId, orderState);
												logger.info("部分退账单记录存在，部分退冲减");
											}
										}
									}
								}
							}
						}
					}
				} else {
					logger.info("存在orderState时");
					try {
						status = getTransctionStatus(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
						String refund_oper_time = orderJson.optString("refund_oper_time");
						if (refund_oper_time == null || "null".equals(refund_oper_time) || "".equals(refund_oper_time)) {
							refund_oper_time = DateUtil.getNowDateYYDDMMHHMMSS();
						}
						orderState.setRefund_oper_time(refund_oper_time);//操作是否同意、时间
						OrderUtil.changeOrderState(tenancyId, storeId, orderState, currentOrderState, NOTIFY_NONE);
						transactionManager.commit(status);
					} catch (Exception e1) {
						transactionManager.rollback(status);
						throw e1;
					}
					if ("1".equals(refund_type)) {//部分退款
						if (com.tzx.orders.base.constant.Constant.ORDER_STATE_REFUND_AGREE.equals(currentOrderState)) {//|| com.tzx.orders.base.constant.Constant.ORDER_STATE_REFUND_CANCEL.equals(currentOrderState)
							logger.info("同意部分退款：" + json);
							//订单装账单
							boolean result = this.isExitsBFTCJ(tenancyId, storeId, SysDictionary.BILL_STATE_BFT04, orderCode);
							if (result) {
								printBuffer = ordersManagementService.refundOrders(tenancyId, storeId, para);
							}
							if (null != printBuffer) {
								logger.info("同意部分退款打印信息  ：：： " + printBuffer);
								ordersManagementService.refundOrderBuffer(tenancyId, storeId, printBuffer);
							}
						} else if (com.tzx.orders.base.constant.Constant.ORDER_STATE_REFUND_CANCEL.equals(currentOrderState)) {//用户取消部分退申请
							logger.info("用户取消部分退款申请：" + json);
							orderState = ordersDao.getOrderStateByOrderCode(tenancyId, storeId, orderCode);//查询部分退订单
							if (orderState != null) {//部分退订单是否存在
								boolean result = this.isExitsBFTCJ(tenancyId, storeId, SysDictionary.BILL_STATE_BFTCJ01, orderCode);
								if (result) {//不存在部分退冲减
									JSONObject billJson = ordersDao.getBillByOrderCode(tenancyId, storeId, orderCode, SysDictionary.BILL_STATE_BFT04);
									if (null != billJson) {//部分退账单不存在
										ordersManagementService.writeDownsRefundOrders(tenancyId, storeId, orderState);
									}
								}
							}
						}
					} else if ("2".equals(refund_type)) {//整单退款
						logger.info("接收外卖整单退信息：：： " + json);
						//查询是否存在部分退款,若存在对部分退款进行冲减 || com.tzx.orders.base.constant.Constant.ORDER_STATE_REFUND_CANCEL.equals(currentOrderState)
						if (com.tzx.orders.base.constant.Constant.ORDER_STATE_REFUND_AGREE.equals(currentOrderState) || com.tzx.orders.base.constant.Constant.ORDER_STATE_REFUND_CANCEL.equals(currentOrderState)) {
							if (orderCode.substring(orderCode.length() - 2, orderCode.length()).equals("T2")) {
								orderCode = orderCode.substring(0, orderCode.length() - 1) + "1";
								logger.info("整单退时对部分退冲减,部分退订单号 ：：： " + orderCode);
								orderState = ordersDao.getOrderStateByOrderCode(tenancyId, storeId, orderCode);
								if (orderState != null) {
									logger.info("整单退时对部分退冲减 ：：： " + orderState);
									//判断是否存在部分退款，如果存在进行冲减
									//1.对部分退款冲减
									//部分退是否存在冲减单
									boolean result = this.isExitsBFTCJ(tenancyId, storeId, SysDictionary.BILL_STATE_BFTCJ01, orderCode);
									if (result) {//不存在部分退冲减
										JSONObject billJson = ordersDao.getBillByOrderCode(tenancyId, storeId, orderCode, SysDictionary.BILL_STATE_BFT04);
										logger.info("整单退时对部分退冲减，是否存在部分退账单 ：：：  " + ((billJson == null) ? "不存在部分退账单" : "存在部分退账单"));
										if (billJson == null) {
											para.put("order_code", orderCode);
											ordersManagementService.refundOrders(tenancyId, storeId, para);
											ordersManagementService.writeDownsRefundOrders(tenancyId, storeId, orderState);
											logger.info("部分退账单记录不存在，部分退先转账单，再冲减");
										} else {
											ordersManagementService.writeDownsRefundOrders(tenancyId, storeId, orderState);
											logger.info("部分退账单记录存在，部分退冲减");
										}
									}
								}
							}
						}
					}
				}
			} catch (Exception e) {
				throw e;
			}
		}
	}
	
	
	/**
	  * @Description: 部分退冲减是否存在
	  * isExitsBFTCJ
	  * @param:@param tenentid
	  * @param:@param storeid
	  * @param:@param para
	  * @param:@return
	  * @param:@throws Exception
	  * @return: JSONObject
	  * @author: shenzhanyu
	  * @version: 1.0
	  * @Date: 2018年8月2日
	 */
	public  Boolean isExitsBFTCJ(String tenentid, int storeid, String bill_state, String order_code) throws Exception {

		StringBuffer sqlBuf = new StringBuffer();
		sqlBuf.append(" SELECT * FROM v_pos_bill WHERE bill_state = '"+bill_state+"' AND order_num='"
				+ order_code + "' AND tenancy_id ='" + tenentid + "' AND store_id =" + storeid);

		List<JSONObject> resultList = ordersDao.query4Json(tenentid, sqlBuf.toString());

		if (resultList != null && resultList.size() > 0) {
			return false;
		} else {
			return true;
		}

	}

	@Override
	public void deliverExcepetionForOrders(String tenancyId, int storeId, JSONObject data) throws Exception {
		JSONArray dataList = data.optJSONArray("data");
        for(Object obj : dataList){ //"data": [{ "order_code": "","channel":""}],
        	JSONObject json = JSONObject.fromObject(obj);
        	String orderCode = json.optString("order_code");
        	String deliver_state = "";
        	String channel = json.optString("channel");
        	
        	//修改接单状态  delivery_state 1 已接单  0 未接单
        	ordersDao.udateOrderDelivery(tenancyId,storeId,orderCode);
        	//查询订单状态
        	OrderState orderState = ordersDao.getOrderStateByOrderCode(tenancyId,storeId,orderCode);
        	
        	
        	// 发送消息
            List<String> msgList = new ArrayList<String>();
            msgList.add(orderCode);
            JSONObject msg = new JSONObject();
            //msg.put("deliver_state", deliver_state);
            msg.put("order_code", msgList);
            msg.put("channel", channel);
            msg.put("order_state",orderState.getOrder_state());
			msg.put("delivery_state","07");
            // msg.put("chanel", orderJson.optString("chanel"));
            // msg.put("is_online_payment",
            // orderJson.optString("is_online_payment"));
             Comet4jUtil.sendMessage2All(Comet4jUtil.CALLCENTER_ORDER,
             msg.toString());

            // 响铃提醒
            List<JSONObject> cometList = new ArrayList<JSONObject>();
            cometList.add(msg);
            Data cometData = new Data();
            cometData.setTenancy_id(tenancyId);
            cometData.setStore_id(storeId);
            cometData.setType(Type.DELIVERY);
            cometData.setOper(Oper.add);
            cometData.setSuccess(true);
            cometData.setData(cometList);
		   /*Comet4jUtil.sendMessage2All(Comet4jUtil.POS_PC, JSONObject
				.fromObject(cometData).toString());*/

            //POS用到UDP
            String port = new CometDataNoticeClientRunnable(cometData).getPort();
            SendUdpToPos.sendMessagePos(JSONObject.fromObject(cometData).toString(), port);
            logger.debug("----推送消息：" + JSONObject.fromObject(cometData).toString() + ", 端口号：" + port);

        }
	}
	
	@Override
	public String ordersManagement(String tenancyId, int storeId, JSONObject data) throws Exception {
        String returnRes = "";
        String oper = data.getString("oper");
        if (Tools.judgeStoreStateOpen(tenancyId, storeId) == false) {
            logger.info("非营业时段不处理外卖订单消息，消息内容：" + data.toString());
            return "非营业时段不处理外卖订单消息";
        }
        JSONArray dataList = data.optJSONArray("data");

        String orderCode = UUID.randomUUID().toString();

        if (dataList != null && !dataList.isEmpty()) {

            JSONObject obj = JSONObject.fromObject(dataList.get(0));
            JSONObject orderJson = obj.optJSONObject("order_list");
            if (null != orderJson) {
                orderCode = orderJson.optString("order_code").intern();
            }
        }

        synchronized (orderCode) {
            if (Oper.add.name().equals(oper)) {
                logger.info("接收订单:" + data);
                try {
                    this.addOrders(tenancyId, storeId, data);
                    returnRes = "接收订单成功";
                    ack(data);
                } catch (Exception e) {
                    logger.error("==========接收订单失败!\n", e);
                    returnRes = "接收订单失败";
                }
            } else if (Oper.add_modify.name().equals(oper)) {
                logger.info("重新接收订单:" + data);
                try {
                    this.modifyOrder(tenancyId, storeId, data);
                    returnRes = "重新接收订单";
                    ack(data);
                } catch (Exception e) {
                    logger.error("==========重新接收订单!\n", e);
                    returnRes = "重新接收订单";
                }
            } else if (Oper.updatedish.name().equals(oper)) {
                logger.info("接收订单[加菜]:" + data);
                try {
                    this.updateDish(tenancyId, storeId, data);
                    returnRes = "接收订单成功";
                    ack(data);
                } catch (Exception e) {
                    logger.error("==========接收订单失败!\n", e);
                    returnRes = "接收订单失败";
                }
            } else if (Oper.cancle.name().equals(oper)) {
                try {
                    logger.info("订单取消得到的消息是：" + data);
                    this.cancleOrders(tenancyId, storeId, data);
                    returnRes = "取消订单成功";
                    ack(data);
                } catch (Exception e) {
                    logger.info("得到的消息是：" + data);
                    logger.error("取消订单失败", e);
                    e.printStackTrace();
                    returnRes = "取消订单失败";
                }
            } else if (Oper.cancle_modify.name().equals(oper)) {
                try {
                    logger.info("订单重新取消得到的消息是：" + data);
                    this.cancleModifyOrders(tenancyId, storeId, data);
                    returnRes = "订单重新取消";
                    ack(data);
                } catch (Exception e) {
                    logger.info("得到的消息是：" + data);
                    logger.error("订单重新取消失败", e);
                    e.printStackTrace();
                    returnRes = "订单重新取消失败";
                }
            } else if (Oper.opinion.name().equals(oper)) {
                try {
                    this.opinionOrders(tenancyId, storeId, data);
                    returnRes = "投诉订单成功";
                    ack(data);
                } catch (Exception e) {
                    logger.info("得到的消息是：" + data);
                    logger.error("投诉订单失败", e);
                    e.printStackTrace();
                    returnRes = "投诉订单失败";
                }
            } else if (Oper.complete.name().equals(oper)) {
                try {
                    this.completeOrders(tenancyId, storeId, data);
                    returnRes = "订单完成";
                    ack(data);
                } catch (Exception e) {
                    logger.info("得到的消息是：" + data);
                    logger.error("完成订单失败", e);
                    e.printStackTrace();
                    returnRes = "完成订单失败";
                }
            } else if (Oper.replace_modify.name().equals(oper)) {
                try {
                    logger.info("修改订单配送消息是：" + data);
                    this.modifySendModeForOrders(tenancyId, storeId, data);
                    returnRes = "修改订单配送成功";
                    ack(data);
                } catch (Exception e) {
                    logger.error("修改订单配送失败!", e);
                    returnRes = "修改订单配送失败";
                }
            } else if (Oper.refund.name().equals(oper)) {
                try {
                    logger.info("退款订单消息是：" + data);
                    this.refundOrders(tenancyId, storeId, data);
                    returnRes = "退款订单成功";
                    ack(data);
                } catch (Exception e) {
                    logger.error("退款订单失败!\n", e);
                    returnRes = "退款订单失败";
                }
            } else if (Oper.deliver_exception.name().equals(oper)) {
                try {
                    logger.info("配送异常消息：" + data);
                    this.deliverExcepetionForOrders(tenancyId, storeId, data);
                    ack(data);
                } catch (Exception e) {
                    logger.error("配送异常失败!\n", e);
                    returnRes = "配送异常失败";
                }
            } else {
                returnRes = "Oper无效";
            }
        }
        return returnRes;
    }

	
	
    private void ack(JSONObject getJson) {
        try {
            if (getJson.containsKey("ack_data")) { 

                JSONArray data = getJson.getJSONArray("ack_data");
                getJson.put("data", data);

                getJson.discard("ack_data");
                getJson.put("oper","ack");
                String reqURL = PosPropertyUtil.getMsg("saas.url") + "/orderRest/post";
                HttpUtil.post(reqURL, getJson, 3);

            }
        } catch (HttpRestException e) {
            logger.error("order ack error",e);
        }
    }
}
