package com.tzx.pos.po.springjdbc.dao.imp;

import java.sql.Timestamp;
import java.util.Date;

import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Repository;

import com.tzx.base.entity.PosCashierReceiveLogEntity;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.dao.imp.BaseDaoImp;
import com.tzx.pos.po.springjdbc.dao.PosCashierReceiveDao;

@Repository(PosCashierReceiveDao.NAME)
public class PosCashierReceiveDaoImp extends BaseDaoImp implements PosCashierReceiveDao
{

	@Override
	public Double getCashierReceiveDifferenceAmount(String tenantId, Integer storeId, Date reportDate) throws Exception
	{
		Double differenceAmount = 0d;
		StringBuilder cashierRcvSql = new StringBuilder();
		cashierRcvSql.append(" select (coalesce(sum(bl.bill_amount),0)-coalesce(sum(pcrl.receive_amount),0)) as difference_amount");
		cashierRcvSql.append(" from ( ");
		cashierRcvSql.append("       select cast('"+SysDictionary.SERVICE_TYPE_CONSUME+"' as text) as service_type,bi.tenancy_id,bi.store_id,bi.report_date,bi.shift_id,cast(bi.cashier_num as int) as waiter_id,coalesce(sum(pbp.currency_amount),0) AS bill_amount");
		cashierRcvSql.append("       from pos_bill_payment pbp left join pos_bill bi on pbp.tenancy_id=bi.tenancy_id and pbp.store_id=bi.store_id and pbp.bill_num=bi.bill_num");
		cashierRcvSql.append("       left join hq_devices hd on bi.tenancy_id = hd.tenancy_id and bi.store_id = hd.store_id and bi.pos_num = hd.devices_code where (hd.show_type = 'YD' or hd.show_type = 'PAD') AND bi.cashier_num <>'"+com.tzx.orders.base.constant.Constant.WM_OPT_NUM+"'");
		cashierRcvSql.append("       group by bi.tenancy_id,bi.store_id,bi.report_date,bi.shift_id,bi.cashier_num");
		cashierRcvSql.append("       union all");
		cashierRcvSql.append("       select cast('"+SysDictionary.SERVICE_TYPE_RECHARGE+"' as text) as service_type,cz.tenancy_id,cz.store_id,cz.business_date,cz.shift_id,cz.operator_id as waiter_id,coalesce(sum(cz.main_trading),0) AS bill_amount");
		cashierRcvSql.append("       from (");
		cashierRcvSql.append("              select tl.tenancy_id,tl.store_id,tl.business_date,tl.shift_id,tl.operator_id,pl.third_bill_code,pl.bill_code,tl.main_trading,pl.pay_money,pl.payment_id");
		cashierRcvSql.append("              from crm_card_payment_list pl left join crm_card_trading_list tl on pl.tenancy_id=tl.tenancy_id and pl.third_bill_code=tl.third_bill_code and tl.operat_type in ('"+SysDictionary.OPERAT_TYPE_CZ+"','"+SysDictionary.OPERAT_TYPE_FCZ+"')");
		cashierRcvSql.append("              left join pos_opt_state ot on tl.tenancy_id=ot.tenancy_id and tl.store_id=ot.store_id and tl.business_date=ot.report_date and cast(tl.operator_id as text) =ot.opt_num and ot.content='"+SysDictionary.OPT_STATE_KSSY+"' where ot.id is null");
		cashierRcvSql.append("              group by tl.tenancy_id,tl.store_id,tl.business_date,tl.shift_id,tl.operator_id,pl.third_bill_code,pl.bill_code,tl.main_trading,pl.pay_money,pl.payment_id");
		cashierRcvSql.append("       ) cz group by cz.tenancy_id,cz.store_id,cz.business_date,cz.shift_id,cz.operator_id");
		cashierRcvSql.append(" ) as bl");
		cashierRcvSql.append(" left join ( "); 
		cashierRcvSql.append("       select pbp.tenancy_id,pbp.store_id,pbp.report_date,pbp.pay_shift_id,pbp.waiter_id,sum(pbp.amount) as receive_amount,pbp.service_type");
		cashierRcvSql.append("       from pos_cashier_receive_log pbp group by pbp.tenancy_id,pbp.store_id,pbp.report_date,pbp.pay_shift_id,pbp.waiter_id,pbp.service_type");
		cashierRcvSql.append(" ) as pcrl on bl.tenancy_id = pcrl.tenancy_id and bl.store_id = pcrl.store_id and bl.report_date = pcrl.report_date and bl.shift_id = pcrl.pay_shift_id and bl.waiter_id = pcrl.waiter_id and bl.service_type = pcrl.service_type");
		cashierRcvSql.append(" where bl.tenancy_id = '").append(tenantId).append("' and bl.store_id = '").append(storeId).append("' and bl.report_date='").append(reportDate).append("'");
		
		SqlRowSet rs = this.query4SqlRowSet(cashierRcvSql.toString());
		if (rs.next())
		{
			differenceAmount = rs.getDouble("difference_amount");
		}
		return differenceAmount;
	}

	@Override
	public Double getReceiveAmountByPayment(String tenancyId, int storeId, Date reportDate, Integer waiterId, Integer paymentId) throws Exception
	{
		StringBuilder cashierRcvSql = new StringBuilder();
		cashierRcvSql.append(" select (coalesce(sum(bl.bill_amount),0)-coalesce(sum(pcrl.receive_amount),0)) as receive_amount");
		cashierRcvSql.append(" from (   ");
		cashierRcvSql.append("       select cast('"+SysDictionary.SERVICE_TYPE_CONSUME+"'as text)as service_type,bi.tenancy_id,bi.store_id,bi.report_date,bi.shift_id,cast(bi.cashier_num as int) as waiter_id,py.jzid,sum(py.currency_amount)as bill_amount,sum(case when bi.bill_property='"+SysDictionary.BILL_PROPERTY_CLOSED+"' then 0 else py.currency_amount end) as unpaid_amount");
		cashierRcvSql.append("       from pos_bill_payment py left join pos_bill bi on py.tenancy_id=bi.tenancy_id and py.store_id=bi.store_id and py.bill_num=bi.bill_num");
		cashierRcvSql.append("       left join hq_devices hd on bi.tenancy_id = hd.tenancy_id and bi.store_id = hd.store_id and bi.pos_num = hd.devices_code");
		cashierRcvSql.append("       where (hd.show_type = 'YD' or hd.show_type = 'PAD') AND bi.cashier_num <>'"+com.tzx.orders.base.constant.Constant.WM_OPT_NUM+"'");
		cashierRcvSql.append("       group by bi.tenancy_id,bi.store_id,bi.report_date,bi.shift_id,bi.cashier_num,py.jzid");
		cashierRcvSql.append(") bl");
		cashierRcvSql.append(" left join (  ");
		cashierRcvSql.append("       select tenancy_id,store_id,report_date,pay_shift_id,waiter_id,payment_id,sum(amount) as receive_amount,bi.service_type from pos_cashier_receive_log bi");
		cashierRcvSql.append("       group by tenancy_id,store_id,report_date,pay_shift_id,waiter_id,payment_id,bi.service_type) as pcrl");
		cashierRcvSql.append(" on bl.tenancy_id = pcrl.tenancy_id and bl.store_id = pcrl.store_id and bl.report_date = pcrl.report_date and bl.shift_id = pcrl.pay_shift_id and bl.waiter_id = pcrl.waiter_id and bl.jzid = pcrl.payment_id and bl.service_type=pcrl.service_type");
		cashierRcvSql.append(" where bl.tenancy_id= ? and bl.store_id = ? and bl.report_date =? and bl.waiter_id=? and bl.jzid = ? ");
		
		SqlRowSet rs = this.query4SqlRowSet(cashierRcvSql.toString(), new Object[]{tenancyId,storeId,reportDate,waiterId,paymentId});
		Double receiveAmount =0d;
		if(rs.next())
		{
			receiveAmount = rs.getDouble("receive_amount");
			if(receiveAmount.isNaN())
			{
				receiveAmount =0d;
			}
		}
		return receiveAmount;
	}
	
	@Override
	public Double getReceiveAmountByRecharge(String tenancyId, int storeId, Date reportDate, Integer waiterId, Integer paymentId) throws Exception
	{
		StringBuilder cashierRcvSql = new StringBuilder();
		cashierRcvSql.append(" select (coalesce(sum(bl.bill_amount),0)-coalesce(sum(pcrl.receive_amount),0)) as receive_amount");
		cashierRcvSql.append(" from (   ");
		cashierRcvSql.append("       select cast('"+SysDictionary.SERVICE_TYPE_RECHARGE+"' as text) as service_type,cz.tenancy_id,cz.store_id,cz.business_date as report_date,cz.shift_id,cz.operator_id as waiter_id,cz.payment_id as jzid,coalesce(sum(cz.main_trading),0) AS bill_amount,0 as unpaid_amount");
		cashierRcvSql.append("       from (");
		cashierRcvSql.append("              select tl.tenancy_id,tl.store_id,tl.business_date,tl.shift_id,tl.operator_id,pl.third_bill_code,pl.bill_code,tl.main_trading,pl.pay_money,pl.payment_id");
		cashierRcvSql.append("              from crm_card_payment_list pl left join crm_card_trading_list tl on pl.tenancy_id=tl.tenancy_id and pl.third_bill_code=tl.third_bill_code and tl.operat_type in ('"+SysDictionary.OPERAT_TYPE_CZ+"','"+SysDictionary.OPERAT_TYPE_FCZ+"')");
		cashierRcvSql.append("              left join pos_opt_state ot on tl.tenancy_id=ot.tenancy_id and tl.store_id=ot.store_id and tl.business_date=ot.report_date and cast(tl.operator_id as text) =ot.opt_num and ot.content='"+SysDictionary.OPT_STATE_KSSY+"' ");
		cashierRcvSql.append("              where ot.id is null ");
		cashierRcvSql.append("              group by tl.tenancy_id,tl.store_id,tl.business_date,tl.shift_id,tl.operator_id,pl.third_bill_code,pl.bill_code,tl.main_trading,pl.pay_money,pl.payment_id");
		cashierRcvSql.append("       ) cz group by cz.tenancy_id,cz.store_id,cz.business_date,cz.shift_id,cz.operator_id,cz.payment_id");
		cashierRcvSql.append(") bl");
		cashierRcvSql.append(" left join (  ");
		cashierRcvSql.append("       select tenancy_id,store_id,report_date,pay_shift_id,waiter_id,payment_id,sum(amount) as receive_amount,bi.service_type from pos_cashier_receive_log bi");
		cashierRcvSql.append("       group by tenancy_id,store_id,report_date,pay_shift_id,waiter_id,payment_id,bi.service_type) as pcrl");
		cashierRcvSql.append(" on bl.tenancy_id = pcrl.tenancy_id and bl.store_id = pcrl.store_id and bl.report_date = pcrl.report_date and bl.shift_id = pcrl.pay_shift_id and bl.waiter_id = pcrl.waiter_id and bl.jzid = pcrl.payment_id and bl.service_type=pcrl.service_type");
		cashierRcvSql.append(" where bl.tenancy_id= ? and bl.store_id = ? and bl.report_date =? and bl.waiter_id=? and bl.jzid = ? ");
		
		SqlRowSet rs = this.query4SqlRowSet(cashierRcvSql.toString(), new Object[]{tenancyId,storeId,reportDate,waiterId,paymentId});
		Double receiveAmount =0d;
		if(rs.next())
		{
			receiveAmount = rs.getDouble("receive_amount");
			if(receiveAmount.isNaN())
			{
				receiveAmount =0d;
			}
		}
		return receiveAmount;
	}

	@Override
	public int insertCashierReceiveLog(String tenancyId, int storeId, PosCashierReceiveLogEntity entity) throws Exception
	{
		StringBuilder updateCashierRcvSql = new StringBuilder(
				"insert into pos_cashier_receive_log (tenancy_id,store_id,report_date,shift_id,oper_type,waiter_id,waiter_name,device_num,amount,cashier_id,cashier_name,pos_num,last_updatetime,upload_tag,payment_id,pay_shift_id,service_type) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ");
		return this.update(updateCashierRcvSql.toString(), new Object[]
		{ tenancyId, storeId, entity.getReport_date(), entity.getShift_id(), entity.getOper_type(), entity.getWaiter_id(), entity.getWaiter_name(), entity.getDevice_num(), entity.getAmount(), entity.getCashier_id(), entity.getCashier_name(), entity.getPos_num(), entity.getLast_updatetime(),
				entity.getUpload_tag(), entity.getPayment_id(), entity.getPay_shift_id(), entity.getService_type() });
	}

	@Override
	public int insertReceiveDetailsByRecharge(String tenancyId, int storeId, PosCashierReceiveLogEntity entity,Timestamp queryTime) throws Exception
	{
		// TODO 自动生成的方法存根
		String receiveId = String.valueOf(entity.getLast_updatetime().getTime());

		StringBuilder insertSql = new StringBuilder("insert into pos_cashier_receive_log_details(tenancy_id,store_id,report_date,pay_shift_id,waiter_id,bill_num,payment_id,receive_id,shift_id,cashier_id,pos_num,last_updatetime,upload_tag,service_type)");
		insertSql.append(" select cz.tenancy_id,cz.store_id,cz.business_date,cz.shift_id as pay_shift_id,cz.operator_id as waiter_id,cz.third_bill_code bill_num,cz.payment_id,");
		insertSql.append(" ? receive_id,? shift_id,? cashier_id,? pos_num,? last_updatetime,'0' upload_tag,'" + SysDictionary.SERVICE_TYPE_RECHARGE + "' service_type from (");
		insertSql.append(" select tl.tenancy_id,tl.store_id,tl.business_date,tl.shift_id,tl.operator_id,pl.third_bill_code,pl.bill_code,tl.main_trading,pl.pay_money,pl.payment_id");
		insertSql.append(" from crm_card_payment_list pl left join crm_card_trading_list tl on pl.tenancy_id=tl.tenancy_id and pl.third_bill_code=tl.third_bill_code and tl.operat_type in ('" + SysDictionary.OPERAT_TYPE_CZ + "','" + SysDictionary.OPERAT_TYPE_FCZ + "')");
		insertSql.append(" left join pos_opt_state ot on tl.tenancy_id=ot.tenancy_id and tl.store_id=ot.store_id and tl.business_date=ot.report_date and cast(tl.operator_id as text) =ot.opt_num and ot.content='" + SysDictionary.OPT_STATE_KSSY + "'");
		insertSql.append(" where ot.id is null and tl.operate_time<=?");
		insertSql.append(" group by tl.tenancy_id,tl.store_id,tl.business_date,tl.shift_id,tl.operator_id,pl.third_bill_code,pl.bill_code,tl.main_trading,pl.pay_money,pl.payment_id ) cz");
		insertSql.append(" left join public.pos_cashier_receive_log_details rd on cz.tenancy_id = rd.tenancy_id and cz.store_id = rd.store_id and cz.business_date=rd.report_date and cz.third_bill_code = rd.bill_num and cz.payment_id = rd.payment_id");
		insertSql.append(" where rd.id is null and cz.tenancy_id = ? and cz.store_id = ? and cz.business_date=? and cz.operator_id=? and cz.payment_id=?");

		return this.update(insertSql.toString(), new Object[]
		{ receiveId, entity.getShift_id(), entity.getCashier_id(), entity.getPos_num(), entity.getLast_updatetime(), queryTime, tenancyId, storeId, entity.getReport_date(), entity.getWaiter_id(), entity.getPayment_id() });
	}
}
