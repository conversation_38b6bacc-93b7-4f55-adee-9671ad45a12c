package com.tzx.base.bo.dto;

import java.io.Serializable;

@SuppressWarnings("serial")
public class Version implements Serializable
{
	private String	versionno;
	private boolean	isnewest;
	private String	url;

	public String getVersionno()
	{
		return versionno;
	}

	public void setVersionno(String versionno)
	{
		this.versionno = versionno;
	}

	public boolean isIsnewest()
	{
		return isnewest;
	}

	public void setIsnewest(boolean isnewest)
	{
		this.isnewest = isnewest;
	}

	public String getUrl()
	{
		return url;
	}

	public void setUrl(String url)
	{
		this.url = url;
	}

}
