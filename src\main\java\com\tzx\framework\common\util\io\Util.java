package com.tzx.framework.common.util.io;
import java.io.BufferedInputStream;
//import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Enumeration;
import java.util.Map;
import java.util.Properties;
import java.util.jar.JarEntry;
import java.util.jar.JarFile;
import java.util.regex.Pattern;

/**
 * 
* <p>Title: 平台工具</p>
* <p>Description: </p>
* <p>Company: gxhk</p> 
* <AUTHOR>
* @date       Aug 4, 2009
 */
public class Util {

	/**
	 * 
	* <p>Title: 根据key读取value</p>
	* <p>Description: </p>
	* <AUTHOR>
	* @param filePath
	* @param key
	* @return
	 */
	public static String readValue(String filePath, String key) {
		Properties props = new Properties();
		try {
			InputStream in = new BufferedInputStream(new FileInputStream(
					filePath));
			props.load(in);
			String value = props.getProperty(key);
			//System.out.println(key + value);
			return value;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	/**
	 * 
	* <p>Title: 读取properties的全部信息</p>
	* <p>Description: </p>
	* <AUTHOR>
	* @param filePath
	 */
	@SuppressWarnings("unchecked")
	public static void readProperties(String filePath) {
		Properties props = new Properties();
		try {
			InputStream in = new BufferedInputStream(new FileInputStream(
					filePath));
			props.load(in);
			Enumeration<String> en = (Enumeration<String>) props.propertyNames();
			while (en.hasMoreElements()) {
				String key = (String) en.nextElement();
				String Property = props.getProperty(key);
				System.out.println(key + Property);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	/**
	 * 
	* <p>Title: 读取非Jar包中属性文件</p>
	* <p>Description: </p>
	* <AUTHOR>
	* @param map
	* @param filePath
	* @return
	 */
	@SuppressWarnings("unchecked")
	public static Map<String, String> getPropertiesMap(Map<String, String> map,String filePath) {
		Properties props = new Properties();
		try {
			InputStream in = new BufferedInputStream(new FileInputStream(
					filePath));
			props.load(in);
			Enumeration<String> en = (Enumeration<String>) props.propertyNames();
			while (en.hasMoreElements()) {
				String key = (String) en.nextElement();
				String Property = props.getProperty(key);
				map.put(key, Property);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return map;
	}
	
	/**
	 * 
	* <p>Title: 读取jar包中的属性文件</p>
	* <p>Description: </p>
	* <AUTHOR>
	* @param map
	* @param filePath
	* @return
	 */
	@SuppressWarnings("unchecked")
	public static Map<String, String> getPropertiesMapInJar(Map<String, String> map,String jarFilePath,String inFilePath) {
		Properties props = getPropertiesInJar( jarFilePath, inFilePath);
		Enumeration<Object> en = (Enumeration<Object>) props.propertyNames();
		while (en.hasMoreElements()) {
			String key = (String) en.nextElement();
			String Property = props.getProperty(key.trim());
			map.put(key, Property);
		}
		return map;
	}

	/**
	 * 读取jar包中的属性文件
	 * @param jarFilePath 示例:homePath+fileSepator+"WEB-INF"+fileSepator+"lib"+fileSepator+"gxhkPlatform.jar"
	 * @param proFileName 示例：com/gxhk/vo/action/platform/module/plat_module_listeners.properties
	 * <AUTHOR>
	 */
	private static Properties getPropertiesInJar(String jarFilePath, String proFileName) {
		InputStream in = null;
		try {
			JarFile jf = new JarFile(jarFilePath);
			String name = "";
	        Enumeration<JarEntry> enu = jf.entries();
	        while(enu.hasMoreElements()){
	            JarEntry element = (JarEntry) enu.nextElement();
	           name = element.getName();
	           //如果是所选属性文件读入输入流
	            if(name.indexOf(proFileName)!=-1){
	            	 JarEntry dbEntry = jf.getJarEntry(name);
	            	 in = jf.getInputStream(dbEntry); 
	            }
	        }
		} catch (Exception e) {
			e.printStackTrace();
		}
		Properties props = new Properties();
		if (null != in) {
			try {
				props.load(in);
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return props;
	}

	/**
	 * 
	* <p>Title: writeProperties</p>
	* <p>Description: 写入properties信息</p>
	* <AUTHOR>
	* @param filePath
	* @param parameterName
	* @param parameterValue
	 */
	public static void writeProperties(String filePath, String parameterName,
			String parameterValue) {
		Properties prop = new Properties();
		try {
			InputStream fis = new FileInputStream(filePath);
			//从输入流中读取属性列表（键和元素对）
			prop.load(fis);
			//调用 Hashtable 的方法 put。使用 getProperty 方法提供并行性。
			//强制要求为属性的键和值使用字符串。返回值是 Hashtable 调用 put 的结果。
			OutputStream fos = new FileOutputStream(filePath);
			prop.setProperty(parameterName, parameterValue);
			//以适合使用 load 方法加载到 Properties 表中的格式，
			//将此 Properties 表中的属性列表（键和元素对）写入输出流
			prop.store(fos, "Update '" + parameterName + "' value");
		} catch (IOException e) {
			System.err.println("Visit " + filePath + " for updating "
					+ parameterName + " value error");
		}
	}

	/**
	 * 
	* <p>Title: readXMLAndJsonFromRequestBody</p>
	* <p>Description:从request中读取xml和json数据 </p>
	* <AUTHOR>
	* @param request
	* @return
	 */
	/*public static String readXMLAndJsonFromRequestBody(HttpServletRequest request){
		StringBuffer stringBuf = new StringBuffer();
		try {
			BufferedReader reader = request.getReader();
			String line = null;
			while ((line = reader.readLine()) != null) {
				stringBuf.append(line);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return stringBuf.toString();
	}
	*/
	/**
	 * 删除文件夹及其子文件
	* <p>Title: deleteFolder</p>
	* <p>Description: </p>
	* <AUTHOR>
	* @param dir
	 */
	  public  static boolean   deleteFolder(File  dir)   {   
	      File   filelist[]=dir.listFiles();   
	      int   listlen=filelist.length;   
	      for(int   i=0;i<listlen;i++)   {   
	          if(filelist[i].isDirectory())   {   
	              deleteFolder(filelist[i]);   
	          }   
	          else   {   
	              filelist[i].delete();   
	          }   
	      }   
	      return dir.delete();//删除当前目录   
	  }
	
	
	/**
	* <p>Title:判断是否含有中文 </p>
	* <p>Description: </p>
	* @version 
	* 创建时间：Feb 26, 2010 11:18:05 AM 
	* <AUTHOR>
	* @param str
	* @return
	 */
	public  static boolean   hasChina(String str)   {   
		if (str.getBytes().length != str.length()) {
		    return true;
		} else{
			return false;
		}
	}
	/**
	* <p>Title: 判断是否是数字</p>
	* <p>Description: </p>
	* @version 
	* 创建时间：Feb 26, 2010 11:18:25 AM 
	* <AUTHOR>
	* @param str
	* @return
	 */
	public  static boolean   isNumeric(String str)   {   
		 Pattern pattern = Pattern.compile("[0-9]*"); 
		 return pattern.matcher(str).matches();  
	}
	
	
	/**
	 * If txt file
	 * @param fileName
	 * @return
	 */
	private boolean isDatFile(String fileName){
		if(fileName.toLowerCase().endsWith(".txt")){
			return true;
		}
		return false;
	}

	public boolean accept(File dir, String name) {
		
		return isDatFile(name);
	}
	
}
