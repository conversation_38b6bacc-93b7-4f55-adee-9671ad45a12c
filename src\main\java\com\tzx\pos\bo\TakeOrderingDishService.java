package com.tzx.pos.bo;

import net.sf.json.JSONObject;

import com.tzx.framework.common.entity.Data;
import com.tzx.pos.bo.dto.TakeOrderingParam;

public interface TakeOrderingDishService {
     String	NAME	= "com.tzx.pos.bo.imp.TakeOrderingDishServiceImp";
     
	 /**
	  * 接单处理
	  * @param param
	  * @param result
	  */
	 public TakeOrderingParam insertOrderDish(TakeOrderingParam params,Data result) throws Exception;
	 
	 public void billPrint(JSONObject json);
}
