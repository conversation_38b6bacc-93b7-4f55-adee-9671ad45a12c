package com.tzx.clientorder.mtwechat.bo.imp;

import com.tzx.clientorder.mtwechat.bo.MtPosEntranceService;
import com.tzx.framework.common.util.Tools;
import com.tzx.pos.base.constant.SelectTypeEnum;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.constant.WLifeConstant;
import com.tzx.pos.base.util.JsonUtil;
import com.tzx.pos.base.util.SqlUtil;
import com.tzx.clientorder.acewillwechat.po.springjdbc.dao.WshPosEntranceDao;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 拉取基础资料信息（提供美团接口）
 * Created by qingui on 2018-05-30.
 */
@Service(MtPosEntranceService.NAME)
public class MtPosEntranceServiceImp implements MtPosEntranceService{

    private static final Logger logger = Logger.getLogger(MtPosEntranceServiceImp.class);

    private static final String SUCCESS_KEY = "success";
    private static final String MSG_KEY = "msg";

    @Resource(name = WshPosEntranceDao.NAME)
    private WshPosEntranceDao	wshPosEntranceDao;

    @Override
    public JSONObject uploadBaseDataToWsh(String tenancyID, int storeId) throws Exception {
        JSONObject data = new JSONObject();

        try{
            logger.info("美团先付调用门店基础信息，门店ID是:" + storeId);
            fillData(tenancyID, String.valueOf(storeId), data);
            logger.info("美团先付调用门店基础信息,出参是：" + data.toString());

        }catch(Exception e){
            e.printStackTrace();
        }
        return data;
    }

    /**
     * 获取基础信息
     * @param tenancy_id
     * @param store_id
     * @param data
     * @throws Exception
     */
    private void fillData(String tenancy_id, String store_id, JSONObject data) throws Exception
    {
        List<JSONObject> uploadClassType = wshPosEntranceDao.selectStoreParams(tenancy_id, Integer.parseInt(store_id), new String[]
                { WLifeConstant.WLIFE_DISH_KINDS_MODE });
        String mode = "2";// mode=1 大类 mode=2小类;默认按小类
        if (null != uploadClassType && uploadClassType.size() > 0)
        {
            mode = uploadClassType.get(0).optString("para_value");
        }

        selectDishKinds(tenancy_id, store_id, data, SysDictionary.CHANEL_WX02,mode);
        selectTables(tenancy_id, store_id, data);
        selectShops(tenancy_id, store_id, data);
        selectMarketing(tenancy_id, store_id, data);
        selectHeadActivity(tenancy_id, store_id, data);
        selectMemo(tenancy_id, store_id, data);
        selectKindsMemo(tenancy_id, store_id, data);
        selectDishs(tenancy_id, store_id, data);
    }

    /**
     * @param tenancy_id
     * @param store_id
     * @param context
     * @param chanel
     * @param classMode
     * @throws Exception
     */
    private void selectDishKinds(String tenancy_id, String store_id, JSONObject context, String chanel, String classMode) throws Exception
    {
        // 查询菜品类别
        List<JSONObject> itemClassList = wshPosEntranceDao.selectDishKinds(tenancy_id, Integer.valueOf(store_id), chanel);

        // mode=1 大类 mode=2小类;默认按小类
        List<JSONObject> retList = new ArrayList<JSONObject>();
        for (JSONObject itemClassJson : itemClassList)
        {
            String pdkid = itemClassJson.optString("pdkid");
            if (Tools.isNullOrEmpty(pdkid))
            {
                pdkid = "0";
            }

            if ("1".equals(classMode) && "0".equals(pdkid))
            {
                String classId = itemClassJson.optString("id");
                itemClassJson.put("pkid", "0");
                itemClassJson.put("must", "0");
                itemClassJson.put("must_seq", "0");
                itemClassJson.put("suggest", "0");
                itemClassJson.put("suggest", "0");
                itemClassJson.put("icon", "");
                List<String> children = new ArrayList<String>();
                Integer dishCount =0;
                for (JSONObject childrenJson : itemClassList)
                {
                    String childrenId = childrenJson.optString("id");
                    String fatherId = childrenJson.optString("pdkid");
                    if (classId.equals(fatherId) && false == children.contains(childrenId))
                    {
                        children.add(childrenId);
                        dishCount = dishCount+childrenJson.optInt("dish_count");
                    }
                }
                itemClassJson.put("children", children);
                itemClassJson.put("dish_count", dishCount);
                retList.add(itemClassJson);
            }
            else if (false == "0".equals(pdkid))
            {
                itemClassJson.put("pkid", pdkid);
                itemClassJson.put("must", "0");
                itemClassJson.put("must_seq", "0");
                itemClassJson.put("suggest", "0");
                itemClassJson.put("suggest", "0");
                itemClassJson.put("icon", "");
                itemClassJson.put("children", "[]");
                retList.add(itemClassJson);
            }
        }
        context.put(SelectTypeEnum.DISH_KINDS.name, retList);
    }

    /**
     * 查询菜品分类
     * @param tenancy_id
     * @param store_id
     * @param context
     * @throws Exception
     */
    @SuppressWarnings("unused")
    private void selectDishKinds(String tenancy_id, String store_id, JSONObject context) throws Exception{
        List<JSONObject> datas = wshPosEntranceDao.selectDishKinds(tenancy_id, store_id);
        convertDishKinds(datas);
        List<JSONObject> uploadClassType = wshPosEntranceDao.selectStoreParams(tenancy_id, Integer.parseInt(store_id), new String[]{WLifeConstant.WLIFE_DISH_KINDS_MODE});
        int mode = uploadClassType.size() > 0 ? uploadClassType.get(0).optInt("para_value", 2) : 2; // 默认按小类
        // mode=1 大类 mode=2小类
        if(mode == 1){
            List<JSONObject> classParents = getClassParents(tenancy_id, store_id, datas);
            datas.clear();
            datas.addAll(classParents);
        }
        context.put(SelectTypeEnum.DISH_KINDS.name, datas);
    }

    private List<JSONObject> getClassParents(String tenancy_id, String store_id,
                                             List<JSONObject> datas) throws Exception {
        Set<Integer> pidSet = new HashSet<Integer>();
        for(JSONObject json : datas){
            Integer pid = json.optInt("pdkid");
            pidSet.add(pid);
        }

        int[] pids = transferIntegerArray(pidSet);
        List<JSONObject> pClassData = wshPosEntranceDao.selectDishKindParents(tenancy_id, store_id, pids);

        Map<Integer, List<JSONObject>> pClassMap = buildMap(pClassData, "id");
        List<JSONObject> pJsons = new ArrayList<JSONObject>();
        // 遍历子类，填充父类children,dish_count信息
        for(JSONObject json : datas){
            String class_id = json.optString("id");
            int chd_dish_count = json.optInt("dish_count");
            Integer pid = json.optInt("pdkid");
            JSONObject pClass = pClassMap.get(pid).get(0);
            if(!pClass.containsKey("children")){
                pClass.put("children", new JSONArray());
            }
            if(!pClass.containsKey("dish_count")){
                pClass.put("dish_count", 0);
            }
            // 填充子id
            JSONArray kids = (JSONArray)pClass.get("children");
            kids.add(class_id);

            // 计算子id的菜品数量和
            int dish_count = pClass.optInt("dish_count");
            dish_count += chd_dish_count;
            pClass.put("dish_count", dish_count);
//			pJsons.add(pClass);
        }

//		for(int key:pids)
//		{
//			if(pClassMap.containsKey(key))
//			{
//				pJsons.add(pClassMap.get(key).get(0));
//			}
//		}
        return pClassData;
    }

    private void convertDishKinds(List<JSONObject> datas) {
        String[] needKeys = new String[]{
                "id", "name", "seq", "dishkindsno", "pdkid", "pkid",
                "must", "must_seq", "suggest", "border", "icon",
                "children", "dish_count"
        };
        Map<String, JSONObject> map = new HashMap<String, JSONObject>();
        for(JSONObject json : datas){
            String class_id = json.optString("id");
            if(!map.containsKey(class_id)){
                JSONObject newJson = new JSONObject();
                JsonUtil.attrCopy(json, newJson, needKeys);
                map.put(class_id, newJson);
            }
        }

        Iterator<Map.Entry<String, JSONObject>> iterator = map.entrySet().iterator();
        while(iterator.hasNext()){
            Map.Entry<String, JSONObject> next = iterator.next();
            JSONObject data = next.getValue();
            String class_id = data.optString("id");
            data.put("children", new TreeSet<String>());
            data.put("dish_count", 0);
            for(JSONObject json : datas){
                String pdkid = json.optString("pdkid");
                if(pdkid.equals(class_id)){
                    Set<String> children = (TreeSet<String>)data.get("children");
                    String chd_id = json.optString("id");
                    children.add(chd_id);
                }
                String cid = json.optString("id");
                // 每一条数据对应一条菜品信息，所以用这个统计分类下菜品数
                if(cid.equals(class_id)){
                    int dish_count = data.optInt("dish_count");
                    dish_count++;
                    data.put("dish_count", dish_count);
                }
            }
        }
        datas.clear();
        datas.addAll(map.values());
    }

    private void selectTables(String tenancy_id, String store_id, JSONObject context) throws Exception{
        List<JSONObject> datas = wshPosEntranceDao.selectTables(tenancy_id, store_id);
        context.put(SelectTypeEnum.TABLES.name, datas);
    }

    private void selectShops(String tenancy_id, String store_id, JSONObject context) throws Exception{
        List<JSONObject> datas = wshPosEntranceDao.selectShops(tenancy_id, store_id);
        JSONObject shopJson = new JSONObject();
        if(datas.size() > 0){
            shopJson = datas.get(0);
            shopJson.put("ordermode", WLifeConstant.WLIFE_ORDER_MODE);
            shopJson.put("is_bind_user", "0");
            shopJson.put("version", WLifeConstant.WLIFE_SHOP_VERSION);
        }
        context.put(SelectTypeEnum.SHOPS.name, shopJson);
    }

    private void selectMarketing(String tenancy_id, String store_id, JSONObject context) throws Exception{
        // 目前当前系统没有营销活动
        context.put(SelectTypeEnum.MARKETING.name, new JSONObject());
    }

    private void selectHeadActivity(String tenancy_id, String store_id, JSONObject context) throws Exception{
        // 目前当前系统没有公告信息
        JSONObject json = new JSONObject();
        json.put("image", "");
        json.put("activities", new JSONArray());

        List<JSONObject> datas = new ArrayList<JSONObject>();
        datas.add(json);
        context.put("activity_rules", datas);
    }

    private void selectMemo(String tenancy_id, String store_id, JSONObject context) throws Exception{
        List<JSONObject> datas = wshPosEntranceDao.selectMemo(tenancy_id, store_id);
        context.put(SelectTypeEnum.MEMO.name, datas);
    }

    private void selectKindsMemo(String tenancy_id, String store_id, JSONObject context) throws Exception{
        List<JSONObject> datas = wshPosEntranceDao.selectMemo(tenancy_id, store_id);
        JSONObject content = new JSONObject();
        content.put("all", datas);
        context.put(SelectTypeEnum.KINDS_MEMO.name, content);
    }
    /**
     * 查询菜品信息
     * @param tenancy_id
     * @param store_id
     * @return
     * @throws Exception
     */
    private void selectDishs(String tenancy_id, String store_id, JSONObject context) throws Exception{
        // 获取门店信息
        JSONObject organ = wshPosEntranceDao.selectOrganInfo(tenancy_id, store_id);
        if(organ == null){
            throw new Exception("门店信息不存在");
        }
        String price_system = organ.optString("price_system", "1");

        List<JSONObject> uploadClassType = wshPosEntranceDao.selectStoreParams(tenancy_id, Integer.parseInt(store_id), new String[]{WLifeConstant.WLIFE_DISH_KINDS_MODE});
        int mode = uploadClassType.size() > 0 ? uploadClassType.get(0).optInt("para_value", 2) : 2; // 默认按小类

        JSONObject dishs = new JSONObject();

        //餐谱中套餐明细单品的id,在单品中不存在
        Set<Integer> normSet = new HashSet<Integer>();

        //查询出门店餐谱的菜品信息
        List<JSONObject> item_infos = wshPosEntranceDao.selectItemInfos(tenancy_id, store_id);
        replaceName(item_infos);
        for(int i = 0; i < item_infos.size(); i++){
            JSONObject json = item_infos.get(i);
            String dishkind =json.optString("dishkind");
            // mode=1 大类 mode=2小类
            if(2==mode)
            {
                dishkind = json.optString("pkid");
            }

            if(dishkind == null || "null".equals(dishkind)){
                json.put("dishkind", new String[0]);	//特殊处理
            }else{
                String[] arr = new String[]{dishkind};
                json.put("dishkind", arr);	//特殊处理
            }
            String dishsno = json.optString("dishsno");	//速记码
            dishs.put(dishsno, json);	// 菜品信息以速记码做key
            //如果是单品
            if ("1".equals(json.optString("type"))){
                //可以单独售卖
                json.put("separateSell", 1);
                normSet.add(json.optInt("id"));
            }
        }

        // 查询套餐信息
        int[] combo_item_ids = getComboItemIds(item_infos);
        JSONObject combObject = selectCombInfos(tenancy_id, combo_item_ids, price_system, normSet);
        List<JSONObject> selectCombInfos = (List<JSONObject>)combObject.opt("default_unit_infos");
        //在套餐明细中存在，但是单品没有的菜品id
        Set<Integer> normIdSet = new HashSet<Integer>();
        //如果餐谱中有设置套餐
        if (combo_item_ids.length != 0){
            JSONArray jsonArray = combObject.optJSONArray("detailMainSet");
            if (null != jsonArray){
                Iterator it = jsonArray.iterator();
                if (it.hasNext()){
                    int num = (Integer)it.next();
                    normIdSet.add(num);
                }
            }
        }

        //从菜品档案查询菜品单品，在微信点餐里是不显示的，separateSell为0
        if (null != normIdSet && normIdSet.size() > 0){
            int[] normIds = transferIntegerArray(normIdSet);
            List<JSONObject> norm_infos = wshPosEntranceDao.getItemInfos(tenancy_id, SqlUtil.getInnerStr(normIds));
            replaceName(norm_infos);
            //把套餐中的单品，不存在的查询出来拼接到单品中
            item_infos.addAll(norm_infos);
            for(int i = 0; i < norm_infos.size(); i++) {
                JSONObject json = norm_infos.get(i);
                String dishkind = json.optString("dishkind");
                // mode=1 大类 mode=2小类
                if (2 == mode) {
                    dishkind = json.optString("pkid");
                }

                if (dishkind == null || "null".equals(dishkind)) {
                    json.put("dishkind", new String[0]);    //特殊处理
                } else {
                    String[] arr = new String[]{dishkind};
                    json.put("dishkind", arr);    //特殊处理
                }
                //不可以单独售卖
                json.put("separateSell", 0);
                String dishsno = json.optString("dishsno");    //速记码
                dishs.put(dishsno, json);    // 菜品信息以速记码做key
            }
        }

        int[] item_ids = getItemIds(item_infos);

        //根据item_id查询unit信息
        List<JSONObject> unit_infos = wshPosEntranceDao.selectUnitInfos(tenancy_id, item_ids, price_system);
        replaceName(unit_infos);
        Map<Integer, List<JSONObject>> unitMap = buildMap(unit_infos, "item_id");

        // 查询做法信息
        List<JSONObject> cooks_infos = wshPosEntranceDao.selectMethodInfos(tenancy_id, item_ids);
        Map<Integer, List<JSONObject>> cooksMap = buildMap(cooks_infos, "item_id");

        replaceName(unit_infos);
        Map<Integer, List<JSONObject>> combMap = buildMap(selectCombInfos, "item_id");


        // 组装
        Iterator iterator = dishs.values().iterator();
        while(iterator.hasNext()){
            JSONObject json = (JSONObject)iterator.next();
            int item_id = json.optInt("id");
            if(unitMap.containsKey(item_id)){
                //如果是套餐，则只返回默认规格，对接美团接口，套餐不支持多规格，多规格套餐会被过滤掉
                if (combMap.containsKey(item_id)){
                    List<JSONObject> list = new ArrayList();
                    list.add(unitMap.get(item_id).get(0));
                    json.put("norms", list);
                }else {
                    json.put("norms", unitMap.get(item_id));
                }
            }else
            {
                json.put("norms", new JSONArray());
            }

            if(cooksMap.containsKey(item_id)){
                json.put("cooks", cooksMap.get(item_id));
            }else{
                json.put("cooks", new JSONArray());
            }

            if(combMap.containsKey(item_id)){
                json.put("setmeals", combMap.get(item_id).get(0));
            }else{
                json.put("setmeals", new JSONObject());
            }
        }
        context.put(SelectTypeEnum.DISHS.name, dishs);
    }

    private JSONObject selectCombInfos(String tenancy_id, int[] combo_item_ids, String price_system, Set<Integer> normSet) throws Exception{
        JSONObject combObject = new JSONObject();
        List<JSONObject> default_unit_infos = wshPosEntranceDao.selectComboBaseInfo(tenancy_id, combo_item_ids, price_system);

        List<JSONObject> detailsInfo = wshPosEntranceDao.selectDetailsInfo(tenancy_id, combo_item_ids);
        // 查询主菜和辅菜
        List<JSONObject> maindish = new ArrayList<JSONObject>();
        Map<Integer, Integer> isGroup = new HashMap<Integer, Integer>();
        Set<Integer> detailMainSet = new HashSet<Integer>();
        //ConcurrentHashMap
        for(JSONObject detail : detailsInfo){
            int item_id = detail.optInt("item_id");
            if("N".equals(detail.optString("is_itemgroup"))){
                // 套餐主菜
                maindish.add(detail);
                //针对套餐中的菜品明细在单品中没有的问题
                if (null != normSet && !normSet.contains(detail.optInt("id"))){
                    detailMainSet.add(detail.optInt("id"));
                }

            }else if("Y".equals(detail.optString("is_itemgroup"))){
                // 辅菜
                isGroup.put(detail.optInt("hicd_id"), item_id);
            }
        }
        // 查询套餐明细中的主菜在餐谱单品中存不存在，不存在的明细主菜在菜品档案中查询


        // 查询主菜信息
        Map<Integer, List<JSONObject>> map0 = buildMap(maindish, "item_id");	// id为菜品信息id

        // 查询辅菜信息
        int[] hicdIds = transferIntegerArray(isGroup.keySet());
        List<JSONObject> mandatoryInfos = getMandatoryInfos(tenancy_id, hicdIds);
        for(JSONObject json : mandatoryInfos){
            Integer hicd_id = json.optInt("hicd_id");
            json.put("item_id", isGroup.get(hicd_id));
        }
        Map<Integer, List<JSONObject>> map1 = buildMap(mandatoryInfos, "item_id");

        // 将主菜和辅菜挂到对应菜品信息下
        for(JSONObject json : default_unit_infos){
            Integer item_id = json.optInt("item_id");
            if(map0.containsKey(item_id)){
                json.put("maindish", map0.get(item_id));
            }else{
                json.put("maindish", new JSONArray());
            }
            if(map1.containsKey(item_id)){
                json.put("mandatory", map1.get(item_id));
            }else{
                json.put("mandatory", new JSONArray());
            }
            json.put("optional", new JSONArray());
            json.put("membergid", new JSONArray());
        }
        combObject.put("default_unit_infos", default_unit_infos);
        if (detailMainSet.isEmpty())
            detailMainSet = null;
        combObject.put("detailMainSet", detailMainSet);
        return combObject;
    }

    private List<JSONObject> getMandatoryInfos(String tenancy_id, int[] hicdIds) throws Exception {
        List<JSONObject> list = wshPosEntranceDao.selectMandatoryBaseInfo(tenancy_id, hicdIds);
        Set<Integer> groupIdSet = new HashSet<Integer>();
        for(JSONObject json : list){
            Integer id =  json.optInt("hig_id");
            groupIdSet.add(id);
        }
        int[] groupIds = transferIntegerArray(groupIdSet);
        List<JSONObject> groupDetails = wshPosEntranceDao.selectGroupDetails(tenancy_id, groupIds);
        Map<Integer, List<JSONObject>> groupDetailMap = buildMap(groupDetails, "group_id");
        for(JSONObject json : list){
            Integer group_id =  json.optInt("hig_id");
            if(groupDetailMap.containsKey(group_id)){
                json.put("items", groupDetailMap.get(group_id));
            }
        }
        return list;
    }

    private int[] getComboItemIds(List<JSONObject> item_infos) {
        Set<Integer> itemIdSet = new HashSet<Integer>();
        for(JSONObject json : item_infos){
            String isComb = json.optString("type", "1");
            if("2".equals(isComb)){
                Integer item_id = json.optInt("id");
                itemIdSet.add(item_id);
            }
        }
        int[] ids = transferIntegerArray(itemIdSet);
        return ids;
    }

    private int[] getItemIds(List<JSONObject> item_infos) {
        Set<Integer> itemIdSet = new HashSet<Integer>();
        for(JSONObject json : item_infos){
            Integer item_id = json.optInt("id");
            itemIdSet.add(item_id);
        }
        int[] ids = transferIntegerArray(itemIdSet);
        return ids;
    }

    private int[] transferIntegerArray(Collection<Integer> collection){
        Integer[] array = collection.toArray(new Integer[0]);
        int[] ids = new int[array.length];
        for(int i = 0; i < ids.length; i++){
            ids[i] = array[i];
        }
        return ids;
    }

    private <T> Map<T, List<JSONObject>> buildMap(List<JSONObject> list, String attr){
        Map<T, List<JSONObject>> map = new HashMap<T, List<JSONObject>>();
        for(JSONObject json : list){
            T key = (T)json.opt(attr);
            if(!map.containsKey(key)){
                map.put(key, new ArrayList<JSONObject>());
            }
            map.get(key).add(json);
        }
        return map;
    }

    private static String[] keys = {
            "wxDishs", "priceName", "isWeigh", "limitCount", "limitCount"
    };

    private static Map<String, String> map;
    static{
        map = new ConcurrentHashMap<String, String>();
        for(String key : keys){
            map.put(key.toLowerCase(), key);
        }
    }

    private static void replaceName(List<JSONObject> list){
        for(JSONObject json : list){
            replaceName(json);
        }
    }
    private static void replaceName(JSONObject json){
        String[][] temp = new String[2][1];
        Set<String> set = new HashSet<String>(json.keySet());
        Iterator<String> iterator = set.iterator();
        while(iterator.hasNext()){
            String next = iterator.next();
            if(map.containsKey(next)){
                temp[0][0] = next;
                temp[1][0] = map.get(next);
                JsonUtil.renameKey(json, temp[0] , temp[1]);
            }
        }
    }
}
