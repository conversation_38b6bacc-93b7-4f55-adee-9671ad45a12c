package com.tzx.pos.bo.imp.calcamount;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Service;

import com.tzx.base.cache.CacheManager;
import com.tzx.base.constant.CacheTableConstant;
import com.tzx.base.entity.PosBill;
import com.tzx.base.entity.PosBillItem;
import com.tzx.base.entity.PosBillPayment;
import com.tzx.base.entity.PosBillService;
import com.tzx.base.entity.PosMethodItem;
import com.tzx.framework.common.util.DoubleHelper;
import com.tzx.framework.common.util.Tools;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.CacheTableUtil;
import com.tzx.pos.bo.PosCalcAmountService;
import com.tzx.pos.po.springjdbc.dao.PosBaseDao;

@Service("com.tzx.pos.bo.imp.calcamount.PosCalcAmountStandardServiceImp")
public class PosCalcAmountStandardServiceImp implements PosCalcAmountService
{
	private final int	defaultScale	= 4;

	@Resource(name = PosBaseDao.NAME)
	private PosBaseDao	baseDao;

    @Override
    public void calcAmount(String tenantId, Integer storeId, String billNum)throws Exception {
        calcAmount(tenantId,storeId,billNum,null,null);
    }
    @Override
    public void calcAmount(String tenantId, Integer storeId, String billNum,Integer dishScale,Integer billScale) throws Exception
	{

		Map<?, ?> sysParameterMap = CacheManager.getCacheMap(CacheTableConstant.SYS_PARAMETER.name());

		 dishScale = Tools.parseInt(CacheTableUtil.getSysParameter("CMJEWS", sysParameterMap), defaultScale);
		 billScale = Tools.parseInt(CacheTableUtil.getSysParameter("ZDJEWS", sysParameterMap), defaultScale);

		String billPointType = CacheTableUtil.getSysParameter("billpointtype", sysParameterMap);

		// 获取账单明细数据
		List<PosBillItem> itemList = baseDao.getPosBillItemByBillnum(tenantId, storeId, billNum);

		// 判断账单明细不为空
		if (null != itemList && itemList.size() > 0)
		{
			// 获取账单
			List<PosBill> billList = baseDao.getPosBillByBillnum(tenantId, storeId, billNum);
			PosBill bill = null;
			if (null != billList && billList.size() > 0)
			{
				bill = billList.get(0);
			}
			else
			{
				return;
			}

			// 账单服务费
			List<PosBillService> serviceList = baseDao.getPosBillServiceByBillnum(tenantId, storeId, billNum);

			// 账单明细做法信息
			List<PosMethodItem> itemMethodList = baseDao.getPosMethodItemByBillnum(tenantId, storeId, billNum);

			// 处理套餐
			this.setMeallistToSetmeal(itemList);

			// 计算账单明细菜品数据
			this.calculateBillItemAmount(bill, itemList, itemMethodList, dishScale);

			// 计算服务费
			this.calculateBillServiceAmount(bill, serviceList);

			// 计算账单金额
			this.calculateBillAmount(bill, billScale, billPointType);

			// 平摊抹零金额
			this.shareBillSmallChange(bill, itemList);

			// 统计账单付款金额
			// 计算账单差额
			List<PosBillPayment> paymentList = baseDao.getPosBillPaymentByBillnum(tenantId, storeId, billNum);
			this.calculateBillDifference(bill, paymentList);

			// 修改账单信息
			baseDao.updatePosBillForAmount(tenantId, storeId, bill);
			// 修改菜品信息
			baseDao.updatePosBillItemForAmount(tenantId, storeId, itemList);
			// 修改账单服务费
			baseDao.updatePosBillServiceForAmount(tenantId, storeId, serviceList);
		}
		else
		{
			// 账单金额置空
			PosBill bill = new PosBill();
			bill.setBill_num(billNum);
			baseDao.updatePosBillForAmount(tenantId, storeId, bill);
		}
	}

	/**
	 * 整理套餐
	 * 
	 * @param itemList
	 * @throws Exception
	 */
	private void setMeallistToSetmeal(List<PosBillItem> itemList) throws Exception
	{
		for (PosBillItem item : itemList)
		{
			if (SysDictionary.ITEM_PROPERTY_SETMEAL.equals(item.getItem_property()))
			{
				List<PosBillItem> itemMealList = new ArrayList<PosBillItem>();
				String itemRemark = Tools.hv(item.getItem_remark()) ? item.getItem_remark() : "";
				for (PosBillItem itemMeal : itemList)
				{
					String mealItemRemark = Tools.hv(itemMeal.getItem_remark()) ? itemMeal.getItem_remark() : "";

					if (SysDictionary.ITEM_PROPERTY_MEALLIST.equals(itemMeal.getItem_property()) && item.getItem_id().equals(itemMeal.getSetmeal_id()) && item.getItem_serial().equals(itemMeal.getSetmeal_rwid()) && itemRemark.equals(mealItemRemark))
					{
						itemMealList.add(itemMeal);
					}
				}
				item.setItemMealList(itemMealList);
			}
		}
	}

	/**
	 * 
	 * 1,计算项目组加价,做法加价,菜目金额; 2,计算折扣金额 ;3,平摊折让金额; 4,计算菜品实付金额 ;5,统计折扣金额; 6,统计奉送金额;
	 * 7,统计菜目小计;
	 * 
	 * @param posBIll
	 * @param itemList
	 * @param itemMethodList
	 * @throws Exception
	 */
	private void calculateBillItemAmount(PosBill bill, List<PosBillItem> itemList, List<PosMethodItem> itemMethodList, int dishScale) throws Exception
	{
		// 奉送金额
		double giviAmount = 0d;
		// 菜目小计
		double subtotal = 0d;

		// 计算菜品做法加价
		Map<Object, Double> methodMap = new HashMap<Object, Double>();
		Double methodAmount = null;
		for (PosMethodItem method : itemMethodList)
		{
			if (methodMap.containsKey(method.getRwid()))
			{
				methodAmount = methodMap.get(method.getRwid());
				if (null == methodAmount || methodAmount.isNaN())
				{
					methodAmount = 0d;
				}
				methodMap.put(method.getRwid(), DoubleHelper.add(methodAmount, method.getAmount(), defaultScale));
			}
			else
			{
				methodMap.put(method.getRwid(), method.getAmount());
			}
		}

		double assistMoney = 0d;
		double methodMoney = 0d;
		double itemAmount = 0d;// 菜目金额=菜品单价*数量+做法加价+套餐项目组加价

		double detailMethodMoney = 0d;
		double detailAssistMoney = 0d;
		double mealMethodMoney = 0d;
		double mealAssistMoney = 0d;
		double mealItemAmount = 0d;

		PosBillItem maxItem = null;
		Map<Object, PosBillItem> maxDetailItemMap = new HashMap<Object, PosBillItem>();
		Double subtotal2 = 0d;

		PosBillItem maxDetailItem = null;
		for (PosBillItem billItem : itemList)
		{
			assistMoney = 0d;
			methodMoney = 0d;
			itemAmount = 0d;

			maxDetailItem = null;

			if (SysDictionary.ITEM_PROPERTY_SINGLE.equalsIgnoreCase(billItem.getItem_property()))
			{
				// 1,计算项目组加价,做法加价,菜目金额;
				if (methodMap.containsKey(billItem.getRwid()))
				{
					methodMoney = DoubleHelper.mul(methodMap.get(billItem.getRwid()), billItem.getItem_count(), dishScale);
				}

				itemAmount = DoubleHelper.add(DoubleHelper.mul(billItem.getItem_price(), billItem.getItem_count(), defaultScale), DoubleHelper.add(assistMoney, methodMoney, defaultScale), dishScale);

				billItem.setMethod_money(methodMoney);
				billItem.setAssist_money(assistMoney);
				billItem.setItem_amount(itemAmount);

				if (!SysDictionary.ITEM_REMARK_TC01.equals(billItem.getItem_remark()) && !SysDictionary.ITEM_REMARK_FS02.equals(billItem.getItem_remark()) && !SysDictionary.ITEM_REMARK_CJ05.equals(billItem.getItem_remark()))
				{
					if (null == maxItem || maxItem.getItem_amount() < itemAmount)
					{
						maxItem = billItem;
					}

					subtotal2 = DoubleHelper.add(subtotal2, itemAmount, defaultScale);
				}

				// 6,统计奉送金额;
				if (SysDictionary.ITEM_REMARK_FS02.equals(billItem.getItem_remark()))
				{
					giviAmount = DoubleHelper.add(giviAmount, itemAmount, defaultScale);
				}

				// 7,统计菜目小计;
				subtotal = DoubleHelper.add(subtotal, itemAmount, defaultScale);
			}
			else if (SysDictionary.ITEM_PROPERTY_SETMEAL.equalsIgnoreCase(billItem.getItem_property()))
			{
				// 1,计算项目组加价,做法加价,菜目金额;
				for (PosBillItem detail : billItem.getItemMealList())
				{
					detailMethodMoney = 0d;
					detailAssistMoney = 0d;

					if (methodMap.containsKey(detail.getRwid()))
					{
						detailMethodMoney = methodMap.get(billItem.getRwid());
					}

					JSONObject comboDetails = CacheTableUtil.getComboDetailsForJson(billItem.getItem_id(), detail.getAssist_item_id(), bill.getSource());

					if (null != comboDetails && "Y".equals(comboDetails.optString("is_itemgroup")))
					{
						JSONObject groupDetails = CacheTableUtil.getGroupDetailsForJson(comboDetails.optInt("details_id"), detail.getItem_id(), detail.getItem_unit_id());
						if (groupDetails.containsKey("makeup_money") && groupDetails.optDouble("makeup_money") > 0)
						{
							detailAssistMoney = groupDetails.optDouble("makeup_money");
						}
					}

					// 计算做法加价
					mealMethodMoney = DoubleHelper.mul(detailMethodMoney, detail.getItem_count(), dishScale);
					// 计算项目组加价
					mealAssistMoney = DoubleHelper.mul(detailAssistMoney, detail.getItem_count(), dishScale);

					Double assistNum = detail.getAssist_num();
					if (null == assistNum || assistNum.isNaN() || assistNum <= 0)
					{
						assistNum = DoubleHelper.div(detail.getItem_count(), billItem.getItem_count(), 0);
					}

					mealItemAmount = DoubleHelper.add(DoubleHelper.mul(detail.getItem_price(), DoubleHelper.div(detail.getItem_count(), assistNum, defaultScale), defaultScale), DoubleHelper.add(mealAssistMoney, mealMethodMoney, defaultScale), dishScale);

					detail.setMethod_money(mealMethodMoney);
					detail.setAssist_money(mealAssistMoney);
					detail.setAssist_num(assistNum);
					detail.setItem_amount(mealItemAmount);

					if (null == maxDetailItem || maxDetailItem.getItem_amount() < mealItemAmount)
					{
						maxDetailItem = detail;
					}

					methodMoney = DoubleHelper.add(methodMoney, mealMethodMoney, dishScale);
					assistMoney = DoubleHelper.add(assistMoney, mealAssistMoney, dishScale);
				}

				itemAmount = DoubleHelper.add(DoubleHelper.mul(billItem.getItem_price(), billItem.getItem_count(), defaultScale), DoubleHelper.add(assistMoney, methodMoney, defaultScale), dishScale);

				billItem.setMethod_money(methodMoney);
				billItem.setAssist_money(assistMoney);
				billItem.setItem_amount(itemAmount);

				// 判断菜品金额最大
				if (!SysDictionary.ITEM_REMARK_TC01.equals(billItem.getItem_remark()) && !SysDictionary.ITEM_REMARK_FS02.equals(billItem.getItem_remark()) && !SysDictionary.ITEM_REMARK_CJ05.equals(billItem.getItem_remark()))
				{
					if (null == maxItem || maxItem.getItem_amount() < itemAmount)
					{
						maxItem = billItem;
					}

					maxDetailItemMap.put(billItem.getRwid(), maxDetailItem);
					subtotal2 = DoubleHelper.add(subtotal2, itemAmount, defaultScale);
				}

				// 6,统计奉送金额;
				if (SysDictionary.ITEM_REMARK_FS02.equals(billItem.getItem_remark()))
				{
					giviAmount = DoubleHelper.add(giviAmount, itemAmount, defaultScale);
				}

				// 7,统计菜目小计;
				subtotal = DoubleHelper.add(subtotal, itemAmount, defaultScale);
			}
		}

		bill.setGivi_amount(giviAmount);
		bill.setSubtotal(subtotal);

		double discountkAmount = 0d;
		//
		Double discountRate = bill.getDiscount_rate().doubleValue();
		if (discountRate == null || discountRate == 0d)
		{
			discountRate = 100d;
			bill.setDiscount_rate(100);
		}

		for (PosBillItem billItem : itemList)
		{
			// 判断不为套餐明细,套餐明细与套餐主项一致,
			if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(billItem.getItem_property()))
			{
				Double itemDiscountRate = 100d;
				double itemDiscountAmount = 0d;
				double itemDiscountrAmount = 0d;
				// 退菜,奉送不允许打折
				if (SysDictionary.ITEM_REMARK_TC01.equals(billItem.getItem_remark()) || SysDictionary.ITEM_REMARK_FS02.equals(billItem.getItem_remark()) || SysDictionary.ITEM_REMARK_CJ05.equals(billItem.getItem_remark()))
				{
					// 退菜,奉送不计算折扣金额
					getDiscountRateAmount(billItem, 100d, dishScale);
					continue;
				}

				// 单品折扣单独处理
				if (SysDictionary.DISCOUNT_MODE_10 == billItem.getDiscount_mode_id())
				{
					itemDiscountRate = billItem.getDiscount_rate();
					if (null == itemDiscountRate || itemDiscountRate == 0d)
					{
						itemDiscountRate = 100d;
					}
					getDiscountRateAmount(billItem, itemDiscountRate, dishScale);
				}
				else if (Tools.hv(bill.getDiscount_mode_id()) && bill.getDiscount_mode_id() > 0)// 判断账单是否打折,如果不打折,取消账单明细折扣
				{
					switch (bill.getDiscount_mode_id())
					{
						case SysDictionary.DISCOUNT_MODE_1:
							getDiscountRateAmount(billItem, discountRate, dishScale);
							break;

						case SysDictionary.DISCOUNT_MODE_2:
//							if ("Y".equalsIgnoreCase(billItem.getDiscount_state()))
//							{
								JSONObject discountCaseDetails2 = CacheTableUtil.getDiscountCaseDetailsForJson(bill.getDiscount_case_id(), billItem.getItem_id(), billItem.getItem_unit_name());

								if (discountCaseDetails2.containsKey("derate") && discountCaseDetails2.optDouble("derate") > 0d)
								{
									double derate = discountCaseDetails2.optDouble("derate");
									if (derate > billItem.getItem_price())
									{
										derate = billItem.getItem_price();
									}
									// 减免金额
									itemDiscountAmount = DoubleHelper.mul(derate, billItem.getItem_count(), dishScale);

									billItem.setDiscount_rate(itemDiscountRate);
									billItem.setDiscount_amount(itemDiscountAmount);

									if (itemDiscountAmount > 0)
									{
										if (SysDictionary.ITEM_PROPERTY_SETMEAL.equals(billItem.getItem_property()))
										{ // 将减免金额平摊到套餐明细
											double ratio = DoubleHelper.div(itemDiscountAmount, billItem.getItem_amount(), defaultScale);// 计算平摊比例
											double spare = DoubleHelper.sub(itemDiscountAmount, DoubleHelper.mul(billItem.getItem_amount(), ratio, dishScale), defaultScale);// 计算除不尽情况下的差额

											PosBillItem maxDetail = maxDetailItemMap.get(billItem.getRwid());

											for (PosBillItem detail : billItem.getItemMealList())
											{
												double detailDiscountAmount = DoubleHelper.mul(detail.getItem_amount(), ratio, dishScale);

												if (maxDetail.getRwid() == detail.getRwid())
												{
													detailDiscountAmount = DoubleHelper.add(detailDiscountAmount, spare, dishScale);
												}
												detail.setDiscount_rate(itemDiscountRate);
												detail.setDiscount_amount(detailDiscountAmount);
											}
										}
									}
								}
								else
								{
									Double rate = discountCaseDetails2.optDouble("rate");
									if (null == rate || rate.isNaN() || rate == 0d)
									{
										itemDiscountRate = 100d;
									}
									getDiscountRateAmount(billItem, itemDiscountRate, dishScale);
								}
//							}
							break;
						case SysDictionary.DISCOUNT_MODE_4:
							if ("Y".equalsIgnoreCase(billItem.getDiscount_state()))
							{
								itemDiscountRate = discountRate;
							}
							getDiscountRateAmount(billItem, itemDiscountRate, dishScale);
							break;

						case SysDictionary.DISCOUNT_MODE_5:
							if ("Y".equalsIgnoreCase(billItem.getDiscount_state()))
							{
								itemDiscountRate = discountRate;
							}
							getDiscountRateAmount(billItem, itemDiscountRate, dishScale);
							break;

						case SysDictionary.DISCOUNT_MODE_6:

							if (billItem.getThird_price() > 0 && billItem.getItem_price() > billItem.getThird_price())
							{
								itemDiscountAmount = DoubleHelper.mul(DoubleHelper.sub(billItem.getItem_price(), billItem.getThird_price(), defaultScale), billItem.getItem_count(), dishScale);
							}

							billItem.setDiscount_rate(itemDiscountRate);
							billItem.setDiscount_amount(itemDiscountAmount);

							if (itemDiscountAmount > 0)
							{
								if (SysDictionary.ITEM_PROPERTY_SETMEAL.equals(billItem.getItem_property()))
								{ // 将减免金额平摊到套餐明细
									double ratio = DoubleHelper.div(itemDiscountAmount, billItem.getItem_amount(), defaultScale);// 计算平摊比例
									double spare = DoubleHelper.sub(itemDiscountAmount, DoubleHelper.mul(billItem.getItem_amount(), ratio, dishScale), defaultScale);// 计算除不尽情况下的差额

									PosBillItem maxDetail = maxDetailItemMap.get(billItem.getRwid());

									for (PosBillItem detail : billItem.getItemMealList())
									{
										double detailDiscountAmount = DoubleHelper.mul(detail.getItem_amount(), ratio, dishScale);

										if (maxDetail.getRwid() == detail.getRwid())
										{
											detailDiscountAmount = DoubleHelper.add(detailDiscountAmount, spare, dishScale);
										}
										detail.setDiscount_rate(itemDiscountRate);
										detail.setDiscount_amount(detailDiscountAmount);
									}
								}
							}

							break;
						case SysDictionary.DISCOUNT_MODE_7:

							double ratio = DoubleHelper.div(bill.getDiscountk_amount(), subtotal2, defaultScale);// 计算平摊比例
							double spare = DoubleHelper.sub(bill.getDiscountk_amount(), DoubleHelper.mul(subtotal2, ratio, dishScale), defaultScale);// 计算除不尽情况下的差额

							itemDiscountAmount = DoubleHelper.mul(billItem.getItem_amount(), ratio, dishScale);

							if (maxItem.getRwid() == billItem.getRwid())
							{
								itemDiscountAmount = DoubleHelper.add(itemDiscountAmount, spare, dishScale);
							}
							billItem.setDiscount_rate(itemDiscountRate);
							billItem.setDiscount_amount(itemDiscountAmount);

							if (itemDiscountAmount > 0)
							{
								if (SysDictionary.ITEM_PROPERTY_SETMEAL.equals(billItem.getItem_property()))
								{ // 将减免金额平摊到套餐明细
									double detailRatio = DoubleHelper.div(itemDiscountAmount, billItem.getItem_amount(), defaultScale);// 计算平摊比例
									double detailSpare = DoubleHelper.sub(itemDiscountAmount, DoubleHelper.mul(billItem.getItem_amount(), detailRatio, dishScale), defaultScale);// 计算除不尽情况下的差额

									PosBillItem maxDetail = maxDetailItemMap.get(billItem.getRwid());

									for (PosBillItem detail : billItem.getItemMealList())
									{
										double detailDiscountAmount = DoubleHelper.mul(detail.getItem_amount(), detailRatio, dishScale);

										if (maxDetail.getRwid() == detail.getRwid())
										{
											detailDiscountAmount = DoubleHelper.add(detailDiscountAmount, detailSpare, dishScale);
										}
										detail.setDiscount_rate(itemDiscountRate);
										detail.setDiscount_amount(detailDiscountAmount);
									}
								}
							}

							break;
						case SysDictionary.DISCOUNT_MODE_8:
							// 清空
							getDiscountRateAmount(billItem, 100d, dishScale);

							// double itemDiscountAmount2 = 0d;
							// 会员价计算
							if (billItem.getThird_price() > 0 && billItem.getItem_price() > billItem.getThird_price())
							{
								itemDiscountAmount = DoubleHelper.mul(DoubleHelper.sub(billItem.getItem_price(), billItem.getThird_price(), defaultScale), billItem.getItem_count(), dishScale);
							}

							// 折扣方案计算
//							if ("Y".equalsIgnoreCase(billItem.getDiscount_state()))
//							{
								JSONObject discountCaseDetails8 = CacheTableUtil.getDiscountCaseDetailsForJson(bill.getDiscount_case_id(), billItem.getItem_id(), billItem.getItem_unit_name());

								if (discountCaseDetails8.containsKey("derate") && discountCaseDetails8.optDouble("derate") > 0d)
								{
									double derate = discountCaseDetails8.optDouble("derate");
									if (derate > billItem.getItem_price())
									{
										derate = billItem.getItem_price();
									}
									// 减免金额
									itemDiscountAmount = DoubleHelper.add(itemDiscountAmount, DoubleHelper.mul(derate, billItem.getItem_count(), dishScale), dishScale);
								}
								else
								{
									Double rate = discountCaseDetails8.optDouble("rate");
									if (null == rate || rate.isNaN() || rate == 0d)
									{
										itemDiscountRate = 100d;
									}
									double itemRate = DoubleHelper.sub(1d, DoubleHelper.div(itemDiscountRate, 100d, defaultScale), defaultScale);
									itemDiscountAmount = DoubleHelper.add(itemDiscountAmount, DoubleHelper.mul(billItem.getItem_amount(), itemRate, dishScale), dishScale);
								}
//							}

							billItem.setDiscount_rate(itemDiscountRate);
							billItem.setDiscount_amount(itemDiscountAmount);

							if (itemDiscountAmount > 0)
							{
								if (SysDictionary.ITEM_PROPERTY_SETMEAL.equals(billItem.getItem_property()))
								{ // 将减免金额平摊到套餐明细
									double detailRatio = DoubleHelper.div(itemDiscountAmount, billItem.getItem_amount(), defaultScale);// 计算平摊比例
									double detailSpare = DoubleHelper.sub(itemDiscountAmount, DoubleHelper.mul(billItem.getItem_amount(), detailRatio, dishScale), defaultScale);// 计算除不尽情况下的差额

									PosBillItem maxDetail = maxDetailItemMap.get(billItem.getRwid());

									for (PosBillItem detail : billItem.getItemMealList())
									{
										double detailDiscountAmount = DoubleHelper.mul(detail.getItem_amount(), detailRatio, dishScale);

										if (maxDetail.getRwid() == detail.getRwid())
										{
											detailDiscountAmount = DoubleHelper.add(detailDiscountAmount, detailSpare, dishScale);
										}

										detail.setDiscount_rate(itemDiscountRate);
										detail.setDiscount_amount(detailDiscountAmount);
									}
								}
							}
							break;
						case SysDictionary.DISCOUNT_MODE_9:
							if ("Y".equalsIgnoreCase(billItem.getDiscount_state()))
							{
								itemDiscountRate = discountRate;
							}
							getDiscountRateAmount(billItem, itemDiscountRate, dishScale);
							break;
					}
				}
				else
				{
					// 账单没有折扣,菜品没有单品折扣,视为取消折扣
					getDiscountRateAmount(billItem, 100d, dishScale);
				}

				discountkAmount = DoubleHelper.add(discountkAmount, billItem.getDiscount_amount(), dishScale);

				// 计算平摊折让
				double discountrRatio = DoubleHelper.div(bill.getDiscountr_amount(), subtotal2, defaultScale);// 计算平摊比例
				double discountrSpare = DoubleHelper.sub(bill.getDiscountr_amount(), DoubleHelper.mul(subtotal2, discountrRatio, dishScale), defaultScale);// 计算除不尽情况下的差额

				itemDiscountrAmount = DoubleHelper.mul(billItem.getItem_amount(), discountrRatio, dishScale);

				if (maxItem.getRwid() == billItem.getRwid())
				{
					itemDiscountrAmount = DoubleHelper.add(itemDiscountAmount, discountrSpare, dishScale);
				}
				billItem.setDiscountr_amount(itemDiscountrAmount);

				if (itemDiscountrAmount > 0)
				{
					if (SysDictionary.ITEM_PROPERTY_SETMEAL.equals(billItem.getItem_property()))
					{ // 将减免金额平摊到套餐明细
						double detailRatio = DoubleHelper.div(itemDiscountrAmount, billItem.getItem_amount(), defaultScale);// 计算平摊比例
						double detailSpare = DoubleHelper.sub(itemDiscountrAmount, DoubleHelper.mul(billItem.getItem_amount(), detailRatio, dishScale), defaultScale);// 计算除不尽情况下的差额

						PosBillItem maxDetail = maxDetailItemMap.get(billItem.getRwid());

						for (PosBillItem detail : billItem.getItemMealList())
						{
							double detailDiscountrAmount = DoubleHelper.mul(detail.getItem_amount(), detailRatio, dishScale);

							if (maxDetail.getRwid() == detail.getRwid())
							{
								detailDiscountrAmount = DoubleHelper.add(detailDiscountrAmount, detailSpare, dishScale);
							}
							detail.setDiscountr_amount(detailDiscountrAmount);
						}
					}
				}

				double realAmount = DoubleHelper.sub(billItem.getItem_amount(), DoubleHelper.add(billItem.getDiscount_amount(), billItem.getDiscountr_amount(), defaultScale), defaultScale);
				billItem.setReal_amount(realAmount);

				if (SysDictionary.ITEM_PROPERTY_SETMEAL.equals(billItem.getItem_property()))
				{
					for (PosBillItem detail : billItem.getItemMealList())
					{
						double detailRealAmount = DoubleHelper.sub(detail.getItem_amount(), DoubleHelper.add(detail.getDiscount_amount(), detail.getDiscountr_amount(), defaultScale), defaultScale);
						detail.setReal_amount(detailRealAmount);
					}
				}
			}
		}

		bill.setDiscountk_amount(discountkAmount);
	}

	private void getDiscountRateAmount(PosBillItem billItem, double itemDiscountRate, int dishScale) throws Exception
	{
		double rate = DoubleHelper.sub(1d, DoubleHelper.div(itemDiscountRate, 100d, defaultScale), defaultScale);
		double itemDiscountAmount = DoubleHelper.mul(billItem.getItem_amount(), rate, dishScale);

		billItem.setDiscount_rate(itemDiscountRate);
		billItem.setDiscount_amount(itemDiscountAmount);

		if (SysDictionary.ITEM_PROPERTY_SETMEAL.equalsIgnoreCase(billItem.getItem_property()))
		{
			for (PosBillItem detail : billItem.getItemMealList())
			{
				detail.setDiscount_rate(itemDiscountRate);
				detail.setDiscount_amount(DoubleHelper.mul(detail.getItem_amount(), rate, dishScale));
			}
		}
	}

	/**
	 * 计算服务费,并统计服务费合计
	 * 
	 * @param serviceList
	 * @throws Exception
	 */
	private void calculateBillServiceAmount(PosBill bill, List<PosBillService> serviceList) throws Exception
	{
		Map<?, ?> servoceTypeMap = CacheManager.getCacheMap(CacheTableConstant.SERVICE_TYPE.name());

		double serviceAmount = 0d;

		double serviceItemAmount = 0d;
		double serviceTotal = 0d;
		double serviceScale = 0d;

		JSONObject servoceTypeJson = null;

		for (PosBillService service : serviceList)
		{
			serviceItemAmount = 0d;
			serviceTotal = 0d;
			serviceScale = service.getService_scale();

			servoceTypeJson = JSONObject.fromObject(servoceTypeMap.get(String.valueOf(service.getService_id())));

			if (null != servoceTypeJson)
			{
				switch (servoceTypeJson.optString("taken_mode"))
				{
					case SysDictionary.SERVICE_MODE_GD01:
						serviceItemAmount = serviceScale;
						break;

					case SysDictionary.SERVICE_MODE_BL02:
						if ("Y".equals(servoceTypeJson.optString("free_count")))// 赠送金额是否计算服务费
						{
							serviceItemAmount = DoubleHelper.mul(bill.getSubtotal(), DoubleHelper.div(serviceScale, 100d, defaultScale), defaultScale);
						}
						else
						{
							serviceItemAmount = DoubleHelper.mul(DoubleHelper.sub(bill.getSubtotal(), bill.getGivi_amount(), defaultScale), DoubleHelper.div(serviceScale, 100d, defaultScale), defaultScale);
						}
						break;
					default:
						break;
				}
			}
			serviceTotal = DoubleHelper.mul(serviceItemAmount, service.getService_count(), defaultScale);
			service.setService_amount(serviceItemAmount);
			service.setService_total(serviceTotal);

			serviceAmount = DoubleHelper.add(serviceAmount, serviceTotal, defaultScale);
		}

		bill.setService_amount(serviceAmount);
	}

	/**
	 * 计算账单金额: 1,计算优惠金额; 2,计算账单金额; 3,计算应付金额; 4,计算抹零; 5,计算人均消费;
	 * 
	 * @param bill
	 * @param billScale
	 *            账单金额保留尾数
	 * @throws Exception
	 */
	private void calculateBillAmount(PosBill bill, int billScale, String billPointType) throws Exception
	{
		// 计算优惠金额 = 折扣金额 + 折让金额
		double discountAmount = DoubleHelper.add(bill.getDiscountr_amount(), bill.getDiscountk_amount(), defaultScale);
		bill.setDiscount_amount(discountAmount);

		// 计算账单金额 = 项目小计金额 + 服务费金额
		double billAmount = DoubleHelper.add(bill.getSubtotal(), bill.getService_amount(), defaultScale);
		bill.setBill_amount(billAmount);

		// 计算应付金额
		// 进位前付款金额 = sum(账单明细.实付金额)+服务费 = 账单金额-优惠金额 - 奉送金额
		double paymentAmount = DoubleHelper.sub(billAmount, DoubleHelper.add(discountAmount, bill.getGivi_amount(), defaultScale), defaultScale);

		if (paymentAmount < 0)
		{
			paymentAmount = 0d;
		}

		// 根据账单小数取舍方式计算进位后付款金额
		if (Tools.isNullOrEmpty(billPointType))
		{
			billPointType = SysDictionary.BILL_POINT_TYPE_HALF_ADJUST;
		}

		double paymentAmount2 = 0d;
		switch (billPointType)
		{
			case SysDictionary.BILL_POINT_TYPE_ROUNDING:
				paymentAmount2 = DoubleHelper.roundDown(paymentAmount, billScale);
				break;
			case SysDictionary.BILL_POINT_TYPE_ONE_ADJUST:
				paymentAmount2 = DoubleHelper.roundUp(paymentAmount, billScale);
				break;
			default:
				paymentAmount2 = DoubleHelper.round(paymentAmount, billScale);
				break;
		}
		bill.setPayment_amount(paymentAmount2);

		// 计算抹零 = 进位后付款金额-进位前付款金额
		double malingAmount = DoubleHelper.sub(paymentAmount2, paymentAmount, defaultScale);
		bill.setMaling_amount(malingAmount);

		// 计算人均消费 = 付款金额/顾客数
		double averageAmount = DoubleHelper.div(paymentAmount2, Double.parseDouble(String.valueOf(bill.getGuest())), defaultScale);
		bill.setAverage_amount(averageAmount);
	}

	/**
	 * 平摊抹零金额到菜品明细中菜目金额最大的菜品上,如果此菜品为套餐,平摊到此套餐中,菜目金额最大的套餐明细菜品上
	 * 
	 * @param bill
	 * @param itemList
	 * @throws Exception
	 */
	private void shareBillSmallChange(PosBill bill, List<PosBillItem> itemList) throws Exception
	{
		PosBillItem maxItem = null;
		for (PosBillItem item : itemList)
		{
			item.setSingle_discount_amount(0d);// 抹零金额置空

			if (!SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item.getItem_property()) && !SysDictionary.ITEM_REMARK_TC01.equals(item.getItem_remark()) && !SysDictionary.ITEM_REMARK_FS02.equals(item.getItem_remark()) && !SysDictionary.ITEM_REMARK_CJ05.equals(item.getItem_remark()))
			{
				if (null == maxItem || maxItem.getItem_amount() < item.getItem_amount())
				{
					maxItem = item;
				}
			}
		}

		if (Tools.hv(maxItem))
		{
			maxItem.setSingle_discount_amount(bill.getMaling_amount());
			maxItem.setReal_amount(DoubleHelper.sub(DoubleHelper.add(maxItem.getItem_amount(), maxItem.getSingle_discount_amount(), defaultScale), DoubleHelper.add(maxItem.getDiscount_amount(), maxItem.getDiscountr_amount(), defaultScale), defaultScale));

			if (SysDictionary.ITEM_PROPERTY_SETMEAL.equalsIgnoreCase(maxItem.getItem_property()))
			{
				PosBillItem detailMaxItem = null;
				for (PosBillItem detail : maxItem.getItemMealList())
				{
					if (null == detailMaxItem || detailMaxItem.getItem_amount() < detail.getItem_amount())
					{
						detailMaxItem = detail;
					}
				}
				if (Tools.hv(detailMaxItem))
				{
					detailMaxItem.setSingle_discount_amount(bill.getMaling_amount());
					detailMaxItem.setReal_amount(DoubleHelper.sub(DoubleHelper.add(detailMaxItem.getItem_amount(), detailMaxItem.getSingle_discount_amount(), defaultScale), DoubleHelper.add(detailMaxItem.getDiscount_amount(), detailMaxItem.getDiscountr_amount(), defaultScale), defaultScale));
				}
			}
		}
	}

	/**
	 * 统计账单付款金额;计算账单差额
	 * 
	 * @param posBIll
	 * @throws Exception
	 */
	private void calculateBillDifference(PosBill bill, List<PosBillPayment> paymentList) throws Exception
	{
		// 统计账单付款金额
		double paymentAmount = 0d;
		if (null != paymentList && paymentList.size() > 0)
		{
			for (PosBillPayment payment : paymentList)
			{
				paymentAmount = DoubleHelper.add(paymentAmount, payment.getCurrency_amount(), defaultScale);
			}
		}
		// 差额=账单金额-(付款合计-多少礼券)
		double difference = DoubleHelper.sub(bill.getPayment_amount(), DoubleHelper.sub(paymentAmount, bill.getMore_coupon(), defaultScale), defaultScale);
		bill.setDifference(difference);
	}

}
