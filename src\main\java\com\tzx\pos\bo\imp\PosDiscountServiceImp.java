package com.tzx.pos.bo.imp;

import com.tzx.base.entity.PosBillMember;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.DoubleHelper;
import com.tzx.framework.common.util.Tools;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.base.util.ReqDataUtil;
import com.tzx.pos.bo.PosDiscountService;
import com.tzx.pos.bo.dto.BasicDataUserDiscountAuthority;
import com.tzx.pos.po.springjdbc.dao.PosBillMemberDao;
import com.tzx.pos.po.springjdbc.dao.PosDiscountDao;
import com.tzx.pos.po.springjdbc.dao.PosDishDao;

import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.sql.Timestamp;
import java.util.*;

/**
 * Created by kevin on 2017-08-14.
 */
@Service(PosDiscountService.NAME)
public class PosDiscountServiceImp extends PosBaseServiceImp implements PosDiscountService {

    @Resource(name = PosDiscountDao.NAME)
    private PosDiscountDao			posDiscountDao;

    @Resource(name = PosDishDao.NAME)
	private PosDishDao			posDishDao;
    
    @Resource(name = PosBillMemberDao.NAME)
	private PosBillMemberDao			memberDao;
    
    private int scale = 2;

    private static final Logger logger	= Logger.getLogger(PosDiscountServiceImp.class);
    
    /**
     * 奉送权限验证
     * @param param
     * @param result
     * @throws Exception
     */
    @Deprecated
    public void giveAuth(Data param, Data result) {
        try {
            String tenancyId = param.getTenancy_id();
            Integer storeId = param.getStore_id();
            Map<String, Object> map = ReqDataUtil.getDataMap(param);
            //人员编号
            String optNum = ParamUtil.getStringValue(map, "opt_num", false, null);
            //账单号
            String billNum = ParamUtil.getStringValue(map, "bill_num", true, PosErrorCode.NOT_NULL_BILL_NUM);
            //日期
            Date reportDate = ParamUtil.getDateValue(map, "report_date", false, null);
            //未下单需要奉送的菜品信息
            List<Map<String, Object>> items = (List<Map<String, Object>>) map.get("items");
            //未下单最终奉送金额
            double final_money = 0d;
            //套餐明细列表
            List<Map<String, Object>> meallistItems = new ArrayList<Map<String,Object>>();
            if(null != items && items.size() > 0) {
                for (Map<String, Object> oDish : items) {
                    String itemProperty = ParamUtil.getStringValue(oDish, "item_property", true, PosErrorCode.NOT_NULL_ITEM_PROPERTY);
                    Integer itemId = ParamUtil.getIntegerValue(oDish, "item_id", true, PosErrorCode.NOT_NULL_ITEM_ID);
                    Integer itemSerial = ParamUtil.getIntegerValue(oDish, "item_serial", false, null);
                    Double itemCount = ParamUtil.getDoubleValue(oDish, "item_count", false, null);
                    Double itemPrice = ParamUtil.getDoubleValue(oDish, "item_price", false, null);
                    if(itemCount.isNaN() || itemCount <= 0) {
                        itemCount = 1d;
                    }
                    if(SysDictionary.ITEM_PROPERTY_MEALLIST.equals(itemProperty)) {
                        meallistItems.add(oDish);
                    }
                    if(SysDictionary.ITEM_PROPERTY_SETMEAL.equals(itemProperty)) {
//                        List<Map<String, Object>> setmealItemList = new ArrayList<Map<String,Object>>();
//                        Double meallistItemPriceTotal = 0d;
                        Double zfmoney = 0d;
                        for (Map<String, Object> meallistItem : items)
                        {
                            String detailsItemProperty = ParamUtil.getStringValue(meallistItem, "item_property", true, PosErrorCode.NOT_NULL_ITEM_PROPERTY);
                            Integer detailsSetmealId = ParamUtil.getIntegerValue(meallistItem, "setmeal_id", false, null);
                            Integer detailsSetmealRwid = ParamUtil.getIntegerValue(meallistItem, "setmeal_rwid", false, null);
                            Double detailsItemCount = ParamUtil.getDoubleValue(meallistItem, "item_count", false, null);
                            Double detailsItemPrice = ParamUtil.getDoubleValue(meallistItem, "item_price", false, null);

                            if(detailsItemCount.isNaN() || detailsItemCount <= 0)
                            {
                                detailsItemCount = 1d;
                            }

                            if(detailsItemPrice.isNaN())
                            {
                                detailsItemPrice = 0d;
                            }

                            if (SysDictionary.ITEM_PROPERTY_MEALLIST.equals(detailsItemProperty) && itemId.equals(detailsSetmealId) && itemSerial.equals(detailsSetmealRwid))
                            {
//                                Double assistNum = DoubleHelper.div(detailsItemCount, itemCount, scale);
//                                detailsItemPrice = DoubleHelper.mul(detailsItemPrice, DoubleHelper.div(detailsItemCount, assistNum, scale), scale);
//                                meallistItemPriceTotal = DoubleHelper.add(meallistItemPriceTotal, detailsItemPrice, scale);
//
//                                meallistItem.put("item_price", detailsItemPrice);
//                                meallistItem.put("assist_num", assistNum);
//                                setmealItemList.add(meallistItem);
                                //明细做法金额
                                Double detailsMoney = this.getMethodMoney(meallistItem, detailsItemPrice, scale);
                                zfmoney = zfmoney + detailsMoney;
                            }
                        }
                        final_money = DoubleHelper.mul(itemPrice, itemCount, scale);
                        final_money = final_money + zfmoney;
                    }
                    //单品奉送金额
                    double fsmoney = 0d;
                    //单品做法加价金额
                    double zfmoney = 0d;
                    if(SysDictionary.ITEM_PROPERTY_SINGLE.equals(itemProperty)) {
                        //菜品奉送金额
                        fsmoney = DoubleHelper.mul(itemPrice, itemCount, scale);
                        //做法金额
                        zfmoney = getMethodMoney(oDish, itemPrice, scale);
                    }
                    final_money = final_money + fsmoney + zfmoney;
                }
            }
            //获取人员奉送限制
            List<BasicDataUserDiscountAuthority> userDiscountCaseList = posDiscountDao.getUserDiscountCase(tenancyId, storeId, optNum);
            if (null != userDiscountCaseList && userDiscountCaseList.size() > 0) {
                BasicDataUserDiscountAuthority basicDataUserDiscountAuthority = userDiscountCaseList.get(0);
                //人员奉送限制金额
                Double pre_reward_money = basicDataUserDiscountAuthority.getPre_reward_money();
                String employee_id = basicDataUserDiscountAuthority.getEmployee_id() + "";
//				if (pre_reward_money > 0) {
                Double item_amonut = posDiscountDao.getItemAmountByManagerNum(param, employee_id, reportDate);
                //计算可奉送金额
                Double need_money = DoubleHelper.sub(pre_reward_money, item_amonut, scale);
                Double fs_money = DoubleHelper.sub(need_money, final_money, scale);
                //设置返回的结果集
                setResultData4Gvie(result, fs_money);
//				} else if (pre_reward_money <= 0) {
//					this.giveAuth4Role(result, optNum);
//				}
            } else {
                this.giveAuth4Role(result, optNum, reportDate, final_money);
            }
        }catch (Exception e) {
            result.setCode(2);
            result.setMsg("奉送权限认证失败" + e.getMessage());
        }
    }

    /**
     * 折让权限验证
     * @param param
     * @param result
     * @throws Exception
     */
   public void discountAuth(Data param, Data result) throws SystemException {
        try {
            String tenancyId = param.getTenancy_id();
            Integer storeId = param.getStore_id();
            Map<String, Object> map = ReqDataUtil.getDataMap(param);
            //操作人
            String optNum = ParamUtil.getStringValue(map, "opt_num", false, null);
            //二次授权人
            String managerNum = ParamUtil.getStringValue(map, "manager_num", false, null);
            //POS前台输入的折让金额
            Double discountr = ParamUtil.getDoubleValue(map, "discountr_amount", false, null);
            //日期
            Date reportDate = ParamUtil.getDateValue(map, "report_date", false, null);
            //账单号
            String billNum =  ParamUtil.getStringValue(map, "bill_num", false, null);
            		
            //获取人员折让限制
            if(!StringUtils.isEmpty(managerNum)) {
                if(!optNum.equals(managerNum)) {
                    optNum = managerNum;
                }
            }
            List<BasicDataUserDiscountAuthority> userDiscountCaseList = posDiscountDao.getUserDiscountCase(tenancyId, storeId, optNum);
            if (null != userDiscountCaseList && userDiscountCaseList.size() > 0) {
                BasicDataUserDiscountAuthority basicDataUserDiscountAuthority = userDiscountCaseList.get(0);
                //人员折让限制金额
                Double pre_discount_money = basicDataUserDiscountAuthority.getPre_discount_money();
                int employee_id = basicDataUserDiscountAuthority.getEmployee_id();
                //已经使用过的折让金额
                Double discount_amonut = 0d;
                if(Tools.isNullOrEmpty(managerNum)) {
                    discount_amonut = posDiscountDao.getDiscountAmountByDiscountNum(param, employee_id, reportDate,billNum);
                }else {
                    discount_amonut = posDiscountDao.getDiscountAmountByDiscountNum(param, Integer.parseInt(managerNum), reportDate,billNum);
                }
                //计算可折让金额
                Double need_money = DoubleHelper.sub(pre_discount_money, discount_amonut, scale);
                
                //每单折让上限额
                Double single_discount_money = basicDataUserDiscountAuthority.getSingle_discount_money();
                boolean check_single_discount = false;//是否验证 每单折让上限额
                //每单折让差额 = 每单折让上限额 - POS前台输入的折让金额
                Double single_sub_money = 0.0;
                if(single_discount_money != null){
                	check_single_discount = true;
                    single_sub_money = DoubleHelper.sub(single_discount_money, discountr, scale);
                }
                
                if(need_money < 0 || (single_sub_money<0 && check_single_discount)) {
                    result.setCode(2);
                    result.setMsg("折让权限认证失败");
                }else {
                    Double subMoney = DoubleHelper.sub(need_money, discountr, scale);
                    if(subMoney >= 0) {
                        //角色id
                        int roleId = posDiscountDao.getRoleIdByOptNum(tenancyId, storeId, optNum);
                        //设置返回的结果集
                        setResultData4Discount(result, need_money, pre_discount_money, roleId);
                    }else {
                        result.setCode(2);
                        result.setMsg("折让权限认证失败");
                    }
                }
            } else {
                this.discountAuth4Role(result, optNum, reportDate, discountr,billNum);
            }
        }catch (Exception e) {
            result.setCode(2);
            result.setMsg("折让权限认证失败" + e.getMessage());
        }
    }

    /**
     *
     * @param result
     * @param optNum
     * @param reportDate
     * @param finalMoney
     * @throws Exception
     */
   @Deprecated
    private void giveAuth4Role(Data result, String optNum,Date reportDate,Double finalMoney) throws Exception {
        //角色id
        int roleId = posDiscountDao.getRoleIdByOptNum(result.getTenancy_id(), result.getStore_id(), optNum);
        //获取角色奉送限制
        List<BasicDataUserDiscountAuthority> roleDisocuntCaseList = posDiscountDao.getRoleDiscountCase(result.getTenancy_id(), roleId);
        //角色奉送限制金额
        if (null != roleDisocuntCaseList && roleDisocuntCaseList.size() > 0) {
            BasicDataUserDiscountAuthority basicDataRoleDiscountAuthority = roleDisocuntCaseList.get(0);
            Double pre_reward_money = basicDataRoleDiscountAuthority.getPre_reward_money();
//            String employee_id = basicDataRoleDiscountAuthority.getEmployee_id() + "";
            if (pre_reward_money > 0) {
                Double item_amonut = posDiscountDao.getItemAmountByManagerNum(result, optNum, reportDate);
                //计算可奉送金额
                Double need_money = DoubleHelper.sub(pre_reward_money, item_amonut, scale);
                //计算差额金额
                Double sub_money = DoubleHelper.sub(need_money, finalMoney, scale);
//                if(sub_money < 0) {
//                    result.setCode(2);
//                    result.setMsg("奉送权限认证失败");
//                }else {
                    //设置返回的结果集
                    setResultData4Gvie(result, sub_money);
//                }
            }
//            else if (pre_reward_money < 0) {
//                result.setCode(2);
//                result.setMsg("奉送权限认证失败");
//            }
        }else {
            result.setCode(3);
            result.setMsg("奉送权限数据集为空");
        }
    }

    /**
     *
     * @param result
     * @param optNum
     * @param reportDate
     * @param discountr
     * @throws Exception
     */
    private void discountAuth4Role(Data result, String optNum, Date reportDate, Double discountr,String billNum) throws Exception {
        //角色id
        int roleId = posDiscountDao.getRoleIdByOptNum(result.getTenancy_id(), result.getStore_id(), optNum);
        //获取角色折让限制
        List<BasicDataUserDiscountAuthority> roleDisocuntCaseList = posDiscountDao.getRoleDiscountCase(result.getTenancy_id(), roleId);
        //角色折让限制金额
        if (null != roleDisocuntCaseList && roleDisocuntCaseList.size() > 0) {
            BasicDataUserDiscountAuthority basicDataRoleDiscountAuthority = roleDisocuntCaseList.get(0);
            Double pre_discount_meony = basicDataRoleDiscountAuthority.getPre_discount_money();
            
//            int employee_id = basicDataRoleDiscountAuthority.getEmployee_id();
//			if (pre_discount_meony > 0) {
            Double discount_amonut = posDiscountDao.getDiscountAmountByDiscountNum(result, Integer.parseInt(optNum), reportDate,billNum);
            //计算可折让金额
            Double need_money = DoubleHelper.sub(pre_discount_meony, discount_amonut, scale);
            //计算最终差额
            Double sub_money = DoubleHelper.sub(need_money, discountr, scale);
            
            //每单折让上限额
            Double single_discount_money = basicDataRoleDiscountAuthority.getSingle_discount_money();
            boolean check_single_discount = false;//是否验证 每单折让上限额
            //每单折让差额 = 每单折让上限额 - POS前台输入的折让金额
            Double single_sub_money = 0.0;
            if(single_discount_money != null){
            	check_single_discount = true;
                single_sub_money = DoubleHelper.sub(single_discount_money, discountr, scale);
            }
            
            if(sub_money < 0 || (single_sub_money<0 && check_single_discount)) {
                result.setCode(2);
				result.setMsg("折让权限认证失败");
            }else {
                //设置返回的结果集
                setResultData4Discount(result, need_money, pre_discount_meony, roleId);
            }
//			}
//			else if (pre_discount_meony <= 0) {
//				result.setCode(2);
//				result.setMsg("折让权限认证失败");
//			}
        }else {
            result.setCode(3);
            result.setMsg("折让权限数据集为空");
        }
    }

    /**
     *
     * @param result
     * @param need_money
     * @throws Exception
     */
    @Deprecated
    private void setResultData4Gvie(Data result, Double need_money) throws Exception {
        List<JSONObject> jsonObjectList = new ArrayList<JSONObject>();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("is_give_item", 0);
        jsonObject.put("give_amount", need_money);
        jsonObjectList.add(jsonObject);
        result.setData(jsonObjectList);
        result.setCode(0);
        result.setMsg("奉送权限认证成功");
    }

    /**
     *
     * @param result
     * @param need_money
     * @param discount_money
     * @param roleId
     * @throws Exception
     */
    private void setResultData4Discount(Data result, Double need_money, Double discount_money, int roleId) throws Exception {
        List<JSONObject> jsonObjectList = new ArrayList<JSONObject>();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("is_discountr", 1);
        jsonObject.put("discountr_amount", need_money);
        jsonObject.put("discount_money", discount_money);
        jsonObject.put("roleId", roleId);
        jsonObjectList.add(jsonObject);
        result.setData(jsonObjectList);
        result.setCode(0);
        result.setMsg("折让权限认证成功");
    }

    @Deprecated
    private double getMethodMoney(Map<String, Object> oDish, Double itemPrice, int scale) throws Exception {
        double zfmoney = 0d;
        if (Tools.isNullOrEmpty(oDish.get("method")) == false) {
            //做法信息
            List<Map<String, String>> methods = (List<Map<String, String>>) oDish.get("method");
            for (Map<String, String> method : methods) {
                if (StringUtils.isNotEmpty(method.get("amount"))) {
                    double amount = Double.parseDouble(method.get("amount"));
                    String makeupWay = method.get("makeup_way");
                    double zfamount = 0d;
                    if (StringUtils.isNotEmpty(makeupWay)) {
                        if ("ADD".equalsIgnoreCase(makeupWay)) {
                            zfamount = amount;
                        }
                        if ("MULTI".equalsIgnoreCase(makeupWay)) {
                            zfamount = DoubleHelper.mul(itemPrice, amount, scale);
                        }
                        zfmoney = zfmoney + zfamount;
                    }
                }
            }
        }
        return zfmoney;
    }
    
    @Override
	public Data newBillDiscount(Data param) throws Exception
	{
		// TODO newBillDiscount
		String tenantId = param.getTenancy_id();
		Integer organId = param.getStore_id();

		Map<String, Object> map = ReqDataUtil.getDataMap(param);
		// 账单编号
		String billno = ParamUtil.getStringValue(map, "bill_num", true, PosErrorCode.NOT_NULL_BILL_NUM);
		Date reportDate = ParamUtil.getDateValue(map, "report_date", false, null);
		Integer shiftId = ParamUtil.getIntegerValue(map, "shift_id", false, null);
		String posNum = ParamUtil.getStringValue(map, "pos_num", false, null);
		String optNum = ParamUtil.getStringValue(map, "opt_num", false, null);
		// 批准人编号 二次授权人
		String managerNum = ParamUtil.getStringValue(map, "manager_num", false, null);
		// //0：取消打折 1：账单打折
		String mode = ParamUtil.getStringValue(map, "mode", true, PosErrorCode.NOT_NULL_MODE);
		// 折扣方案id 默认0
		Integer discountId = ParamUtil.getIntegerValue(map, "discount_id", false, null);
		// 折扣率 默认100
		Double discountRate = ParamUtil.getDoubleValue(map, "discount_rate", false, null);
		// 1:整单折扣、2:折扣方案、3:折让（作废）、4:团体会员折扣、5:会员折扣、6:会员价,7:线上优惠
		// 8：会员价+折扣方案,9：普通折扣率 10：单品折扣
		Integer discountMode = ParamUtil.getIntegerValue(map, "discount_mode", false, null);
		// 折扣原因id 默认0
		Integer discountReasonId = ParamUtil.getIntegerValue(map, "discount_reason_id", false, null);
		// 折让金额 默认0
		Double discountrAmount = ParamUtil.getDoubleValue(map, "discountr_amount", false, null);
		// 会员卡号
		String cardCode = ParamUtil.getStringValue(map, "card_code", false, null);
		// 手机号
		String mobil = ParamUtil.getStringValue(map, "mobil", false, null);
		// 会员编号
		String customerCode = ParamUtil.getStringValue(map, "customer_code", false, null);
		// 会员名称
		String customerName = ParamUtil.getStringValue(map, "customer_name", false, null);
		// 本次消费前积分
		Double customerCredit = ParamUtil.getDoubleValue(map, "customer_credit", false, null);
		Double mainBalance = ParamUtil.getDoubleValue(map, "main_balance", false, null);
		Double rewardBalance = ParamUtil.getDoubleValue(map, "reward_balance", false, null);
		
		String genericFile = null;
	    JSONObject genericFieldJo = new JSONObject();
	        
	    if(map.containsKey("customer_rank")){//会员级别
	       String  customerRank = (String) map.get("customer_rank");
	       genericFieldJo.put("customer_rank", customerRank);
	       genericFile = genericFieldJo.toString();
	     }

		// 如果二次授权人为空，则授权人为操作人，便于统计授权人的折让金额
		if (Tools.isNullOrEmpty(managerNum))
		{
			managerNum = optNum;
		}

		if (Tools.isNullOrEmpty(discountId))
		{
			discountId = 0;
		}
		if (Tools.isNullOrEmpty(discountMode))
		{
			discountMode = 0;
		}
		if (Tools.isNullOrEmpty(discountReasonId))
		{
			discountReasonId = 0;
		}

		if (Tools.isNullOrEmpty(discountRate) || discountRate.isNaN())
		{
			discountRate = 100d;
		}

		if (Tools.isNullOrEmpty(discountrAmount) || discountrAmount.isNaN())
		{
			discountrAmount = 0d;
		}
		discountrAmount = DoubleHelper.roundDown(discountrAmount, 2);

		if (Tools.isNullOrEmpty(customerCredit) || customerCredit.isNaN())
		{
			customerCredit = 0d;
		}
		if (Tools.isNullOrEmpty(mainBalance) || mainBalance.isNaN())
		{
			mainBalance = 0d;
		}
		if (Tools.isNullOrEmpty(rewardBalance) || rewardBalance.isNaN())
		{
			rewardBalance = 0d;
		}

		Timestamp currentTime = DateUtil.currentTimestamp();

		JSONObject billJson = posDiscountDao.getPosBillByBillnum(tenantId, organId, billno);
		String billProperty = null;
		String paymentState = null;
		Integer discountModeId = null;
		Integer discountCaseId = null;
		Double paymentAmount = 0d;
		Double billDiscountrAmount = 0d;
		if (null != billJson)
		{
			billProperty = billJson.optString("bill_property");
			paymentState = billJson.optString("payment_state");
			discountModeId = billJson.optInt("discount_mode_id");
			discountCaseId = billJson.optInt("discount_case_id");
			paymentAmount = billJson.optDouble("payment_amount");
			billDiscountrAmount = billJson.optDouble("discountr_amount");
			if (null == reportDate)
			{
				reportDate = ParamUtil.getDateValueByObject(billJson, "report_date");
			}
		}

		if (SysDictionary.BILL_PROPERTY_CLOSED.equals(billProperty))
		{
			throw SystemException.getInstance(PosErrorCode.BILL_CLOSED);
		}
		if (SysDictionary.PAYMENT_STATE_PAY.equals(paymentState))
		{
			throw SystemException.getInstance(PosErrorCode.BILL_PAYMENT_STATE_PAYING_ERROR);
		}
		
		Double paymentAmountDr = DoubleHelper.add(paymentAmount, billDiscountrAmount, scale);
		if (paymentAmountDr > 0 && discountrAmount > paymentAmountDr)
		{
			throw SystemException.getInstance(PosErrorCode.DISCOUNT_GREATER_THAN_PAYMENT);
		}

		String logTitle = null;
		Data data = new Data();
		if ("0".equals(mode))
		{
			discountMode = 0;

			List<JSONObject> itemList = posDishDao.getPosBillItemByBillNum(tenantId, organId, billno);
			List<Object[]> batchArgs = new ArrayList<Object[]>();
			for (JSONObject itemJson : itemList)
			{
				Integer itemDiscountModeId = itemJson.optInt("discount_mode_id");
				if (SysDictionary.DISCOUNT_MODE_10 == itemDiscountModeId || SysDictionary.DISCOUNT_MODE_11 == itemDiscountModeId)
				{
					discountMode = itemDiscountModeId;
				}
				else
				{
					batchArgs.add(new Object[]{ 0, 0, 100d, null, tenantId, organId, billno, itemJson.optInt("id") });
				}
			}

			if (batchArgs.size() > 0)
			{
				String updateItemSql = new String("update pos_bill_item set discount_mode_id = ?, discount_reason_id = ?, discount_rate = ?,discount_num=? where tenancy_id = ? and store_id = ? and bill_num=? and id=?");
				posDiscountDao.batchUpdate(updateItemSql, batchArgs);
			}
			if (SysDictionary.DISCOUNT_MODE_11 != discountModeId)
			{
				String updateBillSql = new String("update pos_bill set discount_num = ?, discount_mode_id = ?, discount_rate=?, discountr_amount=?, discount_case_id=?,discount_reason_id=? where bill_num = ? and store_id = ?");
				posDiscountDao.update(updateBillSql, new Object[]
				{ null, discountMode, 100d, 0, 0, 0, billno, organId });
			}

			if (SysDictionary.DISCOUNT_MODE_6 == discountModeId || SysDictionary.DISCOUNT_MODE_8 == discountModeId)
			{
				memberDao.deletePosBillMember(tenantId, organId, billno, SysDictionary.BILL_MEMBERCARD_HYJ01);
			}
			else if (SysDictionary.DISCOUNT_MODE_5 == discountModeId)
			{
				memberDao.deletePosBillMember(tenantId, organId, billno, SysDictionary.BILL_MEMBERCARD_HYZK02);
			}

			logTitle = "取消账单折扣";
			data.setCode(Constant.CODE_SUCCESS);
			data.setMsg(Constant.CANC_BILL_DISCOUNT_SUCCESS);
		}
		else if ("1".equals(mode))
		{

			if ((SysDictionary.DISCOUNT_MODE_6 == discountModeId && SysDictionary.DISCOUNT_MODE_2 == discountMode))
			{
				// 如果先使用会员价,再使用折扣方案,自动转成折上折,并查询会员信息
				discountMode = SysDictionary.DISCOUNT_MODE_8;
				List<JSONObject> memberList = memberDao.queryPosBillMember(tenantId, organId, billno, SysDictionary.BILL_MEMBERCARD_HYJ01);

				if (null != memberList && memberList.size() > 0)
				{
					JSONObject memberJson = memberList.get(0);
					// 会员卡号
					cardCode = ParamUtil.getStringValueByObject(memberJson, "card_code");
					// 手机号
					mobil = ParamUtil.getStringValueByObject(memberJson, "mobil");
					// 会员编号
					customerCode = ParamUtil.getStringValueByObject(memberJson, "customer_code");
					// 会员名称
					customerName = ParamUtil.getStringValueByObject(memberJson, "customer_name");
					// 本次消费前积分
					customerCredit = ParamUtil.getDoubleValueByObject(memberJson, "consume_before_credit");
					mainBalance = ParamUtil.getDoubleValueByObject(memberJson, "consume_before_main_balance");
					rewardBalance = ParamUtil.getDoubleValueByObject(memberJson, "consume_before_reward_balance");
				}
			}
			else if (SysDictionary.DISCOUNT_MODE_2 == discountModeId && SysDictionary.DISCOUNT_MODE_6 == discountMode)
			{
				// 如果先使用折扣方案,再使用会员价,自动转成折上折
				discountMode = SysDictionary.DISCOUNT_MODE_8;
				discountId = discountCaseId;
			}



//            List<JSONObject> itemList = posDishDao.getPosBillItemByBillNum(tenantId, organId, billno);
//            boolean result = false;
//
//            for (JSONObject itemJson : itemList) {
//                String item_property = itemJson.optString("item_property");
//                if ("MEALLIST".equals(item_property)) {
//                    continue;
//                }
//                Integer item_id = itemJson.optInt("item_id");
//                Integer item_unit_id = itemJson.optInt("item_unit_id");
//                String sql = "SELECT d.id from hq_discount_case_details d LEFT JOIN hq_discount_case c on d.discount_case_id = c.id where c.tenancy_id=? and c.id =? and d.item_id=? and d.item_unit_id=?";
//                SqlRowSet rs = posDiscountDao.query4SqlRowSet(sql, new Object[]{tenantId, discountId, item_id, item_unit_id});
//                if (rs.next()) {
//                    result = true;
//                    break;
//                }
//            }
//            if (!result) {
//                data.setCode(Constant.CODE_NULL_DATASET);
//                data.setMsg("订单不关联折扣方案");
//                data.setTenancy_id(tenantId);
//                data.setStore_id(organId);
//                data.setSecret(billno);
//                return data;
//            }




			// 更新账单
			String sqlBill = " update pos_bill set discount_num = ?, discount_mode_id = ?, discount_rate=?, discountr_amount=?, discount_case_id=?,discount_reason_id=? where bill_num = ? and store_id = ? ";
			posDiscountDao.update(sqlBill, new Object[]
			{ managerNum, discountMode, discountRate, discountrAmount, discountId, discountReasonId, billno, organId });

			// 更新账单明细
			String sqlBillItem = "update pos_bill_item set discount_mode_id = ?, discount_reason_id = ?, discount_rate = ?,discount_num=? where bill_num = ? and store_id = ? ";
			sqlBillItem += " and (discount_mode_id <> " + SysDictionary.DISCOUNT_MODE_10 + " or discount_mode_id is null) ";
			posDiscountDao.update(sqlBillItem.toString(), new Object[]
			{ 0d, 0d, 100d, managerNum, billno, organId });

			// 验证折扣方案是否有效
			if ((SysDictionary.DISCOUNT_MODE_2 == discountMode || SysDictionary.DISCOUNT_MODE_8 == discountMode) && null != discountId && 0 != discountId)
			{
				String nowDateS = DateUtil.getNowDateYYDDMM();
				Date nowDate = DateUtil.parseDate(nowDateS);
				int week = DateUtil.dayForWeek(nowDateS);
                String dayOfMonth=StringUtils.leftPad(""+ Calendar.getInstance().get(Calendar.DAY_OF_MONTH),2,"0");

                String sql = " select hdc.sarttime,hdc.endtime,hdc.running_cycle,hdc.day_running_cycle,hdc.zero_norm,(select bill_amount from pos_bill where bill_num = ? and store_id = ?) as bill_amount  " + " from hq_discount_case as hdc left join hq_discount_case_org as hdco on hdco.discount_case_id = hdc.id "
                        + " WHERE (hdc.running_cycle LIKE '%0'||?||'%' or hdc.day_running_cycle LIKE '%'||?||'%') AND hdc. ID = ? AND hdc.startdate <= ?  AND hdc.enddate >= ? AND hdc.valid_state = '1' AND hdco.store_id = ?";
				SqlRowSet rs = posDiscountDao.query4SqlRowSet(sql, new Object[]
                        { billno, organId, week,dayOfMonth, discountId, nowDate, nowDate, organId });
				if (rs.next())
				{
					if (rs.getDouble("bill_amount") < rs.getDouble("zero_norm"))
					{
						throw new SystemException(PosErrorCode.LOW_THAN_ZERO_NORM);
					}
				}
				else
				{
					throw new SystemException(PosErrorCode.INVALID_DISCOUNT);
				}


                //校验折扣方式可使用菜品
                String detailsSql = new String("SELECT count(d.id) count from hq_discount_case_details d left join pos_bill_item bi on d.tenancy_id=bi.tenancy_id and d.item_id=bi.item_id and d.item_unit_id=bi.item_unit_id where bi.tenancy_id=? and bi.store_id=? and bi.bill_num=? and d.discount_case_id=? and bi.id is not null and bi.item_property<> ?");
                SqlRowSet drs = posDiscountDao.query4SqlRowSet(detailsSql, new Object[]
                        { tenantId, organId, billno,discountId, SysDictionary.ITEM_PROPERTY_MEALLIST});
                int count =0;
                if (drs.next())
                {
                    count = drs.getInt("count");
                }
                if (0 == count)
                {
                    throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_DISCOUNT_CASE_DISH_ERROR);
                }

				String updateSql = new String(
						"update pos_bill_item bi set discount_mode_id=?,discount_reason_id = ? from hq_discount_case_details dc where bi.item_id=dc.item_id and bi.item_unit_name=dc.unit and dc.discount_case_id=? and discount_mode_id<>? and bi.tenancy_id=? and bi.store_id=? and bi.bill_num=?");
				posDiscountDao.update(updateSql.toString(), new Object[]
				{ discountMode, discountReasonId, discountId, SysDictionary.DISCOUNT_MODE_10, tenantId, organId, billno });
			}

			// 删除会员记录信息
			if (SysDictionary.DISCOUNT_MODE_6 == discountModeId || SysDictionary.DISCOUNT_MODE_8 == discountModeId)
			{
				memberDao.deletePosBillMember(tenantId, organId, billno, SysDictionary.BILL_MEMBERCARD_HYJ01);
			}
			else if (SysDictionary.DISCOUNT_MODE_5 == discountModeId)
			{
				memberDao.deletePosBillMember(tenantId, organId, billno, SysDictionary.BILL_MEMBERCARD_HYZK02);
			}

			// 会员信息
			if (SysDictionary.DISCOUNT_MODE_5 == discountMode)
			{
				// 记录会员刷卡信息
				PosBillMember billMember = new PosBillMember();
				billMember.setBill_num(billno);
				billMember.setReport_date(reportDate);
				billMember.setType(SysDictionary.BILL_MEMBERCARD_HYZK02);
				billMember.setCard_code(cardCode);
				billMember.setMobil(mobil);
				billMember.setCustomer_code(customerCode);
				billMember.setCustomer_name(customerName);
				billMember.setAmount(0d);
				billMember.setCredit(0d);
				billMember.setConsume_after_credit(customerCredit);
				billMember.setConsume_before_credit(customerCredit);
				billMember.setConsume_after_main_balance(mainBalance);
				billMember.setConsume_before_main_balance(mainBalance);
				billMember.setConsume_after_reward_balance(rewardBalance);
				billMember.setConsume_before_reward_balance(rewardBalance);
				billMember.setLast_updatetime(currentTime);
                billMember.setGeneric_field(genericFile);//通用字段
                
				memberDao.insertPosBillMember(tenantId, organId, billMember);
			}

			if (SysDictionary.DISCOUNT_MODE_6 == discountMode || SysDictionary.DISCOUNT_MODE_8 == discountMode)
			{
				this.SetCustomerVipPriceByItem(tenantId, organId, billno, discountMode, new ArrayList<Integer>());

				// 记录会员刷卡信息
				PosBillMember billMember = new PosBillMember();
				billMember.setBill_num(billno);
				billMember.setReport_date(reportDate);
				billMember.setType(SysDictionary.BILL_MEMBERCARD_HYJ01);
				billMember.setCard_code(cardCode);
				billMember.setMobil(mobil);
				billMember.setCustomer_code(customerCode);
				billMember.setCustomer_name(customerName);
				billMember.setAmount(0d);
				billMember.setCredit(0d);
				billMember.setConsume_after_credit(customerCredit);
				billMember.setConsume_before_credit(customerCredit);
				billMember.setConsume_after_main_balance(mainBalance);
				billMember.setConsume_before_main_balance(mainBalance);
				billMember.setConsume_after_reward_balance(rewardBalance);
				billMember.setConsume_before_reward_balance(rewardBalance);
				billMember.setLast_updatetime(currentTime);
				billMember.setGeneric_field(genericFile);//通用字段
				
				memberDao.insertPosBillMember(tenantId, organId, billMember);
			}

			if (SysDictionary.DISCOUNT_MODE_4 == discountMode || SysDictionary.DISCOUNT_MODE_5 == discountMode || SysDictionary.DISCOUNT_MODE_9 == discountMode)
			{
				sqlBillItem += " and discount_state='Y' ";
			}

			if (SysDictionary.DISCOUNT_MODE_2 != discountMode && SysDictionary.DISCOUNT_MODE_6 != discountMode && SysDictionary.DISCOUNT_MODE_8 != discountMode)
			{
				posDiscountDao.update(sqlBillItem.toString(), new Object[]
				{ discountMode, discountReasonId, discountRate, managerNum, billno, organId });
			}

			// referAmount = true;
			logTitle = "账单折扣";
			data.setCode(Constant.CODE_SUCCESS);
			data.setMsg(Constant.BILL_DISCOUNT_SUCCESS);
		}
		else
		{
			throw new SystemException(PosErrorCode.NOT_NULL_MODE);
		}

		// 刷新金额
		try
		{
			this.calcAmount(tenantId, organId, billno);
		}
		catch (SystemException se)
		{
			throw se;
		}
		catch (Exception e)
		{
			logger.info("Exception:" + ExceptionMessage.getExceptionMessage(e));
			throw new SystemException(PosErrorCode.OPER_ERROR);
		}
		
		//
		if (paymentAmount > 0)
		{
			billJson = posDiscountDao.getPosBillByBillnum(tenantId, organId, billno);

			Double billAmount = 0d;
			Double discountkAmount = 0d;
			Double giviAmount = 0d;
			Double paymentAmount2 = 0d;//折后金额
			if (null != billJson)
			{
				billAmount = billJson.optDouble("bill_amount");
				discountkAmount = billJson.optDouble("discountk_amount");
				giviAmount = billJson.optDouble("givi_amount");
				paymentAmount2 = DoubleHelper.sub(billAmount, DoubleHelper.add(discountkAmount, giviAmount, 4), 4);
			}

			if (discountrAmount > paymentAmount2)
			{
				throw SystemException.getInstance(PosErrorCode.DISCOUNT_GREATER_THAN_PAYMENT);
			}
		}
		
		// 数据上传
		// upload(tenantId,organId+"","","","",billno);
		posDiscountDao.savePosLog(tenantId, organId, posNum, optNum, optNum, shiftId, reportDate, Constant.TITLE, logTitle, "账单编号:" + billno, "折扣方式:" + discountMode + ";折扣率" + discountRate);

		this.updatePosBillForUpload(tenantId, organId, billno);
		
		data.setTenancy_id(tenantId);
		data.setStore_id(organId);
		data.setSecret(billno);
		return data;
	}

	@Override
	public Data singleCancelDiscount(Data param) throws Exception
	{
		// TODO Auto-generated method stub
		String tenancyId = param.getTenancy_id();
		Integer storeId = param.getStore_id();
		Data resultData = param.clone();
		// 获取参数
		List<?> dataList = param.getData();
		if (dataList == null || dataList.isEmpty())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}
		JSONObject paramJson = JSONObject.fromObject(dataList.get(0));
		Date reportDate = ParamUtil.getDateValueByObject(paramJson, "report_date", true, PosErrorCode.NOT_NULL_REPORT_DATE);
		Integer shiftId = ParamUtil.getIntegerValueByObject(paramJson, "shift_id", true, PosErrorCode.NOT_NULL_SHIFT_ID);
		String posNum = ParamUtil.getStringValueByObject(paramJson, "pos_num", true, PosErrorCode.NOT_NULL_POS_NUM);
		String optNum = ParamUtil.getStringValueByObject(paramJson, "opt_num", true, PosErrorCode.NOT_NULL_OPT_NUM);
		String billNum = ParamUtil.getStringValueByObject(paramJson, "bill_num", true, PosErrorCode.NOT_NULL_BILL_NUM);
		Integer rwid = ParamUtil.getIntegerValueByObject(paramJson, "rwid", true, PosErrorCode.NOT_NULL_RWID);
		String mode = ParamUtil.getStringValueByObject(paramJson, "mode", true, PosErrorCode.NOT_NULL_MODE);

		String optName = posDiscountDao.getEmpNameById(optNum, tenancyId, storeId);

		// 获取账单信息,验证账单付款状态
		JSONObject billJson = posDiscountDao.getPosBillByBillnum(tenancyId, storeId, billNum);
		String billProperty = null;
		String paymentState = null;
		Integer discountModeId = null;
		Integer discountCaseId = null;
		String discountNum = null;
		Double discountRate = 100d;
		Integer discountReasonId = null;
		if (null != billJson)
		{
			billProperty = billJson.optString("bill_property");
			paymentState = billJson.optString("payment_state");
			discountModeId = billJson.optInt("discount_mode_id");
			discountCaseId = billJson.optInt("discount_case_id");
		}

		if (SysDictionary.BILL_PROPERTY_CLOSED.equals(billProperty))
		{
			throw SystemException.getInstance(PosErrorCode.BILL_CLOSED);
		}
		if (SysDictionary.PAYMENT_STATE_PAY.equals(paymentState))
		{
			throw SystemException.getInstance(PosErrorCode.BILL_PAYMENT_STATE_PAYING_ERROR);
		}

		if ("1".equals(mode))
		{
			// 查询账单明细
			JSONObject itemJson = posDiscountDao.getPosBillItemByRwid(tenancyId, storeId, billNum, rwid);
			if (null != itemJson && false == itemJson.isEmpty())
			{
				// 验证菜品是否是套餐明细
				String itemProperty = ParamUtil.getStringValueByObject(itemJson, "item_property");
				if (SysDictionary.ITEM_PROPERTY_MEALLIST.equals(itemProperty))
				{
					throw SystemException.getInstance(PosErrorCode.MEALLIST_NOT_PERMIT_SINGLE_CANCEL_DISCOUNT_ERROR);
				}
				// 验证菜品是否存在优惠
				Integer itemDiscountModedId = ParamUtil.getIntegerValueByObject(itemJson, "discount_mode_id");
				if (null == itemDiscountModedId || 0 == itemDiscountModedId)
				{
					throw SystemException.getInstance(PosErrorCode.ITEM_NOT_EXISTS_DISCOUNT_ERROR);
				}
				//
				String itemRemark = ParamUtil.getStringValueByObject(itemJson, "item_remark");
				if (false == Tools.isNullOrEmpty(itemRemark))
				{
					throw SystemException.getInstance(PosErrorCode.RETREAT_AND_GIVE_NOT_PERMIT_SINGLE_CANCEL_DISCOUNT_ERROR);
				}
			}
			else
			{
				// 验证菜品是否存在
				throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_DISH_ITEM);
			}

			// 取消菜品折扣
			posDiscountDao.updateBillItemForDiscountByRwid(tenancyId, storeId, billNum, rwid, SysDictionary.DISCOUNT_MODE_SINGLE_CANCEL, null, 100d, 0);
			// 计算账单金额
			this.calcAmount(tenancyId, storeId, billNum);

			// 记录操作日志
			posDiscountDao.savePosLog(tenancyId, storeId, posNum, optNum, optName, shiftId, reportDate, Constant.TITLE, "单品取消折扣", "账单编号:" + billNum, "");
		}
		else
		{
			JSONObject itemJson = posDiscountDao.getPosBillItemByRwid(tenancyId, storeId, billNum, rwid);
			if (null != itemJson && false == itemJson.isEmpty())
			{
				// 验证菜品是否是套餐明细
				String itemProperty = ParamUtil.getStringValueByObject(itemJson, "item_property");
				if (SysDictionary.ITEM_PROPERTY_MEALLIST.equals(itemProperty))
				{
					throw SystemException.getInstance(PosErrorCode.MEALLIST_NOT_PERMIT_SINGLE_CANCEL_DISCOUNT_ERROR);
				}
				//
				String itemRemark = ParamUtil.getStringValueByObject(itemJson, "item_remark");
				if (false == Tools.isNullOrEmpty(itemRemark))
				{
					throw SystemException.getInstance(PosErrorCode.RETREAT_AND_GIVE_NOT_PERMIT_SINGLE_CANCEL_DISCOUNT_ERROR);
				}
			}
			else
			{
				// 验证菜品是否存在
				throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_DISH_ITEM);
			}

			posDiscountDao.updateBillItemForDiscountByRwid(tenancyId, storeId, billNum, rwid, discountModeId, discountNum, discountRate, discountReasonId);

			// 计算账单金额
			this.calcAmount(tenancyId, storeId, billNum);

			// 记录操作日志
			posDiscountDao.savePosLog(tenancyId, storeId, posNum, optNum, optName, shiftId, reportDate, Constant.TITLE, "恢复单品折扣", "账单编号:" + billNum, "");
		}
		resultData.setCode(Constant.CODE_SUCCESS);
		resultData.setData(null);
		return resultData;
	}

	@Override
	public Data cancalDiscountDeleteMeber(Data param) throws Exception {
		
		Data data = new Data();
		String tenantId = param.getTenancy_id();
		Integer organId = param.getStore_id();
		
		Map<String, Object> map = ReqDataUtil.getDataMap(param);
		//0：取消打折 1：账单打折
		String mode = ParamUtil.getStringValue(map, "mode", true, PosErrorCode.NOT_NULL_MODE);
		String billno = ParamUtil.getStringValue(map, "bill_num", true, PosErrorCode.NOT_NULL_BILL_NUM);

		if("0".equals(mode)){
			data = newBillDiscount(param);
			memberDao.deletePosBillMember(tenantId, organId, billno, SysDictionary.BILL_MEMBERCARD_JFZS06);
		}else{
			logger.info("此方法是取消折扣和删除会员记录，mode的值应为0");
			throw new SystemException(PosErrorCode.NOT_NULL_MODE);
		}
		return data;
		
	}
}