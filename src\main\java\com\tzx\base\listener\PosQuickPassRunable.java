package com.tzx.base.listener;

import java.util.ArrayList;
import java.util.List;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.log4j.Logger;

import com.tzx.base.common.util.PosPropertyUtil;
import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.util.HttpUtil;

/**
 * Created by Administrator on 2017-04-21.
 */
public class PosQuickPassRunable{
    Logger logger = Logger.getLogger(PosQuickPassRunable.class);
//    private final  String  URL = "/tzxsaas/payment/news/post";  //请求地址待确认
    private final  String  URL = "/api/queryPayStatus";  //请求地址待确认



    private String tenancy_id;
    private String store_id;
    private String order_no;
    private String service_type;
    private String pos_num;
    private String opt_num;
    private String pay_type;

    public PosQuickPassRunable() {
    }

    public PosQuickPassRunable(String tenancy_id,String store_id,String order_no, String service_type, String pos_num, String opt_num, String pay_type) {
        this.tenancy_id = tenancy_id;
        this.store_id = store_id;
        this.order_no = order_no;
        this.service_type = service_type;
        this.pos_num = pos_num;
        this.opt_num = opt_num;
        this.pay_type = pay_type;
    }

    public JSONObject getPaymentState() throws Exception{

        JSONObject object = new JSONObject();
        object.put("tenancy_id",tenancy_id);
        object.put("store_id",store_id);
        object.put("order_no",order_no);
        object.put("service_type",service_type);
        object.put("pos_num",pos_num);
        object.put("opt_num",opt_num);
        object.put("pay_type",pay_type);
        List<JSONObject> datas = new ArrayList<JSONObject>();
        datas.add(object);
        Data reqData = Data.get(tenancy_id,Integer.valueOf(store_id),0);
        reqData.setType(Type.QUERY_PAY_STATE);
        reqData.setOper(Oper.check);
        reqData.setSecret("0");
        reqData.setData(datas);
        String QUICK_PASS_URL=PosPropertyUtil.getMsg("secondpay.dataupload.url");
//        String  QUICK_PASS_URL = PosPropertyUtil.getMsg(Constant.QUICK_PASS_URL);
        logger.debug("查询秒付交易状态请求参数:"+JSONObject.fromObject(reqData).toString());
        String result = HttpUtil.sendPostRequest(QUICK_PASS_URL+URL,JSONObject.fromObject(reqData).toString());
//        String result = HttpUtil.sendHttpPostRequest("http://mte7e6.meishijia.com/payment/news/post",JSONObject.fromObject(reqData).toString());
        logger.debug("查询秒付交易状态返回内容:"+result);
        String payment_state="3";
        if(null!=result&&!"".equals(result)){
            JSONObject obj = JSONObject.fromObject(result);

            JSONArray jsonArray =  obj.optJSONArray("data");
            if(jsonArray!=null&&jsonArray.size()>0){
                JSONObject data = JSONObject.fromObject(jsonArray.get(0));
                payment_state = data.optString("payment_state");
                return data;
            }
        }
        return  null;
    }
}
