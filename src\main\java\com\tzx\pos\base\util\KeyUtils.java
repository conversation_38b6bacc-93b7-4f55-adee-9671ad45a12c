package com.tzx.pos.base.util;

import java.security.Key;
import java.security.MessageDigest;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;

import org.apache.log4j.Logger;

import com.sun.org.apache.xml.internal.security.utils.Base64;

/**
 * <AUTHOR>
 *
 */
public class KeyUtils {
	private static final Logger	logger	= Logger.getLogger(KeyUtils.class);
	private static final String AESTYPE = "AES/ECB/PKCS5Padding";
	
	/**
	 * @param key
	 * @return
	 * @throws Exception
	 */
	public static Key generateKey(String key) throws Exception{
		try{
			SecretKeySpec keySpec = new SecretKeySpec(key.getBytes("utf-8"), "AES");
			return keySpec;
		}catch(Exception e) {
			logger.error(e);
			throw e;
		}
	}
	
	/**
	 * 
	 * AES编码
	 * @param keyStr
	 * @param plainText
	 * @return
	 */
	public static String AES_Encrype(String keyStr,String plainText){
		byte[]encrypt = null;
		try {
			Key key = generateKey(keyStr);
			Cipher cipher = Cipher.getInstance(AESTYPE);
			cipher.init(Cipher.ENCRYPT_MODE, key);
			encrypt = cipher.doFinal(plainText.getBytes("utf-8"));
		} catch (Exception e) {
			logger.error(e);
			e.printStackTrace();
		}
		return new String(Base64.encode(encrypt));
	}
	
	/**
	 * AES解码
	 * @param keyStr
	 * @param plainText
	 * @return
	 */
	public static String AES_Decrype(String keyStr,String plainText){
		byte[]encrypt = null;
		try {
			Key key = generateKey(keyStr);
			Cipher cipher = Cipher.getInstance(AESTYPE);
			cipher.init(Cipher.DECRYPT_MODE, key);
			encrypt = cipher.doFinal(Base64.decode(plainText));
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(e);
		}
		return new String(encrypt);
	}
	/**
	 * @param s
	 * @return
	 */
	public static String getMD5(String s) {
        char hexDigits[]={'0','1','2','3','4','5','6','7','8','9','a','b','c','d','e','f'};        
        try {
            byte[] btInput = s.getBytes("utf-8");
            MessageDigest mdInst = MessageDigest.getInstance("MD5");
            mdInst.update(btInput);
            byte[] md = mdInst.digest();
            int j = md.length;
            char str[] = new char[j * 2];
            int k = 0;
            for (int i = 0; i < j; i++) {
                byte byte0 = md[i];
                str[k++] = hexDigits[byte0 >>> 4 & 0xf];
                str[k++] = hexDigits[byte0 & 0xf];
            }
            return new String(str);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e);
            return null;
        }
    }
	
	/**
	 * 电子发票
	 * @param content
	 * @return
	 */
	public static String encryptASE_MD5(String content,String password) {
		String aesEncrype = KeyUtils.AES_Encrype(password, content).trim();
		aesEncrype = aesEncrype.replace("\r\n", "");
		aesEncrype = aesEncrype.replace("\n", "");
		String md5str = KeyUtils.getMD5(aesEncrype).trim();
		md5str = md5str.replace("\r\n", "");
		md5str = md5str.replace("\n", "");
		return md5str;
	}
}
