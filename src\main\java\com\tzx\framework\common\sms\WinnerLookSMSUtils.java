package com.tzx.framework.common.sms;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpHeaders;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.params.CoreConnectionPNames;
import org.apache.http.params.CoreProtocolPNames;
import org.apache.log4j.Logger;
import org.dom4j.Attribute;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;

import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.HttpUtil;

import net.sf.json.JSONObject;

/**
 * 云信留客短信平台接口文档:http://www.winnerlook.com/downLoadcenter.html
 * <AUTHOR>
 *
 */
public class WinnerLookSMSUtils {

	/**
	 * 普通短信接口
	 */
	private static String normalUrl = "http://112.124.24.5/api/MsgSend.asmx";
	
	/**
	 * 高速触发接口
	 */
	private static String highSpeedUrl = "http://120.55.197.77:1210/Services/MsgSend.asmx";
	
	private static final Logger	log	= Logger.getLogger(WinnerLookSMSUtils.class);
	
	//测试方法
	public static void main(String[] args) {
		/*List<NameValuePair> params = packageParamByJson4Post(new JSONObject());
		httpPost("http://192.168.40.13:8080/tzxsaas/crmRest/post", params);*/
		JSONObject json  = new JSONObject();
		json.put("userCode", "pztxcf");
		json.put("userPass", "tzxTZX123");
		json.put("DesNo", "15292866807");
		json.put("Msg", "您的验证码是8888【普照天星】");
		json.put("Channel", "0");
		JSONObject rr;
		try {
			rr = sendHighSpeedSmS(json);
			System.out.println(rr.optString("returnCode"));
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		/*
		//JSONObject json  = new JSONObject();
		json.clear();
		json.put("userCode", "pztxyx");
		json.put("userPass", "pztxYX0104");
		json.put("DesNo", "15292866807");
		json.put("Msg", DateUtil.getNowDateYYDDMMHHMMSS()+"时间测试.发送【普照天星】");
		json.put("Channel", "86");
		JSONObject ss;
		try {
			ss = sendNormalSmS(json);
			System.out.println(ss.optString("msg"));
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}*/
		
		
		//JSONObject json  = new JSONObject();
		json.clear();
		json.put("userCode", "pztxyx");
		json.put("userPass", "pztxYX0104");
		JSONObject ss2;
		try {
			ss2 = normalSmSGetBalance(json);
			System.out.println(ss2.optString("msg"));
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		
		
		//JSONObject json  = new JSONObject();
		
		json.clear();
		json.put("userCode", "pztxcf");
		json.put("userPass", "tzxTZX123");
		JSONObject ss1;
		try {
			ss1 = HighSpeedSmSGetBalance(json);
			System.out.println(ss1.optString("msg"));
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		
	}
	
	
	/**
	 * 普通短信发送 (支持群发) 营销类
	 * json入参中需要包括:
	 * 		1.userCode 登录名称
	 * 		2.userPass 登录密码
	 * 		3.DesNo	 手机号码，多个号码用英文半角逗号分隔例如：13900000000,13900000001每次提交不多于500个号码
	 * 		4.Msg	短信内容。Get或者Post方式注意要用UTF8编码
	 * 		5.Channel　　通道号　没有可以传入0
	 * @param json
	 * @return json中包括:
	 * 				1.success用来判断成功失败
	 * 				2.msg记录了失败原因或者返回的code
	 * 				3.returnCode
	 * @throws Exception 
	 */
	public static JSONObject sendNormalSmS(JSONObject json) {
		JSONObject result = new JSONObject();
		Long num = 0l;
		try {
			if(null == json ){
				result.put("success", false);
				result.put("reason", "参数体不能为空");
				result.put("returnCode", -1.11);
				return result;
			}
			JSONObject checkresult = checkParams(json, "userCode","userPass","DesNo","Msg","Channel");
			if(!checkresult.optBoolean("success")){
				result.put("success", false);
				result.put("reason", checkresult.optString("msg"));
				return result;
			}
			String content = json.optString("Msg").trim()+json.optString("sign").replace("[", "【").replace("]", "】");
			json.put("Msg", content);
			List<NameValuePair> params = packageParamByJson4Post(json);
			String returnData =httpPost(normalUrl+"/SendMsg", params);
			
			
			Document document = DocumentHelper.parseText(returnData);
			num = Long.valueOf(document.getRootElement().getTextTrim());
			if(num>0){
				result.put("success", true);
				result.put("returnCode", num);
			}else{
				result.put("reason", "返回值不正确,短信发送失败");
				result.put("returnCode", num);
			}
		} catch (Exception e) {
			result.put("success", false);
			result.put("reason", "调用发生异常,短信发送失败");
			result.put("returnCode",  -2.2);
			StringWriter sw = new StringWriter();   
            e.printStackTrace(new PrintWriter(sw, true));   
            String strs = sw.toString();  
            result.put("exp", strs);
			e.printStackTrace();
		}
		return result;
	}
	
	
	/**
	 * 高速触发短信余额查询接口 交易类
	 *  json入参中需要包括:
	 * 		1.userCode 登录名称
	 * 		2.userPass 登录密码
	 * @param json
	 * @return json中包括:
	 * 				1.success用来判断成功失败
	 * 				2.msg记录了失败原因或者返回的code
	 * @throws Exception
	 */
	public static JSONObject sendHighSpeedSmS(JSONObject json){
		JSONObject result = new JSONObject();
		Long num = 0l;
		try {
			if(null == json ){
				result.put("success", false);
				result.put("reason", "参数体不能为空");
				result.put("returnCode", -1.11);
				return result;
			}
			JSONObject checkresult = checkParams(json, "userCode","userPass","DesNo","Msg","Channel");
			if(!checkresult.optBoolean("success")){
				result.put("success", false);
				result.put("reason", checkresult.optString("msg"));
				return result;
			}
			String content = json.optString("Msg").trim()+json.optString("sign").replace("[", "【").replace("]", "】");
			json.put("Msg", content);
			List<NameValuePair> params = packageParamByJson4Post(json);
			String returnData = httpPost(highSpeedUrl+"/SendMsg",params);

			Document document = DocumentHelper.parseText(returnData);
			num = Long.valueOf(document.getRootElement().getTextTrim());
			
			if(num>0){
				result.put("success", true);
				result.put("returnCode", num);
			}else{
				result.put("reason", "返回值不正确,短信发送失败");
				result.put("returnCode", num);
			}
		} catch (Exception e) {
			result.put("success", false);
			result.put("reason", "调用发生异常,短信发送失败");
			result.put("returnCode", -2.2);
			StringWriter sw = new StringWriter();   
            e.printStackTrace(new PrintWriter(sw, true));   
            String strs = sw.toString();  
            result.put("exp", strs);
			e.printStackTrace();
		}
		return result;
	}
	
	
	
	/**
	 * 普通短信余额查询接口  营销短信   返回值为金额
	 *  json入参中需要包括:
	 * 		1.userCode 登录名称
	 * 		2.userPass 登录密码
	 * @param json
	 * @return json中包括:
	 * 				1.success用来判断成功失败
	 * 				2.msg记录了失败原因或者返回的code
	 * @throws Exception
	 */
	public static JSONObject normalSmSGetBalance(JSONObject json) throws Exception{
		JSONObject result = new JSONObject();
	/*	if(null == json ){
			result.put("success", false);
			result.put("msg", "参数体不能为空");
			return result;
		}
		JSONObject checkresult = checkParams(json, "userCode","userPass");
		if(!checkresult.optBoolean("success")){
			result.put("success", false);
			result.put("msg", checkresult.optString("msg"));
			return result;
		}*/
		List<NameValuePair> params = packageParamByJson4Post(json);
		String returnData =httpPost(normalUrl+"/GetBalance", params);
		Double num = 0d;
		
		Document document = DocumentHelper.parseText(returnData);
		num = Double.valueOf(document.getRootElement().getTextTrim());
		if(num>0){
			result.put("success", true);
			result.put("returnCode", num);
		}else{
			result.put("success", false);
			result.put("returnCode", num);
		}
		return result;
	}
	
	
	
	
	/**
	 * 高速触发短信余额查询接口  交易类短信   返回值为条数
	 *  json入参中需要包括:
	 * 		1.userCode 登录名称
	 * 		2.userPass 登录密码
	 * @param json
	 * @return json中包括:
	 * 				1.success用来判断成功失败
	 * 				2.msg记录了失败原因或者返回的code
	 * @throws Exception
	 */
	public static JSONObject HighSpeedSmSGetBalance(JSONObject json) throws Exception{
		JSONObject result = new JSONObject();
		
	/*	if(null == json ){
			result.put("success", false);
			result.put("msg", "参数体不能为空");
			return result;
		}
		JSONObject checkresult = checkParams(json, "userCode","userPass");
		if(!checkresult.optBoolean("success")){
			result.put("success", false);
			result.put("msg", checkresult.optString("msg"));
			return result;
		}*/
		List<NameValuePair> params = packageParamByJson4Post(json);
		String returnData = httpPost(highSpeedUrl+"/GetBalance",params);
		Integer num = 0;
	
		Document document = DocumentHelper.parseText(returnData);
		num = Integer.valueOf(document.getRootElement().getTextTrim());
		
		if(num>0){
			result.put("success", true);
			result.put("returnCode", num);
		}else{
			result.put("success", false);
			result.put("returnCode", num);
		}
		return result;
	}
	
	
	

	
	/**
	 * post请求参数封装
	 * @param json
	 * @return
	 */
	private static List<NameValuePair> packageParamByJson4Post(JSONObject json){
		List<NameValuePair> params = new ArrayList<NameValuePair>();
		Iterator it = json.keys();
		while(it.hasNext()){
			String key = (String) it.next();
			String value = json.optString(key);
			params.add(new BasicNameValuePair(key, value));
		}
		return params;
	}
	
	/**
	 * get请求参数封装
	 * @param json
	 * @return
	 */
	private static String packageParamByJson4Get(JSONObject json){
		StringBuffer sb = new StringBuffer();
		Iterator it = json.keys();
		while(it.hasNext()){
			String key = (String) it.next();
			String value = json.optString(key);
			if(sb.length() == 0){
				sb.append(key+"="+value);
			}else{
				sb.append("&"+key+"="+value);
			}
			
		}
		return sb.toString();
	}
	
	/**
	 * params包含所有参数,checkKeys是需要校验的key
	 * @param params
	 * @param checkKeys
	 * @return
	 */
	private static JSONObject checkParams(JSONObject params,String...checkKeys){
		JSONObject json = new JSONObject();
		StringBuffer sb = new StringBuffer();
		boolean flag = true;
		if(checkKeys.length >0){
			for(String key :checkKeys){
				if(!params.containsKey(key) || StringUtils.isBlank(params.optString(key))){
					sb.append(key+" ");
					flag = false;
				}
			}
		}
		if(flag){
			json.put("success", true);
		}else{
			json.put("success", false);
			json.put("msg", sb.toString()+"参数不能为空");
		}
		return json;
	}
	
	
    
	private static String httpPost(String url,List<NameValuePair> params) {
         String result = "";
	     try {
	         HttpClient httpclient = new DefaultHttpClient();
	         httpclient.getParams().setParameter(CoreConnectionPNames.CONNECTION_TIMEOUT, 3000);
	         httpclient.getParams().setParameter(CoreProtocolPNames.HTTP_CONTENT_CHARSET, "UTF-8");
	         httpclient.getParams().setParameter(HttpHeaders.CONTENT_TYPE, "application/x-www-form-urlencoded");
	         HttpPost httpPost = new HttpPost(url);            
	         httpPost.setEntity(new UrlEncodedFormEntity(params, "UTF-8"));
	         HttpResponse response = httpclient.execute(httpPost);
	         HttpEntity entity = response.getEntity();
	         if (entity != null) {    
	         	 InputStream instreams = entity.getContent();    
	         	 result = convertStreamToString(instreams);  
	          }
	     } catch (Exception e) {
	    	 e.printStackTrace();
	     }
	     return result;
	}
 
	 private static String httpGet(String url,String params){
	     String result="";        
	     try{
	         HttpClient client=new DefaultHttpClient();            
	         if(params!=""){
	             url=url+"?"+params;
	         }            
	         HttpGet httpget=new HttpGet(url);
	         HttpResponse response=client.execute(httpget);            
	         HttpEntity entity=response.getEntity();
	         if (entity != null) {    
	         	 InputStream instreams = entity.getContent();    
	         	 result = convertStreamToString(instreams);  
	          }
	     }catch(Exception e){
	    	 e.printStackTrace();
	     }
	     return result;
	 }
	 
	 private static String convertStreamToString(InputStream is) {      
	        BufferedReader reader = new BufferedReader(new InputStreamReader(is));      
	        StringBuilder sb = new StringBuilder();      
	       
	        String line = null;
	        try {      
	            while ((line = reader.readLine()) != null) {  
	                sb.append(line + "\n");      
	            }      
	        } catch (IOException e) {      
	            e.printStackTrace();      
	        } finally {
	            try {
	                is.close();
	            } catch (IOException e) {
	               e.printStackTrace();
	            }
	        }
	        return sb.toString();      
	    }
	 
}
