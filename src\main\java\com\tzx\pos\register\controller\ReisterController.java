package com.tzx.pos.register.controller;

import java.io.IOException;
import java.io.PrintWriter;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.tzx.pos.register.exception.ExceptionLog;
import com.tzx.pos.register.service.RegisterService;
import com.tzx.pos.register.thread.UploadQueen;
import com.tzx.pos.register.util.DeviceCodeQueen;
import com.tzx.pos.register.util.InParam;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

@Controller("posRegister")
@RequestMapping("/posRegister")
public class ReisterController extends ExceptionLog {
	private static final Logger logger = Logger.getLogger(ReisterController.class);

	@Resource(name = RegisterService.NAME)
	private RegisterService registerService;
	@Autowired
	private UploadQueen queen;
	@Autowired
	private DeviceCodeQueen deviceCodeQueen;

	public ReisterController() {

	}

	/**
	 * 验证注册接口 根据设备唯一码判断是否注册
	 * 
	 * @param request
	 * @param response
	 * @param jsobj
	 * @throws Exception
	 */
	@SuppressWarnings("unused")
	@RequestMapping(value = "/registerByCode", method = RequestMethod.POST)
	@ResponseBody
	public void registerByCode(HttpServletRequest request, HttpServletResponse response, @RequestBody JSONObject jsobj)
			throws Exception {
		logger.info("registerByCode 传入参数：" + jsobj.toString());
		response.setContentType("text/html; charset=UTF-8");
		PrintWriter out = null;
		JSONObject responseJson = new JSONObject();
		Integer i = 0;
		List<JSONObject> listOrgan = null;
		// 查询是否下发数据成功---暂时不做处理
		Long count = registerService.queryHqDevicesCount();
		if (1 == 2) {// 预留开发
			responseJson.put("msg", "数据未下发完毕，请稍后注册");
			responseJson.put("code", "-99");
		} else {
			responseJson = checkInParam(jsobj, InParam.registerByCode);

			if (responseJson.getBoolean("_if")) {
				listOrgan = registerService.queryOrgan();
				List<JSONObject> oldList = new ArrayList();
				// 判断devices_ip 是否有记录 如果有则是已注册 如果没有在判断唯一吗是否有记录
				if (!jsobj.getString("devices_ip").equals("order_device")) {
					oldList = registerService.registerByDeviceIp(jsobj);
				}
				List<JSONObject> listJson = registerService.registerByCode(jsobj);
				if (oldList.size() > 0) {

					String device_code = "";
					JSONObject data = new JSONObject();
					List<List<JSONObject>> list = new ArrayList<List<JSONObject>>();

					//
					list.add(oldList);
					list.add(listOrgan);
					data = getData(list);
					data.put("is_register", true);
					responseJson.put("code", 0);
					if (oldList.get(0).getString("valid_state").equals("0")) {
						responseJson.put("msg", "已停用");
					} else {
						responseJson.put("msg", "已注册");
					}

					responseJson.put("data", data);
					if (listJson.size() == 0) {
						jsobj.put("device_unique_code", jsobj.getString("devices_ip"));
						jsobj.put("store_id", listOrgan.get(0).get("id"));
						jsobj.put("tenancy_id", listOrgan.get(0).get("tenancy_id"));
						List<String> whereList = new ArrayList<String>();
						whereList.add("devices_ip");
						i = registerService.updateRegister(jsobj, whereList, whereList);
						if (i == 0) {
							throw new RuntimeException("老设备数据更新失败");
						}
					}

				} else {

					JSONObject data = new JSONObject();
					List<List<JSONObject>> list = new ArrayList<List<JSONObject>>();

					list.add(listOrgan);

					if (listJson.size() > 0) {
						data.put("is_register", true);
						if (listJson.get(0).getString("valid_state").equals("0")) {
							responseJson.put("msg", "已停用");

						} else {
							responseJson.put("msg", "已注册");
						}
						list.add(listJson);
					} else {
						// 未注册
						data.put("is_register", false);
						responseJson.put("msg", "未注册");
					}
					data = getData(list);
					responseJson.put("code", 0);
					responseJson.put("data", data);

				}
			}
		}

		try {
			out = response.getWriter();
			out.print(responseJson.toString());
			logger.info(responseJson.toString());
			out.flush();
			out.close();
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} finally {
			if (i > 0) {
				Thread t = new Thread(new Runnable() {
					@Override
					public void run() {
						synchronized (queen) {
							queen.notifyAll();
						}

					}
				});
				t.start();
			}
		}

	}

	/**
	 * 校验注册key并保存信息
	 * 
	 * @param request
	 * @param response
	 * @param jsobj
	 * @throws Exception
	 */
	@RequestMapping(value = "/checkCode", method = RequestMethod.POST)
	public void checkCode(HttpServletRequest request, HttpServletResponse response, @RequestBody JSONObject jsobj)
			throws Exception {
		logger.info("checkCode 传入参数：" + jsobj.toString());
		response.setContentType("text/html; charset=UTF-8");
		PrintWriter out = null;
		JSONObject responseJson = new JSONObject();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss", Locale.CHINA);
		boolean b = false;
		boolean _if = true;
		Long count = registerService.queryHqDevicesCount();
		if (1 == 0) {
			responseJson.put("msg", "数据未下发完，请稍后");
			responseJson.put("code", "-99");
		} else {
			List<JSONObject> list = null;
			responseJson = checkInParam(jsobj, InParam.checkCode);
			if (responseJson.getBoolean("_if")) {

				List<JSONObject> listJson = registerService.registerByCode(jsobj);
				if (listJson.size() > 0) {
					responseJson.put("msg", "该设备已注册");
					responseJson.put("code", "-99");
					responseJson.put("data", listJson.get(0));
				} else {
					if (jsobj.getString("devices_ip") != null && jsobj.getString("devices_ip").equals("order_device")) {
						jsobj.put("valid_state", 1);
						jsobj.put("device_type", "OTHER_TERMINAL");
						jsobj.put("audit_state", 1);
						jsobj.put("business_area", 0);
						jsobj.put("last_operator", "admin");
						jsobj.put("last_updatetime", sdf.format(new Date()));
						jsobj.put("printer", 0);
						jsobj.put("audit_operator", "admin");
						jsobj.put("audit_updatetime", sdf.format(new Date()));
						jsobj.put("is_site", 0);
						jsobj.put("devices_properties", "CLIENT");
						deviceCodeQueen.put(jsobj);
						b = saveDevices(jsobj);
						if (!b) {
							responseJson.put("msg", "注册保存信息失败");
							responseJson.put("code", "-99");
							responseJson.put("data", jsobj);
						} else {

							// 保存后返回信息
							responseJson.put("msg", "成功");
							responseJson.put("code", "0");
							responseJson.put("data", jsobj);

						}

					} else {

						list = registerService.queryOrganByRegisterKey(jsobj);
						if (list.size() > 0) {
							// 符合规则 生成的devices_code值执行保存设备信息 saveDevices

							jsobj.put("valid_state", 1);
							jsobj.put("device_type", "OTHER_TERMINAL");
							jsobj.put("audit_state", 1);
							jsobj.put("business_area", 0);
							jsobj.put("last_operator", "admin");
							jsobj.put("last_updatetime", sdf.format(new Date()));
							jsobj.put("printer", 0);
							jsobj.put("audit_operator", "admin");
							jsobj.put("audit_updatetime", sdf.format(new Date()));
							jsobj.put("is_site", 0);
							jsobj.put("devices_properties", "CLIENT");
							deviceCodeQueen.put(jsobj);
							b = saveDevices(jsobj);
							if (!b) {
								responseJson.put("msg", "注册保存信息失败");
								responseJson.put("code", "-99");
								responseJson.put("data", jsobj);
							} else {
								// 保存后返回信息
								responseJson.put("msg", "成功");
								responseJson.put("code", "0");
								responseJson.put("data", jsobj);

								Integer i = registerService.updatePosDataVersion(jsobj);
								if (i == 0) {
									throw new RuntimeException("更新PosDataVersion表数据失败" + jsobj);
								}

							}

						} else {
							responseJson.put("msg", "注册码 不存在");
							responseJson.put("code", "-99");
						}

					}
				}

			}
		}

		try {
			out = response.getWriter();
			out.print(responseJson.toString());
			out.flush();
			logger.info(responseJson.toString());
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} finally {
			out.close();
			if (b) {
				Thread t = new Thread(new Runnable() {
					@Override
					public void run() {
						synchronized (queen) {
							queen.notifyAll();
						}

					}
				});
				t.start();
			}
		}

	}

	/**
	 * 查询kvs类别
	 * 
	 * @param request
	 * @param response
	 * @param jsobj
	 * @throws Exception
	 */
	@RequestMapping(value = "/getHqKvs", method = RequestMethod.POST)
	public void getKvs(HttpServletRequest request, HttpServletResponse response, @RequestBody JSONObject jsobj)
			throws Exception {
		logger.info("getHqKvs 传入参数：" + jsobj.toString());
		response.setContentType("text/html; charset=UTF-8");
		PrintWriter out = null;
		JSONObject responseJson = new JSONObject();
		responseJson = checkInParam(jsobj, InParam.getHqKvs);
		if (responseJson.getBoolean("_if")) {
			List<JSONObject> list = registerService.getKvs(jsobj);

			responseJson.put("msg", "成功");
			responseJson.put("code", "0");
			responseJson.put("data", new JSONArray().fromObject(list).toArray());
		}

		try {
			out = response.getWriter();
			out.print(responseJson.toString());
			out.flush();

		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} finally {
			out.close();
		}

	}

	/**
	 * 查询显示方式
	 * 
	 * @throws Exception
	 */

	@RequestMapping(value = "/getSysDictionary", method = RequestMethod.POST)
	public void getShowMode(HttpServletRequest request, HttpServletResponse response, @RequestBody JSONObject jsobj)
			throws Exception {
		logger.info("getSysDictionary 传入参数：" + jsobj.toString());
		response.setContentType("text/html; charset=UTF-8");
		PrintWriter out = null;
		JSONObject responseJson = new JSONObject();
		List<JSONObject> list = registerService.getSysDictionary(jsobj);

		responseJson.put("msg", "成功");
		responseJson.put("code", "0");
		responseJson.put("data", new JSONArray().fromObject(list).toArray());
		try {
			out = response.getWriter();
			out.print(responseJson.toString());
			out.flush();

		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} finally {
			out.close();
		}

	}

	/**
	 * 保存设备信息
	 * 
	 * @param jsonParam
	 * @return
	 * @throws Exception
	 */
	public boolean saveDevices(JSONObject jsonParam) throws Exception {

		boolean b = registerService.saveDevices(jsonParam);

		return b;

	}

	/**
	 * 更改注册基本信息
	 * 
	 * @throws Exception
	 */
	@RequestMapping(value = "/updateRegister", method = RequestMethod.POST)
	public void updateRegister(HttpServletRequest request, HttpServletResponse response, @RequestBody JSONObject jsobj)
			throws Exception {
		logger.info("updateRegister 传入参数：" + jsobj.toString());
		response.setContentType("text/html; charset=UTF-8");
		PrintWriter out = null;
		JSONObject responseJson = new JSONObject();
		Integer i = 0;
		responseJson = checkInParam(jsobj, InParam.updateRegister);
		if (responseJson.getBoolean("_if")) {
			List<String> whereList = new ArrayList<String>();
			whereList.add("devices_code");

			i = registerService.updateRegister(jsobj, whereList, whereList);

			if (i > 0) {
				responseJson.put("msg", "成功");
				responseJson.put("code", "0");
				responseJson.put("data", jsobj);
			} else {
				throw new RuntimeException("更新注册信息失败" + jsobj);
			}

		}

		try {
			out = response.getWriter();
			out.print(responseJson.toString());
			out.flush();

		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} finally {
			out.close();
			if (i > 0) {
				Thread t = new Thread(new Runnable() {
					@Override
					public void run() {
						synchronized (queen) {
							queen.notifyAll();
						}

					}
				});
				t.start();
			}
		}

	}

	/**
	 * 组装data数据
	 * 
	 * @param list
	 * @return
	 */
	public JSONObject getData(List<List<JSONObject>> list) {
		JSONObject data = new JSONObject();
		for (int i = 0; i < list.size(); i++) {
			if (list.get(i).size() > 0) {
				for (Iterator<String> it = list.get(i).get(0).keySet().iterator(); it.hasNext();) {
					String key = it.next();
					String value = list.get(i).get(0).getString(key);
					data.put(key, value);
				}
			}

		}
		return data;
	}

	/**
	 * 参数校验
	 */
	public JSONObject checkInParam(JSONObject jsobj, InParam inParam) {

		JSONObject responseJson = new JSONObject();
		responseJson.put("_if", true);

		Map<String, Object> inParams = inParam.getParam();
		for (Iterator<String> it = inParams.keySet().iterator(); it.hasNext();) {
			String key = it.next();
			Object value = inParams.get(key);
			if (!jsobj.has(key)) {
				responseJson.put("msg", "缺少参数：" + key);
				responseJson.put("code", "-99");
				responseJson.put("_if", false);
				break;
			} else if (value.equals(false) && jsobj.getString(key).trim().equals("")) {

				responseJson.put("msg", "参数：" + key + "不能为空值");
				responseJson.put("code", "-99");
				responseJson.put("_if", false);
				break;
			}
		}
		return responseJson;
	}

}
