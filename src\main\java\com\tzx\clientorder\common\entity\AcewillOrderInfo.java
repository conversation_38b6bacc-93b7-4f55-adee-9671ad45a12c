package com.tzx.clientorder.common.entity;

import java.util.List;

public class AcewillOrderInfo {

	private String tableno;
	//订单备注
	private AcewillOrderMemo ordermemo;
	private AcewillOrderMember member;
	private AcewillOrderUpGrade upgrade;
	//应收
	private Double total;
	//实收
	private Double cost;
	private List<AcewillOrderSetmeal> setmeal;
	private List<AcewillOrderNormalitem> normalitems;
	//人数
	private String people;
	//服务费
	private String mealfee;
	//订单模式
	private Integer ordermode;
	//下单人Id
	private String openid;
	//下单人昵称
	private String name;
	
	public String getTableno() {
		return tableno;
	}
	public void setTableno(String tableno) {
		this.tableno = tableno;
	}
	
	public AcewillOrderMemo getOrdermemo() {
		return ordermemo;
	}
	public void setOrdermemo(AcewillOrderMemo ordermemo) {
		this.ordermemo = ordermemo;
	}
	public AcewillOrderUpGrade getUpgrade() {
		return upgrade;
	}
	public void setUpgrade(AcewillOrderUpGrade upgrade) {
		this.upgrade = upgrade;
	}
	public AcewillOrderMember getMember() {
		return member;
	}
	public void setMember(AcewillOrderMember member) {
		this.member = member;
	}
	public Double getTotal() {
		return total;
	}
	public void setTotal(Double total) {
		this.total = total;
	}
	public Double getCost() {
		return cost;
	}
	public void setCost(Double cost) {
		this.cost = cost;
	}
	public List<AcewillOrderSetmeal> getSetmeal() {
		return setmeal;
	}
	public void setSetmeal(List<AcewillOrderSetmeal> setmeal) {
		this.setmeal = setmeal;
	}
	public List<AcewillOrderNormalitem> getNormalitems() {
		return normalitems;
	}
	public void setNormalitems(List<AcewillOrderNormalitem> normalitems) {
		this.normalitems = normalitems;
	}
	public String getPeople() {
		return people;
	}
	public void setPeople(String people) {
		this.people = people;
	}
	public String getMealfee() {
		return mealfee;
	}
	public void setMealfee(String mealfee) {
		this.mealfee = mealfee;
	}
	public Integer getOrdermode() {
		return ordermode;
	}
	public void setOrdermode(Integer ordermode) {
		this.ordermode = ordermode;
	}
	public String getOpenid() {
		return openid;
	}
	public void setOpenid(String openid) {
		this.openid = openid;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	
}
