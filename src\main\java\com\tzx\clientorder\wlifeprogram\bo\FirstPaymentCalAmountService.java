package com.tzx.clientorder.wlifeprogram.bo;

import com.tzx.framework.common.entity.Data;
import net.sf.json.JSONObject;

import java.util.List;

/**
 * Created by zds on 2018-08-16.
 */
public interface FirstPaymentCalAmountService {

    String NAME = "com.tzx.clientorder.wlifeprogram.bo.impl.FirstPaymentCalAmountServiceImpl";

    void calcAmount(String tenantId, Integer storeId, String billNum, Integer dishScale, Integer billScale) throws Exception;

    Data calBillDiscount(Data param) throws Exception;

    List<JSONObject> calCustomerVipPriceByItem(String tenancyId, int storeId, String billNum, int discountMode, List<Integer> rwidList) throws Exception;
}
