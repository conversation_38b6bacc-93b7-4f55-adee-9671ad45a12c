package com.tzx.pos.service.servlet;

import java.util.List;

import net.sf.json.JSONObject;

import com.tzx.framework.common.entity.Data;

public interface PosDishServlet
{
	String	NAME	= "com.tzx.pos.service.servlet.imp.PosDishServletImp";
	
	/**
	 * @param param
	 * @param json
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> newestOpenTable(Data param, JSONObject json) throws Exception;

	/**  开台并绑定会员
	 * @param param
	 * @param result
	 * @param json
	 * @return
	 * @throws Exception
	 */
	public void  optableAndCustomerCreditPlus(Data param, Data result, List<JSONObject> listJson) ;

	/**
	 *开通时验证桌台是否有并台
	 */
	public boolean checkCombineTableOpenTable(Data param)throws Exception;
}
