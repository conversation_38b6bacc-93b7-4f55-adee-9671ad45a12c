package com.tzx.framework.common.print;

import java.util.HashMap;

/**
 * java.awt.Font size2d 范围8 - 79 对应打印高度
 * 
 * <AUTHOR>
 * 
 */
public class FontHeightUtil
{
	private static HashMap<Float, Integer>	fontHeightCache;

	static
	{
		fontHeightCache = new HashMap<Float, Integer>();
	}

	public static HashMap<Float, Integer> getFontHeightCache()
	{
		return fontHeightCache;
	}

	public static Integer getfontHeights(Float key)
	{
		Integer height = fontHeightCache.get(key);
		if (height == null)
		{
			height = key.intValue();
		}
		return height;
	}

	public static boolean isInit()
	{
		return fontHeightCache.values().size() > 0;
	}
}
