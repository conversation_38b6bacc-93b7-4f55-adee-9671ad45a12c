package com.tzx.estate.common.entity;

import java.util.List;

import com.tzx.framework.common.util.JavaMd5;
import com.tzx.estate.base.constant.Oper;
import com.tzx.estate.base.constant.Type;

public class Data implements Cloneable
{
	private Long		t;
	private String		secret;
	private String		msg;
	private List<?>		data;

	private int			code;
	private Oper		oper;
	private Type		type;
	private String		source;
	private boolean		success;
	private Pagination	pagination;

	private String		tenancy_id;
	private int			store_id;

	@Override
	public Data clone()
	{
		try
		{
			return (Data) super.clone();
		}
		catch (CloneNotSupportedException e)
		{
		}
		
		return new Data();
	}

	public static Data get()
	{
		Data d = new Data();
		d.setT(System.currentTimeMillis());
		d.setSuccess(true);
		d.setCode(0);
		d.setSource("SERVER");
		d.setPagination(new Pagination());
		return d;
	}

	public static Data get(Integer code)
	{
		Data d = new Data();
		d.setT(System.currentTimeMillis());
		d.setSuccess(code == 0);
		d.setCode(code);
		d.setSource("SERVER");
		d.setPagination(new Pagination());
		return d;
	}

	public static Data get(String tenancyId,Integer storeId,Integer code)
	{
		Data d = new Data();
		long t = System.currentTimeMillis();
		String secret = JavaMd5.toMd5B32(t + "BASIC" + tenancyId);
		d.setT(t);
		d.setSecret(secret);
		d.setSource("SERVER");
		d.setCode(code);
		d.setSuccess(code == 0);
		d.setPagination(new Pagination());
		d.setTenancy_id(tenancyId);
		d.setStore_id(storeId);
		return d;
	}
	
	public static Data get(Data data)
	{
		Data d = new Data();
		d.setT(data.getT());
		d.setSuccess(data.isSuccess());
		d.setCode(0);
		d.setSecret(data.getSecret());
		d.setType(data.getType());
		d.setOper(data.getOper());
		d.setSource(data.getSource());
		d.setPagination(new Pagination());
		d.setTenancy_id(data.getTenancy_id());
		d.setStore_id(data.getStore_id());
		return d;
	}

	public Long getT()
	{
		return t;
	}

	public void setT(Long t)
	{
		this.t = t;
	}

	public String getSecret()
	{
		return secret;
	}

	public void setSecret(String secret)
	{
		this.secret = secret;
	}

	public String getMsg()
	{
		return msg;
	}

	public void setMsg(String msg)
	{
		this.msg = msg;
	}

	public List<?> getData()
	{
		return data;
	}

	public void setData(List<?> data)
	{
		this.data = data;
	}

	public int getCode()
	{
		return code;
	}

	public void setCode(int code)
	{
		this.code = code;
	}

	public Oper getOper()
	{
		return oper;
	}

	public void setOper(Oper oper)
	{
		this.oper = oper;
	}

	public Type getType()
	{
		return type;
	}

	public void setType(Type type)
	{
		this.type = type;
	}

	public String getSource()
	{
		return source;
	}

	public void setSource(String source)
	{
		this.source = source;
	}

	public boolean isSuccess()
	{
		return success;
	}

	public void setSuccess(boolean success)
	{
		this.success = success;
	}

	public Pagination getPagination()
	{
		return pagination;
	}

	public void setPagination(Pagination pagination)
	{
		this.pagination = pagination;
	}

	public String getTenancy_id()
	{
		return tenancy_id;
	}

	public void setTenancy_id(String tenancy_id)
	{
		this.tenancy_id = tenancy_id;
	}

	public int getStore_id()
	{
		return store_id;
	}

	public void setStore_id(int store_id)
	{
		this.store_id = store_id;
	}
}
