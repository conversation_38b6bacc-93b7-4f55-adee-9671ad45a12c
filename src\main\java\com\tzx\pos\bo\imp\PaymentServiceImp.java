package com.tzx.pos.bo.imp;

import com.alibaba.fastjson.JSON;
import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.*;
import com.tzx.framework.common.util.*;
import com.tzx.member.crm.bo.CustomerService;
import com.tzx.orders.bo.OrdersSyncService;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.base.util.PathUtil;
import com.tzx.pos.base.util.*;
import com.tzx.pos.bo.PaymentService;
import com.tzx.pos.bo.PosPrintService;
import com.tzx.pos.po.springjdbc.dao.PosPaymentDao;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.File;
import java.sql.Timestamp;
import java.util.*;

@Service(PaymentService.NAME)
public class PaymentServiceImp extends PosBaseServiceImp implements PaymentService
{
	private static final Logger	logger			= Logger.getLogger(PaymentServiceImp.class);

	@Resource(name = CustomerService.NAME)
	private CustomerService		customerService;

	@Resource(name = PosPrintService.NAME)
	private PosPrintService		posPrintService;
	
	@Resource(name = OrdersSyncService.NAME)
	private OrdersSyncService	orderSyncService;

	@Autowired
	private PosPaymentDao			posDishDao;

	private final int			PAYMENT_SCALE	= 2;
	private final int			DEFAULT_SCALE	= 4;

    private final List<String> moreCoupouPaymentWays = Arrays.asList(
            SysDictionary.PAYMENT_CLASS_COUPONS,
            SysDictionary.PAYMENT_CLASS_DOUYIN_COUPONS_PAY,
            SysDictionary.PAYMENT_CLASS_DOUYIN_COUPONS_PHANTOM_PAY);

	@Override
	public List<JSONObject> precreate(Data data, String path) throws Exception
	{
		List<JSONObject> response = new ArrayList<JSONObject>();
		@SuppressWarnings("unchecked")
		List<JSONObject> list = (List<JSONObject>) data.getData();
		JSONObject param = JSONObject.fromObject(list.get(0));
		// 验证查询参数是否存在
		if (!param.containsKey("pos_num") || !param.containsKey("opt_num") || !param.containsKey("report_date") || !param.containsKey("shift_id") || !param.containsKey("jzid"))
		{
			JSONObject json = getErrorReturn(data.getTenancy_id(), data.getStore_id(), param.getString("bill_num"));
			json.put("error_msg", "参数不全，请参照对接文档");
			response.add(json);
			return response;
		}
		// 先进行查询接口，如果已经支付成功、则进行订单的关闭
		param.put("polling_flag", false);// 设置不进行轮询
		list.remove(0);
		list.add(param);
		data.setData(list);
//		data.setOper(Oper.query);
//		List<JSONObject> queryResult = this.query(data, path);
//		// 获取状态异常，直接返回错误信息，不再进行二维码生成
//		if (queryResult != null)
//		{
//			// 如果订单状态
//			JSONObject queryReturn = queryResult.get(0);
//			if (queryReturn.getBoolean("success") && "finished".equals(queryReturn.getString("status")))
//			{
//				logger.info("precreate:已经支付成功 \n " + JSON.toJSONString(data));
//			}
//			else if (!queryReturn.getBoolean("success") && queryReturn.containsKey("exception"))
//			{
//				JSONObject json = getErrorReturn(data.getTenancy_id(), data.getStore_id(), param.getString("bill_num"));
//				response.add(json);
//				return response;
//			}
//		}
//		else
//		{
//			JSONObject json = getErrorReturn(data.getTenancy_id(), data.getStore_id(), param.getString("bill_num"));
//			response.add(json);
//			return response;
//		}
		data.setOper(Oper.scan);
		// 没有订单或订单支付未成功，进行预下单
		Map<String, Object> params = new HashMap<String, Object>();
		params.put("oper", data.getOper());
		params.put("tenantId", data.getTenancy_id());
		params.put("store_id", data.getStore_id());
		params.put("bill_num", param.getString("bill_num"));
		// 处理金额，有效值为两位数
		String total_amount = String.format("%.2f", param.optDouble("total_amount"));
		params.put("total_amount", total_amount);
		params.put("subject", param.getString("subject"));
		String paramStr = JSON.toJSONString(params);
		String reqURL = com.tzx.framework.common.constant.Constant.systemMap.get("pos_payment_url");
		String result = HttpUtil.sendPostRequest(reqURL, paramStr);
		// 处理结果
		if (result != null)
		{
			try
			{
				com.alibaba.fastjson.JSONObject json = com.alibaba.fastjson.JSONObject.parseObject(result);
				boolean success = json.getBooleanValue("success");
				if (success)
				{
					String status = json.getString("status");
					if ("finished".equals(status))
					{
						// 获取支付二维码url，生成二维码
						String qr_code = json.getString("qr_code");
						// 存储的物理地址
						String physicalPath = PathUtil.getWebRootPath() + File.separator + "img" + File.separator + "qr_code" + File.separator;
						String fileName = data.getTenancy_id() + "_" + data.getStore_id() + "_" + param.getString("bill_num") + ".png";
						String imgPath = physicalPath + fileName;// 图片存储地址
						String logoPath = physicalPath + Constant.LOGO_ALIPAY;
						// 普通二维码
						// int width = 300, height = 300;
						// QRCode.encode(qr_code, width, height, imgPath);
						// 生成带logo的二维码
						QrCodeUtils.encode(qr_code, logoPath, imgPath, true);
						// 向返回客户端的结果中添加二维码地址
						json.put("qr_code_url", path + "img/qr_code/" + fileName);
					}
				}
				response.add(JSONObject.fromObject(json));
				return response;
			}
			catch (Exception e)
			{
				logger.error(e.getMessage());
				JSONObject json = getErrorReturn(data.getTenancy_id(), data.getStore_id(), param.getString("bill_num"));
				response.add(json);
			}
		}
		else
		{
			JSONObject json = getErrorReturn(data.getTenancy_id(), data.getStore_id(), param.getString("bill_num"));
			response.add(json);
		}
		return response;
	}

	@Override
	@Transactional
	public List<JSONObject> query(Data data, String path) throws Exception
	{
		@SuppressWarnings("unchecked")
		List<JSONObject> list = (List<JSONObject>) data.getData();
		JSONObject param = JSONObject.fromObject(list.get(0));
		List<JSONObject> response = new ArrayList<JSONObject>();
		// 检验6个参数不为空
		if (param == null || param.size() < 6)
		{
			JSONObject errorJson = new JSONObject();
			errorJson.put("success", false);
			errorJson.put("error_msg", "参数不全，请参照文档");
			response.add(errorJson);
			return response;
		}
		Map<String, Object> params = new HashMap<String, Object>();
		params.put("oper", data.getOper());
		params.put("tenantId", data.getTenancy_id());
		params.put("store_id", data.getStore_id());
		params.put("bill_num", param.getString("bill_num"));
		// 是否进行轮询查询的字段
		Map<String, Object> map = new HashMap<String, Object>();
		if (param.containsKey("polling_flag"))
		{
			map.put("polling_flag", param.getBoolean("polling_flag"));
		}
		else
		{
			map.put("polling_flag", true);
		}

		// 获取轮查的时间间隔和次数
		int queryPeriod = 5;
		int queryTime = 20;
		if (param.containsKey("query_period") && param.containsKey("query_time"))
		{
			queryPeriod = param.optInt("query_period");
			queryTime = param.optInt("query_time");
		}
		else
		{
			String sql = "select para_value from sys_parameter where para_code in ('dsffkztjcjgsj','dsffkztlxcs') order by para_code";
			List<JSONObject> gapList = posDishDao.query4Json(data.getTenancy_id(), sql);
			if (gapList != null && gapList.size() == 2)
			{
				queryPeriod = gapList.get(0).getInt("para_value");
				queryTime = gapList.get(1).getInt("para_value");
			}
		}
		map.put("query_period", queryPeriod);
		map.put("query_time", queryTime);
		params.put("json", map);

		String paramStr = JSON.toJSONString(params);
		String reqURL = com.tzx.framework.common.constant.Constant.systemMap.get("pos_payment_url");

		// 超时时间设置(在其轮询时间的基础上添加100s)
		int timeout = (queryPeriod * queryTime + 100) * 1000;
		String result = HttpUtil.sendPostRequest(reqURL, paramStr, timeout, timeout);
		if (result != null)
		{
			try
			{
				com.alibaba.fastjson.JSONObject json = com.alibaba.fastjson.JSONObject.parseObject(result);
				boolean success = json.getBooleanValue("success");
				if (success)
				{
					String status = json.getString("status");
					if ("finished".equals(status))
					{
						// 请求完成、删除订单二维码图片
						String imgPath = PathUtil.getWebRootPath() + File.separator + "img" + File.separator + "qr_code" + File.separator;
						String fileName = data.getTenancy_id() + "_" + data.getStore_id() + "_" + param.getString("bill_num") + ".png";
						imgPath += fileName;
						PathUtil.deleteFile(imgPath);
						// 结账完成订单
						// this.finishedCloseOrder(data,json);
					}
				}
				response.add(JSONObject.fromObject(json));
				return response;
			}
			catch (Exception e)
			{
				logger.error(e.getMessage());
				JSONObject json = getErrorReturn(data.getTenancy_id(), data.getStore_id(), param.getString("bill_num"));
				json.put("exception", e.getMessage());
				response.add(json);
			}
		}
		else
		{
			JSONObject json = getErrorReturn(data.getTenancy_id(), data.getStore_id(), param.getString("bill_num"));
			json.put("exception", "查询接口返回结果为null");
			response.add(json);
		}
		return response;
	}

	@Override
	public List<JSONObject> cancle(Data data, String path) throws Exception
	{
		@SuppressWarnings("unchecked")
		List<JSONObject> list = (List<JSONObject>) data.getData();
		JSONObject param = JSONObject.fromObject(list.get(0));
		List<JSONObject> response = new ArrayList<JSONObject>();
		Map<String, Object> params = new HashMap<String, Object>();
		params.put("oper", data.getOper());
		params.put("tenantId", data.getTenancy_id());
		params.put("store_id", data.getStore_id());
		params.put("bill_num", param.getString("bill_num"));
		String paramStr = JSON.toJSONString(params);
		String reqURL = com.tzx.framework.common.constant.Constant.systemMap.get("pos_payment_url");
		String result = HttpUtil.sendPostRequest(reqURL, paramStr);
		if (result != null)
		{
			try
			{
				com.alibaba.fastjson.JSONObject json = com.alibaba.fastjson.JSONObject.parseObject(result);
				boolean success = json.getBooleanValue("success");
				if (success)
				{
					String status = json.getString("status");
					if ("finished".equals(status))
					{
						// 请求完成、删除订单二维码图片
						String imgPath = PathUtil.getWebRootPath() + File.separator + "img" + File.separator + "qr_code" + File.separator;
						String fileName = data.getTenancy_id() + "_" + data.getStore_id() + "_" + param.getString("bill_num") + ".png";
						imgPath += fileName;
						PathUtil.deleteFile(imgPath);
					}
				}
				response.add(JSONObject.fromObject(json));
				return response;
			}
			catch (Exception e)
			{
				logger.error(e.getMessage());
				JSONObject json = getErrorReturn(data.getTenancy_id(), data.getStore_id(), param.getString("bill_num"));
				response.add(json);
			}
		}
		else
		{
			JSONObject json = getErrorReturn(data.getTenancy_id(), data.getStore_id(), param.getString("bill_num"));
			response.add(json);
		}
		return response;
	}

	@Override
	public List<JSONObject> unifiedorder(Data data, String path) throws Exception
	{
		List<JSONObject> response = new ArrayList<JSONObject>();
		@SuppressWarnings("unchecked")
		List<JSONObject> list = (List<JSONObject>) data.getData();
		JSONObject param = JSONObject.fromObject(list.get(0));
		// 验证查询参数是否存在
		if (!param.containsKey("pos_num") || !param.containsKey("opt_num") || !param.containsKey("report_date") || !param.containsKey("shift_id") || !param.containsKey("jzid"))
		{
			JSONObject json = getErrorReturn(data.getTenancy_id(), data.getStore_id(), param.getString("bill_num"));
			json.put("error_msg", "参数不全，请参照对接文档");
			response.add(json);
			return response;
		}
		// 先进行查询接口，如果已经支付成功、则进行订单的关闭
		param.put("polling_flag", false);// 设置不进行轮询
		list.remove(0);
		list.add(param);
		data.setData(list);
//		data.setOper(Oper.query);
//		List<JSONObject> queryResult = this.orderquery(data, path);
//		if (queryResult != null)
//		{
//			// 如果订单状态
//			JSONObject queryReturn = queryResult.get(0);
//			if (queryReturn.getBoolean("success") && "finished".equals(queryReturn.getString("status")))
//			{
//				logger.info("unifiedorder:已经支付成功 \n " + JSON.toJSONString(data));
//			}
//			else if (!queryReturn.getBoolean("success") && queryReturn.containsKey("exception"))
//			{
//				JSONObject json = getErrorReturn(data.getTenancy_id(), data.getStore_id(), param.getString("bill_num"));
//				response.add(json);
//				return response;
//			}
//			else if (queryReturn.getBoolean("success") && "notpay".equals(queryReturn.getString("status")))
//			{
//				// 因为微信订单号唯一没法进行修改金额后再次下单，所以对这种情况进行了再次生成订单、在重新订单生成之前
//				// 将原本未支付的订单号取消掉
//				// data.setOper(Oper.cancle);
//				// List<JSONObject> cancleReturn = this.closeorder(data, path);
//				// if(cancleReturn!=null){
//				// JSONObject json = cancleReturn.get(0);
//				// if(json.getBoolean("success") &&
//				// "finished".equals(json.getString("status"))){
//				// logger.info("unifiedorder:因为订单未支付，重新生成订单后取消订单 \n "+JSON.toJSONString(data));
//				// }else{
//				// logger.error("unifiedorder:关闭订单失败 出现异常 \n "+JSON.toJSONString(data));
//				// }
//				// }else{
//				// logger.error("unifiedorder:关闭订单失败 出现异常 \n "+JSON.toJSONString(data));
//				// }
//			}
//		}
//		else
//		{
//			JSONObject json = getErrorReturn(data.getTenancy_id(), data.getStore_id(), param.getString("bill_num"));
//			response.add(json);
//			return response;
//		}
		data.setOper(Oper.init);
		String paramStr = JSON.toJSONString(data);
		String reqURL = com.tzx.framework.common.constant.Constant.systemMap.get("pos_payment_wechat_url");
		String result = HttpUtil.sendPostRequest(reqURL, paramStr);
		// 处理结果
		if (result != null)
		{
			try
			{
				com.alibaba.fastjson.JSONObject json = com.alibaba.fastjson.JSONObject.parseObject(result);
				boolean success = json.getBooleanValue("success");
				if (success)
				{
					String status = json.getString("status");
					if ("finished".equals(status))
					{
						// 交易类型
						if (!param.containsKey("trade_type") || "NATIVE".equals(param.getString("trade_type")) || param.getString("trade_type") == null)
						{
							// 获取支付二维码url，生成二维码
							String qr_code = json.getString("qr_code");
							// 存储的物理地址
							String physicalPath = PathUtil.getWebRootPath() + File.separator + "img" + File.separator + "qr_code" + File.separator + "wechat" + File.separator;
							String fileName = data.getTenancy_id() + "_" + data.getStore_id() + "_" + param.getString("bill_num") + ".png";
							String imgPath = physicalPath + fileName;// 图片存储地址
							String logoPath = physicalPath + Constant.LOGO_WECHAT;
							// 普通二维码
							// int width = 300, height = 300;
							// QRCode.encode(qr_code, width, height, imgPath);
							// 生成带logo的二维码
							QrCodeUtils.encode(qr_code, logoPath, imgPath, true);
							// 向返回客户端的结果中添加二维码地址
							json.put("qr_code_url", path + "img/qr_code/wechat/" + fileName);
						}
					}
				}
				response.add(JSONObject.fromObject(json));
				return response;
			}
			catch (Exception e)
			{
				logger.error(e.getMessage());
				JSONObject json = getErrorReturn(data.getTenancy_id(), data.getStore_id(), param.getString("bill_num"));
				response.add(json);
			}
		}
		else
		{
			JSONObject json = getErrorReturn(data.getTenancy_id(), data.getStore_id(), param.getString("bill_num"));
			response.add(json);
		}
		return response;
	}

	@Override
	@Transactional
	public List<JSONObject> orderquery(Data data, String path) throws Exception
	{
		@SuppressWarnings("unchecked")
		List<JSONObject> list = (List<JSONObject>) data.getData();
		JSONObject param = JSONObject.fromObject(list.get(0));
		List<JSONObject> response = new ArrayList<JSONObject>();

		// 检验6个参数不为空
		if (param == null || param.size() < 6)
		{
			JSONObject errorJson = new JSONObject();
			errorJson.put("success", false);
			errorJson.put("error_msg", "参数不全，请参照文档");
			response.add(errorJson);
			return response;
		}
		// 获取轮查的时间间隔和次数
		int queryPeriod = 5;
		int queryTime = 20;
		if (param.containsKey("query_period") && param.containsKey("query_time"))
		{
			queryPeriod = param.optInt("query_period");
			queryTime = param.optInt("query_time");
			param.put("query_period", queryPeriod);
			param.put("query_time", queryTime);
		}
		else
		{
			String sql = "select para_value from sys_parameter where para_code in ('dsffkztjcjgsj','dsffkztlxcs') order by para_code";
			List<JSONObject> gapList = posDishDao.query4Json(data.getTenancy_id(), sql);
			if (gapList != null && gapList.size() == 2)
			{
				queryPeriod = gapList.get(0).getInt("para_value");
				queryTime = gapList.get(1).getInt("para_value");
			}
		}
		list.remove(0);
		list.add(param);
		data.setData(list);

		String paramStr = JSON.toJSONString(data);
		String reqURL = com.tzx.framework.common.constant.Constant.systemMap.get("pos_payment_wechat_url");

		// 超时时间设置(在其轮询时间的基础上添加100s)
		int timeout = (queryPeriod * queryTime + 100) * 1000;
		String result = HttpUtil.sendPostRequest(reqURL, paramStr, timeout, timeout);

		if (result != null)
		{
			try
			{
				com.alibaba.fastjson.JSONObject json = com.alibaba.fastjson.JSONObject.parseObject(result);
				boolean success = json.getBooleanValue("success");
				if (success)
				{
					String status = json.getString("status");
					if ("finished".equals(status))
					{
						// 请求完成、删除订单二维码图片
						String imgPath = PathUtil.getWebRootPath() + File.separator + "img" + File.separator + "qr_code" + File.separator + "wechat" + File.separator;
						String fileName = data.getTenancy_id() + "_" + data.getStore_id() + "_" + param.getString("bill_num") + ".png";
						imgPath += fileName;
						PathUtil.deleteFile(imgPath);
						// 完成订单后，关闭订单结账
						// this.finishedCloseOrder(data, json);
					}
				}
				response.add(JSONObject.fromObject(json));
				return response;
			}
			catch (Exception e)
			{
				logger.error(e.getMessage());
				JSONObject json = getErrorReturn(data.getTenancy_id(), data.getStore_id(), param.getString("bill_num"));
				json.put("exception", e.getMessage());
				response.add(json);
			}
		}
		else
		{
			JSONObject json = getErrorReturn(data.getTenancy_id(), data.getStore_id(), param.getString("bill_num"));
			json.put("exception", "查询接口返回结果为null");
			response.add(json);
		}
		return response;
	}

	@Override
	public List<JSONObject> refund(Data data, String path) throws Exception
	{
		@SuppressWarnings("unchecked")
		List<JSONObject> list = (List<JSONObject>) data.getData();
		JSONObject param = JSONObject.fromObject(list.get(0));
		List<JSONObject> response = new ArrayList<JSONObject>();
		Map<String, Object> params = new HashMap<String, Object>();
		params.put("oper", data.getOper());
		params.put("tenantId", data.getTenancy_id());
		params.put("store_id", data.getStore_id());
		params.put("bill_num", param.getString("bill_num"));
		params.put("refund_amount", param.getString("refund_amount"));
		String paramStr = JSON.toJSONString(params);
		String reqURL = com.tzx.framework.common.constant.Constant.systemMap.get("pos_payment_url");
		String result = HttpUtil.sendPostRequest(reqURL, paramStr);
		if (result != null)
		{
			try
			{
				com.alibaba.fastjson.JSONObject json = com.alibaba.fastjson.JSONObject.parseObject(result);
				boolean success = json.getBooleanValue("success");
				if (success)
				{
					String status = json.getString("status");
					if ("finished".equals(status))
					{
						// 请求完成、删除订单二维码图片
						String imgPath = PathUtil.getWebRootPath() + File.separator + "img" + File.separator + "qr_code" + File.separator;
						String fileName = data.getTenancy_id() + "_" + data.getStore_id() + "_" + param.getString("bill_num") + ".png";
						imgPath += fileName;
						PathUtil.deleteFile(imgPath);
					}
				}
				response.add(JSONObject.fromObject(json));
				return response;
			}
			catch (Exception e)
			{
				logger.error(e.getMessage());
				JSONObject json = getErrorReturn(data.getTenancy_id(), data.getStore_id(), param.getString("bill_num"));
				response.add(json);
			}
		}
		else
		{
			JSONObject json = getErrorReturn(data.getTenancy_id(), data.getStore_id(), param.getString("bill_num"));
			response.add(json);
		}
		return response;
	}

	@Override
	public List<JSONObject> closeorder(Data data, String path) throws Exception
	{
		@SuppressWarnings("unchecked")
		List<JSONObject> list = (List<JSONObject>) data.getData();
		JSONObject param = JSONObject.fromObject(list.get(0));
		List<JSONObject> response = new ArrayList<JSONObject>();

		String paramStr = JSON.toJSONString(data);
		String reqURL = com.tzx.framework.common.constant.Constant.systemMap.get("pos_payment_wechat_url");
		String result = HttpUtil.sendPostRequest(reqURL, paramStr);
		if (result != null)
		{
			try
			{
				com.alibaba.fastjson.JSONObject json = com.alibaba.fastjson.JSONObject.parseObject(result);
				boolean success = json.getBooleanValue("success");
				if (success)
				{
					String status = json.getString("status");
					if ("finished".equals(status))
					{
						// 请求完成、删除订单二维码图片
						String imgPath = PathUtil.getWebRootPath() + File.separator + "img" + File.separator + "qr_code" + File.separator + "wechat" + File.separator;
						String fileName = data.getTenancy_id() + "_" + data.getStore_id() + "_" + param.getString("bill_num") + ".png";
						imgPath += fileName;
						PathUtil.deleteFile(imgPath);
					}
				}
				response.add(JSONObject.fromObject(json));
				return response;
			}
			catch (Exception e)
			{
				logger.error(e.getMessage());
				JSONObject json = getErrorReturn(data.getTenancy_id(), data.getStore_id(), param.getString("bill_num"));
				response.add(json);
			}
		}
		else
		{
			JSONObject json = getErrorReturn(data.getTenancy_id(), data.getStore_id(), param.getString("bill_num"));
			response.add(json);
		}
		return response;
	}

	@Override
	public List<JSONObject> refundorder(Data data, String path) throws Exception
	{
		@SuppressWarnings("unchecked")
		List<JSONObject> list = (List<JSONObject>) data.getData();
		JSONObject param = JSONObject.fromObject(list.get(0));
		List<JSONObject> response = new ArrayList<JSONObject>();

		String paramStr = JSON.toJSONString(data);
		String reqURL = com.tzx.framework.common.constant.Constant.systemMap.get("pos_payment_wechat_url");
		String result = HttpUtil.sendPostRequest(reqURL, paramStr);
		if (result != null)
		{
			try
			{
				com.alibaba.fastjson.JSONObject json = com.alibaba.fastjson.JSONObject.parseObject(result);
				boolean success = json.getBooleanValue("success");
				if (success)
				{
					String status = json.getString("status");
					if ("finished".equals(status))
					{
						// 请求完成、删除订单二维码图片
						String imgPath = PathUtil.getWebRootPath() + File.separator + "img" + File.separator + "qr_code" + File.separator + "wechat" + File.separator;
						String fileName = data.getTenancy_id() + "_" + data.getStore_id() + "_" + param.getString("bill_num") + ".png";
						imgPath += fileName;
						PathUtil.deleteFile(imgPath);
					}
				}
				response.add(JSONObject.fromObject(json));
				return response;
			}
			catch (Exception e)
			{
				logger.error(e.getMessage());
				JSONObject json = getErrorReturn(data.getTenancy_id(), data.getStore_id(), param.getString("bill_num"));
				response.add(json);
			}
		}
		else
		{
			JSONObject json = getErrorReturn(data.getTenancy_id(), data.getStore_id(), param.getString("bill_num"));
			response.add(json);
		}
		return response;
	}

	@Override
	public List<JSONObject> refundquery(Data data, String path) throws Exception
	{
		@SuppressWarnings("unchecked")
		List<JSONObject> list = (List<JSONObject>) data.getData();
		JSONObject param = JSONObject.fromObject(list.get(0));
		List<JSONObject> response = new ArrayList<JSONObject>();

		String paramStr = JSON.toJSONString(data);
		String reqURL = com.tzx.framework.common.constant.Constant.systemMap.get("pos_payment_wechat_url");
		String result = HttpUtil.sendPostRequest(reqURL, paramStr);
		if (result != null)
		{
			try
			{
				com.alibaba.fastjson.JSONObject json = com.alibaba.fastjson.JSONObject.parseObject(result);
				boolean success = json.getBooleanValue("success");
				if (success)
				{
					String status = json.getString("status");
					if ("finished".equals(status))
					{
						// 请求完成、删除订单二维码图片
						String imgPath = PathUtil.getWebRootPath() + File.separator + "img" + File.separator + "qr_code" + File.separator + "wechat" + File.separator;
						String fileName = data.getTenancy_id() + "_" + data.getStore_id() + "_" + param.getString("bill_num") + ".png";
						imgPath += fileName;
						PathUtil.deleteFile(imgPath);
					}
				}
				response.add(JSONObject.fromObject(json));
				return response;
			}
			catch (Exception e)
			{
				logger.error(e.getMessage());
				JSONObject json = getErrorReturn(data.getTenancy_id(), data.getStore_id(), param.getString("bill_num"));
				response.add(json);
			}
		}
		else
		{
			JSONObject json = getErrorReturn(data.getTenancy_id(), data.getStore_id(), param.getString("bill_num"));
			response.add(json);
		}
		return response;
	}

	public JSONObject getErrorReturn(String tenantId, int store_id, String bill_num)
	{
		JSONObject json = new JSONObject();
		json.put("tenantId", tenantId);
		json.put("store_id", store_id);
		json.put("bill_num", bill_num);
		json.put("success", false);
		json.put("error_msg", "网络请求发生错误");
		return json;
	}

	@Override
	public List<JSONObject> barcode(Data data, String path) throws Exception
	{
		List<JSONObject> response = new ArrayList<JSONObject>();
		@SuppressWarnings("unchecked")
		List<JSONObject> list = (List<JSONObject>) data.getData();
		JSONObject param = JSONObject.fromObject(list.get(0));
		// 验证查询参数是否存在
		if (!param.containsKey("pos_num") || !param.containsKey("opt_num") || !param.containsKey("report_date") || !param.containsKey("shift_id") || !param.containsKey("jzid"))
		{
			JSONObject json = getErrorReturn(data.getTenancy_id(), data.getStore_id(), param.getString("bill_num"));
			json.put("error_msg", "参数不全，请参照对接文档");
			response.add(json);
			return response;
		}
		// 先进行查询接口，如果已经支付成功、则进行订单的关闭
		param.put("polling_flag", false);// 设置不进行轮询
		list.remove(0);
		list.add(param);
		data.setData(list);
//		data.setOper(Oper.query);
//		List<JSONObject> queryResult = this.query(data, path);
//		// 获取状态异常，直接返回错误信息，不再进行数据发送
//		if (queryResult != null)
//		{
//			// 如果订单状态
//			JSONObject queryReturn = queryResult.get(0);
//			if (queryReturn.getBoolean("success") && "finished".equals(queryReturn.getString("status")))
//			{
//				logger.info("barcode:已经支付成功 \n " + JSON.toJSONString(data));
//			}
//			else if (!queryReturn.getBoolean("success") && queryReturn.containsKey("exception"))
//			{
//				JSONObject json = getErrorReturn(data.getTenancy_id(), data.getStore_id(), param.getString("bill_num"));
//				response.add(json);
//				return response;
//			}
//		}
//		else
//		{
//			JSONObject json = getErrorReturn(data.getTenancy_id(), data.getStore_id(), param.getString("bill_num"));
//			response.add(json);
//			return response;
//		}
		data.setOper(Oper.barcode);
		// 没有订单或订单支付未成功，进行再次扫码支付
		Map<String, Object> params = new HashMap<String, Object>();
		params.put("oper", data.getOper());
		params.put("tenantId", data.getTenancy_id());
		params.put("store_id", data.getStore_id());
		params.put("bill_num", param.getString("bill_num"));
		params.put("auth_code", param.getString("auth_code"));
		// 处理金额，有效值为两位数
		String total_amount = String.format("%.2f", param.optDouble("total_amount"));
		params.put("total_amount", total_amount);
		params.put("subject", param.getString("subject"));
		String paramStr = JSON.toJSONString(params);
		String reqURL = com.tzx.framework.common.constant.Constant.systemMap.get("pos_payment_url");
		String result = HttpUtil.sendPostRequest(reqURL, paramStr);
		// 处理结果
		if (result != null)
		{
			try
			{
				com.alibaba.fastjson.JSONObject json = com.alibaba.fastjson.JSONObject.parseObject(result);
				boolean success = json.getBooleanValue("success");
				if (success)
				{
					String status = json.getString("status");
					// if("finished".equals(status))
					// {
					// //支付状态完成，进行账单结算
					// // this.finishedCloseOrder(data,json);
					// }
					// else

					if ("query".equals(status))
					{
						// 需要重新查询订单状态,设置间隔时间(建议10秒)重新查询支付结果，直到支付成功或超时(建议30秒)；
						param.put("polling_flag", true);// 设置不进行轮询
						param.put("query_period", 10);
						param.put("query_time", 3);
						list.remove(0);
						list.add(param);
						data.setData(list);
						data.setOper(Oper.query);
						List<JSONObject> queryResultSecond = this.query(data, path);
						// 获取状态异常，直接返回错误信息，不再进行数据发送
						if (queryResultSecond != null)
						{
							// 如果订单状态
							JSONObject queryReturn = queryResultSecond.get(0);
							if (queryReturn.getBoolean("success") && "finished".equals(queryReturn.getString("status")))
							{
								logger.info("barcode:已经支付成功 \n " + JSON.toJSONString(data));
								return queryResultSecond;
							}
							else if (!queryReturn.getBoolean("success"))
							{
								JSONObject jsonSecond = getErrorReturn(data.getTenancy_id(), data.getStore_id(), param.getString("bill_num"));
								response.add(jsonSecond);
								return response;
							}
						}
						else
						{
							JSONObject jsonSecond = getErrorReturn(data.getTenancy_id(), data.getStore_id(), param.getString("bill_num"));
							response.add(jsonSecond);
							return response;
						}
					}
				}
				response.add(JSONObject.fromObject(json));
				return response;
			}
			catch (Exception e)
			{
				logger.error(e.getMessage());
				JSONObject json = getErrorReturn(data.getTenancy_id(), data.getStore_id(), param.getString("bill_num"));
				response.add(json);
			}
		}
		else
		{
			JSONObject json = getErrorReturn(data.getTenancy_id(), data.getStore_id(), param.getString("bill_num"));
			response.add(json);
		}
		return response;
	}

	/**
	 * 支付完成，关闭订单
	 * 
	 * @throws Exception
	 */
	@Deprecated
	public void finishedCloseOrder(Data data, com.alibaba.fastjson.JSONObject json) throws Exception
	{
		@SuppressWarnings("unchecked")
		List<JSONObject> list = (List<JSONObject>) data.getData();
		JSONObject param = JSONObject.fromObject(list.get(0));
		Date reportDate = DateUtil.parseDate(param.getString("report_date"));
		// 查询订单表,如果关闭不在执行
		String querySql = "select * from pos_bill where bill_num = ? and store_id = ?";
		List<JSONObject> queryList = posDishDao.query4Json(data.getTenancy_id(), querySql, new Object[]
		{ param.getString("bill_num"), data.getStore_id() });
		if (queryList != null && queryList.size() > 0)
		{
			JSONObject billObject = queryList.get(0);
			// 查询支付金额与订单金额是否一致，不一致不关闭账单
			String total_amount_str = json.getString("total_amount");// 支付金额
			float total_amount = total_amount_str != null && total_amount_str.length() > 0 ? Float.parseFloat(total_amount_str) : 0;
			float payment_amount = billObject.getString("payment_amount") != null && billObject.getString("payment_amount").length() > 0 ? Float.parseFloat(billObject.getString("payment_amount")) : 0;
			if (payment_amount == total_amount)
			{
				if (!"CLOSED".equals(billObject.getString("bill_property")))
				{
					/**
					 * 查询支付交易类型
					 */
					String jzsql = "select * from payment_way pw left join payment_way_of_ogran poo on pw.id = poo.payment_id where pw.id = ?";
					List<JSONObject> jzList = posDishDao.query4Json(data.getTenancy_id(), jzsql, new Object[]
					{ param.getInt("jzid") });
					if (jzList != null && jzList.size() > 0)
					{
						JSONObject jzObject = jzList.get(0);

						/**
						 * 付款流水
						 */
						// 首先查询，没有的话再插入数据
						String billsql = "select * from pos_bill_payment where bill_num= ? and bill_code= ?";
						List<JSONObject> billList = posDishDao.query4Json(data.getTenancy_id(), billsql, new Object[]
						{ param.getString("bill_num"), json.getString("trade_no") });
						if (billList == null || billList.size() <= 0)
						{
							StringBuilder str = new StringBuilder(
									"insert into pos_bill_payment (tenancy_id,store_id,bill_num,table_code,type,jzid,name,name_english,amount,count,number,phone,report_date,shift_id,pos_num,cashier_num,last_updatetime,is_ysk,rate,currency_amount,customer_id,bill_code,remark) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
							Object[] billParam = new Object[]
							{ data.getTenancy_id(), data.getStore_id(), param.getString("bill_num"), billObject.getString("table_code"), jzObject.getString("payment_class"), param.getInt("jzid"), jzObject.getString("payment_name1"), jzObject.getString("payment_name2"),
									json.getDouble("total_amount"), 1, json.getString("buyer_logon_id"), null, reportDate, param.getInt("shift_id"), param.getString("pos_num"), param.getString("opt_num"), DateUtil.currentTimestamp(), "N", null, json.getDouble("total_amount"), null,
									json.getString("trade_no"), null };
							logger.info(JSON.toJSONString(billParam));
							posDishDao.update(str.toString(), billParam);
						}
						// 更新账单状态
						String upb = new String("update pos_bill set difference = ?, bill_property = ?,payment_time=?,cashier_num=?,pos_num=?,shift_id=? WHERE bill_num = ? and store_id = ? ");
						posDishDao.update(upb, new Object[]
						{ 0d, "CLOSED", DateUtil.currentTimestamp(), param.getString("opt_num"), param.getString("pos_num"), param.getInt("shift_id"), param.getString("bill_num"), data.getStore_id() });
						
						// 打印接口 10022代表打印模板
						posDishDao.billPrint("0", data.getTenancy_id(), data.getStore_id(), param.getString("bill_num"), param.getString("pos_num"), param.getString("opt_num"), reportDate, param.getInt("shift_id"), "1002", "0", "", "", "0", "", "","","","");
						
						// 关闭桌位
						String utInfo = new String("update pos_tablestate set state = ? where table_code = ? and store_id = ? and tenancy_id = ?");
						posDishDao.update(utInfo, new Object[]
						{ "FREE", billObject.getString("table_code"), data.getStore_id(), data.getTenancy_id() });
					}
				}
			}
		}
	}

	@Override
	public void posBillPayment(Data param, Data result, JSONObject printJson) throws Exception
	{
		String tenancyId = param.getTenancy_id();
		int storeId = param.getStore_id();
		String source = param.getSource();
		
		List<?> paramList = param.getData();
		if (paramList == null || paramList.isEmpty())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}
		JSONObject para = JSONObject.fromObject(paramList.get(0));

		String report_Date = ParamUtil.getDateStringValue(para, "report_date");
		Date reportDate = DateUtil.parseDate(report_Date);
		String billNum = para.optString("bill_num");
		int shiftId = para.optInt("shift_id");
		String posNum = para.optString("pos_num");
		String optNum = para.optString("opt_num");
		String saleMode = para.optString("sale_mode");
		String tableCode = para.optString("table_code");
		String isprint_bill = para.optString("isprint_bill");
		if (Tools.isNullOrEmpty(isprint_bill))
		{
			isprint_bill = "N";
		}

		if (Tools.isNullOrEmpty(reportDate))
		{
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_REPORT_DATE);
		}
		if (Tools.isNullOrEmpty(billNum))
		{
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_BILL_NUM);
		}
		if (Tools.isNullOrEmpty(para.opt("item")))
		{
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_ITEM_LIST);
		}

		Timestamp currentTime = DateUtil.currentTimestamp();

		if ("order_device".equals(source) || "android_pad".equals(source))
		{
			posDishDao.checkBindOptnum(tenancyId, storeId, reportDate, posNum);
			try
			{
				shiftId = posDishDao.getShiftId(tenancyId, storeId, reportDate, optNum, posNum);
			}
			catch (Exception e)
			{
				e.printStackTrace();
				throw SystemException.getInstance(PosErrorCode.GET_SHIFT_ID_ERROR);
			}
		}
		else
		{
			posDishDao.checkKssy(reportDate, optNum, storeId,shiftId,posNum);
		}

		posDishDao.checkReportDate(tenancyId, storeId, reportDate);

		StringBuilder billAmountSql = new StringBuilder();
		billAmountSql.append("select coalesce(b.bill_amount,0) as bill_amount,coalesce(b.payment_amount,0) as payment_amount,coalesce(b.discount_rate,100) as discount_rate,coalesce(b.discount_case_id,0) as discount_case_id,coalesce(b.discount_amount,0) as discount_amount,b.source,b.bill_property,b.batch_num");
		billAmountSql.append(" from pos_bill b where b.bill_num = ? and b.store_id = ?");
		SqlRowSet rs = posDishDao.query4SqlRowSet(billAmountSql.toString(), new Object[]
		{ billNum, storeId });

		double paymentAmount = 0d;
		double discountAmount = 0d;
		double discountRate = 0d;
		int discountCaseId = 0;
		String chanel = "";
		String billProperty = "";
		String batchNum="";
		if (rs.next())
		{
			paymentAmount = rs.getDouble("payment_amount");
			discountRate = rs.getDouble("discount_rate");
			discountAmount = rs.getDouble("discount_amount");
			discountCaseId = rs.getInt("discount_case_id");
			chanel = rs.getString("source");
			billProperty = rs.getString("bill_property");
			batchNum = rs.getString("batch_num");
			if (discountRate <= 0)
			{
				discountRate = 100d;
			}
		}

		if (SysDictionary.BILL_PROPERTY_CLOSED.equalsIgnoreCase(billProperty))
		{
			throw SystemException.getInstance(PosErrorCode.BILL_CLOSED);
		}

		if (paymentAmount <= 0)
		{// 账单金额为0,判断是否可以零结账
			String isZeroPay =posDishDao.getSysParameter(tenancyId, storeId, "IS_ZEROPAY");
			if(!"1".equals(isZeroPay))
			{
				throw SystemException.getInstance(PosErrorCode.BILL_AMOUNT_ZERO);
			}
		}

//		StringBuilder findPaymentWaySql = new StringBuilder(
//				"select (case when p.payment_class='cash' and d.id is not null then d.class_item else p.payment_name1 end) as payment_name,p.payment_name1,p.payment_class,p.if_jifen,p.discount_case,p.rate,p.is_check from payment_way p left join payment_way_of_ogran o on p.id = o.payment_id left join sys_dictionary d on d.class_item_code=p.payment_name1 where o.tenancy_id = ? and o.organ_id = ? and o.payment_id = ?");

//		StringBuilder insertPaymentSql = new StringBuilder(
//				"insert into pos_bill_payment(tenancy_id,store_id,bill_num,report_date,shift_id,pos_num,cashier_num,table_code,type,jzid,name,name_english,rate,amount,count,currency_amount,number,phone,is_ysk,customer_id,bill_code,remark,last_updatetime,upload_tag,payment_state,param_cach) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,'0',?,?)");
//
//		StringBuilder insertPaymentLogSql = new StringBuilder(
//				"insert into pos_bill_payment_log(tenancy_id,store_id,bill_num,report_date,shift_id,pos_num,cashier_num,table_code,type,jzid,name,name_english,rate,amount,count,currency_amount,number,phone,is_ysk,customer_id,bill_code,remark,last_updatetime,upload_tag,payment_state) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,'0',?)");

		for (Object itemObj : para.optJSONArray("item"))
		{
			JSONObject itemJson = JSONObject.fromObject(itemObj);
			itemJson.put("chanel", chanel);

			if (Tools.isNullOrEmpty(itemJson.opt("jzid")))
			{
				throw SystemException.getInstance(PosErrorCode.NOT_NULL_JZID);
			}
			if (Tools.isNullOrEmpty(itemJson.opt("amount")))
			{
				throw SystemException.getInstance(PosErrorCode.NOT_NULL_PAYMENT_AMOUNT);
			}

			int jzid = itemJson.optInt("jzid");
			double amount = itemJson.optDouble("amount");
			double count = itemJson.optDouble("count");
			double currency_amount = itemJson.optDouble("currency_amount");
			String number = itemJson.optString("number");
			String phone = itemJson.optString("phone");
			int customer_id = itemJson.optInt("customer_id");
			String remark = itemJson.optString("remark");
			String is_ysk = itemJson.optString("is_ysk");
//			double integraloffset = itemJson.optDouble("integraloffset");
			if (Tools.isNullOrEmpty(is_ysk))
			{
				is_ysk = "N";
			}

			String paymentClass = "";
			String paymentName = "";
			String paymentEnglishName = "";
			double exchangeRate = 1d;
			boolean isCheck = true;
			
			JSONObject paymentWayJson = posDishDao.getPaymentWayByID(tenancyId, storeId, jzid);
			if (null !=paymentWayJson && !paymentWayJson.isEmpty())
			{
				paymentClass = paymentWayJson.optString("payment_class");
				paymentName = paymentWayJson.optString("payment_name");
				paymentEnglishName = paymentWayJson.optString("name_english");
				exchangeRate = Tools.hv(paymentWayJson.opt("rate")) ? paymentWayJson.optDouble("rate") : 1d;
				if(Tools.isNullOrEmpty(itemJson.opt("is_check")))
				{
					isCheck = "1".equals(paymentWayJson.optString("is_check"));
				}
				else
				{
					isCheck = "1".equals(itemJson.getString("is_check"));
				}
			}
			else
			{
				throw SystemException.getInstance(PosErrorCode.NOT_EXIST_JZID);
			}

			if (paymentAmount > 0)
			{
//				if (SysDictionary.PAYMENT_CLASS_CARD_CREDIT.equals(paymentClass))
//				{// 会员积分
//					amount = itemJson.optDouble("integraloffset");
//					currency_amount = itemJson.optDouble("integraloffset");
//				}

				if (amount <= 0)
				{
					throw SystemException.getInstance(PosErrorCode.PAYAMOUNT_NOT_MORE_ZERO_ERROR);
				}

				if (SysDictionary.PAYMENT_CLASS_FREESINGLE.equals(paymentClass))
				{
					// 如果有优惠提示取消优惠
					if (discountCaseId != 0 || discountRate != 100 || discountAmount > 0)
					{
						throw SystemException.getInstance(PosErrorCode.POS_FREESINGLE_NOT_DISCOUNT_ERROR);
					}

					// 免单不允许与其他方式混结
					String sql = new String("select count(*) as count from pos_bill_payment where bill_num =? and store_id = ? and tenancy_id=?");
					int paycount = posDishDao.queryForInt(sql.toString(), new Object[]
					{ billNum, storeId, tenancyId });
					if (paycount > 0)
					{
						throw SystemException.getInstance(PosErrorCode.POS_FREESINGLE_NOT_PAYMENT_ERROR);
					}

					// 免单金额=账单金额
					posDishDao.insertPosBillPayment(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, paymentAmount, count, paymentAmount, null, null, is_ysk, null, null, remark, currentTime,
							SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);

					posDishDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, paymentAmount, count, paymentAmount, null, null, is_ysk, null, null, remark, currentTime,
							SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);

					String updateBillSql = new String("update pos_bill set free_amount=?,billfree_reason_id=? where bill_num = ? and store_id = ? and tenancy_id = ?");
					posDishDao.update(updateBillSql, new Object[]
					{ paymentAmount, itemJson.optInt("reason_id"), billNum, storeId, tenancyId });

					String updateBillItemSql = new String("update pos_bill_item set item_remark=? where bill_num = ? and store_id = ? and tenancy_id = ? and item_remark is null");
					posDishDao.update(updateBillItemSql, new Object[]
					{ SysDictionary.ITEM_REMARK_MD03, billNum, storeId, tenancyId });
				}
				else if (SysDictionary.PAYMENT_CLASS_CASH.equals(paymentClass))
				{// 现金 cash
					double cashAmount = 0d;
					StringBuilder findPaymentSql = new StringBuilder("select amount from pos_bill_payment where bill_num = ? and jzid = ? and store_id = ?");
					rs = posDishDao.query4SqlRowSet(findPaymentSql.toString(), new Object[]
					{ billNum, jzid, storeId });
					if (rs.next())
					{
						cashAmount = rs.getDouble("amount");
					}

					if (cashAmount > 0)
					{
						StringBuilder updateBillPaymentSql = new StringBuilder("update pos_bill_payment set amount=?,count=?,report_date=?,shift_id=?,pos_num=?,cashier_num=?,last_updatetime=?,rate=?,currency_amount=? where store_id = ? and bill_num = ? and jzid = ?");
						cashAmount = DoubleHelper.add(cashAmount, amount, PAYMENT_SCALE);
						double currencyAmount = DoubleHelper.mul(cashAmount, exchangeRate, PAYMENT_SCALE);
						posDishDao.update(updateBillPaymentSql.toString(), new Object[]
						{ cashAmount, 1, reportDate, shiftId, posNum, optNum, currentTime, exchangeRate, currencyAmount, storeId, billNum, jzid });
					}
					else
					{
						double currencyAmount = DoubleHelper.mul(amount, exchangeRate, PAYMENT_SCALE);
						posDishDao.insertPosBillPayment(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, amount, count, currencyAmount, null, null, is_ysk, null, null, remark, currentTime,
								SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);
					}

					double currencyAmount = DoubleHelper.mul(amount, exchangeRate, PAYMENT_SCALE);
					posDishDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, amount, count, currencyAmount, null, null, is_ysk, null, null, remark, currentTime,
							SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);
				}
				else if (SysDictionary.PAYMENT_CLASS_BANKCARD.equals(paymentClass))
				{// 银行卡 bankcard
					posDishDao.insertPosBillPayment(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, amount, count, currency_amount, number, phone, is_ysk, null, itemJson.optString("bill_code"), remark,
							currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);

					posDishDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, amount, count, currency_amount, number, phone, is_ysk, null, itemJson.optString("bill_code"),
							remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);
				}
				else if (SysDictionary.PAYMENT_CLASS_CARD.equals(paymentClass))
				{// 本系统卡 card
					StringBuilder sql =  new StringBuilder(" select count(*) from pos_bill_member where tenancy_id=? and store_id=? and bill_num = ? and type = ?");
					int memberCount = posDishDao.queryForInt(sql.toString(), new Object[]
					{ tenancyId, storeId, billNum, SysDictionary.BILL_MEMBERCARD_JZ03 });
					if(0==memberCount)
					{
						posDishDao.insertPosBillMember(tenancyId, storeId, billNum, reportDate, SysDictionary.BILL_MEMBERCARD_JZ03, number, itemJson.optString("remark"), phone, currentTime, null, null, 0);
					}
					
					if (SysDictionary.CHANEL_WX02.equals(chanel))
					{
						posDishDao.insertPosBillPayment(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, amount, count, currency_amount, number, phone, is_ysk, null, itemJson.optString("bill_code"),
								remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);

						posDishDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, amount, count, currency_amount, number, phone, is_ysk, null, itemJson.optString("bill_code"),
								remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);
					}
					else
					{
						posDishDao.insertPosBillPayment(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, amount, count, currency_amount, number, phone, is_ysk, null, null, remark, currentTime,
								SysDictionary.PAYMENT_STATE_PAY, batchNum, itemJson.toString(), 0d);
					}
				}
				else if (SysDictionary.PAYMENT_CLASS_CARD_CREDIT.equals(paymentClass))
				{// 会员积分
					// amount = itemJson.optDouble("consume_credit");
					// currency_amount = itemJson.optDouble("integraloffset");
					posDishDao.insertPosBillMember(tenancyId, storeId,  billNum,reportDate, SysDictionary.BILL_MEMBERCARD_JZ03, number, itemJson.optString("remark"), phone, currentTime, null, null, 0);
					if (SysDictionary.CHANEL_WX02.equals(chanel))
					{
						posDishDao.insertPosBillPayment(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, amount, count, currency_amount, number, phone, is_ysk, null, itemJson.optString("bill_code"),
								remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);

						posDishDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, amount, count, currency_amount, number, phone, is_ysk, null, itemJson.optString("bill_code"),
								remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);
						
						posDishDao.updatePosBillMember(tenancyId, storeId, billNum, SysDictionary.BILL_MEMBERCARD_JZ03, currency_amount, amount, itemJson.optString("bill_code"), SysDictionary.REQUEST_STATUS_COMPLETE, null, 0d, 0d);
					}
					else
					{
						posDishDao.insertPosBillPayment(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, amount, count, currency_amount, number, phone, is_ysk, null, null, remark, currentTime,
								SysDictionary.PAYMENT_STATE_PAY, batchNum, itemJson.toString(), 0d);
					}
				}
				else if (SysDictionary.PAYMENT_CLASS_COUPONS.equals(paymentClass))
				{
					if (SysDictionary.CHANEL_WX02.equals(chanel))
					{
						posDishDao.insertPosBillPayment(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, amount, count, currency_amount, number, phone, is_ysk, null, itemJson.optString("bill_code"),
								remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);

						posDishDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, amount, count, currency_amount, number, phone, is_ysk, null, itemJson.optString("bill_code"),
								remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);
					}
					else
					{
						if (isCheck || !StringUtils.isEmpty(number))
						{
							posDishDao.insertPosBillPayment(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, amount, count, currency_amount, number, phone, is_ysk, null, null, remark, currentTime,
									SysDictionary.PAYMENT_STATE_PAY, batchNum, itemJson.toString(), 0d);
						}
						else
						{
							posDishDao.insertPosBillPayment(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, amount, count, currency_amount, number, phone, is_ysk, null, null, remark, currentTime,
									SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);

							posDishDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, amount, count, currency_amount, number, phone, is_ysk, null, null, remark, currentTime,
									SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);
						}
					}
				}
				else if (SysDictionary.PAYMENT_CLASS_INCORPORATION.equals(paymentClass))
				{
					if (SysDictionary.CHANEL_WX02.equals(chanel))
					{
						posDishDao.insertPosBillPayment(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, amount, count, currency_amount, number, phone, is_ysk, customer_id,
								itemJson.optString("bill_code"), remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);

						posDishDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, amount, count, currency_amount, number, phone, is_ysk, customer_id,
								itemJson.optString("bill_code"), remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);
					}
					else
					{
						posDishDao.insertPosBillPayment(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, amount, count, currency_amount, number, phone, is_ysk, customer_id, null, remark, currentTime,
								SysDictionary.PAYMENT_STATE_PAY, batchNum, itemJson.toString(), 0d);
					}
				}
				else if (SysDictionary.PAYMENT_CLASS_WECHAT_PAY.equals(paymentClass))
				{// 微信支付 wechat_pay
					posDishDao.insertPosBillPayment(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, amount, count, currency_amount, number, phone, is_ysk, null, itemJson.optString("bill_code"), remark,
							currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);

					posDishDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, amount, count, currency_amount, number, phone, is_ysk, null, itemJson.optString("bill_code"),
							remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);
				}
				else if (SysDictionary.PAYMENT_DDM_QUICK_PAY.equals(paymentClass) || SysDictionary.PAYMENT_CLASS_ALI_PAY.equals(paymentClass))
				{// 支付宝支付 ali_pay
					posDishDao.insertPosBillPayment(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, amount, count, currency_amount, number, phone, is_ysk, null, itemJson.optString("bill_code"), remark,
							currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);

					posDishDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, amount, count, currency_amount, number, phone, is_ysk, null, itemJson.optString("bill_code"),
							remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);
				}
				else
				{
					posDishDao.insertPosBillPayment(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, amount, count, currency_amount, number, phone, is_ysk, customer_id, itemJson.optString("bill_code"),
							remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);

					posDishDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, amount, count, currency_amount, number, phone, is_ysk, customer_id, itemJson.optString("bill_code"),
							remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);
				}
			}
			else
			{
				posDishDao.insertPosBillPayment(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, amount, count, currency_amount, number, phone, is_ysk, customer_id, itemJson.optString("bill_code"),
						remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);

				posDishDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, amount, count, currency_amount, number, phone, is_ysk, customer_id, itemJson.optString("bill_code"),
						remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);
			}
		}

		String sql = new String("select coalesce(sum(currency_amount),0) as amount from pos_bill_payment where bill_num =? and store_id = ?");
		rs = posDishDao.query4SqlRowSet(sql.toString(), new Object[]
		{ billNum, storeId });
		double difference = 0d;
		if (rs.next())
		{
			difference = DoubleHelper.sub(paymentAmount, rs.getDouble("amount"), DEFAULT_SCALE);
		}

		JSONObject resultJson = new JSONObject();
		if (difference <= 0)
		{
			StringBuilder updateBillPaySql = new StringBuilder("update pos_bill set payment_state=?,sale_mode =? where tenancy_id= ? and store_id=? and bill_num=?");
			posDishDao.update(updateBillPaySql.toString(), new Object[]
			{ SysDictionary.PAYMENT_STATE_PAY, saleMode, tenancyId, storeId, billNum });
			
			String updateSaleMode = new String("update pos_bill_item set sale_mode = ? where (sale_mode is null or sale_mode='') and bill_num = ? and store_id =? and tenancy_id=?");
			posDishDao.update(updateSaleMode, new Object[]
			{ saleMode, billNum, storeId, tenancyId });
			
			try
			{
				StringBuilder selectSql = new StringBuilder("select bp.*,pw.payment_class,pw.is_check from pos_bill_payment bp left join payment_way pw on bp.jzid=pw.id where bp.bill_num =? and bp.store_id = ? and bp.tenancy_id=? and bp.payment_state=? order by bp.last_updatetime");
				List<JSONObject> payList = posDishDao.query4Json(tenancyId, selectSql.toString(), new Object[]
				{ billNum, storeId, tenancyId, SysDictionary.PAYMENT_STATE_PAY });
				
				this.paymentDebited(tenancyId, storeId, billNum, chanel, paymentAmount,payList, result);
			}
			catch (SystemException se)
			{
				se.printStackTrace();
				logger.info("结账失败:  " + ExceptionMessage.getExceptionMessage(se));
	
//				this.paymentRefund(tenancyId, storeId, billNum, chanel, paymentAmount);
	
				ErrorCode error = se.getErrorCode();
				String msg = se.getErrorMsg();
//				String msg = PropertiesLoader.getProperty(String.valueOf(error.getNumber()));
//				Map<String, Object> map = se.getProperties();
//				for (String key : map.keySet())
//				{
//					msg = msg.replace(key, String.valueOf(map.get(key)));
//				}
				result.setCode(error.getNumber());
				result.setMsg(msg);
				logger.error("原因：" + msg + ",错误码：" + error.getNumber());
			}
			catch (Exception e)
			{
				e.printStackTrace();
				logger.info("结账失败:  " + ExceptionMessage.getExceptionMessage(e));
	
//				this.paymentRefund(tenancyId, storeId, billNum, chanel, paymentAmount);
				
				result.setCode(Constant.CODE_INNER_EXCEPTION);
				result.setMsg(Constant.PAYMENT_FAILURE);
			}
			
			resultJson = this.closedPosBill(tenancyId, storeId, billNum, report_Date, shiftId, posNum, optNum, isprint_bill, printJson);
			
		}
		else
		{
			String reSql = new String("select id,jzid,name,amount,count,coalesce(number,'') as number,coalesce(phone,'') as phone,shift_id,pos_num,cashier_num,last_updatetime,coalesce(customer_id,0) as customer_id,coalesce(bill_code,'') as bill_code,coalesce(remark,'') as remark,currency_amount,payment_state from pos_bill_payment where tenancy_id=? and store_id = ? and bill_num =?");
			List<JSONObject> paymentList = posDishDao.query4Json(tenancyId, reSql, new Object[]
			{ tenancyId, storeId, billNum });
			for (JSONObject pay : paymentList)
			{
				pay.put("payment_state_text", SysDictionary.PAYMENT_STATE_TEXT_MAP.get(pay.optString("payment_state")));
			}
			
			resultJson.put("difference", difference);
			resultJson.put("change", 0d);
			resultJson.put("paymentlist", paymentList);
			resultJson.put("bill_num", billNum);
			resultJson.put("table_code", tableCode);
			resultJson.put("payment_amount", paymentAmount);
			resultJson.put("discount_amount", discountAmount);
			resultJson.put("payment_state", SysDictionary.PAYMENT_STATE_NOTPAY);
			resultJson.put("payment_state_text", SysDictionary.PAYMENT_STATE_TEXT_MAP.get(SysDictionary.PAYMENT_STATE_NOTPAY));
		}
		
		List<JSONObject> resultList = new ArrayList<JSONObject>();
		resultList.add(resultJson);

		result.setData(resultList);
		if (Constant.CODE_SUCCESS == result.getCode())
		{
			result.setCode(Constant.CODE_SUCCESS);
			result.setMsg(Constant.PAYMENT_SUCCESS);
		}
	}

	@Override
	public List<JSONObject> micropay(Data data, String path) throws Exception
	{
		List<JSONObject> response = new ArrayList<JSONObject>();
		@SuppressWarnings("unchecked")
		List<JSONObject> list = (List<JSONObject>) data.getData();
		JSONObject param = JSONObject.fromObject(list.get(0));
		// 验证查询参数是否存在
		if (!param.containsKey("pos_num") || !param.containsKey("opt_num") || !param.containsKey("report_date") || !param.containsKey("shift_id") || !param.containsKey("jzid"))
		{
			JSONObject json = getErrorReturn(data.getTenancy_id(), data.getStore_id(), param.getString("bill_num"));
			json.put("error_msg", "参数不全，请参照对接文档");
			response.add(json);
			return response;
		}
		// 先进行查询接口，如果已经支付成功、则进行订单的关闭
		param.put("polling_flag", false);// 设置不进行轮询
		list.remove(0);
		list.add(param);
		data.setData(list);
//		data.setOper(Oper.query);
//		List<JSONObject> queryResult = this.orderquery(data, path);
//		if (queryResult != null)
//		{
//			// 如果订单状态
//			JSONObject queryReturn = queryResult.get(0);
//			if (queryReturn.getBoolean("success") && "finished".equals(queryReturn.getString("status")))
//			{
//				logger.info("unifiedorder:已经支付成功 \n " + JSON.toJSONString(data));
//			}
//			else if (!queryReturn.getBoolean("success") && queryReturn.containsKey("exception"))
//			{
//				JSONObject json = getErrorReturn(data.getTenancy_id(), data.getStore_id(), param.getString("bill_num"));
//				response.add(json);
//				return response;
//			}
//			else if (queryReturn.getBoolean("success") && "notpay".equals(queryReturn.getString("status")))
//			{
//				// 因为微信订单号唯一没法进行修改金额后再次下单，所以对这种情况进行了再次生成订单、在重新订单生成之前
//				// 将原本未支付的订单号取消掉
//				// data.setOper(Oper.cancle);
//				// List<JSONObject> cancleReturn = this.closeorder(data, path);
//				// if(cancleReturn!=null){
//				// JSONObject json = cancleReturn.get(0);
//				// if(json.getBoolean("success") &&
//				// "finished".equals(json.getString("status"))){
//				// logger.info("unifiedorder:因为订单未支付，重新生成订单后取消订单 \n "+JSON.toJSONString(data));
//				// }else{
//				// logger.error("unifiedorder:关闭订单失败 出现异常 \n "+JSON.toJSONString(data));
//				// }
//				// }else{
//				// logger.error("unifiedorder:关闭订单失败 出现异常 \n "+JSON.toJSONString(data));
//				// }
//			}
//		}
//		else
//		{
//			JSONObject json = getErrorReturn(data.getTenancy_id(), data.getStore_id(), param.getString("bill_num"));
//			response.add(json);
//			return response;
//		}
		data.setOper(Oper.scan);
		String paramStr = JSON.toJSONString(data);
		String reqURL = com.tzx.framework.common.constant.Constant.systemMap.get("pos_payment_wechat_url");
		String result = HttpUtil.sendPostRequest(reqURL, paramStr);
		// 处理结果
		if (result != null)
		{
			try
			{
				com.alibaba.fastjson.JSONObject json = com.alibaba.fastjson.JSONObject.parseObject(result);
				boolean success = json.getBooleanValue("success");
				if (success)
				{
					String status = json.getString("status");
					if ("query".equals(status))
					{
						// 需要重新查询订单状态,设置间隔时间(建议10秒)重新查询支付结果，直到支付成功或超时(建议30秒)；
						param.put("polling_flag", true);// 设置不进行轮询
						param.put("query_period", 10);
						param.put("query_time", 3);
						list.remove(0);
						list.add(param);
						data.setData(list);
						data.setOper(Oper.query);
						List<JSONObject> queryResultSecond = this.orderquery(data, path);
						// 获取状态异常，直接返回错误信息，不再进行数据发送
						if (queryResultSecond != null)
						{
							// 如果订单状态
							JSONObject queryReturn = queryResultSecond.get(0);
							if (queryReturn.getBoolean("success") && "finished".equals(queryReturn.getString("status")))
							{
								logger.info("micropay:已经支付成功 \n " + JSON.toJSONString(data));
								return queryResultSecond;
							}
							else if (!queryReturn.getBoolean("success"))
							{
								JSONObject jsonSecond = getErrorReturn(data.getTenancy_id(), data.getStore_id(), param.getString("bill_num"));
								response.add(jsonSecond);
								return response;
							}
						}
						else
						{
							JSONObject jsonSecond = getErrorReturn(data.getTenancy_id(), data.getStore_id(), param.getString("bill_num"));
							response.add(jsonSecond);
							return response;
						}
					}
				}
				response.add(JSONObject.fromObject(json));
				return response;
			}
			catch (Exception e)
			{
				logger.error(e.getMessage());
				JSONObject json = getErrorReturn(data.getTenancy_id(), data.getStore_id(), param.getString("bill_num"));
				response.add(json);
			}
		}
		else
		{
			JSONObject json = getErrorReturn(data.getTenancy_id(), data.getStore_id(), param.getString("bill_num"));
			response.add(json);
		}
		return response;
	}

	/**
	 * 增加积分 GJ20160407
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param reportDate
	 */
	private void customerCreditAdd(String tenancyId, int storeId, String billNum,String reportDate,int shiftId, String opt_Num,String opt_Name,String chanel,Timestamp currentTime)
	{
		try
		{
			Date report_Date = DateUtil.parseDate(reportDate);
			StringBuilder sql = new StringBuilder();
			SqlRowSet rst = null;
			String mobil = null;
			String card_code = null;
			String customerCode =null;
			double amount = 0d;

			sql.append(" select card_code,mobil,customer_code from pos_bill_member where tenancy_id=? and store_id=? and bill_num = ? and report_date = ? and type = ? order by last_updatetime desc");
			rst = this.posDishDao.query4SqlRowSet(sql.toString(), new Object[]
			{ tenancyId, storeId, billNum, report_Date, SysDictionary.BILL_MEMBERCARD_JF01 });
			
			if (rst.next())
			{
				mobil = rst.getString("mobil");
				card_code = rst.getString("card_code");
				customerCode = rst.getString("customer_code");
			} else
			{
				sql.delete(0, sql.length());
				sql.append(" select card_code,mobil,customer_code from pos_bill_member where tenancy_id=? and store_id=? and bill_num = ? and report_date = ? order by last_updatetime desc");
				rst = this.posDishDao.query4SqlRowSet(sql.toString(), new Object[]
						{ tenancyId, storeId, billNum, report_Date });
				if (rst.next())
				{
					mobil = rst.getString("mobil");
					card_code = rst.getString("card_code");
					customerCode = rst.getString("customer_code");
				}
				
				posDishDao.insertPosBillMember(tenancyId, storeId,  billNum, report_Date,SysDictionary.BILL_MEMBERCARD_JF01, customerCode,card_code, mobil, currentTime,null, null, 0);
			}

			if (StringUtils.isEmpty(mobil) && StringUtils.isEmpty(card_code))
				return;

			sql.delete(0, sql.length());
			sql.append(" select coalesce(sum(pbp.currency_amount),0) as amount from pos_bill_payment pbp  ");
			sql.append(" join payment_way pway on pbp.jzid = pway.id and pbp.tenancy_id = pway.tenancy_id ");
			sql.append(" where pway.if_jifen = ? and pbp.store_id = ? and pbp.tenancy_id = ? and pbp.bill_num=? and pbp.report_date = ? ");
			rst = this.posDishDao.query4SqlRowSet(sql.toString(), new Object[]
			{ '1', storeId, tenancyId, billNum, report_Date });

			if (rst.next())
			{
				amount = rst.getDouble("amount");	
			}
			
			String currentTimeStr = DateUtil.format(currentTime);
			String lastUpdatetimeStr = String.valueOf(currentTime.getTime());
			
			if(amount>0)
			{
				JSONObject requestJson = new JSONObject();
				requestJson.put("mobil", mobil);// 手机号
				requestJson.put("card_code", card_code);// 会员卡号
				requestJson.put("consume_creditmoney", String.valueOf(amount));// 增加积分数
				requestJson.put("end_date", "");// 失效日期
				requestJson.put("reason", "消费积分");// 原因
				requestJson.put("chanel", chanel);// 渠道
				requestJson.put("bill_code", billNum);// 单号
				requestJson.put("remark", "");//备注
				requestJson.put("operator_id", opt_Num);//操作人
				requestJson.put("updatetime", currentTimeStr);//操作时间
				requestJson.put("business_date",reportDate );//报表日期
				requestJson.put("shift_id", shiftId);
				requestJson.put("operator", opt_Name);
				requestJson.put("batch_no", lastUpdatetimeStr);
				
				List<JSONObject> requestList = new ArrayList<JSONObject>();
				requestList.add(requestJson);
	
				Data requestData = Data.get(tenancyId, storeId, 0);
				requestData.setType(Type.CUSTOMER_CREDIT);
				requestData.setOper(Oper.add);
				requestData.setData(requestList);
	
				Data responseData = Data.get(requestData);
				customerService.commonPost(JSONObject.fromObject(requestData).toString(), responseData);
				if (!responseData.isSuccess())
				{
					JSONObject responseJson = JSONObject.fromObject(responseData.getData().get(0));
					if (5 != responseData.getCode() && 99 != responseData.getCode())
					{
						logger.info("结账失败:  " + responseJson.toString());
						throw SystemException.getInstance(PosErrorCode.BILL_PAYMENT_ERROP).set("{0}", responseData.getMsg());
					}
					
					posDishDao.updatePosBillMember(tenancyId, storeId, billNum, SysDictionary.BILL_MEMBERCARD_JF01, amount, 0d,null,SysDictionary.REQUEST_STATUS_FAILURE,null,0d,0d);
				}
				else
				{
					JSONObject responseJson = null;
					if(null != responseData.getData() && responseData.getData().size()>0)
					{
						responseJson = JSONObject.fromObject(responseData.getData().get(0));
						posDishDao.updatePosBillMember(tenancyId, storeId, billNum, SysDictionary.BILL_MEMBERCARD_JF01, amount, responseJson.optDouble("credit"),responseJson.optString("bill_code"),SysDictionary.REQUEST_STATUS_COMPLETE,responseJson.optString("name"),DoubleHelper.sub(responseJson.optDouble("useful_credit"), responseJson.optDouble("credit"), DEFAULT_SCALE),responseJson.optDouble("useful_credit"));
					}
				}
			}
		}
		catch (Exception e)
		{
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
	private void writeKvsBill(String tenancyId, int storeId, String billNum, String reportDate) throws Exception
	{
//		Timestamp currentTime = DateUtil.currentTimestamp();
		String billSql = 
				"	INSERT INTO pos_kvs_bill(tenancy_id,store_id,bill_num,order_time,kvs_num,upload_tag,report_date)       				 " +
				"	SELECT pb.tenancy_id,pb.store_id,pb.bill_num,current_timestamp,posd.kvs_num,0 as upload_tag,pb.report_date from pos_bill as pb  "+
				"	LEFT JOIN( "+
				"				SELECT DISTINCT posd.pos_num,posd.devices_num as kvs_num,posd.tenancy_id,posd.store_id  "+
				"				from hq_devices as hd JOIN pos_opt_state_devices AS posd "+
				"				ON posd.devices_num = hd.devices_code AND posd.is_valid = '1' and "+
				"				posd.tenancy_id = hd.tenancy_id  AND posd.store_id = hd.store_id"+
				"				WHERE show_type = 'KVS' "+
				"			 ) as posd ON posd.pos_num = pb.pos_num and posd.tenancy_id = pb.tenancy_id and posd.store_id = pb.store_id"+
				" 	WHERE pb.tenancy_id = ? and pb.store_id = ? and pb.report_date= ? and pb.bill_num = ?; "+
				
				
				"	INSERT INTO pos_kvs_bill_item( tenancy_id,store_id,bill_num,rwid,kvs_num,upload_tag,report_date ) "+
				"	SELECT pbi.tenancy_id,pbi.store_id,pbi.bill_num,pbi.rwid,posd.kvs_num,0 as upload_tag,pbi.report_date FROM pos_bill_item as pbi"+
				"	LEFT JOIN( "+
				"				SELECT DISTINCT posd.pos_num,posd.devices_num as kvs_num,posd.tenancy_id,posd.store_id  "+
				"				from hq_devices as hd JOIN pos_opt_state_devices AS posd "+
				"				ON posd.devices_num = hd.devices_code AND posd.is_valid = '1' and "+
				"				posd.tenancy_id = hd.tenancy_id  AND posd.store_id = hd.store_id"+
				"				WHERE show_type = 'KVS' "+
				"			 ) as posd ON posd.pos_num = pbi.item_mac_id and posd.tenancy_id = pbi.tenancy_id and posd.store_id = pbi.store_id"+
				"	WHERE pbi.tenancy_id = ? and pbi.store_id = ? and pbi.report_date= ? and pbi.bill_num = ?"+
				"";
		posDishDao.update(billSql, new Object[]
				{  
				tenancyId, storeId, DateUtil.parseDate(reportDate), billNum, 
				tenancyId, storeId, DateUtil.parseDate(reportDate), billNum
				}
		);
		
		Data cjData = new Data();
		cjData.setType(Type.KVS_INFO);
		cjData.setOper(Oper.find);
		//List<JSONObject> cometList = new ArrayList<JSONObject>();
		//cjData.setData(cometList);
		Comet4jUtil.sendMessage2All(Comet4jUtil.POS_PC, JSONObject.fromObject(cjData).toString());
	}

	/**
	 * 关闭账单
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param reportDate
	 * @param shiftId
	 * @param posNum
	 * @param optNum
	 * @param isPrint
	 * @return
	 * @throws Exception
	 */
	private JSONObject closedPosBill(String tenancyId, int storeId, String billNum, String reportDate, int shiftId, String posNum, String optNum, String isPrint, JSONObject printJson) throws Exception
	{
		Timestamp currentTime = DateUtil.currentTimestamp();
		StringBuilder qureyBillSql = new StringBuilder("select coalesce(payment_amount,0) as payment_amount,coalesce(discount_amount,0) as discount_amount,payment_state,table_code,bill_property,order_num,source,batch_num from pos_bill where tenancy_id=? and store_id=? and bill_num=?");
		SqlRowSet rs = posDishDao.query4SqlRowSet(qureyBillSql.toString(), new Object[]
		{ tenancyId, storeId, billNum });

		double paymentAmount = 0d;
		double discountAmount =0d;
		String paymentState = null;
		String tableCode = null;
		String billProperty=null;
		String orderNum =null;
		String chanel=SysDictionary.CHANEL_MD01;
		String batchNum="";
		if (rs.next())
		{
			paymentAmount = rs.getDouble("payment_amount");
			discountAmount = rs.getDouble("discount_amount");
			paymentState = rs.getString("payment_state");
			tableCode = rs.getString("table_code");
			billProperty = rs.getString("bill_property");
			orderNum = rs.getString("order_num");
			chanel = rs.getString("source");
			batchNum = rs.getString("batch_num");
		}

		if(SysDictionary.BILL_PROPERTY_CLOSED.equals(billProperty))
		{
			throw SystemException.getInstance(PosErrorCode.BILL_CLOSED);
		}
		
		String sql = new String("select count(case when payment_state=? or payment_state=? then 1 else null end) as count,coalesce(sum(currency_amount),0) as amount,coalesce(sum(case when type='card_credit' then currency_amount else 0 end),0) as integraloffset from pos_bill_payment where tenancy_id=? and store_id = ? and bill_num =?");
		rs = posDishDao.query4SqlRowSet(sql.toString(), new Object[]
		{ SysDictionary.PAYMENT_STATE_PAY,SysDictionary.PAYMENT_STATE_PAY_FAILURE,tenancyId ,storeId,billNum });
		double difference = 0d;
		double integraloffset = 0d;
		if (rs.next())
		{
			difference = DoubleHelper.sub(paymentAmount, rs.getDouble("amount"), DEFAULT_SCALE);
			integraloffset = rs.getDouble("integraloffset");
			if(0==rs.getInt("count") && paymentAmount <= rs.getDouble("amount"))
			{
				paymentState = SysDictionary.PAYMENT_STATE_PAY_COMPLETE;
			}
		}

		List<JSONObject> paymentList = null;
		if (difference <= 0 && SysDictionary.PAYMENT_STATE_PAY_COMPLETE.equals(paymentState))
		{
			double moreCoupon = 0d;
            int moreCouponId=-1;
            if (difference < 0)
			{ // 计算优惠券多收礼券

                String moreCouponWays="";
                for(String way:moreCoupouPaymentWays){
                    moreCouponWays+=",'"+way+"'";
                }
                moreCouponWays=moreCouponWays.replaceFirst(",",moreCouponWays);

				String paySql = "select coalesce(currency_amount,0) as amount,p.id,p.type from pos_bill_payment p left join payment_way w on p.tenancy_id=w.tenancy_id and p.jzid=w.id where p.tenancy_id=? and p.store_id=? and p.bill_num=? and p.\"type\" in ("+moreCouponWays+") ORDER BY id ";

				rs = posDishDao.query4SqlRowSet(paySql.toString(), new Object[]
				{ tenancyId, storeId, billNum });

                double amount=0d;

                while (rs.next()){
                    if(-1==moreCouponId){
                        moreCouponId=rs.getInt("id");
                    }
                    amount+=rs.getDouble("amount");
                }
                if (paymentAmount < amount) {
                    moreCoupon = DoubleHelper.sub(amount, paymentAmount, DEFAULT_SCALE);
                    difference = 0d;
                }
			}

			if (difference < 0)
			{ // 在支付流水表插入找零记录
				StringBuilder pwsql = new StringBuilder("select pw.id,pw.rate from payment_way pw left join payment_way_of_ogran pwoo on pw.tenancy_id = pwoo.tenancy_id and pw.id = pwoo.payment_id where pwoo.tenancy_id=? and pwoo.organ_id = ? and pw.is_standard_money='1' order by pw.id");
				rs = posDishDao.query4SqlRowSet(pwsql.toString(), new Object[]
				{ tenancyId,storeId });
				// 保存的是本币对应的jzid
				Integer payWayId = null;
				if (rs.next())
				{
					payWayId = rs.getInt("id");
				}
				posDishDao.insertPosBillPayment(tenancyId, storeId, billNum, DateUtil.parseDate(reportDate), shiftId, posNum, optNum, tableCode, SysDictionary.PAYMENT_CLASS_CASH, payWayId, "找零", "change", 1d, difference, 1d, difference, null, null, "N", null, null, null, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);
				
				posDishDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, DateUtil.parseDate(reportDate), shiftId, posNum, optNum, tableCode, SysDictionary.PAYMENT_CLASS_CASH, payWayId, "找零", "change", 1d, difference, 1d, difference, null, null, "N", null, null, null, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0d);
			}

			if(!SysDictionary.CHANEL_WX02.equals(chanel))
			{
				// 增加积分
				this.customerCreditAdd(tenancyId, storeId, billNum,reportDate, shiftId, optNum, optNum,chanel,currentTime);
			}
			
			// 关闭账单
			StringBuilder updateBillSql = new StringBuilder("update pos_bill set more_coupon=?,difference =?,bill_property =?,payment_time =?,pos_num =?,cashier_num =?,shift_id =?,payment_state=?,integraloffset=? where tenancy_id=? and store_id = ? and bill_num =? ;");
            updateBillSql.append("update pos_bill_payment set more_coupon=? where id=?;");

			posDishDao.update(updateBillSql.toString(), new Object[]
			{ moreCoupon, difference, SysDictionary.BILL_PROPERTY_CLOSED, currentTime, posNum, optNum, shiftId,
                    SysDictionary.PAYMENT_STATE_PAY_COMPLETE,integraloffset,tenancyId, storeId, billNum,moreCoupon,moreCouponId});

			String reSql = new String("select id,jzid,name,amount,count,coalesce(number,'') as number,coalesce(phone,'') as phone,shift_id,pos_num,cashier_num,last_updatetime,coalesce(customer_id,0) as customer_id,coalesce(bill_code,'') as bill_code,coalesce(remark,'') as remark,currency_amount,payment_state from pos_bill_payment where tenancy_id=? and store_id = ? and bill_num =?");
			paymentList = posDishDao.query4Json(tenancyId, reSql, new Object[]
			{ tenancyId, storeId, billNum });
			StringBuilder newstate = new StringBuilder();
			for (JSONObject pay : paymentList)
			{
				newstate.append("支付方式:").append(pay.optString("name")).append(",").append("支付金额:").append(pay.optDouble("amount")).append(";");
				pay.put("payment_state_text", SysDictionary.PAYMENT_STATE_TEXT_MAP.get(pay.optString("payment_state")));
			}

			// 写日志pos_log
			posDishDao.savePosLog(tenancyId, storeId, posNum, optNum, optNum, shiftId, DateUtil.parseDate(reportDate), com.tzx.pos.base.Constant.TITLE, "结账", "账单编号:" + billNum + "; 应付金额:" + String.valueOf(paymentAmount), newstate.toString());

			StringBuilder qureyOrganSql = new StringBuilder("select format_state from organ where tenancy_id=? and id=?");
			rs = posDishDao.query4SqlRowSet(qureyOrganSql.toString(), new Object[]
			{ tenancyId, storeId });
			String formatState = null;
			if (rs.next())
			{
				formatState = rs.getString("format_state");
			}

			String mode = posDishDao.getSysParameter(tenancyId, storeId, "ZCDCMS");// 01,普通;02,自助;
			if (Tools.isNullOrEmpty(mode))
			{
				mode = "01";
			}

			if ("1".equals(formatState) && "01".equals(mode))
			{// 正餐 更新桌位 更改桌位的状态，将桌位状态改为"空闲"
				StringBuffer updateTableState = new StringBuffer("update pos_tablestate set state = ?,lock_pos_num = ?,lock_opt_num=? where table_code = ? and store_id = ? and tenancy_id = ?");
				posDishDao.update(updateTableState.toString(), new Object[]
				{ SysDictionary.TABLE_STATE_FREE, null, null, tableCode, storeId, tenancyId });
			}
			//GJ20160829
			if ("2".equals(formatState))
			{
				writeKvsBill(tenancyId, storeId, billNum, reportDate);
			}

			int printCount = Integer.parseInt(posDishDao.getSysParameter(tenancyId, storeId, "JZDDYSL"));

			// TODO
			printJson.put("tenancy_id", tenancyId);
			printJson.put("store_id", storeId);
			printJson.put("bill_num", billNum);
			printJson.put("pos_num", posNum);
			printJson.put("opt_num", optNum);
			printJson.put("report_date", reportDate);
			printJson.put("shift_id", shiftId);

			printJson.put("format_state", formatState);
			printJson.put("format_mode", mode);
			printJson.put("isprint", isPrint);
			printJson.put("print_count", printCount);
			
			if(Tools.hv(orderNum))
			{
				try
				{
					orderSyncService.sync(OrdersSyncService.POSTPAY_ORDER_COMPLETE, orderNum);
				}
				catch (Exception e)
				{
					// TODO Auto-generated catch block
					e.printStackTrace();
					logger.error("更新订单状态失败:", e);
				}
			}
		}
		else
		{
			String reSql = new String("select id,jzid,name,amount,count,coalesce(number,'') as number,coalesce(phone,'') as phone,shift_id,pos_num,cashier_num,last_updatetime,coalesce(customer_id,0) as customer_id,coalesce(bill_code,'') as bill_code,coalesce(remark,'') as remark,currency_amount,payment_state from pos_bill_payment where bill_num=? and store_id=?");
			paymentList = posDishDao.query4Json(tenancyId, reSql, new Object[]
			{ billNum, storeId });
			double amount =0d;
			for (JSONObject pay : paymentList)
			{
				pay.put("payment_state_text", SysDictionary.PAYMENT_STATE_TEXT_MAP.get(pay.optString("payment_state")));
				amount = DoubleHelper.add(amount, pay.optDouble("currency_amount"), DEFAULT_SCALE);
			}
			
			difference = DoubleHelper.sub(paymentAmount, amount, DEFAULT_SCALE);
		}

		JSONObject resultJson = new JSONObject();
		resultJson.put("bill_num", billNum);
		resultJson.put("table_code", tableCode);
		resultJson.put("payment_amount", paymentAmount);
		resultJson.put("discount_amount", discountAmount);
		resultJson.put("payment_state", paymentState);
		resultJson.put("payment_state_text", SysDictionary.PAYMENT_STATE_TEXT_MAP.get(paymentState));
		resultJson.put("difference", difference > 0 ? difference : 0d);
		resultJson.put("change", difference < 0 ? difference : 0d);
		resultJson.put("paymentlist", paymentList);
		return resultJson;
	}

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param chanel
	 * @param paymentAmount
	 * @throws Exception
	 */
	private void paymentDebited(String tenancyId, int storeId, String billNum, String chanel, double paymentAmount, List<JSONObject> payList, Data result) throws Exception
	{
		Timestamp currentTime = DateUtil.currentTimestamp();
		String currentTimeStr = DateUtil.format(currentTime);

		StringBuilder updateSql = new StringBuilder("update pos_bill_payment set number=?,bill_code=?,remark=?,payment_state=? where tenancy_id=? and store_id=? and id=?");

		for (JSONObject payItem : payList)
		{
			int payId = payItem.optInt("id");
			int jzid = payItem.optInt("jzid");
			String report_date = payItem.optString("report_date");
			Date reportDate = DateUtil.parseDate(report_date);
			int shiftId = payItem.optInt("shift_id");
			String optNum = payItem.optString("cashier_num");
			String posNum = payItem.optString("pos_num");
			String tableCode = payItem.optString("table_code");
			String paymentClass = payItem.optString("payment_class");
			String paymentName = payItem.optString("name");
			String paymentEnglishName = payItem.optString("name_english");
			double amount = payItem.optDouble("amount");
			double exchangeRate = payItem.optDouble("rate");
			double count = payItem.optDouble("count");
			double currency_amount = payItem.optDouble("currency_amount");
			int customer_id = payItem.optInt("customer_id");
			String number = payItem.optString("number");
			String phone = payItem.optString("phone");
			String remark = payItem.optString("remark");
			String is_ysk = Tools.hv(payItem.optString("is_ysk")) ? payItem.optString("is_ysk") : "N";
			String batch_num = payItem.optString("batch_num");
			
			String lastUpdatetimeStr = String.valueOf(DateUtil.formatTimestamp(payItem.optString("last_updatetime")).getTime());
			
			JSONObject paraJson = JSONObject.fromObject(payItem.optString("param_cach"));
			String isCheck = paraJson.optString("is_check");
			if (Tools.isNullOrEmpty(isCheck))
			{
				isCheck = payItem.optString("is_check");
			}

			String optName = posDishDao.getEmpNameById(optNum, tenancyId, storeId);

			if (SysDictionary.PAYMENT_CLASS_CARD.equals(paymentClass))
			{// 本系统卡 card
				JSONObject requestJson = new JSONObject();
				requestJson.put("card_code", paraJson.optString("number"));
				requestJson.put("third_code", paraJson.optString("third_code"));
				requestJson.put("cardpassword", paraJson.optString("password"));
				requestJson.put("consume_cardmoney", paraJson.optDouble("amount"));
				requestJson.put("consume_credit", paraJson.optDouble("consume_credit"));
				requestJson.put("consume_creditmoney", paraJson.optDouble("consume_creditmoney"));
				requestJson.put("consume_totalmoney", paymentAmount);
				requestJson.put("chanel", chanel);
				requestJson.put("bill_code", billNum);
				requestJson.put("updatetime", currentTimeStr);
				requestJson.put("business_date", report_date);// GJ20160408
				requestJson.put("shift_id", shiftId);
				requestJson.put("operator_id", optNum);
				requestJson.put("operator", optName);
				requestJson.put("dynamic_code", paraJson.optString("dynamic_code"));
				requestJson.put("pos_num",posNum);

				List<JSONObject> requestList = new ArrayList<JSONObject>();
				requestList.add(requestJson);

				Data requestData = Data.get(tenancyId, storeId, 0);
				requestData.setType(Type.CUSTOMER_CARD_CONSUME);
				requestData.setOper(Oper.add);
				requestData.setData(requestList);

				Data responseData = Data.get(requestData);
				customerService.customerConsumePost(JSONObject.fromObject(requestData).toString(), responseData);

				if (responseData.isSuccess())
				{
					JSONObject responseJson = JSONObject.fromObject(responseData.getData().get(0));
					posDishDao.update(updateSql.toString(), new Object[]
					{ responseJson.optString("card_code"), responseJson.optString("bill_code"), paraJson.optString("third_code"), SysDictionary.PAYMENT_STATE_PAY_COMPLETE, tenancyId, storeId, payId });

					posDishDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, amount, count, currency_amount, number, phone, is_ysk, null, responseJson.optString("bill_code"),
							remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batch_num, null, 0d);

					// 会员卡打印
					String printCode = "1010";
					String cardCode = responseJson.optString("card_code");
					String billCode = responseJson.optString("bill_code");
					String operator = responseJson.optString("operator");
					String updatetime = responseJson.optString("updatetime");
					double consume_cardmoney = responseJson.optDouble("consume_cardmoney");
					double main_trading = responseJson.optDouble("main_trading");
					double reward_trading = responseJson.optDouble("reward_trading");
					double main_balance = responseJson.optDouble("main_balance");
					double reward_balance = responseJson.optDouble("reward_balance");
					double total_main = responseJson.optDouble("total_main");
					double total_reward = responseJson.optDouble("total_reward");
					double credit = responseJson.optDouble("credit");
					double useful_credit = responseJson.optDouble("useful_credit");
					String card_class_name = responseJson.optString("card_class_name");
					String name = responseJson.optString("name");
					String mobil = responseJson.optString("mobil");
					double income = 0d;
					double deposit = 0d;
					double sales_price = 0d;

					posPrintService.customerPrint(tenancyId, storeId, posNum, operator, updatetime, printCode, cardCode, billCode, consume_cardmoney, main_trading, reward_trading, main_balance, reward_balance, total_main, total_reward, credit, useful_credit, income, deposit, sales_price,
							card_class_name, name, mobil);
				}
				else
				{
					if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
					{
						this.getPaymentException(result, Constant.CODE_CONN_EXCEPTION, Constant.CODE_CONN_EXCEPTION_MSG);
					}
					else
					{
						posDishDao.update(updateSql.toString(), new Object[]
						{ number, null, null, SysDictionary.PAYMENT_STATE_PAY_FAILURE, tenancyId, storeId, payId });

						posDishDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, amount, count, currency_amount, number, phone, is_ysk, null, null, remark, currentTime,
								SysDictionary.PAYMENT_STATE_PAY_FAILURE, batch_num, paraJson.toString(), 0d);

						this.getPaymentException(result, responseData.getCode(), responseData.getMsg());
					}
				}
			}
			if (SysDictionary.PAYMENT_CLASS_CARD_CREDIT.equals(paymentClass))
			{
				JSONObject requestJson = new JSONObject();

				requestJson.put("code", number);
				requestJson.put("mobil", phone);
				requestJson.put("credit", paraJson.optDouble("consume_credit"));
				requestJson.put("cash_money", currency_amount);
				requestJson.put("chanel", chanel);
				requestJson.put("bill_code", billNum);
				requestJson.put("operator", optName);
				requestJson.put("operator_id", optNum);
				requestJson.put("updatetime", currentTimeStr);
				requestJson.put("business_date", report_date);
				requestJson.put("shift_id", shiftId);
				requestJson.put("batch_no", batch_num+"_"+lastUpdatetimeStr);

				List<JSONObject> requestList = new ArrayList<JSONObject>();
				requestList.add(requestJson);

				Data requestData = Data.get(tenancyId, storeId, 0);
				requestData.setType(Type.CUSTOMER_CREDIT_COUNSUME);
				requestData.setOper(Oper.add);
				requestData.setData(requestList);

				Data responseData = Data.get(requestData);
				customerService.commonPost(JSONObject.fromObject(requestData).toString(), responseData);

				if (responseData.isSuccess())
				{
					JSONObject responseJson = JSONObject.fromObject(responseData.getData().get(0));
					posDishDao.update(updateSql.toString(), new Object[]
					{ number, responseJson.optString("bill_code"), null, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, tenancyId, storeId, payId });

					posDishDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, amount, count, currency_amount, number, phone, is_ysk, null, responseJson.optString("bill_code"), remark, currentTime,
							SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batch_num, null, 0d);
					
					posDishDao.updatePosBillMember(tenancyId, storeId, billNum, SysDictionary.BILL_MEMBERCARD_JZ03, currency_amount, responseJson.optDouble("credit"),responseJson.optString("bill_code"),SysDictionary.REQUEST_STATUS_COMPLETE,responseJson.optString("name"),DoubleHelper.sub(responseJson.optDouble("useful_credit"), responseJson.optDouble("credit"), DEFAULT_SCALE),responseJson.optDouble("useful_credit"));
				}
				else
				{
					if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
					{
						this.getPaymentException(result, Constant.CODE_CONN_EXCEPTION, Constant.CODE_CONN_EXCEPTION_MSG);
					}
					else
					{
						posDishDao.update(updateSql.toString(), new Object[]
						{ number, null, null, SysDictionary.PAYMENT_STATE_PAY_FAILURE, tenancyId, storeId, payId });

						posDishDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, amount, count, currency_amount, number, phone, is_ysk, null, null, remark, currentTime,
								SysDictionary.PAYMENT_STATE_PAY_FAILURE, batch_num, paraJson.toString(), 0d);

						this.getPaymentException(result, responseData.getCode(), responseData.getMsg());
					}
				}
			}
			else if (SysDictionary.PAYMENT_CLASS_COUPONS.equals(paymentClass))
			{// 优惠券 coupons
				if ("1".equals(isCheck) || !StringUtils.isEmpty(number))
				{
					JSONObject coupons = new JSONObject();
					coupons.put("coupons_code", number);

					List<JSONObject> couponsList = new ArrayList<JSONObject>();
					couponsList.add(coupons);

					JSONObject requestJson = new JSONObject();
					requestJson.put("couponslist", couponsList);
					requestJson.put("chanel", chanel);
					requestJson.put("bill_money", paymentAmount);
					requestJson.put("bill_code", billNum);
					requestJson.put("updatetime", currentTimeStr);
					requestJson.put("business_date", report_date);// GJ20160408
					requestJson.put("shift_id", shiftId);
					requestJson.put("operator_id", optNum);
					requestJson.put("operator", optName);

					String billDetailsSql = " select a.item_id,a.item_unit_id,a.item_price,a.item_count,a.real_amount,a.item_property,a.discount_rate,a.discountr_amount "
							+ " from pos_bill_item a,pos_bill b where a.tenancy_id=? and a.store_id=? and a.bill_num=? and a.item_property=? and a.bill_num = b.bill_num and b.bill_property=?  and (a.item_remark is null or a.item_remark='') ";
					List<JSONObject> billDetailsList = posDishDao.query4Json(tenancyId, billDetailsSql, new Object[]
					{ tenancyId, storeId, billNum, SysDictionary.ITEM_PROPERTY_SINGLE, SysDictionary.BILL_PROPERTY_OPEN });
					requestJson.put("billdetails", billDetailsList);

					List<JSONObject> requestList = new ArrayList<JSONObject>();
					requestList.add(requestJson);

					Data requestData = Data.get(tenancyId, storeId, 0);

					requestData.setType(Type.NEW_COUPONS);
					requestData.setOper(Oper.update);
					requestData.setData(requestList);

					Data responseData = Data.get(requestData);
					customerService.commonPost(JSONObject.fromObject(requestData).toString(), responseData);

					if (responseData.isSuccess())
					{
						posDishDao.update(updateSql.toString(), new Object[]
						{ number, null, null, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, tenancyId, storeId, payId });

						posDishDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, amount, count, currency_amount, number, phone, is_ysk, null, null, remark, currentTime,
								SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batch_num, null, 0d);
					}
					else
					{
						if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
						{
							this.getPaymentException(result, Constant.CODE_CONN_EXCEPTION, Constant.CODE_CONN_EXCEPTION_MSG);
						}
						else
						{
							posDishDao.update(updateSql.toString(), new Object[]
							{ number, null, null, SysDictionary.PAYMENT_STATE_PAY_FAILURE, tenancyId, storeId, payId });

							posDishDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, amount, count, currency_amount, number, phone, is_ysk, null, null, remark, currentTime,
									SysDictionary.PAYMENT_STATE_PAY_FAILURE, batch_num, paraJson.toString(), 0d);

							this.getPaymentException(result, responseData.getCode(), responseData.getMsg());
						}
					}
				}
			}
			else if (SysDictionary.PAYMENT_CLASS_INCORPORATION.equals(paymentClass))
			{// 团体挂账 incorporation
				// JSONObject para =
				// JSONObject.fromObject(payItem.optString("param_cach"));

				JSONObject requestJson = new JSONObject();
				requestJson.put("incorporation_id", number);
				requestJson.put("gz_money", amount);
				requestJson.put("bill_code", billNum);
				requestJson.put("bill_money", paymentAmount);
				requestJson.put("customer_id", customer_id);
				requestJson.put("password", paraJson.optString("password"));
				requestJson.put("operate_time", currentTimeStr);
				requestJson.put("business_date", report_date);// GJ20160408
				requestJson.put("shift_id", shiftId);
				requestJson.put("operator_id", optNum);
				requestJson.put("operator", optName);

				List<JSONObject> requestList = new ArrayList<JSONObject>();
				requestList.add(requestJson);

				Data requestData = Data.get(tenancyId, storeId, 0);
				requestData.setType(Type.INCORPORATION_GZ);
				requestData.setOper(Oper.add);
				requestData.setData(requestList);

				Data responseData = Data.get(requestData);
				customerService.commonPost(JSONObject.fromObject(requestData).toString(), responseData);

				if (responseData.isSuccess())
				{
					posDishDao.update(updateSql.toString(), new Object[]
					{ number, null, null, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, tenancyId, storeId, payId });

					posDishDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, amount, count, currency_amount, number, phone, is_ysk, customer_id, null, remark, currentTime,
							SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batch_num, null, 0d);
				}
				else
				{
					if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
					{
						this.getPaymentException(result, Constant.CODE_CONN_EXCEPTION, Constant.CODE_CONN_EXCEPTION_MSG);
					}
					else
					{
						posDishDao.update(updateSql.toString(), new Object[]
						{ number, null, null, SysDictionary.PAYMENT_STATE_PAY_FAILURE, tenancyId, storeId, payId });

						posDishDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, amount, count, currency_amount, number, phone, is_ysk, customer_id, null, remark, currentTime,
								SysDictionary.PAYMENT_STATE_PAY_FAILURE, batch_num, paraJson.toString(), 0d);

						this.getPaymentException(result, responseData.getCode(), responseData.getMsg());
					}
				}
			}
		}
	}

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param chanel
	 * @param paymentAmount
	 * @throws Exception
	 */
	@SuppressWarnings("unused")
	private void paymentRefund(String tenancyId, int storeId, String billNum, String chanel, double paymentAmount) throws Exception
	{
		String currentTime = DateUtil.getNowDateYYDDMMHHMMSS();
		StringBuilder selectSql = new StringBuilder("select bp.*,pw.payment_class from pos_bill_payment bp left join payment_way pw on bp.jzid=pw.id where bp.bill_num =? and bp.store_id = ? and bp.tenancy_id=? and bp.payment_state=?");
		List<JSONObject> payList = posDishDao.query4Json(tenancyId, selectSql.toString(), new Object[]
		{ billNum, storeId, tenancyId, SysDictionary.PAYMENT_STATE_PAY_COMPLETE });

		for (JSONObject payItem : payList)
		{
			if (SysDictionary.PAYMENT_CLASS_CARD.equals(payItem.optString("payment_class")))
			{// 本系统卡 card
				JSONObject paraJson = JSONObject.fromObject(payItem.optString("param_cach"));

				String optNum = payItem.optString("cashier_num");
				String optName = posDishDao.getEmpNameById(optNum, tenancyId, storeId);
				
				JSONObject requestJson = new JSONObject();
				requestJson.put("card_code", paraJson.optString("number"));
				requestJson.put("third_code", paraJson.optString("third_code"));
				requestJson.put("old_bill_code", payItem.optString("bill_code"));
				requestJson.put("bill_code", billNum);
				requestJson.put("chanel", chanel);
				requestJson.put("updatetime", currentTime);
				requestJson.put("business_date", payItem.optString("report_date"));// GJ20160408
				requestJson.put("shift_id", payItem.optInt("shift_id"));
				requestJson.put("operator_id", optNum);
				requestJson.put("operator", optName);
				requestJson.put("pos_num", payItem.optString("pos_num"));
				
				List<JSONObject> requestList = new ArrayList<JSONObject>();
				requestList.add(requestJson);

				Data requestData = Data.get(tenancyId, storeId, 0);
				requestData.setType(Type.CUSTOMER_CARD_CONSUME);
				requestData.setOper(Oper.update);
				requestData.setData(requestList);

				Data responseData = Data.get(requestData);
				customerService.cancelCardConsume(requestData, responseData,"N");

				JSONObject responseJson = JSONObject.fromObject(responseData.getData().get(0));
				if (responseData.isSuccess())
				{
					// TODO Auto-generated method stub
					String printCode = "1011";
					String posNum = payItem.optString("pos_num");
					String cardCode = responseJson.optString("card_code");
					String billCode = responseJson.optString("bill_code");
					String operator = responseJson.optString("operator");
					String updatetime = responseJson.optString("updatetime");
					double consume_cardmoney = responseJson.optDouble("consume_cardmoney");
					double main_trading = responseJson.optDouble("main_trading");
					double reward_trading = responseJson.optDouble("reward_trading");
					double main_balance = responseJson.optDouble("main_balance");
					double reward_balance = responseJson.optDouble("reward_balance");
					double total_main = responseJson.optDouble("total_main");
					double total_reward = responseJson.optDouble("total_reward");
					double credit = responseJson.optDouble("credit");
					double useful_credit = responseJson.optDouble("useful_credit");
					String card_class_name = responseJson.optString("card_class_name");
					String name = responseJson.optString("name");
					String mobil = responseJson.optString("mobil");
					double income = 0d;
					double deposit = 0d;
					double sales_price = 0d;

					posPrintService.customerPrint(tenancyId, storeId, posNum, operator, updatetime, printCode, cardCode, billCode, consume_cardmoney, main_trading, reward_trading, main_balance, reward_balance, total_main, total_reward, credit, useful_credit, income, deposit, sales_price, card_class_name, name, mobil);
				}
			}
			else if (SysDictionary.PAYMENT_CLASS_COUPONS.equals(payItem.optString("payment_class")))
			{// 优惠券 coupons
				JSONObject coupons = new JSONObject();
				coupons.put("coupons_code", payItem.optString("number"));
				List<JSONObject> couponsList = new ArrayList<JSONObject>();
				couponsList.add(coupons);

				JSONObject paraJson = new JSONObject();
				paraJson.put("chanel", chanel);
				paraJson.put("couponslist", couponsList);
				List<JSONObject> requestList = new ArrayList<JSONObject>();
				requestList.add(paraJson);

				Data requestData = Data.get(tenancyId, storeId, 0);
				requestData.setType(Type.COUPONS);
				requestData.setOper(Oper.init);
				requestData.setData(requestList);

				Data responseData = Data.get(requestData);
				customerService.commonPost(JSONObject.fromObject(requestData).toString(), responseData);

			}
			else if (SysDictionary.PAYMENT_CLASS_INCORPORATION.equals(payItem.optString("payment_class")))
			{// 团体挂账 incorporation
				JSONObject paraJson = new JSONObject();
				paraJson.put("incorporation_id", payItem.optString("number"));
				paraJson.put("gz_money", payItem.optDouble("amount"));
				paraJson.put("bill_code", billNum);
				paraJson.put("bill_money", paymentAmount);
				paraJson.put("customer_id", payItem.optString("customer_id"));
				paraJson.put("operate_time", currentTime);
				paraJson.put("operator", payItem.optString("cashier_num"));

				List<JSONObject> requestList = new ArrayList<JSONObject>();
				requestList.add(paraJson);

				Data requestData = Data.get(tenancyId, storeId, 0);
				requestData.setType(Type.INCORPORATION_GZ);
				requestData.setOper(Oper.update);
				requestData.setData(requestList);

				Data responseData = Data.get(requestData);
				customerService.commonPost(JSONObject.fromObject(requestData).toString(), responseData);
			}
		}

	}

	/**
	 * 微信支付状态查询
	 * 
	 * @param paramData
	 * @throws Exception
	 */
	private List<JSONObject> paymentOrderQuery(Data paramData) throws Exception
	{
		@SuppressWarnings("unchecked")
		List<JSONObject> list = (List<JSONObject>) paramData.getData();
		JSONObject param = JSONObject.fromObject(list.get(0));
		List<JSONObject> response = new ArrayList<JSONObject>();

		// 检验6个参数不为空
		if (param == null || param.size() < 6)
		{
			JSONObject errorJson = new JSONObject();
			errorJson.put("success", false);
			errorJson.put("error_msg", "参数不全，请参照文档");
			response.add(errorJson);
			return response;
		}
		// 获取轮查的时间间隔和次数
		int queryPeriod = 5;
		int queryTime = 20;
		if (param.containsKey("query_period") && param.containsKey("query_time"))
		{
			queryPeriod = param.optInt("query_period");
			queryTime = param.optInt("query_time");
			param.put("query_period", queryPeriod);
			param.put("query_time", queryTime);
		}
		else
		{
			String sql = "select para_value from sys_parameter where para_code in ('dsffkztjcjgsj','dsffkztlxcs') order by para_code";
			List<JSONObject> gapList = posDishDao.query4Json(paramData.getTenancy_id(), sql);
			if (gapList != null && gapList.size() == 2)
			{
				queryPeriod = gapList.get(0).getInt("para_value");
				queryTime = gapList.get(1).getInt("para_value");
			}
		}
		list.remove(0);
		list.add(param);
		paramData.setData(list);

		String paramStr = JSON.toJSONString(paramData);
		String reqURL = com.tzx.framework.common.constant.Constant.systemMap.get("pos_payment_wechat_url");

		// 超时时间设置(在其轮询时间的基础上添加100s)
		int timeout = (queryPeriod * queryTime + 100) * 1000;
		String result = HttpUtil.sendPostRequest(reqURL, paramStr, timeout, timeout);

		if (result != null)
		{
			try
			{
				JSONObject json = JSONObject.fromObject(result);
				boolean success = json.optBoolean("success");
				if (success)
				{
					String status = json.getString("status");
					if ("finished".equals(status))
					{
						// 请求完成、删除订单二维码图片
						String imgPath = PathUtil.getWebRootPath() + File.separator + "img" + File.separator + "qr_code" + File.separator + "wechat" + File.separator;
						String fileName = paramData.getTenancy_id() + "_" + paramData.getStore_id() + "_" + param.getString("bill_num") + ".png";
						imgPath += fileName;
						PathUtil.deleteFile(imgPath);
						// 完成订单后，关闭订单结账
						// this.finishedCloseOrder(data, json);
					}
				}
				response.add(json);
				return response;
			}
			catch (Exception e)
			{
				logger.error(e.getMessage());
				JSONObject json = getErrorReturn(paramData.getTenancy_id(), paramData.getStore_id(), param.getString("bill_num"));
				json.put("exception", e.getMessage());
				response.add(json);
			}
		}
		else
		{
			JSONObject json = getErrorReturn(paramData.getTenancy_id(), paramData.getStore_id(), param.getString("bill_num"));
			json.put("exception", "查询接口返回结果为null");
			response.add(json);
		}
		return response;
	}

	/**
	 * 支付宝付款状态查询
	 * 
	 * @throws Exception
	 */
	private List<JSONObject> paymentQuery(String tenancyId, int storeId, JSONObject param) throws Exception
	{
		List<JSONObject> response = new ArrayList<JSONObject>();
		// 检验6个参数不为空
		if (param == null || param.size() < 6)
		{
			JSONObject errorJson = new JSONObject();
			errorJson.put("success", false);
			errorJson.put("error_msg", "参数不全，请参照文档");
			response.add(errorJson);
			return response;
		}

		Map<String, Object> params = new HashMap<String, Object>();
		params.put("oper", Oper.query);
		params.put("tenantId", tenancyId);
		params.put("store_id", storeId);
		params.put("bill_num", param.getString("bill_num"));

		// 是否进行轮询查询的字段
		Map<String, Object> map = new HashMap<String, Object>();
		if (param.containsKey("polling_flag"))
		{
			map.put("polling_flag", param.getBoolean("polling_flag"));
		}
		// 获取轮查的时间间隔和次数
		map.putAll(this.getQueryPara(tenancyId, param));
		params.put("json", map);

		int queryPeriod = Integer.parseInt(map.get("query_period").toString());
		int queryTime = Integer.parseInt(map.get("query_time").toString());

		String paramStr = JSON.toJSONString(params);
		String reqURL = com.tzx.framework.common.constant.Constant.systemMap.get("pos_payment_url");

		// 超时时间设置(在其轮询时间的基础上添加100s)
		int timeout = (queryPeriod * queryTime + 100) * 1000;
		String result = HttpUtil.sendPostRequest(reqURL, paramStr, timeout, timeout);
		if (result != null)
		{
			try
			{
				JSONObject json = JSONObject.fromObject(result);
				boolean success = json.optBoolean("success");
				if (success)
				{
					String status = json.getString("status");
					if ("finished".equals(status))
					{
						// 请求完成、删除订单二维码图片
						String imgPath = PathUtil.getWebRootPath() + File.separator + "img" + File.separator + "qr_code" + File.separator;
						String fileName = tenancyId + "_" + storeId + "_" + param.getString("bill_num") + ".png";
						imgPath += fileName;
						PathUtil.deleteFile(imgPath);
						// 结账完成订单
						// this.finishedCloseOrder(data,json);
					}
				}
				response.add(json);
				return response;
			}
			catch (Exception e)
			{
				logger.error(e.getMessage());
				JSONObject json = getErrorReturn(tenancyId, storeId, param.getString("bill_num"));
				json.put("exception", e.getMessage());
				response.add(json);
			}
		}
		else
		{
			JSONObject json = getErrorReturn(tenancyId, storeId, param.getString("bill_num"));
			json.put("exception", "查询接口返回结果为null");
			response.add(json);
		}
		return response;
	}

	@Override
	public void queryBillPayment(Data param, Data result, JSONObject printJson) throws Exception
	{
		String tenancyId = param.getTenancy_id();
		int storeId = param.getStore_id();
		List<?> paramList = param.getData();
		if (paramList == null || paramList.isEmpty())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}
		JSONObject para = JSONObject.fromObject(paramList.get(0));
		String report_date = ParamUtil.getDateStringValue(para, "report_date");
		Date reportDate = DateUtil.parseDate(report_date);
		String billNum = para.optString("bill_num");
		int shiftId = para.optInt("shift_id");
		String posNum = para.optString("pos_num");
		String optNum = para.optString("opt_num");
		String isprint_bill = para.optString("isprint_bill");
		if (Tools.isNullOrEmpty(isprint_bill))
		{
			isprint_bill = "N";
		}

		if (Tools.isNullOrEmpty(reportDate))
		{
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_REPORT_DATE);
		}
		if (Tools.isNullOrEmpty(billNum))
		{
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_BILL_NUM);
		}

		Timestamp currentTime = DateUtil.currentTimestamp();
		
		StringBuilder qureyBillSql = new StringBuilder("select coalesce(payment_amount,0) as payment_amount,coalesce(discount_amount,0) as discount_amount,payment_state,table_code,bill_property from pos_bill where tenancy_id=? and store_id=? and bill_num=?");
		SqlRowSet rs = posDishDao.query4SqlRowSet(qureyBillSql.toString(), new Object[]
		{ tenancyId, storeId, billNum });

		double paymentAmount = 0d;
		double discountAmount =0d;
		String paymentState = null;
		String tableCode = null;
		String billProperty=null;
		if (rs.next())
		{
			paymentAmount = rs.getDouble("payment_amount");
			discountAmount = rs.getDouble("discount_amount");
			paymentState = rs.getString("payment_state");
			tableCode = rs.getString("table_code");
			billProperty = rs.getString("bill_property");
		}
		
		if(SysDictionary.BILL_PROPERTY_CLOSED.equals(billProperty))
		{
			String reSql = new String("select id,jzid,name,amount,count,coalesce(number,'') as number,coalesce(phone,'') as phone,shift_id,pos_num,cashier_num,last_updatetime,coalesce(customer_id,0) as customer_id,coalesce(bill_code,'') as bill_code,coalesce(remark,'') as remark,currency_amount,payment_state from pos_bill_payment where bill_num=? and store_id=?");
			List<JSONObject> paymentList = posDishDao.query4Json(tenancyId, reSql, new Object[]
			{ billNum, storeId });
			double amount =0d;
			for (JSONObject pay : paymentList)
			{
				pay.put("payment_state_text", SysDictionary.PAYMENT_STATE_TEXT_MAP.get(pay.optString("payment_state")));
				amount = DoubleHelper.add(amount, pay.optDouble("currency_amount"), DEFAULT_SCALE);
			}
			
			double difference = DoubleHelper.sub(paymentAmount, amount, DEFAULT_SCALE);
			JSONObject resultJson = new JSONObject();
			resultJson.put("bill_num", billNum);
			resultJson.put("table_code", tableCode);
			resultJson.put("payment_amount", paymentAmount);
			resultJson.put("discount_amount", discountAmount);
			resultJson.put("payment_state", paymentState);
			resultJson.put("payment_state_text", SysDictionary.PAYMENT_STATE_TEXT_MAP.get(paymentState));
			resultJson.put("difference", difference > 0 ? difference : 0d);
			resultJson.put("change", difference < 0 ? difference : 0d);
			resultJson.put("paymentlist", paymentList);
			
			List<JSONObject> resultList = new ArrayList<JSONObject>();
			resultList.add(resultJson);
			result.setData(resultList);
			
			result.setCode(PosErrorCode.BILL_CLOSED.getNumber());
			result.setMsg(PosErrorCode.BILL_CLOSED.getMessage());
			return;
		}
		
		StringBuilder updatePaymentSql = new StringBuilder("update pos_bill_payment set payment_state=?,bill_code=? where tenancy_id=? and store_id=? and bill_num=? and id=?");

//		StringBuilder insertPaymentLogSql = new StringBuilder(
//				"insert into pos_bill_payment_log(tenancy_id,store_id,bill_num,report_date,shift_id,pos_num,cashier_num,table_code,type,jzid,name,name_english,rate,amount,count,currency_amount,number,phone,is_ysk,customer_id,bill_code,remark,last_updatetime,upload_tag,payment_state) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,'0',?)");

		StringBuilder queryPaymentSql = new StringBuilder("select bp.*,pw.payment_class from pos_bill_payment bp left join payment_way pw on bp.tenancy_id=pw.tenancy_id and bp.jzid=pw.id where bp.tenancy_id=? and bp.store_id=? and bp.bill_num=? and bp.payment_state=?");
		rs = posDishDao.query4SqlRowSet(queryPaymentSql.toString(), new Object[]
		{ tenancyId, storeId, billNum, SysDictionary.PAYMENT_STATE_PAY });
		while (rs.next())
		{
//			String tableCode = rs.getString("table_code");
			int payId = rs.getInt("id");
			int jzid = rs.getInt("jzid");
			String paymentClass = rs.getString("payment_class");
			String paymentName = rs.getString("name");
			String paymentEnglishName = rs.getString("name_english");
			double exchangeRate = rs.getDouble("rate");
			double pay_amount = rs.getDouble("amount");
			double pay_count = rs.getDouble("count");
			double currency_amount = rs.getDouble("currency_amount");
			String number = rs.getString("number");
			String phone = rs.getString("phone");
			int customer_id = rs.getInt("customer_id");
			String remark = rs.getString("remark");
			String batch_num = rs.getString("batch_num");
			
			String lastUpdatetimeStr = String.valueOf(DateUtil.formatTimestamp(rs.getString("last_updatetime")).getTime());
			
			String is_ysk = Tools.hv(rs.getString("is_ysk")) ? rs.getString("is_ysk") : "N";
			
			JSONObject cachParamJson = JSONObject.fromObject(rs.getString("param_cach")); 
			
			if (SysDictionary.PAYMENT_CLASS_CARD.equals(paymentClass))
			{// 本系统卡 card
				JSONObject requestJson = new JSONObject();
				if(Tools.hv(number))
				{
					requestJson.put("card_code", number);
				}
				else
				{
					requestJson.put("card_code", cachParamJson.optString("third_code"));
				}
				requestJson.put("third_code", cachParamJson.optString("third_code"));
				requestJson.put("third_bill_code", billNum);

				List<JSONObject> requestList = new ArrayList<JSONObject>();
				requestList.add(requestJson);

				Data requestData = Data.get(tenancyId, storeId, 0);
				requestData.setType(Type.CUSTOMER_BILL);
				requestData.setOper(Oper.find);
				requestData.setData(requestList);

				Data responseData = Data.get(requestData);
				customerService.commonPost(JSONObject.fromObject(requestData).toString(), responseData);
				if (responseData.isSuccess())
				{
					JSONObject responseJson = JSONObject.fromObject(responseData.getData().get(0));
					posDishDao.update(updatePaymentSql.toString(), new Object[]
					{ SysDictionary.PAYMENT_STATE_PAY_COMPLETE, responseJson.optString("bill_code"), tenancyId, storeId, billNum, payId });

					posDishDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, null,
							responseJson.optString("bill_code"), remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batch_num, null, 0d);

//					resultJson.put("credit", responseJson.optDouble("credit"));
//					resultJson.put("main_balance", responseJson.optDouble("main_balance"));
//					resultJson.put("reward_balance", responseJson.optDouble("reward_balance"));
					
					// TODO Auto-generated method stub
					String printCode = "1010";
					// String posNum = rs.getString("pos_num");
					String cardCode = responseJson.optString("card_code");
					String billCode = responseJson.optString("bill_code");
					String updatetime = responseJson.optString("operate_time");
					double main_trading = responseJson.optDouble("main_trading");
					double reward_trading = responseJson.optDouble("reward_trading");
					double consume_cardmoney = DoubleHelper.add(main_trading, reward_trading, PAYMENT_SCALE);
					double main_balance = DoubleHelper.sub(responseJson.optDouble("main_balance"), main_trading, PAYMENT_SCALE);
					double reward_balance = DoubleHelper.sub(responseJson.optDouble("reward_balance"), reward_trading, PAYMENT_SCALE);
					double total_main = responseJson.optDouble("total_main");
					double total_reward = responseJson.optDouble("total_reward");
					double credit = responseJson.optDouble("credit");
					double useful_credit = responseJson.optDouble("useful_credit");
					String card_class_name = responseJson.optString("card_class_name");
					String name = responseJson.optString("name");
					String mobil = responseJson.optString("mobil");
					double income = 0d;
					double deposit = 0d;
					double sales_price = 0d;

					String operator = posDishDao.getEmpNameById(optNum, tenancyId, storeId);
					posPrintService.customerPrint(tenancyId, storeId, posNum, operator, updatetime, printCode, cardCode, billCode, consume_cardmoney, main_trading, reward_trading, main_balance, reward_balance, total_main, total_reward, credit, useful_credit, income, deposit, sales_price, card_class_name, name, mobil);
				}
				else
				{
					if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
					{
						this.getPaymentException(result, Constant.CODE_CONN_EXCEPTION, Constant.CODE_CONN_EXCEPTION_MSG);
					}
					else
					{
						posDishDao.update(updatePaymentSql.toString(), new Object[]
						{ SysDictionary.PAYMENT_STATE_PAY_FAILURE, null, tenancyId, storeId, billNum, payId });

						posDishDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, null, null, remark, currentTime,
								SysDictionary.PAYMENT_STATE_PAY_FAILURE, batch_num, cachParamJson.toString(), 0d);

						this.getPaymentException(result, responseData.getCode(), responseData.getMsg());
					}
				}
			}
			if (SysDictionary.PAYMENT_CLASS_CARD_CREDIT.equals(paymentClass))
			{
				JSONObject requestJson = new JSONObject();
				
				requestJson.put("code", number);
				requestJson.put("mobil", phone);
				requestJson.put("wechat", "");
				requestJson.put("bill_code",billNum);
				requestJson.put("shift_id", shiftId);
				requestJson.put("batch_no", batch_num+"_"+lastUpdatetimeStr);
				requestJson.put("type", "0");

				List<JSONObject> requestList = new ArrayList<JSONObject>();
				requestList.add(requestJson);

				Data requestData = Data.get(tenancyId, storeId, 0);
				requestData.setType(Type.CUSTOMER_CREDIT_COUNSUME);
				requestData.setOper(Oper.check);
				requestData.setData(requestList);

				Data responseData = Data.get(requestData);
				customerService.commonPost(JSONObject.fromObject(requestData).toString(), responseData);
				
				if (responseData.isSuccess())
				{
					JSONObject responseJson = JSONObject.fromObject(responseData.getData().get(0));
					posDishDao.update(updatePaymentSql.toString(), new Object[]
					{ SysDictionary.PAYMENT_STATE_PAY_COMPLETE, responseJson.optString("bill_code"), tenancyId, storeId, billNum, payId });

					posDishDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, null, responseJson.optString("bill_code"), remark, currentTime,
							SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batch_num, null, 0d);
					
					posDishDao.updatePosBillMember(tenancyId, storeId, billNum, SysDictionary.BILL_MEMBERCARD_JZ03, currency_amount, responseJson.optDouble("credit"),responseJson.optString("bill_code"),SysDictionary.REQUEST_STATUS_COMPLETE,responseJson.optString("credit"),DoubleHelper.sub(responseJson.optDouble("useful_credit"), responseJson.optDouble("credit"), DEFAULT_SCALE),responseJson.optDouble("useful_credit"));
				}
				else
				{
					if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
					{
						this.getPaymentException(result, Constant.CODE_CONN_EXCEPTION, Constant.CODE_CONN_EXCEPTION_MSG);
					}
					else
					{
						posDishDao.update(updatePaymentSql.toString(), new Object[]
						{ SysDictionary.PAYMENT_STATE_PAY_FAILURE, null, tenancyId, storeId, billNum, payId });

						posDishDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, null, null, remark, currentTime,
								SysDictionary.PAYMENT_STATE_PAY_FAILURE, batch_num, cachParamJson.toString(), 0d);

						this.getPaymentException(result, responseData.getCode(), responseData.getMsg());
					}
				}
			}
			else if (SysDictionary.PAYMENT_CLASS_COUPONS.equals(paymentClass))
			{// 优惠券 coupons
				StringBuilder billAmountSql = new StringBuilder("select b.source from pos_bill b where b.bill_num = ? and b.store_id = ?");
				SqlRowSet rsb = posDishDao.query4SqlRowSet(billAmountSql.toString(), new Object[]
				{ billNum, storeId });
				String chanel = SysDictionary.CHANEL_MD01;
				if (rsb.next())
				{
					chanel = rsb.getString("source");
				}

				JSONObject coupons = new JSONObject();
				coupons.put("coupons_code", number);

				List<JSONObject> couponsList = new ArrayList<JSONObject>();
				couponsList.add(coupons);

				JSONObject paraJson = new JSONObject();
				paraJson.put("chanel", chanel);
				paraJson.put("bill_money", pay_amount);
				paraJson.put("couponslist", couponsList);

				List<JSONObject> requestList = new ArrayList<JSONObject>();
				requestList.add(paraJson);

				Data requestData = Data.get(tenancyId, storeId, 0);
				requestData.setType(Type.COUPONS);
				requestData.setOper(Oper.check);
				requestData.setData(requestList);

				Data responseData = Data.get(requestData);
				customerService.commonPost(JSONObject.fromObject(requestData).toString(), responseData);
				
				if(CrmErrorCode.COUPONS_CODE_USED.getNumber() == responseData.getCode())
				{
					posDishDao.update(updatePaymentSql.toString(), new Object[]
					{ SysDictionary.PAYMENT_STATE_PAY_COMPLETE, null, tenancyId, storeId, billNum, payId });

					posDishDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, null, null, remark, currentTime,
							SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batch_num, null, 0d);
				}
				else if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
				{
					this.getPaymentException(result, responseData.getCode(), responseData.getMsg());
				}
				else
				{
					posDishDao.update(updatePaymentSql.toString(), new Object[]
					{ SysDictionary.PAYMENT_STATE_PAY_FAILURE, null, tenancyId, storeId, billNum, payId });

					posDishDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, null, null, remark, currentTime,
							SysDictionary.PAYMENT_STATE_PAY_FAILURE, batch_num, cachParamJson.toString(), 0d);

					if (Constant.CODE_SUCCESS == responseData.getCode())
					{
						this.getPaymentException(result, PosErrorCode.BILL_PAYMENT_ERROP.getNumber(), "优惠劵使用失败");
					}
					else
					{
						this.getPaymentException(result, responseData.getCode(), responseData.getMsg());
					}
				}
			}
			else if (SysDictionary.PAYMENT_CLASS_INCORPORATION.equals(paymentClass))
			{// 团体挂账 incorporation
				JSONObject requestJson = new JSONObject();
				requestJson.put("incorporation_id", number);
				requestJson.put("customer_id", customer_id);
				requestJson.put("bill_code", billNum);

				List<JSONObject> requestList = new ArrayList<JSONObject>();
				requestList.add(requestJson);

				Data requestData = Data.get(tenancyId, storeId, 0);
				requestData.setType(Type.INCORPORATION_GZ);
				requestData.setOper(Oper.find);
				requestData.setData(requestList);

				Data responseData = Data.get(requestData);
				customerService.commonPost(JSONObject.fromObject(requestData).toString(), responseData);
				if (responseData.isSuccess())
				{
//					JSONObject responseJson = JSONObject.fromObject(responseData.getData().get(0));
					posDishDao.update(updatePaymentSql.toString(), new Object[]
					{ SysDictionary.PAYMENT_STATE_PAY_COMPLETE, null, tenancyId, storeId, billNum, payId });

					posDishDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, customer_id,
							null, remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batch_num, null, 0d);
				}
				else
				{
					if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
					{
						this.getPaymentException(result, responseData.getCode(), responseData.getMsg());
					}
					else
					{
						posDishDao.update(updatePaymentSql.toString(), new Object[]
						{ SysDictionary.PAYMENT_STATE_PAY_FAILURE, null, tenancyId, storeId, billNum, payId });

						posDishDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, customer_id, null, remark,
								currentTime, SysDictionary.PAYMENT_STATE_PAY_FAILURE, batch_num, cachParamJson.toString(), 0d);

						this.getPaymentException(result, responseData.getCode(), responseData.getMsg());
					}
				}
			}
			else if (SysDictionary.PAYMENT_CLASS_WECHAT_PAY.equals(paymentClass))
			{// 微信支付 wechat_pay
				JSONObject requestJson = new JSONObject();
				requestJson.put("bill_num", billNum);
				requestJson.put("pos_num", posNum);
				requestJson.put("opt_num", optNum);
				requestJson.put("report_date", report_date);
				requestJson.put("shift_id", shiftId);
				requestJson.put("jzid", jzid);
				requestJson.put("polling_flag", false);

				List<JSONObject> requestList = new ArrayList<JSONObject>();
				requestList.add(requestJson);

				Data requestData = Data.get(tenancyId, storeId, 0);
				requestData.setType(Type.PAYMENT_ORDERQUERY);
				requestData.setOper(Oper.query);
				requestData.setData(requestList);

				List<JSONObject> responseList = this.paymentOrderQuery(requestData);
				JSONObject responseJson = JSONObject.fromObject(responseList.get(0));
				if (responseJson.optBoolean("success") && "finished".equals(responseJson.optString("status")))
				{
					posDishDao.update(updatePaymentSql.toString(), new Object[]
					{ SysDictionary.PAYMENT_STATE_PAY_COMPLETE, responseJson.optString("trade_no"), tenancyId, storeId, billNum, payId });

					posDishDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, null,
							responseJson.optString("trade_no"), remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batch_num, null, 0d);
				}
				else if (!responseJson.optBoolean("success"))
				{
					this.getPaymentException(result, Constant.CODE_CONN_EXCEPTION, Constant.CODE_CONN_EXCEPTION_MSG);
				}
				else
				{
					posDishDao.update(updatePaymentSql.toString(), new Object[]
					{ SysDictionary.PAYMENT_STATE_PAY_FAILURE, null, tenancyId, storeId, billNum, payId });

					posDishDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, null, null, remark, currentTime,
							SysDictionary.PAYMENT_STATE_PAY_FAILURE, batch_num, cachParamJson.toString(), 0d);

					this.getPaymentException(result, responseJson.optInt("sub_code"), responseJson.optString("sub_msg"));
				}
			}
			else if (SysDictionary.PAYMENT_DDM_QUICK_PAY.equals(paymentClass) || SysDictionary.PAYMENT_CLASS_ALI_PAY.equals(paymentClass))
			{// 支付宝支付 ali_pay
				JSONObject requestJson = new JSONObject();
				requestJson.put("bill_num", billNum);
				requestJson.put("pos_num", posNum);
				requestJson.put("opt_num", optNum);
				requestJson.put("report_date", report_date);
				requestJson.put("shift_id", shiftId);
				requestJson.put("jzid", jzid);
				requestJson.put("polling_flag", false);

				List<JSONObject> responseList = this.paymentQuery(tenancyId, storeId, requestJson);
				JSONObject responseJson = JSONObject.fromObject(responseList.get(0));
				if (responseJson.optBoolean("success") && "finished".equals(responseJson.optString("status")))
				{
					posDishDao.update(updatePaymentSql.toString(), new Object[]
					{ SysDictionary.PAYMENT_STATE_PAY_COMPLETE, responseJson.optString("trade_no"), tenancyId, storeId, billNum, payId });

					posDishDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, null,
							responseJson.optString("trade_no"), remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batch_num, null, 0d);
				}
				else if (!responseJson.optBoolean("success"))
				{
					this.getPaymentException(result, Constant.CODE_CONN_EXCEPTION, Constant.CODE_CONN_EXCEPTION_MSG);
				}
				else
				{
					posDishDao.update(updatePaymentSql.toString(), new Object[]
					{ SysDictionary.PAYMENT_STATE_PAY_FAILURE, null, tenancyId, storeId, billNum, payId });

					posDishDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, null, null, remark, currentTime,
							SysDictionary.PAYMENT_STATE_PAY_FAILURE, batch_num, cachParamJson.toString(), 0d);

					this.getPaymentException(result, responseJson.optInt("sub_code"), responseJson.optString("sub_msg"));
				}
			}
		}
		
		StringBuilder queryPayStateSql = new StringBuilder("select count(case when payment_state=? or payment_state=? then 1 else null end) as count,sum(currency_amount) as currency_amount from pos_bill_payment where tenancy_id=? and store_id=? and bill_num=?");
		SqlRowSet rsp = posDishDao.query4SqlRowSet(queryPayStateSql.toString(), new Object[]
		{ SysDictionary.PAYMENT_STATE_PAY,SysDictionary.PAYMENT_STATE_PAY_FAILURE, tenancyId, storeId, billNum });
		if (rsp.next())
		{
			if (0 == rsp.getInt("count") && paymentAmount <= rsp.getDouble("currency_amount"))
			{
				paymentState = SysDictionary.PAYMENT_STATE_PAY_COMPLETE;
			}
		}

		if (Constant.CODE_SUCCESS == result.getCode() && SysDictionary.PAYMENT_STATE_PAY_COMPLETE.equals(paymentState))
		{
			JSONObject resultJson = this.closedPosBill(tenancyId, storeId, billNum, report_date, shiftId, posNum, optNum, isprint_bill, printJson);
			List<JSONObject> resultList = new ArrayList<JSONObject>();
			resultList.add(resultJson);
	
			result.setData(resultList);
			result.setCode(Constant.CODE_SUCCESS);
			result.setMsg(Constant.PAYMENT_SUCCESS);
		}
		else
		{
			String reSql = new String("select id,jzid,name,amount,count,coalesce(number,'') as number,coalesce(phone,'') as phone,shift_id,pos_num,cashier_num,last_updatetime,coalesce(customer_id,0) as customer_id,coalesce(bill_code,'') as bill_code,coalesce(remark,'') as remark,currency_amount,payment_state from pos_bill_payment where bill_num=? and store_id=?");
			List<JSONObject> paymentList = posDishDao.query4Json(tenancyId, reSql, new Object[]
			{ billNum, storeId });
			double amount =0d;
			for (JSONObject pay : paymentList)
			{
				pay.put("payment_state_text", SysDictionary.PAYMENT_STATE_TEXT_MAP.get(pay.optString("payment_state")));
				amount = DoubleHelper.add(amount, pay.optDouble("currency_amount"), DEFAULT_SCALE);
			}
			
			double difference = DoubleHelper.sub(paymentAmount, amount, DEFAULT_SCALE);
			JSONObject resultJson = new JSONObject();
			resultJson.put("bill_num", billNum);
			resultJson.put("table_code", tableCode);
			resultJson.put("payment_amount", paymentAmount);
			resultJson.put("discount_amount", discountAmount);
			resultJson.put("payment_state", paymentState);
			resultJson.put("payment_state_text", SysDictionary.PAYMENT_STATE_TEXT_MAP.get(paymentState));
			resultJson.put("difference", difference > 0 ? difference : 0d);
			resultJson.put("change", difference < 0 ? difference : 0d);
			resultJson.put("paymentlist", paymentList);
			
			List<JSONObject> resultList = new ArrayList<JSONObject>();
			resultList.add(resultJson);
	
			result.setData(resultList);
		}
	}
	
	@Override
	public void billPaymentRepeat(Data param, Data result, JSONObject printJson)throws Exception {
		String tenantId = param.getTenancy_id();
		Integer storeId = param.getStore_id();
		
		Map<String, Object> map = ReqDataUtil.getDataMap(param);
		
		String optNum = ParamUtil.getStringValue(map, "opt_num", false, null);//操作员编号
		Date reportDate = ParamUtil.getDateValue(map, "report_date", false, null);//报表日期
		String billNum = ParamUtil.getStringValue(map, "bill_num", true, PosErrorCode.NOT_NULL_BILL_NUM);
		String isprint_bill = ParamUtil.getStringValue(map, "isprint_bill", false, null);
		String posNum = ParamUtil.getStringValue(map, "pos_num", false, null);
		int shiftId = ParamUtil.getIntegerValue(map, "shift_id", false, null);
		if (Tools.isNullOrEmpty(isprint_bill))
		{
			isprint_bill = "N";
		}
		
		//付款ID列表
		JSONArray items = JSONArray.fromObject(map.get("item"));
		StringBuilder rwids = new StringBuilder();
		if (items.size() > 0)
		{
			for (Object obj : items)
			{
				JSONObject itemJson = JSONObject.fromObject(obj);
				if (rwids.length() > 0)
				{
					rwids.append(",");
				}
				rwids.append(itemJson.optInt("pay_id"));
			}
		}
		
		if(rwids.length()<=0)
		{
			throw new SystemException(PosErrorCode.NOT_NULL_ITEM_LIST);
		}
		
		posDishDao.checkKssy(reportDate, optNum, storeId,shiftId,posNum);
		
		SqlRowSet rs = null;
		
		double paymentAmount=0d;
		String chanel = SysDictionary.CHANEL_MD01;
		
		StringBuilder sql = new StringBuilder("select bill_property,payment_state,payment_amount,source from pos_bill where bill_num = ? and store_id = ?");
		rs = posDishDao.query4SqlRowSet(sql.toString(), new Object[]{ billNum, storeId });
		if (rs.next())
		{
			paymentAmount = rs.getDouble("payment_amount");
			chanel = rs.getString("source");
			
			if ("CLOSED".equals(rs.getString("bill_property")))
			{
				throw new SystemException(PosErrorCode.BILL_CLOSED);
			}
		}
		else
		{
			throw new SystemException(PosErrorCode.NOT_EXISTS_BILL);
		}
		
		sql = new StringBuilder("select bp.*,pw.payment_class,pw.is_check from pos_bill_payment bp left join payment_way pw on bp.jzid=pw.id where store_id = ? and bill_num = ? and bp.id in ("+rwids.toString()+")");
		List<JSONObject> payList = posDishDao.query4Json(tenantId, sql.toString(), new Object[]
		{ storeId, billNum});
		
		this.paymentDebited(tenantId, storeId, billNum, chanel, paymentAmount,payList, result);
		
		this.closedPosBill(tenantId, storeId, billNum, DateUtil.formatDate(reportDate), shiftId, posNum, optNum, isprint_bill, printJson);
		
		if(result.getCode()==Constant.CODE_SUCCESS)
		{
			result.setCode(Constant.CODE_SUCCESS);
			result.setMsg(Constant.SELF_RETRY_PAYMENT_REPEAT_SUCCESS);
		}
	}
	
	@Override
	public void updatePaymentStateForFailure(Data param, Data result) throws Exception
	{
		String tenancyId = param.getTenancy_id();
		int storeId = param.getStore_id();
		List<?> paramList = param.getData();
		if (paramList == null || paramList.isEmpty())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}
		JSONObject para = JSONObject.fromObject(paramList.get(0));
		
		if (Tools.isNullOrEmpty(para.opt("bill_num")))
		{
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_BILL_NUM);
		}
		
		if (Tools.isNullOrEmpty(para.opt("report_date")))
		{
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_REPORT_DATE);
		}
		
		Date reportDate = DateUtil.parseDate(para.optString("report_date"));
		int shiftId = para.optInt("report_date");
		String billNum = para.optString("bill_num");
		String posNum = para.optString("pos_num");
		String optNum = para.optString("opt_num");
//		String mode = para.optString("mode");// 0,取消付款;1,付款完成;
		String isprint_bill = para.optString("isprint_bill");
		if (Tools.isNullOrEmpty(isprint_bill))
		{
			isprint_bill = "N";
		}

//		Date reportDate = posDao.getReportDate(tenancyId, storeId);
//		int shiftId = posDao.getShiftId(tenancyId, storeId);

		Timestamp currentTime = DateUtil.currentTimestamp();

		StringBuilder billAmountSql = new StringBuilder("select coalesce(b.payment_amount,0) as payment_amount,b.source,b.bill_property,b.batch_num,b.payment_state,b.table_code from pos_bill b where b.tenancy_id=? and b.store_id=? and b.bill_num=?");
		SqlRowSet rsb = posDishDao.query4SqlRowSet(billAmountSql.toString(), new Object[]
		{ tenancyId, storeId, billNum });

		double paymentAmount = 0d;
//		String batchNum = null;
//		String tableCode = null;

		if (rsb.next())
		{
			paymentAmount = rsb.getDouble("payment_amount");
//			batchNum = rsb.getString("batch_num");
//			tableCode = rsb.getString("table_code");
		}

		int payId = para.optInt("pay_id");
		StringBuilder queryPaymentSql = new StringBuilder("select bp.*,pw.payment_class from pos_bill_payment bp left join payment_way pw on bp.tenancy_id=pw.tenancy_id and bp.jzid=pw.id where bp.tenancy_id=? and bp.store_id=? and bp.bill_num=? and bp.id=?");
		SqlRowSet rs = posDishDao.query4SqlRowSet(queryPaymentSql.toString(), new Object[]
		{ tenancyId, storeId, billNum, payId });
		if (rs.next())
		{
			int jzid = rs.getInt("jzid");
			String table_code = rs.getString("table_code");
			String paymentClass = rs.getString("payment_class");
			String paymentName = rs.getString("name");
			String paymentEnglishName = rs.getString("name_english");
			double exchangeRate = rs.getDouble("rate");
			double pay_amount = rs.getDouble("amount");
			double pay_count = rs.getDouble("count");
			double currency_amount = rs.getDouble("currency_amount");
			String number = rs.getString("number");
			String phone = rs.getString("phone");
			int customer_id = rs.getInt("customer_id");
			String remark = rs.getString("remark");
			String batch_num = rs.getString("batch_num");
			String is_ysk = Tools.hv(rs.getString("is_ysk")) ? rs.getString("is_ysk") : "N";

			if (SysDictionary.PAYMENT_STATE_PAY.equals(rs.getString("payment_state")))
			{
				String deletePaymentSql = new String("delete from pos_bill_payment where id=?");
				posDishDao.update(deletePaymentSql.toString(), new Object[]
				{ payId });

				posDishDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, table_code, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, customer_id, rs.getString("bill_code"),
						remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_FAILURE, batch_num, rs.getString("param_cach"), 0d);
			}
		}

		String sql = new String("select coalesce(sum(currency_amount),0) as amount from pos_bill_payment where bill_num =? and store_id = ?");
		rsb = posDishDao.query4SqlRowSet(sql.toString(), new Object[]
		{ billNum, storeId });
		double difference = 0d;
		if (rsb.next())
		{
			difference = DoubleHelper.sub(paymentAmount, rsb.getDouble("amount"), DEFAULT_SCALE);
		}

		StringBuilder updateBillSql = new StringBuilder("update pos_bill set difference =? where bill_num =? and store_id = ?");
		posDishDao.update(updateBillSql.toString(), new Object[]
		{ difference, billNum, storeId });
		
		posDishDao.savePosLog(tenancyId, storeId, posNum, optNum, optNum, shiftId, reportDate, Constant.TITLE, "付款失败", "账单编号:" + billNum, "");

		result.setCode(Constant.CODE_SUCCESS);
		result.setMsg(Constant.PAYMENT_SUCCESS);
	}
	
	@Override
	public void updatePaymentStateForSuccess(Data param, Data result, JSONObject printJson) throws Exception
	{
		String tenancyId = param.getTenancy_id();
		int storeId = param.getStore_id();
		List<?> paramList = param.getData();
		if (paramList == null || paramList.isEmpty())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}
		JSONObject para = JSONObject.fromObject(paramList.get(0));
		
		if (Tools.isNullOrEmpty(para.opt("bill_num")))
		{
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_BILL_NUM);
		}
		
		if (Tools.isNullOrEmpty(para.opt("report_date")))
		{
			throw SystemException.getInstance(PosErrorCode.NOT_NULL_REPORT_DATE);
		}
		String report_date = para.optString("report_date");
		Date reportDate = DateUtil.parseDate(report_date);
		int shiftId = para.optInt("report_date");
		String billNum = para.optString("bill_num");
		String posNum = para.optString("pos_num");
		String optNum = para.optString("opt_num");
		String isprint_bill = para.optString("isprint_bill");
		if (Tools.isNullOrEmpty(isprint_bill))
		{
			isprint_bill = "N";
		}
		int payId = para.optInt("pay_id");
		
		Timestamp currentTime = DateUtil.currentTimestamp();
		
		StringBuilder queryPaymentSql = new StringBuilder("select bp.*,pw.payment_class from pos_bill_payment bp left join payment_way pw on bp.tenancy_id=pw.tenancy_id and bp.jzid=pw.id where bp.tenancy_id=? and bp.store_id=? and bp.bill_num=? and bp.id=?");
		SqlRowSet rs = posDishDao.query4SqlRowSet(queryPaymentSql.toString(), new Object[]
		{ tenancyId, storeId, billNum, payId });
		if (rs.next())
		{
			int jzid = rs.getInt("jzid");
			String table_code = rs.getString("table_code");
			String paymentClass = rs.getString("payment_class");
			String paymentName = rs.getString("name");
			String paymentEnglishName = rs.getString("name_english");
			double exchangeRate = rs.getDouble("rate");
			double pay_amount = rs.getDouble("amount");
			double pay_count = rs.getDouble("count");
			double currency_amount = rs.getDouble("currency_amount");
			String number = rs.getString("number");
			String phone = rs.getString("phone");
			int customer_id = rs.getInt("customer_id");
			String remark = rs.getString("remark");
			String batch_num = rs.getString("batch_num");
			String is_ysk = Tools.hv(rs.getString("is_ysk")) ? rs.getString("is_ysk") : "N";

			String paymentState = rs.getString("payment_state");
			if (SysDictionary.PAYMENT_STATE_PAY.equals(paymentState) || SysDictionary.PAYMENT_STATE_PAY_FAILURE.equals(paymentState))
			{
				StringBuilder updatePaymentSql = new StringBuilder("update pos_bill_payment set payment_state=? where tenancy_id=? and store_id=? and bill_num=? and id=?");
				posDishDao.update(updatePaymentSql.toString(), new Object[]
				{ SysDictionary.PAYMENT_STATE_PAY_COMPLETE, tenancyId, storeId, billNum, payId });

				posDishDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, table_code, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, customer_id, rs.getString("bill_code"),
						remark, currentTime, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batch_num, rs.getString("param_cach"), 0d);
			}
		}

		JSONObject resultJson = this.closedPosBill(tenancyId, storeId, billNum, report_date, shiftId, posNum, optNum, isprint_bill, printJson);
		List<JSONObject> resultList = new ArrayList<JSONObject>();
		resultList.add(resultJson);

		result.setData(resultList);
		result.setCode(Constant.CODE_SUCCESS);
		result.setMsg(Constant.PAYMENT_SUCCESS);
		
		posDishDao.savePosLog(tenancyId, storeId, posNum, optNum, optNum, shiftId, reportDate, Constant.TITLE, "付款成功", "账单编号:" + billNum, "");
	}

	/**
	 * @param tenancyId
	 * @param param
	 * @return
	 * @throws Exception
	 */
	private Map<String, Object> getQueryPara(String tenancyId, JSONObject param) throws Exception
	{
		int queryPeriod = 5;
		int queryTime = 20;
		Map<String, Object> map = new HashMap<String, Object>();
		if (param.containsKey("query_period") && param.containsKey("query_time"))
		{
			queryPeriod = param.optInt("query_period");
			queryTime = param.optInt("query_time");
		}
		else
		{
			String sql = "select para_code,para_value from sys_parameter where para_code in ('dsffkztjcjgsj','dsffkztlxcs')";
			List<JSONObject> gapList = posDishDao.query4Json(tenancyId, sql);
			for (JSONObject json : gapList)
			{
				if ("dsffkztjcjgsj".equals(json.optString("para_code")))
				{
					queryPeriod = json.optInt("para_value");
				}
				if ("dsffkztlxcs".equals(json.optString("para_code")))
				{
					queryTime = json.optInt("para_value");
				}
			}
			if (param.containsKey("query_period"))
			{
				queryPeriod = param.optInt("query_period");
			}

			if (param.containsKey("query_time"))
			{
				queryTime = param.optInt("query_time");
			}
		}
		map.put("query_period", queryPeriod);
		map.put("query_time", queryTime);

		return map;
	}

	@Override
	public void posPaymentOrder(Data param, Data result, JSONObject json) throws SystemException
	{
		try
		{
			String tenantId = param.getTenancy_id();
			Integer organId = param.getStore_id();

			Map<String, Object> map = ReqDataUtil.getDataMap(param);

			String posNum = ParamUtil.getStringValue(map, "pos_num", true, PosErrorCode.NOT_NULL_POS_NUM); // 机号

			String optNum = ParamUtil.getStringValue(map, "opt_num", false, null); // 操作员编号

			Date reportDate = ParamUtil.getDateValue(map, "report_date", false, null);//

			Integer shiftId = ParamUtil.getIntegerValue(map, "shift_id", false, null);//

			String billno = ParamUtil.getStringValue(map, "bill_num", true, PosErrorCode.NOT_NULL_BILL_NUM);

			String tableCode = ParamUtil.getStringValue(map, "table_code", false, null);

			String saleMode = ParamUtil.getStringValue(map, "sale_mode", true, PosErrorCode.NOT_NULL_SALE_MODE);

			String isprint_bill = ParamUtil.getStringValue(map, "isprint_bill", false, null);
			if (StringUtils.isEmpty(isprint_bill))
			{
				isprint_bill = "N";
			}

			json.put("tenancy_id", tenantId);
			json.put("store_id", organId);
			json.put("bill_num", billno);

			if ("order_device".equals(param.getSource()) || "android_pad".equals(param.getSource()))
			{

				posDishDao.checkBindOptnum(tenantId, organId, reportDate, posNum);

			}
			else
			{
				posDishDao.checkKssy(reportDate, optNum, organId,shiftId,posNum);
			}

			try
			{
				posDishDao.checkReportDate(tenantId, organId, reportDate);
			}
			catch (SystemException se)
			{
				throw se;
			}
			catch (Exception e1)
			{
				throw SystemException.getInstance(PosErrorCode.CHECK_REPORT_DATE_ERROR);
			}
			String uBillTable = new String("update pos_bill set table_code = ? where bill_num=? and store_id=?");
			String qTableCode = new String("select table_code from pos_bill where bill_num = ? and store_id = ?");

			SqlRowSet rstable = posDishDao.query4SqlRowSet(qTableCode, new Object[]
			{ billno, organId });
			String oldTableCode = null;
			if (rstable.next())
			{
				oldTableCode = rstable.getString("table_code");
			}
			if (StringUtils.isEmpty(oldTableCode))
			{
				oldTableCode = tableCode;

				posDishDao.update(uBillTable, new Object[]
				{ tableCode, billno, organId });

			}

			String updateSaleMode = new String("update pos_bill_item set sale_mode = ? where (sale_mode is null or sale_mode='') and bill_num = ? and store_id =? and tenancy_id=?");

			posDishDao.update(updateSaleMode, new Object[]
			{ saleMode, billno, organId, tenantId });

			@SuppressWarnings("unchecked")
			List<Map<String, String>> items = (List<Map<String, String>>) map.get("item");
			if (items.size() == 0)
			{
				throw new SystemException(PosErrorCode.NOT_NULL_ITEM_LIST);
			}
			/**
			 * 查找付款方式记录，根据jzid
			 */
			StringBuilder sql = new StringBuilder("SELECT pw.payment_name1,pw.rate,pw.payment_class,pw.if_jifen,pw.is_recharge FROM payment_way pw left join payment_way_of_ogran pwoo on pw.id = pwoo.payment_id where pwoo.payment_id = ? and pwoo.tenancy_id = ? and pwoo.organ_id = ?");
			/**
			 * 根据jzid查询是否已经存在付款流水
			 */
			StringBuilder sesql = new StringBuilder("select jzid,amount from pos_bill_payment where bill_num = ? and jzid = ? and store_id = ?");
			/**
			 * 付款流水
			 */
			StringBuilder str = new StringBuilder(
					"insert into pos_bill_payment (tenancy_id,store_id,bill_num,table_code,jzid,name,name_english,amount,count,number,phone,report_date,shift_id,pos_num,cashier_num,last_updatetime,is_ysk,rate,currency_amount,customer_id,bill_code,remark) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
			/**
			 * 更新付款流水
			 */
			StringBuilder ustr = new StringBuilder("update pos_bill_payment set amount=?,count=?,number=?,phone=?,report_date=?,shift_id=?,pos_num=?,cashier_num=?,last_updatetime=?,is_ysk=?,rate=?,currency_amount=? where bill_num = ? and jzid = ? ");
			/**
			 * 查询bill_amount
			 */
			StringBuilder billASql = new StringBuilder("select bill_amount,payment_amount from pos_bill where bill_num = ? and store_id = ?");
			/**
			 * 更新pos_bill表
			 */
			StringBuilder updateSql = new StringBuilder("update pos_bill set payment_amount =?,difference =?,bill_property =?,payment_time =?,pos_num =?,cashier_num =?,shift_id =?,sale_mode =? where bill_num =? and store_id = ?");
			/**
			 * 现在的状态,用来拼接字符串，返回前台
			 */
			StringBuilder newState = new StringBuilder();

			String jzid = items.get(0).get("jzid");
			Double amount = null;
			if (StringUtils.isNotEmpty(items.get(0).get("amount")))
			{
				amount = Double.parseDouble(items.get(0).get("amount"));
			}
			else
			{
				throw new SystemException(PosErrorCode.NOT_NULL_PAYMENT_AMOUNT);
			}
			Integer count = null;
			if (StringUtils.isNotEmpty(items.get(0).get("count")))
			{
				count = Integer.parseInt(items.get(0).get("count"));
			}
			else
			{
				count = 0;
			}
			String number = items.get(0).get("number");
			String phone = items.get(0).get("phone");

			Integer reason_id = null;
			if (StringUtils.isNotEmpty(items.get(0).get("reason_id")))
			{
				reason_id = Integer.parseInt(items.get(0).get("reason_id"));
			}

			Integer customerId = null;
			if (StringUtils.isNotEmpty(items.get(0).get("customer_id")))
			{
				customerId = Integer.parseInt(items.get(0).get("customer_id"));
			}

			String billCode = items.get(0).get("bill_code");
			String remark = items.get(0).get("remark");

			String name = null;
			String name_english = null;
			Double rate = null;
			Integer id = null;
			String paymentClass = "";
			String isRecharge = "";
			String isJiFen = null;

			// 写付款流水表
			if (StringUtils.isNotEmpty(jzid))
			{
				id = Integer.parseInt(jzid);

				SqlRowSet rs = posDishDao.query4SqlRowSet(sql.toString(), new Object[]
				{ id, tenantId, organId });
				if (rs.next())
				{
					name = rs.getString("payment_name1");
					newState.append(name);
					name_english = rs.getString("payment_name1");
					rate = rs.getDouble("rate");
					paymentClass = rs.getString("payment_class");
					isRecharge = rs.getString("is_recharge");
					isJiFen = rs.getString("if_jifen");
					if ("cash".equalsIgnoreCase(paymentClass))
					{
						if (StringUtils.isNotEmpty(name))
						{
							String qalias = new String("select class_item from sys_dictionary where class_item_code = ?");
							SqlRowSet rso = posDishDao.query4SqlRowSet(qalias, new Object[]
							{ name });
							if (rso.next())
							{
								name = rso.getString("class_item");
							}
						}
					}
				}
				else
				{
					throw new SystemException(PosErrorCode.NOT_EXIST_JZID);
				}

				newState.append(",应付金额：" + amount + ";");
				Timestamp time = DateUtil.currentTimestamp();

				Double all_pay_amount = null;
				Double billAmount = null;

				SqlRowSet rst = posDishDao.query4SqlRowSet(billASql.toString(), new Object[]
				{ billno, organId });
				while (rst.next())
				{
					all_pay_amount = rst.getDouble("payment_amount");
					billAmount = rst.getDouble("bill_amount");
				}

				JSONObject object = new JSONObject();

				String tableState = "FREE";
				StringBuffer updateTableState = new StringBuffer("UPDATE pos_tablestate set state = ?,lock_pos_num = ?,lock_opt_num=? where table_code = ? and store_id = ? and tenancy_id = ?");

				if (StringUtils.isEmpty(isJiFen))
				{
					isJiFen = "0";
				}

				if (StringUtils.isNotEmpty(paymentClass))
				{
					if (paymentClass.equalsIgnoreCase("freesingle"))
					{
						Object[] objs = new Object[]
						{ tenantId, organId, billno, tableCode, id, name, name_english, amount, count, number, phone, reportDate, shiftId, posNum, optNum, time, "N", rate, Scm.pmui(amount, rate), customerId, billCode, remark };
						posDishDao.update(str.toString(), objs);

						Timestamp timef = DateUtil.currentTimestamp();
						String upPb = new String("update pos_bill set difference=?,payment_time=?,pos_num=?,cashier_num=?,shift_id=?,payment_amount=?,free_amount=?,bill_property=?,billfree_reason_id=? where bill_num = ? and store_id = ? and tenancy_id = ?");
						posDishDao.update(upPb, new Object[]
						{ Scm.psub(billAmount, all_pay_amount), timef, posNum, optNum, shiftId, amount, all_pay_amount, "CLOSED", reason_id, billno, organId, tenantId });
						object.put("difference", 0);
						object.put("change", "0");
						String upPbi = new String("update pos_bill_item set real_amount = ?,item_remark = ? where bill_num = ? and item_remark <> ? and item_remark <> ? and item_remark <> ? and store_id = ? and tenancy_id = ?");
						posDishDao.update(upPbi, new Object[]
						{ 0d, "MD03", billno, "TC01", "FS02", "QX04", organId, tenantId });
						logger.debug("freesingle更新桌位状态：" + tableCode);

						if ("Y".equalsIgnoreCase(isprint_bill))
						{
							json.put("is_print", isprint_bill);

						}

						if (StringUtils.isNotEmpty(tableCode))
						{
							// 更新桌位状态为free
							posDishDao.update(updateTableState.toString(), new Object[]
							{ tableState, null, null, tableCode, organId, tenantId });
						}
					}
					else
					{
						if (paymentClass.equalsIgnoreCase("cash"))
						{
							SqlRowSet jzrs = posDishDao.query4SqlRowSet(sesql.toString(), new Object[]
							{ billno, id, organId });
							Double save_amount = null; // 支付方式支付的钱

							while (jzrs.next())
							{
								int njzid = jzrs.getInt("jzid");
								save_amount = jzrs.getDouble("amount");
								if (id == njzid)
								{
									posDishDao.update(ustr.toString(), new Object[]
									{ Scm.padd(amount, save_amount), count, number, phone, reportDate, shiftId, posNum, optNum, time, "N", rate, Scm.pmui(Scm.padd(amount, save_amount), rate), billno, id });
								}
								else
								{
									Object[] objs = new Object[]
									{ tenantId, organId, billno, tableCode, id, name, name_english, amount, count, number, phone, reportDate, shiftId, posNum, optNum, time, "N", rate, Scm.pmui(amount, rate), customerId, billCode, remark };
									posDishDao.update(str.toString(), objs);
								}
							}

							String jzcSql = new String("select count(id) from pos_bill_payment where bill_num = ? and store_id = ? and jzid = ?");
							int jzcount = posDishDao.queryForInt(jzcSql, new Object[]
							{ billno, organId, id });
							if (jzcount == 0)
							{
								Object[] objs = new Object[]
								{ tenantId, organId, billno, tableCode, id, name, name_english, amount, count, number, phone, reportDate, shiftId, posNum, optNum, time, "N", rate, Scm.pmui(amount, rate), customerId, billCode, remark };
								posDishDao.update(str.toString(), objs);
							}

						}
						else
						{
							Object[] objs = new Object[]
							{ tenantId, organId, billno, tableCode, id, name, name_english, amount, count, number, phone, reportDate, shiftId, posNum, optNum, time, "N", rate, Scm.pmui(amount, rate), customerId, billCode, remark };
							posDishDao.update(str.toString(), objs);
						}

						Timestamp timec = DateUtil.currentTimestamp();
						double difference = 0;

						// 账单流水库遍历并计算差额
						StringBuilder sb = new StringBuilder("select sum(amount) as amount from pos_bill_payment where bill_num = ? and store_id = ?");
						rs = posDishDao.query4SqlRowSet(sb.toString(), new Object[]
						{ billno, organId });

						double zf_amount = 0d;
						while (rs.next())
						{
							zf_amount = rs.getDouble("amount");
						}

						if (Tools.isNullOrEmpty(all_pay_amount) == false && all_pay_amount > 0d)
						{
							difference = Scm.psub(all_pay_amount, zf_amount);
						}
						String qIsZero = new String("select para_value from sys_parameter where para_code = 'IS_ZEROPAY' ");
						SqlRowSet qZero = posDishDao.query4SqlRowSet(qIsZero);
						if (qZero.next())
						{
							String isZero = qZero.getString("para_value");
							if (StringUtils.isNotEmpty(isZero))
							{
								if ("0".equalsIgnoreCase(isZero))
								{
									throw new SystemException(PosErrorCode.BILL_AMOUNT_ZERO);
								}
							}
						}
						else
						{
							throw new SystemException(PosErrorCode.BILL_AMOUNT_ZERO);
						}

						if (difference <= 0d)
						{
							if ("Y".equalsIgnoreCase(isprint_bill))
							{
								json.put("is_print", isprint_bill);
							}

							/**
							 * close账单
							 */
							int updateCount = posDishDao.update(updateSql.toString(), new Object[]
							{ all_pay_amount, difference, "CLOSED", timec, posNum, optNum, shiftId, saleMode, billno, organId });
							logger.debug("差额：" + difference + ",更新桌位状态：" + tableCode);

							if (StringUtils.isNotEmpty(tableCode))
							{
								/**
								 * 更新桌位状态为FREE
								 */
								posDishDao.update(updateTableState.toString(), new Object[]
								{ tableState, null, null, tableCode, organId, tenantId });
							}

							if (difference < 0d)
							{
								if (paymentClass.equalsIgnoreCase("coupons") && StringUtils.equals("0", isRecharge))
								{
									String upb = new String("update pos_bill set more_coupon = ?,difference = ? where bill_num = ? and store_id = ?");
									posDishDao.update(upb, new Object[]
									{ -difference, 0d, billno, organId });
									object.put("difference", "0");
									object.put("change", "0");
								}
								else
								{
									StringBuilder pwsql = new StringBuilder("select pw.id from payment_way pw left join payment_way_of_ogran pwoo on pw.id = pwoo.payment_id where pwoo.organ_id = ? and pw.is_standard_money='1' order by pw.id;");
									rs = posDishDao.query4SqlRowSet(pwsql.toString(), new Object[]
									{ organId });
									// 保存的是本币对应的jzid
									Integer pay_way_id = null;
									if (rs.next())
									{
										pay_way_id = rs.getInt("id");
									}
									Object[] objs = new Object[]
									{ tenantId, organId, billno, tableCode, pay_way_id, "找零", "change", difference, count, number, phone, reportDate, shiftId, posNum, optNum, timec, "N", rate, difference, customerId, billCode, remark };
									posDishDao.update(str.toString(), objs);
									object.put("change", -difference + "");
								}
							}
							else
							{
								object.put("change", "0");
							}

							result.setCode(com.tzx.pos.base.Constant.CODE_SUCCESS);
							if (updateCount == 0)
							{
								result.setMsg(com.tzx.pos.base.Constant.PAYMENT_FAILURE);
							}
							else
							{
								result.setMsg(com.tzx.pos.base.Constant.PAYMENT_SUCCESS);
							}
						}
						else
						{
							object.put("difference", difference + "");
							// 更新差额和应付金额
							StringBuilder difsql = new StringBuilder("update pos_bill set difference=?,payment_amount=? where bill_num = ? and store_id = ? ");
							// 更新账单的difference
							posDishDao.update(difsql.toString(), new Object[]
							{ difference, all_pay_amount, billno, organId });
							object.put("change", "0");
							result.setCode(com.tzx.pos.base.Constant.CODE_SUCCESS);
							result.setMsg(com.tzx.pos.base.Constant.PAYMENT_SUCCESS);
						}

						if (paymentClass.equalsIgnoreCase("coupons"))
						{
							if (isJiFen.equals("1"))
							{
								// 做
								// HttpUtil.sendPostRequest(reqURL, "");
							}
						}
					}
				}
				posDishDao.savePosLog(tenantId, organId, posNum, optNum, optNum, shiftId, reportDate, com.tzx.pos.base.Constant.TITLE, "结账", billno + " 应付金额：" + all_pay_amount, newState.toString());

				result.setData(posDishDao.posPayment(billno, organId, object));

			}
			else
			{
				throw new SystemException(PosErrorCode.NOT_NULL_JZID);
			}

		}
		catch (SystemException se)
		{
			throw se;
		}
		catch (Exception e)
		{
			e.printStackTrace();
			logger.info("点菜结账：" + ExceptionMessage.getExceptionMessage(e));
			throw new SystemException(PosErrorCode.OPER_ERROR);
		}
	}

	@Override
	public void posPayment(Data param, Data result, JSONObject json) throws SystemException
	{
		try
		{
			String tenantId = param.getTenancy_id();
			Integer organId = param.getStore_id();

			Map<String, Object> map = ReqDataUtil.getDataMap(param);

			String posNum = ParamUtil.getStringValue(map, "pos_num", true, PosErrorCode.NOT_NULL_POS_NUM); // 机号

			String optNum = ParamUtil.getStringValue(map, "opt_num", false, null); // 操作员编号

			Date reportDate = ParamUtil.getDateValue(map, "report_date", false, null);//

			Integer shiftId = ParamUtil.getIntegerValue(map, "shift_id", false, null);//

			String billno = ParamUtil.getStringValue(map, "bill_num", true, PosErrorCode.NOT_NULL_BILL_NUM);

			String tableCode = ParamUtil.getStringValue(map, "table_code", false, null);

			String saleMode = ParamUtil.getStringValue(map, "sale_mode", true, PosErrorCode.NOT_NULL_SALE_MODE);

			String isprint_bill = ParamUtil.getStringValue(map, "isprint_bill", false, null);
			if (StringUtils.isEmpty(isprint_bill))
			{
				isprint_bill = "N";
			}

			json.put("tenancy_id", tenantId);
			json.put("store_id", organId);
			json.put("bill_num", billno);

			if ("order_device".equals(param.getSource()) || "android_pad".equals(param.getSource()))
			{
				posDishDao.checkBindOptnum(tenantId, organId, reportDate, posNum);
			}
			else
			{
				posDishDao.checkKssy(reportDate, optNum, organId,shiftId,posNum);
			}

			try
			{
				posDishDao.checkReportDate(tenantId, organId, reportDate);
			}
			catch (SystemException se)
			{
				throw se;
			}
			catch (Exception e1)
			{
				throw SystemException.getInstance(PosErrorCode.CHECK_REPORT_DATE_ERROR);
			}

			String qTableCode = new String("select table_code,bill_property from pos_bill where bill_num = ? and store_id = ?");
			SqlRowSet rstable = posDishDao.query4SqlRowSet(qTableCode, new Object[]
			{ billno, organId });
			String oldTableCode = null;
			String bill_property = null;
			if (rstable.next())
			{
				oldTableCode = rstable.getString("table_code");
				bill_property = rstable.getString("bill_property");
			}

			if ("CLOSED".equals(bill_property))
			{
				throw new SystemException(PosErrorCode.BILL_CLOSED);
			}

			if (StringUtils.isEmpty(oldTableCode))
			{
				oldTableCode = tableCode;
				String uBillTable = new String("update pos_bill set table_code = ? where bill_num=? and store_id=?");

				posDishDao.update(uBillTable, new Object[]
				{ tableCode, billno, organId });

			}

			String updateSaleMode = new String("update pos_bill_item set sale_mode = ? where (sale_mode is null or sale_mode='') and bill_num = ? and store_id =? and tenancy_id=?");

			posDishDao.update(updateSaleMode, new Object[]
			{ saleMode, billno, organId, tenantId });

			/**
			 * 付款流水
			 */
			StringBuilder str = new StringBuilder(
					"insert into pos_bill_payment (tenancy_id,store_id,bill_num,table_code,jzid,name,name_english,amount,count,number,phone,report_date,shift_id,pos_num,cashier_num,last_updatetime,is_ysk,rate,currency_amount,customer_id,bill_code,remark,payment_state) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");

			/**
			 * 现在的状态,用来拼接字符串，返回前台
			 */
			StringBuilder newState = new StringBuilder();

			@SuppressWarnings("unchecked")
			List<Map<String, String>> items = (List<Map<String, String>>) map.get("item");
			if (items.size() == 0)
			{
				throw new SystemException(PosErrorCode.NOT_NULL_ITEM_LIST);
			}
			String jzid = items.get(0).get("jzid");
			Double amount = null;
			if (StringUtils.isNotEmpty(items.get(0).get("amount")))
			{
				amount = Double.parseDouble(items.get(0).get("amount"));
			}
			else
			{
				throw new SystemException(PosErrorCode.NOT_NULL_PAYMENT_AMOUNT);
			}
			Integer count = null;
			if (StringUtils.isNotEmpty(items.get(0).get("count")))
			{
				count = Integer.parseInt(items.get(0).get("count"));
			}
			else
			{
				count = 0;
			}
			String number = items.get(0).get("number");
			String phone = items.get(0).get("phone");

			Integer reason_id = null;
			if (StringUtils.isNotEmpty(items.get(0).get("reason_id")))
			{
				reason_id = Integer.parseInt(items.get(0).get("reason_id"));
			}

			Integer customerId = null;
			if (StringUtils.isNotEmpty(items.get(0).get("customer_id")))
			{
				customerId = Integer.parseInt(items.get(0).get("customer_id"));
			}

			String billCode = items.get(0).get("bill_code");
			String remark = items.get(0).get("remark");

			String name = null;
			String name_english = null;
			Double rate = null;
			Integer id = null;
			String paymentClass = "";
			String isRecharge = "";
			String isJiFen = null;
			// double currencyAmount = 0;

			String mealMode = "01";// 01,普通;02,自助;
			SqlRowSet rs1 = posDishDao.query4SqlRowSet("select para_value from sys_parameter where para_code=?", new Object[]
			{ "ZCDCMS" });
			if (rs1.next())
			{
				mealMode = rs1.getString("para_value");
			}

			String formatState = "";// 1:正餐;2:快餐
			rs1 = posDishDao.query4SqlRowSet("select format_state from organ where tenancy_id=? and id=?", new Object[]
			{ tenantId, organId });
			if (rs1.next())
			{
				formatState = rs1.getString("format_state");
			}

			// 写付款流水表
			if (StringUtils.isNotEmpty(jzid))
			{
				id = Integer.parseInt(jzid);
				/**
				 * 查找付款方式记录，根据jzid
				 */
				StringBuilder sql = new StringBuilder(
						"select pw.payment_name1,pw.rate,pw.payment_class,pw.if_jifen,pw.is_recharge from payment_way pw left join payment_way_of_ogran pwoo on pw.id = pwoo.payment_id where pwoo.payment_id = ? and pw.status='1' and pwoo.tenancy_id = ? and pwoo.organ_id = ?");
				SqlRowSet rs = posDishDao.query4SqlRowSet(sql.toString(), new Object[]
				{ id, tenantId, organId });
				if (rs.next())
				{
					name = rs.getString("payment_name1");
					newState.append(name);
					name_english = rs.getString("payment_name1");
					rate = rs.getDouble("rate");
					paymentClass = rs.getString("payment_class");
					isRecharge = rs.getString("is_recharge");
					isJiFen = rs.getString("if_jifen");
					if ("cash".equalsIgnoreCase(paymentClass))
					{
						if (StringUtils.isNotEmpty(name))
						{
							String qalias = new String("select class_item from sys_dictionary where class_item_code = ?");
							SqlRowSet rso = posDishDao.query4SqlRowSet(qalias, new Object[]
							{ name });
							if (rso.next())
							{
								name = rso.getString("class_item");
							}
						}
					}
					// currencyAmount = Scm.pmui(amount, rate);
				}
				else
				{
					throw new SystemException(PosErrorCode.NOT_EXIST_JZID);
				}

				newState.append(",应付金额：" + amount + ";");
				Timestamp time = DateUtil.currentTimestamp();

				Double all_pay_amount = null;
				// Double billAmount = null;

				/**
				 * 查询bill_amount
				 */
				StringBuilder billASql = new StringBuilder("select payment_amount from pos_bill where bill_num = ? and store_id = ?");
				SqlRowSet rst = posDishDao.query4SqlRowSet(billASql.toString(), new Object[]
				{ billno, organId });
				while (rst.next())
				{
					all_pay_amount = rst.getDouble("payment_amount");
					// billAmount = rst.getDouble("bill_amount");
				}

				JSONObject object = new JSONObject();

				String tableState = "FREE";
				StringBuffer updateTableState = new StringBuffer("update pos_tablestate set state = ?,lock_pos_num = ?,lock_opt_num=? where table_code = ? and store_id = ? and tenancy_id = ?");

				if (StringUtils.isEmpty(isJiFen))
				{
					isJiFen = "0";
				}

				if (StringUtils.isNotEmpty(paymentClass))
				{
					if (paymentClass.equalsIgnoreCase("freesingle"))
					{
						Object[] objs = new Object[]
						{ tenantId, organId, billno, tableCode, id, name, name_english, amount, count, number, phone, reportDate, shiftId, posNum, optNum, time, "N", rate, Scm.pmui(amount, rate), customerId, billCode, remark, SysDictionary.PAYMENT_STATE_PAY_COMPLETE };
						posDishDao.update(str.toString(), objs);

						Timestamp timef = DateUtil.currentTimestamp();
						String upPb = new String("update pos_bill set difference=?,payment_time=?,pos_num=?,cashier_num=?,shift_id=?,free_amount=?,bill_property=?,billfree_reason_id=?,payment_state=? where bill_num = ? and store_id = ? and tenancy_id = ?");
						posDishDao.update(upPb, new Object[]
						{ 0d, timef, posNum, optNum, shiftId, amount, "CLOSED", reason_id, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, billno, organId, tenantId });
						object.put("difference", 0);
						object.put("change", "0");
						// String upPbi = new
						// String("update pos_bill_item set real_amount = ?,item_remark = ? where bill_num = ? and item_remark <> ? and item_remark <> ? and item_remark <> ? and store_id = ? and tenancy_id = ?");
						// posDishDao.update(upPbi, new
						// Object[]{0d,"MD03",billno,"TC01","FS02","QX04",organId,tenantId});
						logger.debug("freesingle更新桌位状态：" + tableCode);

						if ("Y".equalsIgnoreCase(isprint_bill))
						{
							json.put("is_print", isprint_bill);
						}

						// if (StringUtils.isNotEmpty(tableCode))
						if ("1".equals(formatState) && "01".equals(mealMode))
						{
							// 更新桌位状态为free
							posDishDao.update(updateTableState.toString(), new Object[]
							{ tableState, null, null, tableCode, organId, tenantId });
						}
					}
					else
					{
						if (paymentClass.equalsIgnoreCase("cash"))
						{
							/**
							 * 根据jzid查询是否已经存在付款流水
							 */
							StringBuilder sesql = new StringBuilder("select jzid,amount from pos_bill_payment where bill_num = ? and jzid = ? and store_id = ?");
							SqlRowSet jzrs = posDishDao.query4SqlRowSet(sesql.toString(), new Object[]
							{ billno, id, organId });
							Double save_amount = null; // 支付方式支付的钱

							while (jzrs.next())
							{
								int njzid = jzrs.getInt("jzid");
								save_amount = jzrs.getDouble("amount");
								if (id == njzid)
								{
									// 更新付款流水
									StringBuilder ustr = new StringBuilder("update pos_bill_payment set amount=?,count=?,number=?,phone=?,report_date=?,shift_id=?,pos_num=?,cashier_num=?,last_updatetime=?,is_ysk=?,rate=?,currency_amount=? where bill_num = ? and jzid = ? ");
									posDishDao.update(ustr.toString(), new Object[]
									{ Scm.padd(amount, save_amount), count, number, phone, reportDate, shiftId, posNum, optNum, time, "N", rate, Scm.pmui(Scm.padd(amount, save_amount), rate), billno, id });
								}
								else
								{
									Object[] objs = new Object[]
									{ tenantId, organId, billno, tableCode, id, name, name_english, amount, count, number, phone, reportDate, shiftId, posNum, optNum, time, "N", rate, Scm.pmui(amount, rate), customerId, billCode, remark, SysDictionary.PAYMENT_STATE_PAY_COMPLETE };
									posDishDao.update(str.toString(), objs);
								}
							}

							String jzcSql = new String("select count(id) from pos_bill_payment where bill_num = ? and store_id = ? and jzid = ?");
							int jzcount = posDishDao.queryForInt(jzcSql, new Object[]
							{ billno, organId, id });
							if (jzcount == 0)
							{
								Object[] objs = new Object[]
								{ tenantId, organId, billno, tableCode, id, name, name_english, amount, count, number, phone, reportDate, shiftId, posNum, optNum, time, "N", rate, Scm.pmui(amount, rate), customerId, billCode, remark, SysDictionary.PAYMENT_STATE_PAY_COMPLETE };
								posDishDao.update(str.toString(), objs);
							}

						}
						// else if ("thirdparty".equalsIgnoreCase(paymentClass))
						// {
						// //这块儿暂时不做操作先分出来，因为pad那块儿不调用这个，而pos调用这个，会把支付方式保存两次
						// }
						else
						{
							if (paymentClass.equalsIgnoreCase("wechat_pay"))
							{
								Object[] objs = new Object[]
								{ tenantId, organId, billno, tableCode, id, name, name_english, amount, count, "", phone, reportDate, shiftId, posNum, optNum, time, "N", rate, amount, customerId, billCode, remark, SysDictionary.PAYMENT_STATE_PAY_COMPLETE };
								posDishDao.update(str.toString(), objs);
							}
							else
							{
								Object[] objs = new Object[]
								{ tenantId, organId, billno, tableCode, id, name, name_english, amount, count, number, phone, reportDate, shiftId, posNum, optNum, time, "N", rate, Scm.pmui(amount, rate), customerId, billCode, remark, SysDictionary.PAYMENT_STATE_PAY_COMPLETE };
								posDishDao.update(str.toString(), objs);
							}
						}

						Timestamp timec = DateUtil.currentTimestamp();
						double difference = 0;

						// 账单流水库遍历并计算差额
						StringBuilder sb = new StringBuilder("select sum(amount) as amount from pos_bill_payment where bill_num = ? and store_id = ?");
						rs = posDishDao.query4SqlRowSet(sb.toString(), new Object[]
						{ billno, organId });

						double zf_amount = 0d;
						while (rs.next())
						{
							zf_amount = rs.getDouble("amount");
						}

						if (Tools.isNullOrEmpty(all_pay_amount) == false && all_pay_amount > 0d)
						{
							difference = Scm.psub(all_pay_amount, zf_amount);
						}
						String qIsZero = new String("select para_value from sys_parameter where para_code = 'IS_ZEROPAY' ");
						SqlRowSet qZero = posDishDao.query4SqlRowSet(qIsZero);
						if (qZero.next())
						{
							String isZero = qZero.getString("para_value");
							if (StringUtils.isNotEmpty(isZero))
							{
								if ("0".equalsIgnoreCase(isZero))
								{
									throw new SystemException(PosErrorCode.BILL_AMOUNT_ZERO);
								}
							}
						}
						else
						{
							throw new SystemException(PosErrorCode.BILL_AMOUNT_ZERO);
						}

						if (difference <= 0d)
						{
							if ("Y".equalsIgnoreCase(isprint_bill))
							{
								json.put("is_print", isprint_bill);
							}

							/**
							 * close账单
							 */
							/**
							 * 更新pos_bill表
							 */
							StringBuilder updateSql = new StringBuilder("update pos_bill set difference =?,bill_property =?,payment_time =?,pos_num =?,cashier_num =?,shift_id =?,sale_mode =?,payment_state=? where bill_num =? and store_id = ?");
							int updateCount = posDishDao.update(updateSql.toString(), new Object[]
							{ difference, "CLOSED", timec, posNum, optNum, shiftId, saleMode, SysDictionary.PAYMENT_STATE_PAY_COMPLETE, billno, organId });
							logger.debug("差额：" + difference + ",更新桌位状态：" + tableCode);

							// if (StringUtils.isNotEmpty(tableCode))
							if ("1".equals(formatState) && "01".equals(mealMode))
							{
								/**
								 * 更新桌位状态为FREE
								 */
								posDishDao.update(updateTableState.toString(), new Object[]
								{ tableState, null, null, tableCode, organId, tenantId });
								clearCache(tableCode);
							}

							if (difference < 0d)
							{
								// 如果是优惠券，不能找零
								if (paymentClass.equalsIgnoreCase("coupons") && StringUtils.equals("0", isRecharge))
								{
									String upb = new String("update pos_bill set more_coupon = ?,difference = ? where bill_num = ? and store_id = ?");
									posDishDao.update(upb, new Object[]
									{ -difference, 0d, billno, organId });
									object.put("difference", "0");
									object.put("change", "0");
								}
								else
								{
									StringBuilder pwsql = new StringBuilder("select pw.id from payment_way pw left join payment_way_of_ogran pwoo on pw.id = pwoo.payment_id where pwoo.organ_id = ? and pw.is_standard_money='1' order by pw.id;");
									rs = posDishDao.query4SqlRowSet(pwsql.toString(), new Object[]
									{ organId });
									// 保存的是本币对应的jzid
									Integer pay_way_id = null;
									if (rs.next())
									{
										pay_way_id = rs.getInt("id");
									}
									Object[] objs = new Object[]
									{ tenantId, organId, billno, tableCode, pay_way_id, "找零", "change", difference, count, number, phone, reportDate, shiftId, posNum, optNum, timec, "N", rate, Scm.pmui(difference, rate), customerId, billCode, remark, SysDictionary.PAYMENT_STATE_PAY_COMPLETE };
									posDishDao.update(str.toString(), objs);
									// object.put("difference", -difference+"");
									object.put("change", -difference + "");
								}
							}
							else
							{
								object.put("change", "0");
							}

							result.setCode(com.tzx.pos.base.Constant.CODE_SUCCESS);
							if (updateCount == 0)
							{
								result.setMsg(com.tzx.pos.base.Constant.PAYMENT_FAILURE);
							}
							else
							{
								result.setMsg(com.tzx.pos.base.Constant.PAYMENT_SUCCESS);
							}
						}
						else
						{
							object.put("difference", difference + "");
							// 更新差额和应付金额
							StringBuilder difsql = new StringBuilder("update pos_bill set difference=? where bill_num = ? and store_id = ? ");
							// 更新账单的difference
							posDishDao.update(difsql.toString(), new Object[]
							{ difference, billno, organId });
							object.put("change", "0");
							result.setCode(com.tzx.pos.base.Constant.CODE_SUCCESS);
							result.setMsg(com.tzx.pos.base.Constant.PAYMENT_SUCCESS);
						}

						if (paymentClass.equalsIgnoreCase("coupons"))
						{
							if (isJiFen.equals("1"))
							{
								// 做
								// HttpUtil.sendPostRequest(reqURL, "");
							}
						}
					}
				}
				posDishDao.savePosLog(tenantId, organId, posNum, optNum, optNum, shiftId, reportDate, com.tzx.pos.base.Constant.TITLE, "结账", billno + " 应付金额：" + all_pay_amount, newState.toString());

				result.setData(posDishDao.posPayment(billno, organId, object));

			}
			else
			{
				throw new SystemException(PosErrorCode.NOT_NULL_JZID);
			}
		}
		catch (SystemException se)
		{
			throw se;
		}
		catch (Exception e)
		{
			e.printStackTrace();
			logger.info("结账：" + ExceptionMessage.getExceptionMessage(e));
			throw new SystemException(PosErrorCode.OPER_ERROR);
		}
	}
	/**
	 * 清空缓存
	 * @param tableCode
	 * @return
	 */
	@CacheEvict(value="tableStateCache",key="#tableCode")
	public int clearCache(String tableCode){
		try {
			EhcacheUtil.removeAllElment("tableStateCache",tableCode);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return 0;
	}
	@Override
	public void clearPayment(Data param, Data result) throws SystemException
	{
		String tenantId = param.getTenancy_id();
		Integer organId = param.getStore_id();

		Map<String, Object> map = ReqDataUtil.getDataMap(param);

		String mode = ParamUtil.getStringValue(map, "mode", true, PosErrorCode.NOT_NULL_MODE); // 0:全单清除
																								// 1:根据id进行清除
		Date reportDate = ParamUtil.getDateValue(map, "report_date", false, null);
		Integer shiftId = ParamUtil.getIntegerValue(map, "shift_id", false, null);
		String billno = ParamUtil.getStringValue(map, "bill_num", true, PosErrorCode.NOT_NULL_BILL_NUM);
		String posNum = ParamUtil.getStringValue(map, "pos_num", false, null);
		String optNum = ParamUtil.getStringValue(map, "opt_num", false, null);
		Integer id = null;

		try
		{
			List<JSONObject> billList = posDishDao.findBill(tenantId, organId, billno);
			double paymentAmount = 0d;
			String paymentState = SysDictionary.PAYMENT_STATE_NOTPAY;
			if(null != billList && billList.size()>0)
			{
				JSONObject  billJson = JSONObject.fromObject(billList.get(0));
				if(SysDictionary.BILL_PROPERTY_CLOSED.equals(billJson.optString("bill_property")))
				{
					throw SystemException.getInstance(PosErrorCode.BILL_CLOSED);
				}
				paymentState = billJson.optString("payment_state");
				if(SysDictionary.PAYMENT_STATE_PAY.equals(paymentState) && "0".equalsIgnoreCase(mode))
				{
					throw SystemException.getInstance(PosErrorCode.BILL_PAYMENT_STATE_PAYING_ERROR);
				}
				paymentAmount = billJson.optDouble("payment_amount");
			}
			else
			{
				throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_BILL);
			}
			
			if ("0".equalsIgnoreCase(mode))
			{
				StringBuilder sql = new StringBuilder("delete from pos_bill_payment where bill_num = ?");
				posDishDao.update(sql.toString(), new Object[]
				{ billno });
			}
			else
			{
				id = ParamUtil.getIntegerValue(map, "id", true, PosErrorCode.NOT_NULL_ID);
				StringBuilder sql = new StringBuilder("delete from pos_bill_payment where id = ?");
				
				posDishDao.update(sql.toString(), new Object[]
				{ id });
			}
			double amount = posDishDao.getBillPaymentForSumAmount(tenantId, organId, billno);
			double difference =DoubleHelper.sub(paymentAmount, amount, 4);
			if(amount==0)
			{
				paymentState = SysDictionary.PAYMENT_STATE_NOTPAY;
			}
			StringBuilder updateBillSql = new StringBuilder("update pos_bill set difference =?,payment_state=? where tenancy_id =? and store_id=? and bill_num=?");
			posDishDao.update(updateBillSql.toString(), new Object[]{difference,paymentState,tenantId, organId, billno});
			
			StringBuilder updateBillCouponsSql = new StringBuilder("delete from pos_bill_payment_coupons where tenancy_id =? and store_id=? and bill_num=?");
			posDishDao.update(updateBillCouponsSql.toString(), new Object[]{tenantId, organId, billno});
	
			posDishDao.savePosLog(tenantId, organId, posNum, optNum, optNum, shiftId, reportDate, com.tzx.pos.base.Constant.TITLE, "清除付款记录", "", "");
			result.setCode(com.tzx.pos.base.Constant.CODE_SUCCESS);
			result.setMsg(com.tzx.pos.base.Constant.CLEAR_PAYMENT_SUCCESS);
		}
		catch (SystemException se)
		{
			throw se;
		}
		catch (Exception se)
		{
			throw new SystemException(PosErrorCode.CLEAR_PAYMENT_FAILURE);
		}
		
//		if ("0".equalsIgnoreCase(mode))
//		{
//			StringBuilder sql = new StringBuilder("delete from pos_bill_payment where bill_num = ?");
//			try
//			{
//				posDishDao.update(sql.toString(), new Object[]
//				{ billno });
//				posDishDao.savePosLog(tenantId, organId, posNum, optNum, optNum, shiftId, reportDate, com.tzx.pos.base.Constant.TITLE, "清除付款记录", "", "");
//				result.setCode(com.tzx.pos.base.Constant.CODE_SUCCESS);
//				result.setMsg(com.tzx.pos.base.Constant.CLEAR_PAYMENT_SUCCESS);
//			}
//			catch (Exception se)
//			{
//				throw new SystemException(PosErrorCode.CLEAR_PAYMENT_FAILURE);
//			}
//		}
//		else
//		{
//			id = ParamUtil.getIntegerValue(map, "id", true, PosErrorCode.NOT_NULL_ID);
//
//			StringBuilder sql = new StringBuilder("delete from pos_bill_payment where id = ?");
//			try
//			{
//				
//				posDishDao.savePosLog(tenantId, organId, posNum, optNum, optNum, shiftId, reportDate, com.tzx.pos.base.Constant.TITLE, "清除付款记录", "", "");
//				result.setCode(com.tzx.pos.base.Constant.CODE_SUCCESS);
//				result.setMsg(com.tzx.pos.base.Constant.CLEAR_PAYMENT_SUCCESS);
//			}
//			catch (Exception se)
//			{
//				throw new SystemException(PosErrorCode.CLEAR_PAYMENT_FAILURE);
//			}
//		}
	}
	
	
	private void getPaymentException(Data resultData,int code,String msg) throws Exception
	{
		if(Constant.CODE_CONN_EXCEPTION == code)
		{
			resultData.setCode(Constant.CODE_CONN_EXCEPTION);
			resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
		}
		else if (CrmErrorCode.BALANCE_NOTENOUGH_ERROE.getNumber() == code)
		{
			resultData.setCode(code);
			resultData.setMsg(PropertiesLoader.getProperty(String.valueOf(code)));
		}
		else
		{
			int defaultCode = PosErrorCode.BILL_PAYMENT_ERROP.getNumber();
			resultData.setCode(defaultCode);
			resultData.setMsg(PropertiesLoader.getProperty(String.valueOf(defaultCode)).replace("{0}", msg));
		}
	}
}
