package com.tzx.pos.bo.payment.imp.ext;

import java.sql.Timestamp;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.util.PropertiesLoader;
import com.tzx.framework.common.util.Tools;
import com.tzx.member.meituan.bo.PosNewCrmService;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.bo.payment.PaymentWayService;
import com.tzx.pos.bo.payment.imp.PaymentWayServiceImp;
import com.tzx.pos.po.springjdbc.dao.PosBillMemberDao;

import net.sf.json.JSONObject;

/**
 * 美大会员卡
 * 
 *author lihp
 *
 *date 2017.03.20
 */
@Service("com.tzx.pos.bo.payment.imp.ext.MeiDaCardPaymentWayServiceImp")
public class MeiDaCardPaymentWayServiceImp extends PaymentWayServiceImp implements PaymentWayService
{
	@Resource(name = PosNewCrmService.NAME)
	private PosNewCrmService posNewCrmService;
	
	@Resource(name = PosBillMemberDao.NAME)
	private PosBillMemberDao			memberDao;
	
//	@Resource(name = PosNewCrmDao.NAME)
//	private PosNewCrmDao			posNewCrmDao;

	@Override
	public void payment(String tenantId, int storeId, String billNum,String batchNum, Date reportDate, int shiftId, String posNum, String optNum, String tableCode, JSONObject payment, Timestamp currentTime,String isOnlinePay) throws Exception
	{
//		String chanel = payment.optString("chanel");

		double amount = payment.optDouble("amount");
		double count = payment.optDouble("count");
		double currencyAmount = payment.optDouble("currency_amount");
		String number = payment.optString("number");
		String phone = payment.optString("phone");
		String billCode = payment.optString("remark");//美大会员卡订单流水号

		int jzid = payment.optInt("jzid");
		String paymentClass = payment.optString("payment_class");
		String paymentName = payment.optString("payment_name");
		String nameEnglish = payment.optString("payment_english_name");
		double rate = payment.optDouble("rate");
		String remark = payment.optString("remark");
		String isYsk = payment.optString("is_ysk");
		int uploadtag = payment.optInt("upload_tag");
		if (Tools.isNullOrEmpty(isYsk))
		{
			isYsk = "N";
		}
		List<JSONObject> memberList = memberDao.queryPosBillMember(tenantId, storeId, billNum, SysDictionary.BILL_MEMBERCARD_CZXF03);
		if(null == memberList || memberList.size()==0)
		{
			memberDao.insertPosBillMember(tenantId, storeId, billNum, reportDate, SysDictionary.BILL_MEMBERCARD_CZXF03, remark, number, phone, currentTime, null, null, 0,0d,0d,amount,billCode,null);
		}
		
		paymentDao.updatePosBillPayment(tenantId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, nameEnglish, rate, amount, count, currencyAmount, number, phone, isYsk, null, billCode, remark, currentTime,uploadtag,
				SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0);

//		paymentDao.updatePosBillPaymentLog(tenantId, storeId, billNum, reportDate, shiftId, posNum, optNum, tableCode, paymentClass, jzid, paymentName, nameEnglish, rate, amount, count, currencyAmount, number, phone, isYsk, null, billCode, remark, currentTime,uploadtag,
//				SysDictionary.PAYMENT_STATE_PAY_COMPLETE, batchNum, null, 0);
	}

	@Override
	public void paymentDebit(String tenancyId, int storeId, String billNum,String orderNum,String chanel, Double paymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData) throws Exception
	{
		// TODO Auto-generated method stub
		int payId = payment.optInt("id");
		//插入会员明细
		String currentTimeStr = DateUtil.format(currentTime);
//		JSONObject paramJson = new JSONObject();
		Map<String, Object> paramJson =new HashMap<String, Object>();
		String billId;
		if("".equals(payment.optString("bill_code")) || payment.optString("bill_code") == null || "null".equals(payment.optString("bill_code")))//美大会员卡支付流水号,自己单独加进ayment里的
		{
			String billsql = new String("select bill_code from pos_bill_payment where tenancy_id=? and store_id=? and  bill_num=?");
			JSONObject jsonb = paymentDao.query4Json(tenancyId, billsql, new Object[]
					{ tenancyId, storeId, billNum }).get(0);
			billId = jsonb.optString("bill_code");
		}else{
			billId = payment.optString("bill_code");
		}
		paramJson.put("pos_num", payment.get("pos_num")); // 机台号
		paramJson.put("mobil", payment.get("phone")); // 会员手机号
		
		paramJson.put("bill_code", billId);
		paramJson.put("card_code", payment.get("phone"));//会员卡号/顾客号
		paramJson.put("customer_name", "");//顾客id
		paramJson.put("pay_money", payment.optDouble("amount"));//实收金额
		paramJson.put("payment_id", payment.optInt("jzid"));//支付类型
		paramJson.put("pay_no", billNum);
		String optNum = payment.optString("cashier_num");//操作人
		paramJson.put("operator_id", optNum);
		paramJson.put("shift_id", payment.optInt("shift_id"));//操作班次
		paramJson.put("business_date", currentTimeStr);//作日期 
		if(Tools.hv(chanel)){			
			paramJson.put("chanel", chanel);
		}else{			
			paramJson.put("chanel", "meidahuiyuan_pay");
		}
		
		resultData = posNewCrmService.paymentCRM(tenancyId, storeId, paramJson);
		
		if (Constant.CODE_SUCCESS == resultData.getCode())
		{
			JSONObject paymentJson = new JSONObject();
			paymentJson.put("bill_code", payment.optString("bill_code"));
			paymentJson.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
			paymentJson.put("id", payId);
			paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", paymentJson);
		}
		else if (Constant.CODE_CONN_EXCEPTION == resultData.getCode())
		{
			resultData.setCode(Constant.CODE_CONN_EXCEPTION);
			resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
		}
		else if (Constant.PAYMENT_STATUS_PAYING_CODE == resultData.getCode())
		{
			resultData.setCode(Constant.PAYMENT_STATUS_PAYING_CODE);
			resultData.setMsg(PropertiesLoader.getProperty(String.valueOf(resultData.getCode())));
		}
		else
		{
			paymentDao.deletePosBillPaymentByID(tenancyId, storeId, billNum, payId);

			Date reportDate = DateUtil.parseDate(payment.optString("report_date"));
			Integer shiftId = payment.optInt("shift_id");
			String posNum = payment.optString("pos_num");

			int jzid = payment.optInt("jzid");
			String number = payment.optString("number");
			String table_code = payment.optString("table_code");
			String paymentClass = payment.optString("type");
			String paymentName = payment.optString("name");
			String paymentEnglishName = payment.optString("name_english");
			double exchangeRate = payment.optDouble("rate");
			double pay_amount = payment.optDouble("amount");
			double pay_count = payment.optDouble("count");
			double currency_amount = payment.optDouble("currency_amount");
			String phone = payment.optString("phone");
			int customer_id = payment.optInt("customer_id");
			String remark = payment.optString("remark");
			String batch_num = payment.optString("batch_num");
			String is_ysk = Tools.hv(payment.optString("is_ysk")) ? payment.optString("is_ysk") : "N";

			paymentDao.insertPosBillPaymentLog(tenancyId, storeId, billNum, reportDate, shiftId, posNum, optNum, table_code, paymentClass, jzid, paymentName, paymentEnglishName, exchangeRate, pay_amount, pay_count, currency_amount, number, phone, is_ysk, customer_id, null, remark, currentTime,
					SysDictionary.PAYMENT_STATE_PAY_FAILURE, batch_num, payment.optString("param_cach"), 0d);

			resultData.setCode(PosErrorCode.BILL_PAYMENT_ERROP.getNumber());
			resultData.setMsg(PosErrorCode.BILL_PAYMENT_ERROP.getMessage().replace("{0}", resultData.getMsg()));
		}
	}

	@Override
	public void findPaymentDebit(String tenancyId, int storeId, String billNum,String orderNum,String chanel,Double paymentAmount,JSONObject payment,Timestamp currentTime,JSONObject resultJson,Data resultData) throws Exception
	{	
		posNewCrmService.queryXMDCRMPayment(tenancyId, storeId, payment,resultData);
		
		if (Constant.CODE_SUCCESS == resultData.getCode())
		{
			// 修改付款状态
			JSONObject updatePaymentJson = new JSONObject();

			updatePaymentJson.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
			updatePaymentJson.put("id", payment.optInt("id"));
			paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", updatePaymentJson);
		}
		else if (Constant.CODE_CONN_EXCEPTION == resultData.getCode())
		{
			resultData.setCode(Constant.CODE_CONN_EXCEPTION);
			resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
		}
		else
		{
			// 修改付款状态
			JSONObject updatePaymentJson = new JSONObject();
			updatePaymentJson.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_FAILURE);
			updatePaymentJson.put("id", payment.optInt("id"));
			paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", updatePaymentJson);

			resultData.setCode(PosErrorCode.BILL_PAYMENT_ERROP.getNumber());
			resultData.setMsg(PosErrorCode.BILL_PAYMENT_ERROP.getMessage().replace("{0}", resultData.getMsg()));
		}
	}

	@Override
	public void paymentRefund(String tenancyId, int storeId, String billNum,String oldBillNum,String orderNum,String chanel,Double billPaymentAmount,JSONObject payment,Timestamp currentTime,JSONObject resultJson,Data resultData,String operType) throws Exception
	{
		String billId = payment.optString("bill_code");

		if (Tools.hv(billId) && !"null".equals(billId))
		{
			// 还未支付，取消支付，不用退款
			payment.put("chanel", chanel);
			posNewCrmService.cancelPayMent(tenancyId, storeId, payment, resultData);
		}

		if (Constant.CODE_SUCCESS == resultData.getCode())
		{
			JSONObject paymentJson = new JSONObject();
			paymentJson.put("payment_state", SysDictionary.PAYMENT_STATE_REFUND_COMPLETE);
			paymentJson.put("id", payment.optInt("id"));
			paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", paymentJson);
		}
		else if (Constant.CODE_CONN_EXCEPTION == resultData.getCode())
		{
			resultData.setCode(Constant.CODE_CONN_EXCEPTION);
			resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
		}
		else
		{
			JSONObject paymentJson = new JSONObject();
			paymentJson.put("payment_state", SysDictionary.PAYMENT_STATE_REFUND_FAILURE);
			paymentJson.put("id", payment.optInt("id"));
			paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", paymentJson);
		}
	}

	@Override
	public void paymentCancel(String tenancyId, int storeId, String billNum, String orderNum, String chanel, Double billPaymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData, String operType) throws Exception
	{
		String billId = payment.optString("bill_code");
		
		if (Tools.hv(billId) && !"null".equals(billId))
		{
			// 还未支付，取消支付，不用退款
			payment.put("chanel", chanel);
			posNewCrmService.cancelPayMent(tenancyId, storeId, payment, resultData);
		}
	}

	@Override
	public void paymentRegain(String tenancyId, int storeId, String billNum, String orderNum, String chanel, Double billPaymentAmount, JSONObject payment, Timestamp currentTime, JSONObject resultJson, Data resultData, String operType) throws Exception
	{
		String billId = payment.optString("bill_code");
		
		if (Tools.hv(billId) && !"null".equals(billId))
		{
			// 还未支付，取消支付，不用退款
			payment.put("chanel", chanel);
			posNewCrmService.cancelPayMent(tenancyId, storeId, payment, resultData);
		}
	}

	@Override
	public void paymentDebitResult(String tenancyId, int storeId, String billNum, JSONObject paymentJson) throws Exception
	{
		// TODO 自动生成的方法存根
		JSONObject updatePaymentJson = new JSONObject();
		updatePaymentJson.put("bill_code", paymentJson.optString("bill_code"));
		updatePaymentJson.put("payment_state", SysDictionary.PAYMENT_STATE_PAY_COMPLETE);
		updatePaymentJson.put("id", paymentJson.optInt("id"));
		paymentDao.updateIgnorCase(tenancyId, "pos_bill_payment", updatePaymentJson);
	}
}
