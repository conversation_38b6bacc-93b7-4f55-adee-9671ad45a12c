package com.tzx.pos.base.print;

/**
 *
 * <AUTHOR>
 * 2015年7月16日-下午7:38:38
 */
public class QueueUtil
{
	private static final PrintQueue queue = new PrintQueue();
	
	public static PrintQueue getQueue()
	{
		return queue;
	}
	
	public static void push(Object obj)
	{
		queue.push(obj);
	}
	
	public static Object pull()
	{
		return queue.pull();
	}
	
	public static int size()
	{
		return queue.size();
	}
}
