package com.tzx.base.bo.imp;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.tzx.base.bo.MqTtransferLogService;
import com.tzx.base.po.springjdbc.dao.MqTtransferLogDao;

@Service(value = MqTtransferLogService.NAME)
public class MqTtransferLogServiceImpl implements MqTtransferLogService{
	@Resource(name = MqTtransferLogDao.NAME)
	private MqTtransferLogDao mqTtransferLogDao;
	
	@Override
	public Integer addDownLogs(String tenancyId,Integer storeId,String url,
			Integer fileSize,Integer blockSize,String remark){
		return mqTtransferLogDao.addDownLogs(tenancyId, storeId, url, fileSize, blockSize, remark);
	}
}
