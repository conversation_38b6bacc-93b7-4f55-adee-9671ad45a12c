package com.tzx.member.crm.bo;

import net.sf.json.JSONObject;

import com.tzx.framework.common.entity.Data;
import com.tzx.member.common.entity.PosCustomerOperateListEntity;

public interface CustomerCardConsumeService extends CustomerService
{
	String	NAME	= "com.tzx.member.crm.bo.imp.CustomerCardConsumeServiceImp";
	
	/**
	 * 会员卡消费接口方法
	 * 
	 * @param param
	 * @param resData
	 */
	public void customerCardConsumePost(Data param, Data resData) throws Exception;

	/** 会员卡消费接口方法
	 * @param tenancyId
	 * @param storeId
	 * @param param
	 * @param resData
	 * @throws Exception
	 */
	public void customerCardConsumePost(String tenancyId, int storeId, JSONObject param, Data resData,String operType) throws Exception;

	/**会员卡消费查询
	 * @param tenancyId
	 * @param storeId
	 * @param customerOperate
	 * @return
	 * @throws Exception
	 */
	public Data queryCustomerCardConsume(String tenancyId, int storeId, PosCustomerOperateListEntity customerOperate)throws Exception;
	
	/**会员卡消费查询
	 * @param tenancyId
	 * @param storeId
	 * @param paramJson
	 * @return
	 * @throws Exception
	 */
	public Data queryCustomerCardConsume(String tenancyId, int storeId, JSONObject paramJson)throws Exception;
	
	/**
	 * 会员卡撤销消费接口方法
	 * 
	 * @param param
	 * @param resData
	 */
	public void customerCardCancelConsumePost(Data param, Data resData, String isCheck) throws Exception;

	/** 会员卡撤销消费接口方法
	 * @param tenancyId
	 * @param storeId
	 * @param param
	 * @param resData
	 * @param isCheckBillConsume 是否校验账单会员卡消费
	 * @throws Exception
	 */
	public void customerCardCancelConsumePost(String tenancyId, int storeId, JSONObject param, Data resData, String isCheckBillConsume,String operType) throws Exception;
	
	/**会员卡撤销消费查询
	 * @param tenancyId
	 * @param storeId
	 * @param customerOperate
	 * @return
	 * @throws Exception
	 */
	public Data queryCustomerCardCancelConsume(String tenancyId, int storeId, PosCustomerOperateListEntity customerOperate)throws Exception;

}
