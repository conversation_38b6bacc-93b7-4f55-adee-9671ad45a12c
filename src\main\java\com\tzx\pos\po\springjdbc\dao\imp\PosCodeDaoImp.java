package com.tzx.pos.po.springjdbc.dao.imp;

import java.sql.Timestamp;
import java.util.List;

import com.tzx.framework.common.util.EHCacheConfig;
import com.tzx.framework.common.util.EhcacheUtil;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Repository;

import com.tzx.framework.common.util.DateUtil;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.dao.imp.BaseDaoImp;
import com.tzx.pos.po.springjdbc.dao.PosCodeDao;

import net.sf.json.JSONObject;
@Repository(PosCodeDao.NAME)
public class PosCodeDaoImp extends BaseDaoImp implements PosCodeDao {

	@Override
	@Cacheable(value="currentValue",key="#type")
	public String  getDataValue(String tenantId, final String type, Integer store_id, final String prefix) throws Exception{
		Integer result =0;
		List<JSONObject> kk = this.query4Json(tenantId,"select current_value from sys_code_values where code_type='"+type+"' and store_id="+store_id+" and prefix='"+prefix+"' LIMIT 1");
		if(kk.size()>0)
		{
			result = kk.get(0).optInt("current_value");
		}
		return result+"";
	}

	@Override
	@CacheEvict(value="currentValue",key="#type")
	public void delete(String type) {
		// TODO Auto-generated method stub
		try {
			EhcacheUtil.removeAllElment("currentValue",type);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@Override
	@CachePut(value="currentValue",key="#type")
	public String update(String res,String type) {
		// TODO Auto-generated method stub
		try {
			EhcacheUtil.setValue("currentValue",type,res);
		} catch (Exception e) {
			e.printStackTrace();
		}
		 return res;
	}

	@Override
	public int updateDB(String tenantId, String type, Integer store_id, String prefix, Integer current_value) throws Exception {
		// TODO Auto-generated method stub
		
		StringBuilder sql = new StringBuilder("update sys_code_values set current_value=?  where code_type=? and store_id=? and prefix=?  ");
		int res=this.update(sql.toString(), new Object[]
				{current_value, type, store_id, prefix});
		return res;
	}

	@Override
	public int insertDB(String tenantId, String type, Integer store_id, String prefix, Integer current_value)
			throws Exception {
		// TODO Auto-generated method stub
		StringBuilder sql = new StringBuilder("insert into sys_code_values (code_type,store_id,prefix,current_value,step,last_updatetime) values (?,?,?,?,?,?)");
		Timestamp tt = DateUtil.getNowTimestamp();
		int res=this.update(sql.toString(), new Object[]
				{ type, store_id, prefix, current_value, 1, tt });
		return res;
	}

}
