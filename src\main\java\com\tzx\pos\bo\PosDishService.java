package com.tzx.pos.bo;

import java.util.List;

import net.sf.json.JSONObject;

import com.tzx.base.bo.PosCodeService;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.SystemException;

/**
 * 
 * <AUTHOR> 2015年6月24日-下午7:54:47
 */
public interface PosDishService extends PosBaseService
{
	String	NAME	= "com.tzx.pos.bo.imp.PosDishServiceImp";

	/**
	 * 开桌
	 * 
	 * @param param
	 * @param codeService
	 * @return
	 * @throws Exception
	 */
	@Deprecated
	public List<JSONObject> openTable(Data param, PosCodeService codeService) throws Exception;

	/**
	 * 开桌
	 * 
	 * @param param
	 * @param codeService
	 * @return
	 * @throws Exception
	 */
	@Deprecated
	public List<JSONObject> newOpenTable(Data param, PosCodeService codeService, JSONObject json) throws Exception;

	/**
	 * 下单、点菜
	 * 
	 * @param param
	 * @param json
	 * @param agent
	 * @return
	 * @throws Exception
	 */
	@Deprecated
	public Data orderDish(Data param, JSONObject json, String agent) throws Exception;

	//
	/**
	 * 下单、点菜
	 * 
	 * @param param
	 * @param json
	 * @param agent
	 * @return
	 * @throws Exception
	 */
	@Deprecated
	public Data newOrderDish(Data param, JSONObject json) throws Exception;

	/**
	 * 暂存菜单明细
	 * 
	 * @param param
	 * @param result
	 * @return
	 * @throws Exception
	 */
	@Deprecated
	public Data temporary(Data param, Data result) throws Exception;

	/**
	 * 获取暂存账单
	 * 
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	@Deprecated
	public void loadPauseBill(Data param, Data result) throws Exception;

	/**
	 * 账单查询
	 * 
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	@Deprecated
	public void findPosBill(Data param, Data result) throws Exception;

	/**
	 * 账单查询
	 * 
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void newFindPosBill(Data param, Data result) throws Exception;

	/** 账单查询
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void findPosBillList(Data param, Data result)throws Exception;
	
	/**
	 * 查看已下单菜品
	 * 
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> findOderDish(Data param) throws Exception;

	/**
	 * 账单菜品查询
	 * 
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void findBillItem(Data param, Data result) throws Exception;

	/**
	 * 退菜
	 * 
	 * @param param
	 * @param result
	 * @param json
	 * @throws Exception
	 */
	@Deprecated
	public void retreatFood(Data param, Data result, JSONObject json) throws Exception;

	/**
	 * 退菜
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param param
	 * @param result
	 * @param json
	 * @param agent
	 * @throws Exception
	 */
	@Deprecated
	public void retreatFood(String tenancyId, int storeId, List<?> param, Data result, JSONObject json, String agent) throws Exception;

	/**
	 * 账单折扣
	 * 
	 * @param param
	 * @return
	 * @throws Exception
	 */
	@Deprecated
	public Data billDiscount(Data param) throws Exception;
	
	/**
	 * 新账单折扣
	 * @param pram
	 * @return
	 * @throws SystemException
	 */
	@Deprecated
	public Data newBillDiscount(Data param) throws Exception;
	
	/**
	 * 单品折扣
	 * @param param
	 * @return
	 */
	public Data singleDiscount(Data param) throws Exception;
	
	/**
	 * 可优惠金额查询
	 * @param param
	 * @throws Exception
	 */
	public Data getSurplusDiscountAmount(Data param) throws Exception;

	/**
	 * 修改服务费
	 * 
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void modifyServiceFee(Data param, Data result) throws Exception;

	/**
	 * 取消服务费
	 * 
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void cancServiceFee(Data param, Data result) throws Exception;

	/**
	 * 修改人数
	 * 
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void modifyGuest(Data param, Data result) throws Exception;

	/**
	 * 单品备注/整单备注/单品做法
	 * 
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void changeRemarkAndZfkw(Data param, Data result) throws Exception;

	/**
	 * 取消整单备注
	 * 
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void cancBillTaste(Data param, Data result) throws Exception;

	/**
	 * 客人留言
	 * 
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void guestMsg(Data param, Data result) throws Exception;

	/**
	 * 修改服务员
	 * 
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void changeWaiter(Data param, Data result) throws Exception;

	/**
	 * 修改规格
	 * 
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void changeItemUnit(Data param, Data result) throws Exception;

	/**
	 * 修改菜品数量
	 * 
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void changeDishCount(Data param, Data result) throws Exception;

    /** 修改菜品辅助数量
     * @param param
     * @param result
     * @throws Exception
     */
    public void changeItemAssistNum(Data param, Data result) throws Exception;

	/**
	 * 修改菜品名称
	 * 
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void changeDishName(Data param, Data result) throws Exception;



	/**
	 * 催菜
	 * 
	 * @param param
	 * @param result
	 * @param json
	 * @throws Exception
	 */
	public void pushFood(Data param, Data result, JSONObject json) throws Exception;

	/**
	 * 等叫
	 * 
	 * @param param
	 * @param result
	 * @param json
	 * @throws Exception
	 */
	public void waitCall(Data param, Data result, JSONObject json) throws Exception;

	/**
	 * 起菜
	 * 
	 * @param param
	 * @param result
	 * @param json
	 * @throws Exception
	 */
	public void callupFood(Data param, Data result, JSONObject json) throws Exception;

	/**
	 * 整单取消
	 * 
	 * @param param
	 * @param codeService
	 * @param result
	 * @param printJson
	 * @throws Exception
	 */
	public void cancelBill(Data param, PosCodeService codeService, Data result, JSONObject printJson) throws Exception;
	
	
	/**
	  * @Description: 外卖部分退款隔日整单退后 对原订单冲减
	  * @Title:refundOrderCancelBill
	  * @param:@param param
	  * @param:@param codeService
	  * @param:@param result
	  * @param:@param printJson
	  * @param:@throws Exception
	  * @return: void
	  * @author: shenzhanyu
	  * @version: 1.0
	  * @Date: 2018年6月20日
	 */
	public void refundOrderCancelBill(Data param, PosCodeService codeService, Data result, JSONObject printJson) throws Exception;

	/**
	 * 账单恢复
	 * 
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void recoveryBill(Data param, Data result,JSONObject printJson) throws Exception;

	/**
	 * 获取最大序列
	 * 
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void getMaxSerial(Data param, Data result) throws Exception;

	/**
	 * 清除账单明细
	 * 
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void clearBillItem(Data param, Data result) throws Exception;

	/**
	 * 单品退菜
	 * 
	 * @param param
	 * @param codeService
	 * @param result
	 * @param json
	 * @throws Exception
	 */
	public void singleRetreatItem(Data param, PosCodeService codeService, Data result, JSONObject json) throws Exception;
	
	/**
	 * 单品退菜  2016-11-03 肖恒
	 * 
	 * @param param
	 * @param codeService
	 * @param result
	 * @param json
	 * @throws Exception
	 */
	public void singleRetreatItem(Data param, Data result, JSONObject json) throws Exception;

	/**
	 * 更改沽清数量
	 * 
	 * @param param
	 * @return
	 * @throws Exception
	 */
	@Deprecated
	public Data modSoldOutCount(Data param) throws Exception;
	/**
	 * 获取营业统计信息
	 *
	 * @param param
	 * @param result
	 * @return
	 * @throws Exception
	 */
	public void totalBillInfo(Data param, Data result) throws Exception;
	
	/**
	 * 获取KVS数据
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void getKvsInfo(Data param, Data result) throws Exception;
	
	/**
	 * 单品消单/整单消单
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void updateKvsMopState(Data param, Data result) throws Exception;
	
	/**
	 * 查询平均备餐时长
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void getMealConsume(Data param, Data result) throws Exception;
	
	/**
	 * 会员价查询（新）
	 * 
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	@Deprecated
	public void customerVipPrice(JSONObject param, Data result) throws Exception;
	
	/**
	 * 会员绑定
	 * 
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void customerCreditPlus(Data param, Data result) throws Exception;
	
	/**
	 *  复制账单
	 * @param param 输入参数
	 * @param result 输出参数
	 * @param obj 数据上传参数
	 */
	public void copyPostBill(Data param, Data result,JSONObject obj)throws Exception;
	
	/**
	 * 转台或并台
	 * 
	 * @param param
	 * @param result
	 * @param json
	 * @throws SystemException
	 */
	public void changeTable(Data param, Data result, JSONObject json) throws Exception;
	
	/**
	 * 单品转台
	 * 
	 * @param param
	 * @param result
	 * @param json
	 * @throws SystemException
	 */
	public void singleChangeTable(Data param, Data result, JSONObject json) throws SystemException;
	
	/**
	 * 复制桌位
	 * 
	 * @param param
	 * @param codeService
	 * @param result
	 * @param list
	 * @param upobj
	 * @throws SystemException
	 */
	public void posCopyTable(Data param, PosCodeService codeService, Data result, List<JSONObject> list,List<JSONObject> uplist) throws SystemException;
	
	/**
	 * 查询消单信息
	 * @param param
	 * @param result
	 * @throws SystemException
	 */
	public void findAllBill(Data param,Data result)throws SystemException;
	
	/**
	 * 检查是否消单
	 * @param param
	 * @param result
	 * @throws SystemException
	 */
	public void checkIsMop(Data param,Data result) throws SystemException;
	
	/**
	 * 查询已消单数据
	 * @param param
	 * @param result
	 * @throws SystemException
	 */
	public void getmopedData(Data param,Data result) throws SystemException;

	/**
	 * 获取取餐号
	 * @param param
	 * @param result
	 * @return
	 * @throws SystemException
	 */
	public String getTakeMealNum(Data param,Data result) throws SystemException;
	
	/**
	 * 开桌优化
	 *
	 * @param param
	 * @param codeService
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> newestOpenTable(Data param, PosCodeService codeService, JSONObject json) throws Exception;

	/**
	 * 下单优化
	 * @param param
	 * @param json
	 * @return
	 * @throws Exception
	 */
	public Data newestOrderDish(Data param, JSONObject json) throws Exception;
		
	/**
	 * 历史账单查询
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void queryPosBillHistory(Data param, Data result) throws Exception;


	/**
	 * 出餐员出菜
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public  void dishOut(Data param,Data result) throws  SystemException;
	
	/**
	 * 预点确认
	 * @param param
	 * @param result
	 * @throws SystemException
	 */
	public void billBookedOrder(Data param, Data result, JSONObject json) throws SystemException;
	
	/**
	 * 预点账单查询
	 * 
	 * @param param
	 * @param result
	 * @throws Exception
	 */
	public void newFindPosBillBooked(Data param, Data result) throws Exception;

    /**
     * 修改就餐类型
     *
     * @param param
     * @param result
     * @throws Exception
     */
    void changeDinnerType(Data param, Data result) throws Exception;

    /**
     * 菜品实时流水查询
     * @param param
     * @param result
     * @throws Exception
     */
    void queryItemList(Data param, Data result) throws Exception;

    /** 修改单头(人数、服务员、就餐类型)
     * @param param
     * @param result
     * @throws Exception
     */
    void changeBillHeader(Data param, Data result) throws Exception;

    /**  修改服务员
     * @param param
     * @param result
     * @throws Exception
     */
    void changeItemWaiter(Data param, Data result) throws Exception;


	/**
	 * 豪享来并台
	 * @param param
	 * @param result
	 * @param json
	 * @throws SystemException
	 */
	public void combineTable(Data param, Data result, JSONObject json) throws Exception;

	/**
	 * 豪享来取消并台
	 * @param param
	 * @param result
	 * @param json
	 * @throws SystemException
	 */
	public void combineTableCancle(Data param, Data result, JSONObject json) throws Exception;

	/**
	 * 校验小程序账单是否并台
	 * @param tenancyId
	 * @param storeId
	 * @param bill_num
	 * @return
	 * @throws Exception
	 */
	public JSONObject isCombineTableOfBillNum(String tenancyId, int storeId, String bill_num) throws Exception;
}
