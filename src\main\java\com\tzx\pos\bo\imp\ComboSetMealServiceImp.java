package com.tzx.pos.bo.imp;

import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.DoubleHelper;
import com.tzx.framework.common.util.Tools;
//import com.tzx.framework.common.util.UUIDUtil;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.base.util.ReqDataUtil;
import com.tzx.pos.bo.ComboSetMealService;
import com.tzx.pos.bo.dto.PosBillItem;
import com.tzx.pos.po.springjdbc.dao.ComboSetMealDao;
import com.tzx.pos.po.springjdbc.dao.PosDishDao;

import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by kevin on 2017-11-03.
 */
@Service(ComboSetMealService.NAME)
public class ComboSetMealServiceImp extends PosBaseServiceImp implements ComboSetMealService {

    private static final Logger logger = Logger.getLogger(ComboSetMealServiceImp.class);

    private int scale = 2;

    private int defaultScale = 4;

    @Resource(name = ComboSetMealDao.NAME)
    private ComboSetMealDao comboSetMealDao;

    @Resource(name = PosDishDao.NAME)
    private PosDishDao posDishDao;

    /**
     *  设置组合套餐
     * @param param
     * @param result
     * @return
     * @throws SystemException
     * @throws Exception
     */
    public Data setMeal(Data param, Data result) throws SystemException {
        logger.info("使用组合套餐start===========================================");
        String tenancyId = param.getTenancy_id();// 门店id
        Integer storeId = param.getStore_id();// 机构ID
        String source = param.getSource();//来源
        Map<String, Object> map = ReqDataUtil.getDataMap(param);
        Integer mode = ParamUtil.getIntegerValue(map, "mode", false,null);// 操作类型0增加或修改1删除数据
        Integer comboSetId = ParamUtil.getIntegerValue(map, "combo_set_id", false,null); //组合套餐主项rwid
        Double  comboNum = ParamUtil.getDoubleValue(map, "combo_num", false,null);//组合套餐主项数量
        String addList = ParamUtil.getStringValue(map, "addlist", false,null);//添加组合套餐单品集合rwid
        String delList = ParamUtil.getStringValue(map, "dellist", false,null);//删除组合套餐单品集合rwid
        Integer shiftId = ParamUtil.getIntegerValue(map, "shift_id", true, PosErrorCode.NOT_NULL_SHIFT_ID);//班次id
        Date reportDate = ParamUtil.getDateValue(map, "report_date", true, PosErrorCode.NOT_NULL_REPORT_DATE);//报表日期
        String optNum = ParamUtil.getStringValue(map, "opt_num", false, null);//操作员编号
        String waiterNum = ParamUtil.getStringValue(map, "waiter_num", false, null);//服务员编号
        String posNum = ParamUtil.getStringValue(map, "pos_num", true, PosErrorCode.NOT_EXISTS_POSNUM);//收款机号
        //组合套餐增加或修改
        if(mode.intValue() == 0) {
            if(StringUtils.isNotEmpty(addList)) {
                try {
                    //套餐主项
                    PosBillItem posBillItemSetmeal = comboSetMealDao.getPosBillItemByRWID(comboSetId);
                    String billno = posBillItemSetmeal.getBill_num();
                    List<JSONObject> posBillItemComboList = comboSetMealDao.getAllPosBillItemByBillNum(tenancyId, storeId, billno);
                    if (posBillItemComboList != null && posBillItemComboList.size() > 0) {
                        this.clearPosBillItem(tenancyId, storeId, posBillItemSetmeal, posBillItemComboList);
                    }
                    double itemCountSetmeal = Double.parseDouble(posBillItemSetmeal.getItem_count() == null ? "1" : posBillItemSetmeal.getItem_count());
                    double itemPriceSetmeal = Double.parseDouble(posBillItemSetmeal.getItem_price() == null ? "0" : posBillItemSetmeal.getItem_price());
                    double itemAmountSetmeal = Double.parseDouble(posBillItemSetmeal.getItem_amount() == null ? "0" : posBillItemSetmeal.getItem_amount());
                    int itemSerialSetmeal = Integer.parseInt(posBillItemSetmeal.getItem_serial() == null ? "1" : posBillItemSetmeal.getItem_serial());
                    int itemIdSsetmeal = Integer.parseInt(posBillItemSetmeal.getItem_id() == null ? "1" : posBillItemSetmeal.getItem_id());
                    int discountModeIdSsetmeal = Integer.parseInt(posBillItemSetmeal.getDiscount_mode_id() == null ? "0" : posBillItemSetmeal.getDiscount_mode_id());
                    double discountRateSsetmeal = Double.parseDouble(posBillItemSetmeal.getDiscount_rate() == null ? "100" : posBillItemSetmeal.getDiscount_rate());
                    //套餐主项口味做法金额
                    double methodAmountSetmeal = Double.parseDouble(posBillItemSetmeal.getMethod_money() == null ? "0" : posBillItemSetmeal.getMethod_money());
                    double methodAmountSetmealFinal = 0d;
                    double rate = 1d;
                    double discountAmountSetmeal = 0d, discountrAmountSetmeal = 0d;
                    //有折扣折让计算主项折扣、折让金额，排除单品折扣
                    if (discountModeIdSsetmeal != 0 && discountModeIdSsetmeal != SysDictionary.DISCOUNT_MODE_10) {
                        if(discountRateSsetmeal != 100) {
                            rate = DoubleHelper.div(discountRateSsetmeal, 100d, defaultScale);
                        }
                        methodAmountSetmeal = DoubleHelper.mul(methodAmountSetmeal, rate, scale);
                        discountAmountSetmeal = Double.parseDouble(posBillItemSetmeal.getDiscount_amount() == null ?
                                "0" : posBillItemSetmeal.getDiscount_amount());//主项折扣金额
                        discountrAmountSetmeal = Double.parseDouble(posBillItemSetmeal.getDiscountr_amount() == null ?
                            "0" : posBillItemSetmeal.getDiscountr_amount());//主项折让金额
                    }
//                    itemPriceSetmeal = DoubleHelper.div(methodAmountSetmealFinal, itemCountSetmeal, scale);
                    posBillItemSetmeal.setItem_property(SysDictionary.ITEM_PROPERTY_SETMEAL);
                    posBillItemSetmeal.setSetmeal_id(itemIdSsetmeal + "");
                    posBillItemSetmeal.setCombo_prop(itemSerialSetmeal + "");
                    posBillItemSetmeal.setRwid(comboSetId + "");
//                    posBillItemSetmeal.setItem_amount(methodAmountSetmealFinal + "");
//                    posBillItemSetmeal.setItem_price(itemPriceSetmeal + "");
                    //套餐单品集合
                    List<PosBillItem> posBillItemList = comboSetMealDao.getPosBillItemListByRWIDS(addList);
                    //组合套餐集合
                    List<PosBillItem> setmealItemList = new ArrayList<PosBillItem>();
                    Double meallistItemPriceTotal =0d;//菜品金额
                    Double meallistItemDiscountTotal = 0d;//折扣金额
                    Double meallistItemDiscountrTotal = 0d;//折让金额
                    //最大价格单品
                    PosBillItem maxItem = null;
                    double methodAmountTotal = 0d;
                    int itemSerialMeallistmin = Integer.MAX_VALUE;
                    if(itemSerialMeallistmin > itemSerialSetmeal) {
                        itemSerialMeallistmin = itemSerialSetmeal;
                    }
                    for (PosBillItem posBillItem: posBillItemList) {
                        int itemSerial = Integer.parseInt(posBillItem.getItem_serial() == null ? "0" : posBillItem.getItem_serial());
                        //获取序号最小值
                        itemSerialMeallistmin = itemSerialMeallistmin > itemSerial ? itemSerial : itemSerialMeallistmin;
                        int rwid = Integer.parseInt(posBillItem.getRwid() == null ? "0" : posBillItem.getRwid());
                        int itemId = Integer.parseInt(posBillItem.getItem_id() == null ? "0" : posBillItem.getItem_id());
                        int discountModeId =  Integer.parseInt(posBillItem.getDiscount_mode_id() == null ? "0" : posBillItem.getDiscount_mode_id());
                        double itemCount = Double.parseDouble(posBillItem.getItem_count() == null ? "1" : posBillItem.getItem_count());
                        double itemPrice = Double.parseDouble(posBillItem.getItem_price() == null ? "0" : posBillItem.getItem_price());
                        double itemAmount = Double.parseDouble(posBillItem.getItem_amount() == null ? "0" : posBillItem.getItem_amount());
                        double discountAmount = Double.parseDouble(posBillItem.getDiscount_amount() == null ? "0" : posBillItem.getDiscount_amount());
                        double discountrAmount = Double.parseDouble(posBillItem.getDiscountr_amount() == null ? "0" : posBillItem.getDiscountr_amount());
                        double methodMoney = Double.parseDouble(posBillItem.getMethod_money() == null ? "0" : posBillItem.getMethod_money());
                        //组合套餐明细口味做法金额
                        methodAmountSetmealFinal = DoubleHelper.add(methodAmountSetmealFinal, methodMoney, scale);
//                        double methodAmount = comboSetMealDao.getMethodAmountByRWID(tenancyId, storeId, rwid);
//                        methodAmountTotal = methodAmountTotal + methodAmount;
//                        itemAmount = DoubleHelper.add(itemAmount, methodAmount, scale);
//                        Double assistNum = DoubleHelper.div(itemCount, itemCountSetmeal, scale);
//                        itemAmount = DoubleHelper.mul(itemAmount, DoubleHelper.div(itemCount, assistNum, scale), scale);
                        //有折扣折让计算主项折扣、折让金额，排除单品折扣
                        if(discountModeId != 0 && discountModeId != SysDictionary.DISCOUNT_MODE_10) {
//                            discountAmount = DoubleHelper.mul(discountAmount, DoubleHelper.div(itemCount, assistNum, scale), scale);
                            meallistItemDiscountTotal = DoubleHelper.add(meallistItemDiscountTotal, discountAmount, scale);
//                            discountrAmount = DoubleHelper.mul(discountrAmount, DoubleHelper.div(itemCount, assistNum, scale), scale);
                            meallistItemDiscountrTotal = DoubleHelper.add(meallistItemDiscountrTotal, discountrAmount, scale);
                        }
                        //有做法加价取原价格
                        if(methodMoney > 0) {
                            itemAmount = DoubleHelper.mul(itemPrice, itemCount, scale);
                            posBillItem.setItem_amount(itemAmount + "");
                        }else {
                            posBillItem.setItem_amount(itemAmount + "");
                        }
                        meallistItemPriceTotal = DoubleHelper.add(meallistItemPriceTotal, itemAmount, scale);
                        posBillItem.setDiscount_amount(discountAmount + "");
                        posBillItem.setDiscountr_amount(discountrAmount + "");
                        posBillItem.setMethod_money(methodMoney + "");
                        posBillItem.setDiscount_mode_id(discountModeId + "");
                        setmealItemList.add(posBillItem);
                        if(null != maxItem) {
                            Double maxItemPrice = Double.parseDouble(maxItem.getItem_amount() == null ? "0" : maxItem.getItem_amount());
                            Double maxDiscountrAmount = Double.parseDouble(maxItem.getDiscountr_amount() == null ? "0" : maxItem.getDiscountr_amount());
                            Double maxDiscountAmount = Double.parseDouble(maxItem.getDiscount_amount() == null ? "0" : maxItem.getDiscount_amount());
                            if(itemAmount > maxItemPrice) {
                                maxItem = posBillItem;
                            }
                            if(discountrAmount > maxDiscountrAmount) {
                                maxItem = posBillItem;
                            }
                        }
                        else {
                            maxItem = posBillItem;
                        }
                    }
//                    itemAmountSetmeal = DoubleHelper.add(itemAmountSetmeal, methodAmountTotal, scale);
//                    posBillItemSetmeal.setItem_price(itemPriceSetmeal + "");
                    posBillItemSetmeal.setMethod_money(methodAmountSetmealFinal + "");
                    double detailsItemPriceTotal =0d;
                    double detailsDiscountrAmountTotal = 0d;
                    double detailsDiscountAmountTotal = 0d;
                    for(PosBillItem posBillItem : setmealItemList) {
                        Double detailsItemAmount = Double.parseDouble(posBillItem.getItem_amount() == null ? "0" : posBillItem.getItem_amount());
                        Double methodMoney = Double.parseDouble(posBillItem.getMethod_money()== null ? "0" : posBillItem.getMethod_money());
                        int discountModeId =  Integer.parseInt(posBillItem.getDiscount_mode_id() == null ? "0" : posBillItem.getDiscount_mode_id());
                        if(meallistItemPriceTotal > 0) {
//                            detailsItemAmount = DoubleHelper.mul(DoubleHelper.div(detailsItemAmount, meallistItemPriceTotal, defaultScale), itemAmountSetmeal, scale);
                                detailsItemAmount = DoubleHelper.div(DoubleHelper.mul(detailsItemAmount, itemAmountSetmeal, scale),meallistItemPriceTotal, scale);
                        }
                        else {
                            detailsItemAmount = 0d;
                        }
                        detailsItemPriceTotal = DoubleHelper.add(detailsItemPriceTotal, detailsItemAmount, scale);
                        //有折扣折让计算主项折扣、折让金额，排除单品折扣
                        double discountAmount = 0d;
                        if(discountModeId != 0 && discountModeId != SysDictionary.DISCOUNT_MODE_10) {
                            //折扣金额
                            Double detailsItemDiscount = Double.parseDouble(posBillItem.getDiscount_amount() == null ? "0" : posBillItem.getDiscount_amount());
                            if (meallistItemDiscountTotal > 0) {
                                detailsItemDiscount = DoubleHelper.mul(DoubleHelper.div(detailsItemDiscount, meallistItemDiscountTotal, defaultScale), discountAmountSetmeal, scale);
                            } else {
                                detailsItemDiscount = 0d;
                            }
                            detailsDiscountAmountTotal = DoubleHelper.add(detailsDiscountAmountTotal, detailsItemDiscount, scale);
                            posBillItem.setDiscount_amount(detailsItemDiscount + "");
                            //折让金额
                            Double detailsItemDiscountr = Double.parseDouble(posBillItem.getDiscountr_amount() == null ? "0" : posBillItem.getDiscountr_amount());
                            if (meallistItemDiscountrTotal > 0) {
                                detailsItemDiscountr = DoubleHelper.mul(DoubleHelper.div(detailsItemDiscountr, meallistItemDiscountrTotal, defaultScale), discountrAmountSetmeal, scale);
                            } else {
                                detailsItemDiscountr = 0d;
                            }
                            detailsDiscountrAmountTotal = DoubleHelper.add(detailsDiscountrAmountTotal, detailsItemDiscountr, scale);
                            posBillItem.setDiscountr_amount(detailsItemDiscountr + "");
                        }
                        double detailsItemCount = Double.parseDouble(posBillItem.getItem_count() == null ? "1" : posBillItem.getItem_count());
                        Double detailsItemPrice = DoubleHelper.div(detailsItemAmount, detailsItemCount, defaultScale);
                        posBillItem.setItem_price(detailsItemPrice + "");
                        //有做法加价加上
                        if(methodMoney > 0) {
                            detailsItemAmount = DoubleHelper.add(methodMoney, detailsItemAmount, scale);
                            posBillItem.setItem_amount(detailsItemAmount + "");
                        }else {
                            posBillItem.setItem_amount(detailsItemAmount + "");
                        }
                        int detailsItemSerial =  Integer.parseInt(posBillItem.getItem_serial() == null ? "1" : posBillItem.getItem_serial());
                        posBillItem.setCombo_prop(detailsItemSerial + "");
                        posBillItem.setSetmeal_id(itemIdSsetmeal + "");
                        posBillItem.setItem_serial(itemSerialMeallistmin + "");
                        posBillItem.setItem_property(SysDictionary.ITEM_PROPERTY_MEALLIST);
                    }
                    if(detailsItemPriceTotal != itemAmountSetmeal) {
                        double detailsItemCount = Double.parseDouble(maxItem.getItem_count() == null ? "1" : maxItem.getItem_count());
                        Double detailsItemAmount = Double.parseDouble(maxItem.getItem_amount() == null ? "0" : maxItem.getItem_amount());
                        detailsItemAmount = DoubleHelper.add(detailsItemAmount, DoubleHelper.sub(itemAmountSetmeal, detailsItemPriceTotal, scale), scale);
                        maxItem.setItem_amount(detailsItemAmount + "");
                        Double detailsItemPirce = DoubleHelper.div(detailsItemAmount, detailsItemCount, defaultScale);
                        maxItem.setItem_price(detailsItemPirce + "");
                    }
                    if(discountModeIdSsetmeal != 0) {
                        if (detailsDiscountAmountTotal != discountAmountSetmeal) {
                            double detailDiscountAmount = Double.parseDouble(maxItem.getDiscount_amount() == null ? "0" : maxItem.getDiscount_amount());
                            detailDiscountAmount = DoubleHelper.add(detailDiscountAmount, DoubleHelper.sub(discountAmountSetmeal, detailsDiscountAmountTotal, scale), scale);
                            maxItem.setDiscount_amount(detailDiscountAmount + "");
                         }
                        if (detailsDiscountrAmountTotal != discountrAmountSetmeal) {
                            double detailDiscountrAmount = Double.parseDouble(maxItem.getDiscountr_amount() == null ? "0" : maxItem.getDiscountr_amount());
                            detailDiscountrAmount = DoubleHelper.add(detailDiscountrAmount, DoubleHelper.sub(discountrAmountSetmeal, detailsDiscountrAmountTotal, scale), scale);
                            maxItem.setDiscountr_amount(detailDiscountrAmount + "");
                        }
                    }
//                    itemPriceSetmeal = DoubleHelper.div(itemAmountSetmeal, itemCountSetmeal, scale);
                    itemAmountSetmeal = DoubleHelper.add(DoubleHelper.mul(itemCountSetmeal, itemPriceSetmeal, scale), methodAmountSetmealFinal, scale);
                    posBillItemSetmeal.setItem_amount(itemAmountSetmeal + "");
                    posBillItemSetmeal.setItem_serial(itemSerialMeallistmin + "");
                    setmealItemList.add(posBillItemSetmeal);
                    // 复制账单明细到pos_bill_item_combo，如果存在先删除
                    List<JSONObject> posBillItems = comboSetMealDao.getAllPosBillItemByBillNum(tenancyId, storeId, billno);
                    this.deletePosBillItem(tenancyId, posBillItems);
                    List<JSONObject> items = posDishDao.getPosBillItemByBillNum(tenancyId, storeId, billno);
                    if (items != null && items.size() > 0) {
                        for(int i = 0 ; i < items.size(); i++) {
                            JSONObject item = items.get(i);
                            int rwid = item.optInt("rwid");
                            double itemCount = item.optInt("item_count");
                            //套餐主项价格处理做法加价
                            if(comboSetId.intValue() == rwid) {
                                item.put("item_amount", itemAmountSetmeal);
                                item.put("method_money",methodAmountSetmealFinal);
                            }
                            else if(itemCount > 1) {
                                double itemPrice = item.optDouble("item_price");
                                double methodMoney = item.optDouble("method_money");
//                                double itemAmount = item.optDouble("item_amount");
                                double itemAmount = DoubleHelper.add(DoubleHelper.mul(itemPrice, itemCount, scale), methodMoney, scale);
                                item.put("item_amount", itemAmount);
                            }
                            item.put("combo_prop", i + 1);
//                            item.put("default_state", "Y");
                            posDishDao.insertIgnorCase(tenancyId, "pos_bill_item_combo", item);
                        }
                    }
                    //更新pos_bill_item
                    comboSetMealDao.updatePosBillItem4Setmeal(setmealItemList);
                    this.calcAmount(tenancyId, storeId, billno);
                    double discoutrAmount = comboSetMealDao.getBillDiscountrByBillNum(tenancyId, storeId, billno);
                    double billdiscoutrAmount = DoubleHelper.sub(discoutrAmount, discountrAmountSetmeal, scale);
                    posBillItemSetmeal.setDiscountr_amount(billdiscoutrAmount + "");
                    comboSetMealDao.updatePosBill(posBillItemSetmeal);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        //组合套餐删除
        else {
            if(StringUtils.isNotEmpty(delList)) {
                try {
                    //套餐主项
                    PosBillItem posBillItemSetmeal = comboSetMealDao.getPosBillItemByRWID(comboSetId);
                    String billno = posBillItemSetmeal.getBill_num();
                    List<JSONObject> posBillItemList = comboSetMealDao.getAllPosBillItemByBillNum(tenancyId, storeId, billno);
                    this.updatePosBillItem(tenancyId, storeId, posBillItemSetmeal, posBillItemList);
                    //删除pos_bill_item_combo表
                    this.deletePosBillItem(tenancyId, posBillItemList);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return null;
    }

    public void updatePosBillItem(String tenancyId, int storeId, PosBillItem posBillItemSetmeal, List<JSONObject> posBillItemList) throws Exception {
        String billno = posBillItemSetmeal.getBill_num();
        //重新计算账单折让金额
        double discountrAmountTotal = 0d;
        double discountRateAfter = 100d;
        double methodMoneyTotal = 0d;
        if (posBillItemList != null && posBillItemList.size() > 0) {
            for (JSONObject jsonObject : posBillItemList) {
                JSONObject posBillItem = new JSONObject();
                //组合套餐明细重置成菜品默认金额
                if(SysDictionary.ITEM_PROPERTY_MEALLIST.equals(jsonObject.optString("item_property"))) {
                    int itemUnitId = jsonObject.optInt("item_unit_id");
                    int itemId = jsonObject.optInt("item_id");
                    double itemCount = jsonObject.optDouble("item_count");
                    JSONObject hqItemUnit = comboSetMealDao.getHqItemUnit(tenancyId, itemUnitId, itemId);
                    posBillItem.put("item_price", hqItemUnit.optDouble("standard_price"));
                    posBillItem.put("item_amount", DoubleHelper.mul(hqItemUnit.optDouble("standard_price"), itemCount, scale));
                }else {
                    double methodMoney = jsonObject.optDouble("method_money");
                    if(methodMoney > 0) {
                        posBillItem.put("method_money", "0");
                        int rwid = jsonObject.optInt("rwid");
                        double itemCount = jsonObject.optDouble("item_count");
                        double itemAmount = jsonObject.optDouble("item_amount");
                        double itemAmountAfter = DoubleHelper.sub(itemAmount, methodMoney, scale);
                        posBillItem.put("item_amount", itemAmountAfter);
                        double itemPrice = DoubleHelper.div(itemAmountAfter, itemCount, scale);
                        posBillItem.put("item_price", itemPrice);
//                        comboSetMealDao.deletePosKwzfItemByRWID(tenancyId, storeId, rwid);
                    }else {
                        posBillItem.put("item_price", jsonObject.optDouble("item_price"));
                        posBillItem.put("item_amount", jsonObject.optDouble("item_amount"));
                    }

                }
                int discountModeId = jsonObject.optInt("discount_mode_id");
                posBillItem.put("id", jsonObject.optInt("id"));
                posBillItem.put("item_property", SysDictionary.ITEM_PROPERTY_SINGLE);
                posBillItem.put("setmeal_id", "0");
                posBillItem.put("item_serial", jsonObject.optInt("combo_prop"));
                posBillItem.put("combo_prop", "");
                //排除单品折扣
                if(discountModeId != SysDictionary.DISCOUNT_MODE_10){
                    posBillItem.put("discount_amount", jsonObject.optDouble("discount_amount"));
                    posBillItem.put("discountr_amount", jsonObject.optDouble("discountr_amount"));
                    discountrAmountTotal = discountrAmountTotal + jsonObject.optDouble("discountr_amount");
                    posBillItem.put("discount_rate", jsonObject.optDouble("discount_rate"));
                    discountRateAfter = jsonObject.optDouble("discount_rate");
                }else {
                    posBillItem.put("discount_amount", 0);
                    posBillItem.put("discountr_amount",0);
                    posBillItem.put("discount_rate", 100);
                }
                posDishDao.updateIgnorCase(tenancyId, "pos_bill_item", posBillItem);
            }
            posBillItemSetmeal.setDiscountr_amount(discountrAmountTotal + "");
            posBillItemSetmeal.setDiscount_rate(discountRateAfter + "");
            comboSetMealDao.updatePosBill(posBillItemSetmeal);
            this.calcAmount(tenancyId, storeId, billno);
        }
    }

    public void clearPosBillItem(String tenancyId, int storeId, PosBillItem posBillItemSetmeal, List<JSONObject> posBillItemList) throws Exception {
        String billno = posBillItemSetmeal.getBill_num();
        //更新账单折让金额
        double discountrAmountTotal = 0d;
        double discountRateAfter = 100d;
        if (posBillItemList != null && posBillItemList.size() > 0) {
            for (JSONObject jsonObject : posBillItemList) {
                JSONObject posBillItem = new JSONObject();
                posBillItem.put("id", jsonObject.optInt("id"));
                posBillItem.put("item_price", jsonObject.optDouble("item_price"));
                posBillItem.put("item_amount", jsonObject.optDouble("item_amount"));
                posBillItem.put("item_property", jsonObject.optString("item_property"));
                posBillItem.put("setmeal_id", jsonObject.optInt("setmeal_id"));
                posBillItem.put("item_serial", jsonObject.optInt("item_serial"));
                posBillItem.put("discount_amount", jsonObject.optDouble("discount_amount"));
                posBillItem.put("discountr_amount", jsonObject.optDouble("discountr_amount"));
                posBillItem.put("discount_rate", jsonObject.optDouble("discount_rate"));
                posBillItem.put("combo_prop", "");
                posDishDao.updateIgnorCase(tenancyId, "pos_bill_item", posBillItem);
            }
            comboSetMealDao.updatePosBill(posBillItemSetmeal);
        }
    }

    public void clearPosBillItem(String tenancyId, int storeId, String billNum, PosBillItem posBillItemSetmeal) throws Exception
    {
        Integer itemId = Integer.parseInt(posBillItemSetmeal.getItem_id());
        Integer itemSerial = Integer.parseInt(posBillItemSetmeal.getItem_serial());

        // 查询账单明细数据
        List<JSONObject> itemList = comboSetMealDao.getPosBillItemByBillnum(tenancyId, storeId, billNum);

        List<JSONObject> itemMealList = new ArrayList<JSONObject>();// 组合套餐的明细项
        List<Integer> rwidList = new ArrayList<Integer>();// 组合套餐明细的RWID
        List<Integer> itemSerialList = new ArrayList<Integer>();
        for (JSONObject itemJson : itemList)
        {
            String itemProperty = itemJson.optString("item_property");
            if (SysDictionary.ITEM_PROPERTY_MEALLIST.equals(itemProperty) && itemId.equals(itemJson.optInt("setmeal_id")) && itemSerial.equals(itemJson.optInt("setmeal_rwid")))
            {
                itemMealList.add(itemJson);
                rwidList.add(itemJson.optInt("rwid"));
            }

            if(SysDictionary.ITEM_PROPERTY_MEALLIST.equals(itemProperty)==false)
            {
                itemSerialList.add(itemJson.optInt("item_serial"));
            }
        }

        if (itemMealList != null && itemMealList.size() > 0)
        {
            // 查询账单信息
            JSONObject billJson = comboSetMealDao.getPosBillByBillnum(tenancyId, storeId, billNum);

//            List<JSONObject> itemComboList = comboSetMealDao.getPosBillItemComboByItemList(tenancyId, storeId, rwidList);

            List<PosBillItem> updateItemList = new ArrayList<PosBillItem>();
            for (JSONObject jsonObject : itemMealList)
            {
                Integer rwid = jsonObject.optInt("rwid");
                Integer itemMealSerial = jsonObject.optInt("combo_prop");

                PosBillItem posBillItem = new PosBillItem();
                posBillItem.setDiscount_rate(String.valueOf(billJson.optDouble("discount_rate")));
                posBillItem.setDiscount_mode_id(String.valueOf(billJson.optInt("discount_mode_id")));
                posBillItem.setDiscount_reason_id(String.valueOf(billJson.optInt("discount_reason_id")));
                posBillItem.setRwid(String.valueOf(rwid));
                posBillItem.setItem_name(jsonObject.optString("item_name").replaceAll("　", ""));
                posBillItem.setItem_property(SysDictionary.ITEM_PROPERTY_SINGLE);
                posBillItem.setSetmeal_rwid("0");
                posBillItem.setSetmeal_id("0");
                posBillItem.setAssist_item_id("0");
                posBillItem.setCombo_prop("0");
                posBillItem.setSingle_amount("0");

                Double itemPrice = jsonObject.optDouble("origin_item_price");
//                for (JSONObject itemComboJson : itemComboList)
//                {
//                    if (rwid.equals(itemComboJson.optInt("rwid")))
//                    {
//                        itemPrice = itemComboJson.optDouble("item_price");
//                    }
//                }
                posBillItem.setItem_price(String.valueOf(itemPrice));

                itemMealSerial = getItemSerial(itemMealSerial, itemSerialList);
                itemSerialList.add(itemMealSerial);
                posBillItem.setItem_serial(String.valueOf(itemMealSerial));

                updateItemList.add(posBillItem);
            }

            posBillItemSetmeal.setItem_property(SysDictionary.ITEM_PROPERTY_SINGLE);
            posBillItemSetmeal.setSetmeal_rwid("0");
            posBillItemSetmeal.setSetmeal_id("0");
            posBillItemSetmeal.setAssist_item_id("0");
            posBillItemSetmeal.setCombo_prop("0");
            updateItemList.add(posBillItemSetmeal);

            comboSetMealDao.updatePosBillItemForSingle(tenancyId, storeId, billNum, updateItemList);

//            this.deletePosBillItem(tenancyId, itemComboList);
        }
    }


    private static Integer getItemSerial(int itemSerial,List<Integer> itemSerialList)
    {
        for (Integer i : itemSerialList)
        {
            if (itemSerial == i)
            {
                return itemSerial = getItemSerial(itemSerial+1, itemSerialList);
            }
        }
        return itemSerial;
    }

    public void deletePosBillItem(String tenancyId, List<JSONObject> posBillItems) throws  Exception {
        List<JSONObject> list = new ArrayList<JSONObject>();
        if(posBillItems != null && posBillItems.size() > 0) {
            for (JSONObject jsonObject : posBillItems) {
                JSONObject json = new JSONObject();
                json.put("id", jsonObject.optInt("id"));
                list.add(json);
            }
            posDishDao.deleteBatchIgnorCase(tenancyId, "pos_bill_item_combo", list);
        }
    }

	@Override
	public void comboSetMeal(Data param, Data result) throws Exception
	{
		String tenancyId = param.getTenancy_id();// 门店id
		Integer storeId = param.getStore_id();// 机构ID
//		String source = param.getSource();// 来源

		Map<String, Object> map = ReqDataUtil.getDataMap(param);
		Integer mode = ParamUtil.getIntegerValue(map, "mode", false, null);// 操作类型0增加或修改1删除数据
		Integer comboSetId = ParamUtil.getIntegerValue(map, "combo_set_id", false, null); // 组合套餐主项rwid
//		Double comboNum = ParamUtil.getDoubleValue(map, "combo_num", false, null);// 组合套餐主项数量
		String addList = ParamUtil.getStringValue(map, "addlist", false, null);// 添加组合套餐单品集合rwid
		String delList = ParamUtil.getStringValue(map, "dellist", false, null);// 删除组合套餐单品集合rwid

		Date reportDate = ParamUtil.getDateValue(map, "report_date", true, PosErrorCode.NOT_NULL_REPORT_DATE);// 报表日期
		Integer shiftId = ParamUtil.getIntegerValue(map, "shift_id", true, PosErrorCode.NOT_NULL_SHIFT_ID);// 班次id
		String optNum = ParamUtil.getStringValue(map, "opt_num", true, PosErrorCode.NOT_NULL_OPT_NUM);// 操作员编号
		String posNum = ParamUtil.getStringValue(map, "pos_num", true, PosErrorCode.NOT_NULL_POS_NUM);// 收款机号
		String billNum = ParamUtil.getStringValue(map, "bill_num", true, PosErrorCode.NOT_NULL_BILL_NUM);

		// 组合套餐增加或修改
		if (mode.intValue() == 0)
		{
			if (StringUtils.isNotEmpty(addList))
			{
				try
				{
					// 套餐主项
					PosBillItem posBillItemSetmeal = comboSetMealDao.getPosBillItemByRWID(tenancyId, storeId, billNum, comboSetId);

					if (SysDictionary.ITEM_REMARK_TC01.equals(posBillItemSetmeal.getItem_remark()) || SysDictionary.ITEM_REMARK_CJ05.equals(posBillItemSetmeal.getItem_remark()))
					{
						throw SystemException.getInstance(PosErrorCode.ADD_DEFAULT_DISH_FAILURE);
					}
					if (SysDictionary.ITEM_REMARK_FS02.equals(posBillItemSetmeal.getItem_remark()))
					{
						throw SystemException.getInstance(PosErrorCode.ADD_DEFAULT_DISH_FAILURE);
					}

//					List<JSONObject> posBillItemComboList = comboSetMealDao.getAllPosBillItemByBillNum(tenancyId, storeId, billNum);
//					if (posBillItemComboList != null && posBillItemComboList.size() > 0)
//					{
//						this.clearPosBillItem(tenancyId, storeId, posBillItemComboList);
//					}

//					this.clearPosBillItem(tenancyId, storeId, billNum, posBillItemSetmeal);

					int itemSerialSetmeal = Integer.parseInt(posBillItemSetmeal.getItem_serial() == null ? "1" : posBillItemSetmeal.getItem_serial());
					int itemIdSsetmeal = Integer.parseInt(posBillItemSetmeal.getItem_id() == null ? "1" : posBillItemSetmeal.getItem_id());
//					int discountModeIdSsetmeal = Integer.parseInt(posBillItemSetmeal.getDiscount_mode_id() == null ? "0" : posBillItemSetmeal.getDiscount_mode_id());
//					double discountRateSsetmeal = Double.parseDouble(posBillItemSetmeal.getDiscount_rate() == null ? "100" : posBillItemSetmeal.getDiscount_rate());

					// 套餐单品集合
					List<JSONObject> itemList = comboSetMealDao.getPosBillItemListByRWIDS(tenancyId, storeId, billNum, addList);
//					if (itemList != null && itemList.size() > 0)
//					{
//						for(JSONObject itemJson : itemList)
//						{
//							posDishDao.insertIgnorCase(tenancyId, "pos_bill_item_combo", itemJson);
//						}
//					}

					List<PosBillItem> posBillItemList = new ArrayList<PosBillItem>();
					for (JSONObject itemJson : itemList)
					{
						String itemRemark = itemJson.optString("item_remark");
						if (SysDictionary.ITEM_REMARK_TC01.equals(itemRemark) || SysDictionary.ITEM_REMARK_CJ05.equals(itemRemark) || SysDictionary.ITEM_REMARK_FS02.equals(itemRemark))
						{
							continue;
						}

						PosBillItem posBillItem = new PosBillItem();
						posBillItem.setRwid(itemJson.optString("rwid"));
						posBillItem.setCombo_prop(String.valueOf(itemJson.optInt("item_serial")));
						posBillItem.setItem_property(SysDictionary.ITEM_PROPERTY_MEALLIST);
						posBillItem.setItem_serial(String.valueOf(itemSerialSetmeal));
						posBillItem.setSetmeal_rwid(String.valueOf(itemSerialSetmeal));
						posBillItem.setSetmeal_id(String.valueOf(itemIdSsetmeal));
						posBillItem.setAssist_item_id(String.valueOf(itemJson.optInt("item_id")));
						posBillItem.setItem_name("　" + itemJson.optString("item_name"));
						posBillItem.setOrigin_item_price(String.valueOf(itemJson.optDouble("item_price")));
						posBillItemList.add(posBillItem);
					}

					posBillItemSetmeal.setItem_property(SysDictionary.ITEM_PROPERTY_SETMEAL);
					posBillItemSetmeal.setSetmeal_rwid(String.valueOf(itemSerialSetmeal));
					posBillItemSetmeal.setSetmeal_id(String.valueOf(itemIdSsetmeal));
					posBillItemSetmeal.setAssist_item_id("0");
					posBillItemSetmeal.setCombo_prop(String.valueOf(itemSerialSetmeal));
					posBillItemSetmeal.setRwid(String.valueOf(comboSetId));
					posBillItemSetmeal.setOrigin_item_price(posBillItemSetmeal.getItem_price());
					posBillItemList.add(posBillItemSetmeal);

//					// 复制账单明细到pos_bill_item_combo，如果存在先删除
//					List<JSONObject> posBillItems = comboSetMealDao.getAllPosBillItemByBillNum(tenancyId, storeId, billNum);
//					this.deletePosBillItem(tenancyId, posBillItems);
//					List<JSONObject> items = posDishDao.getPosBillItemByBillNum(tenancyId, storeId, billNum);
//					if (items != null && items.size() > 0)
//					{
//						for (int i = 0; i < items.size(); i++)
//						{
//							JSONObject item = items.get(i);
//							item.put("combo_prop", i + 1);
//							posDishDao.insertIgnorCase(tenancyId, "pos_bill_item_combo", item);
//						}
//					}
					// 更新pos_bill_item
					comboSetMealDao.updatePosBillItem4Setmeal(tenancyId, storeId, billNum, posBillItemList);
					this.calcAmount(tenancyId, storeId, billNum);
				}
				catch (Exception e)
				{
					e.printStackTrace();
				}
			}
			posDishDao.savePosLog(tenancyId, storeId, posNum, optNum, optNum, shiftId, reportDate, Constant.TITLE, "组合套餐", "账单编号:" + billNum, "套餐主项:" + String.valueOf(comboSetId) + ";套餐明细" + addList);
		}
		else
		{
			// 组合套餐删除
			if (StringUtils.isNotEmpty(delList))
			{
				try
				{
					// 套餐主项
//					PosBillItem posBillItemSetmeal = comboSetMealDao.getPosBillItemByRWID(comboSetId);
					PosBillItem posBillItemSetmeal = comboSetMealDao.getPosBillItemByRWID(tenancyId, storeId, billNum, comboSetId);
					if (SysDictionary.ITEM_REMARK_TC01.equals(posBillItemSetmeal.getItem_remark()) || SysDictionary.ITEM_REMARK_CJ05.equals(posBillItemSetmeal.getItem_remark()))
					{
						throw SystemException.getInstance(PosErrorCode.ADD_DEFAULT_DISH_FAILURE);
					}
					if (SysDictionary.ITEM_REMARK_FS02.equals(posBillItemSetmeal.getItem_remark()))
					{
						throw SystemException.getInstance(PosErrorCode.ADD_DEFAULT_DISH_FAILURE);
					}

//					List<JSONObject> posBillItemComboList = comboSetMealDao.getAllPosBillItemByBillNum(tenancyId, storeId, billNum);
//					if (posBillItemComboList != null && posBillItemComboList.size() > 0)
//					{
//						this.clearPosBillItem(tenancyId, storeId, posBillItemComboList);
//					}

					this.clearPosBillItem(tenancyId, storeId,billNum, posBillItemSetmeal);

					this.calcAmount(tenancyId, storeId, billNum);
//					// 删除pos_bill_item_combo表
//					this.deletePosBillItem(tenancyId, posBillItemComboList);
				}
				catch (Exception e)
				{
					e.printStackTrace();
				}
			}
			posDishDao.savePosLog(tenancyId, storeId, posNum, optNum, optNum, shiftId, reportDate, Constant.TITLE, "组合套餐删除", "账单编号:" + billNum, "套餐主项:" + String.valueOf(comboSetId) + ";套餐明细" + delList);
		}
		
		this.updatePosBillForUpload(tenancyId, storeId, billNum);
	}

}
