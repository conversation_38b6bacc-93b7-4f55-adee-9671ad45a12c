package com.tzx.member.crm.bo.imp;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Service;

import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.member.common.entity.PosCustomerOperateListEntity;
import com.tzx.member.crm.bo.CustomerCreditService;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.DateUtil;

@Service(CustomerCreditService.NAME)
public class CustomerCreditServiceImp extends CustomerServiceImp implements CustomerCreditService
{
	public final String	CUSTOMER_CREDIT_CONSUME_XF	= "0";
	public final String	CUSTOMER_CREDIT_CONSUME_FXF	= "1";

	public final String	CUSTOMER_CREDIT_ZJ			= "0";
	public final String	CUSTOMER_CREDIT_CXZJ		= "1";

	@Override
	public void customerCreditConsume(String tenancyId, int storeId, JSONObject param, Data resData, String operType) throws Exception
	{
		String reportDateStr = param.optString("report_date");
		Integer shiftId = param.optInt("shift_id");
		String optNum = param.optString("opt_num");
		String posNum = param.optString("pos_num");
		String chanel = param.optString("chanel");
		String billCode = param.optString("bill_code");
		String batchNum = param.optString("batch_num");
		String customerCode = param.optString("customer_code");
		String cardCode = param.optString("card_code");
		String customerMobil = param.optString("mobil");
		Double consumeCredit = param.optDouble("credit");
		Double cashMoney = param.optDouble("cash_money");
		Double billAmount = param.optDouble("bill_amount");

		String optName = customerDao.getEmpNameById(optNum, tenancyId, storeId);

		Timestamp currentTime = DateUtil.currentTimestamp();

		PosCustomerOperateListEntity customerOperate = new PosCustomerOperateListEntity();
		customerOperate.setTenancy_id(tenancyId);
		customerOperate.setStore_id(storeId);
		customerOperate.setBusiness_date(DateUtil.parseDate(reportDateStr));
		customerOperate.setOperator_id(Integer.parseInt(optNum));
		customerOperate.setPos_num(posNum);
		customerOperate.setShift_id(shiftId);
		customerOperate.setOperate_time(currentTime);
		customerOperate.setChanel(chanel);
		customerOperate.setService_type(SysDictionary.SERVICE_TYPE_CONSUME);
		customerOperate.setOperat_type(SysDictionary.OPERAT_TYPE_JFXF);
		customerOperate.setMobil(customerMobil);
		customerOperate.setCustomer_code(customerCode);
		customerOperate.setCard_code(cardCode);
		customerOperate.setThird_bill_code(billCode);
		customerOperate.setBatch_num(batchNum);
		customerOperate.setTrade_amount(cashMoney);
		customerOperate.setTrade_credit(consumeCredit);
		customerOperate.setDeposit(0d);
		customerOperate.setSales_price(0d);
		customerOperate.setOperate_state(SysDictionary.THIRD_PAY_STATUS_PAYING);
		customerOperate.setCancel_state(SysDictionary.CUSTOMER_OPERATE_CANCEL_STATE_N);
		customerOperate.setRequest_status(SysDictionary.REQUEST_STATUS_ING);
		customerOperate.setLast_query_time(currentTime);
		customerOperate.setQuery_count(0);
		customerOperate.setAction_type(operType);

		try
		{
			JSONObject requestJson = new JSONObject();
			requestJson.put("code", customerCode);
			requestJson.put("mobil", customerMobil);
			requestJson.put("credit", consumeCredit);
			requestJson.put("cash_money", cashMoney);
			requestJson.put("chanel", chanel);
			requestJson.put("bill_code", billCode);
			requestJson.put("operator", optName);
			requestJson.put("updatetime", DateUtil.format(currentTime));
			requestJson.put("business_date", reportDateStr);
			requestJson.put("shift_id", shiftId);
			requestJson.put("batch_no", batchNum);
			requestJson.put("operator_id", optNum);
			requestJson.put("bill_amount", billAmount);
			requestJson.put("service_type", SysDictionary.SERVICE_TYPE_CONSUME);

			List<JSONObject> requestList = new ArrayList<JSONObject>();
			requestList.add(requestJson);

			Data requestData = Data.get(tenancyId, storeId, 0);
			requestData.setType(Type.CUSTOMER_CREDIT_COUNSUME);
			requestData.setOper(Oper.add);
			requestData.setData(requestList);

			this.commonPost(JSONObject.fromObject(requestData).toString(), resData);

			this.customerCreditConsumeResult(tenancyId, storeId, customerOperate, resData);
		}
		catch (Exception e)
		{
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

		customerDao.insertPosCustomerOperateList(tenancyId, storeId, customerOperate);
	}

	@Override
	public Data queryCustomerCreditConsume(String tenancyId, int storeId, JSONObject paramJson) throws Exception
	{
		String customerCode = paramJson.optString("code");
		String mobil = paramJson.optString("mobil");
		String billCode = paramJson.optString("bill_code");
		String batchNum = paramJson.optString("batch_no");

		PosCustomerOperateListEntity customerOperate = customerDao.queryPosCustomerOperateListByCredit(tenancyId, storeId, customerCode, mobil, billCode, batchNum, SysDictionary.OPERAT_TYPE_JFXF);

		return this.queryCustomerCreditConsume(tenancyId, storeId, customerOperate);
	}

	@Override
	public Data queryCustomerCreditConsume(String tenancyId, int storeId, PosCustomerOperateListEntity customerOperate) throws Exception
	{
		Data responseData = Data.get();

		JSONObject requestJson = new JSONObject();
		requestJson.put("code", customerOperate.getCustomer_code());
		requestJson.put("mobil", customerOperate.getMobil());
		requestJson.put("wechat", "");
		requestJson.put("bill_code", customerOperate.getThird_bill_code());
		requestJson.put("batch_no", customerOperate.getBatch_num());
		requestJson.put("shift_id", customerOperate.getShift_id());
		requestJson.put("type", this.CUSTOMER_CREDIT_CONSUME_XF);

		List<JSONObject> requestList = new ArrayList<JSONObject>();
		requestList.add(requestJson);

		Data requestData = Data.get(tenancyId, storeId, 0);
		requestData.setType(Type.CUSTOMER_CREDIT_COUNSUME);
		requestData.setOper(Oper.check);
		requestData.setData(requestList);

		this.commonPost(JSONObject.fromObject(requestData).toString(), responseData);

		this.customerCreditConsumeResult(tenancyId, storeId, customerOperate, responseData);

		try
		{
			customerDao.updatePosCustomerOperateList(tenancyId, storeId, customerOperate);
		}
		catch (Exception e)
		{
			e.printStackTrace();
			logger.error("会员积分消费记录pos_customer_operate_list报错:", e);
		}
		return responseData;
	}

	private void customerCreditConsumeResult(String tenancyId, int storeId, PosCustomerOperateListEntity customerOperate, Data responseData) throws Exception
	{
		Timestamp currentTime = DateUtil.currentTimestamp();
		if (Constant.CODE_SUCCESS == responseData.getCode() && responseData.isSuccess() && null != responseData && responseData.getData().size() > 0)
		{
			JSONObject resultObj = JSONObject.fromObject(responseData.getData().get(0));
			Integer customerId = resultObj.optInt("customer_id");
			String mobil = resultObj.optString("mobil");
			String customerName = resultObj.optString("name");
			String billCode = resultObj.optString("bill_code");

			customerOperate.setCustomer_id(customerId);
			customerOperate.setMobil(mobil);
			customerOperate.setCustomer_name(customerName);
			customerOperate.setBill_code(billCode);
			customerOperate.setFinish_time(currentTime);
			customerOperate.setOperate_state(SysDictionary.THIRD_PAY_STATUS_SUCCESS);
			customerOperate.setRequest_status(SysDictionary.REQUEST_STATUS_COMPLETE);

		}
		else if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
		{
			customerOperate.setOperate_state(SysDictionary.THIRD_PAY_STATUS_PAYING);
			customerOperate.setRequest_status(SysDictionary.REQUEST_STATUS_FAILURE);
			customerOperate.setRequest_code(String.valueOf(responseData.getCode()));
			customerOperate.setRequest_msg(responseData.getMsg());
		}
		else
		{
			customerOperate.setFinish_time(currentTime);
			customerOperate.setOperate_state(SysDictionary.THIRD_PAY_STATUS_FAIL);
			customerOperate.setRequest_status(SysDictionary.REQUEST_STATUS_COMPLETE);
			customerOperate.setRequest_code(String.valueOf(responseData.getCode()));
			customerOperate.setRequest_msg(responseData.getMsg());
		}
	}

	@Override
	public void customerCreditCancelConsume(String tenancyId, int storeId, JSONObject param, Data responseData, String operType) throws Exception
	{
		String customerCode = param.optString("code");
		String customerMobil = param.optString("mobil");
		String oldBillCode = param.optString("old_bill_code");
		String billCode = param.optString("bill_code");
		String batchNum = param.optString("batch_no");
		String chanel = param.optString("chanel");
		String reportDateStr = param.optString("report_date");
		Integer shiftId = param.optInt("shift_id");
		String optNum = param.optString("opt_num");
		String posNum = param.optString("pos_num");
		Double amount = param.optDouble("amount");

		String operator = customerDao.getEmpNameById(optNum, tenancyId, storeId);

		Timestamp currentTime = DateUtil.currentTimestamp();

		PosCustomerOperateListEntity customerOperate = new PosCustomerOperateListEntity();
		customerOperate.setTenancy_id(tenancyId);
		customerOperate.setStore_id(storeId);
		customerOperate.setBusiness_date(DateUtil.parseDate(reportDateStr));
		customerOperate.setOperator_id(Integer.getInteger(optNum));
		customerOperate.setPos_num(posNum);
		customerOperate.setShift_id(shiftId);
		customerOperate.setOperate_time(currentTime);
		customerOperate.setChanel(chanel);
		customerOperate.setService_type(SysDictionary.SERVICE_TYPE_CONSUME);
		customerOperate.setOperat_type(SysDictionary.OPERAT_TYPE_CXXF);
		customerOperate.setCustomer_code(customerCode);
		customerOperate.setMobil(customerMobil);
		customerOperate.setThird_bill_code(billCode);
		customerOperate.setBatch_num(batchNum);
		customerOperate.setBill_code_original(oldBillCode);
		customerOperate.setTrade_amount(amount);
		customerOperate.setTrade_credit(0d);
		customerOperate.setDeposit(0d);
		customerOperate.setSales_price(0d);
		customerOperate.setOperate_state(SysDictionary.THIRD_PAY_STATUS_PAYING);
		customerOperate.setCancel_state(SysDictionary.CUSTOMER_OPERATE_CANCEL_STATE_N);
		customerOperate.setRequest_status(SysDictionary.REQUEST_STATUS_ING);
		customerOperate.setLast_query_time(currentTime);
		customerOperate.setQuery_count(0);
		customerOperate.setAction_type(operType);

		try
		{
			JSONObject requestJson = new JSONObject();
			requestJson.put("code", customerCode);
			requestJson.put("mobil", customerMobil);
			requestJson.put("old_bill_code", oldBillCode);
			requestJson.put("bill_code", billCode);
			requestJson.put("batch_no", batchNum);
			requestJson.put("business_date", reportDateStr);
			requestJson.put("shift_id", shiftId);
			requestJson.put("chanel", chanel);
			requestJson.put("operator", operator);
			requestJson.put("operator_id", optNum);
			requestJson.put("updatetime", DateUtil.format(currentTime));

			List<JSONObject> dataList = new ArrayList<JSONObject>();
			dataList.add(requestJson);

			Data paramData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
			paramData.setType(Type.CUSTOMER_CREDIT_COUNSUME);
			paramData.setOper(Oper.update);
			paramData.setData(dataList);

			this.commonPost(JSONObject.fromObject(paramData).toString(), responseData);

			this.customerCreditCancelConsumeResult(tenancyId, storeId, customerOperate, responseData);
		}
		catch (Exception e)
		{
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

		customerDao.insertPosCustomerOperateList(tenancyId, storeId, customerOperate);
	}

	@Override
	public Data queryCustomerCreditCancelConsume(String tenancyId, int storeId, PosCustomerOperateListEntity customerOperate) throws Exception
	{
		Data responseData = Data.get();

		JSONObject requestJson = new JSONObject();
		requestJson.put("code", customerOperate.getCustomer_code());
		requestJson.put("mobil", customerOperate.getMobil());
		requestJson.put("wechat", "");
		requestJson.put("bill_code", customerOperate.getThird_bill_code());
		requestJson.put("batch_no", customerOperate.getBatch_num());
		requestJson.put("shift_id", customerOperate.getShift_id());
		requestJson.put("type", this.CUSTOMER_CREDIT_CONSUME_FXF);

		List<JSONObject> requestList = new ArrayList<JSONObject>();
		requestList.add(requestJson);

		Data requestData = Data.get(tenancyId, storeId, 0);
		requestData.setType(Type.CUSTOMER_CREDIT_COUNSUME);
		requestData.setOper(Oper.check);
		requestData.setData(requestList);

		this.commonPost(JSONObject.fromObject(requestData).toString(), responseData);

		this.customerCreditCancelConsumeResult(tenancyId, storeId, customerOperate, responseData);

		try
		{
			customerDao.updatePosCustomerOperateList(tenancyId, storeId, customerOperate);
		}
		catch (Exception e)
		{
			e.printStackTrace();
			logger.error("会员积分撤销消费记录pos_customer_operate_list报错:", e);
		}
		return responseData;
	}

	private void customerCreditCancelConsumeResult(String tenancyId, int storeId, PosCustomerOperateListEntity customerOperate, Data responseData) throws Exception
	{
		Timestamp currentTime = DateUtil.currentTimestamp();
		if (Constant.CODE_SUCCESS == responseData.getCode() && responseData.isSuccess() && null != responseData && responseData.getData().size() > 0)
		{
			JSONObject resultObj = JSONObject.fromObject(responseData.getData().get(0));
			Integer customerId = resultObj.optInt("customer_id");
			String mobil = resultObj.optString("mobil");
			String customerName = resultObj.optString("name");
			String billCode = resultObj.optString("bill_code");

			customerOperate.setCustomer_id(customerId);
			customerOperate.setMobil(mobil);
			customerOperate.setCustomer_name(customerName);
			customerOperate.setBill_code(billCode);
			customerOperate.setFinish_time(currentTime);
			customerOperate.setOperate_state(SysDictionary.THIRD_PAY_STATUS_SUCCESS);
			customerOperate.setRequest_status(SysDictionary.REQUEST_STATUS_COMPLETE);

		}
		else if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
		{
			customerOperate.setOperate_state(SysDictionary.THIRD_PAY_STATUS_PAYING);
			customerOperate.setRequest_status(SysDictionary.REQUEST_STATUS_FAILURE);
			customerOperate.setRequest_code(String.valueOf(responseData.getCode()));
			customerOperate.setRequest_msg(responseData.getMsg());
		}
		else
		{
			customerOperate.setFinish_time(currentTime);
			customerOperate.setOperate_state(SysDictionary.THIRD_PAY_STATUS_FAIL);
			customerOperate.setRequest_status(SysDictionary.REQUEST_STATUS_COMPLETE);
			customerOperate.setRequest_code(String.valueOf(responseData.getCode()));
			customerOperate.setRequest_msg(responseData.getMsg());
		}
	}

	@Override
	public void customerCreditAdd(String tenancyId, int storeId, JSONObject param, Data resData, String operType) throws Exception
	{
		// TODO Auto-generated method stub
		String mobil = param.optString("mobil");
		String cardCode = param.optString("card_code");
		Double billAmont = param.optDouble("bill_amount");
		Double consumeCreditMoney = param.optDouble("consume_creditmoney");
		Double credit = param.optDouble("credit");
		String reason = param.optString("reason");
		String billCode = param.optString("bill_code");
		String batchNum = param.optString("batch_no");
		String chanel = param.optString("chanel");
		String reportDateStr = param.optString("report_date");
		Integer shiftId = param.optInt("shift_id");
		String optNum = param.optString("opt_num");
		String posNum = param.optString("pos_num");
		String remark = param.optString("remark");
		String end_date = param.optString("end_date");
		List payments=param.getJSONArray("payments");
		String operator = customerDao.getEmpNameById(optNum, tenancyId, storeId);

		Timestamp currentTime = DateUtil.currentTimestamp();

		PosCustomerOperateListEntity customerOperate = new PosCustomerOperateListEntity();
		customerOperate.setTenancy_id(tenancyId);
		customerOperate.setStore_id(storeId);
		customerOperate.setBusiness_date(DateUtil.parseDate(reportDateStr));
		customerOperate.setOperator_id(Integer.getInteger(optNum));
		customerOperate.setPos_num(posNum);
		customerOperate.setShift_id(shiftId);
		customerOperate.setOperate_time(currentTime);
		customerOperate.setChanel(chanel);
		customerOperate.setService_type(SysDictionary.SERVICE_TYPE_CONSUME);
		customerOperate.setOperat_type(SysDictionary.OPERAT_TYPE_JFZJ);
		customerOperate.setMobil(mobil);
		customerOperate.setCard_code(cardCode);
		customerOperate.setThird_bill_code(billCode);
		customerOperate.setBatch_num(batchNum);
		customerOperate.setTrade_amount(consumeCreditMoney);
		customerOperate.setTrade_credit(0d);
		customerOperate.setDeposit(0d);
		customerOperate.setSales_price(0d);
		customerOperate.setOperate_state(SysDictionary.THIRD_PAY_STATUS_PAYING);
		customerOperate.setCancel_state(SysDictionary.CUSTOMER_OPERATE_CANCEL_STATE_N);
		customerOperate.setRequest_status(SysDictionary.REQUEST_STATUS_ING);
		customerOperate.setLast_query_time(currentTime);
		customerOperate.setQuery_count(0);
		customerOperate.setAction_type(operType);

		try
		{
			JSONObject requestJson = new JSONObject();
			requestJson.put("mobil", mobil);
			requestJson.put("card_code", cardCode);
			requestJson.put("bill_amount", billAmont);
			requestJson.put("consume_creditmoney", consumeCreditMoney);
			requestJson.put("credit", credit);
			requestJson.put("reason", reason);
			requestJson.put("bill_code", billCode);
			requestJson.put("batch_no", batchNum);
			requestJson.put("chanel", chanel);
			requestJson.put("business_date", reportDateStr);
			requestJson.put("shift_id", shiftId);
			requestJson.put("operator_id", optNum);
			requestJson.put("operator", operator);
			requestJson.put("updatetime", DateUtil.format(currentTime));
			requestJson.put("remark", remark);
			requestJson.put("end_date", end_date);
			requestJson.put("payments", payments);
			List<JSONObject> requestList = new ArrayList<JSONObject>();
			requestList.add(requestJson);

			Data requestData = Data.get(tenancyId, storeId, 0);
			requestData.setType(Type.CUSTOMER_CREDIT);
			requestData.setOper(Oper.add);
			requestData.setData(requestList);

			this.commonPost(JSONObject.fromObject(requestData).toString(), resData);

			this.customerCreditAddResult(tenancyId, storeId, customerOperate, resData);
			;
		}
		catch (Exception e)
		{
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		customerDao.insertPosCustomerOperateList(tenancyId, storeId, customerOperate);
	}

	@Override
	public Data queryCustomerCreditAdd(String tenancyId, int storeId, PosCustomerOperateListEntity customerOperate) throws Exception
	{
		Data responseData = Data.get();

		JSONObject requestJson = new JSONObject();
		requestJson.put("code", customerOperate.getCustomer_code());
		requestJson.put("mobil", customerOperate.getMobil());
		requestJson.put("bill_code", customerOperate.getThird_bill_code());
		requestJson.put("batch_no", customerOperate.getBatch_num());
		requestJson.put("shift_id", customerOperate.getShift_id());
		requestJson.put("type", this.CUSTOMER_CREDIT_ZJ);

		List<JSONObject> requestList = new ArrayList<JSONObject>();
		requestList.add(requestJson);

		Data requestData = Data.get(tenancyId, storeId, 0);
		requestData.setType(Type.CUSTOMER_CREDIT);
		requestData.setOper(Oper.check);
		requestData.setData(requestList);

		this.commonPost(JSONObject.fromObject(requestData).toString(), responseData);

		this.customerCreditAddResult(tenancyId, storeId, customerOperate, responseData);

		try
		{
			customerDao.updatePosCustomerOperateList(tenancyId, storeId, customerOperate);
		}
		catch (Exception e)
		{
			e.printStackTrace();
			logger.error("会员增加记录pos_customer_operate_list报错:", e);
		}
		return responseData;
	}

	private void customerCreditAddResult(String tenancyId, int storeId, PosCustomerOperateListEntity customerOperate, Data responseData) throws Exception
	{
		Timestamp currentTime = DateUtil.currentTimestamp();
		if (Constant.CODE_SUCCESS == responseData.getCode() && responseData.isSuccess() && null != responseData && responseData.getData().size() > 0)
		{
			JSONObject resultObj = JSONObject.fromObject(responseData.getData().get(0));
			Integer customerId = resultObj.optInt("customer_id");
			String mobil = resultObj.optString("mobil");
			String customerName = resultObj.optString("name");
			String billCode = resultObj.optString("bill_code");

			customerOperate.setCustomer_id(customerId);
			customerOperate.setMobil(mobil);
			customerOperate.setCustomer_name(customerName);
			customerOperate.setBill_code(billCode);
			customerOperate.setFinish_time(currentTime);
			customerOperate.setOperate_state(SysDictionary.THIRD_PAY_STATUS_SUCCESS);
			customerOperate.setRequest_status(SysDictionary.REQUEST_STATUS_COMPLETE);

		}
		else if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
		{
			customerOperate.setOperate_state(SysDictionary.THIRD_PAY_STATUS_PAYING);
			customerOperate.setRequest_status(SysDictionary.REQUEST_STATUS_FAILURE);
			customerOperate.setRequest_code(String.valueOf(responseData.getCode()));
			customerOperate.setRequest_msg(responseData.getMsg());
		}
		else
		{
			customerOperate.setFinish_time(currentTime);
			customerOperate.setOperate_state(SysDictionary.THIRD_PAY_STATUS_FAIL);
			customerOperate.setRequest_status(SysDictionary.REQUEST_STATUS_COMPLETE);
			customerOperate.setRequest_code(String.valueOf(responseData.getCode()));
			customerOperate.setRequest_msg(responseData.getMsg());
		}
	}

	@Override
	public void customerCreditCancelAdd(String tenancyId, int storeId, JSONObject param, Data resData, String operType) throws Exception
	{
		String customerCode = param.optString("code");
		String customerMobil = param.optString("mobil");
		String oldBillCode = param.optString("old_bill_code");
		String billCode = param.optString("bill_code");
		String batchNum = param.optString("batch_no");
		String chanel = param.optString("chanel");
		String reportDateStr = param.optString("report_date");
		Integer shiftId = param.optInt("shift_id");
		String optNum = param.optString("opt_num");
		String posNum = param.optString("pos_num");
		// Double amount= param.optDouble("amount");

		String operator = customerDao.getEmpNameById(optNum, tenancyId, storeId);

		Timestamp currentTime = DateUtil.currentTimestamp();

		PosCustomerOperateListEntity customerOperate = new PosCustomerOperateListEntity();
		customerOperate.setTenancy_id(tenancyId);
		customerOperate.setStore_id(storeId);
		customerOperate.setBusiness_date(DateUtil.parseDate(reportDateStr));
		customerOperate.setOperator_id(Integer.getInteger(optNum));
		customerOperate.setPos_num(posNum);
		customerOperate.setShift_id(shiftId);
		customerOperate.setOperate_time(currentTime);
		customerOperate.setChanel(chanel);
		customerOperate.setService_type(SysDictionary.SERVICE_TYPE_CONSUME);
		customerOperate.setOperat_type(SysDictionary.OPERAT_TYPE_CXJFZJ);
		customerOperate.setCustomer_code(customerCode);
		customerOperate.setMobil(customerMobil);
		customerOperate.setThird_bill_code(billCode);
		customerOperate.setBatch_num(batchNum);
		customerOperate.setBill_code_original(oldBillCode);
		customerOperate.setTrade_amount(0d);
		customerOperate.setTrade_credit(0d);
		customerOperate.setDeposit(0d);
		customerOperate.setSales_price(0d);
		customerOperate.setOperate_state(SysDictionary.THIRD_PAY_STATUS_PAYING);
		customerOperate.setCancel_state(SysDictionary.CUSTOMER_OPERATE_CANCEL_STATE_N);
		customerOperate.setRequest_status(SysDictionary.REQUEST_STATUS_ING);
		customerOperate.setLast_query_time(currentTime);
		customerOperate.setQuery_count(0);
		customerOperate.setAction_type(operType);

		try
		{
			JSONObject requestJson = new JSONObject();
			requestJson.put("code", customerCode);
			requestJson.put("mobil", customerMobil);
			requestJson.put("old_bill_code", oldBillCode);
			requestJson.put("bill_code", billCode);
			requestJson.put("batch_no", batchNum);
			requestJson.put("business_date", reportDateStr);
			requestJson.put("shift_id", shiftId);
			requestJson.put("chanel", chanel);
			requestJson.put("operator", operator);
			requestJson.put("operator_id", optNum);
			requestJson.put("updatetime", DateUtil.format(currentTime));

			List<JSONObject> requestList = new ArrayList<JSONObject>();
			requestList.add(requestJson);

			Data requestData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
			requestData.setType(Type.CUSTOMER_CREDIT);
			requestData.setOper(Oper.update);
			requestData.setData(requestList);

			this.commonPost(JSONObject.fromObject(requestData).toString(), resData);

			this.customerCreditCancelConsumeResult(tenancyId, storeId, customerOperate, resData);
		}
		catch (Exception e)
		{
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

		customerDao.insertPosCustomerOperateList(tenancyId, storeId, customerOperate);
	}

	@Override
	public Data queryCustomerCreditCancelAdd(String tenancyId, int storeId, PosCustomerOperateListEntity customerOperate) throws Exception
	{
		Data responseData = Data.get();

		JSONObject requestJson = new JSONObject();
		requestJson.put("code", customerOperate.getCustomer_code());
		requestJson.put("mobil", customerOperate.getMobil());
		requestJson.put("bill_code", customerOperate.getThird_bill_code());
		requestJson.put("batch_no", customerOperate.getBatch_num());
		requestJson.put("shift_id", customerOperate.getShift_id());
		requestJson.put("type", this.CUSTOMER_CREDIT_CXZJ);

		List<JSONObject> requestList = new ArrayList<JSONObject>();
		requestList.add(requestJson);

		Data requestData = Data.get(tenancyId, storeId, 0);
		requestData.setType(Type.CUSTOMER_CREDIT);
		requestData.setOper(Oper.check);
		requestData.setData(requestList);

		this.commonPost(JSONObject.fromObject(requestData).toString(), responseData);

		this.customerCreditCancelAddResult(tenancyId, storeId, customerOperate, responseData);

		try
		{
			customerDao.updatePosCustomerOperateList(tenancyId, storeId, customerOperate);
		}
		catch (Exception e)
		{
			e.printStackTrace();
			logger.error("会员增加撤销记录pos_customer_operate_list报错:", e);
		}
		return responseData;
	}

	private void customerCreditCancelAddResult(String tenancyId, int storeId, PosCustomerOperateListEntity customerOperate, Data responseData) throws Exception
	{
		Timestamp currentTime = DateUtil.currentTimestamp();
		if (Constant.CODE_SUCCESS == responseData.getCode() && responseData.isSuccess() && null != responseData && responseData.getData().size() > 0)
		{
			JSONObject resultObj = JSONObject.fromObject(responseData.getData().get(0));
			Integer customerId = resultObj.optInt("customer_id");
			String mobil = resultObj.optString("mobil");
			String customerName = resultObj.optString("name");
			String billCode = resultObj.optString("bill_code");

			customerOperate.setCustomer_id(customerId);
			customerOperate.setMobil(mobil);
			customerOperate.setCustomer_name(customerName);
			customerOperate.setBill_code(billCode);
			customerOperate.setFinish_time(currentTime);
			customerOperate.setOperate_state(SysDictionary.THIRD_PAY_STATUS_SUCCESS);
			customerOperate.setRequest_status(SysDictionary.REQUEST_STATUS_COMPLETE);

		}
		else if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
		{
			customerOperate.setOperate_state(SysDictionary.THIRD_PAY_STATUS_PAYING);
			customerOperate.setRequest_status(SysDictionary.REQUEST_STATUS_FAILURE);
			customerOperate.setRequest_code(String.valueOf(responseData.getCode()));
			customerOperate.setRequest_msg(responseData.getMsg());
		}
		else
		{
			customerOperate.setFinish_time(currentTime);
			customerOperate.setOperate_state(SysDictionary.THIRD_PAY_STATUS_FAIL);
			customerOperate.setRequest_status(SysDictionary.REQUEST_STATUS_COMPLETE);
			customerOperate.setRequest_code(String.valueOf(responseData.getCode()));
			customerOperate.setRequest_msg(responseData.getMsg());
		}
	}
}
