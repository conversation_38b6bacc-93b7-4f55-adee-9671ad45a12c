package com.tzx.pos.bo.imp;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Service;

import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.DoubleHelper;
import com.tzx.framework.common.util.Scm;
import com.tzx.framework.common.util.Tools;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.base.util.ReqDataUtil;
import com.tzx.pos.bo.PosDishGiveService;
import com.tzx.pos.bo.dto.BasicDataUserDiscountAuthority;
import com.tzx.pos.po.springjdbc.dao.PosBillMemberDao;
import com.tzx.pos.po.springjdbc.dao.PosDiscountDao;
import com.tzx.pos.po.springjdbc.dao.PosDishDao;

/** 菜品奉送
 * <AUTHOR> 2017-09-22
 *
 */
@Service(PosDishGiveService.NAME)
public class PosDishGiveServiceImp extends PosBaseServiceImp implements PosDishGiveService
{
	private static final Logger	logger	= Logger.getLogger(PosDishGiveService.class);
	
	@Resource(name = PosDishDao.NAME)
	private PosDishDao			posDishDao;
	
	@Resource(name = PosDiscountDao.NAME)
	private PosDiscountDao		posDiscountDao;

	@Resource(name = PosBillMemberDao.NAME)
	private PosBillMemberDao	memberDao;

	private int					scale	= 2;
	    
	
	@Override
	public void giveItem(Data param, Data result) throws Exception
	{
		String tenantId = param.getTenancy_id();
		Integer organId = param.getStore_id();
		String billno = "";

//		int scale = 4;

		List<Map<String, Object>> listMaps = ReqDataUtil.getDataList(param);
		if (null != listMaps && listMaps.size() > 0)
		{
			Map<String, Object> map = listMaps.get(0);
			Date reportDate = ParamUtil.getDateValue(map, "report_date", false, null);

			Integer shiftId = ParamUtil.getIntegerValue(map, "shift_id", false, null);

			String posNum = ParamUtil.getStringValue(map, "pos_num", false, null);

			String optNum = ParamUtil.getStringValue(map, "opt_num", false, null);

			billno = ParamUtil.getStringValue(map, "bill_num", false, null);

			Integer rwid = ParamUtil.getIntegerValue(map, "rwid", true, PosErrorCode.NOT_NULL_RWID);

			Double count = 0d;
			if (Tools.isNullOrEmpty(map.get("count")) == false)
			{
				count = Double.parseDouble(map.get("count").toString());
			}

			Integer reason_id = ParamUtil.getIntegerValue(map, "reason_id", false, null);

			String manager_id = ParamUtil.getStringValue(map, "manager_id", false, null);

//			Timestamp currentTime = DateUtil.currentTimestamp();

			JSONObject billJson = posDishDao.getPosBillByBillnum(tenantId, organId, billno);

			Double subtotal = 0d;
			Double discountkAmount = 0d;
			Double discountrAmount = 0d;
			Double giviAmount = 0d;
			Double malingAmount = 0d;
			String billProperty=null;
            String paymentState = null;
			if (null != billJson)
			{
				subtotal = billJson.optDouble("subtotal");
				discountkAmount = billJson.optDouble("discountk_amount");
				discountrAmount = billJson.optDouble("discountr_amount");
				giviAmount = billJson.optDouble("givi_amount");
				malingAmount = billJson.optDouble("maling_amount");
				billProperty = billJson.optString("bill_property");
                paymentState = billJson.optString("payment_state");
			}
			
			if(SysDictionary.BILL_PROPERTY_CLOSED.equals(billProperty))
			{
				throw SystemException.getInstance(PosErrorCode.BILL_CLOSED);
			}
            if(SysDictionary.PAYMENT_STATE_PAY.equals(paymentState))
            {
                throw SystemException.getInstance(PosErrorCode.BILL_PAYMENT_STATE_PAYING_ERROR);
            }

			/**
			 * 判断奉送菜品是否存在
			 */
			List<JSONObject> itemList = posDishDao.getPosBillItemByRwid(tenantId, organId, billno, rwid);

			Double itemAmount = 0d;
			Double itemDiscountAmount = 0d;
			Integer item_id = 0;
			Integer item_serial = 0;
			double item_count = 0d; // 套餐主项的数量
			String item_remark = null;
			String item_property = null;
			double return_count = 0d;
			double itemCount = 0d;
			String itemName =null;
			if (null != itemList && itemList.size() > 0)
			{
				JSONObject itemJson = itemList.get(0);
				itemAmount = itemJson.optDouble("item_amount");
				itemDiscountAmount = itemJson.optDouble("discount_amount");
				item_id = itemJson.optInt("item_id");
				item_serial = itemJson.optInt("item_serial");
				item_count = itemJson.optDouble("item_count");
				item_remark = itemJson.optString("item_remark");
				item_property = itemJson.optString("item_property");
				return_count = itemJson.optDouble("return_count");
				itemCount = Scm.psub(item_count, return_count);
				itemName = itemJson.optString("item_name");
			}

//			StringBuilder cpsql = new StringBuilder("select item_id,item_serial,setmeal_id,setmeal_rwid,item_count,item_remark,item_property,return_count from pos_bill_item where rwid = ? and bill_num = ? and store_id = ?");
//			SqlRowSet rs = posDishDao.query4SqlRowSet(cpsql.toString(), new Object[]
//			{ rwid, billno, organId });
//
//			Integer item_id = 0;
//			Integer item_serial = 0;
//			double item_count = 0d; // 套餐主项的数量
//			String item_remark = null;
//			String item_property = null;
//			double return_count = 0d;
//			double itemCount = 0d;
//			if (rs.next())
//			{
//				item_id = rs.getInt("item_id");
//				item_serial = rs.getInt("item_serial");
//				item_count = rs.getDouble("item_count");
//				item_remark = rs.getString("item_remark");
//				item_property = rs.getString("item_property");
//				return_count = rs.getDouble("return_count");
//				itemCount = Scm.psub(item_count, return_count);
//			}

			if (item_id == null)
			{
				throw new SystemException(PosErrorCode.NOT_FIND_GIVE_ITEM);
			}

			if (count > itemCount)
			{
				throw new SystemException(PosErrorCode.GIVE_COUNT_MORE_THAN_EXISTS);
			}

			if (StringUtils.isNotEmpty(item_remark))
			{
				switch (item_remark)
				{
					case SysDictionary.ITEM_REMARK_TC01:
						throw new SystemException(PosErrorCode.HAVING_RETREAT_FOOD);
					case SysDictionary.ITEM_REMARK_FS02:
						throw new SystemException(PosErrorCode.HAVING_GIVE_FOOD);
					case SysDictionary.ITEM_REMARK_QX04:
						throw new SystemException(PosErrorCode.HAVING_CANC_FOOD);
				}
			}

			if (StringUtils.isNotEmpty(item_property))
			{
				if (SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item_property))
				{
					throw new SystemException(PosErrorCode.NOT_PERMIT_GIVE_ITEM);
				}
			}

			Integer newSerial = 1;
			try
			{
				if (count < itemCount)
				{
					/**
					 * 查询菜品最新序列
					 */
					StringBuilder iSerial = new StringBuilder("select coalesce(max(item_serial+1),1) as serial from pos_bill_item where bill_num = ? and store_id = ?");
					SqlRowSet rs = posDishDao.query4SqlRowSet(iSerial.toString(), new Object[]
					{ billno, organId });

					if (rs.next())
					{
						newSerial = rs.getInt("serial");
					}

					StringBuilder insertItemSql = new StringBuilder();
					insertItemSql
							.append("insert into pos_bill_item (tenancy_id,store_id,bill_num,details_id,item_id,item_num,item_name,item_english,report_date,item_unit_id,item_unit_name,table_code,pushmoney_way,proportion,assist_num,waiter_num,item_price,discount_rate,discount_state,discount_mode_id,item_class_id,item_property,print_tag,waitcall_tag,setmeal_id,is_setmeal_changitem,item_time,item_shift_id,item_mac_id,order_remark,seat_num,sale_mode,assist_item_id,item_serial,setmeal_rwid,item_remark,item_count,returngive_reason_id,manager_num,method_money,batch_num,origin_item_price) ");
					insertItemSql
							.append("select pb.tenancy_id,pb.store_id,pb.bill_num,pb.details_id,pb.item_id,pb.item_num,pb.item_name,pb.item_english,pb.report_date,pb.item_unit_id,pb.item_unit_name,pb.table_code,pb.pushmoney_way,pb.proportion,pb.assist_num,pb.waiter_num,pb.item_price,pb.discount_rate,pb.discount_state,pb.discount_mode_id,pb.item_class_id,pb.item_property,pb.print_tag,pb.waitcall_tag,pb.setmeal_id,pb.is_setmeal_changitem,pb.item_time,pb.item_shift_id,pb.item_mac_id,pb.order_remark,pb.seat_num,pb.sale_mode,pb.assist_item_id,?,?,?,?,?,?,method_money,batch_num,origin_item_price from pos_bill_item pb where pb.store_id = ? and pb.bill_num = ? and rwid=?");

					/**
					 * 更新旧的账单明细
					 */
					StringBuilder usql = new StringBuilder("update pos_bill_item set item_count = ? where store_id = ? and bill_num = ? and rwid=?");

					StringBuilder itemZfInsert = new StringBuilder("insert into pos_zfkw_item (tenancy_id,store_id,bill_num,report_date,rwid,item_id,pos_num,type,last_updatetime,zfkw_id,zfkw_name,amount,print_id,upload_tag,remark) ");
					itemZfInsert.append(" select tenancy_id,store_id,bill_num,report_date,?,item_id,pos_num,type,last_updatetime,zfkw_id,zfkw_name,amount,print_id,upload_tag,remark from pos_zfkw_item ");
					itemZfInsert.append(" where rwid = ? and bill_num = ? and store_id = ? and tenancy_id = ?");

					String qremark = new String("select currval('pos_bill_item_rwid_seq'::regclass) as rwid from pos_bill_item");

					if (SysDictionary.ITEM_PROPERTY_SETMEAL.equals(item_property))
					{
						StringBuilder qureyItemRwidSql = new StringBuilder("select rwid,assist_num as assistnum from pos_bill_item pb where pb.store_id = ? and pb.bill_num = ? and setmeal_id=? and setmeal_rwid=?");
						rs = posDishDao.query4SqlRowSet(qureyItemRwidSql.toString(), new Object[]
						{ organId, billno, item_id, item_serial });

						while (rs.next())
						{
							BigDecimal assistnum = new BigDecimal(rs.getString("assistnum"));// 辅助数量
							BigDecimal countBig = new BigDecimal(Double.toString(count));// 奉送数量
							double updateCount = countBig.multiply(assistnum).doubleValue();// 奉送记录的数量
							double updateItemCount = new BigDecimal(Double.toString(itemCount)).multiply(assistnum).doubleValue(); // 剩余的数量
							posDishDao.update(insertItemSql.toString(), new Object[]
							{ newSerial, newSerial, SysDictionary.ITEM_REMARK_FS02, updateCount, reason_id, manager_id, organId, billno, rs.getInt("rwid") });

							posDishDao.update(usql.toString(), new Object[]
							{ DoubleHelper.sub(updateItemCount, updateCount, Constant.DEFAULT_SCALE), organId, billno, rs.getInt("rwid") });

							SqlRowSet rst = posDishDao.query4SqlRowSet(qremark);
							if (rst.next())
							{
								Integer mrwid = rst.getInt("rwid");
								// 把做法口味也和奉送的菜品关联起来
								posDishDao.update(itemZfInsert.toString(), new Object[]
								{ mrwid, rs.getInt("rwid"), billno, organId, tenantId });
							}
						}
					}
					else
					{
						posDishDao.update(insertItemSql.toString(), new Object[]
						{ newSerial, 0, SysDictionary.ITEM_REMARK_FS02, count, reason_id, manager_id, organId, billno, rwid });

						posDishDao.update(usql.toString(), new Object[]
						{ DoubleHelper.sub(itemCount, count, Constant.DEFAULT_SCALE), organId, billno, rwid });

						SqlRowSet rst = posDishDao.query4SqlRowSet(qremark);
						if (rst.next())
						{
							Integer mrwid = rst.getInt("rwid");
							// 把做法口味也和奉送的菜品关联起来
							posDishDao.update(itemZfInsert.toString(), new Object[]
							{ mrwid, rwid, billno, organId, tenantId });
						}
					}
				}
				else if (count == itemCount)
				{
					StringBuilder usql = new StringBuilder("update pos_bill_item set item_remark=?,returngive_reason_id=?,manager_num=? where store_id = ? and bill_num = ?");

					if (SysDictionary.ITEM_PROPERTY_SETMEAL.equals(item_property))
					{
						usql.append(" and setmeal_id=? and setmeal_rwid=?");
						posDishDao.update(usql.toString(), new Object[]
						{ SysDictionary.ITEM_REMARK_FS02, reason_id, manager_id, organId, billno, item_id, item_serial });
					}
					else
					{
						usql.append(" and rwid=?");
						posDishDao.update(usql.toString(), new Object[]
						{ SysDictionary.ITEM_REMARK_FS02, reason_id, manager_id, organId, billno, rwid });
					}
				}

				Double paymentAmout = getPaymentAmout(subtotal, discountkAmount, discountrAmount, giviAmount, malingAmount, itemAmount, itemDiscountAmount,item_count,count);
				if (0d < discountrAmount && 0d > paymentAmout)
				{
					StringBuilder updateSql = new StringBuilder("update pos_bill set discountr_amount = 0 where tenancy_id=? and store_id=? and bill_num=?");
					posDishDao.update(updateSql.toString(), new Object[]
					{ tenantId, organId, billno });
				}
				// 计算账单
				this.calcAmount(tenantId, organId, billno);
			}
			catch (Exception e)
			{
				e.printStackTrace();
				logger.info("Exception:" + ExceptionMessage.getExceptionMessage(e));
				throw SystemException.getInstance(PosErrorCode.OPER_ERROR);
			}

//			StringBuilder queryBillItemSql = new StringBuilder("select * from pos_bill_item pb where pb.store_id = ? and pb.bill_num = ?");
//
//			if ("SETMEAL".equals(item_property))
//			{
//				queryBillItemSql.append(" and pb.setmeal_id=? and pb.setmeal_rwid=?");
//			}
//			else
//			{
//				queryBillItemSql.append(" and pb.item_id=? and pb.item_serial=?");
//			}
//
//			if (count < itemCount)
//			{
//				rs = posDishDao.query4SqlRowSet(queryBillItemSql.toString(), new Object[]
//				{ organId, billno, item_id, newSerial });
//			}
//			else if (count == itemCount)
//			{
//				rs = posDishDao.query4SqlRowSet(queryBillItemSql.toString(), new Object[]
//				{ organId, billno, item_id, item_serial });
//			}
//
//			try
//			{
//				/**
//				 * 插入奉送流水库
//				 */
//				List<Object[]> returngiveList = new ArrayList<Object[]>();
//				while (rs.next())
//				{
//					returngiveList.add(new Object[]
//					{ tenantId, organId, billno, reportDate, SysDictionary.ITEM_REMARK_FS02, rs.getInt("item_id"), count, rs.getDouble("item_amount"), reason_id, manager_id, shiftId, null, rs.getTimestamp("item_time"), posNum, currentTime, rs.getInt("rwid"), rs.getString("item_property") });
//				}
//				StringBuilder prisql = new StringBuilder("insert into pos_returngive_item (tenancy_id,store_id,bill_num,report_date,type,item_id,count,amount,reason_id,manager_num,shift_id,return_type,item_time,pos_num,last_updatetime,rwid,item_property) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
//				if (returngiveList.size() > 0)
//				{
//					posDishDao.batchUpdate(prisql.toString(), returngiveList);
//				}
//			}
//			catch (Exception e)
//			{
//				e.printStackTrace();
//				throw SystemException.getInstance(PosErrorCode.OPER_ERROR);
//			}

			//修改账单是否并台后操作
			posDishDao.checkCombineTable(tenantId, organId,billno);

			// 写日志
			posDishDao.savePosLog(tenantId, organId, posNum, optNum, optNum, shiftId, reportDate, Constant.TITLE, "奉送", "账单编号:"+billno, "奉送菜品" + itemName + "," + item_count + "份,共" + itemAmount + "钱,取消奉送菜品，id为：" + rwid);
		}
		// 数据上传
//		upload(tenantId,organId+"","","","",billno);
		
		this.updatePosBillForUpload(tenantId, organId, billno);

		
		result.setCode(Constant.CODE_SUCCESS);
		result.setMsg(Constant.GIVE_DISH_SUCCESS);
	}
	
	@Override
	public void cancGiveItem(Data param, Data result) throws Exception
	{
		String tenantId = param.getTenancy_id();
		Integer organId = param.getStore_id();

		Map<String, Object> map = ReqDataUtil.getDataMap(param);
		String posNum = ParamUtil.getStringValue(map, "pos_num", false, null);// 机号

		String optNum = ParamUtil.getStringValue(map, "opt_num", false, null);

		Date reportDate = ParamUtil.getDateValue(map, "report_date", false, null);

		Integer shiftId = ParamUtil.getIntegerValue(map, "shift_id", false, null);

		String billNum = ParamUtil.getStringValue(map, "bill_num", true, PosErrorCode.NOT_NULL_BILL_NUM);

		Integer rwid = ParamUtil.getIntegerValue(map, "rwid", true, PosErrorCode.NOT_NULL_RWID);

		// String managerId = ParamUtil.getStringValue(map, "manager_id", false,
		// null);
		
		JSONObject billJson = posDishDao.getPosBillByBillnum(tenantId, organId, billNum);

		String billProperty=null;
        String paymentState = null;
		if (null != billJson)
		{
			billProperty = billJson.optString("bill_property");
            paymentState = billJson.optString("payment_state");
		}
		
		if(SysDictionary.BILL_PROPERTY_CLOSED.equals(billProperty))
		{
			throw SystemException.getInstance(PosErrorCode.BILL_CLOSED);
		}
        if(SysDictionary.PAYMENT_STATE_PAY.equals(paymentState))
        {
            throw SystemException.getInstance(PosErrorCode.BILL_PAYMENT_STATE_PAYING_ERROR);
        }

		String rsql = new String("select count(rwid) from pos_bill_item where rwid=? and bill_num=? and store_id=?");
		int count = posDishDao.queryForInt(rsql, new Object[]
		{ rwid, billNum, organId });
		if (count == 0)
		{
			throw new SystemException(PosErrorCode.NOT_EXISTS_GIVE_ITEM);
		}

		String item_property = null;
		String item_remark = null;
		double item_amount = 0;
		double item_count = 0;
		Integer item_serial = 0;
		Integer item_id = null;
		String item_name = "";

		String rssql = new String("select item_property,item_remark,item_amount,item_count,item_serial,item_name,item_id from pos_bill_item where rwid=? and bill_num=? and store_id=?");
		SqlRowSet rs = posDishDao.query4SqlRowSet(rssql, new Object[]
		{ rwid, billNum, organId });

		while (rs.next())
		{
			item_property = rs.getString("item_property");
			item_remark = rs.getString("item_remark");
			item_amount = rs.getDouble("item_amount");
			item_count = rs.getDouble("item_count");
			item_serial = rs.getInt("item_serial");
			item_id = rs.getInt("item_id");
			item_name = rs.getString("item_name");
		}

		if (SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item_property))
		{
			throw new SystemException(PosErrorCode.NOT_PERMIT_GIVE_MEALIST);
		}

		if (SysDictionary.ITEM_REMARK_FS02.equalsIgnoreCase(item_remark) == false)
		{
			throw new SystemException(PosErrorCode.NOT_GIVE_ITEM);
		}
		// double item_amount = Scm.qmui(item_price, item_count);
		// double real_amount = Scm.pdiv(Scm.qmui(Scm.qmui(item_price,
		// item_count), discount_rate), 100d);
		// 删除奉送流水库中数据
//		String dsql = new String("delete from pos_returngive_item where rwid=? and bill_num=? and store_id = ?");

		try
		{
			// 更改套餐明细
			if (SysDictionary.ITEM_PROPERTY_SETMEAL.equalsIgnoreCase(item_property))
			{
				// 查询套餐明细
//				String mealist = new String("select rwid from pos_bill_item pbi where store_id = ? and bill_num = ? and setmeal_id = ? and setmeal_rwid = ? and item_remark = 'FS02'");
//				SqlRowSet rst = posDishDao.query4SqlRowSet(mealist, new Object[]
//				{ organId, billNum, item_id, item_serial });
//				while (rst.next())
//				{
//					Integer m1rwid = rst.getInt("rwid");
//					// 删除奉送流水库中数据
//					posDishDao.update(dsql, new Object[]
//					{ m1rwid, billNum, organId });
//				}

				// 更改套餐明细
				String mealSql = new String("update pos_bill_item set item_remark=null,manager_num=null where store_id = ? and bill_num = ? and setmeal_id = ? and setmeal_rwid = ? and item_remark = 'FS02'");
				posDishDao.update(mealSql, new Object[]{ organId, billNum, item_id, item_serial });
			}
			else
			{
				String singleSql = new String("update pos_bill_item set item_remark=null,manager_num=null where bill_num=? and rwid=? and store_id=?");
				posDishDao.update(singleSql, new Object[]
				{ billNum, rwid, organId });
				// 删除奉送流水库中数据
//				posDishDao.update(dsql, new Object[]
//				{ rwid, billNum, organId });
			}

			this.calcAmount(tenantId, organId, billNum);

			posDishDao.checkCombineTable(tenantId, organId, billNum);

			posDishDao.savePosLog(tenantId, organId, posNum, optNum, optNum, shiftId, reportDate, Constant.TITLE, "取消奉送", "账单编号" + billNum, "将" + item_name + "菜品取消奉送了" + item_count + "份,共" + item_amount + "钱,取消奉送菜品，id为：" + rwid);
			// 数据上传
			upload(tenantId, organId + "", "", "", "", billNum);
			
			this.updatePosBillForUpload(tenantId, organId, billNum);
			
			result.setCode(Constant.CODE_SUCCESS);
			result.setMsg(Constant.CANC_GIVE_ITEM_SUCCESS);
		}
		catch (Exception se)
		{
			result.setCode(Constant.CODE_INNER_EXCEPTION);
			result.setMsg(Constant.CANC_GIVE_ITEM_FAILURE);
			se.printStackTrace();
		}
	}

	@SuppressWarnings("unused")
	@Override
	public void checkGiveItem(Data param, Data result) throws Exception
	{
		// TODO Auto-generated method stub
		String tenantId = param.getTenancy_id();
		Integer storeId = param.getStore_id();

		Map<String, Object> map = ReqDataUtil.getDataList(param).get(0);

		Date reportDate = ParamUtil.getDateValue(map, "report_date", false, null);

		Integer shiftId = ParamUtil.getIntegerValue(map, "shift_id", false, null);

		String posNum = ParamUtil.getStringValue(map, "pos_num", false, null);

		String optNum = ParamUtil.getStringValue(map, "opt_num", false, null);

		String billNum = ParamUtil.getStringValue(map, "bill_num", true, PosErrorCode.NOT_NULL_BILL_NUM);

		Integer rwid = ParamUtil.getIntegerValue(map, "rwid", true, PosErrorCode.NOT_NULL_RWID);

		Double count = ParamUtil.getDoubleValue(map, "count", true, PosErrorCode.NOT_NULL_ITEM_COUNT);

		if (null == count || count.isNaN() || count <= 0)
		{
			throw SystemException.getInstance(PosErrorCode.DISH_COUNT_NULL_ERROR);
		}

		JSONObject billJson = posDishDao.getPosBillByBillnum(tenantId, storeId, billNum);

		Double subtotal = 0d;
		Double discountkAmount = 0d;
		Double discountrAmount = 0d;
		Double giviAmount = 0d;
		Double malingAmount = 0d;
		if (null != billJson)
		{
			subtotal = billJson.optDouble("subtotal");
			discountkAmount = billJson.optDouble("discountk_amount");
			discountrAmount = billJson.optDouble("discountr_amount");
			giviAmount = billJson.optDouble("givi_amount");
			malingAmount = billJson.optDouble("maling_amount");
		}

		List<JSONObject> itemList = posDishDao.getPosBillItemByRwid(tenantId, storeId, billNum, rwid);

		Double itemAmount = 0d;
		Double itemDiscountAmount = 0d;

		Double item_count = 0d; // 套餐主项的数量
		Double return_count = 0d;
		String item_remark = null;
		String item_property = null;

		Double itemCount = 0d;

		if (null != itemList && itemList.size() > 0)
		{
			JSONObject itemJson = itemList.get(0);

			itemAmount = itemJson.optDouble("item_amount");
			itemDiscountAmount = itemJson.optDouble("discount_amount");

			item_count = itemJson.optDouble("item_count");
			return_count = itemJson.optDouble("return_count");
			itemCount = Scm.psub(item_count, return_count);

			item_remark = itemJson.optString("item_remark");
			item_property = itemJson.optString("item_property");

			if (count > itemCount)
			{
				throw new SystemException(PosErrorCode.GIVE_COUNT_MORE_THAN_EXISTS);
			}

			if (StringUtils.isNotEmpty(item_remark))
			{
				switch (item_remark)
				{
					case SysDictionary.ITEM_REMARK_TC01:
						throw new SystemException(PosErrorCode.HAVING_RETREAT_FOOD);
					case SysDictionary.ITEM_REMARK_FS02:
						throw new SystemException(PosErrorCode.HAVING_GIVE_FOOD);
					case SysDictionary.ITEM_REMARK_QX04:
						throw new SystemException(PosErrorCode.HAVING_CANC_FOOD);
				}
			}

			if (StringUtils.isNotEmpty(item_property))
			{
				if (SysDictionary.ITEM_PROPERTY_MEALLIST.equalsIgnoreCase(item_property))
				{
					throw new SystemException(PosErrorCode.NOT_PERMIT_GIVE_ITEM);
				}
			}
		}
		else
		{
			throw SystemException.getInstance(PosErrorCode.NOT_FIND_GIVE_ITEM);
		}

		Double paymentAmout = getPaymentAmout(subtotal, discountkAmount, discountrAmount, giviAmount, malingAmount, itemAmount, itemDiscountAmount,item_count,count);

		String isCancleDiscountr = "0";
		if (0d< discountrAmount && 0d > paymentAmout)
		{
			isCancleDiscountr = "1";
		}

		JSONObject resultJson = new JSONObject();
		resultJson.put("is_cancle_discountr", isCancleDiscountr);

		List<JSONObject> resultList = new ArrayList<JSONObject>();
		resultList.add(resultJson);

		result.setData(resultList);
		result.setCode(Constant.CODE_SUCCESS);
	}
	
	
	private Double getPaymentAmout(Double subtotal,Double discountkAmount,Double discountrAmount,Double giviAmount,Double malingAmount,Double itemAmount,Double itemDiscountAmount,Double itemCount,Double count)
	{
		int scale = 4;
		// 实付金额=账单金额+抹零金额-(折扣金额+折让金额)-奉送
		// Double paymentAmout = subtotal + malingAmount - ((discountkAmount -itemDiscountAmount) + discountrAmount) - (giviAmount + itemAmount);
		
		Double giveItemAmount = DoubleHelper.mul(DoubleHelper.div(itemAmount, itemCount, scale), count, scale);
		Double giveItemDiscountAmount = DoubleHelper.mul(DoubleHelper.div(itemDiscountAmount, itemCount, scale), count, scale);
		Double discountAmount = DoubleHelper.add(DoubleHelper.sub(discountkAmount, giveItemDiscountAmount, scale), discountrAmount, scale);
		Double giveAmount = DoubleHelper.add(giviAmount, giveItemAmount, scale);
		Double paymentAmout = DoubleHelper.sub(DoubleHelper.sub(DoubleHelper.add(subtotal, malingAmount, scale), discountAmount, scale), giveAmount, scale);
		return paymentAmout;
	}
	
	/**
     * 奉送权限验证
     * @param param
     * @param result
     * @throws Exception
     */
    @SuppressWarnings("unchecked")
	public void giveAuth(Data param, Data result) {
        try {
            String tenancyId = param.getTenancy_id();
            Integer storeId = param.getStore_id();
            Map<String, Object> map = ReqDataUtil.getDataMap(param);
            //人员编号
            String optNum = ParamUtil.getStringValue(map, "opt_num", false, null);
            //账单号
            String billNum = ParamUtil.getStringValue(map, "bill_num", true, PosErrorCode.NOT_NULL_BILL_NUM);
            //日期
            Date reportDate = ParamUtil.getDateValue(map, "report_date", false, null);
            //未下单需要奉送的菜品信息
            List<Map<String, Object>> items = (List<Map<String, Object>>) map.get("items");
            //未下单最终奉送金额
            double final_money = 0d;
            //套餐明细列表
            List<Map<String, Object>> meallistItems = new ArrayList<Map<String,Object>>();
            if(null != items && items.size() > 0) {
                for (Map<String, Object> oDish : items) {
                    String itemProperty = ParamUtil.getStringValue(oDish, "item_property", true, PosErrorCode.NOT_NULL_ITEM_PROPERTY);
                    Integer itemId = ParamUtil.getIntegerValue(oDish, "item_id", true, PosErrorCode.NOT_NULL_ITEM_ID);
                    Integer itemSerial = ParamUtil.getIntegerValue(oDish, "item_serial", false, null);
                    Double itemCount = ParamUtil.getDoubleValue(oDish, "item_count", false, null);
                    Double itemPrice = ParamUtil.getDoubleValue(oDish, "item_price", false, null);
                    if(itemCount.isNaN() || itemCount <= 0) {
                        itemCount = 1d;
                    }
                    if(SysDictionary.ITEM_PROPERTY_MEALLIST.equals(itemProperty)) {
                        meallistItems.add(oDish);
                    }
                    if(SysDictionary.ITEM_PROPERTY_SETMEAL.equals(itemProperty)) {
//                        List<Map<String, Object>> setmealItemList = new ArrayList<Map<String,Object>>();
//                        Double meallistItemPriceTotal = 0d;
                        Double zfmoney = 0d;
                        for (Map<String, Object> meallistItem : items)
                        {
                            String detailsItemProperty = ParamUtil.getStringValue(meallistItem, "item_property", true, PosErrorCode.NOT_NULL_ITEM_PROPERTY);
                            Integer detailsSetmealId = ParamUtil.getIntegerValue(meallistItem, "setmeal_id", false, null);
                            Integer detailsSetmealRwid = ParamUtil.getIntegerValue(meallistItem, "setmeal_rwid", false, null);
                            Double detailsItemCount = ParamUtil.getDoubleValue(meallistItem, "item_count", false, null);
                            Double detailsItemPrice = ParamUtil.getDoubleValue(meallistItem, "item_price", false, null);

                            if(detailsItemCount.isNaN() || detailsItemCount <= 0)
                            {
                                detailsItemCount = 1d;
                            }

                            if(detailsItemPrice.isNaN())
                            {
                                detailsItemPrice = 0d;
                            }

                            if (SysDictionary.ITEM_PROPERTY_MEALLIST.equals(detailsItemProperty) && itemId.equals(detailsSetmealId) && itemSerial.equals(detailsSetmealRwid))
                            {
//                                Double assistNum = DoubleHelper.div(detailsItemCount, itemCount, scale);
//                                detailsItemPrice = DoubleHelper.mul(detailsItemPrice, DoubleHelper.div(detailsItemCount, assistNum, scale), scale);
//                                meallistItemPriceTotal = DoubleHelper.add(meallistItemPriceTotal, detailsItemPrice, scale);
//
//                                meallistItem.put("item_price", detailsItemPrice);
//                                meallistItem.put("assist_num", assistNum);
//                                setmealItemList.add(meallistItem);
                                //明细做法金额
                                Double detailsMoney = this.getMethodMoney(meallistItem, detailsItemPrice, scale);
                                zfmoney = zfmoney + detailsMoney;
                            }
                        }
                        final_money = DoubleHelper.mul(itemPrice, itemCount, scale);
                        final_money = final_money + zfmoney;
                    }
                    //单品奉送金额
                    double fsmoney = 0d;
                    //单品做法加价金额
                    double zfmoney = 0d;
                    if(SysDictionary.ITEM_PROPERTY_SINGLE.equals(itemProperty)) {
                        //菜品奉送金额
                        fsmoney = DoubleHelper.mul(itemPrice, itemCount, scale);
                        //做法金额
                        zfmoney = getMethodMoney(oDish, itemPrice, scale);
                    }
                    final_money = final_money + fsmoney + zfmoney;
                }
            }
            
            boolean isGive = true;
            //获取人员奉送限制
            List<BasicDataUserDiscountAuthority> userDiscountCaseList = posDiscountDao.getUserDiscountCase(tenancyId, storeId, optNum);
            if (null != userDiscountCaseList && userDiscountCaseList.size() > 0) {
				BasicDataUserDiscountAuthority basicDataUserDiscountAuthority = userDiscountCaseList.get(0);
				//先验证整单奉送额上限，再验证每天奉送额上限
				isGive = this.giveAuthForBill(result, optNum, billNum, final_money, basicDataUserDiscountAuthority);
				if (isGive)
				{
					isGive = this.giveAuthForDate(result, optNum, reportDate, final_money, basicDataUserDiscountAuthority);
				}
				
//            	Double fs_money = 0.0; //剩余可奉送金额
//                BasicDataUserDiscountAuthority basicDataUserDiscountAuthority = userDiscountCaseList.get(0);
//                String employee_id = basicDataUserDiscountAuthority.getEmployee_id() + "";
//                
//                /*************************获取整单奉送额剩余额度***********************/
//                //人员每单奉送限制金额
//                Double single_reward_money = basicDataUserDiscountAuthority.getSingle_reward_money();
//                
//                boolean check_single_reward = false;//是否验证 每单奉送上限额
//                if(single_reward_money != null){
//                	check_single_reward = true;
//                	Double bill_item_amonut = posDiscountDao.getItemAmountByBillNum(param, employee_id, billNum);
//                	//计算可奉送金额
//                    Double need_money = DoubleHelper.sub(single_reward_money, bill_item_amonut, scale);
//                    fs_money = DoubleHelper.sub(need_money, final_money, scale);
//                }
//                
//                /*************************先验证整单奉送额上限，再验证每天奉送额上限***********************/
//                //超出  每单奉送上限额 
//                if(check_single_reward && fs_money < 0){
//                	//设置返回的结果集
//                    setResultData4Gvie(result, fs_money);
//                }else{                    
//                    //人员奉送限制金额
//                    Double pre_reward_money = basicDataUserDiscountAuthority.getPre_reward_money();
////    				if (pre_reward_money > 0) {
//                    Double item_amonut = posDiscountDao.getItemAmountByManagerNum(param, employee_id, reportDate);
//                    //计算可奉送金额
//                    Double need_money = DoubleHelper.sub(pre_reward_money, item_amonut, scale);
//                    fs_money = DoubleHelper.sub(need_money, final_money, scale);
//                    //设置返回的结果集
//                    setResultData4Gvie(result, fs_money);
////    				} else if (pre_reward_money <= 0) {
////    					this.giveAuth4Role(result, optNum);
////    				}
//                }
            } else {
//                this.giveAuth4Role(result, optNum, reportDate, final_money,billNum);
            	//查询角色奉送权限
				List<BasicDataUserDiscountAuthority> roleDisocuntCaseList = posDiscountDao.getRoleDiscountAuthority(tenancyId, storeId, optNum);

				if (null != roleDisocuntCaseList && roleDisocuntCaseList.size() > 0)
				{
					BasicDataUserDiscountAuthority basicDataRoleDiscountAuthority = roleDisocuntCaseList.get(0);
					// 先验证整单奉送额上限，再验证每天奉送额上限
					isGive = this.giveAuthForBill(result, optNum, billNum, final_money, basicDataRoleDiscountAuthority);
					if (isGive)
					{
						isGive = this.giveAuthForDate(result, optNum, reportDate, final_money, basicDataRoleDiscountAuthority);
					}
				}
            }
            
            if(isGive)
            {
            	setResultData4Gvie(result, 0d);
            }
            
        }catch (Exception e) {
            result.setCode(2);
            result.setMsg("奉送权限认证失败" + e.getMessage());
        }
    }
    
    private double getMethodMoney(Map<String, Object> oDish, Double itemPrice, int scale) throws Exception {
        double zfmoney = 0d;
        if (Tools.isNullOrEmpty(oDish.get("method")) == false) {
            //做法信息
            List<Map<String, String>> methods = (List<Map<String, String>>) oDish.get("method");
            for (Map<String, String> method : methods) {
                if (StringUtils.isNotEmpty(method.get("amount"))) {
                    double amount = Double.parseDouble(method.get("amount"));
                    String makeupWay = method.get("makeup_way");
                    double zfamount = 0d;
                    if (StringUtils.isNotEmpty(makeupWay)) {
                        if ("ADD".equalsIgnoreCase(makeupWay)) {
                            zfamount = amount;
                        }
                        if ("MULTI".equalsIgnoreCase(makeupWay)) {
                            zfamount = DoubleHelper.mul(itemPrice, amount, scale);
                        }
                        zfmoney = zfmoney + zfamount;
                    }
                }
            }
        }
        return zfmoney;
    }
    
    /**
    *
    * @param result
    * @param need_money
    * @throws Exception
    */
   private void setResultData4Gvie(Data result, Double need_money) throws Exception {
       List<JSONObject> jsonObjectList = new ArrayList<JSONObject>();
       JSONObject jsonObject = new JSONObject();
       jsonObject.put("is_give_item", 0);
       jsonObject.put("give_amount", need_money);
       jsonObjectList.add(jsonObject);
       result.setData(jsonObjectList);
       result.setCode(0);
       result.setMsg("奉送权限认证成功");
   }
   
   /**
   *
   * @param result
   * @param optNum
   * @param reportDate
   * @param finalMoney
   * @throws Exception
   */
  @SuppressWarnings("unused")
  @Deprecated
private void giveAuth4Role(Data result, String optNum,Date reportDate,Double finalMoney,String billNum) throws Exception {
	  Double sub_money = 0.0;
      //角色id
      int roleId = posDiscountDao.getRoleIdByOptNum(result.getTenancy_id(), result.getStore_id(), optNum);
      //获取角色奉送限制
      List<BasicDataUserDiscountAuthority> roleDisocuntCaseList = posDiscountDao.getRoleDiscountCase(result.getTenancy_id(), roleId);
      //角色奉送限制金额
      if (null != roleDisocuntCaseList && roleDisocuntCaseList.size() > 0) {
          BasicDataUserDiscountAuthority basicDataRoleDiscountAuthority = roleDisocuntCaseList.get(0);
          Double pre_reward_money = basicDataRoleDiscountAuthority.getPre_reward_money();
//          String employee_id = basicDataRoleDiscountAuthority.getEmployee_id() + "";
          if (pre_reward_money > 0) {
        	  
        	  /*************************获取整单奉送额剩余额度***********************/
              //人员每单奉送限制金额
              Double single_reward_money = basicDataRoleDiscountAuthority.getSingle_reward_money();
              
              boolean check_single_reward = false;//是否验证 每单奉送上限额
              if(single_reward_money != null){
              	check_single_reward = true;
              	Double bill_item_amonut = posDiscountDao.getItemAmountByBillNum(result, optNum, billNum);
              	//计算可奉送金额
                  Double need_money = DoubleHelper.sub(single_reward_money, bill_item_amonut, scale);
                  sub_money = DoubleHelper.sub(need_money, finalMoney, scale);
              }
              
              /*************************先验证整单奉送额上限，再验证每天奉送额上限***********************/
              //超出  每单奉送上限额 
              if(check_single_reward && sub_money < 0){
              	//设置返回的结果集
                  setResultData4Gvie(result, sub_money);
              }else{    
	              Double item_amonut = posDiscountDao.getItemAmountByManagerNum(result, optNum, reportDate);
	              //计算可奉送金额
	              Double need_money = DoubleHelper.sub(pre_reward_money, item_amonut, scale);
	              //计算差额金额
	              sub_money = DoubleHelper.sub(need_money, finalMoney, scale);
	//              if(sub_money < 0) {
	//                  result.setCode(2);
	//                  result.setMsg("奉送权限认证失败");
	//              }else {
	                  //设置返回的结果集
	                  setResultData4Gvie(result, sub_money);
	//              }
//	                else if (pre_reward_money < 0) {
//	                result.setCode(2);
//	                result.setMsg("奉送权限认证失败");
//	            }
              }
          }

      }else {
          result.setCode(3);
          result.setMsg("奉送权限数据集为空");
      }
  }

	/**
	 * 验证整单奉送额上限
	 * 
	 * @param result
	 * @param optNum
	 * @param finalMoney
	 * @param billNum
	 * @param basicDataRoleDiscountAuthority
	 * @return
	 * @throws Exception
	 */
	private boolean giveAuthForBill(Data result, String optNum, String billNum, Double finalMoney, BasicDataUserDiscountAuthority basicDataRoleDiscountAuthority) throws Exception
	{
		boolean isGive = true;
		Double giveAmount = 0d;

		/************************* 先验证整单奉送额上限 ***********************/
		// 人员每单奉送限制金额
		Double single_reward_money = basicDataRoleDiscountAuthority.getSingle_reward_money();
		if (null != single_reward_money && 0 < single_reward_money && isGive)
		{
			Double bill_item_amonut = posDiscountDao.getItemAmountByBillNum(result, optNum, billNum);

			// 计算可奉送金额
			Double totalGiveMoney = DoubleHelper.add(finalMoney, bill_item_amonut, scale);
			giveAmount = DoubleHelper.sub(single_reward_money, totalGiveMoney, scale);

			if (0 > giveAmount)
			{
				isGive = false;
				setResultData4Gvie(result, giveAmount);
			}
		}
		return isGive;
	}

	/**
	 * 验证每天奉送额上限
	 * 
	 * @param result
	 * @param optNum
	 * @param reportDate
	 * @param finalMoney
	 * @param basicDataRoleDiscountAuthority
	 * @return
	 * @throws Exception
	 */
	private boolean giveAuthForDate(Data result, String optNum, Date reportDate, Double finalMoney, BasicDataUserDiscountAuthority basicDataRoleDiscountAuthority) throws Exception
	{
		boolean isGive = true;
		Double giveAmount = 0d;

		/************************* 再验证每天奉送额上限 ***********************/
		Double pre_reward_money = basicDataRoleDiscountAuthority.getPre_reward_money();
		if (null != pre_reward_money && 0 < pre_reward_money && isGive)
		{
			Double item_amonut = posDiscountDao.getItemAmountByManagerNum(result, optNum, reportDate);

			// 计算可奉送金额
			Double totalGiveMoney = DoubleHelper.add(finalMoney, item_amonut, scale);
			giveAmount = DoubleHelper.sub(pre_reward_money, totalGiveMoney, scale);

			if (0 > giveAmount)
			{
				isGive = false;
				setResultData4Gvie(result, giveAmount);
			}
		}
		return isGive;
	}
}





