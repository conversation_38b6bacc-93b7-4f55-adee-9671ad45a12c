package com.tzx.member.common.util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import com.tzx.base.common.util.PosPropertyUtil;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.util.HttpUtil;
import com.tzx.framework.common.util.Md5Utils;
import com.tzx.framework.common.util.Tools;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.util.DateUtil;
import com.tzx.pos.base.util.JsonUtil;
import com.tzx.pos.base.util.ParamUtil;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

public class AcewillRequestUtil
{
	protected static final Logger logger = Logger.getLogger(AcewillRequestUtil.class);
	
	/** 获取用户账户信息 */
	public static final String		CUSTOMER_USER_QUERY_URL			= "/user/account";
	/** 交易预览 */
	public static final String		CUSTOMER_DEAL_PREVIEW_URL		= "/deal/preview";
	/** 交易提交 */
	public static final String		CUSTOMER_DEAL_COMMIT_URL		= "/deal/commit";
	/** 交易冲正 */
	public static final String		CUSTOMER_DEAL_ROLLBACK_URL		= "/deal/rollback";
	/** 交易撤销 */
	public static final String		CUSTOMER_DEAL_CANCEL_URL		= "/deal/cancel";
	/** 发送验证码 */
	public static final String		CUSTOMER_USER_SEND_CODE			= "/user/sendcode";
	/** 消费记录详情 */
	public static final String		CUSTOMER_DEAL_VIEW_URL			= "/consume/view";
	/** 查看等级设置 */
	public static final String		CUSTOMER_GRADE_RULE				= "/grade/rule";

	/** 资产锁定 */
	public static final String		LOCK_CUSTOMER_DEAL_URL			= "/deal/lock";
	/** 资产取消锁定 */
	public static final String		UNLOCK_CUSTOMER_DEAL_URL		= "/deal/unlock";
	/** 资产占用查询 */
	public static final String		LOCKQUERY_CUSTOMER_DEAL_URL		= "/deal/lockquery";

	/** 修改用户信息 */
	public static final String		EDIT_CUSTOMER_USER_URL			= "/user/edit";
	/** 修改用户绑定电话 */
	public static final String		EDIT_CUSTOMER_PHONE_URL			= "/user/bindphone";
	/** 查看门店储值规则设置 */
	public static final String		FIND_ACEWILL_CHARGE_RULE_URL	= "/charge/rule";
	/** 获取收银员列表 */
	public static final String		FIND_ACEWILL_CASHIER_LIST_URL	= "/cashier/list";
	/** 获取门店员工列表 */
	public static final String		FIND_ACEWILL_EMPLOYEE_LIST_URL	= "/employee/list";
	/** 储值支付方式 */
	public static final String		FIND_ACEWILL_DEAL_PAY_TYPE_URL	= "/deal/getpaytype";
	/** 会员储值记录 */
	public static final String		FIND_ACEWILL_CHARGE_USER		= "/charge/user";
	/** 会员消费明细 */
	public static final String		FIND_ACEWILL_CONSUME_USER		= "/consume/user";
	/** 查看积分设置 */
	public static final String		FIND_ACEWILL_CREDIT_RULE_URL	= "/credit/rule";
	/** 积分换礼 */
	public static final String		CREDIT_EXCHANGE_GIFTS_URL		= "/credit/exchange";
	/** 储值预览 */
	public static final String		PREVIEW_ACEWILL_CHARGE_URL		= "/charge/preview";
	/** 储值提交 */
	public static final String		COMMIT_ACEWILL_CHARGE_URL		= "/charge/commit";
	/** 撤销储值 */
	public static final String		CANCEL_ACEWILL_CHARGE_URL		= "/charge/cancel";
	/** 根据业务号查储值详情 */
	public static final String		QUERY_ACEWILL_CHARGE_URL		= "/charge/detailbybiz";
	/** 验卡 */
	public static final String		CHECK_ACEWILL_CONSUME_CARD		= "/user/checkcard";
	/** 制卡 */
	public static final String		MAKE_ACEWILL_CONSUME_CARD		= "/make/actualcard";
	/** 开卡 */
	public static final String		OPEN_ACEWILL_CONSUME_CARD		= "/user/opencard";
	/** 门店列表 */
	public static final String		ACEWILL_SHOP_LIST_URL			= "/shop/list";

	/** 短信验证码为空 */
	public static final int			SMS_NULL_ERROR_CODE				= 3029;
	/** 短信验证码错误 */
	public static final int			SMS_WRONG_ERROR_CODE			= 3030;
	/** 短信验证码过期 */
	public static final int			SMS_OVERDUE_ERROR_CODE			= 3031;
	/** 交易密码仅支持6位数字 */
	public static final int			PASSWORD_LENGTH_ERROR_CODE		= 3055;
	/** 输入的交易密码不正确 */
	public static final int			PASSWORD_WRONG_ERROR_CODE		= 3056;
	/** 不允许使用默认密码交易 */
	public static final int			PASSWORD_DEFAULT_ERROR_CODE		= 3135;
	/** 交易状态异常 */
	public static final int			 TRADE_ABNORMAL_ERROR_CODE		= 3032;
	
	/** 资产占用已解锁 */
	public static final int			ALREADY_UNLOCK_ERROR_CODE		= 90029;
	
	/**
	 * @param requestData
	 * @param url
	 * @param appId
	 * @param appKey
	 * @return
	 * @throws Exception
	 */
	public static Data commonPost(Data requestData, String url, String appId, String appKey) throws Exception {
		String requestUrl = getPequestUrl(url);
		
		Long ts = requestData.getT();
		// 如果为空 ts取当前时间戳
		if (requestData.getT() == null || requestData.getT() == 0)
		{
			ts = DateUtil.currentTimestamp().getTime();
			requestData.setT(ts);
		}
		
		Data resData = Data.get(requestData);
		
		if (Tools.isNullOrEmpty(appId) || Tools.isNullOrEmpty(appKey))
		{
			resData.setCode(Constant.CODE_PARAM_FAILURE);
			resData.setMsg("未配置微生活APPID或APPKEY");
			resData.setSuccess(false);
			return resData;
		}
		
		String paramStr = null;
		try {
			paramStr = requestData.getData().get(0).toString();
			logger.info("请求传入参数 "+paramStr);
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("参数转换JSON对象失败");
		}
		
		if (paramStr == null) {
			logger.info("请求参数为空");
			// 请求参数为空
			resData.setCode(Constant.CODE_PARAM_FAILURE);
			resData.setMsg("请求参数为空");
			resData.setSuccess(false);
			return resData;
		}

		// 获取签名
		String sing = getSign(requestData,appId,appKey);
		if (sing == null || "".equals(sing)) {
			logger.info("获取签名为空");
			// 请求参数为空
			resData.setCode(Constant.CODE_INNER_EXCEPTION);
			resData.setMsg("获取签名为空");
			resData.setSuccess(false);
			return resData;
		}

		Map<String, String> params = new HashMap<String, String>();// 组织参数
		params.put("req", paramStr);
        params.put("appid", appId);
		params.put("v", PosPropertyUtil.getMsg("acewill.v"));
		params.put("ts", ts.toString());
		params.put("sig", sing);
		params.put("fmt", "JSON");

		try {
			logger.info("调用接口请求参数" + params.toString());
			logger.info("调用接口请求url" + requestUrl);
			String result = HttpUtil.sendPostRequest(requestUrl, params);
			logger.info("调用接口返回数据" + result);
			
			if (StringUtils.isEmpty(result))
			{
				resData.setCode(Constant.CODE_CONN_EXCEPTION);
				resData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
				resData.setSuccess(false);
				logger.info("连接超时，请检查网络,请求体为：" + params);
			}
			else
			{
				JSONObject resultJson = JSONObject.fromObject(result);
				if (resultJson.containsKey("res") && !"null".equals(resultJson.getString("res")))
				{
					try
					{
						// 返回参数转换成Data类型
						resData = acewillJsonToData(resultJson);

						resData.setType(requestData.getType());
						resData.setOper(requestData.getOper());
						resData.setTenancy_id(requestData.getTenancy_id());
						resData.setStore_id(requestData.getStore_id());
					}
					catch (Exception e)
					{
						e.printStackTrace();
						logger.info("返回参数转换Data类型失败");
					}
				}
				else if (resultJson.containsKey("errcode") && !"null".equals(resultJson.getString("errcode")))
				{
					logger.info("调用接口返回参数res数据为空" + resultJson);
					resData.setCode(ParamUtil.getIntegerValueByObject(resultJson, "errcode"));
					resData.setMsg(ParamUtil.getStringValueForNullByObject(resultJson, "errmsg"));
					resData.setSuccess(false);
				}
				else if (resultJson.containsKey("code") && !"null".equals(resultJson.getString("code")))
				{
					logger.info("调用接口返回参数res数据为空" + resultJson);
					resData.setCode(ParamUtil.getIntegerValueByObject(resultJson, "code"));
					resData.setMsg(ParamUtil.getStringValueForNullByObject(resultJson, "msg"));
					resData.setSuccess(false);
				}
				else
				{
					logger.info("调用接口返回参数res数据为空" + resultJson);
					resData.setCode(Constant.CODE_CONN_EXCEPTION);
					resData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
					resData.setSuccess(false);
				}
			}

		} catch (Exception se) {
			logger.info("连接超时，请检查网络,请求体为：" + params);
			resData.setCode(Constant.CODE_INNER_EXCEPTION);
			resData.setMsg("接口返回参数解析失败");
			resData.setSuccess(false);
		}

		return resData;
	}
	
	/**
	 * @param url
	 * @return
	 */
	private static String getPequestUrl(String url) {
		return PosPropertyUtil.getMsg("acewill.url") + url;
	}

	/**
	 * 获取签名
	 * @param requestData
	 * @return
	 * @throws Exception
	 */
	private static String getSign(Data requestData,String appId,String appKey) throws Exception {
		JSONObject paramStr = null;
		try {
			paramStr = JSONObject.fromObject(requestData.getData().get(0));
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("获取签名,sig生成:参数转换JSON对象失败");
			return null;
		}
		if (paramStr == null) {
			logger.info("获取签名请求参数为空");
			return null;
		}

		StringBuffer buff = new StringBuffer();
		try {
			paramStr = JsonUtil.sortJsonObject(paramStr);// 排序
			buff.append(JsonUtil.formatJsonObjectToUrl(paramStr, null, true, true));// 转字符串
			buff.append("appid=").append(appId).append("&appkey=").append(appKey);
			buff.append("&v=").append(PosPropertyUtil.getMsg("acewill.v")).append("&ts=").append(String.valueOf(requestData.getT()));
			logger.info("sig生成规则参数" + buff.toString());
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
		// MD5加密
		return Md5Utils.md5(buff.toString());
	}

	/**
	 * 将微生活返回json封装Data
	 * @param result
	 * @return
	 */
	public static Data acewillJsonToData(JSONObject result) {
		Data resData = new Data();
		Integer errcode = ParamUtil.getIntegerValueByObject(result, "errcode");
		resData.setCode(errcode);
		resData.setMsg(ParamUtil.getStringValueForNullByObject(result, "errmsg"));
		if (Constant.CODE_SUCCESS == errcode) {
			resData.setSuccess(true);
		} else {
			resData.setSuccess(false);
		}
		if (!result.getString("res").isEmpty() && result.getString("res") != null) {
			try {
				Object listArray = result.get("res");
				if (listArray instanceof JSONArray) { // JSONArray数组
					JSONArray jsonArray = (JSONArray) listArray;
					resData.setData(jsonArray);
				} else if (listArray instanceof JSONObject) {// JSONObject
					JSONObject jsonObject = (JSONObject) listArray;
					List<JSONObject> list = new ArrayList<JSONObject>();
					list.add(jsonObject);
					resData.setData(list);
				}

			} catch (Exception e) {
				e.printStackTrace();
				logger.info("返回参数res转换类型出错");
				resData.setCode(Constant.CODE_INNER_EXCEPTION);
				resData.setMsg("返回参数res转换类型出错");
				resData.setSuccess(false);
			}
		}
		return resData;
	}
}
