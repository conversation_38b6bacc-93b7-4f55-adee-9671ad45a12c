package com.tzx.framework.common.newprint;

import java.util.concurrent.ConcurrentHashMap;
import net.sf.json.JSONObject;

/**
 * 常量类
 *
 * <AUTHOR>
 */
public class Constant {

    // 默认编码
    public static String DEFAULT_ENCODING = "GBK";
    // 默认端口
    public static int DEFAULT_PORT = 9100;
    // 替换占位符的正则表达式
    public static String REPLACE_PATTERN = "\\{\\$(.+?)\\}";
    
    public static ConcurrentHashMap<Integer,Integer> PRINTSTOPJOBMAP = new ConcurrentHashMap<Integer,Integer>();
   
    public static ConcurrentHashMap<Integer,JSONObject> STOPOBJ = new ConcurrentHashMap<Integer,JSONObject>();

    public static ConcurrentHashMap<Integer,Integer> JOBSTOPTIMES = new ConcurrentHashMap<Integer,Integer>();
   
    public static ConcurrentHashMap<Integer,Integer> PRINTJOBTIMESMAP = new ConcurrentHashMap<Integer,Integer>();

    public static void clear(Integer print_id)
    {
    	int id = 0;
    	if(PRINTSTOPJOBMAP.contains(print_id))
    	{
    		id = PRINTSTOPJOBMAP.get(print_id)+0;
    		PRINTSTOPJOBMAP.remove(print_id);
    	}
    	if(PRINTJOBTIMESMAP.contains(print_id))
    	{
    		PRINTJOBTIMESMAP.remove(print_id);
    	}
    	if(JOBSTOPTIMES.contains(print_id))
    	{
    		JOBSTOPTIMES.remove(print_id);
    	}
    	if(STOPOBJ.contains(id))
    	{
    		STOPOBJ.remove(id);
    	}
    	
    }
    
    public static void setPrint(Integer print_id,Integer job_id)
    {
    	PRINTSTOPJOBMAP.put(print_id, job_id);
    	int stoptimes = 1;
    	if(JOBSTOPTIMES.contains(job_id))
    	{
    		stoptimes = JOBSTOPTIMES.get(job_id)+1;
    		JOBSTOPTIMES.put(job_id, stoptimes);
    	}
    	else
    	{
    		JOBSTOPTIMES.put(print_id, job_id);
    	}
    	JOBSTOPTIMES.put(job_id, 1);
    }

    public static int getTimes(Integer print_id)
    {
    	if(PRINTSTOPJOBMAP.contains(print_id))
    	{
    		if(PRINTJOBTIMESMAP.contains(PRINTSTOPJOBMAP.get(print_id)))
    		{
    			return PRINTJOBTIMESMAP.get(PRINTSTOPJOBMAP.get(print_id));
    		}
    		else
    		{
    			return 0;
    		}
    	}
    	else
    	{
    		return 0;
    	}
    }
    
    public static JSONObject getStopJob(Integer print_id)
    {
    	if(PRINTSTOPJOBMAP.contains(print_id) && STOPOBJ.contains(PRINTSTOPJOBMAP.get(print_id)))
    	{
    		return STOPOBJ.get(PRINTSTOPJOBMAP.get(print_id));
    	}
    	else
    	{
    		return null;
    	}
    }
    
}
